[{"__type__": "cc.Prefab", "_name": "UIFarmBeeManage", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIFarmBeeManage", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 237}], "_active": true, "_components": [{"__id__": 532}], "_prefab": {"__id__": 534}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "slot_item_viewholder", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}], "_active": false, "_components": [{"__id__": 232}, {"__id__": 234}], "_prefab": {"__id__": 236}, "_lpos": {"__type__": "cc.Vec3", "x": -1945.19, "y": 206.167, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 616, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3PLvZ6khCva1qsG0AliTi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5fca5cce-83b1-4ac1-a0a6-9abd0b00b04f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4914r1qMNIhIZQPISpgJA2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9eLZbSRutFnIcduFK2mDV7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 10}, {"__id__": 79}, {"__id__": 120}, {"__id__": 189}], "_active": true, "_components": [{"__id__": 229}], "_prefab": {"__id__": 231}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_my", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 11}, {"__id__": 17}, {"__id__": 23}, {"__id__": 35}, {"__id__": 41}, {"__id__": 47}, {"__id__": 64}, {"__id__": 70}], "_active": true, "_components": [{"__id__": 76}], "_prefab": {"__id__": 78}, "_lpos": {"__type__": "cc.Vec3", "x": -229.624, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_houtou_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}], "_prefab": {"__id__": 16}, "_lpos": {"__type__": "cc.Vec3", "x": -0.928, "y": 10.473, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1.5, "y": 1.5, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 13}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 57}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59hJ0zs1ZCSoNE5CSv3KpM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "eab03054-64ca-49a9-bd77-4ea5f6b510c2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6CBAS3UpCJYnqIEJjjDKe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eaOgFMq9NMyr+6X3nzucnL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_houtou_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 18}, {"__id__": 20}], "_prefab": {"__id__": 22}, "_lpos": {"__type__": "cc.Vec3", "x": -3.936, "y": 10.018, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1.5, "y": 1.5, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 19}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 57}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aw42BGR5IXb7xfeieBzkC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 21}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6d4b0efe-2ba0-4c94-ba48-f8c7952115d6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daWg1bxOxFsLPBKEkc2e3X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "das7ihEUNH944F8d1jZUIu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 24}], "_active": true, "_components": [{"__id__": 30}, {"__id__": 32}], "_prefab": {"__id__": 34}, "_lpos": {"__type__": "cc.Vec3", "x": 26.824, "y": -8.064, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 23}, "_children": [], "_active": true, "_components": [{"__id__": 25}, {"__id__": 27}], "_prefab": {"__id__": 29}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 26}, "_contentSize": {"__type__": "cc.Size", "width": 26.399993896484375, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bo6FfLu9MGrb6lY3Zb5vb"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 28}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 231, "g": 240, "b": 250, "a": 255}, "_string": "10", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22BnYHsTpDb5STjBhHdGv1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32KHjAdqFFupjsmc9PoDMX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcvQTgVepN7pTPaT++BJX3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 33}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}, "_spriteFrame": {"__uuid__": "c1545f37-b08c-4f5e-aaa8-130871c185d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2dzPi0TZIJr8g2PBFQ9na"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "020fJeshpARZsdNHg2Z7hM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": -39.982, "y": 41.596, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 37}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63PPaNfEJGn4Eha9vn3pAk"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 39}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 113, "g": 207, "b": 255, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 6, "g": 30, "b": 55, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9ZYuC+3lDHLt8e33whqj2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6elUk1DVVPHJ99eEMG0WVU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_collecting", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 42}, {"__id__": 44}], "_prefab": {"__id__": 46}, "_lpos": {"__type__": "cc.Vec3", "x": -8, "y": -36.983, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 43}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "210B6+KL5IAp1+LM15X4BJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 45}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 225, "g": 0, "b": 0, "a": 255}, "_string": "正在采集", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 14, "g": 55, "b": 6, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78xTMSeFRDMKyC6P/+/vw1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02YWqNp3NGk77MoLm5CUrq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 10}, "_prefab": {"__id__": 48}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 47}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 49}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8fZNSGmwRHD6XLbqYewjJG", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 50}, {"__id__": 52}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 57}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_name"], "value": "lbl_cd_my"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -8, "y": -64.349, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 60.49998474121094, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["73icu5TNJLZaYTvRIxQDjK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_string"], "value": "05:18"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_enableOutline"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "cc.Node", "_name": "bg_working_my", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 65}, {"__id__": 67}], "_prefab": {"__id__": 69}, "_lpos": {"__type__": "cc.Vec3", "x": 77.593, "y": -12.423, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 66}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f800LurulOq7+iejy3YAQ/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 68}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1c91356f-5280-46d0-bf56-aaf9b8ac4950@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eorDdk/BKeYfocOhW/gGU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b04cQdkyZMHZoEB8GlV/Eu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spine_get_my", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": false, "_components": [{"__id__": 71}, {"__id__": 73}], "_prefab": {"__id__": 75}, "_lpos": {"__type__": "cc.Vec3", "x": 77.593, "y": -12.423, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 70}, "_enabled": true, "__prefab": {"__id__": 72}, "_contentSize": {"__type__": "cc.Size", "width": 50.20000076293945, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6yc2FXgJFIZQP76xz/FjS"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 70}, "_enabled": true, "__prefab": {"__id__": 74}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "b8f6af7d-69d9-44ef-99cb-33d1d9abef01", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "jian_tou_lv", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8crmqei4NIzIUSdMwOygD/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44AU1NjoVH5qCRkR36Nbt3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 77}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75rokhXFRKT7tNnl3LbOjP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "deD3xSqzpLeruRH8+y/vr2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_hulu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 80}, {"__id__": 105}, {"__id__": 111}], "_active": true, "_components": [{"__id__": 117}], "_prefab": {"__id__": 119}, "_lpos": {"__type__": "cc.Vec3", "x": -75, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 79}, "_prefab": {"__id__": 81}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 80}, "asset": {"__uuid__": "ebe26d9f-9d41-4412-a791-b043cb06c858", "__expectedType__": "cc.Prefab"}, "fileId": "96elUVidxPRqijnRGfo8vE", "instance": {"__id__": 82}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d7GM6HCLhBZYdnB/A3CDVg", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 83}, {"__id__": 85}, {"__id__": 87}, {"__id__": 89}, {"__id__": 91}, {"__id__": 93}, {"__id__": 95}, {"__id__": 97}, {"__id__": 99}, {"__id__": 101}, {"__id__": 103}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_name"], "value": "hulu"}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 88}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 90}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 92}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["ddAQ5xzpdMtYI/eBlxjBY6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 42.399993896484375, "height": 34.24}}, {"__type__": "cc.TargetInfo", "localID": ["85QU562IhLooxJJeUfFnfs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 96}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["f1IE1B+rVJcKR7ZJBi0lQ4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 100}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["aa24Y8uNdBfJ9ZEeKl148A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["229yXnPipKP4x6wS6sNlCh"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 104}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["25z1KwwItB/6CBXUmwcqnq"]}, {"__type__": "cc.Node", "_name": "lbl_other_farm", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 79}, "_children": [], "_active": true, "_components": [{"__id__": 106}, {"__id__": 108}], "_prefab": {"__id__": 110}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 65.816, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 105}, "_enabled": true, "__prefab": {"__id__": 107}, "_contentSize": {"__type__": "cc.Size", "width": 163.19998168945312, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2jydcvUNIMo0oz6aKAiGc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 105}, "_enabled": true, "__prefab": {"__id__": 109}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "【111】的福地", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 14, "g": 55, "b": 6, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbH7zPRt1O4I7j4+ef4U+I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9DJWz7IxHNaQLZyJhBw5Z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_my_farm", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 79}, "_children": [], "_active": false, "_components": [{"__id__": 112}, {"__id__": 114}], "_prefab": {"__id__": 116}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 65.816, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 113}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1WlaypsxCyLEYdiCYMo8e"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 115}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}, "_string": "我的福地", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 14, "g": 55, "b": 6, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "787YB/QpNF3a98d2eZFceW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edr0ZFY2REVKbgvt5QJ5PE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": {"__id__": 118}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daiH6BvCBK9pTrN7yjkLaK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c0prnEkcdHQ4mAWB+SdG7v", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_other", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 121}, {"__id__": 127}, {"__id__": 133}, {"__id__": 145}, {"__id__": 151}, {"__id__": 157}, {"__id__": 174}, {"__id__": 180}], "_active": true, "_components": [{"__id__": 186}], "_prefab": {"__id__": 188}, "_lpos": {"__type__": "cc.Vec3", "x": 76.267, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_houtou_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 120}, "_children": [], "_active": true, "_components": [{"__id__": 122}, {"__id__": 124}], "_prefab": {"__id__": 126}, "_lpos": {"__type__": "cc.Vec3", "x": -7.021, "y": 10.473, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 123}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 57}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54rQUZPaBJU7HlSTykY58q"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 125}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "eab03054-64ca-49a9-bd77-4ea5f6b510c2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6wChQYHlIgasWvj9d+UiC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "943H/8X3NNO7ISWi9zydGW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_houtou_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 120}, "_children": [], "_active": true, "_components": [{"__id__": 128}, {"__id__": 130}], "_prefab": {"__id__": 132}, "_lpos": {"__type__": "cc.Vec3", "x": -3.936, "y": 10.018, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 129}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 57}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3Ao7wdY9Ji4J0/d0fpuU1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 131}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6d4b0efe-2ba0-4c94-ba48-f8c7952115d6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "367BV5hJBAMYENW5l8zBcd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9c2rfJ4QlClrdncZx1SXZH", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 120}, "_children": [{"__id__": 134}], "_active": true, "_components": [{"__id__": 140}, {"__id__": 142}], "_prefab": {"__id__": 144}, "_lpos": {"__type__": "cc.Vec3", "x": 26.824, "y": -8.064, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 137}], "_prefab": {"__id__": 139}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 136}, "_contentSize": {"__type__": "cc.Size", "width": 26.399993896484375, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38qdINwU5FB4a2G6A8ifxx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 138}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 231, "g": 240, "b": 250, "a": 255}, "_string": "10", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1Yw47VN5IMYvqHU49Iziv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dhjhaf8xE6q4bGT9rGy8H", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 141}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85gahDarFDcLUPP1B+/Xmv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 143}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_spriteFrame": {"__uuid__": "c1545f37-b08c-4f5e-aaa8-130871c185d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5SVNY0qRKAo95lfFVjMXz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "21jbcZ5WlIAK5bYdWEqFxt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 120}, "_children": [], "_active": false, "_components": [{"__id__": 146}, {"__id__": 148}], "_prefab": {"__id__": 150}, "_lpos": {"__type__": "cc.Vec3", "x": -39.982, "y": 41.596, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 147}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43YTUTuclBG6smla9RnaTY"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 149}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 113, "g": 207, "b": 255, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 6, "g": 30, "b": 55, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bakBRE8y1BMJoSuTimASOd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80KTqoVfVEN4uxZPO4/Ng8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 120}, "_children": [], "_active": true, "_components": [{"__id__": 152}, {"__id__": 154}], "_prefab": {"__id__": 156}, "_lpos": {"__type__": "cc.Vec3", "x": -8, "y": -36.983, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": {"__id__": 153}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dUai+K5xPWoqP9OzBCP35"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": {"__id__": 155}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "正在采集", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 14, "g": 55, "b": 6, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fBUB471hG85hE/W7Z6YJM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69xZ9rLMpJ6LgnelHKDalo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 120}, "_prefab": {"__id__": 158}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 157}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 159}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "53cq46vE1MJZvmBYuBCx6A", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 160}, {"__id__": 162}, {"__id__": 163}, {"__id__": 164}, {"__id__": 165}, {"__id__": 167}, {"__id__": 169}, {"__id__": 170}, {"__id__": 171}, {"__id__": 172}, {"__id__": 173}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_name"], "value": "lbl_cd_other"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -8, "y": -64.349, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 166}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 60.49998474121094, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 225, "g": 0, "b": 0, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["73icu5TNJLZaYTvRIxQDjK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_string"], "value": "05:18"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_enableOutline"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "cc.Node", "_name": "bg_working_other", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 120}, "_children": [], "_active": true, "_components": [{"__id__": 175}, {"__id__": 177}], "_prefab": {"__id__": 179}, "_lpos": {"__type__": "cc.Vec3", "x": -79.018, "y": -12.423, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": {"__id__": 176}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bQDe61b9OV6bw9SNMNkyP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": {"__id__": 178}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ad0b5708-780d-4819-981f-5cb83de1a8fd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cn1j6bGlDmqEEU8rjciCM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44WaXN3XRJmqsHCfI6kkAD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spine_get_other", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 120}, "_children": [], "_active": false, "_components": [{"__id__": 181}, {"__id__": 183}], "_prefab": {"__id__": 185}, "_lpos": {"__type__": "cc.Vec3", "x": -79.018, "y": -12.423, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 182}, "_contentSize": {"__type__": "cc.Size", "width": 50.20000076293945, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57o10sYA9Kg6roWqUdZYLU"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 184}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "b8f6af7d-69d9-44ef-99cb-33d1d9abef01", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "jian_tou_hong", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dct+h85/lNaq+M2/Xkzvsy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1nAg5frFCdKHSTItoRWam", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": {"__id__": 187}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44gFSxhkVMe7ez/o1sinLS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74b0UIHxpBM70s4YR7dDIZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_btn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 190}, {"__id__": 208}], "_active": true, "_components": [{"__id__": 226}], "_prefab": {"__id__": 228}, "_lpos": {"__type__": "cc.Vec3", "x": 219.613, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_callback", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 189}, "_children": [{"__id__": 191}], "_active": true, "_components": [{"__id__": 199}, {"__id__": 201}, {"__id__": 203}, {"__id__": 205}], "_prefab": {"__id__": 207}, "_lpos": {"__type__": "cc.Vec3", "x": -2.052, "y": 25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_callback", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 190}, "_children": [], "_active": true, "_components": [{"__id__": 192}, {"__id__": 194}, {"__id__": 196}], "_prefab": {"__id__": 198}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": {"__id__": 193}, "_contentSize": {"__type__": "cc.Size", "width": 74.5, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50udr4VllM8rK3fQGCJW89"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": {"__id__": 195}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "召 回", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bw29n8DtCX6dF3ky+Zjwn"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": {"__id__": 197}, "_args": [], "_messageKey": 483, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64OYvO8uNE0qye9QEwoNs3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f6jdkpRD9GTb8h50CnKPf9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": {"__id__": 200}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1cNtCJB5L8qLyJCZk1Pb7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": {"__id__": 202}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "562dafc2-c01f-474c-b958-bac7570bbd8d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43OLo1GSBFRLCqzQzK3VVO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": {"__id__": 204}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 51.5, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3lVjDvLtOVqvtspC8WhnL"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": {"__id__": 206}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.1, "_target": {"__id__": 190}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4M79Tc29DJaRhMqp+6u7I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0fhHSROJxIW6eP6/BGNSSK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_go", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 189}, "_children": [{"__id__": 209}], "_active": true, "_components": [{"__id__": 217}, {"__id__": 219}, {"__id__": 221}, {"__id__": 223}], "_prefab": {"__id__": 225}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -38.672, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_go", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 208}, "_children": [], "_active": true, "_components": [{"__id__": 210}, {"__id__": 212}, {"__id__": 214}], "_prefab": {"__id__": 216}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 211}, "_contentSize": {"__type__": "cc.Size", "width": 74.5, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44eZL4C4pLB4FCdoqTP7hQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 213}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "前 往", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dQ+HdIxlEL5CEO4pVjIA0"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 215}, "_args": [], "_messageKey": 484, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dCqqHfZBNjri+j6eVcabb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "95uZ9tcLNNQYRKVY74elSp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 218}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaQBoSNV9DKqWV+RDd5GeL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 220}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cf56348d-b49e-4dfb-ab0c-7ec88fe4870a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46oEpGV3dEYbKnpJ4ydOjq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 222}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -12.171999999999997, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22gBJZ6oxIA72XoXSlHTe3"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 224}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.1, "_target": {"__id__": 208}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9tFJxbJVN7ZV2jtxhYii8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9cw1yuBRRJZLqB5L5CyA9b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 227}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "644ZSZtMZB2p70wRkG7679"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0FbtgOgRBrItIGy6bTw9a", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 230}, "_contentSize": {"__type__": "cc.Size", "width": 616, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7619t1eNxE96Rot7SuTHPf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28ojR63mZLbZSsSfcI77PL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 233}, "_contentSize": {"__type__": "cc.Size", "width": 616, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46T3vXbj1LOouu1viowhkV"}, {"__type__": "7dbfcsO5CFPC6PZF+7iDgK3", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 235}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ba8d9WEJRKFZDARuPUSeEA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90BH6g3cNJLZpeLzr1TuwU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 238}, {"__id__": 244}], "_active": true, "_components": [{"__id__": 529}], "_prefab": {"__id__": 531}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_close_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 237}, "_children": [], "_active": true, "_components": [{"__id__": 239}, {"__id__": 241}], "_prefab": {"__id__": 243}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 238}, "_enabled": true, "__prefab": {"__id__": 240}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85qJa9nF5KNZJEy9OEDJMR"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 238}, "_enabled": true, "__prefab": {"__id__": 242}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 238}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fd+auYt0pOXZSYCqtvT76B"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "42LTDWQDRLB4Zt0MK/XWA1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 237}, "_prefab": {"__id__": 245}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 244}, "asset": {"__uuid__": "468968c7-ca41-40f2-9e5f-74ee660b4fd8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 246}, "targetOverrides": []}, {"__type__": "cc.PrefabInstance", "fileId": "e1Efkk8bdI5LUX6hiyPPIn", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 247}], "mountedComponents": [{"__id__": 492}], "propertyOverrides": [{"__id__": 496}, {"__id__": 498}, {"__id__": 499}, {"__id__": 500}, {"__id__": 501}, {"__id__": 503}, {"__id__": 505}, {"__id__": 507}, {"__id__": 509}, {"__id__": 511}, {"__id__": 513}, {"__id__": 515}, {"__id__": 517}, {"__id__": 519}], "removedComponents": [{"__id__": 521}, {"__id__": 522}, {"__id__": 523}, {"__id__": 524}, {"__id__": 525}, {"__id__": 526}, {"__id__": 527}, {"__id__": 528}]}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 248}, "nodes": [{"__id__": 249}]}, {"__type__": "cc.TargetInfo", "localID": ["ccATktvIZPJY5yZnIE9AmP"]}, {"__type__": "cc.Node", "_name": "layout_main", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 244}}, "_parent": {"__id__": 250}, "_children": [{"__id__": 256}, {"__id__": 262}, {"__id__": 313}, {"__id__": 409}, {"__id__": 421}, {"__id__": 427}], "_active": true, "_components": [{"__id__": 489}], "_prefab": {"__id__": 491}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 244}, "_children": [{"__id__": 249}], "_active": true, "_components": [{"__id__": 251}, {"__id__": 253}], "_prefab": {"__id__": 255}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -16.074499999999944, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": {"__id__": 252}, "_contentSize": {"__type__": "cc.Size", "width": 662.0970000000001, "height": 1079.795}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8DuFrkIFIJrvRcNSr2YgG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": {"__id__": 254}, "_alignFlags": 45, "_target": null, "_left": 18.95149999999997, "_right": 18.951499999999964, "_top": 51.177000000000056, "_bottom": 19.027999999999977, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2m76nTfpD5q+4RNdfiIOx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccATktvIZPJY5yZnIE9AmP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 249}, "_children": [], "_active": true, "_components": [{"__id__": 257}, {"__id__": 259}], "_prefab": {"__id__": 261}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 498.51750000000004, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 258}, "_contentSize": {"__type__": "cc.Size", "width": 268.5999450683594, "height": 38.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00jWf7ldNP5pXEZXk6Zhav"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 260}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "今日可采集次数:14/20", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 75, "g": 124, "b": 193, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6mYyXcr5BEK3BXu8ixTxg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ca4prMh4dJQabGVIcw1F8Z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_worker_get", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 249}, "_children": [{"__id__": 263}, {"__id__": 275}, {"__id__": 281}, {"__id__": 298}], "_active": true, "_components": [{"__id__": 310}], "_prefab": {"__id__": 312}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 429.13750000000005, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zm_bg_jin<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [{"__id__": 264}], "_active": true, "_components": [{"__id__": 270}, {"__id__": 272}], "_prefab": {"__id__": 274}, "_lpos": {"__type__": "cc.Vec3", "x": -255.532, "y": 13.763, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_houtou_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 263}, "_children": [], "_active": true, "_components": [{"__id__": 265}, {"__id__": 267}], "_prefab": {"__id__": 269}, "_lpos": {"__type__": "cc.Vec3", "x": 1.917, "y": 1.012, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.85, "y": 0.85, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 266}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 51}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8upKOFR1CWLEGbTKG+MRI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 268}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6d4b0efe-2ba0-4c94-ba48-f8c7952115d6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70pP5RQt1G95Yb2IHGKfEs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1b9hut/n1JV40o7zxzstl6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 271}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f/59GuHtGxYVgIbczbyp8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 273}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "33b4bf52-e67c-479a-8345-5b54ccc8d9af@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5629XPvfhJA5J3F3YMiOS2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61t0KLYfdHgYQD1v16AF+V", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_work_unlock_num", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [], "_active": true, "_components": [{"__id__": 276}, {"__id__": 278}], "_prefab": {"__id__": 280}, "_lpos": {"__type__": "cc.Vec3", "x": -250.664, "y": -28.082, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 277}, "_contentSize": {"__type__": "cc.Size", "width": 121.5999755859375, "height": 34.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6pFFN/IxAkaD3PhnHCzPV"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 279}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "已解锁 2/7", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 178, "g": 119, "b": 40, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07S+hzUdtIfIpBP38fzImC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2clJza+iVOgaAusX8WPlYN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 262}, "_prefab": {"__id__": 282}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 281}, "asset": {"__uuid__": "1ad8fd55-a1c3-4ab1-a096-94912691677c", "__expectedType__": "cc.Prefab"}, "fileId": "2dVbFkq0RIz7vxZ35Qu6sX", "instance": {"__id__": 283}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "00FNnd1VVC8IFcjz5a3gtf", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 284}, {"__id__": 286}, {"__id__": 287}, {"__id__": 288}, {"__id__": 289}, {"__id__": 291}, {"__id__": 293}, {"__id__": 294}, {"__id__": 295}, {"__id__": 296}, {"__id__": 297}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 285}, "propertyPath": ["_name"], "value": "lbl_next_unlock"}, {"__type__": "cc.TargetInfo", "localID": ["2dVbFkq0RIz7vxZ35Qu6sX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 285}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -189.266, "y": 4.222, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 285}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 285}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 290}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["06/TaunOlDDpr11/UEb7O3"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["e1Dgd01qFBkbuuImaWwWdX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_fontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 290}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 338.39996337890625, "height": 30.240000000000002}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_actualFontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_lineHeight"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_string"], "value": "第3只猴子累计采集 1/20次解锁"}, {"__type__": "cc.Node", "_name": "btn_help", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [{"__id__": 299}], "_active": true, "_components": [{"__id__": 305}, {"__id__": 307}], "_prefab": {"__id__": 309}, "_lpos": {"__type__": "cc.Vec3", "x": 279.013, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_xiangxixinxi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 298}, "_children": [], "_active": true, "_components": [{"__id__": 300}, {"__id__": 302}], "_prefab": {"__id__": 304}, "_lpos": {"__type__": "cc.Vec3", "x": 0.19900000000006912, "y": 4.22199999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": {"__id__": 301}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9527H81NtLXZVzUepadAfc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": {"__id__": 303}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "15970a7b-7b45-475c-9675-180c125cec2f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "391ZQogeVDSKZJ02DZ1qjh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92WW3oppxAL6kAXRcn1ZJZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 306}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "188xeDpElC6bo6yyrzPx4X"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 308}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "004lL4JolO6LCk1Vsge8i9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddH/V2/RtCfKHHAc2ceyZZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 311}, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65Wv22PBFA+pByopBrAno7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74+PG6s5VMLr9jPO520PxP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_worker_stat", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 249}, "_children": [{"__id__": 314}, {"__id__": 392}], "_active": true, "_components": [{"__id__": 406}], "_prefab": {"__id__": 408}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 339.13750000000005, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "layout_stat", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 313}, "_children": [{"__id__": 315}, {"__id__": 339}, {"__id__": 363}], "_active": true, "_components": [{"__id__": 387}, {"__id__": 389}], "_prefab": {"__id__": 391}, "_lpos": {"__type__": "cc.Vec3", "x": -308, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 314}, "_children": [{"__id__": 316}, {"__id__": 328}], "_active": true, "_components": [{"__id__": 334}, {"__id__": 336}], "_prefab": {"__id__": 338}, "_lpos": {"__type__": "cc.Vec3", "x": 36.04999542236328, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zm_icon_yuan_baise", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [{"__id__": 317}], "_active": true, "_components": [{"__id__": 323}, {"__id__": 325}], "_prefab": {"__id__": 327}, "_lpos": {"__type__": "cc.Vec3", "x": -20.04999542236328, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_relax-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 316}, "_children": [], "_active": true, "_components": [{"__id__": 318}, {"__id__": 320}], "_prefab": {"__id__": 322}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 319}, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 34.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36p/Byn6dHrYGjmRS4apKS"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 321}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 231, "g": 240, "b": 250, "a": 255}, "_string": "闲", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bYcqaqIpNyoTug68mVMwi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69H79oY51MrrwOnPWQJMYs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 316}, "_enabled": true, "__prefab": {"__id__": 324}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81swBpMp9JEbphjO4t5cn4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 316}, "_enabled": true, "__prefab": {"__id__": 326}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}, "_spriteFrame": {"__uuid__": "c1545f37-b08c-4f5e-aaa8-130871c185d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aeeM4Y22xDIbl2tlPH59KS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89ul/BRPZHdaiqXC1Co1m6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_relax", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [], "_active": true, "_components": [{"__id__": 329}, {"__id__": 331}], "_prefab": {"__id__": 333}, "_lpos": {"__type__": "cc.Vec3", "x": 0.9500045776367188, "y": 2.1240000000000236, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 330}, "_contentSize": {"__type__": "cc.Size", "width": 35.09999084472656, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e23U7pNSRNh5b59d0lTA9C"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 332}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}, "_string": "[3]", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aep+g+bqJEW5j0jXES8xqb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3f4ufsKrZLzYgz4oY1TNCs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 335}, "_contentSize": {"__type__": "cc.Size", "width": 72.09999084472656, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71W8m4jDxP64c4uegTIcmc"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 337}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 5, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcmQHn4xZAvqPBmBm0NHEh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69jyXvbp1E4YL1K8oZArga", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 314}, "_children": [{"__id__": 340}, {"__id__": 352}], "_active": true, "_components": [{"__id__": 358}, {"__id__": 360}], "_prefab": {"__id__": 362}, "_lpos": {"__type__": "cc.Vec3", "x": 128.14998626708984, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zm_icon_yuan_baise", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [{"__id__": 341}], "_active": true, "_components": [{"__id__": 347}, {"__id__": 349}], "_prefab": {"__id__": 351}, "_lpos": {"__type__": "cc.Vec3", "x": -20.04999542236328, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_relax-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 340}, "_children": [], "_active": true, "_components": [{"__id__": 342}, {"__id__": 344}], "_prefab": {"__id__": 346}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 341}, "_enabled": true, "__prefab": {"__id__": 343}, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 34.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07+ajki7JAab9G52uewDRI"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 341}, "_enabled": true, "__prefab": {"__id__": 345}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 231, "g": 240, "b": 250, "a": 255}, "_string": "总", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5e11kBffxEKKiSo6Rz8hqG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4wXI+IjBGR64HqM8peBvS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 340}, "_enabled": true, "__prefab": {"__id__": 348}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eBMQrjPVMiJp9emuRmdMm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 340}, "_enabled": true, "__prefab": {"__id__": 350}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_spriteFrame": {"__uuid__": "c1545f37-b08c-4f5e-aaa8-130871c185d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15GODgp2xLI7atvhvZvOvl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3iYJwc3JBBLpIlem/0iio", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_total", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 353}, {"__id__": 355}], "_prefab": {"__id__": 357}, "_lpos": {"__type__": "cc.Vec3", "x": 0.9500045776367188, "y": 2.1240000000000236, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": {"__id__": 354}, "_contentSize": {"__type__": "cc.Size", "width": 35.09999084472656, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bUIFiq1FDA6LIS4H6Y4q8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": {"__id__": 356}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "[3]", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cFYOKIqtPv6K3kqxTkKLZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31ajHj5VNGj7kdNTuq8Ylb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 359}, "_contentSize": {"__type__": "cc.Size", "width": 72.09999084472656, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3LTfnoftN5ZfFqwe8jh8E"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 361}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 5, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c1y4J8K1OnICAlUb87+Ga"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4hJgsKD1J/aMcYWWpZOfz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 314}, "_children": [{"__id__": 364}, {"__id__": 376}], "_active": true, "_components": [{"__id__": 382}, {"__id__": 384}], "_prefab": {"__id__": 386}, "_lpos": {"__type__": "cc.Vec3", "x": 220.2499771118164, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zm_icon_yuan_baise", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 363}, "_children": [{"__id__": 365}], "_active": true, "_components": [{"__id__": 371}, {"__id__": 373}], "_prefab": {"__id__": 375}, "_lpos": {"__type__": "cc.Vec3", "x": -20.04999542236328, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_relax-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 364}, "_children": [], "_active": true, "_components": [{"__id__": 366}, {"__id__": 368}], "_prefab": {"__id__": 370}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 365}, "_enabled": true, "__prefab": {"__id__": 367}, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 34.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50O9mLFpxJELFJ5OI72Eqa"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 365}, "_enabled": true, "__prefab": {"__id__": 369}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 231, "g": 240, "b": 250, "a": 255}, "_string": "雇", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3e0HGKRylJcKxv/wn3Rx2G"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e1gGtg21hKMY0nKH6tPhds", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 372}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6Mvn82dVOTYk3wQiYc3ze"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 374}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 121, "g": 85, "b": 193, "a": 255}, "_spriteFrame": {"__uuid__": "c1545f37-b08c-4f5e-aaa8-130871c185d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86XAialvNOGq6Z7XjVwj3e"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81AYFjU/5LJIyt6QZ14rTc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_hire_info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 363}, "_children": [], "_active": true, "_components": [{"__id__": 377}, {"__id__": 379}], "_prefab": {"__id__": 381}, "_lpos": {"__type__": "cc.Vec3", "x": 0.9500045776367188, "y": 2.1240000000000236, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": {"__id__": 378}, "_contentSize": {"__type__": "cc.Size", "width": 35.09999084472656, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1kX9cmUpMLLnTJ/hL5mQt"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": {"__id__": 380}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "[3]", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aVhh5881Ak5nmbP4RKa+q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4aHPSjoaNL5IVPs+iy565B", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 383}, "_contentSize": {"__type__": "cc.Size", "width": 72.09999084472656, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4EstkC9dLEZ7Wgy0avipq"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 385}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 5, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58miNADuBIV7QaJwhBOpxB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53nCizJzlEXL/c1lUOMMMC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 314}, "_enabled": true, "__prefab": {"__id__": 388}, "_contentSize": {"__type__": "cc.Size", "width": 256.2999725341797, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4EA/RCTJHAo722a1BHYhG"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 314}, "_enabled": true, "__prefab": {"__id__": 390}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 20, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41iXdabX9AEI7m8RGMHgOF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46Rwcr8axDrbdhkt0D0Egk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_call_back_all", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 313}, "_children": [{"__id__": 393}], "_active": true, "_components": [{"__id__": 399}, {"__id__": 401}, {"__id__": 403}], "_prefab": {"__id__": 405}, "_lpos": {"__type__": "cc.Vec3", "x": 218.386, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 392}, "_children": [], "_active": true, "_components": [{"__id__": 394}, {"__id__": 396}], "_prefab": {"__id__": 398}, "_lpos": {"__type__": "cc.Vec3", "x": 1, "y": 4, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 393}, "_enabled": true, "__prefab": {"__id__": 395}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "925MeaPuVHwar3ROw+YDWL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 393}, "_enabled": true, "__prefab": {"__id__": 397}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "全部召回", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 154, "g": 49, "b": 37, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cpsfsGdNLvaK+K72EVT+q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0d72mIGm1IJZIBUyxLCk54", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 392}, "_enabled": true, "__prefab": {"__id__": 400}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebe2bfILZP8oc0kSJ8ZsXZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 392}, "_enabled": true, "__prefab": {"__id__": 402}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4cef8caf-fbff-411d-a8a5-fe0d298c671f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3sGXcB9lH6bZHfNDVEpBv"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 392}, "_enabled": true, "__prefab": {"__id__": 404}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aTT1hWYJObq4tUl6lYJpn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "068fP1OetFW4meuyFRYi2b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 313}, "_enabled": true, "__prefab": {"__id__": 407}, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e20cKPnuxLGZPcex1Xm2ra"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1bqZEVWeRMMKtPcD5yMxey", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "list_my_hulu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 249}, "_children": [], "_active": true, "_components": [{"__id__": 410}, {"__id__": 412}, {"__id__": 414}, {"__id__": 416}, {"__id__": 418}], "_prefab": {"__id__": 420}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -32.52899999999994, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": {"__id__": 411}, "_contentSize": {"__type__": "cc.Size", "width": 616, "height": 663.333}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dhvONEJhEyoZfBYAVLzy1"}, {"__type__": "fe48a55/2VHg5MmeyH/Ap7U", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": {"__id__": 413}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feOG/uCMRPwqCzYMOWjR2/"}, {"__type__": "a6dafXbo3BJfLrODGZdcqPR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": {"__id__": 415}, "spaceY": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "804PLwPINPpoP4lsVr5Y6C"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": {"__id__": 417}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0NGq4u+lO7IlZpqQ0JB8V"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": {"__id__": 419}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dhyNzopxFIJykuqkdqmRp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beqkVGGSFBJIsLWHc1bjby", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_no_working", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 249}, "_children": [], "_active": false, "_components": [{"__id__": 422}, {"__id__": 424}], "_prefab": {"__id__": 426}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": {"__id__": 423}, "_contentSize": {"__type__": "cc.Size", "width": 184.00000235884664, "height": 57.96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebnI3R+89M7qNweYOzdrFK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": {"__id__": 425}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 128, "g": 166, "b": 201, "a": 255}, "_string": "暂未派遣", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 47.61, "_fontSize": 46, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06cHs//fhOh7hIrH9uZx66"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59S/uFWgNEH5KcYjqY7ooF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_action", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 249}, "_children": [{"__id__": 428}, {"__id__": 452}], "_active": true, "_components": [{"__id__": 486}], "_prefab": {"__id__": 488}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -439.1954999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_start_work", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [{"__id__": 429}, {"__id__": 437}], "_active": true, "_components": [{"__id__": 443}, {"__id__": 445}, {"__id__": 447}, {"__id__": 449}], "_prefab": {"__id__": 451}, "_lpos": {"__type__": "cc.Vec3", "x": -165.465, "y": -11.530999999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_start_work", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 428}, "_children": [], "_active": true, "_components": [{"__id__": 430}, {"__id__": 432}, {"__id__": 434}], "_prefab": {"__id__": 436}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 431}, "_contentSize": {"__type__": "cc.Size", "width": 6, "height": 50.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2l2EIQtVDiIORltjmtYs5"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 433}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43+Tgn9W5GJqrhHDJMaARq"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 435}, "_args": [], "_messageKey": 99, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70bthHfQ9OJL3Y8x1RSpvp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eaTek0abZBIJ9/DrYMlHPn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_collect_idle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 428}, "_children": [], "_active": true, "_components": [{"__id__": 438}, {"__id__": 440}], "_prefab": {"__id__": 442}, "_lpos": {"__type__": "cc.Vec3", "x": 4.916, "y": -54.076, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": {"__id__": 439}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89+KtEzuVDHLOe+VrFbaj8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": {"__id__": 441}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "正在采集", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 14, "g": 55, "b": 6, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4K/XR7TtEVLdbNW/+Sae7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1x9NjvPRIy4osAnXFGQQX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 444}, "_contentSize": {"__type__": "cc.Size", "width": 209, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9abE5zwnVGIpYoylH6GAKR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 446}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d305d5d2-e829-43ba-8678-c09c43c159a4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69gydt009Iu5qVY8Owsofy"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 448}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 27.969, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45OL35zvhI27QP0I2YUXiw"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 450}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 428}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0Z4slE7hMMILY4JssGabr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edrtVrEXBDn4eVxYwKIaxe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_hire", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [{"__id__": 453}, {"__id__": 459}, {"__id__": 465}, {"__id__": 471}], "_active": true, "_components": [{"__id__": 477}, {"__id__": 479}, {"__id__": 481}, {"__id__": 483}], "_prefab": {"__id__": 485}, "_lpos": {"__type__": "cc.Vec3", "x": 161.039, "y": -11.530999999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 452}, "_children": [], "_active": true, "_components": [{"__id__": 454}, {"__id__": 456}], "_prefab": {"__id__": 458}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 455}, "_contentSize": {"__type__": "cc.Size", "width": 98.5, "height": 48.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eecfgDkFdJz4YHG7AjJN7d"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 457}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "雇 佣 ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "028/0PRS5ARIRBMoz7VPXc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4P6VCDidA6JNmuZ9vXJi+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_hire_cost", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 452}, "_children": [], "_active": true, "_components": [{"__id__": 460}, {"__id__": 462}], "_prefab": {"__id__": 464}, "_lpos": {"__type__": "cc.Vec3", "x": -20.47, "y": 53.077, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 459}, "_enabled": true, "__prefab": {"__id__": 461}, "_contentSize": {"__type__": "cc.Size", "width": 37.199981689453125, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "054EBhDj1Bv5EWO6FnUeoT"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 459}, "_enabled": true, "__prefab": {"__id__": 463}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 225, "g": 0, "b": 0, "a": 255}, "_string": "1/4", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05MTfrY2FI2YToA61uBoR6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9NEUICTNDbbeR6q5f+uI7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "item_6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 452}, "_children": [], "_active": true, "_components": [{"__id__": 466}, {"__id__": 468}], "_prefab": {"__id__": 470}, "_lpos": {"__type__": "cc.Vec3", "x": -43.781, "y": 56.889, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.4, "y": 0.4, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 465}, "_enabled": true, "__prefab": {"__id__": 467}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "360f5bIv5Ly4rTs8T+OKhO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 465}, "_enabled": true, "__prefab": {"__id__": 469}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "32bb5c04-490c-453b-9790-f49510ca888d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6jWdNM8pAbY+rWL7Nvhoi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01syr6u0tCKYCYK4NO1LZb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_hire_count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 452}, "_children": [], "_active": true, "_components": [{"__id__": 472}, {"__id__": 474}], "_prefab": {"__id__": 476}, "_lpos": {"__type__": "cc.Vec3", "x": 4.916, "y": -54.076, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 473}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47xQjqLDJIjaSmByoIybE7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 475}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "正在采集", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 14, "g": 55, "b": 6, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "553ycAH0NHc7iau+CgdVpK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5SX1XdctOnb28qWw1h77s", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 478}, "_contentSize": {"__type__": "cc.Size", "width": 209, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fsXG/yaVN0aURC2Kagpp1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 480}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8aac8aa5-631a-436e-92ba-84e56018ec61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beiF7ybplN/LvLh6bnkxUH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 482}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 27.969, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bFCMSpr5Cy5ZrkTIDbcp2"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 484}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 452}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8AdKn6S1DF5hxOPIBVZZh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51Rjdz0fBNJqGq79D2Fs+5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 487}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28PfSgd+VHwrBWyKLTBv8h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72e+pfJpVB2pfCDg/W9Yds", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 249}, "_enabled": true, "__prefab": {"__id__": 490}, "_contentSize": {"__type__": "cc.Size", "width": 662.0970000000001, "height": 1079.795}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35Py4RdDJNhLcWqXAXb186"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "30uQbHjpdHvIQP+G3djCbq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 493}, "components": [{"__id__": 494}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 244}}, "node": {"__id__": 244}, "_enabled": true, "__prefab": {"__id__": 495}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79K9QdouhIL5isIMxHtNRI"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_name"], "value": "DialogSub"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 6.104, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 502}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 700, "height": 1150}}, {"__type__": "cc.TargetInfo", "localID": ["dcMfg1dj1MdZqre7xUgJqz"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 504}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 700, "height": 1150}}, {"__type__": "cc.TargetInfo", "localID": ["44g2VAc1VIU42rC+WZOf2b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 506}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 662.0970000000001, "height": 1079.795}}, {"__type__": "cc.TargetInfo", "localID": ["d8DuFrkIFIJrvRcNSr2YgG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 508}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 700, "height": 112}}, {"__type__": "cc.TargetInfo", "localID": ["c81ZpQbBZMrp/EQrEebYzT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 510}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -223.65950000000004, "y": -29.579000000000008, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b6eeINGDpEZLVcaNWdjJaK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 512}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 331.47950000000003, "y": 8.123999999999995, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["1fOxWCfPxPAqI5OgnftRyd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 514}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 5, "y": 547, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8d9SnxGQhLO52jTVFOf8SD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 516}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 140, "height": 85.68}}, {"__type__": "cc.TargetInfo", "localID": ["72cV1bU/9PTrznXTmFb0cY"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 518}, "propertyPath": ["_strTitle"], "value": "管理"}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 520}, "propertyPath": ["clickEvents", "length"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["630kdrTEhE3r2bQfHu5867"]}, {"__type__": "cc.TargetInfo", "localID": ["8bCtsUHKxMq6F/+Ej119ax"]}, {"__type__": "cc.TargetInfo", "localID": ["e0LlpQFI5EcLw3AA6mauJV"]}, {"__type__": "cc.TargetInfo", "localID": ["77g46nMrBFZ7le7NI/HU6H"]}, {"__type__": "cc.TargetInfo", "localID": ["e4I2EE2XdEy7GIQgcQlG0t"]}, {"__type__": "cc.TargetInfo", "localID": ["4cFBt7tfxGSY5HsbE7DB6k"]}, {"__type__": "cc.TargetInfo", "localID": ["f1ExBIooxGiYePxoMr8k3i"]}, {"__type__": "cc.TargetInfo", "localID": ["babhDkmCNDYqH1Zm6vqUdq"]}, {"__type__": "cc.TargetInfo", "localID": ["9ddIeszclP2KkZe+N/mFmL"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 530}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dOYQAoqpGGLBNgis7Vhg+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fcIS1XzjFAvrX4xl5SLFjw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 533}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddVf7BgG5LL6Aurjec9HdZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d7fVgnOctI4K8BzHhbqS/X", "instance": null, "targetOverrides": [{"__id__": 535}, {"__id__": 538}], "nestedPrefabInstanceRoots": [{"__id__": 244}, {"__id__": 281}, {"__id__": 157}, {"__id__": 80}, {"__id__": 47}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 244}, "sourceInfo": {"__id__": 536}, "propertyPath": ["graphics"], "target": {"__id__": 244}, "targetInfo": {"__id__": 537}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["9ddIeszclP2KkZe+N/mFmL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 244}, "sourceInfo": {"__id__": 539}, "propertyPath": ["dialogTitle"], "target": {"__id__": 244}, "targetInfo": {"__id__": 540}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["e9cwQVQARBJrTgOq5ECZIX"]}]