import { _decorator, Component, isValid, Label, Node, RichText, ScrollView, Sprite, UITransform, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { Sleep } from "../../GameDefine";
import { dtTime } from "../../BoutStartUp";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import { activityId, FrbpModule } from "../../../module/frbp/FrbpModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { RewardMessage } from "../../net/protocol/Comm";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import Formate from "../../../lib/utils/Formate";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { LangMgr } from "../../mgr/LangMgr";

const { ccclass, property } = _decorator;

@ccclass("UIFrbpAward")
export class UIFrbpAward extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FRBP}?prefab/ui/UIFrbpAward`;
  }

  private _vo: any;

  public init(args: any): void {
    super.init(args);
    this._vo = args.vo;
  }

  protected async onEvtShow() {
    this._vo = await FrbpModule.data.getVO(
      activityId,
      () => {
        TipsMgr.setEnableTouch(true);
      },
      () => {
        UIMgr.instance.back();
        TipsMgr.setEnableTouch(true);
      }
    );
    TipsMgr.setEnableTouch(true);

    let RankRewardVO = this._vo.rankRewardList;
    let enhancedData = this.enhanceRankData(RankRewardVO);

    this.setMyInfo();
    this.loadRankData(enhancedData);
  }

  private setMyInfo() {
    let rank = FrbpModule.data.SimpleRankMessage.rank;
    let diff = FrbpModule.data.SimpleRankMessage.point;

    this.getNode("lbl_rank_my").getComponent(Label).string = "我的排名：" + (rank <= -1 ? "未上榜" : rank);
    this.getNode("lbl_diff_num").getComponent(Label).string = Formate.format(diff);
  }

  private async loadRankData(enhancedData: Array<any>) {
    // for (let i = 1; i <= 3; i++) {
    //   this.getNode("type1_node").getChildByName("rank_show_" + i).active = false;
    // }
    // this.getNode("lbl_rank").active = false;

    for (let i = 0; i < enhancedData.length; i++) {
      if (i > 3) {
        await Sleep(dtTime);
      }
      if (isValid(this.node) == false) {
        return;
      }

      let data = enhancedData[i];
      let itemList = ToolExt.traAwardItemMapList(data.rewardList);
      let title = this.getAwardTitle(itemList);
      let node = null;
      if (title != null) {
        node = ToolExt.clone(this.getNode("type1_node"), this);
      } else {
        node = ToolExt.clone(this.getNode("type2_node"), this);
      }
      node.active = true;
      node.setPosition(v3(0, 0, 0));
      this.getNode("content_lay").addChild(node);

      node["lbl_rank"].active = true;
      node["lbl_rank"].getComponent(Label).string = "第 " + data.displayText + " 名";

      if (title != null) {
        PlayerModule.service.createTitle(node["title_root"], title.id, (node: Node, db) => {});
      }

      for (let i = 0; i < itemList.length; i++) {
        let award_item = ToolExt.clone(this.getNode("Item"));
        node["item_award_lay"].addChild(award_item);
        award_item.active = true;
        let item = itemList[i];
        FmUtils.setItemNode(award_item, item.id, item.num);
      }

      if (node["item_award_lay"].children.length < 7) {
        node["scrollView_award"].getComponent(ScrollView).enabled = false;
      }
    }
  }

  private getAwardTitle(itemList: Array<{ id: number; num: number }>) {
    for (let i = 0; i < itemList.length; i++) {
      let item = itemList[i];
      let info = JsonMgr.instance.getConfigItem(item.id);
      if (info.goodsType == 14) {
        let title = itemList.splice(i, 1)[0];
        return title;
      }
    }

    return null;
  }

  private enhanceRankData(originalData) {
    if (!Array.isArray(originalData)) {
      throw new Error("原始数据必须是一个数组！");
    }

    // 1. 提取并排序 rank 值（确保升序）
    const ranks = originalData.map((item) => item.rank).sort((a, b) => a - b);

    // 2. 遍历原始数据，生成新字段
    return originalData.map((item, index) => {
      const rank = item.rank;
      let displayText, isSingle;

      if (index === 0) {
        // 第一名：直接显示数字
        displayText = rank.toString();
        isSingle = true;
      } else {
        const prevRank = ranks[index - 1];
        if (rank === prevRank + 1) {
          // 连续排名（如 2, 3）：显示单独数字
          displayText = rank.toString();
          isSingle = true;
        } else {
          // 非连续排名（如 5, 10）：显示范围 "4-5", "6-10"
          displayText = `${prevRank + 1}-${rank}`;
          isSingle = false;
        }
      }

      // 返回增强后的对象（保留原字段，新增 displayText 和 isSingle）
      return {
        ...item,
        displayText,
        isSingle,
      };
    });
  }

  on_click_btn_close_huang_2() {
    UIMgr.instance.back();
  }

  on_click_btn_get_award() {
    FrbpModule.api.getRankAward(activityId, (res: RewardMessage) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList, transformList: res.transformList });
    });
  }

  getRemainingTime() {
    const diff = this._vo.publicityTime - TimeUtils.serverTime;
    if (diff <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    // 分解天数、小时、分钟、秒
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const remainingAfterDays = diff % (1000 * 60 * 60 * 24);
    const hours = Math.floor(remainingAfterDays / (1000 * 60 * 60));
    const remainingAfterHours = remainingAfterDays % (1000 * 60 * 60);
    const minutes = Math.floor(remainingAfterHours / (1000 * 60));
    const seconds = Math.floor((remainingAfterHours % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  }

  updateTimerDisplay() {
    const { days, hours, minutes, seconds } = this.getRemainingTime();

    // 拼接时间字符串
    let timeString;
    if (days > 0) {
      timeString = `${days}天${this.padZero(hours)}:${this.padZero(minutes)}:${this.padZero(seconds)}`;
    } else {
      timeString = `${this.padZero(hours)}:${this.padZero(minutes)}:${this.padZero(seconds)}`;
    }

    return timeString;
  }

  padZero(num) {
    return num < 10 ? `0${num}` : num;
  }

  private _tickIndex: number = 0;
  public tick(dt: any): void {
    if (TimeUtils.serverTime < this._vo.publicityTime || TimeUtils.serverTime > this._vo.endTime) {
      if (this.getNode("lbl_get_time").activeInHierarchy == false) {
        this.getNode("lbl_get_time").active = true;
      }
      if (this.getNode("btn_get_award").activeInHierarchy == true) {
        this.getNode("btn_get_award").active = false;
      }

      let str = this.updateTimerDisplay() + LangMgr.txMsgCode(525, [], "后邮件发放");
      this.getNode("lbl_get_time").getComponent(Label).string = str;
    } else {
      if (this.getNode("lbl_get_time").activeInHierarchy == true) {
        this.getNode("lbl_get_time").active = false;
      }
      if (this.getNode("btn_get_award").activeInHierarchy == false) {
        this.getNode("btn_get_award").active = true;
      }
    }

    let pengz = this.getNode("pengz");

    let content_list = this.getNode("content_lay");

    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();

    const index = this._tickIndex;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();

      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
      }
      this._tickIndex = i;
    }

    if (this._tickIndex >= content_list.children.length - 1) {
      this._tickIndex = 0;
    }
  }
}
