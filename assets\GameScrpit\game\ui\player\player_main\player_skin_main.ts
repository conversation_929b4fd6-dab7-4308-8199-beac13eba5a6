import {
  _decorator,
  Color,
  Component,
  instantiate,
  Label,
  Node,
  Prefab,
  RichText,
  ScrollView,
  sp,
  Sprite,
  UITransform,
  Vec2,
} from "cc";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { JsonMgr } from "../../../mgr/JsonMgr";
import ToolExt from "../../../common/ToolExt";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PlayerAudioName } from "db://assets/GameScrpit/module/player/PlayerConfig";
import TipMgr from "db://assets/GameScrpit/lib/tips/TipMgr";
import { ConfirmMsg } from "../../UICostConfirm";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { PlayerRouteName, PublicRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import { SkinMessage } from "../../../net/protocol/Player";
const { ccclass, property } = _decorator;
enum SKIN_TAB_ENUM {
  IDENTITY = "identity",
  ACTIVITY = "activity",
  SHENGDIAN = "shengDian",
}

enum SKIN_SHOW_TYPE_ENUM {
  PORTRAIT = "portrait",
  MODEL = "model",
}
@ccclass("player_skin_main")
export class player_skin_main extends BaseCtrl {
  private _skin_title_tab: SKIN_TAB_ENUM = SKIN_TAB_ENUM.ACTIVITY;
  private _skin_show_type: SKIN_SHOW_TYPE_ENUM = SKIN_SHOW_TYPE_ENUM.PORTRAIT;
  private _skin_listMap: Map<number, any[]> = new Map<number, any[]>();
  private _has_skin_list_map: Map<number, any[]> = new Map<number, any[]>();
  private _skin_node_list: Node[] = [];
  /**选中 */
  private _cur_Pitch_skinId: number = 0;
  /**使用 */
  private _cur_Use_skinId: number = 0;

  protected start(): void {
    super.start();
    this.showSkinMainIsState();
  }

  private showSkinMainIsState() {
    this.getSkinInfoList();
    this.changeScrollList();
    this.setShowType();
  }
  private setShowType() {
    if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.PORTRAIT) {
      this.getNode("skin_portrait_mask").active = true;
      this.getNode("skin_model_mask").active = false;
    } else if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.MODEL) {
      this.getNode("skin_portrait_mask").active = false;
      this.getNode("skin_model_mask").active = true;
    }
  }
  private getSkinInfoList() {
    let db = JsonMgr.instance.jsonList.c_leaderSkin;
    let dbList = Object.keys(db);
    let skinList1 = [];
    let skinList2 = [];
    let skinList3 = [];
    for (let i = 0; i < dbList.length; i++) {
      let data = db[dbList[i]];
      if (data.type == 1 && data.sex == PlayerModule.data.getPlayerInfo().sex) {
        skinList1.push(data);
      } else if (data.type == 2) {
        skinList2.push(data);
      } else if (data.type == 3) {
        skinList3.push(data);
      }
    }
    this._skin_listMap.set(1, skinList1);
    this._skin_listMap.set(2, skinList2);
    this._skin_listMap.set(3, skinList3);
  }
  private async initIdentityList() {
    this.getNode("content_identity_skin").destroyAllChildren();
    this._skin_node_list = [];
    this._has_skin_list_map.set(1, []);
    let list = this._skin_listMap.get(1);
    let boolType = false;
    if (this._cur_Pitch_skinId != 0 && JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId].type == 1) {
      boolType = true;
    }
    for (let i = 0; i < list.length; i++) {
      let data = list[i];
      let spineShowdb = JsonMgr.instance.jsonList.c_spineShow[data.spineId];
      let node = instantiate(this.getNode("btn_skin_card"));
      this.getNode("content_identity_skin").addChild(node);
      node.active = true;
      node.getChildByName("lab_skinName").getComponent(Label).string = data.name;
      node.getChildByPath("Mask/herohalf").getComponent(Sprite).spriteFrame = await this.assetMgr.loadSpriteFrameSync(
        BundleEnum.BUNDLE_G_PLAYER,
        spineShowdb.halfRes
      );
      node["skinInfo"] = data;
      if (boolType == false) {
        this._cur_Pitch_skinId = data.id;
        boolType = true;
      }
      let bool = false;
      if (PlayerModule.data.skinMap[data.id]) {
        bool = true;
        let list = this._has_skin_list_map.get(1);
        list.push(data);
        this._has_skin_list_map.set(1, list);
      }
      this.setUnSkinState(node, bool);
      this._skin_node_list.push(node);
      this.setActiveState(node, data.id);
      node.getChildByPath("active_state/btn_skin_active")["skinId"] = data.id;
      node.getChildByPath("btn_icon_suo")["skinId"] = data.id;
      node.getChildByPath("btn_skin_active")["card_node"] = node;
    }
  }
  private sortActivity() {
    let list = this._skin_listMap.get(2);
    let arr1 = [];
    let arr2 = [];
    let arr3 = [];
    for (let i = 0; i < list.length; i++) {
      let node = instantiate(this.getNode("btn_skin_card"));
      this.getNode("content_activity_skin").addChild(node);
      let data = list[i];
      let info = PlayerModule.data.skinMap[data.id];
      if (info && info.level >= 1) {
        arr1.push(data);
      } else if (info && info.level == 0) {
        arr2.push(data);
      } else {
        arr3.push(data);
      }
    }
    let newList = arr1.concat(arr2, arr3);
    this._skin_listMap.set(2, newList);
  }
  private async initActivityList() {
    this.getNode("content_activity_skin").destroyAllChildren();
    this._skin_node_list = [];
    this._has_skin_list_map.set(2, []);
    let list = this._skin_listMap.get(2);
    let boolType = false;
    if (this._cur_Pitch_skinId != 0 && JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId].type == 2) {
      boolType = true;
    }
    for (let i = 0; i < list.length; i++) {
      let data = list[i];
      let spineShowdb = JsonMgr.instance.jsonList.c_spineShow[data.spineId];
      let node = instantiate(this.getNode("btn_skin_card"));
      this.getNode("content_activity_skin").addChild(node);
      node.active = true;
      node.getChildByName("lab_skinName").getComponent(Label).string = data.name;

      node.getChildByPath("Mask/herohalf").getComponent(Sprite).spriteFrame = await this.assetMgr.loadSpriteFrameSync(
        BundleEnum.BUNDLE_G_PLAYER,
        spineShowdb.halfRes
      );

      node["skinInfo"] = data;
      if (boolType == false) {
        this._cur_Pitch_skinId = data.id;
        boolType = true;
      }
      let bool = false;
      let info = PlayerModule.data.skinMap[data.id];
      if (info && info.level >= 1) {
        bool = true;
        let list = this._has_skin_list_map.get(2);
        list.push(data);
        this._has_skin_list_map.set(2, list);
      }
      this.setUnSkinState(node, bool);
      this._skin_node_list.push(node);
      this.setActiveState(node, data.id);

      node.getChildByPath("active_state/btn_skin_active")["skinId"] = data.id;
      node.getChildByPath("btn_icon_suo")["skinId"] = data.id;
      node.getChildByPath("btn_skin_active")["card_node"] = node;
    }
  }
  private async initShengdianList() {
    this.getNode("content_shengdian_skin").destroyAllChildren();
    this._skin_node_list = [];
    this._has_skin_list_map.set(3, []);
    let list = this._skin_listMap.get(3);
    let boolType = false;
    if (this._cur_Pitch_skinId != 0 && JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId].type == 3) {
      boolType = true;
    }
    for (let i = 0; i < list.length; i++) {
      let data = list[i];
      let spineShowdb = JsonMgr.instance.jsonList.c_spineShow[data.spineId];
      let node = instantiate(this.getNode("btn_skin_card"));
      this.getNode("content_shengdian_skin").addChild(node);
      node.active = true;
      node.getChildByName("lab_skinName").active = true;
      node.getChildByName("lab_skinName").getComponent(Label).string = data.name;
      node.getChildByPath("Mask/herohalf").getComponent(Sprite).spriteFrame = await this.assetMgr.loadSpriteFrameSync(
        BundleEnum.BUNDLE_G_PLAYER,
        spineShowdb.halfRes
      );
      node["skinInfo"] = data;
      if (boolType == false) {
        this._cur_Pitch_skinId = data.id;
        boolType = true;
      }
      let bool = false;
      let info = PlayerModule.data.skinMap[data.id];
      if (info && info.level >= 1) {
        bool = true;
        let list = this._has_skin_list_map.get(3);
        list.push(data);
        this._has_skin_list_map.set(3, list);
      }
      this.setUnSkinState(node, bool);
      this._skin_node_list.push(node);
      this.setActiveState(node, data.id);
      node.getChildByPath("active_state/btn_skin_active")["skinId"] = data.id;
      node.getChildByPath("btn_icon_suo")["skinId"] = data.id;
      node.getChildByPath("btn_skin_active")["card_node"] = node;
    }
  }
  private setUnSkinState(skinNode: Node, colorBool: boolean) {
    let color = new Color(100, 100, 100, 255);
    if (colorBool == true) {
      color = new Color(255, 255, 255, 255);
    }
    // head.getComponent(Sprite).color = head.getComponent(Sprite).color = color;
    skinNode.getChildByName("kuang").getComponent(Sprite).color = color;
    skinNode.getChildByPath("Mask/herohalf").getComponent(Sprite).color = color;
    skinNode.getChildByName("btn_icon_suo").active = !colorBool;
  }
  private changePitchSkin() {
    this.foreachSkinList(this._cur_Pitch_skinId, "spr_cur_pitch");
    this.setTopSkinName();
    let curId = this._cur_Pitch_skinId;
    let data = PlayerModule.data.skinMap[curId];
    if (!data) {
      this.getNode("btn_use_skin").active = false;
      this.getNode("btn_jihuo_skin").active = true;
      this.getNode("btn_jihuo_skin").getComponent(Sprite).grayscale = true;
    } else if (data && data.level >= 1) {
      this.getNode("btn_use_skin").active = true;
      this.getNode("btn_jihuo_skin").active = false;
    } else if (data && data.level == 0) {
      this.getNode("btn_use_skin").active = false;
      this.getNode("btn_jihuo_skin").active = true;
      this.getNode("btn_jihuo_skin").getComponent(Sprite).grayscale = false;
    }
    //
    if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.PORTRAIT) {
      this.setSkinProtraitPrefab();
    } else if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.MODEL) {
      this.setSkinModelPrefab();
    }
  }
  private setActiveState(node: Node, id: number) {
    let db = JsonMgr.instance.jsonList.c_leaderSkin[id];
    if (db.type == 1 || db.type == 3) {
      node.getChildByName("active_state").active = false;
      return;
    }
    let data = PlayerModule.data.skinMap[id];
    let db1 = JsonMgr.instance.jsonList.c_leaderSkin[id];
    if (data && data.level >= 1) {
      node.getChildByName("active_state").active = false;
      node.getChildByName("btn_icon_suo").active = false;
    } else if (data && data.level == 0) {
      node.getChildByName("active_state").active = true;
      node.getChildByName("btn_icon_suo").active = false;
    } else if (!data) {
      node.getChildByName("active_state").active = false;
      node.getChildByName("btn_icon_suo").active = true;
    }
    // else {
    //   node["active_state"].active = false;
    // }
    if (db1.activateCostList.length > 0) {
      let skin_item_icon = node.getChildByPath("active_state/Layout/skin_item_icon");
      let lbl_skin_item_lab = node.getChildByPath("active_state/Layout/lbl_skin_item_lab");
      ToolExt.setItemIcon(skin_item_icon, db1.activateCostList[0]);
      lbl_skin_item_lab.getComponent(Label).string =
        Formate.format(PlayerModule.data.getItemNum(db1.activateCostList[0])) + "/" + db1.activateCostList[1];
      if (PlayerModule.data.getItemNum(db1.activateCostList[0]) >= db1.activateCostList[1]) {
        ToolExt.setLabColor(lbl_skin_item_lab.getComponent(Label), "#00af04");
      } else {
        ToolExt.setLabColor(lbl_skin_item_lab.getComponent(Label), "#FF0000");
      }
    }
  }
  private changeUseSkin() {
    this.foreachSkinList(this._cur_Use_skinId, "spr_cur_use");
  }
  private foreachSkinList(id: number, nodeKey: string) {
    for (let i = 0; i < this._skin_node_list.length; i++) {
      let skin_node = this._skin_node_list[i];
      let skinInfo = skin_node["skinInfo"];
      if (skinInfo.id == id) {
        skin_node.getChildByName(nodeKey).active = true;
      } else {
        skin_node.getChildByName(nodeKey).active = false;
      }
    }
  }
  private setTopSkinName() {
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    this.getNode("lab_top_skin_name").getComponent(Label).string = info.name;
    this.setAttrTip();
    this.setUnlockTip();
  }
  private setAttrTip() {
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    let db = JsonMgr.instance.jsonList.c_attribute;
    let attrAdd = info.attrAdd;
    if (attrAdd.length <= 0) {
      this.getNode("rich_addAttr").getComponent(RichText).string = `<color=#dfeaff>无</color>`;
      return;
    }
    let str = ``;
    for (let i = 0; i < attrAdd.length; i++) {
      let attrinfo = db[attrAdd[i][0]];
      str += `<color=#dfeaff>${attrinfo.name}</color>`;
      if (attrinfo.type1 == 1) {
        str += `<color=#eda03e>+${attrAdd[i][1]}</color>`;
      } else {
        str += `<color=#eda03e>+${attrAdd[i][1]}%</color>`;
      }
    }
    this.getNode("rich_addAttr").getComponent(RichText).string = str;
  }
  private setUnlockTip() {
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    if (info.type == 3) {
      this.getNode("rich_unlock").active = false;
      return;
    }
    this.getNode("rich_unlock").active = true;
    if (info.unlock.length < 2) {
      this.getNode("rich_unlock").getComponent(
        RichText
      ).string = `<outline color=#770404 width=2><color=#ffa0a0>默认解锁</color></outline>`;
      return;
    }
    let type = info.unlock[0];
    let param = info.unlock[1];
    switch (type) {
      case 0:
        this.getNode("rich_unlock").getComponent(
          RichText
        ).string = `<outline color=#770404 width=2><color=#ffa0a0>默认解锁</color></outline>`;
        break;
      case 1:
        this.getNode("rich_unlock").getComponent(
          RichText
        ).string = `<outline color=#770404 width=2><color=#ffa0a0>身份等级达到${param}级，永久有效</color></outline>`;
        break;
      case 2:
        this.getNode("rich_unlock").getComponent(
          RichText
        ).string = `<outline color=#770404 width=2><color=#ffa0a0>${info.source}</color></outline>`;
        break;
      case 3:
        this.getNode("rich_unlock").getComponent(
          RichText
        ).string = `<outline color=#770404 width=2><color=#ffa0a0>${info.source}</color></outline>`;
        break;
      default:
        break;
    }
  }
  private async setSkinProtraitPrefab() {
    this.getNode("zj_sweep_light").active = false;
    this.getNode("zj_sweep_light").getComponent(sp.Skeleton).setCompleteListener(null);
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    let roledb = JsonMgr.instance.jsonList.c_spineShow[info.spineId];
    if (!roledb) {
      return;
    }
    let name = "skin_point";
    let node: Node = this.getNode("skin_potrait_point").getChildByName(name);
    if (roledb.imageRes == "") {
      node.removeFromParent();
      node.destroy();
      let newNode = new Node(name);
      this.getNode("skin_potrait_point").addChild(newNode);
      return;
    }

    let pathList = roledb.imageRes.split("?");
    TipsMgr.setEnableTouch(false, 10);
    let prefabRole = await this.assetMgr.loadPrefabSync(pathList[0], pathList[1]);
    let newNode = instantiate(prefabRole);
    newNode["resPrefab"] = prefabRole;
    newNode.layer = this.getNode("skin_potrait_point").layer;
    newNode.walk((val) => {
      val.layer = newNode.layer;
    });
    newNode.name = name;
    this.getNode("skin_potrait_point").addChild(newNode);
    if (newNode.children[0].activeInHierarchy) {
      newNode.children[0].getComponent(sp.Skeleton).setAnimation(0, "animation1", true);
    }
    this.getNode("zj_sweep_light").active = true;
    this.getNode("zj_sweep_light")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("zj_sweep_light").active = false;
        this.getNode("zj_sweep_light").getComponent(sp.Skeleton).setCompleteListener(null);
      });
    this.getNode("zj_sweep_light").getComponent(sp.Skeleton).setAnimation(0, "animation", false);
    node.removeFromParent();
    let resPrefab: Prefab = node["resPrefab"];
    if (resPrefab) {
      resPrefab.decRef();
    }
    node.destroy();
  }
  private setSkinModelPrefab() {
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    let roledb = JsonMgr.instance.jsonList.c_spineShow[info.spineId];
    if (!roledb) {
      return;
    }
    let name = "skin_model";
    let node = this.getNode("skin_model_point").getChildByName(name);
    if (roledb.prefabPath == "" || roledb.prefabPath == "null") {
      node.removeFromParent();
      node.destroy();
      let newNode = new Node(name);
      this.getNode("skin_model_point").addChild(newNode);
      return;
    }
    ToolExt.loadUIRole(this.getNode("skin_model_point"), this._cur_Pitch_skinId, -1, "renderScale2", null);
  }
  private changeScrollList() {
    this._cur_Pitch_skinId = PlayerModule.data.skin.skinId;
    this._cur_Use_skinId = PlayerModule.data.skin.skinId;
    switch (this._skin_title_tab) {
      case SKIN_TAB_ENUM.IDENTITY:
        this._skin_show_type = SKIN_SHOW_TYPE_ENUM.MODEL;
        this.setShowType();
        this.getNode("btn_change_skin_type").active = false;
        this.getNode("scrollview_identity_skin").getComponent(ScrollView).scrollToTop(0.1);
        this.initIdentityList();
        this.getNode("scrollview_identity_skin").active = true;
        this.getNode("scrollview_activity_skin").active = false;
        this.getNode("scrollview_shengdian_skin").active = false;
        break;
      case SKIN_TAB_ENUM.ACTIVITY:
        this.getNode("btn_change_skin_type").active = true;
        this.getNode("scrollview_activity_skin").getComponent(ScrollView).scrollToTop(0.1);
        this.sortActivity();
        this.initActivityList();
        this.getNode("scrollview_identity_skin").active = false;
        this.getNode("scrollview_shengdian_skin").active = false;
        this.getNode("scrollview_activity_skin").active = true;
        break;
      case SKIN_TAB_ENUM.SHENGDIAN:
        this._skin_show_type = SKIN_SHOW_TYPE_ENUM.MODEL;
        this.setShowType();
        this.getNode("btn_change_skin_type").active = false;
        this.getNode("scrollview_shengdian_skin").getComponent(ScrollView).scrollToTop(0.1);
        this.initShengdianList();
        this.getNode("scrollview_identity_skin").active = false;
        this.getNode("scrollview_activity_skin").active = false;
        this.getNode("scrollview_shengdian_skin").active = true;
        break;
    }
    this.changePitchSkin();
    this.changeUseSkin();
    this.setLabSkinNum();
    this.changeSkinTabBtn();
  }

  private changeSkinTabBtn() {
    if (this._skin_title_tab == SKIN_TAB_ENUM.IDENTITY) {
      this.getNode("btn_skin_identity").getChildByName("pitch").active = true;
      this.getNode("btn_skin_identity").getChildByName("no_pitch").active = false;
      this.getNode("btn_skin_activity").getChildByName("pitch").active = false;
      this.getNode("btn_skin_activity").getChildByName("no_pitch").active = true;
      this.getNode("btn_skin_shengdian").getChildByName("pitch").active = false;
      this.getNode("btn_skin_shengdian").getChildByName("no_pitch").active = true;
      this.getNode("btn_watchSkill").active = false;
    } else if (this._skin_title_tab == SKIN_TAB_ENUM.ACTIVITY) {
      this.getNode("btn_skin_identity").getChildByName("pitch").active = false;
      this.getNode("btn_skin_identity").getChildByName("no_pitch").active = true;
      this.getNode("btn_skin_activity").getChildByName("pitch").active = true;
      this.getNode("btn_skin_activity").getChildByName("no_pitch").active = false;
      this.getNode("btn_skin_shengdian").getChildByName("pitch").active = false;
      this.getNode("btn_skin_shengdian").getChildByName("no_pitch").active = true;
      this.getNode("btn_watchSkill").active = true;
    } else if (this._skin_title_tab == SKIN_TAB_ENUM.SHENGDIAN) {
      this.getNode("btn_skin_identity").getChildByName("pitch").active = false;
      this.getNode("btn_skin_identity").getChildByName("no_pitch").active = true;
      this.getNode("btn_skin_activity").getChildByName("pitch").active = false;
      this.getNode("btn_skin_activity").getChildByName("no_pitch").active = true;
      this.getNode("btn_skin_shengdian").getChildByName("pitch").active = true;
      this.getNode("btn_skin_shengdian").getChildByName("no_pitch").active = false;
      this.getNode("btn_watchSkill").active = true;
    }
  }
  private setLabSkinNum() {
    let haslist = [];
    let maxList = [];
    switch (this._skin_title_tab) {
      case SKIN_TAB_ENUM.IDENTITY:
        haslist = this._has_skin_list_map.get(1) || [];
        maxList = this._skin_listMap.get(1) || [];
        break;
      case SKIN_TAB_ENUM.ACTIVITY:
        haslist = this._has_skin_list_map.get(2) || [];
        maxList = this._skin_listMap.get(2) || [];
        break;
      case SKIN_TAB_ENUM.SHENGDIAN:
        haslist = this._has_skin_list_map.get(3) || [];
        maxList = this._skin_listMap.get(3) || [];
        break;
    }
    this.getNode("lab_skin_hasNum").getComponent(Label).string = "已拥有:" + haslist.length + "/" + maxList.length;
  }
  private on_click_btn_skin_identity() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击页签);
    if (this._skin_title_tab == SKIN_TAB_ENUM.IDENTITY) {
      return;
    }
    this._skin_title_tab = SKIN_TAB_ENUM.IDENTITY;
    this.changeScrollList();
  }
  private on_click_btn_skin_activity() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击页签);
    if (this._skin_title_tab == SKIN_TAB_ENUM.ACTIVITY) {
      return;
    }
    this._skin_title_tab = SKIN_TAB_ENUM.ACTIVITY;
    this.changeScrollList();
  }
  private on_click_btn_skin_shengdian() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击页签);
    if (this._skin_title_tab == SKIN_TAB_ENUM.SHENGDIAN) {
      return;
    }
    this._skin_title_tab = SKIN_TAB_ENUM.SHENGDIAN;
    this.changeScrollList();
  }
  private on_click_btn_change_skin_type() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.PORTRAIT) {
      this._skin_show_type = SKIN_SHOW_TYPE_ENUM.MODEL;
      this.getNode("skin_portrait_mask").active = false;
      this.getNode("skin_model_mask").active = true;
      this.setSkinModelPrefab();
    } else if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.MODEL) {
      this._skin_show_type = SKIN_SHOW_TYPE_ENUM.PORTRAIT;
      this.getNode("skin_portrait_mask").active = true;
      this.getNode("skin_model_mask").active = false;
      this.setSkinProtraitPrefab();
    }
  }
  private on_click_btn_use_skin() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击换装使用按钮);
    let info = PlayerModule.data.skinMap[this._cur_Pitch_skinId];
    if (!info || info.level == -1) {
      TipMgr.showTip("未拥有皮肤");
      return;
    }
    PlayerModule.api.workSkinOrDecoration(info.skinId, () => {
      this._cur_Pitch_skinId = PlayerModule.data.skin.skinId;
      this._cur_Use_skinId = PlayerModule.data.skin.skinId;
      this.changePitchSkin();
      this.changeUseSkin();
      TipMgr.showTip("使用成功");
    });
  }
  private on_click_btn_jihuo_skin() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击激活按钮);
    let curId = this._cur_Pitch_skinId;
    let data = PlayerModule.data.skinMap[curId];
    if (!data) {
      TipMgr.showTip("请先获取该战将");
      return;
    }
    if (data && data.level >= 1) {
      return;
    }
    let db = JsonMgr.instance.jsonList.c_leaderSkin[curId];
    let id = db.activateCostList[0];
    let num = db.activateCostList[1];
    let my = PlayerModule.data.getItemNum(id);
    let itemdb = JsonMgr.instance.getConfigItem(id);
    let msg: ConfirmMsg = {
      msg: `是否花费'${num}${itemdb.name}'解锁该皮肤`,
      itemList: db.activateCostList,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        if (my < num) {
          UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
            itemId: id,
            needNum: num,
          });
          TipMgr.showTip(JsonMgr.instance.getConfigItem(id).name + "不足");
          return;
        }
        PlayerModule.api.activeSkin(curId, (data: SkinMessage) => {
          for (let i = 0; i < this._skin_node_list.length; i++) {
            let skin_node = this._skin_node_list[i];
            let skinInfo = skin_node["skinInfo"];
            if (skinInfo.id == data.skinId) {
              let callback = () => {
                TipMgr.showTip("解锁成功");
                this.changePitchSkin();
                this.changeUseSkin();
                this.setUnSkinState(skin_node, true);
                this.setActiveState(skin_node, data.skinId);
              };
              if (db.type == 2) {
                let node: Node = skin_node;
                this.moveScroll(skin_node);
                UIMgr.instance.showDialog(PlayerRouteName.UISkinOpen, {
                  leaderSkinId: curId,
                  end_node: node,
                  callback: callback,
                  keepMask: true,
                });
              } else {
                callback();
              }
            }
          }
        });
      }
    });
  }
  private on_click_btn_watchSkill() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击战斗预览按钮);
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    UIMgr.instance.showDialog(PlayerRouteName.UIWatchSkill, { roleId: this._cur_Pitch_skinId });
  }
  // private on_click_btn_xiangxixinxi() {
  //   let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
  //   UIMgr.instance.showDialog(PlayerRouteName.UIPlayerSkinTip, { info: info, keepMask: true });
  // }
  private on_click_btn_skin_card(event) {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击皮肤图标);
    if (this._cur_Pitch_skinId == event.node["skinInfo"].id) {
      return;
    }
    this._cur_Pitch_skinId = event.node["skinInfo"].id;
    this.changePitchSkin();
  }
  private moveScroll(node: Node) {
    let content: Node = node.parent;
    let scroll = content.parent.parent;
    let cadWorPos = node.getWorldPosition();
    let scrollWorPos = scroll.getWorldPosition();
    let dis = scrollWorPos.y - cadWorPos.y - scroll.getComponent(UITransform).height / 2;
    let newY = content.getPosition().y + dis;
    scroll.getComponent(ScrollView).scrollToOffset(new Vec2(0, newY), 0.15, true);
  }
  private on_click_btn_icon_suo(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let curId = event.node["skinId"];
    let data = PlayerModule.data.skinMap[curId];
    let db = JsonMgr.instance.jsonList.c_leaderSkin[curId];
    if (!data) {
      switch (db.unlock[0]) {
        case 1:
          TipMgr.showTip(`等级达到${db.unlock[1]}级解锁`);
          break;
        case 2:
          TipMgr.showTip(db.source);
          break;
        case 3:
          TipMgr.showTip(db.source);
          break;
        default:
          break;
      }
      return;
    }
    let id = db.activateCostList[0];
    let num = db.activateCostList[1];
    let my = PlayerModule.data.getItemNum(id);
    if (my < num) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: id,
        needNum: num,
      });
      return;
    }
  }
  private on_click_btn_skin_active(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let curId = event.node["skinId"];
    let data = PlayerModule.data.skinMap[curId];
    if (!data) {
      return;
    }
    if (data && data.level >= 1) {
      return;
    }
    let db = JsonMgr.instance.jsonList.c_leaderSkin[curId];
    let id = db.activateCostList[0];
    let num = db.activateCostList[1];
    let my = PlayerModule.data.getItemNum(id);
    let itemdb = JsonMgr.instance.getConfigItem(id);
    let msg: ConfirmMsg = {
      msg: `是否花费'${num}${itemdb.name}'解锁该皮肤`,
      itemList: db.activateCostList,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        if (my < num) {
          UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
            itemId: id,
            needNum: num,
          });
          TipMgr.showTip(JsonMgr.instance.getConfigItem(id).name + "不足");
          return;
        }
        PlayerModule.api.activeSkin(curId, (data: SkinMessage) => {
          for (let i = 0; i < this._skin_node_list.length; i++) {
            let skin_node = this._skin_node_list[i];
            let skinInfo = skin_node["skinInfo"];
            if (skinInfo.id == data.skinId) {
              //TipMgr.showTip("解锁成功");
              let callback = () => {
                this.setUnSkinState(skin_node, true);
                this.setActiveState(skin_node, data.skinId);
                this.changePitchSkin();
                this.changeUseSkin();
              };
              if (db.type == 2) {
                let node: Node = event.node;
                this.moveScroll(event.node["card_node"]);
                UIMgr.instance.showDialog(PlayerRouteName.UISkinOpen, {
                  leaderSkinId: curId,
                  end_node: node,
                  callback: callback,
                  keepMask: true,
                });
              } else {
                callback();
              }
            }
          }
        });
      }
    });
  }
}
