import { _decorator, Component, Node, sp, Sprite, Sprite<PERSON>rame, tween, v3, Vec3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UISkinOpen")
export class UISkinOpen extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PLAYER}?prefab/ui/UISkinOpen`;
  }

  private _leaderSkinId: number;
  private _end_node: Node;
  private _callback: Function;

  public init(args: any): void {
    super.init(args);
    this._leaderSkinId = args.leaderSkinId;
    this._end_node = args.end_node;
    this._callback = args.callback;
  }

  protected onEvtShow(): void {
    AudioMgr.instance.playEffect(1473);
    this.getNode("btn_close").active = false;
    this.getNode("herohalf").active = false;

    let db = JsonMgr.instance.jsonList.c_leaderSkin[this._leaderSkinId];
    if (db.unlock.length < 2) {
      return;
    }
    let heroId = db.unlock[1];
    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_HALF,
      `images/herohalf_${heroId}`,
      (spine: SpriteFrame) => {
        if (this.node.isValid == false) {
          return;
        }
        this.getNode("herohalf").getComponent(Sprite).spriteFrame = spine;
      }
    );

    this.setSktAni1();
    this.setSktAni2();
  }

  private setSktAni1() {
    this.getNode("kapai_unlock_1")
      .getComponent(sp.Skeleton)
      .setEventListener((animation, event) => {
        if (event["data"].name == "appear") {
          this.getNode("herohalf").active = true;
        }
      });

    this.getNode("kapai_unlock_1")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("kapai_appear_01" == trackEntry.animation.name) {
          this.getNode("btn_close").active = true;
          this.getNode("kapai_unlock_1").getComponent(sp.Skeleton).setAnimation(0, "kapai_disappear_01", true);
        }
      });

    this.getNode("kapai_unlock_1").getComponent(sp.Skeleton).setAnimation(0, "kapai_appear_01", false);
  }

  private setSktAni2() {
    this.getNode("kapai_unlock_2")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("kapai_appear_02" == trackEntry.animation.name) {
          this.getNode("kapai_unlock_2").getComponent(sp.Skeleton).setAnimation(0, "kapai_disappear_02", true);
        }
      });

    this.getNode("kapai_unlock_2").getComponent(sp.Skeleton).setAnimation(0, "kapai_appear_02", false);
  }

  private on_click_btn_close() {
    this.getNode("btn_close").active = false;
    const carNode = this.getNode("car_node");
    // 计算两个世界坐标的距离
    const temp = new Vec3();
    const distance = Vec3.subtract(temp, this._end_node.getWorldPosition(), carNode.worldPosition).length();

    const time = Math.max(0.1, distance / 1000);
    console.log("time", time);

    tween(carNode).to(time, { worldPosition: this._end_node.getWorldPosition() }).start();
    tween(this.getNode("layer1"))
      .to(time, { scale: v3(0, 0, 0) })
      .call(() => {
        this.layer2_Ani();
      })
      .start();
  }

  private layer2_Ani() {
    this.getNode("layer2").active = true;
    this.getNode("layer2")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("animation" == trackEntry.animation.name) {
          this._callback();
          UIMgr.instance.back();
        }
      });

    this.getNode("layer2").getComponent(sp.Skeleton).setAnimation(0, "animation", false);
  }
}
