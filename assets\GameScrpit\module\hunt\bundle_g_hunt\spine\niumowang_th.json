{"skeleton": {"hash": "nOcyU46YMQgnYmZ4HwmAWWufcDg=", "spine": "3.8.75", "x": -260.04, "y": -231.87, "width": 574.22, "height": 545.76, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/传奇_牛魔王"}, "bones": [{"name": "root"}, {"name": "ZONGKON_ALL", "parent": "root", "length": 455.53, "rotation": 0.5, "x": 7.59, "y": -38.98, "scaleX": 0.5, "scaleY": 0.5}, {"name": "ZONGKONG_ZHUTI", "parent": "ZONGKON_ALL", "length": 320.84, "rotation": -90.5, "x": -670.86, "y": -213.13, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI88", "parent": "ZONGKONG_ZHUTI", "length": 410.39, "rotation": 102.26, "x": 625.66, "y": 614.06, "scaleX": 0.6753, "scaleY": 0.6753, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI2", "parent": "ZONGKONG_ZHUTI88", "length": 361.05, "rotation": -0.83, "x": 337.79, "y": 896.75, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI3", "parent": "ZONGKONG_ZHUTI2", "length": 303.19, "rotation": 71.79, "x": -0.13, "y": -0.72, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI4", "parent": "ZONGKONG_ZHUTI3", "length": 323.09, "rotation": 14.73, "x": 303.19, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI5", "parent": "ZONGKONG_ZHUTI4", "length": 262.13, "rotation": -13.64, "x": 322.88, "y": -0.05, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI97", "parent": "ZONGKONG_ZHUTI4", "length": 329.99, "rotation": -45.13, "x": -3.31, "y": -78.51, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI6", "parent": "ZONGKONG_ZHUTI97", "length": 305.89, "rotation": 20.78, "x": 142.33, "y": 19.67, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI7", "parent": "ZONGKONG_ZHUTI6", "length": 336.4, "rotation": -149.53, "x": 0.24, "y": -0.06, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI8", "parent": "ZONGKONG_ZHUTI7", "length": 351.8, "rotation": 58.93, "x": 333.85, "y": 2.25, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI9", "parent": "ZONGKONG_ZHUTI8", "length": 191.09, "rotation": 42.75, "x": 351.14, "y": 1.22, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI96", "parent": "ZONGKONG_ZHUTI4", "length": 296.85, "rotation": 10.96, "x": 115.48, "y": 296.68, "color": "f17c15ff"}, {"name": "ZONGKONG_ZHUTI10", "parent": "ZONGKONG_ZHUTI96", "length": 433.88, "rotation": -2.74, "x": 158.5, "y": -34.21, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI11", "parent": "ZONGKONG_ZHUTI10", "length": 356.18, "rotation": 112.85, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI12", "parent": "ZONGKONG_ZHUTI11", "length": 354.89, "rotation": 32.44, "x": 354.94, "y": 1.03, "color": "f11616ff"}, {"name": "HEAD_HAIR", "parent": "ZONGKONG_ZHUTI5", "length": 69.62, "rotation": 93.47, "x": 113.91, "y": -0.42, "color": "ff15ffff"}, {"name": "HEAD_HAIR2", "parent": "HEAD_HAIR", "length": 70.59, "rotation": 105.31, "x": 25.25, "y": 15.89, "color": "ff15ffff"}, {"name": "HEAD_HAIR3", "parent": "HEAD_HAIR2", "length": 70.59, "x": 70.59, "color": "ff15ffff"}, {"name": "HEAD_HAIR4", "parent": "HEAD_HAIR3", "length": 70.59, "x": 70.59, "color": "ff15ffff"}, {"name": "HEAD_HAIR5", "parent": "HEAD_HAIR4", "length": 70.59, "x": 70.59, "color": "ff15ffff"}, {"name": "HEAD_HAIR6", "parent": "HEAD_HAIR", "length": 64.17, "rotation": 76.37, "x": 103.82, "y": -2.99, "color": "ff15ffff"}, {"name": "HEAD_HAIR7", "parent": "HEAD_HAIR6", "length": 64.17, "x": 64.17, "color": "ff15ffff"}, {"name": "HEAD_HAIR8", "parent": "HEAD_HAIR7", "length": 64.17, "x": 64.17, "color": "ff15ffff"}, {"name": "HEAD_HAIR9", "parent": "HEAD_HAIR", "length": 58.56, "rotation": 35.12, "x": 82.05, "y": -55.34, "color": "ff15ffff"}, {"name": "HEAD_HAIR10", "parent": "HEAD_HAIR9", "length": 51.18, "rotation": -15.23, "x": 58.56, "color": "ff15ffff"}, {"name": "HEAD_HAIR11", "parent": "HEAD_HAIR10", "length": 56.91, "rotation": 5.14, "x": 51.18, "color": "ff15ffff"}, {"name": "HEAD_HAIR12", "parent": "HEAD_HAIR", "length": 79.61, "rotation": 52.99, "x": 65.79, "y": -172, "color": "ff15ffff"}, {"name": "HEAD_HAIR13", "parent": "HEAD_HAIR12", "length": 63, "rotation": -8.92, "x": 79.33, "y": 0.34, "color": "ff15ffff"}, {"name": "HEAD_HAIR14", "parent": "HEAD_HAIR13", "length": 70.19, "rotation": -15.07, "x": 63, "color": "ff15ffff"}, {"name": "HEAD_HAIR15", "parent": "ZONGKONG_ZHUTI5", "length": 68.12, "rotation": 134.36, "x": 267.77, "y": 75.66, "color": "ff15ffff"}, {"name": "HEAD_HAIR16", "parent": "HEAD_HAIR15", "length": 47.96, "rotation": -10.49, "x": 68.12, "color": "ff15ffff"}, {"name": "HEAD_HAIR17", "parent": "HEAD_HAIR16", "length": 73.09, "rotation": -18.9, "x": 47.96, "color": "ff15ffff"}, {"name": "HEAD_HAIR18", "parent": "HEAD_HAIR", "length": 57.44, "rotation": 106.92, "x": -98.12, "y": -50.52, "color": "ff15ffff"}, {"name": "HEAD_HAIR19", "parent": "HEAD_HAIR18", "length": 57.44, "x": 57.44, "color": "ff15ffff"}, {"name": "HEAD_HAIR20", "parent": "HEAD_HAIR19", "length": 57.44, "x": 57.44, "color": "ff15ffff"}, {"name": "HEAD_HAIR21", "parent": "ZONGKONG_ZHUTI5", "length": 51.99, "rotation": -113.84, "x": 221.13, "y": -61.68, "color": "ff15ffff"}, {"name": "HEAD_HAIR22", "parent": "HEAD_HAIR21", "length": 51.99, "x": 51.99, "color": "ff15ffff"}, {"name": "HEAD_HAIR23", "parent": "HEAD_HAIR22", "length": 51.99, "x": 51.99, "color": "ff15ffff"}, {"name": "ZONGKONG_ZHUTI24", "parent": "ZONGKONG_ZHUTI5", "length": 40.92, "rotation": 0.38, "x": 299.14, "y": -10.39, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI25", "parent": "ZONGKONG_ZHUTI24", "length": 45.47, "rotation": 26.41, "x": 40.92, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI26", "parent": "ZONGKONG_ZHUTI25", "length": 50.06, "rotation": 13.78, "x": 45.47, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI27", "parent": "ZONGKONG_ZHUTI5", "length": 27.34, "rotation": 4.93, "x": 218.68, "y": 68.03, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI28", "parent": "ZONGKONG_ZHUTI27", "length": 29.63, "rotation": -83.38, "x": 0.03, "y": -14.24, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI29", "parent": "ZONGKONG_ZHUTI27", "length": 29.79, "rotation": -44.91, "x": 3.44, "y": -43.67, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI30", "parent": "ZONGKONG_ZHUTI27", "length": 37.33, "rotation": -85.55, "x": 24.54, "y": -64.7, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI31", "parent": "ZONGKONG_ZHUTI5", "length": 255.32, "rotation": -97.98, "x": 233.75, "y": -109.72, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI32", "parent": "ZONGKONG_ZHUTI5", "length": 197.39, "rotation": 93.13, "x": 262.54, "y": 122.84, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI34", "parent": "ZONGKONG_ZHUTI5", "length": 89.8, "rotation": -174.58, "x": 107.16, "y": -218.74, "color": "cdff2fff"}, {"name": "ZONGKONG_ZHUTI33", "parent": "ZONGKONG_ZHUTI5", "length": 159.13, "rotation": 4.56, "x": -36.84, "y": -202.26, "color": "cdff2fff"}, {"name": "ZONGKONG_ZHUTI35", "parent": "ZONGKONG_ZHUTI7", "length": 54.5, "rotation": 45.34, "x": 180.69, "y": 47.57, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI36", "parent": "ZONGKONG_ZHUTI35", "length": 59.13, "rotation": 7.58, "x": 54.5, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI37", "parent": "ZONGKONG_ZHUTI36", "length": 64.06, "rotation": 14.89, "x": 59.13, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI38", "parent": "ZONGKONG_ZHUTI4", "length": 196.43, "rotation": -30.53, "x": 0.75, "y": -0.78, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI39", "parent": "ZONGKONG_ZHUTI38", "length": 98.3, "rotation": 144.66, "x": -33.49, "y": 37.18, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI40", "parent": "ZONGKONG_ZHUTI38", "length": 135.21, "rotation": 140.49, "x": -113.68, "y": 94.04, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI41", "parent": "ZONGKONG_ZHUTI3", "length": 90.2, "rotation": 110.86, "x": 143.37, "y": 232.04, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI42", "parent": "ZONGKONG_ZHUTI97", "length": 107.91, "rotation": 82.1, "x": -31.02, "y": -90.67, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI43", "parent": "ZONGKONG_ZHUTI96", "length": 215.07, "rotation": -146.58, "x": 135, "y": -101.41, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI44", "parent": "ZONGKONG_ZHUTI11", "length": 92.02, "rotation": 51.21, "x": 55.14, "y": -43.98, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI45", "parent": "ZONGKONG_ZHUTI44", "length": 92.02, "x": 92.02, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI46", "parent": "ZONGKONG_ZHUTI45", "length": 92.02, "x": 92.02, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI47", "parent": "ZONGKONG_ZHUTI11", "length": 77.94, "rotation": 50.73, "x": 218.98, "y": -4.44, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI48", "parent": "ZONGKONG_ZHUTI47", "length": 77.94, "x": 77.94, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI49", "parent": "ZONGKONG_ZHUTI48", "length": 77.94, "x": 77.94, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI50", "parent": "ZONGKONG_ZHUTI11", "length": 55.92, "rotation": 23.56, "x": 228.64, "y": 20.98, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI51", "parent": "ZONGKONG_ZHUTI50", "length": 55.92, "x": 55.92, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI52", "parent": "ZONGKONG_ZHUTI51", "length": 55.92, "x": 55.92, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI53", "parent": "ZONGKONG_ZHUTI11", "length": 67.41, "rotation": 20.63, "x": 259.47, "y": -76.06, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI54", "parent": "ZONGKONG_ZHUTI53", "length": 58.41, "rotation": -16.03, "x": 67.41, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI55", "parent": "ZONGKONG_ZHUTI54", "length": 61.67, "rotation": -10.18, "x": 58.41, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI56", "parent": "ZONGKONG_ZHUTI11", "length": 69.28, "rotation": 33.49, "x": 335.11, "y": -166.26, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI57", "parent": "ZONGKONG_ZHUTI56", "length": 77.76, "rotation": -61.57, "x": 69.28, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI58", "parent": "ZONGKONG_ZHUTI57", "length": 93.1, "rotation": -41.67, "x": 75.65, "y": -1.17, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI59", "parent": "ZONGKONG_ZHUTI2", "length": 168.39, "rotation": 160.89, "x": -14.55, "y": 9.18, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI60", "parent": "ZONGKONG_ZHUTI59", "length": 79.26, "rotation": 68.04, "x": 214.66, "y": -18.49, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI61", "parent": "ZONGKONG_ZHUTI60", "length": 79.26, "x": 79.26, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI62", "parent": "ZONGKONG_ZHUTI61", "length": 79.26, "x": 79.26, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI63", "parent": "ZONGKONG_ZHUTI59", "length": 104.82, "rotation": 74.02, "x": 76.21, "y": -7.41, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI64", "parent": "ZONGKONG_ZHUTI63", "length": 104.82, "x": 104.82, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI65", "parent": "ZONGKONG_ZHUTI64", "length": 104.82, "x": 104.82, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI66", "parent": "ZONGKONG_ZHUTI59", "length": 108.41, "rotation": 89.7, "x": 287.14, "y": -86.2, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI67", "parent": "ZONGKONG_ZHUTI66", "length": 60.71, "rotation": -48.4, "x": 108.41, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI68", "parent": "ZONGKONG_ZHUTI67", "length": 60.71, "x": 60.71, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI69", "parent": "ZONGKONG_ZHUTI68", "length": 60.71, "x": 60.71, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI70", "parent": "ZONGKONG_ZHUTI59", "length": 45.23, "rotation": -177.33, "x": -147.13, "y": -21.55, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI71", "parent": "ZONGKONG_ZHUTI70", "length": 45.23, "x": 45.23, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI72", "parent": "ZONGKONG_ZHUTI71", "length": 45.23, "x": 45.23, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI73", "parent": "ZONGKONG_ZHUTI59", "length": 32.28, "rotation": 174.2, "x": -131.61, "y": 58.73, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI74", "parent": "ZONGKONG_ZHUTI73", "length": 32.28, "x": 32.28, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI75", "parent": "ZONGKONG_ZHUTI74", "length": 32.28, "x": 32.28, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI76", "parent": "ZONGKONG_ZHUTI59", "length": 58.68, "rotation": -159.14, "x": -177.38, "y": 5.5, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI77", "parent": "ZONGKONG_ZHUTI76", "length": 49.98, "rotation": 5.21, "x": 58.68, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI78", "parent": "ZONGKONG_ZHUTI77", "length": 55.67, "rotation": -13.85, "x": 49.98, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI79", "parent": "ZONGKONG_ZHUTI59", "length": 39.2, "rotation": -153.08, "x": -188.3, "y": 6.83, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI80", "parent": "ZONGKONG_ZHUTI79", "length": 39.2, "x": 39.2, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI81", "parent": "ZONGKONG_ZHUTI80", "length": 39.2, "x": 39.2, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI82", "parent": "ZONGKONG_ZHUTI59", "length": 56.28, "rotation": -170.79, "x": -148.87, "y": 54.09, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI83", "parent": "ZONGKONG_ZHUTI82", "length": 54.45, "rotation": 5.86, "x": 56.28, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI84", "parent": "ZONGKONG_ZHUTI83", "length": 62.95, "rotation": 3.2, "x": 54.45, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI95", "parent": "ZONGKONG_ZHUTI11", "length": 186.78, "rotation": 13.43, "x": 76.86, "y": -190.66, "color": "f11616ff"}, {"name": "bone", "parent": "ZONGKONG_ZHUTI5", "length": 38.32, "rotation": 6.95, "x": 226.72, "y": -76.28, "color": "b92c2cff"}, {"name": "bone2", "parent": "bone", "length": 24.57, "rotation": -73.51, "x": 14.15, "y": -19.33, "color": "b92c2cff"}, {"name": "bone3", "parent": "bone", "length": 27.69, "rotation": -34.77, "x": 21.12, "y": -42.89, "color": "b92c2cff"}, {"name": "bone4", "parent": "bone", "length": 28.46, "rotation": -39.13, "x": 43.87, "y": -58.68, "color": "b92c2cff"}, {"name": "bone5", "parent": "bone", "length": 25.73, "rotation": -105.3, "x": 65.95, "y": -76.65, "color": "b92c2cff"}, {"name": "HEAD_HAIR24", "parent": "HEAD_HAIR", "length": 76.94, "rotation": 17.95, "x": 165.27, "y": -19.81, "color": "ff15ffff"}, {"name": "HEAD_HAIR25", "parent": "HEAD_HAIR24", "length": 56.55, "rotation": -3.99, "x": 76.94, "color": "ff15ffff"}, {"name": "HEAD_HAIR26", "parent": "HEAD_HAIR25", "length": 72.81, "rotation": -2.65, "x": 56.55, "color": "ff15ffff"}, {"name": "ZONGKONG_ZHUTI135", "parent": "ZONGKONG_ZHUTI5", "length": 86.64, "rotation": -86.94, "x": 166.12, "y": -45.25, "color": "f11616ff"}, {"name": "ZONGKONG_ZHUTI136", "parent": "ZONGKONG_ZHUTI5", "length": 86.64, "rotation": -86.94, "x": 158.76, "y": 28.61, "scaleX": -1, "color": "f11616ff"}], "slots": [{"name": "btn_wenhao", "bone": "root"}, {"name": "ZHEZHAO", "bone": "ZONGKON_ALL"}, {"name": "HAND_R_BACK", "bone": "ZONGKONG_ZHUTI8", "attachment": "HAND_R_BACK"}, {"name": "HAND_R_4", "bone": "ZONGKON_ALL", "attachment": "HAND_R_4"}, {"name": "HAND_R_3", "bone": "ZONGKON_ALL", "attachment": "HAND_R_3"}, {"name": "HAND_R_2", "bone": "ZONGKONG_ZHUTI8", "attachment": "HAND_R_2"}, {"name": "HAND_R_PD_1", "bone": "ZONGKON_ALL", "attachment": "HAND_R_PD_1"}, {"name": "HAND_R_1", "bone": "ZONGKON_ALL", "attachment": "HAND_R_1"}, {"name": "XIABAI_HAIR_4", "bone": "ZONGKON_ALL", "attachment": "XIABAI_HAIR_4"}, {"name": "XIABAI_HAIR_3", "bone": "ZONGKON_ALL", "attachment": "XIABAI_HAIR_3"}, {"name": "XIABAI_BACK", "bone": "ZONGKON_ALL"}, {"name": "LEG_R_5", "bone": "ZONGKON_ALL"}, {"name": "LEG_R_1", "bone": "ZONGKON_ALL", "attachment": "LEG_R_1"}, {"name": "LEG_L_3", "bone": "ZONGKON_ALL"}, {"name": "LEG_L_1", "bone": "ZONGKON_ALL"}, {"name": "XIABAI_L", "bone": "ZONGKON_ALL"}, {"name": "YAODAI_HAIR_1", "bone": "ZONGKON_ALL", "attachment": "YAODAI_HAIR_1"}, {"name": "XIABAI_HAIR_R", "bone": "ZONGKON_ALL", "attachment": "XIABAI_HAIR_R"}, {"name": "XIABAI_HAIR_L", "bone": "ZONGKON_ALL", "attachment": "XIABAI_HAIR_L"}, {"name": "BODY_1", "bone": "ZONGKON_ALL", "attachment": "BODY_1"}, {"name": "BODY_2", "bone": "ZONGKON_ALL", "attachment": "BODY_2"}, {"name": "BODY_PEISHI_4", "bone": "ZONGKON_ALL", "attachment": "BODY_PEISHI_4"}, {"name": "BODY_PEISHI_3", "bone": "ZONGKONG_ZHUTI38", "attachment": "BODY_PEISHI_3"}, {"name": "BODY_PEISHI_2", "bone": "ZONGKON_ALL", "attachment": "BODY_PEISHI_2"}, {"name": "HAND_L_3", "bone": "ZONGKON_ALL", "attachment": "HAND_L_3"}, {"name": "HAND_L_2", "bone": "ZONGKON_ALL", "attachment": "HAND_L_2"}, {"name": "HAND_L_1", "bone": "ZONGKONG_ZHUTI12", "attachment": "HAND_L_1"}, {"name": "HAND_L_HAIR_5", "bone": "ZONGKON_ALL", "attachment": "HAND_L_HAIR_5"}, {"name": "YAODAI", "bone": "ZONGKONG_ZHUTI59", "attachment": "YAODAI"}, {"name": "HAND_L_HAIR_4", "bone": "ZONGKON_ALL", "attachment": "HAND_L_HAIR_4"}, {"name": "HAND_L_HAIR_3", "bone": "ZONGKON_ALL", "attachment": "HAND_L_HAIR_3"}, {"name": "HAND_L_HAIR_2", "bone": "ZONGKON_ALL", "attachment": "HAND_L_HAIR_2"}, {"name": "HAND_L_HAIR_1", "bone": "ZONGKON_ALL", "attachment": "HAND_L_HAIR_1"}, {"name": "NIUJIAO_R", "bone": "ZONGKONG_ZHUTI31", "attachment": "NIUJIAO_R"}, {"name": "EAR_R", "bone": "ZONGKON_ALL", "attachment": "EAR_R"}, {"name": "HAIR_6", "bone": "ZONGKON_ALL", "attachment": "HAIR_6"}, {"name": "HAIR_BACK", "bone": "ZONGKON_ALL", "attachment": "HAIR_BACK"}, {"name": "JIANJIA_L", "bone": "ZONGKONG_ZHUTI95", "attachment": "JIANJIA_L"}, {"name": "BODY_PEISHI_1", "bone": "ZONGKONG_ZHUTI43", "attachment": "BODY_PEISHI_1"}, {"name": "HAIR_5", "bone": "ZONGKON_ALL", "attachment": "HAIR_5"}, {"name": "HAIR_4", "bone": "ZONGKON_ALL", "attachment": "HAIR_4"}, {"name": "HAIR_3", "bone": "ZONGKON_ALL", "attachment": "HAIR_3"}, {"name": "HAIR_2", "bone": "ZONGKON_ALL", "attachment": "HAIR_2"}, {"name": "NIUJIAO_L", "bone": "ZONGKONG_ZHUTI32", "attachment": "NIUJIAO_L"}, {"name": "EAR_L", "bone": "ZONGKON_ALL", "attachment": "EAR_L"}, {"name": "HAIR_1", "bone": "ZONGKON_ALL", "attachment": "HAIR_1"}, {"name": "HEAD", "bone": "ZONGKON_ALL", "attachment": "HEAD"}, {"name": "EYE_R", "bone": "bone", "attachment": "EYE_R"}, {"name": "EYE_R2", "bone": "bone", "attachment": "EYE_R", "blend": "additive"}, {"name": "EYE_R3", "bone": "bone", "attachment": "EYE_R", "blend": "additive"}, {"name": "EYE_R4", "bone": "bone", "attachment": "EYE_R", "blend": "additive"}, {"name": "EYE_R5", "bone": "bone", "attachment": "EYE_R", "blend": "additive"}, {"name": "EYE_R6", "bone": "bone", "attachment": "EYE_R", "blend": "additive"}, {"name": "EYE_L", "bone": "ZONGKON_ALL", "attachment": "EYE_L"}, {"name": "EYE_L2", "bone": "ZONGKON_ALL", "attachment": "EYE_L", "blend": "additive"}, {"name": "EYE_L6", "bone": "ZONGKON_ALL", "attachment": "EYE_L", "blend": "additive"}, {"name": "EYE_L3", "bone": "ZONGKON_ALL", "attachment": "EYE_L", "blend": "additive"}, {"name": "EYE_L4", "bone": "ZONGKON_ALL", "attachment": "EYE_L", "blend": "additive"}, {"name": "EYE_L5", "bone": "ZONGKON_ALL", "attachment": "EYE_L", "blend": "additive"}, {"name": "EYELID", "bone": "ZONGKONG_ZHUTI5", "attachment": "EYELID"}, {"name": "HEAD_TX_R_1_1", "bone": "ZONGKONG_ZHUTI31", "color": "ffffff9a", "attachment": "HEAD_TX_8"}, {"name": "HEAD_TX_R_1_2", "bone": "ZONGKONG_ZHUTI31", "color": "ffffff10", "attachment": "HEAD_TX_8", "blend": "additive"}, {"name": "HEAD_TX_R_2_1", "bone": "ZONGKONG_ZHUTI31", "color": "ffffff9a", "attachment": "HEAD_TX_7"}, {"name": "HEAD_TX_R_2_2", "bone": "ZONGKONG_ZHUTI31", "color": "ffffff10", "attachment": "HEAD_TX_7", "blend": "additive"}, {"name": "HEAD_TX_R_3_1", "bone": "ZONGKONG_ZHUTI31", "color": "ffffff9a", "attachment": "HEAD_TX_6"}, {"name": "HEAD_TX_R_3_2", "bone": "ZONGKONG_ZHUTI31", "color": "ffffff10", "attachment": "HEAD_TX_6", "blend": "additive"}, {"name": "HEAD_TX_R_4_1", "bone": "ZONGKONG_ZHUTI31", "color": "ffffff9a", "attachment": "HEAD_TX_5"}, {"name": "HEAD_TX_R_4_2", "bone": "ZONGKONG_ZHUTI31", "color": "ffffff10", "attachment": "HEAD_TX_5", "blend": "additive"}, {"name": "HEAD_TX_L_1_1", "bone": "ZONGKONG_ZHUTI32", "color": "ffffff93", "attachment": "HEAD_TX_4"}, {"name": "HEAD_TX_L_1_2", "bone": "ZONGKONG_ZHUTI32", "color": "ffffff00", "attachment": "HEAD_TX_4", "blend": "additive"}, {"name": "HEAD_TX_L_2_1", "bone": "ZONGKONG_ZHUTI32", "color": "ffffff93", "attachment": "HEAD_TX_3"}, {"name": "HEAD_TX_L_2_2", "bone": "ZONGKONG_ZHUTI32", "color": "ffffff00", "attachment": "HEAD_TX_3", "blend": "additive"}, {"name": "HEAD_TX_L_3_1", "bone": "ZONGKONG_ZHUTI32", "color": "ffffff93", "attachment": "HEAD_TX_2"}, {"name": "HEAD_TX_L_3_2", "bone": "ZONGKONG_ZHUTI32", "color": "ffffff00", "attachment": "HEAD_TX_2", "blend": "additive"}, {"name": "HEAD_TX_L_4_1", "bone": "ZONGKONG_ZHUTI32", "color": "ffffff93"}, {"name": "HEAD_TX_L_4_2", "bone": "ZONGKONG_ZHUTI32", "color": "ffffff00", "blend": "additive"}, {"name": "Y", "bone": "ZONGKONG_ZHUTI135", "attachment": "Y"}, {"name": "Y2", "bone": "ZONGKONG_ZHUTI136", "attachment": "Y"}], "transform": [{"name": "HAIR_1", "order": 1, "bones": ["HEAD_HAIR2"], "target": "ZONGKONG_ZHUTI33", "rotation": -165.79, "x": 150.93, "y": 214.76, "rotateMix": 0, "translateMix": 0.108, "scaleMix": 0, "shearMix": 0}, {"name": "HAIR_2", "order": 2, "bones": ["HEAD_HAIR18"], "target": "ZONGKONG_ZHUTI33", "rotation": -164.18, "x": 214.97, "y": 90.14, "rotateMix": 0, "translateMix": 0.085, "scaleMix": 0, "shearMix": 0}, {"name": "HAIR_3", "order": 3, "bones": ["HEAD_HAIR6"], "target": "ZONGKONG_ZHUTI33", "rotation": 165.28, "x": 171.3, "y": 292.96, "rotateMix": 0, "translateMix": -0.09, "scaleMix": 0, "shearMix": 0}, {"name": "HAIR_4", "order": 4, "bones": ["HEAD_HAIR9"], "target": "ZONGKONG_ZHUTI33", "rotation": 124.02, "x": 223.22, "y": 270.19, "rotateMix": 0, "translateMix": -0.09, "scaleMix": 0, "shearMix": 0}, {"name": "HAIR_5", "order": 5, "bones": ["HEAD_HAIR12"], "target": "ZONGKONG_ZHUTI33", "rotation": 141.9, "x": 339.55, "y": 251.71, "rotateMix": 0, "translateMix": -0.11, "scaleMix": 0, "shearMix": 0}, {"name": "HAIR_6", "order": 6, "bones": ["HEAD_HAIR15"], "target": "ZONGKONG_ZHUTI33", "rotation": 129.8, "x": 325.75, "y": 252.82, "rotateMix": 0, "translateMix": -0.1, "scaleMix": 0, "shearMix": 0}, {"name": "HAIR_7", "order": 7, "bones": ["HEAD_HAIR21"], "target": "ZONGKONG_ZHUTI33", "rotation": -118.4, "x": 268.34, "y": 119.62, "rotateMix": 0, "translateMix": 0.108, "scaleMix": 0, "shearMix": 0}, {"name": "HAIR_8", "order": 10, "bones": ["ZONGKONG_ZHUTI24"], "target": "ZONGKONG_ZHUTI33", "rotation": -4.18, "x": 350.18, "y": 164.55, "rotateMix": 0, "translateMix": 0.1, "scaleMix": 0, "shearMix": 0}, {"name": "HEAD_ZM", "bones": ["ZONGKONG_ZHUTI34"], "target": "ZONGKONG_ZHUTI33", "rotation": -179.14, "x": 142.24, "y": -27.88, "rotateMix": 0, "translateMix": -0.8, "scaleMix": 0, "shearMix": 0}, {"name": "NIUJIAO_L", "order": 9, "bones": ["ZONGKONG_ZHUTI32"], "target": "ZONGKONG_ZHUTI33", "rotation": 88.57, "x": 324.28, "y": 300.26, "shearY": -360, "rotateMix": 0, "translateMix": 0.1, "scaleMix": 0, "shearMix": 0}, {"name": "NIUJIAO_R", "order": 8, "bones": ["ZONGKONG_ZHUTI31"], "target": "ZONGKONG_ZHUTI33", "rotation": -102.54, "x": 277.1, "y": 70.73, "rotateMix": 0, "translateMix": 0.108, "scaleMix": 0, "shearMix": 0}, {"name": "eye_l", "order": 11, "bones": ["ZONGKONG_ZHUTI27"], "target": "ZONGKONG_ZHUTI33", "rotation": 0.37, "x": 276.21, "y": 249.12, "rotateMix": 0, "translateMix": 0.143, "scaleMix": 0, "shearMix": 0}, {"name": "eye_r", "order": 12, "bones": ["bone"], "target": "ZONGKONG_ZHUTI33", "rotation": 2.39, "x": 272.75, "y": 104.63, "shearY": -360, "rotateMix": 0, "translateMix": 0.112, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"HEAD_TX_R_1_1": {"HEAD_TX_8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [190.95, -12.95, 50.18, -47.73, 22.12, 65.85, 162.88, 100.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 145, "height": 117}}, "JIANJIA_L": {"JIANJIA_L": {"type": "mesh", "uvs": [0.46316, 0.13736, 0.55169, 0.29451, 0.59693, 0.23277, 0.8566, 0.27954, 1, 0.461, 1, 0.75096, 0.80152, 0.79399, 0.66185, 0.74909, 0.62644, 0.85572, 0.57136, 1, 0.46119, 0.93617, 0.31169, 0.84637, 0.14448, 0.84637, 0.08153, 0.93617, 0, 0.85385, 0.08546, 0.54705, 0.14644, 0.47784, 0, 0.32631, 0.17595, 0.27206, 0.13464, 0.12988, 0, 0, 0.25661, 0], "triangles": [9, 10, 8, 13, 14, 12, 10, 11, 8, 8, 11, 7, 14, 15, 12, 11, 15, 16, 7, 11, 16, 7, 16, 1, 1, 16, 18, 18, 0, 1, 11, 12, 15, 5, 6, 4, 6, 7, 4, 7, 1, 3, 7, 3, 4, 3, 1, 2, 16, 17, 18, 0, 19, 21, 0, 18, 19, 19, 20, 21], "vertices": [1, 101, -197.18, -157.72, 1, 1, 101, -146.9, -48.79, 1, 1, 101, -197.51, -50.71, 1, 1, 101, -272.5, 100.9, 1, 1, 101, -230.32, 247.73, 1, 1, 101, -74.56, 365.29, 1, 1, 101, 25.08, 281.34, 1, 1, 101, 54.81, 191.79, 1, 1, 101, 125.74, 216.94, 1, 1, 101, 224.48, 247.3, 1, 1, 101, 232.67, 165.14, 1, 1, 101, 242.07, 52.36, 1, 1, 101, 306.54, -33.05, 1, 1, 101, 379.05, -28.8, 1, 1, 101, 366.27, -103.82, 1, 1, 101, 168.52, -184.55, 1, 1, 101, 107.82, -181.47, 1, 1, 101, 82.89, -317.71, 1, 1, 101, -14.09, -249.82, 1, 1, 101, -74.54, -328.57, 1, 1, 101, -92.39, -450.01, 1, 1, 101, -191.33, -318.93, 1], "hull": 22, "edges": [28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 40, 42, 0, 42, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28], "width": 640, "height": 673}}, "HAND_R_BACK": {"HAND_R_BACK": {"x": 349.32, "y": 4.57, "rotation": 16.16, "width": 95, "height": 131}}, "HEAD_TX_L_1_1": {"HEAD_TX_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [10.91, 30.49, 141.76, 36.82, 146.64, -64.06, 15.79, -70.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 101}}, "HAND_R_4": {"HAND_R_4": {"type": "mesh", "uvs": [0.71899, 0.02315, 0.99999, 0.21002, 0.96189, 0.79007, 0.6684, 0.91607, 0.37243, 0.95518, 0.33926, 0.96306, 0.18382, 1, 0, 1, 0, 0.91521, 0.00704, 0.77121, 0.01465, 0.61562, 0.13582, 0.59007, 0.21773, 0.57023, 0.2803, 0.55507, 0.25245, 0.39239, 0.41702, 0, 0.25576, 0.64931, 0.23018, 0.72886, 0.2445, 0.82108, 0.26036, 0.90486, 0.18968, 0.64601, 0.17039, 0.80907], "triangles": [6, 7, 21, 9, 21, 8, 11, 21, 9, 9, 10, 11, 19, 6, 21, 21, 7, 8, 21, 20, 17, 21, 11, 20, 20, 12, 16, 20, 11, 12, 6, 19, 5, 19, 21, 18, 21, 17, 18, 17, 20, 16, 16, 12, 13, 16, 3, 4, 3, 16, 13, 3, 13, 0, 3, 0, 2, 15, 0, 14, 2, 0, 1, 0, 13, 14, 4, 5, 18, 18, 5, 19, 18, 16, 4, 18, 17, 16], "vertices": [1, 12, 214.03, 99.1, 1, 1, 12, 279.55, 14.05, 1, 2, 12, 202.91, -109.75, 0.99897, 11, 574.63, 58.38, 0.00103, 2, 12, 98.53, -92.72, 0.88884, 11, 486.43, 0.03, 0.11116, 1, 12, 3.11, -55.88, 1, 1, 12, -7.97, -52.53, 1, 1, 11, 332.15, -66.51, 1, 1, 11, 271.42, -84.11, 1, 1, 11, 265.52, -63.75, 1, 1, 11, 257.83, -28.5, 1, 1, 11, 249.52, 9.59, 1, 1, 11, 287.77, 27.33, 1, 1, 11, 313.46, 39.93, 1, 1, 12, 19.55, 47.75, 1, 1, 12, 29.19, 88.41, 1, 1, 12, 123.73, 150.78, 1, 1, 12, 1.46, 30.47, 1, 1, 12, -15.32, 16.62, 1, 1, 12, -21.23, -6.2, 1, 1, 12, -25.73, -27.37, 1, 1, 11, 309.46, 19.05, 1, 2, 12, -42.68, 7.9, 0.0001, 11, 314.43, -21.95, 0.9999], "hull": 16, "edges": [0, 30, 0, 2, 4, 6, 12, 14, 14, 16, 26, 28, 6, 8, 16, 18, 18, 20, 20, 22, 28, 30, 2, 4, 26, 32, 32, 34, 34, 36, 36, 38, 8, 10, 10, 12, 38, 10, 22, 24, 24, 26, 24, 40, 40, 42, 42, 12], "width": 344, "height": 250}}, "EAR_R": {"EAR_R": {"type": "mesh", "uvs": [0.19987, 0.03987, 0.42007, 0.12543, 0.74123, 0.43903, 0.99999, 0.827, 0.95549, 0.94916, 0.62834, 1, 0.28061, 0.9904, 0.136, 0.93372, 0.00612, 0.81514, 0.00629, 0.40559, 0.00647, 0.00275, 0.32494, 0.56754, 0.60493, 0.71303, 0.12594, 0.4664], "triangles": [12, 6, 11, 6, 7, 11, 6, 12, 5, 5, 12, 4, 4, 12, 3, 12, 2, 3, 7, 8, 11, 8, 13, 11, 8, 9, 13, 12, 11, 2, 2, 11, 1, 11, 13, 1, 13, 0, 1, 13, 9, 0, 9, 10, 0], "vertices": [3, 37, -5.37, 56.01, 0.8047, 38, -57.35, 56.01, 0.17455, 39, -109.34, 56.01, 0.02075, 3, 37, 29.45, 61.22, 0.55608, 38, -22.54, 61.22, 0.2913, 39, -74.52, 61.22, 0.15261, 3, 37, 94.18, 44.39, 0.26384, 38, 42.19, 44.39, 0.34057, 39, -9.79, 44.39, 0.39559, 3, 37, 156.33, 13.33, 0.08007, 38, 104.35, 13.33, 0.32609, 39, 52.36, 13.33, 0.59384, 3, 37, 159.61, -5.76, 0.08894, 38, 107.62, -5.76, 0.34317, 39, 55.64, -5.76, 0.56789, 3, 37, 121.04, -36.52, 0.09152, 38, 69.06, -36.52, 0.43286, 39, 17.07, -36.52, 0.47562, 3, 37, 75.35, -60.99, 0.23215, 38, 23.36, -60.99, 0.50795, 39, -28.62, -60.99, 0.25991, 3, 37, 52.45, -64.35, 0.43863, 38, 0.46, -64.35, 0.48518, 39, -51.52, -64.35, 0.07619, 3, 37, 26.88, -58.61, 0.6737, 38, -25.11, -58.61, 0.32291, 39, -77.09, -58.61, 0.00339, 3, 37, -3.37, -5.61, 0.88286, 38, -55.36, -5.61, 0.11707, 39, -107.34, -5.61, 7e-05, 2, 37, -33.13, 46.52, 0.92479, 38, -85.11, 46.52, 0.07521, 3, 37, 49.82, -3.01, 0.61702, 38, -2.16, -3.01, 0.27131, 39, -54.15, -3.01, 0.11167, 3, 37, 96.8, -1.13, 0.30027, 38, 44.81, -1.13, 0.36906, 39, -7.17, -1.13, 0.33068, 3, 37, 16.6, -4.63, 0.82636, 38, -35.38, -4.63, 0.17337, 39, -87.37, -4.63, 0.00028], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 24, 8, 6, 8, 18, 26, 26, 22], "width": 149, "height": 149}}, "HEAD_TX_L_1_2": {"HEAD_TX_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [10.91, 30.49, 141.76, 36.82, 146.64, -64.06, 15.79, -70.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 101}}, "HAND_R_1": {"HAND_R_1": {"type": "mesh", "uvs": [0.60666, 0.06506, 0.87922, 0.14475, 0.94011, 0.4147, 1, 0.68021, 1, 0.78173, 0.89743, 0.91769, 0.74238, 0.96235, 0.61166, 1, 0.49126, 1, 0.3596, 0.91331, 0.17637, 0.79266, 0, 0.43926, 1e-05, 0.29243, 0.05157, 0.01923, 0.20951, 1e-05, 0.60435, 0.77655, 0.66381, 0.87894, 0.48172, 0.84508, 0.82388, 0.72309, 0.45102, 0.40937, 0.30706, 0.1587], "triangles": [4, 5, 18, 6, 16, 5, 18, 2, 3, 16, 15, 18, 20, 14, 0, 13, 14, 20, 12, 13, 20, 19, 20, 0, 19, 0, 1, 12, 20, 19, 19, 1, 2, 11, 12, 19, 18, 19, 2, 10, 11, 19, 10, 19, 15, 17, 10, 15, 17, 15, 16, 9, 10, 17, 8, 17, 16, 7, 8, 16, 9, 17, 8, 6, 7, 16, 15, 19, 18, 18, 3, 4, 5, 16, 18], "vertices": [1, 10, -16.68, 95.4, 1, 2, 10, 42.43, 160.33, 0.9972, 11, -15.01, 331.2, 0.0028, 2, 10, 179.17, 141.93, 0.928, 11, 39.8, 204.59, 0.072, 2, 10, 313.65, 123.84, 0.76844, 11, 93.71, 80.06, 0.23156, 2, 10, 363.39, 110.59, 0.01086, 11, 108.04, 30.62, 0.98914, 2, 10, 422.44, 64.5, 0.1334, 11, 99.04, -43.75, 0.8666, 2, 10, 432.91, 15.82, 0.53636, 11, 62.75, -77.84, 0.46364, 2, 10, 441.73, -25.22, 0.84612, 11, 32.15, -106.58, 0.15388, 2, 10, 432.87, -58.49, 0.95147, 11, -0.92, -116.16, 0.04853, 1, 10, 380.71, -83.56, 1, 1, 10, 308.12, -118.46, 1, 1, 10, 121.99, -121.09, 1, 1, 10, 50.06, -101.92, 1, 1, 10, -79.99, -52.02, 1, 1, 10, -77.78, -5.86, 1, 2, 10, 331.73, 1.92, 0.86602, 11, -1.38, 1.65, 0.13398, 2, 10, 386.26, 4.99, 0.60691, 11, 29.4, -43.47, 0.39309, 1, 10, 356.27, -40.91, 1, 2, 10, 321.7, 69.57, 0.80149, 11, 51.38, 45.16, 0.19851, 2, 10, 140.55, 7.46, 0.99908, 11, -95.31, 168.26, 0.00092, 1, 10, 7.15, 0.39, 1], "hull": 15, "edges": [6, 8, 8, 10, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 16, 18, 18, 20, 10, 12, 12, 14, 18, 34, 34, 30, 6, 36, 36, 30, 0, 2, 28, 0, 2, 4, 4, 6], "width": 286, "height": 507}}, "EYE_R2": {"EYE_R": {"type": "mesh", "uvs": [0.90755, 0.06732, 1, 0.14302, 1, 0.18124, 1, 0.21451, 0.90869, 0.18367, 0.82109, 0.16685, 0.74145, 0.20049, 0.71073, 0.30983, 0.65271, 0.5201, 0.49912, 0.62103, 0.38812, 0.66741, 0.36423, 0.81599, 0.27208, 0.90991, 0.10597, 0.87207, 0.05023, 0.75151, 0.06388, 0.60573, 0.19358, 0.52583, 0.30507, 0.46695, 0.44549, 0.46209, 0.55243, 0.40462, 0.60249, 0.26023, 0.65482, 0.11444, 0.7902, 0.04856, 0.47499, 0.54374, 0.60307, 0.45624, 0.65633, 0.28124, 0.69944, 0.16406, 0.80723, 0.10937, 0.91629, 0.14218, 0.35228, 0.58091], "triangles": [16, 14, 15, 13, 14, 16, 16, 17, 29, 12, 13, 16, 16, 11, 12, 10, 11, 16, 10, 16, 29, 23, 18, 19, 29, 18, 23, 9, 23, 24, 10, 23, 9, 29, 17, 18, 10, 29, 23, 24, 25, 7, 24, 19, 25, 8, 24, 7, 23, 19, 24, 9, 24, 8, 26, 21, 22, 6, 26, 27, 20, 21, 26, 25, 20, 26, 25, 26, 6, 7, 25, 6, 25, 19, 20, 27, 22, 0, 1, 28, 0, 27, 0, 28, 27, 26, 22, 5, 27, 28, 28, 1, 2, 4, 5, 28, 4, 28, 2, 5, 6, 27, 4, 2, 3], "vertices": [1, 106, 16.17, 7.88, 1, 1, 106, 30.6, 2.75, 1, 1, 106, 31.64, -1.4, 1, 2, 106, 32.55, -5.02, 0.98259, 105, 37.02, -31.8, 0.01741, 2, 106, 19.48, -4.72, 0.71334, 105, 32.01, -19.73, 0.28666, 3, 106, 7.3, -5.83, 0.38001, 105, 26.08, -9.03, 0.61896, 104, 53.01, -10.99, 0.00103, 3, 106, -2.45, -12.15, 0.06409, 105, 16.36, -2.67, 0.68152, 104, 43.8, -3.91, 0.25439, 2, 105, 4.09, -6.84, 0.41228, 104, 31.25, -7.13, 0.58772, 3, 105, -19.42, -14.98, 0.07997, 104, 7.19, -13.46, 0.58669, 103, 38.6, -6, 0.33333, 3, 104, -13.94, -2.03, 0.33333, 103, 14.97, -10.3, 0.61278, 102, 8.52, -36.61, 0.05388, 3, 104, -26.73, 7.88, 0.00256, 103, -1.21, -10.58, 0.69563, 102, 3.66, -21.18, 0.30181, 2, 103, -9.42, -25.43, 0.3837, 102, -12.9, -17.52, 0.6163, 2, 103, -24.74, -31.57, 0.10425, 102, -23.14, -4.57, 0.89575, 2, 103, -45.27, -20.55, 0.02047, 102, -18.4, 18.25, 0.97953, 1, 102, -4.73, 25.64, 1, 1, 102, 11.55, 23.4, 1, 3, 104, -28.33, 39.02, 0.00369, 103, -21.94, 12.7, 0.13358, 102, 20.11, 5.31, 0.86273, 3, 104, -14.33, 29.83, 0.10445, 103, -5.28, 14.29, 0.41743, 102, 26.36, -10.22, 0.47812, 4, 105, -31.85, 11.58, 0.00774, 104, -3.18, 13.97, 0.45852, 103, 13.34, 8.91, 0.32857, 102, 26.48, -29.6, 0.20517, 4, 105, -17.7, 3.89, 0.33703, 104, 10.33, 5.22, 0.45888, 103, 29.36, 10.54, 0.19499, 102, 32.59, -44.5, 0.00911, 4, 106, -19.43, -23.29, 0.02497, 105, -0.7, 8.36, 0.64539, 104, 27.63, 8.39, 0.32672, 103, 40.87, 23.84, 0.00293, 3, 106, -16.39, -5.7, 0.3583, 105, 16.62, 12.68, 0.63765, 104, 45.23, 11.38, 0.00405, 2, 106, -0.05, 5.99, 0.69164, 105, 33.92, 2.47, 0.30836, 1, 103, 14.43, -1.04, 1, 1, 104, 9.37, -3.8, 1, 1, 105, 2.01, 1.05, 1, 2, 106, -9.06, -9.6, 0.0318, 105, 16.02, 4.41, 0.9682, 2, 106, 3.88, -0.05, 0.99493, 105, 29.99, -3.57, 0.00507, 1, 106, 19.37, 0.04, 1, 3, 104, -21.38, 17.35, 0.0071, 103, -2.96, 0.15, 0.64397, 102, 13.46, -16.45, 0.34893], "hull": 23, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 0, 2, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 2, 4, 4, 6, 20, 58, 58, 34], "width": 138, "height": 112}}, "EYE_R3": {"EYE_R": {"type": "mesh", "uvs": [0.90755, 0.06732, 1, 0.14302, 1, 0.18124, 1, 0.21451, 0.90869, 0.18367, 0.82109, 0.16685, 0.74145, 0.20049, 0.71073, 0.30983, 0.65271, 0.5201, 0.49912, 0.62103, 0.38812, 0.66741, 0.36423, 0.81599, 0.27208, 0.90991, 0.10597, 0.87207, 0.05023, 0.75151, 0.06388, 0.60573, 0.19358, 0.52583, 0.30507, 0.46695, 0.44549, 0.46209, 0.55243, 0.40462, 0.60249, 0.26023, 0.65482, 0.11444, 0.7902, 0.04856, 0.47499, 0.54374, 0.60307, 0.45624, 0.65633, 0.28124, 0.69944, 0.16406, 0.80723, 0.10937, 0.91629, 0.14218, 0.35228, 0.58091], "triangles": [16, 14, 15, 13, 14, 16, 16, 17, 29, 12, 13, 16, 16, 11, 12, 10, 11, 16, 10, 16, 29, 23, 18, 19, 29, 18, 23, 9, 23, 24, 10, 23, 9, 29, 17, 18, 10, 29, 23, 24, 25, 7, 24, 19, 25, 8, 24, 7, 23, 19, 24, 9, 24, 8, 26, 21, 22, 6, 26, 27, 20, 21, 26, 25, 20, 26, 25, 26, 6, 7, 25, 6, 25, 19, 20, 27, 22, 0, 1, 28, 0, 27, 0, 28, 27, 26, 22, 5, 27, 28, 28, 1, 2, 4, 5, 28, 4, 28, 2, 5, 6, 27, 4, 2, 3], "vertices": [1, 106, 16.17, 7.88, 1, 1, 106, 30.6, 2.75, 1, 1, 106, 31.64, -1.4, 1, 2, 106, 32.55, -5.02, 0.98259, 105, 37.02, -31.8, 0.01741, 2, 106, 19.48, -4.72, 0.71334, 105, 32.01, -19.73, 0.28666, 3, 106, 7.3, -5.83, 0.38001, 105, 26.08, -9.03, 0.61896, 104, 53.01, -10.99, 0.00103, 3, 106, -2.45, -12.15, 0.06409, 105, 16.36, -2.67, 0.68152, 104, 43.8, -3.91, 0.25439, 2, 105, 4.09, -6.84, 0.41228, 104, 31.25, -7.13, 0.58772, 3, 105, -19.42, -14.98, 0.07997, 104, 7.19, -13.46, 0.58669, 103, 38.6, -6, 0.33333, 3, 104, -13.94, -2.03, 0.33333, 103, 14.97, -10.3, 0.61278, 102, 8.52, -36.61, 0.05388, 3, 104, -26.73, 7.88, 0.00256, 103, -1.21, -10.58, 0.69563, 102, 3.66, -21.18, 0.30181, 2, 103, -9.42, -25.43, 0.3837, 102, -12.9, -17.52, 0.6163, 2, 103, -24.74, -31.57, 0.10425, 102, -23.14, -4.57, 0.89575, 2, 103, -45.27, -20.55, 0.02047, 102, -18.4, 18.25, 0.97953, 1, 102, -4.73, 25.64, 1, 1, 102, 11.55, 23.4, 1, 3, 104, -28.33, 39.02, 0.00369, 103, -21.94, 12.7, 0.13358, 102, 20.11, 5.31, 0.86273, 3, 104, -14.33, 29.83, 0.10445, 103, -5.28, 14.29, 0.41743, 102, 26.36, -10.22, 0.47812, 4, 105, -31.85, 11.58, 0.00774, 104, -3.18, 13.97, 0.45852, 103, 13.34, 8.91, 0.32857, 102, 26.48, -29.6, 0.20517, 4, 105, -17.7, 3.89, 0.33703, 104, 10.33, 5.22, 0.45888, 103, 29.36, 10.54, 0.19499, 102, 32.59, -44.5, 0.00911, 4, 106, -19.43, -23.29, 0.02497, 105, -0.7, 8.36, 0.64539, 104, 27.63, 8.39, 0.32672, 103, 40.87, 23.84, 0.00293, 3, 106, -16.39, -5.7, 0.3583, 105, 16.62, 12.68, 0.63765, 104, 45.23, 11.38, 0.00405, 2, 106, -0.05, 5.99, 0.69164, 105, 33.92, 2.47, 0.30836, 1, 103, 14.43, -1.04, 1, 1, 104, 9.37, -3.8, 1, 1, 105, 2.01, 1.05, 1, 2, 106, -9.06, -9.6, 0.0318, 105, 16.02, 4.41, 0.9682, 2, 106, 3.88, -0.05, 0.99493, 105, 29.99, -3.57, 0.00507, 1, 106, 19.37, 0.04, 1, 3, 104, -21.38, 17.35, 0.0071, 103, -2.96, 0.15, 0.64397, 102, 13.46, -16.45, 0.34893], "hull": 23, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 0, 2, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 2, 4, 4, 6, 20, 58, 58, 34], "width": 138, "height": 112}}, "EYE_R4": {"EYE_R": {"type": "mesh", "uvs": [0.90755, 0.06732, 1, 0.14302, 1, 0.18124, 1, 0.21451, 0.90869, 0.18367, 0.82109, 0.16685, 0.74145, 0.20049, 0.71073, 0.30983, 0.65271, 0.5201, 0.49912, 0.62103, 0.38812, 0.66741, 0.36423, 0.81599, 0.27208, 0.90991, 0.10597, 0.87207, 0.05023, 0.75151, 0.06388, 0.60573, 0.19358, 0.52583, 0.30507, 0.46695, 0.44549, 0.46209, 0.55243, 0.40462, 0.60249, 0.26023, 0.65482, 0.11444, 0.7902, 0.04856, 0.47499, 0.54374, 0.60307, 0.45624, 0.65633, 0.28124, 0.69944, 0.16406, 0.80723, 0.10937, 0.91629, 0.14218, 0.35228, 0.58091], "triangles": [16, 14, 15, 13, 14, 16, 16, 17, 29, 12, 13, 16, 16, 11, 12, 10, 11, 16, 10, 16, 29, 23, 18, 19, 29, 18, 23, 9, 23, 24, 10, 23, 9, 29, 17, 18, 10, 29, 23, 24, 25, 7, 24, 19, 25, 8, 24, 7, 23, 19, 24, 9, 24, 8, 26, 21, 22, 6, 26, 27, 20, 21, 26, 25, 20, 26, 25, 26, 6, 7, 25, 6, 25, 19, 20, 27, 22, 0, 1, 28, 0, 27, 0, 28, 27, 26, 22, 5, 27, 28, 28, 1, 2, 4, 5, 28, 4, 28, 2, 5, 6, 27, 4, 2, 3], "vertices": [1, 106, 16.17, 7.88, 1, 1, 106, 30.6, 2.75, 1, 1, 106, 31.64, -1.4, 1, 2, 106, 32.55, -5.02, 0.98259, 105, 37.02, -31.8, 0.01741, 2, 106, 19.48, -4.72, 0.71334, 105, 32.01, -19.73, 0.28666, 3, 106, 7.3, -5.83, 0.38001, 105, 26.08, -9.03, 0.61896, 104, 53.01, -10.99, 0.00103, 3, 106, -2.45, -12.15, 0.06409, 105, 16.36, -2.67, 0.68152, 104, 43.8, -3.91, 0.25439, 2, 105, 4.09, -6.84, 0.41228, 104, 31.25, -7.13, 0.58772, 3, 105, -19.42, -14.98, 0.07997, 104, 7.19, -13.46, 0.58669, 103, 38.6, -6, 0.33333, 3, 104, -13.94, -2.03, 0.33333, 103, 14.97, -10.3, 0.61278, 102, 8.52, -36.61, 0.05388, 3, 104, -26.73, 7.88, 0.00256, 103, -1.21, -10.58, 0.69563, 102, 3.66, -21.18, 0.30181, 2, 103, -9.42, -25.43, 0.3837, 102, -12.9, -17.52, 0.6163, 2, 103, -24.74, -31.57, 0.10425, 102, -23.14, -4.57, 0.89575, 2, 103, -45.27, -20.55, 0.02047, 102, -18.4, 18.25, 0.97953, 1, 102, -4.73, 25.64, 1, 1, 102, 11.55, 23.4, 1, 3, 104, -28.33, 39.02, 0.00369, 103, -21.94, 12.7, 0.13358, 102, 20.11, 5.31, 0.86273, 3, 104, -14.33, 29.83, 0.10445, 103, -5.28, 14.29, 0.41743, 102, 26.36, -10.22, 0.47812, 4, 105, -31.85, 11.58, 0.00774, 104, -3.18, 13.97, 0.45852, 103, 13.34, 8.91, 0.32857, 102, 26.48, -29.6, 0.20517, 4, 105, -17.7, 3.89, 0.33703, 104, 10.33, 5.22, 0.45888, 103, 29.36, 10.54, 0.19499, 102, 32.59, -44.5, 0.00911, 4, 106, -19.43, -23.29, 0.02497, 105, -0.7, 8.36, 0.64539, 104, 27.63, 8.39, 0.32672, 103, 40.87, 23.84, 0.00293, 3, 106, -16.39, -5.7, 0.3583, 105, 16.62, 12.68, 0.63765, 104, 45.23, 11.38, 0.00405, 2, 106, -0.05, 5.99, 0.69164, 105, 33.92, 2.47, 0.30836, 1, 103, 14.43, -1.04, 1, 1, 104, 9.37, -3.8, 1, 1, 105, 2.01, 1.05, 1, 2, 106, -9.06, -9.6, 0.0318, 105, 16.02, 4.41, 0.9682, 2, 106, 3.88, -0.05, 0.99493, 105, 29.99, -3.57, 0.00507, 1, 106, 19.37, 0.04, 1, 3, 104, -21.38, 17.35, 0.0071, 103, -2.96, 0.15, 0.64397, 102, 13.46, -16.45, 0.34893], "hull": 23, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 0, 2, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 2, 4, 4, 6, 20, 58, 58, 34], "width": 138, "height": 112}}, "EYE_R5": {"EYE_R": {"type": "mesh", "uvs": [0.90755, 0.06732, 1, 0.14302, 1, 0.18124, 1, 0.21451, 0.90869, 0.18367, 0.82109, 0.16685, 0.74145, 0.20049, 0.71073, 0.30983, 0.65271, 0.5201, 0.49912, 0.62103, 0.38812, 0.66741, 0.36423, 0.81599, 0.27208, 0.90991, 0.10597, 0.87207, 0.05023, 0.75151, 0.06388, 0.60573, 0.19358, 0.52583, 0.30507, 0.46695, 0.44549, 0.46209, 0.55243, 0.40462, 0.60249, 0.26023, 0.65482, 0.11444, 0.7902, 0.04856, 0.47499, 0.54374, 0.60307, 0.45624, 0.65633, 0.28124, 0.69944, 0.16406, 0.80723, 0.10937, 0.91629, 0.14218, 0.35228, 0.58091], "triangles": [16, 14, 15, 13, 14, 16, 16, 17, 29, 12, 13, 16, 16, 11, 12, 10, 11, 16, 10, 16, 29, 23, 18, 19, 29, 18, 23, 9, 23, 24, 10, 23, 9, 29, 17, 18, 10, 29, 23, 24, 25, 7, 24, 19, 25, 8, 24, 7, 23, 19, 24, 9, 24, 8, 26, 21, 22, 6, 26, 27, 20, 21, 26, 25, 20, 26, 25, 26, 6, 7, 25, 6, 25, 19, 20, 27, 22, 0, 1, 28, 0, 27, 0, 28, 27, 26, 22, 5, 27, 28, 28, 1, 2, 4, 5, 28, 4, 28, 2, 5, 6, 27, 4, 2, 3], "vertices": [1, 106, 16.17, 7.88, 1, 1, 106, 30.6, 2.75, 1, 1, 106, 31.64, -1.4, 1, 2, 106, 32.55, -5.02, 0.98259, 105, 37.02, -31.8, 0.01741, 2, 106, 19.48, -4.72, 0.71334, 105, 32.01, -19.73, 0.28666, 3, 106, 7.3, -5.83, 0.38001, 105, 26.08, -9.03, 0.61896, 104, 53.01, -10.99, 0.00103, 3, 106, -2.45, -12.15, 0.06409, 105, 16.36, -2.67, 0.68152, 104, 43.8, -3.91, 0.25439, 2, 105, 4.09, -6.84, 0.41228, 104, 31.25, -7.13, 0.58772, 3, 105, -19.42, -14.98, 0.07997, 104, 7.19, -13.46, 0.58669, 103, 38.6, -6, 0.33333, 3, 104, -13.94, -2.03, 0.33333, 103, 14.97, -10.3, 0.61278, 102, 8.52, -36.61, 0.05388, 3, 104, -26.73, 7.88, 0.00256, 103, -1.21, -10.58, 0.69563, 102, 3.66, -21.18, 0.30181, 2, 103, -9.42, -25.43, 0.3837, 102, -12.9, -17.52, 0.6163, 2, 103, -24.74, -31.57, 0.10425, 102, -23.14, -4.57, 0.89575, 2, 103, -45.27, -20.55, 0.02047, 102, -18.4, 18.25, 0.97953, 1, 102, -4.73, 25.64, 1, 1, 102, 11.55, 23.4, 1, 3, 104, -28.33, 39.02, 0.00369, 103, -21.94, 12.7, 0.13358, 102, 20.11, 5.31, 0.86273, 3, 104, -14.33, 29.83, 0.10445, 103, -5.28, 14.29, 0.41743, 102, 26.36, -10.22, 0.47812, 4, 105, -31.85, 11.58, 0.00774, 104, -3.18, 13.97, 0.45852, 103, 13.34, 8.91, 0.32857, 102, 26.48, -29.6, 0.20517, 4, 105, -17.7, 3.89, 0.33703, 104, 10.33, 5.22, 0.45888, 103, 29.36, 10.54, 0.19499, 102, 32.59, -44.5, 0.00911, 4, 106, -19.43, -23.29, 0.02497, 105, -0.7, 8.36, 0.64539, 104, 27.63, 8.39, 0.32672, 103, 40.87, 23.84, 0.00293, 3, 106, -16.39, -5.7, 0.3583, 105, 16.62, 12.68, 0.63765, 104, 45.23, 11.38, 0.00405, 2, 106, -0.05, 5.99, 0.69164, 105, 33.92, 2.47, 0.30836, 1, 103, 14.43, -1.04, 1, 1, 104, 9.37, -3.8, 1, 1, 105, 2.01, 1.05, 1, 2, 106, -9.06, -9.6, 0.0318, 105, 16.02, 4.41, 0.9682, 2, 106, 3.88, -0.05, 0.99493, 105, 29.99, -3.57, 0.00507, 1, 106, 19.37, 0.04, 1, 3, 104, -21.38, 17.35, 0.0071, 103, -2.96, 0.15, 0.64397, 102, 13.46, -16.45, 0.34893], "hull": 23, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 0, 2, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 2, 4, 4, 6, 20, 58, 58, 34], "width": 138, "height": 112}}, "EYE_R6": {"EYE_R": {"type": "mesh", "uvs": [0.90755, 0.06732, 1, 0.14302, 1, 0.18124, 1, 0.21451, 0.90869, 0.18367, 0.82109, 0.16685, 0.74145, 0.20049, 0.71073, 0.30983, 0.65271, 0.5201, 0.49912, 0.62103, 0.38812, 0.66741, 0.36423, 0.81599, 0.27208, 0.90991, 0.10597, 0.87207, 0.05023, 0.75151, 0.06388, 0.60573, 0.19358, 0.52583, 0.30507, 0.46695, 0.44549, 0.46209, 0.55243, 0.40462, 0.60249, 0.26023, 0.65482, 0.11444, 0.7902, 0.04856, 0.47499, 0.54374, 0.60307, 0.45624, 0.65633, 0.28124, 0.69944, 0.16406, 0.80723, 0.10937, 0.91629, 0.14218, 0.35228, 0.58091], "triangles": [16, 14, 15, 13, 14, 16, 16, 17, 29, 12, 13, 16, 16, 11, 12, 10, 11, 16, 10, 16, 29, 23, 18, 19, 29, 18, 23, 9, 23, 24, 10, 23, 9, 29, 17, 18, 10, 29, 23, 24, 25, 7, 24, 19, 25, 8, 24, 7, 23, 19, 24, 9, 24, 8, 26, 21, 22, 6, 26, 27, 20, 21, 26, 25, 20, 26, 25, 26, 6, 7, 25, 6, 25, 19, 20, 27, 22, 0, 1, 28, 0, 27, 0, 28, 27, 26, 22, 5, 27, 28, 28, 1, 2, 4, 5, 28, 4, 28, 2, 5, 6, 27, 4, 2, 3], "vertices": [1, 106, 16.17, 7.88, 1, 1, 106, 30.6, 2.75, 1, 1, 106, 31.64, -1.4, 1, 2, 106, 32.55, -5.02, 0.98259, 105, 37.02, -31.8, 0.01741, 2, 106, 19.48, -4.72, 0.71334, 105, 32.01, -19.73, 0.28666, 3, 106, 7.3, -5.83, 0.38001, 105, 26.08, -9.03, 0.61896, 104, 53.01, -10.99, 0.00103, 3, 106, -2.45, -12.15, 0.06409, 105, 16.36, -2.67, 0.68152, 104, 43.8, -3.91, 0.25439, 2, 105, 4.09, -6.84, 0.41228, 104, 31.25, -7.13, 0.58772, 3, 105, -19.42, -14.98, 0.07997, 104, 7.19, -13.46, 0.58669, 103, 38.6, -6, 0.33333, 3, 104, -13.94, -2.03, 0.33333, 103, 14.97, -10.3, 0.61278, 102, 8.52, -36.61, 0.05388, 3, 104, -26.73, 7.88, 0.00256, 103, -1.21, -10.58, 0.69563, 102, 3.66, -21.18, 0.30181, 2, 103, -9.42, -25.43, 0.3837, 102, -12.9, -17.52, 0.6163, 2, 103, -24.74, -31.57, 0.10425, 102, -23.14, -4.57, 0.89575, 2, 103, -45.27, -20.55, 0.02047, 102, -18.4, 18.25, 0.97953, 1, 102, -4.73, 25.64, 1, 1, 102, 11.55, 23.4, 1, 3, 104, -28.33, 39.02, 0.00369, 103, -21.94, 12.7, 0.13358, 102, 20.11, 5.31, 0.86273, 3, 104, -14.33, 29.83, 0.10445, 103, -5.28, 14.29, 0.41743, 102, 26.36, -10.22, 0.47812, 4, 105, -31.85, 11.58, 0.00774, 104, -3.18, 13.97, 0.45852, 103, 13.34, 8.91, 0.32857, 102, 26.48, -29.6, 0.20517, 4, 105, -17.7, 3.89, 0.33703, 104, 10.33, 5.22, 0.45888, 103, 29.36, 10.54, 0.19499, 102, 32.59, -44.5, 0.00911, 4, 106, -19.43, -23.29, 0.02497, 105, -0.7, 8.36, 0.64539, 104, 27.63, 8.39, 0.32672, 103, 40.87, 23.84, 0.00293, 3, 106, -16.39, -5.7, 0.3583, 105, 16.62, 12.68, 0.63765, 104, 45.23, 11.38, 0.00405, 2, 106, -0.05, 5.99, 0.69164, 105, 33.92, 2.47, 0.30836, 1, 103, 14.43, -1.04, 1, 1, 104, 9.37, -3.8, 1, 1, 105, 2.01, 1.05, 1, 2, 106, -9.06, -9.6, 0.0318, 105, 16.02, 4.41, 0.9682, 2, 106, 3.88, -0.05, 0.99493, 105, 29.99, -3.57, 0.00507, 1, 106, 19.37, 0.04, 1, 3, 104, -21.38, 17.35, 0.0071, 103, -2.96, 0.15, 0.64397, 102, 13.46, -16.45, 0.34893], "hull": 23, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 0, 2, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 2, 4, 4, 6, 20, 58, 58, 34], "width": 138, "height": 112}}, "HAIR_1": {"HAIR_1": {"type": "mesh", "uvs": [0.85053, 0.06087, 0.9891, 1e-05, 1, 0.12095, 0.96713, 0.41288, 0.79597, 0.64417, 0.63154, 0.86637, 0.47622, 0.92604, 0.28369, 1, 0.1556, 1, 0.09544, 0.91061, 0, 0.76878, 0.15558, 0.66002, 0.36157, 0.52608, 0.51794, 0.4244, 0.51778, 0.35583, 0.3954, 0.2283, 0.54202, 0.18121, 0.71959, 0.12419, 0.24867, 0.82447, 0.47734, 0.6833, 0.65343, 0.52468, 0.81515, 0.28278], "triangles": [6, 19, 5, 5, 20, 4, 5, 19, 20, 19, 13, 20, 19, 12, 13, 4, 20, 21, 8, 18, 7, 7, 18, 6, 8, 9, 18, 6, 18, 19, 18, 9, 11, 9, 10, 11, 18, 12, 19, 18, 11, 12, 3, 4, 21, 20, 13, 21, 21, 16, 17, 14, 16, 21, 21, 13, 14, 3, 21, 2, 14, 15, 16, 21, 0, 2, 21, 17, 0, 0, 1, 2], "vertices": [1, 28, 3.78, -24.04, 1, 2, 28, -21.3, -10.22, 0.98822, 29, -97.78, -26.04, 0.01178, 2, 28, -6.38, 4.77, 0.86679, 29, -85.36, -8.92, 0.13321, 3, 28, 36.86, 32.16, 0.63426, 29, -46.9, 24.86, 0.34736, 30, -112.58, -4.57, 0.01839, 3, 28, 88.81, 32.35, 0.31416, 29, 4.4, 33.1, 0.56244, 30, -65.19, 16.73, 0.12339, 3, 28, 138.73, 32.54, 0.10226, 29, 53.68, 41.02, 0.55824, 30, -19.66, 37.19, 0.33951, 3, 28, 165.69, 16.1, 0.00146, 29, 82.87, 28.97, 0.36858, 30, 11.65, 33.14, 0.62996, 2, 29, 119.05, 14.03, 0.14171, 30, 50.47, 28.12, 0.85829, 2, 29, 137.43, -2.31, 0.02449, 30, 72.47, 17.12, 0.97551, 2, 29, 135.73, -21.61, 0.00138, 30, 75.84, -1.96, 0.99862, 3, 28, 202.65, -71.89, 0.00572, 29, 133.03, -52.23, 0.0661, 30, 81.2, -32.22, 0.92818, 3, 28, 169.06, -60.84, 0.08409, 29, 98.13, -46.52, 0.16882, 30, 46.02, -35.79, 0.74709, 3, 28, 125.93, -45.11, 0.26719, 29, 53.09, -37.67, 0.27517, 30, 0.22, -38.95, 0.45765, 3, 28, 93.2, -33.16, 0.54995, 29, 18.89, -30.94, 0.25482, 30, -34.55, -41.35, 0.19523, 3, 28, 84, -40.77, 0.79853, 29, 10.99, -39.88, 0.15848, 30, -39.85, -52.03, 0.04298, 3, 28, 81.79, -73.01, 0.94877, 29, 13.81, -72.08, 0.05076, 30, -28.76, -82.39, 0.00047, 2, 28, 57.58, -56.48, 0.99362, 29, -12.67, -59.5, 0.00638, 1, 28, 28.26, -36.45, 1, 2, 29, 103.78, -13.26, 2e-05, 30, 42.83, -2.2, 0.99998, 3, 28, 132.94, -10.56, 0.00145, 29, 54.65, -2.45, 0.97468, 30, -7.43, -4.54, 0.02387, 2, 28, 90.14, -1.99, 0.03164, 29, 11.04, -0.62, 0.96836, 1, 28, 37.91, -4.75, 1], "hull": 18, "edges": [4, 6, 14, 16, 26, 28, 28, 30, 6, 8, 8, 10, 10, 12, 12, 14, 24, 26, 22, 24, 16, 18, 18, 20, 20, 22, 30, 32, 32, 34, 2, 0, 0, 34, 2, 4], "width": 192, "height": 174}}, "HAIR_2": {"HAIR_2": {"type": "mesh", "uvs": [0.69793, 0.23301, 0.90406, 0.39874, 1, 0.78432, 0.82618, 0.88881, 0.64121, 1, 0.37756, 0.80586, 0.17807, 0.65897, 0, 0.52785, 0.00722, 0.35108, 0.09642, 0.17383, 0.24342, 0.09184, 0.4081, 0, 0.43489, 0.58607, 0.32157, 0.3041, 0.73061, 0.73401, 0.69745, 0.41376, 0.22155, 0.49694], "triangles": [16, 13, 12, 13, 16, 9, 4, 14, 3, 4, 5, 14, 3, 14, 2, 5, 6, 12, 5, 12, 14, 12, 6, 16, 14, 1, 2, 6, 7, 16, 7, 8, 16, 8, 9, 16, 13, 9, 10, 13, 11, 0, 13, 10, 11, 12, 15, 14, 14, 15, 1, 12, 13, 15, 15, 0, 1, 15, 13, 0], "vertices": [4, 40, 101.96, 0.59, 0.11672, 41, 54.93, -26.62, 0.27781, 42, 2.85, -28.1, 0.45732, 7, 401.1, -9.13, 0.14815, 4, 40, 79.85, -42.76, 0.12176, 41, 15.85, -55.61, 0.2387, 42, -42.02, -46.95, 0.26917, 7, 379.27, -52.62, 0.37037, 4, 40, 21.05, -67.63, 0.09271, 41, -47.88, -51.74, 0.1541, 42, -102.99, -28.01, 0.12356, 7, 320.64, -77.88, 0.62963, 4, 40, 1.29, -34.79, 0.04357, 41, -50.98, -13.53, 0.06499, 42, -96.89, 9.84, 0.03959, 7, 300.66, -45.17, 0.85185, 4, 40, -19.74, 0.15, 0.01291, 41, -54.27, 27.12, 0.01665, 42, -90.41, 50.1, 0.00747, 7, 279.4, -10.37, 0.96296, 4, 40, 5.73, 55.33, 0.00082, 41, -6.91, 65.21, 0.00509, 42, -35.34, 75.81, 0.03113, 7, 304.51, 44.98, 0.96296, 4, 40, 25.01, 97.08, 0.00292, 41, 28.92, 94.03, 0.01938, 42, 6.33, 95.26, 0.12585, 7, 323.51, 86.85, 0.85185, 4, 40, 42.22, 134.35, 0.00648, 41, 60.91, 119.75, 0.04756, 42, 43.52, 112.63, 0.31633, 7, 340.48, 124.23, 0.62963, 4, 40, 70.16, 135.61, 0.0098, 41, 86.49, 108.46, 0.08345, 42, 65.68, 95.56, 0.53638, 7, 368.4, 125.68, 0.37037, 4, 40, 99.74, 120.63, 0.01628, 41, 106.33, 81.89, 0.12973, 42, 78.62, 65.03, 0.70584, 7, 398.09, 110.9, 0.14815, 4, 40, 115.45, 92.77, 0.03833, 41, 108.01, 49.94, 0.189, 42, 72.63, 33.6, 0.73563, 7, 413.98, 83.13, 0.03704, 4, 40, 133.05, 61.54, 0.07617, 41, 109.89, 14.15, 0.24928, 42, 65.93, -1.61, 0.63752, 7, 431.79, 52.03, 0.03704, 4, 40, 41.4, 47.32, 0.15112, 41, 21.47, 42.17, 0.31043, 42, -13.26, 46.68, 0.16245, 7, 340.23, 37.2, 0.376, 3, 40, 83.57, 74.06, 0.01784, 41, 71.13, 47.36, 0.13377, 42, 36.2, 39.89, 0.8484, 3, 40, 23.8, -13.51, 0.6815, 41, -21.34, -4.48, 0.0145, 7, 323.03, -23.74, 0.304, 3, 40, 73.53, -2.06, 0.00328, 41, 28.28, -16.35, 0.99632, 42, -20.59, -11.78, 0.00039, 4, 40, 51.32, 90.93, 0.00847, 41, 49.76, 76.82, 0.06121, 42, 22.47, 73.59, 0.3857, 7, 349.87, 80.88, 0.54462], "hull": 12, "edges": [2, 4, 12, 14, 8, 10, 10, 12, 4, 6, 6, 8, 18, 20, 20, 22, 2, 0, 0, 22, 16, 18, 14, 16], "width": 199, "height": 158}}, "HAIR_3": {"HAIR_3": {"type": "mesh", "uvs": [0.93363, 0, 1, 0.34979, 1, 0.60792, 0.82021, 1, 0.53666, 1, 0.32227, 0.95162, 0.14321, 0.91121, 0, 1, 0, 0.89224, 0.05312, 0.66237, 0.24664, 0.49683, 0.52292, 0.35378, 0.71255, 0.17466, 0.82744, 0, 0.13818, 0.75292, 0.36856, 0.67461, 0.66198, 0.59629, 0.85356, 0.31864], "triangles": [5, 15, 4, 4, 15, 16, 15, 11, 16, 15, 10, 11, 7, 8, 6, 15, 5, 14, 8, 14, 6, 5, 6, 14, 8, 9, 14, 14, 10, 15, 14, 9, 10, 4, 16, 3, 3, 16, 2, 16, 17, 2, 17, 1, 2, 16, 12, 17, 16, 11, 12, 17, 0, 1, 12, 13, 17, 17, 13, 0], "vertices": [2, 25, -13.77, -30.14, 0.99411, 26, -61.87, -48.09, 0.00589, 2, 25, -2.12, 7.68, 0.95943, 26, -60.57, -8.53, 0.04057, 3, 25, 13.07, 31.36, 0.81579, 26, -52.13, 18.31, 0.17729, 27, -101.26, 27.48, 0.00693, 3, 25, 60.36, 51.8, 0.56135, 26, -11.88, 50.45, 0.33376, 27, -58.29, 55.9, 0.10489, 3, 25, 98.54, 27.31, 0.27629, 26, 31.4, 36.85, 0.40975, 27, -16.4, 38.47, 0.31396, 3, 25, 124.57, 4.34, 0.08661, 26, 62.55, 21.54, 0.2931, 27, 13.25, 20.43, 0.62029, 3, 25, 146.3, -14.83, 0.00787, 26, 88.56, 8.74, 0.13647, 27, 38.01, 5.36, 0.85566, 2, 26, 113.32, 11.11, 0.02008, 27, 62.88, 5.5, 0.97992, 2, 26, 109.8, -0.1, 0.03025, 27, 58.37, -5.35, 0.96975, 3, 25, 143.79, -45.44, 0.02617, 26, 94.18, -21.45, 0.14526, 27, 40.9, -25.22, 0.82857, 3, 25, 107.99, -43.91, 0.15773, 26, 59.23, -29.38, 0.266, 27, 5.39, -29.99, 0.57627, 3, 25, 62.36, -33.17, 0.40039, 26, 12.38, -31, 0.32625, 27, -41.42, -27.41, 0.27336, 3, 25, 26.28, -33.22, 0.70182, 26, -22.42, -40.53, 0.21697, 27, -76.93, -33.78, 0.08121, 3, 25, 0.53, -39.32, 0.9036, 26, -45.66, -53.18, 0.09623, 27, -101.22, -44.3, 0.00017, 1, 27, 32.13, -10.88, 1, 2, 26, 46.43, -5.05, 0.83428, 27, -5.18, -4.6, 0.16572, 2, 25, 57.91, 1.09, 0.76112, 26, -0.92, 0.88, 0.23888, 2, 25, 15.77, -7.83, 0.99981, 26, -39.23, -18.8, 0.00019], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 24, 26, 18, 20, 20, 22, 22, 24, 8, 10, 10, 12, 12, 14], "width": 160, "height": 109}}, "HAIR_4": {"HAIR_4": {"type": "mesh", "uvs": [0.50481, 0, 0.73825, 0.15505, 1, 0.32891, 1, 0.38583, 0.94301, 0.71011, 0.82728, 0.96085, 0.60371, 1, 0.25107, 0.81592, 0.03912, 0.57604, 0, 0.32058, 0, 0.22203, 0.09098, 0.06514, 0.35957, 0, 0.44152, 0.21246, 0.44152, 0.51023, 0.50292, 0.75809], "triangles": [14, 9, 13, 5, 15, 4, 7, 15, 6, 9, 14, 8, 4, 14, 3, 15, 14, 4, 7, 8, 14, 7, 14, 15, 14, 13, 1, 13, 9, 10, 11, 13, 10, 6, 15, 5, 13, 0, 1, 1, 2, 3, 14, 1, 3, 13, 12, 0, 11, 12, 13], "vertices": [1, 17, -9.21, -7.98, 1, 1, 17, -71, 38.27, 1, 1, 17, -140.27, 90.14, 1, 1, 17, -141.04, 108.06, 1, 4, 21, 17.35, 99.08, 0.36264, 20, 87.94, 99.08, 0.59893, 19, 158.53, 99.08, 0.03839, 18, 229.13, 99.08, 4e-05, 2, 21, 87.75, 52.6, 0.90542, 20, 158.34, 52.6, 0.09458, 2, 21, 87.02, -5.94, 0.99994, 20, 157.61, -5.94, 6e-05, 3, 21, 10.38, -81.03, 0.39884, 20, 80.98, -81.03, 0.56078, 19, 151.57, -81.03, 0.04038, 4, 21, -75.37, -117.1, 0.01549, 20, -4.77, -117.1, 0.55757, 19, 65.82, -117.1, 0.37815, 18, 136.41, -117.1, 0.04879, 3, 20, -85.45, -108.94, 0.16878, 19, -14.86, -108.94, 0.42428, 18, 55.73, -108.94, 0.40695, 3, 20, -115.72, -102.02, 0.08692, 19, -45.12, -102.02, 0.32235, 18, 25.47, -102.02, 0.59073, 1, 17, 95.76, 17.02, 1, 1, 17, 27.93, -6.4, 1, 2, 19, -22.88, 8.84, 0.01629, 18, 47.71, 8.84, 0.98371, 2, 20, -2.04, -12.06, 0.54555, 19, 68.56, -12.06, 0.45445, 3, 21, 6.98, -14.12, 0.70086, 20, 77.58, -14.12, 0.29876, 19, 148.17, -14.12, 0.00038], "hull": 13, "edges": [0, 24, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 2, 2, 4, 0, 26, 26, 28, 28, 30, 10, 12, 30, 12], "width": 256, "height": 315}}, "HAIR_5": {"HAIR_5": {"type": "mesh", "uvs": [0.68515, 0, 1, 0.17368, 0.94783, 0.35505, 0.84493, 0.71279, 0.76232, 1, 0.39162, 0.93546, 0.00782, 0.86864, 0.00467, 0.46683, 0.15771, 0.17201, 0.57059, 0, 0.43783, 0.63852, 0.62525, 0.26403], "triangles": [10, 11, 2, 7, 8, 11, 2, 11, 1, 8, 9, 11, 11, 0, 1, 11, 9, 0, 4, 5, 3, 5, 10, 3, 5, 6, 10, 6, 7, 10, 3, 10, 2, 10, 7, 11], "vertices": [2, 23, -82.11, -10.65, 0.08974, 22, -17.93, -10.65, 0.91026, 3, 24, -120.91, 40.56, 0.04598, 23, -56.73, 40.56, 0.1406, 22, 7.44, 40.56, 0.81343, 3, 24, -80.28, 44.98, 0.19821, 23, -16.11, 44.98, 0.24483, 22, 48.07, 44.98, 0.55695, 3, 24, -0.15, 53.71, 0.46153, 23, 64.03, 53.71, 0.278, 22, 128.2, 53.71, 0.26047, 3, 24, 64.19, 60.72, 0.73851, 23, 128.36, 60.72, 0.18646, 22, 192.53, 60.72, 0.07502, 3, 24, 64.16, 9.02, 0.82035, 23, 128.34, 9.02, 0.17662, 22, 192.51, 9.02, 0.00303, 3, 24, 64.14, -44.5, 0.68003, 23, 128.32, -44.5, 0.25271, 22, 192.49, -44.5, 0.06726, 3, 24, -21.46, -69.6, 0.37445, 23, 42.72, -69.6, 0.38401, 22, 106.89, -69.6, 0.24154, 3, 24, -90.03, -68, 0.14038, 23, -25.85, -68, 0.33884, 22, 38.32, -68, 0.52078, 3, 24, -142.03, -25.41, 0.01738, 23, -77.86, -25.41, 0.19457, 22, -13.69, -25.41, 0.78805, 2, 24, -0.9, -3.27, 0.39706, 23, 63.28, -3.27, 0.60294, 2, 23, -23.56, -2.15, 0.00406, 22, 40.61, -2.15, 0.99594], "hull": 10, "edges": [0, 18, 0, 2, 12, 14, 14, 16, 16, 18, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12], "width": 134, "height": 222}}, "HAIR_6": {"HAIR_6": {"type": "mesh", "uvs": [0.38612, 0, 0.49138, 0, 0.7367, 0.15814, 1, 0.32787, 1, 0.44493, 0.91017, 0.74255, 0.83247, 1, 0.62477, 0.94944, 0.32898, 0.8276, 0, 0.69209, 0, 0.6311, 0.07467, 0.40953, 0.144, 0.20383, 0.1938, 0.05606, 0.48627, 0.67394, 0.60288, 0.42707, 0.55537, 0.25281], "triangles": [15, 11, 12, 12, 16, 15, 16, 12, 13, 16, 0, 1, 0, 16, 13, 15, 16, 3, 16, 2, 3, 16, 1, 2, 14, 15, 5, 5, 15, 4, 9, 10, 14, 10, 11, 14, 14, 11, 15, 15, 3, 4, 6, 7, 5, 8, 14, 7, 7, 14, 5, 8, 9, 14], "vertices": [2, 35, -81.87, 14.49, 0.00999, 34, -24.43, 14.49, 0.99001, 3, 35, -78.82, 26.31, 0.08227, 34, -21.38, 26.31, 0.91765, 36, -136.25, 26.31, 8e-05, 3, 35, -40.01, 45.68, 0.2378, 34, 17.43, 45.68, 0.75889, 36, -97.44, 45.68, 0.00332, 3, 35, 1.65, 66.47, 0.39263, 34, 59.09, 66.47, 0.50822, 36, -55.79, 66.47, 0.09915, 3, 35, 25.11, 60.41, 0.42132, 34, 82.55, 60.41, 0.27274, 36, -32.32, 60.41, 0.30593, 3, 35, 82.16, 34.91, 0.28432, 34, 139.59, 34.92, 0.09817, 36, 24.72, 34.92, 0.61751, 3, 35, 131.5, 12.86, 0.13911, 34, 188.94, 12.86, 0.0251, 36, 74.07, 12.86, 0.83579, 3, 35, 115.34, -7.85, 0.13574, 34, 172.78, -7.85, 0.00012, 36, 57.91, -7.85, 0.86414, 3, 35, 82.34, -34.76, 0.29922, 34, 139.78, -34.76, 0.00147, 36, 24.91, -34.76, 0.69932, 3, 35, 45.64, -64.7, 0.49807, 34, 103.08, -64.7, 0.05669, 36, -11.79, -64.7, 0.44523, 3, 35, 33.42, -61.54, 0.53976, 34, 90.85, -61.54, 0.22241, 36, -24.02, -61.54, 0.23783, 3, 35, -8.82, -41.69, 0.41346, 34, 48.61, -41.69, 0.49678, 36, -66.26, -41.69, 0.08976, 3, 35, -48.04, -23.25, 0.19577, 34, 9.4, -23.25, 0.7745, 36, -105.48, -23.25, 0.02973, 3, 35, -76.21, -10.01, 0.05608, 34, -18.78, -10.01, 0.942, 36, -133.65, -10.01, 0.00192, 2, 35, 56.11, -9.14, 0.52697, 36, -1.33, -9.14, 0.47303, 2, 35, 10.01, 16.73, 0.80946, 34, 67.45, 16.73, 0.19054, 2, 35, -26.29, 20.42, 0.04744, 34, 31.14, 20.42, 0.95256], "hull": 14, "edges": [0, 26, 0, 2, 6, 8, 12, 14, 18, 20, 8, 10, 10, 12, 14, 16, 16, 18, 2, 4, 4, 6, 20, 22, 22, 24, 24, 26], "width": 116, "height": 207}}, "XIABAI_HAIR_R": {"XIABAI_HAIR_R": {"type": "mesh", "uvs": [0.66469, 0.0505, 0.66928, 0.1674, 0.85472, 0.15779, 1, 0.12479, 0.95047, 0.25514, 0.79782, 0.3933, 0.70861, 0.53707, 0.57256, 0.75634, 0.40904, 0.87147, 0.22648, 1, 0.05971, 0.89952, 0, 0.79757, 0, 0.76714, 0.03841, 0.62711, 0.08438, 0.45957, 0.15912, 0.208, 0.19645, 0.05106, 0.41964, 0.02602, 0.64999, 0.00018, 0.37199, 0.76353, 0.51667, 0.50581, 0.60234, 0.27403, 0.21078, 0.68928, 0.30192, 0.47761, 0.39306, 0.21814, 0.5286, 0.13393], "triangles": [4, 2, 3, 5, 1, 2, 10, 11, 22, 11, 12, 22, 12, 13, 22, 13, 14, 22, 22, 14, 23, 14, 15, 23, 15, 16, 24, 24, 16, 17, 21, 25, 1, 24, 17, 25, 25, 0, 1, 25, 18, 0, 25, 17, 18, 5, 2, 4, 5, 6, 21, 6, 20, 21, 20, 24, 21, 21, 1, 5, 8, 19, 7, 19, 20, 7, 7, 20, 6, 24, 25, 21, 10, 22, 9, 9, 19, 8, 9, 22, 19, 22, 23, 19, 19, 23, 20, 23, 24, 20, 23, 15, 24], "vertices": [3, 86, 86.01, 33.6, 0.04755, 87, 40.79, 33.6, 0.79299, 88, -4.44, 33.6, 0.15945, 2, 87, 43.27, 11.28, 0.59415, 88, -1.96, 11.28, 0.40585, 2, 87, 77.72, 15.63, 0.28499, 88, 32.5, 15.63, 0.71501, 3, 87, 104.36, 23.91, 0.10848, 88, 59.13, 23.91, 0.87742, 91, 87.19, 127.13, 0.0141, 4, 87, 96.93, -1.72, 0.0769, 88, 51.71, -1.72, 0.83776, 91, 83.63, 100.68, 0.08531, 90, 115.91, 100.68, 3e-05, 4, 87, 70.38, -30.24, 0.13226, 88, 25.16, -30.24, 0.60006, 91, 61.57, 68.56, 0.26723, 90, 93.85, 68.56, 0.00045, 5, 87, 55.75, -58.98, 0.14454, 88, 10.52, -58.98, 0.32254, 91, 51.32, 37.97, 0.47297, 90, 83.61, 37.97, 0.05628, 89, 115.89, 37.97, 0.00367, 5, 87, 33.42, -102.82, 0.08918, 88, -11.8, -102.82, 0.11579, 91, 35.7, -8.67, 0.5747, 90, 67.98, -8.67, 0.18391, 89, 100.26, -8.67, 0.03642, 5, 87, 4.52, -127.08, 0.03382, 88, -40.7, -127.08, 0.02016, 91, 10.69, -36.92, 0.45669, 90, 42.97, -36.92, 0.35858, 89, 75.25, -36.92, 0.13075, 3, 91, -17.23, -68.47, 0.24876, 90, 15.05, -68.47, 0.44932, 89, 47.33, -68.47, 0.30191, 3, 91, -51.87, -56.46, 0.07758, 90, -19.59, -56.46, 0.41922, 89, 12.69, -56.46, 0.5032, 3, 91, -67.05, -39.8, 0.01372, 90, -34.77, -39.8, 0.2952, 89, -2.49, -39.8, 0.69109, 4, 86, -27.99, -112.64, 0.02745, 91, -68.33, -34.09, 0.00179, 90, -36.05, -34.09, 0.17867, 89, -3.77, -34.09, 0.79208, 4, 86, -22.77, -85.3, 0.16318, 91, -67.19, -6.29, 5e-05, 90, -34.91, -6.29, 0.08889, 89, -2.63, -6.29, 0.74788, 4, 86, -16.53, -52.6, 0.40883, 87, -61.76, -52.6, 0.00118, 90, -33.56, 26.98, 0.04424, 89, -1.28, 26.98, 0.54575, 4, 86, -6.09, -3.41, 0.64713, 87, -51.31, -3.41, 0.06592, 90, -30.48, 77.17, 0.01476, 89, 1.8, 77.17, 0.27218, 4, 86, -1.31, 27.15, 0.66889, 87, -46.54, 27.15, 0.24178, 90, -30.25, 108.1, 0.00699, 89, 2.03, 108.1, 0.08235, 4, 86, 39.97, 34.97, 0.47078, 87, -5.26, 34.97, 0.52638, 90, 9.43, 121.91, 0.00057, 89, 41.71, 121.91, 0.00227, 3, 86, 82.57, 43.04, 0.20503, 87, 37.35, 43.04, 0.7708, 88, -7.88, 43.04, 0.02417, 3, 91, -0.6, -18.21, 0.42064, 90, 31.68, -18.21, 0.57006, 89, 63.96, -18.21, 0.0093, 5, 86, 64.74, -55.6, 0.03766, 87, 19.51, -55.6, 0.30681, 88, -25.71, -55.6, 0.03263, 91, 14.99, 35.99, 0.50825, 90, 47.27, 35.99, 0.11466, 4, 87, 32.27, -10.05, 0.92708, 88, -12.96, -10.05, 0.03, 91, 20.89, 82.91, 0.03711, 90, 53.17, 82.91, 0.00581, 3, 91, -33.13, -10.89, 0.00401, 90, -0.85, -10.89, 0.54134, 89, 31.43, -10.89, 0.45465, 5, 86, 24.29, -53.11, 0.24714, 87, -20.93, -53.11, 0.09152, 91, -25.38, 32.49, 0.06417, 90, 6.9, 32.49, 0.41719, 89, 39.18, 32.49, 0.17998, 5, 86, 37.68, -2.18, 0.98289, 87, -7.54, -2.18, 0.01226, 91, -19.64, 84.83, 0.00148, 90, 12.64, 84.83, 0.00268, 89, 44.92, 84.83, 0.00069, 2, 86, 61.79, 15.78, 0.03416, 87, 16.57, 15.78, 0.96584], "hull": 19, "edges": [18, 20, 20, 22, 22, 24, 6, 8, 8, 10, 0, 36, 4, 6, 0, 2, 2, 4, 10, 12, 12, 14, 30, 32, 28, 30, 24, 26, 26, 28, 14, 16, 16, 18, 32, 34, 34, 36], "width": 187, "height": 192}}, "HEAD_TX_R_2_2": {"HEAD_TX_7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [284.64, 12.26, 165.23, -17.25, 144.84, 65.27, 264.25, 94.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 123, "height": 85}}, "EYE_L": {"EYE_L": {"type": "mesh", "uvs": [0.28024, 0.39001, 0.29947, 0.64784, 0.26202, 0.78001, 0.19925, 0.89419, 0.1233, 0.84167, 0.03541, 0.65396, 0.03564, 0.30685, 0.12191, 0.12396, 0.24104, 0.16105], "triangles": [3, 4, 2, 4, 5, 0, 0, 5, 6, 1, 2, 4, 7, 0, 6, 1, 4, 0, 8, 0, 7], "vertices": [4, 43, 20.07, -9.09, 0.83458, 44, -2.81, 20.5, 0.08652, 45, -12.64, 36.23, 0.05557, 46, -55.79, -0.14, 0.02333, 4, 43, 0.01, -12.64, 0.73302, 44, -1.59, 0.17, 0.13187, 45, -24.34, 19.55, 0.07356, 46, -53.81, -20.41, 0.06155, 4, 43, -10.4, -6.56, 0.82486, 44, -8.83, -9.47, 0.11202, 45, -36, 16.51, 0.04346, 46, -60.68, -30.32, 0.01966, 4, 43, -19.48, 3.77, 0.77239, 44, -20.14, -17.31, 0.15603, 45, -49.73, 17.41, 0.05708, 46, -71.68, -38.57, 0.0145, 4, 43, -15.6, 16.52, 0.91459, 44, -32.36, -11.98, 0.06627, 45, -55.98, 29.18, 0.01723, 46, -84.09, -33.71, 0.00192, 3, 43, -1.21, 31.45, 0.97311, 44, -45.52, 4.03, 0.02347, 45, -56.33, 49.91, 0.00342, 4, 43, 25.86, 31.87, 0.97682, 44, -42.82, 30.97, 0.01594, 45, -37.45, 69.32, 0.00672, 46, -96.17, 8.82, 0.00053, 4, 43, 40.37, 17.7, 0.92609, 44, -27.08, 43.75, 0.03899, 45, -17.18, 69.53, 0.02934, 46, -80.92, 22.18, 0.00558, 4, 43, 37.81, -2.24, 0.79317, 44, -7.56, 38.92, 0.0961, 45, -4.91, 53.61, 0.0833, 46, -61.24, 18.08, 0.02743], "hull": 9, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 0, 0, 2, 2, 4, 4, 6], "width": 167, "height": 78}}, "HAIR_BACK": {"HAIR_BACK": {"type": "mesh", "uvs": [0.98073, 0.47837, 0.99704, 0.94157, 0.85628, 0.99999, 0.63749, 0.99072, 0.36239, 0.83547, 0.03882, 0.69911, 0, 0.63788, 0, 0.55546, 0.31087, 0.1968, 0.57236, 0, 0.96407, 0.00562, 0.69012, 0.50351, 0.35097, 0.54062], "triangles": [7, 8, 12, 6, 7, 12, 5, 6, 12, 5, 12, 4, 11, 9, 10, 11, 10, 0, 8, 9, 11, 12, 8, 11, 4, 12, 11, 2, 11, 0, 3, 4, 11, 2, 3, 11, 0, 1, 2], "vertices": [3, 107, -0.25, 22.17, 0.87671, 108, -78.54, 16.75, 0.11477, 109, -135.71, 10.49, 0.00851, 3, 107, 15.88, 92.21, 0.92068, 108, -67.32, 87.75, 0.07908, 109, -127.79, 81.93, 0.00024, 3, 107, 47.06, 92.84, 0.76384, 108, -36.26, 90.54, 0.1903, 109, -96.89, 86.15, 0.04586, 3, 107, 91.32, 78.89, 0.51827, 108, 8.86, 79.7, 0.27915, 109, -51.31, 77.41, 0.20258, 3, 107, 140.93, 39.92, 0.25022, 108, 61.07, 44.27, 0.27984, 109, 2.47, 44.43, 0.46994, 3, 107, 201.23, 0.98, 0.07805, 108, 123.93, 9.62, 0.23982, 109, 66.87, 12.72, 0.68213, 3, 107, 206.59, -10.38, 0.09458, 108, 130.06, -1.35, 0.22364, 109, 73.5, 2.05, 0.68178, 3, 107, 203.12, -22.68, 0.10154, 108, 127.46, -13.85, 0.22658, 109, 71.48, -10.56, 0.67188, 3, 107, 124.62, -58.33, 0.30305, 108, 51.62, -54.88, 0.24204, 109, -2.38, -55.04, 0.45491, 3, 107, 62.99, -72.67, 0.59341, 108, -8.86, -73.46, 0.21286, 109, -61.94, -76.41, 0.19373, 3, 107, -16.71, -49.32, 0.83202, 108, -89.99, -55.71, 0.1262, 109, -143.8, -62.43, 0.04178, 3, 107, 60.11, 9.22, 0.65881, 108, -17.43, 8.03, 0.20927, 109, -74.26, 4.6, 0.13193, 3, 107, 130.88, -4.73, 0.32979, 108, 54.14, -0.97, 0.28048, 109, -2.36, -1.08, 0.38973], "hull": 11, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 18, 20, 2, 0, 0, 20, 0, 22, 22, 24, 24, 12, 6, 8, 8, 10, 14, 16, 16, 18], "width": 212, "height": 155}}, "HEAD_TX_L_3_2": {"HEAD_TX_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [142.98, -133.32, 242.86, -128.48, 249.77, -271.32, 149.89, -276.15], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 143}}, "EYELID": {"EYELID": {"type": "mesh", "uvs": [0.29042, 0.52612, 0.72237, 0.71106, 0.94173, 0.33656, 0.99604, 0.54306, 0.96419, 0.85106, 0.88623, 0.9685, 0.78977, 0.86156, 0.23492, 0.70812, 0.11203, 0.74313, 0.03823, 0.6146, 0, 0.39313, 0.0235, 0.15513, 0.07767, 0, 0.08296, 0.36862, 0.16356, 0.54869, 0.87037, 0.79505, 0.94569, 0.67256], "triangles": [12, 14, 13, 11, 12, 13, 10, 11, 13, 0, 14, 12, 9, 10, 13, 9, 13, 14, 16, 2, 3, 7, 14, 0, 2, 15, 1, 8, 9, 14, 8, 14, 7, 16, 15, 2, 4, 16, 3, 15, 16, 4, 6, 1, 15, 7, 0, 1, 6, 7, 1, 5, 15, 4, 6, 15, 5], "vertices": [225.6, 42.16, 220.38, -43.44, 252.22, -83.47, 238.07, -95.58, 214.77, -91.63, 204.61, -77.29, 210.61, -57.69, 211.12, 51.65, 206.16, 75.36, 214.19, 90.7, 229.75, 99.78, 247.74, 96.94, 260.21, 87.52, 233.17, 83.78, 221.48, 66.74, 217.07, -72.92, 227.55, -86.71], "hull": 13, "edges": [24, 0, 0, 14, 14, 16, 20, 22, 22, 24, 22, 26, 14, 28, 28, 26, 20, 18, 18, 16, 0, 2, 14, 12, 2, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 12, 30, 30, 32, 32, 6], "width": 196, "height": 74}}, "XIABAI_HAIR_L": {"XIABAI_HAIR_L": {"type": "mesh", "uvs": [0.25758, 0, 0.4337, 0.01498, 0.69133, 0.0369, 0.90149, 0.05478, 1, 0.06316, 1, 0.20131, 0.97064, 0.43762, 0.93391, 0.73321, 0.79913, 0.85839, 0.64665, 1, 0.52825, 1, 0.31961, 0.77484, 0.14816, 0.58982, 0.00855, 0.43915, 0.00498, 0.28695, 0.00091, 0.11336, 0.48707, 0.57708, 0.60945, 0.35996, 0.71842, 0.68104, 0.82075, 0.42965, 0.24869, 0.42726, 0.3497, 0.26002, 0.15109, 0.23316], "triangles": [3, 4, 5, 18, 19, 7, 7, 19, 6, 16, 17, 18, 18, 17, 19, 12, 20, 11, 11, 20, 16, 8, 9, 18, 11, 16, 10, 9, 10, 18, 10, 16, 18, 8, 18, 7, 16, 21, 17, 21, 1, 17, 6, 19, 5, 17, 2, 19, 19, 3, 5, 13, 22, 20, 13, 14, 22, 20, 22, 21, 14, 15, 22, 22, 0, 21, 22, 15, 0, 12, 13, 20, 20, 21, 16, 17, 1, 2, 21, 0, 1, 19, 2, 3], "vertices": [4, 76, 40.63, -88.55, 0.12581, 77, -38.63, -88.55, 0.12671, 78, -117.89, -88.55, 0.02747, 75, 311.98, -13.92, 0.72, 6, 76, 13.56, -29.08, 0.13092, 77, -65.7, -29.08, 0.10884, 78, -144.96, -29.08, 0.00365, 75, 246.7, -16.79, 0.72, 80, -66.89, -166.48, 0.00015, 79, 37.92, -166.48, 0.03645, 7, 81, -202.03, -75.82, 9e-05, 76, -26.04, 57.93, 0.11066, 77, -105.3, 57.93, 0.02305, 78, -184.56, 57.93, 0.00035, 75, 151.2, -20.97, 0.72, 80, -97.21, -75.82, 0.02876, 79, 7.6, -75.82, 0.11709, 4, 76, -58.34, 128.91, 0.03371, 75, 73.29, -24.39, 0.72, 80, -121.95, -1.86, 0.04024, 79, -17.13, -1.86, 0.20605, 4, 76, -73.48, 162.18, 0.00041, 75, 36.78, -25.99, 0.72, 80, -133.54, 32.8, 0.00024, 79, -28.72, 32.8, 0.27935, 3, 81, -196.59, 50.36, 0.00019, 80, -91.77, 50.36, 0.1408, 79, 13.05, 50.36, 0.85901, 3, 81, -120.93, 70.39, 0.07416, 80, -16.11, 70.39, 0.39793, 79, 88.71, 70.39, 0.5279, 3, 81, -26.28, 95.44, 0.32418, 80, 78.54, 95.44, 0.48039, 79, 183.35, 95.44, 0.19544, 3, 81, 30.9, 65.38, 0.65731, 80, 135.71, 65.38, 0.34046, 79, 240.53, 65.38, 0.00223, 3, 81, 95.58, 31.38, 0.91466, 78, 100.27, 195.55, 0.00202, 80, 200.4, 31.38, 0.08332, 4, 81, 112.56, -9.01, 0.63987, 77, 200.62, 157.15, 7e-05, 78, 121.36, 157.15, 0.16258, 80, 217.38, -9.01, 0.19748, 5, 81, 74.4, -108.79, 0.39767, 76, 252.32, 53.93, 0.00016, 77, 173.07, 53.93, 0.06837, 78, 93.81, 53.93, 0.47008, 80, 179.22, -108.79, 0.06372, 4, 81, 43.05, -190.79, 0.08883, 77, 150.42, -30.88, 0.19423, 78, 71.16, -30.88, 0.71686, 80, 147.86, -190.79, 7e-05, 4, 81, 17.51, -257.57, 8e-05, 76, 211.24, -99.96, 0.00218, 77, 131.98, -99.96, 0.32715, 78, 52.72, -99.96, 0.67059, 4, 81, -28, -278.13, 4e-05, 76, 168.12, -125.15, 0.01504, 77, 88.86, -125.15, 0.54822, 78, 9.61, -125.15, 0.4367, 4, 81, -79.9, -301.59, 2e-05, 76, 118.94, -153.89, 0.14694, 77, 39.68, -153.89, 0.61558, 78, -39.57, -153.89, 0.23746, 6, 81, -9.41, -76.82, 0.19929, 76, 165.64, 77.01, 0.0621, 77, 86.38, 77.01, 0.15693, 78, 7.12, 77.01, 0.30834, 80, 95.41, -76.82, 0.22119, 79, 200.22, -76.82, 0.05215, 6, 81, -92.61, -62.68, 0.08091, 76, 81.42, 82.4, 0.22115, 77, 2.16, 82.4, 0.15683, 78, -77.1, 82.4, 0.09358, 80, 12.21, -62.68, 0.2218, 79, 117.03, -62.68, 0.22573, 4, 81, -11.15, 15.31, 0.40144, 78, -4.21, 168.45, 0.00202, 80, 93.66, 15.31, 0.45412, 79, 198.48, 15.31, 0.14242, 4, 81, -101.84, 18.25, 0.07013, 76, 63.81, 161.93, 0.00147, 80, 2.98, 18.25, 0.45412, 79, 107.79, 18.25, 0.47428, 4, 81, -20.52, -177.17, 4e-05, 76, 165.04, -23.96, 0.0279, 77, 85.78, -23.96, 0.44077, 78, 6.52, -23.96, 0.53128, 3, 76, 98.97, -17.61, 0.33247, 77, 19.71, -17.61, 0.45424, 78, -59.55, -17.61, 0.21329, 4, 81, -65.22, -235.14, 2e-05, 76, 126.63, -86.27, 0.04417, 77, 47.37, -86.27, 0.69913, 78, -31.89, -86.27, 0.25668], "hull": 16, "edges": [0, 30, 8, 10, 18, 20, 20, 22, 22, 32, 32, 34, 34, 4, 20, 36, 36, 38, 4, 6, 6, 8, 38, 6, 22, 24, 24, 26, 24, 40, 40, 42, 0, 2, 2, 4, 42, 2, 26, 28, 28, 30, 14, 16, 16, 18, 10, 12, 12, 14], "width": 370, "height": 328}}, "HAND_L_2": {"HAND_L_2": {"type": "mesh", "uvs": [0.21048, 0, 0.59025, 0.06218, 0.86983, 0.10795, 0.98717, 0.12717, 0.98997, 0.33087, 0.99923, 0.67475, 0.82025, 0.87554, 0.70932, 0.99999, 0.42062, 0.94477, 0.07012, 0.87774, 0.04392, 0.7172, 0, 0.44811, 0, 0.25352, 0.07442, 0.10256, 0.12497, 0, 0.26802, 0.26941, 0.49848, 0.37137, 0.76441, 0.39807, 0.46397, 0.71286, 0.5353, 0.18956], "triangles": [6, 7, 18, 7, 8, 18, 8, 9, 18, 18, 17, 6, 18, 16, 17, 9, 10, 18, 6, 17, 5, 18, 10, 15, 10, 11, 15, 18, 15, 16, 17, 4, 5, 15, 12, 13, 15, 11, 12, 4, 2, 3, 4, 17, 2, 16, 19, 17, 17, 19, 2, 2, 19, 1, 16, 15, 19, 15, 13, 0, 13, 14, 0, 15, 0, 19, 19, 0, 1], "vertices": [2, 16, 47.93, -133.22, 0.352, 15, 466.85, -85.7, 0.648, 2, 16, 30.33, -24.43, 0.432, 15, 393.64, -3.32, 0.568, 2, 16, 17.37, 55.66, 0.432, 15, 339.75, 57.32, 0.568, 2, 16, 11.93, 89.27, 0.432, 15, 317.13, 82.77, 0.568, 1, 16, 62.53, 106.26, 1, 1, 16, 147.56, 136.17, 1, 1, 16, 213.29, 103.22, 1, 1, 16, 254.03, 82.79, 1, 1, 16, 265.42, -0.54, 1, 1, 16, 279.26, -101.71, 1, 1, 16, 241.47, -121.66, 1, 1, 16, 178.14, -155.09, 1, 1, 16, 129.57, -170.59, 1, 1, 16, 85.39, -162.26, 1, 1, 16, 55.39, -156.6, 1, 1, 16, 110.16, -96.04, 1, 1, 16, 115.51, -24.91, 1, 1, 16, 98.98, 49.93, 1, 1, 16, 203.75, -7.15, 1, 1, 16, 66.91, -29.31, 1], "hull": 15, "edges": [0, 28, 22, 24, 4, 6, 24, 26, 26, 28, 26, 30, 30, 32, 32, 34, 6, 8, 8, 10, 34, 8, 14, 16, 16, 18, 18, 20, 20, 22, 10, 12, 12, 14, 0, 2, 2, 4], "width": 287, "height": 262}}, "HAND_L_3": {"HAND_L_3": {"type": "mesh", "uvs": [0.97007, 0, 1, 0.08537, 1, 0.22286, 0.9736, 0.47227, 0.85023, 0.64269, 0.73593, 0.66221, 0.67874, 0.78289, 0.6422, 0.93004, 0.59155, 1, 0.5286, 0.98918, 0.32714, 0.94436, 0.04944, 0.88259, 1e-05, 0.86988, 0.09181, 0.55811, 0.17766, 0.26655, 0.46638, 0.07546, 0.83013, 0, 0.31691, 0.47138, 0.41809, 0.66362, 0.56106, 0.74781, 0.29075, 0.81987, 0.70365, 0.35344], "triangles": [6, 19, 5, 11, 12, 13, 20, 11, 13, 7, 19, 6, 10, 20, 18, 10, 18, 19, 9, 10, 19, 11, 20, 10, 7, 9, 19, 8, 9, 7, 16, 0, 1, 21, 15, 16, 17, 14, 15, 17, 15, 21, 16, 1, 2, 13, 14, 17, 16, 2, 21, 3, 4, 2, 4, 21, 2, 5, 21, 4, 18, 17, 21, 21, 19, 18, 5, 19, 21, 20, 13, 17, 20, 17, 18], "vertices": [1, 15, -6.84, -22.29, 1, 1, 15, 2.75, 11.64, 1, 1, 15, 35.98, 51.42, 1, 2, 15, 106, 115.45, 0.99878, 16, -152.45, 227.65, 0.00122, 2, 15, 192.83, 126.84, 0.95051, 16, -72.46, 191.98, 0.04949, 2, 15, 240, 97.55, 0.76987, 16, -47.55, 142.35, 0.23013, 2, 15, 291.22, 115.15, 0.29407, 16, 5.31, 130.58, 0.70593, 1, 16, 64.12, 130.67, 1, 1, 16, 96.66, 115.48, 1, 1, 16, 101.97, 85.39, 1, 1, 16, 115.32, -12.07, 1, 1, 16, 133.73, -146.4, 1, 1, 16, 136.39, -170.51, 1, 2, 15, 451.66, -132.07, 0.718, 16, 12.9, -164.02, 0.282, 2, 15, 350.09, -189.51, 0.99268, 16, -103.72, -159.91, 0.00732, 1, 15, 197.34, -155.77, 1, 1, 15, 44.82, -65.44, 1, 2, 15, 348.18, -87.3, 0.98091, 16, -51.93, -71.78, 0.01909, 1, 15, 357.32, -0.48, 1, 2, 15, 325.81, 68.38, 0.15225, 16, 10.35, 72.64, 0.84775, 1, 16, 75.92, -43, 1, 1, 15, 176.93, -2.17, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 28, 30, 30, 32, 16, 18, 24, 22, 28, 34, 34, 36, 36, 38, 38, 12, 14, 16, 12, 14, 12, 10, 6, 8, 10, 8, 24, 26, 26, 28, 18, 20, 20, 22], "width": 481, "height": 377}}, "HEAD_TX_R_2_1": {"HEAD_TX_7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [284.64, 12.26, 165.23, -17.25, 144.84, 65.27, 264.25, 94.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 123, "height": 85}}, "LEG_R_1": {"LEG_R_1": {"type": "mesh", "uvs": [0.74264, 0, 0.94294, 0.1366, 1, 0.17551, 1, 0.23114, 0.94047, 0.36204, 0.84952, 0.32562, 0.75024, 0.28585, 0.57393, 0.31125, 0.47992, 0.48639, 0.35085, 0.5255, 0.1713, 0.52995, 0.04525, 0.45519, 0, 0.28294, 0, 0.16579, 0.0706, 0.0143, 0.36597, 0.00681, 0.63481, 0, 0.69715, 0.20524, 0.83097, 0.16415, 0.87043, 0.23262, 0.27853, 0.395, 0.3523, 0.18372], "triangles": [5, 19, 4, 5, 6, 19, 10, 20, 9, 10, 11, 20, 9, 20, 8, 7, 8, 21, 11, 12, 20, 8, 20, 21, 20, 12, 21, 4, 19, 3, 7, 21, 16, 7, 17, 6, 7, 16, 17, 16, 21, 15, 6, 18, 19, 6, 17, 18, 21, 13, 14, 21, 12, 13, 19, 1, 3, 19, 18, 1, 1, 2, 3, 17, 0, 18, 17, 16, 0, 21, 14, 15, 18, 0, 1], "vertices": [310.51, -164.12, 393.96, -216.76, 417.73, -231.75, 417.24, -252.37, 390.93, -300.29, 352.82, -285.88, 311.21, -270.15, 236.48, -277.79, 195.21, -341.76, 140.32, -354.97, 64.4, -354.82, 11.79, -325.85, -5.83, -261.55, -4.8, -218.14, 26.37, -162.69, 151.27, -162.88, 264.94, -163.04, 289.48, -239.73, 346.4, -225.85, 362.48, -251.62, 110.9, -305.87, 143.93, -228.3], "hull": 17, "edges": [0, 32, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 26, 28, 18, 20, 14, 34, 34, 36, 0, 2, 2, 4, 36, 2, 8, 10, 10, 12, 28, 30, 30, 32], "width": 626, "height": 549}}, "NIUJIAO_L": {"NIUJIAO_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-70.58, 100.64, 350.93, 121.03, 377.9, -436.32, -43.61, -456.71], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 422, "height": 558}}, "HAND_R_2": {"HAND_R_2": {"x": 283.54, "y": -1.99, "rotation": 16.16, "width": 225, "height": 219}}, "HAND_R_3": {"HAND_R_3": {"type": "mesh", "uvs": [0.48655, 1e-05, 0.87234, 0.21072, 1, 0.43998, 1, 0.58899, 0.97303, 0.76624, 0.93827, 0.99473, 0.70347, 0.99626, 0.44298, 0.99795, 0.14881, 0.78588, 0, 0.5605, 0, 0.45645, 0.08605, 0.2077, 0.35184, 1e-05, 0.5012, 0.27995, 0.32896, 0.41255, 0.19277, 0.27405], "triangles": [15, 11, 12, 10, 11, 15, 10, 15, 14, 9, 10, 14, 8, 9, 14, 13, 0, 1, 14, 15, 12, 12, 0, 13, 14, 12, 13, 1, 2, 3, 3, 13, 1, 3, 4, 13, 4, 6, 13, 5, 6, 4, 7, 14, 13, 7, 8, 14, 13, 6, 7], "vertices": [2, 11, 77.81, 108.63, 0.69474, 10, 280.97, 124.96, 0.30526, 1, 11, 225.14, 97.78, 1, 1, 11, 284.73, 56.81, 1, 1, 11, 294.85, 21.88, 1, 1, 11, 297.58, -22.35, 1, 1, 11, 301.11, -79.37, 1, 2, 11, 220.25, -103.19, 0.9897, 10, 535.91, 137.64, 0.0103, 2, 11, 130.54, -129.61, 0.86672, 10, 512.24, 47.17, 0.13328, 2, 11, 14.71, -109.29, 0.26785, 10, 435.06, -41.56, 0.73215, 2, 11, -51.91, -71.34, 0.00069, 10, 368.17, -79.03, 0.99931, 1, 10, 343.64, -72.5, 1, 1, 10, 292.94, -27.02, 1, 2, 11, 31.36, 95.17, 0.54371, 10, 268.53, 78.22, 0.45629, 2, 11, 101.87, 44.48, 0.82452, 10, 348.33, 112.46, 0.17548, 2, 11, 51.48, -3.8, 0.81454, 10, 363.68, 44.38, 0.18546, 2, 11, -4.88, 15.05, 0.15509, 10, 318.44, 5.83, 0.84491], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 10, 12, 12, 14, 6, 8, 8, 10, 0, 26, 26, 28, 28, 20], "width": 359, "height": 244}}, "HAND_L_HAIR_1": {"HAND_L_HAIR_1": {"type": "mesh", "uvs": [0.46749, 0, 0.64235, 0.04138, 0.80926, 0.15895, 0.86355, 0.24366, 0.97246, 0.27391, 0.99999, 0.2933, 0.94009, 0.31401, 0.83898, 0.33341, 0.80739, 0.49778, 0.88364, 0.60584, 0.99271, 0.65607, 0.89986, 0.68831, 0.71781, 0.69799, 0.61435, 0.66674, 0.53018, 0.60828, 0.49336, 0.69698, 0.52492, 0.75041, 0.60306, 0.79135, 0.55939, 0.80651, 0.38731, 0.80087, 0.34203, 0.88741, 0.28702, 0.9379, 0.29013, 0.99698, 0.26084, 1, 0.21884, 0.96573, 0.21465, 0.92177, 0.21115, 0.86633, 0.14276, 0.86935, 0.06117, 0.83709, 0, 0.78718, 0, 0.73516, 0.00427, 0.63678, 0.01288, 0.43824, 0.02351, 0.19314, 0.20395, 0.04065, 0.36377, 0, 0.83587, 0.64821, 0.70199, 0.59555, 0.60568, 0.48888, 0.50233, 0.29579, 0.39194, 0.0892, 0.27961, 0.80133, 0.25128, 0.65478, 0.23995, 0.4431, 0.25412, 0.23955], "triangles": [23, 24, 22, 24, 21, 22, 21, 25, 20, 26, 41, 20, 20, 41, 19, 43, 44, 39, 43, 32, 44, 32, 33, 44, 7, 3, 6, 7, 39, 3, 6, 4, 5, 6, 3, 4, 44, 40, 39, 39, 40, 1, 39, 2, 3, 39, 1, 2, 1, 40, 0, 33, 34, 44, 44, 34, 40, 34, 35, 40, 40, 35, 0, 24, 25, 21, 25, 26, 20, 26, 27, 41, 41, 27, 28, 28, 29, 41, 19, 16, 18, 18, 16, 17, 19, 41, 42, 42, 41, 30, 19, 15, 16, 19, 42, 15, 41, 29, 30, 30, 31, 42, 15, 42, 14, 13, 14, 37, 14, 42, 43, 42, 31, 43, 12, 36, 11, 13, 37, 12, 12, 37, 36, 10, 11, 9, 11, 36, 9, 36, 37, 9, 31, 32, 43, 14, 38, 37, 14, 43, 38, 37, 8, 9, 37, 38, 8, 8, 38, 7, 43, 39, 38, 38, 39, 7], "vertices": [3, 62, -209.7, 22.87, 0.00106, 61, -117.68, 22.87, 0.06262, 60, -25.66, 22.87, 0.93631, 2, 61, -101.5, 60.17, 0.10835, 60, -9.47, 60.17, 0.89165, 3, 62, -148.88, 95.21, 1e-05, 61, -56.86, 95.21, 0.19025, 60, 35.17, 95.21, 0.80974, 3, 62, -116.99, 106.29, 0.00011, 61, -24.96, 106.29, 0.27893, 60, 67.06, 106.29, 0.72096, 3, 62, -105.23, 129.49, 0.00268, 61, -13.21, 129.49, 0.35687, 60, 78.82, 129.49, 0.64045, 3, 62, -97.87, 135.27, 0.01448, 61, -5.84, 135.27, 0.42154, 60, 86.18, 135.27, 0.56399, 3, 62, -90.37, 122.24, 0.04655, 61, 1.66, 122.24, 0.48304, 60, 93.68, 122.24, 0.47041, 3, 62, -83.52, 100.37, 0.10542, 61, 8.5, 100.37, 0.54402, 60, 100.53, 100.37, 0.35056, 3, 62, -22.19, 92.42, 0.18496, 61, 69.84, 92.42, 0.59634, 60, 161.86, 92.42, 0.2187, 3, 62, 18.53, 108.05, 0.2663, 61, 110.55, 108.05, 0.62526, 60, 202.58, 108.05, 0.10844, 3, 62, 37.75, 131.14, 0.3334, 61, 129.78, 131.14, 0.62756, 60, 221.8, 131.14, 0.03903, 3, 62, 49.43, 110.96, 0.38926, 61, 141.45, 110.96, 0.60152, 60, 233.48, 110.96, 0.00922, 3, 62, 52.31, 71.75, 0.45569, 61, 144.34, 71.75, 0.54348, 60, 236.36, 71.75, 0.00083, 3, 62, 40.21, 49.73, 0.55494, 61, 132.23, 49.73, 0.44503, 60, 224.26, 49.73, 3e-05, 3, 62, 18, 32.05, 0.68409, 61, 110.03, 32.05, 0.3159, 60, 202.05, 32.05, 1e-05, 2, 62, 51.03, 23.51, 0.81624, 61, 143.05, 23.51, 0.18375, 2, 62, 71.13, 29.92, 0.91707, 61, 163.16, 29.92, 0.08293, 2, 62, 86.76, 46.43, 0.97403, 61, 178.78, 46.43, 0.02597, 2, 62, 92.25, 36.93, 0.99527, 61, 184.27, 36.93, 0.00473, 2, 62, 89.44, -0.02, 0.99999, 61, 181.47, -0.02, 1e-05, 1, 62, 121.62, -10.36, 1, 1, 62, 140.28, -22.54, 1, 1, 62, 162.38, -22.29, 1, 1, 62, 163.39, -28.61, 1, 2, 62, 150.41, -37.39, 0.99997, 61, 242.43, -37.39, 3e-05, 2, 62, 133.95, -37.98, 0.9997, 61, 225.98, -37.98, 0.0003, 2, 62, 113.21, -38.35, 0.99838, 61, 205.23, -38.35, 0.00162, 2, 62, 114.06, -53.07, 0.99226, 61, 206.08, -53.07, 0.00774, 3, 62, 101.66, -70.38, 0.96704, 61, 193.69, -70.38, 0.0318, 60, 285.71, -70.38, 0.00115, 3, 62, 82.75, -83.18, 0.89848, 61, 174.78, -83.18, 0.08597, 60, 266.8, -83.18, 0.01554, 3, 62, 63.3, -82.81, 0.76203, 61, 155.33, -82.81, 0.17037, 60, 247.35, -82.81, 0.0676, 3, 62, 26.53, -81.2, 0.56479, 61, 118.55, -81.2, 0.24574, 60, 210.58, -81.2, 0.18948, 3, 62, -47.67, -77.95, 0.34734, 61, 44.35, -77.95, 0.2707, 60, 136.37, -77.95, 0.38196, 3, 62, -139.28, -73.93, 0.1691, 61, -47.26, -73.93, 0.22226, 60, 44.77, -73.93, 0.60864, 3, 62, -195.57, -34.07, 0.05955, 61, -103.55, -34.07, 0.1396, 60, -11.53, -34.07, 0.80085, 3, 62, -210.13, 0.57, 0.01355, 61, -118.1, 0.57, 0.07242, 60, -26.08, 0.57, 0.91404, 3, 62, 34.18, 97.48, 0.37213, 61, 126.2, 97.48, 0.62752, 60, 218.23, 97.48, 0.00035, 3, 62, 13.94, 69.08, 0.35271, 61, 105.97, 69.08, 0.64317, 60, 197.99, 69.08, 0.00412, 3, 62, -26.34, 49.13, 0.12554, 61, 65.69, 49.13, 0.83937, 60, 157.71, 49.13, 0.03509, 2, 61, -6.93, 28.27, 0.36462, 60, 85.09, 28.27, 0.63538, 1, 60, 7.39, 6, 1, 1, 62, 89.18, -23.17, 1, 2, 62, 34.26, -28.23, 0.93781, 61, 126.29, -28.23, 0.06219, 3, 62, -44.94, -29.17, 0.02454, 61, 47.09, -29.17, 0.95518, 60, 139.11, -29.17, 0.02029, 2, 61, -28.97, -24.69, 0.06543, 60, 63.06, -24.69, 0.93457], "hull": 36, "edges": [0, 70, 0, 2, 14, 16, 36, 38, 44, 46, 58, 60, 66, 68, 68, 70, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 34, 36, 32, 34, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 54, 56, 16, 18, 18, 20, 10, 12, 12, 14, 8, 10, 6, 8, 2, 4, 4, 6, 64, 66, 60, 62, 62, 64], "width": 215, "height": 374}}, "HAND_L_HAIR_2": {"HAND_L_HAIR_2": {"type": "mesh", "uvs": [0.64367, 0, 1, 0.17394, 0.99999, 0.41837, 0.95823, 0.72037, 0.72581, 1, 0.36172, 0.8881, 0, 0.77693, 0.1108, 0.52515, 0.23102, 0.25194, 0.43907, 0.12357, 0.63936, 0, 0.53759, 0.64397, 0.69173, 0.34972], "triangles": [12, 9, 10, 10, 0, 12, 3, 12, 2, 9, 12, 8, 2, 12, 1, 12, 0, 1, 5, 11, 4, 6, 7, 5, 5, 7, 11, 4, 11, 3, 7, 8, 11, 3, 11, 12, 11, 8, 12], "vertices": [3, 68, -120.11, -33.02, 0.00726, 67, -64.19, -33.02, 0.18686, 66, -8.28, -33.02, 0.80588, 3, 68, -117.02, 35.85, 0.03456, 67, -61.1, 35.85, 0.2607, 66, -5.18, 35.85, 0.70474, 3, 68, -74.82, 56.95, 0.12821, 67, -18.91, 56.95, 0.3833, 66, 37.01, 56.95, 0.4885, 3, 68, -19.53, 76.7, 0.30943, 67, 36.38, 76.7, 0.43357, 66, 92.3, 76.7, 0.257, 3, 68, 46.3, 65.7, 0.53492, 67, 102.22, 65.7, 0.37063, 66, 158.14, 65.7, 0.09446, 3, 68, 54.5, 1.01, 0.69665, 67, 110.42, 1.01, 0.27789, 66, 166.34, 1.01, 0.02546, 3, 68, 62.65, -63.26, 0.69736, 67, 118.57, -63.26, 0.26371, 66, 174.49, -63.26, 0.03893, 3, 68, 10.81, -68.24, 0.5337, 67, 66.73, -68.24, 0.33144, 66, 122.65, -68.24, 0.13486, 3, 68, -45.44, -73.65, 0.30772, 67, 10.48, -73.65, 0.37734, 66, 66.4, -73.65, 0.31494, 3, 68, -83.32, -53.28, 0.12478, 67, -27.4, -53.28, 0.32903, 66, 28.52, -53.28, 0.54619, 3, 68, -119.79, -33.67, 0.0326, 67, -63.87, -33.67, 0.22733, 66, -7.95, -33.67, 0.74007, 2, 68, -0.93, 6.52, 0.24855, 67, 54.99, 6.52, 0.75145, 2, 67, -7.46, 4.43, 0.02711, 66, 48.46, 4.43, 0.97289], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 8, 10, 10, 12, 16, 18, 18, 20], "width": 169, "height": 193}}, "HAND_L_HAIR_3": {"HAND_L_HAIR_3": {"type": "mesh", "uvs": [0.69214, 0.19195, 1, 0.3496, 1, 0.44641, 0.84066, 0.65214, 0.68248, 0.85638, 0.42738, 0.9263, 0.15848, 1, 0.00507, 1, 0.20311, 0.82809, 0.236, 0.65421, 0.26134, 0.52022, 0.11597, 0.52018, 0, 0.52015, 0.08204, 0.38188, 0.15837, 0.25323, 0.30862, 0, 0.59225, 0.31804, 0.495, 0.59236, 0.14727, 0.39247, 0.36641, 0.82118], "triangles": [4, 5, 17, 5, 19, 17, 4, 17, 3, 19, 9, 17, 17, 9, 10, 17, 10, 16, 11, 18, 10, 12, 13, 11, 11, 13, 18, 10, 18, 14, 18, 13, 14, 6, 19, 5, 6, 8, 19, 6, 7, 8, 8, 9, 19, 17, 16, 3, 16, 0, 3, 2, 3, 1, 15, 16, 10, 3, 0, 1, 10, 14, 15, 16, 15, 0], "vertices": [2, 69, -6.08, 2.91, 0.90313, 70, -71.44, -17.5, 0.09687, 2, 69, -17.39, 78.93, 0.99927, 70, -103.31, 52.43, 0.00073, 2, 69, -2.65, 87.27, 0.9474, 70, -91.44, 64.52, 0.0526, 2, 69, 46.97, 72.69, 0.71323, 70, -39.73, 64.21, 0.28677, 3, 69, 96.22, 58.21, 0.38063, 70, 11.61, 63.9, 0.49321, 71, -57.35, 54.63, 0.12616, 3, 69, 136.14, 12.5, 0.09917, 70, 62.6, 31, 0.44133, 71, -1.35, 31.25, 0.4595, 2, 70, 116.36, -3.68, 0.20717, 71, 57.68, 6.61, 0.79283, 3, 69, 195.82, -66.79, 5e-05, 70, 141.86, -28.72, 0.00289, 71, 87.21, -13.52, 0.99706, 3, 69, 146.92, -41.44, 0.01707, 70, 87.86, -17.87, 0.18603, 71, 32.14, -12.38, 0.7969, 3, 69, 116.66, -49.76, 0.10786, 70, 61.08, -34.22, 0.41824, 71, 8.67, -33.21, 0.4739, 3, 69, 93.35, -56.16, 0.22522, 70, 40.44, -46.82, 0.63128, 71, -9.42, -49.26, 0.1435, 3, 69, 110.02, -85.65, 0.26325, 70, 64.61, -70.55, 0.54242, 71, 18.56, -68.34, 0.19433, 3, 69, 123.32, -109.17, 0.29038, 70, 83.89, -89.48, 0.52562, 71, 40.88, -83.57, 0.184, 3, 69, 92.85, -104.45, 0.36287, 70, 53.29, -93.36, 0.55713, 71, 11.46, -92.79, 0.08, 2, 69, 64.49, -100.05, 0.51571, 70, 24.83, -96.97, 0.48429, 2, 69, 8.68, -91.41, 0.72577, 70, -31.19, -104.07, 0.27423, 2, 69, 24.58, -6.48, 0.9926, 70, -39.37, -18.06, 0.0074, 1, 70, 10.43, 0.33, 1, 3, 69, 86.97, -90.31, 0.35326, 70, 43.75, -81.39, 0.56674, 71, -0.06, -82.7, 0.08, 2, 70, 59.86, 7.92, 0.4663, 71, 0.02, 8.05, 0.5337], "hull": 16, "edges": [2, 4, 12, 14, 14, 16, 0, 2, 30, 0, 8, 10, 10, 12, 4, 6, 6, 8, 28, 30, 20, 22, 22, 24, 24, 26, 26, 28, 16, 18, 18, 20], "width": 233, "height": 175}}, "HAND_L_HAIR_4": {"HAND_L_HAIR_4": {"type": "mesh", "uvs": [0.87732, 0.01542, 0.99821, 0.05736, 0.99524, 0.29556, 0.99194, 0.56126, 0.86288, 0.75233, 0.72995, 0.94913, 0.52607, 0.97839, 0.37546, 1, 0.27068, 1, 0.14033, 0.80715, 1e-05, 0.59954, 0, 0.53654, 0.02573, 0.42282, 0.23077, 0.29327, 0.45844, 0.14943, 0.69477, 0.00011, 0.30337, 0.64248, 0.65571, 0.47405, 0.76806, 0.24154, 0.54773, 0.75741, 0.47645, 0.55974, 0.40945, 0.40073], "triangles": [6, 7, 19, 7, 16, 19, 6, 19, 5, 5, 19, 4, 19, 17, 4, 19, 20, 17, 4, 17, 3, 8, 9, 16, 16, 7, 8, 9, 10, 16, 16, 20, 19, 16, 11, 12, 16, 21, 20, 16, 13, 21, 16, 10, 11, 20, 21, 17, 17, 18, 3, 3, 18, 2, 18, 17, 14, 2, 18, 1, 1, 18, 0, 14, 15, 18, 18, 15, 0, 12, 13, 16, 17, 21, 14, 21, 13, 14], "vertices": [3, 72, -12.98, 3.72, 0.9892, 73, -72.6, -38.86, 0.00227, 74, -85.67, -126.72, 0.00852, 2, 72, -12.12, 32.62, 0.99456, 73, -86.63, -13.58, 0.00544, 2, 72, 37.6, 46.77, 0.80795, 73, -51.14, 24, 0.19205, 2, 72, 93.06, 62.56, 0.49387, 73, -11.55, 65.92, 0.50613, 2, 72, 141.17, 46.37, 0.16598, 73, 38.08, 76.6, 0.83402, 3, 72, 190.73, 29.68, 0.01925, 73, 89.2, 87.6, 0.94871, 74, -48.9, 75.32, 0.03204, 2, 73, 127.56, 60.94, 0.85308, 74, -2.52, 80.91, 0.14692, 2, 73, 155.9, 41.24, 0.69296, 74, 31.74, 85.04, 0.30704, 2, 73, 173.4, 25.14, 0.43861, 74, 55.52, 84.65, 0.56139, 2, 73, 166.84, -25.69, 0.22016, 74, 84.42, 42.32, 0.77984, 3, 72, 165.49, -150.8, 4e-05, 73, 159.78, -80.41, 0.04695, 74, 115.53, -3.25, 0.95301, 3, 72, 152.39, -154.71, 0.00085, 73, 150.53, -90.48, 0.00809, 74, 115.31, -16.92, 0.99106, 3, 72, 127.08, -156.18, 0.02715, 73, 129.52, -104.68, 0.00776, 74, 109.07, -41.5, 0.96509, 3, 72, 86.81, -119.64, 0.18958, 73, 76.23, -93.86, 0.05638, 74, 62.07, -68.84, 0.75404, 3, 72, 42.11, -79.06, 0.51076, 73, 17.07, -81.84, 0.05778, 74, 9.88, -99.21, 0.43146, 3, 72, -4.3, -36.94, 0.81808, 73, -44.35, -69.36, 0.05001, 74, -44.29, -130.72, 0.13191, 3, 72, 154.7, -82.15, 0.00164, 73, 115.41, -26.93, 0.01079, 74, 46.83, 7.2, 0.98757, 3, 72, 96.78, -15.98, 0.33159, 73, 31.81, 0.32, 0.33497, 74, -33.74, -28.04, 0.33344, 3, 72, 41.14, -5.99, 0.66333, 73, -21.12, -19.54, 0.33497, 74, -60.07, -78.07, 0.0017, 2, 73, 91.47, 28.98, 0.98374, 74, -8.22, 33.04, 0.01626, 3, 72, 126.25, -49.65, 0.0016, 73, 74.34, -13.54, 0.34412, 74, 7.25, -10.11, 0.65428, 3, 72, 97.54, -74.1, 0.10244, 73, 62.17, -49.23, 0.13482, 74, 21.89, -44.86, 0.76274], "hull": 16, "edges": [0, 30, 0, 2, 14, 16, 20, 22, 22, 24, 16, 18, 18, 20, 22, 32, 28, 30, 24, 26, 26, 28, 6, 8, 8, 10, 2, 4, 4, 6, 0, 36, 36, 34, 10, 12, 12, 14, 32, 40, 40, 34], "width": 227, "height": 217}}, "HAND_L_HAIR_5": {"HAND_L_HAIR_5": {"type": "mesh", "uvs": [0.8134, 0.00236, 0.99681, 0.15886, 0.97758, 0.4426, 0.95389, 0.79198, 0.72712, 1, 0.49013, 1, 0.00783, 0.75065, 0.00805, 0.50623, 0.00827, 0.25412, 0.36063, 0.00498, 0.57148, 0.18652, 0.4631, 0.48769, 0.52907, 0.7541], "triangles": [11, 6, 7, 12, 11, 2, 7, 8, 11, 11, 10, 2, 3, 4, 12, 5, 6, 12, 11, 12, 6, 4, 5, 12, 3, 12, 2, 11, 8, 10, 2, 10, 1, 8, 9, 10, 10, 0, 1, 10, 9, 0], "vertices": [1, 63, -3.58, 30.28, 1, 2, 63, 34.2, 51.53, 0.98671, 64, -43.74, 51.53, 0.01329, 2, 63, 102.28, 48.55, 0.23351, 64, 24.33, 48.55, 0.76649, 2, 64, 108.15, 44.87, 0.27103, 65, 30.21, 44.87, 0.72897, 2, 64, 157.79, 17.59, 0.00026, 65, 79.85, 17.59, 0.99974, 1, 65, 79.56, -10.37, 1, 2, 64, 97.06, -66.65, 0.21629, 65, 19.12, -66.65, 0.78371, 3, 63, 116.35, -66.01, 0.0973, 64, 38.41, -66.01, 0.72791, 65, -39.54, -66.01, 0.17479, 3, 63, 55.85, -65.35, 0.73856, 64, -22.1, -65.35, 0.2602, 65, -100.04, -65.35, 0.00124, 1, 63, -3.51, -23.15, 1, 1, 63, 40.32, 1.27, 1, 3, 63, 112.46, -12.27, 0.00015, 64, 34.52, -12.27, 0.98699, 65, -43.42, -12.27, 0.01287, 1, 65, 20.59, -5.16, 1], "hull": 10, "edges": [0, 18, 0, 2, 6, 8, 8, 10, 10, 12, 16, 18, 2, 4, 4, 6, 12, 14, 14, 16], "width": 118, "height": 240}}, "HEAD_TX_R_1_2": {"HEAD_TX_8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [190.95, -12.95, 50.18, -47.73, 22.12, 65.85, 162.88, 100.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 145, "height": 117}}, "HEAD_TX_R_3_2": {"HEAD_TX_6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [314.15, 72.09, 237.45, 53.14, 199.79, 205.55, 276.49, 224.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 157}}, "HAND_R_PD_1": {"HAND_R_PD_1": {"type": "mesh", "uvs": [0.0623, 0.25266, 0.12683, 0, 0.27784, 0.1377, 0.46003, 0.30382, 0.72268, 0.46884, 0.85791, 0.5538, 1, 0.64307, 1, 0.76617, 0.97135, 0.8889, 0.86645, 1, 0.75117, 1, 0.62542, 0.95206, 0.36957, 0.8545, 0.16688, 0.6582, 0, 0.49656, 0.41718, 0.57332, 0.23744, 0.42551, 0.66605, 0.68613, 0.83426, 0.77171], "triangles": [15, 13, 16, 13, 14, 16, 15, 16, 3, 14, 0, 16, 16, 2, 3, 16, 0, 2, 0, 1, 2, 12, 15, 11, 12, 13, 15, 17, 15, 3, 10, 18, 9, 9, 18, 8, 18, 10, 17, 10, 11, 17, 11, 15, 17, 8, 18, 7, 18, 17, 5, 7, 18, 6, 5, 17, 4, 18, 5, 6, 4, 17, 3], "vertices": [2, 52, -57.18, 5.34, 0.00656, 51, -2.88, -2.25, 0.99344, 2, 52, -56.57, 35.32, 0.08736, 51, -6.23, 27.55, 0.91264, 3, 52, -25.17, 31.91, 0.20926, 51, 25.34, 28.31, 0.71497, 53, -73.27, 52.5, 0.07577, 3, 52, 12.7, 27.79, 0.33013, 51, 63.43, 29.22, 0.40777, 53, -37.72, 38.78, 0.2621, 3, 52, 64.24, 29.36, 0.27946, 51, 114.31, 37.58, 0.161, 53, 12.49, 27.06, 0.55954, 3, 52, 90.78, 30.17, 0.1581, 51, 140.51, 41.88, 0.02534, 53, 38.35, 21.03, 0.81656, 2, 52, 118.67, 31.03, 0.03644, 53, 65.51, 14.68, 0.96356, 2, 52, 123.73, 18.6, 0.00055, 53, 67.21, 1.37, 0.99945, 2, 52, 123.89, 4.22, 0.00093, 53, 63.68, -12.56, 0.99907, 2, 52, 110.59, -14.27, 0.04399, 53, 46.06, -27.02, 0.95601, 3, 52, 90.94, -22.28, 0.18877, 51, 147.59, -10.09, 0.00939, 53, 25.02, -29.71, 0.80183, 3, 52, 67.54, -26.17, 0.33747, 51, 124.91, -17.03, 0.12413, 53, 1.41, -27.45, 0.5384, 3, 52, 19.93, -34.08, 0.40188, 51, 78.75, -31.15, 0.34998, 53, -46.63, -22.87, 0.24813, 3, 52, -22.68, -28.34, 0.26287, 51, 35.76, -31.08, 0.66816, 53, -86.34, -6.36, 0.06897, 2, 52, -57.76, -23.61, 0.11325, 51, 0.36, -31.02, 0.88675, 1, 52, 16.48, -2.39, 1, 1, 51, 34.45, -2.62, 1, 2, 52, 63.53, 3.5, 0.0587, 53, 5.15, 2.25, 0.9413, 1, 53, 37.04, -3.09, 1], "hull": 15, "edges": [12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 2, 4, 4, 6, 2, 0, 0, 28, 20, 22, 22, 24, 6, 8, 8, 10, 10, 12], "width": 184, "height": 109}}, "HAND_L_1": {"HAND_L_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [363.39, 161.15, 445.13, -95.13, 211.71, -169.57, 129.97, 86.71], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 269, "height": 245}}, "YAODAI": {"YAODAI": {"x": 57.58, "y": -14.66, "rotation": -173.18, "width": 600, "height": 369}}, "EYE_R": {"EYE_R": {"type": "mesh", "uvs": [0.28088, 0.56772, 0.34286, 0.63896, 0.37878, 0.7255, 0.36423, 0.81599, 0.27208, 0.90991, 0.10597, 0.87207, 0.05023, 0.75151, 0.06388, 0.60573, 0.19358, 0.52583], "triangles": [3, 4, 1, 5, 6, 0, 4, 5, 1, 5, 0, 1, 8, 6, 7, 6, 8, 0, 3, 1, 2], "vertices": [3, 104, -25.58, 26.38, 0.00474, 103, -11.89, 4.57, 0.4044, 102, 15.15, -6.63, 0.59087, 3, 104, -27.51, 14.85, 0.00324, 103, -6.18, -5.64, 0.553, 102, 6.99, -15, 0.44377, 3, 104, -32.86, 5.36, 0.00156, 103, -4.42, -16.38, 0.57368, 102, -2.81, -19.75, 0.42476, 2, 103, -9.42, -25.43, 0.3837, 102, -12.9, -17.52, 0.6163, 2, 103, -24.74, -31.57, 0.10425, 102, -23.14, -4.57, 0.89575, 2, 103, -45.27, -20.55, 0.02047, 102, -18.4, 18.25, 0.97953, 1, 102, -4.73, 25.64, 1, 1, 102, 11.55, 23.4, 1, 3, 104, -28.33, 39.02, 0.00369, 103, -21.94, 12.7, 0.13358, 102, 20.11, 5.31, 0.86273], "hull": 9, "edges": [14, 12, 12, 10, 10, 8, 8, 6, 14, 16, 16, 0, 0, 2, 4, 6, 2, 4], "width": 138, "height": 112}}, "YAODAI_HAIR_1": {"YAODAI_HAIR_1": {"type": "mesh", "uvs": [0.65769, 0.02903, 0.98972, 0.01276, 0.98046, 0.23116, 0.85954, 0.51778, 0.61145, 0.77571, 0.3957, 1, 0.19315, 1, 0.00751, 0.82876, 0.00853, 0.32669, 0.24822, 0.04911, 0.22689, 0.56939, 0.46618, 0.48074, 0.76806, 0.37162], "triangles": [5, 11, 4, 4, 11, 12, 11, 9, 0, 4, 12, 3, 3, 12, 2, 11, 0, 12, 2, 12, 1, 1, 12, 0, 6, 10, 5, 5, 10, 11, 6, 7, 10, 7, 8, 10, 10, 9, 11, 10, 8, 9], "vertices": [3, 95, 83.52, 16.58, 0.18908, 96, 44.31, 16.58, 0.31105, 97, 5.11, 16.58, 0.49988, 3, 95, 119.09, 4.62, 0.04112, 96, 79.89, 4.62, 0.18987, 97, 40.68, 4.62, 0.76901, 3, 95, 113.53, -7.53, 0.0049, 96, 74.33, -7.53, 0.17568, 97, 35.12, -7.53, 0.81942, 3, 95, 94.69, -19.26, 0.08555, 96, 55.49, -19.26, 0.26685, 97, 16.28, -19.26, 0.6476, 3, 95, 62.96, -24.4, 0.27681, 96, 23.75, -24.4, 0.39675, 97, -15.45, -24.4, 0.32644, 3, 95, 35.36, -28.87, 0.56938, 96, -3.84, -28.87, 0.32121, 97, -43.05, -28.87, 0.10941, 3, 95, 13.87, -21, 0.81729, 96, -25.34, -21, 0.17922, 97, -64.54, -21, 0.00349, 2, 95, -2.24, -3.98, 0.88509, 96, -41.45, -3.98, 0.11491, 3, 95, 8.39, 24.74, 0.73558, 96, -30.81, 24.74, 0.20891, 97, -70.02, 24.74, 0.05551, 3, 95, 39.65, 31.33, 0.44814, 96, 0.44, 31.33, 0.32972, 97, -38.76, 31.33, 0.22214, 2, 95, 26.48, 2.36, 0.99544, 96, -12.73, 2.36, 0.00456, 2, 95, 53.73, -1.86, 0.00126, 96, 14.52, -1.86, 0.99874, 2, 96, 48.84, -7.33, 0.05748, 97, 9.64, -7.33, 0.94252], "hull": 10, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 2, 0, 0, 18, 6, 8, 8, 10], "width": 113, "height": 61}}, "BODY_PEISHI_1": {"BODY_PEISHI_1": {"x": 84.42, "y": 14.31, "rotation": 36.81, "width": 236, "height": 212}}, "BODY_PEISHI_2": {"BODY_PEISHI_2": {"type": "mesh", "uvs": [0.19625, 0, 0.31992, 0.04658, 0.37002, 0.11022, 0.39325, 0.22993, 0.52631, 0.33105, 0.57002, 0.36426, 0.69758, 0.26755, 0.83099, 0.5132, 0.86385, 0.6034, 0.94339, 0.71264, 1, 0.78176, 0.96292, 1, 0.93299, 1, 0.85038, 0.87815, 0.76731, 0.796, 0.72357, 0.75629, 0.68139, 0.81838, 0.54135, 0.6075, 0.49959, 0.57388, 0.25933, 0.3987, 0.15405, 0.4097, 0.07592, 0.37295, 0.00224, 0.25288, 0.00196, 0.09582, 0.09436, 0.02202, 0.5236, 0.45904, 0.53037, 0.54416, 0.84019, 0.58999, 0.81847, 0.67872, 0.76778, 0.73492, 0.69763, 0.55292, 0.81199, 0.75485, 0.85532, 0.69305, 0.47746, 0.50394, 0.48027, 0.41566, 0.90345, 0.8059], "triangles": [5, 25, 34, 30, 26, 25, 33, 25, 26, 30, 6, 7, 5, 6, 30, 30, 25, 5, 18, 33, 26, 8, 27, 7, 30, 7, 27, 17, 26, 30, 18, 26, 17, 28, 30, 27, 28, 27, 8, 32, 28, 8, 29, 30, 28, 31, 29, 28, 31, 28, 32, 15, 30, 29, 17, 30, 15, 14, 15, 29, 14, 29, 31, 16, 17, 15, 33, 34, 25, 5, 34, 4, 32, 8, 9, 35, 32, 9, 35, 9, 10, 31, 32, 35, 13, 31, 35, 14, 31, 13, 11, 12, 35, 13, 35, 12, 10, 11, 35, 22, 23, 24, 21, 22, 24, 19, 0, 1, 19, 1, 2, 19, 2, 3, 0, 21, 24, 20, 0, 19, 20, 21, 0, 19, 3, 34, 34, 3, 4, 19, 33, 18, 33, 19, 34], "vertices": [1, 59, 44.75, 49.57, 1, 1, 59, 97.73, 68.57, 1, 1, 59, 128.71, 63.54, 1, 1, 59, 162.27, 35.57, 1, 2, 59, 230.12, 41.52, 0.384, 54, 96.22, 94.99, 0.616, 1, 54, 92.31, 72.97, 1, 1, 54, 144.74, 34.12, 1, 1, 54, 85.2, -52.06, 1, 2, 54, 60.75, -77.19, 0.47617, 58, 96, -44.28, 0.52383, 2, 54, 37.53, -123.69, 0.00026, 58, 44.15, -40.62, 0.99974, 1, 58, 9.39, -40.21, 1, 1, 58, -33.09, 26.56, 1, 1, 58, -23.76, 35.64, 1, 2, 54, -32.03, -107.85, 0.00279, 58, 32.16, 29.7, 0.99721, 2, 54, -18.31, -63.48, 0.4369, 58, 78.4, 34.01, 0.5631, 1, 54, -12.26, -40.59, 1, 1, 54, -39.52, -31.71, 1, 1, 54, 7.47, 52.59, 1, 2, 59, 272.47, -34.46, 0.384, 54, 11.84, 73.89, 0.616, 1, 59, 151.53, -47.3, 1, 1, 59, 117.21, -77.87, 1, 1, 59, 82.18, -87.79, 1, 1, 59, 30.98, -72.87, 1, 1, 59, -2.53, -28.3, 1, 1, 59, 13.95, 16.75, 1, 1, 54, 53.57, 79.27, 1, 1, 54, 26.59, 65.35, 1, 1, 54, 61.36, -65.87, 1, 1, 54, 28.6, -68.75, 1, 1, 54, 1.91, -55.65, 1, 1, 54, 50.64, -3.39, 1, 2, 54, 2.45, -76.14, 0.38805, 58, 74.66, 9.99, 0.61195, 2, 54, 29.81, -85.53, 0.38413, 58, 76.45, -18.88, 0.61587, 2, 59, 249.88, -20.35, 0.504, 54, 31.34, 92.02, 0.496, 2, 59, 232.08, 5.47, 0.576, 54, 60.9, 102.48, 0.424, 2, 54, 0.34, -119.8, 0.00151, 58, 33.51, -4.78, 0.99849], "hull": 25, "edges": [0, 2, 10, 12, 18, 20, 22, 24, 30, 32, 42, 44, 44, 46, 46, 48, 48, 0, 10, 50, 50, 52, 52, 34, 34, 32, 24, 26, 12, 14, 14, 54, 54, 56, 56, 58, 58, 30, 26, 28, 28, 30, 28, 62, 62, 64, 64, 16, 16, 14, 16, 18, 2, 4, 6, 4, 34, 36, 36, 38, 36, 66, 66, 68, 6, 8, 8, 10, 68, 8, 38, 40, 40, 42, 20, 22], "width": 435, "height": 355}}, "BODY_PEISHI_3": {"BODY_PEISHI_3": {"x": 9.35, "y": 22.65, "rotation": -68.28, "width": 86, "height": 80}}, "BODY_PEISHI_4": {"BODY_PEISHI_4": {"type": "mesh", "uvs": [1, 0.32063, 0.91651, 0.42623, 0.83718, 0.42953, 0.77714, 0.53513, 0.58846, 0.73313, 0.39764, 0.87833, 0.2154, 1, 0.08461, 1, 0.06317, 0.87173, 0, 0.77933, 0, 0.61763, 0.1618, 0.63083, 0.31831, 0.55163, 0.46625, 0.42953, 0.65708, 0.23813, 0.69352, 0.16883, 0.74927, 0, 1, 0, 0.68924, 0.42293, 0.53058, 0.58463, 0.38049, 0.71003, 0.20253, 0.79583, 0.79001, 0.27113], "triangles": [22, 16, 17, 15, 16, 22, 22, 17, 0, 22, 14, 15, 1, 22, 0, 2, 22, 1, 18, 22, 2, 9, 10, 11, 21, 11, 12, 8, 9, 11, 21, 8, 11, 21, 20, 5, 7, 8, 21, 6, 21, 5, 7, 21, 6, 22, 18, 14, 13, 14, 18, 3, 18, 2, 4, 18, 3, 19, 13, 18, 20, 12, 13, 20, 13, 19, 4, 19, 18, 4, 5, 20, 4, 20, 19, 21, 12, 20], "vertices": [1, 55, -20.89, 40.13, 1, 1, 55, 17.13, 44.45, 1, 1, 55, 41.11, 29.81, 1, 3, 55, 72.15, 38.64, 0.71488, 56, -28.9, 36.64, 0.27188, 57, -168.11, -3.67, 0.01323, 3, 55, 152.97, 40.54, 0.41801, 56, 51.57, 44.41, 0.50818, 57, -91.84, 23.12, 0.07381, 3, 55, 227.82, 31.84, 0.09868, 56, 126.85, 41.18, 0.52937, 57, -17.96, 37.98, 0.37194, 3, 55, 297.18, 20.25, 0.00022, 56, 196.88, 34.66, 0.26737, 57, 51.59, 48.4, 0.73241, 2, 56, 237.46, 12.38, 0.0613, 57, 96.32, 36.47, 0.9387, 2, 56, 229.91, -17.14, 0.05065, 57, 96.05, 6.01, 0.94935, 2, 56, 239.29, -46.53, 0.00141, 57, 112.18, -20.29, 0.99859, 2, 56, 221.39, -79.13, 0.06512, 57, 102.6, -56.22, 0.93488, 3, 55, 266.93, -61.33, 0.00045, 56, 172.64, -48.9, 0.27182, 57, 48.04, -38.53, 0.72773, 3, 55, 210.53, -46.49, 0.06101, 56, 115.31, -38.21, 0.56841, 57, -10.19, -41.86, 0.37059, 3, 55, 151.31, -41.58, 0.29709, 56, 55.89, -37.62, 0.61018, 57, -68.03, -55.5, 0.09273, 3, 55, 70.68, -41.8, 0.67668, 56, -24.51, -43.7, 0.30781, 57, -144.64, -80.63, 0.01551, 1, 55, 51.18, -48.16, 1, 2, 55, 13.51, -70.02, 0.99681, 56, -79.48, -76, 0.00319, 1, 55, -60.98, -21.76, 1, 2, 55, 84.23, 0.06, 0.60463, 56, -14.04, -0.96, 0.39537, 3, 55, 151.59, 0.74, 0.31402, 56, 53.09, 4.61, 0.60247, 57, -80.84, -15.16, 0.08351, 3, 55, 211.87, -3.95, 0.04096, 56, 113.55, 4.32, 0.61404, 57, -22.07, -0.98, 0.345, 2, 56, 178.27, -8.7, 0.255, 57, 43.89, 1.85, 0.745, 1, 55, 35.31, -9.84, 1], "hull": 18, "edges": [28, 30, 32, 34, 30, 32, 28, 36, 36, 6, 6, 4, 4, 2, 0, 34, 2, 0, 36, 38, 38, 40, 40, 42, 42, 16, 16, 14, 12, 14, 42, 12, 40, 24, 24, 22, 22, 42, 22, 20, 18, 20, 16, 18, 40, 10, 10, 12, 10, 8, 8, 38, 38, 26, 26, 24, 26, 28, 8, 6, 30, 44, 44, 4], "width": 354, "height": 230}}, "BODY_1": {"BODY_1": {"type": "mesh", "uvs": [0.28897, 0.07189, 0.50588, 0.19742, 0.72378, 0.29972, 1, 0.40008, 1, 0.6066, 0.94882, 0.83822, 0.87024, 1, 0.63626, 1, 0.40943, 0.96946, 0.1951, 0.92893, 0.10401, 0.72434, 0, 0.53905, 0, 0.27849, 0.09151, 0, 0.16474, 0, 0.69877, 0.77259, 0.73628, 0.5487, 0.48623, 0.7668, 0.52553, 0.48694, 0.24333, 0.44834], "triangles": [19, 11, 12, 12, 13, 19, 0, 19, 13, 0, 13, 14, 19, 0, 1, 10, 11, 19, 19, 1, 18, 7, 15, 6, 6, 15, 5, 8, 17, 7, 7, 17, 15, 8, 9, 17, 9, 10, 17, 5, 15, 4, 16, 15, 18, 10, 19, 17, 15, 17, 18, 17, 19, 18, 16, 3, 4, 16, 2, 3, 18, 2, 16, 15, 16, 4, 18, 1, 2], "vertices": [1, 13, 157.63, -105.73, 1, 1, 6, 201.11, 104.33, 1, 1, 8, 46.47, 134.19, 1, 1, 8, 100.04, -33.17, 1, 1, 8, 7.2, -101.44, 1, 1, 5, 143.08, -150.55, 1, 1, 5, 48.4, -112.73, 1, 1, 5, 33.83, 27.6, 1, 1, 5, 36.66, 165.41, 1, 1, 5, 45.82, 296.29, 1, 1, 5, 153.7, 362.71, 1, 1, 13, -28.75, 146.41, 1, 1, 13, 108.08, 97.24, 1, 1, 13, 235.65, -7.25, 1, 1, 13, 220.72, -48.8, 1, 1, 5, 163.94, 3.21, 1, 1, 8, -60.99, 45.81, 1, 1, 5, 153.92, 131.02, 1, 2, 6, 39.65, 117.35, 0.248, 5, 311.7, 123.57, 0.752, 1, 13, -30.74, -8.78, 1], "hull": 15, "edges": [14, 30, 30, 32, 32, 4, 12, 14, 12, 10, 10, 8, 8, 6, 6, 4, 14, 16, 16, 34, 34, 36, 36, 2, 2, 4, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 20, 38, 28, 0, 0, 2, 38, 0], "width": 603, "height": 558}}, "EYE_L3": {"EYE_L": {"type": "mesh", "uvs": [0.87298, 0.1536, 0.90464, 0.39942, 0.91006, 0.5392, 0.7579, 0.46989, 0.64801, 0.45832, 0.56712, 0.64829, 0.46432, 0.88972, 0.35952, 0.92841, 0.19925, 0.89419, 0.1233, 0.84167, 0.03541, 0.65396, 0.03564, 0.30685, 0.12191, 0.12396, 0.24104, 0.16105, 0.38473, 0.4477, 0.44772, 0.40487, 0.54938, 0.24532, 0.65381, 0.08144, 0.28024, 0.39001, 0.29947, 0.64784, 0.26202, 0.78001, 0.39459, 0.67818, 0.52514, 0.52868, 0.66782, 0.27734], "triangles": [5, 21, 22, 22, 15, 16, 5, 22, 4, 3, 1, 2, 4, 22, 23, 1, 3, 23, 3, 4, 23, 23, 22, 16, 23, 0, 1, 16, 17, 23, 23, 17, 0, 5, 6, 21, 7, 19, 21, 8, 9, 20, 9, 10, 18, 18, 10, 11, 19, 20, 9, 12, 18, 11, 19, 9, 18, 13, 18, 12, 13, 14, 18, 19, 18, 14, 8, 20, 7, 20, 19, 7, 19, 14, 21, 7, 21, 6, 21, 15, 22, 21, 14, 15], "vertices": [4, 43, 40.18, -107.75, 0.00738, 44, 97.51, 29.11, 0.05995, 45, 71.26, -19.44, 0.18586, 46, 44.13, 12.25, 0.74681, 4, 43, 21.1, -113.36, 0.00451, 44, 100.89, 9.5, 0.06894, 45, 61.71, -36.89, 0.16061, 46, 48.25, -7.21, 0.76594, 4, 43, 10.21, -114.45, 0.00038, 44, 100.72, -1.43, 0.04266, 45, 54.77, -45.35, 0.16363, 46, 48.49, -18.15, 0.79333, 4, 43, 15.19, -88.95, 0.00389, 44, 75.96, 6.45, 0.08291, 45, 40.29, -23.78, 0.21353, 46, 23.45, -11.21, 0.69967, 4, 43, 15.78, -70.58, 0.02187, 44, 57.79, 9.15, 0.18218, 45, 27.74, -10.35, 0.27399, 46, 5.19, -9.19, 0.52197, 4, 43, 0.73, -57.33, 0.0907, 44, 42.89, -4.27, 0.2954, 45, 7.73, -11.59, 0.28929, 46, -9.19, -23.16, 0.32461, 4, 43, -18.39, -40.48, 0.2435, 44, 23.95, -21.32, 0.35251, 45, -17.71, -13.15, 0.24106, 46, -27.47, -40.91, 0.16293, 4, 43, -21.7, -23.03, 0.49825, 44, 6.23, -22.6, 0.29104, 45, -32.37, -3.14, 0.14839, 46, -45.13, -42.86, 0.06232, 4, 43, -19.48, 3.77, 0.77239, 44, -20.14, -17.31, 0.15603, 45, -49.73, 17.41, 0.05708, 46, -71.68, -38.57, 0.0145, 4, 43, -15.6, 16.52, 0.91459, 44, -32.36, -11.98, 0.06627, 45, -55.98, 29.18, 0.01723, 46, -84.09, -33.71, 0.00192, 3, 43, -1.21, 31.45, 0.97311, 44, -45.52, 4.03, 0.02347, 45, -56.33, 49.91, 0.00342, 4, 43, 25.86, 31.87, 0.97682, 44, -42.82, 30.97, 0.01594, 45, -37.45, 69.32, 0.00672, 46, -96.17, 8.82, 0.00053, 4, 43, 40.37, 17.7, 0.92609, 44, -27.08, 43.75, 0.03899, 45, -17.18, 69.53, 0.02934, 46, -80.92, 22.18, 0.00558, 4, 43, 37.81, -2.24, 0.79317, 44, -7.56, 38.92, 0.0961, 45, -4.91, 53.61, 0.0833, 46, -61.24, 18.08, 0.02743, 4, 43, 15.86, -26.61, 0.53312, 44, 14.12, 14.3, 0.16915, 45, -3.25, 20.85, 0.19245, 46, -38.65, -5.69, 0.10528, 4, 43, 19.38, -37.07, 0.28013, 44, 24.91, 16.59, 0.20369, 45, 6.63, 15.92, 0.27523, 46, -27.94, -3, 0.24095, 4, 43, 32.11, -53.84, 0.11579, 44, 43.03, 27.31, 0.16811, 45, 27.48, 13.04, 0.28885, 46, -10.24, 8.39, 0.42726, 4, 43, 45.19, -71.06, 0.03376, 44, 61.65, 38.31, 0.10472, 45, 48.9, 10.08, 0.24364, 46, 7.94, 20.09, 0.61787, 4, 43, 20.07, -9.09, 0.83458, 44, -2.81, 20.5, 0.08652, 45, -12.64, 36.23, 0.05557, 46, -55.79, -0.14, 0.02333, 4, 43, 0.01, -12.64, 0.73302, 44, -1.59, 0.17, 0.13187, 45, -24.34, 19.55, 0.07356, 46, -53.81, -20.41, 0.06155, 4, 43, -10.4, -6.56, 0.82486, 44, -8.83, -9.47, 0.11202, 45, -36, 16.51, 0.04346, 46, -60.68, -30.32, 0.01966, 4, 43, -2.09, -28.56, 0.4434, 44, 13.98, -3.75, 0.20402, 45, -14.58, 6.8, 0.14863, 46, -38.09, -23.74, 0.20395, 4, 43, 9.94, -50.16, 0.18546, 44, 36.83, 5.71, 0.22407, 45, 9.19, -0.01, 0.19328, 46, -15.63, -13.42, 0.3972, 4, 43, 29.95, -73.65, 0.05236, 44, 62.47, 22.87, 0.15612, 45, 39.94, -2.52, 0.18536, 46, 9.35, 4.7, 0.60616], "hull": 18, "edges": [0, 34, 0, 2, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 26, 36, 36, 38, 38, 40, 40, 16, 38, 42, 42, 44, 44, 46, 46, 2, 2, 4, 30, 32, 32, 34, 8, 10, 10, 12], "width": 167, "height": 78}}, "EYE_L4": {"EYE_L": {"type": "mesh", "uvs": [0.87298, 0.1536, 0.90464, 0.39942, 0.91006, 0.5392, 0.7579, 0.46989, 0.64801, 0.45832, 0.56712, 0.64829, 0.46432, 0.88972, 0.35952, 0.92841, 0.19925, 0.89419, 0.1233, 0.84167, 0.03541, 0.65396, 0.03564, 0.30685, 0.12191, 0.12396, 0.24104, 0.16105, 0.38473, 0.4477, 0.44772, 0.40487, 0.54938, 0.24532, 0.65381, 0.08144, 0.28024, 0.39001, 0.29947, 0.64784, 0.26202, 0.78001, 0.39459, 0.67818, 0.52514, 0.52868, 0.66782, 0.27734], "triangles": [5, 21, 22, 22, 15, 16, 5, 22, 4, 3, 1, 2, 4, 22, 23, 1, 3, 23, 3, 4, 23, 23, 22, 16, 23, 0, 1, 16, 17, 23, 23, 17, 0, 5, 6, 21, 7, 19, 21, 8, 9, 20, 9, 10, 18, 18, 10, 11, 19, 20, 9, 12, 18, 11, 19, 9, 18, 13, 18, 12, 13, 14, 18, 19, 18, 14, 8, 20, 7, 20, 19, 7, 19, 14, 21, 7, 21, 6, 21, 15, 22, 21, 14, 15], "vertices": [4, 43, 40.18, -107.75, 0.00738, 44, 97.51, 29.11, 0.05995, 45, 71.26, -19.44, 0.18586, 46, 44.13, 12.25, 0.74681, 4, 43, 21.1, -113.36, 0.00451, 44, 100.89, 9.5, 0.06894, 45, 61.71, -36.89, 0.16061, 46, 48.25, -7.21, 0.76594, 4, 43, 10.21, -114.45, 0.00038, 44, 100.72, -1.43, 0.04266, 45, 54.77, -45.35, 0.16363, 46, 48.49, -18.15, 0.79333, 4, 43, 15.19, -88.95, 0.00389, 44, 75.96, 6.45, 0.08291, 45, 40.29, -23.78, 0.21353, 46, 23.45, -11.21, 0.69967, 4, 43, 15.78, -70.58, 0.02187, 44, 57.79, 9.15, 0.18218, 45, 27.74, -10.35, 0.27399, 46, 5.19, -9.19, 0.52197, 4, 43, 0.73, -57.33, 0.0907, 44, 42.89, -4.27, 0.2954, 45, 7.73, -11.59, 0.28929, 46, -9.19, -23.16, 0.32461, 4, 43, -18.39, -40.48, 0.2435, 44, 23.95, -21.32, 0.35251, 45, -17.71, -13.15, 0.24106, 46, -27.47, -40.91, 0.16293, 4, 43, -21.7, -23.03, 0.49825, 44, 6.23, -22.6, 0.29104, 45, -32.37, -3.14, 0.14839, 46, -45.13, -42.86, 0.06232, 4, 43, -19.48, 3.77, 0.77239, 44, -20.14, -17.31, 0.15603, 45, -49.73, 17.41, 0.05708, 46, -71.68, -38.57, 0.0145, 4, 43, -15.6, 16.52, 0.91459, 44, -32.36, -11.98, 0.06627, 45, -55.98, 29.18, 0.01723, 46, -84.09, -33.71, 0.00192, 3, 43, -1.21, 31.45, 0.97311, 44, -45.52, 4.03, 0.02347, 45, -56.33, 49.91, 0.00342, 4, 43, 25.86, 31.87, 0.97682, 44, -42.82, 30.97, 0.01594, 45, -37.45, 69.32, 0.00672, 46, -96.17, 8.82, 0.00053, 4, 43, 40.37, 17.7, 0.92609, 44, -27.08, 43.75, 0.03899, 45, -17.18, 69.53, 0.02934, 46, -80.92, 22.18, 0.00558, 4, 43, 37.81, -2.24, 0.79317, 44, -7.56, 38.92, 0.0961, 45, -4.91, 53.61, 0.0833, 46, -61.24, 18.08, 0.02743, 4, 43, 15.86, -26.61, 0.53312, 44, 14.12, 14.3, 0.16915, 45, -3.25, 20.85, 0.19245, 46, -38.65, -5.69, 0.10528, 4, 43, 19.38, -37.07, 0.28013, 44, 24.91, 16.59, 0.20369, 45, 6.63, 15.92, 0.27523, 46, -27.94, -3, 0.24095, 4, 43, 32.11, -53.84, 0.11579, 44, 43.03, 27.31, 0.16811, 45, 27.48, 13.04, 0.28885, 46, -10.24, 8.39, 0.42726, 4, 43, 45.19, -71.06, 0.03376, 44, 61.65, 38.31, 0.10472, 45, 48.9, 10.08, 0.24364, 46, 7.94, 20.09, 0.61787, 4, 43, 20.07, -9.09, 0.83458, 44, -2.81, 20.5, 0.08652, 45, -12.64, 36.23, 0.05557, 46, -55.79, -0.14, 0.02333, 4, 43, 0.01, -12.64, 0.73302, 44, -1.59, 0.17, 0.13187, 45, -24.34, 19.55, 0.07356, 46, -53.81, -20.41, 0.06155, 4, 43, -10.4, -6.56, 0.82486, 44, -8.83, -9.47, 0.11202, 45, -36, 16.51, 0.04346, 46, -60.68, -30.32, 0.01966, 4, 43, -2.09, -28.56, 0.4434, 44, 13.98, -3.75, 0.20402, 45, -14.58, 6.8, 0.14863, 46, -38.09, -23.74, 0.20395, 4, 43, 9.94, -50.16, 0.18546, 44, 36.83, 5.71, 0.22407, 45, 9.19, -0.01, 0.19328, 46, -15.63, -13.42, 0.3972, 4, 43, 29.95, -73.65, 0.05236, 44, 62.47, 22.87, 0.15612, 45, 39.94, -2.52, 0.18536, 46, 9.35, 4.7, 0.60616], "hull": 18, "edges": [0, 34, 0, 2, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 26, 36, 36, 38, 38, 40, 40, 16, 38, 42, 42, 44, 44, 46, 46, 2, 2, 4, 30, 32, 32, 34, 8, 10, 10, 12], "width": 167, "height": 78}}, "EYE_L5": {"EYE_L": {"type": "mesh", "uvs": [0.87298, 0.1536, 0.90464, 0.39942, 0.91006, 0.5392, 0.7579, 0.46989, 0.64801, 0.45832, 0.56712, 0.64829, 0.46432, 0.88972, 0.35952, 0.92841, 0.19925, 0.89419, 0.1233, 0.84167, 0.03541, 0.65396, 0.03564, 0.30685, 0.12191, 0.12396, 0.24104, 0.16105, 0.38473, 0.4477, 0.44772, 0.40487, 0.54938, 0.24532, 0.65381, 0.08144, 0.28024, 0.39001, 0.29947, 0.64784, 0.26202, 0.78001, 0.39459, 0.67818, 0.52514, 0.52868, 0.66782, 0.27734], "triangles": [5, 21, 22, 22, 15, 16, 5, 22, 4, 3, 1, 2, 4, 22, 23, 1, 3, 23, 3, 4, 23, 23, 22, 16, 23, 0, 1, 16, 17, 23, 23, 17, 0, 5, 6, 21, 7, 19, 21, 8, 9, 20, 9, 10, 18, 18, 10, 11, 19, 20, 9, 12, 18, 11, 19, 9, 18, 13, 18, 12, 13, 14, 18, 19, 18, 14, 8, 20, 7, 20, 19, 7, 19, 14, 21, 7, 21, 6, 21, 15, 22, 21, 14, 15], "vertices": [4, 43, 40.18, -107.75, 0.00738, 44, 97.51, 29.11, 0.05995, 45, 71.26, -19.44, 0.18586, 46, 44.13, 12.25, 0.74681, 4, 43, 21.1, -113.36, 0.00451, 44, 100.89, 9.5, 0.06894, 45, 61.71, -36.89, 0.16061, 46, 48.25, -7.21, 0.76594, 4, 43, 10.21, -114.45, 0.00038, 44, 100.72, -1.43, 0.04266, 45, 54.77, -45.35, 0.16363, 46, 48.49, -18.15, 0.79333, 4, 43, 15.19, -88.95, 0.00389, 44, 75.96, 6.45, 0.08291, 45, 40.29, -23.78, 0.21353, 46, 23.45, -11.21, 0.69967, 4, 43, 15.78, -70.58, 0.02187, 44, 57.79, 9.15, 0.18218, 45, 27.74, -10.35, 0.27399, 46, 5.19, -9.19, 0.52197, 4, 43, 0.73, -57.33, 0.0907, 44, 42.89, -4.27, 0.2954, 45, 7.73, -11.59, 0.28929, 46, -9.19, -23.16, 0.32461, 4, 43, -18.39, -40.48, 0.2435, 44, 23.95, -21.32, 0.35251, 45, -17.71, -13.15, 0.24106, 46, -27.47, -40.91, 0.16293, 4, 43, -21.7, -23.03, 0.49825, 44, 6.23, -22.6, 0.29104, 45, -32.37, -3.14, 0.14839, 46, -45.13, -42.86, 0.06232, 4, 43, -19.48, 3.77, 0.77239, 44, -20.14, -17.31, 0.15603, 45, -49.73, 17.41, 0.05708, 46, -71.68, -38.57, 0.0145, 4, 43, -15.6, 16.52, 0.91459, 44, -32.36, -11.98, 0.06627, 45, -55.98, 29.18, 0.01723, 46, -84.09, -33.71, 0.00192, 3, 43, -1.21, 31.45, 0.97311, 44, -45.52, 4.03, 0.02347, 45, -56.33, 49.91, 0.00342, 4, 43, 25.86, 31.87, 0.97682, 44, -42.82, 30.97, 0.01594, 45, -37.45, 69.32, 0.00672, 46, -96.17, 8.82, 0.00053, 4, 43, 40.37, 17.7, 0.92609, 44, -27.08, 43.75, 0.03899, 45, -17.18, 69.53, 0.02934, 46, -80.92, 22.18, 0.00558, 4, 43, 37.81, -2.24, 0.79317, 44, -7.56, 38.92, 0.0961, 45, -4.91, 53.61, 0.0833, 46, -61.24, 18.08, 0.02743, 4, 43, 15.86, -26.61, 0.53312, 44, 14.12, 14.3, 0.16915, 45, -3.25, 20.85, 0.19245, 46, -38.65, -5.69, 0.10528, 4, 43, 19.38, -37.07, 0.28013, 44, 24.91, 16.59, 0.20369, 45, 6.63, 15.92, 0.27523, 46, -27.94, -3, 0.24095, 4, 43, 32.11, -53.84, 0.11579, 44, 43.03, 27.31, 0.16811, 45, 27.48, 13.04, 0.28885, 46, -10.24, 8.39, 0.42726, 4, 43, 45.19, -71.06, 0.03376, 44, 61.65, 38.31, 0.10472, 45, 48.9, 10.08, 0.24364, 46, 7.94, 20.09, 0.61787, 4, 43, 20.07, -9.09, 0.83458, 44, -2.81, 20.5, 0.08652, 45, -12.64, 36.23, 0.05557, 46, -55.79, -0.14, 0.02333, 4, 43, 0.01, -12.64, 0.73302, 44, -1.59, 0.17, 0.13187, 45, -24.34, 19.55, 0.07356, 46, -53.81, -20.41, 0.06155, 4, 43, -10.4, -6.56, 0.82486, 44, -8.83, -9.47, 0.11202, 45, -36, 16.51, 0.04346, 46, -60.68, -30.32, 0.01966, 4, 43, -2.09, -28.56, 0.4434, 44, 13.98, -3.75, 0.20402, 45, -14.58, 6.8, 0.14863, 46, -38.09, -23.74, 0.20395, 4, 43, 9.94, -50.16, 0.18546, 44, 36.83, 5.71, 0.22407, 45, 9.19, -0.01, 0.19328, 46, -15.63, -13.42, 0.3972, 4, 43, 29.95, -73.65, 0.05236, 44, 62.47, 22.87, 0.15612, 45, 39.94, -2.52, 0.18536, 46, 9.35, 4.7, 0.60616], "hull": 18, "edges": [0, 34, 0, 2, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 26, 36, 36, 38, 38, 40, 40, 16, 38, 42, 42, 44, 44, 46, 46, 2, 2, 4, 30, 32, 32, 34, 8, 10, 10, 12], "width": 167, "height": 78}}, "EYE_L6": {"EYE_L": {"type": "mesh", "uvs": [0.87298, 0.1536, 0.90464, 0.39942, 0.91006, 0.5392, 0.7579, 0.46989, 0.64801, 0.45832, 0.56712, 0.64829, 0.46432, 0.88972, 0.35952, 0.92841, 0.19925, 0.89419, 0.1233, 0.84167, 0.03541, 0.65396, 0.03564, 0.30685, 0.12191, 0.12396, 0.24104, 0.16105, 0.38473, 0.4477, 0.44772, 0.40487, 0.54938, 0.24532, 0.65381, 0.08144, 0.28024, 0.39001, 0.29947, 0.64784, 0.26202, 0.78001, 0.39459, 0.67818, 0.52514, 0.52868, 0.66782, 0.27734], "triangles": [5, 21, 22, 22, 15, 16, 5, 22, 4, 3, 1, 2, 4, 22, 23, 1, 3, 23, 3, 4, 23, 23, 22, 16, 23, 0, 1, 16, 17, 23, 23, 17, 0, 5, 6, 21, 7, 19, 21, 8, 9, 20, 9, 10, 18, 18, 10, 11, 19, 20, 9, 12, 18, 11, 19, 9, 18, 13, 18, 12, 13, 14, 18, 19, 18, 14, 8, 20, 7, 20, 19, 7, 19, 14, 21, 7, 21, 6, 21, 15, 22, 21, 14, 15], "vertices": [4, 43, 40.18, -107.75, 0.00738, 44, 97.51, 29.11, 0.05995, 45, 71.26, -19.44, 0.18586, 46, 44.13, 12.25, 0.74681, 4, 43, 21.1, -113.36, 0.00451, 44, 100.89, 9.5, 0.06894, 45, 61.71, -36.89, 0.16061, 46, 48.25, -7.21, 0.76594, 4, 43, 10.21, -114.45, 0.00038, 44, 100.72, -1.43, 0.04266, 45, 54.77, -45.35, 0.16363, 46, 48.49, -18.15, 0.79333, 4, 43, 15.19, -88.95, 0.00389, 44, 75.96, 6.45, 0.08291, 45, 40.29, -23.78, 0.21353, 46, 23.45, -11.21, 0.69967, 4, 43, 15.78, -70.58, 0.02187, 44, 57.79, 9.15, 0.18218, 45, 27.74, -10.35, 0.27399, 46, 5.19, -9.19, 0.52197, 4, 43, 0.73, -57.33, 0.0907, 44, 42.89, -4.27, 0.2954, 45, 7.73, -11.59, 0.28929, 46, -9.19, -23.16, 0.32461, 4, 43, -18.39, -40.48, 0.2435, 44, 23.95, -21.32, 0.35251, 45, -17.71, -13.15, 0.24106, 46, -27.47, -40.91, 0.16293, 4, 43, -21.7, -23.03, 0.49825, 44, 6.23, -22.6, 0.29104, 45, -32.37, -3.14, 0.14839, 46, -45.13, -42.86, 0.06232, 4, 43, -19.48, 3.77, 0.77239, 44, -20.14, -17.31, 0.15603, 45, -49.73, 17.41, 0.05708, 46, -71.68, -38.57, 0.0145, 4, 43, -15.6, 16.52, 0.91459, 44, -32.36, -11.98, 0.06627, 45, -55.98, 29.18, 0.01723, 46, -84.09, -33.71, 0.00192, 3, 43, -1.21, 31.45, 0.97311, 44, -45.52, 4.03, 0.02347, 45, -56.33, 49.91, 0.00342, 4, 43, 25.86, 31.87, 0.97682, 44, -42.82, 30.97, 0.01594, 45, -37.45, 69.32, 0.00672, 46, -96.17, 8.82, 0.00053, 4, 43, 40.37, 17.7, 0.92609, 44, -27.08, 43.75, 0.03899, 45, -17.18, 69.53, 0.02934, 46, -80.92, 22.18, 0.00558, 4, 43, 37.81, -2.24, 0.79317, 44, -7.56, 38.92, 0.0961, 45, -4.91, 53.61, 0.0833, 46, -61.24, 18.08, 0.02743, 4, 43, 15.86, -26.61, 0.53312, 44, 14.12, 14.3, 0.16915, 45, -3.25, 20.85, 0.19245, 46, -38.65, -5.69, 0.10528, 4, 43, 19.38, -37.07, 0.28013, 44, 24.91, 16.59, 0.20369, 45, 6.63, 15.92, 0.27523, 46, -27.94, -3, 0.24095, 4, 43, 32.11, -53.84, 0.11579, 44, 43.03, 27.31, 0.16811, 45, 27.48, 13.04, 0.28885, 46, -10.24, 8.39, 0.42726, 4, 43, 45.19, -71.06, 0.03376, 44, 61.65, 38.31, 0.10472, 45, 48.9, 10.08, 0.24364, 46, 7.94, 20.09, 0.61787, 4, 43, 20.07, -9.09, 0.83458, 44, -2.81, 20.5, 0.08652, 45, -12.64, 36.23, 0.05557, 46, -55.79, -0.14, 0.02333, 4, 43, 0.01, -12.64, 0.73302, 44, -1.59, 0.17, 0.13187, 45, -24.34, 19.55, 0.07356, 46, -53.81, -20.41, 0.06155, 4, 43, -10.4, -6.56, 0.82486, 44, -8.83, -9.47, 0.11202, 45, -36, 16.51, 0.04346, 46, -60.68, -30.32, 0.01966, 4, 43, -2.09, -28.56, 0.4434, 44, 13.98, -3.75, 0.20402, 45, -14.58, 6.8, 0.14863, 46, -38.09, -23.74, 0.20395, 4, 43, 9.94, -50.16, 0.18546, 44, 36.83, 5.71, 0.22407, 45, 9.19, -0.01, 0.19328, 46, -15.63, -13.42, 0.3972, 4, 43, 29.95, -73.65, 0.05236, 44, 62.47, 22.87, 0.15612, 45, 39.94, -2.52, 0.18536, 46, 9.35, 4.7, 0.60616], "hull": 18, "edges": [0, 34, 0, 2, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 26, 36, 36, 38, 38, 40, 40, 16, 38, 42, 42, 44, 44, 46, 46, 2, 2, 4, 30, 32, 32, 34, 8, 10, 10, 12], "width": 167, "height": 78}}, "Y": {"Y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.55, -90.53, -24.85, 3.37, 32.43, 24.35, 66.83, -69.54], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 61}}, "HEAD": {"HEAD": {"type": "mesh", "uvs": [0.38757, 0, 0.61418, 0.12613, 0.78632, 0.12613, 1, 0.14934, 1, 0.27316, 1, 0.44146, 1, 0.68715, 1, 1, 0.81465, 1, 0.6948, 0.94059, 0.4987, 0.84386, 0.24158, 0.82064, 0, 0.8632, 0, 0.62525, 0.06073, 0.36021, 0.11738, 0.14934, 0.20672, 0, 0.55971, 0.61944, 0.58804, 0.44533, 0.612, 0.25961, 0.79286, 0.66394, 0.819, 0.45307, 0.82772, 0.27122, 0.29824, 0.6059, 0.33746, 0.40664, 0.36796, 0.19384], "triangles": [25, 16, 0, 25, 0, 1, 15, 16, 25, 19, 25, 1, 22, 2, 3, 22, 3, 4, 25, 14, 15, 24, 25, 19, 24, 14, 25, 22, 4, 5, 18, 24, 19, 2, 19, 1, 21, 22, 5, 23, 14, 24, 17, 24, 18, 23, 24, 17, 2, 22, 19, 21, 19, 22, 13, 14, 23, 21, 18, 19, 20, 18, 21, 17, 18, 20, 21, 5, 6, 20, 21, 6, 11, 13, 23, 10, 23, 17, 11, 23, 10, 12, 13, 11, 20, 10, 17, 9, 10, 20, 8, 20, 6, 9, 20, 8, 8, 6, 7], "vertices": [2, 7, 350.18, 63.45, 0.92, 49, -268.58, -257.98, 0.08, 2, 7, 316.55, -4.96, 0.92, 49, -228.64, -193.05, 0.08, 2, 7, 321.6, -53.76, 0.92, 49, -229.06, -144, 0.08, 2, 7, 320.45, -115.1, 0.92, 49, -222.12, -83.04, 0.08, 2, 7, 280.91, -119.19, 0.92, 49, -182.38, -82.7, 0.08, 2, 7, 227.17, -124.74, 0.92, 49, -128.35, -82.24, 0.08, 2, 7, 148.73, -132.86, 0.92, 49, -49.49, -81.57, 0.08, 1, 7, 48.84, -143.19, 1, 1, 7, 43.4, -90.64, 1, 2, 7, 58.86, -54.7, 0.784, 18, 60.94, 62.22, 0.216, 2, 7, 84, 4.08, 0.656, 18, 18.22, 14.65, 0.344, 2, 7, 83.87, 77.74, 0.784, 18, -5.37, -55.12, 0.216, 2, 7, 63.2, 144.82, 0.92, 49, 9.44, -366.08, 0.08, 2, 7, 139.18, 152.68, 0.92, 49, -66.94, -366.73, 0.08, 2, 7, 225.58, 144.21, 0.92, 49, -152.17, -350.15, 0.08, 2, 7, 294.57, 135.11, 0.92, 49, -219.99, -334.57, 0.08, 2, 7, 344.88, 114.72, 0.92, 49, -268.14, -309.52, 0.08, 2, 7, 157.44, -5.8, 0.822, 50, 209.29, 180.39, 0.178, 2, 7, 213.86, -8.08, 0.772, 50, 265.35, 173.63, 0.228, 2, 7, 273.86, -8.75, 0.822, 50, 325.11, 168.19, 0.178, 2, 7, 150.07, -73.37, 0.914, 50, 196.57, 113.62, 0.086, 2, 7, 218.16, -73.82, 0.864, 50, 264.41, 107.76, 0.136, 2, 7, 276.48, -70.28, 0.914, 50, 322.83, 106.64, 0.086, 2, 7, 154.1, 68.77, 0.914, 50, 211.89, 254.99, 0.086, 2, 7, 218.87, 64.23, 0.864, 50, 276.1, 245.31, 0.136, 2, 7, 287.71, 62.61, 0.914, 50, 344.59, 238.22, 0.086], "hull": 17, "edges": [20, 34, 34, 36, 36, 38, 38, 2, 2, 4, 4, 6, 20, 18, 14, 16, 18, 16, 18, 40, 40, 42, 42, 44, 44, 4, 20, 22, 22, 46, 46, 48, 48, 50, 50, 0, 0, 2, 32, 30, 30, 28, 28, 26, 24, 26, 24, 22, 12, 14, 10, 12, 6, 8, 8, 10, 32, 0], "width": 285, "height": 321}}, "HEAD_TX_R_3_1": {"HEAD_TX_6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [314.15, 72.09, 237.45, 53.14, 199.79, 205.55, 276.49, 224.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 157}}, "HEAD_TX_L_3_1": {"HEAD_TX_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [142.98, -133.32, 242.86, -128.48, 249.77, -271.32, 149.89, -276.15], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 143}}, "ZHEZHAO": {"ZHEZHAO": {"type": "clipping", "end": "ZHEZHAO", "vertexCount": 4, "vertices": [-1481.15, 50.85, -1454.14, 3054.92, 1555.57, 3027.38, 1519.42, 22.32], "color": "ce3a3aff"}}, "NIUJIAO_R": {"NIUJIAO_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.65814, 0.15363, 0.50189, 0.42152, 0.63698, 0.63125, 0.37983, 0.57485, 0, 1, 0], "triangles": [5, 6, 7, 4, 1, 2, 4, 2, 3, 5, 7, 0, 4, 5, 0, 1, 4, 0], "vertices": [466.89, -37.47, -60.25, -167.73, -108.48, 27.41, -49.53, 136.61, 110.74, 94.4, 185.03, 268.51, 101.72, 477.98, 325.84, 533.36], "hull": 8, "edges": [0, 2, 0, 14, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 10, 12], "width": 543, "height": 588}}, "BODY_2": {"BODY_2": {"type": "mesh", "uvs": [0.88257, 0.27542, 0.95302, 0.52835, 1, 0.81791, 1, 1, 0.92278, 1, 0.79964, 0.92842, 0.64886, 0.88489, 0.42756, 0.88331, 0.26809, 0.87779, 0.1368, 0.64003, 0, 0.37253, 0, 0.28212, 0.06584, 0.10129, 0.20704, 1e-05, 0.43859, 1e-05, 0.62279, 1e-05, 0.40798, 0.58715, 0.3188, 0.25192, 0.63159, 0.55877, 0.53043, 0.2129, 0.81128, 0.53571, 0.71545, 0.22709], "triangles": [8, 16, 7, 6, 18, 20, 8, 9, 16, 17, 10, 11, 9, 10, 17, 11, 12, 17, 20, 0, 1, 5, 6, 20, 20, 1, 2, 4, 5, 2, 4, 2, 3, 5, 20, 2, 20, 21, 0, 19, 14, 15, 0, 21, 15, 19, 15, 21, 17, 13, 14, 17, 14, 19, 18, 19, 21, 18, 21, 20, 16, 17, 19, 16, 19, 18, 17, 12, 13, 9, 17, 16, 7, 16, 18, 7, 18, 6], "vertices": [2, 6, 281.6, -172.45, 0.84, 8, 267.57, 135.66, 0.16, 1, 8, 203.42, 38.03, 1, 1, 8, 118.47, -58.09, 1, 1, 8, 54.96, -104.8, 1, 1, 8, 28.56, -68.9, 1, 1, 8, 11.43, 6.7, 1, 2, 6, 41.45, 1.2, 0.376, 5, 342.97, 11.7, 0.624, 2, 6, 61.67, 127.29, 0.376, 5, 330.47, 138.78, 0.624, 1, 13, -51.69, -70.28, 1, 1, 13, 70.82, -33.82, 1, 1, 13, 206.52, 1.29, 1, 1, 13, 243.36, -11.95, 1, 1, 13, 304.2, -74.19, 1, 1, 6, 459.1, 194.48, 1, 1, 6, 438.65, 62.45, 1, 1, 6, 422.39, -42.58, 1, 1, 6, 190.12, 118.82, 1, 1, 6, 341.44, 147.45, 1, 1, 6, 182.52, -10.56, 1, 1, 6, 339.45, 24.2, 1, 1, 8, 152.4, 102.03, 1, 1, 6, 317.04, -80.36, 1], "hull": 16, "edges": [12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 12, 10, 6, 8, 10, 8, 6, 4, 4, 2, 2, 0, 0, 30, 14, 32, 32, 34, 34, 26, 12, 36, 36, 38, 26, 28, 28, 30, 38, 28, 10, 40, 40, 42, 42, 30], "width": 577, "height": 433}}, "XIABAI_HAIR_4": {"XIABAI_HAIR_4": {"type": "mesh", "uvs": [0.99999, 0.16714, 1, 0.32677, 0.93404, 0.52052, 0.71751, 0.69552, 0.41746, 0.89205, 0.21976, 0.99999, 0.11098, 0.82384, 0, 0.63916, 0.26187, 0.60253, 0.49458, 0.48212, 0.57924, 0.3708, 0.65446, 0.2719, 0.69722, 0.14102, 0.73833, 0.01518, 0.83753, 0.00379, 0.94909, 0, 0.8423, 0.15715, 0.83375, 0.29582, 0.762, 0.47091, 0.59627, 0.61099, 0.33487, 0.73145], "triangles": [6, 8, 20, 18, 17, 2, 2, 17, 1, 18, 11, 17, 17, 0, 1, 11, 12, 17, 17, 16, 0, 17, 12, 16, 16, 15, 0, 12, 13, 16, 13, 14, 16, 16, 14, 15, 5, 6, 4, 6, 20, 4, 4, 20, 19, 6, 7, 8, 19, 20, 8, 4, 19, 3, 19, 8, 9, 3, 18, 2, 3, 19, 18, 19, 9, 18, 9, 10, 18, 10, 11, 18], "vertices": [4, 85, -204.39, -38.01, 0.00945, 84, -143.68, -38.01, 0.01756, 83, -82.97, -38.01, 0.10312, 82, 24.9, 36.81, 0.86988, 4, 85, -179.81, -2.22, 0.05015, 84, -119.1, -2.21, 0.04823, 83, -58.39, -2.22, 0.19622, 82, 67.98, 42.2, 0.7054, 4, 85, -137.86, 32.9, 0.15623, 84, -77.15, 32.9, 0.0876, 83, -16.44, 32.9, 0.26882, 82, 122.1, 34.14, 0.48735, 4, 85, -71.11, 44.82, 0.33638, 84, -10.4, 44.82, 0.11597, 83, 50.31, 44.82, 0.27435, 82, 175.32, -7.87, 0.2733, 4, 85, 14.31, 51.01, 0.55617, 84, 75.02, 51.01, 0.11976, 83, 135.73, 51.01, 0.20727, 82, 236.66, -67.63, 0.1168, 4, 85, 67.27, 50.26, 0.73175, 84, 127.98, 50.26, 0.11287, 83, 188.69, 50.26, 0.12063, 82, 271.26, -107.74, 0.03476, 4, 85, 60.15, -2.96, 0.78661, 84, 120.86, -2.96, 0.12667, 83, 181.57, -2.96, 0.07474, 82, 226.73, -137.75, 0.01199, 4, 85, 52.12, -58.39, 0.69332, 84, 112.83, -58.39, 0.16813, 83, 173.54, -58.39, 0.10414, 82, 179.95, -168.54, 0.03441, 4, 85, -1.66, -33.55, 0.49852, 84, 59.05, -33.54, 0.20759, 83, 119.76, -33.55, 0.1828, 82, 162.82, -111.83, 0.11109, 4, 85, -62.98, -31.17, 0.28515, 84, -2.27, -31.17, 0.20344, 83, 58.44, -31.17, 0.25544, 82, 123.89, -64.4, 0.25598, 4, 85, -95.68, -45.45, 0.1242, 84, -34.97, -45.45, 0.15064, 83, 25.74, -45.45, 0.26738, 82, 91.5, -49.42, 0.45778, 4, 85, -124.74, -58.13, 0.03734, 84, -64.03, -58.13, 0.08081, 83, -3.32, -58.13, 0.2114, 82, 62.73, -36.11, 0.67046, 4, 85, -152.75, -82.08, 0.00625, 84, -92.04, -82.08, 0.029, 83, -31.33, -82.08, 0.1241, 82, 26.22, -31.07, 0.84066, 3, 84, -118.97, -105.11, 0.00586, 83, -58.26, -105.11, 0.05146, 82, -8.88, -26.21, 0.94268, 3, 84, -138.96, -95.14, 9e-05, 83, -78.25, -95.14, 0.02049, 82, -14.7, -4.65, 0.97942, 3, 84, -160.05, -81.91, 0.00367, 83, -99.34, -81.91, 0.03764, 82, -18.81, 19.91, 0.9587, 1, 82, 26.56, 1.58, 1, 1, 82, 64.23, 4.37, 1, 1, 83, 7.55, 0.07, 1, 2, 84, -1.13, 10.56, 0.37841, 83, 59.58, 10.56, 0.62159, 2, 85, 4.77, 4.58, 0.98123, 84, 65.48, 4.58, 0.01877], "hull": 16, "edges": [0, 30, 26, 28, 28, 30, 14, 16, 16, 18, 4, 6, 6, 8, 0, 2, 2, 4, 22, 24, 24, 26, 18, 20, 20, 22, 10, 12, 12, 14, 8, 10], "width": 223, "height": 272}}, "HEAD_TX_L_2_1": {"HEAD_TX_3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.41, 2.28, 230.3, 7.07, 236.97, -130.77, 138.08, -135.55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 99, "height": 138}}, "HEAD_TX_L_2_2": {"HEAD_TX_3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.41, 2.28, 230.3, 7.07, 236.97, -130.77, 138.08, -135.55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 99, "height": 138}}, "Y2": {"Y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.55, -90.53, -24.85, 3.37, 32.43, 24.35, 66.83, -69.54], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 61}}, "EAR_L": {"EAR_L": {"type": "mesh", "uvs": [0.86813, 0, 1, 0.02899, 1, 0.25865, 0.91117, 0.6654, 0.66335, 0.82895, 0.41682, 1, 0.28944, 1, 0.07323, 0.7807, 0, 0.49411, 0, 0.41948, 0.13175, 0.40848, 0.45266, 0.35653, 0.67824, 0.10633, 0.82216, 0, 0.80452, 0.32132, 0.53729, 0.59244, 0.256, 0.70602], "triangles": [3, 4, 14, 3, 14, 2, 15, 12, 14, 2, 14, 0, 14, 13, 0, 14, 12, 13, 0, 1, 2, 16, 10, 11, 4, 5, 15, 4, 15, 14, 15, 11, 12, 7, 16, 6, 15, 5, 16, 5, 6, 16, 7, 10, 16, 7, 8, 10, 16, 11, 15, 8, 9, 10], "vertices": [2, 32, -50.91, -31.94, 0.02394, 31, 12.25, -22.14, 0.97606, 2, 32, -70.95, -17.4, 0.0305, 31, -4.81, -4.18, 0.9695, 3, 32, -58.14, 6.74, 0.15854, 31, 12.18, 17.22, 0.8318, 33, -102.56, -28, 0.00966, 3, 32, -20.85, 41.75, 0.29389, 31, 55.22, 44.85, 0.58244, 33, -78.63, 17.2, 0.12367, 3, 32, 28.99, 37.33, 0.36954, 31, 103.42, 31.42, 0.2817, 33, -30.04, 29.17, 0.34877, 3, 32, 79.04, 33.8, 0.24769, 31, 151.99, 18.85, 0.08665, 33, 18.45, 42.05, 0.66566, 3, 32, 99.96, 22.7, 0.11231, 31, 170.54, 4.11, 0.00271, 33, 41.84, 38.32, 0.88498, 2, 32, 123.25, -19.21, 0.00679, 33, 77.45, 6.22, 0.99321, 2, 32, 119.29, -55.72, 5e-05, 33, 85.53, -29.61, 0.99995, 3, 32, 115.12, -63.57, 0.08288, 31, 169.74, -83.47, 0.00141, 33, 84.13, -38.38, 0.91571, 3, 32, 92.86, -53.23, 0.18844, 31, 149.74, -69.25, 0.09122, 33, 59.73, -35.81, 0.72034, 3, 32, 37.24, -30.7, 0.29449, 31, 99.15, -36.97, 0.29157, 33, -0.19, -32.52, 0.41394, 3, 32, -13.78, -37.33, 0.23497, 31, 47.78, -34.2, 0.60017, 33, -46.31, -55.32, 0.16486, 3, 32, -43.35, -35.95, 0.13, 31, 18.95, -27.46, 0.84311, 33, -74.74, -63.6, 0.02689, 1, 31, 45.29, 0.45, 1, 1, 32, 36.5, 1.47, 1, 1, 33, 42.48, 2.79, 1], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 24, 26, 18, 20, 20, 22, 22, 24, 6, 8, 8, 10], "width": 186, "height": 119}}, "XIABAI_HAIR_3": {"XIABAI_HAIR_3": {"type": "mesh", "uvs": [0.99399, 0.14927, 0.99393, 0.38493, 0.99388, 0.55782, 0.73745, 0.90145, 0.31158, 0.97826, 0.03297, 1, 1e-05, 0.81342, 0, 0.59882, 0.21107, 0.34388, 0.48517, 0.0128, 0.83164, 0.00454, 0.28122, 0.67628, 0.63677, 0.44582], "triangles": [3, 12, 2, 2, 12, 1, 12, 10, 1, 12, 9, 10, 1, 10, 0, 4, 5, 11, 5, 6, 11, 4, 12, 3, 4, 11, 12, 6, 7, 11, 7, 8, 11, 11, 8, 12, 8, 9, 12], "vertices": [3, 92, 165.58, 18.53, 0.00114, 93, 108.15, 8.74, 0.16577, 94, 54.38, 22.41, 0.83309, 3, 92, 160.49, -1.81, 0.08311, 93, 101.22, -11.05, 0.20754, 94, 52.4, 1.53, 0.70934, 3, 92, 156.75, -16.74, 0.10226, 93, 96.14, -25.58, 0.25432, 94, 50.94, -13.79, 0.64342, 3, 92, 109.53, -36.45, 0.31564, 93, 47.33, -40.92, 0.25885, 94, 7.22, -40.37, 0.42551, 3, 92, 41.76, -26.56, 0.64012, 93, -19.26, -24.91, 0.18699, 94, -61.26, -40.76, 0.17289, 3, 92, -1.95, -17.63, 0.87098, 93, -61.98, -12.04, 0.09765, 94, -105.82, -38.49, 0.03137, 2, 92, -3.04, -0.24, 0.88648, 93, -61.48, 5.37, 0.11352, 3, 92, 1.59, 18.29, 0.82802, 93, -55.19, 23.4, 0.1426, 94, -107.71, -2.44, 0.02938, 3, 92, 39.86, 32.11, 0.57627, 93, -15.83, 33.69, 0.25443, 94, -71.96, 16.96, 0.16931, 3, 92, 89.55, 50.06, 0.27497, 93, 35.29, 47.05, 0.30469, 94, -25.52, 42.17, 0.42034, 3, 92, 143.51, 37.33, 0.0825, 93, 87.87, 29.47, 0.19375, 94, 29.74, 37.68, 0.72374, 3, 92, 43.57, 0.69, 0.66587, 93, -14.98, 2.06, 0.22302, 94, -63.57, -13.55, 0.11111, 3, 92, 103.73, 6.79, 0.33275, 93, 45.49, 2.67, 0.30686, 94, -5, 1.52, 0.36039], "hull": 11, "edges": [0, 20, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 14, 16, 16, 18, 12, 22, 22, 24, 0, 2, 2, 4, 24, 2], "width": 160, "height": 89}}, "EYE_L2": {"EYE_L": {"type": "mesh", "uvs": [0.87298, 0.1536, 0.90464, 0.39942, 0.91006, 0.5392, 0.7579, 0.46989, 0.64801, 0.45832, 0.56712, 0.64829, 0.46432, 0.88972, 0.35952, 0.92841, 0.19925, 0.89419, 0.1233, 0.84167, 0.03541, 0.65396, 0.03564, 0.30685, 0.12191, 0.12396, 0.24104, 0.16105, 0.38473, 0.4477, 0.44772, 0.40487, 0.54938, 0.24532, 0.65381, 0.08144, 0.28024, 0.39001, 0.29947, 0.64784, 0.26202, 0.78001, 0.39459, 0.67818, 0.52514, 0.52868, 0.66782, 0.27734], "triangles": [5, 21, 22, 22, 15, 16, 5, 22, 4, 3, 1, 2, 4, 22, 23, 1, 3, 23, 3, 4, 23, 23, 22, 16, 23, 0, 1, 16, 17, 23, 23, 17, 0, 5, 6, 21, 7, 19, 21, 8, 9, 20, 9, 10, 18, 18, 10, 11, 19, 20, 9, 12, 18, 11, 19, 9, 18, 13, 18, 12, 13, 14, 18, 19, 18, 14, 8, 20, 7, 20, 19, 7, 19, 14, 21, 7, 21, 6, 21, 15, 22, 21, 14, 15], "vertices": [4, 43, 40.18, -107.75, 0.00738, 44, 97.51, 29.11, 0.05995, 45, 71.26, -19.44, 0.18586, 46, 44.13, 12.25, 0.74681, 4, 43, 21.1, -113.36, 0.00451, 44, 100.89, 9.5, 0.06894, 45, 61.71, -36.89, 0.16061, 46, 48.25, -7.21, 0.76594, 4, 43, 10.21, -114.45, 0.00038, 44, 100.72, -1.43, 0.04266, 45, 54.77, -45.35, 0.16363, 46, 48.49, -18.15, 0.79333, 4, 43, 15.19, -88.95, 0.00389, 44, 75.96, 6.45, 0.08291, 45, 40.29, -23.78, 0.21353, 46, 23.45, -11.21, 0.69967, 4, 43, 15.78, -70.58, 0.02187, 44, 57.79, 9.15, 0.18218, 45, 27.74, -10.35, 0.27399, 46, 5.19, -9.19, 0.52197, 4, 43, 0.73, -57.33, 0.0907, 44, 42.89, -4.27, 0.2954, 45, 7.73, -11.59, 0.28929, 46, -9.19, -23.16, 0.32461, 4, 43, -18.39, -40.48, 0.2435, 44, 23.95, -21.32, 0.35251, 45, -17.71, -13.15, 0.24106, 46, -27.47, -40.91, 0.16293, 4, 43, -21.7, -23.03, 0.49825, 44, 6.23, -22.6, 0.29104, 45, -32.37, -3.14, 0.14839, 46, -45.13, -42.86, 0.06232, 4, 43, -19.48, 3.77, 0.77239, 44, -20.14, -17.31, 0.15603, 45, -49.73, 17.41, 0.05708, 46, -71.68, -38.57, 0.0145, 4, 43, -15.6, 16.52, 0.91459, 44, -32.36, -11.98, 0.06627, 45, -55.98, 29.18, 0.01723, 46, -84.09, -33.71, 0.00192, 3, 43, -1.21, 31.45, 0.97311, 44, -45.52, 4.03, 0.02347, 45, -56.33, 49.91, 0.00342, 4, 43, 25.86, 31.87, 0.97682, 44, -42.82, 30.97, 0.01594, 45, -37.45, 69.32, 0.00672, 46, -96.17, 8.82, 0.00053, 4, 43, 40.37, 17.7, 0.92609, 44, -27.08, 43.75, 0.03899, 45, -17.18, 69.53, 0.02934, 46, -80.92, 22.18, 0.00558, 4, 43, 37.81, -2.24, 0.79317, 44, -7.56, 38.92, 0.0961, 45, -4.91, 53.61, 0.0833, 46, -61.24, 18.08, 0.02743, 4, 43, 15.86, -26.61, 0.53312, 44, 14.12, 14.3, 0.16915, 45, -3.25, 20.85, 0.19245, 46, -38.65, -5.69, 0.10528, 4, 43, 19.38, -37.07, 0.28013, 44, 24.91, 16.59, 0.20369, 45, 6.63, 15.92, 0.27523, 46, -27.94, -3, 0.24095, 4, 43, 32.11, -53.84, 0.11579, 44, 43.03, 27.31, 0.16811, 45, 27.48, 13.04, 0.28885, 46, -10.24, 8.39, 0.42726, 4, 43, 45.19, -71.06, 0.03376, 44, 61.65, 38.31, 0.10472, 45, 48.9, 10.08, 0.24364, 46, 7.94, 20.09, 0.61787, 4, 43, 20.07, -9.09, 0.83458, 44, -2.81, 20.5, 0.08652, 45, -12.64, 36.23, 0.05557, 46, -55.79, -0.14, 0.02333, 4, 43, 0.01, -12.64, 0.73302, 44, -1.59, 0.17, 0.13187, 45, -24.34, 19.55, 0.07356, 46, -53.81, -20.41, 0.06155, 4, 43, -10.4, -6.56, 0.82486, 44, -8.83, -9.47, 0.11202, 45, -36, 16.51, 0.04346, 46, -60.68, -30.32, 0.01966, 4, 43, -2.09, -28.56, 0.4434, 44, 13.98, -3.75, 0.20402, 45, -14.58, 6.8, 0.14863, 46, -38.09, -23.74, 0.20395, 4, 43, 9.94, -50.16, 0.18546, 44, 36.83, 5.71, 0.22407, 45, 9.19, -0.01, 0.19328, 46, -15.63, -13.42, 0.3972, 4, 43, 29.95, -73.65, 0.05236, 44, 62.47, 22.87, 0.15612, 45, 39.94, -2.52, 0.18536, 46, 9.35, 4.7, 0.60616], "hull": 18, "edges": [0, 34, 0, 2, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 26, 36, 36, 38, 38, 40, 40, 16, 38, 42, 42, 44, 44, 46, 46, 2, 2, 4, 30, 32, 32, 34, 8, 10, 10, 12], "width": 167, "height": 78}}, "HEAD_TX_R_4_1": {"HEAD_TX_5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [301.92, 213.28, 234.94, 196.72, 203.51, 323.9, 270.5, 340.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 69, "height": 131}}, "HEAD_TX_R_4_2": {"HEAD_TX_5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [301.92, 213.28, 234.94, 196.72, 203.51, 323.9, 270.5, 340.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 69, "height": 131}}}}], "animations": {"animation1": {"slots": {"HEAD_TX_L_4_2": {"color": [{"color": "ffffffd4"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffd4"}, {"time": 4.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 5.5, "color": "ffffff00"}, {"time": 6.5, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffffd4"}]}, "EYE_R2": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 3.8333, "color": "ffffffff"}, {"time": 4.3333, "color": "ffffff00"}]}, "HEAD_TX_R_1_2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.5, "color": "ffffff00"}, {"time": 4.5, "color": "ffffffff"}, {"time": 5.5, "color": "ffffff00"}]}, "EYE_L6": {"color": [{"color": "ffffffaa"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffaa"}, {"time": 3.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 6, "color": "ffffff00"}, {"time": 6.5, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffffaa"}]}, "EYE_R3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00"}, {"time": 4.5, "color": "ffffffff"}, {"time": 5, "color": "ffffff00"}]}, "HEAD_TX_R_3_2": {"color": [{"color": "ffffff2a"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffa9"}, {"time": 2.5, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff2a"}, {"time": 3.5, "color": "ffffff00", "curve": "stepped"}, {"time": 4.8333, "color": "ffffff00"}, {"time": 5.5, "color": "ffffffa9"}, {"time": 5.8333, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff2a"}]}, "EYE_R4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.6667, "color": "ffffff00"}, {"time": 5.1667, "color": "ffffffff"}, {"time": 5.6667, "color": "ffffff00"}]}, "HEAD_TX_L_1_2": {"color": [{"time": 0.1667, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.5, "color": "ffffff00"}, {"time": 4.5, "color": "ffffffff"}, {"time": 5.5, "color": "ffffff00"}]}, "EYE_L2": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 3.8333, "color": "ffffffff"}, {"time": 4.3333, "color": "ffffff00"}]}, "EYE_L5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 5.3333, "color": "ffffff00"}, {"time": 5.8333, "color": "ffffffff"}, {"time": 6.3333, "color": "ffffff00"}]}, "EYE_L4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.6667, "color": "ffffff00"}, {"time": 5.1667, "color": "ffffffff"}, {"time": 5.6667, "color": "ffffff00"}]}, "Y2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.1667, "color": "ffffff00"}, {"time": 4.3333, "color": "ffffffff"}, {"time": 4.6667, "color": "ffffff00"}]}, "EYE_L3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00"}, {"time": 4.5, "color": "ffffffff"}, {"time": 5, "color": "ffffff00"}]}, "HEAD_TX_R_4_2": {"color": [{"color": "ffffffd4"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffd4"}, {"time": 4.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 5.5, "color": "ffffff00"}, {"time": 6.5, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffffd4"}]}, "HEAD_TX_L_3_2": {"color": [{"color": "ffffff2a"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffa9"}, {"time": 2.5, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff2a"}, {"time": 3.5, "color": "ffffff00", "curve": "stepped"}, {"time": 4.8333, "color": "ffffff00"}, {"time": 5.5, "color": "ffffffa9"}, {"time": 5.8333, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff2a"}]}, "HEAD_TX_L_2_2": {"color": [{"time": 0.8333, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffffaa"}, {"time": 2.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.1667, "color": "ffffff00"}, {"time": 5.1667, "color": "ffffffff"}, {"time": 5.5, "color": "ffffffaa"}, {"time": 6.1667, "color": "ffffff00"}]}, "HEAD_TX_R_2_2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffffaa"}, {"time": 2.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.1667, "color": "ffffff00"}, {"time": 5.1667, "color": "ffffffff"}, {"time": 5.5, "color": "ffffffaa"}, {"time": 6.1667, "color": "ffffff00"}]}, "EYE_R6": {"color": [{"color": "ffffffaa"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffaa"}, {"time": 3.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 6, "color": "ffffff00"}, {"time": 6.5, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffffaa"}]}, "EYE_R5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 5.3333, "color": "ffffff00"}, {"time": 5.8333, "color": "ffffffff"}, {"time": 6.3333, "color": "ffffff00"}]}, "Y": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.1667, "color": "ffffff00"}, {"time": 4.3333, "color": "ffffffff"}, {"time": 4.6667, "color": "ffffff00"}]}, "EYELID": {"attachment": [{"name": null}]}}, "bones": {"ZONGKONG_ZHUTI5": {"translate": [{"x": 0.3, "y": 0.48, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "x": 7.09, "y": 11.35, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "x": 4.48, "y": 7.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "x": 0.3, "y": 0.48}, {"time": 4.8333, "x": 7.09, "y": 11.35, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "x": 4.48, "y": 7.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "x": 0.3, "y": 0.48}]}, "ZONGKONG_ZHUTI2": {"rotate": [{"angle": 1.7, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 2.24, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 1.7}, {"time": 3.8333, "angle": 2.24, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 1.7}], "translate": [{"x": 5.22, "y": 44.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "x": 6.02, "y": 51.82, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.1667, "x": 0.26, "y": 2.21, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 3.3333, "x": 5.22, "y": 44.95}, {"time": 3.6667, "x": 6.02, "y": 51.82, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.5, "x": 0.26, "y": 2.21, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 6.6667, "x": 5.22, "y": 44.95}]}, "ZONGKONG_ZHUTI3": {"translate": [{"x": -2.45, "y": 5.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "x": -4.89, "y": 11.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "x": -0.64, "y": 1.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "x": -2.45, "y": 5.71}, {"time": 4.1667, "x": -4.89, "y": 11.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "x": -0.64, "y": 1.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": -2.45, "y": 5.71}]}, "ZONGKONG_ZHUTI4": {"translate": [{"x": 2.81, "y": 2.64, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "x": 11.6, "y": 10.89, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "x": 4.27, "y": 4.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "x": 2.81, "y": 2.64}, {"time": 4.5, "x": 11.6, "y": 10.89, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "x": 4.27, "y": 4.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "x": 2.81, "y": 2.64}]}, "HEAD_HAIR2": {"rotate": [{"angle": 0.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 1.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.5, "angle": -0.41, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 1.8333, "angle": -1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.1667, "angle": -1.33, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 0.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": 1.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8333, "angle": -0.41, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.1667, "angle": -1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.5, "angle": -1.33, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 0.59}]}, "ZONGKONG_ZHUTI7": {"rotate": [{"angle": 3.89, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "angle": -3.71, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -0.75, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 2.8333, "angle": 3.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1667, "angle": 4.23, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 3.89}, {"time": 4.8333, "angle": -3.71, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -0.75, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 6.1667, "angle": 3.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.5, "angle": 4.23, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 3.89}]}, "ZONGKONG_ZHUTI8": {"rotate": [{"angle": 3.91, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.71, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -2.68, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 2.8333, "angle": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": 3.91}, {"time": 3.5, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -3.71, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -2.68, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 6.1667, "angle": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": 3.91}]}, "ZONGKONG_ZHUTI9": {"rotate": [{"angle": 2.3, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -3.71, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.8333, "angle": -0.79, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": 2.3}, {"time": 3.8333, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -3.71, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.1667, "angle": -0.79, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": 2.3}]}, "ZONGKONG_ZHUTI97": {"translate": [{"x": 6.24, "y": -17.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "x": 8.23, "y": -23.19, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "x": 6.24, "y": -17.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "x": 8.23, "y": -23.19, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "x": 6.24, "y": -17.57}]}, "ZONGKONG_ZHUTI96": {"rotate": [{"angle": 1.67, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 2.21, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 1.67, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": 2.21, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 1.67}], "translate": [{"x": 9.96, "y": 11.39, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "x": 13.14, "y": 15.03, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "x": 9.96, "y": 11.39, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "x": 13.14, "y": 15.03, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "x": 9.96, "y": 11.39}]}, "ZONGKONG_ZHUTI40": {"translate": [{"x": -8.33, "y": -8.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "x": -7.02, "y": -7.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": -12.42, "y": -9.89, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "x": -8.33, "y": -8.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "x": -7.02, "y": -7.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -12.42, "y": -9.89, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "x": -8.33, "y": -8.08}]}, "HEAD_HAIR18": {"rotate": [{"angle": 2.61, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 2.84, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.5, "angle": -1.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.8333, "angle": -2.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -1.86, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 3.3333, "angle": 2.61, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": 2.84, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "angle": -1.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.1667, "angle": -2.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -1.86, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 6.6667, "angle": 2.61}]}, "HEAD_HAIR19": {"rotate": [{"angle": 3.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 5.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.5, "angle": -0.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1667, "angle": -4.43, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 3.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": 5.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8333, "angle": -0.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.5, "angle": -4.43, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 3.43}]}, "HEAD_HAIR20": {"rotate": [{"angle": -1.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": 9.86, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.5, "angle": 1.73, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 2.1667, "angle": -9.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -12.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -1.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": 9.86, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.8333, "angle": 1.73, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 5.5, "angle": -9.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -12.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -1.19}]}, "HEAD_HAIR3": {"rotate": [{"angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": 4.39, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.5, "angle": 1.43, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 1.8333, "angle": -0.68, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.1667, "angle": -2.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -3.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": 4.39, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.8333, "angle": 1.43, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.1667, "angle": -0.68, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.5, "angle": -2.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -3.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.38}]}, "HEAD_HAIR4": {"rotate": [{"angle": -2.36, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": 8.96, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.5, "angle": 7.05, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 1.8333, "angle": 3.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.1667, "angle": -0.47, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": -5.98, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -2.36, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "angle": 8.96, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.8333, "angle": 7.05, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 5.1667, "angle": 3.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.5, "angle": -0.47, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": -5.98, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -2.36}]}, "HEAD_HAIR5": {"rotate": [{"angle": -10.07, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "angle": 20.42, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.8333, "angle": 16.28, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.1667, "angle": 8.83, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": -11.42, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": -10.07, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "angle": 20.42, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.1667, "angle": 16.28, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 5.5, "angle": 8.83, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": -11.42, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": -10.07}]}, "HEAD_HAIR8": {"rotate": [{"angle": -5.26, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "angle": -2.06, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1667, "angle": 13.87, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.5, "angle": 10.65, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 1.8333, "angle": 4.6, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.1667, "angle": -2.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": -11.33, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -5.26, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 3.5, "angle": -2.06, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.5, "angle": 13.87, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.8333, "angle": 10.65, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 5.1667, "angle": 4.6, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.5, "angle": -2.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": -11.33, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -5.26}]}, "HEAD_HAIR7": {"rotate": [{"angle": 0.91, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": 2.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8333, "angle": 6.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.5, "angle": 2.49, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 1.8333, "angle": -0.71, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.1667, "angle": -3.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -5.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 0.91, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 3.5, "angle": 2.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.1667, "angle": 6.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.8333, "angle": 2.49, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.1667, "angle": -0.71, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.5, "angle": -3.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -5.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.91}]}, "HEAD_HAIR6": {"rotate": [{"angle": 1.31, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.71}, {"time": 0.1667, "angle": 1.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5, "angle": 2.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.5, "angle": -0.63, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 1.8333, "angle": -1.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.1667, "angle": -2.41, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 1.31, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.71}, {"time": 3.5, "angle": 1.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.8333, "angle": 2.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8333, "angle": -0.63, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.1667, "angle": -1.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.5, "angle": -2.41, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 1.31}]}, "HEAD_HAIR9": {"rotate": [{"angle": 3.81, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 4.04, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.5, "angle": -0.7, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.8333, "angle": -1.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -0.7, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 3.3333, "angle": 3.81, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": 4.04, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "angle": -0.7, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.1667, "angle": -1.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -0.7, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 6.6667, "angle": 3.81}]}, "HEAD_HAIR10": {"rotate": [{"angle": 4.07, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 6.71, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.5, "angle": -0.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1667, "angle": -4.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 4.07, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": 6.71, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8333, "angle": -0.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.5, "angle": -4.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 4.07}]}, "HEAD_HAIR11": {"rotate": [{"angle": 0.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": 11.18, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.5, "angle": 3.01, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 2.1667, "angle": -8.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -11.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 0.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": 11.18, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.8333, "angle": 3.01, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 5.5, "angle": -8.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -11.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.07}]}, "HEAD_HAIR24": {"rotate": [{"angle": 1.47, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 1.1667, "angle": -1.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.5, "angle": -2.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -0.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": 1.63, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 1.47, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 4.5, "angle": -1.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.8333, "angle": -2.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -0.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": 1.63, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 1.47}]}, "HEAD_HAIR25": {"rotate": [{"angle": 4.2, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 4.72, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.1667, "angle": -2.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.8333, "angle": -7.24, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -5.68, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 3.3333, "angle": 4.2, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": 4.72, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.5, "angle": -2.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1667, "angle": -7.24, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -5.68, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 6.6667, "angle": 4.2}]}, "HEAD_HAIR26": {"rotate": [{"angle": 4.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 11.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.1667, "angle": 1, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.1667, "angle": -17.22, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 4.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": 11.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.5, "angle": 1, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.5, "angle": -17.22, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 4.62}]}, "HEAD_HAIR12": {"rotate": [{"angle": 2.33, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 1, "angle": -0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.3333, "angle": -1.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1667, "angle": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "angle": 2.91, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 2.33, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 4.3333, "angle": -0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.6667, "angle": -1.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "angle": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3333, "angle": 2.91, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.6667, "angle": 2.33}]}, "HEAD_HAIR13": {"rotate": [{"angle": 5.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -1.24, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6667, "angle": -5.25, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.1667, "angle": -2.61, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 3.3333, "angle": 5.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.3333, "angle": -1.24, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5, "angle": -5.25, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.5, "angle": -2.61, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.6667, "angle": 5.67}]}, "HEAD_HAIR14": {"rotate": [{"angle": 9.12, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": 12.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "angle": 3.46, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2, "angle": -11.75, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.1667, "angle": -10.73, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 3.3333, "angle": 9.12, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "angle": 12.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.3333, "angle": 3.46, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.3333, "angle": -11.75, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.5, "angle": -10.73, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 6.6667, "angle": 9.12}]}, "HEAD_HAIR15": {"rotate": [{"angle": -0.02, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.5, "angle": -1.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": -1.7}, {"time": 2.1667, "angle": 1.01}, {"time": 2.5, "angle": 1.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -0.02, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 3.8333, "angle": -1.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.1667, "angle": -1.7}, {"time": 5.5, "angle": 1.01}, {"time": 5.8333, "angle": 1.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -0.02}]}, "HEAD_HAIR16": {"rotate": [{"angle": 1.84, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -1.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1667, "angle": -4.06}, {"time": 2.1667, "angle": 0.61}, {"time": 2.8333, "angle": 3.73, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 1.84, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 3.8333, "angle": -1.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.5, "angle": -4.06}, {"time": 5.5, "angle": 0.61}, {"time": 6.1667, "angle": 3.73, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 1.84}]}, "HEAD_HAIR17": {"rotate": [{"angle": 7, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": 1.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5, "angle": -8.45}, {"time": 2.1667, "angle": -2.01}, {"time": 3.1667, "angle": 7.64, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 7, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 3.8333, "angle": 1.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8333, "angle": -8.45}, {"time": 5.5, "angle": -2.01}, {"time": 6.5, "angle": 7.64, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 7}]}, "HEAD_HAIR21": {"rotate": [{"angle": -0.23, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.5, "angle": 0.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": 1.08, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": -1.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -1.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -0.23, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 3.8333, "angle": 0.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.1667, "angle": 1.08, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -1.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -1.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -0.23}]}, "HEAD_HAIR22": {"rotate": [{"angle": -2.97, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 0.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1667, "angle": 2.91, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": -2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": -4.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -2.97, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 3.8333, "angle": 0.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.5, "angle": 2.91, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": -2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": -4.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -2.97}]}, "HEAD_HAIR23": {"rotate": [{"angle": -7.17, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": -1.99, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5, "angle": 7.97, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": 2.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": -7.79, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": -7.17, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 3.8333, "angle": -1.99, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8333, "angle": 7.97, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": 2.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": -7.79, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": -7.17}]}, "ZONGKONG_ZHUTI35": {"rotate": [{"angle": -1.15, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.8333, "angle": 7.65, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1667, "angle": 9.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": 0.58, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": -4.61, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -1.15, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 4.1667, "angle": 7.65, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.5, "angle": 9.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": 0.58, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": -4.61, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -1.15}]}, "ZONGKONG_ZHUTI36": {"rotate": [{"angle": -9.75, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.8333, "angle": 7.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.5, "angle": 17.7, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": 7.18, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": -10.91, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": -9.75, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 4.1667, "angle": 7.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.8333, "angle": 17.7, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": 7.18, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": -10.91, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": -9.75}]}, "ZONGKONG_ZHUTI37": {"rotate": [{"angle": -20.65, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -22.78, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.8333, "angle": -4.8, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8333, "angle": 26.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": 19.74, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 3.3333, "angle": -20.65, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": -22.78, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.1667, "angle": -4.8, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.1667, "angle": 26.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": 19.74, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 6.6667, "angle": -20.65}]}, "ZONGKONG_ZHUTI24": {"rotate": [{"angle": -2.49, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 1.1667, "angle": 1.51, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.5, "angle": 2.14, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": 0.36, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": -2.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": -2.49, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 4.5, "angle": 1.51, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.8333, "angle": 2.14, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": 0.36, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": -2.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": -2.49}]}, "ZONGKONG_ZHUTI25": {"rotate": [{"angle": -4.87, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -5.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.1667, "angle": 1.36, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.8333, "angle": 5.25, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": 3.87, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 3.3333, "angle": -4.87, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": -5.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.5, "angle": 1.36, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1667, "angle": 5.25, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": 3.87, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 6.6667, "angle": -4.87}]}, "ZONGKONG_ZHUTI26": {"rotate": [{"angle": -4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -8.35, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.1667, "angle": -1.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.1667, "angle": 9.64, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -8.35, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.5, "angle": -1.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.5, "angle": 9.64, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -4}]}, "ZONGKONG_ZHUTI60": {"rotate": [{"angle": 0.16, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.5, "angle": -1.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": -2.21, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": 1.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": 2.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 0.16, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 3.8333, "angle": -1.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.1667, "angle": -2.21, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": 1.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": 2.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.16}]}, "ZONGKONG_ZHUTI62": {"rotate": [{"angle": 10.95, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": 2.32, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5, "angle": -14.29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -4.62, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": 11.99, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 10.95, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 3.8333, "angle": 2.32, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8333, "angle": -14.29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -4.62, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": 11.99, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 10.95}]}, "ZONGKONG_ZHUTI61": {"rotate": [{"angle": 3.17, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -1.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1667, "angle": -5.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": 1.67, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": 6.09, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 3.17, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 3.8333, "angle": -1.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.5, "angle": -5.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": 1.67, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": 6.09, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 3.17}]}, "ZONGKONG_ZHUTI65": {"rotate": [{"angle": 5.61, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 11.99, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.1667, "angle": 2.32, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.1667, "angle": -14.29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.8333, "angle": -4.62, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": 5.61, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": 11.99, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.5, "angle": 2.32, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.5, "angle": -14.29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.1667, "angle": -4.62, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": 5.61}]}, "ZONGKONG_ZHUTI64": {"rotate": [{"angle": 5.61, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 6.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.1667, "angle": -1.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.8333, "angle": -5.94, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -4.38, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 2.8333, "angle": 1.67, "curve": 0.363, "c2": 0.44, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": 5.61, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": 6.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.5, "angle": -1.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1667, "angle": -5.94, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -4.38, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 6.1667, "angle": 1.67, "curve": 0.363, "c2": 0.44, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": 5.61}]}, "ZONGKONG_ZHUTI63": {"rotate": [{"angle": 2.36, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 1.1667, "angle": -1.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.5, "angle": -2.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -0.43, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 2.8333, "angle": 1.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1667, "angle": 2.57, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 2.36, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 4.5, "angle": -1.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.8333, "angle": -2.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -0.43, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 6.1667, "angle": 1.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.5, "angle": 2.57, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 2.36}]}, "ZONGKONG_ZHUTI67": {"rotate": [{"angle": 0.12, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.5, "angle": -1.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": -2.68, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": 2.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": 2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 0.12, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 3.8333, "angle": -1.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.1667, "angle": -2.68, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": 2.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": 2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.12}]}, "ZONGKONG_ZHUTI68": {"rotate": [{"angle": 2.95, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -1.37, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1667, "angle": -5.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": 1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": 5.65, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 2.95, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 3.8333, "angle": -1.37, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.5, "angle": -5.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": 1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": 5.65, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 2.95}]}, "ZONGKONG_ZHUTI69": {"rotate": [{"angle": 11.16, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": 2.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5, "angle": -13.12, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -3.82, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": 12.16, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 11.16, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 3.8333, "angle": 2.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8333, "angle": -13.12, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -3.82, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": 12.16, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 11.16}]}, "ZONGKONG_ZHUTI73": {"rotate": [{"angle": -1.01, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": 2.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": -0.38, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.5, "angle": -1.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.8333, "angle": -2.26, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -1.01, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "angle": 2.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": -0.38, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.8333, "angle": -1.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.1667, "angle": -2.26, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -1.01}]}, "ZONGKONG_ZHUTI75": {"rotate": [{"angle": -12.92, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -13.96, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 12.16, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": 8.82, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 2.55, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.8333, "angle": -4.33, "curve": 0.363, "c2": 0.44, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": -12.92, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": -13.96, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 12.16, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": 8.82, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 5.8333, "angle": 2.55, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 6.1667, "angle": -4.33, "curve": 0.363, "c2": 0.44, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -12.92}]}, "ZONGKONG_ZHUTI74": {"rotate": [{"angle": -5.26, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "angle": 6.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": 1.78, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.5, "angle": -1.37, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.8333, "angle": -4.24, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1667, "angle": -5.77, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": -5.26, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "angle": 6.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": 1.78, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.8333, "angle": -1.37, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 6.1667, "angle": -4.24, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.5, "angle": -5.77, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": -5.26}]}, "ZONGKONG_ZHUTI72": {"rotate": [{"angle": -12.85, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "angle": 12.16, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": 2.55, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.5, "angle": -4.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.1667, "angle": -13.96, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": -12.85, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "angle": 12.16, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": 2.55, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.8333, "angle": -4.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.5, "angle": -13.96, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": -12.85}]}, "ZONGKONG_ZHUTI71": {"rotate": [{"angle": -2.87, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": 6.19, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": -1.37, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.5, "angle": -4.24, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.8333, "angle": -5.77, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -2.87, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "angle": 6.19, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": -1.37, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.8333, "angle": -4.24, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.1667, "angle": -5.77, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -2.87}]}, "ZONGKONG_ZHUTI70": {"rotate": [{"angle": 0.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": 2.9, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": -1.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -2.26, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 0.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": 2.9, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -1.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -2.26, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.32}]}, "ZONGKONG_ZHUTI76": {"rotate": [{"angle": -1.16, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.5, "angle": 0.9, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": 1.64, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": -3.28, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -4.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -1.16, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 3.8333, "angle": 0.9, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.1667, "angle": 1.64, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -3.28, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -4.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -1.16}]}, "ZONGKONG_ZHUTI78": {"rotate": [{"angle": -10.28, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": -3.58, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5, "angle": 9.33, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": 1.8, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.5, "angle": -3.58, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.1667, "angle": -11.09, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": -10.28, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 3.8333, "angle": -3.58, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8333, "angle": 9.33, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": 1.8, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.8333, "angle": -3.58, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.5, "angle": -11.09, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": -10.28}]}, "ZONGKONG_ZHUTI77": {"rotate": [{"angle": -5.31, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -0.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1667, "angle": 3.38, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": -3.92, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.5, "angle": -6.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.8333, "angle": -8.1, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -5.31, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 3.8333, "angle": -0.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.5, "angle": 3.38, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": -3.92, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.8333, "angle": -6.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.1667, "angle": -8.1, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -5.31}]}, "ZONGKONG_ZHUTI79": {"rotate": [{"angle": -2.74, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 1.1667, "angle": -0.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -1.06, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.5, "angle": -1.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.1667, "angle": -2.87, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": -2.74, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 4.5, "angle": -0.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.8333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -1.06, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.8333, "angle": -1.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.5, "angle": -2.87, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": -2.74}]}, "ZONGKONG_ZHUTI81": {"rotate": [{"angle": -9.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -12.95, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.1667, "angle": -8.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.1667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.5, "angle": -1.69, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "angle": -9.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -12.95, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.5, "angle": -8.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.5, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.8333, "angle": -1.69, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 6.6667, "angle": -9.78}]}, "ZONGKONG_ZHUTI80": {"rotate": [{"angle": -7.56, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -7.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.1667, "angle": -2.9, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.8333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -1.01, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": -2.9, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 3.3333, "angle": -7.56, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": -7.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.5, "angle": -2.9, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -1.01, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 5.8333, "angle": -2.9, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 6.6667, "angle": -7.56}]}, "ZONGKONG_ZHUTI82": {"rotate": [{"angle": -0.67, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.71}, {"time": 0.1667, "angle": -0.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.8333, "angle": -2.43, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.1667, "angle": -2.8, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -0.67, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.71}, {"time": 3.5, "angle": -0.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.8333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.1667, "angle": -2.43, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.5, "angle": -2.8, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.67}]}, "ZONGKONG_ZHUTI83": {"rotate": [{"angle": -3.39, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": -2.49, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.8333, "angle": -4.29, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.1667, "angle": -5.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -6.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -3.39, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 3.5, "angle": -2.49, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.1667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.1667, "angle": -4.29, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.5, "angle": -5.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -6.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -3.39}]}, "ZONGKONG_ZHUTI84": {"rotate": [{"angle": -9.4, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "angle": -7.82, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.8333, "angle": -4.55, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.1667, "angle": -7.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": -12.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -9.4, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 3.5, "angle": -7.82, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.1667, "angle": -4.55, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.5, "angle": -7.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": -12.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -9.4}]}, "ZONGKONG_ZHUTI56": {"rotate": [{"angle": 0.32, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.5, "angle": 1.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": 2.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": -1.39, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -1.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 0.32, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 3.8333, "angle": 1.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.1667, "angle": 2.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -1.39, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -1.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.32}]}, "ZONGKONG_ZHUTI58": {"rotate": [{"angle": -9.33, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": -2.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5, "angle": 9.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": 2.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": -10.1, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": -9.33, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 3.8333, "angle": -2.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8333, "angle": 9.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": 2.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": -10.1, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": -9.33}]}, "ZONGKONG_ZHUTI57": {"rotate": [{"angle": -2.46, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 1.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1667, "angle": 4.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": -1.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": -4.79, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -2.46, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 3.8333, "angle": 1.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.5, "angle": 4.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": -1.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": -4.79, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -2.46}]}, "ZONGKONG_ZHUTI55": {"rotate": [{"angle": -5.43, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "angle": -2.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1667, "angle": 9.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": -2.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": -10.1, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": -5.43, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 3.5, "angle": -2.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.5, "angle": 9.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": -2.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": -10.1, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": -5.43}]}, "ZONGKONG_ZHUTI54": {"rotate": [{"angle": 0.01, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": 1.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8333, "angle": 4.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": -3.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 0.01, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 3.5, "angle": 1.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.1667, "angle": 4.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -3.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.01}]}, "ZONGKONG_ZHUTI53": {"rotate": [{"angle": 1.49, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.71}, {"time": 0.1667, "angle": 1.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -1.99, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 1.49, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.71}, {"time": 3.5, "angle": 1.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.8333, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -1.99, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 1.49}]}, "ZONGKONG_ZHUTI52": {"rotate": [{"angle": -0.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": 9.27, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": -7.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -10.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.1667, "angle": -2.97, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 3.3333, "angle": -0.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": 9.27, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -7.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": -10.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.5, "angle": -2.97, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 6.6667, "angle": -0.43}]}, "ZONGKONG_ZHUTI51": {"rotate": [{"angle": 2.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -4.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.1667, "angle": 1.27, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "angle": 2.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -4.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.5, "angle": 1.27, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 6.6667, "angle": 2.48}]}, "ZONGKONG_ZHUTI50": {"rotate": [{"angle": 2.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.99, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -1.39, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 3.1667, "angle": 1.99, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 3.3333, "angle": 2.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -1.99, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -1.39, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 6.5, "angle": 1.99, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 6.6667, "angle": 2.4}]}, "ZONGKONG_ZHUTI49": {"rotate": [{"angle": 4.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -10.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.8333, "angle": -2.97, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": 4.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -10.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.1667, "angle": -2.97, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": 4.57}]}, "ZONGKONG_ZHUTI48": {"rotate": [{"angle": 4.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -4.79, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -3.55, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 2.8333, "angle": 1.27, "curve": 0.363, "c2": 0.44, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": 4.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -4.79, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -3.55, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 6.1667, "angle": 1.27, "curve": 0.363, "c2": 0.44, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": 4.41}]}, "ZONGKONG_ZHUTI47": {"rotate": [{"angle": 2.39, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "angle": -1.99, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -0.28, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 2.8333, "angle": 1.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1667, "angle": 2.58, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 2.39, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "angle": -1.99, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -0.28, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 6.1667, "angle": 1.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.5, "angle": 2.58, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 2.39}]}, "ZONGKONG_ZHUTI46": {"rotate": [{"angle": 8.49, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -10.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -7.62, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": -2.97, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 3.3333, "angle": 8.49, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -10.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -7.62, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 5.8333, "angle": -2.97, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 6.6667, "angle": 8.49}]}, "ZONGKONG_ZHUTI45": {"rotate": [{"angle": 4.39, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "angle": -4.79, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -1.25, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.5, "angle": 1.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.1667, "angle": 4.79, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 4.39, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "angle": -4.79, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -1.25, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 5.8333, "angle": 1.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.5, "angle": 4.79, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 4.39}]}, "ZONGKONG_ZHUTI44": {"rotate": [{"angle": 1.47, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": -1.99, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": 0.92, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.5, "angle": 1.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.8333, "angle": 2.58, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 1.47, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "angle": -1.99, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": 0.92, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.8333, "angle": 1.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.1667, "angle": 2.58, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 1.47}]}, "ZONGKONG_ZHUTI42": {"rotate": [{"angle": -4.44}, {"time": 0.5, "angle": -6.34}, {"time": 2.1667}, {"time": 3.3333, "angle": -4.44}, {"time": 3.8333, "angle": -6.34}, {"time": 5.5}, {"time": 6.6667, "angle": -4.44}], "translate": [{"x": -5.53, "y": -2.01}, {"time": 0.5, "x": -7.91, "y": -2.88}, {"time": 2.1667}, {"time": 3.3333, "x": -5.53, "y": -2.01}, {"time": 3.8333, "x": -7.91, "y": -2.88}, {"time": 5.5}, {"time": 6.6667, "x": -5.53, "y": -2.01}]}, "ZONGKONG_ZHUTI39": {"rotate": [{"angle": 6.79, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 8.96, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 6.79, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": 8.96, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 6.79}]}, "ZONGKONG_ZHUTI33": {"translate": [{"x": 40.94, "y": -3.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "x": 73.43, "y": 0.63, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": -60.68, "y": -15.31, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "x": 40.94, "y": -3.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "x": 73.43, "y": 0.63, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -60.68, "y": -15.31, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "x": 40.94, "y": -3.23}]}, "ZONGKONG_ZHUTI30": {"rotate": [{"angle": 9.56, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "angle": -20.24, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -8.79, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": 10.88, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 9.56, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "angle": -20.24, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -8.79, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": 10.88, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 9.56}], "translate": [{"x": 9.33, "y": 8.52, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "x": -10.49, "y": -6.18, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "x": -2.88, "y": -0.54, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "x": 10.21, "y": 9.17, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "x": 9.33, "y": 8.52, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "x": -10.49, "y": -6.18, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "x": -2.88, "y": -0.54, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "x": 10.21, "y": 9.17, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "x": 9.33, "y": 8.52}]}, "ZONGKONG_ZHUTI29": {"rotate": [{"angle": 3.34, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": -20.24, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": -0.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": 10.88, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 3.34, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "angle": -20.24, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": -0.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": 10.88, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 3.34}], "translate": [{"x": 5.2, "y": 5.45, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "x": -10.49, "y": -6.18, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "x": 2.59, "y": 3.52, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "x": 10.21, "y": 9.17, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "x": 5.2, "y": 5.45, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "x": -10.49, "y": -6.18, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "x": 2.59, "y": 3.52, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "x": 10.21, "y": 9.17, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "x": 5.2, "y": 5.45}]}, "ZONGKONG_ZHUTI28": {"rotate": [{"angle": -4.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": -20.24, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": 6.83, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": 10.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -4.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": -20.24, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": 6.83, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": 10.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -4.68}], "translate": [{"x": -0.14, "y": 1.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "x": -10.49, "y": -6.18, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "x": 7.52, "y": 7.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "x": 10.21, "y": 9.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "x": -0.14, "y": 1.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "x": -10.49, "y": -6.18, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "x": 7.52, "y": 7.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "x": 10.21, "y": 9.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": -0.14, "y": 1.49}]}, "bone5": {"rotate": [{"angle": 15.54, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 17.09}, {"time": 1.8333, "angle": -18.59, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "angle": -13.95, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 3.3333, "angle": 15.54, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": 17.09}, {"time": 5.1667, "angle": -18.59, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -13.95, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 6.6667, "angle": 15.54}], "translate": [{"x": 9.67, "y": 5.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "x": 10.51, "y": 5.61, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -8.7, "y": -4.02, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.1667, "x": -6.2, "y": -2.77, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 3.3333, "x": 9.67, "y": 5.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "x": 10.51, "y": 5.61, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -8.7, "y": -4.02, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "x": -6.2, "y": -2.77, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 6.6667, "x": 9.67, "y": 5.19}]}, "bone4": {"rotate": [{"angle": 13.52}, {"time": 1.5, "angle": -18.59, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "angle": -5.46, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": 17.09}, {"time": 3.3333, "angle": 13.52}, {"time": 4.8333, "angle": -18.59, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -5.46, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": 17.09}, {"time": 6.6667, "angle": 13.52}], "translate": [{"x": 9.69, "y": 5.2, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "x": -8.7, "y": -4.02, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.1667, "x": -1.64, "y": -0.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "x": 10.51, "y": 5.61, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "x": 9.69, "y": 5.2, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "x": -8.7, "y": -4.02, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "x": -1.64, "y": -0.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "x": 10.51, "y": 5.61, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "x": 9.69, "y": 5.2}]}, "bone3": {"rotate": [{"angle": 6.39}, {"time": 1.1667, "angle": -18.59, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": 3.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": 17.09}, {"time": 3.3333, "angle": 6.39}, {"time": 4.5, "angle": -18.59, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": 3.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": 17.09}, {"time": 6.6667, "angle": 6.39}], "translate": [{"x": 5.86, "y": 3.28, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "x": -8.7, "y": -4.02, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "x": 3.44, "y": 2.07, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "x": 10.51, "y": 5.61, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "x": 5.86, "y": 3.28, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "x": -8.7, "y": -4.02, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "x": 3.44, "y": 2.07, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "x": 10.51, "y": 5.61, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "x": 5.86, "y": 3.28}]}, "bone2": {"rotate": [{"angle": -0.75}, {"time": 0.8333, "angle": -18.59, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": 12.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": 17.09}, {"time": 3.3333, "angle": -0.75}, {"time": 4.1667, "angle": -18.59, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": 12.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": 17.09}, {"time": 6.6667, "angle": -0.75}], "translate": [{"x": 0.9, "y": 0.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "x": -8.7, "y": -4.02, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "x": 8.01, "y": 4.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "x": 10.51, "y": 5.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "x": 0.9, "y": 0.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "x": -8.7, "y": -4.02, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "x": 8.01, "y": 4.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "x": 10.51, "y": 5.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": 0.9, "y": 0.8}]}, "ZONGKONG_ZHUTI11": {"rotate": [{"angle": -0.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": -0.81, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": 0.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": 0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -0.01}, {"time": 4.1667, "angle": -0.81, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": 0.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.8333, "angle": 0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -0.01}]}, "ZONGKONG_ZHUTI12": {"rotate": [{"angle": 1.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": -2.64, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1667, "angle": 0.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8333, "angle": 2.4, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 1.18}, {"time": 4.5, "angle": -2.64, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": 0.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1667, "angle": 2.4, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 1.18}]}, "ZONGKONG_ZHUTI38": {"translate": [{"x": 5.13, "y": -14.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.92, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 5.13, "y": -14.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -0.92, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 5.13, "y": -14.57}]}, "bone": {"translate": [{"x": 2.69, "y": 3.73}]}}, "deform": {"default": {"EYE_R2": {"EYE_R": [{}, {"time": 1.1667, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 1.2, "curve": "stepped"}, {"time": 3.3333}, {"time": 4.5, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 4.5333}]}, "EYE_L6": {"EYE_L": [{"vertices": [9.10086, -36.24181, 33.33053, 16.86495, 35.63379, -11.09738, 35.95928, 9.95061, 11.68973, -46.50773, 42.76752, 21.65667, 45.73479, -14.22252, 46.14155, 12.79143, 10.33301, -41.1545, 37.84933, 19.14872, 40.46149, -12.60777, 40.8348, 11.29325, 8.03223, -32.25176, 29.68666, 14.92355, 31.6589, -9.99491, 32.02445, 8.71819, 5.58468, -22.71302, 20.93415, 10.41769, 22.24065, -7.16553, 22.57888, 5.99121, 2.9721, -12.53169, 11.59221, 5.60854, 12.18701, -4.14711, 12.49685, 3.08147, 0.63225, -2.75058, 2.55167, 1.20884, 2.66085, -0.94371, 2.74935, 0.63853, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57268, -2.49433, 2.31388, 1.09584, 2.41316, -0.85554, 2.49318, 0.57994, 1.48842, -6.45868, 5.991, 2.83677, 6.2484, -2.21366, 6.45609, 1.49972, 3.41546, -14.20319, 13.12038, 6.41713, 13.84835, -4.61726, 14.14695, 3.58873, 5.10449, -20.71527, 19.08892, 9.51493, 20.29241, -6.51786, 20.58924, 5.48465, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.42453, -1.85144, 1.71765, 0.8132, 1.7915, -0.63532, 1.85061, 0.42983, 2.5127, -10.73232, 9.94032, 4.7613, 10.4126, -3.60965, 10.7143, 2.57227, 6.09975, -24.6783, 22.73362, 11.35937, 24.18896, -7.73089, 24.52126, 6.5731]}, {"time": 0.5, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 0.5333, "curve": "stepped"}, {"time": 2.6667}, {"time": 3.3333, "vertices": [9.10086, -36.24181, 33.33053, 16.86495, 35.63379, -11.09738, 35.95928, 9.95061, 11.68973, -46.50773, 42.76752, 21.65667, 45.73479, -14.22252, 46.14155, 12.79143, 10.33301, -41.1545, 37.84933, 19.14872, 40.46149, -12.60777, 40.8348, 11.29325, 8.03223, -32.25176, 29.68666, 14.92355, 31.6589, -9.99491, 32.02445, 8.71819, 5.58468, -22.71302, 20.93415, 10.41769, 22.24065, -7.16553, 22.57888, 5.99121, 2.9721, -12.53169, 11.59221, 5.60854, 12.18701, -4.14711, 12.49685, 3.08147, 0.63225, -2.75058, 2.55167, 1.20884, 2.66085, -0.94371, 2.74935, 0.63853, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57268, -2.49433, 2.31388, 1.09584, 2.41316, -0.85554, 2.49318, 0.57994, 1.48842, -6.45868, 5.991, 2.83677, 6.2484, -2.21366, 6.45609, 1.49972, 3.41546, -14.20319, 13.12038, 6.41713, 13.84835, -4.61726, 14.14695, 3.58873, 5.10449, -20.71527, 19.08892, 9.51493, 20.29241, -6.51786, 20.58924, 5.48465, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.42453, -1.85144, 1.71765, 0.8132, 1.7915, -0.63532, 1.85061, 0.42983, 2.5127, -10.73232, 9.94032, 4.7613, 10.4126, -3.60965, 10.7143, 2.57227, 6.09975, -24.6783, 22.73362, 11.35937, 24.18896, -7.73089, 24.52126, 6.5731]}, {"time": 3.8333, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 3.8667, "curve": "stepped"}, {"time": 6}, {"time": 6.6667, "vertices": [9.10086, -36.24181, 33.33053, 16.86495, 35.63379, -11.09738, 35.95928, 9.95061, 11.68973, -46.50773, 42.76752, 21.65667, 45.73479, -14.22252, 46.14155, 12.79143, 10.33301, -41.1545, 37.84933, 19.14872, 40.46149, -12.60777, 40.8348, 11.29325, 8.03223, -32.25176, 29.68666, 14.92355, 31.6589, -9.99491, 32.02445, 8.71819, 5.58468, -22.71302, 20.93415, 10.41769, 22.24065, -7.16553, 22.57888, 5.99121, 2.9721, -12.53169, 11.59221, 5.60854, 12.18701, -4.14711, 12.49685, 3.08147, 0.63225, -2.75058, 2.55167, 1.20884, 2.66085, -0.94371, 2.74935, 0.63853, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57268, -2.49433, 2.31388, 1.09584, 2.41316, -0.85554, 2.49318, 0.57994, 1.48842, -6.45868, 5.991, 2.83677, 6.2484, -2.21366, 6.45609, 1.49972, 3.41546, -14.20319, 13.12038, 6.41713, 13.84835, -4.61726, 14.14695, 3.58873, 5.10449, -20.71527, 19.08892, 9.51493, 20.29241, -6.51786, 20.58924, 5.48465, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.42453, -1.85144, 1.71765, 0.8132, 1.7915, -0.63532, 1.85061, 0.42983, 2.5127, -10.73232, 9.94032, 4.7613, 10.4126, -3.60965, 10.7143, 2.57227, 6.09975, -24.6783, 22.73362, 11.35937, 24.18896, -7.73089, 24.52126, 6.5731]}]}, "EYE_R3": {"EYE_R": [{"time": 0.6667}, {"time": 1.8333, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 1.8667, "curve": "stepped"}, {"time": 4}, {"time": 5.1667, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 5.2}]}, "EYE_L2": {"EYE_L": [{}, {"time": 1.1667, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 1.2, "curve": "stepped"}, {"time": 3.3333}, {"time": 4.5, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 4.5333}]}, "EYE_R4": {"EYE_R": [{"time": 1.3333}, {"time": 2.5, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 2.5333, "curve": "stepped"}, {"time": 4.6667}, {"time": 5.8333, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 5.8667}]}, "EYE_L5": {"EYE_L": [{"time": 2}, {"time": 3.1667, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 3.2, "curve": "stepped"}, {"time": 5.3333}, {"time": 6.5, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 6.5333}]}, "EYE_L4": {"EYE_L": [{"time": 1.3333}, {"time": 2.5, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 2.5333, "curve": "stepped"}, {"time": 4.6667}, {"time": 5.8333, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 5.8667}]}, "Y2": {"Y": [{}, {"time": 0.8333, "vertices": [-8.87782, 42.86523, 3.7737, 11.20532, -34.0757, 2.94897, -27.24434, 38.74243]}, {"time": 1.5, "vertices": [30.66064, -360.43546, -1.05054, 14.0022, -19.60619, -1.09009, 232.88359, -295.74487]}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.1667}, {"time": 4.8333, "vertices": [30.66064, -360.43546, -1.05054, 14.0022, -19.60619, -1.09009, 232.88359, -295.74487]}]}, "EYE_L3": {"EYE_L": [{"time": 0.6667}, {"time": 1.8333, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 1.8667, "curve": "stepped"}, {"time": 4}, {"time": 5.1667, "vertices": [15.92651, -63.42316, 58.32843, 29.51367, 62.35913, -19.42041, 62.92874, 17.41357, 20.45703, -81.38853, 74.84317, 37.89917, 80.03589, -24.8894, 80.74771, 22.38501, 18.08276, -72.02038, 66.23633, 33.51025, 70.80762, -22.0636, 71.46091, 19.76318, 14.0564, -56.44059, 51.95166, 26.11621, 55.40308, -17.49109, 56.04279, 15.25684, 9.77319, -39.74779, 36.63477, 18.23096, 38.92114, -12.53967, 39.51305, 10.48462, 5.20117, -21.93047, 20.28638, 9.81494, 21.32727, -7.25745, 21.86949, 5.39258, 1.10645, -4.81351, 4.46542, 2.11548, 4.65649, -1.65149, 4.81137, 1.11743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0022, -4.36507, 4.04929, 1.91772, 4.22302, -1.49719, 4.36307, 1.01489, 2.60474, -11.30269, 10.48425, 4.96436, 10.93469, -3.8739, 11.29816, 2.62451, 5.97705, -24.85559, 22.96066, 11.22998, 24.23462, -8.0802, 24.75716, 6.28027, 8.93286, -36.25173, 33.40561, 16.65112, 35.51172, -11.40625, 36.03117, 9.59814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74292, -3.24002, 3.00589, 1.4231, 3.13513, -1.11182, 3.23857, 0.7522, 4.39722, -18.78156, 17.39557, 8.33228, 18.22205, -6.31689, 18.75003, 4.50146, 10.67456, -43.18703, 39.78384, 19.87891, 42.33069, -13.52905, 42.9122, 11.50293]}, {"time": 5.2}]}, "Y": {"Y": [{}, {"time": 0.8333, "vertices": [-8.87782, 42.86523, 3.7737, 11.20532, -34.0757, 2.94897, -27.24434, 38.74243]}, {"time": 1.5, "vertices": [9.02658, -380.13928, -1.05054, 14.0022, -19.60619, -1.09009, 204.09955, -305.10483]}, {"time": 3.3333}, {"time": 4.1667, "vertices": [-8.87782, 42.86523, 3.7737, 11.20532, -34.0757, 2.94897, -27.24434, 38.74243]}, {"time": 4.8333, "vertices": [9.02658, -380.13928, -1.05054, 14.0022, -19.60619, -1.09009, 204.09955, -305.10483]}]}, "EYE_R6": {"EYE_R": [{"vertices": [26.89369, 30.76018, 32.26536, 36.96233, 33.69209, 38.75948, 32.20227, 36.98884, 48.28634, -8.57757, 29.28814, 33.54213, 43.83433, -7.8517, 25.41183, 29.03878, 37.97642, -6.84668, 38.26674, -5.01346, 21.87029, 24.94127, 32.64257, -5.91525, 32.89599, -4.34124, 26.41783, -4.83036, 26.62493, -3.55859, 13.01521, -2.37681, 13.1204, -1.75237, 11.83859, 5.89955, 2.24519, -0.31006, 2.02699, 1.00237, 1.08064, -1.98003, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.36858, -1.16643, 6.42083, -0.86642, 5.79469, 2.87835, 3.12151, -5.66073, 14.10889, -2.57729, 14.22259, -1.90011, 12.83428, 6.39969, 6.95061, -12.53625, 14.24018, 16.19001, 21.20891, -3.8757, 21.37849, -2.85554, 19.29567, 9.61356, 16.77978, 19.03125, 24.95424, -4.59326, 25.15297, -3.39181, 22.4024, 25.5572, 33.44447, -6.05699, 3.69988, 1.83677, 14.79925, -1.97621, 23.1589, -4.28411, 19.70869, 22.42913, 29.37674, -5.35317, 22.94699, 26.18443, 34.26339, -6.20145, 28.99179, 33.18206]}, {"time": 0.5, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 0.5333, "curve": "stepped"}, {"time": 2.6667}, {"time": 3.3333, "vertices": [26.89369, 30.76018, 32.26536, 36.96233, 33.69209, 38.75948, 32.20227, 36.98884, 48.28634, -8.57757, 29.28814, 33.54213, 43.83433, -7.8517, 25.41183, 29.03878, 37.97642, -6.84668, 38.26674, -5.01346, 21.87029, 24.94127, 32.64257, -5.91525, 32.89599, -4.34124, 26.41783, -4.83036, 26.62493, -3.55859, 13.01521, -2.37681, 13.1204, -1.75237, 11.83859, 5.89955, 2.24519, -0.31006, 2.02699, 1.00237, 1.08064, -1.98003, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.36858, -1.16643, 6.42083, -0.86642, 5.79469, 2.87835, 3.12151, -5.66073, 14.10889, -2.57729, 14.22259, -1.90011, 12.83428, 6.39969, 6.95061, -12.53625, 14.24018, 16.19001, 21.20891, -3.8757, 21.37849, -2.85554, 19.29567, 9.61356, 16.77978, 19.03125, 24.95424, -4.59326, 25.15297, -3.39181, 22.4024, 25.5572, 33.44447, -6.05699, 3.69988, 1.83677, 14.79925, -1.97621, 23.1589, -4.28411, 19.70869, 22.42913, 29.37674, -5.35317, 22.94699, 26.18443, 34.26339, -6.20145, 28.99179, 33.18206]}, {"time": 3.8333, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 3.8667, "curve": "stepped"}, {"time": 6}, {"time": 6.6667, "vertices": [26.89369, 30.76018, 32.26536, 36.96233, 33.69209, 38.75948, 32.20227, 36.98884, 48.28634, -8.57757, 29.28814, 33.54213, 43.83433, -7.8517, 25.41183, 29.03878, 37.97642, -6.84668, 38.26674, -5.01346, 21.87029, 24.94127, 32.64257, -5.91525, 32.89599, -4.34124, 26.41783, -4.83036, 26.62493, -3.55859, 13.01521, -2.37681, 13.1204, -1.75237, 11.83859, 5.89955, 2.24519, -0.31006, 2.02699, 1.00237, 1.08064, -1.98003, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.36858, -1.16643, 6.42083, -0.86642, 5.79469, 2.87835, 3.12151, -5.66073, 14.10889, -2.57729, 14.22259, -1.90011, 12.83428, 6.39969, 6.95061, -12.53625, 14.24018, 16.19001, 21.20891, -3.8757, 21.37849, -2.85554, 19.29567, 9.61356, 16.77978, 19.03125, 24.95424, -4.59326, 25.15297, -3.39181, 22.4024, 25.5572, 33.44447, -6.05699, 3.69988, 1.83677, 14.79925, -1.97621, 23.1589, -4.28411, 19.70869, 22.42913, 29.37674, -5.35317, 22.94699, 26.18443, 34.26339, -6.20145, 28.99179, 33.18206]}]}, "EYE_R5": {"EYE_R": [{"time": 2}, {"time": 3.1667, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 3.2, "curve": "stepped"}, {"time": 5.3333}, {"time": 6.5, "vertices": [47.06396, 53.83032, 56.46439, 64.68408, 58.96115, 67.8291, 56.35397, 64.73047, 84.5011, -15.01074, 51.25424, 58.69873, 76.71008, -13.74048, 44.4707, 50.81787, 66.45874, -11.98169, 66.9668, -8.77356, 38.27301, 43.64722, 57.12451, -10.35168, 57.56799, -7.59717, 46.2312, -8.45312, 46.59363, -6.22754, 22.77661, -4.15942, 22.96069, -3.06665, 20.71753, 10.32422, 3.92908, -0.5426, 3.54724, 1.75415, 1.89111, -3.46506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.14502, -2.04126, 11.23645, -1.51624, 10.14072, 5.03711, 5.46265, -9.90628, 24.69055, -4.51025, 24.88953, -3.3252, 22.45999, 11.19946, 12.16357, -21.93845, 24.92032, 28.33252, 37.1156, -6.78247, 37.41235, -4.99719, 33.76743, 16.82373, 29.36462, 33.30469, 43.66992, -8.03821, 44.0177, -5.93567, 39.20419, 44.7251, 58.52783, -10.59973, 6.47479, 3.21436, 25.89868, -3.45837, 40.52808, -7.49719, 34.4902, 39.25098, 51.4093, -9.36804, 40.15723, 45.82275, 59.96094, -10.85254, 50.73563, 58.0686]}, {"time": 6.5333}]}, "EYELID": {"EYELID": [{}, {"time": 2.1667, "vertices": [-4.74194, 2.60345, -3.01587, -3.93161, -2.94238, -2.64957, 7.91577, 2.9959, 13.34497, 2.10728, 5.67627, 7.246, 0.95581, 3.28197, 3.91479, -2.16971, 7.18872, -9.27554, 7.99731, -9.70735, 4.95093, -7.44786, -1.86548, -5.82159, -4.74194, 2.60345, 3.06299, -1.23067, 1.84424, -1.86459, 5.07935, 4.38493, 9.77759, 1.66765]}, {"time": 2.3333, "vertices": [-6.83765, 0.43391, -7.14282, 0.45325, -7.28589, 0.33974, -7.3291, 0.39024, -7.31543, 0.47342, -7.26367, 0.50964, -7.19434, 0.48811, -6.80396, 0.48564, -6.71875, 0.50314, -6.66479, 0.47438, -6.63086, 0.41881, -6.64233, 0.35464, -6.67529, 0.31017, -6.69067, 0.40665, -6.74976, 0.44861, -7.24805, 0.46509, -7.29834, 0.4278]}, {"time": 2.5, "vertices": [-3.91479, 0.7464, -5.02319, 0.82138, -5.54419, 0.41214, -5.69873, 0.59647, -5.64624, 0.89801, -5.45898, 1.02847, -5.20679, 0.94907, -3.79004, 0.93318, -3.4812, 0.99562, -3.28394, 0.8902, -3.16797, 0.68781, -3.20459, 0.45514, -3.3291, 0.29428, -3.37427, 0.64482, -3.59448, 0.79781, -5.40381, 0.86665, -5.5835, 0.73196]}, {"time": 2.6667, "vertices": [-4.74194, 2.60345, -3.01587, -3.93161, -2.94238, -2.64957, 7.91577, 2.9959, 13.34497, 2.10728, 5.67627, 7.246, 0.95581, 3.28197, 3.91479, -2.16971, 7.18872, -9.27554, 7.99731, -9.70735, 4.95093, -7.44786, -1.86548, -5.82159, -4.74194, 2.60345, 3.06299, -1.23067, 1.84424, -1.86459, 5.07935, 4.38493, 9.77759, 1.66765]}, {"time": 3.3333}, {"time": 5.5, "vertices": [-4.74194, 2.60345, -3.01587, -3.93161, -2.94238, -2.64957, 7.91577, 2.9959, 13.34497, 2.10728, 5.67627, 7.246, 0.95581, 3.28197, 3.91479, -2.16971, 7.18872, -9.27554, 7.99731, -9.70735, 4.95093, -7.44786, -1.86548, -5.82159, -4.74194, 2.60345, 3.06299, -1.23067, 1.84424, -1.86459, 5.07935, 4.38493, 9.77759, 1.66765]}, {"time": 5.6667, "vertices": [-6.83765, 0.43391, -7.14282, 0.45325, -7.28589, 0.33974, -7.3291, 0.39024, -7.31543, 0.47342, -7.26367, 0.50964, -7.19434, 0.48811, -6.80396, 0.48564, -6.71875, 0.50314, -6.66479, 0.47438, -6.63086, 0.41881, -6.64233, 0.35464, -6.67529, 0.31017, -6.69067, 0.40665, -6.74976, 0.44861, -7.24805, 0.46509, -7.29834, 0.4278]}, {"time": 5.8333, "vertices": [-3.91479, 0.7464, -5.02319, 0.82138, -5.54419, 0.41214, -5.69873, 0.59647, -5.64624, 0.89801, -5.45898, 1.02847, -5.20679, 0.94907, -3.79004, 0.93318, -3.4812, 0.99562, -3.28394, 0.8902, -3.16797, 0.68781, -3.20459, 0.45514, -3.3291, 0.29428, -3.37427, 0.64482, -3.59448, 0.79781, -5.40381, 0.86665, -5.5835, 0.73196]}, {"time": 6, "vertices": [-4.74194, 2.60345, -3.01587, -3.93161, -2.94238, -2.64957, 7.91577, 2.9959, 13.34497, 2.10728, 5.67627, 7.246, 0.95581, 3.28197, 3.91479, -2.16971, 7.18872, -9.27554, 7.99731, -9.70735, 4.95093, -7.44786, -1.86548, -5.82159, -4.74194, 2.60345, 3.06299, -1.23067, 1.84424, -1.86459, 5.07935, 4.38493, 9.77759, 1.66765]}, {"time": 6.6667}]}}}}}}