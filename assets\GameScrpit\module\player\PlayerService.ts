import { instantiate, isValid, Label, Node, Prefab } from "cc";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import { ItemEnum } from "../../lib/common/ItemEnum";
import MsgMgr from "../../lib/event/MsgMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { FriendModule } from "../friend/FriendModule";
import { GoodsModule } from "../goods/GoodsModule";
import { HeroModule } from "../hero/HeroModule";
import { PetModule } from "../pet/PetModule";
import { PlayerEvent } from "./PlayerEvent";
import { PlayerModule } from "./PlayerModule";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import ResMgr from "../../lib/common/ResMgr";
import { ConfirmMsg } from "../../game/ui/UICostConfirm";
import { LangMgr } from "../../game/mgr/LangMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import StorageMgr, { StorageKeyEnum } from "db://assets/platform/src/StorageHelper";
import MsgEnum from "../../game/event/MsgEnum";
import { HdVipCardRouteItem } from "../hd_vipcard/HdVipCardRoute";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { PublicRouteName } from "./PlayerConstant";

const log = Logger.getLoger(LOG_LEVEL.WARN);
export class PlayerService {
  private _tickId: number;

  public init() {
    MsgMgr.on(PlayerEvent.ON_UIPLAYER_DATA_UPDATE, this.updatePopover, this);

    if (this._tickId) {
      TickerMgr.clearInterval(this._tickId);
    }
    this._tickId = TickerMgr.setInterval(3, this.updatePopover.bind(this), true);
  }

  /**
   * 红点更新方法
   */
  private updatePopover() {
    // 玩家当前等级
    const level = PlayerModule.data.getPlayerInfo().level;
    // 当前功德
    const gongDe = PlayerModule.data.getItemNum(ItemEnum.功德_5);
    // 升级需要功德
    const levelUpNeedGongDe = PlayerModule.data.getConfigLeaderData(level + 1).virtue || 0;

    let zhuJueShengJi = false;
    if (level < 60 && gongDe > 0 && gongDe >= levelUpNeedGongDe) {
      // 繁荣度
      const fanRongDu = PlayerModule.data.playerBattleAttrResponse.speed || 0;
      // 升级需要繁荣度
      const levelUpNeedFanRongDu = PlayerModule.data.getConfigLeaderData(level + 1).speed || 0;

      if (fanRongDu >= levelUpNeedFanRongDu) {
        zhuJueShengJi = true;
      }
    }

    // 主角满足升级条件，可以进行升级时，主角升级按钮上有红点提示
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_shengji.btn_go_lv_main.btn_post_lv.id, zhuJueShengJi);

    // 每日奖励（飞鸟）
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_award.id, !PlayerModule.data.ishadGetDailyTreasure());
  }

  /** 判断道具是否足够 */
  public checkitemEnought(costList: number[]): number[] {
    for (let i = 0; i < costList.length; i += 2) {
      let my = PlayerModule.data.getItemNum(costList[i]);
      let need = costList[i + 1];
      if (my < need) {
        return [costList[i], costList[i + 1]];
      }
    }
    return [];
  }

  // 道具是否存在
  public isItemExist(itemId: number): boolean {
    const configItem = GoodsModule.data.getConfigItem(itemId);

    // 1.资源
    // 2.道具
    // 3.战将
    // 5.珍兽
    // 6.挚友
    // 7.领地装扮
    // 8.坐骑道具
    // 9.活动道具
    // 10.武魂道具
    // 11.主角皮肤
    // 12.头像、气泡、称号、皮肤

    if (configItem.goodsType == 3) {
      // 3.战将
      return HeroModule.data.getHeroMessage(itemId) ? true : false;
    } else if (configItem.goodsType == 5) {
      // 5.珍兽
      return PetModule.data.getPet(itemId) ? true : false;
    } else if (configItem.goodsType == 6) {
      // 6.挚友
      return FriendModule.data.getFriendMessage(itemId) ? true : false;
    } else if (configItem.goodsType == 11) {
      // 11.主角皮肤
      return PlayerModule.data.skinMap[itemId] ? true : false;
    } else if (configItem.goodsType == 12) {
      // 12.头像、气泡、称号、皮肤
      if (!PlayerModule.data.headShowMap[itemId]) {
        if (!PlayerModule.data.skinMap[itemId]) {
          if (!PlayerModule.data.titleMap[itemId]) {
            return false;
          }
        }
      }
      return true;
    } else {
      return PlayerModule.data.getItemNum(itemId) > 0;
    }
  }

  /**创建玩家称号或者他人称号
   * @param titleTarget 称号的根节点
   * @param titleId 称号id
   * @param callback 回调
   */
  public async createTitle(titleTarget: Node, titleId: number, callback: Function = (node, db) => {}) {
    if (titleId == -1) {
      log.error("没有装备称号");
      return;
    }
    let db = JsonMgr.instance.jsonList.c_title[titleId];
    return await new Promise((resolve, reject) => {
      ResMgr.loadPrefab(db.res, (prefab: Prefab) => {
        if (isValid(titleTarget) == false) return;
        let node = instantiate(prefab);
        node.layer = titleTarget.layer;
        node.walk((val) => {
          val.layer = titleTarget.layer;
        });
        titleTarget.addChild(node);
        node.active = true;
        node.getChildByName("lab_title").getComponent(Label).string = db.name;
        callback(node, db);
        resolve(node);
      });
    });
  }

  /**判断商店的永久限购商品是否还处于可以购买的状态  true为可以购买，false为不可以购买
   * @param itemId 商店里的商品购买得到道c_item 道具 id
   */
  public isShopBuy(itemId: number): boolean {
    let info_c_item = JsonMgr.instance.getConfigItem(itemId);
    if (!info_c_item) {
      log.error("没有找到物品");
      return false;
    }

    let has = true;
    switch (info_c_item.goodsType) {
      case 1:
        if (PlayerModule.data.getItemNum(info_c_item.id) > 0) {
          has = false;
        }
        break;
      case 2:
        if (PlayerModule.data.getItemNum(info_c_item.id) > 0) {
          has = false;
        }
        break;
      case 3:
        if (HeroModule.data.getHeroMessage(info_c_item.id)) {
          has = false;
        }
        break;
      case 4:
        log.error("徒弟");
        break;
      case 5:
        if (PetModule.data.getPet(info_c_item.id)) {
          has = false;
        }
        break;
      case 6:
        if (FriendModule.data.getFriendMessage(info_c_item.id)) {
          has = false;
        }
        break;
      case 7:
        log.error("领地装扮");
        break;
      case 8:
        log.error("坐骑道具");
        break;
      case 9:
        log.error("活动道具");
        break;
      case 10:
        log.error("武魂道具");
        break;
      case 11:
        log.error("主角皮肤");
        break;
      case 12:
        log.error("头像气泡称号皮肤");
        break;
      default:
        break;
    }
    return has;
  }

  /**
   * 获取头像配置，如果没有找到则返回默认头像
   *
   * @param avatarId 头像ID
   * @returns 头像配置
   */
  public getAvatarConfig(avatarId: number) {
    let db = JsonMgr.instance.jsonList.c_headShow[avatarId];

    if (!db) {
      avatarId = PlayerModule.data.getPlayerInfo().sex == 1 ? 1000001 : 2000001;
      db = JsonMgr.instance.jsonList.c_headShow[avatarId];
    }
    return db;
  }

  /*
   * 判断是否拥有该权限
   * @param id 权限id
   */
  public hasRight(id: number): boolean {
    if (PlayerModule.data.rightMap[id]) {
      return true;
    }
    return false;
  }

  public changeFightSpeed() {
    let db = JsonMgr.instance.jsonList.c_leader;
    let list = Object.keys(db);
    let info = db[list[0]];

    let callback2 = () => {
      // 提示不能开启
      const msg: ConfirmMsg = {
        msg: LangMgr.txMsgCode(116, [info.fast2Battle], "提示"),
        okText: LangMgr.txMsgCode(140, [], "前往"),
      };

      UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
        if (resp?.ok) {
          UIMgr.instance.showDialog(HdVipCardRouteItem.UICardMain);
        }
      });
    };

    let callback3 = () => {
      // 提示不能开启
      const msg: ConfirmMsg = {
        msg: LangMgr.txMsgCode(116, [info.fast3Battle], "提示"),
        okText: LangMgr.txMsgCode(140, [], "前往"),
      };

      UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
        if (resp?.ok) {
          UIMgr.instance.showDialog(HdVipCardRouteItem.UICardMain);
        }
      });
    };

    if (PlayerModule.data.fightSpeed == 0) {
      if (PlayerModule.data.playerDataMsg.level >= info.fast2Battle || PlayerModule.data.rightMap[101] > 0) {
        PlayerModule.data.fightSpeed = 1;
      } else {
        callback2();
        // if (this._isTip == true) {
        //   this._isTip = false;
        //   TickerMgr.setTimeout(3, () => {
        //     this._isTip = true;
        //   });
        //   let str = `<color=#ffffff>主角</color><color=#eaac34>等级${info.fast2Battle}</color><color=#ffffff>或购买</color><color=#eaac34>特权卡</color><color=#ffffff>解锁</color>`;
        //   TipMgr.showTip(str);
        // }
      }
    } else if (PlayerModule.data.fightSpeed == 1) {
      if (PlayerModule.data.playerDataMsg.level >= info.fast3Battle || PlayerModule.data.rightMap[101] > 0) {
        PlayerModule.data.fightSpeed = 2;
      } else {
        // if (this._isTip == true) {
        //   this._isTip = false;
        //   TickerMgr.setTimeout(3, () => {
        //     this._isTip = true;
        //   });
        //   let str = `<color=#ffffff>主角</color><color=#eaac34>等级${info.fast3Battle}</color><color=#ffffff>或购买</color><color=#eaac34>特权卡</color><color=#ffffff>解锁</color>`;
        //   TipMgr.showTip(str);
        // }
        callback3();
        PlayerModule.data.fightSpeed = 0;
      }
    } else if (PlayerModule.data.fightSpeed == 2) {
      PlayerModule.data.fightSpeed = 0;
    }
    MsgMgr.emit(MsgEnum.ON_FIGHT_SPEED);
    StorageMgr.saveItem(StorageKeyEnum.GameSpeed, PlayerModule.data.fightSpeed + "");
  }
}
