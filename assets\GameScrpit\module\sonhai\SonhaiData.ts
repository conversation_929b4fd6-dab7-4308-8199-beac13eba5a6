import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { AchieveMessage, AdventureMessage } from "../../game/net/protocol/Activity";
import { CommIntegerListMessage } from "../../game/net/protocol/Comm";
import MsgMgr from "../../lib/event/MsgMgr";
import TipMgr from "../../lib/tips/TipMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import { AdventureVO } from "../activity/ActivityConfig";
import { SonhaiMsgEnum } from "./SonhaiConfig";
import { SonhaiModule } from "./SonhaiModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class SonhaiData {
  private _adventureMessage: AdventureMessage = null;
  public get adventureMessage(): AdventureMessage {
    if (!this._adventureMessage) {
      this._adventureMessage = {
        activityId: 0,
        achieveMap: {},
        /** 物料ID:兑换次数 */
        redeemMap: {},
        /** 物料ID:广告次数 */
        adMap: {},
        /** 解锁条件：值 */
        limitMap: {},
        /** 自选道具ID：选中的索引列表 */
        chosenMap: {},
        /** 本轮选中的大奖的ID 小于等于0表示未选中 */
        curBigPrizeId: 0,
        /** 中大奖的次数 */
        bigWinCount: 0,
        /** 当前轮次已经抽奖的次数 */
        curCostCount: 0,
        /** 大奖选中分布 */
        bigChosenMap: {},
        /** 累计探险次数的奖励领取索引结合 */
        achieveTakeList: [],
        /** 今日任务列表(为任务列表配置的索引) */
        taskIndexList: [],
        /** 今日任务完成数量 */
        targetValList: [],
        /** 今日任务领取奖励的列表 */
        taskTakeList: [],
      };
    }

    return this._adventureMessage;
  }

  public set adventureMessage(value: AdventureMessage) {
    this._adventureMessage = value;
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /** 中大奖的次数 */
  public get bigWinCount(): number {
    if (!this.adventureMessage) {
      return 0;
    }

    return this.adventureMessage.bigWinCount;
  }
  /** 中大奖的次数 */
  public set bigWinCount(value: number) {
    this.adventureMessage.bigWinCount = value;
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /**当前轮次已经抽奖的次数 */
  public get curCostCount(): number {
    return this.adventureMessage.curCostCount;
  }

  /**当前轮次已经抽奖的次数 */
  public set curCostCount(value: number) {
    this.adventureMessage.curCostCount = value;
    MsgMgr.emit(SonhaiMsgEnum.TOTAL_DRAW_COUNT_CHANGE);
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /**大奖选中分布 */
  public get bigChosenMap(): { [key: number]: number } {
    return this.adventureMessage.bigChosenMap;
  }
  /**大奖选中分布 */
  public set bigChosenMap(value: { [key: number]: number }) {
    this.adventureMessage.bigChosenMap = value;
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /**本轮选中的大奖的ID 小于等于0表示未选中 */
  public get curBigPrizeId(): number {
    return this.adventureMessage.curBigPrizeId;
  }
  /**本轮选中的大奖的ID 小于等于0表示未选中 */
  public set curBigPrizeId(value: number) {
    this.adventureMessage.curBigPrizeId = value;
    MsgMgr.emit(SonhaiMsgEnum.BIG_AWARD_ID_CHANGE);
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /**今日任务领取奖励的列表 */
  public get taskTakeList(): Array<number> {
    return this.adventureMessage.taskTakeList;
  }
  /**今日任务领取奖励的列表 */
  public set taskTakeList(value: Array<number>) {
    this.adventureMessage.taskTakeList = value;
    MsgMgr.emit(SonhaiMsgEnum.DAILY_ACTIVITY_TASK_CHANGE);
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /**今日任务完成数量 */
  public get targetValList(): Array<number> {
    return this.adventureMessage.targetValList;
  }
  /**今日任务完成数量 */
  public set targetValList(value: Array<number>) {
    this.adventureMessage.targetValList = value;
    MsgMgr.emit(SonhaiMsgEnum.SONHAI_LEIJI_REFRESH);
    MsgMgr.emit(SonhaiMsgEnum.DAILY_ACTIVITY_TASK_CHANGE);
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /**物料ID:兑换次数 */
  public get redeemMap(): { [key: number]: number } {
    return this.adventureMessage.redeemMap;
  }
  /**物料ID:兑换次数 */
  public set redeemMap(value: { [key: number]: number }) {
    this.adventureMessage.redeemMap = value;
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /** 物料ID:广告次数 */
  public get adMap(): { [key: number]: number } {
    return this.adventureMessage.adMap;
  }
  /** 物料ID:广告次数 */
  public set adMap(value: { [key: number]: number }) {
    this.adventureMessage.adMap = value;
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /** 自选道具ID：选中的索引列表 */
  public get chosenMap(): { [key: number]: CommIntegerListMessage } {
    return this.adventureMessage.chosenMap;
  }
  /** 自选道具ID：选中的索引列表 */
  public set chosenMap(value: { [key: number]: CommIntegerListMessage }) {
    this.adventureMessage.chosenMap = value;
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  /** 解锁条件：值 */
  public get limitMap(): { [key: number]: number } {
    return this.adventureMessage.limitMap;
  }
  /** 解锁条件：值 */
  public set limitMap(value: { [key: number]: number }) {
    this.adventureMessage.limitMap = value;
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
  }

  public getAchieveMap(id: number) {
    if (!this.adventureMessage.achieveMap[id]) {
      let data = {
        /** 成就互动ID */
        id: id,
        /** 当前成就值 */
        targetVal: 0,
        /** 是否支付 */
        paid: false,
        /** 已经领取基础奖励的列表 */
        basicTakeList: [],
        /** 已经领取奖励的列表 */
        paidTakeList: [],
      };
      this.adventureMessage.achieveMap[id] = data;
    }

    return this.adventureMessage.achieveMap[id];
  }

  public setAchieveMap(id: number, data: AchieveMessage) {
    this.adventureMessage.achieveMap[id] = data;
    MsgMgr.emit(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE);
    MsgMgr.emit(SonhaiMsgEnum.SONHAI_LEIJI_REFRESH);
  }

  private _adventureVO: AdventureVO;
  public async getSonhaiDb(activityId: number): Promise<AdventureVO> {
    return new Promise((resolve, reject) => {
      if (this._adventureVO) {
        resolve(this._adventureVO);
      } else {
        GameHttpApi.getActivityConfig(activityId).then((resp: any) => {
          if (resp.code != 200) {
            log.error(resp);
          }
          let db = JSON.parse(resp.msg);
          this._adventureVO = db;
          resolve(this._adventureVO);
        });
      }
    });
  }

  public upVO(id) {
    this._adventureVO = null;
    this.getSonhaiDb(id);

    let rotueTables = SonhaiModule.route.rotueTables;
    for (let i = 0; i < rotueTables.length; i++) {
      UIMgr.instance.closeByName(rotueTables[i].uiName);
    }
    TipMgr.showTip("活动内容已变更");
  }
}
