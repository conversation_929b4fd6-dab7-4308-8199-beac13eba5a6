import { _decorator, instantiate, Label, Sprite } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { JsonMgr } from "../../../../../game/mgr/JsonMgr";
import Formate from "../../../../../lib/utils/Formate";
import { FriendModule } from "../../../../friend/FriendModule";
import ResMgr from "../../../../../lib/common/ResMgr";
import { PlayerModule } from "../../../../player/PlayerModule";
import { IConfigPupil } from "../../PupilData";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIPupilRatePreview")
export class UIPupilRatePreview extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilRatePreview`;
  }

  protected onEvtShow(): void {
    this.getNode("node_tip").active = false;

    let friendShip = FriendModule.data.getTotalKarma();
    this.getNode("lbl_value").getComponent(Label).string = `${friendShip}`;

    let configMap: { [key: string]: IConfigPupil } = JsonMgr.instance.jsonList.c_pupil;
    let list = Object.keys(configMap);

    let configLeader = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level);

    let nodeLayout = this.getNode("layout");

    const weightList = [];
    for (const id in list) {
      if (friendShip >= configMap[list[id]].unlock) {
        weightList.push(configMap[list[id]].weight);
      } else {
        weightList.push(0);
      }
    }
    const percentageList = Formate.weightListToPercent(weightList, true);

    for (let idx in list) {
      let key = parseInt(idx);
      let configItem = configMap[list[key]];
      let nodeRow = nodeLayout.children[key];
      if (!nodeRow) {
        nodeRow = instantiate(nodeLayout.children[key % 2]);
        nodeLayout.addChild(nodeRow);
      }

      // 解锁的总因果值
      nodeRow.getChildByPath("cell1/lbl_hint").getComponent(Label).string = `${configItem.unlock}`;

      // 天资
      ResMgr.loadImage(
        `${BundleEnum.BUNDLE_G_PUPIL}?images/DZ_dizichenghao${configItem.id}`,
        nodeRow.getChildByPath("cell2/bg").getComponent(Sprite),
        this
      );

      // 锁定状态
      nodeRow.getChildByPath("cell2/node_lock").active = percentageList[idx] == 0;

      // 出现概率
      nodeRow.getChildByPath("cell3/lbl_rate").getComponent(Label).string = `${Formate.formatDecimal(
        percentageList[idx],
        2
      )}%`;

      // 天生属性
      nodeRow.getChildByPath("cell4/lbl_init_attr").getComponent(Label).string = `${configItem.firstNum}`;

      // 繁荣度
      nodeRow.getChildByPath("cell5/lbl_bloom").getComponent(Label).string = `${Formate.format(
        (configItem.rate * configItem.basicSpeed) / 10000
      )}`;

      // 阅历
      nodeRow.getChildByPath("cell6/lbl_yueli").getComponent(Label).string = `${Formate.format(
        (configLeader.trainReward * configItem.rewardAdd) / 10000
      )}`;
    }
  }

  private on_click_btn_value_from() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("node_tip").active = true;
  }

  private on_click_btn_close_tip() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("node_tip").active = false;
  }
}
