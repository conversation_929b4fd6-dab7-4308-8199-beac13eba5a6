// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Rank.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface CompetePointMessage {
  userId: number;
  name: string;
  level: number;
  vipLevel: number;
  avatar: string;
  point: number;
  /** 名次 */
  rank: number;
  totalPower: number;
}

function createBaseCompetePointMessage(): CompetePointMessage {
  return { userId: 0, name: "", level: 0, vipLevel: 0, avatar: "", point: 0, rank: 0, totalPower: 0 };
}

export const CompetePointMessage: MessageFns<CompetePointMessage> = {
  encode(message: CompetePointMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== 0) {
      writer.uint32(8).int64(message.userId);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.level !== 0) {
      writer.uint32(24).int32(message.level);
    }
    if (message.vipLevel !== 0) {
      writer.uint32(32).int32(message.vipLevel);
    }
    if (message.avatar !== "") {
      writer.uint32(42).string(message.avatar);
    }
    if (message.point !== 0) {
      writer.uint32(48).int64(message.point);
    }
    if (message.rank !== 0) {
      writer.uint32(56).int32(message.rank);
    }
    if (message.totalPower !== 0) {
      writer.uint32(65).double(message.totalPower);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompetePointMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompetePointMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.vipLevel = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.avatar = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.point = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.totalPower = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CompetePointMessage>, I>>(base?: I): CompetePointMessage {
    return CompetePointMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompetePointMessage>, I>>(object: I): CompetePointMessage {
    const message = createBaseCompetePointMessage();
    message.userId = object.userId ?? 0;
    message.name = object.name ?? "";
    message.level = object.level ?? 0;
    message.vipLevel = object.vipLevel ?? 0;
    message.avatar = object.avatar ?? "";
    message.point = object.point ?? 0;
    message.rank = object.rank ?? 0;
    message.totalPower = object.totalPower ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
