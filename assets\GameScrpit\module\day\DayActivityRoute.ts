import { UIDayRecharge } from "../../game/ui/ui_fund/UIDayRecharge";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum DayRouteItem {
  UIDayRecharge = "UIDayRecharge",
}
export class DayActivityRoute {
  rotueTables: Recording[] = [
    {
      node: UIDayRecharge,
      uiName: DayRouteItem.UIDayRecharge,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
