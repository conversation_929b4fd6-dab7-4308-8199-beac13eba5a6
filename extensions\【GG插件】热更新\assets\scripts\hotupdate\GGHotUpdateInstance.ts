import { native, path, sys } from "cc";
import { GGHotUpdateInstanceEnum, GGHotUpdateInstanceOption, GGHotUpdateInstanceState, ProjectManifest, ProjectManifestAssetUpdateState, VersionManifest } from "./GGHotUpdateType";
import { ggLogger } from "./GGLogger";
import { GGObserverSystem } from "./GGObserverSystem";

/**
 * 热更新实例观察者方法
 *
 * <AUTHOR>
 * @created 2024-08-30 10:40:53
 */
export interface GGHotUpdateInstanceObserver {
    /**
     * 热更新实例状态更新回调
     *
     * @param instance 热更新实例
     */
    onGGHotUpdateInstanceCallBack?(instance: GGHotUpdateInstance): void;
}

/**
 * 热更新实例
 *
 * <AUTHOR>
 * @created 2024-08-30 10:40:53
 */
export class GGHotUpdateInstance extends GGObserverSystem<GGHotUpdateInstanceObserver> {
    /**
     * 热更新的包名字
     *
     * * 主包: `GGHotUpdateInstanceEnum.BuildIn`
     * * 子包: 传 Bundle 名字
     */
    name: GGHotUpdateInstanceEnum | string;
    /**
     * 热更新实例配置
     */
    private _option: GGHotUpdateInstanceOption;
    /**
     * 热更新文件的远程根地址
     *
     * e.g.
     *
     * * Android:
     *      * 主包: ``http://192.168.0.1:8080/1.0.0``
     *      * 子包: ``http://192.168.0.1:8080/1.0.0/assets/${bundleName}``
     */
    private _remoteRootUrl: string;
    /**
     * 热更包的本地搜索根目录
     *
     * e.g.
     *
     * * Android: ``/data/user/0/com.cocos.game/files/gg-hot-update``
     */
    private _searchRootDirPath: string;
    /**
     * 热更包的本地下载根目录
     *
     * * 主包：如果确认热更新完毕，那么会在下次游戏启动时，将这个下载目录的内容移动到搜索目录
     * * 子包：如果确认热更新完毕，那么会在更新搜索路径时（updateSearchPath）， 将这个下载目录的内容移动到搜索目录
     *
     * e.g.
     *
     * * Android:
     *      * 主包: ``/data/user/0/${packageName}/files/gg-hot-update-temp/build-in``
     *      * 子包: ``/data/user/0/${packageName}/files/gg-hot-update-temp/${bundleName}``
     */
    private _downloadRootDirPath: string;
    /**
     * 远程 version.manifest 地址
     *
     * e.g.
     *
     * * Android:
     *      * 主包: ``http://192.168.0.1:8080/1.0.0/version.manifest``
     *      * 子包: ``http://192.168.0.1:8080/1.0.0/assets/${bundleName}/version.manifest``
     */
    private _versionManifesetRemoteUrl: string;
    /**
     * 远程 project.manifest 地址
     *
     * e.g.
     *
     * * Android:
     *      * 主包: ``http://192.168.0.1:8080/1.0.0/project.manifest``
     *      * 子包: ``http://192.168.0.1:8080/1.0.0/assets/${bundleName}/project.manifest``
     */
    private _projectManifesetRemoteUrl: string;
    /**
     * project.manifeset 的本地搜索路径
     *
     * e.g.
     *
     * * Android:
     *      * 主包: ``/data/user/0/${packageName}/files/gg-hot-update/project.manifest``
     *      * 子包: ``/data/user/0/${packageName}/files/gg-hot-update/assets/${bundleName}/project.manifst``
     */
    private _projectManifestLocalSearchPath: string;
    /**
     * project.manifeset 的本地下载路径
     *
     * e.g.
     *
     * * Android:
     *      * 主包: ``/data/user/0/${packageName}/files/gg-hot-update-temp/build-in/project.manifest``
     *      * 子包: ``/data/user/0/${packageName}/files/gg-hot-update-temp/${bundleName}/project.manifst``
     */
    private _projectManifestDownloadPath: string;
    /**
     * project.manifest 的包内路径
     *
     * e.g.
     *
     * * Android:
     *      * 主包: ``@assets/project.manifest``
     *      * 子包: ``@assets/assets/${bundleName}/project.manifest``
     */
    private _projectManifestBuildInPath: string;
    /**
     * 本地 project.manifest 配置
     */
    private _localProjectManifest: ProjectManifest | null;
    /**
     * 远端 project.manifeset 配置
     */
    private _remoteProjectManifest: ProjectManifest | null;
    /**
     * 下载任务管理器
     */
    private _downloader: native.Downloader;
    /**
     * 下载任务队列
     */
    private _downloadTasks: native.DownloadTask[];
    /**
     * 当前并行下载任务数量
     */
    private _curConcurrentTaskCount: number;

    /**
     * 当前热更新实例状态
     */
    get state(): GGHotUpdateInstanceState {
        return this._state;
    }
    private _state: GGHotUpdateInstanceState = GGHotUpdateInstanceState.Idle;

    // //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 下载信息

    /**
     * 待下载的总字节
     */
    get totalBytes(): number {
        return this._totalBytes;
    }
    private _totalBytes: number = 0;
    /**
     * 已下载的字节
     */
    get downloadedBytes(): number {
        return this._downloadedBytes;
    }
    private _downloadedBytes: number = 0;
    /**
     * 待下载的文件列表
     */
    get totalFiles(): number {
        return this._totalFiles;
    }
    private _totalFiles: number = 0;
    /**
     * 下载成功的文件列表
     */
    readonly downloadSucFiles: native.DownloadTask[] = [];
    /**
     * 下载失败的文件列表
     */
    readonly downloadFailedFiles: native.DownloadTask[] = [];
    /**
     * 热更新下载速度 Bytes/s
     */
    get downloadSpeedInSecond() {
        return this._downloadSpeed;
    }
    private _downloadSpeed: number = 0;
    /**
     * 下载剩余时间(s)
     *
     * * >=0: 已知剩余秒数
     * * <0: 未知剩余时间
     */
    get downloadRemainTimeInSecond() {
        return this._downloadRemainTimeInSecond;
    }
    private _downloadRemainTimeInSecond: number = -1;

    /**
     * @param name 热更新的包名字
     * @param remoteRootUrl 热更包的远程根地址 e.g. ``http://192.168.0.1:8080/1.0.0``
     * @param searchRootDirPath 热更包的本地搜索根目录 e.g. ``/data/user/0/${pacakgeName}/files/gg-hot-update``
     * @param option 热更新实例配置
     */
    constructor(name: GGHotUpdateInstanceEnum | string, remoteRootUrl: string, searchRootDirPath: string, option: GGHotUpdateInstanceOption) {
        super();
        this.name = name;
        this._option = option;
        this._remoteRootUrl = remoteRootUrl;
        this._searchRootDirPath = searchRootDirPath;
        this._downloadRootDirPath = path.join(this._searchRootDirPath + "-temp", this.name);
        if (this.name == GGHotUpdateInstanceEnum.BuildIn) {
            this._versionManifesetRemoteUrl = `${this._remoteRootUrl}/version.manifest`;
            this._projectManifesetRemoteUrl = `${this._remoteRootUrl}/project.manifest`;
            this._projectManifestLocalSearchPath = path.join(this._searchRootDirPath, "project.manifest");
            this._projectManifestDownloadPath = path.join(this._downloadRootDirPath, "project.manifest");
            this._projectManifestBuildInPath = `@assets/project.manifest`;
        } else {
            this._versionManifesetRemoteUrl = `${this._remoteRootUrl}/assets/${this.name}/version.manifest`;
            this._projectManifesetRemoteUrl = `${this._remoteRootUrl}/assets/${this.name}/project.manifest`;
            this._projectManifestLocalSearchPath = path.join(this._searchRootDirPath, "assets", this.name, "project.manifest");
            this._projectManifestDownloadPath = path.join(this._downloadRootDirPath, "assets", this.name, "project.manifest");
            this._projectManifestBuildInPath = `@assets/assets/${this.name}/project.manifest`;
        }
        this._localProjectManifest = null;
        this._remoteProjectManifest = null;
        this._downloader = new native.Downloader();
        this._downloadTasks = [];
        this._curConcurrentTaskCount = 0;
        this._state = GGHotUpdateInstanceState.Idle;
        this._resetDownloadInfo();
    }

    /**
     * 重置下载信息
     */
    private _resetDownloadInfo() {
        // 移除还没有开始的下载任务
        if (this._downloadTasks.length > 0) {
            this._downloadTasks.length = 0;
        }

        // 重置并行下载任务数
        this._curConcurrentTaskCount = 0;

        // 重置下载信息
        this._totalBytes = 0;
        this._downloadedBytes = 0;
        this._totalFiles = 0;
        this.downloadSucFiles.length = 0;
        this.downloadFailedFiles.length = 0;
        this._downloadSpeed = 0;
        this._downloadRemainTimeInSecond = -1;
    }

    /**
     * 更新状态
     */
    private _updateState(state: GGHotUpdateInstanceState) {
        this._state = state;
        this.observers.forEach((observer) => {
            observer.onGGHotUpdateInstanceCallBack?.(this);
        });
    }

    /**
     * 销毁实例
     *
     * 1. 实例销毁之后，没法再次使用
     * 2. 实例销毁的一般应用场合为，主包已经完成了热更新，在重启游戏之前，进行销毁
     */
    destroy() {
        // 移除所有外部观察者
        this.unregisterAll();

        // 重置属性
        this._remoteProjectManifest = null;
        this._localProjectManifest = null;

        // 移除下载回调监听
        this._downloader.onError = null!;
        this._downloader.onProgress = null!;
        this._downloader.onSuccess = null!;

        // 重置状态信息
        this._state = GGHotUpdateInstanceState.Idle;

        // 放弃进行中的下载任务
        if (this._downloadTasks.length > 0) {
            this._downloadTasks.forEach((task) => {
                this._downloader.abort(task);
            });
        }
        // 重置下载信息
        this._resetDownloadInfo();
    }

    /**
     * 清除下载缓存。
     *
     * 1. 清除下载缓存后，后续的检查更新、热更新都会重新下载所有文件
     * 2. 在在多次检查更新失败或者多次热更新失败后，可以考虑调用此方法，清除所有下载临时文件
     */
    clearDownloadCache() {
        // 部分状态下不可以删除下载缓存
        if (this._state == GGHotUpdateInstanceState.CheckUpdateInProgress) {
            this._warn("当前正在检查更新中，删除本地缓存失败");
            return;
        }
        if (this._state == GGHotUpdateInstanceState.HotUpdateInProgress) {
            this._warn("当前正在热更新中，删除本地缓存失败");
            return;
        }
        // 清除下载缓存目录
        if (native.fileUtils.isDirectoryExist(this._downloadRootDirPath)) {
            const suc = native.fileUtils.removeDirectory(this._downloadRootDirPath);
            this._debug(`当前存在本地缓存目录，删除${suc ? "成功" : "失败"}`);
            return;
        }
        this._debug(`当前不存在本地缓存目录`);
    }

    /**
     * 检查更新
     */
    checkUpdate() {
        if (this._state == GGHotUpdateInstanceState.CheckUpdateInProgress) {
            this._warn("检查更新：当前已经在检查新版本中。请不要重复调用 `checkUpdate`.");
            return;
        }

        if (this._state == GGHotUpdateInstanceState.HotUpdateInProgress) {
            this._warn("检查更新：当前已经在热更新中。请不要在此时调用 `checkUpdate`.");
            return;
        }

        // 更新状态
        this._debug(`检查更新：开始`);
        this._resetDownloadInfo();
        this._updateState(GGHotUpdateInstanceState.CheckUpdateInProgress);

        // 按照以下顺序，读取「此包」「本地最新版本」的 project.manifest 文件内容到内存中
        // 1. 本地搜索目录的 project.manifest
        // 2. 内置的 project.manifest
        const localProjectManifestPaths = [this._projectManifestLocalSearchPath, this._projectManifestBuildInPath];
        let localProjectManifestJsonText: string | null = null;
        this._debug(`检查更新：本地 project.manifest 文件搜索路径如下：${JSON.stringify(localProjectManifestPaths)}`);
        for (let localProjectManifestPath of localProjectManifestPaths) {
            this._debug(`检查更新：尝试从路径 ${localProjectManifestPath} 获取 project.manifest 信息：开始`);
            if (!native.fileUtils.isFileExist(localProjectManifestPath)) {
                this._debug(`检查更新：尝试从路径 ${localProjectManifestPath} 获取 project.manifest 信息：失败，文件不存在`);
                continue;
            }
            localProjectManifestJsonText = native.fileUtils.getStringFromFile(localProjectManifestPath);
            if (localProjectManifestJsonText) {
                try {
                    this._localProjectManifest = JSON.parse(localProjectManifestJsonText);
                    this._debug(`检查更新：尝试从路径 ${localProjectManifestPath} 获取 project.manifest 信息：成功`);
                } catch (error) {
                    this._error(`检查更新：尝试从路径 ${localProjectManifestPath} 获取 project.manifest 信息：失败，文件内容解析失败`);
                    this._error(error);
                    this._error(`检查更新：失败，解析本地 project.manifest 失败`);
                    this._updateState(GGHotUpdateInstanceState.CheckUpdateFailedParseLocalProjectManifestError);
                    return;
                }
            }
            if (this._localProjectManifest) {
                break;
            }
        }
        // 如果没有读取本地到 project.manifest 配置，那么可能是包的首次更新，此时生成一个默认空白配置，那么就会全量将包下载下来
        if (!this._localProjectManifest) {
            this._debug(`检查更新：没法解析到本地 project.manifest 配置，将初始化一个默认配置`);
            this._localProjectManifest = { version: "", assets: {} };
        }
        this._debug(`检查更新：解析本地 project.manifest：成功`);

        // fetch 获取远程包的 version.manifest 内容，然后和本地的 project.manifest 做版本比较
        fetch(this._versionManifesetRemoteUrl)
            .then((resp: Response) => {
                return resp.json();
            })
            .then((versionJson: VersionManifest | null) => {
                const localVersion = this._localProjectManifest!.version;
                const remoteVersion = versionJson?.version ?? "";

                this._debug(`检查更新：获取远程 version.manifest 内容成功，内容：${JSON.stringify(versionJson)}`);
                this._debug(`检查更新：当前本地版本: ${localVersion}`);
                this._debug(`检查更新：当前远端版本: ${remoteVersion}`);

                // 本地版本和远程版本比较
                const isNewVersionFound = remoteVersion != localVersion;

                // 未发现新版本
                if (!isNewVersionFound) {
                    this._debug(`检查更新：成功，当前已经是最新版本`);
                    // 释放文件json内存
                    this._localProjectManifest = null;
                    this._updateState(GGHotUpdateInstanceState.CheckUpdateSucAlreadyUpToDate);
                    return;
                }

                // 发现新版本
                // 如果本地已经下载好新版本的远程 project.manifest ，那么解析文件，并获取差异文件记录
                try {
                    if (native.fileUtils.isFileExist(this._projectManifestDownloadPath)) {
                        this._remoteProjectManifest = JSON.parse(native.fileUtils.getStringFromFile(this._projectManifestDownloadPath));
                        if (this._remoteProjectManifest) {
                            this._resetDownloadInfo();
                            Object.keys(this._remoteProjectManifest.assets).forEach((assetPath) => {
                                const remoteAssetInfo = this._remoteProjectManifest!.assets[assetPath];
                                const localAssetInfo = this._localProjectManifest!.assets[assetPath] ?? null;
                                const need2Update = localAssetInfo == null || remoteAssetInfo.size != localAssetInfo.size || remoteAssetInfo.md5 != localAssetInfo.md5;
                                if (need2Update && remoteAssetInfo.state != null) {
                                    // 更新需要下载的文件信息
                                    this._totalFiles++;
                                    this._totalBytes += remoteAssetInfo.size;

                                    // 恢复下载任务
                                    const downloadTask: native.DownloadTask = {
                                        identifier: assetPath,
                                        requestURL: `${this._remoteRootUrl}/${assetPath}`,
                                        storagePath: path.join(this._downloadRootDirPath, assetPath),
                                    };
                                    if (remoteAssetInfo.state == ProjectManifestAssetUpdateState.Suc) {
                                        // 更新累计下载字节数
                                        this._downloadedBytes += remoteAssetInfo.size;
                                        // 下载成功的任务加入到成功列表
                                        this.downloadSucFiles.push(downloadTask);
                                    } else {
                                        // 更新累计下载字节数
                                        // 如果之前已经有相当一部分文件未下载完成，那么这里的读取可能会比较耗时
                                        const downloadTempFilePath = downloadTask.storagePath + ".tmp";
                                        if (native.fileUtils.isFileExist(downloadTempFilePath)) {
                                            let downloadFileSize = native.fileUtils.getFileSize(downloadTempFilePath);
                                            if (downloadFileSize > 0) {
                                                this._downloadedBytes += remoteAssetInfo.size;
                                            }
                                        }
                                        // 下载失败的任务加入到失败列表
                                        this.downloadFailedFiles.push(downloadTask);
                                    }
                                }
                            });

                            let info = `检查更新：成功，发现新版本。发现之前还没有完成的更新，将读取之前的更新进度`;
                            info += ` 总字节数：${this._totalBytes}`;
                            info += ` 已下载字节数: ${this._downloadedBytes}`;
                            info += ` 总下载文件数：${this._totalFiles}`;
                            info += ` 下载成功文件数：${this.downloadSucFiles.length}`;
                            info += ` 下载失败文件数：${this.downloadFailedFiles.length}`;
                            this._debug(info);
                            this._updateState(GGHotUpdateInstanceState.CheckUpdateSucNewVersionFound);
                            return;
                        }
                    }
                } catch (error) {
                    this._error(error);
                    this._error(`检查更新：解析本地已存在的远程 project.manifest 失败。地址： ${this._projectManifestDownloadPath}`);
                }

                // 到这里表示本地没有 project.manifest 文件，或者解析出错
                // 此时我们先删除本地可能存在的 project.manifest 文件，然后重新下载，重新初始化
                if (native.fileUtils.isFileExist(this._projectManifestDownloadPath)) {
                    native.fileUtils.removeFile(this._projectManifestDownloadPath);
                }

                this._downloader.onError = (task: native.DownloadTask, errorCode: number, errorCodeInternal: number, errorStr: string) => {
                    // 处理下载失败
                    this._resetDownloadInfo();

                    this._error(`检查更新：失败，下载远程 project.manifest 失败。地址：${task.requestURL} 错误代码：${errorCode} 内部错误代码：${errorCodeInternal} 错误信息：${errorStr}`);
                    this._updateState(GGHotUpdateInstanceState.CheckUpdateFailedDownloadRemoteProjectManifestError);
                };
                this._downloader.onSuccess = (task: native.DownloadTask) => {
                    // 处理下载成功
                    this._resetDownloadInfo();

                    try {
                        if (native.fileUtils.isFileExist(task.storagePath)) {
                            this._remoteProjectManifest = JSON.parse(native.fileUtils.getStringFromFile(task.storagePath));
                        }
                    } catch (error) {
                        this._error(error);
                    }
                    if (this._remoteProjectManifest == null) {
                        this._error(`检查更新：失败，解析远程 project.manifest 失败。下载地址： ${task.requestURL} 本地存储地址：${task.storagePath}`);
                        this._updateState(GGHotUpdateInstanceState.CheckUpdateFailedParseRemoteProjectManifestError);
                        return;
                    }

                    // 对比本地最新 project.manifest 和远程 project.manifest，生产差异文件，并将需要更新的文件记录下来
                    Object.keys(this._remoteProjectManifest.assets).forEach((assetPath) => {
                        const remoteAssetInfo = this._remoteProjectManifest!.assets[assetPath];
                        const localAssetInfo = this._localProjectManifest!.assets[assetPath] ?? null;
                        const need2Update = localAssetInfo == null || remoteAssetInfo.size != localAssetInfo.size || remoteAssetInfo.md5 != localAssetInfo.md5;
                        if (need2Update) {
                            // 标记文件需要下载并加入到下载任务队列中
                            remoteAssetInfo.state = ProjectManifestAssetUpdateState.Idle;
                            this._downloadTasks.push({
                                identifier: assetPath,
                                requestURL: `${this._remoteRootUrl}/${assetPath}`,
                                storagePath: path.join(this._downloadRootDirPath, assetPath),
                            });
                            this._totalFiles++;
                            this._totalBytes += remoteAssetInfo.size;
                        }
                    });
                    if (this._downloadTasks.length > 0) {
                        // 如果版本不一致，且存在差异文件需要下载，那么返回发现现版本
                        native.fileUtils.writeStringToFile(JSON.stringify(this._remoteProjectManifest), task.storagePath);
                        let info = `检查更新：成功，发现新版本。`;
                        info += ` 总字节数：${this._totalBytes}`;
                        info += ` 已下载字节数: ${this._downloadedBytes}`;
                        info += ` 总下载文件数：${this._totalFiles}`;
                        info += ` 下载成功文件数：${this.downloadSucFiles.length}`;
                        info += ` 下载失败文件数：${this.downloadFailedFiles.length}`;
                        this._debug(info);
                        this._updateState(GGHotUpdateInstanceState.CheckUpdateSucNewVersionFound);
                    } else {
                        // 如果版本不一致，且不存在差异文件需要下载，那么返回已经更新到最新版本
                        this._debug(`检查更新：成功，发现不同远端版本，但和当前本地版本没有文件差异，因此当前已经是最新版本`);

                        // 释放文件json内存
                        this._localProjectManifest = null;
                        this._updateState(GGHotUpdateInstanceState.CheckUpdateSucAlreadyUpToDate);
                    }
                };
                this._downloader.createDownloadTask(this._projectManifesetRemoteUrl, this._projectManifestDownloadPath);
            })
            .catch((error) => {
                this._error(`检查更新：失败，解析远程 version.manifest 失败。地址: ${this._versionManifesetRemoteUrl}`);
                this._error(error);
                this._updateState(GGHotUpdateInstanceState.CheckUpdateFailedParseRemoteVersionManifestError);
            });
    }

    /**
     * 开始热更新
     */
    hotUpdate() {
        if (this._state == GGHotUpdateInstanceState.CheckUpdateInProgress) {
            this._warn("热更新：当前正在检查新版本中。请在发现新版本之后再调用 `hotUpdate`.");
            return;
        }
        if (this._state == GGHotUpdateInstanceState.HotUpdateInProgress) {
            this._warn("热更新：当前已经在热更新中。请不要重复调用 `hotUpdate`.");
            return;
        }

        this._debug(`热更新：开始`);

        // 开始下载之前，重置下载信息
        this._curConcurrentTaskCount = 0;
        this._downloadSpeed = 0;
        this._downloadRemainTimeInSecond = -1;
        this._updateState(GGHotUpdateInstanceState.HotUpdateInProgress);

        // 如果之前已经下载过，但存在下载未完成或者下载失败的文件，那么我们将失败的任务再次加入下载任务队列
        if (this.downloadFailedFiles.length > 0) {
            this._debug(`热更新：发现 ${this.downloadFailedFiles.length} 个下载失败任务，将重新加入队列进行下载`);
            this._downloadTasks.push(...this.downloadFailedFiles);
            this.downloadFailedFiles.length = 0;
        }

        // 如果没有发现差异文件，那么直接返回热更新成功
        if (this._downloadTasks.length == 0) {
            this._debug(`热更新：成功，当前没有资源需要下载`);
            this._updateSearchPath();
            this._updateState(GGHotUpdateInstanceState.HotUpdateSuc);
            return;
        }

        this._debug(`热更新：当前共计 ${this._downloadTasks.length} 个下载任务`);

        // 上次计算下载速度时，累计下载字节数(Bytes)
        let lastDownloadedBytes = 0;
        // 上次计算下载速度时，时间戳(ms)
        let lastSpeedUpdateTimeInMs = 0;
        // 上次外部下载进度回调的时间戳(ms)
        let lastCallBackUpdateTimeInMs = Date.now();

        this._downloader.onProgress = (task: native.DownloadTask, bytesReceived: number, totalBytesReceived: number, totalBytesExpected: number) => {
            // 更新下载进度
            this._downloadedBytes += bytesReceived;

            let curTime = Date.now();

            // 计算下载速度（间隔一段时间在计算，避免短时间内多次计算，值波动范围过大，导致数据失真，失去参考意义）
            if (curTime - lastSpeedUpdateTimeInMs >= this._option.downloadSpeedCalculationIntervalInMs) {
                if (lastSpeedUpdateTimeInMs == 0) {
                    // 首次下载进度回调，是没有上次下载进度记录的，所以此时下载速度和剩余时间重置
                    this._downloadSpeed = 0;
                    this._downloadRemainTimeInSecond = -1;
                } else {
                    // 二次或后续下载进度回调时，存在上次下载进度记录，所以可以比较计算此时下载速度和剩余时间
                    this._downloadSpeed = (this._downloadedBytes - lastDownloadedBytes) / ((curTime - lastSpeedUpdateTimeInMs) / 1000);
                    this._downloadRemainTimeInSecond = Math.round((this._totalBytes - this._downloadedBytes) / this._downloadSpeed);
                }
                lastDownloadedBytes = this._downloadedBytes;
                lastSpeedUpdateTimeInMs = curTime;
            }

            // 外部下载进度回调（间隔一段时间之后在回调）
            if (curTime - lastCallBackUpdateTimeInMs >= this._option.downloadProgressCallBackIntervalInMs) {
                lastCallBackUpdateTimeInMs = curTime;

                let info = "热更新：下载中";
                info += ` 总字节数：${this._totalBytes}`;
                info += ` 已下载字节数: ${this._downloadedBytes}`;
                info += ` 总下载文件数：${this._totalFiles}`;
                info += ` 下载成功文件数：${this.downloadSucFiles.length}`;
                info += ` 下载失败文件数：${this.downloadFailedFiles.length}`;
                info += ` 当前并行下载任务数：${this._curConcurrentTaskCount}`;
                info += ` 当前下载速度：${(this._downloadSpeed / 1024 / 1024).toFixed(2)} MB/s`;
                info += ` 当前剩余时间：${this._downloadRemainTimeInSecond}s`;
                this._debug(info);

                this._updateState(GGHotUpdateInstanceState.HotUpdateInProgress);
            }
        };
        this._downloader.onSuccess = (task: native.DownloadTask) => {
            // 收集下载成功任务
            this.downloadSucFiles.push(task);

            // 更新下载进度
            this._updateState(GGHotUpdateInstanceState.HotUpdateInProgress);

            // 标记文件下载成功，并保存到本地，方便恢复任务
            if (this._remoteProjectManifest) {
                this._remoteProjectManifest.assets[task.identifier]!.state = ProjectManifestAssetUpdateState.Suc;
                native.fileUtils.writeStringToFile(JSON.stringify(this._remoteProjectManifest), this._projectManifestDownloadPath);
            }

            // 处理结果
            this._handleDownloadResult();
        };
        this._downloader.onError = (task: native.DownloadTask, errorCode: number, errorCodeInternal: number, errorStr: string) => {
            // 收集下载失败任务
            this.downloadFailedFiles.push(task);

            // 更新下载进度
            this._debug(
                `热更新：文件下载失败：${task.requestURL} 下载失败。错误代码：${errorCode} 内部错误代码：${errorCodeInternal} 错误信息：${errorStr} 当前累计下载失败文件数量：${this.downloadFailedFiles.length}`
            );
            this._updateState(GGHotUpdateInstanceState.HotUpdateInProgress);

            // 处理结果
            this._handleDownloadResult();
        };
        // 启动下载
        this._nextDownload();
    }

    private _handleDownloadResult() {
        //  不管下载成功还是失败，并行任务数 -1;
        this._curConcurrentTaskCount--;

        // 如果已经没有后续下载任务并且进行中的任务都已经结束了，那么检查热更新结果
        if (this._downloadTasks.length == 0 && this._curConcurrentTaskCount == 0) {
            this._downloadSpeed = 0;
            this._downloadRemainTimeInSecond = -1;

            const suc = this._totalFiles == this.downloadSucFiles.length;
            let info = suc ? "热更新：成功，" : "热更新：失败，";
            info += ` 总字节数：${this._totalBytes}`;
            info += ` 已下载字节数: ${this._downloadedBytes}`;
            info += ` 总下载文件数：${this._totalFiles}`;
            info += ` 下载成功文件数：${this.downloadSucFiles.length}`;
            info += ` 下载失败文件数：${this.downloadFailedFiles.length}`;
            info += ` 当前并行下载任务数：${this._curConcurrentTaskCount}`;
            info += ` 当前下载速度：${(this._downloadSpeed / 1024 / 1024).toFixed(2)} MB/s`;
            info += ` 当前剩余时间：${this._downloadRemainTimeInSecond}s`;
            if (suc) {
                this._debug(info);
                this._updateSearchPath();
                this._updateState(GGHotUpdateInstanceState.HotUpdateSuc);
            } else {
                this._error(info);
                this._updateState(GGHotUpdateInstanceState.HotUpdateFailed);
            }
            return;
        }

        // 如果还有后续其他下载任务，那么开启下个下载
        if (this._downloadTasks.length > 0) {
            this._nextDownload();
        }
    }

    private _nextDownload() {
        while (this._downloadTasks.length > 0 && this._curConcurrentTaskCount < this._option.downloadMaxConcurrentTask) {
            this._curConcurrentTaskCount++;
            const task = this._downloadTasks.shift()!;
            this._downloader.createDownloadTask(task.requestURL, task.storagePath, task.identifier);
        }
    }
    /**
     * 更新搜索地址
     *
     * * 主包：更新搜索路径之后，还需要重启游戏才可以生效
     * * 子包：更新搜索路径之后，不用重启游戏就生效（但是要注意此前还没有加载过子包）
     */
    private _updateSearchPath() {
        // e.g. ["@assets/data/","@assets/Resources/","@assets/"]
        const searchPaths = native.fileUtils.getSearchPaths();

        // 待插入的搜索路径（注意结尾要加 /)
        const newSearchPath = this._searchRootDirPath + "/";

        this._debug(`当前搜索路径顺序：${JSON.stringify(searchPaths)}`);
        this._debug(`待插入的搜索路径：${newSearchPath}`);

        // 插入新的搜索路径到当前搜索路径的最前面（如果当前搜索路径数组已经包含新的待插入搜索路径，那么只需要将其提到数组最前面即可）
        let isNewPathExist = false;
        for (let j = searchPaths.length - 1; j >= 0; --j) {
            if (searchPaths[j] == newSearchPath) {
                searchPaths.unshift(searchPaths.splice(j, 1)[0]);
                isNewPathExist = true;
                break;
            }
        }
        // 如果当前搜索路径数组不包含新的待插入搜索路径，那么将新的路径插入哦到最前面
        if (!isNewPathExist) {
            searchPaths.unshift(newSearchPath);
        }
        this._debug(`最终搜索路径顺序：${JSON.stringify(searchPaths)}`);

        if (this.name == GGHotUpdateInstanceEnum.BuildIn) {
            // 如果是主包，那么不用立即更新搜索路径，在下次重启才需要更新搜索路径
        } else {
            // 如果是子包，那么需要立即更新搜索路径，同时需要将下载缓存目录下的文件更新到搜索路径目录下
            this._debug(`将移动下载目录 ${this._downloadRootDirPath} 的资源到搜索目录 ${this._searchRootDirPath}`);

            // 更新搜索路径
            native.fileUtils.setSearchPaths(searchPaths);

            // 移动下载目录的内容到搜索路径下
            const downloadDirPath = this._downloadRootDirPath + "/";
            const downloadDirPathLength = downloadDirPath.length;
            if (native.fileUtils.isDirectoryExist(downloadDirPath)) {
                const fileList: string[] = [];
                native.fileUtils.listFilesRecursively(downloadDirPath, fileList);
                fileList.forEach((srcPath) => {
                    let relativePath = srcPath.substring(downloadDirPathLength);
                    let dstPath = newSearchPath + relativePath;
                    if (dstPath[dstPath.length - 1] == "/") {
                        native.fileUtils.createDirectory(dstPath);
                    } else {
                        if (native.fileUtils.isFileExist(dstPath)) {
                            native.fileUtils.removeFile(dstPath);
                        }
                        native.fileUtils.renameFile(srcPath, dstPath);
                    }
                });
                native.fileUtils.removeDirectory(downloadDirPath);
            }
        }

        // 同时缓存新的搜索路径数组，以便下次重启的时候，更新新的搜索路径
        // eslint-disable-next-line no-restricted-properties
        sys.localStorage.setItem("GGHotUpdateSearchPaths", JSON.stringify(searchPaths));
        this._debug(`保存最新搜索路径到 LocalStorage 中，方便下次重启游戏时更新搜索路径`);
    }

    private _debug(...args: any[]) {
        ggLogger.debug(this.name, ...args);
    }

    private _warn(...args: any[]) {
        ggLogger.warn(this.name, ...args);
    }

    private _error(...args: any[]) {
        ggLogger.error(this.name, ...args);
    }
}
