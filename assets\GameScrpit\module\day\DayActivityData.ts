import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { RedeemMessage } from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import TipMgr from "../../lib/tips/TipMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import { DayActivityMsgEnum } from "./DayActivityConfig";
import { avId1, DayActivityModule } from "./DayActivityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class DayActivityData {
  /**每日活动 */
  private _dayMessage: RedeemMessage = null;

  private _dayRedeemPackVO: any = null;
  public get dayMessage(): RedeemMessage {
    if (!this._dayMessage) {
      this._dayMessage = {
        activityId: 0,
        /** 物料ID:兑换次数 */
        redeemMap: {},
        /** 物料ID:广告次数 */
        adMap: {},
        /** 限制条件 兑换解锁要求的值 */
        limitMap: {},
        /** 自选礼包的选中情况 */
        chosenMap: {},
        /** 重置时间 */
        resetTime: 0,
      };
    }
    return this._dayMessage;
  } /** 解锁条件：值 */
  public get limitMap(): { [key: number]: number } {
    return this.dayMessage.limitMap;
  }
  /** 解锁条件：值 */
  public set limitMap(value: { [key: number]: number }) {
    this.dayMessage.limitMap = value;
    MsgMgr.emit(DayActivityMsgEnum.DAYACTIVITY_RED_DOT_UPDATE);
  }
  public set dayMessage(value: RedeemMessage) {
    this._dayMessage = value;
    MsgMgr.emit(DayActivityMsgEnum.DAYACTIVITY_RED_DOT_UPDATE);
  }

  /**获取每日配置 */
  public getDayVO(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (this._dayRedeemPackVO) {
        resolve(this._dayRedeemPackVO);
      } else {
        GameHttpApi.getActivityConfig(avId1).then((resp: any) => {
          if (resp.code != 200) {
            log.error(resp);
          }
          let db = JSON.parse(resp.msg);
          this._dayRedeemPackVO = db.redeemList;
          resolve(this._dayRedeemPackVO);
        });
      }
    });
  }

  public upDayVO() {
    this._dayRedeemPackVO = null;
    this.getDayVO();

    let rotueTables = DayActivityModule.route.rotueTables;
    for (let i = 0; i < rotueTables.length; i++) {
      UIMgr.instance.closeByName(rotueTables[i].uiName);
    }
    TipMgr.showTip("活动内容已变更");
  }
}
