{"skeleton": {"hash": "JADzZbeoSQQW/m1TP7Jy/9hTvMI=", "spine": "3.8.75", "images": "./images/", "audio": "D:/spine导出/主界面动画新/冥族"}, "bones": [{"name": "root"}, {"name": "ming<PERSON>", "parent": "root", "x": 4.69, "y": -74.3}, {"name": "all", "parent": "ming<PERSON>", "rotation": 0.45, "x": 79.31, "y": -123.35, "scaleX": 0.3671, "scaleY": 0.3671}, {"name": "fanren", "parent": "all", "length": 85.92, "rotation": -1.14, "x": -226.2, "y": 498.02, "scaleX": 0.6806, "scaleY": 0.6806}, {"name": "mamian", "parent": "all", "length": 37.31, "rotation": -179.31, "x": -351.48, "y": 541.22}, {"name": "<PERSON><PERSON><PERSON>", "parent": "all", "length": 39.21, "rotation": -177.73, "x": -92.54, "y": 546.24, "scaleX": -1.1015, "scaleY": 1.1015}, {"name": "bwc", "parent": "all", "length": 137.98, "rotation": -178.62, "x": -301.27, "y": 513.42}, {"name": "hwc", "parent": "all", "length": 131.29, "rotation": -179.97, "x": -158.79, "y": 502.31, "scaleX": -1}, {"name": "mpmingzu", "parent": "root", "x": -17, "y": -5.15, "scaleX": 0.1835, "scaleY": 0.1835}, {"name": "mpmingzu2", "parent": "mpmingzu", "x": 29.7, "y": 252.69, "color": "ff0000ff"}, {"name": "mpmingzu3", "parent": "mpmingzu2", "length": 41.65, "rotation": 0.64, "x": 0.47, "color": "005cffff"}, {"name": "mpmingzu4", "parent": "mpmingzu3", "length": 23.36, "rotation": 94.55, "x": -0.72, "y": 0.08}, {"name": "mpmingzu5", "parent": "mpmingzu4", "length": 68.48, "rotation": 15.78, "x": 23.36}, {"name": "mpmingzu6", "parent": "mpmingzu5", "length": 22.94, "rotation": -27.71, "x": 68.09, "y": -0.05}, {"name": "mpmingzu7", "parent": "mpmingzu6", "length": 23.4, "rotation": -6.75, "x": 22.94}, {"name": "mpmingzu8", "parent": "mpmingzu7", "length": 36.36, "rotation": -91.18, "x": 21.51, "y": -27.95, "transform": "noRotationOrReflection", "color": "ff671eff"}, {"name": "mpmingzu9", "parent": "mpmingzu8", "length": 18.94, "rotation": 3.44, "x": 36.36, "color": "ff671eff"}, {"name": "mpmingzu10", "parent": "mpmingzu9", "length": 10.81, "rotation": 5.68, "x": 18.94, "color": "ff671eff"}, {"name": "mpmingzu11", "parent": "mpmingzu7", "length": 38.4, "rotation": -92.97, "x": 11.33, "y": 20.59, "transform": "noRotationOrReflection", "color": "ff671eff"}, {"name": "mpmingzu12", "parent": "mpmingzu11", "length": 17.69, "rotation": 4.59, "x": 38.4, "color": "ff671eff"}, {"name": "mpmingzu13", "parent": "mpmingzu12", "length": 10.4, "rotation": 9.43, "x": 17.69, "color": "ff671eff"}, {"name": "mpmingzu14", "parent": "mpmingzu7", "length": 67.35, "rotation": -83.23, "x": 81.25, "y": -39.83, "transform": "noRotationOrReflection", "color": "ff8d8dff"}, {"name": "mpmingzu15", "parent": "mpmingzu14", "length": 47.05, "rotation": 16.91, "x": 67.19, "y": -0.31, "color": "ff8d8dff"}, {"name": "mpmingzu16", "parent": "mpmingzu15", "length": 45.84, "rotation": 34.46, "x": 47.05, "color": "ff8d8dff"}, {"name": "mpmingzu17", "parent": "mpmingzu16", "length": 36.89, "rotation": -13.14, "x": 45.84, "color": "ff8d8dff"}, {"name": "mpmingzu18", "parent": "mpmingzu17", "length": 40.36, "rotation": -23, "x": 36.89, "color": "ff8d8dff"}, {"name": "mpmingzu19", "parent": "mpmingzu7", "length": 58.97, "rotation": -90.37, "x": 79.46, "y": 79.07, "transform": "noRotationOrReflection", "color": "ff8d8dff"}, {"name": "mpmingzu20", "parent": "mpmingzu19", "length": 58.5, "rotation": -27.36, "x": 58.97, "color": "ff8d8dff"}, {"name": "mpmingzu21", "parent": "mpmingzu20", "length": 62.24, "rotation": -22.2, "x": 58.5, "color": "ff8d8dff"}, {"name": "mpmingzu22", "parent": "mpmingzu21", "length": 39.95, "rotation": 20.45, "x": 62.24, "color": "ff8d8dff"}, {"name": "mpmingzu23", "parent": "mpmingzu22", "length": 21.55, "rotation": 30.48, "x": 39.95, "color": "ff8d8dff"}, {"name": "mpmingzu24", "parent": "mpmingzu23", "length": 33.52, "rotation": 6.12, "x": 21.55, "color": "ff8d8dff"}, {"name": "mpmingzu25", "parent": "mpmingzu7", "length": 28.16, "rotation": -167.36, "x": 10.39, "y": 35.51, "color": "ffa60eff"}, {"name": "mpmingzu26", "parent": "mpmingzu7", "length": 25.42, "rotation": -159.97, "x": 15.17, "y": -41.25, "color": "ffa60eff"}, {"name": "mpmingzu27", "parent": "mpmingzu7", "length": 98.73, "rotation": -99.51, "x": 8.23, "y": 24.17, "transform": "noRotationOrReflection"}, {"name": "mpmingzu28", "parent": "mpmingzu27", "length": 85.24, "rotation": -1.29, "x": 98.73}, {"name": "mpmingzu29", "parent": "mpmingzu28", "length": 77.2, "rotation": -6.67, "x": 85.24}, {"name": "mpmingzu30", "parent": "mpmingzu29", "length": 46.62, "rotation": -0.35, "x": 77.2}, {"name": "mpmingzu31", "parent": "mpmingzu30", "length": 25.08, "rotation": 17.55, "x": 46.13, "y": -0.17}, {"name": "mpmingzu34", "parent": "mpmingzu7", "x": 2.66, "y": -11.03, "color": "abe323ff"}, {"name": "mpmingzu35", "parent": "mpmingzu7", "x": 41.25, "y": -9.9}, {"name": "mpmingzu32", "parent": "mpmingzu5", "x": 7.88, "y": -9.6, "color": "abe323ff"}, {"name": "mpmingzu33", "parent": "mpmingzu5", "x": 38.96, "y": -11.25}, {"name": "mpmingzu36", "parent": "mpmingzu5", "length": 50.66, "rotation": -134.42, "x": 32.25, "y": -31.31}, {"name": "mpmingzu37", "parent": "mpmingzu36", "length": 44.08, "rotation": 54.41, "x": 50.66}, {"name": "mpmingzu38", "parent": "mpmingzu37", "length": 21.93, "rotation": -5.13, "x": 44.08, "transform": "noRotationOrReflection"}, {"name": "a1", "parent": "mpmingzu38", "length": 31.13, "rotation": 92.03, "x": 24.58, "y": 11.2}, {"name": "ywyw", "parent": "a1", "x": -168.5, "y": -17.73, "scaleX": 6.9618, "scaleY": 3.149, "transform": "noRotationOrReflection"}, {"name": "ywyw2", "parent": "ywyw", "rotation": 180, "x": -0.81, "y": 11.96, "scaleX": -0.214, "transform": "noRotationOrReflection"}, {"name": "ywyw3", "parent": "ywyw", "x": -0.62, "y": 21.76, "scaleX": 0.4459}, {"name": "ywyw4", "parent": "ywyw", "x": -0.62, "y": 32.06, "scaleX": 0.6658}, {"name": "ywyw5", "parent": "ywyw", "x": -0.81, "y": 42.06, "scaleX": 1.0193}, {"name": "ywyw6", "parent": "ywyw", "x": -0.62, "y": 52.25, "scaleX": 1.307}, {"name": "ywyw7", "parent": "ywyw", "x": -1.39, "y": 63.54, "scaleX": 2.4007}, {"name": "mpmingzu39", "parent": "mpmingzu36", "length": 69.12, "rotation": -60.13, "x": 57.66, "y": -13.48, "transform": "noRotationOrReflection"}, {"name": "mpmingzu40", "parent": "mpmingzu39", "length": 62.56, "rotation": 11.46, "x": 69.12}, {"name": "mpmingzu41", "parent": "mpmingzu40", "length": 46.25, "rotation": 9.5, "x": 62.63, "y": -0.03}, {"name": "mpmingzu42", "parent": "mpmingzu41", "length": 24.32, "rotation": 7, "x": 46.25}, {"name": "mpmingzu43", "parent": "mpmingzu42", "length": 16.33, "rotation": 5.61, "x": 24.32}, {"name": "mpmingzu44", "parent": "mpmingzu5", "length": 63.8, "rotation": 140.81, "x": 78.58, "y": 28.54}, {"name": "mpmingzu45", "parent": "mpmingzu44", "length": 48.84, "rotation": -90.95, "x": 63.78, "y": -0.8}, {"name": "mpmingzu46", "parent": "mpmingzu45", "length": 28.34, "rotation": -8.82, "x": 48.84}, {"name": "a5", "parent": "mpmingzu46", "length": 52, "rotation": -37.91, "x": 19.31, "y": -3.49}, {"name": "mpmingzu47", "parent": "mpmingzu45", "length": 60.45, "rotation": -93.46, "x": -1.21, "y": 21.09, "transform": "noRotationOrReflection"}, {"name": "mpmingzu48", "parent": "mpmingzu47", "length": 55.94, "rotation": -30.92, "x": 60.43, "y": 0.4}, {"name": "mpmingzu49", "parent": "mpmingzu48", "length": 39.41, "rotation": -16.04, "x": 55.94}, {"name": "mpmingzu50", "parent": "mpmingzu49", "length": 27.78, "rotation": -8.9, "x": 39.41}, {"name": "mpmingzu51", "parent": "mpmingzu3", "length": 36.05, "rotation": -90, "x": 0.15, "y": -1.22}, {"name": "mpmingzu52", "parent": "mpmingzu51", "length": 67.83, "rotation": 11.07, "x": 11.89, "y": 31, "color": "c491ffff"}, {"name": "mpmingzu53", "parent": "mpmingzu52", "length": 56.1, "rotation": 1.22, "x": 67.83, "color": "c491ffff"}, {"name": "mpmingzu54", "parent": "mpmingzu53", "length": 41.22, "rotation": 9.6, "x": 56.1, "color": "c491ffff"}, {"name": "mpmingzu55", "parent": "mpmingzu54", "length": 31.23, "rotation": 11.36, "x": 41.22, "color": "c491ffff"}, {"name": "mpmingzu56", "parent": "mpmingzu51", "length": 68.33, "rotation": 7.53, "x": 15.62, "y": -22.38, "color": "c491ffff"}, {"name": "mpmingzu57", "parent": "mpmingzu56", "length": 58.02, "rotation": -0.56, "x": 68.73, "y": -0.06, "color": "c491ffff"}, {"name": "mpmingzu58", "parent": "mpmingzu57", "length": 42.31, "rotation": -2.13, "x": 58.83, "y": -0.11, "color": "c491ffff"}, {"name": "mpmingzu59", "parent": "mpmingzu58", "length": 23.58, "rotation": -10.42, "x": 42.31, "color": "c491ffff"}, {"name": "mpmingzu60", "parent": "mpmingzu51", "x": 26.47, "y": -34}, {"name": "mpmingzu61", "parent": "mpmingzu60", "length": 87.98, "rotation": 7.56, "x": 1.22, "y": 0.39}, {"name": "mpmingzu62", "parent": "mpmingzu61", "length": 79.02, "rotation": -26.73, "x": 87.98}, {"name": "mpmingzu66", "parent": "mpmingzu60", "length": 93.76, "rotation": 19.18, "x": 0.31, "y": 0.03}, {"name": "mpmingzu67", "parent": "mpmingzu66", "length": 86.85, "rotation": -49.98, "x": 93.76, "y": 0.21, "color": "abe323ff"}, {"name": "mpmingzu68", "parent": "mpmingzu51", "x": 30.37, "y": 10.6}, {"name": "mpmingzu69", "parent": "mpmingzu68", "length": 85.2, "rotation": -16.66, "x": 0.3}, {"name": "mpmingzu70", "parent": "mpmingzu69", "length": 70.89, "rotation": 14.29, "x": 85.2}, {"name": "mpmingzu75", "parent": "mpmingzu51", "x": 26.91, "y": 5.59, "color": "fffc00ff"}, {"name": "mpmingzu76", "parent": "mpmingzu75", "length": 87.98, "rotation": 7.56, "x": 1.22, "y": 0.39}, {"name": "mpmingzu77", "parent": "mpmingzu76", "length": 79.02, "rotation": -26.73, "x": 87.98}, {"name": "mpmingzu79", "parent": "mpmingzu75", "length": 93.76, "rotation": 19.18, "x": 0.31, "y": 0.03}, {"name": "mpmingzu80", "parent": "mpmingzu79", "length": 86.85, "rotation": -49.98, "x": 93.76, "y": 0.21, "color": "abe323ff"}, {"name": "mpmingzu81", "parent": "mpmingzu51", "length": 65.95, "rotation": -14.48, "x": 1.57, "y": -38.32, "color": "c491ffff"}, {"name": "mpmingzu82", "parent": "mpmingzu81", "length": 56.33, "rotation": 2.44, "x": 65.95, "color": "c491ffff"}, {"name": "mpmingzu83", "parent": "mpmingzu82", "length": 39.96, "rotation": -1.34, "x": 56.33, "color": "c491ffff"}, {"name": "mpmingzu64", "parent": "mpmingzu", "length": 25.9, "rotation": -1.47, "x": -14.38, "y": 41.47}, {"name": "mpmingzu65", "parent": "mpmingzu64", "length": 14.43, "rotation": 95.05, "x": 0.24, "y": 0.44}, {"name": "yj1", "parent": "mpmingzu65", "rotation": 157.9, "x": 19.55, "y": 0.17, "color": "ff3f00ff"}, {"name": "mpmingzu63", "parent": "yj1", "length": 13.52, "rotation": 27.14}, {"name": "yj", "parent": "mpmingzu65", "rotation": -175.37, "x": 92.76, "y": -29.57, "color": "ff3f00ff"}, {"name": "mpmingzu72", "parent": "mpmingzu", "length": 16.16, "rotation": 74.93, "x": 8.87, "y": 45.42}, {"name": "zj", "parent": "mpmingzu72", "rotation": -166.65, "x": 23.72, "y": -0.63, "color": "ff3f00ff"}, {"name": "mpmingzu71", "parent": "zj", "length": 13.57, "rotation": -14.76, "x": -0.59, "y": -0.08}, {"name": "mpmingzu73", "parent": "mpmingzu", "length": 25.9, "rotation": -1.47, "x": 25.22, "y": 41.47, "color": "fffc00ff"}, {"name": "mpmingzu74", "parent": "mpmingzu73", "length": 14.43, "rotation": 95.05, "x": 0.24, "y": 0.44, "color": "fffc00ff"}, {"name": "yj5", "parent": "mpmingzu74", "rotation": -175.37, "x": 92.76, "y": -29.57, "color": "ff3f00ff"}, {"name": "yj6", "parent": "mpmingzu74", "rotation": 157.9, "x": 19.55, "y": 0.17, "color": "ff3f00ff"}, {"name": "mpmingzu78", "parent": "yj6", "length": 13.52, "rotation": 27.14}, {"name": "mpmingzu84", "parent": "mpmingzu", "length": 35.66, "rotation": -147.76, "x": -56.43, "y": 77.19, "color": "c491ffff"}, {"name": "mpmingzu85", "parent": "mpmingzu", "length": 29.71, "rotation": -165.53, "x": -86.59, "y": 58.17, "color": "c491ffff"}, {"name": "bwc2", "parent": "bwc", "length": 66.74, "rotation": -4.22, "x": -3.5, "y": -92.88}, {"name": "bwc3", "parent": "bwc2", "length": 16.67, "rotation": -125.28, "x": -3.9, "y": -0.86}, {"name": "bwc4", "parent": "bwc3", "length": 17.19, "rotation": 33.03, "x": 16.67, "color": "00ff0eff"}, {"name": "bwc5", "parent": "bwc2", "length": 18.63, "rotation": 60.08, "x": 0.95, "y": 4.91}, {"name": "bwc6", "parent": "bwc5", "length": 21.2, "rotation": -17.57, "x": 18.63}, {"name": "bwc7", "parent": "bwc3", "length": 24.64, "rotation": -114.5, "x": 11.17, "y": 5.37}, {"name": "bwc8", "parent": "bwc7", "length": 21.62, "rotation": -26.45, "x": 24.64}, {"name": "bwc9", "parent": "bwc3", "length": 16.75, "rotation": -130.32, "x": 28.98, "y": -18.54}, {"name": "bwc10", "parent": "bwc9", "length": 21.79, "rotation": -11.55, "x": 16.75}, {"name": "bwc11", "parent": "bwc3", "length": 78.82, "rotation": -170.58, "x": 6.9, "y": -24.66, "transform": "noRotationOrReflection"}, {"name": "bwc16", "parent": "bwc4", "length": 25.34, "rotation": 158.07, "x": 52.29, "y": 26.09, "color": "8aff00ff"}, {"name": "bwc17", "parent": "bwc16", "length": 31.59, "rotation": 33.22, "x": 25.34, "color": "8aff00ff"}, {"name": "bwc18", "parent": "bwc17", "length": 24.99, "rotation": -2.65, "x": 31.59, "color": "8aff00ff"}, {"name": "bwc19", "parent": "bwc4", "length": 24.57, "rotation": -146.98, "x": 54.75, "y": -19.37, "color": "8aff00ff"}, {"name": "bwc20", "parent": "bwc19", "length": 30.74, "rotation": -31.96, "x": 24.57, "color": "8aff00ff"}, {"name": "bwc21", "parent": "bwc20", "length": 29.84, "rotation": 5.92, "x": 30.74, "color": "8aff00ff"}, {"name": "bwc22", "parent": "bwc4", "length": 28.05, "rotation": -7.13, "x": 52.15, "y": -5.24}, {"name": "bwc23", "parent": "bwc22", "length": 34.07, "rotation": 9.36, "x": 28.05}, {"name": "bwc24", "parent": "bwc11", "length": 6.8, "rotation": -92.18, "x": 83.41, "y": 2.64, "transform": "noRotationOrReflection"}, {"name": "bwc25", "parent": "bwc24", "length": 19.75, "rotation": 0.49, "x": 6.8}, {"name": "bwc26", "parent": "bwc25", "x": 17.29, "y": -0.05, "scaleX": 1.5948, "scaleY": 1.5948}, {"name": "bwc12", "parent": "bwc4", "length": 31.5, "rotation": 168.61, "x": 51.85, "y": 14.03, "color": "00ff0eff"}, {"name": "bwc13", "parent": "bwc12", "length": 26.26, "rotation": 31.46, "x": 31.5, "color": "00ff0eff"}, {"name": "bwc14", "parent": "bwc13", "length": 19.12, "rotation": 18.64, "x": 26.26, "color": "00ff0eff"}, {"name": "bwc15", "parent": "bwc14", "length": 11.36, "rotation": -55.73, "x": 19.12, "color": "00ff0eff"}, {"name": "hwc2", "parent": "hwc", "length": 46.35, "rotation": 3.39, "x": 14.25, "y": -99.64}, {"name": "hwc3", "parent": "hwc2", "length": 16.62, "rotation": -99.57, "x": -4.85, "y": -1.89}, {"name": "hwc4", "parent": "hwc3", "length": 17.06, "rotation": 6.65, "x": 16.62, "color": "c30000ff"}, {"name": "hwc5", "parent": "hwc2", "length": 20.21, "rotation": 80.84, "x": -6.12, "y": 5.61}, {"name": "hwc6", "parent": "hwc5", "length": 16.83, "rotation": -5.06, "x": 20.21}, {"name": "hwc7", "parent": "hwc3", "length": 22.76, "rotation": -139.77, "x": 19.26, "y": 8.94}, {"name": "hwc8", "parent": "hwc7", "length": 24.61, "rotation": -28.62, "x": 22.76}, {"name": "hwc9", "parent": "hwc3", "length": 21.21, "rotation": -148.76, "x": 25.52, "y": -10.9}, {"name": "hwc10", "parent": "hwc9", "length": 22.99, "rotation": -9.76, "x": 21.21}, {"name": "hwc11", "parent": "hwc3", "length": 51.91, "rotation": 124.43, "x": 9.56, "y": -13.75, "color": "3cff00ff"}, {"name": "hwc12", "parent": "hwc4", "length": 17.62, "rotation": -118.23, "x": 61.52, "y": -9.4, "color": "c30000ff"}, {"name": "hwc13", "parent": "hwc4", "length": 16.89, "rotation": 165.43, "x": 47.46, "y": 38.49, "color": "c30000ff"}, {"name": "hwc14", "parent": "hwc13", "length": 20.76, "rotation": 51.89, "x": 16.89, "color": "c30000ff"}, {"name": "hwc15", "parent": "hwc4", "x": 59.88, "y": 14.79, "color": "c30000ff"}, {"name": "hwc16", "parent": "hwc4", "length": 18.88, "rotation": 174.54, "x": 51.06, "y": 49.11, "color": "c30000ff"}, {"name": "hwc17", "parent": "hwc16", "length": 27.63, "rotation": 32.18, "x": 18.88, "color": "c30000ff"}, {"name": "hwc18", "parent": "hwc17", "length": 22.81, "rotation": -53.1, "x": 27.63, "color": "c30000ff"}, {"name": "hwc19", "parent": "hwc4", "length": 22.65, "rotation": -143.21, "x": 63.97, "y": 2.04, "color": "c30000ff"}, {"name": "hwc20", "parent": "hwc19", "length": 25.59, "rotation": -17.77, "x": 22.65, "color": "c30000ff"}, {"name": "hwc21", "parent": "hwc20", "length": 18.63, "rotation": -11.45, "x": 25.59, "color": "c30000ff"}, {"name": "hwc22", "parent": "hwc4", "x": 8.03, "y": -5.21, "color": "c30000ff"}, {"name": "hwc23", "parent": "hwc4", "length": 25.05, "rotation": 32.88, "x": 66.33, "y": 17.78, "color": "c30000ff"}, {"name": "hwc24", "parent": "hwc23", "length": 28.18, "rotation": 4.77, "x": 25.05, "color": "c30000ff"}, {"name": "hwc25", "parent": "hwc11", "x": 62.48, "y": 18.53, "color": "3cff00ff"}, {"name": "hwc26", "parent": "hwc11", "x": 84.37, "y": -7.6, "color": "3cff00ff"}, {"name": "hwc27", "parent": "hwc11", "x": 58.19, "y": -11.36, "color": "3cff00ff"}, {"name": "hwc28", "parent": "hwc11", "x": 40.02, "y": 7.81, "color": "3cff00ff"}, {"name": "mamian2", "parent": "mamian", "length": 102.38, "rotation": -2.56, "x": 3.86, "y": -67.61}, {"name": "mamian3", "parent": "mamian2", "length": 28.17, "rotation": -77.17, "x": -9.07, "y": -2.25}, {"name": "mamian4", "parent": "mamian3", "length": 27.38, "rotation": 10.88, "x": 28.17}, {"name": "mamian5", "parent": "mamian4", "length": 35.56, "rotation": -26.38, "x": 27.77, "y": 0.94}, {"name": "mamian6", "parent": "mamian2", "length": 23.31, "rotation": 45.05, "x": 17.92, "y": 11.64}, {"name": "mamian7", "parent": "mamian2", "length": 16.96, "rotation": 97.39, "x": -8.71, "y": 9.41}, {"name": "mamian8", "parent": "mamian2", "length": 13.33, "rotation": 138.8, "x": -28.06, "y": 6.77}, {"name": "mamian9", "parent": "mamian2", "length": 28.31, "rotation": 101.79, "x": -11.92, "y": 4.62}, {"name": "mamian10", "parent": "mamian9", "length": 18.36, "rotation": -28.42, "x": 28.31}, {"name": "mamian11", "parent": "mamian2", "length": 32.2, "rotation": 74.42, "x": 6.2, "y": 9.58}, {"name": "mamian12", "parent": "mamian11", "length": 15.16, "rotation": 27.44, "x": 32.2}, {"name": "mamian13", "parent": "mamian4", "length": 27.1, "rotation": 89.82, "x": 39.34, "y": 23.91}, {"name": "mamian14", "parent": "mamian4", "length": 22.95, "rotation": -152.79, "x": 12.84, "y": -33.48}, {"name": "mamian15", "parent": "mamian5", "length": 31.17, "rotation": 19.78, "x": 49.89, "y": -9.86}, {"name": "mamian16", "parent": "mamian5", "length": 33.93, "rotation": 74.5, "x": 35.99, "y": 8.62}, {"name": "mamian17", "parent": "mamian5", "length": 24.22, "rotation": 99.2, "x": 5.15, "y": 19.2}, {"name": "mamian18", "parent": "mamian5", "length": 16.05, "rotation": 10.91, "x": 61.93, "y": -22.58}, {"name": "mamian19", "parent": "mamian5", "length": 18.91, "rotation": 27.88, "x": 54.07, "y": 11}, {"name": "mamian23", "parent": "mamian4", "x": 24.32, "y": 25.25, "color": "00ff0aff"}, {"name": "mamian20", "parent": "mamian23", "length": 36.21, "rotation": 109.02, "x": 0.58, "y": 5.54, "color": "e3e3e3ff"}, {"name": "mamian21", "parent": "mamian20", "length": 33.42, "rotation": 123.77, "x": 36.21, "color": "e3e3e3ff"}, {"name": "mamian22", "parent": "mamian21", "x": 42.42, "y": 0.09, "color": "e3e3e3ff"}, {"name": "mamian24", "parent": "mamian4", "x": 6.22, "y": -28.46, "color": "00ff0aff"}, {"name": "mamian25", "parent": "mamian24", "length": 18.8, "rotation": -55.98, "x": -37.9, "y": 7.51}, {"name": "mamian26", "parent": "mamian25", "x": 26.69, "y": 6.97}, {"name": "mamian27", "parent": "mamian2", "length": 33.19, "rotation": 57.55, "x": 8.65, "y": 10.72}, {"name": "mamian28", "parent": "mamian27", "length": 22.08, "rotation": 73.27, "x": 33.19, "color": "abe323ff"}, {"name": "mamian29", "parent": "mamian2", "length": 31.34, "rotation": 118.37, "x": -13.51, "y": 4.55}, {"name": "mamian30", "parent": "mamian29", "length": 23.88, "rotation": -70.01, "x": 31.34, "color": "abe323ff"}, {"name": "mamian31", "parent": "mamian12", "length": 7.84, "rotation": 75.38, "x": 14.98, "y": 0.12}, {"name": "mamian32", "parent": "mamian10", "length": 16.81, "rotation": 104.47, "x": 18.89, "y": 0.05}, {"name": "target1", "parent": "mamian", "rotation": 178.85, "x": 20.25, "y": -27.86, "color": "ff3f00ff"}, {"name": "target2", "parent": "mamian", "rotation": 178.85, "x": 17.78, "y": -13.03, "color": "ff3f00ff"}, {"name": "target3", "parent": "target2", "x": 2.97, "y": -0.24, "transform": "noScale", "color": "ff3f00ff"}, {"name": "target11", "parent": "root", "x": -10.03, "y": -131.59, "color": "ff3f00ff"}, {"name": "target22", "parent": "mamian", "rotation": 178.85, "x": -5.96, "y": -16.75, "color": "ff3f00ff"}, {"name": "target33", "parent": "target22", "x": 6.22, "y": -0.5, "transform": "noScale", "color": "ff3f00ff"}, {"name": "mamian33", "parent": "mamian22", "length": 25.09, "rotation": -124.55, "x": -6.95, "y": -13.62, "color": "e3e3e3ff"}, {"name": "mamian34", "parent": "mamian33", "length": 26.83, "rotation": -12.78, "x": 25.09, "color": "e3e3e3ff"}, {"name": "mamian35", "parent": "mamian34", "length": 50, "x": 28.09, "y": -0.29, "color": "e3e3e3ff"}, {"name": "mamian36", "parent": "mamian22", "length": 28.2, "rotation": 22.58, "x": 5.82, "y": 5.09, "color": "e3e3e3ff"}, {"name": "mamian37", "parent": "mamian26", "length": 19.32, "rotation": 155.9, "x": -7.91, "y": 0.69}, {"name": "mamian38", "parent": "mamian26", "length": 20.45, "rotation": -87.71, "x": 19.24, "y": -4.39, "transform": "noRotationOrReflection"}, {"name": "mamian39", "parent": "mamian38", "length": 16.06, "rotation": -6.38, "x": 20.45}, {"name": "mamian40", "parent": "mamian39", "length": 50, "x": 18.09, "y": -0.47}, {"name": "<PERSON><PERSON><PERSON>", "parent": "mamian", "rotation": 178.85, "x": 123.87, "y": -11.53, "color": "ff3f00ff"}, {"name": "fanren2", "parent": "fanren", "length": 63.21, "rotation": -179.11, "x": -2.71, "y": 95.94}, {"name": "fanren7", "parent": "fanren2", "length": 19.07, "rotation": -98.46, "x": -6.26, "y": 18.68}, {"name": "fanren8", "parent": "fanren7", "length": 22.05, "rotation": 17.65, "x": 19.07}, {"name": "fanren3", "parent": "fanren8", "length": 21.86, "rotation": -161.26, "x": 21.38, "y": 19.28}, {"name": "fanren4", "parent": "fanren3", "length": 26.91, "rotation": -26.71, "x": 21.97, "y": 0.2}, {"name": "fanren5", "parent": "fanren8", "length": 20.47, "rotation": -160.59, "x": 17.18, "y": -12.97}, {"name": "fanren6", "parent": "fanren5", "length": 26.19, "rotation": -21.93, "x": 20.36, "y": -0.2}, {"name": "fanren9", "parent": "fanren8", "length": 19.62, "rotation": -6.87, "x": 22.05}, {"name": "fanren10", "parent": "fanren2", "length": 15.31, "rotation": 70, "x": -7.39, "y": 25.32}, {"name": "fanren11", "parent": "fanren10", "length": 16.27, "rotation": -30.59, "x": 15.31}, {"name": "fanren12", "parent": "fanren11", "length": 16.59, "rotation": -50.63, "x": 16.27}, {"name": "fanren13", "parent": "fanren12", "length": 13.56, "rotation": 8.58, "x": 16.59}, {"name": "fanren14", "parent": "fanren9", "length": 19.25, "rotation": 144.18, "x": 70.62, "y": -3.64}, {"name": "fanren15", "parent": "fanren14", "length": 17.81, "rotation": 37.16, "x": 19.25}, {"name": "fanren16", "parent": "fanren15", "length": 17.16, "rotation": -10.29, "x": 17.81}, {"name": "fanren17", "parent": "fanren9", "length": 19.05, "rotation": -159.52, "x": 68.78, "y": -23.51}, {"name": "fanren18", "parent": "fanren17", "length": 17.87, "rotation": 13.24, "x": 19.05}, {"name": "fanren19", "parent": "fanren9", "length": 17.82, "rotation": 140.14, "x": 71.76, "y": 22.28}, {"name": "fanren20", "parent": "fanren19", "length": 24.51, "rotation": -11.64, "x": 17.82}, {"name": "fanren21", "parent": "fanren9", "length": 23.1, "rotation": 173.9, "x": 46.35, "y": 35.26, "color": "43ff00ff"}, {"name": "fanren22", "parent": "fanren21", "length": 21.52, "rotation": -9.32, "x": 23.1, "color": "43ff00ff"}, {"name": "fanren23", "parent": "fanren22", "length": 23.62, "rotation": -21.33, "x": 21.52, "color": "43ff00ff"}, {"name": "fanren24", "parent": "fanren9", "length": 25.9, "rotation": -174, "x": 45.36, "y": -23.51, "color": "43ff00ff"}, {"name": "fanren25", "parent": "fanren24", "length": 22.17, "rotation": 6.11, "x": 25.9, "color": "43ff00ff"}, {"name": "fanren26", "parent": "fanren25", "length": 23.77, "rotation": 30.36, "x": 22.21, "y": 0.15, "color": "43ff00ff"}, {"name": "niutou2", "parent": "<PERSON><PERSON><PERSON>", "length": 75.86, "rotation": 2.99, "x": -1.62, "y": -65.04}, {"name": "niutou3", "parent": "niutou2", "length": 18.81, "rotation": -92.89, "x": -0.84, "y": -3.21}, {"name": "niutou4", "parent": "niutou3", "length": 15.48, "rotation": 16.01, "x": 18.81}, {"name": "niutou5", "parent": "niutou4", "length": 16.36, "rotation": -14.9, "x": 15.48}, {"name": "niutou6", "parent": "niutou2", "length": 15.86, "rotation": 85.55, "x": 0.89, "y": 6.23}, {"name": "niutou7", "parent": "niutou2", "length": 17.24, "rotation": 121.47, "x": -18.73, "y": 20.21}, {"name": "niutou8", "parent": "niutou2", "length": 19.69, "rotation": 87.49, "x": 3.28, "y": 24.55}, {"name": "niutou9", "parent": "niutou2", "length": 18.46, "rotation": 40.92, "x": 34.71, "y": 21.32}, {"name": "niutou10", "parent": "niutou4", "length": 30.81, "rotation": 123.16, "x": 29.85, "y": 41.83}, {"name": "niutou11", "parent": "niutou10", "length": 45.22, "rotation": 99.86, "x": 30.61, "y": 0.21}, {"name": "niutou12", "parent": "niutou4", "length": 9.24, "rotation": 129.37, "x": 10.59, "y": -28.91, "color": "abe323ff"}, {"name": "niutou13", "parent": "niutou12", "length": 14.78, "rotation": 91.96, "x": 8.89, "y": 0.47}, {"name": "niutou14", "parent": "niutou4", "length": 19.13, "rotation": 179.26, "x": 7.85, "y": -29.13}, {"name": "niutou15", "parent": "<PERSON><PERSON><PERSON>", "length": 179.48, "rotation": -145.87, "x": 60.59, "y": -29.01}, {"name": "shou1", "parent": "niutou15", "rotation": -148.59, "x": 114.23, "y": -4.1, "color": "ff3f00ff"}, {"name": "shou2", "parent": "niutou15", "rotation": -148.59, "x": 44.61, "y": 2.59, "color": "ff3f00ff"}, {"name": "niutou16", "parent": "niutou2", "length": 19.63, "rotation": 124.26, "x": -8.36, "y": 24.77}, {"name": "niutou17", "parent": "niutou16", "length": 18.4, "rotation": -68.35, "x": 19.56, "y": 0.41}, {"name": "niutou18", "parent": "niutou17", "length": 9.76, "rotation": 115.07, "x": 18.4}, {"name": "niutou19", "parent": "niutou2", "length": 19.44, "rotation": 89.3, "x": 25.75, "y": 21.64}, {"name": "niutou20", "parent": "niutou19", "length": 17.99, "rotation": -51.94, "x": 19.44}, {"name": "niutou21", "parent": "niutou20", "length": 12.14, "rotation": 115.49, "x": 17.99}, {"name": "niutou22", "parent": "niutou2", "length": 25.06, "rotation": 107.31, "x": 21.74, "y": 21.66}, {"name": "niutou23", "parent": "niutou22", "length": 25.37, "rotation": -93.7, "x": 25.06, "color": "abe323ff"}, {"name": "niutou24", "parent": "niutou2", "length": 24.43, "rotation": 143.63, "x": -11.63, "y": 24.49}, {"name": "niutou25", "parent": "niutou24", "length": 26.28, "rotation": -105.08, "x": 24.34, "y": 0.12, "color": "abe323ff"}, {"name": "jio1", "parent": "<PERSON><PERSON><PERSON>", "rotation": -2.73, "x": -23.38, "y": -25.25, "color": "ff3f00ff"}, {"name": "io2", "parent": "<PERSON><PERSON><PERSON>", "rotation": -2.73, "x": 32.17, "y": -10.26, "color": "ff3f00ff"}, {"name": "jio2", "parent": "<PERSON><PERSON><PERSON>", "rotation": -2.73, "x": -14.4, "y": -9.8, "color": "ff3f00ff"}, {"name": "jio3", "parent": "jio2", "rotation": -5.45, "x": -10.36, "y": 0.67, "transform": "noScale", "color": "ff3f00ff"}, {"name": "io1", "parent": "<PERSON><PERSON><PERSON>", "rotation": -2.73, "x": 19.77, "y": -22.83, "color": "ff3f00ff"}, {"name": "io3", "parent": "io2", "x": -9.98, "y": 3.71, "transform": "noScale", "color": "ff3f00ff"}, {"name": "mingzu2", "parent": "root", "x": -28.55, "y": -3.86, "scaleX": 0.6797, "scaleY": 0.6797}, {"name": "mingzu3", "parent": "all", "rotation": -0.45, "x": -355.01, "y": 537.98, "scaleX": 1.5979, "scaleY": 1.5979}, {"name": "mingzu4", "parent": "root", "x": -16.39, "y": -3.48, "scaleX": 0.6797, "scaleY": 0.6797}, {"name": "bone2", "parent": "root", "length": 60.39, "rotation": -0.47, "x": 148.41, "y": 174.91}, {"name": "bone5", "parent": "bone2", "length": 33.89, "rotation": 3.31, "x": -5.31, "y": 38.49}, {"name": "bone6", "parent": "bone5", "length": 13.38, "rotation": 102.55, "x": -2.89, "y": 5.95}, {"name": "bone7", "parent": "bone6", "length": 10.86, "rotation": -4.47, "x": 13.38}, {"name": "bone8", "parent": "bone5", "length": 8.99, "rotation": -75.92, "x": -0.3, "y": -5.98}, {"name": "bone9", "parent": "bone8", "length": 12.47, "rotation": -3.93, "x": 8.99}, {"name": "bone10", "parent": "bone5", "length": 8.51, "rotation": -39.59, "x": 14.32, "y": 8.53}, {"name": "bone11", "parent": "bone5", "length": 8.41, "rotation": -109.42, "x": -19.38, "y": 3.57}, {"name": "bone3", "parent": "root", "length": 86.59, "rotation": 0.43, "x": 132.55, "y": -103.65}, {"name": "bone20", "parent": "bone3", "length": 36.47, "rotation": 178.18, "x": 14.22, "y": 43.17}, {"name": "bone21", "parent": "bone20", "length": 12.14, "rotation": -104.1, "x": -0.44, "y": -3.55}, {"name": "bone22", "parent": "bone21", "length": 17.16, "rotation": 9.36, "x": 12.14}, {"name": "bone23", "parent": "bone20", "length": 10.35, "rotation": -149.26, "x": -11.25, "y": 0.42}, {"name": "bone24", "parent": "bone23", "length": 11.87, "rotation": 23.36, "x": 10.35}, {"name": "bone25", "parent": "bone20", "length": 10.58, "rotation": -41.45, "x": 13.11, "y": -3.79}, {"name": "bone26", "parent": "bone25", "length": 11.46, "rotation": -12.69, "x": 10.58}, {"name": "bone27", "parent": "bone20", "length": 12.61, "rotation": 85.61, "x": 1.29, "y": 6.65}, {"name": "bone28", "parent": "bone27", "length": 12.13, "rotation": -11.82, "x": 12.61}, {"name": "bone29", "parent": "bone28", "length": 11.29, "rotation": -30.95, "x": 12.13}, {"name": "bone4", "parent": "root", "length": 99.97, "rotation": 2.31, "x": 49.31, "y": 88.55, "scaleX": -1}, {"name": "bone12", "parent": "bone4", "length": 24.55, "rotation": -1.76, "x": -6.89, "y": 28.81}, {"name": "bone13", "parent": "bone12", "length": 9.68, "rotation": 91.54, "x": -4.35, "y": 1.93}, {"name": "bone14", "parent": "bone13", "length": 14.33, "rotation": -6.82, "x": 9.68}, {"name": "bone15", "parent": "bone12", "length": 8.62, "rotation": -60.13, "x": -4.4, "y": -3.5}, {"name": "bone16", "parent": "bone15", "length": 10.77, "rotation": 33.57, "x": 8.62}, {"name": "bone17", "parent": "bone16", "length": 14.09, "rotation": 21.2, "x": 10.77}, {"name": "bone18", "parent": "bone12", "length": 14.94, "rotation": 43.49, "x": 13.53, "y": 8.25}, {"name": "bone19", "parent": "bone12", "length": 14.14, "rotation": 144.3, "x": -24.03, "y": 4.95}, {"name": "bone30", "parent": "root", "length": 99.97, "rotation": 2.31, "x": 499.36, "y": 202.89}, {"name": "bone31", "parent": "bone30", "length": 24.55, "rotation": -1.76, "x": -6.89, "y": 28.81}, {"name": "bone32", "parent": "bone31", "length": 9.68, "rotation": 91.54, "x": -4.35, "y": 1.93}, {"name": "bone33", "parent": "bone32", "length": 14.33, "rotation": -6.82, "x": 9.68}, {"name": "bone34", "parent": "bone31", "length": 8.62, "rotation": -60.13, "x": -4.4, "y": -3.5}, {"name": "bone35", "parent": "bone34", "length": 10.77, "rotation": 33.57, "x": 8.62}, {"name": "bone36", "parent": "bone35", "length": 14.09, "rotation": 21.2, "x": 10.77}, {"name": "bone37", "parent": "bone31", "length": 14.94, "rotation": 43.49, "x": 13.53, "y": 8.25}, {"name": "bone38", "parent": "bone31", "length": 14.14, "rotation": 144.3, "x": -24.03, "y": 4.95}, {"name": "bone39", "parent": "root", "length": 60.39, "rotation": -0.47, "x": -353.88, "y": -140.99, "scaleX": -1}, {"name": "bone40", "parent": "bone39", "length": 33.89, "rotation": 3.31, "x": -5.31, "y": 38.49}, {"name": "bone41", "parent": "bone40", "length": 13.38, "rotation": 102.55, "x": -2.89, "y": 5.95}, {"name": "bone42", "parent": "bone41", "length": 10.86, "rotation": -4.47, "x": 13.38}, {"name": "bone43", "parent": "bone40", "length": 8.99, "rotation": -75.92, "x": -0.3, "y": -5.98}, {"name": "bone44", "parent": "bone43", "length": 12.47, "rotation": -3.93, "x": 8.99}, {"name": "bone45", "parent": "bone40", "length": 8.51, "rotation": -39.59, "x": 14.32, "y": 8.53}, {"name": "bone46", "parent": "bone40", "length": 8.41, "rotation": -109.42, "x": -19.38, "y": 3.57}], "slots": [{"name": "sd", "bone": "mingzu2", "color": "ffffff52"}, {"name": "sd6", "bone": "mingzu4", "color": "ffffff52"}, {"name": "sd3", "bone": "mingzu3", "color": "ffffff52"}, {"name": "sd5", "bone": "mingzu3", "color": "ffffff52"}, {"name": "sd2", "bone": "mingzu2", "color": "ffffff52"}, {"name": "sd4", "bone": "mingzu3", "color": "ffffff52"}, {"name": "bwc01", "bone": "bwc24"}, {"name": "bwc02", "bone": "bwc"}, {"name": "bwc03", "bone": "bwc"}, {"name": "bwc04", "bone": "bwc11"}, {"name": "bwc05", "bone": "bwc9"}, {"name": "bwc06", "bone": "bwc7"}, {"name": "bwc07", "bone": "bwc"}, {"name": "bwc08", "bone": "bwc4"}, {"name": "bwc09", "bone": "bwc"}, {"name": "bwc010", "bone": "bwc"}, {"name": "bwc011", "bone": "bwc26", "color": "ffb10072", "blend": "additive"}, {"name": "fanren01", "bone": "fanren"}, {"name": "fanren02", "bone": "fanren"}, {"name": "fanren03", "bone": "fanren"}, {"name": "fanren04", "bone": "fanren"}, {"name": "fanren05", "bone": "fanren"}, {"name": "fanren06", "bone": "fanren9"}, {"name": "fanren07", "bone": "fanren9"}, {"name": "fanren08", "bone": "fanren"}, {"name": "hwc02", "bone": "hwc"}, {"name": "hwc03", "bone": "hwc"}, {"name": "hwc04", "bone": "hwc11"}, {"name": "hwc05", "bone": "hwc"}, {"name": "hwc06", "bone": "hwc"}, {"name": "hwc07", "bone": "hwc22"}, {"name": "hwc08", "bone": "hwc4"}, {"name": "hwc09", "bone": "hwc"}, {"name": "hwc010", "bone": "hwc"}, {"name": "mamian01", "bone": "mamian"}, {"name": "mamian02", "bone": "mamian"}, {"name": "mamian07", "bone": "mamian25"}, {"name": "mamian03", "bone": "mamian"}, {"name": "mamian04", "bone": "mamian"}, {"name": "mamian05", "bone": "mamian20"}, {"name": "mamian06", "bone": "mamian"}, {"name": "mamian08", "bone": "mamian"}, {"name": "mamian09", "bone": "mamian"}, {"name": "mamian010", "bone": "mamian"}, {"name": "mamian011", "bone": "mamian26"}, {"name": "mamian012", "bone": "mamian26"}, {"name": "mamian013", "bone": "mamian22"}, {"name": "niutou01", "bone": "<PERSON><PERSON><PERSON>"}, {"name": "niutou02", "bone": "<PERSON><PERSON><PERSON>"}, {"name": "niutou03", "bone": "niutou7"}, {"name": "niutou04", "bone": "niutou8"}, {"name": "niutou05", "bone": "niutou9"}, {"name": "niutou06", "bone": "niutou14"}, {"name": "niutou07", "bone": "<PERSON><PERSON><PERSON>"}, {"name": "niutou08", "bone": "niutou11"}, {"name": "niutou09", "bone": "niutou10"}, {"name": "niutou010", "bone": "niutou15"}, {"name": "niutou011", "bone": "niutou5"}, {"name": "qiao", "bone": "all", "color": "ffc095ff", "dark": "212121"}, {"name": "a25", "bone": "mpmingzu28"}, {"name": "a24", "bone": "mpmingzu82"}, {"name": "a23", "bone": "mpmingzu69"}, {"name": "a26", "bone": "mpmingzu76", "color": "ffffff00"}, {"name": "a22", "bone": "mpmingzu61"}, {"name": "a21", "bone": "mpmingzu52"}, {"name": "a20", "bone": "mpmingzu81"}, {"name": "a19", "bone": "mpmingzu39"}, {"name": "a18", "bone": "mpmingzu5"}, {"name": "a17", "bone": "mpmingzu26"}, {"name": "a16", "bone": "mpmingzu8"}, {"name": "a15", "bone": "mpmingzu7"}, {"name": "a14", "bone": "mpmingzu7"}, {"name": "a13", "bone": "mpmingzu7", "color": "ffffff00"}, {"name": "a8", "bone": "mpmingzu44"}, {"name": "a12", "bone": "mpmingzu11"}, {"name": "a11", "bone": "mpmingzu14"}, {"name": "a10", "bone": "mpmingzu25"}, {"name": "a9", "bone": "mpmingzu19"}, {"name": "a7", "bone": "mpmingzu45"}, {"name": "a6", "bone": "mpmingzu46"}, {"name": "a5", "bone": "a5"}, {"name": "a4", "bone": "a5"}, {"name": "a3", "bone": "mpmingzu46"}, {"name": "a2", "bone": "mpmingzu46"}, {"name": "a1", "bone": "a1"}, {"name": "a0", "bone": "mpmingzu38"}, {"name": "tangg", "bone": "all"}, {"name": "yw/wenli_00", "bone": "ywyw", "color": "61ff00ff", "blend": "additive"}, {"name": "kulou", "bone": "ming<PERSON>"}, {"name": "g1", "bone": "bone3"}, {"name": "g2", "bone": "bone4"}, {"name": "g4", "bone": "bone30"}, {"name": "g5", "bone": "bone39"}, {"name": "g3", "bone": "bone2"}], "ik": [{"name": "io1", "order": 20, "bones": ["niutou19"], "target": "io1", "compress": true, "stretch": true}, {"name": "io2", "order": 21, "bones": ["niutou20"], "target": "io2", "compress": true, "stretch": true}, {"name": "io3", "order": 22, "bones": ["niutou21"], "target": "io3"}, {"name": "io5", "order": 18, "bones": ["niutou22", "niutou23"], "target": "io2", "bendPositive": false}, {"name": "jio1", "order": 15, "bones": ["niutou16"], "target": "jio1", "compress": true, "stretch": true}, {"name": "jio2", "order": 16, "bones": ["niutou17"], "target": "jio2", "compress": true, "stretch": true}, {"name": "jio3", "order": 17, "bones": ["niutou18"], "target": "jio3"}, {"name": "jio4", "order": 13, "bones": ["niutou24", "niutou25"], "target": "jio2", "bendPositive": false}, {"name": "<PERSON><PERSON><PERSON>", "order": 10, "bones": ["mamian34", "mamian35"], "target": "<PERSON><PERSON><PERSON>"}, {"name": "shou1", "order": 11, "bones": ["niutou12", "niutou13"], "target": "shou1"}, {"name": "shou2", "order": 12, "bones": ["niutou10", "niutou11"], "target": "shou2"}, {"name": "target1", "order": 2, "bones": ["mamian11"], "target": "target1", "compress": true, "stretch": true}, {"name": "target2", "order": 3, "bones": ["mamian12"], "target": "target2", "compress": true, "stretch": true}, {"name": "target3", "order": 4, "bones": ["mamian31"], "target": "target3"}, {"name": "target4", "bones": ["mamian27", "mamian28"], "target": "target2"}, {"name": "target11", "order": 7, "bones": ["mamian9"], "target": "target11", "compress": true}, {"name": "target22", "order": 8, "bones": ["mamian10"], "target": "target22", "compress": true}, {"name": "target33", "order": 9, "bones": ["mamian32"], "target": "target33"}, {"name": "target44", "order": 5, "bones": ["mamian29", "mamian30"], "target": "target22", "bendPositive": false}], "transform": [{"name": "io4", "order": 19, "bones": ["io1"], "target": "niutou23", "rotation": 14.68, "x": 8.69, "y": -7.08, "scaleX": 1.4044, "scaleY": 0.5957, "shearY": 180, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "jio1", "order": 1, "bones": ["target1"], "target": "mamian28", "rotation": 49.11, "x": 9.1, "y": 7.58, "scaleX": 0.6329, "scaleY": 0.6329, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "jio2", "order": 6, "bones": ["target11"], "target": "mamian30", "rotation": 132.7, "x": 6.72, "y": -7.68, "scaleX": 0.6329, "scaleY": 0.6329, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "jio5", "order": 14, "bones": ["jio1"], "target": "niutou25", "rotation": 43.58, "x": 9.38, "y": -5.82, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "shou31", "order": 23, "bones": ["niutou14"], "target": "niutou12", "rotation": -42.22, "x": 1.86, "y": 2.03, "shearY": 360, "rotateMix": 0.154, "translateMix": 0.154, "scaleMix": 0.154, "shearMix": 0.154}], "skins": [{"name": "default", "attachments": {"a17": {"a17": {"type": "mesh", "uvs": [0.60814, 0.01282, 0.73675, 0.08878, 0.91305, 0.30186, 1, 0.61496, 1, 0.68568, 0.91587, 0.83745, 0.60106, 0.98684, 0.26061, 0.98745, 0.02129, 0.84212, 0.02132, 0.76126, 0.15959, 0.72191, 0.5315, 0.77584, 0.69551, 0.73654, 0.76063, 0.64367, 0.71348, 0.50089, 0.47796, 0.19546, 0.47797, 0.01282], "triangles": [7, 11, 6, 14, 2, 3, 6, 12, 5, 6, 11, 12, 5, 12, 4, 4, 12, 13, 13, 3, 4, 13, 14, 3, 7, 8, 10, 8, 9, 10, 7, 10, 11, 14, 15, 2, 15, 1, 2, 15, 0, 1, 15, 16, 0], "vertices": [1, 14, 62.79, -24.14, 1, 1, 14, 58.56, -31.24, 1, 1, 14, 44.7, -42.9, 1, 1, 33, -3.08, 12.92, 1, 1, 33, 2.26, 12.31, 1, 1, 33, 13.28, 7.15, 1, 1, 33, 22.91, -8.53, 1, 1, 14, -12.97, -25.86, 1, 1, 14, -4.79, -12.58, 1, 1, 14, 1.18, -11.15, 1, 1, 14, 5.57, -16.64, 1, 1, 33, 6.61, -9.88, 1, 1, 33, 4.5, -2.05, 1, 1, 33, -2.17, 1.73, 1, 1, 33, -13.19, 0.82, 1, 1, 14, 47.9, -21.55, 1, 1, 14, 61.4, -18.32, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32], "width": 18, "height": 30}}, "sd3": {"sd": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.48, -8.74, -38.3, -8.74, -38.3, 22.83, 35.48, 22.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 201, "height": 86}}, "sd4": {"sd": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [109.98, -21.52, 48, -21.52, 48, 5, 109.98, 5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 201, "height": 86}}, "sd5": {"sd": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [200.9, -10.05, 127.12, -10.05, 127.12, 21.52, 200.9, 21.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 201, "height": 86}}, "tangg": {"tangg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-78.02, 498.43, -261.61, 499.88, -260.15, 683.48, -76.56, 682.02], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 153, "height": 153}}, "g1": {"g1": {"type": "mesh", "uvs": [0.30893, 0.26335, 0.33279, 0.15995, 0.39353, 0.05279, 0.47813, 0, 0.6213, 0, 0.73844, 0.02835, 0.79267, 0.12611, 0.79267, 0.21259, 0.78182, 0.27651, 0.85992, 0.22387, 0.95753, 0.21259, 1, 0.27275, 1, 0.40623, 0.99224, 0.50023, 0.92716, 0.59799, 0.87293, 0.62431, 0.7623, 0.70327, 0.66469, 0.76531, 0.56924, 0.72771, 0.54972, 0.84239, 0.49115, 0.92699, 0.38702, 1, 0.23735, 1, 0.29592, 0.91195, 0.31544, 0.80103, 0.30242, 0.70703, 0.23301, 0.75403, 0.18095, 0.66755, 0.1137, 0.58859, 0.04646, 0.53407, 0, 0.50211, 0, 0.36299, 0, 0.25019, 0.06598, 0.18251, 0.17444, 0.17123, 0.24169, 0.21823, 0.59438, 0.08366, 0.57629, 0.18689, 0.55066, 0.28227, 0.52503, 0.40771, 0.50392, 0.53054, 0.48734, 0.63376, 0.46321, 0.73699, 0.42703, 0.82323, 0.38632, 0.91731, 0.3034, 0.96566, 0.75118, 0.37766, 0.70746, 0.48742, 0.63509, 0.61547, 0.31245, 0.41817, 0.31094, 0.57496, 0.0878, 0.30057, 0.17223, 0.42078, 0.23405, 0.54099, 0.93814, 0.33977, 0.86426, 0.46913, 0.75721, 0.57758], "triangles": [31, 51, 52, 0, 51, 35, 0, 52, 51, 31, 32, 51, 32, 33, 51, 51, 34, 35, 51, 33, 34, 27, 53, 50, 27, 28, 53, 53, 28, 52, 40, 50, 49, 28, 29, 52, 50, 53, 49, 53, 52, 49, 29, 30, 52, 30, 31, 52, 52, 0, 49, 49, 0, 39, 22, 45, 21, 45, 44, 21, 21, 44, 20, 22, 23, 45, 45, 23, 44, 44, 23, 24, 44, 43, 20, 20, 43, 19, 43, 44, 24, 43, 42, 19, 43, 24, 42, 19, 42, 18, 24, 25, 42, 26, 27, 25, 42, 41, 18, 42, 25, 41, 17, 18, 48, 18, 41, 48, 25, 50, 41, 25, 27, 50, 41, 40, 48, 41, 50, 40, 16, 17, 48, 48, 56, 16, 16, 56, 15, 14, 15, 55, 48, 47, 56, 48, 40, 47, 15, 56, 55, 56, 47, 55, 47, 46, 55, 47, 39, 46, 14, 55, 13, 13, 55, 12, 55, 54, 12, 55, 46, 54, 54, 11, 12, 46, 8, 54, 8, 9, 54, 54, 10, 11, 54, 9, 10, 40, 39, 47, 40, 49, 39, 39, 38, 46, 39, 0, 38, 46, 38, 8, 0, 1, 38, 38, 37, 8, 38, 1, 37, 8, 37, 7, 7, 37, 6, 6, 37, 36, 36, 5, 6, 1, 2, 37, 37, 2, 36, 36, 2, 3, 36, 4, 5, 36, 3, 4], "vertices": [4, 276, 0.31, 15.63, 0.23775, 275, 9.9, 15.47, 0.16226, 279, 7.99, -10.48, 0.3659, 280, -0.23, -10.79, 0.23408, 4, 276, 8.18, 14.92, 0.7652, 275, 17.79, 16.05, 0.08348, 279, 12.12, -17.22, 0.12208, 280, 5.29, -16.46, 0.02924, 4, 276, 16.59, 11.85, 0.96875, 275, 26.59, 14.39, 0.004, 279, 14.69, -25.8, 0.02496, 280, 9.68, -24.26, 0.00229, 2, 276, 21.12, 6.81, 0.99613, 279, 13.35, -32.44, 0.00387, 2, 276, 22.11, -2.45, 0.99735, 278, 18.84, 29.05, 0.00265, 2, 276, 20.81, -10.24, 0.97215, 278, 21.76, 21.7, 0.02785, 4, 276, 13.9, -14.53, 0.90118, 275, 28.22, -12.08, 0.00033, 278, 18.06, 14.45, 0.09803, 277, 21.2, 20.43, 0.00046, 4, 276, 7.45, -15.22, 0.72333, 275, 21.97, -13.81, 0.02086, 278, 12.9, 10.53, 0.23951, 277, 18.02, 14.78, 0.0163, 4, 276, 2.61, -15.04, 0.31194, 275, 17.16, -14.41, 0.05064, 278, 8.66, 8.18, 0.58963, 277, 15.05, 10.95, 0.04779, 4, 276, 7.08, -19.66, 0.02842, 275, 22.32, -18.25, 0.00262, 278, 14.87, 6.54, 0.96882, 277, 21.41, 11.9, 0.00013, 2, 276, 8.59, -25.88, 0.00014, 278, 19.39, 2, 0.99986, 1, 278, 17.47, -2.93, 1, 2, 278, 9.5, -8.99, 0.97461, 277, 22.64, -4.49, 0.02539, 3, 278, 3.59, -12.86, 0.75495, 277, 18.75, -10.38, 0.24249, 281, -9.47, 33.34, 0.00256, 3, 278, -4.81, -13.94, 0.32958, 277, 11.47, -14.7, 0.62825, 281, -1.74, 29.87, 0.04217, 3, 278, -8.51, -12.33, 0.16847, 277, 7.43, -14.69, 0.73765, 281, 0.57, 26.56, 0.09388, 3, 278, -17.58, -10.19, 0.00586, 277, -1.74, -16.33, 0.65066, 281, 7.19, 20, 0.34348, 3, 277, -9.55, -17.27, 0.46585, 281, 12.46, 14.16, 0.53395, 282, -3.05, 13.82, 0.0002, 3, 277, -13.58, -11.77, 0.20938, 281, 10.28, 7.7, 0.68687, 282, -3.87, 7.06, 0.10375, 3, 277, -18.9, -18.65, 0.00362, 281, 18.96, 7.3, 0.0637, 282, 4.72, 8.45, 0.93268, 2, 282, 11.92, 6.74, 0.75458, 283, -3.65, 5.67, 0.24542, 1, 283, 5.05, 5.29, 1, 1, 283, 12.34, -1.15, 1, 2, 282, 14.68, -5.7, 0.08693, 283, 5.12, -3.58, 0.91307, 4, 281, 17.41, -8.16, 0.11311, 282, 6.36, -7.01, 0.80416, 283, -1.34, -8.97, 0.06427, 279, -19.74, 18.8, 0.01846, 3, 281, 10.48, -9.71, 0.59651, 282, -0.1, -9.94, 0.17193, 279, -14.33, 14.21, 0.23156, 3, 281, 14.44, -13.85, 0.58291, 282, 4.62, -13.18, 0.06068, 279, -13.42, 19.86, 0.35641, 4, 281, 8.33, -17.86, 0.48631, 282, -0.54, -18.36, 0.02307, 279, -6.53, 17.41, 0.48926, 280, -20.51, 13.23, 0.00135, 4, 281, 2.87, -22.81, 0.24304, 282, -4.86, -24.32, 0.00086, 279, 0.7, 16.04, 0.71369, 280, -13.15, 13.48, 0.04242, 3, 281, -0.75, -27.57, 0.09471, 279, 6.69, 16.01, 0.73354, 280, -7.31, 14.77, 0.17175, 3, 281, -2.84, -30.82, 0.05541, 279, 10.53, 16.31, 0.68109, 280, -3.62, 15.9, 0.26351, 3, 281, -13.22, -31.86, 0.00341, 279, 17.63, 8.66, 0.2274, 280, 4.98, 9.99, 0.76919, 1, 280, 11.95, 5.2, 1, 1, 280, 13.71, -1.2, 1, 4, 276, 6.24, 25.06, 0.00519, 275, 14.23, 25.74, 0.00255, 279, 19.09, -9.6, 0.00807, 280, 10.42, -7.49, 0.98419, 4, 276, 3.2, 20.34, 0.0404, 275, 12, 20.59, 0.03111, 279, 13.49, -9.99, 0.14753, 280, 5.04, -9.1, 0.78097, 2, 276, 15.69, -1.38, 0.99733, 278, 12.78, 26.64, 0.00267, 3, 276, 7.86, -1.03, 0.99448, 278, 5.91, 22.88, 0.00511, 277, 6.71, 23.35, 0.00041, 4, 276, 0.57, -0.14, 0.75279, 275, 12.73, -0.05, 0.24511, 278, -0.79, 19.88, 0.0011, 277, 1.75, 17.93, 0.001, 3, 275, 3.22, -0.95, 0.97294, 278, -9.28, 15.5, 0.00049, 277, -4.32, 10.55, 0.02657, 4, 275, -6.02, -2.09, 0.32474, 277, -10.03, 3.2, 0.12063, 281, -4.01, 1.99, 0.5436, 279, -14.93, -4.4, 0.01104, 2, 277, -14.77, -3.02, 0.03892, 281, 3.8, 1.69, 0.96108, 3, 277, -19.93, -9, 0.00547, 281, 11.66, 0.91, 0.89656, 282, -1.12, 0.7, 0.09797, 2, 277, -25.15, -13.49, 0, 282, 5.76, 0.41, 1, 2, 282, 13.28, 0.02, 0.04122, 283, 0.98, 0.61, 0.95878, 2, 282, 18.37, -4.02, 0.0001, 283, 7.42, -0.24, 0.9999, 4, 276, -5.15, -13.87, 0.07338, 275, 9.32, -14.52, 0.09428, 278, 1.41, 5.17, 0.46112, 277, 9.6, 5.31, 0.37122, 2, 277, 3.09, -0.47, 0.99752, 281, -8.56, 14.83, 0.00248, 3, 275, -9.88, -12.01, 0.00352, 277, -5.72, -6.54, 0.55775, 281, 1.47, 11.11, 0.43873, 5, 276, -11.21, 14.16, 0.00674, 275, -1.22, 12.15, 0.09887, 281, -11.14, -11.24, 0.00189, 279, -0.08, -2.12, 0.89066, 280, -9.93, -4.41, 0.00183, 4, 275, -12.58, 9.11, 0.01473, 281, 0.57, -10.16, 0.52112, 282, -9.71, -12.41, 0.00387, 279, -8, 6.57, 0.46029, 2, 279, 16.63, 1.34, 0.01157, 280, 5.61, 2.64, 0.98843, 3, 281, -10.03, -20.29, 0.01984, 279, 6.47, 4.22, 0.89639, 280, -4.93, 3.22, 0.08376, 5, 275, -11.46, 14.6, 0.00071, 281, -1.47, -15.39, 0.28037, 282, -10.63, -17.94, 0.0006, 279, -2.6, 8.1, 0.71342, 280, -14.64, 5.01, 0.00489, 1, 278, 11.03, -2.77, 1, 3, 278, 0.41, -4.83, 0.65418, 277, 12.64, -4.27, 0.34226, 281, -10.95, 24.83, 0.00355, 3, 278, -10.28, -4.22, 0.02545, 277, 2.59, -7.95, 0.84165, 281, -2.16, 18.72, 0.1329], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 8, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 16, 92, 92, 94, 94, 96, 0, 98, 98, 100, 66, 102, 102, 104, 104, 106, 20, 108, 108, 110, 110, 112], "width": 65, "height": 75}}, "niutou07": {"niutou07": {"type": "mesh", "uvs": [0.15938, 0.08211, 0.358, 0, 0.61596, 0, 0.84554, 0.12434, 0.97193, 0.3619, 0.97709, 0.56514, 0.9255, 0.69184, 1, 0.75255, 1, 0.84757, 0.82748, 0.94787, 0.50504, 1, 0.19549, 0.99275, 0, 0.903, 0.01492, 0.80534, 0.02524, 0.7024, 0, 0.53083, 0, 0.28535, 0.54625, 0.1848, 0.61358, 0.40269, 0.59529, 0.6097, 0.58467, 0.76466, 0.57523, 0.86498, 0.27668, 0.21754, 0.22925, 0.41457, 0.22027, 0.62068, 0.22859, 0.77699, 0.75771, 0.16735, 0.82917, 0.36239, 0.82168, 0.58438, 0.79243, 0.74115], "triangles": [10, 21, 9, 21, 29, 9, 21, 20, 29, 21, 10, 25, 12, 13, 11, 10, 11, 25, 11, 13, 25, 8, 29, 6, 8, 9, 29, 21, 25, 20, 6, 7, 8, 13, 14, 25, 14, 24, 25, 25, 24, 20, 20, 19, 29, 20, 24, 19, 29, 28, 6, 29, 19, 28, 14, 15, 24, 24, 23, 19, 24, 15, 23, 22, 0, 1, 17, 2, 26, 17, 1, 2, 26, 2, 3, 18, 22, 17, 15, 16, 23, 23, 16, 22, 18, 17, 26, 27, 26, 3, 16, 0, 22, 22, 1, 17, 6, 28, 5, 19, 23, 18, 18, 23, 22, 19, 18, 28, 28, 27, 5, 27, 4, 5, 28, 18, 27, 18, 26, 27, 27, 3, 4], "vertices": [4, 231, 43.49, 38.38, 0.12944, 232, 34.31, 30.09, 0.35819, 233, 10.46, 33.92, 0.44293, 234, -51.98, -38.57, 0.06944, 4, 231, 49.68, 20.58, 0.03301, 232, 35.35, 11.26, 0.16507, 233, 16.3, 15.99, 0.79065, 234, -58.65, -20.94, 0.01127, 4, 231, 48.56, -2.1, 3e-05, 232, 28.02, -10.22, 0.02493, 233, 14.74, -6.65, 0.97504, 234, -58.15, 1.75, 1e-05, 4, 231, 36.88, -21.75, 0.19705, 232, 11.37, -25.89, 0.47639, 233, 2.68, -26.07, 0.32656, 234, -47.01, 21.72, 0, 4, 231, 15.93, -31.85, 0.70198, 232, -11.56, -29.82, 0.22404, 233, -18.46, -35.77, 0.037, 234, -26.34, 32.38, 0.03697, 4, 231, -1.55, -31.44, 0.70181, 232, -28.25, -24.6, 0.04604, 233, -35.93, -35.02, 0.00085, 234, -8.86, 32.45, 0.25129, 3, 231, -12.21, -26.36, 0.38624, 232, -37.09, -16.78, 0.00397, 234, 1.94, 27.67, 0.60978, 3, 231, -17.75, -32.65, 0.22831, 232, -44.15, -21.3, 2e-05, 234, 7.3, 34.11, 0.77167, 2, 231, -25.91, -32.25, 0.18209, 234, 15.47, 33.93, 0.81791, 2, 231, -33.78, -16.66, 0.06261, 234, 23.76, 18.56, 0.93739, 3, 231, -36.85, 11.9, 6e-05, 232, -50.22, 26.79, 0.0054, 234, 27.61, -9.91, 0.99454, 3, 231, -34.89, 39.08, 0.05284, 232, -40.83, 52.37, 0.05911, 234, 26.39, -37.13, 0.88806, 4, 231, -26.33, 55.88, 0.09307, 232, -27.97, 66.16, 0.09327, 233, -59, 52.76, 0.00028, 234, 18.29, -54.16, 0.81338, 4, 231, -18, 54.15, 0.11515, 232, -20.45, 62.21, 0.11075, 233, -50.72, 50.88, 0.00303, 234, 9.92, -52.66, 0.77107, 4, 231, -9.21, 52.81, 0.16702, 232, -12.36, 58.49, 0.15843, 233, -41.95, 49.36, 0.01446, 234, 1.09, -51.55, 0.66009, 4, 231, 5.64, 54.3, 0.23711, 232, 2.32, 55.82, 0.26805, 233, -27.07, 50.56, 0.06336, 234, -13.71, -53.45, 0.43148, 4, 231, 26.73, 53.25, 0.22448, 232, 22.3, 49, 0.37558, 233, -6.01, 49.11, 0.19454, 234, -34.81, -52.98, 0.20539, 4, 231, 32.99, 4.82, 0.00171, 232, 14.96, 0.72, 0.78806, 233, -0.69, 0.56, 0.20999, 234, -42.4, -4.73, 0.00023, 2, 231, 13.98, -0.18, 0.99999, 233, -19.79, -4.06, 1e-05, 3, 231, -3.72, 2.31, 0.58322, 232, -21.02, 8.44, 0.00307, 234, -5.77, -1.22, 0.41372, 2, 232, -33.33, 13.63, 0.00109, 234, 7.53, -2.45, 0.99891, 2, 232, -41.23, 17.2, 0.00107, 234, 16.14, -3.47, 0.99893, 4, 231, 31.35, 28.65, 0.1631, 232, 19.95, 24.08, 0.41377, 233, -1.87, 24.42, 0.34796, 234, -40.1, -28.51, 0.07517, 4, 231, 14.63, 33.65, 0.30106, 232, 5.27, 33.5, 0.34579, 233, -18.49, 29.75, 0.10118, 234, -23.26, -33.06, 0.25197, 4, 231, -3.03, 35.32, 0.24205, 232, -11.25, 39.97, 0.17721, 233, -36.12, 31.76, 0.01838, 234, -5.55, -34.24, 0.56237, 4, 231, -16.5, 35.25, 0.12047, 232, -24.21, 43.62, 0.09256, 233, -49.58, 31.95, 0.00213, 234, 7.9, -33.8, 0.78485, 4, 231, 33.57, -13.84, 0.1399, 232, 10.37, -17.38, 0.49885, 233, -0.48, -18.11, 0.36125, 234, -43.48, 13.91, 0, 4, 231, 16.51, -19.3, 0.67297, 232, -7.54, -17.91, 0.26596, 233, -17.64, -23.23, 0.04529, 234, -26.58, 19.82, 0.01579, 3, 231, -2.53, -17.7, 0.67234, 232, -25.39, -11.12, 0.01566, 234, -7.5, 18.74, 0.312, 2, 231, -15.87, -14.46, 0.229, 234, 5.92, 15.87, 0.771], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 34, 36, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58], "width": 88, "height": 86}}, "niutou08": {"niutou08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [49.23, -6.94, 5.06, -30.37, -15.56, 8.5, 28.61, 31.93], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 50, "height": 44}}, "niutou09": {"niutou09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.88, 32.8, 47.45, -12.38, 8.97, -37.57, -20.61, 7.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 54, "height": 46}}, "bwc02": {"bwc02": {"type": "mesh", "uvs": [0.48668, 0, 0.69177, 0.02543, 0.85501, 0.17416, 0.91151, 0.3852, 0.8843, 0.63041, 0.8843, 0.85351, 1, 0.96606, 1, 1, 0.80687, 0.98214, 0.57876, 1, 0.37576, 1, 0.17904, 0.98214, 0, 0.95802, 0.06603, 0.7912, 0.01789, 0.55202, 0, 0.29275, 0.10998, 0.09578, 0.29833, 0.00533, 0.47125, 0.19066, 0.4558, 0.41857, 0.47546, 0.71121, 0.47124, 0.87843, 0.70434, 0.21359, 0.70995, 0.41183, 0.70153, 0.67075, 0.70855, 0.87034, 0.24939, 0.17178, 0.22411, 0.34979, 0.22832, 0.59793, 0.24798, 0.81639], "triangles": [10, 21, 9, 11, 29, 10, 10, 29, 21, 12, 13, 11, 11, 13, 29, 21, 29, 20, 13, 28, 29, 29, 28, 20, 13, 14, 28, 28, 19, 20, 14, 27, 28, 28, 27, 19, 14, 15, 27, 19, 27, 18, 18, 27, 26, 27, 15, 26, 15, 16, 26, 26, 17, 18, 18, 17, 0, 26, 16, 17, 8, 6, 7, 9, 25, 8, 9, 21, 25, 8, 5, 6, 8, 25, 5, 21, 20, 25, 20, 24, 25, 25, 24, 5, 24, 4, 5, 20, 19, 24, 24, 23, 4, 24, 19, 23, 4, 23, 3, 19, 22, 23, 19, 18, 22, 23, 22, 3, 22, 2, 3, 18, 1, 22, 22, 1, 2, 18, 0, 1], "vertices": [6, 120, -17.95, -13.09, 0.52221, 121, -29.15, -33.61, 0.01961, 122, -63.04, -27.25, 0, 117, -18.86, 19.03, 0.44042, 118, -26.55, 40.13, 0.01765, 119, -59.94, 37.4, 0.00011, 5, 120, -6.24, 3.2, 0.86428, 121, -27.83, -13.6, 0.01182, 117, -25.46, 37.97, 0.12155, 118, -21.69, 59.6, 0.00233, 119, -55.99, 57.07, 1e-05, 3, 120, 14.51, 9.99, 0.65987, 121, -13.83, 3.14, 0.33277, 117, -19.1, 58.86, 0.00736, 3, 120, 35.86, 4.67, 0.3339, 121, 7.11, 9.94, 0.45799, 122, -22.49, 12.33, 0.20811, 3, 120, 56.4, -9.42, 0.00057, 121, 31.99, 8.85, 0.45799, 122, 2.15, 8.68, 0.54144, 2, 121, 54.48, 10.26, 0.12522, 122, 24.66, 7.76, 0.87478, 1, 122, 36.48, 18.51, 1, 2, 122, 39.91, 18.37, 0.9972, 119, 44.29, 80.02, 0.0028, 4, 121, 67.91, 3.57, 0, 122, 37.34, -0.28, 0.89455, 118, 75.58, 59.49, 0, 119, 41.18, 61.46, 0.10544, 4, 121, 71.09, -18.4, 8e-05, 122, 38.24, -22.46, 0.66115, 118, 74.81, 37.3, 0, 119, 41.44, 39.26, 0.33876, 4, 121, 72.32, -38.05, 8e-05, 122, 37.43, -42.14, 0.34108, 118, 72.53, 17.74, 0, 119, 40.07, 19.62, 0.65884, 3, 121, 71.71, -57.21, 8e-05, 122, 34.85, -61.13, 0.11039, 119, 36.94, 0.71, 0.88953, 2, 122, 31.7, -78.38, 0.01046, 119, 33.29, -16.44, 0.98954, 2, 118, 48.1, -9.65, 0.31616, 119, 16.93, -8.88, 0.68384, 3, 117, 51.35, 3.3, 0.12604, 118, 23.56, -11.49, 0.52345, 119, -7.49, -11.85, 0.35051, 3, 117, 28.7, -9.97, 0.45938, 118, -2.65, -10.18, 0.52345, 119, -33.74, -11.76, 0.01718, 4, 120, -26.81, -49.83, 0.03698, 121, -17.22, -69.48, 1e-05, 117, 6.14, -9.32, 0.75572, 118, -21.17, 2.72, 0.20729, 4, 120, -26.16, -29.42, 0.17573, 121, -27.48, -51.81, 0.00275, 117, -10.2, 2.93, 0.81467, 118, -28.13, 21.93, 0.00684, 6, 120, -1.72, -23.56, 0.38515, 121, -9.84, -33.9, 0.1219, 122, -43.87, -29.54, 0.01286, 117, -0.96, 26.31, 0.33797, 118, -7.59, 36.42, 0.12298, 119, -40.83, 34.56, 0.01914, 6, 120, 17.82, -35.82, 0.20266, 121, 13.23, -33.96, 0.16387, 122, -20.93, -31.98, 0.13582, 117, 20.3, 35.26, 0.19138, 118, 15.1, 32.26, 0.15335, 119, -17.97, 31.46, 0.15292, 6, 120, 44.73, -48.19, 0.05344, 121, 42.61, -30.22, 0.14372, 122, 8.68, -31.28, 0.29164, 117, 45.88, 50.18, 0.05615, 118, 44.68, 30.73, 0.13331, 119, 11.64, 31.3, 0.32175, 6, 120, 59.39, -56.58, 0.00244, 121, 59.49, -29.57, 0.07194, 122, 25.54, -32.38, 0.41817, 117, 61.17, 57.37, 0.00253, 118, 61.4, 28.37, 0.0508, 119, 28.46, 29.72, 0.45412, 6, 120, 11.06, -4.77, 0.67639, 121, -8.94, -11.19, 0.2867, 122, -40.63, -7.04, 0.00099, 117, -9, 47.56, 0.02009, 118, -2.67, 58.61, 0.01314, 119, -36.94, 56.96, 0.00269, 6, 120, 28.94, -13.8, 0.35071, 121, 11.01, -9.4, 0.3445, 122, -20.6, -7.32, 0.25134, 117, 8.66, 57.01, 0.0132, 118, 17.28, 56.83, 0.01965, 119, -16.93, 56.1, 0.0206, 6, 120, 51.56, -26.95, 0.04175, 121, 37.16, -8.59, 0.32938, 122, 5.5, -9.2, 0.55754, 117, 32.42, 67.97, 0.00678, 118, 43.16, 52.98, 0.01755, 119, 9.1, 53.46, 0.047, 6, 120, 69.61, -35.94, 0.00044, 121, 57.24, -6.65, 0.08693, 122, 25.67, -9.35, 0.83482, 117, 50.14, 77.59, 0.00072, 118, 63.26, 51.33, 0.01059, 119, 29.25, 52.74, 0.0665, 6, 120, -13.63, -41.58, 0.05994, 121, -10.4, -55.5, 0.01393, 122, -46.65, -50.96, 0.0006, 117, 6.96, 6.2, 0.71743, 118, -11.98, 15.26, 0.20801, 119, -44.24, 13.23, 0.0001, 6, 120, 1.02, -52.29, 0.02449, 121, 7.7, -56.83, 0.02391, 122, -28.79, -54.15, 0.0087, 117, 24.13, 12.05, 0.42326, 118, 5.59, 10.74, 0.38781, 119, -26.47, 9.53, 0.13184, 6, 120, 23.26, -63.84, 0.01127, 121, 32.68, -54.86, 0.02331, 122, -3.73, -54.76, 0.031, 117, 46.37, 23.63, 0.11498, 118, 30.53, 8.25, 0.37976, 119, -1.44, 8.19, 0.43968, 6, 120, 43.58, -72.65, 0.0023, 121, 54.59, -51.58, 0.01886, 122, 18.4, -53.76, 0.04561, 117, 65.25, 35.2, 0.00326, 118, 52.67, 7.58, 0.27062, 119, 20.7, 8.55, 0.65936], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 2, 44, 44, 46, 46, 48, 48, 50, 34, 52, 52, 54, 54, 56, 56, 58], "width": 97, "height": 101}}, "bwc010": {"bwc010": {"type": "mesh", "uvs": [0, 0.45906, 0.03228, 0.67326, 0, 0.88322, 0.30012, 0.92719, 0.66192, 1, 0.84842, 0.93996, 0.95359, 0.81087, 1, 0.65766, 0.87366, 0.55552, 0.85123, 0.38671, 0.81897, 0.13845, 0.70539, 0, 0.47821, 0.00085, 0.3492, 0.24059, 0.24964, 0.41082, 0.58671, 0.11925, 0.50029, 0.31501, 0.43453, 0.50886, 0.37629, 0.66471, 0.34248, 0.81105, 0.69191, 0.13636, 0.6844, 0.35492, 0.69379, 0.53547, 0.67688, 0.71792, 0.66185, 0.87567, 0.8779, 0.74073], "triangles": [21, 17, 16, 17, 21, 22, 22, 9, 8, 22, 21, 9, 17, 14, 16, 16, 14, 13, 21, 10, 9, 21, 20, 10, 16, 15, 21, 21, 15, 20, 16, 13, 15, 13, 12, 15, 20, 11, 10, 20, 15, 11, 15, 12, 11, 4, 3, 24, 3, 19, 24, 4, 24, 5, 5, 25, 6, 5, 24, 25, 3, 2, 19, 2, 1, 19, 24, 19, 23, 24, 23, 25, 23, 19, 18, 19, 1, 18, 6, 25, 7, 23, 8, 25, 25, 8, 7, 18, 17, 23, 23, 17, 22, 23, 22, 8, 1, 14, 18, 1, 0, 14, 18, 14, 17], "vertices": [2, 123, 21.51, 51.43, 0.77512, 124, 1.91, 51.81, 0.22488, 2, 123, 4.04, 44.93, 0.92188, 124, -16.38, 48.23, 0.07812, 1, 123, -14.21, 43.99, 1, 1, 123, -12.58, 17.66, 1, 1, 123, -12.29, -14.43, 1, 1, 123, -3.93, -29.26, 1, 2, 123, 8.81, -35.96, 0.95018, 124, -24.83, -32.35, 0.04982, 2, 123, 22.53, -37.22, 0.81684, 124, -11.5, -35.83, 0.18316, 2, 123, 28.89, -24.67, 0.58567, 124, -3.19, -24.48, 0.41433, 2, 123, 42.7, -19.8, 0.30829, 124, 11.24, -21.92, 0.69171, 2, 123, 63.03, -12.7, 0.10966, 124, 32.45, -18.22, 0.89034, 2, 123, 72.67, -0.59, 0.02866, 124, 43.93, -7.84, 0.97134, 2, 123, 68.57, 18.74, 0.0956, 124, 43.03, 11.9, 0.9044, 2, 123, 46.1, 25.52, 0.2777, 124, 21.96, 22.25, 0.7223, 2, 123, 30, 31.02, 0.54013, 124, 6.97, 30.29, 0.45987, 2, 123, 60.53, 7.42, 0.13144, 124, 33.25, 2.04, 0.86856, 2, 123, 42.51, 11.35, 0.27344, 124, 16.12, 8.85, 0.72656, 2, 123, 25.02, 13.55, 0.55618, 124, -0.78, 13.86, 0.44382, 2, 123, 10.87, 15.78, 0.80325, 124, -14.38, 18.36, 0.19675, 1, 123, -2.05, 16.09, 1, 2, 123, 60.95, -1.84, 0.10892, 124, 32.17, -7.17, 0.89108, 2, 123, 42.42, -5.03, 0.25499, 124, 13.36, -7.3, 0.74501, 2, 123, 27.38, -9, 0.54681, 124, -2.12, -8.77, 0.45319, 2, 123, 11.72, -10.76, 0.80802, 124, -17.86, -7.96, 0.19198, 1, 123, -1.83, -12.24, 1, 2, 123, 13.37, -28.28, 0.97087, 124, -19.08, -25.51, 0.02913], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 30, 32, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 46, 48], "width": 87, "height": 86}}, "mamian013": {"mamian013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.32, -8.82, -7.83, -15.25, -14.53, 9.87, 9.63, 16.31], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 25, "height": 26}}, "a18": {"a18": {"type": "mesh", "uvs": [0.39166, 0.00719, 0.46058, 0.05406, 0.49733, 0.10476, 0.46076, 0.18402, 0.46052, 0.23894, 0.6066, 0.22876, 0.67308, 0.24094, 0.7754, 0.32008, 0.82724, 0.2726, 0.88796, 0.31414, 0.95517, 0.36418, 0.99215, 0.39877, 0.99246, 0.46354, 0.96136, 0.52028, 0.82533, 0.62602, 0.80038, 0.73883, 0.78718, 0.79852, 0.87476, 0.91632, 0.79227, 0.95925, 0.65515, 0.99163, 0.49311, 1, 0.3734, 0.98353, 0.37159, 0.88739, 0.21687, 0.7336, 0.22973, 0.65919, 0.07915, 0.52165, 0.03119, 0.43406, 0.03203, 0.35358, 0.00765, 0.2751, 0.00784, 0.20309, 0.03033, 0.16006, 0.07152, 0.12301, 0.14022, 0.12309, 0.2868, 0.17993, 0.29574, 0.11352, 0.31012, 0.00668, 0.38396, 0.17283, 0.39177, 0.25277, 0.43086, 0.3113, 0.51216, 0.4098, 0.59679, 0.50474, 0.62334, 0.58931, 0.62317, 0.6639, 0.6259, 0.75864, 0.63255, 0.83235, 0.62161, 0.31416, 0.62161, 0.35555, 0.65444, 0.38981, 0.7248, 0.42979, 0.7545, 0.47832, 0.77014, 0.54256, 0.85769, 0.37982, 0.84831, 0.45976, 0.81548, 0.50544, 0.1241, 0.23564, 0.21166, 0.35555, 0.27732, 0.46547, 0.37739, 0.51972, 0.41647, 0.65676, 0.45243, 0.78096, 0.46963, 0.8609], "triangles": [3, 36, 2, 1, 36, 34, 36, 1, 2, 1, 34, 0, 0, 34, 35, 33, 34, 36, 58, 23, 24, 59, 58, 42, 15, 42, 14, 50, 14, 42, 50, 42, 41, 42, 58, 41, 24, 57, 58, 24, 56, 57, 24, 25, 56, 58, 40, 41, 58, 57, 40, 50, 53, 14, 13, 53, 52, 13, 14, 53, 50, 40, 49, 50, 41, 40, 50, 49, 53, 25, 55, 56, 25, 26, 55, 13, 52, 12, 12, 52, 51, 11, 51, 10, 51, 11, 12, 57, 39, 40, 57, 38, 39, 37, 38, 55, 38, 57, 56, 53, 49, 52, 40, 48, 49, 40, 47, 48, 47, 39, 46, 47, 40, 39, 49, 48, 52, 37, 55, 33, 33, 36, 37, 55, 38, 56, 52, 48, 51, 26, 27, 55, 48, 7, 51, 48, 47, 7, 39, 38, 45, 46, 39, 45, 4, 45, 38, 7, 47, 45, 9, 51, 8, 51, 9, 10, 8, 51, 7, 27, 54, 55, 55, 54, 33, 45, 4, 5, 47, 46, 45, 27, 28, 54, 45, 6, 7, 45, 5, 6, 38, 37, 4, 28, 29, 54, 4, 37, 3, 29, 30, 54, 30, 31, 54, 54, 32, 33, 54, 31, 32, 3, 37, 36, 44, 16, 18, 23, 58, 59, 20, 60, 44, 21, 22, 60, 22, 23, 59, 22, 59, 60, 60, 59, 44, 59, 43, 44, 44, 43, 16, 16, 43, 15, 59, 42, 43, 43, 42, 15, 20, 21, 60, 44, 19, 20, 19, 44, 18, 18, 16, 17], "vertices": [2, 12, 95.86, -11.96, 0.00156, 13, 30.13, 2.37, 0.99844, 2, 12, 86.72, -17.75, 0.15005, 13, 24.72, -7.01, 0.84995, 2, 12, 78.53, -19.57, 0.13752, 13, 18.32, -12.43, 0.86248, 2, 12, 69.96, -11.35, 0.47085, 13, 6.91, -9.14, 0.52915, 2, 12, 62.9, -8.61, 0.80418, 13, -0.62, -10, 0.19582, 2, 12, 57.62, -26.3, 0.9, 42, 18.66, -15.05, 0.1, 2, 12, 53.05, -33.52, 0.9, 42, 14.09, -22.27, 0.1, 3, 12, 38.24, -41.65, 0.576, 43, 3.19, 11.51, 0.324, 42, -0.72, -30.4, 0.1, 3, 12, 42.02, -50.09, 0.576, 43, 6.58, 20.12, 0.324, 42, 3.06, -38.85, 0.1, 3, 12, 33.93, -55.18, 0.576, 43, 15.88, 17.9, 0.324, 42, -5.03, -43.94, 0.1, 3, 12, 24.45, -60.62, 0.576, 43, 26.39, 14.94, 0.324, 42, -14.51, -49.37, 0.1, 3, 12, 18.32, -63.26, 0.576, 43, 32.57, 12.41, 0.324, 42, -20.64, -52.02, 0.1, 3, 12, 9.96, -60.1, 0.576, 43, 36.16, 4.23, 0.324, 42, -29, -48.85, 0.1, 3, 12, 4.05, -53.64, 0.576, 43, 35.68, -4.51, 0.324, 42, -34.91, -42.39, 0.1, 3, 11, 28.87, -32.12, 0.3, 12, -3.44, -32.41, 0.6, 42, -42.4, -21.16, 0.1, 3, 11, 13.65, -27.58, 0.6, 12, -16.85, -23.9, 0.3, 42, -55.81, -12.66, 0.1, 3, 67, -8.88, 23.79, 0.3, 11, 5.6, -25.18, 0.6, 42, -62.9, -8.15, 0.1, 3, 67, 7.5, 34.64, 0.6, 11, -11.59, -34.7, 0.3, 42, -82.03, -12.64, 0.1, 2, 67, 13.31, 24.18, 0.816, 41, -52.76, -2.46, 0.184, 2, 67, 17.58, 6.85, 0.816, 41, -50.75, 15.27, 0.184, 2, 67, 18.51, -13.57, 0.816, 41, -44.52, 34.75, 0.184, 3, 67, 16.06, -28.63, 0.6, 11, -15.11, 29.05, 0.3, 42, -68.08, 49.67, 0.1, 3, 67, 2.79, -28.71, 0.3, 11, -1.88, 28.08, 0.6, 42, -55.61, 45.13, 0.1, 3, 11, 21.02, 45.57, 0.6, 12, 10.15, 44.49, 0.3, 42, -28.81, 55.74, 0.1, 3, 11, 31.1, 43.03, 0.3, 12, 19.15, 39.3, 0.6, 42, -19.81, 50.55, 0.1, 2, 12, 43.67, 50.22, 0.9, 42, 4.71, 61.47, 0.1, 2, 12, 57.12, 51.54, 0.9, 42, 18.16, 62.79, 0.1, 2, 12, 67.45, 47.47, 0.9, 42, 28.49, 58.71, 0.1, 2, 12, 78.66, 46.46, 0.9, 42, 39.7, 57.7, 0.1, 2, 12, 87.93, 42.88, 0.9, 42, 48.97, 54.12, 0.1, 2, 12, 92.46, 38.11, 0.9, 42, 53.5, 49.35, 0.1, 2, 12, 95.38, 31.43, 0.9, 42, 56.42, 42.67, 0.1, 2, 12, 92.27, 23.35, 0.9, 42, 53.31, 34.6, 0.1, 2, 12, 78.33, 8.91, 0.74875, 13, 4.9, 12.7, 0.25125, 2, 12, 86.49, 4.58, 0.41697, 13, 14.14, 12.65, 0.58303, 2, 12, 99.61, -2.39, 0.08364, 13, 28.99, 12.58, 0.91636, 3, 12, 74.87, -2.87, 0.47792, 13, 7.31, 0.65, 0.41807, 41, 66.99, 6.73, 0.104, 3, 12, 64.21, 0.16, 0.77659, 13, -3.53, -1.62, 0.11941, 41, 56.33, 9.76, 0.104, 2, 12, 54.91, -1.55, 0.704, 41, 47.03, 8.05, 0.296, 2, 12, 38.55, -6.25, 0.704, 41, 30.67, 3.35, 0.296, 2, 12, 22.5, -11.51, 0.704, 41, 14.62, -1.91, 0.296, 2, 12, 10.41, -10.46, 0.704, 41, 2.53, -0.86, 0.296, 3, 11, 25.97, -6.28, 0.23467, 12, 0.8, -6.75, 0.46933, 41, -7.08, 2.85, 0.296, 3, 11, 12.92, -5.44, 0.46933, 12, -11.53, -2.39, 0.23467, 41, -19.41, 7.2, 0.296, 2, 11, 2.71, -5.35, 0.704, 41, -29.21, 10.06, 0.296, 1, 12, 45.94, -23.85, 1, 2, 12, 40.6, -21.8, 0.848, 41, 32.72, -12.2, 0.152, 2, 12, 34.71, -23.97, 0.848, 41, 26.83, -14.37, 0.152, 2, 12, 26.38, -30.28, 0.864, 41, 18.5, -20.68, 0.136, 2, 12, 18.79, -31.37, 0.888, 41, 10.91, -21.77, 0.112, 2, 12, 9.81, -30.04, 0.872, 41, 1.93, -20.44, 0.128, 3, 12, 26.83, -48.38, 0.576, 43, 15.99, 8.07, 0.324, 42, -12.13, -37.13, 0.1, 3, 12, 16.95, -43.33, 0.576, 43, 19.29, -2.52, 0.324, 42, -22.01, -32.08, 0.1, 3, 12, 12.55, -37.21, 0.576, 43, 18, -9.95, 0.324, 42, -26.41, -25.96, 0.1, 2, 12, 78.49, 30.81, 0.816, 41, 70.61, 40.41, 0.184, 2, 12, 59.09, 26.43, 0.744, 41, 51.21, 36.03, 0.256, 2, 12, 41.97, 24.13, 0.696, 41, 34.09, 33.73, 0.304, 2, 12, 30.47, 15.04, 0.696, 41, 22.59, 24.64, 0.304, 3, 11, 29.31, 19.57, 0.232, 12, 11.04, 17.21, 0.464, 41, 3.16, 26.81, 0.304, 3, 11, 11.83, 16.61, 0.464, 12, -6.58, 19.12, 0.232, 41, -14.46, 28.72, 0.304, 2, 11, 0.65, 15.45, 0.696, 41, -25.54, 30.64, 0.304], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 46, 48, 2, 72, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 10, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 16, 102, 102, 104, 104, 106, 106, 100, 60, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 38, 40, 10, 12, 12, 14, 14, 16, 28, 30, 30, 32, 40, 42], "width": 50, "height": 55}}, "a13": {"a13": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [0, 1, 9, 1, 8, 9, 1, 2, 8, 2, 7, 8, 2, 3, 7, 3, 6, 7, 3, 4, 6, 4, 5, 6], "vertices": [3, 14, 13.88, -38.98, 0.82933, 39, 11.22, -27.95, 0.08533, 40, -27.37, -29.08, 0.08533, 3, 14, 10.96, -26.82, 0.78667, 39, 8.3, -15.79, 0.17067, 40, -30.29, -16.92, 0.04267, 2, 14, 8.05, -14.67, 0.744, 39, 5.39, -3.64, 0.256, 2, 14, 5.13, -2.51, 0.78667, 39, 2.47, 8.52, 0.21333, 2, 14, 2.22, 9.64, 0.82933, 39, -0.44, 20.67, 0.17067, 2, 14, 21.67, 14.31, 0.82933, 39, 19.01, 25.34, 0.17067, 2, 14, 24.58, 2.15, 0.78667, 39, 21.92, 13.18, 0.21333, 2, 14, 27.5, -10.01, 0.744, 39, 24.84, 1.03, 0.256, 3, 14, 30.41, -22.16, 0.78667, 39, 27.75, -11.13, 0.17067, 40, -10.84, -12.26, 0.04267, 3, 14, 33.33, -34.32, 0.82933, 39, 30.66, -23.28, 0.08533, 40, -7.92, -24.42, 0.08533], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 20, "height": 8}}, "fanren08": {"fanren08": {"type": "mesh", "uvs": [0, 0.70665, 0.14657, 0.79308, 0.26992, 0.71108, 0.35543, 0.85957, 0.43673, 1, 0.5783, 0.92384, 0.62876, 0.72217, 0.67642, 0.86844, 0.7465, 0.71995, 0.84602, 0.82633, 1, 0.80195, 0.94414, 0.66898, 1, 0.58919, 0.93713, 0.49833, 0.84462, 0.25676, 0.73949, 0.07946, 0.60353, 0.00411, 0.37785, 0.00411, 0.22086, 0.11714, 0.11293, 0.33655, 0.04845, 0.52714, 0.0891, 0.54487, 0, 0.66898, 0.61083, 0.23421, 0.70956, 0.28424, 0.80576, 0.46436, 0.86398, 0.60245, 0.4121, 0.25222, 0.28805, 0.3743, 0.20071, 0.57243, 0.11464, 0.64848, 0.13869, 0.42633, 0.2197, 0.28223, 0.40703, 0.17817, 0.91208, 0.56042, 0.82475, 0.40232, 0.85133, 0.71452, 0.74247, 0.54041, 0.46146, 0.82259, 0.43741, 0.54441, 0.68677, 0.71652, 0.63994, 0.51639], "triangles": [7, 40, 8, 7, 6, 40, 9, 36, 10, 9, 8, 36, 36, 11, 10, 6, 41, 40, 36, 8, 37, 8, 40, 37, 40, 41, 37, 36, 26, 11, 36, 37, 26, 26, 34, 11, 11, 34, 12, 37, 25, 26, 26, 25, 34, 34, 13, 12, 13, 34, 35, 34, 25, 35, 25, 37, 24, 37, 41, 24, 41, 23, 24, 35, 14, 13, 25, 24, 35, 35, 24, 14, 24, 15, 14, 24, 23, 15, 4, 38, 5, 4, 3, 38, 5, 38, 6, 38, 3, 39, 38, 39, 6, 3, 2, 39, 6, 39, 41, 39, 2, 28, 39, 23, 41, 39, 27, 23, 27, 33, 23, 33, 16, 23, 23, 16, 15, 33, 17, 16, 0, 30, 1, 1, 29, 2, 1, 30, 29, 30, 22, 21, 30, 0, 22, 30, 21, 29, 21, 31, 29, 2, 29, 28, 28, 29, 31, 21, 20, 31, 20, 19, 31, 31, 19, 32, 28, 31, 32, 28, 27, 39, 27, 28, 33, 32, 19, 18, 28, 32, 33, 32, 18, 33, 18, 17, 33], "vertices": [2, 223, 33.62, -2.82, 1, 219, 6.36, -54.22, 0, 3, 223, 24.88, 13.26, 0.96475, 218, 21.91, -37.81, 0.01906, 219, 10.79, -36.46, 0.01619, 4, 222, 31.29, 15.76, 0.01829, 223, 10.01, 18.15, 0.56187, 218, 16.83, -23, 0.20968, 219, 3.15, -22.8, 0.21015, 4, 222, 33.96, 30.38, 0.00036, 223, 9.67, 33.01, 0.07914, 218, 28.47, -13.76, 0.07271, 219, 12.94, -11.63, 0.8478, 1, 219, 22.21, -1.02, 1, 4, 218, 34.97, 11.94, 0.00999, 219, 14.75, 14.81, 0.9556, 220, 38.96, -34.18, 0.00702, 221, 11.55, -37.83, 0.02739, 5, 217, 24.2, 27.38, 0.02596, 218, 20.48, 18.83, 0.23698, 219, -0.74, 19.01, 0.30863, 220, 27.53, -22.91, 0.11198, 221, 3.01, -24.25, 0.31645, 4, 218, 31.66, 23.67, 0.17096, 219, 9.39, 25.76, 0.15125, 220, 39.67, -22.01, 0.07675, 221, 15.03, -26.15, 0.60105, 5, 217, 16.5, 38.81, 0.00415, 218, 21.25, 32.59, 0.08248, 219, -2.45, 32.68, 0.0601, 220, 32.76, -10.17, 0.05679, 221, 11.02, -13.04, 0.79647, 3, 218, 29.88, 43.68, 0.00141, 219, 4.07, 45.13, 0.00099, 221, 24.26, -8.3, 0.9976, 1, 221, 33.45, 7.29, 1, 1, 221, 21.65, 7.84, 1, 2, 220, 35.45, 20.91, 0.00023, 221, 20.76, 16.6, 0.99977, 2, 220, 26.39, 16.77, 0.09162, 221, 10.99, 14.65, 0.90838, 3, 220, 5.7, 13.8, 0.73999, 221, -9.83, 16.49, 0.04401, 212, 68.27, -38.43, 0.216, 3, 217, -22.67, 12.1, 0.11666, 220, -11.18, 7.6, 0.66734, 212, 81.92, -26.72, 0.216, 4, 222, -33.98, 15.07, 0.02881, 217, -18.6, -4.25, 0.54945, 220, -22.53, -4.86, 0.20574, 212, 88.19, -11.07, 0.216, 3, 222, -17.97, -5.92, 0.51346, 217, -4.1, -26.32, 0.27054, 212, 89.35, 15.31, 0.216, 3, 222, -0.18, -15.46, 0.7709, 223, -14.51, -18.77, 0.0131, 212, 81.81, 34.03, 0.216, 2, 222, 20.39, -15.65, 0.31381, 223, 5.67, -14.81, 0.68619, 1, 223, 20.62, -9.12, 1, 1, 223, 17.9, -5.01, 1, 2, 223, 31.79, -4.93, 1, 219, 3.59, -54.53, 0, 3, 217, -4.83, 5.81, 0.65701, 218, -15.69, 19.18, 0.0021, 220, -6.53, -10.73, 0.34089, 3, 217, -8.08, 17.49, 0.03043, 218, -11.21, 30.45, 0.00129, 220, 1.4, -1.54, 0.96828, 2, 220, 18.06, 3.61, 0.57834, 221, -0.13, 3.74, 0.42166, 2, 220, 30.13, 5.88, 0.00528, 221, 12.14, 3.19, 0.99472, 3, 222, -5.8, 8.4, 0.47774, 217, 9.04, -12.9, 0.5219, 218, -15.92, -4.11, 0.00035, 4, 222, 10.18, 2.33, 0.95618, 217, 24.56, -20.07, 0.01515, 218, -7.89, -19.2, 0.02804, 219, -21.86, -23.49, 0.00062, 4, 222, 28.04, 3.1, 0.00109, 223, 9.38, 5.1, 0.9372, 218, 6.05, -30.39, 0.04055, 219, -6.14, -32, 0.02117, 3, 223, 20.67, 2.73, 0.99453, 218, 10.99, -40.81, 0.00298, 219, 0.58, -41.38, 0.0025, 2, 222, 23.84, -9.23, 0.11857, 223, 7.76, -7.82, 0.88143, 2, 222, 9.62, -8.16, 0.88612, 223, -6.39, -9.64, 0.11388, 2, 222, -9.8, 4.6, 0.55787, 217, 4.79, -16.4, 0.44213, 2, 220, 29.47, 12.28, 0.03666, 221, 12.96, 9.56, 0.96334, 2, 220, 14.71, 7.45, 0.75385, 221, -2.52, 8.24, 0.24615, 3, 218, 21.67, 44.85, 0.00187, 219, -4.22, 44.82, 0.00128, 221, 17.95, -2.91, 0.99685, 5, 217, 5.65, 31.12, 0.01933, 218, 7.96, 33.02, 0.04146, 219, -15.6, 30.72, 0.01546, 220, 20.35, -5.41, 0.40884, 221, 0.03, -5.56, 0.5149, 3, 219, 8.84, 0.39, 0.99958, 220, 26.72, -43.83, 9e-05, 221, -2.57, -44.42, 0.00033, 4, 222, 9.6, 23.86, 0.02393, 223, -12.87, 21.71, 0.01299, 218, 5.85, -2.61, 0.95736, 219, -11.3, -4.71, 0.00572, 5, 217, 20.12, 32.83, 0.01421, 218, 20.52, 25.64, 0.16688, 219, -1.92, 25.71, 0.13757, 220, 29.8, -16.5, 0.11372, 221, 6.69, -18.52, 0.56762, 5, 217, 10.75, 20.12, 0.1971, 218, 5.38, 21.17, 0.20677, 219, -16.02, 18.6, 0.03977, 220, 14.02, -15.75, 0.43038, 221, -8.5, -14.19, 0.12599], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 46, 48, 48, 50, 50, 52, 46, 54, 54, 56, 56, 58, 58, 60, 40, 62, 62, 64, 64, 66, 24, 68, 68, 70, 20, 72, 72, 74, 8, 76, 76, 78, 78, 54, 14, 80, 80, 82], "width": 117, "height": 74}}, "a14": {"a14": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 7, 2, 3, 7, 2, 7, 8, 1, 2, 8, 1, 8, 9, 0, 1, 9], "vertices": [3, 14, 13.14, -40.19, 0.904, 39, 10.48, -29.15, 0.03733, 40, -28.11, -30.29, 0.05867, 3, 14, 10.17, -27.79, 0.86667, 39, 7.5, -16.75, 0.104, 40, -31.08, -17.89, 0.02933, 2, 14, 7.19, -15.39, 0.81867, 39, 4.53, -4.36, 0.18133, 2, 14, 4.22, -2.99, 0.80533, 39, 1.56, 8.04, 0.19467, 2, 14, 1.25, 9.41, 0.82133, 39, -1.41, 20.44, 0.17867, 2, 14, 21.67, 14.31, 0.832, 39, 19.01, 25.34, 0.168, 2, 14, 24.64, 1.91, 0.80533, 39, 21.98, 12.94, 0.19467, 2, 14, 27.61, -10.49, 0.816, 39, 24.95, 0.54, 0.184, 3, 14, 30.59, -22.89, 0.85333, 39, 27.92, -11.86, 0.11733, 40, -10.66, -12.99, 0.02933, 3, 14, 33.56, -35.29, 0.90133, 39, 30.9, -24.26, 0.04, 40, -7.69, -25.39, 0.05867], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 20, "height": 8}}, "hwc010": {"hwc010": {"type": "mesh", "uvs": [0.35232, 1, 0.85129, 0.88585, 1, 0.70056, 0.92047, 0.57191, 0.82437, 0.41644, 0.67181, 0.17556, 0.49052, 0.06233, 0.11899, 0, 0.01309, 0.21468, 0.13873, 0.57909, 0.24822, 0.91056, 0.73899, 0.69386, 0.56766, 0.48177, 0.47713, 0.31075, 0.22494, 0.3201, 0.31831, 0.55164, 0.38805, 0.77242], "triangles": [16, 9, 15, 9, 14, 15, 9, 8, 14, 15, 14, 13, 6, 13, 14, 14, 8, 7, 13, 6, 5, 6, 14, 7, 0, 11, 1, 0, 16, 11, 0, 10, 16, 16, 10, 9, 2, 11, 3, 2, 1, 11, 16, 12, 11, 16, 15, 12, 11, 4, 3, 11, 12, 4, 15, 13, 12, 12, 5, 4, 12, 13, 5], "vertices": [2, 153, 1.57, 33.72, 0.92961, 154, -20.6, 35.55, 0.07039, 2, 153, -13.66, -2.93, 0.98249, 154, -38.82, 0.3, 0.01751, 1, 153, -9.65, -19.58, 1, 2, 153, 1.07, -19.3, 0.98899, 154, -25.5, -17.24, 0.01101, 2, 153, 14.02, -18.96, 0.77049, 154, -12.56, -17.98, 0.22951, 2, 153, 34.25, -18.2, 0.44367, 154, 7.66, -18.9, 0.55633, 2, 153, 48.52, -10.75, 0.12134, 154, 22.5, -12.66, 0.87866, 2, 153, 68.18, 10.96, 0.00842, 154, 43.9, 7.34, 0.99158, 2, 153, 60.66, 25.95, 0.11456, 154, 37.64, 22.9, 0.88544, 2, 153, 34.62, 31.61, 0.40085, 154, 12.17, 30.71, 0.59915, 2, 153, 11.14, 37.07, 0.70892, 154, -10.78, 38.1, 0.29108, 1, 153, 2.06, -2.93, 1, 2, 153, 21.48, 0.14, 0.6693, 154, -3.54, 0.44, 0.3307, 2, 153, 35.08, -0.47, 0.50395, 154, 9.95, -1.3, 0.49605, 2, 153, 45.5, 16.22, 0.23809, 154, 21.73, 14.46, 0.76191, 2, 153, 28.37, 18.94, 0.44699, 154, 4.88, 18.6, 0.55301, 2, 153, 12.87, 22.78, 0.64824, 154, -10.24, 23.72, 0.35176], "hull": 11, "edges": [0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 2, 22, 22, 24, 24, 26, 28, 30, 30, 32, 4, 6, 6, 8], "width": 78, "height": 68}}, "sd2": {"sd": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [122.9, -9.43, 49.12, -9.43, 49.12, 22.14, 122.9, 22.14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 201, "height": 86}}, "mamian01": {"mamian01": {"type": "mesh", "uvs": [0.36056, 0.96368, 0.56834, 0.93566, 0.55615, 0.74779, 0.69421, 0.553, 0.86679, 0.45768, 1, 0.39552, 1, 0.19521, 1, 0, 0.69218, 0.01562, 0.32064, 0.14962, 0, 0.47564, 0, 0.82515, 0.09122, 1, 0.78079, 0.23889, 0.50053, 0.36863, 0.31563, 0.5318, 0.30407, 0.78146], "triangles": [0, 12, 16, 0, 16, 1, 12, 11, 16, 16, 2, 1, 16, 11, 15, 16, 15, 2, 15, 11, 10, 2, 15, 3, 15, 14, 3, 3, 14, 4, 15, 10, 14, 10, 9, 14, 14, 13, 4, 4, 13, 5, 13, 6, 5, 14, 9, 13, 9, 8, 13, 13, 8, 6, 8, 7, 6], "vertices": [2, 174, 5.55, 9.96, 0.136, 162, -5.58, 23.09, 0.864, 2, 174, -8.35, 8.48, 0.136, 162, -1.89, 9.6, 0.864, 2, 173, -2.31, 19.86, 0.38317, 174, -9.17, -9.75, 0.61683, 3, 172, -8.65, 17.79, 0.07233, 173, -4.56, -0.99, 0.92684, 174, -19.93, -27.75, 0.00083, 2, 172, -2.83, 4.33, 0.82966, 173, -12.19, -13.52, 0.17034, 1, 172, 0.59, -5.77, 1, 1, 172, 19.3, -11.03, 1, 1, 172, 37.53, -16.15, 1, 2, 172, 41.57, 3.82, 0.96909, 173, 13.04, -50.06, 0.03091, 2, 172, 35.69, 30.94, 0.44777, 173, 31.79, -29.59, 0.55223, 2, 173, 41.12, 7.3, 0.93377, 174, 25.04, -39.31, 0.06623, 2, 173, 29.76, 39.24, 0.06442, 174, 28.06, -5.54, 0.93558, 2, 174, 23.57, 11.89, 0.136, 162, -10.36, 40.57, 0.864, 2, 172, 19.13, 4.05, 0.95584, 173, 0.27, -31.62, 0.04416, 2, 172, 12.02, 25.26, 0.26511, 173, 13.48, -13.56, 0.73489, 2, 173, 19.67, 5.44, 0.93575, 174, 4.78, -32.03, 0.06425, 2, 173, 12.27, 28.52, 0.15243, 174, 7.69, -7.97, 0.84757], "hull": 13, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 12, 26, 26, 28, 28, 30, 30, 32, 2, 0, 0, 24], "width": 66, "height": 97}}, "mamian02": {"mamian02": {"type": "mesh", "uvs": [0.38694, 0.01839, 0.66294, 0.01465, 0.89094, 0.05389, 1, 0.24638, 0.96894, 0.42019, 0.89694, 0.5753, 0.80394, 0.65006, 0.80994, 0.7435, 0.92094, 0.85376, 0.69294, 0.96963, 0.30294, 0.96589, 0.29394, 0.84815, 0.27594, 0.69678, 0.10194, 0.61642, 0, 0.41084, 0.02095, 0.20714, 0.18294, 0.0838, 0.49925, 0.16105, 0.5199, 0.40541, 0.53022, 0.58226, 0.56119, 0.70122, 0.59216, 0.82984], "triangles": [21, 7, 8, 10, 11, 21, 9, 21, 8, 10, 21, 9, 6, 19, 5, 12, 13, 19, 20, 19, 6, 12, 19, 20, 20, 6, 7, 21, 20, 7, 11, 12, 20, 11, 20, 21, 17, 0, 1, 16, 0, 17, 17, 3, 18, 2, 17, 1, 3, 17, 2, 15, 16, 17, 4, 18, 3, 5, 18, 4, 19, 18, 5, 15, 18, 14, 18, 15, 17, 13, 18, 19, 13, 14, 18], "vertices": [1, 166, -7.15, -3.98, 1, 1, 166, -5.52, 6.39, 1, 1, 166, -1.63, 14.49, 1, 1, 166, 10.66, 16.49, 1, 2, 166, 20.89, 13.46, 0.99853, 167, -12.98, 8.24, 0.00147, 3, 166, 29.72, 9.09, 0.7547, 167, -3.13, 8.65, 0.24397, 189, 13.7, 19.26, 0.00133, 3, 166, 33.58, 4.81, 0.1969, 167, 2.31, 6.74, 0.74595, 189, 10.53, 14.44, 0.05715, 3, 166, 39.23, 4.02, 0.00149, 167, 7.64, 8.77, 0.57392, 189, 11.19, 8.78, 0.42459, 2, 167, 12.68, 14.91, 0.03502, 189, 15.92, 2.4, 0.96498, 1, 189, 7.82, -5.32, 1, 1, 189, -6.97, -6.23, 1, 3, 166, 42.05, -16.41, 0.00995, 167, 19.92, -7.79, 0.31266, 189, -7.86, 0.9, 0.67738, 3, 166, 32.84, -15.45, 0.24978, 167, 11.39, -11.38, 0.70569, 189, -9.26, 10.06, 0.04453, 3, 166, 26.84, -21.09, 0.57286, 167, 8.84, -19.2, 0.427, 189, -16.23, 14.44, 0.00014, 2, 166, 13.82, -22.68, 0.86219, 167, -1.82, -26.86, 0.13781, 2, 166, 1.73, -19.7, 0.97647, 167, -13.86, -30.05, 0.02353, 2, 166, -4.59, -12.31, 0.9986, 167, -22.95, -26.6, 0.0014, 1, 166, 2.17, -1.31, 1, 2, 166, 16.98, -3.18, 0.98305, 167, -8.41, -8.23, 0.01695, 2, 166, 27.67, -4.7, 0.48816, 167, 1.69, -4.43, 0.51184, 2, 166, 35.02, -4.83, 0.00989, 167, 8.2, -1.01, 0.99011, 2, 167, 15.27, 2.6, 0.50824, 189, 3.35, 2.89, 0.49176], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 34, 36, 36, 38, 38, 40, 40, 42], "width": 38, "height": 61}}, "mamian03": {"mamian03": {"type": "mesh", "uvs": [0.15096, 0.65241, 0.03881, 0.51276, 0.01804, 0.32376, 0.06096, 0.11986, 0.35807, 0, 0.74705, 0, 0.98992, 0.08767, 1, 0.2918, 0.89125, 0.50998, 0.68253, 0.66563, 0.69012, 0.78046, 0.74894, 0.90039, 0.52807, 1, 0.23402, 0.95301, 0.09422, 0.87684, 0.14725, 0.75366, 0.57647, 0.19766, 0.50424, 0.48905, 0.45172, 0.66564, 0.45828, 0.82016], "triangles": [13, 19, 12, 11, 19, 10, 11, 12, 19, 19, 13, 15, 13, 14, 15, 15, 18, 19, 10, 18, 9, 10, 19, 18, 15, 0, 18, 9, 18, 17, 18, 0, 17, 9, 17, 8, 0, 1, 17, 1, 2, 17, 17, 16, 8, 8, 16, 7, 16, 6, 7, 16, 5, 6, 17, 2, 16, 16, 2, 3, 16, 3, 4, 16, 4, 5], "vertices": [2, 168, 34.52, -10.37, 0.57974, 169, -2.66, -10.29, 0.42026, 2, 168, 27.97, -16.88, 0.97211, 169, -11.48, -13.11, 0.02789, 1, 168, 17.63, -20.8, 1, 1, 168, 5.73, -22.58, 1, 1, 168, -4.32, -13.47, 1, 1, 168, -8.69, 1.07, 1, 1, 168, -6.52, 11.6, 1, 1, 168, 4.79, 15.37, 1, 3, 168, 18.23, 14.93, 0.98063, 169, -5.66, 19.65, 0.01936, 188, 12.86, 25.34, 0, 3, 168, 29.29, 9.71, 0.5438, 169, 1.83, 9.98, 0.38995, 188, 5.71, 15.41, 0.06626, 3, 168, 35.63, 11.9, 0.07346, 169, 8.49, 9.04, 0.4901, 188, 6.71, 8.76, 0.43644, 3, 168, 41.68, 16.09, 4e-05, 169, 15.8, 10.02, 0.02344, 188, 9.72, 2.03, 0.97652, 2, 169, 19.99, 0.47, 0.76923, 188, 1.76, -4.7, 0.23077, 1, 169, 15.22, -10.31, 1, 2, 168, 47.72, -8.76, 0.00071, 169, 9.86, -14.87, 0.99929, 2, 168, 40.23, -8.83, 0.11839, 169, 3.14, -11.52, 0.88161, 1, 168, 4.29, -2.02, 1, 3, 168, 21.41, 0.12, 0.99986, 169, -9.58, 5.02, 0.00014, 188, -2.28, 24.94, 0, 3, 168, 31.88, 1.09, 0.4355, 169, 0.21, 1.11, 0.56224, 188, -3.24, 14.45, 0.00226, 1, 169, 9.15, -0.28, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 32, 34, 34, 36, 36, 38], "width": 39, "height": 58}}, "mamian04": {"mamian04": {"type": "mesh", "uvs": [0.04812, 0.63845, 0, 0.44182, 0, 0.23351, 0.20471, 0.05635, 0.48015, 0, 0.77257, 0.07972, 0.93105, 0.22767, 0.94803, 0.43209, 0.9103, 0.60146, 0.88388, 0.65013, 0.92539, 0.72995, 1, 0.84286, 0.81031, 0.91295, 0.61976, 0.96941, 0.46129, 0.98498, 0.24055, 1, 0.04435, 0.96551, 0.05378, 0.83508, 0.06887, 0.70075, 0.5598, 0.28884, 0.57047, 0.50083, 0.5678, 0.68804, 0.59982, 0.84221, 0.7759, 0.25305, 0.78124, 0.46228, 0.75723, 0.66877, 0.78924, 0.82019, 0.27433, 0.28884, 0.29034, 0.49532, 0.30368, 0.71281, 0.29034, 0.86699], "triangles": [27, 3, 4, 2, 3, 27, 1, 2, 27, 1, 27, 28, 23, 5, 6, 19, 5, 23, 23, 6, 7, 24, 23, 7, 8, 24, 7, 8, 25, 24, 9, 25, 8, 10, 26, 25, 10, 25, 9, 22, 25, 26, 11, 12, 26, 11, 26, 10, 0, 1, 28, 29, 18, 0, 29, 28, 21, 29, 0, 28, 30, 18, 29, 17, 18, 30, 15, 16, 17, 29, 22, 30, 30, 15, 17, 15, 30, 14, 22, 21, 25, 12, 13, 22, 12, 22, 26, 22, 29, 21, 14, 22, 13, 14, 30, 22, 19, 4, 5, 27, 4, 19, 19, 23, 24, 28, 27, 19, 20, 19, 24, 28, 19, 20, 25, 20, 24, 21, 28, 20, 21, 20, 25], "vertices": [3, 160, 8.33, 53.95, 0.01853, 163, 12.29, -26.53, 0.70871, 177, -33.62, 31.47, 0.27275, 3, 160, 27.37, 54.86, 0.01984, 163, 2.92, -43.12, 0.35904, 177, -14.74, 28.78, 0.62113, 3, 160, 46.57, 50.99, 0.00105, 163, -10.59, -57.3, 0.1179, 177, 3.37, 21.35, 0.88105, 2, 161, 35.57, 21.91, 0.12155, 177, 11.25, -3.34, 0.87845, 2, 161, 30.33, -4.82, 0.98581, 181, 24.11, 23.64, 0.01419, 2, 161, 12.64, -28.22, 0.12354, 181, 6.42, 0.24, 0.87646, 3, 160, 29.24, -37.64, 0.16736, 165, -21.79, 41.92, 0.08978, 181, -12.28, -8.7, 0.74286, 3, 160, 10.08, -35.46, 0.33827, 165, -7.57, 28.89, 0.30498, 181, -30.69, -2.94, 0.35675, 3, 160, -4.8, -28.72, 0.24286, 165, 0.52, 14.7, 0.63804, 181, -44.03, 6.49, 0.1191, 3, 160, -8.78, -25.3, 0.12802, 165, 1.73, 9.6, 0.81845, 181, -47.29, 10.59, 0.05353, 3, 160, -16.93, -27.77, 0.00567, 165, 9.78, 6.8, 0.99051, 181, -55.76, 9.71, 0.00383, 1, 165, 22.29, 3.89, 1, 2, 164, 18.65, 18.21, 0.34901, 165, 13.21, -13.42, 0.65099, 3, 164, 22.01, -0.72, 0.98217, 163, -6.37, 34.25, 0.0153, 165, 3.2, -29.84, 0.00253, 2, 164, 21.87, -16.16, 0.58215, 163, 5.77, 24.7, 0.41785, 2, 164, 21.04, -37.61, 0.04484, 163, 22.24, 10.95, 0.95516, 2, 163, 33.78, -4.53, 0.99993, 177, -61.92, 43.47, 7e-05, 2, 163, 24.65, -12.77, 0.97521, 177, -50.92, 37.98, 0.02479, 3, 160, 2.19, 53.13, 0.00921, 163, 14.88, -20.9, 0.83112, 177, -39.8, 31.83, 0.15966, 3, 161, 2.28, -1.67, 0.98174, 165, -44.4, 13.31, 0.00064, 181, -3.94, 26.8, 0.01762, 5, 160, 10.99, 1.72, 0.98318, 161, -16.55, 4.93, 0.00191, 164, -22.3, -0.89, 0.00158, 163, -33.3, -0.92, 0.01046, 177, -40.87, -20.32, 0.00286, 4, 160, -6.21, 5.45, 0.33054, 164, -4.82, -2.98, 0.60465, 163, -20.97, 11.63, 0.06421, 177, -57.05, -13.4, 0.00061, 2, 164, 9.92, -1.4, 0.98659, 163, -13.22, 24.26, 0.01341, 4, 160, 29.88, -22.42, 0.23212, 161, -2.56, -22.34, 0.05976, 165, -31.25, 29.97, 0.05318, 181, -8.78, 6.12, 0.65494, 3, 160, 10.5, -19.04, 0.52449, 165, -17.55, 15.85, 0.26409, 181, -27.17, 13.11, 0.21142, 4, 160, -8.07, -12.91, 0.21001, 164, -4.71, 15.48, 0.08437, 165, -6.12, -0.01, 0.69184, 181, -44.25, 22.62, 0.01378, 2, 164, 9.77, 17.09, 0.28112, 165, 5.8, -8.38, 0.71888, 4, 160, 36.21, 25.93, 0.06583, 161, 12.79, 23.95, 0.18384, 163, -26.26, -35.17, 0.09395, 177, -11.53, -1.3, 0.65638, 5, 160, 16.87, 28.25, 0.21831, 161, -5.76, 29.88, 0.06327, 164, -25.64, -27.87, 0.0004, 163, -13.99, -20.05, 0.42223, 177, -30.08, 4.63, 0.29579, 5, 160, -3.42, 31.03, 0.03542, 161, -25.17, 36.44, 0.00186, 164, -5.17, -28.71, 0.0015, 163, -0.82, -4.36, 0.92848, 177, -49.49, 11.19, 0.03275, 2, 164, 9.11, -31.5, 0.05503, 163, 10.12, 5.23, 0.94497], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 8, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60], "width": 97, "height": 94}}, "mamian05": {"mamian05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [10.68, 33.83, 44.48, 4.13, 16.75, -27.42, -17.05, 2.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 45, "height": 42}}, "mamian06": {"mamian06": {"type": "mesh", "uvs": [0.09976, 0.24865, 0.26325, 0.02884, 0.50475, 0.01643, 0.68025, 0.27524, 0.93225, 0.31602, 1, 0.46315, 0.98325, 0.74147, 0.81075, 1, 0.50838, 1, 0.30996, 1, 0.07007, 0.73581, 0, 0.54156, 0.30033, 0.43819, 0.80796, 0.64993], "triangles": [8, 13, 7, 7, 13, 6, 13, 8, 12, 10, 12, 9, 8, 9, 12, 6, 13, 5, 10, 11, 12, 12, 3, 13, 13, 4, 5, 13, 3, 4, 11, 0, 12, 0, 1, 12, 12, 2, 3, 12, 1, 2], "vertices": [2, 178, 33.92, -10.64, 0.9979, 179, -7.57, 7.81, 0.0021, 1, 178, 21.15, -12.29, 1, 1, 178, 11.36, -4.41, 1, 2, 178, 12.02, 10.17, 0.43677, 179, 21.9, 14.46, 0.56323, 2, 178, 3.36, 20.17, 0.05159, 179, 35.02, 16.1, 0.94841, 2, 178, 4.99, 27.36, 0.01642, 179, 40.09, 10.75, 0.98358, 1, 179, 42.41, -1.31, 1, 1, 179, 36.67, -14.61, 1, 1, 179, 21.47, -18.66, 1, 1, 179, 11.5, -21.32, 1, 2, 178, 49.23, 4.45, 0.11208, 179, -3.54, -13.3, 0.88792, 2, 178, 46.33, -4.38, 0.45694, 179, -9.26, -5.98, 0.54306, 2, 178, 31.59, 2.51, 0.50732, 179, 4.66, 2.44, 0.49268, 2, 178, 17.92, 26.94, 0.00028, 179, 32.56, 0.23, 0.99972], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22, 0, 24, 24, 26], "width": 52, "height": 44}}, "mamian07": {"mamian07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [3.81, -21.47, -9.51, -1.5, 16.29, 15.69, 29.6, -4.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 24, "height": 31}}, "mamian08": {"mamian08": {"type": "mesh", "uvs": [0.25747, 0.43286, 0.268, 0.28963, 0.29747, 0.12447, 0.3901, 0.00834, 0.528, 0, 0.66589, 0.06899, 0.72168, 0.17995, 0.74589, 0.31028, 0.7486, 0.41517, 0.74905, 0.43286, 0.89958, 0.56447, 1, 0.71028, 0.87326, 0.87286, 0.68379, 1, 0.41853, 0.97995, 0.18063, 0.82511, 0, 0.59802, 0.12063, 0.51157, 0.37069, 0.5547, 0.53657, 0.58669, 0.68009, 0.52272, 0.53657, 0.17315, 0.53284, 0.35821, 0.78074, 0.6895, 0.5869, 0.7649, 0.32223, 0.6895, 0.19735, 0.59354], "triangles": [20, 8, 9, 23, 9, 10, 20, 9, 23, 23, 10, 11, 12, 23, 11, 18, 22, 19, 20, 22, 8, 18, 0, 22, 21, 4, 5, 21, 5, 6, 21, 3, 4, 22, 21, 6, 22, 6, 7, 21, 2, 3, 21, 1, 2, 22, 1, 21, 22, 7, 8, 0, 1, 22, 13, 23, 12, 14, 24, 13, 19, 22, 20, 24, 19, 20, 24, 20, 23, 24, 25, 19, 14, 25, 24, 13, 24, 23, 25, 18, 19, 26, 17, 0, 26, 0, 18, 25, 26, 18, 26, 16, 17, 15, 26, 25, 15, 16, 26, 15, 25, 14], "vertices": [2, 170, -1.99, -8.16, 0.81077, 162, 8.37, 27.54, 0.18923, 2, 170, -8.12, -20.05, 0.34542, 162, 21.74, 27.29, 0.65458, 2, 170, -17.01, -33.02, 0.11786, 162, 37.3, 25.03, 0.88214, 2, 170, -30.86, -39.05, 0.03081, 162, 48.82, 15.27, 0.96919, 3, 170, -45.72, -33.85, 0.00088, 162, 50.72, -0.36, 0.9988, 171, -58.83, 17.24, 0.00031, 3, 161, 61.14, -34.03, 0.00434, 162, 45.44, -16.5, 0.97107, 171, -42.71, 22.57, 0.02459, 3, 161, 49.18, -36, 0.03697, 162, 35.6, -23.57, 0.8815, 171, -31.17, 18.85, 0.08153, 3, 161, 36.92, -33.95, 0.12757, 162, 23.7, -27.19, 0.65422, 171, -21.2, 11.43, 0.2182, 3, 161, 27.78, -30.54, 0.21122, 162, 13.99, -28.19, 0.33742, 171, -14.63, 4.21, 0.45136, 3, 161, 26.23, -29.96, 0.21392, 162, 12.36, -28.36, 0.26135, 171, -13.53, 2.99, 0.52473, 1, 171, 7.47, 4.83, 1, 1, 171, 24.98, 1.95, 1, 2, 161, -17, -27.54, 0.31842, 171, 23.82, -18.93, 0.68158, 3, 170, -27.17, 58.99, 0.00012, 161, -19.74, -3.07, 0.88612, 171, 15.07, -41.95, 0.11376, 3, 170, 0.15, 45.88, 0.1872, 161, -6.55, 24.2, 0.79637, 162, -41.08, 5.6, 0.01642, 3, 170, 19.85, 22.34, 0.80967, 161, 17.06, 43.83, 0.18025, 162, -28.65, 33.67, 0.01008, 1, 170, 30.98, -4.98, 1, 1, 170, 15.21, -7.25, 1, 3, 170, -9.69, 7.19, 0.4817, 161, 32.11, 14.25, 0.05238, 162, -2.02, 13.85, 0.46593, 2, 161, 22.19, -2.12, 0.98549, 171, -22.66, -23.61, 0.01451, 3, 161, 21.48, -19.52, 0.47054, 162, 3.46, -21.12, 0.11401, 171, -14.08, -8.47, 0.41546, 3, 161, 57.77, -16.71, 0.00098, 162, 34.72, -2.48, 0.99556, 171, -47.63, 5.63, 0.00346, 3, 161, 42.01, -9.79, 0.03114, 162, 17.53, -3.28, 0.95258, 171, -36.78, -7.73, 0.01627, 2, 161, 2.78, -24.25, 0.30281, 171, 4.72, -12.81, 0.69719, 2, 161, 4.68, -1.14, 0.99219, 171, -7.53, -32.49, 0.00781, 3, 170, 0.15, 16.73, 0.62569, 161, 22.61, 24.11, 0.24206, 162, -14.92, 18.47, 0.13225, 3, 170, 9.98, 3.1, 0.98811, 161, 36.27, 33.9, 0.01149, 162, -7.03, 33.31, 0.00039], "hull": 18, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 8, 42, 42, 44, 20, 46, 46, 48, 48, 50, 50, 52, 14, 16, 16, 18, 16, 40, 0, 2], "width": 114, "height": 93}}, "mamian09": {"mamian09": {"type": "mesh", "uvs": [0.01849, 0.3747, 0.02019, 0.21684, 0.03717, 0.03293, 0.22914, 0.11109, 0.30049, 0.22604, 0.40071, 0.17853, 0.46187, 0.18006, 0.47546, 0.08351, 0.54171, 0, 0.6963, 0.12029, 0.698, 0.32106, 0.77615, 0.38083, 0.79144, 0.46359, 0.96471, 0.57547, 1, 0.72107, 1, 0.84368, 0.91035, 0.90651, 0.79823, 0.97395, 0.72179, 1, 0.557, 0.93257, 0.43469, 0.90805, 0.30218, 0.91265, 0.12381, 0.83142, 0.02188, 0.67049, 0, 0.48505, 0.39778, 0.44168, 0.53015, 0.47017, 0.55079, 0.59616, 0.74387, 0.72653, 0.67708, 0.82403, 0.41964, 0.76706, 0.20106, 0.66737, 0.14277, 0.4614, 0.15856, 0.29817, 0.1197, 0.16122, 0.57022, 0.25434, 0.58479, 0.11411, 0.9236, 0.709, 0.7803, 0.84923], "triangles": [35, 36, 9, 6, 7, 36, 7, 8, 36, 9, 36, 8, 1, 34, 33, 4, 34, 3, 1, 2, 34, 34, 2, 3, 4, 33, 34, 35, 6, 36, 25, 5, 6, 35, 9, 10, 25, 6, 35, 33, 4, 25, 11, 26, 10, 26, 35, 10, 19, 29, 18, 18, 38, 17, 18, 29, 38, 17, 38, 16, 29, 19, 30, 20, 21, 30, 30, 21, 31, 19, 20, 30, 15, 16, 37, 29, 28, 38, 16, 38, 37, 38, 28, 37, 37, 14, 15, 21, 22, 31, 22, 23, 31, 28, 29, 27, 30, 31, 27, 29, 30, 27, 27, 25, 26, 25, 27, 31, 13, 37, 28, 37, 13, 14, 28, 27, 12, 13, 28, 12, 23, 32, 31, 23, 24, 32, 31, 32, 25, 12, 26, 11, 26, 12, 27, 24, 0, 32, 26, 25, 35, 32, 33, 25, 25, 4, 5, 32, 0, 33, 0, 1, 33], "vertices": [1, 162, 41.58, 22.71, 1, 2, 162, 56.07, 23.6, 0.01635, 176, 7.66, 10.2, 0.98365, 1, 176, 22.57, 2.09, 1, 2, 176, 9.57, -9.59, 0.99537, 175, 10.58, 28.08, 0.00463, 1, 162, 56.89, 0.33, 1, 1, 162, 61.84, -7.65, 1, 1, 162, 62.06, -12.72, 1, 3, 162, 71, -13.22, 0.00137, 176, 3.64, -29.32, 0.005, 175, 10.67, 7.48, 0.99364, 1, 175, 17.65, 1.1, 1, 1, 175, 5.14, -10.32, 1, 1, 162, 50.52, -33.2, 1, 1, 162, 45.49, -40.06, 1, 1, 162, 37.99, -41.87, 1, 1, 162, 28.75, -56.95, 1, 1, 162, 15.6, -60.82, 1, 1, 162, 4.34, -61.63, 1, 1, 162, -1.95, -54.62, 1, 1, 162, -8.8, -45.78, 1, 1, 162, -11.65, -39.62, 1, 1, 162, -6.43, -25.53, 1, 1, 162, -4.9, -15.25, 1, 1, 162, -6.11, -4.31, 1, 1, 162, 0.29, 10.99, 1, 2, 162, 14.45, 20.49, 0.96749, 176, -30.58, 26.91, 0.03251, 1, 162, 31.34, 23.51, 1, 1, 162, 37.67, -9.13, 1, 1, 162, 35.84, -20.28, 1, 1, 162, 24.4, -22.81, 1, 1, 162, 13.58, -39.65, 1, 1, 162, 4.24, -34.76, 1, 1, 162, 7.94, -13.08, 1, 2, 162, 15.8, 5.67, 0.99596, 176, -36.32, 13.19, 0.00404, 1, 162, 34.36, 11.85, 1, 3, 162, 49.43, 11.61, 0.90123, 176, -3.82, 2.71, 0.09841, 175, -5.81, 35.94, 0.00036, 1, 176, 9.01, 0.58, 1, 3, 162, 55.88, -22.18, 0.97112, 176, -13.92, -30.18, 0.00057, 175, -5.87, 1.53, 0.0283, 1, 175, 6.8, -1.2, 1, 1, 162, 16.25, -54.42, 1, 1, 162, 2.54, -43.47, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 70, 72, 32, 74, 36, 76], "width": 83, "height": 92}}, "kulou": {"kulou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [333.41, -140.12, -352.59, -140.12, -352.59, 270.88, 333.41, 270.88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 686, "height": 411}}, "fanren01": {"fanren01": {"type": "mesh", "uvs": [0.35162, 0.7336, 0.25162, 0.61277, 0.19162, 0.46929, 0.21562, 0.23267, 0.33262, 0.07912, 0.51162, 0, 0.72562, 0, 0.90662, 0.09926, 1, 0.34343, 1, 0.59515, 0.88562, 0.74493, 0.73762, 0.87708, 0.58262, 0.96393, 0.39962, 0.98533, 0.23862, 0.92114, 0.12162, 0.83933, 0, 0.83681, 0.00962, 0.76381, 0.15462, 0.682, 0.30762, 0.76507, 0.36162, 0.77639, 0.59147, 0.27202, 0.65726, 0.50389, 0.61998, 0.74405, 0.49716, 0.86827, 0.33705, 0.86275, 0.20546, 0.80478, 0.11115, 0.78545, 0.35899, 0.26373, 0.41601, 0.5315, 0.82175, 0.22785, 0.86562, 0.48457], "triangles": [15, 26, 14, 16, 27, 15, 15, 27, 26, 16, 17, 27, 27, 18, 26, 26, 18, 19, 27, 17, 18, 14, 25, 13, 13, 25, 24, 14, 26, 25, 25, 20, 24, 26, 19, 25, 25, 19, 20, 24, 20, 0, 13, 24, 12, 12, 23, 11, 12, 24, 23, 11, 23, 10, 23, 24, 0, 0, 29, 23, 23, 29, 22, 0, 1, 29, 22, 21, 30, 29, 28, 21, 2, 28, 29, 10, 22, 31, 22, 10, 23, 10, 31, 9, 1, 2, 29, 31, 8, 9, 29, 21, 22, 22, 30, 31, 2, 3, 28, 31, 30, 8, 21, 6, 30, 28, 5, 21, 3, 4, 28, 30, 7, 8, 21, 5, 6, 28, 4, 5, 30, 6, 7], "vertices": [3, 213, 25.1, -18.37, 0.12349, 214, 17.77, -10.84, 0.22223, 215, 9.33, -5.72, 0.65428, 2, 213, 20.98, -27.62, 0.63493, 214, 18.93, -20.89, 0.36507, 3, 205, 30.09, 27.24, 0.22792, 213, 14.63, -34.56, 0.59116, 214, 17, -30.1, 0.18092, 2, 205, 28.29, 13.53, 0.16, 213, 1.12, -37.56, 0.84, 2, 205, 19.71, 4.65, 0.688, 213, -10.15, -32.54, 0.312, 2, 205, 6.63, 0.11, 0.824, 213, -18.89, -21.8, 0.176, 2, 205, -8.99, 0.16, 0.8, 213, -24.19, -7.1, 0.2, 2, 205, -22.18, 5.97, 0.72, 213, -23.24, 7.28, 0.28, 1, 205, -28.95, 20.15, 1, 3, 205, -28.9, 34.75, 0.16, 213, 1.51, 23.44, 0.83977, 214, -23.81, 13.15, 0.00023, 2, 213, 12.51, 18.53, 0.90704, 214, -11.84, 14.52, 0.09296, 2, 213, 23.38, 10.96, 0.28151, 214, 1.37, 13.54, 0.71849, 2, 214, 13.3, 10.2, 0.98608, 215, -9.78, 4.17, 0.01392, 2, 214, 24.38, 2.64, 0.03166, 215, 3.1, 7.94, 0.96834, 2, 215, 15.35, 6.54, 0.6733, 216, -0.25, 6.65, 0.3267, 1, 216, 8.48, 2.27, 1, 1, 216, 17.36, 2.5, 1, 1, 216, 16.84, -1.76, 1, 1, 216, 6.46, -6.95, 1, 4, 213, 27.9, -20.78, 0.00098, 214, 21.41, -11.48, 0.0021, 215, 12.13, -3.31, 0.98755, 216, -4.9, -2.61, 0.00936, 3, 213, 27.18, -16.85, 0.03373, 214, 18.79, -8.46, 0.09328, 215, 8.14, -3.42, 0.873, 2, 205, 0.86, 15.9, 0.16, 213, -6.02, -10.97, 0.84, 4, 205, -3.9, 29.37, 0.16, 213, 5, -1.89, 0.82826, 214, -7.91, -6.88, 0.00737, 215, -10.02, -23.06, 0.00437, 1, 214, 3.07, 2.12, 1, 1, 214, 14.57, 1.95, 1, 1, 215, 8.94, 1.84, 1, 1, 216, 2.45, 0.01, 1, 1, 216, 9.38, -0.82, 1, 2, 205, 17.83, 15.36, 0.16, 213, -0.73, -27.1, 0.84, 3, 205, 13.72, 30.91, 0.20939, 213, 12.47, -17.92, 0.49972, 214, 6.68, -16.87, 0.2909, 2, 205, -15.96, 13.4, 0.16, 213, -14.13, 3.98, 0.84, 2, 205, -19.11, 28.3, 0.16, 213, -1.2, 12.04, 0.84], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 10, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 60, 62], "width": 73, "height": 58}}, "fanren02": {"fanren02": {"type": "mesh", "uvs": [0.53739, 0, 0.66869, 0.02997, 0.78937, 0.14291, 0.8464, 0.30214, 0.84508, 0.49284, 0.88619, 0.63356, 0.9525, 0.6928, 0.8968, 0.72243, 0.82982, 0.70055, 0.90156, 0.8278, 1, 0.89849, 0.97836, 0.93384, 0.85878, 0.94861, 0.77339, 0.94583, 0.68403, 0.94722, 0.59467, 0.89455, 0.50729, 1, 0.43183, 0.87237, 0.32261, 0.94445, 0.23226, 0.98603, 0.1002, 0.96108, 0.17467, 0.84465, 0.06049, 0.83055, 0, 0.77772, 0.09703, 0.70303, 0.12183, 0.61923, 0.17664, 0.55182, 0.18316, 0.37511, 0.24841, 0.19475, 0.37107, 0.03808, 0.53627, 0.1881, 0.53159, 0.40388, 0.53315, 0.63275, 0.52222, 0.80494, 0.70177, 0.23169, 0.70177, 0.44529, 0.68928, 0.648, 0.72519, 0.7875, 0.83605, 0.85943, 0.89694, 0.88995, 0.85947, 0.66326, 0.7814, 0.55428, 0.76423, 0.40388, 0.39888, 0.19028, 0.33799, 0.38644, 0.33018, 0.58479, 0.3255, 0.77225, 0.25055, 0.88777, 0.15375, 0.77225, 0.24119, 0.68288, 0.2568, 0.56082], "triangles": [19, 47, 18, 19, 20, 47, 20, 21, 47, 18, 46, 17, 18, 47, 46, 46, 47, 49, 17, 46, 33, 22, 48, 21, 47, 21, 49, 21, 48, 49, 22, 23, 48, 23, 24, 48, 24, 25, 48, 48, 25, 49, 33, 46, 32, 46, 45, 32, 46, 49, 45, 25, 26, 49, 49, 50, 45, 49, 26, 50, 45, 31, 32, 45, 50, 44, 50, 26, 27, 45, 44, 31, 44, 50, 27, 44, 43, 31, 31, 43, 30, 27, 28, 44, 44, 28, 43, 28, 29, 43, 30, 43, 0, 0, 43, 29, 12, 39, 11, 13, 38, 12, 12, 38, 39, 13, 14, 37, 14, 15, 37, 13, 37, 38, 11, 39, 10, 39, 9, 10, 39, 38, 9, 37, 8, 38, 38, 8, 9, 16, 33, 15, 16, 17, 33, 15, 33, 36, 15, 36, 37, 36, 33, 32, 37, 36, 8, 8, 40, 7, 7, 40, 6, 36, 41, 8, 8, 41, 40, 40, 5, 6, 40, 41, 5, 36, 35, 41, 36, 32, 35, 41, 4, 5, 41, 42, 4, 32, 31, 35, 35, 42, 41, 4, 42, 3, 42, 35, 34, 35, 31, 34, 31, 30, 34, 42, 34, 3, 34, 2, 3, 30, 1, 34, 34, 1, 2, 30, 0, 1], "vertices": [2, 227, -28.84, -20.1, 0.60614, 224, -29.25, 32.84, 0.39386, 2, 227, -23.1, -2.24, 0.86091, 224, -27.38, 51.51, 0.13909, 2, 227, -9.3, 12.9, 0.99729, 224, -17.06, 69.2, 0.00271, 2, 227, 7.8, 18.46, 0.9918, 228, -16.04, 20.29, 0.0082, 3, 227, 26.82, 15.42, 0.39908, 228, 2.55, 15.24, 0.59346, 229, -9.34, 22.95, 0.00745, 3, 227, 41.73, 19.05, 0.0039, 228, 17.77, 17.25, 0.77363, 229, 4.81, 17, 0.22247, 2, 228, 25.92, 24.79, 0.7289, 229, 15.65, 19.38, 0.2711, 2, 228, 26.83, 16.43, 0.72109, 229, 12.21, 11.71, 0.27891, 2, 228, 22.3, 7.86, 0.4575, 229, 3.97, 6.6, 0.5425, 1, 229, 20.21, 4.66, 1, 1, 229, 35.07, 9.43, 1, 1, 229, 35.44, 4.75, 1, 3, 229, 24.57, -8.23, 0.99861, 225, 26.3, 89.49, 0.00104, 226, -28.1, 85.1, 0.00035, 4, 228, 44.26, -6.1, 0.04346, 229, 15.86, -16.55, 0.92995, 225, 28.71, 77.69, 0.01938, 226, -21.56, 74.99, 0.00721, 4, 228, 41.21, -18.33, 0.24025, 229, 7.05, -25.55, 0.65707, 225, 31.66, 65.44, 0.07319, 226, -14.35, 64.65, 0.02949, 6, 227, 61.7, -25.51, 0.00138, 228, 32.88, -29.18, 0.43112, 229, -5.62, -30.7, 0.26728, 224, 60.42, 46.54, 0.00196, 225, 29.29, 51.97, 0.19931, 226, -11.67, 51.24, 0.09895, 4, 228, 40.07, -43.79, 0.40411, 229, -6.8, -46.94, 0.12726, 225, 42.42, 42.34, 0.28555, 226, 4.07, 47.04, 0.18307, 6, 227, 56.08, -47.89, 0.0059, 228, 24.91, -50.83, 0.27448, 229, -23.44, -45.35, 0.05736, 224, 59.62, 23.49, 0.00242, 225, 32.23, 29.09, 0.31843, 226, -0.6, 30.99, 0.3414, 4, 228, 28.06, -67.57, 0.06047, 229, -29.18, -61.39, 0.00709, 225, 42.76, 15.7, 0.07999, 226, 14.08, 22.36, 0.85246, 4, 228, 28.91, -80.95, 0.00946, 229, -35.22, -73.37, 0.00061, 225, 49.7, 4.22, 0.00033, 226, 24.72, 14.19, 0.98961, 2, 228, 21.77, -98.33, 0, 226, 33.11, -2.63, 1, 2, 225, 37.59, -6.88, 7e-05, 226, 17.48, -0.56, 0.99993, 1, 226, 25.36, -14.67, 1, 1, 226, 25.74, -24.73, 1, 2, 225, 26.1, -20.74, 0.14837, 226, 11.81, -17.66, 0.85163, 3, 224, 36.82, -21.74, 0.0017, 225, 17.06, -19.22, 0.487, 226, 2.85, -19.53, 0.5113, 3, 224, 29.55, -14.45, 0.12787, 225, 8.7, -13.21, 0.76059, 226, -7.13, -16.97, 0.11155, 2, 224, 11.68, -14.64, 0.94712, 225, -8.9, -16.3, 0.05288, 2, 227, -15.44, -63.31, 0.00805, 224, -7.08, -6.6, 0.99195, 2, 227, -28.52, -43.86, 0.24942, 224, -23.95, 9.68, 0.75058, 4, 227, -10.08, -23.07, 0.60226, 228, -38.23, -19.11, 0.00204, 224, -10.28, 33.87, 0.39157, 225, -38.43, 28.02, 0.00413, 5, 227, 11.38, -26.96, 0.48343, 228, -17.31, -25.26, 0.09906, 224, 11.51, 34.57, 0.32579, 225, -17.03, 32.24, 0.0911, 226, -47.64, 16.01, 0.00062, 6, 227, 34.27, -30.17, 0.14267, 228, 5.11, -30.89, 0.40805, 229, -30.45, -18.14, 0.02351, 224, 34.57, 36.23, 0.10125, 225, 5.45, 37.61, 0.27548, 226, -28.65, 29.19, 0.04904, 6, 227, 51.24, -34.27, 0.01827, 228, 21.54, -36.77, 0.41823, 229, -19.24, -31.52, 0.10788, 224, 52.02, 35.78, 0.01405, 225, 22.75, 39.99, 0.29327, 226, -13.4, 37.7, 0.1483, 3, 227, -2.26, -0.65, 0.98864, 224, -7.34, 57.44, 0.01131, 225, -39.34, 51.75, 6e-05, 5, 227, 19.07, -3.85, 0.95479, 228, -7.2, -3.1, 0.02685, 224, 14.19, 58.78, 0.01141, 225, -18.31, 56.56, 0.00686, 226, -57.68, 38.2, 9e-05, 6, 227, 39.06, -8.63, 0.02759, 228, 12.16, -9.98, 0.88464, 229, -13.79, -3.66, 0.01965, 224, 34.73, 58.3, 0.01119, 225, 2.04, 59.42, 0.04743, 226, -39.76, 48.26, 0.0095, 5, 228, 27.07, -8.64, 0.36121, 229, -0.25, -10.04, 0.58852, 224, 48.48, 64.23, 0.00064, 225, 14.64, 67.5, 0.037, 226, -30.96, 60.37, 0.01262, 3, 229, 15.94, -4.13, 0.99783, 225, 18.23, 84.35, 0.0016, 226, -33.74, 77.38, 0.00056, 2, 229, 24.19, -0.24, 1, 225, 19.32, 93.41, 0, 3, 227, 44.14, 14.88, 0.00119, 228, 19.72, 12.85, 0.73597, 229, 4.26, 12.22, 0.26284, 3, 227, 31.62, 5.62, 0.06791, 228, 6.29, 4.98, 0.92885, 229, -11.3, 12.22, 0.00324, 2, 227, 16.24, 5.48, 0.97544, 228, -9.02, 6.48, 0.02456, 4, 227, -12.74, -42.26, 0.22684, 228, -42.92, -37.91, 0.00143, 224, -8.85, 14.55, 0.77134, 225, -33.89, 9.18, 0.0004, 4, 227, 5.58, -53.69, 0.04323, 228, -25.92, -51.23, 0.01005, 224, 11.46, 7.22, 0.93163, 225, -12.66, 5.24, 0.01508, 6, 227, 25.23, -57.75, 0.01944, 228, -6.81, -57.35, 0.03993, 229, -54.11, -34.95, 0.0009, 224, 31.52, 7.37, 0.08935, 225, 7.12, 8.64, 0.8429, 226, -16.55, 2.81, 0.00749, 6, 227, 43.86, -61.21, 0.00387, 228, 11.34, -62.78, 0.07488, 229, -41.19, -48.8, 0.00763, 224, 50.46, 7.89, 0.00043, 225, 25.72, 12.22, 0.34656, 226, -0.53, 12.91, 0.56663, 4, 228, 19.96, -75.95, 0.01712, 229, -40.41, -64.53, 0.00149, 225, 39.45, 4.52, 0.00767, 226, 15.06, 10.74, 0.97372, 2, 225, 31.12, -11.39, 0.0433, 226, 13.09, -7.11, 0.9567, 2, 225, 19.57, -1.38, 0.88894, 226, -1.31, -2, 0.11106, 3, 224, 29.75, -3.11, 0.01251, 225, 7.07, -1.99, 0.98446, 226, -12.74, -7.11, 0.00303], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 0, 60, 60, 62, 62, 64, 64, 66, 66, 32, 2, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 12, 80, 80, 82, 82, 84, 86, 88, 88, 90, 90, 92, 92, 94, 94, 40, 46, 96, 96, 98, 98, 100], "width": 141, "height": 101}}, "fanren03": {"fanren03": {"type": "mesh", "uvs": [0.44564, 0.00802, 0.62231, 0.00561, 0.79235, 0.1092, 0.87406, 0.35974, 0.94252, 0.63438, 1, 0.90179, 0.77689, 0.98129, 0.51631, 0.94997, 0.18947, 0.9307, 0.01722, 1, 0, 0.8247, 0.06581, 0.57174, 0.10997, 0.27784, 0.19831, 0.08752, 0.49394, 0.28406, 0.5264, 0.60282], "triangles": [15, 3, 4, 11, 14, 15, 8, 11, 15, 10, 11, 8, 7, 8, 15, 6, 7, 15, 4, 6, 15, 5, 6, 4, 9, 10, 8, 14, 0, 1, 13, 0, 14, 14, 1, 2, 12, 13, 14, 14, 2, 3, 15, 14, 3, 14, 11, 12], "vertices": [3, 207, 34.51, 5.67, 0.00606, 212, 11.69, 7.12, 0.97959, 206, 50.24, 15.87, 0.01436, 3, 207, 32.39, -8.17, 0.21625, 212, 11.23, -6.87, 0.78374, 206, 52.41, 2.04, 2e-05, 3, 207, 23.44, -20.34, 0.83683, 212, 3.81, -20.03, 0.16292, 206, 47.58, -12.27, 0.00025, 3, 207, 6.07, -24.03, 0.81721, 212, -13, -25.77, 0.00076, 206, 32.14, -21.05, 0.18202, 2, 207, -12.7, -26.42, 0.24513, 206, 14.98, -29.02, 0.75487, 2, 207, -30.86, -28.03, 0.04078, 206, -1.83, -36.06, 0.95922, 2, 207, -33.15, -9.73, 0.00358, 206, -9.56, -19.32, 0.99642, 1, 206, -10.48, 1.4, 1, 3, 207, -22.25, 35.64, 0.00281, 212, -48.25, 30.08, 0.00252, 206, -12.94, 27.21, 0.99467, 1, 206, -19.42, 40.06, 1, 3, 207, -12.9, 49.31, 0.0129, 212, -40.6, 44.77, 0.00921, 206, -8.17, 43.07, 0.9779, 3, 207, 2.72, 41.44, 0.1196, 212, -24.15, 38.82, 0.0903, 206, 9.1, 40.31, 0.7901, 3, 207, 21.29, 34.82, 0.2605, 212, -4.93, 34.47, 0.35601, 206, 28.8, 39.63, 0.38349, 3, 207, 32.54, 25.86, 0.22017, 212, 7.31, 26.93, 0.59797, 206, 42.24, 34.51, 0.18186, 3, 207, 15.92, 4.87, 0.89467, 212, -6.68, 4.1, 0.06379, 206, 32.76, 9.47, 0.04153, 3, 207, -5.26, 5.77, 0.00395, 212, -27.81, 2.46, 0.00092, 206, 12.31, 3.9, 0.99512], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 28, 28, 30, 30, 14], "width": 72, "height": 66}}, "fanren04": {"fanren04": {"type": "mesh", "uvs": [0.13347, 0, 0.07655, 0.11607, 0.10377, 0.26376, 0.04685, 0.40171, 0, 0.57861, 0.01715, 0.76038, 0.05675, 0.97786, 0.4379, 0.99084, 0.8141, 0.96325, 1, 0.90482, 0.97002, 0.68573, 0.85617, 0.45527, 0.77202, 0.29784, 0.527, 0.16476, 0.36612, 0.02194, 0.22752, 0, 0.23021, 0.09929, 0.31827, 0.22922, 0.40633, 0.40376, 0.48838, 0.55468, 0.53641, 0.76465], "triangles": [18, 3, 2, 2, 17, 18, 18, 12, 11, 18, 13, 12, 18, 17, 13, 2, 16, 17, 2, 1, 16, 17, 16, 13, 16, 14, 13, 1, 0, 16, 0, 15, 16, 16, 15, 14, 7, 20, 8, 20, 7, 5, 5, 7, 6, 20, 5, 19, 18, 4, 3, 18, 19, 4, 9, 8, 10, 8, 20, 10, 20, 19, 10, 19, 5, 4, 19, 11, 10, 19, 18, 11], "vertices": [1, 210, -9.31, 0.9, 1, 2, 211, -21.17, -13.17, 0.00028, 210, -4.2, -4.5, 0.99972, 2, 211, -12.1, -13.16, 0.10717, 210, 4.22, -7.89, 0.89283, 2, 211, -4.02, -16.43, 0.58575, 210, 10.49, -13.94, 0.41425, 2, 211, 6.47, -19.59, 0.96336, 210, 19.05, -20.78, 0.03664, 1, 211, 17.56, -20.23, 1, 1, 211, 30.92, -20.25, 1, 1, 211, 33.54, -5.21, 1, 2, 211, 33.67, 9.93, 0.9984, 210, 55.3, -3.56, 0.0016, 2, 211, 31.02, 17.74, 0.98763, 210, 55.76, 4.68, 0.01237, 2, 211, 17.61, 18.15, 0.85961, 210, 43.47, 10.07, 0.14039, 2, 211, 3.11, 15.32, 0.26282, 210, 28.96, 12.85, 0.73718, 2, 211, -6.83, 13.12, 0.00209, 210, 18.92, 14.53, 0.99791, 1, 210, 7.08, 9.85, 1, 1, 210, -3.65, 8.41, 1, 1, 210, -7.49, 4.2, 1, 1, 210, -2.14, 1.38, 1, 1, 210, 6.51, 0.64, 1, 2, 211, -2.17, -2.17, 0.2858, 210, 17.53, -1.4, 0.7142, 1, 211, 7.36, -0.02, 1, 2, 211, 20.31, 0.36, 0.99966, 210, 39.33, -7.45, 0.00034], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 40, "height": 61}}, "g4": {"g2": {"type": "mesh", "uvs": [0.2411, 0.43976, 0.25034, 0.2723, 0.30764, 0.10246, 0.40191, 0, 0.55349, 0, 0.6644, 0.02831, 0.74203, 0.15509, 0.75313, 0.28187, 0.74019, 0.39909, 0.7827, 0.30579, 0.80303, 0.18619, 0.88807, 0.10725, 0.98049, 0.11921, 1, 0.18619, 1, 0.31297, 0.95461, 0.41105, 0.87328, 0.48042, 0.79379, 0.5163, 0.73834, 0.54501, 0.74758, 0.64787, 0.77531, 0.76509, 0.85849, 0.84403, 0.94722, 0.87273, 1, 0.8847, 0.99528, 0.95885, 0.93058, 1, 0.83816, 1, 0.68658, 1, 0.55719, 1, 0.39822, 0.94211, 0.28731, 0.81532, 0.24479, 0.67179, 0.23925, 0.59525, 0.13943, 0.58568, 0.03592, 0.51391, 0, 0.43736, 0, 0.32493, 0.03407, 0.25317, 0.12834, 0.2436, 0.17825, 0.32015, 0.19858, 0.40626, 0.22658, 0.45368, 0.50703, 0.14619, 0.50703, 0.29098, 0.50058, 0.47196, 0.50488, 0.64737, 0.55006, 0.77545, 0.65548, 0.87011, 0.77597, 0.90631, 0.87709, 0.93137, 0.93949, 0.93972, 0.73989, 0.46938, 0.83739, 0.38979, 0.88839, 0.28497, 0.92289, 0.19179, 0.25412, 0.51985, 0.16862, 0.50626, 0.09962, 0.43832, 0.07262, 0.33156, 0.36362, 0.52761, 0.60212, 0.51791, 0.63812, 0.35679, 0.64112, 0.22479, 0.60212, 0.05591, 0.44762, 0.06756, 0.37562, 0.14909, 0.35462, 0.32379, 0.36512, 0.63049, 0.41612, 0.77414, 0.52712, 0.8712, 0.63212, 0.62855, 0.66212, 0.7392], "triangles": [29, 68, 69, 29, 30, 68, 69, 68, 46, 30, 31, 67, 30, 67, 68, 68, 45, 46, 46, 70, 71, 46, 45, 70, 68, 67, 45, 45, 67, 59, 59, 32, 55, 67, 32, 59, 67, 31, 32, 33, 56, 32, 32, 56, 55, 34, 57, 33, 33, 57, 56, 56, 41, 55, 41, 0, 55, 66, 59, 0, 59, 55, 0, 34, 35, 57, 56, 40, 41, 56, 57, 40, 35, 58, 57, 57, 39, 40, 57, 58, 39, 35, 36, 58, 36, 37, 58, 58, 38, 39, 58, 37, 38, 70, 18, 19, 70, 60, 18, 18, 51, 17, 18, 60, 51, 60, 61, 51, 16, 17, 52, 17, 51, 52, 16, 52, 15, 51, 8, 52, 51, 61, 8, 52, 53, 15, 15, 53, 14, 8, 9, 52, 8, 61, 7, 52, 9, 53, 53, 54, 14, 54, 13, 14, 53, 9, 10, 53, 10, 54, 10, 11, 54, 54, 12, 13, 54, 11, 12, 44, 43, 61, 44, 66, 43, 0, 1, 66, 61, 62, 7, 61, 43, 62, 66, 65, 43, 66, 1, 65, 43, 65, 42, 65, 64, 42, 43, 42, 62, 62, 6, 7, 1, 2, 65, 42, 63, 62, 62, 5, 6, 62, 63, 5, 65, 3, 64, 65, 2, 3, 42, 4, 63, 42, 64, 4, 64, 3, 4, 63, 4, 5, 45, 59, 44, 45, 60, 70, 45, 44, 60, 44, 59, 66, 60, 44, 61, 26, 49, 25, 25, 50, 24, 25, 49, 50, 27, 48, 26, 26, 48, 49, 24, 50, 23, 50, 22, 23, 50, 49, 22, 48, 21, 49, 49, 21, 22, 47, 20, 48, 48, 20, 21, 28, 47, 27, 27, 47, 48, 29, 69, 28, 28, 69, 47, 69, 46, 47, 47, 71, 20, 47, 46, 71, 71, 19, 20, 71, 70, 19], "vertices": [4, 295, 7.11, 16.47, 0.16085, 296, -4.51, 16.05, 0.15702, 301, -0.32, -4.72, 0.66606, 297, -18.76, -8.38, 0.01608, 3, 295, 15.62, 15.55, 0.12999, 296, 4.05, 16.15, 0.64013, 301, 4.09, -12.05, 0.22988, 3, 295, 24.14, 11.46, 0.01172, 296, 13, 13.09, 0.92414, 301, 5.99, -21.31, 0.06415, 2, 296, 18.72, 7.32, 0.9887, 301, 3.91, -29.17, 0.0113, 2, 296, 19.54, -2.65, 0.98344, 300, 5.59, 25.81, 0.01656, 2, 296, 18.7, -10.06, 0.90162, 300, 9.85, 19.68, 0.09838, 3, 295, 20.41, -17.1, 0.00074, 296, 12.68, -15.7, 0.75101, 300, 9.04, 11.47, 0.24825, 3, 295, 13.92, -17.59, 0.02643, 296, 6.3, -16.96, 0.53796, 300, 5.07, 6.32, 0.43561, 6, 298, 1.25, 24.47, 0.00149, 299, -0.02, 26.25, 3e-05, 295, 7.98, -16.52, 0.04237, 296, 0.27, -16.6, 0.11068, 300, 0.3, 2.61, 0.84482, 297, -3.86, 21.08, 0.0006, 1, 300, 5.63, 4.08, 1, 1, 300, 10.83, 7.54, 1, 1, 300, 17.66, 6.53, 1, 1, 300, 21.62, 1.85, 1, 1, 300, 20.18, -1.5, 1, 1, 300, 15.68, -6.15, 1, 1, 300, 10.05, -7.66, 1, 1, 300, 3.73, -6.47, 1, 5, 298, 7.05, 20.64, 0.01253, 299, 4, 20.59, 0.0015, 295, 1.88, -19.84, 0.01569, 300, -1.31, -4.14, 0.9645, 297, 3.08, 21.1, 0.00578, 6, 298, 4.4, 17.72, 0.10685, 299, 0.48, 18.82, 0.01758, 295, 0.55, -16.13, 0.15043, 296, -7.16, -17.1, 0.00217, 300, -4.96, -2.65, 0.66855, 297, 2.49, 17.2, 0.05441, 5, 298, 7.25, 13.28, 0.34354, 299, 1.53, 13.65, 0.11483, 295, -4.72, -16.54, 0.14654, 300, -8.17, -6.85, 0.28393, 297, 7.32, 15.07, 0.11115, 5, 298, 11.52, 8.71, 0.33292, 299, 3.85, 7.84, 0.53342, 295, -10.76, -18.15, 0.03488, 300, -11.01, -12.42, 0.06684, 297, 13.4, 13.62, 0.03194, 5, 298, 18.22, 7.49, 0.00949, 299, 9.66, 4.29, 0.98715, 295, -14.98, -23.49, 0.00078, 300, -9.86, -19.13, 0.00252, 297, 19.66, 16.32, 7e-05, 1, 299, 15.62, 3.32, 1, 1, 299, 19.14, 3.01, 1, 1, 299, 19.14, -0.79, 1, 1, 299, 15.06, -3.24, 1, 1, 299, 8.99, -3.75, 1, 2, 298, 11.51, -4.63, 0.58602, 299, -0.98, -4.58, 0.41398, 3, 298, 3.83, -8.37, 0.90916, 301, -33.83, 6.63, 0.00065, 297, 16.44, -4.86, 0.09019, 3, 298, -6.89, -10.32, 0.17265, 301, -23.55, 10.26, 0.04566, 297, 8.59, -12.41, 0.78169, 4, 298, -16.31, -7.72, 0.00423, 295, -12.14, 14.13, 0.03693, 301, -13.85, 9.19, 0.18581, 297, -0.7, -15.45, 0.77303, 3, 295, -4.72, 16.66, 0.12573, 301, -7.34, 4.82, 0.41535, 297, -8.43, -14.16, 0.45892, 4, 295, -0.81, 16.89, 0.1149, 296, -12.42, 15.52, 0.00616, 301, -4.79, 1.83, 0.68854, 297, -11.98, -12.5, 0.1904, 2, 301, 0.88, 5.23, 0.99934, 297, -15.74, -17.93, 0.00066, 1, 301, 8.57, 6.17, 1, 1, 301, 12.76, 4.34, 1, 1, 301, 16.06, -0.35, 1, 1, 301, 16.33, -4.63, 1, 1, 301, 11.52, -8.62, 1, 1, 301, 6.58, -7.32, 1, 3, 295, 8.92, 19.22, 0.00433, 296, -3.04, 18.99, 0.00473, 301, 2.95, -4.5, 0.99094, 4, 295, 6.44, 17.46, 0.08115, 296, -5.3, 16.95, 0.06511, 301, 0.05, -3.59, 0.84228, 297, -18.63, -9.56, 0.01146, 2, 296, 11.86, -0.21, 0.99911, 300, -1.8, 22.58, 0.00089, 2, 296, 4.5, -0.81, 0.99229, 300, -6.93, 17.28, 0.00771, 4, 298, -11.33, 14.19, 0.00055, 295, 4.84, -0.58, 0.99197, 296, -4.74, -1.15, 0.00062, 300, -13.65, 10.94, 0.00686, 4, 298, -7.16, 6.28, 0.00511, 295, -4.11, -0.54, 0.22017, 300, -19.67, 4.31, 0.00652, 297, -0.81, 1.27, 0.7682, 5, 298, -1.61, 1.71, 0.03452, 299, -10.92, 6.07, 1e-05, 295, -10.74, -3.28, 0.00316, 300, -22.06, -2.46, 0.00198, 297, 6.33, 0.54, 0.96033, 5, 298, 6.76, 0.42, 0.99279, 299, -3.58, 1.84, 0.00313, 295, -15.82, -10.05, 0.00096, 300, -20.42, -10.77, 0.00156, 297, 14.02, 4.09, 0.00156, 5, 298, 14.72, 2.25, 0.00779, 299, 4.5, 0.67, 0.99046, 295, -17.96, -17.93, 0.00047, 300, -15.98, -17.62, 0.00118, 297, 19.64, 10.01, 0.00011, 2, 299, 11.25, -0.04, 1, 300, -12.07, -23.18, 0, 1, 299, 15.39, -0.12, 1, 6, 298, 2.81, 21.23, 0.02274, 299, 0.26, 22.68, 0.0024, 295, 4.4, -16.37, 0.06823, 296, -3.3, -16.88, 0.01901, 300, -2.21, 0.05, 0.87578, 297, -0.78, 19.24, 0.01183, 1, 300, 5.24, -1.5, 1, 1, 300, 11.38, 0, 1, 1, 300, 16.32, 1.83, 1, 4, 295, 3, 15.76, 0.14167, 296, -8.51, 14.86, 0.04339, 301, -3.38, -1.87, 0.73431, 297, -14.8, -9.7, 0.08064, 1, 301, 1.63, 0.81, 1, 1, 301, 7.35, 0.6, 1, 1, 301, 11.94, -2.83, 1, 4, 295, 2.34, 8.56, 0.50977, 296, -8.31, 7.62, 0.04997, 301, -9.52, -5.71, 0.28278, 297, -10.8, -3.67, 0.15748, 6, 298, -4.28, 15.02, 0.04966, 299, -8.6, 19.45, 0.00185, 295, 2.26, -7.19, 0.6005, 296, -6.52, -8.02, 0.03214, 300, -10.46, 4.59, 0.24702, 297, -3.25, 10.15, 0.06882, 5, 298, -5.75, 23.45, 0.00268, 295, 10.38, -9.87, 0.19757, 296, 1.87, -9.71, 0.43699, 300, -3.04, 8.85, 0.36258, 297, -9.14, 16.36, 0.00018, 3, 295, 17.1, -10.31, 0.01771, 296, 8.59, -9.36, 0.77033, 300, 1.78, 13.55, 0.21196, 2, 296, 16.96, -6.08, 0.94289, 300, 5.92, 21.53, 0.05711, 2, 296, 15.53, 4.03, 0.9936, 301, -0.54, -28.09, 0.0064, 3, 295, 21.6, 7.06, 0.00844, 296, 11, 8.42, 0.94288, 301, 0.95, -21.95, 0.04869, 4, 295, 12.75, 8.77, 0.2032, 296, 2, 9.07, 0.62077, 301, -3.05, -13.87, 0.17576, 297, -20.06, 1.08, 0.00026, 4, 295, -2.91, 8.65, 0.28561, 296, -13.53, 7.09, 0.00224, 301, -12.62, -1.48, 0.23619, 297, -6.22, -6.25, 0.47597, 4, 298, -9.59, -2.1, 0.01574, 295, -10.35, 5.55, 0.01445, 301, -19.59, 2.57, 0.07661, 297, 1.8, -7.05, 0.89319, 3, 298, -0.83, -3.34, 0.55426, 301, -28.43, 2.4, 0.00415, 297, 9.78, -3.24, 0.44159, 6, 298, -0.03, 10.82, 0.20701, 299, -6.15, 13.99, 0.01984, 295, -3.45, -8.96, 0.29931, 296, -11.98, -10.46, 7e-05, 300, -12.96, -0.84, 0.20688, 297, 2.61, 9, 0.26689, 5, 298, 4.23, 6.62, 0.56364, 299, -3.71, 8.53, 0.07562, 295, -9.17, -10.74, 0.0814, 300, -15.46, -6.27, 0.09618, 297, 8.48, 7.85, 0.18317], "hull": 42, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 0, 82, 8, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 114, 116, 110, 118, 120, 102, 120, 122, 122, 124, 124, 126, 128, 130, 130, 132, 134, 136, 136, 138, 140, 142], "width": 66, "height": 51}}, "fanren06": {"fanren06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-4.67, -35.39, -1.54, 35.54, 23.44, 34.44, 20.31, -36.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "height": 25}}, "bwc01": {"bwc01": {"type": "mesh", "uvs": [0.21741, 0.04742, 0.28826, 0.18882, 0, 0.27812, 0.15112, 1, 0.76826, 1, 1, 0.85486, 1, 0.67254, 0.72941, 0.09021, 0.41398, 0.14417, 0.34084, 0.00835, 0.27912, 0.02882, 0.36285, 0.19741, 0.62401, 0.84392], "triangles": [8, 11, 10, 11, 7, 6, 7, 11, 8, 12, 11, 6, 12, 6, 5, 2, 12, 3, 11, 2, 1, 12, 2, 11, 4, 12, 5, 3, 12, 4, 10, 11, 1, 8, 10, 9, 0, 10, 1], "vertices": [1, 125, -0.51, -3.59, 1, 1, 126, -0.83, -2.69, 1, 2, 125, 7.21, -13.42, 0.00244, 126, 0.29, -13.43, 0.99756, 1, 126, 31.65, -16.28, 1, 1, 126, 37.19, 4.6, 1, 1, 126, 33.24, 14.04, 1, 1, 126, 25.66, 16.05, 1, 1, 126, -0.97, 13.32, 1, 1, 126, -1.56, 2.06, 1, 1, 125, -1.07, 1.01, 1, 1, 125, -0.75, -1.3, 1, 1, 126, 0.2, -0.26, 1, 1, 126, 29.41, 1.44, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 20, 22], "width": 35, "height": 43}}, "bwc07": {"bwc07": {"type": "mesh", "uvs": [0, 0.52976, 0.04949, 0.26276, 0.12793, 0, 0.32704, 0, 0.57745, 0, 0.79467, 0.05806, 0.9063, 0.23903, 0.98323, 0.4467, 0.8897, 0.65733, 0.79618, 0.77896, 0.75092, 1, 0.61064, 1, 0.57745, 0.84423, 0.38135, 0.86203, 0.13396, 0.7226, 0.23186, 0.37903, 0.4954, 0.40453, 0.76135, 0.33371, 0.68279, 0.83196], "triangles": [8, 17, 6, 11, 12, 18, 11, 18, 10, 12, 16, 18, 14, 15, 13, 14, 0, 15, 13, 16, 12, 13, 15, 16, 18, 17, 9, 18, 16, 17, 9, 17, 8, 10, 18, 9, 8, 6, 7, 15, 0, 1, 15, 3, 16, 16, 4, 17, 16, 3, 4, 1, 2, 15, 15, 2, 3, 17, 5, 6, 17, 4, 5], "vertices": [2, 109, 0.7, 24.66, 0.39594, 108, 3.81, 21.06, 0.60406, 2, 109, 8.92, 22.4, 0.53469, 108, 11.94, 23.64, 0.46531, 2, 109, 17.15, 18.43, 0.71258, 108, 21.01, 24.79, 0.28742, 2, 109, 18.1, 6.72, 0.49806, 108, 28.19, 15.5, 0.50194, 2, 109, 19.29, -8.01, 0.568, 108, 37.21, 3.8, 0.432, 2, 109, 18.59, -20.92, 0.568, 108, 43.67, -7.41, 0.432, 2, 109, 13.71, -27.93, 0.568, 108, 43.39, -15.94, 0.432, 2, 109, 7.87, -32.96, 0.36787, 108, 41.23, -23.34, 0.63213, 2, 109, 1.13, -27.97, 0.18851, 108, 32.86, -22.83, 0.81149, 2, 109, -2.96, -22.76, 0.147, 108, 26.6, -20.69, 0.853, 2, 109, -9.78, -20.64, 0.09822, 108, 19.72, -22.63, 0.90178, 2, 109, -10.45, -12.39, 0.08, 108, 14.66, -16.08, 0.92, 2, 109, -5.95, -10.06, 0.08, 108, 17.16, -11.68, 0.92, 2, 109, -7.42, 1.43, 0.00358, 108, 9.67, -2.84, 0.99642, 2, 109, -4.43, 16.32, 0.27084, 108, 4.06, 11.27, 0.72916, 2, 109, 6.31, 11.39, 0.6023, 108, 15.75, 12.99, 0.3977, 2, 109, 6.8, -4.17, 0.568, 108, 24.65, 0.22, 0.432, 2, 109, 10.19, -19.63, 0.56601, 108, 35.92, -10.91, 0.43399, 2, 109, -5.08, -16.22, 0.08594, 108, 21.25, -16.37, 0.91406], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 2, 30, 30, 32, 32, 34], "width": 59, "height": 30}}, "bwc03": {"bwc03": {"type": "mesh", "uvs": [0.346, 0.17162, 0.38336, 0.05992, 0.4936, 0, 0.77947, 0.02773, 0.99807, 0.11672, 1, 0.30416, 0.94763, 0.44616, 0.914, 0.60709, 0.91213, 0.78128, 0.92894, 0.96872, 0.68418, 1, 0.44689, 0.92896, 0.20587, 0.78128, 0.03397, 0.59952, 0, 0.44237, 0.15915, 0.36474, 0.24884, 0.28712, 0.70734, 0.19549, 0.5639, 0.42301, 0.40174, 0.64631, 0.48697, 0.20181, 0.37056, 0.35771, 0.23127, 0.49674, 0.85078, 0.29872, 0.7406, 0.53677, 0.68447, 0.78957], "triangles": [12, 19, 11, 11, 19, 25, 12, 22, 19, 12, 13, 22, 22, 13, 14, 9, 25, 8, 9, 10, 25, 10, 11, 25, 25, 19, 24, 8, 25, 7, 24, 19, 18, 7, 25, 24, 22, 21, 19, 19, 21, 18, 22, 14, 15, 22, 16, 21, 22, 15, 16, 21, 20, 18, 16, 0, 21, 21, 0, 20, 7, 24, 6, 24, 23, 6, 24, 18, 23, 6, 23, 5, 18, 17, 23, 18, 20, 17, 17, 3, 23, 0, 1, 20, 20, 2, 17, 20, 1, 2, 17, 2, 3, 23, 4, 5, 23, 3, 4], "vertices": [4, 109, -1.66, 29.19, 0.12285, 108, -0.63, 23.57, 0.44454, 110, -9, -22.19, 0.41476, 111, -19.64, -29.5, 0.01786, 4, 109, 6.92, 27.04, 0.25572, 108, 7.74, 26.44, 0.53507, 110, -17.59, -24.27, 0.20858, 111, -27.21, -34.07, 0.00063, 3, 109, 12.08, 19.05, 0.40542, 108, 16.42, 22.56, 0.49541, 110, -25.87, -19.59, 0.09918, 3, 109, 11.76, -2.77, 0.99992, 108, 28.05, 4.09, 8e-05, 110, -35.72, -0.12, 0, 2, 109, 6.45, -19.87, 0.8557, 108, 32.91, -13.14, 0.1443, 3, 109, -7.55, -21.15, 0.45868, 108, 21.88, -21.85, 0.51253, 110, -27.16, 25.13, 0.02879, 4, 109, -18.48, -18.04, 0.1373, 108, 11.01, -25.2, 0.68955, 110, -16.03, 27.46, 0.17215, 111, -41.33, 15.72, 0.00101, 4, 109, -30.72, -16.47, 0.01162, 108, -0.1, -30.56, 0.5, 110, -4.46, 31.75, 0.45886, 111, -31.6, 23.3, 0.02951, 3, 108, -10.53, -38.43, 0.26673, 110, 6.65, 38.61, 0.6203, 111, -23.07, 33.2, 0.11297, 3, 108, -20.88, -48.03, 0.1876, 110, 17.85, 47.2, 0.64568, 111, -14.99, 44.77, 0.16672, 3, 108, -34.1, -34.74, 0.11918, 110, 29.78, 32.73, 0.58563, 111, 0.75, 34.57, 0.29519, 3, 108, -40.9, -17.2, 0.02569, 110, 34.91, 14.65, 0.27794, 111, 11.1, 18.88, 0.69637, 2, 110, 35.34, -6.76, 0.00702, 111, 17.97, -1.4, 0.99298, 2, 110, 30.8, -25.08, 0.30139, 111, 19.18, -20.24, 0.69861, 3, 108, -32.77, 31.98, 1e-05, 110, 22.22, -33.57, 0.41197, 111, 13.55, -30.92, 0.58802, 4, 109, -17.24, 42.18, 0.00147, 108, -20.77, 25.96, 0.02808, 110, 10.84, -26.45, 0.59801, 111, 0.55, -27.57, 0.37244, 4, 109, -10.89, 35.85, 0.01872, 108, -12, 24.13, 0.14708, 110, 2.27, -23.8, 0.68697, 111, -8.41, -27.63, 0.14724, 3, 109, -1.22, 1.68, 0.0037, 108, 14.74, 0.74, 0.99607, 110, -22.16, 1.97, 0.00022, 2, 108, -5.43, -1.06, 0.27832, 110, -1.91, 1.88, 0.72168, 3, 108, -26.22, -1.54, 0.00028, 110, 18.83, 0.42, 0.42268, 111, 0.07, 0.46, 0.57704, 4, 109, -3.05, 18.33, 0.14439, 108, 4.13, 13.71, 0.61905, 110, -12.81, -11.93, 0.23489, 111, -26.37, -20.86, 0.00167, 4, 109, -15.42, 26.21, 0.01457, 108, -10.53, 13.57, 0.15423, 110, 1.8, -13.16, 0.76369, 111, -12.07, -17.62, 0.06751, 3, 108, -25.26, 15.58, 0.00619, 110, 16.27, -16.53, 0.54895, 111, 2.74, -16.47, 0.44487, 3, 109, -8.06, -9.82, 0.297, 108, 15.27, -12.62, 0.67331, 110, -21.44, 15.33, 0.0297, 4, 109, -26.53, -2.91, 0.0034, 108, -3.98, -16.9, 0.51311, 110, -1.88, 17.79, 0.47265, 111, -24.92, 10.77, 0.01084, 3, 108, -21.6, -25.11, 0.15688, 110, 16.43, 24.32, 0.63733, 111, -9.44, 22.52, 0.20579], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 6, 34, 34, 36, 36, 38, 4, 40, 40, 42, 42, 44, 8, 46, 46, 48, 48, 50], "width": 76, "height": 75}}, "bwc04": {"bwc04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-62.33, 33.26, 101.44, 6.09, 93.74, -40.27, -70.02, -13.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 47}}, "niutou01": {"niutou01": {"type": "mesh", "uvs": [0.27456, 0.84948, 0.31169, 0.90605, 0.3519, 0.98619, 0.63343, 0.96262, 0.86856, 0.87776, 1, 0.79526, 0.95209, 0.71276, 0.88403, 0.64205, 0.9459, 0.51948, 0.90568, 0.30262, 0.73243, 0.10227, 0.48803, 0, 0.19103, 0.02684, 0, 0.19184, 0.02397, 0.43462, 0.10131, 0.62555, 0.17247, 0.73869, 0.37262, 0.13336, 0.61123, 0.25324, 0.73365, 0.46201, 0.6071, 0.69126, 0.63453, 0.78628, 0.69595, 0.85516, 0.28432, 0.34563, 0.38241, 0.55854, 0.38848, 0.78051, 0.15577, 0.23667], "triangles": [22, 21, 6, 4, 22, 6, 5, 4, 6, 3, 25, 21, 3, 21, 22, 1, 25, 3, 3, 22, 4, 2, 1, 3, 19, 9, 8, 15, 14, 24, 7, 19, 8, 20, 24, 19, 20, 19, 7, 16, 15, 24, 25, 24, 20, 16, 24, 25, 21, 20, 7, 21, 7, 6, 25, 20, 21, 0, 16, 25, 1, 0, 25, 17, 12, 11, 26, 13, 12, 26, 12, 17, 18, 11, 10, 17, 11, 18, 18, 10, 9, 23, 26, 17, 23, 17, 18, 14, 13, 26, 14, 26, 23, 19, 18, 9, 24, 23, 18, 24, 18, 19, 14, 23, 24], "vertices": [3, 246, 21.48, -22.5, 0.05204, 247, 22.16, -6.62, 0.74452, 248, -7.58, -0.72, 0.20344, 3, 246, 24.05, -23.15, 0.0151, 247, 23.72, -4.47, 0.56615, 248, -6.26, -3.03, 0.41875, 3, 246, 27.44, -24.37, 0.00076, 247, 26.12, -1.77, 0.37274, 248, -4.78, -6.32, 0.62651, 1, 248, 4.16, -4.74, 1, 2, 247, 14.32, 10.74, 0.01022, 248, 11.47, -0.69, 0.98978, 2, 247, 9.26, 12.85, 0.14793, 248, 15.47, 3.06, 0.85207, 2, 247, 6.89, 9.89, 0.33384, 248, 13.74, 6.43, 0.66616, 2, 247, 5.27, 6.59, 0.72801, 248, 11.39, 9.27, 0.27199, 2, 247, -0.24, 5.95, 0.98139, 248, 13.07, 14.56, 0.01861, 2, 246, 17.27, 7.79, 0.387, 247, -7.75, 0.58, 0.613, 2, 246, 7.29, 9.09, 0.9522, 247, -12.65, -8.22, 0.0478, 1, 246, -1.05, 5.97, 1, 2, 246, -6.4, -1.97, 0.99498, 247, -7.36, -25.02, 0.00502, 2, 246, -5.14, -11.12, 0.92642, 247, 1.68, -27.21, 0.07358, 2, 246, 3.07, -17.2, 0.69278, 247, 10.4, -21.8, 0.30722, 2, 246, 10.75, -20.56, 0.37791, 247, 16.38, -15.89, 0.62209, 3, 246, 15.83, -21.94, 0.19744, 247, 19.55, -11.67, 0.78546, 248, -11.12, 3.74, 0.0171, 2, 246, 0.78, -0.49, 0.99905, 247, -6.1, -17.8, 0.00095, 2, 246, 9.56, 2.01, 0.98392, 247, -5.19, -8.71, 0.01608, 2, 246, 18.75, -0.75, 0.88297, 247, 0.79, -1.18, 0.11703, 2, 246, 23.39, -10.1, 0.00208, 247, 11.26, -0.29, 0.99792, 2, 247, 14.4, 2.34, 0.56949, 248, 3.77, 2.68, 0.43051, 1, 248, 5.9, -0.09, 1, 2, 246, 5.68, -8.45, 0.83671, 247, 3.17, -16.16, 0.16329, 2, 246, 14.49, -11.91, 0.38907, 247, 9.66, -9.24, 0.61093, 3, 246, 21.67, -17.85, 0.05353, 247, 17.88, -4.74, 0.86192, 248, -4.11, 2.42, 0.08455, 2, 246, -0.47, -8.58, 0.91934, 247, 1.02, -21.92, 0.08066], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 24, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 46, 52], "width": 32, "height": 42}}, "bwc06": {"bwc06": {"type": "mesh", "uvs": [0.28109, 0.00096, 0.49016, 0.0432, 0.71447, 0.2713, 0.92789, 0.45716, 0.96274, 0.71568, 1, 0.91675, 0.69487, 1, 0.22012, 0.95899, 0, 0.85085, 0.08074, 0.67682, 0.13083, 0.50616, 0.05678, 0.27975, 0.07421, 0.08375, 0.41628, 0.27895, 0.55775, 0.51454, 0.5336, 0.74477], "triangles": [7, 15, 6, 6, 4, 5, 6, 15, 4, 8, 9, 7, 7, 9, 15, 9, 10, 15, 15, 14, 4, 14, 3, 4, 15, 10, 14, 10, 13, 14, 14, 2, 3, 14, 13, 2, 10, 11, 13, 11, 12, 13, 12, 0, 13, 13, 1, 2, 13, 0, 1], "vertices": [1, 112, -7.72, 3.78, 1, 2, 112, -1.16, 10.96, 0.98832, 113, -27.99, -1.69, 0.01168, 2, 112, 15.25, 13.7, 0.80331, 113, -14.52, 8.09, 0.19669, 2, 112, 29.27, 17.16, 0.479, 113, -3.51, 17.43, 0.521, 2, 112, 43.26, 11.55, 0.15734, 113, 11.52, 18.63, 0.84266, 2, 112, 54.35, 7.59, 0.00902, 113, 23.22, 20.02, 0.99098, 2, 112, 52.21, -6.81, 0.00128, 113, 27.71, 6.18, 0.99872, 2, 112, 40.13, -24.59, 0.01303, 113, 24.82, -15.12, 0.98697, 2, 112, 29.96, -30.42, 0.07201, 113, 18.31, -24.87, 0.92799, 2, 112, 22.73, -22.5, 0.27804, 113, 8.31, -20.99, 0.72196, 2, 112, 15.03, -15.88, 0.5926, 113, -1.53, -18.5, 0.4074, 2, 112, 1.86, -12.7, 0.86695, 113, -14.74, -21.51, 0.13305, 2, 112, -7.82, -6.7, 0.99297, 113, -26.09, -20.46, 0.00703, 2, 112, 9.38, 1.63, 0.92773, 113, -14.4, -5.34, 0.07227, 2, 112, 24.43, 0.88, 0.59465, 113, -0.58, 0.69, 0.40535, 2, 112, 35.73, -6.32, 0.39197, 113, 12.74, -0.72, 0.60803], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 0, 26, 26, 28, 28, 30], "width": 45, "height": 58}}, "a23": {"a23": {"type": "mesh", "uvs": [0.88723, 0.00456, 0.93139, 0.01813, 1, 0.11974, 0.98415, 0.25571, 0.66711, 0.51336, 0.63372, 0.54049, 0.63273, 0.57429, 0.62891, 0.70434, 0.37716, 0.85565, 0.34558, 0.87463, 0.34019, 0.90679, 0.33856, 0.96628, 0.24221, 0.99517, 0.01469, 0.99546, 0.01566, 0.95639, 0.11247, 0.88692, 0.14689, 0.8669, 0.16184, 0.84155, 0.19029, 0.79422, 0.21074, 0.60117, 0.17679, 0.56016, 0.16353, 0.52189, 0.16658, 0.47794, 0.17857, 0.40597, 0.24064, 0.26263, 0.2254, 0.09312, 0.33422, 0.05313, 0.43742, 0.0297, 0.64493, 0.00479, 0.24762, 0.87695, 0.40794, 0.54863], "triangles": [17, 29, 16, 29, 10, 15, 29, 15, 16, 10, 11, 15, 14, 15, 11, 12, 14, 11, 13, 14, 12, 1, 2, 28, 2, 24, 25, 28, 0, 1, 2, 27, 28, 2, 26, 27, 26, 2, 25, 3, 24, 2, 3, 23, 24, 4, 23, 3, 22, 23, 4, 22, 30, 21, 22, 4, 30, 4, 5, 30, 20, 21, 30, 6, 30, 5, 19, 20, 30, 6, 19, 30, 7, 19, 6, 18, 19, 7, 8, 18, 7, 17, 18, 8, 17, 8, 29, 8, 9, 29, 10, 29, 9], "vertices": [1, 82, -29.65, 5.61, 1, 1, 82, -27.72, 9.02, 1, 1, 82, -8.77, 18.88, 1, 1, 82, 18.42, 25.66, 1, 2, 83, -4.73, 23.3, 0.23929, 82, 74.86, 21.41, 0.76071, 2, 83, 0.92, 21.39, 0.47317, 82, 80.8, 20.96, 0.52683, 2, 83, 7.88, 21.54, 0.76043, 82, 87.51, 22.82, 0.23957, 1, 83, 34.66, 22.11, 1, 1, 83, 66.29, 7.44, 1, 1, 83, 70.25, 5.6, 1, 2, 83, 76.89, 5.46, 0.70507, 99, 4.96, 7.04, 0.29493, 2, 83, 89.14, 5.73, 0.07874, 99, 16.73, 10.42, 0.92126, 2, 83, 95.27, -0.06, 0.01046, 99, 24.14, 6.38, 0.98954, 1, 99, 28.2, -7.13, 1, 1, 99, 20.46, -9.35, 1, 1, 99, 5.04, -7.66, 1, 2, 83, 69.03, -6.76, 0.13915, 99, 0.48, -6.78, 0.86085, 2, 83, 63.78, -5.99, 0.71884, 99, -4.8, -7.37, 0.28116, 2, 83, 53.99, -4.52, 0.9988, 99, -14.65, -8.45, 0.0012, 1, 83, 14.2, -4.44, 1, 2, 83, 5.82, -6.8, 0.97845, 82, 92.51, -5.16, 0.02155, 2, 83, -2.04, -7.86, 0.33862, 82, 85.16, -8.12, 0.66138, 2, 83, -11.1, -7.94, 0.00012, 82, 76.41, -10.44, 0.99988, 1, 82, 61.95, -13.81, 1, 1, 82, 32.51, -18.26, 1, 1, 82, -0.8, -28.8, 1, 1, 82, -10.58, -24.58, 1, 1, 82, -16.98, -19.76, 1, 1, 82, -25.46, -8.81, 1, 2, 83, 70.91, -0.46, 0.40273, 99, 0.69, -0.2, 0.59727, 2, 83, 3.01, 7.45, 0.71242, 82, 86.28, 7.97, 0.28758], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 22, 24, 24, 26, 26, 28, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 28, 30, 30, 32, 32, 34, 34, 36, 14, 16, 16, 18, 18, 20, 20, 22, 40, 42, 42, 44, 44, 46, 6, 8, 8, 10, 10, 12, 12, 14, 36, 38, 38, 40, 18, 58, 10, 60], "width": 24, "height": 82}}, "hwc02": {"hwc02": {"type": "mesh", "uvs": [0.30671, 0.05089, 0.50992, 0, 0.68663, 0.01483, 0.81188, 0.12445, 0.83933, 0.31483, 0.90109, 0.44752, 1, 0.50906, 0.97144, 0.63983, 0.88908, 0.80522, 0.70551, 0.88599, 0.49276, 0.95137, 0.26972, 1, 0.04668, 0.97445, 0, 0.88022, 0.12389, 0.80329, 0.12904, 0.64368, 0.01237, 0.46868, 0, 0.28983, 0.1256, 0.13599, 0.41515, 0.24569, 0.49057, 0.41552, 0.54777, 0.62521, 0.59025, 0.79303, 0.23344, 0.3545, 0.29499, 0.5404, 0.3309, 0.73996, 0.62139, 0.20491, 0.68995, 0.38805, 0.73944, 0.5531, 0.80287, 0.70735, 0.35147, 0.85813], "triangles": [10, 22, 9, 9, 29, 8, 9, 22, 29, 8, 29, 7, 22, 28, 29, 22, 21, 28, 29, 28, 7, 28, 5, 7, 7, 5, 6, 21, 27, 28, 21, 20, 27, 5, 27, 4, 5, 28, 27, 27, 26, 4, 20, 26, 27, 20, 19, 26, 26, 3, 4, 19, 1, 26, 19, 0, 1, 26, 2, 3, 26, 1, 2, 11, 30, 10, 12, 14, 11, 11, 14, 30, 12, 13, 14, 10, 30, 22, 14, 25, 30, 30, 25, 22, 14, 15, 25, 25, 21, 22, 15, 24, 25, 25, 24, 21, 24, 15, 23, 24, 20, 21, 15, 16, 23, 24, 23, 20, 16, 17, 23, 23, 19, 20, 17, 18, 23, 23, 0, 19, 23, 18, 0], "vertices": [4, 146, -16.82, 13.72, 0.63421, 147, -22.91, 30.63, 0.01952, 149, -21.07, -23.97, 0.3449, 150, -34.32, -36.17, 0.00137, 3, 146, -23.06, 34.02, 0.14214, 147, -17.38, 51.13, 0.00839, 149, -12.05, -4.74, 0.84948, 1, 149, 0.06, 8.68, 1, 2, 149, 15.76, 12.68, 0.98056, 150, -10.43, 9.97, 0.01944, 2, 149, 31.18, 4.28, 0.0097, 150, 6.81, 6.68, 0.9903, 2, 150, 20.31, 8.48, 0.85246, 151, -6.85, 7.26, 0.14754, 2, 150, 29.02, 16.05, 0.3202, 151, 0.18, 16.41, 0.6798, 2, 150, 39.21, 9.24, 0.03474, 151, 11.52, 11.76, 0.96526, 3, 147, 65.47, 51.34, 0.00207, 148, -18.34, 61.09, 0.00607, 151, 25.15, 1.22, 0.99185, 4, 147, 63.28, 31.35, 0.07881, 148, -3.66, 47.33, 0.19914, 150, 50.99, -23.9, 0.02789, 151, 29.65, -18.38, 0.69416, 4, 147, 58.47, 9.37, 0.08234, 148, 11.03, 30.29, 0.63439, 150, 49.17, -46.33, 0.02339, 151, 32.32, -40.73, 0.25988, 3, 148, 24.79, 11.64, 0.97684, 150, 45.56, -69.22, 0.00011, 151, 33.32, -63.88, 0.02304, 1, 148, 32.45, -9.91, 1, 1, 148, 26.75, -17.89, 1, 2, 147, 29.06, -17.69, 0.05387, 148, 15.01, -9.48, 0.94613, 2, 147, 16.44, -10.48, 0.81837, 148, 1.66, -15.24, 0.18163, 2, 146, 23.44, -13.23, 0.45404, 147, -3.19, -13.62, 0.54596, 2, 146, 7.31, -15.76, 0.9916, 147, -18.19, -7.18, 0.0084, 2, 146, -7.65, -4.09, 0.98876, 149, -26.26, -43.31, 0.01124, 5, 146, -0.02, 26.14, 0.36064, 147, -2.08, 32.19, 0.16687, 149, -0.29, -26.07, 0.39934, 150, -13.89, -31.83, 0.07215, 151, -32.37, -39.03, 0.001, 6, 146, 14.79, 35.02, 0.11699, 147, 15.18, 31.82, 0.36552, 148, -32.92, 9.15, 0.00551, 149, 16.64, -29.45, 0.21642, 150, 3.27, -29.88, 0.25742, 151, -15.94, -33.72, 0.03814, 6, 146, 33.35, 42.33, 0.01074, 147, 34.79, 28.12, 0.33684, 148, -18.19, 22.61, 0.1388, 149, 35.3, -36.52, 0.02726, 150, 23.19, -30.92, 0.23136, 151, 3.79, -30.78, 0.255, 5, 147, 50.33, 24.87, 0.15642, 148, -6.25, 33.08, 0.30881, 149, 50.03, -42.45, 0.00064, 150, 39.03, -32.06, 0.07499, 151, 19.54, -28.76, 0.45915, 5, 146, 11.31, 8.44, 0.67483, 147, -1.92, 11.17, 0.26825, 149, -3.8, -46.79, 0.04161, 150, -10.91, -52.63, 0.01517, 151, -25.32, -58.83, 0.00014, 5, 146, 27.68, 16.02, 0.04507, 147, 15.98, 8.88, 0.88765, 149, 13.42, -52.18, 0.02309, 150, 7.13, -52.51, 0.03407, 151, -7.66, -55.13, 0.01013, 5, 147, 33.76, 3.68, 0.26502, 148, 0.74, 7.12, 0.67553, 149, 30.02, -60.41, 0.00122, 150, 25.45, -55.28, 0.01628, 151, 10.84, -54.2, 0.04196, 4, 146, -5.37, 46.82, 0.0318, 147, 4.41, 52.54, 0.03163, 149, 9.66, -7.17, 0.86442, 150, -10.19, -10.79, 0.07215, 6, 146, 10.7, 55.1, 0.0124, 147, 22.42, 50.99, 0.06707, 148, -43.91, 26.45, 0.00249, 149, 27.12, -11.84, 0.12024, 150, 7.87, -9.91, 0.78102, 151, -15.4, -13.24, 0.01677, 6, 146, 25.28, 61.31, 0.00161, 147, 38.06, 48.48, 0.06089, 148, -32.51, 37.46, 0.01924, 149, 42.08, -17.05, 0.00339, 150, 23.71, -10.3, 0.53535, 151, 0.2, -10.47, 0.37951, 4, 147, 53.5, 47.69, 0.01947, 148, -22.6, 49.32, 0.02429, 150, 39.11, -9.02, 0.02357, 151, 15.04, -6.15, 0.93267, 4, 147, 44.26, 0.55, 0.04013, 148, 9.55, 13.63, 0.87035, 150, 36.27, -56.98, 0.00951, 151, 21.79, -53.72, 0.08001], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 36, 46, 46, 48, 48, 50, 2, 52, 52, 54, 54, 56, 56, 58, 50, 60], "width": 102, "height": 91}}, "bwc09": {"bwc09": {"type": "mesh", "uvs": [0.29213, 0.01019, 0.70986, 0, 1, 0.06564, 0.97094, 0.1372, 0.80124, 0.2821, 0.74032, 0.46099, 0.82299, 0.64166, 1, 0.79551, 0.93613, 0.9422, 0.64894, 1, 0.80124, 0.86706, 0.44443, 0.749, 0.10502, 0.56116, 0, 0.31609, 0.06151, 0.13899, 0.42164, 0.21077, 0.40876, 0.47031, 0.66387, 0.69278, 0.89063, 0.84639, 0.85713, 0.91101], "triangles": [9, 19, 8, 9, 10, 19, 19, 18, 8, 8, 18, 7, 19, 10, 18, 18, 10, 17, 10, 11, 17, 18, 17, 7, 17, 6, 7, 6, 17, 16, 11, 12, 17, 12, 16, 17, 6, 16, 5, 12, 13, 16, 4, 5, 16, 16, 13, 15, 4, 16, 15, 13, 14, 15, 4, 15, 3, 14, 0, 15, 15, 1, 3, 15, 0, 1, 3, 1, 2], "vertices": [1, 128, 0.82, -13.21, 1, 1, 128, -4.32, 1.39, 1, 1, 128, -1.61, 13.34, 1, 2, 128, 4.88, 14.08, 0.97866, 129, -15.36, 25.91, 0.02134, 3, 128, 19.15, 11.65, 0.6878, 129, -4.46, 16.38, 0.31205, 130, -23.87, 25.34, 0.00014, 3, 128, 35.24, 13.92, 0.35447, 129, 10.46, 9.93, 0.41244, 130, -11.8, 14.46, 0.23309, 4, 128, 50.03, 21.35, 0.04247, 129, 26.95, 8.55, 0.3911, 130, 3.39, 7.88, 0.54509, 131, -15.37, -8.57, 0.02133, 3, 129, 42.04, 11.18, 0.10039, 130, 18.52, 5.54, 0.54495, 131, -4.92, 2.63, 0.35466, 2, 130, 28.13, -3.81, 0.312, 131, 8.22, 5.3, 0.688, 2, 130, 26.49, -15.53, 0.04566, 131, 16.98, -2.65, 0.95434, 3, 129, 46.29, 2.37, 0.03324, 130, 19.74, -4.16, 0.34576, 131, 3.78, -1.83, 0.621, 4, 128, 63.18, 10.56, 0.00013, 129, 32.53, -7.53, 0.36644, 130, 3.54, -9.14, 0.34576, 131, -1.22, -18.02, 0.28767, 3, 128, 50.4, -6.18, 0.27342, 129, 12.89, -15.13, 0.42648, 130, -17.5, -10.07, 0.3001, 2, 128, 30.27, -16, 0.60675, 129, -9.4, -13.01, 0.39325, 2, 128, 14.32, -18.22, 0.93996, 129, -24.16, -6.57, 0.06004, 2, 128, 16.85, -3.62, 0.66667, 129, -14.39, 4.55, 0.33333, 3, 128, 39.44, 2.36, 0.33333, 129, 8, -2.12, 0.33608, 130, -17.98, 3.82, 0.33059, 3, 129, 29.81, 1.65, 0.33608, 130, 3.9, 0.42, 0.3384, 131, -8.92, -12.34, 0.32552, 3, 129, 45.37, 6.06, 0.00275, 130, 20.05, -0.37, 0.3384, 131, 0.83, 0.56, 0.65885, 2, 130, 24.17, -4.66, 0.01173, 131, 6.69, 1.55, 0.98827], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 2, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 37, "height": 90}}, "hwc04": {"hwc04": {"type": "mesh", "uvs": [0.47364, 0.43385, 0.85324, 0.12203, 0.86716, 0.03127, 0.88943, 0, 0.9609, 0.01371, 1, 0.12789, 0.9841, 0.25525, 0.93027, 0.25379, 0.89222, 0.22597, 0.47364, 0.57731, 0.46993, 0.64465, 0.42631, 0.71053, 0.38083, 0.72956, 0.37991, 0.81447, 0.3697, 0.90816, 0.31308, 0.95647, 0.23791, 1, 0.19893, 0.90816, 0.16366, 0.97403, 0.08941, 0.96964, 0.01795, 0.93012, 0, 0.81739, 0.03929, 0.69003, 0.11354, 0.67686, 0.14324, 0.58024, 0.17758, 0.50412, 0.26575, 0.44849, 0.32979, 0.4075, 0.39383, 0.35626, 0.44023, 0.39432, 0.46884, 0.50986, 0.89059, 0.15133, 0.94495, 0.10607, 0.90667, 0.05653, 0.95228, 0.19238, 0.37953, 0.56819, 0.30682, 0.62966, 0.23349, 0.69902, 0.15742, 0.77968, 0.08267, 0.85019, 0.35998, 0.47097, 0.28017, 0.53487, 0.206, 0.59301, 0.15563, 0.67861, 0.103, 0.75303, 0.06046, 0.77101, 0.12106, 0.90984, 0.18446, 0.84412, 0.27352, 0.84626, 0.32015, 0.74391, 0.35932, 0.67658, 0.41866, 0.63007, 0.24906, 0.75703], "triangles": [48, 52, 49, 48, 49, 13, 47, 52, 48, 17, 47, 48, 14, 48, 13, 15, 48, 14, 16, 17, 48, 16, 48, 15, 44, 22, 23, 44, 23, 43, 45, 22, 44, 44, 43, 38, 21, 22, 45, 47, 38, 52, 39, 45, 44, 21, 45, 39, 39, 44, 38, 46, 39, 38, 46, 38, 47, 46, 47, 17, 20, 21, 39, 19, 39, 46, 20, 39, 19, 18, 46, 17, 19, 46, 18, 25, 26, 41, 42, 25, 41, 24, 25, 42, 43, 24, 42, 23, 24, 43, 43, 42, 37, 38, 43, 37, 11, 51, 10, 50, 51, 11, 12, 50, 11, 49, 50, 12, 13, 49, 12, 33, 3, 4, 2, 3, 33, 32, 33, 4, 1, 2, 33, 32, 4, 5, 31, 1, 33, 31, 33, 32, 34, 32, 5, 31, 32, 34, 8, 31, 34, 7, 8, 34, 6, 34, 5, 7, 34, 6, 40, 27, 28, 40, 28, 29, 30, 29, 0, 40, 29, 30, 30, 0, 1, 30, 1, 31, 41, 26, 27, 41, 27, 40, 35, 40, 30, 41, 40, 35, 8, 9, 30, 8, 30, 31, 35, 30, 9, 36, 41, 35, 42, 41, 36, 51, 35, 9, 10, 51, 9, 50, 36, 35, 50, 35, 51, 37, 42, 36, 49, 36, 50, 37, 36, 49, 52, 37, 49, 38, 37, 52], "vertices": [2, 141, 15.73, -6.17, 0.99839, 157, -42.46, 5.19, 0.00161, 1, 141, -49.74, -4.77, 1, 1, 141, -55.84, -11.46, 1, 1, 141, -60.28, -12.49, 1, 1, 141, -69.23, -6.06, 1, 1, 141, -69.15, 6.53, 1, 1, 141, -61.08, 16.19, 1, 1, 141, -53.93, 12.1, 1, 1, 141, -50.12, 6.94, 1, 2, 141, 22.42, 6.03, 0.91012, 158, -17.6, -1.79, 0.08988, 2, 141, 26.06, 11.48, 0.60446, 158, -13.96, 3.67, 0.39554, 2, 141, 34.98, 13.88, 0.12401, 158, -5.04, 6.06, 0.87599, 3, 141, 41.97, 12.15, 0.00065, 158, 1.95, 4.34, 0.88837, 155, -20.5, -6.38, 0.11098, 3, 141, 46.06, 19.3, 0.01844, 158, 6.03, 11.49, 0.46396, 155, -16.42, 0.78, 0.5176, 2, 158, 11.77, 18.71, 0.21251, 155, -10.68, 7.99, 0.78749, 2, 158, 21.62, 18.65, 0.03536, 155, -0.83, 7.94, 0.96464, 3, 157, 15.57, 36, 0.00043, 156, -10.61, 32.24, 0.04425, 155, 11.28, 6.11, 0.95531, 4, 141, 74.71, 13.96, 0.0379, 157, 16.52, 25.32, 0.02979, 156, -9.66, 21.56, 0.34218, 155, 12.23, -4.57, 0.59013, 4, 141, 82.51, 16.97, 0.01327, 157, 24.32, 28.33, 0.01085, 156, -1.86, 24.57, 0.65511, 155, 20.03, -1.56, 0.32077, 4, 141, 92.27, 11.13, 0.00131, 157, 34.08, 22.49, 0.00019, 156, 7.9, 18.73, 0.85041, 155, 29.79, -7.39, 0.14809, 2, 156, 15.64, 10.11, 0.95907, 155, 37.53, -16.01, 0.04093, 2, 156, 12.79, -0.8, 0.99623, 155, 34.68, -26.92, 0.00377, 2, 157, 27.76, -4.98, 0.00311, 156, 1.58, -8.74, 0.99689, 4, 141, 75.37, -12, 0.00856, 157, 17.18, -0.64, 0.34282, 156, -9, -4.4, 0.6244, 155, 12.9, -30.52, 0.02421, 3, 157, 8.69, -6.67, 0.872, 156, -17.49, -10.43, 0.12485, 155, 4.41, -36.55, 0.00315, 3, 141, 58.73, -21.98, 0.01112, 157, 0.54, -10.62, 0.98249, 156, -25.64, -14.38, 0.00639, 2, 141, 44.3, -20.22, 0.49336, 157, -13.89, -8.86, 0.50664, 2, 141, 33.8, -19, 0.83147, 157, -24.39, -7.64, 0.16853, 2, 141, 22.82, -18.64, 0.95564, 157, -35.37, -7.29, 0.04436, 2, 141, 18.37, -11.99, 0.98431, 157, -39.82, -0.63, 0.01569, 1, 141, 19.92, -0.06, 1, 1, 141, -53.38, 0.47, 1, 1, 141, -62.78, 0.62, 1, 1, 141, -59.96, -6.41, 1, 1, 141, -59.74, 8.5, 1, 2, 141, 34.62, -1.67, 0.99256, 157, -23.57, 9.69, 0.00744, 2, 141, 47.24, -1.79, 0.96255, 157, -10.95, 9.57, 0.03745, 5, 141, 60.32, -1.29, 0.4471, 158, 20.29, -9.1, 0.00187, 157, 2.13, 10.07, 0.34469, 156, -24.05, 6.31, 0.0881, 155, -2.16, -19.81, 0.11825, 4, 141, 74.28, -0.02, 0.07267, 157, 16.09, 11.34, 0.188, 156, -10.09, 7.58, 0.54529, 155, 11.81, -18.55, 0.19404, 4, 141, 87.6, 0.48, 0.00331, 157, 29.41, 11.83, 0.00494, 156, 3.23, 8.07, 0.91633, 155, 25.12, -18.05, 0.07543, 2, 141, 32.71, -11.38, 0.90364, 157, -25.48, -0.02, 0.09636, 2, 141, 46.4, -11.81, 0.5678, 157, -11.79, -0.45, 0.4322, 3, 157, 0.87, -0.97, 0.99126, 156, -25.31, -4.73, 0.00861, 155, -3.42, -30.85, 0.00013, 4, 141, 69.81, -8.75, 0.03795, 157, 11.62, 2.61, 0.55273, 156, -14.56, -1.15, 0.36032, 155, 7.33, -27.28, 0.04901, 4, 141, 80.34, -6.29, 0.00941, 157, 22.15, 5.07, 0.08991, 156, -4.03, 1.31, 0.86553, 155, 17.86, -24.82, 0.03515, 1, 156, 2.52, -0.29, 1, 4, 141, 85.23, 8.37, 0.00917, 157, 27.04, 19.73, 0.0101, 156, 0.86, 15.97, 0.7717, 155, 22.75, -10.15, 0.20903, 4, 141, 73.66, 7.45, 0.07875, 157, 15.47, 18.81, 0.08214, 156, -10.71, 15.05, 0.42579, 155, 11.18, -11.08, 0.41331, 5, 141, 61.81, 14.18, 0.11306, 158, 21.79, 6.37, 0.03466, 157, 3.62, 25.54, 0.01347, 156, -22.56, 21.78, 0.03407, 155, -0.66, -4.35, 0.80474, 4, 141, 50.78, 8.91, 0.40998, 158, 10.76, 1.09, 0.33248, 156, -33.59, 16.5, 0.00165, 155, -11.69, -9.62, 0.25589, 3, 141, 42.39, 6.06, 0.3027, 158, 2.36, -1.75, 0.6765, 155, -20.09, -12.46, 0.02079, 2, 141, 32.26, 6.47, 0.55932, 158, -7.77, -1.34, 0.44068, 5, 141, 60.93, 4.79, 0.43567, 158, 20.91, -3.02, 0.0254, 157, 2.74, 16.15, 0.1191, 156, -23.44, 12.39, 0.09283, 155, -1.55, -13.73, 0.327], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 0, 60, 62, 60, 60, 18, 60, 70, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102], "width": 153, "height": 97}}, "hwc05": {"hwc05": {"type": "mesh", "uvs": [0.12099, 0, 0.30734, 0.00069, 0.74411, 0.35249, 0.86349, 0.65302, 1, 0.87047, 0.62472, 1, 0.15884, 1, 0.13264, 0.78384, 0.08896, 0.45149, 0.00452, 0.09261, 0.41974, 0.41199, 0.55051, 0.73604], "triangles": [6, 11, 5, 5, 11, 4, 6, 7, 11, 11, 3, 4, 11, 8, 10, 11, 7, 8, 11, 10, 3, 10, 2, 3, 10, 9, 0, 10, 8, 9, 0, 1, 10, 10, 1, 2], "vertices": [2, 139, -4.63, -0.96, 0.92084, 140, -25.31, -5.33, 0.07916, 2, 139, -1.87, 4.74, 0.82716, 140, -23.55, 0.76, 0.17284, 2, 139, 22.31, 9.64, 0.49382, 140, -0.55, 9.69, 0.50618, 2, 139, 39.25, 6.05, 0.16049, 140, 16.75, 9.02, 0.83951, 1, 140, 29.73, 10.17, 1, 1, 140, 33.24, -4.08, 1, 2, 139, 46.44, -23.95, 0.00738, 140, 28.93, -19.32, 0.99262, 2, 139, 35.14, -19.53, 0.21966, 140, 17.04, -16.89, 0.78034, 2, 139, 17.71, -12.84, 0.55299, 140, -1.28, -13.25, 0.44701, 2, 139, -1.66, -6.77, 0.87895, 140, -21.39, -10.55, 0.12105, 2, 139, 20.56, -1.74, 0.56132, 140, -0.34, -1.83, 0.43868, 2, 139, 38.85, -5.56, 0.34199, 140, 18.33, -2.49, 0.65801], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18, 0, 20, 20, 22], "width": 34, "height": 56}}, "hwc06": {"hwc06": {"type": "mesh", "uvs": [0, 0.08939, 0.06537, 0.01351, 0.30462, 0.02172, 0.69237, 0.26166, 0.90412, 0.69849, 1, 0.93639, 0.70612, 1, 0.26887, 0.98561, 0.04337, 0.8441, 0.16437, 0.7067, 0.18087, 0.39702, 0.4268, 0.33651, 0.52652, 0.79629], "triangles": [7, 12, 6, 5, 6, 4, 8, 9, 7, 7, 9, 12, 6, 12, 4, 12, 9, 11, 9, 10, 11, 12, 11, 4, 11, 3, 4, 10, 0, 11, 11, 0, 1, 1, 2, 11, 11, 2, 3], "vertices": [2, 137, -6.9, -6.92, 0.90941, 138, -22.72, -20.28, 0.09059, 1, 137, -8.96, -2.01, 1, 2, 137, -2.59, 6.38, 0.94097, 138, -25.31, -6.54, 0.05903, 2, 137, 18.74, 12.41, 0.60764, 138, -9.47, 8.97, 0.39236, 2, 137, 45.26, 5.47, 0.27431, 138, 17.13, 15.58, 0.72569, 1, 138, 31.52, 18.33, 1, 1, 138, 33.92, 5.08, 1, 1, 138, 31.1, -13.97, 1, 2, 137, 30.86, -30.6, 0.0022, 138, 21.77, -22.97, 0.9978, 2, 137, 27.2, -21.61, 0.24494, 138, 14.26, -16.84, 0.75506, 2, 137, 12.56, -10.66, 0.57828, 138, -3.84, -14.24, 0.42172, 2, 137, 15.76, 0.28, 0.66667, 138, -6.28, -3.11, 0.33333, 2, 137, 40.59, -11.48, 0.5, 138, 21.16, -1.54, 0.5], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 2, 22, 22, 24], "width": 44, "height": 59}}, "hwc07": {"hwc07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 152, -4.78, -12.59, 1, 2, 134, 3.99, 26.19, 0.2799, 152, -4.04, 31.4, 0.7201, 2, 134, 17.99, 25.95, 0.99991, 152, 9.96, 31.17, 9e-05, 1, 134, 17.25, -18.04, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 14}}, "hwc08": {"hwc08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.25, -17.91, 10.1, 33.08, 59.1, 32.26, 58.24, -18.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 51, "height": 49}}, "hwc09": {"hwc09": {"type": "mesh", "uvs": [0.68989, 0.55393, 0.81879, 0.43103, 0.96286, 0.31459, 1, 0.15934, 0.89462, 0.01919, 0.70657, 0, 0.42451, 0.00194, 0.16216, 0.13347, 0, 0.36203, 0, 0.64018, 0.13941, 0.84718, 0.30774, 1, 0.47758, 0.97224, 0.53673, 0.89031, 0.41692, 0.80621, 0.43512, 0.61862, 0.57161, 0.53453, 0.65956, 0.41809, 0.86111, 0.21735, 0.69395, 0.16388, 0.50367, 0.20521, 0.27566, 0.33709, 0.19183, 0.63104, 0.27183, 0.83372, 0.37783, 0.91164, 0.35678, 0.49837, 0.52987, 0.37009, 0.67792, 0.31139, 0.34236, 0.64583, 0.14, 0.34953, 0.0802, 0.62451], "triangles": [14, 28, 15, 23, 22, 28, 23, 28, 14, 10, 22, 23, 24, 23, 14, 24, 14, 13, 12, 24, 13, 11, 23, 24, 10, 23, 11, 11, 24, 12, 21, 7, 6, 29, 7, 21, 29, 8, 7, 25, 21, 26, 15, 25, 26, 30, 8, 29, 22, 29, 21, 22, 21, 25, 30, 29, 22, 9, 8, 30, 28, 22, 25, 28, 25, 15, 10, 30, 22, 9, 30, 10, 18, 5, 4, 18, 4, 3, 19, 5, 18, 27, 19, 18, 2, 18, 3, 1, 27, 18, 1, 18, 2, 17, 27, 1, 0, 17, 1, 5, 20, 6, 19, 20, 5, 27, 20, 19, 21, 6, 20, 26, 20, 27, 21, 20, 26, 17, 26, 27, 16, 26, 17, 16, 15, 26], "vertices": [4, 145, -23.8, -15.92, 0.24871, 142, 4.75, -26.32, 0.74877, 143, 1.04, 41.21, 0.00092, 144, 22.65, 37.9, 0.0016, 3, 145, -16.13, -27.78, 0.09753, 142, 11.57, -13.96, 0.90243, 144, 23.74, 51.98, 4e-05, 2, 145, -8.9, -41.02, 3e-05, 142, 19.81, -1.33, 0.99997, 1, 142, 18.26, 9.06, 1, 1, 142, 5.63, 12.64, 1, 2, 145, 11.62, -18.04, 0.24461, 142, -10.14, 5.89, 0.75539, 3, 145, 11.93, 7.63, 0.86695, 142, -32.9, -5.98, 0.00194, 143, -27.61, 9.43, 0.1311, 2, 145, 3.91, 31.64, 0.11644, 143, -13.81, -11.8, 0.88356, 1, 143, 3.88, -22.7, 1, 2, 143, 21.18, -18.51, 0.84753, 144, -11.91, -14.8, 0.15247, 2, 143, 31.07, -3.06, 0.07825, 144, 6.35, -13.05, 0.92175, 1, 144, 23.51, -7.08, 1, 1, 144, 31.7, 6.15, 1, 1, 144, 30.91, 13.62, 1, 3, 145, -39.53, 9.18, 0.01137, 143, 22.58, 20.87, 0.02956, 144, 19.94, 8.4, 0.95907, 4, 145, -27.55, 7.33, 0.22008, 142, -13.96, -40.62, 0.00779, 143, 10.52, 19.65, 0.35181, 144, 11.54, 17.14, 0.42032, 4, 145, -22.38, -5.18, 0.57689, 142, -5.39, -30.15, 0.12632, 143, 2.37, 30.46, 0.20808, 144, 15.01, 30.22, 0.08871, 4, 145, -15.06, -13.31, 0.41102, 142, -1.69, -19.86, 0.54448, 143, -6.76, 36.48, 0.03153, 144, 14.12, 41.12, 0.01297, 2, 145, -2.53, -31.86, 5e-05, 142, 8.73, -0.04, 0.99995, 3, 145, 1.15, -16.71, 0.28403, 142, -6.36, -3.96, 0.71586, 144, 3.29, 53.66, 0.00011, 3, 145, -1.2, 0.64, 0.97512, 143, -16.66, 19.49, 0.02316, 144, -5.37, 38.43, 0.00173, 3, 145, -9.29, 21.53, 0.09445, 143, -3.57, 1.31, 0.90369, 144, -11.6, 16.91, 0.00186, 2, 143, 16.51, -1.68, 0.79904, 144, -1.56, -0.73, 0.20096, 1, 144, 13.14, -3.06, 1, 3, 145, -46.21, 12.85, 0.00013, 143, 29.98, 19, 9e-05, 144, 23.03, 1.43, 0.99978, 4, 145, -19.74, 14.33, 0.17943, 142, -23.83, -37.05, 0.00111, 143, 4.72, 10.91, 0.64297, 144, 1.08, 16.31, 0.17649, 4, 145, -11.79, -1.56, 0.69443, 142, -13.59, -22.53, 0.10733, 143, -6.97, 24.29, 0.15663, 144, 4.39, 33.76, 0.04162, 4, 145, -8.26, -15.1, 0.37513, 142, -3.33, -13.02, 0.61126, 143, -13.79, 36.5, 0.00898, 144, 9.79, 46.66, 0.00464, 4, 145, -29.15, 15.8, 0.06248, 142, -20.67, -46.04, 8e-05, 143, 14.2, 11.86, 0.31443, 144, 7.67, 9.43, 0.62301, 2, 145, -9.88, 33.89, 0.00295, 143, 0.1, -10.5, 0.99705, 2, 143, 18.49, -11.65, 0.80719, 144, -8.18, -8.45, 0.19281], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 26], "width": 91, "height": 64}}, "bwc08": {"bwc08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [10.42, -34.74, 6.06, 19.08, 49.92, 22.64, 54.28, -31.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 54, "height": 44}}, "a10": {"a10": {"type": "mesh", "uvs": [0.74549, 0.00449, 0.8764, 0.00238, 0.96585, 0.12924, 0.99066, 0.19068, 0.99049, 0.26997, 0.98079, 0.30235, 0.9576, 0.32087, 0.90142, 0.2789, 0.80806, 0.41546, 0.70635, 0.58232, 0.59234, 0.75286, 0.45606, 0.88326, 0.31751, 0.98441, 0.12161, 0.98051, 0, 0.75244, 0, 0.49165, 0.04818, 0.33196, 0.07768, 0.26768, 0.09686, 0.26789, 0.14417, 0.4438, 0.19514, 0.52357, 0.3091, 0.51532, 0.41709, 0.4583, 0.49375, 0.33573, 0.60248, 0.15696], "triangles": [22, 23, 10, 11, 22, 10, 21, 22, 11, 12, 21, 11, 10, 23, 9, 20, 21, 12, 3, 7, 2, 7, 1, 2, 4, 7, 3, 4, 6, 7, 5, 6, 4, 7, 8, 0, 7, 0, 1, 24, 0, 8, 9, 24, 8, 23, 24, 9, 15, 20, 14, 16, 17, 18, 16, 18, 19, 19, 15, 16, 20, 13, 14, 19, 20, 15, 13, 20, 12], "vertices": [3, 14, 55.63, 4.42, 0.66281, 32, -37.33, 40.23, 0.14519, 39, 52.96, 15.45, 0.192, 3, 14, 58.93, -8.79, 0.78064, 32, -37.67, 53.85, 0.05136, 39, 56.27, 2.24, 0.168, 3, 14, 53.33, -19.7, 0.82173, 32, -29.81, 63.26, 0.01027, 39, 50.67, -8.67, 0.168, 2, 14, 50.16, -23.11, 0.832, 39, 47.5, -12.08, 0.168, 2, 14, 45.3, -24.26, 0.832, 39, 42.64, -13.23, 0.168, 2, 14, 43.08, -23.75, 0.832, 39, 40.42, -12.72, 0.168, 3, 14, 41.39, -21.68, 0.82173, 32, -17.73, 62.58, 0.01027, 39, 38.73, -10.65, 0.168, 3, 14, 42.6, -15.38, 0.78746, 32, -20.29, 56.7, 0.05254, 39, 39.93, -4.35, 0.16, 3, 14, 31.97, -7.94, 0.68642, 32, -11.54, 47.12, 0.15625, 39, 29.3, 3.09, 0.15733, 3, 14, 19.28, -0.11, 0.53343, 32, -0.87, 36.7, 0.3119, 39, 16.62, 10.92, 0.15467, 3, 14, 6.06, 8.92, 0.37333, 32, 10.04, 25, 0.46667, 39, 3.4, 19.95, 0.16, 3, 14, -5.23, 20.78, 0.31111, 32, 18.47, 10.95, 0.52889, 39, -7.89, 31.81, 0.16, 3, 14, -14.79, 33.31, 0.37333, 32, 25.05, -3.36, 0.46667, 39, -17.45, 44.34, 0.16, 3, 14, -19.3, 53.18, 0.52889, 32, 25.1, -23.74, 0.31111, 39, -21.96, 64.21, 0.16, 3, 14, -8.27, 68.83, 0.68444, 32, 10.92, -36.6, 0.15556, 39, -10.93, 79.86, 0.16, 3, 14, 7.7, 72.66, 0.78815, 32, -5.51, -36.84, 0.05185, 39, 5.04, 83.69, 0.16, 3, 14, 18.66, 70.13, 0.82963, 32, -15.64, -31.98, 0.01037, 39, 15.99, 81.16, 0.16, 3, 14, 23.31, 68.09, 0.82963, 32, -19.73, -28.97, 0.01037, 39, 20.65, 79.12, 0.16, 3, 14, 23.76, 66.15, 0.78815, 32, -19.75, -26.97, 0.05185, 39, 21.1, 77.18, 0.16, 3, 14, 14.13, 58.78, 0.68444, 32, -8.74, -21.89, 0.15556, 39, 11.47, 69.81, 0.16, 3, 14, 10.48, 52.45, 0.52889, 32, -3.79, -16.52, 0.31111, 39, 7.82, 63.49, 0.16, 3, 14, 13.75, 41.05, 0.37333, 32, -4.49, -4.67, 0.46667, 39, 11.09, 52.08, 0.16, 3, 14, 19.86, 30.97, 0.31111, 32, -8.24, 6.51, 0.52889, 39, 17.2, 42, 0.16, 3, 14, 29.23, 25.01, 0.35654, 32, -16.08, 14.36, 0.45679, 39, 26.57, 36.05, 0.18667, 3, 14, 42.82, 16.64, 0.50983, 32, -27.51, 25.5, 0.30084, 39, 40.16, 27.67, 0.18933], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 20, 22, 22, 24, 14, 16, 16, 18, 44, 46, 46, 48, 40, 42, 42, 44], "width": 41, "height": 25}}, "bwc05": {"bwc05": {"type": "mesh", "uvs": [0.45787, 0.0028, 0.28543, 0.08001, 0.25309, 0.29182, 0.08783, 0.49176, 0.04832, 0.7016, 0, 0.85601, 0.23513, 0.95894, 0.74887, 0.97478, 1, 0.85403, 1, 0.69368, 0.97161, 0.45019, 0.93568, 0.2364, 0.68779, 0.02656, 0.59057, 0.2887, 0.59258, 0.49107, 0.54554, 0.75694], "triangles": [6, 15, 7, 7, 15, 8, 15, 6, 4, 6, 5, 4, 15, 9, 8, 4, 3, 15, 15, 14, 9, 15, 3, 14, 14, 10, 9, 3, 2, 14, 2, 13, 14, 14, 13, 10, 13, 11, 10, 2, 1, 13, 1, 0, 13, 13, 12, 11, 13, 0, 12], "vertices": [1, 114, -5.98, 0.9, 1, 2, 114, -3.25, -4.44, 0.99455, 115, -18.71, -8.36, 0.00545, 2, 114, 6.72, -7.45, 0.84744, 115, -8.34, -9.31, 0.15256, 2, 114, 15.38, -13.86, 0.55692, 115, 1.42, -13.85, 0.44308, 2, 114, 25.21, -17.04, 0.23874, 115, 11.7, -15, 0.76126, 2, 114, 32.34, -19.89, 0.05372, 115, 19.25, -16.37, 0.94628, 2, 114, 38.59, -14.73, 0.0109, 115, 24.35, -10.06, 0.9891, 2, 114, 42.24, -1.33, 0.00119, 115, 25.23, 3.8, 0.99881, 2, 114, 37.86, 6.54, 0.0048, 115, 19.37, 10.63, 0.9952, 2, 114, 30.18, 8.17, 0.2081, 115, 11.52, 10.7, 0.7919, 2, 114, 18.35, 9.91, 0.54125, 115, -0.42, 10.02, 0.45875, 2, 114, 7.9, 11.14, 0.86978, 115, -10.9, 9.14, 0.13022, 2, 114, -3.55, 6.73, 0.99982, 115, -21.24, 2.53, 0.00018, 2, 114, 8.47, 1.49, 0.66983, 115, -8.42, -0.2, 0.33017, 2, 114, 18.18, -0.52, 0.33752, 115, 1.5, -0.23, 0.66248, 2, 114, 30.66, -4.47, 0.00628, 115, 14.52, -1.6, 0.99372], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 0, 26, 26, 28, 28, 30], "width": 27, "height": 49}}, "niutou011": {"niutou011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-24.15, -32.84, -16.93, 71.91, 92.81, 64.35, 85.59, -40.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 105, "height": 110}}, "niutou04": {"niutou04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [29.48, 19.21, 27.02, -24.72, -10.92, -22.6, -8.46, 21.33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 38}}, "a9": {"a9": {"type": "mesh", "uvs": [0.97506, 0.03233, 0.99574, 0.11578, 0.99291, 0.20068, 0.96447, 0.27327, 0.93749, 0.32586, 0.88711, 0.38698, 0.83017, 0.43782, 0.74766, 0.48857, 0.63873, 0.53644, 0.51928, 0.57602, 0.40299, 0.61739, 0.31142, 0.65943, 0.22549, 0.71223, 0.16402, 0.76368, 0.14264, 0.82476, 0.14973, 0.86666, 0.25775, 0.88455, 0.28901, 0.94726, 0.23396, 0.9852, 0.15361, 1, 0.0464, 0.99181, 1e-05, 0.9524, 1e-05, 0.91094, 0.05252, 0.87345, 0.04615, 0.81505, 0.08226, 0.74792, 0.1548, 0.68725, 0.24378, 0.63285, 0.34504, 0.58412, 0.46126, 0.53961, 0.5615, 0.50025, 0.65241, 0.46032, 0.74332, 0.41154, 0.8116, 0.35776, 0.85499, 0.29643, 0.88254, 0.20639, 0.87155, 0.12361, 0.86023, 0.03256, 0.87433, 0.006, 0.90889, 0.00097], "triangles": [36, 0, 1, 39, 0, 37, 37, 38, 39, 0, 36, 37, 14, 24, 13, 23, 24, 14, 23, 14, 15, 18, 19, 15, 15, 19, 20, 23, 15, 22, 15, 20, 21, 22, 15, 21, 17, 15, 16, 17, 18, 15, 24, 25, 13, 13, 25, 12, 25, 26, 12, 12, 26, 11, 26, 27, 11, 11, 27, 10, 27, 28, 10, 10, 28, 9, 28, 29, 9, 9, 30, 8, 9, 29, 30, 8, 31, 7, 8, 30, 31, 34, 35, 3, 31, 32, 7, 7, 32, 6, 6, 32, 5, 32, 33, 5, 5, 33, 4, 33, 34, 4, 4, 34, 3, 3, 35, 2, 35, 36, 2, 2, 36, 1], "vertices": [4, 26, -0.69, 6.58, 0.30284, 27, -56.01, -21.57, 0.03968, 14, 81.63, 72.82, 0.47348, 39, 78.97, 83.85, 0.184, 4, 26, 20.07, 9.14, 0.3874, 27, -38.74, -9.76, 0.10959, 14, 61.99, 65.62, 0.31901, 39, 59.32, 76.65, 0.184, 5, 26, 41.21, 8.94, 0.41488, 27, -19.88, -0.22, 0.22917, 28, -72.49, -29.82, 0.0007, 14, 41.35, 61.01, 0.17126, 39, 38.69, 72.04, 0.184, 5, 26, 59.31, 5.73, 0.35669, 27, -2.33, 5.25, 0.38194, 28, -58.3, -18.13, 0.00685, 14, 23, 60.03, 0.07052, 39, 20.34, 71.06, 0.184, 5, 26, 72.43, 2.66, 0.24362, 27, 10.73, 8.54, 0.52162, 28, -47.46, -10.14, 0.03061, 14, 9.53, 60.05, 0.02015, 39, 6.87, 71.08, 0.184, 5, 26, 87.68, -3.14, 0.12883, 27, 26.95, 10.41, 0.59236, 28, -33.15, -2.29, 0.09145, 14, -6.65, 62.23, 0.00336, 39, -9.31, 73.26, 0.184, 5, 26, 100.38, -9.72, 0.05098, 27, 41.25, 10.4, 0.56236, 28, -19.9, 3.11, 0.20062, 29, -75.88, 31.61, 0.00204, 39, -23.17, 76.79, 0.184, 5, 26, 113.08, -19.29, 0.01364, 27, 56.93, 7.73, 0.44441, 28, -4.38, 6.56, 0.34442, 29, -60.13, 29.43, 0.01354, 39, -37.71, 83.23, 0.184, 6, 26, 125.08, -31.96, 0.00206, 27, 73.41, 2, 0.28891, 28, 13.05, 7.48, 0.47434, 29, -43.48, 24.2, 0.05068, 30, -59.62, 63.17, 1e-05, 39, -52.27, 92.85, 0.184, 5, 27, 88.63, -5.79, 0.15033, 28, 30.09, 6.02, 0.53407, 29, -28.02, 16.88, 0.12818, 30, -50.01, 49.03, 0.00342, 39, -65.11, 104.14, 0.184, 6, 27, 104.08, -13.04, 0.06005, 28, 47.13, 5.15, 0.49306, 29, -12.36, 10.11, 0.24255, 30, -39.95, 35.24, 0.0181, 31, -57.39, 41.6, 0.00223, 39, -78.3, 114.97, 0.184, 6, 27, 118.33, -17.66, 0.01666, 28, 62.07, 6.26, 0.37313, 29, 2.02, 5.93, 0.35532, 30, -29.67, 24.34, 0.05638, 31, -48.34, 29.67, 0.01452, 39, -90.98, 122.95, 0.184, 6, 27, 134.65, -20.44, 0.00266, 28, 78.22, 9.85, 0.22808, 29, 18.42, 3.65, 0.41344, 30, -16.71, 14.06, 0.1182, 31, -36.54, 18.06, 0.05362, 39, -106.11, 129.66, 0.184, 5, 28, 91.97, 15.02, 0.13874, 29, 33.11, 3.69, 0.22282, 30, -4.02, 6.65, 0.23363, 31, -24.72, 9.34, 0.17311, 39, -120.24, 133.67, 0.2317, 5, 28, 103.68, 25.05, 0.04799, 29, 47.58, 8.99, 0.13783, 30, 11.14, 3.88, 0.26711, 31, -9.94, 4.97, 0.32617, 39, -135.62, 132.55, 0.2209, 4, 28, 109.76, 33.57, 0.01188, 30, 21.59, 4.53, 0.24899, 31, 0.52, 4.5, 0.51984, 39, -145.57, 129.31, 0.21929, 4, 28, 102.96, 45.11, 0.00142, 30, 26.26, 17.08, 0.16285, 31, 6.51, 16.48, 0.63809, 39, -146.96, 115.98, 0.19764, 3, 30, 41.94, 20.47, 0.08595, 31, 22.45, 18.18, 0.72628, 39, -161.29, 108.79, 0.18777, 3, 30, 51.27, 13.86, 0.03497, 31, 31.03, 10.61, 0.78041, 39, -171.98, 112.85, 0.18462, 3, 30, 54.79, 4.4, 0.01812, 31, 33.52, 0.83, 0.79787, 39, -177.75, 121.13, 0.184, 3, 30, 52.53, -8.11, 0.03537, 31, 29.94, -11.37, 0.78001, 39, -178.69, 133.8, 0.18462, 3, 30, 42.62, -13.36, 0.08678, 31, 19.53, -15.53, 0.72544, 39, -170.41, 141.37, 0.18778, 4, 28, 130.26, 30.73, 0.00142, 30, 32.3, -13.18, 0.16409, 31, 9.29, -14.25, 0.63681, 39, -160.38, 143.78, 0.19768, 4, 28, 119.55, 27.54, 0.0119, 30, 23.08, -6.88, 0.25042, 31, 0.79, -7, 0.51829, 39, -149.87, 139.98, 0.21938, 5, 28, 110.76, 15.93, 0.05385, 29, 51.03, -2.02, 0.03435, 30, 8.52, -7.37, 0.3004, 31, -13.74, -5.94, 0.36381, 39, -135.9, 144.09, 0.24759, 5, 28, 96.77, 5.86, 0.13601, 29, 34.4, -6.57, 0.23937, 30, -8.11, -2.85, 0.22916, 31, -29.8, 0.33, 0.1685, 39, -118.66, 143.88, 0.22697, 6, 27, 132.99, -30.66, 0.00227, 28, 80.55, -0.24, 0.2286, 29, 17.07, -6.62, 0.41387, 30, -23.07, 5.9, 0.11807, 31, -43.74, 10.63, 0.05319, 39, -101.99, 139.15, 0.184, 6, 27, 116.15, -27.74, 0.01471, 28, 63.86, -3.9, 0.37522, 29, 0.16, -4.22, 0.35556, 30, -36.43, 16.55, 0.05615, 31, -55.89, 22.64, 0.01437, 39, -86.39, 132.19, 0.184, 6, 27, 99.9, -22.9, 0.0542, 28, 46.98, -5.56, 0.49904, 29, -16.24, 0.13, 0.24261, 30, -48.36, 28.61, 0.01795, 31, -66.46, 35.9, 0.0022, 39, -71.83, 123.49, 0.184, 5, 27, 83.77, -16.02, 0.13862, 28, 29.44, -5.28, 0.54586, 29, -32.58, 6.51, 0.12816, 30, -59.2, 42.4, 0.00336, 39, -57.88, 112.86, 0.184, 5, 26, 116.13, -41.05, 0.00336, 27, 69.63, -10.2, 0.27005, 28, 14.16, -5.23, 0.49194, 29, -46.88, 11.9, 0.05065, 39, -45.62, 103.74, 0.184, 5, 26, 106.12, -30.48, 0.02015, 27, 55.88, -5.4, 0.41799, 28, -0.38, -5.99, 0.36434, 29, -60.77, 16.27, 0.01353, 39, -33.47, 95.71, 0.184, 6, 26, 93.91, -19.92, 0.06716, 27, 40.19, -1.64, 0.52526, 28, -16.34, -8.44, 0.21819, 29, -76.58, 19.55, 0.00203, 14, -16.52, 77.17, 0.00336, 39, -19.18, 88.2, 0.184, 5, 26, 80.46, -12.02, 0.15111, 27, 24.61, -0.8, 0.54158, 28, -31.07, -13.55, 0.10316, 14, -1.63, 72.52, 0.02015, 39, -4.29, 83.55, 0.184, 5, 26, 65.16, -7.04, 0.25185, 27, 8.73, -3.41, 0.45716, 28, -44.79, -21.96, 0.03647, 14, 14.4, 71.14, 0.07052, 39, 11.74, 82.18, 0.184, 5, 26, 42.72, -3.96, 0.32237, 27, -12.61, -10.99, 0.31357, 28, -61.69, -37.04, 0.0088, 14, 36.96, 73.24, 0.17126, 39, 34.3, 84.27, 0.184, 5, 26, 22.12, -5.38, 0.32573, 27, -30.26, -21.71, 0.17017, 28, -73.97, -53.64, 0.00109, 14, 56.7, 79.29, 0.31901, 39, 54.04, 90.33, 0.184, 4, 26, -0.55, -6.85, 0.272, 27, -49.71, -33.43, 0.07052, 14, 78.44, 85.87, 0.47348, 39, 75.78, 96.9, 0.184, 4, 26, -7.17, -5.24, 0.22033, 27, -56.33, -35.05, 0.02145, 14, 85.26, 85.81, 0.57422, 39, 82.59, 96.84, 0.184, 4, 26, -8.45, -1.21, 0.22855, 27, -59.32, -32.05, 0.01323, 14, 87.42, 82.17, 0.57422, 39, 84.76, 93.2, 0.184], "hull": 40, "edges": [0, 78, 0, 2, 2, 4, 20, 22, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 50, 52, 62, 64, 68, 70, 74, 76, 76, 78, 70, 72, 72, 74, 60, 62, 56, 58, 58, 60, 48, 50, 44, 46, 46, 48, 16, 18, 18, 20, 8, 10, 10, 12, 4, 6, 6, 8, 52, 54, 54, 56, 22, 24, 24, 26, 12, 14, 14, 16, 28, 30, 30, 32, 64, 66, 66, 68], "width": 46, "height": 99}}, "a15": {"a15": {"type": "mesh", "uvs": [0.20344, 0.00654, 0.20384, 0.05681, 0.25389, 0.16741, 0.35048, 0.26291, 0.41789, 0.18452, 0.49937, 0.14241, 0.57249, 0.13926, 0.62079, 0.15648, 0.67329, 0.2114, 0.70185, 0.2636, 0.73307, 0.37195, 0.91252, 0.37809, 0.9504, 0.31387, 0.9813, 0.31239, 0.98123, 0.39014, 0.95661, 0.45424, 0.90543, 0.50036, 0.78656, 0.45506, 0.77116, 0.49832, 0.90474, 0.65486, 0.94074, 0.63444, 0.97753, 0.65299, 0.99999, 0.68299, 0.92999, 0.77647, 0.88524, 0.75546, 0.85431, 0.72257, 0.82214, 0.79431, 0.74619, 0.85919, 0.69513, 0.83082, 0.68237, 0.89063, 0.63111, 0.95224, 0.58111, 1, 0.55669, 0.99566, 0.49636, 0.92581, 0.45392, 0.82924, 0.39776, 0.8564, 0.31885, 0.85312, 0.2293, 0.76587, 0.17608, 0.6498, 0.14461, 0.51734, 0.10377, 0.53541, 0.0472, 0.45114, 0.00321, 0.32584, 0.00457, 0.28324, 0.02328, 0.29541, 0.13198, 0.36023, 0.22302, 0.36685, 0.32417, 0.35062, 0.29183, 0.28438, 0.18233, 0.20121, 0.17306, 0.15842, 0.1731, 0.05076, 0.17695, 0.02043, 0.18614, 0.00644, 0.3362, 0.42008, 0.29174, 0.50033, 0.26672, 0.59043, 0.27784, 0.67631, 0.34083, 0.78472, 0.64947, 0.29292, 0.668, 0.38277, 0.68322, 0.54829, 0.67331, 0.65801, 0.64366, 0.75938, 0.62143, 0.8171, 0.61865, 0.8706, 0.59178, 0.91284, 0.74246, 0.44444, 0.7596, 0.54973, 0.72817, 0.64743, 0.72532, 0.7397, 0.69175, 0.80375, 0.82556, 0.65725, 0.79076, 0.76067, 0.74296, 0.8033, 0.4443, 0.77484, 0.4824, 0.66546, 0.54097, 0.5861, 0.61506, 0.57538, 0.57463, 0.35043, 0.48572, 0.3944, 0.42504, 0.4952, 0.37988, 0.64426, 0.38623, 0.76544, 0.17236, 0.4795, 0.25774, 0.43124, 0.58823, 0.65921, 0.5658, 0.73102, 0.55299, 0.80161, 0.54578, 0.87464], "triangles": [52, 0, 51, 51, 0, 50, 49, 50, 2, 52, 53, 0, 1, 50, 0, 48, 2, 3, 2, 50, 1, 48, 49, 2, 59, 7, 8, 59, 8, 9, 42, 43, 44, 79, 6, 7, 79, 7, 59, 5, 6, 79, 47, 48, 3, 60, 59, 9, 60, 9, 10, 79, 59, 60, 14, 12, 13, 11, 12, 14, 80, 4, 5, 80, 5, 79, 3, 4, 80, 54, 47, 3, 80, 54, 3, 85, 46, 47, 85, 47, 54, 17, 67, 10, 41, 44, 45, 42, 44, 41, 15, 11, 14, 11, 17, 10, 84, 45, 46, 84, 46, 85, 45, 40, 41, 81, 54, 80, 18, 67, 17, 55, 85, 54, 16, 17, 11, 16, 11, 15, 84, 39, 45, 39, 40, 45, 67, 61, 60, 67, 60, 10, 68, 61, 67, 18, 68, 67, 78, 79, 60, 78, 60, 61, 77, 80, 79, 77, 81, 80, 79, 78, 77, 56, 85, 55, 84, 85, 56, 81, 55, 54, 76, 82, 81, 82, 55, 81, 56, 55, 82, 69, 61, 68, 38, 84, 56, 39, 84, 38, 68, 18, 72, 19, 72, 18, 69, 68, 72, 62, 78, 61, 62, 61, 69, 86, 77, 78, 86, 78, 62, 77, 76, 81, 57, 56, 82, 38, 56, 57, 25, 72, 19, 87, 77, 86, 76, 77, 87, 70, 62, 69, 73, 70, 69, 24, 25, 19, 63, 86, 62, 63, 62, 70, 87, 86, 63, 72, 73, 69, 73, 72, 25, 75, 83, 82, 58, 57, 82, 37, 38, 57, 76, 75, 82, 23, 19, 20, 23, 20, 21, 23, 21, 22, 24, 19, 23, 83, 58, 82, 37, 57, 58, 26, 73, 25, 88, 76, 87, 75, 76, 88, 74, 70, 73, 71, 63, 70, 71, 70, 74, 64, 87, 63, 64, 63, 71, 88, 87, 64, 34, 75, 88, 28, 71, 74, 36, 37, 58, 35, 83, 75, 35, 75, 34, 58, 83, 35, 36, 58, 35, 27, 74, 73, 27, 73, 26, 28, 74, 27, 65, 88, 64, 89, 34, 88, 89, 88, 65, 28, 29, 64, 28, 64, 71, 65, 64, 29, 66, 89, 65, 33, 34, 89, 30, 65, 29, 66, 65, 30, 32, 89, 66, 31, 32, 66, 33, 89, 32, 30, 31, 66], "vertices": [2, 14, 108.16, 103.95, 0.68, 39, 105.5, 114.98, 0.32, 2, 14, 100.75, 102.08, 0.69689, 39, 98.09, 113.11, 0.30311, 2, 14, 87.1, 86.91, 0.72445, 39, 84.43, 97.95, 0.27555, 2, 14, 78.18, 61.83, 0.77156, 39, 75.52, 72.86, 0.22844, 3, 14, 93.4, 49.47, 0.81244, 39, 90.74, 60.5, 0.17956, 40, 52.15, 59.37, 0.008, 3, 14, 104.01, 32.66, 0.85956, 39, 101.35, 43.69, 0.11644, 40, 62.76, 42.55, 0.024, 3, 14, 108.42, 16.34, 0.88711, 39, 105.75, 27.37, 0.07089, 40, 67.17, 26.24, 0.042, 3, 14, 108.47, 4.88, 0.898, 39, 105.81, 15.91, 0.0525, 40, 67.22, 14.78, 0.0495, 3, 14, 103.18, -8.86, 0.91733, 39, 100.52, 2.17, 0.014, 40, 61.93, 1.04, 0.06867, 2, 14, 97.01, -17.12, 0.89867, 40, 55.76, -7.22, 0.10133, 2, 14, 82.67, -27.98, 0.85267, 40, 41.42, -18.08, 0.14733, 2, 14, 91.43, -68.5, 0.74756, 40, 50.18, -58.6, 0.25244, 2, 14, 102.96, -74.74, 0.68356, 40, 61.71, -64.84, 0.31644, 2, 14, 104.84, -81.63, 0.656, 40, 63.6, -71.73, 0.344, 2, 14, 93.35, -84.36, 0.656, 40, 52.1, -74.46, 0.344, 2, 14, 82.55, -81.11, 0.68356, 40, 41.3, -71.21, 0.31644, 2, 14, 72.97, -71.24, 0.73867, 40, 31.73, -61.35, 0.26133, 2, 14, 73.27, -42.94, 0.78133, 40, 32.02, -33.04, 0.21867, 2, 14, 66.04, -41.01, 0.75645, 40, 24.8, -31.11, 0.24355, 2, 14, 50.1, -76.57, 0.664, 40, 8.85, -66.67, 0.336, 2, 14, 55.06, -83.93, 0.584, 40, 13.81, -74.03, 0.416, 2, 14, 54.3, -92.85, 0.544, 40, 13.05, -82.95, 0.456, 2, 14, 51.07, -98.96, 0.544, 40, 9.82, -89.06, 0.456, 2, 14, 33.48, -86.55, 0.544, 40, -7.76, -76.65, 0.456, 2, 14, 34.18, -75.75, 0.544, 40, -7.07, -65.85, 0.456, 2, 14, 37.38, -67.64, 0.57333, 40, -3.87, -57.74, 0.42667, 2, 14, 25.04, -62.95, 0.64267, 40, -16.21, -53.05, 0.35733, 2, 14, 11.36, -48.19, 0.752, 40, -29.89, -38.29, 0.248, 2, 14, 12.8, -35.72, 0.84267, 40, -28.45, -25.82, 0.15733, 3, 14, 3.27, -34.97, 0.89333, 39, 0.61, -23.94, 0.01067, 40, -37.97, -25.07, 0.096, 3, 14, -8.59, -25.64, 0.904, 39, -11.25, -14.61, 0.032, 40, -49.84, -15.74, 0.064, 3, 14, -18.35, -16.1, 0.90044, 39, -21.01, -5.07, 0.06756, 40, -59.59, -6.2, 0.032, 3, 14, -19.02, -10.46, 0.88711, 39, -21.68, 0.57, 0.10222, 40, -60.27, -0.56, 0.01067, 2, 14, -11.94, 5.57, 0.86178, 39, -14.61, 16.6, 0.13822, 2, 14, 0.04, 18.52, 0.83167, 39, -2.62, 29.56, 0.16833, 2, 14, -7, 30.18, 0.82356, 39, -9.66, 41.21, 0.17644, 2, 14, -10.76, 48.02, 0.82489, 39, -13.42, 59.05, 0.17511, 2, 14, -2.69, 71.23, 0.81245, 39, -5.35, 82.26, 0.18755, 2, 14, 11.6, 87.3, 0.78133, 39, 8.94, 98.33, 0.21867, 2, 14, 29.49, 99.06, 0.73067, 39, 26.83, 110.09, 0.26933, 2, 14, 24.62, 107.59, 0.69689, 39, 21.96, 118.62, 0.30311, 2, 14, 34.02, 123.29, 0.68, 39, 31.36, 134.32, 0.32, 2, 14, 50.18, 137.61, 0.68, 39, 47.52, 148.64, 0.32, 2, 14, 56.55, 138.81, 0.68, 39, 53.88, 149.85, 0.32, 2, 14, 55.76, 134.18, 0.68267, 39, 53.09, 145.21, 0.31733, 2, 14, 52.03, 107.46, 0.70222, 39, 49.37, 118.5, 0.29778, 2, 14, 55.95, 86.78, 0.73867, 39, 53.29, 97.81, 0.26133, 2, 14, 63.8, 64.63, 0.76978, 39, 61.14, 75.67, 0.23022, 2, 14, 71.85, 74.25, 0.76711, 39, 69.19, 85.28, 0.23289, 2, 14, 78.25, 101.79, 0.73067, 39, 75.58, 112.82, 0.26933, 2, 14, 84.07, 105.39, 0.69689, 39, 81.41, 116.42, 0.30311, 2, 14, 99.99, 109.2, 0.68, 39, 97.32, 120.23, 0.32, 2, 14, 104.68, 109.41, 0.68, 39, 102.02, 120.44, 0.32, 2, 14, 107.24, 107.84, 0.68, 39, 104.58, 118.87, 0.32, 2, 14, 54.18, 59.47, 0.81511, 39, 51.52, 70.5, 0.18489, 2, 14, 39.93, 66.61, 0.832, 39, 37.26, 77.64, 0.168, 2, 14, 25.26, 69.04, 0.832, 39, 22.6, 80.07, 0.168, 2, 14, 13.16, 63.5, 0.832, 39, 10.5, 74.53, 0.168, 2, 14, 0.53, 45.51, 0.832, 39, -2.13, 56.54, 0.168, 3, 14, 89.85, -6.4, 0.85333, 39, 87.19, 4.64, 0.12067, 40, 48.6, 3.5, 0.026, 3, 14, 77.57, -13.74, 0.81267, 39, 74.91, -2.71, 0.18133, 40, 36.32, -3.84, 0.006, 2, 14, 53.92, -23.03, 0.80533, 39, 51.26, -12, 0.19467, 2, 14, 37.17, -24.69, 0.8085, 39, 34.51, -13.66, 0.1915, 2, 14, 20.59, -21.62, 0.808, 39, 17.93, -10.59, 0.192, 2, 14, 10.86, -18.67, 0.808, 39, 8.2, -7.64, 0.192, 2, 14, 2.8, -19.94, 0.808, 39, 0.14, -8.91, 0.192, 2, 14, -4.89, -15.41, 0.808, 39, -7.55, -4.38, 0.192, 2, 14, 72.46, -32.65, 0.876, 40, 31.21, -22.75, 0.124, 2, 14, 57.82, -40.24, 0.80267, 40, 16.57, -30.34, 0.19733, 2, 14, 41.69, -36.64, 0.864, 40, 0.44, -26.74, 0.136, 2, 14, 27.9, -39.27, 0.904, 40, -13.35, -29.37, 0.096, 2, 14, 16.62, -34, 0.904, 40, -24.63, -24.1, 0.096, 2, 14, 45.48, -58.86, 0.73333, 40, 4.23, -48.96, 0.26667, 2, 14, 28.32, -54.71, 0.72933, 40, -12.93, -44.81, 0.27067, 2, 14, 19.45, -45.49, 0.734, 40, -21.8, -35.59, 0.266, 2, 14, 7.57, 22.61, 0.82, 39, 4.91, 33.65, 0.18, 2, 14, 25.79, 17.93, 0.81511, 39, 23.13, 28.96, 0.18489, 2, 14, 40.67, 7.59, 0.81311, 39, 38.01, 18.62, 0.18689, 2, 14, 46.25, -8.68, 0.80983, 39, 43.58, 2.36, 0.19017, 2, 14, 77.32, 8.38, 0.79022, 39, 74.66, 19.41, 0.20978, 2, 14, 66.03, 26.79, 0.77778, 39, 63.37, 37.82, 0.22222, 2, 14, 47.86, 36.85, 0.75378, 39, 45.2, 47.88, 0.24622, 2, 14, 23.4, 41.71, 0.71644, 39, 20.74, 52.74, 0.28356, 2, 14, 5.83, 35.99, 0.69333, 39, 3.17, 47.02, 0.30667, 2, 14, 36.58, 94.17, 0.76867, 39, 33.91, 105.2, 0.23133, 2, 14, 48.31, 76.7, 0.78978, 39, 45.64, 87.73, 0.21022, 2, 14, 32.41, -5.62, 0.80867, 39, 29.75, 5.41, 0.19133, 2, 14, 20.59, -3.13, 0.808, 39, 17.93, 7.9, 0.192, 2, 14, 9.46, -2.75, 0.808, 39, 6.8, 8.28, 0.192, 2, 14, -1.72, -3.72, 0.808, 39, -4.38, 7.31, 0.192], "hull": 54, "edges": [0, 106, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 44, 46, 46, 48, 62, 64, 72, 74, 74, 76, 82, 84, 84, 86, 86, 88, 88, 90, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 76, 78, 68, 70, 70, 72, 64, 66, 66, 68, 60, 62, 54, 56, 52, 54, 40, 42, 36, 38, 38, 40, 32, 34, 34, 36, 108, 110, 110, 112, 112, 114, 114, 116, 14, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 20, 134, 136, 138, 138, 140, 140, 142, 56, 58, 58, 60, 48, 50, 50, 52, 42, 44, 136, 144, 144, 146, 146, 148, 68, 150, 150, 152, 152, 154, 154, 156, 156, 124, 120, 158, 158, 160, 160, 162, 162, 164, 164, 166, 168, 170, 170, 108, 90, 92, 92, 94, 78, 80, 80, 82, 156, 172, 172, 174, 174, 176, 176, 178], "width": 92, "height": 60}}, "qiao": {"qiao": {"type": "mesh", "color": "c5c5c5ff", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-49.78, 348.56, -416.97, 351.48, -414.06, 718.67, -46.87, 715.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 153, "height": 153}}, "niutou06": {"niutou06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [63.78, 25.85, 19.27, -25.06, -25.81, -16.92, 18.7, 34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 29, "height": 52}}, "fanren07": {"fanren07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [7.58, -29.92, 10.54, 37.01, 64.49, 34.63, 61.53, -32.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 54}}, "g5": {"g3": {"type": "mesh", "uvs": [0.25087, 0, 0.09389, 0.07971, 0.03312, 0.21545, 0.0787, 0.36978, 0, 0.44415, 0, 0.56315, 0.01287, 0.66356, 0.08629, 0.69703, 0.15465, 0.63381, 0.24074, 0.7119, 0.35721, 0.74165, 0.39772, 0.83462, 0.52178, 0.86623, 0.53951, 0.94804, 0.57242, 1, 0.68382, 1, 0.74206, 0.92573, 0.82055, 0.85321, 0.80789, 0.7212, 0.87119, 0.61893, 0.86359, 0.5334, 0.95727, 0.55943, 1, 0.52224, 1, 0.41626, 0.99778, 0.31028, 0.87878, 0.2452, 0.79017, 0.23032, 0.73699, 0.12992, 0.64078, 0.01835, 0.42304, 0, 0.29457, 0.13114, 0.35648, 0.2433, 0.41221, 0.35091, 0.48238, 0.49489, 0.54429, 0.63433, 0.60621, 0.73285, 0.65987, 0.84197, 0.62684, 0.93291, 0.59795, 0.32969, 0.76718, 0.37819, 0.88069, 0.4373, 0.22234, 0.40396, 0.11708, 0.52217, 0.06548, 0.61463], "triangles": [11, 10, 35, 36, 35, 18, 12, 11, 35, 36, 18, 17, 36, 12, 35, 16, 36, 17, 37, 12, 36, 37, 36, 16, 13, 12, 37, 14, 13, 37, 15, 37, 16, 14, 37, 15, 33, 20, 34, 34, 20, 19, 9, 8, 33, 33, 10, 9, 18, 34, 19, 35, 34, 18, 34, 10, 33, 10, 34, 35, 42, 3, 41, 4, 3, 42, 5, 4, 42, 43, 5, 42, 33, 8, 42, 33, 42, 41, 43, 42, 8, 6, 5, 43, 7, 43, 8, 6, 43, 7, 38, 27, 26, 39, 38, 26, 39, 26, 25, 39, 25, 24, 40, 39, 24, 23, 40, 24, 40, 23, 22, 20, 39, 40, 20, 40, 22, 21, 20, 22, 39, 33, 38, 20, 33, 39, 31, 28, 27, 38, 31, 27, 32, 31, 38, 3, 2, 31, 41, 3, 31, 32, 41, 31, 33, 32, 38, 41, 32, 33, 30, 0, 29, 1, 0, 30, 2, 1, 30, 31, 30, 29, 2, 30, 31, 28, 31, 29], "vertices": [2, 305, 14.1, 2.73, 0.9971, 309, -30.32, -1.26, 0.0029, 3, 305, 10.49, 10.94, 0.93781, 304, 24.7, 10.09, 0.00315, 309, -23.33, -6.87, 0.05904, 3, 305, 2.5, 15.39, 0.67704, 304, 17.08, 15.15, 0.10802, 309, -14.19, -7.13, 0.21494, 3, 305, -7.6, 15.16, 0.13339, 304, 6.99, 15.71, 0.15205, 309, -5.33, -2.26, 0.71456, 2, 305, -11.57, 19.69, 0.00175, 309, 0.28, -4.45, 0.99825, 1, 309, 7.58, -2.28, 1, 2, 309, 13.57, 0.14, 0.99567, 306, -2.51, -23.56, 0.00433, 3, 304, -13.3, 20.92, 4e-05, 309, 14.64, 4.06, 0.97653, 306, 0.54, -20.88, 0.02343, 4, 304, -10.25, 16.75, 0.0205, 309, 9.84, 5.98, 0.77468, 306, -2.4, -16.63, 0.20339, 307, -10.22, -17.37, 0.00143, 4, 304, -16.15, 14.18, 0.01257, 309, 13.48, 11.28, 0.35239, 306, 3.56, -14.21, 0.59371, 307, -4.44, -14.55, 0.04133, 4, 304, -19.43, 9.41, 0.00073, 309, 13.74, 17.07, 0.11827, 306, 6.98, -9.53, 0.62134, 307, -1.35, -9.65, 0.25965, 3, 309, 18.9, 20.6, 0.01702, 306, 13.23, -9.44, 0.23656, 307, 4.87, -9.13, 0.74643, 3, 309, 19.18, 26.76, 0.00076, 306, 16.86, -4.45, 0.01723, 307, 8.15, -3.9, 0.98201, 1, 307, 13.44, -4.27, 1, 1, 307, 17.03, -3.51, 1, 1, 307, 18.21, 1.59, 1, 2, 308, 19.5, -30.93, 0.00027, 307, 14.19, 5.33, 0.99973, 3, 308, 19.68, -25, 0.00883, 306, 20.15, 9.22, 0.02485, 307, 10.5, 9.97, 0.96632, 3, 308, 14.15, -18.59, 0.11182, 306, 11.89, 11.11, 0.33162, 307, 2.13, 11.29, 0.55657, 4, 304, -18.28, -15.97, 0.00067, 308, 12.62, -11.56, 0.36501, 306, 6.5, 15.86, 0.46154, 307, -3.57, 15.66, 0.17278, 4, 304, -12.9, -17.08, 0.00768, 308, 9.06, -7.39, 0.73086, 306, 1.16, 17.12, 0.22531, 307, -8.99, 16.54, 0.03614, 3, 308, 13.58, -6.09, 0.94178, 306, 4.03, 20.84, 0.05357, 307, -6.37, 20.46, 0.00465, 3, 308, 13.77, -2.98, 0.96672, 306, 2.34, 23.46, 0.03132, 307, -8.24, 22.95, 0.00196, 1, 308, 9.71, 2.45, 1, 2, 305, -12.05, -27.98, 5e-05, 308, 5.57, 7.83, 0.99995, 3, 305, -6.9, -23.27, 0.01816, 304, 4.69, -22.66, 0.03005, 308, -1.41, 7.82, 0.95178, 3, 305, -5.17, -19.36, 0.08615, 304, 6.71, -18.9, 0.1649, 308, -5.31, 6.09, 0.74895, 3, 305, 1.61, -18.13, 0.34389, 304, 13.57, -18.2, 0.27791, 308, -11.16, 9.75, 0.3782, 3, 305, 9.48, -15.04, 0.64857, 304, 21.66, -15.73, 0.17519, 308, -19.05, 12.76, 0.17623, 3, 305, 12.57, -5.21, 0.95099, 304, 25.51, -6.18, 0.02133, 308, -27.96, 7.58, 0.02768, 2, 305, 5.47, 2.3, 0.98931, 309, -22.87, 3.11, 0.01069, 3, 305, -2.13, 0.81, 0.04948, 304, 11.32, 0.97, 0.94167, 309, -16.82, 7.94, 0.00884, 3, 305, -9.39, -0.46, 0.0002, 304, 3.99, 0.27, 0.9966, 309, -10.96, 12.42, 0.0032, 4, 304, -5.77, -0.46, 0.48817, 308, -6.78, -16.13, 0.04239, 309, -3.07, 18.21, 0.02615, 306, -6.42, 0.69, 0.4433, 4, 304, -15.15, -0.9, 0.00012, 308, 0.89, -21.54, 0.00823, 306, 2.97, 0.88, 0.99093, 307, -6.07, 0.46, 0.00072, 3, 308, 7, -24.85, 0.0097, 306, 9.85, 1.83, 0.30768, 307, 0.73, 1.88, 0.68262, 3, 308, 13.2, -28.94, 0.00219, 306, 17.26, 2.21, 0.00397, 307, 8.1, 2.77, 0.99384, 1, 307, 13.42, -0.05, 1, 4, 305, -9.71, -9.29, 0.0163, 304, 2.98, -8.5, 0.59979, 308, -8.75, -4.41, 0.36394, 306, -14.95, 8.97, 0.01997, 5, 305, -14.26, -16.51, 0.00049, 304, -2.12, -15.35, 0.06725, 308, -0.52, -2.14, 0.89785, 306, -9.66, 15.67, 0.03438, 307, -19.69, 14.36, 4e-05, 4, 304, -7.19, -19.49, 0.00296, 308, 6.02, -1.98, 0.96511, 306, -4.49, 19.68, 0.03015, 307, -14.8, 18.71, 0.00177, 4, 305, -11.03, 8.95, 0.04272, 304, 3.09, 9.78, 0.41698, 309, -5.16, 4.83, 0.51838, 306, -15.54, -9.3, 0.02192, 3, 304, -2.9, 16.56, 0.02534, 309, 3.5, 2.25, 0.93771, 306, -9.74, -16.24, 0.03694, 3, 304, -7.96, 20.47, 0.0016, 309, 9.86, 1.61, 0.97569, 306, -4.79, -20.28, 0.02271], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 64, 76, 76, 78, 78, 80, 64, 82, 82, 84, 84, 86], "width": 47, "height": 64}}, "g2": {"g2": {"type": "mesh", "uvs": [0.2411, 0.43976, 0.25034, 0.2723, 0.30764, 0.10246, 0.40191, 0, 0.55349, 0, 0.6644, 0.02831, 0.74203, 0.15509, 0.75313, 0.28187, 0.74019, 0.39909, 0.7827, 0.30579, 0.80303, 0.18619, 0.88807, 0.10725, 0.98049, 0.11921, 1, 0.18619, 1, 0.31297, 0.95461, 0.41105, 0.87328, 0.48042, 0.79379, 0.5163, 0.73834, 0.54501, 0.74758, 0.64787, 0.77531, 0.76509, 0.85849, 0.84403, 0.94722, 0.87273, 1, 0.8847, 0.99528, 0.95885, 0.93058, 1, 0.83816, 1, 0.68658, 1, 0.55719, 1, 0.39822, 0.94211, 0.28731, 0.81532, 0.24479, 0.67179, 0.23925, 0.59525, 0.13943, 0.58568, 0.03592, 0.51391, 0, 0.43736, 0, 0.32493, 0.03407, 0.25317, 0.12834, 0.2436, 0.17825, 0.32015, 0.19858, 0.40626, 0.22658, 0.45368, 0.50703, 0.14619, 0.50703, 0.29098, 0.50058, 0.47196, 0.50488, 0.64737, 0.55006, 0.77545, 0.65548, 0.87011, 0.77597, 0.90631, 0.87709, 0.93137, 0.93949, 0.93972, 0.73989, 0.46938, 0.83739, 0.38979, 0.88839, 0.28497, 0.92289, 0.19179, 0.25412, 0.51985, 0.16862, 0.50626, 0.09962, 0.43832, 0.07262, 0.33156, 0.36362, 0.52761, 0.60212, 0.51791, 0.63812, 0.35679, 0.64112, 0.22479, 0.60212, 0.05591, 0.44762, 0.06756, 0.37562, 0.14909, 0.35462, 0.32379, 0.36512, 0.63049, 0.41612, 0.77414, 0.52712, 0.8712, 0.63212, 0.62855, 0.66212, 0.7392], "triangles": [29, 68, 69, 29, 30, 68, 69, 68, 46, 30, 31, 67, 30, 67, 68, 68, 45, 46, 46, 70, 71, 46, 45, 70, 68, 67, 45, 45, 67, 59, 59, 32, 55, 67, 32, 59, 67, 31, 32, 33, 56, 32, 32, 56, 55, 34, 57, 33, 33, 57, 56, 56, 41, 55, 41, 0, 55, 66, 59, 0, 59, 55, 0, 34, 35, 57, 56, 40, 41, 56, 57, 40, 35, 58, 57, 57, 39, 40, 57, 58, 39, 35, 36, 58, 36, 37, 58, 58, 38, 39, 58, 37, 38, 70, 18, 19, 70, 60, 18, 18, 51, 17, 18, 60, 51, 60, 61, 51, 16, 17, 52, 17, 51, 52, 16, 52, 15, 51, 8, 52, 51, 61, 8, 52, 53, 15, 15, 53, 14, 8, 9, 52, 8, 61, 7, 52, 9, 53, 53, 54, 14, 54, 13, 14, 53, 9, 10, 53, 10, 54, 10, 11, 54, 54, 12, 13, 54, 11, 12, 44, 43, 61, 44, 66, 43, 0, 1, 66, 61, 62, 7, 61, 43, 62, 66, 65, 43, 66, 1, 65, 43, 65, 42, 65, 64, 42, 43, 42, 62, 62, 6, 7, 1, 2, 65, 42, 63, 62, 62, 5, 6, 62, 63, 5, 65, 3, 64, 65, 2, 3, 42, 4, 63, 42, 64, 4, 64, 3, 4, 63, 4, 5, 45, 59, 44, 45, 60, 70, 45, 44, 60, 44, 59, 66, 60, 44, 61, 26, 49, 25, 25, 50, 24, 25, 49, 50, 27, 48, 26, 26, 48, 49, 24, 50, 23, 50, 22, 23, 50, 49, 22, 48, 21, 49, 49, 21, 22, 47, 20, 48, 48, 20, 21, 28, 47, 27, 27, 47, 48, 29, 69, 28, 28, 69, 47, 69, 46, 47, 47, 71, 20, 47, 46, 71, 71, 19, 20, 71, 70, 19], "vertices": [4, 286, 7.11, 16.47, 0.16085, 287, -4.51, 16.05, 0.15702, 292, -0.32, -4.72, 0.66606, 288, -18.76, -8.38, 0.01608, 3, 286, 15.62, 15.55, 0.12999, 287, 4.05, 16.15, 0.64013, 292, 4.09, -12.05, 0.22988, 3, 286, 24.14, 11.46, 0.01172, 287, 13, 13.09, 0.92414, 292, 5.99, -21.31, 0.06415, 2, 287, 18.72, 7.32, 0.9887, 292, 3.91, -29.17, 0.0113, 2, 287, 19.54, -2.65, 0.98344, 291, 5.59, 25.81, 0.01656, 2, 287, 18.7, -10.06, 0.90162, 291, 9.85, 19.68, 0.09838, 3, 286, 20.41, -17.1, 0.00074, 287, 12.68, -15.7, 0.75101, 291, 9.04, 11.47, 0.24825, 3, 286, 13.92, -17.59, 0.02643, 287, 6.3, -16.96, 0.53796, 291, 5.07, 6.32, 0.43561, 6, 289, 1.25, 24.47, 0.00149, 290, -0.02, 26.25, 3e-05, 286, 7.98, -16.52, 0.04237, 287, 0.27, -16.6, 0.11068, 291, 0.3, 2.61, 0.84482, 288, -3.86, 21.08, 0.0006, 1, 291, 5.63, 4.08, 1, 1, 291, 10.83, 7.54, 1, 1, 291, 17.66, 6.53, 1, 1, 291, 21.62, 1.85, 1, 1, 291, 20.18, -1.5, 1, 1, 291, 15.68, -6.15, 1, 1, 291, 10.05, -7.66, 1, 1, 291, 3.73, -6.47, 1, 5, 289, 7.05, 20.64, 0.01253, 290, 4, 20.59, 0.0015, 286, 1.88, -19.84, 0.01569, 291, -1.31, -4.14, 0.9645, 288, 3.08, 21.1, 0.00578, 6, 289, 4.4, 17.72, 0.10685, 290, 0.48, 18.82, 0.01758, 286, 0.55, -16.13, 0.15043, 287, -7.16, -17.1, 0.00217, 291, -4.96, -2.65, 0.66855, 288, 2.49, 17.2, 0.05441, 5, 289, 7.25, 13.28, 0.34354, 290, 1.53, 13.65, 0.11483, 286, -4.72, -16.54, 0.14654, 291, -8.17, -6.85, 0.28393, 288, 7.32, 15.07, 0.11115, 5, 289, 11.52, 8.71, 0.33292, 290, 3.85, 7.84, 0.53342, 286, -10.76, -18.15, 0.03488, 291, -11.01, -12.42, 0.06684, 288, 13.4, 13.62, 0.03194, 5, 289, 18.22, 7.49, 0.00949, 290, 9.66, 4.29, 0.98715, 286, -14.98, -23.49, 0.00078, 291, -9.86, -19.13, 0.00252, 288, 19.66, 16.32, 7e-05, 1, 290, 15.62, 3.32, 1, 1, 290, 19.14, 3.01, 1, 1, 290, 19.14, -0.79, 1, 1, 290, 15.06, -3.24, 1, 1, 290, 8.99, -3.75, 1, 2, 289, 11.51, -4.63, 0.58602, 290, -0.98, -4.58, 0.41398, 3, 289, 3.83, -8.37, 0.90916, 292, -33.83, 6.63, 0.00065, 288, 16.44, -4.86, 0.09019, 3, 289, -6.89, -10.32, 0.17265, 292, -23.55, 10.26, 0.04566, 288, 8.59, -12.41, 0.78169, 4, 289, -16.31, -7.72, 0.00423, 286, -12.14, 14.13, 0.03693, 292, -13.85, 9.19, 0.18581, 288, -0.7, -15.45, 0.77303, 3, 286, -4.72, 16.66, 0.12573, 292, -7.34, 4.82, 0.41535, 288, -8.43, -14.16, 0.45892, 4, 286, -0.81, 16.89, 0.1149, 287, -12.42, 15.52, 0.00616, 292, -4.79, 1.83, 0.68854, 288, -11.98, -12.5, 0.1904, 2, 292, 0.88, 5.23, 0.99934, 288, -15.74, -17.93, 0.00066, 1, 292, 8.57, 6.17, 1, 1, 292, 12.76, 4.34, 1, 1, 292, 16.06, -0.35, 1, 1, 292, 16.33, -4.63, 1, 1, 292, 11.52, -8.62, 1, 1, 292, 6.58, -7.32, 1, 3, 286, 8.92, 19.22, 0.00433, 287, -3.04, 18.99, 0.00473, 292, 2.95, -4.5, 0.99094, 4, 286, 6.44, 17.46, 0.08115, 287, -5.3, 16.95, 0.06511, 292, 0.05, -3.59, 0.84228, 288, -18.63, -9.56, 0.01146, 2, 287, 11.86, -0.21, 0.99911, 291, -1.8, 22.58, 0.00089, 2, 287, 4.5, -0.81, 0.99229, 291, -6.93, 17.28, 0.00771, 4, 289, -11.33, 14.19, 0.00055, 286, 4.84, -0.58, 0.99197, 287, -4.74, -1.15, 0.00062, 291, -13.65, 10.94, 0.00686, 4, 289, -7.16, 6.28, 0.00511, 286, -4.11, -0.54, 0.22017, 291, -19.67, 4.31, 0.00652, 288, -0.81, 1.27, 0.7682, 5, 289, -1.61, 1.71, 0.03452, 290, -10.92, 6.07, 1e-05, 286, -10.74, -3.28, 0.00316, 291, -22.06, -2.46, 0.00198, 288, 6.33, 0.54, 0.96033, 5, 289, 6.76, 0.42, 0.99279, 290, -3.58, 1.84, 0.00313, 286, -15.82, -10.05, 0.00096, 291, -20.42, -10.77, 0.00156, 288, 14.02, 4.09, 0.00156, 5, 289, 14.72, 2.25, 0.00779, 290, 4.5, 0.67, 0.99046, 286, -17.96, -17.93, 0.00047, 291, -15.98, -17.62, 0.00118, 288, 19.64, 10.01, 0.00011, 2, 290, 11.25, -0.04, 1, 291, -12.07, -23.18, 0, 1, 290, 15.39, -0.12, 1, 6, 289, 2.81, 21.23, 0.02274, 290, 0.26, 22.68, 0.0024, 286, 4.4, -16.37, 0.06823, 287, -3.3, -16.88, 0.01901, 291, -2.21, 0.05, 0.87578, 288, -0.78, 19.24, 0.01183, 1, 291, 5.24, -1.5, 1, 1, 291, 11.38, 0, 1, 1, 291, 16.32, 1.83, 1, 4, 286, 3, 15.76, 0.14167, 287, -8.51, 14.86, 0.04339, 292, -3.38, -1.87, 0.73431, 288, -14.8, -9.7, 0.08064, 1, 292, 1.63, 0.81, 1, 1, 292, 7.35, 0.6, 1, 1, 292, 11.94, -2.83, 1, 4, 286, 2.34, 8.56, 0.50977, 287, -8.31, 7.62, 0.04997, 292, -9.52, -5.71, 0.28278, 288, -10.8, -3.67, 0.15748, 6, 289, -4.28, 15.02, 0.04966, 290, -8.6, 19.45, 0.00185, 286, 2.26, -7.19, 0.6005, 287, -6.52, -8.02, 0.03214, 291, -10.46, 4.59, 0.24702, 288, -3.25, 10.15, 0.06882, 5, 289, -5.75, 23.45, 0.00268, 286, 10.38, -9.87, 0.19757, 287, 1.87, -9.71, 0.43699, 291, -3.04, 8.85, 0.36258, 288, -9.14, 16.36, 0.00018, 3, 286, 17.1, -10.31, 0.01771, 287, 8.59, -9.36, 0.77033, 291, 1.78, 13.55, 0.21196, 2, 287, 16.96, -6.08, 0.94289, 291, 5.92, 21.53, 0.05711, 2, 287, 15.53, 4.03, 0.9936, 292, -0.54, -28.09, 0.0064, 3, 286, 21.6, 7.06, 0.00844, 287, 11, 8.42, 0.94288, 292, 0.95, -21.95, 0.04869, 4, 286, 12.75, 8.77, 0.2032, 287, 2, 9.07, 0.62077, 292, -3.05, -13.87, 0.17576, 288, -20.06, 1.08, 0.00026, 4, 286, -2.91, 8.65, 0.28561, 287, -13.53, 7.09, 0.00224, 292, -12.62, -1.48, 0.23619, 288, -6.22, -6.25, 0.47597, 4, 289, -9.59, -2.1, 0.01574, 286, -10.35, 5.55, 0.01445, 292, -19.59, 2.57, 0.07661, 288, 1.8, -7.05, 0.89319, 3, 289, -0.83, -3.34, 0.55426, 292, -28.43, 2.4, 0.00415, 288, 9.78, -3.24, 0.44159, 6, 289, -0.03, 10.82, 0.20701, 290, -6.15, 13.99, 0.01984, 286, -3.45, -8.96, 0.29931, 287, -11.98, -10.46, 7e-05, 291, -12.96, -0.84, 0.20688, 288, 2.61, 9, 0.26689, 5, 289, 4.23, 6.62, 0.56364, 290, -3.71, 8.53, 0.07562, 286, -9.17, -10.74, 0.0814, 291, -15.46, -6.27, 0.09618, 288, 8.48, 7.85, 0.18317], "hull": 42, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 0, 82, 8, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 114, 116, 110, 118, 120, 102, 120, 122, 122, 124, 124, 126, 128, 130, 130, 132, 134, 136, 136, 138, 140, 142], "width": 66, "height": 51}}, "niutou02": {"niutou02": {"type": "mesh", "uvs": [0.03657, 0.72786, 0.02423, 0.80686, 0.0267, 0.88008, 0.30566, 0.96871, 0.68585, 0.95522, 0.69572, 0.86466, 0.69325, 0.78952, 0.78707, 0.7163, 0.94444, 0.55408, 0.99085, 0.32711, 0.92279, 0.12428, 0.70623, 0, 0.40304, 0, 0.18957, 0.11945, 0.05035, 0.31504, 0.01942, 0.50579, 0.70569, 0.28992, 0.58655, 0.53075, 0.41725, 0.72964, 0.35502, 0.84674, 0.4154, 0.19908, 0.28328, 0.37059, 0.25702, 0.67128, 0.85995, 0.29951, 0.8003, 0.51131, 0.65693, 0.70123], "triangles": [19, 2, 1, 5, 19, 18, 4, 19, 5, 3, 19, 4, 3, 2, 19, 15, 14, 21, 22, 15, 21, 22, 21, 17, 25, 17, 24, 18, 22, 17, 7, 25, 24, 0, 15, 22, 25, 18, 17, 6, 25, 7, 19, 22, 18, 18, 6, 5, 6, 18, 25, 22, 1, 0, 1, 22, 19, 20, 12, 11, 13, 12, 20, 16, 20, 11, 16, 11, 10, 23, 16, 10, 23, 10, 9, 21, 13, 20, 14, 13, 21, 24, 16, 23, 24, 23, 9, 16, 21, 20, 17, 16, 24, 17, 21, 16, 8, 24, 9, 7, 24, 8], "vertices": [3, 249, 25.72, -20.95, 0.02967, 250, 20.38, -7.72, 0.90298, 251, -7.95, 1.48, 0.06736, 3, 249, 28.87, -21.65, 0.00576, 250, 22.84, -5.64, 0.73992, 251, -7.25, -1.67, 0.25432, 3, 249, 31.84, -21.86, 0.00042, 250, 24.8, -3.41, 0.58838, 251, -6.19, -4.44, 0.41121, 1, 251, 3.41, -4.88, 1, 2, 250, 11.46, 13.16, 0.00291, 251, 14.68, -0.34, 0.99709, 2, 250, 8.73, 10.68, 0.17438, 251, 13.76, 3.22, 0.82562, 2, 250, 6.72, 8.39, 0.54869, 251, 12.67, 6.07, 0.45131, 3, 249, 27.53, 3, 0.03245, 250, 2.51, 8.25, 0.84641, 251, 14.5, 9.86, 0.12114, 3, 249, 21.45, 8.65, 0.67318, 250, -5.63, 6.85, 0.32517, 251, 17.05, 17.72, 0.00166, 2, 249, 12.42, 11.03, 0.99926, 250, -12.97, 1.11, 0.00074, 1, 249, 4.02, 9.65, 1, 1, 249, -1.66, 3.24, 1, 2, 249, -2.58, -6.41, 0.96438, 250, -8.23, -21.39, 0.03562, 2, 249, 1.6, -13.68, 0.80683, 250, 0.05, -22.48, 0.19317, 2, 249, 9.08, -18.88, 0.50637, 250, 8.69, -19.7, 0.49363, 2, 249, 16.69, -20.62, 0.23249, 250, 14.66, -14.7, 0.76751, 1, 249, 10.05, 2.09, 1, 2, 249, 19.42, -2.65, 0.19571, 250, 2.09, -1.62, 0.80429, 2, 250, 11.53, 0.61, 0.96225, 251, 3.54, 5.44, 0.03775, 2, 250, 16.2, 2.74, 0.05246, 251, 3.25, 0.3, 0.94754, 2, 249, 5.5, -6.8, 0.89309, 250, -3.04, -15.21, 0.10691, 2, 249, 12.03, -11.68, 0.5384, 250, 4.77, -12.98, 0.4616, 2, 249, 24.1, -13.7, 0.0417, 250, 13.66, -4.61, 0.9583, 1, 249, 10.91, 6.96, 1, 3, 249, 19.29, 4.23, 0.75605, 250, -3.44, 2.45, 0.2426, 251, 12.13, 17.83, 0.00135, 2, 250, 5.14, 4.98, 0.8376, 251, 10.38, 9.06, 0.1624], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 22, 32, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 46, 48, 48, 50], "width": 32, "height": 41}}, "niutou010": {"niutou010": {"type": "mesh", "uvs": [0.00016, 0.88032, 0, 1, 0.08098, 1, 0.61433, 0.56084, 0.6857, 0.63048, 1, 0.39064, 1, 0.05931, 0.75985, 0, 0.45934, 0.30163, 0.51203, 0.42773], "triangles": [7, 6, 5, 9, 8, 7, 7, 3, 9, 5, 3, 7, 4, 3, 5, 2, 0, 9, 2, 9, 3, 1, 0, 2], "vertices": [-5.85, 14.76, -16.36, 0.8, -4.37, -8.19, 113.03, -16.07, 117.5, -32.13, 185.03, -38.99, 214.04, -0.28, 183.69, 33.29, 112.79, 31.41, 109.55, 10.83], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 185, "height": 146}}, "niutou05": {"niutou05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.6, 27.96, 36.75, -1.85, 14.21, -23.14, -13.94, 6.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 41, "height": 31}}, "sd": {"sd": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.48, -8.74, -38.3, -8.74, -38.3, 22.83, 35.48, 22.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 201, "height": 86}}, "mamian010": {"mamian010": {"type": "mesh", "uvs": [0.81977, 0.09774, 0.82621, 0.17326, 0.81414, 0.2148, 0.80851, 0.30495, 0.78089, 0.29834, 0.76721, 0.32194, 0.70296, 0.44573, 0.69936, 0.66036, 0.87052, 0.83515, 1, 0.55483, 0.94742, 0.31675, 0.91137, 0.28716, 0.90084, 0.27852, 0.92693, 0.22775, 0.92736, 0.11036, 0.89395, 0, 0.80842, 0, 0.76789, 0.00609, 0.77368, 0.08135, 0.67793, 0.19558, 0.66631, 0.20944, 0.56596, 0.20453, 0.55258, 0.20388, 0.52794, 0.21833, 0.43696, 0.36845, 0.43184, 0.37472, 0.34154, 0.48527, 0.33523, 0.493, 0.26194, 0.56194, 0.26953, 0.48855, 0.14086, 0.43434, 0, 0.53157, 0, 0.90567, 0.20922, 1, 0.31784, 0.8002, 0.30325, 0.75497, 0.39939, 0.66999, 0.41178, 0.65721, 0.5015, 0.52937, 0.51389, 0.5053, 0.57457, 0.38949, 0.5861, 0.39325, 0.69853, 0.38794, 0.71522, 0.35422, 0.79813, 0.27743, 0.81045, 0.23547, 0.81207, 0.18665, 0.80262, 0.11241, 0.81062, 0.10249, 0.26905, 0.67547, 0.36873, 0.58928, 0.47534, 0.4497, 0.57144, 0.29884, 0.68775, 0.29731, 0.7948, 0.16917, 0.787, 0.09839, 0.80495, 0.05797, 0.87602, 0.04271, 0.85955, 0.24103, 0.85478, 0.33332], "triangles": [59, 3, 58, 9, 59, 10, 5, 4, 3, 8, 59, 9, 7, 6, 59, 8, 7, 59, 6, 5, 59, 5, 3, 59, 10, 59, 11, 11, 59, 12, 13, 58, 14, 2, 1, 58, 12, 58, 13, 58, 3, 2, 59, 58, 12, 57, 16, 15, 56, 17, 16, 56, 16, 57, 18, 17, 56, 0, 56, 57, 55, 18, 56, 48, 55, 56, 0, 48, 56, 57, 15, 14, 47, 55, 48, 1, 0, 57, 58, 1, 57, 57, 14, 58, 54, 55, 47, 46, 54, 47, 46, 45, 54, 44, 54, 45, 43, 53, 44, 18, 54, 19, 55, 54, 18, 54, 53, 19, 20, 19, 53, 53, 54, 44, 42, 53, 43, 52, 21, 20, 52, 20, 53, 41, 40, 52, 53, 41, 52, 42, 41, 53, 52, 23, 22, 52, 22, 21, 52, 24, 23, 40, 51, 52, 28, 30, 29, 49, 30, 28, 49, 32, 31, 30, 49, 31, 33, 32, 49, 33, 35, 34, 33, 49, 35, 50, 49, 28, 35, 49, 50, 52, 51, 24, 40, 39, 51, 38, 51, 39, 51, 50, 26, 27, 26, 50, 25, 51, 26, 51, 25, 24, 37, 50, 51, 37, 51, 38, 36, 50, 37, 35, 50, 36, 50, 28, 27], "vertices": [3, 200, -16.03, -4.28, 0.17717, 201, 7.29, -8.3, 0.81658, 202, -12.16, -9.71, 0.00625, 3, 200, -12.2, 4.46, 0.00896, 201, 16.78, -7.26, 0.68931, 202, -2.84, -7.63, 0.30173, 4, 200, -7.18, 7.44, 0.00026, 201, 21.86, -10.12, 0.19196, 202, 2.53, -9.9, 0.80749, 203, -15.56, -9.44, 0.0003, 1, 203, -4.23, -9.87, 1, 1, 203, -4.62, -15.99, 1, 1, 203, -1.46, -18.78, 1, 2, 202, 33.06, -32.24, 0.19008, 203, 14.98, -31.78, 0.80992, 2, 202, 59.88, -31.12, 0.00586, 203, 41.8, -30.66, 0.99414, 1, 203, 60.91, 8.46, 1, 1, 203, 23.93, 34.38, 1, 3, 200, -25.2, 33.86, 0, 202, 13.15, 20.25, 0.008, 203, -4.93, 20.72, 0.992, 1, 203, -8.06, 12.54, 1, 4, 200, -19.08, 24.35, 0, 201, 30.58, 8.62, 0.08291, 202, 9.11, 9.69, 0.88405, 203, -8.97, 10.16, 0.03304, 3, 200, -27.32, 22.04, 0, 201, 24.47, 14.61, 0.47285, 202, 2.37, 14.96, 0.52715, 3, 200, -35.22, 9.67, 0, 201, 9.81, 15.29, 0.9768, 202, -12.27, 14.01, 0.0232, 1, 201, -4.27, 8.49, 1, 2, 200, -20.43, -15.95, 0.18354, 201, -5.02, -10.31, 0.81646, 2, 200, -12.48, -20.06, 0.29406, 201, -4.62, -19.25, 0.70594, 2, 200, -8.54, -11.42, 0.54391, 201, 4.83, -18.35, 0.45609, 2, 199, 34.87, 13.88, 0.10787, 200, 16.89, -10.56, 0.89213, 2, 199, 32.11, 12.5, 0.43892, 200, 19.98, -10.46, 0.56108, 2, 199, 10.31, 16.05, 0.99851, 200, 38.33, -22.74, 0.00149, 1, 199, 7.4, 16.52, 1, 1, 199, 1.79, 15.46, 1, 2, 196, -5.14, -10.85, 0.78382, 199, -20.55, -0.47, 0.21618, 2, 196, -3.78, -10.98, 0.83545, 199, -21.77, -1.1, 0.16455, 2, 196, 20.3, -13.39, 0.68704, 197, -1.71, -14.12, 0.31296, 2, 196, 21.98, -13.56, 0.58305, 197, -0.03, -13.91, 0.41695, 2, 196, 39.84, -17.49, 0.00339, 198, -9.84, -13.5, 0.99661, 2, 196, 32.61, -23.38, 1e-05, 198, -15.59, -20.85, 0.99999, 1, 198, 6.31, -40.02, 1, 2, 197, 67.48, -43.98, 0.06161, 198, 39.39, -43.69, 0.93839, 1, 198, 61.16, -2.31, 1, 2, 197, 54.01, 29.27, 0.024, 198, 25.92, 29.55, 0.976, 1, 198, -6.86, 18.58, 1, 1, 198, -6.65, 12.08, 1, 2, 196, 25.63, 12.43, 0.49571, 197, -2.22, 12.24, 0.50429, 2, 196, 22.52, 12.99, 0.68438, 197, -5.38, 12.1, 0.31562, 2, 196, -2.87, 13.67, 0.84725, 199, -9.16, -22.3, 0.15275, 2, 196, -6.89, 13.15, 0.71763, 199, -6.06, -19.68, 0.28237, 2, 196, -26.44, 10.83, 0.02231, 199, 9.1, -7.12, 0.97769, 2, 196, -28.07, 12.83, 0.0062, 199, 11.56, -7.92, 0.9938, 2, 199, 36.16, -10.56, 0.53931, 200, 25.87, 12.2, 0.46069, 2, 199, 40.36, -6.87, 0.08694, 200, 20.52, 10.59, 0.91306, 1, 200, -0.03, 12.19, 1, 2, 200, -5.12, 9.2, 0.99357, 201, 24.41, -11.04, 0.00643, 2, 200, -8.67, 4.22, 0.94923, 201, 18.32, -10.44, 0.05077, 2, 200, -11.86, -4.74, 0.5552, 201, 8.97, -12.14, 0.4448, 3, 200, -14.01, -4.85, 0.3362, 201, 7.8, -10.34, 0.66168, 202, -11.42, -11.68, 0.00212, 2, 197, 23.47, -0.5, 0.99899, 198, -4.62, -0.22, 0.00101, 2, 196, 24.2, 0.38, 0.63987, 197, -0.95, 0.17, 0.36013, 2, 196, -4.96, 2.36, 0.8156, 199, -13.54, -11.66, 0.1844, 1, 199, 9.93, 4.21, 1, 2, 199, 35.32, 0.99, 0.52032, 200, 21.84, 1.35, 0.47968, 2, 200, -6.62, 0.35, 0.90479, 201, 15.99, -14.15, 0.09521, 2, 200, -9.89, -8.05, 0.55936, 201, 7.08, -15.51, 0.44064, 2, 200, -15.92, -10.22, 0.27159, 201, 2.19, -11.36, 0.72841, 1, 201, 0.91, 4.34, 1, 1, 202, 5.09, 0.3, 1, 1, 203, -1.42, 0.54, 1], "hull": 49, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 44, 46, 46, 48, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 0, 96, 98, 100, 52, 54, 100, 102, 48, 50, 50, 52, 102, 104, 104, 106, 36, 38, 38, 40, 106, 108, 112, 114, 114, 116, 116, 118, 20, 22, 22, 24], "width": 220, "height": 125}}, "a6": {"a6": {"type": "mesh", "uvs": [0.87782, 0.32703, 0.97949, 0.63901, 0.97848, 0.77153, 0.86477, 0.96716, 0.75956, 0.9819, 0.63376, 0.95763, 0.20919, 0.8757, 0.17979, 0.66031, 0.1936, 0.44596, 0.27672, 0.27674, 0.42195, 0.22324, 0.79436, 0.24128, 0.85688, 0.78302], "triangles": [1, 12, 0, 12, 11, 0, 2, 12, 1, 8, 10, 7, 10, 6, 7, 12, 5, 10, 12, 10, 11, 9, 10, 8, 5, 6, 10, 3, 12, 2, 4, 5, 12, 3, 4, 12], "vertices": [2, 61, 8.98, -15.81, 0.99143, 60, 55.29, -17, 0.00857, 2, 61, -0.86, -7.26, 0.60597, 60, 46.88, -7.04, 0.39403, 2, 61, -3.24, -2.67, 0.08705, 60, 45.23, -2.14, 0.91295, 2, 61, -2.2, 6.52, 0.58182, 60, 47.67, 6.78, 0.41818, 2, 61, 1.8, 9.3, 0.84346, 60, 52.05, 8.91, 0.15654, 2, 61, 7.36, 11.18, 0.98044, 60, 57.83, 9.92, 0.01956, 1, 61, 26.1, 17.52, 1, 1, 61, 31.24, 10.73, 1, 1, 61, 34.6, 3.05, 1, 1, 61, 34.32, -4.57, 1, 1, 61, 29.4, -9.55, 1, 2, 61, 13.94, -16.96, 0.99972, 60, 60.02, -18.9, 0.00028, 1, 61, 1.49, 0.35, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 4, 24, 12, 14, 8, 10, 10, 12], "width": 18, "height": 15}}, "mamian012": {"mamian012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-5.13, -15.4, -15.67, 0.41, 5.13, 14.27, 15.67, -1.54], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 25}}, "fanren05": {"fanren05": {"type": "mesh", "uvs": [0.19937, 0, 0.46413, 0.00412, 0.64317, 0.11767, 0.83746, 0.30735, 0.95936, 0.5138, 0.9746, 0.73445, 1, 0.95638, 0.75175, 0.99122, 0.36508, 0.97187, 0.08698, 0.92541, 0.10984, 0.72154, 0.12889, 0.57703, 0.01841, 0.39896, 0.01079, 0.208, 0.06413, 0.07509, 0.3823, 0.17034, 0.5081, 0.34336, 0.59006, 0.55253, 0.59196, 0.75912], "triangles": [11, 16, 17, 11, 12, 16, 12, 15, 16, 12, 13, 15, 16, 2, 3, 16, 15, 2, 13, 14, 15, 14, 0, 15, 15, 1, 2, 15, 0, 1, 8, 18, 7, 6, 7, 5, 18, 8, 10, 7, 18, 5, 8, 9, 10, 10, 11, 18, 11, 17, 18, 18, 17, 5, 17, 4, 5, 17, 3, 4, 17, 16, 3], "vertices": [1, 208, -9.64, 0.44, 1, 2, 209, -27.81, -2.88, 0.02093, 208, -4.17, 10.13, 0.97907, 2, 209, -20.59, 4.46, 0.20132, 208, 5.58, 13.44, 0.79868, 2, 209, -8.63, 12.33, 0.9091, 208, 19.8, 15.09, 0.0909, 1, 209, 4.3, 17.13, 1, 1, 209, 17.99, 17.42, 1, 1, 209, 31.77, 18.15, 1, 1, 209, 33.67, 7.67, 1, 2, 209, 32.06, -8.54, 0.99001, 208, 46.77, -21.84, 0.00999, 2, 209, 28.89, -20.14, 0.94866, 208, 38.72, -30.78, 0.05134, 2, 209, 16.28, -18.86, 0.77034, 208, 28.03, -23.97, 0.22966, 2, 209, 7.34, -17.84, 0.36811, 208, 20.51, -19.04, 0.63189, 2, 209, -3.81, -22.2, 0.01694, 208, 8.58, -17.92, 0.98306, 1, 208, -2.01, -12.62, 1, 1, 208, -8.22, -6.76, 1, 2, 209, -17.6, -6.57, 0.01022, 208, 3.29, 2.24, 0.98978, 2, 209, -6.74, -1.56, 0.12234, 208, 15.24, 1.84, 0.87766, 1, 209, 6.31, 1.56, 1, 1, 209, 19.12, 1.32, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36], "width": 42, "height": 62}}, "niutou03": {"niutou03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [34.53, 4.37, 13.99, -22.71, -12.31, -2.77, 8.24, 24.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 34, "height": 33}}, "hwc03": {"hwc03": {"type": "mesh", "uvs": [0.22102, 0.25024, 0.11444, 0.18638, 0.04021, 0.12808, 0.22673, 0.03368, 0.45702, 0, 0.70634, 0.01424, 0.85669, 0.08088, 0.8015, 0.16834, 0.7996, 0.32798, 0.82624, 0.54871, 0.90047, 0.74306, 1, 0.94852, 0.73489, 1, 0.42847, 0.99572, 0.0954, 0.94852, 0, 0.87356, 0.0916, 0.6917, 0.17724, 0.48346, 0.22673, 0.32243, 0.57906, 0.15938, 0.57206, 0.33601, 0.56917, 0.55413, 0.5642, 0.75035, 0.34805, 0.15759, 0.18948, 0.14108], "triangles": [13, 22, 12, 12, 10, 11, 12, 22, 10, 22, 13, 16, 15, 16, 14, 13, 14, 16, 10, 22, 9, 22, 21, 9, 22, 16, 21, 21, 16, 17, 9, 21, 8, 20, 23, 19, 8, 19, 7, 18, 0, 23, 1, 24, 0, 0, 24, 23, 1, 2, 24, 19, 5, 7, 7, 5, 6, 23, 4, 19, 19, 4, 5, 24, 3, 23, 23, 3, 4, 24, 2, 3, 21, 20, 8, 21, 17, 20, 20, 17, 18, 18, 23, 20, 20, 19, 8], "vertices": [3, 133, 12.97, 19.28, 0.43601, 134, -1.39, 19.57, 0.56045, 135, -20.31, -21.64, 0.00354, 2, 133, 17.71, 26.39, 0.17766, 134, 4.14, 26.09, 0.82234, 2, 133, 22.19, 31.46, 0.13577, 134, 9.18, 30.61, 0.86423, 2, 133, 31.32, 20.75, 0.06837, 134, 17, 18.91, 0.93163, 1, 134, 19.63, 4.59, 1, 2, 133, 35.91, -8.67, 0.00031, 134, 18.16, -10.85, 0.99969, 2, 133, 31.2, -18.51, 0.03163, 134, 12.34, -20.08, 0.96837, 3, 133, 23.46, -15.84, 0.17344, 134, 4.96, -16.53, 0.81945, 135, -30.56, 13.56, 0.00711, 3, 133, 9.95, -17.07, 0.6631, 134, -8.6, -16.18, 0.15476, 135, -17.03, 14.69, 0.18214, 3, 133, -8.56, -20.57, 0.11049, 135, 1.5, 18.06, 0.8519, 136, -20.23, 16.35, 0.03761, 2, 135, 17.52, 24.17, 0.52434, 136, -4.8, 23.84, 0.47566, 2, 135, 34.34, 31.93, 0.18919, 136, 11.27, 33.05, 0.81081, 2, 135, 40.21, 15.96, 0.0724, 136, 18.52, 17.66, 0.9276, 2, 135, 41.6, -2.99, 0.00061, 136, 21.58, -1.09, 0.99939, 3, 133, -46.87, 21.14, 0.00031, 135, 39.51, -23.92, 0.12697, 136, 21.34, -22.13, 0.87272, 3, 133, -41.11, 27.66, 0.0043, 135, 33.71, -30.4, 0.17744, 136, 16.14, -29.09, 0.81826, 3, 133, -25.17, 23.54, 0.07884, 135, 17.8, -26.17, 0.43845, 136, -0.09, -26.28, 0.48271, 4, 133, -7.03, 20.01, 0.51316, 134, -21.17, 22.62, 0.01153, 135, -0.32, -22.51, 0.42142, 136, -18.46, -24.23, 0.05388, 4, 133, 6.9, 18.32, 0.7074, 134, -7.54, 19.32, 0.2434, 135, -14.23, -20.72, 0.0488, 136, -32.47, -23.67, 0.00041, 2, 133, 22.85, -2.04, 0.00236, 134, 5.95, -2.75, 0.99764, 3, 133, 7.87, -3.1, 0.96931, 134, -9.05, -2.07, 0.00163, 135, -15.05, 0.7, 0.02905, 1, 135, 3.42, 2.24, 1, 2, 135, 20.06, 3.47, 0.54697, 136, -0.45, 3.44, 0.45303, 2, 133, 21.58, 12.22, 0.16489, 134, 6.35, 11.57, 0.83511, 2, 133, 22.01, 22.15, 0.15814, 134, 7.92, 21.37, 0.84186], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 8, 38, 38, 40, 40, 42, 42, 44], "width": 62, "height": 85}}, "a21": {"a21": {"type": "mesh", "uvs": [0, 0.01344, 0.10211, 0.0043, 0.40639, 0.00419, 0.50321, 0.07907, 0.57291, 0.18957, 0.60752, 0.28788, 0.64556, 0.39592, 0.68237, 0.49442, 0.72267, 0.58867, 0.76302, 0.66492, 0.8123, 0.73297, 0.87268, 0.79997, 0.93002, 0.86023, 0.98691, 0.91516, 1, 0.94416, 0.88548, 0.93024, 0.61552, 0.96914, 0.43135, 0.99567, 0.26065, 0.9961, 0.20111, 0.985, 0.20478, 0.93189, 0.20549, 0.86892, 0.19514, 0.78762, 0.17642, 0.68263, 0.14538, 0.57082, 0.12299, 0.46785, 0.09697, 0.36111, 0.06156, 0.24782, 0.0227, 0.14223, 0, 0.033, 0.28273, 0.10919, 0.34163, 0.23984, 0.39785, 0.35714, 0.4273, 0.48334, 0.45943, 0.60657, 0.50226, 0.72831, 0.53439, 0.82037, 0.5799, 0.9139], "triangles": [33, 32, 6, 33, 6, 7, 34, 33, 7, 34, 7, 8, 9, 34, 8, 31, 4, 5, 32, 5, 6, 37, 36, 11, 9, 35, 34, 35, 9, 10, 36, 35, 10, 36, 10, 11, 15, 37, 11, 12, 15, 11, 15, 12, 13, 15, 13, 14, 16, 37, 15, 17, 21, 36, 17, 36, 37, 17, 37, 16, 20, 21, 17, 18, 19, 20, 17, 18, 20, 22, 23, 35, 22, 35, 36, 21, 22, 36, 25, 26, 32, 25, 32, 33, 24, 25, 33, 24, 33, 34, 23, 24, 34, 23, 34, 35, 28, 29, 1, 29, 0, 1, 30, 1, 2, 27, 28, 30, 4, 30, 3, 28, 1, 30, 30, 2, 3, 4, 31, 30, 27, 30, 31, 26, 27, 31, 26, 31, 32, 32, 31, 5], "vertices": [2, 67, -11.63, -32.37, 0.9024, 72, -28.32, -6.33, 0.0976, 2, 67, -13.58, -19.38, 0.88, 68, -34.66, -44.55, 0.12, 2, 67, -13.17, 19.26, 0.72, 68, -26.84, -6.71, 0.28, 3, 67, 4.12, 31.37, 0.55294, 68, -7.56, 1.85, 0.42384, 69, -75.33, 3.46, 0.02321, 3, 67, 29.52, 39.93, 0.38261, 68, 19.02, 5.38, 0.52244, 69, -48.69, 6.42, 0.09495, 3, 67, 52.08, 44.08, 0.20769, 68, 41.96, 5.12, 0.54739, 69, -25.76, 5.67, 0.24492, 4, 67, 76.87, 48.63, 0.08148, 70, -55.07, 14.22, 0.03704, 68, 67.16, 4.83, 0.44583, 69, -0.57, 4.84, 0.43565, 4, 67, 99.48, 53.05, 0.01481, 70, -32.44, 9.89, 0.14815, 68, 90.2, 4.82, 0.26786, 69, 22.46, 4.35, 0.56918, 4, 71, -49.49, 16.43, 0.02276, 70, -10.54, 6.35, 0.34761, 68, 112.37, 5.45, 0.1043, 69, 44.64, 4.5, 0.52533, 4, 71, -32.14, 10.94, 0.10532, 70, 7.55, 4.39, 0.52431, 68, 130.51, 6.93, 0.02242, 69, 62.81, 5.59, 0.34795, 3, 71, -15.72, 7.44, 0.28471, 70, 24.34, 4.2, 0.56715, 69, 79.4, 8.2, 0.14815, 3, 71, 1.3, 5.25, 0.52969, 70, 41.45, 5.4, 0.43327, 69, 96.07, 12.24, 0.03704, 4, 74, 48.89, 86.12, 0.00018, 75, -9.11, 85.89, 0.0007, 71, 16.81, 3.6, 0.76528, 70, 56.99, 6.84, 0.23384, 4, 74, 62.1, 92.11, 0.00366, 75, 2.8, 94.17, 0.01591, 71, 31.28, 2.58, 0.89563, 70, 71.38, 8.7, 0.0848, 4, 74, 68.87, 93.13, 0.00625, 75, 9.28, 96.4, 0.03881, 71, 37.72, 0.26, 0.93861, 70, 78.15, 7.69, 0.01633, 4, 74, 64.3, 78.96, 0.01075, 75, 7.35, 81.63, 0.09222, 71, 26.96, -10.03, 0.8812, 70, 69.63, -4.53, 0.01583, 4, 74, 69.89, 43.98, 0.02888, 75, 19.17, 48.24, 0.37916, 71, 15.24, -43.46, 0.54875, 70, 64.72, -39.61, 0.0432, 4, 74, 73.7, 20.12, 0.02913, 75, 27.23, 25.46, 0.63901, 71, 7.24, -66.26, 0.28745, 70, 61.37, -63.54, 0.04441, 4, 74, 71.72, -1.47, 0.03442, 75, 29.19, 3.87, 0.82498, 71, -4.77, -84.31, 0.11193, 70, 53.15, -83.6, 0.02868, 4, 74, 68.47, -8.76, 0.09225, 75, 27.31, -3.88, 0.86472, 71, -11.1, -89.17, 0.03088, 70, 47.9, -89.61, 0.01215, 5, 73, 114.93, -9.33, 0.01062, 74, 56.41, -7.13, 0.2342, 75, 15.15, -4.46, 0.74719, 71, -20.93, -82, 0.00545, 70, 36.85, -84.52, 0.00254, 5, 73, 100.65, -7.33, 0.06889, 74, 42.06, -5.66, 0.41251, 75, 0.77, -5.61, 0.51825, 71, -32.85, -73.89, 0.00024, 70, 23.56, -78.91, 0.0001, 3, 73, 82.02, -6.16, 0.21186, 74, 23.4, -5.19, 0.51295, 75, -17.66, -8.52, 0.2752, 4, 72, 126.55, -5.95, 0.03704, 73, 57.87, -5.33, 0.40766, 74, -0.76, -5.25, 0.45474, 75, -41.41, -12.95, 0.10056, 5, 67, 116.21, -15.34, 0.01896, 72, 100.65, -6.21, 0.12919, 73, 31.97, -5.84, 0.54519, 74, -26.62, -6.72, 0.28549, 75, -66.58, -19.08, 0.02118, 4, 67, 92.6, -17.92, 0.09393, 72, 76.9, -5.67, 0.27644, 73, 8.22, -5.53, 0.51334, 74, -50.37, -7.3, 0.11629, 4, 67, 68.12, -20.95, 0.23354, 72, 52.24, -5.46, 0.38185, 73, -16.44, -5.56, 0.35718, 74, -75.01, -8.25, 0.02744, 3, 67, 42.13, -25.15, 0.4167, 72, 25.92, -6.22, 0.40939, 73, -42.75, -6.58, 0.17391, 3, 67, 17.89, -29.82, 0.59247, 72, 1.28, -7.67, 0.34871, 73, -67.37, -8.26, 0.05882, 2, 67, -7.15, -32.42, 0.7536, 72, -23.89, -6.96, 0.2464, 5, 67, 10.7, 3.29, 0.67718, 72, -1.51, 26.1, 0.05792, 73, -70.49, 25.47, 0.03752, 68, -6.49, -26.97, 0.18811, 69, -74.88, -25.38, 0.03928, 7, 67, 40.7, 10.43, 0.5608, 72, 29.17, 29.25, 0.06873, 73, -39.85, 28.92, 0.07912, 74, -99.68, 25.34, 0.00136, 70, -102.87, -7.73, 0.00249, 68, 24.33, -25.71, 0.20138, 69, -44.04, -24.78, 0.08613, 9, 67, 67.64, 17.27, 0.34945, 72, 56.77, 32.49, 0.07437, 73, -12.28, 32.43, 0.16221, 74, -72.26, 29.88, 0.0151, 75, -118.09, 8.66, 0.00054, 71, -116.51, 11.75, 0.00038, 70, -75.32, -11.43, 0.02274, 68, 52.08, -24.18, 0.1962, 69, -16.26, -23.83, 0.179, 9, 67, 96.58, 20.69, 0.15487, 72, 85.91, 32.08, 0.0637, 73, 16.86, 32.31, 0.23482, 74, -43.14, 30.83, 0.05257, 75, -89.62, 14.87, 0.00706, 71, -90.43, -1.26, 0.00563, 70, -47.2, -19.05, 0.07523, 68, 81.14, -26.38, 0.15225, 69, 12.74, -26.66, 0.25386, 9, 67, 124.84, 24.45, 0.04582, 72, 114.42, 32.11, 0.0351, 73, 45.38, 32.61, 0.24727, 74, -14.66, 32.19, 0.11433, 75, -61.85, 21.36, 0.03251, 71, -64.74, -13.61, 0.02838, 70, -19.57, -26.1, 0.15925, 68, 109.6, -28.11, 0.07915, 69, 41.16, -29, 0.25819, 9, 67, 152.78, 29.58, 0.00635, 72, 142.79, 33.53, 0.01285, 73, 73.73, 34.3, 0.19149, 74, 13.62, 34.94, 0.17529, 75, -34.54, 29.17, 0.08448, 71, -38.56, -24.64, 0.07605, 70, 8.27, -31.76, 0.23958, 68, 138, -28.45, 0.02649, 69, 69.55, -29.93, 0.18742, 9, 67, 173.91, 33.42, 0.00029, 72, 164.24, 34.57, 0.00153, 73, 95.17, 35.55, 0.11148, 74, 34.99, 36.98, 0.21428, 75, -13.89, 35.05, 0.14776, 71, -18.79, -33.01, 0.13558, 70, 29.3, -36.07, 0.28961, 68, 159.47, -28.73, 0.00286, 69, 91.01, -30.68, 0.0966, 6, 73, 117.16, 38.44, 0.06739, 74, 56.87, 40.69, 0.22675, 75, 6.95, 42.65, 0.18346, 71, 2.22, -40.16, 0.16925, 70, 51.3, -38.94, 0.30466, 69, 113.18, -29.84, 0.0485], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 26, 28, 28, 30, 34, 36, 36, 38, 56, 58, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 30, 32, 32, 34, 38, 40, 40, 42], "width": 50, "height": 91}}, "a22": {"a22": {"type": "mesh", "uvs": [0.26831, 0, 0.39594, 0.00459, 0.70552, 0.06002, 0.87459, 0.11049, 0.92331, 0.1327, 0.98415, 0.20289, 1, 0.28711, 0.93679, 0.39481, 0.81651, 0.51729, 0.77454, 0.56239, 0.73596, 0.60384, 0.64508, 0.64757, 0.30536, 0.84758, 0.26686, 0.8774, 0.25592, 0.90816, 0.29267, 0.92982, 0.37687, 0.97149, 0.28938, 0.99546, 0.06502, 0.99575, 0.01181, 0.98066, 0.01305, 0.95998, 0.04609, 0.89995, 0.06469, 0.86851, 0.08896, 0.83362, 0.1128, 0.70919, 0.15384, 0.66128, 0.37518, 0.56357, 0.38569, 0.53345, 0.35144, 0.49901, 0.22978, 0.41174, 0.07853, 0.30324, 0.05164, 0.24745, 0.06372, 0.18484, 0.09865, 0.1422, 0.17747, 0.07472, 0.469, 0.15632], "triangles": [21, 14, 20, 17, 15, 16, 20, 14, 15, 17, 18, 15, 18, 20, 15, 19, 20, 18, 10, 26, 9, 11, 26, 10, 25, 26, 11, 11, 24, 25, 12, 24, 11, 23, 24, 12, 13, 22, 23, 12, 13, 23, 13, 21, 22, 14, 21, 13, 34, 0, 35, 35, 1, 2, 35, 2, 3, 0, 1, 35, 35, 3, 4, 5, 35, 4, 33, 34, 35, 32, 35, 31, 35, 32, 33, 30, 31, 35, 35, 5, 6, 6, 29, 30, 35, 6, 30, 7, 29, 6, 28, 29, 7, 8, 28, 7, 27, 28, 8, 9, 27, 8, 26, 27, 9], "vertices": [1, 77, -37.67, 0.82, 1, 1, 77, -35.55, 8.51, 1, 1, 77, -20.8, 25.78, 1, 1, 77, -8.36, 34.57, 1, 1, 77, -3.11, 36.87, 1, 1, 77, 12.64, 38.41, 1, 1, 77, 31.03, 36.75, 1, 1, 77, 53.82, 29.5, 1, 2, 77, 79.3, 18.3, 0.98846, 78, -15.97, 12.44, 0.01154, 2, 77, 88.71, 14.31, 0.73389, 78, -5.78, 13.11, 0.26611, 2, 77, 97.35, 10.65, 0.22633, 78, 3.58, 13.73, 0.77367, 2, 77, 106.03, 3.7, 0.00133, 78, 14.46, 11.43, 0.99867, 1, 78, 62.68, 5.38, 1, 2, 78, 69.63, 5.19, 0.98252, 95, -5.99, 8.9, 0.01748, 2, 78, 76.23, 6.69, 0.55544, 95, 0.57, 7.22, 0.44456, 2, 78, 80.01, 10.35, 0.10281, 95, 5.6, 8.76, 0.89719, 1, 95, 15.41, 12.56, 1, 1, 95, 19.78, 6.41, 1, 1, 95, 17.76, -7.36, 1, 1, 95, 14, -10.12, 1, 1, 95, 9.53, -9.37, 1, 2, 78, 78.66, -6.22, 0.74389, 95, -3.16, -5.37, 0.25611, 1, 78, 71.77, -7.32, 1, 1, 78, 64.04, -8.32, 1, 1, 78, 37.74, -15.58, 1, 1, 78, 26.98, -16.5, 1, 2, 77, 85.43, -10.23, 0.41822, 78, 2.33, -10.28, 0.58178, 2, 77, 78.99, -8.65, 0.89997, 78, -4.13, -11.76, 0.10003, 2, 77, 71.23, -9.67, 0.99903, 78, -10.61, -16.17, 0.00097, 1, 77, 51.23, -14.41, 1, 1, 77, 26.38, -20.3, 1, 1, 77, 14.04, -20.21, 1, 1, 77, 0.58, -17.51, 1, 1, 77, -8.35, -14.03, 1, 1, 77, -22.28, -7.09, 1, 1, 77, -2.01, 8.25, 1], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 20, 22, 32, 34, 34, 36, 36, 38, 38, 40, 60, 62, 62, 64, 64, 66, 66, 68, 28, 30, 30, 32, 22, 24, 40, 42, 42, 44, 50, 52, 52, 54, 54, 56, 18, 20, 14, 16, 16, 18, 0, 70, 56, 58, 58, 60, 44, 46, 46, 48, 48, 50, 24, 26, 26, 28], "width": 24, "height": 87}}, "bwc011": {"bwc011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24.58, 14.83, 13.81, -25.76, -26.79, -14.99, -16.01, 25.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 42}}, "a24": {"a24": {"type": "mesh", "uvs": [0.43338, 0.00472, 0.52554, 0.00503, 0.66212, 0.0413, 0.7268, 0.14403, 0.77572, 0.22173, 0.82347, 0.32775, 0.86943, 0.4298, 0.90688, 0.54361, 0.93457, 0.63456, 0.95263, 0.71084, 0.97212, 0.78139, 0.98692, 0.85521, 1, 0.92256, 0.97755, 0.9709, 0.7788, 0.96615, 0.52045, 0.96673, 0.24182, 0.97368, 0.00146, 0.9976, 0.02854, 0.91884, 0.05499, 0.84193, 0.08454, 0.75598, 0.11777, 0.65933, 0.15369, 0.55788, 0.19667, 0.42985, 0.23824, 0.30897, 0.26093, 0.21637, 0.28534, 0.11676, 0.28561, 0.03805, 0.3547, 0.0143, 0.52468, 0.88163, 0.52768, 0.74101, 0.52769, 0.57985, 0.52167, 0.42343, 0.52167, 0.28123, 0.51264, 0.14061], "triangles": [31, 30, 21, 15, 29, 14, 15, 16, 29, 13, 14, 12, 31, 23, 32, 31, 7, 8, 31, 6, 7, 31, 32, 6, 32, 5, 6, 32, 33, 5, 33, 4, 5, 19, 16, 18, 17, 18, 16, 30, 10, 29, 14, 11, 12, 11, 29, 10, 19, 29, 16, 19, 20, 29, 29, 20, 30, 30, 20, 21, 11, 14, 29, 30, 8, 9, 30, 31, 8, 10, 30, 9, 31, 21, 22, 31, 22, 23, 23, 24, 32, 25, 34, 33, 24, 25, 33, 25, 26, 34, 26, 27, 34, 27, 28, 34, 28, 0, 34, 34, 0, 1, 24, 33, 32, 4, 34, 3, 4, 33, 34, 34, 2, 3, 34, 1, 2], "vertices": [1, 89, -7.97, 1.44, 1, 1, 89, -10.23, 10.86, 1, 1, 89, -6.61, 26.52, 1, 1, 89, 11.71, 38.02, 1, 1, 67, 38.01, 0.53, 1, 1, 67, 59.27, 5.3, 1, 1, 67, 79.73, 9.9, 1, 1, 67, 102.54, 13.57, 1, 1, 91, -18.94, 80.66, 1, 1, 91, -4.48, 85.87, 1, 1, 91, 8.84, 90.98, 1, 1, 91, 22.89, 95.75, 1, 1, 91, 35.73, 100.06, 1, 2, 91, 45.68, 99.9, 0.00148, 67, 188.07, 20.03, 0.99852, 2, 91, 49.36, 79.33, 0.18685, 67, 186.89, -0.82, 0.81315, 3, 90, 113.01, 51.59, 0, 91, 55.45, 52.9, 0.35872, 67, 186.7, -27.95, 0.64128, 3, 91, 63.26, 24.67, 0.27735, 105, -19.25, 28.83, 0.41627, 67, 187.76, -57.22, 0.30638, 3, 91, 73.5, 1.11, 0.1736, 105, 4.65, 19.41, 0.69797, 67, 192.26, -82.51, 0.12843, 2, 91, 57.51, 0.41, 0.33332, 105, -6.16, 7.61, 0.66668, 2, 91, 41.89, -0.27, 0.66666, 105, -16.72, -3.92, 0.33334, 1, 91, 24.44, -1.04, 1, 2, 90, 61.1, -2.01, 0.33333, 91, 4.82, -1.9, 0.66667, 2, 90, 40.46, -2.33, 0.66667, 91, -15.8, -2.7, 0.33333, 2, 89, 80.53, -2.35, 0.33333, 90, 14.47, -2.97, 0.66667, 2, 89, 56.01, -3.89, 0.66667, 90, -10.09, -3.47, 0.33333, 1, 89, 37.46, -6.01, 1, 1, 89, 17.5, -8.29, 1, 1, 89, 2.21, -12.03, 1, 1, 89, -4.14, -6.12, 1, 3, 90, 96.23, 48.66, 0.01945, 91, 38.76, 49.58, 0.32606, 67, 169.69, -27.31, 0.65449, 3, 90, 68.6, 43.41, 0.11307, 91, 11.25, 43.68, 0.23019, 67, 141.57, -26.68, 0.65674, 4, 89, 101.34, 38.58, 0.02463, 90, 37.01, 37.04, 0.20962, 91, -20.18, 36.57, 0.12745, 67, 109.34, -26.32, 0.6383, 4, 89, 71.12, 30.48, 0.20068, 90, 6.47, 30.23, 0.22532, 91, -50.56, 29.05, 0.01644, 67, 78.05, -26.6, 0.55756, 3, 89, 43.51, 23.67, 0.50414, 90, -21.41, 24.61, 0.14821, 67, 49.61, -26.28, 0.34765, 2, 89, 16.43, 16.03, 0.99797, 90, -48.79, 18.12, 0.00203], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 24, 26, 52, 54, 54, 56, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 30, 32, 32, 34, 26, 28, 28, 30, 4, 6, 6, 8, 48, 50, 50, 52, 30, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 54], "width": 42, "height": 80}}, "a25": {"a25": {"type": "mesh", "uvs": [0.58816, 0.00269, 0.7794, 0.00265, 0.87175, 0.01391, 0.93119, 0.03032, 0.9627, 0.05396, 0.99269, 0.1026, 0.99312, 0.22637, 0.94009, 0.31414, 0.89558, 0.37158, 0.82688, 0.43519, 0.75799, 0.4922, 0.70892, 0.53625, 0.65362, 0.58591, 0.60857, 0.64891, 0.57673, 0.70389, 0.54782, 0.76653, 0.51228, 0.80985, 0.42555, 0.84208, 0.32493, 0.86929, 0.22254, 0.90423, 0.17836, 0.93196, 0.15662, 0.96687, 0.16669, 1, 0.16566, 1, 0.12483, 0.99056, 0.07844, 0.97047, 0.0354, 0.9389, 0.00717, 0.90308, 0, 0.85864, 0.01404, 0.81065, 0.05419, 0.75817, 0.09842, 0.70691, 0.12729, 0.66473, 0.15269, 0.59615, 0.16149, 0.53672, 0.15205, 0.46996, 0.12369, 0.42302, 0.08866, 0.37856, 0.08873, 0.34057, 0.12836, 0.3027, 0.20605, 0.24927, 0.31284, 0.18987, 0.34947, 0.12276, 0.38431, 0.05891, 0.42714, 0.03454, 0.47833, 0.01622, 0.69106, 0.09736, 0.61185, 0.23723, 0.51797, 0.36517], "triangles": [46, 0, 1, 45, 0, 46, 44, 45, 46, 43, 44, 46, 46, 42, 43, 46, 1, 2, 46, 2, 3, 46, 3, 4, 46, 4, 5, 6, 46, 5, 47, 42, 46, 41, 42, 47, 46, 6, 47, 7, 47, 6, 47, 40, 41, 48, 47, 7, 48, 40, 47, 39, 40, 48, 38, 39, 48, 8, 48, 7, 37, 38, 48, 36, 37, 48, 9, 48, 8, 35, 36, 48, 10, 48, 9, 11, 35, 48, 10, 11, 48, 34, 35, 11, 12, 34, 11, 33, 34, 12, 13, 33, 12, 32, 33, 13, 14, 32, 13, 14, 31, 32, 15, 31, 14, 30, 31, 15, 16, 30, 15, 16, 17, 30, 30, 18, 29, 18, 28, 29, 30, 17, 18, 19, 27, 28, 18, 19, 28, 20, 27, 19, 26, 27, 20, 21, 26, 20, 25, 26, 21, 24, 25, 21, 22, 23, 21, 24, 21, 23], "vertices": [1, 14, 45.21, 40.53, 1, 1, 14, 51.29, 15.24, 1, 1, 14, 50.19, 2.06, 1, 1, 14, 46.2, -7.21, 1, 1, 14, 38.74, -13.4, 1, 1, 14, 22.29, -21.54, 1, 2, 35, -65.88, 52.69, 0.00036, 34, 34.05, 54.16, 0.99964, 2, 35, -32.8, 51.66, 0.06958, 34, 67.1, 52.39, 0.93042, 2, 35, -10.91, 49.68, 0.28345, 34, 88.95, 49.91, 0.71655, 2, 35, 13.84, 44.89, 0.70599, 34, 113.58, 44.56, 0.29401, 2, 35, 36.2, 39.62, 0.95378, 34, 135.82, 38.79, 0.04622, 3, 36, -35.84, 32.15, 0.00522, 35, 53.38, 36.1, 0.99267, 34, 152.91, 34.89, 0.00211, 2, 36, -16.16, 30.47, 0.14784, 35, 72.74, 32.14, 0.85216, 2, 36, 7.8, 31.58, 0.7389, 35, 96.66, 30.46, 0.2611, 2, 36, 28.4, 33.53, 0.98492, 35, 117.34, 30, 0.01508, 2, 37, -25.86, 36.54, 0.01742, 36, 51.57, 36.7, 0.98258, 2, 37, -9.2, 36.82, 0.13084, 36, 68.22, 36.88, 0.86916, 2, 37, 5.7, 29.23, 0.44251, 36, 83.08, 29.19, 0.55749, 2, 37, 19.42, 19.26, 0.88845, 36, 96.74, 19.14, 0.11155, 2, 38, -6.68, 12.72, 0.00795, 37, 35.93, 9.95, 0.99205, 2, 38, 3.56, 6.76, 0.66111, 37, 47.48, 7.35, 0.33889, 1, 38, 16.42, 3.87, 1, 1, 38, 28.6, 5.3, 1, 1, 38, 28.6, 5.16, 1, 1, 38, 25.16, -0.41, 1, 1, 38, 17.8, -6.76, 1, 2, 38, 6.21, -12.67, 0.81801, 37, 55.87, -10.38, 0.18199, 2, 38, -6.96, -16.57, 0.20447, 37, 44.49, -18.07, 0.79553, 3, 38, -23.3, -17.62, 0.00256, 37, 29.22, -24, 0.96763, 36, 106.27, -24.18, 0.02981, 2, 37, 11.83, -27.59, 0.73373, 36, 88.85, -27.66, 0.26627, 2, 37, -8.23, -28.31, 0.21531, 36, 68.79, -28.26, 0.78469, 2, 37, -28.03, -28.36, 0.00967, 36, 48.99, -28.18, 0.99033, 2, 36, 33.01, -29.1, 0.97575, 35, 114.64, -32.74, 0.02425, 2, 36, 7.9, -33.38, 0.59129, 35, 89.21, -34.08, 0.40871, 3, 36, -13.32, -38.81, 0.12719, 35, 67.5, -37, 0.87231, 34, 165.39, -38.51, 0.00051, 3, 36, -36.37, -47.41, 0.0027, 35, 43.61, -42.87, 0.9488, 34, 141.37, -43.84, 0.04849, 2, 35, 27.36, -49.89, 0.80757, 34, 124.97, -50.5, 0.19243, 2, 35, 12.18, -57.64, 0.61363, 34, 109.62, -57.9, 0.38637, 2, 35, -1.55, -60.25, 0.4636, 34, 95.83, -60.2, 0.5364, 2, 35, -16.25, -57.57, 0.30002, 34, 81.2, -57.19, 0.69998, 2, 35, -37.54, -50.87, 0.09386, 34, 60.06, -50.02, 0.90614, 2, 35, -61.73, -40.7, 0.00456, 34, 36.1, -39.31, 0.99544, 1, 34, 10.92, -38.48, 1, 1, 14, 18.63, 62.66, 1, 1, 14, 28.71, 59.09, 1, 1, 14, 36.89, 53.89, 1, 1, 14, 14.6, 18.8, 1, 1, 34, 46.56, 3.68, 1, 2, 35, -3.6, -1.21, 0.02508, 34, 95.11, -1.13, 0.97492], "hull": 46, "edges": [0, 90, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 32, 34, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 74, 76, 76, 78, 78, 80, 80, 82, 86, 88, 88, 90, 38, 40, 40, 42, 34, 36, 36, 38, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 12, 14, 14, 16, 82, 84, 84, 86, 2, 92, 92, 94, 94, 96], "width": 54, "height": 147}}, "a26": {"a22": {"type": "mesh", "uvs": [0.26831, 0, 0.39594, 0.00459, 0.70552, 0.06002, 0.87459, 0.11049, 0.92331, 0.1327, 0.98415, 0.20289, 1, 0.28711, 0.93679, 0.39481, 0.81651, 0.51729, 0.77454, 0.56239, 0.73596, 0.60384, 0.64508, 0.64757, 0.30536, 0.84758, 0.26686, 0.8774, 0.25592, 0.90816, 0.29267, 0.92982, 0.37687, 0.97149, 0.28938, 0.99546, 0.06502, 0.99575, 0.01181, 0.98066, 0.01305, 0.95998, 0.04609, 0.89995, 0.06469, 0.86851, 0.08896, 0.83362, 0.1128, 0.70919, 0.15384, 0.66128, 0.37518, 0.56357, 0.38569, 0.53345, 0.35144, 0.49901, 0.22978, 0.41174, 0.07853, 0.30324, 0.05164, 0.24745, 0.06372, 0.18484, 0.09865, 0.1422, 0.17747, 0.07472, 0.469, 0.15632], "triangles": [19, 20, 18, 18, 20, 15, 17, 18, 15, 20, 14, 15, 17, 15, 16, 21, 14, 20, 14, 21, 13, 13, 21, 22, 12, 13, 23, 13, 22, 23, 23, 24, 12, 12, 24, 11, 11, 24, 25, 25, 26, 11, 11, 26, 10, 10, 26, 9, 26, 27, 9, 9, 27, 8, 27, 28, 8, 8, 28, 7, 28, 29, 7, 7, 29, 6, 35, 6, 30, 35, 3, 4, 6, 29, 30, 5, 35, 4, 35, 5, 6, 30, 31, 35, 35, 32, 33, 32, 35, 31, 33, 34, 35, 34, 0, 35, 0, 1, 35, 35, 2, 3, 35, 1, 2], "vertices": [1, 85, -37.78, 0.09, 1, 1, 85, -35.65, 7.78, 1, 1, 85, -22.32, 17.39, 1, 1, 85, -8.26, 22.19, 1, 1, 85, 2.48, 23.38, 1, 1, 85, 16.47, 21.4, 1, 1, 85, 31.52, 18.34, 1, 1, 85, 50.79, 15.06, 1, 2, 85, 76.29, 12.64, 0.98877, 86, -16.12, 6.04, 0.01123, 2, 85, 88.6, 13.58, 0.70723, 86, -5.55, 12.41, 0.29277, 2, 85, 97.24, 9.92, 0.16328, 86, 3.82, 13.03, 0.83672, 1, 86, 14.69, 10.73, 1, 1, 86, 62.91, 4.68, 1, 2, 86, 69.86, 4.49, 0.99946, 104, -6.1, 8.17, 0.00054, 2, 86, 76.47, 5.99, 0.57287, 104, 0.46, 6.49, 0.42713, 2, 86, 80.24, 9.65, 0.10907, 104, 5.49, 8.03, 0.89093, 1, 104, 15.29, 11.83, 1, 1, 104, 19.67, 5.68, 1, 1, 104, 17.65, -8.09, 1, 1, 104, 13.89, -10.85, 1, 2, 86, 92.01, -4.69, 9e-05, 104, 9.42, -10.1, 0.99991, 2, 86, 78.9, -6.92, 0.70856, 104, -3.27, -6.1, 0.29144, 1, 86, 72, -8.02, 1, 1, 86, 64.28, -9.02, 1, 1, 86, 37.97, -16.27, 1, 1, 86, 27.21, -17.2, 1, 2, 85, 85.33, -10.96, 0.46127, 86, 2.56, -10.98, 0.53873, 2, 85, 78.89, -9.38, 0.8986, 86, -3.9, -12.46, 0.1014, 2, 85, 71.12, -10.4, 0.99932, 86, -10.37, -16.87, 0.00068, 1, 85, 51.13, -15.14, 1, 1, 85, 26.27, -21.03, 1, 1, 85, 13.94, -20.94, 1, 1, 85, 0.48, -18.24, 1, 1, 85, -8.46, -14.76, 1, 1, 85, -22.39, -7.82, 1, 1, 85, -2.12, 7.52, 1], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 20, 22, 32, 34, 34, 36, 36, 38, 38, 40, 60, 62, 62, 64, 64, 66, 66, 68, 28, 30, 30, 32, 22, 24, 40, 42, 42, 44, 50, 52, 52, 54, 54, 56, 18, 20, 14, 16, 16, 18, 0, 70, 56, 58, 58, 60, 44, 46, 46, 48, 48, 50, 24, 26, 26, 28], "width": 24, "height": 87}}, "g3": {"g3": {"type": "mesh", "uvs": [0.25087, 0, 0.09389, 0.07971, 0.03312, 0.21545, 0.0787, 0.36978, 0, 0.44415, 0, 0.56315, 0.01287, 0.66356, 0.08629, 0.69703, 0.15465, 0.63381, 0.24074, 0.7119, 0.35721, 0.74165, 0.39772, 0.83462, 0.52178, 0.86623, 0.53951, 0.94804, 0.57242, 1, 0.68382, 1, 0.74206, 0.92573, 0.82055, 0.85321, 0.80789, 0.7212, 0.87119, 0.61893, 0.86359, 0.5334, 0.95727, 0.55943, 1, 0.52224, 1, 0.41626, 0.99778, 0.31028, 0.87878, 0.2452, 0.79017, 0.23032, 0.73699, 0.12992, 0.64078, 0.01835, 0.42304, 0, 0.29457, 0.13114, 0.35648, 0.2433, 0.41221, 0.35091, 0.48238, 0.49489, 0.54429, 0.63433, 0.60621, 0.73285, 0.65987, 0.84197, 0.62684, 0.93291, 0.59795, 0.32969, 0.76718, 0.37819, 0.88069, 0.4373, 0.22234, 0.40396, 0.11708, 0.52217, 0.06548, 0.61463], "triangles": [11, 10, 35, 36, 35, 18, 12, 11, 35, 36, 18, 17, 36, 12, 35, 16, 36, 17, 37, 12, 36, 37, 36, 16, 13, 12, 37, 14, 13, 37, 15, 37, 16, 14, 37, 15, 33, 20, 34, 34, 20, 19, 9, 8, 33, 33, 10, 9, 18, 34, 19, 35, 34, 18, 34, 10, 33, 10, 34, 35, 42, 3, 41, 4, 3, 42, 5, 4, 42, 43, 5, 42, 33, 8, 42, 33, 42, 41, 43, 42, 8, 6, 5, 43, 7, 43, 8, 6, 43, 7, 38, 27, 26, 39, 38, 26, 39, 26, 25, 39, 25, 24, 40, 39, 24, 23, 40, 24, 40, 23, 22, 20, 39, 40, 20, 40, 22, 21, 20, 22, 39, 33, 38, 20, 33, 39, 31, 28, 27, 38, 31, 27, 32, 31, 38, 3, 2, 31, 41, 3, 31, 32, 41, 31, 33, 32, 38, 41, 32, 33, 30, 0, 29, 1, 0, 30, 2, 1, 30, 31, 30, 29, 2, 30, 31, 28, 31, 29], "vertices": [2, 268, 14.1, 2.73, 0.9971, 272, -30.32, -1.26, 0.0029, 3, 268, 10.49, 10.94, 0.93781, 267, 24.7, 10.09, 0.00315, 272, -23.33, -6.87, 0.05904, 3, 268, 2.5, 15.39, 0.67704, 267, 17.08, 15.15, 0.10802, 272, -14.19, -7.13, 0.21494, 3, 268, -7.6, 15.16, 0.13339, 267, 6.99, 15.71, 0.15205, 272, -5.33, -2.26, 0.71456, 2, 268, -11.57, 19.69, 0.00175, 272, 0.28, -4.45, 0.99825, 1, 272, 7.58, -2.28, 1, 2, 272, 13.57, 0.14, 0.99567, 269, -2.51, -23.56, 0.00433, 3, 267, -13.3, 20.92, 4e-05, 272, 14.64, 4.06, 0.97653, 269, 0.54, -20.88, 0.02343, 4, 267, -10.25, 16.75, 0.0205, 272, 9.84, 5.98, 0.77468, 269, -2.4, -16.63, 0.20339, 270, -10.22, -17.37, 0.00143, 4, 267, -16.15, 14.18, 0.01257, 272, 13.48, 11.28, 0.35239, 269, 3.56, -14.21, 0.59371, 270, -4.44, -14.55, 0.04133, 4, 267, -19.43, 9.41, 0.00073, 272, 13.74, 17.07, 0.11827, 269, 6.98, -9.53, 0.62134, 270, -1.35, -9.65, 0.25965, 3, 272, 18.9, 20.6, 0.01702, 269, 13.23, -9.44, 0.23656, 270, 4.87, -9.13, 0.74643, 3, 272, 19.18, 26.76, 0.00076, 269, 16.86, -4.45, 0.01723, 270, 8.15, -3.9, 0.98201, 1, 270, 13.44, -4.27, 1, 1, 270, 17.03, -3.51, 1, 1, 270, 18.21, 1.59, 1, 2, 271, 19.5, -30.93, 0.00027, 270, 14.19, 5.33, 0.99973, 3, 271, 19.68, -25, 0.00883, 269, 20.15, 9.22, 0.02485, 270, 10.5, 9.97, 0.96632, 3, 271, 14.15, -18.59, 0.11182, 269, 11.89, 11.11, 0.33162, 270, 2.13, 11.29, 0.55657, 4, 267, -18.28, -15.97, 0.00067, 271, 12.62, -11.56, 0.36501, 269, 6.5, 15.86, 0.46154, 270, -3.57, 15.66, 0.17278, 4, 267, -12.9, -17.08, 0.00768, 271, 9.06, -7.39, 0.73086, 269, 1.16, 17.12, 0.22531, 270, -8.99, 16.54, 0.03614, 3, 271, 13.58, -6.09, 0.94178, 269, 4.03, 20.84, 0.05357, 270, -6.37, 20.46, 0.00465, 3, 271, 13.77, -2.98, 0.96672, 269, 2.34, 23.46, 0.03132, 270, -8.24, 22.95, 0.00196, 1, 271, 9.71, 2.45, 1, 2, 268, -12.05, -27.98, 5e-05, 271, 5.57, 7.83, 0.99995, 3, 268, -6.9, -23.27, 0.01816, 267, 4.69, -22.66, 0.03005, 271, -1.41, 7.82, 0.95178, 3, 268, -5.17, -19.36, 0.08615, 267, 6.71, -18.9, 0.1649, 271, -5.31, 6.09, 0.74895, 3, 268, 1.61, -18.13, 0.34389, 267, 13.57, -18.2, 0.27791, 271, -11.16, 9.75, 0.3782, 3, 268, 9.48, -15.04, 0.64857, 267, 21.66, -15.73, 0.17519, 271, -19.05, 12.76, 0.17623, 3, 268, 12.57, -5.21, 0.95099, 267, 25.51, -6.18, 0.02133, 271, -27.96, 7.58, 0.02768, 2, 268, 5.47, 2.3, 0.98931, 272, -22.87, 3.11, 0.01069, 3, 268, -2.13, 0.81, 0.04948, 267, 11.32, 0.97, 0.94167, 272, -16.82, 7.94, 0.00884, 3, 268, -9.39, -0.46, 0.0002, 267, 3.99, 0.27, 0.9966, 272, -10.96, 12.42, 0.0032, 4, 267, -5.77, -0.46, 0.48817, 271, -6.78, -16.13, 0.04239, 272, -3.07, 18.21, 0.02615, 269, -6.42, 0.69, 0.4433, 4, 267, -15.15, -0.9, 0.00012, 271, 0.89, -21.54, 0.00823, 269, 2.97, 0.88, 0.99093, 270, -6.07, 0.46, 0.00072, 3, 271, 7, -24.85, 0.0097, 269, 9.85, 1.83, 0.30768, 270, 0.73, 1.88, 0.68262, 3, 271, 13.2, -28.94, 0.00219, 269, 17.26, 2.21, 0.00397, 270, 8.1, 2.77, 0.99384, 1, 270, 13.42, -0.05, 1, 4, 268, -9.71, -9.29, 0.0163, 267, 2.98, -8.5, 0.59979, 271, -8.75, -4.41, 0.36394, 269, -14.95, 8.97, 0.01997, 5, 268, -14.26, -16.51, 0.00049, 267, -2.12, -15.35, 0.06725, 271, -0.52, -2.14, 0.89785, 269, -9.66, 15.67, 0.03438, 270, -19.69, 14.36, 4e-05, 4, 267, -7.19, -19.49, 0.00296, 271, 6.02, -1.98, 0.96511, 269, -4.49, 19.68, 0.03015, 270, -14.8, 18.71, 0.00177, 4, 268, -11.03, 8.95, 0.04272, 267, 3.09, 9.78, 0.41698, 272, -5.16, 4.83, 0.51838, 269, -15.54, -9.3, 0.02192, 3, 267, -2.9, 16.56, 0.02534, 272, 3.5, 2.25, 0.93771, 269, -9.74, -16.24, 0.03694, 3, 267, -7.96, 20.47, 0.0016, 272, 9.86, 1.61, 0.97569, 269, -4.79, -20.28, 0.02271], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 64, 76, 76, 78, 78, 80, 64, 82, 82, 84, 84, 86], "width": 47, "height": 64}}, "a4": {"a4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.68, -21.98, 113.12, 3.58, 142.33, -9.5, 130.89, -35.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 11, "height": 12}}, "mamian011": {"mamian011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-1.25, -21.23, -16.78, 2.07, 8.18, 18.71, 23.72, -4.59], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 30}}, "sd6": {"sd": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.48, -8.74, -38.3, -8.74, -38.3, 22.83, 35.48, 22.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 201, "height": 86}}, "a11": {"a11": {"type": "mesh", "uvs": [0.00656, 0.0048, 0.07308, 0.00473, 0.1038, 0.01162, 0.13679, 0.06106, 0.14579, 0.12041, 0.15454, 0.18149, 0.16444, 0.25065, 0.17289, 0.30971, 0.18659, 0.36627, 0.21375, 0.42412, 0.25355, 0.47878, 0.29529, 0.52326, 0.33393, 0.55846, 0.397, 0.58253, 0.47942, 0.6092, 0.56956, 0.64138, 0.65374, 0.67836, 0.71764, 0.71111, 0.77709, 0.75176, 0.80643, 0.77999, 0.83635, 0.81744, 0.8676, 0.86805, 0.93217, 0.87515, 0.99248, 0.91237, 0.99232, 0.96123, 0.93646, 0.9953, 0.8404, 0.99525, 0.78713, 0.96023, 0.77735, 0.91336, 0.81262, 0.87707, 0.78159, 0.8394, 0.73841, 0.79299, 0.68387, 0.75501, 0.61227, 0.72264, 0.52612, 0.68702, 0.43907, 0.65604, 0.35882, 0.62551, 0.29032, 0.59697, 0.23977, 0.5673, 0.19504, 0.52722, 0.16018, 0.48086, 0.12643, 0.43239, 0.09629, 0.37593, 0.07607, 0.31151, 0.06463, 0.23714, 0.05777, 0.17966, 0.05126, 0.12498, 0.04134, 0.0649, 0.00782, 0.01964], "triangles": [47, 1, 2, 47, 48, 1, 48, 0, 1, 42, 43, 7, 43, 6, 7, 43, 44, 6, 44, 5, 6, 44, 45, 5, 45, 4, 5, 45, 46, 4, 46, 3, 4, 46, 47, 3, 47, 2, 3, 38, 11, 12, 38, 39, 11, 39, 10, 11, 39, 40, 10, 40, 9, 10, 40, 41, 9, 41, 8, 9, 41, 42, 8, 42, 7, 8, 34, 15, 16, 34, 35, 15, 35, 36, 14, 35, 14, 15, 14, 36, 13, 36, 37, 13, 13, 37, 12, 37, 38, 12, 31, 18, 19, 31, 32, 18, 32, 17, 18, 32, 33, 17, 33, 16, 17, 33, 34, 16, 23, 24, 25, 22, 26, 27, 25, 26, 22, 22, 27, 21, 29, 21, 28, 23, 25, 22, 21, 27, 28, 29, 30, 21, 30, 20, 21, 30, 19, 20, 30, 31, 19], "vertices": [3, 21, -4.58, -7.22, 0.30235, 14, 83.05, -31.47, 0.59365, 40, 41.8, -21.57, 0.104, 4, 22, -67.33, 21.95, 2e-05, 21, -3.61, 1.11, 0.30234, 14, 85.02, -39.62, 0.59365, 40, 43.77, -29.72, 0.104, 4, 22, -64.48, 24.93, 0.00128, 21, -1.75, 4.78, 0.39449, 14, 84.55, -43.71, 0.50024, 40, 43.3, -33.81, 0.104, 4, 22, -53.53, 24.66, 0.00862, 21, 8.8, 7.72, 0.53464, 14, 75.66, -50.12, 0.35275, 40, 34.41, -40.22, 0.104, 4, 22, -41.93, 20.81, 0.03405, 21, 21.02, 7.41, 0.65669, 14, 64.09, -54.06, 0.20526, 40, 22.84, -44.16, 0.104, 5, 23, -54.04, 57.46, 5e-05, 22, -30.02, 16.79, 0.09444, 21, 33.59, 7.03, 0.70564, 14, 52.17, -58.05, 0.09587, 40, 10.92, -48.15, 0.104, 5, 23, -45.5, 46.08, 0.00148, 22, -16.54, 12.24, 0.2019, 21, 47.81, 6.59, 0.65821, 14, 38.68, -62.57, 0.03441, 40, -2.57, -52.67, 0.104, 5, 23, -38.21, 36.35, 0.00934, 22, -5.02, 8.35, 0.34704, 21, 59.96, 6.22, 0.53102, 14, 27.15, -66.43, 0.0086, 40, -14.1, -56.53, 0.104, 5, 23, -30.62, 27.42, 0.03579, 22, 6.29, 5.28, 0.49275, 21, 71.68, 6.57, 0.36624, 14, 16.28, -70.81, 0.00123, 40, -24.97, -60.91, 0.104, 4, 23, -21.46, 19.15, 0.09765, 22, 18.53, 3.65, 0.58601, 21, 83.86, 8.57, 0.21235, 40, -35.71, -67, 0.104, 5, 24, -58.42, -1.03, 0.00123, 23, -11.28, 12.28, 0.20661, 22, 30.8, 3.74, 0.58786, 21, 95.58, 12.23, 0.10031, 40, -45.43, -74.49, 0.104, 5, 24, -48.25, -3.76, 0.0086, 23, -2, 7.31, 0.35282, 22, 41.27, 4.89, 0.49758, 21, 105.25, 16.38, 0.037, 40, -53.07, -81.73, 0.104, 5, 24, -39.71, -5.42, 0.03441, 23, 5.94, 3.75, 0.49868, 22, 49.83, 6.45, 0.35317, 21, 112.99, 20.36, 0.00974, 40, -58.95, -88.15, 0.104, 6, 25, -60.83, -29.4, 0.00014, 24, -30.6, -3.29, 0.09573, 23, 15.29, 3.75, 0.59109, 22, 57.54, 11.75, 0.20747, 21, 118.83, 27.67, 0.00157, 40, -61.9, -97.03, 0.104, 6, 25, -51.87, -21.82, 0.00207, 24, -19.39, 0.19, 0.20442, 23, 27, 4.59, 0.59109, 22, 66.72, 19.06, 0.09837, 21, 125.48, 37.34, 6e-05, 40, -64.8, -108.4, 0.104, 5, 25, -41.5, -13.76, 0.01154, 24, -6.69, 3.55, 0.34981, 23, 40.13, 4.98, 0.49847, 22, 77.32, 26.82, 0.03618, 40, -68.56, -120.98, 0.104, 5, 25, -30.5, -6.76, 0.0414, 24, 6.17, 5.69, 0.49325, 23, 53.14, 4.14, 0.35186, 22, 88.53, 33.48, 0.00949, 40, -73.46, -133.06, 0.104, 5, 25, -21.26, -1.81, 0.10844, 24, 16.61, 6.64, 0.58108, 23, 63.52, 2.69, 0.20497, 22, 97.91, 38.16, 0.00152, 40, -78.11, -142.46, 0.104, 5, 25, -10.72, 2.01, 0.22408, 24, 27.8, 6.04, 0.57605, 23, 74.28, -0.44, 0.09581, 22, 108.55, 41.67, 6e-05, 40, -84.47, -151.69, 0.104, 4, 25, -3.98, 3.27, 0.38104, 24, 34.5, 4.56, 0.48055, 23, 80.48, -3.4, 0.03441, 40, -89.24, -156.63, 0.104, 4, 25, 4.56, 3.89, 0.55225, 24, 42.6, 1.8, 0.33515, 23, 87.73, -7.93, 0.0086, 40, -95.82, -162.09, 0.104, 4, 25, 15.65, 3.66, 0.70208, 24, 52.72, -2.75, 0.19269, 23, 96.55, -14.67, 0.00123, 40, -104.99, -168.33, 0.104, 3, 25, 20.05, 10.65, 0.80711, 24, 59.5, 1.97, 0.08889, 40, -104.51, -176.58, 0.104, 3, 25, 29.97, 14.84, 0.86452, 24, 70.27, 1.95, 0.03148, 40, -110.16, -185.75, 0.104, 3, 25, 39.25, 11.07, 0.88823, 24, 77.34, -5.15, 0.00778, 40, -119.91, -188.07, 0.104, 3, 25, 43.09, 1.93, 0.89363, 24, 77.3, -15.06, 0.00237, 40, -128.34, -182.85, 0.104, 3, 25, 38.54, -9.29, 0.88724, 24, 68.74, -23.61, 0.00876, 40, -131.15, -171.08, 0.104, 3, 25, 29.37, -12.82, 0.86125, 24, 58.91, -23.28, 0.03475, 40, -125.73, -162.88, 0.104, 4, 25, 20, -10.37, 0.79961, 24, 51.25, -17.36, 0.09522, 23, 91.79, -28.56, 0.00117, 40, -116.68, -159.44, 0.104, 4, 25, 14.77, -3.46, 0.6889, 24, 49.13, -8.96, 0.19883, 23, 91.64, -19.9, 0.00827, 40, -108.41, -162.03, 0.104, 4, 25, 6.14, -4.19, 0.53413, 24, 40.9, -6.26, 0.32864, 23, 84.25, -15.4, 0.03324, 40, -101.81, -156.43, 0.104, 4, 25, -4.72, -5.67, 0.36106, 24, 30.33, -3.38, 0.44188, 23, 74.6, -10.19, 0.09306, 40, -93.83, -148.92, 0.104, 5, 25, -14.51, -9.13, 0.20644, 24, 19.96, -2.74, 0.48813, 23, 64.66, -7.2, 0.20051, 22, 104.44, 30.65, 0.00092, 40, -87.86, -140.42, 0.104, 5, 25, -24.04, -15.01, 0.09597, 24, 8.89, -4.42, 0.44575, 23, 53.49, -6.33, 0.34753, 22, 94.74, 25.05, 0.00675, 40, -83.51, -130.1, 0.104, 5, 25, -34.88, -22.34, 0.03453, 24, -3.95, -6.93, 0.33473, 23, 40.42, -5.85, 0.49881, 22, 83.69, 18.04, 0.02793, 40, -78.94, -117.84, 0.104, 5, 25, -44.87, -30.13, 0.00868, 24, -16.19, -10.2, 0.20488, 23, 27.75, -6.25, 0.60202, 22, 73.47, 10.55, 0.08042, 40, -75.32, -105.69, 0.104, 6, 25, -54.46, -37.16, 0.00126, 24, -27.77, -12.92, 0.09966, 23, 15.86, -6.27, 0.6164, 22, 63.68, 3.8, 0.17862, 21, 127.01, 21.85, 6e-05, 40, -71.59, -94.4, 0.104, 6, 25, -63.12, -42.97, 1e-05, 24, -38.01, -14.89, 0.03721, 23, 5.44, -5.85, 0.53636, 22, 54.85, -1.75, 0.32083, 21, 120.18, 13.97, 0.00159, 40, -67.91, -84.64, 0.104, 5, 24, -46.81, -15.09, 0.00978, 23, -3.18, -4.05, 0.39513, 22, 46.72, -5.14, 0.48123, 21, 113.39, 8.36, 0.00985, 40, -63.48, -77.03, 0.104, 5, 24, -56.61, -13.27, 0.00157, 23, -12.3, -0.05, 0.24385, 22, 36.94, -7, 0.6132, 21, 104.57, 3.74, 0.03739, 40, -56.81, -69.64, 0.104, 5, 24, -66.43, -9.65, 6e-05, 23, -21.05, 5.71, 0.12362, 22, 26.47, -7.21, 0.67111, 21, 94.61, 0.5, 0.10122, 40, -48.59, -63.15, 0.104, 4, 23, -29.91, 11.9, 0.04986, 22, 15.66, -7.11, 0.63216, 21, 84.24, -2.56, 0.21397, 40, -39.92, -56.7, 0.104, 5, 23, -39.24, 19.73, 0.01509, 22, 3.54, -5.94, 0.51118, 21, 72.3, -4.96, 0.3685, 14, 11.7, -60.21, 0.00123, 40, -29.55, -50.31, 0.104, 5, 23, -48.37, 29.6, 0.00308, 22, -9.58, -2.97, 0.35078, 21, 58.89, -5.93, 0.53354, 14, 23.95, -54.65, 0.0086, 40, -17.3, -44.75, 0.104, 5, 23, -57.65, 41.79, 0.00031, 22, -24.12, 1.84, 0.20083, 21, 43.58, -5.57, 0.66045, 14, 38.44, -49.69, 0.03441, 40, -2.81, -39.79, 0.104, 4, 22, -35.26, 5.78, 0.0929, 21, 31.77, -5.04, 0.70724, 14, 49.7, -46.1, 0.09587, 40, 8.45, -36.2, 0.104, 4, 22, -45.86, 9.53, 0.03317, 21, 20.55, -4.53, 0.65758, 14, 60.41, -42.69, 0.20526, 40, 19.16, -32.79, 0.104, 4, 22, -57.64, 13.33, 0.00825, 21, 8.17, -4.32, 0.53501, 14, 72.09, -38.6, 0.35275, 40, 30.84, -28.71, 0.104, 4, 22, -67.83, 13.19, 0.00117, 21, -1.54, -7.42, 0.39459, 14, 80.13, -32.34, 0.50024, 40, 38.88, -22.44, 0.104], "hull": 49, "edges": [0, 96, 0, 2, 2, 4, 4, 6, 30, 32, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 74, 76, 94, 96, 56, 58, 58, 60, 60, 62, 40, 42, 32, 34, 34, 36, 66, 68, 68, 70, 70, 72, 72, 74, 24, 26, 26, 28, 28, 30, 76, 78, 78, 80, 80, 82, 16, 18, 18, 20, 14, 16, 12, 14, 10, 12, 6, 8, 8, 10, 92, 94, 90, 92, 86, 88, 88, 90, 82, 84, 84, 86, 62, 64, 64, 66, 36, 38, 38, 40, 20, 22, 22, 24], "width": 50, "height": 82}}, "a0": {"a0": {"type": "mesh", "uvs": [0.5061, 0, 0.63331, 0, 0.74917, 0.59728, 0.98684, 0.55889, 0.986, 0.62885, 0.90841, 0.78099, 0.73492, 0.91893, 0.31976, 0.97851, 0.2223, 0.91162, 0.1384, 0.8781, 0.07933, 0.92827, 0.03609, 0.843, 0.00825, 0.68109, 0.02887, 0.59889, 0.09467, 0.51371, 0.15956, 0.514, 0.32759, 0.67873, 0.41199, 0.3538, 0.44374, 0.12006, 0.09073, 0.71633], "triangles": [12, 13, 19, 11, 12, 19, 10, 11, 19, 3, 5, 2, 19, 13, 14, 4, 5, 3, 15, 19, 14, 8, 9, 15, 9, 19, 15, 16, 8, 15, 1, 18, 0, 6, 2, 5, 9, 10, 19, 7, 8, 16, 1, 2, 18, 2, 17, 18, 2, 16, 17, 6, 7, 16, 2, 6, 16], "vertices": [1, 45, 27.01, 28.47, 1, 1, 45, 35.75, 29.25, 1, 1, 45, 45.59, 9.15, 1, 1, 45, 61.8, 11.95, 1, 1, 45, 61.96, 9.51, 1, 1, 45, 57.1, 3.72, 1, 1, 45, 45.61, -2.16, 1, 1, 45, 17.27, -6.79, 1, 2, 45, 10.36, -5.06, 0.99949, 44, 49.47, -10.19, 0.00051, 2, 45, 4.49, -4.41, 0.82014, 44, 45.11, -6.21, 0.17986, 2, 45, 0.59, -6.52, 0.32857, 44, 40.71, -5.62, 0.67143, 2, 45, -2.65, -3.82, 0.03812, 44, 39.69, -1.52, 0.96188, 2, 45, -5.07, 1.65, 0.06666, 44, 40.96, 4.32, 0.93334, 2, 45, -3.91, 4.65, 0.35879, 44, 43.66, 6.06, 0.64121, 2, 45, 0.34, 8.02, 0.86604, 44, 49.09, 6.28, 0.13396, 2, 45, 4.8, 8.41, 0.99111, 44, 52.92, 3.97, 0.00889, 1, 45, 16.87, 3.71, 1, 1, 45, 21.65, 15.55, 1, 1, 45, 23.1, 23.9, 1, 2, 45, 0.71, 0.93, 0.92099, 44, 45.2, 0.34, 0.07901], "hull": 19, "edges": [0, 36, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 24, 38, 16, 18, 18, 20, 0, 2], "width": 27, "height": 14}}, "a1": {"a1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-23.97, -47.91, -28.5, 35.97, 1.45, 37.59, 5.99, -46.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 84, "height": 30}}, "a2": {"a2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [18.61, 2.04, 30.09, 8.14, 36.66, -4.22, 25.18, -10.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 13, "height": 14}}, "a3": {"a3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.93, -15.87, 25.88, -11.65, 31.51, -22.24, 23.56, -26.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 9, "height": 12}}, "yw/wenli_00": {"yw/wenli_00": {"type": "mesh", "uvs": [0.50008, 0.0328, 0.91624, 0.03332, 0.91877, 0.12314, 0.91617, 0.26748, 0.89137, 0.37086, 0.8798, 0.49955, 0.86332, 0.63464, 0.85809, 0.76909, 0.71278, 0.88688, 0.68059, 0.9063, 0.49644, 0.9168, 0.17251, 0.90084, 0.13358, 0.88706, 0.12504, 0.77154, 0.15316, 0.63345, 0.12827, 0.50254, 0.10446, 0.38401, 0.09564, 0.27159, 0.09861, 0.12177, 0.10331, 0.03325, 0.5, 0.5, 0.50029, 0.12244, 0.49861, 0.26957, 0.49849, 0.37743, 0.50026, 0.63403, 0.49698, 0.7703, 0.50611, 0.87233], "triangles": [21, 0, 1, 19, 0, 21, 18, 19, 21, 21, 1, 2, 3, 21, 2, 22, 18, 21, 22, 21, 3, 17, 18, 22, 4, 22, 3, 22, 16, 17, 23, 22, 4, 23, 16, 22, 5, 23, 4, 20, 23, 5, 15, 16, 23, 20, 15, 23, 14, 15, 20, 5, 6, 20, 6, 24, 20, 14, 20, 24, 7, 24, 6, 25, 14, 24, 25, 24, 7, 13, 14, 25, 8, 26, 25, 7, 8, 25, 12, 13, 25, 12, 25, 26, 11, 12, 26, 9, 26, 8, 10, 11, 26, 10, 26, 9], "vertices": [2, 52, 1.97, 18, 0.00039, 53, 1.39, 6.71, 0.99961, 4, 50, 91.69, 38.16, 8e-05, 51, 60.09, 28.16, 0.00475, 52, 46.71, 17.97, 0.14259, 53, 25.75, 6.68, 0.85259, 4, 50, 87.18, 32.41, 0.00028, 51, 57.13, 22.41, 0.01754, 52, 44.41, 12.22, 0.21856, 53, 24.5, 0.93, 0.76363, 4, 50, 65.88, 23.18, 0.01977, 51, 43.22, 13.18, 0.12977, 52, 33.56, 2.98, 0.55017, 53, 18.59, -8.31, 0.30028, 5, 49, 72.82, 26.86, 0.00298, 50, 48.76, 16.56, 0.0763, 51, 32.04, 6.56, 0.34429, 52, 24.84, -3.64, 0.49303, 53, 13.84, -14.92, 0.0834, 6, 48, 112.23, -28.37, 0.00097, 49, 53.44, 18.57, 0.04235, 50, 35.78, 8.28, 0.26587, 51, 23.56, -1.72, 0.52049, 52, 18.23, -11.92, 0.16214, 53, 10.25, -23.21, 0.00818, 5, 48, 79.74, -18.78, 0.02521, 49, 37.84, 8.98, 0.17582, 50, 25.34, -1.32, 0.60163, 51, 16.74, -11.32, 0.16557, 52, 12.91, -21.51, 0.03177, 5, 48, 55.38, -7.83, 0.14932, 49, 26.15, -1.98, 0.56964, 50, 17.51, -12.27, 0.23936, 51, 11.63, -22.27, 0.03984, 52, 8.92, -32.47, 0.00185, 4, 48, 23.56, -0.17, 0.62044, 49, 10.88, -9.63, 0.34107, 50, 7.28, -19.93, 0.03647, 51, 4.95, -29.93, 0.00202, 4, 48, 19.98, 0.32, 0.77898, 49, 9.16, -10.12, 0.18766, 50, 6.13, -20.42, 0.03161, 51, 4.2, -30.42, 0.00175, 1, 48, 0.81, 0.18, 1, 4, 48, -31.42, 0.26, 0.70409, 49, -15.51, -10.06, 0.25238, 50, -10.39, -20.36, 0.04175, 51, -6.6, -30.36, 0.00178, 4, 48, -35.13, -0.07, 0.5841, 49, -17.29, -9.73, 0.36679, 50, -11.58, -20.03, 0.0471, 51, -7.38, -30.03, 0.00201, 5, 48, -51.8, -7.52, 0.15917, 49, -25.29, -2.29, 0.5829, 50, -16.94, -12.58, 0.22357, 51, -10.88, -22.58, 0.03433, 52, -8.63, -32.78, 3e-05, 5, 48, -69.99, -19.16, 0.01741, 49, -34.02, 9.36, 0.17621, 50, -22.78, -0.94, 0.61941, 51, -14.69, -10.94, 0.17115, 52, -11.61, -21.13, 0.01582, 6, 48, -101.83, -28.21, 0.00041, 49, -49.31, 18.41, 0.03948, 50, -33.02, 8.12, 0.25559, 51, -21.38, -1.88, 0.57124, 52, -16.82, -12.08, 0.1253, 53, -8.84, -23.37, 0.00799, 5, 49, -68.65, 26.01, 0.00289, 50, -45.97, 15.72, 0.0685, 51, -29.84, 5.72, 0.39957, 52, -23.42, -4.48, 0.44909, 53, -12.43, -15.76, 0.07995, 4, 50, -62.03, 22.91, 0.00861, 51, -40.33, 12.91, 0.13824, 52, -31.6, 2.72, 0.52126, 53, -16.88, -8.57, 0.33189, 3, 51, -52.86, 22.5, 0.01435, 52, -41.37, 12.31, 0.13318, 53, -22.2, 1.02, 0.85246, 3, 51, -54.91, 28.17, 0.00392, 52, -42.97, 17.97, 0.062, 53, -23.07, 6.69, 0.93408, 2, 50, 0.9, 8.29, 0.14474, 51, 0.78, -1.7, 0.85526, 1, 53, 1.41, 0.98, 1, 2, 52, 0.78, 2.85, 0.80216, 53, 0.74, -8.44, 0.19784, 2, 51, 0.8, 6.14, 0.38318, 52, 0.48, -4.06, 0.61682, 3, 49, 0.94, 10.01, 0.02153, 50, 0.63, -0.28, 0.97842, 52, 0.32, -20.48, 6e-05, 2, 49, 0.44, 1.29, 0.90118, 50, 0.3, -9, 0.09882, 2, 48, 2.37, -1.11, 0.68895, 49, 0.71, -8.69, 0.31105], "hull": 20, "edges": [42, 44, 44, 46, 46, 40, 40, 48, 48, 50, 50, 52, 4, 42, 6, 44, 8, 46, 12, 48, 14, 50, 16, 52, 18, 20, 36, 42, 34, 44, 32, 46, 28, 48, 26, 50, 24, 52, 20, 22, 24, 22, 16, 18, 14, 16, 4, 6, 34, 36, 24, 26, 34, 32, 32, 30, 30, 28, 28, 26, 6, 8, 8, 10, 10, 12, 12, 14, 36, 38, 0, 42, 38, 0, 4, 2, 0, 2], "width": 32, "height": 32}, "yw/wenli_01": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_02": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_03": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_04": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_05": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_06": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_07": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_08": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_09": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_10": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_11": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_12": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_13": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_14": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_15": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_16": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_17": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_18": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}, "yw/wenli_19": {"type": "<PERSON><PERSON><PERSON>", "parent": "yw/wenli_00", "width": 32, "height": 32}}, "a5": {"a5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-105.07, 8.11, -67.48, 92.09, 142.45, -1.88, 104.86, -85.85], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 36, "height": 92}}, "a20": {"a20": {"type": "mesh", "uvs": [0.91086, 0.00432, 0.99933, 0.00385, 0.94923, 0.13931, 0.92291, 0.2772, 0.90617, 0.40088, 0.89537, 0.51629, 0.87247, 0.63741, 0.84944, 0.72569, 0.80254, 0.82356, 0.73881, 0.89565, 0.65689, 0.94192, 0.54845, 0.96911, 0.42621, 0.9873, 0.29767, 0.99682, 0.1653, 1, 0.03031, 0.99572, 0.00603, 0.99128, 0.0078, 0.96959, 0.05284, 0.91793, 0.13612, 0.87043, 0.24605, 0.84234, 0.36237, 0.81479, 0.47353, 0.77725, 0.56115, 0.73489, 0.58766, 0.66631, 0.60456, 0.58114, 0.63214, 0.47887, 0.66494, 0.3679, 0.73108, 0.24142, 0.80244, 0.13919, 0.87137, 0.04455], "triangles": [19, 13, 14, 14, 15, 18, 14, 18, 19, 19, 20, 13, 16, 17, 15, 15, 17, 18, 13, 20, 12, 20, 21, 12, 12, 21, 11, 21, 22, 11, 11, 22, 10, 10, 22, 9, 22, 23, 9, 9, 23, 8, 8, 23, 7, 23, 24, 7, 7, 24, 6, 24, 25, 6, 6, 25, 5, 25, 26, 5, 5, 26, 4, 26, 27, 4, 4, 27, 3, 27, 28, 3, 28, 29, 3, 3, 29, 2, 29, 30, 2, 2, 30, 1, 1, 30, 0], "vertices": [3, 89, -17.61, -1.03, 0.95817, 90, -83.53, 2.53, 0.04175, 91, -139.88, -0.75, 8e-05, 3, 89, -20.47, 10.11, 0.92317, 90, -85.91, 13.78, 0.07367, 91, -142.52, 10.44, 0.00316, 4, 89, 11.34, 11.24, 0.81638, 90, -54.08, 13.56, 0.16461, 91, -110.69, 10.97, 0.019, 105, -132.54, -103.89, 0, 4, 89, 42.95, 15.51, 0.6445, 90, -22.31, 16.47, 0.28753, 91, -79.01, 14.63, 0.0673, 105, -112.72, -78.89, 0.00067, 4, 89, 71.09, 20.2, 0.44004, 90, 6, 19.96, 0.38856, 91, -50.78, 18.78, 0.16576, 105, -95.7, -55.99, 0.00565, 4, 89, 97.2, 25.19, 0.24966, 90, 32.3, 23.83, 0.41482, 91, -24.58, 23.27, 0.3105, 105, -80.36, -34.29, 0.02502, 5, 89, 124.97, 28.96, 0.11299, 90, 60.2, 26.43, 0.35128, 91, 3.25, 26.51, 0.45976, 105, -62.98, -12.32, 0.07597, 106, -90.17, -41.84, 1e-05, 5, 89, 145.4, 30.91, 0.03801, 90, 80.69, 27.5, 0.23443, 91, 23.71, 28.07, 0.55224, 105, -49.61, 3.26, 0.17483, 106, -82.2, -22.92, 0.0005, 5, 89, 168.71, 30.38, 0.00856, 90, 103.96, 25.98, 0.12064, 91, 47.01, 27.09, 0.54433, 105, -32.45, 19.05, 0.32124, 106, -70.67, -2.65, 0.00523, 5, 89, 186.79, 26.3, 0.00089, 90, 121.85, 21.13, 0.04584, 91, 65.01, 22.66, 0.44326, 105, -16.59, 28.65, 0.48474, 106, -58.5, 11.33, 0.02526, 4, 90, 134.39, 12.8, 0.01182, 91, 77.74, 14.62, 0.29771, 105, -1.91, 31.97, 0.60971, 106, -45.53, 18.98, 0.08076, 4, 90, 143.31, 0.22, 0.00168, 91, 86.95, 2.25, 0.16398, 105, 13.35, 29.74, 0.64314, 106, -30.32, 21.51, 0.1912, 4, 90, 150.55, -14.53, 6e-05, 91, 94.54, -12.32, 0.07289, 105, 29.03, 24.8, 0.56805, 106, -13.88, 21.59, 0.35901, 3, 91, 100.36, -28.14, 0.02549, 105, 44.33, 17.73, 0.41668, 106, 2.84, 19.53, 0.55782, 3, 91, 104.87, -44.76, 0.00665, 105, 59.27, 9.17, 0.24964, 106, 19.69, 15.94, 0.74371, 3, 91, 107.78, -62.09, 0.00117, 105, 73.59, -1.02, 0.12163, 106, 36.43, 10.6, 0.8772, 3, 91, 107.48, -65.4, 0.0001, 105, 75.71, -3.57, 0.06074, 106, 39.23, 8.83, 0.93916, 3, 91, 102.57, -66.27, 0.00102, 105, 72.86, -7.67, 0.07487, 106, 37.76, 4.05, 0.92411, 3, 91, 89.69, -63.18, 0.00897, 105, 61.57, -14.59, 0.15869, 106, 29.13, -5.99, 0.83234, 3, 91, 76.64, -55.04, 0.03877, 105, 46.58, -18.06, 0.28907, 106, 15.91, -13.86, 0.67216, 4, 90, 122.5, -44.09, 0.00275, 91, 67.19, -42.52, 0.11064, 105, 31.05, -15.9, 0.41388, 106, 0.46, -16.55, 0.47273, 4, 90, 113.3, -30.51, 0.01788, 91, 57.67, -29.17, 0.23068, 105, 14.88, -13.19, 0.47188, 106, -15.76, -18.9, 0.27956, 5, 89, 168.6, -13.7, 0.00065, 90, 101.98, -18.06, 0.06534, 91, 46.06, -16.98, 0.37032, 105, -1.95, -12.78, 0.43017, 106, -31.91, -23.65, 0.13352, 5, 89, 156.42, -4.97, 0.00737, 90, 90.18, -8.82, 0.16165, 91, 34.04, -8.02, 0.46965, 105, -16.78, -14.95, 0.31275, 106, -45.38, -30.24, 0.04858, 5, 89, 140.28, -5.4, 0.03445, 90, 74.03, -8.56, 0.29933, 91, 17.9, -8.14, 0.47624, 105, -28.11, -26.45, 0.17778, 106, -52.66, -44.65, 0.0122, 5, 89, 120.73, -7.95, 0.10593, 90, 54.4, -10.28, 0.42955, 91, -1.69, -10.32, 0.38628, 105, -40.42, -41.85, 0.07658, 106, -59.68, -63.07, 0.00166, 5, 89, 97.03, -10.1, 0.23915, 90, 30.63, -11.41, 0.48969, 91, -25.42, -12.01, 0.24802, 105, -56, -59.83, 0.02313, 106, -69.03, -84.95, 1e-05, 4, 89, 71.23, -12.06, 0.42824, 90, 4.77, -12.28, 0.44449, 91, -51.26, -13.48, 0.123, 105, -73.23, -79.14, 0.00428, 4, 89, 40.93, -10.67, 0.63421, 90, -25.45, -9.6, 0.32067, 91, -81.53, -11.51, 0.04488, 105, -96.02, -99.16, 0.00024, 3, 89, 15.88, -7.29, 0.80963, 90, -50.33, -5.15, 0.17945, 91, -106.51, -7.65, 0.01092, 3, 89, -7.4, -3.8, 0.92006, 90, -73.44, -0.67, 0.07858, 91, -129.72, -3.71, 0.00136], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 10, 12, 16, 18, 18, 20, 20, 22, 30, 32, 32, 34, 34, 36, 36, 38, 54, 56, 26, 28, 28, 30, 42, 44, 44, 46, 50, 52, 52, 54, 56, 58, 58, 60, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 22, 24, 24, 26, 38, 40, 40, 42, 46, 48, 48, 50], "width": 52, "height": 92}}, "a7": {"a7": {"type": "mesh", "uvs": [0.37113, 0.00282, 0.4919, 0.02771, 0.60389, 0.02313, 0.77809, 0.10356, 0.90228, 0.1609, 0.97754, 0.19979, 0.99318, 0.2335, 1, 0.31667, 0.98879, 0.41256, 0.97063, 0.49885, 0.94449, 0.59225, 0.90299, 0.69131, 0.83574, 0.77282, 0.76429, 0.83474, 0.6892, 0.88179, 0.61778, 0.91721, 0.52575, 0.94908, 0.43534, 0.97153, 0.34659, 0.98801, 0.25308, 0.99732, 0.16294, 1, 0.06781, 0.99517, 0.02671, 0.97922, 0.00687, 0.94937, 0.00686, 0.87753, 0.01802, 0.8171, 0.04785, 0.75109, 0.10438, 0.68069, 0.17956, 0.6068, 0.25828, 0.53172, 0.32301, 0.45264, 0.37713, 0.33999, 0.41176, 0.22926, 0.40571, 0.13987, 0.28652, 0.05274, 0.33216, 0.03605, 0.35445, 0.01441, 0.56402, 0.13199, 0.61862, 0.22295, 0.65507, 0.33557, 0.66892, 0.44608, 0.66298, 0.5435, 0.63923, 0.6162, 0.58382, 0.69618, 0.51454, 0.76597, 0.42944, 0.83432, 0.32324, 0.89788, 0.22824, 0.94004, 0.12335, 0.97058], "triangles": [5, 6, 4, 21, 48, 20, 19, 20, 47, 20, 48, 47, 21, 22, 48, 22, 23, 48, 47, 48, 24, 48, 23, 24, 47, 24, 25, 47, 25, 26, 19, 47, 18, 47, 46, 18, 18, 46, 17, 16, 17, 45, 17, 46, 45, 16, 45, 15, 26, 46, 47, 45, 46, 27, 46, 26, 27, 27, 28, 45, 9, 40, 8, 45, 44, 15, 15, 44, 14, 14, 44, 13, 44, 43, 13, 13, 43, 12, 45, 28, 44, 43, 42, 12, 12, 42, 11, 28, 29, 44, 44, 29, 43, 43, 29, 42, 11, 42, 10, 10, 42, 41, 29, 30, 42, 42, 30, 41, 10, 41, 9, 41, 30, 40, 41, 40, 9, 40, 30, 31, 40, 31, 39, 31, 32, 39, 8, 40, 7, 4, 6, 39, 39, 7, 40, 7, 39, 6, 4, 38, 3, 34, 35, 33, 33, 37, 38, 33, 35, 1, 33, 1, 37, 1, 36, 0, 37, 2, 3, 37, 1, 2, 38, 37, 3, 32, 33, 38, 32, 38, 39, 4, 39, 38, 1, 35, 36], "vertices": [2, 60, 50.74, -7.15, 0.696, 61, 2.97, -6.77, 0.304, 1, 60, 32.71, -8.25, 1, 1, 60, 17.78, -14.39, 1, 5, 60, -11.09, -7.73, 0.62059, 61, -58.04, -16.83, 0.01597, 63, -25.07, 17.31, 0.10187, 64, -82.04, -29.43, 0.01385, 59, 56.24, 10.42, 0.24773, 5, 60, -31.67, -2.98, 0.1706, 61, -79.11, -15.29, 0.00553, 63, -14.93, 35.84, 0.26012, 64, -82.86, -8.33, 0.04914, 59, 61.32, 30.92, 0.51461, 4, 60, -44.41, 0.66, 0.05859, 63, -7.98, 47.12, 0.3372, 64, -82.69, 4.92, 0.09322, 59, 65.17, 43.6, 0.51099, 4, 60, -48.71, 6.16, 0.01156, 63, -1.52, 49.77, 0.37788, 64, -78.51, 10.51, 0.17048, 59, 70.75, 47.8, 0.44008, 4, 63, 14.7, 51.73, 0.36755, 64, -65.61, 20.52, 0.29253, 65, -122.49, -13.86, 1e-05, 59, 85.93, 53.83, 0.33991, 4, 63, 33.55, 51.25, 0.30921, 64, -49.19, 29.81, 0.45543, 65, -109.27, -0.4, 0.00038, 59, 104.28, 58.17, 0.23499, 4, 63, 50.59, 49.66, 0.22092, 64, -33.75, 37.2, 0.63204, 65, -96.48, 10.97, 0.00277, 59, 121.17, 60.97, 0.14427, 4, 63, 69.09, 47.01, 0.13015, 64, -16.52, 44.43, 0.78148, 65, -81.92, 22.68, 0.01205, 59, 139.73, 63.12, 0.07632, 4, 63, 88.83, 42.21, 0.06069, 64, 2.88, 50.46, 0.86792, 65, -64.94, 33.83, 0.03744, 59, 160.04, 63.51, 0.03395, 4, 63, 105.36, 33.51, 0.02097, 64, 21.53, 51.49, 0.87488, 65, -47.3, 39.98, 0.09184, 59, 178.24, 59.31, 0.01231, 5, 63, 118.1, 23.97, 0.00481, 64, 37.36, 49.85, 0.80505, 65, -31.63, 42.78, 0.18642, 66, -76.81, 31.27, 0.00025, 59, 192.99, 53.33, 0.00346, 5, 63, 127.96, 13.73, 0.00053, 64, 51.08, 46.13, 0.67396, 65, -17.42, 42.99, 0.32198, 66, -62.8, 33.68, 0.00285, 59, 205.13, 45.94, 0.00067, 4, 64, 62.61, 41.57, 0.5064, 65, -5.07, 41.79, 0.47688, 66, -50.42, 34.4, 0.01664, 59, 214.94, 38.34, 7e-05, 4, 64, 75.25, 34.16, 0.33384, 65, 9.12, 38.16, 0.60502, 66, -35.83, 33.02, 0.06114, 59, 225.01, 27.7, 0, 3, 64, 86.24, 25.9, 0.18768, 65, 21.96, 33.26, 0.65154, 66, -22.39, 30.16, 0.16078, 3, 64, 96.12, 17.17, 0.08671, 65, 33.87, 27.61, 0.58783, 66, -9.75, 26.41, 0.32546, 3, 64, 105.23, 7.09, 0.03141, 65, 45.41, 20.43, 0.43642, 66, 2.76, 21.11, 0.53216, 3, 64, 112.99, -3.33, 0.00827, 65, 55.75, 12.57, 0.26093, 66, 14.19, 14.94, 0.7308, 3, 64, 119.94, -15.17, 0.00165, 65, 65.7, 3.11, 0.12794, 66, 25.49, 7.14, 0.8704, 4, 63, 152.77, -80.34, 1e-05, 64, 120.71, -21.82, 0.00203, 65, 68.27, -3.07, 0.0738, 66, 28.99, 1.43, 0.92417, 4, 63, 147.1, -83.55, 0.00023, 64, 117.49, -27.48, 0.00895, 65, 66.75, -9.4, 0.10219, 66, 28.46, -5.06, 0.88863, 4, 63, 133.05, -84.4, 0.00176, 64, 105.87, -35.43, 0.03057, 65, 57.77, -20.25, 0.19253, 66, 21.28, -17.17, 0.77513, 4, 63, 121.13, -83.51, 0.0089, 64, 95.19, -40.79, 0.07963, 65, 48.99, -28.36, 0.30644, 66, 13.85, -26.54, 0.60504, 5, 60, 46.58, 146.67, 6e-05, 63, 107.95, -80, 0.0327, 64, 82.09, -44.55, 0.16194, 65, 37.44, -35.59, 0.3919, 66, 3.56, -35.47, 0.4134, 6, 60, 43.42, 130.96, 0.00097, 63, 93.69, -72.71, 0.08593, 64, 66.1, -45.63, 0.26696, 65, 22.37, -41.04, 0.40549, 66, -10.48, -43.19, 0.24063, 59, 194.01, -46.38, 2e-05, 6, 60, 37.95, 113.73, 0.00691, 63, 78.58, -62.77, 0.17831, 64, 48.04, -44.87, 0.36124, 65, 4.8, -45.31, 0.33774, 66, -27.18, -50.12, 0.11569, 59, 176.87, -40.62, 0.00012, 7, 60, 32.07, 96.11, 0.02866, 61, -31.31, 92.4, 5e-05, 63, 63.21, -52.34, 0.2924, 64, 29.49, -43.82, 0.41773, 65, -13.31, -49.43, 0.21717, 66, -44.44, -56.99, 0.04358, 59, 159.35, -34.45, 0.0004, 7, 60, 28.35, 78.41, 0.07109, 61, -32.27, 74.34, 0.00714, 63, 47.17, -43.97, 0.4068, 64, 11.43, -44.88, 0.41833, 65, -30.37, -55.43, 0.08671, 66, -60.37, -65.57, 0.00905, 59, 141.71, -30.44, 0.00089, 7, 60, 28.24, 54.99, 0.18133, 61, -28.79, 51.18, 0.04463, 63, 24.67, -37.53, 0.47974, 64, -11.19, -50.92, 0.25463, 65, -50.45, -67.49, 0.03745, 66, -78.33, -80.58, 0.00177, 59, 118.3, -29.94, 0.00045, 7, 60, 30.65, 32.86, 0.26528, 61, -23.02, 29.68, 0.12755, 63, 2.7, -33.86, 0.48014, 64, -31.92, -59.06, 0.11441, 65, -68.12, -81.04, 0.01209, 66, -93.7, -96.7, 0.00018, 59, 96.12, -31.99, 0.00036, 1, 60, 37.22, 16.59, 1, 2, 60, 59.04, 6.09, 0.696, 61, 9.14, 7.59, 0.304, 2, 60, 53.91, 0.85, 0.696, 61, 4.87, 1.61, 0.304, 2, 60, 52.27, -4.21, 0.696, 61, 4.03, -3.64, 0.304, 1, 60, 16.2, 7.65, 1, 1, 60, 2.92, 21.91, 1, 7, 60, -9.28, 41.04, 0.20755, 61, -63.73, 31.64, 0.03588, 63, 21.39, 2.37, 0.55976, 64, -34.51, -18.38, 0.19141, 65, -81.85, -42.65, 0.00199, 66, -113.2, -60.9, 0, 59, 104.97, 7.81, 0.00341, 7, 60, -18.28, 60.84, 0.08535, 61, -75.66, 49.83, 0.00714, 63, 42.89, 5.67, 0.52734, 64, -17.76, -4.5, 0.36709, 65, -69.59, -24.69, 0.00988, 66, -103.86, -41.26, 0.00013, 59, 124.92, 16.47, 0.00307, 7, 60, -23.74, 79.16, 0.03582, 61, -83.86, 67.1, 8e-05, 63, 62, 5.97, 0.38182, 64, -1.51, 5.58, 0.54054, 65, -56.76, -10.52, 0.03849, 66, -93.38, -25.27, 0.0013, 59, 143.32, 21.63, 0.00195, 6, 60, -25.18, 93.75, 0.00606, 63, 76.43, 3.41, 0.19747, 64, 12.18, 10.8, 0.71988, 65, -45.04, -1.71, 0.07488, 66, -83.17, -14.76, 0.00045, 59, 157.93, 22.83, 0.00125, 6, 60, -22.79, 111.17, 0.00071, 63, 92.56, -3.61, 0.0669, 64, 29.62, 13.07, 0.7448, 65, -28.91, 5.28, 0.1789, 66, -68.31, -5.35, 0.00811, 59, 175.31, 20.15, 0.00058, 6, 60, -17.86, 127.37, 5e-05, 63, 106.81, -12.74, 0.01369, 64, 46.54, 12.56, 0.61275, 65, -12.5, 9.47, 0.33052, 66, -52.75, 1.33, 0.04282, 59, 191.43, 14.95, 0.00018, 4, 64, 64.52, 10.01, 0.40323, 65, 5.48, 11.99, 0.46452, 66, -35.38, 6.59, 0.13222, 59, 207.98, 7.5, 3e-05, 4, 64, 83.44, 4.42, 0.20737, 65, 25.2, 11.85, 0.51368, 66, -15.87, 9.51, 0.27895, 59, 224.59, -3.14, 0, 3, 64, 97.98, -2.2, 0.07982, 65, 41.01, 9.5, 0.48455, 66, 0.11, 9.63, 0.43562, 3, 64, 111.45, -11.29, 0.0317, 65, 56.47, 4.49, 0.44944, 66, 16.16, 7.08, 0.51886], "hull": 37, "edges": [0, 72, 0, 2, 2, 4, 8, 10, 10, 12, 20, 22, 22, 24, 28, 30, 30, 32, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 64, 66, 66, 68, 68, 70, 70, 72, 40, 42, 32, 34, 34, 36, 56, 58, 58, 60, 60, 62, 62, 64, 16, 18, 18, 20, 4, 6, 6, 8, 12, 14, 14, 16, 2, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 24, 26, 26, 28, 36, 38, 38, 40, 66, 76, 82, 60], "width": 57, "height": 78}}, "a8": {"a8": {"type": "mesh", "uvs": [0.43308, 0.00769, 0.51561, 0.00746, 0.7089, 0.10436, 0.78591, 0.21505, 0.70348, 0.29532, 0.85114, 0.33277, 0.88487, 0.48119, 0.72348, 0.45364, 0.82786, 0.54973, 0.91082, 0.6336, 0.99365, 0.73878, 0.9877, 0.82425, 0.84094, 0.93859, 0.67854, 0.99968, 0.40425, 0.96817, 0.20104, 0.86621, 0.0531, 0.70709, 1e-05, 0.5039, 1e-05, 0.37784, 0.10825, 0.33926, 0.03664, 0.24556, 0.26951, 0.23343, 0.33701, 0.07805, 0.62057, 0.8312, 0.55467, 0.15479], "triangles": [7, 16, 17, 19, 17, 18, 23, 8, 9, 8, 16, 7, 7, 19, 4, 17, 19, 7, 19, 21, 4, 21, 24, 4, 21, 22, 24, 7, 5, 6, 7, 4, 5, 22, 0, 24, 19, 20, 21, 4, 24, 3, 24, 2, 3, 0, 1, 24, 24, 1, 2, 12, 13, 23, 13, 14, 23, 14, 15, 23, 12, 23, 11, 15, 16, 23, 8, 23, 16, 11, 23, 10, 10, 23, 9], "vertices": [1, 59, -8.25, -15.35, 1, 1, 59, -9.93, -10.35, 1, 1, 59, -2.02, 5.28, 1, 1, 59, 9.9, 14.39, 1, 1, 59, 21.31, 12.59, 1, 1, 59, 22.91, 23.07, 1, 2, 63, -35.24, 41.33, 0.00016, 59, 40.28, 31.06, 0.99984, 2, 63, -38.14, 30.81, 0.02398, 59, 40.16, 20.14, 0.97602, 2, 63, -26.27, 38.22, 0.18173, 59, 49.76, 30.33, 0.81827, 2, 63, -15.87, 44.16, 0.33373, 59, 58.29, 38.73, 0.66627, 2, 63, -2.75, 50.27, 0.48587, 59, 69.42, 47.97, 0.51413, 2, 63, 8.19, 50.55, 0.569, 59, 79.94, 51.03, 0.431, 2, 63, 23.37, 42.05, 0.70829, 59, 96.77, 46.68, 0.29171, 2, 63, 31.8, 32.15, 0.78605, 59, 107.45, 39.25, 0.21395, 2, 63, 28.83, 14.38, 0.88312, 59, 109.11, 21.32, 0.11688, 2, 63, 16.59, 0.62, 0.99251, 59, 100.77, 4.89, 0.00749, 2, 63, -3.17, -10.06, 0.74265, 60, 9.33, 20.76, 0.25735, 3, 63, -28.93, -15.02, 0.0002, 59, 60.74, -21.83, 0.05168, 60, 21.08, -2.69, 0.94812, 3, 63, -45.03, -16, 0.00023, 59, 45.42, -26.87, 0.49187, 60, 26.38, -17.93, 0.5079, 3, 63, -50.38, -9.38, 0.00014, 59, 38.56, -21.84, 0.73515, 60, 21.45, -24.87, 0.26472, 3, 63, -62.08, -14.68, 9e-05, 59, 28.6, -29.94, 0.88096, 60, 29.72, -34.7, 0.11895, 3, 63, -64.53, 0.11, 3e-05, 59, 22.47, -16.27, 0.95926, 60, 16.15, -41.05, 0.04071, 2, 63, -84.64, 3.22, 0, 59, 2.22, -18.38, 1, 2, 63, 10.49, 27.15, 0.69832, 59, 88.12, 28.99, 0.30168, 2, 63, -75.67, 17.72, 0, 59, 7.2, -2.08, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 14, 16, 16, 18, 18, 20, 26, 46, 4, 48, 48, 0], "width": 25, "height": 51}}, "a12": {"a12": {"type": "mesh", "uvs": [0.63455, 0.00276, 0.73625, 0.19572, 0.96212, 0.2505, 0.99999, 0.56129, 0.67276, 0.58812, 0.671, 0.6536, 0.68186, 0.71884, 0.7057, 0.78796, 0.72954, 0.84674, 0.74306, 0.89654, 0.73026, 0.94728, 0.60709, 0.99808, 0.47287, 0.9614, 0.37239, 0.93395, 0.35058, 0.88249, 0.33254, 0.82086, 0.32412, 0.74531, 0.32988, 0.66146, 0.35548, 0.58428, 0, 0.51833, 0, 0.27074, 0.16524, 0.18923, 0.46612, 0.18837, 0.46028, 0.01303], "triangles": [3, 4, 2, 2, 4, 1, 1, 4, 22, 4, 18, 22, 19, 20, 18, 18, 20, 22, 22, 20, 21, 22, 0, 1, 22, 23, 0, 7, 15, 16, 7, 16, 6, 6, 17, 5, 17, 6, 16, 17, 18, 5, 5, 18, 4, 11, 12, 10, 10, 12, 9, 14, 9, 12, 12, 13, 14, 14, 8, 9, 14, 15, 8, 15, 7, 8], "vertices": [2, 19, -42.61, 4.75, 2e-05, 18, -4.46, 1.33, 0.99998, 2, 19, -28.25, 7.4, 0.01702, 18, 9.64, 5.12, 0.98298, 2, 19, -24.01, 14.06, 0.08779, 18, 13.34, 12.1, 0.91221, 3, 20, -16.04, 17.41, 0.00012, 19, -0.99, 14.55, 0.24918, 18, 36.25, 14.42, 0.7507, 3, 20, -15.97, 7.4, 0.01086, 19, 0.72, 4.68, 0.47688, 18, 38.74, 4.72, 0.51226, 3, 20, -11.23, 6.42, 0.06543, 19, 5.56, 4.49, 0.66432, 18, 43.58, 4.92, 0.27026, 3, 20, -6.42, 5.81, 0.20048, 19, 10.4, 4.68, 0.70082, 18, 48.39, 5.5, 0.0987, 3, 20, -1.27, 5.53, 0.42159, 19, 15.53, 5.25, 0.55761, 18, 53.46, 6.48, 0.0208, 3, 20, 3.14, 5.4, 0.66615, 19, 19.9, 5.84, 0.33361, 18, 57.76, 7.42, 0.00024, 2, 20, 6.83, 5.1, 0.86047, 19, 23.59, 6.15, 0.13953, 2, 20, 10.44, 4, 0.96195, 19, 27.33, 5.66, 0.03805, 2, 20, 13.43, -0.35, 0.99429, 19, 30.99, 1.86, 0.00571, 2, 20, 9.99, -3.78, 0.97521, 19, 28.16, -2.09, 0.02479, 2, 20, 7.42, -6.35, 0.89496, 19, 26.05, -5.05, 0.10504, 3, 20, 3.56, -6.26, 0.72593, 19, 22.22, -5.59, 0.27395, 18, 61, -3.8, 0.00012, 3, 20, -1.02, -5.92, 0.48863, 19, 17.65, -6.01, 0.49018, 18, 56.47, -4.58, 0.02119, 3, 20, -6.56, -5.1, 0.2549, 19, 12.05, -6.1, 0.64485, 18, 50.9, -5.12, 0.10025, 3, 20, -12.61, -3.74, 0.09321, 19, 5.85, -5.75, 0.6328, 18, 44.69, -5.27, 0.27399, 3, 20, -18.07, -1.89, 0.02009, 19, 0.17, -4.83, 0.46298, 18, 38.95, -4.8, 0.51693, 3, 20, -24.91, -11.42, 0.00074, 19, -5.01, -15.35, 0.24425, 18, 34.63, -15.7, 0.755, 2, 19, -23.33, -14.83, 0.08552, 18, 16.33, -16.65, 0.91448, 2, 19, -29.22, -9.71, 0.0162, 18, 10.05, -12.01, 0.9838, 1, 18, 9.52, -3, 1, 1, 18, -3.43, -3.85, 1], "hull": 24, "edges": [0, 46, 4, 6, 6, 8, 18, 20, 20, 22, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 2, 2, 4, 34, 36, 30, 32, 32, 34, 26, 28, 28, 30, 22, 24, 24, 26, 16, 18, 14, 16, 12, 14, 8, 10, 10, 12], "width": 12, "height": 29}}, "a19": {"a19": {"type": "mesh", "uvs": [0.4001, 1e-05, 0.42509, 0.04466, 0.37738, 0.10405, 0.40728, 0.19751, 0.44094, 0.29967, 0.49138, 0.36196, 0.55257, 0.41372, 0.6342, 0.45278, 0.72806, 0.47993, 0.81514, 0.52857, 0.88957, 0.59537, 0.95404, 0.67536, 0.97524, 0.72948, 0.99128, 0.79878, 0.99872, 0.87042, 0.99483, 0.92664, 0.97933, 0.97166, 0.94168, 0.98843, 0.90895, 0.99532, 0.81428, 0.99536, 0.7145, 0.97568, 0.63377, 0.93729, 0.54946, 0.87914, 0.47891, 0.83009, 0.3967, 0.75967, 0.31797, 0.68323, 0.25095, 0.60319, 0.19667, 0.52691, 0.1358, 0.44137, 0.06643, 0.34389, 0.02896, 0.28041, 0.00498, 0.21597, 0.00524, 0.11575, 0.0405, 0.04896, 0.07474, 0.01768, 0.14197, 0.02785, 0.16821, 0.00344, 0.37235, 0.01261, 0.23844, 0.05687, 0.23344, 0.17123, 0.26722, 0.265, 0.33229, 0.36105, 0.3936, 0.43538, 0.4424, 0.48112, 0.51748, 0.52229, 0.60507, 0.55546, 0.69141, 0.58176, 0.7807, 0.613, 0.85328, 0.66331, 0.90458, 0.72849, 0.93461, 0.78796, 0.94838, 0.85085, 0.94587, 0.91946, 0.31717, 0.15124], "triangles": [38, 36, 37, 1, 2, 37, 53, 2, 3, 53, 38, 2, 1, 37, 0, 38, 37, 2, 39, 38, 53, 43, 5, 6, 44, 43, 6, 44, 6, 7, 45, 44, 7, 45, 7, 8, 46, 45, 8, 46, 8, 9, 47, 46, 9, 25, 26, 43, 25, 43, 44, 24, 25, 44, 45, 23, 24, 45, 24, 44, 46, 22, 23, 46, 23, 45, 47, 22, 46, 50, 12, 13, 51, 50, 13, 51, 13, 14, 52, 51, 14, 15, 52, 14, 16, 52, 15, 19, 51, 52, 17, 52, 16, 17, 18, 52, 18, 19, 52, 50, 51, 20, 51, 19, 20, 47, 9, 10, 48, 47, 10, 48, 10, 11, 49, 48, 11, 49, 11, 12, 50, 49, 12, 49, 50, 20, 47, 21, 22, 21, 47, 48, 21, 48, 49, 20, 21, 49, 40, 53, 3, 40, 3, 4, 40, 29, 30, 41, 40, 4, 42, 41, 4, 42, 4, 5, 28, 29, 40, 28, 40, 41, 43, 42, 5, 27, 28, 41, 27, 41, 42, 26, 27, 42, 26, 42, 43, 40, 39, 53, 40, 30, 39, 39, 30, 31, 32, 39, 31, 33, 34, 35, 32, 33, 35, 32, 35, 39, 38, 35, 36, 39, 35, 38], "vertices": [1, 44, 47.09, 5.12, 1, 1, 44, 46.38, -5.34, 1, 1, 44, 32.18, -11.29, 1, 4, 54, 16.8, 34.25, 0.83654, 56, -98.38, 61.06, 0.00358, 57, -136.11, 78.24, 1e-05, 55, -44.48, 43.96, 0.15987, 4, 54, 38.52, 29.19, 0.71724, 56, -79.91, 48.57, 0.01353, 57, -119.3, 63.59, 0.00023, 55, -24.2, 34.69, 0.26899, 4, 54, 54.61, 31.06, 0.56128, 56, -64.22, 44.56, 0.0387, 57, -104.21, 57.7, 0.00141, 55, -8.06, 33.32, 0.3986, 5, 54, 69.81, 35.81, 0.3843, 56, -48.32, 43.56, 0.09038, 57, -88.56, 54.77, 0.006, 58, -106.99, 65.53, 7e-05, 55, 7.78, 34.96, 0.51926, 5, 54, 84.65, 45.26, 0.21898, 56, -31.08, 47.08, 0.17532, 57, -71.02, 56.16, 0.0199, 58, -89.39, 65.2, 0.00087, 55, 24.21, 41.28, 0.58493, 5, 54, 98.5, 57.98, 0.09806, 56, -13.6, 54, 0.28408, 57, -52.82, 60.9, 0.05218, 58, -70.82, 68.14, 0.00448, 55, 40.31, 50.99, 0.56119, 5, 54, 115.6, 67.34, 0.0324, 56, 5.72, 56.63, 0.38527, 57, -33.33, 61.15, 0.11077, 58, -51.4, 66.49, 0.01657, 55, 58.93, 56.77, 0.45499, 5, 54, 134.79, 72.72, 0.00719, 56, 25.56, 54.79, 0.44037, 57, -13.86, 56.9, 0.1913, 58, -32.44, 60.36, 0.04909, 55, 78.8, 58.23, 0.31206, 5, 54, 155.41, 75.07, 0.00089, 56, 45.66, 49.61, 0.42544, 57, 5.46, 49.31, 0.27177, 58, -13.95, 50.91, 0.12028, 55, 99.48, 56.43, 0.18162, 5, 54, 167.24, 72.95, 4e-05, 56, 55.95, 43.4, 0.34713, 57, 14.91, 41.89, 0.31695, 58, -5.27, 42.61, 0.24588, 55, 110.65, 52.01, 0.09, 4, 56, 67.47, 34.1, 0.2367, 57, 25.22, 31.26, 0.30297, 58, 3.95, 31.02, 0.42245, 55, 123.55, 44.74, 0.03788, 4, 56, 78.03, 23.39, 0.13258, 57, 34.39, 19.34, 0.23688, 58, 11.92, 18.27, 0.61707, 55, 135.73, 35.92, 0.01347, 4, 56, 84.88, 13.81, 0.0599, 57, 40.02, 9, 0.16468, 58, 16.51, 7.43, 0.77153, 55, 144.07, 27.61, 0.00389, 4, 56, 88.53, 4.65, 0.02988, 57, 42.53, -0.54, 0.1399, 58, 18.07, -2.32, 0.82936, 55, 149.18, 19.17, 0.00085, 4, 56, 85.16, -2.61, 0.05028, 57, 38.3, -7.34, 0.18928, 58, 13.2, -8.67, 0.76026, 55, 147.06, 11.45, 0.00018, 4, 56, 81.23, -7.68, 0.13313, 57, 33.78, -11.88, 0.2773, 58, 8.25, -12.75, 0.587, 55, 144.01, 5.81, 0.00257, 4, 56, 67.22, -19.1, 0.27437, 57, 18.48, -21.52, 0.33838, 58, -7.91, -20.84, 0.37024, 55, 132.08, -7.78, 0.01701, 4, 56, 49.84, -27.95, 0.43236, 57, 0.16, -28.18, 0.31897, 58, -26.8, -25.69, 0.18434, 55, 116.41, -19.37, 0.06433, 5, 54, 172.42, -5.24, 0.00067, 56, 32.82, -31.47, 0.53291, 57, -17.17, -29.6, 0.23136, 58, -44.18, -25.41, 0.06683, 55, 100.2, -25.65, 0.16822, 5, 54, 153.86, -13.15, 0.00771, 56, 12.66, -32.22, 0.52087, 57, -37.27, -27.89, 0.12483, 58, -64.02, -21.74, 0.01589, 55, 80.44, -29.72, 0.33071, 5, 54, 138.26, -19.73, 0.03716, 56, -4.26, -32.79, 0.40333, 57, -54.13, -26.39, 0.04857, 58, -80.65, -18.6, 0.00145, 55, 63.84, -33.07, 0.50949, 5, 43, 136.49, -104.64, 0.00019, 54, 117.68, -26.02, 0.11446, 56, -25.73, -31.3, 0.24429, 57, -75.26, -22.29, 0.01204, 55, 42.42, -35.14, 0.62902, 5, 43, 116.34, -95.97, 0.0029, 54, 96.34, -31.1, 0.25518, 56, -47.48, -28.41, 0.11238, 57, -96.5, -16.77, 0.0016, 55, 20.49, -35.89, 0.62794, 5, 43, 97.93, -85.72, 0.0152, 54, 75.45, -33.87, 0.44111, 56, -67.97, -23.53, 0.03689, 57, -116.24, -9.43, 3e-05, 55, -0.52, -34.45, 0.50677, 4, 43, 82.08, -75.21, 0.05094, 54, 56.47, -34.92, 0.61447, 56, -86.08, -17.72, 0.00766, 55, -19.34, -31.71, 0.32693, 4, 43, 64.3, -63.44, 0.12686, 54, 35.17, -36.1, 0.70771, 56, -106.39, -11.2, 0.00064, 55, -40.44, -28.64, 0.16479, 3, 43, 44.04, -50.02, 0.25316, 54, 10.91, -37.44, 0.68489, 55, -64.49, -25.14, 0.06195, 3, 43, 32.19, -40.7, 0.42319, 54, -4.16, -37.04, 0.56086, 55, -79.18, -21.75, 0.01594, 3, 43, 22.63, -30.16, 0.60887, 54, -18.12, -34.31, 0.38887, 55, -92.32, -16.3, 0.00226, 3, 43, 14.34, -10.93, 0.77399, 54, -36.26, -23.83, 0.22594, 55, -108.02, -2.43, 6e-05, 2, 43, 14.97, 4.56, 0.89118, 54, -45.01, -11.04, 0.10882, 2, 43, 18.36, 13.16, 0.95821, 54, -47.42, -2.11, 0.04179, 2, 43, 30.99, 16.32, 0.988, 54, -39.18, 7.97, 0.012, 3, 43, 33.56, 22.99, 0.7187, 54, -41.11, 14.85, 0.0013, 44, 8.74, 27.29, 0.28, 1, 44, 41.19, 5.59, 1, 1, 44, 14.5, 10.81, 1, 1, 43, 58.94, -4.22, 1, 4, 43, 72.66, -19.63, 0.05761, 54, 15.71, 4.03, 0.86978, 56, -110.21, 33.23, 0.00014, 55, -51.55, 14.55, 0.07246, 5, 43, 92.05, -33.11, 0.02113, 54, 39.31, 4.8, 0.79794, 56, -87.89, 25.52, 0.00174, 57, -130.03, 41.68, 1e-05, 55, -28.27, 10.63, 0.17919, 5, 43, 108.98, -42.7, 0.00531, 54, 58.61, 7.22, 0.64746, 56, -69, 20.87, 0.00988, 57, -111.85, 34.77, 0.00017, 55, -8.87, 9.17, 0.33718, 6, 43, 121.33, -47.76, 0.00058, 54, 71.54, 10.54, 0.44528, 56, -55.74, 19.35, 0.03603, 57, -98.87, 31.64, 0.00137, 58, -119.51, 43.52, 0, 55, 4.47, 9.85, 0.51674, 5, 54, 86.15, 18.69, 0.2496, 56, -39.19, 21.74, 0.09551, 57, -82.15, 31.99, 0.00672, 58, -102.83, 42.24, 0.00013, 55, 20.4, 14.94, 0.64804, 5, 54, 100.49, 29.75, 0.10802, 56, -21.84, 26.93, 0.19427, 57, -64.3, 35.03, 0.02344, 58, -84.77, 43.52, 0.0013, 55, 36.65, 22.93, 0.67296, 5, 54, 113.47, 41.31, 0.03478, 56, -5.58, 33.09, 0.31381, 57, -47.41, 39.16, 0.06222, 58, -67.56, 45.98, 0.00675, 55, 51.67, 31.68, 0.58244, 5, 54, 127.62, 52.85, 0.00752, 56, 11.76, 38.8, 0.41164, 57, -29.5, 42.71, 0.13053, 58, -49.39, 47.77, 0.02486, 55, 67.83, 40.18, 0.42545, 5, 54, 143.65, 59.63, 0.00114, 56, 29.15, 39.4, 0.44418, 57, -12.17, 41.19, 0.21855, 58, -32.29, 44.56, 0.07275, 55, 84.88, 43.64, 0.26338, 5, 54, 160.34, 61.34, 0.00012, 56, 45.35, 35.03, 0.39633, 57, 3.38, 34.88, 0.29825, 58, -17.43, 36.76, 0.16695, 55, 101.58, 42.01, 0.13835, 5, 54, 173.97, 60.13, 0, 56, 57.65, 29.02, 0.29471, 57, 14.85, 27.41, 0.33941, 58, -6.74, 28.21, 0.30463, 55, 114.7, 38.11, 0.06125, 4, 56, 67.99, 20.49, 0.19279, 57, 24.08, 17.69, 0.34237, 58, 1.49, 17.62, 0.44167, 55, 126.31, 31.4, 0.02317, 4, 56, 76.68, 9.07, 0.14216, 57, 31.31, 5.29, 0.33404, 58, 7.47, 4.58, 0.51302, 55, 136.77, 21.57, 0.01078, 1, 44, 17.25, -13.84, 1], "hull": 38, "edges": [0, 2, 2, 4, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 50, 52, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 56, 58, 52, 54, 54, 56, 46, 48, 48, 50, 42, 44, 44, 46, 12, 14, 14, 16, 4, 6, 6, 8, 26, 28, 28, 30, 8, 10, 10, 12, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 4, 106, 74, 0], "width": 76, "height": 83}}, "a16": {"a16": {"type": "mesh", "uvs": [0.19284, 0.18458, 0.4669, 0.18436, 0.45969, 0.0136, 0.64648, 0.00962, 0.72898, 0.19898, 0.95272, 0.24886, 1, 0.57499, 0.66284, 0.59226, 0.67531, 0.64248, 0.68743, 0.6913, 0.69806, 0.73415, 0.70581, 0.77864, 0.71959, 0.82088, 0.74088, 0.86578, 0.76909, 0.90496, 0.73396, 0.94111, 0.6672, 0.98712, 0.54226, 0.99056, 0.45192, 0.95382, 0.36619, 0.91893, 0.33257, 0.86746, 0.32501, 0.81844, 0.32635, 0.76954, 0.33022, 0.71492, 0.34071, 0.65171, 0.34974, 0.59579, 0, 0.49783, 0, 0.30047], "triangles": [16, 17, 15, 15, 17, 18, 15, 18, 13, 14, 15, 13, 13, 18, 19, 19, 20, 13, 20, 21, 12, 20, 12, 13, 21, 11, 12, 11, 21, 22, 10, 22, 23, 10, 23, 9, 11, 22, 10, 23, 24, 9, 24, 8, 9, 24, 25, 8, 25, 7, 8, 26, 27, 25, 25, 27, 1, 25, 1, 7, 1, 27, 0, 7, 5, 6, 7, 4, 5, 7, 1, 4, 1, 3, 4, 1, 2, 3], "vertices": [2, 15, 7.86, -10.11, 0.97496, 16, -29.05, -8.38, 0.02504, 2, 15, 7.68, -1.89, 0.99998, 16, -28.74, -0.17, 2e-05, 1, 15, -4.95, -2.37, 1, 2, 15, -5.36, 3.23, 0.99777, 16, -41.45, 5.73, 0.00223, 2, 15, 8.6, 5.99, 0.97056, 16, -27.35, 7.65, 0.02944, 2, 15, 12.15, 12.78, 0.88184, 16, -23.4, 14.21, 0.11816, 2, 15, 36.25, 14.69, 0.70126, 16, 0.77, 14.67, 0.29874, 3, 15, 37.74, 4.61, 0.46, 16, 1.65, 4.52, 0.53986, 17, -16.76, 6.21, 0.00013, 3, 15, 41.44, 5.06, 0.23067, 16, 5.38, 4.74, 0.76462, 17, -13.03, 6.06, 0.00471, 3, 15, 45.05, 5.5, 0.08063, 16, 9, 4.96, 0.88651, 17, -9.4, 5.92, 0.03285, 3, 15, 48.21, 5.88, 0.01577, 16, 12.18, 5.16, 0.86612, 17, -6.21, 5.8, 0.11811, 3, 15, 51.5, 6.18, 0.00049, 16, 15.48, 5.26, 0.71503, 17, -2.92, 5.58, 0.28448, 2, 16, 18.62, 5.55, 0.4884, 17, 0.23, 5.55, 0.5116, 2, 16, 21.97, 6.06, 0.26496, 17, 3.61, 5.73, 0.73504, 2, 16, 24.9, 6.79, 0.10593, 17, 6.6, 6.17, 0.89407, 2, 16, 27.53, 5.63, 0.028, 17, 9.1, 4.75, 0.972, 2, 16, 30.85, 3.49, 0.00417, 17, 12.2, 2.3, 0.99583, 2, 16, 30.96, -0.26, 0.00971, 17, 11.93, -1.45, 0.99029, 2, 16, 28.13, -2.86, 0.04893, 17, 8.87, -3.76, 0.95107, 2, 16, 25.45, -5.33, 0.15109, 17, 5.95, -5.95, 0.84891, 2, 16, 21.61, -6.19, 0.33029, 17, 2.04, -6.42, 0.66971, 3, 15, 54.68, -5.18, 0.00013, 16, 17.97, -6.27, 0.55684, 17, -1.58, -6.14, 0.44303, 3, 15, 51.06, -5.22, 0.01245, 16, 14.36, -6.09, 0.75473, 17, -5.16, -5.6, 0.23282, 3, 15, 47.02, -5.18, 0.07397, 16, 10.32, -5.81, 0.83569, 17, -9.15, -4.93, 0.09034, 3, 15, 42.33, -4.96, 0.22134, 16, 5.66, -5.31, 0.7556, 17, -13.74, -3.97, 0.02306, 3, 15, 38.19, -4.78, 0.45543, 16, 1.54, -4.88, 0.54182, 17, -17.8, -3.13, 0.00275, 3, 15, 31.16, -15.42, 0.70222, 16, -6.12, -15.08, 0.29777, 17, -26.43, -12.52, 1e-05, 2, 15, 16.56, -15.72, 0.88803, 16, -20.71, -14.5, 0.11197], "hull": 28, "edges": [4, 6, 10, 12, 12, 14, 28, 30, 30, 32, 32, 34, 38, 40, 50, 52, 52, 54, 4, 2, 6, 8, 0, 54, 0, 2, 8, 10, 48, 50, 46, 48, 44, 46, 40, 42, 42, 44, 26, 28, 24, 26, 22, 24, 20, 22, 18, 20, 14, 16, 16, 18, 34, 36, 36, 38], "width": 12, "height": 29}}}}], "animations": {"gu_tou": {"slots": {"kulou": {"attachment": [{"name": "kulou"}]}}}, "gui_hun": {"slots": {"g2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.4667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.9, "color": "ffffff00"}], "attachment": [{"name": "g2"}]}, "g5": {"attachment": [{"name": "g3"}]}, "g4": {"attachment": [{"name": "g2"}]}, "g1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 6.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "color": "ffffff00"}], "attachment": [{"name": "g1"}]}, "g3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.7, "color": "ffffffff", "curve": "stepped"}, {"time": 8.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "color": "ffffff00"}], "attachment": [{"name": "g3"}]}}, "bones": {"bone37": {"translate": [{"x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": 0.26, "y": 0.13}]}, "bone38": {"translate": [{"x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": 0.34, "y": -0.25}]}, "bone13": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}]}, "bone14": {"translate": [{"x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.2667, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.3333, "x": 0.11, "y": 0.91}]}, "bone15": {"translate": [{"x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": 0.34, "y": -0.15}]}, "bone16": {"translate": [{"x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.3333, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 4, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 4.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5.3333, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 5.7, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 6.6667, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 7.0333, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 8, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 8.3667, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 9.3333, "x": 0.84, "y": 1.45}]}, "bone17": {"translate": [{"x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.7333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.0667, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.3333, "x": 1.74, "y": 1.31}]}, "bone18": {"translate": [{"x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.26, "y": 0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 1.38, "y": 0.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": 0.26, "y": 0.13}]}, "bone19": {"translate": [{"x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.34, "y": -0.25, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 1.85, "y": -1.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": 0.34, "y": -0.25}]}, "bone33": {"translate": [{"x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "x": 0.11, "y": 0.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.2667, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "x": 0.3, "y": 2.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.3333, "x": 0.11, "y": 0.91}]}, "bone32": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.5, "y": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}]}, "bone36": {"translate": [{"x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.7333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.0667, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8, "x": 1.74, "y": 1.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "x": 2.75, "y": 2.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.3333, "x": 1.74, "y": 1.31}]}, "bone35": {"translate": [{"x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.3333, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 4, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 4.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5.3333, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 5.7, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 6.6667, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 7.0333, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 8, "x": 0.84, "y": 1.45, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 8.3667, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "x": 1.49, "y": 2.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 9.3333, "x": 0.84, "y": 1.45}]}, "bone34": {"translate": [{"x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.34, "y": -0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 1.87, "y": -0.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": 0.34, "y": -0.15}]}, "bone23": {"translate": [{"x": -0.15, "y": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.84, "y": 3.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": -0.15, "y": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": -0.84, "y": 3.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -0.15, "y": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -0.84, "y": 3.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": -0.15, "y": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": -0.84, "y": 3.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": -0.15, "y": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": -0.84, "y": 3.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": -0.15, "y": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": -0.84, "y": 3.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": -0.15, "y": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": -0.84, "y": 3.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": -0.15, "y": 0.66}]}, "bone28": {"translate": [{"x": 0.36, "y": -1.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.73, "y": -2.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.36, "y": -1.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 0.73, "y": -2.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.36, "y": -1.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.73, "y": -2.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 0.36, "y": -1.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.73, "y": -2.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.36, "y": -1.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 0.73, "y": -2.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": 0.36, "y": -1.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 0.73, "y": -2.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 0.36, "y": -1.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 0.73, "y": -2.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "x": 0.36, "y": -1.43}]}, "bone27": {"translate": [{"x": 0.32, "y": -0.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.75, "y": -0.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.32, "y": -0.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.75, "y": -0.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.32, "y": -0.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.75, "y": -0.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": 0.32, "y": -0.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 1.75, "y": -0.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.32, "y": -0.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 1.75, "y": -0.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": 0.32, "y": -0.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.75, "y": -0.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.32, "y": -0.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 1.75, "y": -0.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": 0.32, "y": -0.02}]}, "bone25": {"translate": [{"x": 0.14, "y": -0.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.76, "y": -0.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.14, "y": -0.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.76, "y": -0.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.14, "y": -0.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.76, "y": -0.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": 0.14, "y": -0.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 0.76, "y": -0.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.14, "y": -0.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 0.76, "y": -0.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": 0.14, "y": -0.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 0.76, "y": -0.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.14, "y": -0.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 0.76, "y": -0.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": 0.14, "y": -0.07}]}, "bone22": {"translate": [{"x": 0.07, "y": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.4, "y": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.07, "y": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.4, "y": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.07, "y": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.4, "y": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": 0.07, "y": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 0.4, "y": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.07, "y": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 0.4, "y": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "x": 0.07, "y": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 0.4, "y": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.07, "y": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 0.4, "y": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 9.3333, "x": 0.07, "y": -0.44}]}, "bone24": {"translate": [{"x": 0.65, "y": 0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.29, "y": 0.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.65, "y": 0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.29, "y": 0.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.65, "y": 0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 1.29, "y": 0.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 0.65, "y": 0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 1.29, "y": 0.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.65, "y": 0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 1.29, "y": 0.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": 0.65, "y": 0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 1.29, "y": 0.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 0.65, "y": 0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 1.29, "y": 0.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "x": 0.65, "y": 0.27}]}, "bone21": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.48, "y": 0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -1.48, "y": 0.37, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -1.48, "y": 0.37, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -1.48, "y": 0.37, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -1.48, "y": 0.37, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -1.48, "y": 0.37, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -1.48, "y": 0.37, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}]}, "bone26": {"translate": [{"x": 0.18, "y": 0.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.35, "y": 1.92, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.18, "y": 0.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 0.35, "y": 1.92, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.18, "y": 0.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.35, "y": 1.92, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 0.18, "y": 0.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.35, "y": 1.92, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.18, "y": 0.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 0.35, "y": 1.92, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": 0.18, "y": 0.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 0.35, "y": 1.92, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 0.18, "y": 0.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 0.35, "y": 1.92, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "x": 0.18, "y": 0.96}]}, "bone29": {"translate": [{"x": 0.95, "y": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.16, "y": -3.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": 0.95, "y": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.16, "y": -3.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 0.95, "y": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 1.16, "y": -3.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4, "x": 0.95, "y": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.16, "y": -3.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 0.95, "y": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "x": 1.16, "y": -3.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "x": 0.95, "y": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 7.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.16, "y": -3.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "x": 0.95, "y": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 8.5, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "x": 1.16, "y": -3.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 9.3333, "x": 0.95, "y": -2.68}]}, "bone8": {"translate": [{"x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "x": 2.01, "y": -0.4}]}, "bone9": {"translate": [{"x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 7.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 8.5, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 9.3333, "x": 1.05, "y": 2.32}]}, "bone46": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}]}, "bone7": {"translate": [{"x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "x": 0.39, "y": 0.64}]}, "bone5": {"rotate": [{"angle": 10.33}], "translate": [{"x": -0.45, "y": -0.46}]}, "bone6": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}]}, "bone10": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": 3.45, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}]}, "bone42": {"translate": [{"x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 0.39, "y": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 0.77, "y": 1.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "x": 0.39, "y": 0.64}]}, "bone41": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -1.48, "y": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}]}, "bone44": {"translate": [{"x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 7.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "x": 1.05, "y": 2.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 8.5, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "x": 1.29, "y": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 9.3333, "x": 1.05, "y": 2.32}]}, "bone43": {"translate": [{"x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 2.01, "y": -0.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 4.01, "y": -0.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "x": 2.01, "y": -0.4}]}, "bone45": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": 2.08, "y": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}]}, "bone40": {"translate": [{"x": -0.45, "y": -0.46}]}, "bone39": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 135.59, "y": -4.37, "curve": 0.25, "c3": 0.75}, {"time": 9.3333}], "scale": [{"time": 4.6, "curve": "stepped"}, {"time": 4.6667, "x": -1}]}, "bone4": {"translate": [{"x": -137.6, "y": -5.56, "curve": "stepped"}, {"time": 2.3333, "x": -137.6, "y": -5.56}, {"time": 7.9, "x": 8.34, "y": 0.34}]}, "bone30": {"translate": [{"x": 88.08, "y": 3.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.1667, "x": 148.83, "y": 6.01, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "x": 14.66, "y": 0.59, "curve": "stepped"}, {"time": 6.8333, "x": 14.66, "y": 0.59, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 9.3333, "x": 88.08, "y": 3.56}], "scale": [{"x": -1, "curve": "stepped"}, {"time": 2.1, "x": -1, "curve": "stepped"}, {"time": 2.1667, "curve": "stepped"}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": -1}]}, "bone2": {"translate": [{"time": 3.3333, "x": 219.66, "y": -1.79}, {"time": 9.3333, "x": -84.64, "y": 0.69}]}, "bone3": {"translate": [{"x": -187.61, "y": -1.41}, {"time": 6.6333, "x": 206.8, "y": 1.55}]}}}, "hei_bai_wu_chang": {"slots": {"bwc011": {"color": [{"color": "ffb10072", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffb10000", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffb10072"}], "attachment": [{"name": "bwc011"}]}, "bwc03": {"attachment": [{"name": "bwc03"}]}, "hwc08": {"attachment": [{"name": "hwc08"}]}, "hwc07": {"attachment": [{"name": "hwc07"}]}, "hwc02": {"attachment": [{"name": "hwc02"}]}, "bwc01": {"attachment": [{"name": "bwc01"}]}, "hwc010": {"attachment": [{"name": "hwc010"}]}, "bwc09": {"attachment": [{"name": "bwc09"}]}, "hwc06": {"attachment": [{"name": "hwc06"}]}, "bwc010": {"attachment": [{"name": "bwc010"}]}, "hwc09": {"attachment": [{"name": "hwc09"}]}, "bwc04": {"attachment": [{"name": "bwc04"}]}, "bwc08": {"attachment": [{"name": "bwc08"}]}, "hwc05": {"attachment": [{"name": "hwc05"}]}, "bwc05": {"attachment": [{"name": "bwc05"}]}, "hwc04": {"attachment": [{"name": "hwc04"}]}, "bwc07": {"attachment": [{"name": "bwc07"}]}, "bwc02": {"attachment": [{"name": "bwc02"}]}, "hwc03": {"attachment": [{"name": "hwc03"}]}, "sd": {"attachment": [{"name": "sd"}]}, "bwc06": {"attachment": [{"name": "bwc06"}]}, "sd2": {"attachment": [{"name": "sd"}]}}, "bones": {"bwc2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.24, "y": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bwc3": {"rotate": [{"angle": 0.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 4.92, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.47}]}, "bwc4": {"rotate": [{"angle": 1.4, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.92, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.4}]}, "bwc5": {"rotate": [{"angle": 0.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 4.92, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.47}]}, "bwc6": {"rotate": [{"angle": 1.4, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.92, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.4}]}, "bwc7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.72, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bwc8": {"rotate": [{"angle": 1.12, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 6.72, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 1.12}]}, "bwc9": {"rotate": [{"angle": 0.65, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.72, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.65}]}, "bwc10": {"rotate": [{"angle": 2.46, "curve": 0.345, "c2": 0.37, "c3": 0.683, "c4": 0.72}, {"time": 0.1667, "angle": 1.12, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 6.72, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 2.46}]}, "bwc11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bwc16": {"rotate": [{"angle": 5.14, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 8.74, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 5.14}]}, "bwc17": {"rotate": [{"angle": 7.88, "curve": 0.295, "c2": 0.21, "c3": 0.678, "c4": 0.7}, {"time": 0.5667, "angle": 1.77, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 8.74, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 7.88}]}, "bwc18": {"rotate": [{"angle": 8.37, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": 8.74, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.5667, "angle": 4.76, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 1.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": 8.37}]}, "bwc19": {"rotate": [{"angle": 4.76, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": 8.74, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 4.76}]}, "bwc20": {"rotate": [{"angle": 1.77, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": 8.74, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1.4667, "angle": 1.77, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.7333, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 1.77}]}, "bwc21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.74, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.4667, "angle": 4.76, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2}]}, "bwc22": {"rotate": [{"angle": 1.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.61, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.88}]}, "bwc23": {"rotate": [{"angle": 4.73, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": 1.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6.61, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.73}], "translate": [{"x": -3.28, "y": -0.94, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "x": -1.3, "y": -0.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -4.59, "y": -1.31, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -3.28, "y": -0.94}]}, "bwc24": {"rotate": [{"angle": -5.76, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -8.83, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": -5.76}]}, "bwc25": {"rotate": [{"angle": -3.9, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "angle": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -8.83, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": -3.9}]}, "bwc12": {"rotate": [{"angle": 0.75, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 7.8, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.75}]}, "bwc13": {"rotate": [{"angle": 2.21, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.8, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.21}]}, "bwc14": {"rotate": [{"angle": 3.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 7.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 3.9}]}, "bwc15": {"rotate": [{"angle": 5.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.8, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 5.59}]}, "hwc2": {"rotate": [{"angle": -7.35}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "x": 0.18, "y": -2.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2}]}, "hwc3": {"rotate": [{"angle": 1.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.13}]}, "hwc4": {"rotate": [{"angle": 2.86, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.86}]}, "hwc5": {"rotate": [{"angle": -2.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.55}]}, "hwc6": {"rotate": [{"angle": -6.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.42}]}, "hwc11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.54, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "hwc12": {"rotate": [{"angle": -3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.13}]}, "hwc13": {"rotate": [{"angle": -1.06, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -11.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.06}]}, "hwc14": {"rotate": [{"angle": -5.5, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "angle": -3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -11.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -5.5}]}, "hwc16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.02, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "hwc17": {"rotate": [{"angle": -3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.13}]}, "hwc18": {"rotate": [{"angle": -7.43, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -11.02, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": -7.43}]}, "hwc19": {"rotate": [{"angle": -1.06, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -11.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.06}]}, "hwc20": {"rotate": [{"angle": -5.5, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "angle": -3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -11.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -5.5}]}, "hwc21": {"rotate": [{"angle": -9.61, "curve": 0.314, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.1667, "angle": -7.43, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -11.02, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -9.61}]}, "hwc23": {"rotate": [{"angle": 4, "curve": 0.309, "c2": 0.26, "c3": 0.671, "c4": 0.68}, {"time": 0.4333, "angle": 1.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 4.8, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 4}]}, "hwc24": {"rotate": [{"angle": 4.69, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": 4.8, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "angle": 3.24, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 1.0667, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "angle": 4.69}], "translate": [{"x": -2.78, "y": 0.55, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "x": -2.85, "y": 0.56, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "x": -1.92, "y": 0.38, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 1.0667, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "x": -2.78, "y": 0.55}]}, "hwc25": {"translate": [{"x": 0.87, "y": 2.88, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 0.93, "y": 3.1, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "x": 0.87, "y": 2.88}]}, "hwc26": {"translate": [{"x": 0.38, "y": 1.28, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 0.93, "y": 3.1, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "x": 0.38, "y": 1.28}]}, "hwc27": {"translate": [{"x": 0.12, "y": 0.4, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "x": 0.93, "y": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "x": 0.12, "y": 0.4}]}, "hwc28": {"translate": [{"x": 0.09, "y": 0.3, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.93, "y": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 0.09, "y": 0.3}]}}}, "meng_po": {"slots": {"a1": {"attachment": [{"name": "a1"}]}, "sd6": {"attachment": [{"name": "sd"}]}, "a23": {"attachment": [{"name": "a23"}]}, "a15": {"attachment": [{"name": "a15"}]}, "a21": {"attachment": [{"name": "a21"}]}, "a8": {"attachment": [{"name": "a8"}]}, "a16": {"attachment": [{"name": "a16"}]}, "a25": {"attachment": [{"name": "a25"}]}, "a26": {"attachment": [{"name": "a22"}]}, "a11": {"attachment": [{"name": "a11"}]}, "a22": {"attachment": [{"name": "a22"}]}, "a2": {"attachment": [{"name": "a2"}]}, "a17": {"attachment": [{"name": "a17"}]}, "a14": {"attachment": [{"name": "a14"}]}, "a7": {"attachment": [{"name": "a7"}]}, "yw/wenli_00": {"attachment": [{"name": "yw/wenli_00"}, {"time": 0.0333, "name": "yw/wenli_01"}, {"time": 0.0667, "name": "yw/wenli_02"}, {"time": 0.1, "name": "yw/wenli_03"}, {"time": 0.1333, "name": "yw/wenli_04"}, {"time": 0.1667, "name": "yw/wenli_05"}, {"time": 0.2, "name": "yw/wenli_06"}, {"time": 0.2333, "name": "yw/wenli_07"}, {"time": 0.2667, "name": "yw/wenli_08"}, {"time": 0.3, "name": "yw/wenli_09"}, {"time": 0.3333, "name": "yw/wenli_10"}, {"time": 0.3667, "name": "yw/wenli_11"}, {"time": 0.4, "name": "yw/wenli_12"}, {"time": 0.4333, "name": "yw/wenli_13"}, {"time": 0.4667, "name": "yw/wenli_14"}, {"time": 0.5, "name": "yw/wenli_15"}, {"time": 0.5333, "name": "yw/wenli_16"}, {"time": 0.5667, "name": "yw/wenli_17"}, {"time": 0.6, "name": "yw/wenli_18"}, {"time": 0.6333, "name": "yw/wenli_19"}, {"time": 0.6667, "name": "yw/wenli_00"}, {"time": 0.7, "name": "yw/wenli_01"}, {"time": 0.7333, "name": "yw/wenli_02"}, {"time": 0.7667, "name": "yw/wenli_03"}, {"time": 0.8, "name": "yw/wenli_04"}, {"time": 0.8333, "name": "yw/wenli_05"}, {"time": 0.8667, "name": "yw/wenli_06"}, {"time": 0.9, "name": "yw/wenli_07"}, {"time": 0.9333, "name": "yw/wenli_08"}, {"time": 0.9667, "name": "yw/wenli_09"}, {"time": 1, "name": "yw/wenli_10"}, {"time": 1.0333, "name": "yw/wenli_11"}, {"time": 1.0667, "name": "yw/wenli_12"}, {"time": 1.1, "name": "yw/wenli_13"}, {"time": 1.1333, "name": "yw/wenli_14"}, {"time": 1.1667, "name": "yw/wenli_15"}, {"time": 1.2, "name": "yw/wenli_16"}, {"time": 1.2333, "name": "yw/wenli_17"}, {"time": 1.2667, "name": "yw/wenli_18"}, {"time": 1.3, "name": "yw/wenli_19"}]}, "a24": {"attachment": [{"name": "a24"}]}, "a18": {"attachment": [{"name": "a18"}]}, "a10": {"attachment": [{"name": "a10"}]}, "a4": {"attachment": [{"name": "a4"}]}, "tangg": {"attachment": [{"name": "tangg"}]}, "a0": {"attachment": [{"name": "a0"}]}, "a12": {"attachment": [{"name": "a12"}]}, "a13": {"attachment": [{"name": "a13"}]}, "a20": {"attachment": [{"name": "a20"}]}, "a9": {"attachment": [{"name": "a9"}]}, "a6": {"attachment": [{"name": "a6"}]}, "a19": {"attachment": [{"name": "a19"}]}, "a5": {"attachment": [{"name": "a5"}]}, "a3": {"attachment": [{"name": "a3"}]}}, "bones": {"mpmingzu2": {"translate": [{"y": -5.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -5.96}]}, "mpmingzu4": {"translate": [{"x": 0.01, "y": 0.64, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.04, "y": 3.46, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.01, "y": 0.64}]}, "mpmingzu5": {"translate": [{"x": 1.84, "y": -0.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 3.69, "y": -0.34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 1.84, "y": -0.17}]}, "mpmingzu6": {"rotate": [{"angle": 2.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.82, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 2.41}]}, "mpmingzu8": {"rotate": [{"angle": -4.34, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 11.82, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.1333, "angle": 0.45, "curve": 0.372, "c2": 0.5, "c3": 0.72, "c4": 0.9}, {"time": 1.3333, "angle": -4.34}]}, "mpmingzu9": {"rotate": [{"angle": -8.31, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -20.5, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 12.4, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.1333, "angle": 4.43, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -8.31}]}, "mpmingzu10": {"rotate": [{"angle": 11.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": -25.01, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.1333, "angle": 18.94, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1667, "angle": 19.7, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 11.45}]}, "mpmingzu11": {"rotate": [{"angle": -5.05, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "angle": 7.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.8667, "angle": -5.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.1, "angle": -10.57, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -5.05}]}, "mpmingzu12": {"rotate": [{"angle": -22.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 13.2, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.8667, "angle": 4.54, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.3333, "angle": -22.54}]}, "mpmingzu13": {"rotate": [{"angle": -20.84, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -35.75, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.8667, "angle": 12.36, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.9, "angle": 13.2, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -20.84}]}, "mpmingzu14": {"rotate": [{"angle": -0.58, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -4.42, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 8.16, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -0.58}]}, "mpmingzu15": {"rotate": [{"angle": -1.06, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": -5.68, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": -22.97, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -0.15, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -1.06}]}, "mpmingzu16": {"rotate": [{"angle": -21.26, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": -16.16, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": -15.64, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -46.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -21.26}]}, "mpmingzu17": {"rotate": [{"angle": 9.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": 19.43, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 33.12, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -4.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 9.63}]}, "mpmingzu18": {"rotate": [{"angle": 7.03, "curve": 0.292, "c2": 0.13, "c3": 0.633, "c4": 0.5}, {"time": 0.1333, "angle": 13.46, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.6333, "angle": 43.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 6.6, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": 7.03}]}, "mpmingzu19": {"rotate": [{"angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -21.31, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.1, "angle": -12.33, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.3333, "angle": -8.4}]}, "mpmingzu20": {"rotate": [{"angle": 13.21, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 36.03, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.1, "angle": 0.4, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 1.3333, "angle": 13.21}]}, "mpmingzu21": {"rotate": [{"angle": 2.43, "curve": 0.28, "c2": 0.1, "c3": 0.628, "c4": 0.5}, {"time": 0.6333, "angle": 31.96, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.1, "angle": 10.17, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 1.3333, "angle": 2.43}]}, "mpmingzu22": {"rotate": [{"angle": -28.69, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -34.89, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -4.66, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "angle": -14.67, "curve": 0.326, "c2": 0.31, "c3": 0.669, "c4": 0.67}, {"time": 1.2667, "angle": -25.43, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 1.3333, "angle": -28.69}]}, "mpmingzu23": {"rotate": [{"angle": -21.91, "curve": 0.335, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.3667, "angle": -43.72, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -10.87, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -21.91}]}, "mpmingzu24": {"rotate": [{"angle": 11.23, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3333, "angle": -27.24, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.0333, "angle": 1.88, "curve": 0.362, "c2": 0.45, "c3": 0.706, "c4": 0.82}, {"time": 1.3333, "angle": 11.23}]}, "mpmingzu25": {"translate": [{"x": -6.06, "y": 0.14, "curve": 0.331, "c2": 0.33, "c3": 0.674, "c4": 0.69}, {"time": 0.1667, "x": -2.93, "y": 0.07, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -9.61, "y": 0.23, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": -6.06, "y": 0.14}]}, "mpmingzu26": {"translate": [{"x": -5.45, "y": -0.83, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1667, "x": -4.17, "y": -0.64, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": -5.51, "y": -0.84, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "x": -5.45, "y": -0.83}]}, "mpmingzu27": {"rotate": [{"angle": 1.01, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 3.22, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -4.05, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 1.01}]}, "mpmingzu28": {"rotate": [{"angle": -6.81, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": -3.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": 8.47, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -7.44, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -6.81}]}, "mpmingzu29": {"rotate": [{"angle": -5.07, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": -9.8, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": -10.29, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 18.32, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -5.07}]}, "mpmingzu30": {"rotate": [{"angle": 6.14, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -2.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -14.71, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 18.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 6.14}]}, "mpmingzu31": {"rotate": [{"angle": 17.76, "curve": 0.292, "c2": 0.13, "c3": 0.633, "c4": 0.5}, {"time": 0.1333, "angle": 9.39, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.6333, "angle": -30.09, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 18.32, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": 17.76}]}, "mpmingzu32": {"translate": [{"x": 0.8, "y": -2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.6, "y": -4.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.8, "y": -2.34}]}, "mpmingzu34": {"translate": [{"x": 1.82, "y": 0.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 3.64, "y": 0.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 1.82, "y": 0.34}]}, "mpmingzu36": {"rotate": [{"angle": -0.38, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -1.45, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 2.98, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -0.38}]}, "mpmingzu37": {"rotate": [{"angle": -1.37, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -0.07, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -1.37}]}, "mpmingzu38": {"rotate": [{"angle": 6.83, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "angle": -0.89, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 7.24, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": 6.83}]}, "mpmingzu39": {"rotate": [{"angle": -0.9, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.2667, "angle": -12.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": -19.18, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -0.9}]}, "mpmingzu40": {"rotate": [{"angle": -1.25, "curve": 0.362, "c2": 0.45, "c3": 0.706, "c4": 0.82}, {"time": 0.1667, "angle": 7.55, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.2333, "angle": 9.01, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "angle": 0.68, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9, "angle": -25.39, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -1.25}]}, "mpmingzu41": {"rotate": [{"angle": -18.87, "curve": 0.32, "c2": 0.29, "c3": 0.663, "c4": 0.66}, {"time": 0.1667, "angle": -6.87, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.4333, "angle": 8.38, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.4667, "angle": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -27.82, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -18.87}]}, "mpmingzu42": {"rotate": [{"angle": -27.19, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -27.82, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": -23.04, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.4333, "angle": -4.54, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7, "angle": 9.01, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": -27.19}]}, "mpmingzu43": {"rotate": [{"angle": -14.45, "curve": 0.355, "c2": 0.41, "c3": 0.699, "c4": 0.78}, {"time": 0.1667, "angle": -24.76, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2667, "angle": -27.82, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4333, "angle": -21.03, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.9333, "angle": 9.01, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -14.45}]}, "mpmingzu44": {"rotate": [{"angle": 0.06, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.1333, "angle": 1.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3333, "angle": 2.79, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 0.06}]}, "mpmingzu45": {"rotate": [{"angle": -2.69, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": -1.99, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.2667, "angle": -0.68, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667, "angle": 2.79, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.69}]}, "mpmingzu46": {"rotate": [{"angle": -0.77, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.1333, "angle": -2.35, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.2, "angle": -2.69, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2667, "angle": -2.35, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.8667, "angle": 5.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -0.77}]}, "mpmingzu47": {"rotate": [{"angle": -10.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -15.33, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.6, "angle": 2.47, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.8333, "angle": 10.26, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -10.61}]}, "mpmingzu48": {"rotate": [{"angle": 29.05, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6, "angle": 1.32, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.7333, "angle": 8.09, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.2, "angle": 33.39, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 29.05}]}, "mpmingzu49": {"rotate": [{"angle": 33.21, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": 37.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": -0.74, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.7333, "angle": -8.39, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.7667, "angle": -9.18, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": 33.21}]}, "mpmingzu50": {"rotate": [{"angle": 13.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 37.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6, "angle": 19.99, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.7333, "angle": 7.83, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1, "angle": -9.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 13.93}]}, "mpmingzu52": {"rotate": [{"angle": -0.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -2.73, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 4.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -0.67}]}, "mpmingzu53": {"rotate": [{"angle": 6.66, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": 3.71, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": -7.31, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 7.24, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": 6.66}]}, "mpmingzu54": {"rotate": [{"angle": 2.78, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": 6.82, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": 7.24, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -17.15, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 2.78}]}, "mpmingzu55": {"rotate": [{"angle": -8.16, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -1.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 7.24, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -17.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -8.16}]}, "mpmingzu56": {"rotate": [{"angle": -6.22, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": -7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 5.37, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.2, "angle": -3.39, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": -6.22}]}, "mpmingzu57": {"rotate": [{"angle": 5.32, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -11.09, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 10.56, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.2, "angle": 9.7, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 1.3333, "angle": 5.32}]}, "mpmingzu58": {"rotate": [{"angle": 16.47, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 16.97, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -12.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2, "angle": 11.55, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 1.3333, "angle": 16.47}]}, "mpmingzu59": {"rotate": [{"angle": 8.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 20.13, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -12.71, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.2, "angle": -0.6, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 1.3333, "angle": 8.05}]}, "mpmingzu60": {"translate": [{"x": -3.28, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.28, "y": 0.04}]}, "mpmingzu66": {"rotate": [{"angle": 0.13}]}, "mpmingzu67": {"rotate": [{"angle": -0.42}]}, "mpmingzu68": {"translate": [{"x": -5.02, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -5.02, "y": 0.06}]}, "mpmingzu79": {"rotate": [{"angle": 0.13}]}, "mpmingzu80": {"rotate": [{"angle": -0.42}]}, "mpmingzu81": {"rotate": [{"angle": 2.09, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 4.21, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.76, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 2.09}]}, "mpmingzu82": {"rotate": [{"angle": -3.48, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": -1.86, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": 4.21, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -3.8, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -3.48}]}, "mpmingzu83": {"rotate": [{"angle": -4.54, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": -6.32, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": -6.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.21, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -4.54}]}, "mpmingzu84": {"translate": [{"x": -7.99, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.3333, "x": 2.73, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6, "x": 9.39, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -8.72, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "x": -7.99}]}, "mpmingzu85": {"translate": [{"x": -9.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "x": -17.45, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "x": -13.17, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8667, "x": 15.43, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": -9.48}]}, "ywyw": {"translate": [{"x": 11.18}]}, "ywyw3": {"translate": [{"x": -7.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "x": -12.62, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 13.91, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": -7.73}]}, "ywyw4": {"translate": [{"x": 0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -12.62, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 13.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.64}]}, "ywyw5": {"translate": [{"x": 9.02, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "x": -12.62, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 13.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": 9.02}]}, "ywyw6": {"translate": [{"x": 13.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -12.62, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 13.91}]}, "ywyw7": {"translate": [{"x": 9.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "x": 13.91, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -12.62, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 9.02}]}}, "deform": {"default": {"a2": {"a2": [{"vertices": [-13.42834, 6.01086, 0.37622, 13.34473, 8.27319, -1.52179, -5.53076, -8.8551]}]}, "a1": {"a1": [{"vertices": [-15.35583, -51.64233, -20.83356, 49.69312, 15.35773, 51.64954, 20.83533, -49.68567]}]}, "a3": {"a3": [{"vertices": [-7.68201, 3.64972, 1.37183, 8.45929, 7.78442, -3.61182, -1.26892, -8.42133]}]}}}}, "nai_he_qiao": {"slots": {"qiao": {"attachment": [{"name": "qiao"}]}}}, "niu_tou_ma_mian": {"slots": {"mamian013": {"attachment": [{"name": "mamian013"}]}, "fanren03": {"attachment": [{"name": "fanren03"}]}, "mamian04": {"attachment": [{"name": "mamian04"}]}, "fanren01": {"attachment": [{"name": "fanren01"}]}, "niutou010": {"attachment": [{"name": "niutou010"}]}, "niutou09": {"attachment": [{"name": "niutou09"}]}, "mamian010": {"attachment": [{"name": "mamian010"}]}, "niutou07": {"attachment": [{"name": "niutou07"}]}, "mamian07": {"attachment": [{"name": "mamian07"}]}, "mamian05": {"attachment": [{"name": "mamian05"}]}, "mamian011": {"attachment": [{"name": "mamian011"}]}, "niutou08": {"attachment": [{"name": "niutou08"}]}, "niutou011": {"attachment": [{"name": "niutou011"}]}, "mamian06": {"attachment": [{"name": "mamian06"}]}, "fanren08": {"attachment": [{"name": "fanren08"}]}, "niutou03": {"attachment": [{"name": "niutou03"}]}, "fanren05": {"attachment": [{"name": "fanren05"}]}, "niutou04": {"attachment": [{"name": "niutou04"}]}, "sd5": {"attachment": [{"name": "sd"}]}, "mamian012": {"attachment": [{"name": "mamian012"}]}, "fanren04": {"attachment": [{"name": "fanren04"}]}, "mamian08": {"attachment": [{"name": "mamian08"}]}, "mamian02": {"attachment": [{"name": "mamian02"}]}, "niutou01": {"attachment": [{"name": "niutou01"}]}, "mamian09": {"attachment": [{"name": "mamian09"}]}, "niutou02": {"attachment": [{"name": "niutou02"}]}, "fanren02": {"attachment": [{"name": "fanren02"}]}, "sd3": {"attachment": [{"name": "sd"}]}, "sd4": {"attachment": [{"name": "sd"}]}, "fanren07": {"attachment": [{"name": "fanren07"}]}, "fanren06": {"attachment": [{"name": "fanren06"}]}, "mamian03": {"attachment": [{"name": "mamian03"}]}, "niutou05": {"attachment": [{"name": "niutou05"}]}, "mamian01": {"attachment": [{"name": "mamian01"}]}, "niutou06": {"attachment": [{"name": "niutou06"}]}}, "bones": {"mamian2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.06, "y": 1.43, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.01, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian4": {"rotate": [{"angle": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.5}], "translate": [{"x": 0.09, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.32, "y": -0.49, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.09, "y": -0.14}]}, "mamian5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.56, "y": -0.28, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.53, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian7": {"rotate": [{"angle": -3.27, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.27}]}, "mamian8": {"rotate": [{"angle": -8.26, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.53, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -8.26}]}, "mamian9": {"rotate": [{"angle": -0.19}]}, "mamian10": {"rotate": [{"angle": -0.28}]}, "mamian11": {"rotate": [{"angle": 0.36}]}, "mamian12": {"rotate": [{"angle": -0.36}]}, "mamian13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.93, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.69, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian15": {"rotate": [{"angle": 7.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.98, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.15}]}, "mamian16": {"rotate": [{"angle": 2.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 9.98, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.83}]}, "mamian17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.98, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian18": {"rotate": [{"angle": 6.96, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 18.93, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 6.96}]}, "mamian19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 18.93, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian20": {"rotate": [{"angle": 1.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.68, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.61}]}, "mamian21": {"rotate": [{"angle": 4.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.68, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.07}]}, "mamian22": {"rotate": [{"angle": 3.67, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.67}]}, "mamian23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.05, "y": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian24": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.71, "y": -2.15, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mamian25": {"rotate": [{"angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.56, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.26}]}, "mamian26": {"rotate": [{"angle": 4.56, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.56}]}, "mamian38": {"rotate": [{"angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.65}]}, "mamian39": {"rotate": [{"angle": 2.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.62}]}, "mamian40": {"rotate": [{"angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.42}]}, "lianzi": {"translate": [{"y": 1.09, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": 1.2, "curve": 0.291, "c3": 0.63, "c4": 0.37}, {"time": 2, "y": 1.09}]}, "fanren2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.1, "y": 6.66, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "fanren7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "fanren8": {"rotate": [{"angle": 0.35, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.24, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.35}], "translate": [{"x": 0.22, "y": -0.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.77, "y": -0.04, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.22, "y": -0.01}]}, "fanren3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.06, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "fanren4": {"rotate": [{"angle": 2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2}]}, "fanren5": {"rotate": [{"angle": 0.49, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 7.06, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 0.49}]}, "fanren6": {"rotate": [{"angle": 3.21, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1333, "angle": 2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 7.06, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 3.21}]}, "fanren9": {"rotate": [{"angle": 0.89, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.24, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 0.89}], "translate": [{"x": 0.35, "y": -0.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.49, "y": -0.56, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.35, "y": -0.4}]}, "fanren10": {"rotate": [{"angle": -3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.04, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.13}]}, "fanren11": {"rotate": [{"angle": -7.91, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -7.91}]}, "fanren12": {"rotate": [{"angle": -11.04, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.04}]}, "fanren13": {"rotate": [{"angle": -7.91, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -11.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -7.91}]}, "fanren14": {"rotate": [{"angle": 0.79, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 11.41, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 0.79}]}, "fanren15": {"rotate": [{"angle": 5.19, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1333, "angle": 3.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 11.41, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 5.19}]}, "fanren16": {"rotate": [{"angle": 9.95, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.1333, "angle": 8.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 11.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 9.95}]}, "fanren17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 11.84, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "fanren18": {"rotate": [{"angle": 3.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.84, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.36}]}, "fanren19": {"rotate": [{"angle": 0.07, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 0.07}]}, "fanren20": {"rotate": [{"angle": 0.13, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 0.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8667, "angle": 0.28, "curve": 0.347, "c2": 0.38, "c3": 0.683, "c4": 0.73}, {"time": 2, "angle": 0.13}]}, "fanren21": {"rotate": [{"angle": 0.79, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 8.17, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.79}]}, "fanren22": {"rotate": [{"angle": 4.07, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "angle": 2.32, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 8.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 4.07}]}, "fanren23": {"rotate": [{"angle": 7.39, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.1667, "angle": 5.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 8.17, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 7.39}]}, "fanren24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.17, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "fanren25": {"rotate": [{"angle": 2.32, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.17, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.32}]}, "fanren26": {"rotate": [{"angle": 5.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 8.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 5.85}]}, "niutou2": {"rotate": [{"angle": 0.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.38}], "translate": [{"x": -0.03, "y": 0.35, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.09, "y": 1.24, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.03, "y": 0.35}]}, "niutou3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "niutou4": {"rotate": [{"angle": 0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.78}]}, "niutou5": {"rotate": [{"angle": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.97}]}, "niutou7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.6, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "niutou8": {"rotate": [{"angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.87}]}, "niutou9": {"rotate": [{"angle": -1.34, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": -6.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": -1.34}]}, "niutou10": {"rotate": [{"angle": 4.79}]}, "niutou11": {"rotate": [{"angle": -4.59}]}, "niutou12": {"rotate": [{"angle": 7.66}]}, "niutou13": {"rotate": [{"angle": -5.36}]}, "niutou15": {"translate": [{"x": -0.26, "y": -1.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.92, "y": -4.6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.26, "y": -1.31}]}, "niutou16": {"rotate": [{"angle": 0.77}]}, "niutou17": {"rotate": [{"angle": -0.12}]}, "niutou18": {"rotate": [{"angle": -0.86}]}, "niutou19": {"rotate": [{"angle": 0.49}]}, "niutou20": {"rotate": [{"angle": -0.7}]}, "niutou21": {"rotate": [{"angle": 2.22}]}}}}}