import { _decorator, Label, sp, tween, v3, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { math } from "cc";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
const { ccclass } = _decorator;

@ccclass("UIPlayerLevelUpRes")
export class UIPlayerLevelUpRes extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PLAYER}?prefab/ui/UIPlayerLevelUpRes`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private args: number[] = [];

  public init(args: any): void {
    super.init(args);
    this.args = args;
  }
  protected onEvtShow(): void {
    AudioMgr.instance.playEffect(AudioName.Effect.武将升级);
    this.playeAction();
    this.initPopup();
  }

  private playeAction() {
    this.getNode("bg").scale = v3(0, 0, 0);
    this.getNode("tytanchuang")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_gongxitisheng_1", true);
      });
    this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_gongxitisheng", false);
    tween(this.getNode("bg"))
      .to(0.1, { scale: v3(1, 1, 1) })
      .start();
  }

  private setNodeValue(node: Node, oldValue: number, newValue: number) {
    node.active = newValue > 0;

    if (node.active) {
      node.getChildByPath("layout_item/lbl_old_value").getComponent(Label).string = `${oldValue}`;
      node.getChildByPath("layout_item/lbl_new_value").getComponent(Label).string = `${newValue}`;

      node.getChildByPath("layout_item/node_up").active = newValue != oldValue;
      let color = math.color("FFFBE5");
      if (newValue != oldValue) {
        color = math.color("74FF77");
      }
      node.getChildByPath("layout_item/lbl_new_value").getComponent(Label).color = color;
    }
  }

  private initPopup() {
    let lv = PlayerModule.data.getPlayerInfo().level;
    let configLeader = PlayerModule.data.getConfigLeaderData(lv);
    let configLeaderPre = PlayerModule.data.getConfigLeaderData(lv - 1);

    this.getNode("level_lab").getComponent(Label).string = `${lv}`;
    this.getNode("level_name_lab").getComponent(Label).string = `${configLeader.name}`;

    this.setNodeValue(this.getNode("node_day_reward"), configLeaderPre.dayReward, configLeader.dayReward);
    this.setNodeValue(this.getNode("node_chat_max"), configLeaderPre.chatMax, configLeader.chatMax);
    this.setNodeValue(this.getNode("node_chat_time"), configLeaderPre.chatTime, configLeader.chatTime);
    this.setNodeValue(this.getNode("node_train_max"), configLeaderPre.trainMax, configLeader.trainMax);
    this.setNodeValue(this.getNode("node_train_reward"), configLeaderPre.trainReward, configLeader.trainReward);
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, this.args);
  }
}
