import org.apache.tools.ant.taskdefs.condition.Os

apply plugin: 'com.android.application'

RES_PATH = RES_PATH.replace("\\", "/")
COCOS_ENGINE_PATH = COCOS_ENGINE_PATH.replace("\\", "/")
buildDir = "${RES_PATH}/proj/build/instantapp"

android {
    compileSdkVersion PROP_COMPILE_SDK_VERSION.toInteger()
    buildToolsVersion PROP_BUILD_TOOLS_VERSION
    ndkPath PROP_NDK_PATH
    namespace APPLICATION_ID

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion PROP_MIN_SDK_VERSION
        targetSdkVersion PROP_TARGET_SDK_VERSION
        versionCode 1
        versionName "1.0"

        externalNativeBuild {
            cmake {
                targets "cocos"
                arguments "-DRES_DIR=${RES_PATH}", "-DCOCOS_X_PATH=${COCOS_ENGINE_PATH}","-DANDROID_STL=c++_static", "-DANDROID_TOOLCHAIN=clang", "-DANDROID_ARM_NEON=TRUE", "-DANDROID_LD=gold"
                cppFlags "-frtti -fexceptions -fsigned-char -DANDROID_INSTANT=1"
            }
            ndk { abiFilters PROP_APP_ABI.split(':') }
        }
    }

    sourceSets.main {
        java.srcDirs "../src", "src"
        res.srcDirs "../res", 'res', "${RES_PATH}/proj/res"
        jniLibs.srcDirs "../libs", 'libs'
        manifest.srcFile "AndroidManifest.xml"
        assets.srcDir "${RES_PATH}/data"
    }

    externalNativeBuild {
        cmake {
            version "3.22.1"
            path "../CMakeLists.txt"
            buildStagingDirectory "${RES_PATH}/proj/build"
        }
    }

    signingConfigs {

       release {
            if (project.hasProperty("RELEASE_STORE_FILE") && !RELEASE_STORE_FILE.isEmpty()) {
                storeFile file(RELEASE_STORE_FILE)
                storePassword RELEASE_STORE_PASSWORD
                keyAlias RELEASE_KEY_ALIAS
                keyPassword RELEASE_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        release {
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                signingConfig signingConfigs.release
            }

            externalNativeBuild {
                cmake {
                    // switch HIDE_SYMBOLS to OFF to skip compilation flag `-fvisibility=hidden`
                    arguments "-DHIDE_SYMBOLS=ON"
                }
            }
        }

        debug {
            debuggable true
            jniDebuggable true
            renderscriptDebuggable true
            // resValue  "string", "app_name", "${PROP_APP_NAME}-dbg"
            // applicationIdSuffix ".debug"
        }
    }
}

dependencies {
    implementation fileTree(dir: '../libs', include: ['*.jar','*.aar'])
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation fileTree(dir: "${COCOS_ENGINE_PATH}/cocos/platform/android/java/libs", include: ['*.jar'])
    implementation project(':libservice')
    implementation project(':libcocos')
}
