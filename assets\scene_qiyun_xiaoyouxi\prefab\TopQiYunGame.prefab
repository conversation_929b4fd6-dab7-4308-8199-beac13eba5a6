[{"__type__": "cc.Prefab", "_name": "TopQiYunGame", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "TopQiYunGame", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 142}, {"__id__": 148}, {"__id__": 156}, {"__id__": 190}], "_active": true, "_components": [{"__id__": 196}, {"__id__": 198}, {"__id__": 200}], "_prefab": {"__id__": 202}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "8388dcff-c38f-4f75-aa4a-9acf9d9d1c5a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "31459jGJBEUIpKhFqDW7/G", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 12}, {"__id__": 14}, {"__id__": 16}, {"__id__": 18}, {"__id__": 20}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 30}, {"__id__": 32}, {"__id__": 34}, {"__id__": 36}, {"__id__": 38}, {"__id__": 40}, {"__id__": 42}, {"__id__": 44}, {"__id__": 46}, {"__id__": 48}, {"__id__": 50}, {"__id__": 52}, {"__id__": 54}, {"__id__": 56}, {"__id__": 58}, {"__id__": 60}, {"__id__": 62}, {"__id__": 64}, {"__id__": 66}, {"__id__": 68}, {"__id__": 70}, {"__id__": 72}, {"__id__": 74}, {"__id__": 76}, {"__id__": 78}, {"__id__": 80}, {"__id__": 82}, {"__id__": 84}, {"__id__": 86}, {"__id__": 88}, {"__id__": 90}, {"__id__": 92}, {"__id__": 94}, {"__id__": 96}, {"__id__": 98}, {"__id__": 100}, {"__id__": 102}, {"__id__": 104}, {"__id__": 106}, {"__id__": 108}, {"__id__": 110}, {"__id__": 112}, {"__id__": 114}, {"__id__": 116}, {"__id__": 118}, {"__id__": 120}, {"__id__": 122}, {"__id__": 124}, {"__id__": 126}, {"__id__": 128}, {"__id__": 130}, {"__id__": 132}, {"__id__": 134}, {"__id__": 136}, {"__id__": 138}, {"__id__": 140}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "XiaoyouxiUIRun"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 11}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16/JfINE5E+bC06wRRV88c", "fdmOg612FO7JjYx9CWPPGB", "3dC8EDwk1Cy7uh2evMILBY"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 13}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["2cMqr1UV1JVqhVSgl3nkbt", "b4pniGzExEv6oo90U/hRdj", "10GI/u7XBAxZbFFWKllOZT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 15}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["50lkr7Yu9Nm77S+t45sRYZ", "b4pniGzExEv6oo90U/hRdj", "10GI/u7XBAxZbFFWKllOZT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["19JNpy3h1MdoFbnsYSB/Ky", "b4pniGzExEv6oo90U/hRdj", "10GI/u7XBAxZbFFWKllOZT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["3apKi/s+tKvo39bllkhTII", "b4pniGzExEv6oo90U/hRdj", "10GI/u7XBAxZbFFWKllOZT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 21}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["00hwGJGlxOFqBUKvzj1DDF", "b4pniGzExEv6oo90U/hRdj", "10GI/u7XBAxZbFFWKllOZT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["f9BCRmZhdAl5L51MFGY/B6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_top"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["f7uMTAIORNzay//Ky6fzql"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 29}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["f3J80CGIFJaK9xxbzAHrCs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["43vXl6ZDdGwIvqa9j5wVRf"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16/JfINE5E+bC06wRRV88c", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 35}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["f8PmHuKR5OHoPerjX9IhdG", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 37}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["ccOs4ydDhNupx+XOW0C26Q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["62J17CeUdF16MQOwHcyrbl", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 41}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["7aVVfKTYhGWa//gtDSXC/F"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 43}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["32qgYm6QdFoLrbMZjrydkG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["dcWFbk3hVGOIxqP/bJqoAN"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["9apHrSGy1LjbpnNidtnBv3"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["c7zQ8ZjaFPqKYAcuUdPTO7"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16/JfINE5E+bC06wRRV88c", "35/NF5jdZBEpnHGN6PaS7x"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 53}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16/JfINE5E+bC06wRRV88c", "fdmOg612FO7JjYx9CWPPGB", "29AFQuiVpLyoV65rdbF3z7"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["f8PmHuKR5OHoPerjX9IhdG", "35/NF5jdZBEpnHGN6PaS7x"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["02hWpG0BBHvKUTcpNj7JRv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["2cMqr1UV1JVqhVSgl3nkbt", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 61}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["50lkr7Yu9Nm77S+t45sRYZ", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["19JNpy3h1MdoFbnsYSB/Ky", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["3apKi/s+tKvo39bllkhTII", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["00hwGJGlxOFqBUKvzj1DDF", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 69}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["62J17CeUdF16MQOwHcyrbl", "35/NF5jdZBEpnHGN6PaS7x"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["76wfcdPgVCYZUpQMMcM9au"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["90wqjYkxJKvYN2Ru5n4gGM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 75}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16/JfINE5E+bC06wRRV88c", "038xZW3YtPYpt8i1CQfGO/"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16/JfINE5E+bC06wRRV88c", "f8mAhakrVFhLjZahASGMz1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16/JfINE5E+bC06wRRV88c", "a9OJxdQjFJx7EseyFmS2bE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16/JfINE5E+bC06wRRV88c", "81hyphVY5HEJTWLmYXxxHz"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16/JfINE5E+bC06wRRV88c", "49y8A22XhGIZnEz8qYXUcQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 85}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["f8PmHuKR5OHoPerjX9IhdG", "0auoPwY89CBbGntgGmSWOd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["2cMqr1UV1JVqhVSgl3nkbt", "9dju6QqXxJebXVlgrAJtUX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 89}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["2cMqr1UV1JVqhVSgl3nkbt", "b4pniGzExEv6oo90U/hRdj", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 91}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["50lkr7Yu9Nm77S+t45sRYZ", "9dju6QqXxJebXVlgrAJtUX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 93}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["50lkr7Yu9Nm77S+t45sRYZ", "b4pniGzExEv6oo90U/hRdj", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 95}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["19JNpy3h1MdoFbnsYSB/Ky", "9dju6QqXxJebXVlgrAJtUX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["19JNpy3h1MdoFbnsYSB/Ky", "b4pniGzExEv6oo90U/hRdj", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["3apKi/s+tKvo39bllkhTII", "9dju6QqXxJebXVlgrAJtUX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 101}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["3apKi/s+tKvo39bllkhTII", "b4pniGzExEv6oo90U/hRdj", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["00hwGJGlxOFqBUKvzj1DDF", "9dju6QqXxJebXVlgrAJtUX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["00hwGJGlxOFqBUKvzj1DDF", "b4pniGzExEv6oo90U/hRdj", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 107}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["62J17CeUdF16MQOwHcyrbl", "e615Me2GRLH7sPvGL2GCdj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 109}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["2cMqr1UV1JVqhVSgl3nkbt", "a7Trna/JhBSKxZaHAwR08f"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["2cMqr1UV1JVqhVSgl3nkbt", "0db23ySb9IOoBOyXG9DB+4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 113}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["50lkr7Yu9Nm77S+t45sRYZ", "a7Trna/JhBSKxZaHAwR08f"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 115}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["50lkr7Yu9Nm77S+t45sRYZ", "0db23ySb9IOoBOyXG9DB+4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 117}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["19JNpy3h1MdoFbnsYSB/Ky", "a7Trna/JhBSKxZaHAwR08f"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["19JNpy3h1MdoFbnsYSB/Ky", "0db23ySb9IOoBOyXG9DB+4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 121}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["3apKi/s+tKvo39bllkhTII", "a7Trna/JhBSKxZaHAwR08f"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["3apKi/s+tKvo39bllkhTII", "0db23ySb9IOoBOyXG9DB+4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["00hwGJGlxOFqBUKvzj1DDF", "a7Trna/JhBSKxZaHAwR08f"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 127}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["00hwGJGlxOFqBUKvzj1DDF", "0db23ySb9IOoBOyXG9DB+4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["defaultAnimation"], "value": "boss_idle"}, {"__type__": "cc.TargetInfo", "localID": ["62J17CeUdF16MQOwHcyrbl", "d3y6RVqFRAS5JUUA1k4csq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0.5031446540880503, "y": 0.00684931506849315}}, {"__type__": "cc.TargetInfo", "localID": ["2cMqr1UV1JVqhVSgl3nkbt", "81dE4lDeZFeZWTUTp1hDuu"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0.5031446540880503, "y": 0.00684931506849315}}, {"__type__": "cc.TargetInfo", "localID": ["50lkr7Yu9Nm77S+t45sRYZ", "81dE4lDeZFeZWTUTp1hDuu"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 135}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0.5031446540880503, "y": 0.00684931506849315}}, {"__type__": "cc.TargetInfo", "localID": ["19JNpy3h1MdoFbnsYSB/Ky", "81dE4lDeZFeZWTUTp1hDuu"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 137}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0.5031446540880503, "y": 0.00684931506849315}}, {"__type__": "cc.TargetInfo", "localID": ["3apKi/s+tKvo39bllkhTII", "81dE4lDeZFeZWTUTp1hDuu"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 139}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0.5031446540880503, "y": 0.00684931506849315}}, {"__type__": "cc.TargetInfo", "localID": ["00hwGJGlxOFqBUKvzj1DDF", "81dE4lDeZFeZWTUTp1hDuu"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 141}, "propertyPath": ["defaultAnimation"], "value": "xiaoguai1_1"}, {"__type__": "cc.TargetInfo", "localID": ["2cMqr1UV1JVqhVSgl3nkbt", "5ay+6WlBtPL5scI6de12cX"]}, {"__type__": "cc.Node", "_name": "bg_cloud", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 143}, {"__id__": 145}], "_prefab": {"__id__": 147}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -388, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1.2, "z": 1}, "_mobility": 0, "_layer": 16, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": {"__id__": 144}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 754}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76QVg7aqpNqYsj5JOHl/Mf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": {"__id__": 146}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "97c5000b-8425-4b22-85a9-08fbc5a86372@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cd7ZqRKs1EZ66u75ugj4/u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48vVpG7ClEy6N531+9156K", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 149}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 148}, "asset": {"__uuid__": "3bdd6d0f-4916-4c68-9349-8b96005b0e41", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 150}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "acVLK8Di5AH71Zs42hEbJl", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 151}, {"__id__": 153}, {"__id__": 154}, {"__id__": 155}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 152}, "propertyPath": ["_name"], "value": "XiaoyouxiUIMain"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 152}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 152}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 152}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 157}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 156}, "asset": {"__uuid__": "5df3cefb-0b95-41d2-927e-b55fadf919ea", "__expectedType__": "cc.Prefab"}, "fileId": "39Upso2GxFKK0t4xCh7rAv", "instance": {"__id__": 158}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "6eC7cwZUtMTaZyzfabdUPr", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 159}, {"__id__": 161}, {"__id__": 162}, {"__id__": 163}, {"__id__": 164}, {"__id__": 166}, {"__id__": 167}, {"__id__": 168}, {"__id__": 170}, {"__id__": 172}, {"__id__": 174}, {"__id__": 176}, {"__id__": 178}, {"__id__": 180}, {"__id__": 182}, {"__id__": 184}, {"__id__": 186}, {"__id__": 188}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_name"], "value": "XiaoyouxiUIFight"}, {"__type__": "cc.TargetInfo", "localID": ["39Upso2GxFKK0t4xCh7rAv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 750, "height": 1500.0000000000002}}, {"__type__": "cc.TargetInfo", "localID": ["58myWQm41LUo8XL+DBvmEV"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 169}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["c4ziTd1ntM9qlmUFRUGa9Y"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 171}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["81cKsNJ8xDp7ChWtsmw2kt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 173}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["0bJsY+GMpFvbDORVyCJdIq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 175}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["a5xoD+G25AfY4kTthxaiyN"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 177}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["bd41rUTpFEi6g1y1XTJZ/J"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["4dsu/siQNOjJurfjp53YtM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["c7GB2z0IFK6KlvzAsoeMce"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 183}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["0137COXf9O0ouvcIL5VQlY"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 185}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["dfhwR8NCpKYIfRU8IUYV2d"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 187}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["98tK5bC3JIjKis/GbqcTPT", "e6JsnTKLBB1qO+JSaP8oUn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["_layer"], "value": 16}, {"__type__": "cc.TargetInfo", "localID": ["98tK5bC3JIjKis/GbqcTPT", "c0+US56oJCO4RQAay2MCs2"]}, {"__type__": "cc.Node", "_name": "camera_ui_game", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 191}, {"__id__": 193}], "_prefab": {"__id__": 195}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": {"__id__": 192}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1diJ8PEEhEi4o8Rtc4MVQF"}, {"__type__": "cc.Camera", "_name": "Camera<CameraComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": {"__id__": 194}, "_projection": 0, "_priority": 3, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 750, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 8, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61MjLsdNpNyI9fK0CkKPS0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2aofC5g35D3ptKdAOIUrAp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 197}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36eGwJyrtKIrC1P9L9JhYM"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 199}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acS4jhoqtKrKVBw9RhdrRk"}, {"__type__": "778f7jNMbZN74d0odIDVTfi", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 201}, "nodeFight": {"__id__": 156}, "nodeRun": {"__id__": 2}, "nodeUI": {"__id__": 148}, "nodeCamera": {"__id__": 190}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eKw5cjcNM/YeJwDYZOrfc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 156}, {"__id__": 148}, {"__id__": 2}]}]