import { _decorator, Component, EditBox, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ClubModule } from "../../../module/club/ClubModule";
import { BoolValue } from "../../net/protocol/ExternalMessage";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Tue Aug 13 2024 20:23:20 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubSetting.ts
 *
 */

@ccclass("UIClubSetting")
export class UIClubSetting extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubSetting`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.refreshUI();
  }
  private refreshUI() {
    let manual = ClubModule.data.clubMessage.auditOption.manual;
    let autoRefuse = ClubModule.data.clubMessage.auditOption.autoRefuse;
    let levelLowerLimit = ClubModule.data.clubMessage.auditOption.levelLowerLimit;
    let powerLowerLimit = ClubModule.data.clubMessage.auditOption.powerLowerLimit;
    this.getNode("lv_edit").getComponent(EditBox).string = `${levelLowerLimit}`;
    this.getNode("power_edit").getComponent(EditBox).string = `${powerLowerLimit}`;
    this.getNode("item1_check_box").getChildByName("check").active = manual;
    this.getNode("item2_check_box").getChildByName("check").active = autoRefuse;
  }
  private on_click_item1_check_box(event: TouchEvent) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let active = this.getNode("item1_check_box").getChildByName("check").active;
    this.getNode("item1_check_box").getChildByName("check").active = !active;
    if (!active) {
      this.getNode("item2_check_box").getChildByName("check").active = false;
    }
  }
  private on_click_item2_check_box() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let active = this.getNode("item2_check_box").getChildByName("check").active;
    this.getNode("item2_check_box").getChildByName("check").active = !active;
    if (!active) {
      this.getNode("item1_check_box").getChildByName("check").active = false;
    }
  }
  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_btn_commit() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let manual = this.getNode("item1_check_box").getChildByName("check").active;
    let autoRefuse = this.getNode("item2_check_box").getChildByName("check").active;
    let levelLowerLimit = this.getNode("lv_edit").getComponent(EditBox).string;
    let powerLowerLimit = this.getNode("power_edit").getComponent(EditBox).string;
    ClubModule.api.updateAudit(
      manual,
      autoRefuse,
      parseInt(levelLowerLimit),
      parseInt(powerLowerLimit),
      (data: BoolValue) => {
        if (data) {
          TipMgr.showTip("设置成功");
        }
      }
    );
  }
}
