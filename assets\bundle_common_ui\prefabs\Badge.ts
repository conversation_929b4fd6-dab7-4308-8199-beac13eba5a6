import { _decorator, Component } from "cc";
import MsgMgr from "../../GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../GameScrpit/game/event/MsgEnum";
import { BadgeMgr } from "../../GameScrpit/game/mgr/BadgeMgr";
import { Node } from "cc";
const { ccclass, property } = _decorator;

@ccclass("Badge")
export class Badge extends Component {
  private id: number = -1;

  @property(Node)
  bg: Node;

  start() {
    MsgMgr.on(MsgEnum.ON_BADGE_UPDATE, this.updateCallback, this);
    this.updateCallback();
  }

  private updateCallback() {
    this.bg.active = BadgeMgr.instance.isShow(this.id);
  }

  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_BADGE_UPDATE, this.updateCallback, this);
  }

  public setId(id: number) {
    this.id = id;
    this.scheduleOnce(() => {
      this.updateCallback();
    });
  }
}
