import { _decorator, Component, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

@ccclass("UILevelBossPower")
export class UILevelBossPower extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FIGHT}?prefab/ui/UILevelBossPower`;
  }
  private on_click_btn_close() {
    UIMgr.instance.back();
  }

  private on_click_btn_close_lan() {
    UIMgr.instance.back();
  }
}
