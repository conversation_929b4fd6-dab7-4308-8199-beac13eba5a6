import { _decorator, isValid } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { IConfigGuideV2 } from "../game/JsonDefine";
import { Sleep } from "../game/GameDefine";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { RouteCtrl } from "../../platform/src/core/RouteCtrl";
import { BundleEnum } from "../game/bundleEnum/BundleEnum";
import { MainTaskModule } from "../module/mainTask/MainTaskModule";
import { PlayerModule } from "../module/player/PlayerModule";
import { TipsMgr } from "../../platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import GuideMgr from "./GuideMgr";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

const enum GuideStatusEnum {
  NEXT = 0,
  SYSTEM_OPEN = 1,
  PLAY_ANI = 2,
  PLAY_TALK = 3,
}

const enum TopEventRouteEnum {
  TopEventFixNvWa = "TopEventFixNvWa",
  TopEventTalk = "TopEventTalk",
  TopFinger = "TopFinger",
  TopEventHeroCome = "TopEventHeroCome",
}

@ccclass("TopEventRoute")
export class TopEventRoute extends BaseCtrl {
  public static instance: TopEventRoute;

  @property(RouteCtrl)
  private routeCtrl: RouteCtrl;

  // 是否需要修正
  private _needRepare: boolean = false;

  // 引导步骤列表
  private _configGuideV2List: IConfigGuideV2[] = [];

  // 当前引导步骤
  private idx: number = 0;

  // 状态
  public status: GuideStatusEnum = GuideStatusEnum.NEXT;

  public init(args: any) {
    TopEventRoute.instance = this;

    // 修正引导ID
    let fromId = args.from;
    this._needRepare = args.needRepare;
    if (this._needRepare) {
      fromId = this.repireGuideId(args.from);
    }

    this._configGuideV2List = JsonMgr.instance.getConfigGuideV2List(fromId, args.to);
  }

  start() {
    super.start();

    if (!this._configGuideV2List?.length) {
      console.error("没有找到引导配置");
      this.closeBack();
      return;
    }

    this.routeCtrl.regRoute(TopEventRouteEnum.TopEventFixNvWa, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopEventFixNvWa",
      nextHop: [],
      exit: "",
    });

    this.routeCtrl.regRoute(TopEventRouteEnum.TopEventTalk, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopEventTalk",
      nextHop: [],
      exit: "",
    });

    this.routeCtrl.regRoute(TopEventRouteEnum.TopFinger, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopFinger",
      isSingleton: true,
      nextHop: [],
      exit: "",
    });

    this.routeCtrl.regRoute(TopEventRouteEnum.TopEventHeroCome, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopEventHeroCome",
      isSingleton: true,
      nextHop: [],
      exit: "",
    });

    this.showNext();
  }

  // 修正引导ID
  private repireGuideId(fromId: number): number {
    let isRepire = false;
    // 任务进度
    let taskMainId = MainTaskModule.data.mainTaskMsg.taskTimelineId;
    let idList = Object.keys(JsonMgr.instance.jsonList.c_guideV2);
    let idMax = JsonMgr.instance.jsonList.c_guideV2[idList[idList.length - 1]].id;

    // 取上一个任务，发现未完成任务就回退进度
    for (let i = fromId; i > 0; i--) {
      let cfg = JsonMgr.instance.jsonList.c_guideV2[i];
      if (!cfg) {
        return fromId;
      }
      if (cfg.type === 4) {
        if (taskMainId < Number(cfg.args)) {
          fromId = i;
          isRepire = true;
          log.warn("回退进度", fromId);
        }
      }
    }

    if (!isRepire) {
      // 取下一个任务，发现已完成就前进
      for (let i = fromId; i <= idMax; i++) {
        let cfg = JsonMgr.instance.jsonList.c_guideV2[i];
        if (!cfg) {
          continue;
        }

        if (cfg.type === 4) {
          if (taskMainId > Number(cfg.args)) {
            fromId = i + 1;
            isRepire = true;
            log.warn("进度提前", fromId);
          }
        }
      }
    }

    if (isRepire) {
      log.warn("修正进度", fromId);
      PlayerModule.api.updateGuideId(fromId);
    }
    return fromId;
  }

  private showNext() {
    if (isValid(this.node) === false) {
      return;
    }

    TipsMgr.setEnableTouch(false, 0.2);
    let configGuideV2 = this._configGuideV2List[this.idx];
    if (!configGuideV2) {
      TipsMgr.setEnableTouch(true);
      this.closeBack();
      return;
    }

    if (configGuideV2.id === 14) {
      this.routeCtrl.show(TopEventRouteEnum.TopEventFixNvWa, null, () => {
        this.idx++;
        this.status = GuideStatusEnum.NEXT;
        this.showNext();
      });
    } else if (configGuideV2.type === 5) {
      GuideMgr.startGuide({
        stepId: Number(configGuideV2.args),
        isForce: configGuideV2.force === 1,
      });

      GuideMgr.addCallBack(() => {
        this.idx++;
        this.status = GuideStatusEnum.NEXT;
        this.showNext();
      });
    } else if (configGuideV2.type === 4) {
      let fixId = configGuideV2.id;
      if (this._needRepare) {
        fixId = this.repireGuideId(configGuideV2.id);
      }

      if (fixId !== configGuideV2.id) {
        log.warn("修正进度," + configGuideV2.id + " - ", fixId);
        let toId = this._configGuideV2List[this._configGuideV2List.length - 1].id;
        this.idx = 0;
        this._configGuideV2List = JsonMgr.instance.getConfigGuideV2List(fixId, toId);
        this.showNext();
        return;
      }

      let needCount = MainTaskModule.service.getNeedCount();
      let complateNum = MainTaskModule.service.getCompleteNum();

      if (needCount > complateNum) {
        // 做任务
        log.log("做任务");
        GuideMgr.startGuide({ stepId: 49, isForce: configGuideV2.force === 1 });

        GuideMgr.addCallBack(async () => {
          while (GuideMgr.isGuiding) {
            await Sleep(0.1);
          }
          this.showNext();
        });
      } else {
        log.log("领取奖励");
        // 领取奖励
        log.info(" guide 4 50");
        GuideMgr.startGuide({ stepId: 50, isForce: configGuideV2.force === 1 });
        GuideMgr.addCallBack(() => {
          this.idx++;
          this.status = GuideStatusEnum.NEXT;

          PlayerModule.api.updateGuideId(
            configGuideV2.id,
            () => {
              this.showNext();
            },
            () => {
              this.showNext();
              return true;
            }
          );
        });
      }
    } else if (configGuideV2.type === 2) {
      this.routeCtrl.show(configGuideV2.args, null, null, () => {
        this.idx++;
        this.status = GuideStatusEnum.NEXT;
        this.showNext();
      });
    } else if (configGuideV2.type === 1) {
      let cfgList = [configGuideV2];
      for (let i = this.idx + 1; i < this._configGuideV2List.length; i++) {
        let cfg = this._configGuideV2List[i];
        if (cfg.type === 1) {
          this.idx++;
          cfgList.push(cfg);
        } else {
          break;
        }
      }

      this.routeCtrl.show(TopEventRouteEnum.TopEventTalk, { cfgList }, () => {
        this.status = GuideStatusEnum.NEXT;
        this.idx++;

        PlayerModule.api.updateGuideId(
          configGuideV2.id,
          () => {
            this.showNext();
          },
          () => {
            this.showNext();
            return true;
          }
        );
      });
    }
  }

  protected onDestroy(): void {
    TopEventRoute.instance = null;
    super.onDestroy();
  }
}
