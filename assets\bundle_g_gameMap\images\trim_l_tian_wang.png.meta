{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "a9616afc-8e36-48ed-b434-d8a8b4720f50", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "a9616afc-8e36-48ed-b434-d8a8b4720f50@6c48a", "displayName": "trim_l_tian_wang", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "a9616afc-8e36-48ed-b434-d8a8b4720f50", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "a9616afc-8e36-48ed-b434-d8a8b4720f50@f9941", "displayName": "trim_l_tian_wang", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 117, "height": 116, "rawWidth": 117, "rawHeight": 116, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-58.5, -58, 0, 58.5, -58, 0, -58.5, 58, 0, 58.5, 58, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 116, 117, 116, 0, 0, 117, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-58.5, -58, 0], "maxPos": [58.5, 58, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "a9616afc-8e36-48ed-b434-d8a8b4720f50@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "a9616afc-8e36-48ed-b434-d8a8b4720f50@6c48a"}}