import { _decorator } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { Base<PERSON>ttr, BaseAttrList, PageZero } from "../../GameDefine";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import { Label } from "cc";
import { SoulModule } from "../../../module/soul/SoulModule";
import Formate from "../../../lib/utils/Formate";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UISoulGetPop")
export class UISoulGetPop extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SOUL}?prefab/ui/UISoulGetPop`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private soulId: number;

  public init(args: any): void {
    super.init(args);
    this.soulId = args.soulId;
  }

  protected onEvtShow(): void {
    let soulMsg = SoulModule.data.warriorSoulManageMsg.soulMap[this.soulId];
    let itemConfig = JsonMgr.instance.getConfigItem(soulMsg.soulTemplateId);

    ToolExt.setItemBg(this.getNode("iconBg"), itemConfig.color);
    FmUtils.setItemIcon(this.getNode("soul_icon"), soulMsg.soulTemplateId);

    this.getNode("lbl_name").getComponent(Label).string = itemConfig.name;

    let attrMain: number[] = [];

    Object.keys(soulMsg.attrMap).forEach((idx) => {
      let k = Number(idx);

      // 排除基础属性
      if (BaseAttrList.indexOf(k) >= 0) {
        return;
      }

      //取最大值为主属性
      if (attrMain.length == 0 || attrMain[1] < soulMsg.attrMap[k]) {
        attrMain = [k, soulMsg.attrMap[k]];
      }
    });

    this.getNode("lbl_main_attr").getComponent(Label).string = Formate.formatAttribute(attrMain[0], attrMain[1]);
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
