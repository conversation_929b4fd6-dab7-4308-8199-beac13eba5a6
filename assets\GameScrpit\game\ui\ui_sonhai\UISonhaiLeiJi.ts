import {
  _decorator,
  Animation,
  Color,
  Component,
  isValid,
  Label,
  Node,
  ScrollView,
  Sprite,
  Tween,
  tween,
  UIOpacity,
  UITransform,
  v3,
} from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { SonhaiModule, sonhai_achieveId, sonhai_activityId } from "../../../module/sonhai/SonhaiModule";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AchieveRewardRequest } from "../../net/protocol/Activity";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { Check, SONHAI_LJAWARD_State, SonhaiMsgEnum } from "../../../module/sonhai/SonhaiConfig";
import { dtTime } from "../../BoutStartUp";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { LangMgr } from "../../mgr/LangMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import { AdventureAchieveVO } from "../../../module/activity/ActivityConfig";
import { Sleep } from "../../GameDefine";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UISonhaiLeiJi")
export class UISonhaiLeiJi extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_SONHAI}?prefab/ui/UISonhaiLeiJi`;
  }

  private _achieveVO: AdventureAchieveVO = null;

  protected onEvtShow() {
    tween(this.node)
      .delay(dtTime)
      .call(async () => {
        {
          let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
          log.log("getBeShow=================", db);
          for (let i = 0; i < db.achieveVOList.length; i++) {
            if (db.achieveVOList[i].id == sonhai_achieveId) {
              this._achieveVO = db.achieveVOList[i];
              break;
            }
          }
          this.loadBottom();
          this.loadBeShow();
        }
      })
      .start();
  }

  private async loadBottom() {
    let list = await SonhaiModule.service.newLjList();

    let count = SonhaiModule.data.getAchieveMap(sonhai_achieveId).targetVal;
    for (let i = 0; i < list.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) return;

      let param = list[i].param;
      let state = list[i].state;

      let node = this.getNode("bottom_content").children[i];
      if (!node) {
        node = ToolExt.clone(this.getNode("task_item"), this);
        node.setPosition(v3(0, 0, 0));
        this.getNode("bottom_content").addChild(node);
        node.active = true;
      }

      let rewardList = ToolExt.traAwardItemMapList(list[i].basicReward);
      let info = rewardList[0];
      FmUtils.setItemNode(node["task_award"], info.id, info.num);

      node["lbl_task_count"].getComponent(Label).string = LangMgr.txMsgCode(532, [count], "累计探险");
      node["task_count"].getComponent(Label).string = Math.min(param.require, count) + "/" + param.require;
      let bar = count / param.require;
      node["bar"].getComponent(Sprite).fillRange = bar;
      if (state == SONHAI_LJAWARD_State.未达成) {
        node["btn_get_award"].active = false; //隐藏领取按钮
        node["btn_go_task"].active = true; //显示前往按钮
        node["btn_yilingqu"].active = false; //隐藏领取过按钮
      } else if (state == SONHAI_LJAWARD_State.领取过) {
        node["btn_get_award"].active = false; //隐藏领取按钮
        node["btn_go_task"].active = false; //隐藏前往按钮
        node["btn_yilingqu"].active = true; //显示领取过按钮
      } else if (state == SONHAI_LJAWARD_State.可以领取) {
        node["btn_get_award"].active = true; //显示领取按钮
        node["btn_go_task"].active = false; //隐藏前往按钮
        node["btn_yilingqu"].active = false; //隐藏领取过按钮
      } else if (state == SONHAI_LJAWARD_State.异常状态) {
        node["btn_get_award"].active = false; //隐藏领取按钮
        node["btn_go_task"].active = false; //隐藏前往按钮
        node["btn_yilingqu"].active = false; //隐藏领取过按钮
      }
      node["btn_get_award"]["param"] = param;
    }
  }

  /**创建 */
  private async loadBeShow() {
    let showList = this._achieveVO.showList;
    let basicRewardList = this._achieveVO.basicRewardList;
    let requireList = this._achieveVO.requireList;
    for (let i = 0; i < showList.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) return;

      let param = new Check();
      param.index = showList[i];
      param.require = requireList[showList[i]];

      let node = this.getNode("content_top_award_layer").children[i];
      if (!node) {
        node = ToolExt.clone(this.getNode("Item"), this);
        this.getNode("content_top_award_layer").addChild(node);
        node.setPosition(v3(0, 5, 0));
        node.active = true;
      }
      let info = basicRewardList[param.index];
      FmUtils.setItemNode(node, info[0], info[1]);
      node.getChildByName("lbl_hit").getComponent(Label).string = "探险" + param.require;

      node["btn_top_item_get"]["param"] = param;
      let state = SonhaiModule.service.checkGet(param);
      if (state == SONHAI_LJAWARD_State.领取过) {
        this.award_snake(node, false);
        this.set_is_gray(node, false);
        node["btn_yidacheng"].active = false;
        node["btn_top_item_get"].active = false;
        node["spr_gou"].active = true;
      } else if (state == SONHAI_LJAWARD_State.可以领取) {
        this.award_snake(node, true);
        this.set_is_gray(node, true);
        node["btn_yidacheng"].active = true;
        node["btn_top_item_get"].active = true;
        node["spr_gou"].active = false;
      } else {
        this.award_snake(node, false);
        this.set_is_gray(node, true);
        node["btn_yidacheng"].active = false;
        node["btn_top_item_get"].active = false;
        node["spr_gou"].active = false;
      }
    }
  }

  private award_snake(targetNode: Node, is: boolean) {
    // 获取目标节点的初始位置
    const nodeStartPos = targetNode.getPosition();
    if (!targetNode["nodeStartPos"]) {
      targetNode["nodeStartPos"] = nodeStartPos;
    }
    let ani = targetNode.getComponent(Animation);
    if (is == false) {
      ani.stop();
      targetNode.setRotation(0, 0, 0, 0);
      return;
    }
    targetNode.setRotation(0, 0, 0, 0);
    ani.play("ani_dou");
  }

  private set_is_gray(node: Node, bool: boolean) {
    let color = new Color(100, 100, 100, 255);
    if (bool == true) {
      color = new Color(255, 255, 255, 255);
    }
    node["bg_color"].getComponent(Sprite).color = color;
    node["bg_icon"].getComponent(Sprite).color = color;
  }

  on_click_btn_get_award(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let param: AchieveRewardRequest = {
      /** 基金的ID */
      activityId: sonhai_activityId,
      /** 成就ID */
      achieveId: this._achieveVO.id,
      /** 领取的索引处对应的奖励 从0开始 */
      index: event.target.param.index,
      takeAll: true,
    };
    log.log("山海累计次数领取参数", param);
    SonhaiModule.api.takeFundReward(param, (res) => {
      this.loadBottom();
      this.loadBeShow();
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
      log.log("任务奖励条=====", res);
    });
  }

  on_click_btn_top_item_get(event) {
    let param: AchieveRewardRequest = {
      /** 基金的ID */
      activityId: sonhai_activityId,
      /** 成就ID */
      achieveId: this._achieveVO.id,
      /** 领取的索引处对应的奖励 从0开始 */
      index: event.target.param.index,
      takeAll: false,
    };
    console.log("山海累计次数领取参数", param);
    SonhaiModule.api.takeFundReward(param, (res) => {
      this.loadBottom();
      this.loadBeShow();
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
      log.log("任务奖励条=====", res);
    });
  }

  on_click_btn_go_task(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    MsgMgr.emit(SonhaiMsgEnum.GUIDE_CLICK_DRAW);
    UIMgr.instance.back();
  }

  on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    Tween.stopAllByTarget(this.node);
  }
  private _tickIndex1: number = 0;
  private _tickIndex2: number = 0;
  public tick(dt: any): void {
    this.topTick();
    this.bottomTick();
  }

  private bottomTick() {
    let pengz = this.getNode("bottom_layer_box");

    let content_list = this.getNode("bottom_content");

    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();

    const index = this._tickIndex2;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();

      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
      }
      this._tickIndex2 = i;
    }

    if (this._tickIndex2 >= content_list.children.length - 1) {
      this._tickIndex2 = 0;
    }
  }

  private topTick() {
    let pengz = this.getNode("top_award_layer_box");

    let content_list = this.getNode("content_top_award_layer");

    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();

    const index = this._tickIndex1;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();

      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
      }
      this._tickIndex1 = i;
    }

    if (this._tickIndex1 >= content_list.children.length - 1) {
      this._tickIndex1 = 0;
    }
  }
}
