import { _decorator, color, Label, tween, v3 } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { ItemCtrl } from "../../../common/ItemCtrl";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { LangMgr } from "../../../mgr/LangMgr";
const { ccclass, property } = _decorator;

export interface IBoxRewardData {
  id: number;
  itemId: number;
  itemNum: number;
  rate: number;
  unlockLevel: number;
}

@ccclass("ItemReward")
export class ItemReward extends BaseCtrl {
  start() {
    super.start();

    tween(this.node)
      .set({ scale: v3(0.5, 0.5, 1) })
      .to(0.3, { scale: v3(1, 1, 1) })
      .start();
  }

  initData(data: IBoxRewardData) {
    const lblDesc = this.getNode("lbl_desc").getComponent(Label);

    this.getNode("Item").getComponent(ItemCtrl).setItemId(data.itemId, data.itemNum);
    if (data.rate || data.unlockLevel < 1) {
      lblDesc.color = color().fromHEX("#00af04");
      lblDesc.string = Formate.formatDecimal(data.rate, 2) + "%";
    } else {
      lblDesc.color = color().fromHEX("#e10000");
      lblDesc.string = LangMgr.txMsgCode(162, [data.unlockLevel], "s%级解锁");
    }
  }
}
