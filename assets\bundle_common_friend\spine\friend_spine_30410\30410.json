{"skeleton": {"hash": "JI5nQnK0zTeGilX93V7jNnQk53g", "spine": "3.8.75", "x": -446.88, "y": -685.56, "width": 981.39, "height": 1332.73, "images": "./images/", "audio": "D:/spine导出/知己"}, "bones": [{"name": "root", "color": "e6ff00ff"}, {"name": "bone", "parent": "root", "length": 166.46, "rotation": -4.09, "x": 50.53, "y": -85.44}, {"name": "bone2", "parent": "bone", "length": 146.73, "rotation": 79.27, "x": -32.59, "y": 24.11}, {"name": "bone3", "parent": "bone2", "length": 79.8, "rotation": 14.32, "x": 146.73}, {"name": "bone4", "parent": "bone3", "length": 42.99, "rotation": 2.31, "x": 79.8}, {"name": "bone5", "parent": "bone3", "x": 24.1, "y": -113.6}, {"name": "bone6", "parent": "bone3", "x": 38.22, "y": 84.36}, {"name": "bone7", "parent": "bone5", "length": 132.31, "rotation": -165.64, "x": -14.79, "y": -6.88}, {"name": "bone8", "parent": "bone7", "length": 139.8, "rotation": 25.47, "x": 132.31}, {"name": "bone9", "parent": "bone6", "length": 204.37, "rotation": 143.4, "x": -16.1, "y": 20.13}, {"name": "bone10", "parent": "bone9", "length": 212.31, "rotation": -129.76, "x": 204.37}, {"name": "bone11", "parent": "bone10", "length": 67.73, "rotation": 3.92, "x": 212.31}, {"name": "bone12", "parent": "bone10", "length": 74.42, "rotation": 40.86, "x": 12.05, "y": 31.6}, {"name": "bone13", "parent": "bone12", "length": 57.04, "rotation": -8.2, "x": 74.67, "y": 1.59}, {"name": "bone14", "parent": "bone10", "x": 134.54, "y": 66.57}, {"name": "bone15", "parent": "bone4", "x": 59.17, "y": 32.02, "color": "abe323ff"}, {"name": "bone16", "parent": "bone4", "x": 113.07, "y": 33.1}, {"name": "bone17", "parent": "bone4", "x": 88.69, "y": 28.87}, {"name": "bone18", "parent": "bone2", "x": 74.07, "y": 36.88, "color": "abe323ff"}, {"name": "bone19", "parent": "bone2", "x": 145.2, "y": 32.52}, {"name": "bone20", "parent": "bone", "length": 152.62, "rotation": 166.32, "x": 42.25, "y": -91.96, "color": "0040ffff"}, {"name": "bone21", "parent": "bone", "length": 239.21, "rotation": 165.89, "x": -34.36, "y": -191.62}, {"name": "bone22", "parent": "bone21", "length": 514.62, "rotation": 103.81, "x": 238.7, "y": 1.53}, {"name": "bone23", "parent": "bone", "length": 265.49, "rotation": -128.09, "x": -250.9, "y": -248.14}, {"name": "bone24", "parent": "bone4", "length": 36.07, "rotation": 177.6, "x": 72.94, "y": -71.16}, {"name": "bone25", "parent": "bone4", "x": 226.4, "y": 14.76}, {"name": "bone26", "parent": "bone25", "length": 32.04, "rotation": -158.04, "x": -25.77, "y": 2.81}, {"name": "bone27", "parent": "bone26", "length": 39.96, "rotation": -18.07, "x": 32.04}, {"name": "bone28", "parent": "bone25", "length": 30.66, "rotation": 161.22, "x": -34.39, "y": 28.94}, {"name": "bone29", "parent": "bone28", "length": 37.29, "rotation": 15.44, "x": 30.66}, {"name": "bone30", "parent": "bone25", "length": 28.92, "rotation": 149.41, "x": -43.28, "y": 62.04}, {"name": "bone31", "parent": "bone30", "length": 37.39, "rotation": 33.34, "x": 28.92}, {"name": "bone32", "parent": "bone25", "length": 61.6, "rotation": -153.39, "x": -23.1, "y": -38.55}, {"name": "bone33", "parent": "bone32", "length": 78.53, "rotation": -28.06, "x": 61.6}, {"name": "bone34", "parent": "bone33", "length": 58.68, "rotation": 26.85, "x": 78.53}, {"name": "bone35", "parent": "bone34", "length": 56.29, "rotation": 48.99, "x": 58.24, "y": 0.23}, {"name": "bone36", "parent": "bone25", "length": 72.38, "rotation": -124.87, "x": -24.53, "y": -86.36}, {"name": "bone37", "parent": "bone36", "length": 61.2, "rotation": 40.6, "x": 72.38}, {"name": "bone38", "parent": "bone25", "length": 86, "rotation": -146.51, "x": -64.08, "y": -88.03}, {"name": "bone39", "parent": "bone38", "length": 84.34, "rotation": 27.9, "x": 86}, {"name": "bone40", "parent": "bone39", "length": 67.73, "rotation": 40.52, "x": 84.34}, {"name": "bone41", "parent": "bone25", "length": 81.04, "rotation": 19.89, "x": -87.28, "y": -289.15}, {"name": "bone42", "parent": "bone41", "length": 64.22, "rotation": -44.48, "x": 81.04}, {"name": "bone43", "parent": "bone25", "length": 40.74, "rotation": 97.48, "x": 81.26, "y": -209.66}, {"name": "bone44", "parent": "bone43", "length": 49.71, "rotation": 18.78, "x": 40.74}, {"name": "bone45", "parent": "bone25", "length": 77.47, "rotation": -120, "x": -249.58, "y": -169.56}, {"name": "bone46", "parent": "bone45", "length": 57.49, "rotation": -33.08, "x": 77.47}, {"name": "bone47", "parent": "bone25", "length": 89.95, "rotation": 94.89, "x": -85.82, "y": 102.18}, {"name": "bone48", "parent": "bone47", "length": 120.38, "rotation": -31.48, "x": 89.95}, {"name": "bone49", "parent": "bone48", "length": 99.45, "rotation": -29.69, "x": 120.38}, {"name": "bone50", "parent": "bone49", "length": 101.16, "rotation": -6.31, "x": 102.87, "y": -2.44}, {"name": "bone51", "parent": "bone25", "length": 61.11, "rotation": 113.44, "x": -138.1, "y": 106.43}, {"name": "bone52", "parent": "bone51", "length": 62.98, "rotation": -37.79, "x": 61.11}, {"name": "bone53", "parent": "bone52", "length": 51.93, "rotation": -13.73, "x": 62.98}, {"name": "bone54", "parent": "bone25", "length": 51.08, "rotation": 109.63, "x": -80.26, "y": 317.17}, {"name": "bone55", "parent": "bone54", "length": 62.91, "rotation": 47.18, "x": 51.08}, {"name": "bone56", "parent": "bone25", "length": 42.23, "rotation": 170.16, "x": -160.42, "y": 80.63}, {"name": "bone57", "parent": "bone56", "length": 43.78, "rotation": -34.26, "x": 42.23}, {"name": "bone58", "parent": "bone57", "length": 64.92, "rotation": -51.37, "x": 43.78}, {"name": "bone59", "parent": "bone4", "x": 152.96, "y": -66.04}, {"name": "bone60", "parent": "bone59", "length": 27.97, "rotation": -156.03, "x": -6.69, "y": 21.16}, {"name": "bone61", "parent": "bone60", "length": 30.41, "rotation": 12.79, "x": 27.97}, {"name": "bone62", "parent": "bone61", "length": 22.62, "rotation": 4.9, "x": 30.19, "y": 0.18}, {"name": "bone72", "parent": "root", "length": 139.11, "rotation": 6.46, "x": -456.65, "y": 495.71}, {"name": "bone64", "parent": "bone72", "rotation": -6.46, "x": 163.38, "y": -1.57, "color": "ea0000ff"}, {"name": "bone65", "parent": "root", "x": -257.35, "y": 575.15, "color": "ea0000ff"}, {"name": "bone74", "parent": "root", "rotation": -174.99, "x": 171.14, "y": -717}, {"name": "bone73", "parent": "bone74", "length": 298.65, "rotation": -107.65, "x": -29.55, "y": -63.49}, {"name": "bone66", "parent": "bone73", "rotation": 107.65, "x": 333.67, "y": 3.02, "color": "ea0000ff"}, {"name": "bone67", "parent": "root", "x": -313.01, "y": 309.25, "color": "ea0000ff"}, {"name": "bone68", "parent": "root", "x": -216.1, "y": 279.93, "color": "ea0000ff"}, {"name": "bone69", "parent": "root", "x": -67.01, "y": -75.98, "color": "0600ffff"}, {"name": "bone70", "parent": "bone", "length": 152.62, "rotation": 166.32, "x": 42.25, "y": -91.96, "color": "0040ffff"}, {"name": "bone71", "parent": "bone", "length": 152.62, "rotation": 166.32, "x": 42.25, "y": -91.96, "color": "0040ffff"}, {"name": "bone75", "parent": "root", "x": -142.88, "y": -65.51, "color": "0600ffff"}, {"name": "bone76", "parent": "root", "x": -30.99, "y": -63.27, "color": "0600ffff"}, {"name": "bone63", "parent": "root", "length": 285.4, "rotation": -0.77, "x": -24.61, "y": -682.49, "color": "e6ff00ff"}, {"name": "bone77", "parent": "bone63", "x": -246.62, "y": 155.18, "color": "e6ff00ff"}, {"name": "bone78", "parent": "bone63", "x": -110.09, "y": 140.45, "color": "e6ff00ff"}, {"name": "bone79", "parent": "bone63", "x": 14.77, "y": 140.84, "color": "e6ff00ff"}, {"name": "bone80", "parent": "bone63", "x": 163.78, "y": 145.39, "color": "e6ff00ff"}, {"name": "bone81", "parent": "bone63", "x": 251.64, "y": 149.11, "color": "e6ff00ff"}, {"name": "bone82", "parent": "bone63", "x": 251.64, "y": 275.45, "color": "e6ff00ff"}, {"name": "bone83", "parent": "bone63", "x": -110.09, "y": 266.78, "color": "e6ff00ff"}, {"name": "bone84", "parent": "bone63", "x": -246.62, "y": 281.52, "color": "e6ff00ff"}, {"name": "bone85", "parent": "bone63", "x": 14.77, "y": 267.18, "color": "e6ff00ff"}, {"name": "bone86", "parent": "bone63", "x": 163.78, "y": 271.72, "color": "e6ff00ff"}, {"name": "bone87", "parent": "bone63", "x": 251.64, "y": 411.72, "color": "e6ff00ff"}, {"name": "bone88", "parent": "bone63", "x": -110.09, "y": 403.05, "color": "e6ff00ff"}, {"name": "bone89", "parent": "bone63", "x": -246.62, "y": 417.79, "color": "e6ff00ff"}, {"name": "bone90", "parent": "bone63", "x": 14.77, "y": 403.45, "color": "e6ff00ff"}, {"name": "bone91", "parent": "bone63", "x": 163.78, "y": 408, "color": "e6ff00ff"}, {"name": "bone92", "parent": "bone63", "x": 251.64, "y": 550.93, "color": "e6ff00ff"}, {"name": "bone93", "parent": "bone63", "x": -110.09, "y": 542.27, "color": "e6ff00ff"}, {"name": "bone94", "parent": "bone63", "x": -246.62, "y": 557, "color": "e6ff00ff"}, {"name": "bone95", "parent": "bone63", "x": 14.77, "y": 542.66, "color": "e6ff00ff"}, {"name": "bone96", "parent": "bone63", "x": 163.78, "y": 547.21, "color": "e6ff00ff"}, {"name": "bone97", "parent": "bone63", "x": 251.64, "y": 703.94, "color": "e6ff00ff"}, {"name": "bone98", "parent": "bone63", "x": -110.09, "y": 695.27, "color": "e6ff00ff"}, {"name": "bone99", "parent": "bone63", "x": -246.62, "y": 710, "color": "e6ff00ff"}, {"name": "bone100", "parent": "bone63", "x": 14.77, "y": 695.67, "color": "e6ff00ff"}, {"name": "bone101", "parent": "bone63", "x": 163.78, "y": 700.21, "color": "e6ff00ff"}, {"name": "bone102", "parent": "bone63", "x": 251.64, "y": 831.46, "color": "e6ff00ff"}, {"name": "bone103", "parent": "bone63", "x": -110.09, "y": 822.79, "color": "e6ff00ff"}, {"name": "bone104", "parent": "bone63", "x": -246.62, "y": 837.53, "color": "e6ff00ff"}, {"name": "bone105", "parent": "bone63", "x": 14.77, "y": 823.19, "color": "e6ff00ff"}, {"name": "bone106", "parent": "bone63", "x": 163.78, "y": 827.74, "color": "e6ff00ff"}, {"name": "bone107", "parent": "bone63", "x": 251.64, "y": 981.69, "color": "e6ff00ff"}, {"name": "bone108", "parent": "bone63", "x": -110.09, "y": 973.03, "color": "e6ff00ff"}, {"name": "bone109", "parent": "bone63", "x": -246.62, "y": 987.76, "color": "e6ff00ff"}, {"name": "bone110", "parent": "bone63", "x": 14.77, "y": 973.43, "color": "e6ff00ff"}, {"name": "bone111", "parent": "bone63", "x": 163.78, "y": 977.97, "color": "e6ff00ff"}, {"name": "bone112", "parent": "bone63", "x": 251.64, "y": 1130.86, "color": "e6ff00ff"}, {"name": "bone113", "parent": "bone63", "x": -110.09, "y": 1122.19, "color": "e6ff00ff"}, {"name": "bone114", "parent": "bone63", "x": -246.62, "y": 1136.93, "color": "e6ff00ff"}, {"name": "bone115", "parent": "bone63", "x": 14.77, "y": 1122.59, "color": "e6ff00ff"}, {"name": "bone116", "parent": "bone63", "x": 163.78, "y": 1127.13, "color": "e6ff00ff"}, {"name": "bone117", "parent": "bone63", "x": 251.64, "y": 1237.88, "color": "e6ff00ff"}, {"name": "bone118", "parent": "bone63", "x": -110.09, "y": 1229.21, "color": "e6ff00ff"}, {"name": "bone119", "parent": "bone63", "x": -246.62, "y": 1243.95, "color": "e6ff00ff"}, {"name": "bone120", "parent": "bone63", "x": 14.77, "y": 1229.61, "color": "e6ff00ff"}, {"name": "bone121", "parent": "bone63", "x": 163.78, "y": 1234.16, "color": "e6ff00ff"}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "toufa_hou_you", "bone": "root", "attachment": "toufa_hou_you"}, {"name": "toufa_hou_zuo", "bone": "root", "attachment": "toufa_hou_zuo"}, {"name": "tui_zuo", "bone": "root", "attachment": "tui_zuo"}, {"name": "shui", "bone": "bone20", "attachment": "shui"}, {"name": "shui2", "bone": "bone70", "attachment": "shui"}, {"name": "shui3", "bone": "bone71", "attachment": "shui"}, {"name": "gebo_zuo", "bone": "root", "attachment": "gebo_zuo"}, {"name": "shou_zuo", "bone": "root", "attachment": "shou_zuo"}, {"name": "shenti", "bone": "root", "attachment": "shenti"}, {"name": "gebo_you", "bone": "root", "attachment": "gebo_you"}, {"name": "yanjing", "bone": "bone17", "attachment": "yanjing"}, {"name": "lian", "bone": "root", "attachment": "lian"}, {"name": "bu_gebo_you", "bone": "root", "attachment": "bu_gebo_you"}, {"name": "bu_gebo_zuo", "bone": "root", "attachment": "bu_gebo_zuo"}, {"name": "toufa_qian", "bone": "root", "attachment": "toufa_qian"}, {"name": "ererhuan", "bone": "bone24", "attachment": "ererhuan"}, {"name": "fashi", "bone": "root", "attachment": "fashi"}, {"name": "yu4_zuoshang", "bone": "bone68", "attachment": "yu4_zuoshang"}, {"name": "yu3_1_2", "bone": "bone67", "attachment": "yu3_1_2"}, {"name": "yu3_1", "bone": "bone66", "attachment": "yu3_1"}, {"name": "yu1_zuoshang", "bone": "bone64", "attachment": "yu1_zuoshang"}, {"name": "hua_zu<PERSON>ang", "bone": "bone65", "attachment": "hua_zu<PERSON>ang"}, {"name": "paopao1", "bone": "bone69", "attachment": "paopao1"}, {"name": "paopao2", "bone": "bone75", "attachment": "paopao1"}, {"name": "paopao3", "bone": "bone76", "attachment": "paopao1"}, {"name": "datui_you1", "bone": "root", "attachment": "datui_you1"}, {"name": "shou_zuo2", "bone": "bone8", "attachment": "shou_zuo"}], "transform": [{"name": "bd", "order": 2, "bones": ["bone19"], "target": "bone18", "x": 71.13, "y": -4.36, "rotateMix": -0.906, "translateMix": -0.906, "scaleMix": -0.906, "shearMix": -0.906}, {"name": "eye", "order": 1, "bones": ["bone17"], "target": "bone15", "x": 29.53, "y": -3.15, "rotateMix": 0.38, "translateMix": 0.38, "scaleMix": 0.38, "shearMix": 0.38}, {"name": "face", "bones": ["bone16"], "target": "bone15", "x": 53.91, "y": 1.08, "rotateMix": -1.009, "translateMix": -1.009, "scaleMix": -1.009, "shearMix": -1.009}, {"name": "s1", "order": 3, "bones": ["bone6"], "target": "bone18", "rotation": 14.32, "x": 88.82, "y": 54.31, "rotateMix": -0.4, "translateMix": -0.4, "scaleMix": -0.4, "shearMix": -0.4}, {"name": "s2", "order": 4, "bones": ["bone5"], "target": "bone18", "rotation": 14.32, "x": 124.12, "y": -140.99, "rotateMix": -0.5, "translateMix": -0.5, "scaleMix": -0.5, "shearMix": -0.5}], "skins": [{"name": "default", "attachments": {"gebo_zuo": {"gebo_zuo": {"type": "mesh", "uvs": [0.71388, 0.26037, 0.75688, 0.19171, 0.78739, 0.11583, 0.82762, 0.03995, 0.87894, 0, 0.94136, 0.0002, 0.96356, 0.05259, 0.95939, 0.13209, 0.92888, 0.21159, 0.88865, 0.28024, 0.84427, 0.33625, 0.7971, 0.37961, 0.82901, 0.49705, 0.83456, 0.58377, 0.83456, 0.65965, 0.81791, 0.72289, 0.78046, 0.7518, 0.73869, 0.83382, 0.69561, 0.9102, 0.64056, 0.96632, 0.57833, 1, 0.49456, 1, 0.38835, 1, 0.3048, 0.95503, 0.20991, 0.89416, 0.1077, 0.86804, 0.05428, 0.76967, 0.01376, 0.62332, 0, 0.46978, 0.06165, 0.35942, 0.13164, 0.20587, 0.20348, 0.09071, 0.25689, 0.02114, 0.36188, 0.01394, 0.43925, 0.06672, 0.4945, 0.14589, 0.53871, 0.21787, 0.57186, 0.29944, 0.57923, 0.34262, 0.63081, 0.30664, 0.6787, 0.26105, 0.92538, 0.073, 0.89286, 0.13477, 0.83731, 0.21595, 0.7926, 0.28653, 0.75331, 0.33242, 0.72351, 0.4983, 0.63409, 0.60418, 0.5108, 0.63594, 0.32925, 0.57242, 0.22493, 0.44712, 0.15583, 0.34653, 0.35228, 0.20006, 0.36583, 0.313, 0.4417, 0.42947, 0.5108, 0.45594, 0.09622, 0.57947, 0.17209, 0.68712, 0.27235, 0.77006, 0.4268, 0.80359, 0.57718, 0.78418, 0.69099, 0.71712, 0.77093, 0.63594], "triangles": [52, 51, 30, 29, 30, 51, 50, 51, 53, 56, 51, 50, 58, 57, 49, 58, 49, 59, 58, 24, 57, 23, 58, 59, 24, 58, 23, 22, 23, 59, 22, 59, 21, 56, 29, 51, 28, 29, 56, 27, 28, 56, 57, 56, 50, 57, 50, 49, 26, 27, 56, 26, 56, 57, 25, 26, 57, 24, 25, 57, 52, 32, 33, 52, 33, 34, 52, 34, 35, 31, 32, 52, 53, 52, 35, 53, 35, 36, 52, 30, 31, 51, 52, 53, 54, 53, 36, 54, 36, 37, 54, 37, 38, 55, 54, 38, 49, 50, 53, 49, 53, 54, 48, 54, 55, 49, 54, 48, 59, 49, 48, 41, 4, 5, 41, 5, 6, 7, 41, 6, 41, 3, 4, 42, 41, 7, 42, 3, 41, 2, 3, 42, 8, 42, 7, 43, 2, 42, 43, 42, 8, 1, 2, 43, 9, 43, 8, 44, 1, 43, 10, 44, 43, 0, 1, 44, 45, 0, 44, 9, 10, 43, 11, 44, 10, 45, 44, 11, 45, 40, 0, 46, 45, 11, 46, 11, 12, 39, 46, 38, 47, 38, 46, 55, 38, 47, 45, 39, 40, 45, 46, 39, 13, 62, 46, 13, 46, 12, 47, 46, 62, 48, 55, 47, 62, 13, 14, 61, 47, 62, 15, 62, 14, 16, 61, 62, 15, 16, 62, 60, 48, 47, 60, 47, 61, 59, 48, 60, 17, 61, 16, 18, 61, 17, 60, 61, 18, 19, 60, 18, 21, 59, 60, 20, 21, 60, 19, 20, 60], "vertices": [2, 9, 53.24, -28.01, 0.98119, 10, 118.19, -98.26, 0.01881, 2, 9, 32.17, -27.23, 0.99981, 10, 131.07, -114.95, 0.00019, 1, 9, 12.05, -30.58, 1, 1, 9, -9.87, -31.52, 1, 1, 9, -27.05, -24.56, 1, 1, 9, -67.34, 12.6, 1, 1, 9, -58.74, 37.43, 1, 1, 9, -36.49, 39.37, 1, 1, 9, -4.38, 45.15, 1, 1, 9, 24.55, 46.8, 1, 1, 9, 54.32, 37.26, 1, 1, 9, 71.16, 27.07, 1, 1, 9, 76.65, 34.43, 1, 1, 9, 92.07, 48.25, 1, 1, 9, 106.48, 59.15, 1, 1, 9, 121.6, 64.11, 1, 1, 9, 134.09, 59, 1, 1, 9, 157.47, 60.44, 1, 1, 9, 180.02, 60.75, 1, 1, 9, 200.97, 55.2, 1, 3, 9, 219, 44.64, 0.96778, 10, -43.67, -17.31, 0.01812, 12, -74.14, -0.54, 0.0141, 3, 9, 234.66, 23.93, 0.65703, 10, -37.77, 7.98, 0.14767, 12, -53.13, 14.72, 0.1953, 4, 9, 254.52, -2.34, 0.14548, 10, -30.28, 40.04, 0.1223, 13, -104.75, 17.73, 0.00177, 12, -26.49, 34.07, 0.73045, 4, 9, 261.6, -29.45, 0.01161, 10, -13.97, 62.83, 0.0087, 13, -78.72, 28.11, 0.05073, 12, 0.76, 40.64, 0.92895, 2, 13, -47.53, 38.23, 0.38016, 12, 33.07, 46.2, 0.61984, 2, 13, -20.48, 55.86, 0.84351, 12, 62.36, 59.8, 0.15649, 2, 13, 7.71, 50.62, 0.99019, 12, 89.52, 50.59, 0.00981, 1, 13, 41, 34.4, 1, 1, 13, 69.53, 11.17, 1, 2, 13, 74.14, -20.98, 0.5389, 14, 6.68, 37.42, 0.4611, 3, 10, 171.87, 74.56, 0.15779, 13, 84.05, -62.31, 0.01, 14, 37.33, 7.98, 0.83221, 2, 10, 193.49, 46.64, 0.59668, 14, 58.96, -19.94, 0.40332, 2, 10, 205.85, 26.75, 0.83801, 14, 71.32, -39.83, 0.16199, 1, 10, 200.12, -5.34, 1, 2, 9, 67.82, -123.71, 0.00043, 10, 182.43, -25.83, 0.99957, 2, 9, 72.52, -98.69, 0.01942, 10, 160.19, -38.23, 0.98058, 2, 9, 77.92, -77.42, 0.08673, 10, 140.39, -47.68, 0.91327, 2, 9, 87.21, -57.52, 0.2618, 10, 119.15, -53.27, 0.7382, 2, 9, 94.03, -49.5, 0.48528, 10, 108.62, -53.16, 0.51472, 2, 9, 77.55, -41.91, 0.8334, 10, 113.33, -70.68, 0.1666, 2, 9, 59.95, -36.61, 0.94985, 10, 120.52, -87.6, 0.05015, 1, 9, -21.87, -2.6, 1, 1, 9, -4.07, -1.78, 1, 1, 9, 21.73, -3.86, 1, 2, 9, 43.49, -4.79, 0.99984, 10, 106.58, -120.61, 0.00016, 2, 9, 59.54, -7.91, 0.99646, 10, 98.72, -106.26, 0.00354, 1, 9, 96.61, 8.53, 1, 1, 9, 133.43, 1.61, 1, 2, 9, 162.51, -24.31, 0.36162, 10, 45.46, -16.63, 0.63838, 3, 10, 72.99, 34.74, 0.47078, 12, 48.14, -37.49, 0.47442, 14, -61.55, -31.83, 0.0548, 4, 10, 109.38, 59.45, 0.14914, 13, 23.29, -41.3, 0.10658, 12, 91.84, -42.61, 0.25879, 14, -25.16, -7.12, 0.48549, 3, 13, 55.34, -43.53, 0.09346, 12, 123.24, -49.39, 0.00238, 14, 3.03, 8.29, 0.90416, 2, 10, 157.66, 7.64, 0.94949, 14, 23.12, -58.94, 0.05051, 3, 10, 130.53, 9.66, 0.92186, 12, 75.26, -94.11, 0.00786, 14, -4.01, -56.92, 0.07028, 2, 9, 136.23, -71.04, 0.02573, 10, 98.19, -6.94, 0.97427, 2, 9, 128.34, -50.15, 0.25723, 10, 87.18, -26.37, 0.74277, 1, 13, 29.94, 9.1, 1, 1, 13, -4.78, 11.07, 1, 2, 13, -40.82, 3.56, 0.08299, 12, 34.77, 10.93, 0.91701, 2, 10, 12.53, 17.81, 0.53491, 12, -8.66, -10.75, 0.46509, 1, 9, 178.24, 13.38, 1, 1, 9, 144.23, 31.9, 1, 1, 9, 113.87, 40.01, 1], "hull": 41, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 0, 80, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 104, 106, 106, 108, 108, 110, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124], "width": 310, "height": 238}}, "toufa_hou_you": {"toufa_hou_you": {"type": "mesh", "uvs": [0.02268, 0.20524, 0.07931, 0.25982, 0.09218, 0.33504, 0.13337, 0.41616, 0.08961, 0.50023, 0.00466, 0.57693, 0, 0.6492, 0, 0.75392, 0.1205, 0.79227, 0.29039, 0.8262, 0.40108, 0.89552, 0.49632, 0.97664, 0.66621, 1, 0.94937, 0.99876, 1, 0.94862, 1, 0.81735, 1, 0.69345, 1, 0.44124, 1, 0.33209, 1, 0.20229, 1, 0.10642, 1, 0, 0.67651, 0, 0.4783, 0, 0.28524, 0, 0.12822, 0.07545, 0.12307, 0.1492, 0.7177, 0.18312, 0.72542, 0.39994, 0.70225, 0.6138, 0.55038, 0.71558, 0.35217, 0.69788, 0.14366, 0.66248, 0.2492, 0.51793, 0.41652, 0.53711, 0.55553, 0.43239, 0.57354, 0.30259, 0.42682, 0.20082, 0.35474, 0.36601, 0.87987, 0.73917, 0.79749, 0.8439, 0.54008, 0.86749], "triangles": [1, 0, 26, 1, 26, 37, 10, 9, 41, 8, 7, 32, 9, 8, 31, 7, 6, 32, 6, 5, 32, 33, 5, 4, 4, 3, 33, 33, 3, 38, 3, 2, 38, 38, 2, 1, 40, 30, 39, 40, 39, 15, 40, 15, 14, 13, 40, 14, 12, 41, 40, 12, 40, 13, 41, 30, 40, 41, 9, 30, 11, 10, 41, 11, 41, 12, 30, 9, 31, 32, 5, 33, 32, 33, 31, 8, 32, 31, 34, 33, 38, 35, 34, 38, 34, 35, 29, 31, 33, 34, 30, 31, 34, 29, 30, 34, 29, 28, 17, 29, 17, 16, 39, 29, 16, 30, 29, 39, 39, 16, 15, 18, 28, 27, 28, 18, 17, 22, 21, 20, 27, 22, 20, 27, 20, 19, 27, 19, 18, 23, 22, 27, 37, 23, 27, 37, 24, 23, 25, 24, 37, 26, 25, 37, 36, 37, 27, 28, 36, 27, 35, 38, 36, 35, 36, 28, 29, 35, 28, 37, 38, 1, 38, 37, 36], "vertices": [1, 4, 237.29, -38.78, 1, 1, 4, 211.78, -52.87, 1, 1, 4, 179.1, -43.15, 1, 1, 4, 148.8, -44.86, 1, 1, 4, 115.27, -7.37, 1, 1, 4, 69.47, -9.64, 1, 1, 4, 33.8, -26.35, 1, 1, 4, -14.42, 6.32, 1, 1, 4, -35.86, -28.6, 1, 1, 4, -59.25, -78.4, 1, 1, 4, -82.55, -128.19, 1, 3, 35, 111.69, -111.84, 0.00166, 45, 46.29, -85.72, 0.71192, 46, 20.66, -88.84, 0.28642, 2, 45, 90.74, -74.07, 0.33856, 46, 51.55, -54.82, 0.66144, 1, 46, 119.79, 40.21, 1, 3, 40, 99.72, -192.46, 0.00483, 45, 200.69, 6.95, 0, 46, 99.45, 73.08, 0.99517, 3, 40, 112.44, -133.54, 0.18496, 45, 170.79, 59.29, 0.0786, 46, 45.83, 100.62, 0.73644, 3, 40, 126.52, -75.92, 0.63973, 45, 142.78, 111.58, 0.15899, 46, -6.18, 129.14, 0.20128, 2, 41, -40.27, -82.07, 0.81757, 40, 183.11, 42.04, 0.18243, 2, 42, -0.86, -91.84, 0.02594, 41, 16.08, -64.92, 0.97406, 2, 42, 68.75, -84.77, 0.76645, 41, 70.71, -108.65, 0.23355, 2, 42, 107.12, -62.46, 0.99895, 41, 113.71, -119.61, 0.00105, 2, 43, -125.6, -1.92, 0.0586, 42, 130.66, -19.79, 0.9414, 2, 43, -20.96, -15.73, 0.84979, 42, 86.82, 76.22, 0.15021, 2, 44, -17.48, -19.56, 0.14922, 43, 30.49, -24.15, 0.85078, 1, 44, 27.32, -43.46, 1, 2, 36, -56.4, 75.36, 0.06815, 44, 80.05, -32.33, 0.93185, 2, 36, -39.07, 46.25, 0.43351, 44, 97.18, -3.1, 0.56649, 6, 36, 100.5, 118.49, 0.01094, 37, 98.46, 71.67, 0.09308, 44, -33.48, 84.23, 0.02185, 43, -18.08, 68.97, 0.1785, 42, 13.51, 33.7, 0.66882, 41, 114.29, 14.59, 0.02681, 4, 37, 87.39, -27.26, 0.48324, 41, 21.08, 49.5, 0.36262, 40, 44.31, 84.55, 0.09592, 39, 63.09, 93.06, 0.05822, 4, 40, 15.08, -9.36, 0.96034, 35, 124.59, 62.81, 0.00051, 45, 15.39, 86.66, 0.03777, 46, -99.32, 38.73, 0.00137, 4, 40, -34.81, -45.26, 0.08263, 39, 87.29, -57.02, 0.17297, 35, 96.93, 7.92, 0.14754, 45, 2.24, 26.62, 0.59686, 4, 39, 37.09, -73.26, 0.02187, 38, 153.06, -47.39, 3e-05, 34, 84.8, 35.93, 0.0016, 35, 44.37, 3.39, 0.9765, 1, 34, 45.27, -5.4, 1, 4, 39, -24.31, -11.74, 0.03444, 38, 70.01, -21.75, 0.74492, 34, -1.04, 49.62, 0.20277, 35, -1.62, 77.14, 0.01787, 4, 37, -1.41, -79, 0.00036, 40, -49.56, 42.68, 1e-05, 39, 18.94, 0.24, 0.9995, 38, 102.63, 9.08, 0.00013, 5, 37, 41.14, -36.15, 0.65445, 41, 23.76, 96.53, 0.01795, 40, -2.63, 80.69, 0.05255, 39, 29.92, 59.62, 0.25405, 38, 84.53, 66.69, 0.02102, 6, 36, 98.63, 51.85, 0.04959, 37, 53.67, 22.29, 0.78869, 44, 25.78, 114.77, 0.03077, 43, 28.19, 116.96, 0.02958, 42, -51.73, 47.44, 0.05499, 41, 77.36, 70.09, 0.04639, 6, 36, 40.81, 69.96, 0.27806, 37, 21.55, 73.66, 0.18609, 44, 37.84, 55.4, 0.36912, 43, 58.73, 64.63, 0.10231, 42, -23.59, 101.1, 0.06346, 41, 135.04, 88.67, 0.00095, 3, 36, 66.27, -3.94, 0.95375, 39, -30.95, 63.02, 0.00127, 38, 29.15, 41.21, 0.04498, 3, 40, 46.79, -76.36, 0.43349, 45, 83.74, 58, 0.29226, 46, -26.4, 52.02, 0.27425, 3, 40, 14.34, -117.9, 0.02716, 45, 87.34, 5.4, 0.08134, 46, 5.32, 9.91, 0.8915, 3, 35, 110.92, -60.44, 0.01034, 45, 32.78, -36.12, 0.89638, 46, -17.73, -54.66, 0.09328], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 44, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 70, 72, 74, 76, 78, 80, 80, 82], "width": 263, "height": 459}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0.83333, 1, 0.66667, 1, 0.5, 1, 0.33333, 1, 0.16667, 1, 0, 1, 0, 0.91667, 0, 0.83333, 0, 0.75, 0, 0.66667, 0, 0.58333, 0, 0.5, 0, 0.41667, 0, 0.33333, 0, 0.25, 0, 0.16667, 0, 0.08333, 0, 0, 0.16667, 0, 0.33333, 0, 0.5, 0, 0.66667, 0, 0.83333, 0, 1, 0, 1, 0.08333, 1, 0.16667, 1, 0.25, 1, 0.33333, 1, 0.41667, 1, 0.5, 1, 0.58333, 1, 0.66667, 1, 0.75, 1, 0.83333, 1, 0.91667, 0.83333, 0.91667, 0.66667, 0.91667, 0.5, 0.91667, 0.33333, 0.91667, 0.16667, 0.91667, 0.83333, 0.83333, 0.66667, 0.83333, 0.5, 0.83333, 0.33333, 0.83333, 0.16667, 0.83333, 0.83333, 0.75, 0.66667, 0.75, 0.5, 0.75, 0.33333, 0.75, 0.16667, 0.75, 0.83333, 0.66667, 0.66667, 0.66667, 0.5, 0.66667, 0.33333, 0.66667, 0.16667, 0.66667, 0.83333, 0.58333, 0.66667, 0.58333, 0.5, 0.58333, 0.33333, 0.58333, 0.16667, 0.58333, 0.83333, 0.5, 0.66667, 0.5, 0.5, 0.5, 0.33333, 0.5, 0.16667, 0.5, 0.83333, 0.41667, 0.66667, 0.41667, 0.5, 0.41667, 0.33333, 0.41667, 0.16667, 0.41667, 0.83333, 0.33333, 0.66667, 0.33333, 0.5, 0.33333, 0.33333, 0.33333, 0.16667, 0.33333, 0.83333, 0.25, 0.66667, 0.25, 0.5, 0.25, 0.33333, 0.25, 0.16667, 0.25, 0.83333, 0.16667, 0.66667, 0.16667, 0.5, 0.16667, 0.33333, 0.16667, 0.16667, 0.16667, 0.83333, 0.08333, 0.66667, 0.08333, 0.5, 0.08333, 0.33333, 0.08333, 0.16667, 0.08333], "triangles": [16, 17, 90, 15, 16, 85, 14, 15, 80, 13, 14, 75, 12, 13, 70, 11, 12, 65, 10, 11, 60, 9, 10, 55, 8, 9, 50, 7, 8, 45, 6, 7, 40, 88, 89, 21, 86, 87, 23, 86, 23, 24, 25, 86, 24, 81, 86, 25, 76, 81, 26, 56, 61, 30, 35, 36, 34, 2, 37, 36, 5, 6, 40, 4, 5, 39, 3, 4, 38, 2, 3, 37, 1, 2, 36, 0, 1, 35, 1, 36, 35, 34, 41, 33, 33, 46, 32, 31, 56, 30, 32, 51, 31, 30, 61, 29, 29, 66, 28, 27, 76, 26, 28, 71, 27, 26, 81, 25, 89, 20, 21, 17, 18, 19, 90, 19, 20, 88, 21, 22, 87, 22, 23, 90, 17, 19, 40, 7, 45, 45, 8, 50, 50, 9, 55, 60, 11, 65, 55, 10, 60, 65, 12, 70, 70, 13, 75, 80, 15, 85, 75, 14, 80, 85, 16, 90, 39, 40, 44, 5, 40, 39, 4, 39, 38, 38, 39, 43, 37, 38, 42, 3, 38, 37, 37, 42, 41, 36, 37, 41, 36, 41, 34, 41, 42, 46, 41, 46, 33, 44, 45, 49, 40, 45, 44, 44, 49, 48, 45, 50, 49, 43, 44, 48, 39, 44, 43, 43, 48, 47, 42, 43, 47, 38, 43, 42, 42, 47, 46, 46, 47, 51, 46, 51, 32, 49, 50, 54, 49, 54, 53, 50, 55, 54, 48, 49, 53, 48, 53, 52, 47, 48, 52, 47, 52, 51, 51, 52, 56, 51, 56, 31, 55, 60, 59, 54, 55, 59, 54, 59, 58, 53, 54, 58, 53, 58, 57, 52, 53, 57, 52, 57, 56, 61, 62, 66, 56, 57, 61, 61, 66, 29, 64, 65, 69, 60, 65, 64, 59, 60, 64, 64, 69, 68, 59, 64, 63, 65, 70, 69, 63, 64, 68, 58, 59, 63, 63, 68, 67, 62, 63, 67, 58, 63, 62, 57, 58, 62, 62, 67, 66, 57, 62, 61, 66, 67, 71, 66, 71, 28, 69, 70, 74, 69, 74, 73, 70, 75, 74, 68, 69, 73, 68, 73, 72, 67, 68, 72, 67, 72, 71, 71, 72, 76, 71, 76, 27, 74, 75, 79, 74, 79, 78, 75, 80, 79, 73, 74, 78, 77, 78, 82, 73, 78, 77, 72, 73, 77, 72, 77, 76, 81, 82, 86, 76, 77, 81, 84, 85, 89, 80, 85, 84, 79, 80, 84, 79, 84, 83, 83, 84, 88, 78, 79, 83, 82, 83, 87, 78, 83, 82, 82, 87, 86, 77, 82, 81, 89, 90, 20, 85, 90, 89, 84, 89, 88, 87, 88, 22, 83, 88, 87], "vertices": [1, 76, 370.25, 44.62, 1, 1, 76, 254.76, 43.08, 1, 1, 76, 139.27, 41.53, 1, 1, 76, 23.78, 39.98, 1, 1, 76, -91.71, 38.44, 1, 1, 76, -207.2, 36.89, 1, 1, 76, -322.69, 35.34, 1, 1, 76, -324.13, 142.83, 1, 1, 76, -325.57, 250.32, 1, 1, 76, -327.01, 357.81, 1, 1, 76, -328.45, 465.3, 1, 1, 76, -329.89, 572.79, 1, 1, 76, -331.33, 680.28, 1, 1, 76, -332.77, 787.77, 1, 1, 76, -334.21, 895.27, 1, 1, 76, -335.65, 1002.76, 1, 1, 76, -337.09, 1110.25, 1, 1, 76, -338.53, 1217.74, 1, 1, 76, -339.97, 1325.23, 1, 1, 76, -224.48, 1326.77, 1, 1, 76, -108.99, 1328.32, 1, 1, 76, 6.5, 1329.87, 1, 1, 76, 121.99, 1331.41, 1, 1, 76, 237.48, 1332.96, 1, 1, 76, 352.97, 1334.51, 1, 1, 76, 354.41, 1227.02, 1, 1, 76, 355.85, 1119.53, 1, 1, 76, 357.29, 1012.04, 1, 1, 76, 358.73, 904.55, 1, 1, 76, 360.17, 797.06, 1, 1, 76, 361.61, 689.56, 1, 1, 76, 363.05, 582.07, 1, 1, 76, 364.49, 474.58, 1, 1, 76, 365.93, 367.09, 1, 1, 76, 367.37, 259.6, 1, 1, 76, 368.81, 152.11, 1, 3, 86, 89.54, -121.16, 0.00078, 82, 1.67, -124.88, 0.01157, 81, 1.67, 1.45, 0.98765, 4, 86, -25.95, -122.7, 0.05182, 85, 123.06, -118.16, 0.02688, 80, -25.95, 3.63, 0.80032, 79, 123.06, 8.18, 0.12098, 5, 86, -141.44, -124.25, 0.00841, 85, 7.57, -119.71, 0.03131, 83, 132.43, -119.31, 0.00034, 80, -141.44, 2.08, 0.02683, 79, 7.57, 6.63, 0.93311, 4, 85, -107.92, -121.25, 0.01416, 83, 16.94, -120.86, 0.02611, 79, -107.92, 5.08, 0.08319, 78, 16.94, 5.48, 0.87654, 4, 84, 37.97, -137.14, 0.01257, 83, -98.55, -122.4, 0.03455, 78, -98.55, 3.93, 0.28295, 77, 37.97, -10.8, 0.66992, 4, 86, 88.1, -13.67, 0.02937, 82, 0.23, -17.39, 0.88225, 81, 0.23, 108.94, 0.07465, 80, 88.1, 112.67, 0.01372, 7, 91, -27.39, -151.49, 0.01604, 90, 121.62, -146.94, 0.01474, 86, -27.39, -15.21, 0.71331, 85, 121.62, -10.67, 0.13175, 81, -115.26, 107.4, 0.00085, 80, -27.39, 111.12, 0.09562, 79, 121.62, 115.67, 0.02768, 5, 86, -142.88, -16.76, 0.02386, 85, 6.13, -12.22, 0.91409, 80, -142.88, 109.58, 0.01182, 79, 6.13, 114.12, 0.04793, 78, 130.99, 114.52, 0.00231, 5, 90, -109.36, -150.04, 0.00114, 85, -109.36, -13.76, 0.07986, 83, 15.5, -13.37, 0.8422, 79, -109.36, 112.57, 0.01683, 78, 15.5, 112.97, 0.05997, 5, 88, -99.99, -151.18, 0.01292, 84, 36.54, -29.65, 0.57528, 83, -99.99, -14.91, 0.22516, 78, -99.99, 111.42, 0.04905, 77, 36.54, 96.69, 0.1376, 4, 91, 86.66, -42.45, 0.08364, 87, -1.21, -46.17, 0.59286, 86, 86.66, 93.82, 0.04795, 82, -1.21, 90.1, 0.27556, 7, 95, 120.18, -178.66, 0.00187, 91, -28.83, -44, 0.53985, 90, 120.18, -39.45, 0.12781, 87, -116.7, -47.72, 0.0068, 86, -28.83, 92.28, 0.23967, 85, 120.18, 96.82, 0.0736, 82, -116.7, 88.55, 0.0104, 7, 96, -144.32, -184.75, 0.0001, 91, -144.32, -45.54, 0.04653, 90, 4.69, -41, 0.61561, 88, 129.55, -40.6, 0.03611, 86, -144.32, 90.73, 0.0409, 85, 4.69, 95.27, 0.23342, 83, 129.55, 95.67, 0.02733, 6, 90, -110.8, -42.55, 0.08873, 89, 150.59, -56.88, 0.00618, 88, 14.06, -42.15, 0.60012, 85, -110.8, 93.73, 0.06183, 84, 150.59, 79.39, 0.01601, 83, 14.06, 94.12, 0.22714, 5, 93, -101.43, -182.91, 0.00025, 89, 35.1, -58.43, 0.39626, 88, -101.43, -43.69, 0.19942, 84, 35.1, 77.84, 0.3068, 83, -101.43, 92.58, 0.09726, 4, 96, 85.22, -74.17, 0.07653, 92, -2.65, -77.9, 0.36136, 91, 85.22, 65.04, 0.07592, 87, -2.65, 61.32, 0.48619, 6, 96, -30.27, -75.72, 0.34536, 95, 118.74, -71.17, 0.10936, 92, -118.13, -79.44, 0.00856, 91, -30.27, 63.49, 0.40611, 90, 118.74, 68.04, 0.11338, 87, -118.13, 59.77, 0.01722, 6, 96, -145.76, -77.26, 0.04459, 95, 3.25, -72.72, 0.38396, 93, 128.11, -72.32, 0.03736, 91, -145.76, 61.95, 0.05802, 90, 3.25, 66.49, 0.43115, 88, 128.11, 66.89, 0.04492, 6, 95, -112.24, -74.27, 0.07501, 94, 149.15, -88.6, 0.00947, 93, 12.62, -73.87, 0.37074, 90, -112.24, 64.94, 0.07942, 89, 149.15, 50.61, 0.03009, 88, 12.62, 65.34, 0.43528, 4, 94, 33.66, -90.15, 0.23095, 93, -102.87, -75.42, 0.14081, 89, 33.66, 49.06, 0.48921, 88, -102.87, 63.8, 0.13903, 4, 101, 83.78, -119.68, 0.04217, 97, -4.08, -123.41, 0.13147, 96, 83.78, 33.32, 0.09355, 92, -4.08, 29.59, 0.73281, 8, 101, -31.71, -121.23, 0.14478, 100, 117.3, -116.69, 0.06364, 97, -119.57, -124.95, 0.00309, 96, -31.71, 31.77, 0.63493, 95, 117.3, 36.32, 0.14276, 92, -119.57, 28.05, 0.00231, 91, -31.71, 170.98, 0.00388, 90, 117.3, 175.53, 0.00462, 6, 101, -147.2, -122.78, 0.02415, 100, 1.81, -118.23, 0.15628, 98, 126.67, -117.83, 0.02671, 96, -147.2, 30.23, 0.04917, 95, 1.81, 34.77, 0.69986, 93, 126.67, 35.17, 0.04383, 7, 100, -113.68, -119.78, 0.03907, 99, 147.71, -134.12, 0.00758, 98, 11.18, -119.38, 0.15335, 95, -113.68, 33.22, 0.09059, 94, 147.71, 18.89, 0.02604, 93, 11.18, 33.62, 0.68265, 90, -113.68, 172.44, 0.00073, 6, 99, 32.22, -135.66, 0.08183, 98, -104.31, -120.93, 0.07144, 94, 32.22, 17.34, 0.65035, 93, -104.31, 32.07, 0.1873, 89, 32.22, 156.55, 0.00406, 88, -104.31, 171.29, 0.00502, 4, 101, 82.34, -12.19, 0.06788, 97, -5.52, -15.92, 0.86006, 96, 82.34, 140.81, 0.01585, 92, -5.52, 137.08, 0.05621, 7, 106, -33.15, -141.26, 0.01293, 105, 115.86, -136.72, 0.01611, 101, -33.15, -13.74, 0.72027, 100, 115.86, -9.2, 0.15363, 96, -33.15, 139.26, 0.06585, 95, 115.86, 143.81, 0.02988, 92, -121.01, 135.54, 0.00132, 6, 101, -148.64, -15.29, 0.00965, 100, 0.37, -10.74, 0.92586, 98, 125.23, -10.34, 0.01076, 96, -148.64, 137.72, 0.00814, 95, 0.37, 142.26, 0.03835, 93, 125.23, 142.66, 0.00724, 6, 105, -115.12, -139.81, 0.00127, 100, -115.12, -12.29, 0.05848, 98, 9.74, -11.89, 0.87806, 95, -115.12, 140.71, 0.01468, 94, 146.27, 126.38, 0.00427, 93, 9.74, 141.11, 0.04322, 5, 103, -105.75, -140.96, 0.01055, 99, 30.78, -28.17, 0.62945, 98, -105.75, -13.44, 0.19893, 94, 30.78, 124.83, 0.12088, 93, -105.75, 139.56, 0.0402, 4, 106, 80.9, -32.23, 0.1016, 102, -6.96, -35.95, 0.64328, 101, 80.9, 95.3, 0.04855, 97, -6.96, 91.57, 0.20657, 7, 111, -34.59, -184.01, 0.00387, 110, 114.42, -179.46, 0.0044, 106, -34.59, -33.77, 0.57963, 105, 114.42, -29.23, 0.14942, 101, -34.59, 93.75, 0.19327, 100, 114.42, 98.29, 0.06699, 97, -122.45, 90.03, 0.00242, 6, 106, -150.08, -35.32, 0.02562, 105, -1.07, -30.78, 0.70083, 103, 123.79, -30.38, 0.04114, 101, -150.08, 92.2, 0.02262, 100, -1.07, 96.75, 0.18545, 98, 123.79, 97.15, 0.02433, 6, 105, -116.56, -32.32, 0.0594, 104, 144.83, -46.66, 0.00604, 103, 8.3, -31.93, 0.71026, 100, -116.56, 95.2, 0.03559, 99, 144.83, 80.86, 0.01292, 98, 8.3, 95.6, 0.17578, 5, 108, -107.19, -183.71, 0.00175, 104, 29.34, -48.21, 0.49842, 103, -107.19, -33.47, 0.16325, 99, 29.34, 79.32, 0.27135, 98, -107.19, 94.05, 0.06522, 4, 111, 79.46, -74.97, 0.10994, 107, -8.4, -78.7, 0.3584, 106, 79.46, 75.26, 0.10088, 102, -8.4, 71.54, 0.43077, 6, 111, -36.03, -76.52, 0.35686, 110, 112.98, -71.97, 0.12889, 107, -123.89, -80.24, 0.00735, 106, -36.03, 73.72, 0.37029, 105, 112.98, 78.26, 0.12344, 102, -123.89, 69.99, 0.01317, 6, 111, -151.52, -78.06, 0.04281, 110, -2.51, -73.52, 0.39927, 108, 122.35, -73.12, 0.05824, 106, -151.52, 72.17, 0.05126, 105, -2.51, 76.71, 0.38209, 103, 122.35, 77.11, 0.06634, 6, 110, -118, -75.07, 0.06375, 109, 143.39, -89.4, 0.01697, 108, 6.86, -74.67, 0.40054, 105, -118, 75.17, 0.06992, 104, 143.39, 60.83, 0.0383, 103, 6.86, 75.56, 0.41052, 4, 109, 27.9, -90.95, 0.27442, 108, -108.63, -76.22, 0.13121, 104, 27.9, 59.28, 0.47053, 103, -108.63, 74.02, 0.12383, 4, 116, 78.02, -116.64, 0.05277, 112, -9.84, -120.37, 0.13109, 111, 78.02, 32.52, 0.12499, 107, -9.84, 28.79, 0.69115, 7, 116, -37.47, -118.19, 0.15109, 115, 111.54, -113.65, 0.07355, 112, -125.33, -121.92, 0.00284, 111, -37.47, 30.97, 0.59197, 110, 111.54, 35.52, 0.17032, 106, -37.47, 181.21, 0.00588, 105, 111.54, 185.75, 0.00436, 6, 116, -152.96, -119.74, 0.02189, 115, -3.95, -115.19, 0.16117, 113, 120.91, -114.8, 0.03476, 111, -152.96, 29.43, 0.03607, 110, -3.95, 33.97, 0.68463, 108, 120.91, 34.37, 0.06148, 6, 115, -119.44, -116.74, 0.03386, 114, 141.95, -131.08, 0.0095, 113, 5.42, -116.34, 0.16214, 110, -119.44, 32.42, 0.05917, 109, 141.95, 18.09, 0.03279, 108, 5.42, 32.82, 0.70255, 6, 114, 26.46, -132.62, 0.07761, 113, -110.07, -117.89, 0.06066, 109, 26.46, 16.54, 0.71537, 108, -110.07, 31.27, 0.13796, 104, 26.46, 166.77, 0.00459, 103, -110.07, 181.51, 0.00382, 4, 116, 76.58, -9.15, 0.11866, 112, -11.28, -12.88, 0.82444, 111, 76.58, 140.01, 0.01531, 107, -11.28, 136.29, 0.04158, 7, 121, -38.91, -117.72, 0.02759, 120, 110.1, -113.18, 0.02182, 116, -38.91, -10.7, 0.67352, 115, 110.1, -6.16, 0.17655, 111, -38.91, 138.46, 0.06647, 110, 110.1, 143.01, 0.03337, 107, -126.77, 134.74, 0.00067, 7, 118, 119.47, -114.33, 0.00126, 116, -154.4, -12.25, 0.00234, 115, -5.39, -7.7, 0.91966, 113, 119.47, -7.31, 0.03483, 111, -154.4, 136.92, 0.00384, 110, -5.39, 141.46, 0.0298, 108, 119.47, 141.86, 0.00827, 6, 120, -120.88, -116.27, 2e-05, 115, -120.88, -9.25, 0.02718, 113, 3.98, -8.85, 0.93454, 110, -120.88, 139.91, 0.00733, 109, 140.51, 125.58, 0.00361, 108, 3.98, 140.31, 0.02731, 5, 118, -111.51, -117.42, 0.00929, 114, 25.02, -25.13, 0.6892, 113, -111.51, -10.4, 0.15852, 109, 25.02, 124.03, 0.11338, 108, -111.51, 138.77, 0.02961, 4, 121, 75.14, -8.69, 0.11, 117, -12.72, -12.41, 0.80176, 116, 75.14, 98.34, 0.02333, 112, -12.72, 94.61, 0.06491, 4, 121, -40.35, -10.23, 0.62744, 120, 108.66, -5.69, 0.20262, 116, -40.35, 96.79, 0.12519, 115, 108.66, 101.33, 0.04475, 5, 120, -6.83, -7.24, 0.92231, 118, 118.03, -6.84, 0.02959, 116, -155.84, 95.24, 0.00034, 115, -6.83, 99.79, 0.0385, 113, 118.03, 100.18, 0.00927, 4, 118, 2.54, -8.39, 0.95807, 115, -122.32, 98.24, 0.00407, 114, 139.07, 83.9, 0.00168, 113, 2.54, 98.64, 0.03618, 4, 119, 23.58, -24.67, 0.65124, 118, -112.95, -9.93, 0.13614, 114, 23.58, 82.36, 0.17972, 113, -112.95, 97.09, 0.0329], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 0], "width": 485, "height": 902}}, "datui_you1": {"datui_you1": {"type": "mesh", "uvs": [0.31622, 0.24327, 0.31457, 0.2587, 0.31892, 0.2948, 0.32976, 0.32946, 0.33147, 0.37927, 0.32336, 0.45509, 0.30084, 0.53989, 0.26425, 0.63824, 0.21979, 0.76164, 0.19756, 0.83652, 0.21701, 0.89874, 0.16839, 1, 0.08086, 1, 0, 1, 0.00583, 0.92406, 0.01139, 0.82492, 0.02529, 0.72156, 0.02806, 0.61082, 0.0239, 0.50325, 0.02667, 0.39673, 0.04057, 0.3113, 0.0239, 0.21111, 0.04335, 0.12041, 0.09614, 0.01178, 0.14212, 0, 0.1988, 0, 0.25984, 0, 0.36158, 0.03367, 0.4139, 0.03257, 0.54817, 0.06385, 0.69405, 0.08283, 0.86355, 0.08705, 0.99137, 0.13135, 1, 0.23892, 0.99832, 0.33279, 0.93302, 0.38552, 0.8316, 0.43931, 0.72879, 0.44774, 0.66071, 0.40661, 0.57596, 0.37708, 0.44397, 0.32646, 0.3767, 0.28377, 0.33446, 0.2528, 0.19249, 0.10905, 0.22844, 0.10496, 0.16104, 0.14862, 0.32613, 0.12885, 0.47197, 0.18045, 0.60225, 0.22667, 0.79341, 0.26107, 0.9081, 0.22882, 0.15479, 0.23737, 0.17178, 0.30294, 0.16329, 0.42225, 0.14488, 0.52544, 0.13355, 0.63616, 0.11939, 0.75225, 0.09673, 0.83824, 0.08116, 0.92745], "triangles": [44, 25, 26, 44, 26, 27, 24, 25, 44, 43, 24, 44, 23, 24, 43, 46, 44, 27, 43, 22, 23, 47, 28, 29, 46, 27, 28, 47, 46, 28, 48, 29, 30, 47, 29, 48, 50, 31, 32, 50, 32, 33, 0, 44, 46, 0, 46, 47, 43, 44, 0, 42, 0, 47, 49, 30, 31, 49, 31, 50, 48, 30, 49, 41, 42, 47, 40, 41, 47, 40, 47, 48, 34, 50, 33, 39, 40, 48, 38, 39, 48, 35, 50, 34, 49, 50, 35, 49, 38, 48, 36, 38, 49, 35, 36, 49, 37, 38, 36, 0, 45, 43, 45, 22, 43, 21, 22, 45, 51, 21, 45, 0, 51, 45, 1, 51, 0, 52, 51, 1, 52, 1, 2, 20, 21, 51, 20, 51, 52, 53, 20, 52, 19, 20, 53, 52, 3, 4, 3, 52, 2, 4, 53, 52, 5, 53, 4, 18, 19, 53, 54, 18, 53, 54, 53, 5, 6, 54, 5, 17, 18, 54, 55, 17, 54, 7, 55, 54, 6, 7, 54, 16, 17, 55, 56, 16, 55, 8, 56, 55, 7, 8, 55, 15, 16, 56, 9, 56, 8, 57, 15, 56, 9, 57, 56, 14, 15, 57, 58, 14, 57, 12, 13, 14, 58, 12, 14, 57, 9, 10, 58, 57, 10, 11, 58, 10, 12, 58, 11], "vertices": [2, 22, 60.82, 64.66, 0.72625, 21, 161.4, 45.16, 0.27375, 1, 22, 68.47, 64.62, 1, 1, 22, 86.12, 67.61, 1, 1, 22, 102.88, 72.97, 1, 2, 22, 127.37, 75.49, 0.96653, 21, 134.99, 107.2, 0.03347, 2, 22, 164.95, 75.33, 0.99792, 21, 126.19, 143.73, 0.00208, 1, 22, 207.36, 70.11, 1, 1, 22, 256.85, 60.14, 1, 1, 22, 318.91, 48.18, 1, 1, 22, 356.43, 42.7, 1, 1, 22, 386.52, 52.32, 1, 1, 22, 470.82, 29.7, 1, 1, 22, 486.44, -6.75, 1, 1, 22, 478.99, -32.81, 1, 1, 22, 405.04, -25.68, 1, 1, 22, 356.05, -27.35, 1, 1, 22, 304.74, -26.06, 1, 1, 22, 250.12, -29.2, 1, 1, 22, 197.25, -34.83, 1, 1, 22, 144.71, -37.81, 1, 1, 22, 102.23, -35.84, 1, 1, 22, 53.36, -45.86, 1, 2, 22, 8.13, -42.01, 0.80786, 21, 277.56, 19.44, 0.19214, 1, 21, 275.51, -37.72, 1, 1, 21, 260.94, -48.63, 1, 1, 21, 240.75, -55.26, 1, 1, 21, 219.01, -62.41, 1, 1, 21, 177.57, -58.52, 1, 1, 21, 159.1, -65.16, 1, 1, 21, 106.44, -66.2, 1, 1, 21, 51.54, -74.37, 1, 1, 21, -9.5, -92.24, 1, 1, 21, -61.86, -86.41, 1, 1, 21, -81.53, -36.94, 1, 1, 21, -95.41, 7.31, 1, 1, 21, -80.27, 39.71, 1, 1, 21, -52.44, 76.82, 1, 1, 21, -17.11, 92.82, 1, 1, 21, 13.48, 81.48, 1, 1, 21, 48.23, 77.55, 1, 2, 22, 98.13, 115.56, 0.00169, 21, 103.06, 69.24, 0.99831, 2, 22, 79.03, 88.8, 0.0398, 21, 133.61, 57.08, 0.9602, 2, 22, 64.99, 71.84, 0.18117, 21, 153.43, 47.49, 0.81883, 1, 21, 226.18, -3.35, 1, 1, 21, 214.01, -9.48, 1, 2, 22, 18.65, 3.06, 0.93527, 21, 231.28, 18.9, 0.06473, 1, 21, 175.52, -9.7, 1, 1, 21, 115.6, -2.56, 1, 1, 21, 62.06, 3.88, 1, 1, 21, -11.34, -2.36, 1, 1, 21, -47.23, -30.92, 1, 2, 22, 62.54, 4.07, 0.98481, 21, 219.82, 61.29, 0.01519, 2, 22, 94.35, 12.9, 0.97977, 21, 203.66, 90.07, 0.02023, 2, 22, 153.36, 14.23, 0.99984, 21, 188.28, 147.06, 0.00016, 1, 22, 204.72, 11.25, 1, 1, 22, 259.57, 11.19, 1, 1, 22, 317.16, 10.28, 1, 1, 22, 360.17, 5.06, 1, 1, 22, 404.56, 2.61, 1], "hull": 43, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 0, 84, 0, 86, 48, 88, 44, 90, 88, 92, 92, 94, 94, 96, 96, 98, 98, 100, 90, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116], "width": 375, "height": 494}}, "yu3_1": {"yu3_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46.12, -19.6, -47.88, -19.6, -47.88, 39.4, 46.12, 39.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 94, "height": 59}}, "gebo_you": {"gebo_you": {"type": "mesh", "uvs": [0.07706, 0, 0.18835, 0, 0.29964, 0.03101, 0.36964, 0.11181, 0.40734, 0.20386, 0.42888, 0.29796, 0.49529, 0.38285, 0.58145, 0.45854, 0.70344, 0.51463, 0.82729, 0.55247, 0.94397, 0.57497, 1, 0.5852, 1, 0.66702, 1, 0.78158, 1, 0.87976, 1, 0.97079, 0.89909, 0.97079, 0.79858, 1, 0.63882, 1, 0.45573, 1, 0.30675, 0.98511, 0.25469, 0.92477, 0.25828, 0.85726, 0.18469, 0.81942, 0.07519, 0.77135, 0.06981, 0.69055, 0.10571, 0.62202, 0.12545, 0.56781, 0.11289, 0.48088, 0.10391, 0.40519, 0.07699, 0.33462, 0.05724, 0.25382, 0, 0.19552, 0, 0.11881, 0.01705, 0.03963, 0.11712, 0.06159, 0.17584, 0.1239, 0.21634, 0.22429, 0.23862, 0.33159, 0.25887, 0.44005, 0.32788, 0.53775, 0.41566, 0.65294, 0.53536, 0.77571, 0.48204, 0.72102, 0.70826, 0.81815, 0.8785, 0.83634, 0.4662, 0.51199, 0.6125, 0.61051, 0.80136, 0.68023], "triangles": [46, 6, 7, 40, 39, 46, 27, 28, 40, 8, 46, 7, 47, 46, 8, 41, 40, 46, 41, 46, 47, 10, 11, 12, 9, 47, 8, 48, 9, 10, 48, 10, 12, 48, 47, 9, 43, 41, 47, 41, 24, 25, 26, 27, 40, 48, 42, 43, 48, 43, 47, 48, 12, 13, 44, 42, 48, 45, 44, 48, 40, 41, 26, 23, 41, 43, 22, 23, 43, 26, 41, 25, 23, 24, 41, 13, 45, 48, 42, 22, 43, 45, 13, 14, 16, 45, 14, 16, 14, 15, 22, 19, 21, 19, 20, 21, 22, 42, 19, 18, 19, 42, 44, 18, 42, 17, 44, 45, 17, 45, 16, 18, 44, 17, 35, 0, 1, 34, 0, 35, 33, 34, 35, 2, 36, 35, 2, 35, 1, 36, 2, 3, 33, 35, 36, 32, 33, 36, 37, 36, 3, 37, 3, 4, 32, 36, 37, 31, 32, 37, 37, 4, 5, 38, 37, 5, 31, 37, 38, 30, 31, 38, 29, 30, 38, 6, 39, 38, 6, 38, 5, 29, 38, 39, 28, 29, 39, 46, 39, 6, 28, 39, 40], "vertices": [1, 7, -55.45, -3, 1, 1, 7, -50.11, 18.61, 1, 1, 7, -34.21, 37.61, 1, 1, 7, -3.32, 44.41, 1, 1, 7, 29.86, 43.98, 1, 2, 7, 62.95, 40.25, 0.99358, 8, -45.3, 66.17, 0.00642, 2, 7, 95.07, 46.01, 0.8314, 8, -13.84, 57.55, 0.1686, 2, 7, 124.99, 56.37, 0.34196, 8, 17.63, 54.04, 0.65804, 2, 7, 149.95, 75.34, 0.05174, 8, 48.32, 60.43, 0.94826, 2, 7, 168.78, 96.2, 0.00261, 8, 74.3, 71.17, 0.99739, 1, 8, 95.2, 84.21, 1, 1, 8, 105.08, 90.6, 1, 1, 8, 127.29, 72.4, 1, 1, 8, 158.39, 46.91, 1, 1, 8, 185.04, 25.07, 1, 1, 8, 209.76, 4.81, 1, 1, 8, 196.96, -10.8, 1, 1, 8, 192.15, -32.84, 1, 1, 8, 171.9, -57.56, 1, 1, 8, 148.69, -85.88, 1, 1, 8, 125.76, -105.61, 1, 1, 8, 102.78, -100.24, 1, 1, 8, 84.91, -84.66, 1, 1, 8, 65.3, -87.63, 1, 2, 7, 207.32, -68.24, 0.00166, 8, 38.37, -93.87, 0.99834, 2, 7, 179.53, -62.49, 0.03012, 8, 15.75, -76.72, 0.96988, 2, 7, 157.89, -49.76, 0.15239, 8, 1.7, -55.93, 0.84761, 2, 7, 140.37, -41.36, 0.43853, 8, -10.51, -40.81, 0.56147, 2, 7, 110.14, -36.49, 0.9575, 8, -35.71, -23.41, 0.0425, 1, 7, 83.92, -31.87, 1, 1, 7, 58.58, -31.16, 1, 1, 7, 30.1, -28.2, 1, 1, 7, 7.49, -34.41, 1, 1, 7, -18.65, -27.96, 1, 1, 7, -44.82, -17.99, 1, 1, 7, -32.54, -0.4, 1, 1, 7, -8.49, 5.76, 1, 1, 7, 27.66, 5.18, 1, 1, 7, 65.3, 0.48, 1, 1, 7, 103.23, -4.71, 1, 1, 8, 6.99, -2.81, 1, 1, 8, 49.39, -14.86, 1, 1, 8, 97.89, -23.66, 1, 1, 8, 76.29, -19.74, 1, 1, 8, 131.33, -6.35, 1, 1, 8, 157.85, 15.93, 1, 2, 7, 137.68, 29.5, 0.23319, 8, 17.53, 24.32, 0.76681, 2, 7, 178.26, 49.62, 0.00545, 8, 62.82, 25.03, 0.99455, 1, 8, 105.69, 38.73, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 86, 86, 84, 84, 88, 88, 90, 92, 94, 94, 96, 96, 24], "width": 200, "height": 351}}, "yu3_1_2": {"yu3_1_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [29.71, -17.58, -57.29, -17.58, -57.29, 24.42, 29.71, 24.42], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 42}}, "shou_zuo": {"shou_zuo": {"type": "mesh", "uvs": [0.3567, 0, 0.40714, 0.05263, 0.4891, 0.11637, 0.59314, 0.1814, 0.66249, 0.23603, 0.6751, 0.31667, 0.64988, 0.40252, 0.66249, 0.43894, 0.69402, 0.50267, 0.76022, 0.60413, 0.87687, 0.70948, 0.75707, 0.79533, 0.60575, 0.83175, 0.41344, 0.83695, 0.37246, 0.71729, 0.33778, 0.58592, 0.29365, 0.48446, 0.26843, 0.43504, 0.13602, 0.37911, 0.01622, 0.30627, 0, 0.23993, 0.03198, 0.19311, 0, 0.13718, 0.04459, 0.11246, 0.05405, 0.07995, 0.1171, 0.03442, 0.22429, 0.01231, 0.59115, 0.67482, 0.49458, 0.54959, 0.45319, 0.42151, 0.40145, 0.3034, 0.29108, 0.19382, 0.1807, 0.07428], "triangles": [32, 25, 26, 24, 25, 32, 21, 22, 23, 26, 1, 32, 1, 26, 0, 2, 32, 1, 31, 2, 3, 2, 31, 32, 23, 24, 32, 31, 23, 32, 30, 31, 3, 30, 3, 4, 31, 19, 20, 21, 23, 31, 30, 4, 5, 21, 31, 20, 30, 19, 31, 30, 18, 19, 6, 30, 5, 29, 30, 6, 17, 18, 30, 29, 17, 30, 16, 17, 29, 29, 6, 7, 28, 29, 7, 28, 7, 8, 16, 29, 28, 15, 16, 28, 28, 8, 9, 27, 28, 9, 15, 28, 27, 27, 9, 10, 14, 15, 27, 11, 27, 10, 12, 27, 11, 14, 27, 12, 13, 14, 12], "vertices": [1, 11, 119.86, -24.11, 1, 1, 11, 103.72, -25.39, 1, 1, 11, 83.46, -29.28, 1, 1, 11, 62.07, -35.56, 1, 1, 11, 44.74, -38.8, 1, 2, 10, 236.8, -31.85, 0.03306, 11, 22.25, -33.45, 0.96694, 2, 10, 213.57, -23.37, 0.53875, 11, -0.35, -23.4, 0.46125, 2, 10, 203.09, -22.45, 0.88735, 11, -10.74, -21.76, 0.11265, 1, 10, 184.49, -21.93, 1, 1, 10, 154.46, -22.94, 1, 1, 10, 121.99, -29.49, 1, 1, 10, 101.29, -10.14, 1, 1, 10, 95.21, 9.62, 1, 1, 10, 98.92, 32.05, 1, 1, 10, 133.35, 28.98, 1, 1, 10, 170.87, 24.42, 1, 2, 10, 200.31, 22.9, 0.89936, 11, -10.41, 23.66, 0.10064, 2, 10, 214.75, 22.58, 0.38848, 11, 3.97, 22.36, 0.61152, 2, 10, 233.88, 34.16, 0.0125, 11, 23.85, 32.6, 0.9875, 1, 11, 47.91, 40, 1, 1, 11, 66.61, 36.26, 1, 1, 11, 78.31, 28.72, 1, 1, 11, 94.71, 27.64, 1, 1, 11, 99.92, 20.53, 1, 1, 11, 108.48, 16.74, 1, 1, 11, 118.74, 5.8, 1, 1, 11, 121.08, -8.15, 1, 1, 10, 139.31, 1.09, 1, 1, 10, 176.78, 4.04, 1, 2, 10, 213.56, 0.47, 0.1022, 11, 1.27, 0.38, 0.8978, 1, 11, 35.36, -3.7, 1, 1, 11, 69.14, -0.44, 1, 1, 11, 105.64, 1.97, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 22, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 118, "height": 286}}, "ererhuan": {"ererhuan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [57.38, 18.04, 57.7, -13.96, -8.29, -14.63, -8.62, 17.37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 66}}, "yu4_zuoshang": {"yu4_zuoshang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [26.3, -13.75, -33.7, -13.75, -33.7, 41.25, 26.3, 41.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "height": 55}}, "fashi": {"fashi": {"type": "mesh", "uvs": [0.22229, 0.52832, 0.36368, 0.53509, 0.37705, 0.59875, 0.50889, 0.65157, 0.59679, 0.65293, 0.83562, 0.80462, 1, 0.62313, 0.82798, 0.30213, 0.55857, 0, 0.14968, 0.18836, 0.10382, 0.43757, 0, 0.47414, 0.01211, 0.57031, 0.09809, 0.54864, 0.15923, 0.67866, 0.2433, 0.79514, 0.34839, 0.91162, 0.51845, 0.96851, 0.78595, 1, 0.82034, 0.95903, 0.6713, 0.89943, 0.63919, 0.8208, 0.60825, 0.74503, 0.63007, 0.70009, 0.60908, 0.66358, 0.57408, 0.65991, 0.514, 0.66984, 0.40273, 0.64917, 0.31954, 0.61317, 0.24932, 0.55267, 0.14244, 0.50753, 0.18752, 0.59866, 0.28435, 0.71583, 0.39455, 0.78921, 0.51477, 0.87206, 0.37285, 0.69335, 0.48304, 0.75371], "triangles": [17, 20, 18, 18, 20, 19, 16, 34, 17, 17, 34, 20, 16, 33, 34, 34, 21, 20, 33, 36, 34, 34, 36, 21, 21, 36, 22, 36, 26, 22, 26, 25, 22, 23, 25, 24, 23, 22, 25, 16, 15, 33, 15, 32, 33, 15, 14, 32, 32, 35, 33, 33, 35, 36, 36, 35, 26, 26, 35, 27, 28, 32, 31, 32, 28, 35, 35, 28, 27, 31, 32, 14, 14, 13, 31, 31, 29, 28, 31, 0, 29, 13, 11, 30, 12, 11, 13, 11, 10, 30, 4, 6, 5, 6, 4, 7, 7, 4, 1, 1, 4, 3, 1, 3, 2, 7, 1, 9, 1, 10, 9, 9, 8, 7, 1, 0, 10, 0, 30, 10, 0, 31, 30, 30, 31, 13], "vertices": [2, 59, -12.53, 10.39, 0.992, 60, 9.71, 7.47, 0.008, 1, 59, -14.1, -5.4, 1, 1, 59, -24.2, -6.58, 1, 1, 59, -33.01, -21.07, 1, 1, 59, -33.54, -30.91, 1, 1, 59, -58.34, -56.88, 1, 1, 59, -30.26, -76.19, 1, 1, 59, 21.04, -58.54, 1, 1, 59, 69.71, -29.9, 1, 2, 59, 41.42, 16.82, 0.9785, 60, -42.19, 23.52, 0.0215, 1, 59, 2.22, 23.2, 1, 1, 59, -3.18, 35, 1, 2, 59, -18.41, 34.13, 0.992, 60, 5.44, -16.61, 0.008, 1, 59, -15.29, 24.4, 1, 2, 60, 28.03, -9.22, 0.5353, 61, -1.99, -9, 0.4647, 2, 61, 18.27, -13.12, 0.96269, 62, -13.01, -12.23, 0.03731, 2, 61, 40, -15.39, 0.06568, 62, 8.44, -16.35, 0.93432, 1, 62, 28.07, -8.72, 1, 1, 62, 52.3, 9.6, 1, 1, 62, 50.25, 16.85, 1, 1, 62, 31.93, 11.22, 1, 2, 61, 49.09, 19.02, 0.04114, 62, 20.44, 17.16, 0.95886, 2, 61, 37.57, 23.77, 0.3108, 62, 9.37, 22.88, 0.6892, 2, 61, 33.54, 30.11, 0.4395, 62, 5.9, 29.54, 0.5605, 2, 61, 27.57, 31.87, 0.47817, 62, 0.1, 31.8, 0.52183, 2, 61, 24.67, 29.17, 0.50296, 62, -3.02, 29.36, 0.49704, 2, 61, 21.7, 22.93, 0.62028, 62, -6.51, 23.39, 0.37972, 3, 60, 35.69, 17.36, 0.03082, 61, 11.37, 15.22, 0.91237, 62, -17.46, 16.6, 0.05681, 3, 60, 26.52, 11.45, 0.44615, 61, 1.12, 11.48, 0.55356, 62, -28, 13.75, 0.00029, 3, 59, -16.47, 7.49, 0.04751, 60, 14.49, 8.52, 0.94682, 61, -11.26, 11.3, 0.00567, 2, 59, -8.96, 19.23, 0.79974, 60, 2.86, 0.85, 0.20026, 1, 60, 18.02, -0.87, 1, 1, 61, 11.34, -1.71, 1, 2, 61, 28.1, 0.71, 0.89376, 62, -2.03, 0.71, 0.10624, 2, 61, 46.73, 3.07, 0.0001, 62, 16.73, 1.47, 0.9999, 3, 60, 40.52, 11.31, 0.00594, 61, 14.74, 8.25, 0.96033, 62, -14.7, 9.37, 0.03373, 2, 61, 29.89, 11.95, 0.46843, 62, 0.72, 11.76, 0.53157], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 26, 60, 60, 62, 62, 64, 64, 66, 66, 68, 70, 72, 40, 42, 42, 44, 72, 42], "width": 112, "height": 158}}, "hua_zuoshang": {"hua_zuoshang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43.55, -41.97, -57.45, -41.97, -57.45, 46.03, 43.55, 46.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 101, "height": 88}}, "tui_zuo": {"tui_zuo": {"type": "mesh", "uvs": [0.99944, 0, 1, 0.16673, 0.92295, 0.2842, 0.79351, 0.39853, 0.62877, 0.46744, 0.44638, 0.52696, 0.35813, 0.57238, 0.31988, 0.60213, 0.28458, 0.73369, 0.20221, 0.86682, 0, 1, 0, 0.81827, 0, 0.57551, 0, 0.42202, 0.10513, 0.34214, 0.22869, 0.23094, 0.3846, 0.14011, 0.56405, 0.04457, 0.72879, 0, 0.88471, 0, 0.84352, 0.08685, 0.61995, 0.20589, 0.34636, 0.33588, 0.15514, 0.45021, 0.07277, 0.4972], "triangles": [20, 18, 19, 20, 19, 0, 20, 0, 1, 20, 21, 17, 20, 17, 18, 16, 17, 21, 1, 21, 20, 2, 21, 1, 22, 15, 16, 22, 16, 21, 14, 15, 22, 3, 21, 2, 22, 21, 3, 23, 14, 22, 13, 14, 23, 4, 22, 3, 24, 13, 23, 5, 22, 4, 23, 22, 5, 6, 23, 5, 24, 23, 6, 12, 13, 24, 7, 12, 24, 6, 7, 24, 8, 12, 7, 11, 12, 8, 9, 11, 8, 10, 11, 9], "vertices": [1, 23, -60.13, -2.53, 1, 1, 23, -16.34, 37.3, 1, 1, 23, 24.34, 54.5, 1, 1, 23, 70.84, 63.63, 1, 1, 23, 109.87, 56.98, 1, 1, 23, 148.68, 45.63, 1, 1, 23, 171.83, 44.09, 1, 1, 23, 184.51, 45.83, 1, 1, 23, 223.6, 72.25, 1, 1, 23, 269.07, 92.44, 1, 1, 23, 329.77, 95.87, 1, 1, 23, 281.96, 52.55, 1, 1, 23, 218.1, -5.32, 1, 1, 23, 177.72, -41.91, 1, 1, 23, 143.37, -46.22, 1, 1, 23, 98.43, -55.42, 1, 1, 23, 54.75, -55.24, 1, 1, 23, 6.84, -52.88, 1, 1, 23, -25.79, -40.43, 1, 1, 23, -45.57, -18.6, 1, 1, 23, -17.5, -3.66, 1, 1, 23, 42.19, -6.6, 1, 1, 23, 111.11, -13.93, 1, 1, 23, 165.45, -13.46, 1, 1, 23, 188.26, -13.79, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 24], "width": 226, "height": 426}}, "yanjing": {"yanjing": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-24.53, -75.19, -20.5, 51.74, 38.47, 49.87, 34.44, -77.06], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 127, "height": 59}}, "lian": {"lian": {"type": "mesh", "uvs": [0.10099, 0.59128, 0.13971, 0.62299, 0.15693, 0.64884, 0.17415, 0.6852, 0.20767, 0.74579, 0.25569, 0.82173, 0.32455, 0.88151, 0.41154, 0.93322, 0.4958, 0.97684, 0.53023, 0.98735, 0.56285, 0.97361, 0.66508, 0.91002, 0.74502, 0.83796, 0.80651, 0.76042, 0.84867, 0.67113, 0.92863, 0.664, 1, 0.61856, 1, 0.52989, 0.97752, 0.4804, 0.93643, 0.4649, 0.89061, 0.47617, 0.89219, 0.39587, 0.87323, 0.29302, 0.84795, 0.18735, 0.7721, 0.09437, 0.63305, 0.01688, 0.47346, 0, 0.34231, 0.01124, 0.20168, 0.07323, 0.10529, 0.13241, 0.03893, 0.21412, 0, 0.29302, 0, 0.39587, 0.02787, 0.50576, 0.07053, 0.56634, 0.29491, 0.17186, 0.30281, 0.31979, 0.32335, 0.46208, 0.37865, 0.54239, 0.4071, 0.62269, 0.39829, 0.67212, 0.40081, 0.71255, 0.42033, 0.74568, 0.44175, 0.76365, 0.45749, 0.80016, 0.46946, 0.83385, 0.48421, 0.88667, 0.51355, 0.94741, 0.53234, 0.13342, 0.57071, 0.22566, 0.60408, 0.33277, 0.63245, 0.43245, 0.63652, 0.45614, 0.66671, 0.58907, 0.67859, 0.66215, 0.68216, 0.75853, 0.67147, 0.85597, 0.10309, 0.21218, 0.12209, 0.3361, 0.13872, 0.46955, 0.18505, 0.55004, 0.26582, 0.6729, 0.27651, 0.72479, 0.3074, 0.79364, 0.34897, 0.8593, 0.38817, 0.89743, 0.22755, 0.54414, 0.28704, 0.55131, 0.33956, 0.57902, 0.36474, 0.61343, 0.33795, 0.64783, 0.29776, 0.66264, 0.21951, 0.6679, 0.18789, 0.65452, 0.15574, 0.61868, 0.13806, 0.58715, 0.16431, 0.5623, 0.17182, 0.59909, 0.2238, 0.6096, 0.29079, 0.61582, 0.32991, 0.61534, 0.10912, 0.57329, 0.13752, 0.54988, 0.17416, 0.53112, 0.22326, 0.52455, 0.29079, 0.52407, 0.36582, 0.55943, 0.38779, 0.61868, 0.35831, 0.65978, 0.30794, 0.69036, 0.27084, 0.69728, 0.22326, 0.69418, 0.19218, 0.67841, 0.18361, 0.66503, 0.69145, 0.45319, 0.74502, 0.4617, 0.79435, 0.47305, 0.76645, 0.51227, 0.73905, 0.55405, 0.70899, 0.5706, 0.6038, 0.59267, 0.54546, 0.58321, 0.51363, 0.56193, 0.53485, 0.51148, 0.58258, 0.47365, 0.6975, 0.42873, 0.77352, 0.43661, 0.84159, 0.46813, 0.80535, 0.5241, 0.76468, 0.57139, 0.70988, 0.60685, 0.6722, 0.62279, 0.60468, 0.62104, 0.52866, 0.61158, 0.45175, 0.58242, 0.48093, 0.51464, 0.53131, 0.45789, 0.57816, 0.43976, 0.41976, 0.3886, 0.21408, 0.45011, 0.18649, 0.19291, 0.19276, 0.33046, 0.40722, 0.15266, 0.40596, 0.28014, 0.68814, 0.11352, 0.7358, 0.21528, 0.74834, 0.32263, 0.53561, 0.67617, 0.55484, 0.78674, 0.58175, 0.86731, 0.57695, 0.92902, 0.35007, 0.70874, 0.35584, 0.77217, 0.37795, 0.81674, 0.87244, 0.56179, 0.85965, 0.62168, 0.73169, 0.65306, 0.7946, 0.62929, 0.74662, 0.75194, 0.56202, 0.55069, 0.59851, 0.53832, 0.65862, 0.52687, 0.71874, 0.50213, 0.75573, 0.49022, 0.72378, 0.4191, 0.73127, 0.43223, 0.62914, 0.42083, 0.70641, 0.41538, 0.72483, 0.41183, 0.73876, 0.41563], "triangles": [10, 9, 47, 9, 8, 47, 8, 7, 47, 10, 130, 11, 10, 47, 130, 7, 46, 47, 47, 46, 130, 6, 65, 7, 7, 65, 46, 130, 129, 11, 130, 46, 129, 11, 56, 12, 11, 129, 56, 6, 64, 65, 64, 133, 65, 65, 45, 46, 45, 133, 44, 45, 65, 133, 46, 45, 129, 6, 5, 64, 45, 128, 129, 129, 128, 56, 5, 63, 64, 64, 63, 133, 56, 55, 12, 56, 128, 55, 12, 138, 13, 12, 55, 138, 45, 44, 128, 5, 4, 62, 5, 62, 63, 62, 91, 90, 91, 62, 4, 63, 132, 133, 133, 43, 44, 133, 42, 43, 133, 132, 42, 44, 43, 128, 132, 62, 131, 131, 62, 89, 62, 132, 63, 55, 127, 54, 111, 54, 112, 112, 54, 127, 43, 127, 128, 128, 127, 55, 132, 41, 42, 132, 131, 41, 43, 42, 127, 13, 138, 14, 55, 54, 138, 54, 136, 138, 137, 138, 136, 137, 14, 138, 4, 3, 91, 91, 3, 92, 42, 41, 127, 127, 39, 113, 40, 39, 127, 62, 90, 89, 39, 114, 113, 131, 40, 41, 127, 41, 40, 131, 88, 40, 131, 89, 88, 91, 61, 90, 90, 61, 89, 92, 72, 91, 91, 72, 61, 61, 71, 89, 89, 70, 88, 89, 71, 70, 3, 93, 92, 3, 2, 93, 72, 92, 73, 127, 113, 112, 71, 61, 79, 88, 87, 40, 40, 87, 39, 14, 135, 15, 14, 137, 135, 92, 93, 73, 79, 61, 78, 61, 72, 78, 72, 73, 78, 93, 2, 73, 135, 134, 15, 15, 134, 16, 70, 79, 80, 70, 71, 79, 54, 111, 136, 88, 69, 87, 88, 70, 69, 73, 74, 77, 73, 77, 78, 111, 110, 136, 136, 110, 137, 1, 74, 2, 73, 2, 74, 70, 80, 69, 110, 109, 137, 135, 137, 134, 0, 75, 1, 1, 75, 74, 112, 53, 111, 111, 53, 110, 38, 114, 39, 16, 134, 17, 17, 134, 20, 17, 20, 19, 134, 137, 109, 134, 109, 108, 113, 101, 112, 101, 100, 112, 112, 100, 53, 69, 86, 87, 39, 87, 86, 38, 39, 86, 74, 75, 77, 18, 17, 19, 68, 80, 79, 79, 78, 67, 80, 68, 69, 68, 79, 67, 69, 68, 86, 114, 102, 113, 113, 102, 101, 77, 60, 78, 78, 66, 67, 78, 60, 66, 53, 99, 110, 109, 99, 98, 109, 110, 99, 75, 76, 77, 77, 76, 60, 101, 139, 100, 139, 140, 100, 100, 140, 53, 0, 81, 75, 0, 34, 81, 140, 141, 53, 53, 141, 99, 81, 82, 75, 75, 82, 76, 101, 102, 139, 114, 115, 102, 114, 38, 115, 68, 67, 86, 81, 34, 82, 109, 97, 108, 109, 98, 97, 98, 99, 142, 34, 33, 82, 60, 76, 83, 102, 103, 139, 102, 115, 103, 134, 108, 20, 20, 108, 107, 38, 86, 85, 99, 141, 142, 97, 142, 143, 97, 98, 142, 86, 67, 85, 67, 66, 85, 139, 103, 140, 76, 82, 83, 60, 84, 66, 60, 83, 84, 82, 59, 83, 82, 33, 59, 66, 84, 85, 85, 37, 38, 38, 37, 115, 103, 104, 140, 140, 104, 141, 84, 83, 119, 104, 52, 141, 141, 94, 142, 141, 52, 94, 83, 59, 119, 84, 119, 85, 97, 96, 108, 108, 96, 107, 85, 119, 37, 37, 118, 115, 115, 116, 103, 115, 118, 116, 97, 143, 96, 103, 116, 104, 33, 32, 59, 142, 95, 143, 142, 94, 95, 143, 95, 96, 116, 117, 104, 52, 117, 51, 52, 104, 117, 95, 106, 96, 96, 106, 107, 32, 58, 59, 119, 58, 121, 119, 59, 58, 20, 107, 21, 107, 106, 21, 118, 37, 36, 95, 94, 145, 145, 94, 105, 105, 144, 145, 95, 145, 106, 117, 116, 50, 52, 51, 94, 94, 51, 105, 37, 119, 36, 119, 121, 36, 117, 146, 51, 146, 117, 50, 145, 149, 106, 21, 106, 126, 51, 146, 105, 145, 144, 149, 105, 147, 144, 105, 146, 147, 50, 116, 118, 146, 50, 147, 144, 148, 149, 144, 147, 148, 126, 106, 149, 148, 147, 126, 149, 148, 126, 126, 147, 50, 126, 22, 21, 57, 58, 31, 31, 30, 57, 58, 32, 31, 36, 123, 118, 50, 118, 123, 50, 123, 49, 49, 122, 48, 122, 49, 123, 58, 57, 121, 50, 125, 126, 50, 49, 125, 57, 120, 121, 121, 120, 36, 126, 125, 22, 120, 35, 36, 36, 35, 123, 125, 23, 22, 123, 35, 122, 49, 124, 125, 49, 48, 124, 125, 24, 23, 125, 124, 24, 57, 30, 29, 57, 29, 120, 120, 28, 35, 120, 29, 28, 122, 35, 27, 35, 28, 27, 122, 26, 48, 122, 27, 26, 48, 25, 124, 48, 26, 25, 124, 25, 24], "vertices": [2, 4, 88.37, 86.7, 0.7, 16, -24.7, 53.6, 0.3, 2, 4, 81.71, 79.89, 0.7, 16, -31.36, 46.8, 0.3, 2, 4, 76.37, 76.95, 0.7, 16, -36.71, 43.85, 0.3, 2, 4, 68.89, 74.07, 0.876, 16, -44.18, 40.97, 0.124, 2, 4, 56.41, 68.39, 0.908, 16, -56.67, 35.29, 0.092, 2, 4, 40.72, 60.19, 0.884, 16, -72.35, 27.09, 0.116, 2, 4, 28.2, 48.12, 0.884, 16, -84.88, 15.02, 0.116, 2, 4, 17.21, 32.72, 0.868, 16, -95.87, -0.38, 0.132, 2, 4, 7.87, 17.75, 0.7, 16, -105.2, -15.35, 0.3, 2, 4, 5.54, 11.59, 0.7, 16, -107.53, -21.51, 0.3, 2, 4, 8.14, 5.6, 0.7, 16, -104.93, -27.5, 0.3, 2, 4, 20.46, -13.3, 0.7, 16, -92.62, -46.4, 0.3, 2, 4, 34.62, -28.23, 0.7, 16, -78.46, -61.32, 0.3, 2, 4, 50, -39.85, 0.7, 16, -63.08, -72.95, 0.3, 2, 4, 67.87, -48.05, 0.7, 16, -45.2, -81.15, 0.3, 2, 4, 68.86, -62.56, 0.7, 16, -44.21, -95.66, 0.3, 2, 4, 77.67, -75.77, 0.7, 16, -35.4, -108.87, 0.3, 2, 4, 95.66, -76.34, 0.7, 16, -17.41, -109.44, 0.3, 2, 4, 105.83, -72.59, 0.7, 16, -7.24, -105.69, 0.3, 2, 4, 109.21, -65.26, 0.7, 16, -3.86, -98.36, 0.3, 2, 4, 107.19, -56.89, 0.7, 16, -5.88, -89.99, 0.3, 2, 4, 123.47, -57.7, 0.7, 16, 10.4, -90.8, 0.3, 2, 4, 144.45, -54.93, 0.7, 16, 31.38, -88.03, 0.3, 2, 4, 166.04, -51.04, 0.7, 16, 52.96, -84.14, 0.3, 2, 4, 185.34, -37.91, 0.7, 16, 72.26, -71.01, 0.3, 2, 4, 201.86, -13.26, 0.7, 16, 88.78, -46.36, 0.3, 2, 4, 206.2, 15.5, 0.7, 16, 93.13, -17.6, 0.3, 2, 4, 204.67, 39.3, 0.7, 16, 91.6, 6.2, 0.3, 2, 4, 192.9, 65.14, 0.7, 16, 79.83, 32.04, 0.3, 2, 4, 181.45, 82.96, 0.7, 16, 68.37, 49.86, 0.3, 2, 4, 165.25, 95.49, 0.7, 16, 52.18, 62.39, 0.3, 2, 4, 149.47, 103.04, 0.7, 16, 36.39, 69.95, 0.3, 2, 4, 128.6, 103.71, 0.7, 16, 15.52, 70.61, 0.3, 2, 4, 106.14, 99.37, 0.7, 16, -6.93, 66.27, 0.3, 2, 4, 93.6, 92.05, 0.7, 16, -19.47, 58.95, 0.3, 2, 4, 172.36, 48.91, 0.7, 15, 113.19, 16.89, 0.3, 2, 4, 142.3, 48.44, 0.7, 15, 83.13, 16.42, 0.3, 2, 4, 113.31, 45.64, 0.7, 15, 54.14, 13.62, 0.3, 2, 4, 96.69, 36.15, 0.7, 15, 37.53, 4.13, 0.3, 2, 4, 80.24, 31.52, 0.7, 15, 21.07, -0.5, 0.3, 2, 4, 70.26, 33.43, 0.7, 15, 11.09, 1.41, 0.3, 2, 4, 62.04, 33.24, 0.7, 15, 2.88, 1.22, 0.3, 2, 4, 55.21, 29.92, 0.7, 15, -3.96, -2.1, 0.3, 2, 4, 51.44, 26.16, 0.7, 15, -7.73, -5.86, 0.3, 2, 4, 43.94, 23.55, 0.7, 15, -15.22, -8.47, 0.3, 2, 4, 37.04, 21.6, 0.7, 15, -22.13, -10.42, 0.3, 2, 4, 26.23, 19.27, 0.7, 15, -32.93, -12.75, 0.3, 2, 4, 13.74, 14.35, 0.7, 15, -45.42, -17.67, 0.3, 2, 4, 178.79, 5.71, 0.8, 15, 119.62, -26.31, 0.2, 2, 4, 159.86, -0.64, 0.8, 15, 100.69, -32.66, 0.2, 2, 4, 137.93, -5.98, 0.642, 15, 78.76, -38, 0.358, 2, 4, 117.54, -10.47, 0.8, 15, 58.38, -42.49, 0.2, 2, 4, 112.71, -11.06, 0.8, 15, 53.55, -43.08, 0.2, 2, 4, 85.57, -15.66, 0.8, 15, 26.4, -47.68, 0.2, 2, 4, 70.67, -17.34, 0.8, 15, 11.51, -49.36, 0.2, 2, 4, 51.1, -17.37, 0.8, 15, -8.07, -49.38, 0.2, 2, 4, 31.39, -14.8, 0.8, 15, -27.78, -46.82, 0.2, 2, 4, 165.28, 83.87, 0.8, 15, 106.11, 51.86, 0.2, 2, 4, 140.02, 81.23, 0.8, 15, 80.86, 49.22, 0.2, 2, 4, 112.85, 79.09, 0.8, 15, 53.69, 47.07, 0.2, 2, 4, 96.25, 71.22, 0.85, 15, 37.09, 39.2, 0.15, 2, 4, 70.86, 57.4, 0.8, 15, 11.7, 25.38, 0.2, 2, 4, 60.27, 55.8, 0.896, 15, 1.11, 23.78, 0.104, 2, 4, 46.13, 50.66, 0.952, 15, -13.04, 18.64, 0.048, 2, 4, 32.56, 43.56, 0.92, 15, -26.6, 11.54, 0.08, 3, 4, 24.6, 36.71, 0.552, 15, -34.56, 4.7, 0.184, 16, -88.47, 3.62, 0.264, 2, 4, 97.21, 63.5, 0.8, 15, 38.04, 31.48, 0.2, 2, 4, 95.41, 52.78, 0.8, 15, 36.24, 20.76, 0.2, 2, 4, 89.49, 43.46, 0.75, 15, 30.32, 11.44, 0.25, 2, 4, 82.36, 39.12, 0.75, 15, 23.2, 7.1, 0.25, 2, 4, 75.53, 44.19, 0.75, 15, 16.37, 12.17, 0.25, 2, 4, 72.76, 51.56, 0.8, 15, 13.59, 19.54, 0.2, 2, 4, 72.14, 65.75, 0.938, 15, 12.98, 33.73, 0.062, 2, 4, 75.04, 71.38, 0.93, 15, 15.87, 39.36, 0.07, 2, 4, 82.49, 76.97, 0.96, 15, 23.33, 44.95, 0.04, 2, 4, 89, 79.96, 0.912, 15, 29.83, 47.94, 0.088, 2, 4, 93.89, 75.05, 0.85, 15, 34.72, 43.03, 0.15, 2, 4, 86.38, 73.93, 0.71201, 15, 27.21, 41.91, 0.28799, 2, 4, 83.95, 64.6, 0.85, 15, 24.78, 32.58, 0.15, 2, 4, 82.3, 52.52, 0.8, 15, 23.14, 20.5, 0.2, 2, 4, 82.17, 45.44, 0.8, 15, 23.01, 13.42, 0.2, 2, 4, 91.97, 85.11, 0.96, 15, 32.81, 53.09, 0.04, 2, 4, 96.56, 79.82, 0.85, 15, 37.39, 47.8, 0.15, 2, 4, 100.15, 73.07, 0.85, 15, 40.99, 41.05, 0.15, 2, 4, 101.21, 64.15, 0.8, 15, 42.04, 32.13, 0.2, 2, 4, 100.92, 51.93, 0.75, 15, 41.75, 19.91, 0.25, 2, 4, 93.31, 38.58, 0.75, 15, 34.14, 6.56, 0.25, 2, 4, 81.16, 34.99, 0.75, 15, 22, 2.97, 0.25, 2, 4, 72.99, 40.58, 0.75, 15, 13.83, 8.57, 0.25, 2, 4, 67.08, 49.9, 0.8, 15, 7.91, 17.88, 0.2, 2, 4, 65.89, 56.65, 0.8, 15, 6.72, 24.63, 0.2, 2, 4, 66.79, 65.24, 0.938, 15, 7.62, 33.22, 0.062, 1, 4, 70.17, 70.76, 1, 2, 4, 72.93, 72.22, 0.984, 15, 13.76, 40.21, 0.016, 2, 4, 113, -21.01, 0.85, 15, 53.83, -53.03, 0.15, 2, 4, 110.96, -30.65, 0.85, 15, 51.8, -62.67, 0.15, 2, 4, 108.38, -39.5, 0.9, 15, 49.21, -71.52, 0.1, 2, 4, 100.58, -34.2, 0.9, 15, 41.41, -66.22, 0.1, 2, 4, 92.26, -28.97, 0.85, 15, 33.09, -60.99, 0.15, 2, 4, 89.07, -23.43, 0.85, 15, 29.91, -55.45, 0.15, 2, 4, 85.2, -4.26, 0.8, 15, 26.03, -36.28, 0.2, 2, 4, 87.45, 6.24, 0.75, 15, 28.29, -25.78, 0.25, 2, 4, 91.95, 11.86, 0.75, 15, 32.79, -20.16, 0.25, 2, 4, 102.07, 7.69, 0.75, 15, 42.9, -24.33, 0.25, 2, 4, 109.47, -1.19, 0.8, 15, 50.3, -33.21, 0.2, 2, 4, 117.93, -22.27, 0.85, 15, 58.76, -54.28, 0.15, 2, 4, 115.89, -35.97, 0.85, 15, 56.72, -67.99, 0.15, 2, 4, 109.1, -48.08, 0.9, 15, 49.94, -80.1, 0.1, 2, 4, 97.96, -41.16, 0.9, 15, 38.79, -73.18, 0.1, 2, 4, 88.59, -33.5, 0.85, 15, 29.43, -65.52, 0.15, 2, 4, 81.71, -23.36, 0.85, 15, 22.55, -55.38, 0.15, 2, 4, 78.7, -16.44, 0.8, 15, 19.53, -48.46, 0.2, 2, 4, 79.44, -4.24, 0.8, 15, 20.27, -36.25, 0.2, 2, 4, 81.79, 9.46, 0.75, 15, 22.63, -22.56, 0.25, 2, 4, 88.15, 23.18, 0.75, 15, 28.99, -8.84, 0.25, 2, 4, 101.74, 17.47, 0.75, 15, 42.57, -14.55, 0.25, 2, 4, 112.96, 7.99, 0.8, 15, 53.8, -24.03, 0.2, 2, 4, 116.37, -0.61, 0.8, 15, 57.21, -32.62, 0.2, 2, 4, 127.66, 27.72, 0.75, 15, 68.5, -4.3, 0.25, 2, 4, 116.36, 65.33, 0.75, 15, 57.2, 33.31, 0.25, 2, 4, 168.71, 68.66, 0.75, 15, 109.54, 36.64, 0.25, 2, 4, 140.76, 68.41, 0.75, 15, 81.6, 36.39, 0.25, 2, 4, 175.61, 28.47, 0.75, 15, 116.44, -3.55, 0.25, 2, 4, 149.75, 29.52, 0.75, 15, 90.58, -2.5, 0.25, 2, 4, 181.93, -22.6, 0.85, 15, 122.77, -54.62, 0.15, 2, 4, 161.01, -30.57, 0.85, 15, 101.85, -62.59, 0.15, 2, 4, 139.16, -32.15, 0.85, 15, 79.99, -64.17, 0.15, 2, 4, 68.65, 8.62, 0.75, 15, 9.48, -23.4, 0.25, 2, 4, 46.1, 5.85, 0.75, 15, -13.06, -26.17, 0.25, 2, 4, 29.6, 1.5, 0.75, 15, -29.56, -30.52, 0.25, 2, 4, 17.11, 2.77, 0.75, 15, -42.06, -29.25, 0.25, 2, 4, 63.11, 42.39, 0.75, 15, 3.94, 10.37, 0.25, 2, 4, 50.2, 41.76, 0.75, 15, -8.96, 9.74, 0.25, 2, 4, 41.03, 38.04, 0.75, 15, -18.13, 6.02, 0.25, 2, 4, 89.92, -53.06, 0.648, 15, 30.76, -85.08, 0.352, 2, 4, 77.84, -50.36, 0.768, 15, 18.68, -82.38, 0.232, 2, 4, 72.21, -27.01, 0.9, 15, 13.05, -59.02, 0.1, 2, 4, 76.67, -38.54, 0.9, 15, 17.51, -70.56, 0.1, 2, 4, 52.06, -29.07, 0.9, 15, -7.1, -61.09, 0.1, 2, 4, 93.96, 3.03, 0.76877, 15, 34.79, -28.99, 0.23123, 2, 4, 96.26, -3.65, 0.78156, 15, 37.09, -35.67, 0.21844, 2, 4, 98.24, -14.6, 0.81721, 15, 39.07, -46.62, 0.18279, 2, 4, 102.91, -25.63, 0.85295, 15, 43.74, -57.65, 0.14705, 2, 4, 105.11, -32.4, 0.87702, 15, 45.95, -64.42, 0.12298, 2, 4, 119.73, -27.08, 0.85, 15, 60.56, -59.1, 0.15, 2, 4, 117.02, -28.35, 0.85, 15, 57.85, -60.37, 0.15, 2, 4, 119.92, -9.95, 0.78159, 15, 60.75, -41.97, 0.21841, 2, 4, 120.58, -23.96, 0.85, 15, 61.42, -55.98, 0.15, 2, 4, 121.2, -27.32, 0.85, 15, 62.03, -59.34, 0.15, 2, 4, 120.35, -29.81, 0.85, 15, 61.18, -61.83, 0.15], "hull": 35, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 54, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 52, 96, 96, 98, 98, 100, 102, 104, 108, 110, 110, 112, 58, 114, 114, 116, 116, 118, 124, 126, 126, 128, 128, 130, 120, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 122, 122, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 120, 150, 154, 154, 156, 156, 158, 158, 160, 162, 164, 118, 166, 166, 120, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 122, 180, 180, 124, 178, 180, 180, 182, 182, 184, 184, 186, 186, 4, 2, 0, 0, 68, 162, 0, 104, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 106, 200, 200, 202, 202, 204, 204, 206, 206, 208, 102, 210, 212, 214, 214, 216, 216, 218, 218, 220, 106, 222, 222, 108, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 102, 74, 236, 74, 238, 56, 240, 240, 242, 52, 244, 244, 246, 50, 248, 248, 250, 250, 252, 226, 254, 254, 256, 256, 258, 258, 260, 176, 262, 262, 264, 264, 266, 40, 268, 268, 270, 272, 274, 272, 276, 274, 276, 204, 278, 278, 280, 280, 282, 282, 284, 284, 286, 210, 288, 290, 212, 288, 290, 100, 292, 292, 102, 234, 292, 292, 294, 294, 296, 296, 298], "width": 181, "height": 203}}, "shou_zuo2": {"shou_zuo": {"type": "mesh", "uvs": [0.3567, 0, 0.40714, 0.05263, 0.4891, 0.11637, 0.59314, 0.1814, 0.66249, 0.23603, 0.6751, 0.31667, 0.64988, 0.40252, 0.66249, 0.43894, 0.69402, 0.50267, 0.76022, 0.60413, 0.87687, 0.70948, 0.75707, 0.79533, 0.60575, 0.83175, 0.41344, 0.83695, 0.37246, 0.71729, 0.33778, 0.58592, 0.29365, 0.48446, 0.26843, 0.43504, 0.13602, 0.37911, 0.01622, 0.30627, 0, 0.23993, 0.03198, 0.19311, 0, 0.13718, 0.04459, 0.11246, 0.05405, 0.07995, 0.1171, 0.03442, 0.22429, 0.01231, 0.59115, 0.67482, 0.49458, 0.54959, 0.45319, 0.42151, 0.40145, 0.3034, 0.29108, 0.19382, 0.1807, 0.07428], "triangles": [32, 25, 26, 24, 25, 32, 21, 22, 23, 26, 1, 32, 1, 26, 0, 2, 32, 1, 31, 2, 3, 2, 31, 32, 23, 24, 32, 31, 23, 32, 30, 31, 3, 30, 3, 4, 31, 19, 20, 21, 23, 31, 30, 4, 5, 21, 31, 20, 30, 19, 31, 30, 18, 19, 6, 30, 5, 29, 30, 6, 17, 18, 30, 29, 17, 30, 16, 17, 29, 29, 6, 7, 28, 29, 7, 28, 7, 8, 16, 29, 28, 15, 16, 28, 28, 8, 9, 27, 28, 9, 15, 28, 27, 27, 9, 10, 14, 15, 27, 11, 27, 10, 12, 27, 11, 14, 27, 12, 13, 14, 12], "vertices": [1, 8, 287.02, 184.97, 1, 1, 8, 272.38, 178.04, 1, 1, 8, 252.87, 171.35, 1, 1, 8, 231.19, 166.16, 1, 1, 8, 214.54, 160.34, 1, 1, 8, 197.69, 144.52, 1, 1, 8, 183.09, 124.56, 1, 1, 8, 174.89, 117.98, 1, 1, 8, 159.71, 107.21, 1, 1, 8, 134.18, 91.36, 1, 1, 8, 103.54, 78.77, 1, 1, 8, 93.01, 52.11, 1, 1, 8, 94.87, 24.99, 1, 1, 8, 100.26, 3.67, 1, 1, 8, 145.47, 36.47, 1, 1, 8, 174.13, 61.1, 1, 1, 8, 197.76, 78.73, 1, 1, 8, 209.6, 87.01, 1, 1, 8, 231.93, 88.01, 1, 1, 8, 256.49, 93.56, 1, 1, 8, 270.85, 106.11, 1, 1, 8, 277.25, 118.46, 1, 1, 8, 290.94, 127.56, 1, 1, 8, 291.92, 136.32, 1, 1, 8, 297.46, 143.87, 1, 1, 8, 300.93, 158.46, 1, 1, 8, 296.02, 171.72, 1, 1, 8, 134.93, 62.97, 1, 1, 8, 167.73, 81.33, 1, 1, 8, 196.32, 104.73, 1, 1, 8, 223.86, 125.22, 1, 1, 8, 254.79, 139.2, 1, 1, 8, 287.66, 155.26, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 22, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 118, "height": 286}}, "paopao3": {"paopao1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [413.21, -16.85, -234.79, -16.85, -234.79, 406.15, 413.21, 406.15], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 648, "height": 423}}, "shui": {"shui": {"type": "mesh", "uvs": [0.02253, 0.10204, 0.15431, 0.05619, 0.25606, 0, 0.43621, 0, 0.61135, 0.07256, 0.76314, 0.03981, 0.89492, 0.1741, 0.95664, 0.39027, 1, 0.63592, 1, 0.81933, 0.9116, 0.87501, 0.86156, 1, 0.72144, 1, 0.65305, 0.86191, 0.51627, 0.69815, 0.39451, 0.64902, 0.23604, 0.55731, 0.12595, 0.45905, 0.02921, 0.38044, 0, 0.34441, 0, 0.21668, 0.11594, 0.23633, 0.25606, 0.24943, 0.39784, 0.29528, 0.55297, 0.36079, 0.66807, 0.45905, 0.79484, 0.63264, 0.85655, 0.76693, 0.89492, 0.59989, 0.81819, 0.43285, 0.73479, 0.28873, 0.63637, 0.2134, 0.51294, 0.14462, 0.39784, 0.10204, 0.28108, 0.11187, 0.14263, 0.13807, 0.06424, 0.13807, 0.06924, 0.32476, 0.20268, 0.40337, 0.37115, 0.4787, 0.49626, 0.53438, 0.58633, 0.59334, 0.68141, 0.72762, 0.7865, 0.87829], "triangles": [33, 2, 3, 34, 2, 33, 36, 0, 1, 35, 36, 1, 34, 35, 1, 34, 1, 2, 32, 3, 4, 33, 3, 32, 31, 4, 5, 32, 4, 31, 20, 0, 36, 21, 36, 35, 20, 36, 21, 22, 35, 34, 21, 35, 22, 30, 31, 5, 30, 5, 6, 23, 34, 33, 23, 33, 32, 22, 34, 23, 37, 20, 21, 19, 20, 37, 24, 32, 31, 23, 32, 24, 18, 19, 37, 38, 21, 22, 37, 21, 38, 29, 30, 6, 29, 6, 7, 17, 37, 38, 18, 37, 17, 25, 31, 30, 25, 30, 29, 24, 31, 25, 39, 22, 23, 38, 22, 39, 40, 23, 24, 39, 23, 40, 16, 38, 39, 17, 38, 16, 41, 24, 25, 40, 24, 41, 28, 29, 7, 26, 25, 29, 26, 29, 28, 28, 7, 8, 15, 39, 40, 16, 39, 15, 14, 40, 41, 15, 40, 14, 42, 25, 26, 41, 25, 42, 27, 26, 28, 28, 8, 9, 10, 27, 28, 13, 41, 42, 14, 41, 13, 9, 10, 28, 43, 42, 26, 43, 26, 27, 13, 42, 43, 12, 13, 43, 11, 27, 10, 43, 27, 11, 12, 43, 11], "vertices": [340.43, 9.17, 283.03, -21.12, 240.27, -49.39, 157.06, -76.05, 70.7, -84.91, 3.06, -115.08, -67.92, -102.99, -112.72, -61.28, -151.27, -9.92, -165.09, 33.23, -128.46, 59.41, -114.77, 96.21, -50.05, 116.95, -6.97, 125.98, 63.83, 114.95, 122.97, 113.99, 212.51, 143.57, 289.65, 132.46, 357.43, 141.75, 396.66, 117.91, 342.2, 39.47, 287.17, 26.94, 221.47, 9.28, 152.52, -0.92, 75.94, -8.47, 15.37, -2.39, -56.26, 19.68, -94.89, 42.13, -100.02, -2.84, -51.99, -30.77, -2.61, -52.33, 48.53, -55.48, 110.72, -53.39, 167.09, -46.37, 220.28, -26.78, 282.25, -0.13, 318.46, 11.48, 327.67, 91.51, 265.55, 63.34, 182.07, 56.12, 89.05, 40.76, 43, 41.29, -11.03, 58.81, -70.93, 78.69], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 485, "height": 247}}, "shui3": {"shui": {"type": "mesh", "uvs": [0.02253, 0.10204, 0.15431, 0.05619, 0.25606, 0, 0.43621, 0, 0.61135, 0.07256, 0.76314, 0.03981, 0.89492, 0.1741, 0.95664, 0.39027, 1, 0.63592, 1, 0.81933, 0.9116, 0.87501, 0.86156, 1, 0.72144, 1, 0.65305, 0.86191, 0.51627, 0.69815, 0.39451, 0.64902, 0.23604, 0.55731, 0.12595, 0.45905, 0.02921, 0.38044, 0, 0.34441, 0, 0.21668, 0.11594, 0.23633, 0.25606, 0.24943, 0.39784, 0.29528, 0.55297, 0.36079, 0.66807, 0.45905, 0.79484, 0.63264, 0.85655, 0.76693, 0.89492, 0.59989, 0.81819, 0.43285, 0.73479, 0.28873, 0.63637, 0.2134, 0.51294, 0.14462, 0.39784, 0.10204, 0.28108, 0.11187, 0.14263, 0.13807, 0.06424, 0.13807, 0.06924, 0.32476, 0.20268, 0.40337, 0.37115, 0.4787, 0.49626, 0.53438, 0.58633, 0.59334, 0.68141, 0.72762, 0.7865, 0.87829], "triangles": [33, 2, 3, 34, 2, 33, 36, 0, 1, 35, 36, 1, 34, 35, 1, 34, 1, 2, 32, 3, 4, 33, 3, 32, 31, 4, 5, 32, 4, 31, 20, 0, 36, 21, 36, 35, 20, 36, 21, 22, 35, 34, 21, 35, 22, 30, 31, 5, 30, 5, 6, 23, 34, 33, 23, 33, 32, 22, 34, 23, 37, 20, 21, 19, 20, 37, 24, 32, 31, 23, 32, 24, 18, 19, 37, 38, 21, 22, 37, 21, 38, 29, 30, 6, 29, 6, 7, 17, 37, 38, 18, 37, 17, 25, 31, 30, 25, 30, 29, 24, 31, 25, 39, 22, 23, 38, 22, 39, 40, 23, 24, 39, 23, 40, 16, 38, 39, 17, 38, 16, 41, 24, 25, 40, 24, 41, 28, 29, 7, 26, 25, 29, 26, 29, 28, 28, 7, 8, 15, 39, 40, 16, 39, 15, 14, 40, 41, 15, 40, 14, 42, 25, 26, 41, 25, 42, 27, 26, 28, 28, 8, 9, 10, 27, 28, 13, 41, 42, 14, 41, 13, 9, 10, 28, 43, 42, 26, 43, 26, 27, 13, 42, 43, 12, 13, 43, 11, 27, 10, 43, 27, 11, 12, 43, 11], "vertices": [340.43, 9.17, 283.03, -21.12, 240.27, -49.39, 157.06, -76.05, 70.7, -84.91, 3.06, -115.08, -67.92, -102.99, -112.72, -61.28, -151.27, -9.92, -165.09, 33.23, -128.46, 59.41, -114.77, 96.21, -50.05, 116.95, -6.97, 125.98, 63.83, 114.95, 122.97, 113.99, 212.51, 143.57, 289.65, 132.46, 357.43, 141.75, 396.66, 117.91, 342.2, 39.47, 287.17, 26.94, 221.47, 9.28, 152.52, -0.92, 75.94, -8.47, 15.37, -2.39, -56.26, 19.68, -94.89, 42.13, -100.02, -2.84, -51.99, -30.77, -2.61, -52.33, 48.53, -55.48, 110.72, -53.39, 167.09, -46.37, 220.28, -26.78, 282.25, -0.13, 318.46, 11.48, 327.67, 91.51, 265.55, 63.34, 182.07, 56.12, 89.05, 40.76, 43, 41.29, -11.03, 58.81, -70.93, 78.69], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 485, "height": 247}}, "bu_gebo_zuo": {"bu_gebo_zuo": {"type": "mesh", "uvs": [0.99341, 0.00081, 0.90834, 0.05059, 0.81557, 0.07395, 0.71031, 0.03208, 0.6124, 0.00591, 0.47778, 0.02336, 0.35539, 0.08965, 0.26972, 0.19259, 0.20241, 0.33565, 0.14244, 0.47697, 0.06288, 0.5991, 0, 0.71845, 0, 0.75285, 0.06108, 0.781, 0.11301, 0.89568, 0.16932, 1, 0.22051, 0.94468, 0.28471, 0.85605, 0.36097, 0.75329, 0.45709, 0.6818, 0.5459, 0.60286, 0.64385, 0.5027, 0.7337, 0.37312, 0.80997, 0.26589, 0.87056, 0.17653, 0.94474, 0.08568, 0.44936, 0.54886, 0.39888, 0.66471, 0.17167, 0.84078, 0.26657, 0.71497, 0.34817, 0.53488, 0.38922, 0.30657, 0.13836, 0.67452, 0.22748, 0.55169, 0.30645, 0.41021, 0.56183, 0.44255, 0.62865, 0.38591, 0.72617, 0.27392, 0.80383, 0.17095], "triangles": [19, 26, 20, 20, 35, 21, 35, 36, 21, 21, 36, 22, 36, 37, 22, 22, 37, 23, 37, 38, 23, 23, 38, 24, 24, 1, 25, 18, 27, 19, 15, 28, 16, 16, 28, 17, 28, 29, 17, 17, 29, 18, 25, 1, 0, 15, 14, 28, 14, 13, 28, 13, 32, 28, 28, 32, 29, 10, 32, 13, 10, 13, 11, 11, 13, 12, 32, 33, 29, 29, 30, 27, 29, 33, 30, 27, 26, 19, 32, 9, 33, 32, 10, 9, 27, 30, 26, 20, 26, 35, 9, 8, 33, 33, 34, 30, 33, 8, 34, 35, 26, 31, 26, 30, 31, 30, 34, 31, 35, 31, 36, 8, 7, 34, 34, 7, 31, 31, 5, 36, 5, 4, 36, 37, 4, 3, 37, 36, 4, 7, 6, 31, 31, 6, 5, 37, 3, 38, 38, 2, 24, 24, 2, 1, 38, 3, 2, 18, 29, 27], "vertices": [3, 6, 29.07, -61.71, 0.00123, 19, 61.13, 6.07, 0.692, 18, 132.26, 1.71, 0.30677, 1, 9, -76.69, 33.32, 1, 1, 9, -58.81, 23.53, 1, 2, 6, 20.89, -19.69, 0.264, 9, -53.44, 9.91, 0.736, 2, 6, 23.24, -6.55, 0.168, 9, -47.49, -2.04, 0.832, 2, 6, 21.45, 11.48, 0.24, 9, -35.31, -15.44, 0.76, 2, 6, 15.07, 27.82, 0.008, 9, -20.44, -24.77, 0.992, 2, 6, 5.3, 39.22, 0.008, 9, -5.8, -28.09, 0.992, 2, 6, -8.23, 48.13, 0.008, 9, 10.37, -27.17, 0.992, 2, 6, -21.58, 56.05, 0.008, 9, 25.81, -25.57, 0.992, 2, 6, -33.15, 66.61, 0.008, 9, 41.39, -27.15, 0.992, 2, 6, -44.44, 74.94, 0.008, 9, 55.43, -27.1, 0.992, 2, 6, -47.67, 74.91, 0.008, 9, 58, -25.15, 0.992, 2, 6, -50.25, 66.71, 0.008, 9, 55.18, -17.03, 0.992, 1, 9, 59.58, -4.98, 1, 2, 19, -63.68, 91.59, 0.556, 18, 7.44, 87.23, 0.444, 3, 6, -65.45, 45.21, 0.0015, 19, -56.9, 86.29, 0.684, 18, 14.22, 81.93, 0.3145, 2, 19, -46.65, 80.1, 0.596, 18, 24.48, 75.74, 0.404, 2, 19, -34.7, 72.69, 0.596, 18, 36.43, 68.33, 0.404, 3, 6, -40.47, 13.72, 0.00149, 19, -24.91, 61.96, 0.588, 18, 46.22, 57.6, 0.41051, 3, 6, -32.95, 1.89, 0.0046, 19, -14.69, 52.35, 0.42, 18, 56.43, 47.99, 0.5754, 3, 6, -23.42, -11.16, 0.00604, 19, -2.24, 42.07, 0.364, 18, 68.89, 37.71, 0.62996, 3, 6, -11.14, -23.09, 0.13057, 19, 12.62, 33.54, 0.31031, 18, 83.74, 29.18, 0.55912, 3, 6, -0.97, -33.23, 0.17057, 19, 24.97, 26.24, 0.29604, 18, 96.1, 21.88, 0.5334, 3, 6, 7.5, -41.28, 0.00234, 19, 35.17, 20.54, 0.412, 18, 106.3, 16.17, 0.58566, 3, 6, 16.12, -51.14, 0.00209, 19, 45.97, 13.11, 0.476, 18, 117.09, 8.75, 0.52191, 3, 6, -27.98, 14.86, 0.064, 9, 6.39, 11.31, 0.78523, 18, 58.03, 61.79, 0.15077, 2, 6, -38.93, 21.54, 0.024, 9, 19.16, 12.48, 0.976, 4, 6, -55.74, 51.84, 0.00261, 9, 50.72, -1.82, 0.55698, 19, -49.13, 95.12, 0.26248, 18, 21.99, 90.75, 0.17792, 4, 6, -43.81, 39.22, 0.00239, 9, 33.62, 1.19, 0.35356, 19, -34.45, 85.84, 0.38386, 18, 36.68, 81.48, 0.2602, 2, 6, -26.78, 28.43, 0.04733, 9, 13.52, -0.3, 0.95267, 2, 6, -5.28, 23.12, 0.18451, 9, -6.91, -8.85, 0.81549, 4, 6, -40.15, 56.44, 0.00549, 9, 40.95, -14.81, 0.73843, 19, -35.17, 103.42, 0.15263, 18, 35.96, 99.06, 0.10346, 4, 6, -28.5, 44.59, 0.01692, 9, 24.54, -12.24, 0.82202, 19, -20.95, 94.83, 0.09599, 18, 50.18, 90.47, 0.06507, 2, 6, -15.11, 34.12, 0.06607, 9, 7.55, -11.82, 0.93393, 4, 6, -17.86, -0.12, 0.14274, 9, -10.67, 17.31, 0.3248, 19, 0.42, 54.14, 0.19499, 18, 71.55, 49.78, 0.33746, 4, 6, -12.46, -9.03, 0.14006, 9, -20.31, 21.24, 0.24145, 19, 7.86, 46.84, 0.22352, 18, 78.98, 42.48, 0.39496, 4, 6, -1.82, -22.01, 0.16948, 9, -36.59, 25.31, 0.21591, 19, 21.37, 36.9, 0.21938, 18, 92.5, 32.54, 0.39523, 4, 6, 7.95, -32.33, 0.08532, 9, -50.59, 27.78, 0.52214, 19, 33.39, 29.32, 0.14011, 18, 104.52, 24.95, 0.25244], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50], "width": 134, "height": 94}}, "toufa_qian": {"toufa_qian": {"type": "mesh", "uvs": [0.28973, 0.44872, 0.29714, 0.48251, 0.3104, 0.51769, 0.34861, 0.51769, 0.38799, 0.50459, 0.41841, 0.47976, 0.45428, 0.45354, 0.47182, 0.41285, 0.48235, 0.36939, 0.48547, 0.33697, 0.49756, 0.39353, 0.49773, 0.45753, 0.49009, 0.51055, 0.4801, 0.55317, 0.48069, 0.65713, 0.48421, 0.76213, 0.52789, 0.89667, 0.58538, 0.9426, 0.6577, 0.98197, 0.73652, 0.99673, 0.81937, 0.9777, 0.86772, 0.93493, 0.93146, 0.95631, 1, 1, 1, 0.84162, 1, 0.66473, 1, 0.50727, 1, 0.36731, 0.96003, 0.40814, 0.90838, 0.4995, 0.84575, 0.55004, 0.78751, 0.59281, 0.72597, 0.53838, 0.6941, 0.42174, 0.66883, 0.27789, 0.63586, 0.13016, 0.54026, 0.01158, 0.44136, 0, 0.37103, 0, 0.2985, 0.07184, 0.24246, 0.18459, 0.2018, 0.36148, 0.17103, 0.49172, 0.13477, 0.59086, 0.09851, 0.66667, 0.03867, 0.67913, 0, 0.67088, 0, 0.74926, 0.02235, 0.76576, 0.07948, 0.81321, 0.13662, 0.86271, 0.21008, 0.91634, 0.30803, 0.91222, 0.34068, 0.8689, 0.38266, 0.78639, 0.38032, 0.71832, 0.34418, 0.63169, 0.30741, 0.55027, 0.33258, 0.56455, 0.33, 0.54228, 0.31033, 0.52614, 0.30369, 0.51441, 0.29122, 0.48622, 0.28924, 0.46698, 0.56953, 0.61232, 0.56758, 0.56791, 0.56433, 0.52733, 0.56239, 0.49938, 0.57299, 0.48483, 0.58857, 0.49402, 0.59571, 0.53001, 0.58944, 0.5725, 0.58814, 0.60581], "triangles": [68, 10, 34, 67, 11, 68, 12, 67, 66, 69, 66, 68, 66, 67, 68, 66, 70, 65, 64, 65, 72, 72, 65, 71, 71, 65, 70, 65, 14, 66, 64, 14, 65, 70, 66, 69, 68, 34, 69, 70, 69, 33, 32, 71, 70, 53, 56, 55, 57, 56, 51, 9, 39, 38, 37, 9, 38, 9, 37, 36, 22, 24, 23, 31, 30, 21, 24, 21, 25, 25, 21, 30, 25, 30, 29, 29, 26, 25, 29, 28, 26, 28, 27, 26, 20, 19, 31, 19, 18, 31, 20, 31, 21, 22, 21, 24, 17, 72, 18, 16, 64, 17, 17, 64, 72, 16, 15, 64, 15, 14, 64, 18, 72, 32, 18, 32, 31, 70, 33, 32, 32, 72, 71, 10, 9, 34, 9, 35, 34, 9, 36, 35, 33, 69, 34, 12, 66, 13, 66, 14, 13, 12, 11, 67, 68, 11, 10, 7, 6, 5, 5, 8, 7, 0, 9, 5, 9, 8, 5, 0, 4, 3, 1, 3, 2, 5, 4, 0, 0, 40, 39, 61, 57, 62, 62, 57, 42, 62, 42, 63, 57, 61, 60, 1, 0, 3, 63, 41, 0, 9, 0, 39, 42, 41, 63, 41, 40, 0, 49, 44, 50, 48, 45, 49, 49, 45, 44, 48, 47, 45, 47, 46, 45, 53, 55, 54, 57, 59, 58, 57, 60, 59, 52, 51, 56, 53, 52, 56, 51, 50, 57, 44, 43, 50, 42, 57, 43, 57, 50, 43], "vertices": [2, 31, 35.81, 1.19, 0.96963, 29, 42.45, -32.61, 0.03037, 5, 51, -42.73, -7.46, 0.00517, 56, -47.28, 2.39, 0.00588, 47, -29.44, 31.05, 0.00078, 31, 46.34, 4.33, 0.68805, 29, 52.58, -28.37, 0.30011, 5, 51, -44.6, 5.21, 0.00555, 56, -37.71, 10.92, 0.00642, 47, -35.25, 42.47, 0.00084, 31, 57.53, 10.56, 0.49001, 29, 63.05, -20.98, 0.49717, 6, 51, -63.13, 13.95, 0.00298, 56, -40.57, 31.2, 0.00344, 47, -55.6, 44.86, 0.00045, 31, 59.16, 30.98, 0.18865, 29, 62.5, -0.51, 0.7695, 27, 66.59, -56.79, 0.03499, 6, 51, -83.91, 19.37, 0.00059, 56, -47.45, 51.55, 0.00064, 47, -77.02, 43.38, 9e-05, 31, 56.89, 52.34, 0.01, 29, 57.97, 20.49, 0.75209, 27, 64.74, -35.39, 0.23658, 5, 51, -101.87, 19.52, 5e-05, 56, -57.17, 66.64, 5e-05, 47, -94.09, 37.82, 1e-05, 29, 50.02, 36.59, 0.45913, 27, 58.88, -18.42, 0.54076, 4, 29, 41.56, 55.6, 0.05688, 27, 52.89, 1.5, 0.94294, 32, 65.16, -59.8, 0.00016, 33, 31.28, -51.1, 3e-05, 3, 27, 41.55, 12.09, 0.96502, 32, 58.8, -45.66, 0.02722, 33, 19.01, -41.61, 0.00776, 3, 27, 29.01, 19.01, 0.81596, 32, 49.9, -34.43, 0.13932, 33, 5.88, -35.89, 0.04472, 4, 26, 57.21, 14.56, 0.00399, 27, 19.4, 21.65, 0.50307, 32, 42.06, -28.28, 0.36341, 33, -3.94, -34.15, 0.12953, 3, 27, 37.1, 26.39, 0.11539, 32, 60.22, -30.75, 0.26775, 33, 13.24, -27.78, 0.61686, 3, 27, 56.41, 24.55, 0.02025, 32, 77.31, -39.9, 0.04817, 33, 32.63, -27.81, 0.93158, 4, 27, 71.98, 18.88, 0.00222, 32, 89.49, -51.14, 0.00336, 33, 48.67, -32.01, 0.98214, 34, -41.1, -15.07, 0.01228, 3, 27, 84.3, 12.27, 2e-05, 33, 61.55, -37.45, 0.91387, 34, -32.06, -25.74, 0.08611, 2, 33, 93.05, -37.33, 0.39538, 34, -3.9, -39.87, 0.60462, 3, 33, 124.88, -35.64, 0.0543, 34, 25.25, -52.74, 0.91258, 35, -61.61, -9.86, 0.03312, 3, 34, 72.21, -50.56, 0.54205, 35, -29.16, -43.87, 0.45691, 45, -107.02, -54.88, 0.00104, 3, 34, 98.68, -29.52, 0.1359, 35, 4.08, -50.04, 0.82589, 45, -73.29, -52.59, 0.03821, 2, 35, 44.58, -52.38, 0.71603, 45, -33.49, -44.8, 0.28397, 2, 35, 86.67, -46.66, 0.12044, 45, 5.86, -28.79, 0.87956, 1, 45, 42.29, -2.74, 1, 3, 39, 143.9, -64.1, 0.00536, 45, 59.01, 20.92, 0.7304, 40, 3.62, -87.43, 0.26425, 2, 45, 92.19, 31.34, 0.47421, 40, 35.27, -101.83, 0.52579, 2, 45, 130.82, 37.02, 0.42547, 40, 67.81, -123.41, 0.57453, 2, 45, 108.16, 79.32, 0.33035, 40, 79.21, -76.8, 0.66965, 2, 45, 82.85, 126.57, 0.06551, 40, 91.94, -24.73, 0.93449, 1, 40, 103.27, 21.61, 1, 1, 40, 121.5, 58.32, 1, 1, 40, 95.3, 54.48, 1, 1, 40, 56.12, 35.56, 1, 2, 39, 80.83, 34.7, 0.13889, 40, 19.87, 28.66, 0.86111, 2, 39, 58.8, 9.06, 0.95692, 40, -13.53, 23.48, 0.04308, 2, 38, 101.2, 18.14, 0.05066, 39, 21.93, 8.92, 0.94934, 3, 38, 62.49, 24.63, 0.91504, 36, 91.15, -31.64, 0.04214, 39, -9.25, 32.77, 0.04283, 2, 38, 19.09, 38.77, 0.20272, 36, 56.02, -2.5, 0.79728, 1, 36, 16.8, 25.39, 1, 3, 32, -30.67, 44.47, 0.43441, 36, -45.75, 27.56, 0.37116, 25, 24.24, -64.58, 0.19443, 4, 30, -100.11, 26.49, 0.00131, 32, -58.99, -0.48, 0.14069, 36, -92.09, 1.59, 0.02977, 25, 29.43, -11.7, 0.82823, 3, 30, -81.97, -6.56, 0.10048, 28, -62.5, -18.13, 0.12599, 25, 30.62, 25.97, 0.77353, 3, 30, -44.18, -30.16, 0.48976, 28, -30.34, -48.96, 0.22474, 25, 10.1, 65.52, 0.2855, 4, 47, -10.88, -62.03, 0.08229, 30, 0.22, -40.05, 0.89216, 28, 11.1, -67.72, 0.00178, 25, -23.09, 96.63, 0.02377, 2, 47, 17.02, -11.34, 0.93706, 30, 57.69, -33.36, 0.06294, 2, 51, 19.59, -33.79, 0.68603, 47, 38.01, 25.93, 0.31397, 3, 51, 49.99, -14.92, 0.94238, 52, 0.35, -18.61, 0.04497, 47, 60.83, 53.49, 0.01265, 2, 58, 66.16, -60.82, 0.06179, 52, 14.34, 8.04, 0.93821, 3, 58, 97.93, -55.01, 0.11683, 52, 44.83, 18.68, 0.88316, 47, 115.11, 74.03, 1e-05, 3, 58, 118.77, -56.19, 0.10389, 52, 65.61, 20.73, 0.8961, 47, 135.4, 69.13, 1e-05, 3, 58, 117.26, -32.49, 0.11433, 52, 60.46, 43.92, 0.88566, 47, 138.18, 92.71, 1e-05, 3, 58, 104.99, -28.26, 0.1365, 52, 47.68, 46.2, 0.86349, 47, 126.86, 99.08, 1e-05, 4, 51, 105.54, 33.36, 0.00251, 58, 73.52, -15.86, 0.39971, 52, 14.66, 53.59, 0.59778, 47, 98.13, 116.93, 1e-05, 3, 58, 42, -2.84, 0.98069, 52, -18.49, 61.59, 0.01931, 47, 69.47, 135.41, 0, 2, 58, 1.67, 10.88, 0.63029, 57, 53.32, 5.49, 0.36971, 2, 57, 17.08, 43.49, 0.86187, 56, 80.83, 26.33, 0.13813, 2, 57, -4.4, 47.61, 0.59829, 56, 65.4, 41.83, 0.40171, 2, 57, -38.03, 47.45, 0.2042, 56, 37.5, 60.62, 0.7958, 1, 25, -187.08, 27.9, 1, 1, 25, -160.23, 46.43, 1, 4, 51, -38.94, 13.45, 0.09389, 56, -27.72, 10.71, 0.5193, 31, 67.24, 8.18, 0.37736, 29, 72.96, -22.32, 0.00946, 5, 51, -49.3, 23.12, 0.05979, 56, -25.31, 24.67, 0.47413, 47, -45.41, 57.96, 1e-05, 31, 72.63, 21.29, 0.45334, 29, 76.93, -8.71, 0.01272, 5, 51, -50.93, 16.43, 0.06126, 56, -31.8, 22.36, 0.47893, 47, -44.82, 51.1, 2e-05, 31, 65.79, 20.45, 0.44727, 29, 70.22, -10.28, 0.01253, 4, 51, -43.48, 7.51, 0.09949, 56, -35.18, 11.24, 0.43349, 31, 60.08, 10.32, 0.45615, 29, 65.61, -20.95, 0.01087, 4, 51, -41.78, 2.77, 0.11602, 56, -38.2, 7.22, 0.33475, 31, 56.25, 7.06, 0.53853, 29, 62.15, -24.6, 0.01071, 4, 51, -39.38, -7.8, 0.08419, 56, -45.72, -0.59, 0.11316, 31, 47.2, 1.08, 0.79396, 29, 53.79, -31.51, 0.00869, 4, 51, -40.91, -13.53, 0.02439, 56, -51.35, -2.46, 0.02728, 47, -25.79, 25.88, 0.0037, 31, 41.31, 0.49, 0.94462, 7, 27, 106.9, 58.18, 1e-05, 32, 136.88, -28.38, 1e-05, 33, 79.78, 10.37, 0.07067, 38, 71.01, -63.23, 0.00498, 39, -42.83, -48.87, 0.0934, 34, 5.8, 8.69, 0.10292, 4, 68.21, -59.73, 0.728, 1, 4, 81.69, -59.11, 1, 8, 27, 81, 57.97, 0.00018, 32, 112.9, -18.57, 0.00042, 33, 54.01, 7.75, 0.00302, 38, 48.39, -50.62, 0.00108, 36, 50.29, -96.39, 5e-05, 39, -56.92, -27.14, 0.00157, 34, -18.38, 17.99, 0.00168, 4, 94.04, -57.76, 0.992, 8, 27, 72.47, 57.78, 0.00128, 32, 104.96, -15.45, 0.00297, 33, 45.53, 6.76, 0.01537, 38, 40.88, -46.58, 0.00495, 36, 44.8, -89.86, 0.00186, 39, -61.67, -20.05, 0.00656, 34, -26.38, 20.94, 0.00702, 4, 102.54, -56.99, 0.96, 8, 27, 68.65, 63.87, 0.00046, 32, 103.79, -8.36, 0.00108, 33, 41.16, 12.47, 0.00559, 38, 40.57, -39.39, 0.00253, 36, 47.16, -83.07, 0.00137, 39, -58.58, -13.55, 0.00241, 34, -27.7, 28.01, 0.00255, 4, 106.76, -62.81, 0.984, 8, 27, 72.25, 71.9, 0.0006, 32, 110.21, -2.34, 0.00139, 33, 44, 20.81, 0.00753, 38, 47.67, -34.18, 0.00346, 36, 55.68, -80.85, 0.00176, 39, -49.87, -12.27, 0.00571, 34, -21.41, 34.16, 0.00356, 4, 103.71, -71.07, 0.976, 8, 27, 83.48, 74.63, 0.00102, 32, 121.62, -4.16, 0.00237, 33, 54.92, 24.57, 0.01734, 38, 58.78, -37.36, 0.00651, 36, 64.83, -87.9, 0.003, 39, -41.54, -20.28, 0.02113, 34, -9.96, 32.58, 0.01262, 4, 92.69, -74.55, 0.936, 8, 27, 95.96, 70, 0.00033, 32, 131.35, -13.25, 0.00077, 33, 67.78, 21.12, 0.01202, 38, 67.34, -47.55, 0.00271, 36, 69.03, -100.53, 0.00098, 39, -38.74, -33.29, 0.01779, 34, -0.05, 23.7, 0.01339, 4, 79.93, -70.78, 0.952, 8, 27, 105.93, 68.3, 2e-05, 32, 139.89, -18.67, 4e-05, 33, 77.87, 20.36, 0.22827, 38, 75.17, -53.95, 0.02197, 39, -34.81, -42.61, 0.41167, 34, 8.61, 18.46, 0.33246, 35, -18.8, 49.42, 0.00398, 45, -120.18, 38.06, 0.00158], "hull": 64, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 0, 126, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144], "width": 536, "height": 303}}, "paopao1": {"paopao1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [413.21, -16.85, -234.79, -16.85, -234.79, 406.15, 413.21, 406.15], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 648, "height": 423}}, "paopao2": {"paopao1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [413.21, -16.85, -234.79, -16.85, -234.79, 406.15, 413.21, 406.15], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 648, "height": 423}}, "shenti": {"shenti": {"type": "mesh", "uvs": [0.43149, 0.21318, 0.42684, 0.15485, 0.42498, 0.09011, 0.47054, 0.0337, 0.52446, 0.00037, 0.57652, 0, 0.62952, 0.01896, 0.64253, 0.06575, 0.63138, 0.10998, 0.63974, 0.15164, 0.67507, 0.19395, 0.73537, 0.2202, 0.83293, 0.22948, 0.93184, 0.23496, 0.99148, 0.25989, 1, 0.30977, 0.98268, 0.37043, 0.94358, 0.42975, 0.87866, 0.48929, 0.84278, 0.54448, 0.80781, 0.60221, 0.77193, 0.66691, 0.7342, 0.73415, 0.68267, 0.77919, 0.67042, 0.84465, 0.74375, 0.90651, 0.81998, 0.96638, 0.83252, 1, 0.6115, 1, 0.33355, 0.99541, 0.11517, 0.96172, 0.04339, 0.90591, 0.01438, 0.81853, 0.06733, 0.80445, 0.08976, 0.77295, 0.12299, 0.72656, 0.10638, 0.65956, 0.07946, 0.60707, 0.03166, 0.55075, 0.03166, 0.48844, 0.07685, 0.42314, 0.14203, 0.37461, 0.17283, 0.34232, 0.19476, 0.2894, 0.24136, 0.24783, 0.29618, 0.23145, 0.35101, 0.24153, 0.39944, 0.23334, 0.52443, 0.03971, 0.51389, 0.09241, 0.51652, 0.1445, 0.51301, 0.20144, 0.50862, 0.24626, 0.45063, 0.28745, 0.3944, 0.33288, 0.34461, 0.38269, 0.30955, 0.43661, 0.29472, 0.49981, 0.30416, 0.55326, 0.33112, 0.60438, 0.32765, 0.66558, 0.30523, 0.73725, 0.25937, 0.83984, 0.22472, 0.91502, 0.87164, 0.31193, 0.76053, 0.347, 0.68824, 0.42729, 0.63336, 0.52051, 0.60659, 0.58788, 0.58918, 0.66171, 0.55304, 0.73647, 0.50619, 0.8306, 0.46737, 0.92843, 0.63871, 0.92474, 0.72171, 0.96812, 0.65831, 0.27761, 0.55259, 0.32813, 0.49493, 0.38859, 0.45889, 0.46726, 0.45169, 0.55671, 0.4637, 0.63208, 0.44688, 0.70413, 0.40363, 0.77204, 0.36279, 0.85983, 0.34625, 0.93179, 0.31351, 0.27638, 0.26674, 0.32072, 0.24569, 0.37312, 0.18138, 0.42068, 0.12291, 0.46986, 0.1194, 0.52548, 0.16267, 0.57063, 0.19775, 0.62545, 0.2071, 0.70203, 0.17086, 0.77378, 0.13928, 0.84392, 0.09836, 0.89954, 0.81338, 0.25484, 0.94302, 0.28487, 0.85592, 0.39589, 0.93694, 0.34701], "triangles": [33, 34, 95, 96, 33, 95, 32, 33, 96, 31, 32, 96, 63, 95, 62, 96, 95, 63, 83, 63, 62, 84, 83, 72, 84, 63, 83, 30, 96, 63, 31, 96, 30, 74, 73, 25, 74, 25, 26, 29, 63, 84, 30, 63, 29, 28, 72, 73, 28, 73, 74, 29, 84, 72, 28, 29, 72, 74, 26, 27, 28, 74, 27, 95, 34, 94, 95, 94, 62, 83, 62, 82, 73, 24, 25, 72, 83, 71, 72, 71, 73, 85, 45, 46, 44, 45, 85, 86, 44, 85, 43, 44, 86, 86, 85, 54, 42, 43, 86, 87, 42, 86, 55, 86, 54, 87, 86, 55, 87, 41, 42, 88, 41, 87, 40, 41, 88, 97, 11, 12, 98, 13, 14, 98, 14, 15, 13, 97, 12, 64, 13, 98, 64, 97, 13, 97, 75, 11, 65, 97, 64, 65, 75, 97, 100, 64, 98, 100, 98, 15, 16, 100, 15, 99, 65, 64, 99, 64, 100, 66, 65, 99, 17, 100, 16, 99, 100, 17, 18, 99, 17, 66, 99, 18, 48, 3, 4, 48, 4, 5, 49, 3, 48, 2, 3, 49, 48, 5, 6, 7, 48, 6, 7, 49, 48, 8, 49, 7, 50, 49, 8, 50, 8, 9, 50, 1, 2, 50, 2, 49, 51, 1, 50, 51, 50, 9, 0, 1, 51, 52, 0, 51, 75, 10, 11, 10, 52, 51, 75, 52, 10, 53, 0, 52, 47, 0, 53, 76, 52, 75, 53, 52, 76, 46, 53, 85, 53, 46, 47, 54, 85, 53, 77, 53, 76, 65, 76, 75, 66, 76, 65, 10, 51, 9, 55, 54, 77, 77, 76, 66, 56, 87, 55, 78, 55, 77, 56, 55, 78, 89, 40, 88, 39, 40, 89, 57, 88, 56, 57, 56, 78, 89, 88, 57, 66, 78, 77, 19, 67, 66, 67, 78, 66, 90, 39, 89, 90, 89, 57, 18, 19, 66, 38, 39, 90, 79, 58, 57, 91, 90, 57, 78, 79, 57, 79, 78, 67, 58, 91, 57, 68, 79, 67, 20, 67, 19, 68, 67, 20, 59, 58, 79, 37, 38, 90, 37, 90, 91, 92, 91, 58, 92, 58, 59, 37, 91, 92, 80, 79, 68, 59, 79, 80, 36, 37, 92, 69, 80, 68, 60, 92, 59, 60, 59, 80, 21, 68, 20, 69, 68, 21, 93, 92, 60, 36, 92, 93, 81, 60, 80, 81, 80, 69, 35, 36, 93, 22, 69, 21, 70, 81, 69, 70, 69, 22, 61, 93, 60, 61, 60, 81, 82, 61, 81, 82, 81, 70, 94, 35, 93, 94, 93, 61, 34, 35, 94, 23, 70, 22, 71, 82, 70, 71, 70, 23, 62, 94, 61, 62, 61, 82, 24, 71, 23, 83, 82, 71, 73, 71, 24, 54, 53, 77, 88, 87, 56], "vertices": [4, 3, 72.41, 24.62, 0.49366, 4, -6.39, 24.89, 0.1575, 6, 34.19, -59.74, 0.04884, 19, 65.6, 9.25, 0.3, 4, 3, 98.88, 26.3, 0.03615, 4, 20.12, 25.51, 0.66288, 6, 60.66, -58.06, 0.00097, 19, 90.83, 17.42, 0.3, 2, 4, 49.52, 25.16, 0.7, 19, 119.1, 25.5, 0.3, 2, 4, 74.66, 10.09, 0.7, 19, 147.5, 18.26, 0.3, 2, 4, 89.25, -7.26, 0.7, 19, 166.44, 5.81, 0.3, 2, 4, 88.9, -23.55, 0.7, 19, 170.77, -9.9, 0.3, 2, 4, 79.77, -39.85, 0.7, 19, 166.69, -28.14, 0.3, 4, 3, 139.9, -40.87, 1e-05, 4, 58.41, -43.25, 0.69965, 5, 115.8, 72.74, 0.00034, 19, 147.2, -37.51, 0.3, 4, 3, 119.79, -37.55, 0.01727, 4, 38.45, -39.13, 0.6764, 5, 95.69, 76.06, 0.00632, 19, 126.89, -39.26, 0.3, 4, 3, 100.9, -40.33, 0.14076, 4, 19.46, -41.14, 0.5238, 5, 76.8, 73.28, 0.03544, 19, 109.27, -46.63, 0.3, 4, 3, 81.79, -51.55, 0.35439, 4, -0.08, -51.59, 0.20794, 5, 57.69, 62.05, 0.13766, 19, 93.53, -62.23, 0.3, 5, 2, 232.03, -51.01, 0.00017, 3, 70.03, -70.52, 0.31755, 4, -12.6, -70.07, 0.05084, 5, 45.93, 43.08, 0.33144, 19, 86.84, -83.52, 0.3, 4, 3, 66.08, -101.09, 0.1106, 4, -17.77, -100.46, 0.00474, 5, 41.98, 12.51, 0.58466, 19, 90.57, -114.12, 0.3, 4, 3, 63.86, -132.07, 0.01092, 4, -21.24, -131.32, 1e-05, 5, 39.75, -18.47, 0.68907, 19, 96.08, -144.69, 0.3, 3, 3, 52.7, -150.84, 6e-05, 5, 28.59, -37.23, 0.69994, 19, 89.9, -165.63, 0.3, 3, 2, 213.89, -141.48, 0.00028, 5, 5.97, -40.09, 0.69972, 19, 68.69, -174, 0.3, 3, 2, 185.88, -143.28, 0.02335, 5, -21.61, -34.91, 0.67665, 19, 40.68, -175.8, 0.3, 3, 2, 156.72, -138.33, 0.11064, 5, -48.65, -22.9, 0.58936, 19, 11.52, -170.85, 0.3, 4, 2, 125.39, -125.6, 0.2885, 3, -51.75, -116.41, 0.0012, 5, -75.85, -2.81, 0.41029, 19, -19.81, -158.11, 0.3, 3, 2, 98.29, -121.14, 0.43565, 5, -101, 8.2, 0.26435, 19, -46.9, -153.66, 0.3, 3, 2, 70.16, -117.26, 0.54125, 5, -127.3, 18.92, 0.15875, 19, -75.04, -149.78, 0.3, 3, 2, 38.89, -113.91, 0.61561, 5, -156.77, 29.91, 0.08439, 19, -106.31, -146.43, 0.3, 3, 2, 6.36, -110.3, 0.6586, 5, -187.4, 41.45, 0.0414, 19, -138.84, -142.82, 0.3, 3, 2, -17.54, -99.94, 0.68111, 5, -207.99, 57.4, 0.01889, 19, -162.73, -132.46, 0.3, 2, 2, -47.24, -103.83, 0.888, 1, 60.63, -41.64, 0.112, 2, 2, -68.53, -133.2, 0.16, 1, 85.53, -68.02, 0.84, 1, 1, 111.26, -93.43, 1, 1, 1, 116.26, -108.37, 1, 1, 1, 47.26, -113.3, 1, 1, 1, -39.67, -117.42, 1, 1, 1, -108.94, -107.03, 1, 1, 1, -133.15, -83.36, 1, 1, 1, -145.04, -44.44, 1, 1, 1, -128.96, -36.88, 1, 2, 2, -62.24, 80.2, 0.224, 1, -122.97, -22.12, 0.776, 3, 2, -39.22, 75.53, 0.67485, 6, -199.7, 34.82, 0.02515, 19, -184.41, 43.01, 0.3, 3, 2, -11.14, 88.34, 0.62194, 6, -169.33, 40.28, 0.07806, 19, -156.34, 55.82, 0.3, 3, 2, 9.75, 102.57, 0.55877, 6, -145.57, 48.91, 0.14123, 19, -135.45, 70.05, 0.3, 3, 2, 30.64, 123.57, 0.49289, 6, -120.13, 64.09, 0.20711, 19, -114.55, 91.05, 0.3, 3, 2, 57.99, 130.8, 0.44277, 6, -91.85, 64.33, 0.25723, 19, -87.21, 98.28, 0.3, 3, 2, 90.27, 124.71, 0.3706, 6, -62.08, 50.44, 0.3294, 19, -54.93, 92.19, 0.3, 3, 2, 116.78, 110.62, 0.26163, 6, -39.87, 30.23, 0.43837, 19, -28.41, 78.1, 0.3, 4, 2, 133.42, 105.05, 0.15265, 3, 13.09, 105.07, 2e-05, 6, -25.13, 20.71, 0.54733, 19, -11.78, 72.53, 0.3, 3, 2, 158.4, 104.55, 0.01271, 6, -1.05, 14.05, 0.68729, 19, 13.2, 72.03, 0.3, 3, 3, 56.17, 83.99, 0.03443, 6, 17.95, -0.37, 0.66557, 19, 35.18, 62.76, 0.3, 3, 3, 63.75, 66.89, 0.13524, 6, 25.53, -17.47, 0.56476, 19, 46.76, 48.07, 0.3, 5, 2, 191.92, 62.83, 0.00184, 3, 59.32, 49.7, 0.31265, 4, -18.46, 50.48, 0.00021, 6, 21.1, -34.66, 0.3853, 19, 46.72, 30.31, 0.3, 5, 2, 199.39, 49.12, 0.00014, 3, 63.17, 34.57, 0.50738, 4, -15.22, 35.21, 0.0195, 6, 24.95, -49.79, 0.17298, 19, 54.19, 16.6, 0.3, 2, 4, 71.4, -6.68, 0.8, 18, 220.3, -3.11, 0.2, 2, 4, 47.59, -2.62, 0.8, 18, 196.33, -6.03, 0.2, 3, 4, 23.93, -2.7, 0.7995, 5, 79.71, 111.87, 0.0005, 18, 173.68, -12.88, 0.2, 4, 3, 77.96, -0.85, 0.52857, 4, -1.87, -0.78, 0.27086, 5, 53.85, 112.75, 0.00058, 18, 148.4, -18.42, 0.2, 4, 3, 57.59, 0.35, 0.79914, 5, 33.49, 113.95, 1e-05, 6, 19.38, -84.01, 0.00086, 18, 128.38, -22.3, 0.2, 4, 2, 179.73, 27.35, 0.01163, 3, 38.74, 18.34, 0.69529, 6, 0.52, -66.02, 0.09309, 18, 105.66, -9.53, 0.2, 4, 2, 155.29, 39.09, 0.16515, 3, 17.97, 35.76, 0.39576, 6, -20.25, -48.6, 0.23909, 18, 81.22, 2.21, 0.2, 4, 2, 129.44, 48.38, 0.38898, 3, -4.78, 51.15, 0.10993, 6, -43, -33.21, 0.30109, 18, 55.37, 11.5, 0.2, 4, 2, 102.98, 52.73, 0.53124, 3, -29.35, 61.91, 0.01476, 6, -67.57, -22.45, 0.254, 18, 28.91, 15.85, 0.2, 3, 2, 74.05, 49.88, 0.6386, 6, -96.3, -18.05, 0.1614, 18, -0.02, 13, 0.2, 3, 2, 51.34, 40.82, 0.71202, 6, -120.54, -21.21, 0.08798, 18, -22.73, 3.94, 0.2, 3, 2, 31.06, 26.73, 0.76713, 6, -143.68, -29.85, 0.03287, 18, -43.01, -10.15, 0.2, 2, 2, 3.92, 20.68, 0.98344, 6, -171.47, -29, 0.01656, 3, 2, -29.33, 19.14, 0.79401, 6, -204.07, -22.26, 0.00599, 18, -103.4, -17.74, 0.2, 3, 2, -78.02, 21.12, 0.128, 1, -67.86, -48.63, 0.672, 18, -152.09, -15.76, 0.2, 1, 1, -76.24, -83.44, 1, 4, 3, 28.75, -113.53, 0.00638, 4, -55.57, -111.38, 5e-05, 5, 4.65, 0.07, 0.82357, 18, 128.6, -139.77, 0.17, 5, 2, 178.39, -73.34, 0.11291, 3, 12.53, -78.89, 0.18001, 4, -70.39, -76.12, 0.00015, 5, -11.57, 34.71, 0.53693, 18, 104.32, -110.22, 0.17, 4, 2, 137.36, -60.78, 0.37559, 3, -24.11, -56.58, 0.08743, 5, -48.22, 57.03, 0.23898, 18, 63.29, -97.66, 0.298, 4, 2, 92.06, -54.99, 0.61228, 3, -66.58, -39.76, 0.00234, 5, -90.68, 73.84, 0.11137, 18, 17.99, -91.87, 0.274, 3, 2, 60.35, -54.71, 0.62673, 5, -121.34, 81.96, 0.05927, 18, -13.72, -91.59, 0.314, 3, 2, 26.55, -58.01, 0.79177, 5, -154.9, 87.12, 0.03823, 18, -47.52, -94.89, 0.17, 3, 2, -9.15, -55.75, 0.81604, 5, -188.94, 98.14, 0.01396, 18, -83.23, -92.63, 0.17, 3, 2, -54.22, -52.5, 0.71821, 5, -231.8, 112.44, 0.00179, 1, 8.9, -38.94, 0.28, 1, 1, -0.06, -84.11, 1, 1, 1, 53.32, -78.61, 1, 1, 1, 80.64, -96.41, 1, 5, 2, 200.67, -34.35, 0.01649, 3, 43.76, -46.62, 0.49454, 4, -37.88, -45.14, 0.0223, 5, 19.66, 66.98, 0.21667, 18, 126.6, -71.23, 0.25, 3, 2, 170.04, -8.22, 0.0256, 3, 20.55, -13.73, 0.92959, 5, -3.56, 99.87, 0.04481, 2, 2, 138.89, 2.21, 0.99584, 6, -45.27, -80.28, 0.00416, 3, 2, 101.47, 3.98, 0.74487, 6, -81.09, -69.31, 0.00513, 18, 27.4, -32.9, 0.25, 3, 2, 61.63, -4.22, 0.74872, 5, -107.6, 130.56, 0.00128, 18, -12.44, -41.1, 0.25, 3, 2, 29.52, -16.6, 0.7454, 5, -141.78, 126.51, 0.0046, 18, -44.55, -53.48, 0.25, 3, 2, -3.45, -19.87, 0.74755, 5, -174.54, 131.5, 0.00245, 18, -77.53, -56.75, 0.25, 4, 2, -36.72, -14.67, 0.74958, 5, -205.49, 144.77, 0.00041, 6, -219.6, -53.19, 1e-05, 18, -110.79, -51.55, 0.25, 2, 2, -78.52, -12.5, 0.192, 1, -34.92, -55.37, 0.808, 1, 1, -37.76, -88.33, 1, 4, 2, 173.62, 70.13, 0.01507, 3, 43.4, 61.3, 0.18573, 6, 5.18, -23.06, 0.5492, 18, 99.55, 33.25, 0.25, 4, 2, 150.42, 79.14, 0.08033, 3, 23.15, 75.76, 0.05083, 6, -15.07, -8.6, 0.61884, 18, 76.35, 42.25, 0.25, 4, 2, 125.73, 79.42, 0.24373, 3, -0.7, 82.15, 0.02008, 6, -38.92, -2.21, 0.48619, 18, 51.66, 42.54, 0.25, 4, 2, 99.71, 93.36, 0.27733, 3, -22.46, 102.09, 0.00027, 6, -60.68, 17.73, 0.2964, 18, 25.64, 56.48, 0.426, 3, 2, 73.45, 105.35, 0.35635, 6, -83.16, 35.84, 0.24165, 18, -0.62, 68.47, 0.402, 3, 2, 48.75, 99.95, 0.38156, 6, -108.43, 36.72, 0.16844, 18, -25.32, 63.07, 0.45, 3, 2, 32.4, 81.62, 0.59195, 6, -128.81, 23.01, 0.15805, 18, -41.67, 44.74, 0.25, 3, 2, 11.15, 64.64, 0.66417, 6, -153.6, 11.81, 0.08583, 18, -62.92, 27.76, 0.25, 3, 2, -21.72, 52.93, 0.71843, 6, -188.34, 8.59, 0.03157, 18, -95.79, 16.05, 0.25, 2, 2, -56.11, 55.57, 0.98704, 6, -221.01, 19.66, 0.01296, 2, 2, -89.42, 56.98, 0.112, 1, -105.22, -53.15, 0.888, 1, 1, -116.2, -79.25, 1, 4, 2, 223.07, -78.63, 0.00139, 3, 54.51, -95.07, 0.19042, 4, -29.09, -93.98, 0.00838, 5, 30.41, 18.53, 0.79982, 2, 3, 41.23, -135.76, 0.00163, 5, 17.13, -22.16, 0.99837, 3, 2, 164.57, -107.87, 0.16351, 3, -9.4, -108.93, 0.02131, 5, -33.51, 4.67, 0.81518, 2, 2, 192.5, -126.72, 0.01789, 5, -11.1, -20.5, 0.98211], "hull": 48, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 0, 94, 10, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 146, 148, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192], "width": 313, "height": 454}}, "bu_gebo_you": {"bu_gebo_you": {"type": "mesh", "uvs": [0.03692, 0.00255, 0.15181, 0.05645, 0.31479, 0.07504, 0.45373, 0.09362, 0.59534, 0.08247, 0.7316, 0.11593, 0.83848, 0.19028, 0.89993, 0.31109, 0.94001, 0.43748, 0.95337, 0.52298, 0.99345, 0.65309, 1, 0.77205, 0.88657, 0.7516, 0.78237, 0.78692, 0.70756, 0.87056, 0.60068, 0.94305, 0.53121, 0.97465, 0.50182, 0.87985, 0.47511, 0.77577, 0.40296, 0.87613, 0.34686, 0.98766, 0.28273, 1, 0.22128, 0.90773, 0.21059, 0.80922, 0.15982, 0.69584, 0.10104, 0.60662, 0.03425, 0.49882, 0, 0.40589, 0, 0.26648, 0, 0.13823, 0, 0.00069, 0.47778, 0.71629, 0.46442, 0.6122, 0.42167, 0.51741, 0.34953, 0.33526, 0.6274, 0.3018, 0.72626, 0.55458, 0.19723, 0.20701], "triangles": [25, 26, 33, 21, 22, 20, 22, 23, 19, 24, 25, 32, 31, 23, 24, 28, 29, 37, 29, 30, 0, 34, 27, 28, 26, 27, 34, 20, 22, 19, 16, 17, 15, 15, 17, 14, 14, 17, 18, 19, 23, 18, 14, 18, 13, 18, 23, 31, 36, 12, 13, 13, 18, 31, 12, 10, 11, 13, 31, 36, 10, 36, 9, 10, 12, 36, 24, 32, 31, 31, 32, 36, 25, 33, 32, 32, 33, 36, 33, 35, 36, 36, 8, 9, 8, 35, 7, 8, 36, 35, 26, 34, 33, 33, 34, 35, 34, 28, 37, 34, 37, 3, 34, 3, 35, 3, 37, 2, 35, 6, 7, 3, 4, 35, 35, 5, 6, 35, 4, 5, 29, 1, 37, 37, 1, 2, 29, 0, 1], "vertices": [1, 7, -83.81, -49.57, 1, 1, 7, -70.65, -37.67, 1, 1, 7, -62.33, -18.23, 1, 1, 7, -54.75, -1.79, 1, 1, 7, -52.4, 16.3, 1, 1, 7, -42.24, 31.76, 1, 1, 7, -25.68, 41.76, 1, 1, 7, -2.22, 44.07, 1, 1, 7, 21.59, 43.48, 1, 1, 7, 37.27, 41.37, 1, 1, 7, 61.75, 40.61, 1, 1, 7, 83.2, 36.18, 1, 1, 7, 76.06, 22.99, 1, 1, 7, 79.18, 8.48, 1, 1, 7, 91.82, -4.5, 1, 1, 7, 101.49, -20.98, 1, 1, 7, 105.01, -31.01, 1, 1, 7, 87.17, -30.48, 1, 1, 7, 67.76, -29.21, 1, 1, 7, 83.48, -42.6, 1, 2, 7, 101.68, -54.49, 0.616, 2, 70.21, -115.42, 0.384, 1, 2, 65.91, -108.06, 1, 1, 2, 80.32, -96.12, 1, 1, 2, 97.49, -90.16, 1, 1, 2, 116, -78.55, 1, 1, 2, 129.27, -65.7, 1, 1, 2, 144.76, -51.37, 1, 1, 3, 3.97, -47.59, 1, 1, 3, 29.74, -44.96, 1, 1, 3, 54.07, -42.66, 1, 1, 3, 78.52, -46.96, 1, 1, 7, 57.22, -26.25, 1, 1, 7, 38.21, -23.33, 1, 1, 7, 19.97, -24.46, 1, 2, 7, -14.78, -25.39, 0.384, 5, -6.76, 21.38, 0.616, 2, 7, -12.24, 10.62, 0.464, 5, -0.3, -14.13, 0.536, 1, 7, 35.95, 11.75, 1, 2, 7, -41.92, -43.68, 0.64, 5, 14.99, 45.83, 0.36], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 62, 64, 64, 66], "width": 128, "height": 184}}, "toufa_hou_zuo": {"toufa_hou_zuo": {"type": "mesh", "uvs": [0.85876, 0.40831, 0.80463, 0.48705, 0.78058, 0.55506, 0.79411, 0.60517, 0.84072, 0.67795, 0.89786, 0.74238, 0.95951, 0.79249, 1, 0.81277, 0.9971, 0.88794, 0.93846, 0.91419, 0.85576, 0.90584, 0.7911, 0.90106, 0.73998, 0.93686, 0.69186, 0.99413, 0.64676, 1, 0.57759, 0.99532, 0.54902, 0.95953, 0.52647, 0.90822, 0.47985, 0.85215, 0.41821, 0.80204, 0.37911, 0.78653, 0.34904, 0.79607, 0.31446, 0.84022, 0.26634, 0.79965, 0.20169, 0.82828, 0.12651, 0.82828, 0.04681, 0.79488, 0, 0.75789, 0, 0.63142, 0, 0.48586, 0, 0.35342, 0, 0.20906, 0, 0.09452, 0, 0, 0.07489, 0.06647, 0.1964, 0.13805, 0.24426, 0.21401, 0.26819, 0.30312, 0.26635, 0.401, 0.3387, 0.44254, 0.40627, 0.48134, 0.49279, 0.53247, 0.58852, 0.54123, 0.67689, 0.51932, 0.74132, 0.47257, 0.78366, 0.42291, 0.82416, 0.39953, 0.09253, 0.2107, 0.09514, 0.33713, 0.1291, 0.48429, 0.29888, 0.58585, 0.46344, 0.62938, 0.61755, 0.65011, 0.72987, 0.67912, 0.8239, 0.79934, 0.67763, 0.80763, 0.51829, 0.75581, 0.33023, 0.72058, 0.15783, 0.70399, 0.08469, 0.59622], "triangles": [12, 13, 14, 11, 55, 54, 2, 44, 1, 3, 43, 2, 9, 10, 6, 8, 6, 7, 8, 9, 6, 6, 10, 54, 6, 54, 5, 54, 4, 5, 53, 3, 4, 0, 1, 45, 11, 54, 10, 12, 55, 11, 18, 56, 55, 17, 18, 55, 17, 55, 12, 16, 17, 12, 12, 14, 16, 14, 15, 16, 53, 52, 3, 55, 52, 53, 55, 53, 54, 54, 53, 4, 56, 51, 52, 19, 20, 56, 56, 52, 55, 18, 19, 56, 57, 50, 51, 58, 50, 57, 57, 51, 56, 20, 57, 56, 21, 57, 20, 23, 58, 57, 23, 57, 21, 22, 23, 21, 28, 29, 59, 58, 27, 28, 58, 28, 59, 26, 27, 58, 25, 26, 58, 58, 24, 25, 23, 24, 58, 59, 49, 50, 58, 59, 50, 32, 33, 34, 47, 31, 32, 34, 47, 32, 35, 47, 34, 47, 35, 36, 48, 47, 36, 48, 36, 37, 48, 30, 31, 48, 31, 47, 38, 48, 37, 49, 48, 38, 30, 48, 49, 29, 30, 49, 39, 49, 38, 59, 29, 49, 50, 39, 40, 50, 49, 39, 41, 50, 40, 51, 50, 41, 51, 41, 42, 0, 45, 46, 44, 45, 1, 43, 44, 2, 52, 42, 43, 3, 52, 43, 52, 51, 42], "vertices": [1, 4, 229.15, 62.11, 1, 1, 4, 188.69, 85.87, 1, 1, 4, 153.46, 96.98, 1, 4, 47, -23.5, 15.55, 0.03662, 51, -42.03, -24.05, 0.01446, 56, -60.76, -7.29, 0.00492, 4, 127.09, 92.19, 0.944, 4, 47, -38.27, 55.62, 0.00129, 51, -43.28, 18.63, 0.00408, 56, -25.76, 17.17, 0.01863, 4, 88.43, 74.07, 0.976, 3, 56, 4.3, 45.35, 0.04336, 57, -56.88, 16.13, 0.00464, 4, 54, 51.44, 0.952, 3, 56, 17.43, 77.58, 0.01981, 57, -64.17, 50.16, 0.01219, 4, 35.56, 21.93, 0.968, 3, 56, 34.84, 92.46, 0.01756, 57, -58.15, 72.26, 0.01445, 4, 15.86, 10.24, 0.968, 3, 56, 73.94, 96.75, 0.04355, 57, -28.26, 97.81, 0.05245, 4, -23.4, 12.69, 0.904, 1, 4, -36.35, 37.45, 1, 1, 4, -55.84, 65.46, 1, 1, 4, -56.55, 87.55, 1, 1, 4, -68.57, 107.42, 1, 1, 4, -74.89, 141.06, 1, 2, 57, 112.91, 29.66, 0.00344, 58, 19.99, 72.52, 0.99656, 1, 58, 48.8, 71.9, 1, 1, 58, 61.82, 53.97, 1, 1, 58, 72.86, 27.79, 1, 4, 55, 56.2, 190.95, 0.00025, 53, -46.72, 62.89, 0.03149, 52, 32.52, 72.18, 0.22487, 58, 94.03, -0.25, 0.74339, 4, 55, 41.12, 157.58, 0.01647, 53, -12.18, 50.7, 0.31974, 52, 63.18, 52.14, 0.43824, 58, 121.23, -24.79, 0.22555, 4, 55, 39.48, 139.51, 0.06486, 53, 5.96, 50.6, 0.64438, 52, 80.78, 47.74, 0.21716, 58, 137.93, -31.85, 0.07359, 5, 54, -10.98, 123.87, 0.00046, 55, 48.68, 129.71, 0.13634, 53, 14.94, 60.6, 0.75962, 52, 91.88, 55.32, 0.07882, 58, 150.07, -26.08, 0.02476, 4, 55, 75.41, 124.76, 0.19415, 53, 17.6, 87.66, 0.77788, 52, 100.88, 80.98, 0.02158, 58, 162.93, -2.12, 0.00639, 5, 54, 21.65, 113.06, 0.01508, 55, 62.93, 98.43, 0.30141, 53, 44.89, 77.46, 0.67407, 52, 124.97, 64.6, 0.00717, 58, 184.2, -22.03, 0.00227, 3, 54, 52.1, 117.19, 0.01234, 55, 86.65, 78.9, 0.55668, 53, 62.33, 102.77, 0.43098, 3, 54, 81.14, 105.78, 0.00254, 55, 98.02, 49.85, 0.73551, 53, 90.31, 116.57, 0.26195, 2, 55, 109.89, -12.4, 0.92738, 53, 151.32, 133.7, 0.07262, 2, 55, 82.89, -69.72, 0.99674, 53, 210.73, 111.68, 0.00326, 3, 49, 37.04, 160.76, 0.03274, 54, 134.59, -29.14, 0.08799, 55, 35.39, -81.06, 0.87928, 3, 49, 110.6, 128.56, 0.59889, 54, 121.27, -108.33, 0.36656, 55, -31.75, -125.11, 0.03455, 3, 49, 149.86, 85.45, 0.49286, 50, 37.04, 92.52, 0.47247, 54, 89.01, -156.9, 0.03467, 1, 50, 112.97, 64.8, 1, 1, 50, 171.66, 49.52, 1, 1, 50, 208.2, -10.26, 1, 1, 50, 133.88, -55.57, 1, 1, 50, 76.59, -81.3, 1, 2, 49, 126.19, -84.74, 0.00305, 50, 32.22, -79.23, 0.99695, 2, 49, 82.49, -65.73, 0.21088, 50, -13.3, -65.15, 0.78912, 3, 48, 138.74, -51.16, 0.06056, 49, 41.28, -35.36, 0.83473, 50, -57.6, -39.48, 0.10471, 3, 48, 102.37, -44.02, 0.61533, 49, 6.15, -47.16, 0.38385, 50, -91.22, -55.08, 0.00082, 2, 48, 68.41, -37.34, 0.96546, 49, -26.66, -58.18, 0.03454, 2, 47, 96.25, -36.82, 0.17068, 48, 24.6, -28.11, 0.82932, 2, 47, 57.33, -27.63, 0.95129, 48, -13.39, -40.6, 0.04871, 1, 47, 19.57, -34.72, 1, 1, 47, -9.85, -55.88, 1, 1, 47, -34.94, -76.89, 1, 1, 47, -54.92, -86.57, 1, 1, 50, 64.48, -25.13, 1, 3, 49, 109.76, 3.05, 0.16249, 50, 6.24, 6.21, 0.83463, 54, -0.67, -138.06, 0.00288, 3, 49, 38.94, 36.31, 0.70155, 54, 14.35, -61.28, 0.29778, 55, -69.91, -14.71, 0.00068, 4, 48, 85.96, 30.96, 0.47975, 54, -31.81, 13.92, 0.29632, 55, -46.12, 70.26, 0.00017, 53, 82.25, -28.79, 0.22376, 4, 47, 114.27, 12.09, 0.01909, 48, 14.42, 23.01, 0.63578, 53, 10.94, -38.59, 0.21564, 52, 64.44, -40.08, 0.12949, 3, 47, 52.01, 30.33, 0.50543, 52, -0.34, -43.37, 0.08838, 51, 34.27, -34.06, 0.40619, 3, 47, 7.49, 50.85, 0.01011, 51, -1.41, -0.45, 0.98363, 56, -18.74, -28.3, 0.00626, 2, 56, 38.07, 19.11, 0.7358, 57, -14.19, 13.46, 0.2642, 5, 52, -42.55, 31.64, 0.01377, 51, 46.88, 51.08, 0.22754, 56, 50.83, -40.39, 0.05789, 57, 29.85, -28.54, 0.3497, 58, 13.6, -28.7, 0.35109, 4, 55, 3.47, 187.45, 4e-05, 53, -38.73, 10.64, 0.00437, 52, 27.88, 19.53, 0.83873, 58, 81.31, -51.55, 0.15686, 5, 54, -18.15, 84.26, 0.01578, 55, 14.75, 108.05, 0.08496, 53, 39.41, 28.65, 0.88399, 52, 108.06, 18.47, 0.01016, 58, 160.37, -64.98, 0.00511, 3, 54, 29.23, 61.72, 0.16962, 55, 30.41, 57.98, 0.60014, 53, 87.97, 48.52, 0.23023, 3, 49, 2.02, 85.33, 0.07126, 54, 52.91, -13.53, 0.6463, 55, -8.69, -10.54, 0.28243], "hull": 47, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 0, 92, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118], "width": 415, "height": 523}}, "yu1_zuoshang": {"yu1_zuoshang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [20.33, -34.35, -56.67, -34.35, -56.67, 55.65, 20.33, 55.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 90}}, "shui2": {"shui": {"type": "mesh", "uvs": [0.02253, 0.10204, 0.15431, 0.05619, 0.25606, 0, 0.43621, 0, 0.61135, 0.07256, 0.76314, 0.03981, 0.89492, 0.1741, 0.95664, 0.39027, 1, 0.63592, 1, 0.81933, 0.9116, 0.87501, 0.86156, 1, 0.72144, 1, 0.65305, 0.86191, 0.51627, 0.69815, 0.39451, 0.64902, 0.23604, 0.55731, 0.12595, 0.45905, 0.02921, 0.38044, 0, 0.34441, 0, 0.21668, 0.11594, 0.23633, 0.25606, 0.24943, 0.39784, 0.29528, 0.55297, 0.36079, 0.66807, 0.45905, 0.79484, 0.63264, 0.85655, 0.76693, 0.89492, 0.59989, 0.81819, 0.43285, 0.73479, 0.28873, 0.63637, 0.2134, 0.51294, 0.14462, 0.39784, 0.10204, 0.28108, 0.11187, 0.14263, 0.13807, 0.06424, 0.13807, 0.06924, 0.32476, 0.20268, 0.40337, 0.37115, 0.4787, 0.49626, 0.53438, 0.58633, 0.59334, 0.68141, 0.72762, 0.7865, 0.87829], "triangles": [33, 2, 3, 34, 2, 33, 36, 0, 1, 35, 36, 1, 34, 35, 1, 34, 1, 2, 32, 3, 4, 33, 3, 32, 31, 4, 5, 32, 4, 31, 20, 0, 36, 21, 36, 35, 20, 36, 21, 22, 35, 34, 21, 35, 22, 30, 31, 5, 30, 5, 6, 23, 34, 33, 23, 33, 32, 22, 34, 23, 37, 20, 21, 19, 20, 37, 24, 32, 31, 23, 32, 24, 18, 19, 37, 38, 21, 22, 37, 21, 38, 29, 30, 6, 29, 6, 7, 17, 37, 38, 18, 37, 17, 25, 31, 30, 25, 30, 29, 24, 31, 25, 39, 22, 23, 38, 22, 39, 40, 23, 24, 39, 23, 40, 16, 38, 39, 17, 38, 16, 41, 24, 25, 40, 24, 41, 28, 29, 7, 26, 25, 29, 26, 29, 28, 28, 7, 8, 15, 39, 40, 16, 39, 15, 14, 40, 41, 15, 40, 14, 42, 25, 26, 41, 25, 42, 27, 26, 28, 28, 8, 9, 10, 27, 28, 13, 41, 42, 14, 41, 13, 9, 10, 28, 43, 42, 26, 43, 26, 27, 13, 42, 43, 12, 13, 43, 11, 27, 10, 43, 27, 11, 12, 43, 11], "vertices": [340.43, 9.17, 283.03, -21.12, 240.27, -49.39, 157.06, -76.05, 70.7, -84.91, 3.06, -115.08, -67.92, -102.99, -112.72, -61.28, -151.27, -9.92, -165.09, 33.23, -128.46, 59.41, -114.77, 96.21, -50.05, 116.95, -6.97, 125.98, 63.83, 114.95, 122.97, 113.99, 212.51, 143.57, 289.65, 132.46, 357.43, 141.75, 396.66, 117.91, 342.2, 39.47, 287.17, 26.94, 221.47, 9.28, 152.52, -0.92, 75.94, -8.47, 15.37, -2.39, -56.26, 19.68, -94.89, 42.13, -100.02, -2.84, -51.99, -30.77, -2.61, -52.33, 48.53, -55.48, 110.72, -53.39, 167.09, -46.37, 220.28, -26.78, 282.25, -0.13, 318.46, 11.48, 327.67, 91.51, 265.55, 63.34, 182.07, 56.12, 89.05, 40.76, 43, 41.29, -11.03, 58.81, -70.93, 78.69], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 485, "height": 247}}}}], "animations": {"animation1": {"slots": {"shui": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "paopao1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "color": "ffffffff", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "shui3": {"color": [{"color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff", "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1, "color": "ffffffcf", "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff", "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.6667, "color": "ffffffcf", "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "color": "ffffff7f"}]}, "shui2": {"color": [{"color": "ffffffcf", "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "color": "ffffffff", "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "color": "ffffffcf", "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5, "color": "ffffffff", "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "color": "ffffffcf"}]}, "yu3_1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.9, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.2, "color": "ffffff00"}]}, "yu3_1_2": {"color": [{"color": "ffffff00", "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "color": "ffffff00"}]}, "paopao3": {"color": [{"color": "ffffff60", "curve": 0.315, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 1.3, "color": "ffffffff", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9667, "color": "ffffff00", "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 5.3333, "color": "ffffff60"}]}, "paopao2": {"color": [{"color": "ffffffff", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "color": "ffffffff"}]}}, "bones": {"bone2": {"rotate": [{"angle": -0.95, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -1.09, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.95, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -1.09, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.95}]}, "bone3": {"rotate": [{"angle": -0.39, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "angle": -0.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.16, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.39, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.8333, "angle": -0.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -1.16, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -0.39}], "translate": [{"x": 1.06, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "x": 0.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 3.17, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.06, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.8333, "x": 0.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 3.17, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 1.06}]}, "bone4": {"rotate": [{"angle": -0.77, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "angle": -0.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -1.16, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.77, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 2.8333, "angle": -0.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": -1.16, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.77}], "translate": [{"x": 0.71, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "x": 0.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 0.71, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 2.8333, "x": 0.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 1.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 0.71}]}, "bone5": {"rotate": [{"angle": -0.58, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.58, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "angle": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.58}], "translate": [{"x": 0.34, "y": -1.56, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "x": 0.23, "y": -1.05, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.68, "y": -3.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.34, "y": -1.56, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "x": 0.23, "y": -1.05, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 0.68, "y": -3.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.34, "y": -1.56}]}, "bone6": {"rotate": [{"angle": -0.58, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.58, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "angle": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.58}], "translate": [{"x": 0.68, "y": 2.13, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "x": 0.46, "y": 1.43, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.37, "y": 4.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.68, "y": 2.13, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "x": 0.46, "y": 1.43, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.37, "y": 4.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.68, "y": 2.13}]}, "bone7": {"rotate": [{"angle": 2.4, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "angle": 1.95, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.4, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2.8333, "angle": 1.95, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 2.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 2.4}]}, "bone8": {"rotate": [{"angle": 2.93, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.1667, "angle": 2.74, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 2.93, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.8333, "angle": 2.74, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.93}]}, "bone9": {"rotate": [{"angle": -2.26, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "angle": -1.83, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -2.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.26, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2.8333, "angle": -1.83, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -2.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -2.26}]}, "bone10": {"rotate": [{"angle": -2.76, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.1667, "angle": -2.59, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.76, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.8333, "angle": -2.59, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.76}]}, "bone11": {"rotate": [{"angle": -6.16, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -7.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -7.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -6.16, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -7.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -7.53, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -6.16}]}, "bone12": {"rotate": [{"angle": -5.18, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "angle": -6.34, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.3333, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -5.18, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.8333, "angle": -6.34, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.1667, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -5.18}]}, "bone13": {"rotate": [{"angle": -0.31, "curve": 0.31, "c2": 0.22, "c3": 0.646, "c4": 0.57}, {"time": 0.1667, "angle": -1.22, "curve": 0.316, "c2": 0.27, "c3": 0.715, "c4": 0.83}, {"time": 1.0333, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.2, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.6667, "angle": -0.31, "curve": 0.31, "c2": 0.22, "c3": 0.646, "c4": 0.57}, {"time": 2.8333, "angle": -1.22, "curve": 0.316, "c2": 0.27, "c3": 0.715, "c4": 0.83}, {"time": 3.7, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.8667, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.3333, "angle": -0.31}]}, "bone14": {"rotate": [{"angle": -1.43, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "angle": -2.64, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.8333, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.43, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2.8333, "angle": -2.64, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 3.5, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6667, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -1.43}], "translate": [{"x": 2.36, "y": -2.24, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "x": 4.37, "y": -4.14, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.8333, "x": 12.03, "y": -11.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "x": 12.83, "y": -12.16, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 2.36, "y": -2.24, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2.8333, "x": 4.37, "y": -4.14, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 3.5, "x": 12.03, "y": -11.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6667, "x": 12.83, "y": -12.16, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 2.36, "y": -2.24}]}, "bone15": {"translate": [{"x": -5.61, "y": 4.54, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "x": -6.43, "y": 5.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": -6.86, "y": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -5.61, "y": 4.54, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "x": -6.43, "y": 5.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": -6.86, "y": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": -5.61, "y": 4.54}]}, "bone18": {"translate": [{"x": -11.07, "y": 15.96, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": -13.77, "y": 13.97, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.6667, "x": 0.9, "y": 24.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -11.07, "y": 15.96, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": -13.77, "y": 13.97, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4.3333, "x": 0.9, "y": 24.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": -11.07, "y": 15.96}]}, "bone20": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": -30.87, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": -30.87, "curve": "stepped"}, {"time": 5.3333}]}, "bone21": {"rotate": [{"angle": -1.48, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -1.7, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -1.81, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.48, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -1.7, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -1.81, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.48}]}, "bone22": {"rotate": [{"angle": -0.74, "curve": 0.338, "c2": 0.35, "c3": 0.688, "c4": 0.73}, {"time": 0.4333, "angle": -1.48, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.6, "angle": -1.7, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.7667, "angle": -1.81, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "angle": -0.74, "curve": 0.338, "c2": 0.35, "c3": 0.688, "c4": 0.73}, {"time": 3.1, "angle": -1.48, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.2667, "angle": -1.7, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.4333, "angle": -1.81, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "angle": -0.74}]}, "bone23": {"rotate": [{"angle": -0.1, "curve": 0.269, "c2": 0.11, "c3": 0.675, "c4": 0.69}, {"time": 0.9, "angle": -2.94, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.0667, "angle": -3.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.2333, "angle": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "angle": -0.1, "curve": 0.269, "c2": 0.11, "c3": 0.675, "c4": 0.69}, {"time": 3.5667, "angle": -2.94, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.7333, "angle": -3.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.9, "angle": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 5.3333, "angle": -0.1}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone26": {"rotate": [{"angle": 7.75, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 8.88, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 7.75, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 8.88, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 7.75}]}, "bone27": {"rotate": [{"angle": 4.78, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": 6.34, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": 8.88, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 4.78, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "angle": 6.34, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 3.1667, "angle": 8.88, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 4.78}]}, "bone28": {"rotate": [{"angle": -6.77, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -7.76, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -8.28, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -6.77, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -7.76, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -8.28, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -6.77}]}, "bone29": {"rotate": [{"angle": -4.18, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": -5.54, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": -7.76, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": -8.28, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -4.18, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "angle": -5.54, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 3.1667, "angle": -7.76, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": -8.28, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -4.18}]}, "bone30": {"rotate": [{"angle": 1.27, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": 2.06, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5, "angle": 2.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.27, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": 2.06, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1667, "angle": 2.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 1.27}]}, "bone31": {"rotate": [{"angle": 0.46, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 1.27, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.5, "angle": 1.68, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.8333, "angle": 2.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.46, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 3, "angle": 1.27, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 3.1667, "angle": 1.68, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 3.5, "angle": 2.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6667, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 0.46}]}, "bone32": {"rotate": [{"angle": 2.28, "curve": 0.342, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.1333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.3, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.4667, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 2.28, "curve": 0.342, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 2.8, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.9667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.1333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "angle": 2.28}]}, "bone33": {"rotate": [{"angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.67}]}, "bone34": {"rotate": [{"angle": 1.76, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 0.3, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.4667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": 1.76, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 2.9667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "angle": 1.76}]}, "bone35": {"rotate": [{"angle": 0.8, "curve": 0.323, "c2": 0.3, "c3": 0.687, "c4": 0.73}, {"time": 0.6, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.7667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.9333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 0.8, "curve": 0.323, "c2": 0.3, "c3": 0.687, "c4": 0.73}, {"time": 3.2667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.4333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "angle": 0.8}]}, "bone36": {"rotate": [{"angle": 1.65, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.65, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 1.65}]}, "bone37": {"rotate": [{"angle": 0.7, "curve": 0.319, "c2": 0.29, "c3": 0.687, "c4": 0.73}, {"time": 0.6333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.9667, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "angle": 0.7, "curve": 0.319, "c2": 0.29, "c3": 0.687, "c4": 0.73}, {"time": 3.3, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.4667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "angle": 0.7}]}, "bone38": {"rotate": [{"angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.67}]}, "bone39": {"rotate": [{"angle": 1.11, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.6667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.8333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 1.11, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.1667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.3333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 1.11}]}, "bone40": {"rotate": [{"angle": 0.43, "curve": 0.304, "c2": 0.24, "c3": 0.684, "c4": 0.72}, {"time": 0.7333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.9, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0667, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.43, "curve": 0.304, "c2": 0.24, "c3": 0.684, "c4": 0.72}, {"time": 3.4, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": 0.43}]}, "bone41": {"rotate": [{"angle": 0.52, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 0.7, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "angle": 0.52, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 3.3667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "angle": 0.52}], "translate": [{"x": 2.02, "y": -0.21, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 0.7, "x": 10.4, "y": -1.08, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8667, "x": 11.92, "y": -1.24, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0333, "x": 12.72, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "x": 2.02, "y": -0.21, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 3.3667, "x": 10.4, "y": -1.08, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5333, "x": 11.92, "y": -1.24, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7, "x": 12.72, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "x": 2.02, "y": -0.21}]}, "bone42": {"rotate": [{"angle": 0.03, "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 0.0333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.0333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.2, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.3667, "angle": 3.26, "curve": 0.249, "c3": 0.739, "c4": 0.95}, {"time": 2.6667, "angle": 0.03, "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 2.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.7, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.8667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 4.0333, "angle": 3.26, "curve": 0.249, "c3": 0.739, "c4": 0.95}, {"time": 5.3333, "angle": 0.03}]}, "bone43": {"rotate": [{"angle": 1.43, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 0.4, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.7333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "angle": 1.43, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 3.0667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.2333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.4, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5.3333, "angle": 1.43}], "translate": [{"x": 4.34, "y": 3.83, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 0.4, "x": 8.09, "y": 7.15, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5667, "x": 9.27, "y": 8.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.7333, "x": 9.89, "y": 8.74, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "x": 4.34, "y": 3.83, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 3.0667, "x": 8.09, "y": 7.15, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.2333, "x": 9.27, "y": 8.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.4, "x": 9.89, "y": 8.74, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5.3333, "x": 4.34, "y": 3.83}]}, "bone44": {"rotate": [{"angle": 0.52, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 0.7, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "angle": 0.52, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 3.3667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "angle": 0.52}]}, "bone45": {"rotate": [{"angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.67}], "translate": [{"x": 7.84, "y": -8.07, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "x": 8.98, "y": -9.25, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 9.59, "y": -9.87, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 7.84, "y": -8.07, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "x": 8.98, "y": -9.25, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 9.59, "y": -9.87, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 7.84, "y": -8.07}]}, "bone46": {"rotate": [{"angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.67}]}, "bone47": {"rotate": [{"angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.51}]}, "bone48": {"rotate": [{"angle": -0.56, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.56, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.56}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.88, "y": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 3.88, "y": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone49": {"rotate": [{"angle": -0.13, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.8667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.0333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.2, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.6667, "angle": -0.13, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 3.5333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.7, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.8667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.3333, "angle": -0.13}]}, "bone50": {"rotate": [{"angle": -0.31, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.4, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.5667, "angle": -2.99, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": -0.31, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.9, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4.0667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 4.2333, "angle": -2.99, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 5.3333, "angle": -0.31}], "translate": [{"x": 9.59, "y": 0.68}]}, "bone51": {"rotate": [{"angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.44}]}, "bone52": {"rotate": [{"angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.44}]}, "bone53": {"rotate": [{"angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.44}]}, "bone54": {"rotate": [{"angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.51}]}, "bone55": {"rotate": [{"angle": -0.39, "curve": 0.304, "c2": 0.24, "c3": 0.684, "c4": 0.72}, {"time": 0.7333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.9, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": -0.39, "curve": 0.304, "c2": 0.24, "c3": 0.684, "c4": 0.72}, {"time": 3.4, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": -0.39}]}, "bone56": {"rotate": [{"angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.44}]}, "bone57": {"rotate": [{"angle": -0.73, "curve": 0.323, "c2": 0.3, "c3": 0.687, "c4": 0.73}, {"time": 0.6, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.7667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.9333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -0.73, "curve": 0.323, "c2": 0.3, "c3": 0.687, "c4": 0.73}, {"time": 3.2667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.4333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "angle": -0.73}]}, "bone58": {"rotate": [{"angle": -0.25, "curve": 0.292, "c2": 0.2, "c3": 0.681, "c4": 0.71}, {"time": 0.8, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.9667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.1333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": -0.25, "curve": 0.292, "c2": 0.2, "c3": 0.681, "c4": 0.71}, {"time": 3.4667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.6333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.8, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "angle": -0.25}]}, "bone60": {"rotate": [{"angle": 1.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.97, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 7.97, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 1.47}]}, "bone61": {"rotate": [{"angle": 3.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 3.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 7.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 3.99}]}, "bone62": {"rotate": [{"angle": 6.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 7.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 6.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 7.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 6.5}]}, "bone65": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 15.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.157, "y": 1.157, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone67": {"translate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.8667, "x": 175.71, "y": 25.6}]}, "bone69": {"translate": [{"curve": 0.25, "c3": 0.747, "c4": 0.99}, {"time": 5.3, "y": 109.87, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 1.162, "y": 1.162}]}, "bone70": {"translate": [{"x": -20.81, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.9667, "x": -30.87, "curve": "stepped"}, {"time": 1, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2.6667, "x": -20.81, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 3.6333, "x": -30.87, "curve": "stepped"}, {"time": 3.6667, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 5.3333, "x": -20.81}]}, "bone71": {"translate": [{"x": -5.81, "curve": 0.313, "c2": 0.27, "c3": 0.668, "c4": 0.67}, {"time": 1, "x": -20.81, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 1.9667, "x": -30.87, "curve": "stepped"}, {"time": 2, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 2.6667, "x": -5.81, "curve": 0.313, "c2": 0.27, "c3": 0.668, "c4": 0.67}, {"time": 3.6667, "x": -20.81, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.6333, "x": -30.87, "curve": "stepped"}, {"time": 4.6667, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 5.3333, "x": -5.81}]}, "bone72": {"rotate": [{"angle": 64.29, "curve": "stepped"}, {"time": 1.6333, "angle": 64.29, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -80.94}]}, "bone74": {"translate": [{"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "x": 167.54}]}, "bone73": {"rotate": [{"angle": -31.69, "curve": "stepped"}, {"time": 2.9, "angle": -31.69, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": 43.36}]}, "bone75": {"translate": [{"y": 55.05, "curve": 0.374, "c2": 0.49, "c3": 0.748, "c4": 0.99}, {"time": 2.6333, "y": 109.87, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "y": 55.05}], "scale": [{"x": 1.082, "y": 1.082, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.162, "y": 1.162, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.082, "y": 1.082}]}, "bone76": {"translate": [{"y": 20.93, "curve": 0.315, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 1.3, "y": 55.05, "curve": 0.374, "c2": 0.49, "c3": 0.748, "c4": 0.99}, {"time": 3.9333, "y": 109.87, "curve": "stepped"}, {"time": 3.9667, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 5.3333, "y": 20.93}], "scale": [{"x": 1.031, "y": 1.031, "curve": 0.315, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 1.3, "x": 1.082, "y": 1.082, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9333, "x": 1.162, "y": 1.162, "curve": "stepped"}, {"time": 3.9667, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 5.3333, "x": 1.031, "y": 1.031}]}, "bone121": {"translate": [{"x": 37.11, "y": 17.47, "curve": 0.292, "c2": 0.2, "c3": 0.681, "c4": 0.71}, {"time": 1.6, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": 40.47, "y": 19.05, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 37.11, "y": 17.47}]}, "bone120": {"translate": [{"x": 40.24, "y": 18.95, "curve": 0.254, "c2": 0.04, "c3": 0.645, "c4": 0.59}, {"time": 1.6, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": 40.47, "y": 19.05, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 5.3333, "x": 40.24, "y": 18.95}]}, "bone119": {"translate": [{"x": 31.3, "y": 14.74, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.7667, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.6, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 3.4333, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 5.3333, "x": 31.3, "y": 14.74}]}, "bone118": {"translate": [{"x": 38.75, "y": 18.24, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.2667, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5.3333, "x": 38.75, "y": 18.24}]}, "bone117": {"translate": [{"x": 31.73, "y": 14.94, "curve": 0.326, "c2": 0.31, "c3": 0.716, "c4": 0.83}, {"time": 1.6, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "x": 40.47, "y": 19.05, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 31.73, "y": 14.94}]}, "bone116": {"translate": [{"x": 32.94, "y": 15.51, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 1.3333, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 40.47, "y": 19.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 32.94, "y": 15.51}]}, "bone115": {"translate": [{"x": 38.01, "y": 17.9, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 1.3333, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 40.47, "y": 19.05, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 38.01, "y": 17.9}]}, "bone114": {"translate": [{"x": 35.76, "y": 16.83, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.5, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 3.1667, "curve": 0.243, "c3": 0.683, "c4": 0.73}, {"time": 5.3333, "x": 35.76, "y": 16.83}]}, "bone113": {"translate": [{"x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 40.47, "y": 19.05}]}, "bone112": {"translate": [{"x": 26.7, "y": 12.57, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 1.3333, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 40.47, "y": 19.05, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 26.7, "y": 12.57}]}, "bone111": {"translate": [{"x": 26.75, "y": 12.59, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 1, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 40.47, "y": 19.05, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 26.75, "y": 12.59}]}, "bone110": {"translate": [{"x": 33, "y": 15.54, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 1, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 40.47, "y": 19.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 33, "y": 15.54}]}, "bone109": {"translate": [{"x": 39.61, "y": 18.65, "curve": 0.351, "c2": 0.65, "c3": 0.686}, {"time": 0.1667, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.8333, "curve": 0.247, "c3": 0.724, "c4": 0.89}, {"time": 5.3333, "x": 39.61, "y": 18.65}]}, "bone108": {"translate": [{"x": 38.09, "y": 17.93, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 1, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 40.47, "y": 19.05, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 38.09, "y": 17.93}]}, "bone107": {"translate": [{"x": 20.03, "y": 9.43, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 1, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 20.03, "y": 9.43}]}, "bone106": {"translate": [{"x": 23.45, "y": 11.04, "curve": 0.339, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 0.8333, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 40.47, "y": 19.05, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "x": 23.45, "y": 11.04}]}, "bone105": {"translate": [{"x": 30.01, "y": 14.13, "curve": 0.322, "c2": 0.3, "c3": 0.67, "c4": 0.68}, {"time": 0.8333, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 30.01, "y": 14.13}]}, "bone104": {"translate": [{"x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.8333, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 40.47, "y": 19.05}]}, "bone103": {"translate": [{"x": 35.78, "y": 16.85, "curve": 0.303, "c2": 0.23, "c3": 0.652, "c4": 0.62}, {"time": 0.8333, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 40.47, "y": 19.05, "curve": 0.286, "c3": 0.626, "c4": 0.38}, {"time": 5.3333, "x": 35.78, "y": 16.85}]}, "bone102": {"translate": [{"x": 16.69, "y": 7.86, "curve": 0.358, "c2": 0.42, "c3": 0.707, "c4": 0.81}, {"time": 0.8333, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 40.47, "y": 19.05, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 5.3333, "x": 16.69, "y": 7.86}]}, "bone101": {"translate": [{"x": 20.1, "y": 9.46, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 20.1, "y": 9.46}]}, "bone100": {"translate": [{"x": 26.83, "y": 12.63, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.6667, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 40.47, "y": 19.05, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 26.83, "y": 12.63}]}, "bone99": {"translate": [{"x": 39.77, "y": 18.72, "curve": 0.288, "c2": 0.13, "c3": 0.632, "c4": 0.51}, {"time": 0.6667, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 40.47, "y": 19.05, "curve": 0.314, "c3": 0.649, "c4": 0.35}, {"time": 5.3333, "x": 39.77, "y": 18.72}]}, "bone98": {"translate": [{"x": 33.07, "y": 15.57, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.6667, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 40.47, "y": 19.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 33.07, "y": 15.57}]}, "bone97": {"translate": [{"x": 13.4, "y": 6.31, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.6667, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 40.47, "y": 19.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 13.4, "y": 6.31}]}, "bone96": {"translate": [{"x": 16.77, "y": 7.89, "curve": 0.344, "c2": 0.37, "c3": 0.683, "c4": 0.72}, {"time": 0.5, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 40.47, "y": 19.05, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 5.3333, "x": 16.77, "y": 7.89}]}, "bone95": {"translate": [{"x": 23.54, "y": 11.08, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.5, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 40.47, "y": 19.05, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "x": 23.54, "y": 11.08}]}, "bone94": {"translate": [{"x": 38.1, "y": 17.94, "curve": 0.304, "c2": 0.22, "c3": 0.643, "c4": 0.57}, {"time": 0.5, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 40.47, "y": 19.05, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 38.1, "y": 17.94}]}, "bone93": {"translate": [{"x": 30.08, "y": 14.16, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.5, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 30.08, "y": 14.16}]}, "bone92": {"translate": [{"x": 10.25, "y": 4.83, "curve": 0.357, "c2": 0.43, "c3": 0.696, "c4": 0.78}, {"time": 0.5, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 40.47, "y": 19.05, "curve": 0.243, "c3": 0.653, "c4": 0.62}, {"time": 5.3333, "x": 10.25, "y": 4.83}]}, "bone91": {"translate": [{"x": 13.52, "y": 6.36, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.3333, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 40.47, "y": 19.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 13.52, "y": 6.36}]}, "bone90": {"translate": [{"x": 20.2, "y": 9.51, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.3333, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 20.2, "y": 9.51}]}, "bone89": {"translate": [{"x": 35.8, "y": 16.86, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 40.47, "y": 19.05, "curve": 0.286, "c3": 0.626, "c4": 0.38}, {"time": 5.3333, "x": 35.8, "y": 16.86}]}, "bone88": {"translate": [{"x": 26.9, "y": 12.66, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3333, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 40.47, "y": 19.05, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 26.9, "y": 12.66}]}, "bone87": {"translate": [{"x": 7.37, "y": 3.47, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.3333, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 40.47, "y": 19.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 7.37, "y": 3.47}]}, "bone86": {"translate": [{"x": 10.39, "y": 4.89, "curve": 0.34, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.1667, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 40.47, "y": 19.05, "curve": 0.243, "c3": 0.653, "c4": 0.62}, {"time": 5.3333, "x": 10.39, "y": 4.89}]}, "bone85": {"translate": [{"x": 16.86, "y": 7.94, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 40.47, "y": 19.05, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 5.3333, "x": 16.86, "y": 7.94}]}, "bone84": {"translate": [{"x": 33.07, "y": 15.57, "curve": 0.327, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.1667, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 40.47, "y": 19.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 33.07, "y": 15.57}]}, "bone83": {"translate": [{"x": 23.6, "y": 11.11, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.1667, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 40.47, "y": 19.05, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "x": 23.6, "y": 11.11}]}, "bone82": {"translate": [{"x": 4.75, "y": 2.24, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 0.1667, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 40.47, "y": 19.05, "curve": 0.243, "c3": 0.683, "c4": 0.73}, {"time": 5.3333, "x": 4.75, "y": 2.24}]}, "bone81": {"translate": [{"x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 40.47, "y": 19.05, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 2.54, "y": 1.2}]}, "bone80": {"translate": [{"x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 40.47, "y": 19.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 7.47, "y": 3.51}]}, "bone79": {"translate": [{"x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 40.47, "y": 19.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 13.59, "y": 6.4}]}, "bone78": {"translate": [{"x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 20.24, "y": 9.53}]}, "bone77": {"translate": [{"x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 30.04, "y": 14.14}]}}, "deform": {"default": {"bu_gebo_zuo": {"bu_gebo_zuo": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "offset": 6, "vertices": [2.12303, 0.37089, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.88581, -5.2587, 3.88584, -5.25872, 0.8444, 1.29983, 0.57665, 1.4387, 0.57666, 1.43876, -0.11582, -0.66452, -0.11581, -0.66451, 0.43241, -1.45427, 0.43244, -1.45425, 1.13281, 1.63, 0.79567, 1.81849, 0.79568, 1.81852, -1.51688, -2.03108, -1.01301, -2.33515, -1.01299, -2.33513, -1.17973, -1.10408, -0.88996, -1.34859, -0.88996, -1.3486, -1.01257, -1.55475, -0.6224, -1.7479, -0.62238, -1.74791, 0, 0, 0, 0, 0, 0, 0.08057, -2.40664, 0.63914, -2.32162, 0.63917, -2.32162, -2.6473, -0.29723, -2.53934, -0.89, -2.53929, -0.89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.23206, -0.81923, -1.47757, -0.07702, 1.38908, -0.50958, 1.3891, -0.50959], "curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 56, "vertices": [0.71233, -2.0556, 0.71236, -2.05556, 1.6888, 2.59966, 1.1533, 2.87741, 1.15333, 2.87751, 1.41657, 0.70128, 1.41658, 0.70132, 2.37301, 1.92099, 2.37303, 1.92106, 2.26563, 3.26, 1.59133, 3.63697, 1.59136, 3.63704, 0.1965, -0.60814, 0.31042, -0.55864, 0.31045, -0.55857, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.13432, -1.48777, -0.8248, -1.67929, -0.82478, -1.67927], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "offset": 6, "vertices": [2.12303, 0.37089, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.88581, -5.2587, 3.88584, -5.25872, 0.8444, 1.29983, 0.57665, 1.4387, 0.57666, 1.43876, -0.11582, -0.66452, -0.11581, -0.66451, 0.43241, -1.45427, 0.43244, -1.45425, 1.13281, 1.63, 0.79567, 1.81849, 0.79568, 1.81852, -1.51688, -2.03108, -1.01301, -2.33515, -1.01299, -2.33513, -1.17973, -1.10408, -0.88996, -1.34859, -0.88996, -1.3486, -1.01257, -1.55475, -0.6224, -1.7479, -0.62238, -1.74791, 0, 0, 0, 0, 0, 0, 0.08057, -2.40664, 0.63914, -2.32162, 0.63917, -2.32162, -2.6473, -0.29723, -2.53934, -0.89, -2.53929, -0.89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.23206, -0.81923, -1.47757, -0.07702, 1.38908, -0.50958, 1.3891, -0.50959], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "offset": 56, "vertices": [0.71233, -2.0556, 0.71236, -2.05556, 1.6888, 2.59966, 1.1533, 2.87741, 1.15333, 2.87751, 1.41657, 0.70128, 1.41658, 0.70132, 2.37301, 1.92099, 2.37303, 1.92106, 2.26563, 3.26, 1.59133, 3.63697, 1.59136, 3.63704, 0.1965, -0.60814, 0.31042, -0.55864, 0.31045, -0.55857, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.13432, -1.48777, -0.8248, -1.67929, -0.82478, -1.67927], "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "toufa_qian": {"toufa_qian": [{"offset": 304, "vertices": [3.70932, -2.59893, 0.62549, -1.84477]}]}, "lian": {"lian": [{"time": 3.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "offset": 204, "vertices": [-6.26775, -3.14085, -6.26764, -3.14065, -14.0208, -3.15606, -14.02054, -3.15593, 11.86844, 1.28577, 11.86856, 1.28584, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -12.07744, -1.21315, -12.07721, -1.21301, 9.67101, -0.43882, 9.67122, -0.43865, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.06068, 2.09795, -14.06067, 2.09808, -12.24785, 2.16142, -12.24762, 2.16161, -5.22189, 1.96262, -5.22176, 1.96272, 2.0798, -0.02811, 2.07985, -0.02809, 6.87152, 1.39724, 6.87166, 1.39731, 8.49419, -1.2309, 8.49435, -1.23083, 8.82622, 0.38473, 8.82623, 0.38481, 9.04208, 3.33, 9.04218, 3.33015, 2.78505, 0.94241, 2.7851, 0.94247, 0, 0, 0, 0, -7.905, -0.02135, -7.90486, -0.02124, -0.89096, 0.37597, -0.89085, 0.37607, -2.17204, 0.3013, -2.17204, 0.30136, -0.57458, 0.40372, -0.57449, 0.40379, 0.33096, 0.06946, 0.33105, 0.06954, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -11.18349, -1.89521, -11.18338, -1.89513, -5.65327, -0.9438, -5.6532, -0.94373, 0, 0, 0, 0, 2.87653, 0.61215, 2.87656, 0.61221, 9.87442, 2.03271, 9.8745, 2.03283, 10.68687, 1.76672, 10.68701, 1.76683, 8.79529, 0.69911, 8.79555, 0.69928, 5.07928, -0.60579, 5.07941, -0.60565, 0, 0, 0, 0, -8.62756, -1.91077, -8.6274, -1.91065, -14.01909, -2.28908, -14.01886, -2.28886, -7.97127, -1.54274, -7.97096, -1.54251, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.52002, -0.29404, -0.5199, -0.29399, -1.37471, 0.10455, -1.37466, 0.10458, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.68279, 1.31727, -8.6828, 1.31738, -6.82719, -0.13294, -6.82718, -0.13282], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4}]}}}}, "animation2": {"slots": {"shui": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "paopao1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "color": "ffffffff", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "shui3": {"color": [{"color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff", "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1, "color": "ffffffcf", "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff", "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.6667, "color": "ffffffcf", "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "color": "ffffff7f"}]}, "hua_zuoshang": {"attachment": [{"name": null}]}, "shui2": {"color": [{"color": "ffffffcf", "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "color": "ffffffff", "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "color": "ffffffcf", "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5, "color": "ffffffff", "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "color": "ffffffcf"}]}, "yu3_1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.9, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.2, "color": "ffffff00"}], "attachment": [{"name": null}]}, "yu3_1_2": {"color": [{"color": "ffffff00", "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "color": "ffffff00"}]}, "paopao3": {"color": [{"color": "ffffff60", "curve": 0.315, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 1.3, "color": "ffffffff", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9667, "color": "ffffff00", "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 5.3333, "color": "ffffff60"}]}, "paopao2": {"color": [{"color": "ffffffff", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "color": "ffffffff"}]}, "bg": {"attachment": [{"name": null}]}}, "bones": {"bone2": {"rotate": [{"angle": -0.95, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -1.09, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.95, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -1.09, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.95}]}, "bone3": {"rotate": [{"angle": -0.39, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "angle": -0.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.16, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.39, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.8333, "angle": -0.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -1.16, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -0.39}], "translate": [{"x": 1.06, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "x": 0.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 3.17, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.06, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.8333, "x": 0.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 3.17, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 1.06}]}, "bone4": {"rotate": [{"angle": -0.77, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "angle": -0.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -1.16, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.77, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 2.8333, "angle": -0.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": -1.16, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.77}], "translate": [{"x": 0.71, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "x": 0.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 0.71, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 2.8333, "x": 0.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 1.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 0.71}]}, "bone5": {"rotate": [{"angle": -0.58, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.58, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "angle": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.58}], "translate": [{"x": 0.34, "y": -1.56, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "x": 0.23, "y": -1.05, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.68, "y": -3.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.34, "y": -1.56, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "x": 0.23, "y": -1.05, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 0.68, "y": -3.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.34, "y": -1.56}]}, "bone6": {"rotate": [{"angle": -0.58, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.58, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "angle": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.58}], "translate": [{"x": 0.68, "y": 2.13, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "x": 0.46, "y": 1.43, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.37, "y": 4.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.68, "y": 2.13, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "x": 0.46, "y": 1.43, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.37, "y": 4.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.68, "y": 2.13}]}, "bone7": {"rotate": [{"angle": 2.4, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "angle": 1.95, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.4, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2.8333, "angle": 1.95, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 2.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 2.4}]}, "bone8": {"rotate": [{"angle": 2.93, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.1667, "angle": 2.74, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 2.93, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.8333, "angle": 2.74, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.93}]}, "bone9": {"rotate": [{"angle": -2.26, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "angle": -1.83, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -2.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.26, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2.8333, "angle": -1.83, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -2.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -2.26}]}, "bone10": {"rotate": [{"angle": -2.76, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.1667, "angle": -2.59, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.76, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.8333, "angle": -2.59, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.76}]}, "bone11": {"rotate": [{"angle": -6.16, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -7.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -7.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -6.16, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -7.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -7.53, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -6.16}]}, "bone12": {"rotate": [{"angle": -5.18, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "angle": -6.34, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.3333, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -5.18, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.8333, "angle": -6.34, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.1667, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -5.18}]}, "bone13": {"rotate": [{"angle": -0.31, "curve": 0.31, "c2": 0.22, "c3": 0.646, "c4": 0.57}, {"time": 0.1667, "angle": -1.22, "curve": 0.316, "c2": 0.27, "c3": 0.715, "c4": 0.83}, {"time": 1.0333, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.2, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.6667, "angle": -0.31, "curve": 0.31, "c2": 0.22, "c3": 0.646, "c4": 0.57}, {"time": 2.8333, "angle": -1.22, "curve": 0.316, "c2": 0.27, "c3": 0.715, "c4": 0.83}, {"time": 3.7, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.8667, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.3333, "angle": -0.31}]}, "bone14": {"rotate": [{"angle": -1.43, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "angle": -2.64, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.8333, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.43, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2.8333, "angle": -2.64, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 3.5, "angle": -7.27, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6667, "angle": -7.76, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -1.43}], "translate": [{"x": 2.36, "y": -2.24, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "x": 4.37, "y": -4.14, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.8333, "x": 12.03, "y": -11.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "x": 12.83, "y": -12.16, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 2.36, "y": -2.24, "curve": 0.321, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2.8333, "x": 4.37, "y": -4.14, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 3.5, "x": 12.03, "y": -11.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6667, "x": 12.83, "y": -12.16, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 2.36, "y": -2.24}]}, "bone15": {"translate": [{"x": -5.61, "y": 4.54, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "x": -6.43, "y": 5.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": -6.86, "y": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -5.61, "y": 4.54, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "x": -6.43, "y": 5.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": -6.86, "y": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": -5.61, "y": 4.54}]}, "bone18": {"translate": [{"x": -11.07, "y": 15.96, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": -13.77, "y": 13.97, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.6667, "x": 0.9, "y": 24.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -11.07, "y": 15.96, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": -13.77, "y": 13.97, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4.3333, "x": 0.9, "y": 24.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": -11.07, "y": 15.96}]}, "bone20": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": -30.87, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": -30.87, "curve": "stepped"}, {"time": 5.3333}]}, "bone21": {"rotate": [{"angle": -1.48, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -1.7, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -1.81, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.48, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -1.7, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -1.81, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.48}]}, "bone22": {"rotate": [{"angle": -0.74, "curve": 0.338, "c2": 0.35, "c3": 0.688, "c4": 0.73}, {"time": 0.4333, "angle": -1.48, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.6, "angle": -1.7, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.7667, "angle": -1.81, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "angle": -0.74, "curve": 0.338, "c2": 0.35, "c3": 0.688, "c4": 0.73}, {"time": 3.1, "angle": -1.48, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.2667, "angle": -1.7, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.4333, "angle": -1.81, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "angle": -0.74}]}, "bone23": {"rotate": [{"angle": -0.1, "curve": 0.269, "c2": 0.11, "c3": 0.675, "c4": 0.69}, {"time": 0.9, "angle": -2.94, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.0667, "angle": -3.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.2333, "angle": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "angle": -0.1, "curve": 0.269, "c2": 0.11, "c3": 0.675, "c4": 0.69}, {"time": 3.5667, "angle": -2.94, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.7333, "angle": -3.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.9, "angle": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 5.3333, "angle": -0.1}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone26": {"rotate": [{"angle": 7.75, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 8.88, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 7.75, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 8.88, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 7.75}]}, "bone27": {"rotate": [{"angle": 4.78, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": 6.34, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": 8.88, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 4.78, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "angle": 6.34, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 3.1667, "angle": 8.88, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 4.78}]}, "bone28": {"rotate": [{"angle": -6.77, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -7.76, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -8.28, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -6.77, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -7.76, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -8.28, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -6.77}]}, "bone29": {"rotate": [{"angle": -4.18, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": -5.54, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": -7.76, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": -8.28, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -4.18, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.8333, "angle": -5.54, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 3.1667, "angle": -7.76, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": -8.28, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -4.18}]}, "bone30": {"rotate": [{"angle": 1.27, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": 2.06, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5, "angle": 2.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.27, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": 2.06, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1667, "angle": 2.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 1.27}]}, "bone31": {"rotate": [{"angle": 0.46, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 1.27, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.5, "angle": 1.68, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.8333, "angle": 2.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.46, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 3, "angle": 1.27, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 3.1667, "angle": 1.68, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 3.5, "angle": 2.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6667, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 0.46}]}, "bone32": {"rotate": [{"angle": 2.28, "curve": 0.342, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.1333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.3, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.4667, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 2.28, "curve": 0.342, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 2.8, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.9667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.1333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "angle": 2.28}]}, "bone33": {"rotate": [{"angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.67}]}, "bone34": {"rotate": [{"angle": 1.76, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 0.3, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.4667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": 1.76, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 2.9667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "angle": 1.76}]}, "bone35": {"rotate": [{"angle": 0.8, "curve": 0.323, "c2": 0.3, "c3": 0.687, "c4": 0.73}, {"time": 0.6, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.7667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.9333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 0.8, "curve": 0.323, "c2": 0.3, "c3": 0.687, "c4": 0.73}, {"time": 3.2667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.4333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "angle": 0.8}]}, "bone36": {"rotate": [{"angle": 1.65, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.65, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 1.65}]}, "bone37": {"rotate": [{"angle": 0.7, "curve": 0.319, "c2": 0.29, "c3": 0.687, "c4": 0.73}, {"time": 0.6333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.9667, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "angle": 0.7, "curve": 0.319, "c2": 0.29, "c3": 0.687, "c4": 0.73}, {"time": 3.3, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.4667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "angle": 0.7}]}, "bone38": {"rotate": [{"angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.67}]}, "bone39": {"rotate": [{"angle": 1.11, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.6667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.8333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 1.11, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.1667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.3333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 1.11}]}, "bone40": {"rotate": [{"angle": 0.43, "curve": 0.304, "c2": 0.24, "c3": 0.684, "c4": 0.72}, {"time": 0.7333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.9, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0667, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.43, "curve": 0.304, "c2": 0.24, "c3": 0.684, "c4": 0.72}, {"time": 3.4, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": 0.43}]}, "bone41": {"rotate": [{"angle": 0.52, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 0.7, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "angle": 0.52, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 3.3667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "angle": 0.52}], "translate": [{"x": 2.02, "y": -0.21, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 0.7, "x": 10.4, "y": -1.08, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8667, "x": 11.92, "y": -1.24, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0333, "x": 12.72, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "x": 2.02, "y": -0.21, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 3.3667, "x": 10.4, "y": -1.08, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5333, "x": 11.92, "y": -1.24, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7, "x": 12.72, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "x": 2.02, "y": -0.21}]}, "bone42": {"rotate": [{"angle": 0.03, "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 0.0333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.0333, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.2, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.3667, "angle": 3.26, "curve": 0.249, "c3": 0.739, "c4": 0.95}, {"time": 2.6667, "angle": 0.03, "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 2.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.7, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.8667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 4.0333, "angle": 3.26, "curve": 0.249, "c3": 0.739, "c4": 0.95}, {"time": 5.3333, "angle": 0.03}]}, "bone43": {"rotate": [{"angle": 1.43, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 0.4, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.7333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "angle": 1.43, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 3.0667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.2333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.4, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5.3333, "angle": 1.43}], "translate": [{"x": 4.34, "y": 3.83, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 0.4, "x": 8.09, "y": 7.15, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5667, "x": 9.27, "y": 8.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.7333, "x": 9.89, "y": 8.74, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "x": 4.34, "y": 3.83, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 3.0667, "x": 8.09, "y": 7.15, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.2333, "x": 9.27, "y": 8.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.4, "x": 9.89, "y": 8.74, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5.3333, "x": 4.34, "y": 3.83}]}, "bone44": {"rotate": [{"angle": 0.52, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 0.7, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "angle": 0.52, "curve": 0.309, "c2": 0.26, "c3": 0.685, "c4": 0.72}, {"time": 3.3667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "angle": 0.52}]}, "bone45": {"rotate": [{"angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.67}], "translate": [{"x": 7.84, "y": -8.07, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "x": 8.98, "y": -9.25, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 9.59, "y": -9.87, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 7.84, "y": -8.07, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "x": 8.98, "y": -9.25, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 9.59, "y": -9.87, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 7.84, "y": -8.07}]}, "bone46": {"rotate": [{"angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": 3.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.67}]}, "bone47": {"rotate": [{"angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.51}]}, "bone48": {"rotate": [{"angle": -0.56, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.56, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.56}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.88, "y": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 3.88, "y": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone49": {"rotate": [{"angle": -0.13, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.8667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.0333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.2, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.6667, "angle": -0.13, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 3.5333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.7, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.8667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.3333, "angle": -0.13}]}, "bone50": {"rotate": [{"angle": -0.31, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.4, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.5667, "angle": -2.99, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": -0.31, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.9, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4.0667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 4.2333, "angle": -2.99, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 5.3333, "angle": -0.31}], "translate": [{"x": 9.59, "y": 0.68}]}, "bone51": {"rotate": [{"angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.44}]}, "bone52": {"rotate": [{"angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.44}]}, "bone53": {"rotate": [{"angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.44}]}, "bone54": {"rotate": [{"angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.5, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.51}]}, "bone55": {"rotate": [{"angle": -0.39, "curve": 0.304, "c2": 0.24, "c3": 0.684, "c4": 0.72}, {"time": 0.7333, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.9, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.0667, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": -0.39, "curve": 0.304, "c2": 0.24, "c3": 0.684, "c4": 0.72}, {"time": 3.4, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.5667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.7333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": -0.39}]}, "bone56": {"rotate": [{"angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.1667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 2.8333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.44}]}, "bone57": {"rotate": [{"angle": -0.73, "curve": 0.323, "c2": 0.3, "c3": 0.687, "c4": 0.73}, {"time": 0.6, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.7667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.9333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -0.73, "curve": 0.323, "c2": 0.3, "c3": 0.687, "c4": 0.73}, {"time": 3.2667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.4333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.6, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "angle": -0.73}]}, "bone58": {"rotate": [{"angle": -0.25, "curve": 0.292, "c2": 0.2, "c3": 0.681, "c4": 0.71}, {"time": 0.8, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.9667, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.1333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": -0.25, "curve": 0.292, "c2": 0.2, "c3": 0.681, "c4": 0.71}, {"time": 3.4667, "angle": -2.44, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 3.6333, "angle": -2.8, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.8, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "angle": -0.25}]}, "bone60": {"rotate": [{"angle": 1.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.97, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 7.97, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 1.47}]}, "bone61": {"rotate": [{"angle": 3.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 3.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 7.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 3.99}]}, "bone62": {"rotate": [{"angle": 6.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 7.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 6.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 7.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 6.5}]}, "bone65": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 15.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.157, "y": 1.157, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone67": {"translate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.8667, "x": 175.71, "y": 25.6}]}, "bone69": {"translate": [{"curve": 0.25, "c3": 0.747, "c4": 0.99}, {"time": 5.3, "y": 109.87, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 1.162, "y": 1.162}]}, "bone70": {"translate": [{"x": -20.81, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.9667, "x": -30.87, "curve": "stepped"}, {"time": 1, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2.6667, "x": -20.81, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 3.6333, "x": -30.87, "curve": "stepped"}, {"time": 3.6667, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 5.3333, "x": -20.81}]}, "bone71": {"translate": [{"x": -5.81, "curve": 0.313, "c2": 0.27, "c3": 0.668, "c4": 0.67}, {"time": 1, "x": -20.81, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 1.9667, "x": -30.87, "curve": "stepped"}, {"time": 2, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 2.6667, "x": -5.81, "curve": 0.313, "c2": 0.27, "c3": 0.668, "c4": 0.67}, {"time": 3.6667, "x": -20.81, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.6333, "x": -30.87, "curve": "stepped"}, {"time": 4.6667, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 5.3333, "x": -5.81}]}, "bone72": {"rotate": [{"angle": 64.29, "curve": "stepped"}, {"time": 1.6333, "angle": 64.29, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -80.94}]}, "bone74": {"translate": [{"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "x": 167.54}]}, "bone73": {"rotate": [{"angle": -31.69, "curve": "stepped"}, {"time": 2.9, "angle": -31.69, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": 43.36}]}, "bone75": {"translate": [{"y": 55.05, "curve": 0.374, "c2": 0.49, "c3": 0.748, "c4": 0.99}, {"time": 2.6333, "y": 109.87, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "y": 55.05}], "scale": [{"x": 1.082, "y": 1.082, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.162, "y": 1.162, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.082, "y": 1.082}]}, "bone76": {"translate": [{"y": 20.93, "curve": 0.315, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 1.3, "y": 55.05, "curve": 0.374, "c2": 0.49, "c3": 0.748, "c4": 0.99}, {"time": 3.9333, "y": 109.87, "curve": "stepped"}, {"time": 3.9667, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 5.3333, "y": 20.93}], "scale": [{"x": 1.031, "y": 1.031, "curve": 0.315, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 1.3, "x": 1.082, "y": 1.082, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9333, "x": 1.162, "y": 1.162, "curve": "stepped"}, {"time": 3.9667, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 5.3333, "x": 1.031, "y": 1.031}]}, "bone121": {"translate": [{"x": 37.11, "y": 17.47, "curve": 0.292, "c2": 0.2, "c3": 0.681, "c4": 0.71}, {"time": 1.6, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": 40.47, "y": 19.05, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 37.11, "y": 17.47}]}, "bone120": {"translate": [{"x": 40.24, "y": 18.95, "curve": 0.254, "c2": 0.04, "c3": 0.645, "c4": 0.59}, {"time": 1.6, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": 40.47, "y": 19.05, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 5.3333, "x": 40.24, "y": 18.95}]}, "bone119": {"translate": [{"x": 31.3, "y": 14.74, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.7667, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.6, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 3.4333, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 5.3333, "x": 31.3, "y": 14.74}]}, "bone118": {"translate": [{"x": 38.75, "y": 18.24, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.2667, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5.3333, "x": 38.75, "y": 18.24}]}, "bone117": {"translate": [{"x": 31.73, "y": 14.94, "curve": 0.326, "c2": 0.31, "c3": 0.716, "c4": 0.83}, {"time": 1.6, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "x": 40.47, "y": 19.05, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 31.73, "y": 14.94}]}, "bone116": {"translate": [{"x": 32.94, "y": 15.51, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 1.3333, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 40.47, "y": 19.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 32.94, "y": 15.51}]}, "bone115": {"translate": [{"x": 38.01, "y": 17.9, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 1.3333, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 40.47, "y": 19.05, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 38.01, "y": 17.9}]}, "bone114": {"translate": [{"x": 35.76, "y": 16.83, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.5, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 3.1667, "curve": 0.243, "c3": 0.683, "c4": 0.73}, {"time": 5.3333, "x": 35.76, "y": 16.83}]}, "bone113": {"translate": [{"x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 40.47, "y": 19.05}]}, "bone112": {"translate": [{"x": 26.7, "y": 12.57, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 1.3333, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 40.47, "y": 19.05, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 26.7, "y": 12.57}]}, "bone111": {"translate": [{"x": 26.75, "y": 12.59, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 1, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 40.47, "y": 19.05, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 26.75, "y": 12.59}]}, "bone110": {"translate": [{"x": 33, "y": 15.54, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 1, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 40.47, "y": 19.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 33, "y": 15.54}]}, "bone109": {"translate": [{"x": 39.61, "y": 18.65, "curve": 0.351, "c2": 0.65, "c3": 0.686}, {"time": 0.1667, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.8333, "curve": 0.247, "c3": 0.724, "c4": 0.89}, {"time": 5.3333, "x": 39.61, "y": 18.65}]}, "bone108": {"translate": [{"x": 38.09, "y": 17.93, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 1, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 40.47, "y": 19.05, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 38.09, "y": 17.93}]}, "bone107": {"translate": [{"x": 20.03, "y": 9.43, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 1, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 20.03, "y": 9.43}]}, "bone106": {"translate": [{"x": 23.45, "y": 11.04, "curve": 0.339, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 0.8333, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 40.47, "y": 19.05, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "x": 23.45, "y": 11.04}]}, "bone105": {"translate": [{"x": 30.01, "y": 14.13, "curve": 0.322, "c2": 0.3, "c3": 0.67, "c4": 0.68}, {"time": 0.8333, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 30.01, "y": 14.13}]}, "bone104": {"translate": [{"x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.8333, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 40.47, "y": 19.05}]}, "bone103": {"translate": [{"x": 35.78, "y": 16.85, "curve": 0.303, "c2": 0.23, "c3": 0.652, "c4": 0.62}, {"time": 0.8333, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 40.47, "y": 19.05, "curve": 0.286, "c3": 0.626, "c4": 0.38}, {"time": 5.3333, "x": 35.78, "y": 16.85}]}, "bone102": {"translate": [{"x": 16.69, "y": 7.86, "curve": 0.358, "c2": 0.42, "c3": 0.707, "c4": 0.81}, {"time": 0.8333, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 40.47, "y": 19.05, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 5.3333, "x": 16.69, "y": 7.86}]}, "bone101": {"translate": [{"x": 20.1, "y": 9.46, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 20.1, "y": 9.46}]}, "bone100": {"translate": [{"x": 26.83, "y": 12.63, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.6667, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 40.47, "y": 19.05, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 26.83, "y": 12.63}]}, "bone99": {"translate": [{"x": 39.77, "y": 18.72, "curve": 0.288, "c2": 0.13, "c3": 0.632, "c4": 0.51}, {"time": 0.6667, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 40.47, "y": 19.05, "curve": 0.314, "c3": 0.649, "c4": 0.35}, {"time": 5.3333, "x": 39.77, "y": 18.72}]}, "bone98": {"translate": [{"x": 33.07, "y": 15.57, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.6667, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 40.47, "y": 19.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 33.07, "y": 15.57}]}, "bone97": {"translate": [{"x": 13.4, "y": 6.31, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.6667, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 40.47, "y": 19.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 13.4, "y": 6.31}]}, "bone96": {"translate": [{"x": 16.77, "y": 7.89, "curve": 0.344, "c2": 0.37, "c3": 0.683, "c4": 0.72}, {"time": 0.5, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 40.47, "y": 19.05, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 5.3333, "x": 16.77, "y": 7.89}]}, "bone95": {"translate": [{"x": 23.54, "y": 11.08, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.5, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 40.47, "y": 19.05, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "x": 23.54, "y": 11.08}]}, "bone94": {"translate": [{"x": 38.1, "y": 17.94, "curve": 0.304, "c2": 0.22, "c3": 0.643, "c4": 0.57}, {"time": 0.5, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 40.47, "y": 19.05, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 38.1, "y": 17.94}]}, "bone93": {"translate": [{"x": 30.08, "y": 14.16, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.5, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 30.08, "y": 14.16}]}, "bone92": {"translate": [{"x": 10.25, "y": 4.83, "curve": 0.357, "c2": 0.43, "c3": 0.696, "c4": 0.78}, {"time": 0.5, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 40.47, "y": 19.05, "curve": 0.243, "c3": 0.653, "c4": 0.62}, {"time": 5.3333, "x": 10.25, "y": 4.83}]}, "bone91": {"translate": [{"x": 13.52, "y": 6.36, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.3333, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 40.47, "y": 19.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 13.52, "y": 6.36}]}, "bone90": {"translate": [{"x": 20.2, "y": 9.51, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.3333, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 20.2, "y": 9.51}]}, "bone89": {"translate": [{"x": 35.8, "y": 16.86, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 40.47, "y": 19.05, "curve": 0.286, "c3": 0.626, "c4": 0.38}, {"time": 5.3333, "x": 35.8, "y": 16.86}]}, "bone88": {"translate": [{"x": 26.9, "y": 12.66, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3333, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 40.47, "y": 19.05, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 26.9, "y": 12.66}]}, "bone87": {"translate": [{"x": 7.37, "y": 3.47, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.3333, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 40.47, "y": 19.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 7.37, "y": 3.47}]}, "bone86": {"translate": [{"x": 10.39, "y": 4.89, "curve": 0.34, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.1667, "x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 40.47, "y": 19.05, "curve": 0.243, "c3": 0.653, "c4": 0.62}, {"time": 5.3333, "x": 10.39, "y": 4.89}]}, "bone85": {"translate": [{"x": 16.86, "y": 7.94, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 40.47, "y": 19.05, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 5.3333, "x": 16.86, "y": 7.94}]}, "bone84": {"translate": [{"x": 33.07, "y": 15.57, "curve": 0.327, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.1667, "x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 40.47, "y": 19.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 33.07, "y": 15.57}]}, "bone83": {"translate": [{"x": 23.6, "y": 11.11, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.1667, "x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 40.47, "y": 19.05, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "x": 23.6, "y": 11.11}]}, "bone82": {"translate": [{"x": 4.75, "y": 2.24, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 0.1667, "x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 40.47, "y": 19.05, "curve": 0.243, "c3": 0.683, "c4": 0.73}, {"time": 5.3333, "x": 4.75, "y": 2.24}]}, "bone81": {"translate": [{"x": 2.54, "y": 1.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 40.47, "y": 19.05, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 2.54, "y": 1.2}]}, "bone80": {"translate": [{"x": 7.47, "y": 3.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 40.47, "y": 19.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 7.47, "y": 3.51}]}, "bone79": {"translate": [{"x": 13.59, "y": 6.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 40.47, "y": 19.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 13.59, "y": 6.4}]}, "bone78": {"translate": [{"x": 20.24, "y": 9.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 40.47, "y": 19.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 20.24, "y": 9.53}]}, "bone77": {"translate": [{"x": 30.04, "y": 14.14, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 40.47, "y": 19.05, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 30.04, "y": 14.14}]}}, "deform": {"default": {"bu_gebo_zuo": {"bu_gebo_zuo": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "offset": 6, "vertices": [2.12303, 0.37089, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.88581, -5.2587, 3.88584, -5.25872, 0.8444, 1.29983, 0.57665, 1.4387, 0.57666, 1.43876, -0.11582, -0.66452, -0.11581, -0.66451, 0.43241, -1.45427, 0.43244, -1.45425, 1.13281, 1.63, 0.79567, 1.81849, 0.79568, 1.81852, -1.51688, -2.03108, -1.01301, -2.33515, -1.01299, -2.33513, -1.17973, -1.10408, -0.88996, -1.34859, -0.88996, -1.3486, -1.01257, -1.55475, -0.6224, -1.7479, -0.62238, -1.74791, 0, 0, 0, 0, 0, 0, 0.08057, -2.40664, 0.63914, -2.32162, 0.63917, -2.32162, -2.6473, -0.29723, -2.53934, -0.89, -2.53929, -0.89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.23206, -0.81923, -1.47757, -0.07702, 1.38908, -0.50958, 1.3891, -0.50959], "curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 56, "vertices": [0.71233, -2.0556, 0.71236, -2.05556, 1.6888, 2.59966, 1.1533, 2.87741, 1.15333, 2.87751, 1.41657, 0.70128, 1.41658, 0.70132, 2.37301, 1.92099, 2.37303, 1.92106, 2.26563, 3.26, 1.59133, 3.63697, 1.59136, 3.63704, 0.1965, -0.60814, 0.31042, -0.55864, 0.31045, -0.55857, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.13432, -1.48777, -0.8248, -1.67929, -0.82478, -1.67927], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "offset": 6, "vertices": [2.12303, 0.37089, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.88581, -5.2587, 3.88584, -5.25872, 0.8444, 1.29983, 0.57665, 1.4387, 0.57666, 1.43876, -0.11582, -0.66452, -0.11581, -0.66451, 0.43241, -1.45427, 0.43244, -1.45425, 1.13281, 1.63, 0.79567, 1.81849, 0.79568, 1.81852, -1.51688, -2.03108, -1.01301, -2.33515, -1.01299, -2.33513, -1.17973, -1.10408, -0.88996, -1.34859, -0.88996, -1.3486, -1.01257, -1.55475, -0.6224, -1.7479, -0.62238, -1.74791, 0, 0, 0, 0, 0, 0, 0.08057, -2.40664, 0.63914, -2.32162, 0.63917, -2.32162, -2.6473, -0.29723, -2.53934, -0.89, -2.53929, -0.89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.23206, -0.81923, -1.47757, -0.07702, 1.38908, -0.50958, 1.3891, -0.50959], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "offset": 56, "vertices": [0.71233, -2.0556, 0.71236, -2.05556, 1.6888, 2.59966, 1.1533, 2.87741, 1.15333, 2.87751, 1.41657, 0.70128, 1.41658, 0.70132, 2.37301, 1.92099, 2.37303, 1.92106, 2.26563, 3.26, 1.59133, 3.63697, 1.59136, 3.63704, 0.1965, -0.60814, 0.31042, -0.55864, 0.31045, -0.55857, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.13432, -1.48777, -0.8248, -1.67929, -0.82478, -1.67927], "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "toufa_qian": {"toufa_qian": [{"offset": 304, "vertices": [3.70932, -2.59893, 0.62549, -1.84477]}]}, "lian": {"lian": [{"time": 3.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "offset": 204, "vertices": [-6.26775, -3.14085, -6.26764, -3.14065, -14.0208, -3.15606, -14.02054, -3.15593, 11.86844, 1.28577, 11.86856, 1.28584, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -12.07744, -1.21315, -12.07721, -1.21301, 9.67101, -0.43882, 9.67122, -0.43865, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.06068, 2.09795, -14.06067, 2.09808, -12.24785, 2.16142, -12.24762, 2.16161, -5.22189, 1.96262, -5.22176, 1.96272, 2.0798, -0.02811, 2.07985, -0.02809, 6.87152, 1.39724, 6.87166, 1.39731, 8.49419, -1.2309, 8.49435, -1.23083, 8.82622, 0.38473, 8.82623, 0.38481, 9.04208, 3.33, 9.04218, 3.33015, 2.78505, 0.94241, 2.7851, 0.94247, 0, 0, 0, 0, -7.905, -0.02135, -7.90486, -0.02124, -0.89096, 0.37597, -0.89085, 0.37607, -2.17204, 0.3013, -2.17204, 0.30136, -0.57458, 0.40372, -0.57449, 0.40379, 0.33096, 0.06946, 0.33105, 0.06954, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -11.18349, -1.89521, -11.18338, -1.89513, -5.65327, -0.9438, -5.6532, -0.94373, 0, 0, 0, 0, 2.87653, 0.61215, 2.87656, 0.61221, 9.87442, 2.03271, 9.8745, 2.03283, 10.68687, 1.76672, 10.68701, 1.76683, 8.79529, 0.69911, 8.79555, 0.69928, 5.07928, -0.60579, 5.07941, -0.60565, 0, 0, 0, 0, -8.62756, -1.91077, -8.6274, -1.91065, -14.01909, -2.28908, -14.01886, -2.28886, -7.97127, -1.54274, -7.97096, -1.54251, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.52002, -0.29404, -0.5199, -0.29399, -1.37471, 0.10455, -1.37466, 0.10458, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.68279, 1.31727, -8.6828, 1.31738, -6.82719, -0.13294, -6.82718, -0.13282], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4}]}}}}}}