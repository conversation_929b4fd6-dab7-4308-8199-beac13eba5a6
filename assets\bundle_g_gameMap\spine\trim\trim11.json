{"skeleton": {"hash": "N9pu4VrLR1rF8DIKqZiwdPxQoIM=", "spine": "3.8.75", "x": -279.55, "y": -127.88, "width": 586.63, "height": 532.42, "images": "./images/", "audio": "D:/spine导出/灵兽动画/山膏"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 670.82, "rotation": -179.62, "x": 22.39, "y": -20.75, "scaleX": 0.3862, "scaleY": 0.3862}, {"name": "bone2", "parent": "bone", "length": 80.91, "rotation": -149.65, "x": 42.24, "y": -191.95, "color": "f0ff00ff"}, {"name": "bone3", "parent": "bone2", "length": 79.15, "rotation": 11.86, "x": 80.91, "color": "f0ff00ff"}, {"name": "bone4", "parent": "bone", "length": 91.29, "rotation": 28.56, "x": 51.2, "y": -188.2, "color": "f0ff00ff"}, {"name": "bone5", "parent": "bone4", "length": 92.9, "rotation": -3.15, "x": 91.29, "color": "f0ff00ff"}, {"name": "bone6", "parent": "bone3", "length": 78.8, "rotation": 107.61, "x": 41.83, "y": 186.23}, {"name": "bone7", "parent": "bone6", "length": 92.5, "rotation": -1.05, "x": 78.8}, {"name": "bone8", "parent": "bone3", "x": 97.56, "y": 2.39, "color": "abe323ff"}, {"name": "bone9", "parent": "bone3", "x": 123.43, "y": 64.3, "color": "abe323ff"}, {"name": "bone10", "parent": "bone3", "length": 57.36, "rotation": 17.36, "x": 152.46, "y": 91.64}, {"name": "bone11", "parent": "bone10", "length": 64.9, "rotation": 5.21, "x": 57.36}, {"name": "bone12", "parent": "bone3", "x": 126.39, "y": 96.58, "color": "abe323ff"}, {"name": "bone13", "parent": "bone3", "rotation": -42.59, "x": 131.08, "y": 37.82, "scaleX": 2.5454, "scaleY": 2.5454}, {"name": "bone14", "parent": "bone13", "x": -13.67, "y": -24.77}, {"name": "bone16", "parent": "bone3", "length": 35.98, "rotation": 43.87, "x": 134.8, "y": 115.33}, {"name": "bone17", "parent": "bone16", "length": 33.7, "rotation": 2.28, "x": 35.98}, {"name": "bone18", "parent": "bone17", "length": 28.49, "rotation": 10.23, "x": 33.7}, {"name": "bone19", "parent": "bone3", "length": 36.47, "rotation": 96.11, "x": 133.27, "y": 157.98}, {"name": "bone20", "parent": "bone19", "length": 54.22, "rotation": 21.75, "x": 36.47}, {"name": "bone21", "parent": "bone3", "length": 38.53, "rotation": 135.2, "x": 110.7, "y": 183.76}, {"name": "bone22", "parent": "bone21", "length": 41.94, "rotation": 10.83, "x": 38.53}, {"name": "bone23", "parent": "bone4", "length": 173.48, "rotation": 47.41, "x": 57.93, "y": -108.26}, {"name": "bone24", "parent": "bone23", "length": 108.74, "rotation": 102.49, "x": 173.48}, {"name": "bone25", "parent": "bone24", "length": 47.7, "rotation": -40.47, "x": 108.74}, {"name": "bone26", "parent": "bone4", "length": 250.22, "rotation": 28.85, "x": 63.19, "y": -109.66}, {"name": "bone27", "parent": "bone26", "length": 207.52, "rotation": 133.09, "x": 249.98, "y": 0.37, "color": "abe323ff"}, {"name": "target1", "parent": "bone", "rotation": 179.62, "x": 195.34, "y": -86.98, "color": "ff3f00ff"}, {"name": "bone56", "parent": "bone", "length": 40.07, "rotation": 176.71, "x": 43.17, "y": -48.6, "color": "10ff00ff"}, {"name": "target2", "parent": "bone56", "rotation": 2.91, "x": -43.65, "y": 34.87, "color": "ff3f00ff"}, {"name": "target3", "parent": "target2", "x": 32.16, "y": -30.79, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone43", "parent": "bone2", "length": 67.8, "rotation": 103.35, "x": 46.83, "y": 51.21}, {"name": "bone44", "parent": "bone43", "length": 95.02, "rotation": 28.66, "x": 67.44, "y": -0.37}, {"name": "bone45", "parent": "bone4", "length": 66.5, "rotation": -54.52, "x": 9.72, "y": -61.02}, {"name": "bone46", "parent": "bone45", "length": 78.53, "rotation": 18.45, "x": 66.5}, {"name": "bone48", "parent": "bone5", "length": 65.07, "rotation": -35.77, "x": -22.01, "y": -57.78}, {"name": "bone49", "parent": "bone48", "length": 51.62, "rotation": 25.54, "x": 65.07}, {"name": "bone50", "parent": "bone2", "length": 78.79, "rotation": -119.83, "x": 1.9, "y": 13.32}, {"name": "bone51", "parent": "bone50", "length": 57.71, "rotation": 34.39, "x": 78.79}, {"name": "bone52", "parent": "bone51", "length": 44.52, "rotation": 10.84, "x": 57.71}, {"name": "bone53", "parent": "bone2", "length": 90.96, "rotation": -142.33, "x": -1.26, "y": 15.76}, {"name": "bone54", "parent": "bone53", "length": 79.95, "rotation": 77.97, "x": 89.22, "y": 0.47, "color": "abe323ff"}, {"name": "jio1", "parent": "bone", "rotation": 179.62, "x": 46.76, "y": -124.58, "color": "ff3f00ff"}, {"name": "bone55", "parent": "bone", "length": 47.82, "rotation": 179.62, "x": -22.84, "y": -42.23, "color": "10ff00ff"}, {"name": "jio2", "parent": "bone55", "x": -36.97, "y": 35.15, "color": "ff3f00ff"}, {"name": "jio3", "parent": "jio2", "x": 31.37, "y": -31.04, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone60", "parent": "bone2", "length": 63.91, "rotation": -70.11, "x": -25.27, "y": -18.79}, {"name": "bone61", "parent": "bone60", "length": 52.18, "rotation": -33.48, "x": 63.91}, {"name": "bone62", "parent": "bone61", "length": 41.51, "rotation": 43.99, "x": 52.18}, {"name": "bone63", "parent": "bone2", "length": 87.16, "rotation": -47.15, "x": -25.99, "y": -14.68}, {"name": "bone64", "parent": "bone63", "length": 65.94, "rotation": -86.68, "x": 87.16, "color": "abe323ff"}, {"name": "a1", "parent": "bone", "rotation": 179.62, "x": 4.94, "y": -122.09, "color": "ff3f00ff"}, {"name": "bone65", "parent": "bone", "length": 41.89, "rotation": 177.86, "x": -50.97, "y": -51.98, "color": "09ff00ff"}, {"name": "a2", "parent": "bone65", "rotation": 1.75, "x": -41.89, "y": 18.75, "color": "ff3f00ff"}, {"name": "a3", "parent": "a2", "x": 35.63, "y": -19.94, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone66", "parent": "bone2", "length": 63.16, "rotation": -76.48, "x": 51.5, "y": -39.86}, {"name": "bone67", "parent": "bone66", "length": 55.03, "rotation": -49.4, "x": 63.16}, {"name": "bone68", "parent": "bone67", "length": 39.33, "rotation": 61.57, "x": 55.03}, {"name": "bone69", "parent": "bone2", "length": 83.9, "rotation": -61.73, "x": 56.57, "y": -39.84}, {"name": "bone70", "parent": "bone69", "length": 67.69, "rotation": -90.51, "x": 83.9, "color": "abe323ff"}, {"name": "b1", "parent": "bone", "rotation": 179.62, "x": -66.28, "y": -137.82, "color": "ff3f00ff"}, {"name": "bone71", "parent": "bone", "length": 47.07, "rotation": 179.9, "x": -98.32, "y": -59, "color": "61ff00ff"}, {"name": "b2", "parent": "bone71", "rotation": -0.28, "x": -37.88, "y": 24.06, "color": "ff3f00ff"}, {"name": "b3", "parent": "b2", "x": 12.95, "y": -8.54, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone72", "parent": "bone5", "length": 62.59, "rotation": -50.6, "x": 74.33, "y": -9.46}, {"name": "bone73", "parent": "bone72", "length": 81.87, "rotation": -45.69, "x": 62.59}, {"name": "bone74", "parent": "bone73", "length": 83.72, "rotation": 33.04, "x": 81.87}, {"name": "<PERSON><PERSON><PERSON>", "parent": "root", "rotation": -100.83, "x": 54, "y": 72.37, "scaleX": 1.943, "scaleY": 1.943}, {"name": "shouji2", "parent": "root", "rotation": -39.74, "x": 50.9, "y": 107.25, "scaleX": -1.6428, "scaleY": 1.6428}, {"name": "bone15", "parent": "root", "x": 2.39, "y": -141.09}, {"name": "bone28", "parent": "bone15", "length": 165.44, "rotation": 1.19, "x": 23.45, "y": 4.29}, {"name": "bone29", "parent": "bone15", "length": 149.98, "rotation": 179.67, "x": -18, "y": 1.71}, {"name": "bone30", "parent": "bone28", "length": 90.02, "rotation": 90.45, "x": 172.81, "y": 24.71}, {"name": "bone31", "parent": "bone29", "length": 90.02, "rotation": -88.04, "x": 146.3, "y": -12.02}, {"name": "bone32", "parent": "root", "x": 117.02, "y": 456.52}, {"name": "bone33", "parent": "bone32", "length": 157.14, "rotation": -49.24, "x": -367.43, "y": -55.79}, {"name": "bone34", "parent": "bone32", "length": 157.14, "rotation": 42.42, "x": 167.11, "y": -124.53, "scaleX": -1}, {"name": "bone35", "parent": "root", "scaleX": 7.5022, "scaleY": 7.5022}], "slots": [{"name": "Shadow", "bone": "bone35", "attachment": "Shadow"}, {"name": "shangao01", "bone": "bone"}, {"name": "shangao02", "bone": "bone", "attachment": "shangao02"}, {"name": "shangao03", "bone": "bone", "attachment": "shangao03"}, {"name": "shangao04", "bone": "bone", "attachment": "shangao04"}, {"name": "shangao05", "bone": "bone", "attachment": "shangao05"}, {"name": "shangao06", "bone": "bone", "attachment": "shangao06"}, {"name": "shangao08", "bone": "bone", "attachment": "shangao08"}, {"name": "shangao09", "bone": "bone", "attachment": "shangao09"}, {"name": "shangao07", "bone": "bone", "attachment": "shangao07"}, {"name": "shangao010", "bone": "bone", "attachment": "shangao010"}, {"name": "shangao012", "bone": "bone", "attachment": "shangao012"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "s<PERSON><PERSON><PERSON><PERSON>"}, {"name": "shangao013", "bone": "bone12", "attachment": "shangao014"}, {"name": "shangao015", "bone": "bone", "attachment": "shangao015"}, {"name": "shangao016", "bone": "bone3"}, {"name": "shouji/tx_baodian02", "bone": "<PERSON><PERSON><PERSON>"}, {"name": "shouji/tx_baodian2", "bone": "shouji2"}, {"name": "qt1", "bone": "bone30", "color": "ffffff00", "dark": "000000", "attachment": "qt1"}, {"name": "qt2", "bone": "bone31", "color": "ffffff00", "dark": "000000", "attachment": "qt1"}, {"name": "cmcm", "bone": "bone33", "color": "ffffff00", "attachment": "cmcm"}, {"name": "cmcm2", "bone": "bone34", "color": "ffffff00", "attachment": "cmcm"}], "ik": [{"name": "a1", "order": 12, "bones": ["bone60"], "target": "a1", "compress": true, "stretch": true}, {"name": "a2", "order": 13, "bones": ["bone61"], "target": "a2", "compress": true, "stretch": true}, {"name": "a3", "order": 14, "bones": ["bone62"], "target": "a3"}, {"name": "a4", "order": 10, "bones": ["bone63", "bone64"], "target": "a2", "bendPositive": false}, {"name": "b1", "order": 17, "bones": ["bone66"], "target": "b1", "compress": true, "stretch": true}, {"name": "b2", "order": 18, "bones": ["bone67"], "target": "b2", "compress": true, "stretch": true}, {"name": "b3", "order": 19, "bones": ["bone68"], "target": "b3"}, {"name": "b4", "order": 15, "bones": ["bone69", "bone70"], "target": "b2", "bendPositive": false}, {"name": "jio1", "order": 7, "bones": ["bone50"], "target": "jio1", "compress": true, "stretch": true}, {"name": "jio2", "order": 8, "bones": ["bone51"], "target": "jio2", "compress": true, "stretch": true}, {"name": "jio3", "order": 9, "bones": ["bone52"], "target": "jio3"}, {"name": "jio4", "order": 5, "bones": ["bone53", "bone54"], "target": "jio2"}, {"name": "target1", "order": 2, "bones": ["bone23"], "target": "target1", "compress": true, "stretch": true}, {"name": "target2", "order": 3, "bones": ["bone24"], "target": "target2", "compress": true, "stretch": true}, {"name": "target3", "order": 4, "bones": ["bone25"], "target": "target3"}, {"name": "target4", "bones": ["bone26", "bone27"], "target": "target2"}], "transform": [{"name": "11", "order": 6, "bones": ["jio1"], "target": "bone54", "rotation": 32.45, "x": 26.91, "y": 21.71, "scaleX": 0.6138, "scaleY": 0.6138, "rotateMix": 0.809, "translateMix": 0.809, "scaleMix": 0.809, "shearMix": 0.809}, {"name": "a5", "order": 11, "bones": ["a1"], "target": "bone64", "rotation": 102.68, "x": 20.75, "y": -25.63, "scaleX": 0.6138, "scaleY": 0.6138, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "b5", "order": 16, "bones": ["b1"], "target": "bone70", "rotation": 122.16, "x": 18.46, "y": -24.53, "scaleX": 0.6138, "scaleY": 0.6138, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "bizi", "order": 24, "bones": ["bone13"], "target": "bone8", "rotation": -42.59, "x": 33.52, "y": 35.43, "scaleX": 0.5969, "scaleY": 0.5969, "rotateMix": 0.136, "translateMix": 0.136, "scaleMix": 0.136, "shearMix": 0.136}, {"name": "erd", "order": 21, "bones": ["bone6"], "target": "bone9", "rotation": 107.61, "x": -81.6, "y": 121.93, "shearY": -360, "rotateMix": 0, "translateMix": 0.311, "scaleMix": 0, "shearMix": 0}, {"name": "erduo1", "order": 23, "bones": ["bone10"], "target": "bone9", "rotation": 17.36, "x": 29.03, "y": 27.35, "rotateMix": 0, "translateMix": 0.12, "scaleMix": 0, "shearMix": 0}, {"name": "eye", "order": 22, "bones": ["bone12"], "target": "bone9", "x": 2.96, "y": 32.28, "rotateMix": 0, "translateMix": -0.186, "scaleMix": 0, "shearMix": 0}, {"name": "face", "order": 20, "bones": ["bone9"], "target": "bone8", "x": 25.87, "y": 61.9, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "jio1", "order": 1, "bones": ["target1"], "target": "bone27", "rotation": -12.66, "x": 100.76, "y": 25.87, "scaleX": 0.6138, "scaleY": 0.6138, "rotateMix": 0.806, "translateMix": 0.806, "scaleMix": 0.806, "shearMix": 0.806}], "skins": [{"name": "default", "attachments": {"Shadow": {"Shadow": {"scaleX": 0.5153, "scaleY": 0.5153, "width": 42, "height": 24}}, "cmcm2": {"cmcm": {"x": 82.66, "y": -0.92, "scaleX": 0.7044, "scaleY": 0.7044, "rotation": 18.1, "width": 219, "height": 90}}, "cmcm": {"cmcm": {"x": 82.66, "y": -0.92, "scaleX": 0.7044, "scaleY": 0.7044, "rotation": 18.1, "width": 219, "height": 90}}, "shangao02": {"shangao02": {"type": "mesh", "uvs": [1, 0.84356, 0.99125, 0.92118, 0.93429, 0.97939, 0.84402, 1, 0.71869, 0.98168, 0.6144, 0.90406, 0.5031, 0.79219, 0.41079, 0.61011, 0.34925, 0.38043, 0.22261, 0.24478, 0.06994, 0.09526, 0, 0.0521, 0, 0, 0.04745, 0, 0.15752, 0, 0.31256, 0.0259, 0.46405, 0.08293, 0.61672, 0.18158, 0.75401, 0.33881, 0.82147, 0.51762, 0.86644, 0.70414, 0.8842, 0.79354, 0.96112, 0.79663, 0.05068, 0.03652, 0.16413, 0.07517, 0.3125, 0.132, 0.4853, 0.25022, 0.59701, 0.46619, 0.67207, 0.63442, 0.72967, 0.81401, 0.78902, 0.90495, 0.89026, 0.88449, 0.9496, 0.8663], "triangles": [23, 13, 14, 12, 13, 23, 11, 12, 23, 24, 14, 15, 23, 14, 24, 10, 23, 24, 11, 23, 10, 25, 24, 15, 25, 15, 16, 9, 24, 25, 10, 24, 9, 26, 16, 17, 25, 16, 26, 8, 25, 26, 9, 25, 8, 27, 26, 17, 27, 17, 18, 8, 26, 27, 7, 8, 27, 27, 18, 19, 28, 27, 19, 7, 27, 28, 6, 7, 28, 5, 6, 28, 28, 19, 20, 29, 28, 20, 29, 20, 21, 32, 21, 22, 32, 22, 0, 31, 21, 32, 30, 29, 21, 29, 5, 28, 31, 30, 21, 1, 32, 0, 2, 31, 32, 2, 32, 1, 4, 5, 29, 4, 29, 30, 3, 30, 31, 3, 31, 2, 4, 30, 3], "vertices": [2, 64, -13.76, -14.65, 0.9767, 65, -42.85, -64.87, 0.0233, 3, 64, -18.32, 0.77, 0.98778, 65, -57.07, -57.35, 0.01222, 66, -147.75, 27.68, 0, 3, 64, -9.52, 17.99, 0.95535, 65, -63.24, -39.03, 0.04428, 66, -142.93, 46.4, 0.00037, 3, 64, 10.58, 31.94, 0.8639, 65, -59.18, -14.9, 0.13115, 66, -126.37, 64.42, 0.00496, 3, 64, 42.53, 42.57, 0.70101, 65, -44.47, 15.39, 0.27052, 66, -97.52, 81.78, 0.02847, 3, 64, 74.49, 39.81, 0.48747, 65, -20.17, 36.32, 0.41602, 66, -65.74, 86.09, 0.09651, 3, 64, 111.08, 31.46, 0.27724, 65, 11.37, 56.68, 0.48974, 66, -28.2, 85.96, 0.23301, 3, 64, 149.12, 7.92, 0.12192, 65, 54.78, 67.45, 0.44718, 66, 14.06, 71.31, 0.4309, 3, 64, 183.79, -27.92, 0.03789, 65, 104.65, 67.22, 0.31383, 66, 55.74, 43.93, 0.64827, 3, 64, 226.15, -38.98, 0.007, 65, 142.15, 89.81, 0.16587, 66, 99.49, 42.42, 0.82713, 3, 64, 276.01, -49.7, 0.00031, 65, 184.65, 118, 0.06215, 66, 150.49, 42.88, 0.93754, 2, 65, 199.23, 132.65, 0.01489, 66, 170.7, 47.21, 0.98511, 2, 65, 209.29, 129.08, 0.00114, 66, 177.19, 38.73, 0.99886, 2, 65, 205.06, 117.14, 0.00026, 66, 167.14, 31.03, 0.99974, 3, 64, 262.98, -77.23, 1e-05, 65, 195.25, 89.44, 0.01264, 66, 143.81, 13.16, 0.98736, 3, 64, 223.18, -89.79, 0.00155, 65, 176.43, 52.19, 0.0598, 66, 107.72, -7.8, 0.93864, 3, 64, 181.56, -96.14, 0.01589, 65, 151.9, 17.97, 0.16602, 66, 68.5, -23.12, 0.81809, 3, 64, 136.07, -94.89, 0.06645, 65, 119.23, -13.71, 0.30931, 66, 23.84, -31.86, 0.62424, 3, 64, 89.27, -81.01, 0.18293, 65, 76.61, -37.5, 0.42438, 66, -24.86, -28.57, 0.39269, 3, 64, 57.54, -55.3, 0.36846, 65, 36.05, -42.24, 0.43706, 66, -61.45, -10.42, 0.19448, 3, 64, 30.6, -25.63, 0.58973, 65, -4.01, -40.79, 0.34215, 66, -94.23, 12.63, 0.06813, 3, 64, 18.61, -10.98, 0.78553, 65, -22.86, -39.14, 0.19941, 66, -109.14, 24.29, 0.01506, 3, 64, -0.3, -19.03, 0.91596, 65, -30.32, -58.29, 0.0833, 66, -125.83, 12.3, 0.00074, 2, 65, 197.72, 118.83, 0.01121, 66, 161.9, 36.45, 0.98879, 2, 65, 180.14, 92.92, 0.05714, 66, 133.04, 24.31, 0.94286, 3, 64, 214.07, -70.03, 0.01184, 65, 155.93, 59.47, 0.16181, 66, 94.5, 9.47, 0.82635, 3, 64, 162.02, -67.4, 0.05971, 65, 117.68, 24.07, 0.3045, 66, 43.14, 0.65, 0.6358, 3, 64, 116.37, -39.72, 0.18013, 65, 65.99, 10.73, 0.41511, 66, -7.46, 17.65, 0.40476, 3, 64, 83.7, -16.83, 0.37463, 65, 26.79, 3.35, 0.42157, 66, -44.34, 32.84, 0.2038, 3, 64, 54.3, 10.14, 0.60768, 65, -13.05, 1.15, 0.31892, 66, -78.94, 52.72, 0.07339, 3, 64, 32.09, 20.41, 0.80673, 65, -35.91, -7.56, 0.17624, 66, -102.86, 57.88, 0.01703, 3, 64, 9.31, 5.26, 0.93297, 65, -40.98, -34.44, 0.06586, 66, -121.77, 38.11, 0.00118, 3, 64, -3.5, -4.77, 0.97807, 65, -42.75, -50.63, 0.02192, 66, -132.08, 25.51, 1e-05], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 24, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 267, "height": 205}}, "shangao03": {"shangao03": {"type": "mesh", "uvs": [0.95144, 0.55736, 1, 0.53833, 1, 0.40389, 0.95193, 0.23973, 0.8441, 0.08831, 0.74864, 0.04671, 0.66871, 0.01189, 0.50371, 0.02887, 0.38549, 0.08972, 0.26207, 0.09821, 0.26467, 0.18171, 0.27246, 0.26379, 0.34068, 0.28898, 0.38792, 0.30298, 0.29223, 0.32884, 0.18393, 0.35308, 0.1053, 0.34742, 0.01702, 0.2949, 0.01183, 0.34338, 0.02741, 0.40156, 0.06079, 0.4557, 0.10381, 0.49691, 0.06407, 0.54994, 0.02984, 0.63228, 0.0113, 0.72083, 0.0113, 0.80239, 0.04053, 0.85677, 0.07052, 0.8229, 0.08956, 0.79879, 0.07241, 0.85839, 0.07401, 0.91585, 0.06282, 0.95937, 0.04044, 0.99071, 0.05962, 1, 0.09159, 0.99158, 0.13155, 0.95415, 0.19948, 0.90627, 0.28864, 0.83909, 0.4004, 0.78726, 0.45953, 0.76395, 0.51659, 0.74146, 0.56196, 0.70771, 0.62835, 0.71132, 0.68589, 0.71373, 0.70016, 0.67523, 0.77346, 0.62474, 0.83177, 0.60301, 0.89633, 0.57895, 0.35968, 0.1903, 0.49981, 0.186, 0.6656, 0.18815, 0.80771, 0.29349, 0.85902, 0.44184, 0.51165, 0.32789, 0.64784, 0.43324, 0.7031, 0.52138, 0.18008, 0.45689, 0.34981, 0.45688, 0.5156, 0.50204, 0.60836, 0.59018, 0.15442, 0.71702, 0.33402, 0.69123, 0.44652, 0.69123, 0.04745, 0.7947, 0.07271, 0.69113, 0.14996, 0.6086, 0.29703, 0.56653, 0.44113, 0.57462, 0.54957, 0.62641, 0.63425, 0.6701, 0.07097, 0.97702, 0.09949, 0.94013, 0.12208, 0.87216, 0.15773, 0.80679, 0.2225, 0.7673, 0.30866, 0.74724, 0.38532, 0.73883, 0.45084, 0.72948, 0.03964, 0.35259, 0.09675, 0.40263, 0.18738, 0.40263, 0.30533, 0.37829, 0.43568, 0.3837, 0.55487, 0.43779, 0.6455, 0.51488, 0.70633, 0.59466, 0.38851, 0.23899, 0.53997, 0.25522, 0.6815, 0.3269, 0.75599, 0.43509, 0.80317, 0.51758, 0.36368, 0.14163, 0.499, 0.11999, 0.67281, 0.10647, 0.82055, 0.20248, 0.89504, 0.3296, 0.92732, 0.44726], "triangles": [22, 21, 65, 23, 22, 65, 64, 23, 65, 61, 60, 65, 64, 65, 60, 24, 23, 64, 63, 24, 64, 28, 63, 64, 60, 28, 64, 25, 24, 63, 73, 60, 74, 28, 60, 73, 27, 63, 28, 26, 25, 63, 26, 63, 27, 72, 28, 73, 29, 28, 72, 36, 73, 74, 72, 73, 36, 30, 29, 72, 71, 30, 72, 35, 71, 72, 36, 35, 72, 31, 30, 71, 70, 31, 71, 32, 31, 70, 34, 70, 71, 34, 71, 35, 33, 32, 70, 33, 70, 34, 65, 56, 66, 61, 66, 67, 62, 67, 68, 61, 67, 62, 40, 62, 68, 61, 65, 66, 40, 77, 62, 76, 61, 62, 76, 62, 77, 68, 41, 40, 61, 74, 60, 75, 61, 76, 39, 77, 40, 75, 74, 61, 38, 76, 77, 38, 77, 39, 37, 74, 75, 38, 37, 75, 38, 75, 76, 36, 74, 37, 78, 18, 17, 16, 78, 17, 81, 14, 13, 81, 13, 82, 19, 18, 78, 79, 78, 16, 19, 78, 79, 81, 80, 15, 81, 15, 14, 16, 15, 80, 79, 16, 80, 20, 19, 79, 57, 81, 82, 56, 79, 80, 56, 80, 81, 56, 81, 57, 21, 79, 56, 20, 79, 21, 66, 56, 57, 65, 21, 56, 66, 57, 67, 54, 53, 88, 83, 53, 54, 82, 53, 83, 58, 82, 83, 57, 82, 58, 84, 83, 54, 58, 83, 84, 55, 54, 89, 84, 54, 55, 67, 57, 58, 59, 58, 84, 85, 55, 90, 84, 55, 85, 45, 85, 90, 68, 58, 59, 67, 58, 68, 85, 69, 59, 85, 59, 84, 68, 59, 69, 44, 69, 85, 44, 85, 45, 41, 68, 69, 42, 41, 69, 43, 69, 44, 42, 69, 43, 82, 13, 53, 93, 6, 5, 7, 6, 93, 92, 8, 7, 92, 7, 93, 91, 9, 8, 91, 8, 92, 10, 9, 91, 50, 49, 92, 91, 92, 49, 93, 50, 92, 48, 10, 91, 48, 91, 49, 93, 5, 94, 50, 93, 94, 86, 48, 49, 87, 49, 50, 86, 49, 87, 11, 10, 48, 11, 48, 86, 12, 11, 86, 13, 12, 86, 53, 13, 86, 87, 50, 88, 87, 53, 86, 53, 87, 88, 94, 5, 4, 94, 4, 3, 51, 50, 94, 95, 51, 94, 88, 50, 51, 3, 95, 94, 95, 3, 2, 89, 88, 51, 54, 88, 89, 52, 51, 95, 96, 52, 95, 89, 51, 52, 2, 96, 95, 90, 89, 52, 55, 89, 90, 96, 2, 1, 0, 96, 1, 47, 52, 96, 47, 96, 0, 90, 52, 47, 46, 90, 47, 46, 45, 90], "vertices": [2, 31, -23.81, 5.82, 0.94575, 33, -65.83, -33.63, 0.05425, 3, 31, -28.99, -5.66, 0.98558, 32, -87.15, 41.61, 0.00026, 33, -74.68, -42.59, 0.01417, 3, 31, -7.36, -26.61, 0.96256, 32, -78.22, 12.85, 0.03502, 33, -61.68, -69.76, 0.00242, 3, 31, 27.22, -43.76, 0.85063, 32, -56.11, -18.79, 0.1493, 33, -35.22, -97.86, 7e-05, 2, 31, 69.89, -48.46, 0.63264, 32, -20.92, -43.37, 0.36736, 2, 31, 92.78, -38.21, 0.37248, 32, 4.09, -45.36, 0.62752, 2, 31, 111.95, -29.62, 0.16024, 32, 25.03, -47.02, 0.83976, 3, 31, 137.23, 1.94, 0.04522, 32, 62.35, -31.44, 0.95474, 34, -13.09, -93.94, 3e-05, 4, 31, 147.5, 32.15, 0.00682, 32, 85.85, -9.86, 0.99095, 33, 103.95, -68.5, 4e-05, 34, 13.85, -76.83, 0.00219, 3, 32, 114.05, 0.89, 0.98925, 33, 130.29, -53.78, 0.00095, 34, 43.49, -71.21, 0.00981, 3, 32, 107.89, 18.56, 0.95505, 33, 121.65, -37.19, 0.00811, 34, 40.55, -52.73, 0.03684, 4, 31, 138.68, 79.08, 0.00049, 32, 100.62, 35.55, 0.86632, 33, 111.99, -21.42, 0.03615, 34, 36.38, -34.72, 0.09704, 4, 31, 123.05, 71.05, 0.00123, 32, 83.05, 36, 0.69556, 33, 94.54, -23.52, 0.07617, 34, 19.16, -31.19, 0.22704, 4, 31, 112.78, 64.95, 0.02478, 32, 71.11, 35.58, 0.43196, 33, 82.79, -25.67, 0.16015, 34, 7.33, -29.51, 0.38311, 4, 31, 124.86, 85.76, 0.00123, 32, 91.69, 48.04, 0.21173, 33, 101.35, -10.36, 0.06363, 34, 29.78, -20.86, 0.72342, 3, 32, 115.31, 61.06, 0.05983, 33, 122.84, 5.94, 0.01375, 34, 55.33, -12.2, 0.92643, 2, 32, 134.01, 65.55, 0.00335, 34, 74.53, -11.07, 0.99665, 3, 34, 97.36, -20.08, 0.99951, 35, 121.26, -72.05, 2e-05, 36, 19.64, -89.23, 0.00047, 3, 34, 97.27, -9.14, 0.9937, 35, 120.62, -61.13, 0.00024, 36, 23.78, -79.11, 0.00606, 3, 34, 91.88, 3.32, 0.94927, 35, 114.62, -48.96, 0.0039, 36, 23.61, -65.54, 0.04682, 3, 34, 82.29, 14.34, 0.84319, 35, 104.5, -38.43, 0.01144, 36, 19.01, -51.67, 0.14538, 4, 33, 126.57, 43.44, 0.00234, 34, 70.73, 22.2, 0.66997, 35, 92.56, -31.16, 0.02213, 36, 11.38, -39.96, 0.30556, 3, 34, 78.88, 35.19, 0.36312, 35, 100.05, -17.77, 0.01283, 36, 23.9, -31.12, 0.62405, 3, 34, 84.88, 54.53, 0.14013, 35, 105.08, 1.84, 0.0053, 36, 36.9, -15.59, 0.85457, 3, 34, 86.91, 74.77, 0.03571, 35, 106.1, 22.16, 0.00099, 36, 46.57, 2.31, 0.9633, 3, 34, 84.64, 92.9, 0.00409, 35, 102.93, 40.15, 0.00269, 36, 51.47, 19.91, 0.99323, 3, 34, 76.06, 104.1, 2e-05, 35, 93.79, 50.91, 0.01232, 36, 47.87, 33.55, 0.98766, 3, 34, 69.74, 95.66, 1e-05, 35, 87.9, 42.17, 0.05167, 36, 38.78, 28.21, 0.94831, 3, 34, 65.8, 89.73, 1e-05, 35, 84.26, 36.05, 0.17877, 36, 32.86, 24.25, 0.82122, 3, 34, 68.29, 103.49, 0, 35, 86.07, 49.92, 0.15192, 36, 40.47, 35.99, 0.84808, 3, 34, 66.31, 116.22, 0, 35, 83.45, 62.53, 0.17712, 36, 43.55, 48.49, 0.82288, 3, 34, 67.81, 126.23, 0, 35, 84.45, 72.6, 0.18457, 36, 48.79, 57.15, 0.81543, 3, 34, 72.36, 133.87, 0, 35, 88.61, 80.46, 0.18268, 36, 55.93, 62.45, 0.81732, 3, 34, 67.45, 135.36, 0, 35, 83.64, 81.7, 0.18384, 36, 51.98, 65.71, 0.81616, 3, 34, 59.95, 132.52, 0, 35, 76.29, 78.49, 0.20759, 36, 43.96, 65.99, 0.79241, 3, 34, 51.31, 122.99, 0, 35, 68.14, 68.54, 0.2952, 36, 32.32, 60.52, 0.7048, 3, 34, 36.2, 110.29, 0, 35, 53.67, 55.11, 0.46554, 36, 13.48, 54.64, 0.53446, 4, 33, 52.8, 93.11, 0.00024, 34, 16.48, 92.66, 0, 35, 34.86, 36.52, 0.68707, 36, -11.52, 45.97, 0.31268, 4, 33, 33.21, 70.87, 0.01796, 34, -9.14, 77.76, 0, 35, 10.01, 20.36, 0.85434, 36, -40.9, 42.1, 0.12771, 4, 33, 22.45, 59.93, 0.09237, 34, -22.81, 70.79, 0, 35, -3.29, 12.71, 0.87906, 36, -56.2, 40.94, 0.02858, 3, 33, 12.07, 49.37, 0.25803, 35, -16.13, 5.34, 0.74116, 36, -70.96, 39.82, 0.0008, 3, 31, 18.11, 97.52, 0.00082, 33, 5.35, 37.77, 0.48926, 35, -25.72, -4.03, 0.50992, 3, 31, 6.26, 86.44, 0.00573, 33, -9.61, 31.51, 0.70806, 35, -41.82, -6.04, 0.28621, 3, 31, -3.9, 76.73, 0.0481, 33, -22.51, 25.94, 0.81714, 35, -55.74, -7.94, 0.13476, 3, 31, -0.13, 68.23, 0.15958, 33, -21.93, 16.65, 0.78305, 35, -57.67, -17.04, 0.05737, 3, 31, -4.44, 47.52, 0.36068, 33, -33.18, -1.27, 0.61704, 35, -73.33, -31.28, 0.02228, 3, 31, -10.84, 33.91, 0.59569, 33, -43.91, -11.8, 0.39801, 35, -86.49, -38.54, 0.00631, 3, 31, -17.93, 18.84, 0.80498, 33, -55.79, -23.46, 0.1949, 35, -101.08, -46.57, 0.00012, 4, 31, 135.7, 52.34, 0.00161, 32, 85.18, 13.52, 0.97127, 33, 99.9, -45.46, 0.00473, 34, 17.3, -53.7, 0.02239, 4, 31, 112.61, 27.11, 0.11433, 32, 52.81, 2.45, 0.86908, 33, 69.48, -61.09, 0.00433, 34, -16.51, -58.89, 0.01226, 4, 31, 84.12, -1.61, 0.3361, 32, 14.04, -9.09, 0.65421, 33, 32.78, -78.12, 0.00462, 34, -56.71, -63.43, 0.00507, 4, 31, 43.05, -10.1, 0.66472, 32, -26.07, 3.15, 0.32899, 33, -8.68, -71.8, 0.00594, 34, -94.04, -44.32, 0.00036, 3, 31, 10.47, 4.02, 0.82799, 32, -47.89, 31.17, 0.16425, 33, -34.32, -47.24, 0.00776, 4, 31, 87.76, 47.15, 0.09387, 32, 40.62, 31.95, 0.33961, 33, 53.15, -33.67, 0.38056, 34, -23.32, -27.71, 0.18596, 4, 31, 47.7, 39.69, 0.18142, 32, 1.89, 44.62, 0.20627, 33, 12.99, -26.73, 0.55667, 34, -59.22, -8.42, 0.05564, 4, 31, 24.13, 43.74, 0.22441, 32, -16.85, 59.48, 0.12921, 33, -7.7, -14.74, 0.63784, 34, -75.04, 9.5, 0.00854, 4, 33, 113.65, 27.32, 0.11675, 34, 53.38, 10.99, 0.70006, 35, 75.78, -43.21, 0.03263, 36, -8.96, -43.61, 0.15056, 4, 33, 76.3, 9.44, 0.33434, 34, 12.28, 5.86, 0.57589, 35, 35, -50.39, 0.03796, 36, -48.85, -32.5, 0.05182, 4, 33, 35.44, 1.1, 0.65292, 34, -29.11, 10.87, 0.30299, 35, -6.6, -47.44, 0.03564, 36, -85.11, -11.91, 0.00846, 3, 33, 6.5, 9.14, 0.80894, 34, -54.02, 27.66, 0.15433, 35, -32.31, -31.92, 0.03673, 4, 33, 94.14, 82.59, 0.00268, 34, 52.36, 69.59, 9e-05, 35, 71.85, 15.26, 0.40822, 36, 12.7, 10.85, 0.58901, 4, 33, 57.11, 58.45, 0.00669, 34, 9.6, 58.42, 0.00021, 35, 29.69, 1.98, 0.70058, 36, -31.07, 17.03, 0.29252, 4, 33, 32.35, 46.6, 0.01004, 34, -17.64, 55.02, 0.00031, 35, 2.66, -2.78, 0.84576, 36, -57.51, 24.39, 0.14389, 3, 34, 76.1, 90.09, 0.01415, 35, 94.54, 36.92, 0.01489, 36, 42.51, 20.61, 0.97096, 4, 33, 114.63, 85.96, 0.00432, 34, 72.87, 66.31, 0.06788, 35, 92.49, 13.01, 0.08851, 36, 30.35, -0.09, 0.83928, 4, 33, 105.61, 61.15, 0.0667, 34, 56.46, 45.63, 0.12782, 35, 77.13, -8.47, 0.21034, 36, 7.24, -12.84, 0.59513, 4, 33, 77.31, 37.16, 0.20424, 34, 22.02, 31.82, 0.15949, 35, 43.42, -23.97, 0.3487, 36, -29.86, -12.3, 0.28757, 5, 31, 60.03, 97.96, 0.00012, 33, 44.81, 23.61, 0.42856, 34, -13.1, 29.26, 0.11195, 35, 8.48, -28.28, 0.37081, 36, -63.25, -1.12, 0.08856, 5, 31, 33.29, 87.02, 0.00031, 33, 15.94, 22.65, 0.62206, 34, -40.79, 37.49, 0.052, 35, -19.59, -21.44, 0.32496, 36, -85.63, 17.15, 0.00066, 4, 31, 11.89, 78.99, 0.00046, 33, -6.92, 22.56, 0.72678, 34, -62.51, 44.64, 0.0093, 35, -41.63, -15.38, 0.26346, 3, 34, 65.34, 129.91, 0, 35, 81.81, 76.15, 0.18891, 36, 47.93, 61.49, 0.81109, 3, 34, 59.46, 120.84, 0, 35, 76.39, 66.81, 0.20805, 36, 39.01, 55.4, 0.79195, 3, 34, 55.89, 105.05, 0, 35, 73.6, 50.86, 0.27275, 36, 29.62, 42.21, 0.72725, 3, 34, 49.07, 89.44, 0, 35, 67.57, 34.93, 0.41196, 36, 17.32, 30.44, 0.58803, 3, 34, 34.48, 78.71, 0, 35, 53.54, 23.48, 0.61748, 36, -0.28, 26.15, 0.38252, 3, 34, 14.18, 71.64, 0, 35, 33.61, 15.41, 0.81747, 36, -21.74, 27.46, 0.18253, 3, 34, -4.15, 67.45, 0, 35, 15.52, 10.31, 0.94387, 36, -40.26, 30.66, 0.05613, 2, 35, 0.13, 5.48, 0.98877, 36, -56.23, 32.93, 0.01123, 3, 34, 90.28, -7.94, 0.99907, 35, 113.58, -60.28, 5e-05, 36, 17.79, -75.3, 0.00088, 4, 32, 132.33, 77.98, 0.00205, 34, 75.06, 1.46, 0.99655, 35, 97.91, -51.66, 8e-05, 36, 7.37, -60.77, 0.00132, 6, 31, 130.78, 115.63, 4e-05, 32, 111.21, 71.41, 0.02193, 33, 117.29, 15.59, 0.06352, 34, 53.12, -1.29, 0.91358, 35, 76.14, -55.49, 5e-05, 36, -13.93, -54.84, 0.00088, 6, 31, 114.68, 91.17, 0.0048, 32, 85.35, 57.67, 0.05427, 33, 93.69, -1.75, 0.22095, 34, 25.24, -10.27, 0.71951, 35, 48.74, -65.85, 3e-05, 36, -43.12, -52.38, 0.00044, 4, 31, 91.68, 69.16, 0.01921, 32, 54.62, 49.39, 0.08393, 33, 64.47, -14.39, 0.47843, 34, -6.47, -13.01, 0.41843, 5, 31, 62.74, 56.7, 0.04848, 32, 23.25, 52.33, 0.07794, 33, 33.01, -16.02, 0.7044, 34, -36.83, -4.59, 0.16853, 35, -13.54, -63.27, 0.00065, 5, 31, 34.96, 52.83, 0.08059, 32, -2.99, 62.25, 0.04774, 33, 5.61, -9.99, 0.8403, 34, -60.92, 9.8, 0.02974, 35, -38.31, -50.1, 0.00164, 4, 31, 11.79, 54.6, 0.09928, 32, -22.47, 74.92, 0.02404, 33, -15.49, -0.28, 0.87423, 35, -56.03, -35.07, 0.00245, 4, 31, 122.97, 54.88, 0.06366, 32, 75.23, 21.85, 0.80379, 33, 88.85, -38.66, 0.06604, 34, 8.97, -43.75, 0.0665, 4, 31, 94.65, 30.86, 0.20739, 32, 38.86, 14.35, 0.63434, 33, 53.95, -51.33, 0.11938, 34, -28.15, -44.72, 0.03889, 4, 31, 59.09, 17.22, 0.42581, 32, 1.11, 19.44, 0.37709, 33, 15.87, -51.76, 0.18038, 34, -64.41, -33.07, 0.01672, 4, 31, 29.04, 21.02, 0.59787, 32, -23.44, 37.19, 0.16071, 33, -10.99, -37.75, 0.23975, 34, -85.45, -11.28, 0.00167, 3, 31, 7.76, 25.61, 0.68122, 32, -39.91, 51.42, 0.0509, 33, -29.35, -26.05, 0.26787, 4, 31, 142.85, 44.06, 0.00878, 32, 87.48, 2.82, 0.98814, 33, 103.73, -55.72, 0.00016, 34, 17.69, -64.64, 0.00292, 4, 31, 123.37, 16.97, 0.11693, 32, 57.39, -11.61, 0.88219, 33, 76.04, -74.34, 0.00012, 34, -14.48, -73.54, 0.00076, 4, 31, 96.04, -15.6, 0.33618, 32, 17.79, -27.09, 0.66339, 33, 39.1, -95.38, 6e-05, 34, -56.18, -81.81, 0.00037, 2, 31, 55.51, -26.54, 0.64899, 32, -23.02, -17.24, 0.35101, 3, 31, 22.41, -19.79, 0.87189, 32, -48.82, 4.56, 0.12582, 33, -31.39, -73.71, 0.00228, 3, 31, -2, -7.11, 0.97423, 32, -64.17, 27.39, 0.01175, 33, -49.88, -53.34, 0.01403], "hull": 48, "edges": [2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 20, 96, 96, 98, 98, 100, 100, 102, 102, 104, 26, 106, 106, 108, 108, 110, 42, 112, 112, 114, 114, 116, 116, 118, 56, 120, 120, 122, 122, 124, 52, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 64, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 76, 78, 78, 80, 34, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 22, 172, 172, 174, 174, 176, 176, 178, 178, 180, 90, 92, 92, 94, 18, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 2, 0, 0, 94, 192, 0, 8, 10, 10, 12], "width": 244, "height": 224}}, "shangao04": {"shangao04": {"type": "mesh", "uvs": [0, 0.2268, 0.05335, 0.1173, 0.17289, 0.0378, 0.32784, 0, 0.5337, 0.0048, 0.71079, 0.0618, 0.84361, 0.1728, 0.89452, 0.3093, 0.90116, 0.4668, 0.89452, 0.6168, 0.89673, 0.7353, 0.92551, 0.8118, 0.98749, 0.8958, 0.99192, 0.9813, 0.88345, 1, 0.70194, 1, 0.53149, 0.9783, 0.45844, 0.8943, 0.39425, 0.7788, 0.37654, 0.6618, 0.31677, 0.5208, 0.25036, 0.4353, 0.07549, 0.3753, 0, 0.2943, 0.26631, 0.15385, 0.49556, 0.22764, 0.59872, 0.38687, 0.62738, 0.55775, 0.6503, 0.75194, 0.73627, 0.89175, 0.154, 0.2617], "triangles": [11, 29, 28, 11, 28, 10, 17, 28, 29, 29, 11, 12, 16, 17, 29, 15, 16, 29, 14, 29, 12, 14, 12, 13, 15, 29, 14, 27, 26, 8, 20, 26, 27, 9, 27, 8, 19, 20, 27, 28, 27, 9, 28, 9, 10, 19, 27, 28, 18, 19, 28, 17, 18, 28, 24, 2, 3, 1, 2, 24, 4, 24, 3, 25, 4, 5, 25, 5, 6, 25, 24, 4, 30, 1, 24, 0, 1, 30, 23, 0, 30, 22, 23, 30, 26, 25, 6, 26, 6, 7, 25, 21, 30, 25, 30, 24, 21, 25, 26, 22, 30, 21, 26, 7, 8, 20, 21, 26], "vertices": [1, 55, -2.57, -16.83, 1, 1, 55, -10.63, -1.28, 1, 1, 55, -10.7, 15.97, 1, 1, 55, -3.71, 31.42, 1, 1, 55, 11.53, 46.1, 1, 1, 55, 30.38, 53.13, 1, 2, 55, 51.92, 51.17, 0.97894, 56, -46.54, 24.26, 0.02106, 3, 55, 70.36, 40.45, 0.8195, 56, -26.43, 31.51, 0.18049, 57, -9.83, 86.79, 2e-05, 3, 55, 87.91, 24.25, 0.32636, 56, -2.68, 34.55, 0.65276, 57, 3.87, 67.15, 0.02088, 3, 55, 103.71, 7.85, 0.01957, 56, 20.08, 36.12, 0.8034, 57, 15.81, 47.71, 0.17702, 2, 56, 37.98, 38.12, 0.46782, 57, 25.88, 32.77, 0.53218, 2, 56, 49.24, 42.21, 0.18175, 57, 34.72, 24.68, 0.81825, 2, 56, 61.29, 49.8, 0.02791, 57, 47.03, 17.53, 0.97209, 2, 56, 74.18, 51.54, 0.00055, 57, 54.54, 6.91, 0.99945, 1, 57, 46.81, -1.62, 1, 1, 57, 31.26, -11.93, 1, 1, 57, 14.85, -18.85, 1, 2, 56, 66.69, -4.31, 0.08386, 57, 1.59, -12.33, 0.91614, 1, 56, 49.9, -12.6, 1, 2, 55, 71.54, -35.15, 0.03038, 56, 32.39, -16.16, 0.96962, 2, 55, 51.97, -24.61, 0.53873, 56, 11.7, -24.39, 0.46127, 2, 55, 37.95, -20.45, 0.92903, 56, -0.53, -32.46, 0.07097, 1, 55, 18.94, -27, 1, 1, 55, 4.75, -23.98, 1, 1, 55, 8.57, 10.57, 1, 1, 55, 32.97, 19.66, 1, 2, 55, 57.61, 10.4, 0.94482, 56, -11.56, 2.43, 0.05518, 2, 56, 13.99, 7.92, 0.98527, 57, -12, 40.04, 0.01473, 2, 56, 43.11, 13.17, 0.61218, 57, 6.15, 16.66, 0.38782, 2, 56, 63.35, 24.06, 0.01975, 57, 25.18, 3.78, 0.98025, 1, 55, 12.23, -9.16, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 2, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 103, "height": 152}}, "shangao05": {"shangao05": {"type": "mesh", "uvs": [0.06527, 0.09287, 0.197, 0.02844, 0.35932, 0, 0.53576, 0.02259, 0.66985, 0.1124, 0.76159, 0.26468, 0.80394, 0.40916, 0.83922, 0.58097, 0.86981, 0.71568, 0.96861, 0.80744, 1, 0.92068, 0.94038, 0.96949, 0.80158, 1, 0.60398, 1, 0.51458, 0.89921, 0.44166, 0.78792, 0.38755, 0.63759, 0.29581, 0.5224, 0.17583, 0.45211, 0.03939, 0.40525, 0, 0.27444, 0, 0.18854, 0.16954, 0.19793, 0.37875, 0.19793, 0.5171, 0.33516, 0.5947, 0.50039, 0.65544, 0.70202, 0.74317, 0.85605], "triangles": [27, 26, 8, 27, 8, 9, 27, 14, 26, 11, 27, 9, 10, 11, 9, 13, 14, 27, 12, 27, 11, 13, 27, 12, 25, 24, 6, 25, 6, 7, 16, 17, 25, 26, 25, 7, 16, 25, 26, 26, 7, 8, 15, 16, 26, 14, 15, 26, 22, 0, 1, 21, 0, 22, 23, 2, 3, 23, 3, 4, 1, 2, 23, 22, 1, 23, 20, 21, 22, 24, 23, 4, 24, 4, 5, 19, 20, 22, 24, 5, 6, 18, 22, 23, 18, 23, 24, 19, 22, 18, 17, 18, 24, 25, 17, 24], "vertices": [1, 46, -5.97, 11.94, 1, 1, 46, 0.48, 29.42, 1, 1, 46, 13.14, 45.16, 1, 2, 46, 31.83, 56.16, 0.98364, 47, -58.59, 28.43, 0.01636, 2, 46, 52.72, 56.23, 0.90282, 47, -41.2, 40.23, 0.09718, 3, 46, 75.39, 45.92, 0.62884, 47, -16.44, 44.44, 0.3675, 48, -18.17, 79.71, 0.00366, 3, 46, 92.7, 32.7, 0.25109, 47, 5.49, 43.22, 0.70031, 48, -3.31, 63.54, 0.0486, 3, 46, 111.85, 15.81, 0.01434, 47, 31.04, 40.02, 0.69048, 48, 12.76, 43.42, 0.29518, 2, 47, 51.18, 37.84, 0.25315, 48, 25.68, 27.82, 0.74685, 2, 47, 67.62, 45.45, 0.02745, 48, 42.76, 21.8, 0.97255, 2, 47, 84.74, 44.29, 4e-05, 48, 54.22, 9.02, 0.99996, 1, 48, 51.38, -0.81, 1, 1, 48, 38.81, -12.96, 1, 1, 48, 17.8, -24.65, 1, 2, 47, 64.75, -11.37, 0.04294, 48, 1.07, -16.91, 0.95706, 2, 47, 46.45, -15.12, 0.86803, 48, -14.66, -6.84, 0.13197, 2, 46, 74.56, -25.4, 0.02755, 47, 23.28, -15.03, 0.97245, 2, 46, 55.31, -19.32, 0.62399, 47, 3.77, -20.81, 0.37601, 2, 46, 37.54, -20.53, 0.98268, 47, -10.37, -31.8, 0.01732, 1, 46, 20.38, -25.69, 1, 1, 46, 4.62, -13.8, 1, 1, 46, -3.3, -4, 1, 1, 46, 13.52, 7.98, 1, 2, 46, 33.21, 24.08, 0.99765, 47, -39.26, 2.67, 0.00235, 3, 46, 58.88, 19.07, 0.83228, 47, -15, 12.95, 0.16749, 48, -39.11, 56.14, 0.00023, 3, 46, 81.41, 6.19, 0.06085, 47, 11.1, 14.96, 0.91215, 48, -19.01, 39.38, 0.027, 2, 47, 41.76, 13.45, 0.64461, 48, 1.91, 16.91, 0.35539, 2, 47, 66.62, 17.11, 0.00699, 48, 22.28, 2.19, 0.99301], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 40, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 122, "height": 147}}, "shangao06": {"shangao06": {"type": "mesh", "uvs": [0.83295, 0.0397, 0.94724, 0.13077, 0.99829, 0.23003, 0.95572, 0.45601, 0.88768, 0.63431, 0.7559, 0.84609, 0.5881, 0.95234, 0.42348, 0.99999, 0.21859, 0.98508, 0.10413, 0.94524, 0.04246, 0.87281, 0.00288, 0.76302, 0.00221, 0.58017, 0.0577, 0.33515, 0.1121, 0.25068, 0.20296, 0.15627, 0.35907, 0.09841, 0.50687, 0.04363, 0.69268, 0.00651, 0.86147, 0.23845, 0.67511, 0.29471, 0.49574, 0.41037, 0.31404, 0.53853, 0.14398, 0.67919, 0.11137, 0.48226, 0.24881, 0.33848, 0.39557, 0.26971, 0.57028, 0.16031, 0.75199, 0.11967, 0.85215, 0.12905, 0.19989, 0.84173, 0.37228, 0.75733, 0.55864, 0.64168, 0.74966, 0.51977, 0.86613, 0.47288], "triangles": [11, 12, 23, 10, 11, 23, 12, 13, 24, 24, 13, 14, 23, 24, 22, 12, 24, 23, 30, 23, 31, 10, 23, 30, 9, 30, 8, 9, 10, 30, 8, 30, 31, 8, 31, 7, 26, 16, 17, 26, 25, 15, 26, 15, 16, 14, 15, 25, 22, 25, 26, 22, 26, 21, 24, 25, 22, 22, 21, 32, 31, 22, 32, 23, 22, 31, 31, 32, 6, 7, 31, 6, 24, 14, 25, 28, 18, 0, 1, 29, 0, 28, 0, 29, 27, 17, 18, 27, 18, 28, 19, 29, 1, 19, 1, 2, 20, 27, 28, 19, 20, 28, 19, 28, 29, 3, 19, 2, 34, 19, 3, 20, 19, 34, 33, 20, 34, 4, 34, 3, 26, 17, 27, 21, 26, 27, 21, 27, 20, 21, 20, 33, 33, 34, 4, 32, 21, 33, 5, 33, 4, 32, 33, 5, 6, 32, 5], "vertices": [3, 2, 168.33, 45.02, 0.00038, 3, 94.8, 26.1, 0.99955, 4, -177.33, -49.84, 6e-05, 2, 2, 191.77, 1.94, 1e-05, 3, 108.89, -20.88, 0.99999, 2, 2, 194.02, -31.15, 0.01227, 3, 104.29, -53.73, 0.98773, 3, 2, 148.75, -76.54, 0.23434, 3, 50.66, -88.84, 0.76565, 4, -161.58, 72.27, 1e-05, 3, 2, 102.11, -105.85, 0.60124, 3, -1, -107.95, 0.37749, 4, -115.88, 103.03, 0.02127, 4, 2, 30.55, -131.06, 0.73954, 3, -76.22, -117.91, 0.05073, 4, -45.14, 130.47, 0.20218, 5, -143.4, 122.77, 0.00755, 4, 2, -37.6, -124.53, 0.43448, 3, -141.58, -97.51, 0.00031, 4, 23.18, 126.08, 0.47378, 5, -74.93, 122.14, 0.09143, 3, 2, -96.51, -104.74, 0.2003, 4, 82.68, 108.15, 0.7437, 5, -14.54, 107.51, 0.056, 3, 2, -159.4, -62.58, 0.04585, 4, 146.86, 67.98, 0.94416, 5, 51.75, 70.93, 0.00999, 3, 4, 178.52, 37.95, 0.00179, 5, 85.02, 42.69, 0.87821, 22, 189.11, 9.49, 0.12, 2, 5, 96.84, 14.86, 0.008, 22, 175.04, -17.27, 0.992, 1, 22, 149.1, -38.5, 1, 2, 4, 162.85, -68.1, 0.10705, 22, 100.29, -50.43, 0.89295, 3, 2, -119.09, 121.39, 0.01825, 4, 112.33, -117.16, 0.104, 22, 30.08, -46.18, 0.87775, 4, 2, -89.96, 131.09, 0.12687, 3, -140.29, 163.41, 0.00028, 4, 83.52, -127.78, 0.192, 22, 2.87, -32.05, 0.68085, 4, 2, -47.87, 136.28, 0.11527, 3, -98.03, 159.83, 0.00671, 4, 41.62, -134.27, 0.63323, 5, -42.21, -136.8, 0.2448, 4, 2, 9.77, 120.51, 0.33681, 3, -44.86, 132.56, 0.08043, 4, -16.49, -120.32, 0.51651, 5, -101, -126.07, 0.06626, 4, 2, 64.35, 105.59, 0.43769, 3, 5.49, 106.74, 0.34605, 4, -71.51, -107.12, 0.21071, 5, -156.66, -115.91, 0.00555, 3, 2, 128.5, 79.32, 0.10384, 3, 62.87, 67.85, 0.87575, 4, -136.45, -82.87, 0.02041, 2, 2, 149.44, -7.34, 0.01008, 3, 65.55, -21.26, 0.98992, 3, 2, 82.42, 14.51, 0.34678, 3, 4.46, 13.89, 0.64837, 4, -92.43, -16.65, 0.00484, 4, 2, 9.28, 21, 0.71836, 3, -65.79, 35.27, 0.00723, 4, -19.11, -20.84, 0.27332, 5, -109.09, -26.88, 0.00108, 3, 2, -66.36, 24.97, 0.00109, 4, 56.62, -22.45, 0.93306, 5, -33.38, -24.32, 0.06585, 2, 4, 130.25, -18.96, 0.01028, 5, 39.94, -16.79, 0.98972, 4, 2, -122.74, 76.49, 0.00139, 4, 114.57, -72.17, 0.22151, 5, 27.22, -70.78, 0.58511, 22, 64.78, -17.51, 0.192, 4, 2, -58.94, 84.56, 0.07313, 3, -119.49, 111.49, 0.00239, 4, 51.06, -82.24, 0.67033, 5, -35.65, -84.32, 0.25416, 4, 2, -2.72, 73.14, 0.34647, 3, -66.82, 88.76, 0.04587, 4, -5.48, -72.58, 0.5598, 5, -92.63, -77.79, 0.04786, 4, 2, 68.07, 66.05, 0.46337, 3, 1, 67.28, 0.41722, 4, -76.46, -67.71, 0.11853, 5, -163.77, -76.84, 0.00088, 3, 2, 131.41, 41.39, 0.02175, 3, 57.92, 30.13, 0.9746, 4, -140.54, -45.05, 0.00365, 1, 3, 83.38, 3.21, 1, 3, 2, -145.18, -25.17, 0.00064, 4, 133.82, 30.14, 0.03689, 5, 40.81, 32.43, 0.96247, 3, 2, -78.65, -37.73, 0.04625, 4, 66.93, 40.61, 0.71275, 5, -26.56, 39.21, 0.24099, 4, 2, -3.28, -45.54, 0.61528, 3, -91.76, -27.26, 0.00047, 4, -8.64, 46.05, 0.37458, 5, -102.31, 40.49, 0.00968, 3, 2, 74.44, -52.75, 0.71859, 3, -17.18, -50.29, 0.26791, 4, -86.55, 50.82, 0.0135, 3, 2, 117.97, -63.63, 0.35973, 3, 23.19, -69.89, 0.63914, 4, -130.4, 60.34, 0.00112], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 4, 38, 38, 40, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 30, 32, 32, 34, 18, 60, 60, 62, 62, 64, 64, 66, 66, 68], "width": 369, "height": 275}}, "shangao07": {"shangao07": {"type": "mesh", "uvs": [0.0648, 0.35346, 0.12206, 0.39467, 0.16352, 0.43213, 0.20301, 0.53951, 0.25829, 0.65938, 0.33134, 0.77675, 0.45375, 0.90911, 0.58011, 0.98403, 0.74793, 1, 0.88416, 0.99527, 1, 0.97903, 1, 0.93158, 0.93747, 0.85042, 0.85454, 0.76302, 0.81308, 0.66188, 0.78939, 0.54201, 0.76767, 0.40341, 0.74595, 0.2698, 0.71437, 0.1362, 0.64724, 0.02756, 0.53075, 0, 0.33529, 0, 0.10034, 0, 0, 0.07751, 0, 0.21736, 0.331, 0.10158, 0.36619, 0.24399, 0.43304, 0.37083, 0.50693, 0.53105, 0.57027, 0.67569, 0.67582, 0.80698, 0.80601, 0.90044], "triangles": [9, 8, 31, 8, 7, 31, 10, 9, 11, 7, 30, 31, 7, 6, 30, 9, 31, 11, 31, 12, 11, 6, 5, 30, 31, 13, 12, 31, 30, 13, 30, 14, 13, 5, 29, 30, 30, 29, 14, 5, 4, 29, 4, 28, 29, 29, 15, 14, 29, 28, 15, 4, 3, 28, 28, 16, 15, 28, 3, 27, 3, 2, 27, 28, 27, 16, 2, 1, 27, 27, 17, 16, 27, 1, 26, 1, 0, 26, 17, 26, 18, 20, 19, 18, 26, 17, 27, 0, 24, 26, 20, 18, 25, 24, 25, 26, 25, 18, 26, 20, 25, 21, 25, 23, 22, 25, 24, 23, 25, 22, 21], "vertices": [1, 37, 44.35, -29.33, 1, 1, 37, 51.94, -22.7, 1, 1, 37, 58.83, -17.91, 1, 2, 37, 78.5, -13.47, 0.76049, 38, -8.11, -10.85, 0.23951, 1, 38, 13.79, -18.35, 1, 2, 38, 36.54, -23.9, 0.96604, 39, -24.86, -20.04, 0.03396, 2, 38, 64.97, -26.34, 0.28919, 39, 2.77, -27.18, 0.71081, 2, 38, 85.01, -22.3, 0.01258, 39, 23.2, -26.53, 0.98742, 1, 39, 39.4, -14.8, 1, 1, 39, 50.23, -2.98, 1, 1, 39, 57.84, 8.66, 1, 1, 39, 51.62, 14.83, 1, 2, 38, 89.58, 25.92, 0.00269, 39, 35.74, 20.24, 0.99731, 3, 37, 120.02, 62.38, 0.00303, 38, 70.66, 27.33, 0.17174, 39, 17.32, 24.79, 0.82522, 3, 37, 101.49, 57.7, 0.05446, 38, 52.54, 34.15, 0.62946, 39, 0.59, 34.54, 0.31607, 3, 37, 79.57, 55.13, 0.27735, 38, 32.82, 44.66, 0.67785, 39, -17.1, 48.18, 0.0448, 3, 37, 54.22, 52.82, 0.66667, 38, 10.42, 57.35, 0.33298, 39, -37.08, 64.42, 0.00035, 2, 37, 29.78, 50.5, 0.90501, 38, -11.23, 69.5, 0.09499, 2, 37, 5.33, 47.03, 0.98665, 38, -33.57, 80.72, 0.01335, 2, 37, -14.59, 39.36, 0.99965, 38, -54.59, 85.89, 0.00035, 1, 37, -19.75, 25.77, 1, 1, 37, -19.95, 2.9, 1, 1, 37, -20.2, -24.58, 1, 1, 37, -6.14, -36.45, 1, 1, 37, 19.42, -36.69, 1, 1, 37, -1.39, 2.23, 1, 2, 37, 24.67, 6.11, 0.99796, 38, -41.39, 36.12, 0.00204, 2, 37, 47.91, 13.72, 0.94517, 38, -17.64, 28.98, 0.05483, 3, 37, 77.27, 22.1, 0.35278, 38, 11.63, 18.96, 0.64683, 39, -42.28, 26.37, 0.00039, 3, 37, 103.76, 29.27, 0.01883, 38, 37.83, 9.59, 0.95452, 39, -18.01, 12.77, 0.02665, 3, 37, 127.86, 41.4, 7e-05, 38, 64.93, 5.65, 0.03887, 39, 8.06, 4.37, 0.96106, 1, 39, 31.24, 2.92, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 42, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 117, "height": 185}}, "shangao08": {"shangao08": {"type": "mesh", "uvs": [0.03572, 0.37322, 0.07616, 0.24567, 0.1644, 0.1375, 0.26121, 0.04975, 0.31636, 0, 0.45484, 0, 0.57616, 0.05791, 0.69013, 0.1926, 0.75876, 0.34363, 0.77469, 0.47832, 0.76734, 0.59158, 0.7416, 0.66811, 0.7269, 0.70383, 0.79185, 0.72526, 0.8617, 0.76403, 0.89724, 0.79975, 0.95484, 0.85281, 0.99405, 0.91403, 0.99283, 0.95179, 0.94258, 0.98138, 0.89234, 1, 0.79258, 1, 0.67615, 0.98789, 0.62262, 0.96895, 0.51289, 0.96003, 0.38442, 0.94777, 0.23453, 0.911, 0.11945, 0.84526, 0.04584, 0.75055, 0.00436, 0.65137, 0, 0.52992, 0.01239, 0.43743, 0.4015, 0.08674, 0.32127, 0.18927, 0.26903, 0.30422, 0.22799, 0.43627, 0.20746, 0.55433, 0.23545, 0.69104, 0.37351, 0.78736, 0.55821, 0.81376, 0.69254, 0.86503, 0.79329, 0.92872, 0.85859, 0.9629, 0.56754, 0.26694, 0.52277, 0.34928, 0.47986, 0.47666, 0.45, 0.60094, 0.53209, 0.69415, 0.66269, 0.7392, 0.7709, 0.80444, 0.87911, 0.87124, 0.93882, 0.91785], "triangles": [21, 42, 20, 20, 42, 19, 21, 41, 42, 21, 22, 41, 41, 22, 40, 42, 51, 19, 19, 51, 18, 22, 23, 40, 42, 50, 51, 42, 41, 50, 18, 51, 17, 40, 49, 41, 41, 49, 50, 51, 16, 17, 51, 50, 16, 50, 49, 15, 50, 15, 16, 15, 49, 14, 40, 48, 49, 49, 13, 14, 49, 12, 13, 40, 23, 39, 23, 24, 39, 24, 25, 39, 26, 38, 25, 25, 38, 39, 38, 26, 37, 39, 48, 40, 26, 27, 37, 38, 47, 39, 39, 47, 48, 48, 12, 49, 38, 46, 47, 38, 37, 46, 12, 48, 11, 11, 48, 47, 47, 46, 11, 46, 45, 11, 11, 45, 9, 10, 11, 9, 27, 28, 37, 28, 29, 37, 29, 36, 37, 37, 36, 46, 44, 9, 45, 29, 30, 36, 46, 36, 45, 8, 9, 44, 30, 31, 36, 36, 35, 45, 36, 31, 35, 35, 34, 45, 45, 34, 44, 31, 0, 35, 35, 0, 34, 0, 1, 34, 44, 43, 8, 43, 44, 33, 43, 7, 8, 1, 2, 34, 44, 34, 33, 34, 2, 33, 33, 32, 43, 43, 6, 7, 43, 32, 6, 2, 3, 33, 33, 3, 32, 3, 4, 32, 32, 5, 6, 32, 4, 5], "vertices": [1, 22, 79.13, -48.84, 1, 1, 22, 46.85, -48.08, 1, 1, 22, 16.92, -36.74, 1, 1, 22, -8.56, -22.53, 1, 1, 22, -23.01, -14.42, 1, 2, 22, -29.58, 13.06, 0.99649, 23, 56.03, 195.44, 0.00351, 2, 22, -21.54, 40.43, 0.97223, 23, 80.77, 181.68, 0.02777, 2, 22, 5.13, 70.71, 0.87001, 23, 104.34, 149.11, 0.12999, 2, 22, 37.82, 92.93, 0.68906, 23, 118.82, 112.38, 0.31094, 2, 22, 69.13, 103.76, 0.49525, 23, 122.6, 79.47, 0.50475, 3, 22, 96.45, 108.76, 0.31647, 23, 121.59, 51.72, 0.67304, 24, -25.65, 46.72, 0.01049, 3, 22, 115.88, 108.01, 0.17779, 23, 116.71, 32.91, 0.72876, 24, -16.47, 29.58, 0.09345, 3, 22, 125.08, 107.13, 0.08572, 23, 113.89, 24.12, 0.62647, 24, -12.58, 21.21, 0.28781, 3, 22, 127.11, 121.24, 0.01353, 23, 127.09, 19.09, 0.17354, 24, 0.53, 26.48, 0.81293, 3, 22, 133.03, 137.3, 0.00035, 23, 141.36, 9.84, 0.00415, 24, 17.29, 29.36, 0.9955, 1, 24, 28.52, 27.99, 1, 1, 24, 45.9, 26.64, 1, 1, 24, 61.98, 21.27, 1, 1, 24, 68.18, 14.42, 1, 1, 24, 65.85, 2.17, 1, 1, 24, 61.66, -8.13, 1, 2, 23, 128.38, -48.14, 0.02995, 24, 47.09, -22.05, 0.97005, 2, 23, 104.83, -45.58, 0.27208, 24, 28.04, -36.14, 0.72792, 2, 23, 93.94, -41.12, 0.50425, 24, 17.02, -40.25, 0.49575, 2, 23, 71.75, -39.32, 0.87564, 24, -0.51, -53.98, 0.12436, 2, 23, 45.76, -36.76, 0.99287, 24, -21.34, -69.73, 0.00713, 1, 23, 15.34, -28.27, 1, 2, 22, 187.53, -5.34, 0.36786, 23, -8.17, -12.57, 0.63214, 1, 22, 168.48, -25.34, 1, 1, 22, 146.83, -39.22, 1, 1, 22, 118.13, -47, 1, 1, 22, 95.52, -49.81, 1, 2, 22, -6.4, 7.41, 0.99751, 23, 45.62, 174.03, 0.00249, 1, 22, 21.81, -2.66, 1, 1, 22, 51.65, -6.48, 1, 1, 22, 85.03, -7.1, 1, 1, 22, 114.11, -4.45, 1, 2, 22, 145.32, 8.89, 0.84656, 23, 14.61, 25.57, 0.15344, 2, 22, 161.71, 41.77, 0.0124, 23, 42.89, 2.47, 0.9876, 2, 23, 80.29, -3.37, 0.99104, 24, -18.62, -21.77, 0.00896, 2, 23, 107.63, -15.45, 0.29656, 24, 9.66, -12.11, 0.70344, 2, 23, 128.23, -30.7, 0.0209, 24, 35.14, -9.33, 0.9791, 2, 23, 141.56, -38.84, 0.00074, 24, 50.46, -6.27, 0.99926, 2, 22, 28.63, 50.62, 0.86662, 23, 79.9, 130.5, 0.13338, 2, 22, 50.35, 46.43, 0.82299, 23, 71.2, 110.19, 0.17701, 2, 22, 82.71, 45.17, 0.70682, 23, 63.07, 78.87, 0.29318, 2, 22, 113.71, 46.33, 0.49916, 23, 57.56, 48.35, 0.50084, 2, 22, 132.01, 67.93, 0.15668, 23, 74.52, 25.82, 0.84332, 3, 22, 136.54, 96.4, 0.04712, 23, 101.07, 15.24, 0.8666, 24, -15.97, 6, 0.08628, 2, 22, 146.95, 121.59, 0.00071, 24, 10.86, 9.54, 0.99929, 1, 24, 37.96, 12.81, 1, 1, 24, 54.56, 12.89, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 10, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 14, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102], "width": 204, "height": 245}}, "shangao09": {"shangao09": {"type": "mesh", "uvs": [0.00015, 0.76122, 0.02569, 0.61911, 0.13878, 0.47544, 0.30658, 0.35832, 0.48169, 0.26618, 0.63855, 0.16311, 0.77535, 0.06004, 0.86108, 0, 0.92857, 0, 0.94681, 0.0335, 0.94498, 0.17717, 0.96505, 0.34582, 0.99606, 0.52541, 1, 0.72686, 0.9614, 0.8924, 0.86108, 1, 0.64109, 0.97598, 0.41705, 0.92614, 0.18239, 0.86368, 0.9035, 0.02209, 0.79331, 0.16733, 0.68892, 0.3225, 0.54103, 0.52981, 0.45839, 0.76691, 0.71357, 0.7967, 0.78461, 0.54843, 0.84116, 0.33492, 0.889, 0.17105, 0.24235, 0.67257, 0.35254, 0.49257, 0.55698, 0.29519, 0.72081, 0.15989], "triangles": [0, 1, 28, 14, 15, 24, 18, 0, 28, 17, 18, 23, 15, 16, 24, 17, 23, 16, 16, 23, 24, 22, 21, 25, 25, 12, 13, 19, 7, 8, 19, 8, 9, 31, 6, 7, 31, 5, 6, 20, 31, 7, 20, 7, 19, 27, 20, 19, 9, 27, 19, 10, 27, 9, 30, 4, 5, 30, 5, 31, 21, 30, 31, 21, 31, 20, 26, 20, 27, 21, 20, 26, 26, 27, 10, 26, 10, 11, 29, 4, 30, 22, 30, 21, 25, 21, 26, 12, 25, 26, 12, 26, 11, 29, 3, 4, 2, 3, 29, 22, 29, 30, 28, 2, 29, 1, 2, 28, 23, 29, 22, 28, 29, 23, 24, 22, 25, 24, 25, 13, 23, 22, 24, 18, 28, 23, 14, 24, 13], "vertices": [3, 10, -19.76, 49.13, 0.31217, 11, -72.34, 55.94, 0.00383, 3, 118.94, 132.65, 0.684, 3, 10, -0.2, 56.76, 0.59647, 11, -52.17, 61.75, 0.03819, 3, 135.33, 145.76, 0.36533, 3, 10, 25.03, 55.03, 0.71013, 11, -27.19, 57.73, 0.15743, 3, 159.93, 151.63, 0.13244, 3, 10, 50.34, 45.43, 0.61065, 11, -2.86, 45.88, 0.37868, 3, 186.95, 150.03, 0.01067, 2, 10, 72.94, 33.22, 0.34714, 11, 18.54, 31.67, 0.65286, 2, 10, 95.78, 23.78, 0.13347, 11, 40.43, 20.19, 0.86653, 2, 10, 117.37, 16.51, 0.02588, 11, 61.27, 10.99, 0.97412, 2, 10, 130.32, 11.63, 0.01063, 11, 73.72, 4.95, 0.98937, 1, 11, 77.27, -2.71, 1, 2, 10, 131.46, -0.1, 0.00341, 11, 73.79, -6.83, 0.99659, 2, 10, 113.19, -10.4, 0.04899, 11, 54.65, -15.43, 0.95101, 3, 10, 93.13, -24.9, 0.17303, 11, 33.36, -28.05, 0.81186, 3, 248.77, 95.66, 0.01511, 3, 10, 72.37, -41.39, 0.37902, 11, 11.19, -42.58, 0.57209, 3, 233.88, 73.74, 0.04889, 3, 10, 47.16, -56.54, 0.50069, 11, -15.29, -55.38, 0.30553, 3, 214.34, 51.75, 0.19378, 3, 10, 23.82, -64.46, 0.46542, 11, -39.25, -61.15, 0.11503, 3, 194.43, 37.22, 0.41956, 1, 3, 174.56, 34.15, 1, 1, 3, 156.69, 55.34, 1, 1, 3, 141, 79.65, 1, 3, 10, -21.3, 21.93, 0.18942, 11, -76.34, 28.98, 0.00624, 3, 125.58, 106.22, 0.80433, 2, 10, 130.19, 5.42, 0.00021, 11, 73.02, -1.22, 0.99979, 2, 10, 104.94, 6.73, 0.10884, 11, 47.99, 2.38, 0.89116, 3, 10, 78.79, 6.68, 0.30903, 11, 21.95, 4.7, 0.67141, 3, 225.67, 121.53, 0.01956, 3, 10, 43.34, 7.53, 0.60879, 11, -13.28, 8.77, 0.34232, 3, 191.57, 111.76, 0.04889, 3, 10, 8.2, -0.86, 0.75023, 11, -49.03, 3.61, 0.17643, 3, 160.54, 93.27, 0.07333, 3, 10, 20.41, -30.65, 0.37065, 11, -39.58, -27.17, 0.20958, 3, 181.08, 68.48, 0.41978, 3, 10, 56.23, -20.19, 0.36886, 11, -2.96, -20, 0.47736, 3, 212.15, 89.15, 0.15378, 3, 10, 86.75, -10.7, 0.21815, 11, 28.3, -13.33, 0.76052, 3, 238.45, 107.32, 0.02133, 2, 10, 110.45, -3.9, 0.0983, 11, 52.52, -8.71, 0.9017, 3, 10, 6.6, 29.41, 0.49026, 11, -47.87, 33.9, 0.12174, 3, 149.98, 121.68, 0.388, 3, 10, 36.25, 30.64, 0.52728, 11, -18.24, 32.43, 0.34205, 3, 177.91, 131.71, 0.13067, 3, 10, 73.99, 22.95, 0.34088, 11, 18.65, 21.35, 0.64934, 3, 216.22, 135.63, 0.00978, 2, 10, 101.34, 15.12, 0.12677, 11, 45.17, 11.06, 0.87323], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 16, 38, 38, 40, 40, 42, 42, 44, 44, 46, 32, 48, 48, 50, 50, 52, 52, 54, 36, 56, 56, 58, 58, 60, 60, 62, 62, 14], "width": 125, "height": 146}}, "shangao012": {"shangao012": {"type": "mesh", "uvs": [0.62815, 0.16376, 0.70004, 0.17551, 0.78003, 0.21207, 0.85013, 0.27213, 0.89416, 0.36222, 0.94629, 0.42881, 0.98403, 0.53195, 0.99751, 0.65991, 0.98403, 0.76697, 0.94204, 0.84576, 0.89183, 0.90906, 0.82224, 0.96532, 0.73055, 0.99205, 0.6417, 0.99689, 0.55529, 0.97178, 0.47986, 0.92626, 0.38909, 0.84531, 0.33427, 0.73694, 0.33068, 0.62204, 0.33966, 0.55023, 0.28215, 0.53979, 0.21348, 0.48621, 0.14251, 0.40164, 0.09308, 0.29853, 0.06915, 0.21165, 0.0253, 0.12128, 0.00456, 0.04714, 0, 0, 0.03088, 0, 0.09467, 0.01586, 0.15368, 0.03903, 0.22236, 0.05069, 0.28563, 0.05331, 0.34213, 0.06644, 0.39546, 0.09468, 0.44833, 0.13276, 0.48087, 0.18332, 0.55469, 0.1662, 0.67767, 0.25396, 0.73648, 0.39377, 0.77818, 0.52582, 0.79208, 0.68582, 0.77711, 0.86447, 0.74824, 0.94059, 0.62421, 0.41241, 0.49268, 0.46367, 0.84555, 0.39377, 0.75252, 0.24464, 0.80491, 0.31765, 0.5825, 0.27726, 0.42639, 0.24308, 0.37934, 0.34561, 0.35475, 0.44193, 0.02592, 0.03654, 0.07725, 0.08936, 0.12857, 0.14062, 0.2077, 0.17325, 0.31783, 0.24626, 0.25795, 0.39073, 0.16493, 0.27888, 0.09008, 0.17325, 0.04945, 0.10489, 0.35954, 0.16703, 0.29645, 0.11887, 0.21839, 0.09713, 0.14782, 0.0847, 0.08901, 0.05829, 0.47823, 0.31616, 0.40231, 0.50413, 0.6461, 0.55229, 0.49854, 0.602, 0.40231, 0.6657, 0.8653, 0.52899, 0.94015, 0.55229, 0.88776, 0.67968, 0.95619, 0.70453, 0.66642, 0.71075, 0.51458, 0.76978, 0.43332, 0.81638, 0.66107, 0.87231, 0.53704, 0.87697, 0.87172, 0.84745, 0.94015, 0.8024, 0.8482, 0.90804, 0.64503, 0.936, 0.56163, 0.92202], "triangles": [19, 52, 68, 19, 68, 71, 45, 67, 49, 49, 67, 36, 18, 19, 71, 68, 45, 70, 49, 36, 37, 49, 37, 0, 13, 14, 84, 13, 84, 12, 84, 14, 85, 12, 43, 11, 12, 84, 43, 14, 15, 85, 85, 15, 80, 11, 83, 10, 11, 43, 83, 43, 42, 83, 84, 79, 43, 43, 79, 42, 84, 85, 79, 16, 78, 15, 15, 78, 80, 85, 80, 79, 83, 81, 10, 10, 81, 9, 83, 42, 81, 78, 77, 80, 80, 77, 79, 79, 76, 42, 79, 77, 76, 42, 41, 81, 42, 76, 41, 81, 82, 9, 81, 74, 82, 81, 41, 74, 9, 82, 8, 16, 17, 78, 17, 71, 78, 78, 71, 77, 82, 75, 8, 82, 74, 75, 71, 70, 77, 77, 70, 76, 8, 75, 7, 70, 69, 76, 41, 76, 40, 7, 75, 73, 76, 69, 40, 41, 72, 74, 41, 40, 72, 75, 74, 73, 74, 72, 73, 73, 6, 7, 70, 45, 69, 45, 44, 69, 69, 39, 40, 69, 44, 39, 6, 73, 5, 73, 72, 5, 40, 46, 72, 5, 46, 4, 5, 72, 46, 40, 39, 46, 44, 38, 39, 39, 48, 46, 39, 47, 48, 39, 38, 47, 46, 3, 4, 46, 48, 3, 47, 2, 48, 48, 2, 3, 38, 1, 47, 47, 1, 2, 45, 49, 44, 44, 49, 38, 49, 0, 38, 38, 0, 1, 71, 68, 70, 17, 18, 71, 67, 50, 36, 51, 67, 45, 45, 68, 51, 59, 23, 60, 23, 24, 60, 60, 55, 59, 59, 55, 56, 24, 25, 60, 60, 25, 61, 55, 65, 56, 56, 65, 64, 61, 54, 60, 60, 54, 55, 54, 66, 55, 55, 66, 65, 25, 53, 61, 25, 26, 53, 61, 53, 54, 65, 30, 64, 64, 30, 31, 54, 53, 66, 65, 66, 30, 29, 66, 28, 66, 29, 30, 28, 66, 53, 26, 27, 53, 53, 27, 28, 19, 20, 52, 21, 58, 20, 20, 58, 52, 21, 22, 58, 68, 52, 51, 52, 58, 51, 22, 59, 58, 22, 23, 59, 58, 59, 57, 58, 57, 51, 57, 59, 56, 51, 50, 67, 51, 57, 50, 57, 56, 63, 56, 64, 63, 57, 62, 50, 57, 63, 62, 50, 62, 35, 50, 35, 36, 35, 62, 34, 63, 33, 62, 62, 33, 34, 63, 64, 32, 64, 31, 32, 63, 32, 33], "vertices": [2, 3, 139.05, 151.84, 0.7, 9, 15.62, 87.55, 0.3, 2, 3, 157.32, 130.82, 0.7, 9, 33.89, 66.52, 0.3, 2, 3, 173.44, 102.84, 0.7, 9, 50, 38.55, 0.3, 2, 3, 182.53, 72.86, 0.7, 9, 59.1, 8.56, 0.3, 2, 3, 178.86, 43.81, 0.7, 9, 55.42, -20.48, 0.3, 2, 3, 181.69, 17.24, 0.7, 9, 58.25, -47.06, 0.3, 2, 3, 173.88, -12.72, 0.7, 9, 50.45, -77.01, 0.3, 2, 3, 154.76, -41.19, 0.7, 9, 31.32, -105.49, 0.3, 2, 3, 131.73, -58.57, 0.7, 9, 8.3, -122.86, 0.3, 2, 3, 105.7, -63, 0.7, 9, -17.73, -127.29, 0.3, 2, 3, 80.12, -62.26, 0.7, 9, -43.32, -126.56, 0.3, 2, 3, 50.3, -55.1, 0.7, 9, -73.13, -119.4, 0.3, 2, 3, 19.52, -36.43, 0.7, 9, -103.91, -100.72, 0.3, 2, 3, -6.53, -14.22, 0.7, 9, -129.96, -78.52, 0.3, 2, 3, -26.52, 13.19, 0.7, 9, -149.95, -51.1, 0.3, 2, 3, -39.73, 41.73, 0.7, 9, -163.16, -22.57, 0.3, 2, 3, -50.94, 81.17, 0.7, 9, -174.37, 16.88, 0.3, 2, 3, -47.04, 116.6, 0.7, 9, -170.47, 52.3, 0.3, 2, 3, -27.45, 139.95, 0.7, 9, -150.88, 75.65, 0.3, 3, 6, -16.69, 61.8, 0.0896, 3, -12.02, 151.62, 0.6104, 9, -135.46, 87.32, 0.3, 3, 6, 3.9, 70.4, 0.93885, 7, -76.18, 69.02, 0.00118, 3, -26.45, 168.64, 0.05997, 3, 6, 33.9, 71.22, 0.95319, 7, -46.2, 70.38, 0.03681, 3, -36.3, 196.98, 0.01, 2, 6, 68.74, 65.35, 0.76096, 7, -11.26, 65.15, 0.23904, 2, 6, 98.84, 51.09, 0.34594, 7, 19.1, 51.45, 0.65406, 2, 6, 118.27, 35.69, 0.06474, 7, 38.81, 36.41, 0.93526, 1, 7, 65.58, 24.5, 1, 1, 7, 82.51, 11.73, 1, 1, 7, 90.42, 1.9, 1, 1, 7, 80.22, -4.19, 1, 1, 7, 56.98, -13.18, 1, 2, 6, 112.76, -20.18, 0.00651, 7, 34.32, -19.55, 0.99349, 2, 6, 88.28, -30.64, 0.4025, 7, 10.04, -30.46, 0.5975, 2, 6, 66.79, -42.14, 0.88007, 7, -11.23, -42.35, 0.11993, 2, 6, 46.19, -49.93, 0.99095, 7, -31.69, -50.52, 0.00905, 2, 6, 24.65, -53.64, 0.99807, 3, 85.49, 225.95, 0.00193, 2, 6, 1.97, -54.99, 0.97689, 3, 93.65, 204.74, 0.02311, 3, 6, -15.56, -49.59, 0.0896, 3, 93.8, 186.4, 0.6104, 9, -29.63, 122.1, 0.3, 2, 3, 117.8, 170.51, 0.7, 9, -5.63, 106.21, 0.3, 2, 3, 136.91, 121.34, 0.7, 8, 39.35, 118.95, 0.3, 2, 3, 128.51, 78.74, 0.7, 8, 30.94, 76.35, 0.3, 2, 3, 116.64, 42.12, 0.7, 8, 19.08, 39.72, 0.3, 2, 3, 91.88, 7.28, 0.7, 8, -5.68, 4.89, 0.3, 2, 3, 55.6, -23.67, 0.7, 8, -41.96, -26.06, 0.3, 2, 3, 33.77, -31, 0.7, 8, -63.8, -33.39, 0.3, 2, 3, 93.34, 104.36, 0.75, 8, -4.22, 101.97, 0.25, 2, 3, 46.87, 128.63, 0.83, 8, -50.69, 126.24, 0.17, 2, 3, 159.42, 50.32, 0.8, 8, 61.85, 47.93, 0.2, 3, 3, 159.8, 103.66, 0.7744, 8, 62.24, 101.26, 0.1936, 9, 36.37, 39.36, 0.032, 2, 3, 161.55, 75.76, 0.8, 8, 63.99, 73.37, 0.2, 2, 3, 105.76, 141.59, 0.75, 8, 8.2, 139.2, 0.25, 3, 6, -5.22, -25.42, 0.56717, 3, 67.64, 188.94, 0.13283, 9, -55.79, 124.64, 0.3, 2, 6, -3.01, 7.16, 0.98518, 3, 35.92, 181.2, 0.01482, 3, 6, -7.47, 34.01, 0.54414, 3, 11.68, 168.81, 0.15586, 9, -111.76, 104.52, 0.3, 1, 7, 76.89, 5.1, 1, 1, 7, 52.75, 6.99, 1, 2, 6, 107.77, 7.99, 0.00257, 7, 28.82, 8.52, 0.99743, 2, 6, 77.04, 0.35, 0.70516, 7, -1.77, 0.32, 0.29484, 1, 6, 30.63, -3.93, 1, 3, 6, 31.61, 40.75, 0.97399, 7, -47.93, 39.88, 0.01954, 3, -6.58, 204.02, 0.00647, 2, 6, 77.42, 32.83, 0.57894, 7, -1.98, 32.8, 0.42106, 2, 6, 116.34, 22.85, 0.02656, 7, 37.11, 23.54, 0.97344, 1, 7, 59.83, 16.01, 1, 2, 6, 27.13, -30.12, 0.99985, 3, 62.34, 221.2, 0.00015, 2, 6, 54.55, -29.13, 0.96126, 7, -23.72, -29.57, 0.03874, 2, 6, 83.49, -19.2, 0.4113, 7, 5.04, -19.11, 0.5887, 2, 6, 108.7, -8.56, 0.0013, 7, 30.06, -8.01, 0.9987, 1, 7, 53.08, -2.41, 1, 3, 6, -32.17, -18.53, 0.02656, 3, 69.23, 161.17, 0.80344, 8, -28.33, 158.78, 0.17, 2, 3, 14, 144.29, 0.9, 8, -83.56, 141.89, 0.1, 2, 3, 74.46, 71.37, 0.75, 8, -23.1, 68.97, 0.25, 2, 3, 23.72, 100.12, 0.83, 8, -73.84, 97.73, 0.17, 2, 3, -14.98, 112.77, 0.9, 8, -112.54, 110.38, 0.1, 2, 3, 140.77, 18.8, 0.8, 8, 43.2, 16.4, 0.2, 2, 3, 157.8, -5.25, 0.888, 8, 60.24, -7.65, 0.112, 2, 3, 120.1, -16.45, 0.8, 8, 22.54, -18.84, 0.2, 2, 3, 135.04, -39.13, 0.888, 8, 37.48, -41.53, 0.112, 2, 3, 51.8, 35.16, 0.75, 8, -45.76, 32.77, 0.25, 2, 3, -1.82, 63.21, 0.83, 8, -99.39, 60.82, 0.17, 2, 3, -33.21, 75.29, 0.9, 8, -130.78, 72.9, 0.1, 2, 3, 21.31, 5.04, 0.75, 8, -76.26, 2.64, 0.25, 2, 3, -14.68, 36.45, 0.83, 8, -112.25, 34.05, 0.17, 2, 3, 85.47, -45, 0.832, 8, -12.09, -47.4, 0.168, 3, 3, 112.94, -54.05, 0.75265, 8, 15.38, -56.44, 0.08735, 9, -10.49, -118.34, 0.15999, 3, 3, 67.93, -50.69, 0.83826, 8, -29.63, -53.09, 0.10573, 9, -55.5, -114.99, 0.05601, 2, 3, 5.34, -3.21, 0.75, 8, -92.22, -5.6, 0.25, 2, 3, -15.79, 21.25, 0.928, 9, -139.22, -43.05, 0.072], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 0, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 22, 24, 24, 26, 78, 88, 88, 90, 78, 92, 2, 94, 94, 96, 74, 98, 72, 100, 100, 102, 102, 104, 54, 106, 106, 108, 108, 110, 110, 112, 112, 114, 104, 116, 116, 118, 118, 120, 120, 122, 100, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 56, 98, 134, 90, 136, 80, 138, 138, 140, 140, 142, 80, 144, 144, 146, 82, 148, 148, 150, 82, 152, 152, 154, 154, 156, 84, 158, 158, 160, 84, 162, 162, 164, 86, 166, 86, 168, 168, 170], "width": 385, "height": 265}}, "shangao013": {"shangao013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 12, 23.1, -48.88, 1, 2, 12, -70.4, 37.07, 0.76, 9, -67.44, 69.36, 0.24, 1, 12, -38.59, 71.67, 1, 2, 12, 54.91, -14.28, 0.76, 9, 57.87, 18, 0.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 127, "height": 47}, "shangao014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 12, 21.01, -49.68, 1, 2, 9, -71, 69.92, 0.288, 12, -73.96, 37.63, 0.712, 1, 12, -40.79, 73.7, 1, 2, 9, 57.13, 18.68, 0.288, 12, 54.17, -13.6, 0.712], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 49}}, "shangao010": {"shangao010": {"type": "mesh", "uvs": [0, 0.14237, 0.06537, 0.08389, 0.19907, 0.03953, 0.35282, 0.00929, 0.54935, 0.00929, 0.70176, 0.03147, 0.82075, 0.18874, 0.88359, 0.44483, 0.97049, 0.76342, 1, 1, 0.89027, 0.99328, 0.67636, 0.89045, 0.45175, 0.71301, 0.23115, 0.49927, 0.04264, 0.2573, 0.07771, 0.16653, 0.23117, 0.22744, 0.47925, 0.2988, 0.71169, 0.38616, 0.70029, 0.65433, 0.46777, 0.45834], "triangles": [15, 1, 2, 0, 1, 15, 16, 2, 3, 15, 2, 16, 14, 0, 15, 17, 3, 4, 16, 3, 17, 20, 16, 17, 20, 17, 18, 16, 14, 15, 13, 16, 20, 13, 14, 16, 12, 13, 20, 12, 20, 19, 18, 17, 5, 19, 20, 18, 17, 4, 5, 18, 5, 6, 18, 6, 7, 19, 18, 7, 19, 7, 8, 11, 12, 19, 10, 11, 19, 8, 10, 19, 10, 8, 9], "vertices": [2, 20, 91.62, -4.88, 0.00013, 21, 51.22, -14.77, 0.99987, 2, 20, 85.75, -8.68, 6e-05, 21, 44.74, -17.4, 0.99994, 2, 20, 73.56, -11.86, 0.04664, 21, 32.17, -18.23, 0.95336, 2, 20, 59.5, -14.24, 0.20213, 21, 17.91, -17.93, 0.79787, 2, 20, 41.43, -14.94, 0.46862, 21, 0.04, -15.22, 0.53138, 2, 20, 27.37, -14.13, 0.75299, 21, -13.62, -11.77, 0.24701, 2, 20, 16.06, -4.96, 0.93051, 21, -23.01, -0.65, 0.06949, 2, 20, 9.68, 10.43, 0.98191, 21, -26.38, 15.66, 0.01809, 2, 20, 0.94, 29.54, 0.89904, 21, -31.37, 36.08, 0.10096, 2, 20, -2.32, 43.86, 0.96204, 21, -31.89, 50.75, 0.03796, 2, 20, 7.78, 43.84, 0.87752, 21, -21.97, 48.83, 0.12248, 2, 20, 27.69, 38.32, 0.66927, 21, -3.45, 39.68, 0.33073, 2, 20, 48.75, 28.3, 0.3897, 21, 15.35, 25.88, 0.6103, 2, 20, 69.53, 16.05, 0.15645, 21, 33.46, 9.94, 0.84355, 2, 20, 87.43, 1.97, 0.03274, 21, 48.39, -7.25, 0.96726, 2, 20, 84.42, -3.68, 0.01106, 21, 44.37, -12.24, 0.98894, 2, 20, 70.17, -0.51, 0.16726, 21, 30.97, -6.45, 0.83274, 2, 20, 47.19, 2.96, 0.27885, 21, 9.06, 1.28, 0.72115, 2, 20, 25.62, 7.46, 0.40169, 21, -11.28, 9.75, 0.59831, 2, 20, 26.04, 23.85, 0.63859, 21, -7.79, 25.77, 0.36141, 2, 20, 47.87, 12.72, 0.33171, 21, 11.57, 10.74, 0.66829], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 16, 38, 38, 40, 40, 32], "width": 92, "height": 61}}, "qt1": {"qt1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.04, 47.58, -15.56, -18.83, 62.97, -47.92, 87.57, 18.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 115, "height": 136}}, "qt2": {"qt1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.48, -45.51, -0.71, 22.93, 80.23, 44.44, 98.41, -24.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 115, "height": 136}}, "shouji/tx_baodian2": {"shouji/tx_baodian02": {"width": 50, "height": 100}, "shouji/tx_baodian03": {"width": 50, "height": 100}, "shouji/tx_baodian04": {"width": 50, "height": 100}, "shouji/tx_baodian05": {"width": 50, "height": 100}, "shouji/tx_baodian06": {"width": 50, "height": 100}, "shouji/tx_baodian07": {"width": 50, "height": 100}, "shouji/tx_baodian08": {"width": 50, "height": 100}}, "shangao015": {"shangao015": {"type": "mesh", "uvs": [0.27325, 0.78013, 0.40961, 0.73841, 0.56249, 0.79264, 0.69885, 0.89067, 0.77598, 1, 0.87512, 0.90392, 0.94997, 0.74526, 0.99059, 0.57202, 0.99808, 0.47812, 0.94034, 0.48945, 0.8794, 0.54773, 0.88581, 0.39231, 0.86122, 0.20935, 0.81418, 0.05393, 0.77676, 0, 0.75965, 0.07821, 0.71047, 0.20612, 0.66984, 0.28869, 0.65808, 0.37612, 0.55009, 0.27897, 0.39934, 0.20288, 0.24217, 0.17535, 0.08286, 0.18669, 0, 0.17374, 0.02085, 0.27088, 0.09462, 0.37288, 0.20368, 0.46193, 0.11601, 0.45221, 0.00053, 0.4425, 0.07858, 0.51373, 0.16305, 0.5785, 0.23148, 0.66754, 0.66487, 0.5683, 0.68108, 0.76069, 0.78651, 0.07711, 0.78921, 0.22856, 0.78651, 0.38411, 0.78245, 0.58877, 0.77029, 0.75455, 0.7365, 0.85074, 0.85138, 0.70748, 0.82029, 0.82823, 0.77975, 0.91623, 0.9568, 0.55398, 0.91085, 0.67678, 0.86354, 0.78116, 0.0783, 0.24697, 0.20534, 0.29814, 0.36618, 0.33702, 0.51079, 0.43322, 0.59594, 0.51714, 0.09992, 0.49256, 0.19588, 0.52326, 0.29184, 0.57648, 0.35671, 0.64606, 0.29995, 0.50485, 0.39591, 0.53555, 0.4716, 0.55192, 0.51484, 0.65016, 0.59864, 0.69519], "triangles": [51, 28, 27, 29, 28, 51, 52, 27, 26, 52, 26, 55, 51, 27, 52, 53, 52, 55, 53, 55, 56, 30, 51, 52, 29, 51, 30, 54, 53, 56, 31, 52, 53, 30, 52, 31, 57, 54, 56, 1, 54, 57, 54, 0, 31, 54, 31, 53, 0, 54, 1, 46, 23, 22, 24, 23, 46, 47, 22, 21, 46, 22, 47, 48, 21, 20, 47, 21, 48, 25, 46, 47, 24, 46, 25, 19, 48, 20, 47, 26, 25, 55, 26, 47, 48, 55, 47, 55, 48, 56, 49, 48, 19, 49, 19, 18, 50, 49, 18, 56, 48, 49, 57, 56, 49, 57, 49, 50, 50, 18, 32, 58, 57, 50, 59, 50, 32, 58, 50, 59, 1, 57, 58, 33, 59, 32, 2, 58, 59, 1, 58, 2, 2, 59, 33, 34, 14, 13, 15, 14, 34, 34, 13, 12, 35, 34, 12, 15, 34, 35, 16, 15, 35, 35, 17, 16, 12, 36, 35, 36, 17, 35, 18, 17, 36, 12, 11, 36, 10, 36, 11, 32, 18, 36, 37, 32, 36, 10, 37, 36, 43, 9, 8, 10, 9, 43, 7, 43, 8, 44, 10, 43, 40, 37, 10, 40, 10, 44, 7, 6, 44, 7, 44, 43, 37, 33, 32, 38, 37, 40, 37, 38, 33, 45, 40, 44, 45, 44, 6, 41, 38, 40, 45, 41, 40, 39, 33, 38, 39, 38, 41, 3, 33, 39, 3, 2, 33, 5, 45, 6, 41, 45, 5, 42, 39, 41, 42, 41, 5, 42, 3, 39, 4, 3, 42, 4, 42, 5], "vertices": [5, 15, 10.22, 81.76, 0.01176, 18, 28.33, 45, 0.13698, 19, 9.11, 44.81, 0.00049, 20, 16.18, 19.45, 0.81254, 21, -18.3, 23.31, 0.03822, 3, 15, 15.93, 60.39, 0.1152, 18, 14.93, 27.4, 0.27075, 20, -5.31, 14.24, 0.61405, 3, 15, 11.75, 35.77, 0.32976, 18, -7.09, 15.64, 0.35923, 20, -29.82, 19, 0.31102, 3, 15, 2.81, 13.5, 0.6319, 18, -30.17, 9.06, 0.26432, 20, -51.88, 28.45, 0.10378, 4, 15, -7.89, 0.55, 0.85412, 16, -43.82, 2.29, 0.00767, 18, -46.96, 9.59, 0.13055, 20, -64.58, 39.45, 0.00766, 3, 15, 3.15, -14.56, 0.93622, 16, -33.38, -13.24, 0.04435, 18, -52.15, -8.39, 0.01943, 2, 15, 20.52, -25.41, 0.88744, 16, -16.47, -24.77, 0.11256, 2, 15, 39.07, -30.73, 0.80157, 16, 1.86, -30.83, 0.19843, 2, 15, 48.98, -31.31, 0.72779, 16, 11.75, -31.81, 0.27221, 2, 15, 47.23, -22.22, 0.62534, 16, 10.35, -22.65, 0.37466, 3, 15, 40.52, -12.93, 0.52976, 16, 4.02, -13.1, 0.40498, 17, -31.53, -7.62, 0.06526, 3, 15, 56.88, -12.94, 0.22824, 16, 20.36, -13.76, 0.48662, 17, -15.57, -11.17, 0.28513, 3, 15, 75.81, -7.85, 0.05074, 16, 39.48, -9.43, 0.3549, 17, 4.01, -10.31, 0.59437, 2, 16, 55.63, -1.59, 0.15931, 17, 21.3, -5.46, 0.84069, 3, 16, 61.16, 4.48, 0.04297, 17, 27.82, -0.47, 0.95423, 18, 22.24, -69.38, 0.0028, 3, 16, 52.89, 7.02, 0.13527, 17, 20.13, 3.5, 0.84125, 18, 18.86, -61.41, 0.02348, 4, 15, 74.67, 16.09, 1e-05, 16, 39.29, 14.54, 0.2944, 17, 8.09, 13.32, 0.61245, 18, 15.87, -46.16, 0.09314, 5, 15, 65.61, 22.01, 0.01197, 16, 30.48, 20.81, 0.38382, 17, 0.53, 21.05, 0.33845, 18, 15.01, -35.38, 0.25698, 19, -33.04, -24.91, 0.00877, 5, 15, 56.34, 23.31, 0.06927, 16, 21.26, 22.48, 0.3251, 17, -8.25, 24.33, 0.10185, 18, 10.35, -27.25, 0.41173, 19, -34.35, -15.64, 0.09205, 5, 15, 65.46, 41.07, 0.01197, 16, 31.08, 39.87, 0.19689, 17, 4.51, 39.7, 0.02694, 18, 29.98, -23.58, 0.41639, 19, -14.76, -19.5, 0.34781, 5, 15, 71.95, 65.49, 1e-05, 16, 38.55, 64.01, 0.07437, 17, 16.14, 62.13, 0.00433, 18, 53.27, -13.77, 0.25775, 19, 10.5, -19.01, 0.66354, 3, 16, 40.89, 89.05, 0.00871, 18, 73.95, 0.55, 0.10838, 19, 35.02, -13.37, 0.88291, 3, 16, 39.14, 114.35, 5e-05, 18, 92.19, 18.16, 0.00878, 19, 58.49, -3.78, 0.99116, 2, 19, 71.36, -0.65, 0.99869, 20, 62.04, -42.5, 0.00131, 2, 19, 64.82, 7.85, 0.93407, 20, 58.34, -32.43, 0.06593, 4, 18, 77.89, 31.62, 6e-05, 19, 50.19, 14.02, 0.74588, 20, 46.21, -22.18, 0.20806, 21, 3.37, -23.23, 0.046, 4, 18, 58.69, 27.2, 0.02401, 19, 30.72, 17.03, 0.41756, 20, 28.52, -13.51, 0.39459, 21, -12.37, -11.39, 0.16383, 4, 18, 69.83, 35.63, 6e-05, 19, 44.19, 20.73, 0.19381, 20, 42.49, -13.99, 0.36635, 21, 1.26, -14.49, 0.43978, 3, 19, 61.84, 25.91, 0.04602, 20, 60.87, -14.31, 0.37132, 21, 19.25, -18.25, 0.58266, 3, 19, 47.64, 28.81, 0.00136, 20, 48.19, -7.31, 0.46709, 21, 8.11, -8.99, 0.53155, 3, 19, 32.71, 30.73, 0.00148, 20, 34.5, -1.03, 0.65007, 21, -4.15, -0.25, 0.34844, 4, 18, 41.12, 40.5, 0.02264, 19, 19.33, 35.9, 0.00099, 20, 23.27, 7.9, 0.83859, 21, -13.5, 10.62, 0.13778, 5, 15, 36.26, 20.98, 0.21288, 16, 1.11, 20.95, 0.25237, 17, -28.35, 26.41, 0.02694, 18, -3.78, -12.8, 0.49904, 19, -42.12, 3.02, 0.00877, 4, 15, 16.26, 17.16, 0.30138, 16, -19.02, 17.93, 0.1947, 17, -48.7, 27.01, 0.00649, 18, -19.04, 0.67, 0.49743, 3, 16, 53.1, 2.75, 0.10988, 17, 19.58, -0.74, 0.88862, 18, 15.73, -64.32, 0.0015, 4, 15, 73.09, 3.45, 0.06562, 16, 37.21, 1.97, 0.26287, 17, 3.81, 1.32, 0.66633, 18, 4.91, -52.65, 0.00518, 4, 15, 56.76, 2.87, 0.23876, 16, 20.88, 2.04, 0.41577, 17, -12.26, 4.29, 0.33332, 18, -5.54, -40.1, 0.01214, 4, 15, 35.27, 2.19, 0.51278, 16, -0.62, 2.22, 0.3491, 17, -33.38, 8.28, 0.11117, 18, -19.24, -23.53, 0.02696, 4, 15, 17.78, 3.04, 0.75976, 16, -18.07, 3.77, 0.19611, 17, -50.28, 12.9, 0.00013, 18, -29.28, -9.17, 0.04401, 3, 15, 7.37, 7.78, 0.87992, 16, -28.28, 8.92, 0.06474, 18, -31.9, 1.96, 0.05534, 3, 15, 23.51, -9.52, 0.77786, 16, -12.84, -9.02, 0.222, 18, -35.7, -21.4, 0.00014, 3, 15, 10.55, -5.37, 0.93348, 16, -25.63, -4.35, 0.06617, 18, -40.35, -8.61, 0.00035, 3, 15, 0.93, 0.5, 0.99513, 16, -35.01, 1.89, 0.00435, 18, -41.61, 2.59, 0.00053, 2, 15, 40.63, -25.25, 0.78565, 16, 3.64, -25.42, 0.21435, 2, 15, 27.31, -18.76, 0.85803, 16, -9.41, -18.4, 0.14197, 2, 15, 15.91, -11.93, 0.90091, 16, -20.54, -11.12, 0.09909, 2, 19, 57.06, 2.43, 0.99921, 20, 49.31, -35.3, 0.00079, 4, 16, 27.87, 94.62, 0.00326, 18, 69.84, 14.1, 0.10717, 19, 36.22, 0.73, 0.88838, 20, 28.92, -30.7, 0.00119, 5, 15, 57.57, 69.88, 0.00058, 16, 24.35, 68.97, 0.02059, 18, 47.93, 0.29, 0.3108, 19, 10.76, -3.98, 0.66724, 20, 3.21, -27.61, 0.00079, 5, 15, 48.91, 46.31, 0.00146, 16, 14.76, 45.76, 0.04659, 18, 23.99, -7.29, 0.61624, 19, -14.29, -2.15, 0.33532, 20, -20.16, -18.4, 0.0004, 4, 15, 40.95, 32.25, 0.00219, 16, 6.24, 32.03, 0.065, 18, 8, -9.61, 0.76359, 19, -30, 1.62, 0.16922, 3, 19, 45.19, 25.58, 0.028, 20, 44.88, -9.66, 0.51689, 21, 4.42, -10.68, 0.45511, 4, 18, 55.37, 32.85, 0.00036, 19, 29.73, 23.51, 0.04301, 20, 29.51, -7.03, 0.72285, 21, -10.18, -5.2, 0.23378, 4, 18, 40.22, 26.98, 0.00091, 19, 13.48, 23.67, 0.0357, 20, 14.05, -2.03, 0.90206, 21, -24.43, 2.61, 0.06133, 3, 18, 27.65, 25.67, 0.00137, 19, 1.32, 27.11, 0.03038, 20, 3.46, 4.88, 0.96825, 4, 18, 44.21, 20.48, 0.15482, 19, 14.78, 16.15, 0.28638, 20, 13.05, -9.59, 0.5128, 21, -26.83, -4.63, 0.046, 3, 18, 30.62, 12.84, 0.33884, 19, -0.67, 14.09, 0.15807, 20, -2.32, -6.96, 0.50309, 3, 18, 20.45, 6.18, 0.46034, 19, -12.59, 11.68, 0.09527, 20, -14.41, -5.7, 0.4444, 2, 18, 8.47, 9.4, 0.8325, 20, -21.68, 4.34, 0.1675, 4, 15, 22.31, 30.67, 0.09139, 16, -12.44, 31.19, 0.00095, 18, -4.66, 4.16, 0.89155, 20, -35.17, 8.55, 0.01611], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 36, 64, 64, 66, 28, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 20, 80, 80, 82, 82, 84, 16, 86, 86, 88, 88, 90, 46, 92, 92, 94, 94, 96, 96, 98, 98, 100, 56, 102, 102, 104, 104, 106, 106, 108, 52, 110, 110, 112, 112, 114], "width": 159, "height": 105}}, "shanggaobi": {"shanggaobi": {"type": "mesh", "uvs": [0.2613, 0.00646, 0.34599, 0.00649, 0.58234, 0.00659, 0.7399, 0.00666, 0.81517, 0.00669, 0.95663, 0.14697, 1, 0.26697, 1, 0.383, 0.90164, 0.53582, 0.75129, 0.54706, 0.59939, 0.55841, 0.5301, 0.65231, 0.58306, 0.81027, 0.63093, 0.95306, 0.5432, 0.96888, 0.46505, 0.98297, 0.37056, 1, 0.28021, 1, 0.0655, 0.82702, 0.0329, 0.61849, 0.01289, 0.49055, 0, 0.40809, 0, 0.35196, 0.11572, 0.16847, 0.21791, 0.00644, 0.65717, 0.0814, 0.71232, 0.22562, 0.67096, 0.37454, 0.39523, 0.44351, 0.33024, 0.57362, 0.31843, 0.74291, 0.40509, 0.90437, 0.80489, 0.09081, 0.87382, 0.23032, 0.82655, 0.37924, 0.30857, 0.16762, 0.2357, 0.34789, 0.12738, 0.48897, 0.42674, 0.1974, 0.41493, 0.35416, 0.10572, 0.61438, 0.17465, 0.79778, 0.30464, 0.93886, 0.54689, 0.50151, 0.46417, 0.5987, 0.42281, 0.76643, 0.53704, 0.87616], "triangles": [17, 42, 16, 16, 31, 15, 16, 42, 31, 17, 18, 42, 42, 18, 41, 14, 15, 46, 15, 31, 46, 14, 46, 13, 46, 12, 13, 31, 42, 30, 42, 41, 30, 31, 45, 46, 31, 30, 45, 46, 45, 12, 18, 40, 41, 18, 19, 40, 45, 11, 12, 41, 40, 30, 11, 45, 44, 45, 30, 44, 30, 29, 44, 30, 40, 29, 11, 44, 10, 19, 20, 40, 40, 37, 29, 44, 29, 28, 40, 20, 37, 43, 44, 28, 37, 36, 28, 37, 21, 36, 21, 22, 36, 10, 43, 9, 43, 27, 9, 9, 34, 8, 9, 27, 34, 8, 34, 7, 43, 28, 27, 28, 39, 27, 28, 36, 39, 34, 6, 7, 27, 26, 34, 34, 33, 6, 34, 26, 33, 26, 27, 38, 36, 35, 39, 27, 39, 38, 39, 35, 38, 22, 23, 36, 36, 23, 35, 33, 5, 6, 26, 32, 33, 33, 32, 5, 38, 25, 26, 26, 25, 32, 35, 1, 38, 38, 2, 25, 38, 1, 2, 35, 24, 0, 35, 23, 24, 35, 0, 1, 32, 4, 5, 25, 3, 32, 32, 3, 4, 25, 2, 3, 44, 43, 10, 29, 37, 28, 20, 21, 37], "vertices": [1, 13, -15.48, 13.05, 1, 1, 13, -11.66, 13.05, 1, 1, 13, -0.98, 13.04, 1, 1, 13, 6.14, 13.04, 1, 1, 13, 9.54, 13.03, 1, 1, 13, 15.94, 5.07, 1, 1, 13, 17.9, -1.74, 1, 1, 13, 17.9, -8.33, 1, 1, 13, 13.45, -17.01, 1, 1, 13, 6.66, -17.65, 1, 2, 13, -0.21, -18.29, 0.54292, 14, 13.47, 6.48, 0.45708, 2, 13, -3.34, -23.62, 0.15603, 14, 10.34, 1.15, 0.84397, 2, 13, -0.95, -32.59, 0.00296, 14, 12.73, -7.82, 0.99704, 1, 14, 14.89, -15.93, 1, 1, 14, 10.93, -16.83, 1, 1, 14, 7.4, -17.63, 1, 1, 14, 3.13, -18.59, 1, 1, 14, -0.96, -18.59, 1, 1, 14, -10.66, -8.77, 1, 2, 13, -25.81, -21.7, 0.06936, 14, -12.13, 3.07, 0.93064, 1, 13, -26.71, -14.44, 1, 1, 13, -27.29, -9.76, 1, 1, 13, -27.29, -6.57, 1, 1, 13, -22.06, 3.85, 1, 1, 13, -17.45, 13.05, 1, 1, 13, 2.4, 8.79, 1, 1, 13, 4.9, 0.6, 1, 1, 13, 3.03, -7.85, 1, 1, 13, -9.43, -11.77, 1, 2, 13, -12.37, -19.15, 0.16046, 14, 1.3, 5.61, 0.83954, 1, 14, 0.77, -4, 1, 1, 14, 4.69, -13.16, 1, 1, 13, 9.08, 8.26, 1, 1, 13, 12.19, 0.34, 1, 1, 13, 10.06, -8.12, 1, 1, 13, -13.35, 3.9, 1, 1, 13, -16.64, -6.34, 1, 1, 13, -21.54, -14.35, 1, 1, 13, -8.01, 2.21, 1, 1, 13, -8.54, -6.69, 1, 2, 13, -22.52, -21.47, 0.06686, 14, -8.84, 3.3, 0.93314, 1, 14, -5.73, -7.11, 1, 1, 14, 0.15, -15.12, 1, 1, 13, -2.58, -15.06, 1, 2, 13, -6.32, -20.58, 0.22743, 14, 7.36, 4.19, 0.77257, 2, 13, -8.19, -30.1, 0.00677, 14, 5.49, -5.33, 0.99323, 1, 14, 10.65, -11.56, 1], "hull": 25, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 32, 34, 34, 36, 42, 44, 4, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 30, 32, 62, 30, 26, 28, 28, 30, 22, 24, 24, 26, 36, 38, 44, 46, 46, 48, 4, 6, 6, 8, 6, 64, 64, 66, 66, 68, 16, 18, 18, 20, 68, 18, 0, 48, 0, 70, 70, 72, 72, 74, 38, 40, 40, 42, 0, 2, 2, 4, 2, 76, 76, 78, 74, 80, 80, 82, 82, 84, 86, 88, 88, 90, 90, 92], "width": 117, "height": 147}}, "shouji/tx_baodian02": {"shouji/tx_baodian02": {"width": 50, "height": 100}, "shouji/tx_baodian03": {"width": 50, "height": 100}, "shouji/tx_baodian04": {"width": 50, "height": 100}, "shouji/tx_baodian05": {"width": 50, "height": 100}, "shouji/tx_baodian06": {"width": 50, "height": 100}, "shouji/tx_baodian07": {"width": 50, "height": 100}, "shouji/tx_baodian08": {"width": 50, "height": 100}}}}], "events": {"atk": {}}, "animations": {"die": {"slots": {"shangao013": {"attachment": [{"time": 0.1333, "name": "shangao013"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "angle": 11.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -24.78}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -2.38, "y": 27.36}]}, "bone3": {"rotate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 11.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -25.35}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 9.07, "y": -35.21}]}, "bone4": {"rotate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "angle": -8.76, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2333, "angle": 11.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -10.35}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 2.73, "y": 25.61}]}, "bone5": {"rotate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "angle": -8.76, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2333, "angle": 11.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -10.35}]}, "bone14": {"translate": [{"time": 0.2333, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "x": 3.6, "y": 12.38}]}, "bone55": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -80.39}]}, "bone71": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -88.04, "y": -5.39}]}, "bone72": {"rotate": [{"angle": -27.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -27.7}]}, "bone73": {"rotate": [{"time": 0.2333, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": 30.28}]}, "bone74": {"rotate": [{"time": 0.2333, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": 30.28}]}, "bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -28.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}}}, "hurt": {"slots": {"shouji/tx_baodian2": {"attachment": [{"time": 0.1333, "name": "shouji/tx_baodian02"}, {"time": 0.1667, "name": "shouji/tx_baodian03"}, {"time": 0.2, "name": "shouji/tx_baodian04"}, {"time": 0.2333, "name": "shouji/tx_baodian05"}, {"time": 0.2667, "name": "shouji/tx_baodian06"}, {"time": 0.3, "name": "shouji/tx_baodian07"}, {"time": 0.3333, "name": "shouji/tx_baodian08"}, {"time": 0.3667, "name": null}]}, "shouji/tx_baodian02": {"attachment": [{"time": 0.1333, "name": "shouji/tx_baodian02"}, {"time": 0.1667, "name": "shouji/tx_baodian03"}, {"time": 0.2, "name": "shouji/tx_baodian04"}, {"time": 0.2333, "name": "shouji/tx_baodian05"}, {"time": 0.2667, "name": "shouji/tx_baodian06"}, {"time": 0.3, "name": "shouji/tx_baodian07"}, {"time": 0.3333, "name": "shouji/tx_baodian08"}, {"time": 0.3667, "name": null}]}, "shangao013": {"attachment": [{"time": 0.1333, "name": "shangao013"}]}}, "bones": {"bone2": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone3": {"rotate": [{"angle": 19.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 9.61, "curve": "stepped"}, {"time": 0.1667, "angle": 9.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 28.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": 19.26}]}, "bone4": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -7.35, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2667, "angle": 7.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4}]}, "bone5": {"rotate": [{"angle": 1.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -7.35, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2667, "angle": 7.54, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.4, "angle": 1.94}]}, "bone6": {"rotate": [{"angle": 11.62, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 12.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 0.4, "angle": 11.62}]}, "bone7": {"rotate": [{"angle": 7.02, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.0667, "angle": 12.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 6.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.4, "angle": 7.02}]}, "bone8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 26.98, "y": -2.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone10": {"rotate": [{"angle": 11.62, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 12.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 0.4, "angle": 11.62}]}, "bone11": {"rotate": [{"angle": 7.02, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.0667, "angle": 12.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 6.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.4, "angle": 7.02}]}, "bone16": {"rotate": [{"angle": 6.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 12.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": 6.13}]}, "bone17": {"rotate": [{"angle": 11.07, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": 3.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 12.25, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.4, "angle": 11.07}]}, "bone18": {"rotate": [{"angle": 11.62, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 12.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 8.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 0.4, "angle": 11.62}]}, "bone19": {"rotate": [{"angle": 11.07, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": 3.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 12.25, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.4, "angle": 11.07}]}, "bone20": {"rotate": [{"angle": 11.62, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 12.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 8.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 0.4, "angle": 11.62}]}, "bone21": {"rotate": [{"angle": 11.07, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": 3.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 12.25, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.4, "angle": 11.07}]}, "bone22": {"rotate": [{"angle": 11.62, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 12.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 8.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 0.4, "angle": 11.62}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.62, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone44": {"rotate": [{"angle": 3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": 3.31}]}, "bone45": {"rotate": [{"angle": 3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": 3.31}]}, "bone46": {"rotate": [{"angle": 6.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": 3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.62}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.62, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone49": {"rotate": [{"angle": 3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": 3.31}]}, "bone72": {"rotate": [{"angle": -27.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -27.7}]}, "bone73": {"rotate": [{"angle": 25.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 5.26, "curve": 0.299, "c2": 0.22, "c3": 0.649, "c4": 0.61}, {"time": 0.3, "angle": 46.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": 25.81}]}, "bone74": {"rotate": [{"angle": 46.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 46.03}]}, "bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 1.21, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -25.33, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.21, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}}}, "run": {"bones": {"bone4": {"rotate": [{"angle": -8.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -20.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.27}], "translate": [{"x": -3.73, "y": -29.54}]}, "bone5": {"rotate": [{"angle": -6.49}]}, "bone56": {"rotate": [{"angle": -23.54, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.1667, "angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -32.05, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5, "angle": -23.54}], "translate": [{"x": 119.79, "y": -30.99, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.1667, "x": 8.62, "y": -33.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 175.99, "y": -29.81, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5, "x": 119.79, "y": -30.99}]}, "bone2": {"rotate": [{"angle": -20.69, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "angle": -16.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -28.03, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5, "angle": -20.69}], "translate": [{"x": 2.12, "y": -16.79}]}, "bone63": {"rotate": [{"angle": -54.06}], "translate": [{"x": -34.26, "y": 63.11}]}, "bone60": {"rotate": [{"angle": -28.39}], "translate": [{"x": -39.66, "y": 68.55}]}, "bone65": {"rotate": [{"angle": -61.8, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "angle": -35.83, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": -61.8}], "translate": [{"x": 253.12, "y": -45.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "x": 68.52, "y": -21.39, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": 253.12, "y": -45.23}]}, "bone3": {"rotate": [{"angle": 13.69, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": 15.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -5.46, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5, "angle": 13.69}], "translate": [{"x": -12.81, "y": -29.05}]}, "bone": {"rotate": [{"angle": -5.49, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5, "angle": -5.49}], "translate": [{"x": 0.83, "y": 8.45, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "x": -0.67, "y": -7.97, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5, "x": 0.83, "y": 8.45}]}, "bone72": {"rotate": [{"angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -32.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.95}], "translate": [{"x": -22.25, "y": -37.01}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 22.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 22.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 22.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 22.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone44": {"rotate": [{"angle": 3.47, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.34, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5, "angle": 3.47}]}, "bone45": {"rotate": [{"angle": 1.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 1.91}]}, "bone46": {"rotate": [{"angle": 6.86, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.0667, "angle": 3.47, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 10.34, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5, "angle": 6.86}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone55": {"rotate": [{"angle": -12.2, "curve": 0.326, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.1667, "angle": -66.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": 17.19, "curve": 0.324, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.5, "angle": -12.2}], "translate": [{"x": 7.44, "y": -45.96, "curve": 0.326, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.1667, "x": 150.71, "y": -24.08, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "x": -70.15, "y": -57.81, "curve": 0.324, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.5, "x": 7.44, "y": -45.96}]}, "bone61": {"rotate": [{"angle": 27.89}]}, "bone62": {"rotate": [{"angle": 18.73}]}, "bone64": {"rotate": [{"angle": 86.68}]}, "bone71": {"rotate": [{"angle": 37.21, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2667, "angle": -54.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 37.21}], "translate": [{"x": -69.87, "y": -58.15, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2667, "x": 151.62, "y": 1.7, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -69.87, "y": -58.15}]}, "bone73": {"rotate": [{"angle": -15.04, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": -2.95, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.3667, "angle": -32.76, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -15.04}]}, "bone74": {"rotate": [{"angle": -34.67, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "angle": -7.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4667, "angle": -36.97, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -34.67}]}, "bone26": {"translate": [{"x": -32.24, "y": -10.61}]}, "bone23": {"translate": [{"x": -11.56, "y": 6.09}]}, "bone35": {"translate": [{"curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "x": 8.89, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5}]}}, "deform": {"default": {"shangao06": {"shangao06": [{"offset": 10, "vertices": [-13.57068, 18.08487, -9.56445, 20.48743, -3.0498, 3.85416, -1.79842, 4.574, 2.93602, -3.94152, 6.44491, -4.93725, 5.80278, -5.98849, -8.20869, 2.98599, 12.14026, -8.83514, 10.42573, -11.06786, -14.23728, 6.14864, -15.06628, 3.67595, -1.80479, 1.45741, -1.30348, 1.91889, 1.7615, -1.50943, 1.98957, -1.19294, 0.05309, 1.05039, -0.08372, -1.0484, 0.09316, -1.0476], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "offset": 10, "vertices": [-13.57068, 18.08487, -9.56445, 20.48743, 0, 0, 0, 0, 0, 0, 9.74242, -10.08473, 7.46245, -11.87199, -11.35458, 8.22751, 13.97075, -12.59924, 11.08359, -15.20141, -15.95714, 9.96454, -17.40133, 7.14973], "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "offset": 10, "vertices": [-13.57068, 18.08487, -9.56445, 20.48743, -12.8786, 16.27524, -7.59432, 19.31493, 12.39816, -16.64412, -4.18221, 11.6519, 0.45406, 12.97268, 1.92979, -13.9062, 6.24102, 3.2957, 8.30561, 2.25361, -8.69453, -6.14911, -7.54096, -7.51927, -7.62123, 6.15433, -5.5043, 8.10305, 7.43843, -6.37396, 8.40149, -5.03751, 0.2242, 4.43556, -0.35353, -4.42715, 0.39339, -4.42377], "curve": 0.345, "c2": 0.37, "c3": 0.696, "c4": 0.76}, {"time": 0.5, "offset": 10, "vertices": [-13.57068, 18.08487, -9.56445, 20.48743, -3.0498, 3.85416, -1.79842, 4.574, 2.93602, -3.94152, 6.44491, -4.93725, 5.80278, -5.98849, -8.20869, 2.98599, 12.14026, -8.83514, 10.42573, -11.06786, -14.23728, 6.14864, -15.06628, 3.67595, -1.80479, 1.45741, -1.30348, 1.91889, 1.7615, -1.50943, 1.98957, -1.19294, 0.05309, 1.05039, -0.08372, -1.0484, 0.09316, -1.0476]}]}}}}, "shan_gao": {"slots": {"shangao013": {"attachment": [{"time": 0.5667, "name": "shangao013"}, {"time": 0.6667, "name": "shangao014"}]}}, "bones": {"bone2": {"rotate": [{"angle": 0.23, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 2.4, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1, "angle": 0.23}]}, "bone3": {"rotate": [{"angle": 1.2, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": 0.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 1.2}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone5": {"rotate": [{"angle": 0.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 0.99}]}, "bone6": {"rotate": [{"angle": 6.25, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 10.62, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 1, "angle": 6.25}]}, "bone7": {"rotate": [{"angle": 10.18, "curve": 0.277, "c2": 0.14, "c3": 0.661, "c4": 0.65}, {"time": 0.2667, "angle": 3.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 10.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1, "angle": 10.18}]}, "bone10": {"rotate": [{"angle": 6.25, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 10.62, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 1, "angle": 6.25}]}, "bone11": {"rotate": [{"angle": 10.18, "curve": 0.277, "c2": 0.14, "c3": 0.661, "c4": 0.65}, {"time": 0.2667, "angle": 3.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 10.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1, "angle": 10.18}]}, "bone14": {"rotate": [{"angle": -2.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.37}], "translate": [{"y": -0.81, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": -2.84, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "y": -0.81}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.62, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone17": {"rotate": [{"angle": 3.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10.62, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 3.01}]}, "bone18": {"rotate": [{"angle": 7.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 10.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 7.61}]}, "bone19": {"rotate": [{"angle": 3.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10.62, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 3.01}]}, "bone20": {"rotate": [{"angle": 7.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 10.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 7.61}]}, "bone21": {"rotate": [{"angle": 3.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10.62, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 3.01}]}, "bone22": {"rotate": [{"angle": 7.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 10.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 7.61}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone44": {"rotate": [{"angle": -2.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.14}]}, "bone45": {"rotate": [{"angle": -2.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.14}]}, "bone46": {"rotate": [{"angle": -5.4, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "angle": -2.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -5.4}]}, "bone48": {"rotate": [{"angle": -5.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -5.4}]}, "bone49": {"rotate": [{"angle": -7.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3333, "angle": -2.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.55}]}, "bone72": {"rotate": [{"angle": 18.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 18.08}]}, "bone73": {"rotate": [{"angle": -10.11, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -24.91, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 1, "angle": -10.11}]}, "bone74": {"rotate": [{"angle": -25.4, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.4, "angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9333, "angle": -27.33, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 1, "angle": -25.4}]}, "bone8": {"translate": [{"x": -1.43, "y": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 9.34, "y": -5.78, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.43, "y": -3.91}]}}}}}