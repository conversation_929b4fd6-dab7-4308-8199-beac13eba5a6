import { _decorator } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UIKnappsack_itemMain_son } from "./UIKnappsack_itemMain_son";
import { UIKnappsack_huodongMain_son } from "./UIKnappsack_huodongMain_son";
import { UIKnappsack_joinMain_son } from "./UIKnappsack_joinMain_son";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { KnappsackAudioName } from "../../../module/player/PlayerConfig";

const { ccclass, property } = _decorator;

@ccclass("UIKnappsack")
export class UIKnappsack extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_KNAPSACK}?prefab/ui/UIKnappsack`;
  }

  protected dependOn(): Array<BundleEnum> {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM, BundleEnum.BUNDLE_COMMON_FONT];
  }

  private _curMainIndex: number = 0;

  public init(param: any[]) {
    super.init(param);
    this._curMainIndex = (param && param[0]) || 0;
  }

  protected onEvtShow(): void {
    GoodsModule.api.buyInfo((data) => {
      this.setBtnState();
    });
  }
  private on_click_btn_item() {
    AudioMgr.instance.playEffect(KnappsackAudioName.Effect.点击下方页签);
    if (this._curMainIndex == 0) {
      return;
    }
    this._curMainIndex = 0;
    this.setBtnState();
  }

  private on_click_btn_huodong() {
    AudioMgr.instance.playEffect(KnappsackAudioName.Effect.点击下方页签);
    if (this._curMainIndex == 1) {
      return;
    }
    this._curMainIndex = 1;
    this.setBtnState();
  }

  private on_click_btn_join() {
    AudioMgr.instance.playEffect(KnappsackAudioName.Effect.点击下方页签);
    if (this._curMainIndex == 2) {
      return;
    }
    this._curMainIndex = 2;
    this.setBtnState();
  }

  private setBtnState() {
    this.getNode("mainLay").children.forEach((val) => {
      val.active = false;
    });
    switch (this._curMainIndex) {
      case 0:
        this.itemMainState();
        break;

      case 1:
        this.huodongMainState();
        break;

      case 2:
        this.joinMainState();
        break;
    }
  }

  private itemMainState() {
    UIMgr.instance.showSon(UIKnappsack_itemMain_son, { parent: this["itemMain"] });
    this.getNode("itemMain").active = true;

    this.getNode("btn_item").getChildByName("pitch").active = true;
    this.getNode("btn_huodong").getChildByName("pitch").active = false;
    this.getNode("btn_join").getChildByName("pitch").active = false;

    this.getNode("btn_item").getChildByName("no_pitch").active = false;
    this.getNode("btn_huodong").getChildByName("no_pitch").active = true;
    this.getNode("btn_join").getChildByName("no_pitch").active = true;
  }

  private huodongMainState() {
    UIMgr.instance.showSon(UIKnappsack_huodongMain_son, { parent: this["huodongMain"] });
    this.getNode("huodongMain").active = true;

    this.getNode("btn_item").getChildByName("pitch").active = false;
    this.getNode("btn_huodong").getChildByName("pitch").active = true;
    this.getNode("btn_join").getChildByName("pitch").active = false;

    this.getNode("btn_item").getChildByName("no_pitch").active = true;
    this.getNode("btn_huodong").getChildByName("no_pitch").active = false;
    this.getNode("btn_join").getChildByName("no_pitch").active = true;
  }

  private joinMainState() {
    UIMgr.instance.showSon(UIKnappsack_joinMain_son, { parent: this["joinMain"] });
    this.getNode("joinMain").active = true;

    this.getNode("btn_item").getChildByName("pitch").active = false;
    this.getNode("btn_huodong").getChildByName("pitch").active = false;
    this.getNode("btn_join").getChildByName("pitch").active = true;

    this.getNode("btn_item").getChildByName("no_pitch").active = true;
    this.getNode("btn_huodong").getChildByName("no_pitch").active = true;
    this.getNode("btn_join").getChildByName("no_pitch").active = false;
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    UIMgr.instance.close(UIKnappsack_itemMain_son);
    UIMgr.instance.close(UIKnappsack_huodongMain_son);
    UIMgr.instance.close(UIKnappsack_joinMain_son);
  }
}
