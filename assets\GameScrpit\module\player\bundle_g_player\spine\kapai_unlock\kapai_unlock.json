{"skeleton": {"hash": "zwNIhz+yXdU3Ew73ekHBEr1CxxQ=", "spine": "3.8.75", "x": -479.04, "y": -528.89, "width": 959.91, "height": 1056.06, "images": "", "audio": "D:/spine导出/战将解锁"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "y": -401.25}, {"name": "light22", "parent": "bone2", "y": 401.25, "scaleX": 1.2468, "scaleY": 1.2468}, {"name": "light1", "parent": "root"}, {"name": "tx1", "parent": "root", "rotation": 45.82, "scaleX": 16.874, "scaleY": 16.874}, {"name": "tx1111", "parent": "root"}, {"name": "tx2", "parent": "root", "rotation": -75.44, "scaleX": 16.874, "scaleY": 16.874}, {"name": "tx3", "parent": "root", "rotation": -29.41, "scaleX": 16.874, "scaleY": 16.874}, {"name": "light2", "parent": "root"}, {"name": "txlizi1", "parent": "root", "x": 36.48, "y": 38.3}, {"name": "tx22", "parent": "txlizi1", "rotation": -15.62, "x": 149.74, "y": 5.09, "scaleX": -1.6166, "scaleY": 1.6166}, {"name": "tx23", "parent": "txlizi1", "rotation": 2.83, "x": 227.48, "y": -7.56, "scaleX": 1.6166, "scaleY": 1.6166}, {"name": "txlizi2", "parent": "root", "rotation": -165.59, "x": 2.13, "y": -79.22}, {"name": "tx24", "parent": "txlizi2", "rotation": -15.62, "x": 95.5, "y": 32.21, "scaleX": 1.6166, "scaleY": 1.6166}, {"name": "tx25", "parent": "txlizi2", "rotation": 2.83, "x": 227.48, "y": -7.56, "scaleX": 1.6166, "scaleY": 1.6166}, {"name": "txlizi3", "parent": "root", "rotation": -88.47, "x": 2.13, "y": -79.22}, {"name": "tx26", "parent": "txlizi3", "rotation": -8.38, "x": 139.33, "y": -19.6, "scaleX": 1.6166, "scaleY": 1.6166}, {"name": "tx27", "parent": "txlizi3", "rotation": 2.83, "x": 227.48, "y": -7.56, "scaleX": 1.6166, "scaleY": 1.6166}, {"name": "txlizi4", "parent": "root", "rotation": 115.81, "x": 40.1, "y": 76.27}, {"name": "tx28", "parent": "txlizi4", "rotation": -15.62, "x": 95.5, "y": 32.21, "scaleX": 1.6166, "scaleY": 1.6166}, {"name": "tx29", "parent": "txlizi4", "rotation": 2.83, "x": 168.9, "y": -41.49, "scaleX": 1.6166, "scaleY": 1.6166}, {"name": "guazai", "parent": "root"}], "slots": [{"name": "rootcut", "bone": "root"}, {"name": "img/A_juji", "bone": "tx1", "color": "ffde00ff", "attachment": "img/A_juji", "blend": "additive"}, {"name": "img/A_juji2", "bone": "tx2", "color": "ffde00ff", "attachment": "img/A_juji", "blend": "additive"}, {"name": "img/A_juji3", "bone": "tx3", "color": "ffde00ff", "attachment": "img/A_juji", "blend": "additive"}, {"name": "img/card1l3", "bone": "tx1111", "color": "ffc900d0", "blend": "additive"}, {"name": "img/card1", "bone": "bone2", "attachment": "img/card3"}, {"name": "img/card1l1", "bone": "bone2", "color": "ffb50055", "blend": "additive"}, {"name": "img/card1l2", "bone": "light22", "color": "ffbf00ff", "blend": "additive"}, {"name": "img/card2", "bone": "light1", "color": "ffb500ff", "dark": "ffd400", "blend": "additive"}, {"name": "img/light3", "bone": "light2", "color": "ffb500ff", "blend": "additive"}, {"name": "img/tx1/dian1", "bone": "tx22", "color": "ff9700ff", "attachment": "img/tx1/dian1", "blend": "additive"}, {"name": "img/tx1/dian3", "bone": "tx24", "color": "ff9700ff", "attachment": "img/tx1/dian1", "blend": "additive"}, {"name": "img/tx1/dian5", "bone": "tx26", "color": "ff9700ff", "attachment": "img/tx1/dian1", "blend": "additive"}, {"name": "img/tx1/dian7", "bone": "tx28", "color": "ff9700ff", "attachment": "img/tx1/dian1", "blend": "additive"}, {"name": "img/tx1/dian2", "bone": "tx23", "color": "ff9700ff", "attachment": "img/tx1/dian1", "blend": "additive"}, {"name": "img/tx1/dian4", "bone": "tx25", "color": "ff9700ff", "attachment": "img/tx1/dian1", "blend": "additive"}, {"name": "img/tx1/dian6", "bone": "tx27", "color": "ff9700ff", "attachment": "img/tx1/dian2", "blend": "additive"}, {"name": "img/tx1/dian8", "bone": "tx29", "color": "ff9700ff", "attachment": "img/tx1/dian1", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"img/tx1/dian5": {"img/tx1/dian1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "img/tx1/dian1": {"img/tx1/dian1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "img/tx1/dian2": {"img/tx1/dian1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "img/tx1/dian3": {"img/tx1/dian1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "img/A_juji": {"img/A_juji": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [6.78, -6.78, -6.78, -6.78, -6.78, 6.78, 6.78, 6.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "img/card1": {"img/card1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [260, 22.25, -260, 22.25, -260, 780.25, 260, 780.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 379}, "img/card2": {"type": "<PERSON><PERSON><PERSON>", "parent": "img/card1", "width": 260, "height": 379}, "img/card3": {"type": "<PERSON><PERSON><PERSON>", "path": "img/card1", "parent": "img/card1", "width": 260, "height": 379}}, "img/card2": {"img/card1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [260, -379, -260, -379, -260, 379, 260, 379], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 379}}, "img/tx1/dian7": {"img/tx1/dian1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "img/tx1/dian8": {"img/tx1/dian1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "img/card1l1": {"img/card1l1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [270.95, 13.74, -269.95, 13.74, -269.95, 794.05, 270.95, 794.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 213, "height": 308}}, "img/card1l2": {"img/card1l2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -326, -232, -326, -232, 326, 232, 326], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 326}}, "img/card1l3": {"img/card1l2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [323.12, -443.85, -323.12, -443.85, -323.12, 443.85, 323.12, 443.85], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 326}}, "img/A_juji2": {"img/A_juji": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.58, 0.37, 0.37, -9.58, -9.58, -0.37, -0.37, 9.58], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "img/A_juji3": {"img/A_juji": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [6.78, -6.78, -6.78, -6.78, -6.78, 6.78, 6.78, 6.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "rootcut": {"rootcut": {"type": "clipping", "end": "rootcut", "vertexCount": 4, "vertices": [-375.18, 750.16, 375.45, 750.44, 375.4, -750.69, -375.22, -750.75], "color": "ce3a3aff"}}, "img/tx1/dian6": {"img/tx1/dian1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "img/light3": {"img/light3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -161, -161, -161, -161, 161, 162, 161], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 161, "height": 161}}, "img/tx1/dian4": {"img/tx1/dian1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}, "img/tx1/dian4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}}}], "events": {"appear": {}}, "animations": {"kapai_appear_01": {"slots": {"img/tx1/dian7": {"color": [{"color": "ff970000"}], "attachment": [{"name": null}, {"time": 0.6667, "name": "img/tx1/dian1"}]}, "img/card1l3": {"color": [{"time": 0.6667, "color": "ffc900d0", "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1.7, "color": "ffc90080"}], "attachment": [{"time": 0.6667, "name": "img/card1l2"}]}, "img/tx1/dian1": {"color": [{"color": "ff970000"}], "attachment": [{"name": null}]}, "img/tx1/dian2": {"color": [{"color": "ff970000"}], "attachment": [{"name": null}]}, "img/A_juji2": {"color": [{"color": "ffde0000", "curve": "stepped"}, {"time": 1.2, "color": "ffde0000", "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 1.7, "color": "ffde00ff"}]}, "img/card1l1": {"attachment": [{"time": 0.6, "name": "img/card1l1"}]}, "img/A_juji3": {"color": [{"color": "ffde0000"}]}, "img/A_juji": {"color": [{"color": "ffde0000", "curve": "stepped"}, {"time": 0.6667, "color": "ffde0000", "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 1.2, "color": "ffde00ff", "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 1.7, "color": "ffde00e7"}]}, "img/tx1/dian5": {"color": [{"color": "ff970000"}], "attachment": [{"name": null}]}, "img/card1": {"color": [{"color": "6f6f6fff", "curve": 0.058, "c2": 0.5, "c3": 0.839, "c4": 0.61}, {"time": 0.2, "color": "ffffffff"}], "attachment": [{"time": 0.1, "name": "img/card2"}, {"time": 0.1622, "name": "img/card3"}, {"time": 0.3333, "name": "img/card2"}, {"time": 0.5, "name": "img/card3"}]}, "img/tx1/dian6": {"color": [{"color": "ff970000"}], "attachment": [{"name": null}]}, "img/tx1/dian4": {"color": [{"color": "ff970000"}], "attachment": [{"name": null}]}, "img/tx1/dian8": {"color": [{"color": "ff970000"}], "attachment": [{"name": null}]}, "img/tx1/dian3": {"color": [{"color": "ff970000"}], "attachment": [{"name": null}]}}, "bones": {"bone2": {"translate": [{"x": -0.35, "y": -991.43, "curve": 0.058, "c2": 0.5, "c3": 0.839, "c4": 0.61}, {"time": 0.2}], "scale": [{"x": 0.269, "y": 0.072, "curve": 0.058, "c2": 0.5, "c3": 0.839, "c4": 0.61}, {"time": 0.2, "y": 0.639, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "shear": [{"x": 179, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 89, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -179, "curve": "stepped"}, {"time": 0.2102, "x": 179, "curve": 0.138, "c2": 0.02, "c3": 0.888}, {"time": 0.3333, "x": 89, "curve": 0.138, "c2": 0.02, "c3": 0.888}, {"time": 0.6, "x": -179, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.6091, "x": -180}]}, "bone": {"translate": [{"y": 115.98, "curve": "stepped"}, {"time": 0.2, "y": 115.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "scale": [{"x": 0.789, "y": 0.519, "curve": "stepped"}, {"time": 0.2, "x": 0.789, "y": 0.519, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "light1": {"scale": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 2.23, "y": 2.23}]}, "tx1": {"scale": [{"x": 3.668, "y": 3.668, "curve": "stepped"}, {"time": 0.0333, "x": 3.668, "y": 3.668, "curve": 0.323, "c2": 0.3, "c3": 0.667, "c4": 0.67}, {"time": 1.7, "x": 6.971, "y": 6.971}]}, "tx2": {"scale": [{"x": 3.668, "y": 3.668, "curve": "stepped"}, {"time": 0.5667, "x": 3.668, "y": 3.668, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 1.7, "x": 5.915, "y": 5.915}]}, "tx3": {"scale": [{"x": 3.668, "y": 3.668, "curve": "stepped"}, {"time": 1.1, "x": 3.668, "y": 3.668, "curve": 0.327, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 1.7, "x": 4.837, "y": 4.837}]}, "light2": {"scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 7.109, "y": 7.109}]}, "tx29": {"translate": [{"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 133.79, "y": 57.86}], "scale": [{"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 1.885, "y": 1.885}]}}, "deform": {"default": {"img/card1": {"img/card1": [{"vertices": [-44.24437, 124.94997, 45.89468, 0.52661, -13.61929, -0.15627, 15.26956, 125.63271], "curve": "stepped"}, {"time": 0.2, "vertices": [-44.24437, 124.94997, 45.89468, 0.52661, -13.61929, -0.15627, 15.26956, 125.63271], "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 0.2667, "vertices": [-29.16224, 82.35671, 30.24999, 0.3471, -39.27249, -4.76282, 40.36024, 87.4664], "curve": 0.311, "c2": 0.25, "c3": 0.65, "c4": 0.61}, {"time": 0.3333, "curve": 0.327, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.4, "offset": 4, "vertices": [-83.22655, 26.16052, 83.22659, -26.16061], "curve": 0.346, "c2": 0.38, "c3": 0.687, "c4": 0.73}, {"time": 0.5, "offset": 2, "vertices": [7.90639, 0.8877], "curve": 0.356, "c2": 0.44, "c3": 0.693, "c4": 0.79}, {"time": 0.5333}]}}}, "events": [{"time": 0.6, "name": "appear"}]}, "kapai_appear_02": {"slots": {"img/tx1/dian7": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.6667, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "color": "ff970000"}], "attachment": [{"name": null}, {"time": 0.6667, "name": "img/tx1/dian1"}]}, "img/light3": {"color": [{"time": 0.4333, "color": "ffb50000", "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "color": "ffb500ff", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffb50000"}], "attachment": [{"time": 0.6, "name": "img/light3"}]}, "img/tx1/dian1": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.6, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 1.2, "color": "ff970000"}], "attachment": [{"name": null}, {"time": 0.6, "name": "img/tx1/dian1"}]}, "img/tx1/dian2": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.8, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ff970000"}], "attachment": [{"name": null}, {"time": 0.8, "name": "img/tx1/dian1"}]}, "img/A_juji2": {"color": [{"color": "ffde0000"}]}, "img/card1l1": {"color": [{"color": "ffb50055"}], "attachment": [{"time": 0.6, "name": "img/card1l1"}]}, "img/A_juji3": {"color": [{"color": "ffde0000"}]}, "img/card2": {"twoColor": [{"time": 0.6, "light": "ff7f00ff", "dark": "ff8c00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "light": "ffb50000", "dark": "ffd400"}], "attachment": [{"time": 0.6, "name": "img/card1"}]}, "img/A_juji": {"color": [{"color": "ffde0000"}]}, "img/tx1/dian5": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.8667, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 1.2, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "color": "ff970000"}], "attachment": [{"name": null}, {"time": 0.8667, "name": "img/tx1/dian1"}]}, "img/card1": {"color": [{"color": "6f6f6fff"}], "attachment": [{"name": null}]}, "img/tx1/dian6": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.7667, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ff970000"}], "attachment": [{"name": null}, {"time": 0.7667, "name": "img/tx1/dian1"}]}, "img/tx1/dian4": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.8, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ff970000"}], "attachment": [{"name": null}, {"time": 0.8, "name": "img/tx1/dian1"}]}, "img/tx1/dian8": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.9667, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 1.3, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "color": "ff970000"}], "attachment": [{"name": null}, {"time": 0.9667, "name": "img/tx1/dian1"}]}, "img/tx1/dian3": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.5, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 0.8, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ff970000"}], "attachment": [{"name": null}, {"time": 0.5, "name": "img/tx1/dian1"}]}}, "bones": {"bone2": {"translate": [{"x": -0.35, "y": -991.43, "curve": 0.058, "c2": 0.5, "c3": 0.839, "c4": 0.61}, {"time": 0.2}], "scale": [{"x": 0.269, "y": 0.072, "curve": 0.058, "c2": 0.5, "c3": 0.839, "c4": 0.61}, {"time": 0.2, "y": 0.639, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "shear": [{"x": -180, "curve": 0.315, "c2": 0.27, "c3": 0.652, "c4": 0.62}, {"time": 0.2, "x": 179, "curve": 0.138, "c2": 0.02, "c3": 0.888}, {"time": 0.3333, "x": 89, "curve": 0.138, "c2": 0.02, "c3": 0.888}, {"time": 0.6, "x": -179, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.6091, "x": -180}]}, "light1": {"scale": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 2.23, "y": 2.23}]}, "tx1": {"scale": [{"x": 3.668, "y": 3.668}]}, "tx2": {"scale": [{"x": 3.668, "y": 3.668}]}, "tx3": {"scale": [{"x": 3.668, "y": 3.668}]}, "light2": {"scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 7.109, "y": 7.109}]}, "tx22": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 133.79, "y": 57.86}], "scale": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 1.885, "y": 1.885}]}, "tx23": {"translate": [{"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 133.79, "y": 57.86}], "scale": [{"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 1.885, "y": 1.885}]}, "tx27": {"translate": [{"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 133.79, "y": 57.86}], "scale": [{"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.885, "y": 1.885}]}, "tx26": {"translate": [{"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 133.79, "y": 57.86}], "scale": [{"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 1.885, "y": 1.885}]}, "tx24": {"translate": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 133.79, "y": 57.86}], "scale": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 1.885, "y": 1.885}]}, "tx25": {"translate": [{"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 133.79, "y": 57.86}], "scale": [{"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 1.885, "y": 1.885}]}, "tx29": {"translate": [{"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 133.79, "y": 57.86}], "scale": [{"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 1.885, "y": 1.885}]}, "tx28": {"translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 133.79, "y": 57.86}], "scale": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 1.885, "y": 1.885}]}, "txlizi4": {"shear": [{"time": 1.7}]}}, "deform": {"default": {"img/card1": {"img/card1": [{"vertices": [-44.24437, 124.94997, 45.89468, 0.52661, -13.61929, -0.15627, 15.26956, 125.63271]}]}}}}, "kapai_disappear_01": {"slots": {"img/tx1/dian7": {"attachment": [{"name": null}]}, "img/card1l3": {"attachment": [{"name": "img/card1l2"}]}, "img/tx1/dian1": {"attachment": [{"name": null}]}, "img/tx1/dian2": {"attachment": [{"name": null}]}, "img/A_juji2": {"color": [{"color": "ffde0073", "curve": 0.378, "c2": 0.55, "c3": 0.727, "c4": 0.96}, {"time": 0.6333, "color": "ffde0000", "curve": "stepped"}, {"time": 0.6667, "color": "ffde00ff", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffde0000", "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 1.7, "color": "ffde00ff", "curve": 0.337, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 2.2, "color": "ffde0073"}]}, "img/card1l1": {"color": [{"color": "ffb50055", "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "color": "ffb50055"}]}, "img/A_juji3": {"color": [{"color": "ffde00ae", "curve": 0.328, "c2": 0.32, "c3": 0.663, "c4": 0.66}, {"time": 0.2333, "color": "ffde00ff", "curve": 0.368, "c2": 0.47, "c3": 0.747, "c4": 0.97}, {"time": 1.3667, "color": "ffde0000", "curve": "stepped"}, {"time": 1.4, "color": "ffde00ff", "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "color": "ffde0000", "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 2.2, "color": "ffde00ae"}]}, "img/tx1/dian5": {"attachment": [{"name": null}]}, "img/tx1/dian6": {"attachment": [{"name": null}]}, "img/A_juji": {"color": [{"color": "ffde00ff", "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "color": "ffde0000", "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 1.0333, "color": "ffde00ff", "curve": 0.368, "c2": 0.47, "c3": 0.747, "c4": 0.97}, {"time": 2.1667, "color": "ffde0000", "curve": "stepped"}, {"time": 2.2, "color": "ffde00ff"}]}, "img/tx1/dian4": {"attachment": [{"name": null}]}, "img/tx1/dian8": {"attachment": [{"name": null}]}, "img/tx1/dian3": {"attachment": [{"name": null}]}}, "bones": {"tx2": {"scale": [{"x": 8.352, "y": 8.352, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6333, "x": 10.652, "y": 10.652, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.7, "curve": 0.243, "c3": 0.656, "c4": 0.63}, {"time": 2.2, "x": 8.352, "y": 8.352}]}, "tx3": {"scale": [{"x": 4.053, "y": 4.053, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 1.3667, "x": 10.652, "y": 10.652, "curve": "stepped"}, {"time": 1.4, "curve": "stepped"}, {"time": 1.4333, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 2.2, "x": 4.053, "y": 4.053}]}, "tx1": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "angle": 63.43, "curve": "stepped"}, {"time": 2.1667, "angle": 63.43, "curve": "stepped"}, {"time": 2.2}], "scale": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 10.652, "y": 10.652, "curve": "stepped"}, {"time": 2.2}]}, "bone2": {"shear": [{"x": -180}]}}}, "kapai_disappear_02": {"slots": {"img/tx1/dian7": {"attachment": [{"name": null}]}, "img/tx1/dian1": {"attachment": [{"name": null}]}, "img/tx1/dian2": {"attachment": [{"name": null}]}, "img/A_juji2": {"attachment": [{"name": null}]}, "img/card1l1": {"color": [{"color": "ffb50055", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffb5009b", "curve": 0.25, "c3": 0.75}, {"time": 2.2, "color": "ffb50055"}], "attachment": [{"name": "img/card1l1"}]}, "img/A_juji3": {"attachment": [{"name": null}]}, "img/tx1/dian5": {"attachment": [{"name": null}]}, "img/card1": {"attachment": [{"name": null}]}, "img/tx1/dian6": {"attachment": [{"name": null}]}, "img/A_juji": {"attachment": [{"name": null}]}, "img/tx1/dian4": {"attachment": [{"name": null}]}, "img/tx1/dian8": {"attachment": [{"name": null}]}, "img/tx1/dian3": {"attachment": [{"name": null}]}}, "bones": {"bone2": {"shear": [{"x": -180}]}}}}}