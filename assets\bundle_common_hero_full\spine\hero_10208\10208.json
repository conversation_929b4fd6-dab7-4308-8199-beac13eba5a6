{"skeleton": {"hash": "upV0bVXk3maW9cLx2y9JJx0FoNc", "spine": "3.8.75", "x": -431.55, "y": -442.42, "width": 864.22, "height": 880.9, "images": "./images/", "audio": "D:/spine/燕赤霞"}, "bones": [{"name": "root", "x": 38.8, "y": -345.25, "scaleX": 0.535, "scaleY": 0.535}, {"name": "bone", "parent": "root", "length": 764.38, "rotation": -0.33, "x": -325.29, "y": -223.65}, {"name": "shenti2", "parent": "bone", "length": 288.16, "rotation": -102.72, "x": 285.91, "y": 914.34}, {"name": "zuodabi2", "parent": "shenti2", "length": 187.83, "rotation": -29.75, "x": 18.1, "y": -54.76}, {"name": "youdabi_2", "parent": "shenti2", "length": 128.92, "rotation": 41.33, "x": 86.21, "y": 84.81}, {"name": "youxiaobi2", "parent": "youdabi_2", "length": 185.24, "rotation": -96.7, "x": 132.4, "y": 46.14}, {"name": "zuodabi4", "parent": "zuodabi2", "length": 183.11, "rotation": -24.03, "x": 183.19, "y": -5.36}, {"name": "zuoshou2", "parent": "zuodabi4", "length": 80.84, "rotation": 21.05, "x": 190.72, "y": 1.85}, {"name": "youshou2", "parent": "youxiaobi2", "length": 72.72, "rotation": -77.13, "x": 195.03, "y": -3.99}, {"name": "youshou3", "parent": "youshou2", "length": 72.72, "x": 72.72}, {"name": "youshou6", "parent": "youshou3", "length": 37.54, "rotation": 5.56, "x": 37.74, "y": -33.16}, {"name": "youshou7", "parent": "youshou6", "length": 37.54, "rotation": -7.7, "x": 39.71, "y": 0.59}, {"name": "youshou8", "parent": "youshou7", "length": 37.54, "rotation": -10.8, "x": 39.68, "y": -1.36}, {"name": "youshou9", "parent": "youshou8", "length": 37.54, "rotation": -8.67, "x": 33.91, "y": 1.83}, {"name": "youshou10", "parent": "youshou9", "length": 37.54, "rotation": 2.22, "x": 39.95, "y": 1.14}, {"name": "youshou11", "parent": "youshou2", "length": 39.84, "rotation": -11.35, "x": 107.13, "y": 28.04}, {"name": "youshou12", "parent": "youshou11", "length": 39.84, "rotation": 3.19, "x": 44.72, "y": 1.46}, {"name": "youshou13", "parent": "youshou12", "length": 39.84, "rotation": 13.61, "x": 41.61, "y": 0.96}, {"name": "youshou14", "parent": "youshou13", "length": 39.84, "rotation": 8.63, "x": 43.13, "y": 0.5}, {"name": "youshou15", "parent": "youshou14", "length": 39.84, "rotation": 31.89, "x": 42.62, "y": 0.8}, {"name": "zuodatui2", "parent": "shenti2", "length": 208.37, "rotation": -21.68, "x": 310.91, "y": -19.3}, {"name": "youdatui3", "parent": "shenti2", "length": 109.21, "rotation": 133.24, "x": 377.57, "y": 26.18}, {"name": "youdatui2", "parent": "youdatui3", "length": 169.98, "rotation": 8.18, "x": 81.3, "y": -17.31}, {"name": "youxuiaotui2", "parent": "youdatui2", "length": 267.9, "rotation": -109.56, "x": 159.4, "y": 34.35}, {"name": "zuoxiaotui2", "parent": "zuodatui2", "length": 177.15, "rotation": 18.98, "x": 198.24, "y": -10.98}, {"name": "zuojiao2", "parent": "zuoxiaotui2", "length": 32.79, "rotation": 11.8, "x": 182.61, "y": -1.97}, {"name": "zu<PERSON>jiao4", "parent": "zuojiao2", "length": 45.95, "rotation": -89.34, "x": 30.81, "y": -9.56}, {"name": "youjiao2", "parent": "youxuiaotui2", "length": 125.66, "rotation": 132.3, "x": 310.9, "y": -12.26}, {"name": "tou2", "parent": "shenti2", "length": 80.46, "rotation": -173.2, "x": 12.65, "y": 17.48}, {"name": "tou3", "parent": "tou2", "length": 80.46, "x": 80.46}, {"name": "tou4", "parent": "tou3", "length": 80.46, "x": 80.46}, {"name": "tou5", "parent": "tou4", "length": 80.46, "x": 80.46}, {"name": "jian<PERSON>", "parent": "shenti2", "length": 372.81, "rotation": -40.77, "x": -112.7, "y": 196.53}, {"name": "gong<PERSON><PERSON>", "parent": "shenti2", "length": 637.07, "rotation": 37.83, "x": -231.42, "y": -237.09}, {"name": "bone2", "parent": "root", "length": 422.66, "rotation": -0.38, "x": -54.1, "y": -165.3}, {"name": "shent1", "parent": "bone2", "length": 214.32, "rotation": -168.34, "x": 233.77, "y": 252.76}, {"name": "zuodabi1", "parent": "shent1", "length": 115.59, "rotation": 128.26, "x": 57.52, "y": 18.96}, {"name": "zuoxiaobi1", "parent": "zuodabi1", "length": 110.59, "rotation": 11.25, "x": 99.95, "y": -4.76}, {"name": "zuoshou1", "parent": "zuoxiaobi1", "length": 34.87, "rotation": 129.71, "x": 107.71, "y": -15.54}, {"name": "zuoshou4", "parent": "zuoshou1", "length": 16.62, "rotation": -3.91, "x": 43.11, "y": -0.88}, {"name": "zuoshou3", "parent": "zuoshou4", "length": 15.5, "rotation": -61.59, "x": 26.08, "y": -1.09}, {"name": "tou", "parent": "shent1", "length": 167.28, "rotation": 139.77, "x": 0.67, "y": -30.52}, {"name": "youdabi2", "parent": "shent1", "length": 62.98, "rotation": 167.63, "x": -85.58, "y": 7.4}, {"name": "yoluxiaobi2", "parent": "youdabi2", "length": 81.29, "rotation": 5.14, "x": 44.87, "y": -1.05}, {"name": "<PERSON><PERSON><PERSON>", "parent": "yoluxiaobi2", "length": 34.27, "rotation": 77.16, "x": 82.42, "y": -11.08}, {"name": "youhsou3", "parent": "<PERSON><PERSON><PERSON>", "length": 14.31, "rotation": -44.32, "x": 40.22, "y": -1.01}, {"name": "zuidatui2", "parent": "shent1", "length": 86.66, "rotation": 151.27, "x": 260.94, "y": 22.06}, {"name": "zuixiaotui1", "parent": "zuidatui2", "length": 82.09, "rotation": -12.66, "x": 79.6, "y": -1.03}, {"name": "youshou1", "parent": "zuixiaotui1", "length": 59.03, "rotation": 112.99, "x": 83.38, "y": -5.74}, {"name": "youshou16", "parent": "youshou1", "length": 22.22, "rotation": -19.44, "x": 65.91, "y": -0.19}, {"name": "kuzi", "parent": "shent1", "length": 82.22, "rotation": 62.45, "x": 183.32, "y": -49.41}, {"name": "dang<PERSON>", "parent": "shenti2", "length": 114.54, "rotation": -3.5, "x": 278.42, "y": 66.91}, {"name": "dangbu2", "parent": "dang<PERSON>", "length": 70.91, "x": 114.54}, {"name": "dangbu3", "parent": "dangbu2", "length": 43.63, "x": 70.91}, {"name": "dangbu4", "parent": "dangbu3", "length": 27.27, "x": 43.63}, {"name": "dangbu5", "parent": "dangbu4", "length": 16.36, "x": 27.27}, {"name": "dangbu6", "parent": "dangbu5", "length": 10.91, "x": 16.36}, {"name": "dangbu7", "parent": "dangbu6", "length": 5.45, "x": 10.91}, {"name": "dangbu8", "parent": "shenti2", "length": 109.24, "rotation": -0.79, "x": 294.86, "y": -39.55}, {"name": "dangbu9", "parent": "dangbu8", "length": 67.62, "rotation": -31.2, "x": 109.24}, {"name": "dangbu10", "parent": "dangbu9", "length": 41.61, "rotation": -10.8, "x": 67.62}, {"name": "dangbu11", "parent": "dangbu10", "length": 26.01, "rotation": -12, "x": 41.61}, {"name": "dangbu12", "parent": "dangbu11", "length": 15.61, "rotation": -3.6, "x": 26.01}, {"name": "dangbu13", "parent": "dangbu12", "length": 10.4, "rotation": -4.8, "x": 15.61}, {"name": "dangbu14", "parent": "dangbu13", "length": 5.2, "rotation": -6, "x": 10.4}, {"name": "yijin", "parent": "shenti2", "length": 41.54, "rotation": -36.96, "x": 294.71, "y": -70.54}, {"name": "yijin2", "parent": "yijin", "length": 24.92, "rotation": -20.4, "x": 41.54}, {"name": "yijin3", "parent": "yijin2", "length": 16.62, "rotation": -9.6, "x": 24.92}, {"name": "yijin4", "parent": "yijin3", "length": 8.31, "x": 16.62}, {"name": "yijin5", "parent": "shenti2", "length": 42.88, "rotation": -5.63, "x": 300.79, "y": -45.65}, {"name": "yijin6", "parent": "yijin5", "length": 25.73, "rotation": -38.4, "x": 42.88}, {"name": "yijin7", "parent": "yijin6", "length": 17.15, "x": 25.73}, {"name": "yijin8", "parent": "yijin7", "length": 8.58, "x": 17.15}, {"name": "<PERSON><PERSON><PERSON>", "parent": "shenti2", "length": 137.84, "rotation": 34.64, "x": 17.27, "y": -82.15}, {"name": "shengzi2", "parent": "<PERSON><PERSON><PERSON>", "length": 91.89, "x": 137.84}, {"name": "shengzi3", "parent": "shengzi2", "length": 45.95, "x": 91.89}, {"name": "pidai", "parent": "shenti2", "length": 121.89, "rotation": -32.38, "x": 9.4, "y": 67.66}, {"name": "pidai2", "parent": "pidai", "length": 73.13, "x": 121.89}, {"name": "pidai3", "parent": "pidai2", "length": 48.76, "x": 73.13}, {"name": "pidai4", "parent": "pidai3", "length": 24.38, "x": 48.76}, {"name": "ya<PERSON>i", "parent": "shenti2", "length": 140.69, "rotation": 95.17, "x": 273.68, "y": -66.91}, {"name": "bone3", "parent": "root", "length": 336.25, "rotation": -0.67, "x": -141.99, "y": -261.02}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "bone3", "length": 188.73, "rotation": 58.17, "x": -374.97, "y": 1177.3}, {"name": "heishanlaoyao2", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 113.24, "x": 188.73}, {"name": "heishanlaoyao3", "parent": "heishanlaoyao2", "length": 75.49, "x": 113.24}, {"name": "heishanlaoyao4", "parent": "heishanlaoyao3", "length": 37.75, "x": 75.49}, {"name": "<PERSON><PERSON><PERSON>", "parent": "bone3", "length": 375.36, "rotation": 91.3, "x": 529.88, "y": 802.17}, {"name": "texiao2", "parent": "<PERSON><PERSON><PERSON>", "length": 250.24, "x": 375.36}, {"name": "texiao3", "parent": "texiao2", "length": 125.12, "rotation": -38.4, "x": 250.24}, {"name": "<PERSON><PERSON><PERSON>", "parent": "bone3", "length": 769.48, "rotation": 0.92, "x": -212.96, "y": 276.38}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "parent": "bone3", "length": 540.18, "rotation": 91.27, "x": -599.37, "y": 113.14}, {"name": "youxiaobi4", "parent": "youxiaobi2", "length": 48.14, "rotation": 48.44, "x": 1.72, "y": 6.86}, {"name": "youxiaobi5", "parent": "youxiaobi4", "length": 32.09, "x": 48.14}, {"name": "youxiaobi6", "parent": "youxiaobi5", "length": 16.05, "x": 32.09}, {"name": "youxiaobi7", "parent": "youxiaobi2", "length": 35.01, "rotation": 126.67, "x": 152.48, "y": 12.34}, {"name": "youxiaobi8", "parent": "youxiaobi7", "length": 23.34, "x": 35.01}, {"name": "youxiaobi9", "parent": "youxiaobi8", "length": 11.67, "x": 23.34}, {"name": "youxuiaotui3", "parent": "youxuiaotui2", "length": 60.55, "rotation": -29.44, "x": 29.91, "y": -48.68}, {"name": "youxuiaotui4", "parent": "youxuiaotui3", "length": 40.37, "x": 60.55}, {"name": "youxuiaotui5", "parent": "youxuiaotui4", "length": 20.18, "x": 40.37}, {"name": "youxuiaotui7", "parent": "youxuiaotui2", "length": 60.07, "rotation": 9.02, "x": 20.67, "y": 38.77}, {"name": "youxuiaotui8", "parent": "youxuiaotui7", "length": 40.05, "x": 60.07}, {"name": "youxuiaotui9", "parent": "youxuiaotui8", "length": 20.02, "x": 40.05}, {"name": "zuodabi3", "parent": "zuodabi4", "length": 78.04, "rotation": 0.48, "x": -15.62, "y": -60.19}, {"name": "zuodabi5", "parent": "zuodabi3", "length": 52.02, "x": 78.04}, {"name": "zuodabi6", "parent": "zuodabi5", "length": 26.01, "x": 52.02}, {"name": "zuodabi8", "parent": "zuodabi4", "length": 90, "rotation": 23.73, "x": -18.64, "y": 20.79}, {"name": "zuodabi9", "parent": "zuodabi8", "length": 60, "x": 90}, {"name": "zuodabi10", "parent": "zuodabi9", "length": 30, "x": 60}, {"name": "kuzi2", "parent": "kuzi", "length": 30.68, "rotation": -71.53, "x": -5.2, "y": 9.01}, {"name": "kuzi3", "parent": "kuzi2", "length": 20.45, "x": 30.68}, {"name": "kuzi4", "parent": "kuzi3", "length": 10.23, "x": 20.45}, {"name": "kuzi6", "parent": "kuzi", "length": 35.86, "rotation": -46.32, "x": 72.92, "y": 6.43}, {"name": "kuzi7", "parent": "kuzi6", "length": 23.91, "x": 35.86}, {"name": "kuzi8", "parent": "kuzi7", "length": 11.95, "x": 23.91}, {"name": "youtui", "parent": "root", "x": -292.06, "y": 67.89, "color": "ff3f00ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "root", "rotation": 8.33, "x": 172.35, "y": 220.35, "color": "ff3f00ff"}, {"name": "you<PERSON>ou", "parent": "root", "x": -386.63, "y": 481.98, "color": "ff3f00ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "root", "x": -40.53, "y": 425.3, "color": "ff3f00ff"}, {"name": "guaitui1", "parent": "root", "x": 73.65, "y": -51.44, "color": "ff3f00ff"}, {"name": "guaishou1", "parent": "root", "x": 295.6, "y": -64.23, "color": "ff3f00ff"}, {"name": "guaishou2", "parent": "root", "x": 392.22, "y": 99.74, "color": "ff3f00ff"}, {"name": "beijing", "parent": "bone3", "length": 1466.71, "rotation": 1.61, "x": -654.39, "y": 773.76}, {"name": "beijing3", "parent": "beijing", "length": 390.72, "rotation": 123.03, "x": 1417.46, "y": 37.75}, {"name": "beijing4", "parent": "beijing3", "length": 190.98, "rotation": 12.87, "x": 446.65, "y": 53.45}, {"name": "beijing5", "parent": "beijing3", "length": 237.78, "rotation": -55.52, "x": 416.48, "y": -47.68}, {"name": "beijing6", "parent": "beijing3", "length": 62.96, "rotation": 92.91, "x": 190.16, "y": -185.6}, {"name": "beijing7", "parent": "beijing3", "length": 91.89, "rotation": 22.21, "x": 196.11, "y": -175.37}, {"name": "beijing8", "parent": "beijing7", "length": 42.38, "rotation": 138.89, "x": 90.79, "y": 5.89}, {"name": "beijing9", "parent": "beijing3", "length": 93.5, "rotation": 2.14, "x": 200.18, "y": -190.45}, {"name": "beijing10", "parent": "beijing9", "length": 30.92, "rotation": 158.65, "x": 90.15, "y": 3.42}, {"name": "beijing11", "parent": "beijing3", "length": 64.02, "rotation": -13.08, "x": 214.09, "y": -203.61}, {"name": "beijing12", "parent": "beijing4", "length": 195.86, "rotation": -35.48, "x": 187.42, "y": -30.73}, {"name": "beijing13", "parent": "beijing12", "length": 51.9, "rotation": 37.95, "x": 204.51, "y": 7.09}, {"name": "beijing14", "parent": "beijing13", "length": 40.21, "rotation": 80.93, "x": 47.45, "y": 7.59}, {"name": "beijing15", "parent": "beijing12", "length": 42.27, "rotation": 102.82, "x": 197.88, "y": 10.03}, {"name": "beijing16", "parent": "beijing15", "length": 56.95, "rotation": 76.98, "x": 45.32, "y": 2.94}, {"name": "beijing17", "parent": "beijing12", "length": 53.85, "rotation": -74.42, "x": 83.62, "y": -39.27}, {"name": "beijing18", "parent": "beijing17", "length": 56.12, "rotation": 48.44, "x": 55.99, "y": 4.21}, {"name": "beijing19", "parent": "beijing18", "length": 37.39, "rotation": -26.11, "x": 61.35, "y": -7.58}, {"name": "beijing20", "parent": "beijing18", "length": 36.34, "rotation": 19.59, "x": 59.33, "y": 3.28}, {"name": "beijing21", "parent": "beijing18", "length": 22.94, "rotation": 73.66, "x": 52.74, "y": 12.95}, {"name": "beijing22", "parent": "beijing4", "length": 162.25, "rotation": 16.35, "x": 205.1, "y": 34.41}, {"name": "beijing23", "parent": "beijing22", "length": 90.29, "rotation": 41.46, "x": 173.14, "y": 4.62}, {"name": "beijing24", "parent": "beijing23", "length": 38.6, "rotation": -16.98, "x": 103.23, "y": -3.38}, {"name": "beijing25", "parent": "beijing23", "length": 62.37, "rotation": 28.31, "x": 97.61, "y": 6.22}, {"name": "beijing26", "parent": "beijing4", "length": 106.3, "rotation": 89.06, "x": 218.52, "y": 53.46}, {"name": "shu1", "parent": "root", "x": 101.44, "y": 1273.66, "color": "ff3f00ff"}, {"name": "shu2", "parent": "root", "x": 141.03, "y": 1188.82, "color": "ff3f00ff"}, {"name": "shu3", "parent": "root", "x": 598.38, "y": 839.46, "color": "ff3f00ff"}, {"name": "shu4", "parent": "root", "x": 626.56, "y": 896.73, "color": "ff3f00ff"}, {"name": "shu5", "parent": "root", "x": -80.44, "y": 1087.21, "color": "ff3f00ff"}, {"name": "shu6", "parent": "root", "x": 301.68, "y": 1242.16, "color": "ff3f00ff"}, {"name": "texiao4", "parent": "bone3", "length": 75.86, "rotation": 89.99, "x": 440.43, "y": 539.88}, {"name": "yanjing2", "parent": "tou2", "x": 146.24, "y": 46.44}, {"name": "yanjing1", "parent": "tou2", "x": 130.27, "y": -45.11}, {"name": "meimao2", "parent": "tou2", "length": 66.57, "rotation": -60.84, "x": 137.27, "y": -31.86}, {"name": "meimao1", "parent": "tou2", "length": 83.38, "rotation": 50.01, "x": 149.95, "y": 13.22}, {"name": "huzi1", "parent": "tou2", "length": 66.94, "rotation": -120.99, "x": 77.89, "y": -9.36}, {"name": "huzi2", "parent": "tou2", "length": 81.76, "rotation": 96.77, "x": 81.98, "y": 3.57}, {"name": "biyan", "parent": "tou2", "x": 137.46, "y": -13.48}, {"name": "xuanzhuan2", "parent": "beijing3", "x": 47.26, "y": 46.29}, {"name": "xuanzhuan1", "parent": "beijing", "rotation": 123.03, "x": 294.92, "y": 55.52}, {"name": "dengguang2", "parent": "s<PERSON><PERSON><PERSON><PERSON>", "x": 383.75, "y": -23.47}, {"name": "dengguang1", "parent": "s<PERSON><PERSON><PERSON><PERSON>", "x": 391.76, "y": 66.02}, {"name": "wu1", "parent": "bone3", "length": 401.3, "rotation": 105.93, "x": 402.46, "y": 1084.53}, {"name": "wu2", "parent": "bone3", "length": 350.7, "rotation": 104.71, "x": 767.54, "y": 968.55}, {"name": "wu3", "parent": "bone3", "length": 401.3, "rotation": 105.93, "x": 402.46, "y": 1084.53}, {"name": "wu4", "parent": "bone3", "length": 401.3, "rotation": 105.93, "x": 402.46, "y": 1084.53}, {"name": "wu5", "parent": "bone3", "length": 350.7, "rotation": 104.71, "x": 767.54, "y": 968.55}, {"name": "wu6", "parent": "bone3", "length": 350.7, "rotation": 104.71, "x": 767.54, "y": 968.55}, {"name": "texiao5", "parent": "bone3", "length": 375.36, "rotation": 91.3, "x": 529.88, "y": 802.17}, {"name": "texiao6", "parent": "texiao5", "length": 250.24, "x": 375.36}, {"name": "texiao7", "parent": "texiao6", "length": 125.12, "rotation": -38.4, "x": 250.24}, {"name": "texiao8", "parent": "bone3", "length": 375.36, "rotation": 91.3, "x": 529.88, "y": 802.17}, {"name": "texiao9", "parent": "texiao8", "length": 250.24, "x": 375.36}, {"name": "texiao10", "parent": "texiao9", "length": 125.12, "rotation": -38.4, "x": 250.24}, {"name": "yachi", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 99.53, "rotation": -68.5, "x": 87.83, "y": 20.67}, {"name": "yanjing3", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x": 258.44, "y": -29.93}, {"name": "yanjing4", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x": 233.61, "y": 79.75}], "slots": [{"name": "tiankogn", "bone": "root", "attachment": "tiankogn"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "texiao3", "bone": "texiao5", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "texiao4", "bone": "texiao8", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "wu2", "bone": "wu2", "attachment": "wu2"}, {"name": "wu5", "bone": "wu5", "attachment": "wu2"}, {"name": "wu6", "bone": "wu6", "attachment": "wu2"}, {"name": "wu1", "bone": "wu1", "attachment": "wu1"}, {"name": "wu3", "bone": "wu3", "attachment": "wu1"}, {"name": "wu4", "bone": "wu4", "attachment": "wu1"}, {"name": "xuanzhuan2", "bone": "xuanzhuan2", "attachment": "xuanzhuan2"}, {"name": "beijing", "bone": "beijing3", "attachment": "beijing"}, {"name": "xuanzhuan1", "bone": "xuanzhuan1", "attachment": "xuanzhuan1"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "yanjing3", "bone": "yanjing3", "attachment": "yanjing3"}, {"name": "yanjing4", "bone": "yanjing4", "attachment": "yanjing4"}, {"name": "yachi", "bone": "yachi", "attachment": "yachi"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "bone": "s<PERSON><PERSON><PERSON><PERSON>", "attachment": "s<PERSON><PERSON><PERSON><PERSON>"}, {"name": "dengguang1", "bone": "dengguang1", "attachment": "dengguang1"}, {"name": "dengguang2", "bone": "dengguang2", "attachment": "dengguang2"}, {"name": "zuoshou2", "bone": "zuoshou2", "attachment": "zuoshou2"}, {"name": "jian<PERSON>", "bone": "jian<PERSON>", "attachment": "jian<PERSON>"}, {"name": "gong<PERSON><PERSON>", "bone": "gong<PERSON><PERSON>", "attachment": "gong<PERSON><PERSON>"}, {"name": "zuodabi3", "bone": "zuodabi8", "attachment": "zuodabi3"}, {"name": "zuodabi2", "bone": "zuodabi2", "attachment": "zuodabi2"}, {"name": "zuojiao2", "bone": "zu<PERSON>jiao4", "attachment": "zuojiao2"}, {"name": "zuoxiaotui2", "bone": "zuoxiaotui2", "attachment": "zuoxiaotui2"}, {"name": "zuodatui2", "bone": "zuodatui2", "attachment": "zuodatui2"}, {"name": "yijin", "bone": "yijin5", "attachment": "yijin"}, {"name": "shenti2", "bone": "shenti2", "attachment": "shenti2"}, {"name": "youdabi_2", "bone": "youdabi_2", "attachment": "youdabi_2"}, {"name": "youdatui3", "bone": "youdatui3", "attachment": "youdatui3"}, {"name": "dang<PERSON>", "bone": "dangbu8", "attachment": "dang<PERSON>"}, {"name": "ya<PERSON>i", "bone": "ya<PERSON>i", "attachment": "ya<PERSON>i"}, {"name": "youxiaobi2", "bone": "youxiaobi7", "attachment": "youxiaobi2"}, {"name": "youdatui2", "bone": "youdatui2", "attachment": "youdatui2"}, {"name": "youxuiaotui2", "bone": "youxuiaotui7", "attachment": "youxuiaotui2"}, {"name": "youjiao2", "bone": "youjiao2", "attachment": "youjiao2"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "pidai", "bone": "pidai", "attachment": "pidai"}, {"name": "tou2", "bone": "tou2", "attachment": "tou2"}, {"name": "huzi1", "bone": "huzi1", "attachment": "huzi1"}, {"name": "huzi2", "bone": "huzi2", "attachment": "huzi2"}, {"name": "yanjing1", "bone": "yanjing1", "attachment": "yanjing1"}, {"name": "yanjing2", "bone": "yanjing2", "attachment": "yanjing2"}, {"name": "biyan", "bone": "biyan", "attachment": "biyan"}, {"name": "meimao1", "bone": "meimao1", "attachment": "meimao1"}, {"name": "meimao2", "bone": "meimao2", "attachment": "meimao2"}, {"name": "youshou2", "bone": "youshou11", "attachment": "youshou2"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "youhsou3", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "yoluxiaobi2", "bone": "yoluxiaobi2", "attachment": "yoluxiaobi2"}, {"name": "youdabi2", "bone": "youdabi2", "attachment": "youdabi2"}, {"name": "shent1", "bone": "shent1", "attachment": "shent1"}, {"name": "zuidatui2", "bone": "zuidatui2", "attachment": "zuidatui2"}, {"name": "zuixiaotui1", "bone": "zuixiaotui1", "attachment": "zuixiaotui1"}, {"name": "kuzi", "bone": "kuzi6", "attachment": "kuzi"}, {"name": "youshou1", "bone": "youshou16", "attachment": "youshou1"}, {"name": "tou", "bone": "tou", "attachment": "tou"}, {"name": "zuodabi1", "bone": "zuodabi1", "attachment": "zuodabi1"}, {"name": "zuoxiaobi1", "bone": "zuoxiaobi1", "attachment": "zuoxiaobi1"}, {"name": "zuoshou1", "bone": "zuoshou3", "attachment": "zuoshou1"}, {"name": "texiao2", "bone": "texiao4", "attachment": "texiao2"}], "ik": [{"name": "guaishou1", "order": 5, "bones": ["zuodabi1", "zuoxiaobi1"], "target": "guaishou1"}, {"name": "guaishou2", "order": 6, "bones": ["youdabi2", "yoluxiaobi2"], "target": "guaishou2"}, {"name": "guaitui1", "order": 4, "bones": ["zuidatui2", "zuixiaotui1"], "target": "guaitui1", "bendPositive": false}, {"name": "shu1", "order": 7, "bones": ["beijing13", "beijing14"], "target": "shu1"}, {"name": "shu2", "order": 8, "bones": ["beijing15", "beijing16"], "target": "shu2"}, {"name": "shu3", "order": 9, "bones": ["beijing7", "beijing8"], "target": "shu3"}, {"name": "shu4", "order": 10, "bones": ["beijing9", "beijing10"], "target": "shu4"}, {"name": "shu5", "order": 11, "bones": ["beijing22", "beijing23"], "target": "shu5"}, {"name": "shu6", "order": 12, "bones": ["beijing17", "beijing18"], "target": "shu6"}, {"name": "you<PERSON>ou", "order": 2, "bones": ["zuodabi2", "zuodabi4"], "target": "you<PERSON>ou", "bendPositive": false}, {"name": "youtui", "bones": ["zuodatui2", "zuoxiaotui2"], "target": "youtui"}, {"name": "<PERSON><PERSON><PERSON>", "order": 3, "bones": ["youdabi_2", "youxiaobi2"], "target": "<PERSON><PERSON><PERSON>", "bendPositive": false}, {"name": "<PERSON><PERSON><PERSON>", "order": 1, "bones": ["youdatui2", "youxuiaotui2"], "target": "<PERSON><PERSON><PERSON>", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"dengguang2": {"dengguang2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-39.66, -52.61, -38.55, 53.38, 43.44, 52.53, 42.33, -53.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 82}}, "zuoshou1": {"zuoshou1": {"type": "mesh", "uvs": [0.43069, 0.08882, 0.6151, 0.01801, 0.86217, 0.01875, 1, 0.01916, 1, 0.04641, 0.93984, 0.14433, 0.91249, 0.23221, 0.88498, 0.32057, 0.90018, 0.40806, 0.91809, 0.49404, 0.95242, 0.65896, 0.92251, 0.70967, 0.72648, 0.82359, 0.55802, 0.92149, 0.3916, 0.98323, 0.36686, 0.97755, 0.13958, 0.80875, 0.05973, 0.70866, 0.03854, 0.54119, 0.02272, 0.41614, 0.00988, 0.31471, 0.01379, 0.24741, 0.1976, 0.17833], "triangles": [2, 3, 4, 5, 2, 4, 5, 1, 2, 1, 5, 6, 6, 0, 1, 7, 0, 6, 22, 0, 7, 22, 20, 21, 19, 20, 22, 7, 18, 19, 22, 7, 19, 12, 16, 17, 11, 9, 10, 12, 17, 18, 12, 18, 9, 8, 18, 7, 9, 18, 8, 12, 9, 11, 13, 16, 12, 15, 16, 13, 14, 15, 13], "vertices": [2, 39, 33.21, 2.65, 0.02562, 40, 0.1, 8.05, 0.97438, 1, 40, 16.15, 5.89, 1, 2, 39, 36.66, -31.63, 0.04387, 40, 31.89, -5.22, 0.95613, 2, 39, 35.39, -42.3, 0.0463, 40, 40.68, -11.42, 0.9537, 3, 38, 72.71, -44.98, 4e-05, 39, 32.54, -41.98, 0.04894, 40, 39.03, -13.77, 0.95102, 3, 38, 63.46, -38.49, 0.01008, 39, 22.87, -36.14, 0.15035, 40, 29.29, -19.5, 0.83957, 3, 38, 54.77, -34.71, 0.05949, 39, 13.95, -32.96, 0.33809, 40, 22.25, -25.84, 0.60242, 3, 38, 46.04, -30.91, 0.21261, 39, 4.98, -29.76, 0.47879, 40, 15.18, -32.21, 0.3086, 3, 38, 36.79, -30.41, 0.48157, 39, -4.29, -29.89, 0.40372, 40, 10.88, -40.41, 0.11471, 3, 38, 27.66, -30.13, 0.73124, 39, -13.42, -30.24, 0.23452, 40, 6.85, -48.61, 0.03424, 3, 38, 10.14, -29.61, 0.95693, 39, -30.92, -30.92, 0.04274, 40, -0.89, -64.33, 0.00033, 2, 38, 5.33, -26.35, 0.97499, 39, -35.95, -27.99, 0.02501, 1, 38, -3.64, -9.14, 1, 1, 38, -11.36, 5.65, 1, 1, 38, -15.37, 19.6, 1, 1, 38, -14.43, 21.39, 1, 2, 38, 6.23, 35.59, 0.96846, 39, -39.28, 33.87, 0.03154, 2, 38, 17.69, 39.8, 0.89864, 39, -28.13, 38.85, 0.10136, 3, 38, 35.28, 38.22, 0.65371, 39, -10.47, 38.48, 0.34593, 40, -52.2, -13.32, 0.00036, 3, 38, 48.42, 37.05, 0.37853, 39, 2.72, 38.2, 0.6062, 40, -45.68, -1.86, 0.01527, 3, 38, 59.07, 36.09, 0.21106, 39, 13.41, 37.97, 0.73951, 40, -40.39, 7.44, 0.04943, 3, 38, 65.97, 34.51, 0.16054, 39, 20.39, 36.86, 0.76738, 40, -36.09, 13.06, 0.07208, 3, 38, 70.49, 19.09, 0.0575, 39, 25.96, 21.79, 0.7077, 40, -20.18, 10.78, 0.2348], "hull": 23, "edges": [6, 8, 8, 10, 14, 16, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 40, 42, 42, 44, 34, 36, 2, 0, 0, 44, 2, 4, 4, 6, 10, 12, 12, 14, 16, 18, 18, 20, 22, 24, 24, 26, 36, 38, 38, 40], "width": 78, "height": 105}}, "zuoshou2": {"zuoshou2": {"type": "mesh", "uvs": [0.99607, 0.00181, 0.99545, 0.04499, 0.95523, 0.15046, 0.90139, 0.20434, 0.82596, 0.28398, 0.45483, 0.67724, 0.40397, 0.73225, 0.40324, 0.78558, 0.34545, 0.81932, 0.30842, 0.82653, 0.14542, 0.97518, 0.0627, 1, 0.0552, 1, 0.00963, 0.98591, 0.01231, 0.95569, 0.03071, 0.92154, 0.1112, 0.85163, 0.1635, 0.83121, 0.16333, 0.78642, 0.21443, 0.7311, 0.25563, 0.71707, 0.41278, 0.54584, 0.8311, 0.08904, 0.9198, 0.02909, 0.97619, 0.00316], "triangles": [1, 24, 0, 23, 24, 1, 1, 22, 23, 2, 22, 1, 3, 22, 2, 4, 22, 3, 4, 21, 22, 5, 21, 4, 20, 21, 5, 6, 20, 5, 7, 20, 6, 8, 20, 7, 9, 19, 20, 9, 18, 19, 20, 8, 9, 17, 18, 9, 10, 16, 17, 15, 16, 10, 14, 15, 10, 10, 17, 9, 12, 13, 14, 14, 11, 12, 10, 11, 14], "vertices": [1, 7, -648, -198.85, 1, 1, 7, -625.71, -176.33, 1, 1, 7, -558.86, -133.35, 1, 1, 7, -514.07, -121.76, 1, 1, 7, -449.2, -103.32, 1, 1, 7, -129.27, -11.86, 1, 1, 7, -84.85, 1.26, 1, 1, 7, -57.33, 29.09, 1, 1, 7, -21.58, 28.86, 1, 1, 7, -6.05, 21.14, 1, 1, 7, 122.14, 48.64, 1, 1, 7, 161.29, 35.98, 1, 1, 7, 163.69, 33.64, 1, 1, 7, 171.04, 12.06, 1, 1, 7, 154.71, -3.01, 1, 1, 7, 131.35, -15.25, 1, 1, 7, 69.84, -26.99, 1, 1, 7, 42.67, -21.47, 1, 1, 7, 19.8, -45.09, 1, 1, 7, -24.85, -58.3, 1, 1, 7, -45.2, -52.86, 1, 1, 7, -183.08, -94.07, 1, 1, 7, -550.62, -204.27, 1, 1, 7, -609.66, -208.22, 1, 1, 7, -640.96, -204.33, 1], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 446, "height": 734}}, "youdatui2": {"youdatui2": {"type": "mesh", "uvs": [0.99435, 0.1187, 0.99447, 0.40386, 0.936, 0.56314, 0.76341, 0.74493, 0.48129, 0.88303, 0.24233, 1, 0.21802, 1, 0.09375, 0.94314, 0.00547, 0.84738, 0.00558, 0.57255, 0.04823, 0.47004, 0.21557, 0.32045, 0.38048, 0.17303, 0.65125, 0.00508, 0.88101, 0.00522], "triangles": [13, 0, 1, 0, 13, 14, 13, 2, 12, 1, 2, 13, 3, 12, 2, 11, 12, 3, 4, 11, 3, 10, 11, 4, 9, 10, 4, 8, 9, 4, 7, 8, 4, 5, 6, 7, 4, 5, 7], "vertices": [1, 22, 192.55, 1.77, 1, 1, 22, 161.42, -37.6, 1, 1, 22, 136.09, -53.3, 1, 1, 22, 92.82, -59.85, 1, 1, 22, 39.46, -48.61, 1, 1, 22, -5.73, -39.09, 1, 1, 22, -9.02, -36.48, 1, 1, 22, -19.67, -15.29, 1, 1, 22, -21.18, 7.4, 1, 1, 22, 8.86, 45.32, 1, 1, 22, 25.84, 54.88, 1, 1, 22, 64.88, 57.55, 1, 1, 22, 103.35, 60.19, 1, 1, 22, 158.43, 54.29, 1, 1, 22, 189.58, 29.6, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 6, 8, 8, 10, 20, 22, 22, 24], "width": 173, "height": 176}}, "youdatui3": {"youdatui3": {"type": "mesh", "uvs": [0.61065, 0, 0.75785, 0.04323, 0.97798, 0.21782, 1, 0.42409, 1, 0.46692, 0.96808, 0.59118, 0.70724, 0.72797, 0.47845, 0.84795, 0.39258, 0.87489, 0.2599, 0.99087, 0.06805, 0.99668, 0.00491, 0.91577, 0.00604, 0.59588, 0.16571, 0.30213, 0.4228, 0.18369, 0.46768, 0.13032, 0.58658, 0], "triangles": [3, 1, 2, 1, 3, 6, 4, 5, 3, 1, 15, 0, 5, 6, 3, 15, 6, 14, 7, 14, 6, 13, 14, 7, 8, 12, 13, 15, 16, 0, 15, 1, 6, 7, 8, 13, 9, 11, 12, 8, 9, 12, 10, 11, 9], "vertices": [1, 21, 132.75, 45.07, 1, 1, 21, 151.67, 26.66, 1, 1, 21, 171.78, -14.93, 1, 1, 21, 159.75, -43.24, 1, 1, 21, 156.56, -48.72, 1, 1, 21, 142.51, -61.83, 1, 1, 21, 93.1, -56.51, 1, 1, 21, 49.76, -51.84, 1, 1, 21, 34.84, -47.77, 1, 1, 21, 6.25, -51, 1, 1, 21, -23.03, -34.96, 1, 1, 21, -26.51, -19.09, 1, 1, 21, -2.53, 21.74, 1, 1, 21, 43.34, 45.35, 1, 1, 21, 90.82, 38.01, 1, 1, 21, 101.54, 40.91, 1, 1, 21, 129.13, 47.18, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 10, 12, 12, 14], "width": 174, "height": 148}}, "meimao2": {"meimao2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.31, -32.63, -7.7, -3.05, 18, 57.75, 88, 28.16], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 66}}, "youxiaobi2": {"youxiaobi2": {"type": "mesh", "uvs": [0.88293, 0, 1, 0.0378, 1, 0.08238, 0.98224, 0.32345, 0.86009, 0.80884, 0.71243, 1, 0.63475, 1, 0.4273, 0.9609, 0.21642, 0.71664, 0.17776, 0.72857, 0.04027, 0.7994, 0, 0.69166, 0, 0.64963, 0.14842, 0.36566, 0.3264, 0.20657, 0.72619, 0.01685, 0.87339, 0], "triangles": [6, 7, 4, 5, 6, 4, 0, 1, 2, 3, 0, 2, 12, 9, 11, 8, 13, 14, 13, 9, 12, 13, 8, 9, 10, 11, 9, 3, 4, 15, 14, 15, 4, 7, 8, 14, 16, 3, 15, 3, 16, 0, 4, 7, 14], "vertices": [1, 5, -2.93, -34.18, 1, 1, 5, -22.63, -20.34, 1, 2, 5, -20.19, -14.16, 0.99999, 91, -30.26, 2.45, 1e-05, 1, 91, 4.71, 11.38, 1, 3, 5, 45.64, 76.21, 0, 92, 32.89, 13.14, 0.47855, 93, 0.8, 13.14, 0.52145, 2, 93, 37.66, -4.88, 0.66069, 96, 46.56, 7.78, 0.33931, 3, 5, 98.03, 86.12, 0.00024, 93, 42.97, -19.48, 0.36622, 96, 33.34, -0.39, 0.63354, 2, 95, 18.34, -17.27, 0.86978, 96, -5, -17.27, 0.13022, 2, 5, 160.3, 16.09, 0.21452, 94, -1.66, -8.51, 0.78548, 2, 5, 168.15, 14.9, 0.67331, 94, -7.3, -14.09, 0.32669, 2, 5, 197.6, 14.6, 0.99961, 94, -25.13, -37.54, 0.00039, 1, 5, 199.18, -3.29, 1, 1, 5, 196.88, -9.11, 1, 1, 5, 153.72, -37.54, 1, 1, 5, 111.9, -46.49, 1, 1, 5, 27.15, -43.38, 1, 1, 5, -1.15, -34.89, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32], "width": 200, "height": 149}}, "zuojiao2": {"zuojiao2": {"type": "mesh", "uvs": [0.87417, 0.01245, 0.96898, 0.24521, 0.97612, 0.49044, 0.98401, 0.76112, 0.98162, 0.80007, 0.87283, 0.90746, 0.63714, 0.98679, 0.30543, 0.98689, 0.09524, 0.91809, 0.0094, 0.8425, 0.03072, 0.66177, 0.26291, 0.35032, 0.50142, 0.20622, 0.64685, 0.01337], "triangles": [2, 6, 12, 7, 8, 10, 9, 10, 8, 11, 12, 6, 11, 7, 10, 6, 7, 11, 5, 6, 2, 3, 5, 2, 4, 5, 3, 2, 12, 1, 1, 13, 0, 1, 12, 13], "vertices": [1, 25, -23.32, 11.46, 1, 1, 25, -6.78, 22.01, 1, 1, 26, -33.75, -19.15, 1, 1, 26, -35.67, 0.81, 1, 1, 26, -35.6, 3.7, 1, 1, 26, -25.3, 12.25, 1, 1, 26, -2.35, 19.45, 1, 1, 26, 30.44, 21.33, 1, 1, 26, 51.51, 17.44, 1, 1, 26, 60.31, 12.35, 1, 1, 26, 58.97, -1.13, 1, 1, 26, 37.34, -25.45, 1, 2, 25, -6.47, -24.37, 0.14866, 26, 14.38, -37.45, 0.85134, 2, 25, -21.7, -10.99, 0.96542, 26, 0.82, -52.52, 0.03458], "hull": 14, "edges": [0, 26, 0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 2, 4, 4, 6], "width": 99, "height": 74}}, "shengzi": {"shengzi": {"type": "mesh", "uvs": [0.08118, 0, 0.10738, 0.00905, 0.15145, 0.22828, 0.23861, 0.37381, 0.36966, 0.51519, 0.45608, 0.60843, 0.55532, 0.69203, 0.8456, 0.88159, 1, 0.96455, 1, 0.97782, 0.95744, 1, 0.9501, 1, 0.69522, 0.89379, 0.54881, 0.79219, 0.32859, 0.63936, 0.24009, 0.52843, 0.12363, 0.38245, 0.00344, 0.16937, 0.01476, 0.03605, 0.05657, 0], "triangles": [5, 14, 15, 5, 15, 4, 14, 5, 6, 13, 14, 6, 13, 6, 7, 12, 13, 7, 11, 7, 8, 9, 11, 8, 12, 7, 11, 10, 11, 9, 19, 0, 1, 18, 19, 1, 17, 18, 1, 17, 1, 2, 16, 17, 2, 16, 2, 3, 15, 3, 4, 16, 3, 15], "vertices": [1, 73, -17.8, 10.07, 1, 1, 73, -14.19, 12.06, 1, 2, 73, 46.89, -6.37, 0.95922, 75, -182.84, -6.37, 0.04078, 2, 73, 90.02, -12.09, 0.74594, 75, -139.71, -12.09, 0.25406, 2, 73, 133.97, -12.43, 0.46482, 75, -95.76, -12.43, 0.53518, 2, 73, 162.96, -12.65, 0.29238, 75, -66.77, -12.65, 0.70762, 2, 73, 189.93, -10.4, 0.15029, 75, -39.81, -10.4, 0.84971, 1, 75, 24.23, 2.03, 1, 1, 75, 53.48, 10.56, 1, 1, 75, 57.05, 9.14, 1, 1, 75, 61.14, 1.99, 1, 1, 75, 60.82, 1.16, 1, 2, 73, 250.56, -16.19, 0.00022, 75, 20.83, -16.19, 0.99978, 2, 73, 216.64, -21.82, 0.05252, 75, -13.09, -21.82, 0.94748, 2, 73, 165.63, -30.3, 0.28238, 75, -64.1, -30.3, 0.71762, 2, 73, 131.78, -28.42, 0.47541, 75, -97.95, -28.42, 0.52459, 2, 73, 87.23, -25.95, 0.75564, 75, -142.5, -25.95, 0.24436, 2, 73, 24.42, -16.74, 0.99674, 75, -205.31, -16.74, 0.00326, 1, 73, -11.03, -1.24, 1, 1, 73, -18.89, 7.3, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 32, 34, 34, 36, 36, 38, 6, 8, 8, 10, 28, 30, 30, 32, 24, 26, 26, 28], "width": 121, "height": 290}}, "wu2": {"wu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-88.77, -27.43, -34.93, 187.94, 602.46, 28.59, 548.61, -186.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 155, "height": 459}}, "wu3": {"wu1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-115.81, -235.67, 38.9, 331.61, 600.4, 178.48, 445.68, -388.8], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 411, "height": 407}}, "wu4": {"wu1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-115.81, -235.67, 38.9, 331.61, 600.4, 178.48, 445.68, -388.8], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 411, "height": 407}}, "wu5": {"wu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-88.77, -27.43, -34.93, 187.94, 602.46, 28.59, 548.61, -186.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 155, "height": 459}}, "wu6": {"wu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-88.77, -27.43, -34.93, 187.94, 602.46, 28.59, 548.61, -186.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 155, "height": 459}}, "kuzi": {"kuzi": {"type": "mesh", "uvs": [0.83407, 0.08745, 0.98048, 0.16576, 0.86744, 0.26133, 0.79709, 0.39191, 0.74227, 0.56087, 0.75979, 0.77105, 0.66355, 0.76111, 0.55063, 0.7871, 0.49628, 0.80778, 0.33392, 0.90253, 0.23855, 0.95819, 0.16692, 1, 0.13492, 1, 0.07354, 0.80592, 0.12082, 0.70683, 0.18361, 0.5752, 0.05531, 0.53737, 0, 0.48618, 0, 0.47548, 0.09043, 0.48515, 0.20752, 0.44086, 0.36123, 0.32205, 0.28484, 0.20665, 0.46899, 0.11673, 0.59958, 0.05297, 0.68847, 0.00957], "triangles": [11, 12, 10, 12, 13, 10, 10, 13, 9, 13, 14, 9, 9, 14, 8, 16, 19, 15, 15, 19, 20, 16, 17, 19, 17, 18, 19, 7, 8, 15, 8, 14, 15, 6, 7, 4, 21, 15, 20, 3, 21, 23, 3, 23, 24, 21, 22, 23, 0, 24, 25, 24, 0, 2, 6, 4, 5, 2, 0, 1, 4, 7, 15, 21, 4, 15, 4, 21, 3, 3, 24, 2], "vertices": [2, 50, -13.56, 2.15, 0.216, 109, 3.85, -10.1, 0.784, 1, 50, -8.44, 23.17, 1, 1, 50, 8.27, 12.97, 1, 1, 50, 28.1, 9.38, 1, 1, 50, 52.45, 9.18, 1, 1, 50, 79.66, 19.46, 1, 2, 50, 81.79, 7.25, 0.21045, 112, 5.53, 6.98, 0.78955, 2, 50, 89.29, -5.62, 0.09599, 112, 20.02, 3.51, 0.90401, 1, 112, 27.51, 2.84, 1, 1, 113, 16.12, 4.88, 1, 2, 113, 30.49, 6.08, 0.00684, 114, 6.58, 6.08, 0.99316, 1, 114, 17.38, 6.98, 1, 1, 114, 21.02, 5.1, 1, 5, 50, 108.89, -63.52, 0.00778, 110, 74.26, 85.23, 0.01456, 112, 75.42, -22.29, 0.00546, 113, 39.57, -22.29, 0.26473, 114, 15.66, -22.29, 0.70748, 5, 50, 94.07, -61.54, 0.04285, 110, 67.69, 71.8, 0.04806, 112, 63.76, -31.65, 0.05498, 113, 27.9, -31.65, 0.48171, 114, 4, -31.65, 0.37241, 5, 50, 74.38, -58.91, 0.17234, 110, 58.96, 53.96, 0.17686, 112, 48.26, -44.07, 0.16746, 113, 12.4, -44.07, 0.42924, 114, -11.5, -44.07, 0.05409, 5, 50, 73.97, -76.14, 0.18495, 110, 75.17, 48.11, 0.22353, 112, 60.44, -56.27, 0.16754, 113, 24.58, -56.27, 0.42035, 114, 0.67, -56.27, 0.00363, 5, 50, 69.17, -84.92, 0.18241, 110, 81.98, 40.78, 0.22462, 112, 63.47, -65.8, 0.1672, 113, 27.61, -65.8, 0.42571, 114, 3.71, -65.8, 7e-05, 4, 50, 67.75, -85.33, 0.18235, 110, 81.92, 39.31, 0.22463, 112, 62.79, -67.11, 0.16719, 113, 26.93, -67.11, 0.42583, 5, 50, 65.79, -73.85, 0.1883, 110, 70.4, 41.08, 0.22458, 112, 53.13, -60.6, 0.16777, 113, 17.27, -60.6, 0.41387, 114, -6.63, -60.6, 0.00548, 5, 50, 55.73, -61.17, 0.23589, 110, 55.19, 35.55, 0.26854, 112, 37.01, -59.12, 0.16337, 113, 1.15, -59.12, 0.32461, 114, -22.75, -59.12, 0.00759, 4, 50, 34.47, -46.88, 0.21307, 110, 34.9, 19.93, 0.66086, 112, 12, -64.62, 0.05256, 113, -23.86, -64.62, 0.07352, 4, 50, 21.93, -60.73, 0.02471, 110, 44.06, 3.64, 0.95559, 112, 13.35, -83.26, 0.00759, 113, -22.51, -83.26, 0.01211, 2, 109, 50.71, -7.86, 0.00128, 110, 20.03, -7.86, 0.99872, 2, 109, 33.66, -16.01, 0.49177, 110, 2.99, -16.01, 0.50823, 2, 109, 22.07, -21.56, 0.86133, 110, -8.61, -21.56, 0.13867], "hull": 26, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 2, 0, 0, 50, 16, 18, 26, 28, 28, 30, 46, 48, 48, 50, 18, 20, 20, 22], "width": 128, "height": 138}}, "zuodabi2": {"zuodabi2": {"type": "mesh", "uvs": [0.86504, 0.022, 0.98278, 0.12413, 0.99543, 0.25816, 0.91109, 0.51127, 0.7454, 0.70178, 0.59662, 0.81142, 0.45097, 0.88002, 0.27259, 0.96404, 0.21211, 0.96256, 0.06215, 0.83501, 0, 0.62223, 0, 0.57268, 0.01874, 0.49505, 0.21521, 0.39596, 0.26817, 0.36463, 0.48674, 0.1867, 0.61497, 0.07732, 0.54089, 0.05006, 0.7353, 0.00176], "triangles": [16, 17, 18, 0, 2, 18, 2, 0, 1, 13, 10, 11, 18, 3, 16, 2, 3, 18, 3, 15, 16, 4, 15, 3, 14, 15, 4, 5, 14, 4, 13, 14, 5, 12, 13, 11, 13, 6, 10, 10, 6, 9, 13, 5, 6, 7, 8, 9, 6, 7, 9], "vertices": [1, 3, -4.23, -17.67, 1, 1, 3, -2.94, 9.2, 1, 1, 3, 14.17, 27.8, 1, 1, 3, 58.25, 50.24, 1, 1, 3, 102.54, 55.12, 1, 1, 3, 133.84, 51.66, 1, 1, 3, 159.14, 43.33, 1, 1, 3, 190.12, 33.12, 1, 1, 3, 196.49, 25.83, 1, 1, 3, 195.2, -8.06, 1, 1, 3, 172.61, -42.54, 1, 1, 3, 165.77, -48.87, 1, 1, 3, 153.03, -56.59, 1, 1, 3, 118, -46.18, 1, 1, 3, 107.92, -43.97, 1, 1, 3, 59.62, -41.04, 1, 1, 3, 30.59, -39.96, 1, 1, 3, 34.88, -52.14, 1, 1, 3, 7.08, -35.49, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 10, 12, 12, 14], "width": 160, "height": 188}}, "zuodabi3": {"zuodabi3": {"type": "mesh", "uvs": [0.85153, 0, 0.98234, 0.05468, 0.99409, 0.22882, 1, 0.31643, 1, 0.37221, 0.92848, 0.49611, 0.84212, 0.64571, 0.77948, 0.70897, 0.69495, 0.79433, 0.60345, 0.88675, 0.53359, 0.93408, 0.43945, 0.99786, 0.37702, 0.99332, 0.32627, 0.8972, 0.27312, 0.79655, 0.20333, 0.66439, 0.1781, 0.67728, 0.13251, 0.7026, 0.05751, 0.74424, 0, 0.65681, 0, 0.61107, 0.1108, 0.50678, 0.13594, 0.48726, 0.12447, 0.31966, 0.17728, 0.26624, 0.30411, 0.2431, 0.48699, 0.16544, 0.63235, 0.08607, 0.77087, 0.01043, 0.82981, 0], "triangles": [10, 13, 14, 11, 12, 13, 10, 11, 13, 8, 15, 7, 9, 14, 8, 9, 10, 14, 4, 5, 3, 6, 26, 5, 7, 26, 6, 22, 23, 24, 7, 25, 26, 5, 27, 28, 5, 26, 27, 7, 15, 25, 25, 22, 24, 29, 5, 28, 5, 2, 3, 2, 0, 1, 19, 20, 17, 15, 22, 25, 21, 22, 15, 21, 16, 20, 21, 15, 16, 16, 17, 20, 18, 19, 17, 14, 15, 8, 2, 5, 29, 0, 2, 29], "vertices": [1, 6, -25.71, -61.88, 1, 1, 6, -46.72, -40.17, 1, 1, 6, -34.36, -4.92, 1, 1, 6, -28.14, 12.81, 1, 2, 103, -7.12, 84.04, 0.00029, 106, -3.2, 4.68, 0.99971, 1, 106, 26.57, 11.67, 1, 2, 106, 62.52, 20.11, 0.99937, 107, -27.48, 20.11, 0.00063, 2, 106, 81.52, 19.62, 0.82183, 107, -8.48, 19.62, 0.17817, 2, 106, 107.16, 18.96, 0.06367, 107, 17.16, 18.96, 0.93633, 2, 107, 44.92, 18.24, 0.99893, 108, -15.08, 18.24, 0.00107, 2, 107, 62.48, 14.3, 0.471, 108, 2.48, 14.3, 0.529, 2, 107, 86.15, 8.98, 0, 108, 26.15, 8.98, 1, 2, 6, 150.84, 93.78, 0.00077, 108, 34.53, -1.39, 0.99923, 3, 6, 152.68, 70.62, 0.13334, 107, 86.89, -23.34, 0.10841, 108, 26.89, -23.34, 0.75825, 3, 6, 154.62, 46.36, 0.46767, 107, 78.9, -46.32, 0.21381, 108, 18.9, -46.32, 0.31852, 1, 6, 157.16, 14.52, 1, 1, 6, 163.18, 14.94, 1, 3, 6, 174.24, 16.1, 0.99878, 107, 84.68, -81.93, 0.00053, 108, 24.69, -81.93, 0.00068, 1, 6, 192.44, 18.01, 1, 1, 6, 196.34, -4.01, 1, 2, 6, 192.49, -13.01, 0.99958, 105, 78.43, 45.42, 0.00042, 1, 6, 162.01, -24.25, 1, 1, 6, 155.44, -25.98, 1, 2, 6, 143.58, -59.92, 0.00057, 105, 29.13, -1.07, 0.99943, 1, 105, 14.24, -7.03, 1, 1, 104, 39.54, -0.73, 1, 2, 103, 75.23, -0.33, 0.99204, 104, -2.81, -0.33, 0.00796, 1, 103, 40.05, -3.47, 1, 1, 103, 6.53, -6.46, 1, 1, 6, -21.45, -63.7, 1], "hull": 30, "edges": [0, 58, 0, 2, 6, 8, 22, 24, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 56, 58, 28, 30, 24, 26, 26, 28, 16, 18, 8, 10, 10, 12, 52, 54, 54, 56, 2, 4, 4, 6, 18, 20, 20, 22, 12, 14, 14, 16, 32, 34, 34, 36], "width": 213, "height": 214}}, "yachi": {"yachi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [151.72, -36.73, -38.71, -73.76, -61.04, 41.09, 129.39, 78.12], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 117}}, "zuoxiaotui2": {"zuoxiaotui2": {"type": "mesh", "uvs": [0.46267, 0.05153, 0.69809, 0.08039, 0.87003, 0.17456, 1, 0.34825, 1, 0.40317, 0.96855, 0.52985, 0.96749, 0.58733, 0.62985, 0.62147, 0.62408, 0.66199, 0.50564, 0.70801, 0.30031, 0.89841, 0.29875, 0.97894, 0.11011, 1, 0.08631, 1, 0.00027, 0.93612, 0.06172, 0.80849, 0.1476, 0.63013, 0.15522, 0.57948, 0.10723, 0.44945, 0.0415, 0.2714, 0.02629, 0.09182, 0.08734, 0.05339, 0.22813, 0.02277], "triangles": [19, 20, 21, 7, 2, 3, 22, 19, 21, 0, 18, 19, 3, 4, 7, 18, 7, 17, 22, 0, 19, 4, 5, 7, 7, 0, 1, 7, 1, 2, 6, 7, 5, 7, 18, 0, 9, 16, 17, 17, 7, 9, 8, 9, 7, 10, 16, 9, 15, 16, 10, 12, 14, 15, 14, 12, 13, 12, 15, 10, 11, 12, 10], "vertices": [1, 24, -30.35, -9.29, 1, 1, 24, -34.02, 27.34, 1, 1, 24, -21.08, 58.5, 1, 1, 24, 10.6, 88.23, 1, 1, 24, 22.33, 91.54, 1, 1, 24, 50.71, 94.51, 1, 1, 24, 63.04, 97.81, 1, 1, 24, 84.45, 49.82, 1, 1, 24, 93.35, 51.41, 1, 1, 24, 108.13, 36.63, 1, 1, 24, 157.39, 17.66, 1, 1, 24, 174.66, 22.28, 1, 1, 24, 187.05, -4.41, 1, 1, 24, 188.04, -7.94, 1, 1, 24, 177.99, -24.54, 1, 1, 24, 148.15, -23.12, 1, 1, 24, 106.45, -21.13, 1, 1, 24, 95.31, -23.06, 1, 1, 24, 69.53, -38, 1, 1, 24, 34.23, -58.47, 1, 1, 24, -3.5, -71.54, 1, 1, 24, -14.26, -64.81, 1, 1, 24, -26.69, -45.79, 1], "hull": 23, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 38, 40, 40, 42, 42, 44, 34, 36, 36, 38, 2, 0, 0, 44, 28, 30, 30, 32], "width": 154, "height": 222}}, "zuidatui2": {"zuidatui2": {"type": "mesh", "uvs": [0.72872, 0.20241, 0.87451, 0.31436, 0.97673, 0.51231, 0.99625, 0.57069, 0.97079, 0.7534, 0.8166, 0.90505, 0.65301, 0.96511, 0.38229, 0.87947, 0.14814, 0.8054, 0.01794, 0.68313, 0.01847, 0.30305, 0.13476, 0.15606, 0.27986, 0.08316, 0.48891, 0.01828], "triangles": [2, 5, 1, 4, 2, 3, 8, 11, 12, 10, 11, 8, 9, 10, 8, 7, 12, 13, 7, 13, 0, 8, 12, 7, 5, 0, 1, 4, 5, 2, 6, 7, 0, 5, 6, 0], "vertices": [1, 46, 58.98, 25.65, 1, 1, 46, 76.49, 22.47, 1, 1, 46, 91.41, 11.8, 1, 1, 46, 94.72, 8.31, 1, 1, 46, 96.15, -5.41, 1, 1, 46, 83.63, -21.11, 1, 1, 46, 68.11, -30.65, 1, 1, 46, 38.32, -33.37, 1, 1, 46, 12.55, -35.73, 1, 1, 46, -3.58, -31.31, 1, 1, 46, -11.96, -4.47, 1, 1, 46, -3.24, 9.68, 1, 1, 46, 10.09, 19.52, 1, 1, 46, 30.19, 30.88, 1], "hull": 14, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 2, 0, 0, 26, 12, 14, 14, 16], "width": 108, "height": 74}}, "texiao4": {"texiao": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-135.97, -98.64, -121.47, 1216.28, 631.48, 1207.98, 616.99, -106.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 920, "height": 527}}, "meimao1": {"meimao1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-10.37, -6.03, 41.5, 48.14, 105.78, -13.42, 53.91, -67.59], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 75, "height": 89}}, "youxuiaotui2": {"youxuiaotui2": {"type": "mesh", "uvs": [0.49926, 0, 0.66109, 0.02985, 0.74323, 0.12566, 0.80231, 0.19458, 0.87854, 0.28351, 0.93618, 0.35075, 0.98952, 0.44681, 0.92606, 0.54014, 0.86096, 0.6359, 0.88999, 0.71376, 1, 0.88303, 1, 0.93644, 0.93987, 0.97161, 0.84955, 1, 0.82434, 1, 0.72037, 0.98219, 0.72027, 0.93203, 0.65293, 0.86122, 0.53027, 0.79275, 0.3925, 0.71584, 0.3942, 0.7132, 0.22796, 0.69401, 0.06784, 0.67553, 0.00025, 0.61843, 0.05589, 0.48148, 0.12143, 0.32018, 0.15469, 0.25214, 0.14195, 0.18093, 0.12699, 0.09732, 0.23416, 0.05478, 0.37219, 0], "triangles": [4, 20, 21, 24, 21, 23, 22, 23, 21, 7, 5, 6, 7, 4, 5, 3, 4, 26, 2, 0, 1, 2, 26, 0, 26, 2, 3, 21, 24, 25, 27, 28, 29, 26, 27, 29, 29, 0, 26, 26, 4, 25, 21, 25, 4, 29, 30, 0, 20, 4, 7, 8, 20, 7, 18, 20, 8, 9, 18, 8, 19, 20, 18, 17, 18, 9, 17, 9, 10, 16, 17, 10, 12, 16, 10, 11, 12, 10, 14, 15, 16, 16, 13, 14, 12, 13, 16], "vertices": [2, 23, -12.42, 22.25, 0.70877, 100, -35.27, -11.13, 0.29123, 2, 23, 5.54, 47.93, 0.05202, 100, -13.51, 11.42, 0.94798, 1, 100, 18.36, 11.87, 1, 1, 100, 41.29, 12.19, 1, 2, 100, 70.87, 12.61, 0.02774, 101, 10.8, 12.61, 0.97226, 2, 101, 33.17, 12.92, 0.80597, 102, -6.88, 12.92, 0.19403, 2, 101, 62.61, 8.6, 7e-05, 102, 22.56, 8.6, 0.99993, 2, 23, 162.48, 46.54, 0.38721, 102, 41.15, -14.56, 0.61279, 2, 23, 185.04, 26.06, 0.89607, 102, 60.22, -38.32, 0.10393, 2, 23, 208.3, 23.84, 0.99489, 102, 82.85, -44.16, 0.00511, 1, 23, 261.69, 27.27, 1, 1, 23, 276.45, 22.24, 1, 1, 23, 282.57, 8.35, 1, 1, 23, 285, -10.23, 1, 1, 23, 283.48, -14.67, 1, 1, 23, 272.33, -31.3, 1, 2, 23, 258.46, -26.59, 0.99979, 99, 87.26, 131.58, 0.00021, 3, 23, 234.85, -31.78, 0.99007, 98, 109.62, 115.45, 0.00014, 99, 69.25, 115.45, 0.00978, 3, 23, 208.56, -46.93, 0.9114, 98, 94.17, 89.34, 0.01086, 99, 53.81, 89.34, 0.07774, 3, 23, 179.04, -63.94, 0.6274, 98, 76.83, 60.01, 0.06633, 99, 36.47, 60.01, 0.30626, 3, 23, 178.41, -63.4, 0.62238, 98, 76.01, 60.18, 0.06723, 99, 35.65, 60.18, 0.31039, 3, 23, 163.14, -90.86, 0.20781, 98, 76.21, 28.76, 0.04873, 99, 35.85, 28.76, 0.74346, 2, 23, 148.43, -117.31, 0.0089, 99, 36.04, -1.51, 0.9911, 2, 98, 62.34, -16.94, 0.00036, 99, 21.97, -16.94, 0.99964, 2, 98, 21.13, -14.15, 0.9982, 99, -19.24, -14.15, 0.0018, 1, 97, 33.13, -10.86, 1, 1, 97, 12.46, -8.44, 1, 2, 23, 16.16, -57.7, 0.06978, 97, -7.54, -14.61, 0.93022, 2, 23, -7.84, -52.46, 0.32587, 97, -31.02, -21.85, 0.67413, 2, 23, -13.17, -29.58, 0.64252, 97, -46.9, -4.55, 0.35748, 3, 23, -20.04, -0.12, 0.96635, 97, -67.36, 17.73, 0.01734, 100, -46.31, -32.03, 0.01631], "hull": 31, "edges": [0, 60, 0, 2, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 44, 46, 50, 52, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 60, 12, 14, 14, 16, 40, 42, 42, 44, 34, 36, 36, 38, 6, 8, 8, 10, 2, 4, 4, 6], "width": 186, "height": 292}}, "xuanzhuan1": {"xuanzhuan1": {"type": "mesh", "uvs": [0.24669, 0.01159, 0.44253, 0.01164, 0.63494, 0.01169, 0.83531, 0.07843, 0.91065, 0.14879, 0.9845, 0.21777, 0.98483, 0.42624, 0.98505, 0.5605, 0.98519, 0.65085, 0.98539, 0.7718, 0.88124, 0.89205, 0.79935, 0.94076, 0.62986, 0.95648, 0.36755, 0.98082, 0.16075, 1, 0.13537, 1, 0.01361, 0.90094, 0.0142, 0.68997, 0.01484, 0.45976, 0.01537, 0.26702, 0.05461, 0.13757, 0.18639, 0.0336], "triangles": [7, 20, 6, 3, 4, 2, 6, 4, 5, 6, 2, 4, 7, 19, 20, 10, 8, 9, 21, 0, 1, 1, 20, 21, 2, 6, 1, 1, 6, 20, 19, 7, 8, 19, 8, 18, 8, 10, 12, 8, 17, 18, 8, 12, 17, 17, 13, 16, 12, 10, 11, 12, 13, 17, 14, 15, 16, 13, 14, 16], "vertices": [1, 162, 44.19, -8.25, 1, 1, 162, 37.08, -18.8, 1, 1, 162, 30.09, -29.18, 1, 1, 162, 18.22, -36.88, 1, 1, 162, 10.64, -37.68, 1, 1, 162, 3.21, -38.46, 1, 1, 162, -11.15, -28.82, 1, 1, 162, -20.4, -22.6, 1, 1, 162, -26.63, -18.42, 1, 1, 162, -34.96, -12.82, 1, 1, 162, -39.46, -1.63, 1, 1, 162, -39.84, 5.04, 1, 1, 162, -34.77, 14.91, 1, 1, 162, -26.92, 30.18, 1, 1, 162, -20.73, 42.22, 1, 1, 162, -19.81, 43.58, 1, 1, 162, -8.56, 45.55, 1, 1, 162, 5.94, 35.74, 1, 1, 162, 21.76, 25.03, 1, 1, 162, 35.01, 16.07, 1, 1, 162, 42.5, 7.95, 1, 1, 162, 44.87, -3.98, 1], "hull": 22, "edges": [0, 42, 4, 6, 18, 20, 20, 22, 28, 30, 30, 32, 38, 40, 40, 42, 10, 12, 12, 14, 36, 38, 32, 34, 34, 36, 26, 28, 22, 24, 24, 26, 0, 2, 2, 4, 14, 16, 16, 18, 6, 8, 8, 10], "width": 65, "height": 83}}, "xuanzhuan2": {"xuanzhuan2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-77.63, -15.51, -15.62, 76.56, 76.44, 14.55, 14.43, -77.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 111, "height": 111}}, "shidenglong": {"shidenglong": {"type": "mesh", "uvs": [0.37002, 0.00255, 0.68285, 0.10906, 0.76867, 0.14523, 0.79757, 0.16636, 0.86382, 0.16658, 0.95858, 0.20386, 0.9461, 0.20219, 0.78496, 0.23657, 0.73202, 0.23034, 0.73195, 0.36621, 0.74222, 0.37842, 0.8865, 0.41179, 0.99556, 0.48073, 0.99052, 0.49891, 0.8312, 0.5093, 0.75326, 0.54799, 0.8279, 0.59697, 0.69693, 0.62081, 0.65047, 0.63599, 0.69392, 0.69871, 0.64743, 0.74434, 0.84774, 0.77531, 0.8772, 0.82839, 0.84538, 0.96225, 0.6461, 0.99852, 0.07642, 0.99841, 0.06704, 1, 0.06794, 0.77187, 0.00333, 0.77643, 0.00404, 0.70711, 0.06804, 0.6985, 0.0676, 0.64954, 0.00445, 0.62039, 0.00449, 0.08332, 0.0379, 0.07795, 0.10753, 0.0864, 0.16599, 0.08177, 0.35656, 0.00562], "triangles": [5, 6, 4, 8, 1, 2, 8, 2, 3, 7, 8, 3, 7, 3, 4, 7, 4, 6, 8, 0, 1, 12, 14, 11, 14, 10, 11, 13, 14, 12, 15, 10, 14, 33, 34, 35, 32, 33, 35, 0, 8, 9, 18, 10, 15, 10, 18, 9, 17, 15, 16, 0, 9, 37, 15, 17, 18, 9, 36, 37, 9, 35, 36, 9, 31, 32, 35, 9, 32, 18, 31, 9, 30, 31, 18, 20, 30, 18, 19, 20, 18, 27, 29, 30, 27, 30, 20, 28, 29, 27, 20, 22, 24, 22, 20, 21, 24, 25, 27, 20, 24, 27, 22, 23, 24, 25, 26, 27], "vertices": [1, 90, 564.27, -9.3, 1, 1, 90, 500.29, -77.46, 1, 1, 90, 478.6, -96.11, 1, 1, 90, 465.99, -102.34, 1, 1, 90, 465.71, -116.91, 1, 1, 90, 443.34, -137.53, 1, 1, 90, 444.36, -134.79, 1, 1, 90, 424.31, -99.13, 1, 1, 90, 428.14, -87.52, 1, 1, 90, 347.43, -86.66, 1, 1, 90, 340.16, -88.84, 1, 1, 90, 320.01, -120.38, 1, 1, 90, 278.8, -143.94, 1, 1, 90, 268.02, -142.72, 1, 1, 90, 262.21, -107.6, 1, 1, 90, 239.42, -90.22, 1, 1, 90, 210.15, -106.33, 1, 1, 90, 196.29, -77.37, 1, 1, 90, 187.38, -67.06, 1, 1, 90, 150.03, -76.22, 1, 1, 90, 123.03, -65.71, 1, 1, 90, 104.18, -109.59, 1, 1, 90, 72.58, -115.74, 1, 1, 90, -6.85, -107.91, 1, 1, 90, -27.94, -63.84, 1, 1, 90, -26.56, 61.48, 1, 1, 90, -27.48, 63.55, 1, 1, 90, 108.02, 61.94, 1, 1, 90, 105.46, 76.18, 1, 1, 90, 146.63, 75.59, 1, 1, 90, 151.59, 61.46, 1, 1, 90, 180.67, 61.25, 1, 1, 90, 198.14, 74.96, 1, 1, 90, 517.14, 71.61, 1, 1, 90, 520.25, 64.23, 1, 1, 90, 515.07, 48.96, 1, 1, 90, 517.69, 36.08, 1, 1, 90, 562.48, -6.32, 1], "hull": 38, "edges": [0, 74, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74], "width": 220, "height": 594}}, "tou2": {"tou2": {"type": "mesh", "uvs": [0.60795, 0, 0.65339, 0.01243, 0.68895, 0.0363, 0.71504, 0.06784, 0.71078, 0.08687, 0.86762, 0.19405, 0.88316, 0.22312, 0.92377, 0.29592, 0.95614, 0.47225, 0.94199, 0.5339, 0.9834, 0.55602, 0.96132, 0.6107, 0.9153, 0.65773, 0.87276, 0.68491, 0.88782, 0.74678, 0.81114, 0.82722, 0.81651, 0.82984, 0.81649, 0.89694, 0.78524, 0.95349, 0.77289, 0.96351, 0.74037, 0.92527, 0.68879, 0.91514, 0.63418, 0.97599, 0.52167, 0.99917, 0.52343, 0.96157, 0.37879, 0.97844, 0.29229, 0.96595, 0.28626, 0.95778, 0.29792, 0.92867, 0.31771, 0.90762, 0.16476, 0.85122, 0.16362, 0.78922, 0.09871, 0.78373, 0.03183, 0.79212, 0.02643, 0.80162, 0.0461, 0.70997, 0.0728, 0.68703, 0.10124, 0.67183, 0.00718, 0.57147, 0.00032, 0.5271, 0.03976, 0.3997, 0.08319, 0.36584, 0.07311, 0.34137, 0.06317, 0.29486, 0.07206, 0.254, 0.0937, 0.2887, 0.10472, 0.30039, 0.26704, 0.16383, 0.34583, 0.11457, 0.4696, 0.07124, 0.46314, 0.04704, 0.45949, 0.03519, 0.47735, 0.02076, 0.56869, 0, 0.30183, 0.47297, 0.35598, 0.46613, 0.40359, 0.47449, 0.45494, 0.50871, 0.49788, 0.53684, 0.51002, 0.56649, 0.67245, 0.60451, 0.70606, 0.58702, 0.75927, 0.57942, 0.80782, 0.57942, 0.83115, 0.60755, 0.85449, 0.61287], "triangles": [50, 51, 52, 49, 50, 52, 4, 2, 3, 43, 44, 45, 46, 42, 43, 46, 43, 45, 41, 42, 46, 48, 56, 47, 55, 47, 56, 54, 46, 47, 54, 41, 46, 47, 55, 54, 49, 56, 48, 2, 53, 0, 53, 49, 52, 1, 2, 0, 53, 4, 49, 4, 53, 2, 4, 57, 56, 4, 56, 49, 6, 57, 4, 6, 4, 5, 58, 57, 6, 7, 58, 6, 7, 8, 58, 8, 62, 58, 59, 58, 60, 54, 38, 39, 62, 8, 63, 9, 63, 8, 62, 61, 58, 61, 60, 58, 65, 64, 63, 11, 9, 10, 9, 65, 63, 11, 65, 9, 12, 65, 11, 54, 40, 41, 40, 54, 39, 37, 38, 54, 13, 65, 12, 32, 36, 37, 35, 36, 32, 59, 31, 37, 32, 37, 31, 33, 35, 32, 33, 34, 35, 63, 64, 62, 61, 64, 60, 13, 64, 65, 15, 13, 14, 59, 30, 31, 17, 15, 16, 37, 54, 59, 55, 57, 54, 56, 57, 55, 57, 59, 54, 59, 57, 58, 29, 30, 59, 59, 24, 29, 15, 21, 60, 60, 24, 59, 64, 61, 62, 60, 64, 13, 13, 15, 60, 20, 21, 15, 20, 15, 17, 18, 20, 17, 60, 21, 24, 19, 20, 18, 26, 27, 28, 22, 24, 21, 25, 29, 24, 28, 29, 25, 26, 28, 25, 23, 24, 22], "vertices": [360.28, 10.31, 357.05, -4.48, 349.07, -16.66, 337.8, -26.19, 330.31, -25.66, 294.37, -79.46, 283.7, -85.56, 257.02, -101.4, 190.12, -119.02, 165.86, -117.18, 158.75, -131.12, 136.9, -126.5, 117.18, -114.03, 105.23, -101.82, 81.88, -109.16, 48.22, -88.48, 47.4, -90.27, 21.52, -93.1, -1.37, -85.68, -5.66, -82.22, 7.97, -70.39, 10.1, -53.76, -15.25, -39.18, -28.06, -4.82, -13.5, -3.78, -24.98, 40.94, -23.14, 68.64, -20.2, 70.88, -8.57, 68.45, 0.23, 63.12, 16.71, 113.55, 40.59, 116.53, 40.47, 137.15, 34.93, 157.8, 31.08, 159.1, 67.11, 156.79, 76.87, 149.38, 83.72, 141.09, 119.19, 174.87, 136.07, 178.9, 186.56, 171.9, 201.11, 159.69, 210.2, 163.89, 227.8, 168.98, 243.86, 167.91, 231.23, 159.65, 227.1, 155.69, 285.36, 110.48, 307.07, 87.81, 328.04, 50.76, 337.15, 53.81, 341.6, 55.46, 347.77, 50.46, 358.93, 22.65, 167.32, 86.48, 171.82, 69.76, 170.24, 54.45, 158.81, 36.88, 149.44, 22.2, 138.42, 17.13, 129.35, -35.5, 137.25, -45.32, 142.01, -61.71, 143.68, -76.96, 133.64, -85.48, 132.39, -93.03], "hull": 54, "edges": [0, 106, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106], "width": 316, "height": 388}}, "tiankogn": {"tiankogn": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [684, -139, -818, -139, -818, 1363, 684, 1363], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 536, "height": 536}}, "wu1": {"wu1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-115.81, -235.67, 38.9, 331.61, 600.4, 178.48, 445.68, -388.8], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 411, "height": 407}}, "huzi1": {"huzi1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [84.89, -1.07, 28.37, -44.04, -7.94, 3.73, 48.58, 46.7], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "height": 60}}, "huzi2": {"huzi2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-12.14, 22.81, 97.86, 21.83, 97.42, -27.17, -12.58, -26.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 110, "height": 49}}, "yingzi": {"yingzi": {"type": "mesh", "uvs": [0.68728, 0.0132, 0.7038, 0.05768, 0.70446, 0.1486, 0.68919, 0.25268, 0.6292, 0.38737, 0.69198, 0.39991, 0.74025, 0.33335, 0.79207, 0.2345, 0.898, 0.17222, 0.93196, 0.16169, 0.95611, 0.23024, 0.96693, 0.28748, 0.98995, 0.45727, 0.98795, 0.54486, 0.9641, 0.62553, 0.91363, 0.72383, 0.90275, 0.7503, 0.88604, 0.87081, 0.8549, 0.94598, 0.81494, 0.96572, 0.75503, 0.98165, 0.70809, 0.95302, 0.69787, 0.91243, 0.65125, 0.87438, 0.60498, 0.78087, 0.58987, 0.77147, 0.49283, 0.80073, 0.42631, 0.7528, 0.36917, 0.69591, 0.32365, 0.668, 0.33561, 0.77855, 0.33314, 0.789, 0.32945, 0.77816, 0.32351, 0.67454, 0.2751, 0.67498, 0.26959, 0.66561, 0.22488, 0.68902, 0.19418, 0.68023, 0.13897, 0.58842, 0.12574, 0.57097, 0.05848, 0.56094, 0.0295, 0.5222, 0.0149, 0.48062, 0.01576, 0.44454, 0.07065, 0.39591, 0.10145, 0.38623, 0.14028, 0.41783, 0.1753, 0.37814, 0.28601, 0.26949, 0.32451, 0.24941, 0.41744, 0.23014, 0.48034, 0.23051, 0.48678, 0.24247, 0.53649, 0.19518, 0.53648, 0.17303, 0.57104, 0.101, 0.63813, 0.0119, 0.67114, 0.00186], "triangles": [53, 54, 55, 0, 2, 57, 2, 0, 1, 3, 4, 55, 53, 55, 4, 56, 3, 55, 56, 57, 3, 2, 3, 57, 41, 43, 44, 42, 43, 41, 12, 14, 11, 40, 41, 44, 39, 45, 46, 40, 44, 45, 39, 40, 45, 38, 39, 46, 10, 11, 8, 13, 14, 12, 35, 47, 48, 29, 48, 49, 35, 48, 29, 29, 34, 35, 33, 34, 29, 36, 37, 47, 38, 46, 47, 37, 38, 47, 35, 36, 47, 28, 49, 50, 29, 49, 28, 30, 33, 29, 50, 27, 28, 52, 50, 51, 8, 11, 15, 10, 8, 9, 14, 15, 11, 7, 8, 15, 16, 7, 15, 6, 7, 16, 52, 27, 50, 4, 52, 53, 25, 52, 4, 26, 27, 52, 24, 25, 4, 30, 32, 33, 5, 24, 4, 31, 32, 30, 25, 26, 52, 22, 6, 19, 23, 24, 5, 22, 5, 6, 19, 6, 16, 23, 5, 22, 17, 18, 16, 22, 20, 21, 19, 16, 18, 19, 20, 22], "vertices": [1, 89, 552.27, 131.91, 1, 1, 89, 566.1, 118.73, 1, 1, 89, 566.54, 91.9, 1, 1, 89, 553.57, 61.25, 1, 1, 89, 502.95, 21.74, 1, 1, 89, 555.74, 17.81, 1, 1, 89, 596.41, 37.28, 1, 1, 89, 640.11, 66.25, 1, 1, 89, 729.28, 84.24, 1, 1, 89, 757.86, 87.23, 1, 1, 89, 778.07, 66.92, 1, 1, 89, 787.1, 49.99, 1, 1, 89, 806.25, -0.18, 1, 1, 89, 804.46, -26.01, 1, 1, 89, 784.3, -49.72, 1, 1, 89, 741.73, -78.54, 1, 1, 89, 732.54, -86.31, 1, 1, 89, 718.34, -121.8, 1, 1, 89, 692.06, -143.86, 1, 1, 89, 658.43, -149.54, 1, 1, 89, 608.03, -154.02, 1, 1, 89, 568.58, -145.41, 1, 1, 89, 560.04, -133.4, 1, 1, 89, 520.89, -122.01, 1, 1, 89, 482.09, -94.26, 1, 1, 89, 469.39, -91.43, 1, 1, 89, 387.74, -99.71, 1, 1, 89, 331.86, -85.34, 1, 1, 89, 283.88, -68.35, 1, 1, 89, 245.63, -59.95, 1, 1, 89, 255.55, -92.61, 1, 1, 89, 253.46, -95.68, 1, 1, 89, 250.37, -92.47, 1, 1, 89, 245.51, -61.88, 1, 1, 89, 204.79, -61.84, 1, 1, 89, 200.17, -59.06, 1, 1, 89, 162.54, -65.8, 1, 1, 89, 136.73, -63.1, 1, 1, 89, 90.41, -35.81, 1, 1, 89, 79.32, -30.62, 1, 1, 89, 22.76, -27.42, 1, 1, 89, -1.56, -15.89, 1, 1, 89, -13.79, -3.57, 1, 1, 89, -13.02, 7.07, 1, 1, 89, 33.2, 21.22, 1, 1, 89, 59.12, 23.96, 1, 1, 89, 91.73, 14.51, 1, 1, 89, 121.23, 26.09, 1, 1, 89, 214.48, 57.74, 1, 1, 89, 246.88, 63.53, 1, 1, 89, 325.06, 68.88, 1, 1, 89, 377.96, 68.54, 1, 1, 89, 383.36, 64.99, 1, 1, 89, 425.22, 78.76, 1, 1, 89, 425.24, 85.3, 1, 1, 89, 454.4, 106.42, 1, 1, 89, 510.93, 132.47, 1, 1, 89, 538.71, 135.31, 1], "hull": 58, "edges": [0, 114, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114], "width": 841, "height": 295}}, "texiao3": {"texiao": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-135.97, -98.64, -121.47, 1216.28, 631.48, 1207.98, 616.99, -106.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 920, "height": 527}}, "zuodatui2": {"zuodatui2": {"type": "mesh", "uvs": [0.84998, 0.01083, 0.97032, 0.07223, 1, 0.17318, 1, 0.20962, 0.96469, 0.37559, 0.81547, 0.66821, 0.6619, 0.96937, 0.54675, 0.99727, 0.41763, 0.98815, 0.26248, 0.95755, 0.11893, 0.88856, 0.04503, 0.81665, 0, 0.74782, 0, 0.69433, 0.05005, 0.60591, 0.1743, 0.44239, 0.32832, 0.23967, 0.57509, 0.02663, 0.72101, 0.00105], "triangles": [0, 1, 2, 0, 3, 18, 2, 3, 0, 18, 4, 17, 3, 4, 18, 4, 16, 17, 5, 16, 4, 15, 16, 5, 11, 12, 13, 14, 11, 13, 9, 10, 14, 15, 9, 14, 11, 14, 10, 15, 8, 9, 15, 5, 8, 5, 7, 8, 6, 7, 5], "vertices": [1, 20, -2.06, -8.27, 1, 1, 20, -5.84, 18.1, 1, 1, 20, 6.9, 34.08, 1, 1, 20, 12.71, 38.11, 1, 1, 20, 43.16, 50.71, 1, 1, 20, 106.65, 58.77, 1, 1, 20, 171.99, 67.06, 1, 1, 20, 189.42, 51.41, 1, 1, 20, 202.53, 29.39, 1, 1, 20, 215.16, 0.76, 1, 1, 20, 220.35, -30.22, 1, 1, 20, 217.22, -50.2, 1, 1, 20, 211.32, -65.13, 1, 1, 20, 202.8, -71.04, 1, 1, 20, 183.05, -72.67, 1, 1, 20, 142.96, -70.53, 1, 1, 20, 93.27, -67.87, 1, 1, 20, 31.47, -51.26, 1, 1, 20, 10.93, -30.34, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 8, 10, 10, 12, 28, 30, 30, 32], "width": 198, "height": 194}}, "beijing": {"beijing": {"type": "mesh", "uvs": [0.91552, 0.00029, 0.9077, 0.02838, 0.91994, 0.04373, 0.93624, 0.04477, 0.92702, 0.02891, 0.95291, 0.03961, 0.97253, 0.03249, 0.97913, 0.02598, 0.99976, 0.06309, 0.99545, 0.07081, 0.97577, 0.11261, 0.99945, 0.12212, 0.99942, 0.15353, 1, 0.15779, 0.99292, 0.15596, 0.9904, 0.14292, 0.96904, 0.14731, 0.9623, 0.17065, 0.96251, 0.18852, 0.94533, 0.20615, 0.96045, 0.21901, 0.97638, 0.2057, 0.99194, 0.17272, 0.99932, 0.16699, 1, 0.21102, 0.98308, 0.24465, 0.95833, 0.26564, 0.94137, 0.26312, 0.9417, 0.27482, 0.92399, 0.28837, 0.91142, 0.2866, 0.88817, 0.27719, 0.92129, 0.3262, 0.92596, 0.31265, 0.92906, 0.30368, 0.9374, 0.29203, 0.95018, 0.29589, 0.95356, 0.28243, 0.95519, 0.27595, 0.96448, 0.27385, 0.97468, 0.28063, 0.98852, 0.26652, 0.98736, 0.27205, 0.99344, 0.28293, 0.99997, 0.29462, 1, 0.35879, 0.98945, 0.3731, 0.95978, 0.37214, 0.96031, 0.3702, 0.94336, 0.34909, 0.9596, 0.33323, 0.94123, 0.34751, 0.93354, 0.34132, 0.93299, 0.36144, 0.97025, 0.39305, 0.9801, 0.39968, 1, 0.4194, 1, 0.7097, 1, 1, 0.5, 1, 0, 1, 0, 0.53282, 0.0036, 0.52152, 0, 0.46751, 0, 0.45272, 0.00437, 0.43651, 0.00376, 0.42066, 0.02256, 0.41226, 0.00066, 0.40933, 0.00045, 0.35289, 0.00816, 0.3437, 0.01233, 0.35003, 0.04976, 0.33153, 0.05223, 0.3166, 0.06041, 0.32252, 0.06155, 0.29466, 0.06524, 0.30194, 0.08518, 0.30531, 0.08804, 0.25763, 0.09146, 0.24311, 0.09718, 0.24221, 0.108, 0.24139, 0.11471, 0.23915, 0.10778, 0.26219, 0.11622, 0.27932, 0.13583, 0.26103, 0.15766, 0.26675, 0.18232, 0.28361, 0.19032, 0.30202, 0.20968, 0.29212, 0.21908, 0.29244, 0.22424, 0.32092, 0.26565, 0.33446, 0.28254, 0.31611, 0.29384, 0.32451, 0.29725, 0.3476, 0.31803, 0.33864, 0.33157, 0.34747, 0.36076, 0.33445, 0.37048, 0.32836, 0.37183, 0.32872, 0.37814, 0.35592, 0.37665, 0.36431, 0.36131, 0.36986, 0.36727, 0.3885, 0.35943, 0.39781, 0.33902, 0.40254, 0.32683, 0.39188, 0.31759, 0.40502, 0.31074, 0.40965, 0.30344, 0.39209, 0.29074, 0.40169, 0.29866, 0.41431, 0.29134, 0.43911, 0.31522, 0.43522, 0.3176, 0.43114, 0.32546, 0.4421, 0.32774, 0.45091, 0.32622, 0.45379, 0.31844, 0.47708, 0.32161, 0.48475, 0.32509, 0.52483, 0.38579, 0.52182, 0.42112, 0.51081, 0.47152, 0.50396, 0.52707, 0.5022, 0.57399, 0.50765, 0.63248, 0.52307, 0.66876, 0.52247, 0.70411, 0.52095, 0.68895, 0.48264, 0.68868, 0.47039, 0.69581, 0.46055, 0.71473, 0.44999, 0.71641, 0.45322, 0.74, 0.4847, 0.76684, 0.4707, 0.77971, 0.44214, 0.80274, 0.39526, 0.79758, 0.37154, 0.78304, 0.35224, 0.78352, 0.3324, 0.75881, 0.31385, 0.75789, 0.30382, 0.73802, 0.30778, 0.73643, 0.29318, 0.67894, 0.25223, 0.65113, 0.218, 0.63253, 0.21748, 0.62621, 0.24455, 0.62284, 0.24163, 0.61116, 0.24009, 0.6009, 0.25601, 0.60535, 0.31436, 0.59756, 0.30266, 0.59251, 0.27177, 0.57968, 0.27541, 0.58333, 0.30684, 0.57659, 0.30254, 0.57064, 0.28652, 0.57001, 0.28407, 0.56552, 0.24867, 0.57225, 0.25272, 0.58687, 0.23505, 0.60947, 0.22789, 0.58959, 0.21433, 0.57736, 0.22928, 0.55879, 0.22925, 0.54663, 0.25197, 0.54244, 0.25322, 0.54018, 0.2429, 0.52379, 0.26246, 0.51937, 0.26059, 0.52798, 0.22804, 0.51286, 0.23567, 0.51334, 0.22636, 0.52556, 0.21272, 0.55156, 0.21364, 0.56753, 0.20944, 0.58028, 0.19318, 0.55009, 0.16554, 0.51191, 0.17963, 0.51303, 0.17079, 0.4736, 0.19303, 0.46193, 0.21352, 0.4549, 0.2164, 0.46372, 0.18077, 0.43344, 0.19172, 0.44339, 0.18049, 0.44913, 0.16665, 0.48149, 0.1591, 0.48446, 0.16002, 0.49545, 0.1658, 0.50092, 0.15185, 0.52403, 0.1586, 0.5558, 0.14597, 0.5633, 0.14913, 0.56437, 0.15077, 0.59959, 0.17833, 0.62667, 0.16757, 0.66511, 0.18663, 0.66918, 0.1721, 0.67747, 0.16674, 0.68458, 0.14445, 0.67755, 0.11516, 0.65856, 0.09154, 0.65762, 0.09299, 0.65056, 0.06692, 0.64019, 0.07201, 0.63872, 0.08837, 0.65062, 0.11426, 0.64145, 0.11065, 0.62196, 0.09739, 0.62002, 0.07058, 0.63759, 0.05807, 0.63914, 0.04122, 0.60568, 0.0669, 0.61134, 0.09209, 0.60681, 0.09316, 0.5985, 0.07664, 0.59634, 0.07258, 0.59649, 0.04629, 0.60014, 0.04295, 0.60587, 0.04575, 0.63688, 0.01811, 0.65582, 0.0316, 0.66469, 0.00077, 0.67038, 0.00283, 0.66665, 0.00704, 0.66905, 0.02218, 0.68182, 0.03896, 0.68184, 0.05874, 0.67366, 0.07946, 0.68458, 0.07946, 0.68011, 0.08595, 0.70589, 0.11026, 0.73121, 0.09979, 0.74003, 0.07672, 0.72848, 0.06348, 0.71767, 0.07386, 0.71291, 0.0722, 0.71902, 0.05944, 0.72668, 0.05236, 0.7386, 0.05246, 0.72718, 0.04743, 0.74838, 0.03284, 0.75317, 0.0536, 0.75328, 0.03282, 0.76771, 0.03769, 0.77401, 0.0542, 0.74764, 0.11018, 0.71595, 0.13131, 0.72144, 0.15274, 0.71084, 0.18243, 0.78156, 0.17006, 0.80588, 0.14077, 0.77082, 0.12023, 0.76794, 0.09568, 0.77421, 0.06246, 0.78475, 0.05748, 0.7882, 0.05933, 0.78207, 0.06982, 0.78924, 0.096, 0.79412, 0.0974, 0.81322, 0.11216, 0.84585, 0.11507, 0.89015, 0.0994, 0.89483, 0.10174, 0.9058, 0.10372, 0.92002, 0.07107, 0.91324, 0.05429, 0.89808, 0.03472, 0.89612, 0.0234, 0.90979, 0.00386, 0.908, 0.00061, 0.76063, 0.09239, 0.71468, 0.13066, 0.74431, 0.18068, 0.71296, 0.18372, 0.78167, 0.11283, 0.79155, 0.12805, 0.81517, 0.13631, 0.80143, 0.19286, 0.73315, 0.22417, 0.73486, 0.1859, 0.76879, 0.17894, 0.75419, 0.2059, 0.30855, 0.70705, 0.72409, 0.64834, 0.50491, 0.60291, 0.13585, 0.59126], "triangles": [39, 37, 38, 37, 39, 40, 44, 40, 43, 40, 42, 43, 42, 40, 41, 40, 36, 37, 51, 52, 50, 52, 33, 50, 36, 33, 34, 50, 33, 36, 36, 34, 35, 50, 36, 40, 47, 48, 46, 46, 48, 45, 49, 50, 48, 48, 50, 45, 19, 31, 282, 282, 255, 281, 286, 285, 282, 285, 254, 282, 28, 29, 27, 27, 29, 30, 265, 19, 282, 30, 31, 27, 27, 31, 19, 267, 19, 265, 267, 265, 266, 265, 282, 281, 25, 26, 20, 26, 27, 20, 27, 19, 20, 267, 268, 19, 20, 21, 25, 19, 17, 18, 19, 268, 17, 282, 254, 255, 254, 285, 277, 17, 268, 16, 16, 268, 10, 10, 268, 269, 5, 269, 3, 256, 280, 255, 255, 280, 281, 280, 264, 281, 281, 264, 265, 256, 279, 280, 264, 280, 279, 264, 279, 263, 263, 279, 262, 256, 257, 279, 279, 257, 262, 9, 269, 5, 9, 5, 6, 257, 261, 262, 261, 257, 258, 269, 270, 3, 3, 270, 2, 9, 6, 8, 261, 259, 260, 261, 258, 259, 6, 7, 8, 270, 271, 2, 2, 271, 1, 3, 4, 5, 271, 272, 1, 1, 273, 0, 1, 272, 273, 273, 274, 0, 155, 152, 154, 157, 158, 156, 154, 152, 153, 158, 159, 156, 159, 160, 156, 160, 162, 156, 160, 161, 162, 156, 162, 155, 155, 162, 152, 162, 163, 152, 152, 163, 151, 149, 150, 148, 148, 150, 164, 163, 164, 151, 150, 151, 164, 164, 165, 148, 147, 148, 200, 184, 185, 186, 184, 186, 183, 191, 183, 186, 191, 186, 190, 187, 188, 186, 188, 189, 186, 186, 189, 190, 183, 191, 192, 181, 194, 180, 181, 182, 194, 182, 183, 192, 192, 193, 182, 182, 193, 194, 180, 194, 195, 171, 172, 170, 173, 170, 172, 169, 170, 168, 168, 170, 167, 170, 173, 167, 174, 175, 173, 167, 178, 166, 166, 178, 165, 173, 177, 167, 167, 177, 178, 175, 176, 173, 173, 176, 177, 165, 198, 148, 198, 199, 148, 148, 199, 200, 178, 179, 165, 165, 179, 198, 180, 197, 179, 179, 197, 198, 197, 195, 196, 197, 180, 195, 238, 243, 237, 239, 241, 238, 239, 240, 241, 241, 242, 238, 238, 242, 243, 237, 243, 246, 243, 245, 246, 243, 244, 245, 246, 249, 237, 246, 248, 249, 246, 247, 248, 250, 236, 237, 250, 237, 249, 251, 252, 276, 276, 252, 203, 203, 235, 276, 250, 251, 236, 251, 276, 236, 276, 235, 236, 212, 209, 211, 210, 211, 209, 209, 212, 213, 209, 213, 208, 213, 214, 208, 208, 214, 207, 214, 215, 207, 218, 219, 217, 216, 217, 219, 219, 220, 216, 220, 221, 216, 216, 221, 223, 223, 221, 222, 215, 216, 223, 230, 225, 229, 207, 215, 225, 215, 223, 224, 215, 224, 225, 229, 225, 228, 228, 225, 226, 228, 226, 227, 202, 253, 200, 146, 253, 278, 202, 200, 201, 284, 278, 277, 278, 253, 277, 202, 203, 253, 203, 252, 253, 277, 253, 254, 203, 204, 235, 204, 205, 234, 204, 234, 235, 234, 205, 232, 207, 205, 206, 205, 207, 232, 234, 232, 233, 232, 207, 231, 231, 207, 225, 225, 230, 231, 31, 143, 282, 143, 141, 142, 144, 145, 143, 282, 143, 286, 286, 143, 283, 146, 283, 145, 143, 145, 283, 147, 200, 146, 146, 200, 253, 146, 278, 283, 283, 284, 286, 283, 278, 284, 284, 277, 286, 286, 277, 285, 269, 9, 10, 21, 22, 24, 14, 12, 13, 12, 15, 11, 50, 44, 45, 44, 50, 40, 32, 33, 52, 54, 57, 138, 54, 138, 53, 57, 54, 56, 54, 55, 56, 138, 139, 53, 140, 141, 139, 139, 32, 53, 139, 31, 32, 139, 141, 31, 53, 32, 52, 14, 15, 12, 15, 10, 11, 22, 23, 24, 25, 21, 24, 16, 10, 15, 31, 141, 143, 59, 288, 58, 288, 289, 127, 127, 128, 288, 128, 129, 288, 58, 288, 57, 136, 57, 288, 136, 288, 135, 57, 136, 137, 57, 137, 138, 60, 287, 59, 60, 290, 287, 287, 290, 121, 59, 287, 289, 122, 289, 287, 288, 59, 289, 289, 126, 127, 289, 125, 126, 122, 287, 121, 289, 122, 123, 111, 113, 91, 111, 91, 92, 67, 91, 113, 60, 61, 290, 123, 124, 289, 120, 121, 119, 119, 121, 113, 290, 113, 121, 288, 129, 135, 289, 124, 125, 91, 67, 88, 91, 89, 90, 91, 88, 89, 61, 62, 290, 290, 62, 67, 67, 63, 65, 290, 67, 113, 77, 88, 67, 67, 72, 74, 67, 74, 77, 88, 84, 87, 88, 77, 84, 67, 62, 63, 129, 130, 135, 134, 130, 132, 130, 134, 135, 130, 131, 132, 119, 113, 118, 118, 114, 116, 114, 118, 113, 84, 86, 87, 84, 85, 86, 63, 64, 65, 132, 133, 134, 118, 116, 117, 114, 115, 116, 113, 111, 112, 65, 66, 67, 68, 71, 67, 67, 71, 72, 109, 110, 108, 74, 76, 77, 68, 69, 71, 108, 110, 107, 105, 106, 103, 111, 92, 95, 111, 95, 110, 94, 92, 93, 92, 94, 95, 104, 105, 103, 107, 110, 97, 97, 110, 95, 103, 106, 107, 107, 97, 103, 97, 95, 96, 97, 98, 103, 102, 103, 101, 100, 98, 99, 98, 101, 103, 100, 101, 98, 69, 70, 71, 72, 73, 74, 76, 74, 75, 84, 78, 83, 84, 77, 78, 83, 78, 80, 78, 79, 80, 83, 81, 82, 83, 80, 81], "vertices": [2, 124, 220.08, -447.14, 1e-05, 125, 407.29, 30.96, 0.99999, 2, 124, 192.26, -411.02, 1e-05, 125, 364.25, 26.58, 0.99999, 2, 124, 162.61, -411.67, 1e-05, 125, 349.83, 1.12, 0.99999, 2, 124, 147.07, -430.82, 1e-05, 125, 357.38, -22.22, 0.99999, 2, 124, 174.71, -434.38, 1e-05, 125, 374.17, -0.7, 0.99999, 2, 124, 138.87, -456.27, 1e-05, 125, 373.69, -42.69, 0.99999, 2, 124, 130.5, -487.22, 1e-05, 125, 394.35, -66.23, 0.99999, 3, 123, 592.99, -431.69, 0.568, 124, 126.04, -490.8, 0, 125, 394.98, -71.91, 0.432, 3, 123, 530.41, -427.19, 0.488, 124, 63.71, -483.71, 0, 125, 357.86, -120.94, 0.512, 3, 123, 522.85, -412.96, 0.624, 124, 56.74, -469.18, 0, 125, 342.68, -119.12, 0.376, 1, 123, 483.89, -347.89, 1, 1, 123, 452.32, -369.52, 1, 1, 123, 413.7, -343.46, 1, 1, 123, 407.98, -340.65, 1, 1, 123, 416.18, -333.35, 1, 1, 123, 434.32, -341.01, 1, 5, 123, 451.2, -317.11, 0.392, 124, -10.86, -370.43, 0, 125, 229.28, -123.92, 0.60789, 128, -232.85, 61.95, 0.00011, 130, -191.28, 72.88, 0, 5, 123, 427.16, -288, 0.44, 124, -33.67, -340.35, 0, 125, 193.61, -127.25, 0.5592, 128, -197.36, 49.08, 0.0008, 130, -159.64, 52.27, 0, 5, 123, 404.13, -272.22, 0.52, 124, -56.02, -323.62, 0, 125, 168.91, -137.3, 0.47853, 128, -169.51, 47.17, 0.00147, 130, -132.95, 44.08, 0, 5, 123, 397.04, -236.74, 0.336, 124, -61.63, -287.88, 0, 125, 137.36, -123.06, 0.65825, 128, -145.38, 20.21, 0.00575, 130, -115.59, 12.34, 0, 4, 123, 367.9, -244.07, 0.432, 125, 127.45, -151.23, 0.5606, 128, -124, 41.32, 0.0074, 130, -89.97, 28.04, 0, 4, 123, 371.25, -275.37, 0.464, 125, 153.72, -166.19, 0.53051, 128, -142.78, 66.59, 0.00549, 130, -102.51, 56.91, 0, 1, 123, 396.38, -318.24, 1, 1, 123, 397.24, -332.19, 1, 1, 123, 342.5, -296.55, 1, 4, 123, 317.08, -250.54, 0.4, 125, 105.22, -196.79, 0.59139, 128, -83.5, 72.7, 0.00861, 130, -43.4, 49.38, 0, 4, 123, 311.93, -202.5, 0.00099, 125, 64.9, -173.83, 0.97746, 128, -54.68, 33.92, 0.02154, 130, -24.14, 5.07, 1e-05, 4, 123, 329, -183.42, 0.00885, 125, 59.15, -148.96, 0.96111, 128, -59.7, 8.81, 0.03004, 130, -34.75, -18.24, 1e-05, 4, 123, 313.86, -173.46, 0.01716, 125, 43.23, -155.8, 0.94539, 128, -41.6, 7.92, 0.03744, 130, -17.32, -23.22, 1e-05, 4, 123, 311.47, -139.2, 0.03558, 125, 15.16, -138.38, 0.91632, 128, -22.14, -20.37, 0.04809, 130, -4.81, -55.21, 1e-05, 4, 123, 324.13, -125.17, 0.0581, 125, 10.99, -119.99, 0.88464, 128, -25.92, -38.9, 0.05725, 130, -12.7, -72.38, 1e-05, 4, 123, 355.4, -105.71, 0.912, 125, 12.57, -83.2, 0.07651, 128, -42.98, -71.53, 0.01149, 130, -36.74, -100.29, 0, 1, 123, 266.88, -103.07, 1, 4, 123, 286.54, -130.46, 0.0221, 125, -5.06, -153.98, 0.00457, 128, 3.78, -15.25, 0.97325, 130, 21.59, -56.11, 8e-05, 4, 123, 292.69, -142.98, 0.00113, 125, 8.03, -155.99, 0.00027, 127, 101.72, 5.51, 0.52038, 128, -7.87, -7.59, 0.47822, 2, 127, 99.95, -15.1, 0.91757, 130, 2.31, -27.72, 0.08243, 4, 123, 278.09, -173.21, 0, 125, 23.83, -185.15, 0, 127, 79.6, -19.75, 0.38181, 130, 16.9, -12.78, 0.61819, 1, 130, -1.94, -5.06, 1, 1, 129, 100.13, -0.23, 1, 4, 123, 293.69, -209.81, 0, 125, 60.82, -193, 0, 131, 71.12, 36.29, 0.13473, 129, 94.18, -15.8, 0.86527, 4, 123, 274.83, -221.23, 0, 125, 59.62, -215.02, 0, 131, 61, 16.7, 0.57548, 129, 75.76, -27.92, 0.42452, 2, 131, 75.41, -14.97, 0.94079, 129, 72.83, -62.59, 0.05921, 3, 126, -97.52, -17.59, 0, 131, 68.32, -10.89, 0.96369, 129, 68.64, -55.57, 0.03631, 2, 126, -81.15, -8.68, 1e-05, 131, 49.99, -14.24, 0.99999, 1, 123, 239.7, -227.25, 1, 1, 123, 160.76, -174.13, 1, 3, 126, 39.71, 33.67, 0.9387, 131, -78.06, -16.7, 0.02683, 129, -61.85, 11.01, 0.03448, 3, 126, 73.14, 8.31, 0.99882, 127, -46.75, 54.58, 0.00108, 128, 130.62, 65.01, 0.0001, 3, 126, 70.79, 6.5, 0.99908, 131, -99.09, 18.81, 0.0004, 129, -62.81, 52.28, 0.00052, 7, 123, 186.13, -109.4, 0, 125, -75.44, -224.82, 0, 126, 60.72, -46.22, 0.71351, 127, 7.95, 66.25, 0.25444, 128, 100.98, 17.58, 0.03203, 131, -73.05, 65.74, 1e-05, 129, -17.12, 80.46, 1e-05, 4, 126, 9.71, -52.09, 0.17287, 127, 34.72, 22.44, 0.64523, 128, 50.87, 28.79, 0.1819, 131, -22.76, 55.38, 0, 4, 123, 234.06, -109.89, 0.01867, 125, -49.32, -185.59, 0.00016, 128, 59.43, -6.33, 0.98117, 130, 77.82, -60.08, 0, 1, 123, 238, -105.81, 1, 1, 123, 213.72, -88.45, 1, 1, 123, 143.58, -108.67, 1, 1, 123, 127.15, -115.45, 1, 1, 123, 86.2, -123.91, 1, 3, 122, 1467.32, -252.9, 0.99354, 123, -270.87, 116.6, 0.00646, 128, 609.43, 54.91, 0, 3, 122, 1460.27, -683.36, 0.99335, 123, -627.93, 357.11, 0.00664, 128, 1039.16, 28.99, 0, 3, 122, 709.37, -671.06, 0.99877, 123, -208.38, 979.99, 0.00123, 128, 993.96, -720.64, 0, 2, 122, -41.53, -658.77, 1, 123, 211.17, 1602.87, 0, 2, 122, -30.19, 33.97, 1, 144, 845.74, 483, 0, 2, 122, -24.51, 50.63, 1, 144, 838.02, 467.18, 0, 2, 122, -28.6, 130.81, 1, 144, 832, 387.13, 0, 2, 122, -28.24, 152.74, 1, 144, 828.88, 365.41, 0, 2, 122, -21.29, 176.67, 1, 144, 818.98, 342.55, 0, 2, 122, -21.81, 200.19, 1, 144, 816.54, 319.15, 0, 2, 122, 6.63, 212.17, 1, 144, 786.82, 310.84, 0, 2, 122, -26.19, 217.07, 1, 144, 818.77, 301.86, 0, 2, 122, -25.14, 300.75, 1, 144, 807.2, 218.97, 0, 2, 122, -13.34, 314.2, 1, 144, 793.81, 207.11, 0, 2, 122, -7.23, 304.7, 1, 144, 788.94, 217.3, 0, 2, 122, 49.44, 331.22, 1, 144, 729.39, 198.11, 0, 2, 122, 53.5, 353.29, 1, 144, 722.58, 176.73, 0, 2, 122, 65.63, 344.31, 1, 144, 711.68, 187.16, 0, 2, 122, 68.03, 385.6, 1, 144, 704.11, 146.5, 0, 2, 122, 73.4, 374.72, 1, 144, 700.15, 157.97, 0, 2, 122, 103.25, 369.23, 1, 144, 671.22, 167.17, 0, 2, 122, 108.72, 439.85, 1, 144, 656.93, 97.8, 0, 2, 122, 114.2, 461.3, 1, 144, 648.79, 77.21, 0, 2, 122, 122.81, 462.49, 1, 144, 640.1, 77.1, 0, 2, 122, 139.08, 463.45, 1, 144, 623.84, 78.2, 0, 2, 122, 149.21, 466.61, 1, 144, 613.4, 76.34, 0, 2, 122, 138.25, 432.6, 1, 144, 628.54, 108.7, 0, 2, 122, 150.51, 406.99, 1, 144, 619.59, 135.64, 0, 2, 122, 180.41, 433.64, 1, 144, 586.59, 112.96, 0, 2, 122, 213.05, 424.62, 1, 144, 555.34, 126.01, 0, 2, 122, 249.67, 399.01, 1, 144, 522.22, 156.02, 0, 2, 122, 261.23, 371.51, 1, 144, 514.21, 184.76, 0, 2, 122, 290.55, 385.73, 1, 144, 483.34, 174.34, 0, 2, 122, 304.66, 385.01, 1, 144, 469.43, 176.83, 0, 2, 122, 311.72, 342.66, 1, 144, 467.75, 219.73, 0, 2, 122, 373.58, 321.56, 1, 144, 409.03, 248.43, 0, 2, 122, 399.39, 348.36, 1, 144, 380.06, 225.09, 0, 2, 122, 416.15, 335.63, 1, 144, 365.03, 239.83, 0, 2, 122, 420.72, 301.3, 1, 144, 364.81, 274.46, 0, 2, 122, 452.15, 314.07, 1, 144, 332.03, 265.73, 0, 2, 122, 472.26, 300.65, 1, 144, 313.76, 281.57, 0, 2, 122, 516.42, 319.23, 1, 144, 267.61, 268.69, 0, 2, 122, 531.17, 328.04, 1, 144, 251.88, 261.81, 0, 2, 122, 533.18, 327.46, 1, 144, 249.95, 262.63, 0, 2, 122, 542, 286.97, 1, 144, 246.29, 303.92, 0, 2, 122, 539.56, 274.57, 1, 144, 250.28, 315.91, 0, 2, 122, 516.39, 266.72, 1, 144, 274.25, 320.79, 0, 2, 122, 524.89, 238.93, 1, 144, 269.31, 349.42, 0, 2, 122, 512.89, 225.32, 1, 144, 282.92, 361.41, 0, 2, 122, 482.11, 218.81, 1, 144, 314.27, 364.01, 0, 2, 122, 464.07, 234.91, 1, 144, 330.15, 345.77, 0, 2, 122, 449.88, 215.66, 1, 144, 346.65, 363.08, 0, 2, 122, 439.48, 208.96, 1, 144, 357.81, 368.42, 0, 2, 122, 428.94, 235.18, 1, 144, 364.96, 341.08, 0, 2, 122, 409.62, 221.26, 1, 144, 385.88, 352.47, 0, 2, 122, 421.21, 202.35, 1, 144, 376.76, 372.69, 0, 2, 122, 409.63, 165.76, 1, 144, 392.85, 407.53, 0, 2, 122, 445.58, 170.94, 1, 144, 356.53, 406.9, 0, 2, 122, 449.25, 176.93, 1, 144, 352.14, 401.43, 0, 2, 122, 460.79, 160.48, 1, 144, 342.76, 419.2, 0, 2, 122, 464, 147.36, 1, 144, 341.23, 432.62, 0, 2, 122, 461.64, 143.13, 1, 144, 344.09, 436.52, 0, 1, 122, 449.39, 108.78, 1, 1, 122, 453.97, 97.34, 1, 1, 122, 458.23, 37.83, 1, 1, 122, 549.45, 40.79, 1, 2, 122, 602.79, 56.25, 1, 144, 214.98, 540.44, 0, 2, 122, 678.64, 65.17, 1, 144, 138.61, 541.13, 0, 2, 122, 762.12, 66.4, 1, 144, 55.64, 550.39, 0, 1, 122, 832.44, 57.17, 1, 1, 122, 919.91, 32.87, 1, 1, 122, 974.4, 32.87, 1, 3, 122, 1027.54, 34.25, 0.99911, 123, 209.58, 328.82, 0.00089, 144, -203.64, 615.65, 0, 3, 122, 1005.69, 91.43, 0.99985, 123, 269.42, 315.98, 0.00015, 144, -189.15, 556.18, 0, 2, 122, 1005.59, 109.6, 1, 144, -191.33, 538.14, 0, 2, 122, 1016.54, 124.02, 1, 144, -204, 525.21, 0, 3, 122, 1045.2, 139.21, 0.99896, 123, 287.95, 256.81, 0.00104, 144, -234.35, 513.74, 0, 3, 122, 1047.66, 134.38, 0.99879, 123, 282.56, 257.39, 0.00121, 144, -236.18, 518.85, 0, 3, 122, 1082.31, 87.12, 0.95687, 123, 224.05, 254.09, 0.04313, 144, -264.62, 570.08, 0, 3, 122, 1122.96, 107.22, 0.78633, 123, 218.75, 209.06, 0.21367, 144, -307.47, 555.25, 0, 3, 122, 1142.98, 149.25, 0.53117, 123, 243.08, 169.36, 0.46883, 144, -332.61, 516.07, 0, 4, 122, 1178.37, 218.16, 0.14926, 123, 281.57, 102.13, 0.83997, 124, -162.92, 55.5, 0.01077, 144, -376.38, 452.15, 0, 4, 122, 1170.21, 253.32, 0.04803, 123, 315.49, 89.81, 0.89832, 124, -129.54, 41.78, 0.05365, 144, -372.71, 416.25, 0, 3, 122, 1147.49, 282.38, 0.01492, 123, 352.23, 93.03, 0.84299, 124, -92.69, 43.46, 0.14209, 3, 122, 1147.36, 311.28, 0.00417, 123, 376.54, 77.38, 0.69986, 124, -69.06, 26.82, 0.29597, 3, 122, 1108.94, 342.68, 7e-05, 123, 423.8, 92.48, 0.23554, 124, -21.2, 39.94, 0.7644, 2, 123, 436.35, 83.91, 0.11586, 124, -9.02, 30.86, 0.88414, 2, 123, 453.6, 109.63, 0.00646, 124, 9.28, 55.84, 0.99354, 2, 124, 25.91, 41.68, 0.99999, 146, -14.93, 192.39, 1e-05, 3, 124, 130.52, 56.74, 0.9, 142, -67.43, 38.92, 0.07168, 146, 1.83, 88.04, 0.02832, 3, 124, 196.1, 48.32, 0.08895, 142, -5.56, 15.61, 0.63956, 146, -5.51, 22.34, 0.27149, 1, 146, 13.05, 2.2, 1, 1, 146, 48.49, 23.32, 1, 1, 146, 48.91, 16.67, 1, 1, 146, 59.48, 2.48, 1, 2, 142, 31.31, 101.85, 1e-05, 146, 87.15, 7.85, 0.99999, 2, 142, -17.55, 173.58, 2e-05, 146, 144.63, 72.87, 0.99998, 2, 142, 1.24, 164.35, 2e-05, 146, 140.32, 52.39, 0.99998, 2, 142, 30.62, 128.39, 1e-05, 146, 112.7, 15.06, 0.99999, 2, 142, 44.65, 142.67, 0, 146, 130, 4.98, 1, 2, 142, 16.69, 180.37, 0, 146, 159.65, 41.36, 1, 2, 142, 28.64, 179.88, 0, 146, 162.12, 29.66, 1, 1, 146, 151.27, 6.7, 1, 1, 146, 149.31, 3.49, 1, 2, 142, 82.82, 118.85, 0, 146, 116.31, -37.88, 1, 1, 146, 113.59, -26.45, 1, 1, 146, 79.5, -28.92, 1, 2, 142, 41.23, 59.03, 0.1097, 146, 48.09, -12.31, 0.8903, 3, 124, 269.59, 104.75, 0, 142, 78.96, 53.59, 0.7486, 146, 52.11, -50.22, 0.2514, 3, 124, 269.66, 133.08, 0, 142, 85.56, 81.14, 0.88479, 146, 80.44, -49.83, 0.11521, 3, 124, 291.28, 150.89, 0, 142, 110.7, 93.49, 0.94457, 146, 98.61, -71.16, 0.05543, 3, 124, 283.78, 188.23, 0, 142, 112.01, 131.55, 0.98705, 146, 135.82, -63.04, 0.01295, 3, 124, 287.38, 193.7, 0, 142, 116.77, 136.05, 0.9862, 146, 141.35, -66.55, 0.0138, 3, 124, 299.86, 184.2, 0, 142, 126.73, 123.92, 0.98765, 146, 132.05, -79.19, 0.01235, 3, 124, 300.07, 222.19, 0, 142, 135.69, 160.84, 0.99988, 146, 170.04, -78.78, 0.00012, 2, 124, 306.94, 224.34, 0, 142, 142.87, 161.35, 1, 3, 124, 328.09, 179.17, 0, 142, 153.04, 112.52, 0.99034, 146, 127.48, -107.5, 0.00966, 2, 142, 168.24, 132.82, 0.99655, 146, 150.9, -117.23, 0.00345, 2, 142, 173.82, 120.18, 0.99479, 146, 140.02, -125.75, 0.00521, 3, 124, 345.53, 164.16, 0, 142, 166.55, 93.89, 0.98976, 146, 112.75, -125.18, 0.01024, 3, 124, 314.59, 140.21, 0, 142, 130.93, 77.73, 0.95274, 146, 88.31, -94.64, 0.04726, 3, 124, 300.02, 120.26, 0, 142, 112.15, 61.67, 0.89061, 146, 68.13, -80.4, 0.10938, 3, 124, 301.21, 89.33, 0, 142, 106.17, 31.3, 0.92288, 146, 37.21, -82.09, 0.07712, 3, 142, 163.96, 13.33, 0.37395, 143, 1.91, 12.51, 0.62603, 146, 34.03, -142.52, 1e-05, 3, 142, 193.04, 65.75, 2e-05, 143, 61.56, 18.51, 0.99833, 145, -25.9, 27.92, 0.00165, 3, 142, 200.29, 54.71, 1e-05, 143, 56.63, 6.26, 0.99999, 146, 83.09, -167.53, 0, 4, 142, 223.79, 118.29, 0, 143, 122.26, 23.2, 0.00034, 145, 29.76, 3.26, 0.99966, 146, 150.5, -174.65, 0, 2, 144, 37.22, 56.36, 0.00768, 145, 63.24, 13.63, 0.99232, 2, 143, 158.2, 49.62, 0, 145, 73.93, 9.47, 1, 2, 144, 26.59, 11.49, 0.61949, 145, 28.76, -17, 0.38051, 3, 144, 70.79, 31.49, 0.99987, 145, 76.44, -26.01, 0.00013, 146, 200.73, -197.31, 0, 3, 144, 56.54, 14.23, 0.99833, 145, 54.85, -31.95, 0.00167, 146, 180.15, -206.13, 0, 1, 144, 48.76, -6.61, 1, 2, 143, 100.81, -22.83, 0.22243, 144, -0.12, -19.59, 0.77757, 2, 143, 96.35, -20.85, 0.29685, 144, -4.78, -18.15, 0.70315, 1, 143, 80.3, -7.58, 1, 4, 143, 67.15, -25.54, 0.99438, 144, -33.23, -26.24, 0.00446, 145, -41.88, -13.52, 0.00116, 146, 81.81, -201.01, 0, 2, 143, 36.07, -7.06, 1, 144, -66.27, -11.55, 0, 2, 142, 172.28, -14.92, 0.66861, 143, -16.55, -10.43, 0.33139, 3, 142, 158.75, -16.02, 0.96247, 143, -25.16, 0.05, 0.03753, 144, -127.91, -11.68, 0, 2, 142, 156.01, -14.48, 1, 144, -128.58, -8.6, 0, 1, 142, 90.36, -1.87, 1, 2, 124, 272.67, 15.05, 0.01824, 142, 61.29, -34.4, 0.98176, 3, 124, 211.42, -0.41, 0.47061, 132, 1.94, 38.62, 0.14202, 142, -1.87, -35.33, 0.38737, 3, 124, 222.17, -20.27, 0.22179, 132, 22.22, 28.68, 0.69276, 142, 4.01, -57.14, 0.08545, 3, 124, 218.7, -34.52, 0.07779, 132, 27.67, 15.06, 0.90221, 142, -2.66, -70.21, 0.02, 3, 132, 56.15, -4.12, 0.69484, 135, -47.96, 134.12, 0.0001, 137, -43.88, -8.06, 0.30506, 3, 132, 101.12, 0.19, 0.82979, 135, -32.92, 91.51, 0.00048, 137, -26.85, 33.79, 0.16973, 3, 132, 143, 21.74, 0.96838, 135, -1.88, 56.08, 0.03145, 137, -26.55, 80.89, 0.00017, 3, 132, 141.01, 23.17, 0.98468, 135, -0.98, 58.35, 0.01514, 137, -28.74, 79.79, 0.00018, 3, 132, 188.03, 30.45, 0.18705, 135, 17.44, 14.49, 0.81295, 137, -13.4, 124.83, 1e-05, 2, 135, 32.66, 6.3, 0.16987, 136, 8.94, 9.58, 0.83013, 3, 132, 176.2, 42.69, 0.00019, 136, 31.8, 4.37, 0.99981, 137, -29.73, 120.01, 0, 5, 132, 138.8, 22.8, 0, 133, -57.75, 35.08, 0, 134, 55.02, 93.78, 0, 136, 72.93, 14.49, 1, 137, -29.44, 77.66, 0, 4, 132, 143.42, 36.85, 0, 134, 64.16, 82.16, 0, 136, 65.02, 2.01, 1, 137, -39.74, 88.26, 0, 3, 133, -22.55, 70.31, 0.00011, 134, 79.37, 50.34, 0, 136, 40.06, -22.91, 0.99989, 4, 133, 17.01, 62.62, 0.00142, 134, 61.23, 14.35, 5e-05, 135, 60.94, 11.39, 0.03676, 136, 0.08, -17.76, 0.96177, 2, 133, 16.84, 28.03, 0.02917, 135, 31.45, -6.69, 0.97083, 3, 133, 32.68, 8.45, 0.55672, 134, 4.83, 13.98, 0.33191, 135, 23.15, -30.47, 0.11137, 3, 133, 16.67, 65.52, 0.00029, 134, 64.11, 13.89, 0.99969, 135, 63.22, 13.21, 1e-05, 3, 133, -20.84, 73.25, 0.00047, 134, 81.73, 47.89, 0.99951, 135, 50.02, 49.16, 2e-05, 3, 133, -19.46, 80.1, 0.00047, 134, 87.95, 44.71, 0.99951, 135, 56.57, 51.6, 2e-05, 3, 133, 8, 81.31, 0.00038, 134, 81.66, 17.95, 0.9996, 135, 72.07, 28.9, 2e-05, 3, 133, 14.83, 81.77, 0.00035, 134, 80.25, 11.25, 0.99963, 135, 76.06, 23.34, 2e-05, 3, 133, 50.2, 65.42, 4e-05, 134, 54.91, -18.35, 0.99996, 135, 80.8, -15.34, 0, 3, 133, 52.43, 58.37, 3e-05, 134, 47.52, -18.59, 0.99996, 135, 75.99, -20.95, 0, 2, 134, 43.63, -9.86, 1, 135, 66.92, -17.93, 0, 1, 133, 55.47, -12.31, 1, 2, 132, 229.15, -0.13, 0.14537, 133, 21.25, -14.42, 0.85463, 2, 132, 263.48, -30.16, 0.2736, 133, 44.74, -53.52, 0.7264, 2, 132, 257.15, -36.33, 0.27335, 133, 36.82, -57.45, 0.72665, 2, 132, 254.09, -28.77, 0.27288, 133, 36.21, -49.32, 0.72712, 2, 132, 232.91, -22.3, 0.27362, 133, 18.03, -36.68, 0.72638, 2, 132, 205.59, -28.19, 0.48193, 133, -9.77, -33.92, 0.51807, 2, 132, 183.08, -22.29, 0.94734, 133, -29.4, -21.41, 0.05266, 2, 132, 155.75, -4.76, 0.99999, 133, -50.04, 3.64, 1e-05, 3, 132, 152.51, -20.84, 0.9997, 133, -58.05, -10.66, 2e-05, 135, -40.91, 36.57, 0.00028, 2, 132, 144.4, -12.36, 0.99999, 133, -63.18, -0.11, 1e-05, 3, 132, 101.72, -39.87, 0.11855, 135, -71.64, 81.27, 5e-05, 137, 8.92, 15.75, 0.8814, 2, 138, 2.07, 10.46, 0.99994, 141, -16.64, 47.92, 6e-05, 2, 138, 38.52, 6.28, 0.75529, 141, -10.4, 11.77, 0.24471, 3, 132, 171.7, -72.79, 0, 139, -23.03, 28.36, 1e-05, 141, 14.58, 3.85, 0.99999, 5, 132, 156.18, -56.66, 0, 138, 34.15, 39.86, 0.10992, 139, -45.3, 30.63, 2e-05, 140, -11.46, 42.91, 3e-05, 141, 20.59, 25.41, 0.89003, 4, 132, 158.58, -49.5, 0, 138, 34.73, 47.39, 0.11978, 139, -48.1, 37.64, 2e-05, 141, 27.98, 26.98, 0.8802, 4, 132, 177.58, -58.53, 0, 138, 55.36, 43.29, 0.03509, 139, -27.77, 43.04, 1e-05, 141, 29.86, 6.03, 0.9649, 2, 140, 19.14, 26.66, 0.00327, 141, 25.39, -8.9, 0.99673, 2, 140, 17.43, 8.84, 0.7919, 141, 9.96, -17.97, 0.2081, 1, 140, 26.36, 25.28, 1, 1, 140, 45.16, -8.32, 1, 2, 139, 12.26, 9.82, 0.6477, 140, 13.85, -12.82, 0.3523, 2, 139, 35.73, 29.81, 0.98323, 140, 44.55, -15.66, 0.01677, 4, 132, 210.39, -131.41, 0, 138, 105.02, -19.33, 0.0008, 139, 44.38, 8.68, 0.99896, 140, 35.47, -36.62, 0.00023, 1, 139, 32.01, -14.47, 1, 2, 137, 64.42, -12.01, 0.70174, 138, -6.47, -17.09, 0.29826, 3, 132, 70.65, -54.62, 0.07717, 137, 7.59, -18.61, 0.92283, 139, -111.85, -23.13, 0, 3, 132, 37.37, -57.52, 0.58469, 137, -5.26, -49.45, 0.4149, 125, 109.85, 185.65, 0.00042, 5, 124, 166.29, -51.3, 0.19098, 132, -5.27, -29.02, 0.75983, 137, -50.28, -74.02, 0.01987, 144, -315.01, 111.54, 0, 125, 62.15, 195.74, 0.02933, 5, 124, 116.71, -127.6, 0.25971, 132, -1.35, -119.92, 0.13276, 137, 32.09, -112.68, 9e-05, 144, -405.68, 119.2, 0, 125, 98.47, 113.19, 0.60743, 5, 124, 139.01, -180.45, 0.04903, 132, 47.48, -150.02, 0.03718, 137, 81.39, -83.35, 6e-05, 144, -441.73, 74.58, 0, 125, 152.16, 104, 0.91373, 5, 124, 197.6, -155.94, 0.00358, 132, 80.96, -96.06, 0.02068, 137, 49.09, -28.67, 8e-05, 144, -392.45, 34.52, 0, 125, 162, 166.65, 0.97566, 5, 124, 230.49, -175.48, 0.00061, 132, 119.1, -92.88, 0.01885, 137, 63.94, 6.59, 8e-05, 144, -394.14, -3.71, 0, 125, 194.28, 184.13, 0.98046, 4, 132, 170.64, -104.06, 0.01688, 137, 97.74, 47.08, 9e-05, 144, -411.78, -53.42, 0, 125, 243.52, 193.45, 0.98303, 5, 124, 262.86, -232.2, 0, 132, 178.37, -120.28, 0.01677, 137, 115.69, 46.42, 9e-05, 144, -428.84, -59.03, 0, 125, 256.16, 181.41, 0.98313, 5, 124, 257.55, -234.69, 0, 132, 175.5, -125.38, 0.01672, 137, 118.88, 41.5, 9e-05, 144, -433.54, -55.53, 0, 125, 255.49, 175.59, 0.98319, 4, 132, 159.23, -115.56, 0.01707, 137, 102.64, 31.64, 9e-05, 144, -421.73, -40.64, 0, 125, 237.67, 178.47, 0.98284, 5, 124, 211.27, -201.45, 0.0023, 132, 118.51, -125.18, 0.01966, 137, 92.3, -8.9, 8e-05, 144, -426.11, 0.97, 0, 125, 205.46, 154.05, 0.97795, 5, 124, 205.15, -206.18, 0.00384, 132, 116.28, -132.58, 0.02036, 137, 97.82, -14.31, 8e-05, 144, -433.16, 4.13, 0, 125, 206.17, 146.35, 0.97572, 5, 124, 169.61, -215.9, 0.01398, 132, 92.98, -161.13, 0.02269, 137, 112.32, -48.18, 6e-05, 144, -458.52, 30.86, 0, 125, 196.08, 111.08, 0.96327, 5, 124, 138.33, -253.05, 0.00686, 132, 89.07, -209.54, 0.01079, 137, 153.4, -74.08, 3e-05, 144, -506.04, 40.88, 0, 125, 210.15, 64.84, 0.98232, 5, 124, 119.92, -322.41, 0, 132, 114.34, -276.7, 0.00018, 137, 224.63, -82.82, 0, 144, -575.87, 24.35, 0, 125, 256.6, 12.39, 0.99981, 6, 124, 112.94, -325.98, 0, 132, 110.72, -283.66, 0.00011, 137, 229.12, -89.24, 0, 144, -582.31, 28.81, 0, 125, 255.95, 4.58, 0.99989, 130, -292.77, -10.79, 0, 3, 124, 100.91, -337.67, 0, 125, 259.28, -11.82, 1, 130, -285.4, 4.27, 0, 2, 124, 128.78, -386, 1e-05, 125, 312.16, -13.9, 0.99999, 2, 124, 155.43, -393.44, 1e-05, 125, 331.56, 4.72, 0.99999, 2, 124, 192.84, -393.15, 1e-05, 125, 350.18, 36.57, 0.99999, 2, 124, 208.53, -401.41, 1e-05, 125, 364.73, 45.47, 0.99999, 2, 124, 220.69, -436.71, 1e-05, 125, 399.22, 37.01, 0.99999, 2, 124, 226.27, -437.57, 1e-05, 125, 402.72, 41.28, 0.99999, 3, 132, 129.19, -121.4, 1e-05, 138, 23.86, -29.52, 0.96314, 139, -24.01, -36.2, 0.03686, 4, 132, 71.63, -52.74, 0.07309, 135, -91.39, 107.35, 0, 137, 6.38, -16.87, 0.9269, 139, -112.32, -21.07, 1e-05, 5, 124, 134.47, -82.84, 0.44362, 132, -12.87, -73.18, 0.33049, 137, -14.67, -101.22, 1e-05, 144, -357.84, 124.69, 0, 125, 71.46, 152.02, 0.22588, 5, 124, 162.77, -51.7, 0.23293, 132, -7.9, -31.39, 0.71243, 137, -49.39, -77.45, 0.01445, 144, -317.03, 114.45, 0, 125, 60.7, 192.54, 0.04018, 5, 124, 197.11, -176.3, 0.00419, 132, 92.39, -112.92, 0.0208, 137, 69.32, -26.36, 8e-05, 144, -410.62, 25.33, 0, 125, 178.12, 155.42, 0.97493, 5, 124, 169.19, -174.28, 0.0138, 132, 68.48, -127.48, 0.02434, 137, 71.15, -54.3, 7e-05, 144, -422.04, 50.89, 0, 125, 162.42, 132.84, 0.96179, 5, 124, 137.11, -195.72, 0.03393, 132, 54.8, -163.56, 0.02978, 137, 96.78, -83.14, 6e-05, 144, -456.09, 69.04, 0, 125, 163.48, 94.27, 0.93623, 5, 124, 74.13, -127.01, 0.21535, 132, -36.37, -144.17, 0.08309, 137, 37.34, -154.94, 6e-05, 144, -425.27, 157.01, 0, 125, 76.53, 77.43, 0.7015, 4, 124, 99.66, -28.3, 0.82374, 132, -72.88, -48.97, 0.1086, 144, -326.21, 181.14, 0, 125, 10.09, 151.51, 0.06767, 5, 124, 138.03, -68.96, 0.45038, 132, -18.02, -59.8, 0.3945, 137, -28.91, -99.59, 0, 144, -343.92, 128.1, 0, 125, 62.1, 162.41, 0.15512, 5, 124, 115.19, -107.04, 0.36347, 132, -14.53, -104.07, 0.197, 137, 11.93, -117.01, 9e-05, 144, -388.28, 130.25, 0, 125, 81.18, 122.82, 0.43943, 5, 124, 97.65, -66.67, 0.55362, 132, -52.24, -81.37, 0.20191, 137, -25.65, -139.9, 2e-05, 144, -360.98, 164.78, 0, 125, 39.91, 129.42, 0.24444, 3, 122, 428.96, -231.97, 0.99991, 123, 312.6, 975.79, 9e-05, 128, 542.99, -981.54, 0, 3, 122, 1054.45, -155.12, 0.99935, 123, 36.13, 409.48, 0.00065, 128, 493.65, -353.28, 0, 3, 122, 726.38, -82.38, 0.99994, 123, 275.92, 644.89, 6e-05, 128, 406.59, -677.84, 0, 2, 122, 172.41, -56.02, 1, 123, 599.94, 1095, 0], "hull": 275, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 68, 70, 70, 72, 76, 78, 78, 80, 80, 82, 82, 84, 88, 90, 90, 92, 92, 94, 94, 96, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 316, 316, 318, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 376, 378, 378, 380, 380, 382, 382, 384, 384, 386, 386, 388, 388, 390, 390, 392, 392, 394, 394, 396, 396, 398, 398, 400, 400, 402, 402, 404, 404, 406, 406, 408, 408, 410, 410, 412, 412, 414, 414, 416, 416, 418, 418, 420, 420, 422, 422, 424, 424, 426, 426, 428, 428, 430, 430, 432, 432, 434, 436, 438, 438, 440, 440, 442, 442, 444, 444, 446, 446, 448, 448, 450, 450, 452, 452, 454, 454, 456, 456, 458, 458, 460, 460, 462, 462, 464, 464, 466, 466, 468, 468, 470, 470, 472, 472, 474, 474, 476, 476, 478, 478, 480, 480, 482, 518, 520, 520, 522, 522, 524, 524, 526, 526, 528, 528, 530, 530, 532, 532, 534, 534, 536, 536, 538, 538, 540, 540, 542, 542, 544, 544, 546, 546, 548, 548, 0, 434, 436, 486, 488, 488, 490, 490, 492, 492, 494, 494, 496, 482, 484, 484, 486, 496, 498, 498, 500, 516, 518, 500, 502, 502, 504, 504, 506, 512, 514, 514, 516, 510, 512, 506, 508, 508, 510, 84, 86, 86, 88, 72, 74, 74, 76, 64, 66, 66, 68, 100, 102, 102, 104, 96, 98, 98, 100], "width": 767, "height": 757}}, "biyan": {"biyan": {"type": "mesh", "uvs": [0.07053, 0.09752, 0.09979, 0, 0.10806, 0, 0.18728, 0.04379, 0.2428, 0.07448, 0.35849, 0.20648, 0.45114, 0.31219, 0.55083, 0.51994, 0.65466, 0.59306, 0.71056, 0.53626, 0.74957, 0.49661, 0.84948, 0.51838, 0.91113, 0.53182, 0.98423, 0.54774, 1, 0.61419, 1, 0.68458, 0.97162, 0.79066, 0.90718, 0.95047, 0.84928, 1, 0.83641, 1, 0.75448, 0.9623, 0.70005, 0.80828, 0.67933, 0.74965, 0.61454, 0.56633, 0.49451, 0.48105, 0.44444, 0.55843, 0.38307, 0.65329, 0.29573, 0.6352, 0.18706, 0.61269, 0.11504, 0.50839, 0.07521, 0.4507, 0.00913, 0.355, 0.00965, 0.25756, 0.04098, 0.19601], "triangles": [2, 30, 0, 33, 0, 30, 31, 32, 33, 30, 31, 33, 6, 7, 24, 3, 30, 2, 2, 0, 1, 3, 29, 30, 25, 5, 6, 25, 6, 24, 8, 23, 7, 24, 7, 23, 4, 28, 29, 4, 29, 3, 28, 4, 5, 27, 28, 5, 25, 27, 5, 26, 27, 25, 12, 13, 14, 22, 8, 9, 23, 8, 22, 15, 16, 12, 14, 15, 12, 21, 22, 9, 16, 17, 11, 16, 11, 12, 10, 21, 9, 11, 19, 21, 10, 11, 21, 19, 20, 21, 17, 19, 11, 18, 19, 17], "vertices": [1, 160, 35, 104.34, 1, 1, 160, 45.41, 99.74, 1, 1, 160, 45.59, 98.13, 1, 1, 160, 42.88, 82.3, 1, 1, 160, 40.97, 71.2, 1, 1, 160, 30.18, 47.32, 1, 1, 160, 21.53, 28.2, 1, 1, 160, 2.79, 6.59, 1, 1, 160, -2.34, -14.35, 1, 1, 160, 4.55, -24.56, 1, 1, 160, 9.36, -31.68, 1, 1, 160, 9.29, -51.29, 1, 1, 160, 9.25, -63.39, 1, 1, 160, 9.21, -77.73, 1, 1, 160, 2.87, -81.52, 1, 1, 160, -4.19, -82.29, 1, 1, 160, -15.45, -77.96, 1, 1, 160, -32.86, -67.23, 1, 1, 160, -39.06, -56.55, 1, 1, 160, -39.34, -54.05, 1, 1, 160, -37.29, -37.76, 1, 1, 160, -22.99, -25.51, 1, 1, 160, -17.54, -20.85, 1, 1, 160, -0.51, -6.27, 1, 1, 160, 5.5, 17.93, 1, 1, 160, -3.33, 26.78, 1, 1, 160, -14.16, 37.64, 1, 1, 160, -14.2, 54.77, 1, 1, 160, -14.25, 76.08, 1, 1, 160, -5.3, 91.19, 1, 1, 160, -0.36, 99.54, 1, 1, 160, 7.85, 113.4, 1, 1, 160, 17.64, 114.37, 1, 1, 160, 24.48, 108.98, 1], "hull": 34, "edges": [2, 4, 12, 14, 14, 16, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 46, 48, 62, 64, 64, 66, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 8, 10, 10, 12, 4, 6, 6, 8, 48, 50, 50, 52, 2, 0, 0, 66, 40, 42, 42, 44, 44, 46, 24, 26, 20, 22, 22, 24, 16, 18, 18, 20], "width": 195, "height": 101}}, "zuixiaotui1": {"zuixiaotui1": {"type": "mesh", "uvs": [0.27437, 0.01404, 0.37248, 0.01427, 0.54049, 0.16217, 0.64394, 0.25324, 0.90217, 0.48056, 0.98897, 0.66476, 0.98919, 0.88033, 0.84474, 0.99627, 0.6154, 0.86807, 0.52539, 0.8445, 0.30856, 0.84013, 0.00432, 0.66877, 0.04441, 0.26069, 0.18837, 0.07269], "triangles": [13, 1, 2, 1, 13, 0, 9, 10, 2, 2, 12, 13, 10, 12, 2, 11, 12, 10, 3, 9, 2, 8, 9, 3, 4, 8, 3, 5, 8, 4, 7, 8, 5, 6, 7, 5], "vertices": [-4.74, 18.8, 2.9, 23.21, 21.1, 21.97, 32.31, 21.21, 60.28, 19.3, 73.42, 12.22, 80.9, -0.63, 73.66, -14.08, 51.37, -16.78, 43.55, -19.44, 26.52, -28.97, -3.1, -32.48, -14.11, -6.31, -9.41, 11.41], "hull": 14, "edges": [0, 26, 0, 2, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 6, 8, 2, 4, 4, 6], "width": 90, "height": 69}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [0.10773, 0, 0.44062, 0.21345, 0.56533, 0.28686, 0.9141, 0.27553, 0.98602, 0.38838, 0.99089, 0.43797, 0.89355, 0.60124, 0.82159, 0.64792, 0.65915, 0.6938, 0.3845, 0.74853, 0.30178, 0.71544, 0.02798, 0.44948, 0.00152, 0.3582, 0.08633, 0.01804, 0.09144, 0], "triangles": [11, 12, 13, 3, 7, 2, 6, 3, 4, 6, 4, 5, 6, 7, 3, 8, 2, 7, 1, 11, 0, 13, 14, 0, 0, 11, 13, 1, 10, 11, 9, 10, 1, 9, 1, 2, 9, 2, 8], "vertices": [1, 80, 1.25, 22.87, 1, 1, 80, 60.14, 10.12, 1, 1, 80, 82.12, 5.98, 1, 1, 80, 140.7, 15.2, 1, 1, 80, 154.31, 6.03, 1, 1, 80, 155.79, 1.38, 1, 1, 80, 141.58, -16.58, 1, 1, 80, 130.08, -22.74, 1, 1, 80, 103.34, -30.94, 1, 1, 80, 57.81, -42.6, 1, 1, 80, 43.44, -41.35, 1, 1, 80, -6.2, -22.18, 1, 1, 80, -11.87, -14.02, 1, 1, 80, -2.11, 20.64, 1, 1, 80, -1.49, 22.49, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 170, "height": 97}}, "dangbu": {"dangbu": {"type": "mesh", "uvs": [1, 0.02374, 1, 0.03135, 0.96996, 0.12552, 0.93836, 0.22461, 0.89899, 0.35897, 0.85918, 0.49481, 0.84703, 0.52292, 0.79105, 0.772, 0.7415, 0.9925, 0.71442, 0.99979, 0.65485, 0.91958, 0.61806, 0.87589, 0.47485, 0.87056, 0.27841, 0.86326, 0.17426, 0.77565, 0.05893, 0.67863, 0, 0.67266, 0, 0.66204, 0.18583, 0.64352, 0.30281, 0.60273, 0.42557, 0.5025, 0.47829, 0.42406, 0.52384, 0.33996, 0.55831, 0.2763, 0.60899, 0.15697, 0.61734, 0.00084], "triangles": [14, 15, 18, 18, 15, 17, 15, 16, 17, 19, 14, 18, 13, 19, 12, 19, 20, 12, 13, 14, 19, 7, 11, 20, 11, 12, 20, 20, 21, 7, 6, 21, 22, 22, 23, 5, 23, 24, 4, 24, 25, 2, 9, 10, 8, 8, 10, 7, 10, 11, 7, 7, 21, 6, 6, 22, 5, 5, 23, 4, 4, 24, 3, 3, 24, 2, 1, 2, 0, 2, 25, 0], "vertices": [2, 51, -11.91, 11.86, 0.99999, 58, -29.23, 118.64, 1e-05, 2, 51, -9.67, 12.53, 1, 58, -26.96, 119.2, 0, 1, 51, 20.97, 10.91, 1, 1, 51, 53.21, 9.21, 1, 1, 51, 96.59, 8.06, 1, 1, 52, 25.9, 6.88, 1, 1, 52, 35.36, 5.36, 1, 2, 53, 43.21, 8.78, 0.56584, 54, -0.43, 8.78, 0.43416, 2, 56, 25.66, 11.82, 0.0057, 57, 14.75, 11.82, 0.9943, 1, 57, 19.53, 3.58, 1, 9, 53, 99.9, -22.96, 0.00181, 54, 56.27, -22.96, 0.06888, 55, 29, -22.96, 0.15745, 56, 12.63, -22.96, 0.28316, 57, 1.73, -22.96, 0.42205, 59, 97.97, 140.95, 0.01005, 60, 3.4, 144.14, 0.03136, 61, -67.35, 133.04, 0.02315, 63, -127.33, 116.68, 0.0021, 9, 53, 90.63, -38.85, 0.02694, 54, 46.99, -38.85, 0.23885, 55, 19.72, -38.85, 0.21192, 56, 3.36, -38.85, 0.17742, 57, -7.55, -38.85, 0.1022, 59, 97.39, 122.56, 0.03733, 60, 6.28, 125.97, 0.11072, 61, -60.75, 115.87, 0.0858, 63, -118.31, 100.65, 0.00882, 10, 53, 103.01, -86.26, 0.02233, 54, 59.38, -86.26, 0.1535, 55, 32.1, -86.26, 0.06898, 56, 15.74, -86.26, 0.0571, 57, 4.83, -86.26, 0.00372, 59, 130.89, 86.8, 0.03406, 60, 45.89, 97.12, 0.2607, 61, -16.01, 95.88, 0.34101, 62, -47.96, 93.06, 0.00222, 63, -71.12, 87.41, 0.05639, 9, 53, 119.99, -151.3, 0.00013, 54, 76.36, -151.3, 0.03307, 55, 49.09, -151.3, 0.01331, 56, 32.72, -151.3, 0.01239, 60, 100.22, 57.54, 0.06605, 61, 45.36, 68.46, 0.53667, 62, 15.02, 69.54, 0.06482, 63, -6.4, 69.25, 0.26451, 64, -23.95, 67.11, 0.00906, 8, 54, 60.72, -193.1, 0.00521, 55, 33.45, -193.1, 0.00213, 56, 17.09, -193.1, 0.00215, 60, 114.59, 15.28, 0.00048, 61, 68.2, 30.12, 0.18682, 62, 40.22, 32.71, 0.06222, 63, 21.79, 34.65, 0.41103, 64, 7.71, 35.65, 0.32995, 2, 63, 53.02, -3.66, 0.00019, 64, 42.76, 0.81, 0.99981, 2, 63, 72.16, -10.24, 1e-05, 64, 62.48, -3.73, 0.99999, 2, 63, 71.38, -13.41, 1e-05, 64, 62.04, -6.96, 0.99999, 2, 63, 8.3, -3.81, 0.82496, 64, -1.69, -4.01, 0.17504, 1, 61, 7.46, -2.46, 1, 2, 59, 62.97, -5.06, 0.91179, 60, -3.62, -5.85, 0.08821, 1, 59, 33.2, -9.36, 1, 2, 58, 103.99, -16.26, 0.56978, 59, 3.93, -16.63, 0.43022, 1, 58, 82.19, -9.48, 1, 1, 58, 42.47, -1.42, 1, 1, 58, -4.75, -10.11, 1], "hull": 26, "edges": [0, 50, 0, 2, 10, 12, 16, 18, 18, 20, 20, 22, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 46, 48, 48, 50, 22, 24, 24, 26, 12, 14, 14, 16, 6, 8, 8, 10, 2, 4, 4, 6, 26, 28, 28, 30, 42, 44, 44, 46], "width": 342, "height": 307}}, "gongjian": {"gongjian": {"type": "mesh", "uvs": [0.07531, 0, 0.16017, 0.01189, 0.16061, 0.0857, 0.15649, 0.08334, 0.36989, 0.36993, 0.397, 0.40185, 0.57303, 0.58559, 0.6448, 0.63422, 0.82476, 0.75227, 0.83596, 0.78121, 0.81209, 0.82158, 0.98854, 0.94531, 1, 0.96354, 1, 0.98319, 0.96823, 1, 0.96542, 1, 0.57036, 0.79752, 0.21507, 0.53853, 0.18142, 0.51974, 0.07845, 0.48531, 0.12571, 0.45307, 0.08393, 0.43325, 0, 0.37113, 0.12918, 0.32139, 0.10177, 0.28387, 0.05985, 0], "triangles": [3, 0, 1, 3, 1, 2, 3, 25, 0, 4, 24, 3, 24, 25, 3, 4, 23, 24, 21, 22, 23, 21, 23, 4, 20, 21, 4, 5, 20, 4, 18, 20, 5, 19, 20, 18, 17, 18, 5, 17, 5, 6, 16, 17, 6, 16, 6, 7, 16, 7, 8, 16, 8, 9, 10, 16, 9, 15, 11, 12, 15, 10, 11, 13, 15, 12, 16, 10, 15, 14, 15, 13], "vertices": [1, 33, -70.23, 17.26, 1, 1, 33, -51.39, 38.94, 1, 1, 33, -6.09, 18.19, 1, 1, 33, -8.1, 17.65, 1, 1, 33, 196.61, -0.45, 1, 1, 33, 219.87, -1.48, 1, 1, 33, 356.45, -1.51, 1, 1, 33, 396.03, 5.92, 1, 1, 33, 492.89, 25.63, 1, 1, 33, 512.15, 20.74, 1, 1, 33, 533.64, 2.28, 1, 1, 33, 633.5, 19.35, 1, 1, 33, 646.23, 17.58, 1, 1, 33, 658.28, 12.02, 1, 1, 33, 664.25, -2.11, 1, 1, 33, 663.87, -2.94, 1, 1, 33, 485.97, -62.24, 1, 1, 33, 278.85, -93.82, 1, 1, 33, 262.75, -98.44, 1, 1, 33, 227.63, -119.09, 1, 1, 33, 214.31, -96.02, 1, 1, 33, 196.47, -102.74, 1, 1, 33, 146.97, -109.94, 1, 1, 33, 134.08, -57.75, 1, 1, 33, 107.35, -55.22, 1, 1, 33, -72.34, 12.7, 1], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 325, "height": 675}}, "youhsou": {"youhsou": {"type": "mesh", "uvs": [0.83308, 0.015, 1, 0.10207, 1, 0.12122, 0.75348, 0.37499, 0.6893, 0.45033, 0.68934, 0.64111, 0.68937, 0.84922, 0.39634, 0.99869, 0.02254, 0.91572, 0.02132, 0.6986, 0.02035, 0.52669, 0.23165, 0.29415, 0.48556, 0.01469], "triangles": [4, 11, 3, 0, 1, 2, 3, 12, 0, 3, 0, 2, 11, 12, 3, 4, 10, 11, 10, 5, 9, 5, 10, 4, 9, 5, 6, 7, 8, 9, 6, 7, 9], "vertices": [1, 45, 17.94, 4.27, 1, 1, 45, 20.5, -4.83, 1, 2, 44, 50.27, -18.98, 0.00028, 45, 19.75, -5.84, 0.99972, 1, 45, 0.82, -12.58, 1, 2, 44, 26.66, -8.49, 0.49869, 45, -4.47, -14.83, 0.50131, 1, 44, 14.22, -10.42, 1, 1, 44, 0.64, -12.53, 1, 1, 44, -11.12, -1.01, 1, 1, 44, -8.29, 16.45, 1, 1, 44, 5.87, 18.7, 1, 2, 44, 17.07, 20.48, 0.736, 45, -31.58, -0.8, 0.264, 1, 45, -14.76, 5.77, 1, 2, 44, 53.67, 4.97, 0.00077, 45, 5.44, 13.67, 0.99923], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 20, 22, 22, 24, 8, 10, 10, 12, 16, 18, 18, 20], "width": 45, "height": 66}}, "yanjing1": {"yanjing1": {"type": "mesh", "uvs": [0.7776, 0.05595, 0.85206, 0.32522, 1, 0.32442, 1, 0.36914, 0.88909, 0.54992, 0.72987, 0.85395, 0.55863, 0.97751, 0.33921, 0.97783, 0.16374, 0.79088, 0.0159, 0.37275, 0.01609, 0.21509, 0.12214, 0.10158, 0.39916, 0.00327], "triangles": [1, 2, 3, 4, 1, 3, 8, 11, 12, 9, 10, 11, 8, 9, 11, 1, 5, 12, 1, 12, 0, 5, 1, 4, 6, 8, 12, 5, 6, 12, 7, 8, 6], "vertices": [1, 155, 14.17, -35.79, 1, 1, 155, 3.16, -41.56, 1, 1, 155, 4.17, -50.53, 1, 1, 155, 2.26, -50.74, 1, 1, 155, -6.2, -44.86, 1, 1, 155, -20.26, -36.63, 1, 1, 155, -26.68, -26.83, 1, 1, 155, -28.15, -13.52, 1, 1, 155, -21.32, -2.01, 1, 1, 155, -4.43, 8.91, 1, 1, 155, 2.31, 9.64, 1, 1, 155, 7.86, 3.74, 1, 1, 155, 13.91, -12.59, 1], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 61, "height": 43}}, "yanjing2": {"yanjing2": {"type": "mesh", "uvs": [0.51608, 0.01754, 0.77133, 0.31853, 0.94475, 0.52305, 0.98659, 0.6973, 0.98685, 0.84898, 0.95538, 0.89547, 0.76771, 0.98274, 0.48603, 0.9808, 0.25152, 0.81817, 0.12862, 0.53304, 0.0295, 0.2524, 0, 0.03354, 0, 0.01256, 0.16509, 0.11244, 0.31249, 0.0187], "triangles": [13, 11, 12, 10, 11, 13, 9, 10, 13, 14, 9, 13, 0, 8, 9, 3, 6, 2, 5, 3, 4, 14, 0, 9, 1, 8, 0, 6, 7, 1, 2, 6, 1, 1, 7, 8, 3, 5, 6], "vertices": [1, 154, 29, 9.07, 1, 1, 154, 15.2, -11.44, 1, 1, 154, 5.83, -25.38, 1, 1, 154, -3.02, -29.47, 1, 1, 154, -11.01, -30.36, 1, 1, 154, -13.71, -28.31, 1, 1, 154, -19.82, -15.01, 1, 1, 154, -21.99, 5.72, 1, 1, 154, -15.31, 23.91, 1, 1, 154, -1.28, 34.59, 1, 1, 154, 12.71, 43.51, 1, 1, 154, 24, 46.94, 1, 1, 154, 25.11, 47.06, 1, 1, 154, 21.17, 34.34, 1, 1, 154, 27.3, 24.04, 1], "hull": 15, "edges": [0, 28, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 2, 2, 4], "width": 74, "height": 53}}, "yanjing3": {"yanjing3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-4.2, -15.22, -19.78, 9.24, 4.68, 24.82, 20.26, 0.36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 29, "height": 29}}, "yanjing4": {"yanjing4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.27, -17.54, -19.17, 2.7, 6.14, 18.82, 19.03, -1.42], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 24, "height": 30}}, "youshou1": {"youshou1": {"type": "mesh", "uvs": [0.76423, 0.01031, 0.948, 0.03346, 0.99142, 0.06631, 0.98047, 0.10194, 0.80356, 0.21893, 0.75272, 0.30778, 0.77107, 0.39472, 0.79225, 0.4951, 0.80293, 0.5457, 0.80348, 0.77355, 0.53424, 0.92262, 0.34569, 0.99196, 0.18641, 0.98964, 0.01393, 0.84676, 0.01361, 0.5735, 0.03176, 0.51561, 0.11663, 0.24491, 0.29876, 0.1176, 0.45272, 0.00998], "triangles": [6, 15, 16, 5, 6, 16, 3, 1, 2, 3, 4, 0, 3, 0, 1, 4, 5, 18, 4, 18, 0, 17, 18, 5, 16, 17, 5, 10, 13, 14, 8, 14, 15, 7, 15, 6, 8, 15, 7, 10, 8, 9, 8, 10, 14, 11, 12, 13, 10, 11, 13], "vertices": [1, 49, 31.46, -6.94, 1, 1, 49, 35.31, -19.6, 1, 1, 49, 33.87, -23.77, 1, 2, 48, 86.44, -33.54, 0.00029, 49, 30.46, -24.61, 0.99971, 1, 49, 14.8, -18.39, 1, 2, 48, 64.83, -19.94, 0.06907, 49, 5.56, -18.98, 0.93093, 2, 48, 56.71, -22.27, 0.2651, 49, -1.33, -23.88, 0.7349, 2, 48, 47.33, -24.96, 0.95113, 49, -9.27, -29.53, 0.04887, 2, 48, 42.61, -26.31, 0.98364, 49, -13.28, -32.38, 0.01636, 1, 48, 20.91, -29.06, 1, 1, 48, 4.34, -11.87, 1, 1, 48, -3.93, 0.59, 1, 1, 48, -5.11, 11.84, 1, 1, 48, 6.98, 25.69, 1, 2, 48, 33.01, 28.97, 0.99485, 49, -40.73, 16.55, 0.00515, 2, 48, 38.68, 28.38, 0.97839, 49, -35.18, 17.88, 0.02161, 2, 48, 65.22, 25.62, 0.23501, 49, -9.24, 24.11, 0.76499, 2, 48, 78.95, 14.31, 0.05318, 49, 7.47, 18.01, 0.94682, 1, 49, 21.6, 12.86, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 14, 16, 32, 34, 34, 36, 10, 12, 12, 14], "width": 71, "height": 96}}, "youshou2": {"youshou2": {"type": "mesh", "uvs": [0.47049, 0, 0.47741, 0, 0.4982, 0.04892, 0.51873, 0.09723, 0.54061, 0.1487, 0.56306, 0.20151, 0.59328, 0.27262, 0.61283, 0.31859, 0.64221, 0.36351, 0.68811, 0.41884, 0.73351, 0.47356, 0.77312, 0.51122, 0.83087, 0.54976, 0.89365, 0.59165, 0.79436, 0.61195, 0.76013, 0.64449, 0.83039, 0.72051, 0.87711, 0.77106, 0.9141, 0.81109, 0.97106, 0.86604, 1, 0.89397, 1, 0.92274, 0.98961, 0.98851, 0.9016, 0.99967, 0.83407, 0.98255, 0.76043, 0.96389, 0.68605, 0.90352, 0.5983, 0.83231, 0.5382, 0.78354, 0.60313, 0.73127, 0.5706, 0.65628, 0.52647, 0.58747, 0.4922, 0.53405, 0.46146, 0.48614, 0.42606, 0.44549, 0.38558, 0.39902, 0.35102, 0.37124, 0.32277, 0.34853, 0.29422, 0.32558, 0.25208, 0.29171, 0.18948, 0.26351, 0.136, 0.23943, 0.06028, 0.23404, 0, 0.22975, 0, 0.22345, 0.0852, 0.14974, 0.12944, 0.11147, 0.16893, 0.0773, 0.27073, 0.05021, 0.36068, 0.02628, 0.43801, 0.0057], "triangles": [40, 46, 47, 40, 47, 48, 42, 45, 41, 45, 46, 41, 40, 41, 46, 45, 42, 44, 42, 43, 44, 5, 38, 39, 37, 38, 5, 39, 48, 4, 39, 40, 48, 34, 35, 7, 6, 35, 36, 36, 37, 6, 31, 32, 10, 32, 33, 9, 33, 34, 8, 16, 27, 29, 27, 28, 29, 29, 30, 15, 30, 31, 15, 4, 48, 3, 3, 49, 2, 48, 49, 3, 49, 50, 2, 50, 0, 2, 0, 1, 2, 5, 39, 4, 34, 7, 8, 7, 35, 6, 6, 37, 5, 33, 8, 9, 15, 31, 11, 15, 11, 14, 11, 31, 10, 14, 12, 13, 14, 11, 12, 32, 9, 10, 29, 15, 16, 22, 23, 21, 21, 23, 24, 21, 24, 19, 19, 24, 18, 24, 25, 18, 25, 26, 18, 19, 20, 21, 26, 27, 16, 26, 17, 18, 26, 16, 17], "vertices": [3, 14, 43.38, -1, 0.99935, 18, 42.6, -100.04, 0.00015, 19, -53.3, -85.61, 0.0005, 1, 14, 42.95, -2.59, 1, 1, 14, 27.07, -3.44, 1, 2, 13, 51.49, -2.7, 0.00386, 14, 11.38, -4.28, 0.99614, 1, 13, 34.83, -4.24, 1, 2, 12, 50.57, -6.59, 0.06733, 13, 17.73, -5.82, 0.93267, 1, 12, 27.49, -5.23, 1, 1, 12, 12.56, -4.35, 1, 2, 11, 35.74, -6.47, 0.47174, 12, -2.91, -5.76, 0.52826, 3, 10, 54.14, -7.99, 0.01234, 11, 15.45, -6.57, 0.98766, 12, -22.83, -9.66, 0, 1, 10, 34.25, -5.39, 1, 1, 10, 19.28, -5.13, 1, 1, 10, 1.32, -8, 1, 1, 10, -18.2, -11.12, 1, 2, 9, 28.89, -22.96, 0.3261, 10, -7.82, 11.01, 0.6739, 2, 9, 25.21, -10.56, 0.8798, 10, -10.28, 23.72, 0.1202, 2, 8, 69.1, -11.06, 0.73522, 9, -3.62, -11.06, 0.26478, 1, 8, 49.93, -11.39, 1, 1, 8, 34.75, -11.66, 1, 1, 8, 13.08, -13.23, 1, 1, 8, 2.07, -14.03, 1, 1, 8, -5.27, -9, 1, 1, 8, -20.62, 4.54, 1, 2, 8, -11.62, 23.76, 0.99742, 15, -115.59, -27.57, 0.00258, 2, 8, 1.83, 34.02, 0.98264, 15, -104.42, -14.86, 0.01736, 2, 8, 16.5, 45.21, 0.94531, 15, -92.24, -1, 0.05469, 2, 8, 41.9, 49.26, 0.81404, 15, -68.14, 7.96, 0.18596, 3, 8, 71.86, 54.04, 0.4524, 9, -0.87, 54.04, 0.00536, 15, -39.7, 18.54, 0.54223, 2, 8, 92.37, 57.31, 0.29315, 15, -20.23, 25.79, 0.70685, 3, 8, 96.95, 35.43, 0.13331, 9, 24.23, 35.43, 0.06964, 15, -11.44, 5.23, 0.79705, 1, 15, 12.92, 3.26, 1, 2, 15, 36.59, 4.58, 0.96864, 16, -7.94, 3.57, 0.03136, 1, 16, 10.47, 3.57, 1, 1, 16, 26.98, 3.57, 1, 1, 17, 1.44, 4.39, 1, 1, 17, 18.64, 2.57, 1, 1, 17, 30.5, 3.38, 1, 3, 17, 40.19, 4.04, 0.74074, 18, -2.38, 3.93, 0.25926, 19, -36.55, 26.43, 0, 2, 18, 7.41, 3.12, 1, 19, -28.67, 20.57, 0, 2, 18, 21.86, 1.92, 0.99999, 19, -17.04, 11.92, 1e-05, 2, 18, 38.79, 5.26, 0.72621, 19, -0.89, 5.81, 0.27379, 2, 18, 53.26, 8.11, 4e-05, 19, 12.89, 0.59, 0.99996, 1, 19, 30.94, 1.94, 1, 2, 14, 3.92, 125.58, 0, 19, 45.31, 3.02, 1, 1, 19, 45.63, 1.1, 1, 2, 14, 22.52, 99.58, 0.06393, 19, 29.43, -24.73, 0.93607, 3, 14, 31.21, 86.34, 0.17705, 18, 80.61, -20.48, 0.0082, 19, 21.01, -38.14, 0.81475, 3, 14, 38.96, 74.52, 0.28448, 18, 80.56, -34.62, 0.03207, 19, 13.5, -50.12, 0.68344, 3, 14, 40.75, 48.95, 0.53925, 18, 67.95, -56.93, 0.07791, 19, -9, -62.4, 0.38284, 3, 14, 42.33, 26.35, 0.80933, 18, 56.81, -76.65, 0.0478, 19, -28.88, -73.26, 0.14286, 3, 14, 43.69, 6.93, 0.97623, 18, 47.23, -93.61, 0.00591, 19, -45.97, -82.59, 0.01786], "hull": 51, "edges": [0, 100, 0, 2, 14, 16, 20, 22, 26, 28, 28, 30, 40, 42, 42, 44, 44, 46, 56, 58, 58, 60, 86, 88, 16, 18, 18, 20, 22, 24, 24, 26, 60, 62, 62, 64, 64, 66, 88, 90, 94, 96, 96, 98, 98, 100, 30, 32, 54, 56, 50, 52, 52, 54, 32, 34, 34, 36, 36, 38, 38, 40, 46, 48, 48, 50, 74, 76, 76, 78, 70, 72, 72, 74, 10, 12, 12, 14, 6, 8, 8, 10, 78, 80, 80, 82, 66, 68, 68, 70, 82, 84, 84, 86, 2, 4, 4, 6, 90, 92, 92, 94], "width": 238, "height": 309}}, "texiao": {"texiao": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-135.97, -98.64, -121.47, 1216.28, 631.48, 1207.98, 616.99, -106.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 920, "height": 527}}, "tou": {"tou": {"type": "mesh", "uvs": [0.68184, 0, 0.71522, 0.0501, 0.73974, 0.17715, 0.8167, 0.2587, 0.93567, 0.36719, 0.93555, 0.53646, 0.93543, 0.70291, 0.96043, 0.83147, 1, 0.88655, 1, 0.92242, 0.92867, 0.99747, 0.7306, 0.99447, 0.56827, 0.99202, 0.44703, 0.99018, 0.35653, 0.89347, 0.26051, 0.73958, 0.24972, 0.65371, 0.17493, 0.59968, 0.09162, 0.53949, 0.06397, 0.42494, 0.01909, 0.23895, 0.03101, 0.24291, 0.07459, 0.29828, 0.19681, 0.36022, 0.18079, 0.1734, 0.22113, 0.02795, 0.26753, 0.05527, 0.29367, 0.13997, 0.43771, 0.00646, 0.55895, 0.06276, 0.63119, 0.10281, 0.65203, 0], "triangles": [31, 0, 1, 30, 31, 1, 24, 25, 26, 24, 26, 27, 30, 1, 2, 23, 24, 27, 22, 20, 21, 19, 22, 23, 19, 20, 22, 5, 3, 4, 18, 19, 23, 17, 18, 23, 30, 16, 23, 17, 23, 16, 3, 5, 15, 30, 15, 16, 27, 28, 29, 2, 15, 30, 30, 27, 29, 23, 27, 30, 3, 15, 2, 6, 14, 5, 12, 13, 14, 15, 5, 14, 7, 8, 9, 14, 6, 12, 6, 11, 12, 7, 11, 6, 10, 11, 7, 9, 10, 7], "vertices": [1, 41, 23.05, 141.09, 1, 1, 41, 35.11, 135.28, 1, 1, 41, 53.5, 113.8, 1, 1, 41, 77.73, 106.89, 1, 1, 41, 113.33, 99.56, 1, 1, 41, 131.17, 67.26, 1, 1, 41, 148.72, 35.49, 1, 1, 41, 167.36, 13.78, 1, 1, 41, 181.2, 7.72, 1, 1, 41, 184.99, 0.87, 1, 1, 41, 178.43, -21.45, 1, 1, 41, 137.91, -43.13, 1, 1, 41, 104.7, -60.89, 1, 1, 41, 79.89, -74.16, 1, 1, 41, 51.31, -65.88, 1, 1, 41, 15.58, -47.31, 1, 1, 41, 4.32, -32.14, 1, 1, 41, -16.56, -30.23, 1, 1, 41, -39.83, -28.11, 1, 1, 41, -57.53, -9.36, 1, 1, 41, -86.27, 21.07, 1, 1, 41, -83.43, 21.65, 1, 1, 41, -68.74, 15.99, 1, 1, 41, -37.39, 17.9, 1, 1, 41, -60.36, 51.74, 1, 1, 41, -67.52, 84.01, 1, 1, 41, -55.22, 84.01, 1, 1, 41, -40.98, 70.79, 1, 1, 41, -25.83, 112.44, 1, 1, 41, 4.73, 115.32, 1, 1, 41, 23.62, 115.79, 1, 1, 41, 17, 137.74, 1], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 8, 10, 10, 12, 20, 22, 36, 38, 38, 40, 32, 34, 34, 36, 22, 24, 24, 26], "width": 232, "height": 218}}, "heishanlaoyao": {"heishanlaoyao": {"type": "mesh", "uvs": [0.63474, 0.0591, 0.66275, 0.13039, 0.7108, 0.20868, 0.70881, 0.29611, 0.71062, 0.31582, 0.76117, 0.31335, 0.79009, 0.36474, 0.77617, 0.42932, 0.74523, 0.47939, 0.73233, 0.48642, 0.69827, 0.41159, 0.69431, 0.40222, 0.65787, 0.40919, 0.64917, 0.41299, 0.64349, 0.47864, 0.63167, 0.51575, 0.61803, 0.52135, 0.67481, 0.58512, 0.64701, 0.66395, 0.66157, 0.6773, 0.7367, 0.68743, 0.75146, 0.72139, 0.74809, 0.72428, 0.80969, 0.7651, 0.84086, 0.82269, 0.84891, 0.81266, 0.86004, 0.77832, 0.87662, 0.7674, 0.84232, 0.7127, 0.85801, 0.67366, 0.89769, 0.65888, 0.90725, 0.60196, 0.9074, 0.57248, 0.87028, 0.52811, 0.86682, 0.52797, 0.8009, 0.56537, 0.79717, 0.56644, 0.79348, 0.53812, 0.83733, 0.50933, 0.89313, 0.50582, 0.93689, 0.52346, 0.98517, 0.46192, 0.92011, 0.37902, 0.91461, 0.35578, 0.96094, 0.32666, 0.94901, 0.28759, 0.89243, 0.2916, 0.84139, 0.2511, 0.87768, 0.16342, 0.94274, 0.1621, 0.93283, 0.19934, 0.92231, 0.20852, 1, 0.23659, 1, 0.26302, 0.98646, 0.3039, 0.96115, 0.32829, 0.96023, 0.36371, 0.99921, 0.36275, 0.99982, 0.46893, 0.93497, 0.52626, 0.9403, 0.53859, 0.97884, 0.57695, 0.97726, 0.62893, 0.94593, 0.67895, 0.94958, 0.68549, 0.99915, 0.71162, 0.99918, 0.79159, 0.94128, 0.8679, 0.94806, 0.87952, 0.99292, 0.91112, 0.9907, 0.9398, 0.92298, 0.97029, 0.85862, 0.95675, 0.69077, 0.95771, 0.67264, 0.96759, 0.5816, 0.97249, 0.55216, 0.94661, 0.53338, 1, 0.53191, 1, 0.49773, 0.98853, 0.47459, 0.93255, 0.48506, 0.86711, 0.50508, 0.80618, 0.5513, 0.76191, 0.65714, 0.76018, 0.66416, 0.74662, 0.68378, 0.69599, 0.66963, 0.68532, 0.64582, 0.67383, 0.58488, 0.71226, 0.5789, 0.63385, 0.56358, 0.62677, 0.53318, 0.66482, 0.53178, 0.71592, 0.45482, 0.73442, 0.43603, 0.73461, 0.38379, 0.76119, 0.34643, 0.74945, 0.30733, 0.71443, 0.31889, 0.63793, 0.30374, 0.62271, 0.2685, 0.65513, 0.20918, 0.65117, 0.18573, 0.63111, 0.17753, 0.65172, 0.13095, 0.68051, 0.12709, 0.68017, 0.11193, 0.72811, 0.0616, 0.74663, 0.03998, 0.77057, 0.00071, 0.77278, 0, 0.37405, 0.07393, 0.3168, 0.11571, 0.31815, 0.11015, 0.26957, 0.04298, 0.25991, 0.03192, 0.21241, 0.06069, 0.147, 0.09661, 0.10128, 0.16859, 0.064, 0.1857, 0.14102, 0.18347, 0.16505, 0.29789, 0.06799, 0.30538, 0.13269, 0.32008, 0.12827, 0.35455, 0.04113, 0.39163, 0.04528, 0.40959, 0.05504, 0.45008, 0.03141, 0.50203, 0.05851, 0.50886, 0.05565, 0.58761, 0.02541, 0.35205, 0.33456, 0.32309, 0.36511, 0.34336, 0.39567, 0.39067, 0.43991, 0.48529, 0.46203, 0.51908, 0.47046, 0.54032, 0.41463, 0.53935, 0.36828, 0.50846, 0.39567, 0.45632, 0.38935, 0.44184, 0.34089, 0.42639, 0.32719, 0.36943, 0.35985, 0.40129, 0.36828], "triangles": [118, 119, 120, 121, 118, 120, 123, 121, 122, 50, 48, 49, 51, 48, 50, 47, 48, 51, 114, 115, 116, 117, 118, 121, 45, 51, 52, 114, 117, 121, 114, 116, 117, 53, 45, 52, 46, 47, 51, 45, 46, 51, 2, 139, 1, 54, 45, 53, 113, 114, 121, 44, 45, 54, 129, 143, 127, 129, 127, 128, 55, 44, 54, 132, 124, 143, 126, 124, 125, 127, 124, 126, 143, 124, 127, 123, 124, 132, 139, 142, 143, 130, 131, 0, 43, 44, 55, 144, 132, 143, 56, 43, 55, 132, 121, 123, 133, 132, 144, 133, 121, 132, 113, 121, 133, 145, 144, 143, 145, 143, 142, 130, 1, 129, 1, 130, 0, 3, 139, 2, 139, 3, 4, 139, 129, 1, 139, 143, 129, 42, 43, 56, 140, 141, 142, 145, 142, 141, 134, 133, 144, 139, 140, 142, 139, 4, 13, 11, 12, 4, 7, 10, 11, 12, 13, 4, 138, 139, 13, 140, 139, 138, 6, 4, 5, 145, 134, 144, 135, 145, 141, 135, 134, 145, 41, 56, 57, 42, 56, 41, 136, 141, 140, 137, 136, 140, 135, 141, 136, 41, 57, 58, 138, 137, 140, 14, 138, 13, 16, 137, 138, 11, 4, 7, 6, 7, 4, 8, 10, 7, 8, 9, 10, 138, 14, 16, 15, 16, 14, 59, 39, 40, 58, 59, 40, 58, 40, 41, 34, 38, 39, 33, 34, 39, 35, 37, 38, 35, 38, 34, 36, 37, 35, 32, 39, 59, 32, 59, 60, 33, 39, 32, 133, 103, 113, 134, 103, 133, 100, 134, 135, 91, 137, 16, 90, 91, 16, 61, 32, 60, 61, 31, 32, 62, 31, 61, 134, 100, 103, 17, 90, 16, 99, 100, 135, 102, 103, 100, 113, 103, 112, 101, 102, 100, 63, 30, 31, 18, 90, 17, 91, 136, 137, 88, 90, 18, 88, 18, 19, 62, 63, 31, 104, 106, 103, 105, 106, 104, 20, 87, 19, 88, 19, 87, 86, 87, 20, 89, 90, 88, 99, 135, 136, 92, 99, 136, 136, 91, 92, 92, 94, 99, 98, 99, 97, 22, 20, 21, 112, 106, 111, 103, 106, 112, 106, 108, 111, 92, 93, 94, 94, 95, 99, 107, 108, 106, 95, 97, 99, 96, 97, 95, 30, 28, 29, 64, 27, 30, 64, 30, 63, 27, 28, 30, 108, 110, 111, 109, 110, 108, 27, 66, 67, 64, 66, 27, 65, 66, 64, 70, 68, 69, 76, 83, 84, 82, 83, 76, 81, 82, 76, 80, 81, 76, 25, 67, 24, 72, 24, 67, 25, 26, 27, 27, 67, 25, 86, 22, 85, 22, 86, 20, 22, 84, 85, 73, 23, 24, 73, 24, 72, 84, 22, 23, 73, 84, 23, 74, 76, 84, 73, 74, 84, 68, 71, 72, 68, 72, 67, 71, 68, 70, 75, 76, 74, 79, 80, 76, 78, 79, 76, 77, 78, 76], "vertices": [442.18, -20.81, 406.21, -73.54, 375.05, -144.56, 313.4, -181.71, 300.59, -191.79, 326.7, -228.99, 304.98, -273.62, 253.44, -291.63, 203.76, -290.34, 192.65, -283.68, 228.16, -224.79, 232.75, -217.64, 210.33, -193.14, 203.5, -188.23, 155.19, -212.95, 123.73, -220.41, 113.26, -212.55, 96.39, -283.75, 28.26, -297.56, 26.01, -314.49, 55.23, -375.87, 38.79, -402.06, 35.15, -400.8, 36.53, -465.49, 11.6, -514.57, 22.45, -516.23, 51.65, -509.48, 67.24, -517.2, 88.65, -467.04, 123.32, -461.66, 152.73, -485.18, 196.85, -467.24, 217.38, -454.32, 230.27, -406.6, 228.7, -403.91, 170.93, -370.52, 168.39, -368.17, 186.27, -352.85, 227.41, -373.33, 256.77, -414.04, 265.63, -454.99, 331.65, -464.33, 357.8, -378.41, 371.28, -363.96, 413.84, -386.18, 435.2, -359.86, 405.12, -318.79, 408.61, -262.22, 486.98, -250.94, 519.28, -299.63, 488.66, -308.59, 477.21, -304.68, 495.21, -375.93, 476.86, -387.62, 441.96, -395.44, 412.82, -387.06, 387.79, -402.02, 407.26, -431.12, 333.85, -478.53, 262.77, -454.76, 256.78, -464.26, 248.75, -510.41, 211.91, -532.2, 162.07, -530.58, 159.3, -536.24, 165.07, -585.34, 109.57, -620.72, 28.67, -610.61, 23.88, -620.89, 23.59, -668.83, 2.62, -679.84, -51.23, -642.03, -72.88, -587.3, -154.53, -460.6, -170.14, -451.24, -217.46, -384.45, -213.7, -350.71, -259.82, -360.1, -260.53, -358.98, -269.06, -328.02, -241.38, -285.74, -190.89, -264.73, -138.95, -252.95, -85.91, -268.39, -33.65, -347.78, -20.85, -347.1, 23.76, -339.57, 24.34, -324.14, 20.83, -301.03, -35.25, -271.86, 16.29, -232.66, 13.81, -217.93, -27.27, -211.73, -63.41, -233.27, -113.39, -183.16, -122.58, -169.01, -166.23, -141.2, -176.11, -107.72, -170.67, -62.62, -111.99, -37.54, -108.74, -19.34, -148.24, -6.99, -174.12, 39.69, -171.5, 66.32, -189.77, 63.42, -232.22, 85.97, -233.85, 89.04, -274.44, 79.33, -311.58, 109.26, -338.63, 115.04, -359.11, 143.81, -82.68, 320.66, -7.28, 289.98, 11.95, 257.74, 42.98, 283.44, 17.28, 338.58, 44.91, 367.96, 104.2, 375.09, 153.26, 368.1, 213.87, 330.08, 168.66, 283.05, 150.91, 274.12, 273.48, 230.38, 232.18, 196.1, 242.35, 186.92, 319.46, 199.35, 334.47, 169.42, 336.36, 151.51, 372.3, 131.29, 378.56, 79.97, 383.84, 76.05, 442.83, 29.78, 114.58, 71.49, 79.4, 79.91, 67.98, 51.05, 60.09, -4.35, 90.38, -85.79, 100.84, -115.11, 149.84, -106.51, 181.55, -85.28, 147.63, -73.99, 126.86, -31.71, 153.51, 0.69, 155.57, 18.44, 105.41, 47.14, 114.94, 19.29], "hull": 132, "edges": [0, 262, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262], "width": 655, "height": 600}}, "shenti2": {"shenti2": {"type": "mesh", "uvs": [0.60515, 0.0003, 0.90242, 0.17802, 0.99228, 0.32692, 0.99289, 0.48689, 0.93248, 0.61419, 0.52328, 0.98858, 0.26235, 0.99222, 0.11378, 0.91243, 0.03799, 0.77123, 0, 0.48194, 0, 0.44899, 0.09106, 0.14674, 0.22477, 0.0074], "triangles": [1, 9, 10, 3, 4, 1, 3, 1, 2, 4, 8, 9, 11, 12, 0, 0, 10, 11, 4, 9, 1, 10, 0, 1, 8, 6, 7, 4, 5, 8, 5, 6, 8], "vertices": [1, 2, -20.87, 10.79, 1, 1, 2, 15.15, 99.4, 1, 1, 2, 54.78, 132.84, 1, 1, 2, 103.05, 144.2, 1, 1, 2, 145.08, 137.63, 1, 1, 2, 282.45, 59, 1, 1, 2, 299.04, -7.6, 1, 1, 2, 283.77, -51.25, 1, 1, 2, 245.63, -80.55, 1, 1, 2, 160.52, -110.53, 1, 1, 2, 150.58, -112.84, 1, 1, 2, 53.89, -110.67, 1, 1, 2, 3.87, -86.17, 1], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 263, "height": 310}}, "texiao2": {"texiao2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-10.23, -36.38, -11.17, 42.61, 81.82, 43.72, 82.76, -35.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 93}}, "youdabi_2": {"youdabi_2": {"type": "mesh", "uvs": [0.44953, 0, 0.52603, 0.02261, 0.7333, 0.34529, 0.83501, 0.4827, 1, 0.6555, 1, 0.65955, 0.94782, 0.80411, 0.65409, 0.99216, 0.30245, 0.99643, 0.16926, 0.95838, 0.10965, 0.91143, 0.0023, 0.50162, 0.11483, 0.27345, 0.27663, 0.06827, 0.41683, 0], "triangles": [3, 4, 5, 6, 3, 5, 2, 10, 11, 14, 0, 1, 13, 14, 1, 13, 1, 2, 12, 13, 2, 12, 2, 11, 2, 8, 10, 8, 2, 3, 10, 8, 9, 6, 7, 3, 7, 8, 3], "vertices": [1, 4, -15.64, 49.29, 1, 1, 4, -7.08, 57.46, 1, 1, 4, 53.39, 59.53, 1, 1, 4, 80.07, 62.15, 1, 1, 4, 116.22, 70.25, 1, 1, 4, 116.79, 69.94, 1, 1, 4, 133.78, 52.09, 1, 1, 4, 140.16, -0.37, 1, 1, 4, 116.28, -46.22, 1, 1, 4, 101.58, -60.54, 1, 1, 4, 90.73, -64.66, 1, 1, 4, 24.78, -47.1, 1, 1, 4, 0.07, -15.02, 1, 1, 4, -17.94, 21.67, 1, 1, 4, -17.91, 45.06, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 147, "height": 162}}, "jianxia": {"jianxia": {"type": "mesh", "uvs": [0.74305, 0, 0.81593, 0.02559, 0.85262, 0.09593, 0.91559, 0.21235, 0.9695, 0.31202, 0.97475, 0.3368, 0.70792, 0.55902, 0.4299, 0.79054, 0.18022, 0.99387, 0.10274, 1, 0.0086, 0.65558, 0, 0.62503, 0, 0.61494, 0.176, 0.47065, 0.48769, 0.20366, 0.72545, 0], "triangles": [6, 14, 3, 7, 13, 14, 14, 15, 2, 1, 15, 0, 1, 2, 15, 3, 14, 2, 6, 3, 4, 6, 4, 5, 10, 11, 12, 13, 10, 12, 6, 7, 14, 10, 13, 7, 8, 10, 7, 9, 10, 8], "vertices": [1, 32, -2.04, -73.55, 1, 1, 32, -19.39, -49.73, 1, 1, 32, -16.22, -21.47, 1, 1, 32, -11.67, 25.83, 1, 1, 32, -7.78, 66.32, 1, 1, 32, -4.28, 74.54, 1, 1, 32, 124.69, 76.86, 1, 1, 32, 259.06, 79.28, 1, 1, 32, 378.78, 80.16, 1, 1, 32, 404.12, 64.29, 1, 1, 32, 362.03, -54.69, 1, 1, 32, 358.37, -65.3, 1, 1, 32, 356.28, -68.16, 1, 1, 32, 271.69, -69.05, 1, 1, 32, 119.51, -73.87, 1, 1, 32, 3.43, -77.55, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 10, 12, 12, 14, 4, 6, 6, 8], "width": 385, "height": 351}}, "youjiao2": {"youjiao2": {"type": "mesh", "uvs": [0.83612, 0.01878, 0.91091, 0.07275, 0.97159, 0.23324, 0.97155, 0.3951, 0.97152, 0.48616, 0.90742, 0.62802, 0.79633, 0.74277, 0.67642, 0.86663, 0.3087, 0.99888, 0.19463, 0.97331, 0.1057, 0.92723, 0, 0.62462, 0, 0.62362, 0.04102, 0.44491, 0.12359, 0.33247, 0.29597, 0.21671, 0.47526, 0.09632, 0.58839, 0.0329, 0.75882, 0.00185], "triangles": [2, 0, 1, 0, 2, 3, 6, 11, 12, 17, 18, 0, 4, 5, 3, 17, 0, 3, 5, 6, 3, 3, 16, 17, 3, 15, 16, 6, 15, 3, 14, 15, 6, 13, 14, 6, 6, 12, 13, 7, 11, 6, 10, 11, 7, 8, 9, 10, 7, 8, 10], "vertices": [1, 27, 131.28, -1.06, 1, 1, 27, 128.17, -11.74, 1, 1, 27, 110.74, -28.64, 1, 1, 27, 90.04, -40.05, 1, 1, 27, 78.4, -46.47, 1, 1, 27, 57.01, -50.58, 1, 1, 27, 36.71, -48.46, 1, 1, 27, 14.79, -46.17, 1, 1, 27, -20.76, -21.69, 1, 1, 27, -23.28, -9.4, 1, 1, 27, -21.9, 2.02, 1, 1, 27, 11.42, 33.08, 1, 1, 27, 11.55, 33.15, 1, 1, 27, 36.47, 41.99, 1, 1, 27, 55.04, 42.33, 1, 1, 27, 78.58, 34.64, 1, 1, 27, 103.06, 26.65, 1, 1, 27, 116.91, 20.72, 1, 1, 27, 129.52, 7.24, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 28, 30, 30, 32, 10, 12, 12, 14, 4, 6, 6, 8], "width": 105, "height": 146}}, "yoluxiaobi2": {"yoluxiaobi2": {"type": "mesh", "uvs": [0.98843, 0.13084, 0.99092, 0.50701, 0.91897, 0.71952, 0.80577, 0.85119, 0.45206, 1, 0.32607, 1, 0.10404, 0.89013, 0.00966, 0.61641, 0.01057, 0.23916, 0.12933, 0.01822, 0.43291, 0.03041, 0.71618, 0.04179, 0.91196, 0.04966], "triangles": [1, 2, 12, 1, 12, 0, 2, 3, 11, 2, 11, 12, 9, 7, 8, 6, 7, 9, 5, 9, 10, 6, 9, 5, 4, 10, 11, 4, 11, 3, 5, 10, 4], "vertices": [1, 43, 87.24, 9.17, 1, 1, 43, 86.41, -5.86, 1, 1, 43, 79.14, -13.87, 1, 1, 43, 68.27, -18.38, 1, 1, 43, 35.03, -22, 1, 1, 43, 23.35, -21.18, 1, 1, 43, 3.06, -15.34, 1, 1, 43, -4.93, -3.8, 1, 1, 43, -3.78, 11.25, 1, 1, 43, 7.86, 19.29, 1, 1, 43, 35.99, 16.81, 1, 1, 43, 62.23, 14.5, 1, 1, 43, 80.37, 12.91, 1], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 93, "height": 40}}, "youdabi2": {"youdabi2": {"type": "mesh", "uvs": [0.77525, 0.06377, 0.95873, 0.30291, 0.99736, 0.59321, 0.96026, 0.76481, 0.86899, 0.93607, 0.42502, 1, 0.40795, 1, 0.09371, 0.87502, 0, 0.66303, 0, 0.50162, 0.04963, 0.18176, 0.15536, 0.02032, 0.38906, 0.01914], "triangles": [7, 8, 9, 3, 1, 2, 10, 12, 9, 9, 12, 7, 3, 4, 0, 3, 0, 1, 5, 6, 12, 0, 5, 12, 11, 12, 10, 6, 7, 12, 4, 5, 0], "vertices": [1, 42, 53.58, 21.45, 1, 1, 42, 67.94, 9.77, 1, 1, 42, 71.19, -4.69, 1, 1, 42, 68.5, -13.32, 1, 1, 42, 61.64, -22.01, 1, 1, 42, 27.52, -25.87, 1, 1, 42, 26.2, -25.89, 1, 1, 42, 1.89, -20.11, 1, 1, 42, -5.52, -9.65, 1, 1, 42, -5.68, -1.58, 1, 1, 42, -2.16, 14.49, 1, 1, 42, 5.82, 22.71, 1, 1, 42, 23.81, 23.12, 1], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 77, "height": 50}}, "dengguang1": {"dengguang1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.37, -18.23, -34.1, 7.77, 31.9, 7.08, 31.62, -18.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 26, "height": 66}}, "pidai": {"pidai": {"type": "mesh", "uvs": [0.92001, 0.12374, 0.91986, 0.21966, 0.85231, 0.23404, 0.75217, 0.31064, 0.73776, 0.32866, 0.71671, 0.33697, 0.61365, 0.42919, 0.37277, 0.63318, 0.22468, 0.77897, 0.05845, 0.99872, 0.02982, 0.95973, 0, 0.84554, 0, 0.80866, 0.17809, 0.62468, 0.37371, 0.42258, 0.54859, 0.27431, 0.6741, 0.16789, 0.80279, 0.09567], "triangles": [9, 10, 8, 10, 11, 8, 11, 12, 8, 12, 13, 8, 8, 13, 7, 7, 14, 6, 7, 13, 14, 14, 15, 6, 6, 15, 5, 3, 4, 5, 16, 3, 5, 16, 5, 15, 3, 16, 2, 16, 17, 2, 1, 2, 0, 0, 2, 17], "vertices": [1, 76, -27.28, 8.46, 1, 1, 76, -10.22, 25.72, 1, 1, 76, 4.32, 16.5, 1, 1, 76, 35.68, 12.81, 1, 1, 76, 41.44, 13.54, 1, 1, 76, 46.65, 11.36, 1, 1, 76, 81.3, 9.97, 1, 1, 77, 38.36, 4.64, 1, 1, 78, 17.38, 5.04, 1, 1, 79, 37.13, 15.59, 1, 1, 79, 35.28, 3.56, 1, 2, 78, 69.05, -22.23, 0.11577, 79, 20.3, -22.23, 0.88423, 2, 78, 62.51, -28.88, 0.23392, 79, 13.75, -28.88, 0.76608, 2, 77, 71.38, -30.91, 0.531, 78, -1.75, -30.91, 0.469, 2, 76, 122.69, -33.15, 0.46804, 77, 0.8, -33.15, 0.53196, 1, 76, 65.34, -29.32, 1, 1, 76, 24.18, -26.56, 1, 1, 76, -11.47, -17.09, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 32, 34, 24, 26, 26, 28, 28, 30, 30, 32], "width": 249, "height": 253}}, "zuoxiaobi1": {"zuoxiaobi1": {"type": "mesh", "uvs": [0.21557, 0, 0.25805, 0, 0.39956, 0.04223, 0.76592, 0.21662, 0.91194, 0.349, 0.99274, 0.48723, 0.99225, 0.62086, 0.94315, 0.788, 0.84107, 0.92209, 0.75322, 0.97767, 0.58702, 0.9137, 0.355, 0.77432, 0.01456, 0.3708, 0.00488, 0.33712, 0.02682, 0.18658, 0.08213, 0.07747], "triangles": [12, 13, 14, 6, 4, 5, 1, 14, 15, 11, 2, 3, 15, 0, 1, 14, 1, 2, 2, 12, 14, 11, 12, 2, 6, 10, 4, 10, 11, 3, 10, 3, 4, 7, 10, 6, 8, 10, 7, 9, 10, 8], "vertices": [1, 37, -16.58, 19.86, 1, 1, 37, -11.8, 22.53, 1, 1, 37, 6.47, 27.28, 1, 1, 37, 57.33, 33.14, 1, 1, 37, 81.07, 29.28, 1, 1, 37, 97.79, 20.74, 1, 1, 37, 105.11, 7.53, 1, 1, 37, 108.8, -12.05, 1, 1, 37, 104.7, -31.7, 1, 1, 37, 97.88, -42.72, 1, 1, 37, 75.64, -46.87, 1, 1, 37, 41.82, -47.73, 1, 1, 37, -18.76, -29.37, 1, 1, 37, -21.71, -26.66, 1, 1, 37, -27.54, -10.43, 1, 1, 37, -27.33, 3.82, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30], "width": 129, "height": 113}}, "shent1": {"shent1": {"type": "mesh", "uvs": [0.89823, 0.04739, 0.97829, 0.18051, 1, 0.38733, 1, 0.44695, 0.95686, 0.69015, 0.88227, 0.91833, 0.83589, 0.97331, 0.67296, 0.98563, 0.48292, 1, 0.47488, 1, 0.33254, 0.89887, 0.22208, 0.65312, 0.21679, 0.51325, 0.00609, 0.50655, 0.00738, 0.47655, 0.02598, 0.50678, 0.21932, 0.51234, 0.21921, 0.47307, 0.30269, 0.22584, 0.51294, 0.12385, 0.76541, 0.00138], "triangles": [2, 0, 1, 13, 14, 15, 16, 17, 18, 12, 15, 16, 13, 15, 12, 10, 11, 16, 12, 16, 11, 0, 2, 20, 3, 4, 2, 10, 18, 19, 10, 16, 18, 19, 20, 6, 2, 4, 20, 20, 4, 6, 10, 19, 9, 4, 5, 6, 6, 7, 19, 19, 8, 9, 7, 8, 19], "vertices": [1, 35, 15.53, -43.67, 1, 1, 35, -3.94, -18.89, 1, 1, 35, -4.08, 13.62, 1, 1, 35, -2.28, 22.62, 1, 1, 35, 17.69, 56.83, 1, 1, 35, 46.43, 86.93, 1, 1, 35, 61.69, 92.52, 1, 1, 35, 109.84, 84.85, 1, 1, 35, 165.99, 75.91, 1, 1, 35, 168.35, 75.44, 1, 1, 35, 207.04, 51.85, 1, 1, 35, 232.03, 8.27, 1, 1, 35, 229.37, -13.16, 1, 1, 35, 290.95, -26.49, 1, 1, 35, 289.67, -30.95, 1, 1, 35, 285.13, -25.29, 1, 1, 35, 228.6, -13.15, 1, 1, 35, 227.45, -19.09, 1, 1, 35, 195.53, -51.54, 1, 1, 35, 130.8, -54.65, 1, 1, 35, 53.09, -58.39, 1], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 12, 14, 14, 16], "width": 299, "height": 154}}, "zuodabi1": {"zuodabi1": {"type": "mesh", "uvs": [0.39083, 0.01065, 0.51321, 0.06384, 0.62458, 0.11223, 0.69652, 0.30132, 0.72718, 0.34797, 0.87644, 0.47312, 0.99085, 0.56905, 0.99225, 0.80904, 0.89943, 0.92365, 0.76916, 0.99498, 0.62481, 0.98113, 0.52897, 0.93449, 0.44, 0.84649, 0.32763, 0.73533, 0.26752, 0.65069, 0.04688, 0.55908, 0, 0.45639, 0, 0.43749, 0.02356, 0.27894, 0.06304, 0.17335, 0.18711, 0.03886, 0.27871, 0.00562], "triangles": [15, 16, 17, 18, 14, 17, 14, 15, 17, 20, 0, 19, 0, 20, 21, 14, 0, 1, 14, 19, 0, 14, 18, 19, 13, 14, 4, 1, 3, 14, 2, 3, 1, 4, 14, 3, 12, 13, 4, 12, 4, 5, 7, 8, 5, 7, 5, 6, 11, 12, 5, 5, 10, 11, 8, 10, 5, 9, 10, 8], "vertices": [1, 36, 16.01, 29.22, 1, 1, 36, 29.99, 33.94, 1, 1, 36, 42.72, 38.25, 1, 1, 36, 61.49, 28.66, 1, 1, 36, 67.22, 27.23, 1, 1, 36, 88.3, 28.27, 1, 1, 36, 104.46, 29.07, 1, 1, 36, 120.62, 10.36, 1, 1, 36, 120.37, -5.37, 1, 1, 36, 114.04, -20.43, 1, 1, 36, 100.81, -29.83, 1, 1, 36, 89.53, -33.14, 1, 1, 36, 76.07, -32.71, 1, 1, 36, 59.06, -32.17, 1, 1, 36, 48.28, -29.9, 1, 1, 36, 23.36, -38.76, 1, 1, 36, 12.5, -34.12, 1, 1, 36, 11.23, -32.64, 1, 1, 36, 2.64, -18.5, 1, 1, 36, -1.05, -7.36, 1, 1, 36, 0.53, 12.2, 1, 1, 36, 6.12, 21.46, 1], "hull": 22, "edges": [0, 42, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 8, 10, 10, 12, 0, 2, 2, 4, 22, 24, 24, 26], "width": 112, "height": 103}}, "yijin": {"yijin": {"type": "mesh", "uvs": [0.90023, 0, 0.96141, 0.08733, 1, 0.2877, 1, 0.30933, 0.93931, 0.4522, 0.87603, 0.60115, 0.77649, 0.69801, 0.67165, 0.80002, 0.57416, 0.89489, 0.48767, 0.97904, 0.28879, 0.98513, 0.11096, 0.99058, 0.05871, 0.86362, 0.00797, 0.74032, 0.00755, 0.51604, 0.03484, 0.4763, 0.20285, 0.44041, 0.36613, 0.40552, 0.51699, 0.31898, 0.61546, 0.26248, 0.64383, 0.08582, 0.72936, 0.04841, 0.84003, 0], "triangles": [21, 2, 4, 2, 21, 1, 1, 21, 22, 1, 22, 0, 4, 20, 21, 3, 4, 2, 9, 17, 8, 10, 17, 9, 11, 12, 10, 8, 17, 7, 7, 18, 6, 4, 5, 20, 6, 19, 5, 5, 19, 20, 13, 14, 15, 16, 13, 15, 12, 13, 16, 10, 16, 17, 12, 16, 10, 6, 18, 19, 17, 18, 7], "vertices": [1, 80, 12.96, -9.83, 1, 1, 80, 21.75, -17.08, 1, 1, 80, 29.2, -35.47, 1, 1, 80, 29.48, -37.52, 1, 1, 69, 21.88, 9.33, 1, 1, 69, 37.98, 6.35, 1, 1, 70, 7.8, 2.93, 1, 2, 70, 24.21, 3.97, 0.85281, 71, -1.52, 3.97, 0.14719, 2, 71, 13.74, 4.94, 0.92609, 72, -3.41, 4.94, 0.07391, 3, 68, -30.55, 54.03, 0.00029, 71, 27.28, 5.8, 0.0163, 72, 10.13, 5.8, 0.98341, 5, 66, 44, 47.74, 0.01408, 67, 10.84, 50.26, 0.07839, 68, -5.77, 50.26, 0.25239, 71, 48.63, -7.32, 4e-05, 72, 31.48, -7.32, 0.6551, 4, 66, 65.28, 40.72, 0, 67, 33, 46.88, 0.07123, 68, 16.39, 46.88, 0.49849, 72, 50.58, -19.06, 0.43028, 3, 67, 37.37, 33.74, 0.05735, 68, 20.76, 33.74, 0.61959, 72, 49.48, -32.87, 0.32306, 3, 67, 41.62, 20.97, 0.02422, 68, 25, 20.97, 0.7893, 72, 48.42, -46.28, 0.18648, 2, 68, 21.32, -0.24, 0.98403, 72, 36.76, -64.38, 0.01597, 2, 68, 17.27, -3.4, 0.99478, 72, 31.8, -65.72, 0.00522, 1, 67, 12.44, -3.12, 1, 1, 66, 16.16, -1.41, 1, 1, 65, 36.29, -1.11, 1, 4, 65, 23.3, 2.71, 0.96599, 69, 17.7, -35.16, 0.02654, 70, 2.11, -43.2, 0.00746, 71, -23.62, -43.2, 1e-05, 1, 80, -17.91, -22.42, 1, 1, 80, -7.73, -17.39, 1, 1, 80, 5.45, -10.87, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 26, 28, 28, 30, 38, 40, 6, 8, 8, 10, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 10, 12, 12, 14, 14, 16, 16, 18], "width": 126, "height": 96}}}}], "animations": {"animation1": {"slots": {"yanjing3": {"color": [{"color": "ffffff39", "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "color": "ffffffc1", "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.6667, "color": "ffa5a5c7", "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "color": "ffccccb9", "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "color": "ffffff96", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd6", "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 4.6667, "color": "ffffff39"}]}, "heishanlaoyao": {"color": [{"color": "ffffff39", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffffc7", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff96", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd6", "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 4.6667, "color": "ffffff39"}]}, "wu6": {"color": [{"color": "ffffff9a"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.8, "color": "ffffff00"}, {"time": 4.6667, "color": "ffffff9a"}]}, "texiao": {"color": [{"color": "ffffff76"}, {"time": 0.3667, "color": "ffffffb7"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffff"}, {"time": 3.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00"}, {"time": 4.6667, "color": "ffffff76"}]}, "dengguang2": {"color": [{"color": "ffffffd0"}, {"time": 0.6667, "color": "ffffffb3"}, {"time": 1.2667, "color": "969595ff"}, {"time": 1.7667, "color": "ded6d6ff"}, {"time": 2.5, "color": "ded6d6b7"}, {"time": 3.1, "color": "676767ff"}, {"time": 3.7, "color": "ffffffb3"}, {"time": 4.6667, "color": "ffffffd1"}]}, "wu4": {"color": [{"color": "ffffff9a"}, {"time": 0.3, "color": "ffffffcf"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.8, "color": "ffffff00"}, {"time": 4.6667, "color": "ffffff9a"}]}, "texiao3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffff35"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff7f"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00"}, {"time": 3.8333, "color": "ffffffff"}, {"time": 4.1667, "color": "ffffff00"}]}, "xuanzhuan1": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "color": "656077ff", "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "color": "656077ff", "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "color": "ffffffff"}]}, "yachi": {"color": [{"color": "ffffff20", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff9a", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff56", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd6", "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 4.6667, "color": "ffffff20"}]}, "wu3": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}, {"time": 4.2333, "color": "ffffffcf"}, {"time": 4.5, "color": "ffffffff"}, {"time": 4.6667, "color": "ffffff7f"}]}, "xuanzhuan2": {"color": [{"color": "ffffffde", "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "color": "ffffff73", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "color": "ffffffdd", "curve": 0.25, "c3": 0.75}, {"time": 3.5, "color": "ffffff7c", "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "color": "ffffffdd"}]}, "dengguang1": {"color": [{"color": "ffffffd0"}, {"time": 0.6667, "color": "ffffffb3"}, {"time": 1.2667, "color": "969595ff"}, {"time": 1.7667, "color": "ded6d6ff"}, {"time": 2.5, "color": "ded6d6b7"}, {"time": 3.1, "color": "676767ff"}, {"time": 3.7, "color": "ffffffb3"}, {"time": 4.6667, "color": "ffffffd1"}]}, "wu1": {"color": [{"color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 3.7667, "color": "ffffffff"}, {"time": 4.1, "color": "ffffff00"}]}, "wu5": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}, {"time": 4.5, "color": "ffffffff"}, {"time": 4.6667, "color": "ffffff7f"}]}, "texiao4": {"color": [{"color": "ffffffb2"}, {"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffff9a"}, {"time": 2.2333, "color": "ffffffff"}, {"time": 2.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1333, "color": "ffffff00"}, {"time": 4.5667, "color": "ffffffff"}, {"time": 4.6667, "color": "ffffffb2"}]}, "texiao2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffd2", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd2", "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "color": "ffffff00"}]}, "wu2": {"color": [{"color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 3.7667, "color": "ffffffff"}, {"time": 4.1, "color": "ffffff00"}]}, "yanjing4": {"color": [{"color": "ffffff39", "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "color": "ffffffc1", "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.6667, "color": "ffa5a5c7", "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "color": "ffccccb9", "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "color": "ffffff96", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd6", "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 4.6667, "color": "ffffff39"}]}}, "bones": {"shenti2": {"rotate": [{"curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.8333, "angle": 7.74, "curve": 0.329, "c2": 0.32, "c3": 0.682, "c4": 0.71}, {"time": 1.6667, "angle": -0.76, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.3333, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 3.1667, "angle": 7.74, "curve": 0.329, "c2": 0.32, "c3": 0.682, "c4": 0.71}, {"time": 4, "angle": -0.76, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 18.2, "y": -33.89, "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 1.3333, "x": 34.66, "y": -31.78, "curve": 0.25, "c4": 0.77}, {"time": 1.6667, "x": 23.03, "y": -21.53, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 18.2, "y": -33.89, "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 3.6667, "x": 34.66, "y": -31.78, "curve": 0.25, "c4": 0.77}, {"time": 4, "x": 23.03, "y": -21.53, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuotui": {"translate": [{"time": 0.8333, "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 1.1667, "x": 41.29, "y": -5.63, "curve": 0.346, "c4": 0.76}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 3.5, "x": 41.29, "y": -5.63, "curve": 0.346, "c4": 0.76}, {"time": 4}]}, "shent1": {"rotate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 2.21, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.5, "angle": -2.39, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 2.21, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.8333, "angle": -2.39, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 3.1667}], "translate": [{"time": 0.8333, "curve": 0, "c2": 0.23, "c3": 0.75}, {"time": 1.1667, "x": 58, "y": 0.38, "curve": 0.25, "c4": 0.8}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0, "c2": 0.23, "c3": 0.75}, {"time": 3.5, "x": 58, "y": 0.38, "curve": 0.25, "c4": 0.8}, {"time": 4}], "scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.16, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "guaishou1": {"translate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -18.47, "y": 7.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 70, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": -18.47, "y": 7.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 70, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "guaishou2": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": -12.79, "y": 4.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 90, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -12.79, "y": 4.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 90, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "dangbu8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 21.46, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 29.08, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 21.46, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 29.08, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.023, "y": 1.023}]}, "dangbu14": {"rotate": [{"angle": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.35, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 11.35, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.34}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 1, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.006, "y": 1.006}]}, "dangbu13": {"rotate": [{"angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.58, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 19.08, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 9.58, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 19.08, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 3.37}], "scale": [{"x": 1.015, "y": 1.015, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.8333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.015, "y": 1.015, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3.1667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.015, "y": 1.015}]}, "dangbu12": {"rotate": [{"angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 23.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -13.47, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 23.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -13.47, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 5.4}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.6667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.023, "y": 1.023}]}, "dangbu11": {"rotate": [{"angle": 4.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 26.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.6, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 21.02, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.05, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 26.22, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -13.6, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 21.02, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.05}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.5, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.8333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.023, "y": 1.023}]}, "dangbu10": {"rotate": [{"angle": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 21.45, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.01, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 21.45, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 16.01, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 2.7}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.6667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.023, "y": 1.023}]}, "dangbu9": {"rotate": [{"angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 19.22, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.44, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 19.22, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 13.44, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.35}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.1667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.5, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 1.023, "y": 1.023}]}, "dangbu7": {"rotate": [{"angle": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.35, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 11.35, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.34}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 1, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.006, "y": 1.006}]}, "dangbu6": {"rotate": [{"angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.58, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 19.08, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 9.58, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 19.08, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 3.37}], "scale": [{"x": 1.015, "y": 1.015, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.8333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.015, "y": 1.015, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3.1667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.015, "y": 1.015}]}, "dangbu5": {"rotate": [{"angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 23.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -13.47, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 23.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -13.47, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 5.4}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.6667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.023, "y": 1.023}]}, "dangbu4": {"rotate": [{"angle": 4.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 26.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.6, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 21.02, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.05, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 26.22, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -13.6, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 21.02, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.05}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.5, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.8333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.023, "y": 1.023}]}, "dangbu3": {"rotate": [{"angle": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 21.45, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.01, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 21.45, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 16.01, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 2.7}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.6667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.023, "y": 1.023}]}, "dangbu2": {"rotate": [{"angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 19.22, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.44, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 19.22, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 13.44, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.35}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.1667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.5, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 1.023, "y": 1.023}]}, "dangbu": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 21.46, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 29.08, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 21.46, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 29.08, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.023, "y": 1.023}]}, "tou": {"rotate": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -13.76, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": "stepped"}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -13.76, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 4}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "heishanlaoyao": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 10.55, "y": 16.99, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 8.49, "y": 13.56, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": 0.59, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 8.17, "y": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "x": 0.59, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": 8.17, "y": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 3.7333}], "scale": [{"x": 0.9}]}, "heishanlaoyao2": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"x": 0.8, "curve": "stepped"}, {"time": 0.3333, "x": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 0.8}]}, "zuodabi8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youshou6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youshou7": {"rotate": [{"angle": -4.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -17.75, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -15.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.3333, "angle": -4.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -17.75, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -15.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "angle": -4.28}]}, "youshou8": {"rotate": [{"angle": -8.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12.12, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -12.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.3333, "angle": -8.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 12.12, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -12.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.6667, "angle": -8.84}]}, "youshou9": {"rotate": [{"angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -14.43, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.15, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -14.43, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 13.15, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -17.71}]}, "youshou10": {"rotate": [{"angle": -8.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -16.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.3333, "angle": -8.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "angle": -16.69, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "angle": -8.26}]}, "youshou15": {"rotate": [{"angle": -8.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -16.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.3333, "angle": -8.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "angle": -16.69, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "angle": -8.26}]}, "youshou14": {"rotate": [{"angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -14.43, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.15, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -14.43, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 13.15, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -17.71}]}, "youshou13": {"rotate": [{"angle": -8.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12.12, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -12.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.3333, "angle": -8.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 12.12, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -12.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.6667, "angle": -8.84}]}, "youshou12": {"rotate": [{"angle": -4.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -17.75, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -15.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.3333, "angle": -4.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -17.75, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -15.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "angle": -4.28}]}, "youshou11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "yijin5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -17.39, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -17.39, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "yijin": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -17.39, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -17.39, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxuiaotui7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxuiaotui9": {"rotate": [{"angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 0.11}]}, "youxuiaotui8": {"rotate": [{"angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 0.06}]}, "youxuiaotui5": {"rotate": [{"angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 0.11}]}, "youxuiaotui4": {"rotate": [{"angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 0.06}]}, "youxuiaotui3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youshou": {"rotate": [{"angle": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -94.96, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -94.96, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.08}], "translate": [{"x": 43.21, "y": -46.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 123.88, "y": -70.82, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 43.21, "y": -46.84, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 123.88, "y": -70.82, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 43.21, "y": -46.84}]}, "zuoshou2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 27.53, "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 1.2333, "angle": 17.51, "curve": 0.309, "c2": 0.24, "c3": 0.647, "c4": 0.59}, {"time": 1.6667, "angle": 22.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 27.53, "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 3.5667, "angle": 17.51, "curve": 0.309, "c2": 0.24, "c3": 0.647, "c4": 0.59}, {"time": 4, "angle": 22.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667}]}, "heishanlaoyao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -30.89, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "heishanlaoyao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -12.83, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuoshou": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": 137.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "angle": 137.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 53.23}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -12.33, "y": -24.65, "curve": 0, "c2": 0.21, "c3": 0.75}, {"time": 1.1667, "x": 47.74, "y": 49.6, "curve": 0.25, "c4": 0.72}, {"time": 1.6667, "x": 62.77, "y": 29.56, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -12.33, "y": -24.65, "curve": 0, "c2": 0.21, "c3": 0.75}, {"time": 3.5, "x": 47.74, "y": 49.6, "curve": 0.25, "c4": 0.72}, {"time": 4, "x": 62.77, "y": 29.56, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "tou2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.22, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -0.22, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "yingzi": {"scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "youjiao2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.95, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.89, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -9.95, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -5.89, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -7.37, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "zuojiao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 12.35, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.19, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 12.35, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.19, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "jianxia": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 6.97, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.28, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 6.97, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.28, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxiaobi8": {"rotate": [{"angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.52}]}, "youxiaobi7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxiaobi6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxiaobi5": {"rotate": [{"angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.52}]}, "youxiaobi4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxiaobi9": {"rotate": [{"time": 0.8333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.73, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.73}]}, "beijing4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 18.48, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "shu5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 45.08, "y": 66.76, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 15.95, "y": -26.1, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -21.85, "y": -13.18, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -18.73, "y": -8.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "shu2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 67.54, "y": 53.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -46.93, "y": 7.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -54.14, "y": -61.98, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -51.96, "y": -32.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "shu1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -47.08, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 84.14, "y": 41.47, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 26.62, "y": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -86.03, "y": -97.17, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -64.35, "y": -17.77, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "shu3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.44, "y": 14.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.3333, "x": 10.89, "y": 3.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.6667}]}, "shu4": {"translate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 1, "x": 10.6, "y": 5.78, "curve": 0.308, "c2": 0.25, "c3": 0.654, "c4": 0.63}, {"time": 2.3333, "x": -1.46, "y": -7.67, "curve": 0.34, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 3.3333, "x": 8.99, "y": 19.7, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.6667}]}, "shu6": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 48.55, "y": 19.07, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -30.23, "y": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": -73.58, "y": -42.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -127.97, "y": -27.92, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -36.24, "y": -10.49, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "beijing25": {"rotate": [{"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 54.54, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 64.64, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "beijing24": {"rotate": [{"angle": 27.6, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 46.86, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 59.84, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -8.34, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 27.6}]}, "beijing5": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "texiao4": {"translate": [{"x": -33.74, "y": -242.45, "curve": "stepped"}, {"time": 0.8333, "x": -33.74, "y": -242.45, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 116.27, "y": -184.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 130.29, "y": -108.93, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 114.29, "y": -53.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 128.3, "y": -11.82, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 116.31, "y": 37.74, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -33.74, "y": -242.45, "curve": "stepped"}, {"time": 3.1667, "x": -33.74, "y": -242.45, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 116.27, "y": -184.48, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 130.29, "y": -108.93, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 114.29, "y": -53.37, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 128.3, "y": -11.82, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 116.31, "y": 37.74, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 116.33, "y": 131.16}], "scale": [{"x": 2, "y": 1.5, "curve": "stepped"}, {"time": 1.3333, "x": 2, "y": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.1, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 2, "y": 1.5, "curve": "stepped"}, {"time": 3.6667, "x": 2, "y": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.1, "y": 1.1}]}, "beijing11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -24.14, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -25.34, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "beijing6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 49.06, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 49.06, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "meimao2": {"rotate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -6.03, "curve": 0.25, "c3": 0.75}, {"time": 3.6}], "translate": [{"time": 0.4333, "curve": 0, "c2": 0.37, "c3": 0.75}, {"time": 0.6, "x": 4.66, "y": -2.33, "curve": 0.25, "c4": 0.59}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.7667, "curve": 0, "c2": 0.45, "c3": 0.75}, {"time": 2.9333, "x": 4.66, "y": -2.33, "curve": 0.25, "c4": 0.66}, {"time": 3.1667}], "scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 1.082, "y": 1.082, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.002, "y": 1.002, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 1.082, "y": 1.082, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 1.002, "y": 1.002, "curve": 0.25, "c3": 0.75}, {"time": 3.6}]}, "meimao1": {"rotate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 4.31, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 4.31, "curve": 0.25, "c3": 0.75}, {"time": 3.6}], "translate": [{"time": 0.4333, "curve": 0, "c2": 0.34, "c3": 0.75}, {"time": 0.6, "x": 4.66, "y": -2.33, "curve": 0.381, "c4": 0.46}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.7667, "curve": 0, "c2": 0.41, "c3": 0.75}, {"time": 2.9333, "x": 4.66, "y": -2.33, "curve": 0.47, "c4": 0.62}, {"time": 3.1667}], "scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 1.082, "y": 1.082, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.002, "y": 1.002, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 1.082, "y": 1.082, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 1.002, "y": 1.002, "curve": 0.25, "c3": 0.75}, {"time": 3.6}]}, "huzi2": {"rotate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -4.86, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": "stepped"}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -4.86, "curve": 0.25, "c3": 0.75}, {"time": 3.8333}], "scale": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": "stepped"}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.8333}]}, "huzi1": {"rotate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 12.47, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.66, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": "stepped"}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 12.47, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 3.66, "curve": 0.25, "c3": 0.75}, {"time": 4.1667}], "scale": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": "stepped"}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667}]}, "xuanzhuan2": {"rotate": [{}, {"time": 1.1667, "angle": 90, "curve": 0, "c2": 0.26, "c3": 0.75}, {"time": 2.3333, "angle": 180}, {"time": 3.5, "angle": -90, "curve": 0, "c2": 0.34, "c3": 0.75}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.5, "y": 0.74, "curve": 0.25, "c3": 0.75}, {"time": 2.3333}]}, "wu1": {"scale": [{}, {"time": 1.4333, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 1.7667, "x": 1.3, "y": 1.2}, {"time": 1.8, "curve": "stepped"}, {"time": 2.3333}, {"time": 3.7667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 4.1, "x": 1.3, "y": 1.2}, {"time": 4.1333}]}, "wu3": {"scale": [{"x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.1667, "x": 1.3, "y": 1.2}, {"time": 0.2, "curve": "stepped"}, {"time": 0.7333}, {"time": 2.1667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 2.5, "x": 1.3, "y": 1.2}, {"time": 2.5333, "curve": "stepped"}, {"time": 3.0667}, {"time": 4.2333, "x": 1.163, "y": 1.163}, {"time": 4.5, "x": 1.3, "y": 1.2}]}, "wu4": {"scale": [{"x": 1.121, "y": 1.121}, {"time": 0.3, "x": 1.163, "y": 1.163}, {"time": 0.5667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.9, "x": 1.3, "y": 1.2}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.4667}, {"time": 2.9, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 3.2333, "x": 1.3, "y": 1.2}, {"time": 3.2667, "curve": "stepped"}, {"time": 3.8}, {"time": 4.6667, "x": 1.121, "y": 1.121}]}, "wu2": {"scale": [{}, {"time": 1.4333, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 1.7667, "x": 1.3, "y": 1.2}, {"time": 1.8, "curve": "stepped"}, {"time": 2.3333}, {"time": 3.7667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 4.1, "x": 1.3, "y": 1.2}, {"time": 4.1333}]}, "wu5": {"scale": [{"x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.1667, "x": 1.3, "y": 1.2}, {"time": 0.2, "curve": "stepped"}, {"time": 0.7333}, {"time": 2.1667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 2.5, "x": 1.3, "y": 1.2}, {"time": 2.5333, "curve": "stepped"}, {"time": 3.0667}, {"time": 4.5, "x": 1.3, "y": 1.2}]}, "wu6": {"scale": [{"x": 1.181, "y": 1.121}, {"time": 0.5667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.9, "x": 1.3, "y": 1.2}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.4667}, {"time": 2.9, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 3.2333, "x": 1.3, "y": 1.2}, {"time": 3.2667, "curve": "stepped"}, {"time": 3.8}, {"time": 4.6667, "x": 1.181, "y": 1.121}]}, "texiao": {"scale": [{"x": 1.14, "y": 1.093}, {"time": 0.3667, "x": 1.216, "y": 1.144}, {"time": 0.7667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 1.1, "x": 1.3, "y": 1.2}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}, {"time": 3.1, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 3.4333, "x": 1.3, "y": 1.2}, {"time": 3.4667, "curve": "stepped"}, {"time": 4}, {"time": 4.6667, "x": 1.14, "y": 1.093}]}, "texiao5": {"scale": [{"time": 0.0667}, {"time": 0.3667, "x": 1.063, "y": 1.042}, {"time": 1.5, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 1.8333, "x": 1.3, "y": 1.2}, {"time": 1.8667, "curve": "stepped"}, {"time": 2.4}, {"time": 3.8333, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 4.1667, "x": 1.3, "y": 1.2}, {"time": 4.2}]}, "texiao8": {"scale": [{"x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.2333, "x": 1.3, "y": 1.2}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.8}, {"time": 1.6667, "x": 1.181, "y": 1.121}, {"time": 2.2333, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 2.5667, "x": 1.3, "y": 1.2}, {"time": 2.6, "curve": "stepped"}, {"time": 3.1333}, {"time": 4.5667, "x": 1.3, "y": 1.2}]}, "yachi": {"translate": [{"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -13.03, "y": -5.13, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -24.19, "y": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -20.47, "y": -8.06, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "yanjing3": {"scale": [{"time": 1.3333}, {"time": 1.6667, "x": 1.22, "y": 1.22}, {"time": 2.6667}]}, "yanjing4": {"scale": [{"time": 1.3333}, {"time": 1.6667, "x": 1.22, "y": 1.22}, {"time": 2.6667}]}}, "deform": {"default": {"dangbu": {"dangbu": [{"offset": 46, "vertices": [-3.38246, -2.59709, -3.47754, -2.29809, -3.5948, -1.91624, -3.6468, -1.67616, -3.66159, -1.57991, -1.58336, -4.06671, -0.97386, -4.15121, -0.38135, -4.15109, -0.3916, -3.99435], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 5.86571, 10.62106, 6.69887, 10.02138, 8.48859, 8.16731, 9.41895, 6.6179, 9.49988, 6.0287, -2.12436, 11.6946, -2.18224, 11.94235, -3.62978, 11.49791, -1.79955, 11.63986, -0.96014, 11.4705, 3.39001, 16.27112, 4.71777, 15.81795, 7.80771, 14.11613, 9.65277, 12.46458, -7.67967, 14.74089, -9.39272, 13.57401, -7.05727, 14.50545, -5.88887, 14.62402, -6.56766, 13.93797, 0.0775, 1.80768, 0.45149, 1.70964, 0.69178, 1.58353, -1.21123, 1.36102, -1.36206, 1.19109, -1.13892, 1.35246, -1.01794, 1.39635, -1.07172, 1.30547], "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 4.36351, 4.78389, 4.21781, 4.46695, 4.85728, 3.4057, 5.15772, 2.60684, 5.22487, 2.22934, 0.68494, 6.62373, 0.35207, 6.47437, -0.84784, 6.08749, 0.00658, 5.93079, 0.35179, 5.76722, 3.39001, 16.27112, 4.71777, 15.81795, 7.80771, 14.11613, 9.65277, 12.46458, -7.67967, 14.74089, -9.39272, 13.57401, -7.05727, 14.50545, -5.88887, 14.62402, -6.56766, 13.93797, 0.0775, 1.80768, 0.45149, 1.70964, 0.69178, 1.58353, -1.21123, 1.36102, -1.36206, 1.19109, -1.13892, 1.35246, -1.01794, 1.39635, -1.07172, 1.30547], "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 2.43216, -4.50378, -1.95159, -3.56821, -4.15638, -2.15786, -5.07113, -0.71372, -4.96673, -0.79142, 5.37448, -1.04659, 4.739, -1.93668, 1.5648, -3.75116, -0.62553, -4.64191, -1.94366, -4.73933, 12.00905, 0.54741, 5.70497, -3.3046, 1.6743, -4.94234, -0.97934, -4.86047, 8.94819, 8.0298, 6.14925, 2.38705, 5.00951, -1.4706, 3.70384, -3.29906, 4.27962, -2.97487, -10.04523, -4.67726, -11.85042, -0.87054, -12.06305, 1.99654, 0.29841, -9.79456, -2.63066, -10.76292, -6.10182, -10.19368, -7.81078, -9.40671, -6.92351, -9.86836], "curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 28, "vertices": [-21.10349, -19.99986, -23.08531, -17.73276, -24.13258, -13.11888, -22.54674, -9.79493, -22.46397, -8.75478, -3.06308, -28.22568, -3.66327, -28.83625, -0.59842, -29.10129, -2.92412, -24.40848, -15.37457, -8.1398, -16.47507, -5.07919, -15.29947, -6.80289, -11.34256, -11.29792, -12.06123, -11.0075, -5.39182, -16.8047, -6.74393, -16.03628, -6.33934, -16.03303, 4.02875, -15.49318, 10.92833, -18.32014, 3.6106, -18.53137, 3.4234, -16.11474, 7.17746, -10.87589, 7.02275, -11.7443, 21.48434, -5.03632, 20.06396, -7.24927, 16.71951, -8.7686, 15.15151, -6.46887, 13.01112, 0.70644, 12.00905, 0.54741, 5.70497, -3.3046, 1.6743, -4.94234, -0.97934, -4.86047, 8.94819, 8.0298, 6.14925, 2.38705, 5.00951, -1.4706, 3.70384, -3.29906, 4.27962, -2.97487, 3.10603, 0.44391, 0.38396, 6.02051, -3.24724, 12.96261, 4.78492, 3.82495, 1.5972, 2.70139, -4.7022, 3.77745, -12.82842, 3.73938, -12.79932, 3.23074], "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "offset": 46, "vertices": [-3.38246, -2.59709, -3.47754, -2.29809, -3.5948, -1.91624, -3.6468, -1.67616, -3.66159, -1.57991, -1.58336, -4.06671, -0.97386, -4.15121, -0.38135, -4.15109, -0.3916, -3.99435], "curve": 0.25, "c3": 0.75}, {"time": 3, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 5.86571, 10.62106, 6.69887, 10.02138, 8.48859, 8.16731, 9.41895, 6.6179, 9.49988, 6.0287, -2.12436, 11.6946, -2.18224, 11.94235, -3.62978, 11.49791, -1.79955, 11.63986, -0.96014, 11.4705, 3.39001, 16.27112, 4.71777, 15.81795, 7.80771, 14.11613, 9.65277, 12.46458, -7.67967, 14.74089, -9.39272, 13.57401, -7.05727, 14.50545, -5.88887, 14.62402, -6.56766, 13.93797, 0.0775, 1.80768, 0.45149, 1.70964, 0.69178, 1.58353, -1.21123, 1.36102, -1.36206, 1.19109, -1.13892, 1.35246, -1.01794, 1.39635, -1.07172, 1.30547], "curve": 0.25, "c3": 0.75}, {"time": 3.1, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 4.36351, 4.78389, 4.21781, 4.46695, 4.85728, 3.4057, 5.15772, 2.60684, 5.22487, 2.22934, 0.68494, 6.62373, 0.35207, 6.47437, -0.84784, 6.08749, 0.00658, 5.93079, 0.35179, 5.76722, 3.39001, 16.27112, 4.71777, 15.81795, 7.80771, 14.11613, 9.65277, 12.46458, -7.67967, 14.74089, -9.39272, 13.57401, -7.05727, 14.50545, -5.88887, 14.62402, -6.56766, 13.93797, 0.0775, 1.80768, 0.45149, 1.70964, 0.69178, 1.58353, -1.21123, 1.36102, -1.36206, 1.19109, -1.13892, 1.35246, -1.01794, 1.39635, -1.07172, 1.30547], "curve": 0.25, "c3": 0.75}, {"time": 3.5, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 2.43216, -4.50378, -1.95159, -3.56821, -4.15638, -2.15786, -5.07113, -0.71372, -4.96673, -0.79142, 5.37448, -1.04659, 4.739, -1.93668, 1.5648, -3.75116, -0.62553, -4.64191, -1.94366, -4.73933, 12.00905, 0.54741, 5.70497, -3.3046, 1.6743, -4.94234, -0.97934, -4.86047, 8.94819, 8.0298, 6.14925, 2.38705, 5.00951, -1.4706, 3.70384, -3.29906, 4.27962, -2.97487, -10.04523, -4.67726, -11.85042, -0.87054, -12.06305, 1.99654, 0.29841, -9.79456, -2.63066, -10.76292, -6.10182, -10.19368, -7.81078, -9.40671, -6.92351, -9.86836], "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "offset": 28, "vertices": [-21.10349, -19.99986, -23.08531, -17.73276, -24.13258, -13.11888, -22.54674, -9.79493, -22.46397, -8.75478, -3.06308, -28.22568, -3.66327, -28.83625, -0.59842, -29.10129, -2.92412, -24.40848, -15.37457, -8.1398, -16.47507, -5.07919, -15.29947, -6.80289, -11.34256, -11.29792, -12.06123, -11.0075, -5.39182, -16.8047, -6.74393, -16.03628, -6.33934, -16.03303, 4.02875, -15.49318, 10.92833, -18.32014, 3.6106, -18.53137, 3.4234, -16.11474, 7.17746, -10.87589, 7.02275, -11.7443, 21.48434, -5.03632, 20.06396, -7.24927, 16.71951, -8.7686, 15.15151, -6.46887, 13.01112, 0.70644, 12.00905, 0.54741, 5.70497, -3.3046, 1.6743, -4.94234, -0.97934, -4.86047, 8.94819, 8.0298, 6.14925, 2.38705, 5.00951, -1.4706, 3.70384, -3.29906, 4.27962, -2.97487, 3.10603, 0.44391, 0.38396, 6.02051, -3.24724, 12.96261, 4.78492, 3.82495, 1.5972, 2.70139, -4.7022, 3.77745, -12.82842, 3.73938, -12.79932, 3.23074], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "offset": 46, "vertices": [-3.38246, -2.59709, -3.47754, -2.29809, -3.5948, -1.91624, -3.6468, -1.67616, -3.66159, -1.57991, -1.58336, -4.06671, -0.97386, -4.15121, -0.38135, -4.15109, -0.3916, -3.99435]}]}, "heishanlaoyao": {"heishanlaoyao": [{"offset": 264, "vertices": [1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}, {"time": 1.1667, "offset": 6, "vertices": [37.01584, -5.19891, 17.56363, -30.8661, -7.83007, -39.02874, -29.07426, -19.08594, -30.20103, 12.94344, -20.83209, 40.56923, -15.16461, 45.63982, 11.66888, 15.48314, 14.87523, 11.66552, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63.72848, -0.90401, 80.21976, 4.01882, 87.30679, -4.92717, 86.85941, -21.81368, 82.88434, -35.2578, 69.74225, -50.65623, 58.44325, -36.06524, 52.7627, -31.8332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}, {"time": 2.3333, "offset": 264, "vertices": [1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}, {"time": 3.5, "offset": 6, "vertices": [37.01584, -5.19891, 17.56363, -30.8661, -7.83007, -39.02874, -29.07426, -19.08594, -30.20103, 12.94344, -20.83209, 40.56923, -15.16461, 45.63982, 11.66888, 15.48314, 14.87523, 11.66552, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63.72848, -0.90401, 80.21976, 4.01882, 87.30679, -4.92717, 86.85941, -21.81368, 82.88434, -35.2578, 69.74225, -50.65623, 58.44325, -36.06524, 52.7627, -31.8332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}, {"time": 4.6667, "offset": 264, "vertices": [1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}]}, "yanjing2": {"yanjing2": [{"offset": 26, "vertices": [0.23975, -0.21631], "curve": "stepped"}, {"time": 0.4, "offset": 26, "vertices": [0.23975, -0.21631], "curve": 0, "c2": 0.16, "c3": 0.75}, {"time": 0.6, "vertices": [0.32556, -1.23749, 3.18701, -0.96603, 4.15332, -2.11094, 1.34863, -2.07011, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23975, -0.21631, 1.62756], "curve": 0.25, "c4": 0.64}, {"time": 0.8333, "vertices": [-2.9502, 2.04332, -1.03461, 0.80608, -0.11121, 0.91625, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23975, -0.21631]}, {"time": 2.3333, "offset": 26, "vertices": [0.23975, -0.21631], "curve": "stepped"}, {"time": 2.7333, "offset": 26, "vertices": [0.23975, -0.21631], "curve": 0, "c2": 0.16, "c3": 0.75}, {"time": 2.9333, "vertices": [0.32556, -1.23749, 3.18701, -0.96603, 4.15332, -2.11094, 1.34863, -2.07011, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23975, -0.21631, 1.62756], "curve": 0.25, "c4": 0.64}, {"time": 3.1667, "vertices": [-2.9502, 2.04332, -1.03461, 0.80608, -0.11121, 0.91625, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23975, -0.21631]}, {"time": 4.6667, "offset": 26, "vertices": [0.23975, -0.21631]}]}, "youdatui2": {"youdatui2": [{"offset": 18, "vertices": [1.93533, -3.84293]}, {"time": 0.6667, "offset": 14, "vertices": [9.26516, -3.61014, 16.27471, 0.93989, 10.17694, -19.98319, 11.7945, -15.01548]}, {"time": 0.8333, "offset": 14, "vertices": [11.58145, -4.51267, 20.34338, 1.17486, 8.62848, -13.12063, 6.76694, -10.98196]}, {"time": 1.3333, "offset": 18, "vertices": [3.44644, -10.59183, 8.10861, -10.55568]}, {"time": 1.6667, "offset": 14, "vertices": [7.60828, -0.60697, 16.50376, -2.73043, 8.67138, -20.17707, 8.12164, -13.58149]}, {"time": 2, "offset": 14, "vertices": [15.21657, -1.21394, 26.95015, -4.54137, 16.9595, -9.57256, 6.48489, -7.06223]}, {"time": 2.3333, "offset": 18, "vertices": [1.93533, -3.84293]}, {"time": 3, "offset": 14, "vertices": [9.26516, -3.61014, 16.27471, 0.93989, 10.17694, -19.98319, 11.7945, -15.01548]}, {"time": 3.1667, "offset": 14, "vertices": [11.58145, -4.51267, 20.34338, 1.17486, 8.62848, -13.12063, 6.76694, -10.98196]}, {"time": 3.6667, "offset": 18, "vertices": [3.44644, -10.59183, 8.10861, -10.55568]}, {"time": 4, "offset": 14, "vertices": [7.60828, -0.60697, 16.50376, -2.73043, 8.67138, -20.17707, 8.12164, -13.58149]}, {"time": 4.3333, "offset": 14, "vertices": [15.21657, -1.21394, 26.95015, -4.54137, 16.9595, -9.57256, 6.48489, -7.06223]}, {"time": 4.6667, "offset": 18, "vertices": [1.93533, -3.84293]}]}, "xuanzhuan1": {"xuanzhuan1": [{"offset": 10, "vertices": [0.7312, 1.08154, 0.87042, -1.70563]}, {"time": 0.4, "vertices": [-13.39819, -13.72544, -15.87707, -13.45438, -16.25982, -7.17588, -13.14601, -0.01043, -14.68376, 4.93367, -16.24631, 11.43638, -12.06781, 9.57838, -11.83101, 11.6961, -10.49418, 16.72785, 0.57764, 19.06791, 10.88095, 19.98938, 14.9584, 17.55696, 14.06435, 15.51774, 14.35611, 5.35254, 16.23355, -5.035, 23.31629, -10.03007, 21.83068, -16.78044, 20.15303, -16.89948, 16.63615, -18.52275, 7.17014, -17.20588, -2.27093, -18.16859, -9.6987, -13.05803]}, {"time": 0.8333, "vertices": [-27.9129, -28.59467, -34.17828, -17.79126, -37.6073, -1.93861, -31.70996, 9.88752, -32.97687, 16.38387, -34.63861, 22.65412, -26.08423, 21.80272, -17.04791, 26.74503, -2.76129, 37.97768, 10.75427, 41.28883, 22.66864, 41.64454, 31.16333, 36.57699, 33.27832, 22.65052, 40.61108, -2.24846, 41.49036, -19.10395, 50.14459, -27.75562, 45.48059, -34.95924, 35.55695, -32.87715, 19.61896, -33.45871, 1.26031, -34.63068, -10.6889, -33.33871, -20.20563, -27.20423]}, {"time": 1.3333, "vertices": [-48.45125, -24.94424, -50.8042, -7.20292, -51.98012, 8.97669, -50.8783, 23.86078, -47.13438, 31.19124, -41.95329, 40.24373, -22.88811, 40.92004, -7.95757, 43.13138, 7.23991, 53.13043, 24.35532, 49.1041, 37.75713, 38.50808, 49.70175, 24.30758, 52.6292, 7.88161, 54.60086, -15.44605, 53.51272, -32.88342, 56.56767, -42.27536, 47.5064, -51.18335, 31.49622, -50.83792, 10.61891, -47.44164, -11.01217, -48.84343, -27.17641, -45.88372, -38.72342, -33.45285]}, {"time": 1.8, "vertices": [-70.33557, -9.03977, -67.27234, 4.7113, -64.43115, 19.90658, -53.30231, 35.76167, -44.6665, 44.18957, -34.92932, 52.43295, -13.52368, 54.46387, 4.56427, 54.77796, 19.70447, 52.63911, 43.38464, 42.38917, 57.34082, 25.84393, 67.00427, 12.85613, 70.69, -5.9027, 66.16644, -27.22312, 60.57782, -44.33759, 57.9776, -55.0589, 43.21661, -64.48256, 18.75781, -64.35785, -6.40973, -57.46369, -28.49683, -51.08958, -50.47107, -36.96075, -64.66113, -19.07675]}, {"time": 2.2333, "vertices": [-79.70609, -0.71279, -75.49484, 15.45541, -66.7811, 34.33334, -50.13989, 50.46962, -39.3096, 59.09302, -25.79282, 65.6636, -1.49915, 63.1622, 16.97455, 57.99012, 32.62564, 48.66281, 56.65292, 32.04926, 70.14432, 13.94578, 78.80569, 0.72525, 73.71265, -18.85919, 63.13666, -43.42595, 54.5289, -60.84639, 47.12042, -69.61516, 29.45468, -78.58795, 4.45776, -70.374, -20.50885, -57.61506, -42.38077, -45.85937, -61.2876, -31.10726, -71.92096, -12.66581]}, {"time": 2.6667, "vertices": [-78.80939, 7.89623, -69.67719, 30.65815, -56.42535, 51.2383, -38.23944, 68.88882, -24.23798, 74.17062, -8.61285, 75.87523, 16.10889, 62.34037, 34.29462, 49.25644, 47.72858, 39.3774, 69.9212, 21.70936, 82.94781, 2.04762, 80.31903, -15.99326, 71.09564, -33.77609, 57.46588, -57.85042, 46.16931, -75.79881, 36.26324, -84.17142, 15.84143, -84.63602, -8.599, -69.55333, -34.36713, -53.13249, -56.14484, -38.31219, -69.6731, -24.49295, -79.18079, -6.25486]}, {"time": 3.1667, "vertices": [-62.73257, 38.18067, -48.7435, 54.23508, -32.90378, 64.24633, -13.42453, 67.2901, 1.37256, 63.75833, 15.68701, 60.20965, 38.35971, 45.46159, 55.91882, 31.38833, 66.66101, 15.90009, 72.51831, 1.11702, 72.37729, -18.00052, 66.82455, -32.81509, 54.12476, -47.75425, 33.01437, -64.30235, 18.39139, -72.32404, 10.22754, -69.45779, -15.96402, -62.58065, -37.27115, -45.19556, -59.73611, -23.49065, -69.68637, -8.88144, -74.55627, 7.17407, -68.25537, 27.31042]}, {"time": 3.6667, "vertices": [-45.00708, 43.84067, -29.82117, 49.89318, -9.39221, 52.30645, 10.01428, 55.10042, 25.31146, 50.15242, 39.98688, 44.54407, 54.0816, 25.30238, 58.42926, 10.07841, 60.22772, -2.48938, 59.78168, -17.30528, 56.20837, -31.39948, 46.20264, -39.55788, 33.79926, -47.47716, 10.30164, -53.21175, -6.79913, -58.59576, -12.83051, -55.31039, -25.95673, -49.46033, -40.04742, -30.12346, -52.50623, -9.56918, -60.54639, 8.62938, -61.97241, 21.31622, -53.34412, 40.43484]}, {"time": 4.1667, "vertices": [-10.86878, 19.60206, 0.19409, 21.01176, 7.5387, 22.23171, 17.73017, 21.86008, 19.06199, 14.28313, 17.14096, 7.70542, 25.84516, -6.29705, 29.01194, -14.498, 25.52879, -13.75142, 23.08347, -12.70426, 18.27174, -17.47416, 11.73993, -18.12397, 1.12564, -21.51039, -10.03381, -28.13358, -12.01285, -29.97426, -7.88669, -23.78455, -13.59439, -17.81629, -18.48148, 1.51251, -23.69642, 13.0995, -28.05531, 16.25951, -25.87507, 17.58273, -19.26777, 21.94843]}, {"time": 4.6667, "offset": 10, "vertices": [0.7312, 1.08154, 0.87042, -1.70563]}]}, "yanjing1": {"yanjing1": [{"offset": 2, "vertices": [0.03082, -0.62324], "curve": "stepped"}, {"time": 0.4, "offset": 2, "vertices": [0.03082, -0.62324], "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 0.6, "vertices": [3.3197, -5.16576, 3.71802, -4.43129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.59601, -0.40235, 2.79376, -0.54199, 4.00891, -2.23525], "curve": 0.25, "c4": 0.71}, {"time": 0.8333, "vertices": [-1.31616, -0.98753, 1.65137, -2.26788, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.10095, -1.57452, -1.5116, -1.22902]}, {"time": 2.3333, "offset": 2, "vertices": [0.03082, -0.62324], "curve": "stepped"}, {"time": 2.7333, "offset": 2, "vertices": [0.03082, -0.62324], "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 2.9333, "vertices": [3.3197, -5.16576, 3.71802, -4.43129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.59601, -0.40235, 2.79376, -0.54199, 4.00891, -2.23525], "curve": 0.25, "c4": 0.71}, {"time": 3.1667, "vertices": [-1.31616, -0.98753, 1.65137, -2.26788, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.10095, -1.57452, -1.5116, -1.22902]}, {"time": 4.6667, "offset": 2, "vertices": [0.03082, -0.62324]}]}, "youxuiaotui2": {"youxuiaotui2": [{"offset": 102, "vertices": [0.48044, 1.33839, -0.23959, 1.40167], "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "offset": 98, "vertices": [2.6149, 9.21962, -1.76807, 9.41857, 5.9647, 20.01318, -3.66297, 20.57058, 2.90762, 10.25139, -1.96585, 10.47267], "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "offset": 102, "vertices": [0.48044, 1.33839, -0.23959, 1.40167], "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "offset": 98, "vertices": [2.6149, 9.21962, -1.76807, 9.41857, 5.9647, 20.01318, -3.66297, 20.57058, 2.90762, 10.25139, -1.96585, 10.47267], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "offset": 102, "vertices": [1.08673, 1.62439, 0.14774, 1.94873]}]}, "biyan": {"biyan": [{"time": 1.0333, "vertices": [8.04675, -4.54137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.48621, 4.76982, 22.99463, 6.18795, 41.70264, 2.78001, 46.18787, 2.89685, 46.12848, 5.11605, 43.73682, -0.37101, 25.87354, -5.87912, 14.6134, -3.75144, 0, 0, 0, 0, 13.42377, -3.27269, 30.20154, -1.92525, 41.44092, -1.23176, 46.4173, -9.20277, 39.92633, -18.25568, 40.85229, -22.74601, 32.3067, -25.59232, 22.17816, -21.95834, 16.11371, -11.59898], "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 15.80115, -3.8375, 24.98145, -5.02985, 18.96552, -13.43731, 22.93707, -4.67553, 13.22858, -3.31053, 6.02765, -2.2454, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, 16.69543, -6.40791, 24.46967, -14.7349, 19.73352, -19.16244, 18.19244, -21.17055, 12.28687, -19.00793, 3.28119, -14.57358, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.1, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 2.49835, -0.7139, 5.04016, -6.10699, 0.34418, -1.37444, 2.27356, -4.7381, -6.24304, -7.39289, -3.34064, -1.64785, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -3.8783, -12.40305, -4.10822, -18.31657, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 1.68793, -0.54974, -2.46759, -1.51248, -0.33173, -3.51298, -4.04962, 2.60317, -2.8844, -3.00537, -5.23846, -1.34132, -5.06134, -2.03774, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -6.41125, -13.59341, -5.89233, -17.92814, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": "stepped"}, {"time": 1.2333, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 1.68793, -0.54974, -2.46759, -1.51248, -0.33173, -3.51298, -4.04962, 2.60317, -2.8844, -3.00537, -5.23846, -1.34132, -5.06134, -2.03774, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -6.41125, -13.59341, -5.89233, -17.92814, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 2.49835, -0.7139, 5.04016, -6.10699, 0.34418, -1.37444, 2.27356, -4.7381, -6.24304, -7.39289, -3.34064, -1.64785, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -3.8783, -12.40305, -4.10822, -18.31657, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.3, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 15.80115, -3.8375, 24.98145, -5.02985, 18.96552, -13.43731, 22.93707, -4.67553, 13.22858, -3.31053, 6.02765, -2.2454, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, 16.69543, -6.40791, 24.46967, -14.7349, 19.73352, -19.16244, 18.19244, -21.17055, 12.28687, -19.00793, 3.28119, -14.57358, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [8.04675, -4.54137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.48621, 4.76982, 22.99463, 6.18795, 41.70264, 2.78001, 46.18787, 2.89685, 46.12848, 5.11605, 43.73682, -0.37101, 25.87354, -5.87912, 14.6134, -3.75144, 0, 0, 0, 0, 13.42377, -3.27269, 30.20154, -1.92525, 41.44092, -1.23176, 46.4173, -9.20277, 39.92633, -18.25568, 40.85229, -22.74601, 32.3067, -25.59232, 22.17816, -21.95834, 16.11371, -11.59898], "curve": "stepped"}, {"time": 3.3667, "vertices": [8.04675, -4.54137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.48621, 4.76982, 22.99463, 6.18795, 41.70264, 2.78001, 46.18787, 2.89685, 46.12848, 5.11605, 43.73682, -0.37101, 25.87354, -5.87912, 14.6134, -3.75144, 0, 0, 0, 0, 13.42377, -3.27269, 30.20154, -1.92525, 41.44092, -1.23176, 46.4173, -9.20277, 39.92633, -18.25568, 40.85229, -22.74601, 32.3067, -25.59232, 22.17816, -21.95834, 16.11371, -11.59898], "curve": 0.25, "c3": 0.75}, {"time": 3.4, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 15.80115, -3.8375, 24.98145, -5.02985, 18.96552, -13.43731, 22.93707, -4.67553, 13.22858, -3.31053, 6.02765, -2.2454, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, 16.69543, -6.40791, 24.46967, -14.7349, 19.73352, -19.16244, 18.19244, -21.17055, 12.28687, -19.00793, 3.28119, -14.57358, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 2.49835, -0.7139, 5.04016, -6.10699, 0.34418, -1.37444, 2.27356, -4.7381, -6.24304, -7.39289, -3.34064, -1.64785, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -3.8783, -12.40305, -4.10822, -18.31657, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 1.68793, -0.54974, -2.46759, -1.51248, -0.33173, -3.51298, -4.04962, 2.60317, -2.8844, -3.00537, -5.23846, -1.34132, -5.06134, -2.03774, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -6.41125, -13.59341, -5.89233, -17.92814, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": "stepped"}, {"time": 3.5667, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 1.68793, -0.54974, -2.46759, -1.51248, -0.33173, -3.51298, -4.04962, 2.60317, -2.8844, -3.00537, -5.23846, -1.34132, -5.06134, -2.03774, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -6.41125, -13.59341, -5.89233, -17.92814, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.6, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 2.49835, -0.7139, 5.04016, -6.10699, 0.34418, -1.37444, 2.27356, -4.7381, -6.24304, -7.39289, -3.34064, -1.64785, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -3.8783, -12.40305, -4.10822, -18.31657, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 15.80115, -3.8375, 24.98145, -5.02985, 18.96552, -13.43731, 22.93707, -4.67553, 13.22858, -3.31053, 6.02765, -2.2454, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, 16.69543, -6.40791, 24.46967, -14.7349, 19.73352, -19.16244, 18.19244, -21.17055, 12.28687, -19.00793, 3.28119, -14.57358, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "vertices": [8.04675, -4.54137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.48621, 4.76982, 22.99463, 6.18795, 41.70264, 2.78001, 46.18787, 2.89685, 46.12848, 5.11605, 43.73682, -0.37101, 25.87354, -5.87912, 14.6134, -3.75144, 0, 0, 0, 0, 13.42377, -3.27269, 30.20154, -1.92525, 41.44092, -1.23176, 46.4173, -9.20277, 39.92633, -18.25568, 40.85229, -22.74601, 32.3067, -25.59232, 22.17816, -21.95834, 16.11371, -11.59898]}]}}}, "drawOrder": [{"offsets": [{"slot": "<PERSON><PERSON><PERSON>", "offset": 2}, {"slot": "xuanzhuan2", "offset": -3}, {"slot": "beijing", "offset": -3}, {"slot": "biyan", "offset": -5}]}]}, "animation2": {"slots": {"yanjing3": {"color": [{"color": "ffffff39", "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "color": "ffffffc1", "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.6667, "color": "ffa5a5c7", "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "color": "ffccccb9", "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "color": "ffffff96", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd6", "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 4.6667, "color": "ffffff39"}], "attachment": [{"name": null}]}, "heishanlaoyao": {"color": [{"color": "ffffff39", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffffc7", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff96", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd6", "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 4.6667, "color": "ffffff39"}], "attachment": [{"name": null}]}, "wu6": {"color": [{"color": "ffffff9a"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.8, "color": "ffffff00"}, {"time": 4.6667, "color": "ffffff9a"}], "attachment": [{"name": null}]}, "texiao": {"color": [{"color": "ffffff76"}, {"time": 0.3667, "color": "ffffffb7"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffff"}, {"time": 3.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00"}, {"time": 4.6667, "color": "ffffff76"}], "attachment": [{"name": null}]}, "dengguang2": {"color": [{"color": "ffffffd0"}, {"time": 0.6667, "color": "ffffffb3"}, {"time": 1.2667, "color": "969595ff"}, {"time": 1.7667, "color": "ded6d6ff"}, {"time": 2.5, "color": "ded6d6b7"}, {"time": 3.1, "color": "676767ff"}, {"time": 3.7, "color": "ffffffb3"}, {"time": 4.6667, "color": "ffffffd1"}], "attachment": [{"name": null}]}, "wu4": {"color": [{"color": "ffffff9a"}, {"time": 0.3, "color": "ffffffcf"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.8, "color": "ffffff00"}, {"time": 4.6667, "color": "ffffff9a"}], "attachment": [{"name": null}]}, "texiao3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffff35"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff7f"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00"}, {"time": 3.8333, "color": "ffffffff"}, {"time": 4.1667, "color": "ffffff00"}], "attachment": [{"name": null}]}, "xuanzhuan1": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "color": "656077ff", "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "color": "656077ff", "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "color": "ffffffff"}], "attachment": [{"name": null}]}, "yachi": {"color": [{"color": "ffffff20", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff9a", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff56", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd6", "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 4.6667, "color": "ffffff20"}], "attachment": [{"name": null}]}, "wu3": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}, {"time": 4.2333, "color": "ffffffcf"}, {"time": 4.5, "color": "ffffffff"}, {"time": 4.6667, "color": "ffffff7f"}], "attachment": [{"name": null}]}, "xuanzhuan2": {"color": [{"color": "ffffffde", "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "color": "ffffff73", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "color": "ffffffdd", "curve": 0.25, "c3": 0.75}, {"time": 3.5, "color": "ffffff7c", "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "color": "ffffffdd"}], "attachment": [{"name": null}]}, "dengguang1": {"color": [{"color": "ffffffd0"}, {"time": 0.6667, "color": "ffffffb3"}, {"time": 1.2667, "color": "969595ff"}, {"time": 1.7667, "color": "ded6d6ff"}, {"time": 2.5, "color": "ded6d6b7"}, {"time": 3.1, "color": "676767ff"}, {"time": 3.7, "color": "ffffffb3"}, {"time": 4.6667, "color": "ffffffd1"}], "attachment": [{"name": null}]}, "wu1": {"color": [{"color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 3.7667, "color": "ffffffff"}, {"time": 4.1, "color": "ffffff00"}], "attachment": [{"name": null}]}, "wu5": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}, {"time": 4.5, "color": "ffffffff"}, {"time": 4.6667, "color": "ffffff7f"}], "attachment": [{"name": null}]}, "tiankogn": {"attachment": [{"name": null}]}, "texiao4": {"color": [{"color": "ffffffb2"}, {"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffff9a"}, {"time": 2.2333, "color": "ffffffff"}, {"time": 2.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1333, "color": "ffffff00"}, {"time": 4.5667, "color": "ffffffff"}, {"time": 4.6667, "color": "ffffffb2"}], "attachment": [{"name": null}]}, "texiao2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffd2", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd2", "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "color": "ffffff00"}]}, "shidenglong": {"attachment": [{"name": null}]}, "wu2": {"color": [{"color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 3.7667, "color": "ffffffff"}, {"time": 4.1, "color": "ffffff00"}], "attachment": [{"name": null}]}, "beijing": {"attachment": [{"name": null}]}, "yanjing4": {"color": [{"color": "ffffff39", "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "color": "ffffffc1", "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.6667, "color": "ffa5a5c7", "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "color": "ffccccb9", "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "color": "ffffff96", "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "color": "ffffffd6", "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 4.6667, "color": "ffffff39"}], "attachment": [{"name": null}]}}, "bones": {"shenti2": {"rotate": [{"curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.8333, "angle": 7.74, "curve": 0.329, "c2": 0.32, "c3": 0.682, "c4": 0.71}, {"time": 1.6667, "angle": -0.76, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.3333, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 3.1667, "angle": 7.74, "curve": 0.329, "c2": 0.32, "c3": 0.682, "c4": 0.71}, {"time": 4, "angle": -0.76, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 18.2, "y": -33.89, "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 1.3333, "x": 34.66, "y": -31.78, "curve": 0.25, "c4": 0.77}, {"time": 1.6667, "x": 23.03, "y": -21.53, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 18.2, "y": -33.89, "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 3.6667, "x": 34.66, "y": -31.78, "curve": 0.25, "c4": 0.77}, {"time": 4, "x": 23.03, "y": -21.53, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuotui": {"translate": [{"time": 0.8333, "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 1.1667, "x": 41.29, "y": -5.63, "curve": 0.346, "c4": 0.76}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 3.5, "x": 41.29, "y": -5.63, "curve": 0.346, "c4": 0.76}, {"time": 4}]}, "shent1": {"rotate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 2.21, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.5, "angle": -2.39, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 2.21, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.8333, "angle": -2.39, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 3.1667}], "translate": [{"time": 0.8333, "curve": 0, "c2": 0.23, "c3": 0.75}, {"time": 1.1667, "x": 58, "y": 0.38, "curve": 0.25, "c4": 0.8}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0, "c2": 0.23, "c3": 0.75}, {"time": 3.5, "x": 58, "y": 0.38, "curve": 0.25, "c4": 0.8}, {"time": 4}], "scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.16, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "guaishou1": {"translate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -18.47, "y": 7.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 70, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": -18.47, "y": 7.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 70, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "guaishou2": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": -12.79, "y": 4.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 90, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -12.79, "y": 4.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 90, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "dangbu8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 21.46, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 29.08, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 21.46, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 29.08, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.023, "y": 1.023}]}, "dangbu14": {"rotate": [{"angle": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.35, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 11.35, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.34}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 1, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.006, "y": 1.006}]}, "dangbu13": {"rotate": [{"angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.58, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 19.08, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 9.58, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 19.08, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 3.37}], "scale": [{"x": 1.015, "y": 1.015, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.8333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.015, "y": 1.015, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3.1667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.015, "y": 1.015}]}, "dangbu12": {"rotate": [{"angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 23.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -13.47, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 23.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -13.47, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 5.4}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.6667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.023, "y": 1.023}]}, "dangbu11": {"rotate": [{"angle": 4.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 26.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.6, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 21.02, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.05, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 26.22, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -13.6, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 21.02, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.05}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.5, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.8333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.023, "y": 1.023}]}, "dangbu10": {"rotate": [{"angle": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 21.45, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.01, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 21.45, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 16.01, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 2.7}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.6667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.023, "y": 1.023}]}, "dangbu9": {"rotate": [{"angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 19.22, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.44, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 19.22, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 13.44, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.35}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.1667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.5, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 1.023, "y": 1.023}]}, "dangbu7": {"rotate": [{"angle": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.35, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 11.35, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.34}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 1, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.006, "y": 1.006}]}, "dangbu6": {"rotate": [{"angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.58, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 19.08, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 9.58, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 19.08, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 3.37}], "scale": [{"x": 1.015, "y": 1.015, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.8333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.015, "y": 1.015, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3.1667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.015, "y": 1.015}]}, "dangbu5": {"rotate": [{"angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 23.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -13.47, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 23.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -13.47, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 5.4}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.6667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 3, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.023, "y": 1.023}]}, "dangbu4": {"rotate": [{"angle": 4.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 26.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.6, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 21.02, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.05, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 26.22, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -13.6, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 21.02, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.05}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.5, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.8333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.023, "y": 1.023}]}, "dangbu3": {"rotate": [{"angle": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 21.45, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.01, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 21.45, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 16.01, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 2.7}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.6667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.023, "y": 1.023}]}, "dangbu2": {"rotate": [{"angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 19.22, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.44, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 19.22, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 13.44, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.35}], "scale": [{"x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 0.1667, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.5, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 1.023, "y": 1.023}]}, "dangbu": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 21.46, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 29.08, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 21.46, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 29.08, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.023, "y": 1.023, "curve": "stepped"}, {"time": 2.3333, "x": 1.023, "y": 1.023, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.935, "y": 0.935, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.998, "y": 0.998, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.023, "y": 1.023}]}, "tou": {"rotate": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -13.76, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": "stepped"}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -13.76, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 4}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "heishanlaoyao": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 10.55, "y": 16.99, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 8.49, "y": 13.56, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": 0.59, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 8.17, "y": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "x": 0.59, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": 8.17, "y": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 3.7333}], "scale": [{"x": 0.9}]}, "heishanlaoyao2": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"x": 0.8, "curve": "stepped"}, {"time": 0.3333, "x": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 0.8}]}, "zuodabi8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuodabi3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.1667, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -0.89, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.5, "angle": 4.58, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youshou6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youshou7": {"rotate": [{"angle": -4.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -17.75, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -15.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.3333, "angle": -4.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -17.75, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -15.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "angle": -4.28}]}, "youshou8": {"rotate": [{"angle": -8.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12.12, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -12.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.3333, "angle": -8.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 12.12, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -12.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.6667, "angle": -8.84}]}, "youshou9": {"rotate": [{"angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -14.43, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.15, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -14.43, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 13.15, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -17.71}]}, "youshou10": {"rotate": [{"angle": -8.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -16.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.3333, "angle": -8.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "angle": -16.69, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "angle": -8.26}]}, "youshou15": {"rotate": [{"angle": -8.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -16.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.3333, "angle": -8.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "angle": -16.69, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "angle": -8.26}]}, "youshou14": {"rotate": [{"angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -14.43, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.15, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -14.43, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 13.15, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -17.71}]}, "youshou13": {"rotate": [{"angle": -8.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12.12, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -12.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.3333, "angle": -8.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 12.12, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -12.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.6667, "angle": -8.84}]}, "youshou12": {"rotate": [{"angle": -4.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -17.75, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -15.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.3333, "angle": -4.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -17.75, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -15.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "angle": -4.28}]}, "youshou11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "yijin5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -17.39, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -17.39, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "yijin": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -17.39, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -17.39, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxuiaotui7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxuiaotui9": {"rotate": [{"angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 0.11}]}, "youxuiaotui8": {"rotate": [{"angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 0.06}]}, "youxuiaotui5": {"rotate": [{"angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 0.11}]}, "youxuiaotui4": {"rotate": [{"angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 0.06}]}, "youxuiaotui3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youshou": {"rotate": [{"angle": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -94.96, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -94.96, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.08}], "translate": [{"x": 43.21, "y": -46.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 123.88, "y": -70.82, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 43.21, "y": -46.84, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 123.88, "y": -70.82, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 43.21, "y": -46.84}]}, "zuoshou2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 27.53, "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 1.2333, "angle": 17.51, "curve": 0.309, "c2": 0.24, "c3": 0.647, "c4": 0.59}, {"time": 1.6667, "angle": 22.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 27.53, "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 3.5667, "angle": 17.51, "curve": 0.309, "c2": 0.24, "c3": 0.647, "c4": 0.59}, {"time": 4, "angle": 22.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667}]}, "heishanlaoyao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -30.89, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "heishanlaoyao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -12.83, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "zuoshou": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": 137.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "angle": 137.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 53.23}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -12.33, "y": -24.65, "curve": 0, "c2": 0.21, "c3": 0.75}, {"time": 1.1667, "x": 47.74, "y": 49.6, "curve": 0.25, "c4": 0.72}, {"time": 1.6667, "x": 62.77, "y": 29.56, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -12.33, "y": -24.65, "curve": 0, "c2": 0.21, "c3": 0.75}, {"time": 3.5, "x": 47.74, "y": 49.6, "curve": 0.25, "c4": 0.72}, {"time": 4, "x": 62.77, "y": 29.56, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "tou2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.22, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -0.22, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "yingzi": {"scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "youjiao2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.95, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.89, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -9.95, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -5.89, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -7.37, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "zuojiao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 12.35, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.19, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 12.35, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.19, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "jianxia": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 6.97, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.28, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 6.97, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.28, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxiaobi8": {"rotate": [{"angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.52}]}, "youxiaobi7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxiaobi6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxiaobi5": {"rotate": [{"angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.52}]}, "youxiaobi4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "youxiaobi9": {"rotate": [{"time": 0.8333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.73, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.73}]}, "beijing4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 18.48, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "shu5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 45.08, "y": 66.76, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 15.95, "y": -26.1, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -21.85, "y": -13.18, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -18.73, "y": -8.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "shu2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 67.54, "y": 53.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -46.93, "y": 7.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -54.14, "y": -61.98, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -51.96, "y": -32.17, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "shu1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -47.08, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 84.14, "y": 41.47, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 26.62, "y": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -86.03, "y": -97.17, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -64.35, "y": -17.77, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "shu3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.44, "y": 14.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.3333, "x": 10.89, "y": 3.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.6667}]}, "shu4": {"translate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 1, "x": 10.6, "y": 5.78, "curve": 0.308, "c2": 0.25, "c3": 0.654, "c4": 0.63}, {"time": 2.3333, "x": -1.46, "y": -7.67, "curve": 0.34, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 3.3333, "x": 8.99, "y": 19.7, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.6667}]}, "shu6": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 48.55, "y": 19.07, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -30.23, "y": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": -73.58, "y": -42.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -127.97, "y": -27.92, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -36.24, "y": -10.49, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "beijing25": {"rotate": [{"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 54.54, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 64.64, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "beijing24": {"rotate": [{"angle": 27.6, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 46.86, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 59.84, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -8.34, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 27.6}]}, "beijing5": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "texiao4": {"translate": [{"x": -33.74, "y": -242.45, "curve": "stepped"}, {"time": 0.8333, "x": -33.74, "y": -242.45, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 116.27, "y": -184.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 130.29, "y": -108.93, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 114.29, "y": -53.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 128.3, "y": -11.82, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 116.31, "y": 37.74, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -33.74, "y": -242.45, "curve": "stepped"}, {"time": 3.1667, "x": -33.74, "y": -242.45, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 116.27, "y": -184.48, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 130.29, "y": -108.93, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 114.29, "y": -53.37, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 128.3, "y": -11.82, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 116.31, "y": 37.74, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 116.33, "y": 131.16}], "scale": [{"x": 2, "y": 1.5, "curve": "stepped"}, {"time": 1.3333, "x": 2, "y": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.1, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 2, "y": 1.5, "curve": "stepped"}, {"time": 3.6667, "x": 2, "y": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.1, "y": 1.1}]}, "beijing11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -24.14, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -25.34, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "beijing6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 49.06, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 49.06, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "meimao2": {"rotate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -6.03, "curve": 0.25, "c3": 0.75}, {"time": 3.6}], "translate": [{"time": 0.4333, "curve": 0, "c2": 0.37, "c3": 0.75}, {"time": 0.6, "x": 4.66, "y": -2.33, "curve": 0.25, "c4": 0.59}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.7667, "curve": 0, "c2": 0.45, "c3": 0.75}, {"time": 2.9333, "x": 4.66, "y": -2.33, "curve": 0.25, "c4": 0.66}, {"time": 3.1667}], "scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 1.082, "y": 1.082, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.002, "y": 1.002, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 1.082, "y": 1.082, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 1.002, "y": 1.002, "curve": 0.25, "c3": 0.75}, {"time": 3.6}]}, "meimao1": {"rotate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 4.31, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 4.31, "curve": 0.25, "c3": 0.75}, {"time": 3.6}], "translate": [{"time": 0.4333, "curve": 0, "c2": 0.34, "c3": 0.75}, {"time": 0.6, "x": 4.66, "y": -2.33, "curve": 0.381, "c4": 0.46}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.7667, "curve": 0, "c2": 0.41, "c3": 0.75}, {"time": 2.9333, "x": 4.66, "y": -2.33, "curve": 0.47, "c4": 0.62}, {"time": 3.1667}], "scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 1.082, "y": 1.082, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.002, "y": 1.002, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 1.082, "y": 1.082, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 1.002, "y": 1.002, "curve": 0.25, "c3": 0.75}, {"time": 3.6}]}, "huzi2": {"rotate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -4.86, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": "stepped"}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -4.86, "curve": 0.25, "c3": 0.75}, {"time": 3.8333}], "scale": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": "stepped"}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.8333}]}, "huzi1": {"rotate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 12.47, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.66, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": "stepped"}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 12.47, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 3.66, "curve": 0.25, "c3": 0.75}, {"time": 4.1667}], "scale": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": "stepped"}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667}]}, "xuanzhuan2": {"rotate": [{}, {"time": 1.1667, "angle": 90, "curve": 0, "c2": 0.26, "c3": 0.75}, {"time": 2.3333, "angle": 180}, {"time": 3.5, "angle": -90, "curve": 0, "c2": 0.34, "c3": 0.75}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.5, "y": 0.74, "curve": 0.25, "c3": 0.75}, {"time": 2.3333}]}, "wu1": {"scale": [{}, {"time": 1.4333, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 1.7667, "x": 1.3, "y": 1.2}, {"time": 1.8, "curve": "stepped"}, {"time": 2.3333}, {"time": 3.7667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 4.1, "x": 1.3, "y": 1.2}, {"time": 4.1333}]}, "wu3": {"scale": [{"x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.1667, "x": 1.3, "y": 1.2}, {"time": 0.2, "curve": "stepped"}, {"time": 0.7333}, {"time": 2.1667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 2.5, "x": 1.3, "y": 1.2}, {"time": 2.5333, "curve": "stepped"}, {"time": 3.0667}, {"time": 4.2333, "x": 1.163, "y": 1.163}, {"time": 4.5, "x": 1.3, "y": 1.2}]}, "wu4": {"scale": [{"x": 1.121, "y": 1.121}, {"time": 0.3, "x": 1.163, "y": 1.163}, {"time": 0.5667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.9, "x": 1.3, "y": 1.2}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.4667}, {"time": 2.9, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 3.2333, "x": 1.3, "y": 1.2}, {"time": 3.2667, "curve": "stepped"}, {"time": 3.8}, {"time": 4.6667, "x": 1.121, "y": 1.121}]}, "wu2": {"scale": [{}, {"time": 1.4333, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 1.7667, "x": 1.3, "y": 1.2}, {"time": 1.8, "curve": "stepped"}, {"time": 2.3333}, {"time": 3.7667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 4.1, "x": 1.3, "y": 1.2}, {"time": 4.1333}]}, "wu5": {"scale": [{"x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.1667, "x": 1.3, "y": 1.2}, {"time": 0.2, "curve": "stepped"}, {"time": 0.7333}, {"time": 2.1667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 2.5, "x": 1.3, "y": 1.2}, {"time": 2.5333, "curve": "stepped"}, {"time": 3.0667}, {"time": 4.5, "x": 1.3, "y": 1.2}]}, "wu6": {"scale": [{"x": 1.181, "y": 1.121}, {"time": 0.5667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.9, "x": 1.3, "y": 1.2}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.4667}, {"time": 2.9, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 3.2333, "x": 1.3, "y": 1.2}, {"time": 3.2667, "curve": "stepped"}, {"time": 3.8}, {"time": 4.6667, "x": 1.181, "y": 1.121}]}, "texiao": {"scale": [{"x": 1.14, "y": 1.093}, {"time": 0.3667, "x": 1.216, "y": 1.144}, {"time": 0.7667, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 1.1, "x": 1.3, "y": 1.2}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}, {"time": 3.1, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 3.4333, "x": 1.3, "y": 1.2}, {"time": 3.4667, "curve": "stepped"}, {"time": 4}, {"time": 4.6667, "x": 1.14, "y": 1.093}]}, "texiao5": {"scale": [{"time": 0.0667}, {"time": 0.3667, "x": 1.063, "y": 1.042}, {"time": 1.5, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 1.8333, "x": 1.3, "y": 1.2}, {"time": 1.8667, "curve": "stepped"}, {"time": 2.4}, {"time": 3.8333, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 4.1667, "x": 1.3, "y": 1.2}, {"time": 4.2}]}, "texiao8": {"scale": [{"x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 0.2333, "x": 1.3, "y": 1.2}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.8}, {"time": 1.6667, "x": 1.181, "y": 1.121}, {"time": 2.2333, "x": 1.3, "y": 1.2, "curve": "stepped"}, {"time": 2.5667, "x": 1.3, "y": 1.2}, {"time": 2.6, "curve": "stepped"}, {"time": 3.1333}, {"time": 4.5667, "x": 1.3, "y": 1.2}]}, "yachi": {"translate": [{"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -13.03, "y": -5.13, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -24.19, "y": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -20.47, "y": -8.06, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "yanjing3": {"scale": [{"time": 1.3333}, {"time": 1.6667, "x": 1.22, "y": 1.22}, {"time": 2.6667}]}, "yanjing4": {"scale": [{"time": 1.3333}, {"time": 1.6667, "x": 1.22, "y": 1.22}, {"time": 2.6667}]}}, "deform": {"default": {"dangbu": {"dangbu": [{"offset": 46, "vertices": [-3.38246, -2.59709, -3.47754, -2.29809, -3.5948, -1.91624, -3.6468, -1.67616, -3.66159, -1.57991, -1.58336, -4.06671, -0.97386, -4.15121, -0.38135, -4.15109, -0.3916, -3.99435], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 5.86571, 10.62106, 6.69887, 10.02138, 8.48859, 8.16731, 9.41895, 6.6179, 9.49988, 6.0287, -2.12436, 11.6946, -2.18224, 11.94235, -3.62978, 11.49791, -1.79955, 11.63986, -0.96014, 11.4705, 3.39001, 16.27112, 4.71777, 15.81795, 7.80771, 14.11613, 9.65277, 12.46458, -7.67967, 14.74089, -9.39272, 13.57401, -7.05727, 14.50545, -5.88887, 14.62402, -6.56766, 13.93797, 0.0775, 1.80768, 0.45149, 1.70964, 0.69178, 1.58353, -1.21123, 1.36102, -1.36206, 1.19109, -1.13892, 1.35246, -1.01794, 1.39635, -1.07172, 1.30547], "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 4.36351, 4.78389, 4.21781, 4.46695, 4.85728, 3.4057, 5.15772, 2.60684, 5.22487, 2.22934, 0.68494, 6.62373, 0.35207, 6.47437, -0.84784, 6.08749, 0.00658, 5.93079, 0.35179, 5.76722, 3.39001, 16.27112, 4.71777, 15.81795, 7.80771, 14.11613, 9.65277, 12.46458, -7.67967, 14.74089, -9.39272, 13.57401, -7.05727, 14.50545, -5.88887, 14.62402, -6.56766, 13.93797, 0.0775, 1.80768, 0.45149, 1.70964, 0.69178, 1.58353, -1.21123, 1.36102, -1.36206, 1.19109, -1.13892, 1.35246, -1.01794, 1.39635, -1.07172, 1.30547], "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 2.43216, -4.50378, -1.95159, -3.56821, -4.15638, -2.15786, -5.07113, -0.71372, -4.96673, -0.79142, 5.37448, -1.04659, 4.739, -1.93668, 1.5648, -3.75116, -0.62553, -4.64191, -1.94366, -4.73933, 12.00905, 0.54741, 5.70497, -3.3046, 1.6743, -4.94234, -0.97934, -4.86047, 8.94819, 8.0298, 6.14925, 2.38705, 5.00951, -1.4706, 3.70384, -3.29906, 4.27962, -2.97487, -10.04523, -4.67726, -11.85042, -0.87054, -12.06305, 1.99654, 0.29841, -9.79456, -2.63066, -10.76292, -6.10182, -10.19368, -7.81078, -9.40671, -6.92351, -9.86836], "curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 28, "vertices": [-21.10349, -19.99986, -23.08531, -17.73276, -24.13258, -13.11888, -22.54674, -9.79493, -22.46397, -8.75478, -3.06308, -28.22568, -3.66327, -28.83625, -0.59842, -29.10129, -2.92412, -24.40848, -15.37457, -8.1398, -16.47507, -5.07919, -15.29947, -6.80289, -11.34256, -11.29792, -12.06123, -11.0075, -5.39182, -16.8047, -6.74393, -16.03628, -6.33934, -16.03303, 4.02875, -15.49318, 10.92833, -18.32014, 3.6106, -18.53137, 3.4234, -16.11474, 7.17746, -10.87589, 7.02275, -11.7443, 21.48434, -5.03632, 20.06396, -7.24927, 16.71951, -8.7686, 15.15151, -6.46887, 13.01112, 0.70644, 12.00905, 0.54741, 5.70497, -3.3046, 1.6743, -4.94234, -0.97934, -4.86047, 8.94819, 8.0298, 6.14925, 2.38705, 5.00951, -1.4706, 3.70384, -3.29906, 4.27962, -2.97487, 3.10603, 0.44391, 0.38396, 6.02051, -3.24724, 12.96261, 4.78492, 3.82495, 1.5972, 2.70139, -4.7022, 3.77745, -12.82842, 3.73938, -12.79932, 3.23074], "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "offset": 46, "vertices": [-3.38246, -2.59709, -3.47754, -2.29809, -3.5948, -1.91624, -3.6468, -1.67616, -3.66159, -1.57991, -1.58336, -4.06671, -0.97386, -4.15121, -0.38135, -4.15109, -0.3916, -3.99435], "curve": 0.25, "c3": 0.75}, {"time": 3, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 5.86571, 10.62106, 6.69887, 10.02138, 8.48859, 8.16731, 9.41895, 6.6179, 9.49988, 6.0287, -2.12436, 11.6946, -2.18224, 11.94235, -3.62978, 11.49791, -1.79955, 11.63986, -0.96014, 11.4705, 3.39001, 16.27112, 4.71777, 15.81795, 7.80771, 14.11613, 9.65277, 12.46458, -7.67967, 14.74089, -9.39272, 13.57401, -7.05727, 14.50545, -5.88887, 14.62402, -6.56766, 13.93797, 0.0775, 1.80768, 0.45149, 1.70964, 0.69178, 1.58353, -1.21123, 1.36102, -1.36206, 1.19109, -1.13892, 1.35246, -1.01794, 1.39635, -1.07172, 1.30547], "curve": 0.25, "c3": 0.75}, {"time": 3.1, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 4.36351, 4.78389, 4.21781, 4.46695, 4.85728, 3.4057, 5.15772, 2.60684, 5.22487, 2.22934, 0.68494, 6.62373, 0.35207, 6.47437, -0.84784, 6.08749, 0.00658, 5.93079, 0.35179, 5.76722, 3.39001, 16.27112, 4.71777, 15.81795, 7.80771, 14.11613, 9.65277, 12.46458, -7.67967, 14.74089, -9.39272, 13.57401, -7.05727, 14.50545, -5.88887, 14.62402, -6.56766, 13.93797, 0.0775, 1.80768, 0.45149, 1.70964, 0.69178, 1.58353, -1.21123, 1.36102, -1.36206, 1.19109, -1.13892, 1.35246, -1.01794, 1.39635, -1.07172, 1.30547], "curve": 0.25, "c3": 0.75}, {"time": 3.5, "offset": 28, "vertices": [-19.59081, -14.70309, -20.61249, -12.90775, -22.37472, -8.01926, -22.81024, -4.39575, -22.46992, -3.23888, -5.75867, -23.27454, -5.84688, -23.77861, -2.81702, -24.15372, -7.71811, -21.91168, -1.55774, 0.3559, -1.51163, 0.48265, -1.34238, 0.77646, -1.18025, 0.95081, -1.10837, 0.98256, -1.40201, -0.69418, -1.43089, -0.71089, -1.3219, -0.87769, -1.4165, -0.53903, 2.43216, -4.50378, -1.95159, -3.56821, -4.15638, -2.15786, -5.07113, -0.71372, -4.96673, -0.79142, 5.37448, -1.04659, 4.739, -1.93668, 1.5648, -3.75116, -0.62553, -4.64191, -1.94366, -4.73933, 12.00905, 0.54741, 5.70497, -3.3046, 1.6743, -4.94234, -0.97934, -4.86047, 8.94819, 8.0298, 6.14925, 2.38705, 5.00951, -1.4706, 3.70384, -3.29906, 4.27962, -2.97487, -10.04523, -4.67726, -11.85042, -0.87054, -12.06305, 1.99654, 0.29841, -9.79456, -2.63066, -10.76292, -6.10182, -10.19368, -7.81078, -9.40671, -6.92351, -9.86836], "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "offset": 28, "vertices": [-21.10349, -19.99986, -23.08531, -17.73276, -24.13258, -13.11888, -22.54674, -9.79493, -22.46397, -8.75478, -3.06308, -28.22568, -3.66327, -28.83625, -0.59842, -29.10129, -2.92412, -24.40848, -15.37457, -8.1398, -16.47507, -5.07919, -15.29947, -6.80289, -11.34256, -11.29792, -12.06123, -11.0075, -5.39182, -16.8047, -6.74393, -16.03628, -6.33934, -16.03303, 4.02875, -15.49318, 10.92833, -18.32014, 3.6106, -18.53137, 3.4234, -16.11474, 7.17746, -10.87589, 7.02275, -11.7443, 21.48434, -5.03632, 20.06396, -7.24927, 16.71951, -8.7686, 15.15151, -6.46887, 13.01112, 0.70644, 12.00905, 0.54741, 5.70497, -3.3046, 1.6743, -4.94234, -0.97934, -4.86047, 8.94819, 8.0298, 6.14925, 2.38705, 5.00951, -1.4706, 3.70384, -3.29906, 4.27962, -2.97487, 3.10603, 0.44391, 0.38396, 6.02051, -3.24724, 12.96261, 4.78492, 3.82495, 1.5972, 2.70139, -4.7022, 3.77745, -12.82842, 3.73938, -12.79932, 3.23074], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "offset": 46, "vertices": [-3.38246, -2.59709, -3.47754, -2.29809, -3.5948, -1.91624, -3.6468, -1.67616, -3.66159, -1.57991, -1.58336, -4.06671, -0.97386, -4.15121, -0.38135, -4.15109, -0.3916, -3.99435]}]}, "heishanlaoyao": {"heishanlaoyao": [{"offset": 264, "vertices": [1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}, {"time": 1.1667, "offset": 6, "vertices": [37.01584, -5.19891, 17.56363, -30.8661, -7.83007, -39.02874, -29.07426, -19.08594, -30.20103, 12.94344, -20.83209, 40.56923, -15.16461, 45.63982, 11.66888, 15.48314, 14.87523, 11.66552, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63.72848, -0.90401, 80.21976, 4.01882, 87.30679, -4.92717, 86.85941, -21.81368, 82.88434, -35.2578, 69.74225, -50.65623, 58.44325, -36.06524, 52.7627, -31.8332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}, {"time": 2.3333, "offset": 264, "vertices": [1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}, {"time": 3.5, "offset": 6, "vertices": [37.01584, -5.19891, 17.56363, -30.8661, -7.83007, -39.02874, -29.07426, -19.08594, -30.20103, 12.94344, -20.83209, 40.56923, -15.16461, 45.63982, 11.66888, 15.48314, 14.87523, 11.66552, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63.72848, -0.90401, 80.21976, 4.01882, 87.30679, -4.92717, 86.85941, -21.81368, 82.88434, -35.2578, 69.74225, -50.65623, 58.44325, -36.06524, 52.7627, -31.8332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}, {"time": 4.6667, "offset": 264, "vertices": [1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701, 1.10199, -1.55701]}]}, "yanjing2": {"yanjing2": [{"offset": 26, "vertices": [0.23975, -0.21631], "curve": "stepped"}, {"time": 0.4, "offset": 26, "vertices": [0.23975, -0.21631], "curve": 0, "c2": 0.16, "c3": 0.75}, {"time": 0.6, "vertices": [0.32556, -1.23749, 3.18701, -0.96603, 4.15332, -2.11094, 1.34863, -2.07011, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23975, -0.21631, 1.62756], "curve": 0.25, "c4": 0.64}, {"time": 0.8333, "vertices": [-2.9502, 2.04332, -1.03461, 0.80608, -0.11121, 0.91625, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23975, -0.21631]}, {"time": 2.3333, "offset": 26, "vertices": [0.23975, -0.21631], "curve": "stepped"}, {"time": 2.7333, "offset": 26, "vertices": [0.23975, -0.21631], "curve": 0, "c2": 0.16, "c3": 0.75}, {"time": 2.9333, "vertices": [0.32556, -1.23749, 3.18701, -0.96603, 4.15332, -2.11094, 1.34863, -2.07011, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23975, -0.21631, 1.62756], "curve": 0.25, "c4": 0.64}, {"time": 3.1667, "vertices": [-2.9502, 2.04332, -1.03461, 0.80608, -0.11121, 0.91625, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23975, -0.21631]}, {"time": 4.6667, "offset": 26, "vertices": [0.23975, -0.21631]}]}, "youdatui2": {"youdatui2": [{"offset": 18, "vertices": [1.93533, -3.84293]}, {"time": 0.6667, "offset": 14, "vertices": [9.26516, -3.61014, 16.27471, 0.93989, 10.17694, -19.98319, 11.7945, -15.01548]}, {"time": 0.8333, "offset": 14, "vertices": [11.58145, -4.51267, 20.34338, 1.17486, 8.62848, -13.12063, 6.76694, -10.98196]}, {"time": 1.3333, "offset": 18, "vertices": [3.44644, -10.59183, 8.10861, -10.55568]}, {"time": 1.6667, "offset": 14, "vertices": [7.60828, -0.60697, 16.50376, -2.73043, 8.67138, -20.17707, 8.12164, -13.58149]}, {"time": 2, "offset": 14, "vertices": [15.21657, -1.21394, 26.95015, -4.54137, 16.9595, -9.57256, 6.48489, -7.06223]}, {"time": 2.3333, "offset": 18, "vertices": [1.93533, -3.84293]}, {"time": 3, "offset": 14, "vertices": [9.26516, -3.61014, 16.27471, 0.93989, 10.17694, -19.98319, 11.7945, -15.01548]}, {"time": 3.1667, "offset": 14, "vertices": [11.58145, -4.51267, 20.34338, 1.17486, 8.62848, -13.12063, 6.76694, -10.98196]}, {"time": 3.6667, "offset": 18, "vertices": [3.44644, -10.59183, 8.10861, -10.55568]}, {"time": 4, "offset": 14, "vertices": [7.60828, -0.60697, 16.50376, -2.73043, 8.67138, -20.17707, 8.12164, -13.58149]}, {"time": 4.3333, "offset": 14, "vertices": [15.21657, -1.21394, 26.95015, -4.54137, 16.9595, -9.57256, 6.48489, -7.06223]}, {"time": 4.6667, "offset": 18, "vertices": [1.93533, -3.84293]}]}, "xuanzhuan1": {"xuanzhuan1": [{"offset": 10, "vertices": [0.7312, 1.08154, 0.87042, -1.70563]}, {"time": 0.4, "vertices": [-13.39819, -13.72544, -15.87707, -13.45438, -16.25982, -7.17588, -13.14601, -0.01043, -14.68376, 4.93367, -16.24631, 11.43638, -12.06781, 9.57838, -11.83101, 11.6961, -10.49418, 16.72785, 0.57764, 19.06791, 10.88095, 19.98938, 14.9584, 17.55696, 14.06435, 15.51774, 14.35611, 5.35254, 16.23355, -5.035, 23.31629, -10.03007, 21.83068, -16.78044, 20.15303, -16.89948, 16.63615, -18.52275, 7.17014, -17.20588, -2.27093, -18.16859, -9.6987, -13.05803]}, {"time": 0.8333, "vertices": [-27.9129, -28.59467, -34.17828, -17.79126, -37.6073, -1.93861, -31.70996, 9.88752, -32.97687, 16.38387, -34.63861, 22.65412, -26.08423, 21.80272, -17.04791, 26.74503, -2.76129, 37.97768, 10.75427, 41.28883, 22.66864, 41.64454, 31.16333, 36.57699, 33.27832, 22.65052, 40.61108, -2.24846, 41.49036, -19.10395, 50.14459, -27.75562, 45.48059, -34.95924, 35.55695, -32.87715, 19.61896, -33.45871, 1.26031, -34.63068, -10.6889, -33.33871, -20.20563, -27.20423]}, {"time": 1.3333, "vertices": [-48.45125, -24.94424, -50.8042, -7.20292, -51.98012, 8.97669, -50.8783, 23.86078, -47.13438, 31.19124, -41.95329, 40.24373, -22.88811, 40.92004, -7.95757, 43.13138, 7.23991, 53.13043, 24.35532, 49.1041, 37.75713, 38.50808, 49.70175, 24.30758, 52.6292, 7.88161, 54.60086, -15.44605, 53.51272, -32.88342, 56.56767, -42.27536, 47.5064, -51.18335, 31.49622, -50.83792, 10.61891, -47.44164, -11.01217, -48.84343, -27.17641, -45.88372, -38.72342, -33.45285]}, {"time": 1.8, "vertices": [-70.33557, -9.03977, -67.27234, 4.7113, -64.43115, 19.90658, -53.30231, 35.76167, -44.6665, 44.18957, -34.92932, 52.43295, -13.52368, 54.46387, 4.56427, 54.77796, 19.70447, 52.63911, 43.38464, 42.38917, 57.34082, 25.84393, 67.00427, 12.85613, 70.69, -5.9027, 66.16644, -27.22312, 60.57782, -44.33759, 57.9776, -55.0589, 43.21661, -64.48256, 18.75781, -64.35785, -6.40973, -57.46369, -28.49683, -51.08958, -50.47107, -36.96075, -64.66113, -19.07675]}, {"time": 2.2333, "vertices": [-79.70609, -0.71279, -75.49484, 15.45541, -66.7811, 34.33334, -50.13989, 50.46962, -39.3096, 59.09302, -25.79282, 65.6636, -1.49915, 63.1622, 16.97455, 57.99012, 32.62564, 48.66281, 56.65292, 32.04926, 70.14432, 13.94578, 78.80569, 0.72525, 73.71265, -18.85919, 63.13666, -43.42595, 54.5289, -60.84639, 47.12042, -69.61516, 29.45468, -78.58795, 4.45776, -70.374, -20.50885, -57.61506, -42.38077, -45.85937, -61.2876, -31.10726, -71.92096, -12.66581]}, {"time": 2.6667, "vertices": [-78.80939, 7.89623, -69.67719, 30.65815, -56.42535, 51.2383, -38.23944, 68.88882, -24.23798, 74.17062, -8.61285, 75.87523, 16.10889, 62.34037, 34.29462, 49.25644, 47.72858, 39.3774, 69.9212, 21.70936, 82.94781, 2.04762, 80.31903, -15.99326, 71.09564, -33.77609, 57.46588, -57.85042, 46.16931, -75.79881, 36.26324, -84.17142, 15.84143, -84.63602, -8.599, -69.55333, -34.36713, -53.13249, -56.14484, -38.31219, -69.6731, -24.49295, -79.18079, -6.25486]}, {"time": 3.1667, "vertices": [-62.73257, 38.18067, -48.7435, 54.23508, -32.90378, 64.24633, -13.42453, 67.2901, 1.37256, 63.75833, 15.68701, 60.20965, 38.35971, 45.46159, 55.91882, 31.38833, 66.66101, 15.90009, 72.51831, 1.11702, 72.37729, -18.00052, 66.82455, -32.81509, 54.12476, -47.75425, 33.01437, -64.30235, 18.39139, -72.32404, 10.22754, -69.45779, -15.96402, -62.58065, -37.27115, -45.19556, -59.73611, -23.49065, -69.68637, -8.88144, -74.55627, 7.17407, -68.25537, 27.31042]}, {"time": 3.6667, "vertices": [-45.00708, 43.84067, -29.82117, 49.89318, -9.39221, 52.30645, 10.01428, 55.10042, 25.31146, 50.15242, 39.98688, 44.54407, 54.0816, 25.30238, 58.42926, 10.07841, 60.22772, -2.48938, 59.78168, -17.30528, 56.20837, -31.39948, 46.20264, -39.55788, 33.79926, -47.47716, 10.30164, -53.21175, -6.79913, -58.59576, -12.83051, -55.31039, -25.95673, -49.46033, -40.04742, -30.12346, -52.50623, -9.56918, -60.54639, 8.62938, -61.97241, 21.31622, -53.34412, 40.43484]}, {"time": 4.1667, "vertices": [-10.86878, 19.60206, 0.19409, 21.01176, 7.5387, 22.23171, 17.73017, 21.86008, 19.06199, 14.28313, 17.14096, 7.70542, 25.84516, -6.29705, 29.01194, -14.498, 25.52879, -13.75142, 23.08347, -12.70426, 18.27174, -17.47416, 11.73993, -18.12397, 1.12564, -21.51039, -10.03381, -28.13358, -12.01285, -29.97426, -7.88669, -23.78455, -13.59439, -17.81629, -18.48148, 1.51251, -23.69642, 13.0995, -28.05531, 16.25951, -25.87507, 17.58273, -19.26777, 21.94843]}, {"time": 4.6667, "offset": 10, "vertices": [0.7312, 1.08154, 0.87042, -1.70563]}]}, "yanjing1": {"yanjing1": [{"offset": 2, "vertices": [0.03082, -0.62324], "curve": "stepped"}, {"time": 0.4, "offset": 2, "vertices": [0.03082, -0.62324], "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 0.6, "vertices": [3.3197, -5.16576, 3.71802, -4.43129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.59601, -0.40235, 2.79376, -0.54199, 4.00891, -2.23525], "curve": 0.25, "c4": 0.71}, {"time": 0.8333, "vertices": [-1.31616, -0.98753, 1.65137, -2.26788, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.10095, -1.57452, -1.5116, -1.22902]}, {"time": 2.3333, "offset": 2, "vertices": [0.03082, -0.62324], "curve": "stepped"}, {"time": 2.7333, "offset": 2, "vertices": [0.03082, -0.62324], "curve": 0, "c2": 0.19, "c3": 0.75}, {"time": 2.9333, "vertices": [3.3197, -5.16576, 3.71802, -4.43129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.59601, -0.40235, 2.79376, -0.54199, 4.00891, -2.23525], "curve": 0.25, "c4": 0.71}, {"time": 3.1667, "vertices": [-1.31616, -0.98753, 1.65137, -2.26788, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.10095, -1.57452, -1.5116, -1.22902]}, {"time": 4.6667, "offset": 2, "vertices": [0.03082, -0.62324]}]}, "youxuiaotui2": {"youxuiaotui2": [{"offset": 102, "vertices": [0.48044, 1.33839, -0.23959, 1.40167], "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "offset": 98, "vertices": [2.6149, 9.21962, -1.76807, 9.41857, 5.9647, 20.01318, -3.66297, 20.57058, 2.90762, 10.25139, -1.96585, 10.47267], "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "offset": 102, "vertices": [0.48044, 1.33839, -0.23959, 1.40167], "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "offset": 98, "vertices": [2.6149, 9.21962, -1.76807, 9.41857, 5.9647, 20.01318, -3.66297, 20.57058, 2.90762, 10.25139, -1.96585, 10.47267], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "offset": 102, "vertices": [1.08673, 1.62439, 0.14774, 1.94873]}]}, "biyan": {"biyan": [{"time": 1.0333, "vertices": [8.04675, -4.54137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.48621, 4.76982, 22.99463, 6.18795, 41.70264, 2.78001, 46.18787, 2.89685, 46.12848, 5.11605, 43.73682, -0.37101, 25.87354, -5.87912, 14.6134, -3.75144, 0, 0, 0, 0, 13.42377, -3.27269, 30.20154, -1.92525, 41.44092, -1.23176, 46.4173, -9.20277, 39.92633, -18.25568, 40.85229, -22.74601, 32.3067, -25.59232, 22.17816, -21.95834, 16.11371, -11.59898], "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 15.80115, -3.8375, 24.98145, -5.02985, 18.96552, -13.43731, 22.93707, -4.67553, 13.22858, -3.31053, 6.02765, -2.2454, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, 16.69543, -6.40791, 24.46967, -14.7349, 19.73352, -19.16244, 18.19244, -21.17055, 12.28687, -19.00793, 3.28119, -14.57358, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.1, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 2.49835, -0.7139, 5.04016, -6.10699, 0.34418, -1.37444, 2.27356, -4.7381, -6.24304, -7.39289, -3.34064, -1.64785, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -3.8783, -12.40305, -4.10822, -18.31657, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 1.68793, -0.54974, -2.46759, -1.51248, -0.33173, -3.51298, -4.04962, 2.60317, -2.8844, -3.00537, -5.23846, -1.34132, -5.06134, -2.03774, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -6.41125, -13.59341, -5.89233, -17.92814, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": "stepped"}, {"time": 1.2333, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 1.68793, -0.54974, -2.46759, -1.51248, -0.33173, -3.51298, -4.04962, 2.60317, -2.8844, -3.00537, -5.23846, -1.34132, -5.06134, -2.03774, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -6.41125, -13.59341, -5.89233, -17.92814, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 2.49835, -0.7139, 5.04016, -6.10699, 0.34418, -1.37444, 2.27356, -4.7381, -6.24304, -7.39289, -3.34064, -1.64785, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -3.8783, -12.40305, -4.10822, -18.31657, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.3, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 15.80115, -3.8375, 24.98145, -5.02985, 18.96552, -13.43731, 22.93707, -4.67553, 13.22858, -3.31053, 6.02765, -2.2454, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, 16.69543, -6.40791, 24.46967, -14.7349, 19.73352, -19.16244, 18.19244, -21.17055, 12.28687, -19.00793, 3.28119, -14.57358, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [8.04675, -4.54137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.48621, 4.76982, 22.99463, 6.18795, 41.70264, 2.78001, 46.18787, 2.89685, 46.12848, 5.11605, 43.73682, -0.37101, 25.87354, -5.87912, 14.6134, -3.75144, 0, 0, 0, 0, 13.42377, -3.27269, 30.20154, -1.92525, 41.44092, -1.23176, 46.4173, -9.20277, 39.92633, -18.25568, 40.85229, -22.74601, 32.3067, -25.59232, 22.17816, -21.95834, 16.11371, -11.59898], "curve": "stepped"}, {"time": 3.3667, "vertices": [8.04675, -4.54137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.48621, 4.76982, 22.99463, 6.18795, 41.70264, 2.78001, 46.18787, 2.89685, 46.12848, 5.11605, 43.73682, -0.37101, 25.87354, -5.87912, 14.6134, -3.75144, 0, 0, 0, 0, 13.42377, -3.27269, 30.20154, -1.92525, 41.44092, -1.23176, 46.4173, -9.20277, 39.92633, -18.25568, 40.85229, -22.74601, 32.3067, -25.59232, 22.17816, -21.95834, 16.11371, -11.59898], "curve": 0.25, "c3": 0.75}, {"time": 3.4, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 15.80115, -3.8375, 24.98145, -5.02985, 18.96552, -13.43731, 22.93707, -4.67553, 13.22858, -3.31053, 6.02765, -2.2454, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, 16.69543, -6.40791, 24.46967, -14.7349, 19.73352, -19.16244, 18.19244, -21.17055, 12.28687, -19.00793, 3.28119, -14.57358, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 2.49835, -0.7139, 5.04016, -6.10699, 0.34418, -1.37444, 2.27356, -4.7381, -6.24304, -7.39289, -3.34064, -1.64785, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -3.8783, -12.40305, -4.10822, -18.31657, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 1.68793, -0.54974, -2.46759, -1.51248, -0.33173, -3.51298, -4.04962, 2.60317, -2.8844, -3.00537, -5.23846, -1.34132, -5.06134, -2.03774, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -6.41125, -13.59341, -5.89233, -17.92814, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": "stepped"}, {"time": 3.5667, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 1.68793, -0.54974, -2.46759, -1.51248, -0.33173, -3.51298, -4.04962, 2.60317, -2.8844, -3.00537, -5.23846, -1.34132, -5.06134, -2.03774, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -6.41125, -13.59341, -5.89233, -17.92814, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.6, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 2.49835, -0.7139, 5.04016, -6.10699, 0.34418, -1.37444, 2.27356, -4.7381, -6.24304, -7.39289, -3.34064, -1.64785, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, -8.1795, -4.4646, -3.8783, -12.40305, -4.10822, -18.31657, -4.06421, -20.91122, 1.06427, -17.81416, -1.19025, -13.80044, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "vertices": [-1.23267, 4.49939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.37866, -1.24813, 5.12085, -0.19287, 15.80115, -3.8375, 24.98145, -5.02985, 18.96552, -13.43731, 22.93707, -4.67553, 13.22858, -3.31053, 6.02765, -2.2454, 0, 0, 0, 0, -2.45929, 3.74506, 12.85022, -2.33291, 16.69543, -6.40791, 24.46967, -14.7349, 19.73352, -19.16244, 18.19244, -21.17055, 12.28687, -19.00793, 3.28119, -14.57358, 5.17377, -1.18847], "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "vertices": [8.04675, -4.54137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.48621, 4.76982, 22.99463, 6.18795, 41.70264, 2.78001, 46.18787, 2.89685, 46.12848, 5.11605, 43.73682, -0.37101, 25.87354, -5.87912, 14.6134, -3.75144, 0, 0, 0, 0, 13.42377, -3.27269, 30.20154, -1.92525, 41.44092, -1.23176, 46.4173, -9.20277, 39.92633, -18.25568, 40.85229, -22.74601, 32.3067, -25.59232, 22.17816, -21.95834, 16.11371, -11.59898]}]}}}, "drawOrder": [{"offsets": [{"slot": "<PERSON><PERSON><PERSON>", "offset": 2}, {"slot": "xuanzhuan2", "offset": -3}, {"slot": "beijing", "offset": -3}, {"slot": "biyan", "offset": -5}]}]}}}