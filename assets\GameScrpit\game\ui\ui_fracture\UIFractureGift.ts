import { _decorator, Component, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { ActivityModule } from "../../../module/activity/ActivityModule";
import { FractureActivityConfig } from "../../../module/fracture/FractureConstant";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { FractureGiftAdapter } from "./adapter/FractureGiftViewHolder";
import { FractureModule } from "../../../module/fracture/FractureModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { RedeemBuyMessage } from "../../net/protocol/Activity";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
/**
 * ivan_huang
 * Sun May 25 2025 10:39:30 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_fracture/UIFractureGift.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIFractureGift")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureGift",
  nextHop: [],
  exit: "",
})
export class UIFractureGift extends BaseCtrl {
  public playShowAni: boolean = true;
  private _adapter: FractureGiftAdapter = null;
  private onUpdateFractureInfo(data: RedeemBuyMessage) {
    this._adapter?.notifyDataSetChanged();
    TipsMgr.showTip("支付成功");
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
  }
  protected start(): void {
    super.start();
    let activity = ActivityModule.data.allActivityConfig[11501] as FractureActivityConfig;
    let giftList = activity.redeemList[0];
    this._adapter = new FractureGiftAdapter(this.getNode("node_gift_viewholder"));
    this.getNode("node_list").getComponent(AdapterView).setAdapter(this._adapter);
    this._adapter.setData(giftList);
    MsgMgr.on(MsgEnum.ON_FRACTURE_BUY_UPDATE, this.onUpdateFractureInfo, this);
  }
  protected onDestroy(): void {
    super.onDestroy();
    MsgMgr.off(MsgEnum.ON_FRACTURE_BUY_UPDATE, this.onUpdateFractureInfo, this);
  }
  update(deltaTime: number) {}

  private onClickTest() {
    //
    let activity = ActivityModule.data.allActivityConfig[11501] as FractureActivityConfig;
    let giftList = [].concat(activity.redeemList[0]);
    giftList.shift();
    this._adapter.setData(giftList);
  }
}
