import { Layers, Node, UIOpacity, UITransform, Vec2, Vec3, instantiate, v3 } from "cc";
import ResMgr from "../common/ResMgr";
import Tool from "../common/Tool";
import NotifySection from "./NotifySection";
import { Section } from "./Section";
import { LayerEnum } from "../../game/GameDefine";

export enum GO_STATE {
  NULL,
  INIT,
  DISABLED,
  RUNNING,
  PAUSE,
  REMOVE,
}
export default class GameObject extends Node {
  public static count: number = 0;
  private goname: string = "";
  private state: GO_STATE = GO_STATE.NULL;
  protected sections: Map<string, Section> = new Map<string, Section>();
  private waitTasks: Array<any> = new Array<any>();
  private backupTasks: Array<any> = new Array<any>();
  private objId: number = 0;
  private tag: string = "";
  private zPos: number = 0;

  public getDetail() {}

  public constructor(args = null) {
    super();
    this.objId = GameObject.count++;
    //  this._render.layer = Layers.Enum["UILayer"];
    this.layer = LayerEnum.UI;
    this.addComponent(UITransform);
    this.onInit(args);
  }

  protected onInit(args = null) {
    this.state = GO_STATE.INIT;
    this.createSection(NotifySection);
  }

  protected onStart() {
    this.state = GO_STATE.RUNNING;
    for (let i = 0; i < this.backupTasks.length; i++) {
      this.backupTasks[i].onStart();
    }
    this.backupTasks.length = 0;
  }
  protected onEnabled() {
    this.sections.forEach((val, key) => {
      val.setPause(false);
    });
  }
  protected onDisabled() {
    this.sections.forEach((val, key) => {
      val.setPause(true);
    });
  }
  protected onPause() {
    this.sections.forEach((val, key) => {
      val.onPause();
    });
  }
  protected onResume() {
    this.sections.forEach((val, key) => {
      val.onResume();
    });
  }
  protected onRemove() {
    this.sections.forEach((val, key) => {
      val.onRemove();
    });
    this.waitTasks.length = 0;
    this.backupTasks.length = 0;
    this.destroy();
  }
  protected updateSelf(dt) {}

  public tick(dt) {
    if (this.state == GO_STATE.INIT) {
      this.updateWaitTask(dt);
    } else if (this.state == GO_STATE.RUNNING) {
      this.updateSelf(dt);
      this.updateSection(dt);
    }
  }
  ///////////////////////////////
  public getObjId(): number {
    return this.objId;
  }
  public getState(): GO_STATE {
    return this.state;
  }
  public setName(name: string) {
    this.goname = name;
    this.name = name;
  }
  public getName(): string {
    return this.goname;
  }

  public setTag(tag: string) {
    this.tag = tag;
  }
  public getTag(): string {
    return this.tag;
  }
  public remove() {
    this.state = GO_STATE.REMOVE;
    this.onRemove();
  }
  ///////////////////////////////
  public setPause(isPause: boolean) {
    if (this.state == GO_STATE.INIT || this.state == GO_STATE.REMOVE) {
      return;
    }
    if (isPause && this.state != GO_STATE.PAUSE) {
      this.state = GO_STATE.PAUSE;
      this.onPause();
    }

    if (!isPause && this.state == GO_STATE.PAUSE) {
      this.state = GO_STATE.RUNNING;
      this.onResume();
    }
  }

  public enable() {
    if (this.state != GO_STATE.DISABLED) {
      return;
    }
    this.state = GO_STATE.RUNNING;
    this.onEnabled();
  }

  public disable() {
    if (this.state == GO_STATE.REMOVE) {
      return;
    }
    this.state = GO_STATE.DISABLED;
    this.onDisabled();
  }

  public setPosVec3(vec2: Vec2) {
    this.setPosition(v3(vec2.x, vec2.y, 1));
  }

  // public setPosVec2(vec2: Vec2) {
  //   // this.getPosition().x = vec2.x;
  //   // this.getPosition().y = vec2.y;
  // }

  public getPosVec2(): Vec2 {
    return new Vec2(this.getPosition().x, this.getPosition().y);
  }

  public getPosVec3(): Vec3 {
    return new Vec3(this.getPosition().x, this.getPosition().y, this.zPos);
  }

  public getZPos(): number {
    return this.zPos;
  }

  protected createOriginVisual(path) {
    ResMgr.loadPrefab(
      path,
      function (prefab) {
        let render = instantiate(prefab);
        render.walk((child) => (child.layer = LayerEnum.UI));
        this.addChild(render);
      }.bind(this)
    );
  }

  //////////////////////////////section

  public async createSection<T extends Section>(type: { new (): T }, args: any = null, self = this) {
    let name = type["sectionName"]();
    if (self.sections.has(name)) {
      return;
    } //重复
    //var section: any = new (<any>SectionSpace)[sectionName]();
    let section = new type();
    self.waitTasks.push(section);
    self.backupTasks.push(section);
    self.sections.set(name, section);
    section.setSub(self);
    await section.onInit(self, args);
    return section;
  }

  public removeAllSection() {
    this.sections.forEach((section, key) => {
      section.onRemove();
      this.sections.delete(key);
      let index = this.waitTasks.indexOf(section);
      if (index != -1) {
        this.waitTasks.splice(index, 1);
      }
      index = this.backupTasks.indexOf(section);
      if (index != -1) {
        this.backupTasks.splice(index, 1);
      }
    });
    this.sections.clear();
  }

  //删除模块
  public removeSection<T extends Section>(type: { new (): T }) {
    let name = type["sectionName"]();
    let section = this.sections.get(name);
    if (section) {
      section.onRemove();
      this.sections.delete(name);
      let index = this.waitTasks.indexOf(section);
      if (index != -1) {
        this.waitTasks.splice(index, 1);
      }
      index = this.backupTasks.indexOf(section);
      if (index != -1) {
        this.backupTasks.splice(index, 1);
      }
    }
  }

  //获取功能模块
  public getSection<T extends Section>(type: { new (): T }): T {
    let name = type["sectionName"]();
    let sec = this.sections.get(name);
    if (sec) {
      return this.sections.get(name) as T;
    } else {
      let tempSec = null;
      this.sections.forEach((val, key) => {
        if (val instanceof type) {
          tempSec = val;
          return;
        }
      });
      return tempSec;
    }
  }

  //
  public getSectionByName(name) {
    if (this.isValid == false) {
      return;
    }

    let sec = this.sections.get(name);
    if (sec) {
      return this.sections.get(name);
    }
  }

  private updateWaitTask(dt) {
    if (this.state != GO_STATE.INIT) {
      return;
    }
    for (let i = this.waitTasks.length - 1; i >= 0; i--) {
      let isReady = this.waitTasks[i].isReady();
      if (isReady) {
        this.waitTasks.splice(i, 1);
      }
    }
    if (this.waitTasks.length == 0) {
      //准备完成
      this.onStart();
    }
  }

  private updateSection(dt) {
    this.sections.forEach((val, key) => {
      if (!val.isPause()) {
        val.updateSelf(dt);
      }
    });
  }

  //////////////////////////////msg
  public onMsg(name: string, func) {
    if (this.getSection(NotifySection)) {
      this.getSection(NotifySection).onNotify(name, func);
    }
  }
  public offMsg(name: string, func) {
    if (this.getSection(NotifySection)) {
      this.getSection(NotifySection).offNotify(name, func);
    }
  }

  public emitMsg(name: string, args = null) {
    if (this.getSection(NotifySection)) {
      this.getSection(NotifySection).notify(name, args);
    }
  }
}
