import { _decorator, Component, instantiate, Label, log, Node } from "cc";
import { ListAdapter, ViewHolder } from "../platform/src/core/ui/adapter_view/ListAdapter";
import { JsonMgr } from "../GameScrpit/game/mgr/JsonMgr";
import FmUtils from "../GameScrpit/lib/utils/FmUtils";
import { TimeTaskModule } from "../GameScrpit/module/time_task/TimeTaskModule";
import { TimeTestModule } from "./config/TimeTestModule";
import GuideMgr from "../GameScrpit/ext_guide/GuideMgr";
import data from "../GameScrpit/lib/data/data";
import { ActivityTakeResponse } from "../GameScrpit/game/net/protocol/Activity";
import MsgMgr from "../GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../GameScrpit/game/event/MsgEnum";
import { CityModule } from "../GameScrpit/module/city/CityModule";
const { ccclass, property } = _decorator;

@ccclass("TimeTastViewHolder")
/**
 *
 */
export class TimeTastViewHolder extends ViewHolder {
  //任务Id
  private _taskId: number;
  //索引
  private _index: number;

  //任务类型id
  private _taskTypeId: number;
  //任务数据集合 Numder类型
  private _data: number[];
  //任务数据
  private _data2: number;

  updateData(data: number[], data2: number, taskId: number, taskTypeId: number) {
    this._taskId = taskId;
    this._taskTypeId = taskTypeId;
    this._data = data;
    this._data2 = data2;
    this._index = this.position;
    //通过任务类型id 获取配置表中的任务数据
    let task = JsonMgr.instance.jsonList.c_task[taskTypeId];

    //获取到任务数据之后 将数据赋值给文本信息 reqlace 字符串方法 用于把配置表中的s% 替换成特定的内容
    this.getNode("lab_diglog").getComponent(Label).string = task.des.replace("s%", data2 + "");
    //循环数据
    for (let i = 0; i < data.length && data.length > 1; i += 2) {
      let item = this.getNode("node_items").children[i / 2];
      if (!item) {
        //不存在就创建
        item = instantiate(this.getNode("node_items").children[0]);
        //添加到节点下
        this.getNode("node_items").addChild(item);
        //设置为true
        item.active = true;
      }
      //设置item 图标
      FmUtils.setItemNode(item, data[i], data[i + 1]);
    }

    // 判断当前任务是否完成
    let isComplete = false;
    let isGet = false;

    isComplete = TimeTestModule.data.timeTestMessage.completeMap[taskId].targetVal > data2;
    console.log("isComplete:", isComplete);
    isGet = TimeTaskModule.data.timeTaskMessage.completeMap[taskId].takeList.includes(this.position);
    console.log("isGet:", isGet);
    //设置任务状态
    this.getNode("btn_go").active = !isComplete; //未完成
    this.getNode("btn_get").active = isComplete && !isGet; //可领取
    this.getNode("btn_yilingqu").active = isComplete && isGet; //已领取
  }
  //点击前往引导
  onClickGo() {
    //跳转引导 获取跳转的类型
    let task = JsonMgr.instance.jsonList.c_task[this._taskTypeId];
    let args: any = { stepId: task.guideType };
    if (args.stepId == 24) {
      //获取消耗最小建筑号召
      let buildId = CityModule.service.findMinCostCallCityId();
      args.args = { buildId: buildId };
    }

    GuideMgr.startGuide(args);
  }

  //点击领取领取奖励物品
  onClickGet() {
    TimeTestModule.api.timeTaskTake(this._taskId, this.position, (data: ActivityTakeResponse) => {
      console.log("领取奖励成功，更新UI状态");
      this.updateData(this._data, this._data2, this._taskId, this._taskTypeId);
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList || [] });
    });
  }
}

export class TimeTestAdapter extends ListAdapter {
  private _item: Node;
  private _data: number[][] = [];
  private _data2: number[] = [];
  private _taskId: number;
  private _taskTypeId: number;

  constructor(item: Node) {
    super();
    this._item = item;
  }

  setData(data: number[][], data2: number[], taskId: number, taskTypeId: number) {
    this._taskTypeId = taskTypeId;
    this._taskId = taskId;
    this._data = data || [];
    this._data2 = data2 || [];
    this.notifyDataSetChanged();
  }

  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }

  onBindData(node: Node, position: number): void {
    const viewHolder = node.getComponent(TimeTastViewHolder);
    return viewHolder.updateData(this._data[position], this._data2[position], this._taskId, this._taskTypeId);
  }
  getCount(): number {
    return this._data.length;
  }
}
