import { _decorator, Color, Component, EventTouch, Label, math, Node, RichText, Sprite, Widget } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ToolExt from "../../common/ToolExt";
import { FriendModule } from "../../../module/friend/FriendModule";
import ResMgr from "../../../lib/common/ResMgr";
import { FriendBellesColorVerticalURL, FriendBellesColorVertical } from "../../../module/friend/FriendConstant";
import TipMgr from "../../../lib/tips/TipMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { IConfigFriednFame } from "../../JsonDefine";
import { divide } from "../../../lib/utils/NumbersUtils";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { FriendAudioName } from "../../../module/friend/FriendConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Mon Jun 24 2024 21:22:27 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/friend/UIFriendBelles.ts
 *
 */

@ccclass("UIFriendBelles")
export class UIFriendBelles extends UINode {
  protected _openAct: boolean = true;
  private _originalBkg: Sprite;
  private _originalBelles: Label;
  private _improveBkg: Sprite;
  private _improveBelles: Label;
  private _conditionValue1: Label;
  private _conditionValue2: Label;
  private _conditionValue3: Label;

  private _friendId: number;

  private _canUpgrade: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FRIEND}?prefab/ui/UIFriendBelles`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
    this._friendId = args.friendId;
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this._originalBelles = this.getNode("original_belles").getComponent(Label);
    this._originalBkg = this.getNode("original_bkg").getComponent(Sprite);
    this._improveBelles = this.getNode("improve_belles").getComponent(Label);
    this._improveBkg = this.getNode("improve_bkg").getComponent(Sprite);
    this._conditionValue1 = this.getNode("condition_1_value").getComponent(Label);
    this._conditionValue2 = this.getNode("condition_2_value").getComponent(Label);
    this._conditionValue3 = this.getNode("condition_3_value").getComponent(Label);
    this.refreshUI();
    // new Widget()
  }
  private refreshUI() {
    log.log("refreshUI", this._friendId);
    let friendMessage = FriendModule.data.getFriendMessage(this._friendId);
    let fame = FriendModule.config.getFriendBellesByFameLevel(friendMessage.fameLv);
    let NextFame = FriendModule.config.getFriendBellesByFameLevel(friendMessage.fameLv + 1);
    this._originalBelles.string = `${fame?.name}`;
    this._improveBelles.string = `${NextFame?.name || fame?.name}`;
    let canUp_1 = ToolExt.setOtherCost(this._conditionValue1, friendMessage.karma, NextFame?.levelUpNeed1);
    let canUp_2 = ToolExt.setOtherCost(this._conditionValue2, friendMessage.destiny, NextFame?.levelUpNeed2);
    log.log(friendMessage);
    let canUp_3 = ToolExt.setOtherCost(
      this._conditionValue3,
      FriendModule.data.getFriendIds(true).length,
      NextFame?.levelUpNeed3
    );
    this._canUpgrade = canUp_1 && canUp_2 && canUp_3;

    ResMgr.loadSpriteFrame(
      FriendBellesColorVerticalURL,
      FriendBellesColorVertical[`${friendMessage.fameLv}`],
      this._originalBkg,
      this
    );
    ResMgr.loadSpriteFrame(
      FriendBellesColorVerticalURL,
      FriendBellesColorVertical[`${friendMessage.fameLv + 1}`],
      this._improveBkg,
      this
    );
    this.refreshFame();
  }
  private findFameIndex(fameLv: number, fameEffect: IConfigFriednFame) {
    let index: number = -1;
    for (let i = 0; i < fameEffect.costList.length; i++) {
      if (fameEffect.costList[i] <= fameLv) {
        index = i;
      }
    }
    return index;
  }
  private refreshFame() {
    this.getNode(`lbl_fame_1`).active = false;
    this.getNode(`lbl_fame_2`).active = false;
    let fameList = FriendModule.config.getFriendById(this._friendId).fameIdList;
    let friendMessage = FriendModule.data.getFriendMessage(this._friendId);
    for (let i = 0; i < fameList.length && i < 2; i++) {
      let fameEffect = JsonMgr.instance.jsonList.c_friednFame[fameList[i]];
      let attr = JsonMgr.instance.jsonList.c_attribute[fameEffect.attrId];
      let index: number = this.findFameIndex(friendMessage.fameLv, fameEffect);
      if (index < 0) {
        // TODO
        this.getNode(`lbl_fame_${i + 1}`).getComponent(Label).color = math.color("9E9E9E");
        index = 0;
      } else {
        this.getNode(`lbl_fame_${i + 1}`).getComponent(Label).color = math.color("3973AE");
      }
      if (fameList[i] == 1008) {
        this.getNode(`lbl_fame_${i + 1}`).getComponent(Label).string = `${attr.name
          .replace("%s", `${divide(fameEffect.addList[index][0], 100)}%`)
          .replace("%s", `${divide(fameEffect.addList[index][1], 100)}%`)
          .replace("%s", `${divide(fameEffect.addList[index][2], 100)}%`)}`;
      } else {
        this.getNode(`lbl_fame_${i + 1}`).getComponent(Label).string = `${attr.name} +${fameEffect.addList[0][index]}`;
      }
      this.getNode(`lbl_fame_${i + 1}`).active = true;
    }
  }
  private on_click_upgrade() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    // TODO
    if (!this._canUpgrade) {
      TipMgr.showTip("晋升条件不足");
      return;
    }
    log.log("click upgrade", this._friendId);
    FriendModule.api.levelUpFame(this._friendId, (data) => {
      AudioMgr.instance.playEffect(FriendAudioName.Effect.晋升按钮成功);
      TipMgr.showTip("晋升成功");
      this.refreshUI();
    });
  }
  private formatFameEffect(fameId: number) {
    let fameEffect = JsonMgr.instance.jsonList.c_friednFame[fameId];
    let attr = JsonMgr.instance.jsonList.c_attribute[fameEffect.attrId];
    let strList = "";
    let friendMessage = FriendModule.data.getFriendMessage(this._friendId);
    let index: number = this.findFameIndex(friendMessage.fameLv, fameEffect);
    for (let i = 0; i < fameEffect.costList.length; i++) {
      let fameName = FriendModule.config.getFriendBellesByFameLevel(fameEffect.costList[i]);
      if (index >= i) {
        if (fameId == 1008 && i < fameEffect.addList.length) {
          strList += `(${fameName.name})${attr.name
            .replace("+%s", `<color=#ff8f3c>+${divide(fameEffect.addList[i][0], 100)}%</color>`)
            .replace("+%s", `<color=#ff8f3c>+${divide(fameEffect.addList[i][1], 100)}%</color>`)
            .replace("+%s", `<color=#ff8f3c>+${divide(fameEffect.addList[i][2], 100)}%</color>`)}<br/>`;
        } else if (fameId != 1008) {
          strList += `(${fameName.name}) ${attr.name}<color=#ff8f3c> +${fameEffect.addList[0][i]}</color><br/>`;
        }
      } else {
        if (fameId == 1008 && i < fameEffect.addList.length) {
          strList += `<color=#9E9E9E>(${fameName.name})${attr.name
            .replace("+%s", `+${divide(fameEffect.addList[i][0], 100)}%`)
            .replace("+%s", `+${divide(fameEffect.addList[i][1], 100)}%`)
            .replace("+%s", `+${divide(fameEffect.addList[i][2], 100)}%`)}</color><br/>`;
        } else if (fameId != 1008) {
          strList += `<color=#9E9E9E>(${fameName.name}) ${attr.name} +${fameEffect.addList[0][i]}</color><br/>`;
        }
      }
    }
    log.log(strList);
    return strList.slice(0, -5);
  }
  private on_click_btn_fame_1(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let fameList = FriendModule.config.getFriendById(this._friendId).fameIdList;
    let formatStr = this.formatFameEffect(fameList[0]);
    this.getNode("lbl_fame_detail").getComponent(RichText).string = formatStr;
    this.getNode("node_fame_list").active = true;
  }
  private on_click_btn_fame_2(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let fameList = FriendModule.config.getFriendById(this._friendId).fameIdList;
    let formatStr = this.formatFameEffect(fameList[1]);
    this.getNode("lbl_fame_detail").getComponent(RichText).string = formatStr;
    this.getNode("node_fame_list").active = true;
  }

  private on_click_node_fame_list() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("node_fame_list").active = false;
  }
}
