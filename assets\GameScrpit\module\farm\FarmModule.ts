import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { FarmApi } from "./FarmApi";
import { FarmData } from "./FarmData";
import { FarmRoute } from "./FarmRoute";
import { FarmService } from "./FarmService";
import { FarmSubScriber } from "./FarmSubScriber";

export class FarmModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): FarmModule {
    if (!GameData.instance.FarmModule) {
      GameData.instance.FarmModule = new FarmModule();
    }
    return GameData.instance.FarmModule;
  }
  private _data = new FarmData();
  private _api = new FarmApi();
  private _service = new FarmService();
  private _subscriber = new FarmSubScriber();
  private _route = new FarmRoute();
  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new FarmData();
    this._api = new FarmApi();
    this._service = new FarmService();
    this._subscriber = new FarmSubScriber();
    this._route = new FarmRoute();

    FarmModule.api.getFarmInfo((data) => {
      completedCallback && completedCallback();
      this._route.init();
      this._subscriber.register();
    });
  }
}
