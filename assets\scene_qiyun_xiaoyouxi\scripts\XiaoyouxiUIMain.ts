import {
  _decorator,
  Camera,
  Color,
  instantiate,
  Label,
  Node,
  ProgressBar,
  sp,
  tween,
  UIOpacity,
  UITransform,
  v3,
  Vec3,
} from "cc";
import { IUIModule, UIState } from "../interface/IUIModule";
import Tool from "../../GameScrpit/lib/common/Tool";
import { QiyunXiaoyouxiData } from "../QiyunXiaoyouxiData";
import Formate from "../../GameScrpit/lib/utils/Formate";
import MsgMgr from "../../GameScrpit/lib/event/MsgMgr";
import { QiyunXiaoyouxiEvent } from "../QiyunXiaoyouxiEvent";
import ToolExt from "../../GameScrpit/game/common/ToolExt";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { PoolMgr } from "../../platform/src/PoolHelper";
import { Sleep } from "../../GameScrpit/game/GameDefine";
import { AudioMgr } from "../../platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../GameScrpit/lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("XiaoyouxiUIMain")
export class XiaoyouxiUIMain extends BaseCtrl implements IUIModule {
  @property(Node)
  ui_layer: Node = null;

  @property(Node)
  level_aim_ui: Node = null;

  @property(sp.Skeleton)
  skt: sp.Skeleton;

  /** 不自动更新层级 */
  protected autoSetLayer: boolean = false;

  private _callback: (state: UIState) => void = null;

  // 玩家结点，用于完成任务气运飞行动画
  private _nodePlayer: Node = null;

  // 女娲点击特效池
  poolSpineNwDianJiTxA2: PoolMgr<Node>;

  // 点击发光效果
  nodeSpineNwDianJiTxA1: Node;

  // 点击掉落效果，放入池子管理
  nodeSpineNwDianJiTxA2: Node;

  // 冲的节点
  public nodeBtnGo: Node;

  // 冲的展示动画
  spineBtnGo: sp.Skeleton;

  // 消耗信息节点
  nodeNeedItemLayer: Node;

  // 消耗UI节点展示动画
  spineNeedItem: sp.Skeleton;

  // 消耗信息
  lblNeedItem: Label;

  // 游戏信息节点
  nodeArea1: Node;

  // 游戏信息展示动画
  spineArea1: sp.Skeleton;

  // 进度条节点
  nodeLevelProgress: Node;

  // 当前气运节点
  nodeCurQiYun: Node;

  // 当前气运
  lblCurQiyun: Label;

  // 女娲
  public nodeBtnStatuary: Node;

  // 任务
  public nodeBtnTask: Node;

  // 任务展示动画
  spineBtnTask: sp.Skeleton;

  // 气运池
  poolQiYun: PoolMgr<Node>;

  // 数字变化池
  poolNum: PoolMgr<Node>;

  // 数字变化
  lblCostNum: Label;

  // 手指节点
  nodeBtnFinger: Node;

  // 可点击节点名称
  toClickName: string;

  // 手指动画结束
  aniFingerEnd: boolean = true;

  // 任务气运 -1 代表清除状态， 其他代表可用
  qiYunClear: number = 0;

  onShowUI(isShow: boolean) {
    let target_pop = this.node.getChildByName("target_pop");
    target_pop.setScale(v3(0, 0, 0));
    target_pop.active = true;

    tween(target_pop)
      .to(0.3, { scale: v3(1, 1, 1) })
      .start();
  }

  onShowNvwa(isShow: boolean) {
    this.onShowTask(false);
    this.nodeBtnStatuary.active = true;

    this.toClickName = "btn_statuary";

    this.setTask(QiyunXiaoyouxiData.instance.currentQiyun);
  }

  // 冲撞显隐
  async onShowGo(isShow: boolean) {
    if (isShow) {
      this.toClickName = "btn_go";

      if (this.nodeBtnGo.active) {
        return;
      }

      this.onShowGameInfo(isShow);
      await Sleep(0.3);

      this.spineBtnGo.node.active = true;
      this.spineBtnGo.setAnimation(0, "action", false);
      setTimeout(() => {
        this.nodeBtnGo.active = true;
      }, 0.3);

      this.onShowItemNeed(isShow);
      await Sleep(0.3);
    } else {
      this.spineBtnGo.node.active = false;
      this.nodeBtnGo.active = false;
    }
  }

  // 消耗信息显隐
  onShowItemNeed(isShow: boolean) {
    return;

    if (isShow) {
      if (this.nodeNeedItemLayer.active) {
        return;
      }
      this.spineNeedItem.node.active = true;
      this.spineNeedItem.setAnimation(0, "action", false);
      setTimeout(() => {
        this.nodeNeedItemLayer.active = true;
      }, 0.3);
    } else {
      this.spineNeedItem.node.active = false;
      this.nodeNeedItemLayer.active = false;
    }
  }

  // 任务显隐
  onShowTask(isShow: boolean) {
    this.setTask(QiyunXiaoyouxiData.instance.currentQiyun);

    if (isShow) {
      this.qiYunClear = 0;
      if (this.nodeBtnTask.active) {
        return;
      }

      this.onShowItemNeed(false);

      this.spineBtnTask.node.active = false;

      this.nodeBtnTask.active = true;
      const uiOpacity = this.nodeBtnTask.getComponent(UIOpacity);
      uiOpacity.opacity = 0;
      tween(uiOpacity).to(0.6, { opacity: 255 }).start();

      // this.spineBtnTask.node.active = true;
      // this.spineBtnTask.setAnimation(0, "action", false);
      // setTimeout(() => {
      //   this.nodeBtnTask.active = true;
      // }, 0.3);
      // this.onShowItemNeed(false);
    }
  }

  // 当前气运显隐
  onShowGameInfo(isShow: boolean) {
    if (isShow) {
      if (this.nodeArea1.active) {
        return;
      }
      this.nodeArea1.active = true;
      this.spineArea1.node.active = true;
      this.spineArea1.setAnimation(0, "action", false);
      setTimeout(() => {
        this.nodeArea1.active = true;
      }, 0.3);
    } else {
      this.spineArea1.node.active = false;
      this.nodeArea1.active = false;
    }
  }

  onStart(callback: (state: UIState) => void, nodePlayer: Node): void {
    this._nodePlayer = nodePlayer;
    log.log("UI界面的onStart");
    this._callback = callback;
  }

  protected onLoad(): void {
    super.onLoad();
    MsgMgr.on(QiyunXiaoyouxiEvent.QIYUN_ADD, this.upCurrentQiyun, this);
    MsgMgr.on(QiyunXiaoyouxiEvent.QIYUN_CONSOME, this.setConsumeQiyun, this);

    this.ui_layer.children.forEach((node) => {
      node.active = false;
    });

    let dotLay = this.getNode("dotLay");
    dotLay.children.forEach((val) => {
      val.getChildByName("ok").active = false;
    });

    this.nodeSpineNwDianJiTxA1 = this.getNode("spine_nw_dianjitx_a1");
    this.nodeSpineNwDianJiTxA2 = this.getNode("spine_nw_dianjitx_a2");

    this.poolSpineNwDianJiTxA2 = new PoolMgr(async () => {
      return instantiate(this.nodeSpineNwDianJiTxA2);
    }, 20);
  }

  start() {
    super.start();

    this.nodeBtnGo = this.getNode("btn_go");
    this.spineBtnGo = this.getNode("node_ui_show2").getComponentInChildren(sp.Skeleton);

    this.nodeNeedItemLayer = this.getNode("need_item_layer");
    this.spineNeedItem = this.nodeNeedItemLayer.getComponentInChildren(sp.Skeleton);

    this.nodeArea1 = this.getNode("node_area1");
    this.spineArea1 = this.nodeArea1.getComponentInChildren(sp.Skeleton);
    this.nodeLevelProgress = this.nodeArea1.getChildByName("levelProgress");
    this.nodeCurQiYun = this.nodeArea1.getChildByName("node_cur_qi_yun");
    this.lblCurQiyun = this.nodeCurQiYun.getChildByName("lbl_cur_qiyun").getComponent(Label);

    this.lblNeedItem = this.nodeNeedItemLayer.getChildByName("lbl_need_qiyun").getComponent(Label);

    this.nodeBtnStatuary = this.getNode("btn_statuary");

    this.nodeBtnTask = this.getNode("btn_task");
    this.spineBtnTask = this.nodeBtnTask.getComponentInChildren(sp.Skeleton);

    this.poolQiYun = new PoolMgr(async () => {
      return instantiate(this.nodeCurQiYun.getChildByName("bg_qi_yun"));
    }, 250);

    this.lblCostNum = this.getNode("lbl_cost_num").getComponent(Label);

    this.nodeBtnFinger = this.getNode("btn_finger");
    this.nodeBtnFinger.active = false;

    this.poolNum = new PoolMgr(async () => {
      return instantiate(this.lblCostNum.node);
    }, 250);
  }

  protected onDestroy(): void {
    super.onDestroy();

    MsgMgr.off(QiyunXiaoyouxiEvent.QIYUN_ADD, this.upCurrentQiyun, this);
    MsgMgr.off(QiyunXiaoyouxiEvent.QIYUN_CONSOME, this.setConsumeQiyun, this);
  }

  /**消耗的气运显示设置 */
  private setConsumeQiyun(needNum: number) {
    this.numCost(needNum);
    this.upCurrentQiyun();
  }

  /**设置关卡进度条 */
  private setLevelProGress(isAct: boolean = false) {
    QiyunXiaoyouxiData.instance;

    let dotLay = this.nodeLevelProgress.getChildByPath("dotLay");

    let curIndex = QiyunXiaoyouxiData.instance.getCurrentTaskIndex() + 1;
    let maxBar = QiyunXiaoyouxiData.instance.getTask().length;
    let bar = curIndex / maxBar;
    if (isAct == true) {
      tween(this.nodeLevelProgress.getComponent(ProgressBar))
        .to(0.5, { progress: bar })
        .call(() => {
          dotLay.children[curIndex - 1].getChildByName("ok").active = true;
        })
        .start();
    } else {
      this.nodeLevelProgress.getComponent(ProgressBar).progress = bar;
      for (let i = 0; i < dotLay.children.length && i < curIndex; i++) {
        dotLay.children[i].getChildByName("ok").active = true;
      }
    }
  }

  /**设置任务，任务需要监听气运的事件，看是否达到了要求 */
  private setTask(value: number) {
    if (this.qiYunClear == -1) {
      return;
    }

    this.qiYunClear = value;

    if (value < 0) {
      value = 0;
    }

    let taskdb = QiyunXiaoyouxiData.instance.getCurrentTask();

    let lbl_task_finish = this.nodeBtnTask.getChildByName("lbl_task_finish");
    lbl_task_finish.getComponent(Label).string = taskdb.des;
    lbl_task_finish.getComponent(Label).string = lbl_task_finish
      .getComponent(Label)
      .string.replace("s%", String(taskdb.doneNum));
    lbl_task_finish.getComponent(Label).string += `\n(${value}/${taskdb.doneNum})`;

    let lbl_task = this.nodeBtnTask.getChildByName("lbl_task");
    lbl_task.getComponent(Label).string = lbl_task_finish.getComponent(Label).string;

    //QiyunXiaoyouxiData.instance.currentQiyun
    if (value >= taskdb.doneNum) {
      lbl_task.active = false;
      lbl_task_finish.active = true;
      this.getNode("ps_starlizi").active = true;
      this.toClickName = "btn_task";

      this.showFinger(this.nodeBtnTask.worldPosition, 0);
    } else {
      lbl_task.active = true;
      lbl_task_finish.active = false;
      this.getNode("ps_starlizi").active = false;
    }
  }

  /**弹窗按钮事件，弹窗缩小，移动到指定位置 */
  private async on_click_btn_pop_next_msg() {
    this.level_aim_ui.active = true;

    let target_pop = this.node.getChildByName("target_pop");
    let bg = target_pop.getChildByName("bg");
    let move_pop = target_pop.getChildByName("move_pop");

    await new Promise(async (resolve) => {
      tween(bg)
        .to(0.3, { scale: v3(0, 0, 0) })
        .call(() => {
          resolve(true);
        })
        .start();
    });

    bg.active = false;
    move_pop.active = true;

    await new Promise(async (resolve) => {
      let newPos = ToolExt.transferOfAxes(this.level_aim_ui, target_pop);
      tween(move_pop)
        .to(0.8, { position: newPos })
        .call(async () => {
          this.onShowGo(true);

          await Sleep(1);
          // 移动手指
          this.showFinger(this.nodeBtnGo.worldPosition, 1);
          resolve(true);
        })
        .start();
    });

    this.level_aim_ui.getComponent(UIOpacity).opacity = 255;
    target_pop.active = false;
    this.upCurrentQiyun();
  }

  /**设置自己的气运数量 */
  private upCurrentQiyun() {
    let str = Formate.format(QiyunXiaoyouxiData.instance.currentQiyun);
    this.lblCurQiyun.getComponent(Label).string = str;
    // this.setTask();
  }

  /**按钮冲的点击事件 */
  private async on_btn_go() {
    if (this.toClickName != "btn_go") {
      return;
    }

    this._callback(UIState.GO_RUN);
    this.onShowGo(false);
    this.setLevelProGress(true);

    this.hideFinger();
  }

  /**任务栏得的点击事件 */
  private async on_btn_task() {
    if (this.toClickName != "btn_task") {
      return;
    }

    this.hideFinger();
    this.setTask(-1);
    this.toClickName = "btn_go";

    let taskdb = QiyunXiaoyouxiData.instance.getCurrentTask();
    if (QiyunXiaoyouxiData.instance.currentQiyun >= taskdb.doneNum) {
      TipsMgr.setEnableTouch(false, 3, false);
      await this.playQiYunUse();
      await Sleep(0.4);
      this._callback(UIState.UPGRADE);
    }
  }

  /**
   * 任务完成气运飞行动画
   */
  private async playQiYunUse() {
    AudioMgr.instance.playEffect(1667);

    const camera = this._nodePlayer.getComponentInChildren(Camera);
    const worldPos = this._nodePlayer.getWorldPosition();
    // 玩家相对相机坐标
    const endPos = worldPos.subtract(camera.node.getWorldPosition());

    // 结束位置
    const endPos2 = endPos.subtract(this.nodeBtnTask.getPosition());
    endPos2.z = 0;

    // 终点
    endPos2.add(v3(0, 150, 0));

    // 起点
    const startPos = v3(0, 0, 0);

    // 中间点
    const midPos1 = v3(startPos.x - 100, startPos.y + 100, 0);

    // 中间点
    // const midPos2 = v3(startPos.x - 100, startPos.y + 100, 0);

    for (let i = 0; i < 16; i++) {
      await Sleep(0.05);
      let nodeQiYun = await this.poolQiYun.getOne();
      nodeQiYun.active = true;
      nodeQiYun.setParent(this.nodeBtnTask);
      nodeQiYun.setPosition(0, 0, 0);

      tween();
      // 随机大小
      let randomScale = Math.random() * 1 + 0.5;
      nodeQiYun.setScale(v3(randomScale, randomScale, 1));

      // 随机角度
      let randomAngle = Math.random() * 360;
      nodeQiYun.setRotationFromEuler(0, 0, randomAngle);

      tween(nodeQiYun)
        .to(
          0.3,
          { position: endPos2 },
          {
            onUpdate: (target, ratio) => {
              Tool.bezierCurve(ratio, startPos, midPos1, midPos1, endPos2, nodeQiYun.position);
            },
          }
        )
        .call(() => {
          nodeQiYun.active = false;
          this.poolQiYun.recycle(nodeQiYun);
        })
        .start();
    }
  }

  /**女娲的点击事件 */
  private on_btn_statuary(event) {
    if (this.toClickName != "btn_statuary") {
      return;
    }

    let node: Node = event.target;
    let worldPosition = node.getWorldPosition();
    this.flyEnergyFun(worldPosition);
    this.setTask(QiyunXiaoyouxiData.instance.currentQiyun);
  }

  /**飞行气运 */
  private async flyEnergyFun(energyPosition: Vec3) {
    // 开始位置
    const startPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(energyPosition);

    // 结束位置
    let energyEnd = this.nodeCurQiYun.getChildByName("bg_qi_yun");

    const endPos = FmUtils.transferOfAxes(energyEnd, this.node);
    // 随机数量
    const nCount = 2 + Math.random() * 3;

    let energyItem = this.getNode("energyItem");

    let time1 = 0.75;
    let time2 = 0.3;
    // 逐个添加
    for (let i = 0; i < nCount; i++) {
      // 克隆节点
      let node = instantiate(energyItem);
      this.node.addChild(node);
      node.setPosition(startPos);
      node.active = true;

      // 生成中间停顿点
      const midPos = Tool.createRandomPos(startPos, {
        area: new Vec3(600, 300, 0),
        space: new Vec3(100, 0, 0),
        offset: new Vec3(0, -0.0, 0),
      });
      const midPos2 = Tool.createRandomPos(startPos, {
        area: new Vec3(400, 300, 0),
        space: new Vec3(0, 0, 0),
        offset: new Vec3(0, 1, 0),
      });

      const tempVec3 = v3();

      // 开始运动
      tween(node)
        .to(
          time1,
          { position: endPos },
          {
            onUpdate: (target, ratio) => {
              Tool.bezierCurve(ratio, startPos, midPos, midPos2, endPos, tempVec3);
              node.setPosition(tempVec3);
            },
          }
        )
        .delay(time2)
        .call(() => {
          node.destroy();
        })
        .start();
    }
    let aniPre = "a06";
    this.skt.setAnimation(0, aniPre + "-2");
    this.skt.setCompleteListener(() => {
      this.skt.setCompleteListener(null);
      this.skt.setAnimation(0, aniPre + "-1");
    });

    let nodeA2 = await this.poolSpineNwDianJiTxA2.getOne();
    nodeA2.active = true;
    this.skt.node.addChild(nodeA2);

    const spineA2 = nodeA2.getComponent(sp.Skeleton);
    spineA2.setAnimation(0, "animation2", false);
    spineA2.setCompleteListener(() => {
      spineA2.setCompleteListener(null);

      this.poolSpineNwDianJiTxA2.recycle(nodeA2);
      nodeA2.active = false;
    });

    QiyunXiaoyouxiData.instance.addQiyun();
  }

  private numAdd(n: number) {
    this.lblCostNum.node.active = true;
    this.lblCostNum.color = Color.GREEN;
    this.lblCostNum.string = String(n);
  }

  private async numCost(n: number) {
    let nodeLblCostNum = await this.poolNum.getOne();
    if (nodeLblCostNum.active == false) {
      nodeLblCostNum.active = true;
      nodeLblCostNum.setParent(this.getNode("node_cost_num"));
      nodeLblCostNum.setPosition(0, 0);
    }
    let lbl = nodeLblCostNum.getComponent(Label);
    lbl.color = Color.RED;
    lbl.string = String(-n);

    tween(nodeLblCostNum)
      .to(0.5, { position: v3(0, 50, 0) }, { easing: "sineInOut" })
      .delay(0.1)
      .call(() => {
        nodeLblCostNum.active = false;
        this.poolNum.recycle(nodeLblCostNum);
      })
      .start();
  }

  public showFinger(toWorld: Vec3, from: number) {
    if (!this.aniFingerEnd) {
      return;
    }

    this.aniFingerEnd = false;
    if (from == 2) {
      this.nodeBtnFinger.setPosition(v3(312, 820, 0));
    } else if (from == 1) {
      this.nodeBtnFinger.setPosition(v3(-350, 820, 0));
    }

    this.nodeBtnFinger.active = true;
    const toPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(toWorld);
    // let distance = Vec3.distance(from, toPos); //toPos.subtract(this.nodeBtnFinger.position).length();
    // let time = distance / 1000;
    tween(this.nodeBtnFinger).to(0.6, { position: toPos }, { easing: "sineOut" }).start();
    setTimeout(() => {
      this.aniFingerEnd = true;
    }, 0.6);
  }

  public hideFinger() {
    this.nodeBtnFinger.active = false;
  }
}
