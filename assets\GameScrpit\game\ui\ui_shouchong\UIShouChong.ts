import { Label, Node, UIOpacity, _decorator, instantiate, sp, Animation, Sprite, math } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ItemCtrl } from "../../common/ItemCtrl";
import TipMgr from "../../../lib/tips/TipMgr";
import GameHttpApi from "../../httpNet/GameHttpApi";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { BannerAdapter } from "./BannerAdapter";
import { HeroModule } from "../../../module/hero/HeroModule";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { FirstRechargeSignResponse } from "../../net/protocol/Activity";
import { HdShouChongModule } from "../../../module/hd_shouchong/HdShouChongModule";
import { GoodsRouteName } from "../../../module/goods/GoodsRoute";
import { JsonMgr } from "../../mgr/JsonMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HdShouChongAudioName } from "../../../module/hd_shouchong/HdShouChongConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import GuideMgr from "../../../ext_guide/GuideMgr";
import { ActivityModule } from "../../../module/activity/ActivityModule";
import { BannerLayoutManager } from "db://assets/platform/src/core/ui/adapter_view/layout_manager/BannerLayoutManager";

const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Mon Oct 21 2024 14:33:48 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_shouchong/UIShouChong.ts
 *
 */

const ACTIVITY_ID = 10101;
@ccclass("UIShouChong")
export class UIShouChong extends UINode {
  protected _openAct: boolean = true;
  // @persistent
  private _shouchong_activity_info: any = ActivityModule.data.allActivityConfig[ACTIVITY_ID];
  private _layoutManager: BannerLayoutManager = new BannerLayoutManager();
  private _adapter: BannerAdapter;
  private _tabDay6Index = 0;
  private _tabDay30Index = 0;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_SHOUCHONG}?prefab/ui/UIShouChong`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    HdShouChongModule.api.firstRecharge((data) => {
      log.log("data", data);
      this.refresh6UI();
    });
    this.getNode("node_page").addComponent(BannerLayoutManager);
    this._layoutManager = this.getNode("node_page").getComponent(BannerLayoutManager);
    this._layoutManager.onPageScroll = this.onPageScrollListener.bind(this);
    this._adapter = new BannerAdapter(this.getNode("page_viewholder"), this._layoutManager);
    this.getNode("node_page").getComponent(AdapterView).setAdapter(this._adapter);
    this._adapter.initDatas();
    for (let i = 0; i < 9; i++) {
      this.getNode(`tab_${i}`).on(
        Node.EventType.TOUCH_END,
        () => {
          for (let j in [0, 1, 2, 3, 4, 5, 6, 7, 8]) {
            this.getNode(`tab_${j}`).getChildByName("bg_select").getComponent(UIOpacity).opacity = 0;
          }
          this._layoutManager.foucsPosition = i;
        },
        this
      );
    }

    this.getNode("shouchong2").setPosition(0, 43);
    this.getNode("shouchong1").setPosition(0, 43);
    this.getNode("shouchong2").active = false;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_ACTIVITY_FIRST_RECHARGE, this.onFirstChargeCallback, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_ACTIVITY_FIRST_RECHARGE, this.onFirstChargeCallback, this);
  }

  private onFirstChargeCallback() {
    TipMgr.showTip("支付成功");
    this.refresh6UI();
    this.refresh30UI();
  }

  // 更新道具奖励内容
  private refreshItemLayout(stateList: number[], chargeId: number) {
    let nodeTag = 6;
    let tabIndex = 0;
    if (chargeId == 1010110) {
      nodeTag = 6;
      tabIndex = this._tabDay6Index;
    } else {
      nodeTag = 30;
      tabIndex = this._tabDay30Index;
    }
    let rechargePlanList = null;
    for (let i = 0; i < this._shouchong_activity_info.subRechargeVOList.length; i++) {
      if (`${chargeId}` == this._shouchong_activity_info.subRechargeVOList[i].id) {
        rechargePlanList = this._shouchong_activity_info.subRechargeVOList[i].planVOList;
      }
    }
    for (let j = 0; j < rechargePlanList[tabIndex].rewardList.length; j += 2) {
      //stateList
      let item = this.getNode(`node_item_${nodeTag}_layout`).children[Math.floor(j / 2)];
      if (!item) {
        item = instantiate(this.getNode(`node_item_${nodeTag}_layout`).children[0]);
        this.getNode(`node_item_${nodeTag}_layout`).addChild(item);
      }
      item.active = true;

      let itemCtrl = item.getChildByName("Item").getComponent(ItemCtrl);
      itemCtrl.setItemId(rechargePlanList[tabIndex].rewardList[j], rechargePlanList[tabIndex].rewardList[j + 1]);
      item.getChildByName("icon_duihao").active = false;
      item.getChildByName("icon_suo").active = false;
      let btn_lingqu = this.getNode(`node_day_${nodeTag}_${tabIndex}`).getChildByPath(
        `node_select/btn_lingqu_${nodeTag}_${tabIndex}`
      );
      btn_lingqu.active = false;
      item.getComponent(Animation).stop();
      let rotation = item.getRotation();
      rotation.z = 0;
      item.setRotation(rotation);
      if (!stateList) {
        // 显示未解锁
        item.getChildByName("icon_suo").active = true;
        item
          .getChildByName("Item")
          .getComponentsInChildren(Sprite)
          .forEach((item) => {
            item.color = math.color("#6E6E6E");
          });
      } else if (stateList[tabIndex] == -1) {
        item
          .getChildByName("Item")
          .getComponentsInChildren(Sprite)
          .forEach((item) => {
            item.color = math.color("#6E6E6E");
          });
        item.getChildByName("icon_suo").active = true;
      } else if (stateList[tabIndex] == 0) {
        btn_lingqu.active = true;
        item.getComponent(Animation).play();
        item
          .getChildByName("Item")
          .getComponentsInChildren(Sprite)
          .forEach((item) => {
            item.color = math.color("#FFFFFF");
          });
      } else if (stateList[tabIndex] == 1) {
        item
          .getChildByName("Item")
          .getComponentsInChildren(Sprite)
          .forEach((item) => {
            item.color = math.color("#6E6E6E");
          });
        item.getChildByName("icon_duihao").active = true;
      }
      // item.getComponent(Animation).play();
    }
  }
  private refresh6UI() {
    this.getNode("bg_yichongzhi_6").active = false;
    this.getNode("btn_buy_6").active = false;
    this.getNode("node_switch").active = false;

    if (Object.keys(HdShouChongModule.data.firstRechargeMessage).length == 0) {
      //没充值
      this.refreshItemLayout(null, 1010110);
      this.getNode("btn_buy_6").active = true;
    } else if (Object.keys(HdShouChongModule.data.firstRechargeMessage).length > 0) {
      let stateList: number[] = HdShouChongModule.data.firstRechargeMessage["1010110"].stateList;
      if (stateList.every((v) => v == 1)) {
        //隐藏6元
        this.getNode("node_switch").active = false;

        this.getNode("shouchong2").active = true;
        this.getNode("shouchong1").active = false;
        this.refresh30UI();
      } else {
        this.getNode("node_switch").active = true;
        this.getNode("bg_yichongzhi_6").active = true;
        this.refreshItemLayout(stateList, 1010110);
      }
    }

    // 引导判断
    if (this.getNode("btn_lingqu_6_0").active) {
      GuideMgr.startGuide({ stepId: 79, isForce: true });
      GuideMgr.unshiftGuide({ stepId: 80, isForce: true });
    }
  }
  private refresh30UI() {
    this.getNode("btn_buy_30").active = false;
    this.getNode("bg_yichongzhi_30").active = false;
    if (Object.keys(HdShouChongModule.data.firstRechargeMessage).length < 2) {
      this.refreshItemLayout(null, 1010120);
      this.getNode("btn_buy_30").active = true;
    } else if (Object.keys(HdShouChongModule.data.firstRechargeMessage).length > 1) {
      this.getNode("bg_yichongzhi_30").active = true;
      let stateList: number[] = HdShouChongModule.data.firstRechargeMessage["1010120"].stateList;
      this.refreshItemLayout(stateList, 1010120);
    }
  }
  private onPageScrollListener(node: Node, position: number, currentPos: number, positionOffset: number): void {
    // let scale = 0.6 + (1 - Math.abs(positionOffset)) * 0.4;
    // node.getChildByName("hero_img").setScale(scale, scale);
    for (let j in [0, 1, 2, 3, 4, 5, 6, 7, 8]) {
      this.getNode(`tab_${j}`).getChildByName("bg_select").getComponent(UIOpacity).opacity = 0;
    }
    this.getNode(`tab_${position}`).getChildByName("bg_select").getComponent(UIOpacity).opacity =
      255 * (1 - Math.abs(positionOffset));
    if (position == currentPos) {
      //pingzhi
      this.getNode("hero_name").getComponent(UIOpacity).opacity = (1 - Math.abs(positionOffset)) * 255;
      let heroId: number = this._adapter.getItem(position);
      let heroInfo = HeroModule.config.getHeroInfo(heroId);
      this.getNode("hero_name").getComponent(Label).string = `${heroInfo.name}`;
    }
    if (!node.getComponentInChildren(sp.Skeleton)) {
      return;
    }
    if (position == currentPos) {
      node.getComponentInChildren(sp.Skeleton).paused = false;
    } else {
      node.getComponentInChildren(sp.Skeleton).paused = true;
    }
  }
  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_btn_close_2() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_btn_arrow_right() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let scaleX = this.getNode("bg_point").scale.x * -1;
    this.getNode("bg_point").setScale(scaleX, 1);
    if (scaleX > 0) {
      this.refresh30UI();
      this.getNode("shouchong2").active = true;
      this.getNode("shouchong1").active = false;
    } else {
      this.refresh6UI();
      this.getNode("shouchong2").active = false;
      this.getNode("shouchong1").active = true;
    }
  }
  private on_click_btn_arrow_left() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let scaleX = this.getNode("bg_point").scale.x * -1;
    this.getNode("bg_point").setScale(scaleX, 1);
    if (scaleX > 0) {
      this.refresh30UI();
      this.getNode("shouchong2").active = true;
      this.getNode("shouchong1").active = false;
    } else {
      this.refresh6UI();
      this.getNode("shouchong2").active = false;
      this.getNode("shouchong1").active = true;
    }
  }
  private on_click_btn_zhandouyulan() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击战斗预览按钮);
    // TipMgr.showTip("战斗预览");
    // 首充一档奖励固定为大侠
    let skinId = JsonMgr.instance.jsonList.c_hero[10401].skinId;
    UIMgr.instance.showDialog(PlayerRouteName.UIWatchSkill, { roleId: skinId });
  }
  private on_click_btn_zhandouyulan_2() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击战斗预览按钮);
    let roleId = this._adapter.getItem(this._layoutManager.foucsPosition);
    let skinId = JsonMgr.instance.jsonList.c_hero[roleId].skinId;
    UIMgr.instance.showDialog(PlayerRouteName.UIWatchSkill, { roleId: skinId });
  }

  // 抖动检测
  // private checkFlutterState(aniParent: Node, group: number, index: number) {
  //   let keys = Object.keys(HdShouChongModule.data.firstRechargeMessage);
  //   let stateList: number[] = HdShouChongModule.data.firstRechargeMessage[keys[group]]?.stateList;
  //   if (stateList?.[index] == 0) {
  //     // this.getNode(`node_day_6_${i}`).getChildByPath(`node_select/Scroll/btn_lingqu_6_0`).active = true;
  //     for (let i = 0; i < aniParent.children.length; i++) {
  //       let item = aniParent.children[i];
  //       item.getComponent(Animation).play();
  //     }
  //   }
  // }

  private on_click_btn_buy_6() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    // TipMgr.showTip("购买");
    let goodsId = this._shouchong_activity_info.subRechargeVOList[0].id;
    let price = this._shouchong_activity_info.subRechargeVOList[0].price - 10000;
    GameHttpApi.pay({
      goodsId: goodsId,
      goodsType: 3,
      count: 1,
      playerId: PlayerModule.data.playerId,
      orderAmount: price,
      goodsName: "首充",
      platformType: "TEST",
    }).then((resp: any) => {
      log.log("pay resp");
      if (resp.code != 200) {
        log.error(resp);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url }, () => {
        TipsMgr.setEnableTouch(false, 7);
      });
    });
  }

  private on_click_btn_buy_30() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let goodsId = this._shouchong_activity_info.subRechargeVOList[1].id;
    let price = this._shouchong_activity_info.subRechargeVOList[1].price - 10000;
    GameHttpApi.pay({
      goodsId: goodsId,
      goodsType: 3,
      count: 1,
      playerId: PlayerModule.data.playerId,
      orderAmount: price,
      goodsName: "首充",
      platformType: "TEST",
    }).then((resp: any) => {
      log.log("pay resp");
      if (resp.code != 200) {
        log.error(resp);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }
  private on_click_btn_lingqu_6_0() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击领取按钮);
    let goodsId = this._shouchong_activity_info.subRechargeVOList[0].id;
    HdShouChongModule.api.takeFirstRechargeReward(goodsId, 0, (data: FirstRechargeSignResponse) => {
      log.log(data);
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
      this.refresh6UI();
    });
  }
  private on_click_btn_lingqu_6_1() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击领取按钮);
    let goodsId = this._shouchong_activity_info.subRechargeVOList[0].id;
    HdShouChongModule.api.takeFirstRechargeReward(goodsId, 1, (data: FirstRechargeSignResponse) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
      this.refresh6UI();
    });
  }
  private on_click_btn_lingqu_6_2() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击领取按钮);
    let goodsId = this._shouchong_activity_info.subRechargeVOList[0].id;
    HdShouChongModule.api.takeFirstRechargeReward(goodsId, 2, (data: FirstRechargeSignResponse) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
      this.refresh6UI();
    });
  }
  private on_click_btn_lingqu_30_0() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击领取按钮);
    let goodsId = this._shouchong_activity_info.subRechargeVOList[1].id;
    HdShouChongModule.api.takeFirstRechargeReward(goodsId, 0, (data: FirstRechargeSignResponse) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
      this.refresh30UI();
    });
  }
  private on_click_btn_lingqu_30_1() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击领取按钮);
    let goodsId = this._shouchong_activity_info.subRechargeVOList[1].id;
    HdShouChongModule.api.takeFirstRechargeReward(goodsId, 1, (data: FirstRechargeSignResponse) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
      this.refresh30UI();
    });
  }
  private on_click_btn_lingqu_30_2() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击领取按钮);
    let goodsId = this._shouchong_activity_info.subRechargeVOList[1].id;
    HdShouChongModule.api.takeFirstRechargeReward(goodsId, 2, (data: FirstRechargeSignResponse) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
      this.refresh30UI();
    });
  }

  private on_click_node_day_6_0() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击天数页签);
    this.getNode("node_day_switch_6").children.forEach((child) => {
      child.getChildByName("node_select").active = false;
    });
    this.getNode("node_day_switch_6").children[0].getChildByName("node_select").active = true;

    this._tabDay6Index = 0;
    this.refresh6UI();
  }

  private on_click_node_day_6_1() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击天数页签);
    this.getNode("node_day_switch_6").children.forEach((child) => {
      child.getChildByName("node_select").active = false;
    });
    this.getNode("node_day_switch_6").children[1].getChildByName("node_select").active = true;

    this._tabDay6Index = 1;
    this.refresh6UI();
  }

  private on_click_node_day_6_2() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击天数页签);
    this.getNode("node_day_switch_6").children.forEach((child) => {
      child.getChildByName("node_select").active = false;
    });
    this.getNode("node_day_switch_6").children[2].getChildByName("node_select").active = true;

    this._tabDay6Index = 2;
    this.refresh6UI();
  }

  private on_click_node_day_30_0() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击天数页签);
    this.getNode("node_day_switch_30").children.forEach((child) => {
      child.getChildByName("node_select").active = false;
    });
    this.getNode("node_day_switch_30").children[0].getChildByName("node_select").active = true;
    this._tabDay30Index = 0;
    this.refresh30UI();
  }

  private on_click_node_day_30_1() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击天数页签);
    this.getNode("node_day_switch_30").children.forEach((child) => {
      child.getChildByName("node_select").active = false;
    });
    this.getNode("node_day_switch_30").children[1].getChildByName("node_select").active = true;
    this._tabDay30Index = 1;
    this.refresh30UI();
  }

  private on_click_node_day_30_2() {
    AudioMgr.instance.playEffect(HdShouChongAudioName.Effect.点击天数页签);
    this.getNode("node_day_switch_30").children.forEach((child) => {
      child.getChildByName("node_select").active = false;
    });
    this.getNode("node_day_switch_30").children[2].getChildByName("node_select").active = true;
    this._tabDay30Index = 2;
    this.refresh30UI();
  }

  /** 查看上一个英雄*/
  private on_click_btn_next() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let i = this._layoutManager.foucsPosition;
    i++;
    if (i > 8) {
      i = 0;
    }
    this._layoutManager.foucsPosition = i;
  }

  /** 查看下一个英雄*/
  private on_click_btn_last() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let i = this._layoutManager.foucsPosition;
    i--;
    if (i < 0) {
      i = 8;
    }
    this._layoutManager.foucsPosition = i;
  }
}
