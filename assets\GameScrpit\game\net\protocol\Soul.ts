// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Soul.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RewardMessage } from "./Comm";

export const protobufPackage = "sim";

/**  */
export interface SoulIntegrateRequest {
  /** 被吞噬的兽魂ID */
  srcId: number;
  /** 吞噬的兽魂ID */
  dstId: number;
}

/**  */
export interface SoulIntegrateResponse {
  /** 吞噬的武魂已经吞噬的次数 */
  integrateCount: number;
  /** 吞噬的武魂拥有的属性集合 */
  attrMap: { [key: number]: number };
  /** 奖励的道具 */
  rewardMessage: RewardMessage | undefined;
}

export interface SoulIntegrateResponse_AttrMapEntry {
  key: number;
  value: number;
}

/**  */
export interface SoulPictureActiveResponse {
  /**  */
  soulPlan:
    | SoulPlanMessage
    | undefined;
  /** 是否有激活新的方案 */
  unlockPlan: boolean;
  /** 如果激活不为空 */
  newSoulPlan: SoulPlanMessage | undefined;
}

/**  */
export interface SoulPictureMessage {
  /** 当前生效的属性 */
  attrId: number;
  /** 当前生效的属性值 */
  attrAdd: number;
  /** 候补生效的属性 <= 0 时表示没有或者已经被用于替换为生效的属性 */
  backUpAttrId: number;
  /** 候补生效的属性值 */
  backUpAttrAdd: number;
}

/**  */
export interface SoulPictureSelectRequest {
  /** 第几个计划 */
  planIndex: number;
  /** 第几个方案 */
  pictureIndex: number;
}

/**  */
export interface SoulPlanMessage {
  /** 是否方案生效 */
  use: boolean;
  /** 生效的组合 key为组合在配置表的索引顺序，从0开始 */
  pictureMap: { [key: number]: SoulPictureMessage };
}

export interface SoulPlanMessage_PictureMapEntry {
  key: number;
  value: SoulPictureMessage | undefined;
}

/**  */
export interface WarriorBuySoulResponse {
  /** 得到的新的武魂信息 */
  warriorSoulMessage:
    | WarriorSoulMessage
    | undefined;
  /** 本轮武魂的购买情况 */
  chosenIndexList: boolean[];
  /** 三个武魂的模板ID */
  refreshTemplateIdList: number[];
  /** 曾经获得的所有兽魂 */
  soulHoldSet: number[];
}

/**  */
export interface WarriorRefreshResponse {
  refreshTemplateIdList: number[];
  freeCount: number;
  paidCount: number;
  exp: number;
}

/**  */
export interface WarriorSoulManageMessage {
  slot: number;
  chosenIndexList: boolean[];
  refreshTemplateIdList: number[];
  freeCount: number;
  paidCount: number;
  exp: number;
  /** 曾经获得的所有兽魂集合数量 */
  soulHoldSet: number[];
  /** 生效的图鉴方案 */
  planList: SoulPlanMessage[];
  soulMap: { [key: number]: WarriorSoulMessage };
}

export interface WarriorSoulManageMessage_SoulMapEntry {
  key: number;
  value: WarriorSoulMessage | undefined;
}

/**  */
export interface WarriorSoulMessage {
  id: number;
  /** 武魂的模板ID */
  soulTemplateId: number;
  /** 阶 */
  stage: number;
  /** 等级 */
  grade: number;
  /** 是否上阵 */
  chosen: boolean;
  /** 属性集合 */
  attrMap: { [key: number]: number };
  /** 吞噬次数 */
  integrateCount: number;
  /** 获取的时间 */
  timeStamp: number;
}

export interface WarriorSoulMessage_AttrMapEntry {
  key: number;
  value: number;
}

/**  */
export interface WarriorSoulWorkResponse {
  allSoulMap: { [key: number]: WarriorSoulMessage };
}

export interface WarriorSoulWorkResponse_AllSoulMapEntry {
  key: number;
  value: WarriorSoulMessage | undefined;
}

function createBaseSoulIntegrateRequest(): SoulIntegrateRequest {
  return { srcId: 0, dstId: 0 };
}

export const SoulIntegrateRequest: MessageFns<SoulIntegrateRequest> = {
  encode(message: SoulIntegrateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.srcId !== 0) {
      writer.uint32(8).int64(message.srcId);
    }
    if (message.dstId !== 0) {
      writer.uint32(16).int64(message.dstId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulIntegrateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulIntegrateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.srcId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.dstId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulIntegrateRequest>, I>>(base?: I): SoulIntegrateRequest {
    return SoulIntegrateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulIntegrateRequest>, I>>(object: I): SoulIntegrateRequest {
    const message = createBaseSoulIntegrateRequest();
    message.srcId = object.srcId ?? 0;
    message.dstId = object.dstId ?? 0;
    return message;
  },
};

function createBaseSoulIntegrateResponse(): SoulIntegrateResponse {
  return { integrateCount: 0, attrMap: {}, rewardMessage: undefined };
}

export const SoulIntegrateResponse: MessageFns<SoulIntegrateResponse> = {
  encode(message: SoulIntegrateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.integrateCount !== 0) {
      writer.uint32(8).int32(message.integrateCount);
    }
    Object.entries(message.attrMap).forEach(([key, value]) => {
      SoulIntegrateResponse_AttrMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulIntegrateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulIntegrateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.integrateCount = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = SoulIntegrateResponse_AttrMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.attrMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulIntegrateResponse>, I>>(base?: I): SoulIntegrateResponse {
    return SoulIntegrateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulIntegrateResponse>, I>>(object: I): SoulIntegrateResponse {
    const message = createBaseSoulIntegrateResponse();
    message.integrateCount = object.integrateCount ?? 0;
    message.attrMap = Object.entries(object.attrMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseSoulIntegrateResponse_AttrMapEntry(): SoulIntegrateResponse_AttrMapEntry {
  return { key: 0, value: 0 };
}

export const SoulIntegrateResponse_AttrMapEntry: MessageFns<SoulIntegrateResponse_AttrMapEntry> = {
  encode(message: SoulIntegrateResponse_AttrMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulIntegrateResponse_AttrMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulIntegrateResponse_AttrMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulIntegrateResponse_AttrMapEntry>, I>>(
    base?: I,
  ): SoulIntegrateResponse_AttrMapEntry {
    return SoulIntegrateResponse_AttrMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulIntegrateResponse_AttrMapEntry>, I>>(
    object: I,
  ): SoulIntegrateResponse_AttrMapEntry {
    const message = createBaseSoulIntegrateResponse_AttrMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseSoulPictureActiveResponse(): SoulPictureActiveResponse {
  return { soulPlan: undefined, unlockPlan: false, newSoulPlan: undefined };
}

export const SoulPictureActiveResponse: MessageFns<SoulPictureActiveResponse> = {
  encode(message: SoulPictureActiveResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.soulPlan !== undefined) {
      SoulPlanMessage.encode(message.soulPlan, writer.uint32(10).fork()).join();
    }
    if (message.unlockPlan !== false) {
      writer.uint32(16).bool(message.unlockPlan);
    }
    if (message.newSoulPlan !== undefined) {
      SoulPlanMessage.encode(message.newSoulPlan, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulPictureActiveResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulPictureActiveResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.soulPlan = SoulPlanMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.unlockPlan = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.newSoulPlan = SoulPlanMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulPictureActiveResponse>, I>>(base?: I): SoulPictureActiveResponse {
    return SoulPictureActiveResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulPictureActiveResponse>, I>>(object: I): SoulPictureActiveResponse {
    const message = createBaseSoulPictureActiveResponse();
    message.soulPlan = (object.soulPlan !== undefined && object.soulPlan !== null)
      ? SoulPlanMessage.fromPartial(object.soulPlan)
      : undefined;
    message.unlockPlan = object.unlockPlan ?? false;
    message.newSoulPlan = (object.newSoulPlan !== undefined && object.newSoulPlan !== null)
      ? SoulPlanMessage.fromPartial(object.newSoulPlan)
      : undefined;
    return message;
  },
};

function createBaseSoulPictureMessage(): SoulPictureMessage {
  return { attrId: 0, attrAdd: 0, backUpAttrId: 0, backUpAttrAdd: 0 };
}

export const SoulPictureMessage: MessageFns<SoulPictureMessage> = {
  encode(message: SoulPictureMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.attrId !== 0) {
      writer.uint32(8).int64(message.attrId);
    }
    if (message.attrAdd !== 0) {
      writer.uint32(17).double(message.attrAdd);
    }
    if (message.backUpAttrId !== 0) {
      writer.uint32(24).int64(message.backUpAttrId);
    }
    if (message.backUpAttrAdd !== 0) {
      writer.uint32(33).double(message.backUpAttrAdd);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulPictureMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulPictureMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.attrId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.attrAdd = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.backUpAttrId = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.backUpAttrAdd = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulPictureMessage>, I>>(base?: I): SoulPictureMessage {
    return SoulPictureMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulPictureMessage>, I>>(object: I): SoulPictureMessage {
    const message = createBaseSoulPictureMessage();
    message.attrId = object.attrId ?? 0;
    message.attrAdd = object.attrAdd ?? 0;
    message.backUpAttrId = object.backUpAttrId ?? 0;
    message.backUpAttrAdd = object.backUpAttrAdd ?? 0;
    return message;
  },
};

function createBaseSoulPictureSelectRequest(): SoulPictureSelectRequest {
  return { planIndex: 0, pictureIndex: 0 };
}

export const SoulPictureSelectRequest: MessageFns<SoulPictureSelectRequest> = {
  encode(message: SoulPictureSelectRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.planIndex !== 0) {
      writer.uint32(8).int32(message.planIndex);
    }
    if (message.pictureIndex !== 0) {
      writer.uint32(16).int32(message.pictureIndex);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulPictureSelectRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulPictureSelectRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.planIndex = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pictureIndex = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulPictureSelectRequest>, I>>(base?: I): SoulPictureSelectRequest {
    return SoulPictureSelectRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulPictureSelectRequest>, I>>(object: I): SoulPictureSelectRequest {
    const message = createBaseSoulPictureSelectRequest();
    message.planIndex = object.planIndex ?? 0;
    message.pictureIndex = object.pictureIndex ?? 0;
    return message;
  },
};

function createBaseSoulPlanMessage(): SoulPlanMessage {
  return { use: false, pictureMap: {} };
}

export const SoulPlanMessage: MessageFns<SoulPlanMessage> = {
  encode(message: SoulPlanMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.use !== false) {
      writer.uint32(8).bool(message.use);
    }
    Object.entries(message.pictureMap).forEach(([key, value]) => {
      SoulPlanMessage_PictureMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulPlanMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulPlanMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.use = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = SoulPlanMessage_PictureMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.pictureMap[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulPlanMessage>, I>>(base?: I): SoulPlanMessage {
    return SoulPlanMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulPlanMessage>, I>>(object: I): SoulPlanMessage {
    const message = createBaseSoulPlanMessage();
    message.use = object.use ?? false;
    message.pictureMap = Object.entries(object.pictureMap ?? {}).reduce<{ [key: number]: SoulPictureMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = SoulPictureMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseSoulPlanMessage_PictureMapEntry(): SoulPlanMessage_PictureMapEntry {
  return { key: 0, value: undefined };
}

export const SoulPlanMessage_PictureMapEntry: MessageFns<SoulPlanMessage_PictureMapEntry> = {
  encode(message: SoulPlanMessage_PictureMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== undefined) {
      SoulPictureMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulPlanMessage_PictureMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulPlanMessage_PictureMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SoulPictureMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulPlanMessage_PictureMapEntry>, I>>(base?: I): SoulPlanMessage_PictureMapEntry {
    return SoulPlanMessage_PictureMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulPlanMessage_PictureMapEntry>, I>>(
    object: I,
  ): SoulPlanMessage_PictureMapEntry {
    const message = createBaseSoulPlanMessage_PictureMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? SoulPictureMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseWarriorBuySoulResponse(): WarriorBuySoulResponse {
  return { warriorSoulMessage: undefined, chosenIndexList: [], refreshTemplateIdList: [], soulHoldSet: [] };
}

export const WarriorBuySoulResponse: MessageFns<WarriorBuySoulResponse> = {
  encode(message: WarriorBuySoulResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.warriorSoulMessage !== undefined) {
      WarriorSoulMessage.encode(message.warriorSoulMessage, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.chosenIndexList) {
      writer.bool(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.refreshTemplateIdList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.soulHoldSet) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WarriorBuySoulResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWarriorBuySoulResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.warriorSoulMessage = WarriorSoulMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.chosenIndexList.push(reader.bool());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.chosenIndexList.push(reader.bool());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.refreshTemplateIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.refreshTemplateIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.soulHoldSet.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.soulHoldSet.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WarriorBuySoulResponse>, I>>(base?: I): WarriorBuySoulResponse {
    return WarriorBuySoulResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WarriorBuySoulResponse>, I>>(object: I): WarriorBuySoulResponse {
    const message = createBaseWarriorBuySoulResponse();
    message.warriorSoulMessage = (object.warriorSoulMessage !== undefined && object.warriorSoulMessage !== null)
      ? WarriorSoulMessage.fromPartial(object.warriorSoulMessage)
      : undefined;
    message.chosenIndexList = object.chosenIndexList?.map((e) => e) || [];
    message.refreshTemplateIdList = object.refreshTemplateIdList?.map((e) => e) || [];
    message.soulHoldSet = object.soulHoldSet?.map((e) => e) || [];
    return message;
  },
};

function createBaseWarriorRefreshResponse(): WarriorRefreshResponse {
  return { refreshTemplateIdList: [], freeCount: 0, paidCount: 0, exp: 0 };
}

export const WarriorRefreshResponse: MessageFns<WarriorRefreshResponse> = {
  encode(message: WarriorRefreshResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.refreshTemplateIdList) {
      writer.int64(v);
    }
    writer.join();
    if (message.freeCount !== 0) {
      writer.uint32(16).int32(message.freeCount);
    }
    if (message.paidCount !== 0) {
      writer.uint32(24).int32(message.paidCount);
    }
    if (message.exp !== 0) {
      writer.uint32(32).int32(message.exp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WarriorRefreshResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWarriorRefreshResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.refreshTemplateIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.refreshTemplateIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.freeCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.paidCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.exp = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WarriorRefreshResponse>, I>>(base?: I): WarriorRefreshResponse {
    return WarriorRefreshResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WarriorRefreshResponse>, I>>(object: I): WarriorRefreshResponse {
    const message = createBaseWarriorRefreshResponse();
    message.refreshTemplateIdList = object.refreshTemplateIdList?.map((e) => e) || [];
    message.freeCount = object.freeCount ?? 0;
    message.paidCount = object.paidCount ?? 0;
    message.exp = object.exp ?? 0;
    return message;
  },
};

function createBaseWarriorSoulManageMessage(): WarriorSoulManageMessage {
  return {
    slot: 0,
    chosenIndexList: [],
    refreshTemplateIdList: [],
    freeCount: 0,
    paidCount: 0,
    exp: 0,
    soulHoldSet: [],
    planList: [],
    soulMap: {},
  };
}

export const WarriorSoulManageMessage: MessageFns<WarriorSoulManageMessage> = {
  encode(message: WarriorSoulManageMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0) {
      writer.uint32(8).int32(message.slot);
    }
    writer.uint32(18).fork();
    for (const v of message.chosenIndexList) {
      writer.bool(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.refreshTemplateIdList) {
      writer.int64(v);
    }
    writer.join();
    if (message.freeCount !== 0) {
      writer.uint32(32).int32(message.freeCount);
    }
    if (message.paidCount !== 0) {
      writer.uint32(40).int32(message.paidCount);
    }
    if (message.exp !== 0) {
      writer.uint32(48).int32(message.exp);
    }
    writer.uint32(58).fork();
    for (const v of message.soulHoldSet) {
      writer.int64(v);
    }
    writer.join();
    for (const v of message.planList) {
      SoulPlanMessage.encode(v!, writer.uint32(66).fork()).join();
    }
    Object.entries(message.soulMap).forEach(([key, value]) => {
      WarriorSoulManageMessage_SoulMapEntry.encode({ key: key as any, value }, writer.uint32(74).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WarriorSoulManageMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWarriorSoulManageMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.int32();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.chosenIndexList.push(reader.bool());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.chosenIndexList.push(reader.bool());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.refreshTemplateIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.refreshTemplateIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.freeCount = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.paidCount = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.exp = reader.int32();
          continue;
        }
        case 7: {
          if (tag === 56) {
            message.soulHoldSet.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.soulHoldSet.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.planList.push(SoulPlanMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          const entry9 = WarriorSoulManageMessage_SoulMapEntry.decode(reader, reader.uint32());
          if (entry9.value !== undefined) {
            message.soulMap[entry9.key] = entry9.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WarriorSoulManageMessage>, I>>(base?: I): WarriorSoulManageMessage {
    return WarriorSoulManageMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WarriorSoulManageMessage>, I>>(object: I): WarriorSoulManageMessage {
    const message = createBaseWarriorSoulManageMessage();
    message.slot = object.slot ?? 0;
    message.chosenIndexList = object.chosenIndexList?.map((e) => e) || [];
    message.refreshTemplateIdList = object.refreshTemplateIdList?.map((e) => e) || [];
    message.freeCount = object.freeCount ?? 0;
    message.paidCount = object.paidCount ?? 0;
    message.exp = object.exp ?? 0;
    message.soulHoldSet = object.soulHoldSet?.map((e) => e) || [];
    message.planList = object.planList?.map((e) => SoulPlanMessage.fromPartial(e)) || [];
    message.soulMap = Object.entries(object.soulMap ?? {}).reduce<{ [key: number]: WarriorSoulMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = WarriorSoulMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseWarriorSoulManageMessage_SoulMapEntry(): WarriorSoulManageMessage_SoulMapEntry {
  return { key: 0, value: undefined };
}

export const WarriorSoulManageMessage_SoulMapEntry: MessageFns<WarriorSoulManageMessage_SoulMapEntry> = {
  encode(message: WarriorSoulManageMessage_SoulMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      WarriorSoulMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WarriorSoulManageMessage_SoulMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWarriorSoulManageMessage_SoulMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = WarriorSoulMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WarriorSoulManageMessage_SoulMapEntry>, I>>(
    base?: I,
  ): WarriorSoulManageMessage_SoulMapEntry {
    return WarriorSoulManageMessage_SoulMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WarriorSoulManageMessage_SoulMapEntry>, I>>(
    object: I,
  ): WarriorSoulManageMessage_SoulMapEntry {
    const message = createBaseWarriorSoulManageMessage_SoulMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? WarriorSoulMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseWarriorSoulMessage(): WarriorSoulMessage {
  return { id: 0, soulTemplateId: 0, stage: 0, grade: 0, chosen: false, attrMap: {}, integrateCount: 0, timeStamp: 0 };
}

export const WarriorSoulMessage: MessageFns<WarriorSoulMessage> = {
  encode(message: WarriorSoulMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.soulTemplateId !== 0) {
      writer.uint32(16).int64(message.soulTemplateId);
    }
    if (message.stage !== 0) {
      writer.uint32(24).int32(message.stage);
    }
    if (message.grade !== 0) {
      writer.uint32(32).int32(message.grade);
    }
    if (message.chosen !== false) {
      writer.uint32(40).bool(message.chosen);
    }
    Object.entries(message.attrMap).forEach(([key, value]) => {
      WarriorSoulMessage_AttrMapEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    if (message.integrateCount !== 0) {
      writer.uint32(56).int32(message.integrateCount);
    }
    if (message.timeStamp !== 0) {
      writer.uint32(64).int64(message.timeStamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WarriorSoulMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWarriorSoulMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.soulTemplateId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.stage = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.grade = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.chosen = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = WarriorSoulMessage_AttrMapEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.attrMap[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.integrateCount = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.timeStamp = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WarriorSoulMessage>, I>>(base?: I): WarriorSoulMessage {
    return WarriorSoulMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WarriorSoulMessage>, I>>(object: I): WarriorSoulMessage {
    const message = createBaseWarriorSoulMessage();
    message.id = object.id ?? 0;
    message.soulTemplateId = object.soulTemplateId ?? 0;
    message.stage = object.stage ?? 0;
    message.grade = object.grade ?? 0;
    message.chosen = object.chosen ?? false;
    message.attrMap = Object.entries(object.attrMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.integrateCount = object.integrateCount ?? 0;
    message.timeStamp = object.timeStamp ?? 0;
    return message;
  },
};

function createBaseWarriorSoulMessage_AttrMapEntry(): WarriorSoulMessage_AttrMapEntry {
  return { key: 0, value: 0 };
}

export const WarriorSoulMessage_AttrMapEntry: MessageFns<WarriorSoulMessage_AttrMapEntry> = {
  encode(message: WarriorSoulMessage_AttrMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WarriorSoulMessage_AttrMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWarriorSoulMessage_AttrMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WarriorSoulMessage_AttrMapEntry>, I>>(base?: I): WarriorSoulMessage_AttrMapEntry {
    return WarriorSoulMessage_AttrMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WarriorSoulMessage_AttrMapEntry>, I>>(
    object: I,
  ): WarriorSoulMessage_AttrMapEntry {
    const message = createBaseWarriorSoulMessage_AttrMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseWarriorSoulWorkResponse(): WarriorSoulWorkResponse {
  return { allSoulMap: {} };
}

export const WarriorSoulWorkResponse: MessageFns<WarriorSoulWorkResponse> = {
  encode(message: WarriorSoulWorkResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.allSoulMap).forEach(([key, value]) => {
      WarriorSoulWorkResponse_AllSoulMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WarriorSoulWorkResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWarriorSoulWorkResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = WarriorSoulWorkResponse_AllSoulMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.allSoulMap[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WarriorSoulWorkResponse>, I>>(base?: I): WarriorSoulWorkResponse {
    return WarriorSoulWorkResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WarriorSoulWorkResponse>, I>>(object: I): WarriorSoulWorkResponse {
    const message = createBaseWarriorSoulWorkResponse();
    message.allSoulMap = Object.entries(object.allSoulMap ?? {}).reduce<{ [key: number]: WarriorSoulMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = WarriorSoulMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseWarriorSoulWorkResponse_AllSoulMapEntry(): WarriorSoulWorkResponse_AllSoulMapEntry {
  return { key: 0, value: undefined };
}

export const WarriorSoulWorkResponse_AllSoulMapEntry: MessageFns<WarriorSoulWorkResponse_AllSoulMapEntry> = {
  encode(message: WarriorSoulWorkResponse_AllSoulMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      WarriorSoulMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WarriorSoulWorkResponse_AllSoulMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWarriorSoulWorkResponse_AllSoulMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = WarriorSoulMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WarriorSoulWorkResponse_AllSoulMapEntry>, I>>(
    base?: I,
  ): WarriorSoulWorkResponse_AllSoulMapEntry {
    return WarriorSoulWorkResponse_AllSoulMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WarriorSoulWorkResponse_AllSoulMapEntry>, I>>(
    object: I,
  ): WarriorSoulWorkResponse_AllSoulMapEntry {
    const message = createBaseWarriorSoulWorkResponse_AllSoulMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? WarriorSoulMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
