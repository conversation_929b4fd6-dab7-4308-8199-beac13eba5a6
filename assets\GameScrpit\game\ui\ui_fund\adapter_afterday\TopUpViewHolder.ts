import { instantiate, Node, _decorator, Label } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { AfterAudioName, TopUpCostRewardVO } from "db://assets/GameScrpit/module/after/AfterConfig";
import ToolExt from "../../../common/ToolExt";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { AfterModule } from "db://assets/GameScrpit/module/after/AfterModule";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { NodeTool } from "db://assets/GameScrpit/lib/utils/NodeTool";
import { avId1 } from "db://assets/GameScrpit/module/day/DayActivityModule";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import MsgEnum from "../../../event/MsgEnum";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { TopUpMessage, TopUpRewardResponse } from "../../../net/protocol/Activity";
import { ActivityID } from "db://assets/GameScrpit/module/activity/ActivityConstant";
const { ccclass, property } = _decorator;

//累计充值
@ccclass("TopUpViewHolder")
export class TopUpViewHolder extends ViewHolder {
  private _data: TopUpCostRewardVO;
  private _type: number;
  public updateData(data: TopUpCostRewardVO, type: number) {
    this._data = data;
    this._type = type;

    let rewardList = ToolExt.traAwardItemMapList(data.rewardList);
    this.getNode("item_content").children.forEach((item) => {
      item.active = false;
    });
    for (let i = 0; i < rewardList.length; i++) {
      let item = this.getNode("item_content").children[i];
      if (!item) {
        item = instantiate(this.getNode("item_content").children[0]);
        item.parent = this.getNode("item_content");
      }
      item.active = true;
      FmUtils.setItemNode(item, rewardList[i].id, rewardList[i].num);
    }

    this.getNode("lbl_money").getComponent(Label).string =
      Math.min(AfterModule.data.totalRecharge, data.money) + "/" + data.money;

    // this.getNode["btn_get_award"][db_info] = info;
    // this.getNode["btn_get_award"][TopUpRewardRequest_index] = i;
    // 原先逻辑
    if (AfterModule.data.takeList.indexOf(this.position) != -1) {
      /**领取隐藏 */
      this.getNode("btn_get_award").active = false;
      /**前往隐藏 */
      this.getNode("btn_go_shop").active = false;
      /**提示标本隐藏 */
      this.getNode("money_hit").active = false;

      /**已经领取展示 */
      this.getNode("btn_yilingqu").active = true;
    } else {
      /**已经领取隐藏 */
      this.getNode("btn_yilingqu").active = false;
      /**提示标本展示 */
      this.getNode("money_hit").active = true;
      /**判断是前往还是领取 */
      let bool = data.money > AfterModule.data.totalRecharge ? true : false;
      this.getNode("btn_get_award").active = !bool;
      this.getNode("btn_go_shop").active = bool;
    }
  }

  private on_click_btn_get_award(event) {
    AudioMgr.instance.playEffect(AfterAudioName.Effect.点击领取按钮);

    AfterModule.api.takeTopUpCostReward(
      {
        activityId: ActivityID.AFTERID,
        index: this.position,
      },
      (res: TopUpRewardResponse) => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
        this.updateData(this._data, this._type);
      }
    );
  }

  private on_click_btn_go_shop() {
    AudioMgr.instance.playEffect(AfterAudioName.Effect.点击前往按钮);
    function clickNode(nodePageName: string, nodeClickName: string) {
      if (nodeClickName) {
        let nodeParent = NodeTool.findByName(UIMgr.instance.uiRoot, nodePageName);
        if (!nodeParent) {
          return;
        }
        let nodeClick = NodeTool.findByName(nodeParent, nodeClickName);
        NodeTool.fakeClick(nodeClick);
      }
    }
    let cfgJump = JsonMgr.instance.getConfigJump(55);
    UIMgr.instance.showDialog(cfgJump.pageName, {}, null, () => {
      clickNode(cfgJump.pageName, cfgJump.nodeClick);
    });
  }
}

export class TopUpAdapter extends ListAdapter {
  private _type: number;
  private _item: Node;
  private _data: TopUpCostRewardVO[] = [];
  constructor(item: Node) {
    super();
    this._item = item;
  }
  setData(data: TopUpCostRewardVO[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(TopUpViewHolder).updateData(this._data[position], this._type);
  }
  getCount(): number {
    return this._data.length;
  }
}
