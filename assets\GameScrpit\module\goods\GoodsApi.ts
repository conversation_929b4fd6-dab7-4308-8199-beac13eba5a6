import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>pi<PERSON>andlerSuccess } from "../../game/mgr/ApiHandler";
import { GoodsSubCmd } from "../../game/net/cmd/CmdData";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DoubleValueList, LongValue } from "../../game/net/protocol/ExternalMessage";
import {
  GoodsAddRequest,
  GoodsRedeemMessage,
  GoodsRedeemRequest,
  GoodsRedeemResponse,
} from "../../game/net/protocol/Goods";
import { GoodsModule } from "./GoodsModule";

export default class GoodsApi {
  public addRes(data: GoodsAddRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(BoolValue, GoodsSubCmd.addRes, GoodsAddRequest.encode(data), (data: BoolValue) => {
      success && success(data);
    });
  }

  /**
   * 获取兑换情况
   * @param success
   */
  public buyInfo(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(GoodsRedeemMessage, GoodsSubCmd.redeemInfo, null, (data: GoodsRedeemMessage) => {
      GoodsModule.data.initGoodsRedeemMsg(data);
      success && success(data);
    });
  }

  /**
   * 兑换
   * @param id  redeemId
   * @param success
   */
  public redeemGoods(id: number, num: number, success?: ApiHandlerSuccess) {
    let data = GoodsRedeemRequest.create({ redeemId: id, count: num });
    ApiHandler.instance.request(
      GoodsRedeemResponse,
      GoodsSubCmd.redeemGoods,
      GoodsRedeemRequest.encode(data),
      (data: GoodsRedeemResponse) => {
        this.buyInfo(() => {
          success && success(data.rewardList);
        });
      }
    );
  }
}

// 路由: 10 - 3  --- 【获取兑换情况】 --- 【GoodAction:58】【info】
// 方法返回值: GoodsRedeemMessage

// 路由: 10 - 4  --- 【兑换】 --- 【GoodAction:73】【buy】
// 方法参数: LongValue redeemId
// 方法返回值: DoubleValueList
