import CmdMgr from "../../game/mgr/CmdMgr";
import { MainCmd } from "../../game/net/cmd/CmdData";

// 宠物
// 宠物模块
const petCmd = 5;
export enum PetSubCmd {
  // 宠物升1级
  levelUpOne = CmdMgr.getMergeCmd(petCmd, 1),
  // 宠物升10级
  levelUpTen = CmdMgr.getMergeCmd(petCmd, 2),
  // 宠物觉醒
  awakeUp = CmdMgr.getMergeCmd(petCmd, 3),
  // 洗练技能
  reFreshSkill = CmdMgr.getMergeCmd(petCmd, 4),
  // 获取所有宠物
  getAllPet = CmdMgr.getMergeCmd(petCmd, 5),
  // getOnePet
  getOnePet = CmdMgr.getMergeCmd(petCmd, 6),
  // 穿戴皮肤
  chooseSkin = CmdMgr.getMergeCmd(petCmd, 7),
}
export enum PetSort {
  QUALITY,
  LEVEL,
}

export enum HeroPetCard { //宠物品质背景颜色
  color_1 = "S1106",
  color_2 = "S1109",
  color_3 = "S1112",
  color_4 = "S1115",
  color_5 = "S1118",
}
export enum HeroPetColorLevel {
  color_1 = "S1107",
  color_2 = "S1110",
  color_3 = "S1113",
  color_4 = "S1116",
  color_5 = "S1119",
}
export enum HeroPetColorIcon { //宠物觉醒次数背景图标
  color_1 = "S1108",
  color_2 = "S1111",
  color_3 = "S1114",
  color_4 = "S1117",
  color_5 = "S1120",
}
export enum HeroPetSkillIcon {
  icon_101 = "S1125",
  icon_102 = "S1123",
  icon_103 = "S1124",
}
