import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess, Net_Code } from "../../game/mgr/ApiHandler";
import { BoolValue, ByteValueList, LongValue } from "../../game/net/protocol/ExternalMessage";
import { MailMessage, ReadResponse } from "../../game/net/protocol/Mail";
import MsgMgr from "../../lib/event/MsgMgr";
import { MailSubCmd } from "./MailConstant";
import { MailModule } from "./MailModule";

const enum MailErrorCodeEnum {
  notExist = 2002,
}

export class MailApi {
  /**
   * 客户端邮件
   * @param success 成功回调方法
   */
  public syncMail(cmdId: number, mailListOld: MailMessage[], success?: ApiHandlerSuccess) {
    let createTime = 0;
    let id = 0;
    if (mailListOld && mailListOld.length > 0) {
      createTime = mailListOld[0].createTime;
      id = mailListOld[0].id;
      // 本地最新的创建时间对应的邮件ID
      for (let i = 1; i < mailListOld.length; i++) {
        if (createTime > mailListOld[i].createTime) {
          createTime = mailListOld[i].createTime;
          id = mailListOld[i].id;
        }
      }
    }
    let sendData: LongValue = {
      value: id,
    };

    ApiHandler.instance.list(MailMessage, cmdId, LongValue.encode(sendData), (mailList: MailMessage[]) => {
      MailModule.service.updateMerge(mailListOld, mailList);
      MsgMgr.emit(MsgEnum.ON_MAIL_UPDATE);
      success && success(mailList);
    });
  }

  /**
   * 客户端同步日常邮件
   * @param success 成功回调方法
   */
  public syncDailyMail(success?: ApiHandlerSuccess) {
    this.syncMail(MailSubCmd.syncDailyMail, MailModule.data.dailyMailList, success);
  }

  /**
   * 客户端同步官方邮件
   * @param success 成功回调方法
   */
  public syncSysMail(success?: ApiHandlerSuccess) {
    this.syncMail(MailSubCmd.syncSysMail, MailModule.data.sysMailList, success);
  }

  /**
   * 读取单个邮件
   * @param mailId 邮件ID
   * @param success 成功回调方法
   */
  public readMail(mailId: number, success?: ApiHandlerSuccess) {
    let sendData: LongValue = {
      value: mailId,
    };

    ApiHandler.instance.request(
      ReadResponse,
      MailSubCmd.readOne,
      LongValue.encode(sendData),
      (resp: ReadResponse) => {
        MailModule.service.updateReadMail(resp.idList);
        MsgMgr.emit(MsgEnum.ON_MAIL_UPDATE);
        success && success(resp);
      },
      (errorCode: number, msg: string[], data: any): boolean => {
        if (errorCode == MailErrorCodeEnum.notExist) {
          MailModule.service.deleteMail([mailId]);
        }
        MsgMgr.emit(MsgEnum.ON_MAIL_UPDATE);
        return true;
      }
    );
  }

  /**
   * 读取全部邮件
   * @param success 成功回调方法
   */
  public readAll(isDaily: boolean, success?: ApiHandlerSuccess) {
    let data: BoolValue = {
      value: isDaily,
    };

    ApiHandler.instance.request(ReadResponse, MailSubCmd.readAll, BoolValue.encode(data), (resp: ReadResponse) => {
      MailModule.service.updateReadMail(resp.idList);
      MsgMgr.emit(MsgEnum.ON_MAIL_UPDATE);
      success && success(resp);
    });
  }

  /**
   * 删除已读邮件
   * @param success 成功回调方法
   */
  public deleteRead(mailId: number, success?: ApiHandlerSuccess) {
    let sendData: LongValue = {
      value: mailId,
    };
    ApiHandler.instance.request(BoolValue, MailSubCmd.deleteOne, LongValue.encode(sendData), (resp: BoolValue) => {
      MailModule.service.deleteMail([mailId]);
      MsgMgr.emit(MsgEnum.ON_MAIL_UPDATE);
      success && success(resp);
    });
  }

  /**
   * 删除已读邮件
   * @param success
   */
  public deleteAll(isDaily: boolean, success?: ApiHandlerSuccess) {
    let data: BoolValue = {
      value: isDaily,
    };

    ApiHandler.instance.request(BoolValue, MailSubCmd.deleteReadAll, BoolValue.encode(data), (resp: BoolValue) => {
      MailModule.data.dailyMailList = MailModule.data.dailyMailList.filter((e) => !e.read);
      MailModule.data.sysMailList = MailModule.data.sysMailList.filter((e) => !e.read);
      MsgMgr.emit(MsgEnum.ON_MAIL_UPDATE);
      success && success(resp);
    });
  }
}
