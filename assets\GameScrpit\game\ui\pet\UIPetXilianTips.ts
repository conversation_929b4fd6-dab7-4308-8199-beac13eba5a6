import { _decorator, Label } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { PetModule } from "../../../module/pet/PetModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Sat Feb 08 2025 17:40:54 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/pet/UIPetXilianTips.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIPetXilianTips")
export class UIPetXilianTips extends UINode {
  protected _openAct: boolean = true;

  private _originalValue: number = 0;
  private _currentValue: number = 0;

  /**
   * override
   * @returns {string}
   * @protected
   */
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PET}?prefab/ui/UIPetXilianTips`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
    this._originalValue = args.old;
    this._currentValue = args.new;
  }
  protected onEvtShow(): void {
    // do something
    AudioMgr.instance.playEffect(AudioName.Effect.二级弹窗提示);

    this.getNode("lbl_original").getComponent(Label).string = `${this._originalValue}%`;
    this.getNode("lbl_improved").getComponent(Label).string = `${this._currentValue}%`;
    this.getNode("check").active = !PetModule.viewModel.isShowXilianTipsSetting();
    this.getNode("check_forever").active = !PetModule.viewModel.isShowXilianTipsSettingForever();
  }

  private on_click_btn_check_box() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("check").active = !this.getNode("check").active;
    PetModule.viewModel.setXilianTipsSetting(this.getNode("check").active);
  }
  private on_click_btn_ok() {
    AudioMgr.instance.playEffect(515);
    UIMgr.instance.back();
  }

  private on_click_btn_check_box_forever() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("check_forever").active = !this.getNode("check_forever").active;
    PetModule.viewModel.setXilianTipsSettingForever(this.getNode("check_forever").active);
  }
}
