{"skeleton": {"hash": "AvUXB0D3K+i7RYZiIg1H7//R9JI=", "spine": "3.8.75", "x": -122.23, "y": -110.61, "width": 265.24, "height": 239.66, "images": "./pb/export/", "audio": ""}, "bones": [{"name": "root"}, {"name": "pb", "parent": "root", "rotation": 20.16, "x": 7.32, "y": -37.5, "scaleX": 0.1716, "scaleY": 0.1716}, {"name": "lyly", "parent": "root", "x": 113.97, "y": -121.08}, {"name": "bone21", "parent": "lyly", "rotation": -14.38, "x": -67.19, "y": -40.1, "scaleX": 0.1714, "scaleY": 0.3644, "shearY": -28.58}, {"name": "bone31", "parent": "lyly", "rotation": -14.38, "x": -67.19, "y": -40.1, "scaleX": 0.1714, "scaleY": 0.3644, "shearY": -28.58}, {"name": "jsjs", "parent": "root", "rotation": 39.38, "x": 93.39, "y": -0.82, "scaleX": 0.3187, "scaleY": 0.1067, "shearY": -33.02}, {"name": "bone", "parent": "root", "length": 13.18, "rotation": -174.67, "x": -108.29, "y": -1.77, "scaleX": 0.5, "scaleY": 0.5}, {"name": "bone2", "parent": "bone", "length": 12.11, "rotation": -0.75, "x": 13.27, "y": -0.52}, {"name": "bone3", "parent": "bone2", "length": 12.66, "rotation": -1.98, "x": 12.06, "y": -0.79}, {"name": "bone4", "parent": "bone3", "length": 10.57, "rotation": -14.41, "x": 12.6, "y": -0.64}, {"name": "bone5", "parent": "bone4", "length": 8.2, "rotation": 1.06, "x": 10.38, "y": 1.71}, {"name": "bone6", "parent": "root", "rotation": 14.11, "x": 67.19, "y": -102.96}, {"name": "shuihua", "parent": "root", "rotation": 14.11, "x": -3.5, "y": -31.04, "scaleX": 0.7854, "scaleY": 0.819}, {"name": "bone7", "parent": "root", "length": 13.18, "rotation": -174.67, "x": -108.29, "y": -1.77, "scaleX": 0.5, "scaleY": 0.5}, {"name": "bone8", "parent": "bone7", "length": 12.11, "rotation": -0.75, "x": 13.27, "y": -0.52}, {"name": "bone9", "parent": "bone8", "length": 12.66, "rotation": -1.98, "x": 12.06, "y": -0.79}, {"name": "bone10", "parent": "bone9", "length": 10.57, "rotation": -14.41, "x": 12.6, "y": -0.64}, {"name": "bone11", "parent": "bone10", "length": 8.2, "rotation": 1.06, "x": 10.38, "y": 1.71}, {"name": "bone12", "parent": "root", "rotation": 14.11, "x": 67.19, "y": -102.96}, {"name": "bone13", "parent": "root", "length": 13.18, "rotation": -174.67, "x": -108.29, "y": -1.77, "scaleX": 0.5, "scaleY": 0.5}, {"name": "bone14", "parent": "bone13", "length": 12.11, "rotation": -0.75, "x": 13.27, "y": -0.52}, {"name": "bone15", "parent": "bone14", "length": 12.66, "rotation": -1.98, "x": 12.06, "y": -0.79}, {"name": "bone16", "parent": "bone15", "length": 10.57, "rotation": -14.41, "x": 12.6, "y": -0.64}, {"name": "bone17", "parent": "bone16", "length": 8.2, "rotation": 1.06, "x": 10.38, "y": 1.71}, {"name": "bone18", "parent": "root", "rotation": 14.11, "x": 35.49, "y": -125.21}], "slots": [{"name": "1", "bone": "pb", "color": "b6f8ff66", "attachment": "1", "blend": "additive"}, {"name": "ly17", "bone": "bone21", "color": "91f7ff71", "attachment": "ly17", "blend": "additive"}, {"name": "ly18", "bone": "bone31", "color": "91f7ff71", "attachment": "ly17", "blend": "additive"}, {"name": "jljl1", "bone": "jsjs", "color": "3ff7ff73", "attachment": "jljl1", "blend": "additive"}, {"name": "yu3", "bone": "bone"}, {"name": "yu4", "bone": "bone7"}, {"name": "yu5", "bone": "bone13", "color": "fd654dff"}, {"name": "bone6", "bone": "bone6", "attachment": "bone6"}, {"name": "bone7", "bone": "bone12", "attachment": "bone6"}, {"name": "bone8", "bone": "bone18", "attachment": "bone6"}, {"name": "shuihua", "bone": "shuihua", "attachment": "shuihua"}], "path": [{"name": "bone6", "bones": ["bone", "bone2", "bone3", "bone4", "bone5"], "target": "bone6"}, {"name": "bone7", "order": 1, "bones": ["bone7", "bone8", "bone9", "bone10", "bone11"], "target": "bone7", "position": -0.6255}, {"name": "bone8", "order": 2, "bones": ["bone13", "bone14", "bone15", "bone16", "bone17"], "target": "bone8"}], "skins": [{"name": "default", "attachments": {"bone7": {"bone6": {"type": "path", "lengths": [12.95, 26.95, 43.14, 60.35, 75.57, 93.85, 110.12, 128.31, 145.99, 167.47, 188.07, 205.78, 227.67, 256.22, 276.68, 291.11, 302.97, 314.23, 329.34, 342.15, 361.39, 381.08, 438.44], "vertexCount": 69, "vertices": [39.22, 59.41, 41.79, 58.14, 43.26, 57.41, 33.71, 53.07, 36.59, 47.14, 37.87, 44.49, 35.85, 38.49, 38.47, 33.36, 39.9, 30.56, 50.63, 31.65, 53.1, 27.17, 58.98, 16.53, 62.69, 17.91, 65.56, 15.98, 68.42, 14.05, 76.67, 17.48, 80.64, 15.93, 84.61, 14.39, 89.89, 5.95, 95.38, 5.43, 104.42, 4.57, 106.24, 9.52, 111.27, 6.63, 116.31, 3.74, 120.1, 5.17, 127.97, 10.36, 132.32, 13.22, 138.76, 6.44, 143.29, 3.28, 147.81, 0.11, 161.01, -4.09, 162.97, 1.82, 165.1, 8.27, 172.44, 14.93, 164.04, 20, 156.71, 24.43, 158.12, 30.06, 152.36, 33.04, 139.24, 39.83, 140.44, 31.67, 131.43, 37.55, 114.2, 48.79, 112.1, 41.63, 104.61, 46.04, 98.83, 49.45, 100.52, 57.35, 87.72, 54.3, 83.31, 53.25, 84.3, 62.03, 77.14, 63.16, 67.91, 64.62, 70.37, 62.07, 65.56, 64.72, 60.94, 67.25, 66.04, 68.09, 57.63, 72.25, 55.9, 73.11, 49.12, 74, 43.16, 76.55, 39.17, 78.26, 36.64, 81.46, 32.2, 83.12, 25.13, 85.75, 18.1, 87.31, 13.88, 89, 1.8, 93.82, -1.45, 95.54, -4.15, 96.9, -9.14, 99.4]}}, "yu3": {"yu1": {"type": "mesh", "uvs": [1, 0.53794, 1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0.52547, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0, 0.14485, 0.52547, 0.28859, 0.52859, 0.43153, 0.52547, 0.57848, 0.51611, 0.72865, 0.54417, 0.86677, 0.55041], "triangles": [18, 7, 9, 7, 8, 9, 18, 12, 19, 9, 11, 18, 9, 10, 11, 18, 11, 12, 6, 19, 5, 7, 18, 6, 6, 18, 19, 19, 13, 20, 19, 12, 13, 5, 20, 4, 5, 19, 20, 20, 13, 14, 4, 21, 3, 3, 21, 22, 4, 20, 21, 21, 15, 22, 22, 15, 16, 20, 14, 21, 21, 14, 15, 2, 23, 1, 23, 0, 1, 3, 22, 2, 2, 22, 23, 17, 0, 23, 23, 22, 16, 23, 16, 17], "vertices": [1, 6, -4.24, 0.53, 1, 1, 6, -3.43, 8.34, 1, 1, 6, 5.95, 7.38, 1, 2, 6, 15.32, 6.39, 0.9017, 7, 1.97, 6.94, 0.0983, 3, 6, 24.72, 5.37, 0.08033, 7, 11.37, 6.04, 0.53855, 8, -0.93, 6.81, 0.38111, 2, 7, 20.79, 5.23, 0.00102, 8, 8.52, 6.32, 0.99898, 2, 8, 17.89, 5.81, 0.28593, 9, 3.52, 7.57, 0.71407, 3, 8, 27.29, 5.27, 0.00144, 9, 12.76, 9.39, 0.69736, 10, 2.52, 7.63, 0.3012, 2, 9, 22, 11.24, 0.09186, 10, 11.8, 9.31, 0.90814, 2, 9, 23.6, 3.33, 0.0018, 10, 13.24, 1.38, 0.9982, 1, 10, 14.85, -7.41, 1, 2, 9, 16.12, -7.29, 0.00709, 10, 5.57, -9.1, 0.99291, 3, 8, 16.96, -11.2, 0.01551, 9, 6.85, -9.14, 0.48386, 10, -3.73, -10.78, 0.50063, 4, 7, 19.29, -11.7, 0.04502, 8, 7.6, -10.66, 0.47709, 9, -2.35, -10.94, 0.41688, 10, -12.97, -12.41, 0.06101, 4, 7, 9.95, -10.9, 0.59696, 8, -1.76, -10.18, 0.3562, 9, -11.54, -12.81, 0.04655, 10, -22.18, -14.1, 0.00029, 4, 6, 13.68, -10.64, 0.04692, 7, 0.55, -10.11, 0.93751, 8, -11.18, -9.72, 0.01546, 9, -20.78, -14.7, 0.00011, 2, 6, 4.23, -9.6, 0.70108, 7, -8.92, -9.19, 0.29892, 2, 6, -5.18, -8.57, 0.98052, 7, -18.34, -8.29, 0.01948, 1, 10, 3.84, -0.34, 1, 2, 9, 4.88, -0.35, 0.91525, 10, -5.53, -1.95, 0.08475, 4, 7, 19.91, -2.79, 0.00055, 8, 7.91, -1.72, 0.9213, 9, -4.27, -2.21, 0.07444, 10, -14.72, -3.65, 0.00371, 3, 7, 10.21, -2.12, 0.88521, 8, -1.8, -1.39, 0.11204, 9, -13.76, -4.3, 0.00275, 1, 7, 0.37, -0.82, 1, 2, 6, 4.53, -0.16, 0.99773, 7, -8.74, 0.25, 0.00227], "hull": 18, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 16, 18, 18, 20, 2, 0, 0, 34], "width": 66, "height": 17}, "yu3": {"type": "mesh", "uvs": [1, 0.53091, 1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0.50517, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0, 0.85606, 0.5103, 0.71278, 0.5103, 0.5695, 0.5103, 0.42932, 0.567, 0.28398, 0.56185, 0.14483, 0.51032], "triangles": [9, 10, 11, 23, 11, 12, 9, 11, 23, 22, 23, 12, 7, 8, 9, 23, 7, 9, 7, 23, 22, 6, 22, 21, 7, 22, 6, 21, 13, 20, 12, 21, 22, 21, 12, 13, 5, 6, 21, 5, 21, 4, 20, 13, 14, 20, 14, 19, 4, 20, 19, 21, 20, 4, 18, 15, 16, 19, 14, 15, 19, 15, 18, 18, 16, 17, 0, 18, 17, 3, 19, 18, 4, 19, 3, 2, 18, 0, 3, 18, 2, 2, 0, 1], "vertices": [1, 6, -5.7, 2.57, 1, 2, 6, -5.15, 8.66, 0.95539, 7, -18.54, 8.95, 0.04461, 3, 6, 4.19, 7.84, 0.79985, 7, -9.18, 8.25, 0.1827, 8, -21.55, 8.3, 0.01745, 4, 6, 13.6, 6.81, 0.53319, 7, 0.23, 7.34, 0.32185, 8, -12.1, 7.71, 0.14482, 9, -26, 1.95, 0.00013, 4, 6, 22.87, 5.08, 0.24465, 7, 9.53, 5.73, 0.37178, 8, -2.76, 6.43, 0.28469, 9, -16.63, 3.03, 0.09887, 5, 6, 31.91, 3.05, 0.06686, 7, 18.6, 3.81, 0.23475, 8, 6.37, 4.83, 0.38966, 9, -7.39, 3.75, 0.23652, 10, -17.73, 2.37, 0.07221, 5, 6, 40.72, 1.72, 0.00018, 7, 27.42, 2.6, 0.0956, 8, 15.23, 3.92, 0.27479, 9, 1.42, 5.08, 0.3739, 10, -8.9, 3.54, 0.25552, 4, 7, 36.21, 2.81, 0.00106, 8, 24.01, 4.43, 0.13492, 9, 9.79, 7.76, 0.31407, 10, -0.48, 6.06, 0.54995, 3, 8, 32.84, 6.05, 0.0125, 9, 17.94, 11.52, 0.17642, 10, 7.74, 9.67, 0.81108, 2, 9, 20.39, 5.8, 0.0602, 10, 10.08, 3.9, 0.9398, 3, 8, 34.79, -6.78, 0.02727, 9, 23.02, -0.42, 0.12095, 10, 12.6, -2.37, 0.85178, 4, 7, 36.99, -10.13, 0.00515, 8, 25.24, -8.47, 0.15464, 9, 14.19, -4.43, 0.22647, 10, 3.69, -6.21, 0.61374, 5, 6, 40.32, -11.24, 0.0006, 7, 27.19, -10.36, 0.10624, 8, 15.45, -9.04, 0.29658, 9, 4.85, -7.42, 0.28939, 10, -5.7, -9.03, 0.30718, 5, 6, 30.62, -9.85, 0.07702, 7, 17.48, -9.1, 0.24263, 8, 5.7, -8.12, 0.38398, 9, -4.82, -8.96, 0.1956, 10, -15.4, -10.38, 0.10078, 5, 6, 21.24, -7.79, 0.26455, 7, 8.07, -7.16, 0.3687, 8, -3.77, -6.5, 0.27118, 9, -14.39, -9.75, 0.09008, 10, -24.98, -10.99, 0.00548, 4, 6, 12.08, -6.11, 0.56199, 7, -1.12, -5.6, 0.30291, 8, -13.01, -5.26, 0.12924, 9, -23.65, -10.84, 0.00586, 3, 6, 2.92, -5.09, 0.8189, 7, -10.28, -4.7, 0.16652, 8, -22.2, -4.68, 0.01457, 2, 6, -6.29, -4.29, 0.96471, 7, -19.51, -4.02, 0.03529, 1, 6, 3.6, 1.5, 1, 2, 6, 12.9, 0.59, 0.80129, 7, -0.38, 1.11, 0.19871, 1, 7, 8.96, -0.6, 1, 2, 8, 6, -0.37, 0.9997, 9, -6.46, -1.38, 0.0003, 3, 8, 15.48, -1.36, 0.01251, 9, 2.97, 0.02, 0.98697, 10, -7.44, -1.55, 0.00053, 1, 10, 1.39, 0.28, 1], "hull": 18, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 2, 0, 0, 34, 16, 18, 18, 20], "width": 65, "height": 13}}, "yu4": {"yu1": {"type": "mesh", "uvs": [1, 0.53794, 1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0.52547, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0, 0.14485, 0.52547, 0.28859, 0.52859, 0.43153, 0.52547, 0.57848, 0.51611, 0.72865, 0.54417, 0.86677, 0.55041], "triangles": [18, 7, 9, 7, 8, 9, 18, 12, 19, 9, 11, 18, 9, 10, 11, 18, 11, 12, 6, 19, 5, 7, 18, 6, 6, 18, 19, 19, 13, 20, 19, 12, 13, 5, 20, 4, 5, 19, 20, 20, 13, 14, 4, 21, 3, 3, 21, 22, 4, 20, 21, 21, 15, 22, 22, 15, 16, 20, 14, 21, 21, 14, 15, 2, 23, 1, 23, 0, 1, 3, 22, 2, 2, 22, 23, 17, 0, 23, 23, 22, 16, 23, 16, 17], "vertices": [1, 13, -4.24, 0.53, 1, 1, 13, -3.43, 8.34, 1, 1, 13, 5.95, 7.38, 1, 2, 13, 15.32, 6.39, 0.9017, 14, 1.97, 6.94, 0.0983, 3, 13, 24.72, 5.37, 0.08033, 14, 11.37, 6.04, 0.53855, 15, -0.93, 6.81, 0.38111, 2, 14, 20.79, 5.23, 0.00102, 15, 8.52, 6.32, 0.99898, 2, 15, 17.89, 5.81, 0.28593, 16, 3.52, 7.57, 0.71407, 3, 15, 27.29, 5.27, 0.00144, 16, 12.76, 9.39, 0.69736, 17, 2.52, 7.63, 0.3012, 2, 16, 22, 11.24, 0.09186, 17, 11.8, 9.31, 0.90814, 2, 16, 23.6, 3.33, 0.0018, 17, 13.24, 1.38, 0.9982, 1, 17, 14.85, -7.41, 1, 2, 16, 16.12, -7.29, 0.00709, 17, 5.57, -9.1, 0.99291, 3, 15, 16.96, -11.2, 0.01551, 16, 6.85, -9.14, 0.48386, 17, -3.73, -10.78, 0.50063, 4, 14, 19.29, -11.7, 0.04502, 15, 7.6, -10.66, 0.47709, 16, -2.35, -10.94, 0.41688, 17, -12.97, -12.41, 0.06101, 4, 14, 9.95, -10.9, 0.59696, 15, -1.76, -10.18, 0.3562, 16, -11.54, -12.81, 0.04655, 17, -22.18, -14.1, 0.00029, 4, 13, 13.68, -10.64, 0.04692, 14, 0.55, -10.11, 0.93751, 15, -11.18, -9.72, 0.01546, 16, -20.78, -14.7, 0.00011, 2, 13, 4.23, -9.6, 0.70108, 14, -8.92, -9.19, 0.29892, 2, 13, -5.18, -8.57, 0.98052, 14, -18.34, -8.29, 0.01948, 1, 17, 3.84, -0.34, 1, 2, 16, 4.88, -0.35, 0.91525, 17, -5.53, -1.95, 0.08475, 4, 14, 19.91, -2.79, 0.00055, 15, 7.91, -1.72, 0.9213, 16, -4.27, -2.21, 0.07444, 17, -14.72, -3.65, 0.00371, 3, 14, 10.21, -2.12, 0.88521, 15, -1.8, -1.39, 0.11204, 16, -13.76, -4.3, 0.00275, 1, 14, 0.37, -0.82, 1, 2, 13, 4.53, -0.16, 0.99773, 14, -8.74, 0.25, 0.00227], "hull": 18, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 16, 18, 18, 20, 2, 0, 0, 34], "width": 66, "height": 17}, "yu3": {"type": "mesh", "uvs": [1, 0.53091, 1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0.50517, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0, 0.85606, 0.5103, 0.71278, 0.5103, 0.5695, 0.5103, 0.42932, 0.567, 0.28398, 0.56185, 0.14483, 0.51032], "triangles": [9, 10, 11, 23, 11, 12, 9, 11, 23, 22, 23, 12, 7, 8, 9, 23, 7, 9, 7, 23, 22, 6, 22, 21, 7, 22, 6, 21, 13, 20, 12, 21, 22, 21, 12, 13, 5, 6, 21, 5, 21, 4, 20, 13, 14, 20, 14, 19, 4, 20, 19, 21, 20, 4, 18, 15, 16, 19, 14, 15, 19, 15, 18, 18, 16, 17, 0, 18, 17, 3, 19, 18, 4, 19, 3, 2, 18, 0, 3, 18, 2, 2, 0, 1], "vertices": [1, 13, -5.7, 2.57, 1, 2, 13, -5.15, 8.66, 0.95539, 14, -18.54, 8.95, 0.04461, 3, 13, 4.19, 7.84, 0.79985, 14, -9.18, 8.25, 0.1827, 15, -21.55, 8.3, 0.01745, 4, 13, 13.6, 6.81, 0.53319, 14, 0.23, 7.34, 0.32185, 15, -12.1, 7.71, 0.14482, 16, -26, 1.95, 0.00013, 4, 13, 22.87, 5.08, 0.24465, 14, 9.53, 5.73, 0.37178, 15, -2.76, 6.43, 0.28469, 16, -16.63, 3.03, 0.09887, 5, 13, 31.91, 3.05, 0.06686, 14, 18.6, 3.81, 0.23475, 15, 6.37, 4.83, 0.38966, 16, -7.39, 3.75, 0.23652, 17, -17.73, 2.37, 0.07221, 5, 13, 40.72, 1.72, 0.00018, 14, 27.42, 2.6, 0.0956, 15, 15.23, 3.92, 0.27479, 16, 1.42, 5.08, 0.3739, 17, -8.9, 3.54, 0.25552, 4, 14, 36.21, 2.81, 0.00106, 15, 24.01, 4.43, 0.13492, 16, 9.79, 7.76, 0.31407, 17, -0.48, 6.06, 0.54995, 3, 15, 32.84, 6.05, 0.0125, 16, 17.94, 11.52, 0.17642, 17, 7.74, 9.67, 0.81108, 2, 16, 20.39, 5.8, 0.0602, 17, 10.08, 3.9, 0.9398, 3, 15, 34.79, -6.78, 0.02727, 16, 23.02, -0.42, 0.12095, 17, 12.6, -2.37, 0.85178, 4, 14, 36.99, -10.13, 0.00515, 15, 25.24, -8.47, 0.15464, 16, 14.19, -4.43, 0.22647, 17, 3.69, -6.21, 0.61374, 5, 13, 40.32, -11.24, 0.0006, 14, 27.19, -10.36, 0.10624, 15, 15.45, -9.04, 0.29658, 16, 4.85, -7.42, 0.28939, 17, -5.7, -9.03, 0.30718, 5, 13, 30.62, -9.85, 0.07702, 14, 17.48, -9.1, 0.24263, 15, 5.7, -8.12, 0.38398, 16, -4.82, -8.96, 0.1956, 17, -15.4, -10.38, 0.10078, 5, 13, 21.24, -7.79, 0.26455, 14, 8.07, -7.16, 0.3687, 15, -3.77, -6.5, 0.27118, 16, -14.39, -9.75, 0.09008, 17, -24.98, -10.99, 0.00548, 4, 13, 12.08, -6.11, 0.56199, 14, -1.12, -5.6, 0.30291, 15, -13.01, -5.26, 0.12924, 16, -23.65, -10.84, 0.00586, 3, 13, 2.92, -5.09, 0.8189, 14, -10.28, -4.7, 0.16652, 15, -22.2, -4.68, 0.01457, 2, 13, -6.29, -4.29, 0.96471, 14, -19.51, -4.02, 0.03529, 1, 13, 3.6, 1.5, 1, 2, 13, 12.9, 0.59, 0.80129, 14, -0.38, 1.11, 0.19871, 1, 14, 8.96, -0.6, 1, 2, 15, 6, -0.37, 0.9997, 16, -6.46, -1.38, 0.0003, 3, 15, 15.48, -1.36, 0.01251, 16, 2.97, 0.02, 0.98697, 17, -7.44, -1.55, 0.00053, 1, 17, 1.39, 0.28, 1], "hull": 18, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 2, 0, 0, 34, 16, 18, 18, 20], "width": 65, "height": 13}}, "yu5": {"yu1": {"type": "mesh", "uvs": [1, 0.53794, 1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0.52547, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0, 0.14485, 0.52547, 0.28859, 0.52859, 0.43153, 0.52547, 0.57848, 0.51611, 0.72865, 0.54417, 0.86677, 0.55041], "triangles": [18, 7, 9, 7, 8, 9, 18, 12, 19, 9, 11, 18, 9, 10, 11, 18, 11, 12, 6, 19, 5, 7, 18, 6, 6, 18, 19, 19, 13, 20, 19, 12, 13, 5, 20, 4, 5, 19, 20, 20, 13, 14, 4, 21, 3, 3, 21, 22, 4, 20, 21, 21, 15, 22, 22, 15, 16, 20, 14, 21, 21, 14, 15, 2, 23, 1, 23, 0, 1, 3, 22, 2, 2, 22, 23, 17, 0, 23, 23, 22, 16, 23, 16, 17], "vertices": [1, 19, -4.24, 0.53, 1, 1, 19, -3.43, 8.34, 1, 1, 19, 5.95, 7.38, 1, 2, 19, 15.32, 6.39, 0.9017, 20, 1.97, 6.94, 0.0983, 3, 19, 24.72, 5.37, 0.08033, 20, 11.37, 6.04, 0.53855, 21, -0.93, 6.81, 0.38111, 2, 20, 20.79, 5.23, 0.00102, 21, 8.52, 6.32, 0.99898, 2, 21, 17.89, 5.81, 0.28593, 22, 3.52, 7.57, 0.71407, 3, 21, 27.29, 5.27, 0.00144, 22, 12.76, 9.39, 0.69736, 23, 2.52, 7.63, 0.3012, 2, 22, 22, 11.24, 0.09186, 23, 11.8, 9.31, 0.90814, 2, 22, 23.6, 3.33, 0.0018, 23, 13.24, 1.38, 0.9982, 1, 23, 14.85, -7.41, 1, 2, 22, 16.12, -7.29, 0.00709, 23, 5.57, -9.1, 0.99291, 3, 21, 16.96, -11.2, 0.01551, 22, 6.85, -9.14, 0.48386, 23, -3.73, -10.78, 0.50063, 4, 20, 19.29, -11.7, 0.04502, 21, 7.6, -10.66, 0.47709, 22, -2.35, -10.94, 0.41688, 23, -12.97, -12.41, 0.06101, 4, 20, 9.95, -10.9, 0.59696, 21, -1.76, -10.18, 0.3562, 22, -11.54, -12.81, 0.04655, 23, -22.18, -14.1, 0.00029, 4, 19, 13.68, -10.64, 0.04692, 20, 0.55, -10.11, 0.93751, 21, -11.18, -9.72, 0.01546, 22, -20.78, -14.7, 0.00011, 2, 19, 4.23, -9.6, 0.70108, 20, -8.92, -9.19, 0.29892, 2, 19, -5.18, -8.57, 0.98052, 20, -18.34, -8.29, 0.01948, 1, 23, 3.84, -0.34, 1, 2, 22, 4.88, -0.35, 0.91525, 23, -5.53, -1.95, 0.08475, 4, 20, 19.91, -2.79, 0.00055, 21, 7.91, -1.72, 0.9213, 22, -4.27, -2.21, 0.07444, 23, -14.72, -3.65, 0.00371, 3, 20, 10.21, -2.12, 0.88521, 21, -1.8, -1.39, 0.11204, 22, -13.76, -4.3, 0.00275, 1, 20, 0.37, -0.82, 1, 2, 19, 4.53, -0.16, 0.99773, 20, -8.74, 0.25, 0.00227], "hull": 18, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 16, 18, 18, 20, 2, 0, 0, 34], "width": 66, "height": 17}, "yu3": {"type": "mesh", "uvs": [1, 0.53091, 1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0.50517, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0, 0.85606, 0.5103, 0.71278, 0.5103, 0.5695, 0.5103, 0.42932, 0.567, 0.28398, 0.56185, 0.14483, 0.51032], "triangles": [9, 10, 11, 23, 11, 12, 9, 11, 23, 22, 23, 12, 7, 8, 9, 23, 7, 9, 7, 23, 22, 6, 22, 21, 7, 22, 6, 21, 13, 20, 12, 21, 22, 21, 12, 13, 5, 6, 21, 5, 21, 4, 20, 13, 14, 20, 14, 19, 4, 20, 19, 21, 20, 4, 18, 15, 16, 19, 14, 15, 19, 15, 18, 18, 16, 17, 0, 18, 17, 3, 19, 18, 4, 19, 3, 2, 18, 0, 3, 18, 2, 2, 0, 1], "vertices": [1, 19, -5.7, 2.57, 1, 2, 19, -5.15, 8.66, 0.95539, 20, -18.54, 8.95, 0.04461, 3, 19, 4.19, 7.84, 0.79985, 20, -9.18, 8.25, 0.1827, 21, -21.55, 8.3, 0.01745, 4, 19, 13.6, 6.81, 0.53319, 20, 0.23, 7.34, 0.32185, 21, -12.1, 7.71, 0.14482, 22, -26, 1.95, 0.00013, 4, 19, 22.87, 5.08, 0.24465, 20, 9.53, 5.73, 0.37178, 21, -2.76, 6.43, 0.28469, 22, -16.63, 3.03, 0.09887, 5, 19, 31.91, 3.05, 0.06686, 20, 18.6, 3.81, 0.23475, 21, 6.37, 4.83, 0.38966, 22, -7.39, 3.75, 0.23652, 23, -17.73, 2.37, 0.07221, 5, 19, 40.72, 1.72, 0.00018, 20, 27.42, 2.6, 0.0956, 21, 15.23, 3.92, 0.27479, 22, 1.42, 5.08, 0.3739, 23, -8.9, 3.54, 0.25552, 4, 20, 36.21, 2.81, 0.00106, 21, 24.01, 4.43, 0.13492, 22, 9.79, 7.76, 0.31407, 23, -0.48, 6.06, 0.54995, 3, 21, 32.84, 6.05, 0.0125, 22, 17.94, 11.52, 0.17642, 23, 7.74, 9.67, 0.81108, 2, 22, 20.39, 5.8, 0.0602, 23, 10.08, 3.9, 0.9398, 3, 21, 34.79, -6.78, 0.02727, 22, 23.02, -0.42, 0.12095, 23, 12.6, -2.37, 0.85178, 4, 20, 36.99, -10.13, 0.00515, 21, 25.24, -8.47, 0.15464, 22, 14.19, -4.43, 0.22647, 23, 3.69, -6.21, 0.61374, 5, 19, 40.32, -11.24, 0.0006, 20, 27.19, -10.36, 0.10624, 21, 15.45, -9.04, 0.29658, 22, 4.85, -7.42, 0.28939, 23, -5.7, -9.03, 0.30718, 5, 19, 30.62, -9.85, 0.07702, 20, 17.48, -9.1, 0.24263, 21, 5.7, -8.12, 0.38398, 22, -4.82, -8.96, 0.1956, 23, -15.4, -10.38, 0.10078, 5, 19, 21.24, -7.79, 0.26455, 20, 8.07, -7.16, 0.3687, 21, -3.77, -6.5, 0.27118, 22, -14.39, -9.75, 0.09008, 23, -24.98, -10.99, 0.00548, 4, 19, 12.08, -6.11, 0.56199, 20, -1.12, -5.6, 0.30291, 21, -13.01, -5.26, 0.12924, 22, -23.65, -10.84, 0.00586, 3, 19, 2.92, -5.09, 0.8189, 20, -10.28, -4.7, 0.16652, 21, -22.2, -4.68, 0.01457, 2, 19, -6.29, -4.29, 0.96471, 20, -19.51, -4.02, 0.03529, 1, 19, 3.6, 1.5, 1, 2, 19, 12.9, 0.59, 0.80129, 20, -0.38, 1.11, 0.19871, 1, 20, 8.96, -0.6, 1, 2, 21, 6, -0.37, 0.9997, 22, -6.46, -1.38, 0.0003, 3, 21, 15.48, -1.36, 0.01251, 22, 2.97, 0.02, 0.98697, 23, -7.44, -1.55, 0.00053, 1, 23, 1.39, 0.28, 1], "hull": 18, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 2, 0, 0, 34, 16, 18, 18, 20], "width": 65, "height": 13}}, "shuihua": {"shuihua": {"x": -27.07, "y": -17.04, "width": 34, "height": 26}}, "jljl1": {"jljl1": {"type": "mesh", "uvs": [0.25296, 0.25464, 0.50896, 0.25284, 0.78956, 0.30926, 0.99754, 0.35107, 0.99675, 0.5, 0.99746, 0.75977, 0.99835, 1, 0.76018, 1, 0.5, 1, 0.22733, 1, 0.00239, 1, 0.00147, 0.75794, 0.0047, 0.5, 0.00537, 0.25638, 0.5, 0.5, 0.5, 0.76865, 0.2493, 0.76326, 0.24822, 0.5, 0.76991, 0.5, 0.76373, 0.77718], "triangles": [17, 13, 0, 12, 13, 17, 14, 0, 1, 14, 1, 2, 17, 0, 14, 18, 14, 2, 18, 2, 3, 4, 18, 3, 16, 17, 14, 11, 12, 17, 16, 11, 17, 15, 16, 14, 18, 15, 14, 19, 15, 18, 18, 4, 5, 19, 18, 5, 9, 10, 11, 16, 9, 11, 8, 15, 19, 16, 15, 8, 9, 16, 8, 7, 8, 19, 19, 5, 6, 7, 19, 6], "vertices": [-346.76, 329.73, -160.78, 115.65, -20.49, 128.08, 145.68, -51.53, 128.47, -113.53, 95.77, -294.1, 95.62, -450.55, -30.92, -535.97, -173.86, -505.66, -327.27, -272.77, -568.01, 47.49, -560.02, 318.2, -546.39, 430.23, -547.45, 677, -152.23, -90.45, -155.99, -286.1, -323.89, -97.86, -334.32, 78.29, -5.65, -147.77, -23.09, -350.56], "hull": 14, "edges": [2, 28, 28, 30, 30, 16, 16, 18, 32, 30, 18, 32, 34, 28, 32, 34, 0, 2, 34, 0, 4, 2, 36, 28, 4, 36, 36, 38, 14, 16, 38, 14, 26, 0, 24, 34, 26, 24, 22, 32, 24, 22, 18, 20, 22, 20, 6, 4, 8, 36, 6, 8, 12, 14, 12, 10, 8, 10], "width": 128, "height": 128}, "jljl2": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl3": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl4": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl5": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl6": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl7": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl8": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl9": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl10": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl11": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl12": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl13": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl14": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl15": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}, "jljl16": {"type": "<PERSON><PERSON><PERSON>", "parent": "jljl1", "width": 128, "height": 128}}, "1": {"1": {"type": "mesh", "uvs": [1, 0.74109, 1, 1, 0.5, 1, 0, 1, 0, 0.75336, 0, 0.5, 0, 0.2503, 0, 0, 0.5, 0, 1, 0, 1, 0.24212, 1, 0.5, 0.5, 0.5, 0.50615, 0.23803, 0.49388, 0.74518], "triangles": [13, 8, 9, 13, 9, 10, 6, 7, 8, 6, 8, 13, 12, 5, 6, 13, 12, 6, 13, 10, 11, 12, 13, 11, 12, 11, 0, 14, 5, 12, 14, 12, 0, 4, 5, 14, 3, 4, 14, 2, 14, 0, 3, 14, 2, 2, 0, 1], "vertices": [408, 103.47, 713.97, -61.46, 356.97, -209.88, -49.48, -273.03, -221.54, -84.87, -286.89, 222.49, -291.94, 637.14, -471.37, 907.27, -166.75, 971.65, 36.94, 1020.57, 145.57, 709.09, 198.47, 344.54, -42.06, 262.88, -40.85, 645.03, 120.48, 4.35], "hull": 12, "edges": [2, 4, 4, 6, 14, 16, 16, 18, 10, 12, 12, 14, 18, 20, 20, 22, 2, 0, 0, 22, 6, 8, 8, 10], "width": 128, "height": 128}, "2": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "3": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "4": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "5": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "6": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "7": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "8": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "9": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "10": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "11": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "12": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "13": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "14": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "15": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}, "16": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 128, "height": 128}}, "bone6": {"bone6": {"type": "path", "lengths": [8.94, 19.27, 29.88, 41.5, 54.03, 71.55, 89.87, 104.27, 124.93, 144.06, 163.81, 177.43, 241.38, 286.63, 332.63, 364.13, 382.98, 403.05, 418.16, 430.97, 450.22, 469.9, 522.21], "vertexCount": 69, "vertices": [-112.77, 56.31, -109.91, 56.16, -108.27, 56.07, -103.92, 56.85, -100.99, 56.57, -98.06, 56.3, -94.07, 54.53, -90.93, 54.28, -87.79, 54.03, -85.45, 54.6, -80.33, 54.63, -70.73, 54.67, -72.31, 53.61, -68.91, 52.96, -65.52, 52.31, -60.92, 55.13, -56.66, 55.27, -52.41, 55.42, -44.92, 52.85, -39.29, 53.24, -33.67, 53.62, -26.79, 54.84, -21.03, 54.17, -15.26, 53.5, -12.22, 50.72, -7.03, 51.07, -1.84, 51.41, 7.1, 58.39, 12.51, 57.27, 17.91, 56.14, 29.69, 57.07, 29.01, 50.87, 28.33, 44.67, 22, 43.9, 12.29, 45.25, 3.81, 46.43, 5.23, 46.29, -1.25, 46.76, -15.98, 47.83, -9.61, 87.08, -13.74, 107.1, -17.9, 127.25, -26.62, 143.76, -36.42, 143.58, -48.67, 143.36, -57.28, 119.45, -59.99, 106.57, -62.81, 93.21, -62.79, 84.58, -64.55, 75.41, -66.31, 66.24, -72.39, 64.42, -77.85, 64.95, -83.1, 65.46, -88.46, 66.32, -97.83, 66.82, -99.77, 66.93, -106.34, 65.07, -112.82, 65.06, -117.16, 65.06, -120.75, 67.01, -125.49, 66.77, -133.03, 66.4, -140.09, 65.07, -144.65, 64.96, -157.64, 64.62, -161.3, 64.92, -164.33, 65.1, -169.9, 65.44]}}, "ly17": {"ly17": {"type": "mesh", "uvs": [1, 1, 0.52055, 1, 0, 1, 0, 0.5, 0, 0, 0.53746, 0, 1, 0, 1, 0.5, 0.5354, 0.5097], "triangles": [3, 4, 5, 5, 6, 7, 8, 3, 5, 7, 8, 5, 2, 3, 8, 1, 2, 8, 8, 7, 0, 1, 8, 0], "vertices": [-214.75, 216.12, -299.61, 216.12, -391.75, 216.12, -391.75, 373.54, -391.75, 530.97, -296.62, 530.97, -214.75, 530.97, -214.75, 373.54, -296.98, 370.49], "hull": 8, "edges": [4, 6, 6, 8, 12, 14, 14, 0, 8, 10, 10, 12, 0, 2, 2, 4], "width": 52, "height": 92}}, "ly18": {"ly17": {"type": "mesh", "uvs": [1, 1, 0.52055, 1, 0, 1, 0, 0.5, 0, 0, 0.53746, 0, 1, 0, 1, 0.5, 0.5354, 0.5097], "triangles": [3, 4, 5, 5, 6, 7, 8, 3, 5, 7, 8, 5, 2, 3, 8, 1, 2, 8, 8, 7, 0, 1, 8, 0], "vertices": [-214.75, 216.12, -299.61, 216.12, -391.75, 216.12, -391.75, 373.54, -391.75, 530.97, -296.62, 530.97, -214.75, 530.97, -214.75, 373.54, -296.98, 370.49], "hull": 8, "edges": [4, 6, 6, 8, 12, 14, 14, 0, 8, 10, 10, 12, 0, 2, 2, 4], "width": 52, "height": 92}}, "bone8": {"bone6": {"type": "path", "lengths": [7.66, 19.18, 35.66, 51.04, 65.04, 80.89, 99.74, 123.35, 146.45, 165.17, 182.04, 201.74, 224.82, 253.02, 274.9, 286.72, 297.61, 310.76, 325.87, 338.69, 357.93, 377.62, 428.57], "vertexCount": 69, "vertices": [12.16, 51.82, 14.96, 51.19, 16.56, 50.83, 14.22, 44.97, 17.06, 44.21, 19.91, 43.45, 22.77, 42.23, 24.73, 36.38, 25.72, 33.45, 35.15, 31.5, 38.61, 27.73, 46.84, 18.78, 47.6, 25.41, 52.24, 24.4, 55.61, 23.66, 58.74, 29.15, 65.7, 26.45, 71.43, 24.22, 72.65, 19.74, 79.07, 18.15, 87.89, 15.96, 92.2, 20.04, 97.78, 18.42, 103.35, 16.8, 109.24, -0.36, 115.67, 6.54, 119.21, 10.35, 127.24, 0.23, 137.99, 6.92, 142.68, 9.83, 148.9, 12.63, 151.39, 19.43, 153.54, 25.28, 152.85, 28.27, 143.49, 31.21, 135.32, 33.77, 139.89, 45.57, 131.18, 45.02, 116.43, 44.09, 118.68, 40.18, 108.53, 43.76, 89.13, 50.61, 96.53, 58.44, 84.1, 54.98, 77.63, 53.18, 74.82, 62.37, 63.1, 56.38, 59.07, 54.32, 58.85, 54.38, 51.62, 54.88, 42.65, 55.5, 46.78, 56.83, 41.48, 58.25, 36.39, 59.63, 38.1, 54.12, 28.95, 56.17, 27.05, 56.59, 20.26, 55.85, 13.87, 56.92, 9.59, 57.63, 6.37, 60.15, 1.66, 60.7, -5.83, 61.59, -13.02, 61.44, -17.53, 62.08, -30.4, 63.9, -33.97, 64.8, -36.92, 65.48, -42.36, 66.74]}}}}], "animations": {"pu_bu": {"slots": {"ly17": {"color": [{"color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 1.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 2.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 3, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 3.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 4.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 5, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 5.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 6.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 7, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 7.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 8.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 9, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 9.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 10.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 11, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 11.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 12.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 13, "color": "91f7ff00", "curve": 0.25, "c3": 0.75}, {"time": 13.5, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 14, "color": "91f7ff00"}]}, "ly18": {"color": [{"color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 1.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 2.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 3, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 3.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 4, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 4.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 5, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 5.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 6, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 6.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 7, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 7.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 8, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 8.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 9, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 9.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 10, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 10.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 11, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 11.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 11.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 12, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 12.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 12.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 13, "color": "91f7ff6f", "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 13.0333, "color": "91f7ff71", "curve": 0.25, "c3": 0.75}, {"time": 13.5333, "color": "91f7ff00", "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 14, "color": "91f7ff6f"}]}, "jljl1": {"attachment": [{"time": 0.1, "name": "jljl2"}, {"time": 0.2, "name": "jljl3"}, {"time": 0.2667, "name": "jljl8"}, {"time": 0.3667, "name": "jljl9"}, {"time": 0.4667, "name": "jljl10"}, {"time": 0.5667, "name": "jljl11"}, {"time": 0.6333, "name": "jljl12"}, {"time": 0.7333, "name": "jljl13"}, {"time": 0.8333, "name": "jljl14"}, {"time": 0.9333, "name": "jljl15"}, {"time": 1, "name": "jljl1"}, {"time": 1.1, "name": "jljl2"}, {"time": 1.2, "name": "jljl3"}, {"time": 1.2667, "name": "jljl8"}, {"time": 1.3667, "name": "jljl9"}, {"time": 1.4667, "name": "jljl10"}, {"time": 1.5667, "name": "jljl11"}, {"time": 1.6333, "name": "jljl12"}, {"time": 1.7333, "name": "jljl13"}, {"time": 1.8333, "name": "jljl14"}, {"time": 1.9333, "name": "jljl15"}, {"time": 2, "name": "jljl1"}, {"time": 2.1, "name": "jljl2"}, {"time": 2.2, "name": "jljl3"}, {"time": 2.2667, "name": "jljl8"}, {"time": 2.3667, "name": "jljl9"}, {"time": 2.4667, "name": "jljl10"}, {"time": 2.5667, "name": "jljl11"}, {"time": 2.6333, "name": "jljl12"}, {"time": 2.7333, "name": "jljl13"}, {"time": 2.8333, "name": "jljl14"}, {"time": 2.9333, "name": "jljl15"}, {"time": 3, "name": "jljl1"}, {"time": 3.1, "name": "jljl2"}, {"time": 3.2, "name": "jljl3"}, {"time": 3.2667, "name": "jljl8"}, {"time": 3.3667, "name": "jljl9"}, {"time": 3.4667, "name": "jljl10"}, {"time": 3.5667, "name": "jljl11"}, {"time": 3.6333, "name": "jljl12"}, {"time": 3.7333, "name": "jljl13"}, {"time": 3.8333, "name": "jljl14"}, {"time": 3.9333, "name": "jljl15"}, {"time": 4, "name": "jljl1"}, {"time": 4.1, "name": "jljl2"}, {"time": 4.2, "name": "jljl3"}, {"time": 4.2667, "name": "jljl8"}, {"time": 4.3667, "name": "jljl9"}, {"time": 4.4667, "name": "jljl10"}, {"time": 4.5667, "name": "jljl11"}, {"time": 4.6333, "name": "jljl12"}, {"time": 4.7333, "name": "jljl13"}, {"time": 4.8333, "name": "jljl14"}, {"time": 4.9333, "name": "jljl15"}, {"time": 5, "name": "jljl1"}, {"time": 5.1, "name": "jljl2"}, {"time": 5.2, "name": "jljl3"}, {"time": 5.2667, "name": "jljl8"}, {"time": 5.3667, "name": "jljl9"}, {"time": 5.4667, "name": "jljl10"}, {"time": 5.5667, "name": "jljl11"}, {"time": 5.6333, "name": "jljl12"}, {"time": 5.7333, "name": "jljl13"}, {"time": 5.8333, "name": "jljl14"}, {"time": 5.9333, "name": "jljl15"}, {"time": 6, "name": "jljl1"}, {"time": 6.1, "name": "jljl2"}, {"time": 6.2, "name": "jljl3"}, {"time": 6.2667, "name": "jljl8"}, {"time": 6.3667, "name": "jljl9"}, {"time": 6.4667, "name": "jljl10"}, {"time": 6.5667, "name": "jljl11"}, {"time": 6.6333, "name": "jljl12"}, {"time": 6.7333, "name": "jljl13"}, {"time": 6.8333, "name": "jljl14"}, {"time": 6.9333, "name": "jljl15"}, {"time": 7, "name": "jljl1"}, {"time": 7.1, "name": "jljl2"}, {"time": 7.2, "name": "jljl3"}, {"time": 7.2667, "name": "jljl8"}, {"time": 7.3667, "name": "jljl9"}, {"time": 7.4667, "name": "jljl10"}, {"time": 7.5667, "name": "jljl11"}, {"time": 7.6333, "name": "jljl12"}, {"time": 7.7333, "name": "jljl13"}, {"time": 7.8333, "name": "jljl14"}, {"time": 7.9333, "name": "jljl15"}, {"time": 8, "name": "jljl1"}, {"time": 8.1, "name": "jljl2"}, {"time": 8.2, "name": "jljl3"}, {"time": 8.2667, "name": "jljl8"}, {"time": 8.3667, "name": "jljl9"}, {"time": 8.4667, "name": "jljl10"}, {"time": 8.5667, "name": "jljl11"}, {"time": 8.6333, "name": "jljl12"}, {"time": 8.7333, "name": "jljl13"}, {"time": 8.8333, "name": "jljl14"}, {"time": 8.9333, "name": "jljl15"}, {"time": 9, "name": "jljl1"}, {"time": 9.1, "name": "jljl2"}, {"time": 9.2, "name": "jljl3"}, {"time": 9.2667, "name": "jljl8"}, {"time": 9.3667, "name": "jljl9"}, {"time": 9.4667, "name": "jljl10"}, {"time": 9.5667, "name": "jljl11"}, {"time": 9.6333, "name": "jljl12"}, {"time": 9.7333, "name": "jljl13"}, {"time": 9.8333, "name": "jljl14"}, {"time": 9.9333, "name": "jljl15"}, {"time": 10, "name": "jljl1"}, {"time": 10.1, "name": "jljl2"}, {"time": 10.2, "name": "jljl3"}, {"time": 10.2667, "name": "jljl8"}, {"time": 10.3667, "name": "jljl9"}, {"time": 10.4667, "name": "jljl10"}, {"time": 10.5667, "name": "jljl11"}, {"time": 10.6333, "name": "jljl12"}, {"time": 10.7333, "name": "jljl13"}, {"time": 10.8333, "name": "jljl14"}, {"time": 10.9333, "name": "jljl15"}, {"time": 11, "name": "jljl1"}, {"time": 11.1, "name": "jljl2"}, {"time": 11.2, "name": "jljl3"}, {"time": 11.2667, "name": "jljl8"}, {"time": 11.3667, "name": "jljl9"}, {"time": 11.4667, "name": "jljl10"}, {"time": 11.5667, "name": "jljl11"}, {"time": 11.6333, "name": "jljl12"}, {"time": 11.7333, "name": "jljl13"}, {"time": 11.8333, "name": "jljl14"}, {"time": 11.9333, "name": "jljl15"}, {"time": 12, "name": "jljl1"}, {"time": 12.1, "name": "jljl2"}, {"time": 12.2, "name": "jljl3"}, {"time": 12.2667, "name": "jljl8"}, {"time": 12.3667, "name": "jljl9"}, {"time": 12.4667, "name": "jljl10"}, {"time": 12.5667, "name": "jljl11"}, {"time": 12.6333, "name": "jljl12"}, {"time": 12.7333, "name": "jljl13"}, {"time": 12.8333, "name": "jljl14"}, {"time": 12.9333, "name": "jljl15"}, {"time": 13, "name": "jljl1"}, {"time": 13.1, "name": "jljl2"}, {"time": 13.2, "name": "jljl3"}, {"time": 13.2667, "name": "jljl8"}, {"time": 13.3667, "name": "jljl9"}, {"time": 13.4667, "name": "jljl10"}, {"time": 13.5667, "name": "jljl11"}, {"time": 13.6333, "name": "jljl12"}, {"time": 13.7333, "name": "jljl13"}, {"time": 13.8333, "name": "jljl14"}, {"time": 13.9333, "name": "jljl15"}, {"time": 14, "name": "jljl1"}]}, "yu4": {"color": [{"color": "ffffff00"}, {"time": 2.6667, "color": "40404000", "curve": "stepped"}, {"time": 3.2667, "color": "40404000"}, {"time": 3.6667, "color": "40404047"}, {"time": 4.2, "color": "9cc9fc8b"}, {"time": 9.2333, "color": "b0c7ff7a"}, {"time": 10.7, "color": "40404000"}], "attachment": [{"name": "yu1"}]}, "1": {"attachment": [{"time": 0.0667, "name": "2"}, {"time": 0.1333, "name": "3"}, {"time": 0.2, "name": "4"}, {"time": 0.2667, "name": "5"}, {"time": 0.3333, "name": "6"}, {"time": 0.4, "name": "7"}, {"time": 0.4667, "name": "8"}, {"time": 0.5333, "name": "9"}, {"time": 0.6, "name": "10"}, {"time": 0.6667, "name": "11"}, {"time": 0.7333, "name": "12"}, {"time": 0.8, "name": "13"}, {"time": 0.8667, "name": "14"}, {"time": 0.9333, "name": "15"}, {"time": 1, "name": "1"}, {"time": 1.0667, "name": "2"}, {"time": 1.1333, "name": "3"}, {"time": 1.2, "name": "4"}, {"time": 1.2667, "name": "5"}, {"time": 1.3333, "name": "6"}, {"time": 1.4, "name": "7"}, {"time": 1.4667, "name": "8"}, {"time": 1.5333, "name": "9"}, {"time": 1.6, "name": "10"}, {"time": 1.6667, "name": "11"}, {"time": 1.7333, "name": "12"}, {"time": 1.8, "name": "13"}, {"time": 1.8667, "name": "14"}, {"time": 1.9333, "name": "15"}, {"time": 2, "name": "1"}, {"time": 2.0667, "name": "2"}, {"time": 2.1333, "name": "3"}, {"time": 2.2, "name": "4"}, {"time": 2.2667, "name": "5"}, {"time": 2.3333, "name": "6"}, {"time": 2.4, "name": "7"}, {"time": 2.4667, "name": "8"}, {"time": 2.5333, "name": "9"}, {"time": 2.6, "name": "10"}, {"time": 2.6667, "name": "11"}, {"time": 2.7333, "name": "12"}, {"time": 2.8, "name": "13"}, {"time": 2.8667, "name": "14"}, {"time": 2.9333, "name": "15"}, {"time": 3, "name": "1"}, {"time": 3.0667, "name": "2"}, {"time": 3.1333, "name": "3"}, {"time": 3.2, "name": "4"}, {"time": 3.2667, "name": "5"}, {"time": 3.3333, "name": "6"}, {"time": 3.4, "name": "7"}, {"time": 3.4667, "name": "8"}, {"time": 3.5333, "name": "9"}, {"time": 3.6, "name": "10"}, {"time": 3.6667, "name": "11"}, {"time": 3.7333, "name": "12"}, {"time": 3.8, "name": "13"}, {"time": 3.8667, "name": "14"}, {"time": 3.9333, "name": "15"}, {"time": 4, "name": "1"}, {"time": 4.0667, "name": "2"}, {"time": 4.1333, "name": "3"}, {"time": 4.2, "name": "4"}, {"time": 4.2667, "name": "5"}, {"time": 4.3333, "name": "6"}, {"time": 4.4, "name": "7"}, {"time": 4.4667, "name": "8"}, {"time": 4.5333, "name": "9"}, {"time": 4.6, "name": "10"}, {"time": 4.6667, "name": "11"}, {"time": 4.7333, "name": "12"}, {"time": 4.8, "name": "13"}, {"time": 4.8667, "name": "14"}, {"time": 4.9333, "name": "15"}, {"time": 5, "name": "1"}, {"time": 5.0667, "name": "2"}, {"time": 5.1333, "name": "3"}, {"time": 5.2, "name": "4"}, {"time": 5.2667, "name": "5"}, {"time": 5.3333, "name": "6"}, {"time": 5.4, "name": "7"}, {"time": 5.4667, "name": "8"}, {"time": 5.5333, "name": "9"}, {"time": 5.6, "name": "10"}, {"time": 5.6667, "name": "11"}, {"time": 5.7333, "name": "12"}, {"time": 5.8, "name": "13"}, {"time": 5.8667, "name": "14"}, {"time": 5.9333, "name": "15"}, {"time": 6, "name": "1"}, {"time": 6.0667, "name": "2"}, {"time": 6.1333, "name": "3"}, {"time": 6.2, "name": "4"}, {"time": 6.2667, "name": "5"}, {"time": 6.3333, "name": "6"}, {"time": 6.4, "name": "7"}, {"time": 6.4667, "name": "8"}, {"time": 6.5333, "name": "9"}, {"time": 6.6, "name": "10"}, {"time": 6.6667, "name": "11"}, {"time": 6.7333, "name": "12"}, {"time": 6.8, "name": "13"}, {"time": 6.8667, "name": "14"}, {"time": 6.9333, "name": "15"}, {"time": 7, "name": "1"}, {"time": 7.0667, "name": "2"}, {"time": 7.1333, "name": "3"}, {"time": 7.2, "name": "4"}, {"time": 7.2667, "name": "5"}, {"time": 7.3333, "name": "6"}, {"time": 7.4, "name": "7"}, {"time": 7.4667, "name": "8"}, {"time": 7.5333, "name": "9"}, {"time": 7.6, "name": "10"}, {"time": 7.6667, "name": "11"}, {"time": 7.7333, "name": "12"}, {"time": 7.8, "name": "13"}, {"time": 7.8667, "name": "14"}, {"time": 7.9333, "name": "15"}, {"time": 8, "name": "1"}, {"time": 8.0667, "name": "2"}, {"time": 8.1333, "name": "3"}, {"time": 8.2, "name": "4"}, {"time": 8.2667, "name": "5"}, {"time": 8.3333, "name": "6"}, {"time": 8.4, "name": "7"}, {"time": 8.4667, "name": "8"}, {"time": 8.5333, "name": "9"}, {"time": 8.6, "name": "10"}, {"time": 8.6667, "name": "11"}, {"time": 8.7333, "name": "12"}, {"time": 8.8, "name": "13"}, {"time": 8.8667, "name": "14"}, {"time": 8.9333, "name": "15"}, {"time": 9, "name": "1"}, {"time": 9.0667, "name": "2"}, {"time": 9.1333, "name": "3"}, {"time": 9.2, "name": "4"}, {"time": 9.2667, "name": "5"}, {"time": 9.3333, "name": "6"}, {"time": 9.4, "name": "7"}, {"time": 9.4667, "name": "8"}, {"time": 9.5333, "name": "9"}, {"time": 9.6, "name": "10"}, {"time": 9.6667, "name": "11"}, {"time": 9.7333, "name": "12"}, {"time": 9.8, "name": "13"}, {"time": 9.8667, "name": "14"}, {"time": 9.9333, "name": "15"}, {"time": 10, "name": "1"}, {"time": 10.0667, "name": "2"}, {"time": 10.1333, "name": "3"}, {"time": 10.2, "name": "4"}, {"time": 10.2667, "name": "5"}, {"time": 10.3333, "name": "6"}, {"time": 10.4, "name": "7"}, {"time": 10.4667, "name": "8"}, {"time": 10.5333, "name": "9"}, {"time": 10.6, "name": "10"}, {"time": 10.6667, "name": "11"}, {"time": 10.7333, "name": "12"}, {"time": 10.8, "name": "13"}, {"time": 10.8667, "name": "14"}, {"time": 10.9333, "name": "15"}, {"time": 11, "name": "1"}, {"time": 11.0667, "name": "2"}, {"time": 11.1333, "name": "3"}, {"time": 11.2, "name": "4"}, {"time": 11.2667, "name": "5"}, {"time": 11.3333, "name": "6"}, {"time": 11.4, "name": "7"}, {"time": 11.4667, "name": "8"}, {"time": 11.5333, "name": "9"}, {"time": 11.6, "name": "10"}, {"time": 11.6667, "name": "11"}, {"time": 11.7333, "name": "12"}, {"time": 11.8, "name": "13"}, {"time": 11.8667, "name": "14"}, {"time": 11.9333, "name": "15"}, {"time": 12, "name": "1"}, {"time": 12.0667, "name": "2"}, {"time": 12.1333, "name": "3"}, {"time": 12.2, "name": "4"}, {"time": 12.2667, "name": "5"}, {"time": 12.3333, "name": "6"}, {"time": 12.4, "name": "7"}, {"time": 12.4667, "name": "8"}, {"time": 12.5333, "name": "9"}, {"time": 12.6, "name": "10"}, {"time": 12.6667, "name": "11"}, {"time": 12.7333, "name": "12"}, {"time": 12.8, "name": "13"}, {"time": 12.8667, "name": "14"}, {"time": 12.9333, "name": "15"}, {"time": 13, "name": "1"}, {"time": 13.0667, "name": "2"}, {"time": 13.1333, "name": "3"}, {"time": 13.2, "name": "4"}, {"time": 13.2667, "name": "5"}, {"time": 13.3333, "name": "6"}, {"time": 13.4, "name": "7"}, {"time": 13.4667, "name": "8"}, {"time": 13.5333, "name": "9"}, {"time": 13.6, "name": "10"}, {"time": 13.6667, "name": "11"}, {"time": 13.7333, "name": "12"}, {"time": 13.8, "name": "13"}, {"time": 13.8667, "name": "14"}, {"time": 13.9333, "name": "15"}, {"time": 14, "name": "1"}]}, "yu5": {"color": [{"color": "ff303064"}, {"time": 1.3333, "color": "ff313163"}, {"time": 1.6333, "color": "40404000", "curve": "stepped"}, {"time": 2.8, "color": "40404000"}, {"time": 3.2, "color": "ff1f1f79"}, {"time": 14, "color": "ff303064"}], "attachment": [{"name": "yu1"}]}, "shuihua": {"attachment": [{"name": null}, {"time": 0.6333, "name": "shuihua"}, {"time": 1.0333, "name": null}, {"time": 1.2, "name": "shuihua"}, {"time": 1.5667, "name": null}]}, "yu3": {"color": [{"color": "40404000", "curve": "stepped"}, {"time": 0.3, "color": "40404000"}, {"time": 0.5, "color": "40404047", "curve": "stepped"}, {"time": 0.6333, "color": "40404047"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.2, "color": "40404047", "curve": "stepped"}, {"time": 2.3333, "color": "40404047"}, {"time": 2.7667, "color": "40404000"}], "attachment": [{"name": "yu1"}]}}, "bones": {"bone21": {"translate": [{"x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 1, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 2, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 3, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 4, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 5, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 6, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 7, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 8, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 9, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 10, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 11, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 12, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 12.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 13, "x": -28.43, "y": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 13.9667, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 14, "x": -28.43, "y": -2.48}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 11, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 12, "curve": 0.25, "c3": 0.75}, {"time": 12.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 13, "curve": 0.25, "c3": 0.75}, {"time": 13.9667, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 14}]}, "bone31": {"translate": [{"x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 0.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 1.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 2, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 2.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 2.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 3, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 3.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 3.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 4, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 4.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 4.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 5, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 5.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 5.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 6, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 6.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 6.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 7, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 7.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 7.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 8, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 8.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 8.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 9, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 9.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 9.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 10, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 10.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 10.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 11, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 11.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 11.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 12, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 12.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 12.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 13, "x": 24.95, "y": -24.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 13.5, "x": 83.42, "y": -47.85, "curve": "stepped"}, {"time": 13.5333, "x": -28.43, "y": -2.48, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 14, "x": 24.95, "y": -24.13}], "scale": [{"x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 0.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 1.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 2, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 2.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 2.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 3, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 3.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 3.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 4, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 4.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 4.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 5, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 5.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 5.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 6, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 6.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 6.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 7, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 7.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 7.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 8, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 8.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 8.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 9, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 9.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 9.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 10, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 10.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 10.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 11, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 11.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 11.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 12, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 12.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 12.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 13, "x": 1.153, "y": 1.153, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 13.5, "x": 1.321, "y": 1.321, "curve": "stepped"}, {"time": 13.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 14, "x": 1.153, "y": 1.153}]}, "bone": {"scale": [{"time": 0.6333, "curve": "stepped"}, {"time": 0.6667, "x": 1.4, "y": 1.4, "curve": "stepped"}, {"time": 1.1667, "x": 1.4, "y": 1.4, "curve": "stepped"}, {"time": 1.2}]}, "shuihua": {"translate": [{"time": 1.1667, "curve": "stepped"}, {"time": 1.2, "x": 58.87}], "scale": [{"x": 0.127, "y": 0.122, "curve": "stepped"}, {"time": 0.6333, "x": 0.127, "y": 0.122}, {"time": 0.7333, "x": 0.924, "y": 0.56}, {"time": 1.0333, "x": 0.127, "y": 0.122, "curve": "stepped"}, {"time": 1.2, "x": 0.127, "y": 0.122}, {"time": 1.3}, {"time": 1.5667, "x": 0.127, "y": 0.122}]}, "bone7": {"scale": [{"time": 3.9333, "curve": "stepped"}, {"time": 4, "x": 1.4, "y": 1.4, "curve": "stepped"}, {"time": 5, "x": 1.4, "y": 1.4, "curve": "stepped"}, {"time": 5.0667}]}, "bone13": {"scale": [{"time": 7.4667}, {"time": 8.5333, "x": 1.12, "y": 1.12}, {"time": 9.5667}]}}, "path": {"bone6": {"position": [{"position": 0.9134, "curve": 0.25, "c3": 0.827, "c4": 0.82}, {"time": 0.6333, "position": 0.7034, "curve": 0.276, "c2": 0.19, "c3": 0.784}, {"time": 1.5667, "position": 0.3776}, {"time": 2.7667, "position": 0.0315}]}, "bone7": {"position": [{"time": 2, "position": 0.9609}, {"time": 11, "position": 0.0469}]}, "bone8": {"position": [{"position": 0.1499}, {"time": 1.6667, "position": 0.031}, {"time": 2.7333, "position": 0.9535}, {"time": 14, "position": 0.1499}]}}, "deform": {"default": {"ly17": {"ly17": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 11, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 12, "curve": 0.25, "c3": 0.75}, {"time": 12.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 13, "curve": 0.25, "c3": 0.75}, {"time": 13.9667, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": 0.25, "c3": 0.75}, {"time": 14}]}, "ly18": {"ly17": [{"offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 0.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 1.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 2, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 2.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 2.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 3, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 3.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 3.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 4, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 4.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 4.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 5, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 5.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 5.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 6, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 6.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 6.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 7, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 7.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 7.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 8, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 8.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 8.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 9, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 9.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 9.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 10, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 10.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 10.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 11, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 11.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 11.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 12, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 12.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 12.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 13, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888], "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 13.5, "offset": 4, "vertices": [-147.34207, -7.99246, -128.7055, 12.90427, -129.37152, -46.94211, -60.03033, -38.08896, 10.68387, -32.07803, 41.08621, -1.80469, -86.94373, 4.39816], "curve": "stepped"}, {"time": 13.5333, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 14, "offset": 4, "vertices": [-70.3141, -3.81414, -61.42042, 6.15813, -61.73825, -22.40156, -28.64748, -18.17669, 5.09852, -15.30817, 19.60703, -0.86123, -41.491, 2.09888]}]}, "jljl1": {"jljl1": [{"vertices": [2.7261, 136.07654, -9.7843, 139.33167]}]}, "bone7": {"bone6": [{"vertices": [0.32877, -0.14108, 0, 0, -0.18823, 0.0808, -0.68852, 0.29553, -1.02516, 0.44, -1.3618, 0.58447, -1.81984, 0.78104, -2.18037, 0.93576, -2.36444, 1.01477, -2.93399, 1.25921, -3.39809, 1.45836, -4.50037, 1.93141, -4.31912, 1.85367, -4.70929, 2.02113, -5.09946, 2.18855, -5.62747, 2.41516, -6.11631, 2.62498, -6.60512, 2.8347, -7.71099, 3.30936, -8.11168, 3.48131, -9.15548, 3.92928, -9.54757, 4.0976, -10.20998, 4.38189, -10.87238, 4.66612, -11.22116, 4.81586, -11.8175, 5.07175, -12.14665, 5.21307, -13.44101, 5.7685, -14.06183, 6.03496, -14.68251, 6.30131, -16.03612, 6.8823, -15.95795, 6.84869, -15.87971, 6.81516, -15.15273, 6.50314, -14.03656, 6.02412, -13.06263, 5.60612, -13.22569, 5.67613, -12.48215, 5.35699, -10.78956, 4.63062, -11.96749, 5.13615, -10.74938, 4.61334, -8.4217, 3.6144, -9.20355, -6.19777, -8.21379, -6.62258, -7.44822, -6.95113, -6.59018, 2.82836, -5.37664, 2.30754, -4.9593, 2.1284, -4.66574, 2.00243, -3.85862, 1.65601, -2.81854, 1.20969, -3.19298, 1.37035, -2.56522, 1.10092, -1.96298, 0.84247, -2.46378, 1.05739, -1.38776, 0.5956, -1.16526, 0.50011, -0.40968, 0.17583, 0.33444, -0.14351, 0.83292, -0.35742, 1.24545, -0.53448, 1.78977, -0.76812, 2.65521, -1.13956, 3.46714, -1.48798, 3.9901, -1.71243, 5.48276, -2.35303, 5.90339, -2.53353, 6.25076, -2.68263, 6.89057, -2.95721]}, {"time": 1.0333, "vertices": [5.109, 0.786, 5.109, 0.786, 5.109, 0.786, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.25102, -10.03999, -0.25102, -10.03999, -0.25102, -10.03999]}]}, "bone8": {"bone6": [{"vertices": [7.88695, 14.46429, 8.41745, 13.71091, 8.72123, 13.27969, 5.35574, 15.56679, 1.57695, 13.43115, -2.26473, 11.10121, -2.5334, 7.36133, -3.36271, 6.41386, -3.77806, 5.93944, 3.08907, 7.8887, 3.08299, 6.72388, 3.06858, 3.95738, 4.72153, 4.28038, 5.6082, 3.03401, 6.25432, 2.12567, 8.22179, 1.76389, 9.28837, -0.19907, 10.16681, -1.81551, 9.46774, -2.47166, 10.65124, -4.21135, 12.27841, -6.60316, 14.21921, -7.37389, 15.19613, -8.90437, 16.17305, -10.43459, 17.33675, -8.57281, 16.84515, -14.33311, 19.33546, -16.98178, 13.74336, -19.15535, 17.80429, -21.34534, 19.57504, -22.30029, 24.56124, -25.42166, 28.3887, -27.11722, 30.88365, -29.22365, 33.28548, -27.14418, 43.21968, -26.14235, 52.00653, -24.45239, 45.28272, -38.73254, 51.4737, -28.61638, 64.84518, -22.48059, 60.65797, -16.69388, 59.02044, -13.85851, 55.89142, -8.44016, 53.73523, -16.25992, 59.98936, -11.58073, 58.1921, -8.14233, 60.98706, -20.61919, 64.29807, -11.97504, 63.00227, -8.23361, 71.09075, 3.1611, 69.27047, 1.89186, 71.02572, 0.66052, 71.27298, 4.20685, 69.80108, 4.00856, 68.56655, 3.78586, 66.21642, 14.7177, 69.52579, 14.39235, 69.07987, 14.65868, 66.21898, 10.31401, 64.9248, 12.0065, 64.05772, 13.14035, 71.71426, 6.39449, 70.70835, 7.62492, 69.10882, 9.58087, 83.63304, 3.35094, 79.11116, 4.95689, 76.15659, 7.06671, 92.85839, -10.47806, 92.30183, -9.68152, 91.27664, -8.21431]}]}, "bone6": {"bone6": [{"offset": 84, "vertices": [-0.25102, -10.03999, -0.25102, -10.03999, -0.25102, -10.03999]}, {"time": 1.0333, "vertices": [5.109, 0.786, 5.109, 0.786, 5.109, 0.786, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.25102, -10.03999, -0.25102, -10.03999, -0.25102, -10.03999]}]}}}}}}