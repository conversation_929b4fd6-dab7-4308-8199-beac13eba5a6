import MsgEnum from "../../game/event/MsgEnum";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { WarriorSoulSubCmd } from "../../game/net/cmd/CmdData";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DoubleValueList,
  IntValue,
  LongValue,
  LongValueList,
  StringValue,
} from "../../game/net/protocol/ExternalMessage";
import {
  SoulPictureActiveResponse,
  SoulPictureMessage,
  SoulPictureSelectRequest,
  WarriorBuySoulResponse,
  WarriorRefreshResponse,
  WarriorSoulManageMessage,
  WarriorSoulMessage,
  WarriorSoulWorkResponse,
} from "../../game/net/protocol/Soul";
import MsgMgr from "../../lib/event/MsgMgr";
import { SoulModule } from "./SoulModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class SoulApi {
  /**
   * 获取用户所有武魂
   * @param success
   */
  public getAll(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      WarriorSoulManageMessage,
      WarriorSoulSubCmd.getAll,
      null,
      (data: WarriorSoulManageMessage) => {
        SoulModule.data.warriorSoulManageMsg = data;
        success && success(data);
      }
    );
  }

  /**
   * 刷新获取新的武魂
   * @param isFree 是否使用免费刷新  TRUE 免费刷新  FALSE|null 使用道具刷新
   * @param success
   * refreshTemplateIdList
   */
  public refreshSoul(isFree: boolean, success?: ApiHandlerSuccess) {
    let data: BoolValue = {
      value: isFree,
    };
    ApiHandler.instance.requestSync(
      WarriorRefreshResponse,
      WarriorSoulSubCmd.refreshSoul,
      BoolValue.encode(data),
      (data: WarriorRefreshResponse) => {
        SoulModule.data.warriorSoulManageMsg.refreshTemplateIdList = data.refreshTemplateIdList;
        SoulModule.data.warriorSoulManageMsg.freeCount = data.freeCount;
        SoulModule.data.warriorSoulManageMsg.paidCount = data.paidCount;

        SoulModule.data.warriorSoulManageMsg.chosenIndexList = [];
        SoulModule.data.warriorSoulManageMsg.exp = data.exp;

        MsgMgr.emit(MsgEnum.ON_SOUL_UPDATE);
        success && success(data);
      }
    );
  }

  /**
   * 刷新出武魂后,选中制定槽位武魂购买
   * @param selectIndex  selectIndex 区间范围 [0,2]  0 1 2
   * @param success
   */
  public buySoul(selectIndex: number, success?: ApiHandlerSuccess) {
    let data: IntValue = {
      value: selectIndex,
    };
    ApiHandler.instance.request(
      WarriorBuySoulResponse,
      WarriorSoulSubCmd.buySoul,
      IntValue.encode(data),
      (data: WarriorBuySoulResponse) => {
        // SoulModule.data.warriorSoulManageMsg.refreshTemplateIdList = data.refreshTemplateIdList;
        // SoulModule.data.warriorSoulManageMsg.chosenIndexList = data.chosenIndexList;
        // SoulModule.data.warriorSoulManageMsg.soulMap[data.warriorSoulMessage.id] = data.warriorSoulMessage;

        // MsgMgr.emit(MsgEnum.ON_SOUL_UPDATE);
        this.getAll(() => {
          success && success(data);
        });
        // success && success(data);
      }
    );
  }

  /**
   * 解锁新的槽位
   * @param success
   * 返回槽位的数量
   */
  public unLockSlot(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(IntValue, WarriorSoulSubCmd.unLockSlot, null, (data: IntValue) => {
      SoulModule.data.warriorSoulManageMsg.slot = data.value;
      MsgMgr.emit(MsgEnum.ON_SOUL_UPDATE);
      success && success(data);
    });
  }

  /**
   * 升级武魂
   * @param soulId
   * @param success
   */
  public upgrade(soulId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: soulId,
    };
    ApiHandler.instance.requestSync(
      WarriorSoulMessage,
      WarriorSoulSubCmd.upgrade,
      LongValue.encode(data),
      (data: WarriorSoulMessage) => {
        log.log("升级武魂", data);
        SoulModule.data.warriorSoulManageMsg.soulMap[data.id] = data;
        MsgMgr.emit(MsgEnum.ON_SOUL_UPDATE);
        success && success(data);
      }
    );
  }

  /**
   * 释放武魂
   * @param soulId
   * @param success
   */
  public freeSoul(soulId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: soulId,
    };
    ApiHandler.instance.request(
      DoubleValueList,
      WarriorSoulSubCmd.freeSoul,
      LongValue.encode(data),
      (data: DoubleValueList) => {
        delete SoulModule.data.warriorSoulManageMsg.soulMap[soulId];
        MsgMgr.emit(MsgEnum.ON_SOUL_UPDATE);
        success && success(data.values);
      }
    );
  }

  /**
   * 选择武魂参战
   * @param soulId
   * @param success
   */
  public goWork(soulId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: soulId,
    };
    ApiHandler.instance.request(
      WarriorSoulWorkResponse,
      WarriorSoulSubCmd.goWork,
      LongValue.encode(data),
      (data: WarriorSoulWorkResponse) => {
        SoulModule.data.warriorSoulManageMsg.soulMap = data.allSoulMap;
        MsgMgr.emit(MsgEnum.ON_SOUL_UPDATE);
        MsgMgr.emit(MsgEnum.ON_SOUL_WORK_UPDATE);
        success && success(data);
      }
    );
  }

  public activePicture(planIndex: number, pictureIndex: number, success?: ApiHandlerSuccess) {
    let request: SoulPictureSelectRequest = {
      planIndex: planIndex,
      pictureIndex: pictureIndex,
    };
    ApiHandler.instance.request(
      SoulPictureActiveResponse,
      WarriorSoulSubCmd.activePicture,
      SoulPictureSelectRequest.encode(request),
      (data: SoulPictureActiveResponse) => {
        this.getAll(() => {
          success && success(data);
        });
      }
    );
  }

  /**
   * 刷新武魂技能
   * @param planIndex
   * @param pictureIndex
   * @param success
   */
  public refreshPictureSkill(planIndex: number, pictureIndex: number, success?: ApiHandlerSuccess) {
    let request: SoulPictureSelectRequest = {
      planIndex: planIndex,
      pictureIndex: pictureIndex,
    };
    ApiHandler.instance.request(
      SoulPictureMessage,
      WarriorSoulSubCmd.refreshPictureSkill,
      SoulPictureSelectRequest.encode(request),
      (data: SoulPictureMessage) => {
        SoulModule.data.warriorSoulManageMsg.planList[planIndex].pictureMap[pictureIndex] = data;
        MsgMgr.emit(MsgEnum.ON_SOUL_TUJIAN_UPDATE);
        success && success(data);
      }
    );
  }
  public useBackUpPictureSkill(planIndex: number, pictureIndex: number, success?: ApiHandlerSuccess) {
    let request: SoulPictureSelectRequest = {
      planIndex: planIndex,
      pictureIndex: pictureIndex,
    };
    ApiHandler.instance.request(
      SoulPictureMessage,
      WarriorSoulSubCmd.useBackUpPictureSkill,
      SoulPictureSelectRequest.encode(request),
      (data: SoulPictureMessage) => {
        SoulModule.data.warriorSoulManageMsg.planList[planIndex].pictureMap[pictureIndex] = data;
        MsgMgr.emit(MsgEnum.ON_SOUL_TUJIAN_UPDATE);
        success && success(data);
      }
    );
  }
  public workPlan(planIndex: number, success?: ApiHandlerSuccess) {
    let data: IntValue = {
      value: planIndex,
    };
    ApiHandler.instance.request(IntValue, WarriorSoulSubCmd.workPlan, IntValue.encode(data), (data: IntValue) => {
      this.getAll(() => {
        success && success(data);
      });
    });
  }

  public testAddSoulTemplateId(soulId: number, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      LongValueList,
      WarriorSoulSubCmd.testAddSoulTemplateId,
      LongValue.encode({ value: soulId }),
      (data: LongValueList) => {
        success && success(data);
      }
    );
  }

  /**
   * 重置武魂
   * @param success
   */
  public testReset(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      WarriorSoulManageMessage,
      WarriorSoulSubCmd.testReset,
      null,
      (data: WarriorSoulManageMessage) => {
        success && success(data);
      }
    );
  }

  /**
   * 模块数据
   * @param success
   */
  public testGetCache(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(StringValue, WarriorSoulSubCmd.testGetCache, null, (data: StringValue) => {
      success && success(data);
    });
  }
}
