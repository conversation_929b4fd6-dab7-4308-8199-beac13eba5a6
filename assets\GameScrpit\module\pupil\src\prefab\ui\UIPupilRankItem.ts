import { _decorator, instantiate, Label, Sprite } from "cc";
import { ScrollListItem } from "../../../../../game/common/ScrollListItem";
import ToolExt from "../../../../../game/common/ToolExt";
import { PupilMessage } from "../../../../../game/net/protocol/Pupil";
import { PupilAni } from "./PupilAni";
import { PupilModule } from "../../PupilModule";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { AssetMgr } from "../../../../../../platform/src/ResHelper";
import { SpriteFrame } from "cc";
import { NodeTool } from "../../../../../lib/utils/NodeTool";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("UIPupilRankItem")
export class UIPupilRankItem extends ScrollListItem {
  // 资源管理
  private _assetMgr: AssetMgr;

  protected onLoad(): void {
    this._assetMgr = AssetMgr.create();
  }

  protected onDestroy(): void {
    this._assetMgr.release();
  }

  /**滚动列表数据变更*/
  onItemRender(pupilMsg: PupilMessage, ...param: any[]) {
    // 形象
    let pupilAni = this.node.getChildByPath("dz_bg_touxiangkuang/PupilAni");
    pupilAni.getComponent(PupilAni).setAniByNameId(pupilMsg.ownInfo.nameId);

    //配置
    let config = PupilModule.data.getConfigPupil(pupilMsg.ownInfo.talentId);

    // 所属人
    this.node.getChildByName("user_name_lab").getComponent(Label).string = `${pupilMsg.ownInfo.userName}`;

    // 弟子名字
    PupilModule.service.setPupilNameNode(this.node.getChildByName("pupil_name_lab"), pupilMsg.ownInfo.nameId);

    // 天资背景
    this._assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_G_PUPIL, `images/${config.talentBg}`, (spf: SpriteFrame) => {
      this.node.getChildByName("bg_talent").getComponent(Sprite).spriteFrame = spf;
    });

    // 弟子领悟属性
    let adult_attr_own = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.adultAttrList);

    log.log(pupilMsg.ownInfo.userName + "======", adult_attr_own);

    let adult_attr_bg = this.node.getChildByName("adult_attr_bg");
    PupilModule.service.setPupilAttrNode(adult_attr_bg.getChildByName("Label"), adult_attr_own[0]);
    PupilModule.service.setPupilAdultAttrBgNode(adult_attr_bg, config.color);

    // 天生属性
    let init_attr = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.initAttrList);
    let init_attr_lay = this.node.getChildByName("init_attr_lay");
    init_attr_lay.removeAllChildren();
    let attr_node = this.node.getChildByName("attr_node_item");
    for (let i = 0; i < init_attr.length; i++) {
      let init_attr_item = instantiate(attr_node);
      init_attr_item.active = true;
      PupilModule.service.setPupilAttrNode(init_attr_item.getChildByName("Label"), init_attr[i]);
      init_attr_lay.addChild(init_attr_item);
    }

    // 名次
    const node_mingci = NodeTool.findByName(this.node, "node_mingci");
    node_mingci.getChildByName("bg_1").active = param[0] + 1 == 1;
    node_mingci.getChildByName("bg_2").active = param[0] + 1 == 2;
    node_mingci.getChildByName("bg_3").active = param[0] + 1 == 3;
    node_mingci.getChildByName("bg_deault").active = param[0] + 1 > 3;
    node_mingci.getChildByName("lbl_mingci").active = param[0] + 1 > 3;
    node_mingci.getChildByName("lbl_mingci").getComponent(Label).string = `${param[0] + 1}`;

    this.node.active = true;
  }
}
