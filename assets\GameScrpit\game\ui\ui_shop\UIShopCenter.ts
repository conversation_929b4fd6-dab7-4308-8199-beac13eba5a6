import { _decorator, is<PERSON><PERSON><PERSON>, <PERSON>de, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { Label } from "cc";
import { PlayerModule } from "../../../module/player/PlayerModule";
import GameHttpApi from "../../httpNet/GameHttpApi";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import { instantiate } from "cc";
import { GoodsRedeemMessage } from "../../net/protocol/Goods";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { GoodsRouteName } from "../../../module/goods/GoodsRoute";
import Formate from "../../../lib/utils/Formate";
import { PublicRouteName } from "../../../module/player/PlayerConstant";
import { BuyConfirm } from "../UIBuyConfirm";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { IConfigShop } from "../../bundleDefine/bundle_shop_define";
import { Sleep } from "../../GameDefine";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { ShopAudioName } from "../../../module/player/PlayerConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIShopCenter")
export class UIShopCenter extends UINode {
  _tabIdx: number = 5;
  _goodsRedeemMsg: GoodsRedeemMessage = null;
  _readyList = [];

  protected _openAct: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SHOP}?prefab/ui/UIShopCenter`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  public init(args: any): void {
    super.init(args);
    this._tabIdx = args.tabIdx || 5;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.updatePlayerItem, this);
    MsgMgr.on(MsgEnum.ON_SHOP_ORDER_NOTIFY, this.onOrderResult, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.updatePlayerItem, this);
    MsgMgr.off(MsgEnum.ON_SHOP_ORDER_NOTIFY, this.onOrderResult, this);
  }

  protected onEvtShow(): void {
    GoodsModule.api.buyInfo(() => {
      this.onSwitchTab(this._tabIdx);
    });

    this.updatePlayerItem();
  }

  /**
   * 订单结果展示
   */
  private onOrderResult() {
    TipsMgr.setEnableTouch(true);

    this.onSwitchTab(this._tabIdx);
    this.updatePlayerItem();
  }

  // 用户道具更新回调
  private updatePlayerItem() {
    let item6Num = PlayerModule.data.getItemNum(ItemEnum.仙玉_6);
    this.getNode("lbl_item6").getComponent(Label).string = Formate.format(item6Num);
  }

  // 点击事件
  private onItemClick(event: Event) {
    AudioMgr.instance.playEffect(ShopAudioName.Effect.点击购买);
    const nodeItem = event.target;

    let cfgShop: IConfigShop = nodeItem["FM_CFG_SHOP"];
    let cfgItem = GoodsModule.data.getConfigItem(cfgShop.itemsList[0][0]);

    if (cfgShop.type == 6) {
      this.onPay(
        cfgShop.id,
        2,
        PlayerModule.data.playerId,
        cfgShop.coinPrice,
        cfgItem.des + "x" + cfgShop.itemsList[0][1],
        "TEST"
      );
    } else if (cfgShop.type == 5) {
      let numLimit = cfgShop.max - GoodsModule.data.getGoodsRedeemMsgById(cfgShop.id);
      const buyConfirm: BuyConfirm = {
        itemInfo: cfgShop.itemsList[0],
        moneyInfo: [cfgShop.cointype, cfgShop.coinPrice],
        maxNum: numLimit,
      };

      UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
        if (resp.ok) {
          GoodsModule.api.redeemGoods(cfgShop.id, resp.num, (resp) => {
            MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: resp });
            this.showItem(cfgShop.type);
          });
        }
      });
    }
  }

  private async showItem(goodsType: number) {
    function updateExistItem(nodeItem: Node, cfgShop: IConfigShop) {
      let numLimit = cfgShop.max - GoodsModule.data.getGoodsRedeemMsgById(cfgShop.id);
      // 限购设置
      let lblLimit = nodeItem.getChildByName("lbl_limit").getComponent(Label);
      if (cfgShop.maxtype == 0 || numLimit == 0) {
        lblLimit.string = "";
      } else if (cfgShop.maxtype == 1) {
        lblLimit.string = `每日限购:(${numLimit}/${cfgShop.max})`;
      } else if (cfgShop.maxtype == 2) {
        lblLimit.string = "每周限购：" + numLimit;
      } else if (cfgShop.maxtype == 3) {
        lblLimit.string = "每月限购：" + numLimit;
      } else if (cfgShop.maxtype == 4) {
        lblLimit.string = "永久限购：" + numLimit;

        if (PlayerModule.service.isShopBuy(cfgShop.itemsList[0][0]) == false) {
          lblLimit.string = "";
        }
      }
    }

    let needReShow = !this._readyList.includes(goodsType);
    if (needReShow) {
      this._readyList.push(goodsType);
    }

    let nodeLayoutContent = this.getNode("layout_content");
    nodeLayoutContent.children.forEach((item) => (item.active = false));
    let list = GoodsModule.data.getConfigShopListByType(goodsType);
    for (let idx in list) {
      if (isValid(this.node) == false) return;
      if (needReShow) {
        await Sleep(0.01);
      }

      let cfgShop = list[idx];

      // 获取或创建组件
      let nodeItem = nodeLayoutContent.children[idx];
      if (!nodeItem) {
        nodeItem = instantiate(nodeLayoutContent.children[0]);
        nodeLayoutContent.addChild(nodeItem);
      }
      nodeItem.active = true;

      if (needReShow) {
        tween(nodeItem)
          .set({ scale: v3(0.1, 0.1, 1) })
          .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
          .start();
      }

      // 节点传值
      nodeItem.getChildByName("btn_buy")["FM_CFG_SHOP"] = cfgShop;

      // 首充翻倍默认不显示
      nodeItem.getChildByName("bg_double").active = false;
      nodeItem.getChildByName("lbl_double").active = false;

      // 获取道具配置
      let cfgItem = GoodsModule.data.getConfigItem(cfgShop.itemsList[0][0]);
      // 名字
      nodeItem.getChildByName("lbl_title").getComponent(Label).string = cfgItem.name;
      // 图标数量
      FmUtils.setItemNode(nodeItem.getChildByName("Item"), cfgItem.id, cfgShop.itemsList[0][1]);
      // 限购设置
      updateExistItem(nodeItem, cfgShop);

      // 购买按钮
      nodeItem.getChildByName("btn_buy").active = true;
      nodeItem.getChildByName("sell_out").active = false;
      if (goodsType == 6) {
        // 法币

        // 价格
        nodeItem.getChildByPath("btn_buy/layout/lbl_price").getComponent(Label).string = GoodsModule.service.getPayInfo(
          cfgShop.rmbId
        );
        nodeItem.getChildByPath("btn_buy/layout/bg").active = false;
      } else {
        // 仙玉

        // 价格
        nodeItem.getChildByPath("btn_buy/layout/lbl_price").getComponent(Label).string = Formate.format(
          cfgShop.coinPrice
        );
        nodeItem.getChildByPath("btn_buy/layout/bg").active = true;
        FmUtils.setItemIcon(nodeItem.getChildByPath("btn_buy/layout/bg"), cfgShop.cointype);
      }
    }

    let resp = GoodsModule.data.getGoodsRedeemMsg();
    // 售罄处理
    for (let idx in list) {
      let cfgShop = list[idx];
      let nodeItem = nodeLayoutContent.children[idx];

      // 售罄判断
      let sellOut = false;
      if (cfgShop.maxtype > 0) {
        sellOut = cfgShop.max <= (resp.redeemMap[cfgShop.id] || 0);
      }

      if (cfgShop.maxtype == 4) {
        sellOut = !PlayerModule.service.isShopBuy(cfgShop.itemsList[0][0]);
      }

      // 首充 翻倍
      nodeItem.getChildByName("bg_double").active = goodsType == 6 && !resp.virtualMoneySet.includes(cfgShop.id);
      nodeItem.getChildByName("lbl_double").active = nodeItem.getChildByName("bg_double").active;
      nodeItem.getChildByName("lbl_double").getComponent(Label).string = `额外赠送${cfgShop.itemsList[0][1]}`;

      nodeItem.getChildByName("sell_out").active = sellOut;
      nodeItem.getChildByName("btn_buy").active = !sellOut;
      nodeItem.getChildByName("lbl_limit").active = !sellOut;

      // 限购设置
      updateExistItem(nodeItem, cfgShop);

      nodeItem.getChildByName("btn_buy").off(Node.EventType.TOUCH_END, this.onItemClick, this);

      if (!sellOut) {
        nodeItem.getChildByName("btn_buy").on(Node.EventType.TOUCH_END, this.onItemClick, this);
      }
    }
  }

  private onSwitchTab(tabId: number) {
    this.getNode("btn_xianyu").getChildByName("bg_unactive").active = tabId != 5;
    this.getNode("btn_xianyu").getChildByName("bg_active").active = tabId == 5;
    this.getNode("btn_rmb").getChildByName("bg_unactive").active = tabId != 6;
    this.getNode("btn_rmb").getChildByName("bg_active").active = tabId == 6;

    this.showItem(tabId);

    this._tabIdx = tabId;
  }

  private on_click_btn_xianyu() {
    AudioMgr.instance.playEffect(ShopAudioName.Effect.点击下方页签);
    this.onSwitchTab(5);
  }

  private on_click_btn_rmb() {
    AudioMgr.instance.playEffect(ShopAudioName.Effect.点击下方页签);
    this.onSwitchTab(6);
  }

  private onPay(
    goodsId: number,
    goodsType: number,
    playerId: number,
    orderAmount: number,
    goodsName: string,
    platformType: string
  ) {
    GameHttpApi.pay({
      goodsId,
      goodsType,
      playerId,
      orderAmount,
      goodsName,
      platformType,
    }).then((resp: any) => {
      log.log("pay resp", resp);
      if (resp.code != 200) {
        let err = JSON.parse(resp.msg);
        log.log(err);
        return;
      }

      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url }, () => {
        TipsMgr.setEnableTouch(false, 7);
      });
    });
  }
}
