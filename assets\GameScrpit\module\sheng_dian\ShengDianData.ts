import { JsonMgr } from "../../game/mgr/JsonMgr";
import { TempleMessage } from "../../game/net/protocol/Activity";
import { times } from "../../lib/utils/NumbersUtils";
import { ShengDianModule } from "./ShengDianModule";

export class ShengDianData {
  private _templeMessage: TempleMessage;
  public get templeMessage() {
    return this._templeMessage;
  }
  public set templeMessage(value) {
    this._templeMessage = value;
  }
}
