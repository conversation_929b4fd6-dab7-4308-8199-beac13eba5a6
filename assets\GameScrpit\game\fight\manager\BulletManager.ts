import { _decorator, isValid } from "cc";
import ManagerSection from "./ManagerSection";
import GameObject from "../../../lib/object/GameObject";
import FightManager from "./FightManager";
import { CallBulletDetail } from "../FightDefine";
import { JsonMgr } from "../../mgr/JsonMgr";
import { GOHurtBullet } from "../bullet/GOHurtBullet";
import { GOLIneFlyBullet } from "../bullet/GOLIneFlyBullet";
import { GOBullet } from "../bullet/GOBullet";
import { GOSummonBullet } from "../bullet/GOSummonBullet";
import { GONoumenonBullet } from "../bullet/GONoumenonBullet";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

@ccclass("BulletManager")
export default class BulletManager extends ManagerSection {
  private _bulletScrpitMap: Map<string, any> = new Map();
  public static sectionName(): string {
    return "BulletManager";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.createRoot("BulletRoot", FightManager.instance);
    this.ready();
  }

  public onStart() {
    super.onStart();
    this._bulletScrpitMap.set("GOHurtBullet", GOHurtBullet);
    this._bulletScrpitMap.set("GOLIneFlyBullet", GOLIneFlyBullet);
    this._bulletScrpitMap.set("GOSummonBullet", GOSummonBullet);
    this._bulletScrpitMap.set("GONoumenonBullet", GONoumenonBullet);
  }

  public doCallObject(detail: CallBulletDetail): GOBullet {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    if (isValid(this.root) == false) {
      return;
    }
    let db = JsonMgr.instance.jsonList.c_bulletShow[detail.bulletId];
    if (db == null) {
      log.error("子弹表不存在" + detail.bulletId + "为空");
      return;
    }
    let type = this._bulletScrpitMap.get(db.scrpitName);
    let bullet = new type(detail);
    this.addChild(bullet);
    bullet.onInitDetail(detail);
    return bullet;
  }

  public updateSelf(dt) {
    super.updateSelf(dt);
    this.updateCallObject(dt);
  }

  private updateCallObject(dt) {}

  public onRemove() {
    super.onRemove();
  }
}
