{"skeleton": {"hash": "VJRqKw6q6dGDdKBYbBh2Y+E2tNA", "spine": "3.8.75", "x": -408.61, "y": -709.27, "width": 840.94, "height": 1350.53, "images": "./images/", "audio": "E:/1/文件/PSD/动画拆分/何仙姑"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 133.79, "rotation": -179.12, "x": 551.79, "y": 124.3}, {"name": "bone2", "parent": "bone", "length": 157.8, "rotation": -97.5, "x": 463.94, "y": 284.87}, {"name": "bone3", "parent": "bone2", "length": 173.42, "rotation": 15.23, "x": 157.8}, {"name": "bone4", "parent": "bone3", "length": 111.54, "rotation": 22.73, "x": 173.42}, {"name": "bone5", "parent": "bone3", "x": 191.78, "y": 120.47}, {"name": "bone6", "parent": "bone3", "x": 135.27, "y": -111.7}, {"name": "bone7", "parent": "bone3", "length": 215.35, "rotation": 94.98, "x": 176.08, "y": 112.6}, {"name": "bone8", "parent": "bone7", "length": 212.67, "rotation": -143.61, "x": 215.35}, {"name": "bone9", "parent": "bone3", "length": 278.43, "rotation": -164.46, "x": 114.72, "y": -112.06}, {"name": "bone10", "parent": "bone9", "length": 107.96, "rotation": -74.87, "x": 278.43}, {"name": "bone11", "parent": "bone10", "length": 76.62, "rotation": -66.14, "x": 107.96}, {"name": "bone12", "parent": "bone11", "length": 43.08, "rotation": 82.22, "x": 76.62}, {"name": "bone13", "parent": "bone3", "length": 114.87, "rotation": 169.42, "x": -174.92, "y": -203.24}, {"name": "bone14", "parent": "bone3", "length": 180.1, "rotation": 83.53, "x": 42.85, "y": 204.03}, {"name": "bone15", "parent": "bone", "length": 238.74, "rotation": 55.49, "x": 573.77, "y": 252.25}, {"name": "bone16", "parent": "bone15", "length": 191.45, "rotation": -24.75, "x": 239.29, "y": -0.83}, {"name": "bone17", "parent": "bone", "length": 133.26, "rotation": 129.58, "x": 373.62, "y": 348.33}, {"name": "bone18", "parent": "bone17", "length": 145.99, "rotation": 43.28, "x": 133.26}, {"name": "bone19", "parent": "root", "x": 336.62, "y": -417.59}, {"name": "bone28", "parent": "bone", "length": 126.21, "rotation": -112.35, "x": 370.1, "y": 267.8}, {"name": "bone29", "parent": "bone28", "length": 101.43, "rotation": -35.82, "x": 126.21}, {"name": "bone30", "parent": "bone29", "length": 68.28, "rotation": -36.65, "x": 101.43}, {"name": "bone31", "parent": "bone30", "length": 88.02, "rotation": -140.1, "x": 67.77, "y": -3.96}, {"name": "bone32", "parent": "bone4", "length": 33.93, "rotation": 159.02, "x": 123.5, "y": -51.61}, {"name": "bone33", "parent": "bone4", "length": 27.86, "rotation": 160.46, "x": 94.18, "y": 74.74}, {"name": "bone34", "parent": "bone4", "length": 82.27, "rotation": 103.59, "x": 225.44, "y": 94.35}, {"name": "bone35", "parent": "bone34", "length": 108.41, "rotation": -16.74, "x": 82.27}, {"name": "bone36", "parent": "bone35", "length": 103.84, "rotation": -0.42, "x": 108.41}, {"name": "bone37", "parent": "bone4", "length": 100.29, "rotation": 111.36, "x": 145.6, "y": 103.88}, {"name": "bone38", "parent": "bone37", "length": 146.36, "rotation": -30.21, "x": 100.29}, {"name": "bone39", "parent": "bone38", "length": 121.05, "rotation": 1.33, "x": 147.13, "y": -0.2}, {"name": "bone40", "parent": "bone4", "length": 97.53, "rotation": 176.45, "x": 114.11, "y": -54.5}, {"name": "bone41", "parent": "bone40", "length": 76.75, "rotation": 19.67, "x": 97.53}, {"name": "bone42", "parent": "bone4", "x": 192.22, "y": 65.39}, {"name": "bone43", "parent": "bone4", "x": 128.86, "y": 63.39, "color": "abe323ff"}, {"name": "bone44", "parent": "bone4", "x": 155, "y": 59.99, "color": "abe323ff"}, {"name": "bone21", "parent": "bone", "length": 223.29, "rotation": 88.18, "x": 495.38, "y": 253.01}, {"name": "bone45", "parent": "bone21", "length": 164.16, "rotation": 67.13, "x": 118.15, "y": -105.27, "color": "ff3f00ff"}, {"name": "bone46", "parent": "bone45", "length": 347.12, "rotation": -93.68, "x": 164.16}, {"name": "bone47", "parent": "bone21", "length": 210.45, "rotation": 16.19, "x": 55.6, "y": 24.51}, {"name": "bone48", "parent": "bone47", "length": 162.55, "rotation": -60.26, "x": 210.45}, {"name": "bone49", "parent": "bone21", "length": 107.32, "rotation": 26.9, "x": 149.66, "y": -133.37}, {"name": "bone50", "parent": "bone49", "length": 222.88, "rotation": -52.82, "x": 112.56, "y": 1.18}, {"name": "bone51", "parent": "bone", "length": 72.06, "rotation": -13.22, "x": 178.89, "y": 10.57}, {"name": "bone52", "parent": "bone51", "length": 77.51, "rotation": 25.88, "x": 72.06, "y": 0.2}, {"name": "bone53", "parent": "bone4", "length": 106.97, "rotation": -92.68, "x": 354.43, "y": 57.55}, {"name": "bone54", "parent": "bone3", "x": 115.35, "y": -17.24}, {"name": "bone55", "parent": "bone3", "x": 53.38, "y": -9.87, "color": "abe323ff"}, {"name": "bone56", "parent": "bone21", "length": 112.87, "rotation": -17.96, "x": 77.82, "y": -80.85}, {"name": "bone57", "parent": "bone56", "length": 94.67, "rotation": -8.97, "x": 112.87}, {"name": "bone58", "parent": "bone57", "length": 86.71, "rotation": -12.18, "x": 94.22, "y": 0.85}, {"name": "bone59", "parent": "bone58", "length": 100.07, "rotation": -11.98, "x": 86.71}, {"name": "bone60", "parent": "root", "x": 331.74, "y": -547.54}, {"name": "bone61", "parent": "root", "x": -221.82, "y": -253.18}, {"name": "bone20", "parent": "bone3", "length": 142.71, "rotation": 96.49, "x": -12.52, "y": 244.62}, {"name": "bone22", "parent": "root", "length": 370.35, "rotation": 0.62, "x": -161.34, "y": 734.21}, {"name": "bone23", "parent": "bone22", "x": -100.92, "y": -178.89, "color": "ffffffff"}, {"name": "bone24", "parent": "bone22", "x": 14.46, "y": -176.63, "color": "ffffffff"}, {"name": "bone25", "parent": "bone22", "x": 150.06, "y": -176.97, "color": "ffffffff"}, {"name": "bone26", "parent": "bone22", "x": 282.88, "y": -181, "color": "ffffffff"}, {"name": "bone27", "parent": "bone22", "x": 389.59, "y": -178.19, "color": "ffffffff"}, {"name": "bone62", "parent": "bone22", "x": -100.92, "y": -281.76, "color": "ffffffff"}, {"name": "bone63", "parent": "bone22", "x": 14.46, "y": -279.51, "color": "ffffffff"}, {"name": "bone64", "parent": "bone22", "x": 150.06, "y": -279.84, "color": "ffffffff"}, {"name": "bone65", "parent": "bone22", "x": 282.88, "y": -283.88, "color": "ffffffff"}, {"name": "bone66", "parent": "bone22", "x": 389.59, "y": -281.07, "color": "ffffffff"}, {"name": "bone67", "parent": "bone22", "x": -100.92, "y": -378.16, "color": "ffffffff"}, {"name": "bone68", "parent": "bone22", "x": 14.46, "y": -375.91, "color": "ffffffff"}, {"name": "bone69", "parent": "bone22", "x": 150.06, "y": -376.24, "color": "ffffffff"}, {"name": "bone70", "parent": "bone22", "x": 282.88, "y": -380.28, "color": "ffffffff"}, {"name": "bone71", "parent": "bone22", "x": 389.59, "y": -377.46, "color": "ffffffff"}, {"name": "bone72", "parent": "bone22", "x": -100.92, "y": -484.42, "color": "ffffffff"}, {"name": "bone73", "parent": "bone22", "x": 14.46, "y": -482.17, "color": "ffffffff"}, {"name": "bone74", "parent": "bone22", "x": 150.06, "y": -482.5, "color": "ffffffff"}, {"name": "bone75", "parent": "bone22", "x": 282.88, "y": -486.54, "color": "ffffffff"}, {"name": "bone76", "parent": "bone22", "x": 389.59, "y": -483.73, "color": "ffffffff"}, {"name": "bone77", "parent": "bone22", "x": -100.92, "y": -594.02, "color": "ffffffff"}, {"name": "bone78", "parent": "bone22", "x": 14.46, "y": -591.77, "color": "ffffffff"}, {"name": "bone79", "parent": "bone22", "x": 150.06, "y": -592.1, "color": "ffffffff"}, {"name": "bone80", "parent": "bone22", "x": 282.88, "y": -596.14, "color": "ffffffff"}, {"name": "bone81", "parent": "bone22", "x": 389.59, "y": -593.32, "color": "ffffffff"}, {"name": "bone82", "parent": "bone22", "x": -100.92, "y": -714.59, "color": "ffffffff"}, {"name": "bone83", "parent": "bone22", "x": 14.46, "y": -712.34, "color": "ffffffff"}, {"name": "bone84", "parent": "bone22", "x": 150.06, "y": -712.67, "color": "ffffffff"}, {"name": "bone85", "parent": "bone22", "x": 282.88, "y": -716.71, "color": "ffffffff"}, {"name": "bone86", "parent": "bone22", "x": 389.59, "y": -713.89, "color": "ffffffff"}, {"name": "bone87", "parent": "bone22", "x": -100.92, "y": -832.15, "color": "ffffffff"}, {"name": "bone88", "parent": "bone22", "x": 14.46, "y": -829.9, "color": "ffffffff"}, {"name": "bone89", "parent": "bone22", "x": 150.06, "y": -830.23, "color": "ffffffff"}, {"name": "bone90", "parent": "bone22", "x": 282.88, "y": -834.27, "color": "ffffffff"}, {"name": "bone91", "parent": "bone22", "x": 389.59, "y": -831.45, "color": "ffffffff"}, {"name": "bone92", "parent": "bone22", "x": -100.92, "y": -937.04, "color": "ffffffff"}, {"name": "bone93", "parent": "bone22", "x": 14.46, "y": -934.78, "color": "ffffffff"}, {"name": "bone94", "parent": "bone22", "x": 150.06, "y": -935.11, "color": "ffffffff"}, {"name": "bone95", "parent": "bone22", "x": 282.88, "y": -939.15, "color": "ffffffff"}, {"name": "bone96", "parent": "bone22", "x": 389.59, "y": -936.34, "color": "ffffffff"}, {"name": "bone97", "parent": "bone22", "x": -100.92, "y": -1074.66, "color": "ffffffff"}, {"name": "bone98", "parent": "bone22", "x": 14.46, "y": -1072.41, "color": "ffffffff"}, {"name": "bone99", "parent": "bone22", "x": 150.06, "y": -1072.74, "color": "ffffffff"}, {"name": "bone100", "parent": "bone22", "x": 282.88, "y": -1076.78, "color": "ffffffff"}, {"name": "bone101", "parent": "bone22", "x": 389.59, "y": -1073.97, "color": "ffffffff"}, {"name": "bone102", "parent": "bone22", "x": -100.92, "y": -1206.28, "color": "ffffffff"}, {"name": "bone103", "parent": "bone22", "x": 14.46, "y": -1204.02, "color": "ffffffff"}, {"name": "bone104", "parent": "bone22", "x": 150.06, "y": -1204.35, "color": "ffffffff"}, {"name": "bone105", "parent": "bone22", "x": 282.88, "y": -1208.39, "color": "ffffffff"}, {"name": "bone106", "parent": "bone22", "x": 389.59, "y": -1205.58, "color": "ffffffff"}, {"name": "bone107", "parent": "bone22", "x": -100.92, "y": -1316, "color": "ffffffff"}, {"name": "bone108", "parent": "bone22", "x": 14.46, "y": -1313.74, "color": "ffffffff"}, {"name": "bone109", "parent": "bone22", "x": 150.06, "y": -1314.07, "color": "ffffffff"}, {"name": "bone110", "parent": "bone22", "x": 282.88, "y": -1318.11, "color": "ffffffff"}, {"name": "bone111", "parent": "bone22", "x": 389.59, "y": -1315.3, "color": "ffffffff"}, {"name": "bone112", "parent": "root", "length": 60.33, "rotation": 95.22, "x": -196.69, "y": 222.22}, {"name": "bone113", "parent": "bone112", "length": 56.41, "rotation": -10.15, "x": 60.62, "y": -0.35}, {"name": "bone114", "parent": "root", "length": 81.76, "rotation": 79.86, "x": 182.9, "y": 63.63}, {"name": "bone115", "parent": "bone114", "length": 128.51, "rotation": -15.15, "x": 81.76}, {"name": "bone116", "parent": "bone114", "length": 116.91, "rotation": 40.63, "x": 120.65, "y": 24.47}, {"name": "bone117", "parent": "bone114", "length": 89.32, "rotation": -71.51, "x": 135.47, "y": -51.36}, {"name": "bone118", "parent": "root", "length": 107.44, "rotation": 78.6, "x": 37.27, "y": 350.84}, {"name": "bone119", "parent": "bone118", "length": 54.13, "rotation": -17.26, "x": 107.72, "y": 0.06}, {"name": "bone120", "parent": "root", "length": 81.76, "rotation": 87.4, "x": 240.24, "y": 500.52, "scaleX": 0.458, "scaleY": 0.5292, "shearX": 42.12, "shearY": 7.91}, {"name": "bone121", "parent": "bone120", "length": 128.51, "rotation": -15.15, "x": 81.76}, {"name": "bone122", "parent": "bone120", "length": 116.91, "rotation": 40.63, "x": 120.65, "y": 24.47}, {"name": "bone123", "parent": "bone120", "length": 89.32, "rotation": -71.51, "x": 135.47, "y": -51.36}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "he<PERSON>_zu<PERSON>g", "bone": "root", "attachment": "he<PERSON>_zu<PERSON>g"}, {"name": "heye_you_da", "bone": "bone114", "attachment": "heye_you_da"}, {"name": "heye_you_da2", "bone": "bone120", "color": "fdfff4d2", "attachment": "heye_you_da"}, {"name": "heye_zuo", "bone": "bone61", "attachment": "heye_zuo"}, {"name": "heye_shang", "bone": "bone118", "attachment": "heye_shang"}, {"name": "toufa_zuo", "bone": "root", "attachment": "toufa_zuo"}, {"name": "toufa_you", "bone": "root", "attachment": "toufa_you"}, {"name": "gebo_you", "bone": "root", "attachment": "gebo_you"}, {"name": "xiayi", "bone": "root", "attachment": "xiayi"}, {"name": "qundai", "bone": "root", "attachment": "qundai"}, {"name": "shangyi", "bone": "root", "attachment": "shangyi"}, {"name": "heye_youxia", "bone": "bone60", "attachment": "heye_youxia"}, {"name": "sidai4", "bone": "root", "attachment": "sidai4"}, {"name": "shou_you", "bone": "root", "attachment": "shou_you"}, {"name": "gebo_zuo", "bone": "root", "attachment": "gebo_zuo"}, {"name": "shou__zuo", "bone": "root", "attachment": "shou__zuo"}, {"name": "yanjing", "bone": "bone44", "attachment": "yanjing"}, {"name": "erz<PERSON>_zuo", "bone": "root", "attachment": "erz<PERSON>_zuo"}, {"name": "lian", "bone": "root", "attachment": "lian"}, {"name": "erz<PERSON>_you", "bone": "bone32", "attachment": "erz<PERSON>_you"}, {"name": "bu_jian<PERSON>_you", "bone": "root", "attachment": "bu_jian<PERSON>_you"}, {"name": "bu_ji<PERSON><PERSON>_zuo", "bone": "root", "attachment": "bu_ji<PERSON><PERSON>_zuo"}], "transform": [{"name": "bone43", "bones": ["bone42"], "target": "bone43", "x": 63.36, "y": 2, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "bone44", "order": 2, "bones": ["bone43"], "target": "bone44", "x": -26.14, "y": 3.41, "rotateMix": 0, "translateMix": -0.7, "scaleMix": 0, "shearMix": 0}, {"name": "bone55", "order": 1, "bones": ["bone54"], "target": "bone55", "x": 61.97, "y": -7.37, "rotateMix": -1, "translateMix": -1, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"gebo_zuo": {"gebo_zuo": {"type": "mesh", "uvs": [0.86668, 0, 0.72144, 0.07512, 0.58593, 0.10679, 0.42942, 0.13054, 0.28905, 0.15939, 0.15029, 0.23065, 0.17933, 0.32567, 0.11802, 0.49365, 0.04703, 0.65824, 0, 0.76174, 0, 0.85676, 0.09887, 0.86889, 0.12447, 0.87203, 0.09866, 0.98232, 0.20515, 0.9908, 0.31325, 0.97214, 0.4988, 0.95687, 0.66499, 0.85846, 0.79891, 0.71253, 0.86668, 0.53777, 0.90218, 0.40542, 0.91357, 0.37046, 0.9409, 0.28664, 1, 0.17975, 1, 0.05588, 0.98931, 0, 0.92315, 0.08642, 0.76019, 0.21877, 0.57464, 0.27986, 0.33584, 0.29173, 0.84409, 0.26289, 0.65209, 0.42917, 0.43588, 0.56491, 0.22451, 0.626, 0.74728, 0.53946, 0.53107, 0.72271, 0.33584, 0.78041, 0.78297, 0.11918, 0.82319, 0.16761, 0.85474, 0.22016, 0.88823, 0.28772, 0.43494, 0.17475, 0.40406, 0.28834, 0.35703, 0.37316, 0.26223, 0.52517, 0.17847, 0.64488, 0.07147, 0.75643, 0.03017, 0.77518, 0.34555, 0.68437, 0.19068, 0.81269, 0.27328, 0.80506, 0.19443, 0.91759, 0.16158, 0.94819, 0.51201, 0.39049, 0.50618, 0.52078, 0.50082, 0.66719, 0.49394, 0.73369, 0.44707, 0.84733, 0.39333, 0.90974, 0.30152, 0.93093, 0.72268, 0.63135, 0.66011, 0.75522], "triangles": [56, 55, 35, 61, 35, 60, 36, 48, 56, 36, 50, 48, 49, 48, 50, 57, 36, 56, 17, 61, 18, 11, 46, 49, 47, 46, 11, 10, 47, 11, 12, 11, 49, 58, 36, 57, 51, 49, 50, 12, 49, 51, 59, 50, 36, 59, 36, 58, 51, 50, 59, 52, 12, 51, 35, 57, 56, 35, 17, 57, 17, 35, 61, 17, 16, 57, 58, 57, 16, 15, 59, 58, 15, 58, 16, 13, 12, 52, 14, 51, 59, 14, 59, 15, 52, 51, 14, 13, 52, 14, 26, 0, 25, 26, 25, 24, 54, 43, 53, 54, 53, 31, 32, 44, 43, 7, 6, 44, 20, 34, 31, 19, 34, 20, 54, 32, 43, 44, 45, 7, 33, 44, 32, 34, 35, 31, 31, 55, 54, 33, 45, 44, 8, 7, 45, 55, 32, 54, 31, 35, 55, 48, 33, 32, 48, 32, 55, 19, 60, 34, 18, 60, 19, 35, 34, 60, 56, 48, 55, 61, 60, 18, 46, 8, 45, 9, 8, 46, 47, 9, 46, 33, 48, 49, 49, 45, 33, 49, 46, 45, 10, 9, 47, 30, 20, 31, 53, 28, 31, 37, 1, 0, 37, 0, 26, 38, 37, 26, 41, 3, 2, 4, 3, 41, 26, 24, 23, 27, 1, 37, 27, 37, 38, 39, 38, 26, 39, 26, 23, 27, 38, 39, 30, 27, 39, 28, 41, 2, 27, 28, 2, 27, 2, 1, 22, 39, 23, 40, 30, 39, 22, 40, 39, 41, 29, 4, 42, 41, 28, 42, 29, 41, 6, 5, 4, 29, 6, 4, 21, 40, 22, 43, 29, 42, 53, 42, 28, 43, 42, 53, 21, 30, 40, 31, 28, 27, 31, 27, 30, 43, 44, 6, 43, 6, 29, 21, 20, 30], "vertices": [2, 7, 16.45, -39.24, 0.09968, 5, 21.96, 11.93, 0.90032, 2, 7, 67.94, -27.74, 0.95821, 5, 6.03, 62.23, 0.04179, 1, 7, 113.19, -28.59, 1, 1, 7, 164.51, -33.43, 1, 1, 7, 211.09, -35.5, 1, 2, 7, 260.26, -24.66, 0.97794, 14, 184.14, -115.15, 0.02206, 2, 7, 257.98, 6.19, 0.78598, 14, 175.78, -85.36, 0.21402, 2, 7, 289.65, 52.11, 0.13289, 14, 197.7, -34.07, 0.86711, 2, 14, 222.74, 16.05, 0.78523, 55, 173.76, -85.93, 0.21477, 2, 14, 239.25, 47.54, 0.53738, 55, 196.92, -58.94, 0.46262, 2, 14, 240.35, 76.98, 0.44593, 55, 204.6, -30.5, 0.55407, 2, 14, 208.29, 81.94, 0.29747, 55, 174.46, -18.48, 0.70253, 2, 14, 199.98, 83.22, 0.17708, 55, 166.65, -15.36, 0.82292, 1, 55, 183.68, 15.46, 1, 1, 55, 150.85, 27.04, 1, 1, 55, 115.32, 30.63, 1, 1, 55, 55.69, 41.82, 1, 2, 14, 23.74, 85.6, 0.00122, 55, -4.57, 26.48, 0.99878, 2, 14, -21.58, 42.03, 0.68306, 55, -58.5, -5.82, 0.31694, 3, 7, 55.65, 122.79, 0.14498, 14, -45.68, -11.29, 0.85414, 55, -93.95, -52.37, 0.00088, 2, 7, 34.75, 85.64, 0.50886, 14, -58.78, -51.86, 0.49114, 2, 7, 28.59, 75.98, 0.61799, 14, -62.89, -62.54, 0.38201, 2, 7, 13.82, 52.82, 0.84353, 14, -72.77, -88.18, 0.15647, 2, 7, -12.69, 25.14, 0.98474, 14, -93.26, -120.57, 0.01526, 2, 7, -21.72, -12.18, 0.42354, 5, -1.68, -28.45, 0.57646, 2, 7, -22.41, -29.84, 0.11546, 5, 15.97, -27.6, 0.88454, 2, 7, 4.85, -8.87, 0.4564, 5, -7.29, -2.26, 0.5436, 2, 7, 66.14, 18.51, 0.77929, 14, -14.68, -111.4, 0.22071, 2, 7, 129.38, 22.69, 0.75328, 14, 46.47, -94.74, 0.24672, 2, 7, 205.91, 7.97, 0.96417, 14, 124.4, -93.97, 0.03583, 2, 7, 42.77, 38.24, 0.77445, 14, -41.51, -96.72, 0.22555, 3, 7, 115.73, 73.62, 0.19723, 14, 22.97, -47.54, 0.56277, 55, -35.18, -103.1, 0.24, 3, 7, 194.13, 97.95, 0.01922, 14, 94.98, -8.12, 0.74878, 55, 43.84, -80.83, 0.232, 2, 14, 164.54, 8.22, 0.80195, 55, 115.3, -80.5, 0.19805, 3, 7, 93.6, 114.15, 0.05113, 14, -6.76, -12.22, 0.76487, 55, -56.23, -62, 0.184, 2, 14, 65.79, 41.92, 0.33373, 55, 26.62, -25.52, 0.66627, 2, 14, 130.06, 57.41, 0.2323, 55, 92.73, -24.83, 0.7677, 2, 7, 51.66, -9.75, 0.95215, 5, -10.48, 44.45, 0.04785, 2, 7, 42.45, 7.93, 0.86773, 14, -35.8, -126.49, 0.13227, 2, 7, 36.28, 26.18, 0.74931, 14, -45.47, -109.82, 0.25069, 2, 7, 30.59, 49.1, 0.8058, 14, -55.6, -88.49, 0.1942, 1, 7, 165.99, -19.68, 1, 2, 7, 184.05, 12.17, 0.94571, 14, 102.14, -94.19, 0.05429, 2, 7, 205.13, 34.12, 0.68656, 14, 118.44, -68.49, 0.31344, 2, 7, 246.26, 72.66, 0.11696, 14, 151.09, -22.55, 0.88304, 2, 14, 179.76, 13.51, 0.91172, 55, 131.31, -78.76, 0.08828, 2, 14, 215.91, 46.76, 0.55464, 55, 174, -54.46, 0.44536, 2, 14, 229.58, 52.07, 0.52127, 55, 188.51, -52.36, 0.47873, 2, 14, 125.79, 27.78, 0.71077, 55, 81.91, -52.75, 0.28923, 2, 14, 177.73, 65.65, 0.24682, 55, 141.02, -27.5, 0.75318, 2, 14, 150.73, 64.29, 0.18449, 55, 114.41, -22.77, 0.81551, 2, 14, 177.72, 98.19, 0.00234, 55, 148.31, 4.22, 0.99766, 2, 14, 188.78, 107.27, 0.0039, 55, 161.12, 10.58, 0.9961, 3, 7, 157.29, 51.23, 0.41352, 14, 68.16, -61.23, 0.34648, 55, 5.79, -126.57, 0.24, 3, 7, 168.64, 90.04, 0.05778, 14, 71.56, -20.94, 0.50222, 55, 18.14, -88.07, 0.44, 2, 14, 75.01, 24.35, 0.70029, 55, 31.66, -44.71, 0.29971, 2, 14, 78.02, 44.87, 0.30901, 55, 39.19, -25.39, 0.69099, 1, 55, 63.12, 4.65, 1, 1, 55, 85.08, 18.76, 1, 1, 55, 115.68, 17.3, 1, 2, 14, 2.31, 15.95, 0.87665, 55, -41.07, -36.59, 0.12335, 2, 14, 24.13, 53.56, 0.14518, 55, -11.37, -4.83, 0.85482], "hull": 26, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 0, 50, 54, 56, 60, 62, 64, 66, 68, 70, 52, 76, 76, 54, 74, 76, 76, 78, 78, 80, 40, 42, 42, 44, 80, 42, 74, 2, 2, 4, 0, 2, 56, 84, 84, 58, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 64, 96, 96, 98, 20, 22, 22, 24, 98, 22, 96, 100, 100, 102, 102, 104, 62, 108, 108, 64, 106, 108, 108, 110, 70, 112, 112, 72, 110, 112, 112, 114, 114, 116, 116, 118, 120, 122], "width": 326, "height": 310}}, "qundai": {"qundai": {"type": "mesh", "uvs": [0.93699, 0.00108, 0.85369, 0.0025, 0.77837, 0.11883, 0.73505, 0.23679, 0.69174, 0.38425, 0.60324, 0.49894, 0.47895, 0.62018, 0.35466, 0.73487, 0.20401, 0.84301, 0.06843, 0.91018, 0.23414, 1, 0.35278, 0.96589, 0.45447, 0.86594, 0.53921, 0.766, 0.63148, 0.65459, 0.71811, 0.54481, 0.79155, 0.4334, 0.84427, 0.28594, 0.89888, 0.16798, 0.97044, 0.06967, 1, 0.04378, 1, 0], "triangles": [9, 8, 10, 10, 8, 11, 11, 8, 12, 8, 7, 12, 12, 7, 13, 7, 6, 13, 13, 6, 14, 6, 5, 14, 14, 5, 15, 5, 4, 15, 15, 4, 16, 16, 4, 17, 4, 3, 17, 17, 3, 18, 3, 2, 18, 2, 1, 18, 19, 1, 0, 19, 18, 1, 19, 0, 20, 0, 21, 20], "vertices": [1, 49, -14.1, 10.36, 1, 1, 49, -5.31, -13.75, 1, 1, 49, 41.16, -22.37, 1, 2, 49, 84.99, -21.46, 0.99904, 50, -24.19, -25.54, 0.00096, 1, 50, 28.19, -12.94, 1, 1, 50, 76.83, -18.06, 1, 2, 50, 132.66, -31.84, 0.02682, 51, 44.47, -23.84, 0.97318, 2, 51, 100.18, -27.02, 0.09436, 52, 18.78, -23.64, 0.90564, 1, 52, 78.91, -22.01, 1, 1, 52, 126.47, -28.95, 1, 1, 52, 105.79, 27.51, 1, 1, 52, 69.56, 40.48, 1, 2, 51, 115.92, 26.37, 0.0079, 52, 23.1, 31.86, 0.9921, 2, 51, 72.04, 23.58, 0.93711, 52, -19.24, 20.02, 0.06289, 1, 51, 23.57, 19.96, 1, 2, 50, 74.65, 20.81, 0.59234, 51, -23.35, 15.38, 0.40766, 2, 50, 29.21, 22.37, 0.99979, 51, -68.09, 7.32, 0.00021, 2, 49, 90.56, 16, 0.99907, 50, -24.53, 12.33, 0.00093, 1, 49, 45.6, 18.39, 1, 1, 49, 5.54, 27.97, 1, 1, 49, -6.08, 33.61, 1, 1, 49, -20.74, 28.59, 1], "hull": 22, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 2, 0, 0, 42], "width": 308, "height": 354}}, "erzhui_zuo": {"erzhui_zuo": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 25, 37.1, 3.84, 1, 1, 25, 33.01, -15.74, 1, 1, 25, -0.28, -8.79, 1, 1, 25, 3.82, 10.79, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 34}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.9, 0, 0.8, 0, 0.7, 0, 0.6, 0, 0.5, 0, 0.4, 0, 0.3, 0, 0.2, 0, 0.1, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 1, 0.1, 1, 0.2, 1, 0.3, 1, 0.4, 1, 0.5, 1, 0.6, 1, 0.7, 1, 0.8, 1, 0.9, 0.8, 0.9, 0.6, 0.9, 0.4, 0.9, 0.2, 0.9, 0.8, 0.8, 0.6, 0.8, 0.4, 0.8, 0.2, 0.8, 0.8, 0.7, 0.6, 0.7, 0.4, 0.7, 0.2, 0.7, 0.8, 0.6, 0.6, 0.6, 0.4, 0.6, 0.2, 0.6, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2, 0.5, 0.8, 0.4, 0.6, 0.4, 0.4, 0.4, 0.2, 0.4, 0.8, 0.3, 0.6, 0.3, 0.4, 0.3, 0.2, 0.3, 0.8, 0.2, 0.6, 0.2, 0.4, 0.2, 0.2, 0.2, 0.8, 0.1, 0.6, 0.1, 0.4, 0.1, 0.2, 0.1], "triangles": [50, 54, 23, 34, 38, 27, 64, 65, 17, 63, 64, 18, 62, 63, 19, 58, 62, 21, 57, 12, 61, 53, 11, 57, 49, 10, 53, 45, 9, 49, 41, 8, 45, 14, 15, 16, 65, 14, 16, 65, 16, 17, 64, 17, 18, 63, 18, 19, 13, 14, 65, 12, 13, 61, 22, 58, 21, 11, 12, 57, 10, 11, 53, 9, 10, 49, 26, 42, 25, 8, 9, 45, 27, 38, 26, 7, 8, 41, 28, 34, 27, 6, 7, 37, 29, 30, 28, 5, 6, 33, 3, 4, 32, 2, 3, 31, 1, 30, 29, 0, 1, 29, 1, 2, 30, 2, 31, 30, 3, 32, 31, 4, 33, 32, 4, 5, 33, 25, 46, 24, 24, 50, 23, 23, 54, 22, 61, 13, 65, 62, 19, 20, 21, 62, 20, 30, 34, 28, 33, 6, 37, 37, 7, 41, 38, 42, 26, 42, 46, 25, 46, 50, 24, 54, 58, 22, 30, 31, 34, 31, 35, 34, 32, 36, 35, 31, 32, 35, 32, 33, 36, 33, 37, 36, 34, 35, 38, 35, 39, 38, 36, 40, 39, 35, 36, 39, 37, 41, 40, 36, 37, 40, 38, 39, 42, 39, 43, 42, 40, 44, 43, 39, 40, 43, 41, 45, 44, 40, 41, 44, 42, 43, 46, 43, 44, 47, 44, 45, 48, 46, 47, 50, 43, 47, 46, 47, 48, 51, 44, 48, 47, 48, 49, 52, 45, 49, 48, 47, 51, 50, 51, 52, 55, 48, 52, 51, 52, 53, 56, 49, 53, 52, 54, 55, 58, 51, 55, 54, 50, 51, 54, 55, 56, 59, 52, 56, 55, 56, 57, 60, 53, 57, 56, 58, 59, 62, 55, 59, 58, 59, 60, 63, 56, 60, 59, 60, 61, 64, 57, 61, 60, 59, 63, 62, 60, 64, 63, 61, 65, 64], "vertices": [1, 56, 496.46, -1388.37, 1, 1, 56, 357.67, -1386.88, 1, 1, 56, 218.88, -1385.38, 1, 1, 56, 80.09, -1383.89, 1, 1, 56, -58.71, -1382.4, 1, 1, 56, -197.5, -1380.91, 1, 1, 56, -196.11, -1251.91, 1, 1, 56, -194.72, -1122.92, 1, 1, 56, -193.34, -993.93, 1, 1, 56, -191.95, -864.94, 1, 1, 56, -190.56, -735.94, 1, 1, 56, -189.18, -606.95, 1, 1, 56, -187.79, -477.96, 1, 1, 56, -186.4, -348.97, 1, 1, 56, -185.01, -219.97, 1, 1, 56, -183.63, -90.98, 1, 1, 56, -44.84, -92.47, 1, 1, 56, 93.96, -93.97, 1, 1, 56, 232.75, -95.46, 1, 1, 56, 371.54, -96.95, 1, 1, 56, 510.33, -98.44, 1, 1, 56, 508.95, -227.44, 1, 1, 56, 507.56, -356.43, 1, 1, 56, 506.17, -485.42, 1, 1, 56, 504.78, -614.41, 1, 1, 56, 503.4, -743.41, 1, 1, 56, 502.01, -872.4, 1, 1, 56, 500.62, -1001.39, 1, 1, 56, 499.24, -1130.38, 1, 1, 56, 497.85, -1259.38, 1, 4, 105, 76.18, -49.49, 0.16739, 106, -30.53, -52.31, 0.39375, 110, 76.18, 60.23, 0.12771, 111, -30.53, 57.41, 0.31115, 4, 104, 70.2, -52.04, 0.24905, 105, -62.61, -48, 0.31693, 109, 70.2, 57.68, 0.20113, 110, -62.61, 61.72, 0.23289, 6, 98, 67.01, -182.49, 0.0009, 99, -68.59, -182.16, 0.00103, 103, 67.01, -50.88, 0.28202, 104, -68.59, -50.55, 0.26969, 108, 67.01, 58.84, 0.2376, 109, -68.59, 59.17, 0.20877, 4, 102, 43.6, -47.13, 0.37886, 103, -71.78, -49.38, 0.21732, 107, 43.6, 62.59, 0.22999, 108, -71.78, 60.34, 0.17383, 4, 100, 77.57, -52.11, 0.18844, 101, -29.14, -54.92, 0.39844, 105, 77.57, 79.5, 0.11573, 106, -29.14, 76.69, 0.29739, 6, 94, 71.59, -192.28, 0.0002, 95, -61.23, -188.25, 0.00205, 99, 71.59, -54.66, 0.25094, 100, -61.23, -50.62, 0.34457, 104, 71.59, 76.95, 0.19578, 105, -61.23, 80.99, 0.20647, 6, 93, 68.4, -191.12, 0.00234, 94, -67.2, -190.79, 0.00152, 98, 68.4, -53.5, 0.28735, 99, -67.2, -53.16, 0.2894, 103, 68.4, 78.12, 0.21366, 104, -67.2, 78.45, 0.20573, 5, 93, -70.39, -189.63, 6e-05, 97, 44.99, -49.75, 0.3564, 98, -70.39, -52, 0.24342, 102, 44.99, 81.86, 0.23157, 103, -70.39, 79.61, 0.16856, 5, 81, -27.76, -406.58, 6e-05, 95, 78.95, -60.74, 0.16593, 96, -27.76, -63.56, 0.38612, 100, 78.95, 76.88, 0.13216, 101, -27.76, 74.07, 0.31573, 5, 94, 72.98, -63.29, 0.23855, 95, -59.84, -59.25, 0.31344, 99, 72.98, 74.34, 0.2049, 100, -59.84, 78.37, 0.24303, 101, -166.55, 75.56, 9e-05, 4, 93, 69.79, -62.13, 0.27799, 94, -65.81, -61.8, 0.29117, 98, 69.79, 75.5, 0.21147, 99, -65.81, 75.83, 0.21937, 4, 92, 46.38, -58.39, 0.32033, 93, -69.01, -60.64, 0.25011, 97, 46.38, 79.24, 0.2401, 98, -69.01, 76.99, 0.18947, 5, 85, 80.34, -154.2, 0.00023, 90, 80.34, -36.64, 0.16595, 91, -26.37, -39.45, 0.48978, 95, 80.34, 68.25, 0.09438, 96, -26.37, 65.43, 0.24966, 6, 84, 74.36, -156.75, 0.00637, 85, -58.45, -152.71, 0.00711, 89, 74.36, -39.19, 0.25412, 90, -58.45, -35.15, 0.36489, 94, 74.36, 65.7, 0.16865, 95, -58.45, 69.74, 0.19887, 6, 83, 71.17, -155.58, 0.00748, 84, -64.43, -155.25, 0.00466, 88, 71.17, -38.02, 0.30099, 89, -64.43, -37.69, 0.31858, 93, 71.17, 66.86, 0.17293, 94, -64.43, 67.19, 0.19535, 5, 83, -67.62, -154.09, 0.00224, 87, 47.77, -34.28, 0.3688, 88, -67.62, -36.53, 0.2867, 92, 47.77, 70.61, 0.17681, 93, -67.62, 68.35, 0.16546, 5, 80, 81.73, -145.78, 0.00237, 85, 81.73, -25.21, 0.18242, 86, -24.98, -28.02, 0.58362, 90, 81.73, 92.35, 0.06442, 91, -24.98, 89.54, 0.16717, 6, 79, 75.75, -148.33, 0.01073, 80, -57.07, -144.29, 0.01533, 84, 75.75, -27.75, 0.28123, 85, -57.07, -23.71, 0.44002, 89, 75.75, 89.81, 0.11598, 90, -57.07, 93.85, 0.13671, 6, 78, 72.56, -147.16, 0.01434, 79, -63.04, -146.83, 0.01389, 83, 72.56, -26.59, 0.32852, 84, -63.04, -26.26, 0.37799, 88, 72.56, 90.97, 0.12898, 89, -63.04, 91.3, 0.13628, 6, 77, 49.15, -143.42, 0.00608, 78, -66.23, -145.67, 0.0081, 82, 49.15, -22.85, 0.43231, 83, -66.23, -25.1, 0.32593, 87, 49.15, 94.71, 0.11067, 88, -66.23, 92.46, 0.11691, 6, 75, 83.11, -126.38, 0.00626, 80, 83.11, -16.79, 0.17523, 81, -23.6, -19.6, 0.66586, 85, 83.11, 103.79, 0.04679, 86, -23.6, 100.97, 0.10586, 101, -23.6, 461.05, 0, 6, 74, 77.14, -128.93, 0.0219, 75, -55.68, -124.89, 0.03029, 79, 77.14, -19.33, 0.29418, 80, -55.68, -15.29, 0.46892, 84, 77.14, 101.24, 0.08128, 85, -55.68, 105.28, 0.10344, 6, 73, 73.95, -127.77, 0.0192, 74, -61.65, -127.43, 0.02306, 78, 73.95, -18.17, 0.35296, 79, -61.65, -17.84, 0.41422, 83, 73.95, 102.4, 0.08788, 84, -61.65, 102.73, 0.10267, 6, 72, 50.54, -124.02, 0.01098, 73, -64.84, -126.27, 0.01776, 77, 50.54, -14.43, 0.45679, 78, -64.84, -16.68, 0.37582, 82, 50.54, 106.15, 0.06609, 83, -64.84, 103.89, 0.07255, 6, 70, 84.5, -103.65, 0.02675, 71, -22.21, -106.47, 0.02526, 75, 84.5, 2.61, 0.15423, 76, -22.21, -0.2, 0.75199, 80, 84.5, 112.21, 0.01689, 81, -22.21, 109.39, 0.02488, 6, 69, 78.53, -106.2, 0.05089, 70, -54.29, -102.16, 0.08128, 74, 78.53, 0.07, 0.29661, 75, -54.29, 4.11, 0.47766, 79, 78.53, 109.66, 0.04205, 80, -54.29, 113.7, 0.0515, 6, 68, 75.33, -105.04, 0.05915, 69, -60.27, -104.71, 0.06429, 73, 75.33, 1.23, 0.33775, 74, -60.27, 1.56, 0.43466, 78, 75.33, 110.82, 0.05214, 79, -60.27, 111.15, 0.05201, 6, 67, 51.93, -101.29, 0.05132, 68, -63.46, -103.54, 0.0616, 72, 51.93, 4.97, 0.43447, 73, -63.46, 2.72, 0.3888, 77, 51.93, 114.57, 0.02685, 78, -63.46, 112.31, 0.03696, 5, 65, 85.89, -71.06, 0.07049, 66, -20.82, -73.87, 0.17004, 70, 85.89, 25.34, 0.14368, 71, -20.82, 22.53, 0.61459, 75, 85.89, 131.61, 0.0012, 6, 64, 79.91, -73.6, 0.10495, 65, -52.9, -69.56, 0.18927, 69, 79.91, 22.79, 0.26017, 70, -52.9, 26.83, 0.4208, 74, 79.91, 129.06, 0.01316, 75, -52.9, 133.1, 0.01166, 6, 63, 76.72, -72.44, 0.13146, 64, -58.88, -72.11, 0.15721, 68, 76.72, 23.96, 0.28239, 69, -58.88, 24.29, 0.39727, 73, 76.72, 130.22, 0.01805, 74, -58.88, 130.55, 0.01362, 6, 62, 53.31, -68.7, 0.16901, 63, -62.07, -70.95, 0.13533, 67, 53.31, 27.7, 0.33691, 68, -62.07, 25.45, 0.34811, 72, 53.31, 133.96, 0.00419, 73, -62.07, 131.71, 0.00645, 4, 60, 87.27, -44.94, 0.09967, 61, -19.44, -47.75, 0.40202, 65, 87.27, 57.94, 0.09914, 66, -19.44, 55.12, 0.39917, 5, 59, 81.3, -47.49, 0.19099, 60, -51.52, -43.45, 0.33786, 64, 81.3, 55.39, 0.17944, 65, -51.52, 59.43, 0.29088, 69, 81.3, 151.79, 0.00082, 6, 58, 78.11, -46.33, 0.22495, 59, -57.49, -45.99, 0.29718, 63, 78.11, 56.55, 0.20485, 64, -57.49, 56.88, 0.27191, 68, 78.11, 152.95, 0.00109, 69, -57.49, 153.28, 3e-05, 4, 57, 54.7, -42.58, 0.27232, 58, -60.68, -44.83, 0.26017, 62, 54.7, 60.3, 0.231, 63, -60.68, 58.04, 0.23651], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 0], "width": 485, "height": 903}}, "heye_youxia": {"heye_youxia": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75.28, -94.21, -93.72, -94.21, -93.72, 58.79, 75.28, 58.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 169, "height": 153}}, "gebo_you": {"gebo_you": {"type": "mesh", "uvs": [0.0124, 0.00257, 0.1346, 0, 0.23545, 0.02736, 0.30915, 0.07329, 0.35182, 0.13575, 0.44298, 0.20096, 0.47595, 0.26709, 0.55741, 0.36721, 0.62918, 0.4462, 0.70288, 0.51417, 0.7824, 0.58581, 0.87356, 0.66848, 1, 0.79615, 1, 0.85034, 1, 0.92015, 1, 0.9771, 0.95114, 1, 0.80761, 1, 0.67379, 0.99087, 0.49923, 0.96148, 0.34213, 0.9128, 0.21993, 0.85126, 0.16175, 0.78513, 0.14817, 0.71893, 0.18502, 0.68679, 0.17339, 0.5931, 0.15011, 0.5187, 0.14623, 0.44246, 0.1249, 0.37725, 0.11908, 0.28632, 0.13266, 0.23305, 0.08611, 0.17426, 0.03762, 0.11548, 0, 0.0668, 0.27812, 0.71526, 0.41777, 0.7024, 0.51862, 0.76394, 0.61754, 0.84109, 0.72033, 0.8861, 0.8755, 0.92835, 0.2723, 0.63259, 0.3848, 0.62708, 0.51086, 0.63535, 0.66991, 0.68036, 0.79598, 0.73914, 0.91429, 0.80068, 0.31109, 0.59034, 0.45462, 0.53523, 0.61172, 0.53431, 0.72227, 0.57197, 0.2723, 0.48012, 0.39061, 0.4691, 0.50311, 0.41032, 0.17727, 0.36898, 0.31885, 0.30653, 0.41195, 0.23213, 0.18308, 0.25142, 0.27036, 0.19447, 0.20248, 0.14369, 0.1249, 0.06286, 0.72261, 0.63533, 0.35823, 0.81009, 0.49516, 0.86834], "triangles": [11, 43, 60, 44, 43, 11, 36, 43, 44, 44, 11, 12, 45, 44, 12, 37, 36, 44, 37, 44, 45, 45, 12, 13, 37, 62, 36, 38, 37, 45, 39, 38, 45, 13, 39, 45, 14, 39, 13, 38, 19, 62, 38, 62, 37, 20, 62, 19, 39, 14, 15, 18, 19, 38, 18, 38, 39, 17, 18, 39, 16, 17, 39, 15, 16, 39, 40, 46, 41, 43, 42, 60, 24, 25, 40, 35, 41, 42, 35, 34, 40, 35, 40, 41, 24, 40, 34, 23, 24, 34, 43, 35, 42, 36, 35, 43, 22, 23, 34, 61, 34, 35, 61, 35, 36, 22, 34, 61, 62, 61, 36, 21, 22, 61, 20, 21, 61, 20, 61, 62, 59, 0, 1, 59, 1, 2, 33, 0, 59, 32, 33, 59, 3, 58, 59, 3, 59, 2, 58, 3, 4, 32, 59, 58, 31, 32, 58, 57, 58, 4, 57, 4, 5, 55, 57, 5, 57, 30, 31, 57, 31, 58, 56, 30, 57, 55, 5, 6, 29, 30, 56, 55, 56, 57, 54, 55, 6, 54, 56, 55, 54, 6, 7, 54, 53, 29, 54, 29, 56, 28, 29, 53, 52, 54, 7, 53, 54, 52, 27, 28, 53, 53, 50, 27, 52, 7, 8, 52, 51, 53, 51, 50, 53, 26, 27, 50, 8, 51, 52, 48, 8, 9, 8, 47, 51, 48, 47, 8, 10, 49, 9, 48, 9, 49, 47, 46, 50, 47, 50, 51, 26, 50, 46, 25, 26, 46, 41, 46, 47, 40, 25, 46, 60, 49, 10, 42, 47, 48, 42, 48, 49, 42, 49, 60, 41, 47, 42, 60, 10, 11], "vertices": [1, 6, 19.81, 18.2, 1, 1, 6, 17.03, -7.95, 1, 2, 9, -13.95, 32.07, 0.36621, 6, 1.5, -27.53, 0.63379, 2, 9, 11.55, 38, 0.83811, 6, -21.49, -40.07, 0.16189, 2, 9, 41.18, 34.76, 0.99485, 6, -50.9, -44.89, 0.00515, 1, 9, 76.22, 40.53, 1, 1, 9, 106.51, 34.71, 1, 1, 9, 155.15, 32.09, 1, 1, 9, 194.19, 31.49, 1, 2, 9, 228.83, 33.32, 0.99827, 13, -77.2, -12.14, 0.00173, 2, 9, 265.5, 35.61, 0.82072, 13, -45.27, 6.06, 0.17928, 2, 9, 307.77, 38.14, 0.08677, 13, -8.44, 26.93, 0.91323, 1, 13, 48.56, 56.09, 1, 1, 13, 73.15, 56.93, 1, 1, 13, 104.82, 58.02, 1, 1, 13, 130.66, 58.9, 1, 1, 13, 141.41, 48.76, 1, 1, 13, 142.47, 17.92, 1, 2, 10, 96.77, 124.36, 0.01791, 13, 139.31, -10.98, 0.98209, 2, 10, 117.37, 90.27, 0.23529, 13, 127.26, -48.94, 0.76471, 2, 10, 129.51, 51.78, 0.63332, 13, 106.33, -83.46, 0.36668, 2, 10, 132.16, 13.52, 0.93999, 13, 79.31, -110.67, 0.06001, 2, 9, 293.46, -123.17, 0.0036, 10, 122.83, -17.64, 0.9964, 2, 9, 264.84, -113.54, 0.05325, 10, 106.05, -42.75, 0.94675, 2, 9, 254.77, -100.33, 0.13788, 10, 90.68, -49.03, 0.86212, 2, 9, 214.94, -85.21, 0.56608, 10, 65.68, -83.53, 0.43392, 2, 9, 182.07, -75.95, 0.83297, 10, 48.17, -112.84, 0.16703, 2, 9, 150.15, -62.55, 0.96184, 10, 26.89, -140.16, 0.03816, 2, 9, 121.26, -54.62, 0.99572, 10, 11.7, -165.98, 0.00428, 1, 9, 83.08, -38.87, 1, 1, 9, 62.2, -26.31, 1, 1, 9, 33.76, -24.52, 1, 2, 9, 5.14, -23.11, 0.95447, 6, -31.68, 20.52, 0.04553, 2, 9, -18.34, -21.44, 0.45066, 6, -8.62, 25.21, 0.54934, 2, 9, 274.76, -87.36, 0.06176, 10, 83.37, -26.35, 0.93824, 2, 9, 281.72, -57.58, 0.0399, 10, 56.44, -11.86, 0.9601, 2, 10, 57.35, 23.5, 0.75215, 13, 37.49, -47.85, 0.24785, 2, 10, 63.07, 64.08, 0.19961, 13, 71.77, -25.39, 0.80039, 2, 10, 58.9, 93.89, 0.00489, 13, 91.43, -2.61, 0.99511, 1, 13, 109.46, 31.39, 1, 2, 9, 240, -73.14, 0.37252, 10, 60.58, -56.19, 0.62748, 2, 9, 247.61, -50.05, 0.4187, 10, 40.27, -42.81, 0.5813, 2, 9, 262.13, -26.86, 0.41598, 10, 21.67, -22.74, 0.58402, 3, 9, 294.77, -4.02, 8e-05, 10, 8.15, 14.73, 0.62326, 13, -1.55, -16.64, 0.37666, 1, 13, 24.19, 11.36, 1, 1, 13, 51.25, 37.74, 1, 2, 9, 225.91, -57.69, 0.5925, 10, 41.98, -65.75, 0.4075, 2, 9, 215.71, -19.29, 0.92973, 10, 2.25, -65.58, 0.07027, 1, 9, 229.15, 11.7, 1, 2, 9, 254.48, 26.39, 0.93815, 13, -51.11, -7.08, 0.06185, 2, 9, 176.84, -44.82, 0.92642, 10, 16.74, -109.76, 0.07358, 2, 9, 182.68, -19.56, 0.9772, 10, -6.11, -97.53, 0.0228, 1, 9, 168.23, 13.43, 1, 2, 9, 122.44, -42.81, 0.99702, 10, 0.61, -161.75, 0.00298, 1, 9, 109.02, -3.43, 1, 1, 9, 86.4, 28.65, 1, 1, 9, 74.25, -19.83, 1, 1, 9, 58.34, 7.87, 1, 2, 9, 31.33, 3.99, 0.99972, 6, -49.66, -12.61, 0.00028, 2, 9, -8.98, 3.79, 0.55917, 6, -10.88, -1.61, 0.44083, 3, 9, 280.75, 14.69, 0.60028, 10, -13.57, 6.08, 0.10446, 13, -22.37, -6.02, 0.29526, 2, 10, 97.31, 17.88, 0.9093, 13, 59.61, -81.6, 0.0907, 2, 10, 91.27, 56.99, 0.4492, 13, 85.03, -51.27, 0.5508], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 92, 94, 94, 96, 96, 98, 100, 102, 102, 104, 106, 108, 108, 110, 112, 114, 114, 8, 116, 118, 122, 124], "width": 215, "height": 454}}, "erzhui_you": {"erzhui_you": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 24, 51.85, 9.51, 1, 1, 24, 46.81, -18.03, 1, 1, 24, -5.33, -8.49, 1, 1, 24, -0.29, 19.05, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 53}}, "heye_you_da": {"heye_you_da": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.75, 0.66667, 0.5, 0.66667, 0.25, 0.66667, 0.75, 0.33333, 0.5, 0.33333, 0.25, 0.33333], "triangles": [0, 1, 13, 1, 14, 13, 13, 14, 12, 14, 17, 12, 12, 17, 11, 1, 2, 14, 2, 15, 14, 14, 15, 17, 16, 19, 18, 19, 8, 9, 16, 5, 19, 5, 6, 19, 19, 6, 8, 6, 7, 8, 17, 10, 11, 15, 18, 17, 17, 18, 10, 18, 9, 10, 15, 16, 18, 18, 19, 9, 2, 3, 15, 3, 16, 15, 3, 4, 16, 4, 5, 16], "vertices": [4, 114, 31.38, -188.56, 0.13201, 115, 0.65, -195.17, 4e-05, 116, -206.47, -103.52, 5e-05, 117, 97.1, -142.23, 0.8679, 4, 114, 29.57, -98.7, 0.28088, 115, -24.57, -108.91, 0.04902, 116, -149.33, -34.16, 7e-05, 117, 11.31, -115.44, 0.67002, 4, 114, 18.4, -36.19, 0.79896, 115, -51.7, -51.5, 0.04578, 116, -117.1, 20.56, 0.00011, 117, -51.52, -106.21, 0.15514, 4, 114, 7.22, 26.32, 0.93105, 115, -78.83, 5.92, 0.00019, 116, -84.88, 75.28, 0.06758, 117, -114.34, -96.99, 0.00118, 4, 114, -3.96, 88.83, 0.79264, 115, -105.96, 63.33, 3e-05, 116, -52.65, 129.99, 0.20627, 117, -177.17, -87.76, 0.00106, 3, 114, 73.48, 102.67, 0.44575, 116, 15.13, 90.07, 0.55387, 117, -165.74, -9.93, 0.00038, 3, 114, 150.92, 116.52, 0.03655, 116, 82.92, 50.15, 0.96343, 117, -154.31, 67.9, 3e-05, 1, 116, 150.7, 10.23, 1, 2, 115, 134.55, 106.74, 0.18141, 116, 118.48, -44.49, 0.81859, 2, 115, 161.68, 49.32, 0.7702, 116, 86.25, -99.21, 0.2298, 3, 115, 188.81, -8.09, 0.85268, 116, 54.03, -153.92, 0.00281, 117, 45.6, 118.06, 0.14451, 2, 115, 223.78, -98.55, 0.63563, 117, 140.28, 97.06, 0.36437, 2, 115, 145.01, -118.17, 0.1902, 117, 112.97, 20.61, 0.8098, 3, 114, 108.26, -179.56, 0.03614, 116, -142.28, -146.76, 2e-05, 117, 112.95, -66.47, 0.96384, 4, 114, 107.01, -84.85, 0.05955, 115, 46.55, -75.3, 0.03495, 116, -81.54, -74.08, 2e-05, 117, 22.74, -37.61, 0.90548, 4, 114, 95.83, -22.34, 0.05174, 115, 19.43, -17.89, 0.79801, 116, -49.32, -19.36, 0, 117, -40.09, -28.38, 0.15025, 4, 114, 84.66, 40.16, 0.45766, 115, -7.7, 39.52, 0.05347, 116, -17.09, 35.35, 0.48874, 117, -102.91, -19.16, 0.00012, 2, 115, 117.68, -41.7, 0.45817, 117, 34.17, 40.22, 0.54183, 2, 115, 90.55, 15.72, 0.88452, 116, 18.47, -59.28, 0.11548, 2, 115, 63.43, 73.13, 0.0172, 116, 50.69, -4.57, 0.9828], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 254, "height": 236}}, "shou__zuo": {"shou__zuo": {"type": "mesh", "uvs": [0.80598, 0, 0.8766, 0.09668, 1, 0.17027, 0.98157, 0.26903, 0.88232, 0.29421, 0.78307, 0.39104, 0.68955, 0.51304, 0.60175, 0.61568, 0.5235, 0.73769, 0.40707, 0.86937, 0.32015, 1, 0.13413, 1, 0.00625, 0.97782, 0.00816, 0.86163, 0.06924, 0.73575, 0.14368, 0.62149, 0.24483, 0.52466, 0.36317, 0.44332, 0.45669, 0.35811, 0.51586, 0.29227, 0.64183, 0.19157, 0.72963, 0.11217, 0.74872, 0, 0.16849, 0.8287, 0.304, 0.70476, 0.42997, 0.58276, 0.55404, 0.46075, 0.81934, 0.19157], "triangles": [0, 21, 22, 0, 1, 21, 27, 21, 1, 2, 27, 1, 2, 4, 27, 3, 4, 2, 27, 20, 21, 5, 27, 4, 5, 20, 27, 19, 20, 5, 26, 19, 5, 18, 19, 26, 6, 26, 5, 26, 25, 17, 26, 17, 18, 16, 17, 25, 7, 26, 6, 25, 26, 7, 24, 16, 25, 15, 16, 24, 8, 25, 7, 24, 25, 8, 23, 15, 24, 14, 15, 23, 13, 14, 23, 9, 24, 8, 23, 24, 9, 10, 23, 9, 11, 13, 23, 11, 23, 10, 12, 13, 11], "vertices": [1, 8, 222.77, 22.22, 1, 1, 8, 217.03, -1.77, 1, 1, 8, 221.98, -31.13, 1, 1, 8, 204.01, -41.21, 1, 1, 8, 186.78, -28.72, 1, 1, 8, 158.31, -25.67, 1, 1, 8, 126.64, -26.85, 1, 1, 8, 98.79, -26.39, 1, 1, 8, 69.17, -30, 1, 1, 8, 30.48, -33.78, 1, 1, 8, 2.02, -33.76, 1, 1, 8, -24.09, -2.54, 1, 1, 8, -37.71, 20.75, 1, 1, 8, -19.21, 35.76, 1, 1, 8, 8.72, 42.62, 1, 1, 8, 36.62, 45.82, 1, 1, 8, 65.35, 42.47, 1, 1, 8, 93.95, 34.34, 1, 1, 8, 119.83, 30.67, 1, 1, 8, 138.08, 29.92, 1, 1, 8, 170.74, 23.12, 1, 1, 8, 194.95, 19.6, 1, 1, 8, 215.11, 31.34, 1, 1, 8, 7.4, 14.56, 1, 1, 8, 44.98, 9.3, 1, 1, 8, 80.99, 5.32, 1, 1, 8, 116.73, 1.63, 1, 1, 8, 194.48, -5.16, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 46, 48, 48, 50, 50, 52, 42, 54], "width": 228, "height": 225}}, "shou_you": {"shou_you": {"type": "mesh", "uvs": [0.11363, 0.00771, 0.38044, 0, 0.50838, 0.23359, 0.61532, 0.33688, 0.76809, 0.18729, 0.92468, 0.26565, 1, 0.49004, 1, 0.71087, 0.81392, 0.9317, 0.66115, 1, 0.52939, 0.99225, 0.39189, 0.88896, 0.26968, 0.90321, 0.15892, 0.97088, 0.09017, 0.87471, 0.03479, 0.70375, 0, 0.55415, 0.01379, 0.30483, 0.16186, 0.21884, 0.22701, 0.4584, 0.30147, 0.57297, 0.40012, 0.73268, 0.08926, 0.46881, 0.16372, 0.62852, 0.24376, 0.76045, 0.30333, 0.09385, 0.36662, 0.33688, 0.48761, 0.63199, 0.56393, 0.83336, 0.64769, 0.45493, 0.73518, 0.58686, 0.77055, 0.816, 0.77799, 0.40285, 0.8934, 0.50006, 0.89154, 0.69796], "triangles": [9, 31, 8, 9, 28, 31, 8, 34, 7, 8, 31, 34, 28, 30, 31, 28, 29, 30, 31, 30, 34, 34, 33, 7, 33, 6, 7, 34, 30, 33, 30, 32, 33, 30, 29, 32, 33, 5, 6, 33, 32, 5, 29, 3, 32, 3, 4, 32, 32, 4, 5, 10, 28, 9, 10, 11, 28, 28, 11, 21, 28, 21, 27, 11, 12, 21, 28, 27, 29, 24, 20, 21, 21, 20, 27, 20, 26, 27, 27, 3, 29, 27, 2, 3, 27, 26, 2, 20, 19, 26, 19, 25, 26, 26, 1, 2, 26, 25, 1, 25, 0, 1, 12, 13, 24, 13, 14, 24, 21, 12, 24, 14, 23, 24, 14, 15, 23, 24, 23, 20, 15, 22, 23, 15, 16, 22, 23, 19, 20, 23, 22, 19, 16, 17, 22, 22, 18, 19, 22, 17, 18, 19, 18, 25, 17, 0, 18, 18, 0, 25], "vertices": [2, 12, -8.02, -31.33, 0.80403, 11, 106.58, -12.18, 0.19597, 2, 12, -33.75, 4.73, 0.00033, 11, 67.37, -32.8, 0.99967, 2, 11, 39.04, -23.84, 0.97611, 10, 101.95, -45.34, 0.02389, 2, 11, 19.05, -23.66, 0.59415, 10, 94.03, -26.99, 0.40585, 2, 11, 2.43, -46.99, 0.02005, 10, 65.97, -21.23, 0.97995, 1, 10, 50.27, 0.63, 1, 1, 10, 53.24, 24, 1, 1, 10, 65.69, 39.21, 1, 2, 11, -34.28, 8.69, 0.01774, 10, 102.04, 34.86, 0.98226, 3, 12, 13.01, 93.64, 0.00035, 11, -14.4, 25.57, 0.47963, 10, 125.52, 23.51, 0.52001, 3, 12, 24.87, 75.25, 0.02268, 11, 5.43, 34.83, 0.8994, 10, 142.01, 9.12, 0.07792, 2, 12, 30.28, 51.24, 0.21193, 11, 29.95, 36.93, 0.78807, 2, 12, 42.85, 35.27, 0.64161, 11, 47.48, 47.23, 0.35839, 2, 12, 58.25, 23.56, 0.89974, 11, 61.16, 60.9, 0.10026, 2, 12, 57.69, 9.31, 0.96726, 11, 75.21, 58.42, 0.03274, 1, 12, 50.4, -6.9, 1, 1, 12, 42.72, -19.22, 1, 1, 12, 23.16, -29.94, 1, 2, 12, 2.9, -14.07, 0.90139, 11, 90.95, 0.97, 0.09861, 2, 12, 14.3, 6.95, 0.80881, 11, 71.67, 15.11, 0.19119, 2, 12, 15.66, 22.91, 0.39574, 11, 56.04, 18.62, 0.60426, 2, 12, 18.06, 44.47, 0.17251, 11, 35.01, 23.91, 0.82749, 1, 12, 28.05, -11.34, 1, 2, 12, 32.73, 6.91, 0.95203, 11, 74.21, 33.36, 0.04797, 2, 12, 34.84, 24.51, 0.70084, 11, 57.05, 37.83, 0.29916, 2, 12, -19.6, -1.06, 0.04871, 11, 75.02, -19.56, 0.95129, 1, 11, 55.88, -5.01, 1, 2, 12, 2.43, 51.32, 0.02115, 11, 26.1, 9.35, 0.97885, 3, 12, 9.98, 71.93, 0.01174, 11, 6.7, 19.62, 0.90614, 10, 128.62, 1.81, 0.08212, 2, 11, 9.51, -16.71, 0.42703, 10, 96.53, -15.45, 0.57297, 1, 10, 92.72, 2.83, 1, 2, 11, -23.2, 2.75, 0.02336, 10, 101.09, 22.33, 0.97664, 2, 11, -7.7, -30.61, 0.01114, 10, 76.85, -5.34, 0.98886, 1, 10, 67.5, 13.48, 1, 1, 10, 78.89, 26.92, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 64, 66, 66, 68], "width": 166, "height": 89}}, "heye_zuo": {"heye_zuo": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [102.84, -72.56, -93.16, -72.56, -93.16, 53.44, 102.84, 53.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 196, "height": 126}}, "lian": {"lian": {"type": "mesh", "uvs": [0.31053, 0.03599, 0.37804, 0.01301, 0.46592, 0, 0.56559, 0.0075, 0.62453, 0.05529, 0.65561, 0.09481, 0.71134, 0.13341, 0.71884, 0.18029, 0.77136, 0.2244, 0.7585, 0.26209, 0.85495, 0.30528, 0.94283, 0.37422, 1, 0.44591, 1, 0.54609, 0.99427, 0.60675, 0.96533, 0.65818, 0.93854, 0.69403, 0.89567, 0.7023, 0.90853, 0.78502, 0.87853, 0.86957, 0.8332, 0.92821, 0.81315, 0.95413, 0.75421, 1, 0.61274, 1, 0.482, 0.96516, 0.42734, 0.91829, 0.35768, 0.90174, 0.28159, 0.85211, 0.21943, 0.79697, 0.15406, 0.75561, 0.12619, 0.683, 0.12298, 0.59936, 0.0844, 0.5819, 0.07904, 0.52091, 0.04046, 0.47312, 0.05868, 0.43268, 0.08118, 0.4097, 0.03188, 0.38672, 0, 0.32422, 0.00509, 0.23691, 0.02545, 0.16154, 0.08761, 0.0871, 0.15942, 0.04758, 0.2548, 0.02644, 0.15861, 0.56038, 0.16291, 0.51684, 0.20679, 0.48438, 0.21023, 0.44748, 0.25927, 0.43051, 0.27562, 0.40248, 0.30315, 0.40395, 0.31864, 0.38034, 0.34789, 0.37222, 0.4021, 0.37222, 0.42447, 0.35673, 0.48469, 0.36263, 0.53718, 0.36263, 0.57417, 0.34197, 0.58536, 0.32943, 0.64128, 0.33016, 0.68602, 0.31172, 0.69376, 0.27925, 0.10871, 0.38108, 0.10613, 0.36189, 0.09924, 0.33754, 0.11903, 0.30729, 0.09924, 0.28442, 0.09322, 0.26081, 0.13968, 0.25712, 0.1741, 0.22244, 0.15331, 0.1994, 0.14755, 0.14896, 0.1747, 0.15143, 0.19568, 0.17577, 0.21789, 0.15778, 0.24956, 0.18917, 0.28206, 0.16659, 0.30715, 0.16483, 0.29727, 0.21034, 0.31414, 0.21457, 0.33841, 0.18741, 0.39723, 0.19199, 0.40998, 0.1987, 0.44412, 0.17788, 0.49265, 0.17012, 0.51281, 0.17471, 0.52885, 0.13626, 0.56422, 0.12426, 0.58355, 0.12603, 0.61152, 0.10698, 0.64731, 0.09604, 0.29635, 0.04506, 0.34865, 0.05884, 0.40308, 0.09063, 0.43847, 0.12054, 0.45744, 0.17575, 0.10122, 0.50142, 0.25302, 0.34176, 0.26544, 0.33111, 0.44206, 0.28854, 0.59577, 0.23695, 0.66069, 0.20666, 0.16805, 0.31392, 0.25971, 0.25579, 0.36473, 0.24924, 0.26031, 0.7872, 0.2432, 0.70262, 0.27254, 0.62922, 0.32471, 0.5733, 0.40133, 0.50549, 0.51299, 0.47124, 0.59939, 0.47613, 0.69394, 0.50969, 0.78686, 0.55023, 0.82273, 0.57749, 0.83332, 0.51668, 0.87326, 0.48312, 0.94255, 0.50829, 0.97271, 0.56281, 0.95966, 0.63132, 0.93929, 0.67256, 0.32411, 0.84732, 0.38606, 0.88996, 0.85567, 0.631, 0.88275, 0.69801, 0.37133, 0.6729, 0.42598, 0.73694, 0.49219, 0.78447, 0.53607, 0.85907, 0.56686, 0.8715, 0.6269, 0.89329, 0.45474, 0.62562, 0.57375, 0.72987, 0.57475, 0.70318, 0.58579, 0.67434, 0.60687, 0.65067, 0.64552, 0.6356, 0.67111, 0.63517, 0.66961, 0.65971, 0.65054, 0.68424, 0.62243, 0.70964, 0.60085, 0.72427, 0.59573, 0.69845, 0.62518, 0.67072, 0.65578, 0.64745, 0.41891, 0.88217, 0.4148, 0.86101, 0.42837, 0.83914, 0.45798, 0.82573, 0.47567, 0.8282, 0.4876, 0.83984, 0.48554, 0.85924, 0.46662, 0.88147, 0.44893, 0.89099, 0.43084, 0.89029, 0.43454, 0.86524, 0.45346, 0.84549, 0.48095, 0.83336, 0.48396, 0.8156, 0.45023, 0.8029, 0.40252, 0.80643, 0.37372, 0.83747, 0.37455, 0.85969, 0.38894, 0.87804, 0.39717, 0.88368, 0.39799, 0.90343, 0.41362, 0.90802, 0.42308, 0.90061, 0.44488, 0.90202, 0.46092, 0.90379, 0.47326, 0.88756, 0.49177, 0.86393, 0.49506, 0.84064, 0.32766, 0.83359, 0.34699, 0.80114, 0.39429, 0.77715, 0.43213, 0.77151, 0.46257, 0.77362, 0.307, 0.81267, 0.31485, 0.78195, 0.34281, 0.75335, 0.38304, 0.73988, 0.41051, 0.74493, 0.44926, 0.67215, 0.46153, 0.6305, 0.49636, 0.59306, 0.54885, 0.56319, 0.61998, 0.53878, 0.65236, 0.53794, 0.52039, 0.69865, 0.53756, 0.65406, 0.58171, 0.61914, 0.63273, 0.60147, 0.68424, 0.5981, 0.72447, 0.59011, 0.70288, 0.63429, 0.68621, 0.64733, 0.6813, 0.67341, 0.65922, 0.68982, 0.61998, 0.72474, 0.57632, 0.73904, 0.55964, 0.74073, 0.53609, 0.72095, 0.47968, 0.69739, 0.49439, 0.66289, 0.52187, 0.62251, 0.55768, 0.59179, 0.61115, 0.56066, 0.65088, 0.55351, 0.57211, 0.7642, 0.62108, 0.82075, 0.64197, 0.91593, 0.7079, 0.968, 0.74316, 0.98703, 0.5, 0.49118, 0.52662, 0.55968, 0.65088, 0.72732, 0.70265, 0.7806, 0.78179, 0.83832, 0.83948, 0.8878, 0.7352, 0.56508, 0.81279, 0.64079, 0.85245, 0.73319, 0.87066, 0.80203, 0.56139, 0.49214, 0.59097, 0.52766, 0.70487, 0.66911, 0.76774, 0.73064, 0.85382, 0.84254, 0.69105, 0.87507, 0.76366, 0.94172, 0.3004, 0.70931, 0.32597, 0.74527, 0.4947, 0.90577, 0.54481, 0.94523, 0.61844, 0.97417, 0.68287, 0.97593, 0.73353, 0.31741, 0.77023, 0.41561, 0.71004, 0.45715, 0.8539, 0.38791], "triangles": [90, 4, 5, 4, 87, 3, 89, 4, 90, 3, 86, 2, 89, 87, 4, 86, 94, 2, 88, 87, 89, 3, 87, 86, 71, 41, 42, 74, 72, 42, 71, 42, 72, 43, 74, 42, 74, 43, 91, 40, 41, 71, 77, 91, 92, 77, 92, 93, 76, 74, 91, 77, 76, 91, 84, 94, 86, 85, 84, 86, 95, 94, 84, 73, 72, 74, 83, 94, 95, 80, 77, 93, 81, 80, 93, 94, 81, 93, 75, 74, 76, 73, 74, 75, 83, 81, 94, 82, 81, 83, 70, 71, 72, 70, 72, 73, 40, 71, 70, 6, 90, 5, 101, 6, 7, 6, 89, 90, 101, 89, 6, 88, 89, 101, 78, 76, 77, 78, 77, 80, 75, 76, 78, 79, 78, 80, 69, 70, 73, 67, 39, 40, 100, 88, 101, 104, 80, 81, 104, 81, 82, 79, 80, 104, 103, 75, 78, 40, 70, 67, 68, 70, 69, 69, 73, 75, 68, 67, 70, 9, 7, 8, 101, 7, 9, 61, 101, 9, 100, 101, 61, 66, 67, 68, 95, 99, 82, 104, 82, 99, 95, 84, 85, 95, 82, 83, 99, 95, 85, 85, 86, 87, 85, 87, 88, 85, 88, 100, 99, 85, 100, 65, 66, 68, 61, 59, 100, 102, 68, 69, 65, 68, 102, 69, 75, 103, 102, 69, 103, 237, 61, 9, 237, 9, 10, 60, 61, 237, 38, 39, 67, 38, 67, 66, 58, 99, 100, 59, 58, 100, 61, 60, 59, 103, 104, 98, 79, 103, 78, 104, 103, 79, 102, 103, 98, 98, 104, 99, 64, 38, 66, 64, 66, 65, 97, 102, 98, 57, 99, 58, 98, 99, 52, 55, 54, 99, 102, 63, 64, 99, 56, 55, 57, 56, 99, 99, 54, 52, 54, 53, 52, 51, 98, 52, 97, 98, 51, 102, 62, 63, 65, 102, 64, 37, 38, 64, 37, 64, 63, 37, 63, 62, 240, 237, 10, 240, 10, 11, 49, 97, 51, 50, 49, 51, 36, 37, 62, 238, 237, 240, 49, 47, 97, 97, 62, 102, 48, 47, 49, 97, 96, 62, 60, 238, 59, 238, 60, 237, 239, 59, 238, 110, 55, 56, 59, 57, 58, 111, 59, 239, 111, 57, 59, 56, 57, 111, 110, 56, 111, 116, 240, 11, 116, 11, 12, 238, 240, 116, 62, 96, 36, 96, 97, 47, 110, 53, 55, 224, 110, 111, 46, 96, 47, 35, 36, 96, 34, 35, 96, 109, 52, 53, 53, 54, 55, 110, 109, 53, 214, 109, 110, 117, 116, 12, 112, 111, 239, 115, 238, 116, 239, 238, 115, 113, 112, 239, 45, 96, 46, 33, 34, 96, 225, 224, 111, 111, 187, 225, 112, 188, 111, 188, 187, 111, 117, 12, 13, 115, 113, 239, 208, 187, 188, 224, 214, 110, 186, 215, 224, 215, 214, 224, 44, 96, 45, 33, 96, 44, 48, 49, 50, 187, 186, 225, 207, 187, 208, 225, 186, 224, 118, 117, 13, 187, 207, 186, 220, 112, 113, 188, 112, 220, 208, 188, 220, 50, 52, 109, 52, 50, 51, 109, 48, 50, 108, 48, 109, 46, 47, 48, 108, 46, 48, 114, 113, 115, 32, 33, 44, 220, 193, 208, 206, 186, 207, 215, 185, 109, 215, 109, 214, 131, 108, 109, 185, 215, 186, 194, 193, 220, 31, 32, 44, 192, 207, 208, 192, 208, 193, 191, 206, 207, 14, 118, 13, 192, 191, 207, 206, 205, 185, 206, 185, 186, 205, 206, 191, 185, 131, 109, 184, 131, 185, 46, 44, 45, 46, 108, 44, 107, 44, 108, 205, 184, 185, 118, 114, 117, 117, 115, 116, 117, 114, 115, 119, 123, 118, 123, 114, 118, 14, 119, 118, 195, 193, 194, 137, 192, 193, 137, 193, 195, 136, 192, 137, 135, 191, 192, 220, 221, 194, 221, 114, 123, 221, 220, 113, 114, 221, 113, 195, 194, 221, 196, 137, 195, 144, 136, 137, 136, 135, 192, 190, 205, 191, 134, 190, 191, 15, 119, 14, 138, 144, 137, 138, 137, 196, 204, 184, 205, 204, 205, 190, 226, 195, 221, 196, 195, 226, 197, 138, 196, 143, 135, 136, 143, 136, 144, 131, 125, 108, 183, 131, 184, 183, 184, 204, 125, 107, 108, 120, 123, 119, 120, 119, 15, 131, 183, 125, 226, 197, 196, 135, 134, 191, 134, 135, 143, 107, 30, 31, 107, 31, 44, 139, 143, 144, 139, 144, 138, 139, 138, 197, 198, 139, 197, 120, 124, 123, 16, 120, 15, 203, 183, 204, 120, 17, 124, 142, 134, 143, 189, 204, 190, 133, 189, 190, 134, 133, 190, 203, 204, 189, 16, 17, 120, 106, 30, 107, 231, 106, 107, 142, 133, 134, 125, 231, 107, 140, 142, 143, 140, 143, 139, 202, 189, 133, 141, 142, 140, 132, 133, 142, 199, 141, 140, 140, 139, 198, 199, 140, 198, 216, 199, 198, 132, 202, 133, 141, 132, 142, 227, 226, 221, 124, 222, 221, 124, 221, 123, 227, 221, 222, 126, 125, 183, 126, 183, 203, 200, 132, 141, 200, 141, 199, 181, 125, 126, 232, 231, 125, 201, 202, 132, 201, 132, 200, 182, 181, 126, 181, 232, 125, 180, 232, 181, 29, 30, 106, 209, 201, 200, 177, 176, 126, 182, 126, 176, 126, 127, 177, 175, 181, 182, 175, 182, 176, 180, 181, 175, 198, 226, 227, 226, 198, 197, 227, 216, 198, 217, 216, 227, 232, 105, 231, 179, 232, 180, 127, 126, 203, 202, 203, 189, 127, 202, 201, 127, 201, 209, 202, 127, 203, 105, 106, 231, 179, 105, 232, 28, 29, 106, 105, 28, 106, 174, 180, 175, 179, 180, 174, 222, 18, 223, 17, 222, 124, 18, 222, 17, 159, 176, 177, 159, 177, 127, 160, 175, 176, 160, 176, 159, 174, 175, 160, 178, 105, 179, 178, 179, 174, 158, 159, 127, 210, 199, 216, 210, 216, 217, 209, 200, 199, 210, 209, 199, 148, 159, 158, 149, 148, 158, 157, 149, 158, 173, 178, 174, 161, 174, 160, 173, 174, 161, 227, 228, 218, 223, 227, 222, 217, 227, 218, 147, 160, 159, 147, 159, 148, 161, 160, 147, 158, 172, 157, 150, 157, 172, 127, 128, 158, 209, 128, 127, 128, 172, 158, 223, 228, 227, 156, 147, 148, 156, 148, 149, 156, 149, 157, 121, 178, 173, 105, 178, 121, 27, 105, 121, 28, 105, 27, 210, 128, 209, 150, 156, 157, 151, 150, 172, 171, 151, 172, 151, 156, 150, 146, 162, 161, 147, 146, 161, 128, 171, 172, 155, 147, 156, 146, 147, 155, 19, 223, 18, 228, 223, 19, 129, 128, 210, 229, 210, 217, 229, 217, 218, 163, 162, 146, 152, 156, 151, 152, 151, 171, 155, 156, 152, 145, 146, 155, 164, 163, 146, 145, 164, 146, 170, 152, 171, 219, 218, 228, 219, 228, 19, 122, 162, 163, 122, 163, 164, 121, 173, 161, 162, 121, 161, 121, 162, 122, 154, 145, 155, 153, 154, 155, 152, 153, 155, 130, 210, 229, 129, 210, 130, 167, 145, 154, 26, 121, 122, 27, 121, 26, 168, 154, 153, 167, 154, 168, 165, 164, 145, 165, 145, 167, 122, 164, 165, 170, 169, 153, 170, 153, 152, 168, 153, 169, 233, 171, 128, 170, 171, 233, 169, 170, 233, 166, 165, 167, 211, 130, 229, 25, 167, 168, 166, 167, 25, 165, 166, 25, 26, 122, 165, 26, 165, 25, 219, 230, 218, 20, 219, 19, 230, 229, 218, 20, 230, 219, 212, 211, 229, 129, 233, 128, 234, 129, 130, 234, 130, 211, 234, 233, 129, 21, 230, 20, 24, 169, 233, 24, 233, 234, 25, 168, 169, 24, 25, 169, 230, 212, 229, 235, 234, 211, 236, 235, 211, 212, 236, 211, 213, 212, 230, 23, 234, 235, 24, 234, 23, 21, 22, 213, 21, 213, 230, 236, 212, 213, 22, 236, 213, 23, 235, 236, 22, 23, 236, 91, 43, 0, 92, 0, 1, 91, 0, 92, 93, 1, 2, 92, 1, 93, 94, 93, 2], "vertices": [2, 4, 373.51, -15.35, 0.72, 46, 71.93, 22.48, 0.28, 2, 4, 370.27, -34.32, 0.72, 46, 91.03, 20.12, 0.28, 2, 4, 361.59, -56.3, 0.72, 46, 113.39, 12.48, 0.28, 2, 4, 345.87, -77.65, 0.72, 46, 135.46, -2.22, 0.28, 1, 4, 325.13, -83.31, 1, 1, 4, 310.42, -83.99, 1, 1, 4, 292.55, -90.4, 1, 1, 4, 279.14, -84.56, 1, 1, 4, 260.26, -89.36, 1, 1, 4, 252.09, -80.39, 1, 1, 4, 227.39, -95.28, 1, 1, 4, 197.09, -104.09, 1, 1, 4, 170.29, -105.5, 1, 1, 4, 143.85, -89.4, 1, 1, 4, 128.63, -78.35, 1, 1, 4, 119.05, -63.53, 1, 1, 4, 113.28, -51.71, 1, 2, 4, 117.01, -40.68, 0.7, 34, -75.21, -106.07, 0.3, 3, 4, 93.4, -30.29, 0.63, 35, -35.45, -93.68, 0.07, 34, -98.81, -95.68, 0.3, 3, 4, 75.23, -9.91, 0.595, 35, -53.63, -73.3, 0.105, 34, -116.99, -75.3, 0.3, 3, 4, 66, 9.77, 0.574, 35, -62.86, -53.62, 0.126, 34, -126.22, -55.62, 0.3, 3, 4, 61.92, 18.48, 0.525, 35, -66.94, -44.91, 0.175, 34, -130.3, -46.91, 0.3, 3, 4, 57.94, 39.19, 0.49, 35, -70.91, -24.2, 0.21, 34, -134.27, -26.2, 0.3, 2, 4, 77.45, 71.21, 0.7, 34, -114.77, 5.82, 0.3, 2, 4, 104.66, 95.2, 0.7, 34, -87.56, 29.81, 0.3, 3, 4, 124.57, 100.03, 0.644, 35, -4.29, 36.64, 0.056, 34, -67.65, 34.64, 0.3, 1, 4, 138.54, 113.14, 1, 1, 4, 162.12, 122.38, 1, 1, 4, 185.25, 127.59, 1, 1, 4, 205.17, 135.74, 1, 1, 4, 228.17, 130.37, 1, 1, 4, 250.69, 117.66, 1, 1, 4, 260.62, 123.58, 1, 1, 4, 277.45, 114.99, 1, 1, 4, 295.38, 116.04, 1, 1, 4, 303.54, 105.42, 1, 1, 4, 306.5, 96.63, 1, 2, 4, 319.36, 104.09, 0.72, 46, -44.85, -37.21, 0.28, 2, 4, 340.25, 101.26, 0.72, 46, -43, -16.21, 0.28, 2, 4, 362.59, 86.08, 0.72, 46, -28.87, 6.82, 0.28, 2, 4, 379.67, 69.35, 0.72, 46, -12.97, 24.67, 0.28, 2, 4, 390.75, 43.32, 0.72, 46, 12.52, 36.95, 0.28, 2, 4, 391.28, 20.71, 0.72, 46, 35.07, 38.54, 0.28, 2, 4, 383.71, -4.27, 0.72, 46, 60.38, 32.15, 0.28, 1, 4, 256.07, 103.32, 1, 1, 4, 266.96, 95.35, 1, 1, 4, 269.48, 80.2, 1, 1, 4, 278.74, 73.49, 1, 1, 4, 276.46, 59.67, 1, 1, 4, 281.61, 51.46, 1, 1, 4, 277.42, 45.47, 1, 1, 4, 281.52, 38.17, 1, 1, 4, 279.63, 30.24, 1, 1, 4, 272.15, 17.97, 1, 1, 4, 273.16, 10.42, 1, 2, 4, 263.3, -2.26, 0.984, 46, 64.02, -88.23, 0.016, 1, 4, 256.07, -14.14, 1, 1, 4, 256.42, -25.83, 1, 2, 4, 258.19, -30.38, 0.984, 46, 92.34, -92.02, 0.016, 1, 4, 250.28, -42.92, 1, 1, 4, 248.98, -56.01, 1, 1, 4, 256.48, -62.98, 1, 2, 4, 310.26, 85.8, 0.672, 46, -26.15, -45.44, 0.328, 2, 4, 315.68, 83.3, 0.672, 46, -23.9, -39.91, 0.328, 1, 4, 323.06, 80.94, 1, 2, 4, 328.31, 71.6, 0.672, 46, -12.81, -26.75, 0.328, 1, 4, 337.08, 72.4, 1, 1, 4, 344.14, 69.97, 1, 2, 4, 338.71, 58.86, 0.672, 46, -0.57, -15.77, 0.328, 2, 4, 343.11, 45.5, 0.672, 46, 12.57, -10.74, 0.328, 2, 4, 352.06, 46.5, 0.672, 46, 11.15, -1.85, 0.328, 2, 4, 366.16, 39.7, 0.672, 46, 17.29, 12.56, 0.328, 2, 4, 361.77, 33.95, 0.672, 46, 23.23, 8.44, 0.328, 2, 4, 352.45, 33.11, 0.672, 46, 24.5, -0.83, 0.328, 2, 4, 354.14, 25.19, 0.672, 46, 32.34, 1.23, 0.328, 2, 4, 341.49, 23.07, 0.672, 46, 35.05, -11.31, 0.328, 2, 4, 342.97, 12.09, 0.672, 46, 45.95, -9.32, 0.328, 2, 4, 339.97, 6.13, 0.672, 46, 52.04, -12.03, 0.328, 2, 4, 329.33, 15.68, 0.672, 46, 43.01, -23.11, 0.328, 2, 4, 325.89, 12.54, 0.672, 46, 46.3, -26.4, 0.328, 2, 4, 329.71, 2.68, 0.672, 46, 55.97, -22.12, 0.328, 2, 4, 320.39, -9.89, 0.672, 46, 68.97, -30.84, 0.328, 2, 4, 316.86, -11.7, 0.672, 46, 70.94, -34.28, 0.328, 2, 4, 317.65, -22.77, 0.672, 46, 81.96, -32.98, 0.328, 2, 4, 313.01, -35.01, 0.672, 46, 94.4, -37.04, 0.328, 2, 4, 309.02, -38.83, 0.672, 46, 98.4, -40.85, 0.328, 2, 4, 316.95, -48.64, 0.672, 46, 107.83, -32.46, 0.328, 1, 4, 315.24, -58.58, 1, 2, 4, 312.11, -62.67, 0.672, 46, 122.07, -36.64, 0.328, 1, 4, 313.28, -72.06, 1, 1, 4, 311.24, -81.92, 1, 2, 4, 373.07, -10.68, 0.72, 46, 67.28, 21.82, 0.28, 2, 4, 362.22, -20.3, 0.72, 46, 77.4, 11.43, 0.28, 2, 4, 346.33, -27.51, 0.72, 46, 85.35, -4.1, 0.28, 2, 4, 333.56, -30.72, 0.72, 46, 89.15, -16.71, 0.28, 2, 4, 316.37, -26.13, 0.672, 46, 85.38, -34.09, 0.328, 1, 4, 279.54, 106.84, 1, 2, 4, 300.74, 46.82, 0.928, 46, 13.24, -53.12, 0.072, 2, 4, 301.84, 42.3, 0.976, 46, 17.7, -51.81, 0.024, 2, 4, 288.73, -4.52, 0.912, 46, 65.08, -62.72, 0.088, 1, 4, 281.15, -47.6, 1, 1, 4, 280.2, -67.16, 1, 1, 4, 319.8, 61.57, 1, 1, 4, 322.51, 31.48, 1, 2, 4, 309.76, 6.66, 0.984, 46, 52.92, -42.23, 0.016, 3, 4, 182.19, 116.77, 0.644, 35, 53.33, 53.37, 0.056, 34, -10.03, 51.37, 0.3, 3, 4, 206.87, 107.04, 0.63, 35, 78.01, 43.65, 0.07, 34, 14.65, 41.65, 0.3, 3, 4, 222.19, 88.61, 0.56, 35, 93.33, 25.21, 0.14, 34, 29.97, 23.21, 0.3, 3, 4, 229.76, 67.81, 0.49, 35, 100.9, 4.42, 0.21, 34, 37.54, 2.42, 0.3, 3, 4, 237.09, 39.57, 0.525, 35, 108.23, -23.82, 0.175, 34, 44.87, -25.82, 0.3, 3, 4, 230.74, 8.79, 0.595, 35, 101.88, -54.6, 0.105, 34, 38.52, -56.6, 0.3, 3, 4, 217.54, -9.98, 0.595, 35, 88.68, -73.37, 0.105, 34, 25.32, -75.37, 0.3, 3, 4, 195.65, -25.98, 0.63, 35, 66.79, -89.37, 0.07, 34, 3.43, -91.37, 0.3, 2, 4, 172.14, -40.49, 0.7, 34, -20.08, -105.89, 0.3, 1, 4, 160, -44.23, 1, 1, 4, 174.59, -56.4, 1, 1, 4, 177.94, -70.84, 1, 1, 4, 161.74, -82.47, 1, 1, 4, 143.2, -80.53, 1, 1, 4, 126.92, -66.57, 1, 1, 4, 118.84, -55.33, 1, 3, 4, 157.53, 111.99, 0.644, 35, 28.67, 48.6, 0.056, 34, -34.69, 46.6, 0.3, 3, 4, 137.73, 104.82, 0.644, 35, 8.87, 41.43, 0.056, 34, -54.49, 39.43, 0.3, 2, 4, 141.34, -43.08, 0.7, 34, -50.88, -108.48, 0.3, 2, 4, 119.92, -38.44, 0.7, 34, -72.3, -103.83, 0.3, 2, 4, 197.05, 73.27, 0.7, 35, 68.19, 9.88, 0.3, 2, 4, 172.61, 71.19, 0.7, 35, 43.75, 7.8, 0.3, 2, 4, 150.94, 63.85, 0.7, 35, 22.08, 0.46, 0.3, 2, 4, 125.21, 65.91, 0.7, 35, -3.65, 2.52, 0.3, 2, 4, 117.68, 60.94, 0.7, 35, -11.18, -2.45, 0.3, 2, 4, 103.65, 50.85, 0.7, 35, -25.21, -12.54, 0.3, 2, 4, 198.03, 46.79, 0.75, 35, 69.17, -16.6, 0.25, 2, 4, 154.11, 36.61, 0.77, 35, 25.25, -26.78, 0.23, 2, 4, 161.01, 32.1, 0.8, 35, 32.15, -31.3, 0.2, 2, 4, 167.1, 24.96, 0.8, 35, 38.24, -38.43, 0.2, 2, 4, 170.44, 16.39, 0.85, 35, 41.58, -47.01, 0.15, 2, 4, 169.09, 5.22, 0.85, 35, 40.23, -58.17, 0.15, 2, 4, 165.68, -0.64, 0.85, 35, 36.82, -64.04, 0.15, 2, 4, 159.41, 3.64, 0.85, 35, 30.55, -59.75, 0.15, 2, 4, 155.56, 11.9, 0.85, 35, 26.71, -51.49, 0.15, 2, 4, 152.74, 22.34, 0.8, 35, 23.88, -41.05, 0.2, 2, 4, 151.85, 29.58, 0.8, 35, 22.99, -33.81, 0.2, 2, 4, 159.37, 26.59, 0.8, 35, 30.51, -36.8, 0.2, 2, 4, 162.63, 15.47, 0.85, 35, 33.77, -47.93, 0.15, 2, 4, 164.55, 4.8, 0.85, 35, 35.69, -58.59, 0.15, 2, 4, 135.26, 96.14, 0.9, 35, 6.4, 32.75, 0.1, 2, 4, 141.41, 93.67, 0.9, 35, 12.55, 30.28, 0.1, 2, 4, 145.31, 87.08, 0.8, 35, 16.45, 23.69, 0.2, 2, 4, 144.77, 78.22, 0.75, 35, 15.91, 14.83, 0.25, 2, 4, 141.68, 74.62, 0.75, 35, 12.82, 11.23, 0.25, 2, 4, 136.96, 73.79, 0.75, 35, 8.1, 10.4, 0.25, 2, 4, 132.13, 77.37, 0.75, 35, 3.27, 13.98, 0.25, 2, 4, 128.87, 85.23, 0.8, 35, 0.01, 21.83, 0.2, 2, 4, 128.79, 90.76, 0.9, 35, -0.07, 27.37, 0.1, 2, 4, 131.48, 94.74, 0.9, 35, 2.62, 31.35, 0.1, 2, 4, 137.57, 89.88, 0.8, 35, 8.71, 26.49, 0.2, 2, 4, 140.18, 82.42, 0.8, 35, 11.32, 19.03, 0.2, 2, 4, 139.59, 74.25, 0.75, 35, 10.73, 10.86, 0.25, 2, 4, 143.86, 70.72, 0.75, 35, 15, 7.32, 0.25, 2, 4, 151.86, 76.31, 0.75, 35, 23, 12.92, 0.25, 2, 4, 157.51, 87.67, 0.8, 35, 28.65, 24.28, 0.2, 2, 4, 153.29, 99.18, 0.9, 35, 24.43, 35.79, 0.1, 2, 4, 147.31, 102.57, 0.92, 35, 18.45, 39.17, 0.08, 2, 4, 140.48, 102.26, 0.92, 35, 11.62, 38.86, 0.08, 2, 4, 137.86, 101.3, 0.92, 35, 9, 37.91, 0.08, 3, 4, 132.53, 104.29, 0.644, 35, 3.67, 40.9, 0.056, 34, -59.69, 38.9, 0.3, 3, 4, 129.17, 101.49, 0.644, 35, 0.31, 38.1, 0.056, 34, -63.05, 36.1, 0.3, 2, 4, 129.82, 98.16, 0.92, 35, 0.96, 34.77, 0.08, 2, 4, 126.44, 93.45, 0.9, 35, -2.42, 30.06, 0.1, 2, 4, 123.76, 90.1, 0.9, 35, -5.09, 26.71, 0.1, 2, 4, 126.35, 84.7, 0.8, 35, -2.51, 21.31, 0.2, 2, 4, 130.03, 76.71, 0.75, 35, 1.17, 13.32, 0.25, 2, 4, 135.72, 72.23, 0.75, 35, 6.86, 8.84, 0.25, 2, 4, 160.66, 108.98, 0.92, 35, 31.8, 45.59, 0.08, 2, 4, 166.56, 99.39, 0.9, 35, 37.7, 36, 0.1, 2, 4, 166.37, 84.83, 0.75, 35, 37.51, 21.44, 0.25, 2, 4, 162.64, 75.36, 0.75, 35, 33.78, 11.97, 0.25, 2, 4, 157.89, 68.81, 0.7, 35, 29.03, 5.42, 0.3, 2, 4, 169.03, 110.29, 0.92, 35, 40.17, 46.9, 0.08, 2, 4, 176.05, 103.58, 0.9, 35, 47.19, 40.19, 0.1, 2, 4, 179.75, 92.65, 0.8, 35, 50.89, 29.26, 0.2, 2, 4, 177.76, 81.39, 0.75, 35, 48.9, 17.99, 0.25, 2, 4, 172.64, 75.98, 0.8, 35, 43.78, 12.59, 0.2, 2, 4, 186.5, 55.51, 0.75, 35, 57.64, -7.88, 0.25, 2, 4, 195.8, 46.04, 0.75, 35, 66.94, -17.35, 0.25, 2, 4, 200.88, 32.14, 0.8, 35, 72.02, -31.25, 0.2, 2, 4, 201.53, 15.46, 0.85, 35, 72.67, -47.94, 0.15, 2, 4, 198.16, -4.56, 0.85, 35, 69.3, -67.96, 0.15, 2, 4, 193.92, -12.03, 0.85, 35, 65.06, -75.42, 0.15, 2, 4, 169.7, 43.67, 0.75, 35, 40.84, -19.72, 0.25, 2, 4, 179.1, 32.62, 0.8, 35, 50.24, -30.77, 0.2, 2, 4, 182.23, 17.01, 0.85, 35, 53.37, -46.38, 0.15, 2, 4, 179.86, 2.62, 0.85, 35, 51, -60.77, 0.15, 2, 4, 173.65, -9.57, 0.85, 35, 44.79, -72.97, 0.15, 2, 4, 170.21, -19.96, 0.9, 35, 41.35, -83.35, 0.1, 2, 4, 161.53, -7.98, 0.85, 35, 32.67, -71.37, 0.15, 2, 4, 160.39, -2.11, 0.85, 35, 31.53, -65.5, 0.15, 2, 4, 154.18, 3.2, 0.85, 35, 25.32, -60.19, 0.15, 2, 4, 152.9, 10.83, 0.85, 35, 24.04, -52.56, 0.15, 2, 4, 149.09, 25.33, 0.8, 35, 20.23, -38.07, 0.2, 2, 4, 151.33, 37.51, 0.77, 35, 22.47, -25.89, 0.23, 2, 4, 153.19, 41.55, 0.75, 35, 24.33, -21.84, 0.25, 2, 4, 161.65, 43.7, 0.75, 35, 32.79, -19.69, 0.25, 2, 4, 175.65, 52.68, 0.75, 35, 46.79, -10.71, 0.25, 2, 4, 182.72, 43.81, 0.75, 35, 53.86, -19.58, 0.25, 2, 4, 189.59, 31.1, 0.8, 35, 60.73, -32.29, 0.2, 2, 4, 192.76, 18.06, 0.85, 35, 63.9, -45.34, 0.15, 2, 4, 193.61, 0.95, 0.85, 35, 64.75, -62.44, 0.15, 2, 4, 190.02, -9.19, 0.85, 35, 61.16, -72.58, 0.15, 1, 4, 145.27, 42.5, 1, 2, 4, 123.6, 40.51, 0.75, 35, -5.26, -22.88, 0.25, 2, 4, 95.6, 51.08, 0.7, 35, -33.26, -12.31, 0.3, 2, 4, 72.77, 44.53, 0.7, 35, -56.09, -18.86, 0.3, 3, 4, 62.89, 39.61, 0.5432, 35, -65.97, -23.78, 0.2328, 34, -129.33, -25.78, 0.224, 2, 4, 227.27, 14.94, 0.8, 35, 98.41, -48.45, 0.2, 2, 4, 205.52, 19.92, 0.8, 35, 76.66, -43.47, 0.2, 2, 4, 144.15, 18.75, 0.8, 35, 15.29, -44.64, 0.2, 2, 4, 122.95, 15.59, 0.8, 35, -5.91, -47.8, 0.2, 2, 4, 96.81, 6.96, 0.8, 35, -32.05, -56.43, 0.2, 2, 4, 75.8, 1.86, 0.8, 35, -53.06, -61.53, 0.2, 2, 4, 175.34, -26.41, 0.9, 35, 46.48, -89.81, 0.1, 2, 4, 144.67, -31.81, 0.9, 35, 15.81, -95.2, 0.1, 2, 4, 114.81, -25.93, 0.9, 35, -14.05, -89.32, 0.1, 2, 4, 94.14, -18.98, 0.9, 35, -34.72, -82.38, 0.1, 2, 4, 218.55, 1.2, 0.85, 35, 89.69, -62.19, 0.15, 2, 4, 205.1, 0.21, 0.85, 35, 76.24, -63.18, 0.15, 2, 4, 152.07, -2.83, 0.85, 35, 23.21, -66.22, 0.15, 2, 4, 127.16, -7.17, 0.85, 35, -1.69, -70.56, 0.15, 2, 4, 85.77, -8.66, 0.85, 35, -43.09, -72.05, 0.15, 2, 4, 99.62, 33.4, 0.776, 35, -29.24, -29.99, 0.224, 2, 4, 72.02, 27.69, 0.75, 35, -56.84, -35.71, 0.25, 2, 4, 197.21, 95.17, 0.8, 35, 68.36, 31.78, 0.2, 2, 4, 184.2, 95.17, 0.8, 35, 55.34, 31.78, 0.2, 2, 4, 118.59, 82.78, 0.8, 35, -10.27, 19.38, 0.2, 2, 4, 101.26, 77.78, 0.8, 35, -27.6, 14.39, 0.2, 2, 4, 83.48, 65.77, 0.8, 35, -45.38, 2.38, 0.2, 2, 4, 74.13, 51.47, 0.75, 35, -54.73, -11.92, 0.25, 1, 4, 240.93, -65.85, 1, 1, 4, 209.96, -58.37, 1, 1, 4, 207.29, -38.07, 1, 1, 4, 205.73, -81.76, 1], "hull": 44, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 0, 86, 62, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 18, 72, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 182, 184, 184, 186, 186, 188, 166, 190, 190, 168, 188, 190, 192, 194, 196, 198, 198, 200, 200, 202, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 210, 242, 242, 244, 228, 246, 246, 248, 216, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 218, 262, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 264, 264, 284, 284, 286, 286, 288, 288, 274, 290, 292, 292, 294, 294, 296, 296, 298, 300, 302, 302, 304, 304, 306, 306, 308, 308, 290, 290, 310, 310, 312, 298, 314, 314, 300, 312, 314, 316, 318, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 342, 344, 344, 316, 346, 348, 348, 350, 350, 352, 352, 354, 356, 358, 358, 360, 360, 362, 362, 364, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 378, 380, 380, 382, 382, 384, 384, 386, 386, 388, 388, 390, 390, 392, 392, 394, 394, 396, 396, 398, 398, 400, 400, 402, 402, 404, 404, 378, 406, 408, 408, 410, 410, 412, 412, 414, 414, 416, 418, 420, 38, 40, 40, 42, 260, 422, 422, 424, 424, 426, 428, 430, 432, 434, 434, 436, 436, 438, 440, 442, 442, 444, 444, 446, 448, 450, 452, 454, 454, 456, 420, 458, 458, 460, 214, 462, 462, 464, 466, 468, 468, 470, 470, 472, 474, 476, 476, 478], "width": 265, "height": 309}}, "xiayi": {"xiayi": {"type": "mesh", "uvs": [0.50132, 0.00581, 0.39556, 0.0589, 0.3167, 0.12261, 0.27027, 0.17454, 0.22887, 0.22084, 0.1733, 0.33233, 0.15717, 0.48763, 0.17689, 0.6217, 0.20377, 0.67612, 0.10519, 0.77832, 0.01556, 0.86725, 0, 0.94291, 0.0066, 1, 0.15538, 1, 0.33821, 1, 0.44038, 1, 0.62141, 1, 0.78453, 0.98141, 0.84189, 0.92433, 0.88132, 0.81416, 0.96019, 0.74779, 0.9799, 0.62568, 1, 0.55002, 1, 0.42259, 0.98887, 0.30579, 0.94406, 0.19429, 0.90104, 0.09607, 0.83292, 0.03103, 0.75943, 0, 0.64292, 0, 0.3633, 0.20225, 0.52283, 0.23677, 0.66085, 0.2819, 0.79528, 0.32835, 0.87953, 0.38543, 0.91538, 0.47967, 0.92255, 0.6124, 0.90821, 0.71859, 0.25038, 0.63895, 0.38839, 0.56329, 0.53358, 0.47436, 0.68056, 0.42525, 0.18047, 0.8938, 0.26472, 0.77965, 0.4117, 0.66152, 0.55868, 0.57126, 0.71283, 0.49294, 0.38302, 0.93628, 0.51387, 0.79691, 0.6716, 0.64293, 0.78094, 0.54338, 0.8383, 0.61904, 0.76122, 0.74514, 0.64472, 0.86062, 0.51028, 0.9469, 0.26651, 0.4664, 0.44934, 0.38012, 0.60707, 0.3403, 0.29519, 0.29782, 0.41349, 0.27791, 0.50311, 0.07217, 0.52641, 0.15314, 0.65726, 0.06687, 0.6967, 0.19695, 0.78453, 0.11067, 0.82755, 0.25137], "triangles": [36, 35, 22, 35, 23, 22, 20, 37, 21, 21, 36, 22, 19, 37, 20, 37, 36, 21, 34, 24, 23, 33, 65, 24, 65, 25, 24, 32, 63, 65, 63, 64, 65, 25, 64, 26, 25, 65, 64, 1, 61, 2, 31, 61, 63, 61, 1, 60, 61, 62, 63, 63, 62, 64, 61, 60, 62, 62, 28, 64, 64, 27, 26, 64, 28, 27, 1, 0, 60, 60, 29, 62, 60, 0, 29, 62, 29, 28, 14, 13, 42, 42, 13, 11, 13, 12, 11, 11, 10, 42, 42, 43, 47, 10, 9, 42, 42, 9, 43, 43, 44, 48, 9, 8, 43, 8, 38, 43, 43, 38, 44, 8, 7, 38, 38, 39, 44, 44, 39, 45, 39, 38, 55, 39, 40, 45, 38, 7, 55, 7, 6, 55, 39, 55, 56, 6, 5, 55, 55, 5, 58, 50, 46, 35, 15, 54, 16, 14, 47, 15, 15, 47, 54, 47, 14, 42, 54, 47, 48, 47, 43, 48, 49, 44, 45, 49, 46, 50, 49, 45, 46, 45, 40, 46, 49, 48, 44, 49, 50, 51, 48, 49, 52, 54, 48, 53, 53, 48, 52, 52, 49, 51, 53, 52, 19, 16, 54, 53, 19, 52, 37, 18, 53, 19, 17, 53, 18, 17, 16, 53, 52, 51, 37, 37, 51, 36, 36, 51, 35, 34, 33, 24, 46, 41, 34, 33, 57, 32, 32, 31, 63, 39, 56, 40, 40, 41, 46, 40, 57, 41, 40, 56, 57, 55, 58, 56, 58, 59, 56, 41, 57, 33, 57, 59, 31, 57, 56, 59, 5, 4, 58, 58, 4, 30, 58, 30, 59, 30, 4, 3, 59, 30, 31, 31, 30, 61, 3, 2, 30, 61, 30, 2, 57, 31, 32, 41, 33, 34, 33, 32, 65, 46, 34, 35, 35, 34, 23, 51, 50, 35], "vertices": [2, 38, 2.66, 164.45, 0.24701, 40, -102.13, -36.37, 0.75299, 2, 38, -22.49, 124.27, 0.46013, 40, -86.79, -81.22, 0.53987, 2, 38, -36.37, 83.26, 0.69127, 40, -63.7, -117.84, 0.30873, 3, 38, -41.63, 52.5, 0.84057, 42, -81.07, 52.53, 0.00093, 40, -43.13, -141.31, 0.1585, 3, 38, -46.31, 25.08, 0.89184, 42, -66.94, 28.56, 0.04739, 40, -24.8, -162.23, 0.06077, 2, 38, -42.62, -34.35, 0.16325, 42, -25.74, -14.43, 0.83675, 2, 42, 41.59, -53.87, 0.97296, 43, 0.97, -89.81, 0.02704, 2, 42, 105.18, -76.7, 0.36155, 43, 57.59, -52.95, 0.63845, 2, 42, 134.06, -79.67, 0.0698, 43, 77.41, -31.73, 0.9302, 1, 43, 170.35, -39.01, 1, 1, 43, 221.58, -50.14, 1, 1, 43, 327.08, -37.62, 1, 1, 43, 329.36, 1.97, 1, 2, 39, 418.48, -40.8, 0.12758, 43, 301.58, 39.57, 0.87242, 2, 39, 438.63, 2.6, 0.99845, 41, 388.99, 54.98, 0.00155, 1, 39, 399.88, 59.63, 1, 4, 39, 367.07, 123.29, 0.56582, 41, 284.44, 148.56, 0.13025, 40, 480.57, -173.25, 0.09482, 37, 565.42, -7.84, 0.20911, 3, 39, 332.63, 171.54, 0.592, 40, 488.03, -114.45, 0.1991, 37, 556.19, 50.72, 0.2089, 5, 38, 319.92, -306.6, 0.01051, 39, 295.97, 175.13, 0.464, 41, 201.04, 176.61, 0.02232, 40, 463.54, -86.93, 0.34023, 37, 525, 70.31, 0.16294, 5, 38, 313.79, -210.99, 0.03459, 39, 200.95, 162.87, 0.784, 41, 114.11, 136.34, 0.05941, 40, 385.45, -31.43, 0.04424, 37, 434.52, 101.83, 0.07776, 1, 37, 336.56, 137.42, 1, 1, 37, 275.27, 143.73, 1, 1, 37, 214.06, 152.45, 1, 1, 37, 173.42, 149.52, 1, 1, 40, 89.34, 103.14, 1, 1, 40, 32.19, 97.26, 1, 1, 40, -19.49, 94.81, 1, 1, 40, -57.57, 79, 1, 2, 38, 89.09, 205.78, 0.00691, 40, -79.74, 56.79, 0.99309, 2, 38, 49.55, 188.32, 0.07109, 40, -91.12, 15.08, 0.92891, 1, 38, -4.45, 53.73, 1, 1, 38, 56.68, 61.81, 1, 1, 38, 112.66, 61.8, 1, 1, 38, 167.68, 60.65, 1, 2, 38, 207.82, 47.11, 0.96, 40, 118.27, 48.96, 0.04, 2, 38, 239.05, 9.28, 0.984, 40, 167.33, 49.37, 0.016, 4, 38, 268.34, -50.48, 0.29502, 41, -19.12, 35.95, 0.14739, 40, 232.18, 34.44, 0.14159, 37, 268.97, 122.34, 0.416, 3, 38, 284.95, -101.3, 0.63942, 41, 22.26, 69.81, 0.18458, 40, 282.11, 15.31, 0.176, 2, 42, 124.89, -55.97, 0.15879, 43, 52.99, -24.72, 0.84121, 3, 38, 77.11, -107.99, 0.00713, 39, 113.35, -79.94, 0.03264, 43, -3.96, 3.84, 0.96023, 4, 38, 108.39, -45.48, 0.45029, 39, 48.96, -52.73, 0.34432, 42, 96.75, 74.59, 0.10231, 43, -68.04, 31.77, 0.10308, 4, 38, 148.35, -0.95, 0.97573, 39, 1.96, -15.72, 0.02423, 42, 98.5, 134.39, 1e-05, 43, -114.63, 69.31, 3e-05, 2, 39, 354.57, -66.03, 0.06493, 43, 237.39, 15.06, 0.93507, 2, 39, 267.35, -70.96, 0.0955, 43, 150.13, 11.11, 0.9045, 2, 39, 198.99, -59.16, 0.37739, 43, 81.91, 23.67, 0.62261, 4, 38, 124.23, -113.95, 0.03741, 39, 116.28, -32.53, 0.80671, 42, 153.06, 32.53, 0.01174, 43, -0.51, 51.21, 0.14415, 3, 39, 26.52, 10.56, 0.77891, 41, -6.43, -61.38, 0.003, 40, 153.97, -24.88, 0.21809, 2, 39, 346.84, 2.29, 0.98763, 41, 301.54, 27.08, 0.01237, 2, 39, 243.63, 12.02, 0.968, 41, 200.19, 5.32, 0.032, 2, 39, 100.24, 31.68, 0.99642, 40, 222.44, -59.4, 0.00358, 3, 39, 37.27, 44.64, 0.73393, 41, -6.43, -25.65, 0.01439, 40, 185, -7.15, 0.25167, 1, 38, 241.09, -66.14, 1, 2, 39, 209.98, 93.49, 0.784, 41, 143.59, 72.89, 0.216, 2, 39, 283.18, 83.89, 0.896, 40, 392.22, -145.25, 0.104, 2, 39, 348.22, 67.19, 0.88, 40, 428.65, -201.66, 0.12, 1, 42, 49.78, -12.74, 1, 4, 38, 60.74, -14.9, 0.89277, 39, 21.51, -102.25, 0.00891, 42, 40.62, 67.16, 0.09203, 43, -96.05, -17.44, 0.00628, 2, 38, 106.22, 26.98, 0.67801, 40, 69.87, -42.61, 0.32199, 3, 38, -8.23, -0.27, 0.92782, 42, -21.49, 33.8, 0.06909, 40, 18.89, -148.64, 0.00308, 1, 38, 27.89, 26.57, 1, 2, 38, 16.7, 134.3, 0.29773, 40, -69.88, -44.47, 0.70227, 2, 38, 40.98, 100.68, 0.3392, 40, -28.47, -46.81, 0.6608, 2, 38, 67.94, 159.82, 0.05494, 40, -57.4, 11.4, 0.94506, 1, 40, 9.32, 8.37, 1, 1, 40, -23.8, 51.18, 1, 1, 40, 48.4, 48.03, 1], "hull": 30, "edges": [0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 4, 6, 6, 8, 6, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 38, 76, 78, 78, 80, 80, 82, 84, 86, 86, 88, 88, 90, 90, 92, 94, 96, 96, 98, 98, 100, 70, 102, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 116, 118, 120, 122, 124, 126, 128, 130], "width": 371, "height": 501}}, "yanjing": {"yanjing": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 36, -70.15, -30.31, 1, 1, 36, -22.81, 47.4, 1, 1, 36, 67.72, -7.74, 1, 1, 36, 20.38, -85.45, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 106}}, "bu_jianbang_zuo": {"bu_jianbang_zuo": {"type": "mesh", "uvs": [0, 0.1826, 0.14332, 0.10098, 0.3332, 0.03248, 0.48113, 0, 0.67542, 0.00479, 0.84102, 0.02811, 1, 0.08058, 1, 0.15637, 0.93154, 0.2686, 0.87634, 0.38666, 0.82998, 0.51055, 0.78361, 0.61986, 0.74387, 0.72334, 0.67984, 0.83703, 0.62243, 0.95654, 0.55399, 1, 0.49217, 0.92011, 0.45242, 0.80642, 0.49879, 0.68545, 0.47892, 0.59654, 0.3906, 0.46973, 0.28904, 0.36479, 0.15877, 0.26714, 0.4171, 0.19426, 0.76595, 0.15491, 0.59373, 0.3269, 0.66659, 0.48722], "triangles": [24, 4, 5, 24, 5, 6, 24, 6, 7, 23, 2, 3, 1, 2, 23, 22, 1, 23, 0, 1, 22, 8, 24, 7, 23, 4, 24, 4, 23, 3, 25, 24, 8, 25, 23, 24, 21, 22, 23, 21, 23, 25, 9, 25, 8, 20, 21, 25, 26, 25, 9, 20, 25, 26, 10, 26, 9, 19, 20, 26, 11, 26, 10, 19, 26, 11, 18, 19, 11, 12, 18, 11, 13, 18, 12, 17, 18, 13, 16, 17, 13, 14, 16, 13, 15, 16, 14], "vertices": [-96.15, 150.71, -81.68, 163.2, -62.5, 173.68, -47.56, 178.65, -27.94, 177.92, -11.21, 174.35, 4.85, 166.32, 4.85, 154.72, -2.07, 137.55, -7.64, 119.49, -12.33, 100.53, -17.01, 83.81, -21.02, 67.98, -27.49, 50.58, -33.29, 32.3, -40.2, 25.65, -46.44, 37.87, -50.46, 55.27, -45.78, 73.77, -47.78, 87.38, -56.7, 106.78, -66.96, 122.83, -80.12, 137.78, -54.03, 148.93, -18.79, 154.95, -36.19, 128.63, -28.83, 104.1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 101, "height": 153}}, "hehua_zuosahng": {"hehua_zuosahng": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.5, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.5, 0.66667, 0.5, 0.33333], "triangles": [4, 5, 6, 11, 4, 6, 11, 6, 7, 8, 11, 7, 10, 11, 8, 3, 4, 11, 10, 3, 11, 2, 3, 10, 1, 2, 10, 9, 10, 8, 1, 10, 9, 0, 1, 9], "vertices": [2, 112, -16.69, -50.54, 0.91009, 113, -67.26, -63.03, 0.08991, 2, 112, -11.87, 2.24, 0.99977, 113, -71.81, -10.23, 0.00023, 1, 112, -7.05, 55.02, 1, 1, 112, 36.44, 51.04, 1, 2, 112, 79.92, 47.07, 0.74791, 113, 10.65, 50.08, 0.25209, 2, 112, 123.41, 43.09, 0.3057, 113, 54.15, 53.83, 0.6943, 2, 112, 118.59, -9.69, 0.00133, 113, 58.7, 1.02, 0.99867, 1, 113, 63.26, -51.78, 1, 2, 112, 70.28, -58.49, 0.04204, 113, 19.75, -55.53, 0.95796, 2, 112, 26.79, -54.52, 0.58109, 113, -23.76, -59.28, 0.41891, 2, 112, 31.61, -1.74, 0.99195, 113, -28.31, -6.48, 0.00805, 1, 113, 15.2, -2.73, 1], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 106, "height": 131}}, "toufa_you": {"toufa_you": {"type": "mesh", "uvs": [0.26191, 0.00428, 0.42249, 0.06498, 0.48877, 0.27281, 0.55504, 0.4696, 0.65699, 0.67192, 0.83542, 0.83377, 1, 0.91653, 1, 0.9993, 0.84816, 1, 0.62641, 0.99378, 0.43269, 0.94596, 0.35877, 0.77123, 0.24407, 0.55237, 0.15231, 0.37764, 0.03506, 0.22867, 0, 0.10176, 0.02996, 0, 0.24407, 0.10176, 0.33583, 0.23602, 0.37152, 0.51007, 0.49386, 0.73997, 0.68248, 0.87055, 0.86855, 0.94412], "triangles": [18, 17, 1, 17, 0, 1, 17, 16, 0, 21, 4, 5, 20, 4, 21, 22, 5, 6, 21, 5, 22, 10, 20, 21, 9, 10, 21, 8, 9, 21, 22, 6, 7, 22, 8, 21, 8, 22, 7, 15, 16, 17, 14, 15, 17, 14, 17, 18, 18, 1, 2, 13, 14, 18, 19, 18, 2, 19, 2, 3, 13, 18, 19, 12, 13, 19, 4, 20, 19, 4, 19, 3, 12, 19, 20, 11, 12, 20, 10, 11, 20], "vertices": [2, 32, -92.63, 34.72, 0.12, 4, 204.41, -94.89, 0.88, 1, 4, 175.82, -112.08, 1, 1, 32, -12.6, 39.72, 1, 1, 32, 37.48, 27.11, 1, 2, 32, 91.87, 19.65, 0.61279, 33, 1.28, 20.41, 0.38721, 1, 33, 53.37, 12.19, 1, 1, 33, 89.96, 17.12, 1, 1, 33, 104.23, 1.57, 1, 1, 33, 83.76, -17.45, 1, 2, 32, 161.85, -23.6, 0.00107, 33, 52.62, -43.87, 0.99893, 2, 32, 134.45, -49.44, 0.09711, 33, 18.12, -58.98, 0.90289, 2, 32, 88.69, -40.7, 0.74915, 33, -22.03, -35.35, 0.25085, 1, 32, 29.48, -33.35, 1, 1, 32, -17.81, -27.51, 1, 1, 32, -61.48, -28.89, 1, 1, 32, -93.11, -19.51, 1, 2, 32, -113.5, -2.53, 0.552, 4, 227.54, -59.01, 0.448, 1, 4, 184.89, -79.16, 1, 1, 32, -34.01, 19.2, 1, 1, 32, 30.87, -7.58, 1, 2, 32, 93.23, -14.99, 0.77107, 33, -9.1, -12.67, 0.22893, 1, 33, 38.98, -13.74, 1, 1, 33, 76.89, -4.42, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 184, "height": 255}}, "toufa_zuo": {"toufa_zuo": {"type": "mesh", "uvs": [0.04696, 0.13678, 0.17964, 0.10987, 0.32594, 0.13836, 0.44955, 0.19217, 0.52327, 0.13836, 0.5743, 0, 0.63894, 0.00382, 0.69564, 0.13994, 0.80565, 0.24282, 0.92882, 0.26815, 0.99913, 0.36945, 1, 0.52457, 0.95604, 0.61954, 0.85737, 0.59421, 0.74964, 0.64961, 0.6351, 0.72717, 0.57386, 0.76516, 0.51375, 0.83797, 0.41963, 0.91236, 0.30962, 0.95351, 0.18487, 0.93927, 0.06693, 0.95509, 0, 0.8807, 0, 0.70343, 0.00229, 0.55622, 0.01817, 0.38211, 0.03518, 0.23174, 0.64516, 0.15225, 0.54027, 0.2598, 0.39471, 0.28221, 0.29088, 0.2613, 0.17208, 0.24636, 0.6837, 0.22843, 0.56061, 0.37334, 0.4129, 0.4794, 0.22131, 0.56007, 0.04792, 0.578, 0.31122, 0.37334, 0.18706, 0.41517, 0.75969, 0.30611, 0.72651, 0.46148, 0.59379, 0.63626, 0.40327, 0.72589, 0.25021, 0.75128, 0.08431, 0.78863, 0.89241, 0.35989, 0.85495, 0.50928], "triangles": [19, 43, 42, 19, 42, 18, 20, 43, 19, 16, 42, 41, 16, 41, 15, 35, 37, 34, 34, 33, 41, 42, 35, 34, 42, 34, 41, 17, 42, 16, 18, 42, 17, 43, 35, 42, 32, 33, 28, 39, 32, 8, 40, 32, 39, 45, 39, 8, 46, 39, 45, 40, 39, 46, 41, 33, 40, 14, 40, 46, 14, 46, 13, 41, 40, 14, 15, 41, 14, 32, 7, 8, 33, 32, 40, 35, 38, 37, 36, 25, 38, 36, 38, 35, 24, 25, 36, 23, 24, 36, 44, 36, 35, 44, 35, 43, 23, 36, 44, 22, 23, 44, 20, 44, 43, 21, 22, 44, 21, 44, 20, 31, 0, 1, 30, 31, 1, 26, 0, 31, 38, 31, 30, 25, 26, 31, 25, 31, 38, 33, 29, 28, 33, 34, 29, 2, 30, 1, 29, 2, 3, 30, 2, 29, 29, 3, 28, 37, 30, 29, 38, 30, 37, 37, 29, 34, 32, 27, 7, 32, 28, 27, 27, 6, 7, 4, 27, 28, 3, 4, 28, 5, 27, 4, 6, 27, 5, 45, 8, 9, 45, 9, 10, 13, 46, 12, 45, 11, 46, 10, 11, 45, 11, 12, 46], "vertices": [1, 28, 125.75, -27.6, 1, 2, 27, 142.86, -35.44, 0.02122, 28, 23.58, -43.44, 0.97878, 2, 27, 75.8, -31.06, 0.99641, 28, -39.8, -21.08, 0.00359, 2, 26, 82.65, -25.53, 0.18519, 27, 18.42, -17.69, 0.81481, 2, 26, 45.84, -16.72, 0.99144, 27, -13.75, -37.64, 0.00856, 1, 26, -1.3, -35.46, 1, 1, 26, -22.65, -15.19, 1, 3, 4, 192.92, 75.65, 0.00278, 26, -12.94, 35.21, 0.58143, 29, -41.16, -36.63, 0.41579, 3, 4, 138.24, 50.35, 0.27974, 26, -28.62, 93.39, 0.00978, 29, -48.84, 23.13, 0.71048, 2, 4, 102.03, 6.78, 0.82998, 29, -79.49, 70.77, 0.17002, 1, 4, 57.19, -3.36, 1, 2, 4, 13.79, 22.6, 0.92334, 29, -37.85, 150.16, 0.07666, 2, 4, -2.24, 55.79, 0.84461, 29, -1.38, 155.46, 0.15539, 2, 4, 28.16, 89.84, 0.57016, 29, 21.94, 116.23, 0.42984, 3, 4, 38.24, 141.1, 0.18546, 29, 67.8, 91.19, 0.77565, 30, -78, 57.34, 0.03889, 3, 4, 43.75, 198.76, 0.02424, 29, 121.13, 68.58, 0.50197, 30, -21.16, 68.48, 0.47379, 3, 4, 47.67, 229, 0.00353, 29, 148.78, 55.75, 0.16822, 30, 8.95, 73.34, 0.82826, 3, 31, -128.33, 52.07, 6e-05, 29, 184.65, 50.77, 0.01273, 30, 41.45, 89.31, 0.98721, 2, 31, -87.01, 78.81, 0.04044, 30, 89.05, 101.83, 0.95956, 2, 31, -37.84, 95.17, 0.26797, 30, 140.87, 102.02, 0.73203, 2, 31, 19.09, 93.88, 0.75485, 30, 194.56, 83.04, 0.24515, 2, 31, 122.68, 123.47, 0.97851, 30, 298.97, 93.36, 0.02149, 2, 31, 143.53, 88.47, 0.99977, 30, 310.7, 50.04, 0.00023, 2, 31, 146.67, 52.66, 1, 30, 306.09, 9.65, 0, 2, 28, 157.55, 107.71, 0.13189, 31, 128.13, 4.58, 0.86811, 2, 28, 121.63, 44.39, 0.74613, 31, 109.67, -65.84, 0.25387, 1, 28, 158.83, -1.98, 1, 2, 26, 7.01, 23.14, 0.77434, 29, -23.03, -51.28, 0.22566, 4, 26, 66.03, 18.21, 0.82079, 27, -24.37, 1.27, 0.02377, 29, 34.77, -64.16, 0.14743, 30, -18.34, -89.84, 0.00801, 4, 27, 41.15, 13.42, 0.94406, 31, -63.55, -125.58, 9e-05, 29, 83.85, -109.23, 0.00162, 30, 47.57, -99.69, 0.05422, 4, 27, 88.76, 10.08, 0.95205, 28, -16.19, 15.02, 0.02042, 31, -15.99, -129.6, 0.00438, 30, 91.5, -118.34, 0.02315, 1, 28, 35.82, -0.52, 1, 3, 4, 171.11, 95.3, 0.00401, 26, 10.12, 53.39, 0.29887, 29, -15.86, -21.74, 0.69711, 4, 26, 83.38, 52.19, 0.22875, 27, -36.31, 37.51, 0.09706, 29, 56.56, -32.84, 0.60802, 30, -17.83, -51.7, 0.06617, 6, 26, 156.76, 34.09, 0.0005, 27, 28.18, 76.92, 0.26206, 28, -56.44, 95.75, 0.00334, 31, -75.62, -61.9, 0.00034, 29, 126.82, -60.7, 0.016, 30, 55.97, -35.42, 0.71776, 4, 27, 113.19, 109.54, 0.04064, 28, 34.22, 104.17, 0.09626, 31, 9.85, -30.5, 0.54131, 30, 146.97, -32.25, 0.32179, 2, 28, 112.7, 94.19, 0.08932, 31, 88.26, -20, 0.91068, 4, 27, 76.86, 45.83, 0.59133, 28, -17.99, 52.65, 0.10863, 31, -27.39, -93.68, 0.03396, 30, 91.88, -80.66, 0.26608, 4, 27, 132.2, 63.57, 0.09121, 28, 40.09, 54.77, 0.5228, 31, 28.2, -76.73, 0.26693, 30, 149.98, -81.9, 0.11906, 3, 4, 131.49, 78.94, 0.15382, 26, 0.7, 95.2, 0.00384, 29, -19.54, 20.96, 0.84234, 2, 4, 96.09, 118.18, 0.14229, 29, 28.53, 42.91, 0.85771, 3, 4, 78.84, 199.4, 0.01205, 29, 111.17, 34.93, 0.47708, 30, -10.55, 35.03, 0.51087, 2, 31, -75.99, 18.57, 0.00181, 30, 80.72, 41.15, 0.99819, 2, 31, -6.96, 30.95, 0.44204, 30, 150.17, 31.38, 0.55796, 2, 31, 67.67, 47.57, 0.99351, 30, 226.26, 23.88, 0.00649, 2, 4, 85.11, 36.49, 0.74284, 29, -46.07, 77.97, 0.25716, 2, 4, 52.38, 76.38, 0.57955, 29, 1.82, 97.18, 0.42045], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 54, 56, 56, 58, 58, 60, 60, 62, 64, 66, 66, 68, 68, 70, 70, 72, 74, 76, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 90, 92], "width": 455, "height": 326}}, "heye_shang": {"heye_shang": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.66667, 0.66667, 0.33333, 0.66667, 0.66667, 0.33333, 0.33333, 0.33333], "triangles": [10, 14, 9, 14, 8, 9, 14, 15, 8, 15, 7, 8, 5, 6, 7, 0, 1, 11, 1, 12, 11, 11, 12, 10, 12, 14, 10, 1, 2, 12, 2, 13, 12, 12, 13, 14, 13, 15, 14, 2, 3, 13, 3, 4, 13, 13, 4, 15, 4, 5, 15, 15, 5, 7], "vertices": [2, 118, 42.53, -29.39, 0.99727, 119, -53.51, -47.46, 0.00273, 1, 118, 32.91, 18.32, 1, 1, 118, 23.29, 66.03, 1, 1, 118, 13.67, 113.73, 1, 1, 118, 67.58, 124.61, 1, 2, 118, 121.49, 135.48, 0.79087, 119, -27.02, 133.41, 0.20913, 2, 118, 175.41, 146.35, 0.27483, 119, 21.25, 159.79, 0.72517, 2, 118, 185.03, 98.65, 0.04156, 119, 44.59, 117.09, 0.95844, 2, 118, 194.65, 50.94, 0.01586, 119, 67.93, 74.38, 0.98414, 2, 118, 204.27, 3.24, 0.08701, 119, 91.27, 31.68, 0.91299, 2, 118, 150.36, -7.64, 0.43176, 119, 43.01, 5.3, 0.56824, 2, 118, 96.44, -18.51, 0.8835, 119, -5.25, -21.08, 0.1165, 2, 118, 86.82, 29.19, 0.94911, 119, -28.59, 21.63, 0.05089, 1, 118, 77.2, 76.9, 1, 2, 118, 140.74, 40.07, 0.47621, 119, 19.67, 48, 0.52379, 2, 118, 131.12, 87.77, 0.97968, 119, -3.67, 90.71, 0.02032], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 146, "height": 165}}, "bu_jianbang_you": {"bu_jianbang_you": {"type": "mesh", "uvs": [0, 0.00711, 0.18935, 0, 0.42194, 0, 0.59219, 0.01255, 0.75284, 0.11046, 0.86554, 0.26275, 0.95426, 0.41685, 1, 0.56368, 0.86314, 0.74316, 0.73366, 0.87007, 0.66747, 0.933, 0.59698, 1, 0.54903, 0.94622, 0.53224, 0.8175, 0.47469, 0.69059, 0.41235, 0.54555, 0.33322, 0.40413, 0.2421, 0.2736, 0.14139, 0.15756, 0.03589, 0.0651, 0, 0.03065], "triangles": [19, 20, 0, 1, 19, 0, 18, 19, 1, 2, 18, 1, 17, 18, 2, 17, 4, 16, 3, 17, 2, 16, 5, 15, 3, 4, 17, 5, 16, 4, 15, 5, 6, 15, 6, 7, 14, 15, 7, 8, 14, 7, 13, 14, 8, 9, 13, 8, 10, 13, 9, 12, 13, 10, 11, 12, 10], "vertices": [158.23, 155.39, 175.84, 156.27, 197.47, 156.27, 213.31, 154.72, 228.25, 142.68, 238.73, 123.95, 246.98, 104.99, 251.23, 86.93, 238.5, 64.86, 225.87, 49.6, 218.05, 40.42, 213.25, 28.61, 209.29, 39.88, 207.73, 55.71, 202.38, 71.32, 196.58, 89.16, 189.22, 106.56, 180.75, 122.61, 171.38, 136.89, 161.57, 148.26, 158.23, 152.5], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 18, 20, 20, 22], "width": 93, "height": 123}}, "shangyi": {"shangyi": {"type": "mesh", "uvs": [0.51231, 0.02226, 0.52691, 0.0547, 0.53939, 0.09025, 0.56311, 0.13286, 0.57345, 0.13596, 0.58623, 0.14979, 0.61659, 0.17277, 0.64069, 0.18323, 0.67094, 0.1799, 0.70786, 0.18276, 0.71966, 0.2132, 0.71556, 0.25411, 0.70171, 0.28935, 0.71453, 0.32788, 0.72017, 0.38067, 0.71453, 0.42301, 0.69599, 0.45032, 0.6812, 0.51481, 0.66992, 0.54906, 0.67864, 0.57284, 0.70428, 0.60233, 0.72325, 0.63753, 0.7453, 0.66797, 0.77965, 0.69033, 0.80984, 0.71084, 0.86727, 0.72844, 0.91599, 0.73558, 0.95701, 0.73463, 1, 0.71988, 1, 0.76697, 1, 0.80788, 1, 0.84355, 0.97701, 0.85639, 0.82933, 0.90824, 0.76215, 0.91633, 0.71653, 0.90629, 0.68562, 0.86882, 0.66875, 0.82483, 0.66032, 0.77856, 0.65272, 0.73819, 0.64831, 0.70619, 0.64639, 0.68116, 0.64269, 0.65819, 0.63273, 0.62791, 0.6219, 0.59769, 0.61282, 0.58045, 0.60472, 0.57831, 0.59315, 0.59033, 0.57902, 0.58582, 0.57, 0.59141, 0.55981, 0.59828, 0.55865, 0.6103, 0.54592, 0.61503, 0.53157, 0.60193, 0.53527, 0.59248, 0.52856, 0.58346, 0.5193, 0.58346, 0.51282, 0.57358, 0.49705, 0.57315, 0.48594, 0.56371, 0.46326, 0.5622, 0.45029, 0.5476, 0.44233, 0.56522, 0.40504, 0.62268, 0.35764, 0.67223, 0.31702, 0.72317, 0.28316, 0.76923, 0.26209, 0.83134, 0.23351, 0.88229, 0.19589, 0.93603, 0.12818, 0.9772, 0.07852, 1, 0.03413, 1, 0, 0.9772, 0, 0.92067, 0.06949, 0.90253, 0.1214, 0.85647, 0.12291, 0.79715, 0.12065, 0.75039, 0.2004, 0.70014, 0.26285, 0.64013, 0.30573, 0.59407, 0.34786, 0.55289, 0.38398, 0.52428, 0.39, 0.49706, 0.38699, 0.43355, 0.35764, 0.35679, 0.34937, 0.32608, 0.34109, 0.27304, 0.32304, 0.21372, 0.32605, 0.16836, 0.35087, 0.14952, 0.393, 0.15091, 0.42686, 0.16208, 0.45094, 0.14812, 0.43438, 0.12928, 0.41558, 0.0902, 0.43589, 0.03227, 0.47727, 0, 0.50593, 0, 0.88266, 0.88446, 0.93394, 0.86638, 0.46848, 0.03722, 0.49074, 0.08928, 0.17614, 0.75161, 0.26862, 0.70173, 0.67461, 0.64296, 0.7005, 0.6873, 0.7513, 0.74273, 0.82798, 0.76952, 0.91264, 0.79262, 0.97041, 0.79262, 0.71038, 0.77171, 0.76096, 0.81603, 0.84387, 0.84991, 0.76378, 0.87337, 0.22582, 0.77333, 0.18333, 0.85215, 0.13963, 0.91972, 0.07286, 0.95688, 0.341, 0.62165, 0.42433, 0.52362, 0.51274, 0.13188, 0.65507, 0.59952, 0.64065, 0.54483, 0.53723, 0.19145, 0.56103, 0.25768, 0.5769, 0.32758, 0.56651, 0.38993, 0.5667, 0.45641, 0.55367, 0.52474, 0.55083, 0.57151, 0.44994, 0.19576, 0.46819, 0.25205, 0.47731, 0.31215, 0.47428, 0.38919, 0.4665, 0.46048, 0.45093, 0.51492, 0.62222, 0.20437, 0.64917, 0.26937, 0.65995, 0.34381, 0.63959, 0.40992, 0.64259, 0.4777, 0.62582, 0.53603, 0.42287, 0.40531, 0.51375, 0.38529], "triangles": [102, 97, 98, 98, 0, 102, 0, 98, 99, 1, 102, 0, 1, 103, 102, 103, 1, 2, 96, 103, 95, 102, 96, 97, 103, 96, 102, 122, 103, 2, 94, 95, 103, 122, 2, 3, 122, 94, 103, 125, 122, 3, 132, 122, 125, 122, 132, 94, 3, 5, 125, 4, 5, 3, 132, 93, 94, 127, 126, 139, 85, 86, 144, 86, 87, 134, 133, 132, 125, 138, 126, 125, 6, 125, 5, 133, 125, 126, 134, 133, 126, 145, 134, 127, 135, 134, 145, 135, 144, 134, 136, 144, 135, 136, 135, 145, 133, 88, 132, 134, 88, 133, 134, 144, 86, 138, 125, 6, 88, 134, 87, 134, 126, 127, 128, 145, 127, 128, 127, 140, 137, 84, 136, 84, 85, 136, 141, 140, 14, 141, 128, 140, 45, 46, 143, 123, 44, 45, 124, 123, 45, 124, 19, 123, 19, 124, 18, 85, 144, 136, 137, 59, 61, 60, 61, 59, 129, 137, 136, 129, 59, 137, 59, 57, 58, 59, 130, 57, 55, 57, 131, 56, 57, 55, 54, 55, 131, 16, 141, 15, 129, 128, 141, 142, 141, 16, 129, 141, 142, 17, 142, 16, 129, 145, 128, 130, 129, 142, 143, 130, 142, 143, 142, 17, 124, 143, 17, 18, 124, 17, 136, 145, 129, 130, 59, 129, 46, 131, 130, 131, 57, 130, 143, 46, 130, 124, 45, 143, 48, 131, 46, 47, 48, 46, 49, 131, 48, 50, 54, 131, 49, 50, 131, 50, 53, 54, 50, 52, 53, 51, 52, 50, 15, 141, 14, 27, 28, 29, 109, 24, 25, 111, 27, 29, 110, 25, 26, 109, 25, 110, 110, 26, 27, 111, 110, 27, 111, 29, 30, 32, 111, 30, 114, 109, 110, 113, 109, 114, 31, 32, 30, 101, 110, 111, 101, 111, 32, 114, 110, 101, 115, 113, 114, 100, 114, 101, 33, 115, 114, 33, 114, 100, 32, 33, 100, 32, 100, 101, 34, 35, 115, 34, 115, 33, 123, 19, 20, 43, 44, 123, 106, 123, 20, 106, 20, 21, 43, 123, 106, 42, 43, 106, 41, 42, 106, 107, 106, 21, 107, 21, 22, 41, 106, 107, 40, 41, 107, 39, 40, 107, 108, 22, 23, 108, 23, 24, 107, 22, 108, 112, 39, 107, 108, 24, 109, 108, 112, 107, 38, 39, 112, 113, 108, 109, 112, 108, 113, 37, 38, 112, 37, 112, 113, 36, 37, 113, 36, 113, 115, 35, 36, 115, 77, 78, 104, 117, 104, 116, 77, 104, 117, 76, 77, 117, 68, 117, 67, 118, 76, 117, 69, 118, 117, 75, 76, 118, 68, 69, 117, 119, 75, 118, 74, 75, 119, 73, 74, 119, 70, 119, 118, 70, 118, 69, 72, 73, 119, 71, 119, 70, 72, 119, 71, 121, 84, 137, 61, 121, 137, 62, 121, 61, 120, 81, 82, 121, 82, 83, 121, 120, 82, 120, 121, 63, 62, 63, 121, 64, 120, 63, 105, 120, 64, 120, 80, 81, 120, 105, 80, 79, 80, 105, 65, 105, 64, 104, 78, 79, 104, 79, 105, 66, 105, 65, 116, 104, 105, 66, 116, 105, 67, 116, 66, 117, 116, 67, 83, 84, 121, 126, 138, 139, 140, 13, 14, 127, 139, 140, 138, 6, 7, 9, 10, 8, 8, 138, 7, 10, 139, 8, 11, 139, 10, 139, 138, 8, 12, 139, 11, 140, 139, 12, 140, 12, 13, 91, 89, 90, 92, 88, 89, 91, 92, 89, 92, 132, 88, 93, 132, 92], "vertices": [2, 47, 184.23, 14.14, 0, 4, 115.16, -51.61, 1, 1, 4, 87.32, -47.36, 1, 3, 6, 107.46, 96.86, 0.00028, 47, 127.37, 2.41, 0.00109, 4, 58.18, -40.46, 0.99863, 4, 6, 71.07, 84.55, 0.02543, 47, 90.99, -9.91, 0.06562, 3, 206.34, -27.15, 0.0779, 4, 19.87, -37.76, 0.83106, 4, 6, 67.47, 77.32, 0.04522, 47, 87.38, -17.13, 0.11079, 3, 202.73, -34.37, 0.13526, 4, 13.75, -43.03, 0.70873, 4, 6, 55.09, 69.6, 0.0999, 47, 75.01, -24.86, 0.10651, 3, 190.36, -42.1, 0.25831, 4, -0.65, -45.37, 0.53527, 5, 6, 33.51, 50.05, 0.43751, 48, 115.4, -51.77, 0.00617, 47, 53.43, -44.4, 0.09546, 3, 168.78, -61.65, 0.27997, 4, -28.1, -55.06, 0.18089, 5, 6, 22.54, 33.6, 0.6812, 48, 104.43, -68.22, 0.01553, 47, 42.46, -60.85, 0.18156, 3, 157.81, -78.1, 0.08948, 4, -44.58, -66, 0.03223, 4, 6, 21.81, 10.98, 0.86435, 47, 41.73, -83.48, 0.11948, 3, 157.08, -100.72, 0.01342, 4, -53.99, -86.58, 0.00275, 1, 6, 15.44, -15.8, 1, 3, 6, -9.98, -20.82, 0.95881, 48, 71.9, -122.64, 0.04109, 2, 313.5, -94.93, 0.00011, 4, 6, -41.92, -12.89, 0.63597, 48, 39.96, -114.72, 0.24213, 47, -22.01, -107.35, 0.11632, 2, 280.6, -95.68, 0.00557, 5, 6, -68.29, 1.51, 0.56598, 48, 13.59, -100.32, 0.20328, 47, -48.38, -92.95, 0.17702, 2, 251.38, -88.72, 0.05352, 3, 66.98, -110.19, 0.00019, 5, 6, -100.23, -3.29, 0.33803, 48, -18.34, -105.11, 0.30877, 47, -80.32, -97.74, 0.13703, 2, 221.82, -101.74, 0.20167, 3, 35.04, -114.98, 0.0145, 6, 6, -142.67, -1.1, 0.09484, 48, -60.79, -102.92, 0.44826, 47, -122.76, -95.55, 0.14672, 17, -148.11, 178.29, 0.00125, 2, 180.29, -110.77, 0.28623, 3, -7.4, -112.79, 0.02271, 6, 6, -175.57, 8.13, 0.05569, 48, -93.69, -93.7, 0.31064, 47, -155.66, -86.33, 0.12047, 17, -125.03, 153.09, 0.00785, 2, 146.13, -110.52, 0.48627, 3, -40.3, -103.57, 0.01907, 5, 6, -197.12, 22.85, 0.02713, 48, -115.23, -78.97, 0.17676, 47, -177.2, -71.6, 0.22456, 2, 121.46, -101.97, 0.56316, 3, -61.85, -88.84, 0.0084, 5, 6, -244.56, 43.63, 0.00393, 48, -162.68, -58.2, 0.13013, 47, -224.65, -50.83, 0.13691, 17, -85.16, 86.53, 0.072, 2, 70.23, -94.39, 0.65703, 5, 6, -270.43, 56.03, 0.00047, 48, -188.54, -45.8, 0.08325, 47, -250.52, -38.43, 0.17133, 17, -69.73, 62.35, 0.22744, 2, 42.01, -89.23, 0.51751, 4, 6, -290.23, 52.48, 3e-05, 48, -208.35, -49.35, 0.00743, 17, -51.03, 54.92, 0.52443, 2, 23.84, -97.86, 0.46811, 3, 48, -234.56, -64.65, 0.00058, 17, -20.69, 54.08, 0.79614, 2, 2.57, -119.5, 0.20329, 2, 17, 9.9, 46.51, 0.95149, 2, -23.81, -136.76, 0.04851, 3, 17, 39.09, 43.15, 0.99061, 18, -38.97, 95.98, 0.00756, 2, -46.15, -155.84, 0.00183, 2, 17, 69.28, 50.95, 0.90971, 18, -11.64, 80.96, 0.09029, 2, 17, 96.34, 57.35, 0.65315, 18, 12.45, 67.07, 0.34685, 2, 17, 139.09, 93.84, 0.13316, 18, 68.58, 64.31, 0.86684, 2, 17, 162.59, 104.5, 0.01189, 18, 93, 55.96, 0.98811, 1, 18, 123.22, 60.04, 1, 1, 18, 153.68, 75.26, 1, 1, 18, 166.71, -31.65, 1, 1, 18, 170.28, -64.22, 1, 3, 47, -589.87, -236.38, 0, 17, 322.99, 51.45, 0, 18, 173.4, -92.63, 1, 3, 47, -597.48, -217.95, 0, 17, 319.73, 31.78, 0, 18, 157.54, -104.72, 1, 3, 47, -663.59, -120.48, 0, 17, 324.46, -85.91, 0.06318, 18, 80.29, -193.63, 0.93682, 3, 47, -674.43, -74.91, 0, 17, 309.62, -130.34, 0.30375, 18, 39.03, -215.8, 0.69625, 3, 47, -723.78, -5.1, 0, 17, 314.71, -215.68, 0.4802, 18, -15.78, -281.41, 0.5198, 1, 17, 215.27, -219.64, 1, 1, 17, 169.62, -167.85, 1, 3, 47, -500.59, 5.35, 0, 17, 119.61, -106.79, 0.99357, 18, -83.15, -68.38, 0.00643, 2, 47, -398.39, -3.1, 0, 17, 37.24, -45.68, 1, 2, 47, -372.56, -3.7, 0, 17, 15.62, -31.54, 1, 2, 47, -352.52, -5.29, 0, 17, -0.56, -19.62, 1, 2, 17, -16.35, -9.77, 0.958, 2, -47.15, -79.2, 0.042, 3, 48, -246.86, -9.02, 0.00023, 17, -39.6, 0.34, 0.77725, 2, -23.91, -69.06, 0.22252, 4, 48, -221.72, -4.69, 0.2, 47, -283.69, 2.68, 0, 17, -63.24, 9.93, 0.40494, 2, -0.79, -58.28, 0.39506, 4, 48, -207.06, -0.09, 0.024, 47, -269.03, 7.28, 0, 17, -78.12, 13.76, 0.28653, 2, 12.14, -49.99, 0.68947, 4, 48, -204.45, 5.61, 0.04, 47, -266.42, 12.98, 0, 17, -83.34, 10.29, 0.18822, 2, 13.16, -43.81, 0.77178, 4, 48, -212.69, 15.55, 0.112, 47, -274.66, 22.92, 0, 17, -81.59, -2.5, 0.10359, 2, 2.6, -36.38, 0.78441, 4, 48, -207.55, 25.38, 0.074, 47, -269.52, 32.76, 0, 17, -91.15, -8.14, 0.06231, 2, 4.98, -25.54, 0.86369, 4, 48, -210.96, 32.69, 0.06, 47, -272.93, 40.06, 0, 17, -92.1, -16.15, 0.02866, 2, -0.24, -19.39, 0.91134, 4, 48, -215.28, 41, 0.108, 47, -277.25, 48.37, 0, 17, -92.82, -25.48, 0.00669, 2, -6.58, -12.51, 0.88531, 3, 48, -224.67, 43.29, 0.092, 17, -86.05, -32.38, 0.0013, 2, -16.24, -12.76, 0.9067, 2, 48, -226.99, 53.21, 0.116, 2, -21.09, -3.8, 0.884, 3, 48, -215.02, 62.18, 0.076, 15, -21.1, 107.26, 8e-05, 2, -11.9, 8, 0.92392, 3, 48, -207.95, 58.32, 0.3, 15, -28.93, 105.36, 0.00167, 2, -4.07, 6.14, 0.69833, 3, 48, -200.06, 62.17, 0.3, 15, -32.18, 97.21, 0.0135, 2, 2.53, 11.93, 0.6865, 3, 48, -199.03, 68.98, 0.044, 15, -28.37, 91.48, 0.03719, 2, 1.74, 18.76, 0.91881, 4, 5, -328.88, -57.78, 0, 48, -190.48, 72.55, 0.074, 15, -32.29, 83.09, 0.07632, 2, 9.05, 24.46, 0.84967, 5, 5, -326.79, -46.26, 0.00012, 48, -188.39, 84.08, 0.018, 15, -26.09, 73.15, 0.15597, 2, 8.04, 36.13, 0.82568, 3, -135, 74.21, 0.00023, 5, 5, -318.07, -39.23, 0.00048, 48, -179.67, 91.11, 0.042, 15, -27.82, 62.08, 0.24076, 2, 14.6, 45.2, 0.71501, 3, -126.28, 81.24, 0.00175, 5, 5, -314.35, -22.74, 0.00118, 48, -175.95, 107.6, 0.082, 15, -19.49, 47.38, 0.38967, 2, 13.86, 62.09, 0.52224, 3, -122.57, 97.72, 0.00492, 5, 5, -301.35, -14.97, 0.00239, 48, -162.95, 115.37, 0.008, 15, -23.9, 32.88, 0.61825, 2, 24.37, 73, 0.36083, 3, -109.56, 105.5, 0.01054, 4, 5, -314.41, -7.01, 0.00067, 15, -8.87, 35.77, 0.84989, 2, 9.66, 77.25, 0.14652, 3, -122.63, 113.46, 0.00292, 1, 15, 44.8, 38.19, 1, 1, 15, 97.35, 30.84, 1, 1, 15, 188.61, 50.2, 1, 1, 15, 252.54, 63.23, 1, 2, 15, 344.53, 91.1, 0.53367, 16, 57.09, 127.54, 0.46633, 3, 47, -595.08, 364.34, 0, 15, 412.56, 102.85, 0.05286, 16, 113.95, 166.69, 0.94714, 2, 47, -633.45, 398.42, 0, 16, 160.32, 188.68, 1, 3, 47, -658.52, 453.1, 0, 15, 519.2, 79.78, 0, 16, 220.45, 190.38, 1, 3, 47, -671.05, 492.32, 0, 15, 554.83, 59.17, 1e-05, 16, 261.44, 186.59, 0.99999, 3, 47, -666.11, 524.93, 0, 15, 573.1, 31.71, 1e-05, 16, 289.52, 169.29, 0.99999, 3, 47, -644.25, 547.26, 0, 15, 571.93, 0.48, 1e-05, 16, 301.54, 140.44, 0.99999, 3, 47, -599.48, 540.48, 0, 15, 534.23, -24.6, 1e-05, 16, 277.8, 101.89, 0.99999, 3, 47, -592.85, 487.25, 0, 15, 493.53, 10.35, 1e-05, 16, 226.21, 116.59, 0.99999, 2, 47, -562.15, 443.59, 0, 16, 174.02, 105.4, 1, 2, 15, 401.27, -3.34, 0.0719, 16, 148.15, 65.53, 0.9281, 2, 15, 328.29, -25.6, 0.24321, 16, 91.2, 14.77, 0.75679, 3, 47, -376.8, 351.25, 0, 15, 242.16, -34.2, 0.86407, 16, 16.58, -29.11, 0.13593, 2, 47, -277.33, 271.54, 0, 15, 114.94, -42.05, 1, 2, 47, -245.63, 234.51, 0, 15, 66.57, -35.95, 1, 2, 47, -217.71, 198.62, 0, 15, 21.77, -28.14, 1, 5, 5, -275.49, 30.95, 0.01456, 48, -137.1, 161.29, 0.31134, 47, -199.07, 168.66, 0.25899, 15, -12.17, -18.49, 0.22793, 2, 37.24, 124.1, 0.18718, 5, 5, -254.61, 23.26, 0.05425, 48, -116.21, 153.6, 0.2569, 47, -178.18, 160.97, 0.13434, 15, -32.8, -26.84, 0.05949, 2, 59.41, 122.17, 0.49503, 4, 5, -203.98, 17.85, 0.18456, 48, -65.58, 148.19, 0.20989, 47, -127.55, 155.56, 0.07547, 2, 109.69, 130.25, 0.53009, 5, 5, -139.92, 30.19, 0.23961, 48, -1.52, 160.53, 0.09528, 47, -63.49, 167.9, 0.12933, 2, 168.25, 158.99, 0.09715, 3, 51.87, 150.66, 0.43864, 5, 5, -114.68, 32.59, 0.33199, 48, 23.72, 162.93, 0.09896, 47, -38.25, 170.3, 0.06681, 2, 191.98, 167.94, 0.06131, 3, 77.11, 153.06, 0.44094, 6, 5, -71.75, 32.3, 0.4639, 48, 66.65, 162.64, 0.08557, 47, 4.68, 170.01, 0.1424, 15, -162.09, -156.46, 0.02156, 2, 233.47, 178.94, 0.01616, 3, 120.03, 152.77, 0.27041, 5, 5, -22.76, 38.45, 0.90476, 47, 53.67, 176.16, 0, 15, -194.24, -193.94, 0.00584, 2, 279.13, 197.74, 0.00207, 3, 169.02, 158.91, 0.08733, 3, 5, 12.83, 30.8, 0.99963, 47, 89.25, 168.51, 0, 15, -225.72, -212.2, 0.00037, 2, 5, 24.99, 10.3, 0.99935, 4, 90.5, 103.86, 0.00065, 3, 5, 19.19, -20.49, 0.88895, 3, 210.97, 99.98, 0.03062, 4, 73.26, 77.71, 0.08042, 5, 5, 6.58, -44.02, 0.53885, 47, 83.01, 93.69, 0, 15, -271.39, -152.61, 0.00032, 3, 198.36, 76.45, 0.14038, 4, 52.54, 60.88, 0.32044, 3, 5, 14.95, -63.38, 0.17063, 3, 206.74, 57.09, 0.08231, 4, 52.78, 39.78, 0.74707, 3, 5, 31.72, -53.48, 0.05773, 3, 223.5, 66.99, 0.01687, 4, 72.07, 42.43, 0.92541, 2, 5, 64.76, -44.35, 0.00565, 4, 106.08, 38.09, 0.99435, 2, 47, 184.8, 71.48, 0, 4, 137.85, 1.06, 1, 2, 47, 205.76, 37.21, 0, 4, 143.93, -38.65, 1, 2, 47, 202.57, 16.16, 0, 4, 132.86, -56.83, 1, 3, 47, -662.05, -175.71, 0, 17, 352.29, -38.18, 0.00097, 18, 133.28, -177.96, 0.99903, 3, 47, -600.6, -185.11, 0, 17, 305.05, 2.24, 0, 18, 126.6, -116.16, 1, 3, 48, 239.23, 40.76, 0.3, 47, 177.26, 48.13, 0, 4, 121.87, -17.56, 0.7, 2, 48, 195.52, 30.66, 0.3, 4, 77.65, -9.99, 0.7, 2, 15, 315.15, 7.92, 0.47423, 16, 65.23, 39.71, 0.52577, 3, 47, -374.48, 298.42, 0, 15, 204.93, 3.36, 0.99841, 16, -32.96, -10.58, 0.00159, 2, 17, -10.24, 16.19, 0.9271, 2, -32.3, -101.36, 0.0729, 2, 17, 29.27, 7.78, 0.99991, 2, -65.36, -124.56, 9e-05, 2, 17, 87.54, 7.68, 0.98107, 18, -28.01, 36.94, 0.01893, 2, 17, 194.94, -7.31, 0.15721, 18, 39.89, -47.61, 0.84279, 2, 17, 249.83, 28.54, 0.00078, 18, 104.43, -59.14, 0.99922, 1, 18, 147.09, -54.47, 1, 3, 47, -500.74, -32.25, 0, 17, 139.57, -74.93, 0.99829, 18, -46.78, -58.87, 0.00171, 3, 47, -576.62, -55.33, 0, 17, 216.2, -95.35, 0.61925, 18, -5, -126.29, 0.38075, 3, 47, -618.65, -127.66, 0, 17, 290.07, -56.09, 0.00619, 18, 75.7, -148.34, 0.99381, 3, 47, -660.15, -34.41, 0, 17, 276.12, -157.2, 0.33561, 18, -3.78, -212.38, 0.66439, 2, 47, -481.04, 344.89, 0, 15, 315.06, 40.58, 1, 3, 47, -535.31, 375.88, 0, 15, 376.07, 54.13, 0.00078, 16, 101.21, 107.17, 0.99922, 2, 47, -599.14, 408.62, 0, 16, 156.3, 153.12, 1, 3, 47, -636.27, 491.3, 0, 15, 528.4, 36.54, 1e-05, 16, 246.91, 154.97, 0.99999, 2, 47, -271.4, 211.91, 0, 15, 70.46, -1.89, 1, 4, 5, -279.46, 1.23, 0.00784, 15, -29.21, 6.18, 0.72622, 2, 41.22, 94.38, 0.23164, 3, -87.68, 121.69, 0.03431, 4, 6, 77.46, 121.43, 0.00016, 48, 159.34, 19.61, 0.3, 47, 97.37, 26.98, 0.00056, 4, 40.01, -6.21, 0.69928, 3, 48, -226.86, -28.84, 0.00203, 17, -46.13, 27.73, 0.63661, 2, 0.59, -82.93, 0.36136, 4, 6, -263.82, 77.02, 0.00033, 48, -181.94, -24.8, 0.2177, 17, -86.41, 48, 0.11449, 2, 42.87, -67.24, 0.66748, 5, 6, 27.55, 110.59, 0.00133, 48, 109.43, 8.76, 0.3, 47, 47.46, 16.13, 0.00617, 3, 162.82, -1.11, 0.62615, 4, -10.21, 3.08, 0.06634, 2, 48, 54.34, -0.77, 0.31583, 3, 107.72, -10.65, 0.68417, 2, 48, -2.79, -4.04, 0.296, 3, 50.59, -13.91, 0.704, 5, 5, -192.44, -118.52, 0.00028, 48, -54.04, 11.81, 0.3, 15, -174.14, 36.33, 0.00042, 2, 156.65, 1.7, 0.37042, 3, -0.66, 1.94, 0.32888, 5, 6, -185.57, 120.73, 0.00016, 48, -103.68, 18.91, 0.188, 47, -165.65, 26.28, 3e-05, 17, -175.96, 52.16, 0.00076, 2, 106.89, -4.49, 0.81106, 3, 48, -156.35, 36.68, 0.188, 17, -140.6, 9.28, 0.00089, 2, 51.41, -1.18, 0.81111, 4, 48, -193.08, 44.38, 0.3, 47, -255.05, 51.75, 0, 17, -113.46, -16.64, 0.00311, 2, 13.95, -3.41, 0.69689, 6, 5, -22.66, -56.93, 0.17924, 48, 115.73, 73.41, 0.2316, 47, 53.76, 80.78, 0, 15, -258.42, -123.39, 0.00175, 3, 169.12, 63.54, 0.31896, 4, 20.58, 60.27, 0.26844, 7, 5, -69.27, -63.58, 0.07723, 48, 69.12, 66.76, 0.3744, 47, 7.15, 74.13, 0, 15, -228.39, -87.14, 0.0065, 2, 261.05, 87.08, 0.00237, 3, 122.51, 56.88, 0.50639, 4, -24.98, 72.14, 0.0331, 5, 48, 20.51, 67.26, 0.42543, 47, -41.46, 74.63, 0, 15, -192.06, -54.83, 0.01843, 2, 214.02, 74.79, 0.02526, 3, 73.9, 57.39, 0.53088, 4, 5, -166.72, -54.99, 0.07193, 48, -28.32, 75.35, 0.21255, 2, 164.78, 69.76, 0.18717, 3, 25.06, 65.48, 0.52835, 5, 5, -234.15, -37.33, 0.02558, 48, -95.75, 93, 0.2, 15, -88.68, 4.27, 0.20494, 2, 95.07, 69.08, 0.43259, 3, -42.37, 83.13, 0.13689, 5, 5, -275.54, -19.36, 0.00744, 48, -137.14, 110.98, 0.2, 15, -45.96, 18.78, 0.4155, 2, 50.42, 75.55, 0.34261, 3, -83.75, 101.11, 0.03445, 4, 6, 7.86, 49.7, 0.50976, 48, 89.74, -52.12, 0.34207, 3, 143.13, -61.99, 0.11338, 4, -51.9, -45.47, 0.0348, 3, 6, -46.61, 37.7, 0.54123, 48, 35.27, -64.12, 0.42387, 2, 262.78, -48.1, 0.0349, 3, 48, -24.89, -63.11, 0.38882, 2, 204.47, -62.93, 0.46342, 3, 28.5, -72.98, 0.14776, 5, 6, -156.86, 61.61, 0.04026, 48, -74.98, -40.22, 0.201, 47, -136.95, -32.84, 0.0317, 2, 150.12, -54, 0.63348, 3, -21.6, -50.09, 0.09355, 6, 6, -210.87, 67.54, 0.00834, 48, -128.99, -34.28, 0.096, 47, -190.96, -26.91, 0.0035, 17, -126.39, 83.99, 0.04674, 2, 96.45, -62.47, 0.84471, 3, -75.61, -44.15, 0.00071, 4, 6, -255.2, 86.86, 0.00034, 48, -173.32, -14.96, 0.22396, 17, -98.93, 44.19, 0.02797, 2, 48.6, -55.48, 0.74773, 4, 5, -180.31, -13.42, 0.12114, 48, -41.91, 116.92, 0.19537, 2, 140.74, 106.3, 0.244, 3, 11.48, 107.05, 0.4395, 5, 5, -172.99, -83.03, 0.02843, 48, -34.59, 47.31, 0.16472, 47, -96.56, 54.68, 0, 2, 166.09, 41.06, 0.19367, 3, 18.79, 37.44, 0.61318], "hull": 100, "edges": [196, 194, 194, 192, 192, 190, 190, 188, 188, 186, 186, 184, 184, 182, 182, 180, 180, 178, 178, 176, 176, 174, 174, 172, 170, 168, 168, 166, 166, 164, 164, 162, 162, 160, 160, 158, 158, 156, 156, 154, 154, 152, 152, 150, 150, 148, 146, 148, 146, 144, 142, 144, 142, 140, 140, 138, 138, 136, 136, 134, 134, 132, 132, 130, 130, 128, 128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 200, 200, 202, 202, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 196, 198, 0, 198, 64, 66, 204, 206, 208, 210, 30, 32, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 224, 226, 226, 228, 232, 234, 234, 236, 236, 238, 232, 210, 210, 240, 240, 242, 206, 244, 212, 246, 246, 248, 244, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 170, 172, 170, 288, 288, 270, 270, 290, 290, 256, 256, 282, 32, 34], "width": 743, "height": 801}}, "sidai4": {"sidai4": {"type": "mesh", "uvs": [0, 0.90214, 0.0587, 0.72664, 0.13591, 0.56674, 0.22302, 0.39319, 0.35566, 0.24304, 0.52987, 0.12604, 0.7318, 0.04609, 0.89612, 0, 1, 0.01684, 1, 0.14944, 1, 0.28399, 0.90007, 0.36199, 0.74764, 0.47704, 0.63282, 0.54919, 0.5853, 0.43804, 0.69419, 0.28594, 0.54175, 0.35614, 0.3616, 0.45754, 0.24876, 0.60769, 0.1758, 0.66184, 0.12268, 0.76423, 0.10186, 0.89603, 0.10424, 1, 0, 1, 0.82485, 0.20209], "triangles": [24, 9, 10, 15, 6, 24, 11, 24, 10, 15, 24, 11, 12, 15, 11, 14, 15, 12, 13, 14, 12, 7, 8, 9, 24, 6, 7, 24, 7, 9, 15, 5, 6, 16, 5, 15, 4, 5, 16, 17, 4, 16, 3, 4, 17, 18, 3, 17, 2, 3, 18, 19, 2, 18, 1, 2, 19, 20, 1, 19, 20, 0, 1, 21, 0, 20, 22, 23, 0, 22, 0, 21], "vertices": [1, 20, 14.21, 13.07, 1, 1, 20, 37.92, 14.46, 1, 1, 20, 75.42, 11.46, 1, 1, 20, 114, 8.2, 1, 1, 21, 23.52, 10.65, 1, 1, 21, 65.04, 11.8, 1, 2, 21, 107.15, 3.76, 0.11774, 22, 2.35, 6.43, 0.88226, 2, 22, 34.01, 17.85, 0.99963, 23, 11.91, -38.39, 0.00037, 2, 22, 54.66, 15.9, 0.99662, 23, -2.68, -23.65, 0.00338, 2, 22, 109.16, 0.77, 0.25987, 23, -34.78, 22.91, 0.74013, 1, 23, -12.41, 37.93, 1, 1, 23, 12.92, 39.33, 1, 1, 1, 152.24, 193.63, 1, 1, 23, 36.02, 78.94, 1, 1, 23, 32.29, 59.75, 1, 3, 21, 116.98, -49.48, 0.3363, 22, 42.01, -30.41, 0.02145, 23, 36.73, 3.77, 0.64225, 3, 20, 143.88, -47.52, 0.00177, 21, 42.14, -28.19, 0.96981, 23, 110.24, -21.72, 0.02842, 2, 20, 112.02, -21.92, 0.64522, 21, 1.32, -26.08, 0.35478, 1, 20, 75.94, -12.22, 1, 1, 20, 59.36, -1.95, 1, 1, 20, 34.66, 1.5, 1, 1, 20, 13.76, -4.3, 1, 1, 20, -28.57, -19.19, 1, 1, 20, -17.21, 2.4, 1, 3, 21, 134.37, -43.12, 0.05007, 22, 52.16, -14.93, 0.28208, 23, 19.01, -1.6, 0.66785], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 44, 46, 0, 46, 40, 42, 42, 44, 40, 38, 38, 36], "width": 197, "height": 200}}, "heye_you_da2": {"heye_you_da": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.2261, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.75, 0.66667, 0.5, 0.66667, 0.25, 0.66667, 0.75, 0.33333, 0.5, 0.33333, 0.25, 0.33333], "triangles": [1, 13, 0, 2, 14, 1, 1, 14, 13, 2, 15, 14, 14, 12, 13, 15, 17, 14, 14, 17, 12, 17, 11, 12, 5, 19, 16, 16, 19, 18, 5, 6, 19, 6, 8, 19, 19, 8, 9, 6, 7, 8, 16, 18, 15, 15, 18, 17, 18, 10, 17, 17, 10, 11, 19, 9, 18, 18, 9, 10, 3, 16, 2, 16, 15, 2, 16, 3, 5, 3, 4, 5], "vertices": [4, 120, 31.38, -188.56, 0.13201, 121, 0.65, -195.17, 4e-05, 122, -206.47, -103.52, 5e-05, 123, 97.1, -142.23, 0.8679, 4, 120, 29.57, -98.7, 0.28088, 121, -24.57, -108.91, 0.04902, 122, -149.33, -34.16, 7e-05, 123, 11.31, -115.44, 0.67002, 4, 120, 18.4, -36.19, 0.79896, 121, -51.7, -51.5, 0.04578, 122, -117.1, 20.56, 0.00011, 123, -51.52, -106.21, 0.15514, 4, 120, 4.79, 33.57, 0.93105, 121, -83.07, 12.29, 0.00019, 122, -82, 82.36, 0.06758, 123, -121.99, -96.99, 0.00118, 4, 120, -3.96, 88.83, 0.79264, 121, -105.96, 63.33, 3e-05, 122, -52.65, 129.99, 0.20627, 123, -177.17, -87.76, 0.00106, 3, 120, 59.51, 142.42, 0.44575, 122, 30.42, 129.33, 0.55387, 123, -207.86, -10.57, 0.00038, 3, 120, 131.61, 144.9, 0.03655, 122, 86.74, 84.26, 0.96343, 123, -187.35, 58.59, 3e-05, 1, 122, 150.7, 10.23, 1, 2, 121, 134.55, 106.74, 0.18141, 122, 118.48, -44.49, 0.81859, 2, 121, 161.68, 49.32, 0.7702, 122, 86.25, -99.21, 0.2298, 3, 121, 188.81, -8.09, 0.85268, 122, 54.03, -153.92, 0.00281, 123, 45.6, 118.06, 0.14451, 2, 121, 223.78, -98.55, 0.63563, 123, 140.28, 97.06, 0.36437, 2, 121, 118.79, -165.71, 0.1902, 123, 138.03, -27.56, 0.8098, 3, 120, 165.34, -179.29, 0.03614, 122, -98.78, -183.73, 2e-05, 123, 130.8, -12.24, 0.96384, 4, 120, 107.01, -84.85, 0.05955, 121, 46.55, -75.3, 0.03495, 122, -81.54, -74.08, 2e-05, 123, 22.74, -37.61, 0.90548, 4, 120, 95.83, -22.34, 0.05174, 121, 19.43, -17.89, 0.79801, 122, -49.32, -19.36, 0, 123, -40.09, -28.38, 0.15025, 4, 120, 70.61, 53.62, 0.45766, 121, -24.77, 48.85, 0.05347, 122, -18.99, 54.71, 0.48874, 123, -120.13, -28.21, 0.00012, 2, 121, 117.68, -41.7, 0.45817, 123, 34.17, 40.22, 0.54183, 2, 121, 90.55, 15.72, 0.88452, 122, 18.47, -59.28, 0.11548, 2, 121, 44.36, 82.07, 0.0172, 122, 47.35, 16.23, 0.9828], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 254, "height": 236}}}}], "animations": {"animation1": {"bones": {"bone61": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -28.38, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 0.998, "y": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone60": {"translate": [{"x": 14.92, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1}, {"time": 3.6667, "x": 44.44, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 14.92}], "scale": [{"x": 0.979, "y": 0.915, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1}, {"time": 3.6667, "x": 0.937, "y": 0.746, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.979, "y": 0.915}]}, "bone50": {"rotate": [{"angle": -0.2, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.2, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.2}]}, "bone49": {"rotate": [{"angle": -0.2, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.2, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.2}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone46": {"rotate": [{"angle": -0.4, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.4, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.4}]}, "bone45": {"rotate": [{"angle": -0.4, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.4, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.4}], "translate": [{"x": 0.04, "y": -1.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "x": 0.11, "y": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 0.04, "y": -1.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "x": 0.11, "y": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 0.04, "y": -1.38}]}, "bone31": {"rotate": [{"angle": -2.08, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -2.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.08, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -2.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -2.08}]}, "bone30": {"rotate": [{"angle": -1.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -2.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.28}]}, "bone29": {"rotate": [{"angle": -0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.56, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -2.56, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.47}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.56, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.56, "y": -0.01}, {"time": 2.6667}]}, "bone18": {"rotate": [{"angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -9.9, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -9.9, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 5.3333, "angle": -0.82}]}, "bone17": {"rotate": [{"angle": -0.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.17, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -0.17, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.03}]}, "bone16": {"rotate": [{"angle": -3.14, "curve": 0.298, "c2": 0.2, "c3": 0.641, "c4": 0.57}, {"time": 0.3333, "angle": -2.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -3.33, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -3.14, "curve": 0.298, "c2": 0.2, "c3": 0.641, "c4": 0.57}, {"time": 3, "angle": -2.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -3.33, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "angle": -3.14}]}, "bone15": {"rotate": [{"angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -3.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.62}]}, "bone55": {"translate": [{"x": 3.26, "y": 0.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.84, "y": 0.3, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "x": 3.94, "y": 1.11, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 1.6667, "x": 9.54, "y": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.84, "y": 0.3, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 3.4333, "x": 3.94, "y": 1.11, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 4.3333, "x": 9.54, "y": 3.26, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 3.26, "y": 0.85}]}, "bone12": {"rotate": [{"angle": -3.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -7.24, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -1.66, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -3.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -7.24, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": -1.66, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -3.53}]}, "bone11": {"rotate": [{"angle": 4.49, "curve": 0.344, "c2": 0.37, "c3": 0.683, "c4": 0.72}, {"time": 0.6667, "angle": 3.5, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2, "angle": 5.56, "curve": 0.328, "c2": 0.32, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 4.49, "curve": 0.344, "c2": 0.37, "c3": 0.683, "c4": 0.72}, {"time": 3.3333, "angle": 3.5, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4.6667, "angle": 5.56, "curve": 0.328, "c2": 0.32, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 4.49}]}, "bone10": {"rotate": [{"angle": 0.13, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "angle": -0.35, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 1.6667, "angle": 1.71, "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 2.6667, "angle": 0.13, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 3, "angle": -0.35, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 4.3333, "angle": 1.71, "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 5.3333, "angle": 0.13}]}, "bone9": {"rotate": [{"angle": 0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.8333, "angle": -0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.1667, "angle": 0.75, "curve": 0.344, "c2": 0.4, "c3": 0.677, "c4": 0.74}, {"time": 2.6667, "angle": 0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 3.5, "angle": -0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.8333, "angle": 0.75, "curve": 0.344, "c2": 0.4, "c3": 0.677, "c4": 0.74}, {"time": 5.3333, "angle": 0.25}]}, "bone8": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.024, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 1.024, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone6": {"translate": [{"x": 2.44, "y": -0.83, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 6.5, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 2.44, "y": -0.83, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 6.5, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 2.44, "y": -0.83}]}, "bone5": {"translate": [{"x": 1.41, "y": 0.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.19, "y": 0.21, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.41, "y": 0.77, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.19, "y": 0.21, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 1.41, "y": 0.77}]}, "bone53": {"scale": [{"x": 0.963, "y": 0.972, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.926, "y": 0.944, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.963, "y": 0.972, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 0.926, "y": 0.944, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.963, "y": 0.972}]}, "bone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -3.65, "y": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -3.65, "y": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone43": {"translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": -2.08, "y": 0.31, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -2.08, "y": 0.31, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 5.3333}]}, "bone41": {"rotate": [{"angle": -2.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -11.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.17}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.7, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone39": {"rotate": [{"angle": -11.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -18.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -11.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -18.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -11.44}]}, "bone38": {"rotate": [{"angle": -2.12, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 0.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.12, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.3333, "angle": 0.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.12}]}, "bone37": {"rotate": [{"angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.94}]}, "bone36": {"rotate": [{"angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.45}]}, "bone35": {"rotate": [{"angle": -1.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 3.21, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.3333, "angle": 3.21, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.61}]}, "bone34": {"rotate": [{"angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.94}]}, "bone33": {"rotate": [{"angle": 12.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 5.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 19.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 12.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 5.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 19.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 12.75}]}, "bone32": {"rotate": [{"angle": 2.08, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.29, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.08, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 11.29, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.08}]}, "bone4": {"rotate": [{"angle": -0.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -0.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -1.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -0.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -1.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.65}]}, "bone3": {"rotate": [{"angle": 0.19}], "translate": [{"x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "x": 0.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "x": 0.48}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.58, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 0.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.02, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone14": {"translate": [{"x": 8.57, "y": 3.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 17.13, "y": 6.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 8.57, "y": 3.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 17.13, "y": 6.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 8.57, "y": 3.27}]}, "bone20": {"rotate": [{"angle": -8.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -10.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -8.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -10.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -8.95}], "translate": [{"x": 2.5, "y": 16.56, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 3.06, "y": 20.31, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 2.5, "y": 16.56, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 3.06, "y": 20.31, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 2.5, "y": 16.56}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone26": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone25": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone24": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone62": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone63": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone64": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone65": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone66": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone67": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone68": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone69": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone70": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone71": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone72": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone73": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone74": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone75": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone76": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone77": {"translate": [{"x": 25.74, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": 19.42, "y": 3.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "x": 32.06, "y": 3.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 25.74, "y": 3.1}]}, "bone79": {"translate": [{"x": 25.74, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": 19.42, "y": 3.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "x": 32.06, "y": 3.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 25.74, "y": 3.1}]}, "bone80": {"translate": [{"x": 25.74, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": 19.42, "y": 3.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "x": 32.06, "y": 3.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 25.74, "y": 3.1}]}, "bone81": {"translate": [{"x": 25.74, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": 19.42, "y": 3.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "x": 32.06, "y": 3.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 25.74, "y": 3.1}]}, "bone82": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone83": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone84": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone85": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone86": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone87": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone88": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone89": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone90": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone91": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone92": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333}, {"time": 5, "x": 38.83, "y": 6.34, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone93": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 38.83, "y": 6.34, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone94": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333}, {"time": 5, "x": 38.83, "y": 6.34, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone95": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333}, {"time": 5, "x": 38.83, "y": 6.34, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone96": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone97": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone98": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone99": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone100": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone101": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone102": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone103": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone104": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone105": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone106": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone107": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone108": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone109": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone110": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone111": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone78": {"translate": [{"time": 4, "x": 12.65, "y": -0.14}]}, "bone112": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone113": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone118": {"rotate": [{"angle": 0.33, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.2, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "angle": 0.33}]}, "bone119": {"rotate": [{"angle": 0.96, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 5.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.96}]}, "bone114": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone115": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone116": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone117": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone121": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone122": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone123": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone120": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -10.33, "y": -1.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}}, "deform": {"default": {"lian": {"lian": [{"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 444, "vertices": [-0.67892, -0.17059, -0.67906, -0.17069, -6.43485, -1.96715, -6.43494, -1.96733, -11.05316, -3.80823, -11.05341, -3.80842, -12.43325, -2.53502, -12.43355, -2.53525, -8.43793, -1.7605, -8.43808, -1.7606, -2.55099, -0.71112, -2.55104, -0.71114, 0.78929, 0.75835, 0.78932, 0.75832, 2.00531, 2.60387, 2.00534, 2.6038, 3.12767, 0.36618, 3.12769, 0.36613, 2.63403, 0.10217, 2.63403, 0.10211, -4.36057, -1.86584, -4.36067, -1.8659, -4.37663, -2.0799, -4.37694, -2.07995, -3.86836, -0.48227, -3.86842, -0.48232, -2.97089, -2.32085, -2.97127, -2.32099, -8.55322, -4.12932, -8.5536, -4.1293, -10.35976, -3.64629, -10.35979, -3.64648, -7.44794, -2.54274, -7.44824, -2.54292, -2.81625, 0.19339, -2.81625, 0.19325, 1.20577, 1.55923, 1.20529, 1.55914, 4.77577, 2.85524, 4.7749, 2.85504, 6.93902, -0.12527, 6.93898, -0.12547, 5.80381, -0.79214, 5.80344, -0.79221, 2.8417, -1.97972, 2.84154, -1.97985, -2.14988, -1.599, -2.14999, -1.59907, -2.2843, -1.16907, -2.28426, -1.16909, 0, 0, 0, 0, -2.26996, -0.36636, -2.27022, -0.36657, -8.67959, -1.31534, -8.67973, -1.31533, -14.12794, -4.10953, -14.12827, -4.1094, -12.8199, -6.19833, -12.82013, -6.19843, -8.72681, -5.98328, -8.72716, -5.98328, -4.35521, -4.6972, -4.35593, -4.69724, -3.63189, -2.50238, -3.63222, -2.50239, -0.71156, -1.18935, -0.71165, -1.18938, -0.71152, -1.18935, 0.94363, 0.78253, 0.94362, 0.78253, 0.9437, 0.78255, 1.0723, 0.4679, 1.07231, 0.46782, 2.59895, 1.29774, 2.59894, 1.29771, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.75708, -0.19029, -0.75714, -0.19031, -2.12959, -0.33397, -2.12967, -0.33404, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.40179, -0.40285, -0.40182, -0.40286, -1.37247, -0.14369, -1.37256, -0.14375, -2.84113, 0.89577, -2.8412, 0.89574, -1.58604, 0.30566, -1.58611, 0.30565, -1.84517, -0.66498, -1.84525, -0.66501, -2.7682, -0.59512, -2.76834, -0.59518, -1.11237, 0.02226, -1.11241, 0.02225, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.75097, 0.68323, -6.75099, 0.68296, -12.71889, -5.92604, -12.71936, -5.92673, -13.7132, -0.85664, -13.71336, -0.85702, -10.04135, -1.26375, -10.04134, -1.26386, -3.11203, 2.0146, -3.1123, 2.01454, 2.16695, 2.05382, 2.16676, 2.05378, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.1324, 0.01132, -4.13243, 0.01125, -2.1285, -1.13869, -2.12863, -1.13873, -1.30058, -0.82997, -1.3007, -0.82999, -0.63866, -0.26114, -0.63872, -0.26114], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "bu_jianbang_you": {"bu_jianbang_you": [{"offset": 1, "vertices": [2.992, 0, 2.992, 0, 2.992, 0, 2.992, 0.74797, 1.12199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.992, 0, 2.992], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [2.685, 5.14, 2.327, 6.214, 1.43202, 7.646, 3.043, 6.57201, 3.43297, 3.26999, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 4.53099, 1.72199, 3.11099, -0.124, 2.698, -0.426, 1.57402, 2.49401, 1.07401, -1.611, 1.43199, 2.14799, 1.61101, 2.32699, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 2.685, 5.14, 2.685, 5.14], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 1, "vertices": [2.992, 0, 2.992, 0, 2.992, 0, 2.992, 0.74797, 1.12199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.992, 0, 2.992], "curve": 0.25, "c3": 0.75}, {"time": 4, "vertices": [2.685, 5.14, 2.327, 6.214, 1.43202, 7.646, 3.043, 6.57201, 3.43297, 3.26999, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 4.53099, 1.72199, 3.11099, -0.124, 2.698, -0.426, 1.57402, 2.49401, 1.07401, -1.611, 1.43199, 2.14799, 1.61101, 2.32699, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 2.685, 5.14, 2.685, 5.14], "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "offset": 1, "vertices": [2.992, 0, 2.992, 0, 2.992, 0, 2.992, 0.74797, 1.12199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.992, 0, 2.992]}]}, "shou__zuo": {"shou__zuo": [{"offset": 18, "vertices": [-1.4407, 0.21663, -0.68179, 1.00688]}, {"time": 0.6667, "offset": 18, "vertices": [-0.26254, 0.43779, -0.18908, -0.38446]}, {"time": 2, "offset": 18, "vertices": [-0.75012, 1.25082, -3.17876, 3.29581]}, {"time": 2.6667, "offset": 18, "vertices": [-1.4407, 0.21663, -0.68179, 1.00688]}, {"time": 3.3333, "offset": 18, "vertices": [1.29601, 1.07741, 0.12103, -0.26483]}, {"time": 4, "offset": 18, "vertices": [0.45691, 1.31064, -1.62642, 1.12427]}, {"time": 4.6667, "offset": 18, "vertices": [-0.36504, 0.422, -3.33399, 2.89885]}, {"time": 5.3333, "offset": 18, "vertices": [-1.4407, 0.21663, -0.68179, 1.00688]}]}, "sidai4": {"sidai4": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 32, "vertices": [-4.48474, -1.16324, -4.49246, 1.13171, -2.21533, -0.15994], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "offset": 32, "vertices": [-4.48474, -1.16324, -4.49246, 1.13171, -2.21533, -0.15994], "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bu_jianbang_zuo": {"bu_jianbang_zuo": [{"offset": 2, "vertices": [-0.906, 0.75499, -1.05701, 1.35901, -0.87199, 2.39798, 0.10901, 1.526, 0.981, 1.41699, -0.453, 2.114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.409, 5.03699, -2.62801, 2.628, -2.62799, -1.533], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [4.65799, 3.836, 0, 0.82201, 0.137, 1.78101, 0.791, 4.181, -2.74999, 5.205, 1.243, 7.87598, 2.59218, 4.79483, 0, 0, 2.192, 0.411, 0.82199, -0.548, 1.09599, -1.37, 1.096, -0.41099, 1.507, -0.13701, 2.74, 5.89097, 2.73999, 8.90497, 3.83599, 0.411, 2.32899, 3.97298, 0.27399, 6.30198, 0.27399, 6.30198, 1.781, 6.43897, 5.20599, 0.685, 4.247, 2.466, 3.97298, 3.15099, 0, 0, 0, 0, 0, 0, 0.25299, 5.313], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "vertices": [2.32899, 1.918, -0.453, 0.7885, -0.46, 1.57001, -0.0405, 3.28949, -1.32049, 3.3655, 1.112, 4.64648, 1.06959, 3.45441, 0, 0, 1.096, 0.2055, 0.411, -0.274, 0.54799, -0.685, 0.548, -0.20549, 0.7535, -0.0685, 1.37, 2.94548, 1.37, 4.45248, 1.91799, 0.2055, 1.1645, 1.98649, -3.59751, 13.58946, -2.05701, 7.98498, -0.4235, 2.45298, 2.603, 0.3425, 2.1235, 1.233, 1.98649, 1.57549, 0, 0, 0, 0, 0, 0, 0.1265, 2.6565], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "offset": 2, "vertices": [-0.906, 0.75499, -1.05701, 1.35901, -0.87199, 2.39798, 0.10901, 1.526, 0.981, 1.41699, -0.453, 2.114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.409, 5.03699, -2.62801, 2.628, -2.62799, -1.533], "curve": 0.25, "c3": 0.75}, {"time": 4, "vertices": [4.65799, 3.836, 0, 0.82201, 0.137, 1.78101, 0.791, 4.181, -2.74999, 5.205, 1.243, 7.87598, 2.59218, 4.79483, 0, 0, 2.192, 0.411, 0.82199, -0.548, 1.09599, -1.37, 1.096, -0.41099, 1.507, -0.13701, 2.74, 5.89097, 2.73999, 8.90497, 3.83599, 0.411, 2.32899, 3.97298, 0.27399, 6.30198, 0.27399, 6.30198, 1.781, 6.43897, 5.20599, 0.685, 4.247, 2.466, 3.97298, 3.15099, 0, 0, 0, 0, 0, 0, 0.25299, 5.313], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "vertices": [2.32899, 1.918, -0.453, 0.7885, -0.46, 1.57001, -0.0405, 3.28949, -1.32049, 3.3655, 1.112, 4.64648, 1.06959, 3.45441, 0, 0, 1.096, 0.2055, 0.411, -0.274, 0.54799, -0.685, 0.548, -0.20549, 0.7535, -0.0685, 1.37, 2.94548, 1.37, 4.45248, 1.91799, 0.2055, 1.1645, 1.98649, -3.59751, 13.58946, -2.05701, 7.98498, -0.4235, 2.45298, 2.603, 0.3425, 2.1235, 1.233, 1.98649, 1.57549, 0, 0, 0, 0, 0, 0, 0.1265, 2.6565], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "offset": 2, "vertices": [-0.906, 0.75499, -1.05701, 1.35901, -0.87199, 2.39798, 0.10901, 1.526, 0.981, 1.41699, -0.453, 2.114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.409, 5.03699, -2.62801, 2.628, -2.62799, -1.533]}]}}}}, "animation2": {"slots": {"bg": {"attachment": [{"name": null}]}, "heye_you_da2": {"attachment": [{"name": null}]}, "heye_shang": {"attachment": [{"name": null}]}}, "bones": {"bone61": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -28.38, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 0.998, "y": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone60": {"translate": [{"x": 14.92, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1}, {"time": 3.6667, "x": 44.44, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 14.92}], "scale": [{"x": 0.979, "y": 0.915, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1}, {"time": 3.6667, "x": 0.937, "y": 0.746, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.979, "y": 0.915}]}, "bone50": {"rotate": [{"angle": -0.2, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.2, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.2}]}, "bone49": {"rotate": [{"angle": -0.2, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.2, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.2}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone46": {"rotate": [{"angle": -0.4, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.4, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.4}]}, "bone45": {"rotate": [{"angle": -0.4, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -0.4, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -0.4}], "translate": [{"x": 0.04, "y": -1.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "x": 0.11, "y": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 0.04, "y": -1.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "x": 0.11, "y": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 0.04, "y": -1.38}]}, "bone31": {"rotate": [{"angle": -2.08, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -2.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.08, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -2.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -2.08}]}, "bone30": {"rotate": [{"angle": -1.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -2.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.28}]}, "bone29": {"rotate": [{"angle": -0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.56, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -2.56, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.47}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.56, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.56, "y": -0.01}, {"time": 2.6667}]}, "bone18": {"rotate": [{"angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -9.9, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -9.9, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 5.3333, "angle": -0.82}]}, "bone17": {"rotate": [{"angle": -0.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.17, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -0.17, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.03}]}, "bone16": {"rotate": [{"angle": -3.14, "curve": 0.298, "c2": 0.2, "c3": 0.641, "c4": 0.57}, {"time": 0.3333, "angle": -2.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -3.33, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -3.14, "curve": 0.298, "c2": 0.2, "c3": 0.641, "c4": 0.57}, {"time": 3, "angle": -2.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -3.33, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "angle": -3.14}]}, "bone15": {"rotate": [{"angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -3.33, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.62}]}, "bone55": {"translate": [{"x": 3.26, "y": 0.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.84, "y": 0.3, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "x": 3.94, "y": 1.11, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 1.6667, "x": 9.54, "y": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.84, "y": 0.3, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 3.4333, "x": 3.94, "y": 1.11, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 4.3333, "x": 9.54, "y": 3.26, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 3.26, "y": 0.85}]}, "bone12": {"rotate": [{"angle": -3.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -7.24, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -1.66, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -3.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -7.24, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": -1.66, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -3.53}]}, "bone11": {"rotate": [{"angle": 4.49, "curve": 0.344, "c2": 0.37, "c3": 0.683, "c4": 0.72}, {"time": 0.6667, "angle": 3.5, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2, "angle": 5.56, "curve": 0.328, "c2": 0.32, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 4.49, "curve": 0.344, "c2": 0.37, "c3": 0.683, "c4": 0.72}, {"time": 3.3333, "angle": 3.5, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4.6667, "angle": 5.56, "curve": 0.328, "c2": 0.32, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 4.49}]}, "bone10": {"rotate": [{"angle": 0.13, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "angle": -0.35, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 1.6667, "angle": 1.71, "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 2.6667, "angle": 0.13, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 3, "angle": -0.35, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 4.3333, "angle": 1.71, "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 5.3333, "angle": 0.13}]}, "bone9": {"rotate": [{"angle": 0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.8333, "angle": -0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.1667, "angle": 0.75, "curve": 0.344, "c2": 0.4, "c3": 0.677, "c4": 0.74}, {"time": 2.6667, "angle": 0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 3.5, "angle": -0.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.8333, "angle": 0.75, "curve": 0.344, "c2": 0.4, "c3": 0.677, "c4": 0.74}, {"time": 5.3333, "angle": 0.25}]}, "bone8": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.024, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 1.024, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone6": {"translate": [{"x": 2.44, "y": -0.83, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 6.5, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 2.44, "y": -0.83, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 6.5, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 2.44, "y": -0.83}]}, "bone5": {"translate": [{"x": 1.41, "y": 0.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.19, "y": 0.21, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.41, "y": 0.77, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.19, "y": 0.21, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 1.41, "y": 0.77}]}, "bone53": {"scale": [{"x": 0.963, "y": 0.972, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.926, "y": 0.944, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.963, "y": 0.972, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 0.926, "y": 0.944, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.963, "y": 0.972}]}, "bone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -3.65, "y": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -3.65, "y": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone43": {"translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": -2.08, "y": 0.31, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -2.08, "y": 0.31, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 5.3333}]}, "bone41": {"rotate": [{"angle": -2.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -11.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.17}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.7, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone39": {"rotate": [{"angle": -11.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -18.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -11.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -18.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -11.44}]}, "bone38": {"rotate": [{"angle": -2.12, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 0.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.12, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.3333, "angle": 0.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.12}]}, "bone37": {"rotate": [{"angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.94}]}, "bone36": {"rotate": [{"angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.45}]}, "bone35": {"rotate": [{"angle": -1.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 3.21, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.3333, "angle": 3.21, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.61}]}, "bone34": {"rotate": [{"angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.94}]}, "bone33": {"rotate": [{"angle": 12.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 5.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 19.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 12.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 5.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 19.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 12.75}]}, "bone32": {"rotate": [{"angle": 2.08, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.29, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.08, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 11.29, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.08}]}, "bone4": {"rotate": [{"angle": -0.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -0.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -1.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -0.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -1.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.65}]}, "bone3": {"rotate": [{"angle": 0.19}], "translate": [{"x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "x": 0.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "x": 0.48}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.58, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 0.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.02, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone14": {"translate": [{"x": 8.57, "y": 3.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 17.13, "y": 6.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 8.57, "y": 3.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 17.13, "y": 6.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 8.57, "y": 3.27}]}, "bone20": {"rotate": [{"angle": -8.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -10.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -8.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -10.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -8.95}], "translate": [{"x": 2.5, "y": 16.56, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 3.06, "y": 20.31, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 2.5, "y": 16.56, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 3.06, "y": 20.31, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 2.5, "y": 16.56}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone26": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone25": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone24": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 12.65, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone62": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone63": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone64": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone65": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone66": {"translate": [{"x": 3.23, "y": 0.39, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 2.44, "y": 0.4, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "x": 15.08, "y": 0.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 3.23, "y": 0.39}]}, "bone67": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone68": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone69": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone70": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone71": {"translate": [{"x": 9.5, "y": 1.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 7.16, "y": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "x": 19.81, "y": 1.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 9.5, "y": 1.14}]}, "bone72": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone73": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone74": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone75": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone76": {"translate": [{"x": 17.29, "y": 2.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "x": 13.04, "y": 2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6667, "x": 25.69, "y": 1.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 17.29, "y": 2.08}]}, "bone77": {"translate": [{"x": 25.74, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": 19.42, "y": 3.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "x": 32.06, "y": 3.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 25.74, "y": 3.1}]}, "bone79": {"translate": [{"x": 25.74, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": 19.42, "y": 3.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "x": 32.06, "y": 3.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 25.74, "y": 3.1}]}, "bone80": {"translate": [{"x": 25.74, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": 19.42, "y": 3.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "x": 32.06, "y": 3.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 25.74, "y": 3.1}]}, "bone81": {"translate": [{"x": 25.74, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": 19.42, "y": 3.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "x": 32.06, "y": 3.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 25.74, "y": 3.1}]}, "bone82": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone83": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone84": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone85": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone86": {"translate": [{"x": 34.19, "y": 4.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "x": 25.79, "y": 4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 4.3333, "x": 38.44, "y": 4.07, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 34.19, "y": 4.12}]}, "bone87": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone88": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone89": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone90": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone91": {"translate": [{"x": 41.98, "y": 5.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2, "x": 31.67, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6667, "x": 44.31, "y": 5.03, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 41.98, "y": 5.06}]}, "bone92": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333}, {"time": 5, "x": 38.83, "y": 6.34, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone93": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 38.83, "y": 6.34, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone94": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333}, {"time": 5, "x": 38.83, "y": 6.34, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone95": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333}, {"time": 5, "x": 38.83, "y": 6.34, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone96": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone97": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone98": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone99": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone100": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone101": {"translate": [{"x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 38.83, "y": 6.34}]}, "bone102": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone103": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone104": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone105": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone106": {"translate": [{"x": 36.39, "y": 5.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "x": 36.39, "y": 5.94}]}, "bone107": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone108": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone109": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone110": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone111": {"translate": [{"x": 31.67, "y": 5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "x": 38.83, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 31.67, "y": 5.17}]}, "bone78": {"translate": [{"time": 4, "x": 12.65, "y": -0.14}]}, "bone112": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone113": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone118": {"rotate": [{"angle": 0.33, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.2, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "angle": 0.33}]}, "bone119": {"rotate": [{"angle": 0.96, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 5.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.96}]}, "bone114": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone115": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone116": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone117": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone121": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone122": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone123": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}]}, "bone120": {"rotate": [{"angle": 2.46, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 4.3333, "angle": 3.71, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 2.46}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -10.33, "y": -1.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}}, "deform": {"default": {"lian": {"lian": [{"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 444, "vertices": [-0.67892, -0.17059, -0.67906, -0.17069, -6.43485, -1.96715, -6.43494, -1.96733, -11.05316, -3.80823, -11.05341, -3.80842, -12.43325, -2.53502, -12.43355, -2.53525, -8.43793, -1.7605, -8.43808, -1.7606, -2.55099, -0.71112, -2.55104, -0.71114, 0.78929, 0.75835, 0.78932, 0.75832, 2.00531, 2.60387, 2.00534, 2.6038, 3.12767, 0.36618, 3.12769, 0.36613, 2.63403, 0.10217, 2.63403, 0.10211, -4.36057, -1.86584, -4.36067, -1.8659, -4.37663, -2.0799, -4.37694, -2.07995, -3.86836, -0.48227, -3.86842, -0.48232, -2.97089, -2.32085, -2.97127, -2.32099, -8.55322, -4.12932, -8.5536, -4.1293, -10.35976, -3.64629, -10.35979, -3.64648, -7.44794, -2.54274, -7.44824, -2.54292, -2.81625, 0.19339, -2.81625, 0.19325, 1.20577, 1.55923, 1.20529, 1.55914, 4.77577, 2.85524, 4.7749, 2.85504, 6.93902, -0.12527, 6.93898, -0.12547, 5.80381, -0.79214, 5.80344, -0.79221, 2.8417, -1.97972, 2.84154, -1.97985, -2.14988, -1.599, -2.14999, -1.59907, -2.2843, -1.16907, -2.28426, -1.16909, 0, 0, 0, 0, -2.26996, -0.36636, -2.27022, -0.36657, -8.67959, -1.31534, -8.67973, -1.31533, -14.12794, -4.10953, -14.12827, -4.1094, -12.8199, -6.19833, -12.82013, -6.19843, -8.72681, -5.98328, -8.72716, -5.98328, -4.35521, -4.6972, -4.35593, -4.69724, -3.63189, -2.50238, -3.63222, -2.50239, -0.71156, -1.18935, -0.71165, -1.18938, -0.71152, -1.18935, 0.94363, 0.78253, 0.94362, 0.78253, 0.9437, 0.78255, 1.0723, 0.4679, 1.07231, 0.46782, 2.59895, 1.29774, 2.59894, 1.29771, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.75708, -0.19029, -0.75714, -0.19031, -2.12959, -0.33397, -2.12967, -0.33404, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.40179, -0.40285, -0.40182, -0.40286, -1.37247, -0.14369, -1.37256, -0.14375, -2.84113, 0.89577, -2.8412, 0.89574, -1.58604, 0.30566, -1.58611, 0.30565, -1.84517, -0.66498, -1.84525, -0.66501, -2.7682, -0.59512, -2.76834, -0.59518, -1.11237, 0.02226, -1.11241, 0.02225, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.75097, 0.68323, -6.75099, 0.68296, -12.71889, -5.92604, -12.71936, -5.92673, -13.7132, -0.85664, -13.71336, -0.85702, -10.04135, -1.26375, -10.04134, -1.26386, -3.11203, 2.0146, -3.1123, 2.01454, 2.16695, 2.05382, 2.16676, 2.05378, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.1324, 0.01132, -4.13243, 0.01125, -2.1285, -1.13869, -2.12863, -1.13873, -1.30058, -0.82997, -1.3007, -0.82999, -0.63866, -0.26114, -0.63872, -0.26114], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "bu_jianbang_you": {"bu_jianbang_you": [{"offset": 1, "vertices": [2.992, 0, 2.992, 0, 2.992, 0, 2.992, 0.74797, 1.12199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.992, 0, 2.992], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [2.685, 5.14, 2.327, 6.214, 1.43202, 7.646, 3.043, 6.57201, 3.43297, 3.26999, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 4.53099, 1.72199, 3.11099, -0.124, 2.698, -0.426, 1.57402, 2.49401, 1.07401, -1.611, 1.43199, 2.14799, 1.61101, 2.32699, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 2.685, 5.14, 2.685, 5.14], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 1, "vertices": [2.992, 0, 2.992, 0, 2.992, 0, 2.992, 0.74797, 1.12199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.992, 0, 2.992], "curve": 0.25, "c3": 0.75}, {"time": 4, "vertices": [2.685, 5.14, 2.327, 6.214, 1.43202, 7.646, 3.043, 6.57201, 3.43297, 3.26999, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 4.53099, 1.72199, 3.11099, -0.124, 2.698, -0.426, 1.57402, 2.49401, 1.07401, -1.611, 1.43199, 2.14799, 1.61101, 2.32699, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 2.685, 2.14799, 2.685, 5.14, 2.685, 5.14], "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "offset": 1, "vertices": [2.992, 0, 2.992, 0, 2.992, 0, 2.992, 0.74797, 1.12199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.992, 0, 2.992]}]}, "shou__zuo": {"shou__zuo": [{"offset": 18, "vertices": [-1.4407, 0.21663, -0.68179, 1.00688]}, {"time": 0.6667, "offset": 18, "vertices": [-0.26254, 0.43779, -0.18908, -0.38446]}, {"time": 2, "offset": 18, "vertices": [-0.75012, 1.25082, -3.17876, 3.29581]}, {"time": 2.6667, "offset": 18, "vertices": [-1.4407, 0.21663, -0.68179, 1.00688]}, {"time": 3.3333, "offset": 18, "vertices": [1.29601, 1.07741, 0.12103, -0.26483]}, {"time": 4, "offset": 18, "vertices": [0.45691, 1.31064, -1.62642, 1.12427]}, {"time": 4.6667, "offset": 18, "vertices": [-0.36504, 0.422, -3.33399, 2.89885]}, {"time": 5.3333, "offset": 18, "vertices": [-1.4407, 0.21663, -0.68179, 1.00688]}]}, "sidai4": {"sidai4": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 32, "vertices": [-4.48474, -1.16324, -4.49246, 1.13171, -2.21533, -0.15994], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "offset": 32, "vertices": [-4.48474, -1.16324, -4.49246, 1.13171, -2.21533, -0.15994], "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bu_jianbang_zuo": {"bu_jianbang_zuo": [{"offset": 2, "vertices": [-0.906, 0.75499, -1.05701, 1.35901, -0.87199, 2.39798, 0.10901, 1.526, 0.981, 1.41699, -0.453, 2.114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.409, 5.03699, -2.62801, 2.628, -2.62799, -1.533], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [4.65799, 3.836, 0, 0.82201, 0.137, 1.78101, 0.791, 4.181, -2.74999, 5.205, 1.243, 7.87598, 2.59218, 4.79483, 0, 0, 2.192, 0.411, 0.82199, -0.548, 1.09599, -1.37, 1.096, -0.41099, 1.507, -0.13701, 2.74, 5.89097, 2.73999, 8.90497, 3.83599, 0.411, 2.32899, 3.97298, 0.27399, 6.30198, 0.27399, 6.30198, 1.781, 6.43897, 5.20599, 0.685, 4.247, 2.466, 3.97298, 3.15099, 0, 0, 0, 0, 0, 0, 0.25299, 5.313], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "vertices": [2.32899, 1.918, -0.453, 0.7885, -0.46, 1.57001, -0.0405, 3.28949, -1.32049, 3.3655, 1.112, 4.64648, 1.06959, 3.45441, 0, 0, 1.096, 0.2055, 0.411, -0.274, 0.54799, -0.685, 0.548, -0.20549, 0.7535, -0.0685, 1.37, 2.94548, 1.37, 4.45248, 1.91799, 0.2055, 1.1645, 1.98649, -3.59751, 13.58946, -2.05701, 7.98498, -0.4235, 2.45298, 2.603, 0.3425, 2.1235, 1.233, 1.98649, 1.57549, 0, 0, 0, 0, 0, 0, 0.1265, 2.6565], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "offset": 2, "vertices": [-0.906, 0.75499, -1.05701, 1.35901, -0.87199, 2.39798, 0.10901, 1.526, 0.981, 1.41699, -0.453, 2.114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.409, 5.03699, -2.62801, 2.628, -2.62799, -1.533], "curve": 0.25, "c3": 0.75}, {"time": 4, "vertices": [4.65799, 3.836, 0, 0.82201, 0.137, 1.78101, 0.791, 4.181, -2.74999, 5.205, 1.243, 7.87598, 2.59218, 4.79483, 0, 0, 2.192, 0.411, 0.82199, -0.548, 1.09599, -1.37, 1.096, -0.41099, 1.507, -0.13701, 2.74, 5.89097, 2.73999, 8.90497, 3.83599, 0.411, 2.32899, 3.97298, 0.27399, 6.30198, 0.27399, 6.30198, 1.781, 6.43897, 5.20599, 0.685, 4.247, 2.466, 3.97298, 3.15099, 0, 0, 0, 0, 0, 0, 0.25299, 5.313], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "vertices": [2.32899, 1.918, -0.453, 0.7885, -0.46, 1.57001, -0.0405, 3.28949, -1.32049, 3.3655, 1.112, 4.64648, 1.06959, 3.45441, 0, 0, 1.096, 0.2055, 0.411, -0.274, 0.54799, -0.685, 0.548, -0.20549, 0.7535, -0.0685, 1.37, 2.94548, 1.37, 4.45248, 1.91799, 0.2055, 1.1645, 1.98649, -3.59751, 13.58946, -2.05701, 7.98498, -0.4235, 2.45298, 2.603, 0.3425, 2.1235, 1.233, 1.98649, 1.57549, 0, 0, 0, 0, 0, 0, 0.1265, 2.6565], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "offset": 2, "vertices": [-0.906, 0.75499, -1.05701, 1.35901, -0.87199, 2.39798, 0.10901, 1.526, 0.981, 1.41699, -0.453, 2.114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.409, 5.03699, -2.62801, 2.628, -2.62799, -1.533]}]}}}}}}