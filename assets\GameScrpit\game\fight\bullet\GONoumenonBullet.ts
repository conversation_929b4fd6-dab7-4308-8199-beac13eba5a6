import { _decorator, Details, sp, UITransform, v2, v3, Widget } from "cc";
import { CallBulletDetail } from "../FightDefine";
import { JsonMgr } from "../../mgr/JsonMgr";
import RenderSection from "../../../lib/object/RenderSection";
import { GOBullet } from "./GOBullet";
import { AnimationSection } from "../section/AnimationSection";
import DirectSection, { DIRECT } from "../../../lib/object/DirectSection";
import ToolExt from "../../common/ToolExt";
import { PlayerModule } from "../../../module/player/PlayerModule";
import BulletRenderSection from "../../../lib/object/BulletRenderSection";

const { ccclass, property } = _decorator;

@ccclass("GONoumenonBullet")
export class GONoumenonBullet extends GOBullet {
  /**本体伤害结算 */
  public async onInitDetail(detail: CallBulletDetail) {
    super.onInitDetail(detail);
    this.setPosVec3(this.setBulletStartPos(detail));
    this.hurtRoleMsg();
    this.atkRoleMsg();
    this.resolveBack(true);
    this.remove();
  }

  private setBulletStartPos(detail: CallBulletDetail) {
    let newPos = ToolExt.transferOfAxes(detail.target.getSection(RenderSection).getRender(), this.parent);
    let pos = v2(newPos.x, newPos.y);
    return pos;
  }

  public onEnter(): void {
    super.onEnter();
  }
}
