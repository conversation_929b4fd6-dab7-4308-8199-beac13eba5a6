import { _decorator, Component, Node, UITransform } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ClubRouteItem } from "../../../module/club/ClubRoute";
import TipMgr from "../../../lib/tips/TipMgr";
import { ClubModule } from "../../../module/club/ClubModule";
import { ClubMemberMessage } from "../../net/protocol/Club";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { CLUB_POSITION } from "../../../module/club/ClubConstant";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { LangMgr } from "../../mgr/LangMgr";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIClubConfirmDialog } from "./UIClubConfirmDialog";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("UIClubMembersTop")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_CLUB,
  url: "prefab/ui/UIClubMembersTop",
  nextHop: [],
  exit: "dialog_close",
  transparent: true,
})
export class UIClubMembersTop extends BaseCtrl {
  private _target: Node;
  private _targetParent: Node;
  private _data: ClubMemberMessage;
  start() {
    super.start();
    this.getNode("btn_transfer").active = false;
    this.getNode("btn_appoint").active = false;
    this.getNode("btn_kick").active = false;
    this.getNode("btn_demote").active = false;
    if (ClubModule.data.position == CLUB_POSITION.副盟主) {
      // this.getNode("btn_transfer").active = false;
      // 显示卸任
      this.getNode("btn_kick").active = true;
    } else if (ClubModule.data.position == CLUB_POSITION.盟主) {
      this.getNode("btn_transfer").active = true;
      this.getNode("btn_kick").active = true;
      if (this._data.position == CLUB_POSITION.副盟主) {
        this.getNode("btn_demote").active = true;
      } else {
        this.getNode("btn_appoint").active = true;
      }
    }

    // next frame
    setTimeout(() => {
      this.showTips();
    }, 1);
  }
  private showTips() {
    this.getNode("node_tips").active = true;
    let tipsHeight = this.getNode("node_tips").getComponent(UITransform).height;
    let targetPosition = this._targetParent
      .getComponent(UITransform)
      .convertToNodeSpaceAR(this._target.getWorldPosition());
    let targetHeight = this._target.getComponent(UITransform).height;

    let targetBottom = targetPosition.y - targetHeight / 2;
    let parentBottom = NodeTool.getBorderBottom(this._targetParent);
    if (targetBottom - parentBottom - 10 < tipsHeight / 2) {
      let toWorldY = this._target.getWorldPosition().y + targetHeight / 2 + tipsHeight / 2 + 10;
      this.getNode("node_tips").setWorldPosition(this._target.getWorldPosition().x, toWorldY, 1);
    } else {
      let toWorldY = this._target.getWorldPosition().y - targetHeight / 2 - tipsHeight / 2 - 10;
      this.getNode("node_tips").setWorldPosition(this._target.getWorldPosition().x, toWorldY, 1);
    }
  }
  init(args: RouteShowArgs): void {
    this._target = args.payload.target;
    this._targetParent = args.payload.parent;
    this._data = args.payload.data;
  }

  update(deltaTime: number) {}

  onClickBlank() {
    this.closeBack();
  }
  on_click_btn_transfer() {
    //
    let dialogCallBack = (isOK: boolean) => {
      if (isOK) {
        ClubModule.api.adjustPosition(
          this._data.simpleMessage.userId,
          1,
          () => {
            TipsMgr.showTipX(555);
            UIMgr.instance.back();
          },
          () => {
            return false;
          }
        );
      }
    };
    let content = LangMgr.txMsgCode(556, [`<color=d46b0e>${this._data.simpleMessage.nickname}</color>`]);
    let dialogArgs: RouteShowArgs = {
      payload: { content: content, callback: dialogCallBack },
      onCloseBack: () => {
        this.closeBack();
      },
    };
    RouteManager.uiRouteCtrl.showRoute(UIClubConfirmDialog, dialogArgs);
  }
  on_click_btn_appoint() {
    //
    if (ClubModule.service.getVicePresidentCount() >= 2) {
      TipsMgr.showTipX(557);
      return;
    }
    if (this._data.position == 2) {
      let content = LangMgr.txMsgCode(558, [`<color=d46b0e>${this._data.simpleMessage.nickname}</color>`]);
      let dialogArgs: RouteShowArgs = {
        payload: { content: content },
      };
      RouteManager.uiRouteCtrl.showRoute(UIClubConfirmDialog, dialogArgs);
      return;
    }
    let dialogCallBack = (isOK: boolean) => {
      let userId = this._data?.simpleMessage?.userId;
      if (isOK) {
        ClubModule.api.adjustPosition(
          userId,
          CLUB_POSITION.副盟主,
          () => {
            TipsMgr.showTipX(559);
          },
          () => {
            return false;
          }
        );
      }
    };
    let content = LangMgr.txMsgCode(560, [`<color=d46b0e>${this._data.simpleMessage.nickname}</color>`]);
    let dialogArgs: RouteShowArgs = {
      payload: { content: content, callback: dialogCallBack },
      onCloseBack: () => {
        this.closeBack();
      },
    };
    RouteManager.uiRouteCtrl.showRoute(UIClubConfirmDialog, dialogArgs);
  }
  on_click_btn_kick() {
    //

    let kickoutCallback = (isOK: boolean) => {
      if (isOK) {
        ClubModule.api.kickOutClub(
          this._data.simpleMessage.userId,
          () => {
            TipsMgr.showTipX(561);
          },
          () => {
            return false;
          }
        );
      }
    };
    let content = LangMgr.txMsgCode(562, [`<color=d46b0e>${this._data.simpleMessage.nickname}</color>`]);
    let dialogArgs: RouteShowArgs = {
      payload: { content: content, callback: kickoutCallback },
      onCloseBack: () => {
        this.closeBack();
      },
    };
    RouteManager.uiRouteCtrl.showRoute(UIClubConfirmDialog, dialogArgs);
  }
  on_click_btn_demote() {
    let demoteCallback = (isOK: boolean) => {
      if (isOK) {
        ClubModule.api.adjustPosition(
          this._data.simpleMessage.userId,
          CLUB_POSITION.成员,
          () => {
            TipsMgr.showTipX(563);
          },
          () => {
            return false;
          }
        );
      }
    };

    let content = LangMgr.txMsgCode(564, [`<color=d46b0e>${this._data.simpleMessage.nickname}</color>`]);
    let dialogArgs: RouteShowArgs = {
      payload: { content: content, callback: demoteCallback },
      onCloseBack: () => {
        this.closeBack();
      },
    };
    RouteManager.uiRouteCtrl.showRoute(UIClubConfirmDialog, dialogArgs);
  }
  on_click_btn_detail() {
    //
    if (this._data.simpleMessage.userId && this._data.simpleMessage.userId > 0) {
      UIMgr.instance.showDialog(PlayerRouteName.UIPlayerOtherMsg, this._data.simpleMessage);
    }
    this.closeBack();
  }
}
