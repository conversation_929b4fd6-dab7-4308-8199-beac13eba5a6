import { _decorator, EventTouch, Label, Node, Animation, UITransform, Vec3, But<PERSON> } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { ItemCtrl } from "../../../common/ItemCtrl";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { ItemEnum } from "db://assets/GameScrpit/lib/common/ItemEnum";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { ShengDianModule } from "db://assets/GameScrpit/module/sheng_dian/ShengDianModule";
import { ActivityTakeResponse } from "../../../net/protocol/Activity";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("ChengjiuviewHolder")
export class ChengjiuviewHolder extends ViewHolder {
  public updateData(position: number) {
    this.position = position;
    let templeTree = JsonMgr.instance.jsonList.c_templeTree[1];
    let timeBase = position * 8;
    let maxTimes =
      templeTree.Add1Time + templeTree.Add2Time - timeBase > 8
        ? 8
        : templeTree.Add1Time + templeTree.Add2Time - timeBase;
    // log.log(maxTimes);
    let i = 0;
    for (; i < maxTimes; i++) {
      let item = this.node.getChildByName("node_item" + i);
      item.active = true;
      let itemIndex = timeBase + i;
      if (itemIndex < templeTree.Add1Time) {
        let chengjiu = templeTree.yunshiAdd1 * (itemIndex + 1);
        let curYunshi = ShengDianModule.data.templeMessage.totalTreeVal;
        item.getChildByName("lbl_chengjiu").getComponent(Label).string = `运势：${
          curYunshi > chengjiu ? chengjiu : curYunshi
        }/${chengjiu}`;
        item.getComponentInChildren(ItemCtrl).setItemId(templeTree.rewardList[0][0], templeTree.rewardList[0][1]);

        let isTake = ShengDianModule.service.findTreeTakeIndex(itemIndex) + 1;
        log.log("Item", curYunshi, chengjiu, isTake);
        if (curYunshi >= chengjiu && !isTake) {
          item.getComponentInChildren(Animation).play();
          item.getChildByName("Item").getComponent(Button).enabled = false;

          item.getChildByName("lbl_chengjiu").active = true;
          item.getChildByName("bg_yilingqu").active = false;
        } else if (isTake) {
          item.getComponentInChildren(Animation).stop();
          item.getComponentInChildren(Animation).node.setRotation(0, 0, 0, 0);
          item.getChildByName("bg_yilingqu").active = true;
          item.getChildByName("lbl_chengjiu").active = false;
          item.getChildByName("Item").getComponent(Button).enabled = true;
        } else {
          item.getComponentInChildren(Animation).stop();
          item.getComponentInChildren(Animation).node.setRotation(0, 0, 0, 0);
          item.getChildByName("bg_yilingqu").active = false;
          item.getChildByName("lbl_chengjiu").active = true;
          item.getChildByName("Item").getComponent(Button).enabled = true;
        }
      } else {
        let chengjiu =
          templeTree.yunshiAdd1 * templeTree.Add1Time + templeTree.yunshiAdd2 * (itemIndex + 1 - templeTree.Add1Time);
        let curYunshi = ShengDianModule.data.templeMessage.totalTreeVal;
        item.getChildByName("lbl_chengjiu").getComponent(Label).string = `运势：${
          curYunshi > chengjiu ? chengjiu : curYunshi
        }/${chengjiu}`;
        item.getComponentInChildren(ItemCtrl).setItemId(templeTree.rewardList[1][0], templeTree.rewardList[1][1]);
        let isTake = ShengDianModule.service.findTreeTakeIndex(itemIndex) + 1;
        // log.log("=============", isTake);
        if (curYunshi >= chengjiu && !isTake) {
          item.getComponentInChildren(Animation).play();
          item.getChildByName("Item").getComponent(Button).enabled = false;
          item.getChildByName("lbl_chengjiu").active = true;
          item.getChildByName("bg_yilingqu").active = false;
        } else if (isTake) {
          item.getComponentInChildren(Animation).stop();
          item.getComponentInChildren(Animation).node.setRotation(0, 0, 0, 0);
          item.getChildByName("bg_yilingqu").active = true;
          item.getChildByName("lbl_chengjiu").active = false;
          item.getChildByName("Item").getComponent(Button).enabled = true;
        } else {
          item.getComponentInChildren(Animation).stop();
          item.getComponentInChildren(Animation).node.setRotation(0, 0, 0, 0);
          item.getChildByName("bg_yilingqu").active = false;
          item.getChildByName("lbl_chengjiu").active = true;
          item.getChildByName("Item").getComponent(Button).enabled = true;
        }
      }
    }
    for (; i < 8; i++) {
      let item = this.node.getChildByName("node_item" + i);
      item.active = false;
    }
    // JsonMgr.instance.jsonList.c_templeTree[1].Add1Time;
  }

  private onClickItemAward(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    //
    let target: Node = e.target;
    let itemIndex = Number(target.name.slice(-1));
    let chengjiuIndex = this.position * 8 + itemIndex;

    log.log(chengjiuIndex);
    ShengDianModule.api.takeTreeReward(chengjiuIndex, (data: ActivityTakeResponse) => {
      this.updateData(this.position);
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
    });
  }
}
