{"skeleton": {"hash": "EhcpA9RqEo7qkqQhchafqdfaryo=", "spine": "3.8.75", "x": -96.89, "y": -3.17, "width": 203, "height": 210.77, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/小白龙"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -12.93, "y": -22.41, "scaleX": 0.41, "scaleY": 0.41}, {"name": "bone2", "parent": "bone", "x": 31.66, "y": 229.67, "color": "ff0000ff"}, {"name": "bone3", "parent": "bone2", "length": 52.71, "rotation": 0.69, "color": "0171ffff"}, {"name": "bone4", "parent": "bone3", "length": 32, "rotation": 91.51, "x": 0.48, "y": 1.54}, {"name": "bone5", "parent": "bone4", "length": 98.77, "rotation": 12.01, "x": 32.13, "y": 0.6}, {"name": "bone6", "parent": "bone5", "length": 34.27, "rotation": -26.6, "x": 98.5, "y": -0.24}, {"name": "bone17", "parent": "bone6", "x": 14.45, "y": -10.46, "color": "abe323ff"}, {"name": "bone18", "parent": "bone6", "x": 61.3, "y": -8.92}, {"name": "bone49", "parent": "bone5", "length": 59.61, "rotation": 121.13, "x": 81.75, "y": 47.53}, {"name": "bone50", "parent": "bone49", "length": 49.69, "rotation": -53.49, "x": 59.16, "y": -0.6}, {"name": "bone51", "parent": "bone50", "length": 27.62, "rotation": 146.8, "x": 49.11, "y": -0.1, "transform": "noRotationOrReflection"}, {"name": "bone52", "parent": "bone5", "x": 3.22, "y": -7.91, "color": "abe323ff"}, {"name": "bone53", "parent": "bone5", "x": 53.15, "y": -10.17, "color": "abe323ff"}, {"name": "bone44", "parent": "bone5", "length": 59.52, "rotation": -130.74, "x": 43.25, "y": -32.96}, {"name": "bone45", "parent": "bone44", "length": 46.57, "rotation": 36.62, "x": 58.76, "y": -0.01}, {"name": "bone46", "parent": "bone45", "length": 22.99, "rotation": 19.33, "x": 46.55, "y": -0.07, "transform": "noRotationOrReflection"}, {"name": "bone54", "parent": "bone3", "length": 36.8, "rotation": -91.35, "x": -0.37, "y": -0.85}, {"name": "bone76", "parent": "bone54", "x": 29.86, "y": 12.29, "color": "b375ffff"}, {"name": "bone77", "parent": "bone76", "length": 62.12, "rotation": 19.58}, {"name": "bone78", "parent": "bone77", "length": 60.97, "rotation": -38.3, "x": 61.79, "y": -1.1}, {"name": "bone82", "parent": "bone54", "x": 28.93, "y": -28.25}, {"name": "bone83", "parent": "bone82", "length": 64.26, "rotation": -37.43}, {"name": "bone84", "parent": "bone83", "length": 70.02, "rotation": 42.8, "x": 63.49, "y": -0.24}, {"name": "bone107", "parent": "bone54", "x": 26.79, "y": -31.31, "color": "003dffff"}, {"name": "bone108", "parent": "bone107", "length": 66.01, "rotation": 14.21}, {"name": "bone109", "parent": "bone108", "length": 65.77, "rotation": -46.06, "x": 65.18, "y": -0.73}, {"name": "bone80", "parent": "bone", "length": 30.02, "rotation": 0.66, "x": 54.51, "y": 55.45}, {"name": "bone81", "parent": "bone80", "length": 22.22, "rotation": 115.11, "x": 0.35, "y": 0.69}, {"name": "zj", "parent": "bone81", "rotation": 146.73, "x": 29.29, "y": 0.53, "color": "ff3f00ff"}, {"name": "bone79", "parent": "zj", "length": 18.98, "rotation": 34.18}, {"name": "bone86", "parent": "bone", "length": 27.94, "x": -35.1, "y": 51.69}, {"name": "bone87", "parent": "bone86", "length": 25.92, "rotation": 80.04, "x": -0.35, "y": 0.69}, {"name": "yj", "parent": "bone87", "rotation": -165.15, "x": 26.41, "y": 0.08, "color": "ff3f00ff"}, {"name": "bone85", "parent": "yj", "length": 19.23, "rotation": -14.18}, {"name": "bone110", "parent": "bone", "length": 30.02, "rotation": 0.66, "x": -8.34, "y": 55.45, "color": "ffeb00ff"}, {"name": "bone111", "parent": "bone110", "length": 22.22, "rotation": 115.11, "x": 0.35, "y": 0.69, "color": "ffeb00ff"}, {"name": "zj2", "parent": "bone111", "rotation": 146.73, "x": 29.29, "y": 0.53, "color": "ff3f00ff"}, {"name": "bone112", "parent": "zj2", "length": 18.98, "rotation": 34.18}, {"name": "a5", "parent": "bone49", "length": 43.88, "rotation": -27.68, "x": 0.22, "y": 0.1}, {"name": "bone7", "parent": "bone6", "length": 105.71, "rotation": -99.26, "x": 33.63, "y": 25.76, "transform": "noRotationOrReflection"}, {"name": "bone8", "parent": "bone7", "length": 67.6, "rotation": -11.71, "x": 105.71}, {"name": "bone9", "parent": "bone8", "length": 42.27, "rotation": -17.13, "x": 67.6}, {"name": "bone10", "parent": "bone9", "length": 28.41, "rotation": -26.7, "x": 42.27}, {"name": "bone11", "parent": "bone7", "length": 52.27, "rotation": -48.69, "x": 40.59, "y": -46.08}, {"name": "bone12", "parent": "bone11", "length": 30.39, "rotation": 33.83, "x": 52.27}, {"name": "bone55", "parent": "bone54", "length": 66.93, "rotation": 21.88, "x": 19.01, "y": 37.81}, {"name": "bone56", "parent": "bone55", "length": 46.9, "rotation": -3.92, "x": 67.06, "y": 0.34}, {"name": "bone57", "parent": "bone56", "length": 28.66, "rotation": -3.98, "x": 46.9}, {"name": "bone58", "parent": "bone54", "length": 70.15, "rotation": -60.23, "x": 11.18, "y": -52.08}, {"name": "bone59", "parent": "bone58", "length": 59.29, "rotation": -14.04, "x": 70.15}, {"name": "bone60", "parent": "bone59", "length": 39.7, "rotation": 4.37, "x": 59.29}, {"name": "bone61", "parent": "bone54", "length": 66.05, "rotation": -16.47, "x": 19.88, "y": -11.08}, {"name": "bone62", "parent": "bone61", "length": 54.33, "rotation": -9.79, "x": 66.05}, {"name": "bone63", "parent": "bone62", "length": 43.43, "rotation": -14.32, "x": 54.33}, {"name": "bone64", "parent": "bone54", "length": 64.59, "rotation": 0.66, "x": 30.76, "y": -7.2}, {"name": "bone65", "parent": "bone64", "length": 44.48, "rotation": -8.06, "x": 64.59}, {"name": "bone66", "parent": "bone65", "length": 27.81, "rotation": -14.44, "x": 44.48}, {"name": "bone67", "parent": "bone54", "length": 64.16, "rotation": 6.24, "x": 27.75, "y": 30.94}, {"name": "bone68", "parent": "bone67", "length": 49.18, "rotation": -6.44, "x": 64.16}, {"name": "bone69", "parent": "bone68", "length": 24.63, "rotation": 4.27, "x": 49.18}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "bone", "x": -149.37, "y": 333.04}, {"name": "a0", "parent": "<PERSON><PERSON><PERSON><PERSON>", "length": 58.46, "rotation": 87.54, "y": 9.42}, {"name": "bone14", "parent": "bone6", "length": 21.69, "rotation": -104.49, "x": 50.93, "y": -41.63, "transform": "noRotationOrReflection"}, {"name": "bone15", "parent": "bone14", "length": 17.14, "rotation": -23.8, "x": 21.69}, {"name": "bone16", "parent": "bone15", "length": 11.42, "rotation": -21.97, "x": 17.14}, {"name": "bone19", "parent": "bone", "x": -159.65, "y": 198.18}, {"name": "bone20", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -508.08, "y": 362.93}, {"name": "bone21", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -474.47, "y": 362.59}, {"name": "bone22", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -440.85, "y": 362.25}, {"name": "bone23", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -407.23, "y": 361.91}, {"name": "bone24", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -373.62, "y": 361.57}, {"name": "bone25", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -340, "y": 361.24}, {"name": "bone26", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -306.38, "y": 360.9}, {"name": "bone27", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -272.77, "y": 360.56}, {"name": "bone28", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -239.15, "y": 360.22}, {"name": "bone29", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -205.53, "y": 359.88}, {"name": "bone30", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -171.92, "y": 359.55}, {"name": "bone31", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -138.3, "y": 359.21}, {"name": "bone32", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -104.68, "y": 358.87}, {"name": "bone33", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -71.07, "y": 358.53}, {"name": "bone34", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -37.45, "y": 358.19}, {"name": "bone35", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": -3.83, "y": 357.85}, {"name": "bone36", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": 29.78, "y": 357.52}, {"name": "bone37", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": 63.4, "y": 357.18}, {"name": "bone38", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": 97.02, "y": 356.84}, {"name": "bone39", "parent": "bone19", "length": 33.62, "rotation": -0.58, "x": 130.63, "y": 356.5}, {"name": "bone40", "parent": "bone27", "length": 38.46, "rotation": 168.25, "x": 11.15, "y": -2.55, "color": "ff6969ff"}, {"name": "bone41", "parent": "bone40", "length": 42.3, "rotation": 101.9, "x": 38.46, "color": "ff6969ff"}, {"name": "bone42", "parent": "bone41", "length": 29.36, "rotation": -53.48, "x": 42.3, "color": "ff6969ff"}, {"name": "bone43", "parent": "bone26", "length": 22.42, "rotation": -168.98, "x": 2.59, "y": -26, "color": "ff6969ff"}, {"name": "bone47", "parent": "bone43", "length": 30.13, "rotation": 44.69, "x": 22.42, "color": "ff6969ff"}, {"name": "bone48", "parent": "bone47", "length": 23.05, "rotation": -34.17, "x": 30.13, "color": "ff6969ff"}, {"name": "bone70", "parent": "bone33", "length": 32.94, "rotation": -161.47, "x": 23.23, "y": -0.95, "color": "ff6969ff"}, {"name": "bone71", "parent": "bone70", "length": 33.23, "rotation": 90.45, "x": 32.94, "color": "ff6969ff"}, {"name": "bone72", "parent": "bone71", "length": 33.1, "rotation": -51.92, "x": 33.23, "color": "ff6969ff"}, {"name": "bone73", "parent": "bone33", "length": 28.76, "rotation": -100.23, "x": 26.58, "y": 1.02, "color": "ff6969ff"}, {"name": "bone74", "parent": "bone73", "length": 19.52, "rotation": 40.28, "x": 28.76, "color": "ff6969ff"}, {"name": "bone75", "parent": "bone74", "length": 28.15, "rotation": -29.33, "x": 19.84, "y": -0.12, "color": "ff6969ff"}, {"name": "bone88", "parent": "bone5", "length": 37.06, "rotation": 51.51, "x": 44.56, "y": -7.01}, {"name": "bone89", "parent": "bone88", "length": 25.27, "rotation": 18.51, "x": 37.06}, {"name": "bone90", "parent": "bone5", "length": 24.98, "rotation": -85.22, "x": 62.74, "y": -40.74}, {"name": "bone91", "parent": "bone19", "x": 70.89, "y": -113.24, "color": "afff00ff"}, {"name": "bone92", "parent": "bone19", "x": -6.7, "y": 75.55, "color": "afff00ff"}, {"name": "bone93", "parent": "bone19", "x": 211.76, "y": 125, "color": "afff00ff"}, {"name": "bone94", "parent": "bone19", "x": 184.02, "y": 339.24, "color": "afff00ff"}, {"name": "bone95", "parent": "bone19", "x": 359.54, "y": 86.93, "color": "afff00ff"}, {"name": "dianball", "parent": "<PERSON><PERSON><PERSON><PERSON>", "x": 0.76, "y": 2.33, "scaleX": 0.5, "scaleY": 0.5}, {"name": "dianball2", "parent": "<PERSON><PERSON><PERSON><PERSON>", "x": -26.09, "y": 11.69}, {"name": "dianball3", "parent": "<PERSON><PERSON><PERSON><PERSON>", "x": 5.13, "y": -1.42}, {"name": "faz", "parent": "root", "x": -14.53, "y": 106.31, "scaleX": 1.8169, "scaleY": 1.8169}, {"name": "cicici", "parent": "root", "x": 528.28, "y": 79.05, "scaleX": 6.5661, "scaleY": 6.5661}, {"name": "<PERSON><PERSON><PERSON>", "parent": "root", "x": 355.67, "y": 67.04, "scaleX": 1.3343, "scaleY": 1.3343}, {"name": "shouji2", "parent": "root", "x": 575.58, "y": 47.48, "scaleX": 1.3343, "scaleY": 1.3343}, {"name": "shouji3", "parent": "root", "x": 754.19, "y": 85.2, "scaleX": 1.3343, "scaleY": 1.3343}, {"name": "loong", "parent": "root", "x": 126.72, "y": -49.83}, {"name": "loong2", "parent": "root", "x": 126.72, "y": -49.83}, {"name": "light", "parent": "root", "x": 4.11, "y": 100.1}, {"name": "chongji1", "parent": "root", "x": -29.65, "y": 71.24, "scaleX": 1.6097, "scaleY": 1.6097}, {"name": "chongji2", "parent": "root", "x": 9.2, "y": 18.88, "scaleX": 0.5072, "scaleY": 0.466}, {"name": "bone13", "parent": "root", "rotation": -16.59, "x": -22.96, "y": 92.73, "scaleX": 5.2206, "scaleY": 5.2206}], "slots": [{"name": "chongci/qf_xishi_jn_slj_dm_00", "bone": "chongji2", "color": "0092ffff", "blend": "additive"}, {"name": "a24", "bone": "bone43"}, {"name": "a23", "bone": "bone73"}, {"name": "a22", "bone": "bone26"}, {"name": "a21", "bone": "bone40"}, {"name": "a20", "bone": "bone70"}, {"name": "a32", "bone": "bone21"}, {"name": "a18", "bone": "bone7", "attachment": "a18"}, {"name": "a17", "bone": "bone55", "attachment": "a17"}, {"name": "a16", "bone": "bone77", "attachment": "a16"}, {"name": "a25", "bone": "bone108", "color": "ffffff00", "attachment": "a16"}, {"name": "a15", "bone": "bone83", "attachment": "a15"}, {"name": "a14", "bone": "bone64", "attachment": "a14"}, {"name": "a13", "bone": "bone58", "attachment": "a13"}, {"name": "a12", "bone": "bone55", "attachment": "a12"}, {"name": "a11", "bone": "bone45", "attachment": "a11"}, {"name": "a10", "bone": "bone46", "attachment": "a10"}, {"name": "a9", "bone": "bone5", "attachment": "a9"}, {"name": "a8", "bone": "a5", "attachment": "a8"}, {"name": "a7", "bone": "bone49", "attachment": "a7"}, {"name": "a6", "bone": "bone51", "attachment": "a6"}, {"name": "a5", "bone": "a5", "attachment": "a5"}, {"name": "a4", "bone": "bone88", "attachment": "a4"}, {"name": "a3", "bone": "bone6", "attachment": "a3"}, {"name": "a2", "bone": "bone6", "attachment": "a2"}, {"name": "a1", "bone": "bone6", "attachment": "a1"}, {"name": "a0", "bone": "a0", "attachment": "a0"}, {"name": "long", "bone": "bone19", "attachment": "long"}, {"name": "dianball/qf_qing_jn3_qtgd_rw_sd_01", "bone": "dianball"}, {"name": "dianball/qf_qing_jn3_qtgd_xlsd_00", "bone": "dianball2"}, {"name": "dianball/qf_qing_jn3_qtgd_tw_sg_00", "bone": "dianball3"}, {"name": "yichu/qf_qing_jn3_qtkz_rwql_bw_00", "bone": "faz", "blend": "additive"}, {"name": "chongj/changcici1", "bone": "cicici"}, {"name": "shouji/qf_boss_fsf_pg_bz_00", "bone": "<PERSON><PERSON><PERSON>"}, {"name": "shouji/qf_boss_fsf_pg_bz_0", "bone": "shouji2"}, {"name": "shouji/qf_boss_fsf_pg_bz_1", "bone": "shouji3"}, {"name": "loong", "bone": "loong", "color": "36c7ffff", "blend": "additive"}, {"name": "loong2", "bone": "loong2", "color": "36c7ffff", "blend": "additive"}, {"name": "light", "bone": "light", "color": "158fff00", "attachment": "light", "blend": "additive"}, {"name": "chongci/jss_01", "bone": "chongji1", "blend": "additive"}, {"name": "chongci/A_wenli_wave_0", "bone": "bone13", "color": "007affff", "blend": "additive"}, {"name": "chongci/A_wenli_wave_1", "bone": "bone13", "color": "007affff", "blend": "additive"}], "ik": [{"name": "yj", "order": 2, "bones": ["bone83", "bone84"], "target": "yj"}, {"name": "zj", "bones": ["bone77", "bone78"], "target": "zj", "bendPositive": false}, {"name": "zj2", "order": 1, "bones": ["bone108", "bone109"], "target": "zj2", "bendPositive": false}], "transform": [{"name": "s01", "order": 5, "bones": ["bone53"], "target": "bone52", "x": 49.93, "y": -2.26, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "s1", "order": 7, "bones": ["bone49"], "target": "bone52", "rotation": 121.13, "x": 78.53, "y": 55.44, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "s2", "order": 8, "bones": ["bone44"], "target": "bone53", "rotation": -130.74, "x": -9.9, "y": -22.8, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "t01", "order": 4, "bones": ["bone18"], "target": "bone17", "x": 46.85, "y": 1.54, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "w1", "order": 6, "bones": ["bone88"], "target": "bone52", "rotation": 51.51, "x": 41.33, "y": 0.9, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}], "path": [{"name": "long", "order": 3, "bones": ["bone39", "bone38", "bone37", "bone36", "bone35", "bone34", "bone33", "bone32", "bone31", "bone30", "bone29", "bone28", "bone27", "bone26", "bone25", "bone24", "bone23", "bone22", "bone21", "bone20"], "target": "long", "spacingMode": "percent", "rotateMode": "chainScale", "spacing": 0.025}], "skins": [{"name": "default", "attachments": {"a32": {"a19": {"type": "mesh", "uvs": [0.54291, 0.00482, 0.55648, 0.06185, 0.59986, 0.13446, 0.59955, 0.0587, 0.62712, 0.04221, 0.63899, 0.05043, 0.63343, 0.11676, 0.72248, 0.1715, 0.73494, 0.20804, 0.81286, 0.28683, 0.81637, 0.36139, 0.86203, 0.41014, 0.86205, 0.45029, 0.84738, 0.48374, 0.88394, 0.59914, 0.91048, 0.61691, 0.89072, 0.66494, 0.97386, 0.67583, 0.99885, 0.70425, 0.99116, 0.7948, 0.86216, 0.84912, 0.86215, 0.93552, 0.78979, 1, 0.78373, 1, 0.7653, 0.96893, 0.64664, 0.98679, 0.6521, 0.85049, 0.61415, 0.85027, 0.56785, 0.80178, 0.58675, 0.76216, 0.3963, 0.72388, 0.36017, 0.68212, 0.33804, 0.63613, 0.33783, 0.56004, 0.28118, 0.53025, 0.2306, 0.47773, 0.21906, 0.38063, 0.15362, 0.31714, 0.1475, 0.29704, 0.07036, 0.11978, 0.00365, 0.09977, 0.00509, 0.07937, 0.07893, 0.08009, 0.13439, 0.11898, 0.18976, 0.19858, 0.26902, 0.20988, 0.23309, 0.11795, 0.23324, 0.03391, 0.25004, 0.01768, 0.33348, 0.13484, 0.35333, 0.08963, 0.4539, 0.07222, 0.50951, 0.12862, 0.50935, 0.03959, 0.52603, 0.00458, 0.51666, 0.33942, 0.65868, 0.54912, 0.77629, 0.73086], "triangles": [5, 3, 4, 6, 3, 5, 39, 41, 42, 39, 42, 43, 40, 41, 39, 53, 1, 52, 0, 53, 54, 1, 53, 0, 2, 3, 6, 52, 1, 2, 48, 46, 47, 46, 48, 49, 45, 46, 49, 44, 39, 43, 38, 39, 44, 45, 37, 38, 55, 52, 2, 49, 50, 51, 51, 52, 49, 55, 49, 52, 44, 45, 38, 36, 37, 45, 55, 35, 36, 45, 49, 55, 45, 55, 36, 34, 35, 55, 2, 6, 7, 8, 55, 2, 7, 8, 2, 33, 34, 55, 12, 13, 10, 12, 10, 11, 56, 55, 10, 8, 10, 55, 9, 10, 8, 56, 10, 13, 56, 33, 55, 31, 32, 33, 16, 14, 15, 33, 30, 31, 56, 30, 33, 14, 57, 56, 14, 56, 13, 57, 14, 16, 29, 30, 56, 29, 56, 57, 19, 17, 18, 20, 57, 16, 19, 20, 16, 19, 16, 17, 26, 27, 29, 28, 29, 27, 57, 26, 29, 26, 57, 20, 20, 24, 26, 21, 24, 20, 25, 26, 24, 22, 23, 24, 21, 22, 24], "vertices": [2, 67, -38.21, 52.74, 0.02797, 68, 0.58, 57.58, 0.97203, 2, 67, -30.27, 47.4, 0.0512, 68, 7.86, 51.2, 0.9488, 2, 67, -17.43, 44.72, 0.23051, 68, 20.09, 46.82, 0.76949, 2, 67, -26.12, 54.63, 0.24816, 68, 12.42, 57.84, 0.75184, 2, 67, -25.12, 61.16, 0.24693, 68, 13.88, 64.2, 0.75307, 2, 67, -22.94, 61.96, 0.24707, 68, 16.05, 64.7, 0.75293, 2, 67, -15.94, 52.36, 0.28549, 68, 22.11, 54.22, 0.71451, 2, 67, -0.36, 59.26, 0.54883, 68, 37.7, 59, 0.45117, 2, 67, 5.12, 56.43, 0.63905, 68, 42.79, 55.46, 0.36095, 2, 67, 22.28, 58.41, 0.86831, 68, 59.55, 55.13, 0.13169, 2, 67, 31.17, 49.17, 0.94987, 68, 67.47, 44.75, 0.05013, 2, 67, 41.52, 49.99, 0.99049, 68, 77.55, 44.18, 0.00951, 2, 67, 46.12, 44.71, 0.99595, 68, 81.6, 38.32, 0.00405, 2, 67, 48.4, 37.99, 0.99912, 68, 83.31, 31.33, 0.00088, 1, 67, 65.42, 28.61, 1, 1, 67, 70.23, 30.48, 1, 1, 67, 73.65, 21.04, 1, 1, 67, 83.6, 32.77, 1, 1, 67, 89.46, 32.99, 1, 1, 67, 99.01, 19.87, 1, 1, 67, 91.72, -7.69, 1, 1, 67, 101.6, -19.05, 1, 1, 67, 101.39, -38.98, 1, 1, 67, 100.76, -39.94, 1, 1, 67, 95.28, -38.77, 1, 1, 67, 84.9, -59.9, 1, 1, 67, 69.89, -41.12, 1, 1, 67, 65.9, -47.1, 1, 2, 67, 55.51, -48.06, 0.9999, 68, 83.76, -55.22, 0.0001, 2, 67, 52.96, -39.86, 0.99774, 68, 81.9, -46.73, 0.00226, 2, 67, 28.65, -64.97, 0.89652, 68, 56.49, -68.46, 0.10348, 2, 67, 20.1, -65.2, 0.85094, 68, 48.2, -67.55, 0.14906, 2, 67, 12.52, -62.66, 0.78997, 68, 41.06, -64.01, 0.21003, 2, 67, 3.8, -52.69, 0.57181, 68, 33.36, -52.93, 0.42819, 2, 67, -5.53, -57.74, 0.32926, 68, 23.95, -56.71, 0.67074, 2, 67, -16.83, -58.85, 0.19561, 68, 12.93, -56.3, 0.80439, 2, 67, -29.14, -47.91, 0.05806, 68, 1.83, -43.77, 0.94194, 2, 67, -43.24, -49.93, 0.00417, 68, -11.97, -43.89, 0.99583, 2, 67, -46.18, -48.25, 0.00184, 68, -14.69, -41.83, 0.99816, 1, 68, -41.29, -27.02, 1, 1, 68, -50.85, -33.67, 1, 1, 68, -52.75, -30.48, 1, 1, 68, -44.32, -19.99, 1, 1, 68, -34.13, -17.71, 1, 1, 68, -19.84, -21.39, 1, 1, 68, -9.73, -11.67, 1, 1, 68, -23.07, -3.4, 1, 1, 68, -31.52, 8.89, 1, 1, 68, -31.26, 13.68, 1, 1, 68, -10, 8.54, 1, 1, 68, -12.32, 17.99, 1, 2, 67, -39.82, 29.8, 0.00219, 68, -2.69, 34.96, 0.99781, 2, 67, -27.55, 31.19, 0.05285, 68, 9.28, 34.71, 0.94715, 2, 67, -37.74, 42.86, 0.03402, 68, 0.29, 47.68, 0.96598, 2, 67, -40, 50.1, 0.028, 68, -1.35, 55.19, 0.972, 2, 67, -2.7, 4.61, 0.22463, 68, 31.35, 4.95, 0.77537, 1, 67, 36.13, -0.47, 1, 1, 67, 69.21, -5.74, 1], "hull": 55, "edges": [0, 108, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 98, 110, 110, 112, 112, 114], "width": 210, "height": 200}}, "a11": {"a11": {"type": "mesh", "uvs": [0.13049, 0.00996, 0.22597, 0.00929, 0.25974, 0.03879, 0.35863, 0.15351, 0.46109, 0.27236, 0.56524, 0.27261, 0.67063, 0.39408, 0.79944, 0.40392, 0.89347, 0.37667, 0.95134, 0.39739, 0.99151, 0.44462, 0.99167, 0.56041, 0.77691, 0.66659, 0.69838, 0.74153, 0.63344, 0.90775, 0.54279, 0.96406, 0.41843, 1, 0.27419, 0.99001, 0.16572, 0.94029, 0.09519, 0.85827, 0.0286, 0.6866, 0.00401, 0.41608, 0.00909, 0.12749, 0.16765, 0.26496, 0.26665, 0.51378, 0.46105, 0.60578, 0.89305, 0.48869], "triangles": [4, 23, 3, 23, 2, 3, 21, 22, 23, 2, 0, 1, 2, 23, 0, 22, 0, 23, 26, 7, 8, 12, 26, 11, 26, 8, 9, 26, 9, 10, 26, 10, 11, 25, 5, 6, 12, 6, 7, 12, 7, 26, 12, 13, 6, 4, 24, 23, 21, 23, 24, 25, 24, 4, 25, 4, 5, 13, 25, 6, 20, 21, 24, 19, 20, 24, 14, 25, 13, 18, 19, 24, 17, 18, 24, 24, 25, 17, 14, 15, 25, 25, 16, 17, 15, 16, 25], "vertices": [3, 14, -3.65, 30.18, 0.21946, 15, -32.08, 61.46, 0.00021, 5, 68.5, -49.89, 0.78033, 3, 14, 6.14, 35.14, 0.1047, 15, -21.26, 59.6, 0.00419, 5, 65.86, -60.55, 0.89111, 3, 14, 10.92, 34.26, 0.16338, 15, -17.95, 56.04, 0.05796, 5, 62.08, -63.6, 0.77867, 3, 14, 26.17, 29.18, 0.28943, 15, -8.74, 42.87, 0.2039, 5, 48.28, -71.83, 0.50667, 3, 14, 41.96, 23.91, 0.31976, 15, 0.79, 29.22, 0.45268, 5, 33.98, -80.37, 0.22756, 4, 14, 52.69, 29.24, 0.22842, 15, 12.58, 27.1, 0.7104, 16, -29.16, 32.27, 0.00251, 5, 31.01, -91.97, 0.05867, 3, 14, 68.91, 23.9, 0.09987, 15, 22.41, 13.13, 0.82353, 16, -21.71, 16.91, 0.0766, 3, 14, 82.59, 29.64, 0.01938, 15, 36.82, 9.58, 0.72094, 16, -8.05, 11.09, 0.25968, 3, 14, 91.06, 36.89, 0.00017, 15, 47.94, 10.34, 0.45099, 16, 3.04, 10.06, 0.54884, 2, 15, 54.13, 7.16, 0.25804, 16, 8.64, 5.92, 0.74196, 3, 14, 104.16, 35.9, 0.00974, 15, 57.86, 1.74, 0.24895, 16, 11.46, -0.02, 0.74131, 3, 14, 109.29, 25.65, 0.08562, 15, 55.87, -9.55, 0.32554, 16, 7.68, -10.85, 0.58883, 3, 14, 91.89, 5.22, 0.26418, 15, 29.72, -15.57, 0.41411, 16, -19.11, -12.59, 0.32171, 3, 14, 87.12, -5.45, 0.52755, 15, 19.52, -21.29, 0.36711, 16, -30.08, -16.6, 0.10534, 3, 14, 87.79, -23.51, 0.78274, 15, 9.29, -36.18, 0.21718, 16, -42.58, -29.66, 8e-05, 2, 14, 80.96, -33.16, 0.9372, 15, -1.95, -39.84, 0.0628, 2, 14, 69.75, -42.73, 0.99417, 15, -16.66, -40.84, 0.00583, 2, 14, 54.47, -49.25, 0.99968, 15, -32.81, -36.96, 0.00032, 1, 14, 41.11, -50.42, 1, 2, 14, 30.22, -46.78, 0.94667, 5, -11.92, -25.34, 0.05333, 2, 14, 15.78, -34.99, 0.78311, 5, 6.44, -22.08, 0.21689, 3, 14, 1.29, -12.29, 0.50933, 15, -53.45, 24.42, 0, 5, 33.09, -25.92, 0.49067, 3, 14, -10.95, 13.53, 0.28122, 15, -47.87, 52.45, 0.00012, 5, 60.64, -33.5, 0.71867, 3, 14, 11.44, 9.5, 0.51914, 15, -32.3, 35.85, 0.00042, 5, 42.97, -47.83, 0.48044, 3, 14, 32.63, -7.46, 0.80059, 15, -25.41, 9.61, 0.0003, 5, 16.3, -52.82, 0.19911, 3, 14, 56.7, -5.62, 0.93311, 15, -4.99, -3.28, 0.00023, 5, 1.98, -72.26, 0.06667, 2, 15, 45.95, -0.57, 0.27945, 16, -0.67, -0.39, 0.72055], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 46, 46, 48, 48, 50, 20, 52, 4, 6, 6, 8, 24, 26, 26, 28], "width": 115, "height": 99}}, "a12": {"a12": {"type": "mesh", "uvs": [0.10768, 0.00501, 0.27378, 0.10239, 0.37503, 0.17663, 0.46954, 0.25261, 0.54124, 0.31991, 0.61986, 0.39001, 0.68982, 0.46369, 0.74714, 0.52744, 0.80797, 0.595, 0.86726, 0.67542, 0.91664, 0.74487, 0.95718, 0.81516, 0.98926, 0.87576, 1, 0.91364, 0.799, 0.96163, 0.58594, 0.9948, 0.48956, 0.99653, 0.48338, 0.95548, 0.46516, 0.89838, 0.45177, 0.84045, 0.42837, 0.77235, 0.4037, 0.69061, 0.37036, 0.61462, 0.3346, 0.54342, 0.29107, 0.46578, 0.24454, 0.38594, 0.18624, 0.29935, 0.12947, 0.22161, 0.0623, 0.14135, 0.01451, 0.07076, 0.01446, 0.03152, 0.05544, 0.00529], "triangles": [0, 29, 30, 31, 0, 30, 1, 29, 0, 1, 28, 29, 28, 1, 2, 22, 7, 8, 21, 22, 8, 21, 8, 9, 20, 21, 9, 20, 9, 10, 19, 20, 10, 19, 10, 11, 18, 19, 11, 18, 11, 12, 14, 18, 12, 14, 17, 18, 12, 13, 14, 15, 17, 14, 16, 17, 15, 27, 28, 2, 27, 2, 3, 26, 27, 3, 26, 3, 4, 25, 26, 4, 25, 4, 5, 24, 25, 5, 24, 5, 6, 23, 24, 6, 23, 6, 7, 22, 23, 7], "vertices": [2, 46, -31.56, 0.61, 0.14815, 4, 6.8, -26.2, 0.85185, 2, 46, -10.92, 4.54, 0.37037, 4, -11.44, -36.64, 0.62963, 2, 46, 4.2, 5.94, 0.62963, 4, -25.28, -42.89, 0.37037, 3, 46, 19.46, 6.81, 0.84154, 47, -47.93, 3.2, 0.01032, 4, -39.41, -48.69, 0.14815, 3, 46, 32.68, 6.83, 0.89498, 47, -34.75, 4.13, 0.06798, 4, -51.9, -53.01, 0.03704, 2, 46, 46.54, 7.1, 0.78996, 47, -20.93, 5.34, 0.21004, 2, 46, 60.81, 6.59, 0.55742, 47, -6.67, 5.81, 0.44258, 3, 46, 73.07, 5.95, 0.30848, 48, -41.6, 3.13, 0.01826, 47, 5.61, 6.01, 0.67327, 3, 46, 86.07, 5.28, 0.1172, 48, -28.63, 4.25, 0.09181, 47, 18.63, 6.22, 0.79099, 3, 46, 101.23, 3.65, 0.02672, 48, -13.39, 4.72, 0.2577, 47, 33.86, 5.64, 0.71558, 2, 48, -0.26, 5.01, 0.49818, 47, 46.98, 5.02, 0.50182, 2, 48, 12.88, 4.69, 0.73918, 47, 60.07, 3.78, 0.26082, 2, 48, 24.17, 4.22, 0.90663, 47, 71.3, 2.54, 0.09337, 2, 48, 31.08, 3.33, 0.98122, 47, 78.13, 1.16, 0.01878, 2, 48, 36.52, -11.8, 0.99978, 47, 82.51, -14.31, 0.00022, 2, 48, 39.14, -27.09, 0.99732, 47, 84.06, -29.74, 0.00268, 3, 46, 146.85, -41.21, 6e-05, 48, 37.96, -33.45, 0.98489, 47, 82.44, -36, 0.01505, 3, 46, 139.7, -38.87, 0.00105, 48, 30.55, -32.12, 0.94227, 47, 75.15, -34.16, 0.05668, 3, 46, 129.52, -36.23, 0.00848, 48, 20.1, -30.9, 0.84154, 47, 64.81, -32.22, 0.14999, 3, 46, 119.31, -33.23, 0.04126, 48, 9.58, -29.33, 0.66896, 47, 54.42, -29.92, 0.28978, 3, 46, 107.12, -30.18, 0.13159, 48, -2.91, -27.98, 0.44946, 47, 42.05, -27.71, 0.41895, 3, 46, 92.58, -26.31, 0.29992, 48, -17.84, -26.14, 0.24186, 47, 27.28, -24.84, 0.45822, 3, 46, 78.81, -23.36, 0.52615, 48, -31.89, -25.11, 0.09678, 47, 13.34, -22.84, 0.37708, 3, 46, 65.79, -20.87, 0.74565, 48, -45.12, -24.44, 0.02587, 47, 0.19, -21.25, 0.22848, 3, 46, 51.49, -18.45, 0.90093, 48, -59.62, -24, 0.00332, 47, -14.25, -19.81, 0.09575, 4, 46, 36.75, -16.07, 0.93814, 48, -74.56, -23.67, 9e-05, 47, -29.12, -18.44, 0.02473, 4, -63.21, -32.69, 0.03704, 3, 46, 20.56, -13.98, 0.84955, 47, -45.41, -17.46, 0.0023, 4, -47.23, -29.39, 0.14815, 2, 46, 5.92, -12.37, 0.62963, 4, -32.87, -26.14, 0.37037, 2, 46, -9.4, -11.25, 0.37037, 4, -18.02, -22.21, 0.62963, 2, 46, -22.6, -9.56, 0.14815, 4, -4.99, -19.5, 0.85185, 2, 46, -29.3, -6.97, 0.03704, 4, 2.19, -19.77, 0.96296, 2, 46, -32.78, -2.67, 0.03704, 4, 6.88, -22.7, 0.96296], "hull": 32, "edges": [0, 62, 0, 2, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 56, 58, 58, 60, 60, 62, 38, 40, 20, 22, 22, 24, 16, 18, 18, 20, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 10, 12, 12, 14, 14, 16, 6, 8, 8, 10, 50, 52, 52, 54, 54, 56, 2, 4, 4, 6, 34, 36, 36, 38], "width": 67, "height": 183}}, "a13": {"a13": {"type": "mesh", "uvs": [0.88181, 0, 0.92399, 0, 1, 0.0723, 0.99881, 0.12635, 0.98187, 0.22417, 0.96387, 0.30785, 0.94089, 0.39445, 0.9147, 0.48541, 0.88439, 0.5751, 0.85397, 0.65133, 0.82024, 0.725, 0.77703, 0.79894, 0.73294, 0.86052, 0.69352, 0.91136, 0.65151, 0.95426, 0.61016, 0.99365, 0.53964, 0.98053, 0.44825, 0.94598, 0.3713, 0.88732, 0.31813, 0.82001, 0.31888, 0.72914, 0.2421, 0.81163, 0.16499, 0.72264, 0.09701, 0.66207, 0.03453, 0.59789, 0.00452, 0.52583, 0.00404, 0.49317, 0.05084, 0.4586, 0.10832, 0.42933, 0.16034, 0.40785, 0.2293, 0.38365, 0.29971, 0.36563, 0.37148, 0.34657, 0.4346, 0.3271, 0.51232, 0.29899, 0.57494, 0.27232, 0.6388, 0.24011, 0.69865, 0.19852, 0.76497, 0.14512, 0.8151, 0.07994, 0.86067, 0.01806, 0.90721, 0.10946, 0.21029, 0.67347, 0.22827, 0.6478, 0.29506, 0.60215, 0.36698, 0.55794, 0.44276, 0.50374, 0.51468, 0.44383, 0.58789, 0.37394, 0.65468, 0.30405, 0.71119, 0.23416, 0.13854, 0.59896, 0.47712, 0.87354], "triangles": [5, 6, 39, 39, 7, 38, 4, 41, 3, 3, 41, 2, 39, 40, 41, 40, 0, 41, 41, 1, 2, 41, 0, 1, 5, 39, 41, 5, 41, 4, 8, 49, 7, 6, 7, 39, 11, 47, 10, 10, 47, 9, 9, 48, 8, 20, 45, 52, 15, 16, 14, 17, 52, 16, 16, 52, 14, 14, 52, 13, 17, 18, 52, 13, 52, 12, 18, 19, 52, 19, 20, 52, 12, 52, 11, 11, 52, 46, 22, 42, 21, 22, 23, 42, 23, 51, 42, 42, 51, 43, 23, 24, 51, 43, 51, 44, 29, 44, 51, 24, 27, 51, 27, 28, 51, 51, 28, 29, 24, 25, 27, 25, 26, 27, 45, 46, 52, 21, 42, 20, 42, 43, 20, 43, 44, 20, 20, 44, 45, 44, 29, 30, 45, 30, 31, 45, 44, 30, 45, 32, 46, 45, 31, 32, 32, 33, 46, 46, 33, 47, 11, 46, 47, 9, 47, 48, 48, 49, 8, 7, 49, 50, 33, 34, 47, 47, 34, 48, 34, 35, 48, 48, 35, 49, 35, 36, 49, 49, 36, 50, 36, 37, 50, 50, 37, 38, 7, 50, 38], "vertices": [3, 49, -38.82, -14.66, 0.02469, 52, -34.9, -25.51, 0.00309, 4, 19.71, 25.49, 0.97222, 3, 49, -46.6, -10.33, 0.00309, 52, -37.53, -17.01, 0.01775, 4, 19.37, 16.6, 0.97917, 2, 52, -29.12, 2.37, 0.08539, 4, 5.02, 1.1, 0.91461, 3, 53, -84.92, -9.42, 0.00323, 52, -19.23, 5.15, 0.20665, 4, -5.23, 1.75, 0.79012, 3, 53, -66.73, -4.19, 0.02026, 52, -0.42, 7.21, 0.3748, 4, -23.66, 6.03, 0.60494, 3, 53, -50.83, -0.38, 0.07312, 52, 15.9, 8.26, 0.53182, 4, -39.41, 10.44, 0.39506, 4, 54, -86.23, -19.18, 0.00014, 53, -33.97, 2.74, 0.18313, 52, 33.05, 8.47, 0.60685, 4, -55.66, 15.92, 0.20988, 4, 54, -69.59, -11.95, 0.00483, 53, -16.05, 5.63, 0.35033, 52, 51.19, 8.28, 0.55843, 4, -72.72, 22.1, 0.08642, 4, 54, -52.56, -5.53, 0.02683, 53, 2.04, 7.64, 0.53289, 52, 69.36, 7.19, 0.4156, 4, -89.5, 29.15, 0.02469, 4, 54, -37.43, -0.81, 0.09069, 53, 17.86, 8.48, 0.65953, 52, 85.09, 5.32, 0.24567, 4, -103.73, 36.12, 0.00412, 3, 54, -22.22, 3.07, 0.21628, 53, 33.56, 8.46, 0.6707, 52, 100.56, 2.64, 0.11302, 5, 50, -41.35, 100.17, 0.00016, 51, -92.71, 107.55, 2e-05, 54, -5.64, 5.47, 0.40214, 53, 50.21, 6.69, 0.55969, 52, 116.67, -1.94, 0.03799, 5, 50, -29.33, 109.05, 0.00142, 51, -80.05, 115.48, 0.00016, 54, 9.29, 6.18, 0.60972, 53, 64.86, 3.69, 0.38015, 52, 130.59, -7.38, 0.00855, 6, 49, 80.13, 117.3, 1e-05, 50, -18.78, 116.21, 0.00659, 51, -68.99, 121.82, 0.00082, 54, 22.04, 6.29, 0.78663, 53, 77.24, 0.65, 0.20506, 52, 142.27, -12.49, 0.00089, 5, 49, 91.84, 120.1, 5e-05, 50, -8.1, 121.78, 0.02094, 51, -57.92, 126.56, 0.00289, 54, 34.01, 5, 0.89043, 53, 88.52, -3.57, 0.0857, 5, 49, 103.11, 122.4, 0.00015, 50, 2.27, 126.74, 0.05079, 51, -47.2, 130.71, 0.00805, 54, 45.39, 3.37, 0.91307, 53, 99.14, -7.96, 0.02794, 5, 49, 114.89, 112.98, 0.00033, 50, 15.99, 120.46, 0.09947, 51, -34, 123.41, 0.01912, 54, 53.32, -9.46, 0.86884, 53, 103.65, -22.36, 0.01224, 5, 49, 128.55, 97.86, 0.00054, 50, 32.9, 109.11, 0.16367, 51, -18, 110.8, 0.04059, 54, 61.09, -28.29, 0.78156, 53, 106.52, -42.52, 0.01364, 5, 49, 137.31, 80.23, 0.0006, 50, 45.68, 94.13, 0.23814, 51, -6.4, 94.89, 0.09412, 54, 63.41, -47.85, 0.64962, 53, 103.93, -62.04, 0.01752, 5, 49, 140.89, 63.6, 0.00062, 50, 53.19, 78.86, 0.29865, 51, -0.07, 79.1, 0.17446, 54, 61.19, -64.71, 0.50723, 53, 97.61, -77.84, 0.01904, 5, 49, 132.35, 48.59, 0.00058, 50, 48.55, 62.23, 0.32708, 51, -5.97, 62.87, 0.28706, 54, 48.1, -75.97, 0.36791, 53, 82.14, -85.51, 0.01737, 5, 49, 154.13, 54.4, 0.00047, 50, 68.27, 73.15, 0.31312, 51, 14.53, 72.26, 0.42843, 54, 70.57, -77.83, 0.2448, 53, 103.45, -92.86, 0.01318, 5, 49, 160.12, 31.71, 0.00029, 50, 79.58, 52.6, 0.25939, 51, 24.24, 50.9, 0.58604, 54, 68.57, -101.21, 0.14631, 53, 95.74, -115.02, 0.00797, 5, 49, 167.05, 14.68, 0.00011, 50, 90.44, 37.75, 0.18296, 51, 33.94, 35.27, 0.74087, 54, 69.37, -119.58, 0.07278, 53, 91.97, -133.02, 0.00327, 5, 49, 172.63, -2.39, 4e-05, 50, 100, 22.55, 0.11167, 51, 42.31, 19.38, 0.84912, 54, 68.89, -137.53, 0.03784, 53, 87.06, -150.3, 0.00133, 5, 49, 171.51, -17.43, 1e-05, 50, 102.56, 7.68, 0.06184, 51, 43.72, 4.36, 0.92033, 54, 62.77, -151.32, 0.01739, 53, 77.72, -162.14, 0.00043, 4, 50, 101.04, 1.67, 0.05194, 51, 41.75, -1.52, 0.9412, 54, 58.17, -155.48, 0.00676, 53, 72.23, -165.04, 0.0001, 4, 50, 89.8, -2.11, 0.10263, 51, 30.26, -4.43, 0.89527, 54, 46.72, -152.39, 0.00209, 53, 61.9, -159.21, 2e-05, 5, 49, 143.45, -22.79, 0.00024, 50, 76.64, -4.32, 0.2251, 51, 16.97, -5.63, 0.7742, 54, 34.54, -146.93, 0.00046, 53, 51.46, -150.91, 0, 4, 49, 131.87, -21.02, 0.00532, 50, 64.98, -5.41, 0.40583, 51, 5.26, -5.83, 0.58879, 54, 24.24, -141.37, 6e-05, 4, 49, 116.92, -17.96, 0.02831, 50, 49.73, -6.07, 0.59069, 51, -9.99, -5.32, 0.381, 54, 11.19, -133.45, 0, 4, 49, 102.28, -13.72, 0.09366, 50, 34.5, -5.51, 0.70584, 51, -25.14, -3.61, 0.20051, 52, 67.65, -122.43, 0, 4, 49, 87.29, -9.52, 0.22073, 50, 18.93, -5.07, 0.69753, 51, -40.63, -1.98, 0.08173, 52, 59.73, -109.02, 0, 5, 49, 73.85, -6.27, 0.40324, 50, 5.11, -5.18, 0.5695, 51, -54.42, -1.04, 0.02313, 52, 52.28, -97.39, 1e-05, 4, -38.77, 122.17, 0.00412, 5, 49, 56.93, -2.96, 0.59109, 50, -12.11, -6.08, 0.3804, 51, -71.66, -0.62, 0.0038, 52, 42.34, -83.29, 2e-05, 4, -34.06, 105.58, 0.02469, 4, 49, 42.92, -0.95, 0.71092, 50, -26.19, -7.54, 0.20264, 52, 33.61, -72.15, 3e-05, 4, -29.5, 92.18, 0.08642, 4, 49, 28.17, 0.25, 0.70729, 50, -40.79, -9.94, 0.0828, 52, 23.79, -61.08, 3e-05, 4, -23.91, 78.48, 0.20988, 4, 49, 13.29, -0.51, 0.58143, 50, -55.04, -14.29, 0.02348, 52, 12.52, -51.34, 3e-05, 4, -16.5, 65.56, 0.39506, 4, 49, -3.87, -2.56, 0.39117, 50, -71.19, -20.45, 0.00387, 52, -1.3, -40.95, 2e-05, 4, -6.9, 51.19, 0.60494, 3, 49, -19.14, -8.24, 0.20987, 52, -16.24, -34.49, 1e-05, 4, 5.07, 40.14, 0.79012, 3, 49, -33.26, -13.83, 0.08642, 52, -30.31, -28.76, 0, 4, 16.45, 30.08, 0.91358, 2, 52, -16.61, -14.26, 0.00463, 4, -1.28, 20.93, 0.99537, 6, 49, 147.22, 28.2, 0.00936, 50, 67.92, 46.06, 0.4908, 51, 12.12, 45.27, 0.36426, 54, 55.25, -100.18, 0.11741, 53, 83.08, -110.73, 0.01807, 52, 129.11, -123.24, 0.0001, 6, 49, 141.53, 25.78, 0.02385, 50, 62.99, 42.34, 0.5094, 51, 6.92, 41.93, 0.32719, 54, 49.08, -100.54, 0.11588, 53, 77.01, -109.55, 0.02324, 52, 123.33, -121.05, 0.00044, 6, 49, 125, 25.06, 0.06926, 50, 47.13, 37.63, 0.53445, 51, -9.26, 38.44, 0.24977, 54, 33.27, -95.66, 0.11009, 53, 62.9, -100.91, 0.03462, 52, 110.89, -110.14, 0.00181, 6, 49, 107.66, 25.11, 0.16044, 50, 30.29, 33.46, 0.52835, 51, -26.37, 35.57, 0.16051, 54, 16.95, -89.78, 0.09733, 53, 48.54, -91.18, 0.04818, 52, 98.39, -98.11, 0.00521, 6, 49, 88.68, 23.89, 0.30221, 50, 12.17, 27.67, 0.46535, 51, -44.87, 31.18, 0.08551, 54, -1.34, -84.54, 0.0773, 53, 32.12, -81.59, 0.05821, 52, 83.84, -85.86, 0.01142, 6, 49, 69.88, 21.33, 0.47761, 50, -5.44, 20.63, 0.35187, 51, -62.97, 25.5, 0.03679, 54, -19.9, -80.63, 0.05355, 53, 15.1, -73.21, 0.06008, 52, 68.5, -74.71, 0.02011, 6, 49, 49.92, 17.24, 0.65005, 50, -23.81, 11.82, 0.22295, 51, -81.95, 18.12, 0.01231, 54, -40.07, -77.77, 0.03167, 53, -3.73, -65.44, 0.05353, 52, 51.26, -63.86, 0.02949, 6, 49, 31.15, 12.5, 0.77906, 50, -40.87, 2.66, 0.12084, 51, -99.66, 10.29, 0.00302, 54, -59.34, -75.92, 0.01638, 53, -21.95, -58.89, 0.04374, 52, 34.42, -54.3, 0.03697, 6, 49, 14.27, 6.7, 0.8353, 50, -55.83, -7.06, 0.07545, 51, -115.33, 1.73, 0.00074, 54, -77.19, -75.71, 0.01002, 53, -39.19, -54.26, 0.03811, 52, 18.22, -46.81, 0.04037, 5, 49, 153.56, 8.47, 6e-05, 50, 78.86, 28.45, 0.1796, 51, 21.68, 26.88, 0.75771, 54, 54.58, -120.89, 0.06044, 53, 77.31, -130.64, 0.0022, 5, 49, 116.53, 88.8, 0.00062, 50, 23.44, 97.4, 0.15925, 51, -28.32, 99.85, 0.03094, 54, 46.73, -32.78, 0.79502, 53, 91.49, -43.32, 0.01416], "hull": 41, "edges": [0, 80, 4, 6, 6, 8, 14, 16, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 54, 56, 56, 58, 60, 62, 66, 68, 12, 14, 76, 78, 78, 80, 68, 70, 70, 72, 2, 82, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 16, 18, 18, 20, 24, 26, 26, 28, 72, 74, 74, 76, 8, 10, 10, 12, 62, 64, 64, 66, 2, 4, 0, 2, 46, 102, 34, 104], "width": 211, "height": 190}}, "a14": {"a14": {"type": "mesh", "uvs": [0.62503, 0.00486, 0.73265, 0.00463, 0.80138, 0.02162, 0.88408, 0.11551, 0.93521, 0.22047, 0.96596, 0.31409, 0.98287, 0.39299, 0.99677, 0.48527, 1, 0.56421, 1, 0.64608, 0.98887, 0.73778, 0.98544, 0.81121, 0.9865, 0.88411, 1, 0.94306, 0.85598, 0.94932, 0.70501, 0.98212, 0.51343, 0.99706, 0.33408, 0.98604, 0.17106, 0.94767, 0.01224, 0.93579, 0.01279, 0.92095, 0.0793, 0.88166, 0.1356, 0.8301, 0.17855, 0.77, 0.2173, 0.69925, 0.23424, 0.61935, 0.24147, 0.54992, 0.24797, 0.46457, 0.24362, 0.37622, 0.22955, 0.27051, 0.17707, 0.141, 0.10049, 0.04983, 0.16472, 0.01445, 0.52968, 0.01496], "triangles": [26, 7, 8, 27, 6, 7, 6, 28, 5, 11, 23, 10, 10, 24, 9, 25, 8, 9, 14, 15, 22, 13, 14, 12, 11, 12, 14, 23, 11, 14, 15, 16, 22, 22, 16, 17, 19, 20, 18, 20, 21, 18, 21, 22, 18, 23, 14, 22, 22, 17, 18, 10, 23, 24, 24, 25, 9, 25, 26, 8, 26, 27, 7, 27, 28, 6, 3, 4, 29, 3, 29, 30, 28, 29, 4, 5, 28, 4, 0, 2, 3, 3, 33, 0, 0, 1, 2, 30, 32, 33, 31, 32, 30, 30, 33, 3], "vertices": [2, 4, 17.16, -15.26, 0.96296, 17, -20.31, 15.17, 0.03704, 2, 4, 16.9, -23.32, 0.85185, 17, -20.45, 23.24, 0.14815, 3, 4, 13.27, -28.34, 0.62963, 17, -17.08, 28.43, 0.33333, 58, -44.84, 2.38, 0.03704, 3, 4, -5.92, -33.81, 0.37037, 17, 1.82, 34.85, 0.48148, 58, -25.36, 6.71, 0.14815, 3, 4, -27.25, -36.83, 0.14815, 17, 22.97, 38.93, 0.48148, 58, -3.89, 8.47, 0.37037, 4, 4, -46.24, -38.41, 0.03704, 17, 41.86, 41.46, 0.33333, 59, -49.69, 3.37, 0.02635, 58, 15.16, 8.92, 0.60328, 3, 17, 57.78, 42.91, 0.14815, 59, -33.77, 4.88, 0.1161, 58, 31.15, 8.63, 0.73575, 3, 17, 76.41, 44.16, 0.03704, 59, -15.15, 6.2, 0.30627, 58, 49.8, 7.86, 0.65669, 3, 60, -47.76, 10.27, 0.02798, 59, 0.79, 6.68, 0.52687, 58, 65.69, 6.55, 0.44515, 3, 60, -31.25, 9.28, 0.12098, 59, 17.33, 6.93, 0.66677, 58, 82.15, 4.94, 0.21225, 3, 60, -12.81, 7.34, 0.31604, 59, 35.86, 6.37, 0.61487, 58, 100.51, 2.31, 0.06909, 5, 56, 40, 56.15, 0, 57, -18.34, 53.26, 0.00238, 60, 1.98, 6.2, 0.56386, 59, 50.69, 6.33, 0.42307, 58, 115.25, 0.61, 0.01068, 4, 56, 54.57, 58.29, 1e-05, 57, -4.77, 58.97, 0.01775, 60, 16.68, 5.4, 0.77977, 59, 65.42, 6.63, 0.20248, 4, 56, 66.22, 60.97, 2e-05, 57, 5.85, 64.46, 0.06539, 60, 28.63, 5.71, 0.87039, 59, 77.31, 7.82, 0.0642, 4, 56, 68.98, 50.45, 4e-05, 57, 11.15, 54.96, 0.16491, 60, 29.25, -5.15, 0.826, 59, 78.73, -2.96, 0.00905, 3, 56, 77.13, 40.17, 4e-05, 57, 21.6, 47.04, 0.31925, 60, 35.19, -16.85, 0.68071, 3, 56, 82.14, 26.36, 3e-05, 57, 29.89, 34.92, 0.50755, 60, 37.34, -31.37, 0.49242, 3, 56, 81.82, 12.73, 1e-05, 57, 32.98, 21.64, 0.69375, 60, 34.32, -44.67, 0.30624, 3, 56, 75.86, -0.46, 0, 57, 30.5, 7.38, 0.84248, 60, 25.85, -56.41, 0.15752, 3, 56, 75.15, -12.59, 0.00024, 57, 32.84, -4.54, 0.93733, 60, 22.75, -68.16, 0.06243, 3, 56, 72.18, -12.97, 0.03411, 57, 30.06, -5.65, 0.94869, 60, 19.76, -67.94, 0.0172, 3, 56, 63.62, -9.14, 0.13866, 57, 20.82, -4.08, 0.85904, 60, 12.13, -62.49, 0.0023, 2, 56, 52.72, -6.42, 0.3502, 57, 9.58, -4.17, 0.6498, 3, 55, 103.75, -10.53, 0.03516, 56, 40.25, -4.94, 0.57042, 57, -2.87, -5.84, 0.39442, 3, 55, 89.46, -7.63, 0.14252, 56, 25.69, -4.06, 0.6882, 57, -17.18, -8.62, 0.16927, 3, 55, 73.32, -6.36, 0.35912, 56, 9.53, -5.07, 0.59316, 57, -32.58, -13.63, 0.04772, 4, 17, 90.12, -12.33, 0.03704, 55, 59.29, -5.82, 0.57947, 56, -4.43, -6.5, 0.37985, 57, -45.75, -18.49, 0.00364, 3, 17, 72.87, -12.04, 0.14815, 55, 42.05, -5.33, 0.69246, 56, -21.57, -8.44, 0.1594, 4, 4, -56.7, 16.21, 0.03704, 17, 55.03, -12.57, 0.33333, 55, 24.2, -5.65, 0.58697, 56, -39.2, -11.26, 0.04266, 4, 4, -35.32, 16.45, 0.14815, 17, 33.69, -13.87, 0.48148, 55, 2.85, -6.71, 0.3685, 56, -60.19, -15.3, 0.00187, 3, 4, -9.03, 19.37, 0.37037, 17, 7.57, -18.11, 0.48148, 55, -23.31, -10.65, 0.14815, 3, 4, 9.6, 24.4, 0.62963, 17, -10.78, -24.06, 0.33333, 55, -41.73, -16.39, 0.03704, 2, 4, 16.55, 19.32, 0.85185, 17, -17.98, -19.33, 0.14815, 2, 4, 15.4, -8.03, 0.96296, 17, -18.19, 8.04, 0.03704], "hull": 34, "edges": [0, 66, 0, 2, 2, 4, 4, 6, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 62, 64, 64, 66, 24, 26, 22, 24, 20, 22, 18, 20, 14, 16, 16, 18, 46, 48, 48, 50, 42, 44, 44, 46, 50, 52, 56, 58, 58, 60, 60, 62, 6, 8, 8, 10, 10, 12, 12, 14, 52, 54, 54, 56], "width": 75, "height": 202}}, "dianball/qf_qing_jn3_qtgd_rw_sd_01": {"dianball/qf_qing_jn3_qtgd_rw_sd_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 175, "height": 175}, "dianball/qf_qing_jn3_qtgd_rw_sd_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 175, "height": 175}, "dianball/qf_qing_jn3_qtgd_rw_sd_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 175, "height": 175}, "dianball/qf_qing_jn3_qtgd_rw_sd_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 175, "height": 175}, "dianball/qf_qing_jn3_qtgd_rw_sd_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 175, "height": 175}, "dianball/qf_qing_jn3_qtgd_rw_sd_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 175, "height": 175}, "dianball/qf_qing_jn3_qtgd_rw_sd_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 175, "height": 175}, "dianball/qf_qing_jn3_qtgd_rw_sd_17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 175, "height": 175}, "dianball/qf_qing_jn3_qtgd_rw_sd_19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 175, "height": 175}}, "a16": {"a16": {"type": "mesh", "uvs": [0.6744, 0.0055, 0.84196, 0.1313, 0.9553, 0.34342, 0.9908, 0.5075, 0.92896, 0.56079, 0.65511, 0.66746, 0.57898, 0.7734, 0.59811, 0.80729, 0.58965, 0.83646, 0.65175, 0.87172, 0.7191, 0.87029, 0.8192, 0.77282, 0.9173, 0.83531, 0.9253, 0.94125, 0.89341, 0.98033, 0.83523, 0.99867, 0.29557, 1, 0.22219, 0.98075, 0.25264, 0.89485, 0.30497, 0.84068, 0.27985, 0.81318, 0.29917, 0.77273, 0.25554, 0.61967, 0.08023, 0.55431, 0.01558, 0.48521, 0.01078, 0.32945, 0.05909, 0.15017, 0.18961, 0.08944, 0.50364, 0.00524, 0.51873, 0.55508, 0.44846, 0.83736], "triangles": [15, 9, 10, 15, 16, 9, 9, 16, 30, 30, 18, 19, 30, 8, 9, 17, 18, 16, 30, 16, 18, 14, 15, 13, 13, 15, 10, 10, 12, 13, 10, 11, 12, 19, 20, 30, 8, 30, 7, 7, 30, 6, 20, 21, 30, 30, 21, 6, 6, 21, 5, 21, 22, 5, 22, 29, 5, 5, 29, 4, 22, 23, 29, 4, 29, 3, 24, 29, 23, 2, 29, 24, 2, 3, 29, 25, 1, 2, 25, 26, 1, 26, 27, 1, 2, 24, 25, 1, 28, 0, 1, 27, 28], "vertices": [1, 19, -13.76, 19.42, 1, 1, 19, 9.63, 22.75, 1, 1, 19, 45.7, 18.25, 1, 2, 19, 72.55, 11.65, 0.92511, 20, 0.73, 16.67, 0.07489, 2, 19, 79.81, 5.12, 0.55866, 20, 10.46, 15.94, 0.44134, 2, 19, 91.35, -16.92, 0.00028, 20, 33.07, 5.56, 0.99972, 2, 20, 51.46, 6.9, 0.89994, 30, -1.66, 11.64, 0.10006, 2, 20, 56.43, 9.92, 0.53801, 30, 3.97, 10.16, 0.46199, 2, 20, 61.24, 11.03, 0.1603, 30, 8.11, 7.48, 0.8397, 2, 20, 65.54, 16.68, 0.00431, 30, 15.16, 8.32, 0.99569, 1, 30, 16.85, 12.22, 1, 1, 30, 5.05, 25.2, 1, 1, 30, 17.2, 26.01, 1, 1, 30, 33.33, 18.47, 1, 1, 30, 38.3, 13.73, 1, 1, 30, 39.41, 9.07, 1, 1, 30, 24.34, -21.41, 1, 1, 30, 19.38, -24.09, 1, 1, 30, 7.34, -15.89, 1, 2, 20, 67.82, -5.67, 0.1713, 30, 0.69, -8.86, 0.8287, 2, 20, 63.98, -8.69, 0.57029, 30, -4.15, -8.2, 0.42971, 2, 20, 57.16, -9.78, 0.95854, 30, -9.67, -4.06, 0.04146, 2, 19, 75.77, -38.26, 0.00825, 20, 33.79, -20.85, 0.99175, 2, 19, 61.86, -45.26, 0.09573, 20, 27.07, -34.9, 0.90427, 2, 19, 49.56, -45.45, 0.22875, 20, 17.45, -42.57, 0.77125, 2, 19, 24.64, -37.46, 0.70522, 20, -7.15, -51.49, 0.29478, 2, 19, -2.97, -25.04, 0.97896, 20, -36.59, -58.54, 0.02104, 2, 19, -10.04, -14.01, 0.99747, 20, -48.93, -54.15, 0.00253, 1, 19, -17.2, 9.23, 1, 2, 19, 70.73, -19.1, 0.00073, 20, 18.08, -8.78, 0.99927, 1, 30, 4.25, -0.54, 1], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 6, 58, 16, 60, 60, 38, 38, 40, 40, 42, 36, 38, 14, 16, 16, 18, 18, 20, 10, 12, 12, 14, 34, 36], "width": 63, "height": 168}}, "a17": {"a17": {"type": "mesh", "uvs": [0.7653, 0.0097, 0.79429, 0.04409, 0.82523, 0.09849, 0.85718, 0.17396, 0.89922, 0.28305, 0.93, 0.39146, 0.95819, 0.50351, 0.9781, 0.62148, 0.98832, 0.71246, 0.9944, 0.80288, 1, 0.90358, 1, 1, 0.96287, 0.97997, 0.89742, 0.95634, 0.81738, 0.94735, 0.7121, 0.94315, 0.62397, 0.92289, 0.53024, 0.88375, 0.45504, 0.84382, 0.3854, 0.80678, 0.30537, 0.76646, 0.21773, 0.71403, 0.13647, 0.66445, 0.06417, 0.63245, 0, 0.61035, 0.00427, 0.58664, 0.06382, 0.54058, 0.13054, 0.50425, 0.20121, 0.47319, 0.27971, 0.44107, 0.3617, 0.39987, 0.44607, 0.3308, 0.52867, 0.25141, 0.59944, 0.17125, 0.68039, 0.06935, 0.74171, 0.00087, 0.74373, 0.18471, 0.73509, 0.3425, 0.72299, 0.4948, 0.70915, 0.64848, 0.69014, 0.79804], "triangles": [9, 13, 8, 11, 12, 10, 12, 13, 10, 10, 13, 9, 13, 14, 8, 38, 31, 32, 37, 32, 33, 6, 39, 38, 38, 37, 5, 38, 39, 31, 16, 40, 15, 15, 40, 14, 40, 17, 39, 20, 30, 19, 18, 19, 30, 37, 36, 3, 36, 2, 3, 37, 33, 36, 36, 33, 34, 34, 35, 36, 36, 1, 2, 36, 0, 1, 36, 35, 0, 31, 39, 18, 16, 17, 40, 39, 17, 18, 39, 14, 40, 4, 37, 3, 38, 32, 37, 38, 5, 6, 37, 4, 5, 39, 6, 7, 8, 39, 7, 39, 8, 14, 20, 29, 30, 30, 31, 18, 21, 29, 20, 22, 28, 21, 24, 25, 23, 25, 26, 23, 23, 26, 27, 23, 27, 22, 21, 28, 29, 22, 27, 28], "vertices": [6, 50, -129.6, -31.55, 0.00201, 49, -63.23, 0.84, 0.10539, 4, 16.93, -3.29, 0.79053, 46, -48.61, -17.75, 0.098, 47, -114.16, -25.95, 0.00278, 48, -158.87, -37.06, 0.00129, 6, 50, -135.71, -23.78, 0.00037, 49, -67.27, 9.86, 0.05295, 4, 10.72, -10.97, 0.75416, 46, -40.23, -12.51, 0.18136, 47, -106.16, -20.15, 0.01067, 48, -151.29, -30.72, 0.00049, 6, 50, -141.43, -12.55, 4e-05, 49, -70.09, 22.15, 0.02229, 4, 1.04, -19.05, 0.66428, 46, -28.45, -8.03, 0.28152, 47, -94.71, -14.87, 0.03125, 48, -140.24, -24.66, 0.00062, 5, 49, -71.4, 37.73, 0.00762, 4, -12.26, -27.27, 0.5356, 46, -13.19, -4.59, 0.3802, 47, -79.73, -10.41, 0.07377, 48, -125.6, -19.16, 0.00282, 5, 49, -72.3, 59.71, 0.00201, 4, -31.45, -38.01, 0.39195, 46, 8.45, -0.68, 0.45053, 47, -58.4, -5.03, 0.14479, 48, -104.69, -12.32, 0.01072, 5, 49, -70.57, 80.09, 0.00037, 4, -50.41, -45.69, 0.25786, 46, 28.88, 0.4, 0.4692, 47, -38.1, -2.55, 0.24082, 48, -84.61, -8.44, 0.03175, 5, 49, -67.91, 100.67, 4e-05, 4, -69.96, -52.64, 0.15083, 46, 49.63, 0.6, 0.42891, 47, -17.41, -0.93, 0.3437, 48, -64.08, -5.39, 0.07652, 5, 51, -210.51, 101.5, 0, 4, -90.45, -57.3, 0.07775, 46, 70.51, -1.68, 0.34298, 47, 3.58, -1.78, 0.42422, 48, -43.09, -4.78, 0.15504, 5, 51, -207.93, 117.19, 4e-05, 4, -106.19, -59.48, 0.03666, 46, 86.11, -4.74, 0.23848, 47, 19.35, -3.77, 0.45486, 48, -27.22, -5.67, 0.26995, 5, 51, -204.32, 132.41, 0.00023, 4, -121.8, -60.54, 0.02244, 46, 101.21, -8.82, 0.14288, 47, 34.69, -6.81, 0.42422, 48, -11.7, -7.64, 0.41023, 5, 51, -200, 149.25, 0.00088, 4, -139.16, -61.4, 0.03264, 46, 117.91, -13.66, 0.07277, 47, 51.69, -10.5, 0.3437, 48, 5.51, -10.14, 0.55001, 5, 51, -194.48, 164.89, 0.00262, 4, -155.74, -60.77, 0.06995, 46, 133.37, -19.67, 0.03088, 47, 67.52, -15.43, 0.24082, 48, 21.64, -13.96, 0.65573, 5, 51, -186.07, 158.26, 0.00637, 4, -151.9, -50.77, 0.13951, 46, 126.49, -27.87, 0.01058, 47, 61.22, -24.08, 0.1448, 48, 15.96, -23.03, 0.69874, 5, 51, -170.57, 148.49, 0.01333, 4, -147.16, -33.07, 0.24249, 46, 116.23, -43.06, 0.00278, 47, 52.02, -39.94, 0.07377, 48, 7.88, -39.48, 0.66762, 5, 51, -150.48, 139.75, 0.02518, 4, -144.77, -11.3, 0.37022, 46, 106.88, -62.87, 0.00051, 47, 44.05, -60.34, 0.03125, 48, 1.34, -60.39, 0.57285, 5, 51, -123.62, 129.51, 0.04482, 4, -142.94, 17.4, 0.50271, 46, 95.81, -89.4, 5e-05, 47, 34.81, -87.56, 0.01067, 48, -5.99, -88.19, 0.44174, 4, 51, -102.09, 118.21, 0.07759, 4, -138.54, 41.3, 0.61324, 47, 24.33, -109.5, 0.00279, 48, -14.92, -110.8, 0.30638, 4, 51, -80.2, 103.35, 0.13128, 4, -130.83, 66.61, 0.6766, 47, 10.29, -131.93, 0.00051, 48, -27.37, -134.15, 0.19161, 5, 51, -63.13, 90.04, 0.2137, 50, -10.52, 84.97, 5e-05, 4, -123.17, 86.86, 0.67748, 47, -2.37, -149.49, 5e-05, 48, -38.78, -152.54, 0.10872, 4, 51, -47.32, 77.71, 0.3274, 50, 6.19, 73.87, 0.00051, 4, -116.08, 105.62, 0.61547, 48, -49.36, -169.57, 0.05662, 4, 51, -29.02, 63.89, 0.46433, 50, 25.48, 61.49, 0.00279, 4, -108.31, 127.18, 0.50539, 48, -61.15, -189.24, 0.02748, 5, 51, -9.46, 47.43, 0.60424, 50, 46.24, 46.57, 0.01067, 49, 126.31, 33.95, 5e-05, 4, -98.38, 150.74, 0.37247, 48, -75.44, -210.44, 0.01257, 5, 51, 8.62, 32, 0.719, 50, 65.44, 32.57, 0.03125, 49, 141.54, 15.71, 0.00051, 4, -89, 172.58, 0.24387, 48, -88.85, -230.06, 0.00538, 5, 51, 25.4, 20.24, 0.78139, 50, 83.07, 22.12, 0.07377, 49, 156.11, 1.3, 0.00279, 4, -82.74, 192.1, 0.13997, 48, -98.75, -248, 0.00208, 5, 51, 40.65, 10.83, 0.77459, 50, 99, 13.9, 0.14479, 49, 169.56, -10.55, 0.01072, 4, -78.27, 209.46, 0.0692, 48, -106.49, -264.17, 0.00069, 6, 51, 38.2, 7.37, 0.69846, 50, 96.81, 10.26, 0.24082, 49, 166.56, -13.54, 0.0317, 4, -74.24, 208.13, 0.02885, 46, -31.29, -247.34, 0, 48, -110.19, -262.1, 0.00018, 6, 51, 20.23, 5.31, 0.56996, 50, 79.06, 6.84, 0.3437, 49, 148.51, -12.55, 0.07606, 4, -66.95, 191.59, 0.01025, 46, -32.8, -229.32, 0, 48, -114.15, -244.45, 3e-05, 6, 51, 0.97, 5.48, 0.41771, 50, 59.84, 5.54, 0.42422, 49, 129.55, -9.15, 0.15272, 4, -61.41, 173.14, 0.00532, 46, -32.03, -210.08, 2e-05, 48, -116.03, -225.28, 0, 5, 51, -19, 6.86, 0.27211, 50, 39.83, 5.4, 0.45486, 49, 110.1, -4.43, 0.26184, 4, -56.81, 153.66, 0.01114, 46, -30.03, -190.16, 5e-05, 5, 51, -41.04, 8.79, 0.15547, 50, 17.7, 5.63, 0.42422, 49, 88.68, 1.17, 0.38837, 4, -52.11, 132.03, 0.03179, 46, -27.42, -168.18, 0.00015, 6, 51, -64.51, 9.55, 0.07656, 50, -5.76, 4.61, 0.3437, 49, 65.68, 5.87, 0.50239, 4, -45.89, 109.39, 0.07699, 46, -25.92, -144.75, 0.00036, 48, -118.96, -159.74, 0, 6, 51, -90.18, 6.02, 0.03175, 50, -31.09, -0.88, 0.24082, 49, 39.78, 6.7, 0.56882, 4, -34.91, 85.92, 0.15772, 46, -28.66, -118.98, 0.00087, 48, -125.21, -134.59, 2e-05, 6, 51, -115.99, 0.64, 0.01072, 50, -56.41, -8.2, 0.14478, 49, 13.43, 5.74, 0.5625, 4, -22.13, 62.86, 0.27958, 46, -33.23, -93.02, 0.00229, 48, -133.3, -109.5, 0.00012, 6, 51, -138.8, -5.93, 0.00279, 50, -78.65, -16.49, 0.07363, 49, -10.15, 3.09, 0.48007, 4, -9.09, 43.03, 0.43684, 46, -39.09, -70.02, 0.00622, 48, -142.27, -87.52, 0.00044, 7, 51, -165.47, -15.1, 0.00051, 50, -104.55, -27.67, 0.03046, 49, -37.99, -1.47, 0.34118, 4, 7.57, 20.27, 0.61064, 46, -47.43, -43.07, 0.01588, 47, -111.26, -51.13, 4e-05, 48, -154.23, -61.98, 0.00129, 7, 51, -185.18, -20.63, 4e-05, 50, -123.78, -34.69, 0.00758, 49, -58.34, -3.62, 0.17919, 4, 18.7, 3.09, 0.77359, 46, -52.35, -23.2, 0.03617, 47, -117.53, -31.65, 0.00037, 48, -161.83, -42.97, 0.00305, 6, 50, -116.09, -4.01, 0.00201, 49, -43.44, 24.28, 0.10539, 4, -12.92, 3.75, 0.86878, 46, -22.68, -34.13, 0.01559, 47, -87.17, -40.53, 4e-05, 48, -130.93, -49.72, 0.00819, 5, 50, -106.76, 21.58, 0.00037, 49, -28.18, 46.84, 0.05295, 4, -39.95, 7.15, 0.92635, 46, 1.77, -46.16, 0.00575, 48, -105.07, -58.28, 0.01458, 5, 50, -96.75, 46.02, 4e-05, 49, -12.54, 68.12, 0.02231, 4, -66, 11.46, 0.95446, 46, 24.99, -58.72, 0.0018, 48, -80.34, -67.53, 0.02139, 4, 49, 3.62, 89.38, 0.00783, 4, -92.27, 16.25, 0.9647, 46, 48.27, -71.8, 0.00048, 48, -55.49, -77.29, 0.02698, 4, 49, 20.67, 109.32, 0.00328, 4, -117.77, 22.43, 0.96696, 46, 70.37, -85.95, 0.00017, 48, -31.65, -88.27, 0.02959], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 4, 6, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 46, 48, 48, 50, 50, 52, 58, 60, 52, 54, 54, 56, 56, 58, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 44, 46, 42, 44, 40, 42, 36, 38, 38, 40, 26, 28, 6, 8, 8, 10, 12, 14, 14, 16, 24, 26, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80], "width": 273, "height": 172}}, "a18": {"a18": {"type": "mesh", "uvs": [0.78815, 0.00044, 0.92097, 0.03262, 0.9935, 0.07294, 1, 0.13006, 0.98925, 0.22801, 0.96619, 0.31376, 0.94035, 0.38705, 0.91083, 0.46669, 0.87484, 0.5419, 0.84247, 0.60782, 0.80107, 0.66846, 0.74964, 0.72701, 0.68814, 0.78494, 0.6272, 0.83572, 0.56809, 0.87398, 0.49596, 0.91628, 0.4223, 0.94817, 0.32121, 0.97999, 0.22184, 1, 0.11555, 1, 0.11952, 0.98822, 0.17535, 0.97618, 0.24688, 0.95214, 0.29599, 0.9268, 0.33053, 0.88608, 0.34897, 0.84745, 0.30782, 0.79781, 0.24652, 0.73264, 0.20623, 0.68986, 0.19023, 0.6394, 0.20836, 0.58553, 0.12196, 0.59209, 0.04216, 0.59147, 0.00683, 0.58629, 0.00678, 0.57458, 0.06215, 0.57096, 0.12063, 0.54889, 0.16965, 0.52068, 0.20788, 0.48959, 0.24582, 0.44832, 0.28107, 0.40553, 0.20963, 0.4215, 0.15056, 0.44369, 0.09167, 0.47506, 0.06504, 0.51389, 0.07272, 0.54765, 0.0423, 0.55542, 0.01173, 0.52115, 0.00838, 0.4763, 0.02032, 0.43469, 0.06246, 0.39661, 0.1155, 0.36627, 0.07393, 0.33912, 0.19727, 0.32668, 0.29782, 0.3115, 0.39762, 0.2756, 0.46458, 0.22595, 0.50579, 0.134, 0.55919, 0.0532, 0.65542, 0.0036, 0.31166, 0.54963, 0.38905, 0.50412, 0.46225, 0.44836, 0.75716, 0.12063, 0.40413, 0.37375, 0.54787, 0.32658, 0.68934, 0.22975], "triangles": [65, 56, 66, 66, 57, 63, 4, 63, 3, 57, 58, 63, 63, 2, 3, 58, 59, 63, 63, 1, 2, 63, 0, 1, 63, 59, 0, 66, 63, 4, 56, 57, 66, 5, 66, 4, 6, 66, 5, 55, 56, 65, 46, 47, 45, 47, 44, 45, 47, 48, 44, 44, 48, 43, 48, 49, 43, 43, 49, 42, 49, 50, 42, 42, 50, 51, 60, 38, 61, 38, 39, 61, 39, 40, 61, 61, 40, 62, 40, 64, 62, 62, 64, 65, 41, 42, 51, 40, 41, 53, 41, 51, 53, 40, 54, 64, 40, 53, 54, 54, 55, 64, 64, 55, 65, 51, 52, 53, 9, 62, 8, 8, 62, 7, 62, 65, 7, 7, 65, 6, 65, 66, 6, 13, 26, 12, 26, 27, 12, 12, 27, 11, 27, 28, 60, 27, 60, 11, 30, 60, 29, 11, 60, 10, 10, 60, 61, 60, 28, 29, 10, 61, 9, 32, 35, 31, 35, 36, 31, 31, 36, 30, 35, 32, 34, 32, 33, 34, 36, 37, 30, 30, 37, 60, 37, 38, 60, 61, 62, 9, 17, 23, 16, 23, 24, 16, 16, 24, 15, 24, 25, 15, 15, 25, 14, 14, 25, 13, 25, 26, 13, 18, 19, 21, 19, 20, 21, 18, 22, 17, 18, 21, 22, 22, 23, 17], "vertices": [3, 6, 78.58, 38.35, 0.933, 8, 17.28, 47.27, 0.025, 7, 64.13, 48.81, 0.042, 1, 6, 74.42, 18.13, 1, 1, 6, 66.35, 5.81, 1, 1, 6, 51.99, 1.71, 1, 1, 6, 26.69, -2.29, 1, 2, 40, 31.07, 27.99, 0.13067, 6, 4.13, -3.89, 0.86933, 1, 40, 50.55, 27.45, 1, 2, 41, -38.67, 19.21, 0.01986, 40, 71.73, 26.66, 0.98014, 2, 41, -18.52, 21.46, 0.19567, 40, 91.93, 24.77, 0.80433, 2, 41, -0.8, 23.32, 0.51106, 40, 109.65, 23, 0.48894, 2, 41, 16.08, 23.5, 0.82453, 40, 126.22, 19.75, 0.17547, 3, 42, -39.63, 10.96, 0.00015, 41, 32.96, 22.15, 0.98191, 40, 142.48, 15, 0.01795, 2, 42, -22.34, 13.42, 0.08563, 41, 50.21, 19.41, 0.91437, 2, 42, -6.57, 14.79, 0.37779, 41, 65.68, 16.07, 0.62221, 2, 42, 6.47, 14.34, 0.71098, 41, 78.01, 11.8, 0.28902, 2, 42, 21.47, 13.1, 0.95883, 41, 91.98, 6.19, 0.04117, 2, 43, -11.46, 5.43, 0.32983, 42, 34.48, 10, 0.67017, 2, 43, 5.07, 6.83, 0.66316, 42, 49.87, 3.83, 0.33684, 2, 43, 20.06, 5.55, 0.9965, 42, 62.69, -4.05, 0.0035, 1, 43, 33.72, -0.87, 1, 1, 43, 31.9, -3.42, 1, 1, 43, 23.39, -2.88, 1, 2, 43, 11.52, -4.24, 0.83609, 42, 50.66, -8.96, 0.16391, 4, 43, 2.4, -7.25, 0.50367, 42, 41.16, -7.56, 0.49508, 41, 104.71, -19.34, 0.00118, 44, 103.66, 126.85, 7e-05, 4, 43, -6.56, -14.78, 0.17033, 42, 29.76, -10.26, 0.79558, 41, 93.03, -18.57, 0.03286, 44, 93.86, 120.45, 0.00123, 4, 43, -13.23, -22.79, 0.00091, 42, 20.22, -14.41, 0.81002, 41, 82.68, -19.73, 0.18022, 44, 86.29, 113.29, 0.00886, 4, 42, 13.63, -27.01, 0.54835, 41, 72.67, -29.82, 0.41566, 40, 170.81, -43.95, 0.00028, 44, 84.37, 99.21, 0.03571, 4, 42, 5.61, -44.35, 0.28631, 41, 59.9, -44.04, 0.63197, 40, 155.42, -55.28, 0.00273, 44, 82.72, 80.17, 0.07898, 4, 42, 0.35, -55.74, 0.12854, 41, 51.52, -53.37, 0.72291, 40, 145.32, -62.72, 0.01034, 44, 81.64, 67.68, 0.13821, 4, 42, -8.61, -65.66, 0.06465, 41, 40.04, -60.21, 0.68566, 40, 132.69, -67.08, 0.0273, 44, 76.58, 55.31, 0.22239, 4, 42, -21.26, -72.31, 0.02211, 41, 25.99, -62.84, 0.59894, 40, 118.4, -66.8, 0.05644, 44, 66.93, 44.76, 0.32251, 4, 42, -12.34, -80.91, 0.00661, 41, 31.98, -73.68, 0.5742, 40, 122.06, -78.64, 0.03964, 44, 78.24, 39.7, 0.37956, 4, 42, -5.48, -89.92, 0.00099, 41, 35.88, -84.32, 0.56298, 40, 123.73, -89.85, 0.03152, 44, 87.76, 33.55, 0.40451, 4, 42, -3.45, -94.71, 8e-05, 41, 36.41, -89.49, 0.56424, 40, 123.2, -95.02, 0.02785, 44, 91.29, 29.74, 0.40783, 4, 42, -5.85, -96.6, 0.00025, 41, 33.56, -90.59, 0.56375, 40, 120.18, -95.52, 0.02872, 44, 89.68, 27.14, 0.40728, 4, 42, -11.44, -90.99, 0.00124, 41, 29.86, -83.59, 0.55845, 40, 117.99, -87.91, 0.03474, 44, 82.51, 30.51, 0.40556, 4, 42, -21.1, -88.01, 0.00222, 41, 21.51, -77.89, 0.53584, 40, 110.96, -80.64, 0.04864, 44, 72.42, 30.04, 0.4133, 4, 42, -31.19, -87.08, 0.00234, 41, 12.15, -74.03, 0.48128, 40, 102.58, -74.95, 0.06962, 44, 62.61, 27.49, 0.44676, 4, 42, -40.92, -87.81, 0.00135, 41, 2.63, -71.86, 0.3804, 40, 93.7, -70.9, 0.0896, 44, 53.7, 23.5, 0.52865, 4, 42, -52.73, -90.22, 0.00037, 41, -9.36, -70.69, 0.23446, 40, 82.2, -67.32, 0.08123, 44, 43.42, 17.23, 0.68394, 6, 42, -64.6, -93.17, 0, 41, -21.58, -70.01, 0.08215, 40, 70.37, -64.17, 0.05848, 44, 33.25, 10.42, 0.80115, 45, -10, 19.24, 0.01622, 7, -54.59, 96.46, 0.042, 4, 41, -14.06, -77.99, 0.02044, 40, 76.12, -73.51, 0.01514, 44, 44.06, 8.57, 0.68511, 45, -2.05, 11.69, 0.27931, 4, 41, -5.65, -83.75, 0.00083, 40, 83.18, -80.86, 0.00049, 44, 54.24, 9.02, 0.3861, 45, 6.66, 6.4, 0.61258, 3, 44, 65.68, 11.53, 0.06965, 45, 17.56, 2.11, 0.85035, 6, -63.63, 108.37, 0.08, 3, 44, 74.26, 18.11, 6e-05, 45, 28.35, 2.8, 0.92794, 6, -74.34, 109.89, 0.072, 3, 44, 78.01, 26.16, 0, 45, 35.95, 7.4, 0.96667, 8, -144.02, 115.86, 0.03333, 3, 44, 82.75, 25.58, 0, 45, 39.56, 4.28, 0.93333, 8, -146.92, 119.64, 0.06667, 4, 44, 81.68, 15.7, 0, 45, 33.17, -3.33, 0.864, 6, -77.82, 116.87, 0.04, 8, -139.12, 125.8, 0.096, 3, 45, 22.68, -8.55, 0.828, 6, -66.49, 119.85, 0.08, 8, -127.79, 128.78, 0.092, 4, 44, 68.67, -2.78, 0.0632, 45, 12.08, -11.44, 0.736, 6, -55.52, 120.53, 0.112, 8, -116.82, 129.45, 0.0888, 4, 44, 58.32, -8.03, 0.31511, 45, 0.56, -10.04, 0.49129, 6, -44.52, 116.81, 0.104, 8, -105.82, 125.74, 0.0896, 4, 44, 47.74, -10.74, 0.53699, 45, -9.74, -6.41, 0.20461, 6, -35.17, 111.16, 0.176, 8, -96.48, 120.08, 0.0824, 4, 44, 48.98, -19.88, 0.76781, 45, -13.8, -14.69, 0.01699, 6, -29.52, 118.44, 0.128, 8, -90.82, 127.37, 0.0872, 3, 44, 32.41, -13.34, 0.7344, 6, -22.59, 102.03, 0.184, 8, -83.89, 110.96, 0.0816, 4, 40, 45.76, -65.78, 0.00261, 44, 18.21, -9.12, 0.64539, 6, -15.66, 88.94, 0.28, 8, -76.96, 97.86, 0.072, 4, 40, 34.24, -53.3, 0.05048, 44, 1.22, -9.54, 0.41752, 6, -3.46, 77.1, 0.48, 8, -64.77, 86.03, 0.052, 2, 6, 11.23, 70.6, 0.75, 8, -50.07, 79.52, 0.25, 2, 6, 35.93, 70.03, 0.85714, 8, -25.37, 78.96, 0.14286, 2, 6, 58.15, 67.15, 0.9, 8, -3.15, 76.08, 0.1, 2, 6, 73.73, 56.58, 0.93333, 8, 12.43, 65.51, 0.06667, 4, 42, -37.68, -66.55, 0.00821, 41, 11.99, -52.49, 0.48759, 40, 106.79, -53.83, 0.13294, 44, 49.53, 44.6, 0.37127, 4, 42, -53.81, -65.23, 0.00259, 41, -3.04, -46.48, 0.33148, 40, 93.3, -44.9, 0.23931, 44, 33.91, 40.36, 0.42662, 4, 42, -71.68, -66.03, 0.00033, 41, -20.35, -41.98, 0.23618, 40, 77.26, -36.98, 0.30254, 44, 17.38, 33.55, 0.46095, 4, 40, -13.9, -9.41, 0.06028, 44, -63.51, -16.73, 0.01072, 6, 46.99, 35.92, 0.817, 7, 32.54, 46.38, 0.112, 4, 41, -35.58, -56.66, 0.02695, 40, 59.37, -48.26, 0.14204, 44, 14.04, 12.66, 0.71901, 7, -42.74, 81.17, 0.112, 5, 41, -54.38, -42.01, 0.00735, 40, 43.94, -30.1, 0.18766, 44, -9.8, 13.06, 0.43065, 6, -11.88, 53.41, 0.20634, 7, -26.33, 63.88, 0.168, 3, 41, -85.16, -32.29, 0.00034, 6, 17.11, 39.21, 0.74195, 7, 2.66, 49.68, 0.25771], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 92, 94, 102, 104, 112, 114, 114, 116, 116, 118, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 22, 24, 24, 26, 20, 22, 14, 16, 12, 14, 8, 10, 10, 12, 108, 110, 110, 112, 90, 92, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 70, 72, 72, 74, 56, 58, 58, 60, 28, 30, 30, 32, 16, 18, 18, 20, 104, 106, 106, 108, 98, 100, 100, 102, 94, 96, 96, 98, 60, 120, 120, 122, 122, 124, 0, 126, 80, 128, 128, 130, 130, 132, 132, 126], "width": 142, "height": 261}}, "loong2": {"loong": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -201, -125, -201, -125, 201, 125, 201], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 188}}, "shouji/qf_boss_fsf_pg_bz_0": {"shouji/qf_boss_fsf_pg_bz_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}}, "shouji/qf_boss_fsf_pg_bz_1": {"shouji/qf_boss_fsf_pg_bz_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}}, "chongci/A_wenli_wave_0": {"chongci/A_wenli_wave_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}}, "chongci/A_wenli_wave_1": {"chongci/A_wenli_wave_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "chongci/A_wenli_wave_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.2, -29.64, -29.64, 34.2, 34.2, 29.64, 29.64, -34.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}}, "yichu/qf_qing_jn3_qtkz_rwql_bw_00": {"yichu/qf_qing_jn3_qtkz_rwql_bw_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_26": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}, "yichu/qf_qing_jn3_qtkz_rwql_bw_28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138, -137, -137, -137, -137, 138, 138, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "height": 275}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [102, -100, -101, -100, -101, 101, 102, 101], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 40}}, "chongj/changcici1": {"chongj/changcici1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 309, "height": 57}, "chongj/changcici2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 309, "height": 57}, "chongj/changcici3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 309, "height": 57}, "chongj/changcici4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 309, "height": 57}}, "a1": {"a1": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 7, 2, 3, 7, 2, 7, 8, 1, 2, 8, 1, 8, 9, 0, 1, 9], "vertices": [3, 6, 39.7, -35.8, 0.856, 7, 25.25, -25.34, 0.06933, 8, -21.6, -26.88, 0.07467, 3, 6, 36.69, -22.13, 0.77067, 7, 22.24, -11.66, 0.192, 8, -24.61, -13.2, 0.03733, 2, 6, 33.69, -8.45, 0.70933, 7, 19.24, 2.01, 0.29067, 2, 6, 30.69, 5.22, 0.70667, 7, 16.23, 15.68, 0.29333, 2, 6, 27.68, 18.89, 0.75733, 7, 13.23, 29.36, 0.24267, 2, 6, 40.38, 21.68, 0.75733, 7, 25.93, 32.15, 0.24267, 2, 6, 43.38, 8.01, 0.73067, 7, 28.93, 18.47, 0.26933, 2, 6, 46.39, -5.66, 0.73333, 7, 31.94, 4.8, 0.26667, 3, 6, 49.39, -19.34, 0.79467, 7, 34.94, -8.87, 0.168, 8, -11.91, -10.41, 0.03733, 3, 6, 52.39, -33.01, 0.856, 7, 37.94, -22.55, 0.06933, 8, -8.91, -24.09, 0.07467], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 56, "height": 13}}, "chongci/jss_01": {"chongci/jss_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118, -57, -117, -57, -117, 57, 118, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 235, "height": 114}, "chongci/jss_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118, -57, -117, -57, -117, 57, 118, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 235, "height": 114}, "chongci/jss_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118, -57, -117, -57, -117, 57, 118, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 235, "height": 114}, "chongci/jss_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118, -57, -117, -57, -117, 57, 118, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 235, "height": 114}, "chongci/jss_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118, -57, -117, -57, -117, 57, 118, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 235, "height": 114}}, "dianball/qf_qing_jn3_qtgd_tw_sg_00": {"dianball/qf_qing_jn3_qtgd_tw_sg_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -112, -112, -112, -112, 113, 113, 113], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dianball/qf_qing_jn3_qtgd_tw_sg_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113.72, -111.28, -111.28, -111.28, -111.28, 113.72, 113.72, 113.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dianball/qf_qing_jn3_qtgd_tw_sg_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113.72, -111.28, -111.28, -111.28, -111.28, 113.72, 113.72, 113.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dianball/qf_qing_jn3_qtgd_tw_sg_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113.72, -111.28, -111.28, -111.28, -111.28, 113.72, 113.72, 113.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dianball/qf_qing_jn3_qtgd_tw_sg_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113.72, -111.28, -111.28, -111.28, -111.28, 113.72, 113.72, 113.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}}, "a20": {"a20": {"type": "mesh", "uvs": [0.10188, 0, 0.10283, 0, 0.20053, 0.018, 0.28573, 0.08127, 0.33558, 0.14544, 0.35255, 0.20608, 0.36217, 0.28565, 0.32541, 0.38219, 0.28027, 0.45383, 0.37279, 0.45331, 0.46348, 0.45393, 0.49591, 0.43769, 0.53384, 0.36691, 0.62622, 0.44206, 0.70825, 0.54533, 0.82626, 0.54727, 0.88965, 0.63441, 0.9763, 0.77163, 0.99644, 0.89809, 0.9569, 0.89832, 0.88379, 0.80629, 0.90716, 0.98933, 0.85869, 0.99328, 0.8194, 0.94128, 0.67852, 0.82213, 0.63891, 0.95779, 0.59754, 0.99422, 0.56427, 0.94134, 0.52246, 0.85655, 0.49056, 0.69035, 0.48496, 0.58788, 0.46121, 0.58928, 0.43078, 0.60547, 0.37858, 0.67432, 0.27395, 0.72048, 0.09545, 0.71583, 0.0737, 0.65064, 0.09289, 0.53487, 0.10087, 0.48675, 0.07253, 0.36131, 0.01632, 0.22444, 0, 0.11426, 0.0335, 0.033, 0.16186, 0.1676, 0.46517, 0.52763, 0.19775, 0.50873, 0.17485, 0.64121], "triangles": [26, 27, 25, 22, 23, 21, 25, 27, 24, 27, 28, 24, 21, 23, 20, 23, 24, 20, 18, 19, 17, 19, 20, 17, 28, 29, 24, 20, 24, 16, 16, 24, 14, 24, 29, 14, 20, 16, 17, 15, 16, 14, 14, 30, 13, 30, 14, 29, 11, 12, 13, 35, 46, 34, 33, 45, 8, 33, 8, 32, 34, 46, 33, 45, 33, 46, 35, 36, 46, 11, 13, 44, 32, 8, 9, 31, 32, 44, 31, 44, 30, 44, 9, 10, 9, 44, 32, 13, 30, 44, 44, 10, 11, 36, 37, 46, 46, 37, 45, 37, 38, 45, 8, 45, 39, 45, 38, 39, 8, 39, 7, 39, 43, 7, 6, 7, 43, 6, 43, 5, 4, 43, 3, 43, 4, 5, 39, 40, 43, 43, 41, 42, 42, 0, 43, 43, 40, 41, 0, 1, 43, 43, 2, 3, 43, 1, 2], "vertices": [1, 93, -11.45, -5.82, 1, 1, 93, -11.44, -5.72, 1, 1, 93, -8.53, 3.57, 1, 1, 93, -2.42, 11.49, 1, 1, 93, 3.21, 16.02, 1, 1, 93, 8.05, 17.41, 1, 2, 93, 14.21, 18.03, 0.99896, 94, 18.18, 18.59, 0.00104, 2, 93, 20.9, 14.15, 0.94674, 94, 14.24, 11.93, 0.05326, 2, 93, 25.59, 9.55, 0.55588, 94, 9.61, 7.28, 0.44412, 2, 93, 27.02, 18.41, 0.02341, 94, 18.46, 5.78, 0.97659, 2, 94, 27.13, 4.23, 0.99135, 95, -7.09, -2.19, 0.00865, 2, 94, 30.31, 4.92, 0.81069, 95, -5.67, 0.73, 0.18931, 2, 94, 34.25, 9.62, 0.51459, 95, -6.95, 6.73, 0.48541, 2, 94, 42.76, 2.43, 0.0852, 95, 3.96, 8.99, 0.9148, 1, 95, 15.72, 9.17, 1, 1, 95, 24.33, 16.76, 1, 1, 95, 33.83, 16.53, 1, 1, 95, 47.85, 15.32, 1, 1, 95, 56.46, 10.31, 1, 1, 95, 53.62, 7.73, 1, 1, 95, 43.14, 7.56, 1, 1, 95, 55.19, -0.06, 1, 1, 95, 51.92, -3.41, 1, 1, 95, 46.15, -3.37, 1, 1, 95, 29.25, -6.59, 1, 1, 95, 34.07, -15.94, 1, 1, 95, 33.16, -20.45, 1, 1, 95, 27.77, -19.98, 1, 1, 95, 19.95, -18.46, 1, 2, 94, 28.68, -14.04, 0.04976, 95, 8.24, -12.24, 0.95024, 2, 94, 28.6, -6.22, 0.54577, 95, 2.04, -7.48, 0.45423, 2, 94, 26.32, -5.93, 0.83898, 95, 0.41, -9.1, 0.16102, 2, 94, 23.34, -6.65, 0.96846, 95, -0.87, -11.89, 0.03154, 1, 94, 18.04, -10.97, 1, 2, 93, 45.6, 7.93, 0.00354, 94, 7.83, -12.72, 0.99646, 2, 93, 42.42, -9.15, 0.37136, 94, -9.23, -9.41, 0.62864, 2, 93, 37.16, -10.99, 0.52511, 94, -11.02, -4.13, 0.47489, 2, 93, 28.74, -8.71, 0.98299, 94, -8.68, 4.28, 0.01701, 1, 93, 25.23, -7.76, 1, 1, 93, 15.33, -10, 1, 1, 93, 4.12, -14.87, 1, 1, 93, -4.45, -16.01, 1, 1, 93, -10.05, -12.49, 1, 1, 93, 2.13, -0.71, 1, 2, 94, 26.97, -1.35, 0.96274, 95, -2.8, -5.76, 0.03726, 2, 93, 28.42, 1.43, 0.88669, 94, 1.47, 4.51, 0.11331, 2, 93, 38.05, -1.26, 0.2325, 94, -1.3, -5.1, 0.7675], "hull": 43, "edges": [0, 84, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 70, 72, 80, 82, 82, 84, 0, 86, 76, 78, 78, 80, 16, 18, 18, 20, 24, 26, 26, 28, 56, 58, 58, 60, 64, 66, 20, 88, 60, 62, 62, 64, 20, 22, 22, 24, 16, 90, 72, 74, 74, 76, 70, 92, 66, 68, 68, 70], "width": 98, "height": 98}}, "a21": {"a21": {"type": "mesh", "uvs": [0.14219, 0.01397, 0.23188, 0.04198, 0.30494, 0.11272, 0.33726, 0.17232, 0.35834, 0.25807, 0.34257, 0.35176, 0.29975, 0.41115, 0.26211, 0.46647, 0.33057, 0.45984, 0.40518, 0.4579, 0.47664, 0.46351, 0.51872, 0.39989, 0.56289, 0.39964, 0.6113, 0.43965, 0.71389, 0.56466, 0.8254, 0.55611, 0.93315, 0.69947, 0.97042, 0.77344, 0.99216, 0.85406, 0.99118, 0.94703, 0.88666, 0.79885, 0.89671, 0.9932, 0.8581, 0.9918, 0.80409, 0.9195, 0.67297, 0.8218, 0.62551, 0.97896, 0.5862, 0.98931, 0.56418, 0.88937, 0.53316, 0.86984, 0.53288, 0.79329, 0.47785, 0.58227, 0.44327, 0.60601, 0.39523, 0.63901, 0.34178, 0.69188, 0.26683, 0.71698, 0.10422, 0.71671, 0.07369, 0.67685, 0.07347, 0.6321, 0.09022, 0.53972, 0.09654, 0.48394, 0.06846, 0.37168, 0.00925, 0.2276, 0.00424, 0.10335, 0.02901, 0.0418, 0.07182, 0.00708, 0.14005, 0.13446, 0.16978, 0.62174, 0.46269, 0.52196, 0.54481, 0.49384, 0.18164, 0.50218, 0.18164, 0.44043, 0.17714, 0.33018, 0.36381, 0.55609], "triangles": [21, 22, 23, 26, 27, 25, 25, 27, 24, 18, 19, 20, 23, 20, 21, 23, 24, 20, 24, 27, 29, 27, 28, 29, 20, 17, 18, 24, 14, 20, 14, 15, 20, 24, 29, 14, 20, 16, 17, 20, 15, 16, 14, 30, 48, 48, 13, 14, 14, 29, 30, 48, 12, 13, 35, 46, 34, 34, 46, 33, 33, 46, 52, 7, 52, 46, 33, 52, 32, 52, 7, 8, 32, 52, 31, 30, 31, 47, 31, 52, 47, 30, 47, 48, 52, 9, 47, 52, 8, 9, 47, 10, 48, 47, 9, 10, 10, 11, 48, 48, 11, 12, 35, 36, 46, 7, 46, 49, 36, 37, 46, 37, 38, 46, 46, 38, 49, 38, 39, 49, 39, 50, 49, 49, 50, 7, 39, 40, 50, 7, 50, 6, 40, 51, 50, 50, 51, 6, 6, 51, 5, 40, 41, 51, 5, 51, 4, 41, 45, 51, 51, 3, 4, 3, 45, 2, 2, 45, 1, 45, 3, 51, 41, 42, 45, 42, 43, 45, 43, 44, 45, 45, 0, 1, 45, 44, 0], "vertices": [1, 87, -12.71, -1.78, 1, 1, 87, -10.6, 8.64, 1, 1, 87, -4.52, 16.75, 1, 1, 87, 0.74, 20.11, 1, 1, 87, 8.42, 21.94, 1, 2, 87, 17.01, 19.34, 0.99987, 88, 23.35, 17, 0.00013, 2, 87, 22.6, 13.79, 0.96949, 88, 16.76, 12.67, 0.03051, 2, 87, 27.8, 8.89, 0.66265, 88, 10.9, 8.59, 0.33735, 2, 87, 26.88, 17.06, 0.09958, 88, 19.09, 7.81, 0.90042, 2, 87, 26.35, 25.93, 0.00248, 88, 27.87, 6.5, 0.99752, 2, 88, 36.09, 4.6, 0.99923, 89, -7.39, -2.26, 0.00077, 2, 88, 42.69, 9.31, 0.7936, 89, -7.25, 5.86, 0.2064, 2, 88, 47.87, 8.46, 0.65094, 89, -3.48, 9.51, 0.34906, 2, 88, 52.48, 4.01, 0.28638, 89, 2.84, 10.57, 0.71362, 1, 89, 18.42, 9.86, 1, 1, 89, 27.5, 19.66, 1, 1, 89, 44.51, 18.04, 1, 1, 89, 51.73, 15.7, 1, 1, 89, 57.97, 11.59, 1, 1, 89, 62.95, 4.71, 1, 1, 89, 45.94, 6.95, 1, 1, 89, 57.37, -6.44, 1, 1, 89, 53.99, -9.51, 1, 1, 89, 45.44, -8.67, 1, 1, 89, 28.9, -12.31, 1, 1, 89, 33.38, -27.71, 1, 1, 89, 30.58, -31.7, 1, 1, 89, 23.26, -26.2, 1, 1, 89, 19.55, -27.33, 1, 2, 88, 33.96, -25.29, 0.00177, 89, 15.36, -21.75, 0.99823, 2, 88, 33.09, -5.79, 0.72234, 89, -0.83, -10.85, 0.27766, 2, 88, 28.42, -7.17, 0.955, 89, -2.5, -15.43, 0.045, 2, 88, 21.92, -9.1, 0.99974, 89, -4.81, -21.79, 0.00026, 1, 88, 14.27, -12.65, 1, 2, 87, 50.53, 7.48, 0.0258, 88, 4.83, -13.36, 0.9742, 2, 87, 51.28, -11.8, 0.54426, 88, -14.19, -10.11, 0.45574, 2, 87, 47.8, -15.11, 0.64129, 88, -16.72, -6.03, 0.35871, 2, 87, 43.74, -14.79, 0.74829, 88, -15.56, -2.12, 0.25171, 2, 87, 35.27, -12.08, 0.99562, 88, -11.16, 5.61, 0.00438, 1, 87, 30.18, -10.89, 1, 1, 87, 20.12, -13.34, 1, 1, 87, 7.31, -19.23, 1, 1, 87, -3.94, -18.85, 1, 1, 87, -9.65, -15.43, 1, 1, 87, -13.01, -10.08, 1, 1, 87, -1.76, -2.98, 1, 2, 87, 42.34, -3.28, 0.48911, 88, -4.01, -3.12, 0.51089, 2, 88, 32.91, -0.23, 0.99686, 89, -5.41, -7.68, 0.00314, 2, 88, 43.27, 0.6, 0.43467, 89, 0.09, 1.13, 0.56533, 1, 87, 31.43, -0.94, 1, 1, 87, 25.82, -0.45, 1, 1, 87, 15.83, -0.12, 1, 1, 88, 20.43, -1.24, 1], "hull": 45, "edges": [0, 88, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 82, 84, 84, 86, 86, 88, 88, 90, 78, 80, 80, 82, 74, 76, 76, 78, 72, 92, 14, 16, 16, 18, 18, 20, 60, 62, 62, 64, 20, 94, 22, 96, 14, 98, 10, 12, 12, 14, 98, 100, 100, 102, 94, 104], "width": 120, "height": 120}}, "a22": {"a22": {"type": "mesh", "uvs": [0.63809, 0.00181, 0.6919, 0.00209, 0.75663, 0.03421, 0.82012, 0.07509, 0.8255, 0.05633, 0.82532, 0.04253, 0.87618, 0.06821, 0.92884, 0.10323, 0.95194, 0.1364, 0.94911, 0.16728, 0.98203, 0.18951, 0.99999, 0.20969, 0.9962, 0.22215, 0.97466, 0.23918, 0.93513, 0.25333, 0.88279, 0.26173, 0.8279, 0.25786, 0.77772, 0.24194, 0.71596, 0.2188, 0.6643, 0.20132, 0.6344, 0.19608, 0.63218, 0.21664, 0.67312, 0.24872, 0.71687, 0.2791, 0.76634, 0.31346, 0.80261, 0.33865, 0.83332, 0.38168, 0.84935, 0.41994, 0.84923, 0.47317, 0.83464, 0.50121, 0.81526, 0.52555, 0.78574, 0.54601, 0.74697, 0.55903, 0.6837, 0.56416, 0.59326, 0.56293, 0.50525, 0.54631, 0.44567, 0.52381, 0.40257, 0.49774, 0.37315, 0.4816, 0.33936, 0.47136, 0.30479, 0.47632, 0.27432, 0.48834, 0.24636, 0.51191, 0.2232, 0.53948, 0.20629, 0.56577, 0.1962, 0.59938, 0.1984, 0.63523, 0.21715, 0.67653, 0.24631, 0.71391, 0.2834, 0.7491, 0.31907, 0.7751, 0.36361, 0.80006, 0.41236, 0.82734, 0.44682, 0.85035, 0.48132, 0.87746, 0.50936, 0.90445, 0.5272, 0.93282, 0.53517, 0.95309, 0.53155, 0.97268, 0.50502, 0.99008, 0.46707, 1, 0.42946, 1, 0.40099, 0.99281, 0.38692, 0.97853, 0.40023, 0.97578, 0.42448, 0.98473, 0.45393, 0.9806, 0.47642, 0.96573, 0.48719, 0.94581, 0.47803, 0.9273, 0.46001, 0.90595, 0.43445, 0.88855, 0.3975, 0.88383, 0.40547, 0.91464, 0.39053, 0.91387, 0.35827, 0.89547, 0.35384, 0.86703, 0.34285, 0.84427, 0.30778, 0.84609, 0.28911, 0.85943, 0.24538, 0.8444, 0.17543, 0.8032, 0.12681, 0.77456, 0.08452, 0.72047, 0.03864, 0.66179, 0.03732, 0.61144, 0, 0.5966, 0, 0.58114, 0.0074, 0.55148, 0.05562, 0.51159, 0.02446, 0.51275, 0.03259, 0.48499, 0.05737, 0.46038, 0.10313, 0.43898, 0.14231, 0.42067, 0.18706, 0.39974, 0.14976, 0.38787, 0.17553, 0.36921, 0.23901, 0.3743, 0.29853, 0.37907, 0.34299, 0.38263, 0.32906, 0.34976, 0.40364, 0.3618, 0.46009, 0.39072, 0.49937, 0.41085, 0.53569, 0.42946, 0.57304, 0.4486, 0.58067, 0.43272, 0.58898, 0.41542, 0.6197, 0.39836, 0.60163, 0.38293, 0.57692, 0.36181, 0.55879, 0.37535, 0.52789, 0.36159, 0.50867, 0.341, 0.5031, 0.30537, 0.46384, 0.29519, 0.42232, 0.26834, 0.42272, 0.23089, 0.37155, 0.20376, 0.3722, 0.1707, 0.3971, 0.14139, 0.41869, 0.12115, 0.35089, 0.14106, 0.30605, 0.17139, 0.30159, 0.16347, 0.32963, 0.13353, 0.37517, 0.09914, 0.43344, 0.0771, 0.50605, 0.06004, 0.45522, 0.04854, 0.4506, 0.03085, 0.52573, 0.0241, 0.61482, 0.04419, 0.67577, 0.05772, 0.68906, 0.03694, 0.66279, 0.01581, 0.62552, 0.01736, 0.62142, 0.00928, 0.88034, 0.13561, 0.80247, 0.10595, 0.71292, 0.08168, 0.62532, 0.07156, 0.54453, 0.07696, 0.47639, 0.10527, 0.43161, 0.14505, 0.41604, 0.18415, 0.59617, 0.18681, 0.56666, 0.18856, 0.54558, 0.20842, 0.54811, 0.24638, 0.58436, 0.28609, 0.63159, 0.32028, 0.68724, 0.35357, 0.72265, 0.3927, 0.74205, 0.43825, 0.74542, 0.47738, 0.73446, 0.50892, 0.70242, 0.53695, 0.65267, 0.5533, 0.52549, 0.29803, 0.57271, 0.32373, 0.61487, 0.34825, 0.43442, 0.22327, 0.46562, 0.25656, 0.49513, 0.27992, 0.65654, 0.38518, 0.67003, 0.42081, 0.66413, 0.45468, 0.6422, 0.47571, 0.59836, 0.47746, 0.55198, 0.47045, 0.50139, 0.45176, 0.46007, 0.43307, 0.40189, 0.40679, 0.34624, 0.39395, 0.28317, 0.39219, 0.2174, 0.40446, 0.15845, 0.43134, 0.11207, 0.46697, 0.08088, 0.50668, 0.06232, 0.54766, 0.06317, 0.59088, 0.0775, 0.64104, 0.10195, 0.68192, 0.13315, 0.72105, 0.16772, 0.75083, 0.22, 0.78179, 0.27144, 0.80632, 0.32007, 0.82653, 0.37081, 0.84961, 0.42231, 0.87532], "triangles": [67, 68, 58, 59, 67, 58, 66, 67, 59, 62, 64, 65, 63, 64, 62, 61, 65, 66, 62, 65, 61, 60, 66, 59, 61, 66, 60, 70, 71, 54, 70, 54, 55, 69, 70, 55, 69, 55, 56, 68, 69, 56, 68, 56, 57, 58, 68, 57, 190, 77, 189, 51, 190, 189, 52, 190, 51, 76, 77, 190, 191, 52, 53, 190, 52, 191, 72, 76, 190, 191, 53, 54, 191, 72, 190, 71, 191, 54, 72, 191, 71, 75, 76, 72, 74, 75, 72, 73, 74, 72, 188, 187, 49, 188, 49, 50, 189, 50, 51, 188, 50, 189, 80, 187, 188, 78, 80, 188, 81, 187, 80, 189, 78, 188, 78, 189, 77, 79, 80, 78, 185, 184, 47, 185, 47, 48, 83, 184, 185, 186, 185, 48, 82, 83, 185, 82, 185, 186, 187, 186, 48, 187, 48, 49, 81, 186, 187, 82, 186, 81, 183, 182, 45, 183, 45, 46, 85, 182, 183, 84, 85, 183, 184, 183, 46, 184, 46, 47, 84, 183, 184, 83, 84, 184, 180, 92, 179, 91, 92, 180, 89, 91, 180, 90, 91, 89, 180, 179, 43, 181, 89, 180, 44, 181, 180, 181, 88, 89, 43, 44, 180, 182, 181, 44, 88, 181, 182, 87, 88, 182, 86, 87, 182, 45, 182, 44, 85, 86, 182, 95, 97, 98, 96, 97, 95, 177, 95, 98, 178, 94, 95, 178, 95, 177, 178, 179, 93, 178, 93, 94, 92, 93, 179, 41, 177, 40, 178, 177, 41, 42, 178, 41, 179, 178, 42, 43, 179, 42, 100, 101, 102, 176, 98, 99, 175, 100, 102, 99, 100, 175, 176, 99, 175, 177, 98, 176, 174, 175, 102, 39, 176, 175, 39, 175, 174, 40, 176, 39, 177, 176, 40, 38, 39, 174, 174, 102, 103, 173, 174, 103, 173, 103, 104, 172, 104, 105, 173, 104, 172, 172, 105, 171, 38, 174, 173, 37, 38, 173, 37, 173, 172, 36, 37, 172, 171, 36, 172, 107, 170, 106, 171, 105, 106, 168, 169, 107, 169, 170, 107, 171, 106, 170, 35, 36, 171, 35, 171, 170, 34, 35, 170, 170, 169, 159, 34, 170, 159, 34, 159, 33, 156, 169, 168, 30, 157, 156, 157, 169, 156, 29, 30, 156, 158, 169, 157, 31, 157, 30, 32, 158, 157, 159, 169, 158, 31, 32, 157, 33, 159, 158, 33, 158, 32, 25, 154, 153, 154, 25, 26, 167, 166, 154, 109, 166, 167, 155, 154, 26, 155, 26, 27, 167, 154, 155, 167, 108, 109, 168, 167, 155, 167, 107, 108, 168, 107, 167, 28, 155, 27, 156, 155, 28, 168, 155, 156, 29, 156, 28, 152, 22, 23, 161, 151, 152, 162, 161, 152, 153, 152, 23, 153, 23, 24, 162, 152, 153, 113, 114, 161, 111, 113, 161, 162, 111, 161, 112, 113, 111, 110, 111, 162, 166, 162, 153, 110, 162, 166, 25, 153, 24, 166, 153, 154, 109, 110, 166, 21, 150, 149, 150, 164, 149, 117, 118, 164, 165, 164, 150, 151, 21, 22, 151, 150, 21, 160, 165, 150, 116, 117, 164, 116, 164, 165, 151, 160, 150, 115, 165, 160, 116, 165, 115, 152, 151, 22, 161, 160, 151, 114, 115, 160, 114, 160, 161, 144, 128, 129, 144, 129, 143, 122, 127, 128, 122, 128, 144, 123, 126, 127, 123, 127, 122, 145, 122, 144, 121, 122, 145, 123, 124, 125, 123, 125, 126, 146, 121, 145, 120, 121, 146, 148, 144, 143, 148, 145, 144, 145, 149, 146, 148, 143, 147, 147, 142, 20, 119, 120, 146, 148, 149, 145, 21, 147, 20, 163, 146, 149, 119, 146, 163, 118, 119, 163, 164, 163, 149, 149, 148, 147, 118, 163, 164, 149, 147, 21, 136, 0, 1, 137, 138, 0, 137, 0, 136, 135, 136, 1, 135, 1, 2, 130, 131, 132, 129, 130, 132, 142, 133, 134, 143, 132, 133, 143, 133, 142, 129, 132, 143, 141, 135, 2, 134, 135, 141, 142, 134, 141, 147, 143, 142, 20, 142, 141, 19, 20, 141, 6, 4, 5, 3, 4, 6, 141, 2, 3, 140, 141, 3, 139, 6, 7, 3, 6, 139, 140, 3, 139, 19, 141, 140, 18, 19, 140, 17, 18, 140, 139, 17, 140, 139, 7, 8, 9, 139, 8, 139, 15, 16, 16, 17, 139, 15, 139, 9, 14, 15, 9, 12, 10, 11, 10, 14, 9, 13, 10, 12, 13, 14, 10], "vertices": [2, 69, -39.35, 39.83, 0.3848, 70, 23.76, 63.92, 0.6152, 2, 69, -27.01, 48.12, 0.3999, 70, 37.38, 59.55, 0.6001, 2, 69, -6.69, 44.35, 0.55803, 70, 50.97, 39.21, 0.44197, 3, 68, -24.73, 28.65, 0.07254, 69, 14.85, 36.59, 0.88861, 70, 63.47, 14.81, 0.03885, 3, 68, -28.37, 36.58, 0.10528, 69, 12.85, 45.55, 0.89066, 70, 66.48, 23.29, 0.00406, 3, 68, -31.8, 41.46, 0.10207, 69, 10.44, 51.49, 0.89737, 70, 67.64, 29.85, 0.00056, 2, 68, -16.22, 44.05, 0.19101, 69, 26.48, 48.33, 0.80899, 2, 68, 1.99, 43.74, 0.51345, 69, 44.53, 41.41, 0.48655, 3, 67, -22.56, 34.42, 0.01181, 68, 14.36, 37.25, 0.74899, 69, 55.52, 30.67, 0.2392, 3, 67, -14.42, 23.79, 0.12544, 68, 21.44, 25.59, 0.81711, 69, 60.19, 16.87, 0.05745, 3, 67, -2.68, 25.05, 0.5529, 68, 32.9, 25.28, 0.44467, 69, 71.54, 12.4, 0.00243, 2, 67, 5.98, 23.15, 0.80239, 68, 41.14, 22.23, 0.19761, 2, 67, 8.81, 18.19, 0.86849, 68, 43.51, 16.92, 0.13151, 2, 67, 9.94, 7.24, 0.99353, 68, 43.78, 5.88, 0.00647, 3, 67, 7.22, -7.37, 0.85723, 68, 40.06, -8.3, 0.14277, 73, 1.64, -102.6, 0, 5, 67, 0.75, -23.4, 0.19348, 68, 32.6, -23.38, 0.79367, 69, 61.29, -34.36, 0.01175, 70, 62.99, -78.7, 0.00111, 73, 6.65, -85.72, 0, 6, 67, -9.57, -36.14, 0.0148, 68, 21.65, -34.67, 0.86924, 69, 48.09, -41.27, 0.10127, 70, 49.41, -72.54, 0.01397, 71, 17.94, -94.59, 0.00072, 73, 15.71, -71.53, 0, 5, 68, 8.6, -40.59, 0.61346, 69, 33.88, -42.23, 0.31144, 70, 38.09, -61.03, 0.06582, 71, 18.39, -75.94, 0.00927, 73, 27.71, -62, 0, 6, 68, -8.34, -46.61, 0.19699, 69, 15.79, -41.87, 0.46873, 70, 24.47, -45.18, 0.26808, 71, 20.15, -52.32, 0.06506, 72, 11.13, -50.97, 0.00114, 73, 43.54, -51.25, 0, 5, 68, -22.05, -52.31, 0.03356, 69, 0.98, -42.39, 0.23393, 70, 12.91, -32.81, 0.44695, 71, 20.99, -32.92, 0.25017, 72, 24.16, -41.11, 0.03539, 6, 68, -28.78, -57.35, 0.00573, 69, -6.75, -44.8, 0.07081, 70, 5.79, -27.98, 0.29505, 71, 19.79, -22.61, 0.50274, 72, 30.16, -34.09, 0.12483, 73, 62.1, -34.88, 0.00084, 6, 68, -24.13, -65.19, 0.00073, 69, -3.72, -54.04, 0.01795, 70, 3.42, -37.55, 0.08654, 71, 12.5, -25.81, 0.41157, 72, 23.97, -27.93, 0.4623, 73, 56.25, -28.56, 0.02091, 5, 69, 11.15, -61.52, 0.00027, 70, 10.99, -56.01, 0.00383, 71, 5.58, -44.64, 0.0641, 72, 7.87, -30.84, 0.60872, 73, 40.72, -31.04, 0.32308, 4, 71, -0.48, -64.02, 0.00328, 72, -8.09, -34.99, 0.20452, 73, 25.3, -34.77, 0.7788, 74, 66.71, -19.53, 0.0134, 3, 72, -26.14, -39.69, 0.01562, 73, 7.86, -38.99, 0.73933, 74, 51.79, -32.19, 0.24506, 2, 73, -4.93, -42.08, 0.44294, 74, 40.85, -41.47, 0.55706, 3, 73, -22.27, -38.68, 0.11673, 74, 23.89, -47.02, 0.87957, 75, 84.72, 3.4, 0.0037, 3, 73, -36.07, -32.51, 0.01451, 74, 9.42, -48.24, 0.92006, 75, 76.71, -13.88, 0.06543, 2, 74, -9.47, -42.53, 0.66506, 75, 60.61, -32.71, 0.33494, 2, 74, -18.62, -34.73, 0.45125, 75, 48.92, -39.25, 0.54875, 2, 74, -26.2, -25.75, 0.25851, 75, 37.3, -43.37, 0.74149, 2, 74, -31.84, -13.83, 0.1099, 75, 24.6, -43.76, 0.8901, 3, 74, -34.33, 0.34, 0.02431, 75, 12.11, -39.36, 0.97199, 76, 64.23, -0.28, 0.00371, 2, 75, -3.41, -26.47, 0.82794, 76, 51.09, -12.31, 0.17206, 3, 75, -23, -5.02, 0.07298, 76, 30.89, -25.79, 0.92112, 77, 62.97, -30.39, 0.0059, 2, 76, 8.7, -32.22, 0.71865, 77, 39.74, -33.36, 0.28135, 3, 76, -8.17, -31.7, 0.22149, 77, 22.54, -30.23, 0.77487, 78, 47.02, -38.67, 0.00364, 3, 76, -22, -27.07, 0.01633, 77, 8.82, -23.5, 0.84833, 78, 35.77, -26.08, 0.13534, 2, 77, -0.33, -19.66, 0.45539, 78, 28.09, -18.3, 0.54461, 3, 77, -9.75, -19.04, 0.0558, 78, 19.31, -13.41, 0.91691, 79, 41.21, -26.83, 0.02729, 3, 78, 10.37, -15.94, 0.75003, 79, 33.13, -20.9, 0.24913, 80, 50.07, -42.04, 0.00084, 3, 78, 2.52, -21.9, 0.26366, 79, 24.07, -18.46, 0.7095, 80, 44.1, -33.18, 0.02683, 3, 78, -4.64, -33.49, 0.03251, 79, 12.6, -20.81, 0.72232, 80, 34.07, -26.33, 0.24516, 3, 78, -10.54, -47.02, 9e-05, 79, 1.06, -25.69, 0.2763, 80, 22.86, -21.47, 0.72361, 3, 79, -8.91, -31.51, 0.03915, 80, 12.44, -18.54, 0.87721, 81, 38.56, -23.87, 0.08365, 2, 80, -0.26, -18.61, 0.29687, 81, 27.21, -16.55, 0.70313, 2, 80, -13.12, -22.97, 0.00568, 81, 14.3, -12.99, 0.99432, 2, 81, -1.59, -14.2, 0.27775, 82, 28.48, -12.58, 0.72225, 3, 81, -16.72, -19.3, 0.00013, 82, 13.17, -10.36, 0.99428, 83, 45.36, -13.06, 0.00559, 2, 82, -2.61, -10.95, 0.25772, 83, 29.75, -10.25, 0.74228, 1, 83, 16.78, -10.18, 1, 2, 83, 2.55, -12.63, 0.5954, 84, 35.75, -12.76, 0.4046, 1, 84, 20.22, -14.56, 1, 2, 84, 8.33, -14.52, 0.95202, 85, 45.14, -10.91, 0.04798, 2, 84, -4.6, -13.04, 0.2765, 85, 31.68, -14.25, 0.7235, 2, 84, -16.34, -10.09, 0.00131, 85, 19.12, -15.73, 0.99869, 1, 85, 7.47, -13.89, 1, 3, 84, -33.05, 1, 0, 85, -0.21, -11.19, 0.85231, 86, 42.17, 1.34, 0.14768, 2, 85, -6.13, -5.29, 0.46895, 86, 38.51, -7.05, 0.53105, 2, 85, -8.28, 6.75, 0.00196, 86, 29.66, -11.51, 0.99804, 2, 85, -6.46, 20.26, 0.00567, 86, 19.12, -11.13, 0.99433, 2, 85, -1.45, 31.21, 0.00071, 86, 10.07, -6.31, 0.99929, 1, 86, 4.25, 0.58, 1, 2, 85, 11.24, 38.28, 0.00012, 86, 2.9, 8.83, 0.99988, 2, 85, 10.36, 33.72, 0.00014, 86, 6.49, 8.37, 0.99986, 1, 86, 11.05, 1.22, 1, 1, 86, 18.72, -0.69, 1, 2, 85, 3.49, 9.05, 0.02614, 86, 26.24, 3.15, 0.97386, 2, 84, -22.63, 9.66, 4e-05, 85, 8.56, 0.98, 0.99996, 1, 85, 15.83, -0.94, 1, 2, 84, -7.85, 1.98, 0.07792, 85, 25.21, -0.98, 0.92208, 2, 84, 1.05, 1.83, 0.63375, 85, 34.31, 2.15, 0.36625, 1, 84, 8.86, 8.82, 1, 1, 84, -0.23, 17.8, 1, 1, 84, 2.65, 21.03, 1, 1, 84, 13, 22.09, 1, 1, 84, 20.86, 13.11, 1, 2, 83, -5.4, 7.36, 0.17725, 84, 28.49, 7.66, 0.82275, 2, 83, 0.19, 16.54, 0.82513, 84, 34.33, 16.51, 0.17487, 2, 83, -0.06, 25.6, 0.91454, 84, 34.37, 25.58, 0.08546, 3, 82, -25.65, 26.01, 0.02612, 83, 11.44, 31.2, 0.96343, 84, 45.95, 30.52, 0.01045, 2, 82, -3.4, 34.08, 0.5013, 83, 34.32, 34.37, 0.4987, 3, 81, -30.89, 27.11, 0.01881, 82, 12.07, 39.69, 0.85596, 83, 50.22, 36.58, 0.12523, 3, 81, -8.98, 34.51, 0.3999, 82, 34.24, 36.5, 0.59942, 83, 71.67, 28.65, 0.00068, 3, 80, -31.24, 27.16, 0.04442, 81, 14.79, 42.54, 0.89154, 82, 58.28, 33.04, 0.06404, 3, 80, -13.09, 32.7, 0.41263, 81, 32.81, 36.96, 0.58729, 82, 73.5, 19.48, 7e-05, 2, 80, -9.7, 46.54, 0.62683, 81, 40.42, 47.42, 0.37317, 2, 80, -4.1, 48.11, 0.67514, 81, 45.93, 45.57, 0.32486, 3, 79, -44.59, 18.27, 0.00138, 80, 7.03, 48.67, 0.8024, 81, 56.04, 39.6, 0.19622, 3, 79, -24.99, 22.05, 0.12816, 80, 24.04, 36.77, 0.85632, 81, 67.25, 19.03, 0.01552, 3, 79, -31.44, 28.6, 0.1785, 80, 21.96, 46.95, 0.82145, 81, 68.78, 29.38, 5e-05, 2, 79, -22.82, 36.93, 0.23299, 80, 32.44, 47.08, 0.76701, 2, 79, -11.72, 40.36, 0.396, 80, 42.67, 41.38, 0.604, 3, 78, -41.99, 1.64, 0.00043, 79, 2.72, 37.93, 0.70598, 80, 52.85, 28.42, 0.29359, 3, 78, -31.91, 10.71, 0.03109, 79, 15.09, 35.84, 0.90068, 80, 61.57, 17.32, 0.06823, 3, 78, -20.39, 21.06, 0.25579, 79, 29.21, 33.46, 0.74384, 80, 71.53, 4.65, 0.00037, 3, 78, -30.09, 26.73, 0.29229, 79, 24.84, 46.16, 0.7077, 80, 73.84, 18.18, 0, 2, 78, -23.48, 35.92, 0.30818, 79, 34.64, 47.2, 0.69182, 2, 78, -7.03, 33.64, 0.53193, 79, 45.89, 31.12, 0.46807, 3, 77, -31.61, 18.18, 0.00506, 78, 8.41, 31.5, 0.89311, 79, 56.45, 16.04, 0.10184, 3, 77, -20.5, 21.88, 0.10307, 78, 19.94, 29.9, 0.89368, 79, 64.33, 4.77, 0.00325, 2, 77, -28.14, 35.21, 0.1403, 78, 16.21, 45.89, 0.8597, 2, 77, -8.73, 38.65, 0.32941, 78, 35.57, 40.26, 0.67059, 2, 77, 8.56, 32.22, 0.77038, 78, 50.29, 26.32, 0.22962, 4, 74, 31.89, 66.24, 0.003, 76, -14.9, 25.61, 0.04125, 77, 20.59, 27.75, 0.92702, 78, 60.54, 16.62, 0.02872, 5, 73, 6.19, 57.33, 0.0008, 74, 23.29, 56.24, 0.02843, 75, 4.58, 55.65, 0.00537, 76, -3.81, 23.17, 0.29464, 77, 31.72, 23.61, 0.67076, 5, 73, -4.93, 52.26, 0.01427, 74, 14.44, 45.96, 0.16084, 75, 7.05, 40.19, 0.05887, 76, 7.6, 20.66, 0.67143, 77, 43.16, 19.35, 0.09458, 5, 73, -1.26, 45.73, 0.05425, 74, 19.66, 41.75, 0.33131, 75, 13.53, 44.04, 0.08156, 76, 6.67, 28.73, 0.50989, 77, 42.9, 27.51, 0.02299, 5, 73, 2.74, 38.62, 0.12458, 74, 25.34, 37.17, 0.42757, 75, 20.59, 48.24, 0.07565, 76, 5.66, 37.52, 0.36995, 77, 42.62, 36.4, 0.00226, 5, 72, -31.76, 24.51, 0.00321, 73, 3.45, 25.34, 0.37057, 74, 29.71, 25.22, 0.45481, 75, 32.52, 47.15, 0.02883, 76, 9.64, 49.68, 0.14258, 5, 72, -24.25, 25.45, 0.03111, 73, 10.69, 26.08, 0.7179, 74, 36.18, 29.54, 0.1875, 75, 33.19, 56.81, 0.00714, 76, 3.11, 53.57, 0.05637, 5, 72, -13.98, 26.73, 0.21288, 73, 20.58, 27.09, 0.75916, 74, 45.04, 35.44, 0.01958, 75, 34.11, 70.04, 0.00012, 76, -5.83, 58.9, 0.00826, 4, 72, -15.64, 35.48, 0.30475, 73, 19.13, 35.89, 0.69185, 74, 41.23, 42.86, 0.00116, 76, -7.61, 50.22, 0.00224, 3, 72, -6.8, 40.49, 0.39616, 73, 27.7, 40.66, 0.60316, 76, -16.71, 51.39, 0.00068, 3, 71, -42.61, -10.88, 0.00056, 72, 2.51, 40.37, 0.53485, 73, 36.66, 40.29, 0.46459, 3, 71, -30.92, -2.4, 0.03748, 72, 14.6, 32.34, 0.80763, 73, 48.16, 31.95, 0.15489, 3, 71, -31.36, 11.76, 0.14885, 72, 23.52, 40.67, 0.82529, 73, 56.87, 40.04, 0.02586, 2, 71, -26.29, 29.77, 0.31694, 72, 38.04, 45.15, 0.68306, 2, 71, -13.38, 36.74, 0.59196, 72, 49.84, 34.95, 0.40804, 2, 71, -9.18, 57.82, 0.81747, 72, 65.85, 42.08, 0.18253, 3, 70, -58.44, 4.72, 0.00168, 71, 2.24, 63.87, 0.87324, 72, 76.23, 32.99, 0.12508, 3, 70, -49.56, 16.67, 0.02047, 71, 14.81, 61.67, 0.91433, 72, 81.9, 18.08, 0.0652, 3, 70, -42.31, 24.58, 0.06662, 71, 23.93, 58.77, 0.91804, 72, 85.17, 6.54, 0.01534, 2, 70, -61.24, 20.47, 0.01515, 71, 10.3, 76.12, 0.98485, 2, 71, -4.62, 84.35, 1, 72, 85.6, 51.82, 0, 3, 70, -75.7, 13.72, 0.00042, 71, -2.35, 87.24, 0.99958, 72, 88.76, 50.95, 0, 2, 70, -65.97, 25.72, 0.01146, 71, 10.75, 84.17, 0.98854, 3, 70, -51.41, 38.45, 0.05512, 71, 27.13, 76.49, 0.94436, 72, 98.45, 12.89, 0.00053, 3, 70, -34.71, 44.32, 0.17136, 71, 40.54, 62.52, 0.82838, 72, 96.98, -9.47, 0.00027, 2, 70, -14.81, 46.7, 0.54452, 71, 53.68, 43.13, 0.45548, 2, 70, -26.68, 56.16, 0.62029, 71, 52.54, 61.14, 0.37971, 2, 70, -26.3, 64.92, 0.62161, 71, 58.16, 65.93, 0.37839, 2, 70, -6.67, 62.2, 0.651, 71, 68, 43.8, 0.349, 3, 69, -37.37, 17.86, 0.00933, 70, 14.15, 45.65, 0.87241, 71, 70.03, 12.24, 0.11826, 3, 69, -21.11, 21.53, 0.22834, 70, 28.41, 34.42, 0.7666, 71, 71.48, -9.31, 0.00505, 2, 69, -21.66, 32.6, 0.38587, 70, 33.6, 43.24, 0.61413, 2, 69, -31.29, 37.64, 0.39131, 70, 28.8, 55.34, 0.60869, 2, 69, -39.54, 31.14, 0.38372, 70, 19.21, 57.54, 0.61628, 2, 69, -41.87, 34, 0.38376, 70, 18.88, 61.7, 0.61624, 2, 68, 1.12, 21, 0.54533, 69, 39.03, 19.82, 0.45467, 3, 68, -20.35, 13.58, 0.03407, 69, 16.13, 20.48, 0.94715, 70, 56.29, 1.56, 0.01878, 2, 69, -8.51, 16.98, 0.40299, 70, 35.72, 20.13, 0.59701, 3, 69, -30.26, 7.66, 0.02049, 70, 14.41, 31.83, 0.91458, 71, 61.67, 3.79, 0.06493, 2, 70, -6.54, 35.64, 0.60079, 71, 51.72, 27.94, 0.39921, 3, 70, -26.29, 27.57, 0.20156, 71, 35.17, 43.8, 0.79614, 72, 81.83, -14, 0.0023, 3, 70, -41.13, 12.22, 0.03136, 71, 17.01, 50.22, 0.91766, 72, 75.73, 9.34, 0.05098, 3, 70, -48.51, -5.11, 0.00096, 71, 2.01, 47.67, 0.84819, 72, 65.61, 24.25, 0.15086, 5, 68, -38.02, -62.88, 0.00035, 69, -17.08, -46.76, 0.01226, 70, -3.09, -20.56, 0.08944, 71, 19.14, -8.95, 0.83908, 72, 38.64, -25.81, 0.05887, 3, 69, -23.53, -52.14, 1e-05, 71, 15.59, -0.09, 0.99943, 72, 42.36, -17.02, 0.00057, 1, 71, 6.65, 2.72, 1, 1, 72, 26.75, 3.77, 1, 1, 72, 8.92, 4.24, 1, 2, 73, 25.2, 0.37, 0.99996, 76, -0.59, 85.38, 4e-05, 1, 73, 7.19, -5.87, 1, 2, 73, -9.66, -4.85, 0.00577, 74, 26.06, -9.35, 0.99423, 1, 74, 8.82, -10.89, 1, 2, 74, -5.26, -7.84, 0.39575, 75, 36.42, -10.07, 0.60425, 2, 74, -15.85, -0.86, 0.04998, 75, 24.48, -18.7, 0.95002, 2, 75, 8.94, -21.18, 0.96805, 76, 50.75, 2.39, 0.03195, 2, 75, -6.98, -15.41, 0.58252, 76, 42.44, -12.41, 0.41748, 3, 71, -26.16, -7.98, 0.02219, 72, 13.68, 24.06, 0.8328, 73, 47.14, 23.69, 0.14501, 3, 72, -1.31, 17.66, 0.45974, 73, 32.63, 17.69, 0.53981, 76, -13.04, 74.76, 0.00045, 4, 72, -15.19, 12.38, 0.05736, 73, 19.2, 12.78, 0.91764, 74, 47.76, 21.48, 0.01708, 76, 0.32, 70.66, 0.00791, 2, 71, -9.59, 34.54, 0.62982, 72, 50.56, 29.6, 0.37018, 3, 71, -17.91, 18.52, 0.30948, 72, 35.49, 29.77, 0.68905, 73, 68.21, 28.82, 0.00147, 3, 71, -22.98, 4.9, 0.11525, 72, 23.81, 27.74, 0.85059, 73, 56.95, 27.1, 0.03415, 4, 73, 2.11, 11.44, 0.52182, 74, 32.36, 11.66, 0.43163, 75, 44.63, 43.25, 0.00564, 76, 15.62, 61.1, 0.04091, 4, 73, -10.53, 17.58, 0.03008, 74, 18.97, 11.01, 0.84277, 75, 36.86, 27.49, 0.04546, 76, 24.46, 47.74, 0.08169, 4, 73, -19.86, 28.62, 0.00238, 74, 7.27, 16.56, 0.47769, 75, 25.32, 16.86, 0.35549, 76, 28.74, 32.14, 0.16444, 4, 73, -23.02, 40.55, 0.00092, 74, 1.01, 26.03, 0.15291, 75, 14.14, 14.51, 0.50361, 76, 27.35, 19.62, 0.34256, 4, 73, -17.24, 53.23, 0.00271, 74, 2.8, 40.68, 0.08823, 75, 3.93, 24.08, 0.11589, 76, 17.95, 12.07, 0.79317, 5, 73, -8.46, 64.18, 0.00136, 74, 7.84, 55.23, 0.03559, 75, -4.2, 37.34, 0.01503, 76, 6.54, 7.92, 0.89173, 77, 40.98, 6.86, 0.05629, 4, 74, 17.25, 69.93, 0.00655, 75, -9.72, 55.72, 0.0004, 76, -7.72, 8.18, 0.07353, 77, 26.43, 9.32, 0.91951, 3, 74, 26.16, 81.57, 9e-05, 77, 14.1, 12.9, 0.97775, 78, 50.44, 5.65, 0.02217, 2, 77, -3.25, 17.91, 0.44451, 78, 35.28, 18.29, 0.55549, 3, 77, -18.25, 17.11, 0.08778, 78, 20.82, 24.39, 0.9103, 79, 62.11, -0.09, 0.00192, 2, 78, 4.47, 25.04, 0.83361, 79, 50.1, 14.68, 0.16639, 2, 78, -12.52, 18.85, 0.34685, 79, 34.01, 24.94, 0.65315, 3, 78, -27.69, 5.55, 0.03199, 79, 15.58, 28.32, 0.92301, 80, 58.56, 10.9, 0.045, 2, 79, -2.58, 25.7, 0.5817, 80, 43.2, 22.63, 0.4183, 3, 79, -18.77, 18.18, 0.15735, 80, 27.16, 28.92, 0.83776, 81, 67.42, 10.17, 0.00489, 3, 79, -32.78, 7.36, 0.00972, 80, 11.33, 30.9, 0.8824, 81, 53.98, 21.14, 0.10788, 2, 80, -4.27, 26.24, 0.56571, 81, 38.52, 26.03, 0.43429, 3, 80, -21.66, 16.41, 0.0767, 81, 19.76, 27.32, 0.90627, 82, 58.95, 16.48, 0.01703, 2, 81, 3.67, 24.19, 0.74552, 82, 43.26, 20.98, 0.25448, 3, 81, -12.22, 18.64, 0.17924, 82, 27.14, 23.12, 0.81631, 83, 63.09, 16.97, 0.00445, 3, 81, -24.98, 10.87, 0.00601, 82, 13.33, 21.74, 0.9185, 83, 49.33, 18.58, 0.07548, 2, 82, -3.35, 15.75, 0.39391, 83, 32.19, 16.27, 0.60609, 2, 82, -17.99, 8.21, 0.01081, 83, 16.88, 11.99, 0.98919, 2, 83, 3.17, 6.94, 0.80237, 84, 36.98, 6.75, 0.19763, 1, 84, 22.15, 3.01, 1, 1, 84, 6.52, 0.01, 1], "hull": 139, "edges": [0, 276, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 108, 110, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 190, 192, 192, 194, 200, 202, 202, 204, 216, 218, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 32, 34, 34, 36, 18, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 38, 40, 40, 42, 218, 220, 220, 222, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 198, 200, 194, 196, 196, 198, 188, 190, 184, 186, 186, 188, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 164, 166, 166, 168, 160, 162, 162, 164, 100, 102, 102, 104, 104, 106, 106, 108, 140, 142, 142, 144, 128, 130, 130, 132, 132, 134, 134, 136, 110, 112, 112, 114, 136, 138, 138, 140, 40, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 316, 316, 318, 320, 322, 322, 324, 292, 326, 326, 328, 328, 330, 330, 320, 324, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 376, 378, 378, 380, 380, 382, 96, 98, 98, 100, 82, 84, 84, 86, 78, 80, 80, 82, 74, 76, 76, 78], "width": 338, "height": 488}}, "a23": {"a23": {"type": "mesh", "uvs": [0.05236, 0.00282, 0.1734, 0.0905, 0.25653, 0.34162, 0.32884, 0.3383, 0.38674, 0.34097, 0.40456, 0.26254, 0.43651, 0.14428, 0.57591, 0.17058, 0.79564, 0.19726, 0.82523, 0.15385, 0.89006, 0.18348, 0.94465, 0.18393, 0.99087, 0.316, 0.91796, 0.31663, 0.99085, 0.48318, 0.99222, 0.58712, 0.89635, 0.54993, 0.90062, 0.89707, 0.82209, 0.71215, 0.69966, 0.70969, 0.79836, 0.97182, 0.6702, 0.97736, 0.5547, 0.86811, 0.49969, 0.54697, 0.44219, 0.53399, 0.39177, 0.65328, 0.31054, 0.78234, 0.26923, 0.72078, 0.07398, 0.57693, 0.00833, 0.3983, 0.00912, 0.16623, 0.41636, 0.44, 0.30168, 0.57919, 0.08649, 0.29063], "triangles": [21, 19, 20, 21, 22, 19, 18, 16, 17, 22, 23, 19, 19, 8, 18, 13, 16, 8, 9, 13, 8, 8, 16, 18, 23, 7, 19, 19, 7, 8, 16, 14, 15, 16, 13, 14, 10, 13, 9, 7, 23, 6, 13, 11, 12, 13, 10, 11, 27, 32, 26, 26, 32, 25, 25, 32, 31, 32, 3, 4, 25, 31, 24, 31, 32, 4, 32, 2, 3, 24, 31, 23, 6, 23, 31, 4, 5, 31, 31, 5, 6, 27, 28, 2, 2, 28, 33, 33, 1, 2, 27, 2, 32, 28, 29, 33, 29, 30, 33, 30, 0, 33, 33, 0, 1], "vertices": [1, 96, -2.74, 6.09, 1, 1, 96, 10.6, 8.08, 1, 2, 96, 22.2, 4.42, 0.8883, 97, -2.15, 7.62, 0.1117, 2, 96, 29.45, 7.07, 0.14459, 97, 5.1, 4.95, 0.85541, 2, 96, 35.33, 9.06, 0.00017, 97, 10.87, 2.66, 0.99983, 1, 97, 13.22, 4.21, 1, 1, 97, 17.28, 6.34, 1, 1, 98, 9.56, 5.82, 1, 1, 98, 33.02, 8.51, 1, 1, 98, 35.82, 10.21, 1, 1, 98, 42.91, 10.39, 1, 1, 98, 48.69, 11.23, 1, 1, 98, 54.58, 8.22, 1, 1, 98, 46.87, 7.06, 1, 1, 98, 55.84, 3.49, 1, 1, 98, 56.77, 0.57, 1, 1, 98, 46.34, 0.12, 1, 1, 98, 49.41, -9.63, 1, 1, 98, 39.7, -5.64, 1, 1, 98, 26.72, -7.49, 1, 1, 98, 39.15, -13.35, 1, 1, 98, 25.62, -15.52, 1, 1, 98, 12.57, -14.24, 1, 2, 97, 20.66, -7.49, 0.03119, 98, 4.33, -6.02, 0.96881, 2, 97, 15.01, -4.93, 0.68854, 98, -1.86, -6.56, 0.31146, 2, 97, 9.1, -6.38, 0.99107, 98, -6.29, -10.72, 0.00893, 2, 96, 33.28, -5.27, 0.24933, 97, 0.04, -6.94, 0.75067, 2, 96, 28.32, -5.12, 0.66394, 97, -3.64, -3.62, 0.33606, 1, 96, 6.78, -8.26, 1, 1, 96, -2.13, -5.89, 1, 1, 96, -5.02, 0.25, 1, 2, 97, 13.11, -1.27, 0.95058, 98, -5.3, -4.3, 0.04942, 2, 96, 29.79, -0.23, 0.23405, 97, 0.63, -0.84, 0.76595, 1, 96, 4.38, -0.28, 1], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 52, 54, 54, 56, 56, 58, 58, 60, 46, 48, 48, 50, 50, 52, 8, 62, 8, 10, 10, 12, 4, 6, 6, 8, 52, 64, 0, 66, 12, 14, 14, 16], "width": 108, "height": 38}}, "a24": {"a24": {"type": "mesh", "uvs": [0.10449, 0, 0.15621, 0.0518, 0.22027, 0.15647, 0.25072, 0.30082, 0.33284, 0.34319, 0.39986, 0.37939, 0.4351, 0.34589, 0.46992, 0.31701, 0.61728, 0.48146, 0.69403, 0.48172, 0.79113, 0.59873, 0.86019, 0.58527, 0.95801, 0.66319, 1, 0.7449, 1, 0.75301, 0.9608, 0.75332, 0.97455, 0.93134, 0.84186, 0.8237, 0.83887, 0.91186, 0.83904, 0.98843, 0.8064, 0.98845, 0.79268, 0.93787, 0.63754, 0.72585, 0.63741, 0.84723, 0.68792, 0.92097, 0.66329, 0.94132, 0.60687, 0.94138, 0.47709, 0.77518, 0.45259, 0.55273, 0.41672, 0.52742, 0.3777, 0.54264, 0.24925, 0.57604, 0.19237, 0.46933, 0.10904, 0.38916, 0.04534, 0.30915, 0.00797, 0.22368, 0.00784, 0.14166, 0.03227, 0.05233, 0.09619, 0.17898, 0.4113, 0.44687, 0.25939, 0.42987], "triangles": [19, 20, 18, 20, 21, 18, 26, 23, 25, 26, 27, 23, 25, 23, 24, 18, 21, 17, 17, 15, 16, 22, 17, 21, 23, 27, 22, 22, 10, 17, 17, 12, 15, 17, 11, 12, 17, 10, 11, 27, 28, 22, 14, 15, 13, 15, 12, 13, 28, 8, 22, 22, 9, 10, 22, 8, 9, 28, 7, 8, 31, 40, 30, 31, 32, 40, 7, 28, 39, 7, 39, 6, 40, 4, 30, 30, 39, 29, 39, 4, 5, 39, 30, 4, 28, 29, 39, 40, 32, 3, 39, 5, 6, 40, 3, 4, 32, 33, 3, 34, 38, 33, 3, 38, 2, 3, 33, 38, 34, 35, 38, 35, 36, 38, 36, 37, 38, 38, 1, 2, 38, 0, 1, 38, 37, 0], "vertices": [1, 90, -1.14, 13.06, 1, 1, 90, 4.73, 14.74, 1, 2, 90, 13.7, 14.5, 0.99149, 91, 3.99, 16.44, 0.00851, 2, 90, 21.95, 8.94, 0.47365, 91, 5.96, 6.69, 0.52635, 2, 90, 29.59, 13.9, 0.00036, 91, 14.88, 4.84, 0.99964, 1, 91, 22.14, 3.22, 1, 2, 91, 26.51, 6.07, 0.98322, 92, -6.41, 2.99, 0.01678, 2, 91, 30.78, 8.59, 0.88739, 92, -4.29, 7.47, 0.11261, 1, 92, 13.55, 8.04, 1, 1, 92, 20.2, 13.82, 1, 1, 92, 32.22, 13.64, 1, 1, 92, 37.78, 19.72, 1, 1, 92, 48.66, 22.1, 1, 1, 92, 54.82, 20.03, 1, 1, 92, 55.07, 19.51, 1, 1, 92, 51.68, 16.53, 1, 1, 92, 58.38, 6.14, 1, 1, 92, 43.57, 3.03, 1, 1, 92, 46.04, -2.85, 1, 1, 92, 48.42, -7.75, 1, 1, 92, 45.59, -10.22, 1, 1, 92, 42.84, -8.01, 1, 1, 92, 22.86, -6.11, 1, 1, 92, 26.6, -13.91, 1, 1, 92, 33.25, -14.83, 1, 1, 92, 31.75, -18, 1, 1, 92, 26.87, -22.26, 1, 1, 92, 10.5, -21.39, 1, 2, 91, 26.34, -8.26, 0.30393, 92, 1.5, -8.97, 0.69607, 2, 91, 22.51, -6.98, 0.82407, 92, -2.38, -10.05, 0.17593, 2, 91, 17.9, -8.59, 0.98545, 92, -5.29, -13.98, 0.01455, 1, 91, 2.91, -12.72, 1, 2, 90, 24.89, -5.99, 0.3115, 91, -2.46, -5.99, 0.6885, 1, 90, 15.57, -8.89, 1, 1, 90, 7.66, -10.02, 1, 1, 90, 1.39, -8.48, 1, 1, 90, -2.07, -3.78, 1, 1, 90, -4.09, 3.56, 1, 1, 90, 5.8, 2.03, 1, 2, 91, 22.74, -1.38, 0.98875, 92, -5.34, -5.29, 0.01125, 1, 91, 5.6, -2.28, 1], "hull": 38, "edges": [0, 74, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 10, 78, 10, 12, 12, 14, 6, 8, 8, 10, 60, 62, 56, 58, 58, 60, 6, 80], "width": 124, "height": 85}}, "a25": {"a16": {"type": "mesh", "uvs": [0.6744, 0.0055, 0.84196, 0.1313, 0.9553, 0.34342, 0.9908, 0.5075, 0.92896, 0.56079, 0.65511, 0.66746, 0.57898, 0.7734, 0.59811, 0.80729, 0.58965, 0.83646, 0.65175, 0.87172, 0.7191, 0.87029, 0.8192, 0.77282, 0.9173, 0.83531, 0.9253, 0.94125, 0.89341, 0.98033, 0.83523, 0.99867, 0.29557, 1, 0.22219, 0.98075, 0.25264, 0.89485, 0.30497, 0.84068, 0.27985, 0.81318, 0.29917, 0.77273, 0.25554, 0.61967, 0.08023, 0.55431, 0.01558, 0.48521, 0.01078, 0.32945, 0.05909, 0.15017, 0.18961, 0.08944, 0.50364, 0.00524, 0.51873, 0.55508, 0.44846, 0.83736], "triangles": [15, 9, 10, 15, 16, 9, 9, 16, 30, 30, 18, 19, 30, 8, 9, 17, 18, 16, 30, 16, 18, 14, 15, 13, 13, 15, 10, 10, 12, 13, 10, 11, 12, 19, 20, 30, 20, 21, 30, 8, 30, 7, 7, 30, 6, 30, 21, 6, 6, 21, 5, 21, 22, 5, 22, 29, 5, 5, 29, 4, 22, 23, 29, 24, 29, 23, 2, 29, 24, 4, 29, 3, 2, 3, 29, 25, 1, 2, 25, 26, 1, 26, 27, 1, 2, 24, 25, 1, 28, 0, 1, 27, 28], "vertices": [1, 25, -12.12, 17.72, 1, 1, 25, 10.78, 23.53, 1, 1, 25, 46.07, 18.13, 1, 2, 25, 72.29, 8.97, 0.99647, 26, -1.52, 11.94, 0.00353, 2, 25, 80.21, 3.26, 0.6014, 26, 8.16, 13.24, 0.3986, 1, 26, 34.23, 4.77, 1, 2, 26, 55.21, 4.49, 0.9186, 38, -1.81, 11.33, 0.0814, 2, 26, 59.45, 8.47, 0.48938, 38, 3.82, 9.85, 0.51062, 2, 26, 63.92, 10.56, 0.11907, 38, 7.96, 7.18, 0.88093, 2, 26, 66.96, 16.97, 5e-05, 38, 15.01, 8.01, 0.99995, 1, 38, 16.7, 11.91, 1, 1, 38, 4.9, 24.9, 1, 1, 38, 17.05, 25.71, 1, 1, 38, 33.18, 18.17, 1, 1, 38, 38.15, 13.43, 1, 1, 38, 39.25, 8.77, 1, 1, 38, 24.19, -21.71, 1, 1, 38, 19.22, -24.39, 1, 1, 38, 7.19, -16.2, 1, 2, 26, 73.82, -4.42, 0.14617, 38, 0.54, -9.17, 0.85383, 2, 26, 70.68, -8.17, 0.47973, 38, -4.3, -8.51, 0.52027, 2, 26, 63.7, -9.75, 0.9204, 38, -9.35, -3.43, 0.0796, 2, 25, 80.09, -43.72, 0.01761, 26, 40.4, -20.92, 0.98239, 2, 25, 67.76, -48.75, 0.1011, 26, 34.92, -33.06, 0.8989, 2, 25, 55.55, -50.26, 0.21104, 26, 27.1, -42.56, 0.78896, 2, 25, 31.11, -39.51, 0.64828, 26, 1.97, -51.58, 0.35172, 2, 25, 3.37, -25.33, 0.972, 26, -27.91, -60.38, 0.028, 2, 25, -4.85, -15.13, 0.99626, 26, -40.9, -58.63, 0.00374, 1, 25, -14.45, 7.22, 1, 2, 25, 73.77, -21.79, 0.01322, 26, 20.72, -9.36, 0.98678, 1, 38, 4.1, -0.84, 1], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 6, 58, 16, 60, 60, 38, 38, 40, 40, 42, 36, 38, 14, 16, 16, 18, 18, 20, 10, 12, 12, 14, 34, 36], "width": 63, "height": 168}}, "a15": {"a15": {"type": "mesh", "uvs": [0.74124, 0.00532, 0.87213, 0.03213, 0.97283, 0.07438, 0.98728, 0.12133, 1, 0.41757, 0.98497, 0.55822, 0.96154, 0.60168, 0.83531, 0.63254, 0.6634, 0.63462, 0.44035, 0.7869, 0.41395, 0.80492, 0.41153, 0.82864, 0.40702, 0.9711, 0.2439, 1, 0.1141, 1, 0, 0.97537, 0, 0.89515, 0.0842, 0.81533, 0.14976, 0.81686, 0.17858, 0.78512, 0.20753, 0.76473, 0.2117, 0.73643, 0.2247, 0.52518, 0.17142, 0.4865, 0.15362, 0.43992, 0.15369, 0.36168, 0.20293, 0.27349, 0.42073, 0.07075, 0.5774, 0.00509, 0.54966, 0.50129, 0.30188, 0.80357, 0.72025, 0.08481], "triangles": [13, 14, 16, 13, 16, 18, 13, 18, 12, 12, 18, 11, 16, 14, 15, 16, 17, 18, 18, 19, 30, 11, 18, 30, 11, 30, 10, 10, 30, 9, 19, 20, 30, 9, 30, 21, 30, 20, 21, 9, 21, 8, 21, 22, 8, 22, 29, 8, 8, 29, 7, 7, 29, 6, 6, 29, 5, 5, 29, 4, 22, 23, 29, 29, 23, 24, 26, 29, 25, 29, 26, 4, 4, 31, 3, 25, 29, 24, 4, 26, 31, 31, 2, 3, 31, 1, 2, 26, 27, 31, 27, 28, 31, 31, 0, 1, 31, 28, 0], "vertices": [1, 22, -26.51, -17.47, 1, 1, 22, -28.29, -6.24, 1, 1, 22, -26.37, 4.7, 1, 1, 22, -19.86, 10.64, 1, 2, 22, 24.68, 43.09, 0.72826, 23, -4.44, 58, 0.27174, 2, 22, 46.77, 57.15, 0.38674, 23, 21.65, 55.71, 0.61326, 2, 22, 54.44, 60.29, 0.33241, 23, 29.65, 53.54, 0.66759, 2, 22, 64.79, 55.53, 0.26809, 23, 34.96, 43.46, 0.73191, 2, 22, 72.81, 44.77, 0.13266, 23, 34.78, 30.05, 0.86734, 2, 23, 62.34, 11.46, 0.988, 34, -9.85, 9.66, 0.012, 2, 23, 65.6, 9.26, 0.91153, 34, -6.21, 8.17, 0.08847, 2, 23, 70, 8.89, 0.56035, 34, -1.83, 8.7, 0.43965, 1, 34, 24.38, 12.63, 1, 1, 34, 31.74, 0.94, 1, 1, 34, 33.37, -9.05, 1, 1, 34, 30.29, -18.57, 1, 2, 23, 80.99, -23.71, 0.02401, 34, 15.57, -20.98, 0.97599, 2, 23, 66.44, -16.52, 0.27173, 34, -0.15, -16.9, 0.72827, 2, 23, 66.94, -11.42, 0.45933, 34, -0.69, -11.81, 0.54067, 2, 23, 61.14, -8.92, 0.88551, 34, -6.88, -10.54, 0.11449, 2, 23, 57.45, -6.5, 0.98715, 34, -10.99, -8.93, 0.01285, 1, 23, 52.2, -5.96, 1, 2, 22, 75.79, 5.07, 0.02358, 23, 12.99, -3.27, 0.97642, 2, 22, 72.29, -2.47, 0.43161, 23, 5.62, -7.12, 0.56839, 2, 22, 65.99, -8.58, 0.34014, 23, -3.09, -8.14, 0.65986, 2, 22, 54.07, -16.93, 0.85911, 23, -17.63, -7.51, 0.14089, 2, 22, 38.44, -23.2, 0.99367, 23, -33.86, -2.98, 0.00633, 1, 22, -2.19, -30.95, 1, 1, 22, -19.21, -27.95, 1, 2, 22, 57.6, 23.27, 0.34318, 23, 9.62, 22.24, 0.65682, 1, 23, 64.97, 0.54, 1, 1, 22, -13.47, -10.32, 1], "hull": 29, "edges": [0, 56, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 24, 26, 26, 28, 30, 32, 32, 34, 34, 36, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 12, 58, 20, 60, 16, 18, 18, 20, 40, 42, 42, 44, 36, 38, 38, 40, 20, 22, 22, 24, 28, 30, 12, 14, 0, 2, 0, 62], "width": 78, "height": 186}}, "loong": {"loong": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -201, -125, -201, -125, 201, 125, 201], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 188}}, "dianball/qf_qing_jn3_qtgd_xlsd_00": {"dianball/qf_qing_jn3_qtgd_xlsd_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}, "dianball/qf_qing_jn3_qtgd_xlsd_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.72, -150, -160.28, -150, -160.28, 150, 162.72, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 210}}, "chongci/qf_xishi_jn_slj_dm_00": {"chongci/qf_xishi_jn_slj_dm_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [394, -150, -394, -150, -394, 150, 394, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 120}, "chongci/qf_xishi_jn_slj_dm_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [394, -150, -394, -150, -394, 150, 394, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 120}, "chongci/qf_xishi_jn_slj_dm_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [394, -150, -394, -150, -394, 150, 394, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 120}, "chongci/qf_xishi_jn_slj_dm_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [394, -150, -394, -150, -394, 150, 394, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 120}, "chongci/qf_xishi_jn_slj_dm_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [394, -150, -394, -150, -394, 150, 394, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 120}, "chongci/qf_xishi_jn_slj_dm_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [394, -150, -394, -150, -394, 150, 394, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 120}, "chongci/qf_xishi_jn_slj_dm_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [394, -150, -394, -150, -394, 150, 394, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 120}, "chongci/qf_xishi_jn_slj_dm_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [394, -150, -394, -150, -394, 150, 394, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 120}}, "a0": {"a0": {"x": 1, "y": -0.66, "rotation": -87.54, "width": 91, "height": 91}}, "a10": {"a10": {"type": "mesh", "uvs": [0.41307, 0.02149, 0.46579, 0.02126, 0.46583, 0.17394, 0.37527, 0.40755, 0.51766, 0.39028, 0.65411, 0.21724, 0.77485, 0.21567, 0.9839, 0.45025, 0.98645, 0.52885, 0.68967, 0.60943, 0.5824, 0.82709, 0.2666, 0.97894, 0.10363, 0.979, 0.01664, 0.86946, 0.01631, 0.72053, 0.08231, 0.44281, 0.1514, 0.2696, 0.339, 0.11485, 0.13653, 0.82553], "triangles": [0, 1, 2, 17, 0, 2, 3, 17, 2, 16, 17, 3, 9, 5, 6, 9, 6, 7, 9, 7, 8, 4, 5, 9, 3, 18, 15, 3, 15, 16, 14, 15, 18, 10, 4, 9, 13, 14, 18, 11, 18, 3, 3, 4, 10, 11, 3, 10, 12, 13, 18, 12, 18, 11], "vertices": [1, 16, 31.43, 26.91, 1, 1, 16, 34.32, 25.91, 1, 1, 16, 32, 19.28, 1, 1, 16, 23.49, 10.88, 1, 1, 16, 31.54, 8.89, 1, 1, 16, 41.64, 13.79, 1, 1, 16, 48.28, 11.54, 1, 1, 16, 56.15, -2.66, 1, 1, 16, 55.09, -6.12, 1, 1, 16, 37.62, -3.92, 1, 1, 16, 28.43, -11.31, 1, 1, 16, 8.84, -11.84, 1, 2, 16, -0.08, -8.71, 0.87106, 15, 47.87, -8.68, 0.12894, 2, 16, -3.18, -2.29, 0.15256, 15, 43.78, -2.84, 0.84744, 2, 16, -0.93, 4.19, 0.72264, 15, 44.96, 3.91, 0.27736, 1, 16, 6.91, 14.97, 1, 1, 16, 13.33, 21.17, 1, 1, 16, 25.96, 24.28, 1, 2, 16, 4.05, -2.68, 0.99862, 15, 50.98, -2.07, 0.00138], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 26, 36], "width": 58, "height": 46}}, "a2": {"a2": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [0, 1, 9, 1, 8, 9, 1, 2, 8, 2, 7, 8, 2, 3, 7, 3, 6, 7, 3, 4, 6, 4, 5, 6], "vertices": [3, 6, 40.03, -32.66, 0.87467, 7, 25.58, -22.19, 0.088, 8, -21.27, -23.73, 0.03733, 3, 6, 37.24, -19.96, 0.80533, 7, 22.79, -9.5, 0.176, 8, -24.06, -11.04, 0.01867, 2, 6, 34.45, -7.26, 0.736, 7, 20, 3.2, 0.264, 2, 6, 31.66, 5.43, 0.76533, 7, 17.21, 15.9, 0.23467, 2, 6, 28.87, 18.13, 0.79467, 7, 14.42, 28.6, 0.20533, 2, 6, 39.62, 20.49, 0.79467, 7, 25.17, 30.96, 0.20533, 2, 6, 42.41, 7.79, 0.76533, 7, 27.95, 18.26, 0.23467, 2, 6, 45.2, -4.9, 0.736, 7, 30.74, 5.56, 0.264, 3, 6, 47.98, -17.6, 0.80533, 7, 33.53, -7.14, 0.176, 8, -13.32, -8.67, 0.01867, 3, 6, 50.77, -30.3, 0.87467, 7, 36.32, -19.83, 0.088, 8, -10.53, -21.37, 0.03733], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 52, "height": 11}}, "a3": {"a3": {"type": "mesh", "uvs": [0.4555, 0.00477, 0.53806, 0.06272, 0.62095, 0.1354, 0.68217, 0.09319, 0.72045, 0.09235, 0.70176, 0.28812, 0.78297, 0.39899, 0.89452, 0.35042, 0.93539, 0.34914, 0.93633, 0.37117, 0.87149, 0.4262, 0.88511, 0.56383, 0.95883, 0.64531, 0.9531, 0.69072, 0.98064, 0.7431, 0.99774, 0.78665, 0.99605, 0.82544, 0.98234, 0.85594, 0.96052, 0.87682, 0.93298, 0.89399, 0.90372, 0.88602, 0.87753, 0.90697, 0.87517, 0.938, 0.87317, 0.96405, 0.84703, 0.96879, 0.8439, 0.95584, 0.83841, 0.93766, 0.80699, 0.952, 0.77805, 0.96132, 0.74399, 0.9676, 0.71377, 0.97223, 0.68201, 0.97219, 0.70019, 0.95571, 0.72697, 0.93768, 0.75645, 0.91375, 0.78075, 0.88687, 0.7957, 0.85898, 0.80624, 0.82419, 0.80941, 0.77973, 0.78931, 0.84365, 0.72469, 0.92854, 0.6689, 0.97306, 0.62991, 0.99999, 0.57561, 0.99963, 0.5019, 0.95889, 0.433, 0.9096, 0.41082, 0.86132, 0.31974, 0.83053, 0.23992, 0.80355, 0.18559, 0.76853, 0.14229, 0.73463, 0.18964, 0.62888, 0.25848, 0.5467, 0.2005, 0.50682, 0.18175, 0.46436, 0.10562, 0.41912, 0.02229, 0.39136, 0.02991, 0.37286, 0.14082, 0.37543, 0.24802, 0.3925, 0.21395, 0.30639, 0.09164, 0.2247, 0.00558, 0.1307, 1e-05, 0.10535, 0.03843, 0.10255, 0.14138, 0.13427, 0.04523, 0.03515, 0.04331, 0.00849, 0.09363, 0.01102, 0.13265, 0.03051, 0.20964, 0.08734, 0.29849, 0.20022, 0.33347, 0.17515, 0.37746, 0.33866, 0.48711, 0.44472, 0.64336, 0.43987, 0.5719, 0.3145, 0.51935, 0.17432, 0.46968, 0.09502, 0.40192, 0.02339, 0.4021, 0.00441, 0.34283, 0.5732, 0.43944, 0.57647, 0.49274, 0.65993, 0.49607, 0.68693, 0.51272, 0.47175, 0.55714, 0.47911, 0.62043, 0.55356, 0.64042, 0.60102, 0.63486, 0.65338, 0.65929, 0.66156, 0.71925, 0.6632, 0.78588, 0.70656, 0.79907, 0.67567, 0.87114, 0.69975, 0.92208, 0.73728, 0.95065, 0.77814, 0.9569, 0.81632, 0.94572, 0.84576, 0.92677, 0.86877, 0.28084, 0.73336, 0.33393, 0.67399, 0.40314, 0.62788, 0.40302, 0.78123, 0.38611, 0.72665, 0.33699, 0.70648, 0.30398, 0.74563, 0.30639, 0.79369, 0.30106, 0.45092, 0.37589, 0.48928, 0.6974, 0.48867, 0.72948, 0.55958, 0.69123, 0.62137, 0.75914, 0.6039, 0.83567, 0.60787, 0.8928, 0.63567, 0.69763, 0.73149, 0.68091, 0.80538, 0.68927, 0.87517, 0.6577, 0.89638, 0.6382, 0.92922, 0.59615, 0.693, 0.58315, 0.74705, 0.57387, 0.78879, 0.56737, 0.84216, 0.56458, 0.89963, 0.57201, 0.94342, 0.75731, 0.73161, 0.75181, 0.76403, 0.73924, 0.81267, 0.73139, 0.86245, 0.70703, 0.90298, 0.6756, 0.9383, 0.62531, 0.95798, 0.49989, 0.73324, 0.4881, 0.78245, 0.4881, 0.84209, 0.50617, 0.90345], "triangles": [29, 33, 28, 32, 33, 29, 30, 32, 29, 31, 32, 30, 28, 33, 34, 35, 36, 21, 26, 35, 21, 34, 35, 26, 22, 26, 21, 27, 34, 26, 25, 26, 22, 27, 28, 34, 23, 25, 22, 24, 25, 23, 21, 36, 20, 16, 97, 15, 98, 97, 16, 98, 37, 97, 17, 98, 16, 99, 37, 98, 20, 36, 37, 18, 98, 17, 99, 98, 18, 99, 20, 37, 19, 99, 18, 20, 99, 19, 96, 14, 15, 38, 97, 37, 97, 38, 96, 95, 94, 13, 38, 94, 95, 110, 75, 6, 110, 87, 86, 110, 86, 75, 87, 110, 111, 88, 87, 111, 112, 88, 111, 113, 112, 111, 91, 112, 113, 90, 112, 91, 91, 113, 93, 127, 116, 91, 92, 127, 91, 128, 116, 127, 128, 127, 38, 129, 128, 38, 38, 39, 129, 66, 67, 68, 62, 63, 64, 65, 69, 70, 66, 68, 69, 69, 65, 66, 65, 70, 71, 65, 62, 64, 61, 65, 71, 61, 62, 65, 60, 61, 71, 71, 72, 73, 60, 71, 73, 59, 60, 73, 55, 57, 58, 56, 57, 55, 108, 59, 73, 109, 108, 73, 54, 58, 59, 54, 59, 108, 55, 58, 54, 86, 85, 74, 75, 86, 74, 74, 109, 73, 53, 54, 108, 52, 53, 108, 52, 108, 109, 81, 52, 109, 85, 82, 109, 85, 109, 74, 81, 109, 82, 102, 81, 82, 88, 83, 87, 89, 88, 112, 82, 87, 83, 86, 82, 85, 87, 82, 86, 89, 83, 88, 102, 82, 83, 90, 89, 112, 81, 51, 52, 101, 81, 102, 101, 51, 81, 121, 84, 83, 89, 121, 83, 104, 105, 101, 102, 104, 101, 84, 104, 102, 84, 102, 83, 116, 90, 91, 134, 84, 121, 104, 84, 134, 100, 51, 101, 100, 101, 105, 50, 51, 100, 106, 100, 105, 122, 134, 121, 49, 50, 100, 103, 104, 134, 135, 103, 134, 123, 135, 134, 122, 123, 134, 106, 103, 107, 104, 106, 105, 103, 106, 104, 48, 49, 100, 48, 100, 106, 48, 106, 107, 116, 121, 90, 117, 116, 128, 129, 117, 128, 46, 47, 107, 48, 107, 47, 136, 103, 135, 136, 135, 123, 124, 136, 123, 121, 89, 90, 116, 122, 121, 46, 103, 136, 46, 107, 103, 130, 117, 129, 130, 129, 39, 40, 130, 39, 118, 117, 130, 116, 117, 122, 118, 124, 117, 117, 123, 122, 117, 124, 123, 118, 119, 124, 124, 137, 136, 125, 124, 119, 131, 118, 130, 130, 40, 131, 119, 118, 131, 125, 137, 124, 45, 46, 136, 137, 45, 136, 120, 125, 119, 132, 119, 131, 132, 131, 40, 41, 132, 40, 120, 119, 132, 126, 125, 120, 133, 126, 120, 44, 45, 137, 132, 133, 120, 41, 133, 132, 43, 126, 133, 137, 125, 126, 126, 44, 137, 43, 44, 126, 42, 133, 41, 43, 133, 42, 79, 80, 0, 78, 0, 1, 79, 0, 78, 77, 78, 1, 77, 1, 2, 4, 2, 3, 5, 2, 4, 77, 2, 5, 76, 77, 5, 7, 8, 9, 10, 6, 7, 10, 7, 9, 75, 76, 5, 75, 5, 6, 110, 11, 111, 10, 110, 6, 11, 110, 10, 114, 113, 111, 11, 114, 111, 12, 115, 11, 114, 11, 115, 93, 113, 114, 93, 114, 115, 13, 115, 12, 94, 93, 115, 94, 115, 13, 92, 91, 93, 92, 93, 94, 38, 92, 94, 127, 92, 38, 95, 13, 14, 96, 38, 95, 96, 95, 14, 97, 96, 15], "vertices": [2, 6, 194.95, 54.46, 0.952, 8, 133.65, 63.39, 0.048, 2, 6, 185.85, 39.45, 0.904, 8, 124.54, 48.37, 0.096, 2, 6, 173.75, 23.72, 0.872, 8, 112.45, 32.64, 0.128, 2, 6, 184.39, 16.4, 0.856, 8, 123.09, 25.33, 0.144, 2, 6, 185.82, 10.68, 0.856, 8, 124.52, 19.61, 0.144, 2, 6, 145.24, 4.72, 0.856, 8, 83.94, 13.64, 0.144, 2, 6, 125.29, -12.47, 0.856, 8, 63.99, -3.55, 0.144, 2, 6, 138.9, -27.07, 0.856, 8, 77.59, -18.15, 0.144, 2, 6, 140.51, -33.16, 0.856, 8, 79.21, -24.24, 0.144, 2, 6, 136.04, -34.29, 0.856, 8, 74.74, -25.36, 0.144, 2, 6, 122.67, -27.01, 0.856, 8, 61.36, -18.08, 0.144, 2, 6, 95.02, -35.22, 0.856, 8, 33.72, -26.3, 0.144, 2, 6, 80.82, -49.97, 0.856, 8, 19.52, -41.04, 0.144, 4, 6, 71.36, -51.14, 0.83639, 63, -20.77, 8.76, 0.01959, 64, -42.39, -9.12, 1e-05, 8, 10.06, -42.22, 0.144, 4, 6, 61.58, -57.63, 0.72675, 63, -11.23, 15.6, 0.12881, 64, -36.42, 0.99, 0.00043, 8, 0.28, -48.71, 0.144, 4, 6, 53.26, -62.16, 0.522, 63, -3.08, 20.43, 0.33158, 64, -30.91, 8.7, 0.00242, 8, -8.04, -53.23, 0.144, 4, 6, 45.28, -63.64, 0.26135, 63, 4.84, 22.21, 0.58553, 64, -24.39, 13.51, 0.00911, 8, -16.02, -54.72, 0.144, 4, 6, 38.6, -62.95, 0.08566, 63, 11.53, 21.76, 0.74088, 64, -18.08, 15.81, 0.02946, 8, -22.7, -54.02, 0.144, 4, 6, 33.62, -60.6, 0.00508, 63, 16.6, 19.6, 0.77118, 64, -12.57, 15.87, 0.07974, 8, -27.68, -51.68, 0.144, 3, 63, 21.14, 16.39, 0.67736, 64, -7.12, 14.77, 0.17864, 8, -32.09, -48.31, 0.144, 3, 63, 20.65, 11.61, 0.52749, 64, -5.64, 10.2, 0.32851, 8, -31.43, -43.55, 0.144, 3, 63, 25.9, 8.8, 0.36382, 64, 0.3, 9.75, 0.52418, 8, -36.58, -40.55, 0.112, 3, 63, 32.27, 10.07, 0.1957, 64, 5.61, 13.48, 0.7003, 8, -42.99, -41.58, 0.104, 4, 63, 37.62, 11.13, 0.07977, 64, 10.08, 16.61, 0.82364, 65, -12.76, 12.76, 0.00059, 8, -48.37, -42.45, 0.096, 4, 63, 39.58, 7.48, 0.02241, 64, 13.35, 14.07, 0.85827, 65, -8.78, 11.63, 0.00732, 8, -50.2, -38.73, 0.112, 4, 63, 37.08, 6.34, 0.00466, 64, 11.52, 12.01, 0.87777, 65, -9.7, 9.04, 0.03757, 8, -47.66, -37.68, 0.08, 4, 63, 33.62, 4.57, 0.00123, 64, 9.06, 8.99, 0.79582, 65, -10.85, 5.32, 0.11495, 8, -44.13, -36.04, 0.088, 4, 63, 37.73, 0.64, 0.00092, 64, 14.41, 7.05, 0.66907, 65, -5.16, 5.52, 0.26601, 8, -48.1, -31.96, 0.064, 4, 63, 40.73, -3.19, 0.00059, 64, 18.71, 4.76, 0.48283, 65, -0.33, 5, 0.47657, 8, -50.96, -28.02, 0.04, 3, 63, 43.31, -7.94, 0.00025, 64, 22.99, 1.46, 0.28929, 65, 4.88, 3.54, 0.71046, 3, 63, 45.42, -12.21, 6e-05, 64, 26.63, -1.59, 0.14175, 65, 9.4, 2.07, 0.85819, 3, 63, 46.63, -16.94, 0.00036, 64, 29.65, -5.44, 0.10341, 65, 13.64, -0.36, 0.89623, 4, 6, 8.92, -24.98, 1e-05, 63, 42.6, -15.1, 0.00909, 64, 25.22, -5.38, 0.18522, 65, 9.5, -1.97, 0.80567, 4, 6, 13.48, -28.2, 0.00025, 63, 37.91, -12.04, 0.04625, 64, 19.7, -4.47, 0.33631, 65, 4.05, -3.19, 0.61719, 4, 6, 19.34, -31.56, 0.01329, 63, 31.94, -8.9, 0.13043, 64, 12.96, -4.01, 0.47012, 65, -2.37, -5.28, 0.38616, 4, 6, 25.63, -34.01, 0.06401, 63, 25.56, -6.68, 0.2496, 64, 6.24, -4.56, 0.49641, 65, -8.41, -8.3, 0.18998, 5, 6, 31.82, -35.01, 0.21427, 63, 19.34, -5.91, 0.31194, 64, 0.23, -6.36, 0.39673, 65, -13.3, -12.22, 0.06595, 8, -29.49, -26.09, 0.01111, 5, 6, 39.27, -35.04, 0.43892, 63, 11.9, -6.16, 0.27915, 64, -6.48, -9.59, 0.23422, 65, -18.32, -17.73, 0.01437, 8, -22.03, -26.11, 0.03333, 5, 6, 48.45, -33.52, 0.68819, 63, 2.78, -8.01, 0.15124, 64, -14.08, -14.96, 0.09325, 65, -23.35, -25.55, 0.00065, 8, -12.85, -24.6, 0.06667, 5, 6, 34.74, -33.36, 0.8377, 63, 16.49, -7.67, 0.05175, 64, -1.67, -9.12, 0.02166, 65, -14.04, -15.49, 0, 8, -26.57, -24.44, 0.08889, 2, 6, 15.27, -27.45, 0.9, 8, -46.03, -18.53, 0.1, 2, 6, 4.34, -21.06, 0.9, 8, -56.96, -12.13, 0.1, 2, 6, -2.45, -16.4, 0.9, 8, -63.75, -7.47, 0.1, 2, 6, -4.17, -8.21, 0.932, 8, -65.47, 0.71, 0.068, 2, 6, 1.71, 4.7, 0.932, 8, -59.59, 13.62, 0.068, 3, 6, 9.5, 17.27, 0.93289, 8, -51.8, 26.2, 0.05387, 7, -4.95, 27.74, 0.01325, 3, 6, 18.62, 22.78, 0.93433, 8, -42.68, 31.7, 0.03125, 7, 4.17, 33.24, 0.03442, 3, 6, 21.9, 37.86, 0.90533, 8, -39.4, 46.78, 0.07187, 7, 7.45, 48.32, 0.0228, 3, 6, 24.77, 51.07, 0.89667, 8, -36.53, 60, 0.06983, 7, 10.32, 61.53, 0.0335, 3, 6, 30.12, 60.81, 0.90533, 8, -31.18, 69.74, 0.08467, 7, 15.67, 71.28, 0.01, 3, 6, 35.61, 68.85, 0.8768, 8, -25.69, 77.77, 0.096, 7, 21.16, 79.31, 0.0272, 3, 6, 58.76, 66.47, 0.819, 8, -2.54, 75.39, 0.09033, 7, 44.31, 76.93, 0.09067, 3, 6, 77.81, 59.8, 0.72665, 8, 16.51, 68.72, 0.07708, 7, 63.36, 70.26, 0.19627, 3, 6, 84.04, 70.31, 0.68167, 8, 22.74, 79.23, 0.09167, 7, 69.59, 80.77, 0.22667, 3, 6, 92.09, 75.03, 0.6552, 8, 30.78, 83.95, 0.1, 7, 77.63, 85.49, 0.2448, 3, 6, 98.81, 88.51, 0.6552, 8, 37.51, 97.43, 0.1, 7, 84.36, 98.97, 0.2448, 3, 6, 101.72, 102.29, 0.6552, 8, 40.42, 111.21, 0.1, 7, 87.27, 112.75, 0.2448, 3, 6, 105.75, 101.97, 0.66329, 8, 44.45, 110.9, 0.08889, 7, 91.3, 112.44, 0.24782, 3, 6, 108.89, 85.17, 0.68149, 8, 47.59, 94.1, 0.06389, 7, 94.44, 95.64, 0.25462, 3, 6, 108.94, 68.28, 0.70525, 8, 47.64, 77.21, 0.03125, 7, 94.49, 78.75, 0.2635, 3, 6, 125.4, 77.27, 0.70327, 8, 64.1, 86.19, 0.04167, 7, 110.95, 87.73, 0.25507, 3, 6, 138.03, 99.33, 0.69627, 8, 76.73, 108.26, 0.06667, 7, 123.58, 109.8, 0.23707, 3, 6, 154.38, 116.49, 0.69689, 8, 93.07, 125.41, 0.08889, 7, 139.92, 126.95, 0.21422, 3, 6, 159.37, 118.47, 0.70249, 8, 98.07, 127.39, 0.08889, 7, 144.92, 128.93, 0.20862, 3, 6, 161.21, 112.81, 0.74329, 8, 99.91, 121.74, 0.06667, 7, 146.76, 123.28, 0.19004, 3, 6, 158.13, 95.9, 0.8112, 8, 96.83, 104.83, 0.03333, 7, 143.68, 106.37, 0.15547, 3, 6, 175.19, 114.81, 0.89884, 8, 113.89, 123.74, 0.01911, 7, 160.74, 125.28, 0.08204, 3, 6, 180.57, 116.3, 0.94578, 8, 119.27, 125.22, 0.024, 7, 166.12, 126.76, 0.03022, 2, 6, 181.72, 108.61, 0.95467, 8, 120.42, 117.54, 0.04533, 3, 6, 179.03, 101.87, 0.91354, 8, 117.72, 110.8, 0.056, 7, 164.57, 112.33, 0.03046, 3, 6, 169.97, 87.74, 0.85261, 8, 108.67, 96.67, 0.056, 7, 155.52, 98.21, 0.09139, 3, 6, 149.86, 69.32, 0.76655, 8, 88.56, 78.24, 0.05067, 7, 135.41, 79.78, 0.18278, 3, 6, 156.14, 65.18, 0.71209, 8, 94.84, 74.1, 0.04267, 7, 141.69, 75.64, 0.24525, 3, 6, 124.21, 51.23, 0.69017, 8, 62.91, 60.15, 0.03067, 7, 109.76, 61.69, 0.27917, 3, 6, 106.18, 29.98, 0.70567, 8, 44.88, 38.91, 0.011, 7, 91.73, 40.44, 0.28333, 3, 6, 112.34, 6.7, 0.84283, 8, 51.04, 15.62, 0.00353, 7, 97.89, 17.16, 0.15364, 2, 6, 135.57, 23.07, 0.842, 7, 121.12, 33.53, 0.158, 2, 6, 162.45, 37.26, 0.872, 7, 148, 47.72, 0.128, 2, 6, 176.99, 48.28, 0.888, 7, 162.54, 58.75, 0.112, 2, 6, 189.38, 61.69, 0.904, 7, 174.92, 72.15, 0.096, 3, 6, 193.26, 62.51, 0.952, 8, 131.96, 71.44, 0.016, 7, 178.81, 72.98, 0.032, 3, 6, 75.19, 45.92, 0.71945, 8, 13.89, 54.85, 0.03611, 7, 60.74, 56.38, 0.24444, 3, 6, 77.71, 31.24, 0.71725, 8, 16.41, 40.17, 0.01111, 7, 63.26, 41.71, 0.27164, 2, 6, 62.44, 19.48, 0.72978, 7, 47.99, 29.95, 0.27022, 2, 6, 57.04, 17.77, 0.732, 7, 42.59, 28.24, 0.268, 3, 6, 101.51, 24.92, 0.70737, 8, 40.21, 33.84, 0.004, 7, 87.06, 35.38, 0.28863, 2, 6, 101.48, 17.91, 0.70667, 7, 87.03, 28.37, 0.29333, 2, 6, 88.37, 5.05, 0.70133, 7, 73.92, 15.51, 0.29867, 2, 6, 79.34, -0.09, 0.69778, 7, 64.89, 10.38, 0.30222, 2, 6, 68.47, -1.6, 0.69933, 7, 54.02, 8.86, 0.30067, 2, 6, 67.61, -5.64, 0.709, 7, 53.16, 4.82, 0.291, 2, 6, 69.26, -14.73, 0.756, 7, 54.8, -4.27, 0.244, 2, 6, 62.61, -26.7, 0.812, 7, 48.15, -16.24, 0.188, 2, 6, 69.35, -27.3, 0.83778, 7, 54.9, -16.83, 0.16222, 5, 6, 66.81, -39.22, 0.88574, 63, -15.79, -2.99, 0.02001, 64, -33.09, -17.86, 3e-05, 8, 5.51, -30.29, 0.016, 7, 52.36, -28.76, 0.07822, 5, 6, 60.84, -48.56, 0.81632, 63, -10.15, 6.57, 0.11265, 64, -31.79, -6.84, 0.0008, 8, -0.47, -39.64, 0.048, 7, 46.38, -38.1, 0.02222, 4, 6, 53.44, -54.69, 0.60759, 63, -2.99, 12.96, 0.29308, 64, -27.81, 1.9, 0.00332, 8, -7.86, -45.77, 0.096, 4, 6, 45.85, -57.35, 0.3376, 63, 4.5, 15.89, 0.52611, 64, -22.15, 7.6, 0.00829, 8, -15.45, -48.42, 0.128, 4, 6, 39.47, -56.98, 0.13806, 63, 10.89, 15.76, 0.70404, 64, -16.25, 10.06, 0.0139, 8, -21.83, -48.06, 0.144, 4, 6, 34.15, -55.17, 0.05352, 63, 16.27, 14.14, 0.78541, 64, -10.67, 10.75, 0.01707, 8, -27.15, -46.24, 0.144, 3, 6, 40.45, 48.06, 0.85867, 8, -20.85, 56.99, 0.03178, 7, 26, 58.53, 0.10956, 3, 6, 54.32, 42.74, 0.81333, 8, -6.98, 51.67, 0.00978, 7, 39.87, 53.2, 0.17689, 2, 6, 66.02, 34.4, 0.78667, 7, 51.57, 44.86, 0.21333, 3, 6, 34.71, 27.54, 0.85022, 8, -26.59, 36.46, 0.03031, 7, 20.26, 38, 0.11947, 3, 6, 45.3, 32.53, 0.83956, 8, -16, 41.46, 0.01111, 7, 30.85, 42.99, 0.14933, 2, 6, 47.79, 40.82, 0.872, 7, 33.34, 51.29, 0.128, 3, 6, 38.71, 44.03, 0.92489, 8, -22.59, 52.96, 0.01111, 7, 24.26, 54.5, 0.064, 3, 6, 28.98, 41.51, 0.93556, 8, -32.32, 50.44, 0.03031, 7, 14.53, 51.98, 0.03413, 3, 6, 98.77, 57.69, 0.72193, 8, 37.47, 66.61, 0.00833, 7, 84.32, 68.15, 0.26973, 2, 6, 93.41, 44.71, 0.728, 7, 78.96, 55.18, 0.272, 2, 6, 104.16, -3.62, 0.83133, 7, 89.71, 6.84, 0.16867, 2, 6, 90.75, -11.63, 0.756, 7, 76.29, -1.16, 0.244, 2, 6, 76.87, -8.64, 0.71644, 7, 62.42, 1.82, 0.28356, 3, 6, 82.68, -18.07, 0.80505, 8, 21.38, -9.15, 0.01717, 7, 68.23, -7.61, 0.17778, 3, 6, 84.4, -29.76, 0.85069, 8, 23.1, -20.84, 0.04309, 7, 69.95, -19.3, 0.10622, 3, 6, 80.61, -39.6, 0.88717, 8, 19.31, -30.68, 0.0655, 7, 66.16, -29.14, 0.04733, 2, 6, 54.6, -14.54, 0.72, 7, 40.15, -4.08, 0.28, 2, 6, 38.97, -15.34, 0.768, 7, 24.51, -4.88, 0.232, 2, 6, 25, -19.73, 0.768, 7, 10.54, -9.27, 0.232, 2, 6, 19.62, -15.93, 0.768, 7, 5.17, -5.47, 0.232, 2, 6, 12.27, -14.47, 0.768, 7, -2.18, -4.01, 0.232, 2, 6, 59.11, 2.45, 0.696, 7, 44.65, 12.91, 0.304, 2, 6, 47.64, 1.98, 0.696, 7, 33.19, 12.44, 0.304, 2, 6, 38.82, 1.5, 0.696, 7, 24.36, 11.97, 0.304, 2, 6, 27.71, 0.09, 0.736, 7, 13.26, 10.55, 0.264, 2, 6, 15.88, -2.07, 0.712, 7, 1.43, 8.39, 0.288, 2, 6, 7.19, -5.15, 0.816, 7, -7.26, 5.31, 0.184, 2, 6, 56.55, -23.53, 0.824, 7, 42.1, -13.06, 0.176, 2, 6, 49.75, -24.15, 0.832, 7, 35.3, -13.69, 0.168, 2, 6, 39.41, -24.44, 0.84, 7, 24.95, -13.98, 0.16, 2, 6, 28.98, -25.49, 0.856, 7, 14.53, -15.03, 0.144, 2, 6, 19.9, -23.65, 0.864, 7, 5.45, -13.18, 0.136, 2, 6, 11.66, -20.5, 0.856, 7, -2.79, -10.04, 0.144, 2, 6, 5.98, -13.82, 0.8, 7, -8.47, -3.36, 0.2, 2, 6, 47.71, 15.12, 0.736, 7, 33.26, 25.58, 0.264, 2, 6, 37.27, 14.69, 0.736, 7, 22.82, 25.15, 0.264, 2, 6, 25.1, 12.01, 0.816, 7, 10.65, 22.48, 0.184, 2, 6, 13.17, 6.54, 0.832, 7, -1.28, 17.01, 0.168], "hull": 81, "edges": [0, 160, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 46, 48, 84, 86, 90, 92, 96, 98, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 154, 156, 156, 158, 158, 160, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 58, 60, 56, 58, 52, 54, 54, 56, 40, 42, 36, 38, 38, 40, 32, 34, 34, 36, 150, 152, 152, 154, 114, 116, 116, 118, 100, 102, 102, 104, 104, 162, 162, 164, 164, 166, 166, 168, 148, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 182, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 42, 44, 44, 46, 48, 50, 50, 52, 70, 72, 72, 74, 96, 200, 200, 202, 202, 204, 92, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 92, 92, 94, 94, 96, 98, 100, 118, 216, 216, 218, 150, 220, 220, 222, 178, 224, 224, 226, 226, 228, 228, 230, 180, 232, 232, 234, 234, 236, 236, 238, 238, 240, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 240, 266, 268, 270, 270, 272, 272, 274, 86, 88, 88, 90], "width": 154, "height": 209}}, "a4": {"a4": {"type": "mesh", "uvs": [0.35343, 0.10243, 0.24983, 0.25568, 0.33866, 0.33675, 0.4604, 0.4172, 0.58214, 0.49414, 0.6956, 0.57729, 0.79557, 0.67348, 0.87141, 0.76088, 0.92422, 0.83807, 0.96694, 0.91468, 0.98245, 0.99986, 0.88026, 0.92348, 0.79719, 0.86887, 0.70597, 0.82235, 0.62027, 0.78121, 0.52152, 0.74266, 0.41251, 0.70828, 0.29011, 0.68723, 0.1693, 0.6777, 0.05842, 0.68184, 0.03228, 0.58156, 0.01513, 0.47221, 0.00757, 0.32671, 0.01612, 0.16138, 0.05938, 0.04451, 0.11361, 0.00979, 0.35342, 0.00982, 0.16333, 0.0776, 0.12431, 0.10221, 0.09661, 0.20386, 0.0941, 0.32584, 0.13312, 0.44461, 0.20865, 0.53877, 0.21243, 0.57622, 0.32953, 0.58596, 0.46171, 0.62021, 0.58885, 0.6705], "triangles": [4, 35, 3, 5, 36, 4, 36, 5, 6, 13, 6, 7, 12, 7, 8, 11, 12, 8, 11, 8, 9, 11, 9, 10, 27, 25, 26, 28, 24, 25, 27, 28, 25, 27, 26, 0, 23, 24, 28, 29, 23, 28, 1, 27, 0, 28, 27, 1, 29, 28, 1, 29, 22, 23, 30, 29, 1, 30, 22, 29, 2, 31, 30, 2, 30, 1, 21, 22, 30, 32, 2, 3, 12, 13, 7, 21, 30, 31, 32, 31, 2, 34, 33, 32, 20, 21, 31, 20, 31, 32, 20, 32, 33, 3, 34, 32, 35, 34, 3, 18, 20, 33, 19, 20, 18, 17, 33, 34, 18, 33, 17, 17, 34, 16, 36, 35, 4, 16, 34, 35, 15, 35, 36, 16, 35, 15, 14, 36, 6, 15, 36, 14, 13, 14, 6], "vertices": [2, 5, 120.09, 12.31, 0.97067, 12, 116.87, 20.22, 0.02933, 3, 5, 107.4, 24.61, 0.86613, 13, 54.25, 34.78, 0.072, 12, 104.17, 32.52, 0.06187, 3, 5, 97.68, 19.28, 0.78067, 13, 44.53, 29.45, 0.088, 12, 94.46, 27.19, 0.13133, 3, 5, 87.34, 11.23, 0.73826, 13, 34.19, 21.39, 0.032, 12, 84.12, 19.14, 0.22974, 2, 5, 77.34, 3.08, 0.696, 12, 74.12, 10.99, 0.304, 2, 5, 66.92, -4.22, 0.67733, 12, 63.69, 3.69, 0.32267, 2, 5, 55.51, -10.1, 0.664, 12, 52.28, -2.19, 0.336, 2, 5, 45.45, -14.2, 0.664, 12, 42.23, -6.29, 0.336, 2, 5, 36.87, -16.66, 0.664, 12, 33.64, -8.75, 0.336, 2, 5, 28.55, -18.3, 0.664, 12, 25.33, -10.39, 0.336, 3, 99, -23.5, 12.73, 0.02459, 5, 19.97, -17.48, 0.63941, 12, 16.74, -9.57, 0.336, 3, 99, -12.44, 9.34, 0.09837, 5, 29.5, -10.94, 0.56563, 12, 26.28, -3.03, 0.336, 3, 99, -3.76, 7.26, 0.32059, 5, 36.53, -5.44, 0.45541, 12, 33.31, 2.47, 0.224, 4, 99, 5.22, 6.21, 0.58714, 100, -28.22, 16, 0.00516, 5, 42.94, 0.94, 0.2957, 12, 39.72, 8.85, 0.112, 3, 99, 13.55, 5.46, 0.79959, 100, -20.56, 12.64, 0.05226, 5, 48.72, 6.99, 0.14815, 3, 99, 22.79, 5.39, 0.78462, 100, -11.82, 9.65, 0.17834, 5, 54.52, 14.18, 0.03704, 2, 99, 32.65, 6.07, 0.59504, 100, -2.26, 7.16, 0.40496, 2, 99, 43, 8.43, 0.3412, 100, 8.31, 6.11, 0.6588, 2, 99, 52.75, 11.78, 0.1342, 100, 18.62, 6.19, 0.8658, 4, 99, 61.17, 16.04, 0.03055, 100, 27.95, 7.55, 0.87078, 5, 70.08, 50.85, 0.03467, 13, 16.93, 61.01, 0.064, 4, 99, 67.32, 7.81, 0.00024, 100, 31.17, -2.2, 0.79709, 5, 80.34, 50.54, 0.13867, 13, 27.19, 60.71, 0.064, 3, 100, 33.72, -12.94, 0.58933, 5, 91.3, 49.27, 0.34667, 13, 38.15, 59.43, 0.064, 3, 100, 35.82, -27.35, 0.34667, 5, 105.57, 46.32, 0.58933, 13, 52.42, 56.48, 0.064, 3, 100, 36.76, -43.87, 0.13867, 5, 121.41, 41.55, 0.79733, 13, 68.26, 51.72, 0.064, 3, 100, 34.28, -55.87, 0.03467, 5, 131.84, 35.12, 0.90133, 13, 78.69, 45.29, 0.064, 2, 5, 134.08, 29.8, 0.936, 13, 80.93, 39.97, 0.064, 1, 5, 129.07, 10.04, 1, 2, 5, 126.46, 27.37, 0.936, 13, 73.31, 37.53, 0.064, 4, 99, 79.9, -39.1, 2e-05, 100, 28.21, -50.68, 0.03465, 5, 124.89, 31.19, 0.90133, 13, 71.74, 41.35, 0.064, 4, 99, 77.87, -28.87, 8e-05, 100, 29.53, -40.33, 0.13859, 5, 115.62, 35.96, 0.79733, 13, 62.47, 46.13, 0.064, 4, 99, 73.05, -17.66, 0.00019, 100, 28.51, -28.17, 0.34648, 5, 103.84, 39.17, 0.58933, 13, 50.69, 49.33, 0.064, 4, 99, 65.14, -8.2, 0.00103, 100, 24.02, -16.69, 0.58831, 5, 91.52, 38.87, 0.34667, 13, 38.37, 49.03, 0.064, 3, 99, 55.41, -2.26, 0.03834, 100, 16.69, -7.97, 0.81351, 5, 80.81, 34.95, 0.14815, 3, 99, 53.58, 1.03, 0.14896, 100, 15.99, -4.27, 0.814, 5, 77.1, 35.56, 0.03704, 2, 99, 44.11, -2.18, 0.36703, 100, 5.99, -4.3, 0.63297, 2, 99, 32.46, -3.68, 0.60352, 100, -5.53, -2.03, 0.39648, 2, 99, 20.54, -3.54, 0.73916, 100, -16.79, 1.89, 0.26084], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 10, 12, 18, 20, 34, 36, 36, 38, 46, 48, 48, 50, 50, 52, 4, 6, 6, 8, 8, 10, 42, 44, 44, 46, 38, 40, 40, 42, 24, 26, 20, 22, 22, 24, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30, 30, 32, 32, 34, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72], "width": 85, "height": 100}}, "a5": {"a5": {"x": 23.07, "y": -0.83, "rotation": 162.33, "width": 100, "height": 93}}, "a6": {"a6": {"type": "mesh", "uvs": [0.53418, 0.0193, 0.57551, 0.02127, 0.85592, 0.2812, 0.91744, 0.43235, 0.9832, 0.70201, 0.98337, 0.84981, 0.88015, 0.97989, 0.76663, 0.97923, 0.41535, 0.80519, 0.31891, 0.60688, 0.14069, 0.53331, 0.06417, 0.53217, 0.01688, 0.4987, 0.01705, 0.44234, 0.26275, 0.16956, 0.62005, 0.44596, 0.53408, 0.18066, 0.82922, 0.81771], "triangles": [6, 17, 5, 17, 4, 5, 8, 15, 17, 17, 3, 4, 17, 15, 3, 8, 9, 15, 15, 9, 14, 9, 10, 14, 14, 10, 13, 12, 13, 11, 10, 11, 13, 15, 2, 3, 15, 16, 2, 16, 1, 2, 16, 0, 1, 7, 8, 17, 7, 17, 6], "vertices": [1, 11, 38.6, -17.48, 1, 1, 11, 36.54, -18.71, 1, 1, 11, 16.24, -17.39, 1, 1, 11, 9.37, -13.4, 1, 1, 11, -0.76, -4.89, 1, 2, 11, -4.58, 0.92, 0.2575, 10, 45.36, 2.67, 0.7425, 2, 11, -2.92, 9.31, 0.78143, 10, 50.42, 9.57, 0.21857, 1, 11, 2.61, 12.89, 1, 1, 11, 24.14, 17.21, 1, 2, 11, 33.92, 12.47, 0.99705, 10, 85.13, -3.17, 0.00295, 1, 11, 44.47, 15.24, 1, 1, 11, 48.21, 17.62, 1, 1, 11, 51.36, 17.81, 1, 1, 11, 52.81, 15.59, 1, 1, 11, 47.9, -2.95, 1, 1, 11, 23.45, -3.42, 1, 1, 11, 34.45, -11.13, 1, 2, 11, 3.73, 4.55, 0.74439, 10, 54.42, 2.44, 0.25561], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 10, 34], "width": 58, "height": 47}}, "a7": {"a7": {"type": "mesh", "uvs": [0.73106, 0.04882, 0.76522, 0.00952, 0.84741, 0.00974, 0.92385, 0.06259, 0.99137, 0.15313, 0.99143, 0.22301, 0.99161, 0.44745, 0.95964, 0.72527, 0.89692, 0.86678, 0.82205, 0.94164, 0.69565, 0.99019, 0.56941, 0.99024, 0.42023, 0.94094, 0.36909, 0.90375, 0.30473, 0.73182, 0.21603, 0.66015, 0, 0.54234, 0, 0.445, 0.04934, 0.39244, 0.10235, 0.37794, 0.32071, 0.39973, 0.43348, 0.27144, 0.54106, 0.26744, 0.84019, 0.22219, 0.72932, 0.50779, 0.52532, 0.60469, 0.10344, 0.48374], "triangles": [17, 18, 26, 25, 14, 20, 16, 26, 15, 14, 15, 20, 15, 26, 20, 20, 21, 25, 16, 17, 26, 18, 19, 26, 26, 19, 20, 10, 11, 25, 9, 10, 25, 25, 11, 12, 25, 24, 9, 9, 24, 8, 12, 13, 25, 13, 14, 25, 8, 24, 7, 7, 24, 6, 6, 23, 5, 23, 6, 24, 25, 22, 24, 25, 21, 22, 24, 22, 23, 22, 0, 23, 23, 4, 5, 2, 23, 1, 23, 3, 4, 23, 2, 3, 1, 23, 0], "vertices": [1, 9, 6.31, -18.01, 1, 1, 9, 0.75, -17.98, 1, 1, 9, -5.88, -11.24, 1, 1, 9, -8.3, -1.27, 1, 1, 9, -7.31, 10.61, 1, 1, 9, -2.35, 15.53, 1, 1, 9, 13.6, 31.32, 1, 1, 9, 35.95, 48.23, 1, 1, 9, 51.09, 53.04, 1, 1, 9, 62.46, 52.18, 1, 1, 9, 76.13, 45.25, 1, 1, 9, 86.34, 34.93, 1, 2, 9, 94.89, 19.26, 0.9562, 10, 5.3, 40.53, 0.0438, 2, 9, 96.38, 12.46, 0.91038, 10, 11.65, 37.68, 0.08962, 2, 9, 89.35, -4.89, 0.45174, 10, 21.41, 21.71, 0.54826, 2, 9, 91.42, -17.18, 0.08248, 10, 32.52, 16.06, 0.91752, 2, 10, 58.79, 7.92, 0.94891, 11, 5.36, 11.36, 0.05109, 2, 10, 60.16, -1.72, 0.19488, 11, 10.69, 3.22, 0.80512, 2, 10, 55.29, -7.72, 0.02723, 11, 8.83, -4.29, 0.97277, 2, 10, 49.46, -10.02, 0.4732, 11, 4.52, -8.84, 0.5268, 2, 9, 64.43, -26.92, 0.00647, 10, 24.3, -11.42, 0.99353, 2, 9, 46.19, -26.71, 0.27071, 10, 13.28, -25.96, 0.72929, 2, 9, 37.21, -18.19, 0.6986, 10, 1.09, -28.11, 0.3014, 1, 9, 9.82, 3.1, 1, 1, 9, 39.1, 14.1, 1, 1, 9, 62.48, 4.22, 1, 1, 10, 47.84, 0.43, 1], "hull": 23, "edges": [2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 4, 46, 46, 48, 48, 50, 34, 52, 26, 28, 28, 30, 2, 0, 0, 44, 46, 2, 8, 10, 10, 12], "width": 115, "height": 100}}, "a8": {"a8": {"x": 48.8, "y": 2.52, "rotation": 162.33, "width": 53, "height": 33}}, "a9": {"a9": {"type": "mesh", "uvs": [0.35366, 0, 0.43421, 0.03571, 0.54758, 0.06107, 0.65302, 0.09309, 0.75121, 0.13168, 0.82271, 0.16304, 0.89105, 0.19699, 0.9294, 0.2189, 0.96932, 0.24465, 0.99492, 0.26667, 0.99208, 0.28523, 0.95598, 0.27557, 0.9174, 0.27833, 0.87524, 0.28939, 0.83737, 0.30468, 0.80598, 0.32279, 0.84572, 0.37153, 0.8841, 0.42712, 0.89882, 0.48223, 0.8832, 0.55239, 0.86752, 0.62482, 0.85487, 0.69184, 0.86757, 0.76034, 0.89028, 0.82628, 0.93743, 0.88256, 0.91797, 0.93791, 0.87744, 0.9954, 0.8576, 0.98248, 0.82401, 0.91029, 0.78537, 0.84678, 0.78759, 0.91663, 0.79075, 0.99888, 0.76523, 0.99507, 0.66977, 0.94606, 0.56053, 0.90383, 0.43791, 0.87358, 0.33852, 0.8655, 0.24859, 0.88315, 0.21368, 0.8928, 0.2073, 0.86986, 0.26836, 0.82473, 0.33597, 0.76528, 0.34479, 0.67503, 0.28586, 0.62405, 0.25233, 0.59204, 0.25885, 0.56181, 0.16027, 0.49543, 0.07224, 0.42336, 0, 0.33063, 0.00455, 0.27241, 0.05052, 0.2289, 0.10804, 0.19012, 0.17439, 0.15824, 0.25373, 0.11851, 0.29104, 0.05689, 0.30293, 0, 0.40559, 0.10662, 0.47034, 0.19539, 0.5569, 0.27692, 0.6346, 0.36467, 0.66193, 0.45345, 0.7144, 0.52939, 0.74893, 0.60797, 0.76476, 0.68246, 0.77771, 0.77429, 0.16661, 0.30758, 0.322, 0.41473, 0.42704, 0.51371, 0.4774, 0.5933, 0.49178, 0.67799, 0.47596, 0.77697, 0.6259, 0.79534, 0.47382, 0.08506, 0.58032, 0.12249, 0.70001, 0.16927, 0.79237, 0.21472, 0.81027, 0.2468, 0.80368, 0.27688, 0.75184, 0.3123, 0.70001, 0.36778], "triangles": [8, 12, 7, 11, 8, 9, 11, 12, 8, 7, 13, 76, 10, 11, 9, 7, 12, 13, 72, 1, 2, 56, 0, 1, 56, 1, 72, 55, 0, 56, 54, 55, 56, 56, 53, 54, 70, 35, 41, 40, 41, 36, 28, 29, 23, 28, 23, 24, 29, 33, 71, 25, 28, 24, 27, 28, 25, 26, 27, 25, 35, 36, 41, 37, 39, 40, 37, 40, 36, 38, 39, 37, 33, 34, 71, 35, 70, 34, 30, 33, 29, 32, 33, 30, 31, 32, 30, 23, 29, 64, 71, 69, 63, 42, 68, 69, 21, 63, 20, 64, 63, 21, 64, 21, 22, 70, 69, 71, 23, 64, 22, 70, 42, 69, 41, 42, 70, 69, 62, 63, 71, 63, 64, 34, 70, 71, 71, 64, 29, 72, 2, 73, 57, 56, 72, 77, 75, 76, 20, 62, 19, 78, 75, 77, 15, 79, 78, 16, 79, 15, 75, 5, 6, 78, 74, 75, 73, 2, 3, 53, 56, 57, 58, 57, 73, 58, 73, 74, 65, 51, 52, 50, 51, 65, 49, 50, 65, 48, 49, 65, 59, 58, 78, 66, 57, 58, 66, 58, 59, 57, 52, 53, 57, 65, 52, 66, 65, 57, 47, 48, 65, 47, 65, 66, 60, 59, 79, 67, 66, 59, 46, 47, 66, 60, 67, 59, 61, 60, 17, 45, 46, 66, 45, 66, 67, 68, 67, 60, 68, 60, 61, 68, 61, 62, 43, 45, 67, 43, 67, 68, 44, 45, 43, 42, 43, 68, 69, 68, 62, 73, 3, 74, 57, 72, 73, 74, 3, 4, 75, 4, 5, 74, 4, 75, 58, 74, 78, 61, 17, 18, 79, 59, 78, 16, 17, 60, 16, 60, 79, 15, 78, 77, 19, 61, 18, 62, 61, 19, 63, 62, 20, 76, 75, 6, 13, 77, 76, 14, 15, 77, 7, 76, 6, 14, 77, 13], "vertices": [3, 5, 132.39, 2.89, 0.10285, 6, 28.91, 17.98, 0.8893, 101, -37.68, 73.04, 0.00785, 3, 5, 122.86, -6.25, 0.15965, 6, 24.47, 5.54, 0.82504, 101, -29.37, 62.78, 0.01531, 3, 5, 114.17, -20.31, 0.38145, 6, 23, -10.92, 0.60083, 101, -16.08, 52.96, 0.01771, 4, 5, 104.49, -32.97, 0.57602, 6, 20.02, -26.58, 0.34314, 101, -4.26, 42.25, 0.04751, 13, 51.34, -22.81, 0.03333, 4, 5, 93.81, -44.35, 0.66019, 6, 15.55, -41.53, 0.1347, 101, 6.18, 30.66, 0.13844, 13, 40.66, -34.18, 0.06667, 4, 5, 85.41, -52.47, 0.53486, 6, 11.68, -52.56, 0.03404, 101, 13.58, 21.62, 0.3311, 13, 32.26, -42.3, 0.1, 4, 5, 76.63, -60.05, 0.33345, 6, 7.22, -63.26, 0.00544, 101, 20.39, 12.23, 0.56111, 13, 23.48, -49.88, 0.1, 4, 5, 71.16, -64.16, 0.13343, 6, 4.17, -69.39, 0.0049, 101, 24.04, 6.44, 0.76166, 13, 18.01, -53.99, 0.1, 4, 5, 64.9, -68.3, 0.03338, 6, 0.43, -75.89, 0.00277, 101, 27.64, -0.14, 0.86384, 13, 11.75, -58.13, 0.1, 4, 5, 59.84, -70.69, 2e-05, 6, -3.02, -80.3, 0.00105, 101, 29.6, -5.38, 0.89893, 13, 6.69, -60.52, 0.1, 3, 6, -6.66, -80.69, 0.00013, 101, 28.05, -8.69, 0.89987, 13, 3.26, -59.25, 0.1, 4, 5, 59.48, -65.01, 7e-05, 4, 103.83, -50.62, 9e-05, 101, 23.92, -5.27, 0.89984, 13, 6.33, -54.85, 0.1, 4, 5, 60.27, -59.68, 0.03356, 4, 103.5, -45.24, 0.00026, 101, 18.67, -4.03, 0.86618, 13, 7.12, -49.52, 0.1, 4, 5, 59.61, -53.47, 0.13378, 4, 101.56, -39.3, 0.00052, 101, 12.42, -4.18, 0.7657, 13, 6.46, -43.3, 0.1, 4, 5, 58, -47.63, 0.35609, 4, 98.77, -33.92, 0.00063, 101, 6.47, -5.3, 0.57662, 13, 4.85, -37.46, 0.06667, 4, 5, 55.63, -42.53, 0.58935, 4, 95.39, -29.43, 0.00054, 101, 1.19, -7.24, 0.34344, 13, 2.48, -32.36, 0.06667, 4, 5, 45.01, -45.54, 0.78913, 4, 85.63, -34.58, 0.00028, 101, 3.31, -18.07, 0.14393, 13, -8.14, -35.37, 0.06667, 4, 5, 33.14, -48.04, 0.86674, 4, 74.54, -39.49, 9e-05, 101, 4.81, -30.1, 0.03317, 13, -20.01, -37.87, 0.1, 3, 5, 22.17, -47.37, 0.86667, 4, 63.67, -41.12, 0.03333, 13, -30.98, -37.2, 0.1, 3, 5, 9.37, -41.89, 0.76667, 4, 50.01, -38.42, 0.13333, 13, -43.78, -31.72, 0.1, 4, 5, -3.86, -36.29, 0.56667, 4, 35.9, -35.7, 0.3, 17, -40.05, 34.65, 0.03333, 13, -57.01, -26.12, 0.1, 4, 5, -16.16, -31.36, 0.33333, 4, 22.85, -33.44, 0.43333, 17, -26.9, 33.04, 0.13333, 13, -69.31, -21.19, 0.1, 4, 5, -29.61, -29.77, 0.13333, 4, 9.36, -34.68, 0.43333, 17, -13.49, 34.96, 0.33333, 13, -82.76, -19.61, 0.1, 4, 5, -42.91, -29.66, 0.03333, 4, -3.67, -37.34, 0.3, 17, -0.61, 38.27, 0.56667, 13, -96.06, -19.49, 0.1, 3, 4, -14.95, -43.47, 0.13333, 17, 10.35, 44.95, 0.8, 13, -108.37, -23.14, 0.06667, 4, 4, -25.69, -40.35, 0.03333, 17, 21.23, 42.37, 0.87733, 12, -68.29, -20.11, 0.056, 13, -118.22, -17.85, 0.03333, 2, 17, 32.56, 36.87, 0.888, 12, -77.83, -11.88, 0.112, 2, 17, 30.06, 34.08, 0.824, 12, -74.7, -9.83, 0.176, 2, 17, 15.97, 29.25, 0.816, 12, -59.83, -8.78, 0.184, 2, 17, 3.58, 23.73, 0.808, 12, -46.45, -6.63, 0.192, 2, 17, 17.27, 24.2, 0.816, 12, -59.8, -3.57, 0.184, 2, 17, 33.38, 24.82, 0.824, 12, -75.53, -0.03, 0.176, 3, 4, -36.06, -18.7, 0.02378, 17, 32.68, 21.27, 0.81622, 12, -73.94, 3.22, 0.16, 3, 4, -25.95, -5.81, 0.07728, 17, 23.22, 7.89, 0.77072, 12, -61.37, 13.73, 0.152, 3, 4, -17.1, 9.04, 0.15154, 17, 15.12, -7.39, 0.69246, 12, -49.62, 26.41, 0.156, 3, 4, -10.52, 25.85, 0.07728, 17, 9.39, -24.5, 0.82672, 12, -39.68, 41.48, 0.096, 4, 4, -8.41, 39.59, 0.02378, 17, 7.96, -38.33, 0.89489, 12, -34.76, 54.48, 0.048, 13, -84.68, 56.74, 0.03333, 2, 17, 11.57, -50.79, 0.93333, 13, -84.97, 69.71, 0.06667, 3, 4, -13.09, 57.14, 0.03333, 17, 13.51, -55.62, 0.86667, 13, -85.61, 74.88, 0.1, 3, 4, -8.56, 57.85, 0.13333, 17, 9.03, -56.56, 0.76667, 13, -81.03, 74.63, 0.1, 4, 5, -21.39, 54.07, 0.03333, 4, -0.05, 49.03, 0.3, 17, 0.08, -48.17, 0.56667, 13, -74.54, 64.23, 0.1, 4, 5, -12.41, 42.09, 0.13333, 4, 11.24, 39.19, 0.43333, 17, -11.67, -38.91, 0.33333, 13, -65.56, 52.26, 0.1, 4, 5, 4.44, 36.56, 0.33333, 4, 28.86, 37.28, 0.43333, 17, -29.38, -37.89, 0.13333, 13, -48.71, 46.73, 0.1, 4, 5, 16.14, 42.05, 0.56667, 4, 39.16, 45.09, 0.3, 17, -39.27, -46.19, 0.03333, 13, -37.01, 52.22, 0.1, 3, 5, 23.36, 45.03, 0.76667, 4, 45.61, 49.5, 0.13333, 13, -29.79, 55.2, 0.1, 3, 5, 28.89, 42.7, 0.86667, 4, 51.5, 48.37, 0.03333, 13, -24.26, 52.86, 0.1, 2, 5, 44.86, 52.78, 0.9, 13, -8.29, 62.95, 0.1, 2, 5, 61.56, 61.18, 0.9, 13, 8.41, 71.34, 0.1, 3, 5, 81.64, 66.45, 0.9, 101, -105.24, 27.76, 0, 13, 28.49, 76.62, 0.1, 4, 5, 92.55, 63.03, 0.877, 101, -100.93, 38.35, 0, 12, 89.33, 70.95, 0.048, 13, 39.4, 73.2, 0.075, 3, 5, 99.25, 54.75, 0.9, 101, -92.12, 44.33, 0, 13, 46.1, 64.91, 0.1, 4, 5, 104.65, 45.13, 0.86852, 6, -14.81, 43.33, 0.03147, 101, -82.08, 48.92, 1e-05, 13, 51.5, 55.3, 0.1, 4, 5, 108.45, 34.66, 0.79485, 6, -6.73, 35.66, 0.13842, 101, -71.33, 51.83, 6e-05, 13, 55.3, 44.82, 0.06667, 4, 5, 113.29, 22.05, 0.61207, 6, 3.24, 26.56, 0.35446, 101, -58.37, 55.6, 0.00014, 13, 60.14, 32.22, 0.03333, 3, 5, 123.72, 14.06, 0.40261, 6, 16.15, 24.09, 0.59715, 101, -49.53, 65.33, 0.00023, 3, 5, 134.13, 9.72, 0.22062, 6, 27.39, 24.86, 0.77722, 101, -44.34, 75.34, 0.00216, 4, 5, 110.36, 1.02, 0.34137, 6, 10.05, 6.44, 0.57578, 101, -37.65, 50.94, 0.00018, 12, 107.14, 8.93, 0.08267, 4, 5, 91.29, -3.43, 0.50558, 6, -5.02, -6.08, 0.32901, 101, -34.8, 31.55, 8e-05, 12, 88.07, 4.48, 0.16533, 4, 5, 72.84, -11.17, 0.64194, 6, -18.04, -21.26, 0.11004, 101, -28.62, 12.53, 2e-05, 12, 69.62, -3.26, 0.248, 4, 5, 53.52, -17.42, 0.7246, 6, -32.53, -35.5, 0.0274, 101, -24.01, -7.25, 0, 12, 50.3, -9.51, 0.248, 3, 5, 35.72, -16.83, 0.72415, 4, 70.57, -8.43, 0.02785, 12, 32.5, -8.92, 0.248, 3, 5, 19.5, -20.25, 0.64059, 4, 55.41, -15.15, 0.11141, 12, 16.28, -12.34, 0.248, 3, 5, 3.39, -21.12, 0.47348, 4, 39.84, -19.35, 0.27852, 12, 0.17, -13.21, 0.248, 3, 5, -11.3, -19.67, 0.29244, 4, 25.17, -20.99, 0.45956, 12, -14.52, -11.76, 0.248, 3, 5, -29.19, -16.99, 0.188, 4, 7.11, -22.1, 0.564, 12, -32.41, -9.08, 0.248, 4, 5, 80.34, 42.89, 0.83867, 101, -81.88, 24.5, 0, 12, 77.12, 50.8, 0.128, 13, 27.19, 53.06, 0.03333, 4, 5, 54.68, 27.11, 0.77807, 4, 79.97, 38.49, 0.02993, 101, -68.28, -2.39, 0, 12, 51.45, 35.02, 0.192, 4, 5, 32.29, 17.72, 0.6883, 4, 60.02, 24.65, 0.1197, 101, -60.79, -25.48, 0, 12, 29.06, 25.63, 0.192, 4, 5, 15.44, 14.76, 0.50874, 4, 44.16, 18.25, 0.29926, 101, -59.25, -42.51, 0, 12, 12.22, 22.67, 0.192, 4, 5, -1.14, 16.9, 0.31422, 4, 27.5, 16.89, 0.49378, 101, -62.76, -58.86, 0, 12, -4.36, 24.81, 0.192, 3, 5, -19.4, 23.79, 0.202, 4, 8.2, 19.83, 0.606, 12, -22.63, 31.7, 0.192, 3, 5, -28.01, 4.47, 0.15213, 4, 3.8, -0.85, 0.61256, 12, -31.23, 12.38, 0.23532, 3, 5, 112.13, -9.21, 0.65396, 6, 16.21, -1.91, 0.34454, 101, -27.31, 51.85, 0.00151, 3, 5, 101.39, -21.76, 0.7324, 6, 12.22, -17.95, 0.26644, 101, -15.7, 40.09, 0.00116, 3, 5, 88.41, -35.64, 0.87081, 6, 6.83, -36.16, 0.12863, 101, -2.95, 26.01, 0.00056, 3, 5, 76.63, -45.9, 0.96309, 6, 0.89, -50.61, 0.03675, 101, 6.29, 13.41, 0.00016, 1, 5, 69.92, -46.76, 1, 2, 5, 64.43, -44.43, 0.94133, 12, 61.21, -36.52, 0.05867, 2, 5, 59.47, -35.74, 0.85333, 12, 56.25, -27.83, 0.14667, 2, 5, 50.7, -26.09, 0.78, 12, 47.48, -18.18, 0.22], "hull": 56, "edges": [0, 110, 0, 2, 18, 20, 20, 22, 30, 32, 32, 34, 34, 36, 46, 48, 48, 50, 50, 52, 52, 54, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 94, 96, 96, 98, 98, 100, 100, 102, 106, 108, 108, 110, 2, 4, 4, 6, 6, 8, 102, 104, 104, 106, 90, 92, 92, 94, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 54, 56, 56, 58, 58, 60, 60, 62, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 98, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 68, 142, 8, 10, 10, 12, 16, 18, 12, 14, 14, 16, 26, 28, 28, 30, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 22, 24, 24, 26], "width": 139, "height": 196}}, "shouji/qf_boss_fsf_pg_bz_00": {"shouji/qf_boss_fsf_pg_bz_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}, "shouji/qf_boss_fsf_pg_bz_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -150, -150, -150, -150, 150, 150, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 210}}, "long": {"long": {"type": "path", "lengths": [317.73, 602.42, 964.2, 1217.5, 1517.67, 1788.39, 2268.58, 3428.69], "vertexCount": 24, "vertices": [1, 102, -151.92, -58.74, 1, 1, 102, -115.67, -11.43, 1, 1, 102, -24.93, 107, 1, 1, 105, 110.4, 67.77, 1, 1, 105, 83.7, 235.15, 1, 1, 105, 14.16, 671.15, 1, 1, 106, -535.78, 549.18, 1, 1, 106, -654.14, 719.29, 1, 1, 106, -834.35, 978.29, 1, 1, 106, -218.59, 1272.34, 1, 1, 106, -51.74, 1127.41, 1, 1, 106, 130.84, 968.82, 1, 1, 106, -54.13, 692.22, 1, 1, 106, 67.72, 537.83, 1, 1, 106, 423.88, 86.57, 1, 1, 106, 419.04, 403.17, 1, 1, 106, 680.78, 431.81, 1, 1, 106, 819.25, 446.96, 1, 1, 106, 958.16, 144.1, 1, 1, 106, 1207.59, 46.63, 1, 1, 106, 1325, 0.75, 1, 1, 106, 2352.72, -0.7, 1, 1, 106, 2376.97, 1.27, 1, 1, 106, 2479.53, 9.58, 1]}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"a0": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}]}, "dianball/qf_qing_jn3_qtgd_xlsd_00": {"color": [{"time": 0.2575, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_00"}, {"time": 0.0667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_02"}, {"time": 0.1, "name": "dianball/qf_qing_jn3_qtgd_xlsd_04"}, {"time": 0.1333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_06"}, {"time": 0.1667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_08"}, {"time": 0.2, "name": "dianball/qf_qing_jn3_qtgd_xlsd_10"}, {"time": 0.2333, "name": null}]}, "dianball/qf_qing_jn3_qtgd_tw_sg_00": {"color": [{"time": 0.2575, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0.1333, "name": "dianball/qf_qing_jn3_qtgd_tw_sg_00"}, {"time": 0.1667, "name": "dianball/qf_qing_jn3_qtgd_tw_sg_01"}, {"time": 0.2, "name": "dianball/qf_qing_jn3_qtgd_tw_sg_02"}, {"time": 0.2333, "name": "dianball/qf_qing_jn3_qtgd_tw_sg_04"}, {"time": 0.2667, "name": "dianball/qf_qing_jn3_qtgd_tw_sg_06"}, {"time": 0.3, "name": "dianball/qf_qing_jn3_qtgd_tw_sg_00"}, {"time": 0.3252, "name": null}]}, "dianball/qf_qing_jn3_qtgd_rw_sd_01": {"color": [{"time": 0.2575, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0.1333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_01"}, {"time": 0.1667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_03"}, {"time": 0.2333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_05"}, {"time": 0.2667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_07"}, {"time": 0.3, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_09"}, {"time": 0.3252, "name": null}]}}, "bones": {"bone2": {"translate": [{"y": -4.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -34.41, "y": 10.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.88, "y": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 13.42, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -4.99}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "translate": [{"x": 0.01, "y": 0.94}]}, "bone5": {"rotate": [{"angle": 1.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.28}], "translate": [{"x": 3.65, "y": -0.14}]}, "bone6": {"rotate": [{"angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 19.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.1}]}, "bone17": {"translate": [{"x": 2.03, "y": -0.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 11.78, "y": -1.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 5.34, "y": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 2.03, "y": -0.12}]}, "bone49": {"rotate": [{"angle": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -43.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 99.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.4, "angle": 15.45, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": 0.2}], "translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.05, "y": 7.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone50": {"rotate": [{"angle": -1.67, "curve": "stepped"}, {"time": 0.1667, "angle": -1.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 81.77, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.4, "angle": -9.02, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -1.67}]}, "bone51": {"rotate": [{"angle": -4.68, "curve": "stepped"}, {"time": 0.1667, "angle": -4.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -115.18, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.4, "angle": 27.51, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -4.68}]}, "bone52": {"translate": [{"x": 1.15, "y": -0.12, "curve": "stepped"}, {"time": 0.1667, "x": 1.15, "y": -0.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -3.58, "y": -34.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1.15, "y": -0.12}]}, "bone77": {"rotate": [{"angle": 5.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -6.29, "curve": "stepped"}, {"time": 0.2333, "angle": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 5.79}]}, "bone78": {"rotate": [{"angle": -12.11, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 37.28, "curve": "stepped"}, {"time": 0.2333, "angle": 37.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.11}]}, "bone83": {"rotate": [{"angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.89, "curve": "stepped"}, {"time": 0.2333, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.5}]}, "bone84": {"rotate": [{"angle": 5.89, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.75, "curve": "stepped"}, {"time": 0.2333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 5.89}]}, "bone108": {"rotate": [{"angle": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.96, "curve": "stepped"}, {"time": 0.2333, "angle": 7.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.63}]}, "bone109": {"rotate": [{"angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.92, "curve": "stepped"}, {"time": 0.2333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.98}]}, "bone44": {"rotate": [{"angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.44}]}, "bone45": {"rotate": [{"angle": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 18.68, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -0.63}]}, "bone46": {"rotate": [{"angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -33.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 19.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.4}]}, "bone14": {"rotate": [{"angle": 6.92, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 6.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 34.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -2.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": -1.06}]}, "bone7": {"rotate": [{"angle": -1.16, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": 3.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 6.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.66, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": -1.16}]}, "bone11": {"rotate": [{"angle": -2.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -9.09, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.1, "angle": -8.8, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1333, "angle": -2.99, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 16.09, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -2.99}]}, "bone12": {"rotate": [{"angle": 7.24, "curve": 0.328, "c2": 0.32, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "angle": -16.61, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1333, "angle": -28.4, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 23.44, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 7.24}]}, "bone8": {"rotate": [{"angle": -4.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -10, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": -8.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": -3.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": 18.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -4.74}]}, "bone9": {"rotate": [{"angle": 13.28, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.1, "angle": -8.56, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": -17.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 31.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "angle": 13.28}]}, "bone10": {"rotate": [{"angle": 41.05, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.1, "angle": 17.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": 0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "angle": -24.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 41.86, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.5, "angle": 41.05}]}, "bone89": {"rotate": [{"angle": -6.01, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": -7.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3667, "angle": 7.11, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.5, "angle": -6.01}]}, "bone88": {"rotate": [{"angle": -2.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "angle": 5.37, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3667, "angle": -1.14, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4333, "angle": -3.99, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": -2.77}]}, "bone90": {"rotate": [{"angle": 9.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "angle": -5.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 3.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 12.01, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": 9.77}]}, "bone55": {"rotate": [{"angle": 4.19, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.1667, "angle": -1.38, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.19}]}, "bone56": {"rotate": [{"angle": 2.39, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.1333, "angle": 15.37, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1667, "angle": 14.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.2, "angle": 9.82, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3667, "angle": -7.55, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "angle": 2.39}]}, "bone67": {"rotate": [{"angle": 1.08, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": -1.02, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": -4.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.48, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.4667, "angle": 3.18, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.5, "angle": 1.08}]}, "bone68": {"rotate": [{"angle": 15.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 14.43, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": 8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": -12.15, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4667, "angle": 11.93, "curve": 0.362, "c2": 0.5, "c3": 0.7, "c4": 0.85}, {"time": 0.5, "angle": 15.22}]}, "bone57": {"rotate": [{"angle": -10.24, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.1667, "angle": 13.78, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.2, "angle": 18.92, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2333, "angle": 19.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.58, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": -10.24}]}, "bone69": {"rotate": [{"angle": 5.37, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 12.75, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": 20.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -20.49, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.4667, "angle": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 0.5, "angle": 5.37}]}, "bone58": {"rotate": [{"angle": 12.61, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": 18.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 22.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 9.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 12.61}]}, "bone59": {"rotate": [{"angle": -9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -15.32, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": -14.02, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": -7.38, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": 17.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -9.27}]}, "bone60": {"rotate": [{"angle": 3.74, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.1, "angle": -18.33, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": -27.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 22.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "angle": 3.74}]}, "bone63": {"rotate": [{"angle": -5.62, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.0667, "angle": -15.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -5.62}]}, "bone62": {"rotate": [{"angle": -13.47, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.0667, "angle": -6.49, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 16.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.81, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.5, "angle": -13.47}]}, "bone61": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.67, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": 3.4}]}, "bone64": {"rotate": [{"angle": -4.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -5.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 6.35, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4667, "angle": -2.02, "curve": 0.348, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 0.5, "angle": -4.15}]}, "bone65": {"rotate": [{"angle": 11.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 17.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "angle": 16.53, "curve": 0.306, "c2": 0.21, "c3": 0.643, "c4": 0.56}, {"time": 0.5, "angle": 12.72}]}, "bone66": {"rotate": [{"angle": 23.87, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 24.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -12.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "angle": 17.73, "curve": 0.357, "c2": 0.44, "c3": 0.694, "c4": 0.79}, {"time": 0.5, "angle": 22.9}]}, "bone16": {"rotate": [{"angle": 5.15, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": -1.81, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 39.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 5.15}]}, "a5": {"rotate": [{"angle": -2.24, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": 9.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -2.24}], "translate": [{"curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.1667, "x": 6.99, "y": -17.71, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.2333}]}, "dianballall": {"translate": [{"y": 17.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.7, "y": 157.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 57.57, "y": -27.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 737.66, "y": -38.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": 40.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "y": 17.6}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 2, "y": 2, "curve": "stepped"}, {"time": 0.3, "x": 2, "y": 2, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone20": {"rotate": [{"angle": -49.29}], "translate": [{"x": 395.45, "y": -88.33}]}, "bone21": {"rotate": [{"angle": -42.82}], "translate": [{"x": 328.54, "y": -56.51}]}, "bone22": {"rotate": [{"angle": -25.96}], "translate": [{"x": 255.19, "y": -36.34}]}, "bone23": {"rotate": [{"angle": 16.66}], "translate": [{"x": 179.25, "y": -48.2}]}, "bone24": {"rotate": [{"angle": 65.29}], "translate": [{"x": 126.51, "y": -88.34}]}, "bone25": {"rotate": [{"angle": 116.84}], "translate": [{"x": 111.92, "y": -126.56}]}, "bone26": {"rotate": [{"angle": 128.44}], "translate": [{"x": 106.11, "y": -161.98}]}, "bone27": {"rotate": [{"angle": 107.36}], "translate": [{"x": 85.55, "y": -204.92}]}, "bone28": {"rotate": [{"angle": 61.04}], "translate": [{"x": 32.44, "y": -238.97}]}, "bone29": {"rotate": [{"angle": -24.29}], "translate": [{"x": -43.12, "y": -219.2}]}, "bone30": {"rotate": [{"angle": -21.93}], "translate": [{"x": -117.53, "y": -201.96}]}, "bone31": {"rotate": [{"angle": -4.52}], "translate": [{"x": -195.56, "y": -197.66}]}, "bone32": {"rotate": [{"angle": 34.34}], "translate": [{"x": -265.4, "y": -221.54}]}, "bone33": {"rotate": [{"angle": 74.66}], "translate": [{"x": -311.2, "y": -263.93}]}, "bone34": {"rotate": [{"angle": 101.94}], "translate": [{"x": -335.96, "y": -307.69}]}, "bone35": {"rotate": [{"angle": 124.53}], "translate": [{"x": -344.15, "y": -345.11}]}, "bone36": {"rotate": [{"angle": 134.99}], "translate": [{"x": -345.82, "y": -377.38}]}, "bone37": {"rotate": [{"angle": 138.24}], "translate": [{"x": -345.27, "y": -408.17}]}, "bone38": {"rotate": [{"angle": 124.32}], "translate": [{"x": -354.51, "y": -444.32}]}, "dianball3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -4.41, "y": 10.09}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.532, "y": 0.532}]}, "dianball2": {"translate": [{"time": 0.1667, "x": 11.35, "y": 5.67}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.611, "y": 0.611}]}}, "deform": {"default": {"a7": {"a7": [{"time": 0.1667}, {"time": 0.2333, "offset": 12, "vertices": [-3.57857, -8.43425, -12.71384, -21.57254, -16.63316, -24.00977, -16.67825, -22.27533, -19.40343, -16.31555, -20.9827, -7.96994, -25.90503, 3.06985, -21.35853, 14.97679, -22.64297, 9.93486, -15.23318, 19.47719, -0.33452, 12.13658, 5.45576, 10.84692, 7.04015, 3.44792, 7.83365, -0.29897, -10.96826, 2.435, -4.233, 10.40712, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.97137, -3.88918, -0.12776, -10.74448, 0, 0, 0.06663, 1.34894]}, {"time": 0.5}]}}}, "events": [{"time": 0.2333, "name": "atk"}]}, "boss_attack2_2": {"bones": {"bone2": {"translate": [{"y": -4.99}]}, "bone4": {"translate": [{"x": 0.01, "y": 0.94}]}, "bone5": {"rotate": [{"angle": 1.28}], "translate": [{"x": 3.65, "y": -0.14}]}, "bone6": {"rotate": [{"angle": 2.1}]}, "bone17": {"translate": [{"x": 2.03, "y": -0.12}]}, "bone49": {"rotate": [{"angle": 0.2}]}, "bone50": {"rotate": [{"angle": -1.67}]}, "bone51": {"rotate": [{"angle": -4.68}]}, "bone52": {"translate": [{"x": 1.15, "y": -0.12}]}, "bone77": {"rotate": [{"angle": 5.79}]}, "bone78": {"rotate": [{"angle": -12.11}]}, "bone83": {"rotate": [{"angle": -3.5}]}, "bone84": {"rotate": [{"angle": 5.89}]}, "bone108": {"rotate": [{"angle": 3.63}]}, "bone109": {"rotate": [{"angle": -7.98}]}, "bone44": {"rotate": [{"angle": -1.44}]}, "bone45": {"rotate": [{"angle": -0.63}]}, "bone46": {"rotate": [{"angle": -2.4}]}, "bone14": {"rotate": [{"angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06}]}, "bone7": {"rotate": [{"angle": -1.16}]}, "bone11": {"rotate": [{"angle": -2.99}]}, "bone12": {"rotate": [{"angle": 7.24}]}, "bone8": {"rotate": [{"angle": -4.74}]}, "bone9": {"rotate": [{"angle": 13.28}]}, "bone10": {"rotate": [{"angle": 41.05}]}, "bone89": {"rotate": [{"angle": -6.01}]}, "bone88": {"rotate": [{"angle": -2.77}]}, "bone90": {"rotate": [{"angle": 9.77}]}, "bone55": {"rotate": [{"angle": 4.19}]}, "bone56": {"rotate": [{"angle": 2.39}]}, "bone67": {"rotate": [{"angle": 1.08}]}, "bone68": {"rotate": [{"angle": 15.53}]}, "bone57": {"rotate": [{"angle": -10.24}]}, "bone69": {"rotate": [{"angle": 5.37}]}, "bone58": {"rotate": [{"angle": 12.61}]}, "bone59": {"rotate": [{"angle": -9.27}]}, "bone60": {"rotate": [{"angle": 3.74}]}, "bone63": {"rotate": [{"angle": -5.62}]}, "bone62": {"rotate": [{"angle": -13.47}]}, "bone61": {"rotate": [{"angle": 3.4}]}, "bone64": {"rotate": [{"angle": -4.15}]}, "bone65": {"rotate": [{"angle": 11.1}]}, "bone66": {"rotate": [{"angle": 23.87}]}, "bone16": {"rotate": [{"angle": 5.15}]}, "a5": {"rotate": [{"angle": -2.24}]}, "dianballall": {"translate": [{"y": 17.6}]}, "bone20": {"rotate": [{"angle": -49.29}], "translate": [{"x": 395.45, "y": -88.33}]}, "bone21": {"rotate": [{"angle": -42.82}], "translate": [{"x": 328.54, "y": -56.51}]}, "bone22": {"rotate": [{"angle": -25.96}], "translate": [{"x": 255.19, "y": -36.34}]}, "bone23": {"rotate": [{"angle": 16.66}], "translate": [{"x": 179.25, "y": -48.2}]}, "bone24": {"rotate": [{"angle": 65.29}], "translate": [{"x": 126.51, "y": -88.34}]}, "bone25": {"rotate": [{"angle": 116.84}], "translate": [{"x": 111.92, "y": -126.56}]}, "bone26": {"rotate": [{"angle": 128.44}], "translate": [{"x": 106.11, "y": -161.98}]}, "bone27": {"rotate": [{"angle": 107.36}], "translate": [{"x": 85.55, "y": -204.92}]}, "bone28": {"rotate": [{"angle": 61.04}], "translate": [{"x": 32.44, "y": -238.97}]}, "bone29": {"rotate": [{"angle": -24.29}], "translate": [{"x": -43.12, "y": -219.2}]}, "bone30": {"rotate": [{"angle": -21.93}], "translate": [{"x": -117.53, "y": -201.96}]}, "bone31": {"rotate": [{"angle": -4.52}], "translate": [{"x": -195.56, "y": -197.66}]}, "bone32": {"rotate": [{"angle": 34.34}], "translate": [{"x": -265.4, "y": -221.54}]}, "bone33": {"rotate": [{"angle": 74.66}], "translate": [{"x": -311.2, "y": -263.93}]}, "bone34": {"rotate": [{"angle": 101.94}], "translate": [{"x": -335.96, "y": -307.69}]}, "bone35": {"rotate": [{"angle": 124.53}], "translate": [{"x": -344.15, "y": -345.11}]}, "bone36": {"rotate": [{"angle": 134.99}], "translate": [{"x": -345.82, "y": -377.38}]}, "bone37": {"rotate": [{"angle": 138.24}], "translate": [{"x": -345.27, "y": -408.17}]}, "bone38": {"rotate": [{"angle": 124.32}], "translate": [{"x": -354.51, "y": -444.32}]}, "bone39": {"rotate": [{"angle": 23.07}], "translate": [{"x": -428.57, "y": -460.73}]}}, "deform": {"default": {"long": {"long": [{"offset": 1, "vertices": [5.145, 2e-05, 5.145, 2e-05, 5.145, 0, 9.26099, 0, 9.26099, 0, 9.26099]}]}}}}, "boss_attack3": {"slots": {"a14": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a32": {"color": [{"time": 1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "a19"}]}, "a0": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a18": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a25": {"color": [{"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a23": {"color": [{"time": 1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "a23"}]}, "loong2": {"color": [{"time": 0.4333, "color": "35c7ffff", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "36c7ffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "color": "35c7ff00"}], "attachment": [{"time": 0.4333, "name": "loong"}]}, "a7": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a6": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a2": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a16": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a22": {"color": [{"time": 1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "a22"}]}, "a17": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a3": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a1": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "chongj/changcici1": {"attachment": [{"time": 0.8333, "name": "chongj/changcici1"}, {"time": 0.8667, "name": "chongj/changcici2"}, {"time": 0.9, "name": "chongj/changcici3"}, {"time": 0.9333, "name": "chongj/changcici4"}, {"time": 0.9667, "name": null}]}, "a13": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "light": {"color": [{"time": 0.2, "color": "148eff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "138effff", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "128eff00"}]}, "a4": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a10": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a5": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "loong": {"color": [{"time": 0.4333, "color": "35c7ffff", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "36c7ffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "color": "35c7ff00"}], "attachment": [{"time": 0.4333, "name": "loong"}]}, "yichu/qf_qing_jn3_qtkz_rwql_bw_00": {"color": [{"time": 0.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"name": "yichu/qf_qing_jn3_qtkz_rwql_bw_02"}, {"time": 0.0667, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_04"}, {"time": 0.1, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_06"}, {"time": 0.1667, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_08"}, {"time": 0.2, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_10"}, {"time": 0.2667, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_12"}, {"time": 0.3, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_14"}, {"time": 0.3667, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_22"}, {"time": 0.4, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_26"}, {"time": 0.4667, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_28"}, {"time": 1.4, "name": "yichu/qf_qing_jn3_qtkz_rwql_bw_02"}]}, "a20": {"color": [{"time": 1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "a20"}]}, "a9": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "shouji/qf_boss_fsf_pg_bz_0": {"attachment": [{"time": 0.8667, "name": "shouji/qf_boss_fsf_pg_bz_00"}, {"time": 0.9, "name": "shouji/qf_boss_fsf_pg_bz_01"}, {"time": 0.9333, "name": "shouji/qf_boss_fsf_pg_bz_02"}, {"time": 0.9667, "name": "shouji/qf_boss_fsf_pg_bz_04"}, {"time": 1, "name": "shouji/qf_boss_fsf_pg_bz_06"}, {"time": 1.0333, "name": null}]}, "shouji/qf_boss_fsf_pg_bz_1": {"attachment": [{"time": 0.9333, "name": "shouji/qf_boss_fsf_pg_bz_00"}, {"time": 0.9667, "name": "shouji/qf_boss_fsf_pg_bz_01"}, {"time": 1, "name": "shouji/qf_boss_fsf_pg_bz_02"}, {"time": 1.0333, "name": "shouji/qf_boss_fsf_pg_bz_04"}, {"time": 1.0667, "name": "shouji/qf_boss_fsf_pg_bz_06"}, {"time": 1.1, "name": null}]}, "a21": {"color": [{"time": 1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "a21"}]}, "a24": {"color": [{"time": 1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "a24"}]}, "a15": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a12": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a8": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "a11": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff"}]}, "shouji/qf_boss_fsf_pg_bz_00": {"attachment": [{"time": 0.8333, "name": "shouji/qf_boss_fsf_pg_bz_00"}, {"time": 0.8667, "name": "shouji/qf_boss_fsf_pg_bz_01"}, {"time": 0.9, "name": "shouji/qf_boss_fsf_pg_bz_02"}, {"time": 0.9333, "name": "shouji/qf_boss_fsf_pg_bz_04"}, {"time": 0.9667, "name": "shouji/qf_boss_fsf_pg_bz_06"}, {"time": 1, "name": null}]}}, "bones": {"bone2": {"translate": [{"y": -4.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -34.41, "y": 10.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 38.39, "y": 499.49, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 4.05, "y": 76.44, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "y": -4.99}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.15, "curve": "stepped"}, {"time": 0.6667, "angle": -7.15, "curve": 0.25, "c3": 0.75}, {"time": 1.4}], "translate": [{"x": 0.01, "y": 0.94}]}, "bone5": {"rotate": [{"angle": 1.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.88, "curve": "stepped"}, {"time": 0.6667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 1.28}], "translate": [{"x": 3.65, "y": -0.14}]}, "bone6": {"rotate": [{"angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.85, "curve": "stepped"}, {"time": 0.6667, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 2.1}]}, "bone17": {"translate": [{"x": 2.03, "y": -0.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 11.78, "y": -1.9, "curve": "stepped"}, {"time": 0.6667, "x": 11.78, "y": -1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 2.03, "y": -0.12}]}, "bone49": {"rotate": [{"angle": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -43.74, "curve": "stepped"}, {"time": 0.6667, "angle": -43.74, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 0.2}]}, "bone50": {"rotate": [{"angle": -1.67}]}, "bone51": {"rotate": [{"angle": -4.68}]}, "bone52": {"translate": [{"x": 1.15, "y": -0.12}]}, "bone77": {"rotate": [{"angle": 5.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 5.79}]}, "bone78": {"rotate": [{"angle": -12.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 37.28, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -12.11}]}, "bone83": {"rotate": [{"angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -3.5}]}, "bone84": {"rotate": [{"angle": 5.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 5.89}]}, "bone108": {"rotate": [{"angle": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.96, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 3.63}]}, "bone109": {"rotate": [{"angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -7.98}]}, "bone44": {"rotate": [{"angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -18.29, "curve": "stepped"}, {"time": 0.6667, "angle": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -1.44}]}, "bone45": {"rotate": [{"angle": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.28, "curve": "stepped"}, {"time": 0.6667, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -0.63}]}, "bone46": {"rotate": [{"angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -33.71, "curve": "stepped"}, {"time": 0.6667, "angle": -33.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -2.4}]}, "bone19": {"translate": [{"time": 0.2, "x": 13.91, "y": -1071.24}], "scale": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.909, "y": 1.909}]}, "bone80": {"translate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 67.56, "y": 500.85, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 67.56, "y": 77.81, "curve": 0.25, "c3": 0.75}, {"time": 1.4}]}, "bone86": {"translate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 81.49, "y": 505.14, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 81.49, "y": 82.09, "curve": 0.25, "c3": 0.75}, {"time": 1.4}]}, "a0": {"translate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 29.9, "y": 248.99, "curve": "stepped"}, {"time": 0.7333, "x": 29.9, "y": 248.99, "curve": 0.25, "c3": 0.75}, {"time": 1.4}]}, "bone20": {"rotate": [{"angle": -49.29}], "translate": [{"x": 395.45, "y": -88.33}]}, "bone21": {"rotate": [{"angle": -42.82}], "translate": [{"x": 328.54, "y": -56.51}]}, "bone22": {"rotate": [{"angle": -25.96}], "translate": [{"x": 255.19, "y": -36.34}]}, "bone23": {"rotate": [{"angle": 16.66}], "translate": [{"x": 179.25, "y": -48.2}]}, "bone24": {"rotate": [{"angle": 65.29}], "translate": [{"x": 126.51, "y": -88.34}]}, "bone25": {"rotate": [{"angle": 116.84}], "translate": [{"x": 111.92, "y": -126.56}]}, "bone26": {"rotate": [{"angle": 128.44}], "translate": [{"x": 106.11, "y": -161.98}]}, "bone27": {"rotate": [{"angle": 107.36}], "translate": [{"x": 85.55, "y": -204.92}]}, "bone28": {"rotate": [{"angle": 61.04}], "translate": [{"x": 32.44, "y": -238.97}]}, "bone29": {"rotate": [{"angle": -24.29}], "translate": [{"x": -43.12, "y": -219.2}]}, "bone30": {"rotate": [{"angle": -21.93}], "translate": [{"x": -117.53, "y": -201.96}]}, "bone31": {"rotate": [{"angle": -4.52}], "translate": [{"x": -195.56, "y": -197.66}]}, "bone32": {"rotate": [{"angle": 34.34}], "translate": [{"x": -265.4, "y": -221.54}]}, "bone33": {"rotate": [{"angle": 74.66}], "translate": [{"x": -311.2, "y": -263.93}]}, "bone34": {"rotate": [{"angle": 101.94}], "translate": [{"x": -335.96, "y": -307.69}]}, "bone35": {"rotate": [{"angle": 124.53}], "translate": [{"x": -344.15, "y": -345.11}]}, "bone36": {"rotate": [{"angle": 134.99}], "translate": [{"x": -345.82, "y": -377.38}]}, "bone37": {"rotate": [{"angle": 138.24}], "translate": [{"x": -345.27, "y": -408.17}]}, "bone38": {"rotate": [{"angle": 124.32}], "translate": [{"x": -354.51, "y": -444.32}]}, "bone39": {"rotate": [{"angle": 23.07}], "translate": [{"x": -428.57, "y": -460.73}]}, "bone62": {"rotate": [{"angle": -13.47, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1667, "angle": -6.49, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6667, "angle": 16.39, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -13.47}]}, "bone61": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 3.4}]}, "bone64": {"rotate": [{"angle": -4.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -5.69, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -4.15}]}, "bone65": {"rotate": [{"angle": 12.72, "curve": 0.328, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.0667, "angle": 11.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 12.72}]}, "bone66": {"rotate": [{"angle": 22.9, "curve": 0.346, "c2": 0.43, "c3": 0.679, "c4": 0.77}, {"time": 0.0667, "angle": 24.5, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 22.9}]}, "a5": {"rotate": [{"angle": -2.24, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4667, "angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -2.24}]}, "bone16": {"rotate": [{"angle": 5.15, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1667, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 5.15}]}, "bone14": {"rotate": [{"angle": 6.92, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1667, "angle": 6.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": 34.37, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -1.06}]}, "bone7": {"rotate": [{"angle": -1.16, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2, "angle": 3.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 4.21, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -1.16}], "translate": [{"time": 0.4667, "x": -2}], "scale": [{"time": 0.4667, "x": 0.98}]}, "bone11": {"rotate": [{"angle": -2.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -8.8, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.4, "angle": -2.99}]}, "bone12": {"rotate": [{"angle": 7.24, "curve": 0.328, "c2": 0.32, "c3": 0.68, "c4": 0.71}, {"time": 0.2, "angle": -16.61, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.4, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 7.24}]}, "bone8": {"rotate": [{"angle": -4.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -10, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2, "angle": -8.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3667, "angle": -3.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.4, "angle": -4.74}]}, "bone9": {"rotate": [{"angle": 13.28, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.2, "angle": -8.56, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.3667, "angle": -16.68, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.4, "angle": -17.52, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 13.28}]}, "bone10": {"rotate": [{"angle": 41.05, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.2, "angle": 17.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.3667, "angle": 0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": -24.29, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 41.05}]}, "bone89": {"rotate": [{"angle": -6.01, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": -7.62, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -6.01}]}, "bone88": {"rotate": [{"angle": -2.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5, "angle": 5.37, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.4, "angle": -2.77}]}, "bone90": {"rotate": [{"angle": 9.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5, "angle": -5.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4, "angle": 9.77}]}, "bone55": {"rotate": [{"angle": 4.19, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4, "angle": -1.38, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.4, "angle": 4.19}]}, "bone56": {"rotate": [{"angle": 2.39, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.3667, "angle": 15.37, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4, "angle": 14.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.6, "angle": 9.82, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.4, "angle": 2.39}]}, "bone67": {"rotate": [{"angle": 1.08, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.1, "angle": -1.02, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": -4.31, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 1.08}]}, "bone68": {"rotate": [{"angle": 15.22, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "angle": 15.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": 14.43, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.2, "angle": 8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.4, "angle": 15.22}]}, "bone57": {"rotate": [{"angle": -10.24, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.4, "angle": 13.78, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.6, "angle": 19.45, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -10.24}]}, "bone69": {"rotate": [{"angle": 5.37, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.1, "angle": 12.75, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.2, "angle": 19.48, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2667, "angle": 20.17, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 5.37}]}, "bone58": {"rotate": [{"angle": 12.61, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2, "angle": 18.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 22.91, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 12.61}]}, "bone59": {"rotate": [{"angle": -9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -15.32, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2, "angle": -14.02, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3667, "angle": -7.38, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.4, "angle": -9.27}]}, "bone60": {"rotate": [{"angle": 3.74, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.2, "angle": -18.33, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.3667, "angle": -26.55, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.4, "angle": -27.39, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 3.74}]}, "bone63": {"rotate": [{"angle": -5.62, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1667, "angle": -15.1, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2, "angle": -15.83, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -5.62}]}, "dianballall": {"translate": [{"y": 17.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3, "x": -21.44, "y": 172.94, "curve": "stepped"}, {"time": 1.2333, "x": -21.44, "y": 172.94, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "y": 17.6}], "scale": [{}, {"time": 0.3, "x": 2, "y": 2, "curve": "stepped"}, {"time": 0.4333, "x": 2, "y": 2}, {"time": 0.6667}, {"time": 1.2333, "x": 2, "y": 2}, {"time": 1.4}]}, "bone110": {"translate": [{"time": 0.4, "x": 39.54, "y": 499.92, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 39.54, "y": 76.87, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 39.54, "y": 499.92}]}, "bone91": {"translate": [{"time": 0.8333, "x": -339.99, "y": -64.25}]}, "faz": {"translate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 156.62, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "y": -266.42, "curve": 0.25, "c3": 0.75}, {"time": 1.4}]}, "shouji2": {"scale": [{"time": 0.8667, "x": 1.82, "y": 1.82}]}, "shouji3": {"scale": [{"time": 0.9333, "x": 1.82, "y": 1.82}]}, "dianball": {"translate": [{"y": -423.04}]}, "loong": {"translate": [{"time": 0.4333, "x": -159.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -140.67, "y": 106.46}], "scale": [{"time": 0.4333, "x": 2.226, "y": 2.226, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 3.28, "y": 3.28}]}, "loong2": {"translate": [{"time": 0.4333, "x": -159.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -140.67, "y": 106.46}], "scale": [{"time": 0.4333, "x": 2.226, "y": 2.226, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 3.28, "y": 3.28}]}, "light": {"translate": [{"time": 0.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "x": -7.71, "y": 0.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": 10.11, "y": 200.51}], "scale": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.247, "y": 1.731}]}}, "path": {"long": {"position": [{"time": 0.2, "position": -0.2676, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "position": -0.0863, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.8, "position": 0.0382, "curve": 0.335, "c2": 0.34, "c3": 0.681, "c4": 0.71}, {"time": 1.1, "position": 1}], "spacing": [{"spacing": 0.02}]}}, "deform": {"default": {"long": {"long": [{"offset": 1, "vertices": [5.145, 2e-05, 5.145, 2e-05, 5.145, 0, 9.26099, 0, 9.26099, 0, 9.26099], "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.2, "vertices": [-583.2532, 46.80663, -583.2532, 46.80663, -583.2532, 46.80663, -0.38802, 13.52929, -0.38802, 13.52929, -0.38802, 13.52929], "curve": 0.31, "c2": 0.26, "c3": 0.654, "c4": 0.63}, {"time": 0.2667, "vertices": [-717.4237, 113.63847, -717.4237, 113.63847, -717.4237, 113.63847, -359.26477, -77.64336, -302.62054, -78.1935, -193.55292, -294.00854, -55.94302, 181.824, 231.48892, 222.7161, 691.91907, 15.436, 190.4277, -551.57806, 190.4277, -551.57806, 190.4277, -551.57806, 313.2799, 153.1583, 313.2799, 153.1583, 313.2799, 153.1583], "curve": 0.363, "c2": 0.6, "c3": 0.699, "c4": 0.96}, {"time": 0.8, "vertices": [-5.05914, 5.91008, -5.05914, 5.91008, -5.05914, 5.91008, -3.74283, 21.95165, -3.34338, 21.94777, -2.57428, 20.42587, -0.3945, 1.2822, 1.63242, 1.57056, 4.87927, 0.10885, 1.34286, -3.88965, 1.34286, -3.88965, 1.34286, -3.88965, 6.47159, 126.77263, 6.47159, 126.77263, 6.47159, 126.77263, 1163.4581, 47.19998, 1164.7966, 29.38544, 1273.9868, 24.42225, 1478.7965, 302.30356, 1498.6498, 401.5677, 1507.2935, 448.2846, 1222.5771, 433.43518, 1557.6938, 438.36386, 1558.0085, 432.0635]}]}}}, "events": [{"time": 0.8667, "name": "atk"}]}, "boss_idle": {"slots": {"dianball/qf_qing_jn3_qtgd_xlsd_00": {"attachment": [{"name": "dianball/qf_qing_jn3_qtgd_xlsd_00"}, {"time": 0.0333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_02"}, {"time": 0.0667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_04"}, {"time": 0.1, "name": "dianball/qf_qing_jn3_qtgd_xlsd_06"}, {"time": 0.1667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_08"}, {"time": 0.2, "name": "dianball/qf_qing_jn3_qtgd_xlsd_10"}, {"time": 0.2333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_12"}, {"time": 0.2667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_14"}, {"time": 0.3, "name": "dianball/qf_qing_jn3_qtgd_xlsd_16"}, {"time": 0.3333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_18"}, {"time": 0.3667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_20"}, {"time": 0.4, "name": "dianball/qf_qing_jn3_qtgd_xlsd_22"}, {"time": 0.4667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_00"}, {"time": 0.5, "name": "dianball/qf_qing_jn3_qtgd_xlsd_02"}, {"time": 0.5333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_04"}, {"time": 0.5667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_06"}, {"time": 0.6, "name": "dianball/qf_qing_jn3_qtgd_xlsd_08"}, {"time": 0.6333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_10"}, {"time": 0.6667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_12"}, {"time": 0.7333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_14"}, {"time": 0.7667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_16"}, {"time": 0.8, "name": "dianball/qf_qing_jn3_qtgd_xlsd_18"}, {"time": 0.8333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_20"}, {"time": 0.8667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_22"}, {"time": 0.9, "name": "dianball/qf_qing_jn3_qtgd_xlsd_00"}, {"time": 0.9333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_02"}, {"time": 0.9667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_04"}, {"time": 1.0333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_06"}, {"time": 1.0667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_08"}, {"time": 1.1, "name": "dianball/qf_qing_jn3_qtgd_xlsd_10"}, {"time": 1.1333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_12"}, {"time": 1.1667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_14"}, {"time": 1.2, "name": "dianball/qf_qing_jn3_qtgd_xlsd_16"}, {"time": 1.2333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_18"}, {"time": 1.2667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_20"}, {"time": 1.3333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_22"}]}, "dianball/qf_qing_jn3_qtgd_rw_sd_01": {"attachment": [{"name": "dianball/qf_qing_jn3_qtgd_rw_sd_01"}, {"time": 0.0333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_03"}, {"time": 0.1, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_05"}, {"time": 0.1667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_07"}, {"time": 0.2, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_09"}, {"time": 0.2667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_12"}, {"time": 0.3, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_15"}, {"time": 0.3333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_17"}, {"time": 0.4, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_19"}, {"time": 0.4667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_01"}, {"time": 0.5, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_03"}, {"time": 0.5667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_05"}, {"time": 0.6, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_07"}, {"time": 0.6333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_09"}, {"time": 0.7333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_12"}, {"time": 0.7667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_15"}, {"time": 0.8, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_17"}, {"time": 0.8667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_19"}, {"time": 0.9, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_01"}, {"time": 0.9333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_03"}, {"time": 1.0333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_05"}, {"time": 1.0667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_07"}, {"time": 1.1, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_09"}, {"time": 1.1667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_12"}, {"time": 1.2, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_15"}, {"time": 1.2333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_17"}, {"time": 1.3333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_19"}]}}, "bones": {"bone2": {"translate": [{"y": -4.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -4.99}]}, "bone4": {"translate": [{"x": 0.01, "y": 0.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.06, "y": 5.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.01, "y": 0.94}]}, "bone5": {"rotate": [{"angle": 1.28}], "translate": [{"x": 3.65, "y": -0.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 7.3, "y": -0.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 3.65, "y": -0.14}]}, "bone6": {"rotate": [{"angle": 2.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 2.1}]}, "bone17": {"translate": [{"x": 2.03, "y": -0.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.05, "y": -0.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 2.03, "y": -0.12}]}, "bone49": {"rotate": [{"angle": 0.2, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -3.43, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 0.2}]}, "bone50": {"rotate": [{"angle": -1.67, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -3.43, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -1.67}]}, "bone51": {"rotate": [{"angle": -4.68, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "angle": 8.24, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -4.88, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -4.68}]}, "bone52": {"translate": [{"x": 1.15, "y": -0.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.31, "y": -0.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 1.15, "y": -0.12}]}, "bone77": {"rotate": [{"angle": -0.27}]}, "bone78": {"rotate": [{"angle": -0.39}]}, "bone83": {"rotate": [{"angle": -0.1}]}, "bone84": {"rotate": [{"angle": -0.62}]}, "bone108": {"rotate": [{"angle": -0.54}]}, "bone109": {"rotate": [{"angle": -4.48}]}, "bone76": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.79, "y": 0.75, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone44": {"rotate": [{"angle": -1.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -2.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.7667, "angle": -0.88, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.44}]}, "bone45": {"rotate": [{"angle": -0.63, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "angle": -7.62, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.7667, "angle": -5.78, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.2333, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "angle": -0.63}]}, "bone46": {"rotate": [{"angle": -2.4, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.7667, "angle": -18.15, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.8, "angle": -18.46, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -2.4}]}, "bone14": {"rotate": [{"angle": 6.92, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": 6.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": 34.37, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -2.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -1.06}]}, "bone7": {"rotate": [{"angle": -1.16, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": 3.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 6.61, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -3.66, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -1.16}]}, "bone11": {"rotate": [{"angle": -2.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -9.09, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.2333, "angle": -8.8, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.4, "angle": -2.99, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8667, "angle": 16.09, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -2.99}]}, "bone12": {"rotate": [{"angle": 7.24, "curve": 0.328, "c2": 0.32, "c3": 0.68, "c4": 0.71}, {"time": 0.2333, "angle": -16.61, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.4, "angle": -28.4, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.4333, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 23.44, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 7.24}]}, "bone8": {"rotate": [{"angle": -4.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -10, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "angle": -8.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3667, "angle": -3.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8333, "angle": 18.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -4.74}]}, "bone9": {"rotate": [{"angle": 13.28, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.2333, "angle": -8.56, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.3667, "angle": -16.68, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.4, "angle": -17.52, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 31.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 13.28}]}, "bone10": {"rotate": [{"angle": 41.05, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.2333, "angle": 17.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.3667, "angle": 0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6333, "angle": -24.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 41.86, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": 41.05}]}, "bone89": {"rotate": [{"angle": -6.01, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": -7.62, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 11.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.9667, "angle": 7.11, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 1.3333, "angle": -6.01}]}, "bone88": {"rotate": [{"angle": -2.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 5.37, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.9667, "angle": -1.14, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.2, "angle": -3.99, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -2.77}]}, "bone90": {"rotate": [{"angle": 9.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": -5.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "angle": 3.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2, "angle": 12.01, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 9.77}]}, "bone55": {"rotate": [{"angle": 4.19, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4333, "angle": -1.38, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.6667, "angle": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.19}]}, "bone56": {"rotate": [{"angle": 2.39, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.3667, "angle": 15.37, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4333, "angle": 14.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.5667, "angle": 9.82, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.0333, "angle": -7.55, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.3333, "angle": 2.39}]}, "bone67": {"rotate": [{"angle": 1.08, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.1, "angle": -1.02, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": -4.31, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.48, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.2333, "angle": 3.18, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 1.3333, "angle": 1.08}]}, "bone68": {"rotate": [{"angle": 15.22, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 15.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": 14.43, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.2333, "angle": 8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": -12.15, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.2333, "angle": 11.93, "curve": 0.362, "c2": 0.5, "c3": 0.7, "c4": 0.85}, {"time": 1.3333, "angle": 15.22}]}, "bone57": {"rotate": [{"angle": -10.24, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.4333, "angle": 13.78, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.5667, "angle": 18.92, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.6, "angle": 19.45, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -11.58, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -10.24}]}, "bone69": {"rotate": [{"angle": 5.37, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.1, "angle": 12.75, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.2333, "angle": 19.48, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2667, "angle": 20.17, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -20.49, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.2333, "angle": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 1.3333, "angle": 5.37}]}, "bone58": {"rotate": [{"angle": 12.61, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": 18.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 22.91, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 9.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 12.61}]}, "bone59": {"rotate": [{"angle": -9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -15.32, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "angle": -14.02, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3667, "angle": -7.38, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8333, "angle": 17.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -9.27}]}, "bone60": {"rotate": [{"angle": 3.74, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.2333, "angle": -18.33, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.3667, "angle": -26.55, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.4, "angle": -27.39, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 22.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 3.74}]}, "bone63": {"rotate": [{"angle": -5.62, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1667, "angle": -15.1, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2, "angle": -15.83, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 26.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -5.62}]}, "bone62": {"rotate": [{"angle": -13.47, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1667, "angle": -6.49, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6333, "angle": 16.39, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -13.81, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -13.47}]}, "bone61": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.67, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 3.4}]}, "bone64": {"rotate": [{"angle": -4.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -5.69, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 6.35, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.2333, "angle": -2.02, "curve": 0.348, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 1.3333, "angle": -4.15}]}, "bone65": {"rotate": [{"angle": 12.72, "curve": 0.328, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.0333, "angle": 11.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 17.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.2333, "angle": 16.53, "curve": 0.306, "c2": 0.21, "c3": 0.643, "c4": 0.56}, {"time": 1.3333, "angle": 12.72}]}, "bone66": {"rotate": [{"angle": 22.9, "curve": 0.346, "c2": 0.43, "c3": 0.679, "c4": 0.77}, {"time": 0.0333, "angle": 23.87, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "angle": 24.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -12.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2333, "angle": 17.73, "curve": 0.357, "c2": 0.44, "c3": 0.694, "c4": 0.79}, {"time": 1.3333, "angle": 22.9}]}, "bone16": {"rotate": [{"angle": 5.15, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": -1.81, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 39.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 5.15}]}, "a5": {"rotate": [{"angle": -2.24, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -2.24}]}, "dianballall": {"translate": [{"y": 17.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": 21.58, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "y": 17.6}]}, "dianball2": {"scale": [{"x": 0.564, "y": 0.564}]}}, "deform": {"default": {"long": {"long": [{"offset": 1, "vertices": [5.145, 2e-05, 5.145, 2e-05, 5.145, 0, 9.26099, 0, 9.26099, 0, 9.26099], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "offset": 1, "vertices": [10.29002, 3e-05, 10.29002, 3e-05, 10.29002, 0, 18.522, 0, 18.522, 0, 18.522], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "offset": 1, "vertices": [5.145, 2e-05, 5.145, 2e-05, 5.145, 0, 9.26099, 0, 9.26099, 0, 9.26099]}]}}}}, "die": {"slots": {"a1": {"color": [{"time": 0.1667, "color": "ffffff00"}]}}, "bones": {"bone2": {"translate": [{"curve": 0.25, "c3": 0.471}, {"time": 0.0667, "y": 44.45, "curve": 0.791, "c3": 0.75}, {"time": 0.2667, "y": -145.2, "curve": 0.25, "c3": 0.191}, {"time": 0.3333, "y": -131.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": -145.2}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 82.96}]}, "bone4": {"translate": [{"x": 0.01, "y": 0.94}]}, "bone5": {"rotate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "angle": -18.67, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333}], "translate": [{"x": 2.56, "y": -0.1}]}, "bone6": {"rotate": [{"angle": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.91, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 23.8, "curve": 0.25, "c3": 0}, {"time": 0.3333, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 29.32}]}, "bone17": {"translate": [{"x": 2.76, "y": 0.03}]}, "bone49": {"rotate": [{"angle": 1.79, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1667, "angle": -42.34, "curve": 0.329, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.2333, "angle": 41.94, "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 0.4333, "angle": 18.27}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 21.71, "y": 5.68}]}, "bone50": {"rotate": [{"curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 0.2333, "angle": 14.25, "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 0.4333, "angle": 35.73}]}, "bone51": {"rotate": [{"angle": 2.11, "curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 0.1667, "angle": 12.44, "curve": 0.332, "c2": 0.33, "c3": 0.679, "c4": 0.7}, {"time": 0.2333, "angle": 63.88, "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 0.3, "angle": 129.2}, {"time": 0.4333, "angle": 141}]}, "bone52": {"translate": [{"x": 2.35, "y": -0.13, "curve": "stepped"}, {"time": 0.2667, "x": 2.35, "y": -0.13, "curve": 0.25, "c3": 0}, {"time": 0.3333, "x": 5.48, "y": -46.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 4.49, "y": -31.57}]}, "bone80": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": 0.77, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 89.47}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": 42.27, "y": 54.11, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "x": 153.23, "y": 82.89, "curve": 0.836, "c3": 0.75}, {"time": 0.4333, "x": 153.23, "y": 49.52}]}, "bone86": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": 12.47, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 97.48, "curve": 1, "c3": 0.75}, {"time": 0.3333, "angle": 98.78}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": 28.36, "y": 39.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "x": 246.46, "y": 46.89, "curve": 1, "c3": 0.75}, {"time": 0.3333, "x": 248.59, "y": 9.4}]}, "bone108": {"rotate": [{"angle": 2.69}]}, "bone109": {"rotate": [{"angle": -4.6}]}, "bone44": {"rotate": [{}, {"time": 0.2667, "angle": -10.7}, {"time": 0.4333, "angle": -46.05}], "translate": [{}, {"time": 0.4333, "x": -0.69, "y": 38.56}]}, "bone45": {"rotate": [{}, {"time": 0.2667, "angle": 0.72}, {"time": 0.4333, "angle": -90.27}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 37.81, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 11.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4333, "angle": -33.61}]}, "bone66": {"rotate": [{"angle": 23.87, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 24.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -12.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "angle": 17.73, "curve": 0.357, "c2": 0.44, "c3": 0.694, "c4": 0.79}, {"time": 0.5, "angle": 22.9}]}, "a5": {"rotate": [{"angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.56}]}, "bone14": {"rotate": [{"angle": 6.92, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 6.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 34.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -2.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": -1.06}]}, "bone7": {"rotate": [{"angle": -1.16, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": 3.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 6.61, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.2667, "angle": 84.69}]}, "bone11": {"rotate": [{"angle": -2.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -9.09, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.1, "angle": -8.8, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1667, "angle": -2.99, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 16.09, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -2.99}]}, "bone12": {"rotate": [{"angle": 7.24, "curve": 0.328, "c2": 0.32, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "angle": -16.61, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1667, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 23.44, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 7.24}]}, "bone8": {"rotate": [{"angle": -4.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -10, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": -8.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": -3.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": 18.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -4.74}]}, "bone9": {"rotate": [{"angle": 13.28, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.1, "angle": -8.56, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": -16.68, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": -17.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 31.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "angle": 13.28}]}, "bone10": {"rotate": [{"angle": 41.05, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.1, "angle": 17.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": 0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "angle": -24.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 41.05}]}, "bone89": {"rotate": [{"angle": -6.01, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": -7.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3667, "angle": 7.11, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.5, "angle": -6.01}]}, "bone88": {"rotate": [{"angle": -2.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "angle": 5.37, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3667, "angle": -1.14, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": -3.99, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": -2.77}]}, "bone90": {"rotate": [{"angle": 9.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "angle": -5.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 3.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 12.01, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": 9.77}]}, "bone55": {"rotate": [{"angle": 4.19, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.1667, "angle": -1.38, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": -3.81, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3333, "angle": 19.22, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.5, "angle": -4.45}]}, "bone56": {"rotate": [{"angle": 2.39, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.1333, "angle": 15.37, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1667, "angle": 14.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.2, "angle": 9.82, "curve": 0.324, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.3333, "angle": -17.46, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4, "angle": 22.76, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "angle": 2.39}]}, "bone67": {"rotate": [{"angle": 1.08, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": -1.02, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": -4.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.48, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.4667, "angle": 3.18, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.5, "angle": 1.08}]}, "bone68": {"rotate": [{"angle": 15.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 14.43, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": 8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": -12.15, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4667, "angle": 11.93, "curve": 0.362, "c2": 0.5, "c3": 0.7, "c4": 0.85}, {"time": 0.5, "angle": 15.22}]}, "bone57": {"rotate": [{"angle": -10.24, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.1667, "angle": 13.78, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.2, "angle": 18.92, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2333, "angle": 19.45, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.3333, "angle": -8.25, "curve": 0.336, "c2": 0.34, "c3": 0.682, "c4": 0.71}, {"time": 0.4, "angle": 37.81, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.5, "angle": -10.24}]}, "bone69": {"rotate": [{"angle": 5.37, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 12.75, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": 20.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -20.49, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.4667, "angle": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 0.5, "angle": 5.37}]}, "bone58": {"rotate": [{"angle": 12.61, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": 18.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 22.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 49.04, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 49.75}]}, "bone59": {"rotate": [{"angle": -9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -15.32, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": -14.02, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": -7.38, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": 17.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 4.58}]}, "bone60": {"rotate": [{"angle": 3.74, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.1, "angle": -18.33, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": -26.55, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": -27.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 22.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "angle": 3.74}]}, "bone63": {"rotate": [{"angle": -5.62, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.0667, "angle": -15.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": 22.73}]}, "bone62": {"rotate": [{"angle": -13.47, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.0667, "angle": -6.49, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 16.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 16.02}]}, "bone61": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 22.38, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": 26.32}]}, "bone64": {"rotate": [{"angle": -4.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -5.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 6.35, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4667, "angle": -2.02, "curve": 0.348, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 0.5, "angle": -4.15}]}, "bone65": {"rotate": [{"angle": 11.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 17.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "angle": 16.53, "curve": 0.306, "c2": 0.21, "c3": 0.643, "c4": 0.56}, {"time": 0.5, "angle": 12.72}]}, "bone16": {"rotate": [{"angle": 5.15, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 39.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 5.15}]}, "dianballall": {"translate": [{"y": 17.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": -337.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "x": -73.21, "y": -337.04}]}, "a0": {"translate": [{"time": 0.3667}, {"time": 0.4333, "y": 32.89}, {"time": 0.5}]}, "bone95": {"translate": [{"time": 0.2, "curve": 0.932, "c3": 0.75}, {"time": 0.3333, "x": 18.67, "y": -230.29}]}, "bone94": {"translate": [{"time": 0.2, "curve": 0.932, "c3": 0.75}, {"time": 0.4333, "x": 31.12, "y": -426.34}]}, "bone93": {"rotate": [{"time": 0.2}, {"time": 0.5, "angle": -42.49}], "translate": [{"time": 0.2, "curve": 0.932, "c3": 0.75}, {"time": 0.5, "x": -104.86, "y": -292.53}]}, "bone92": {"translate": [{"time": 0.2, "curve": 0.932, "c3": 0.75}, {"time": 0.3333, "x": -57.57, "y": -217.84}]}, "bone91": {"translate": [{"time": 0.2, "curve": 0.932, "c3": 0.75}, {"time": 0.4667, "x": -233.4, "y": -42.01}]}}, "deform": {"default": {"long": {"long": [{"offset": 1, "vertices": [5.145, 2e-05, 5.145, 2e-05, 5.145, 0, 9.26099, 0, 9.26099, 0, 9.26099], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "offset": 1, "vertices": [9.4956, 3e-05, 9.4956, 3e-05, 9.4956, 67.96951, -40.83653, 0, 17.09204, -14.09003, 3.74833], "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.3667, "offset": 1, "vertices": [10.29002, 3e-05, 10.29002, 3e-05, 10.29002, 73.65601, -44.25302, 0, 18.522, -15.26884, 4.06192], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "offset": 1, "vertices": [5.145, 2e-05, 5.145, 2e-05, 5.145, 0, 9.26099, 0, 9.26099, 0, 9.26099, 19.84502, -26.45996, 19.84502, -26.45996, 19.84502, -26.45996]}]}}}, "drawOrder": [{"time": 0.2, "offsets": [{"slot": "a11", "offset": -10}, {"slot": "a10", "offset": -10}]}]}, "hurt": {"slots": {"a1": {"attachment": [{"time": 0.1, "name": null}]}}, "bones": {"bone2": {"translate": [{"y": -4.99}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"x": 0.01, "y": 0.94}], "scale": [{"x": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone5": {"rotate": [{"angle": 3.24, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 1.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 3.24}], "translate": [{"x": 3.65, "y": -0.14}]}, "bone6": {"rotate": [{"angle": 23.67, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": 27.43, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 23.67}]}, "bone17": {"translate": [{"x": 2.03, "y": -0.12}]}, "bone49": {"rotate": [{"angle": 6.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 17.32, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 6.5}]}, "bone50": {"rotate": [{"angle": 13.22, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -1.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.45, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 13.22}]}, "bone51": {"rotate": [{"angle": 10.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 12.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -4.68, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 10.22}]}, "bone52": {"translate": [{"x": 1.15, "y": -0.12}]}, "bone77": {"rotate": [{"angle": 5.79}]}, "bone78": {"rotate": [{"angle": -12.11}]}, "bone83": {"rotate": [{"angle": -3.5}]}, "bone84": {"rotate": [{"angle": 5.89}]}, "bone108": {"rotate": [{"angle": 3.63}]}, "bone109": {"rotate": [{"angle": -7.98}]}, "bone44": {"rotate": [{"angle": 13.72, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 39.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 13.72}]}, "bone45": {"rotate": [{"angle": 35.23, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 40.59, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 35.23}]}, "bone46": {"rotate": [{"angle": 33.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 38.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 33.46}]}, "bone14": {"rotate": [{"angle": 6.92, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 44.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 36.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.06}]}, "bone7": {"rotate": [{"angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 43.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.16}]}, "bone11": {"rotate": [{"angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.99}]}, "bone12": {"rotate": [{"angle": 23.17, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 7.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 50.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 23.17}]}, "bone8": {"rotate": [{"angle": 11.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -4.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 38.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 11.19}]}, "bone9": {"rotate": [{"angle": 50.96, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 13.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 56.59, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 50.96}]}, "bone10": {"rotate": [{"angle": 78.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 84.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 41.05, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 78.73}]}, "bone89": {"rotate": [{"angle": -6.01}]}, "bone88": {"rotate": [{"angle": -2.77}]}, "bone90": {"rotate": [{"angle": 9.77}]}, "bone55": {"rotate": [{"angle": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 34.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.19}]}, "bone56": {"rotate": [{"angle": 13.58, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 32.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 13.58}]}, "bone67": {"rotate": [{"angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.08}]}, "bone68": {"rotate": [{"angle": 23.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 15.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 36.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 23.1}]}, "bone57": {"rotate": [{"angle": 16.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 20.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.24, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 16.19}]}, "bone69": {"rotate": [{"angle": 23.27, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 25.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.37, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 23.27}]}, "bone58": {"rotate": [{"angle": 12.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 24.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.61}]}, "bone59": {"rotate": [{"angle": -5.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 2.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -5.03}]}, "bone60": {"rotate": [{"angle": 13.78, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 3.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.28, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 13.78}]}, "bone63": {"rotate": [{"angle": 14.07, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 17.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.62, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 14.07}]}, "bone62": {"rotate": [{"angle": -5.14, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -13.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.17, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -5.14}]}, "bone61": {"rotate": [{"angle": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 26.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.4}]}, "bone64": {"rotate": [{"angle": -4.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 23.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.15}]}, "bone65": {"rotate": [{"angle": 19.73, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 10.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 34.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 19.73}]}, "bone66": {"rotate": [{"angle": 44.28, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 47.33, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 23.87, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 44.28}]}, "bone16": {"rotate": [{"angle": 5.15}]}, "a5": {"rotate": [{"angle": -2.24}]}, "dianballall": {"translate": [{"y": 17.6}]}, "bone20": {"rotate": [{"angle": -49.29}], "translate": [{"x": 395.45, "y": -88.33}]}, "bone21": {"rotate": [{"angle": -42.82}], "translate": [{"x": 328.54, "y": -56.51}]}, "bone22": {"rotate": [{"angle": -25.96}], "translate": [{"x": 255.19, "y": -36.34}]}, "bone23": {"rotate": [{"angle": 16.66}], "translate": [{"x": 179.25, "y": -48.2}]}, "bone24": {"rotate": [{"angle": 65.29}], "translate": [{"x": 126.51, "y": -88.34}]}, "bone25": {"rotate": [{"angle": 116.84}], "translate": [{"x": 111.92, "y": -126.56}]}, "bone26": {"rotate": [{"angle": 128.44}], "translate": [{"x": 106.11, "y": -161.98}]}, "bone27": {"rotate": [{"angle": 107.36}], "translate": [{"x": 85.55, "y": -204.92}]}, "bone28": {"rotate": [{"angle": 61.04}], "translate": [{"x": 32.44, "y": -238.97}]}, "bone29": {"rotate": [{"angle": -24.29}], "translate": [{"x": -43.12, "y": -219.2}]}, "bone30": {"rotate": [{"angle": -21.93}], "translate": [{"x": -117.53, "y": -201.96}]}, "bone31": {"rotate": [{"angle": -4.52}], "translate": [{"x": -195.56, "y": -197.66}]}, "bone32": {"rotate": [{"angle": 34.34}], "translate": [{"x": -265.4, "y": -221.54}]}, "bone33": {"rotate": [{"angle": 74.66}], "translate": [{"x": -311.2, "y": -263.93}]}, "bone34": {"rotate": [{"angle": 101.94}], "translate": [{"x": -335.96, "y": -307.69}]}, "bone35": {"rotate": [{"angle": 73.78}], "translate": [{"x": 227.15, "y": 7.11}]}, "bone36": {"rotate": [{"angle": 66.84}], "translate": [{"x": 137.61, "y": -119.7}]}, "bone37": {"rotate": [{"angle": 63.01}], "translate": [{"x": 39.55, "y": -242.81}]}, "bone38": {"rotate": [{"angle": 60.07}], "translate": [{"x": -64.58, "y": -362.18}]}, "bone39": {"rotate": [{"angle": 56.51}], "translate": [{"x": -175.42, "y": -476.03}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "scale": [{"x": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "a0": {"translate": [{"y": 16.55, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "y": 56.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": -6.68, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "y": 16.55}]}}, "deform": {"default": {"long": {"long": [{"offset": 1, "vertices": [5.145, 2e-05, 5.145, 2e-05, 5.145, 0, 9.26099, 0, 9.26099, 0, 9.26099]}]}}}}, "run1": {"slots": {"a32": {"color": [{"color": "ffffff00"}]}, "a25": {"color": [{"color": "ffffffff"}]}, "a23": {"color": [{"color": "ffffff00"}]}, "a22": {"color": [{"color": "ffffff00"}]}, "dianball/qf_qing_jn3_qtgd_xlsd_00": {"attachment": [{"name": "dianball/qf_qing_jn3_qtgd_xlsd_00"}, {"time": 0.0333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_02"}, {"time": 0.1, "name": "dianball/qf_qing_jn3_qtgd_xlsd_04"}, {"time": 0.1333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_06"}, {"time": 0.2, "name": "dianball/qf_qing_jn3_qtgd_xlsd_08"}, {"time": 0.2333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_10"}, {"time": 0.3, "name": "dianball/qf_qing_jn3_qtgd_xlsd_12"}, {"time": 0.3333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_14"}, {"time": 0.3667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_16"}, {"time": 0.4333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_18"}, {"time": 0.4667, "name": "dianball/qf_qing_jn3_qtgd_xlsd_20"}, {"time": 0.5333, "name": "dianball/qf_qing_jn3_qtgd_xlsd_22"}]}, "a21": {"color": [{"color": "ffffff00"}]}, "a20": {"color": [{"color": "ffffff00"}]}, "a15": {"color": [{"color": "ffffff00"}]}, "a24": {"color": [{"color": "ffffff00"}]}, "dianball/qf_qing_jn3_qtgd_rw_sd_01": {"attachment": [{"name": "dianball/qf_qing_jn3_qtgd_rw_sd_01"}, {"time": 0.0333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_03"}, {"time": 0.1, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_05"}, {"time": 0.1333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_07"}, {"time": 0.1667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_09"}, {"time": 0.2333, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_12"}, {"time": 0.2667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_15"}, {"time": 0.3, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_17"}, {"time": 0.3667, "name": "dianball/qf_qing_jn3_qtgd_rw_sd_19"}]}}, "bones": {"bone2": {"rotate": [{"angle": -13.78}], "translate": [{"y": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 3.93}]}, "bone4": {"rotate": [{"angle": -9.87}], "translate": [{"x": 0.01, "y": 0.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.06, "y": 5.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 0.01, "y": 0.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.06, "y": 5.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "x": 0.01, "y": 0.94}]}, "bone5": {"rotate": [{"angle": -4.96}], "translate": [{"x": 2.56, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 5.11, "y": -0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 2.56, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 5.11, "y": -0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "x": 2.56, "y": -0.1}]}, "bone6": {"rotate": [{"angle": 19.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 20.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 19.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 20.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 19.83}]}, "bone17": {"translate": [{"x": 2.76, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 5.52, "y": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 2.76, "y": 0.03}]}, "bone52": {"translate": [{"x": 2.35, "y": -0.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 15.91, "y": -25.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": 2.35, "y": -0.13}]}, "bone76": {"translate": [{"x": 6.73, "y": -10.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 22.02, "y": -32.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 11.08, "y": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": -2.21, "y": -3.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": 6.73, "y": -10.29}]}, "bone80": {"translate": [{"x": 61.42, "curve": 0.25, "c3": 0.417}, {"time": 0.2667, "x": -163.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 61.42}]}, "bone81": {"rotate": [{"angle": 17.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": 1.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 0.54, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": -91.96, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2667, "angle": -118.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -117.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "angle": 17.23}], "translate": [{"x": -18.09, "y": 10.95, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": 16.65, "y": -0.13, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "x": 0.03, "y": 2.48, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "x": -0.93, "y": 58.24, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2667, "x": 6.02, "y": 98.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": -27.07, "y": 78.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": -18.09, "y": 10.95}]}, "bone107": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -9.81, "y": 38.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 21.97, "y": 14.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333}]}, "bone108": {"rotate": [{"angle": 2.69}]}, "bone109": {"rotate": [{"angle": -4.6}]}, "bone110": {"translate": [{"x": -103.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 125.32, "curve": 0.25, "c3": 0.399}, {"time": 0.5333, "x": -103.59}]}, "bone111": {"rotate": [{"angle": -130.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -107.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 19.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 4.18, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4, "angle": -2.48, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4667, "angle": -61.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "angle": -130.46}], "translate": [{"x": 8.09, "y": 96.67, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -25.41, "y": 70.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": -23.17, "y": 18.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": 0.03, "y": 2.49, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4, "x": 0.03, "y": 3, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4667, "x": -13.7, "y": 47.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "x": 8.09, "y": 96.67}]}, "bone61": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.67, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": 3.4}]}, "bone64": {"rotate": [{"angle": 27.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 25.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 37.52, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": 29.15, "curve": 0.348, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 0.5333, "angle": 27.02}], "translate": [{"x": -3.31, "y": 12.85}]}, "bone65": {"rotate": [{"angle": 11.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 17.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 16.53, "curve": 0.306, "c2": 0.21, "c3": 0.643, "c4": 0.56}, {"time": 0.5333, "angle": 12.72}]}, "bone66": {"rotate": [{"angle": 23.87, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 24.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -12.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 17.73, "curve": 0.357, "c2": 0.44, "c3": 0.694, "c4": 0.79}, {"time": 0.5333, "angle": 22.9}]}, "a5": {"rotate": [{"angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.56}]}, "bone16": {"rotate": [{"angle": 5.15, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 39.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 5.15}]}, "bone14": {"rotate": [{"angle": 6.92, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 6.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 34.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -1.06}]}, "bone7": {"rotate": [{"angle": -39.88, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": -35.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": -32.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -42.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": -39.88}]}, "bone11": {"rotate": [{"angle": -2.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -9.09, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.1, "angle": -8.8, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1667, "angle": -2.99, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3667, "angle": 16.09, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": -2.99}], "translate": [{"x": 31.34, "y": 22.01}]}, "bone12": {"rotate": [{"angle": 7.24, "curve": 0.328, "c2": 0.32, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "angle": -16.61, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1667, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 23.44, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5333, "angle": 7.24}]}, "bone8": {"rotate": [{"angle": -4.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -10, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": -8.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1667, "angle": -3.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 18.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -4.74}]}, "bone9": {"rotate": [{"angle": 13.28, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.1, "angle": -8.56, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1667, "angle": -17.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 31.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": 13.28}]}, "bone10": {"rotate": [{"angle": 41.05, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.1, "angle": 17.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": 0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -24.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 41.05}]}, "bone89": {"rotate": [{"angle": 5.29, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "angle": 11.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2, "angle": 7.11, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.3333, "angle": -6.01, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3667, "angle": -7.62, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": 5.29}]}, "bone88": {"rotate": [{"angle": 4.87, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 5.37, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2, "angle": -1.14, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": -3.99, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -2.77, "curve": 0.31, "c2": 0.26, "c3": 0.717, "c4": 0.84}, {"time": 0.5333, "angle": 4.87}]}, "bone90": {"rotate": [{"angle": 8.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 12.01, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": 9.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3333, "angle": -5.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4667, "angle": 3.42, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.5333, "angle": 8.86}]}, "bone55": {"rotate": [{"angle": 24.06, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.1667, "angle": 18.49, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": 16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 24.06}], "translate": [{"x": -17.93, "y": 7.56}]}, "bone56": {"rotate": [{"angle": 16.31, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "angle": 28.38, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.2333, "angle": 23.74, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4333, "angle": 6.36, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5333, "angle": 16.31}]}, "bone67": {"rotate": [{"angle": 32.25, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 30.15, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 37.65, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 34.35, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.5333, "angle": 32.25}], "translate": [{"x": -3.31, "y": 12.85}]}, "bone68": {"rotate": [{"angle": 15.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 14.43, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": 8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -12.15, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": 11.93, "curve": 0.362, "c2": 0.5, "c3": 0.7, "c4": 0.85}, {"time": 0.5333, "angle": 15.22}]}, "bone57": {"rotate": [{"angle": -10.24, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.1667, "angle": 13.78, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.2333, "angle": 19.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.24}]}, "bone69": {"rotate": [{"angle": 5.37, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 12.75, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": 20.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -20.49, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "angle": -2.68, "curve": 0.333, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 0.5333, "angle": 5.37}]}, "bone58": {"rotate": [{"angle": 12.61, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": 18.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 22.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 9.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": 12.61}]}, "bone59": {"rotate": [{"angle": -9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -15.32, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": -14.02, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1667, "angle": -7.38, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 17.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -9.27}]}, "bone60": {"rotate": [{"angle": 3.74, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.1, "angle": -18.33, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1667, "angle": -27.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 22.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": 3.74}]}, "bone63": {"rotate": [{"angle": -5.62, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.0667, "angle": -15.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 26.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": -5.62}]}, "bone62": {"rotate": [{"angle": -13.47, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.0667, "angle": -6.49, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": 16.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -13.47}]}, "dianballall": {"translate": [{"x": 37.02, "y": 95.59, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "x": 37.02, "y": 78, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 37.02, "y": 99.57, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "x": 37.02, "y": 95.59}]}, "bone49": {"rotate": [{"angle": 0.2, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.43, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": 0.2}]}, "bone50": {"rotate": [{"angle": -1.67, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.43, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": -1.67}]}, "bone51": {"rotate": [{"angle": -4.68, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2333, "angle": 8.24, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.68}]}, "bone44": {"rotate": [{"angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -1.44}]}, "bone45": {"rotate": [{"angle": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 45.68, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -0.63}]}, "bone46": {"rotate": [{"angle": -12.02, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "angle": -22.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 8.27, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": -12.02}]}, "dianball2": {"translate": [{"x": 2.52, "y": 7.57}], "scale": [{"x": 0.907, "y": 0.907}]}}}, "run2": {"slots": {"a32": {"color": [{"color": "ffffff00"}]}, "a0": {"attachment": [{"name": null}]}, "a25": {"color": [{"color": "ffffffff"}]}, "a23": {"color": [{"color": "ffffff00"}]}, "a22": {"color": [{"color": "ffffff00"}]}, "a21": {"color": [{"color": "ffffff00"}]}, "a20": {"color": [{"color": "ffffff00"}]}, "a15": {"color": [{"color": "ffffff00"}]}, "a24": {"color": [{"color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{"angle": -13.78}], "translate": [{"y": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "y": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 3.93}]}, "bone4": {"rotate": [{"angle": -4.79}], "translate": [{"x": 0.01, "y": 0.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.06, "y": 5.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "x": 0.01, "y": 0.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.06, "y": 5.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 0.01, "y": 0.94}]}, "bone5": {"rotate": [{"angle": -12.57}], "translate": [{"x": 2.56, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 5.11, "y": -0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 2.56, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 5.11, "y": -0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 2.56, "y": -0.1}]}, "bone6": {"rotate": [{"angle": 19.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 20.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 19.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 20.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 19.83}]}, "bone17": {"translate": [{"x": 2.76, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 5.52, "y": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 2.76, "y": 0.03}]}, "bone52": {"translate": [{"x": 2.35, "y": -0.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": 15.91, "y": -25.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 2.35, "y": -0.13}]}, "bone76": {"translate": [{"x": 6.73, "y": -10.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 22.02, "y": -32.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": 11.08, "y": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": -2.21, "y": -3.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 6.73, "y": -10.29}]}, "bone80": {"translate": [{"x": 61.42, "curve": 0.25, "c3": 0.417}, {"time": 0.1333, "x": -163.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 61.42}]}, "bone81": {"rotate": [{"angle": 17.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": 1.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "angle": 0.54, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -91.96, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1333, "angle": -118.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -117.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 17.23}], "translate": [{"x": -18.09, "y": 10.95, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "x": 16.65, "y": -0.13, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "x": 0.03, "y": 2.48, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "x": -0.93, "y": 58.24, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1333, "x": 6.02, "y": 98.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": -27.07, "y": 78.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": -18.09, "y": 10.95}]}, "bone107": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -9.81, "y": 38.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": 21.97, "y": 14.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone108": {"rotate": [{"angle": 2.69}]}, "bone109": {"rotate": [{"angle": -4.6}]}, "bone110": {"translate": [{"x": -103.59, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 125.32, "curve": 0.25, "c3": 0.399}, {"time": 0.2667, "x": -103.59}]}, "bone111": {"rotate": [{"angle": -130.46, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -107.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 19.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 4.18, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2, "angle": -2.48, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2333, "angle": -61.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": -130.46}], "translate": [{"x": 8.09, "y": 96.67, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -25.41, "y": 70.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": -23.17, "y": 18.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": 0.03, "y": 2.49, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2, "x": 0.03, "y": 3, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2333, "x": -13.7, "y": 47.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "x": 8.09, "y": 96.67}]}, "bone61": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.67, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 3.4}]}, "bone64": {"rotate": [{"angle": 27.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 25.48, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 37.52, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 27.02}], "translate": [{"x": -3.31, "y": 12.85}]}, "bone65": {"rotate": [{"angle": 11.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 17.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2667, "angle": 12.72}]}, "bone66": {"rotate": [{"angle": 23.87, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 24.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 22.9}]}, "a5": {"rotate": [{"angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.56}]}, "bone16": {"rotate": [{"angle": 5.15, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 39.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 5.15}]}, "bone14": {"rotate": [{"angle": 6.92, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 6.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 34.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.06}]}, "bone7": {"rotate": [{"angle": -39.88, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0667, "angle": -35.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -32.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -39.88}]}, "bone11": {"rotate": [{"angle": -2.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -9.09, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.0667, "angle": -8.8, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1, "angle": -2.99, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": 16.09, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -2.99}], "translate": [{"x": 31.34, "y": 22.01}]}, "bone12": {"rotate": [{"angle": 7.24, "curve": 0.328, "c2": 0.32, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "angle": -16.61, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 23.44, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "angle": 7.24}]}, "bone8": {"rotate": [{"angle": -4.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -10, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0667, "angle": -8.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": -3.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 18.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -4.74}]}, "bone9": {"rotate": [{"angle": 13.28, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.0667, "angle": -8.56, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": -17.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 31.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": 13.28}]}, "bone10": {"rotate": [{"angle": 41.05, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.0667, "angle": 17.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -24.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 41.05}]}, "bone89": {"rotate": [{"angle": 5.29, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 11.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 7.11, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.1667, "angle": -6.01, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": -7.62, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.2667, "angle": 5.29}]}, "bone88": {"rotate": [{"angle": 4.87, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 5.37, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.1, "angle": -1.14, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": -2.77, "curve": 0.31, "c2": 0.26, "c3": 0.717, "c4": 0.84}, {"time": 0.2667, "angle": 4.87}]}, "bone90": {"rotate": [{"angle": 8.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 12.01, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "angle": 9.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1667, "angle": -5.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 3.42, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": 8.86}]}, "bone55": {"rotate": [{"angle": 24.06, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.1, "angle": 18.49, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 24.06}], "translate": [{"x": -17.93, "y": 7.56}]}, "bone56": {"rotate": [{"angle": 16.31, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.1, "angle": 28.38, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": 23.74, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 6.36, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2667, "angle": 16.31}]}, "bone67": {"rotate": [{"angle": 32.25, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 30.15, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 37.65, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "angle": 32.25}], "translate": [{"x": -3.31, "y": 12.85}]}, "bone68": {"rotate": [{"angle": 15.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 14.43, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -12.15, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": 15.22}]}, "bone57": {"rotate": [{"angle": -10.24, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.1, "angle": 13.78, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": 19.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.24}]}, "bone69": {"rotate": [{"angle": 5.37, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 12.75, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 20.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -20.49, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2667, "angle": 5.37}]}, "bone58": {"rotate": [{"angle": 12.61, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0667, "angle": 18.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 22.91, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": 12.61}]}, "bone59": {"rotate": [{"angle": -9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -15.32, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0667, "angle": -14.02, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": -7.38, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 17.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -9.27}]}, "bone60": {"rotate": [{"angle": 3.74, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.0667, "angle": -18.33, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": -27.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 22.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": 3.74}]}, "bone63": {"rotate": [{"angle": -5.62, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.0333, "angle": -15.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 26.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -5.62}]}, "bone62": {"rotate": [{"angle": -13.47, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.0333, "angle": -6.49, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 16.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.47}]}, "dianballall": {"translate": [{"x": 413.94, "y": 2.37}], "scale": [{"x": 2, "y": 2}]}, "bone44": {"rotate": [{"angle": -21.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -21.9}], "translate": [{"x": 32.28, "y": 9.82}]}, "bone45": {"rotate": [{"angle": -38.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -47.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -38.14}]}, "bone46": {"rotate": [{"angle": -93.96, "curve": 0.35, "c2": 0.39, "c3": 0.689, "c4": 0.75}, {"time": 0.1333, "angle": -61.4, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.2667, "angle": -93.96}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 16.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 16.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 16.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}}}, "run3": {"slots": {"chongci/A_wenli_wave_0": {"attachment": [{"name": "chongci/A_wenli_wave_00010"}, {"time": 0.0333, "name": "chongci/A_wenli_wave_00008"}, {"time": 0.0667, "name": "chongci/A_wenli_wave_00007"}, {"time": 0.1, "name": "chongci/A_wenli_wave_00005"}, {"time": 0.1333, "name": "chongci/A_wenli_wave_00004"}, {"time": 0.1667, "name": "chongci/A_wenli_wave_00003"}, {"time": 0.2, "name": "chongci/A_wenli_wave_00002"}, {"time": 0.2333, "name": "chongci/A_wenli_wave_00001"}, {"time": 0.2667, "name": "chongci/A_wenli_wave_00000"}]}, "a32": {"color": [{"color": "ffffff00"}]}, "a0": {"attachment": [{"name": null}]}, "a25": {"color": [{"color": "ffffffff"}]}, "a23": {"color": [{"color": "ffffff00"}]}, "chongci/qf_xishi_jn_slj_dm_00": {"attachment": [{"name": "chongci/qf_xishi_jn_slj_dm_04"}, {"time": 0.0333, "name": "chongci/qf_xishi_jn_slj_dm_08"}, {"time": 0.0667, "name": "chongci/qf_xishi_jn_slj_dm_10"}, {"time": 0.1, "name": "chongci/qf_xishi_jn_slj_dm_12"}, {"time": 0.1333, "name": "chongci/qf_xishi_jn_slj_dm_14"}, {"time": 0.1667, "name": "chongci/qf_xishi_jn_slj_dm_16"}, {"time": 0.2, "name": "chongci/qf_xishi_jn_slj_dm_18"}, {"time": 0.2333, "name": "chongci/qf_xishi_jn_slj_dm_20"}, {"time": 0.2667, "name": "chongci/qf_xishi_jn_slj_dm_04"}]}, "a22": {"color": [{"color": "ffffff00"}]}, "a21": {"color": [{"color": "ffffff00"}]}, "chongci/A_wenli_wave_1": {"attachment": [{"name": "chongci/A_wenli_wave_00010"}, {"time": 0.0333, "name": "chongci/A_wenli_wave_00008"}, {"time": 0.0667, "name": "chongci/A_wenli_wave_00007"}, {"time": 0.1, "name": "chongci/A_wenli_wave_00005"}, {"time": 0.1333, "name": "chongci/A_wenli_wave_00004"}, {"time": 0.1667, "name": "chongci/A_wenli_wave_00003"}, {"time": 0.2, "name": "chongci/A_wenli_wave_00002"}, {"time": 0.2333, "name": "chongci/A_wenli_wave_00001"}, {"time": 0.2667, "name": "chongci/A_wenli_wave_00000"}]}, "a20": {"color": [{"color": "ffffff00"}]}, "a15": {"color": [{"color": "ffffff00"}]}, "a24": {"color": [{"color": "ffffff00"}]}, "chongci/jss_01": {"attachment": [{"name": "chongci/jss_01"}, {"time": 0.0667, "name": "chongci/jss_03"}, {"time": 0.1333, "name": "chongci/jss_05"}, {"time": 0.2, "name": "chongci/jss_07"}, {"time": 0.2667, "name": "chongci/jss_09"}]}}, "bones": {"bone2": {"rotate": [{"angle": -13.78}], "translate": [{"y": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "y": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 3.93}]}, "bone4": {"rotate": [{"angle": -4.79}], "translate": [{"x": 0.01, "y": 0.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.06, "y": 5.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "x": 0.01, "y": 0.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.06, "y": 5.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 0.01, "y": 0.94}]}, "bone5": {"rotate": [{"angle": -12.57}], "translate": [{"x": 2.56, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 5.11, "y": -0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 2.56, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 5.11, "y": -0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 2.56, "y": -0.1}]}, "bone6": {"rotate": [{"angle": 19.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 20.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 19.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 20.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 19.83}]}, "bone17": {"translate": [{"x": 2.76, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 5.52, "y": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 2.76, "y": 0.03}]}, "bone52": {"translate": [{"x": 2.35, "y": -0.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": 15.91, "y": -25.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 2.35, "y": -0.13}]}, "bone76": {"translate": [{"x": 6.73, "y": -10.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 22.02, "y": -32.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": 11.08, "y": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": -2.21, "y": -3.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 6.73, "y": -10.29}]}, "bone80": {"translate": [{"x": 61.42, "curve": 0.25, "c3": 0.417}, {"time": 0.1333, "x": -163.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 61.42}]}, "bone81": {"rotate": [{"angle": 17.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": 1.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "angle": 0.54, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -91.96, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1333, "angle": -118.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -117.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 17.23}], "translate": [{"x": -18.09, "y": 10.95, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "x": 16.65, "y": -0.13, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "x": 0.03, "y": 2.48, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "x": -0.93, "y": 58.24, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1333, "x": 6.02, "y": 98.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": -27.07, "y": 78.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": -18.09, "y": 10.95}]}, "bone107": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -9.81, "y": 38.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": 21.97, "y": 14.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone108": {"rotate": [{"angle": 2.69}]}, "bone109": {"rotate": [{"angle": -4.6}]}, "bone110": {"translate": [{"x": -103.59, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 125.32, "curve": 0.25, "c3": 0.399}, {"time": 0.2667, "x": -103.59}]}, "bone111": {"rotate": [{"angle": -130.46, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -107.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 19.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 4.18, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2, "angle": -2.48, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2333, "angle": -61.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": -130.46}], "translate": [{"x": 8.09, "y": 96.67, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -25.41, "y": 70.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": -23.17, "y": 18.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": 0.03, "y": 2.49, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2, "x": 0.03, "y": 3, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2333, "x": -13.7, "y": 47.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "x": 8.09, "y": 96.67}]}, "bone61": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.67, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 3.4}]}, "bone64": {"rotate": [{"angle": 27.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 25.48, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 37.52, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 27.02}], "translate": [{"x": -3.31, "y": 12.85}]}, "bone65": {"rotate": [{"angle": 11.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 17.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2667, "angle": 12.72}]}, "bone66": {"rotate": [{"angle": 23.87, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 24.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 22.9}]}, "a5": {"rotate": [{"angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.56}]}, "bone16": {"rotate": [{"angle": 5.15, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 39.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 5.15}]}, "bone14": {"rotate": [{"angle": 6.92, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 6.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 34.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.06}]}, "bone7": {"rotate": [{"angle": -39.88, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0667, "angle": -35.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -32.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -39.88}]}, "bone11": {"rotate": [{"angle": -2.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -9.09, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.0667, "angle": -8.8, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1, "angle": -2.99, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": 16.09, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -2.99}], "translate": [{"x": 31.34, "y": 22.01}]}, "bone12": {"rotate": [{"angle": 7.24, "curve": 0.328, "c2": 0.32, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "angle": -16.61, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 23.44, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "angle": 7.24}]}, "bone8": {"rotate": [{"angle": -4.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -10, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0667, "angle": -8.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": -3.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 18.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -4.74}]}, "bone9": {"rotate": [{"angle": 13.28, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.0667, "angle": -8.56, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": -17.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 31.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": 13.28}]}, "bone10": {"rotate": [{"angle": 41.05, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.0667, "angle": 17.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -24.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 41.05}]}, "bone89": {"rotate": [{"angle": 5.29, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 11.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 7.11, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.1667, "angle": -6.01, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": -7.62, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.2667, "angle": 5.29}]}, "bone88": {"rotate": [{"angle": 4.87, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 5.37, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.1, "angle": -1.14, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": -2.77, "curve": 0.31, "c2": 0.26, "c3": 0.717, "c4": 0.84}, {"time": 0.2667, "angle": 4.87}]}, "bone90": {"rotate": [{"angle": 8.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 12.01, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "angle": 9.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1667, "angle": -5.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 3.42, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": 8.86}]}, "bone55": {"rotate": [{"angle": 24.06, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.1, "angle": 18.49, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 24.06}], "translate": [{"x": -17.93, "y": 7.56}]}, "bone56": {"rotate": [{"angle": 16.31, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.1, "angle": 28.38, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": 23.74, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 6.36, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2667, "angle": 16.31}]}, "bone67": {"rotate": [{"angle": 32.25, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 30.15, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 37.65, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "angle": 32.25}], "translate": [{"x": -3.31, "y": 12.85}]}, "bone68": {"rotate": [{"angle": 15.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 14.43, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -12.15, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": 15.22}]}, "bone57": {"rotate": [{"angle": -10.24, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.1, "angle": 13.78, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": 19.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.24}]}, "bone69": {"rotate": [{"angle": 5.37, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 12.75, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 20.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -20.49, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2667, "angle": 5.37}]}, "bone58": {"rotate": [{"angle": 12.61, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0667, "angle": 18.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 22.91, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": 12.61}]}, "bone59": {"rotate": [{"angle": -9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -15.32, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0667, "angle": -14.02, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": -7.38, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 17.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -9.27}]}, "bone60": {"rotate": [{"angle": 3.74, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.0667, "angle": -18.33, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": -27.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 22.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": 3.74}]}, "bone63": {"rotate": [{"angle": -5.62, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.0333, "angle": -15.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 26.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -5.62}]}, "bone62": {"rotate": [{"angle": -13.47, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.0333, "angle": -6.49, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 16.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.47}]}, "dianballall": {"translate": [{"x": 413.94, "y": 2.37}], "scale": [{"x": 2, "y": 2}]}, "bone44": {"rotate": [{"angle": -21.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -21.9}], "translate": [{"x": 32.28, "y": 9.82}]}, "bone45": {"rotate": [{"angle": -38.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -47.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -38.14}]}, "bone46": {"rotate": [{"angle": -93.96, "curve": 0.35, "c2": 0.39, "c3": 0.689, "c4": 0.75}, {"time": 0.1333, "angle": -61.4, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.2667, "angle": -93.96}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 16.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 16.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 16.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}}}, "show_time": {"slots": {"chongci/A_wenli_wave_0": {"attachment": [{"name": "chongci/A_wenli_wave_00010"}, {"time": 0.0333, "name": "chongci/A_wenli_wave_00008"}, {"time": 0.0667, "name": "chongci/A_wenli_wave_00007"}, {"time": 0.1, "name": "chongci/A_wenli_wave_00005"}, {"time": 0.1333, "name": "chongci/A_wenli_wave_00004"}, {"time": 0.1667, "name": "chongci/A_wenli_wave_00003"}, {"time": 0.2, "name": "chongci/A_wenli_wave_00002"}, {"time": 0.2333, "name": "chongci/A_wenli_wave_00001"}, {"time": 0.2667, "name": "chongci/A_wenli_wave_00000"}]}, "a32": {"color": [{"color": "ffffff00"}]}, "a0": {"attachment": [{"name": null}]}, "a25": {"color": [{"color": "ffffffff"}]}, "a23": {"color": [{"color": "ffffff00"}]}, "chongci/qf_xishi_jn_slj_dm_00": {"attachment": [{"name": "chongci/qf_xishi_jn_slj_dm_04"}, {"time": 0.0333, "name": "chongci/qf_xishi_jn_slj_dm_08"}, {"time": 0.0667, "name": "chongci/qf_xishi_jn_slj_dm_10"}, {"time": 0.1, "name": "chongci/qf_xishi_jn_slj_dm_12"}, {"time": 0.1333, "name": "chongci/qf_xishi_jn_slj_dm_14"}, {"time": 0.1667, "name": "chongci/qf_xishi_jn_slj_dm_16"}, {"time": 0.2, "name": "chongci/qf_xishi_jn_slj_dm_18"}, {"time": 0.2333, "name": "chongci/qf_xishi_jn_slj_dm_20"}, {"time": 0.2667, "name": "chongci/qf_xishi_jn_slj_dm_04"}]}, "a22": {"color": [{"color": "ffffff00"}]}, "a21": {"color": [{"color": "ffffff00"}]}, "chongci/A_wenli_wave_1": {"attachment": [{"name": "chongci/A_wenli_wave_00010"}, {"time": 0.0333, "name": "chongci/A_wenli_wave_00008"}, {"time": 0.0667, "name": "chongci/A_wenli_wave_00007"}, {"time": 0.1, "name": "chongci/A_wenli_wave_00005"}, {"time": 0.1333, "name": "chongci/A_wenli_wave_00004"}, {"time": 0.1667, "name": "chongci/A_wenli_wave_00003"}, {"time": 0.2, "name": "chongci/A_wenli_wave_00002"}, {"time": 0.2333, "name": "chongci/A_wenli_wave_00001"}, {"time": 0.2667, "name": "chongci/A_wenli_wave_00000"}]}, "a20": {"color": [{"color": "ffffff00"}]}, "a15": {"color": [{"color": "ffffff00"}]}, "a24": {"color": [{"color": "ffffff00"}]}, "chongci/jss_01": {"attachment": [{"name": "chongci/jss_01"}, {"time": 0.0667, "name": "chongci/jss_03"}, {"time": 0.1333, "name": "chongci/jss_05"}, {"time": 0.2, "name": "chongci/jss_07"}, {"time": 0.2667, "name": "chongci/jss_09"}]}}, "bones": {"bone2": {"rotate": [{"angle": -13.78}], "translate": [{"y": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "y": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 3.93}]}, "bone4": {"rotate": [{"angle": -4.79}], "translate": [{"x": 0.01, "y": 0.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.06, "y": 5.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "x": 0.01, "y": 0.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.06, "y": 5.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 0.01, "y": 0.94}]}, "bone5": {"rotate": [{"angle": -12.57}], "translate": [{"x": 2.56, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 5.11, "y": -0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 2.56, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 5.11, "y": -0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 2.56, "y": -0.1}]}, "bone6": {"rotate": [{"angle": 19.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 20.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 19.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 20.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 19.83}]}, "bone17": {"translate": [{"x": 2.76, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 5.52, "y": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 2.76, "y": 0.03}]}, "bone52": {"translate": [{"x": 2.35, "y": -0.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": 15.91, "y": -25.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 2.35, "y": -0.13}]}, "bone76": {"translate": [{"x": 6.73, "y": -10.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 22.02, "y": -32.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": 11.08, "y": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": -2.21, "y": -3.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 6.73, "y": -10.29}]}, "bone80": {"translate": [{"x": 61.42, "curve": 0.25, "c3": 0.417}, {"time": 0.1333, "x": -163.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 61.42}]}, "bone81": {"rotate": [{"angle": 17.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": 1.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "angle": 0.54, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -91.96, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1333, "angle": -118.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -117.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 17.23}], "translate": [{"x": -18.09, "y": 10.95, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "x": 16.65, "y": -0.13, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "x": 0.03, "y": 2.48, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "x": -0.93, "y": 58.24, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1333, "x": 6.02, "y": 98.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": -27.07, "y": 78.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": -18.09, "y": 10.95}]}, "bone107": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -9.81, "y": 38.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": 21.97, "y": 14.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone108": {"rotate": [{"angle": 2.69}]}, "bone109": {"rotate": [{"angle": -4.6}]}, "bone110": {"translate": [{"x": -103.59, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 125.32, "curve": 0.25, "c3": 0.399}, {"time": 0.2667, "x": -103.59}]}, "bone111": {"rotate": [{"angle": -130.46, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -107.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 19.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 4.18, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2, "angle": -2.48, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2333, "angle": -61.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": -130.46}], "translate": [{"x": 8.09, "y": 96.67, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -25.41, "y": 70.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": -23.17, "y": 18.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": 0.03, "y": 2.49, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2, "x": 0.03, "y": 3, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2333, "x": -13.7, "y": 47.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "x": 8.09, "y": 96.67}]}, "bone61": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.67, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 3.4}]}, "bone64": {"rotate": [{"angle": 27.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 25.48, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 37.52, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 27.02}], "translate": [{"x": -3.31, "y": 12.85}]}, "bone65": {"rotate": [{"angle": 11.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 17.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2667, "angle": 12.72}]}, "bone66": {"rotate": [{"angle": 23.87, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 24.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 22.9}]}, "a5": {"rotate": [{"angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.56}]}, "bone16": {"rotate": [{"angle": 5.15, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 39.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 5.15}]}, "bone14": {"rotate": [{"angle": 6.92, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 6.92}]}, "bone15": {"rotate": [{"angle": -1.06, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 6.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 34.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.06}]}, "bone7": {"rotate": [{"angle": -39.88, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0667, "angle": -35.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -32.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -39.88}]}, "bone11": {"rotate": [{"angle": -2.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -9.09, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.0667, "angle": -8.8, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1, "angle": -2.99, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": 16.09, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -2.99}], "translate": [{"x": 31.34, "y": 22.01}]}, "bone12": {"rotate": [{"angle": 7.24, "curve": 0.328, "c2": 0.32, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "angle": -16.61, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 23.44, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "angle": 7.24}]}, "bone8": {"rotate": [{"angle": -4.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -10, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0667, "angle": -8.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": -3.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 18.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -4.74}]}, "bone9": {"rotate": [{"angle": 13.28, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.0667, "angle": -8.56, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": -17.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 31.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": 13.28}]}, "bone10": {"rotate": [{"angle": 41.05, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.0667, "angle": 17.46, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -24.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 41.05}]}, "bone89": {"rotate": [{"angle": 5.29, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 11.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 7.11, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.1667, "angle": -6.01, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": -7.62, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.2667, "angle": 5.29}]}, "bone88": {"rotate": [{"angle": 4.87, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 5.37, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.1, "angle": -1.14, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": -2.77, "curve": 0.31, "c2": 0.26, "c3": 0.717, "c4": 0.84}, {"time": 0.2667, "angle": 4.87}]}, "bone90": {"rotate": [{"angle": 8.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 12.01, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "angle": 9.77, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1667, "angle": -5.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 3.42, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": 8.86}]}, "bone55": {"rotate": [{"angle": 24.06, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.1, "angle": 18.49, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 24.06}], "translate": [{"x": -17.93, "y": 7.56}]}, "bone56": {"rotate": [{"angle": 16.31, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.1, "angle": 28.38, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": 23.74, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 6.36, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2667, "angle": 16.31}]}, "bone67": {"rotate": [{"angle": 32.25, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 30.15, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 37.65, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "angle": 32.25}], "translate": [{"x": -3.31, "y": 12.85}]}, "bone68": {"rotate": [{"angle": 15.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 14.43, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -12.15, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": 15.22}]}, "bone57": {"rotate": [{"angle": -10.24, "curve": 0.277, "c2": 0.14, "c3": 0.677, "c4": 0.7}, {"time": 0.1, "angle": 13.78, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": 19.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.24}]}, "bone69": {"rotate": [{"angle": 5.37, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 12.75, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 20.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -20.49, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2667, "angle": 5.37}]}, "bone58": {"rotate": [{"angle": 12.61, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0667, "angle": 18.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 22.91, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": 12.61}]}, "bone59": {"rotate": [{"angle": -9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -15.32, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0667, "angle": -14.02, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": -7.38, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 17.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -9.27}]}, "bone60": {"rotate": [{"angle": 3.74, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.0667, "angle": -18.33, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": -27.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 22.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": 3.74}]}, "bone63": {"rotate": [{"angle": -5.62, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.0333, "angle": -15.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 26.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -5.62}]}, "bone62": {"rotate": [{"angle": -13.47, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.0333, "angle": -6.49, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 16.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.47}]}, "dianballall": {"translate": [{"x": 413.94, "y": 2.37}], "scale": [{"x": 2, "y": 2}]}, "bone44": {"rotate": [{"angle": -21.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -21.9}], "translate": [{"x": 32.28, "y": 9.82}]}, "bone45": {"rotate": [{"angle": -38.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -47.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -38.14}]}, "bone46": {"rotate": [{"angle": -93.96, "curve": 0.35, "c2": 0.39, "c3": 0.689, "c4": 0.75}, {"time": 0.1333, "angle": -61.4, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.2667, "angle": -93.96}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 16.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 16.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 16.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}}}}}