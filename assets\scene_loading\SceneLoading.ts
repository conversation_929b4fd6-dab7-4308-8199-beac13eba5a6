import { _decorator, Component, No<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, director, ProgressBar, Label, sp, Sprite } from "cc";
import { BundleEnum, ResHelper } from "../platform/src/ResHelper";
import { JsonMgr } from "../GameScrpit/game/mgr/JsonMgr";
import { PlayerModule } from "../GameScrpit/module/player/PlayerModule";
import { PlayerDataMessage } from "../GameScrpit/game/net/protocol/Player";
import { GameDirector, GameStatusEnum } from "../GameScrpit/game/GameDirector";
import { FmConfig } from "../GameScrpit/game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";

const log = Logger.getLoger(LOG_LEVEL.INFO);
const { ccclass, property } = _decorator;

interface BundleInfo {
  name: string;
  size: number;
}

@ccclass("SceneLoading")
export class SceneLoading extends Component {
  static SceneMoviePlayed = false;

  @property(ProgressBar)
  private progressBar: ProgressBar;

  @property(Sprite)
  spr_bar: Sprite;

  @property(Label)
  private lblProgress: Label;

  @property(Node)
  private nodeAni: Node;

  @property(sp.Skeleton)
  game_loading: sp.Skeleton = null;

  // 进度条当前目标
  progressNow: number = 0;

  // 进度阶段
  progressLevel = 0;

  // 分配比例
  progressMap = [
    { desc: "正在加载配置", progress: 0, power: 40 },
    { desc: "正在下载资源", progress: 0, power: 20 },
    { desc: "正在进入三界", progress: 0, power: 20 },
    { desc: "正在进入三界", progress: 0, power: 20 },
  ];

  // 进入游戏加载的最小bundle列表
  startBundleList: BundleInfo[] = [
    { name: BundleEnum.RESOURCES, size: 5 },
    { name: BundleEnum.BUNDLE_COMMON_PLAYERHEAD, size: 2 },
    { name: BundleEnum.BUNDLE_COMMON_UI, size: 3 },
    { name: BundleEnum.BUNDLE_COMMON_FONT, size: 1 },
    { name: BundleEnum.BUNDLE_COMMON_ITEM, size: 2 },
    { name: BundleEnum.BUNDLE_G_COMMON_MAIN, size: 2 },
    { name: BundleEnum.BUNDLE_G_MAINPAGE, size: 2 },
    { name: BundleEnum.BUNDLE_G_GAME_MAP, size: 2 },
    { name: BundleEnum.BUNDLE_CITY_100, size: 1 },
    { name: BundleEnum.BUNDLE_CITY_101, size: 1 },
    { name: BundleEnum.BUNDLE_CITY_102, size: 1 },
    { name: BundleEnum.BUNDLE_CITY_103, size: 1 },
  ];

  // 资源总大小
  sizeAll = 0;

  // 已下载大小
  sizeNow = 0;

  updateProgress(progress: number) {
    let progressCurr = this.progressMap[this.progressLevel];
    progressCurr.progress = progress * progressCurr.power;

    let progressNow = 0;

    for (let i = 0; i <= this.progressLevel; i++) {
      progressNow += this.progressMap[i].progress;
    }
    this.progressNow = progressNow / 100;
  }

  async gameLoadingConfig() {
    // 下载配置文件
    let bundleCfg = await ResHelper.loadBundleSync(BundleEnum.BUNDLE_COMMON_JSON);

    // this.checkRole();

    return await new Promise((resolve, reject) => {
      // 加载配置信息
      bundleCfg.preloadDir(
        "json",
        (finished: number, total: number) => {
          this.updateProgress(finished / total);
        },
        (err: Error | null) => {
          if (err) {
            log.error("配置获取错误");
            return reject(err);
          }
          bundleCfg.loadDir("json", (error: any, items: any) => {
            if (error) {
              log.error("配置获取错误");
              return reject(error);
            } else {
              for (let i = 0; i < items.length; i++) {
                const item = items[i];
                JsonMgr.instance.jsonList[item.name] = item.json;

                // 初始化给默认值
                JsonMgr.instance.initDefault(JsonMgr.instance.jsonList[item.name]);

                if (item.name == "c_copyMain") {
                  let index = 0;
                  for (let j in item.json) {
                    item.json[j]["index"] = index;
                    index++;
                  }
                }
              }
              return resolve(true);
            }
          });
        }
      );
    });
  }

  async getPlayerInfo(): Promise<PlayerDataMessage> {
    return new Promise((resolve, reject) => {
      PlayerModule.api.getPlayerInfo(async (data: PlayerDataMessage) => {
        resolve(data);
      });
    });
  }

  async gameLoading() {
    this.progressLevel = 0;

    let rs = await this.gameLoadingConfig();
    if (rs) {
      this.updateProgress(1);
      this.progressLevel = 1;

      let playerData = await this.getPlayerInfo();

      // 创角前播放动画
      if (playerData.level < 1) {
        if (!SceneLoading.SceneMoviePlayed && !FmConfig.jumpManHua && !FmConfig.forReview) {
          director.preloadScene(
            "SceneMovie",
            (completedCount: number, totalCount: number, item: any) => {
              this.updateProgress(completedCount / totalCount);
            },
            () => {
              SceneLoading.SceneMoviePlayed = true;
              director.loadScene("SceneMovie");
            }
          );
        } else {
          this.lblProgress.string = `加载创角场景中：${(0 * 100).toFixed(1)}%}`;

          // 加载创角场景
          ResHelper.loadBundle(BundleEnum.SCENE_CREATE_PLAYER, (bundle) => {
            this.updateProgress(1);
            this.progressLevel = 2;
            director.preloadScene(
              "SceneCreatePlayer",
              (completedCount: number, totalCount: number, item: any) => {
                this.updateProgress(completedCount / totalCount);
              },
              () => {
                this.updateProgress(1);
                director.loadScene("SceneCreatePlayer");
              }
            );
          });
        }
        return;
      }

      // 计算总大小
      this.sizeAll = 0;
      for (let idx = 0; idx < this.startBundleList.length; idx++) {
        this.sizeAll += this.startBundleList[idx].size;
      }

      // 开始下载
      this.sizeNow = 0;
      let nowTs = new Date().getTime();
      log.info(`==== 开始下载资源  时间`, nowTs);

      for (let idx = 0; idx < this.startBundleList.length; idx++) {
        ResHelper.loadBundle(this.startBundleList[idx].name, (bundle: AssetManager.Bundle) => {
          this.sizeNow += this.startBundleList[idx].size;
          this.updateProgress(this.sizeNow / this.sizeAll);
          log.info(`==== 下载/加载【${bundle.name}】完成`, new Date().getTime() - nowTs);
          nowTs = new Date().getTime();

          // 下载完成，开始加载游戏场景
          if (this.sizeNow >= this.sizeAll) {
            this.updateProgress(1);
            this.progressLevel = 2;

            // 加载游戏场景
            director.preloadScene(
              "GameScene",
              (completedCount: number, totalCount: number, item: any) => {
                this.updateProgress(completedCount / totalCount);
              },

              async () => {
                await GameDirector.instance.checkAndLoadModule();
                log.info("==== 加载代码模块  完成", new Date().getTime() - nowTs);
                nowTs = new Date().getTime();
                this.updateProgress(1);
                this.progressLevel = 3;

                await ResHelper.preLoadResSync(BundleEnum.BUNDLE_G_COMMON_MAIN, "prefab/ui/UIMain");
                this.updateProgress(0.5);
                log.info("==== 加载公用资源  完成", new Date().getTime() - nowTs);
                nowTs = new Date().getTime();

                await ResHelper.preLoadResSync(BundleEnum.BUNDLE_G_GAME_MAP, "prefab/ui/UIGameMap");
                this.updateProgress(1);
                log.info("==== 加载 三界 资源  完成", new Date().getTime() - nowTs);
                nowTs = new Date().getTime();

                director.loadScene("GameScene", () => {
                  PlayerModule.data.onPlayerAttrUpdate();
                  log.info("==== 加载 三界 场景  完成", new Date().getTime() - nowTs);
                  nowTs = new Date().getTime();

                  // 设置游戏进行状态
                  GameDirector.instance.gameStatus = GameStatusEnum.GAME_RUNNING;
                });
              }
            );
          }
        });
      }
    }
  }

  start() {
    // 加载配置
    GameDirector.clear();
    this.gameLoading();

    if (FmConfig.lodingIs == false) {
      this.game_loading.setCompleteListener((res: sp.spine.TrackEntry) => {
        if (res.animation.name === "logo_loading_01") {
          FmConfig.lodingIs = true;
          this.game_loading.setCompleteListener(null);
          this.game_loading.setAnimation(0, "logo_loading_02", true);
        }
      });
      this.game_loading.setAnimation(0, "logo_loading_01", true);
    } else {
      this.game_loading.setAnimation(0, "logo_loading_02", true);
    }
  }

  protected update(dt: number): void {
    let aniProgress = this.spr_bar.fillRange;
    if (aniProgress >= 1) {
      return;
    }

    let remain = this.progressNow - aniProgress;

    // 修改速度
    remain *= 0.1;

    // 最低速度
    let minSpeed = (1 - aniProgress) * 0.001;
    remain = Math.max(minSpeed, remain);
    aniProgress += remain;
    aniProgress = Math.min(1, aniProgress);
    this.spr_bar.fillRange = aniProgress;
    this.nodeAni.setPosition(-273 + 546 * aniProgress, 0);

    this.lblProgress.string = `${this.progressMap[this.progressLevel].desc}：${(aniProgress * 100).toFixed(1)}%`;
  }

  protected onDestroy(): void {
    if (this.game_loading.isValid == true) {
      this.game_loading.setCompleteListener(null);
    }
  }
}
