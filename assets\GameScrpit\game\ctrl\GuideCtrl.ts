import { _decorator } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { getConfigGuideList, GuideRouteEnum } from "../../ext_guide/GuideDefine";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { IConfigGuide } from "../JsonDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

const enum GuideStatusEnum {
  NEXT = 0,
  SYSTEM_OPEN = 1,
  PLAY_ANI = 2,
  PLAY_TALK = 3,
  WAIT_CLICK = 4,
}

@ccclass("GuideCtrl")
export class GuideCtrl extends BaseCtrl {
  // 引导步骤列表
  public configGuideList: IConfigGuide[] = [];

  // 状态
  public status: GuideStatusEnum = GuideStatusEnum.NEXT;

  public init(args: any) {
    // clone一份配置出来
    this.configGuideList = getConfigGuideList(args);
  }

  onLoad() {
    super.onLoad();

    // 注册路由
    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopFinger, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopFinger",
      isSingleton: true,
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopTalk, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopTalk",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopOpen, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopOpen",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopEventRoute, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopEventRoute",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopFusu, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopFusu",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopLoading, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopLoading",
      parentNode: TipsMgr.topRouteCtrl.nodeHighLayer,
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopLoading2, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopLoading2",
      parentNode: TipsMgr.topRouteCtrl.nodeHighLayer,
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopQiYunGame, {
      bundle: "scene_qiyun_xiaoyouxi",
      url: "prefab/TopQiYunGame",
      parentNode: TipsMgr.topRouteCtrl.nodeDefaultLayer,
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopConfirm, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopConfirm",
      parentNode: TipsMgr.topRouteCtrl.nodeDefaultLayer,
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopHeroHelp, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopHeroHelp",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(GuideRouteEnum.TopEventHeroCome, {
      bundle: BundleEnum.BUNDLE_EXT_GUIDE,
      url: "prefab/top/TopEventHeroCome",
      nextHop: [],
      exit: "",
    });
  }

  start() {
    super.start();
  }

  onDestroy() {
    super.onDestroy();
  }
}
