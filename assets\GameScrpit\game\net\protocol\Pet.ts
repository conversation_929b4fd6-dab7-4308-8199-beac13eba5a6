// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Pet.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface PetMessage {
  /** 宠物模版ID */
  petId: number;
  /** 宠物等级 */
  level: number;
  /** 觉醒次数 */
  awakeCount: number;
  /** 技能栏 */
  petSkillList: PetSkillMessage[];
  /** 生效的皮肤ID */
  chosenSkinId: number;
  /** 已经解锁的皮肤列表 */
  skinList: number[];
}

/**  */
export interface PetSkillMessage {
  /** 当前技能值 */
  skillAdd: number;
  /** 最近一次洗练出来的值 */
  backUpSkillAdd: number;
  /** 气运洗练的次数 */
  energyWashCount: number;
  /** 道具洗练的次数 */
  itemWashCount: number;
}

/** 用于洗练技能和替换技能 */
export interface PetSkillRequest {
  /** 宠物id */
  petId: number;
  /** 技能在技能表中的顺序 */
  rank: number;
  /** 是否通过消耗道具洗练技能 false表示通过气运洗练 */
  consumeItem: boolean;
}

/**  */
export interface PetSkinRequest {
  /** 宠物id */
  petId: number;
  /** 穿戴的皮肤 */
  skinId: number;
}

/**  */
export interface PetSkinUnlockResponse {
  /** 灵兽ID */
  petId: number;
  /** 新解锁的皮肤ID */
  unlockSkinId: number;
  /** 新增的灵兽技能栏，如果长度大于0，前端需要加入到灵兽技能列表中 */
  petSkillAddList: PetSkillMessage[];
  /** 已经解锁的皮肤列表 */
  skinList: number[];
}

function createBasePetMessage(): PetMessage {
  return { petId: 0, level: 0, awakeCount: 0, petSkillList: [], chosenSkinId: 0, skinList: [] };
}

export const PetMessage: MessageFns<PetMessage> = {
  encode(message: PetMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.petId !== 0) {
      writer.uint32(8).int64(message.petId);
    }
    if (message.level !== 0) {
      writer.uint32(16).int32(message.level);
    }
    if (message.awakeCount !== 0) {
      writer.uint32(24).int32(message.awakeCount);
    }
    for (const v of message.petSkillList) {
      PetSkillMessage.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.chosenSkinId !== 0) {
      writer.uint32(40).int64(message.chosenSkinId);
    }
    writer.uint32(50).fork();
    for (const v of message.skinList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PetMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePetMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.petId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.awakeCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.petSkillList.push(PetSkillMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.chosenSkinId = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag === 48) {
            message.skinList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 50) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.skinList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PetMessage>, I>>(base?: I): PetMessage {
    return PetMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PetMessage>, I>>(object: I): PetMessage {
    const message = createBasePetMessage();
    message.petId = object.petId ?? 0;
    message.level = object.level ?? 0;
    message.awakeCount = object.awakeCount ?? 0;
    message.petSkillList = object.petSkillList?.map((e) => PetSkillMessage.fromPartial(e)) || [];
    message.chosenSkinId = object.chosenSkinId ?? 0;
    message.skinList = object.skinList?.map((e) => e) || [];
    return message;
  },
};

function createBasePetSkillMessage(): PetSkillMessage {
  return { skillAdd: 0, backUpSkillAdd: 0, energyWashCount: 0, itemWashCount: 0 };
}

export const PetSkillMessage: MessageFns<PetSkillMessage> = {
  encode(message: PetSkillMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.skillAdd !== 0) {
      writer.uint32(9).double(message.skillAdd);
    }
    if (message.backUpSkillAdd !== 0) {
      writer.uint32(17).double(message.backUpSkillAdd);
    }
    if (message.energyWashCount !== 0) {
      writer.uint32(24).int32(message.energyWashCount);
    }
    if (message.itemWashCount !== 0) {
      writer.uint32(32).int32(message.itemWashCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PetSkillMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePetSkillMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.skillAdd = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.backUpSkillAdd = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.energyWashCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.itemWashCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PetSkillMessage>, I>>(base?: I): PetSkillMessage {
    return PetSkillMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PetSkillMessage>, I>>(object: I): PetSkillMessage {
    const message = createBasePetSkillMessage();
    message.skillAdd = object.skillAdd ?? 0;
    message.backUpSkillAdd = object.backUpSkillAdd ?? 0;
    message.energyWashCount = object.energyWashCount ?? 0;
    message.itemWashCount = object.itemWashCount ?? 0;
    return message;
  },
};

function createBasePetSkillRequest(): PetSkillRequest {
  return { petId: 0, rank: 0, consumeItem: false };
}

export const PetSkillRequest: MessageFns<PetSkillRequest> = {
  encode(message: PetSkillRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.petId !== 0) {
      writer.uint32(8).int64(message.petId);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    if (message.consumeItem !== false) {
      writer.uint32(24).bool(message.consumeItem);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PetSkillRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePetSkillRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.petId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.consumeItem = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PetSkillRequest>, I>>(base?: I): PetSkillRequest {
    return PetSkillRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PetSkillRequest>, I>>(object: I): PetSkillRequest {
    const message = createBasePetSkillRequest();
    message.petId = object.petId ?? 0;
    message.rank = object.rank ?? 0;
    message.consumeItem = object.consumeItem ?? false;
    return message;
  },
};

function createBasePetSkinRequest(): PetSkinRequest {
  return { petId: 0, skinId: 0 };
}

export const PetSkinRequest: MessageFns<PetSkinRequest> = {
  encode(message: PetSkinRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.petId !== 0) {
      writer.uint32(8).int64(message.petId);
    }
    if (message.skinId !== 0) {
      writer.uint32(16).int64(message.skinId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PetSkinRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePetSkinRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.petId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.skinId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PetSkinRequest>, I>>(base?: I): PetSkinRequest {
    return PetSkinRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PetSkinRequest>, I>>(object: I): PetSkinRequest {
    const message = createBasePetSkinRequest();
    message.petId = object.petId ?? 0;
    message.skinId = object.skinId ?? 0;
    return message;
  },
};

function createBasePetSkinUnlockResponse(): PetSkinUnlockResponse {
  return { petId: 0, unlockSkinId: 0, petSkillAddList: [], skinList: [] };
}

export const PetSkinUnlockResponse: MessageFns<PetSkinUnlockResponse> = {
  encode(message: PetSkinUnlockResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.petId !== 0) {
      writer.uint32(8).int64(message.petId);
    }
    if (message.unlockSkinId !== 0) {
      writer.uint32(16).int64(message.unlockSkinId);
    }
    for (const v of message.petSkillAddList) {
      PetSkillMessage.encode(v!, writer.uint32(26).fork()).join();
    }
    writer.uint32(34).fork();
    for (const v of message.skinList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PetSkinUnlockResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePetSkinUnlockResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.petId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.unlockSkinId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.petSkillAddList.push(PetSkillMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag === 32) {
            message.skinList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.skinList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PetSkinUnlockResponse>, I>>(base?: I): PetSkinUnlockResponse {
    return PetSkinUnlockResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PetSkinUnlockResponse>, I>>(object: I): PetSkinUnlockResponse {
    const message = createBasePetSkinUnlockResponse();
    message.petId = object.petId ?? 0;
    message.unlockSkinId = object.unlockSkinId ?? 0;
    message.petSkillAddList = object.petSkillAddList?.map((e) => PetSkillMessage.fromPartial(e)) || [];
    message.skinList = object.skinList?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
