import { UIMailBox } from "../../game/ui/ui_mail/UIMailBox";
import { UIMailDetail } from "../../game/ui/ui_mail/UIMailDetail";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum MailRouteItem {
  UIMailBox = "UIMailBox",
  UIMailDetail = "UIMailDetail",
}

export class MailRoute {


  rotueTables: Recording[] = [
    {
      node: UIMailBox,
      uiName: MailRouteItem.UIMailBox,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIMailDetail,
      uiName: MailRouteItem.UIMailDetail,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
