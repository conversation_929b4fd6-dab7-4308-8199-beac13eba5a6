import { _decorator, Component, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

/**
 *
 * i<PERSON>_huang
 * Tue Aug 27 2024 14:29:53 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubBoss.ts
 *
 */

@ccclass("UIClubBoss")
export class UIClubBoss extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubBoss`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
  }
}
