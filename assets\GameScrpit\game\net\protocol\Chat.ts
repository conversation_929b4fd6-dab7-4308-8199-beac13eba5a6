// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Chat.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { PlayerBaseMessage } from "./Player";

export const protobufPackage = "sim";

/**  */
export interface ChatPackAddRequest {
  /** CLUB(1,"妖盟"),LOCAL(2,"本地"),CROSS(3,"跨服") */
  marketType: number;
  /** 消息的类型  1-文本 */
  messageType: number;
  /** 消息体 */
  content: string;
  /** 列出提到的用户ID */
  mentionedUsers: number[];
}

/**  */
export interface ChatPackFindRequest {
  /** 开始的序列  start和end可以为<=0，表示序列以后和序列之前 （start,end） 左开右开 */
  start: number;
  /** 结束序列 */
  end: number;
  /** 频道 CLUB(1,"妖盟"),LOCAL(2,"本地"),CROSS(3,"跨服") */
  marketType: number;
}

/**  */
export interface ChatPackMessage {
  /** 频道  1-妖盟 2-本地 3-跨服 */
  marketType: number;
  /** 仙盟频道才有效 */
  groupId: number;
  /** 玩家信息 */
  sender:
    | PlayerBaseMessage
    | undefined;
  /** 记录已经看过的信息ID */
  sequence: number;
  /** 消息的类型  1-文本 */
  messageType: number;
  /** 消息体 */
  content: string;
  /** 列出提到的用户ID */
  mentionedUsers: number[];
  /** 创建时间 */
  createTime: number;
}

function createBaseChatPackAddRequest(): ChatPackAddRequest {
  return { marketType: 0, messageType: 0, content: "", mentionedUsers: [] };
}

export const ChatPackAddRequest: MessageFns<ChatPackAddRequest> = {
  encode(message: ChatPackAddRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.marketType !== 0) {
      writer.uint32(8).int32(message.marketType);
    }
    if (message.messageType !== 0) {
      writer.uint32(16).int32(message.messageType);
    }
    if (message.content !== "") {
      writer.uint32(26).string(message.content);
    }
    writer.uint32(34).fork();
    for (const v of message.mentionedUsers) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChatPackAddRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChatPackAddRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.marketType = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.messageType = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 4: {
          if (tag === 32) {
            message.mentionedUsers.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.mentionedUsers.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChatPackAddRequest>, I>>(base?: I): ChatPackAddRequest {
    return ChatPackAddRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChatPackAddRequest>, I>>(object: I): ChatPackAddRequest {
    const message = createBaseChatPackAddRequest();
    message.marketType = object.marketType ?? 0;
    message.messageType = object.messageType ?? 0;
    message.content = object.content ?? "";
    message.mentionedUsers = object.mentionedUsers?.map((e) => e) || [];
    return message;
  },
};

function createBaseChatPackFindRequest(): ChatPackFindRequest {
  return { start: 0, end: 0, marketType: 0 };
}

export const ChatPackFindRequest: MessageFns<ChatPackFindRequest> = {
  encode(message: ChatPackFindRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.start !== 0) {
      writer.uint32(8).int64(message.start);
    }
    if (message.end !== 0) {
      writer.uint32(16).int64(message.end);
    }
    if (message.marketType !== 0) {
      writer.uint32(24).int32(message.marketType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChatPackFindRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChatPackFindRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.start = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.end = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.marketType = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChatPackFindRequest>, I>>(base?: I): ChatPackFindRequest {
    return ChatPackFindRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChatPackFindRequest>, I>>(object: I): ChatPackFindRequest {
    const message = createBaseChatPackFindRequest();
    message.start = object.start ?? 0;
    message.end = object.end ?? 0;
    message.marketType = object.marketType ?? 0;
    return message;
  },
};

function createBaseChatPackMessage(): ChatPackMessage {
  return {
    marketType: 0,
    groupId: 0,
    sender: undefined,
    sequence: 0,
    messageType: 0,
    content: "",
    mentionedUsers: [],
    createTime: 0,
  };
}

export const ChatPackMessage: MessageFns<ChatPackMessage> = {
  encode(message: ChatPackMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.marketType !== 0) {
      writer.uint32(8).int32(message.marketType);
    }
    if (message.groupId !== 0) {
      writer.uint32(16).int64(message.groupId);
    }
    if (message.sender !== undefined) {
      PlayerBaseMessage.encode(message.sender, writer.uint32(26).fork()).join();
    }
    if (message.sequence !== 0) {
      writer.uint32(32).int64(message.sequence);
    }
    if (message.messageType !== 0) {
      writer.uint32(40).int32(message.messageType);
    }
    if (message.content !== "") {
      writer.uint32(50).string(message.content);
    }
    writer.uint32(58).fork();
    for (const v of message.mentionedUsers) {
      writer.int64(v);
    }
    writer.join();
    if (message.createTime !== 0) {
      writer.uint32(64).int64(message.createTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChatPackMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChatPackMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.marketType = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.groupId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.sender = PlayerBaseMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.sequence = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.messageType = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 7: {
          if (tag === 56) {
            message.mentionedUsers.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.mentionedUsers.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.createTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChatPackMessage>, I>>(base?: I): ChatPackMessage {
    return ChatPackMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChatPackMessage>, I>>(object: I): ChatPackMessage {
    const message = createBaseChatPackMessage();
    message.marketType = object.marketType ?? 0;
    message.groupId = object.groupId ?? 0;
    message.sender = (object.sender !== undefined && object.sender !== null)
      ? PlayerBaseMessage.fromPartial(object.sender)
      : undefined;
    message.sequence = object.sequence ?? 0;
    message.messageType = object.messageType ?? 0;
    message.content = object.content ?? "";
    message.mentionedUsers = object.mentionedUsers?.map((e) => e) || [];
    message.createTime = object.createTime ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
