import { _decorator, find, Label, ProgressBar, RichText, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import { FriendMessage } from "../../net/protocol/Friend";
import { FriendModule } from "../../../module/friend/FriendModule";
import { TravelModule } from "../../../module/travel/TravelModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UISiteDetail")
export class UISiteDetail extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_TRAVEL}?prefab/ui/UISiteDetail`;
  }
  protected _openAct: boolean = true; //打开动作

  private _placeId: number = null;

  public init(args: any): void {
    super.init(args);
    this._placeId = args.id;
  }

  protected onEvtShow(): void {
    let c_travlePlace = JsonMgr.instance.jsonList.c_travlePlace;
    let c_friend = JsonMgr.instance.jsonList.c_friend;
    let data = c_travlePlace[this._placeId];
    //  / this.getNode("titleLab").getComponent(Label).string = data.name;

    let str = data.name;
    let tx1 = str[0];
    let tx2 = "";
    for (let i = 1; i < str.length; i++) {
      tx2 += str[i];
    }
    let title_str = `<outline color=#ffa200 width=3><color=#ffffff><size=68>${tx1}</size></color></outline><outline color=#ffa200 width=3><color=#ffffff><size=60>${tx2}</size></color></outline>`;
    this.getNode("titleLab").getComponent(RichText).string = title_str;

    TravelModule.api.friendUnlockStatistics((res) => {
      for (let i = 0; i < data.friendList.length; i++) {
        let node = ToolExt.clone(this.getNode("friend"), this);
        node.setPosition(v3(0, 0, 0));
        this.getNode("content").addChild(node);
        node.active = true;

        let friendId = data.friendList[i];
        let info = c_friend[friendId];
        log.log(info);

        let dfriendInfo: FriendMessage = FriendModule.data.getFriendMessage(friendId);
        log.log(dfriendInfo);
        node["friendName"].getComponent(Label).string = info.name;
        node["require"].getComponent(Label).string = ToolExt.friendUnlockText(friendId);

        node["feelLab"].getComponent(Label).string = "因果值：";
        node["feelLab"].getComponent(Label).string += dfriendInfo ? dfriendInfo.karma : info.friendlyFirst;

        let headNode = find("btn_header/headMask/head", node);
        ToolExt.setFriendImages(headNode, info.id, this);

        let unlockNum = info.unlockNum == 0 ? 1 : info.unlockNum;

        if (info["isTarget"] == 1) {
          unlockNum = 1;
        }

        node["requireLab"].getComponent(Label).string =
          Math.floor(ToolExt.friendUnlockOkNum(res, friendId)) + "/" + unlockNum;

        let bar = ToolExt.friendUnlockOkNum(res, friendId) / unlockNum;
        node["requireProgress"].getComponent(ProgressBar).progress = bar;

        if (dfriendInfo) {
          node["getFriend"].active = true;
          bar = 1;
          // node["require"].active = false;
          // node["requireProgress"].active = false;
        } else {
          node["getFriend"].active = false;
          // node["require"].active = true;
          // node["requireProgress"].active = true;
        }
      }
    });
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
