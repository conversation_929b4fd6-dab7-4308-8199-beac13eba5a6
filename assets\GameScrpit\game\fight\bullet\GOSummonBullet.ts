import { _decorator, Details, v2 } from "cc";
import { CallBulletDetail } from "../FightDefine";
import RenderSection from "../../../lib/object/RenderSection";
import { GOBullet } from "./GOBullet";
import { AnimationSection } from "../section/AnimationSection";
import DirectSection, { DIRECT } from "../../../lib/object/DirectSection";
import ToolExt from "../../common/ToolExt";
import BulletRenderSection from "../../../lib/object/BulletRenderSection";

const { ccclass, property } = _decorator;

@ccclass("GOSummonBullet")
export class GOSummonBullet extends GOBullet {
  /**召唤单次伤害脚本 */
  public async onInitDetail(detail: CallBulletDetail) {
    super.onInitDetail(detail);
    this.setPosVec3(this.setBulletStartPos(detail));
    this.createAllSection(detail.bulletId);
  }

  private setBulletStartPos(detail: CallBulletDetail) {
    let newPos = ToolExt.transferOfAxes(detail.target.getSection(RenderSection).getRender(), this.parent);
    let pos = v2(newPos.x, newPos.y);
    return pos;
  }

  public async createAllSection(bulletId: number) {
    await new Promise(async (res) => {
      let param = {
        bulletId: bulletId,
        callBack: res,
      };
      this.createSection(BulletRenderSection, param);
    });
    this.createSection(AnimationSection);
    this.createSection(DirectSection, this.getDir());
    this.getSection(AnimationSection).playAction(101, false);
  }

  public onEnter(): void {
    super.onEnter();
  }

  protected async AnimationStart(detail) {}
  protected async PlayAnimation(event) {
    if (event["data"]["name"] == "atk") {
      this.hurtRoleMsg();
      this.atkRoleMsg();
    }
  }
  protected async AnimationCompleted(data) {
    if ("animation" == data.animation.name) {
      this.resolveBack(true);
      this.remove();
    }
  }
}
