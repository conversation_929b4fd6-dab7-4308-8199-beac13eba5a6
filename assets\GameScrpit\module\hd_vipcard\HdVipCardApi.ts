import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>andlerSuccess } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { VipCardMessage, VipCardResponse } from "../../game/net/protocol/Activity";
import { HdVipCardModule } from "./HdVipCardModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HdVipCardApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }

  public vipCard(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(VipCardMessage, ActivityCmd.VipCard, null, (data: VipCardMessage) => {
      log.log(data);
      HdVipCardModule.data.vipCardMessage = data;
      success && success(data);
    });
  }
  public takeMonthVipDailyReward(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(VipCardResponse, ActivityCmd.takeMonthVipDailyReward, null, (data: VipCardResponse) => {
      log.log(data);
      HdVipCardModule.data.vipCardMessage = data.vipCard;
      success && success(data);
    });
  }
  public takeLifeDailyReward(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(VipCardResponse, ActivityCmd.takeLifeDailyReward, null, (data: VipCardResponse) => {
      log.log(data);
      HdVipCardModule.data.vipCardMessage = data.vipCard;
      success && success(data);
    });
  }
}
