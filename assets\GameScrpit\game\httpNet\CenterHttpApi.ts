import { sys } from "cc";
import { GHttp } from "../../lib/http/GHttp";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
export default class CenterHttpApi {
  static register(username: string, pwd: string, origin: string) {
    log.log("CenterHttpApi register");
    return GHttp.POST({ url: "public/register", params: { username: username, password: pwd, origin: origin } });
  }

  static async login(username: string, pwd: string) {
    let rs: any = await GHttp.POST({ url: "public/login", params: { username: username, password: pwd } });
    if (rs.code === 200) {
      GHttp.tokenInfo.id = rs.data.id || rs.data.accountId;
      GHttp.tokenInfo.token = rs.data.token;
      GHttp.tokenInfo.refreshToken = rs.data.refreshToken;
      sys.localStorage.setItem("tokenInfo", JSON.stringify(GHttp.tokenInfo));
    }
    return rs;
  }

  static serverList() {
    return GHttp.GET({ url: "public/serverList" });
  }

  static getActivityInfo(activityId: number) {
    return GHttp.GET({ url: `activity/$${activityId}` });
  }

  static async announcement(serverId: number): Promise<any> {
    return await GHttp.GET({
      url: `server/noticeArray/${serverId}`,
    });
  }

  static async realNameAuth(realName: string, idCard: string) {
    // return { code: 200, age: 16, force_logout_time: TimeUtils.serverTime + 1000 * 60 * 60, status: 0 };
    return GHttp.POST({ url: "user/realNameAuth", params: { realName: realName, idCard: idCard } });
  }
}
