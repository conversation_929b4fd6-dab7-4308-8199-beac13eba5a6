import { _decorator, EventTouch, Label, Node } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";

import { PupilModule } from "../../PupilModule";
import { PupilWorkAdapter } from "../../adapter/PupilWorkAdapter";
import { ListView } from "../../../../../game/common/ListView";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import data from "../../../../../lib/data/data";
import { AdapterView } from "../../../../../../platform/src/core/ui/adapter_view/AdapterView";
const { ccclass, property } = _decorator;

enum SortType {
  ASC,
  DESC,
}
@ccclass("UIPupilWorkSlotPop")
export class UIPupilWorkSlotPop extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilWorkSlotPop`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  /**当前处理的任命槽位 */
  private slotIndex: number;

  private _adapter: PupilWorkAdapter;

  private _isOnlyShowPartner: boolean = false;
  private _sortType: SortType = SortType.DESC;

  public init(args: any) {
    super.init(args);
    this.slotIndex = args.slotIndex;
  }

  protected onRegEvent() {}
  protected onDelEvent() {}

  protected onEvtShow(): void {
    this._adapter = new PupilWorkAdapter(this.getNode("pupil_married_item"), this.getNode("pupil_unmarried_item"));
    this.getNode("node_list").getComponent(AdapterView).setAdapter(this._adapter);
    this._isOnlyShowPartner = this.getNode("icon_duigou").active;
    this.setDataList();
  }

  private setDataList() {
    let dataList: any[] = [];
    Object.keys(PupilModule.data.allPupilMap).forEach((key) => {
      let pupilId = parseInt(key);
      let pupilInfo = PupilModule.data.allPupilMap[pupilId];
      if (pupilInfo.ownInfo.adultAttrList.length > 0) {
        if (this._isOnlyShowPartner) {
          if (!pupilInfo.partnerInfo) {
            return;
          }
        }
        dataList.push({ pupilId, slotIndex: this.slotIndex });
      }
    });

    dataList = this.filterPupil(dataList);
    switch (this._sortType) {
      case SortType.ASC:
        dataList.sort(this.sortAsc.bind(this));
        break;
      case SortType.DESC:
        dataList.sort(this.sortDesc.bind(this));
        break;
    }
    this._adapter.setDatas(dataList);
  }

  // 过滤函数：当pupilId在PupilModule.data.pupilTrainMsg.workSlotList中存在并且所在的index不等于this.slotIndex时,过滤
  private filterPupil(dataList: any[]): any[] {
    return dataList.filter((data) => {
      const pupilId = data.pupilId;
      const workSlotIndex = PupilModule.data.pupilTrainMsg.workSlotList.indexOf(pupilId);
      return workSlotIndex === -1 || workSlotIndex === this.slotIndex;
    });
  }

  // 过滤函数：
  // 1.当pupilId在PupilModule.data.pupilTrainMsg.workSlotList中存在并且所在的index等于this.slotIndex时显示
  // 2.仅显示PupilModule.data.allPupilMap[value.pupilId].partnerInfo不为空的内容
  private filterPartnerPupil(dataList: any[]): any[] {
    return dataList.filter((data) => {
      const pupilId = data.pupilId;
      const workSlotIndex = PupilModule.data.pupilTrainMsg.workSlotList.indexOf(pupilId);
      const partnerInfo = PupilModule.data.allPupilMap[pupilId].partnerInfo;
      return (workSlotIndex === -1 || workSlotIndex === this.slotIndex) && !!partnerInfo;
    });
  }

  // 排序:
  // 1.当value.pupilId在PupilModule.data.pupilTrainMsg.workSlotList中存在时,优先级最高
  // 2.其次是 PupilModule.data.allPupilMap[value.pupilId].ownInfo.initAttrList.length加PupilModule.data.allPupilMap[value.pupilId].partnerInfo.initAttrList.length 值越小越优先级越高，注：partnerInfo可能为空
  // 3.按PupilModule.data.allPupilMap[value.pupilId].ownInfo.adultAttrList和PupilModule.data.allPupilMap[value.pupilId].partnerInfo.adultAttrList中的所有奇数位值的和，值越小优先级越高 注：adultAttrList中的值为number类型
  private sortAsc(a: any, b: any) {
    const aPupilIdInWorkSlotList = PupilModule.data.pupilTrainMsg.workSlotList.indexOf(a.pupilId) !== -1;
    const bPupilIdInWorkSlotList = PupilModule.data.pupilTrainMsg.workSlotList.indexOf(b.pupilId) !== -1;

    if (aPupilIdInWorkSlotList !== bPupilIdInWorkSlotList) {
      return aPupilIdInWorkSlotList ? -1 : 1;
    }

    const aInitAttrLength =
      PupilModule.data.allPupilMap[a.pupilId].ownInfo.initAttrList.length +
      (PupilModule.data.allPupilMap[a.pupilId].partnerInfo?.initAttrList.length || 0);
    const bInitAttrLength =
      PupilModule.data.allPupilMap[b.pupilId].ownInfo.initAttrList.length +
      (PupilModule.data.allPupilMap[b.pupilId].partnerInfo?.initAttrList.length || 0);

    if (aInitAttrLength !== bInitAttrLength) {
      return aInitAttrLength - bInitAttrLength;
    }

    const aOddAdultAttrSum =
      PupilModule.data.allPupilMap[a.pupilId].ownInfo.adultAttrList.reduce((pre, cur, index) => {
        if (index % 2 === 1) return pre + cur;
        return pre;
      }, 0) +
      (PupilModule.data.allPupilMap[a.pupilId].partnerInfo?.adultAttrList.reduce((pre, cur, index) => {
        if (index % 2 === 1) return pre + cur;
        return pre;
      }, 0) || 0);

    const bOddAdultAttrSum =
      PupilModule.data.allPupilMap[b.pupilId].ownInfo.adultAttrList.reduce((pre, cur, index) => {
        if (index % 2 === 1) return pre + cur;
        return pre;
      }, 0) +
      (PupilModule.data.allPupilMap[b.pupilId].partnerInfo?.adultAttrList.reduce((pre, cur, index) => {
        if (index % 2 === 1) return pre + cur;
        return pre;
      }, 0) || 0);

    return aOddAdultAttrSum - bOddAdultAttrSum;
  }

  /**
   * 降序排列函数
   * 1.当value.pupilId在PupilModule.data.pupilTrainMsg.workSlotList中存在时,优先级最高
   * 2.其次是 PupilModule.data.allPupilMap[value.pupilId].ownInfo.initAttrList.length加PupilModule.data.allPupilMap[value.pupilId].partnerInfo.initAttrList.length 值越大越优先级越高，注：partnerInfo可能为空
   * 3.按PupilModule.data.allPupilMap[value.pupilId].ownInfo.adultAttrList中的所有值相加，值越大优先级越高 注：adultAttrList中的值为number类型
   * @param a
   * @param b
   * @returns
   */
  private sortDesc(a: any, b: any) {
    const aPupilIdInWorkSlotList = PupilModule.data.pupilTrainMsg.workSlotList.indexOf(a.pupilId) !== -1;
    const bPupilIdInWorkSlotList = PupilModule.data.pupilTrainMsg.workSlotList.indexOf(b.pupilId) !== -1;

    if (aPupilIdInWorkSlotList !== bPupilIdInWorkSlotList) {
      return aPupilIdInWorkSlotList ? -1 : 1;
    }

    const aInitAttrLength =
      PupilModule.data.allPupilMap[a.pupilId].ownInfo.initAttrList.length +
      (PupilModule.data.allPupilMap[a.pupilId].partnerInfo?.initAttrList.length || 0);
    const bInitAttrLength =
      PupilModule.data.allPupilMap[b.pupilId].ownInfo.initAttrList.length +
      (PupilModule.data.allPupilMap[b.pupilId].partnerInfo?.initAttrList.length || 0);

    if (aInitAttrLength !== bInitAttrLength) {
      return bInitAttrLength - aInitAttrLength;
    }

    const aOddAdultAttrSum =
      PupilModule.data.allPupilMap[a.pupilId].ownInfo.adultAttrList.reduce((pre, cur, index) => {
        if (index % 2 === 1) return pre + cur;
        return pre;
      }, 0) +
      (PupilModule.data.allPupilMap[a.pupilId].partnerInfo?.adultAttrList.reduce((pre, cur, index) => {
        if (index % 2 === 1) return pre + cur;
        return pre;
      }, 0) || 0);

    const bOddAdultAttrSum =
      PupilModule.data.allPupilMap[b.pupilId].ownInfo.adultAttrList.reduce((pre, cur, index) => {
        if (index % 2 === 1) return pre + cur;
        return pre;
      }, 0) +
      (PupilModule.data.allPupilMap[b.pupilId].partnerInfo?.adultAttrList.reduce((pre, cur, index) => {
        if (index % 2 === 1) return pre + cur;
        return pre;
      }, 0) || 0);

    return aOddAdultAttrSum - bOddAdultAttrSum;
  }

  private initItemNode(pupilId: number) {}

  private roateIcon() {
    let scaleY = this.getNode("btn_shuxing_selsct").active ? -1 : 1;
    this.getNode("icon_jiantou_shang").setScale(1, scaleY, 1);
  }
  private on_click_btn_duigou(event: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let check: Node = event.target;
    check.getChildByName("icon_duigou").active = !check.getChildByName("icon_duigou").active;
    this._isOnlyShowPartner = check.getChildByName("icon_duigou").active;
    this.setDataList();
  }

  private on_click_btn_shuxing(event: EventTouch) {
    //
    AudioMgr.instance.playEffect(524);
    this.getNode("btn_shuxing_selsct").active = !this.getNode("btn_shuxing_selsct").active;
    this.roateIcon();
  }

  private on_click_btn_select_desc(event: EventTouch) {
    AudioMgr.instance.playEffect(524);
    this.getNode("lbl_select").getComponent(Label).string = "属性降序";
    this._sortType = SortType.DESC;
    this.setDataList();
    this.getNode("btn_shuxing_selsct").active = false;
    this.roateIcon();
  }

  private on_click_btn_select_asc(event: EventTouch) {
    AudioMgr.instance.playEffect(524);
    this.getNode("lbl_select").getComponent(Label).string = "属性升序";
    this._sortType = SortType.ASC;
    this.setDataList();
    this.getNode("btn_shuxing_selsct").active = false;
    this.roateIcon();
  }
  private on_click_btn_shuxing_selsct(event: EventTouch) {
    this.getNode("btn_shuxing_selsct").active = false;
    this.roateIcon();
  }
}
