import { _decorator, Component, Label, Node } from "cc";
import { ClubAudioName, ShopConfig } from "../../../../module/club/ClubConfig";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { GoodsModule } from "../../../../module/goods/GoodsModule";
import MsgMgr from "../../../../lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { ClubModule } from "../../../../module/club/ClubModule";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { PublicRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { ActivityRedeemItem } from "db://assets/GameScrpit/module/fracture/FractureConstant";
import { FractureModule } from "db://assets/GameScrpit/module/fracture/FractureModule";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("FractureShopViewholder")
export class FractureShopViewholder extends Component {
  @property(Label)
  private item_title: Label;

  @property(Label)
  private price: Label;
  @property(Label)
  private limit: Label;
  @property(Node)
  private item: Node;

  @property(Node)
  private buy: Node;
  @property(Node)
  private sell_out: Node;

  shopConfig: ActivityRedeemItem;
  start() {}

  update(deltaTime: number) {}
  public updateData(data: ActivityRedeemItem) {
    this.shopConfig = data;
    let item = data.rewardList;
    let itemConfig = JsonMgr.instance.getConfigItem(item[0]);
    this.item_title.string = itemConfig.name;
    // ToolExt.setItemBg(this.item_bkg, itemConfig.color);
    // ToolExt.setItemIcon(this.item_img, item[0][0]);
    // this.item_num.string = `${item[0][1]}`;

    FmUtils.setItemNode(this.item, item[0], item[1]);
    this.price.string = `${data.cost[1]}`;

    this.refreshLimit();
  }
  private refreshLimit() {
    // 0.不限购
    // 1.每日限购
    // 2.每周限购
    // 3.每月限购
    // 4.永久限购
    // 5.活动限购

    let limit_num = this.shopConfig.max - (FractureModule.data.fractureData.redeemMap[this.shopConfig.id] || 0);
    if (limit_num <= 0) {
      this.buy.active = false;
      this.sell_out.active = true;
      this.limit.node.active = false;
      return;
    }
    this.sell_out.active = false;
    this.buy.active = true;
    this.limit.node.active = true;

    switch (this.shopConfig.maxtype) {
      case 0:
        this.limit.string = "";
        break;
      case 1:
        this.limit.string = `每日限购:(${limit_num}/${this.shopConfig.max})`;
        break;
      case 2:
        this.limit.string = `每周限购:${limit_num}`;
        break;
      case 3:
        this.limit.string = `每月限购:${limit_num}`;
        break;
      case 4:
        this.limit.string = `永久限购:${limit_num}`;
        break;
      case 5:
        this.limit.string = `活动限购:${limit_num}`;
        break;
    }
  }

  private onClickBuy() {
    AudioMgr.instance.playEffect(1773);

    let numLimit = this.shopConfig.max - (FractureModule.data.fractureData.redeemMap[this.shopConfig.id] || 0);
    const buyConfirm: any = {
      itemInfo: this.shopConfig.rewardList,
      moneyInfo: [this.shopConfig.cost[0], this.shopConfig.cost[1]],
      maxNum: numLimit,
    };
    UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
      if (resp.ok) {
        FractureModule.api.buyRedeem(this.shopConfig.id, resp.num, (data: any) => {
          log.log("buyRedeem", data);
          this.updateData(this.shopConfig);
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
        });
      }
    });
  }
}
