import { _decorator, Label, sp } from "cc";
import { RouteMgr } from "../../platform/src/RouteHelper";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { RouteSceneLogin } from "./RouteSceneLogin";
import { BundleEnum, ResHelper } from "../../platform/src/ResHelper";
import { UIMgr } from "../../GameScrpit/lib/ui/UIMgr";
import MsgMgr from "../../GameScrpit/lib/event/MsgMgr";
import { AudioMgr, AudioName } from "../../platform/src/AudioHelper";
import { PlatformHttp } from "../../platform/src/lib/http";
import { assetManager } from "cc";
import { director } from "cc";
import StorageMgr, { StorageKeyEnum } from "../../platform/src/StorageHelper";
import { sys } from "cc";
import { GameData } from "../../GameScrpit/game/GameData";
import { ApiHandler } from "../../GameScrpit/game/mgr/ApiHandler";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { ggHotUpdateManager } from "db://gg-hot-update/scripts/hotupdate/GGHotUpdateManager";
import { DEBUG } from "cc/env";
import { ScreenUtil } from "../../GameScrpit/lib/utils/ScreenUtil";
import { JsonMgr } from "../../GameScrpit/game/mgr/JsonMgr";
import { FmConfig } from "../../GameScrpit/game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { RouteManager } from "../../platform/src/core/managers/RouteManager";
import GuideMgr from "../../GameScrpit/ext_guide/GuideMgr";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("SceneLogin")
export class SceneLogin extends BaseCtrl {
  public static instance: SceneLogin;
  private routeMgr: RouteMgr;

  @property(Label)
  lblVersion: Label = null;

  @property(sp.Skeleton)
  game_loading: sp.Skeleton = null;

  public static get routeMgr() {
    return this.instance.routeMgr;
  }
  async start() {
    super.start();
    ScreenUtil.adaptScreen();

    // 先加载消息配置
    await JsonMgr.instance.loadConfigOne("c_errorcode");
    await JsonMgr.instance.loadConfigOne("c_message");
    await JsonMgr.instance.loadConfigOne("c_music");

    if ([sys.Platform.ANDROID].includes(sys.platform)) {
      ggHotUpdateManager.init({
        enableLog: DEBUG,
        packageUrl: "http://192.168.3.39:10086/", // 显示本地版本号，实际没用
      });
      log.log("热更新配置", ggHotUpdateManager.hotUpdateConfig);
      this.lblVersion.string = "版本号：" + ggHotUpdateManager.hotUpdateConfig.bundles["build-in"].version;
    }

    SceneLogin.instance = this;
    this.routeMgr = RouteMgr.create(this.node.getChildByName("node_root_route"));

    if (FmConfig.lodingIs == false) {
      this.game_loading.setCompleteListener((res: sp.spine.TrackEntry) => {
        if (res.animation.name === "logo_loading_01") {
          FmConfig.lodingIs = true;
          this.game_loading.setCompleteListener(null);
          this.game_loading.setAnimation(0, "logo_loading_02", true);
        }
      });
      this.game_loading.setAnimation(0, "logo_loading_01", true);
    } else {
      this.game_loading.setAnimation(0, "logo_loading_02", true);
    }

    // 重置路由
    UIMgr.clear();

    // 清空消息
    MsgMgr.removeAll();

    GameData.clear();

    GuideMgr.endGuide;

    // 清空Api消息订阅
    ApiHandler.instance.removeAllSubscribe();

    // 清空路由节点
    TipsMgr.topRouteCtrl.removeAllRouteNode();

    RouteManager.uiRouteCtrl?.removeAllRouteNode();

    AudioMgr.instance.playMusic(501);

    SceneLogin.routeMgr.showPage(RouteSceneLogin.PageStart);
    ResHelper.loadBundle(BundleEnum.SCENE_LOADING);

    // 版本检测，如果不同就直接全删除，重新打开游戏
    PlatformHttp.GET({ url: "lastVersion.json" }).then((v: any) => {
      let sLocal = StorageMgr.loadStr(StorageKeyEnum.Version) || "{}";
      let jsonLocalVersion = JSON.parse(sLocal);

      // 微信端版本控制
      if (sys.platform == sys.Platform.WECHAT_GAME) {
        log.log("版本信息", v.version, jsonLocalVersion.version);
        // 大版本号不同，强制更新
        if (v.version != jsonLocalVersion.version) {
          log.log("大版本不一致", v.version, jsonLocalVersion.version);
          // 强制删除更新
          assetManager.cacheManager.clearCache();
          // 重启

          StorageMgr.saveItem(StorageKeyEnum.Version, JSON.stringify(v));

          wx.restartMiniProgram({ path: "" });
          return;
        }

        if (v.debugVersion && v.debugVersion != jsonLocalVersion.debugVersion) {
          log.log("开发版本不一致", v.debugVersion, jsonLocalVersion.debugVersion);
          // 强制删除更新
          assetManager.cacheManager.clearCache();
          // 重启
          director.reset();

          StorageMgr.saveItem(StorageKeyEnum.Version, JSON.stringify(v));

          wx.restartMiniProgram({ path: "" });
          return;
        }
      }
    });
  }

  protected onDestroy(): void {
    if (this.game_loading.isValid == true) {
      this.game_loading.setCompleteListener(null);
    }
  }
}
