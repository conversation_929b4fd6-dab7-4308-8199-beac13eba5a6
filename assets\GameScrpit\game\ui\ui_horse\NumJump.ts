import { tween, Animation } from "cc";
import { v3 } from "cc";
import { Label } from "cc";
import { _decorator, Component, Node } from "cc";
const { ccclass, property } = _decorator;

@ccclass("NumJump")
export class NumJump extends Component {
  @property(Label)
  private lblNum: Label;

  @property(Animation)
  private anim: Animation;

  start() {}

  // 显示内容和动画等级
  public play(str: string, level: number = 0) {
    this.lblNum.string = str;

    level = Math.min(level, 4);
    level = Math.max(level, 0);

    this.anim.defaultClip = this.anim.clips[level];
    this.anim.play();
    // 3秒后销毁
    tween(this.node).delay(3).destroySelf().start();
  }
}
