syntax = "proto3";
package sim;

// 
message ApprenticeGetRequestMessage {
  bool random = 1;
  int32 rank = 2;
}

// 
message ApprenticeMessage {
  int64 id = 1;
  // 用户
  int64 userId = 2;
  // 弟子昵称
  string name = 3;
  // 委任师傅
  int64 friendId = 4;
  // 天资/机敏
  int32 intelligenceId = 5;
}

// 
message ApprenticeSlotDesMessage {
  // 槽位的第几个位置 从0开始
  int32 rank = 1;
  // 槽位的徒弟id
  repeated int64 apprenticeIdList = 2;
}

// 
message ApprenticeTrainChangeMessage {
  // 
  ApprenticeTrainMessage apprenticeTrainMessage = 1;
  // 
  ApprenticeMessage apprenticeMessage = 2;
}

// 
message ApprenticeTrainMessage {
  int64 id = 1;
  // 用户
  int64 userId = 2;
  // key就是槽位索引，从0开始
  map<int32,ApprenticeSlotDesMessage> slotDesMap = 3;
}

