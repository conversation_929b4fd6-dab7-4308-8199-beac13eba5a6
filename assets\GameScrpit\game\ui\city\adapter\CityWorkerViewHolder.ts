import Formate from "../../../../lib/utils/Formate";
import { CityModule } from "../../../../module/city/CityModule";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { _decorator, Label, Node } from "cc";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { ProgressBar } from "cc";

const { ccclass, property } = _decorator;

@ccclass("CityWorkerViewHolder")
export class CityWorkerViewHolder extends ViewHolder {
  @property(ProgressBar)
  private progressBar: ProgressBar;

  updateData(data: any) {
    let configWorkerReward = CityModule.data.getConfigBuildWorkerRewardData(data);
    let lastWorkerNum: number = 0;
    if (data > 1) {
      let configWorkerRewardPre = CityModule.data.getConfigBuildWorkerRewardData(data - 1);
      lastWorkerNum = configWorkerRewardPre.workerNum;
    }

    // 当前进度
    let progress: number =
      (CityModule.data.cityTotalNum - lastWorkerNum) / (configWorkerReward.workerNum - lastWorkerNum);
    progress = Math.min(1, progress);
    progress = Math.max(0, progress);

    // 进度条进度
    this.progressBar.progress = progress;

    // 当前显示的结点
    let nodeActive: Node;
    this.node.getChildByName("bg_wz_on").active = progress < 1;
    this.node.getChildByName("bg_wz_finish").active = progress >= 1;
    if (progress == 1) {
      nodeActive = this.node.getChildByName("bg_wz_finish");
    } else {
      nodeActive = this.node.getChildByName("bg_wz_on");
    }

    // 第一个节点不显示进度条
    if (progress >= 1) {
      nodeActive.getChildByPath("bar/bg_progress").active = data != 1;
    } else {
      nodeActive.getChildByPath("bar/progress_bar").active = data != 1;
    }

    // 任务序号
    nodeActive.getChildByPath("bar/lbl_level").getComponent(Label).string = `${data}`;

    // 任务描述
    nodeActive.getChildByPath("bg_9g_biaoqian_wzry1/lbl_task").getComponent(Label).string =
      configWorkerReward.des.replace(
        "s%",
        `(${CityModule.data.cityTotalNum}/${Formate.format(configWorkerReward.workerNum)})`
      );

    // 任务属性
    let nodeLayoutAttr = nodeActive.getChildByName("layout_attr");
    nodeLayoutAttr.children.forEach((child) => {
      child.active = false;
    });
    for (let i = 0; i < configWorkerReward.attrAdd.length; i++) {
      let nodeAttr = nodeLayoutAttr.children[i];

      let configAttr = JsonMgr.instance.getConfigAttribute(configWorkerReward.attrAdd[i][0]);
      nodeAttr.getComponent(Label).string = `${i + 1}.${configAttr.name}+${configWorkerReward.attrAdd[i][1] / 100}%`;
      nodeAttr.active = true;
    }
  }
}
