<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>WJ_bg_shuixingdi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{160,278}</string>
                <key>spriteSourceSize</key>
                <string>{160,278}</string>
                <key>textureRect</key>
                <string>{{1,1},{160,278}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jinengkuang.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,72}</string>
                <key>spriteSourceSize</key>
                <string>{71,72}</string>
                <key>textureRect</key>
                <string>{{1,281},{71,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_jiantou.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{35,31}</string>
                <key>spriteSourceSize</key>
                <string>{35,31}</string>
                <key>textureRect</key>
                <string>{{74,346},{35,31}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_jineng.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{63,63}</string>
                <key>spriteSourceSize</key>
                <string>{63,63}</string>
                <key>textureRect</key>
                <string>{{74,281},{63,63}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UIHeroImprint.png</string>
            <key>size</key>
            <string>{162,378}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:b08f4c5b96274aae59cf42daa3293bef:a8ec94d0d4eb3b9b295a2896a531b750:b7f659e857daaa64e5ee9bd98dc59913$</string>
            <key>textureFileName</key>
            <string>UIHeroImprint.png</string>
        </dict>
    </dict>
</plist>
