import { U<PERSON><PERSON><PERSON><PERSON> } from "../../game/ui/ui_shouchong/UIShouChong";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
export enum HdShouChongRouteItem {
  UIShouChong = "UIShouChong",
}
export class HdShouChongRoute {


  rotueTables: Recording[] = [
    {
      node: UIShouChong,
      uiName: HdShouChongRouteItem.UIShouChong,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
