{"skeleton": {"hash": "vaQwULuDEfVDfk7nfX5d0APJJDo", "spine": "3.8.99", "x": -148.32, "y": 0.47, "width": 253.53, "height": 200.75, "images": "./images/", "audio": "D:/spine导出/灵兽动画/文鳐"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "bone", "parent": "root", "length": 252.17, "rotation": 0.47, "x": -2.11, "y": -25.84, "scaleX": 0.4933, "scaleY": 0.4933}, {"name": "bone71", "parent": "bone", "length": 90, "rotation": -1.3, "x": 37.25, "y": 228.93, "color": "df0a0aff"}, {"name": "bone2", "parent": "bone71", "rotation": 1.3, "x": -77.84, "y": -8.48, "color": "abe323ff"}, {"name": "bone3", "parent": "bone71", "length": 50.91, "rotation": 33.61, "x": -19.24, "y": 67.26}, {"name": "bone4", "parent": "bone3", "length": 45.55, "rotation": 22.51, "x": 50.91}, {"name": "bone5", "parent": "bone4", "length": 34.17, "rotation": 25.69, "x": 45.55}, {"name": "bone6", "parent": "bone5", "length": 48.71, "rotation": 24.21, "x": 31.81, "y": 2.73}, {"name": "bone7", "parent": "bone6", "length": 46.15, "rotation": 11.08, "x": 48.71}, {"name": "bone8", "parent": "bone5", "length": 39.74, "rotation": -34.95, "x": 8.4, "y": -15.99}, {"name": "bone9", "parent": "bone8", "length": 47.06, "rotation": 22.97, "x": 39.74}, {"name": "bone10", "parent": "bone5", "length": 31.81, "rotation": 38.87, "x": 9.17, "y": 18.28}, {"name": "bone11", "parent": "bone10", "length": 39.66, "rotation": -7.92, "x": 31.81}, {"name": "bone12", "parent": "bone4", "length": 22.47, "rotation": 41.48, "x": 19.04, "y": 23.18}, {"name": "bone13", "parent": "bone12", "length": 23.19, "rotation": -20.03, "x": 22.47}, {"name": "bone14", "parent": "bone13", "length": 20.98, "rotation": 12.52, "x": 23.19}, {"name": "bone15", "parent": "bone4", "length": 18.86, "rotation": 33.08, "x": 30.93, "y": 16.78}, {"name": "bone16", "parent": "bone15", "length": 18.69, "rotation": -20.94, "x": 18.86}, {"name": "bone17", "parent": "bone16", "length": 21.04, "rotation": 26.9, "x": 18.69}, {"name": "bone18", "parent": "bone4", "length": 15.07, "rotation": 20.41, "x": 40.68, "y": 12.61}, {"name": "bone19", "parent": "bone18", "length": 15.83, "rotation": -8.41, "x": 15.07}, {"name": "bone20", "parent": "bone19", "length": 15.42, "rotation": 25.65, "x": 15.83}, {"name": "bone21", "parent": "bone71", "length": 36.94, "rotation": 12.88, "x": 48.82, "y": 48.36}, {"name": "bone22", "parent": "bone21", "length": 47.16, "rotation": 30.53, "x": 36.94}, {"name": "bone23", "parent": "bone22", "length": 48.46, "rotation": 25.3, "x": 47.16}, {"name": "bone24", "parent": "bone21", "x": 26.21, "y": -36.8}, {"name": "bone25", "parent": "bone22", "x": 12.38, "y": -34.73}, {"name": "bone26", "parent": "bone23", "x": 11.25, "y": -21.17}, {"name": "bone27", "parent": "bone71", "length": 34.15, "rotation": 125.98, "x": -117.48, "y": 68.12}, {"name": "bone28", "parent": "bone27", "length": 36.15, "rotation": -20.66, "x": 34.15}, {"name": "bone29", "parent": "bone28", "length": 37.89, "rotation": -19.25, "x": 36.15}, {"name": "bone30", "parent": "bone27", "x": 21.19, "y": 30.78}, {"name": "bone31", "parent": "bone28", "x": 16.16, "y": 29.54}, {"name": "bone32", "parent": "bone29", "x": 14.01, "y": 16.56}, {"name": "bone33", "parent": "bone2", "length": 40.68, "rotation": 82.17, "x": 4.48, "y": 95.35}, {"name": "bone34", "parent": "bone33", "length": 50.86, "rotation": -21.18, "x": 40.68}, {"name": "bone35", "parent": "bone34", "length": 42.03, "rotation": -16.89, "x": 50.86}, {"name": "bone36", "parent": "bone2", "length": 74.92, "rotation": -3.65, "x": -42.82, "y": -16.97}, {"name": "bone37", "parent": "bone36", "length": 44.65, "rotation": -25.77, "x": 74.89, "y": 0.57}, {"name": "bone38", "parent": "bone37", "length": 60.58, "rotation": -13.83, "x": 44.48, "y": -0.56}, {"name": "bone39", "parent": "bone2", "length": 48.32, "rotation": 175.58, "x": -51.56, "y": -18.98}, {"name": "bone40", "parent": "bone39", "length": 45.63, "rotation": -37.73, "x": 48.32}, {"name": "bone41", "parent": "bone40", "length": 55.47, "rotation": -29.07, "x": 45.63}, {"name": "bone42", "parent": "bone41", "length": 57.43, "rotation": -37.82, "x": 55.47}, {"name": "bone43", "parent": "bone42", "length": 48.08, "rotation": -49.08, "x": 57.43}, {"name": "bone44", "parent": "bone43", "length": 57.37, "rotation": -29.43, "x": 48.08}, {"name": "bone45", "parent": "bone44", "length": 58.11, "rotation": -25.37, "x": 57.32, "y": 0.41}, {"name": "bone46", "parent": "bone45", "length": 73.01, "rotation": -13.77, "x": 58.69, "y": -0.59}, {"name": "bone47", "parent": "bone46", "length": 64.93, "rotation": 0.87, "x": 73.01}, {"name": "bone48", "parent": "bone47", "length": 67.24, "rotation": -0.64, "x": 64.93}, {"name": "bone49", "parent": "bone48", "length": 61.18, "rotation": 26.26, "x": 67.24}, {"name": "bone50", "parent": "bone49", "length": 51.38, "rotation": 37.58, "x": 61.18}, {"name": "bone51", "parent": "bone", "x": -81.04, "y": 65.64}, {"name": "bone52", "parent": "bone", "length": 75.25, "rotation": 48.02, "x": 95.59, "y": 143.1}, {"name": "bone53", "parent": "bone52", "length": 70.4, "rotation": -27.75, "x": 75.25}, {"name": "bone54", "parent": "bone53", "length": 60.26, "rotation": -8.32, "x": 69.87, "y": -1.4}, {"name": "bone55", "parent": "bone54", "rotation": -11.95, "x": -12.57, "y": 42.6}, {"name": "bone56", "parent": "bone52", "rotation": -48.02, "x": 73.77, "y": 39.31}, {"name": "bone57", "parent": "bone", "length": 41.48, "rotation": -1.4, "x": 90.9, "y": 48.82}, {"name": "bone58", "parent": "bone57", "x": 22.46, "y": 18.09}, {"name": "bone59", "parent": "bone57", "x": 40.14, "y": 62.54}, {"name": "bone60", "parent": "bone", "length": 41.5, "rotation": 178.35, "x": -163.01, "y": 164.25}, {"name": "bone61", "parent": "bone60", "x": 6.12, "y": -26.83}, {"name": "bone62", "parent": "bone60", "x": -11.72, "y": -74.69}, {"name": "bone63", "parent": "bone60", "x": 35.94, "y": -62.15}, {"name": "bone64", "parent": "bone", "length": 28.43, "rotation": -3.11, "x": -81.57, "y": 336.26}, {"name": "bone65", "parent": "bone64", "x": 11.55, "y": 11.64}, {"name": "bone66", "parent": "bone64", "x": 25.41, "y": 51.49}, {"name": "bone67", "parent": "bone37", "length": 48.86, "rotation": 26.1, "x": 64.27, "y": 33.36}, {"name": "bone68", "parent": "bone37", "length": 40.56, "rotation": 83.15, "x": 53.78, "y": 41.73}, {"name": "bone69", "parent": "bone37", "length": 37.65, "rotation": -72.2, "x": 43.25, "y": -32.81}, {"name": "bone70", "parent": "bone37", "length": 36.71, "rotation": -71.2, "x": 71.3, "y": -60.12}, {"name": "bone72", "parent": "bone51", "x": -69.02, "y": 46}, {"name": "bone73", "parent": "bone51", "x": -0.06, "y": 67.83}, {"name": "bone74", "parent": "bone51", "x": 61.99, "y": 91.31}, {"name": "bone75", "parent": "bone37", "length": 39.27, "rotation": -140.24, "x": 26.62, "y": -67.66}], "slots": [{"name": "hy3", "bone": "bone42", "attachment": "hy3"}, {"name": "hy2", "bone": "bone51", "attachment": "hy2"}, {"name": "hy1", "bone": "bone52", "attachment": "hy1"}, {"name": "yc7", "bone": "bone", "attachment": "yc7"}, {"name": "bd2", "bone": "bone", "attachment": "bd2"}, {"name": "yc6", "bone": "bone18", "attachment": "yc6"}, {"name": "yc5", "bone": "bone15", "attachment": "yc5"}, {"name": "yc4", "bone": "bone12", "attachment": "yc4"}, {"name": "yc3", "bone": "bone27", "attachment": "yc3"}, {"name": "yc2", "bone": "bone21", "attachment": "yc2"}, {"name": "bd1", "bone": "bone", "attachment": "bd1"}, {"name": "biyan", "bone": "bone2", "attachment": "biyan"}, {"name": "eye", "bone": "bone2", "attachment": "eye"}, {"name": "lh1", "bone": "bone36", "attachment": "lh1"}, {"name": "z1", "bone": "bone2", "attachment": "z1"}, {"name": "yc1", "bone": "bone", "attachment": "yc1"}, {"name": "hb3", "bone": "bone", "attachment": "hb3"}, {"name": "hb2", "bone": "bone", "attachment": "hb2"}, {"name": "hb1", "bone": "bone", "attachment": "hb1"}], "transform": [{"name": "bbh1", "order": 1, "bones": ["bone12"], "target": "bone2", "rotation": 96.32, "x": 95.23, "y": 103.25, "shearY": -360, "rotateMix": 0, "translateMix": 0.136, "scaleMix": 0, "shearMix": 0}, {"name": "bbh2", "order": 2, "bones": ["bone15"], "target": "bone2", "rotation": 87.91, "x": 108.53, "y": 106.59, "rotateMix": 0, "translateMix": 0.136, "scaleMix": 0, "shearMix": 0}, {"name": "bbh3", "order": 3, "bones": ["bone18"], "target": "bone2", "rotation": 75.25, "x": 116.02, "y": 112.37, "rotateMix": 0, "translateMix": 0.136, "scaleMix": 0, "shearMix": 0}, {"name": "bh1", "order": 4, "bones": ["bone21"], "target": "bone2", "rotation": 11.58, "x": 127.91, "y": 53.96, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "bh2", "order": 5, "bones": ["bone27"], "target": "bone2", "rotation": 124.68, "x": -37.9, "y": 77.47, "shearY": -360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "bone2", "bones": ["bone3"], "target": "bone2", "rotation": 32.32, "x": 60.14, "y": -3, "rotateMix": 0, "translateMix": 0.228, "scaleMix": 0, "shearMix": 0}, {"name": "hu11", "order": 6, "bones": ["bone60"], "target": "bone2", "rotation": 178.35, "x": -122.24, "y": -57.96, "shearY": -360, "rotateMix": 0, "translateMix": 0.478, "scaleMix": 0, "shearMix": 0}, {"name": "hu22", "order": 7, "bones": ["bone64"], "target": "bone2", "rotation": -3.11, "x": -40.8, "y": 114.03, "rotateMix": 0, "translateMix": 0.772, "scaleMix": 0, "shearMix": 0}, {"name": "hu33", "order": 8, "bones": ["bone57"], "target": "bone2", "rotation": -1.4, "x": 131.65, "y": -173.38, "rotateMix": 0, "translateMix": 0.593, "scaleMix": 0, "shearMix": 0}, {"name": "hy1", "order": 9, "bones": ["bone51"], "target": "bone2", "x": -40.28, "y": -156.56, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "hy2", "order": 10, "bones": ["bone52"], "target": "bone2", "rotation": 48.02, "x": 136.34, "y": -79.11, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"bd1": {"bd1": {"type": "mesh", "uvs": [0.57242, 0, 0.40644, 0, 0.2679, 0.05977, 0.14435, 0.1851, 0.06448, 0.3635, 0.04076, 0.55075, 0.052, 0.73063, 0.13187, 0.8766, 0.19156, 0.91763, 0.26915, 0.97096, 0.41892, 1, 0.57492, 1, 0.73592, 0.97391, 0.88318, 0.89282, 0.97304, 0.7616, 1, 0.59351, 1, 0.42543, 0.93435, 0.22933, 0.82453, 0.09074, 0.70222, 0.01996, 0.46646, 0.09771, 0.40289, 0.27691, 0.37689, 0.45953, 0.37256, 0.63703, 0.37833, 0.81965, 0.65138, 0.13013, 0.61564, 0.30977, 0.60081, 0.48513, 0.58637, 0.65581, 0.57337, 0.83502, 0.32777, 0.10795, 0.24831, 0.27009, 0.20353, 0.44588, 0.20208, 0.64386, 0.2122, 0.81283, 0.81932, 0.20432, 0.81497, 0.37616, 0.79761, 0.52364, 0.77373, 0.68394, 0.72922, 0.84039, 0.93329, 0.41335, 0.91701, 0.56852, 0.88553, 0.72754, 0.83451, 0.87117, 0.11374, 0.3146, 0.08986, 0.51722, 0.10397, 0.71215, 0.15499, 0.85578], "triangles": [14, 42, 15, 13, 43, 42, 40, 36, 17, 8, 34, 9, 9, 24, 10, 7, 46, 47, 10, 24, 29, 9, 34, 24, 31, 3, 2, 41, 37, 40, 42, 38, 41, 42, 41, 15, 43, 38, 42, 30, 1, 20, 20, 0, 25, 31, 2, 30, 21, 30, 20, 26, 20, 25, 26, 25, 35, 44, 3, 31, 36, 26, 35, 32, 45, 44, 37, 27, 36, 38, 28, 37, 46, 45, 33, 38, 39, 28, 47, 46, 34, 31, 30, 21, 21, 20, 26, 32, 44, 31, 27, 26, 36, 33, 45, 32, 28, 27, 37, 46, 33, 34, 39, 29, 28, 22, 31, 21, 32, 31, 22, 27, 21, 26, 22, 21, 27, 23, 32, 22, 23, 22, 27, 33, 32, 23, 28, 23, 27, 34, 33, 23, 24, 23, 28, 34, 23, 24, 29, 24, 28, 36, 35, 17, 37, 36, 40, 38, 37, 41, 39, 38, 43, 5, 45, 46, 12, 39, 43, 35, 25, 18, 6, 46, 7, 12, 29, 39, 10, 29, 11, 12, 11, 29, 41, 40, 16, 20, 1, 0, 30, 2, 1, 45, 4, 44, 7, 47, 8, 8, 47, 34, 41, 16, 15, 6, 5, 46, 5, 4, 45, 25, 0, 19, 18, 25, 19, 35, 18, 17, 40, 17, 16, 13, 42, 14, 12, 43, 13, 44, 4, 3], "vertices": [2, 2, -35.67, 117.57, 0.904, 3, 43.44, 123.53, 0.096, 2, 2, -77.83, 116.97, 0.904, 3, 1.29, 123.88, 0.096, 2, 2, -112.82, 103.62, 0.6, 3, -34, 111.32, 0.4, 2, 2, -143.81, 76.23, 0.704, 3, -65.61, 84.64, 0.296, 2, 2, -163.55, 37.58, 0.712, 3, -86.21, 46.45, 0.288, 2, 2, -168.99, -2.75, 0.688, 3, -92.56, 6.25, 0.312, 2, 2, -165.58, -41.38, 0.568, 3, -90.03, -32.44, 0.432, 2, 2, -144.85, -72.47, 0.512, 3, -70, -63.99, 0.488, 2, 2, -129.56, -81.07, 0.672, 3, -54.92, -72.94, 0.328, 2, 2, -109.69, -92.25, 0.52, 3, -35.31, -84.57, 0.48, 2, 2, -71.57, -97.94, 0.384, 3, 2.68, -91.12, 0.616, 2, 2, -31.95, -97.38, 0.376, 3, 42.3, -91.45, 0.624, 2, 2, 8.85, -91.18, 0.464, 3, 83.23, -86.18, 0.536, 2, 2, 46, -73.21, 0.816, 3, 120.78, -69.06, 0.184, 2, 2, 68.41, -44.68, 0.648, 3, 143.83, -41.03, 0.352, 2, 2, 74.74, -8.45, 0.656, 3, 150.98, -4.96, 0.344, 2, 2, 74.22, 27.68, 0.736, 3, 151.28, 31.18, 0.264, 2, 2, 56.95, 69.6, 0.776, 3, 134.95, 73.47, 0.224, 2, 2, 28.63, 98.99, 0.736, 3, 107.31, 103.5, 0.264, 2, 2, -2.65, 113.76, 0.896, 3, 76.37, 118.97, 0.104, 2, 2, -62.28, 96.18, 0.16, 3, 16.36, 102.75, 0.84, 1, 3, -0.11, 64.36, 1, 1, 3, -7.04, 25.15, 1, 1, 3, -8.45, -13, 1, 2, 2, -82.43, -59.33, 0.236, 3, -7.31, -52.27, 0.764, 2, 2, -15.22, 89.89, 0.14, 3, 63.26, 95.39, 0.86, 1, 3, 53.87, 56.85, 1, 1, 3, 49.79, 19.18, 1, 1, 3, 45.82, -17.48, 1, 2, 2, -32.86, -61.92, 0.112, 3, 42.2, -55.98, 0.888, 2, 2, -97.47, 93.48, 0.3, 3, -18.89, 100.84, 0.7, 1, 3, -39.35, 66.15, 1, 1, 3, -51.04, 28.45, 1, 1, 3, -51.76, -14.11, 1, 2, 2, -124.65, -58.46, 0.3, 3, -49.49, -50.45, 0.7, 2, 2, 27.66, 74.55, 0.292, 3, 105.78, 79.09, 0.708, 1, 3, 104.37, 42.16, 1, 1, 3, 99.7, 10.49, 1, 1, 3, 93.35, -23.92, 1, 2, 2, 6.74, -62.5, 0.156, 3, 81.77, -57.46, 0.844, 2, 2, 57.25, 30.04, 0.388, 3, 134.36, 33.91, 0.612, 2, 2, 53.59, -3.38, 0.228, 3, 129.95, 0.59, 0.772, 2, 2, 46.09, -37.68, 0.148, 3, 121.67, -33.53, 0.852, 2, 2, 33.57, -68.74, 0.3, 3, 108.46, -64.3, 0.7, 2, 2, -151.19, 48.28, 0.516, 3, -73.61, 56.86, 0.484, 2, 2, -156.63, 4.63, 0.3, 3, -80.03, 13.35, 0.7, 2, 2, -152.44, -37.22, 0.3, 3, -76.8, -28.58, 0.7, 2, 2, -139.04, -67.91, 0.556, 3, -64.09, -59.56, 0.444], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 20, 38, 50, 50, 52, 52, 54, 54, 56, 56, 58, 2, 60, 60, 62, 62, 64, 64, 66, 66, 68, 36, 70, 70, 72, 72, 74, 74, 76, 76, 78, 34, 80, 80, 82, 82, 84, 84, 86, 6, 88, 88, 90, 90, 92, 92, 94, 14, 16, 16, 18, 94, 16], "width": 254, "height": 215}}, "bd2": {"bd2": {"type": "mesh", "uvs": [0, 0.62617, 0.00765, 0.49855, 0.09732, 0.36547, 0.23664, 0.28161, 0.41599, 0.25244, 0.57453, 0.14852, 0.661, 0.03549, 0.71705, 0, 0.80992, 0.01908, 0.8996, 0.16311, 0.97166, 0.34724, 1, 0.59335, 0.96526, 0.7866, 0.86117, 0.95067, 0.68822, 1, 0.46243, 1, 0.22703, 0.90874, 0.0701, 0.80301, 0.01566, 0.71914, 0.38838, 0.78285, 0.56513, 0.62537, 0.68808, 0.44165, 0.76237, 0.22876, 0.77774, 0.07712, 0.75478, 0.81164, 0.80043, 0.60194, 0.84281, 0.37739, 0.84444, 0.21779, 0.17936, 0.64648, 0.35867, 0.52214, 0.51353, 0.39038, 0.60645, 0.26975, 0.67817, 0.14913, 0.71077, 0.05262], "triangles": [14, 24, 13, 26, 27, 10, 26, 22, 27, 27, 9, 10, 31, 32, 22, 31, 5, 32, 22, 32, 23, 22, 23, 27, 23, 32, 33, 27, 23, 9, 23, 8, 9, 5, 6, 32, 32, 6, 33, 8, 23, 7, 23, 33, 7, 33, 6, 7, 24, 25, 12, 12, 25, 11, 20, 21, 25, 20, 30, 21, 25, 26, 11, 25, 21, 26, 26, 10, 11, 30, 31, 21, 21, 22, 26, 21, 31, 22, 30, 4, 31, 4, 5, 31, 14, 15, 24, 16, 19, 15, 15, 20, 24, 15, 19, 20, 13, 24, 12, 17, 28, 16, 16, 28, 19, 24, 20, 25, 17, 18, 28, 28, 29, 19, 19, 29, 20, 18, 0, 28, 0, 1, 28, 1, 2, 28, 29, 2, 3, 29, 28, 2, 29, 30, 20, 29, 4, 30, 29, 3, 4], "vertices": [3, 4, -25.42, 39.95, 0.4559, 5, -55.22, 66.13, 0.0001, 3, 17.3, 87.9, 0.544, 3, 4, -15.48, 53.28, 0.45326, 5, -40.94, 74.64, 0.00274, 3, 18.57, 104.48, 0.544, 3, 4, 5.04, 60.63, 0.43848, 5, -19.16, 73.58, 0.01752, 3, 31.99, 121.66, 0.544, 4, 4, 28.28, 58.63, 0.39263, 5, 1.54, 62.83, 0.06308, 6, -12.43, 75.7, 0.00029, 3, 52.69, 132.4, 0.544, 4, 4, 52.64, 47.45, 0.41051, 5, 19.76, 43.17, 0.36113, 6, -4.53, 50.08, 0.04436, 3, 79.26, 135.97, 0.184, 4, 4, 79.68, 46.1, 0.06496, 5, 44.23, 31.57, 0.38673, 6, 12.49, 29.02, 0.46031, 3, 102.84, 149.28, 0.088, 3, 4, 98.4, 51.52, 0.00347, 5, 63.59, 29.41, 0.06477, 6, 29, 18.68, 0.93176, 3, 4, 107.87, 50.9, 0.0001, 5, 72.1, 25.22, 0.01321, 6, 34.86, 11.21, 0.98669, 2, 5, 77.89, 12.5, 0, 6, 34.56, -2.75, 1, 3, 5, 70.05, -9.06, 0.04074, 6, 18.15, -18.79, 0.90326, 3, 150.93, 146.99, 0.056, 4, 4, 115.1, -7.45, 0.00739, 5, 56.44, -31.46, 0.577, 6, -3.82, -33.07, 0.34361, 3, 161.39, 122.97, 0.072, 4, 4, 101.3, -36.61, 0.13611, 5, 32.52, -53.12, 0.75282, 6, -34.76, -42.22, 0.02308, 3, 165.32, 90.94, 0.088, 3, 4, 83.37, -54.95, 0.36433, 5, 8.94, -63.19, 0.63567, 6, -60.38, -41.07, 1e-05, 3, 4, 58.87, -64.53, 0.27043, 5, -17.36, -62.66, 0.18557, 3, 144.39, 44.66, 0.544, 3, 4, 33.88, -56.06, 0.37414, 5, -37.2, -45.27, 0.08186, 3, 118.75, 38.46, 0.544, 3, 4, 5.79, -37.97, 0.45315, 5, -56.22, -17.8, 0.00285, 3, 85.33, 38.74, 0.544, 2, 4, -17.07, -9.13, 0.456, 3, 50.6, 50.89, 0.544, 2, 4, -29.15, 15, 0.456, 3, 27.49, 64.83, 0.544, 2, 4, -30.02, 28.53, 0.456, 3, 19.52, 75.79, 0.544, 2, 4, 11.87, -8.3, 0.832, 3, 74.61, 67.06, 0.168, 2, 4, 44.94, -5.26, 0.904, 3, 100.93, 87.31, 0.096, 2, 5, 22.46, -3.94, 0.872, 3, 119.33, 111.04, 0.128, 1, 6, 6.54, -0.07, 1, 3, 4, 109.99, 37.61, 0, 5, 68.97, 12.13, 0.00062, 6, 26.37, 0.77, 0.99938, 3, 4, 55.42, -40.81, 0.52572, 5, -11.46, -39.43, 0.32228, 3, 128.8, 62.86, 0.152, 4, 4, 75.86, -21.56, 0.18077, 5, 14.79, -29.47, 0.70553, 6, -40.49, -13.22, 0.0017, 3, 135.78, 90.07, 0.112, 3, 4, 96.95, -0.42, 0.00602, 5, 42.36, -18.01, 0.78627, 6, -10.68, -14.85, 0.2077, 2, 5, 59.56, -6.4, 0.07003, 6, 9.85, -11.84, 0.92997, 3, 4, -4.53, 23.35, 0.926, 5, -42.28, 42.8, 0.002, 3, 43.82, 85.04, 0.072, 3, 4, 26.53, 22.57, 0.828, 5, -13.89, 30.19, 0.068, 3, 70.49, 100.98, 0.104, 4, 4, 55.07, 24.56, 0.32879, 5, 13.24, 21.09, 0.52859, 6, -19.98, 33.01, 0.02262, 3, 93.55, 117.92, 0.12, 4, 4, 75.12, 30.29, 0.0735, 5, 33.96, 18.71, 0.61558, 6, -2.34, 21.89, 0.21492, 3, 107.43, 133.48, 0.096, 3, 4, 92.53, 37.72, 0.01094, 5, 52.89, 18.91, 0.17139, 6, 14.81, 13.86, 0.81768, 3, 4, 103.38, 45.66, 0.00082, 5, 65.95, 22.09, 0.02707, 6, 27.96, 11.06, 0.97211], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 32, 38, 38, 40, 40, 42, 42, 44, 44, 46, 28, 48, 48, 50, 50, 52, 52, 54, 36, 56, 56, 58, 58, 60, 60, 62, 62, 64, 66, 64], "width": 148, "height": 130}}, "biyan": {"biyan": {"x": 3.19, "y": 19.37, "rotation": -0.47, "width": 100, "height": 21}}, "eye": {"eye": {"x": 5.68, "y": 18.35, "rotation": -0.47, "width": 101, "height": 35}}, "hb1": {"hb1": {"type": "mesh", "uvs": [0.40579, 0, 0.73262, 0, 1, 0, 1, 0.14687, 1, 0.28087, 0.89276, 0.53349, 0.76857, 0.74657, 0.68032, 0.95306, 0.46788, 1, 0.1574, 1, 0, 1, 0, 0.83224, 0.0332, 0.63454, 0.1574, 0.41487, 0.29466, 0.28526, 0.51037, 0.22815, 0.41559, 0.11392, 0.7329, 0.11599, 0.78481, 0.26223, 0.72548, 0.4633, 0.57466, 0.72752, 0.29776, 0.84051, 0.2409, 0.68597, 0.27056, 0.52977, 0.45599, 0.35362], "triangles": [16, 0, 1, 17, 1, 2, 16, 1, 17, 17, 2, 3, 15, 16, 17, 18, 17, 3, 15, 17, 18, 18, 3, 4, 24, 14, 15, 18, 24, 15, 19, 24, 18, 24, 13, 14, 4, 19, 18, 5, 19, 4, 19, 23, 24, 24, 23, 13, 12, 13, 23, 22, 12, 23, 6, 20, 19, 20, 23, 19, 22, 23, 20, 5, 6, 19, 11, 12, 22, 21, 22, 20, 11, 22, 21, 7, 20, 6, 8, 21, 20, 9, 10, 11, 21, 9, 11, 7, 8, 20, 9, 21, 8], "vertices": [1, 67, -14.25, 9.84, 1, 1, 67, -0.87, 10.45, 1, 1, 67, 10.09, 10.96, 1, 2, 66, 24.35, 41.85, 0.0031, 67, 10.5, 2.01, 0.9969, 2, 66, 24.72, 33.69, 0.06099, 67, 10.87, -6.16, 0.93901, 2, 66, 21.04, 18.09, 0.42339, 67, 7.19, -21.75, 0.57661, 2, 66, 16.55, 4.87, 0.81346, 67, 2.7, -34.97, 0.18654, 2, 66, 13.52, -7.88, 0.9853, 67, -0.34, -47.72, 0.0147, 2, 66, 4.95, -11.14, 0.99997, 67, -8.91, -50.98, 3e-05, 1, 66, -7.77, -11.72, 1, 1, 66, -14.22, -12.02, 1, 2, 66, -14.69, -1.79, 0.99391, 67, -28.54, -41.64, 0.00609, 2, 66, -13.88, 10.32, 0.89145, 67, -27.73, -29.53, 0.10855, 2, 66, -9.41, 23.93, 0.61273, 67, -23.26, -15.91, 0.38727, 2, 66, -4.15, 32.09, 0.41547, 67, -18, -7.76, 0.58453, 2, 66, 4.52, 35.98, 0.14408, 67, -9.33, -3.87, 0.85592, 2, 66, 0.32, 42.76, 0.01017, 67, -13.53, 2.91, 0.98983, 1, 67, -0.53, 3.38, 1, 2, 66, 15.86, 34.42, 0.06891, 67, 2.01, -5.43, 0.93109, 2, 66, 13.99, 22.05, 0.37793, 67, 0.14, -17.79, 0.62207, 2, 66, 8.56, 5.67, 0.85903, 67, -5.3, -34.18, 0.14097, 1, 66, -2.47, -1.74, 1, 2, 66, -5.23, 7.57, 0.90907, 67, -19.08, -32.27, 0.09093, 2, 66, -4.45, 17.15, 0.71057, 67, -18.31, -22.7, 0.28943, 2, 66, 2.65, 28.23, 0.37019, 67, -11.2, -11.62, 0.62981], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 34, 36, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48], "width": 41, "height": 61}}, "hb2": {"hb2": {"type": "mesh", "uvs": [0, 0.38702, 0.03247, 0.27437, 0.14971, 0.16173, 0.39721, 0.10938, 0.63819, 0.03639, 0.77497, 0, 1, 0, 1, 0.18394, 1, 0.38702, 0.93129, 0.57264, 0.83793, 0.72019, 0.88352, 0.8487, 0.89438, 1, 0.65556, 1, 0.42326, 0.95182, 0.20399, 0.85029, 0.1128, 0.74716, 0, 0.71702, 0, 0.58851, 0, 0.49332, 0.22099, 0.36871, 0.60919, 0.33204, 0.83367, 0.33204, 0.35039, 0.56362, 0.6805, 0.5752, 0.34247, 0.69871, 0.54317, 0.83188, 0.77292, 0.93416, 0.1312, 0.28379, 0.39528, 0.20467, 0.72275, 0.17765], "triangles": [16, 17, 18, 23, 16, 18, 18, 19, 23, 19, 20, 23, 23, 20, 21, 19, 0, 20, 0, 28, 20, 0, 1, 28, 20, 29, 21, 20, 28, 29, 28, 2, 29, 28, 1, 2, 29, 2, 3, 23, 21, 24, 24, 22, 9, 24, 21, 22, 9, 22, 8, 22, 7, 8, 21, 30, 22, 22, 30, 7, 21, 29, 30, 30, 3, 4, 30, 29, 3, 30, 5, 7, 5, 6, 7, 30, 4, 5, 13, 27, 12, 27, 11, 12, 14, 26, 13, 13, 26, 27, 14, 15, 26, 27, 26, 11, 15, 25, 26, 15, 16, 25, 26, 10, 11, 26, 24, 10, 26, 25, 24, 23, 25, 16, 10, 24, 9, 25, 23, 24], "vertices": [3, 62, 39.92, -39.4, 0.00057, 63, 57.76, 8.46, 0.00503, 64, 10.09, -4.08, 0.9944, 2, 63, 55.53, -3.31, 0.07305, 64, 7.87, -15.84, 0.92695, 2, 63, 46.86, -15.2, 0.22433, 64, -0.8, -27.74, 0.77567, 2, 63, 28.17, -21.04, 0.56992, 64, -19.49, -33.57, 0.43008, 2, 63, 10.01, -29, 0.89216, 64, -37.65, -41.54, 0.10784, 2, 63, -0.3, -33, 0.96679, 64, -47.96, -45.54, 0.03321, 2, 63, -17.4, -33.35, 0.99989, 64, -65.06, -45.89, 0.00011, 1, 63, -17.79, -14.23, 1, 2, 62, -36.07, -40.97, 0.06602, 63, -18.23, 6.89, 0.93398, 2, 62, -31.24, -21.56, 0.38079, 63, -13.41, 26.3, 0.61921, 3, 62, -24.47, -6.08, 0.78236, 63, -6.63, 41.78, 0.21752, 64, -54.29, 29.25, 0.00011, 2, 62, -28.21, 7.22, 0.96178, 63, -10.37, 55.08, 0.03822, 2, 62, -29.36, 22.93, 0.99986, 63, -11.52, 70.79, 0.00014, 1, 62, -11.21, 23.3, 1, 2, 62, 6.54, 18.66, 0.98639, 64, -23.28, 53.98, 0.01361, 2, 62, 23.42, 8.45, 0.78509, 64, -6.4, 43.77, 0.21491, 3, 62, 30.57, -2.13, 0.51761, 63, 48.41, 45.73, 3e-05, 64, 0.75, 33.19, 0.48235, 2, 62, 39.21, -5.09, 0.38125, 64, 9.39, 30.23, 0.61875, 2, 62, 39.48, -18.45, 0.21825, 64, 9.66, 16.87, 0.78175, 2, 62, 39.69, -28.35, 0.06537, 64, 9.87, 6.97, 0.93463, 3, 62, 23.16, -41.65, 0.01234, 63, 41, 6.21, 0.11708, 64, -6.66, -6.33, 0.87058, 3, 62, -6.25, -46.08, 0.04577, 63, 11.58, 1.78, 0.77771, 64, -36.08, -10.75, 0.17652, 2, 62, -23.31, -46.43, 0.01868, 63, -5.47, 1.43, 0.98132, 3, 62, 12.91, -21.59, 0.38695, 63, 30.75, 26.27, 0.11929, 64, -16.91, 13.73, 0.49376, 3, 62, -12.19, -20.9, 0.50521, 63, 5.64, 26.96, 0.42908, 64, -42.02, 14.42, 0.06572, 3, 62, 13.23, -7.53, 0.69968, 63, 31.06, 40.33, 0.025, 64, -16.6, 27.79, 0.27532, 2, 62, -2.31, 6, 0.99801, 63, 15.53, 53.86, 0.00199, 2, 62, -19.99, 16.27, 0.99423, 63, -2.15, 64.13, 0.00577, 2, 63, 48.01, -2.48, 0.1114, 64, 0.35, -15.02, 0.8886, 3, 62, 10.27, -58.98, 0.00205, 63, 28.11, -11.12, 0.50401, 64, -19.55, -23.66, 0.49394, 2, 63, 3.29, -14.45, 0.93665, 64, -44.38, -26.98, 0.06335], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 36, 46, 46, 48, 50, 52, 52, 54, 56, 58, 58, 60], "width": 76, "height": 104}}, "hb3": {"hb3": {"type": "mesh", "uvs": [0.13702, 0.20011, 0.28144, 0.08621, 0.49297, 0.02997, 0.79057, 0.04151, 1, 0.16839, 1, 0.32411, 0.91895, 0.44523, 0.8037, 0.64565, 0.65782, 0.8576, 0.46234, 1, 0.315, 1, 0.14723, 1, 0.10347, 0.8302, 0.17349, 0.66151, 0.2158, 0.49569, 0.13994, 0.3169, 0.58927, 0.2048, 0.6011, 0.42404, 0.53899, 0.64327, 0.35858, 0.81866], "triangles": [16, 2, 3, 15, 0, 1, 3, 4, 5, 16, 3, 5, 6, 17, 16, 5, 6, 16, 17, 14, 15, 1, 16, 17, 16, 1, 2, 1, 17, 15, 18, 14, 17, 7, 17, 6, 18, 17, 7, 13, 14, 18, 19, 13, 18, 12, 13, 19, 8, 18, 7, 19, 18, 8, 10, 11, 12, 19, 10, 12, 9, 19, 8, 10, 19, 9], "vertices": [2, 59, -22.49, 54.46, 0.09496, 60, -40.18, 10.01, 0.90504, 2, 59, -10.38, 64.45, 0.03715, 60, -28.06, 20, 0.96285, 2, 59, 7.52, 69.57, 0.00086, 60, -10.16, 25.13, 0.99914, 1, 60, 15.15, 24.54, 1, 1, 60, 33.12, 13.92, 1, 2, 59, 51.02, 44.98, 0.0076, 60, 33.34, 0.53, 0.9924, 2, 59, 44.3, 34.45, 0.06029, 60, 26.62, -10, 0.93971, 2, 59, 34.79, 17.06, 0.35439, 60, 17.1, -27.39, 0.64561, 2, 59, 22.68, -1.37, 0.80785, 60, 5, -45.81, 0.19215, 2, 59, 6.26, -13.88, 0.99808, 60, -11.42, -58.33, 0.00192, 1, 59, -6.26, -14.08, 1, 1, 59, -20.52, -14.31, 1, 2, 59, -24.47, 0.23, 0.98463, 60, -42.15, -44.22, 0.01537, 2, 59, -18.75, 14.83, 0.82526, 60, -36.44, -29.62, 0.17474, 2, 59, -15.39, 29.15, 0.44016, 60, -33.07, -15.3, 0.55984, 2, 59, -22.08, 44.42, 0.15006, 60, -39.77, -0.03, 0.84994, 1, 60, -1.73, 10.23, 1, 2, 59, 17.26, 35.84, 0.12606, 60, -0.42, -8.61, 0.87394, 2, 59, 12.28, 16.9, 0.59078, 60, -5.4, -27.55, 0.40922, 2, 59, -2.8, 1.57, 0.98706, 60, -20.49, -42.88, 0.01294], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 4, 32, 32, 34, 34, 36, 36, 38], "width": 85, "height": 86}}, "hy1": {"hy1": {"type": "mesh", "uvs": [0.10906, 1, 0.23306, 0.86884, 0.38373, 0.72877, 0.57573, 0.628, 0.78106, 0.57334, 0.93439, 0.52893, 1, 0.42473, 1, 0.2488, 0.86106, 0.15314, 0.75039, 0.03699, 0.63924, 0.01995, 0.50906, 0, 0.40773, 0.04553, 0.27706, 0.13777, 0.14506, 0.17706, 0.02373, 0.31541, 0, 0.42473, 0, 0.61775, 0.03039, 0.80051, 0.02373, 0.91667, 0.03439, 1, 0.12037, 0.8432, 0.19192, 0.70907, 0.28441, 0.56152, 0.46415, 0.46316, 0.6212, 0.39833, 0.84457, 0.38715, 0.77651, 0.27537, 0.63167, 0.19266, 0.45193, 0.21054, 0.25823, 0.27313, 0.13608, 0.41174, 0.08198, 0.59058], "triangles": [17, 16, 32, 32, 31, 23, 32, 16, 31, 31, 30, 23, 16, 15, 31, 31, 14, 30, 31, 15, 14, 30, 14, 13, 30, 29, 24, 25, 29, 28, 30, 13, 29, 13, 12, 29, 29, 11, 28, 29, 12, 11, 28, 10, 9, 28, 11, 10, 3, 25, 4, 4, 26, 5, 4, 25, 26, 5, 26, 6, 26, 7, 6, 25, 27, 26, 25, 28, 27, 26, 8, 7, 26, 27, 8, 28, 9, 27, 27, 9, 8, 2, 24, 3, 2, 23, 24, 3, 24, 25, 23, 30, 24, 24, 29, 25, 0, 21, 1, 21, 0, 19, 0, 20, 19, 19, 18, 21, 21, 22, 1, 1, 22, 2, 21, 18, 22, 18, 32, 22, 18, 17, 32, 22, 23, 2, 22, 32, 23], "vertices": [1, 53, -15.03, -12.54, 1, 1, 53, 22.61, -18.46, 1, 3, 53, 65.68, -28.03, 0.54706, 54, 4.58, -29.26, 0.45058, 55, -60.57, -37.02, 0.00236, 2, 54, 53.74, -27.95, 0.43464, 55, -12.12, -28.6, 0.56536, 1, 55, 37.58, -29.2, 1, 1, 55, 74.84, -29, 1, 1, 55, 94.17, -13.52, 1, 2, 55, 101.17, 18.27, 0.98589, 56, 116.32, -0.25, 0.01411, 3, 54, 148.09, 30.25, 0.00276, 55, 72.82, 42.63, 0.80854, 56, 83.54, 17.72, 0.1887, 3, 54, 131.18, 59.63, 0.00653, 55, 51.83, 69.26, 0.4668, 56, 57.49, 39.42, 0.52667, 4, 54, 107.66, 71.91, 0.00268, 55, 26.79, 78.01, 0.22715, 56, 31.17, 42.79, 0.77016, 57, 106.29, 79.57, 1e-05, 3, 55, -2.55, 88.25, 0.04075, 56, 0.35, 46.74, 0.93329, 57, 75.47, 83.52, 0.02596, 4, 54, 54.67, 86.92, 0.00048, 55, -27.82, 85.2, 0.00187, 56, -23.73, 38.51, 0.86761, 57, 51.38, 75.3, 0.13004, 3, 54, 19.67, 81.94, 0.0379, 56, -54.84, 21.7, 0.47785, 57, 20.27, 58.49, 0.48426, 3, 54, -12.16, 86.22, 0.00837, 56, -86.18, 14.69, 0.17965, 57, -11.07, 51.48, 0.81198, 3, 53, 66.41, 86.54, 0.00859, 56, -115.15, -10.66, 0.02347, 57, -40.04, 26.12, 0.96794, 3, 53, 47.53, 77.35, 0.08439, 56, -120.94, -30.84, 0.00171, 57, -45.83, 5.95, 0.9139, 2, 53, 20.79, 53.69, 0.48164, 57, -46.12, -29.76, 0.51836, 2, 53, 0.25, 25.88, 0.90892, 57, -39.2, -63.63, 0.09108, 2, 53, -16.89, 12.83, 0.99498, 57, -40.95, -85.11, 0.00502, 1, 53, -26.76, 0.72, 1, 2, 53, 8.47, 4.68, 0.99089, 57, -17.94, -71.7, 0.00911, 2, 53, 38.29, 8.43, 0.93777, 57, -0.78, -47.03, 0.06223, 4, 53, 73.25, 10.1, 0.61296, 54, -6.47, 8.01, 0.21098, 56, -53.75, -56.7, 0.00439, 57, 21.37, -19.92, 0.17167, 3, 54, 39.81, 9.94, 0.89222, 56, -11, -38.86, 0.08877, 57, 64.12, -2.07, 0.01901, 3, 54, 78.87, 7.97, 0.58064, 55, 7.55, 10.57, 0.31175, 56, 26.32, -27.17, 0.10761, 3, 54, 129.1, -8.85, 0.0001, 55, 59.69, 1.2, 0.99803, 56, 79.27, -25.54, 0.00187, 3, 54, 121.34, 16.2, 0.02681, 55, 48.39, 24.86, 0.80792, 56, 63.31, -4.73, 0.16527, 3, 54, 94.66, 42.67, 0.07967, 55, 18.16, 47.19, 0.25254, 56, 29.12, 10.86, 0.66778, 3, 54, 53.66, 54.67, 0.02781, 56, -13.51, 7.9, 0.88164, 57, 61.61, 44.68, 0.09054, 3, 54, 6.62, 60.1, 0.09157, 56, -59.51, -3.3, 0.28339, 57, 15.61, 33.48, 0.62504, 3, 53, 70.71, 54.79, 0.01332, 56, -88.67, -28.7, 0.01463, 57, -13.56, 8.08, 0.97205, 2, 53, 37.43, 42.47, 0.44715, 57, -26.65, -24.9, 0.55285], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 237, "height": 185}}, "hy2": {"hy2": {"type": "mesh", "uvs": [0, 0.70604, 0.01173, 0.49398, 0.13906, 0.20752, 0.29109, 0.03266, 0.46023, 0, 0.67118, 0, 0.83652, 0, 1, 0, 1, 0.29308, 0.95434, 0.50886, 0.87643, 0.76557, 0.72249, 0.9367, 0.56476, 0.9739, 0.40322, 1, 0.22268, 1, 0.06114, 0.92554, 0, 0.86229, 0.12197, 0.61479, 0.30259, 0.49861, 0.49869, 0.41779, 0.68447, 0.37233, 0.82896, 0.28646, 0.93991, 0.16019, 0.77994, 0.65015, 0.60706, 0.76127, 0.44709, 0.77138, 0.25099, 0.79663, 0.16584, 0.37739, 0.28711, 0.25111, 0.48579, 0.16019, 0.67415, 0.12988, 0.81864, 0.13493], "triangles": [11, 23, 10, 10, 23, 9, 23, 21, 9, 23, 20, 21, 9, 21, 8, 8, 21, 22, 21, 30, 31, 21, 20, 30, 22, 7, 8, 21, 31, 22, 31, 6, 22, 22, 6, 7, 6, 31, 5, 31, 30, 5, 13, 25, 12, 12, 24, 11, 12, 25, 24, 11, 24, 23, 25, 19, 24, 25, 18, 19, 24, 20, 23, 24, 19, 20, 18, 28, 19, 28, 29, 19, 20, 29, 30, 20, 19, 29, 29, 3, 4, 29, 28, 3, 29, 5, 30, 29, 4, 5, 14, 26, 13, 13, 26, 25, 14, 15, 26, 15, 17, 26, 17, 15, 0, 15, 16, 0, 26, 18, 25, 26, 17, 18, 0, 1, 17, 17, 27, 18, 17, 1, 27, 27, 28, 18, 1, 2, 27, 27, 2, 28, 28, 2, 3], "vertices": [1, 72, -53.05, -1.96, 1, 2, 72, -50.14, 23.04, 0.99316, 73, -119.1, 1.22, 0.00684, 2, 72, -20.45, 56.6, 0.8048, 73, -89.41, 34.78, 0.1952, 3, 72, 14.84, 76.94, 0.46556, 73, -54.12, 55.12, 0.53084, 74, -116.17, 31.64, 0.0036, 3, 72, 53.94, 80.48, 0.14929, 73, -15.02, 58.65, 0.74716, 74, -77.07, 35.17, 0.10355, 3, 72, 102.67, 80.07, 0.00135, 73, 33.71, 58.25, 0.34817, 74, -28.34, 34.77, 0.65048, 2, 73, 71.9, 57.93, 0.01068, 74, 9.85, 34.45, 0.98932, 1, 74, 47.62, 34.14, 1, 2, 73, 109.38, 23.04, 5e-05, 74, 47.33, -0.44, 0.99995, 2, 73, 98.62, -2.34, 0.04282, 74, 36.57, -25.82, 0.95718, 2, 73, 80.37, -32.48, 0.26859, 74, 18.32, -55.96, 0.73141, 3, 72, 113.61, -30.55, 0.00678, 73, 44.65, -52.38, 0.62643, 74, -17.4, -75.86, 0.3668, 3, 72, 77.14, -34.64, 0.12618, 73, 8.17, -56.47, 0.78114, 74, -53.87, -79.95, 0.09268, 3, 72, 39.8, -37.41, 0.52109, 73, -29.17, -59.24, 0.4771, 74, -91.21, -82.72, 0.00181, 2, 72, -1.9, -37.07, 0.9569, 73, -70.87, -58.89, 0.0431, 1, 72, -39.15, -27.97, 1, 1, 72, -53.21, -20.39, 1, 2, 72, -24.79, 8.58, 0.99536, 73, -93.75, -13.25, 0.00464, 2, 72, 17.04, 21.94, 0.67606, 73, -51.92, 0.12, 0.32394, 3, 72, 62.42, 31.1, 0.05385, 73, -6.54, 9.28, 0.9311, 74, -68.59, -14.2, 0.01505, 2, 73, 36.41, 14.29, 0.42516, 74, -25.63, -9.19, 0.57484, 1, 74, 7.83, 0.66, 1, 1, 74, 33.58, 15.35, 1, 2, 73, 58.19, -18.67, 0.35669, 74, -3.85, -42.16, 0.64331, 3, 72, 87.12, -9.63, 0.03854, 73, 18.15, -31.46, 0.79303, 74, -43.89, -54.94, 0.16843, 3, 72, 50.15, -10.52, 0.33071, 73, -18.81, -32.34, 0.66418, 74, -80.86, -55.83, 0.0051, 2, 72, 4.83, -13.12, 0.94868, 73, -64.13, -34.95, 0.05132, 2, 72, -14.43, 36.51, 0.8682, 73, -83.39, 14.68, 0.1318, 3, 72, 13.71, 51.18, 0.56119, 73, -55.26, 29.35, 0.43842, 74, -117.3, 5.87, 0.00039, 3, 72, 59.69, 61.53, 0.11025, 73, -9.27, 39.7, 0.77534, 74, -71.32, 16.22, 0.11441, 3, 72, 103.23, 64.74, 0.00057, 73, 34.27, 42.92, 0.35046, 74, -27.78, 19.44, 0.64897, 2, 73, 67.64, 42.04, 0.01127, 74, 5.59, 18.56, 0.98873], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60], "width": 231, "height": 118}}, "hy3": {"hy3": {"type": "mesh", "uvs": [0, 0.35306, 0.02806, 0.31401, 0.05239, 0.29371, 0.06542, 0.21092, 0.11061, 0.14532, 0.19663, 0.13438, 0.2983, 0.17656, 0.36087, 0.27809, 0.42691, 0.40929, 0.49208, 0.53581, 0.56594, 0.67639, 0.64068, 0.80135, 0.72583, 0.92006, 0.81968, 0.98566, 0.92222, 0.97473, 1, 0.92787, 1, 0.86227, 1, 0.78104, 0.90919, 0.8529, 0.8249, 0.87945, 0.74061, 0.80447, 0.67283, 0.70919, 0.61113, 0.60766, 0.53466, 0.45303, 0.47036, 0.32651, 0.40258, 0.19686, 0.32524, 0.09377, 0.2201, 0.05004, 0.11007, 0.03804, 0.04055, 0.10833, 0.01014, 0.19424, 0, 0.28171, 0.02459, 0.24453, 0.05151, 0.15668, 0.10676, 0.09683, 0.16839, 0.09047, 0.24347, 0.1032, 0.3136, 0.13885, 0.36318, 0.21015, 0.41843, 0.29037, 0.47085, 0.3935, 0.51618, 0.49282, 0.56999, 0.60173, 0.62524, 0.6985, 0.69183, 0.80163, 0.7662, 0.88949, 0.8335, 0.93533, 0.91, 0.92387], "triangles": [0, 31, 1, 31, 32, 1, 1, 32, 2, 2, 32, 3, 31, 30, 32, 3, 32, 33, 32, 30, 33, 3, 33, 4, 30, 29, 33, 33, 34, 4, 33, 29, 34, 4, 34, 35, 34, 29, 28, 34, 28, 35, 5, 36, 6, 4, 35, 5, 5, 35, 36, 35, 27, 36, 36, 27, 26, 35, 28, 27, 38, 25, 39, 7, 6, 38, 6, 37, 38, 25, 38, 26, 38, 37, 26, 6, 36, 37, 37, 36, 26, 8, 40, 9, 8, 7, 39, 7, 38, 39, 8, 39, 40, 39, 24, 40, 23, 40, 24, 24, 39, 25, 10, 42, 43, 42, 22, 43, 10, 9, 42, 9, 41, 42, 41, 23, 42, 22, 42, 23, 9, 40, 41, 41, 40, 23, 11, 44, 12, 44, 43, 21, 20, 44, 21, 11, 10, 43, 44, 11, 43, 21, 43, 22, 14, 13, 46, 12, 45, 13, 13, 45, 46, 45, 19, 46, 46, 19, 18, 12, 44, 45, 44, 20, 45, 45, 20, 19, 14, 46, 47, 14, 47, 15, 47, 46, 18, 47, 16, 15, 47, 18, 16, 18, 17, 16], "vertices": [1, 43, 6.53, 3.72, 1, 1, 43, 19.38, -4.84, 1, 1, 43, 27.41, -13.31, 1, 2, 44, 3.25, -15.03, 0.30242, 43, 48.2, -12.3, 0.69758, 1, 44, 27.47, -7.83, 1, 2, 45, 22.7, -9.71, 0.99998, 44, 63.08, -19.61, 2e-05, 2, 46, 15.85, -8.87, 0.99498, 45, 67.85, -14.39, 0.00502, 2, 47, -3.07, -15.59, 0.69485, 46, 52, -15.01, 0.30515, 1, 47, 39.73, -16.83, 1, 2, 48, 8.18, -17.68, 0.82647, 47, 81.46, -17.56, 0.17353, 2, 49, -9.76, -18.85, 0.11868, 48, 54.96, -18.74, 0.88132, 1, 49, 34.58, -16.49, 1, 2, 50, 7.98, -14.88, 0.95495, 49, 80.98, -9.81, 0.04505, 2, 51, -17.25, -6.97, 0.02162, 50, 51.76, -16.04, 0.97838, 1, 51, 26.02, -18.13, 1, 1, 51, 61.7, -17.71, 1, 1, 51, 66.57, -2.6, 1, 1, 51, 72.59, 16.11, 1, 1, 51, 29.66, 11.67, 1, 2, 51, -7.21, 16.8, 0.02671, 50, 45.22, 8.92, 0.97329, 2, 50, 4.58, 13.62, 0.56738, 49, 65.33, 14.25, 0.43262, 1, 49, 28.26, 9.07, 1, 1, 48, 56.95, 6.93, 1, 2, 48, 6.95, 9.57, 0.91724, 47, 79.81, 9.68, 0.08276, 1, 47, 38.35, 10.68, 1, 1, 46, 56.77, 11.32, 1, 1, 46, 15, 14.32, 1, 1, 45, 30.32, 11.8, 1, 2, 45, -17.54, 8.79, 0.00729, 44, 37.13, 16.27, 0.99271, 2, 44, 2.69, 12.04, 0.90883, 43, 68.29, 5.85, 0.09117, 1, 43, 44.37, 11.78, 1, 1, 43, 22.9, 9.22, 1, 1, 43, 34.84, 1.94, 1, 2, 44, 2.65, -0.59, 0.78031, 43, 58.72, -2.39, 0.21969, 1, 44, 30.38, 3.66, 1, 1, 45, 9.2, -0.68, 1, 1, 45, 41.99, 0.29, 1, 1, 46, 16.57, 2.4, 1, 2, 47, -14.24, -3.49, 0.00521, 46, 44.03, -0.59, 0.99479, 2, 47, 16.41, 0.42, 0.99928, 46, 74.73, -4.08, 0.00072, 1, 47, 50.2, -0.39, 1, 2, 48, 8.14, -2.91, 0.98995, 47, 81.2, -2.79, 0.01005, 1, 48, 43.35, -4.79, 1, 2, 49, 12.01, -4.02, 0.97825, 48, 76.9, -4.15, 0.02175, 1, 49, 50.09, -0.53, 1, 1, 50, 22.01, -1.99, 1, 1, 50, 53.31, -2.55, 1, 1, 51, 24.73, -4.78, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 2, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 32], "width": 435, "height": 242}}, "lh1": {"lh1": {"type": "mesh", "uvs": [0.56122, 0.49853, 0.53823, 0.48468, 0.49599, 0.47661, 0.44299, 0.47277, 0.39321, 0.47354, 0.33524, 0.48007, 0.28748, 0.48814, 0.23835, 0.48545, 0.16544, 0.4684, 0.1125, 0.4125, 0.06454, 0.32797, 0.03247, 0.23462, 0.02198, 0.13802, 0.02139, 0.05197, 0.04513, 0.01901, 0.08092, 0, 0.07571, 0.0634, 0.07571, 0.13406, 0.08287, 0.20743, 0.10304, 0.27447, 0.13037, 0.33969, 0.17397, 0.3877, 0.22147, 0.4185, 0.28654, 0.41941, 0.34185, 0.41307, 0.40107, 0.40491, 0.45703, 0.41307, 0.51755, 0.41853, 0.56994, 0.43657, 0.61614, 0.46716, 0.65727, 0.50167, 0.67147, 0.45179, 0.70448, 0.38371, 0.74849, 0.34031, 0.75827, 0.28074, 0.79005, 0.22117, 0.83468, 0.17011, 0.87263, 0.24243, 0.89644, 0.33292, 0.94313, 0.42086, 1, 0.50752, 0.96235, 0.58399, 0.93855, 0.67066, 0.92939, 0.78537, 0.88362, 0.86439, 0.80306, 0.90007, 0.71242, 0.8835, 0.67489, 0.94086, 0.59799, 0.99566, 0.56045, 0.90899, 0.48172, 0.8287, 0.40573, 0.83507, 0.3389, 0.84527, 0.37369, 0.76115, 0.45883, 0.66939, 0.53116, 0.67194, 0.57327, 0.60184, 0.60806, 0.52409, 0.81989, 0.32453, 0.79915, 0.44545, 0.72396, 0.53749, 0.67599, 0.58802, 0.63062, 0.70713, 0.55932, 0.76669, 0.47247, 0.75406, 0.38691, 0.80279, 0.62544, 0.90205, 0.64229, 0.82264, 0.7084, 0.72699, 0.7784, 0.64216, 0.86785, 0.53388, 0.93915, 0.51042, 0.82767, 0.76128], "triangles": [16, 14, 15, 13, 14, 16, 12, 13, 16, 17, 12, 16, 18, 11, 12, 18, 12, 17, 19, 10, 11, 19, 11, 18, 10, 19, 20, 9, 10, 20, 9, 20, 21, 8, 9, 21, 8, 21, 22, 7, 22, 23, 8, 22, 7, 6, 23, 5, 7, 23, 6, 3, 25, 26, 4, 24, 25, 4, 25, 3, 2, 26, 27, 3, 26, 2, 5, 23, 24, 5, 24, 4, 1, 27, 28, 2, 27, 1, 0, 1, 28, 0, 28, 29, 57, 0, 29, 57, 29, 30, 30, 31, 60, 57, 30, 61, 56, 57, 61, 64, 54, 55, 64, 55, 63, 64, 65, 53, 64, 53, 54, 50, 64, 63, 51, 65, 64, 50, 51, 64, 52, 53, 65, 52, 65, 51, 50, 63, 49, 67, 62, 68, 67, 68, 46, 67, 49, 63, 66, 67, 46, 66, 49, 67, 47, 66, 46, 48, 49, 66, 48, 66, 47, 62, 56, 61, 55, 56, 62, 63, 55, 62, 63, 62, 67, 61, 30, 60, 61, 60, 69, 68, 61, 69, 62, 61, 68, 72, 69, 42, 68, 69, 72, 43, 72, 42, 44, 72, 43, 46, 68, 72, 45, 46, 72, 44, 45, 72, 38, 39, 59, 71, 39, 40, 71, 70, 39, 39, 70, 59, 60, 32, 59, 41, 71, 40, 69, 60, 59, 69, 59, 70, 41, 42, 70, 41, 70, 71, 69, 70, 42, 35, 36, 37, 58, 35, 37, 34, 35, 58, 58, 37, 38, 33, 34, 58, 59, 33, 58, 32, 33, 59, 38, 59, 58, 31, 32, 60], "vertices": [2, 38, 8.54, -5.78, 0.9779, 37, 80.1, -8.92, 0.0221, 2, 38, -0.31, -6.64, 0.31963, 37, 71.76, -5.85, 0.68037, 2, 38, -14.43, -12.1, 0.00013, 37, 56.67, -4.62, 0.99987, 2, 38, -31.36, -20.35, 0, 37, 37.83, -4.69, 1, 1, 37, 20.2, -5.86, 1, 2, 37, -0.25, -8.67, 0.69344, 40, -8.43, 6, 0.30656, 1, 40, 8.34, 9.22, 1, 1, 40, 25.79, 9.73, 1, 2, 40, 51.91, 7.18, 0.38565, 41, -1.56, 7.87, 0.61435, 1, 41, 21.95, 9.72, 1, 2, 41, 49, 4.94, 0.35263, 42, 0.55, 5.96, 0.64737, 1, 42, 26.77, 8.86, 1, 1, 42, 51.26, 4.25, 1, 1, 43, 14.8, 7.96, 1, 1, 43, 25.45, 2.65, 1, 2, 42, 77.58, -27.1, 0.00026, 43, 34.09, -7.85, 0.99974, 2, 42, 62.93, -20.03, 0.03335, 43, 18.18, -11.25, 0.96665, 2, 42, 45.92, -14.09, 0.74918, 43, 1.1, -16.98, 0.25082, 1, 42, 27.42, -10.32, 1, 2, 41, 47.86, -14.34, 0.11238, 42, 8.92, -11.44, 0.88762, 2, 41, 29.56, -8.37, 0.98225, 42, -9.98, -15.12, 0.01775, 2, 40, 50.3, -13.56, 0.11564, 41, 9.86, -9.51, 0.88436, 1, 40, 32.94, -6.88, 1, 3, 37, -18.37, 5.82, 0.00504, 40, 9.88, -8.24, 0.99494, 41, -25.36, -30.05, 2e-05, 3, 37, 1.14, 8.52, 0.96061, 40, -9.6, -11.21, 0.03939, 41, -38.95, -44.31, 0, 1, 37, 22.02, 11.76, 1, 1, 37, 41.97, 10.79, 1, 1, 37, 63.49, 10.59, 1, 2, 38, 3.6, 9.54, 0.84239, 37, 82.32, 7.03, 0.15761, 3, 69, -34.68, 28.12, 0.00091, 68, -48.19, -1.67, 0.00048, 38, 21.73, 10.66, 0.99861, 4, 69, -33.28, 11.13, 0.07229, 68, -33.17, -9.73, 0.06526, 39, -8.09, 8.91, 0.16815, 38, 38.76, 10.02, 0.6943, 4, 69, -20.01, 14.48, 0.4281, 68, -28.77, 3.22, 0.16994, 39, -13.02, 21.67, 0.14446, 38, 37.02, 23.59, 0.25751, 3, 69, 0.92, 15.13, 0.96639, 39, -16.21, 42.37, 0.00572, 38, 38.87, 44.45, 0.02789, 1, 69, 19.04, 8.94, 1, 1, 69, 33.38, 15.01, 1, 1, 69, 52.3, 14.74, 1, 1, 69, 72.13, 9.51, 1, 2, 69, 65.06, -12.2, 0.93038, 68, 39.9, 60.08, 0.06962, 2, 69, 51.29, -32.55, 0.53216, 68, 49.48, 37.46, 0.46784, 2, 69, 42.8, -59.11, 0.0607, 68, 67.15, 15.89, 0.9393, 2, 68, 88.41, -5.18, 0.96338, 39, 82.23, 90.44, 0.03662, 2, 68, 76.03, -25.32, 0.81699, 39, 85.66, 67.05, 0.18301, 2, 68, 68.69, -47.81, 0.47139, 39, 94.47, 45.1, 0.52861, 3, 68, 66.9, -77.18, 0.16107, 39, 111.94, 21.42, 0.8271, 71, -22.72, 99.63, 0.01183, 3, 68, 51.67, -98.11, 0.0458, 39, 113.7, -4.4, 0.88215, 71, -0.02, 87.19, 0.07205, 3, 68, 23.56, -108.62, 0.00088, 39, 98.89, -30.5, 0.74494, 71, 13.98, 60.65, 0.25418, 2, 39, 72.41, -49.25, 0.27513, 71, 15.49, 28.23, 0.72487, 2, 39, 72.56, -69.04, 0.03723, 71, 32.24, 17.7, 0.96277, 3, 70, 85.49, 12.53, 0.01555, 71, 50.8, -6.71, 0.98426, 75, -14.73, 67.82, 0.00019, 3, 70, 66.39, -4.82, 0.36645, 71, 31.4, -23.72, 0.52976, 75, -5.78, 43.61, 0.10379, 3, 70, 51.71, -36.19, 0.11187, 71, 16.18, -54.84, 0.00289, 75, 17.83, 18.26, 0.88524, 1, 75, 44.63, 14.8, 1, 2, 70, 65.65, -85.12, 0, 75, 68.42, 12.91, 1, 2, 70, 42.22, -77.15, 0, 75, 52.27, -5.85, 1, 1, 75, 18.19, -23.16, 1, 3, 70, 9.1, -26.71, 0.37295, 75, -6.9, -17.71, 0.62439, 38, 20.61, -49.64, 0.00266, 4, 39, -12.63, -30.08, 0.00124, 70, -11.33, -15.5, 0.74869, 75, -24.94, -32.46, 0.05746, 38, 25.03, -26.76, 0.19261, 2, 70, -33.17, -7.21, 0.0432, 38, 26.24, -3.44, 0.9568, 2, 69, 37.12, -9.26, 0.88294, 68, 22.24, 38.24, 0.11706, 2, 69, 7.81, -21.33, 0.17529, 68, 16.41, 7.09, 0.82471, 4, 69, -26.83, -13.41, 0.01171, 68, -9.08, -17.68, 0.42268, 39, 15.49, 18.29, 0.55318, 38, 63.9, 13.49, 0.01242, 3, 39, 11.75, -2.73, 0.95835, 70, -21.84, 19.6, 0.03948, 71, -56.39, 2.22, 0.00217, 3, 39, 20.55, -35.96, 0.1061, 70, 11.08, 9.67, 0.74535, 71, -23.65, -8.28, 0.14855, 2, 70, 30.87, -12.23, 0.63412, 75, -12.19, 7.9, 0.36588, 1, 75, 17.49, -1.05, 1, 1, 75, 49.65, 5.46, 1, 3, 70, 60.19, 17.47, 0.01318, 71, 25.59, -1.33, 0.98579, 75, -28.77, 46.2, 0.00104, 2, 39, 43.59, -54.77, 0.00404, 71, 4.61, 0.99, 0.99596, 3, 39, 44.25, -20.93, 0.6028, 70, 10.7, 37.74, 0.06909, 71, -23.54, 19.79, 0.32811, 2, 68, 11.55, -43.38, 0.16553, 39, 47.81, 11.82, 0.83447, 2, 68, 41.89, -14.22, 0.86564, 39, 52.36, 53.65, 0.13436, 2, 68, 66.87, -6.99, 0.94757, 39, 66.88, 75.23, 0.05243, 3, 68, 30.52, -72.84, 0.05337, 39, 81.27, 1.4, 0.89498, 71, -22.4, 63, 0.05164], "hull": 58, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 0, 114, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142], "width": 355, "height": 255}}, "yc1": {"yc1": {"type": "mesh", "uvs": [0.17743, 1, 0.04929, 0.86974, 0.01586, 0.66966, 0.105, 0.45431, 0.24708, 0.3017, 0.53122, 0.14401, 0.89336, 0, 1, 0, 1, 0.15757, 0.94907, 0.36953, 0.924, 0.53909, 0.87665, 0.70527, 0.60922, 0.80022, 0.37522, 0.86805, 0.261, 0.99861, 0.1678, 0.80488, 0.25251, 0.59346, 0.46147, 0.37689, 0.6902, 0.20673, 0.92175, 0.09157, 0.83986, 0.27892, 0.72973, 0.41127, 0.65067, 0.55393, 0.60549, 0.70175, 0.31464, 0.74472], "triangles": [21, 20, 9, 21, 18, 20, 17, 5, 18, 9, 20, 8, 20, 18, 8, 18, 19, 8, 18, 5, 19, 19, 7, 8, 5, 6, 19, 19, 6, 7, 10, 21, 9, 23, 16, 22, 2, 3, 16, 16, 17, 22, 16, 3, 17, 22, 21, 10, 22, 17, 21, 3, 4, 17, 17, 18, 21, 17, 4, 5, 11, 22, 10, 23, 22, 11, 14, 0, 15, 14, 15, 13, 13, 24, 12, 12, 23, 11, 24, 16, 23, 0, 1, 15, 1, 2, 15, 15, 24, 13, 24, 15, 16, 24, 23, 12, 15, 2, 16], "vertices": [2, 34, -18.87, 0.1, 0.65, 2, -77.76, 68.22, 0.35, 1, 34, -2.42, 13.07, 1, 2, 34, 24.6, 19.39, 0.98511, 35, -22, 12.27, 0.01489, 2, 34, 55.03, 15.77, 0.09687, 35, 7.68, 19.89, 0.90313, 2, 35, 31.89, 19.46, 0.99666, 36, -23.81, 13.11, 0.00334, 2, 35, 62.4, 8.89, 0.01652, 36, 8.46, 11.86, 0.98348, 1, 36, 44.07, 4.66, 1, 1, 36, 50.45, -1.63, 1, 2, 35, 79.57, -26.6, 0.00046, 36, 35.19, -17.11, 0.99954, 2, 35, 51.83, -36.81, 0.27729, 36, 11.61, -34.94, 0.72271, 3, 34, 52.23, -53.95, 0.57428, 35, 30.26, -46.14, 0.30351, 36, -6.31, -50.13, 0.12221, 2, 34, 28.98, -52.94, 0.65, 2, -19.62, 109.72, 0.35, 2, 34, 13.11, -32.34, 0.65, 2, -41.89, 96.3, 0.35, 4, 34, 1.32, -14.05, 0.60137, 35, -31.63, -27.32, 0.04854, 36, -71, -50.11, 9e-05, 2, -61.41, 86.66, 0.35, 2, 34, -17.78, -6.84, 0.65, 2, -70.74, 68.51, 0.35, 1, 34, 7.73, 4.35, 1, 1, 35, -3.27, -0.17, 1, 2, 35, 31.37, -1.32, 0.99773, 36, -18.27, -6.92, 0.00227, 1, 36, 11.89, -3.68, 1, 1, 36, 36.9, -6.01, 1, 2, 35, 58.43, -22.78, 0.07464, 36, 13.86, -19.6, 0.92536, 3, 34, 67.64, -35.51, 7e-05, 35, 37.97, -23.38, 0.54733, 36, -5.55, -26.11, 0.4526, 3, 34, 47.26, -31.45, 0.04088, 35, 17.5, -26.95, 0.8394, 36, -24.1, -35.47, 0.11972, 3, 34, 26.55, -30.29, 0.89166, 35, -2.23, -33.35, 0.10305, 36, -41.12, -47.34, 0.00529, 3, 34, 17.54, -6.82, 0.89406, 35, -19.11, -14.72, 0.10576, 36, -62.68, -34.41, 0.00018], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 46, 48], "width": 84, "height": 138}}, "yc2": {"yc2": {"type": "mesh", "uvs": [0, 0.62999, 0.02695, 0.57252, 0.09557, 0.5366, 0.21491, 0.52103, 0.33276, 0.46835, 0.45957, 0.38333, 0.57742, 0.28874, 0.6729, 0.17978, 0.74301, 0.06483, 0.77881, 0, 0.83103, 0, 0.90562, 0.06124, 0.96976, 0.14027, 1, 0.25521, 1, 0.37136, 0.97573, 0.46835, 0.92352, 0.55216, 0.92949, 0.67788, 0.89667, 0.7641, 0.81462, 0.83354, 0.70572, 0.87784, 0.6729, 0.95088, 0.58339, 0.988, 0.44316, 1, 0.32381, 0.96046, 0.20596, 0.87665, 0.06872, 0.78565, 9e-05, 0.70542, 0.1162, 0.67276, 0.26644, 0.64536, 0.41896, 0.59968, 0.56465, 0.51198, 0.68075, 0.41149, 0.76497, 0.27994, 0.82416, 0.14108, 0.84465, 0.06251, 0.8879, 0.14656, 0.89928, 0.26715, 0.86514, 0.39139, 0.79684, 0.51381, 0.7331, 0.60516, 0.6466, 0.71296, 0.5305, 0.77874, 0.38254, 0.81893, 0.16856, 0.75316, 0.51912, 0.89933, 0.79912, 0.71844], "triangles": [43, 30, 42, 25, 44, 43, 45, 43, 42, 45, 42, 20, 21, 45, 20, 24, 25, 43, 24, 43, 45, 22, 45, 21, 23, 24, 45, 23, 45, 22, 41, 31, 40, 40, 39, 16, 17, 46, 40, 17, 40, 16, 41, 40, 46, 18, 46, 17, 42, 30, 41, 19, 46, 18, 19, 20, 41, 19, 41, 46, 42, 41, 20, 37, 13, 14, 38, 37, 14, 15, 38, 14, 39, 38, 15, 16, 39, 15, 35, 10, 11, 9, 35, 8, 35, 9, 10, 34, 8, 35, 36, 35, 11, 36, 11, 12, 34, 35, 36, 7, 8, 34, 37, 36, 12, 37, 12, 13, 33, 7, 34, 37, 33, 34, 37, 34, 36, 6, 7, 33, 38, 33, 37, 32, 6, 33, 32, 33, 38, 5, 6, 32, 31, 5, 32, 4, 5, 31, 39, 32, 38, 31, 32, 39, 30, 4, 31, 40, 31, 39, 30, 31, 41, 3, 4, 30, 29, 3, 30, 28, 2, 3, 28, 3, 29, 1, 2, 28, 0, 1, 28, 27, 0, 28, 44, 28, 29, 26, 27, 28, 26, 28, 44, 43, 29, 30, 44, 29, 43, 26, 44, 25], "vertices": [2, 22, -12.05, 5.3, 0.99924, 25, -38.27, 42.1, 0.00076, 1, 22, -7.02, 13.15, 1, 1, 22, 2.31, 16.75, 1, 2, 22, 17.05, 16.02, 0.94871, 23, -9, 23.9, 0.05129, 2, 22, 32.78, 20.85, 0.42621, 23, 7.01, 20.07, 0.57379, 3, 22, 50.61, 30.25, 0.01396, 23, 27.14, 19.11, 0.95015, 24, -9.93, 25.84, 0.0359, 2, 23, 47.46, 19.97, 0.3448, 24, 8.8, 17.93, 0.6552, 2, 23, 67.24, 24.28, 0.00148, 24, 28.53, 13.37, 0.99852, 1, 24, 47.94, 12.02, 1, 1, 24, 58.71, 11.68, 1, 1, 24, 61.11, 5.78, 1, 2, 24, 55.91, -6.15, 0.98884, 27, 44.66, 15.02, 0.01116, 2, 24, 47.72, -17.92, 0.85424, 27, 36.47, 3.25, 0.14576, 2, 24, 32.93, -27.92, 0.41005, 27, 21.67, -6.75, 0.58995, 4, 23, 76.91, -24.16, 0.00209, 24, 16.57, -34.56, 0.0144, 27, 5.32, -13.39, 0.97489, 26, 64.53, 10.56, 0.00862, 3, 23, 64.76, -33.01, 0.07514, 27, -9.45, -16.2, 0.81254, 26, 52.37, 1.71, 0.11232, 3, 23, 51.45, -38.08, 0.16331, 27, -23.65, -15.09, 0.47064, 26, 39.06, -3.36, 0.36605, 3, 23, 39.05, -52.65, 0.06725, 27, -41.08, -22.96, 0.1746, 26, 26.67, -17.92, 0.75815, 4, 23, 27.23, -59.58, 0.01217, 27, -54.73, -24.18, 0.08061, 26, 14.85, -24.86, 0.90478, 25, 64.46, -0.69, 0.00245, 3, 27, -68.28, -18.88, 0.021, 26, 0.34, -25.85, 0.92063, 25, 52.46, -8.92, 0.05837, 3, 27, -79.52, -9.1, 0.00048, 26, -14, -21.82, 0.69151, 25, 38.06, -12.73, 0.30801, 2, 26, -24.46, -27.29, 0.47719, 25, 31.83, -22.75, 0.52281, 2, 26, -36.32, -24.05, 0.33096, 25, 19.97, -25.99, 0.66904, 2, 26, -50.15, -13.81, 0.12229, 25, 2.86, -24.2, 0.87771, 3, 22, 16.09, -52.08, 0.00469, 26, -56.8, 0.46, 0.01193, 25, -10.12, -15.28, 0.98339, 2, 22, 4.69, -36.62, 0.2822, 25, -21.52, 0.19, 0.7178, 2, 22, -8.8, -19.59, 0.798, 25, -35.01, 17.21, 0.202, 2, 22, -14.44, -5.92, 0.96655, 25, -40.65, 30.88, 0.03345, 2, 22, 0.45, -4.02, 0.97329, 25, -25.76, 32.78, 0.02671, 3, 22, 19.25, -3.78, 0.96496, 26, -29.54, 40.46, 0.00035, 25, -6.96, 33.02, 0.03469, 4, 22, 38.89, -0.87, 0.16052, 23, 1.24, -1.74, 0.82004, 26, -11.14, 32.98, 0.01231, 25, 12.68, 35.93, 0.00712, 3, 23, 23.35, -3.96, 0.95413, 27, -34.47, 27.77, 0.00454, 26, 10.96, 30.77, 0.04133, 3, 23, 44.11, -2.3, 0.9681, 27, -14.99, 20.4, 0.02395, 26, 31.73, 32.43, 0.00795, 2, 24, 18.65, -2.77, 0.9467, 27, 7.4, 18.4, 0.0533, 2, 24, 40.92, -1.51, 0.99268, 27, 29.67, 19.66, 0.00732, 2, 24, 52.93, 0.67, 0.9999, 27, 41.68, 21.84, 0.0001, 2, 24, 43.08, -9.03, 0.92391, 27, 31.83, 12.14, 0.07609, 2, 24, 26.62, -17.22, 0.49772, 27, 15.37, 3.96, 0.50228, 4, 23, 62.74, -15.27, 0.03751, 24, 7.56, -20.46, 0.0548, 27, -3.69, 0.71, 0.88846, 26, 50.36, 19.45, 0.01924, 4, 23, 44.01, -23.33, 0.35043, 24, -12.82, -19.75, 0.02556, 27, -24.07, 1.42, 0.35632, 26, 31.63, 11.39, 0.2677, 3, 23, 28.89, -28.29, 0.29743, 27, -39.86, 3.4, 0.12578, 26, 16.51, 6.43, 0.57679, 4, 22, 62.46, -23.51, 0.00566, 23, 10.03, -33.22, 0.0232, 26, -2.35, 1.51, 0.9479, 25, 36.25, 13.29, 0.02324, 4, 22, 46.52, -30.33, 0.06857, 23, -7.16, -30.99, 0.0465, 26, -19.54, 3.74, 0.46606, 25, 20.31, 6.47, 0.41888, 4, 22, 27.59, -32.54, 0.09329, 23, -24.59, -23.27, 0.00357, 26, -36.97, 11.45, 0.0309, 25, 1.38, 4.26, 0.87224, 2, 22, 4.15, -17.31, 0.73064, 25, -22.07, 19.49, 0.26936, 4, 22, 41.33, -47.97, 0.00044, 23, -20.59, -43.55, 0, 26, -32.97, -8.82, 0.28074, 25, 15.12, -11.17, 0.71882, 4, 23, 23.17, -46.42, 0.02481, 27, -52.78, -10.54, 0.07226, 26, 10.78, -11.69, 0.90108, 25, 54.27, 8.59, 0.00185], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 122, "height": 152}}, "yc3": {"yc3": {"type": "mesh", "uvs": [1, 0.79887, 0.98962, 0.70311, 0.88326, 0.61737, 0.78028, 0.53943, 0.70263, 0.41361, 0.67899, 0.27888, 0.68068, 0.13858, 0.74314, 0.00051, 0.67224, 0, 0.49161, 0.02166, 0.34474, 0.08847, 0.24851, 0.17087, 0.21475, 0.27776, 0.23669, 0.35459, 0.12021, 0.39802, 0.04255, 0.48041, 0.03411, 0.58954, 0.08138, 0.65634, 0.1354, 0.69197, 0.07969, 0.7599, 0.10502, 0.85677, 0.21475, 0.9336, 0.38187, 0.95141, 0.56926, 0.94919, 0.75158, 0.94139, 0.90014, 0.88683, 0.53849, 0.10766, 0.52646, 0.22009, 0.51443, 0.33119, 0.52445, 0.44759, 0.58461, 0.56134, 0.67486, 0.68435, 0.79718, 0.77561, 0.59865, 0.82984, 0.46429, 0.71609, 0.39611, 0.61293, 0.30587, 0.4542, 0.32592, 0.32987, 0.36603, 0.19231, 0.43221, 0.13015, 0.16349, 0.4833, 0.18555, 0.60234, 0.28582, 0.70419, 0.23368, 0.80471, 0.38809, 0.85762], "triangles": [34, 35, 30, 42, 35, 34, 43, 18, 42, 19, 18, 43, 33, 34, 31, 20, 19, 43, 44, 42, 34, 44, 34, 33, 43, 42, 44, 21, 20, 43, 21, 43, 44, 23, 44, 33, 23, 33, 24, 22, 21, 44, 22, 44, 23, 36, 13, 37, 14, 13, 36, 40, 14, 36, 15, 14, 40, 16, 15, 40, 41, 40, 36, 16, 40, 41, 29, 36, 37, 35, 36, 29, 41, 36, 35, 17, 16, 41, 18, 17, 41, 42, 41, 35, 18, 41, 42, 38, 10, 39, 11, 10, 38, 38, 39, 27, 12, 11, 38, 37, 12, 38, 37, 38, 27, 13, 12, 37, 26, 9, 8, 39, 10, 9, 26, 39, 9, 6, 8, 7, 26, 8, 6, 27, 39, 26, 27, 26, 6, 5, 27, 6, 28, 37, 27, 28, 27, 5, 28, 5, 4, 29, 28, 4, 30, 29, 4, 30, 4, 3, 29, 37, 28, 35, 29, 30, 31, 30, 3, 31, 3, 2, 34, 30, 31, 32, 31, 2, 32, 2, 1, 32, 1, 0, 33, 31, 32, 25, 32, 0, 24, 33, 32, 24, 32, 25], "vertices": [2, 28, -11.36, -6.83, 0.99683, 31, -32.56, -37.6, 0.00317, 1, 28, 0.23, -13.81, 1, 2, 28, 15.81, -12.69, 0.99986, 29, -12.69, -18.34, 0.00014, 2, 28, 30.31, -11.18, 0.62598, 29, 0.35, -11.82, 0.37402, 2, 29, 19.33, -9.27, 0.99973, 30, -12.82, -14.3, 0.00027, 2, 29, 38.28, -11.89, 0.262, 30, 5.92, -10.53, 0.738, 1, 30, 25.65, -9.04, 1, 1, 30, 45.53, -13.21, 1, 1, 30, 45.06, -6.64, 1, 2, 30, 40.62, 9.85, 0.877, 33, 26.61, -6.71, 0.123, 2, 30, 30.1, 22.68, 0.28105, 33, 16.08, 6.12, 0.71895, 4, 29, 63.04, 23.06, 0.0025, 30, 17.78, 30.63, 0.0019, 33, 3.76, 14.07, 0.97398, 32, 46.87, -6.48, 0.02163, 3, 29, 49.23, 29.87, 0.06278, 33, -11.52, 15.95, 0.74305, 32, 33.07, 0.33, 0.19417, 4, 29, 38.23, 30.6, 0.10293, 30, -8.13, 29.58, 0.00175, 33, -22.14, 13.02, 0.35757, 32, 22.07, 1.07, 0.53775, 4, 29, 35.01, 42.63, 0.01156, 33, -29.14, 23.31, 0.10261, 32, 18.85, 13.09, 0.88513, 31, 60.77, -3.25, 0.00069, 3, 33, -41.32, 29.54, 0.02203, 32, 9.41, 22.99, 0.94099, 31, 55.42, 9.35, 0.03698, 3, 33, -56.72, 29.04, 0.00026, 32, -5.29, 27.59, 0.8298, 31, 43.3, 18.85, 0.16994, 2, 32, -15.51, 25.7, 0.68065, 31, 33.06, 20.68, 0.31935, 2, 32, -21.63, 22.09, 0.48183, 31, 26.06, 19.46, 0.51817, 2, 32, -29.61, 29.5, 0.26046, 31, 21.22, 29.21, 0.73954, 2, 32, -43.42, 30.64, 0.14101, 31, 8.69, 35.15, 0.85899, 2, 32, -56.47, 23.47, 0.0595, 31, -6.04, 33.04, 0.9405, 3, 28, 4.15, 52.56, 0.01754, 32, -62.79, 9.05, 0.00876, 31, -17.04, 21.78, 0.9737, 2, 28, -5.63, 38.13, 0.22552, 31, -26.82, 7.35, 0.77448, 2, 28, -14.49, 23.63, 0.58739, 31, -35.68, -7.14, 0.41261, 2, 28, -16.16, 7.91, 0.87055, 31, -37.35, -22.87, 0.12945, 2, 30, 28.9, 4.5, 0.908, 33, 14.89, -12.06, 0.092, 3, 30, 13.01, 4.3, 0.83197, 33, -1.01, -12.26, 0.16764, 32, 33.69, -29.77, 0.00039, 4, 29, 34.96, 4.77, 0.65751, 30, -2.7, 4.11, 0.21533, 33, -16.71, -12.45, 0.08137, 32, 18.8, -24.77, 0.04579, 4, 29, 18.84, 7.98, 0.82827, 33, -32.99, -14.74, 0.0137, 32, 2.68, -21.56, 0.15628, 31, 33.4, -29.96, 0.00174, 4, 28, 38.26, 5.47, 0.18187, 29, 1.91, 6.57, 0.6806, 32, -14.25, -22.97, 0.0822, 31, 17.07, -25.3, 0.05534, 3, 28, 19.25, 8.6, 0.82714, 32, -33.15, -26.75, 0.00496, 31, -1.94, -22.18, 0.1679, 2, 28, 2.18, 6.71, 0.90393, 31, -19.02, -24.07, 0.09607, 2, 28, 6.56, 26.21, 0.31672, 31, -14.64, -4.57, 0.68328, 4, 28, 26.86, 27.19, 0.09703, 29, -16.42, 22.86, 0.02139, 32, -32.58, -6.67, 0.08358, 31, 5.67, -3.59, 0.798, 4, 28, 42.41, 24, 0.07088, 29, -0.74, 25.36, 0.12647, 32, -16.91, -4.17, 0.46651, 31, 21.22, -6.78, 0.33614, 3, 29, 23.02, 27.89, 0.09513, 33, -35.61, 5.44, 0.07303, 32, 6.86, -1.65, 0.83184, 4, 29, 39.53, 21.7, 0.17638, 30, -3.97, 21.6, 0.03225, 33, -17.98, 5.04, 0.47896, 32, 23.37, -7.84, 0.31241, 3, 29, 57.38, 13.23, 9e-05, 33, 1.66, 2.93, 0.99542, 32, 41.21, -16.31, 0.00449, 2, 30, 24.92, 14.09, 0.41836, 33, 10.9, -2.47, 0.58164, 3, 33, -40.79, 18.3, 0.03272, 32, 6.2, 12.2, 0.94508, 31, 48.62, 0.38, 0.0222, 4, 28, 54.9, 39.15, 0.00017, 29, 5.6, 43.95, 0.00022, 32, -10.56, 14.41, 0.75353, 31, 33.71, 8.37, 0.24608, 4, 28, 37.79, 39.79, 0.0025, 29, -10.64, 38.52, 0.00577, 32, -26.8, 8.98, 0.33903, 31, 16.6, 9.02, 0.6527, 2, 32, -39.31, 17.22, 0.15342, 31, 7.8, 21.14, 0.84658, 3, 28, 14.63, 44.47, 0.00761, 32, -50.13, 5.18, 0.01952, 31, -6.56, 13.7, 0.97287], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 16, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 86, 88], "width": 93, "height": 141}}, "yc4": {"yc4": {"type": "mesh", "uvs": [0.47909, 1, 0.21205, 0.90587, 0.02894, 0.76221, 0, 0.56299, 0, 0.399, 0.07854, 0.26347, 0.2502, 0.13608, 0.38372, 0, 0.62405, 0.02901, 0.83768, 0.14286, 1, 0.2838, 1, 0.42882, 0.9712, 0.57925, 0.81861, 0.68496, 0.63168, 0.78389, 0.65457, 0.89231, 0.75757, 1, 0.62405, 1, 0.43318, 0.88782, 0.34474, 0.72575, 0.38664, 0.55211, 0.52163, 0.36524, 0.54491, 0.21475, 0.48439, 0.09237], "triangles": [21, 22, 10, 21, 5, 22, 22, 9, 10, 5, 6, 22, 6, 23, 22, 22, 23, 9, 23, 8, 9, 6, 7, 23, 23, 7, 8, 19, 3, 20, 13, 20, 12, 12, 20, 11, 3, 4, 20, 20, 21, 11, 20, 4, 21, 21, 10, 11, 4, 5, 21, 17, 15, 16, 15, 17, 18, 1, 18, 0, 17, 0, 18, 18, 1, 19, 18, 14, 15, 1, 2, 19, 18, 19, 14, 14, 19, 13, 2, 3, 19, 19, 20, 13], "vertices": [1, 13, -4.9, 0.45, 1, 1, 13, 3.05, 6.76, 1, 2, 13, 14.48, 10.38, 0.99371, 14, -11.06, 7.02, 0.00629, 3, 13, 29.61, 9.37, 0.14982, 14, 3.5, 11.24, 0.8465, 15, -16.79, 15.24, 0.00368, 2, 14, 15.63, 14.1, 0.786, 15, -4.32, 15.4, 0.214, 2, 14, 26.14, 14.39, 0.27064, 15, 6, 13.41, 0.72936, 2, 14, 36.63, 12.1, 0.00898, 15, 15.74, 8.9, 0.99102, 1, 15, 26.13, 5.43, 1, 1, 15, 24.01, -1.09, 1, 1, 15, 15.43, -6.97, 1, 2, 14, 30.34, -10.18, 0.14479, 15, 4.77, -11.49, 0.85521, 3, 13, 36.54, -18.65, 0.00991, 14, 19.61, -12.7, 0.82357, 15, -6.25, -11.63, 0.16651, 3, 13, 25.28, -16.53, 0.18737, 14, 8.3, -14.56, 0.81258, 15, -17.69, -10.99, 5e-05, 2, 13, 17.79, -11.49, 0.63813, 14, -0.46, -12.39, 0.36187, 2, 13, 10.92, -5.59, 0.99282, 14, -8.94, -9.2, 0.00718, 1, 13, 2.66, -5.23, 1, 1, 13, -5.79, -7.02, 1, 1, 13, -5.37, -3.44, 1, 1, 13, 3.71, 0.67, 1, 1, 13, 16.22, 1.58, 1, 2, 14, 6.7, 1.27, 0.99996, 15, -15.83, 4.82, 4e-05, 2, 14, 21.35, 0.98, 0.88842, 15, -1.58, 1.35, 0.11158, 2, 14, 32.63, 2.98, 0.00133, 15, 9.86, 0.87, 0.99867, 1, 15, 19.14, 2.62, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 27, "height": 76}}, "yc5": {"yc5": {"type": "mesh", "uvs": [0.52779, 0, 0.70416, 0.03228, 0.86288, 0.15167, 1, 0.29941, 0.99515, 0.46505, 0.88052, 0.59787, 0.63802, 0.72023, 0.52779, 0.81126, 0.52779, 0.91125, 0.48811, 1, 0.36025, 1, 0.14861, 0.959, 0.09129, 0.85006, 0.06043, 0.7068, 0.07807, 0.56354, 0.21916, 0.40237, 0.3382, 0.29194, 0.32938, 0.13824, 0.3867, 0.0293, 0.60777, 0.15603, 0.64905, 0.30448, 0.60261, 0.43372, 0.45812, 0.58043, 0.33428, 0.71141, 0.293, 0.80572, 0.31364, 0.90702], "triangles": [3, 21, 20, 21, 16, 20, 16, 19, 20, 20, 2, 3, 20, 19, 2, 16, 17, 19, 17, 18, 19, 19, 1, 2, 1, 19, 0, 19, 18, 0, 6, 22, 5, 22, 23, 14, 22, 21, 5, 5, 21, 4, 14, 15, 22, 22, 15, 21, 4, 21, 3, 15, 16, 21, 9, 10, 8, 11, 25, 10, 10, 25, 8, 11, 12, 25, 25, 7, 8, 12, 24, 25, 25, 24, 7, 12, 13, 24, 24, 23, 7, 7, 23, 6, 24, 13, 23, 23, 22, 6, 23, 13, 14], "vertices": [1, 18, 24.28, 0.25, 1, 1, 18, 21.9, -3.46, 1, 1, 18, 13.89, -6.35, 1, 2, 17, 26.25, -5.85, 0.07302, 18, 4.09, -8.63, 0.92698, 3, 16, 30.52, -15.04, 7e-05, 17, 16.26, -9.88, 0.89416, 18, -6.64, -7.71, 0.10577, 2, 16, 21.82, -12.76, 0.08477, 17, 7.32, -10.86, 0.91523, 2, 16, 13.72, -7.65, 0.74563, 17, -2.07, -8.99, 0.25437, 2, 16, 7.74, -5.4, 0.99469, 17, -8.46, -9.02, 0.00531, 1, 16, 1.24, -5.58, 1, 1, 16, -4.55, -4.87, 1, 1, 16, -4.63, -2.06, 1, 1, 16, -2.1, 2.67, 1, 1, 16, 4.94, 4.13, 1, 2, 16, 14.23, 5.07, 0.99584, 17, -6.14, 3.08, 0.00416, 2, 16, 23.55, 4.95, 0.05993, 17, 2.61, 6.3, 0.94007, 2, 17, 13.48, 7.45, 0.8463, 18, -1.28, 9, 0.1537, 2, 17, 21.11, 7.78, 0.13346, 18, 5.68, 5.85, 0.86654, 1, 18, 15.65, 5.29, 1, 1, 18, 22.62, 3.49, 1, 1, 18, 14.04, -0.73, 1, 1, 18, 4.35, -0.91, 1, 1, 17, 14.83, -1.12, 1, 2, 16, 22.69, -3.44, 0.02505, 17, 4.8, -1.85, 0.97495, 2, 16, 14.1, -0.96, 0.99132, 17, -4.1, -2.59, 0.00868, 2, 16, 7.95, -0.22, 1, 17, -10.11, -4.11, 0, 1, 16, 1.38, -0.86, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 22, "height": 65}}, "yc6": {"yc6": {"type": "mesh", "uvs": [0.59922, 0, 0.75388, 0.08306, 0.88988, 0.19721, 1, 0.34583, 0.99121, 0.50844, 0.86855, 0.62475, 0.63655, 0.71952, 0.39655, 0.79598, 0.38588, 0.91121, 0.25255, 0.9769, 0.08455, 0.98121, 0.05522, 0.86275, 0, 0.71952, 0.06855, 0.56552, 0.21522, 0.43521, 0.32988, 0.35337, 0.38588, 0.20367, 0.45788, 0.07121, 0.62004, 0.10451, 0.63085, 0.21803, 0.64167, 0.34684, 0.53896, 0.52803, 0.31192, 0.65684, 0.20381, 0.77909, 0.1984, 0.88606], "triangles": [20, 16, 19, 20, 2, 3, 20, 19, 2, 16, 18, 19, 2, 18, 1, 2, 19, 18, 16, 17, 18, 1, 18, 0, 18, 17, 0, 5, 6, 21, 22, 13, 21, 5, 21, 4, 13, 14, 21, 14, 15, 21, 21, 20, 4, 21, 15, 20, 4, 20, 3, 20, 15, 16, 10, 24, 9, 10, 11, 24, 9, 24, 8, 8, 24, 7, 24, 23, 7, 24, 11, 23, 11, 12, 23, 6, 7, 22, 7, 23, 22, 23, 12, 22, 12, 13, 22, 6, 22, 21], "vertices": [1, 21, 17.51, 1.11, 1, 1, 21, 13.03, -1.92, 1, 1, 21, 6.95, -4.46, 1, 2, 20, 17.79, -6.13, 0.49913, 21, -0.88, -6.37, 0.50087, 3, 19, 23.54, -10.57, 0.00966, 20, 9.92, -9.22, 0.9875, 21, -9.32, -5.75, 0.00284, 2, 19, 17.04, -9.57, 0.17489, 20, 3.35, -9.17, 0.82511, 2, 19, 11.06, -6.06, 0.77555, 20, -3.08, -6.58, 0.22445, 1, 19, 5.97, -2.16, 1, 1, 19, 0.11, -3.42, 1, 1, 19, -3.9, -1.55, 1, 1, 19, -4.98, 1.82, 1, 1, 19, 0.84, 3.93, 1, 2, 19, 7.77, 6.89, 0.99312, 20, -8.24, 5.75, 0.00688, 2, 19, 15.88, 7.47, 0.46642, 20, -0.29, 7.51, 0.53358, 3, 19, 23.21, 6.16, 0.00965, 20, 7.15, 7.29, 0.97696, 21, -4.67, 10.33, 0.01339, 2, 20, 12, 6.71, 0.77241, 21, -0.55, 7.7, 0.22759, 2, 20, 19.64, 8.62, 0.05705, 21, 7.16, 6.13, 0.94295, 1, 21, 13.96, 4.26, 1, 1, 21, 12.06, 0.95, 1, 2, 20, 20.93, 3.59, 0.00571, 21, 6.15, 1.03, 0.99429, 2, 20, 14.84, 0.8, 0.83999, 21, -0.55, 1.15, 0.16001, 2, 19, 20.21, -1.62, 0.00289, 20, 5.32, -0.85, 0.99711, 2, 19, 12.54, 1.35, 0.9875, 20, -2.7, 0.97, 0.0125, 1, 19, 5.82, 1.98, 1, 1, 19, 0.4, 0.72, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 21, "height": 52}}, "yc7": {"yc7": {"type": "mesh", "uvs": [0.24845, 0.97423, 0.18122, 0.83483, 0.085, 0.68122, 0.02009, 0.53666, 0, 0.38822, 0.00733, 0.24494, 0.06182, 0.14792, 0.12789, 0, 0.27628, 0, 0.48957, 0.07176, 0.65882, 0.16083, 0.83503, 0.26667, 0.88255, 0.50805, 0.83271, 0.7107, 0.73301, 0.8643, 0.59275, 0.97273, 0.47103, 1, 0.35394, 1, 0.34715, 0.8508, 0.37449, 0.67551, 0.35092, 0.48238, 0.31416, 0.27666, 0.21629, 0.11, 0.44447, 0.88156, 0.57416, 0.78528, 0.67384, 0.62214, 0.70747, 0.39616, 0.50211, 0.19023, 0.54054, 0.38546, 0.4925, 0.57534, 0.11182, 0.24105, 0.18027, 0.35337, 0.24271, 0.56063, 0.24392, 0.73313, 0.0902, 0.3868, 0.12983, 0.57267], "triangles": [5, 6, 30, 31, 30, 21, 34, 5, 30, 34, 30, 31, 4, 5, 34, 3, 4, 34, 35, 34, 31, 3, 34, 35, 35, 31, 32, 2, 3, 35, 33, 32, 19, 35, 32, 33, 2, 35, 33, 1, 2, 33, 18, 33, 19, 1, 33, 18, 27, 9, 10, 28, 27, 10, 26, 10, 11, 28, 10, 26, 26, 11, 12, 25, 28, 26, 25, 26, 12, 13, 25, 12, 29, 28, 25, 24, 29, 25, 14, 25, 13, 24, 25, 14, 24, 23, 19, 15, 24, 14, 23, 24, 15, 16, 23, 15, 22, 7, 8, 6, 7, 22, 30, 6, 22, 21, 8, 9, 22, 8, 21, 30, 22, 21, 21, 9, 27, 21, 27, 28, 20, 21, 28, 31, 21, 20, 32, 31, 20, 29, 20, 28, 32, 20, 19, 19, 20, 29, 19, 29, 24, 23, 18, 19, 0, 1, 18, 17, 18, 23, 0, 18, 17, 17, 23, 16], "vertices": [2, 5, 18.05, 2.53, 0.98731, 11, -28.15, 17.45, 0.01269, 2, 5, 28.2, 22.39, 0.32439, 11, -5.85, 16.82, 0.67561, 3, 5, 37.41, 47.14, 0.00027, 11, 20.46, 19.13, 0.91352, 12, -13.88, 17.39, 0.08621, 2, 11, 43.21, 17.82, 0.16665, 12, 8.83, 19.22, 0.83335, 1, 12, 29.42, 14.33, 1, 2, 8, 22.48, 39.2, 0.0351, 12, 47.73, 5.72, 0.9649, 2, 8, 30.96, 25.47, 0.46524, 12, 57.22, -7.33, 0.53476, 2, 8, 45.06, 6.94, 0.97048, 12, 72.69, -24.74, 0.02952, 3, 7, 85.49, -7.01, 0.0001, 8, 34.75, -13.95, 0.98897, 10, 55.55, 83.5, 0.01093, 4, 6, 107.9, -3.27, 6e-05, 7, 66.94, -36.67, 0.18243, 8, 10.85, -39.49, 0.53984, 10, 58.1, 48.61, 0.27766, 4, 6, 99.66, -31.47, 0.00093, 7, 47.86, -59.02, 0.12136, 8, -12.17, -57.75, 0.1161, 10, 55.9, 19.31, 0.76161, 1, 10, 51.88, -11.86, 1, 2, 9, 72.82, -19.67, 0.01235, 10, 22.78, -31.02, 0.98765, 2, 9, 46.82, -33.87, 0.46034, 10, -6.7, -33.95, 0.53966, 3, 5, 74.09, -51.2, 0.03002, 9, 20.37, -37.64, 0.89554, 10, -32.53, -27.1, 0.07444, 2, 5, 48.99, -41.79, 0.22449, 9, -5.92, -32.4, 0.77551, 2, 5, 34.95, -28.27, 0.53475, 9, -21.95, -21.31, 0.46525, 2, 5, 24.49, -13.16, 0.88829, 9, -34.71, -8.08, 0.11171, 2, 5, 41.17, -0.31, 0.99703, 9, -20.31, 7.29, 0.00297, 3, 6, 21, 1.25, 0.95481, 7, -10.46, 3.09, 0.02747, 11, -1.47, -20.68, 0.01773, 4, 6, 47.32, 9.17, 0.00301, 7, 16.79, -0.48, 0.99586, 9, 17.49, 42.92, 0.00031, 10, -3.75, 48.2, 0.00081, 4, 6, 75.06, 19.41, 0.00034, 7, 46.29, -2.52, 0.94026, 8, -2.86, -2.01, 0.0518, 10, 21.27, 63.98, 0.0076, 2, 8, 25.01, 1.36, 0.99064, 12, 53.12, -31.82, 0.00936, 3, 5, 46.3, -15.34, 0.48172, 6, -5.97, -14.15, 0.03261, 9, -12.83, -6.72, 0.48568, 2, 5, 69.05, -24.35, 0.03575, 9, 11.08, -11.96, 0.96425, 2, 9, 38.5, -7.25, 0.62847, 10, -3.98, -6.19, 0.37153, 5, 6, 68.09, -44.21, 0.00011, 7, 13.84, -57.68, 0.00046, 8, -45.3, -49.91, 8e-05, 9, 65.09, 11.06, 1e-05, 10, 27.66, 0.29, 0.99934, 4, 6, 91.71, -7.82, 0.00626, 7, 50.31, -34.19, 0.28726, 8, -5, -33.86, 0.33349, 10, 43.21, 40.79, 0.37299, 5, 6, 65.47, -18.09, 0.08946, 7, 22.17, -32.79, 0.31871, 8, -32.35, -27.08, 0.02914, 9, 47.99, 30.97, 0.03819, 10, 19.68, 25.3, 0.52449, 4, 6, 37.85, -14.84, 0.47863, 7, -1.69, -18.5, 0.0625, 9, 23.48, 17.82, 0.37549, 10, -8.02, 22.75, 0.08339, 3, 7, 59.46, 26.82, 0.0228, 8, 15.71, 24.25, 0.26057, 12, 42.11, -9.7, 0.71663, 3, 7, 41.36, 20.6, 0.27945, 8, -3.25, 21.63, 0.0819, 12, 23.41, -13.76, 0.63865, 3, 7, 10.59, 18.8, 0.36129, 11, 22.87, -10.8, 0.57857, 12, -7.36, -11.93, 0.06015, 2, 5, 45.59, 22.46, 0.00386, 11, 1.69, 1.14, 0.99614, 1, 12, 24.32, 1.12, 1, 2, 11, 30.23, 5.41, 0.6907, 12, -2.32, 5.14, 0.3093], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 60, 62, 62, 64, 64, 66, 68, 70], "width": 157, "height": 141}}, "z1": {"z1": {"x": -2.58, "y": -12.34, "rotation": -0.47, "width": 75, "height": 31}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"eye": {"attachment": [{"time": 0.4, "name": null}]}}, "bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 13.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 47.11, "y": 31.62, "curve": 0.25, "c3": 0.759, "c4": 1.8}, {"time": 0.4667, "x": -26.4, "y": 11.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 19.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.36, "curve": 0.25, "c3": 0.736, "c4": 1.35}, {"time": 0.4667, "angle": 26.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 40.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 56.62, "y": 39.36, "curve": 0.25, "c3": 0.736, "c4": 1.35}, {"time": 0.4667, "x": -34.18, "y": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 13.05, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 19.1, "y": 3.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone3": {"rotate": [{"angle": 0.35, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.5, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.6333, "angle": 0.35}]}, "bone4": {"rotate": [{"angle": 1.01, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.5, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.6333, "angle": 1.01}]}, "bone5": {"rotate": [{"angle": 2.68, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.5, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6333, "angle": 2.68}]}, "bone6": {"rotate": [{"angle": 4.15, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 4.5, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.6333, "angle": 4.15}]}, "bone7": {"rotate": [{"angle": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 4.5}]}, "bone8": {"rotate": [{"angle": 1.01, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.5, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.6333, "angle": 1.01}]}, "bone9": {"rotate": [{"angle": 2.68, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.5, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6333, "angle": 2.68}]}, "bone10": {"rotate": [{"angle": 1.01, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.5, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.6333, "angle": 1.01}]}, "bone11": {"rotate": [{"angle": 2.68, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.5, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6333, "angle": 2.68}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -13.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone22": {"rotate": [{"angle": 1.63, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.29, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.6333, "angle": 1.63}]}, "bone23": {"rotate": [{"angle": 4.33, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -13.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 7.29, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6333, "angle": 4.33}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 4, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -13.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 7.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone28": {"rotate": [{"angle": 3.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 7.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6333, "angle": 3.82}]}, "bone29": {"rotate": [{"angle": 5.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -13.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 7.64, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "angle": 5.47}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 4.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -10.19, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone34": {"rotate": [{"angle": 4.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -10.19, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6333, "angle": 4.38}]}, "bone35": {"rotate": [{"angle": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -10.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.75}]}}, "events": [{"time": 0.3667, "name": "atk"}]}, "boss_idle": {"slots": {"eye": {"attachment": [{"time": 1, "name": null}, {"time": 1.1, "name": "eye"}]}}, "bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.99, "y": 15.9, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone2": {"translate": [{"x": -5.94, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -20.93, "y": 0.28, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -5.94, "y": 0.08}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": 1.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.49, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.56}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone6": {"rotate": [{"angle": 3.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.94}]}, "bone7": {"rotate": [{"angle": 3.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.94}]}, "bone8": {"rotate": [{"angle": 3.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.94}]}, "bone9": {"rotate": [{"angle": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.49}]}, "bone10": {"rotate": [{"angle": 3.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.94}]}, "bone11": {"rotate": [{"angle": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.49}]}, "bone12": {"rotate": [{"angle": -8.62, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 15.15, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -8.62}]}, "bone13": {"rotate": [{"angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -8.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 15.15, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.87}]}, "bone14": {"rotate": [{"angle": 15.15, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.62, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 15.15}]}, "bone15": {"rotate": [{"angle": -6.97, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -8.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1333, "angle": 15.15, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": -6.97}]}, "bone16": {"rotate": [{"angle": 2.19, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1333, "angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": -8.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4667, "angle": 15.15, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 2.19}]}, "bone17": {"rotate": [{"angle": 13.5, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 15.15, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -8.62, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 13.5}]}, "bone18": {"rotate": [{"angle": -2.86, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": -8.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3, "angle": 15.15, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -2.86}]}, "bone19": {"rotate": [{"angle": 7.39, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.3, "angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "angle": -8.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6333, "angle": 15.15, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 7.39}]}, "bone20": {"rotate": [{"angle": 9.39, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 15.15, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -8.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 9.39}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.63, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone22": {"rotate": [{"angle": 3.02, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.02}]}, "bone23": {"rotate": [{"angle": 7.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.62}]}, "bone24": {"translate": [{"x": 1.3, "y": -2.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.59, "y": -7.51, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.3, "y": -2.13}]}, "bone25": {"translate": [{"x": 3.29, "y": -5.38, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.59, "y": -7.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 3.29, "y": -5.38}]}, "bone26": {"translate": [{"x": 4.59, "y": -7.51, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.59, "y": -7.51}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.98, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone28": {"rotate": [{"angle": -1.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.98}]}, "bone29": {"rotate": [{"angle": -5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.98, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5}]}, "bone30": {"translate": [{"x": -0.11, "y": 1.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.37, "y": 4.84, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.11, "y": 1.37}]}, "bone31": {"translate": [{"x": -0.27, "y": 3.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.37, "y": 4.84, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.27, "y": 3.47}]}, "bone32": {"translate": [{"x": -0.37, "y": 4.84, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -0.37, "y": 4.84}]}, "bone33": {"rotate": [{"angle": -2.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -8.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -2.08}]}, "bone34": {"rotate": [{"angle": -5.78, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.3, "angle": -2.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -8.58, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": -5.78}]}, "bone35": {"rotate": [{"angle": -8.53, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 0.3, "angle": -6.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -8.58, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": -8.53}]}, "bone37": {"rotate": [{"angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 11.07, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.2}]}, "bone38": {"rotate": [{"angle": -2.02, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 11.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.02}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.62, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.62, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone41": {"rotate": [{"angle": -0.33, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -1.62, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -0.33}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.62, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone43": {"rotate": [{"angle": -0.95, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -1.62, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": -0.95}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.62, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone45": {"rotate": [{"angle": -1.46, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.62, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -1.46}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.62, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone47": {"rotate": [{"angle": -1.55, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -1.62, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -1.55}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.62, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone49": {"rotate": [{"angle": -1.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -1.62, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -1.02}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.62, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.99, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone53": {"rotate": [{"angle": -2.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.99, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.55}]}, "bone54": {"rotate": [{"angle": -6.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -8.99, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -6.81}]}, "bone55": {"translate": [{"x": -4.93, "y": 7.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -6.88, "y": 9.96, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -4.93, "y": 7.13}]}, "bone56": {"translate": [{"x": -1.95, "y": 2.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -6.88, "y": 9.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.95, "y": 2.83}]}, "bone57": {"translate": [{"x": -58.01, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "x": -106.62, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": -58.01}]}, "bone58": {"translate": [{"x": -13.19, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": -17.41, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "x": -13.19}]}, "bone59": {"translate": [{"x": -7.02, "y": 5.29, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -21.58, "y": 16.26, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "x": -7.02, "y": 5.29}]}, "bone60": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -73.42, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone61": {"translate": [{"x": 2.6, "y": -0.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 9.15, "y": -0.89, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 2.6, "y": -0.25}], "scale": [{"x": 1.032, "y": 1.032, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.111, "y": 1.111, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.032, "y": 1.032}]}, "bone62": {"translate": [{"x": 7.16, "y": -12.07, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 7.24, "y": -12.21, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "x": 7.16, "y": -12.07}], "scale": [{"x": 1.11, "y": 1.11, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 1.111, "y": 1.111, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "x": 1.11, "y": 1.11}]}, "bone63": {"translate": [{"x": 9.56, "y": 2.89, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 13.35, "y": 4.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 9.56, "y": 2.89}], "scale": [{"x": 1.08, "y": 1.08, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.111, "y": 1.111, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.08, "y": 1.08}]}, "bone64": {"translate": [{"x": -88.5, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": -162.67, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": -88.5}]}, "bone65": {"translate": [{"x": -1.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -6.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.83}]}, "bone66": {"translate": [{"x": -4.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -6.45, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -4.62}]}, "bone67": {"rotate": [{"angle": 1.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5, "angle": 11.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.93}]}, "bone68": {"rotate": [{"angle": 9.8, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8667, "angle": 11.07, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 9.8}]}, "bone69": {"rotate": [{"angle": 1.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5, "angle": 11.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.93}]}, "bone70": {"rotate": [{"angle": 9.8, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8667, "angle": 11.07, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 9.8}]}, "bone72": {"translate": [{"x": -3.48, "y": 1.33, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": -14.37, "y": 5.51, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "x": -3.48, "y": 1.33}]}, "bone73": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 10.23, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone74": {"translate": [{"x": 2.01, "y": 5.53, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "x": 6.19, "y": 16.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "x": 2.01, "y": 5.53}]}, "bone75": {"rotate": [{"angle": 9.8, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8667, "angle": 11.07, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 9.8}]}}}, "die": {"slots": {"eye": {"attachment": [{"time": 0.1333, "name": null}]}, "hb1": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "hb2": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "hb3": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "hy1": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "hy2": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}}, "bones": {"bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 21.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.53}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -35.45, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.2667, "x": 80.36, "y": 104.01, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.4667, "x": 29.37, "y": -49.36, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.5, "x": 29.37, "y": -16.23, "curve": 0.307, "c2": 0.25, "c3": 0.67, "c4": 0.68}, {"time": 0.5667, "x": 29.37, "y": -49.36}]}, "bone3": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -21.58}]}, "bone4": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -21.58}]}, "bone5": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -21.58}]}, "bone6": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -21.58}]}, "bone7": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -21.58}]}, "bone21": {"rotate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -41.14}]}, "bone22": {"rotate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -41.14}]}, "bone23": {"rotate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -41.14}]}, "bone27": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 43.89}]}, "bone28": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 43.89}]}, "bone29": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 43.89}]}}}, "hurt": {"slots": {"eye": {"attachment": [{"time": 0.1, "name": null}]}}, "bones": {"bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 18.99, "curve": 0.25, "c3": 0.694, "c4": 1.38}, {"time": 0.2667, "angle": -12.46, "curve": 0.25, "c3": 0.694, "c4": 1.38}, {"time": 0.5333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -11.24, "curve": 0.25, "c3": 0.592, "c4": 1.97}, {"time": 0.2667, "x": 90.37}, {"time": 0.5333}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -14.28, "y": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 13.45, "y": 5.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.96, "y": 0.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone4": {"rotate": [{"angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.22, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -1.15}]}, "bone5": {"rotate": [{"angle": -3.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -3.11}]}, "bone6": {"rotate": [{"angle": -5.07, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -6.22, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": -5.07}]}, "bone7": {"rotate": [{"angle": -6.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.22}]}, "bone8": {"rotate": [{"angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.22, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -1.15}]}, "bone9": {"rotate": [{"angle": -3.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -3.11}]}, "bone10": {"rotate": [{"angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.22, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -1.15}]}, "bone11": {"rotate": [{"angle": -3.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -3.11}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone13": {"rotate": [{"angle": -3.03, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.02, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": -3.03}]}, "bone14": {"rotate": [{"angle": -7.36, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -9.02, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": -7.36}]}, "bone15": {"rotate": [{"angle": -1.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -1.66}]}, "bone16": {"rotate": [{"angle": -5.98, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.0667, "angle": -3.03, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.02, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5333, "angle": -5.98}]}, "bone17": {"rotate": [{"angle": -9.02, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": -7.36, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9.02}]}, "bone18": {"rotate": [{"angle": -3.03, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.02, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": -3.03}]}, "bone19": {"rotate": [{"angle": -7.36, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -3.03, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -9.02, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": -7.36}]}, "bone20": {"rotate": [{"angle": -7.69, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -9.02, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -7.36, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.22, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5333, "angle": -7.69}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 14.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone22": {"rotate": [{"angle": -2.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.96, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -2.21}]}, "bone23": {"rotate": [{"angle": -5.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.96, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -5.98}]}, "bone24": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 8.35, "y": -9.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone25": {"translate": [{"x": 1.54, "y": -1.78, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 8.35, "y": -9.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "x": 1.54, "y": -1.78}]}, "bone26": {"translate": [{"x": 7.85, "y": -9.09, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.1333, "x": 2.81, "y": -3.25, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 8.35, "y": -9.67, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.5333, "x": 7.85, "y": -9.09}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.33, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone28": {"rotate": [{"angle": -2.13, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -6.33, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": -2.13}]}, "bone29": {"rotate": [{"angle": -4.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.33, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5333, "angle": -4.21}]}, "bone30": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 8.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone31": {"translate": [{"y": 1.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 8.97, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "y": 1.65}]}, "bone32": {"translate": [{"y": 4.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": 8.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "y": 4.49}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 14.21, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone34": {"rotate": [{"angle": -2.11, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 14.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -6.27, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": -2.11}]}, "bone35": {"rotate": [{"angle": -5.11, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 14.21, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -6.27, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": -5.11}]}, "bone51": {"translate": [{"curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.2333, "x": 44.46, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.5333}]}, "bone52": {"translate": [{"curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.2333, "x": 72.42, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.5333}]}}}}}