import { WindowPackMessage } from "../../game/net/protocol/Activity";

export class HdTiaoJianLiBaoData {
  private _windowPackMessage: WindowPackMessage;

  // @persistent
  private _tiaojianlibao_activity_info: any = null;

  public init() {
    this._windowPackMessage = null;
  }
  /**
   * 弹窗礼包
   */
  public get windowPackMessage(): WindowPackMessage {
    return this._windowPackMessage;
  }
  public set windowPackMessage(value: WindowPackMessage) {
    this._windowPackMessage = value;
  }

  /**
   * @description
   * 10601 (hd_tiaojianlibao)
   */
  public get tiaojianlibao_activity_info() {
    return this._tiaojianlibao_activity_info;
  }
  public set tiaojianlibao_activity_info(value) {
    this._tiaojianlibao_activity_info = value;
  }
}
