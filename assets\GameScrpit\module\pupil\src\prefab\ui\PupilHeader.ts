import { _decorator, Component, Sprite } from "cc";

import { Sprite<PERSON>rame } from "cc";
import { AssetMgr, BundleEnum } from "../../../../../../platform/src/ResHelper";
import { PupilModule } from "../../PupilModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("PupilHeader")
export class PupilHeader extends Component {
  @property(Sprite)
  private spBgHeader: Sprite = null;

  _assetMgr: AssetMgr;

  get assetMgr() {
    if (this._assetMgr == null) {
      this._assetMgr = AssetMgr.create();
    }
    return this._assetMgr;
  }

  onLoad() {}

  start() {}

  onDestroy() {
    log.log("PupilHeader onDestroy");
    this.assetMgr.release();
  }

  setHeaderByNameId(nameId: number) {
    let configPupilName = PupilModule.data.getConfigPupilName(nameId);
    let configPulilSkin = PupilModule.data.getConfigPulilSkin(configPupilName.skinId);
    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_G_PUPIL,
      `images/${configPulilSkin.headId}`,
      (spine: SpriteFrame) => {
        if (this.isValid == false) {
          return;
        }

        this.spBgHeader.spriteFrame = spine;
        this.node.active = true;
      }
    );
  }
}
