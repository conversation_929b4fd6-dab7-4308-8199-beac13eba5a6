export interface IConfigCopyMain {
  id: number;
  des: string;
  buildId: number;
  cost: number;
  costTime: number;
  reward1List: Array<Array<number>>;
  reward2: number;
  show: number;
  power: number;
  inspireCost1: number;
  eventIdList: Array<Array<number>>;
  reward2RateList: Array<number>;
  costTimeList: Array<number>;
  inspireRateList: Array<number>;
  inspireMax1: number;
  inspireAdd1: number;
  inspireCost2List: Array<number>;
  inspireMax2: number;
  inspireAdd2: number;
  lossRateList: Array<number>;
  index: number;

  /**
   * 初始气运消耗固定值
   */
  costNum: number;

  powerList: number[][];
}
