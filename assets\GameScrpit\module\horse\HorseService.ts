import { JsonMgr } from "../../game/mgr/JsonMgr";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import { PlayerModule } from "../player/PlayerModule";
import { HorseModule } from "./HorseModule";
import { GameDirector } from "../../game/GameDirector";
import { SystemOpenEnum } from "../../game/GameDefine";
import MsgEnum from "../../game/event/MsgEnum";

export class HorseService {
  public init() {
    MsgMgr.on(MsgEnum.ON_HORSE_UPDATE, this.updatePopover, this);
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.updatePopover, this);
    this.updatePopover();
  }

  private updatePopover() {
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.HOURSE_至宝系统)) {
      return;
    }

    let _horse = HorseModule.data.horseMessage;
    if (!_horse) {
      return;
    }

    let configHorseLv = JsonMgr.instance.jsonList.c_horseLv[_horse.stage];
    let showPopover = true;
    if (_horse.grade < configHorseLv.lvNeed) {
      for (let i = 0; i < configHorseLv.lvCostList.length; i++) {
        let item = configHorseLv.lvCostList[i];
        let info = JsonMgr.instance.getConfigItem(item[0]);
        if (info.goodsType == 1) {
          if (PlayerModule.data.getItemNum(item[0]) < item[1]) {
            showPopover = false;
            break;
          }
        } else {
          if (PlayerModule.data.getItemNum(item[0]) < item[1]) {
            showPopover = false;
            break;
          }
        }
      }
    } else {
      for (let i = 0; i < configHorseLv.breakCostList.length; i++) {
        let item = configHorseLv.breakCostList[i];
        let info = JsonMgr.instance.getConfigItem(item[0]);
        if (info.goodsType == 1) {
          if (PlayerModule.data.getItemNum(item[0]) < item[1]) {
            showPopover = false;
            break;
          }
        } else {
          if (PlayerModule.data.getItemNum(item[0]) < item[1]) {
            showPopover = false;
            break;
          }
        }
      }
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_horse.btn_upgrade.id, showPopover);
  }
}
