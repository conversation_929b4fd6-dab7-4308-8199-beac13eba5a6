{"skeleton": {"hash": "RHHN2kmUrylyM9TcOZgA79/9+Xo=", "spine": "3.8.75", "x": -306.84, "y": -17.02, "width": 714.16, "height": 431.39, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/太乙"}, "bones": [{"name": "root"}, {"name": "bone101", "parent": "root", "y": -70.79}, {"name": "bone", "parent": "bone101", "x": -8.44, "y": -52.58, "color": "003affff"}, {"name": "bone2", "parent": "bone", "x": 11.28, "y": 219.05, "color": "fc0000ff"}, {"name": "bone3", "parent": "bone2", "length": 69.94, "rotation": 3.04, "color": "003affff"}, {"name": "bone4", "parent": "bone3", "length": 52.4, "rotation": 88.69, "x": 0.11, "y": 0.15}, {"name": "bone5", "parent": "bone4", "length": 110.61, "rotation": 2.81, "x": 52.4}, {"name": "bone6", "parent": "bone5", "length": 40.95, "rotation": -0.97, "x": 111.8, "y": -0.22}, {"name": "bone50", "parent": "bone6", "x": 12.84, "y": -15.49, "color": "abe323ff"}, {"name": "bone51", "parent": "bone6", "x": 58.68, "y": -13.9, "color": "abe323ff"}, {"name": "bone11", "parent": "bone5", "length": 72.22, "rotation": -128.94, "x": 91.83, "y": -59.35}, {"name": "bone12", "parent": "bone11", "length": 43.52, "rotation": 151.25, "x": 87.83, "y": -4.1}, {"name": "bone13", "parent": "bone12", "length": 26.59, "rotation": -8.65, "x": 43.9, "y": -0.31}, {"name": "bone14", "parent": "bone5", "length": 70.55, "rotation": 163.47, "x": 96.51, "y": 89.81}, {"name": "bone15", "parent": "bone14", "length": 40.23, "rotation": 95.69, "x": 69.92, "y": -0.34}, {"name": "bone16", "parent": "bone15", "length": 25.36, "rotation": 10.12, "x": 39.52, "y": 0.77, "transform": "noRotationOrReflection"}, {"name": "bone48", "parent": "bone5", "x": 7.05, "y": -13.31, "color": "abe323ff"}, {"name": "bone49", "parent": "bone5", "x": 67.47, "y": -12.84, "color": "abe323ff"}, {"name": "bone31", "parent": "bone3", "length": 32.08, "rotation": -92.38, "x": -0.22, "y": -1.21, "color": "fffc00ff"}, {"name": "bone32", "parent": "bone31", "x": 12.75, "y": -58.08}, {"name": "bone33", "parent": "bone32", "length": 39.8, "rotation": -10.92}, {"name": "bone34", "parent": "bone33", "length": 36.37, "rotation": 0.21, "x": 39.8}, {"name": "bone36", "parent": "bone32", "length": 43.59, "rotation": 12.14, "x": 0.18, "y": -0.34}, {"name": "bone37", "parent": "bone36", "length": 39.82, "rotation": -47.37, "x": 43.59, "y": 0.43, "color": "abe323ff"}, {"name": "bone38", "parent": "bone31", "x": 11.06, "y": 39.65}, {"name": "bone39", "parent": "bone38", "length": 34.38, "rotation": 4.01}, {"name": "bone40", "parent": "bone39", "length": 34.26, "rotation": -1.91, "x": 33.52, "y": -0.1}, {"name": "bone42", "parent": "bone38", "length": 39.81, "rotation": 30.77}, {"name": "bone43", "parent": "bone42", "length": 37.19, "rotation": -58.31, "x": 39.44, "y": -0.6, "color": "abe323ff"}, {"name": "bone44", "parent": "bone", "length": 23.91, "rotation": 1.59, "x": -59.46, "y": 113.68}, {"name": "bone45", "parent": "bone44", "length": 14.94, "rotation": 88.41, "x": 0.03, "y": 1}, {"name": "yj1", "parent": "bone45", "rotation": 169.96, "x": 14.75, "y": 0.77, "color": "ff3f00ff"}, {"name": "bone35", "parent": "yj1", "length": 13.73, "rotation": 57.24}, {"name": "yj", "parent": "bone45", "rotation": 169.74, "x": 50.55, "y": -5.57, "color": "ff3f00ff"}, {"name": "bone46", "parent": "bone", "length": 27.23, "rotation": 0.7, "x": 68.38, "y": 121.21}, {"name": "bone47", "parent": "bone46", "length": 18.42, "rotation": 123.28, "y": 0.33}, {"name": "zj1", "parent": "bone47", "rotation": 148.79, "x": 22.34, "y": 0.86, "color": "ff3f00ff"}, {"name": "bone41", "parent": "zj1", "length": 19.54, "rotation": 50.85}, {"name": "zj", "parent": "bone47", "rotation": 150.7, "x": 50.85, "y": -16.56, "color": "ff3f00ff"}, {"name": "bone54", "parent": "bone6", "length": 17.55, "rotation": -106.81, "x": 38.04, "y": -18.38}, {"name": "bone55", "parent": "bone54", "length": 25.44, "rotation": -63.82, "x": 17.55}, {"name": "bone56", "parent": "bone55", "length": 18.47, "rotation": -8.78, "x": 25.44}, {"name": "bone57", "parent": "bone56", "length": 11.34, "rotation": 14.81, "x": 18.47}, {"name": "bone58", "parent": "bone6", "length": 22.47, "rotation": 83.87, "x": 39.34, "y": -8.4}, {"name": "bone59", "parent": "bone58", "length": 27.88, "rotation": 88.43, "x": 23.12, "y": 0.36}, {"name": "bone60", "parent": "bone59", "length": 21.8, "rotation": 6.78, "x": 27.88}, {"name": "bone61", "parent": "bone60", "length": 13.78, "rotation": -28.58, "x": 21.8}, {"name": "bone62", "parent": "bone6", "length": 13.22, "rotation": -54.69, "x": 80.26, "y": -23.95}, {"name": "bone63", "parent": "bone62", "length": 10.25, "rotation": -67.94, "x": 13.22}, {"name": "bone64", "parent": "bone6", "length": 11.33, "rotation": 54.6, "x": 81.03, "y": 9.6}, {"name": "bone65", "parent": "bone64", "length": 8.77, "rotation": 56.45, "x": 11.33}, {"name": "bone66", "parent": "bone6", "length": 24.76, "rotation": 179.83, "x": -1.88, "y": -8.4}, {"name": "bone67", "parent": "bone66", "length": 13.72, "rotation": -7.54, "x": 24.76}, {"name": "bone68", "parent": "bone67", "length": 10.48, "rotation": -1.97, "x": 13.72}, {"name": "bone21", "parent": "bone11", "length": 60.42, "rotation": -62.78, "x": 75.89, "y": -6.52}, {"name": "bone22", "parent": "bone21", "length": 44.46, "rotation": -1.98, "x": 60.42}, {"name": "bone23", "parent": "bone22", "length": 33.38, "rotation": 1.03, "x": 44.46}, {"name": "bone17", "parent": "bone14", "length": 55.46, "rotation": -142.52, "x": 21.36, "y": -46.74, "transform": "noRotationOrReflection"}, {"name": "bone18", "parent": "bone17", "length": 44.6, "rotation": 0.4, "x": 55.46}, {"name": "bone19", "parent": "bone18", "length": 33.94, "rotation": -1.67, "x": 44.52, "y": 0.69}, {"name": "bone20", "parent": "bone19", "length": 24.29, "rotation": 3.89, "x": 33.94}, {"name": "bone24", "parent": "bone16", "length": 83.63, "rotation": 107.03, "x": 19.24, "y": 1.26}, {"name": "a2", "parent": "bone24", "length": 38.21, "rotation": -92.82, "x": 56.53, "y": -0.58}, {"name": "a9", "parent": "a2", "length": 68.64, "rotation": 95.9, "x": -4.81, "y": 93.38}, {"name": "a3", "parent": "a9", "length": 27.92, "rotation": 160.02, "x": 0.12, "y": -0.53, "transform": "noRotationOrReflection"}, {"name": "a4", "parent": "a3", "length": 82.32, "rotation": 63.57, "x": 27.08, "y": -0.25}, {"name": "a5", "parent": "a4", "length": 75.92, "rotation": 15.82, "x": 82.32}, {"name": "a6", "parent": "a5", "length": 41.37, "rotation": -38.46, "x": 75.92}, {"name": "a7", "parent": "a6", "length": 24.06, "rotation": -60.31, "x": 41.37}, {"name": "a8", "parent": "a7", "length": 23.42, "rotation": -47.13, "x": 24.06}, {"name": "bone69", "parent": "bone14", "length": 53.01, "rotation": -120.26, "x": 61.45, "y": 37.98, "transform": "noRotationOrReflection"}, {"name": "bone70", "parent": "bone69", "length": 55.68, "rotation": -33.84, "x": 53.01}, {"name": "bone71", "parent": "bone70", "length": 46.94, "rotation": -20.66, "x": 55.68}, {"name": "bone7", "parent": "bone", "x": 295.89, "y": 118.57, "color": "ff1a1aff"}, {"name": "bone8", "parent": "bone7", "x": -12.3, "y": 70.58}, {"name": "bone9", "parent": "bone8", "x": -33.67, "y": 13.39}, {"name": "bone10", "parent": "bone9", "length": 32.66, "rotation": 156.47, "x": -17.87, "y": 4.83}, {"name": "bone25", "parent": "bone10", "length": 28.04, "rotation": -24.26, "x": 32.66}, {"name": "bone26", "parent": "bone25", "length": 19.11, "rotation": -26.06, "x": 28.04}, {"name": "bone27", "parent": "bone8", "x": 33.55, "y": 11.67}, {"name": "bone28", "parent": "bone27", "length": 53.81, "rotation": 14.04}, {"name": "bone29", "parent": "bone28", "length": 28.54, "rotation": -143.79, "x": -1.53, "y": 52.89}, {"name": "bone30", "parent": "bone29", "length": 20.44, "rotation": 4.83, "x": 28.54}, {"name": "bone52", "parent": "bone28", "length": 19.82, "rotation": -52.01, "x": 69.46, "y": 31.68}, {"name": "bone72", "parent": "bone52", "length": 10.83, "rotation": -12.74, "x": 19.82}, {"name": "bone73", "parent": "bone9", "length": 23.22, "rotation": -83.83, "x": -4.98, "y": -21.65}, {"name": "bone74", "parent": "bone73", "length": 24.37, "rotation": -56.36, "x": 23.22}, {"name": "bone77", "parent": "bone7", "length": 17.17, "rotation": -87.92, "x": -52.51, "y": 3.04}, {"name": "j1", "parent": "bone77", "rotation": -52.28, "x": -21.11, "y": -13.91, "color": "ff3f00ff"}, {"name": "bone75", "parent": "j1", "length": 14.72, "rotation": 82.2}, {"name": "bone76", "parent": "bone75", "length": 16.37, "rotation": 50.33, "x": 14.72}, {"name": "bone78", "parent": "bone9", "length": 23.22, "rotation": -83.83, "x": 38.39, "y": -20.72}, {"name": "bone79", "parent": "bone78", "length": 24.37, "rotation": -56.36, "x": 23.22}, {"name": "bone89", "parent": "bone7", "length": 16.02, "rotation": -90, "x": -12.81, "y": 3.32}, {"name": "j2", "parent": "bone89", "rotation": -50.19, "x": -21.24, "y": -10.99, "color": "ff3f00ff"}, {"name": "bone80", "parent": "j2", "length": 14.72, "rotation": 82.2}, {"name": "bone81", "parent": "bone80", "length": 16.37, "rotation": 50.33, "x": 14.72}, {"name": "bone82", "parent": "bone8", "x": 42.63, "y": -13.87}, {"name": "bone83", "parent": "bone82", "length": 31.2, "rotation": -91.55, "x": -16.86, "y": 4.5}, {"name": "bone84", "parent": "bone83", "length": 22.65, "rotation": 45.54, "x": 31.2}, {"name": "bone90", "parent": "bone7", "length": 16.58, "rotation": -89.03, "x": 34.39, "y": -0.05}, {"name": "j3", "parent": "bone90", "rotation": 43.02, "x": -13.86, "y": -5.79, "color": "ff3f00ff"}, {"name": "bone85", "parent": "j3", "length": 14.27, "rotation": -11.87, "x": 0.2, "y": 0.2}, {"name": "bone86", "parent": "bone82", "length": 31.2, "rotation": -91.55, "x": 23.04, "y": 9.55}, {"name": "bone87", "parent": "bone86", "length": 22.65, "rotation": 45.54, "x": 31.2}, {"name": "bone91", "parent": "bone7", "length": 16.87, "rotation": -88.09, "x": 74.29, "y": 4.17}, {"name": "j4", "parent": "bone91", "rotation": 42.09, "x": -14.8, "y": -5.54, "color": "ff3f00ff"}, {"name": "bone88", "parent": "j4", "length": 14.27, "rotation": -11.87, "x": 0.2, "y": 0.2}, {"name": "bone53", "parent": "bone31", "x": 6.38, "y": -34.9}, {"name": "bone92", "parent": "bone53", "length": 34.63, "rotation": -10.92}, {"name": "bone93", "parent": "bone92", "length": 36.37, "rotation": 0.21, "x": 34.3, "y": -0.02}, {"name": "bone94", "parent": "bone53", "length": 39.34, "rotation": 12.14, "x": 0.18, "y": -0.34}, {"name": "bone95", "parent": "bone94", "length": 36.88, "rotation": -46.37, "x": 38.36, "y": -0.36, "color": "abe323ff"}, {"name": "bone96", "parent": "bone", "length": 23.91, "rotation": 1.59, "x": -36.36, "y": 125.89}, {"name": "bone97", "parent": "bone96", "length": 14.94, "rotation": 88.41, "x": 0.03, "y": 1}, {"name": "yj2", "parent": "bone97", "rotation": 169.96, "x": 14.75, "y": 0.77, "color": "ff3f00ff"}, {"name": "bone98", "parent": "yj2", "length": 13.73, "rotation": 57.24}, {"name": "yj3", "parent": "bone97", "rotation": 169.74, "x": 50.55, "y": -5.57, "color": "ff3f00ff"}, {"name": "bone99", "parent": "bone8", "x": -20.39, "y": 1.44, "color": "abe323ff"}, {"name": "bone100", "parent": "bone8", "x": 14.5, "y": 2.19, "color": "abe323ff"}, {"name": "juqi", "parent": "root", "x": -150.29, "y": 454.32, "scaleX": 2.5246, "scaleY": 2.5246}, {"name": "shui", "parent": "root", "x": 175.88, "y": 279.73, "scaleX": 5.1185, "scaleY": 5.1185}], "slots": [{"name": "a25", "bone": "bone52", "color": "ffffff00", "attachment": "a25"}, {"name": "a24", "bone": "bone86", "color": "ffffff00", "attachment": "a24"}, {"name": "a23", "bone": "bone78", "color": "ffffff00", "attachment": "a23"}, {"name": "a22", "bone": "bone9", "color": "ffffff00", "attachment": "a22"}, {"name": "a21", "bone": "bone73", "color": "ffffff00", "attachment": "a21"}, {"name": "a20", "bone": "bone83", "color": "ffffff00", "attachment": "a20"}, {"name": "a19", "bone": "bone29", "color": "ffffff00", "attachment": "a19"}, {"name": "a18", "bone": "bone11", "attachment": "a18"}, {"name": "a17111111", "bone": "a3", "attachment": "a17"}, {"name": "a16", "bone": "bone21", "attachment": "a16"}, {"name": "a15", "bone": "bone39", "attachment": "a15"}, {"name": "a2611", "bone": "bone92", "color": "ffffff00", "attachment": "a15"}, {"name": "a1411", "bone": "bone33", "attachment": "a14"}, {"name": "a13", "bone": "bone5", "attachment": "a13"}, {"name": "a12111111", "bone": "bone18", "attachment": "a12"}, {"name": "a11111111", "bone": "bone14", "attachment": "a11"}, {"name": "a10111111", "bone": "bone15", "attachment": "a10"}, {"name": "a9111111", "bone": "bone17", "attachment": "a9"}, {"name": "a8", "bone": "bone6", "attachment": "a8"}, {"name": "a7", "bone": "bone64", "attachment": "a7"}, {"name": "a6", "bone": "bone62", "attachment": "a6"}, {"name": "a5", "bone": "bone6", "color": "ffffff00", "attachment": "a5"}, {"name": "a4", "bone": "bone6", "attachment": "a4"}, {"name": "a3", "bone": "bone59", "attachment": "a3"}, {"name": "a2111111", "bone": "a2", "attachment": "a2"}, {"name": "a1", "bone": "bone16", "attachment": "a1"}, {"name": "a0", "bone": "bone12", "attachment": "a0"}, {"name": "jushui/xiezi_skill_xl_00003", "bone": "juqi", "blend": "additive"}, {"name": "skill/skill0/dg01/shui_00011", "bone": "shui", "blend": "additive"}], "ik": [{"name": "j1", "order": 5, "bones": ["bone73", "bone74"], "target": "j1", "bendPositive": false}, {"name": "j2", "order": 8, "bones": ["bone78", "bone79"], "target": "j2", "bendPositive": false}, {"name": "j3", "order": 10, "bones": ["bone83", "bone84"], "target": "j3"}, {"name": "j4", "order": 12, "bones": ["bone86", "bone87"], "target": "j4"}, {"name": "yj", "order": 2, "bones": ["bone33"], "target": "yj", "compress": true, "stretch": true}, {"name": "yj1", "order": 3, "bones": ["bone34"], "target": "yj1", "compress": true, "stretch": true}, {"name": "yj2", "bones": ["bone36", "bone37"], "target": "yj1", "bendPositive": false}, {"name": "yj5", "order": 19, "bones": ["bone92"], "target": "yj3", "compress": true, "stretch": true}, {"name": "yj6", "order": 20, "bones": ["bone93"], "target": "yj2", "compress": true, "stretch": true}, {"name": "yj7", "order": 17, "bones": ["bone94", "bone95"], "target": "yj2", "bendPositive": false, "stretch": true}, {"name": "zj", "order": 15, "bones": ["bone39"], "target": "zj", "compress": true, "stretch": true}, {"name": "zj1", "order": 16, "bones": ["bone40"], "target": "zj1", "compress": true, "stretch": true}, {"name": "zj2", "order": 13, "bones": ["bone42", "bone43"], "target": "zj1", "bendPositive": false, "stretch": true}], "transform": [{"name": "c01", "order": 6, "bones": ["bone99"], "target": "bone100", "x": -34.89, "y": -0.75, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "l1", "order": 7, "bones": ["bone78"], "target": "bone99", "rotation": -83.83, "x": 25.11, "y": -8.76, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "l2", "order": 11, "bones": ["bone86"], "target": "bone99", "rotation": -91.55, "x": 86.06, "y": -5.76, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "r1", "order": 4, "bones": ["bone73"], "target": "bone100", "rotation": -83.83, "x": -53.15, "y": -10.45, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "r2", "order": 9, "bones": ["bone83"], "target": "bone100", "rotation": -91.55, "x": 11.27, "y": -11.57, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "s01", "order": 21, "bones": ["bone49"], "target": "bone48", "x": 60.42, "y": 0.47, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "t01", "order": 22, "bones": ["bone51"], "target": "bone50", "x": 45.84, "y": 1.59, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "yj", "order": 1, "bones": ["yj"], "target": "bone37", "rotation": 25.32, "x": 7, "y": -15.67, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "yj1", "order": 18, "bones": ["yj3"], "target": "bone95", "rotation": 21.43, "x": 3.08, "y": -13.41, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "ys", "order": 23, "bones": ["bone14"], "target": "bone48", "rotation": 163.47, "x": 89.46, "y": 103.12, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "zj", "order": 14, "bones": ["zj"], "target": "bone43", "rotation": 29.77, "x": 7.59, "y": -15.5, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "zs", "order": 24, "bones": ["bone11"], "target": "bone49", "rotation": -128.94, "x": 24.36, "y": -46.51, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"a13": {"a13": {"type": "mesh", "uvs": [0.43955, 0.00433, 0.58452, 0.02053, 0.66823, 0.06797, 0.73573, 0.11992, 0.7952, 0.18798, 0.86803, 0.36177, 0.92131, 0.48891, 0.96727, 0.58409, 0.98339, 0.70403, 0.99999, 0.73766, 0.97499, 0.80618, 0.93539, 0.91472, 0.83732, 0.96525, 0.63861, 1, 0.43686, 1, 0.27185, 0.97649, 0.19968, 0.95486, 0.1136, 0.89592, 0.06186, 0.78432, 0.03492, 0.76045, 0.00375, 0.55252, 0.00375, 0.38493, 0.01553, 0.28083, 0.05256, 0.17048, 0.08433, 0.11077, 0.12953, 0.07739, 0.18534, 0.04599, 0.31279, 0.004, 0.55851, 0.14864, 0.63381, 0.27423, 0.68509, 0.398, 0.73316, 0.54908, 0.7588, 0.70561, 0.76681, 0.85669, 0.178, 0.21018, 0.22767, 0.34669, 0.25811, 0.50505, 0.27253, 0.65066, 0.27414, 0.84906, 0.35265, 0.15193, 0.41194, 0.31757, 0.45413, 0.4638, 0.48509, 0.60697, 0.50808, 0.76533, 0.51609, 0.88182, 0.89172, 0.83041, 0.88531, 0.69209, 0.84794, 0.52463, 0.78917, 0.38447, 0.73469, 0.25524, 0.6706, 0.14785], "triangles": [18, 19, 37, 28, 0, 1, 50, 2, 3, 39, 0, 28, 49, 50, 3, 29, 28, 50, 39, 27, 0, 34, 25, 26, 24, 25, 34, 23, 24, 34, 26, 27, 39, 49, 3, 4, 29, 50, 49, 40, 39, 28, 40, 28, 29, 39, 34, 26, 35, 39, 40, 35, 34, 39, 49, 4, 5, 48, 49, 5, 30, 29, 49, 30, 49, 48, 41, 40, 29, 41, 29, 30, 36, 35, 40, 36, 40, 41, 47, 48, 5, 47, 5, 6, 31, 30, 48, 31, 48, 47, 22, 23, 34, 22, 34, 35, 42, 41, 30, 42, 30, 31, 42, 37, 36, 42, 36, 41, 7, 47, 6, 32, 31, 47, 42, 31, 32, 21, 35, 36, 35, 21, 22, 37, 20, 36, 36, 20, 21, 37, 19, 20, 7, 46, 47, 37, 42, 43, 28, 1, 2, 28, 2, 50, 18, 37, 38, 38, 37, 43, 13, 44, 33, 38, 43, 44, 17, 18, 38, 43, 42, 32, 10, 46, 8, 10, 8, 9, 45, 46, 10, 32, 46, 45, 33, 32, 45, 43, 32, 33, 44, 43, 33, 11, 45, 10, 12, 33, 45, 46, 7, 8, 32, 47, 46, 44, 14, 38, 16, 17, 38, 13, 33, 12, 14, 15, 38, 14, 44, 13, 15, 16, 38, 12, 45, 11], "vertices": [2, 6, 146.48, 11.74, 0.62302, 7, 34.47, 12.54, 0.37698, 3, 6, 139.82, -25.4, 0.5424, 7, 28.44, -24.7, 0.3776, 17, 72.35, -12.56, 0.08, 3, 6, 127.32, -46.15, 0.64301, 7, 16.3, -45.67, 0.27699, 17, 59.85, -33.32, 0.08, 3, 6, 114.13, -62.64, 0.79115, 7, 3.39, -62.38, 0.12885, 17, 46.66, -49.81, 0.08, 3, 6, 97.44, -76.77, 0.88629, 7, -13.06, -76.79, 0.03371, 17, 29.97, -63.93, 0.08, 3, 5, 113.3, -89.56, 0.03407, 6, 56.45, -92.44, 0.88593, 17, -11.02, -79.6, 0.08, 3, 5, 83.91, -102.48, 0.1363, 6, 26.46, -103.9, 0.7837, 17, -41.01, -91.06, 0.08, 3, 5, 61.86, -113.72, 0.34074, 6, 3.89, -114.04, 0.57926, 17, -63.58, -101.21, 0.08, 4, 18, -37.95, 116.68, 0.03407, 5, 34.4, -117.06, 0.54519, 6, -23.71, -116.04, 0.34074, 17, -91.18, -103.21, 0.08, 4, 18, -30.23, 120.89, 0.1363, 5, 26.6, -121.13, 0.64741, 6, -31.69, -119.72, 0.1363, 17, -99.16, -106.88, 0.08, 4, 18, -14.69, 114.23, 0.34074, 5, 11.18, -114.18, 0.54519, 6, -46.75, -112.03, 0.03407, 17, -114.22, -99.19, 0.08, 2, 18, 9.94, 103.69, 0.62963, 5, -13.24, -103.18, 0.37037, 2, 18, 21.16, 78.16, 0.85185, 5, -23.99, -77.44, 0.14815, 2, 18, 28.49, 26.6, 0.96296, 5, -30.35, -25.76, 0.03704, 1, 18, 27.88, -25.65, 1, 3, 18, 22.02, -68.32, 0.8243, 5, -22.11, 69.02, 0.0317, 16, -78.09, 85.89, 0.144, 4, 18, 16.88, -86.95, 0.70002, 5, -16.62, 87.56, 0.12174, 17, -132.12, 103.67, 0.04, 16, -71.69, 104.13, 0.13824, 5, 18, 3.18, -109.09, 0.5174, 5, -2.51, 109.43, 0.27392, 6, -49.48, 111.99, 0.03044, 17, -116.95, 124.83, 0.04, 16, -56.53, 125.3, 0.13824, 5, 18, -22.42, -122.19, 0.30436, 5, 23.33, 122.06, 0.39566, 6, -23.06, 123.33, 0.12174, 17, -90.53, 136.17, 0.04, 16, -30.11, 136.64, 0.13824, 5, 18, -27.94, -129.11, 0.12174, 5, 28.98, 128.87, 0.39566, 6, -17.08, 129.86, 0.30436, 17, -84.55, 142.7, 0.04, 16, -24.13, 143.17, 0.13824, 5, 18, -75.44, -136.63, 0.03044, 5, 76.61, 135.5, 0.27392, 6, 30.82, 134.15, 0.5174, 17, -36.65, 146.99, 0.04, 16, 23.77, 147.46, 0.13824, 4, 5, 114.8, 134.34, 0.12174, 6, 68.91, 131.13, 0.70002, 17, 1.44, 143.96, 0.04, 16, 61.86, 144.43, 0.13824, 4, 5, 138.44, 130.58, 0.03044, 6, 92.33, 126.21, 0.79132, 17, 24.86, 139.04, 0.04, 16, 85.28, 139.51, 0.13824, 3, 6, 116.65, 114.66, 0.82176, 17, 49.18, 127.49, 0.04, 16, 109.6, 127.96, 0.13824, 4, 6, 129.57, 105.38, 0.7622, 7, 15.98, 105.88, 0.0058, 17, 62.1, 118.21, 0.04, 16, 122.52, 118.68, 0.192, 4, 6, 136.23, 93.1, 0.71445, 7, 22.84, 93.72, 0.04587, 17, 68.76, 105.94, 0.04, 16, 129.18, 106.41, 0.19968, 4, 6, 142.22, 78.13, 0.64521, 7, 29.09, 78.85, 0.13047, 17, 74.75, 90.96, 0.04, 16, 135.17, 91.43, 0.18432, 4, 6, 149.15, 44.46, 0.60947, 7, 36.59, 45.31, 0.26637, 17, 81.68, 57.3, 0.07616, 16, 142.1, 57.77, 0.048, 3, 6, 111.24, -16.37, 0.5477, 7, -0.29, -16.16, 0.1963, 16, 104.19, -3.06, 0.256, 4, 5, 135.09, -29.53, 0.02756, 6, 81.15, -33.54, 0.62877, 7, -30.08, -33.84, 0.08768, 16, 74.1, -20.24, 0.256, 4, 5, 106.48, -41.95, 0.11022, 6, 51.97, -44.55, 0.61137, 7, -59.07, -45.34, 0.0224, 16, 44.92, -31.24, 0.256, 3, 5, 71.67, -53.35, 0.27556, 6, 16.64, -54.23, 0.46844, 16, 9.6, -40.93, 0.256, 3, 5, 35.8, -58.91, 0.45467, 6, -19.46, -58.03, 0.28933, 16, -26.51, -44.72, 0.256, 3, 5, 1.31, -59.94, 0.558, 6, -53.96, -57.37, 0.186, 16, -61.01, -44.06, 0.256, 2, 6, 105.05, 82.99, 0.808, 16, 98.01, 96.29, 0.192, 3, 5, 121.76, 76.11, 0.02844, 6, 73.01, 72.63, 0.73956, 16, 65.96, 85.93, 0.232, 3, 5, 85.43, 69.32, 0.10059, 6, 36.39, 67.62, 0.67541, 16, 29.34, 80.93, 0.224, 3, 5, 52.14, 66.6, 0.21696, 6, 3, 66.53, 0.59104, 16, -4.05, 79.83, 0.192, 3, 5, 6.91, 67.55, 0.27222, 6, -42.13, 69.7, 0.51178, 16, -49.17, 83, 0.216, 2, 6, 114.71, 36.84, 0.744, 16, 107.66, 50.15, 0.256, 3, 5, 126.95, 28.21, 0.02756, 6, 75.85, 24.52, 0.71644, 16, 68.8, 37.83, 0.256, 3, 5, 93.3, 18.29, 0.11022, 6, 41.75, 16.27, 0.63378, 16, 34.7, 29.58, 0.256, 3, 5, 60.42, 11.27, 0.27556, 6, 8.57, 10.86, 0.46844, 16, 1.52, 24.17, 0.256, 3, 5, 24.16, 6.41, 0.45467, 6, -27.89, 7.78, 0.28933, 16, -34.94, 21.09, 0.256, 3, 5, -2.46, 5.14, 0.558, 6, -54.53, 7.82, 0.186, 16, -61.58, 21.13, 0.256, 3, 5, 6.31, -92.46, 0.672, 6, -50.55, -90.09, 0.224, 16, -57.6, -76.79, 0.104, 3, 5, 37.89, -91.75, 0.54756, 6, -18.98, -90.93, 0.34844, 16, -26.03, -77.63, 0.104, 3, 5, 76.34, -83.24, 0.33185, 6, 19.85, -84.31, 0.56415, 16, 12.8, -71, 0.104, 3, 5, 108.75, -68.99, 0.13274, 6, 52.91, -71.67, 0.76326, 16, 45.86, -58.36, 0.104, 3, 5, 138.63, -55.78, 0.03319, 6, 83.4, -59.93, 0.86281, 16, 76.35, -46.63, 0.104, 2, 6, 109.12, -45.32, 0.896, 16, 102.07, -32.02, 0.104], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 8, 10, 10, 12, 48, 68, 68, 70, 70, 72, 72, 74, 74, 76, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 18, 20, 20, 22, 16, 18, 24, 26], "width": 259, "height": 228}}, "a15": {"a15": {"type": "mesh", "uvs": [0.79671, 0, 0.917, 0.03565, 0.98565, 0.10258, 1, 0.26924, 0.99086, 0.43944, 0.9491, 0.6052, 0.84878, 0.69494, 0.75056, 0.73968, 0.5913, 0.80155, 0.57908, 0.82904, 0.67802, 0.89138, 0.73079, 0.93369, 0.73371, 0.96956, 0.69999, 0.99154, 0.33572, 0.9916, 0.30895, 0.94618, 0.37953, 0.83316, 0.36887, 0.80886, 0.2065, 0.73364, 0.06497, 0.59015, 0.00915, 0.48796, 0.00909, 0.36694, 0.09688, 0.17367, 0.15911, 0.09732, 0.30344, 0.03895, 0.45331, 0.00844, 0.51038, 0.25023, 0.50964, 0.53587, 0.48761, 0.79946, 0.48279, 0.84376], "triangles": [13, 29, 10, 10, 29, 9, 29, 13, 14, 14, 16, 29, 14, 15, 16, 13, 11, 12, 13, 10, 11, 29, 28, 9, 29, 16, 28, 16, 17, 28, 9, 28, 8, 28, 17, 27, 8, 28, 27, 27, 17, 18, 7, 8, 27, 7, 27, 6, 18, 19, 27, 6, 27, 5, 5, 27, 4, 19, 20, 27, 27, 20, 26, 22, 26, 21, 26, 22, 23, 27, 26, 4, 26, 23, 24, 26, 20, 21, 4, 26, 3, 26, 2, 3, 1, 26, 0, 1, 2, 26, 24, 25, 26, 26, 25, 0], "vertices": [1, 25, -27.15, 41.15, 1, 1, 25, -21.94, 53.76, 1, 1, 25, -13.53, 60.51, 1, 2, 25, 6.03, 60.46, 0.99859, 26, -29.49, 59.62, 0.00141, 2, 25, 25.8, 57.85, 0.96552, 26, -9.64, 57.67, 0.03448, 2, 25, 44.76, 51.78, 0.86769, 26, 9.51, 52.23, 0.13231, 3, 25, 54.34, 40.12, 0.73843, 26, 19.47, 40.9, 0.26091, 37, 22.37, 37.29, 0.00066, 3, 25, 58.69, 29.12, 0.55487, 26, 24.19, 30.05, 0.42896, 37, 16.94, 26.78, 0.01616, 3, 25, 64.51, 11.39, 0.09763, 26, 30.59, 12.52, 0.55276, 37, 7.39, 10.75, 0.34961, 3, 25, 67.6, 9.81, 0.02779, 26, 33.74, 11.04, 0.27784, 37, 8.23, 7.38, 0.69438, 2, 26, 41.54, 21.36, 0.00023, 37, 21.16, 7.85, 0.99977, 1, 37, 28.69, 7.24, 1, 1, 37, 31.43, 4.05, 1, 1, 37, 30.02, -0.18, 1, 2, 26, 51.46, -16.13, 0.10301, 37, -1.64, -23.52, 0.89699, 2, 26, 46.02, -18.76, 0.13308, 37, -7.12, -20.96, 0.86692, 3, 25, 66.33, -11.71, 0.0001, 26, 33.18, -10.51, 0.73355, 37, -8.83, -5.79, 0.26635, 3, 25, 63.4, -12.62, 0.00178, 26, 30.28, -11.52, 0.92586, 37, -11.45, -4.18, 0.07236, 2, 25, 53.2, -29.38, 0.1542, 26, 20.64, -28.61, 0.8458, 2, 25, 35.22, -43.25, 0.55637, 26, 3.14, -43.06, 0.44363, 2, 25, 22.81, -48.28, 0.72873, 26, -9.1, -48.51, 0.27127, 2, 25, 8.7, -47.13, 0.85727, 26, -23.24, -47.83, 0.14273, 2, 25, -13.06, -35.84, 0.98576, 26, -45.37, -37.27, 0.01424, 2, 25, -21.42, -28.41, 0.99873, 26, -53.96, -30.12, 0.00127, 1, 25, -26.95, -12.32, 1, 1, 25, -29.19, 4.1, 1, 1, 25, -0.5, 7.94, 1, 1, 25, 32.81, 5.14, 1, 3, 25, 63.35, 0.25, 0.00319, 26, 29.8, 1.34, 0.9121, 37, -1.77, 4.31, 0.08471, 2, 26, 34.96, 0.57, 0.20398, 37, 0.88, -0.17, 0.79602], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 30, 32, 32, 34, 16, 18, 18, 20, 20, 22, 12, 14, 14, 16, 52, 4, 52, 2], "width": 108, "height": 117}}, "a16": {"a16": {"type": "mesh", "uvs": [0.50166, 0, 0.68649, 0, 0.79661, 0.05186, 0.89376, 0.1161, 0.95066, 0.18805, 1, 0.32589, 0.99386, 0.46146, 0.94166, 0.60106, 0.8889, 0.69452, 0.8371, 0.76988, 0.77125, 0.846, 0.69136, 0.91584, 0.58973, 0.96801, 0.45838, 0.99693, 0.35439, 1, 0.26798, 0.97531, 0.19898, 0.93017, 0.14306, 0.86964, 0.10339, 0.80759, 0.06205, 0.71879, 0.03191, 0.64157, 0.00897, 0.55148, 0.00672, 0.44721, 0.06338, 0.34646, 0.12647, 0.26133, 0.21734, 0.17996, 0.30608, 0.1005, 0.39409, 0.02629, 0.44733, 0.0616, 0.37872, 0.12016, 0.34338, 0.22053, 0.33922, 0.34321, 0.3517, 0.45474, 0.3413, 0.55373, 0.3122, 0.63715, 0.26646, 0.70964, 0.22904, 0.78353, 0.22072, 0.85881, 0.58531, 0.04655, 0.73707, 0.08559, 0.83062, 0.14693, 0.88468, 0.24591, 0.90547, 0.33792, 0.91378, 0.45642, 0.88468, 0.5805, 0.8327, 0.67391, 0.77865, 0.74919, 0.72044, 0.82726, 0.65391, 0.89278, 0.57076, 0.94018, 0.45434, 0.96946, 0.34623, 0.96667, 0.26307, 0.93879], "triangles": [38, 0, 1, 28, 27, 0, 28, 0, 38, 39, 1, 2, 38, 1, 39, 29, 26, 27, 28, 29, 27, 40, 2, 3, 39, 2, 40, 30, 26, 29, 25, 26, 30, 29, 28, 38, 38, 30, 29, 39, 30, 38, 30, 39, 40, 37, 18, 36, 36, 49, 37, 17, 18, 37, 11, 48, 47, 16, 17, 37, 37, 51, 52, 16, 37, 52, 36, 48, 49, 37, 50, 51, 11, 12, 49, 11, 49, 48, 49, 50, 37, 15, 52, 51, 16, 52, 15, 12, 13, 50, 12, 50, 49, 14, 51, 50, 14, 50, 13, 15, 51, 14, 34, 21, 33, 20, 21, 34, 45, 44, 7, 45, 33, 44, 34, 33, 45, 8, 45, 7, 35, 20, 34, 19, 20, 35, 46, 34, 45, 46, 45, 8, 9, 46, 8, 36, 19, 35, 18, 19, 36, 47, 34, 46, 10, 47, 46, 35, 34, 47, 48, 36, 35, 9, 10, 46, 35, 47, 48, 11, 47, 10, 40, 3, 4, 41, 40, 4, 41, 4, 5, 42, 41, 5, 30, 24, 25, 31, 24, 30, 23, 24, 31, 30, 40, 31, 31, 42, 32, 6, 43, 42, 5, 6, 42, 32, 21, 22, 32, 23, 31, 23, 32, 22, 33, 21, 32, 42, 31, 41, 41, 31, 40, 43, 32, 42, 6, 44, 43, 7, 44, 6, 43, 44, 32, 44, 33, 32], "vertices": [3, 54, -32.97, -10.99, 0.03256, 55, -92.96, -14.21, 6e-05, 10, 51.04, 17.78, 0.96738, 3, 54, -35.61, 9.92, 0.08435, 55, -96.31, 6.59, 0, 10, 68.43, 29.68, 0.91565, 3, 54, -28.43, 23.48, 0.21976, 55, -89.61, 20.39, 0.00013, 10, 83.76, 29.5, 0.78011, 3, 54, -18.98, 35.83, 0.4193, 55, -80.59, 33.06, 0.00433, 10, 99.07, 26.74, 0.57637, 3, 54, -7.65, 43.8, 0.61602, 55, -69.55, 41.42, 0.02726, 10, 111.34, 20.32, 0.35673, 4, 54, 14.89, 52.3, 0.72725, 55, -47.31, 50.7, 0.10237, 56, -90.84, 52.34, 0.00025, 10, 129.21, 4.16, 0.17013, 4, 54, 37.85, 54.49, 0.68936, 55, -24.44, 53.68, 0.2471, 56, -67.93, 54.91, 0.0042, 10, 141.66, -15.25, 0.05934, 4, 54, 62.13, 51.55, 0.52743, 55, -0.07, 51.58, 0.44002, 56, -43.59, 52.38, 0.0217, 10, 150.15, -38.2, 0.01084, 4, 54, 78.65, 47.57, 0.32381, 55, 16.58, 48.17, 0.60058, 56, -27.01, 48.67, 0.07481, 10, 154.17, -54.7, 0.0008, 3, 54, 92.1, 43.31, 0.1588, 55, 30.16, 44.38, 0.65161, 56, -13.5, 44.63, 0.18959, 3, 54, 105.88, 37.48, 0.06156, 55, 44.14, 39.03, 0.56361, 56, 0.38, 39.03, 0.37483, 3, 54, 118.79, 29.93, 0.0175, 55, 57.31, 31.93, 0.38375, 56, 13.42, 31.7, 0.59875, 3, 54, 129.04, 19.55, 0.00286, 55, 67.91, 21.91, 0.1978, 56, 23.83, 21.48, 0.79934, 3, 54, 135.79, 5.3, 0.00012, 55, 75.14, 7.91, 0.07189, 56, 30.82, 7.35, 0.92799, 2, 55, 77.55, -3.71, 0.02821, 56, 33.01, -4.31, 0.97179, 3, 54, 134.86, -16.69, 0.00025, 55, 74.97, -14.11, 0.05979, 56, 30.25, -14.65, 0.93995, 3, 54, 128.23, -25.45, 0.0026, 55, 68.65, -23.09, 0.16088, 56, 23.77, -23.53, 0.83652, 3, 54, 118.82, -33.07, 0.01538, 55, 59.51, -31.03, 0.32153, 56, 14.48, -31.29, 0.66309, 4, 54, 108.92, -38.87, 0.05219, 55, 49.81, -37.17, 0.49504, 56, 4.68, -37.26, 0.45273, 10, 91.14, -121.16, 4e-05, 4, 54, 94.53, -45.43, 0.13018, 55, 35.66, -44.23, 0.61597, 56, -9.6, -44.06, 0.25311, 10, 78.72, -111.36, 0.00074, 4, 54, 81.93, -50.48, 0.25387, 55, 23.25, -49.71, 0.6279, 56, -22.11, -49.32, 0.1138, 10, 68.47, -102.47, 0.00443, 4, 54, 67.06, -54.99, 0.40914, 55, 8.54, -54.73, 0.53523, 56, -36.9, -54.07, 0.038, 10, 57.66, -91.31, 0.01763, 4, 54, 49.51, -57.46, 0.55557, 55, -8.91, -57.81, 0.38233, 56, -54.41, -56.84, 0.00968, 10, 47.43, -76.83, 0.05242, 4, 54, 31.71, -53.2, 0.63623, 55, -26.85, -54.16, 0.22876, 56, -72.28, -52.86, 0.00135, 10, 43.09, -59.05, 0.13366, 4, 54, 16.45, -47.87, 0.59402, 55, -42.29, -49.36, 0.11256, 56, -87.62, -47.79, 2e-05, 10, 40.85, -43.04, 0.29341, 3, 54, 1.43, -39.32, 0.43756, 55, -57.59, -41.34, 0.04331, 10, 41.58, -25.78, 0.51913, 3, 54, -13.24, -30.97, 0.23384, 55, -72.54, -33.5, 0.01209, 10, 42.3, -8.92, 0.75407, 3, 54, -27.01, -22.59, 0.08488, 55, -86.59, -25.6, 0.00187, 10, 43.45, 7.16, 0.91324, 3, 54, -21.81, -15.82, 0.10386, 55, -81.63, -18.66, 0.00089, 10, 51.85, 5.64, 0.89525, 3, 54, -10.96, -22.34, 0.26391, 55, -70.56, -24.79, 0.00664, 10, 51.02, -7, 0.72945, 3, 54, 6.48, -24.2, 0.51004, 55, -53.07, -26.05, 0.02921, 10, 57.33, -23.35, 0.46075, 4, 54, 27.23, -22.06, 0.66779, 55, -32.4, -23.2, 0.11647, 56, -77.27, -21.81, 3e-05, 10, 68.72, -40.83, 0.21571, 4, 54, 45.86, -18.28, 0.64157, 55, -13.91, -18.78, 0.29218, 56, -58.71, -17.72, 0.00168, 10, 80.61, -55.67, 0.06458, 4, 54, 62.71, -17.36, 0.4446, 55, 2.89, -17.27, 0.5278, 56, -41.88, -16.51, 0.01646, 10, 89.14, -70.22, 0.01114, 4, 54, 77.19, -18.88, 0.2263, 55, 17.42, -18.28, 0.69604, 56, -27.37, -17.79, 0.07635, 10, 94.41, -83.8, 0.00131, 4, 54, 90.07, -22.51, 0.08313, 55, 30.41, -21.47, 0.70146, 56, -14.43, -21.21, 0.21534, 10, 97.07, -96.91, 8e-05, 3, 54, 103.07, -25.17, 0.02431, 55, 43.49, -23.68, 0.54058, 56, -1.4, -23.66, 0.4351, 3, 54, 115.88, -24.51, 0.00739, 55, 56.28, -22.58, 0.31697, 56, 11.41, -22.79, 0.67564, 3, 54, -26.31, -0.53, 0.0909, 55, -86.66, -3.53, 1e-05, 10, 63.38, 16.63, 0.90909, 3, 54, -21.89, 17.46, 0.22988, 55, -82.86, 14.6, 0.00058, 10, 81.4, 20.93, 0.76954, 3, 54, -12.88, 29.34, 0.44877, 55, -74.27, 26.79, 0.00537, 10, 96.1, 18.35, 0.54586, 3, 54, 3.05, 37.56, 0.66071, 55, -58.64, 35.56, 0.02863, 10, 110.69, 7.95, 0.31067, 4, 54, 18.27, 41.87, 0.76298, 55, -43.57, 40.39, 0.10042, 56, -87.29, 41.97, 0.00015, 10, 121.48, -3.62, 0.13645, 4, 54, 38.14, 45.33, 0.71278, 55, -23.83, 44.53, 0.24114, 56, -67.48, 45.75, 0.00342, 10, 133.64, -19.7, 0.04266, 4, 54, 59.48, 44.67, 0.53973, 55, -2.48, 44.61, 0.43185, 56, -46.13, 45.45, 0.01928, 10, 142.82, -38.98, 0.00915, 4, 54, 75.97, 40.78, 0.33195, 55, 14.14, 41.29, 0.59644, 56, -29.58, 41.83, 0.07092, 10, 146.9, -55.43, 0.00069, 3, 54, 89.44, 36.26, 0.16211, 55, 27.75, 37.25, 0.65272, 56, -16.04, 37.54, 0.18516, 3, 54, 103.44, 31.34, 0.06104, 55, 41.91, 32.81, 0.56809, 56, -1.96, 32.85, 0.37086, 3, 54, 115.44, 25.21, 0.01658, 55, 54.12, 27.09, 0.38839, 56, 10.14, 26.92, 0.59503, 3, 54, 124.62, 16.81, 0.00255, 55, 63.58, 19.02, 0.2017, 56, 19.46, 18.67, 0.79575, 3, 54, 131.22, 4.26, 0.00011, 55, 70.61, 6.71, 0.07708, 56, 26.26, 6.24, 0.92281, 3, 54, 132.29, -8.02, 0.00011, 55, 72.1, -5.53, 0.05259, 56, 27.54, -6.03, 0.9473, 3, 54, 128.77, -18.02, 0.00156, 55, 68.93, -15.65, 0.13482, 56, 24.18, -16.09, 0.86362], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 42, 44, 38, 40, 40, 42, 18, 20, 44, 46, 46, 48, 52, 54, 48, 50, 50, 52, 20, 22, 22, 24, 14, 16, 16, 18, 34, 36, 36, 38, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 56, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 74], "width": 114, "height": 170}}, "a18": {"a18": {"type": "mesh", "uvs": [0.17878, 0.00665, 0.29647, 0.04672, 0.43434, 0.10198, 0.59971, 0.16825, 0.73854, 0.23347, 0.84197, 0.28949, 0.92289, 0.34027, 0.99593, 0.39994, 0.99171, 0.51319, 0.96252, 0.63117, 0.91321, 0.74048, 0.84039, 0.83041, 0.75536, 0.89905, 0.61823, 0.96644, 0.49977, 1, 0.35584, 0.99325, 0.25053, 0.96658, 0.20358, 0.88485, 0.16527, 0.78258, 0.12348, 0.67338, 0.08952, 0.54273, 0.05768, 0.37818, 0.02708, 0.22604, 0.00845, 0.14499, 0.00815, 0.06142, 0.05133, 0.02715, 0.10221, 0.00652, 0.17661, 0.15758, 0.3265, 0.31017, 0.4683, 0.46438, 0.58984, 0.59099, 0.71745, 0.7306], "triangles": [21, 22, 27, 22, 23, 27, 27, 24, 25, 25, 26, 27, 27, 0, 1, 27, 26, 0, 27, 23, 24, 13, 14, 31, 31, 14, 15, 31, 15, 16, 12, 13, 31, 31, 16, 17, 12, 31, 11, 17, 30, 31, 17, 18, 30, 11, 31, 10, 30, 18, 19, 10, 31, 9, 31, 30, 9, 9, 30, 8, 30, 19, 29, 19, 20, 29, 8, 30, 7, 30, 5, 6, 5, 29, 4, 5, 30, 29, 20, 28, 29, 20, 21, 28, 7, 30, 6, 29, 3, 4, 29, 28, 3, 21, 27, 28, 28, 27, 2, 28, 2, 3, 2, 27, 1], "vertices": [2, 10, -24.13, 20.88, 0.352, 6, 123.24, -53.7, 0.648, 2, 10, -9.46, 23.83, 0.352, 6, 116.32, -66.96, 0.648, 2, 10, 8.4, 26.29, 0.824, 6, 107, -82.4, 0.176, 1, 10, 29.83, 29.24, 1, 1, 10, 48.62, 30.55, 1, 1, 10, 63.22, 30.64, 1, 2, 10, 75.22, 29.87, 0.9954, 54, -32.67, 16.05, 0.0046, 2, 10, 87.2, 27.51, 0.94257, 54, -25.1, 25.62, 0.05743, 2, 10, 96.13, 13.59, 0.65554, 54, -8.63, 27.2, 0.34446, 2, 10, 103.04, -2.55, 0.17105, 54, 8.89, 25.96, 0.82895, 2, 10, 107.3, -18.98, 0.00594, 54, 25.44, 22.23, 0.99406, 1, 54, 39.53, 15.42, 1, 1, 54, 50.72, 6.8, 1, 2, 10, 97.46, -65.7, 0.0063, 54, 62.49, -7.88, 0.9937, 2, 10, 88.79, -77.57, 0.02781, 54, 69.08, -21.02, 0.97219, 2, 10, 74.34, -86.27, 0.06994, 54, 70.21, -37.85, 0.93006, 2, 10, 61.97, -90.02, 0.10435, 54, 67.89, -50.57, 0.89565, 2, 10, 50.7, -83.28, 0.16118, 54, 56.73, -57.51, 0.83882, 2, 10, 38.56, -73.49, 0.29561, 54, 42.48, -63.82, 0.70439, 2, 10, 25.52, -63.09, 0.49247, 54, 27.27, -70.66, 0.50753, 3, 10, 11.47, -49.6, 0.61304, 54, 8.84, -76.99, 0.22696, 6, 46.04, -37.09, 0.16, 3, 10, -5.18, -31.88, 0.66622, 54, -14.53, -83.69, 0.04578, 6, 70.29, -35.28, 0.288, 3, 10, -20.68, -15.58, 0.35067, 54, -36.12, -90.02, 0.00133, 6, 92.71, -33.47, 0.648, 3, 10, -29.16, -7.04, 0.35198, 54, -47.59, -93.66, 2e-05, 6, 104.68, -32.23, 0.648, 2, 10, -36.09, 3, 0.352, 6, 116.85, -33.16, 0.648, 2, 10, -34.74, 9.99, 0.352, 6, 121.44, -38.6, 0.648, 2, 10, -31.53, 15.83, 0.352, 6, 123.97, -44.77, 0.648, 2, 10, -11.89, 2.55, 0.352, 6, 101.29, -51.7, 0.648, 2, 10, 15.16, -5.92, 0.99128, 54, -28.31, -53.73, 0.00872, 2, 10, 41.57, -15.12, 0.85169, 54, -8.05, -34.45, 0.14831, 2, 10, 63.75, -22.34, 0.32665, 54, 8.52, -18.03, 0.67335, 2, 10, 87.59, -30.73, 0.00075, 54, 26.87, -0.67, 0.99925], "hull": 27, "edges": [0, 52, 0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 44, 46, 46, 48, 48, 50, 50, 52, 32, 34, 34, 36, 40, 42, 42, 44, 2, 4, 4, 6, 36, 38, 38, 40, 50, 54, 54, 56, 56, 58, 58, 60, 60, 62, 60, 14, 62, 32], "width": 117, "height": 146}}, "a19": {"a19": {"type": "mesh", "uvs": [0.79355, 0.07225, 0.93781, 0.23722, 0.99526, 0.37079, 0.93077, 0.45077, 0.75063, 0.43657, 0.64677, 0.47011, 0.57152, 0.52774, 0.50573, 0.6807, 0.41238, 0.78584, 0.30077, 0.86668, 0.19214, 0.90447, 0.1084, 0.93751, 0.04829, 0.97517, 0.01425, 0.98264, 0.00449, 0.90032, 0.01545, 0.80124, 0.04652, 0.69374, 0.08328, 0.57612, 0.12963, 0.4668, 0.20211, 0.34538, 0.30427, 0.21797, 0.4284, 0.0922, 0.57011, 0.01788, 0.65153, 0.01784, 0.08518, 0.85124, 0.15184, 0.73561, 0.23565, 0.60034, 0.34803, 0.43888, 0.50041, 0.27524, 0.63375, 0.21197, 0.77089, 0.30579], "triangles": [5, 6, 28, 28, 29, 5, 4, 5, 30, 4, 30, 3, 3, 1, 2, 3, 30, 1, 30, 5, 29, 30, 0, 1, 8, 9, 26, 8, 26, 7, 26, 27, 7, 7, 27, 6, 17, 18, 26, 26, 18, 27, 27, 18, 19, 6, 27, 28, 19, 20, 27, 27, 20, 28, 30, 29, 0, 20, 21, 28, 28, 21, 29, 21, 22, 29, 29, 23, 0, 29, 22, 23, 11, 12, 24, 13, 24, 12, 13, 14, 24, 11, 24, 10, 9, 10, 25, 14, 15, 24, 10, 24, 25, 25, 24, 16, 24, 15, 16, 9, 25, 26, 16, 17, 25, 25, 17, 26], "vertices": [3, 81, -19.67, 8.25, 0.60267, 80, 19.21, 57.85, 0.30133, 118, 58.54, 71.01, 0.096, 2, 81, -18.5, 21.04, 0.33333, 80, 25.83, 46.84, 0.66667, 2, 81, -15.17, 28.52, 0.11111, 80, 27.56, 38.84, 0.88889, 2, 80, 22.55, 35.55, 0.896, 119, 32.3, 49.43, 0.104, 2, 80, 11.73, 39.06, 0.864, 119, 20.95, 50.22, 0.136, 4, 82, -24.11, 17.23, 0.00914, 81, 3.07, 15.14, 0.08686, 80, 4.93, 38.86, 0.768, 119, 14.41, 48.37, 0.136, 4, 82, -18.8, 15.16, 0.05133, 81, 8.54, 13.52, 0.23667, 80, -0.43, 36.94, 0.576, 119, 9.67, 45.2, 0.136, 4, 82, -9.53, 16.57, 0.16535, 81, 17.66, 15.71, 0.41065, 80, -6.5, 29.78, 0.288, 119, 5.52, 36.79, 0.136, 4, 82, -1.42, 15.06, 0.35421, 81, 25.86, 14.89, 0.41379, 80, -13.6, 25.6, 0.096, 119, -0.36, 31.01, 0.136, 3, 82, 6.25, 11.84, 0.57299, 81, 33.78, 12.33, 0.29101, 119, -7.39, 26.56, 0.136, 3, 82, 11.88, 7.42, 0.7441, 81, 39.75, 8.4, 0.1199, 119, -14.23, 24.48, 0.136, 3, 82, 16.39, 4.14, 0.83409, 81, 44.52, 5.5, 0.02991, 119, -19.51, 22.66, 0.136, 3, 82, 20.25, 2.22, 0.86113, 81, 48.54, 3.92, 0.00287, 119, -23.3, 20.59, 0.136, 2, 82, 21.82, 0.7, 0.864, 119, -25.44, 20.18, 0.136, 3, 82, 18.46, -2.4, 0.86068, 81, 47.14, -0.84, 0.00332, 119, -26.05, 24.71, 0.136, 2, 82, 13.59, -4.95, 0.93534, 81, 42.51, -3.79, 0.06466, 3, 82, 7.63, -6.73, 0.75589, 81, 36.71, -6.07, 0.2281, 118, 11.48, 36.82, 0.016, 3, 82, 1, -8.54, 0.48169, 81, 30.26, -8.43, 0.48631, 118, 13.8, 43.29, 0.032, 3, 82, -5.61, -9.59, 0.21514, 81, 23.77, -10.02, 0.72086, 118, 16.72, 49.31, 0.064, 3, 82, -13.7, -9.67, 0.05603, 81, 15.71, -10.79, 0.82397, 118, 21.28, 55.98, 0.12, 3, 82, -23.13, -8.4, 0.00431, 81, 6.21, -10.32, 0.89969, 118, 27.72, 62.99, 0.096, 2, 81, -4.11, -8.73, 0.904, 118, 35.54, 69.91, 0.096, 2, 81, -12.96, -4.48, 0.904, 118, 44.47, 74, 0.096, 3, 81, -16.24, -0.54, 0.80356, 80, 11.26, 62.92, 0.10044, 118, 49.6, 74, 0.096, 3, 82, 13.33, 0.22, 0.79388, 81, 41.81, 1.34, 0.07317, 119, -20.97, 27.41, 0.13296, 4, 82, 5.71, 0.02, 0.58067, 81, 34.24, 0.51, 0.28291, 118, 18.12, 34.52, 0.01219, 119, -16.77, 33.77, 0.12423, 5, 82, -3.41, 0.09, 0.26816, 81, 25.14, -0.19, 0.53907, 80, -21.93, 38.2, 0.03286, 118, 23.4, 41.96, 0.03455, 119, -11.49, 41.21, 0.12536, 5, 82, -14.74, 0.81, 0.06955, 81, 13.79, -0.43, 0.53119, 80, -12.91, 45.09, 0.20876, 118, 30.48, 50.84, 0.06109, 119, -4.41, 50.09, 0.12941, 5, 82, -27.62, 3.53, 0.01152, 81, 0.73, 1.2, 0.51611, 80, -1.41, 51.5, 0.29041, 118, 40.08, 59.84, 0.05218, 119, 5.19, 59.09, 0.12977, 5, 82, -35.28, 8.43, 0.00614, 81, -7.32, 5.43, 0.48734, 80, 7.58, 52.84, 0.33032, 118, 48.48, 63.32, 0.06162, 119, 13.59, 62.57, 0.11457, 5, 82, -36, 18.47, 0.00192, 81, -8.88, 15.37, 0.24555, 80, 14.71, 45.73, 0.59581, 118, 57.12, 58.16, 0.01924, 119, 22.23, 57.41, 0.13748], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 42, 44, 44, 46, 18, 20, 20, 22, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 8, 10, 10, 12, 26, 28, 28, 30, 26, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 63, "height": 55}}, "a12111111": {"a12": {"type": "mesh", "uvs": [0.56738, 0.00084, 0.69021, 0.03329, 0.79183, 0.07757, 0.88613, 0.14943, 0.95051, 0.24614, 0.98391, 0.26995, 0.99551, 0.31087, 0.99567, 0.44154, 0.97286, 0.55981, 0.91518, 0.69378, 0.83348, 0.80301, 0.74071, 0.88836, 0.6373, 0.95206, 0.5186, 0.98578, 0.39407, 0.9972, 0.27263, 0.97632, 0.18177, 0.93173, 0.09269, 0.8642, 0.03566, 0.76287, 0.00436, 0.65349, 0.00416, 0.5909, 0.05122, 0.50405, 0.1063, 0.41906, 0.16699, 0.33857, 0.2338, 0.25175, 0.3138, 0.16056, 0.4073, 0.06717, 0.5015, 0.01214, 0.05782, 0.5964, 0.11193, 0.70224, 0.1918, 0.79908, 0.28842, 0.85538, 0.38504, 0.86439, 0.50228, 0.85763, 0.6105, 0.8261, 0.71356, 0.76755, 0.80374, 0.69999, 0.88619, 0.60766, 0.92613, 0.49957, 0.95189, 0.37346], "triangles": [36, 35, 1, 10, 36, 9, 36, 1, 2, 36, 37, 9, 38, 37, 2, 37, 36, 2, 9, 37, 8, 37, 38, 8, 38, 2, 3, 8, 38, 7, 38, 39, 7, 39, 3, 4, 39, 38, 3, 39, 6, 7, 39, 5, 6, 39, 4, 5, 13, 34, 12, 12, 35, 11, 12, 34, 35, 10, 35, 36, 10, 11, 35, 35, 34, 0, 16, 17, 30, 14, 15, 32, 15, 31, 32, 14, 33, 13, 14, 32, 33, 13, 33, 34, 15, 16, 31, 16, 30, 31, 33, 32, 25, 32, 31, 24, 31, 30, 23, 26, 34, 33, 29, 23, 30, 29, 22, 23, 17, 29, 30, 17, 18, 29, 18, 28, 29, 18, 19, 28, 28, 22, 29, 19, 20, 28, 20, 21, 28, 28, 21, 22, 31, 23, 24, 24, 25, 32, 33, 25, 26, 26, 27, 34, 27, 0, 34, 35, 0, 1], "vertices": [6, 57, 12.21, 14.51, 0.63322, 58, -43.15, 14.81, 0.03704, 59, -88.04, 11.57, 0, 72, -11.26, -107.41, 4e-05, 71, 7.25, -96.53, 0.02351, 70, 5.28, -84.21, 0.30619, 5, 57, -6.92, 34.37, 0.48184, 59, -107.6, 31, 0, 72, -38.04, -100.81, 3e-05, 71, -15.48, -80.91, 0.02335, 70, -4.9, -58.58, 0.49478, 5, 57, -21.41, 52.56, 0.29397, 59, -122.48, 48.87, 0, 72, -59.99, -93.15, 1e-05, 71, -33.32, -65.99, 0.01742, 70, -11.41, -36.26, 0.68859, 5, 57, -32.47, 72.54, 0.14698, 59, -133.98, 68.6, 0, 72, -80, -82.14, 0, 71, -48.16, -48.64, 0.00935, 70, -14.07, -13.58, 0.84366, 3, 57, -36.34, 90.98, 0.05617, 71, -55.65, -31.34, 0.00341, 70, -10.66, 4.95, 0.94042, 3, 57, -40.38, 97.89, 0.01509, 71, -61, -25.38, 0.00069, 70, -11.79, 12.88, 0.98423, 3, 57, -39.26, 103.58, 0.00217, 71, -61.04, -19.58, 0.00142, 70, -8.6, 17.72, 0.99641, 3, 57, -29.19, 116.78, 2e-05, 71, -53.82, -4.64, 0.02758, 70, 5.72, 26.12, 0.9724, 2, 71, -42.71, 6.66, 0.1156, 70, 21.25, 29.31, 0.8844, 3, 72, -80.1, -12.71, 0.00166, 71, -23.76, 16.37, 0.29665, 70, 42.39, 26.82, 0.70169, 3, 72, -60.77, -0.56, 0.04103, 71, -1.38, 20.92, 0.50152, 70, 63.52, 18.15, 0.45745, 3, 72, -39.27, 8.35, 0.15514, 71, 21.88, 21.67, 0.61908, 70, 83.26, 5.82, 0.22577, 5, 59, -29.2, 118.21, 0.00136, 60, -54.97, 122.23, 0.00372, 72, -15.67, 14.31, 0.37097, 71, 46.06, 18.92, 0.5474, 70, 101.81, -9.93, 0.07655, 5, 59, -5.41, 106.1, 0.00786, 60, -32.05, 108.53, 0.02376, 72, 10.96, 16.17, 0.60104, 71, 71.64, 11.26, 0.35371, 70, 118.79, -30.54, 0.01363, 4, 59, 17.76, 90.94, 0.02237, 60, -9.97, 91.83, 0.08393, 72, 38.62, 15.08, 0.74459, 71, 97.14, 0.48, 0.14912, 4, 59, 37.94, 72.87, 0.04158, 60, 8.94, 72.43, 0.20542, 72, 65.23, 9.98, 0.71464, 71, 120.23, -13.68, 0.03835, 4, 59, 50.88, 56.39, 0.05418, 60, 20.72, 55.11, 0.38721, 72, 84.8, 2.49, 0.55762, 71, 135.9, -27.59, 0.00099, 3, 59, 61.76, 37.79, 0.05155, 60, 30.33, 35.81, 0.59484, 72, 103.7, -7.86, 0.35361, 3, 59, 64.38, 19.92, 0.03541, 60, 31.72, 17.81, 0.77943, 72, 115.13, -21.83, 0.18516, 4, 58, 106.4, 3.5, 1e-05, 59, 61.78, 4.61, 0.04512, 60, 28.09, 2.71, 0.87724, 72, 120.78, -36.3, 0.07763, 4, 58, 101.56, -2.8, 0.00447, 59, 57.12, -1.83, 0.10959, 60, 23, -3.4, 0.85651, 72, 120.1, -44.22, 0.02944, 4, 58, 86.54, -5.1, 0.04005, 59, 42.17, -4.56, 0.23659, 60, 7.91, -5.11, 0.69224, 72, 108.69, -54.25, 0.03113, 4, 58, 70.26, -6.11, 0.18232, 59, 25.93, -6.04, 0.40782, 60, -8.4, -5.49, 0.40404, 72, 95.53, -63.88, 0.00583, 5, 57, 108.85, -5.53, 0.03704, 58, 53.35, -5.9, 0.37492, 59, 9.02, -6.33, 0.43067, 60, -25.29, -4.63, 0.15701, 72, 81.17, -72.82, 0.00036, 7, 57, 90.37, -5.26, 0.14344, 58, 34.87, -5.5, 0.51711, 59, -9.46, -6.47, 0.29758, 60, -43.74, -3.51, 0.03717, 72, 65.4, -82.45, 0, 71, 87.78, -100.22, 0.00075, 70, 70.12, -132.12, 0.00396, 7, 57, 69.23, -3.64, 0.34712, 58, 13.74, -3.74, 0.49929, 59, -30.63, -5.32, 0.1303, 60, -64.78, -0.92, 4e-05, 72, 46.65, -92.36, 1e-05, 71, 66.75, -102.88, 0.00347, 70, 51.17, -122.62, 0.01977, 6, 57, 45.54, -0.43, 0.55443, 58, -9.92, -0.36, 0.33927, 59, -54.39, -2.63, 0.03111, 72, 24.9, -102.27, 2e-05, 71, 42.89, -104.48, 0.0096, 70, 30.47, -110.67, 0.06558, 6, 57, 24.69, 6.75, 0.677, 58, -30.72, 6.96, 0.14815, 59, -75.39, 4.09, 0, 72, 3.43, -107.32, 3e-05, 71, 21.03, -101.63, 0.01751, 70, 13.89, -96.12, 0.15731, 4, 58, 92.58, 5.06, 0.00447, 59, 47.92, 5.77, 0.15571, 60, 14.34, 4.81, 0.71923, 72, 108.3, -42.43, 0.12059, 6, 57, 146.65, 23.68, 1e-05, 58, 91.36, 23.05, 5e-05, 59, 46.17, 23.71, 0.11705, 60, 13.81, 22.83, 0.60542, 72, 97.57, -27.95, 0.27747, 71, 137.12, -60.58, 0, 7, 57, 140.06, 44.23, 4e-05, 58, 84.91, 43.64, 0.00011, 59, 39.13, 44.11, 0.09957, 60, 8.17, 43.66, 0.41064, 72, 81.04, -14.08, 0.48957, 71, 126.54, -41.77, 7e-05, 70, 134.86, -105.15, 0, 7, 57, 127.39, 62.96, 9e-05, 58, 72.37, 62.45, 0.00019, 59, 26.04, 62.55, 0.07995, 60, -3.63, 62.94, 0.2146, 72, 60.33, -5, 0.66794, 71, 110.37, -25.96, 0.03723, 70, 130.23, -83.02, 0, 7, 57, 111.06, 76.92, 0.00012, 58, 56.14, 76.53, 0.00022, 59, 9.41, 76.15, 0.04584, 60, -19.3, 77.64, 0.08464, 72, 39.08, -1.9, 0.72067, 71, 91.57, -15.56, 0.14851, 70, 120.41, -63.92, 0, 7, 57, 89.88, 92.07, 0.00012, 58, 35.07, 91.83, 0.00019, 59, -12.1, 90.83, 0.01742, 60, -39.76, 93.74, 0.02298, 72, 13.08, -0.37, 0.58852, 71, 67.79, -4.96, 0.35303, 70, 106.56, -41.87, 0.01776, 7, 57, 68.38, 103.51, 7e-05, 58, 13.65, 103.42, 0.0001, 59, -33.85, 101.79, 0.00378, 60, -60.72, 106.16, 0.0033, 72, -11.21, -2.16, 0.36276, 71, 44.43, 1.93, 0.53988, 70, 90.99, -23.14, 0.0901, 7, 57, 45.7, 111.53, 3e-05, 58, -8.98, 111.59, 4e-05, 59, -56.7, 109.31, 0.00022, 60, -83.01, 115.21, 0, 72, -34.67, -7.48, 0.14769, 71, 20.6, 5.24, 0.59797, 70, 73.04, -7.12, 0.25406, 6, 57, 24.59, 116.9, 1e-05, 58, -30.05, 117.11, 0, 59, -77.92, 114.21, 1e-05, 72, -55.39, -14.19, 0.03696, 71, -1.16, 6.27, 0.4696, 70, 55.54, 5.85, 0.49342, 2, 71, -22.75, 3.72, 0.26528, 70, 36.19, 15.75, 0.73472, 2, 71, -36.72, -4.76, 0.09622, 70, 19.87, 16.49, 0.90378, 2, 71, -48.86, -16.67, 0.03, 70, 3.15, 13.36, 0.97], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 50, 52, 52, 54, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 30, 32, 32, 34, 18, 20, 42, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78], "width": 222, "height": 127}}, "a1411": {"a14": {"type": "mesh", "uvs": [0.34529, 0, 0.54678, 0.00657, 0.74955, 0.04858, 0.89425, 0.10801, 0.9919, 0.18275, 1, 0.35625, 0.95941, 0.56573, 0.92532, 0.69522, 0.72592, 0.80608, 0.57118, 0.84853, 0.45563, 0.84861, 0.45887, 0.86479, 0.52681, 0.95356, 0.41754, 0.99513, 0.25473, 0.99318, 0.19896, 0.96079, 0.19156, 0.92846, 0.25375, 0.84938, 0.25181, 0.82294, 0.11711, 0.74943, 1e-05, 0.63684, 0.00553, 0.53664, 0.04773, 0.3666, 0.13006, 0.18944, 0.2723, 0.03132, 0.57453, 0.22978, 0.47093, 0.45712, 0.40787, 0.66412, 0.35713, 0.84405, 0.35452, 0.87717], "triangles": [14, 29, 13, 13, 29, 11, 13, 11, 12, 10, 29, 28, 29, 10, 11, 29, 14, 16, 29, 16, 17, 14, 15, 16, 29, 17, 28, 17, 18, 28, 9, 10, 27, 8, 9, 27, 27, 10, 28, 28, 18, 27, 18, 19, 27, 19, 20, 27, 8, 27, 26, 20, 21, 27, 27, 21, 26, 7, 8, 26, 7, 26, 6, 5, 6, 26, 5, 26, 25, 25, 2, 3, 4, 25, 3, 25, 4, 5, 21, 22, 26, 22, 23, 26, 26, 23, 25, 25, 23, 24, 25, 0, 1, 0, 25, 24, 25, 1, 2], "vertices": [1, 20, -42.76, -23.44, 1, 1, 20, -46.09, 0.32, 1, 1, 20, -44.39, 25.15, 1, 2, 20, -38.98, 43.63, 0.99917, 21, -78.62, 43.92, 0.00083, 2, 20, -30.39, 56.99, 0.99299, 21, -69.97, 57.26, 0.00701, 2, 20, -5.8, 62.42, 0.94484, 21, -45.37, 62.59, 0.05516, 3, 20, 24.95, 63.08, 0.74912, 21, -14.62, 63.13, 0.25083, 32, 25.51, 77.03, 5e-05, 3, 20, 44.15, 62.43, 0.59353, 21, 4.58, 62.41, 0.40293, 32, 35.29, 60.5, 0.00354, 3, 20, 64.19, 41.94, 0.2944, 21, 24.55, 41.85, 0.65683, 32, 28.8, 32.58, 0.04876, 3, 20, 73.52, 24.92, 0.08873, 21, 33.82, 24.79, 0.7308, 32, 19.47, 15.56, 0.18047, 3, 20, 75.98, 11.39, 0.00783, 21, 36.23, 11.25, 0.34684, 32, 9.39, 6.21, 0.64533, 3, 20, 78.22, 12.18, 0.00273, 21, 38.47, 12.04, 0.138, 32, 11.26, 4.75, 0.85927, 1, 32, 25.94, 0.79, 1, 1, 32, 20.5, -12.46, 1, 2, 21, 61.04, -8.63, 0.00205, 32, 6.09, -25.42, 0.99795, 2, 21, 57.57, -15.99, 0.02206, 32, -1.97, -26.48, 0.97794, 2, 21, 53.11, -17.67, 0.04336, 32, -5.8, -23.64, 0.95664, 3, 20, 80.37, -12.23, 0.00031, 21, 40.53, -12.38, 0.47768, 32, -8.16, -10.2, 0.52201, 3, 20, 76.64, -13.14, 0.00392, 21, 36.79, -13.28, 0.79472, 32, -10.94, -7.54, 0.20136, 2, 20, 69.01, -30.81, 0.10962, 21, 29.09, -30.92, 0.89038, 2, 20, 55.42, -47.43, 0.31955, 21, 15.44, -47.49, 0.68045, 2, 20, 41.01, -49.37, 0.49019, 21, 1.02, -49.38, 0.50981, 2, 20, 15.85, -48.82, 0.83769, 21, -24.13, -48.73, 0.16231, 2, 20, -11.17, -43.76, 0.98785, 21, -51.13, -43.56, 0.01215, 1, 20, -36.74, -31.18, 1, 1, 20, -14.83, 9.33, 1, 2, 20, 19.8, 3.07, 0.99789, 21, -19.99, 3.15, 0.00211, 3, 20, 50.67, 1.03, 0.00232, 21, 10.88, 0.99, 0.99726, 32, -12.96, 21.97, 0.00042, 2, 21, 37.62, -0.4, 0.26766, 32, 0.34, -1.27, 0.73234, 2, 21, 42.4, 0.13, 0.00356, 32, 3.37, -5.01, 0.99644], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 8, 10, 4, 50, 50, 52, 52, 54, 54, 56, 54, 40, 32, 34, 34, 36, 20, 22, 22, 24, 56, 58], "width": 119, "height": 145}}, "a2111111": {"a2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.7, -132.3, -103.93, -79.57, -18.67, 109.05, 97.97, 56.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 207}}, "skill/skill0/dg01/shui_00011": {"jushui/shui_00011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [61.25, -53.25, -61.25, -53.25, -61.25, 53.25, 61.25, 53.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 114, "height": 55}, "jushui/shui_00013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [61, -60.5, -61, -60.5, -61, 60.5, 61, 60.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 122, "height": 121}, "jushui/shui_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [71, -72.5, -71, -72.5, -71, 72.5, 71, 72.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 145}, "jushui/shui_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [79.5, -74, -79.5, -74, -79.5, 74, 79.5, 74], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 159, "height": 148}, "jushui/shui_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [85, -70, -85, -70, -85, 70, 85, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 170, "height": 140}, "jushui/shui_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [84.5, -53, -84.5, -53, -84.5, 53, 84.5, 53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 169, "height": 106}, "jushui/shui_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -45, -80, -45, -80, 45, 80, 45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 90}}, "a9111111": {"a9": {"type": "mesh", "uvs": [1, 0.6257, 0.96512, 0.5657, 0.92133, 0.5147, 0.87978, 0.4767, 0.86642, 0.36903, 0.84267, 0.24503, 0.81349, 0.10473, 0.78628, 0.0392, 0.75066, 0, 0.72914, 0, 0.64838, 0.09514, 0.55487, 0.21185, 0.46795, 0.32011, 0.38512, 0.36248, 0.31091, 0.41548, 0.253, 0.46612, 0.1966, 0.52312, 0.14317, 0.57912, 0.09511, 0.63837, 0.04988, 0.70181, 0.01572, 0.76649, 0, 0.82371, 0, 0.87471, 0.02066, 0.87245, 0.04126, 0.8332, 0.08318, 0.8332, 0.13859, 0.86766, 0.20036, 0.905, 0.26429, 0.94903, 0.31828, 0.98637, 0.37796, 1, 0.4374, 0.98733, 0.48855, 0.94999, 0.53677, 0.89392, 0.57371, 0.82212, 0.59502, 0.74266, 0.61491, 0.66703, 0.64758, 0.59392, 0.70867, 0.55659, 0.78256, 0.54893, 0.85314, 0.55452, 0.91779, 0.58803, 0.96396, 0.6244, 1, 0.6694, 0.71333, 0.19003, 0.71333, 0.32798, 0.21375, 0.77541, 0.28413, 0.69166, 0.36273, 0.60421, 0.44865, 0.51306, 0.52634, 0.43916, 0.61592, 0.37141, 0.7989, 0.44333], "triangles": [25, 19, 18, 22, 21, 23, 23, 21, 24, 25, 20, 19, 21, 20, 24, 25, 24, 20, 31, 30, 47, 32, 31, 47, 29, 47, 30, 48, 33, 32, 27, 46, 28, 47, 29, 28, 28, 46, 47, 27, 26, 46, 32, 47, 48, 33, 48, 34, 26, 25, 46, 25, 18, 46, 34, 48, 35, 18, 17, 46, 46, 17, 47, 17, 16, 47, 16, 15, 47, 47, 15, 48, 15, 14, 48, 35, 48, 49, 35, 49, 36, 49, 50, 36, 36, 50, 37, 49, 14, 13, 50, 51, 37, 49, 12, 50, 49, 13, 12, 50, 12, 51, 12, 11, 51, 49, 48, 14, 11, 10, 44, 42, 0, 43, 42, 1, 0, 42, 41, 1, 37, 51, 38, 41, 2, 1, 41, 40, 2, 39, 38, 52, 38, 51, 45, 40, 3, 2, 3, 40, 52, 40, 39, 52, 52, 38, 45, 52, 4, 3, 52, 45, 4, 45, 51, 44, 45, 5, 4, 45, 44, 5, 44, 6, 5, 44, 7, 6, 7, 44, 8, 44, 9, 8, 44, 10, 9, 51, 11, 44], "vertices": [4, 13, 58, 57.32, 1, 57, -39.79, 102.9, 0, 58, -94.52, 103.56, 0, 59, -141.97, 98.78, 0, 4, 13, 49.97, 47.74, 1, 57, -39.66, 90.4, 0, 58, -94.48, 91.06, 0, 59, -141.57, 86.29, 0, 4, 13, 43.8, 36.53, 1, 57, -37.07, 77.87, 0, 58, -91.98, 78.51, 0, 59, -138.7, 73.83, 0, 4, 13, 39.61, 26.26, 1, 57, -33.57, 67.34, 0, 58, -88.55, 67.96, 0, 59, -134.97, 63.37, 0, 4, 13, 22.95, 19.7, 1, 57, -41.97, 51.53, 0, 58, -97.07, 52.2, 0, 59, -143.02, 47.38, 0, 4, 13, 4.15, 10.34, 1, 57, -50.18, 32.2, 0, 58, -105.41, 32.93, 0, 59, -150.8, 27.87, 0, 1, 13, -17.02, -0.74, 1, 1, 13, -26.28, -8.86, 1, 1, 13, -30.93, -17.89, 1, 2, 13, -29.95, -22.55, 0.89195, 57, -54.72, -14.96, 0.10805, 2, 13, -10.98, -36.76, 0.67279, 57, -31.06, -13.44, 0.32721, 3, 13, 12.04, -53, 0.34252, 57, -3.01, -10.82, 0.65322, 58, -58.54, -10.42, 0.00426, 3, 13, 33.4, -68.11, 0.11724, 57, 23.03, -8.42, 0.79018, 58, -32.48, -8.19, 0.09259, 3, 13, 44, -84.57, 0.00306, 57, 41.79, -14.04, 0.70514, 58, -13.76, -13.95, 0.2918, 2, 57, 60.09, -17.12, 0.40639, 58, 4.52, -17.15, 0.59361, 3, 57, 75.3, -18.32, 0.16162, 58, 19.72, -18.46, 0.81907, 59, -24.23, -19.85, 0.01931, 4, 57, 90.88, -18.48, 0.0275, 58, 35.3, -18.73, 0.85034, 59, -8.65, -19.67, 0.11998, 60, -43.83, -16.74, 0.00219, 4, 57, 105.84, -18.38, 0.00023, 58, 50.26, -18.73, 0.66644, 59, 6.3, -19.24, 0.27951, 60, -28.88, -17.32, 0.05382, 3, 58, 64.61, -17.58, 0.38302, 59, 20.61, -17.68, 0.40383, 60, -14.5, -16.73, 0.21315, 3, 58, 78.88, -15.51, 0.15533, 59, 34.82, -15.19, 0.36545, 60, -0.15, -15.21, 0.47922, 3, 58, 91.36, -11.77, 0.03317, 59, 47.18, -11.09, 0.20934, 60, 12.46, -11.96, 0.75749, 3, 58, 99.86, -6.49, 0.0028, 59, 55.53, -5.57, 0.0657, 60, 21.16, -7.02, 0.9315, 2, 59, 60.47, 1.18, 0.00342, 60, 26.55, -0.62, 0.99658, 2, 59, 56.56, 3.58, 0.01339, 60, 22.82, 2.04, 0.98661, 3, 13, 135.29, -142.87, 0, 59, 49.09, 1.08, 0.11301, 60, 15.19, 0.05, 0.88699, 4, 13, 133.37, -133.81, 0.0001, 57, 141.72, 6.62, 0.00015, 59, 41.61, 6.55, 0.35164, 60, 8.1, 6.01, 0.64811, 5, 13, 136.36, -120.66, 0.00077, 57, 135.44, 18.56, 0.00204, 58, 80.11, 18, 0.01583, 59, 35.07, 18.34, 0.69539, 60, 2.37, 18.22, 0.28598, 5, 13, 139.51, -106.03, 0.00241, 57, 128.33, 31.72, 0.00755, 58, 73.09, 31.22, 0.00052, 59, 27.67, 31.35, 0.864, 60, -4.12, 31.7, 0.12551, 5, 13, 143.64, -90.71, 0.00577, 57, 121.51, 46.05, 0.01974, 58, 66.37, 45.59, 0.00293, 59, 20.54, 45.52, 0.94046, 60, -10.28, 46.33, 0.0311, 5, 13, 147.15, -77.77, 0.01103, 57, 115.77, 58.17, 0.03952, 58, 60.71, 57.75, 0.00925, 59, 14.53, 57.51, 0.93707, 60, -15.46, 58.69, 0.00313, 5, 13, 146.6, -64.4, 0.01851, 57, 106.66, 67.97, 0.06756, 58, 51.68, 67.61, 0.02077, 59, 5.21, 67.1, 0.89311, 60, -24.11, 68.9, 4e-05, 4, 13, 141.84, -51.98, 0.02924, 57, 94.97, 74.31, 0.10553, 58, 40.03, 74.04, 0.03722, 59, -6.62, 73.19, 0.82801, 4, 13, 133.5, -42.2, 0.04521, 57, 82.27, 76.33, 0.1561, 58, 27.35, 76.14, 0.05604, 59, -19.36, 74.93, 0.74265, 4, 13, 122.29, -33.68, 0.07016, 57, 68.22, 75.52, 0.22172, 58, 13.29, 75.43, 0.07259, 59, -33.39, 73.8, 0.63554, 4, 13, 109.08, -28.14, 0.11143, 57, 54.58, 71.14, 0.299, 58, -0.38, 71.14, 0.08015, 59, -46.93, 69.12, 0.50941, 4, 13, 95.35, -26.24, 0.18857, 57, 42.91, 63.66, 0.3674, 58, -12.1, 63.75, 0.07328, 59, -58.43, 61.39, 0.37075, 4, 13, 82.31, -24.52, 0.33208, 57, 31.88, 56.49, 0.37946, 58, -23.19, 56.66, 0.05337, 59, -69.3, 53.98, 0.23508, 4, 13, 69.08, -19.95, 0.5372, 57, 18.85, 51.37, 0.31145, 58, -36.25, 51.63, 0.0291, 59, -82.21, 48.57, 0.12225, 4, 13, 60.28, -8.01, 0.75585, 57, 4.41, 54.73, 0.18493, 58, -50.66, 55.08, 0.01098, 59, -96.72, 51.61, 0.04824, 4, 13, 55.66, 7.7, 0.91153, 57, -9.31, 63.67, 0.07358, 58, -64.32, 64.11, 0.00229, 59, -110.64, 60.24, 0.01259, 4, 13, 53.32, 23.15, 0.98428, 57, -21.13, 73.88, 0.014, 58, -76.07, 74.41, 0.00014, 59, -122.68, 70.19, 0.00157, 4, 13, 55.73, 38.27, 1, 57, -29.13, 86.94, 0, 58, -83.98, 87.52, 0, 59, -130.96, 83.07, 0, 4, 13, 59.44, 49.49, 1, 57, -33.6, 97.88, 0, 58, -88.37, 98.5, 0, 59, -135.67, 93.91, 0, 4, 13, 65.01, 58.81, 1, 57, -35.43, 108.58, 0, 58, -90.12, 109.21, 0, 59, -137.74, 104.57, 0, 1, 13, 1.27, -19.49, 1, 1, 13, 23.4, -14.79, 1, 5, 13, 118.11, -107.55, 0.00179, 57, 113.05, 16.66, 0.01243, 58, 57.71, 16.26, 0.11999, 59, 12.73, 15.95, 0.74585, 60, -20.07, 17.35, 0.11994, 5, 13, 101.44, -95.19, 0.00658, 57, 92.35, 15.22, 0.11407, 58, 37, 14.96, 0.23026, 59, -7.93, 14.05, 0.62361, 60, -40.82, 16.86, 0.02548, 5, 13, 83.8, -81.18, 0.02077, 57, 69.84, 14.41, 0.31325, 58, 14.48, 14.31, 0.29851, 59, -30.42, 12.74, 0.36717, 60, -63.35, 17.08, 0.0003, 4, 13, 65.24, -65.71, 0.06564, 57, 45.67, 14.1, 0.56898, 58, -9.68, 14.17, 0.21116, 59, -54.57, 11.9, 0.15421, 4, 13, 49.81, -51.43, 0.12478, 57, 24.67, 14.93, 0.73248, 58, -30.68, 15.14, 0.10115, 59, -75.59, 12.26, 0.04159, 4, 13, 34.84, -34.37, 0.1654, 57, 2.2, 18.15, 0.79875, 58, -53.13, 18.52, 0.01771, 59, -98.12, 14.99, 0.01814, 4, 13, 37.97, 7.64, 1, 57, -22.71, 52.12, 0, 58, -77.81, 52.66, 0, 59, -123.78, 48.4, 0], "hull": 44, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 0, 86, 16, 88, 88, 90, 52, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 6, 104], "width": 221, "height": 164}}, "a10111111": {"a10": {"type": "mesh", "uvs": [0.50509, 0.01703, 0.69215, 0.09852, 0.87272, 0.17719, 0.95279, 0.29314, 0.99999, 0.5615, 0.97464, 0.80797, 0.83854, 0.88625, 0.67077, 0.95093, 0.52391, 0.98296, 0.35835, 0.98618, 0.20801, 0.90597, 0.08256, 0.72391, 0.01709, 0.54247, 0.00909, 0.3407, 0.03903, 0.21025, 0.16973, 0.04832, 0.31115, 0.00518, 0.19482, 0.37777, 0.81653, 0.52673, 0.5011, 0.51018], "triangles": [18, 2, 3, 17, 15, 16, 14, 15, 17, 13, 14, 17, 19, 16, 0, 19, 0, 1, 17, 16, 19, 18, 1, 2, 19, 1, 18, 12, 13, 17, 18, 3, 4, 11, 12, 17, 5, 18, 4, 6, 18, 5, 10, 17, 19, 11, 17, 10, 7, 19, 18, 7, 18, 6, 19, 9, 10, 7, 8, 19, 8, 9, 19], "vertices": [2, 14, 10.11, 20.54, 0.98381, 15, -22.62, 27.27, 0.01619, 2, 14, 26.25, 17.57, 0.7661, 15, -7.98, 19.86, 0.2339, 2, 14, 41.83, 14.7, 0.17653, 15, 6.15, 12.7, 0.82347, 2, 14, 49.25, 8.75, 0.01702, 15, 11.59, 4.9, 0.98298, 2, 14, 54.9, -6.29, 0.65421, 15, 12.75, -11.12, 0.34579, 2, 14, 54.35, -20.73, 0.97609, 15, 8.15, -24.82, 0.02391, 1, 14, 43.48, -26.5, 1, 1, 14, 29.88, -31.77, 1, 1, 14, 17.83, -34.97, 1, 1, 14, 4.02, -36.68, 1, 1, 14, -9.04, -33.44, 1, 1, 14, -20.67, -24.1, 1, 1, 14, -27.29, -14.24, 1, 1, 14, -29.24, -2.68, 1, 1, 14, -27.57, 5.11, 1, 1, 14, -17.69, 15.65, 1, 1, 14, -6.16, 19.44, 1, 1, 14, -13.5, -3.11, 1, 1, 14, 39.36, -5.97, 1, 1, 14, 12.92, -7.92, 1], "hull": 17, "edges": [0, 32, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 30, 32, 28, 34, 8, 36, 36, 38, 24, 26, 28, 30, 10, 12, 0, 2, 2, 4], "width": 84, "height": 58}}, "a20": {"a20": {"type": "mesh", "uvs": [0.14335, 0, 0.33396, 0, 0.49093, 0.069, 0.56775, 0.17851, 0.56844, 0.29098, 0.64138, 0.43913, 0.64738, 0.583, 0.67918, 0.61939, 0.70178, 0.67841, 0.73029, 0.70987, 0.78255, 0.73236, 0.9096, 0.78706, 0.98669, 0.86916, 0.98702, 0.91279, 0.9597, 0.95672, 0.8949, 0.95688, 0.89157, 0.99288, 0.759, 0.98846, 0.56315, 0.98195, 0.42733, 0.98246, 0.28728, 0.98299, 0.28226, 0.91253, 0.31452, 0.86948, 0.29099, 0.85419, 0.18413, 0.78472, 0.06883, 0.65963, 0.06173, 0.51158, 0.04405, 0.43203, 0.01236, 0.33015, 0.01339, 0.2092, 0.06305, 0.07673, 0.31381, 0.27598, 0.37811, 0.54473, 0.51, 0.78696], "triangles": [16, 17, 15, 18, 9, 17, 17, 10, 15, 17, 9, 10, 9, 33, 8, 9, 18, 33, 13, 14, 15, 15, 10, 11, 13, 15, 12, 12, 15, 11, 33, 7, 8, 33, 6, 7, 20, 21, 19, 21, 22, 19, 18, 19, 33, 19, 22, 33, 22, 23, 33, 33, 23, 32, 23, 24, 32, 33, 32, 6, 24, 25, 32, 25, 26, 32, 32, 5, 6, 26, 31, 32, 32, 4, 5, 32, 31, 4, 26, 27, 31, 27, 28, 31, 28, 29, 31, 31, 3, 4, 29, 30, 31, 30, 0, 31, 31, 2, 3, 31, 1, 2, 31, 0, 1], "vertices": [1, 98, -5.44, -13.15, 1, 1, 98, -5.82, 0.95, 1, 3, 98, -1.38, 12.69, 0.99356, 99, -13.76, 32.14, 0.0011, 102, -42.4, 23.72, 0.00534, 3, 98, 6.02, 18.57, 0.95211, 99, -4.38, 30.98, 0.02164, 102, -32.98, 24.52, 0.02626, 3, 98, 13.78, 18.83, 0.82163, 99, 1.24, 25.63, 0.09446, 102, -26.38, 20.44, 0.08391, 3, 98, 23.85, 24.51, 0.4404, 99, 12.34, 22.41, 0.25644, 102, -14.85, 19.57, 0.30316, 3, 98, 33.76, 25.22, 0.1324, 99, 19.79, 15.83, 0.18751, 102, -6.21, 14.67, 0.68009, 3, 98, 36.21, 27.64, 0.06408, 99, 23.23, 15.78, 0.08486, 102, -2.83, 15.33, 0.85106, 3, 98, 40.24, 29.42, 0.02154, 99, 27.32, 14.16, 0.01462, 102, 1.51, 14.58, 0.96384, 3, 98, 42.35, 31.59, 0.00639, 99, 30.35, 14.17, 0.00068, 102, 4.47, 15.21, 0.99293, 2, 98, 43.8, 35.49, 0.00057, 102, 7.84, 17.66, 0.99943, 1, 102, 16.04, 23.61, 1, 1, 102, 23.87, 25.43, 1, 1, 102, 26.43, 23.85, 1, 1, 102, 27.92, 20.53, 1, 1, 102, 25.38, 16.46, 1, 1, 102, 27.35, 14.93, 1, 1, 102, 21.88, 6.79, 1, 2, 99, 35.27, -7.77, 0.11281, 102, 13.79, -5.25, 0.88719, 2, 99, 28.31, -15.03, 0.74168, 102, 8.48, -13.78, 0.25832, 2, 99, 21.14, -22.51, 0.97911, 102, 3, -22.57, 0.02089, 2, 99, 17.38, -19.4, 0.98558, 102, -1.32, -20.3, 0.01442, 2, 99, 16.9, -15.62, 0.99023, 102, -2.56, -16.7, 0.00977, 3, 98, 53.18, -0.64, 0.00102, 99, 14.94, -16.14, 0.99743, 102, -4.38, -17.62, 0.00155, 2, 98, 48.6, -8.67, 0.07915, 99, 5.99, -18.5, 0.92085, 2, 98, 40.21, -17.44, 0.43838, 99, -6.14, -18.64, 0.56162, 2, 98, 30.01, -18.24, 0.82355, 99, -13.86, -11.92, 0.17645, 2, 98, 24.56, -19.69, 0.96034, 99, -18.71, -9.05, 0.03966, 2, 98, 17.59, -22.23, 0.99931, 99, -25.4, -5.85, 0.00069, 1, 98, 9.25, -22.38, 1, 1, 98, 0.01, -18.95, 1, 1, 98, 13.25, -0.03, 1, 3, 98, 31.66, 5.23, 0.22335, 99, 4.05, 3.33, 0.76002, 102, -19.04, -0.8, 0.01664, 1, 99, 22.86, -1.26, 1], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 40, 42, 54, 56, 56, 58, 58, 60, 42, 44, 48, 50, 50, 52, 52, 54, 2, 62, 62, 64, 64, 66, 14, 16, 16, 18, 18, 20, 20, 22, 10, 12, 12, 14, 44, 46, 46, 48, 66, 46, 36, 38, 38, 40, 66, 38, 32, 34, 34, 36], "width": 74, "height": 69}}, "a21": {"a21": {"type": "mesh", "uvs": [0.83811, 0.06374, 0.98023, 0.1364, 0.98046, 0.40952, 0.87338, 0.57105, 0.73976, 0.6217, 0.58857, 0.62345, 0.54542, 0.64279, 0.67012, 0.70579, 0.70729, 0.72457, 0.84513, 0.7072, 0.95708, 0.91912, 0.76195, 0.96737, 0.71062, 0.98866, 0.50012, 0.98508, 0.30528, 0.98505, 0.25933, 0.95982, 0.23093, 0.86499, 0.22068, 0.83077, 0.14931, 0.76627, 0.07524, 0.69934, 0, 0.63134, 0, 0.63088, 0.04933, 0.56788, 0.12979, 0.48906, 0.15301, 0.39233, 0.19708, 0.19176, 0.33564, 0, 0.65253, 0, 0.29419, 0.64056, 0.40723, 0.50829, 0.65811, 0.29447, 0.48092, 0.80011], "triangles": [31, 6, 7, 31, 7, 8, 11, 8, 9, 11, 9, 10, 31, 8, 11, 14, 15, 31, 13, 14, 31, 31, 12, 13, 11, 12, 31, 21, 19, 20, 22, 23, 28, 28, 21, 22, 28, 19, 21, 18, 19, 28, 31, 28, 6, 17, 18, 28, 31, 17, 28, 16, 17, 31, 15, 16, 31, 24, 25, 29, 23, 24, 29, 30, 5, 29, 3, 4, 30, 4, 5, 30, 28, 23, 29, 28, 29, 5, 6, 28, 5, 30, 27, 0, 30, 0, 1, 26, 27, 30, 25, 26, 30, 30, 1, 2, 29, 25, 30, 3, 30, 2], "vertices": [1, 85, 2.78, 11.48, 1, 1, 85, 8.31, 18.03, 1, 2, 85, 26.23, 16.11, 0.76065, 86, -11.74, 11.43, 0.23935, 2, 85, 36.26, 9.64, 0.24396, 86, -0.81, 16.19, 0.75604, 4, 85, 38.86, 2.64, 0.05789, 86, 6.47, 14.48, 0.93078, 89, 11.92, 19.7, 0.00771, 90, 13.38, 14.73, 0.00362, 4, 85, 38.16, -4.89, 0.00012, 86, 12.35, 9.73, 0.82591, 89, 8.01, 13.23, 0.1045, 90, 5.9, 13.61, 0.06946, 3, 86, 14.82, 9.33, 0.54874, 89, 7.95, 10.72, 0.24446, 90, 3.93, 12.06, 0.2068, 3, 86, 12.69, 16.52, 0.06812, 89, 14.78, 13.81, 0.09699, 90, 10.67, 8.77, 0.83489, 3, 86, 12.06, 18.66, 0.02664, 89, 16.81, 14.73, 0.03949, 90, 12.67, 7.79, 0.93387, 3, 86, 6.03, 22.19, 0.00012, 89, 19.49, 21.18, 9e-05, 90, 19.35, 9.84, 0.99979, 1, 90, 26.76, -3.27, 1, 1, 90, 17.52, -7.73, 1, 1, 90, 15.16, -9.47, 1, 2, 89, 25.9, -3.17, 0.04628, 90, 4.7, -10.64, 0.95372, 2, 89, 20.74, -11.43, 0.49076, 90, -4.95, -11.93, 0.50924, 2, 89, 18.11, -12.5, 0.57381, 90, -7.45, -10.59, 0.42619, 2, 89, 12.05, -10.38, 0.87104, 90, -9.7, -4.58, 0.12896, 2, 89, 9.86, -9.62, 0.96258, 90, -10.5, -2.41, 0.03742, 2, 86, 35.25, 2.91, 0.01061, 89, 4.36, -10.39, 0.98939, 2, 86, 35.27, -2.85, 0.16855, 89, -1.35, -11.19, 0.83145, 3, 85, 35.52, -34.21, 3e-05, 86, 35.29, -8.71, 0.34387, 89, -7.15, -12, 0.6561, 3, 85, 35.49, -34.2, 3e-05, 86, 35.27, -8.73, 0.34392, 89, -7.17, -11.99, 0.65605, 3, 85, 31.62, -31.3, 0.00451, 86, 30.71, -10.35, 0.50499, 89, -9.39, -7.69, 0.4905, 3, 85, 26.88, -26.74, 0.05, 86, 24.29, -11.77, 0.8359, 89, -11.67, -1.52, 0.1141, 3, 85, 20.66, -24.9, 0.18714, 86, 19.31, -15.93, 0.81041, 89, -16.47, 2.85, 0.00245, 2, 85, 7.73, -21.29, 0.61905, 86, 9.15, -24.69, 0.38095, 2, 85, -4.11, -13.04, 0.92126, 86, -4.28, -29.98, 0.07874, 1, 85, -2.4, 2.71, 1, 3, 86, 24.38, 1.17, 0.00532, 89, 1.17, 0.15, 0.99466, 90, -8.54, 10.53, 1e-05, 2, 85, 29.63, -13.09, 0.01226, 86, 14.45, -1.91, 0.98774, 1, 85, 16.95, 0.9, 1, 3, 86, 23.95, 15.24, 0.00792, 89, 15.04, 2.49, 0.19937, 90, 2.12, 1.34, 0.79271], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 40, 42, 50, 52, 52, 54, 38, 40, 42, 44, 44, 46, 10, 56, 56, 58, 58, 60, 16, 62, 24, 26, 26, 28, 10, 12, 30, 32, 32, 34, 42, 56, 12, 14, 14, 16, 30, 62, 46, 48, 48, 50, 34, 36, 36, 38], "width": 50, "height": 66}}, "jushui/xiezi_skill_xl_00003": {"jushui/xiezi_skill_xl_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}, "jushui/xiezi_skill_xl_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -95, -95, -95, -95, 95, 95, 95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 190, "height": 190}}, "a23": {"a23": {"type": "mesh", "uvs": [0.33991, 0, 0.67451, 0.00834, 0.99001, 0.12451, 1, 0.25332, 1, 0.37396, 0.91771, 0.51776, 0.74853, 0.63278, 0.58554, 0.62826, 0.54686, 0.65159, 0.63191, 0.6899, 0.70606, 0.74247, 0.7968, 0.69457, 0.83896, 0.72606, 0.96069, 0.93011, 0.71425, 0.98522, 0.45397, 0.98525, 0.25173, 0.98528, 0.20731, 0.82969, 0.12045, 0.75404, 0.01999, 0.66656, 0.01977, 0.59115, 0.07904, 0.54652, 0.14237, 0.43509, 0.16459, 0.27667, 0.24023, 0.11242, 0.60873, 0.31129, 0.38671, 0.52844, 0.28981, 0.64274, 0.42759, 0.80374, 0.36167, 0.72046], "triangles": [10, 13, 14, 12, 10, 11, 13, 10, 12, 28, 14, 15, 14, 28, 10, 29, 27, 8, 18, 19, 27, 18, 27, 29, 28, 29, 8, 28, 8, 9, 17, 18, 29, 17, 29, 28, 10, 28, 9, 16, 17, 28, 15, 16, 28, 26, 22, 23, 25, 26, 23, 7, 26, 25, 21, 22, 26, 25, 6, 7, 5, 6, 25, 27, 21, 26, 8, 27, 26, 20, 21, 27, 7, 8, 26, 19, 20, 27, 25, 0, 1, 25, 1, 2, 25, 2, 3, 24, 0, 25, 23, 24, 25, 4, 25, 3, 5, 25, 4], "vertices": [2, 91, -6.53, -16.29, 0.88002, 92, -2.92, -33.8, 0.11998, 2, 91, -4.22, -0.05, 0.99805, 92, -15.16, -22.88, 0.00195, 1, 91, 5.06, 14.49, 1, 2, 91, 13.57, 14.07, 0.99971, 92, -17.06, -0.25, 0.00029, 2, 91, 21.48, 13.21, 0.9204, 92, -11.96, 5.87, 0.0796, 2, 91, 30.48, 8.18, 0.3684, 92, -2.79, 10.58, 0.6316, 3, 91, 37.14, -0.88, 0.00118, 92, 8.44, 11.1, 0.99878, 95, 8.84, 17.29, 4e-05, 3, 92, 14.38, 5.76, 0.89654, 95, 4.35, 10.67, 0.08458, 96, 1.6, 14.79, 0.01888, 3, 92, 16.83, 5.73, 0.61362, 95, 4.65, 8.25, 0.3024, 96, -0.07, 13.01, 0.08398, 3, 92, 15.24, 10.34, 0.16433, 95, 9.01, 10.44, 0.39948, 96, 4.39, 11.06, 0.43619, 3, 92, 14.67, 15.33, 0.027, 95, 13.87, 11.69, 0.1241, 96, 8.46, 8.11, 0.8489, 3, 92, 9.23, 15.75, 0.00102, 95, 13.55, 17.13, 0.01615, 96, 12.44, 11.84, 0.98283, 3, 92, 8.98, 18.67, 0.0002, 95, 16.41, 17.78, 0.00958, 96, 14.77, 10.05, 0.99021, 1, 96, 22.47, -2.5, 1, 1, 96, 10.99, -7.72, 1, 2, 95, 20.92, -7.28, 0.46846, 96, -1.65, -9.42, 0.53154, 2, 95, 15.66, -15.68, 0.92235, 96, -11.47, -10.74, 0.07765, 3, 92, 37.13, 4.11, 0.02915, 95, 5.8, -12.09, 0.97055, 96, -14.99, -0.86, 0.0003, 2, 92, 37.21, -2.45, 0.24536, 95, -0.69, -13.05, 0.75464, 3, 91, 35.52, -36.61, 0.00336, 92, 37.29, -10.04, 0.50704, 95, -8.19, -14.17, 0.4896, 3, 91, 30.57, -36.08, 0.01253, 92, 34.11, -13.87, 0.60719, 95, -12.42, -11.54, 0.38029, 3, 91, 27.95, -32.88, 0.03647, 92, 30, -14.27, 0.70255, 95, -13.38, -7.51, 0.26097, 3, 91, 20.98, -29, 0.17973, 92, 22.9, -17.94, 0.78, 95, -17.97, -0.98, 0.04026, 2, 91, 10.7, -26.8, 0.47535, 92, 15.37, -25.27, 0.52465, 2, 91, 0.32, -21.95, 0.76181, 92, 5.59, -31.23, 0.23819, 2, 91, 15.31, -5.41, 0.82981, 92, 0.12, -9.58, 0.17019, 3, 91, 28.39, -17.76, 0.05354, 92, 17.65, -5.54, 0.94565, 95, -6.4, 5.9, 0.00081, 3, 91, 35.38, -23.29, 0.0041, 92, 26.13, -2.78, 0.59965, 95, -2.52, -2.12, 0.39625, 1, 95, 10.07, -2.03, 1, 1, 95, 3.7, -1.85, 1], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 8, 10, 24, 26, 26, 28, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 16, 18, 14, 16, 2, 50, 50, 52, 52, 54, 54, 40, 34, 36, 36, 38, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 4, 6, 6, 8, 54, 58, 58, 56, 28, 30, 30, 32], "width": 49, "height": 66}}, "a24": {"a24": {"type": "mesh", "uvs": [0.14684, 0.01542, 0.29491, 0.01545, 0.41895, 0.04861, 0.50724, 0.09703, 0.57402, 0.20375, 0.57378, 0.32068, 0.63779, 0.42141, 0.65819, 0.59051, 0.69103, 0.65176, 0.71226, 0.69135, 0.96043, 0.82202, 0.99443, 0.89204, 0.97708, 0.95084, 0.89722, 0.98471, 0.53911, 0.98464, 0.30717, 0.98459, 0.26642, 0.96528, 0.3037, 0.88317, 0.2768, 0.86038, 0.21846, 0.81094, 0.10288, 0.71299, 0.05862, 0.63772, 0.05856, 0.50721, 0.01398, 0.34258, 0.0146, 0.20315, 0.08048, 0.06209, 0.29177, 0.23973, 0.32736, 0.41177, 0.40031, 0.60273, 0.50174, 0.78801], "triangles": [14, 9, 13, 12, 13, 11, 13, 9, 10, 14, 29, 9, 11, 13, 10, 29, 8, 9, 29, 7, 8, 14, 17, 29, 16, 17, 15, 14, 15, 17, 17, 18, 29, 29, 18, 28, 18, 19, 28, 19, 20, 28, 29, 28, 7, 20, 21, 28, 28, 21, 22, 28, 6, 7, 28, 5, 6, 28, 22, 27, 28, 27, 5, 27, 22, 23, 27, 23, 26, 26, 24, 25, 24, 26, 23, 5, 27, 4, 3, 26, 2, 26, 4, 27, 25, 0, 26, 3, 4, 26, 26, 1, 2, 26, 0, 1], "vertices": [1, 103, -2.47, -10.6, 1, 1, 103, -2.73, -0.53, 1, 3, 103, -0.84, 7.96, 0.99781, 104, -16.76, 28.44, 0.0008, 107, -44.58, 19.49, 0.00139, 3, 103, 2.09, 14.04, 0.98113, 104, -10.36, 30.61, 0.01091, 107, -38.76, 22.93, 0.00796, 3, 103, 8.8, 18.77, 0.92028, 104, -2.3, 29.13, 0.05095, 107, -30.56, 23.14, 0.02878, 3, 103, 16.28, 18.95, 0.74768, 104, 3.08, 23.92, 0.16157, 107, -24.23, 19.15, 0.09076, 3, 103, 22.61, 23.48, 0.47027, 104, 10.74, 22.58, 0.30149, 107, -16.46, 19.41, 0.22824, 3, 103, 33.39, 25.16, 0.13594, 104, 19.49, 16.06, 0.25962, 107, -6.55, 14.83, 0.60444, 3, 103, 37.25, 27.49, 0.04979, 104, 23.86, 14.94, 0.11177, 107, -2.05, 14.63, 0.83844, 3, 103, 39.74, 29.01, 0.02029, 104, 26.68, 14.22, 0.0371, 107, 0.87, 14.51, 0.94261, 2, 104, 44.42, 20.55, 1e-05, 107, 16.92, 24.35, 0.99999, 2, 104, 49.25, 19.1, 2e-05, 107, 21.95, 23.93, 0.99998, 2, 104, 51.14, 15.64, 2e-05, 107, 24.51, 20.93, 0.99998, 2, 104, 48.93, 10.23, 3e-05, 107, 23.46, 15.18, 0.99997, 2, 104, 32.01, -7.29, 0.21427, 107, 10.5, -5.45, 0.78573, 2, 104, 21.05, -18.63, 0.99252, 107, 2.11, -18.8, 0.00748, 1, 104, 18.24, -19.77, 1, 2, 104, 16.22, -14.29, 0.99917, 107, -3.51, -15.55, 0.00083, 1, 104, 13.9, -14.6, 1, 2, 103, 48.3, -4.35, 0.01296, 104, 8.87, -15.25, 0.98704, 2, 103, 42.24, -12.38, 0.25413, 104, -1.1, -16.55, 0.74587, 2, 103, 37.51, -15.52, 0.46659, 104, -6.66, -15.37, 0.53341, 2, 103, 29.16, -15.75, 0.84214, 104, -12.67, -9.57, 0.15786, 2, 103, 18.71, -19.06, 0.99949, 104, -22.36, -4.43, 0.00051, 1, 103, 9.79, -19.26, 1, 1, 103, 0.64, -15.03, 1, 1, 103, 11.62, -0.36, 1, 3, 103, 22.56, 2.36, 0.95878, 104, -4.37, 7.82, 0.03633, 107, -28.2, 1.86, 0.00489, 3, 103, 34.64, 7.65, 0.07288, 104, 7.87, 2.9, 0.9042, 107, -15.22, -0.44, 0.02292, 1, 104, 21.19, -0.37, 1], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 2, 52, 52, 54, 54, 56, 56, 58, 26, 28, 28, 30, 38, 40, 14, 16, 16, 18, 34, 36, 36, 38], "width": 68, "height": 64}}, "a25": {"a25": {"type": "mesh", "uvs": [0.90819, 0.19798, 0.96328, 0.36869, 0.99317, 0.53749, 0.99641, 0.67461, 0.98859, 0.81172, 0.96251, 0.91921, 0.90895, 0.99163, 0.80686, 1, 0.68938, 0.9735, 0.58001, 0.9, 0.41556, 0.76275, 0.3609, 0.86301, 0.2807, 0.89092, 0.14074, 0.80135, 0.02211, 0.64953, 0.01933, 0.43508, 0.17302, 0.23088, 0.34157, 0.1173, 0.51054, 0.0318, 0.69087, 0, 0.81793, 0.02563, 0.44011, 0.56215, 0.5024, 0.34551, 0.63237, 0.14475, 0.75955, 0.51788, 0.81805, 0.79299, 0.23132, 0.60693], "triangles": [3, 25, 24, 3, 24, 2, 4, 25, 3, 5, 25, 4, 8, 9, 25, 6, 25, 5, 7, 8, 25, 6, 7, 25, 25, 9, 24, 23, 18, 19, 22, 17, 18, 22, 18, 23, 23, 20, 0, 20, 23, 19, 24, 0, 1, 24, 23, 0, 22, 23, 24, 24, 1, 2, 22, 16, 17, 21, 22, 24, 21, 16, 22, 26, 15, 16, 21, 26, 16, 14, 15, 26, 10, 26, 21, 10, 21, 24, 9, 10, 24, 13, 14, 26, 26, 12, 13, 10, 11, 26, 11, 12, 26], "vertices": [3, 83, 14.91, 17.78, 0.74509, 84, -8.71, 16.26, 0.11091, 118, 136.33, 62.65, 0.144, 3, 83, 20.79, 14.36, 0.55207, 84, -2.21, 14.22, 0.30393, 118, 138.86, 56.34, 0.144, 3, 83, 25.72, 10.29, 0.3073, 84, 3.49, 11.33, 0.5487, 118, 140.24, 50.09, 0.144, 3, 83, 28.96, 6.38, 0.10814, 84, 7.51, 8.24, 0.74786, 118, 140.39, 45.02, 0.144, 3, 83, 31.8, 2.16, 0.018, 84, 11.21, 4.75, 0.838, 118, 140.03, 39.95, 0.144, 3, 83, 33.3, -1.72, 5e-05, 84, 13.53, 1.3, 0.85595, 118, 138.83, 35.97, 0.144, 3, 83, 33, -5.34, 0.00695, 84, 14.04, -2.31, 0.84905, 118, 136.36, 33.29, 0.144, 3, 83, 29.49, -8.48, 0.06124, 84, 11.31, -6.14, 0.79476, 118, 131.67, 32.98, 0.144, 3, 83, 24.63, -11.03, 0.2176, 84, 7.13, -9.7, 0.6384, 118, 126.26, 33.96, 0.144, 3, 83, 18.99, -11.98, 0.48747, 84, 1.84, -11.87, 0.36853, 118, 121.23, 36.68, 0.144, 4, 83, 9.9, -12.63, 0.73225, 84, -6.88, -14.51, 0.09636, 118, 113.67, 41.76, 0.13939, 119, 78.78, 41.01, 0.032, 3, 83, 10.2, -17.1, 0.84689, 84, -5.6, -18.8, 0.00911, 118, 111.15, 38.05, 0.144, 3, 83, 7.93, -20.19, 0.8554, 84, -7.14, -22.31, 0.0006, 118, 107.46, 37.02, 0.144, 3, 83, 0.82, -21.54, 0.856, 84, -13.78, -25.2, 0, 118, 101.03, 40.33, 0.144, 2, 83, -6.94, -20.47, 0.856, 118, 95.57, 45.95, 0.144, 2, 83, -11.93, -14.29, 0.856, 118, 95.44, 53.88, 0.144, 2, 83, -11, -3.98, 0.856, 118, 102.51, 61.44, 0.144, 2, 83, -7.47, 4.1, 0.856, 118, 110.26, 65.64, 0.144, 3, 83, -3.29, 11.38, 0.85598, 84, -25.05, 6, 2e-05, 118, 118.04, 68.8, 0.144, 3, 83, 2.52, 17.41, 0.85381, 84, -20.71, 13.16, 0.00219, 118, 126.33, 69.98, 0.144, 3, 83, 7.71, 20.26, 0.83119, 84, -16.27, 17.09, 0.02481, 118, 132.18, 69.03, 0.144, 4, 83, 6.23, -6.09, 0.8014, 84, -11.91, -8.93, 0.00895, 118, 114.8, 49.18, 0.13632, 119, 79.91, 48.43, 0.05333, 4, 83, 3.55, 1.99, 0.78707, 84, -16.3, -1.64, 0.00045, 118, 117.66, 57.2, 0.13248, 119, 82.77, 56.44, 0.08, 4, 83, 3.7, 11.53, 0.76543, 84, -18.27, 7.69, 0.02209, 118, 123.64, 64.62, 0.13248, 119, 88.75, 63.87, 0.08, 4, 83, 16.8, 4.24, 0.58604, 84, -3.88, 3.48, 0.23572, 118, 129.49, 50.82, 0.13824, 119, 94.6, 50.07, 0.04, 4, 83, 25.19, -2.12, 0.36309, 84, 5.71, -0.89, 0.45867, 118, 132.18, 40.64, 0.13824, 119, 97.29, 39.89, 0.04, 2, 83, -0.33, -13.3, 0.856, 118, 105.19, 47.52, 0.144], "hull": 21, "edges": [0, 40, 0, 2, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 16, 18, 18, 20, 6, 8, 8, 10, 32, 34, 34, 36, 36, 38, 38, 40, 20, 42, 42, 44, 44, 46, 46, 48, 48, 50, 48, 20, 2, 4, 4, 6, 12, 14, 14, 16, 26, 52], "width": 46, "height": 37}}, "a22": {"a22": {"type": "mesh", "uvs": [0.72798, 0.00643, 0.75486, 0.00648, 0.7918, 0.05173, 0.8339, 0.13097, 0.84461, 0.22352, 0.88044, 0.27122, 0.90801, 0.34782, 0.91037, 0.42634, 0.93845, 0.4265, 0.96102, 0.43686, 0.97521, 0.4709, 0.9751, 0.53198, 0.99999, 0.55507, 0.99292, 0.58631, 0.94897, 0.68066, 0.89389, 0.76177, 0.81605, 0.8421, 0.73928, 0.9211, 0.66852, 0.9643, 0.60218, 0.99232, 0.52134, 0.99952, 0.44166, 0.99171, 0.36227, 0.95876, 0.30033, 0.91066, 0.25248, 0.84949, 0.22179, 0.7443, 0.13604, 0.67864, 0.06901, 0.57424, 0.03576, 0.47562, 0.02, 0.40066, 0.00399, 0.32455, 0, 0.2217, 0.00163, 0.15369, 0.01409, 0.15912, 0.0225, 0.19902, 0.05366, 0.22858, 0.08579, 0.26501, 0.12375, 0.31224, 0.13524, 0.27966, 0.16266, 0.31795, 0.1974, 0.33747, 0.1914, 0.27454, 0.25913, 0.25754, 0.30156, 0.29642, 0.33525, 0.35749, 0.42125, 0.33376, 0.51489, 0.29273, 0.60646, 0.22525, 0.61805, 0.18373, 0.58339, 0.15487, 0.58546, 0.13024, 0.60292, 0.10266, 0.63343, 0.10034, 0.67031, 0.11857, 0.71756, 0.15222, 0.75722, 0.10755, 0.74704, 0.04717, 0.72773, 0.01973, 0.2246, 0.57506, 0.37538, 0.62095, 0.50393, 0.61636, 0.60735, 0.57353, 0.74846, 0.4695, 0.14052, 0.50791, 0.08446, 0.41918, 0.04483, 0.33657, 0.02067, 0.26161, 0.89695, 0.4871, 0.8553, 0.56976, 0.84274, 0.6723, 0.25585, 0.34883, 0.35932, 0.75909, 0.51474, 0.74689, 0.64191, 0.78146, 0.73697, 0.81399, 0.23671, 0.45411, 0.31379, 0.47038, 0.39985, 0.47444, 0.50775, 0.46631, 0.58996, 0.43378, 0.73768, 0.34229, 0.73896, 0.64319], "triangles": [78, 79, 61, 60, 77, 78, 60, 78, 61, 30, 31, 66, 66, 34, 35, 66, 31, 34, 34, 32, 33, 34, 31, 32, 27, 28, 64, 39, 64, 37, 28, 29, 64, 29, 65, 64, 37, 65, 36, 37, 64, 65, 29, 30, 65, 30, 66, 65, 65, 35, 36, 65, 66, 35, 37, 38, 39, 25, 58, 59, 25, 26, 58, 26, 63, 58, 26, 27, 63, 59, 58, 76, 58, 75, 76, 58, 63, 75, 27, 64, 63, 63, 64, 39, 63, 40, 75, 63, 39, 40, 76, 44, 77, 75, 70, 76, 76, 70, 44, 75, 40, 70, 70, 43, 44, 70, 42, 43, 70, 40, 42, 40, 41, 42, 72, 21, 71, 23, 71, 22, 21, 22, 71, 23, 24, 71, 24, 25, 71, 72, 59, 60, 72, 71, 59, 71, 25, 59, 59, 77, 60, 59, 76, 77, 77, 44, 45, 77, 45, 78, 19, 20, 72, 20, 21, 72, 19, 73, 18, 19, 72, 73, 72, 61, 73, 72, 60, 61, 18, 74, 17, 18, 73, 74, 17, 74, 16, 16, 69, 15, 16, 74, 69, 74, 81, 69, 74, 73, 81, 73, 61, 81, 15, 69, 14, 69, 68, 14, 14, 68, 11, 14, 11, 13, 11, 68, 67, 69, 81, 68, 81, 62, 68, 81, 61, 62, 13, 11, 12, 61, 79, 62, 68, 62, 67, 11, 67, 10, 10, 67, 9, 67, 8, 9, 67, 7, 8, 67, 62, 7, 79, 80, 62, 62, 6, 7, 6, 80, 5, 5, 80, 4, 80, 6, 62, 78, 46, 79, 78, 45, 46, 79, 47, 80, 80, 47, 54, 47, 48, 54, 48, 53, 54, 79, 46, 47, 3, 4, 55, 55, 4, 54, 54, 4, 80, 49, 50, 48, 50, 51, 48, 48, 52, 53, 48, 51, 52, 55, 2, 3, 55, 56, 2, 56, 1, 2, 56, 57, 1, 1, 57, 0], "vertices": [2, 80, 52.92, 73.13, 0.9, 118, 87.54, 94.01, 0.1, 2, 80, 59.15, 71.57, 0.9, 118, 93.96, 94, 0.1, 2, 80, 66.06, 62.8, 0.9, 118, 102.79, 87.17, 0.1, 2, 80, 72.92, 48.75, 0.9, 118, 112.85, 75.2, 0.1, 2, 80, 72.01, 34.57, 0.9, 118, 115.41, 61.23, 0.1, 2, 80, 78.57, 25.5, 0.9, 118, 123.98, 54.03, 0.1, 2, 80, 82.16, 12.69, 0.9, 118, 130.56, 42.46, 0.1, 2, 80, 79.83, 1.05, 0.9, 118, 131.13, 30.6, 0.1, 2, 80, 86.34, -0.6, 0.9, 118, 137.84, 30.58, 0.1, 2, 80, 91.19, -3.43, 0.925, 118, 143.24, 29.01, 0.075, 2, 80, 93.23, -9.24, 0.9, 118, 146.63, 23.87, 0.1, 2, 80, 90.97, -18.18, 0.9, 118, 146.6, 14.65, 0.1, 2, 80, 95.9, -23.01, 0.9, 118, 152.55, 11.17, 0.1, 2, 80, 93.11, -27.17, 0.9, 118, 150.86, 6.45, 0.1, 4, 80, 79.47, -38.45, 0.8904, 79, 86.42, -18.03, 0.0091, 75, 153.64, -19.75, 0.0005, 118, 140.36, -7.8, 0.1, 4, 80, 63.72, -47.14, 0.83787, 79, 73.25, -30.27, 0.05622, 75, 140.47, -32, 0.00591, 118, 127.19, -20.05, 0.1, 4, 80, 42.74, -54.39, 0.70907, 79, 54.65, -42.4, 0.16674, 75, 121.87, -44.13, 0.02419, 118, 108.59, -32.18, 0.1, 4, 80, 22.04, -61.51, 0.49947, 79, 36.3, -54.33, 0.33357, 75, 103.52, -56.06, 0.06696, 118, 90.24, -44.11, 0.1, 4, 80, 4.05, -63.74, 0.27573, 79, 19.39, -60.85, 0.48474, 75, 86.61, -62.58, 0.13953, 118, 73.33, -50.63, 0.1, 5, 80, -12.35, -64, 0.10453, 79, 3.54, -65.09, 0.55385, 75, 70.76, -66.81, 0.2413, 76, -109.86, 30.3, 0.00031, 118, 57.47, -54.86, 0.1, 5, 80, -31.36, -60.37, 0.02373, 79, -15.79, -66.17, 0.51378, 75, 51.43, -67.9, 0.35995, 76, -92.58, 39.01, 0.00254, 118, 38.15, -55.95, 0.1, 4, 79, -34.83, -64.99, 0.41058, 75, 32.39, -66.72, 0.47846, 76, -74.65, 45.53, 0.01096, 118, 19.11, -54.77, 0.1, 4, 79, -53.8, -60.02, 0.29226, 75, 13.42, -61.74, 0.56918, 76, -55.27, 48.55, 0.03857, 118, 0.13, -49.79, 0.1, 4, 79, -68.61, -52.76, 0.19064, 75, -1.39, -54.48, 0.58927, 76, -38.79, 47.8, 0.12009, 118, -14.67, -42.53, 0.1, 5, 79, -80.04, -43.52, 0.11151, 75, -12.82, -45.24, 0.5178, 76, -24.62, 43.9, 0.26677, 77, -70.26, 16.48, 0.00391, 118, -26.11, -33.29, 0.1, 6, 79, -87.38, -27.63, 0.05694, 75, -20.16, -29.36, 0.32954, 76, -11.56, 32.26, 0.41267, 77, -53.57, 11.24, 0.04385, 119, -68.33, -18.16, 0.082, 118, -33.44, -17.41, 0.075, 5, 79, -107.87, -17.72, 0.02184, 75, -40.65, -19.45, 0.21097, 76, 11.19, 31.35, 0.52774, 77, -32.45, 19.76, 0.13945, 118, -53.94, -7.49, 0.1, 6, 79, -123.89, -1.95, 0.00588, 75, -56.67, -3.68, 0.08863, 76, 32.18, 23.3, 0.48365, 77, -10.02, 21.04, 0.31227, 78, -43.43, 2.18, 0.00958, 118, -69.95, 8.27, 0.1, 6, 79, -131.84, 12.94, 0.00113, 75, -64.62, 11.21, 0.02635, 76, 45.41, 12.82, 0.3312, 77, 6.35, 16.92, 0.47924, 78, -26.91, 5.67, 0.06209, 118, -77.9, 23.16, 0.1, 6, 79, -135.61, 24.26, 0, 75, -68.39, 22.53, 0.00268, 76, 53.38, 3.94, 0.16403, 77, 17.27, 12.11, 0.54245, 78, -14.99, 6.14, 0.19084, 118, -81.67, 34.48, 0.1, 4, 76, 61.48, -5.07, 0.05328, 77, 28.35, 7.22, 0.44629, 78, -2.89, 6.62, 0.40043, 118, -85.5, 45.97, 0.1, 4, 76, 68.55, -18.92, 0.00865, 77, 40.5, -2.51, 0.26725, 78, 12.29, 3.22, 0.62411, 118, -86.45, 61.5, 0.1, 4, 76, 72.29, -28.5, 0.00026, 77, 47.84, -9.69, 0.11201, 78, 22.05, -0.01, 0.78773, 118, -86.06, 71.77, 0.1, 4, 76, 69.24, -28.93, 0.00048, 77, 45.23, -11.35, 0.07048, 78, 20.43, -2.64, 0.82904, 118, -83.08, 70.95, 0.1, 5, 75, -67.79, 52.98, 0, 76, 64.99, -24.21, 0.00992, 77, 39.42, -8.79, 0.14145, 78, 14.09, -2.9, 0.74863, 118, -81.07, 64.93, 0.1, 5, 75, -60.34, 48.51, 0, 76, 56.38, -23.09, 0.03984, 77, 31.11, -11.31, 0.29118, 78, 7.73, -8.81, 0.56898, 118, -73.63, 60.46, 0.1, 5, 75, -52.66, 43.01, 0.00031, 76, 47.14, -21.11, 0.10692, 77, 21.88, -13.3, 0.43928, 78, 0.31, -14.65, 0.3535, 118, -65.95, 54.96, 0.1, 5, 75, -43.59, 35.88, 0.00339, 76, 35.98, -18.2, 0.21274, 77, 10.5, -15.23, 0.51634, 78, -9.06, -21.39, 0.16753, 118, -56.87, 47.83, 0.1, 5, 75, -40.84, 40.8, 0.01312, 76, 35.42, -23.8, 0.35065, 77, 12.3, -20.57, 0.48101, 78, -5.1, -25.39, 0.05522, 118, -54.13, 52.75, 0.1, 6, 79, -101.51, 36.74, 5e-05, 75, -34.29, 35.02, 0.03547, 76, 27.11, -21.12, 0.49373, 77, 3.61, -21.54, 0.36051, 78, -12.48, -30.08, 0.01023, 118, -47.57, 46.97, 0.1, 6, 79, -93.21, 33.8, 0.00068, 75, -25.99, 32.07, 0.0742, 76, 18.32, -21.73, 0.60795, 77, -4.15, -25.71, 0.21674, 78, -17.62, -37.24, 0.00043, 118, -39.27, 44.02, 0.1, 6, 79, -94.64, 43.3, 0.00496, 75, -27.42, 41.57, 0.13699, 76, 23.43, -29.87, 0.65506, 77, 3.85, -31.03, 0.10297, 78, -8.09, -38.5, 2e-05, 118, -40.7, 53.52, 0.1, 5, 79, -78.45, 45.87, 0.02213, 75, -11.23, 44.14, 0.21634, 76, 9.61, -38.69, 0.62235, 77, -5.12, -44.75, 0.03918, 118, -24.52, 56.09, 0.1, 6, 80, -56.57, 55.37, 0.0178, 79, -68.31, 40, 0.05564, 75, -1.09, 38.27, 0.29539, 76, -2.03, -37.36, 0.51895, 77, -16.28, -48.31, 0.01222, 118, -14.38, 50.22, 0.1, 6, 80, -51, 44.47, 0.07395, 79, -60.26, 30.77, 0.10202, 75, 6.96, 29.05, 0.33914, 76, -13.1, -32.12, 0.38045, 77, -28.52, -48.08, 0.00444, 118, -6.32, 41, 0.1, 6, 80, -30.19, 42.96, 0.2698, 79, -39.71, 34.36, 0.14426, 75, 27.51, 32.63, 0.28946, 76, -30.51, -43.61, 0.19498, 77, -39.68, -65.71, 0.00151, 118, 14.23, 44.58, 0.1, 6, 80, -6.97, 43.54, 0.49947, 79, -17.33, 40.55, 0.13836, 75, 49.89, 38.83, 0.19209, 76, -48.56, -58.22, 0.0697, 77, -50.12, -86.45, 0.00038, 118, 36.61, 50.78, 0.1, 5, 80, 16.73, 48.12, 0.70907, 79, 4.56, 50.74, 0.08778, 75, 71.78, 49.02, 0.08984, 76, -64.55, -76.3, 0.01332, 118, 58.5, 60.97, 0.1, 5, 80, 20.94, 53.53, 0.83787, 79, 7.33, 57.01, 0.03555, 75, 74.55, 55.29, 0.02577, 76, -64.59, -83.16, 0.00081, 118, 61.27, 67.24, 0.1, 4, 80, 13.96, 59.77, 0.8904, 79, -0.95, 61.37, 0.00732, 75, 66.26, 59.64, 0.00228, 118, 52.98, 71.6, 0.1, 2, 80, 15.34, 63.26, 0.9, 118, 53.48, 75.31, 0.1, 2, 80, 20.4, 66.29, 0.9, 118, 57.65, 79.48, 0.1, 2, 80, 27.56, 64.86, 0.9, 118, 64.94, 79.83, 0.1, 2, 80, 35.44, 60.05, 0.9, 118, 73.76, 77.08, 0.1, 2, 80, 45.16, 52.38, 0.9, 118, 85.05, 71.99, 0.1, 2, 80, 55.99, 56.62, 0.9, 118, 94.53, 78.74, 0.1, 2, 80, 55.85, 66.06, 0.9, 118, 92.09, 87.86, 0.1, 2, 80, 52.37, 71.2, 0.9, 118, 87.48, 92, 0.1, 7, 80, -84.62, 19.01, 0.01978, 79, -86.71, -2.08, 0.0906, 75, -19.49, -3.81, 0.2702, 76, -1.97, 8.56, 0.35748, 77, -35.09, -6.42, 0.07293, 119, -67.66, 7.39, 0.164, 118, -32.77, 8.15, 0.025, 6, 80, -51.34, 3.55, 0.0867, 79, -50.67, -9.01, 0.17376, 75, 16.55, -10.74, 0.27269, 76, -37.77, 0.53, 0.22952, 77, -64.43, -28.46, 0.01867, 119, -31.62, 0.46, 0.21867, 5, 80, -21.37, -3.23, 0.21095, 79, -19.95, -8.32, 0.21545, 75, 47.27, -10.04, 0.23969, 76, -65.66, -12.37, 0.11524, 119, -0.9, 1.16, 0.21867, 5, 80, 4.18, -2.95, 0.34436, 79, 4.77, -1.85, 0.1873, 75, 71.99, -3.57, 0.11929, 76, -85.74, -28.17, 0.02105, 119, 23.82, 7.62, 0.328, 4, 80, 40.71, 4.11, 0.42605, 79, 38.5, 13.86, 0.17136, 75, 105.72, 12.13, 0.07459, 119, 57.54, 23.33, 0.328, 6, 79, -106.8, 8.06, 0.0317, 75, -39.58, 6.33, 0.1338, 76, 20.51, 7.29, 0.37628, 77, -14.08, 1.65, 0.27482, 78, -38.56, -17.02, 0.03141, 119, -87.75, 17.53, 0.152, 6, 79, -120.2, 21.46, 0.00503, 75, -52.98, 19.73, 0.04366, 76, 38.14, 0.36, 0.26361, 77, 4.85, 2.57, 0.46341, 78, -21.96, -7.88, 0.13096, 119, -101.15, 30.93, 0.09333, 5, 75, -62.45, 32.21, 0.00409, 76, 51.8, -7.3, 0.11082, 77, 20.45, 1.21, 0.53421, 78, -7.35, -2.25, 0.26288, 119, -110.62, 43.41, 0.088, 4, 76, 61.62, -15.37, 0.04978, 77, 32.71, -2.12, 0.56544, 78, 5.13, 0.15, 0.33278, 119, -116.4, 54.72, 0.052, 3, 80, 74.49, -7.08, 0.8352, 119, 93.03, 20.68, 0.136, 118, 127.92, 21.43, 0.0288, 2, 80, 61.81, -16.77, 0.776, 119, 83.08, 8.19, 0.224, 2, 80, 55.14, -31.06, 0.784, 119, 80.08, -7.29, 0.216, 6, 80, -69.09, 50.34, 0.0267, 79, -79.24, 32.08, 0.09134, 75, -12.02, 30.36, 0.36845, 76, 4.83, -25.74, 0.40945, 77, -14.8, -34.9, 0.00406, 118, -25.3, 42.31, 0.1, 7, 80, -60.13, -15.76, 0.03974, 79, -54.51, -29.87, 0.16811, 75, 12.71, -31.59, 0.32683, 76, -42.58, 21.19, 0.12976, 77, -77.3, -11.6, 0.00856, 119, -35.46, -20.4, 0.29202, 118, -0.57, -19.64, 0.03498, 6, 80, -23.64, -22.98, 0.15182, 79, -17.36, -28.03, 0.31983, 75, 49.86, -29.75, 0.27366, 76, -75.9, 4.66, 0.07658, 119, 1.69, -18.55, 0.14392, 118, 36.57, -17.8, 0.03418, 6, 80, 4.58, -35.41, 0.28091, 79, 13.03, -33.25, 0.30693, 75, 80.25, -34.97, 0.11491, 76, -105.85, -2.69, 0.00877, 119, 32.08, -23.77, 0.24053, 118, 66.97, -23.02, 0.04794, 6, 80, 25.43, -45.69, 0.44775, 79, 35.75, -38.16, 0.20802, 75, 102.97, -39.88, 0.06036, 76, -128.64, -7.25, 0.00376, 119, 54.8, -28.68, 0.21373, 118, 89.69, -27.93, 0.06638, 8, 80, -77.38, 36.03, 0.01807, 79, -83.81, 16.18, 0.07094, 75, -16.59, 14.46, 0.25306, 76, 2.67, -9.34, 0.33781, 77, -23.51, -20.84, 0.04444, 78, -37.15, -41.36, 3e-05, 119, -64.76, 25.66, 0.22095, 118, -29.87, 26.41, 0.0547, 8, 80, -60.11, 29.18, 0.05038, 79, -65.39, 13.73, 0.09898, 75, 1.83, 12, 0.25708, 76, -15.2, -14.44, 0.28725, 77, -37.7, -32.83, 0.02121, 78, -44.63, -58.38, 1e-05, 119, -46.34, 23.2, 0.23659, 118, -11.45, 23.95, 0.04851, 7, 80, -40.3, 23.59, 0.15516, 79, -44.82, 13.11, 0.13626, 75, 22.4, 11.39, 0.24068, 76, -34.3, -22.09, 0.18071, 77, -51.97, -47.65, 0.00836, 119, -25.77, 22.59, 0.23514, 118, 9.12, 23.34, 0.04369, 7, 80, -14.99, 18.53, 0.27455, 79, -19.03, 14.34, 0.14368, 75, 48.19, 12.62, 0.17482, 76, -57.45, -33.51, 0.07644, 77, -68.39, -67.58, 0.00023, 119, 0.01, 23.81, 0.2931, 118, 34.9, 24.57, 0.03719, 7, 80, 5.27, 18.53, 0.3612, 79, 0.61, 19.25, 0.11596, 75, 67.83, 17.53, 0.0966, 76, -73.51, -45.86, 0.02386, 77, -77.95, -85.43, 4e-05, 119, 19.66, 28.73, 0.36953, 118, 54.55, 29.48, 0.03282, 5, 80, 42.87, 23.37, 0.58065, 79, 35.92, 33.07, 0.09605, 75, 103.14, 31.34, 0.04181, 119, 54.97, 42.54, 0.24351, 118, 89.86, 43.29, 0.03798, 6, 80, 32.15, -20.78, 0.38975, 79, 36.23, -12.37, 0.17128, 75, 103.45, -14.09, 0.06086, 76, -118.78, -31.09, 0.00228, 119, 55.27, -2.89, 0.34388, 118, 90.16, -2.14, 0.03195], "hull": 58, "edges": [0, 114, 0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 64, 66, 66, 68, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 106, 108, 108, 110, 110, 112, 112, 114, 68, 70, 70, 72, 72, 74, 56, 58, 58, 60, 88, 90, 90, 92, 6, 8, 30, 32, 32, 34, 50, 116, 116, 118, 118, 120, 120, 122, 122, 124, 116, 126, 126, 128, 128, 130, 130, 132, 60, 62, 62, 64, 104, 106, 8, 10, 10, 12, 18, 134, 134, 136, 136, 138, 34, 36, 24, 26, 88, 140, 50, 142, 142, 144, 144, 146, 146, 148, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 124, 162], "width": 239, "height": 151}}, "a17111111": {"a17": {"type": "mesh", "uvs": [0.65069, 1e-05, 0.73528, 0.00937, 0.81277, 0.02981, 0.89428, 0.06581, 0.9648, 0.11728, 0.99432, 0.18057, 0.99555, 0.23434, 0.95964, 0.2794, 0.91814, 0.29257, 0.86398, 0.28519, 0.81365, 0.26581, 0.75509, 0.3253, 0.71305, 0.46556, 0.66884, 0.56576, 0.60534, 0.65227, 0.5311, 0.7402, 0.4742, 0.82513, 0.42041, 0.88819, 0.35887, 0.94403, 0.28184, 0.98389, 0.20075, 0.9967, 0.14062, 0.98578, 0.08101, 0.95297, 0.04326, 0.91201, 0.01304, 0.8464, 0.00452, 0.79048, 0.00475, 0.72231, 0.02525, 0.6518, 0.04254, 0.65239, 0.04088, 0.72724, 0.05297, 0.78631, 0.08686, 0.8148, 0.13459, 0.80702, 0.16181, 0.7727, 0.17924, 0.72313, 0.17873, 0.64802, 0.16725, 0.56253, 0.1647, 0.45374, 0.1884, 0.33017, 0.24624, 0.22931, 0.29479, 0.18846, 0.27726, 0.14432, 0.27749, 0.1093, 0.29184, 0.10917, 0.30622, 0.14048, 0.34015, 0.16139, 0.39193, 0.15105, 0.43013, 0.10135, 0.49116, 0.04262, 0.56183, 0.00907, 0.91474, 0.19992, 0.81962, 0.14742, 0.69235, 0.13721, 0.58115, 0.16929, 0.49675, 0.30346, 0.42441, 0.43909, 0.37082, 0.58784, 0.31857, 0.71763, 0.2623, 0.82117, 0.18326, 0.87804, 0.10823, 0.87513, 0.04259, 0.83575], "triangles": [29, 26, 27, 28, 29, 27, 25, 26, 29, 25, 29, 30, 61, 25, 30, 61, 30, 31, 24, 25, 61, 60, 31, 32, 61, 31, 60, 23, 61, 60, 24, 61, 23, 22, 23, 60, 22, 60, 21, 58, 34, 57, 33, 34, 58, 59, 33, 58, 32, 33, 59, 60, 32, 59, 18, 58, 17, 19, 58, 18, 59, 58, 19, 21, 60, 59, 20, 21, 59, 19, 20, 59, 39, 40, 55, 38, 39, 55, 37, 38, 55, 56, 36, 37, 55, 56, 37, 14, 56, 55, 35, 36, 56, 13, 14, 55, 57, 35, 56, 34, 35, 57, 15, 56, 14, 57, 56, 15, 16, 57, 15, 17, 58, 57, 16, 17, 57, 55, 40, 54, 41, 42, 43, 44, 41, 43, 47, 48, 53, 40, 41, 44, 40, 44, 45, 53, 46, 47, 54, 46, 53, 11, 52, 10, 54, 45, 46, 54, 40, 45, 11, 53, 52, 11, 54, 53, 12, 54, 11, 55, 54, 12, 13, 55, 12, 52, 0, 1, 51, 2, 3, 52, 1, 2, 51, 52, 2, 53, 49, 0, 53, 0, 52, 48, 49, 53, 50, 3, 4, 50, 4, 5, 51, 3, 50, 50, 5, 6, 10, 52, 51, 10, 51, 50, 7, 50, 6, 9, 10, 50, 8, 9, 50, 7, 8, 50], "vertices": [3, 64, 61.44, -18.31, 0.70822, 65, -0.89, -38.8, 0.28713, 66, -90.64, -14.65, 0.00465, 3, 64, 44.21, -22.66, 0.71737, 65, -12.45, -25.31, 0.28191, 66, -98.09, 1.48, 0.00072, 3, 64, 27.65, -24.51, 0.73547, 65, -21.48, -11.3, 0.26449, 66, -102.95, 17.42, 5e-05, 2, 64, 9.27, -23.83, 0.79413, 65, -29.05, 5.45, 0.20587, 2, 64, -7.95, -19.58, 0.88354, 65, -32.91, 22.77, 0.11646, 2, 64, -17.9, -10.27, 0.95439, 65, -29, 35.83, 0.04561, 2, 64, -21.67, -0.66, 0.97855, 65, -22.07, 43.48, 0.02145, 2, 64, -17.58, 10.04, 0.96321, 65, -10.67, 44.57, 0.03679, 2, 64, -10.29, 15.38, 0.87332, 65, -2.64, 40.42, 0.12668, 3, 64, 0.83, 17.91, 0.69898, 65, 4.58, 31.59, 0.29799, 66, -66.19, 51.58, 0.00303, 3, 64, 11.99, 18.01, 0.46394, 65, 9.63, 21.64, 0.51316, 66, -64.04, 40.63, 0.02291, 3, 64, 19.59, 32.93, 0.2394, 65, 26.37, 21.48, 0.67318, 66, -47.98, 35.91, 0.08742, 3, 64, 18.64, 61.24, 0.08625, 65, 51.31, 34.92, 0.69052, 66, -20.32, 42.06, 0.22322, 4, 64, 20.75, 82.48, 0.01833, 65, 71.26, 42.49, 0.55583, 66, 0.94, 43.89, 0.42338, 67, -86.01, -12.27, 0.00247, 4, 64, 27.55, 102.62, 0.00046, 65, 92.33, 45.37, 0.35055, 66, 21.99, 40.92, 0.62825, 67, -67.68, -1.5, 0.02074, 3, 65, 115.21, 46.9, 0.16844, 66, 44.42, 36.16, 0.74825, 67, -47.15, 8.72, 0.08331, 3, 65, 135.06, 50.51, 0.05896, 66, 64.51, 34.22, 0.72207, 67, -30.22, 19.7, 0.21897, 4, 65, 151.55, 51.52, 0.01331, 66, 80.65, 30.71, 0.56194, 67, -15.39, 26.99, 0.42332, 68, -51.56, -35.94, 0.00143, 4, 65, 168.26, 50.42, 0.00137, 66, 96.43, 25.09, 0.34642, 67, 0.45, 32.41, 0.63996, 68, -48.42, -19.5, 0.01225, 4, 65, 185.2, 44.86, 3e-05, 66, 111.21, 15.13, 0.16278, 67, 18.22, 33.8, 0.78096, 68, -40.82, -3.37, 0.05623, 3, 66, 121.95, 1.79, 0.05437, 67, 34.93, 30.04, 0.78176, 68, -29.28, 9.28, 0.16388, 4, 66, 126.54, -10.09, 0.01106, 67, 45.92, 23.59, 0.64058, 68, -18.24, 15.63, 0.34811, 69, -40.24, -20.37, 0.00025, 4, 66, 127.46, -24.02, 0.00084, 67, 55.3, 13.25, 0.42378, 68, -4.61, 18.67, 0.55471, 69, -33.19, -8.31, 0.02067, 3, 67, 59.86, 3.08, 0.21811, 68, 6.48, 17.59, 0.68359, 69, -24.86, -0.92, 0.0983, 3, 67, 61.26, -10.94, 0.08232, 68, 19.35, 11.86, 0.64397, 69, -11.9, 4.61, 0.27371, 3, 67, 59.08, -21.6, 0.01999, 68, 27.54, 4.69, 0.46242, 69, -1.07, 5.73, 0.51759, 3, 67, 54.36, -33.81, 0.00215, 68, 35.8, -5.46, 0.23877, 69, 11.99, 4.89, 0.75908, 2, 68, 41.08, -18.64, 0.10716, 69, 25.24, -0.22, 0.89284, 3, 67, 42.18, -43.52, 0.00062, 68, 38.21, -20.85, 0.11919, 69, 24.91, -3.82, 0.88019, 4, 66, 94.42, -53.3, 0.00015, 67, 47.64, -30.23, 0.02331, 68, 29.37, -9.52, 0.26145, 69, 10.58, -2.59, 0.71509, 4, 66, 102.9, -45.35, 0.00296, 67, 49.34, -18.73, 0.1253, 68, 20.22, -2.35, 0.44698, 69, -0.89, -4.42, 0.42476, 4, 66, 104, -36.47, 0.01971, 67, 44.68, -11.09, 0.30663, 68, 11.27, -2.62, 0.49725, 69, -6.78, -11.16, 0.17641, 5, 65, 184.07, -0.95, 1e-05, 66, 97.64, -28.65, 0.07519, 67, 34.83, -8.92, 0.49993, 68, 4.51, -10.1, 0.37793, 69, -5.9, -21.2, 0.04695, 6, 64, 106.76, 156.03, 0, 65, 175.41, -1.8, 0.00041, 66, 89.07, -27.1, 0.19459, 67, 27.16, -13.04, 0.60735, 68, 4.29, -18.8, 0.19434, 69, 0.33, -27.29, 0.00331, 5, 64, 106.59, 145.84, 0.00014, 65, 166.21, -6.18, 0.00389, 66, 79.03, -28.81, 0.37682, 67, 20.35, -20.63, 0.55561, 68, 7.51, -28.47, 0.06354, 5, 64, 111.62, 132.33, 0.00113, 65, 156.34, -16.7, 0.01863, 66, 66.67, -36.24, 0.5745, 67, 15.3, -34.13, 0.3961, 68, 16.74, -39.55, 0.00965, 5, 64, 119.48, 117.72, 0.00494, 65, 146.76, -30.25, 0.05839, 66, 53.76, -46.66, 0.71789, 67, 11.67, -50.32, 0.21825, 68, 29.01, -50.72, 0.00054, 4, 64, 127.12, 98.27, 0.01445, 65, 132.75, -45.74, 0.13415, 66, 36.05, -57.75, 0.75657, 67, 4.71, -70.02, 0.09482, 4, 64, 130.58, 74.28, 0.03108, 65, 112.8, -59.51, 0.24131, 66, 13.1, -65.56, 0.69576, 67, -8.4, -90.41, 0.03185, 4, 64, 125.83, 51.95, 0.05245, 65, 90.69, -65.2, 0.35624, 66, -9.72, -65.01, 0.58315, 67, -26.61, -104.17, 0.00815, 4, 64, 118.98, 41.11, 0.07268, 65, 77.94, -63.88, 0.45009, 66, -21.63, -60.27, 0.47586, 67, -38.89, -107.87, 0.00137, 4, 64, 125.32, 34.4, 0.08683, 65, 74.74, -72.55, 0.50747, 66, -27.06, -67.74, 0.4056, 67, -38.5, -117.09, 9e-05, 3, 64, 127.57, 28.07, 0.09449, 65, 70.07, -77.38, 0.53332, 66, -32.88, -71.12, 0.37219, 3, 64, 124.76, 27.02, 0.10223, 65, 67.89, -75.33, 0.54439, 66, -34.42, -68.55, 0.35338, 3, 64, 119.88, 31.64, 0.1222, 65, 69.85, -68.91, 0.55386, 66, -30.78, -62.9, 0.32394, 3, 64, 111.84, 32.99, 0.1705, 65, 67.49, -61.11, 0.56116, 66, -30.93, -54.75, 0.26834, 3, 64, 102.35, 27.43, 0.25869, 65, 58.28, -55.09, 0.54966, 66, -38.15, -46.45, 0.19165, 3, 64, 98.11, 15.73, 0.38737, 65, 45.92, -56.49, 0.49988, 66, -50.43, -44.43, 0.11275, 3, 64, 89.98, 0.77, 0.53249, 65, 28.9, -55.87, 0.41476, 66, -66.62, -39.19, 0.05275, 3, 64, 78.3, -10.33, 0.65004, 65, 13.76, -50.35, 0.33142, 66, -79.69, -29.76, 0.01855, 3, 64, -3.54, -1.1, 0.93468, 65, -14.39, 27.05, 0.0652, 66, -85.68, 52.38, 0.00012, 3, 64, 18.58, -3.78, 0.81391, 65, -6.94, 6.04, 0.18342, 66, -84.24, 30.14, 0.00268, 3, 64, 44.25, 3.47, 0.62399, 65, 10.97, -13.72, 0.33825, 66, -72.39, 6.25, 0.03776, 4, 64, 63.99, 17.2, 0.39914, 65, 32.05, -25.28, 0.45881, 66, -55.26, -10.62, 0.1419, 67, -96.1, -89.91, 0.00016, 4, 64, 71.76, 47.44, 0.20604, 65, 62.59, -18.79, 0.4483, 66, -24.11, -12.7, 0.34341, 67, -70.42, -72.16, 0.00226, 4, 64, 77.07, 77.08, 0.07884, 65, 91.49, -10.35, 0.32584, 66, 6, -12.46, 0.55197, 67, -46.99, -53.24, 0.04334, 5, 64, 77.84, 107.75, 0.02112, 65, 119.3, 2.61, 0.16157, 66, 36.29, -7.57, 0.65733, 67, -26.32, -30.57, 0.1599, 68, -6.97, -73.95, 8e-05, 5, 64, 79.59, 134.9, 0.00269, 65, 144.39, 13.13, 0.05283, 66, 63.3, -4.28, 0.56065, 67, -7.22, -11.2, 0.35545, 68, -14.33, -47.76, 0.02838, 6, 64, 83.84, 157.6, 0.00017, 65, 166.61, 19.42, 0.00676, 66, 86.39, -4.29, 0.35224, 67, 10.87, 3.16, 0.51889, 68, -17.85, -24.93, 0.12191, 69, -10.25, -47.68, 3e-05, 4, 66, 104.2, -12.95, 0.14263, 67, 30.2, 7.45, 0.54426, 68, -12.01, -6.01, 0.31302, 69, -20.14, -30.52, 0.0001, 4, 66, 111.7, -26.73, 0.03525, 67, 44.65, 1.33, 0.43539, 68, 0.47, 3.5, 0.52916, 69, -18.62, -14.91, 0.00021, 3, 67, 54.76, -10.64, 0.3463, 68, 15.87, 6.36, 0.65344, 69, -10.23, -1.67, 0.00027], "hull": 50, "edges": [0, 98, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 72, 74, 26, 28, 28, 30, 64, 66, 66, 68, 60, 62, 62, 64, 68, 70, 70, 72, 32, 34, 34, 36, 12, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 58, 52], "width": 209, "height": 192}}, "a2611": {"a15": {"type": "mesh", "uvs": [0.79671, 0, 0.917, 0.03565, 0.98565, 0.10258, 1, 0.26924, 0.99086, 0.43944, 0.9491, 0.6052, 0.84878, 0.69494, 0.75056, 0.73968, 0.5913, 0.80155, 0.57908, 0.82904, 0.67802, 0.89138, 0.73079, 0.93369, 0.73371, 0.96956, 0.69999, 0.99154, 0.33572, 0.9916, 0.30895, 0.94618, 0.37953, 0.83316, 0.36887, 0.80886, 0.2065, 0.73364, 0.06497, 0.59015, 0.00915, 0.48796, 0.00909, 0.36694, 0.09688, 0.17367, 0.15911, 0.09732, 0.30344, 0.03895, 0.45331, 0.00844, 0.51038, 0.25023, 0.50964, 0.53587, 0.48761, 0.79946, 0.48279, 0.84376], "triangles": [13, 29, 10, 10, 29, 9, 29, 13, 14, 14, 16, 29, 14, 15, 16, 13, 11, 12, 13, 10, 11, 29, 28, 9, 29, 16, 28, 16, 17, 28, 9, 28, 8, 28, 17, 27, 7, 8, 27, 8, 28, 27, 27, 17, 18, 7, 27, 6, 18, 19, 27, 19, 20, 27, 27, 20, 26, 6, 27, 5, 5, 27, 4, 22, 26, 21, 26, 22, 23, 27, 26, 4, 26, 23, 24, 26, 20, 21, 4, 26, 3, 26, 2, 3, 1, 26, 0, 1, 2, 26, 24, 25, 26, 26, 25, 0], "vertices": [1, 109, -28.45, 16.31, 1, 1, 109, -27.04, 29.89, 1, 1, 109, -20.86, 38.76, 1, 1, 109, -1.94, 44.3, 1, 2, 109, 17.92, 47.44, 0.9795, 110, -14.86, 47.97, 0.0205, 2, 109, 37.99, 47.03, 0.86121, 110, 5.19, 46.91, 0.13879, 2, 109, 50.61, 38.6, 0.68265, 110, 17.52, 38.07, 0.31735, 3, 109, 57.98, 29.3, 0.45686, 110, 24.59, 28.53, 0.54267, 116, 17.35, 25.75, 0.00047, 3, 109, 68.71, 13.96, 0.05642, 110, 34.81, 12.85, 0.64194, 116, 9.65, 8.76, 0.30164, 3, 109, 72.15, 13.33, 0.01371, 110, 38.23, 12.11, 0.29166, 116, 10.87, 5.5, 0.69462, 2, 110, 43.59, 23.91, 0.00072, 116, 23.66, 7.41, 0.99928, 1, 116, 31.21, 7.65, 1, 1, 116, 34.29, 4.78, 1, 1, 116, 33.37, 0.42, 1, 1, 116, 4.5, -26.31, 1, 1, 116, -1.23, -24.38, 1, 2, 110, 42.51, -9.02, 0.13891, 116, -4.62, -9.5, 0.86109, 2, 110, 39.89, -10.65, 0.37761, 116, -7.4, -8.19, 0.62239, 3, 109, 69.51, -28.34, 0.01368, 110, 34.24, -29.45, 0.79272, 116, -26.24, -13.65, 0.1936, 3, 109, 56.13, -46.76, 0.13897, 110, 20.26, -47.43, 0.77853, 116, -48.86, -11.72, 0.0825, 3, 109, 45.59, -55.13, 0.23806, 110, 9.45, -55.45, 0.70766, 116, -61.41, -7.04, 0.05429, 3, 109, 31.61, -58.06, 0.36017, 110, -4.61, -57.92, 0.60566, 116, -71.03, 3.35, 0.03417, 3, 109, 7.33, -53.45, 0.63282, 110, -28.74, -52.52, 0.3577, 116, -79.44, 26.38, 0.00948, 3, 109, -2.89, -48.71, 0.7274, 110, -38.79, -47.46, 0.2681, 116, -80.58, 37.5, 0.00449, 3, 109, -12.87, -34.87, 0.85959, 110, -48.32, -33.3, 0.13971, 116, -73.78, 53.1, 0.0007, 2, 109, -19.76, -19.77, 0.95742, 110, -54.71, -17.98, 0.04258, 3, 109, 6.87, -7.9, 0.96754, 110, -27.71, -6.98, 0.03237, 116, -40.59, 50.15, 9e-05, 2, 110, 5.49, -1.24, 0.99923, 116, -17.94, 25.57, 0.00077, 2, 109, 70.8, 2.95, 0.00103, 110, 36.54, 1.78, 0.99897, 1, 116, 4.41, -2.83, 1], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 30, 32, 32, 34, 16, 18, 18, 20, 20, 22, 12, 14, 14, 16, 52, 4, 52, 2], "width": 108, "height": 117}}, "a0": {"a0": {"type": "mesh", "uvs": [0.3853, 0.00907, 0.55858, 0.23598, 0.60723, 0.35592, 0.66455, 0.39823, 0.81744, 0.51108, 0.96, 0.65198, 0.98858, 0.71589, 0.9867, 0.86717, 0.83843, 0.9773, 0.66313, 0.99488, 0.46963, 0.95901, 0.31058, 0.84085, 0.23311, 0.53914, 0.21366, 0.4634, 0.04145, 0.38596, 0.00689, 0.31463, 0.0594, 0.05071, 0.13851, 0.04235, 0.1909, 0.12172, 0.29474, 0.10461, 0.29458, 0.00924, 0.43728, 0.44655, 0.71762, 0.83349], "triangles": [19, 20, 0, 19, 0, 1, 16, 17, 18, 15, 16, 18, 13, 14, 15, 21, 19, 1, 21, 1, 2, 13, 18, 19, 13, 15, 18, 13, 19, 21, 12, 13, 21, 4, 21, 3, 22, 4, 5, 22, 5, 6, 11, 12, 21, 21, 2, 3, 22, 21, 4, 11, 21, 22, 7, 22, 6, 10, 11, 22, 8, 22, 7, 9, 10, 22, 8, 9, 22], "vertices": [1, 12, 41.26, -10.85, 1, 1, 12, 14.4, -16.24, 1, 2, 11, 42.74, -16.23, 0.13787, 12, 1.25, -15.92, 0.86213, 2, 11, 36.76, -18.22, 0.44026, 12, -4.37, -18.78, 0.55974, 2, 11, 20.8, -23.5, 0.89585, 12, -19.35, -26.4, 0.10415, 2, 11, 2.58, -26.74, 0.99424, 12, -36.87, -32.34, 0.00576, 2, 11, -4.42, -25.7, 0.99921, 12, -43.95, -32.37, 0.00079, 1, 11, -18.52, -18.39, 1, 1, 11, -23.61, -2.85, 1, 1, 11, -19.09, 10.18, 1, 1, 11, -8.91, 21.95, 1, 1, 11, 7.77, 27.41, 1, 2, 11, 38.76, 18.49, 0.9737, 12, -7.91, 17.81, 0.0263, 2, 11, 46.54, 16.25, 0.6839, 12, 0.12, 16.77, 0.3161, 2, 11, 59.86, 24.56, 0.19814, 12, 12.04, 26.99, 0.80186, 2, 11, 67.76, 23.58, 0.12291, 12, 20, 27.21, 0.87709, 2, 11, 90.63, 7.41, 2e-05, 12, 45.04, 14.67, 0.99998, 1, 12, 43.95, 8.53, 1, 2, 11, 79.35, 1.63, 0.00141, 12, 34.76, 7.25, 0.99859, 1, 12, 33.93, -1, 1, 1, 12, 43.45, -4.12, 1, 2, 11, 40.24, -0.11, 0.98569, 12, -3.65, -0.35, 0.01431, 1, 11, -5.89, -1.26, 1], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 4, 42, 16, 44, 22, 24, 24, 26, 4, 6, 6, 8], "width": 78, "height": 105}}, "a1": {"a1": {"type": "mesh", "uvs": [0.42765, 0.01607, 0.69985, 0.00496, 0.82923, 0.0721, 0.9236, 0.31537, 0.98104, 0.66708, 0.98098, 0.81293, 0.95173, 0.84554, 0.8356, 0.9291, 0.67637, 0.9818, 0.43706, 0.98211, 0.28382, 0.95036, 0.1569, 0.85997, 0.06007, 0.64129, 0.0052, 0.42875, 0.04675, 0.30647, 0.2863, 0.05795, 0.18261, 0.607], "triangles": [12, 13, 16, 11, 12, 16, 10, 11, 16, 10, 16, 9, 8, 9, 16, 16, 14, 15, 13, 14, 16, 16, 3, 4, 5, 6, 4, 3, 0, 1, 16, 15, 0, 0, 3, 16, 2, 3, 1, 16, 4, 8, 6, 7, 4, 4, 7, 8], "vertices": [1, 15, 21.41, 24.62, 1, 1, 15, 35.44, 22.72, 1, 1, 15, 41.43, 17.97, 1, 2, 15, 43.95, 4.17, 0.99982, 14, 80.5, 17.2, 0.00018, 2, 15, 43.56, -15.05, 0.94157, 14, 85.55, -1.35, 0.05843, 2, 15, 42.17, -22.8, 0.90358, 14, 86.41, -9.18, 0.09642, 2, 15, 40.36, -24.27, 0.89418, 14, 85.1, -11.1, 0.10582, 2, 15, 33.63, -27.65, 0.83617, 14, 79.59, -16.25, 0.16383, 2, 15, 24.98, -28.99, 0.72664, 14, 71.67, -19.98, 0.27336, 2, 15, 12.72, -26.82, 0.46657, 14, 59.3, -21.36, 0.53343, 2, 15, 5.18, -23.74, 0.27145, 14, 51.19, -20.53, 0.72855, 2, 15, -0.46, -17.77, 0.10772, 14, 44.1, -16.4, 0.89228, 1, 14, 37.8, -5.22, 1, 2, 15, -4.14, 6.54, 0.74083, 14, 33.71, 5.88, 0.25917, 2, 15, -0.85, 12.66, 0.96114, 14, 35.13, 12.68, 0.03886, 1, 15, 13.77, 23.68, 1, 2, 15, 3.26, -4.56, 0.19099, 14, 43.93, -2.68, 0.80901], "hull": 16, "edges": [0, 30, 0, 2, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 24, 32, 2, 4], "width": 52, "height": 54}}, "a3": {"a3": {"type": "mesh", "uvs": [0.18963, 0.01485, 0.30682, 0.00627, 0.44295, 0.04583, 0.57898, 0.08536, 0.7683, 0.05817, 0.88802, 0.19824, 0.93285, 0.34506, 0.98289, 0.50899, 0.94286, 0.53743, 0.94546, 0.63573, 0.9325, 0.74663, 0.93451, 0.8317, 0.95075, 0.90964, 0.9522, 0.98997, 0.88073, 0.94047, 0.83339, 0.85362, 0.81104, 0.7511, 0.81009, 0.61848, 0.80965, 0.50864, 0.79504, 0.37267, 0.76482, 0.25744, 0.69024, 0.20389, 0.54481, 0.17253, 0.41653, 0.17519, 0.29822, 0.15893, 0.23118, 0.2393, 0.22299, 0.36544, 0.23863, 0.50701, 0.24293, 0.64638, 0.23603, 0.77015, 0.20255, 0.87002, 0.1546, 0.95365, 0.0853, 0.99422, 0.0862, 0.94115, 0.10681, 0.86527, 0.11436, 0.77876, 0.10383, 0.67602, 0.08162, 0.59989, 0.00319, 0.60126, 0.02644, 0.48193, 0.0489, 0.36664, 0.04931, 0.28571, 0.07441, 0.16897, 0.11692, 0.07172], "triangles": [32, 33, 31, 33, 34, 31, 31, 34, 30, 34, 35, 30, 30, 35, 29, 35, 36, 29, 29, 36, 28, 36, 37, 28, 37, 27, 28, 38, 39, 37, 37, 39, 27, 26, 39, 40, 39, 26, 27, 40, 41, 26, 26, 41, 25, 41, 42, 25, 22, 2, 3, 24, 43, 0, 23, 2, 22, 23, 24, 2, 24, 1, 2, 24, 0, 1, 25, 43, 24, 42, 43, 25, 14, 12, 13, 14, 11, 12, 14, 15, 11, 11, 15, 10, 15, 16, 10, 16, 17, 10, 10, 17, 9, 17, 8, 9, 17, 18, 8, 7, 8, 6, 8, 18, 6, 18, 19, 6, 6, 19, 5, 19, 20, 5, 5, 20, 4, 22, 3, 21, 20, 21, 4, 21, 3, 4], "vertices": [2, 43, 22.36, -5.79, 0.73867, 8, 34.64, 28.71, 0.26133, 2, 43, 12.9, -6.82, 0.73333, 8, 34.65, 19.2, 0.26667, 3, 39, -12.51, 2.49, 0.24267, 43, 1.76, -4.54, 0.48533, 8, 31.2, 8.37, 0.272, 4, 39, -1.15, 2.32, 0.51008, 43, -9.37, -2.27, 0.24267, 9, -18.09, -4.05, 0.02667, 8, 27.75, -2.46, 0.22059, 3, 39, 13.34, 7.68, 0.81675, 9, -17.14, -19.48, 0.05333, 8, 28.7, -17.88, 0.12992, 4, 39, 25.02, 0.36, 0.57408, 40, 2.97, 6.87, 0.30667, 9, -27.53, -28.54, 0.08, 8, 18.31, -26.95, 0.03925, 4, 39, 30.91, -8.81, 0.30667, 40, 13.8, 8.1, 0.54087, 41, -12.73, 6.23, 0.07246, 9, -38.02, -31.53, 0.08, 3, 40, 25.89, 9.48, 0.69447, 41, -0.99, 9.44, 0.22553, 9, -49.72, -34.86, 0.08, 3, 40, 27.11, 5.88, 0.38905, 41, 0.76, 6.06, 0.53095, 9, -51.51, -31.5, 0.08, 4, 40, 33.86, 4.54, 0.15485, 41, 7.63, 5.78, 0.74942, 42, -9, 8.35, 0.01573, 9, -58.39, -31.28, 0.08, 4, 40, 41.19, 1.78, 0.00125, 41, 15.3, 4.17, 0.61301, 42, -2, 4.84, 0.30573, 9, -66.07, -29.75, 0.08, 3, 41, 21.25, 3.9, 0.3076, 42, 3.68, 3.06, 0.6124, 9, -72.02, -29.54, 0.08, 3, 41, 26.79, 4.81, 0.01666, 42, 9.27, 2.53, 0.90334, 9, -77.55, -30.51, 0.08, 3, 42, 14.62, 0.81, 0.92, 9, -83.17, -30.28, 0.05333, 8, -37.33, -28.69, 0.02667, 4, 41, 28.53, -1, 0.04117, 42, 9.47, -3.54, 0.87083, 9, -79.35, -24.72, 0.02667, 8, -33.51, -23.13, 0.06133, 3, 41, 22.19, -4.38, 0.3221, 42, 2.47, -5.19, 0.5739, 8, -27.21, -19.68, 0.104, 4, 40, 39.29, -7.88, 0.00319, 41, 14.9, -5.67, 0.63091, 42, -4.91, -4.57, 0.26724, 8, -19.93, -18.32, 0.09867, 4, 40, 30.23, -5.87, 0.26647, 41, 5.63, -5.07, 0.63045, 42, -13.71, -1.62, 0.00974, 8, -10.66, -18.82, 0.09333, 3, 40, 22.73, -4.19, 0.5438, 41, -2.04, -4.55, 0.34953, 8, -2.98, -19.26, 0.10667, 4, 39, 20.49, -13.25, 0.28, 40, 13.19, -3.21, 0.54061, 41, -11.62, -5.04, 0.04072, 8, 6.59, -18.67, 0.13867, 3, 39, 16.26, -5.96, 0.568, 40, 4.78, -3.79, 0.27733, 8, 14.79, -16.73, 0.15467, 3, 39, 9.52, -3.69, 0.568, 43, -18.74, 5.61, 0.24267, 8, 18.91, -10.94, 0.18933, 3, 39, -2.45, -4.26, 0.288, 43, -6.88, 3.95, 0.48533, 8, 21.83, 0.68, 0.22667, 2, 43, 3.5, 4.6, 0.70133, 8, 22.29, 11.06, 0.29867, 2, 43, 13.12, 3.89, 0.70667, 8, 24.03, 20.56, 0.29333, 3, 43, 18.29, 9.75, 0.464, 44, 9.25, 5.08, 0.248, 8, 18.75, 26.33, 0.288, 4, 43, 18.56, 18.6, 0.248, 44, 18.11, 5.06, 0.34361, 45, -9.11, 6.18, 0.15239, 8, 9.98, 27.54, 0.256, 3, 44, 27.9, 7.04, 0.34361, 45, 0.85, 6.98, 0.40039, 8, 0.01, 26.89, 0.256, 4, 44, 37.61, 8.09, 0.09561, 45, 10.61, 6.88, 0.63554, 46, -13.11, 0.69, 0.01285, 8, -9.75, 27.15, 0.256, 3, 45, 19.24, 5.92, 0.49789, 46, -5.08, 3.98, 0.24611, 8, -18.36, 28.25, 0.256, 3, 45, 26.1, 2.89, 0.24989, 46, 2.39, 4.6, 0.49411, 8, -25.17, 31.39, 0.256, 3, 45, 31.77, -1.26, 0.01474, 46, 9.36, 3.67, 0.72926, 8, -30.77, 35.63, 0.256, 2, 46, 14.37, -0.14, 0.744, 8, -33.25, 41.41, 0.256, 3, 45, 30.64, -6.75, 0.00156, 46, 10.99, -1.7, 0.74244, 8, -29.55, 41.11, 0.256, 3, 45, 25.41, -4.84, 0.19766, 46, 5.49, -2.52, 0.54634, 8, -24.35, 39.11, 0.256, 3, 45, 19.39, -3.95, 0.44566, 46, -0.23, -4.62, 0.29834, 8, -18.35, 38.12, 0.256, 4, 44, 40.49, -3, 0.01105, 45, 12.17, -4.47, 0.68106, 46, -6.32, -8.53, 0.05189, 8, -11.12, 38.53, 0.256, 3, 44, 35.3, -5.18, 0.04228, 45, 6.76, -6.02, 0.70172, 8, -5.69, 39.99, 0.256, 3, 44, 35.86, -11.51, 0.17204, 45, 6.56, -12.37, 0.57196, 8, -5.39, 46.34, 0.256, 3, 44, 27.39, -10.23, 0.40321, 45, -1.69, -10.1, 0.34079, 8, 2.83, 43.94, 0.256, 3, 44, 19.21, -9, 0.61999, 45, -9.67, -7.92, 0.12401, 8, 10.77, 41.62, 0.256, 3, 44, 13.55, -9.38, 0.73822, 45, -15.33, -7.62, 0.00578, 8, 16.43, 41.23, 0.256, 3, 43, 31.2, 5.4, 0.248, 44, 5.26, -7.94, 0.496, 8, 24.46, 38.7, 0.256, 3, 43, 28.06, -1.55, 0.496, 44, -1.78, -4.99, 0.248, 8, 31.04, 34.83, 0.256], "hull": 44, "edges": [0, 86, 0, 2, 6, 8, 8, 10, 14, 16, 26, 28, 28, 30, 48, 50, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 74, 76, 80, 82, 82, 84, 84, 86, 24, 26, 22, 24, 16, 18, 30, 32, 32, 34, 34, 36, 10, 12, 12, 14, 50, 52, 52, 54, 54, 56, 56, 58, 76, 78, 78, 80, 44, 46, 46, 48, 2, 4, 4, 6, 40, 42, 42, 44, 70, 72, 72, 74, 36, 38, 38, 40, 18, 20, 20, 22], "width": 81, "height": 70}}, "a4": {"a4": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [0, 1, 9, 1, 8, 9, 1, 2, 8, 2, 7, 8, 2, 3, 7, 3, 6, 7, 3, 4, 6, 4, 5, 6], "vertices": [2, 7, 47.61, -33.52, 0.936, 9, -11.07, -19.62, 0.064, 2, 7, 48.49, -19.55, 0.856, 8, 35.64, -4.06, 0.144, 2, 7, 49.36, -5.58, 0.744, 8, 36.52, 9.92, 0.256, 2, 7, 50.23, 8.4, 0.744, 8, 37.39, 23.89, 0.256, 2, 7, 51.1, 22.37, 0.8, 8, 38.26, 37.86, 0.2, 2, 7, 66.07, 21.44, 0.8, 8, 53.23, 36.93, 0.2, 2, 7, 65.2, 7.46, 0.744, 8, 52.36, 22.95, 0.256, 2, 7, 64.33, -6.51, 0.744, 8, 51.49, 8.98, 0.256, 2, 7, 63.46, -20.48, 0.856, 8, 50.61, -4.99, 0.144, 2, 7, 62.59, -34.46, 0.936, 9, 3.9, -20.56, 0.064], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 56, "height": 15}}, "a5": {"a5": {"x": 53.26, "y": -7.32, "rotation": -93.57, "width": 55, "height": 8}}, "a6": {"a6": {"type": "mesh", "uvs": [0.0683, 0.72467, 0.13572, 0.54028, 0.21769, 0.35252, 0.33984, 0.1822, 0.49339, 0.08422, 0.64652, 0.04589, 0.79525, 0.05913, 0.88545, 0.13518, 0.96577, 0.24036, 0.96509, 0.44135, 0.8866, 0.35611, 0.82262, 0.26236, 0.74919, 0.20054, 0.66214, 0.19083, 0.58639, 0.22459, 0.50184, 0.31858, 0.42048, 0.47121, 0.33334, 0.63917, 0.22408, 0.80241, 0.12405, 0.96193, 0.00071, 0.96071], "triangles": [18, 19, 0, 19, 20, 0, 17, 18, 1, 18, 0, 1, 17, 1, 2, 9, 10, 8, 10, 11, 8, 11, 7, 8, 11, 12, 7, 12, 6, 7, 6, 12, 5, 12, 13, 5, 16, 17, 2, 2, 3, 16, 16, 3, 15, 3, 4, 15, 15, 4, 14, 13, 14, 5, 14, 4, 5], "vertices": [3, 47, -8.74, -2.08, 0.16444, 7, 73.51, -18.02, 0.72356, 8, 60.67, -2.52, 0.112, 4, 47, -4.38, 0.32, 0.33236, 48, -6.91, -16.19, 0.00749, 7, 78, -20.19, 0.54815, 8, 65.15, -4.7, 0.112, 5, 47, 0.35, 2.54, 0.50853, 48, -7.19, -10.97, 0.0414, 7, 82.54, -22.77, 0.33985, 9, 23.85, -8.87, 0.01067, 8, 69.7, -7.28, 0.09956, 5, 47, 5.69, 3.71, 0.59723, 48, -6.26, -5.59, 0.13166, 7, 86.57, -26.45, 0.16444, 9, 27.89, -12.55, 0.032, 8, 73.73, -10.96, 0.07467, 5, 47, 10.57, 2.91, 0.55469, 48, -3.69, -1.36, 0.28916, 7, 88.75, -30.89, 0.05481, 9, 30.07, -16.99, 0.064, 8, 75.91, -15.4, 0.03733, 5, 47, 14.51, 0.97, 0.40052, 48, -0.41, 1.56, 0.49074, 7, 89.44, -35.23, 0.01096, 9, 30.76, -21.33, 0.08533, 8, 76.6, -19.74, 0.01244, 3, 47, 17.55, -1.9, 0.22395, 48, 3.39, 3.29, 0.68005, 9, 30.17, -25.47, 0.096, 3, 47, 18.32, -4.97, 0.09114, 48, 6.52, 2.86, 0.81286, 9, 28.11, -27.87, 0.096, 3, 47, 18.42, -8.43, 0.0253, 48, 9.76, 1.65, 0.8787, 9, 25.35, -29.95, 0.096, 3, 47, 15.25, -12.33, 0.0064, 48, 12.19, -2.75, 0.8976, 9, 20.34, -29.62, 0.096, 3, 47, 14.88, -9.29, 0.02218, 48, 9.23, -1.96, 0.88182, 9, 22.6, -27.56, 0.096, 3, 47, 14.95, -6.34, 0.08271, 48, 6.53, -0.78, 0.8213, 9, 25.05, -25.92, 0.096, 3, 47, 14.32, -3.85, 0.2089, 48, 3.98, -0.42, 0.6951, 9, 26.72, -23.96, 0.096, 4, 47, 12.58, -2.13, 0.39082, 48, 1.73, -1.4, 0.5115, 7, 85.8, -35.44, 0.01235, 9, 27.11, -21.54, 0.08533, 5, 47, 10.4, -1.45, 0.55568, 48, 0.29, -3.16, 0.31029, 7, 85.09, -33.27, 0.05758, 9, 26.4, -19.37, 0.064, 8, 72.24, -17.78, 0.01244, 5, 47, 7.08, -1.8, 0.6135, 48, -0.64, -6.37, 0.14857, 7, 82.89, -30.76, 0.16859, 9, 24.21, -16.87, 0.032, 8, 70.05, -15.27, 0.03733, 5, 47, 2.91, -3.34, 0.52147, 48, -0.78, -10.81, 0.05058, 7, 79.22, -28.25, 0.34262, 9, 20.54, -14.35, 0.01067, 8, 66.38, -12.76, 0.07467, 4, 47, -1.63, -5.07, 0.33994, 48, -0.87, -15.67, 0.01098, 7, 75.18, -25.56, 0.54953, 8, 62.34, -10.06, 0.09956, 4, 47, -6.57, -6.33, 0.16402, 48, -1.57, -20.72, 0.00042, 7, 71.3, -22.25, 0.72356, 8, 58.46, -6.76, 0.112, 3, 47, -11.25, -7.68, 0.06578, 7, 67.5, -19.21, 0.82222, 8, 54.65, -3.71, 0.112, 3, 47, -13.92, -5.48, 0.06578, 7, 67.74, -15.76, 0.82222, 8, 54.9, -0.27, 0.112], "hull": 21, "edges": [0, 40, 12, 14, 14, 16, 16, 18, 38, 40, 36, 38, 34, 36, 32, 34, 22, 24, 8, 10, 10, 12, 18, 20, 20, 22, 28, 30, 30, 32, 4, 6, 6, 8, 0, 2, 2, 4, 24, 26, 26, 28], "width": 28, "height": 25}}, "a7": {"a7": {"type": "mesh", "uvs": [0.16671, 0.28981, 0.09841, 0.36335, 0.03203, 0.42067, 0.01284, 0.32924, 0.05529, 0.24844, 0.13555, 0.14376, 0.22311, 0.05675, 0.30146, 0.02532, 0.41363, 0.04562, 0.51266, 0.12242, 0.60093, 0.2376, 0.67368, 0.38651, 0.73784, 0.52416, 0.84029, 0.62786, 0.94528, 0.70049, 0.97413, 0.79373, 0.97414, 0.94427, 0.93043, 0.96856, 0.78531, 0.85344, 0.6604, 0.72197, 0.56528, 0.57598, 0.48356, 0.4309, 0.39669, 0.29569, 0.31475, 0.23027, 0.2331, 0.23972], "triangles": [16, 17, 15, 17, 18, 15, 18, 14, 15, 18, 13, 14, 18, 19, 13, 19, 12, 13, 19, 20, 12, 20, 11, 12, 20, 21, 11, 21, 10, 11, 21, 9, 10, 21, 22, 9, 22, 8, 9, 22, 23, 8, 23, 7, 8, 2, 3, 1, 3, 4, 1, 1, 4, 0, 4, 5, 0, 0, 5, 24, 5, 6, 24, 24, 6, 23, 6, 7, 23], "vertices": [3, 50, 6.39, 2.13, 0.72446, 49, 13.09, 6.5, 0.07554, 8, 70.47, 39.52, 0.2, 3, 50, 9.48, 2.65, 0.79596, 49, 14.36, 9.37, 0.00404, 8, 68.87, 42.22, 0.2, 2, 50, 12.35, 2.85, 0.8, 8, 67.65, 44.83, 0.2, 2, 50, 12.09, 0.55, 0.8, 8, 69.89, 45.42, 0.2, 2, 50, 9.82, -0.54, 0.8, 8, 71.72, 43.69, 0.2, 3, 50, 6, -1.55, 0.74352, 49, 15.94, 4.14, 0.05648, 8, 74.04, 40.49, 0.2, 3, 50, 2.11, -2.07, 0.59815, 49, 14.22, 0.61, 0.20185, 8, 75.92, 37.04, 0.2, 3, 50, -0.91, -1.51, 0.36389, 49, 12.09, -1.6, 0.43611, 8, 76.49, 34.02, 0.2, 3, 50, -4.59, 0.71, 0.1537, 49, 8.21, -3.43, 0.6463, 8, 75.73, 29.79, 0.2, 4, 50, -7.24, 3.95, 0.03241, 49, 4.04, -3.85, 0.67871, 7, 86.5, 10.66, 0.08889, 8, 73.66, 26.15, 0.2, 3, 49, -0.27, -3.27, 0.53333, 7, 83.53, 7.49, 0.26667, 8, 70.69, 22.98, 0.2, 3, 49, -4.5, -1.69, 0.26667, 7, 79.8, 4.95, 0.53333, 8, 66.95, 20.44, 0.2, 3, 49, -8.32, -0.17, 0.08889, 7, 76.35, 2.72, 0.71111, 8, 63.5, 18.21, 0.2, 2, 7, 73.62, -1.01, 0.8, 8, 60.78, 14.48, 0.2, 2, 7, 71.63, -4.88, 0.8, 8, 58.79, 10.61, 0.2, 2, 7, 69.33, -5.84, 0.8, 8, 56.49, 9.65, 0.2, 2, 7, 65.72, -5.61, 0.8, 8, 52.88, 9.88, 0.2, 2, 7, 65.25, -3.92, 0.8, 8, 52.4, 11.57, 0.2, 2, 7, 68.35, 1.41, 0.8, 8, 55.5, 16.91, 0.2, 3, 49, -8.32, 5.42, 0.08889, 7, 71.79, 5.95, 0.71111, 8, 58.95, 21.45, 0.2, 3, 49, -3.4, 4.34, 0.26667, 7, 75.51, 9.34, 0.53333, 8, 62.67, 24.84, 0.2, 4, 50, -3.15, 10.22, 0.02143, 49, 1.07, 3.02, 0.51191, 7, 79.18, 12.23, 0.26667, 8, 66.34, 27.72, 0.2, 4, 50, -1.5, 5.9, 0.1277, 49, 5.59, 2.01, 0.58341, 7, 82.63, 15.32, 0.08889, 8, 69.78, 30.81, 0.2, 3, 50, 0.68, 3.17, 0.32287, 49, 9.06, 2.32, 0.47713, 8, 71.54, 33.82, 0.2, 3, 50, 3.59, 2.08, 0.56407, 49, 11.58, 4.14, 0.23593, 8, 71.51, 36.93, 0.2], "hull": 25, "edges": [4, 6, 28, 30, 30, 32, 32, 34, 6, 8, 8, 10, 10, 12, 4, 2, 44, 46, 46, 48, 12, 14, 14, 16, 16, 18, 18, 20, 42, 44, 38, 40, 40, 42, 20, 22, 22, 24, 24, 26, 26, 28, 34, 36, 36, 38, 2, 0, 0, 48], "width": 38, "height": 24}}, "a8": {"a8": {"type": "mesh", "uvs": [0.4918, 0.00434, 0.49243, 0.08477, 0.35162, 0.09887, 0.6163, 0.17531, 0.61754, 0.2597, 0.75422, 0.29486, 0.85226, 0.35192, 0.90819, 0.40482, 0.94894, 0.48339, 0.99204, 0.56648, 0.99184, 0.65417, 0.89948, 0.72831, 0.91158, 0.77622, 0.90786, 0.82037, 0.87527, 0.85754, 0.85405, 0.89849, 0.82433, 0.93261, 0.78214, 0.96386, 0.73407, 0.99556, 0.7123, 0.99609, 0.71491, 0.97005, 0.71241, 0.94086, 0.70395, 0.9121, 0.67849, 0.87889, 0.60299, 0.88816, 0.60941, 0.84524, 0.58104, 0.81383, 0.53641, 0.77908, 0.43742, 0.76341, 0.24887, 0.7172, 0.0354, 0.66487, 0, 0.62807, 0, 0.61042, 0.03277, 0.56235, 0.05562, 0.47885, 0.07979, 0.39049, 0.10776, 0.28827, 0.12374, 0.22986, 0.25045, 0.15893, 0.3377, 0.01749, 0.38312, 0.00472, 0.55382, 0.7021, 0.68686, 0.72371, 0.75919, 0.6872, 0.84444, 0.71626, 0.74572, 0.78656, 0.76583, 0.85012, 0.76675, 0.89281, 0.76139, 0.93561, 0.51989, 0.26447, 0.65256, 0.33049, 0.72718, 0.4099, 0.76095, 0.48942, 0.78356, 0.5534, 0.73547, 0.61655, 0.3799, 0.27503, 0.35931, 0.41183, 0.36171, 0.50118, 0.38127, 0.58305, 0.42184, 0.65738, 0.23988, 0.31785, 0.1929, 0.4017, 0.18024, 0.51015, 0.20511, 0.62018, 0.27808, 0.70054, 0.83899, 0.41354, 0.8769, 0.49431, 0.88857, 0.56919, 0.8769, 0.63734, 0.52287, 0.32436, 0.56516, 0.42532, 0.57245, 0.50777, 0.57245, 0.58433, 0.56224, 0.64828], "triangles": [25, 45, 46, 44, 11, 12, 10, 11, 68, 11, 44, 68, 44, 43, 68, 45, 26, 42, 26, 27, 42, 44, 42, 43, 27, 41, 42, 27, 28, 41, 29, 64, 28, 64, 59, 28, 28, 59, 41, 43, 42, 73, 30, 63, 29, 29, 63, 64, 42, 41, 73, 41, 59, 73, 64, 63, 59, 73, 54, 43, 43, 54, 68, 30, 31, 63, 63, 58, 59, 73, 59, 72, 10, 68, 9, 9, 68, 67, 73, 72, 54, 72, 59, 58, 54, 53, 68, 68, 53, 67, 31, 32, 63, 32, 33, 63, 33, 62, 63, 58, 62, 57, 58, 63, 62, 54, 72, 53, 58, 71, 72, 53, 71, 52, 53, 72, 71, 58, 57, 71, 53, 66, 67, 67, 8, 9, 67, 66, 8, 33, 34, 62, 53, 52, 66, 57, 61, 56, 57, 62, 61, 62, 34, 61, 57, 70, 71, 52, 70, 51, 52, 71, 70, 57, 56, 70, 52, 65, 66, 66, 7, 8, 66, 65, 7, 52, 51, 65, 34, 35, 61, 56, 69, 70, 70, 50, 51, 70, 69, 50, 7, 65, 6, 61, 60, 56, 56, 55, 69, 56, 60, 55, 65, 51, 6, 51, 5, 6, 51, 50, 5, 61, 35, 60, 35, 36, 60, 50, 49, 4, 50, 4, 5, 55, 49, 69, 50, 69, 49, 55, 60, 37, 60, 36, 37, 37, 38, 55, 49, 55, 2, 55, 38, 2, 49, 3, 4, 49, 2, 3, 38, 39, 2, 2, 40, 1, 2, 39, 40, 40, 0, 1, 44, 45, 42, 44, 12, 45, 25, 26, 45, 45, 12, 46, 19, 20, 18, 18, 20, 17, 20, 48, 17, 20, 21, 48, 17, 48, 16, 21, 22, 48, 48, 47, 16, 48, 22, 47, 16, 47, 15, 22, 23, 47, 15, 47, 46, 23, 46, 47, 14, 15, 46, 24, 25, 23, 23, 25, 46, 14, 46, 13, 13, 46, 12], "vertices": [2, 7, 155.74, 11.82, 0.87467, 8, 142.9, 27.31, 0.12533, 2, 7, 139.04, 12.78, 0.87467, 8, 126.2, 28.28, 0.12533, 3, 7, 137.17, 29.83, 0.888, 9, 78.48, 43.73, 0.01867, 8, 124.32, 45.32, 0.09333, 3, 7, 119.32, -0.88, 0.904, 9, 60.64, 13.02, 0.03467, 8, 106.48, 14.61, 0.06133, 2, 7, 101.79, 0.06, 0.94933, 9, 43.11, 13.96, 0.05067, 2, 7, 93.47, -15.85, 0.952, 9, 34.79, -1.95, 0.048, 2, 7, 80.89, -26.85, 0.952, 9, 22.21, -12.95, 0.048, 2, 7, 69.49, -32.87, 0.952, 9, 10.81, -18.97, 0.048, 2, 7, 52.88, -36.73, 0.952, 9, -5.8, -22.83, 0.048, 3, 51, -37.28, 32.31, 0.01171, 7, 35.31, -40.81, 0.94029, 9, -23.38, -26.92, 0.048, 3, 51, -19.08, 31.2, 0.01171, 7, 17.11, -39.65, 0.93496, 9, -41.58, -25.76, 0.05333, 5, 51, -4.34, 19.22, 0.11177, 52, -31.37, 15.24, 0.00667, 7, 2.4, -27.63, 0.80155, 9, -56.28, -13.73, 0.044, 8, -10.44, -12.14, 0.036, 5, 51, 5.69, 20.08, 0.33382, 52, -21.54, 17.41, 0.05104, 53, -35.84, 16.18, 6e-05, 7, -7.63, -28.46, 0.55108, 9, -66.31, -14.56, 0.064, 5, 51, 14.83, 19.09, 0.51856, 52, -12.35, 17.62, 0.15423, 53, -26.66, 16.72, 0.00482, 7, -16.77, -27.44, 0.25839, 9, -75.45, -13.54, 0.064, 5, 51, 22.32, 14.73, 0.49941, 52, -4.35, 14.28, 0.29992, 53, -18.55, 13.65, 0.04406, 7, -24.24, -23.06, 0.09261, 9, -82.93, -9.16, 0.064, 6, 51, 30.67, 11.68, 0.34686, 52, 4.32, 12.36, 0.40014, 53, -9.81, 12.03, 0.14302, 7, -32.59, -19.99, 0.01931, 9, -91.27, -6.09, 0.04267, 8, -45.43, -4.5, 0.048, 5, 51, 37.54, 7.7, 0.17184, 52, 11.66, 9.31, 0.38717, 53, -2.38, 9.24, 0.32366, 9, -98.13, -2.09, 0.02133, 8, -52.29, -0.49, 0.096, 4, 51, 43.73, 2.26, 0.0542, 52, 18.51, 4.73, 0.26842, 53, 4.63, 4.89, 0.53338, 8, -58.46, 4.96, 0.144, 4, 51, 49.97, -3.89, 0.01025, 52, 25.5, -0.54, 0.14257, 53, 11.8, -0.14, 0.70318, 8, -64.68, 11.13, 0.144, 5, 51, 49.93, -6.5, 0.00123, 52, 25.8, -3.14, 0.10732, 53, 12.18, -2.72, 0.74734, 7, -51.79, -1.75, 0.00011, 8, -64.63, 13.75, 0.144, 5, 51, 44.54, -5.87, 0.01977, 52, 20.38, -3.22, 0.18101, 53, 6.77, -2.99, 0.65251, 7, -46.4, -2.4, 0.00271, 8, -59.24, 13.1, 0.144, 5, 51, 38.46, -5.81, 0.07957, 52, 14.34, -3.96, 0.30863, 53, 0.76, -3.93, 0.4548, 7, -40.32, -2.48, 0.013, 8, -53.17, 13.02, 0.144, 5, 51, 32.43, -6.46, 0.19845, 52, 8.45, -5.4, 0.37239, 53, -5.08, -5.58, 0.24497, 7, -34.29, -1.83, 0.0402, 8, -47.13, 13.66, 0.144, 5, 51, 25.35, -9.11, 0.34407, 52, 1.78, -8.95, 0.33475, 53, -11.62, -9.36, 0.08956, 7, -27.2, 0.79, 0.10152, 8, -40.05, 16.28, 0.13009, 5, 51, 26.74, -18.26, 0.42184, 52, 4.36, -17.85, 0.2097, 53, -8.74, -18.16, 0.01856, 7, -28.56, 9.95, 0.20376, 8, -41.41, 25.44, 0.14614, 5, 51, 17.87, -16.97, 0.41796, 52, -4.6, -17.72, 0.10942, 53, -17.7, -18.34, 4e-05, 7, -19.7, 8.62, 0.37658, 8, -32.54, 24.11, 0.096, 4, 51, 11.15, -19.98, 0.25273, 52, -10.87, -21.59, 0.03508, 7, -12.97, 11.61, 0.57086, 8, -25.81, 27.11, 0.14133, 5, 51, 3.62, -24.89, 0.07521, 52, -17.7, -27.45, 0.00635, 7, -5.42, 16.51, 0.71973, 9, -64.11, 30.41, 0.05642, 8, -18.27, 32, 0.14229, 3, 7, -1.43, 28.16, 0.78566, 9, -60.11, 42.06, 0.09067, 8, -14.27, 43.65, 0.12367, 3, 7, 9.57, 50.15, 0.77414, 9, -49.11, 64.04, 0.104, 8, -3.27, 65.64, 0.12186, 3, 7, 22.03, 75.03, 0.77414, 9, -36.65, 88.93, 0.104, 8, 9.19, 90.53, 0.12186, 3, 7, 29.93, 78.8, 0.77414, 9, -28.75, 92.7, 0.104, 8, 17.09, 94.29, 0.12186, 3, 7, 33.6, 78.57, 0.77414, 9, -25.08, 92.47, 0.104, 8, 20.76, 94.06, 0.12186, 3, 7, 43.33, 74.02, 0.77414, 9, -15.35, 87.92, 0.104, 8, 30.49, 89.51, 0.12186, 3, 7, 60.5, 70.2, 0.77414, 9, 1.81, 84.1, 0.104, 8, 47.65, 85.7, 0.12186, 3, 7, 78.66, 66.16, 0.77414, 9, 19.98, 80.06, 0.104, 8, 65.82, 81.66, 0.12186, 3, 7, 99.67, 61.49, 0.77414, 9, 40.99, 75.39, 0.104, 8, 86.83, 76.98, 0.12186, 3, 7, 111.68, 58.82, 0.74358, 9, 52.99, 72.72, 0.09989, 8, 98.83, 74.31, 0.15652, 3, 7, 125.46, 42.73, 0.75753, 9, 66.77, 56.62, 0.06523, 8, 112.61, 58.22, 0.17724, 3, 7, 154.17, 30.44, 0.77149, 9, 95.48, 44.34, 0.03056, 8, 141.32, 45.93, 0.19795, 2, 7, 156.48, 24.84, 0.84533, 8, 143.63, 40.33, 0.15467, 3, 7, 10.43, 13.43, 0.7869, 9, -48.25, 27.33, 0.02133, 8, -2.41, 28.92, 0.19177, 5, 51, -6.81, -6.19, 0.07151, 52, -30.49, -10.28, 0.02387, 53, -43.82, -11.8, 1e-05, 7, 4.95, -2.23, 0.7006, 8, -7.9, 13.26, 0.204, 2, 7, 11.99, -11.36, 0.80267, 8, -0.86, 4.13, 0.19733, 3, 7, 5.32, -21.2, 0.856, 9, -53.37, -7.3, 0.02133, 8, -7.53, -5.71, 0.12267, 5, 51, 6.66, 0.09, 0.21359, 52, -17.96, -2.29, 0.12055, 53, -31.58, -3.38, 0.00662, 7, -8.54, -8.46, 0.48858, 8, -21.38, 7.03, 0.17067, 5, 51, 20, 1.71, 0.28852, 52, -4.94, 1.07, 0.29441, 53, -18.69, 0.43, 0.02311, 7, -21.89, -10.05, 0.24995, 8, -34.73, 5.44, 0.144, 5, 51, 28.87, 1.3, 0.24062, 52, 3.9, 1.82, 0.47573, 53, -9.87, 1.48, 0.04783, 7, -30.75, -9.61, 0.09181, 8, -43.6, 5.89, 0.144, 5, 51, 37.72, 0.13, 0.18358, 52, 12.83, 1.82, 0.58052, 53, -0.95, 1.79, 0.06184, 7, -39.6, -8.41, 0.03006, 8, -52.44, 7.08, 0.144, 2, 7, 101.53, 11.82, 0.824, 8, 88.69, 27.31, 0.176, 2, 7, 86.84, -3.21, 0.808, 8, 73.99, 12.28, 0.192, 2, 7, 69.79, -11.12, 0.792, 8, 56.95, 4.37, 0.208, 2, 7, 53.03, -14.14, 0.776, 8, 40.19, 1.36, 0.224, 2, 7, 39.58, -16.02, 0.76267, 8, 26.74, -0.52, 0.23733, 2, 7, 26.83, -9.44, 0.756, 8, 13.99, 6.05, 0.244, 2, 7, 100.39, 28.73, 0.76, 8, 87.54, 44.22, 0.24, 2, 7, 72.14, 32.96, 0.736, 8, 59.3, 48.45, 0.264, 2, 7, 53.57, 33.83, 0.728, 8, 40.73, 49.32, 0.272, 2, 7, 36.43, 32.55, 0.728, 8, 23.59, 48.04, 0.272, 2, 7, 20.7, 28.65, 0.728, 8, 7.86, 44.15, 0.272, 2, 7, 92.54, 46.05, 0.848, 8, 79.7, 61.54, 0.152, 2, 7, 75.49, 52.76, 0.864, 8, 62.64, 68.25, 0.136, 2, 7, 53.07, 55.68, 0.864, 8, 40.22, 71.18, 0.136, 3, 7, 30.04, 54.13, 0.83405, 9, -28.64, 68.03, 0.03467, 8, 17.2, 69.62, 0.13129, 3, 7, 12.81, 46.43, 0.81907, 9, -45.87, 60.33, 0.052, 8, -0.03, 61.92, 0.12893, 2, 7, 68.2, -24.46, 0.932, 8, 55.36, -8.97, 0.068, 2, 7, 51.15, -27.96, 0.928, 8, 38.31, -12.47, 0.072, 2, 7, 35.52, -28.39, 0.93333, 8, 22.68, -12.89, 0.06667, 2, 7, 21.46, -26.11, 0.936, 8, 8.62, -10.61, 0.064, 2, 7, 89.08, 12.24, 0.736, 8, 76.23, 27.73, 0.264, 2, 7, 67.8, 8.48, 0.728, 8, 54.96, 23.98, 0.272, 2, 7, 50.63, 8.68, 0.728, 8, 37.79, 24.17, 0.272, 2, 7, 34.74, 9.67, 0.74133, 8, 21.89, 25.16, 0.25867, 2, 7, 21.54, 11.72, 0.748, 8, 8.7, 27.21, 0.252], "hull": 41, "edges": [0, 80, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 36, 38, 38, 40, 46, 48, 48, 50, 54, 56, 60, 62, 62, 64, 64, 66, 74, 76, 76, 78, 78, 80, 54, 82, 82, 84, 84, 86, 86, 88, 88, 22, 50, 52, 52, 54, 44, 46, 32, 34, 34, 36, 30, 32, 22, 24, 24, 26, 26, 28, 28, 30, 84, 90, 90, 92, 92, 94, 94, 96, 40, 42, 42, 44, 14, 16, 16, 18, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 114, 116, 116, 118, 110, 120, 120, 122, 122, 124, 124, 126, 126, 128, 130, 132, 132, 134, 134, 136, 138, 140, 140, 142, 142, 144, 144, 146, 56, 58, 58, 60, 66, 68, 68, 70, 70, 72, 72, 74], "width": 120, "height": 208}}, "a11111111": {"a11": {"type": "mesh", "uvs": [0.50518, 0.00829, 0.65875, 0.09456, 0.85933, 0.28991, 0.98245, 0.46879, 0.98423, 0.62115, 0.81312, 0.77002, 0.58288, 0.88776, 0.31651, 0.96597, 0.10686, 1, 0.08718, 1, 0.01677, 0.96555, 0, 0.8471, 0, 0.70091, 0.0242, 0.51117, 0.085, 0.31972, 0.2071, 0.14146, 0.37183, 0.0079], "triangles": [7, 8, 10, 8, 9, 10, 6, 11, 12, 12, 13, 5, 5, 13, 4, 2, 3, 13, 2, 13, 14, 4, 13, 3, 14, 15, 2, 15, 1, 2, 1, 16, 0, 1, 15, 16, 5, 6, 12, 6, 7, 11, 7, 10, 11], "vertices": [1, 13, -24.66, -18.71, 1, 1, 13, -16.6, -8.05, 1, 1, 13, 3.38, 7.88, 1, 2, 13, 22.4, 19.09, 0.99245, 14, 24.05, 45.36, 0.00755, 2, 13, 39.81, 22.89, 0.9126, 14, 26.1, 27.65, 0.0874, 2, 13, 58.88, 16.97, 0.58259, 14, 18.32, 9.27, 0.41741, 2, 13, 75.08, 7, 0.55913, 14, 6.78, -5.86, 0.44087, 2, 13, 87.18, -5.96, 0.2964, 14, -7.3, -16.62, 0.7036, 2, 13, 93.56, -16.82, 0.32087, 14, -18.75, -21.89, 0.67913, 2, 13, 93.79, -17.92, 0.3813, 14, -19.86, -22.01, 0.6187, 2, 13, 90.68, -22.68, 0.49105, 14, -24.29, -18.44, 0.50895, 2, 13, 77.32, -26.49, 0.73684, 14, -26.76, -4.77, 0.26316, 2, 13, 60.59, -30.04, 0.86977, 14, -28.64, 12.23, 0.13023, 2, 13, 38.59, -33.31, 0.96279, 14, -29.7, 34.44, 0.03721, 2, 13, 15.96, -34.57, 0.99312, 14, -28.71, 57.09, 0.00688, 1, 13, -5.89, -32.09, 1, 1, 13, -23.12, -26.15, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 20, 22, 22, 24, 24, 26], "width": 57, "height": 117}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"skill/skill0/dg01/shui_00011": {"attachment": [{"time": 0.1667, "name": "jushui/shui_00011"}, {"time": 0.2, "name": "jushui/shui_00013"}, {"time": 0.2333, "name": "jushui/shui_00014"}, {"time": 0.2667, "name": "jushui/shui_00015"}, {"time": 0.3, "name": "jushui/shui_00016"}, {"time": 0.3333, "name": "jushui/shui_00018"}, {"time": 0.3667, "name": "jushui/shui_00019"}, {"time": 0.4, "name": null}]}}, "bones": {"bone2": {"translate": [{"y": -3.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "y": 3.51, "curve": "stepped"}, {"time": 0.2333, "y": 3.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "y": -3.3}]}, "bone4": {"rotate": [{}, {"time": 0.1667, "angle": 5.66}, {"time": 0.2333, "angle": -9.2}, {"time": 0.5}], "translate": [{"x": 0.03, "y": 0.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 0.39, "y": 7.38, "curve": "stepped"}, {"time": 0.2333, "x": 0.39, "y": 7.38, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": 0.03, "y": 0.57}]}, "bone5": {"rotate": [{}, {"time": 0.1667, "angle": 7.09}, {"time": 0.2333, "angle": -15.93}, {"time": 0.5}], "translate": [{"x": 1.86, "y": -0.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 15.38, "y": -1.81, "curve": "stepped"}, {"time": 0.2333, "x": 15.38, "y": -1.81, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": 1.86, "y": -0.06}]}, "bone6": {"rotate": [{"angle": 1.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "angle": 10.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3, "angle": 4.33, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 1.29}]}, "bone14": {"rotate": [{"angle": 2.45, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 158.48, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 85.03, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.3, "angle": -53.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 2.45}], "translate": [{"time": 0.1667}, {"time": 0.2333, "x": -13.21, "y": -42.44}, {"time": 0.3, "x": 2.24, "y": 16.53}, {"time": 0.5}]}, "bone15": {"rotate": [{"angle": 1.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -50.04, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": -91.34, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.3, "angle": -85.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 1.22}], "translate": [{"time": 0.1667}, {"time": 0.2333, "x": 38.25, "y": 19.13}, {"time": 0.5}]}, "bone16": {"rotate": [{"angle": -1.98, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 106.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2, "angle": -19.81, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -116.13, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.3, "angle": 159.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -1.98}]}, "bone32": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 5.08, "y": -0.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "x": 1.82, "y": -2.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": -0.97, "y": 0.01}]}, "bone36": {"rotate": [{"angle": 3.07}]}, "bone37": {"rotate": [{"angle": -8.17}]}, "bone38": {"translate": [{"x": -0.97, "y": 0.01, "curve": "stepped"}, {"time": 0.1667, "x": -0.97, "y": 0.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "x": -0.87, "y": 8.19, "curve": "stepped"}, {"time": 0.3667, "x": -0.87, "y": 8.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": -0.97, "y": 0.01}]}, "bone42": {"rotate": [{"angle": 3.52}]}, "bone43": {"rotate": [{"angle": -5.29}]}, "bone48": {"translate": [{"x": 3.79, "y": -0.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 38.44, "y": -50.26, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "x": -10.89, "y": -86.12, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.3, "x": 18.46, "y": 58.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 3.79, "y": -0.3}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44}]}, "bone95": {"rotate": [{"angle": -4.3}]}, "bone94": {"rotate": [{"angle": 1.2}]}, "bone53": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone11": {"rotate": [{"angle": 0.8, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -4.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 8.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.8}]}, "bone12": {"rotate": [{"angle": -4.77, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -10.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -25.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.77}]}, "bone13": {"rotate": [{"angle": -5.94, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "angle": 15.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -5.94}]}, "a4": {"rotate": [{"angle": -7.75, "curve": "stepped"}, {"time": 0.1667, "angle": -7.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": -44.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": -7.75}]}, "a5": {"rotate": [{"angle": -5.87, "curve": "stepped"}, {"time": 0.1667, "angle": -5.87, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 16.49, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": -5.87}]}, "a6": {"rotate": [{"angle": 11.87, "curve": "stepped"}, {"time": 0.1667, "angle": 11.87, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 60.08, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 11.87}]}, "a7": {"rotate": [{"angle": 28.89, "curve": "stepped"}, {"time": 0.1667, "angle": 28.89, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 77.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 28.89}]}, "a8": {"rotate": [{"angle": 12.93, "curve": "stepped"}, {"time": 0.1667, "angle": 12.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 61.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 12.93}]}, "a3": {"rotate": [{"angle": -4.24, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 58.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": -99.64, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 0.2667, "angle": 128.65, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 0.5, "angle": -4.24}]}, "bone3": {"translate": [{"time": 0.1667}, {"time": 0.2333, "x": 27.34}, {"time": 0.5}]}, "bone33": {"rotate": [{"angle": 2.53}]}, "bone34": {"rotate": [{"angle": -5.5}]}, "bone39": {"rotate": [{"angle": 3.27}]}, "bone40": {"rotate": [{"angle": -5.48}]}, "bone45": {"rotate": [{"time": 0.1667}, {"time": 0.2333, "angle": -13.18}, {"time": 0.5}]}, "yj": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 2.18, "y": -84.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone46": {"rotate": [{"time": 0.3667}, {"time": 0.4333, "angle": -13.66}, {"time": 0.5}], "translate": [{"time": 0.1667}, {"time": 0.2333, "x": 38.29, "curve": "stepped"}, {"time": 0.3667, "x": 38.29}, {"time": 0.4333, "x": 19.15, "y": 4.68}, {"time": 0.5}]}, "bone24": {"rotate": [{"time": 0.2333}, {"time": 0.3, "angle": -64.49}, {"time": 0.5}]}, "bone92": {"rotate": [{"angle": 1.99}]}, "bone93": {"rotate": [{"angle": -5.49}]}, "bone63": {"rotate": [{"angle": 16.24, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 18.55, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 11.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -9.27, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5, "angle": 16.24}]}, "bone57": {"rotate": [{"angle": -1.37, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.1, "angle": 21.37, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1333, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -25.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -1.37}]}, "bone17": {"rotate": [{"angle": -6.57, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 6.52, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 1.23, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.2667, "angle": -6.8, "curve": 0.322, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.3333, "angle": -84.11, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -6.57}], "translate": [{"curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1667, "x": -25.39, "y": 66.98, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": -6.79, "y": 3.44, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.3667}]}, "bone22": {"rotate": [{"angle": 11.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": 12.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 7.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": -6.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.5, "angle": 11.68}]}, "bone65": {"rotate": [{"angle": -12.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -21.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -12.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 14.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -12.62}]}, "bone60": {"rotate": [{"angle": -16.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": -10.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": 6.04, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5, "angle": -15.96}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone69": {"rotate": [{"angle": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 29.01, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2, "angle": 40.3, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.3333, "angle": -54.05, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": 1.42}], "translate": [{"curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "x": -3.78, "y": -13.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "x": 3.19, "y": -20.65, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone56": {"rotate": [{"angle": 14.59, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 17.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 9.8, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -13, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5, "angle": 14.59}]}, "bone55": {"rotate": [{"angle": 9.18, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -0.19, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": 9.18}]}, "bone62": {"rotate": [{"angle": 8.16, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -3.17, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": -9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": 8.16}]}, "bone19": {"rotate": [{"angle": 3.05, "curve": 0.326, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.0667, "angle": -4.83, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.1667, "angle": -17.72, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2667, "angle": 14.07, "curve": 0.34, "c2": 0.35, "c3": 0.691, "c4": 0.74}, {"time": 0.3667, "angle": 37.96, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.4333, "angle": -30.28, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 3.05}]}, "bone66": {"rotate": [{"angle": 0.6, "curve": 0.335, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.0667, "angle": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.13, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "angle": 0.6}]}, "bone21": {"rotate": [{"angle": 12.38, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 8.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 24.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 12.38}]}, "bone64": {"rotate": [{"angle": -12.77, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.1667, "angle": 6.06, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.77}]}, "bone18": {"rotate": [{"angle": -8.95, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.0667, "angle": -14.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -8.83, "curve": 0.322, "c2": 0.3, "c3": 0.682, "c4": 0.71}, {"time": 0.2667, "angle": 41.03, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.3333, "angle": 25.74, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.4, "angle": -38.35, "curve": 0.318, "c2": 0.29, "c3": 0.678, "c4": 0.7}, {"time": 0.5, "angle": -8.95}]}, "bone59": {"rotate": [{"angle": -7.48, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": -1.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -10.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": -7.48}]}, "bone70": {"rotate": [{"angle": -8.28, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": -5.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.81, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": -8.28}], "translate": [{"x": 1.54, "y": -12.11, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "x": -1.34, "y": -9.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "x": -12.11, "y": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 2.1, "y": -12.71, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "x": 1.54, "y": -12.11}]}, "bone61": {"rotate": [{"angle": -4.18, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.1, "angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 16.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": -4.18}]}, "bone20": {"rotate": [{"angle": 12.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "angle": 7.74, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "angle": -9.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 14.83, "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 41.83, "curve": 0.325, "c2": 0.31, "c3": 0.666, "c4": 0.67}, {"time": 0.4333, "angle": -44.16, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.5, "angle": 12.18}]}, "bone71": {"rotate": [{"angle": -4.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -4.46}], "translate": [{"x": 9.74, "y": -5.24, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "x": 14.68, "y": -6.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -12.34, "y": -0.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "x": 9.74, "y": -5.24}]}, "bone67": {"rotate": [{"angle": 18.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 12.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 20.08, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 18.87}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone68": {"rotate": [{"angle": 13.53, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -19.01, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 13.53}]}, "bone23": {"rotate": [{"angle": 3.6, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.1, "angle": 16.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -14.02, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.5, "angle": 3.6}]}, "shui": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 44.68}], "scale": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.717, "y": 1.717}]}}, "deform": {"default": {"a1411": {"a14": [{"time": 0.1667}, {"time": 0.2333, "vertices": [-9.36735, -1.27077, -9.36735, -1.27077, -9.36735, -1.27077, -9.36735, -1.27077, -10.00366, -3.16337, -9.36735, -1.27077, -10.00366, -3.16337, -9.36735, -1.27077, -10.00366, -3.16337, -8.85953, -1.05557, -9.4949, -2.84863, -6.99984, 7.78967, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.36735, -1.27077, -10.00366, -3.16337, -9.36735, -1.27077, -10.00366, -3.16337, -9.36735, -1.27077, -9.36735, -1.27077, -9.36735, -1.27077, -10.00366, -3.16337]}, {"time": 0.5}]}, "a9111111": {"a9": [{}, {"time": 0.1667, "vertices": [6.58821, -36.1391, -23.59217, 28.15625, -27.46532, 24.39325, -34.00392, 13.89627, 9.77142, -21.9359, -19.33789, 14.2363, -21.21621, 11.24744, -23.74652, 3.56937, 8.99976, -6.91397, -11.24088, 1.56009, -11.34818, -0.10471, -10.67038, -3.8647, 8.85513, 5.48369, -4.98638, -9.14459, -3.59191, -9.77676, -0.14394, -10.41475, 17.22568, 16.25076, -6.93967, -22.64221, -3.54521, -23.41492, 4.42604, -23.26456, 31.77684, 27.53868, -14.00811, -39.64792, -8.04406, -41.27316, 6.10834, -41.60378, 41.45914, 41.40924, 42.22385, 51.14169, 42.06136, 61.16251, 22.64716, 61.6169, -0.35028, -82.50554, -15.14409, 57.10015, 29.18715, -81.49982, -37.94357, 27.11797, 35.21724, -50.16549, 24.75007, -51.36893, -32.38161, 8.55569, 18.13796, -25.50407, 7.21456, -25.84783, -11.69733, 0.45687, 4.96568, -10.60068, -0.02917, -11.70613, 2.52163, -4.37651, 0.41432, -5.03399, 0.6953, -2.09338, -0.26386, -2.19003, -0.19295, -2.19739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.13, 5.23492, 7.91666, -1.52048, 8.05398, -0.34329, 7.71143, 2.34889, -13.69846, 12.59634, 18.13493, -4.17847, 18.55144, -1.47437, 17.98935, 4.76541, -13.23517, 15.82872, 19.33017, -7.21729, 20.17934, -4.30521, 20.4644, 2.63516, -7.17961, 8.9397, 10.66048, -4.22217, 11.16424, -2.61356, 11.39883, 1.23938, -3.44359, -3.73523, 1.14693, 4.94882, 0.40884, 5.06351, -1.29474, 4.91217, 0.40248, -17.89514, -9.19622, 15.35556, -11.34821, 13.84143, -15.29849, 9.29115, 0.50832, -30.17947, -15.36097, 25.9812, -19.00422, 23.44852, -25.70868, 15.81302, -1.86839, -41.96333, -19.12035, 37.39935, -24.39707, 34.19217, -34.36153, 24.15819, 3.59537, 44.18907, -10.80772, 37.7789, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.72658, 9.0331, 9.44387, -5.02228, 10.07804, -3.58334, 10.69591, -0.03606, -3.48782, 18.07846, 11.96944, -13.99188, 13.89106, -12.08542, 17.11453, -6.79085, -1.68283, 14.85246, 8.8054, -12.07919, 10.48104, -10.65768, 13.42372, -6.57567]}, {"time": 0.2333, "vertices": [17.04844, -28.60666, 17.58478, 28.2827, 27.96432, 18.08278, 27.36723, 18.97689, 18.41878, -17.44659, 7.04141, 24.37491, 16.76169, 19.04444, 16.13911, 19.57619, 13.20528, -7.22565, 0.35579, 15.05042, 6.73766, 13.46097, 6.30114, 13.67232, 2.15655, -1.41446, 0.26666, 2.56606, 1.33463, 2.20706, 1.26315, 2.24932, 0.7246, -0.72437, 0.31081, 0.97639, 0.69739, 0.75061, 0.67284, 0.77277, 0, 0, 0, 0, 0, 0, 0, 0, -3.23516, 15.06279, -3.35383, 21.77344, -8.19559, 23.42824, -19.43247, 12.99062, -2.6158, -23.22643, -39.8362, 8.0331, 11.15762, -39.0751, -46.54604, 2.74635, 18.93437, -42.60902, -1.04416, -46.61513, -35.79982, 4.16144, 12.74258, -33.71274, -2.85069, -35.9278, -16.37626, 0.63962, 6.95195, -14.84096, -0.04083, -16.38858, 3.53029, -6.12712, 0.58005, -7.04759, 0.97342, -2.93073, -0.3694, -3.06604, -0.27013, -3.07634, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.58688, 0.5518, -0.22047, -0.77448, -0.53003, -0.60661, -0.51001, -0.62329, 0.1567, -0.87869, 0.70891, 0.54311, 0.87227, 0.18876, 0.86613, 0.21707, -2.15461, -3.50507, 4.10341, -0.30391, 3.58139, -2.0249, 3.64523, -1.90801, -8.23789, -2.32208, 5.84628, -6.25069, 2.6214, -8.14727, 2.88379, -8.05788, -3.77473, -4.68994, 5.89989, -1.1992, 4.82471, -3.60078, 4.93867, -3.443, 4.05491, -9.02776, 6.15794, 7.74818, 8.87331, 4.3822, 8.72778, 4.66663, 8.77069, -16.99805, 11.07323, 15.59785, 16.66615, 9.38621, 16.35524, 9.9198, 11.04821, -26.63884, 18.59213, 22.04924, 26.21695, 12.01485, 25.81697, 12.85576, 16.43812, -37.2083, 25.50603, 31.68999, 36.58273, 17.78706, 35.99039, 18.95937, -12.78767, 1.89258, -20.11656, -0.34131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -11.05056, -0.79112, 5.77724, -9.45316, 1.19426, -11.01425, 1.54933, -10.96987, -3.15774, 1.57843, 0.04843, -3.52908, -1.4621, -3.21307, -1.35699, -3.25829, -20.36408, -4.26573, 13.14066, -16.13103, 5.00679, -20.19456, 5.65639, -20.02224, -6.94903, -0.28329, 3.44301, -6.04227, 0.53696, -6.93384, 0.76082, -6.91262]}, {"time": 0.3667, "vertices": [5.88771, -14.81955, 15.88885, -1.35863, 15.45096, 3.9449, 14.7878, -5.96527, 4.78594, -11.31604, 12.26231, -0.77802, 11.83551, 3.29997, 11.4922, -4.34525, 2.47134, -5.75037, 6.24873, -0.36017, 6.01932, 1.71593, 5.86696, -2.1796, -0.52977, -1.62332, 1.21555, -1.19905, 1.54248, -0.73256, 0.80885, -1.50314, 0.84304, 3.4072, -2.67068, 2.2776, -3.27142, 1.27192, -1.88405, 2.96158, -3.14046, 12.24081, -12.35326, 2.66412, -12.54193, -1.54917, -11.02609, 6.17487, -1.17781, 14.9015, -2.63068, 18.51702, -5.96592, 19.95049, -9.59473, 10.20763, -4.52702, -6.4295, -21.74848, 6.12304, 2.92335, -16.81779, -24.0137, 2.24202, 7.77448, -19.37743, -1.56344, -23.52626, -17.82138, 1.92374, 6.47466, -16.71456, -1.27135, -17.87971, -8.18813, 0.31981, 3.47598, -7.42048, -0.02042, -8.19429, 1.76514, -3.06356, 0.29002, -3.5238, 0.48671, -1.46536, -0.1847, -1.53302, -0.13506, -1.53817, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.6771, -5.00879, 5.67732, 0.1554, 5.31, 2.01475, 5.47231, -1.51901, 3.13899, -8.93234, 9.39351, -1.18567, 9.26056, 1.97119, 8.63051, -3.89246, 3.09157, -16.78046, 16.39267, -4.73659, 17.03851, 0.92088, 14.27737, -9.34253, 2.71427, -23.47119, 22.20853, -8.06555, 23.62595, -0.30903, 18.85907, -14.23285, -2.90349, 13.59088]}, {"time": 0.5}]}, "a13": {"a13": [{}, {"time": 0.2333, "offset": 128, "vertices": [19.49281, 15.34938, -17.12557, -17.95239, -12.60313, -21.37139, -12.60301, -21.3714, -12.60313, -21.37138, -26.55443, -19.09226, -21.52711, -24.62199, -21.52672, -24.62198, -21.52716, -24.62194, -31.32098, -15.81372, -26.9135, -22.51109, -26.91313, -22.51116, -26.91344, -22.51101, -32.35286, -15.3675, -32.35242, -15.36758, -32.3528, -15.36754, -29.98856, -13.10148, -31.04312, -10.35622, -29.98814, -13.10152, -29.98854, -13.1014, -22.23814, -12.94625, -23.31033, -10.89745, -22.2379, -12.9463, -22.2381, -12.94622, -19.97401, -11.63651, -20.93793, -9.79629, -19.97389, -11.63654, -19.97398, -11.63649, -16.8047, -9.06812, -17.55051, -7.52273, -16.80444, -9.06815, -16.80479, -9.06808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.48424, -6.90224, -13.48427, -6.90225]}, {"time": 0.3667, "offset": 128, "vertices": [9.7464, 7.67469, -8.56279, -8.97619, -6.30157, -10.68569, -6.30151, -10.6857, -6.30157, -10.68569, -13.27721, -9.54613, -10.76356, -12.311, -10.76336, -12.31099, -10.76358, -12.31097, -15.66049, -7.90686, -13.45675, -11.25554, -13.45657, -11.25558, -13.45672, -11.2555, -16.17643, -7.68375, -16.17621, -7.68379, -16.1764, -7.68377, -14.99428, -6.55074, -15.52156, -5.17811, -14.99407, -6.55076, -14.99427, -6.5507, -7.0444, -4.9455, -7.52641, -4.07426, -7.04427, -4.94555, -7.04443, -4.94549, -14.59496, -6.94444, -15.11618, -5.85157, -14.59492, -6.94449, -14.5951, -6.94441, -8.40235, -4.53406, -8.77525, -3.76136, -8.40222, -4.53408, -8.4024, -4.53404, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -12.68017, -1.90471, -12.68029, -1.90471, -6.12846, -0.30671, -6.07602, -0.85643, -6.07607, -0.85641]}, {"time": 0.5}]}}}, "drawOrder": [{"time": 0.0667, "offsets": [{"slot": "a17111111", "offset": 12}, {"slot": "a12111111", "offset": 7}, {"slot": "a11111111", "offset": 7}, {"slot": "a10111111", "offset": 7}, {"slot": "a9111111", "offset": 7}, {"slot": "a0", "offset": -7}]}], "events": [{"time": 0.2333, "name": "atk"}]}, "boss_attack2_2": {"bones": {"bone2": {"translate": [{"y": -3.3}]}, "bone4": {"translate": [{"x": 0.03, "y": 0.57}]}, "bone5": {"translate": [{"x": 1.86, "y": -0.06}]}, "bone6": {"rotate": [{"angle": 1.29}]}, "bone14": {"rotate": [{"angle": 2.45}]}, "bone15": {"rotate": [{"angle": 1.22}]}, "bone16": {"rotate": [{"angle": -1.98}]}, "bone32": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone36": {"rotate": [{"angle": 3.07}]}, "bone37": {"rotate": [{"angle": -8.17}]}, "bone38": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone42": {"rotate": [{"angle": 3.52}]}, "bone43": {"rotate": [{"angle": -5.29}]}, "bone48": {"translate": [{"x": 3.79, "y": -0.3}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44}]}, "bone95": {"rotate": [{"angle": -4.3}]}, "bone94": {"rotate": [{"angle": 1.2}]}, "bone53": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone11": {"rotate": [{"angle": 0.8}]}, "bone12": {"rotate": [{"angle": -4.77}]}, "bone13": {"rotate": [{"angle": -5.94}]}, "a4": {"rotate": [{"angle": -7.75}]}, "a5": {"rotate": [{"angle": -5.87}]}, "a6": {"rotate": [{"angle": 11.87}]}, "a7": {"rotate": [{"angle": 28.89}]}, "a8": {"rotate": [{"angle": 12.93}]}, "a3": {"rotate": [{"angle": -4.24}]}, "bone33": {"rotate": [{"angle": 2.53}]}, "bone34": {"rotate": [{"angle": -5.5}]}, "bone39": {"rotate": [{"angle": 3.27}]}, "bone40": {"rotate": [{"angle": -5.48}]}, "bone92": {"rotate": [{"angle": 1.99}]}, "bone93": {"rotate": [{"angle": -5.49}]}, "bone63": {"rotate": [{"angle": 16.24}]}, "bone57": {"rotate": [{"angle": -1.37}]}, "bone17": {"rotate": [{"angle": -6.57}]}, "bone22": {"rotate": [{"angle": 11.68}]}, "bone65": {"rotate": [{"angle": -12.62}]}, "bone60": {"rotate": [{"angle": -16.34}]}, "bone69": {"rotate": [{"angle": 1.42}]}, "bone56": {"rotate": [{"angle": 14.59}]}, "bone55": {"rotate": [{"angle": 9.18}]}, "bone62": {"rotate": [{"angle": 8.16}]}, "bone19": {"rotate": [{"angle": 3.05}]}, "bone66": {"rotate": [{"angle": 0.6}]}, "bone21": {"rotate": [{"angle": 12.38}]}, "bone64": {"rotate": [{"angle": -12.77}]}, "bone18": {"rotate": [{"angle": -8.95}]}, "bone59": {"rotate": [{"angle": -7.48}]}, "bone70": {"rotate": [{"angle": -8.28}], "translate": [{"x": 1.54, "y": -12.11}]}, "bone61": {"rotate": [{"angle": -4.18}]}, "bone20": {"rotate": [{"angle": 12.18}]}, "bone71": {"rotate": [{"angle": -4.46}], "translate": [{"x": 9.74, "y": -5.24}]}, "bone67": {"rotate": [{"angle": 18.87}]}, "bone68": {"rotate": [{"angle": 13.53}]}, "bone23": {"rotate": [{"angle": 3.6}]}}}, "boss_attack3": {"slots": {"a23": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "a21": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "a19": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "a24": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "a25": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "a20": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "skill/skill0/dg01/shui_00011": {"attachment": [{"time": 0.5, "name": "jushui/shui_00011"}, {"time": 0.5333, "name": "jushui/shui_00013"}, {"time": 0.5667, "name": "jushui/shui_00014"}, {"time": 0.6, "name": "jushui/shui_00015"}, {"time": 0.6333, "name": "jushui/shui_00016"}, {"time": 0.6667, "name": "jushui/shui_00018"}, {"time": 0.7, "name": "jushui/shui_00019"}, {"time": 0.7333, "name": null}]}, "jushui/xiezi_skill_xl_00003": {"attachment": [{"time": 0.1333, "name": "jushui/xiezi_skill_xl_00001"}, {"time": 0.1667, "name": "jushui/xiezi_skill_xl_00003"}, {"time": 0.2, "name": "jushui/xiezi_skill_xl_00005"}, {"time": 0.2333, "name": "jushui/xiezi_skill_xl_00007"}, {"time": 0.2667, "name": "jushui/xiezi_skill_xl_00009"}, {"time": 0.3, "name": "jushui/xiezi_skill_xl_00011"}, {"time": 0.3333, "name": "jushui/xiezi_skill_xl_00013"}, {"time": 0.3667, "name": "jushui/xiezi_skill_xl_00015"}, {"time": 0.4, "name": "jushui/xiezi_skill_xl_00017"}, {"time": 0.4333, "name": "jushui/xiezi_skill_xl_00019"}, {"time": 0.4667, "name": "jushui/xiezi_skill_xl_00021"}, {"time": 0.5, "name": null}]}, "a22": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}}, "bones": {"bone2": {"translate": [{"y": -3.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "y": 3.51, "curve": "stepped"}, {"time": 0.5, "y": 3.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "y": -3.3}]}, "bone4": {"rotate": [{}, {"time": 0.1667, "angle": 5.66, "curve": "stepped"}, {"time": 0.4333, "angle": 5.66}, {"time": 0.5, "angle": -9.2}, {"time": 0.8333}], "translate": [{"x": 0.03, "y": 0.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 0.39, "y": 7.38, "curve": "stepped"}, {"time": 0.5, "x": 0.39, "y": 7.38, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "x": 0.03, "y": 0.57}]}, "bone5": {"rotate": [{}, {"time": 0.1667, "angle": -2.93, "curve": "stepped"}, {"time": 0.4333, "angle": -2.93}, {"time": 0.5, "angle": -15.93}, {"time": 0.8333}], "translate": [{"x": 1.86, "y": -0.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 15.38, "y": -1.81, "curve": "stepped"}, {"time": 0.5, "x": 15.38, "y": -1.81, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "x": 1.86, "y": -0.06}]}, "bone6": {"rotate": [{"angle": 1.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -16.27, "curve": "stepped"}, {"time": 0.4333, "angle": -16.27, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5667, "angle": 4.33, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "angle": 1.29}]}, "bone14": {"rotate": [{"angle": 2.45, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 169.85, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3, "angle": 158.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "angle": 169.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 78.8, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.5667, "angle": 2.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": 2.45}], "translate": [{}, {"time": 0.1667, "x": -22.12, "y": 45.72, "curve": "stepped"}, {"time": 0.4333, "x": -22.12, "y": 45.72}, {"time": 0.5, "x": -17.53, "y": -30.92}, {"time": 0.5667, "x": -17.63, "y": 11.47}, {"time": 0.8333}]}, "bone15": {"rotate": [{"angle": 1.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -81.06, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3, "angle": -92.22, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "angle": -81.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": -91.34, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.5667, "angle": -85.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": 1.22}], "translate": [{"time": 0.4333}, {"time": 0.5, "x": 3.72, "y": -3.2}, {"time": 0.8333}]}, "bone16": {"rotate": [{"angle": -1.98, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 62.82, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.3, "angle": 51.66, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.4333, "angle": 62.82, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.4667, "angle": -19.81, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "angle": -116.13, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.5667, "angle": 159.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": -1.98}]}, "bone32": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 5.08, "y": -0.06, "curve": "stepped"}, {"time": 0.4333, "x": 5.08, "y": -0.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": 1.82, "y": -2.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "x": -0.97, "y": 0.01}]}, "bone36": {"rotate": [{"angle": 3.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 2.11, "curve": "stepped"}, {"time": 0.4333, "angle": 2.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 3.07}]}, "bone37": {"rotate": [{"angle": -8.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -5.92, "curve": "stepped"}, {"time": 0.4333, "angle": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": -8.17}]}, "bone38": {"translate": [{"x": -0.97, "y": 0.01, "curve": "stepped"}, {"time": 0.4333, "x": -0.97, "y": 0.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": -0.87, "y": 8.19, "curve": "stepped"}, {"time": 0.6333, "x": -0.87, "y": 8.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "x": -0.97, "y": 0.01}]}, "bone42": {"rotate": [{"angle": 3.52, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -8.28, "curve": "stepped"}, {"time": 0.4333, "angle": -8.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 3.52}]}, "bone43": {"rotate": [{"angle": -5.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 18.47, "curve": "stepped"}, {"time": 0.4333, "angle": 18.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": -5.29}]}, "bone48": {"translate": [{"x": 3.79, "y": -0.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 38.44, "y": -50.26, "curve": "stepped"}, {"time": 0.4333, "x": 38.44, "y": -50.26, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": -10.89, "y": -86.12, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.5667, "x": 18.46, "y": 58.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "x": 3.79, "y": -0.3}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44}]}, "bone95": {"rotate": [{"angle": -4.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 34.28, "curve": "stepped"}, {"time": 0.4333, "angle": 34.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": -4.3}]}, "bone94": {"rotate": [{"angle": 1.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -16.67, "curve": "stepped"}, {"time": 0.4333, "angle": -16.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 1.2}]}, "bone53": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone11": {"rotate": [{"angle": 0.8, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -4.99, "curve": "stepped"}, {"time": 0.4333, "angle": -4.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.88, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.8}]}, "bone12": {"rotate": [{"angle": -4.77, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": -27.4, "curve": "stepped"}, {"time": 0.4333, "angle": -27.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -25.82, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.77}]}, "bone13": {"rotate": [{"angle": -5.94, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 14.25, "curve": "stepped"}, {"time": 0.4333, "angle": 14.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.6333, "angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.94}]}, "a4": {"rotate": [{"angle": -7.75, "curve": "stepped"}, {"time": 0.4333, "angle": -7.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": -44.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "angle": -7.75}]}, "a5": {"rotate": [{"angle": -5.87, "curve": "stepped"}, {"time": 0.1667, "angle": -5.87, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.2333, "angle": 28.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": -5.87, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 16.49, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "angle": -5.87}]}, "a6": {"rotate": [{"angle": 11.87, "curve": "stepped"}, {"time": 0.1667, "angle": 11.87, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.2333, "angle": 74.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 87.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 60.08, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "angle": 11.87}]}, "a7": {"rotate": [{"angle": 28.89, "curve": "stepped"}, {"time": 0.1667, "angle": 28.89, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.2333, "angle": 91.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 104.72, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 77.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "angle": 28.89}]}, "a8": {"rotate": [{"angle": 12.93, "curve": "stepped"}, {"time": 0.1667, "angle": 12.93, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.2333, "angle": 75.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 88.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 61.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "angle": 12.93}]}, "a3": {"rotate": [{"angle": -4.24, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 58.1, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.2, "angle": -81.5, "curve": 0.334, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.2333, "angle": -169.92, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.2667, "angle": 105.29, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.3333, "angle": 3.4, "curve": 0.34, "c2": 0.37, "c3": 0.674, "c4": 0.71}, {"time": 0.3667, "angle": -81.55, "curve": 0.341, "c2": 0.39, "c3": 0.675, "c4": 0.73}, {"time": 0.4, "angle": 153.85, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.4333, "angle": 58.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": -99.64, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 0.5333, "angle": 128.65, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 0.8333, "angle": -4.24}]}, "bone3": {"translate": [{"time": 0.4333}, {"time": 0.5, "x": 27.34}, {"time": 0.8333}]}, "bone33": {"rotate": [{"angle": 2.53}, {"time": 0.1667, "angle": 1.71, "curve": "stepped"}, {"time": 0.4333, "angle": 1.71}, {"time": 0.5, "angle": 2.53}]}, "bone34": {"rotate": [{"angle": -5.5}, {"time": 0.1667, "angle": -3.8, "curve": "stepped"}, {"time": 0.4333, "angle": -3.8}, {"time": 0.5, "angle": -5.5}]}, "bone39": {"rotate": [{"angle": 3.27}, {"time": 0.1667, "angle": -6.27, "curve": "stepped"}, {"time": 0.4333, "angle": -6.27}, {"time": 0.5, "angle": 3.27}]}, "bone40": {"rotate": [{"angle": -5.48}, {"time": 0.1667, "angle": 14.11, "curve": "stepped"}, {"time": 0.4333, "angle": 14.11}, {"time": 0.5, "angle": -5.48}]}, "bone45": {"rotate": [{"time": 0.4333}, {"time": 0.5, "angle": -13.18}, {"time": 0.8333}]}, "yj": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 2.18, "y": -84.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "bone46": {"rotate": [{"time": 0.6333}, {"time": 0.7, "angle": -13.66}, {"time": 0.8333}], "translate": [{"time": 0.4333}, {"time": 0.5, "x": 38.29, "curve": "stepped"}, {"time": 0.6333, "x": 38.29}, {"time": 0.7, "x": 19.15, "y": 4.68}, {"time": 0.8333}]}, "bone24": {"rotate": [{}, {"time": 0.1667, "angle": -43.35, "curve": "stepped"}, {"time": 0.4333, "angle": -43.35}, {"time": 0.5}, {"time": 0.5667, "angle": -64.49}, {"time": 0.8333}], "translate": [{}, {"time": 0.1667, "x": 10.29, "y": 3.12, "curve": "stepped"}, {"time": 0.4333, "x": 10.29, "y": 3.12}, {"time": 0.5}]}, "bone7": {"rotate": [{"angle": -18.53}, {"time": 0.1667}], "translate": [{"x": -105.26, "y": 392.33}, {"time": 0.1667, "x": 83.39, "y": -2.73}], "scale": [{"time": 0.1667}, {"time": 0.4333, "x": 2, "y": 2}]}, "bone8": {"rotate": [{}, {"time": 0.1, "angle": -18.93}, {"time": 0.1667, "angle": -25.12}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4333}, {"time": 0.5, "angle": 19.62}, {"time": 0.5333, "angle": -2.34}, {"time": 0.5667, "angle": 15.18}, {"time": 0.8333}], "translate": [{}, {"time": 0.1667, "y": 6.06}, {"time": 0.2333, "x": -1.03, "y": -0.34}, {"time": 0.4333, "x": -4.1, "y": -1.37}, {"time": 0.5, "x": -11.62, "y": 4.1}, {"time": 0.5333, "x": 63.57, "y": -12.99}, {"time": 0.5667, "x": 35.54, "y": 9.57}, {"time": 0.8333}]}, "bone9": {"rotate": [{"time": 0.1667}, {"time": 0.4333, "angle": 24.61, "curve": "stepped"}, {"time": 0.5, "angle": 24.61}, {"time": 0.5333}], "translate": [{"time": 0.1667}, {"time": 0.4333, "y": -19.14, "curve": "stepped"}, {"time": 0.5, "y": -19.14}, {"time": 0.5333}]}, "bone27": {"rotate": [{"time": 0.4333}, {"time": 0.5, "angle": -27.52}, {"time": 0.5333, "angle": -55.34}, {"time": 0.5667, "angle": 10.35}, {"time": 0.8333}]}, "bone28": {"rotate": [{"time": 0.1667}, {"time": 0.4333, "angle": -40.77, "curve": "stepped"}, {"time": 0.5, "angle": -40.77}, {"time": 0.5333, "angle": 24.43, "curve": "stepped"}, {"time": 0.5667, "angle": 24.43}, {"time": 0.8333}]}, "bone73": {"translate": [{"time": 0.5}, {"time": 0.5333, "x": -5.28, "y": 1.43, "curve": "stepped"}, {"time": 0.5667, "x": -5.28, "y": 1.43}, {"time": 0.8333}]}, "j1": {"rotate": [{"angle": -135.37}, {"time": 0.1, "angle": -160.12}, {"time": 0.1667, "angle": -152.65}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5}, {"time": 0.5333, "angle": -51.88, "curve": "stepped"}, {"time": 0.5667, "angle": -51.88}, {"time": 0.8333}], "translate": [{"x": -30.16, "y": -24.48}, {"time": 0.1, "x": -54.1, "y": -16.65}, {"time": 0.1667, "x": -82.36, "y": -18.45}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5}, {"time": 0.5333, "x": -1.7, "y": 9.64, "curve": "stepped"}, {"time": 0.5667, "x": -1.7, "y": 9.64}, {"time": 0.8333}]}, "bone76": {"rotate": [{"time": 0.5}, {"time": 0.5333, "angle": 50.71, "curve": "stepped"}, {"time": 0.5667, "angle": 50.71}, {"time": 0.8333}]}, "j2": {"rotate": [{"angle": -135.37}, {"time": 0.1, "angle": -160.12}, {"time": 0.1667, "angle": -152.65}, {"time": 0.2333}], "translate": [{"x": -29.25, "y": -25.56}, {"time": 0.1, "x": -53.46, "y": -18.61}, {"time": 0.1667, "x": -81.64, "y": -21.43}, {"time": 0.2333}]}, "j3": {"rotate": [{"time": 0.4333}, {"time": 0.5, "angle": -22.96}, {"time": 0.5333}], "translate": [{"x": -9.25, "y": 12.74}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333}, {"time": 0.5, "x": -14.12, "y": -26.42}, {"time": 0.5333}]}, "j4": {"rotate": [{"time": 0.4333}, {"time": 0.5, "angle": -50.88}, {"time": 0.5333}], "translate": [{"x": -9.04, "y": 12.89}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333}, {"time": 0.5, "x": -24.25, "y": -29.97}, {"time": 0.5333}]}, "bone92": {"rotate": [{"angle": 1.99}, {"time": 0.1667, "angle": -11.45, "curve": "stepped"}, {"time": 0.4333, "angle": -11.45}, {"time": 0.5, "angle": 1.99}]}, "bone93": {"rotate": [{"angle": -5.49}, {"time": 0.1667, "angle": 27.43, "curve": "stepped"}, {"time": 0.4333, "angle": 27.43}, {"time": 0.5, "angle": -5.49}]}, "bone55": {"rotate": [{"angle": 9.18, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "angle": -0.19, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 11.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.8333, "angle": 9.18}]}, "bone67": {"rotate": [{"angle": 18.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": 12.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 20.08, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.8333, "angle": 18.87}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "bone68": {"rotate": [{"angle": 13.53, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -19.01, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8333, "angle": 13.53}]}, "bone71": {"rotate": [{"angle": -4.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8333, "angle": -4.46}], "translate": [{"x": 9.74, "y": -5.24, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "x": 14.68, "y": -6.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -12.34, "y": -0.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8333, "x": 9.74, "y": -5.24}]}, "bone63": {"rotate": [{"angle": 16.24, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0667, "angle": 18.55, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": 11.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5, "angle": -9.27, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.8333, "angle": 16.24}]}, "bone57": {"rotate": [{"angle": -1.37, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.1667, "angle": 21.37, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2333, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -25.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "angle": -1.37}]}, "bone17": {"rotate": [{"angle": -6.57, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": 6.52, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": 1.23, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.4333, "angle": -6.8, "curve": 0.322, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.5667, "angle": -84.11, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.8333, "angle": -6.57}], "translate": [{"curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "x": -25.39, "y": 66.98, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.4333, "x": -6.79, "y": 3.44, "curve": 0.346, "c2": 0.38, "c3": 0.683, "c4": 0.72}, {"time": 0.5, "x": -23.14, "y": 11.42, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6}]}, "bone22": {"rotate": [{"angle": 11.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": 12.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": 7.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4333, "angle": -6.1, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 0.7333, "angle": 33.07, "curve": 0.362, "c2": 0.45, "c3": 0.703, "c4": 0.82}, {"time": 0.8333, "angle": 11.68}]}, "bone65": {"rotate": [{"angle": -12.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -21.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -12.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5667, "angle": 14.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.8333, "angle": -12.62}]}, "bone60": {"rotate": [{"angle": -16.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -10.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4333, "angle": 6.04, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.8333, "angle": -15.96}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "bone69": {"rotate": [{"angle": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 82.26, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.3333, "angle": 72.77, "curve": 0.334, "c2": 0.34, "c3": 0.677, "c4": 0.7}, {"time": 0.4333, "angle": 81.06, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.5667, "angle": -36.72, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.8333, "angle": 1.42}], "translate": [{"curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1333, "x": -15.06, "y": -12.03, "curve": 0.327, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": -17.76, "y": -8.76, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.3333, "x": -16.6, "y": -18.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4333, "x": -16.36, "y": -20.52, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5667, "x": -6.46, "y": -20.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333}]}, "bone56": {"rotate": [{"angle": 14.59, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0667, "angle": 17.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": 9.8, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5, "angle": -13, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.8333, "angle": 14.59}]}, "bone62": {"rotate": [{"angle": 8.16, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "angle": -3.17, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": -9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 10.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.8333, "angle": 8.16}]}, "bone19": {"rotate": [{"angle": 3.05, "curve": 0.326, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.1, "angle": -4.83, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.2667, "angle": -17.72, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.4333, "angle": 14.07, "curve": 0.34, "c2": 0.35, "c3": 0.691, "c4": 0.74}, {"time": 0.6, "angle": 37.96, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.7333, "angle": -30.28, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.8333, "angle": 3.05}]}, "bone66": {"rotate": [{"angle": 0.6, "curve": 0.335, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.1, "angle": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 6.13, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.8333, "angle": 0.6}]}, "bone21": {"rotate": [{"angle": 12.38, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": 8.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 43.73, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 12.38}]}, "bone64": {"rotate": [{"angle": -12.77, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.2667, "angle": 6.06, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4, "angle": 14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -12.77}]}, "bone18": {"rotate": [{"angle": -8.95, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.1, "angle": -14.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -8.83, "curve": 0.322, "c2": 0.3, "c3": 0.682, "c4": 0.71}, {"time": 0.4333, "angle": 41.03, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.5667, "angle": 25.74, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.6667, "angle": -38.35, "curve": 0.318, "c2": 0.29, "c3": 0.678, "c4": 0.7}, {"time": 0.8333, "angle": -8.95}]}, "bone59": {"rotate": [{"angle": -7.48, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1667, "angle": -1.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -10.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.8333, "angle": -7.48}]}, "bone70": {"rotate": [{"angle": -8.28, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": -5.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -8.81, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.8333, "angle": -8.28}], "translate": [{"x": 1.54, "y": -12.11, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "x": -1.34, "y": -9.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "x": -12.11, "y": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 2.1, "y": -12.71, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.8333, "x": 1.54, "y": -12.11}]}, "bone61": {"rotate": [{"angle": -4.18, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.1667, "angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 16.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.8333, "angle": -4.18}]}, "bone20": {"rotate": [{"angle": 12.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": 7.74, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.2667, "angle": -9.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4333, "angle": 14.83, "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "angle": 41.83, "curve": 0.325, "c2": 0.31, "c3": 0.666, "c4": 0.67}, {"time": 0.7333, "angle": -44.16, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8333, "angle": 12.18}]}, "bone23": {"rotate": [{"angle": 3.6, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.1667, "angle": 16.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -14.02, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.7333, "angle": 19.62, "curve": 0.322, "c2": 0.3, "c3": 0.664, "c4": 0.66}, {"time": 0.8333, "angle": 3.6}]}, "bone72": {"rotate": [{"angle": 9.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": 50, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4333, "angle": 9.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5667, "angle": 50, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.8333, "angle": 9.22}]}, "bone52": {"rotate": [{"angle": 40.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 50, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4333, "angle": 40.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4667, "angle": 50, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "angle": 25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8333, "angle": 40.77}]}, "bone30": {"rotate": [{"angle": -4.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": -26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4333, "angle": -4.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5667, "angle": -26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.8333, "angle": -4.95}]}, "bone29": {"rotate": [{"angle": -21.9, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -26.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -13.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4333, "angle": -21.9, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4667, "angle": -26.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "angle": -13.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8333, "angle": -21.9}]}, "bone10": {"rotate": [{"angle": -3.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 12.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 2.31, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4333, "angle": -3.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 12.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8, "angle": 2.31, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.8333, "angle": -3.85}]}, "bone25": {"rotate": [{"angle": 38.19, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 48.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4333, "angle": 38.19, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 48.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.8333, "angle": 38.19}]}, "bone26": {"rotate": [{"angle": 38.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 48.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 20.55, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4333, "angle": 38.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4667, "angle": 48.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -7.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8, "angle": 20.55, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.8333, "angle": 38.28}]}, "juqi": {"translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -21.86, "y": 39.74, "curve": "stepped"}, {"time": 0.4333, "x": -21.86, "y": 39.74, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 256.32, "y": -37.75}], "scale": [{"time": 0.1333, "x": 3.309, "y": 3.309}]}, "shui": {"rotate": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 44.68}], "scale": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.717, "y": 1.717}]}}, "deform": {"default": {"a1411": {"a14": [{"time": 0.4333}, {"time": 0.5, "vertices": [-9.36735, -1.27077, -9.36735, -1.27077, -9.36735, -1.27077, -9.36735, -1.27077, -10.00366, -3.16337, -9.36735, -1.27077, -10.00366, -3.16337, -9.36735, -1.27077, -10.00366, -3.16337, -8.85953, -1.05557, -9.4949, -2.84863, -6.99984, 7.78967, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.36735, -1.27077, -10.00366, -3.16337, -9.36735, -1.27077, -10.00366, -3.16337, -9.36735, -1.27077, -9.36735, -1.27077, -9.36735, -1.27077, -10.00366, -3.16337]}, {"time": 0.8333}]}, "a9111111": {"a9": [{}, {"time": 0.1, "vertices": [2.47058, -13.55216, -8.84706, 10.55859, -10.29949, 9.14747, -12.75147, 5.2111, 3.66428, -8.22596, -7.25171, 5.33861, -7.95608, 4.21779, -8.90494, 1.33851, 3.37491, -2.59274, -4.21533, 0.58503, -4.25557, -0.03926, -4.00139, -1.44926, 2.41764, 0.7171, -0.26059, -3.56834, 0.24691, -3.40416, 1.50013, -3.46473, 6.45963, 6.09404, -2.60237, -8.49083, -1.32945, -8.78059, 1.65977, -8.72421, 14.74746, 14.63711, -10.39568, -14.48572, -8.09454, -16.37546, -2.65321, -17.06781, 23.09024, 24.17057, 23.377, 27.82024, 23.78271, 35.15954, 16.03575, 31.74844, -11.34889, -28.54164, -0.42387, 30.22444, 0.6901, -30.24703, -13.17323, 17.62452, 6.17645, -21.50906, 3.13365, -23.61105, -16.42714, 7.7452, 4.91573, -15.51204, 2.34481, -15.9224, -4.3865, 0.17133, 1.86213, -3.97526, -0.01094, -4.3898, 0.94561, -1.64119, 0.15537, -1.88775, 0.26074, -0.78502, -0.09895, -0.82126, -0.07236, -0.82402, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.29875, 1.9631, 2.96875, -0.57018, 3.02024, -0.12873, 2.89178, 0.88083, -5.13692, 4.72363, 6.8006, -1.56692, 6.95679, -0.55289, 6.74601, 1.78703, -4.96319, 5.93577, 7.24881, -2.70648, 7.56725, -1.61445, 7.67415, 0.98819, -2.69235, 3.35239, 3.99768, -1.58331, 4.18659, -0.98008, 4.27456, 0.46477, -1.29135, -1.40071, 0.4301, 1.85581, 0.15332, 1.89881, -0.48553, 1.84206, 0.15093, -6.71068, -3.44858, 5.75833, -4.25558, 5.19054, -5.73693, 3.48418, 0.19062, -11.3173, -5.76036, 9.74295, -7.12658, 8.79319, -9.64075, 5.92988, -0.70065, -15.73625, -7.17013, 14.02476, -9.1489, 12.82206, -12.88557, 9.05932, 0.7156, 17.52172, -4.0529, 14.16709, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.64306, 3.72103, 5.43266, -5.97541, 6.62134, -4.8429, 7.23065, -3.16867, -7.83859, 6.76187, 7.67483, -10.94756, 9.70309, -9.2706, 11.419, -6.74643, -0.63106, 5.56967, 3.30203, -4.5297, 3.93039, -3.99663, 5.03389, -2.46588]}, {"time": 0.1333, "vertices": [3.29411, -18.06955, -11.79609, 14.07812, -13.73266, 12.19662, -17.00196, 6.94813, 4.88571, -10.96795, -9.66895, 7.11815, -10.6081, 5.62372, -11.87326, 1.78468, 4.49988, -3.45699, -5.62044, 0.78004, -5.67409, -0.05235, -5.33519, -1.93235, 3.70514, 1.67042, -1.20575, -4.68359, -0.52085, -4.67868, 1.17132, -4.85474, 10.12777, 7.79219, -4.78678, -10.50156, -3.24779, -11.22801, 0.67805, -11.40945, 23.45628, 16.73817, -15.95728, -17.29688, -13.31528, -20.36037, -6.22451, -21.88014, 39.03488, 26.7166, 42.91785, 35.49811, 42.87847, 43.23184, 30.95149, 40.78521, -22.98583, -37.68616, 3.64132, 36.63374, -0.5631, -39.13364, -16.3742, 20.46831, 10.01657, -27.54632, 5.61996, -29.93213, -19.61804, 7.9073, 7.56018, -17.51045, 3.31876, -17.90748, -5.84866, 0.22843, 2.48284, -5.30034, -0.01458, -5.85306, 1.26082, -2.18826, 0.20716, -2.517, 0.34765, -1.04669, -0.13193, -1.09501, -0.09647, -1.09869, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.065, 2.61746, 3.95833, -0.76024, 4.02699, -0.17165, 3.85571, 1.17445, -6.84923, 6.29817, 9.06747, -2.08923, 9.27572, -0.73718, 8.99467, 2.38271, -6.61758, 7.91436, 9.66508, -3.60864, 10.08967, -2.1526, 10.2322, 1.31758, -3.58981, 4.46985, 5.33024, -2.11108, 5.58212, -1.30678, 5.69942, 0.61969, -1.72179, -1.86761, 0.57346, 2.47441, 0.20442, 2.53175, -0.64737, 2.45609, 0.20124, -8.94757, -4.59811, 7.67778, -5.6741, 6.92071, -7.64925, 4.64558, 0.25416, -15.08974, -7.68048, 12.9906, -9.50211, 11.72426, -12.85434, 7.90651, -0.9342, -20.98166, -9.56018, 18.69968, -12.19853, 17.09608, -17.18076, 12.07909, 3.61138, 22.47381, -5.40386, 18.88945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.82997, 7.53125, 6.60936, -8.83279, 8.40812, -7.45972, 9.48933, -5.18427, -8.95665, 9.54814, 10.23335, -12.71298, 12.46836, -10.54817, 14.57923, -7.1335, -0.84142, 7.42623, 4.4027, -6.0396, 5.24052, -5.32884, 6.71186, -3.28783]}, {"time": 0.2667, "vertices": [6.58821, -36.1391, -23.59217, 28.15625, -27.46532, 24.39325, -34.00392, 13.89627, 9.77142, -21.9359, -19.33789, 14.2363, -21.21621, 11.24744, -23.74652, 3.56937, 8.99976, -6.91397, -11.24088, 1.56009, -11.34818, -0.10471, -10.67038, -3.8647, 8.85513, 5.48369, -4.98638, -9.14459, -3.59191, -9.77676, -0.14394, -10.41475, 17.22568, 16.25076, -6.93967, -22.64221, -3.54521, -23.41492, 4.42604, -23.26456, 31.77684, 27.53868, -14.00811, -39.64792, -8.04406, -41.27316, 6.10834, -41.60378, 41.45914, 41.40924, 42.22385, 51.14169, 42.06136, 61.16251, 22.64716, 61.6169, -0.35028, -82.50554, -15.14409, 57.10015, 29.18715, -81.49982, -37.94357, 27.11797, 35.21724, -50.16549, 24.75007, -51.36893, -32.38161, 8.55569, 18.13796, -25.50407, 7.21456, -25.84783, -11.69733, 0.45687, 4.96568, -10.60068, -0.02917, -11.70613, 2.52163, -4.37651, 0.41432, -5.03399, 0.6953, -2.09338, -0.26386, -2.19003, -0.19295, -2.19739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.13, 5.23492, 7.91666, -1.52048, 8.05398, -0.34329, 7.71143, 2.34889, -13.69846, 12.59634, 18.13493, -4.17847, 18.55144, -1.47437, 17.98935, 4.76541, -13.23517, 15.82872, 19.33017, -7.21729, 20.17934, -4.30521, 20.4644, 2.63516, -7.17961, 8.9397, 10.66048, -4.22217, 11.16424, -2.61356, 11.39883, 1.23938, -3.44359, -3.73523, 1.14693, 4.94882, 0.40884, 5.06351, -1.29474, 4.91217, 0.40248, -17.89514, -9.19622, 15.35556, -11.34821, 13.84143, -15.29849, 9.29115, 0.50832, -30.17947, -15.36097, 25.9812, -19.00422, 23.44852, -25.70868, 15.81302, -1.86839, -41.96333, -19.12035, 37.39935, -24.39707, 34.19217, -34.36153, 24.15819, 3.59537, 44.18907, -10.80772, 37.7789, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.72658, 9.0331, 9.44387, -5.02228, 10.07804, -3.58334, 10.69591, -0.03606, -3.48782, 18.07846, 11.96944, -13.99188, 13.89106, -12.08542, 17.11453, -6.79085, -1.68283, 14.85246, 8.8054, -12.07919, 10.48104, -10.65768, 13.42372, -6.57567]}, {"time": 0.4333, "vertices": [6.32661, -39.11224, -25.29885, 30.60475, -27.12492, 27.35841, -33.03448, 16.7191, 9.64734, -23.81294, -20.44688, 15.75575, -21.04237, 13.12054, -23.17435, 5.36139, 8.99976, -6.91397, -11.24088, 1.56009, -11.34818, -0.10471, -10.67038, -3.8647, 8.85513, 5.48369, -4.98638, -9.14459, -3.59191, -9.77676, -0.14394, -10.41475, 17.22568, 16.25076, -6.93967, -22.64221, -3.54521, -23.41492, 4.42604, -23.26456, 31.77684, 27.53868, -14.00811, -39.64792, -8.04406, -41.27316, 6.10834, -41.60378, 41.45914, 41.40924, 42.14885, 47.99142, 40.12001, 53.9636, 22.64716, 61.6169, -0.35028, -82.50554, -24.01839, 61.59528, 38.87852, -79.25469, -54.76987, 42.72761, 58.13912, -51.34452, 41.15605, -67.41981, -50.02757, 23.66329, 41.36647, -25.7722, 24.45326, -41.4186, -24.46725, 7.31148, 19.15899, -7.66622, 12.5543, -18.89734, 6.07567, -2.08959, 4.59222, -5.67096, 0.6953, -2.09338, -0.26386, -2.19003, -0.19295, -2.19739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.13, 5.23492, 7.91666, -1.52048, 8.05398, -0.34329, 7.71143, 2.34889, -13.69846, 12.59634, 18.13493, -4.17847, 18.55144, -1.47437, 17.98935, 4.76541, -13.23517, 15.82872, 19.33017, -7.21729, 20.17934, -4.30521, 20.4644, 2.63516, -7.17961, 8.9397, 10.66048, -4.22217, 11.16424, -2.61356, 11.39883, 1.23938, -3.44359, -3.73523, 1.14693, 4.94882, 0.40884, 5.06351, -1.29474, 4.91217, -0.26959, -19.70345, -9.84073, 17.17387, -10.62836, 15.63121, -14.21095, 10.88452, 0.61717, -31.97443, -16.59599, 27.28827, -19.06539, 25.24574, -25.38234, 17.58144, -1.45787, -43.12822, -20.18254, 38.02953, -24.77651, 35.36749, -34.4796, 25.38762, 6.7654, 44.12865, -3.12178, 32.47139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.78168, -3.26939, -4.99797, 0.08171, -3.69357, 3.36826, -2.88367, 4.08321, -1.42052, 4.79247, -3.99014, -1.73553, 1.9476, 3.89107, 4.03482, 1.62874, 4.29051, 0.724, -5.50136, 13.05638, 11.85245, -8.25307, 9.74614, -7.59938, 9.509, -3.88699, -6.37527, 52.7697, 36.44142, -38.75079, 15.85647, -46.84193, 11.56715, -41.15741, -1.68283, 14.85246, 8.8054, -12.07919, 10.48104, -10.65768, 13.42372, -6.57567]}, {"time": 0.5667, "vertices": [5.97549, -19.67809, 7.65131, 5.03405, 6.93578, 8.62761, 5.22333, -1.42839, 5.75822, -13.81542, 5.72047, 2.52874, 5.25993, 5.26409, 4.55889, -2.40392, 3.77703, -5.98309, 2.75081, 0.02388, 2.54582, 1.3518, 2.55949, -2.51662, 1.34721, -0.20192, -0.02484, -2.78816, 0.5156, -2.5414, 0.6183, -3.28547, 4.11957, 5.97591, -3.52448, -2.70636, -3.32618, -3.66545, -0.62203, -2.28365, 2.11559, 12.92789, -9.91644, -6.77308, -9.58675, -11.58864, -6.96824, -6.24696, 2.49524, 23.2513, -7.41771, 31.46453, -9.92615, 31.03563, -12.74216, 22.92219, -3.27282, -31.53502, -24.8959, 17.94038, 10.1937, -32.0928, -30.16494, 10.33915, 17.84742, -25.77085, 6.98046, -32.30498, -24.26263, 6.27166, 13.45303, -18.52609, 3.87358, -22.58749, -11.44396, 1.71815, 6.61258, -7.46963, 2.49453, -10.3349, 2.62725, -2.86877, 1.15046, -3.95323, 0.52843, -1.59097, -0.20053, -1.66442, -0.14664, -1.67001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.226, 1.04699, 1.58333, -0.3041, 1.6108, -0.06866, 1.54229, 0.46978, -2.73969, 2.51927, 3.62699, -0.83569, 3.71029, -0.29487, 3.59787, 0.95308, -2.64704, 3.16575, 3.86604, -1.44346, 4.03587, -0.86104, 4.09288, 0.52703, -1.43592, 1.78794, 2.1321, -0.84443, 2.23285, -0.52271, 2.27977, 0.24788, 1.45296, -4.75408, 4.77124, 1.11408, 4.32977, 2.6245, 4.1189, -0.23277, 2.45727, -11.08657, 5.54666, 2.48624, 5.28277, 4.7032, 4.06221, -0.93706, 2.59669, -19.81925, 9.79493, 1.66839, 9.81773, 5.78585, 6.34542, -3.95773, 1.87984, -27.4026, 13.7303, 1.15347, 13.94545, 6.82628, 8.19133, -6.30875, -11.99171, 21.00261, -2.32171, 5.85918, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75634, -0.65388, -0.99959, 0.01634, -0.73872, 0.67365, -0.57673, 0.81664, -0.2841, 0.9585, -0.79803, -0.34711, 0.38952, 0.77821, 0.80696, 0.32575, 0.8581, 0.1448, -1.10027, 2.61128, 2.37049, -1.65061, 1.94923, -1.51988, 1.9018, -0.7774, -1.27506, 10.55395, 7.28829, -7.75017, 3.1713, -9.36839, 2.31343, -8.23149, -0.33657, 2.9705, 1.76108, -2.41584, 2.09621, -2.13154, 2.68475, -1.31514]}, {"time": 0.6, "vertices": [5.88771, -14.81955, 15.88885, -1.35863, 15.45096, 3.9449, 14.7878, -5.96527, 4.78594, -11.31604, 12.26231, -0.77802, 11.83551, 3.29997, 11.4922, -4.34525, 2.47134, -5.75037, 6.24873, -0.36017, 6.01932, 1.71593, 5.86696, -2.1796, -0.52977, -1.62332, 1.21555, -1.19905, 1.54248, -0.73256, 0.80885, -1.50314, 0.84304, 3.4072, -2.67068, 2.2776, -3.27142, 1.27192, -1.88405, 2.96158, -3.14046, 12.24081, -12.35326, 2.66412, -12.54193, -1.54917, -11.02609, 6.17487, -1.17781, 14.9015, -2.63068, 18.51702, -5.96592, 19.95049, -9.59473, 10.20763, -4.52702, -6.4295, -21.74848, 6.12304, 2.92335, -16.81779, -24.0137, 2.24202, 7.77448, -19.37743, -1.56344, -23.52626, -17.82138, 1.92374, 6.47466, -16.71456, -1.27135, -17.87971, -8.18813, 0.31981, 3.47598, -7.42048, -0.02042, -8.19429, 1.76514, -3.06356, 0.29002, -3.5238, 0.48671, -1.46536, -0.1847, -1.53302, -0.13506, -1.53817, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.6771, -5.00879, 5.67732, 0.1554, 5.31, 2.01475, 5.47231, -1.51901, 3.13899, -8.93234, 9.39351, -1.18567, 9.26056, 1.97119, 8.63051, -3.89246, 3.09157, -16.78046, 16.39267, -4.73659, 17.03851, 0.92088, 14.27737, -9.34253, 2.71427, -23.47119, 22.20853, -8.06555, 23.62595, -0.30903, 18.85907, -14.23285, -2.90349, 13.59088]}, {"time": 0.8333}]}, "a11111111": {"a11": [{}, {"time": 0.1, "vertices": [18.15843, 6.33267, 12.68008, 4.64462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.68008, 4.64462, 18.15843, 6.33267]}, {"time": 0.1667, "vertices": [18.15843, 6.33267, 12.68008, 4.64462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.21672, 3.18045, 18.15843, 6.33267], "curve": "stepped"}, {"time": 0.4333, "vertices": [18.15843, 6.33267, 12.68008, 4.64462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.21672, 3.18045, 18.15843, 6.33267]}, {"time": 0.8333}]}, "a13": {"a13": [{}, {"time": 0.1667, "offset": 128, "vertices": [5.73318, 4.51452, -5.03693, -5.28012, -3.7068, -6.2857, -3.70677, -6.28571, -3.7068, -6.2857, -7.81013, -5.61537, -6.33151, -7.24176, -6.33139, -7.24176, -6.33152, -7.24175, -9.21205, -4.6511, -7.91574, -6.62091, -7.91563, -6.62093, -7.91572, -6.62089, -9.51555, -4.51985, -9.51542, -4.51988, -9.51553, -4.51987, -8.82016, -3.85338, -9.13033, -3.04595, -8.82004, -3.85339, -8.82016, -3.85335, -6.54063, -3.80772, -6.85598, -3.20513, -6.54056, -3.80773, -6.54062, -3.80771, -5.87471, -3.4225, -6.15821, -2.88126, -5.87467, -3.42251, -5.8747, -3.4225, -4.94256, -2.66709, -5.16191, -2.21257, -4.94248, -2.6671, -4.94259, -2.66708, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.96595, -2.03007, -3.96596, -2.03007], "curve": "stepped"}, {"time": 0.4333, "offset": 128, "vertices": [5.73318, 4.51452, -5.03693, -5.28012, -3.7068, -6.2857, -3.70677, -6.28571, -3.7068, -6.2857, -7.81013, -5.61537, -6.33151, -7.24176, -6.33139, -7.24176, -6.33152, -7.24175, -9.21205, -4.6511, -7.91574, -6.62091, -7.91563, -6.62093, -7.91572, -6.62089, -9.51555, -4.51985, -9.51542, -4.51988, -9.51553, -4.51987, -8.82016, -3.85338, -9.13033, -3.04595, -8.82004, -3.85339, -8.82016, -3.85335, -6.54063, -3.80772, -6.85598, -3.20513, -6.54056, -3.80773, -6.54062, -3.80771, -5.87471, -3.4225, -6.15821, -2.88126, -5.87467, -3.42251, -5.8747, -3.4225, -4.94256, -2.66709, -5.16191, -2.21257, -4.94248, -2.6671, -4.94259, -2.66708, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.96595, -2.03007, -3.96596, -2.03007]}, {"time": 0.5, "offset": 128, "vertices": [19.49281, 15.34938, -17.12557, -17.95239, -12.60313, -21.37139, -12.60301, -21.3714, -12.60313, -21.37138, -26.55443, -19.09226, -21.52711, -24.62199, -21.52672, -24.62198, -21.52716, -24.62194, -31.32098, -15.81372, -26.9135, -22.51109, -26.91313, -22.51116, -26.91344, -22.51101, -32.35286, -15.3675, -32.35242, -15.36758, -32.3528, -15.36754, -29.98856, -13.10148, -31.04312, -10.35622, -29.98814, -13.10152, -29.98854, -13.1014, -22.23814, -12.94625, -23.31033, -10.89745, -22.2379, -12.9463, -22.2381, -12.94622, -19.97401, -11.63651, -20.93793, -9.79629, -19.97389, -11.63654, -19.97398, -11.63649, -16.8047, -9.06812, -17.55051, -7.52273, -16.80444, -9.06815, -16.80479, -9.06808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.48424, -6.90224, -13.48427, -6.90225]}, {"time": 0.6333, "offset": 128, "vertices": [9.7464, 7.67469, -8.56279, -8.97619, -6.30157, -10.68569, -6.30151, -10.6857, -6.30157, -10.68569, -13.27721, -9.54613, -10.76356, -12.311, -10.76336, -12.31099, -10.76358, -12.31097, -15.66049, -7.90686, -13.45675, -11.25554, -13.45657, -11.25558, -13.45672, -11.2555, -20.297, -4.36327, -20.29674, -4.36333, -20.29679, -4.36326, -17.2119, -4.044, -17.6299, -2.57883, -17.21163, -4.04405, -17.21175, -4.04395, -12.39849, -1.27062, -12.71846, -0.17377, -12.39835, -1.27078, -12.39821, -1.27057, -12.79702, -7.19393, -13.3306, -6.1777, -12.79699, -7.194, -12.79713, -7.19389, -8.40235, -4.53406, -8.77525, -3.76136, -8.40222, -4.53408, -8.4024, -4.53404, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -12.68017, -1.90471, -12.68029, -1.90471, -6.12846, -0.30671, -6.07602, -0.85643, -6.07607, -0.85641]}, {"time": 0.8333}]}}}, "drawOrder": [{"offsets": [{"slot": "a19", "offset": 7}, {"slot": "a18", "offset": -7}, {"slot": "a17111111", "offset": -7}, {"slot": "a16", "offset": -7}, {"slot": "a15", "offset": -7}]}, {"time": 0.0667, "offsets": [{"slot": "a17111111", "offset": 12}, {"slot": "a12111111", "offset": 7}, {"slot": "a11111111", "offset": 7}, {"slot": "a10111111", "offset": 7}, {"slot": "a9111111", "offset": 7}, {"slot": "a0", "offset": -7}]}, {"time": 0.2, "offsets": [{"slot": "a17111111", "offset": 17}, {"slot": "a12111111", "offset": 6}, {"slot": "a11111111", "offset": 6}, {"slot": "a10111111", "offset": 6}, {"slot": "a9111111", "offset": 6}, {"slot": "a0", "offset": -7}]}, {"time": 0.4333, "offsets": [{"slot": "a19", "offset": 7}, {"slot": "a18", "offset": -7}, {"slot": "a17111111", "offset": -7}, {"slot": "a16", "offset": -7}, {"slot": "a15", "offset": -7}]}, {"time": 0.5667, "offsets": [{"slot": "a17111111", "offset": 12}, {"slot": "a12111111", "offset": 7}, {"slot": "a11111111", "offset": 7}, {"slot": "a10111111", "offset": 7}, {"slot": "a9111111", "offset": 7}, {"slot": "a0", "offset": -7}]}, {"time": 0.8333, "offsets": [{"slot": "a19", "offset": 7}, {"slot": "a18", "offset": -7}, {"slot": "a17111111", "offset": -7}, {"slot": "a16", "offset": -7}, {"slot": "a15", "offset": -7}]}], "events": [{"time": 0.5333, "name": "atk"}]}, "boss_idle": {"bones": {"bone2": {"translate": [{"y": -3.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -3.3}]}, "bone4": {"translate": [{"x": 0.03, "y": 0.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.16, "y": 3.1, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.03, "y": 0.57}]}, "bone5": {"translate": [{"x": 1.86, "y": -0.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 3.72, "y": -0.11, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 1.86, "y": -0.06}]}, "bone6": {"rotate": [{"angle": 1.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 1.29}]}, "bone14": {"rotate": [{"angle": 2.45, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 3.85, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -1.94, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 2.45}]}, "bone15": {"rotate": [{"angle": 1.22, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 4.36, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -0.61, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 1.22}]}, "bone16": {"rotate": [{"angle": -1.98, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -2.06, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -1.98}]}, "bone32": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.97, "y": 0.01}]}, "bone36": {"rotate": [{"angle": 0.56}]}, "bone37": {"rotate": [{"angle": -1.01}]}, "bone38": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.97, "y": 0.01}]}, "bone42": {"rotate": [{"angle": -0.42}]}, "bone43": {"rotate": [{"angle": 1.77}]}, "bone48": {"translate": [{"x": 3.79, "y": -0.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 7.58, "y": -0.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 3.79, "y": -0.3}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 8.11, "y": -0.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 4.06, "y": -0.44}]}, "bone95": {"rotate": [{"angle": -1.01}]}, "bone94": {"rotate": [{"angle": 0.56}]}, "bone53": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.97, "y": 0.01}]}, "bone11": {"rotate": [{"angle": 0.8, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.63, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 0.8}]}, "bone12": {"rotate": [{"angle": -4.77, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -6.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -4.77}]}, "bone13": {"rotate": [{"angle": -5.94, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": -5.94}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone55": {"rotate": [{"angle": 9.18, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": -0.19, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5333, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 11.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 9.18}]}, "bone56": {"rotate": [{"angle": 14.59, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": 17.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3, "angle": 9.8, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7667, "angle": -13, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": 14.59}]}, "bone57": {"rotate": [{"angle": -1.37, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.3, "angle": 21.37, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.3333, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -25.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.37}]}, "bone62": {"rotate": [{"angle": 8.16, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": -3.17, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5333, "angle": -9.27, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 10.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 8.16}]}, "bone63": {"rotate": [{"angle": 16.24, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": 18.55, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3, "angle": 11.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7667, "angle": -9.27, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": 16.24}]}, "bone64": {"rotate": [{"angle": -12.77, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.4, "angle": 6.06, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.6333, "angle": 14.44, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -13.09, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -12.77}]}, "bone65": {"rotate": [{"angle": -12.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -21.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4, "angle": -12.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8667, "angle": 14.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -12.62}]}, "bone59": {"rotate": [{"angle": -7.48, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": -1.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -10.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -7.48}]}, "bone60": {"rotate": [{"angle": -15.96, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -16.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": -10.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": 6.04, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": -15.96}]}, "bone61": {"rotate": [{"angle": -4.18, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.2333, "angle": -15.77, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2667, "angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 16.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -4.18}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone66": {"rotate": [{"angle": 0.6, "curve": 0.335, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.1333, "angle": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3667, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 6.13, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.3333, "angle": 0.6}]}, "bone67": {"rotate": [{"angle": 18.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": 12.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 20.08, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": 18.87}]}, "bone68": {"rotate": [{"angle": 13.53, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": 20.12, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -19.01, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 13.53}]}, "bone17": {"rotate": [{"angle": -6.57, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.2667, "angle": 1.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": 5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -9.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -6.57}]}, "bone18": {"rotate": [{"angle": -8.95, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.1333, "angle": -13.65, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.2, "angle": -14.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4, "angle": -8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8667, "angle": 9.47, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -8.95}]}, "bone19": {"rotate": [{"angle": 3.05, "curve": 0.326, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.1333, "angle": -4.83, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.4, "angle": -17.21, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.4333, "angle": -17.72, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 12.18, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 3.05}]}, "bone20": {"rotate": [{"angle": 12.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 7.74, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.4, "angle": -9.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": -22.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 12.18}]}, "a4": {"rotate": [{"angle": -7.75, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -7.95, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": -5.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": 3.5, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": -7.75}]}, "a5": {"rotate": [{"angle": -5.87, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.2333, "angle": -14.19, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2667, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 9.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -5.87}]}, "a6": {"rotate": [{"angle": 11.87, "curve": 0.312, "c2": 0.27, "c3": 0.664, "c4": 0.66}, {"time": 0.2333, "angle": -6.09, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "angle": -20.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 19.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 11.87}]}, "a7": {"rotate": [{"angle": 28.89, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": 31.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2333, "angle": 21.33, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7333, "angle": -22.06, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "angle": 28.89}]}, "a8": {"rotate": [{"angle": 12.93, "curve": 0.362, "c2": 0.44, "c3": 0.715, "c4": 0.84}, {"time": 0.2333, "angle": 36.5, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.3, "angle": 39.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -22.06, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "angle": 12.93}]}, "a3": {"rotate": [{"angle": -4.24, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": -0.61, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 1.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -6.19, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -4.24}]}, "bone69": {"rotate": [{"angle": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -5.7, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 1.42}]}, "bone70": {"rotate": [{"angle": -8.28, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": -5.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -8.81, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -8.28}], "translate": [{"x": 1.54, "y": -12.11, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "x": -1.34, "y": -9.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "x": -12.11, "y": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 2.1, "y": -12.71, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "x": 1.54, "y": -12.11}]}, "bone71": {"rotate": [{"angle": -4.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": -6.29, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -4.46}], "translate": [{"x": 9.74, "y": -5.24, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "x": 14.22, "y": -6.22, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "x": 14.68, "y": -6.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -12.34, "y": -0.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 9.74, "y": -5.24}]}, "bone21": {"rotate": [{"angle": 12.38, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.2667, "angle": 8.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": 5.47, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 13.94, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 12.38}]}, "bone22": {"rotate": [{"angle": 11.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": 12.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": 7.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7333, "angle": -6.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "angle": 11.68}]}, "bone23": {"rotate": [{"angle": 3.6, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.2667, "angle": 16.43, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.3, "angle": 16.96, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -14.02, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "angle": 3.6}]}}}, "die": {"slots": {"a4": {"color": [{"time": 0.1333, "color": "ffffff00"}]}, "a5": {"color": [{"time": 0.1333, "color": "ffffffff"}]}}, "bones": {"bone2": {"translate": [{"y": -3.3, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 3.66, "y": 66.56, "curve": 0.809, "c3": 0.75}, {"time": 0.2667, "x": 42.53, "y": -3.3, "curve": 0.25, "c3": 0.264}, {"time": 0.3667, "x": 42.53, "y": 7.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 42.53, "y": -3.3}]}, "bone3": {"rotate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": 85.78}], "translate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "y": -42.11}]}, "bone4": {"translate": [{"x": 0.03, "y": 0.57}]}, "bone5": {"translate": [{"x": 1.86, "y": -0.06}]}, "bone6": {"rotate": [{"angle": 1.29, "curve": "stepped"}, {"time": 0.2667, "angle": 1.29, "curve": 0.349, "c2": 0.4, "c3": 0.684, "c4": 0.74}, {"time": 0.3667, "angle": -11.34, "curve": 0.365, "c2": 0.64, "c3": 0.701}, {"time": 0.5, "angle": 1.29}]}, "bone11": {"rotate": [{"angle": -0.16, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.2667, "angle": 8.05, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.68}, {"time": 0.5, "angle": -40.61}]}, "bone12": {"rotate": [{"angle": 3.29, "curve": 0.325, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.2667, "angle": -116.32, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.3667, "angle": -99.25, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.5, "angle": -154.64}]}, "bone13": {"rotate": [{"angle": 6.05, "curve": 0.341, "c2": 0.46, "c3": 0.675, "c4": 0.79}, {"time": 0.2667, "angle": -12.05, "curve": 0.336, "c2": 0.42, "c3": 0.67, "c4": 0.76}, {"time": 0.3667, "angle": 23.39, "curve": 0.337, "c2": 0.66, "c3": 0.671}, {"time": 0.5, "angle": 6.05}]}, "bone14": {"rotate": [{"angle": 2.45, "curve": "stepped"}, {"time": 0.2333, "angle": 2.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 32.13}]}, "bone15": {"rotate": [{"angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -93.96}]}, "bone16": {"rotate": [{"angle": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 90.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 71.17, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": 0.9}]}, "bone32": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone33": {"rotate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": 2.53}]}, "bone34": {"rotate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": -5.5}]}, "bone44": {"translate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "x": 42.53}]}, "bone45": {"rotate": [{"curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 0.0333, "angle": -5.29, "curve": 0.317, "c2": 0.23, "c3": 0.652, "c4": 0.57}, {"time": 0.0667, "angle": -4.98, "curve": 0.29, "c2": 0.19, "c3": 0.65, "c4": 0.61}, {"time": 0.2667, "angle": 92.84, "curve": 0.25, "c3": 0.218}, {"time": 0.4, "angle": 112.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 92.84}], "translate": [{"curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 0.0333, "x": 6.06, "y": 30.07, "curve": 0.317, "c2": 0.23, "c3": 0.652, "c4": 0.57}, {"time": 0.0667, "x": 19.54, "y": 59.68, "curve": 0.29, "c2": 0.19, "c3": 0.65, "c4": 0.61}, {"time": 0.2667, "x": 145.66, "y": 19.29, "curve": 0.25, "c3": 0.218}, {"time": 0.4, "x": 146.69, "y": 56.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 145.66, "y": 19.29}]}, "bone36": {"rotate": [{"angle": 0.56, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": 3.62}]}, "bone37": {"rotate": [{"angle": -1.01, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": -8.17}]}, "bone38": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone39": {"rotate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": 3.27}]}, "bone40": {"rotate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": -5.48}]}, "bone46": {"translate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "x": 42.53}]}, "bone47": {"rotate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": 90.9}], "translate": [{"curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 0.0333, "x": 1.6, "y": 30.41, "curve": 0.317, "c2": 0.23, "c3": 0.652, "c4": 0.57}, {"time": 0.0667, "x": 5.14, "y": 75.25, "curve": 0.29, "c2": 0.19, "c3": 0.65, "c4": 0.61}, {"time": 0.2667, "x": 35.12, "y": 70.29, "curve": 0.25, "c3": 0.309}, {"time": 0.3667, "x": 35.6, "y": 109.06, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 35.12, "y": 70.29}]}, "bone42": {"rotate": [{"angle": -0.42, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": 3.11}]}, "bone43": {"rotate": [{"angle": 1.77, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": -5.29}]}, "bone48": {"translate": [{"x": 3.79, "y": -0.3, "curve": "stepped"}, {"time": 0.2667, "x": 3.79, "y": -0.3, "curve": 0.25, "c3": 0.255}, {"time": 0.3667, "x": 3.6, "y": -34.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 3.79, "y": -0.3}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44, "curve": "stepped"}, {"time": 0.2667, "x": 4.06, "y": -0.44, "curve": 0.25, "c3": 0.245}, {"time": 0.3667, "x": 3.74, "y": -28.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 4.06, "y": -0.44}]}, "bone93": {"rotate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": -5.5}]}, "bone92": {"rotate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": 2.53}]}, "bone95": {"rotate": [{"angle": -1.01, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": -8.17}]}, "bone94": {"rotate": [{"angle": 0.56, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "angle": 3.62}]}, "bone53": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone97": {"rotate": [{"curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 0.0333, "angle": -5.29, "curve": 0.317, "c2": 0.23, "c3": 0.652, "c4": 0.57}, {"time": 0.0667, "angle": -4.98, "curve": 0.29, "c2": 0.19, "c3": 0.65, "c4": 0.61}, {"time": 0.2667, "angle": 92.84, "curve": 0.25, "c3": 0.218}, {"time": 0.4, "angle": 112.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 92.84}], "translate": [{"curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 0.0333, "x": 6.06, "y": 30.07, "curve": 0.317, "c2": 0.23, "c3": 0.652, "c4": 0.57}, {"time": 0.0667, "x": 19.54, "y": 59.68, "curve": 0.29, "c2": 0.19, "c3": 0.65, "c4": 0.61}, {"time": 0.2667, "x": 145.66, "y": 19.29, "curve": 0.25, "c3": 0.218}, {"time": 0.4, "x": 146.69, "y": 56.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 145.66, "y": 19.29}]}, "bone96": {"translate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.2667, "x": 42.53}]}, "bone17": {"rotate": [{"angle": -6.57, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 27.85, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 83.08, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 132.73, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.4667, "angle": 131.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": 130.84}]}, "bone67": {"rotate": [{"angle": 18.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 12.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 18.87}]}, "bone69": {"rotate": [{"angle": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 4.54, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.2, "angle": 46.22, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.3667, "angle": 119.99, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": 104.35}]}, "bone55": {"rotate": [{"angle": 9.18, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": -0.19, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": 9.18}]}, "bone65": {"rotate": [{"angle": -12.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -21.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -12.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3667, "angle": 14.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": -12.62}]}, "bone64": {"rotate": [{"angle": -12.77, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.1667, "angle": 6.06, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": 14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.77}]}, "bone19": {"rotate": [{"angle": 3.05, "curve": 0.326, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.0667, "angle": -4.83, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.1667, "angle": -17.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -36.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 21.74, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5333, "angle": 3.05}]}, "a4": {"rotate": [{"angle": -7.95, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.2, "angle": -86.56, "curve": 0.303, "c2": 0.21, "c3": 0.642, "c4": 0.56}, {"time": 0.5333, "angle": -42.18}]}, "a6": {"rotate": [{"angle": 11.87, "curve": 0.312, "c2": 0.27, "c3": 0.664, "c4": 0.66}, {"time": 0.5333, "angle": 1.04}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone56": {"rotate": [{"angle": 14.59, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 17.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1333, "angle": 9.8, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -13, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5333, "angle": 14.59}]}, "bone66": {"rotate": [{"angle": 0.6, "curve": 0.335, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.0667, "angle": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 6.13, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5333, "angle": 0.6}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone68": {"rotate": [{"angle": 13.53, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.01, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 13.53}]}, "bone71": {"rotate": [{"angle": -4.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.54, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "angle": 55.63, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5333, "angle": -4.46}], "translate": [{"x": 9.74, "y": -5.24, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "x": 14.68, "y": -6.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -12.34, "y": -0.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "x": 9.74, "y": -5.24}]}, "a7": {"rotate": [{"angle": 28.89}]}, "bone63": {"rotate": [{"angle": 16.24, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 18.55, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1333, "angle": 11.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -9.27, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5333, "angle": 16.24}]}, "bone18": {"rotate": [{"angle": -8.95, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.0667, "angle": -14.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -8.83, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2667, "angle": -30.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3667, "angle": 29.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": -0.02}]}, "bone70": {"rotate": [{"angle": -8.28, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": -5.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 29.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 49.04}], "translate": [{"x": 1.54, "y": -12.11, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "x": -1.34, "y": -9.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "x": -12.11, "y": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -14.65, "y": 5.61}]}, "bone59": {"rotate": [{"angle": -7.48, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": -1.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -10.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": -7.48}]}, "bone20": {"rotate": [{"angle": 12.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "angle": 7.74, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "angle": -9.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -22.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "angle": 33.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "angle": 12.18}]}, "bone57": {"rotate": [{"angle": -1.37, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.1333, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -25.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -1.37}]}, "a5": {"rotate": [{"angle": -5.87, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.2, "angle": -54.74, "curve": 0.369, "c2": 0.5, "c3": 0.71, "c4": 0.88}, {"time": 0.4333, "angle": -13.71}]}, "bone21": {"rotate": [{"angle": 12.38, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 8.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 42.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 32.09, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": 12.38}]}, "bone62": {"rotate": [{"angle": 8.16, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": -3.17, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": 8.16}]}, "a8": {"rotate": [{"angle": 12.93}]}, "bone22": {"rotate": [{"angle": 11.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": 12.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 7.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -6.1, "curve": 0.249, "c3": 0.626, "c4": 0.51}, {"time": 0.4333, "angle": 22.96, "curve": 0.36, "c2": 0.43, "c3": 0.717, "c4": 0.85}, {"time": 0.5333, "angle": 11.68}]}, "bone61": {"rotate": [{"angle": -4.18, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.1, "angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 16.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": -4.18}]}, "a3": {"rotate": [{"angle": -4.24, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.5333, "angle": 30.47}]}, "bone60": {"rotate": [{"angle": -16.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": -10.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": 6.04, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5333, "angle": -15.96}]}, "bone23": {"rotate": [{"angle": 3.6, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.1, "angle": 16.43, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1333, "angle": 16.96, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.2667, "angle": -17.68, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.4333, "angle": 32.79, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.5333, "angle": 3.6}]}, "bone24": {"rotate": [{}, {"time": 0.2, "angle": -29.09}, {"time": 0.3667, "angle": 19.16}, {"time": 0.5333, "angle": 81.34}], "translate": [{}, {"time": 0.3667, "x": -11.9, "y": 22.5}, {"time": 0.5333, "x": -41.17, "y": 15.27}]}}, "drawOrder": [{"time": 0.2667, "offsets": [{"slot": "a0", "offset": -16}]}]}, "hurt": {"slots": {"a4": {"attachment": [{"time": 0.0667, "name": null}]}, "a5": {"color": [{"time": 0.0667, "color": "ffffffff"}]}}, "bones": {"bone2": {"translate": [{"y": -3.3}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -5.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"x": 0.03, "y": 0.57}]}, "bone5": {"rotate": [{"angle": 2.29, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.09, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 15.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 2.29}], "translate": [{"x": 1.86, "y": -0.06}]}, "bone6": {"rotate": [{"angle": 6.27, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 1.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 7.01, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 6.27}]}, "bone14": {"rotate": [{"angle": 17.39, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 11.12, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "angle": 28.17, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 17.39}]}, "bone15": {"rotate": [{"angle": 21.73, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 38.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 21.73}]}, "bone16": {"rotate": [{"angle": 34.33, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 51.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 34.33}]}, "bone32": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone36": {"rotate": [{"angle": 3.07}]}, "bone37": {"rotate": [{"angle": -8.17}]}, "bone38": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone42": {"rotate": [{"angle": 3.52}]}, "bone43": {"rotate": [{"angle": -5.29}]}, "bone48": {"translate": [{"x": 3.79, "y": -0.3}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44}]}, "bone95": {"rotate": [{"angle": -4.3}]}, "bone94": {"rotate": [{"angle": 1.2}]}, "bone53": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone11": {"rotate": [{"angle": 7.08, "curve": 0.327, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 15.14, "curve": 0.318, "c2": 0.27, "c3": 0.653, "c4": 0.61}, {"time": 0.3333, "angle": 7.08}]}, "bone12": {"rotate": [{"angle": -23.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -26.7, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -23.84}]}, "bone13": {"rotate": [{"angle": -5.94, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 8.4, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.3333, "angle": -5.94}]}, "a4": {"rotate": [{"angle": -7.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 24.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2667, "angle": 4.12, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -7.75}]}, "a5": {"rotate": [{"angle": 6.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -5.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 26.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2667, "angle": 22.21, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3333, "angle": 6.15}]}, "a6": {"rotate": [{"angle": 39.95, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 11.87, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": 39.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": 44.15, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 39.95}]}, "a7": {"rotate": [{"angle": 28.89}]}, "a8": {"rotate": [{"angle": 12.93}]}, "a3": {"rotate": [{"angle": -4.24}]}, "bone33": {"rotate": [{"angle": 2.53}]}, "bone34": {"rotate": [{"angle": -5.5}]}, "bone39": {"rotate": [{"angle": 3.27}]}, "bone40": {"rotate": [{"angle": -5.48}]}, "bone92": {"rotate": [{"angle": 1.99}]}, "bone93": {"rotate": [{"angle": -5.49}]}, "bone63": {"rotate": [{"angle": 16.24}]}, "bone57": {"rotate": [{"angle": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.37}]}, "bone17": {"rotate": [{"angle": -17.27, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -21.98, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 0.2, "angle": 14.22, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -17.27}]}, "bone22": {"rotate": [{"angle": 33.88, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 11.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 37.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 33.88}]}, "bone65": {"rotate": [{"angle": -12.62}]}, "bone60": {"rotate": [{"angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.34}]}, "bone69": {"rotate": [{"angle": 1.42}]}, "bone56": {"rotate": [{"angle": 14.59, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 38.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 14.59}]}, "bone55": {"rotate": [{"angle": 9.18, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 33.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.18}]}, "bone62": {"rotate": [{"angle": 8.16}]}, "bone19": {"rotate": [{"angle": 23.95, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 23.95}]}, "bone66": {"rotate": [{"angle": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.6}]}, "bone21": {"rotate": [{"angle": 21.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 12.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 37.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 21.76}]}, "bone64": {"rotate": [{"angle": -12.77}]}, "bone18": {"rotate": [{"angle": -0.64, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -24.36, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": 13.16, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -0.64}]}, "bone59": {"rotate": [{"angle": -7.48, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 16.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.48}]}, "bone70": {"rotate": [{"angle": -19.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -23.69, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.2, "angle": 11.62, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -19.1}], "translate": [{"x": 1.54, "y": -12.11}]}, "bone61": {"rotate": [{"angle": -4.18, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.18}]}, "bone20": {"rotate": [{"angle": 13.49, "curve": 0.312, "c2": 0.12, "c3": 0.646, "c4": 0.46}, {"time": 0.1333, "angle": 38.52, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.3, "angle": 12.18, "curve": 0.327, "c3": 0.66, "c4": 0.34}, {"time": 0.3333, "angle": 13.49}]}, "bone71": {"rotate": [{"angle": 4.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -19.87, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 0.2667, "angle": 18.26, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 4.23}], "translate": [{"x": 9.74, "y": -5.24}]}, "bone67": {"rotate": [{"angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 42.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 18.87}]}, "bone68": {"rotate": [{"angle": 13.53}]}, "bone23": {"rotate": [{"angle": 25.8, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 29.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.6, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 25.8}]}, "bone3": {"rotate": [{"curve": 0.58, "c3": 0.585, "c4": 0.99}, {"time": 0.0667, "angle": -5.89, "curve": 0.58, "c3": 0.585, "c4": 0.99}, {"time": 0.1667, "angle": 13.07, "curve": 0.58, "c3": 0.585, "c4": 0.99}, {"time": 0.3333}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 29.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333}]}}}, "run1": {"slots": {"a2611": {"color": [{"color": "ffffffff"}]}, "a1411": {"color": [{"color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{"angle": -12.92}], "translate": [{"x": -21.18, "y": -13.22, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -21.18, "y": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -21.18, "y": -13.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -21.18, "y": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -21.18, "y": -13.22}]}, "bone4": {"rotate": [{"angle": 6.3}], "translate": [{"x": 0.03, "y": 0.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.16, "y": 3.1, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 0.03, "y": 0.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.16, "y": 3.1, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "x": 0.03, "y": 0.57}]}, "bone5": {"rotate": [{"angle": -9.17}], "translate": [{"x": 1.86, "y": -0.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 3.72, "y": -0.11, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 1.86, "y": -0.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 3.72, "y": -0.11, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "x": 1.86, "y": -0.06}]}, "bone6": {"rotate": [{"angle": 1.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 1.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 1.29}]}, "bone14": {"rotate": [{"angle": -4.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 25.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.11}], "translate": [{"x": -11.96, "y": -1.76}]}, "bone15": {"rotate": [{"angle": 1.22, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": 4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -0.61, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": 1.22}]}, "bone16": {"rotate": [{"angle": -1.98, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2333, "angle": 10.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.06, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5333, "angle": -1.98}]}, "bone32": {"translate": [{"x": -20.15, "y": 49.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 4.3, "y": 22.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -20.15, "y": 49.3}]}, "bone44": {"translate": [{"x": 109.06, "y": 0.61, "curve": 0.25, "c3": 0.606}, {"time": 0.2667, "x": -71.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 109.06, "y": 0.61}]}, "bone45": {"rotate": [{"angle": 39.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": 1.04, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "angle": 0.42, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2667, "angle": -83.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -76.85, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "angle": 39.34}], "translate": [{"x": -6.1, "y": 25.88, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": 0.16, "y": 5.92, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "x": 14.9, "y": 3.87, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2667, "x": 1.31, "y": 47.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": -30.9, "y": 41.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": -6.1, "y": 25.88}]}, "bone36": {"rotate": [{"angle": 0.56}]}, "bone37": {"rotate": [{"angle": -1.01}]}, "bone38": {"translate": [{"x": -4.26, "y": -35.19, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -2.74, "y": 12.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -4.26, "y": -35.19}]}, "bone46": {"translate": [{"x": -112.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 22.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -112.85}]}, "bone47": {"rotate": [{"angle": -104.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -115.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 34.33, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -0.24, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4, "angle": 0.86, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4667, "angle": -75.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "angle": -104.25}], "translate": [{"x": -19.75, "y": 38.31, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": -38.15, "y": 38.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 16.29, "y": 28.61, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": 13, "y": -0.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4, "x": 8.02, "y": 0.03, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4667, "x": -30.28, "y": 24.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "x": -19.75, "y": 38.31}]}, "bone42": {"rotate": [{"angle": -0.42}]}, "bone43": {"rotate": [{"angle": 1.77}]}, "bone48": {"translate": [{"x": 3.79, "y": -0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 36.32, "y": -42.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 3.79, "y": -0.3}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 15.35, "y": -24.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 4.06, "y": -0.44}]}, "bone11": {"rotate": [{"angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -17.09, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.46}], "translate": [{"x": 20.96, "y": 6.6}]}, "bone12": {"rotate": [{"angle": -41.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -39.82, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.3333, "angle": -59.84, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -41.69}]}, "bone13": {"rotate": [{"angle": 1.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 10.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -7.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 1.37}]}, "bone95": {"rotate": [{"angle": -1.01}]}, "bone94": {"rotate": [{"angle": 0.56}]}, "bone53": {"translate": [{"x": -13.17, "y": 36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 11.27, "y": 9.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 6.97, "y": -13.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -13.17, "y": 36}]}, "bone97": {"rotate": [{"angle": 39.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": 1.04, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "angle": 0.42, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2667, "angle": -83.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -76.85, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "angle": 39.34}], "translate": [{"x": -6.1, "y": 25.88, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": 0.16, "y": 5.92, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "x": 14.9, "y": 3.87, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2667, "x": 1.31, "y": 47.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": -30.9, "y": 41.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": -6.1, "y": 25.88}]}, "bone96": {"translate": [{"x": 94.59, "y": -3.38, "curve": 0.25, "c3": 0.606}, {"time": 0.2667, "x": -86.07, "y": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 94.59, "y": -3.38}]}, "bone67": {"rotate": [{"angle": 18.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 12.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 20.08, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5333, "angle": 18.87}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone21": {"rotate": [{"angle": 15.1, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.0667, "angle": 27.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": 39.95, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 0.4333, "angle": 2.47, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5333, "angle": 15.1}]}, "a8": {"rotate": [{"angle": -19.07, "curve": 0.283, "c2": 0.16, "c3": 0.647, "c4": 0.6}, {"time": 0.1333, "angle": 12.93, "curve": 0.362, "c2": 0.44, "c3": 0.715, "c4": 0.84}, {"time": 0.2333, "angle": 36.5, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.2667, "angle": 39.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -22.06, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.5333, "angle": -19.07}]}, "bone63": {"rotate": [{"angle": 16.24, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 18.55, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1333, "angle": 11.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -9.27, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5333, "angle": 16.24}]}, "bone62": {"rotate": [{"angle": 8.16, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": -3.17, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": -9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": 8.16}]}, "bone17": {"rotate": [{"angle": -8.44, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "angle": -9.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -6.57, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "angle": 1.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": 5.95, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5333, "angle": -8.44}]}, "bone19": {"rotate": [{"angle": 12.18, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": 3.05, "curve": 0.326, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.1667, "angle": -4.83, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.2667, "angle": -17.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 12.18}]}, "a4": {"rotate": [{"angle": -1.97, "curve": 0.364, "c2": 0.45, "c3": 0.734, "c4": 0.91}, {"time": 0.1333, "angle": -7.95, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": -5.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": 3.5, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.5333, "angle": -1.97}]}, "bone22": {"rotate": [{"angle": -15.83, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": -5.67, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 39.25, "curve": 0.346, "c2": 0.43, "c3": 0.679, "c4": 0.77}, {"time": 0.5333, "angle": -15.83}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone61": {"rotate": [{"angle": -4.18, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.1, "angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 16.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": -4.18}]}, "bone60": {"rotate": [{"angle": -16.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": -10.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": 6.04, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5333, "angle": -15.96}]}, "bone66": {"rotate": [{"angle": 0.6, "curve": 0.335, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.0667, "angle": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.13, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5333, "angle": 0.6}]}, "bone69": {"rotate": [{"angle": -3.69, "curve": 0.314, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.7, "curve": 0.274, "c3": 0.62, "c4": 0.4}, {"time": 0.5333, "angle": -3.69}]}, "a5": {"rotate": [{"angle": 7.89, "curve": 0.284, "c2": 0.17, "c3": 0.653, "c4": 0.62}, {"time": 0.1333, "angle": -5.87, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.2333, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.25, "curve": 0.3, "c3": 0.636, "c4": 0.36}, {"time": 0.5333, "angle": 7.89}]}, "bone57": {"rotate": [{"angle": -1.37, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.1333, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -25.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -1.37}]}, "bone68": {"rotate": [{"angle": 13.53, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.01, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 13.53}]}, "a3": {"rotate": [{"angle": -5.57, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -6.19, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1333, "angle": -4.24, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": -0.61, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": 1.84, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5333, "angle": -5.57}]}, "bone64": {"rotate": [{"angle": -12.77, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.1667, "angle": 6.06, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": 14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -13.09, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.5333, "angle": -12.77}]}, "bone18": {"rotate": [{"angle": 2.02, "curve": 0.328, "c2": 0.32, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "angle": -8.95, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.1667, "angle": -14.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4333, "angle": 9.47, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5333, "angle": 2.02}]}, "bone55": {"rotate": [{"angle": 9.18, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": -0.19, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": 9.18}]}, "bone20": {"rotate": [{"angle": 0.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "angle": 12.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": 7.74, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.2667, "angle": -9.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3667, "angle": -22.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": 0.68}]}, "bone70": {"rotate": [{"angle": -6.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -8.81, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": -8.28, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1667, "angle": -5.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 4.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -6.35}], "translate": [{"x": -0.52, "y": -9.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "x": 2.1, "y": -12.71, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "x": 1.54, "y": -12.11, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1667, "x": -1.34, "y": -9.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "x": -12.11, "y": 2.39, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "x": -0.52, "y": -9.92}]}, "bone56": {"rotate": [{"angle": 14.59, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 17.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1333, "angle": 9.8, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -13, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5333, "angle": 14.59}]}, "a6": {"rotate": [{"angle": 11.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 19.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "angle": 11.87, "curve": 0.312, "c2": 0.27, "c3": 0.664, "c4": 0.66}, {"time": 0.2333, "angle": -6.09, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -20.84, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 11.86}]}, "bone71": {"rotate": [{"angle": 0.8, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.1, "angle": -4.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1667, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.54, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5333, "angle": 0.8}], "translate": [{"x": -3.16, "y": -2.41, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.1, "x": 9.74, "y": -5.24, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1667, "x": 14.68, "y": -6.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -12.34, "y": -0.4, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5333, "x": -3.16, "y": -2.41}]}, "bone59": {"rotate": [{"angle": -7.48, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": -1.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -10.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": -7.48}]}, "a7": {"rotate": [{"angle": -3.2, "curve": 0.347, "c2": 0.38, "c3": 0.722, "c4": 0.86}, {"time": 0.1333, "angle": 28.89, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": 31.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2333, "angle": 21.33, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4333, "angle": -22.06, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.5333, "angle": -3.2}]}, "bone65": {"rotate": [{"angle": -12.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -21.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -12.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 14.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": -12.62}]}, "bone23": {"rotate": [{"angle": -5.67, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.0667, "angle": -20.45, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1, "angle": -23.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 31.18, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": -5.67}]}}, "deform": {"default": {"a9111111": {"a9": [{"offset": 48, "vertices": [-3.62207, 1.03696, -5.9024, 0.70586, -2.77, 6.57912, -1.63907, 4.54085, -3.79312, 2.98648, 0.59341, 1.15033, -0.10496, 1.29016], "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "offset": 40, "vertices": [-3.22356, 1.49243, -3.27158, -1.38399, -3.03331, -1.84862, -2.24802, -2.75066, -3.32761, 7.8023, -5.33673, 11.89333, -3.85905, 16.30716, -0.51491, 6.01367, -4.79898, 3.66039, -7.03311, 3.28296, -7.15769, -3.00159], "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "offset": 48, "vertices": [-3.62207, 1.03696, -5.9024, 0.70586, -2.77, 6.57912, -1.63907, 4.54085, -3.79312, 2.98648, 0.59341, 1.15033, -0.10496, 1.29016]}]}, "a11111111": {"a11": [{"vertices": [9.86137, 4.49936, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.45657, 2.00677, 11.58209, 4.96251]}]}, "a18": {"a18": [{"offset": 4, "vertices": [-1.20045, 2.64307, 2.85832, -0.50675]}]}, "a13": {"a13": [{"offset": 82, "vertices": [-0.12559, 7.58631, 4.27103, 32.90507, -8.45513, -32.08572, -14.14311, -30.01593, 5.62802, 12.6432, -7.20267, -11.81711, -9.22911, -10.31208, -9.22958, -10.31206, -5.37067, 24.71931, 2.15666, -25.20391, -2.45778, -25.17633, -2.45764, -25.17635, -2.4577, -25.17633, -7.83529, 32.55878, 3.5957, -33.29468, -2.51244, -33.3939, -2.51225, -33.39392, -2.51236, -33.39389, -7.77246, 28.7656, 4.01978, -29.52476, -1.41058, -29.76374, -1.4104, -29.76376, -1.41049, -29.76374, -5.37067, 24.71931, 2.15666, -25.20391, -2.45778, -25.17633, -2.45764, -25.17635, -2.4577, -25.17633, 2.15666, -25.20391, -2.45778, -25.17633, -2.45764, -25.17635, -2.4577, -25.17633, 2.15666, -25.20391, -2.45778, -25.17633, -2.45764, -25.17635, -2.4577, -25.17633, -13.60693, -18.69961, -13.60677, -18.69965, -13.60686, -18.69961, -12.1313, -10.6786, -12.19125, -10.61015, -12.13127, -10.67859, -12.13129, -10.67859, -10.22922, -6.14709, -10.26353, -6.0894, -10.22923, -6.14707, -10.22923, -6.14708, -9.06773, -4.58408, -9.09329, -4.53296, -9.06783, -4.58405, -9.06786, -4.58406, -5.83633, -1.32408, -5.84369, -1.2912, -5.83635, -1.32407, -5.83635, -1.32407, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.48252, 0.12685, 1.4808, -0.14456, 1.48093, -0.14456, 1.31205, -6.60822, 0.08972, -6.73661, 0.08981, -6.73661, 0.44393, -5.18905, -0.50615, -5.18335, -0.50606, -5.18335, 0.44393, -5.18905, -0.50615, -5.18335, -0.50606, -5.18335], "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "offset": 84, "vertices": [2.60629, -0.28205, -2.54861, 0.61394, -2.39464, 1.0667, 3.66238, -5.1426, -2.9727, 5.56979, -1.9115, 6.01713, -1.91135, 6.01713, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.28802, 10.53131, 0.91877, -10.73775, -1.0472, -10.72599, -1.0472, -10.726, -1.04694, -10.726, 0.05431, -13.88123, -2.06453, -13.8234, -2.06447, -13.82341, -2.06424, -13.8234, -4.048, -15.68347, -6.25017, -15.42274, -6.25009, -15.42274, -6.24998, -15.42273, -15.24434, -10.68952, -15.24434, -10.68954, -15.24413, -10.68953, -14.03664, -4.59772, -14.06233, -4.51865, -14.03664, -4.59771, -14.03641, -4.59772, -10.22922, -6.14709, -10.26353, -6.0894, -10.22923, -6.14707, -10.22923, -6.14708, -9.06773, -4.58408, -9.09329, -4.53296, -9.06783, -4.58405, -9.06786, -4.58406, -5.83633, -1.32408, -5.84369, -1.2912, -5.83635, -1.32407, -5.83635, -1.32407], "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "offset": 82, "vertices": [-0.12559, 7.58631, 4.27103, 32.90507, -8.45513, -32.08572, -14.14311, -30.01593, 5.62802, 12.6432, -7.20267, -11.81711, -9.22911, -10.31208, -9.22958, -10.31206, -5.37067, 24.71931, 2.15666, -25.20391, -2.45778, -25.17633, -2.45764, -25.17635, -2.4577, -25.17633, -7.83529, 32.55878, 3.5957, -33.29468, -2.51244, -33.3939, -2.51225, -33.39392, -2.51236, -33.39389, -7.77246, 28.7656, 4.01978, -29.52476, -1.41058, -29.76374, -1.4104, -29.76376, -1.41049, -29.76374, -5.37067, 24.71931, 2.15666, -25.20391, -2.45778, -25.17633, -2.45764, -25.17635, -2.4577, -25.17633, 2.15666, -25.20391, -2.45778, -25.17633, -2.45764, -25.17635, -2.4577, -25.17633, 2.15666, -25.20391, -2.45778, -25.17633, -2.45764, -25.17635, -2.4577, -25.17633, -13.60693, -18.69961, -13.60677, -18.69965, -13.60686, -18.69961, -12.1313, -10.6786, -12.19125, -10.61015, -12.13127, -10.67859, -12.13129, -10.67859, -10.22922, -6.14709, -10.26353, -6.0894, -10.22923, -6.14707, -10.22923, -6.14708, -9.06773, -4.58408, -9.09329, -4.53296, -9.06783, -4.58405, -9.06786, -4.58406, -5.83633, -1.32408, -5.84369, -1.2912, -5.83635, -1.32407, -5.83635, -1.32407, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.48252, 0.12685, 1.4808, -0.14456, 1.48093, -0.14456, 1.31205, -6.60822, 0.08972, -6.73661, 0.08981, -6.73661, 0.44393, -5.18905, -0.50615, -5.18335, -0.50606, -5.18335, 0.44393, -5.18905, -0.50615, -5.18335, -0.50606, -5.18335]}]}}}, "drawOrder": [{"offsets": [{"slot": "a0", "offset": -13}]}]}, "run2": {"slots": {"a23": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}, "a21": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}, "a19": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}, "a24": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}, "a25": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}, "a20": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}, "a22": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}}, "bones": {"bone2": {"translate": [{"y": -3.3, "curve": 0.25, "c3": 0.345}, {"time": 0.1, "y": -17.66, "curve": 0.25, "c3": 0.309}, {"time": 0.2333, "y": 146.69, "curve": 0.782, "c3": 0.75}, {"time": 0.5, "y": -3.3}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.345}, {"time": 0.1, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 7.64}], "translate": [{"x": 0.03, "y": 0.57}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.345}, {"time": 0.1, "angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "translate": [{"x": 1.86, "y": -0.06, "curve": 0.25, "c3": 0.345}, {"time": 0.1, "x": -8.28, "y": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.86, "y": -0.06}]}, "bone6": {"rotate": [{"angle": 1.29, "curve": 0.25, "c3": 0.345}, {"time": 0.1, "angle": -10.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.29}], "translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 8.18, "y": -7.48}]}, "bone14": {"rotate": [{"angle": 2.45, "curve": 0.25, "c3": 0.345}, {"time": 0.2333, "angle": 66.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 45.55}], "translate": [{"curve": 0.25, "c3": 0.345}, {"time": 0.2333, "x": 20.76, "y": 18.95, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone15": {"rotate": [{"angle": 1.22, "curve": "stepped"}, {"time": 0.2333, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -45.1}]}, "bone16": {"rotate": [{"angle": -1.98, "curve": 0.25, "c3": 0.345}, {"time": 0.2333, "angle": 34.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -79.95}]}, "bone32": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.345}, {"time": 0.2333, "x": -11.33, "y": -1.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.97, "y": 0.01}]}, "bone36": {"rotate": [{"angle": 3.07}]}, "bone37": {"rotate": [{"angle": -8.17}]}, "bone38": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone42": {"rotate": [{"angle": 3.52}]}, "bone43": {"rotate": [{"angle": -5.29}]}, "bone48": {"translate": [{"x": 3.79, "y": -0.3, "curve": 0.25, "c3": 0.345}, {"time": 0.1, "x": -7.44, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 3.79, "y": -0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.29, "y": -76.85}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44, "curve": 0.25, "c3": 0.345}, {"time": 0.1, "x": -7.17, "y": 0.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 4.06, "y": -0.44}]}, "bone95": {"rotate": [{"angle": -4.3}]}, "bone94": {"rotate": [{"angle": 1.2}]}, "bone53": {"translate": [{"x": -0.97, "y": 0.01}]}, "bone11": {"rotate": [{"angle": 0.8, "curve": 0.367, "c3": 0.636, "c4": 0.47}, {"time": 0.1, "angle": -8.79, "curve": 0.2, "c2": 0.46, "c3": 0.471}, {"time": 0.2333, "angle": 8.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.8}]}, "bone12": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.345}, {"time": 0.2333, "angle": -28.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.77}]}, "bone13": {"rotate": [{"angle": -5.94, "curve": "stepped"}, {"time": 0.2333, "angle": -5.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.05}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone55": {"rotate": [{"angle": 9.18, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -0.19, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": 9.18}]}, "bone56": {"rotate": [{"angle": 14.59, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 17.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 9.8, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -13, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5, "angle": 14.59}]}, "bone57": {"rotate": [{"angle": -1.37, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.1, "angle": 21.37, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1333, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -25.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -1.37}]}, "bone62": {"rotate": [{"angle": 8.16, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -3.17, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": -9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": 8.16}]}, "bone63": {"rotate": [{"angle": 16.24, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 18.55, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 11.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -9.27, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5, "angle": 16.24}]}, "bone64": {"rotate": [{"angle": -12.77, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.1667, "angle": 6.06, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.77}]}, "bone65": {"rotate": [{"angle": -12.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -21.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -12.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 14.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -12.62}]}, "bone59": {"rotate": [{"angle": -7.48, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": -1.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -10.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": -7.48}]}, "bone60": {"rotate": [{"angle": -16.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": -10.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": 6.04, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5, "angle": -15.96}]}, "bone61": {"rotate": [{"angle": -4.18, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.1, "angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 16.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": -4.18}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone66": {"rotate": [{"angle": 0.6, "curve": 0.335, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.0667, "angle": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.13, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "angle": 0.6}]}, "bone67": {"rotate": [{"angle": 18.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 12.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 20.08, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 18.87}]}, "bone68": {"rotate": [{"angle": 13.53, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.01, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 13.53}]}, "bone17": {"rotate": [{"angle": -6.57, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 1.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 36.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -6.57}]}, "bone18": {"rotate": [{"angle": -8.95, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.0667, "angle": -14.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 9.47, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.4333, "angle": -34.34, "curve": 0.335, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": -8.95}]}, "bone19": {"rotate": [{"angle": 3.05, "curve": 0.326, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.0667, "angle": -4.83, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.1667, "angle": -17.72, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.3, "angle": 18.04, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.5, "angle": 3.05}]}, "bone20": {"rotate": [{"angle": 12.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "angle": 7.74, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "angle": -9.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -22.05, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.3, "angle": 22.82, "curve": 0.293, "c2": 0.2, "c3": 0.707, "c4": 0.8}, {"time": 0.5, "angle": 12.18}]}, "a4": {"rotate": [{"angle": -7.95, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": -5.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": 3.5, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 0.3667, "angle": -34.45, "curve": 0.356, "c2": 0.42, "c3": 0.737, "c4": 0.92}, {"time": 0.5, "angle": -7.75}]}, "a5": {"rotate": [{"angle": -5.87, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.1, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 38.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -24.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -5.87}]}, "a6": {"rotate": [{"angle": 11.87, "curve": 0.312, "c2": 0.27, "c3": 0.664, "c4": 0.66}, {"time": 0.1, "angle": -6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -20.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.87}]}, "a7": {"rotate": [{"angle": 28.89, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": 31.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 21.33, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -22.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 18.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -1.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 28.89}]}, "a8": {"rotate": [{"angle": 12.93, "curve": 0.362, "c2": 0.44, "c3": 0.715, "c4": 0.84}, {"time": 0.1, "angle": 39.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -14.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 12.93}]}, "a3": {"rotate": [{"angle": -4.24, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": -0.61, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 1.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.19, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": -4.24}]}, "bone69": {"rotate": [{"angle": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.52, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": 1.42}]}, "bone70": {"rotate": [{"angle": -8.28, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": -5.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 60.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 20.59, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": -8.28}], "translate": [{"x": 1.54, "y": -12.11, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "x": -1.34, "y": -9.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "x": -12.11, "y": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 2.1, "y": -12.71, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "x": 1.54, "y": -12.11}]}, "bone71": {"rotate": [{"angle": -4.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -4.46}], "translate": [{"x": 9.74, "y": -5.24, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "x": 14.68, "y": -6.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -12.34, "y": -0.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "x": 9.74, "y": -5.24}]}, "bone21": {"rotate": [{"angle": 12.38, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": 8.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 5.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 60.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 12.38}]}, "bone22": {"rotate": [{"angle": 11.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": 12.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 7.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -22.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 40.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.68}]}, "bone23": {"rotate": [{"angle": 3.6, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.1, "angle": 16.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -20.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -14.02, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 25.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.6}]}, "j2": {"rotate": [{"angle": -63.88}, {"time": 0.0333, "angle": -117.97}, {"time": 0.1, "angle": -112.72}, {"time": 0.1667, "angle": -1.12}, {"time": 0.2333, "angle": -9.8}, {"time": 0.2667, "angle": -63.88}, {"time": 0.3, "angle": -117.97}, {"time": 0.3667, "angle": -112.72}, {"time": 0.4333, "angle": -1.12}, {"time": 0.5, "angle": -63.88}], "translate": [{"x": -9.14, "y": -22.51}, {"time": 0.0333, "x": -22.28, "y": -32.01}, {"time": 0.1, "x": -12.48, "y": -6.47}, {"time": 0.1667, "x": 0.66, "y": 27.94}, {"time": 0.2333, "x": 4, "y": -13}, {"time": 0.2667, "x": -9.14, "y": -22.51}, {"time": 0.3, "x": -22.28, "y": -32.01}, {"time": 0.3667, "x": -12.48, "y": -6.47}, {"time": 0.4333, "x": 0.66, "y": 27.94}, {"time": 0.5, "x": -9.14, "y": -22.51}]}, "bone9": {"rotate": [{}, {"time": 0.1333, "angle": 24.13}, {"time": 0.2667}, {"time": 0.4, "angle": 24.13}, {"time": 0.5}], "translate": [{}, {"time": 0.1333, "y": -7.36}, {"time": 0.2667}, {"time": 0.4, "y": -7.36}, {"time": 0.5}]}, "zj": {"translate": [{"curve": 0.25, "c3": 0.345}, {"time": 0.2333, "x": -8.24, "y": -70.68, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 76.93}]}, "bone40": {"rotate": [{"angle": -5.48}]}, "bone93": {"rotate": [{"angle": -5.49}]}, "bone3": {"translate": [{"curve": 0.25, "c3": 0.345}, {"time": 0.1, "x": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone10": {"rotate": [{"angle": -3.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 2.31, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -3.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 12.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -3.85}]}, "bone79": {"rotate": [{"angle": 9.69}, {"time": 0.0333}, {"time": 0.1667, "angle": 56.36}, {"time": 0.2667, "angle": 9.69}, {"time": 0.3}, {"time": 0.4333, "angle": 56.36}, {"time": 0.5, "angle": 14.09}]}, "bone84": {"rotate": [{"angle": 61.62}, {"time": 0.1333, "angle": 4.35}, {"time": 0.2667, "angle": 61.62}, {"time": 0.4, "angle": 4.35}]}, "j4": {"rotate": [{"angle": 33.51}, {"time": 0.1333, "angle": 3.85}, {"time": 0.2, "angle": -103.54}, {"time": 0.2667, "angle": 33.51}, {"time": 0.4, "angle": 3.85}, {"time": 0.4667, "angle": -103.54}, {"time": 0.5, "angle": 33.51}], "translate": [{"x": -11.85, "y": 23.77}, {"time": 0.0667, "x": 10.06, "y": 6.59}, {"time": 0.1333, "x": 5.76, "y": -43.21}, {"time": 0.2, "x": -21.21, "y": -63.73}, {"time": 0.2667, "x": -11.85, "y": 23.77}, {"time": 0.3333, "x": 10.06, "y": 6.59}, {"time": 0.4, "x": 5.76, "y": -43.21}, {"time": 0.4667, "x": -21.21, "y": -63.73}, {"time": 0.5, "x": -11.85, "y": 23.77}]}, "bone26": {"rotate": [{"angle": 38.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 48.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 20.55, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": 38.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "angle": 48.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 38.28}]}, "j1": {"rotate": [{"angle": -69.46}, {"time": 0.0333, "angle": -125.2}, {"time": 0.1, "angle": -112.57}, {"time": 0.1667, "angle": -0.92}, {"time": 0.2333, "angle": -13.72}, {"time": 0.2667, "angle": -69.46}, {"time": 0.3, "angle": -125.2}, {"time": 0.3667, "angle": -112.57}, {"time": 0.4333, "angle": -0.92}, {"time": 0.5, "angle": -69.46}], "translate": [{"x": -9.02, "y": -14.53}, {"time": 0.0333, "x": -19.11, "y": -21.79}, {"time": 0.1, "x": -6.69, "y": 23.23}, {"time": 0.1667, "x": 1.52, "y": 51.06}, {"time": 0.2333, "x": 1.07, "y": -7.27}, {"time": 0.2667, "x": -9.02, "y": -14.53}, {"time": 0.3, "x": -19.11, "y": -21.79}, {"time": 0.3667, "x": -6.69, "y": 23.23}, {"time": 0.4333, "x": 1.52, "y": 51.06}, {"time": 0.5, "x": -9.02, "y": -14.53}]}, "bone52": {"rotate": [{"angle": 40.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 50, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 40.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "angle": 50, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 40.77}]}, "bone46": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.309}, {"time": 0.2333, "angle": 49.54, "curve": 0.782, "c3": 0.75}, {"time": 0.5}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.309}, {"time": 0.2333, "x": 78.63, "y": 188.6, "curve": 0.782, "c3": 0.75}, {"time": 0.5}]}, "bone78": {"rotate": [{"angle": -34.28}, {"time": 0.0333}, {"time": 0.1667, "angle": -53.76}, {"time": 0.2667, "angle": -34.28}, {"time": 0.3}, {"time": 0.4333, "angle": -53.76}, {"time": 0.5, "angle": -13.44}], "translate": [{"x": -5.34, "y": -0.86}, {"time": 0.0333, "x": -6.61}, {"time": 0.1667, "x": -1.54, "y": -3.45}, {"time": 0.2667, "x": -5.34, "y": -0.86}, {"time": 0.3, "x": -6.61}, {"time": 0.4333, "x": -1.54, "y": -3.45}, {"time": 0.5, "x": -5.34, "y": -0.86}]}, "bone87": {"rotate": [{"angle": -3.99}, {"time": 0.1333}, {"time": 0.2667, "angle": -3.99}, {"time": 0.4}]}, "j3": {"rotate": [{"angle": -39.11}, {"time": 0.0333, "angle": 68.75}, {"time": 0.1, "angle": 12.95}, {"time": 0.1667, "angle": 15.61}, {"time": 0.2333, "angle": -94.44}, {"time": 0.2667, "angle": -39.11}, {"time": 0.3, "angle": 68.75}, {"time": 0.3667, "angle": 12.95}, {"time": 0.4333, "angle": 15.61}, {"time": 0.5, "angle": -39.11}], "translate": [{"x": -15.72, "y": -7.24}, {"time": 0.0333, "x": -13.39, "y": 28.82}, {"time": 0.1, "x": 7.91, "y": 6.56}, {"time": 0.1667, "x": 2.23, "y": -34.8}, {"time": 0.2333, "x": -18.06, "y": -43.31}, {"time": 0.2667, "x": -15.72, "y": -7.24}, {"time": 0.3, "x": -13.39, "y": 28.82}, {"time": 0.3667, "x": 7.91, "y": 6.56}, {"time": 0.4333, "x": 2.23, "y": -34.8}, {"time": 0.5, "x": -15.72, "y": -7.24}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": -12.55, "curve": 0.25, "c3": 0.173}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": -12.55, "curve": 0.25, "c3": 0.173}, {"time": 0.5}]}, "bone44": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.309}, {"time": 0.2333, "angle": -38.25, "curve": 0.782, "c3": 0.75}, {"time": 0.5}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.309}, {"time": 0.2333, "x": -54.82, "y": 178.83, "curve": 0.782, "c3": 0.75}, {"time": 0.5}]}, "bone73": {"rotate": [{"angle": -29.68}, {"time": 0.0333}, {"time": 0.1667, "angle": -33.28}, {"time": 0.2667, "angle": -29.68}, {"time": 0.3}, {"time": 0.4333, "angle": -33.28}, {"time": 0.5, "angle": -8.32}]}, "bone7": {"translate": [{"x": -767.54, "y": -65.22}, {"time": 0.5, "x": -289.74, "y": -65.22}]}, "bone86": {"rotate": [{"angle": 33.54}, {"time": 0.1333}, {"time": 0.2667, "angle": 33.54}, {"time": 0.4}], "translate": [{}, {"time": 0.0667, "x": -4.91, "y": -18.54}, {"time": 0.1333, "x": -9.82, "y": -8.84}, {"time": 0.2667}, {"time": 0.3333, "x": -4.91, "y": -18.54}, {"time": 0.4, "x": -9.82, "y": -8.84}, {"time": 0.5}]}, "bone83": {"rotate": [{"angle": -30.39}, {"time": 0.0333, "angle": 23.2, "curve": "stepped"}, {"time": 0.1667, "angle": 23.2}, {"time": 0.2667, "angle": -30.39}, {"time": 0.3, "angle": 23.2}], "translate": [{"x": 2.48, "y": -2.02}, {"time": 0.0333, "x": 3.3, "y": -2.36}, {"time": 0.1, "x": 6.84, "y": -5.45}, {"time": 0.1667, "y": -0.98}, {"time": 0.2667, "x": 2.48, "y": -2.02}, {"time": 0.3, "x": 3.3, "y": -2.36}, {"time": 0.3667, "x": 6.84, "y": -5.45}, {"time": 0.4333, "y": -0.98}, {"time": 0.5, "x": 2.48, "y": -2.02}]}, "bone72": {"rotate": [{"angle": 9.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 50, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 9.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3667, "angle": 50, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.22}]}, "bone28": {"rotate": [{}, {"time": 0.1333, "angle": -24.03}, {"time": 0.2667}, {"time": 0.4, "angle": -24.03}, {"time": 0.5}]}, "bone85": {"rotate": [{"angle": 35.9}, {"time": 0.0667, "angle": -17.35}, {"time": 0.1, "angle": 20.07}, {"time": 0.1333}, {"time": 0.2667, "angle": 35.9}, {"time": 0.3333, "angle": -17.35}, {"time": 0.3667, "angle": 20.07}, {"time": 0.4}, {"time": 0.5, "angle": 35.9}]}, "bone30": {"rotate": [{"angle": -4.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": -26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": -4.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3667, "angle": -26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.95}]}, "bone8": {"translate": [{"y": 7.16}, {"time": 0.1333, "y": -11.94}, {"time": 0.2667, "y": 11.94}, {"time": 0.3667, "y": -10.75}, {"time": 0.5}]}, "bone29": {"rotate": [{"angle": -21.9, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -26.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": -13.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -21.9, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "angle": -26.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -13.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -21.9}]}, "bone74": {"rotate": [{"angle": 16.4}, {"time": 0.0333}, {"time": 0.1667, "angle": -7.71}, {"time": 0.2667, "angle": 16.4}, {"time": 0.3}, {"time": 0.4333, "angle": -7.71}, {"time": 0.5, "angle": -1.93}]}, "bone34": {"rotate": [{"angle": -5.5}]}, "bone25": {"rotate": [{"angle": 38.19, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 48.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 38.19, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 38.19}]}, "bone33": {"rotate": [{"angle": 2.53}]}, "bone39": {"rotate": [{"angle": 3.27}]}, "bone75": {"rotate": [{"time": 0.0333}, {"time": 0.1, "angle": -7.62}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3}, {"time": 0.3667, "angle": -7.62}, {"time": 0.4333}]}, "bone92": {"rotate": [{"angle": 1.99}]}, "bone100": {"translate": [{"y": 9.88, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": -28.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 9.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": -28.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.52, "y": 9.88}]}}, "deform": {"default": {"a12111111": {"a12": [{"time": 0.5, "vertices": [0.95061, 4.29788, 0.30091, 4.39148, 0.40688, 4.38287, -1.82603, 4.0052, 0.04712, 4.4015, 2.98691, 3.23334, 0.715, 1.19424, 0.56042, 1.27414, -0.14725, 1.38414, 0.45432, 1.31572, 1.21939, 0.67125, 2.0861, 2.80698, 1.7196, 3.04538, -0.02209, 3.49721, 1.46476, 3.17575, 3.21637, 1.37334, 0.65407, 2.44751, 0.34359, 2.50998, -0.94984, 2.34866, 0.13716, 2.52969, 1.79829, 1.78447, 2.5674, -1.13168, 2.74516, -0.57977, 1.64723, -2.27104, 7.93594, 1.73819, 7.40936, 3.33209, 7.73059, -2.49747, 7.93594, 1.73819, 7.40936, 3.33209, 7.73059, -2.49747, 7.93594, 1.73819, 7.40936, 3.33209, 7.73059, -2.49747, 4.83237, -0.93224, 2.95927, -3.93236, 2.64316, -1.81859, 1.62111, -2.76874, -0.6544, -3.14091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.01558, -1.01367, 1.15503, -0.85135, 1.13419, -0.87894, 1.42101, -0.19879, 1.20222, -0.78329, 0.36642, -1.38725, 1.73472, -2.43983, -0.34956, -2.97313, 3.52845, 0.62242, 3.03471, -1.90465, 7.40936, 3.33209, 7.73059, -2.49747]}]}, "a9111111": {"a9": [{}, {"time": 0.1, "vertices": [3.17478, -2.70506, 2.09392, 3.60721, 1.53421, 3.87845, 1.62743, 3.84034, 2.15902, -1.05078, 0.64789, 2.3121, 0.29686, 2.38271, 0.35433, 2.37488, 1.73682, 0.06544, -0.37486, 1.69716, -0.62306, 1.62255, -0.58372, 1.63712, 1.11592, -0.32397, 0.11927, 1.15587, -0.05394, 1.16076, -0.02589, 1.16172, 0.35018, 0.7197, -0.7707, 0.21593, -0.79427, 0.0989, -0.79158, 0.11807, 8.13524, 7.45086, -4.92133, 9.41488, -7.00272, 8.09342, -8.64488, 6.66164, 15.89261, 15.32698, 12.18436, 25.98064, 10.07946, 25.93452, 8.20519, 13.78282, -10.9409, 12.51431, -4.53435, 8.121, -7.38913, -1.42395, -4.63641, -1.35221, 2.15922, -4.32001, 2.77759, -3.95092, -3.15656, 0.33786, 0.23183, -3.16609, 0.70005, -3.09642, -0.86503, 0.29086, -0.13156, -0.90308, 0.0042, -0.91262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.87131, -1.13617, 0.96211, 1.06037, 0.79373, 1.19166, 0.82227, 1.17217, 1.3881, -2.17169, 1.88856, 1.75395, 1.60675, 2.01525, 1.65493, 1.97593, 2.55063, -2.41055, 1.91573, 2.94047, 1.45715, 3.19263, 1.53383, 3.15658, 2.96942, -3.6592, 3.06941, 3.5757, 2.50357, 3.99232, 2.59923, 3.93077, 7.22, 8.81693, -0.49655, 2.04048, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.42657, -0.30125, 0.55141, -1.34973, 0.74599, -1.25273, 0.71557, -1.27036]}, {"time": 0.2, "vertices": [6.34956, -5.41011, 4.18783, 7.21443, 3.06843, 7.75691, 3.25486, 7.68068, 4.31804, -2.10156, 1.29579, 4.62419, 0.59371, 4.76542, 0.70865, 4.74977, 3.47364, 0.13088, -0.74972, 3.39431, -1.24612, 3.24509, -1.16743, 3.27423, 2.23185, -0.64794, 0.23855, 2.31174, -0.10787, 2.32152, -0.05177, 2.32345, 5.39046, 3.56049, -4.74192, 4.46357, -5.0743, 3.98497, -6.04659, 2.79967, 14.85506, 8.17487, -8.36731, 14.99213, -10.44891, 13.55967, -13.94273, 10.87739, 25.29229, 18.27763, 27.4248, 29.39408, 21.48291, 28.86957, 14.65544, 16.36748, -16.1079, 15.99017, -1.54511, 3.90683, -4.13633, 1.04801, -9.27283, -2.70441, 4.31844, -8.64003, 5.55519, -7.90184, -7.88044, 2.16285, -0.59645, -8.21476, 0.47988, -8.14777, -1.73007, 0.58173, -0.26312, -1.80616, 0.0084, -1.82523, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.74263, -2.27235, 1.92422, 2.12074, 1.58746, 2.38331, 1.64453, 2.34434, 2.7762, -4.34338, 3.77713, 3.5079, 3.2135, 4.03051, 3.30985, 3.95186, 5.10127, -4.8211, 3.83145, 5.88094, 2.9143, 6.38527, 3.06767, 6.31316, 5.93883, -7.3184, 6.13882, 7.15139, 5.00713, 7.98464, 5.19846, 7.86154, 14.71188, 11.66465, 4.98803, 6.66656, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.85314, -0.60249, 1.10283, -2.69945, 1.49198, -2.50547, 1.43115, -2.54072]}, {"time": 0.2667, "vertices": [8.46608, -7.21348, 5.58378, 9.61924, 4.09124, 10.34255, 4.33982, 10.2409, 5.75739, -2.80208, 1.72772, 6.16559, 0.79162, 6.35389, 0.94487, 6.33302, 4.63153, 0.17451, -0.99962, 4.52575, -1.66149, 4.32679, -1.55658, 4.36564, 2.9758, -0.86392, 0.31807, 3.08232, -0.14383, 3.09536, -0.06903, 3.09793, 4.58167, 3.56893, -4.54449, 3.71159, -4.82919, 3.20931, -5.58244, 2.30871, 11.5852, 8.85729, -8.95114, 10.99478, -10.5181, 9.94865, -13.24664, 8.01431, 25.80158, 19.33283, 26.79217, 29.18658, 20.01616, 28.188, 13.11156, 14.88468, -15.10321, 12.9071, -5.80852, 1.3515, -0.61226, -3.45646, -12.36377, -3.60588, 5.75792, -11.52004, 7.40692, -10.53579, -9.63653, 2.05761, -0.20632, -9.90714, 1.15107, -9.77763, -2.30675, 0.77564, -0.35082, -2.40822, 0.0112, -2.43364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.3235, -3.0298, 2.56563, 2.82765, 2.11661, 3.17775, 2.19271, 3.12578, 3.7016, -5.79117, 5.03617, 4.6772, 4.28467, 5.37401, 4.41314, 5.26914, 6.80169, -6.42813, 5.10861, 7.84126, 3.88573, 8.51369, 4.09023, 8.41755, 7.91844, -9.75786, 8.18509, 9.53519, 6.67618, 10.64619, 6.93128, 10.48205, 11.39272, 15.26494, 3.87958, 5.1851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.80418, -0.80332, 1.47043, -3.59927, 1.9893, -3.34063, 1.9082, -3.38763]}, {"time": 0.3667, "vertices": [11.64086, -9.91854, 7.67769, 13.22645, 5.62545, 14.221, 5.96725, 14.08124, 7.91641, -3.85286, 2.37561, 8.47769, 1.08847, 8.7366, 1.29919, 8.70791, 6.36835, 0.23995, -1.37448, 6.2229, -2.28456, 5.94934, -2.1403, 6.00276, 4.09172, -1.1879, 0.43734, 4.2382, -0.19776, 4.25612, -0.09491, 4.25966, 3.36849, 3.58159, -4.24834, 2.58361, -4.46154, 2.04583, -4.88622, 1.57226, 6.68042, 9.88091, -9.82688, 4.99874, -10.62188, 4.53211, -12.2025, 3.71967, 13.82671, 15.55178, 12.70625, 24.21409, 7.58304, 22.29388, 1.56944, 9.28714, -7.2086, 0.8189, -10.70447, -3.48826, 5.09285, -8.45666, -17.00018, -4.95809, 7.91714, -15.84005, 10.18451, -14.48671, -12.27065, 1.89976, 0.37889, -12.44571, 2.15786, -12.22241, -3.17179, 1.0665, -0.48238, -3.3113, 0.0154, -3.34626, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.19482, -4.16597, 3.52774, 3.88802, 2.91034, 4.36941, 3.01498, 4.29795, 5.08969, -7.96286, 6.92473, 6.43115, 5.89142, 7.38926, 6.06807, 7.24507, 9.35233, -8.83868, 7.02433, 10.78173, 5.34288, 11.70632, 5.62406, 11.57413, 10.88786, -13.41706, 11.2545, 13.11088, 9.17974, 14.63851, 9.53051, 14.41282, 6.27497, 9.44863, 2.2169, 2.96292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.23075, -1.10457, 2.02185, -4.94899, 2.73529, -4.59336, 2.62377, -4.65799]}, {"time": 0.5, "vertices": [15.8739, -13.52528, 10.46958, 18.03607, 7.67107, 19.39227, 8.13716, 19.20169, 10.7951, -5.2539, 3.23947, 11.56049, 1.48428, 11.91354, 1.77163, 11.87442, 8.68411, 0.32721, -1.87429, 8.48578, -3.1153, 8.11273, -2.91859, 8.18558, 5.57962, -1.61986, 0.59637, 5.77936, -0.26968, 5.8038, -0.12943, 5.80862, 1.75092, 3.59848, -3.85348, 1.07965, -3.97133, 0.49451, -3.95792, 0.59033, -1.05505, 4.65609, -4.39251, -1.87036, -4.06557, -2.50278, -4.12473, -2.40393, 0, 0, 0, 0, -0.03293, 0.49526, -1.06046, -10.26868, 10.29283, 0.79224, -18.03195, -9.40433, 12.47619, -16.06039, -23.18207, -6.76103, 10.7961, -21.60007, 13.88797, -19.75461, -15.78281, 1.68929, 1.15916, -15.83047, 3.50024, -15.48212, -4.32516, 1.45432, -0.65779, -4.51541, 0.021, -4.56308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.35657, -5.68087, 4.81055, 5.30185, 3.96865, 5.95828, 4.11134, 5.86084, 6.94049, -10.85844, 9.44282, 8.76974, 8.03375, 10.07626, 8.27464, 9.87964, 12.75317, -12.05275, 9.57864, 14.70236, 7.28575, 15.96317, 7.66917, 15.7829, 14.84708, -18.29599, 15.34705, 17.87848, 12.51783, 19.96161, 12.99615, 19.65384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.13284, -1.50623, 2.75706, -6.74863, 3.72994, -6.26367, 3.57787, -6.35181]}]}, "a11111111": {"a11": [{}, {"time": 0.1, "vertices": [15.72891, 11.07146, 15.72891, 11.07146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.35132, 7.01475, 15.72891, 11.07146]}, {"time": 0.2333, "vertices": [23.06663, 11.48821, 18.58756, 11.35589, 6.8107, 2.14362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.06076, 3.79483, 2.91675, -7.46638, 15.87288, 7.20253, 23.06663, 11.48821]}, {"time": 0.5}]}, "a1": {"a1": [{"time": 0.5, "offset": 6, "vertices": [-0.79515, 1.08903, 0.78283, 1.09796, -0.65027, 2.51018, 2.17722, 1.40848, -0.65027, 2.51018, 2.17722, 1.40848, -0.65027, 2.51018, 2.17722, 1.40848, -0.90793, 0.85767, 0.52775, 1.13205, 1.64619, -2.17731, -1.54734, -2.2486, 2.41951, -8.92815, -7.71019, -5.1106, -0.89195, -10.79155, -10.52246, -2.55531, -2.83376, -7.62354, -8.12812, 0.28618, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.05496, -2.67653, -0.00079, -8.48788]}]}}}, "drawOrder": [{"offsets": [{"slot": "a19", "offset": 7}, {"slot": "a18", "offset": -7}, {"slot": "a17111111", "offset": -7}, {"slot": "a16", "offset": -7}, {"slot": "a15", "offset": -7}]}]}, "run3": {"slots": {"a23": {"color": [{"color": "ffffffff"}]}, "a21": {"color": [{"color": "ffffffff"}]}, "a19": {"color": [{"color": "ffffffff"}]}, "a24": {"color": [{"color": "ffffffff"}]}, "a25": {"color": [{"color": "ffffffff"}]}, "a20": {"color": [{"color": "ffffffff"}]}, "a22": {"color": [{"color": "ffffffff"}]}}, "bones": {"bone": {"translate": [{"y": 76.93, "curve": 0.619, "c3": 0.75}, {"time": 0.1333, "y": 62.61, "curve": 0.25, "c3": 0.399}, {"time": 0.2667, "y": 76.93}]}, "bone7": {"translate": [{"x": -289.74, "y": -65.22}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}, "bone60": {"rotate": [{"angle": -16.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0333, "angle": -10.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 6.04, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.2667, "angle": -15.96}]}, "bone70": {"rotate": [{"angle": -8.28, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": -5.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.28}], "translate": [{"x": 1.54, "y": -12.11, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "x": -1.34, "y": -9.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "x": -12.11, "y": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.54, "y": -12.11}]}, "a6": {"rotate": [{"angle": 11.87, "curve": 0.312, "c2": 0.27, "c3": 0.664, "c4": 0.66}, {"time": 0.0333, "angle": -6.09, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "angle": -20.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 19.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 11.87}]}, "bone16": {"rotate": [{"angle": -79.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1333, "angle": -77.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -79.95}]}, "bone11": {"rotate": [{"angle": 0.8, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.63, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 0.8}]}, "bone57": {"rotate": [{"angle": -1.37, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.0667, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -1.37}]}, "bone56": {"rotate": [{"angle": 14.59, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 17.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": 9.8, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -13, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.2667, "angle": 14.59}]}, "bone67": {"rotate": [{"angle": 18.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 12.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 18.87}]}, "bone17": {"rotate": [{"angle": -6.57, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": 1.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": -6.57}]}, "bone19": {"rotate": [{"angle": 3.05, "curve": 0.326, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "angle": -4.83, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.1, "angle": -17.72, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.18, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "angle": 3.05}]}, "bone71": {"rotate": [{"angle": -4.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -4.46}], "translate": [{"x": 9.74, "y": -5.24, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "x": 14.68, "y": -6.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -12.34, "y": -0.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 9.74, "y": -5.24}]}, "bone22": {"rotate": [{"angle": 12.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": 7.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -6.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.2667, "angle": 11.68}]}, "bone12": {"rotate": [{"angle": -4.77, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -4.77}]}, "bone21": {"rotate": [{"angle": 12.38, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": 8.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 5.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 13.94, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 12.38}]}, "bone59": {"rotate": [{"angle": -7.48, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0333, "angle": -1.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -7.48}]}, "bone65": {"rotate": [{"angle": -12.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -21.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": -12.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 14.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -12.62}]}, "bone61": {"rotate": [{"angle": -4.18, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.0667, "angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 16.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2667, "angle": -4.18}]}, "a8": {"rotate": [{"angle": 12.93, "curve": 0.362, "c2": 0.44, "c3": 0.715, "c4": 0.84}, {"time": 0.0333, "angle": 36.5, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": 39.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -22.06, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.2667, "angle": 12.93}]}, "bone37": {"rotate": [{"angle": -8.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.01}]}, "bone20": {"rotate": [{"angle": 12.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0333, "angle": 7.74, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": -9.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -22.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 12.18}]}, "bone55": {"rotate": [{"angle": 9.18, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": -0.19, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2667, "angle": 9.18}]}, "bone66": {"rotate": [{"angle": 0.6, "curve": 0.335, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.0333, "angle": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.13, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2667, "angle": 0.6}]}, "bone53": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.97, "y": 0.01}]}, "a7": {"rotate": [{"angle": 31.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": 21.33, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": -22.06, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.2667, "angle": 28.89}]}, "bone62": {"rotate": [{"angle": 8.16, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": -3.17, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2667, "angle": 8.16}]}, "bone14": {"rotate": [{"angle": 45.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -313.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -318.84, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -314.45}]}, "bone2": {"translate": [{"y": -3.3, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": -3.3}]}, "bone68": {"rotate": [{"angle": 13.53, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.01, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 13.53}]}, "bone13": {"rotate": [{"angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.2667, "angle": -5.94}]}, "bone36": {"rotate": [{"angle": 3.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 0.56}]}, "bone43": {"rotate": [{"angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.77}]}, "bone18": {"rotate": [{"angle": -8.95, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.0333, "angle": -14.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": -8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 9.47, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -8.95}]}, "bone48": {"translate": [{"x": -2.29, "y": -76.85, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.1333, "x": -4.06, "y": -99.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "x": -2.29, "y": -76.85}]}, "bone5": {"translate": [{"x": 1.86, "y": -0.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 3.72, "y": -0.11, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2333, "x": 1.86, "y": -0.06}]}, "bone38": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.97, "y": 0.01}]}, "bone32": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.97, "y": 0.01}]}, "a3": {"rotate": [{"angle": -4.24, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0333, "angle": -0.61, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 1.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.19, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -4.24}]}, "bone94": {"rotate": [{"angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 0.56}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 8.11, "y": -0.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 4.06, "y": -0.44}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}, "a4": {"rotate": [{"angle": -7.95, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0333, "angle": -5.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 3.5, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.2667, "angle": -7.75}]}, "bone69": {"rotate": [{"angle": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.7, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 1.42}]}, "bone64": {"rotate": [{"angle": -12.77, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.0667, "angle": 6.06, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -12.77}]}, "bone42": {"rotate": [{"angle": 3.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -0.42}]}, "bone6": {"rotate": [{"angle": 1.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 1.29}], "translate": [{"x": 8.18, "y": -7.48}]}, "a5": {"rotate": [{"angle": -5.87, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.0667, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2667, "angle": -5.87}]}, "bone15": {"rotate": [{"angle": -45.1, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.0667, "angle": -41.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -46.93, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": -45.1}]}, "bone95": {"rotate": [{"angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.01}]}, "bone4": {"rotate": [{"angle": 7.64}], "translate": [{"x": 0.03, "y": 0.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.16, "y": 3.1, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 0.03, "y": 0.57}]}, "bone63": {"rotate": [{"angle": 16.24, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 18.55, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": 11.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -9.27, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.2667, "angle": 16.24}]}, "bone23": {"rotate": [{"angle": 3.6, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.0667, "angle": 16.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.02, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.2667, "angle": 3.6}]}, "j3": {"rotate": [{"angle": -39.11}, {"time": 0.0333, "angle": 68.75}, {"time": 0.1, "angle": 12.95}, {"time": 0.1667, "angle": 15.61}, {"time": 0.2333, "angle": -94.44}, {"time": 0.2667, "angle": -39.11}], "translate": [{"x": -15.72, "y": -7.24}, {"time": 0.0333, "x": -13.39, "y": 28.82}, {"time": 0.1, "x": 7.91, "y": 6.56}, {"time": 0.1667, "x": 2.23, "y": -34.8}, {"time": 0.2333, "x": -18.06, "y": -43.31}, {"time": 0.2667, "x": -15.72, "y": -7.24}]}, "bone28": {"rotate": [{}, {"time": 0.1333, "angle": -24.03}, {"time": 0.2667}]}, "bone9": {"rotate": [{}, {"time": 0.1333, "angle": 24.13}, {"time": 0.2667}], "translate": [{}, {"time": 0.1333, "y": -7.36}, {"time": 0.2667}]}, "bone46": {"translate": [{}, {"time": 0.1333, "x": 13.94}, {"time": 0.2667}]}, "bone44": {"translate": [{}, {"time": 0.1333, "x": -9.48}, {"time": 0.2667}]}, "j4": {"rotate": [{"angle": 33.51}, {"time": 0.1333, "angle": 3.85}, {"time": 0.2, "angle": -103.54}, {"time": 0.2667, "angle": 33.51}], "translate": [{"x": -11.85, "y": 23.77}, {"time": 0.0667, "x": 10.06, "y": 6.59}, {"time": 0.1333, "x": 5.76, "y": -43.21}, {"time": 0.2, "x": -21.21, "y": -63.73}, {"time": 0.2667, "x": -11.85, "y": 23.77}]}, "bone85": {"rotate": [{"angle": 35.9}, {"time": 0.0667, "angle": -17.35}, {"time": 0.1, "angle": 20.07}, {"time": 0.1333}, {"time": 0.2667, "angle": 35.9}]}, "j2": {"rotate": [{"angle": -63.88}, {"time": 0.0333, "angle": -117.97}, {"time": 0.1, "angle": -112.72}, {"time": 0.1667, "angle": -1.12}, {"time": 0.2333, "angle": -9.8}, {"time": 0.2667, "angle": -63.88}], "translate": [{"x": -9.14, "y": -22.51}, {"time": 0.0333, "x": -22.28, "y": -32.01}, {"time": 0.1, "x": -12.48, "y": -6.47}, {"time": 0.1667, "x": 0.66, "y": 27.94}, {"time": 0.2333, "x": 4, "y": -13}, {"time": 0.2667, "x": -9.14, "y": -22.51}]}, "bone75": {"rotate": [{"time": 0.0333}, {"time": 0.1, "angle": -7.62}, {"time": 0.1667}]}, "j1": {"rotate": [{"angle": -69.46}, {"time": 0.0333, "angle": -125.2}, {"time": 0.1, "angle": -112.57}, {"time": 0.1667, "angle": -0.92}, {"time": 0.2333, "angle": -13.72}, {"time": 0.2667, "angle": -69.46}], "translate": [{"x": -9.02, "y": -14.53}, {"time": 0.0333, "x": -19.11, "y": -21.79}, {"time": 0.1, "x": -6.69, "y": 23.23}, {"time": 0.1667, "x": 1.52, "y": 51.06}, {"time": 0.2333, "x": 1.07, "y": -7.27}, {"time": 0.2667, "x": -9.02, "y": -14.53}]}, "bone100": {"translate": [{"y": 9.88, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": -28.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.52, "y": 9.88}]}, "bone87": {"rotate": [{"angle": -3.99}, {"time": 0.1333}]}, "bone86": {"rotate": [{"angle": 33.54}, {"time": 0.1333}], "translate": [{}, {"time": 0.0667, "x": -4.91, "y": -18.54}, {"time": 0.1333, "x": -9.82, "y": -8.84}, {"time": 0.2667}]}, "bone84": {"rotate": [{"angle": 61.62}, {"time": 0.1333, "angle": 4.35}]}, "bone83": {"rotate": [{"angle": -30.39}, {"time": 0.0333, "angle": 23.2}], "translate": [{"x": 2.48, "y": -2.02}, {"time": 0.0333, "x": 3.3, "y": -2.36}, {"time": 0.1, "x": 6.84, "y": -5.45}, {"time": 0.1667, "y": -0.98}, {"time": 0.2667, "x": 2.48, "y": -2.02}]}, "bone72": {"rotate": [{"angle": 9.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 50, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 9.22}]}, "bone52": {"rotate": [{"angle": 40.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 50, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 40.77}]}, "bone30": {"rotate": [{"angle": -4.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": -26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": -4.95}]}, "bone29": {"rotate": [{"angle": -21.9, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -26.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": -13.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -21.9}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": -12.55, "curve": 0.25, "c3": 0.173}, {"time": 0.2667}]}, "bone79": {"rotate": [{"angle": 9.69}, {"time": 0.0333}, {"time": 0.1667, "angle": 56.36}, {"time": 0.2667, "angle": 14.09}]}, "bone78": {"rotate": [{"angle": -34.28}, {"time": 0.0333}, {"time": 0.1667, "angle": -53.76}, {"time": 0.2667, "angle": -13.44}], "translate": [{"x": -5.34, "y": -0.86}, {"time": 0.0333, "x": -6.61}, {"time": 0.1667, "x": -1.54, "y": -3.45}, {"time": 0.2667, "x": -5.34, "y": -0.86}]}, "bone74": {"rotate": [{"angle": 16.4}, {"time": 0.0333}, {"time": 0.1667, "angle": -7.71}, {"time": 0.2667, "angle": -1.93}]}, "bone73": {"rotate": [{"angle": -29.68}, {"time": 0.0333}, {"time": 0.1667, "angle": -33.28}, {"time": 0.2667, "angle": -8.32}]}, "bone10": {"rotate": [{"angle": -3.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 2.31, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -3.85}]}, "bone25": {"rotate": [{"angle": 38.19, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 48.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 38.19}]}, "bone26": {"rotate": [{"angle": 38.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 48.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 20.55, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": 38.28}]}, "bone33": {"rotate": [{"angle": 2.53}]}, "bone34": {"rotate": [{"angle": -5.5}]}, "bone39": {"rotate": [{"angle": 3.27}]}, "bone40": {"rotate": [{"angle": -5.48}]}, "bone92": {"rotate": [{"angle": 1.99}]}, "bone93": {"rotate": [{"angle": -5.49}]}}, "deform": {"default": {"a12111111": {"a12": [{"vertices": [0.95061, 4.29788, 0.30091, 4.39148, 0.40688, 4.38287, -1.82603, 4.0052, 0.04712, 4.4015, 2.98691, 3.23334, 0.715, 1.19424, 0.56042, 1.27414, -0.14725, 1.38414, 0.45432, 1.31572, 1.21939, 0.67125, 2.0861, 2.80698, 1.7196, 3.04538, -0.02209, 3.49721, 1.46476, 3.17575, 3.21637, 1.37334, 0.65407, 2.44751, 0.34359, 2.50998, -0.94984, 2.34866, 0.13716, 2.52969, 1.79829, 1.78447, 2.5674, -1.13168, 2.74516, -0.57977, 1.64723, -2.27104, 7.93594, 1.73819, 7.40936, 3.33209, 7.73059, -2.49747, 7.93594, 1.73819, 7.40936, 3.33209, 7.73059, -2.49747, 7.93594, 1.73819, 7.40936, 3.33209, 7.73059, -2.49747, 4.83237, -0.93224, 2.95927, -3.93236, 2.64316, -1.81859, 1.62111, -2.76874, -0.6544, -3.14091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.01558, -1.01367, 1.15503, -0.85135, 1.13419, -0.87894, 1.42101, -0.19879, 1.20222, -0.78329, 0.36642, -1.38725, 1.73472, -2.43983, -0.34956, -2.97313, 3.52845, 0.62242, 3.03471, -1.90465, 7.40936, 3.33209, 7.73059, -2.49747]}]}, "a9111111": {"a9": [{"vertices": [15.8739, -13.52528, 10.46958, 18.03607, 7.67107, 19.39227, 8.13716, 19.20169, 10.7951, -5.2539, 3.23947, 11.56049, 1.48428, 11.91354, 1.77163, 11.87442, 8.68411, 0.32721, -1.87429, 8.48578, -3.1153, 8.11273, -2.91859, 8.18558, 5.57962, -1.61986, 0.59637, 5.77936, -0.26968, 5.8038, -0.12943, 5.80862, 1.75092, 3.59848, -3.85348, 1.07965, -3.97133, 0.49451, -3.95792, 0.59033, -1.05505, 4.65609, -4.39251, -1.87036, -4.06557, -2.50278, -4.12473, -2.40393, 0, 0, 0, 0, -0.03293, 0.49526, -1.06046, -10.26868, 10.29283, 0.79224, -18.03195, -9.40433, 12.47619, -16.06039, -23.18207, -6.76103, 10.7961, -21.60007, 13.88797, -19.75461, -15.78281, 1.68929, 1.15916, -15.83047, 3.50024, -15.48212, -4.32516, 1.45432, -0.65779, -4.51541, 0.021, -4.56308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.35657, -5.68087, 4.81055, 5.30185, 3.96865, 5.95828, 4.11134, 5.86084, 6.94049, -10.85844, 9.44282, 8.76974, 8.03375, 10.07626, 8.27464, 9.87964, 12.75317, -12.05275, 9.57864, 14.70236, 7.28575, 15.96317, 7.66917, 15.7829, 14.84708, -18.29599, 15.34705, 17.87848, 12.51783, 19.96161, 12.99615, 19.65384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.13284, -1.50623, 2.75706, -6.74863, 3.72994, -6.26367, 3.57787, -6.35181]}]}, "a1": {"a1": [{"offset": 6, "vertices": [-0.79515, 1.08903, 0.78283, 1.09796, -0.65027, 2.51018, 2.17722, 1.40848, -0.65027, 2.51018, 2.17722, 1.40848, -0.65027, 2.51018, 2.17722, 1.40848, -0.90793, 0.85767, 0.52775, 1.13205, 1.64619, -2.17731, -1.54734, -2.2486, 2.41951, -8.92815, -7.71019, -5.1106, -0.89195, -10.79155, -10.52246, -2.55531, -2.83376, -7.62354, -8.12812, 0.28618, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.05496, -2.67653, -0.00079, -8.48788]}]}}}, "drawOrder": [{"offsets": [{"slot": "a19", "offset": 7}, {"slot": "a18", "offset": -7}, {"slot": "a17111111", "offset": -7}, {"slot": "a16", "offset": -7}, {"slot": "a15", "offset": -7}]}]}, "show_time": {"slots": {"a23": {"color": [{"color": "ffffffff"}]}, "a21": {"color": [{"color": "ffffffff"}]}, "a19": {"color": [{"color": "ffffffff"}]}, "a24": {"color": [{"color": "ffffffff"}]}, "a25": {"color": [{"color": "ffffffff"}]}, "a20": {"color": [{"color": "ffffffff"}]}, "a22": {"color": [{"color": "ffffffff"}]}}, "bones": {"bone": {"translate": [{"y": 76.93, "curve": 0.619, "c3": 0.75}, {"time": 0.1333, "y": 62.61, "curve": 0.25, "c3": 0.399}, {"time": 0.2667, "y": 76.93}]}, "bone7": {"translate": [{"x": -289.74, "y": -65.22}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}, "bone60": {"rotate": [{"angle": -16.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0333, "angle": -10.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 6.04, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.2667, "angle": -15.96}]}, "bone70": {"rotate": [{"angle": -8.28, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": -5.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.28}], "translate": [{"x": 1.54, "y": -12.11, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "x": -1.34, "y": -9.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "x": -12.11, "y": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.54, "y": -12.11}]}, "a6": {"rotate": [{"angle": 11.87, "curve": 0.312, "c2": 0.27, "c3": 0.664, "c4": 0.66}, {"time": 0.0333, "angle": -6.09, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "angle": -20.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 19.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 11.87}]}, "bone16": {"rotate": [{"angle": -79.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1333, "angle": -77.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -79.95}]}, "bone11": {"rotate": [{"angle": 0.8, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.63, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 0.8}]}, "bone57": {"rotate": [{"angle": -1.37, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.0667, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -1.37}]}, "bone56": {"rotate": [{"angle": 14.59, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 17.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": 9.8, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -13, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.2667, "angle": 14.59}]}, "bone67": {"rotate": [{"angle": 18.87, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 12.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 18.87}]}, "bone17": {"rotate": [{"angle": -6.57, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": 1.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": -6.57}]}, "bone19": {"rotate": [{"angle": 3.05, "curve": 0.326, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "angle": -4.83, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.1, "angle": -17.72, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.18, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.2667, "angle": 3.05}]}, "bone71": {"rotate": [{"angle": -4.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -4.46}], "translate": [{"x": 9.74, "y": -5.24, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "x": 14.68, "y": -6.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -12.34, "y": -0.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 9.74, "y": -5.24}]}, "bone22": {"rotate": [{"angle": 12.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": 7.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -6.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.2667, "angle": 11.68}]}, "bone12": {"rotate": [{"angle": -4.77, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -4.77}]}, "bone21": {"rotate": [{"angle": 12.38, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": 8.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 5.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 13.94, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 12.38}]}, "bone59": {"rotate": [{"angle": -7.48, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0333, "angle": -1.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -7.48}]}, "bone65": {"rotate": [{"angle": -12.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -21.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": -12.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 14.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -12.62}]}, "bone61": {"rotate": [{"angle": -4.18, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.0667, "angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 16.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2667, "angle": -4.18}]}, "a8": {"rotate": [{"angle": 12.93, "curve": 0.362, "c2": 0.44, "c3": 0.715, "c4": 0.84}, {"time": 0.0333, "angle": 36.5, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": 39.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -22.06, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.2667, "angle": 12.93}]}, "bone37": {"rotate": [{"angle": -8.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.01}]}, "bone20": {"rotate": [{"angle": 12.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0333, "angle": 7.74, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": -9.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -22.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 12.18}]}, "bone55": {"rotate": [{"angle": 9.18, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": -0.19, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2667, "angle": 9.18}]}, "bone66": {"rotate": [{"angle": 0.6, "curve": 0.335, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.0333, "angle": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.13, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2667, "angle": 0.6}]}, "bone53": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.97, "y": 0.01}]}, "a7": {"rotate": [{"angle": 31.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": 21.33, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": -22.06, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.2667, "angle": 28.89}]}, "bone62": {"rotate": [{"angle": 8.16, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": -3.17, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2667, "angle": 8.16}]}, "bone14": {"rotate": [{"angle": 45.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -313.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -318.84, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -314.45}]}, "bone2": {"translate": [{"y": -3.3, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": -3.3}]}, "bone68": {"rotate": [{"angle": 13.53, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.01, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 13.53}]}, "bone13": {"rotate": [{"angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.2667, "angle": -5.94}]}, "bone36": {"rotate": [{"angle": 3.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 0.56}]}, "bone43": {"rotate": [{"angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.77}]}, "bone18": {"rotate": [{"angle": -8.95, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.0333, "angle": -14.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": -8.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 9.47, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -8.95}]}, "bone48": {"translate": [{"x": -2.29, "y": -76.85, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.1333, "x": -4.06, "y": -99.13, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "x": -2.29, "y": -76.85}]}, "bone5": {"translate": [{"x": 1.86, "y": -0.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 3.72, "y": -0.11, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2333, "x": 1.86, "y": -0.06}]}, "bone38": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.97, "y": 0.01}]}, "bone32": {"translate": [{"x": -0.97, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.97, "y": 0.01}]}, "a3": {"rotate": [{"angle": -4.24, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0333, "angle": -0.61, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 1.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.19, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -4.24}]}, "bone94": {"rotate": [{"angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 0.56}]}, "bone50": {"translate": [{"x": 4.06, "y": -0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 8.11, "y": -0.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 4.06, "y": -0.44}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}, "a4": {"rotate": [{"angle": -7.95, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0333, "angle": -5.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 3.5, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.2667, "angle": -7.75}]}, "bone69": {"rotate": [{"angle": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.7, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": 1.42}]}, "bone64": {"rotate": [{"angle": -12.77, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.0667, "angle": 6.06, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -12.77}]}, "bone42": {"rotate": [{"angle": 3.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -0.42}]}, "bone6": {"rotate": [{"angle": 1.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 1.29}], "translate": [{"x": 8.18, "y": -7.48}]}, "a5": {"rotate": [{"angle": -5.87, "curve": 0.371, "c2": 0.49, "c3": 0.725, "c4": 0.9}, {"time": 0.0667, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2667, "angle": -5.87}]}, "bone15": {"rotate": [{"angle": -45.1, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.0667, "angle": -41.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -46.93, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": -45.1}]}, "bone95": {"rotate": [{"angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.01}]}, "bone4": {"rotate": [{"angle": 7.64}], "translate": [{"x": 0.03, "y": 0.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.16, "y": 3.1, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 0.03, "y": 0.57}]}, "bone63": {"rotate": [{"angle": 16.24, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 18.55, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": 11.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -9.27, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.2667, "angle": 16.24}]}, "bone23": {"rotate": [{"angle": 3.6, "curve": 0.37, "c2": 0.47, "c3": 0.729, "c4": 0.91}, {"time": 0.0667, "angle": 16.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.02, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.2667, "angle": 3.6}]}, "j3": {"rotate": [{"angle": -39.11}, {"time": 0.0333, "angle": 68.75}, {"time": 0.1, "angle": 12.95}, {"time": 0.1667, "angle": -107.8}, {"time": 0.2333, "angle": -94.44}, {"time": 0.2667, "angle": -39.11}], "translate": [{"x": -15.72, "y": -7.24}, {"time": 0.0333, "x": -13.39, "y": 28.82}, {"time": 0.1, "x": 7.91, "y": 6.56}, {"time": 0.1667, "x": -18.32, "y": -56.96}, {"time": 0.2333, "x": -18.06, "y": -43.31}, {"time": 0.2667, "x": -15.72, "y": -7.24}]}, "bone28": {"rotate": [{}, {"time": 0.1333, "angle": -24.03}, {"time": 0.2667}]}, "bone9": {"rotate": [{}, {"time": 0.1333, "angle": 24.13}, {"time": 0.2667}], "translate": [{}, {"time": 0.1333, "y": -7.36}, {"time": 0.2667}]}, "bone46": {"translate": [{}, {"time": 0.1333, "x": 13.94}, {"time": 0.2667}]}, "bone44": {"translate": [{}, {"time": 0.1333, "x": -9.48}, {"time": 0.2667}]}, "j4": {"rotate": [{"angle": -49.85}, {"time": 0.0333, "angle": -103.54}, {"time": 0.0667, "angle": -35.02}, {"time": 0.1, "angle": 33.51}, {"time": 0.2333, "angle": 3.85}, {"time": 0.2667, "angle": -49.85}], "translate": [{"x": -7.73, "y": -53.47}, {"time": 0.0333, "x": -21.21, "y": -63.73}, {"time": 0.0667, "x": -16.53, "y": -19.98}, {"time": 0.1, "x": -11.85, "y": 23.77}, {"time": 0.1667, "x": 10.06, "y": 6.59}, {"time": 0.2333, "x": 5.76, "y": -43.21}, {"time": 0.2667, "x": -7.73, "y": -53.47}]}, "bone85": {"rotate": [{"angle": 35.9}, {"time": 0.0667, "angle": -17.35}, {"time": 0.1, "angle": 20.07}, {"time": 0.1333}, {"time": 0.2667, "angle": 35.9}]}, "j2": {"rotate": [{"angle": -5.46}, {"time": 0.0333, "angle": -9.8}, {"time": 0.0667, "angle": -63.88}, {"time": 0.1, "angle": -117.97}, {"time": 0.1667, "angle": -112.72}, {"time": 0.2333, "angle": -1.12}, {"time": 0.2667, "angle": -5.46}], "translate": [{"x": 2.33, "y": 7.47}, {"time": 0.0333, "x": 4, "y": -13}, {"time": 0.0667, "x": -9.14, "y": -22.51}, {"time": 0.1, "x": -22.28, "y": -32.01}, {"time": 0.1667, "x": -12.48, "y": -6.47}, {"time": 0.2333, "x": 0.66, "y": 27.94}, {"time": 0.2667, "x": 2.33, "y": 7.47}]}, "bone75": {"rotate": [{"time": 0.0333}, {"time": 0.1, "angle": -7.62}, {"time": 0.1667}]}, "j1": {"rotate": [{"angle": -69.46}, {"time": 0.0333, "angle": -125.2}, {"time": 0.1, "angle": -112.57}, {"time": 0.1667, "angle": -0.92}, {"time": 0.2333, "angle": -13.72}, {"time": 0.2667, "angle": -69.46}], "translate": [{"x": -9.02, "y": -14.53}, {"time": 0.0333, "x": -19.11, "y": -21.79}, {"time": 0.1, "x": -6.69, "y": 23.23}, {"time": 0.1667, "x": 1.52, "y": 51.06}, {"time": 0.2333, "x": 1.07, "y": -7.27}, {"time": 0.2667, "x": -9.02, "y": -14.53}]}, "bone100": {"translate": [{"y": 9.88, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": -28.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.52, "y": 9.88}]}, "bone87": {"rotate": [{"angle": -3.99}, {"time": 0.1333}]}, "bone86": {"rotate": [{"angle": 33.54}, {"time": 0.1333}], "translate": [{}, {"time": 0.0667, "x": -4.91, "y": -18.54}, {"time": 0.1333, "x": -9.82, "y": -8.84}, {"time": 0.2667}]}, "bone84": {"rotate": [{"angle": 61.62}, {"time": 0.1333, "angle": 4.35}]}, "bone83": {"rotate": [{"angle": -30.39}, {"time": 0.0333, "angle": 23.2}], "translate": [{"x": 2.48, "y": -2.02}, {"time": 0.0333, "x": 3.3, "y": -2.36}, {"time": 0.1, "x": 6.84, "y": -5.45}, {"time": 0.1667, "y": -0.98}, {"time": 0.2667, "x": 2.48, "y": -2.02}]}, "bone72": {"rotate": [{"angle": 9.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 50, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 9.22}]}, "bone52": {"rotate": [{"angle": 40.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 50, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 40.77}]}, "bone30": {"rotate": [{"angle": -4.95, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": -26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": -4.95}]}, "bone29": {"rotate": [{"angle": -21.9, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -26.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": -13.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -21.9}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": -12.55, "curve": 0.25, "c3": 0.173}, {"time": 0.2667}]}, "bone79": {"rotate": [{"angle": 9.69}, {"time": 0.0333}, {"time": 0.1667, "angle": 56.36}, {"time": 0.2667, "angle": 14.09}]}, "bone78": {"rotate": [{"angle": -34.28}, {"time": 0.0333}, {"time": 0.1667, "angle": -53.76}, {"time": 0.2667, "angle": -13.44}], "translate": [{"x": -5.34, "y": -0.86}, {"time": 0.0333, "x": -6.61}, {"time": 0.1667, "x": -1.54, "y": -3.45}, {"time": 0.2667, "x": -5.34, "y": -0.86}]}, "bone74": {"rotate": [{"angle": 16.4}, {"time": 0.0333}, {"time": 0.1667, "angle": -7.71}, {"time": 0.2667, "angle": -1.93}]}, "bone73": {"rotate": [{"angle": -29.68}, {"time": 0.0333}, {"time": 0.1667, "angle": -33.28}, {"time": 0.2667, "angle": -8.32}]}, "bone10": {"rotate": [{"angle": -3.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 2.31, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -3.85}]}, "bone25": {"rotate": [{"angle": 38.19, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 48.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 38.19}]}, "bone26": {"rotate": [{"angle": 38.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 48.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 20.55, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": 38.28}]}, "bone33": {"rotate": [{"angle": 2.53}]}, "bone34": {"rotate": [{"angle": -5.5}]}, "bone39": {"rotate": [{"angle": 3.27}]}, "bone40": {"rotate": [{"angle": -5.48}]}, "bone92": {"rotate": [{"angle": 1.99}]}, "bone93": {"rotate": [{"angle": -5.49}]}}, "deform": {"default": {"a12111111": {"a12": [{"vertices": [0.95061, 4.29788, 0.30091, 4.39148, 0.40688, 4.38287, -1.82603, 4.0052, 0.04712, 4.4015, 2.98691, 3.23334, 0.715, 1.19424, 0.56042, 1.27414, -0.14725, 1.38414, 0.45432, 1.31572, 1.21939, 0.67125, 2.0861, 2.80698, 1.7196, 3.04538, -0.02209, 3.49721, 1.46476, 3.17575, 3.21637, 1.37334, 0.65407, 2.44751, 0.34359, 2.50998, -0.94984, 2.34866, 0.13716, 2.52969, 1.79829, 1.78447, 2.5674, -1.13168, 2.74516, -0.57977, 1.64723, -2.27104, 7.93594, 1.73819, 7.40936, 3.33209, 7.73059, -2.49747, 7.93594, 1.73819, 7.40936, 3.33209, 7.73059, -2.49747, 7.93594, 1.73819, 7.40936, 3.33209, 7.73059, -2.49747, 4.83237, -0.93224, 2.95927, -3.93236, 2.64316, -1.81859, 1.62111, -2.76874, -0.6544, -3.14091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.01558, -1.01367, 1.15503, -0.85135, 1.13419, -0.87894, 1.42101, -0.19879, 1.20222, -0.78329, 0.36642, -1.38725, 1.73472, -2.43983, -0.34956, -2.97313, 3.52845, 0.62242, 3.03471, -1.90465, 7.40936, 3.33209, 7.73059, -2.49747]}]}, "a9111111": {"a9": [{"vertices": [15.8739, -13.52528, 10.46958, 18.03607, 7.67107, 19.39227, 8.13716, 19.20169, 10.7951, -5.2539, 3.23947, 11.56049, 1.48428, 11.91354, 1.77163, 11.87442, 8.68411, 0.32721, -1.87429, 8.48578, -3.1153, 8.11273, -2.91859, 8.18558, 5.57962, -1.61986, 0.59637, 5.77936, -0.26968, 5.8038, -0.12943, 5.80862, 1.75092, 3.59848, -3.85348, 1.07965, -3.97133, 0.49451, -3.95792, 0.59033, -1.05505, 4.65609, -4.39251, -1.87036, -4.06557, -2.50278, -4.12473, -2.40393, 0, 0, 0, 0, -0.03293, 0.49526, -1.06046, -10.26868, 10.29283, 0.79224, -18.03195, -9.40433, 12.47619, -16.06039, -23.18207, -6.76103, 10.7961, -21.60007, 13.88797, -19.75461, -15.78281, 1.68929, 1.15916, -15.83047, 3.50024, -15.48212, -4.32516, 1.45432, -0.65779, -4.51541, 0.021, -4.56308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.35657, -5.68087, 4.81055, 5.30185, 3.96865, 5.95828, 4.11134, 5.86084, 6.94049, -10.85844, 9.44282, 8.76974, 8.03375, 10.07626, 8.27464, 9.87964, 12.75317, -12.05275, 9.57864, 14.70236, 7.28575, 15.96317, 7.66917, 15.7829, 14.84708, -18.29599, 15.34705, 17.87848, 12.51783, 19.96161, 12.99615, 19.65384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.13284, -1.50623, 2.75706, -6.74863, 3.72994, -6.26367, 3.57787, -6.35181]}]}, "a1": {"a1": [{"offset": 6, "vertices": [-0.79515, 1.08903, 0.78283, 1.09796, -0.65027, 2.51018, 2.17722, 1.40848, -0.65027, 2.51018, 2.17722, 1.40848, -0.65027, 2.51018, 2.17722, 1.40848, -0.90793, 0.85767, 0.52775, 1.13205, 1.64619, -2.17731, -1.54734, -2.2486, 2.41951, -8.92815, -7.71019, -5.1106, -0.89195, -10.79155, -10.52246, -2.55531, -2.83376, -7.62354, -8.12812, 0.28618, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.05496, -2.67653, -0.00079, -8.48788]}]}}}, "drawOrder": [{"offsets": [{"slot": "a19", "offset": 7}, {"slot": "a18", "offset": -7}, {"slot": "a17111111", "offset": -7}, {"slot": "a16", "offset": -7}, {"slot": "a15", "offset": -7}]}]}}}