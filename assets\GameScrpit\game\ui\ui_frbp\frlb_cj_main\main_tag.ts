import {
  _decorator,
  CCInteger,
  Component,
  easing,
  EventTouch,
  Input,
  instantiate,
  isValid,
  Label,
  Node,
  Prefab,
  ScrollView,
  Sprite,
  Tween,
  tween,
  UITransform,
  v3,
} from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import ToolExt from "../../../common/ToolExt";
import { activityId, FrbpModule } from "db://assets/GameScrpit/module/frbp/FrbpModule";
import { AchieveRewardRequest, AchieveRewardResponse } from "../../../net/protocol/Activity";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { Sleep } from "../../../GameDefine";
import { dtTime } from "../../../BoutStartUp";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("main_tag")
export class main_tag extends BaseCtrl {
  // 启用自动绑定
  protected autoBind: boolean = true;

  protected _achieveVO: any = null;

  public _startInit: boolean = false;

  @property(CCInteger)
  type: number = 0;

  @property(Node)
  content: Node = null;

  @property(Node)
  pengz: Node = null;

  protected _item: Prefab;
  protected _type_node: Prefab;

  public async init(achieveVO: any) {
    this._achieveVO = achieveVO;
    this._startInit = true;
    //this._item = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_COMMON_UI, "prefabs/Item");
    this._type_node = await this.assetMgr.loadPrefabSync(
      BundleEnum.BUNDLE_HD_FRBP,
      "prefab/cheng_jiu_main_list/type_node"
    );

    if (this.isValid == false) {
      return;
    }

    this.loadTask();
  }

  protected async start() {
    super.start();
  }

  protected async loadTask() {
    log.log("成就任务查看====", this._achieveVO);
    log.log("任务的相关完成数据======", FrbpModule.data.ProsperityMessage.achieveMap);

    let arr1 = [];
    let arr2 = [];
    for (let i = 0; i < this._achieveVO.requireList.length; i++) {
      if (FrbpModule.data.ProsperityMessage.achieveMap[this._achieveVO.id].basicTakeList.includes(i) == true) {
        arr1.push({
          index: i,
          req: this._achieveVO.requireList[i],
          award: this._achieveVO.basicRewardList[i],
          getIs: true,
        });
      } else {
        arr2.push({
          index: i,
          req: this._achieveVO.requireList[i],
          award: this._achieveVO.basicRewardList[i],
          getIs: false,
        });
      }
    }

    let list = arr2.concat(arr1);

    for (let i = 0; i < list.length; i++) {
      let node = this.content.children[i];
      if (!node) {
        // if (this.content.children.length > 5) {
        //   await Sleep(dtTime);
        // }
        await Sleep(dtTime);

        if (this.isValid == false) {
          return;
        }

        node = instantiate(this._type_node);
        node.setScale(0, 0, 0);
        this.content.addChild(node);
        node.walk((val) => {
          val.layer = this.node.layer;
        });
        node.active = true;
        tween(node)
          .tag(10999)
          .to(0.3, { scale: v3(1, 1, 1) }, { easing: "backOut" })
          .start();
      }

      let info = list[i];

      let basicReward = info.award;
      this.loadbasicReward(node, basicReward);

      node.getChildByName("task_des").getComponent(Label).string = this._achieveVO.name + info.req;

      let targetVal = FrbpModule.data.ProsperityMessage.achieveMap[this._achieveVO.id].targetVal;
      if (targetVal > info.req) {
        targetVal = info.req;
      }
      node.getChildByName("task_val_num").getComponent(Label).string = targetVal + "/" + info.req;
      node.getChildByPath("bg_jindutiaodi_jieshijindu/bar").getComponent(Sprite).fillRange = targetVal / info.req;

      let btn_task_get = node.getChildByName("btn_task_get");
      let btn_task_go = node.getChildByName("btn_task_go");
      let btn_yilingqu = node.getChildByName("btn_yilingqu");

      btn_task_get["info"] = info;
      btn_task_get.off(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.click_btn_task_get.bind(this)), this);
      btn_task_get.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.click_btn_task_get.bind(this)), this);

      btn_task_go.off(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.click_btn_task_go.bind(this)), this);
      btn_task_go.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.click_btn_task_go.bind(this)), this);

      if (info.getIs == true) {
        btn_task_get.active = false;
        btn_task_go.active = false;
        btn_yilingqu.active = true;
      } else {
        btn_yilingqu.active = false;
        if (targetVal >= info.req) {
          btn_task_get.active = true;
          btn_task_go.active = false;
        } else {
          btn_task_get.active = false;
          btn_task_go.active = true;
        }
      }
    }
  }

  protected click_btn_task_go(event) {}

  private click_btn_task_get(event: EventTouch) {
    AudioMgr.instance.playEffect(2009);
    let info = event.target["info"];
    let param = AchieveRewardRequest.create({
      activityId: activityId,
      achieveId: this._achieveVO.id,
      index: info.index,
      takeAll: true,
    });

    FrbpModule.api.takeAchieveReward(param, (res: AchieveRewardResponse) => {
      log.log("领取成就奖励结果=====", res);
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
      this.loadTask();
    });
  }

  private loadbasicReward(node: Node, basicReward) {
    let id = basicReward[0];
    let num = basicReward[1];
    let item = node.getChildByName("Item");
    FmUtils.setItemNode(item, id, num);
  }

  private _tickIndex: number = 0;
  public tick(dt: any): void {
    let pengz = this.pengz;

    let content_list = this.content;

    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();

    const index = this._tickIndex;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();

      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
      }
      this._tickIndex = i;
    }

    if (this._tickIndex >= content_list.children.length - 1) {
      this._tickIndex = 0;
    }
  }

  protected onDestroy(): void {
    Tween.stopAllByTag(10999);
  }
}
