// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Goods.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface GoodsAddRequest {
  resId: number;
  num: number;
}

/**  */
export interface GoodsRedeemMessage {
  /** 是否有购买过商城商店-仙玉 */
  virtualMoneySet: number[];
  /** 总的兑换物料ID和兑换次数 */
  redeemMap: { [key: number]: number };
}

export interface GoodsRedeemMessage_RedeemMapEntry {
  key: number;
  value: number;
}

/**  */
export interface GoodsRedeemRequest {
  /** 兑换的商品主键ID */
  redeemId: number;
  /** 购买数量 */
  count: number;
}

/**  */
export interface GoodsRedeemResponse {
  /** 兑换情况 */
  redeemMessage:
    | GoodsRedeemMessage
    | undefined;
  /** 兑换后得到的道具 */
  rewardList: number[];
}

function createBaseGoodsAddRequest(): GoodsAddRequest {
  return { resId: 0, num: 0 };
}

export const GoodsAddRequest: MessageFns<GoodsAddRequest> = {
  encode(message: GoodsAddRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.resId !== 0) {
      writer.uint32(8).int64(message.resId);
    }
    if (message.num !== 0) {
      writer.uint32(16).int32(message.num);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsAddRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsAddRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.resId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.num = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsAddRequest>, I>>(base?: I): GoodsAddRequest {
    return GoodsAddRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsAddRequest>, I>>(object: I): GoodsAddRequest {
    const message = createBaseGoodsAddRequest();
    message.resId = object.resId ?? 0;
    message.num = object.num ?? 0;
    return message;
  },
};

function createBaseGoodsRedeemMessage(): GoodsRedeemMessage {
  return { virtualMoneySet: [], redeemMap: {} };
}

export const GoodsRedeemMessage: MessageFns<GoodsRedeemMessage> = {
  encode(message: GoodsRedeemMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.virtualMoneySet) {
      writer.int64(v);
    }
    writer.join();
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      GoodsRedeemMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsRedeemMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsRedeemMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.virtualMoneySet.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.virtualMoneySet.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = GoodsRedeemMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.redeemMap[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsRedeemMessage>, I>>(base?: I): GoodsRedeemMessage {
    return GoodsRedeemMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsRedeemMessage>, I>>(object: I): GoodsRedeemMessage {
    const message = createBaseGoodsRedeemMessage();
    message.virtualMoneySet = object.virtualMoneySet?.map((e) => e) || [];
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseGoodsRedeemMessage_RedeemMapEntry(): GoodsRedeemMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsRedeemMessage_RedeemMapEntry: MessageFns<GoodsRedeemMessage_RedeemMapEntry> = {
  encode(message: GoodsRedeemMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsRedeemMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsRedeemMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsRedeemMessage_RedeemMapEntry>, I>>(
    base?: I,
  ): GoodsRedeemMessage_RedeemMapEntry {
    return GoodsRedeemMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsRedeemMessage_RedeemMapEntry>, I>>(
    object: I,
  ): GoodsRedeemMessage_RedeemMapEntry {
    const message = createBaseGoodsRedeemMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsRedeemRequest(): GoodsRedeemRequest {
  return { redeemId: 0, count: 0 };
}

export const GoodsRedeemRequest: MessageFns<GoodsRedeemRequest> = {
  encode(message: GoodsRedeemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.redeemId !== 0) {
      writer.uint32(8).int64(message.redeemId);
    }
    if (message.count !== 0) {
      writer.uint32(16).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsRedeemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsRedeemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.redeemId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsRedeemRequest>, I>>(base?: I): GoodsRedeemRequest {
    return GoodsRedeemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsRedeemRequest>, I>>(object: I): GoodsRedeemRequest {
    const message = createBaseGoodsRedeemRequest();
    message.redeemId = object.redeemId ?? 0;
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseGoodsRedeemResponse(): GoodsRedeemResponse {
  return { redeemMessage: undefined, rewardList: [] };
}

export const GoodsRedeemResponse: MessageFns<GoodsRedeemResponse> = {
  encode(message: GoodsRedeemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.redeemMessage !== undefined) {
      GoodsRedeemMessage.encode(message.redeemMessage, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsRedeemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsRedeemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.redeemMessage = GoodsRedeemMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsRedeemResponse>, I>>(base?: I): GoodsRedeemResponse {
    return GoodsRedeemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsRedeemResponse>, I>>(object: I): GoodsRedeemResponse {
    const message = createBaseGoodsRedeemResponse();
    message.redeemMessage = (object.redeemMessage !== undefined && object.redeemMessage !== null)
      ? GoodsRedeemMessage.fromPartial(object.redeemMessage)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
