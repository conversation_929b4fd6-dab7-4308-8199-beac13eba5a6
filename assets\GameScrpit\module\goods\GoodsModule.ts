import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import GoodsApi from "./GoodsApi";
import { GoodsData } from "./GoodsData";
import { GoodsRoute } from "./GoodsRoute";
import { GoodsService } from "./GoodsService";
import { GoodsSubscriber } from "./GoodsSubscriber";

export class GoodsModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): GoodsModule {
    if (!GameData.instance.GoodsModule) {
      GameData.instance.GoodsModule = new GoodsModule();
    }
    return GameData.instance.GoodsModule;
  }
  private _data = new GoodsData();
  private _api = new GoodsApi();
  private _service = new GoodsService();
  private _subscriber = new GoodsSubscriber();
  private _route = new GoodsRoute();
  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new GoodsData();
    this._api = new GoodsApi();
    this._service = new GoodsService();
    this._subscriber = new GoodsSubscriber();
    this._route = new GoodsRoute();

    // 初始化模块
    this._subscriber.register();

    this._route.init();
    completedCallback && completedCallback();
  }
}
