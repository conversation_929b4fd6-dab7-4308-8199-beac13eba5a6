export enum CityRouteName {
  UIGameMap = "UIGameMap",
  UICityFight = "UICityFight",
  UICityBuild = "UICityBuild",
  UICityBuildRes = "UICityBuildRes",
  UICityDetail = "UICityDetail",
  UICityLvReward = "UICityReward",
  UICityReel = "UICityReel",
  UIEnergyUpgrade = "UIEnergyUpgrade",
  UICityHeroInfo = "UICityHeroInfo",
  UIWorldPreview = "UIWorldPreview",
  UIWorldPreviewDetail = "UIWorldPreviewDetail",
  UIWorldPreviewCity = "UIWorldPreviewCity",

  UIWorldPreviewTrim = "UIWorldPreviewTrim",
  UIWorldPreviewAward = "UIWorldPreviewAward",

  UISanJieXiaoJia = "UISanJieXiaoJia",
  UISanJieXiaoJiaOpen = "UISanJieXiaoJiaOpen",
  UISanJieXiaoJiaLvUp = "UISanJieXiaoJiaLvUp",
  UISanJieXiaoJiaCj = "UISanJieXiaoJiaCj",
  UISanJieXiaoJiaHero = "UISanJieXiaoJiaHero",
  UIHaoZhaoBox = "UIHaoZhaoBox",

  TopCityBuild = "TopCityBuild",
}

export enum typeMap {
  color_1 = "人族",
  color_2 = "神族",
  color_3 = "妖族",
  color_4 = "冥族",
  color_5 = "巫族",
}

export enum typeIconMap {
  color_1 = "icon_judianjineng_2",
  color_2 = "icon_judianjineng_4",
  color_3 = "icon_judianjineng_3",
  color_4 = "icon_judianjineng_5",
  color_5 = "icon_judianjineng_1",
}

// c_buildWorkerReward(建筑伙计成就)
export interface ConfigBuildWorkerReward {
  id: number;
  workerNum: number;
  attrAdd: number[][];
  des: string;
}

export interface ConfigBuildRecord {
  id: number;
  /**建筑名称 */
  name: string;
  /**产金要求 */
  goldSpeed: string;
  /**关卡通关开启 */
  unlockLv: number;
  /**建造消耗金币 */
  buildCost: number;
  /**每员工每秒产金数 */
  goldProduce: number;
  /**描述 */
  des: string;
  /**类型 */
  type: number;
  /**美术ICON */
  icon: number;
  /**建筑皮肤解锁等级 */
  unlockLvList: number[];
  /**入住战将ID */
  heroIdList: number[];
  /**据点通关奖励宝箱 */
  rewardList: number[][];
  /**最终建筑ID */
  lastId: number;
  /**建筑初始伙计数量 */
  workerFirst: number;
  /**入住战将上限 */
  workerMax: number;
}

// c_buildCrystal(种族水晶)
export interface ConfigBuildCrystalRecord {
  id: number;
  /**建筑等级 */
  level: number;
  /**消耗(物品数量） */
  cost: number;
  /**商铺加成倍数(万分比） */
  rateAdd: number;
  /**每秒自动点击次数 */
  time: number;
  /**点击收益 */
  reward: number;
  /**升级消耗道具ID */
  cfg: number;
  /**建筑最大等级 */
  lvMax: number;
  /**水晶皮肤解锁等级 */
  unlockLvList: number[];
}

export const CityAudioName = {
  Effect: {
    点击建筑图标: 1501,
    升级成功: 1502,
    号召成功: 1503,
    点击镇守战将卡牌: 1504,
    点击预览图标: 1505,
    未解锁点击前往挑战: 1506,
    解锁成功: 1507,
  },
  Sound: {},
};

export const NvWaAudioName = {
  Effect: {
    点击女娲: 1621,
  },
  Sound: {},
};

export enum OtherBuildIdEnum {
  弟子 = 10001,
  福地 = 10002,
  灵兽 = 10003,
}
