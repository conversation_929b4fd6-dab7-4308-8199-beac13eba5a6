import { _decorator, Component, EditBox, Label, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ClubModule } from "../../../module/club/ClubModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { FontColor } from "../../common/FmConstant";
import Formate from "../../../lib/utils/Formate";
import TipMgr from "../../../lib/tips/TipMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ItemCost } from "../../common/ItemCost";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Wed Sep 18 2024 10:51:42 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubNameFix.ts
 *
 */

@ccclass("UIClubNameFix")
export class UIClubNameFix extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubNameFix`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.getNode("lbl_club_name").getComponent(Label).string = `${ClubModule.data.clubMessage.name}`;
    let cost = ClubModule.config.getClubNameCost();
    this.getNode("ItemCost").getComponent(ItemCost).setItemId(cost[0], cost[1]);
  }

  private on_click_btn_modify() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let newName = this.getNode("edit_new_club_name").getComponent(EditBox).string;
    if (!newName) {
      TipMgr.showTip("新名字不能为空");
      return;
    }
    if (!this.getNode("ItemCost").getComponent(ItemCost).isEnough()) {
      TipMgr.showTip("仙玉不足");
      return;
    }
    ClubModule.api.modifyName(
      newName,
      () => {
        UIMgr.instance.back();
      },
      (errorCode: number, msg: string[], data: any): boolean => {
        TipsMgr.showErrX(errorCode, msg);
        this.getNode("edit_new_club_name").getComponent(EditBox).string = "";
        return true;
      }
    );
  }
}
