import { Layers, _decorator, instantiate, isValid } from "cc";
import ManagerSection from "./ManagerSection";
import GameObject, { GO_STATE } from "../../../lib/object/GameObject";
import FightManager from "./FightManager";
import { GOBuff } from "../buff/GOBuff";
import { BuffDetail } from "../FightDefine";
import BEBuff from "../buff/BEBuff";
const { ccclass, property } = _decorator;

@ccclass("BuffSpecialManager")
export class BuffSpecialManager extends ManagerSection {
  public static sectionName(): string {
    return "BuffSpecialManager";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.createRoot("BuffSpecialRoot", FightManager.instance);
    this.ready();
  }

  public onStart(): void {
    super.onStart();
  }

  public async doCallObject(detail: BuffDetail) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    if (isValid(this.root) == false) {
      return;
    }
    let buff = new GOBuff(detail);
    buff.walk((val) => {
      val.layer = this.root.layer;
    });
    this.addChild(buff);

    buff.onInitDetail(detail);
    return buff;
  }
}
