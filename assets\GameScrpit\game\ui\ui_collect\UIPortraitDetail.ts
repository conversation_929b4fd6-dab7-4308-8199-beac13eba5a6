import {
  _decorator,
  color,
  Component,
  Input,
  instantiate,
  isValid,
  Label,
  Node,
  RichText,
  Sprite,
  tween,
  UITransform,
  v2,
  v3,
  Vec3,
  Widget,
} from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import ResMgr from "../../../lib/common/ResMgr";
import ToolExt from "../../common/ToolExt";
import { HeroModule } from "../../../module/hero/HeroModule";
import data from "../../../lib/data/data";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { CollectAudioName } from "../../../module/player/PlayerConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const shopHead = {
  10406: "icon_chiguo",
  10407: "icon_zengzhang",
  10408: "icon_guangmu",
  10409: "icon_duowen",

  10416: "icon_bj",
  10417: "icon_wuk",
  10418: "icon_ss",
  10419: "icon_blm",
  10420: "icon_tsz",
};

const shopTypeLab = {
  7: "师徒一行",
  8: "四大天王",
};

const shopTagSpr = {
  7: "shituyixing",
  8: "sidatianw",
};

@ccclass("UIPortraitDetail")
export class UIPortraitDetail extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COLLECT}?prefab/ui/UIPortraitDetail`;
  }

  private _heroId: number = null;

  private _shopType: number = null;

  private _shopList: Array<any> = [];

  private _curIndex: number = null;

  public init(args: any): void {
    super.init(args);
    this._heroId = args.heroId;
    this._shopType = args.shopType;
  }

  protected onEvtShow(): void {
    let lab = this.getNode("btn_back").getChildByName("Label").getComponent(Label);
    lab.string = shopTypeLab[this._shopType];

    this.getNode("btn_skill_des").active = false;
    this._shopList = this.getTypeShopIdList();
    this.changeHero(0);
  }

  private changeHero(num: number) {
    let index = 0;

    for (let i = 0; i < this._shopList.length; i++) {
      let data = this._shopList[i];
      if (data.itemsList[0][0] == this._heroId) {
        index = i;
        break;
      }
    }

    index += num;
    if (index < 0) {
      index = this._shopList.length - 1;
    }
    if (index >= this._shopList.length) {
      index = 0;
    }
    if (this._curIndex == index) {
      return;
    }
    this._curIndex = index;
    let info = this._shopList[index];
    this._heroId = info.itemsList[0][0];

    this.checkHasHero();
    this.setHeroNameQuality();
    this.setHeroSpineImage();
    this.loadShopHead();
    this.setCointype();
    this.setShopLab();
    this.loadSkill();
    this.setTagSpr();
  }

  private setHeroSpineImage() {
    ResMgr.loadPrefab(
      `${BundleEnum.BUNDLE_COMMON_HERO_FULL}?prefabs/hero_${this._heroId}`,
      (prefab) => {
        if (isValid(this.node) == false) {
          return;
        }
        let heroSpine = this.getNode("heroImgPoint").getChildByName("heroSpine");

        let node: Node = instantiate(prefab);
        this.getNode("heroImgPoint").addChild(node);
        node.walk((child) => (child.layer = this.getNode("heroImgPoint").layer));

        heroSpine.removeFromParent();
        heroSpine.destroy();

        node.name = "heroSpine";

        node.setScale(v3(1.5, 1.5, 1));

        let widget = node.addComponent(Widget);
        widget.isAlignTop = true;
        widget.top = 0;
      },
      this
    );
  }

  private setShopLab() {
    this.getNode("lbl_group_name").getComponent(Label).string = shopTypeLab[this._shopType];
  }

  private setTagSpr() {
    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_G_COLLECT}?images/tag/${shopTagSpr[this._shopType]}`,
      this.getNode("spr_tag").getComponent(Sprite),
      this
    );
  }

  private loadShopHead() {
    //this.getNode("lay_heroHead").destroyAllChildren();
    let c_hero = JsonMgr.instance.jsonList.c_hero;

    let children = this.getNode("lay_heroHead").children;
    let num = this._shopList.length - children.length;

    if (num > 0) {
      for (let i = 0; i < num; i++) {
        let node = ToolExt.clone(this.getNode("Item"), this);
        this.getNode("lay_heroHead").addChild(node);
        node.active = true;
      }
    } else if (num < 0) {
      for (let i = num; i > 0; i--) {
        let node = this.getNode("lay_heroHead").children[0];
        node.removeFromParent();
        node.destroy();
      }
    }

    for (let i = 0; i < this._shopList.length; i++) {
      let data = this._shopList[i];
      let node = this.getNode("lay_heroHead").children[i];

      let spr = shopHead[data.itemsList[0][0]];

      ResMgr.loadImage(
        `${BundleEnum.BUNDLE_G_COLLECT}?images/circle/${spr}`,
        node["btn_shop_heroHead"].getComponent(Sprite),
        this
      );

      FmUtils.setItemNode(node, data.itemsList[0][0], -1);
      let herodb = c_hero[data.itemsList[0][0]];
      node["lbl_heroName"].getComponent(Label).string = herodb.name;
    }
  }

  private loadSkill() {
    //this.getNode("lay_heroSkill").destroyAllChildren();
    let c_hero = JsonMgr.instance.jsonList.c_hero;
    let c_heroSkill = JsonMgr.instance.jsonList.c_heroSkill;
    let db = c_hero[this._heroId];

    let children = this.getNode("lay_heroSkill").children;

    let num = db.lightList.length - children.length;

    if (num > 0) {
      for (let i = 0; i < num; i++) {
        let node = ToolExt.clone(this.getNode("btn_heroskill"), this);
        this.getNode("lay_heroSkill").addChild(node);
        node.active = true;
      }
    } else if (num < 0) {
      for (let i = num; i > 0; i--) {
        let node = this.getNode("lay_heroSkill").children[0];
        node.removeFromParent();
        node.destroy();
      }
    }

    for (let i = 0; i < db.lightList.length; i++) {
      let info = c_heroSkill[db.lightList[i]];
      let node = this.getNode("lay_heroSkill").children[i];
      node["info"] = info;

      ResMgr.loadImage(
        `${BundleEnum.BUNDLE_G_HERO}?atlas_hero_skill/heroskill_${info.iconId}`,
        node.getComponent(Sprite),
        this
      );
    }
  }

  private setHeroNameQuality() {
    HeroModule.service.updateHeroColorBg(this.getNode("quality").getComponent(Sprite), this._heroId);

    let db = JsonMgr.instance.jsonList.c_hero[this._heroId];

    if (!db) {
      return;
    }
    this.getNode("heroName").getComponent(Label).string = db.name;
  }

  private setCointype() {
    let info = null;

    for (let i = 0; i < this._shopList.length; i++) {
      let data = this._shopList[i];
      if (data.itemsList[0][0] == this._heroId) {
        info = data;
        break;
      }
    }

    ToolExt.setItemIcon(this.getNode("itemIcon"), info.cointype, this);

    let item_num = PlayerModule.data.getItemNum(info.cointype);
    if (item_num >= info.coinPrice) {
      this["lbl_coinPrice"].getComponent(Label).color = color().fromHEX("#01b505");
    } else {
      this["lbl_coinPrice"].getComponent(Label).color = color().fromHEX("#e10000");
    }

    this.getNode("lbl_coinPrice").getComponent(Label).string = item_num + "/" + info.coinPrice;
  }

  private checkHasHero() {
    let info = HeroModule.data.getHeroMessage(this._heroId);
    if (info) {
      this.getNode("btn_duihuan").active = false;
      this.getNode("spr_has").active = true;
    } else {
      this.getNode("btn_duihuan").active = true;
      this.getNode("spr_has").active = false;
    }
  }

  private getTypeShopIdList() {
    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let list = Object.keys(c_shop);
    let arr = [];
    for (let i = 0; i < list.length; i++) {
      let data = c_shop[list[i]];
      if (this._shopType == data.type) {
        arr.push(data);
      }
    }
    return arr;
  }

  private on_click_btn_duihuan() {
    AudioMgr.instance.playEffect(CollectAudioName.Effect.点击兑换按钮);
    let info = null;

    for (let i = 0; i < this._shopList.length; i++) {
      let data = this._shopList[i];
      if (data.itemsList[0][0] == this._heroId) {
        info = data;
        break;
      }
    }

    let item_num = PlayerModule.data.getItemNum(info.cointype);

    if (item_num < info.coinPrice) {
      TipMgr.showTip("道具不足");
      return;
    }

    GoodsModule.api.redeemGoods(info.id, 1, (data: number[]) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data });
      this.checkHasHero();
      this.getNode("btn_duihuan").active = false;
      this.getNode("spr_has").active = true;
    });
  }

  private on_click_btn_xiangxixinxi() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog("UIPortraitIntroduce", { heroId: this._heroId, keepMask: true });
  }

  private on_click_btn_right() {
    AudioMgr.instance.playEffect(1226);
    this.changeHero(1);
  }

  private on_click_btn_left() {
    AudioMgr.instance.playEffect(1226);
    this.changeHero(-1);
  }

  private on_click_btn_heroskill(event) {
    AudioMgr.instance.playEffect(CollectAudioName.Effect.点击角色技能);
    let info = event.node["info"];

    let btn_skill_des = ToolExt.clone(this.getNode("btn_skill_des"), this);
    btn_skill_des.on(Input.EventType.TOUCH_END, this.click_btn_skill_des.bind(this), this);
    this.node.addChild(btn_skill_des);
    btn_skill_des.active = true;

    let db = JsonMgr.instance.jsonList.c_heroSkill[info.id];
    btn_skill_des["lbl_halo_name"].getComponent(Label).string = db.name;

    let lvId = info.id * 10000 + 1;

    let key = skillAttrMapKey.get(lvId);
    let c_heroSkillLv = JsonMgr.instance.jsonList.c_heroSkillLv;
    let powerAdd = c_heroSkillLv[lvId][key] / 100;
    let str: string = db.des;

    if (skillAttrSix.has(lvId)) {
      for (let i = 0; i < 6; i++) {
        str = str.replace("%s", String(powerAdd));
      }
    } else {
      for (let i = 0; i < 3; i++) {
        str = str.replace("%s", String(powerAdd));
      }
    }
    btn_skill_des["lbl_halo_add"].getComponent(RichText).string = str;

    // btn_skill_des["lbl_halo_add"].getComponent(RichText).string = this.onDefaultLanguage(
    //   btn_skill_des["lbl_halo_add"].getComponent(RichText),
    //   powerAdd
    // );
  }

  //判断label是否有直接使用language
  private onDefaultLanguage(label: RichText, powerAdd) {
    label.string = label.string.replace("%s", powerAdd);

    let index = label.string.search("%s");
    log.log(index);
    if (index != -1) {
      this.onDefaultLanguage(label, powerAdd);
    }
  }

  private click_btn_skill_des(event) {
    let node = event.target;
    node.off(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.click_btn_skill_des.bind(this)), this);

    node.destroy();
  }

  private on_click_btn_back() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}

const skillAttrMapKey: Map<number, string> = new Map([
  [31000001, "powerAdd2"],
  [31000002, "powerAdd2"],
  [31000003, "powerAdd2"],
  [31000004, "powerAdd2"],
  [31000005, "powerAdd2"],
  [31000006, "powerAdd2"],
  [31000007, "powerAdd2"],
  [31000008, "powerAdd2"],
  [31000009, "powerAdd2"],
  [31000010, "powerAdd2"],

  [21010001, "powerAdd2"],
  [21010002, "powerAdd2"],
  [21010003, "powerAdd2"],
  [21010004, "powerAdd2"],
  [21010005, "powerAdd2"],

  [21020001, "powerAdd2"],
  [21020002, "powerAdd2"],
  [21020003, "powerAdd2"],
  [21020004, "powerAdd2"],
  [21020005, "powerAdd2"],
  [21020006, "powerAdd2"],
  [21020007, "powerAdd2"],
  [21020008, "powerAdd2"],
  [21020009, "powerAdd2"],
  [21020010, "powerAdd2"],

  [21030001, "heroPowerAdd1"],
  [21030002, "heroPowerAdd1"],
  [21030003, "heroPowerAdd1"],
  [21030004, "heroPowerAdd1"],
  [21030005, "heroPowerAdd1"],
  [21030006, "heroPowerAdd1"],
  [21030007, "heroPowerAdd1"],
  [21030008, "heroPowerAdd1"],
  [21030009, "heroPowerAdd1"],
  [21030010, "heroPowerAdd1"],

  [22010001, "powerAdd2"],
  [22010002, "powerAdd2"],
  [22010003, "powerAdd2"],
  [22010004, "powerAdd2"],
  [22010005, "powerAdd2"],

  [22020001, "powerAdd2"],
  [22020002, "powerAdd2"],
  [22020003, "powerAdd2"],
  [22020004, "powerAdd2"],
  [22020005, "powerAdd2"],
  [22020006, "powerAdd2"],
  [22020007, "powerAdd2"],
  [22020008, "powerAdd2"],
  [22020009, "powerAdd2"],
  [22020010, "powerAdd2"],

  [22030001, "heroPowerAdd2"],
  [22030002, "heroPowerAdd2"],
  [22030003, "heroPowerAdd2"],
  [22030004, "heroPowerAdd2"],
  [22030005, "heroPowerAdd2"],
  [22030006, "heroPowerAdd2"],
  [22030007, "heroPowerAdd2"],
  [22030008, "heroPowerAdd2"],
  [22030009, "heroPowerAdd2"],
  [22030010, "heroPowerAdd2"],
]);

const skillAttrSix: Map<number, boolean> = new Map([
  [21010001, true],
  [21010002, true],
  [21010003, true],
  [21010004, true],
  [21010005, true],

  [22010001, true],
  [22010002, true],
  [22010003, true],
  [22010004, true],
  [22010005, true],
]);
