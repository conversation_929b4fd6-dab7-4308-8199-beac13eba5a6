import { _decorator, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { JsonMgr } from "../mgr/JsonMgr";
import { Label } from "cc";
import { PlayerModule } from "../../module/player/PlayerModule";
import ToolExt from "../common/ToolExt";
import { instantiate } from "cc";
import Formate from "../../lib/utils/Formate";
import { UIMgr } from "../../lib/ui/UIMgr";
import { CityModule } from "../../module/city/CityModule";
import { PlayerRouteName } from "../../module/player/PlayerConstant";
import { NodeTool } from "../../lib/utils/NodeTool";
import { TipsMgr } from "../../../platform/src/TipsHelper";
import { BoutStartUp } from "../BoutStartUp";
import { GameDirector } from "../GameDirector";
import { ClubModule } from "../../module/club/ClubModule";
import MsgMgr from "../../lib/event/MsgMgr";
import MsgEnum from "../event/MsgEnum";
import { ItemEnum } from "../../lib/common/ItemEnum";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import GuideMgr from "../../ext_guide/GuideMgr";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * hopewsw
 * Sun Sep 22 2024 22:39:49 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/UIItemFetch.ts
 *
 */
@ccclass("UIItemFetch")
export class UIItemFetch extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UIItemFetch`;
  }

  itemId: number;
  needNum: number;

  //=================================================
  public init(args: any): void {
    super.init(args);
    this.itemId = args.itemId;
    this.needNum = args.needNum;
  }

  protected async onEvtShow(): Promise<void> {
    TipsMgr.setEnableTouch(false, 0.3);

    if (this.itemId == ItemEnum.气运_1) {
      UIMgr.instance.closeByName(PlayerRouteName.UIItemFetch);
      UIMgr.instance.showDialog(PlayerRouteName.UIQiYunNo);
      TipsMgr.setEnableTouch(true);
      return;
    }

    let configItem = JsonMgr.instance.getConfigItem(this.itemId);
    log.log("二狗你要的物品信息在这里====", configItem);

    // 道具名称
    this.node.getChildByPath("layout/node_head/lbl_item_name").getComponent(Label).string = configItem.name;

    // 已拥有的道具数量
    let numInfo = "拥有：" + Formate.format(PlayerModule.data.getItemNum(this.itemId));
    if (this.needNum > 0) {
      numInfo += "/" + Formate.format(this.needNum);
    }
    this.node.getChildByPath("layout/node_head/lbl_item_num").getComponent(Label).string = numInfo;

    // 物品描述
    this.getNode("lbl_desc").getComponent(Label).string = configItem.des;

    // 背景品质图片
    ToolExt.setItemBg(this.getNode("bg_color"), configItem.color);

    // 道具图标
    ToolExt.setItemIcon(this.getNode("bg_icon"), configItem.id);

    // 设置获取路径
    const layoutPath = this.getNode("layout_path");
    layoutPath.children.forEach((node) => (node.active = false));

    if (configItem.jumplist.length > 0) {
      for (let idx = 0; idx < configItem.jumplist.length; idx++) {
        // 获取跳转配置
        let cfgJump = JsonMgr.instance.getConfigJump(configItem.jumplist[idx]);

        // 系统未开启不展示
        if (cfgJump.systemOpenId && !GameDirector.instance.isSystemOpen(cfgJump.systemOpenId)) {
          continue;
        }

        // 道具数量不足的道具使用不展示
        if (cfgJump.pageName == PlayerRouteName.UIItemUse) {
          if (PlayerModule.data.getItemNum(Number(cfgJump.args)) < 1) {
            continue;
          }
        }

        let nodeJumpPath: Node;
        if (idx < layoutPath.children.length) {
          nodeJumpPath = layoutPath.children[idx];
        } else {
          nodeJumpPath = instantiate(layoutPath.children[0]);
          layoutPath.addChild(nodeJumpPath);
        }
        nodeJumpPath.active = true;

        // 设置跳转名称
        nodeJumpPath.getChildByName("lbl_title").getComponent(Label).string = cfgJump.des;

        let lblSubDesc = nodeJumpPath.getChildByName("lbl_sub_desc").getComponent(Label);
        let lblSubTitle = nodeJumpPath.getChildByName("lbl_sub_title").getComponent(Label);
        lblSubDesc.node.active = false;
        lblSubTitle.node.active = false;

        if (cfgJump.pageName == PlayerRouteName.UIItemUse) {
          lblSubDesc.node.active = true;
          lblSubTitle.node.active = true;
          lblSubDesc.string = "拥有:" + PlayerModule.data.getItemNum(Number(cfgJump.args));
        }

        // 模拟点击事件
        function clickNode(nodePageName: string, nodeClickName: string) {
          if (nodeClickName) {
            let nodeParent = NodeTool.findByName(BoutStartUp.instance.gameRoot, nodePageName);
            if (!nodeParent) {
              nodeParent = NodeTool.findByName(BoutStartUp.instance.uiRoot, nodePageName);
            }
            let nodeClick = NodeTool.findByName(nodeParent, nodeClickName);
            NodeTool.fakeClick(nodeClick);
          }
        }

        // 绑定事件
        nodeJumpPath.on(Node.EventType.TOUCH_END, () => {
          // 关闭itemFetch
          UIMgr.instance.back();

          // 弱提示类型
          if (cfgJump.pageName == "tips") {
            TipsMgr.showTip(cfgJump.args);
          } else if (cfgJump.type > 0) {
            // 主地图回到正常模式
            MsgMgr.emit(MsgEnum.ON_CITY_UN_FOCOUS);

            let args: any = {};
            if (cfgJump.id == 71) {
              args.buildId = CityModule.service.findMinCostLevelUpCityId();
            } else if (cfgJump.id == 72) {
              args.buildId = CityModule.service.findMinCostCallCityId();
            }
            GuideMgr.startGuide({ stepId: cfgJump.type, args: args });
          } else if (cfgJump.pageName == PlayerRouteName.UIItemUse) {
            UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: Number(cfgJump.args) });
          } else if (cfgJump.pageName == PlayerRouteName.UIItemJoinPop) {
            UIMgr.instance.showDialog(PlayerRouteName.UIItemJoinPop, { itemId: Number(cfgJump.args) });
          } else if (cfgJump.pageName) {
            // 主地图回到正常模式
            MsgMgr.emit(MsgEnum.ON_CITY_UN_FOCOUS);

            // 战盟商店
            if (cfgJump.id == 49) {
              if (!ClubModule.data.clubMessage) {
                TipsMgr.showTip("请先创建或加入战盟");
                return;
              }
            }

            UIMgr.instance.showDialog(cfgJump.pageName, {}, null, () => {
              clickNode(cfgJump.pageName, cfgJump.nodeClick);
            });
          }
        });
      }
    } else {
      layoutPath.children[0].getComponentInChildren(Label).string = "暂无路径";
      layoutPath.children[0].active = true;

      layoutPath.children[0].getChildByName("lbl_sub_desc").active = false;
      layoutPath.children[0].getChildByName("lbl_sub_title").active = false;
    }
  }
}
