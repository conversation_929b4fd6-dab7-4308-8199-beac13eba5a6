{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "d52b25e7-9a70-4e2b-b885-917b3585f6a5", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "d52b25e7-9a70-4e2b-b885-917b3585f6a5@6c48a", "displayName": "CJ_bg_touxiang_xuanzhong", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "d52b25e7-9a70-4e2b-b885-917b3585f6a5", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "d52b25e7-9a70-4e2b-b885-917b3585f6a5@f9941", "displayName": "CJ_bg_touxiang_xuanzhong", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 200, "height": 200, "rawWidth": 200, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-100, -100, 0, 100, -100, 0, -100, 100, 0, 100, 100, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 200, 200, 200, 0, 0, 200, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-100, -100, 0], "maxPos": [100, 100, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "d52b25e7-9a70-4e2b-b885-917b3585f6a5@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "d52b25e7-9a70-4e2b-b885-917b3585f6a5@6c48a"}}