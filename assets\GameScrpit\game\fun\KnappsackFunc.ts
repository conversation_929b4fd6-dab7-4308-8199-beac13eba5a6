import { _decorator, Component, Node } from "cc";
import Func from "./Func";
import { UIMgr } from "../../lib/ui/UIMgr";
import { UIKnappsack } from "../ui/ui_knappsack/UIKnappsack";
const { ccclass, property } = _decorator;

@ccclass("KnappsackFunc")
export class KnappsackFunc extends Func {
  public condition(id: number): boolean {
    return true;
  }
  public showUI(args?: any) {
    return UIMgr.instance.showDialog("UIKnappsack", args);
  }

  public tick(dt: any): void {}

  public funcName() {
    return "KnappsackFunc";
  }
  public uiName() {
    return new UIKnappsack().name;
  }
}
