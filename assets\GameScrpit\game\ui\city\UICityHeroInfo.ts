import { _decorator, Label } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UICityHeroInfo")
export class UICityHeroInfo extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UICityHeroInfo`;
  }

  private _card_data;

  public init(args: any): void {
    super.init(args);
    // log.log("点击的战将卡牌数据====", args.card_data);
    this._card_data = args.card_data;
  }

  protected onEvtShow(): void {
    let info = JsonMgr.instance.jsonList.c_hero[this._card_data.id];
    this.getNode("lbl_name").getComponent(Label).string = info.name;

    if (this._card_data.has == false) {
      this.getNode("has").active = false;
      this.getNode("no_has").active = true;
    } else {
      this.getNode("has").active = true;
      this.getNode("no_has").active = false;
    }
  }

  on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
