import { Vec3, Node } from "cc";

export enum RunState {
  TALK_END, // 对话结束 回调例：RunStateCallback(TALK_END)
  GO_FIGHT, // 前往战斗 回调例：RunStateCallback(GO_FIGHT)
  ROLE_MOVE, // 主角移动 回调例：RunStateCallback(ROLE_MOVE, 500)
  ACT_END, // 动作结束 冲击小怪结束，BOSS进行动作播放结束，主角走路进场结束 回调例：RunStateCallback(ACT_END)
}
// 角色类型
export enum XiaoyouxiRoleType {
  ROLE_TYPE_LEAD, // 主角
  ROLE_TYPE_MONSTER, // 小怪
  ROLE_TYPE_BOSS, // BOSS
  ROLE_TYPE_DAJI, // 旁白
}
// 角色行为
export enum XiaoyouxiRoleBehavior {
  ROLE_IN, // 角色入场，角色主动移动到屏幕内
  ROLE_APPEAR, // 角色出现并站在原地
  ROLE_RUN, // 角色冲锋
  ROLE_UPGRADE, // 角色升级
  ROLE_DISAPPEAR, // 角色消失
  ROLE_OUT, // 角色出场
  ROLE_TALK, // 角色说话
  ROLE_UI, // 展示角色UI
}
type RunStateCallback = (state: RunState, val?: any) => void;
export interface IRunModule {
  /**
   * 初始化模块的时候调用
   * @param callback 状态变更回调
   */
  onStart(callback: RunStateCallback, distance: number): void;

  /**
   *
   * @param type 角色类型
   * @param behavior 角色行为
   * @param val 行为参数 ROLE_UPGRADE的时候val为number，ROLE_TALK的时候val为string
   */
  roleDo(type: XiaoyouxiRoleType, behavior: XiaoyouxiRoleBehavior, val?: any): void;

  // 获取小怪位置
  getMonsterPosition(): Vec3;

  // 获取BOSS位置
  getBossPosition(): Vec3;

  // 获取主角位置
  getLeaderPosition(): Vec3;

  // 获取主角节点
  getLeaderNode(): Node;

  // 获取怪物节点
  getMonster01Node(): Node;

  /**
   *
   * @param dt
   * @param cameraMove 相机移动的距离
   */
  updateFrame(dt: number, cameraMove: number): void;

  /**
   *
   * @param node 相机节点,用于同步背景
   */
  setNodeCamera(node: Node): void;
}
