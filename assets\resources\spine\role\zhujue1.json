{"skeleton": {"hash": "JgeUTNOQYWIYfrBwWAZeoIJ26vI=", "spine": "3.8.75", "x": -72.29, "y": -11.17, "width": 135.18, "height": 363.71, "images": "./images/", "audio": "D:/spine导出/主角换皮动画/主角/主角1"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": -21.44}, {"name": "bone2", "parent": "bone", "length": 75.15, "rotation": 0.7, "x": 0.97, "y": 174.93, "color": "afff00ff"}, {"name": "bone3", "parent": "bone2", "length": 42.77, "rotation": 97.43, "x": 2.26, "y": 4.99}, {"name": "bone4", "parent": "bone3", "length": 34.8, "rotation": 0.62, "x": 42.77}, {"name": "bone5", "parent": "bone4", "length": 10.59, "rotation": -6.7, "x": 34.8}, {"name": "bone6", "parent": "bone5", "x": 27.33, "y": -15.43}, {"name": "bone7", "parent": "bone6", "x": 62.45, "y": 40.12}, {"name": "bone13", "parent": "bone6", "x": 27.12, "y": -3.13}, {"name": "bone14", "parent": "bone13", "length": 11.27, "rotation": 59.56, "x": 2.51, "y": 5.92}, {"name": "bone15", "parent": "bone14", "length": 14.53, "rotation": 58.27, "x": 11.84, "y": 3.97}, {"name": "bone16", "parent": "bone15", "length": 20.24, "rotation": 53.28, "x": 14.53}, {"name": "bone17", "parent": "bone16", "length": 19.08, "rotation": 2.81, "x": 20.24}, {"name": "bone18", "parent": "bone13", "length": 10.35, "rotation": -70.79, "x": 1.72, "y": -4.35}, {"name": "bone19", "parent": "bone18", "length": 17.62, "rotation": -78.04, "x": 10.35}, {"name": "bone20", "parent": "bone19", "length": 19.37, "rotation": -18.79, "x": 18.81, "y": -0.14}, {"name": "bone21", "parent": "bone20", "length": 14.63, "rotation": -22.85, "x": 19.37}, {"name": "bone31", "parent": "bone4", "x": 21.73, "y": 31.05}, {"name": "bone32", "parent": "bone4", "x": 14.46, "y": -24.82}, {"name": "bone33", "parent": "bone32", "length": 35.85, "rotation": -173.49, "x": -8.17, "y": -1.39}, {"name": "bone34", "parent": "bone33", "length": 35.66, "rotation": -2.95, "x": 35.85}, {"name": "bone35", "parent": "bone34", "length": 15.47, "rotation": -1.56, "x": 35.66}, {"name": "bone36", "parent": "bone31", "length": 37.62, "rotation": 153.83, "x": -1.83, "y": 1.87}, {"name": "bone37", "parent": "bone36", "length": 44.81, "rotation": 18.1, "x": 37.62}, {"name": "bone38", "parent": "bone37", "length": 16.25, "rotation": 1.18, "x": 44.81}, {"name": "bone39", "parent": "bone2", "x": 0.12, "y": -4.55, "color": "afff00ff"}, {"name": "bone44", "parent": "bone39", "length": 30, "rotation": -87.8, "x": 21.81, "y": -3.38, "scaleX": 0.7737, "color": "52f94bff"}, {"name": "bone45", "parent": "bone44", "length": 30, "rotation": 0.13, "x": 30.5, "y": -0.01, "scaleX": 0.7737, "color": "52f94bff"}, {"name": "bone46", "parent": "bone45", "length": 30, "rotation": 3.38, "x": 31.4, "y": -0.21, "scaleX": 0.7737, "color": "52f94bff"}, {"name": "bone49", "parent": "bone39", "length": 30, "rotation": -90.2, "x": -23.65, "y": -1.9, "scaleX": 0.7737, "color": "52f94bff"}, {"name": "bone50", "parent": "bone49", "length": 30, "rotation": 1.5, "x": 31.44, "y": -0.45, "scaleX": 0.7737, "color": "52f94bff"}, {"name": "bone51", "parent": "bone50", "length": 30, "rotation": -6.31, "x": 30.52, "y": -0.37, "scaleX": 0.7737, "color": "52f94bff"}, {"name": "bone52", "parent": "bone2", "length": 73.79, "rotation": -86.53, "x": -17.36, "y": -3.86}, {"name": "bone53", "parent": "bone52", "length": 56.02, "rotation": -16.35, "x": 76.15, "y": -0.28}, {"name": "bone54", "parent": "bone53", "length": 30, "rotation": 44.85, "x": 55.59, "y": -0.36}, {"name": "bone56", "parent": "bone2", "length": 76.08, "rotation": -80.93, "x": 10.47, "y": -3.6}, {"name": "bone57", "parent": "bone56", "length": 55.47, "rotation": -20.31, "x": 77.15, "y": 0.01}, {"name": "bone58", "parent": "bone57", "length": 30, "rotation": 59.13, "x": 55.97, "y": 0.39}, {"name": "bone62", "parent": "bone38", "length": 42.77, "rotation": 69.27, "x": 8.88, "y": 0.27, "scaleX": 1.0173, "scaleY": 0.6852, "skin": true}, {"name": "bone63", "parent": "bone2", "length": 73.29, "rotation": -77.18, "x": -13.46, "y": -4.88}, {"name": "bone64", "parent": "bone63", "length": 63.46, "rotation": -38.8, "x": 73.29, "color": "abe323ff"}, {"name": "bone65", "parent": "bone2", "length": 79.58, "rotation": -71.24, "x": 12.66, "y": -4.43}, {"name": "bone66", "parent": "bone65", "length": 60.14, "rotation": -43.49, "x": 79.58, "color": "abe323ff"}, {"name": "bone55", "parent": "bone", "length": 41.87, "rotation": -0.73, "x": -36.34, "y": 23.68, "color": "39ff00ff"}, {"name": "bone59", "parent": "bone55", "length": 36.88, "rotation": 93.61, "x": -0.78, "y": -0.81, "color": "ff0000ff"}, {"name": "rjio1", "parent": "bone59", "rotation": -92.88, "x": 71.57, "y": -19.42, "color": "ff3f00ff"}, {"name": "rjio2", "parent": "bone59", "rotation": -92.88, "x": 17.55, "y": -3.84, "color": "ff3f00ff"}, {"name": "rjio3", "parent": "rjio2", "x": 14.96, "y": -24.99, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone60", "parent": "bone", "length": 41.51, "x": 14.27, "y": 24.9, "color": "39ff00ff"}, {"name": "bone61", "parent": "bone60", "length": 29.56, "rotation": 91.45, "x": -0.56, "y": 0.37, "color": "ff0000ff"}, {"name": "ljio1", "parent": "bone61", "rotation": -91.45, "x": 69.63, "y": -12.99, "color": "ff3f00ff"}, {"name": "ljio2", "parent": "bone61", "rotation": -91.45, "x": 15.31, "y": -1.32, "color": "ff3f00ff"}, {"name": "ljio3", "parent": "ljio2", "x": 23.19, "y": -20.38, "transform": "noScale", "color": "ff3f00ff"}, {"name": "feigun", "parent": "root", "x": 49.46, "y": 384.69, "scaleX": 1.7354, "scaleY": 1.7354}, {"name": "bone67", "parent": "root", "x": -30.33, "y": -430.18}], "slots": [{"name": "dauo1", "bone": "bone5", "attachment": "dauo2"}, {"name": "dauo2", "bone": "bone5", "color": "dbdbdbed", "attachment": "dauo2"}, {"name": "s2", "bone": "bone", "color": "f1f1f1ed", "attachment": "s2"}, {"name": "j2", "bone": "bone", "color": "ffffffed", "attachment": "j2"}, {"name": "j1", "bone": "bone", "attachment": "j1"}, {"name": "yixyiu2", "bone": "bone4", "attachment": "y<PERSON><PERSON><PERSON>"}, {"name": "xiuziz2", "bone": "bone34", "attachment": "xiu<PERSON>z"}, {"name": "body", "bone": "bone", "attachment": "body"}, {"name": "yi2", "bone": "bone", "attachment": "yi2"}, {"name": "yaodai1", "bone": "bone2", "attachment": "yaodai1"}, {"name": "tou", "bone": "bone5", "attachment": "tou"}, {"name": "biyan", "bone": "bone6"}, {"name": "toufa3", "bone": "bone5", "attachment": "toufa3"}, {"name": "er", "bone": "bone5", "attachment": "er"}, {"name": "toufa1", "bone": "bone", "attachment": "toufa1"}, {"name": "mutou", "bone": "bone62", "attachment": "mutou"}, {"name": "s1", "bone": "bone", "attachment": "s1"}, {"name": "mutou2", "bone": "feigun"}, {"name": "xuanz", "bone": "feigun", "color": "ffffff6f", "blend": "additive"}, {"name": "y<PERSON><PERSON><PERSON>", "bone": "bone4", "attachment": "y<PERSON><PERSON><PERSON>"}, {"name": "xiu<PERSON>z", "bone": "bone37", "attachment": "xiu<PERSON>z"}, {"name": "wq", "bone": "bone62", "attachment": "wq"}], "ik": [{"name": "ljio1", "order": 7, "bones": ["bone56"], "target": "ljio1", "compress": true, "stretch": true}, {"name": "ljio2", "order": 8, "bones": ["bone57"], "target": "ljio2", "compress": true, "stretch": true}, {"name": "ljio3", "order": 9, "bones": ["bone58"], "target": "ljio3"}, {"name": "ljio4", "order": 5, "bones": ["bone65", "bone66"], "target": "ljio2", "bendPositive": false}, {"name": "rjio1", "order": 2, "bones": ["bone52"], "target": "rjio1", "compress": true, "stretch": true}, {"name": "rjio2", "order": 3, "bones": ["bone53"], "target": "rjio2", "compress": true, "stretch": true}, {"name": "rjio3", "order": 4, "bones": ["bone54"], "target": "rjio3"}, {"name": "rjio4", "bones": ["bone63", "bone64"], "target": "rjio2", "bendPositive": false}], "transform": [{"name": "ljio5", "order": 6, "bones": ["ljio1"], "target": "bone66", "rotation": 114.4, "x": 6.16, "y": -13.19, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "rjio5", "order": 1, "bones": ["rjio1"], "target": "bone64", "rotation": 114.87, "x": 8.4, "y": -11.37, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}], "skins": [{"name": "default", "attachments": {"mutou2": {"mutou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.4, -51.34, -68.62, 12.5, -48.9, 51.12, 76.12, -12.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 254, "height": 98}}, "j1": {"j1": {"type": "mesh", "uvs": [0.54714, 0.00379, 0.73698, 0.00644, 0.89593, 0.05403, 0.94449, 0.13996, 0.97098, 0.24705, 0.98423, 0.35017, 0.98864, 0.44535, 1, 0.52467, 0.90476, 0.59342, 0.7988, 0.60928, 0.73113, 0.67312, 0.68402, 0.74879, 0.60693, 0.81805, 0.61556, 0.854, 0.70353, 0.891, 0.7852, 0.92674, 0.82709, 0.95433, 0.8229, 0.99071, 0.70353, 1, 0.53807, 1, 0.35587, 0.9951, 0.28047, 0.95998, 0.1946, 0.91169, 0.16738, 0.87783, 0.23021, 0.85525, 0.25115, 0.81511, 0.21764, 0.74174, 0.21136, 0.67966, 0.16528, 0.59751, 0.05009, 0.55173, 0, 0.51661, 0.04172, 0.43195, 0.05639, 0.36059, 0.08642, 0.26053, 0.12397, 0.14585, 0.20658, 0.05029, 0.31546, 0.01094, 0.55482, 0.07294, 0.53248, 0.17496, 0.49339, 0.29202, 0.4878, 0.3957, 0.47105, 0.46092, 0.47105, 0.52949, 0.46546, 0.58467, 0.46546, 0.63317, 0.43754, 0.69839, 0.42078, 0.77866, 0.39844, 0.83552, 0.37052, 0.879, 0.49339, 0.93753, 0.62184, 0.97265], "triangles": [49, 48, 13, 49, 13, 14, 50, 49, 14, 21, 48, 49, 15, 50, 14, 50, 15, 16, 16, 18, 50, 20, 21, 49, 19, 20, 49, 50, 19, 49, 17, 18, 16, 19, 50, 18, 47, 12, 13, 24, 25, 47, 48, 47, 13, 48, 22, 23, 48, 23, 24, 21, 22, 48, 42, 28, 29, 7, 9, 42, 9, 43, 42, 43, 28, 42, 7, 8, 9, 44, 43, 9, 28, 43, 44, 10, 44, 9, 27, 28, 44, 45, 27, 44, 45, 44, 10, 26, 27, 45, 11, 45, 10, 46, 26, 45, 46, 45, 11, 25, 26, 46, 12, 46, 11, 47, 25, 46, 47, 46, 12, 48, 24, 47, 37, 0, 1, 37, 1, 2, 36, 0, 37, 35, 36, 37, 37, 2, 3, 37, 34, 35, 38, 37, 3, 38, 34, 37, 38, 3, 4, 33, 34, 38, 39, 33, 38, 39, 38, 4, 39, 4, 5, 32, 33, 39, 40, 32, 39, 40, 39, 5, 31, 32, 40, 40, 5, 6, 41, 31, 40, 41, 40, 6, 30, 31, 41, 41, 6, 7, 42, 41, 7, 30, 41, 42, 29, 30, 42], "vertices": [1, 32, -15.28, 1.36, 1, 1, 32, -14.15, 11.37, 1, 1, 32, -5.4, 19.2, 1, 1, 32, 9.56, 20.72, 1, 1, 32, 28.09, 20.83, 1, 1, 32, 45.89, 20.28, 1, 2, 32, 62.29, 19.36, 0.96006, 33, -20.52, 14.39, 0.03994, 2, 32, 75.98, 19, 0.57817, 33, -6.54, 18.28, 0.42183, 2, 32, 87.48, 13.14, 0.10809, 33, 7.05, 16.26, 0.89191, 2, 32, 89.83, 7.34, 0.01711, 33, 11.34, 11.48, 0.98289, 1, 33, 23.67, 10.67, 1, 1, 33, 37.81, 11.41, 1, 2, 33, 51.23, 10.35, 0.61689, 34, 4.25, 10.75, 0.38311, 2, 33, 57.52, 12.29, 0.06787, 34, 10.14, 7.81, 0.93213, 1, 34, 18.22, 8.47, 1, 1, 34, 25.94, 8.96, 1, 1, 34, 31.36, 8.34, 1, 1, 34, 36.99, 4.76, 1, 2, 33, 82.34, 22.91, 0.00072, 34, 35.42, -1.68, 0.99928, 2, 33, 84.64, 14.41, 0.03739, 34, 31.2, -9.41, 0.96261, 2, 33, 86.29, 4.84, 0.13512, 34, 25.78, -17.46, 0.86488, 2, 33, 81.07, -0.5, 0.24679, 34, 18.31, -17.71, 0.75321, 2, 33, 73.65, -6.92, 0.50152, 34, 8.51, -17.22, 0.49848, 2, 33, 67.99, -9.74, 0.58302, 34, 2.47, -15.34, 0.41698, 2, 33, 63.09, -7.45, 0.51311, 34, 0.51, -10.3, 0.48689, 1, 34, -5.28, -5.59, 1, 2, 33, 43.02, -12.84, 0.69635, 34, -17.71, -0.32, 0.30365, 3, 32, 99.87, -24.57, 0.01241, 33, 32.03, -15.76, 0.9359, 34, -27.67, 5.17, 0.05169, 3, 32, 85.57, -26.01, 0.21501, 33, 18.02, -21.56, 0.78426, 34, -41.8, 10.66, 0.00073, 2, 32, 77.28, -31.55, 0.47114, 33, 11.45, -29.39, 0.52886, 2, 32, 71.06, -33.78, 0.58696, 33, 5.88, -33.43, 0.41304, 2, 32, 56.63, -30.54, 0.85118, 33, -9.79, -34.82, 0.14882, 2, 32, 44.4, -28.91, 0.97142, 33, -22.72, -37.05, 0.02858, 2, 32, 27.29, -26.11, 0.99999, 33, -40.98, -39.69, 1e-05, 1, 32, 7.68, -22.73, 1, 1, 32, -8.48, -17.21, 1, 1, 32, -14.87, -10.98, 1, 1, 32, -3.35, 0.93, 1, 1, 32, 14.13, -1.49, 1, 1, 32, 34.14, -4.97, 1, 2, 32, 51.97, -6.52, 0.99408, 33, -22.44, -13.42, 0.00592, 2, 32, 63.14, -8.19, 0.93255, 33, -10.57, -11.55, 0.06745, 2, 32, 74.94, -9.02, 0.44283, 33, 1.65, -8.69, 0.55717, 2, 32, 84.42, -9.99, 0.07624, 33, 11.57, -6.67, 0.92377, 3, 32, 92.77, -10.57, 0.01152, 33, 20.22, -4.65, 0.98783, 34, -28.52, 21.36, 0.00065, 3, 32, 103.9, -12.84, 0.00016, 33, 32.24, -3.36, 0.98673, 34, -18.94, 13.98, 0.0131, 2, 33, 46.79, -0.86, 0.95161, 34, -6.71, 5.73, 0.04839, 2, 33, 57.24, 0.37, 0.75544, 34, 1.69, -0.61, 0.24456, 2, 33, 65.38, 0.75, 0.35918, 34, 7.84, -5.96, 0.64082, 2, 33, 74.12, 9.5, 0.0659, 34, 20.2, -5.68, 0.9341, 2, 33, 78.6, 17.57, 0.00722, 34, 29.02, -2.95, 0.99278], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 0, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100], "width": 53, "height": 177}}, "j2": {"j2": {"type": "mesh", "uvs": [0.32681, 0, 0.55252, 0.00562, 0.69102, 0.06166, 0.70128, 0.14973, 0.76283, 0.27782, 0.86542, 0.41232, 0.97315, 0.52119, 0.84491, 0.58524, 0.71666, 0.64608, 0.69102, 0.73735, 0.64143, 0.80688, 0.63304, 0.85664, 0.80361, 0.88718, 1, 0.90988, 1, 0.96225, 0.87361, 1, 0.72304, 0.99926, 0.56926, 0.97526, 0.45393, 0.96626, 0.30015, 0.96726, 0.20084, 0.93026, 0.21045, 0.89226, 0.22006, 0.84326, 0.21685, 0.80926, 0.19763, 0.71226, 0.19443, 0.62526, 0.172, 0.58926, 0.03745, 0.55926, 0, 0.50126, 0.06948, 0.42926, 0.04385, 0.33526, 0.02463, 0.25426, 0.03424, 0.16426, 0.05667, 0.07026, 0.11754, 0.01126, 0.345, 0.07026, 0.34821, 0.15326, 0.38345, 0.25826, 0.41228, 0.33926, 0.44432, 0.42626, 0.43471, 0.51926, 0.42509, 0.62326, 0.42189, 0.71626, 0.42392, 0.65727, 0.40908, 0.77926, 0.41548, 0.85026, 0.41869, 0.90126, 0.59809, 0.92426, 0.83837, 0.94626], "triangles": [18, 46, 47, 18, 19, 46, 19, 20, 46, 20, 21, 46, 47, 46, 11, 21, 45, 46, 46, 45, 11, 21, 22, 45, 11, 45, 10, 16, 48, 15, 15, 48, 14, 48, 16, 47, 16, 17, 47, 17, 18, 47, 48, 13, 14, 47, 12, 48, 48, 12, 13, 47, 11, 12, 26, 27, 40, 41, 26, 40, 45, 23, 44, 45, 44, 10, 45, 22, 23, 23, 24, 44, 10, 44, 9, 44, 42, 9, 44, 24, 42, 9, 42, 8, 8, 42, 43, 42, 24, 43, 24, 25, 43, 43, 41, 8, 43, 25, 41, 8, 41, 7, 25, 26, 41, 41, 40, 7, 7, 40, 6, 27, 28, 40, 5, 40, 39, 40, 5, 6, 28, 29, 40, 40, 29, 39, 29, 38, 39, 29, 30, 38, 39, 38, 5, 38, 4, 5, 30, 37, 38, 38, 37, 4, 30, 31, 37, 37, 3, 4, 31, 32, 37, 32, 36, 37, 37, 36, 3, 32, 33, 36, 33, 35, 36, 36, 35, 3, 35, 2, 3, 33, 34, 35, 34, 0, 35, 35, 1, 2, 35, 0, 1], "vertices": [1, 35, -15.28, 2.85, 1, 1, 35, -12.26, 14.69, 1, 1, 35, -1.6, 20.37, 1, 1, 35, 13.24, 18.27, 1, 1, 35, 35.25, 17.7, 1, 2, 35, 58.71, 19.12, 0.97631, 36, -24.92, 11.05, 0.02369, 2, 35, 77.93, 21.58, 0.55594, 36, -7.38, 20.36, 0.44406, 2, 35, 87.47, 12.83, 0.07932, 36, 5.09, 15.7, 0.92068, 1, 36, 17, 10.94, 1, 2, 36, 33.01, 12.62, 0.97615, 37, -0.78, 26, 0.02385, 2, 36, 45.54, 12.3, 0.62378, 37, 5.16, 14.97, 0.37622, 2, 36, 54.2, 13.51, 0.07064, 37, 10.51, 8.04, 0.92936, 1, 37, 20.88, 10.11, 1, 1, 37, 31.4, 14.15, 1, 1, 37, 37.38, 7.18, 1, 1, 37, 36.59, -2.39, 1, 1, 37, 30.43, -7.7, 1, 1, 37, 21.48, -10.03, 1, 1, 37, 15.8, -12.98, 1, 1, 37, 9.71, -18.64, 1, 2, 36, 71.72, -6.9, 0.01113, 37, 1.48, -17.29, 0.98887, 2, 36, 65.06, -7.65, 0.14508, 37, -2.48, -11.89, 0.85492, 2, 36, 56.52, -8.77, 0.78619, 37, -7.69, -5.02, 0.21381, 2, 36, 50.7, -10.07, 0.99363, 37, -11.7, -0.61, 0.00637, 2, 35, 102.78, -25.4, 0.00844, 36, 34.21, -14.3, 0.99156, 1, 36, 19.26, -17.36, 1, 2, 35, 81.95, -23.07, 0.17912, 36, 13.31, -19.74, 0.82088, 2, 35, 75.69, -29.33, 0.12936, 36, 9.65, -27.85, 0.87064, 2, 35, 65.63, -29.58, 0.66095, 36, 0.09, -31.75, 0.33905, 2, 35, 54.22, -23.72, 0.8071, 36, -13.09, -30.47, 0.1929, 1, 35, 38.24, -22.26, 1, 1, 35, 24.51, -20.85, 1, 1, 35, 9.53, -17.64, 1, 1, 35, -6, -13.62, 1, 1, 35, -15.32, -8.61, 1, 1, 35, -3.35, 1.71, 1, 1, 35, 10.58, -0.61, 1, 1, 35, 28.48, -1.89, 1, 1, 35, 42.31, -2.79, 1, 1, 35, 57.17, -3.7, 1, 2, 35, 72.65, -7, 0.26755, 36, -1.68, -8.17, 0.73245, 1, 36, 16.34, -5.23, 1, 2, 35, 105.51, -13.6, 0.00048, 36, 32.39, -2.31, 0.99952, 2, 35, 95.66, -11.72, 0.01947, 36, 22.21, -4.16, 0.98053, 1, 36, 43.38, -0.9, 1, 2, 36, 55.54, 1.79, 0.0033, 37, 1, 1.07, 0.9967, 1, 37, 6.95, -5.6, 1, 1, 37, 16.82, -2.21, 1, 1, 37, 29.03, 3.5, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 0, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 86, 86, 84, 84, 88, 88, 90, 90, 92, 92, 94, 94, 96], "width": 54, "height": 173}}, "xiuziz2": {"xiuziz": {"type": "mesh", "uvs": [1, 0.48424, 1, 1, 0.69887, 1, 0, 1, 0, 0.55898, 0, 0, 0.52841, 0, 1, 0, 0.55825, 0.51171], "triangles": [8, 6, 7, 0, 8, 7, 8, 4, 5, 8, 5, 6, 3, 4, 8, 2, 8, 0, 3, 8, 2, 2, 0, 1], "vertices": [31.46, -6.99, 45.64, -9.37, 43.8, 2.74, 40.42, 15.18, 29, 10.06, 9.04, 15.47, 9.61, 8.38, 11.61, -10.94, 30.28, 3.72], "hull": 8, "edges": [2, 0, 0, 14, 6, 8, 8, 10, 10, 12, 12, 14, 2, 4, 4, 6], "width": 31, "height": 29}}, "yaodai1": {"yaodai1": {"type": "mesh", "uvs": [0.0197, 0.24462, 0.07005, 0.4909, 0.03581, 0.79279, 0.24931, 0.94373, 0.5917, 1, 0.90388, 0.8762, 1, 0.70937, 0.9643, 0.4194, 0.98042, 0.11354, 0.79714, 0.04204, 0.52322, 0, 0.20701, 0.06984, 0.53693, 0.58941, 0.79605, 0.48908, 0.26226, 0.51755], "triangles": [7, 9, 8, 13, 10, 9, 13, 9, 7, 1, 0, 11, 14, 11, 10, 1, 11, 14, 12, 10, 13, 14, 10, 12, 6, 5, 13, 6, 13, 7, 14, 2, 1, 3, 14, 12, 3, 2, 14, 4, 12, 13, 4, 13, 5, 3, 12, 4], "vertices": [-39.33, 12.03, -34.65, 2.44, -40.65, -9.16, -23.87, -14.57, -0.13, -16.75, 24.34, -14.11, 34.73, -7.56, 29.49, 3.6, 34.64, 16.5, 16.78, 19.19, -3.61, 19.85, -26.71, 17.64, -2.89, -1.29, 16.06, 2.07, -22.9, 1.54], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22, 20, 24, 18, 26, 22, 28], "width": 71, "height": 36}}, "xuanz": {"xuanz": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-104.92, 12.46, -21.22, -104.34, 105.49, -13.54, 21.79, 103.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 236, "height": 256}}, "xiuziz": {"xiuziz": {"type": "mesh", "uvs": [1, 0.48424, 1, 1, 0, 1, 0, 0.55898, 0, 0, 1, 0], "triangles": [4, 5, 0, 3, 4, 0, 1, 2, 3, 1, 3, 0], "vertices": [36.83, 10.21, 55.26, 10.93, 48.83, -15.71, 32.95, -13.87, 12.81, -11.55, 19.24, 15.09], "hull": 6, "edges": [2, 4, 8, 10, 2, 0, 0, 10, 4, 6, 6, 8], "width": 31, "height": 29}}, "yixyiu2": {"yixyiu": {"type": "mesh", "uvs": [1, 0.31224, 1, 0.67163, 1, 1, 0.62261, 1, 0.44492, 0.89337, 0.12727, 0.66965, 0, 0.32673, 0, 0, 0.37887, 0, 0.76515, 0, 0.46999, 0.28062, 0.73343, 0.60662, 0.86871, 0.80309, 0.30453, 0.5151], "triangles": [5, 13, 4, 4, 13, 11, 5, 6, 13, 13, 10, 11, 13, 6, 10, 6, 8, 10, 6, 7, 8, 10, 9, 0, 10, 8, 9, 3, 12, 2, 12, 1, 2, 12, 3, 11, 12, 11, 1, 11, 0, 1, 3, 4, 11, 11, 10, 0], "vertices": [1, 4, 28.54, 17.29, 1, 1, 4, 14.5, 14.99, 1, 1, 4, 1.67, 12.9, 1, 1, 4, 4.19, 30.76, 1, 1, 17, -13.24, 7.46, 1, 1, 17, -4.58, 21.1, 1, 1, 17, 6.3, 25, 1, 1, 17, 18.83, 25.16, 1, 1, 17, 18.31, 10.17, 1, 1, 17, 13.93, -5.92, 1, 1, 17, 7.57, 6.39, 1, 1, 4, 17.46, 26.29, 1, 1, 4, 9.57, 19.51, 1, 1, 17, 0.12, 13.49, 1], "hull": 10, "edges": [12, 14, 12, 10, 10, 8, 4, 6, 8, 6, 18, 0, 0, 2, 2, 4, 14, 16, 16, 18], "width": 32, "height": 31}}, "tou": {"tou": {"x": 42.54, "y": -1.7, "rotation": -92.05, "width": 76, "height": 89}}, "s1": {"s1": {"type": "mesh", "uvs": [0.76209, 0.00449, 0.91788, 0.03552, 1, 0.14005, 1, 0.25439, 0.83245, 0.32789, 0.8023, 0.41772, 0.73357, 0.45774, 0.68168, 0.48795, 0.68551, 0.53278, 0.69173, 0.60555, 0.58117, 0.72969, 0.64148, 0.75419, 0.65153, 0.81625, 0.65377, 0.83315, 0.72259, 0.86945, 0.71853, 0.91115, 0.71453, 0.95232, 0.61222, 0.98469, 0.50453, 1, 0.37799, 0.98294, 0.2003, 0.96457, 0.09261, 0.92694, 0.11146, 0.88232, 0.13569, 0.83769, 0.12222, 0.79307, 0.14646, 0.73094, 0.06838, 0.62682, 0.00915, 0.50169, 0.00422, 0.46254, 0, 0.42907, 0.03281, 0.38769, 0.06196, 0.35093, 0.18257, 0.23701, 0.20142, 0.15248, 0.30658, 0.0377, 0.48873, 0.0025, 0.66596, 0.0697, 0.53796, 0.1785, 0.49366, 0.2761, 0.39027, 0.3913, 0.34104, 0.4457, 0.34104, 0.5033, 0.36566, 0.6249, 0.36566, 0.7561, 0.3755, 0.8121, 0.38043, 0.8633, 0.42473, 0.9177], "triangles": [13, 45, 44, 13, 44, 12, 23, 44, 45, 22, 23, 45, 13, 46, 45, 13, 14, 46, 15, 46, 14, 16, 46, 15, 22, 46, 21, 46, 22, 45, 20, 21, 46, 19, 20, 46, 46, 18, 19, 16, 17, 46, 17, 18, 46, 36, 35, 0, 36, 0, 1, 36, 1, 2, 36, 34, 35, 37, 36, 2, 37, 34, 36, 33, 34, 37, 32, 33, 37, 37, 2, 3, 38, 32, 37, 38, 37, 3, 4, 38, 3, 31, 32, 38, 39, 31, 38, 39, 38, 4, 30, 31, 39, 5, 39, 4, 40, 30, 39, 29, 30, 40, 40, 39, 5, 6, 40, 5, 7, 40, 6, 8, 40, 7, 28, 29, 40, 41, 27, 28, 40, 41, 28, 8, 41, 40, 42, 41, 8, 42, 8, 9, 26, 27, 41, 26, 41, 42, 10, 42, 9, 25, 26, 42, 43, 25, 42, 10, 43, 42, 24, 25, 43, 11, 44, 43, 11, 43, 10, 24, 43, 44, 44, 11, 12, 23, 24, 44], "vertices": [1, 22, -12.19, -2.22, 1, 1, 22, -10.84, 5.92, 1, 1, 22, -4.5, 12.63, 1, 1, 22, 8.59, 16.74, 1, 2, 23, -13.65, 18.29, 0.0043, 22, 18.96, 13.14, 0.9957, 2, 23, -2.89, 16.99, 0.15724, 22, 29.6, 15.25, 0.84276, 1, 22, 34.99, 14.13, 1, 2, 23, 5.49, 12.18, 0.12, 22, 39.05, 13.29, 0.88, 1, 23, 10.87, 12.27, 1, 2, 23, 19.6, 12.41, 0.99977, 22, 52.4, 17.89, 0.00023, 2, 23, 34.45, 7.93, 0.97329, 24, -10.19, 8.14, 0.02671, 2, 23, 37.41, 10.24, 0.87555, 24, -7.18, 10.39, 0.12445, 2, 23, 44.87, 10.55, 0.40319, 24, 0.28, 10.54, 0.59681, 2, 23, 46.9, 10.61, 0.22979, 24, 2.31, 10.57, 0.77021, 2, 23, 51.28, 13.24, 0.03855, 24, 6.75, 13.11, 0.96145, 2, 23, 56.28, 13.03, 0.00175, 24, 11.74, 12.79, 0.99825, 1, 24, 16.68, 12.47, 1, 1, 24, 20.43, 8.36, 1, 1, 24, 22.13, 4.1, 1, 1, 24, 19.93, -0.76, 1, 1, 24, 17.5, -7.62, 1, 1, 24, 12.85, -11.67, 1, 2, 23, 52.55, -10.61, 0.0244, 24, 7.52, -10.76, 0.9756, 2, 23, 47.2, -9.6, 0.25427, 24, 2.2, -9.65, 0.74573, 2, 23, 41.84, -10.06, 0.70131, 24, -3.17, -10, 0.29869, 2, 23, 34.4, -9.03, 0.98511, 24, -10.59, -8.81, 0.01489, 1, 23, 21.87, -11.93, 1, 2, 23, 6.83, -14.06, 0.89032, 22, 48.48, -11.24, 0.10968, 2, 23, 2.13, -14.2, 0.66832, 22, 44.05, -12.84, 0.33168, 2, 23, -1.89, -14.32, 0.47854, 22, 40.27, -14.2, 0.52146, 2, 23, -6.84, -12.98, 0.2605, 22, 35.15, -14.46, 0.7395, 2, 23, -11.24, -11.79, 0.06679, 22, 30.6, -14.7, 0.93321, 1, 22, 16.15, -14.3, 1, 1, 22, 6.25, -16.64, 1, 1, 22, -5.34, -14.91, 1, 1, 22, -10.06, -10.01, 1, 1, 22, -8.65, -2.33, 1, 1, 22, 5.3, -3.18, 1, 1, 22, 16.99, -1.32, 1, 1, 22, 31.39, -1.03, 1, 2, 23, 0.26, -1.04, 0.6474, 22, 38.19, -0.91, 0.3526, 1, 23, 7.17, -1.12, 1, 1, 23, 21.78, -0.33, 1, 1, 23, 37.52, -0.52, 1, 2, 23, 44.24, -0.21, 0.72062, 24, -0.57, -0.2, 0.27938, 1, 24, 5.58, -0.2, 1, 1, 24, 12.16, 1.31, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 28, 30, 30, 32, 50, 52, 52, 54, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 0, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 10, 12, 12, 14, 14, 16, 16, 18, 58, 60, 60, 62, 60, 80, 54, 56, 56, 58, 80, 56, 10, 80, 80, 16], "width": 39, "height": 120}}, "s2": {"s2": {"type": "mesh", "uvs": [0.10804, 0.07209, 0.26342, 0.03841, 0.44913, 0.0352, 0.63743, 0.08341, 0.62163, 0.19475, 0.71448, 0.24931, 0.6974, 0.30482, 0.71163, 0.37809, 0.7458, 0.45508, 0.77538, 0.5217, 0.80913, 0.63933, 0.83538, 0.72947, 0.88413, 0.75239, 0.89163, 0.8135, 0.98163, 0.86239, 1, 0.91892, 1, 0.97392, 0.82038, 1, 0.49038, 0.98308, 0.46788, 0.9235, 0.50538, 0.84406, 0.45288, 0.80433, 0.44913, 0.75239, 0.38163, 0.65614, 0.32538, 0.5767, 0.26746, 0.50169, 0.22077, 0.44121, 0.18044, 0.38899, 0.1423, 0.33959, 0.08817, 0.26948, 0.06377, 0.1791, 0.04991, 0.11786, 0.49038, 0.44225, 0.55038, 0.55684, 0.31788, 0.26045, 0.22413, 0.126, 0.39663, 0.35517, 0.58038, 0.65614, 0.65163, 0.77378, 0.69663, 0.85475, 0.68163, 0.8135, 0.71163, 0.91586, 0.50916, 0.49455, 0.43516, 0.39514], "triangles": [40, 12, 13, 40, 20, 38, 39, 40, 13, 20, 40, 39, 14, 41, 39, 14, 39, 13, 41, 14, 15, 17, 41, 15, 19, 41, 18, 20, 41, 19, 39, 41, 20, 16, 17, 15, 18, 41, 17, 25, 32, 42, 42, 8, 9, 33, 42, 9, 24, 25, 42, 24, 42, 33, 33, 9, 10, 37, 33, 10, 23, 24, 33, 37, 23, 33, 37, 10, 11, 22, 23, 37, 38, 22, 37, 11, 38, 37, 21, 22, 38, 12, 40, 38, 12, 38, 11, 20, 21, 38, 35, 0, 1, 31, 0, 35, 30, 31, 35, 3, 35, 2, 4, 35, 3, 2, 35, 1, 34, 35, 4, 30, 35, 34, 29, 30, 34, 5, 34, 4, 6, 34, 5, 28, 29, 34, 36, 34, 6, 28, 34, 36, 43, 36, 6, 27, 28, 36, 7, 43, 6, 27, 36, 43, 26, 27, 43, 32, 43, 7, 26, 43, 32, 32, 7, 8, 42, 32, 8, 25, 26, 32], "vertices": [1, 19, -9.38, -4.05, 1, 1, 19, -11.09, 3.51, 1, 1, 19, -9.27, 11.48, 1, 1, 19, -2.07, 18.1, 1, 1, 19, 9.35, 14.27, 1, 1, 19, 16.11, 16.66, 1, 1, 19, 21.69, 14.36, 1, 1, 19, 29.49, 12.88, 1, 2, 19, 37.91, 12.14, 0.53984, 20, 1.43, 12.23, 0.46016, 2, 19, 45.19, 11.51, 0.14171, 20, 8.74, 11.97, 0.85829, 1, 20, 21.47, 10.72, 1, 2, 20, 31.23, 9.77, 0.80094, 21, -4.7, 9.65, 0.19906, 2, 20, 34.1, 11.34, 0.59948, 21, -1.87, 11.29, 0.40052, 2, 20, 40.62, 10.26, 0.16218, 21, 4.68, 10.39, 0.83782, 2, 20, 46.62, 13, 0.00815, 21, 10.6, 13.29, 0.99185, 1, 21, 16.75, 12.95, 1, 1, 21, 22.59, 11.84, 1, 1, 21, 23.88, 3.55, 1, 1, 21, 19.38, -10.37, 1, 1, 21, 12.87, -10.15, 1, 2, 20, 40.23, -7.05, 0.11503, 21, 4.75, -6.93, 0.88497, 2, 20, 35.54, -8.4, 0.54274, 21, 0.11, -8.4, 0.45726, 2, 20, 30.03, -7.36, 0.9385, 21, -5.44, -7.51, 0.0615, 1, 20, 19.24, -8.05, 1, 2, 19, 45.71, -9.16, 0.05444, 20, 10.33, -8.64, 0.94556, 2, 19, 37.23, -9.49, 0.28531, 20, 1.87, -9.4, 0.71469, 2, 19, 30.39, -9.75, 0.47144, 20, -4.95, -10.02, 0.52856, 2, 19, 24.48, -9.98, 0.63219, 20, -10.84, -10.55, 0.36781, 2, 19, 18.89, -10.19, 0.78423, 20, -16.41, -11.05, 0.21577, 1, 19, 10.96, -10.5, 1, 1, 19, 1.26, -8.97, 1, 1, 19, -5.28, -7.81, 1, 1, 19, 33.62, 1.67, 1, 2, 19, 46.25, 0.96, 0.00053, 20, 10.34, 1.49, 0.99947, 1, 19, 12.68, -0.49, 1, 1, 19, -2.42, -0.65, 1, 1, 19, 23.46, 0.16, 1, 1, 20, 21.1, 0.5, 1, 2, 20, 34.18, 0.85, 0.94499, 21, -1.51, 0.81, 0.05501, 2, 20, 43.15, 0.92, 0.0013, 21, 7.45, 1.13, 0.9987, 2, 20, 38.65, 1.23, 0.03874, 21, 2.95, 1.31, 0.96126, 1, 21, 14.06, 0.55, 1, 1, 20, 3.38, 1.15, 1, 1, 19, 28.07, 0.66, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 58, 60, 60, 62, 0, 62, 8, 10, 10, 12, 14, 64, 48, 66, 66, 18, 8, 68, 44, 76, 76, 22, 40, 78, 78, 26, 12, 86, 14, 16, 16, 18, 84, 16, 48, 50, 84, 50, 50, 52, 64, 52, 52, 54, 86, 54, 54, 56, 56, 58], "width": 44, "height": 108}}, "body": {"body": {"type": "mesh", "uvs": [0.00894, 0.19503, 0.07856, 0.19503, 0.1095, 0.13593, 0.17138, 0.10536, 0.23583, 0.05033, 0.3467, 0.0809, 0.39569, 0.0218, 0.50656, 0, 0.64063, 0.02995, 0.66899, 0.11147, 0.80564, 0.13185, 0.85205, 0.18484, 0.9887, 0.22764, 0.94229, 0.32547, 0.92609, 0.43466, 0.94895, 0.52124, 0.94229, 0.64545, 0.99644, 0.74736, 1, 0.88187, 0.89588, 0.9797, 0.72056, 1, 0.41889, 0.99804, 0.23068, 0.94301, 0.12755, 0.88595, 0.15075, 0.81665, 0.06051, 0.64749, 0.00894, 0.53743, 0, 0.39681, 0, 0.2786, 0.50505, 0.12522, 0.57336, 0.29082, 0.59386, 0.48882, 0.63485, 0.69762, 0.65306, 0.88122, 0.42535, 0.85602, 0.33655, 0.65982, 0.23863, 0.42762, 0.22952, 0.25302, 0.24318, 0.15582, 0.71455, 0.21522, 0.7191, 0.29262, 0.77375, 0.46542, 0.81474, 0.67962, 0.82612, 0.86682, 0.88607, 0.26961, 0.84399, 0.31065, 0.8515, 0.46507, 0.86746, 0.6602], "triangles": [23, 24, 22, 19, 43, 18, 21, 22, 34, 20, 43, 19, 21, 33, 20, 38, 2, 3, 38, 1, 2, 37, 1, 38, 1, 27, 28, 1, 28, 0, 37, 27, 1, 27, 37, 36, 26, 27, 36, 25, 26, 36, 39, 9, 10, 39, 10, 11, 44, 11, 12, 44, 40, 39, 44, 39, 11, 30, 39, 40, 45, 40, 44, 13, 44, 12, 45, 44, 13, 14, 45, 13, 46, 45, 14, 41, 40, 45, 41, 45, 46, 31, 30, 40, 31, 40, 41, 46, 14, 15, 29, 6, 7, 29, 7, 8, 29, 8, 9, 5, 6, 29, 38, 4, 5, 3, 4, 38, 39, 30, 29, 39, 29, 9, 38, 5, 29, 37, 38, 29, 29, 30, 37, 30, 36, 37, 31, 36, 30, 15, 47, 46, 31, 35, 36, 25, 36, 35, 16, 47, 15, 47, 42, 41, 47, 41, 46, 32, 31, 41, 42, 32, 41, 35, 31, 32, 24, 25, 35, 34, 35, 32, 24, 35, 34, 17, 43, 42, 32, 42, 43, 33, 34, 32, 47, 16, 17, 17, 42, 47, 43, 33, 32, 43, 17, 18, 22, 24, 34, 21, 34, 33, 20, 33, 43], "vertices": [3, 4, 37.89, 39.12, 0.00069, 5, -1.5, 39.21, 0.08028, 17, 16.16, 8.07, 0.91903, 3, 4, 37.01, 33.41, 0.02027, 5, -1.7, 33.44, 0.1432, 17, 15.28, 2.36, 0.83653, 3, 4, 42.76, 29.93, 0.04614, 5, 4.41, 30.65, 0.29025, 17, 21.02, -1.12, 0.66361, 3, 4, 45.15, 24.36, 0.05891, 5, 7.43, 25.4, 0.42019, 17, 23.41, -6.69, 0.5209, 3, 4, 50.04, 18.2, 0.05064, 5, 13.01, 19.85, 0.57031, 17, 28.31, -12.85, 0.37905, 3, 4, 45.47, 9.59, 0.04486, 5, 9.48, 10.77, 0.79354, 17, 23.74, -21.46, 0.1616, 2, 5, 15.53, 6.49, 0.97696, 17, 29.25, -26.42, 0.02304, 2, 5, 17.49, -2.79, 0.99503, 18, 37.39, 20.01, 0.00497, 3, 4, 47.05, -15.34, 0.02123, 5, 13.95, -13.8, 0.90696, 18, 32.59, 9.49, 0.07181, 3, 4, 38.23, -16.36, 0.17625, 5, 5.32, -15.85, 0.55086, 18, 23.77, 8.46, 0.27288, 3, 4, 35.27, -28.1, 0.1137, 5, 3.74, -27.85, 0.16562, 18, 20.8, -3.27, 0.72068, 3, 4, 25.9, -32.85, 0.02617, 5, -5.01, -33.66, 0.05175, 18, 11.43, -8.02, 0.92208, 1, 18, 6.71, -27.79, 1, 2, 3, 52.33, -41.02, 0.00701, 18, -5.34, -16.3, 0.99299, 3, 3, 41.32, -33.36, 0.13067, 4, -1.8, -33.35, 0.00425, 18, -16.27, -8.52, 0.86508, 3, 3, 34.48, -31, 0.45407, 4, -8.62, -30.91, 0.01087, 18, -23.09, -6.09, 0.53506, 2, 3, 22.46, -30.47, 0.8312, 18, -35.1, -5.43, 0.1688, 2, 3, 12.45, -31.95, 0.92944, 18, -45.12, -6.8, 0.07056, 3, 3, 0.78, -23.23, 0.21086, 18, -56.69, 2.05, 0.00514, 2, 25.19, 8.77, 0.784, 3, 3, -6.8, -17.41, 0.21451, 18, -64.22, 7.94, 0.0015, 2, 20.4, 0.49, 0.784, 3, 3, -11.79, -6.99, 0.21593, 18, -69.09, 18.42, 7e-05, 2, 10.71, -5.8, 0.784, 3, 3, -8.05, 17.77, 0.2099, 17, -72.35, -12.74, 0.0061, 2, -14.32, -5.29, 0.784, 3, 3, -0.12, 32.41, 0.1955, 17, -64.27, 1.82, 0.0205, 2, -29.87, 0.68, 0.784, 3, 3, 7.02, 40.04, 0.18741, 17, -57.04, 9.37, 0.02859, 2, -38.36, 6.78, 0.784, 2, 3, 13.96, 37.1, 0.82316, 17, -50.14, 6.36, 0.17684, 3, 3, 32.6, 42.01, 0.50718, 4, -9.72, 42.11, 0.00826, 17, -31.45, 11.06, 0.48456, 3, 3, 44.64, 44.61, 0.27734, 4, 2.36, 44.59, 0.00717, 17, -19.38, 13.54, 0.71549, 2, 3, 59.37, 43.26, 0.05872, 17, -4.67, 12.03, 0.94128, 3, 3, 71.65, 41.5, 0.00018, 5, -10.24, 40.27, 0.03165, 17, 7.6, 10.14, 0.96818, 3, 4, 38.87, -2.69, 0.01209, 5, 4.36, -2.2, 0.97572, 18, 24.41, 22.13, 0.01219, 3, 4, 20.32, -8.97, 0.73249, 5, -13.34, -10.6, 0.02196, 18, 5.85, 15.85, 0.24555, 3, 3, 41.18, -6.68, 0.61149, 4, -1.66, -6.67, 0.2874, 18, -16.12, 18.16, 0.10112, 2, 3, 20.65, -4.44, 0.98825, 18, -36.63, 20.62, 0.01175, 2, 3, 1.35, -3.21, 0.99923, 18, -55.91, 22.06, 0.00077, 2, 3, 6.64, 15.13, 0.95738, 17, -57.7, -15.54, 0.04262, 3, 3, 28.08, 19.51, 0.78004, 4, -14.48, 19.67, 0.03077, 17, -36.21, -11.38, 0.18919, 4, 3, 53.38, 20.87, 0.12731, 4, 10.84, 20.76, 0.35594, 5, -26.22, 17.82, 0.00028, 17, -10.89, -10.29, 0.51646, 3, 4, 29.09, 17.74, 0.35411, 5, -7.75, 16.95, 0.17025, 17, 7.35, -13.31, 0.47564, 3, 4, 39, 19.28, 0.12876, 5, 1.92, 19.64, 0.4152, 17, 17.27, -11.77, 0.45603, 3, 4, 29.37, -19.15, 0.24972, 5, -3.16, -19.65, 0.19662, 18, 14.91, 5.68, 0.55366, 4, 3, 60.19, -21.52, 0.00053, 4, 17.19, -21.7, 0.12317, 5, -14.96, -23.61, 0.01663, 18, 2.73, 3.12, 0.85967, 3, 3, 41.43, -20.02, 0.4276, 4, -1.55, -20.01, 0.101, 18, -16.01, 4.82, 0.4714, 2, 3, 20.41, -19.49, 0.88944, 18, -37.03, 5.58, 0.11056, 2, 3, 0.81, -17.64, 0.98462, 18, -56.6, 7.63, 0.01538, 3, 3, 60.99, -39.97, 0.00025, 5, -12.21, -41.87, 0.00717, 18, 3.33, -15.34, 0.99257, 4, 3, 56.33, -32.06, 0.00412, 4, 13.22, -32.2, 0.05402, 5, -17.68, -34.5, 0.00735, 18, -1.25, -7.38, 0.93452, 3, 3, 40.36, -26.74, 0.33798, 4, -2.69, -26.72, 0.05518, 18, -17.16, -1.89, 0.60684, 3, 3, 21.83, -24.1, 0.84916, 4, -21.2, -23.87, 0.00047, 18, -35.66, 0.95, 0.15037], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 14, 58, 58, 60, 60, 62, 62, 64, 64, 66, 42, 68, 68, 70, 70, 72, 72, 74, 78, 80, 80, 82, 82, 84, 84, 86, 24, 88, 88, 80, 90, 92, 92, 94], "width": 83, "height": 105}}, "yi2": {"yi2": {"type": "mesh", "uvs": [0.1261, 0.16694, 0.14727, 0, 0.41048, 0.01757, 0.64343, 0.02504, 0.80074, 0.02691, 0.95201, 0.02691, 0.94596, 0.18935, 0.93083, 0.38914, 0.92478, 0.62067, 0.93991, 0.83166, 0.92478, 0.92689, 0.77623, 0.93623, 0.59805, 0.94743, 0.30459, 0.9549, 0.06257, 0.90822, 0.08072, 0.79805, 0.09584, 0.6076, 0.11702, 0.37047, 0.61015, 0.20055, 0.57687, 0.38541, 0.54964, 0.63187, 0.57384, 0.82606, 0.76746, 0.20429, 0.74326, 0.39474, 0.73116, 0.63748, 0.74326, 0.80926, 0.35905, 0.18748, 0.33484, 0.38727, 0.31064, 0.63561, 0.28341, 0.81859], "triangles": [15, 16, 28, 29, 15, 28, 14, 15, 29, 13, 29, 21, 14, 29, 13, 27, 17, 26, 16, 17, 27, 20, 27, 19, 28, 16, 27, 20, 28, 27, 29, 28, 20, 26, 0, 1, 2, 26, 1, 18, 2, 3, 26, 2, 18, 17, 0, 26, 19, 26, 18, 19, 27, 26, 24, 20, 23, 25, 24, 8, 21, 20, 24, 21, 24, 25, 25, 8, 9, 21, 29, 20, 9, 11, 25, 10, 11, 9, 12, 21, 25, 11, 12, 25, 13, 21, 12, 23, 19, 18, 7, 23, 22, 8, 23, 7, 20, 19, 23, 8, 24, 23, 6, 4, 5, 22, 3, 4, 22, 4, 6, 18, 3, 22, 7, 22, 6, 22, 23, 18], "vertices": [2, 29, 12.05, -7.82, 0.99895, 30, -19.58, -6.86, 0.00105, 1, 29, -6.95, -6.77, 1, 2, 26, -1.3, -36.19, 0.1622, 29, -1.79, 11.97, 0.8378, 2, 26, -0.88, -18.66, 0.65838, 29, -0.5, 29.46, 0.34162, 2, 26, -3.9, -6.3, 0.9565, 29, -2.9, 41.95, 0.0435, 1, 26, -12.16, 2.5, 1, 2, 26, 11.6, 5.57, 0.99988, 27, -18.88, 5.63, 0.00012, 1, 27, 3.83, 6.21, 1, 2, 27, 30.1, 7.73, 0.79341, 28, -0.83, 8, 0.20659, 1, 28, 23.13, 9.5, 1, 1, 28, 33.96, 8.55, 1, 3, 28, 35.16, -2.45, 0.9743, 30, 67.97, 41.45, 0.00269, 31, 32.62, 45.68, 0.02301, 3, 28, 36.59, -15.65, 0.71039, 30, 69.21, 28.23, 0.03333, 31, 35.3, 32.68, 0.25628, 3, 28, 37.7, -37.4, 0.16427, 30, 69.99, 6.47, 0.0164, 31, 38.48, 11.13, 0.81933, 1, 31, 35.13, -7.3, 1, 1, 31, 22.54, -7.35, 1, 2, 30, 30.49, -9.05, 0.01564, 31, 0.92, -8.63, 0.98436, 2, 29, 35.19, -7.86, 0.0071, 30, 3.55, -7.51, 0.9929, 4, 26, 20.02, -19.15, 0.54702, 27, -10.52, -19.11, 0.07455, 29, 20.35, 27.93, 0.34315, 30, -10.34, 28.66, 0.03528, 5, 26, 35.82, -20.01, 0.17496, 27, 5.27, -20.01, 0.38387, 28, -27.25, -18.23, 0.01506, 29, 36.08, 26.28, 0.19097, 30, 5.34, 26.6, 0.23515, 5, 27, 33.35, -19.93, 0.15334, 28, 0.78, -19.8, 0.39143, 29, 64.13, 25.02, 0.00277, 30, 33.34, 24.61, 0.41605, 31, 0.05, 25.14, 0.03642, 3, 28, 22.82, -17.66, 0.6647, 30, 55.41, 26.42, 0.09725, 31, 21.79, 29.37, 0.23804, 4, 26, 19.6, -7.68, 0.89443, 27, -10.92, -7.64, 0.01113, 29, 20.5, 39.4, 0.08616, 30, -9.89, 40.13, 0.00828, 5, 26, 35.97, -7.62, 0.10368, 27, 5.46, -7.62, 0.78929, 28, -26.34, -5.87, 0.00405, 29, 36.85, 38.65, 0.04535, 30, 6.43, 38.94, 0.05764, 5, 27, 33.02, -6.46, 0.17964, 28, 1.25, -6.33, 0.72995, 29, 64.45, 38.5, 0.00042, 30, 34.01, 38.07, 0.08474, 31, -0.76, 38.59, 0.00525, 3, 28, 20.76, -5.13, 0.94883, 30, 53.53, 38.99, 0.0202, 31, 18.55, 41.65, 0.03097, 3, 26, 14.58, -37.86, 0.11988, 27, -16.01, -37.81, 0.00926, 29, 13.98, 9.52, 0.87086, 5, 26, 37.34, -37.9, 0.03414, 27, 6.76, -37.9, 0.06837, 28, -26.82, -36.17, 0.00505, 29, 36.72, 8.34, 0.22898, 30, 5.5, 8.65, 0.66345, 5, 27, 35.03, -37.58, 0.02203, 28, 1.42, -37.52, 0.0692, 29, 64.97, 7.31, 0, 30, 33.71, 6.88, 0.7953, 31, 2.37, 7.56, 0.11347, 3, 28, 22.23, -39.21, 0.09722, 30, 54.5, 4.88, 0.05037, 31, 23.25, 7.86, 0.85241], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 20, 22, 22, 24, 6, 36, 36, 38, 38, 40, 40, 42, 8, 44, 44, 46, 46, 48, 48, 50, 4, 52, 52, 54, 54, 56, 56, 58], "width": 79, "height": 128}}, "er": {"er": {"x": 28.51, "y": 25.82, "rotation": -92.05, "width": 18, "height": 23}}, "mutou": {"mutou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115.81, 0.22, -4.35, -40.89, -20.21, 5.47, 99.95, 46.58], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 254, "height": 98}}, "dauo1": {"dauo2": {"type": "mesh", "path": "dauo1", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-10.72, 3.95, -4.34, 41.34, 33.05, 34.96, 26.67, -2.43], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 43, "height": 43}}, "dauo2": {"dauo2": {"type": "mesh", "path": "dauo1", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-8.17, -11.04, -6.49, -37.92, 25.33, -36.5, 23.65, -9.62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 43, "height": 43}}, "toufa1": {"toufa1": {"type": "mesh", "uvs": [0.60679, 0.29001, 0.54344, 0.26224, 0.47228, 0.20922, 0.38944, 0.16252, 0.33485, 0.17261, 0.28905, 0.24583, 0.25298, 0.34302, 0.24518, 0.49071, 0.23591, 0.64511, 0.21161, 0.78517, 0.15814, 0.91422, 0.07429, 0.98347, 0, 0.98504, 0.0111, 0.93468, 0.07915, 0.8796, 0.11682, 0.76629, 0.11926, 0.64039, 0.11926, 0.48931, 0.12533, 0.3335, 0.16057, 0.17455, 0.22862, 0.05495, 0.30639, 0, 0.41698, 0, 0.51176, 0.0455, 0.59439, 0.13206, 0.68082, 0.07818, 0.77168, 0.03208, 0.86254, 0.05998, 0.92249, 0.19221, 0.94778, 0.31595, 0.97494, 0.47123, 0.99087, 0.60831, 0.98993, 0.74296, 0.96839, 0.86549, 0.9075, 1, 0.91031, 0.86306, 0.9253, 0.7187, 0.90469, 0.58162, 0.87471, 0.44454, 0.8232, 0.30746, 0.79603, 0.23467, 0.70611, 0.26621, 0.59464, 0.21405, 0.69754, 0.18044, 0.78867, 0.13677, 0.86432, 0.20168, 0.89895, 0.3268, 0.92265, 0.45427, 0.94726, 0.59709, 0.95819, 0.72339, 0.93723, 0.86384, 0.50602, 0.14797, 0.40743, 0.08305, 0.31387, 0.07981, 0.24954, 0.15013, 0.20359, 0.27997, 0.19022, 0.42387, 0.17518, 0.56777, 0.16766, 0.73331, 0.14177, 0.85016, 0.08412, 0.9259], "triangles": [42, 24, 43, 51, 24, 42, 1, 51, 42, 0, 42, 41, 1, 42, 0, 16, 17, 57, 8, 57, 7, 57, 8, 58, 57, 58, 16, 15, 16, 58, 9, 58, 8, 59, 15, 58, 59, 58, 9, 14, 15, 59, 10, 59, 9, 60, 14, 59, 10, 60, 59, 13, 14, 60, 60, 12, 13, 11, 60, 10, 11, 12, 60, 55, 19, 54, 55, 54, 5, 18, 19, 55, 6, 55, 5, 56, 18, 55, 56, 55, 6, 17, 18, 56, 7, 56, 6, 57, 17, 56, 57, 56, 7, 52, 53, 21, 20, 21, 53, 54, 20, 53, 4, 53, 3, 54, 53, 4, 19, 20, 54, 5, 54, 4, 22, 52, 21, 52, 22, 23, 51, 52, 23, 51, 23, 24, 3, 53, 52, 2, 3, 52, 51, 2, 52, 2, 51, 1, 25, 26, 44, 43, 25, 44, 24, 25, 43, 41, 43, 40, 42, 43, 41, 44, 26, 27, 45, 44, 27, 28, 45, 27, 40, 44, 45, 43, 44, 40, 39, 40, 45, 46, 45, 28, 46, 28, 29, 39, 45, 46, 38, 39, 46, 47, 46, 29, 38, 46, 47, 47, 29, 30, 37, 38, 47, 48, 37, 47, 30, 48, 47, 48, 30, 31, 36, 37, 48, 31, 49, 48, 49, 36, 48, 32, 49, 31, 50, 35, 36, 49, 50, 36, 32, 50, 49, 33, 50, 32, 34, 35, 50, 34, 50, 33], "vertices": [3, 13, -9.04, -8.5, 0.117, 9, -12.12, 8.31, 0.01166, 8, -9.28, 1.39, 0.87135, 2, 9, -6.92, 9.2, 0.27331, 8, -7.41, 6.33, 0.72669, 3, 9, -0.43, 9.03, 0.91123, 10, 1.52, 14.7, 0.00264, 8, -3.97, 11.83, 0.08613, 2, 9, 6.68, 9.63, 0.65383, 10, 5.78, 8.97, 0.34617, 3, 9, 10.18, 12.23, 0.09555, 10, 9.82, 7.36, 0.87637, 11, 3.09, 8.17, 0.02808, 2, 10, 15.18, 9.43, 0.46178, 11, 7.95, 5.11, 0.53822, 2, 10, 20.61, 13.15, 0.01135, 11, 14.18, 2.99, 0.98865, 2, 11, 23.19, 3.45, 0.73529, 12, 3.12, 3.3, 0.26471, 1, 12, 12.57, 3.23, 1, 1, 12, 21.22, 1.92, 1, 1, 12, 29.37, -1.75, 1, 1, 12, 34.05, -8.06, 1, 1, 12, 34.56, -13.9, 1, 1, 12, 31.43, -13.25, 1, 1, 12, 27.7, -8.12, 1, 1, 12, 20.6, -5.64, 1, 1, 12, 12.93, -5.98, 1, 2, 11, 24.3, -6.44, 0.5711, 12, 3.73, -6.63, 0.4289, 1, 11, 14.8, -7.1, 1, 2, 10, 21.82, 0.6, 0.09602, 11, 4.84, -5.49, 0.90398, 1, 10, 13.52, -3.05, 1, 1, 10, 6.53, -2.89, 1, 2, 9, 9.48, -0.12, 0.96268, 10, -1.05, 1.46, 0.03732, 2, 9, 1.57, -1.24, 0.99233, 8, 5.9, 8.36, 0.00767, 3, 13, -6.46, 0.84, 0.22343, 9, -6.68, 0.3, 0.23906, 8, 0.39, 2.02, 0.5375, 1, 13, 1.09, 1.43, 1, 2, 14, -1.74, -1.22, 0.29351, 13, 8.8, 1.44, 0.70649, 2, 14, 3.62, 3.86, 0.96407, 13, 14.88, -2.74, 0.03593, 1, 14, 12.96, 3.4, 1, 2, 15, 1.14, 1.53, 0.22227, 14, 20.37, 0.94, 0.77773, 1, 15, 10.84, 1.25, 1, 1, 15, 19.26, 0.38, 1, 1, 16, 7.88, 1.44, 1, 1, 16, 15.53, 0.85, 1, 1, 16, 24.35, -2.71, 1, 1, 16, 16.05, -3.71, 1, 2, 16, 7.17, -3.83, 0.91122, 15, 24.49, -6.31, 0.08878, 1, 15, 15.98, -5.81, 1, 2, 15, 7.3, -6.02, 0.91435, 14, 23.77, -8.18, 0.08565, 2, 14, 14.55, -7.01, 0.99958, 13, 6.51, -15.69, 0.00042, 2, 14, 9.66, -6.37, 0.86885, 13, 6.12, -10.77, 0.13115, 3, 14, 7.38, -13.37, 0.11008, 13, -1.2, -9.99, 0.76925, 8, -8.11, -6.5, 0.12067, 2, 9, -9.08, 4.69, 0.02769, 8, -4.61, 2.18, 0.97231, 3, 14, 2.63, -11.07, 0.03247, 13, 0.06, -4.87, 0.95674, 8, -2.85, -6.01, 0.01079, 1, 14, 4.34, -3.59, 1, 1, 14, 10.93, -0.76, 1, 1, 14, 18.81, -2.65, 1, 1, 15, 8.81, -2.5, 1, 1, 15, 17.73, -2.79, 1, 2, 16, 7.07, -1.22, 0.9966, 15, 25.41, -3.87, 0.0034, 1, 16, 15.79, -1.6, 1, 2, 9, -1, 4.47, 0.97483, 8, -0.33, 9.04, 0.02517, 2, 9, 7.73, 4.69, 0.9786, 10, 2.13, 5.48, 0.0214, 1, 10, 8.44, 1.62, 1, 1, 10, 14.98, 2.81, 1, 1, 11, 10.82, -1.35, 1, 1, 11, 19.66, -1.35, 1, 1, 12, 8.2, -1.89, 1, 1, 12, 18.31, -1.77, 1, 1, 12, 25.56, -3.31, 1, 1, 12, 30.49, -7.53, 1], "hull": 42, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 0, 82, 94, 96, 98, 100, 84, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 24], "width": 79, "height": 61}}, "yixyiu": {"yixyiu": {"type": "mesh", "uvs": [1, 0.31224, 1, 0.67163, 1, 1, 0.68512, 0.92705, 0.51172, 0.69752, 0.30403, 0.45376, 0.09311, 0.26887, 0, 0, 0.37887, 0, 0.76515, 0, 0.46999, 0.28062, 0.73343, 0.60662, 0.86871, 0.80309], "triangles": [11, 0, 1, 12, 11, 1, 3, 11, 12, 12, 1, 2, 3, 12, 2, 6, 7, 8, 10, 8, 9, 6, 8, 10, 10, 9, 0, 5, 6, 10, 11, 10, 0, 4, 5, 10, 11, 4, 10, 3, 4, 11], "vertices": [2, 17, 8.2, -27.03, 0.128, 4, 29.93, 4.02, 0.872, 2, 17, -9.67, -30.87, 0.128, 4, 12.06, 0.18, 0.872, 2, 17, -15.11, -29.32, 0.128, 4, 6.63, 1.73, 0.872, 2, 17, -19.6, -8.11, 0.128, 4, 2.14, 22.94, 0.872, 1, 17, -9.51, 1.33, 1, 1, 17, 2.17, 13.47, 1, 1, 17, 8.18, 21.28, 1, 1, 17, 15.84, 22.97, 1, 1, 17, 17.04, 9.81, 1, 1, 17, 18.91, -13.42, 1, 1, 17, 7.57, 6.39, 1, 1, 17, -4.28, -4.77, 1, 2, 17, -12.17, -11.54, 0.128, 4, 9.57, 19.51, 0.872], "hull": 10, "edges": [18, 0, 0, 2, 2, 4, 14, 16, 16, 18, 14, 12, 12, 10, 10, 8, 8, 6, 4, 6], "width": 32, "height": 31}}, "toufa3": {"toufa3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [18.28, -37.53, 21.88, 63.38, 111.24, 53.97, 115.83, -42.85], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 107, "height": 100}}, "biyan": {"biyan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-10.98, -18.45, -9.23, 30.62, 12.76, 29.83, 11.01, -19.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 51, "height": 22}}}}, {"name": "wq", "bones": ["bone62"]}], "events": {"atk": {}}, "animations": {"boss_attack1": {"bones": {"bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -20.44, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -36.08, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -9.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -80.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -113.18, "curve": "stepped"}, {"time": 0.3667, "angle": -113.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.27}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -92.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8.81, "curve": "stepped"}, {"time": 0.3667, "angle": 8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.44}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -30.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 74.78, "curve": "stepped"}, {"time": 0.3667, "angle": 74.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.62}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": "stepped"}, {"time": 0.1, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -30.18, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -3.87}]}, "bone45": {"rotate": [{"angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -7.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": 3.04}]}, "bone44": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -38.41, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.5}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -34.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.6}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.51, "curve": "stepped"}, {"time": 0.3667, "angle": -46.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 68.61, "curve": "stepped"}, {"time": 0.3667, "angle": 68.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.77}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": "stepped"}, {"time": 0.1, "angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 58.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 44.1, "curve": "stepped"}, {"time": 0.4667, "angle": 44.1, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -4.1}]}, "bone49": {"rotate": [{"angle": 7.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2667, "angle": -30.76, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 7.66}]}, "bone5": {"rotate": [{"angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24}]}, "bone19": {"rotate": [{"angle": -2.76}]}, "bone4": {"rotate": [{"angle": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.71, "curve": "stepped"}, {"time": 0.3667, "angle": 17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 117.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 31.39, "curve": "stepped"}, {"time": 0.3667, "angle": 31.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.22}]}, "bone50": {"rotate": [{"angle": 10.7, "curve": "stepped"}, {"time": 0.1, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -27.71, "curve": 0.349, "c2": 0.38, "c3": 0.693, "c4": 0.75}, {"time": 0.5, "angle": 10.7}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -18.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -70.9, "curve": "stepped"}, {"time": 0.3667, "angle": -70.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.48}]}, "bone51": {"rotate": [{"angle": 7.67, "curve": "stepped"}, {"time": 0.2, "angle": 7.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": -30.75, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.5, "angle": 7.67}]}, "bone21": {"rotate": [{"angle": -13.37, "curve": "stepped"}, {"time": 0.1, "angle": -13.37, "curve": 0.287, "c2": 0.13, "c3": 0.632, "c4": 0.52}, {"time": 0.3667, "angle": -39.68, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -13.37}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 76.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 38.17, "curve": "stepped"}, {"time": 0.3667, "angle": 38.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.08}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 30.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.55, "curve": "stepped"}, {"time": 0.3667, "angle": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.52}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 54.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -19.21, "curve": "stepped"}, {"time": 0.3667, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -0.69}]}, "bone2": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -17.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -24.9, "y": -51.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 48.13, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 36.43, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone46": {"rotate": [{"angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -30.75, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.1333, "angle": -23.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 7.67}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 28.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -57.6, "curve": "stepped"}, {"time": 0.3667, "angle": -57.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.235, "y": 1.832, "curve": "stepped"}, {"time": 0.3667, "x": 1.235, "y": 1.832, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone55": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -57.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 53.48, "curve": "stepped"}, {"time": 0.3333, "x": 53.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone59": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.55, "curve": "stepped"}, {"time": 0.3667, "angle": -46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 10.67, "y": 48.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 48.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}}, "deform": {"default": {"yaodai1": {"yaodai1": [{"curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "vertices": [8.76444, 9.67755, 5.53945, 8.69655, 2.21813, 11.14754, -0.30828, 6.18335, -2.17212, -1.11809, -2.5699, -8.88322, -1.04432, -12.4526, 2.69912, -11.37733, 6.46672, -13.62819, 8.19656, -8.18867, 9.42283, -1.85922, 9.88535, 5.46134, 2.78877, -1.02753, 2.891, -7.1099, 4.67016, 5.07431], "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.2667, "vertices": [11.07556, 9.37441, 6.99721, 8.4283, 3.08634, 11.743, -0.45924, 5.80271, -3.3791, -3.08928, -4.52104, -12.67861, -2.92885, -17.22926, 1.79979, -16.20953, 6.28043, -19.31382, 8.87868, -12.71772, 10.92721, -4.97681, 12.1122, 4.056, 2.77591, -3.39174, 2.39426, -10.93757, 5.61727, 4.01226], "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4, "vertices": [9.38803, 12.22678, 5.09917, 11.38957, 1.14135, 14.99066, -2.77507, 8.92438, -6.14707, -0.24378, -7.69012, -10.20457, -6.19631, -15.00976, -1.22635, -14.11965, 3.3335, -17.52205, 6.28578, -10.73709, 8.70667, -2.73776, 10.27419, 6.64112, 0.26218, -0.78508, -0.41286, -8.64214, 3.49767, 6.83382], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333}]}}}, "drawOrder": [{"time": 0.2, "offsets": [{"slot": "s1", "offset": 3}]}, {"time": 0.4667}], "events": [{"time": 0.2667, "name": "atk"}]}, "boss_attack3": {"slots": {"mutou2": {"attachment": [{"time": 0.2667, "name": "mutou"}, {"time": 0.5333, "name": null}]}, "mutou": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffff00"}]}, "xuanz": {"attachment": [{"time": 0.2667, "name": "xuanz"}, {"time": 0.5333, "name": null}]}}, "bones": {"feigun": {"rotate": [{"time": 0.2667}, {"time": 0.3333, "angle": -120}, {"time": 0.4, "angle": 120}, {"time": 0.4333}, {"time": 0.5333, "angle": -120}, {"time": 0.5667, "angle": 120}, {"time": 0.6667}], "translate": [{"x": -6.78, "y": -184.11, "curve": "stepped"}, {"time": 0.2667, "x": -6.78, "y": -184.11, "curve": 0.321, "c2": 0.51, "c3": 0.75}, {"time": 0.6667, "x": 777.72, "y": -196.09}]}, "bone2": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 5.42, "curve": "stepped"}, {"time": 0.2, "angle": 5.42, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": -28, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": -11.2, "y": -18.17, "curve": "stepped"}, {"time": 0.2, "x": -11.2, "y": -18.17, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "x": 8.84, "y": -18.08, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone3": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 9.89, "curve": "stepped"}, {"time": 0.2, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone4": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone5": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 9.26, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.4, "angle": 3.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -25.46, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone17": {"rotate": [{"angle": -11.2, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -25.46, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.7, "angle": -11.2}]}, "bone20": {"rotate": [{"angle": -8.19, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -25.46, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.7, "angle": -8.19}]}, "bone21": {"rotate": [{"angle": -22.65, "curve": 0.3, "c2": 0.22, "c3": 0.654, "c4": 0.63}, {"time": 0.1333, "angle": -11.2, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -25.46, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 0.7, "angle": -22.65}]}, "bone33": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 15.99, "curve": "stepped"}, {"time": 0.2, "angle": 15.99, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3667, "angle": -39.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone34": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 62.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4333, "angle": -10.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone36": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 37.6, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.1667, "angle": -177.28, "curve": "stepped"}, {"time": 0.2, "angle": -177.28, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 71.15, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.7}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.74, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3, "angle": 18.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -26.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone45": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -26.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone46": {"rotate": [{"angle": -25.88, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -26.82, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 0.7, "angle": -25.88}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -26.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone50": {"rotate": [{"angle": -11.8, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -26.82, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.7, "angle": -11.8}]}, "bone51": {"rotate": [{"angle": -25.88, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -26.82, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 0.7, "angle": -25.88}]}, "bone62": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -37.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone55": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -50.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone59": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 1.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"x": 0.05, "y": -3.79}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.76}]}}, "deform": {"default": {"yaodai1": {"yaodai1": [{"curve": 0.278, "c3": 0.622, "c4": 0.4}, {"time": 0.1667, "vertices": [-0.72968, -3.22919, 0.07357, -2.79367, 1.08857, -3.26437, 1.49022, -1.80901, 1.58909, 0.23116, 1.27344, 2.315, 0.67462, 3.18005, -0.26117, 2.69144, -1.38389, 3.08487, -1.54814, 1.54634, -1.53041, -0.20111, -1.25608, -2.17032, 0.27652, -0.06204, -0.08062, 1.54778, 0.10793, -1.78453], "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2333, "vertices": [-1.09271, -5.81825, 0.31416, -4.98747, 2.15872, -5.76222, 2.78126, -3.13522, 2.82448, 0.51691, 2.12413, 4.21992, 0.9975, 5.72666, -0.64276, 4.79229, -2.67472, 5.42181, -2.8676, 2.66186, -2.72163, -0.45956, -2.10259, -3.96051, 0.49818, -0.09289, -0.24531, 2.76036, 0.30958, -3.18197], "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7}]}}}, "events": [{"time": 0.4667, "name": "atk"}]}, "boss_idle": {"bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.8, "y": -0.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.51, "y": -0.08}]}, "bone5": {"rotate": [{"angle": 1.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.52, "y": -0.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.09, "y": -0.24}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.64, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.87}]}, "bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.64, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -9.77}]}, "bone19": {"rotate": [{"angle": -2.76, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -13.64, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -2.76}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -13.64, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -8.6}]}, "bone21": {"rotate": [{"angle": -13.37, "curve": 0.287, "c2": 0.13, "c3": 0.632, "c4": 0.52}, {"time": 0.2667, "angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -13.64, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -13.37}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.63, "y": 2.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.82, "y": 1.09}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.22, "y": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.11, "y": -1.69}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.48}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.22}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.62}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.72, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.1}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone49": {"rotate": [{"angle": 3.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "angle": -4.54, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.7667, "angle": -3.4, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.6, "angle": 7.36, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.7667, "angle": 6.21, "curve": 0.305, "c2": 0.23, "c3": 0.647, "c4": 0.6}, {"time": 2, "angle": 3.05}]}, "bone50": {"rotate": [{"angle": 7.24, "curve": 0.268, "c2": 0.07, "c3": 0.626, "c4": 0.5}, {"time": 0.7667, "angle": -1.92, "curve": 0.331, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.9333, "angle": -4.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6, "angle": 3.98, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 1.7667, "angle": 6.23, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.9333, "angle": 7.36, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": 7.24}]}, "bone51": {"rotate": [{"angle": 4.95, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": 7.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "angle": 3, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 1.2667, "angle": -4.54, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.6, "angle": -1.17, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 1.7667, "angle": 1.39, "curve": 0.342, "c2": 0.36, "c3": 0.683, "c4": 0.72}, {"time": 2, "angle": 4.95}]}, "bone44": {"rotate": [{"angle": 5.38, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "angle": -4.54, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 7.36, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 5.38}]}, "bone45": {"rotate": [{"angle": 6.89, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": 7.36, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "angle": 0.64, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 1.1, "angle": -4.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.7667, "angle": 3.98, "curve": 0.362, "c2": 0.45, "c3": 0.704, "c4": 0.82}, {"time": 2, "angle": 6.89}]}, "bone46": {"rotate": [{"angle": 2.44, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": 7.36, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.7667, "angle": 5.08, "curve": 0.299, "c2": 0.22, "c3": 0.649, "c4": 0.61}, {"time": 1.4333, "angle": -4.54, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.7667, "angle": -1.17, "curve": 0.324, "c2": 0.3, "c3": 0.666, "c4": 0.66}, {"time": 2, "angle": 2.44}]}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.2, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -23.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 52.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 103.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 84.48}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 11.86, "y": -31.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.74, "y": 42.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.74, "y": 57.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.74, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 3.7, "y": -124.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 3.7, "y": -99.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 3.7, "y": -124.49}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone33": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 45.03, "curve": "stepped"}, {"time": 0.3333, "angle": 45.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -24.05}]}, "bone34": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 30.81, "curve": "stepped"}, {"time": 0.3667, "angle": 30.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.76}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 73.21, "curve": "stepped"}, {"time": 0.3, "angle": 73.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 22.07}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -15.22, "curve": "stepped"}, {"time": 0.3, "angle": -15.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.67}]}, "bone44": {"rotate": [{"angle": -3.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -11.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9.68, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6333, "angle": -3.56}]}, "bone45": {"rotate": [{"angle": -8.43, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": -3.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -11.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -9.68, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6333, "angle": -8.43}]}, "bone46": {"rotate": [{"angle": -0.95, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -9.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -11.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 21.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6333, "angle": -0.95}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -11.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -9.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone50": {"rotate": [{"angle": -3.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -11.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9.68, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6333, "angle": -3.56}]}, "bone51": {"rotate": [{"angle": -9.68, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -11.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.68}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.73}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 16.4}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.98}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.81}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 49.39}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -45.22, "curve": "stepped"}, {"time": 0.3, "angle": -45.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -72.27}]}, "bone55": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 177.46}]}, "bone60": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 139.15}]}, "bone59": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 82.57}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 7.16, "y": 100.94, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 7.16, "y": 132.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 7.16, "y": 55.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 7.16, "y": 3.52}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 82.57}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2413, "x": 7.16, "y": 100.94, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "x": 7.16, "y": 124.74, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "x": 7.16, "y": 51.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 7.16, "y": 5.73}]}}}, "fusu1": {"slots": {"biyan": {"attachment": [{"name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"angle": 84.48}], "translate": [{"x": 3.7, "y": -124.49}]}, "bone19": {"rotate": [{"angle": -11.24}]}, "bone20": {"rotate": [{"angle": -11.24}]}, "bone21": {"rotate": [{"angle": -11.24}]}, "bone33": {"rotate": [{"angle": -24.05}]}, "bone34": {"rotate": [{"angle": 3.76}]}, "bone36": {"rotate": [{"angle": 22.07}]}, "bone37": {"rotate": [{"angle": -17.67}]}, "bone44": {"rotate": [{"angle": -5.8}]}, "bone45": {"rotate": [{"angle": -0.49}]}, "bone46": {"rotate": [{"angle": -0.27}]}, "bone49": {"rotate": [{"angle": -7.16}]}, "bone50": {"rotate": [{"angle": 1.62}]}, "bone51": {"rotate": [{"angle": 11.71}]}, "bone52": {"rotate": [{"angle": 6.73}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 16.4}]}, "bone56": {"rotate": [{"angle": -5.98}]}, "bone57": {"rotate": [{"angle": 8.81}]}, "bone58": {"rotate": [{"angle": 49.39}]}, "bone62": {"rotate": [{"angle": -72.27}]}, "bone60": {"translate": [{"x": 139.15}]}, "bone61": {"rotate": [{"angle": 82.57}], "translate": [{"x": 7.16, "y": 5.73}]}, "bone59": {"rotate": [{"angle": 82.57}], "translate": [{"x": 7.16, "y": 3.52}]}, "bone55": {"translate": [{"x": 177.46, "y": -12.77}]}}}, "fusu2": {"slots": {"biyan": {"attachment": [{"name": "biyan"}, {"time": 3.2, "name": null}]}}, "bones": {"bone2": {"rotate": [{"angle": 84.48, "curve": "stepped"}, {"time": 1.2333, "angle": 84.48, "curve": 0.5, "c2": 0.38, "c4": 0.75}, {"time": 2.9, "angle": 103.17, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.0667, "angle": 52.43, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.3, "angle": -23.3, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.4667}], "translate": [{"x": 3.7, "y": -124.49, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": 0.74, "y": 8.15, "curve": 0, "c2": 0.25, "c3": 0.5, "c4": 0.63}, {"time": 2.9, "x": 0.74, "y": 57.8, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.0667, "x": 0.74, "y": 42.24, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.3, "x": 11.86, "y": -31.86, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.4667}]}, "bone19": {"rotate": [{"angle": -11.24, "curve": 0.25, "c3": 0.75}, {"time": 3.4667}]}, "bone20": {"rotate": [{"angle": -11.24, "curve": 0.25, "c3": 0.75}, {"time": 3.4667}]}, "bone21": {"rotate": [{"angle": -11.24, "curve": 0.25, "c3": 0.75}, {"time": 3.4667}]}, "bone33": {"rotate": [{"angle": -24.05, "curve": "stepped"}, {"time": 0.5, "angle": -24.05, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 2.7333, "angle": 45.03, "curve": "stepped"}, {"time": 3.0667, "angle": 45.03, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.4}]}, "bone34": {"rotate": [{"angle": 3.76, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 30.81, "curve": "stepped"}, {"time": 2.9, "angle": 30.81, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.3}]}, "bone36": {"rotate": [{"angle": 22.07, "curve": "stepped"}, {"time": 0.7333, "angle": 22.07, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 2.8667, "angle": 73.21, "curve": "stepped"}, {"time": 3.2, "angle": 73.21, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.4667}]}, "bone37": {"rotate": [{"angle": -17.67, "curve": "stepped"}, {"time": 0.7333, "angle": -17.67, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 2.8667, "angle": -15.22, "curve": "stepped"}, {"time": 3.2, "angle": -15.22, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.4667}]}, "bone44": {"rotate": [{"angle": -5.8, "curve": 0.25, "c3": 0.75}, {"time": 2.7429, "angle": -6.69, "curve": 0.25, "c3": 0.75}, {"time": 3.4667}]}, "bone45": {"rotate": [{"angle": -0.49, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.2667, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 1.8333, "angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -8.32, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 3.4667, "angle": -2.68}]}, "bone46": {"rotate": [{"angle": -0.27, "curve": 0.328, "c2": 0.31, "c3": 0.758}, {"time": 0.5667, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 2.1333, "angle": -16.64, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -10.77, "curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 3.4667, "angle": -9.05}]}, "bone49": {"rotate": [{"angle": -7.16, "curve": 0.25, "c3": 0.75}, {"time": 2.7429, "angle": -6.69, "curve": 0.25, "c3": 0.75}, {"time": 3.4667}]}, "bone50": {"rotate": [{"angle": 1.62, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.2667, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 1.8333, "angle": -21.85, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -12.05, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 3.4667, "angle": -3.88}]}, "bone51": {"rotate": [{"angle": 11.71, "curve": 0.328, "c2": 0.31, "c3": 0.758}, {"time": 0.5667, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 2.1333, "angle": -14.58, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -10.27, "curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 3.4667, "angle": -8.63}]}, "bone52": {"rotate": [{"angle": 6.73, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 16.4, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 4.08}]}, "bone56": {"rotate": [{"angle": -5.98, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -0.69}]}, "bone57": {"rotate": [{"angle": 8.81, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 2.44}]}, "bone58": {"rotate": [{"angle": 49.39, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -9.52}]}, "bone62": {"rotate": [{"angle": -72.27, "curve": "stepped"}, {"time": 0.5, "angle": -72.27, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 2.2333, "angle": -45.22, "curve": "stepped"}, {"time": 2.8, "angle": -45.22, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 3.2}]}, "bone60": {"translate": [{"x": 139.15, "curve": 0.248, "c3": 0.628, "c4": 0.51}, {"time": 1.8333, "x": 107.25, "curve": "stepped"}, {"time": 2.5667, "x": 107.25, "curve": 0.34, "c2": 0.35, "c3": 0.678, "c4": 0.7}, {"time": 2.9, "x": 96.12, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.3333}]}, "bone61": {"rotate": [{"angle": 82.57, "curve": 0.248, "c3": 0.628, "c4": 0.51}, {"time": 1.8333, "angle": 32.42, "curve": "stepped"}, {"time": 2.5667, "angle": 32.42, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 2.9, "angle": 14.11, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3.3333}], "translate": [{"x": 7.16, "y": 5.73, "curve": 0.248, "c3": 0.628, "c4": 0.51}, {"time": 1.8333, "x": 3.31, "y": 58.58, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 2.5667, "x": 3.31, "y": 88.21, "curve": 0.34, "c2": 0.35, "c3": 0.678, "c4": 0.7}, {"time": 2.9, "x": 1.7, "y": 128.02, "curve": 0.369, "c2": 0.5, "c3": 0.713, "c4": 0.88}, {"time": 3.1333, "x": 0.09, "y": 48.48, "curve": 0.349, "c2": 0.65, "c3": 0.683}, {"time": 3.3333}]}, "bone59": {"rotate": [{"angle": 82.57, "curve": 0.248, "c3": 0.628, "c4": 0.51}, {"time": 1.8333, "angle": 32.42, "curve": "stepped"}, {"time": 2.5667, "angle": 32.42, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.3333}], "translate": [{"x": 7.16, "y": 3.52, "curve": 0.248, "c3": 0.628, "c4": 0.51}, {"time": 1.8333, "x": 3.31, "y": 75.38, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 2.5667, "x": 3.31, "y": 105.01, "curve": 0.34, "c2": 0.35, "c3": 0.678, "c4": 0.7}, {"time": 2.9, "x": 1.7, "y": 136.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.3333}]}, "bone55": {"translate": [{"x": 177.46, "y": -12.77, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 124.94, "curve": "stepped"}, {"time": 2.5667, "x": 124.94, "curve": 0.34, "c2": 0.35, "c3": 0.678, "c4": 0.7}, {"time": 2.9, "x": 105.22, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.3333}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.1667, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -1.12, "y": -8.93, "curve": "stepped"}, {"time": 0.2, "x": -1.12, "y": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone4": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone33": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone34": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 41.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone36": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 62, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone37": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone45": {"rotate": [{"angle": 8.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 16.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 8.09}]}, "bone46": {"rotate": [{"angle": 16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 16.18}]}, "bone49": {"rotate": [{"angle": 2.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 16.18, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3667, "angle": 2.99}]}, "bone50": {"rotate": [{"angle": 13.22, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.0333, "angle": 8.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 16.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 13.22}]}, "bone51": {"rotate": [{"angle": 11.12, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.28, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3667, "angle": 11.12}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -20.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -52.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 29.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 35.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -40.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.52}]}, "bone62": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -39.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}}}, "run": {"bones": {"bone": {"rotate": [{"angle": -11.61}], "translate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.2333, "x": 1.41, "y": 6.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 0.09, "y": -7.2, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.2333, "curve": 0.34, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 0.3333, "x": 0.09, "y": -7.2, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone33": {"rotate": [{"angle": -109.01, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "angle": 29.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -120.59, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.4667, "angle": -109.01}]}, "bone34": {"rotate": [{"angle": 99.06, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": 99.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 88.76, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4333, "angle": 97.38, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.4667, "angle": 99.06}]}, "bone36": {"rotate": [{"angle": 27.04, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "angle": -54.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 33.83, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.4667, "angle": 27.04}]}, "bone37": {"rotate": [{"angle": 77.27, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": 80.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 32.79, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4333, "angle": 70.01, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.4667, "angle": 77.27}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -15.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "translate": [{"x": 4.97, "y": 6.96}]}, "bone45": {"rotate": [{"angle": -6.18, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -15.24, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.4667, "angle": -6.18}]}, "bone46": {"rotate": [{"angle": -14.07, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -15.24, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.4667, "angle": -14.07}]}, "bone49": {"rotate": [{"angle": -6.18, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -15.24, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.4667, "angle": -6.18}], "translate": [{"x": 12.94, "y": 2.01}]}, "bone50": {"rotate": [{"angle": -14.11, "curve": 0.29, "c2": 0.19, "c3": 0.652, "c4": 0.62}, {"time": 0.1, "angle": -6.18, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -15.24, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.4667, "angle": -14.11}]}, "bone51": {"rotate": [{"angle": -9.53, "curve": 0.327, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 0.0667, "angle": -15.24, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.1, "angle": -14.07, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.3, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.4667, "angle": -9.53}]}, "bone52": {"rotate": [{"angle": -6.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 79.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": -6.53}]}, "bone53": {"rotate": [{"angle": 2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -51.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 2.85}]}, "bone54": {"rotate": [{"angle": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -30.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 0.62}]}, "bone56": {"rotate": [{"angle": 0.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -42.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 0.22}]}, "bone57": {"rotate": [{"angle": -1.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -16.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": -1.1}]}, "bone58": {"rotate": [{"angle": 1.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -24.08, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 1.13}]}, "bone63": {"rotate": [{"angle": -7.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 83.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": -7.74}]}, "bone64": {"rotate": [{"angle": 5.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -21.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 5.68}]}, "bone65": {"rotate": [{"angle": -0.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -57.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": -0.55}]}, "bone66": {"rotate": [{"angle": 0.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 21.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 0.17}]}, "bone55": {"translate": [{"x": -70.8, "y": 0.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": 110.57, "y": -1.4, "curve": 0.336, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.3, "x": 83.47, "y": -1.06, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.3333, "x": 31.2, "y": -0.39, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667, "x": -70.8, "y": 0.9}]}, "bone59": {"translate": [{"x": -4.56, "y": 72.32, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.1333, "x": 1.15, "y": 31.75, "curve": 0.319, "c2": 0.29, "c3": 0.66, "c4": 0.64}, {"time": 0.2333, "x": 3.09, "y": 62.66, "curve": 0.336, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.3, "x": 4.66, "y": 29.67, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.3333, "x": 5.94, "y": 5.6, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.4, "x": 0.9, "y": 18.29, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.4667, "x": -4.56, "y": 72.32}]}, "rjio1": {"translate": [{"x": -79.02, "y": -70.97, "curve": "stepped"}, {"time": 0.2333, "x": -79.02, "y": -70.97, "curve": 0.336, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.3, "x": -25.17, "y": -72.53, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.4667, "x": -79.02, "y": -70.97}]}, "rjio3": {"translate": [{"x": -31.06, "y": -4.19, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.0667, "x": -29.32, "y": -0.35, "curve": 0.293, "c2": 0.2, "c3": 0.647, "c4": 0.6}, {"time": 0.2333, "x": 16.2, "y": 12.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": -31.06, "y": -4.19}]}, "bone60": {"translate": [{"x": 60.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -116.76, "curve": 0.336, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.3, "x": -93.49, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.3333, "x": -50.29, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.4, "x": 3.04, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.4667, "x": 60.51}]}, "bone61": {"translate": [{"x": -1.07, "y": 42.15, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.0667, "x": -0.26, "y": 10.08, "curve": 0.311, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 0.1333, "x": -0.37, "y": 14.47, "curve": 0.327, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": -0.42, "y": 16.78, "curve": 0.327, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 0.2333, "x": -1.72, "y": 68.02, "curve": 0.336, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.3, "x": -2.06, "y": 81.25, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.4667, "x": -1.07, "y": 42.15}]}, "ljio1": {"translate": [{"x": -21.3, "y": 45.96, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -138.72, "y": -118.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": -21.3, "y": 45.96}]}, "ljio3": {"translate": [{"curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.1667, "x": -20.31, "y": -0.94, "curve": 0.327, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 0.2333, "x": -50.9, "y": -1.5, "curve": 0.34, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 0.3333, "x": -44.81, "y": 5.96, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.4, "x": -21.47, "y": -1.5, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.4667}]}, "bone3": {"rotate": [{"angle": -4.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.67}]}, "bone4": {"rotate": [{"angle": -1.52, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": -4.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.09, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.4667, "angle": -1.52}]}, "bone5": {"rotate": [{"angle": 2.49, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "angle": -4.67, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 3.09, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.4667, "angle": 2.49}]}}}, "run1": {"bones": {"bone60": {"translate": [{"x": 33.29, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -41.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -90.06, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -50.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 33.29}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -67.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -50.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"x": 0.48, "y": 17.65, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.0667, "x": 2.45, "y": 6.74, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "y": 2.34, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "x": -3.06, "y": 15.91, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "x": -0.79, "y": 55.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.48, "y": 17.65}]}, "bone55": {"translate": [{"x": -58.4, "y": -0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -34.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 85.88, "y": 0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 14.42, "y": -0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -58.4, "y": -0.4}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.4, "curve": "stepped"}, {"time": 0.2, "angle": -19.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 60.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -58.5}], "translate": [{"x": -1.86, "y": 44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 16.3, "y": 58.28, "curve": "stepped"}, {"time": 0.2, "x": 16.3, "y": 58.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.7, "y": 37.63, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4333, "x": -1.09, "y": 10, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "y": 6.63, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5667, "x": -2.6, "y": 15.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667, "x": -1.86, "y": 44.51}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone2": {"rotate": [{"angle": -22.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -2.93, "y": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.93, "y": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone": {"rotate": [{"angle": 0.47}], "translate": [{"x": -0.63, "y": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -8.51, "curve": "stepped"}, {"time": 0.2333, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.63, "y": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -8.51, "curve": "stepped"}, {"time": 0.5667, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -0.63, "y": -0.09}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone3": {"rotate": [{"angle": 5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.55}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.82}]}, "bone36": {"rotate": [{"angle": 17.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -15.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 17.64}]}, "bone37": {"rotate": [{"angle": 57.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 61.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 33.81, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "angle": 29.65, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 57.41}]}, "bone38": {"rotate": [{"angle": 16.21}]}, "bone35": {"rotate": [{"angle": 35.12}]}, "bone33": {"rotate": [{"angle": -69.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -69.22}]}, "bone34": {"rotate": [{"angle": 70.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 65.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 74.36, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4667, "angle": 79.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 70.84}]}, "bone50": {"rotate": [{"angle": -9.45, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": 0.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "angle": 10.57, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.5667, "angle": -15.88, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -9.45}]}, "bone51": {"rotate": [{"angle": -22.39, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "angle": -25.59, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": -22.15, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3667, "angle": 0.85, "curve": 0.308, "c2": 0.22, "c3": 0.645, "c4": 0.57}, {"time": 0.6667, "angle": -22.39}]}, "bone46": {"rotate": [{"angle": -28.4, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": -5.4, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.6, "angle": -31.84, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -28.4}]}, "bone45": {"rotate": [{"angle": -4.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 5.09, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 0.4667, "angle": -21.35, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -4.64}]}, "bone39": {"rotate": [{"angle": 5.61}]}, "bone44": {"rotate": [{"angle": 16.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 16.59}]}, "bone49": {"rotate": [{"angle": 14.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 20.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -5.53, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 14.51}], "translate": [{"x": 10.7, "y": 1.68}]}, "rjio1": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.25, "y": 53.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}}, "deform": {"default": {"yaodai1": {"yaodai1": [{"vertices": [-1.05258, -2.88472, -0.11927, -2.35201, 1.18983, -2.90135, 1.49915, -1.15289, 1.36163, 1.29134, 0.70838, 3.78203, -0.13865, 4.80974, -1.22826, 4.21127, -2.65994, 4.66641, -2.66676, 2.82196, -2.42283, 0.72984, -1.83497, -1.62427, -0.2166, 0.92171, -0.86071, 2.84444, -0.20527, -1.14307]}]}, "yi2": {"yi2": [{"offset": 6, "vertices": [-2.4433, 5.67358, -4.99255, 3.63784, -14.23624, 6.0274, -15.32115, -2.06464, -11.45535, 3.47414, -11.62749, -2.84535]}]}}}}, "run2": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"x": 0.88, "y": 12.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.17, "y": -9.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.88, "y": 12.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -2.17, "y": -9.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.88, "y": 12.75}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 75.4}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone46": {"rotate": [{"angle": -14.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -19.88, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -14.24}]}, "bone49": {"rotate": [{"angle": 19.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": 24.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.79, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 19.03}], "translate": [{"x": 6.65, "y": 10.38}]}, "bone45": {"rotate": [{"angle": -5.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -19.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -5.64}]}, "bone44": {"rotate": [{"angle": 31.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 31.79}], "translate": [{"x": 1.8, "y": 19.72}]}, "bone50": {"rotate": [{"angle": -14.22, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": -5.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -19.88, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -14.22}]}, "bone51": {"rotate": [{"angle": -19.88, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": -14.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -19.88}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "ljio3": {"translate": [{"curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 0.1, "x": 1.06, "y": 9.48, "curve": 0.319, "c2": 0.29, "c3": 0.658, "c4": 0.64}, {"time": 0.1667, "x": -8.8, "y": -10.76, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.3667}]}, "rjio3": {"translate": [{"time": 0.1667, "curve": 0.339, "c2": 0.35, "c3": 0.684, "c4": 0.72}, {"time": 0.2667, "x": 8.11, "y": 8.86, "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.3667}]}}, "deform": {"default": {"yaodai1": {"yaodai1": [{"vertices": [-2.91897, -1.32939, -0.29453, -0.47743, 2.18545, -2.13972, 4.51134, 1.60664, 6.46156, 7.04088, 7.27724, 12.75533, 6.29547, 15.31205, 3.2532, 14.36944, 0.40588, 15.86668, -1.31797, 11.80422, -2.69931, 7.10931, -3.5378, 1.71853, 2.51579, 6.77099, 2.8262, 11.23014, 0.62891, 2.21636]}]}}}}, "show_time": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 75.4}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone46": {"rotate": [{"angle": -14.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -19.88, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -14.24}]}, "bone49": {"rotate": [{"angle": 19.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": 24.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.79, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 19.03}], "translate": [{"x": 6.65, "y": 10.38}]}, "bone45": {"rotate": [{"angle": -5.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -19.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -5.64}]}, "bone44": {"rotate": [{"angle": 31.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 31.79}], "translate": [{"x": 1.8, "y": 19.72}]}, "bone50": {"rotate": [{"angle": -14.22, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": -5.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -19.88, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -14.22}]}, "bone51": {"rotate": [{"angle": -19.88, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": -14.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -19.88}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}}, "deform": {"default": {"yaodai1": {"yaodai1": [{"vertices": [-2.91897, -1.32939, -0.29453, -0.47743, 2.18545, -2.13972, 4.51134, 1.60664, 6.46156, 7.04088, 7.27724, 12.75533, 6.29547, 15.31205, 3.2532, 14.36944, 0.40588, 15.86668, -1.31797, 11.80422, -2.69931, 7.10931, -3.5378, 1.71853, 2.51579, 6.77099, 2.8262, 11.23014, 0.62891, 2.21636]}]}}}}}}