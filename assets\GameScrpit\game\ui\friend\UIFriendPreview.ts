import { _decorator, instantiate, Label, Node, Sprite } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import ResMgr from "../../../lib/common/ResMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { FriendBellesColorVerticalURL, FriendBellesColorVertical } from "../../../module/friend/FriendConstant";
import { FriendModule } from "../../../module/friend/FriendModule";
import { HeroModule } from "../../../module/hero/HeroModule";
import { AudioItem, AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import TipMgr from "../../../lib/tips/TipMgr";
import GuideMgr from "../../../ext_guide/GuideMgr";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu Oct 17 2024 19:02:29 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/friend/UIFriendPreview.ts
 *
 */

@ccclass("UIFriendPreview")
export class UIFriendPreview extends UINode {
  protected _openAct: boolean = true;
  private _friendIds: number[];
  private _index: number;
  private _itemAudio: AudioItem;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FRIEND}?prefab/ui/UIFriendPreview`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
    this._friendIds = args[0];
    this._index = args[1];
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.refreshUI();
    this.playVoice();
  }
  protected onEvtHide(): void {
    super.onEvtHide();
    if (this._itemAudio) {
      this._itemAudio.release();
    }
  }
  private playVoice() {
    if (this._itemAudio) {
      this._itemAudio.release();
    }

    let friend = FriendModule.config.getFriendById(this._friendIds[this._index]);
    if (friend.voice && friend.voice.length > 0) {
      this._itemAudio = AudioMgr.instance.playVoice(friend.voice[0]);
    }
  }
  private refreshUI() {
    let friend = FriendModule.config.getFriendById(this._friendIds[this._index]);
    this.getNode("friend_name").getComponent(Label).string = `${friend.name}`;
    this.getNode("talent").getComponent(Label).string = `${friend.talentFirst}`;
    this.getNode("friendship").getComponent(Label).string = `${friend.friendlyFirst}`;
    let name = FriendModule.config.getFriendBellesByFameLevel(1)?.name;
    ResMgr.setNodePrefab(
      BundleEnum.BUNDLE_COMMON_FRIEND,
      `prefab/friend_${friend.id}`,
      this.getNode("friend_img"),
      (item: Node) => {
        this.getNode("friend_img").destroyAllChildren();
        item.setScale(1.2, 1.2);
      }
    );
    ResMgr.loadSpriteFrame(
      FriendBellesColorVerticalURL,
      FriendBellesColorVertical[`${1}`],
      this.getNode("friend_bells").getComponent(Sprite),
      this
    );
    this.getNode("bells_name").getComponent(Label).string = `${name}`;

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_UI,
      `atlas_imgs/pingzhi_${friend.color}`,
      this.getNode("friend_color").getComponent(Sprite)
    );
    this.getNode("node_fate_list").removeAllChildren();
    FriendModule.data.getFriendHeros(this._friendIds[this._index]).forEach((heroId) => {
      let item = instantiate(this.getNode("node_friend_viewholder"));
      this.getNode("node_fate_list").addChild(item);
      let head = item.getChildByPath("item_skill/head_mask/head").getComponent(Sprite);
      item.getChildByPath("item_skill/info/power_add").getComponent(Label).string = `${
        HeroModule.config.getHeroInfo(heroId)?.name ?? ""
      }`;
      ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/item_${heroId}`, head);
    });
  }
  private on_click_btn_back() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_switch_left() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._index = (this._index == 0 ? this._friendIds.length : this._index) - 1;
    this.refreshUI();
    this.playVoice();
  }
  private on_click_switch_right() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._index = (this._index + 1) % this._friendIds.length;
    this.refreshUI();
    this.playVoice();
  }
  private on_click_btn_get() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    //
    let friend = FriendModule.config.getFriendById(this._friendIds[this._index]);

    if (friend.labelType.length > 0 && friend.labelType[0] == 1) {
      //仙友目标
      GuideMgr.startGuide({ stepId: 60 });
      return;
    }
    switch (friend.unlock) {
      case 1: //游历累计偶遇次数
        GuideMgr.startGuide({ stepId: 21 });
        break;
      case 2: //VIP等级
        GuideMgr.startGuide({ stepId: 61 });
        break;
      case 3: //主角等级
        GuideMgr.startGuide({ stepId: 25 });
        break;
      case 4: //建筑总等级
        TipMgr.showTip(`建筑总等级达到${friend.unlockNum}级解锁`);
        break;
      case 5: //繁荣度
        TipMgr.showTip(`繁荣度达到${friend.unlockNum}解锁`);
        break;
      case 6: //累计徒弟结伴数量
        TipMgr.showTip(`累计徒弟结伴数量达到${friend.unlockNum}解锁`);
        break;
      case 7: //累计获得灵兽数量
        TipMgr.showTip(`累计获得灵兽数量达到${friend.unlockNum}解锁`);
        break;
      case 8: //首充次数
        break;
      case 9: //累计徒弟成年数
        TipMgr.showTip(`累计徒弟成年数达到${friend.unlockNum}解锁`);
        break;
      case 10: //演武场累计胜利
        TipMgr.showTip(`演武场累计胜利达到${friend.unlockNum}解锁`);
        break;
      case 11: //幸运商店
        GuideMgr.startGuide({ stepId: 36 });
        break;
      case 12: //七日登录
        TipMgr.showTip("请关注限时任务开启");
        break;
      case 13: //福地采集次数47
        GuideMgr.startGuide({ stepId: 42 });
        break;
      case 14: //活动
        TipMgr.showTip("请关注限时任务开启");

        break;
    }
  }
}
