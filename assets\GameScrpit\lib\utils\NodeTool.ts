import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, is<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, EventHandler } from "cc";
import { UITransform, Component } from "cc";
import { TypeConstructor } from "../../game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class NodeTool {
  /**
   * 递归查找页面内的节点，没有返回为空
   * @param nodePage 页面节点
   * @param name 要引导的节点名
   * @returns 返回节点
   */
  public static findByName(nodePage: Node, name: string, needActive: boolean = false): Node {
    if (!isValid(nodePage)) {
      log.error(`父节点不能为空`, name);
      return;
    }

    if (!name) {
      log.error(`name 不能为空`, nodePage);
      return;
    }

    for (let idx in nodePage.children) {
      let node = nodePage.children[idx];
      if (!node) {
        continue;
      }

      if (needActive && !node.activeInHierarchy) {
        continue;
      }

      if (node.name == name) {
        return node;
      }
    }

    for (let idx in nodePage.children) {
      let node = nodePage.children[idx];
      if (!node) {
        continue;
      }

      if (needActive && !node.activeInHierarchy) {
        continue;
      }

      if (node.children.length > 0) {
        let nodeFind = NodeTool.findByName(node, name);
        if (nodeFind) {
          return nodeFind;
        }
      }
    }
  }

  /**
   *
   * @param nodeFrom 节点
   * @param name 要引导的节点名
   * @param className 类名
   * @returns
   */
  public static findCompent<T extends Component>(nodeFrom: Node, name: string, className: TypeConstructor<T>): T {
    let node = NodeTool.findByName(nodeFrom, name);
    if (isValid(node, true)) {
      return node.getComponent(className);
    } else {
      log.error("找不到节点：" + name);
    }
  }

  /**
   * 根据有button组件的 on_click_节点名称 绑定事件
   * @param node 节点名
   * @param ctrl 脚本实例
   */
  public static addClickEvent(node: Node, ctrl: Component) {
    let componentName = ctrl.name.substring(ctrl.name.indexOf("<") + 1, ctrl.name.indexOf(">"));
    node.walk((child) => {
      let btnComponent = child.getComponent(Button);
      if (!btnComponent) {
        return;
      }

      const handlerName = "on_click_" + child.name;
      if (ctrl[handlerName]) {
        let eventHandler: EventHandler = new EventHandler();
        eventHandler.target = ctrl.node;
        eventHandler.component = componentName;
        eventHandler.handler = handlerName;
        btnComponent.clickEvents.push(eventHandler);
      }
    });
  }

  /**
   * 模拟点击节点功能
   * @param widget 模拟点击节点
   * @returns 是否点击成功
   */
  public static fakeClick(widget: Node): boolean {
    if (isValid(widget, true)) {
      let changedTouches: Touch[] = [];

      let startEvent = new EventTouch(changedTouches, true, Node.EventType.TOUCH_START);
      startEvent.touch = new Touch(widget.position.x, widget.position.y);
      startEvent.currentTarget = widget;
      startEvent.simulate = true;
      startEvent.target = widget;
      widget.emit(Node.EventType.TOUCH_START, startEvent);

      let endEvent = new EventTouch(changedTouches, true, Node.EventType.TOUCH_END);
      endEvent.touch = new Touch(widget.position.x, widget.position.y);
      endEvent.currentTarget = widget;
      endEvent.target = widget;
      endEvent.simulate = true;

      setTimeout(() => {
        if (isValid(widget, true)) {
          widget.emit(Node.EventType.TOUCH_END, endEvent);
        }
      }, 1);

      return true;
    }
    return false;
  }

  /**获取节点左边界 */
  public static getNodeLeft(node: Node): number {
    if (!node) {
      return 0;
    }
    let width = node.getComponent(UITransform).width;
    let anchorX = node.getComponent(UITransform).anchorX;
    // log.log(width, anchorX, node.position.x);
    return node.position.x - width * anchorX;
  }
  /**获取节点上边界 */
  public static getNodeTop(node: Node): number {
    if (!node) {
      return 0;
    }
    let height = node.getComponent(UITransform).height;
    let anchorY = node.getComponent(UITransform).anchorY;
    return node.position.y + height * (1 - anchorY);
  }
  /**获取节点右边界 */
  public static getNodeRight(node: Node): number {
    if (!node) {
      return 0;
    }
    let width = node.getComponent(UITransform).width;
    return NodeTool.getNodeLeft(node) + width;
  }
  /**获取节点下边界 */
  public static getNodeBottom(node: Node): number {
    if (!node) {
      return 0;
    }
    let height = node.getComponent(UITransform).height;
    return NodeTool.getNodeTop(node) - height;
  }
  /**获得容器的左边界 */
  public static getBorderLeft(parent: Node) {
    let width = parent.getComponent(UITransform).width;
    let anchorX = parent.getComponent(UITransform).anchorX;
    return 0 - width * anchorX;
  }
  /**获得容器的上边界 */
  public static getBorderTop(parent: Node) {
    let height = parent.getComponent(UITransform).height;
    let anchorY = parent.getComponent(UITransform).anchorY;
    return height * (1 - anchorY);
  }
  /**获得容器的右边界 */
  public static getBorderRight(parent: Node) {
    let width = parent.getComponent(UITransform).width;
    return NodeTool.getBorderLeft(parent) + width;
  }
  /**获得容器的下边界 */
  public static getBorderBottom(parent: Node) {
    let height = parent.getComponent(UITransform).height;
    let anchorY = parent.getComponent(UITransform).anchorY;
    return 0 - height * anchorY;
  }

  public static getNodeCenterPos(node: Node) {
    const pos = node.getPosition();
    const citySize = node.getComponent(UITransform).contentSize;
    const cityAnchor = node.getComponent(UITransform).anchorPoint;
    pos.x += citySize.x * (0.5 - cityAnchor.x);
    pos.y += citySize.y * (0.5 - cityAnchor.y);
    return pos;
  }

  public static getNodeCenterWorldPos(node: Node) {
    const pos = node.getWorldPosition();
    const citySize = node.getComponent(UITransform).contentSize;
    const cityAnchor = node.getComponent(UITransform).anchorPoint;
    pos.x += citySize.x * (0.5 - cityAnchor.x);
    pos.y += citySize.y * (0.5 - cityAnchor.y);
    return pos;
  }
}
