import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { SoulTuJianViewholder } from "./SoulTuJianViewholder";
import { UISoulDetailTujian } from "../UISoulDetailTujian";

export class SoulTuJianAdaper extends ListAdapter {
  private item: Node;
  private _datas: any[];
  private _context: UISoulDetailTujian;
  private _planIndex: number;
  constructor(context: UISoulDetailTujian, item: Node) {
    super();
    this.item = item;
    this._context = context;
  }
  public setData(data: any[], planIndex: number) {
    this._datas = data;
    this._planIndex = planIndex;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(SoulTuJianViewholder).init();
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(SoulTuJianViewholder).updateData(this._context, this._datas[position], position, this._planIndex);
  }
  getCount(): number {
    return this._datas.length;
  }
}
