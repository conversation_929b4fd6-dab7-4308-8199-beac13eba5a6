{"skeleton": {"hash": "fcnBR1VoLhQZOBvzKsV49oqAJJ4=", "spine": "3.8.75", "x": -158.73, "y": -67.2, "width": 316.06, "height": 300.66, "images": "./images/", "audio": "D:/spine导出/洞天福地闪电动画/rock"}, "bones": [{"name": "root"}, {"name": "tongfuall", "parent": "root", "x": 11.64, "y": -190.11, "color": "ff0000ff"}, {"name": "dflight", "parent": "tongfuall", "x": -11.6, "y": 244.87, "color": "ffffffff"}, {"name": "tongfuall2", "parent": "tongfuall", "x": -6.6, "y": 133.82, "color": "ffef00ff"}, {"name": "tongfuall3", "parent": "tongfuall2", "x": -80.23, "y": 55.28, "color": "ffef00ff"}, {"name": "tongfuall4", "parent": "tongfuall2", "x": -39.57, "y": 61.55, "color": "ffef00ff"}, {"name": "tongfuall5", "parent": "tongfuall2", "x": 1.6, "y": 57.04, "color": "ffef00ff"}, {"name": "tongfuall6", "parent": "tongfuall2", "x": -4.93, "y": 95.44, "color": "ffef00ff"}, {"name": "tongfuall7", "parent": "tongfuall2", "x": -57.64, "y": 100.46, "color": "ffef00ff"}, {"name": "tongfuall8", "parent": "tongfuall2", "x": -71.69, "y": 126.56, "color": "ffef00ff"}, {"name": "tongfuall9", "parent": "tongfuall2", "x": -23.75, "y": 122.8, "color": "ffef00ff"}, {"name": "tongfuall10", "parent": "tongfuall2", "x": 29.46, "y": 73.35, "color": "ffef00ff"}, {"name": "tongfuall11", "parent": "tongfuall2", "x": 81.17, "y": 50.39, "color": "ffef00ff"}, {"name": "tongfuall12", "parent": "tongfuall2", "x": 66.97, "y": 79.06, "color": "ffef00ff"}, {"name": "tongfuall13", "parent": "tongfuall2", "x": 45.53, "y": 118.19, "color": "ffef00ff"}, {"name": "tongfuall14", "parent": "tongfuall2", "x": -39.69, "y": 150.08, "color": "ffef00ff"}, {"name": "tongfuall15", "parent": "tongfuall2", "x": 10.15, "y": 139.9, "color": "ffef00ff"}, {"name": "tongfuall16", "parent": "tongfuall2", "x": 41.24, "y": 149.28, "color": "ffef00ff"}, {"name": "tongfuall17", "parent": "tongfuall2", "x": 14.17, "y": 177.69, "color": "ffef00ff"}, {"name": "tongfuall18", "parent": "tongfuall2", "x": -23.35, "y": 185.19, "color": "ffef00ff"}, {"name": "dflight2", "parent": "tongfuall", "x": -11.6, "y": 244.87, "color": "ffffffff"}, {"name": "tongfuall19", "parent": "tongfuall", "rotation": 0.08, "x": -6.62, "y": 144.62, "color": "ff0000ff"}, {"name": "bone", "parent": "root", "x": 8.95, "y": 249.83}, {"name": "bone2", "parent": "bone", "x": -86.45, "y": -87.79}, {"name": "bone3", "parent": "bone", "x": -58.32, "y": -82.57}, {"name": "bone4", "parent": "bone", "x": -42.95, "y": -58.79}, {"name": "bone5", "parent": "bone", "x": -12.79, "y": -58.79}, {"name": "bone6", "parent": "bone", "x": 10.7, "y": -37.91}, {"name": "bone7", "parent": "bone", "x": 11.28, "y": -75.32}, {"name": "bone8", "parent": "bone", "x": 41.44, "y": -85.47}, {"name": "bone9", "parent": "bone", "x": 63.48, "y": -66.62}, {"name": "bone10", "parent": "bone", "x": 69.28, "y": -93.59}, {"name": "bone11", "parent": "bone", "x": 94.93, "y": -90.65}, {"name": "bone12", "parent": "bone", "x": -116.34, "y": -83.82}, {"name": "sd1", "parent": "root", "rotation": -19.97, "x": -36.21, "y": 139.14}, {"name": "sd3", "parent": "sd1", "rotation": 42.75, "x": 35.46, "y": -153.31, "color": "abe323ff"}, {"name": "sd2", "parent": "root", "x": -1.65, "y": 175.12}, {"name": "sd6", "parent": "sd2", "x": 26.92, "y": -138.51}, {"name": "sd4", "parent": "root", "rotation": 11.83, "x": 17.43, "y": 148.14}, {"name": "sd5", "parent": "sd4", "rotation": -26.32, "x": 29.99, "y": -123.85, "color": "abe323ff"}, {"name": "sd7", "parent": "root", "x": 11.28, "y": 169.09}, {"name": "sd8", "parent": "sd7", "x": -39.03, "y": -168.25}], "slots": [{"name": "tflf01", "bone": "tongfuall3", "attachment": "tflf01"}, {"name": "tflf02", "bone": "tongfuall4", "attachment": "tflf02"}, {"name": "tflf03", "bone": "tongfuall5", "attachment": "tflf03"}, {"name": "tflf04", "bone": "tongfuall6", "attachment": "tflf04"}, {"name": "tflf05", "bone": "tongfuall7", "attachment": "tflf05"}, {"name": "tflf06", "bone": "tongfuall8", "attachment": "tflf06"}, {"name": "tflf07", "bone": "tongfuall9", "attachment": "tflf07"}, {"name": "tflf08", "bone": "tongfuall10", "attachment": "tflf08"}, {"name": "tflf09", "bone": "tongfuall11", "attachment": "tflf09"}, {"name": "tflf010", "bone": "tongfuall12", "attachment": "tflf010"}, {"name": "tflf011", "bone": "tongfuall13", "attachment": "tflf011"}, {"name": "tflf012", "bone": "tongfuall14", "attachment": "tflf012"}, {"name": "tflf013", "bone": "tongfuall15", "attachment": "tflf013"}, {"name": "tflf014", "bone": "tongfuall16", "attachment": "tflf014"}, {"name": "tflf015", "bone": "tongfuall17", "attachment": "tflf015"}, {"name": "tflf016", "bone": "tongfuall18", "attachment": "tflf016"}, {"name": "dk", "bone": "root", "attachment": "dk"}, {"name": "lightlf", "bone": "dflight", "blend": "additive"}, {"name": "lightlf2", "bone": "dflight2", "blend": "additive"}, {"name": "fxdt", "bone": "tongfuall19"}, {"name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "bone": "sd1", "attachment": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00019"}, {"name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_9", "bone": "sd4", "attachment": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00019"}, {"name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_21", "bone": "sd7"}, {"name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "bone": "sd2"}, {"name": "yun", "bone": "root", "attachment": "yun"}], "skins": [{"name": "default", "attachments": {"shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021": {"shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.8, 0, 0.6, 0, 0.4, 0, 0.2, 0, 0, 0.5, 0, 1, 0, 1, 0.2, 1, 0.4, 1, 0.6, 1, 0.8, 0.5, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2], "triangles": [0, 1, 13, 1, 14, 13, 13, 14, 12, 14, 15, 12, 12, 15, 11, 1, 2, 14, 2, 3, 14, 14, 3, 15, 3, 4, 15, 15, 4, 16, 15, 16, 11, 11, 16, 10, 16, 17, 10, 10, 17, 9, 17, 8, 9, 4, 5, 16, 16, 5, 17, 5, 6, 17, 17, 6, 8, 6, 7, 8], "vertices": [2, 36, 80, -159.2, 0.00244, 37, 53.08, -20.69, 0.99756, 1, 37, -1.36, -20.65, 1, 2, 36, -29, -159.2, 0.00231, 37, -55.92, -20.69, 0.99769, 2, 36, -31.11, -122.93, 0.08484, 37, -58.03, 15.57, 0.91516, 2, 36, -37.2, -89.2, 0.32327, 37, -64.12, 49.31, 0.67673, 2, 36, -45.1, -56.6, 0.63211, 37, -72.01, 81.91, 0.36789, 2, 36, -51.69, -23.18, 0.89022, 37, -78.61, 115.33, 0.10978, 2, 36, -54.25, 12.8, 0.99028, 37, -81.17, 151.31, 0.00972, 1, 36, 0, 12.64, 1, 2, 36, 54.81, 12.84, 0.98785, 37, 27.89, 151.35, 0.01215, 2, 36, 57.36, -23.15, 0.8882, 37, 30.44, 115.36, 0.1118, 2, 36, 63.94, -56.58, 0.63059, 37, 37.02, 81.93, 0.36941, 2, 36, 71.83, -89.17, 0.3218, 37, 44.92, 49.34, 0.6782, 2, 36, 77.93, -122.91, 0.08351, 37, 51.01, 15.6, 0.91649, 2, 36, 23.94, -122.58, 0.06323, 37, -2.97, 15.92, 0.93677, 2, 36, 17.45, -89.1, 0.31743, 37, -9.47, 49.41, 0.68257, 2, 36, 9.37, -56.62, 0.63346, 37, -17.55, 81.89, 0.36654, 2, 36, 2.49, -23.38, 0.90245, 37, -24.42, 115.13, 0.09755], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_21": {"type": "<PERSON><PERSON><PERSON>", "path": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00022": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00023": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00024": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00025": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00026": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00027": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00028": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}}, "dk": {"dk": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114.65, -23.53, -119.35, -23.53, -119.35, 139.47, 114.65, 139.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 234, "height": 163}}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_9": {"shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009": {"type": "mesh", "uvs": [1, 0.231, 1, 0.46034, 1, 0.65126, 1, 0.8358, 1, 1, 0.49283, 1, 0, 1, 0, 0.81631, 0, 0.64084, 0, 0.44456, 0, 0.23041, 0, 0, 0.52106, 0, 1, 0, 0.50851, 0.23071, 0.50655, 0.45255, 0.50194, 0.64607, 0.49174, 0.8259], "triangles": [16, 8, 15, 16, 1, 2, 17, 7, 8, 16, 17, 8, 16, 2, 3, 17, 16, 3, 6, 7, 17, 5, 17, 3, 6, 17, 5, 5, 3, 4, 14, 10, 11, 12, 14, 11, 12, 13, 0, 14, 12, 0, 9, 10, 14, 15, 9, 14, 15, 14, 0, 1, 15, 0, 8, 9, 15, 16, 15, 1], "vertices": [2, 38, 110.55, 5.41, 0.77972, 39, 14.88, 151.57, 0.22028, 2, 38, 120.11, -53.38, 0.56214, 39, 49.52, 103.12, 0.43786, 2, 38, 119.93, -100.42, 0.38101, 39, 70.22, 60.88, 0.61899, 2, 38, 112.74, -144.24, 0.20593, 39, 83.21, 18.41, 0.79407, 2, 38, 100.53, -181.88, 0.05015, 39, 88.96, -20.73, 0.94985, 2, 38, 18.2, -143.47, 0.05139, 39, -1.86, -22.81, 0.94861, 2, 38, -61.81, -106.24, 0.05259, 39, -90.09, -24.92, 0.94741, 2, 38, -51.8, -77.92, 0.22642, 39, -93.68, 4.9, 0.77358, 2, 38, -48.61, -49.38, 0.39249, 39, -103.47, 31.9, 0.60751, 2, 38, -52.42, -15.73, 0.57824, 39, -121.81, 60.37, 0.42176, 2, 38, -65.46, 23.07, 0.78091, 39, -150.7, 89.35, 0.21909, 2, 38, -89.86, 67.23, 0.99896, 39, -192.15, 118.12, 0.00104, 2, 38, 3.93, 67.19, 0.99892, 39, -108.07, 159.67, 0.00108, 2, 38, 90.13, 67.14, 0.99888, 39, -30.79, 197.86, 0.00112, 2, 38, 24.04, 14.11, 0.7803, 39, -66.51, 121.02, 0.2197, 2, 38, 35.06, -34.48, 0.57009, 39, -35.09, 82.35, 0.42991, 2, 38, 36.05, -74.77, 0.38673, 39, -16.34, 46.68, 0.61327, 2, 38, 29.22, -110.13, 0.21635, 39, -6.78, 11.96, 0.78365], "hull": 14, "edges": [20, 22, 0, 26, 18, 20, 0, 2, 16, 18, 2, 4, 12, 14, 14, 16, 4, 6, 6, 8, 22, 24, 24, 26, 0, 28, 28, 20, 24, 28, 2, 30, 30, 18, 28, 30, 4, 32, 32, 16, 30, 32, 6, 34, 34, 14, 32, 34, 8, 10, 10, 12, 34, 10], "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_9": {"type": "<PERSON><PERSON><PERSON>", "path": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00010": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00011": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00012": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00013": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00014": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00015": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00016": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00017": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00018": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00019": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009": {"shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009": {"type": "mesh", "uvs": [1, 0.231, 1, 0.46034, 1, 0.65126, 1, 0.8358, 1, 1, 0.49283, 1, 0, 1, 0, 0.81631, 0, 0.64084, 0, 0.44456, 0, 0.23041, 0, 0, 0.52106, 0, 1, 0, 0.50851, 0.23071, 0.50655, 0.45255, 0.50194, 0.64607, 0.49174, 0.8259], "triangles": [16, 8, 15, 16, 1, 2, 17, 7, 8, 16, 17, 8, 16, 2, 3, 17, 16, 3, 6, 7, 17, 5, 17, 3, 6, 17, 5, 5, 3, 4, 14, 10, 11, 12, 14, 11, 12, 13, 0, 14, 12, 0, 9, 10, 14, 15, 9, 14, 15, 14, 0, 1, 15, 0, 8, 9, 15, 16, 15, 1], "vertices": [2, 34, 67.9, 15.21, 0.77972, 35, 138.21, 101.74, 0.22028, 2, 34, 62.49, -29.98, 0.56214, 35, 103.57, 72.23, 0.43786, 2, 34, 70.45, -62.72, 0.38101, 35, 87.18, 42.78, 0.61899, 2, 34, 88.89, -90.17, 0.20593, 35, 82.1, 10.11, 0.79407, 2, 34, 114.18, -111.1, 0.05015, 35, 86.46, -22.43, 0.94985, 2, 34, 45.86, -169.97, 0.05139, 35, -3.66, -19.29, 0.94861, 2, 34, -20.58, -227.01, 0.05259, 35, -91.18, -16.08, 0.94741, 2, 34, -56.56, -182.08, 0.22642, 35, -87.11, 41.34, 0.77358, 2, 34, -81.18, -135.34, 0.39249, 35, -73.46, 92.37, 0.60751, 2, 34, -97.42, -78.63, 0.57824, 35, -46.89, 145.03, 0.42176, 2, 34, -101.53, -11.44, 0.78091, 35, -4.31, 197.17, 0.21909, 2, 34, -90.09, 67.06, 0.99896, 35, 57.37, 247.05, 0.00104, 2, 34, 3.67, 67.12, 0.99892, 35, 126.27, 183.46, 0.00108, 2, 34, 89.84, 67.18, 0.99888, 35, 189.6, 125.02, 0.00112, 2, 34, -15.36, 2.07, 0.7803, 35, 68.15, 148.61, 0.2197, 2, 34, -16.23, -54.48, 0.57009, 35, 29.12, 107.67, 0.42991, 2, 34, -4.94, -99.24, 0.38673, 35, 7.03, 67.13, 0.61327, 2, 34, 15.19, -137.51, 0.21635, 35, -4.16, 25.36, 0.78365], "hull": 14, "edges": [20, 22, 0, 26, 18, 20, 0, 2, 16, 18, 2, 4, 12, 14, 14, 16, 4, 6, 6, 8, 22, 24, 24, 26, 0, 28, 28, 20, 24, 28, 2, 30, 30, 18, 28, 30, 4, 32, 32, 16, 30, 32, 6, 34, 34, 14, 32, 34, 8, 10, 10, 12, 34, 10], "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_9": {"type": "<PERSON><PERSON><PERSON>", "path": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00010": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00011": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00012": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00013": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00014": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00015": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00016": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00017": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00018": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00019": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009", "width": 180, "height": 241}}, "lightlf": {"lightlf": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [116.33, -78.57, -119.67, -78.57, -119.67, 86.43, 116.33, 86.43], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 236, "height": 165}}, "yun": {"yun": {"type": "mesh", "uvs": [0.32987, 0.83176, 0.30966, 0.93426, 0.25385, 1, 0.1682, 1, 0.08929, 1, 0.02096, 0.96426, 0, 0.92676, 0, 0.73676, 0.00171, 0.54176, 0.07774, 0.43676, 0.14221, 0.48676, 0.16916, 0.51176, 0.18359, 0.43176, 0.20862, 0.39176, 0.23075, 0.28677, 0.27309, 0.25427, 0.31793, 0.11406, 0.39614, 0.06554, 0.44984, 0.08677, 0.4942, 0.00489, 0.57358, 0, 0.64129, 0.05038, 0.65763, 0.13529, 0.70082, 0.15652, 0.73818, 0.2293, 0.76386, 0.33847, 0.7662, 0.39609, 0.80355, 0.387, 0.85025, 0.42339, 0.85959, 0.48101, 0.9148, 0.54858, 0.96723, 0.62979, 1, 0.72933, 1, 0.85245, 1, 0.9258, 0.95412, 1, 0.8916, 1, 0.819, 0.96247, 0.76858, 0.97033, 0.73531, 0.93889, 0.70304, 0.95985, 0.64859, 0.95985, 0.62338, 0.92318, 0.59817, 0.86817, 0.55683, 0.89436, 0.5054, 0.86817, 0.47212, 0.79482, 0.43078, 0.81577, 0.3854, 0.85769, 0.05264, 0.75015, 0.12302, 0.75305, 0.18894, 0.72113, 0.23809, 0.63116, 0.28836, 0.5441, 0.34534, 0.46284, 0.4213, 0.42801, 0.4928, 0.4077, 0.57324, 0.43672, 0.64138, 0.49186, 0.69054, 0.59924, 0.75198, 0.69791, 0.81678, 0.75305, 0.89498, 0.78788, 0.94413, 0.82271, 0.13531, 0.88655, 0.21016, 0.84883, 0.28613, 0.75305, 0.34869, 0.6776, 0.42466, 0.66019, 0.51514, 0.67179, 0.59111, 0.72113, 0.68048, 0.79368, 0.73746, 0.84302, 0.79555, 0.86914, 0.87599, 0.89236, 0.95642, 0.92138, 0.92067, 0.69791, 0.85029, 0.63407, 0.79332, 0.57022, 0.73076, 0.50927, 0.69277, 0.40189, 0.62463, 0.2858, 0.54419, 0.21905, 0.45929, 0.23937, 0.39561, 0.2858, 0.30959, 0.35546, 0.26602, 0.45123, 0.21798, 0.53249, 0.15989, 0.63987, 0.10627, 0.63987, 0.03812, 0.63116, 0.04817, 0.85173], "triangles": [86, 14, 15, 52, 87, 53, 87, 88, 11, 51, 87, 52, 51, 88, 87, 65, 51, 52, 13, 14, 86, 87, 13, 86, 12, 13, 87, 87, 86, 53, 11, 12, 87, 88, 10, 11, 89, 10, 88, 50, 89, 88, 50, 88, 51, 64, 50, 51, 91, 50, 64, 4, 91, 64, 4, 64, 3, 90, 8, 9, 89, 9, 10, 90, 9, 89, 7, 8, 90, 49, 90, 89, 7, 90, 49, 49, 89, 50, 91, 7, 49, 6, 7, 91, 5, 6, 91, 50, 91, 49, 5, 91, 4, 64, 51, 65, 3, 64, 65, 57, 82, 81, 57, 81, 58, 80, 24, 25, 80, 25, 26, 79, 80, 26, 78, 26, 27, 78, 27, 28, 78, 28, 29, 77, 78, 29, 77, 29, 30, 59, 58, 80, 21, 82, 20, 81, 21, 22, 81, 82, 21, 23, 81, 22, 80, 23, 24, 80, 81, 23, 58, 81, 80, 70, 57, 58, 70, 58, 59, 69, 57, 70, 43, 70, 71, 53, 86, 85, 65, 52, 66, 0, 66, 67, 48, 67, 68, 0, 67, 48, 1, 66, 0, 1, 2, 66, 68, 56, 69, 47, 68, 46, 48, 68, 47, 66, 52, 53, 66, 53, 67, 53, 85, 54, 54, 55, 68, 67, 54, 68, 53, 54, 67, 56, 83, 82, 55, 83, 56, 68, 55, 56, 54, 84, 55, 82, 19, 20, 56, 82, 57, 2, 65, 66, 3, 65, 2, 76, 30, 31, 77, 30, 76, 62, 77, 76, 63, 76, 31, 63, 31, 32, 62, 76, 63, 63, 32, 33, 75, 63, 33, 75, 33, 34, 74, 75, 36, 63, 74, 62, 75, 74, 63, 35, 36, 75, 35, 75, 34, 84, 18, 83, 55, 84, 83, 84, 16, 17, 84, 17, 18, 85, 15, 16, 84, 85, 16, 86, 15, 85, 54, 85, 84, 18, 19, 82, 83, 18, 82, 69, 56, 57, 46, 68, 69, 45, 46, 69, 44, 45, 69, 70, 44, 69, 43, 44, 70, 79, 26, 78, 59, 80, 79, 60, 79, 78, 61, 78, 77, 60, 78, 61, 71, 70, 59, 71, 59, 60, 59, 79, 60, 61, 77, 62, 73, 72, 60, 61, 73, 60, 74, 61, 62, 73, 61, 74, 37, 73, 74, 73, 38, 72, 38, 73, 37, 37, 74, 36, 72, 71, 60, 42, 43, 71, 72, 40, 71, 38, 39, 72, 41, 42, 71, 40, 41, 71, 72, 39, 40], "vertices": [5, 28, -58.09, -17.57, 0.02632, 26, -34.02, -34.1, 0.0767, 25, -3.86, -34.1, 0.0562, 24, 11.51, -10.32, 0.8143, 23, 39.64, -5.1, 0.02648, 5, 28, -62.92, -27, 0.0028, 26, -38.85, -43.53, 0.00942, 25, -8.69, -43.53, 0.00048, 24, 6.68, -19.75, 0.82599, 23, 34.81, -14.53, 0.16131, 2, 24, -6.66, -25.79, 0.57665, 23, 21.47, -20.57, 0.42335, 3, 33, 30.89, -24.55, 0.06593, 24, -27.13, -25.79, 0.12135, 23, 1, -20.57, 0.81271, 3, 33, 12.03, -24.55, 0.46807, 24, -45.99, -25.79, 9e-05, 23, -17.86, -20.57, 0.53184, 2, 33, -4.3, -21.26, 0.85286, 23, -34.19, -17.29, 0.14714, 2, 33, -9.31, -17.81, 0.90759, 23, -39.2, -13.84, 0.09241, 1, 33, -9.31, -0.33, 1, 2, 33, -8.9, 17.61, 0.97457, 23, -38.79, 21.58, 0.02543, 4, 33, 9.27, 27.27, 0.72666, 25, -64.12, 2.24, 0.01075, 24, -48.75, 26.02, 0.00188, 23, -20.62, 31.24, 0.26071, 4, 33, 24.68, 22.67, 0.35663, 25, -48.71, -2.36, 0.07676, 24, -33.34, 21.42, 0.06495, 23, -5.21, 26.64, 0.50166, 4, 33, 31.12, 20.37, 0.14075, 25, -42.27, -4.66, 0.19383, 24, -26.9, 19.12, 0.17089, 23, 1.23, 24.34, 0.49452, 4, 33, 34.57, 27.73, 0.03634, 25, -38.82, 2.7, 0.39078, 24, -23.45, 26.48, 0.2364, 23, 4.68, 31.7, 0.33648, 4, 33, 40.55, 31.41, 0.01045, 25, -32.84, 6.38, 0.55129, 24, -17.47, 30.16, 0.21845, 23, 10.66, 35.38, 0.21981, 6, 33, 45.84, 41.07, 0.00019, 27, -81.2, -4.84, 0.00074, 26, -57.71, 16.04, 0.0001, 25, -27.55, 16.04, 0.75972, 24, -12.18, 39.82, 0.1335, 23, 15.95, 45.04, 0.10576, 5, 27, -71.08, -1.85, 0.01031, 26, -47.59, 19.03, 0.01832, 25, -17.43, 19.03, 0.87058, 24, -2.06, 42.81, 0.0558, 23, 26.07, 48.03, 0.045, 5, 27, -60.37, 11.05, 0.05751, 26, -36.88, 31.93, 0.12493, 25, -6.72, 31.93, 0.81428, 24, 8.65, 55.71, 0.00024, 23, 36.78, 60.93, 0.00304, 3, 27, -41.67, 15.52, 0.17797, 26, -18.18, 36.4, 0.27782, 25, 11.98, 36.4, 0.54421, 3, 27, -28.84, 13.56, 0.40618, 26, -5.35, 34.44, 0.31544, 25, 24.81, 34.44, 0.27838, 3, 27, -18.24, 21.1, 0.71107, 26, 5.25, 41.98, 0.18574, 25, 35.41, 41.98, 0.10319, 4, 30, -52.05, 50.26, 0.00802, 27, 0.73, 21.55, 0.95634, 26, 24.22, 42.43, 0.02151, 25, 54.38, 42.43, 0.01413, 4, 30, -35.86, 45.62, 0.07734, 29, -13.82, 64.47, 0.00483, 28, 16.34, 54.32, 0.0027, 27, 16.92, 16.91, 0.91513, 4, 30, -31.96, 37.81, 0.16765, 29, -9.92, 56.66, 0.01957, 28, 20.24, 46.51, 0.02191, 27, 20.82, 9.1, 0.79088, 4, 30, -21.64, 35.86, 0.32278, 29, 0.4, 54.71, 0.03968, 28, 30.56, 44.56, 0.03872, 27, 31.14, 7.15, 0.59882, 4, 30, -12.71, 29.16, 0.45383, 29, 9.33, 48.01, 0.05444, 28, 39.49, 37.86, 0.04316, 27, 40.07, 0.45, 0.44858, 4, 30, -6.57, 19.12, 0.62258, 29, 15.47, 37.97, 0.06394, 28, 45.63, 27.82, 0.03789, 27, 46.21, -9.59, 0.27558, 4, 30, -6.01, 13.82, 0.75621, 29, 16.03, 32.67, 0.05528, 28, 46.19, 22.52, 0.02658, 27, 46.77, -14.89, 0.16193, 5, 32, -28.53, 38.67, 0.01428, 30, 2.92, 14.65, 0.94764, 29, 24.96, 33.5, 0.00335, 28, 55.12, 23.35, 0.00333, 27, 55.7, -14.06, 0.03141, 4, 32, -17.37, 35.33, 0.09251, 31, 8.28, 38.28, 0.00969, 30, 14.08, 11.31, 0.89737, 27, 66.86, -17.4, 0.00043, 3, 32, -15.13, 30.03, 0.18097, 31, 10.51, 32.97, 0.03765, 30, 16.31, 6, 0.78138, 3, 32, -1.94, 23.81, 0.60203, 31, 23.7, 26.76, 0.0519, 30, 29.5, -0.21, 0.34608, 3, 32, 10.59, 16.34, 0.91084, 31, 36.24, 19.29, 0.00163, 30, 42.04, -7.68, 0.08753, 2, 32, 18.42, 7.18, 0.98832, 30, 49.87, -16.84, 0.01168, 1, 32, 18.42, -4.15, 1, 2, 32, 18.42, -10.89, 0.99998, 31, 44.07, -7.95, 2e-05, 2, 32, 7.46, -17.72, 0.94894, 31, 33.1, -14.77, 0.05106, 2, 32, -7.48, -17.72, 0.64063, 31, 18.16, -14.77, 0.35937, 3, 32, -24.83, -14.27, 0.07266, 31, 0.81, -11.32, 0.89391, 29, 28.65, -19.44, 0.03343, 2, 31, -11.24, -12.04, 0.70761, 29, 16.6, -20.16, 0.29239, 4, 31, -19.19, -9.15, 0.40628, 30, -13.39, -36.12, 0.00017, 29, 8.65, -17.27, 0.58869, 28, 38.81, -27.42, 0.00486, 3, 31, -26.91, -11.08, 0.1634, 29, 0.93, -19.2, 0.7898, 28, 31.09, -29.35, 0.0468, 3, 31, -39.92, -11.08, 0.02161, 29, -12.08, -19.2, 0.76357, 28, 18.08, -29.35, 0.21482, 5, 31, -45.95, -7.71, 0.00419, 29, -18.11, -15.83, 0.65207, 28, 12.05, -25.98, 0.34351, 26, 36.12, -42.51, 9e-05, 24, 81.65, -18.73, 0.00015, 4, 29, -24.13, -10.76, 0.39614, 28, 6.03, -20.91, 0.59395, 26, 30.1, -37.44, 0.00739, 24, 75.63, -13.66, 0.00252, 5, 29, -34.01, -13.17, 0.13396, 28, -3.85, -23.32, 0.78927, 26, 20.22, -39.85, 0.06224, 25, 50.38, -39.85, 5e-05, 24, 65.75, -16.07, 0.01448, 5, 29, -46.3, -10.76, 0.02564, 28, -16.14, -20.91, 0.73624, 26, 7.93, -37.44, 0.18652, 25, 38.09, -37.44, 0.00689, 24, 53.46, -13.66, 0.04472, 5, 29, -54.26, -4.02, 0.00139, 28, -24.1, -14.17, 0.49019, 26, -0.03, -30.7, 0.34388, 25, 30.13, -30.7, 0.0443, 24, 45.5, -6.92, 0.12024, 4, 28, -33.98, -16.09, 0.23057, 26, -9.91, -32.62, 0.35918, 25, 20.25, -32.62, 0.11462, 24, 35.62, -8.84, 0.29564, 4, 28, -44.82, -19.95, 0.109, 26, -20.75, -36.48, 0.25013, 25, 9.41, -36.48, 0.13278, 24, 24.78, -12.7, 0.50809, 2, 33, 3.28, -1.56, 0.91021, 23, -26.62, 2.41, 0.08979, 3, 33, 20.1, -1.83, 0.29241, 25, -53.3, -26.85, 0.00239, 23, -9.8, 2.15, 0.7052, 4, 33, 35.85, 1.11, 0.00389, 25, -37.55, -23.92, 0.02901, 24, -22.18, -0.14, 0.19508, 23, 5.95, 5.08, 0.77203, 4, 33, 47.6, 9.38, 0.00191, 25, -25.8, -15.64, 0.16697, 24, -10.43, 8.14, 0.54973, 23, 17.7, 13.36, 0.28139, 5, 28, -68.01, 8.9, 0.00028, 26, -43.94, -7.63, 0.00064, 25, -13.78, -7.63, 0.53319, 24, 1.59, 16.15, 0.41578, 23, 29.72, 21.37, 0.05012, 4, 28, -54.4, 16.38, 7e-05, 25, -0.17, -0.15, 0.99572, 24, 15.2, 23.63, 0.00402, 23, 43.33, 28.85, 0.00019, 5, 28, -36.24, 19.58, 0.00094, 27, -35.66, -17.83, 0.02034, 26, -12.17, 3.05, 0.59914, 25, 17.99, 3.05, 0.37032, 24, 33.36, 26.83, 0.00926, 4, 28, -19.15, 21.45, 0.04418, 27, -18.57, -15.96, 0.15773, 26, 4.92, 4.92, 0.78507, 25, 35.08, 4.92, 0.01302, 5, 30, -52.13, 10.08, 0.03199, 29, -30.09, 28.93, 0.02242, 28, 0.07, 18.78, 0.36378, 27, 0.65, -18.63, 0.4003, 26, 24.14, 2.25, 0.18152, 5, 30, -35.84, 5.01, 0.16997, 29, -13.8, 23.86, 0.20132, 28, 16.36, 13.71, 0.34028, 27, 16.94, -23.7, 0.28286, 26, 40.43, -2.82, 0.00557, 5, 31, -29.89, 22.1, 0.00094, 30, -24.09, -4.87, 0.24563, 29, -2.05, 13.98, 0.51044, 28, 28.11, 3.83, 0.14879, 27, 28.69, -33.58, 0.09421, 5, 31, -15.21, 13.02, 0.22518, 30, -9.41, -13.95, 0.31754, 29, 12.63, 4.9, 0.45123, 28, 42.79, -5.25, 0.0006, 27, 43.37, -42.66, 0.00545, 4, 32, -25.37, 5, 0.06665, 31, 0.28, 7.95, 0.67919, 30, 6.08, -19.02, 0.21638, 29, 28.12, -0.17, 0.03777, 3, 32, -6.68, 1.79, 0.72125, 31, 18.97, 4.74, 0.21903, 30, 24.77, -22.23, 0.05972, 2, 32, 5.07, -1.41, 0.99988, 30, 36.52, -25.43, 0.00012, 3, 33, 23.03, -14.11, 0.18517, 24, -34.99, -15.36, 0.01272, 23, -6.86, -10.14, 0.80211, 2, 24, -17.1, -11.89, 0.34148, 23, 11.03, -6.67, 0.65852, 4, 28, -68.55, -10.32, 0.00217, 26, -44.48, -26.85, 0.00717, 24, 1.05, -3.07, 0.96317, 23, 29.18, 2.15, 0.02749, 4, 28, -53.6, -3.38, 0.03702, 26, -29.53, -19.91, 0.13814, 25, 0.63, -19.91, 0.28238, 24, 16, 3.87, 0.54246, 4, 28, -35.44, -1.78, 0.16384, 26, -11.37, -18.31, 0.45224, 25, 18.79, -18.31, 0.17803, 24, 34.16, 5.47, 0.20589, 6, 29, -43.97, 7.3, 0.00037, 28, -13.81, -2.85, 0.63862, 27, -13.23, -40.26, 0.00074, 26, 10.26, -19.38, 0.31753, 25, 40.42, -19.38, 0.0069, 24, 55.79, 4.4, 0.03584, 5, 30, -47.86, -16.09, 0.00143, 29, -25.82, 2.76, 0.20303, 28, 4.34, -7.39, 0.79288, 27, 4.92, -44.8, 0.00124, 24, 73.94, -0.14, 0.00142, 4, 31, -32.3, 4.21, 0.01519, 29, -4.46, -3.91, 0.88457, 28, 25.7, -14.06, 0.09972, 27, 26.28, -51.47, 0.00052, 3, 31, -18.68, -0.33, 0.35635, 30, -12.88, -27.3, 0.02251, 29, 9.16, -8.45, 0.62113, 3, 31, -4.8, -2.73, 0.87296, 30, 1, -29.7, 0.00872, 29, 23.04, -10.85, 0.11832, 3, 32, -11.22, -7.82, 0.50748, 31, 14.43, -4.87, 0.48785, 30, 20.23, -31.84, 0.00467, 2, 32, 8.01, -10.49, 0.97484, 31, 33.65, -7.54, 0.02516, 3, 32, -0.54, 10.07, 0.82697, 31, 25.11, 13.02, 0.04599, 30, 30.91, -13.95, 0.12704, 3, 32, -17.36, 15.94, 0.26246, 31, 8.29, 18.89, 0.2234, 30, 14.09, -8.08, 0.51414, 4, 32, -30.97, 21.82, 0.00726, 31, -5.33, 24.77, 0.03959, 30, 0.47, -2.2, 0.9358, 29, 22.51, 16.65, 0.01735, 5, 31, -20.28, 30.37, 0.00215, 30, -14.48, 3.4, 0.59364, 29, 7.56, 22.25, 0.21924, 28, 37.72, 12.1, 0.05596, 27, 38.3, -25.31, 0.12902, 4, 30, -23.56, 13.28, 0.40138, 29, -1.52, 32.13, 0.14092, 28, 28.64, 21.98, 0.11098, 27, 29.22, -15.43, 0.34672, 5, 30, -39.85, 23.96, 0.13128, 29, -17.81, 42.81, 0.03639, 28, 12.35, 32.66, 0.09059, 27, 12.93, -4.75, 0.739, 26, 36.42, 16.13, 0.00274, 4, 28, -6.87, 38.8, 0.00387, 27, -6.29, 1.39, 0.85499, 26, 17.2, 22.27, 0.12117, 25, 47.36, 22.27, 0.01997, 3, 27, -26.58, -0.48, 0.31149, 26, -3.09, 20.4, 0.45998, 25, 27.07, 20.4, 0.22853, 3, 27, -41.8, -4.75, 0.09097, 26, -18.31, 16.13, 0.32844, 25, 11.85, 16.13, 0.58059, 5, 27, -62.36, -11.16, 0.00852, 26, -38.87, 9.72, 0.01543, 25, -8.71, 9.72, 0.91359, 24, 6.66, 33.5, 0.03887, 23, 34.79, 38.72, 0.02358, 5, 33, 54.27, 25.94, 0.00033, 27, -72.77, -19.97, 1e-05, 25, -19.12, 0.91, 0.6623, 24, -3.75, 24.69, 0.2354, 23, 24.38, 29.91, 0.10196, 4, 33, 42.79, 18.46, 0.01877, 25, -30.6, -6.56, 0.32937, 24, -15.23, 17.22, 0.32915, 23, 12.9, 22.44, 0.32271, 4, 33, 28.91, 8.58, 0.14224, 25, -44.49, -16.44, 0.06148, 24, -29.12, 7.34, 0.09373, 23, -0.99, 12.56, 0.70255, 4, 33, 16.09, 8.58, 0.51454, 25, -57.3, -16.44, 0.01425, 24, -41.93, 7.34, 0.00618, 23, -13.8, 12.56, 0.46503, 3, 33, -0.19, 9.38, 0.9523, 25, -73.59, -15.64, 0.00035, 23, -30.09, 13.36, 0.04735, 2, 33, 2.21, -10.91, 0.82349, 23, -27.69, -6.93, 0.17651], "hull": 49, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 0, 96, 14, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126], "width": 239, "height": 92}}, "lightlf2": {"lightlf": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [116.33, -78.57, -119.67, -78.57, -119.67, 86.43, 116.33, 86.43], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 236, "height": 165}}, "tflf010": {"tflf010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [42.36, -30.6, -26.64, -30.6, -26.64, 33.4, 42.36, 33.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 69, "height": 64}}, "tflf011": {"tflf011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [25.8, -16.72, -24.2, -16.72, -24.2, 12.28, 25.8, 12.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 50, "height": 29}}, "tflf012": {"tflf012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [30.02, -29.62, -27.98, -29.62, -27.98, 26.38, 30.02, 26.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 56}}, "tflf013": {"tflf013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39.17, -30.43, -23.83, -30.43, -23.83, 33.57, 39.17, 33.57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 63, "height": 64}}, "tflf014": {"tflf014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [25.09, -26.81, -21.91, -26.81, -21.91, 22.19, 25.09, 22.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 47, "height": 49}}, "tflf015": {"tflf015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [29.15, -20.22, -24.85, -20.22, -24.85, 19.78, 29.15, 19.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 54, "height": 40}}, "tflf016": {"tflf016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32.67, -17.72, -27.33, -17.72, -27.33, 12.28, 32.67, 12.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "height": 30}}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_21": {"shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.8, 0, 0.6, 0, 0.4, 0, 0.2, 0, 0, 0.5, 0, 1, 0, 1, 0.2, 1, 0.4, 1, 0.6, 1, 0.8, 0.5, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2], "triangles": [0, 1, 13, 1, 14, 13, 13, 14, 12, 14, 15, 12, 12, 15, 11, 1, 2, 14, 2, 3, 14, 14, 3, 15, 3, 4, 15, 15, 4, 16, 15, 16, 11, 11, 16, 10, 16, 17, 10, 10, 17, 9, 17, 8, 9, 4, 5, 16, 16, 5, 17, 5, 6, 17, 17, 6, 8, 6, 7, 8], "vertices": [2, 40, 14.22, -188.87, 0.00244, 41, 53.24, -20.62, 0.99756, 1, 41, -1.36, -20.65, 1, 2, 40, -94.79, -188.87, 0.00231, 41, -55.76, -20.62, 0.99769, 2, 40, -91.46, -150.15, 0.08484, 41, -52.43, 18.1, 0.91516, 2, 40, -81.83, -109.32, 0.32327, 41, -42.8, 58.93, 0.67673, 2, 40, -69.36, -67.54, 0.63211, 41, -30.33, 100.71, 0.36789, 2, 40, -58.93, -26.45, 0.89022, 41, -19.91, 141.8, 0.10978, 2, 40, -54.89, 12.51, 0.99028, 41, -15.87, 180.76, 0.00972, 1, 40, 0, 12.64, 1, 2, 40, 54.01, 12.48, 0.98785, 41, 93.03, 180.72, 0.01215, 2, 40, 49.99, -26.47, 0.8882, 41, 89.01, 141.77, 0.1118, 2, 40, 39.58, -67.56, 0.63059, 41, 78.61, 100.69, 0.36941, 2, 40, 27.11, -109.34, 0.3218, 41, 66.14, 58.91, 0.6782, 2, 40, 17.49, -150.17, 0.08351, 41, 56.51, 18.08, 0.91649, 2, 40, -37.83, -150.44, 0.06323, 41, 1.2, 17.81, 0.93677, 2, 40, -27.56, -109.4, 0.31743, 41, 11.46, 58.85, 0.68257, 2, 40, -14.8, -67.52, 0.63346, 41, 24.22, 100.73, 0.36654, 2, 40, -3.94, -26.28, 0.90245, 41, 35.09, 141.97, 0.09755], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_21": {"type": "<PERSON><PERSON><PERSON>", "path": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00022": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00023": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00024": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00025": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00026": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00027": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00028": {"type": "<PERSON><PERSON><PERSON>", "parent": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021", "width": 109, "height": 188}}, "tflf01": {"tflf01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32.55, -22.81, -44.45, -22.81, -44.45, 35.19, 32.55, 35.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 58}}, "tflf02": {"tflf02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [30.89, -29.09, -49.11, -29.09, -49.11, 28.91, 30.89, 28.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 58}}, "tflf03": {"tflf03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [49.73, -24.57, -27.27, -24.57, -27.27, 26.43, 49.73, 26.43], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 51}}, "tflf04": {"tflf04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.25, -22.97, -30.75, -22.97, -30.75, 24.03, 35.25, 24.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 66, "height": 47}}, "tflf05": {"tflf05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [33.96, -24.99, -36.04, -24.99, -36.04, 20.01, 33.96, 20.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 45}}, "tflf06": {"tflf06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.02, -19.1, -13.98, -19.1, -13.98, 42.9, 23.02, 42.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 37, "height": 62}}, "tflf07": {"tflf07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40.08, -20.33, -29.92, -20.33, -29.92, 22.67, 40.08, 22.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 43}}, "tflf08": {"tflf08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [34.87, -40.88, -23.13, -40.88, -23.13, 41.12, 34.87, 41.12], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 82}}, "tflf09": {"tflf09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [30.15, -17.92, -26.85, -17.92, -26.85, 20.08, 30.15, 20.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "fxdt": {"fxdt": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [109.39, -32.66, -124.61, -32.33, -124.41, 113.67, 109.59, 113.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 234, "height": 146}}}}], "animations": {"a0-1": {"slots": {"dk": {"attachment": [{"name": null}]}, "tflf05": {"attachment": [{"name": null}]}, "tflf010": {"attachment": [{"name": null}]}, "fxdt": {"attachment": [{"name": "fxdt"}]}, "tflf01": {"attachment": [{"name": null}]}, "tflf015": {"attachment": [{"name": null}]}, "tflf013": {"attachment": [{"name": null}]}, "tflf014": {"attachment": [{"name": null}]}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_9": {"attachment": [{"name": null}]}, "yun": {"attachment": [{"name": null}]}, "tflf03": {"attachment": [{"name": null}]}, "tflf07": {"attachment": [{"name": null}]}, "tflf08": {"attachment": [{"name": null}]}, "tflf06": {"attachment": [{"name": null}]}, "tflf09": {"attachment": [{"name": null}]}, "tflf011": {"attachment": [{"name": null}]}, "tflf02": {"attachment": [{"name": null}]}, "tflf04": {"attachment": [{"name": null}]}, "tflf016": {"attachment": [{"name": null}]}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009": {"attachment": [{"name": null}]}, "tflf012": {"attachment": [{"name": null}]}}}, "a01": {"slots": {"dk": {"attachment": [{"name": null}, {"time": 0.9, "name": "dk"}]}, "fxdt": {"color": [{"time": 0.0667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff00"}], "attachment": [{"name": "fxdt"}]}, "yun": {"color": [{"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff"}], "attachment": [{"name": null}, {"time": 0.9333, "name": "yun"}]}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_9": {"attachment": [{"name": null}]}, "lightlf2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.8333, "name": "lightlf"}]}, "lightlf": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0.8333, "name": "lightlf"}]}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009": {"attachment": [{"name": null}]}}, "bones": {"dflight": {"scale": [{"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 1.106, "y": 1.106}]}, "tongfuall3": {"rotate": [{"angle": 63.69, "curve": "stepped"}, {"time": 0.1, "angle": 63.69, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "tongfuall4": {"rotate": [{"angle": 92.8, "curve": "stepped"}, {"time": 0.0333, "angle": 92.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "tongfuall6": {"rotate": [{"angle": 43.64, "curve": "stepped"}, {"time": 0.1333, "angle": 43.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}], "translate": [{"y": -41.61, "curve": "stepped"}, {"time": 0.1333, "y": -41.61, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.5667, "y": -2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "tongfuall7": {"rotate": [{"angle": -66.17, "curve": "stepped"}, {"time": 0.1, "angle": -66.17, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "translate": [{"y": -43.67, "curve": "stepped"}, {"time": 0.1, "y": -43.67, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.5333, "y": -2.56, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "tongfuall8": {"rotate": [{"time": 0.2333, "angle": 58.92, "curve": 0.25, "c3": 0.75}, {"time": 0.9}], "translate": [{"y": -77.04, "curve": "stepped"}, {"time": 0.2333, "y": -77.04, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.6667, "y": -4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9}]}, "tongfuall9": {"rotate": [{"angle": -107.37, "curve": "stepped"}, {"time": 0.2, "angle": -107.37, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"y": -73.34, "curve": "stepped"}, {"time": 0.2, "y": -73.34, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.6333, "y": -4.31, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "tongfuall10": {"rotate": [{"angle": 39.35, "curve": "stepped"}, {"time": 0.1, "angle": 39.35, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "translate": [{"y": -23.48, "curve": "stepped"}, {"time": 0.1, "y": -23.48, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.5333, "y": -1.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "tongfuall12": {"rotate": [{"angle": -101.75, "curve": "stepped"}, {"time": 0.1, "angle": -101.75, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "translate": [{"y": -25.96, "curve": "stepped"}, {"time": 0.1, "y": -25.96, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.5333, "y": -1.52, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "tongfuall13": {"translate": [{"y": -63.86, "curve": "stepped"}, {"time": 0.2, "y": -63.86, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.6333, "y": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "tongfuall14": {"rotate": [{"angle": 60.25, "curve": "stepped"}, {"time": 0.3667, "angle": 60.25, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"y": -92.7, "curve": "stepped"}, {"time": 0.3667, "y": -92.7, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.7667, "y": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "tongfuall15": {"rotate": [{"angle": 121.19, "curve": "stepped"}, {"time": 0.4333, "angle": 121.19, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}], "translate": [{"y": -88.58, "curve": "stepped"}, {"time": 0.4333, "y": -88.58, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.8333, "y": -5.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "tongfuall16": {"rotate": [{"angle": -85.61, "curve": "stepped"}, {"time": 0.4333, "angle": -85.61, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}], "translate": [{"y": -89.82, "curve": "stepped"}, {"time": 0.4333, "y": -89.82, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.8333, "y": -5.27, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "tongfuall17": {"rotate": [{"angle": -106.99, "curve": "stepped"}, {"time": 0.4667, "angle": -106.99, "curve": 0.25, "c3": 0.75}, {"time": 1.1}], "translate": [{"y": -128.96, "curve": "stepped"}, {"time": 0.4667, "y": -128.96, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.9, "y": -7.57, "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}, "tongfuall18": {"rotate": [{"angle": 104.59, "curve": "stepped"}, {"time": 0.4, "angle": 104.59, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}], "translate": [{"y": -130.19, "curve": "stepped"}, {"time": 0.4, "y": -130.19, "curve": 0.25, "c3": 0.679, "c4": 3.68}, {"time": 0.8, "y": -7.65, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "tongfuall2": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.455, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "tongfuall19": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "y": 0.836, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.159, "y": 0.927}]}}, "deform": {"default": {"tflf07": {"tflf07": [{"vertices": [-20.09628, -16.77737, -5.8801, 25.50937, 20.0961, 16.77643, 5.8799, -25.51004]}]}}}}, "a01-1": {"slots": {"tflf05": {"attachment": [{"name": null}]}, "tflf016": {"attachment": [{"name": null}]}, "tflf010": {"attachment": [{"name": null}]}, "tflf06": {"attachment": [{"name": null}]}, "tflf01": {"attachment": [{"name": null}]}, "tflf015": {"attachment": [{"name": null}]}, "tflf013": {"attachment": [{"name": null}]}, "tflf014": {"attachment": [{"name": null}]}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_9": {"color": [{"color": "ffffff93"}], "attachment": [{"name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00015"}, {"time": 0.0333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00016"}, {"time": 0.0667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00017"}, {"time": 0.1, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00018"}, {"time": 0.1333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00019"}, {"time": 0.1667, "name": null}, {"time": 2.0667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_9"}, {"time": 2.1, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00010"}, {"time": 2.1333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00011"}, {"time": 2.1667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00012"}, {"time": 2.2, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00013"}, {"time": 2.2333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00014"}]}, "tflf03": {"attachment": [{"name": null}]}, "tflf07": {"attachment": [{"name": null}]}, "tflf08": {"attachment": [{"name": null}]}, "tflf09": {"attachment": [{"name": null}]}, "tflf011": {"attachment": [{"name": null}]}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_21": {"color": [{"color": "ffffff93"}], "attachment": [{"time": 1.6333, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_21"}, {"time": 1.6667, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00022"}, {"time": 1.7, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00023"}, {"time": 1.7333, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00024"}, {"time": 1.7667, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00025"}, {"time": 1.8, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00026"}, {"time": 1.8333, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00027"}, {"time": 1.8667, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00028"}, {"time": 1.9, "name": null}]}, "tflf02": {"attachment": [{"name": null}]}, "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00021": {"color": [{"color": "ffffff93"}], "attachment": [{"time": 1.1333, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_21"}, {"time": 1.1667, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00022"}, {"time": 1.2, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00023"}, {"time": 1.2333, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00024"}, {"time": 1.2667, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00025"}, {"time": 1.3, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00026"}, {"time": 1.3333, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00027"}, {"time": 1.3667, "name": "shandian/gwsl_skill1_d2/gwsl_skill1_d2_00028"}, {"time": 1.4, "name": null}]}, "tflf04": {"attachment": [{"name": null}]}, "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00009": {"color": [{"color": "ffffff93"}], "attachment": [{"name": null}, {"time": 0.7, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_9"}, {"time": 0.7333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00010"}, {"time": 0.7667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00011"}, {"time": 0.8, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00012"}, {"time": 0.8333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00013"}, {"time": 0.8667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00014"}, {"time": 0.9, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00015"}, {"time": 0.9333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00016"}, {"time": 0.9667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00017"}, {"time": 1, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00018"}, {"time": 1.0333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00019"}, {"time": 1.0667, "name": null}, {"time": 1.8667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00010"}, {"time": 1.9333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00011"}, {"time": 1.9667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00012"}, {"time": 2, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00013"}, {"time": 2.0333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00014"}, {"time": 2.0667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00015"}, {"time": 2.1, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00016"}, {"time": 2.1333, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00017"}, {"time": 2.1667, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00018"}, {"time": 2.2, "name": "shandian/gwsl_skill1_d1/gwsl_skill1_d1_00019"}, {"time": 2.2333, "name": null}]}, "tflf012": {"attachment": [{"name": null}]}}, "bones": {"bone12": {"translate": [{"y": 8.89, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "y": 9.29, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.2333, "y": 8.89}]}, "bone11": {"translate": [{"y": 8.89, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "y": 9.29, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.2333, "y": 8.89}]}, "bone10": {"translate": [{"y": 8.08, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "y": 9.29, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.2333, "y": 8.08}]}, "bone9": {"translate": [{"y": 8.89, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "y": 9.29, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.2333, "y": 8.89}]}, "bone8": {"translate": [{"y": 9.29, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "y": 9.29}]}, "bone7": {"translate": [{"y": 8.71, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "y": 9.29, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.2333, "y": 8.71}]}, "bone6": {"translate": [{"y": 7.57, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 9.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.2333, "y": 7.57}]}, "bone5": {"translate": [{"y": 6.17, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "y": 9.29, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.2333, "y": 6.17}]}, "bone4": {"translate": [{"y": 4.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "y": 9.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2333, "y": 4.64}]}, "bone3": {"translate": [{"y": 1.71, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 9.29, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.2333, "y": 1.71}]}, "bone2": {"translate": [{"y": 1.71, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 9.29, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.2333, "y": 1.71}]}, "bone": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 1.091, "y": 1.175, "curve": 0.25, "c3": 0.75}, {"time": 2.2333}]}, "root": {"translate": [{"x": 0.66}]}}}}}