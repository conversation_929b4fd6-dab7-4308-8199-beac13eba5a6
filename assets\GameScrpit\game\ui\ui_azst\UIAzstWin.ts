import { _decorator, Label, Node, RichText, sp, Tween, tween, v3 } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { tweenTagEnum } from "../../GameDefine";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import FmUtils from "../../../lib/utils/FmUtils";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { ShenjiEnum } from "../../../lib/common/ShenjiEnum";
import { AzstAudioName } from "../../../module/azst/AzstConfig";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";

const { ccclass, property } = _decorator;

@ccclass("UIAzstWin")
export class UIAzstWin extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_AZST}?prefab/ui/UIAzstWin`;
  }

  private _heInfo: { name: string; point: number; pointCh: number; avatarList: number[] } = null;
  private _myInfo: { name: string; point: number; pointCh: number; avatarList: number[] } = null;
  private _award: any = null;
  private _triggerShenji: boolean = false;

  public init(args: any): void {
    super.init(args);
    this._myInfo = args.myInfo;
    this._heInfo = args.heInfo;
    this._award = args.award;
    this._triggerShenji = args.trigger;
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);
    this.playeAction();
    this.setInfo(this.getNode("spr_right_info"), this._heInfo, false);
    this.setInfo(this.getNode("spr_left_info"), this._myInfo, true);
    AudioMgr.instance.playEffect(AzstAudioName.Effect.战斗胜利);
    // 是否触发神迹
    if (this._triggerShenji) {
      MsgMgr.emit(MsgEnum.ON_TRIGGER_SHENJI, ShenjiEnum.演武神迹_101);
    }
  }

  private setInfo(
    node: Node,
    info: { name: string; point: number; pointCh: number; avatarList: number[] },
    isWin: boolean
  ) {
    node.getChildByName("name").getComponent(Label).string = info.name;

    let str = `<color=#fff9cf>积分：${info.point}</color><color=#74ff77>（+${info.pointCh}）</color>`;
    if (isWin == false) str = `<color=#fff9cf>积分：${info.point}</color><color=#ff9b9b>（-${info.pointCh}）</color>`;
    node.getChildByName("point").getComponent(RichText).string = str;

    let param = ToolExt.newPlayerBaseMessage();
    param.avatarList = info.avatarList;
    param.nickname = info.name;
    param.userId = 0;
    FmUtils.setHeaderNode(node.getChildByName("head"), param);
  }

  private playeAction() {
    this.getNode("bg_spr").scale = v3(0, 0, 0);

    this.getNode("victory_appear")
      .getComponent(sp.Skeleton)
      .setEventListener((animation, event) => {
        if (event["data"].name == "appear") {
          tween(this.getNode("bg_spr"))
            .tag(tweenTagEnum.UIAzstWin_Tag)
            .to(0.1, { scale: v3(1, 1, 1) })
            .call(() => {
              let list = ToolExt.traAwardItemMapList(this._award);
              this.loadItem(list);
            })
            .start();
          this.getNode("victory_appear").getComponent(sp.Skeleton).setEventListener(null);
        }
      });
    this.getNode("victory_appear")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("victory_appear" == trackEntry.animation.name) {
          this.getNode("victory_appear").getComponent(sp.Skeleton).setAnimation(0, "victory_lasts", true);
          this.getNode("victory_appear").getComponent(sp.Skeleton).setCompleteListener(null);
        }
      });

    this.getNode("victory_appear").getComponent(sp.Skeleton).setAnimation(0, "victory_appear", false);
  }

  private loadItem(layerList: Array<{ id: number; num: number }>) {
    for (let i = 0; i < layerList.length; i++) {
      let node = ToolExt.clone(this.getNode("item_top"), this);
      node.active = true;
      node["Item"].active = false;
      this.getNode("itemContent").addChild(node);

      this.itemAct(node["Item"], layerList[i].id, layerList[i].num, i);
    }
  }

  private itemAct(node: Node, id: number, num: number, index) {
    FmUtils.setItemNode(node, id, num);
    node.scale = v3(0, 0, 0);
    node.active = true;
    tween(node)
      .tag(tweenTagEnum.UIAzstWin_Tag)
      .delay(0.08 * index)
      .to(0.16, { scale: v3(1, 1, 1) })
      .start();
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    Tween.stopAllByTag(tweenTagEnum.UIAzstWin_Tag);
    this.getNode("victory_appear").getComponent(sp.Skeleton).setEventListener(null);
    this.getNode("victory_appear").getComponent(sp.Skeleton).setCompleteListener(null);
  }
}
