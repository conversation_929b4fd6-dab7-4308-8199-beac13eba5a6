import { _decorator } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Sun May 25 2025 10:41:06 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_fracture/UIFractureUnlockSuccess.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIFractureUnlockSuccess")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureUnlockSuccess",
  nextHop: [],
  exit: "",
})
export class UIFractureUnlockSuccess extends BaseCtrl {
  protected start(): void {
    super.start();
    AudioMgr.instance.playEffect(AudioName.Effect.通用升级);
  }
  update(deltaTime: number) {}
}
