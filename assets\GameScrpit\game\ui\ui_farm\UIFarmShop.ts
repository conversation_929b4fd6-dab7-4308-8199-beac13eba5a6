import { _decorator, instantiate, Label, Node, tween, v3 } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UINode } from "../../../lib/ui/UINode";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PublicRouteName } from "../../../module/player/PlayerConstant";
import { ShopConfig } from "../../../module/club/ClubConfig";
import { GoodsRedeemMessage } from "../../net/protocol/Goods";
import FmUtils from "../../../lib/utils/FmUtils";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { BuyConfirm } from "../UIBuyConfirm";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { FarmAudioName } from "../../../module/farm/FarmConfig";
import { Sleep } from "../../GameDefine";
const { ccclass, property } = _decorator;

@ccclass("UIFarmShop")
export class UIFarmShop extends UINode {
  protected _openAct: boolean = true;

  private _shopList: any[] = [];
  private _goodsRedeemMsg: GoodsRedeemMessage;
  private _nodeList: Node;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FARM}?prefab/ui/UIFarmShop`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  protected onEvtShow(): void {
    this._nodeList = this.getNode("shop_list_content");
    this._nodeList.removeAllChildren();
    this.initShopList();
  }

  private async initShopList() {
    // 设置货币数量
    this.getNode("lbl_money").getComponent(Label).string = PlayerModule.data.getItemNum(1101) + "";
    this.getNode("lbl_money2").getComponent(Label).string = PlayerModule.data.getItemNum(1102) + "";

    // 取商品买过的次数
    this._goodsRedeemMsg = GoodsModule.data.getGoodsRedeemMsg();
    this._shopList = [];
    let arr = [];
    // 过滤出福地商品
    Object.values(JsonMgr.instance.jsonList.c_shop).forEach((val: ShopConfig) => {
      if (val.type == 10) {
        let remainTime = val.max - (this._goodsRedeemMsg.redeemMap[val.id] || 0);
        if (remainTime <= 0) {
          arr.push(val);
        } else {
          this._shopList.push(val);
        }
      }
    });

    // 排序
    this._shopList = this._shopList.sort((a, b) => a.sort - b.sort);
    this._shopList = this._shopList.concat(arr);

    for (let idx = 0; idx < this._shopList.length; idx++) {
      let shopItem = this._shopList[idx];

      let cfgItem = JsonMgr.instance.getConfigItem(shopItem.itemsList[0][0]);
      let nodeGoods = this._nodeList.children[idx];
      if (!nodeGoods) {
        await Sleep(0.03);
        nodeGoods = instantiate(this.getNode("shop_viewholder"));
        nodeGoods.setScale(0, 0, 1);
        tween(nodeGoods)
          .to(0.2, { scale: v3(1, 1, 1) })
          .start();
      }

      nodeGoods.getChildByName("lbl_title").getComponent(Label).string = cfgItem.name;

      // 设置商品信息
      FmUtils.setItemNode(nodeGoods.getChildByName("Item"), shopItem.itemsList[0][0], shopItem.itemsList[0][1]);

      const btnBuy = nodeGoods.getChildByName("btn_buy");

      // 货币图标
      FmUtils.setItemIcon(btnBuy.getChildByPath("layout/node_icon/bg"), shopItem.cointype);

      // 商品价格
      const lblPrice = btnBuy.getChildByPath("layout/lbl_price").getComponent(Label);
      // 加价处理
      let priceChange = 0;
      if (shopItem.priceAdd) {
        let buyTimes = this._goodsRedeemMsg.redeemMap[shopItem.id] || 0;
        if (buyTimes) {
          priceChange = buyTimes * shopItem.priceAdd;
        }
      }
      const price = shopItem.coinPrice + priceChange;
      lblPrice.string = `${price}`;

      // 售罄状态
      const nodeLblLimit = nodeGoods.getChildByName("lbl_limit");
      const nodeSellOut = nodeGoods.getChildByName("sell_out");
      nodeSellOut.active = false;
      btnBuy.active = true;
      nodeLblLimit.active = false;

      // 最大可买
      let remainTime = -1;
      if (shopItem.max > 0) {
        remainTime = shopItem.max - (this._goodsRedeemMsg.redeemMap[shopItem.id] || 0);
        if (remainTime <= 0) {
          nodeSellOut.active = true;
          btnBuy.active = false;
          remainTime = 1;
        } else {
          let rs = ToolExt.getMaxtypeLab(shopItem.maxtype);
          if (rs) {
            rs = rs + "(" + remainTime + "/" + shopItem.max + ")";
          }
          nodeLblLimit.active = true;
          nodeLblLimit.getComponent(Label).string = rs;
        }
      }

      nodeGoods.active = true;
      this._nodeList.addChild(nodeGoods);

      btnBuy.off(Node.EventType.TOUCH_END);
      // 购买事件
      btnBuy.on(
        Node.EventType.TOUCH_END,
        (event: Event) => {
          AudioMgr.instance.playEffect(FarmAudioName.Effect.福地商店进行兑换);

          const buyConfirm: BuyConfirm = {
            itemInfo: shopItem.itemsList[0],
            moneyInfo: [shopItem.cointype, price],
            maxNum: remainTime,
            increase: shopItem.priceAdd,
          };

          UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
            if (resp.ok) {
              GoodsModule.api.redeemGoods(shopItem.id, resp.num, (resp) => {
                this.initShopList();
                MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: resp });
              });
            }
          });
        },
        btnBuy
      );
    }
  }
}
