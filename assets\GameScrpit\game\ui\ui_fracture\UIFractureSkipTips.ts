import { _decorator } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { FractureModule } from "../../../module/fracture/FractureModule";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

@ccclass("UIFractureSkipTips")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureSkipTips",
  nextHop: [],
  exit: "",
})
export class UIFractureSkipTips extends BaseCtrl {
  public playShowAni: boolean = true;
  start() {
    super.start();
    this.getNode("node_check").active = FractureModule.config.fracture_skip_tips;
  }

  update(deltaTime: number) {}

  private on_click_btn_cancel() {
    //
    this.closeBack(false);
  }
  private on_click_btn_ok() {
    //
    this.closeBack(true);
  }
  private on_click_btn_no_tips() {
    this.getNode("node_check").active = !this.getNode("node_check").active;
    FractureModule.config.fracture_skip_tips = this.getNode("node_check").active;
  }
}
