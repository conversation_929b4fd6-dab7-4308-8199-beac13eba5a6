import { GameDirector } from "../../game/GameDirector";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { MainTaskSubCmd } from "../../game/net/cmd/CmdData";
import { IntValue } from "../../game/net/protocol/ExternalMessage";
import { MainTaskMessage, MainTaskPassResponse } from "../../game/net/protocol/MainTask";
import { MainTaskModule } from "./MainTaskModule";

export default class MainTaskApi {
  /**
   * 获取当前主线任务的情况
   * @param success
   */
  public getMainTaskInfo(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(MainTaskMessage, MainTaskSubCmd.mainTaskInfo, null, (data: MainTaskMessage) => {
      MainTaskModule.data.mainTaskMsg = data;
      success && success(data);
    });
  }

  /**
   * 完成主线任务并获取下一个任务的完成情况，如果当前任务未完成，则返回当前任务的最新完成情况
   * @param success
   */
  public passMainTask(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      MainTaskPassResponse,
      MainTaskSubCmd.passMainTask,
      null,
      (data: MainTaskPassResponse) => {
        MainTaskModule.data.mainTaskMsg = data.mainTask;
        // 检查模块开启
        GameDirector.instance.checkAndLoadModule(true);
        success && success(data);
      }
    );
  }

  /**
   * 获取当前主线任务的情况
   * @param success
   */
  public clientPassTask(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(MainTaskMessage, MainTaskSubCmd.clientPassTask, null, (data: MainTaskMessage) => {
      success && success(data);
    });
  }

  /**
   * 重置主线任务进度
   * @param success
   */
  public testResetMainTask(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(MainTaskMessage, MainTaskSubCmd.testResetMainTask, null, (data: MainTaskMessage) => {
      success && success(data);
    });
  }

  public testGoToMainTask(taskId: number, success?: ApiHandlerSuccess) {
    let data: IntValue = {
      value: taskId,
    };
    ApiHandler.instance.request(
      MainTaskMessage,
      MainTaskSubCmd.testSetMainTaskId,
      IntValue.encode(data),
      (data: MainTaskMessage) => {
        MainTaskModule.data.mainTaskMsg = data;
        success && success(data);
      }
    );
  }
}
