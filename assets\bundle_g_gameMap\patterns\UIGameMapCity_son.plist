<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>S0829.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{45,218}</string>
                <key>spriteSourceSize</key>
                <string>{45,218}</string>
                <key>textureRect</key>
                <string>{{1,1},{45,218}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0831.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{58,174}</string>
                <key>spriteSourceSize</key>
                <string>{58,174}</string>
                <key>textureRect</key>
                <string>{{142,1},{58,174}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0832.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{116,129}</string>
                <key>spriteSourceSize</key>
                <string>{116,129}</string>
                <key>textureRect</key>
                <string>{{95,221},{116,129}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0834.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{45,218}</string>
                <key>spriteSourceSize</key>
                <string>{45,218}</string>
                <key>textureRect</key>
                <string>{{1,221},{45,218}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0846.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{107,101}</string>
                <key>spriteSourceSize</key>
                <string>{107,101}</string>
                <key>textureRect</key>
                <string>{{95,352},{107,101}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0848.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{45,218}</string>
                <key>spriteSourceSize</key>
                <string>{45,218}</string>
                <key>textureRect</key>
                <string>{{48,1},{45,218}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0849.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{45,218}</string>
                <key>spriteSourceSize</key>
                <string>{45,218}</string>
                <key>textureRect</key>
                <string>{{48,221},{45,218}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0850.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{45,218}</string>
                <key>spriteSourceSize</key>
                <string>{45,218}</string>
                <key>textureRect</key>
                <string>{{95,1},{45,218}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UIGameMapCity_son.png</string>
            <key>size</key>
            <string>{212,454}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:509ba90e629b224f052c9c3c64e1ddef:6076108a87306180fdced35926f41c70:b538b9c3ff798780387bfa4a80db06ba$</string>
            <key>textureFileName</key>
            <string>UIGameMapCity_son.png</string>
        </dict>
    </dict>
</plist>
