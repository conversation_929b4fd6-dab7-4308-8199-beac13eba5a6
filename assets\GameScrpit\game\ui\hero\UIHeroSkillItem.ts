import { _decorator, find, Label, Node, Sprite } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { Star } from "../../common/Star";
import { HeroModule } from "../../../module/hero/HeroModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import ResMgr from "../../../lib/common/ResMgr";
import { IConfigHeroSkill } from "../../JsonDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * i<PERSON>_huang
 * Thu May 23 2024 14:54:31 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/hero/UIHeroSkillItem.ts
 *
 */

@ccclass("UIHeroSkillItem")
export class UIHeroSkillItem extends BaseCtrl {
  //itemLayout/Node/skillName
  txtSkillName: Node;
  //itemLayout/Node/skillLv
  txtSkillLv: Node;
  //itemLayout/Node/skillQualification
  txtSkillQualification: Node;
  // itemLayout/bgSkill/mask/iconSkill
  iconSkill: Node;
  // itemLayout/bgSkill/bgSkillStart/layoutStart
  layoutStart: Node;
  // itemLayout/bgSkill/bgSkillStart/layoutStart/spStart
  spStart: Node;
  @property(Node)
  lock: Node;
  @property(Node)
  select: Node;

  //skillinfo
  skillInfo: IConfigHeroSkill;
  level: number;
  heroId: number;
  start() {}
  public open(...param: any[]) {
    log.log("====HeroSkillItem====");
    log.log(param[0]);
    this.skillInfo = param[0];
    this.level = param[1];
    this.heroId = param[2];
    this.findNode();
    this.refreshUI();
    MsgMgr.on(MsgEnum.ON_HERO_UPDATE, this.heroChange, this);
  }
  protected findNode(): void {
    this.txtSkillName = find("itemLayout/Node/skillName", this.node);
    this.txtSkillLv = find("itemLayout/Node/skillLv", this.node);
    this.txtSkillQualification = find("itemLayout/Node/skillQualification", this.node);
    this.iconSkill = find("itemLayout/bgSkill/mask/iconSkill", this.node);
    this.layoutStart = find("itemLayout/bgSkill/bgSkillStart/layoutStart", this.node);
    this.spStart = find("spStart", this.layoutStart);
    // this.lock = ("lock", this.node);
  }
  private heroChange() {
    log.log("====HeroSkillItem====");
    log.log(this.heroId);
    let hero = HeroModule.data.getHeroMessage(this.heroId);
    if (!hero) {
      return;
    }
    this.level = hero.skillMap[this.skillInfo.id];
    this.refreshUI();
  }
  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_HERO_UPDATE, this.heroChange, this);
  }
  update(deltaTime: number) {}
  private refreshUI() {
    this.txtSkillName.getComponent(Label).string = `${this.skillInfo.name}`;
    let quality = HeroModule.service.getSkillQuality(this.skillInfo.id, this.level);

    this.txtSkillLv.getComponent(Label).string = `等级 ${this.level ?? 0}`;
    this.txtSkillQualification.getComponent(Label).string = `资质 ${quality}`;
    if (this.level) {
      // this.iconSkill.getComponent(Sprite).grayscale = false;
      this.lock.active = false;
    } else {
      // this.iconSkill.getComponent(Sprite).grayscale = true;
    }

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_G_HERO,
      `atlas_hero_skill/heroskill_${this.skillInfo.iconId}`,
      this.iconSkill.getComponent(Sprite)
    );
    // this.iconSkill.getComponent(Sprite).changeSpriteFrameFromAtlas(`heroskill_${this.skillInfo.iconId}`);
    this.layoutStart.getComponent(Star).setStar(this.skillInfo.star);
    //
    // for (let i = 1; i < this.skillInfo.star; i++) {
    //   let start = instantiate(this.spStart);
    //   this.layoutStart.addChild(start);
    // }
  }

  public updateData(...param: any[]) {
    this.skillInfo = param[0];
    this.level = param[1];
    this.refreshUI();
  }
  public setSelect(tag: boolean) {
    if (tag) {
      this.select.active = true;
    } else {
      this.select.active = false;
    }
  }
}
