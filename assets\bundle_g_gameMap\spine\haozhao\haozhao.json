{"skeleton": {"hash": "LoDjuXmXrmgDhPeEgRI6aTHXLns=", "spine": "3.8.75", "x": -469.25, "y": -125, "width": 810.17, "height": 480.99, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "hecheng", "parent": "root"}, {"name": "xuan<PERSON>di", "parent": "hecheng", "rotation": 35, "scaleX": 0.4542, "scaleY": 0.2687}, {"name": "huan2", "parent": "xuan<PERSON>di", "scaleX": 3.8474, "scaleY": 3.8474}, {"name": "glow_orange2", "parent": "hecheng", "scaleX": 2.3358, "scaleY": 2.3358}, {"name": "nws1", "parent": "root", "x": -445.79, "y": 334.53}, {"name": "ren1", "parent": "root", "x": -153.22, "y": -27.78}, {"name": "p1", "parent": "ren1", "length": 24.4, "rotation": -179.28, "x": -292.9, "y": 326.14}, {"name": "ren2", "parent": "root", "y": -126.83}, {"name": "p2", "parent": "ren2", "length": 22.19, "rotation": 179.11, "x": 3.16, "y": 26.34}, {"name": "p3", "parent": "root", "length": 20.65, "rotation": 180, "x": 174.47, "y": -102.09}, {"name": "p4", "parent": "root", "length": 23.74, "rotation": -179.07, "x": 234.11, "y": -104.41}, {"name": "p5", "parent": "root", "length": 20.49, "rotation": 177.1, "x": 317.69, "y": -106.56}, {"name": "p6", "parent": "p1", "length": 7.45, "rotation": -81.25, "x": -1.77, "y": -0.7}, {"name": "p7", "parent": "p6", "length": 5.74, "rotation": 7.04, "x": 7.45}, {"name": "p8", "parent": "p7", "length": 4.08, "rotation": -16.5, "x": 5.74}, {"name": "p9", "parent": "p7", "length": 9.34, "rotation": -162.64, "x": -0.34, "y": -7.24}, {"name": "p10", "parent": "p9", "length": 8.19, "rotation": -14.98, "x": 9.34}, {"name": "p11", "parent": "p7", "length": 7.9, "rotation": 143.89, "x": 2.37, "y": 8.34}, {"name": "p12", "parent": "p11", "length": 7.42, "rotation": 6.9, "x": 7.9}, {"name": "p13", "parent": "p1", "length": 8.16, "rotation": 87.85, "x": -3.17, "y": 1.97}, {"name": "p14", "parent": "p13", "length": 8.67, "rotation": 0.76, "x": 8.16}, {"name": "p15", "parent": "p1", "length": 7.45, "rotation": 65.88, "x": 4.4, "y": 3.71}, {"name": "p16", "parent": "p15", "length": 7.38, "rotation": -17.11, "x": 7.45}, {"name": "p17", "parent": "p1", "length": 8.42, "rotation": 108.38, "x": -8.66, "y": 2.96}, {"name": "p18", "parent": "p17", "length": 7.5, "rotation": -4.11, "x": 8.42}, {"name": "p19", "parent": "p1", "length": 8.3, "rotation": 73.91, "x": 3.85, "y": 4.58}, {"name": "p20", "parent": "p19", "length": 9.71, "rotation": -0.15, "x": 8.3}, {"name": "p21", "parent": "p1", "length": 7.96, "rotation": 99.12, "x": -5.22, "y": 5.49}, {"name": "p22", "parent": "p21", "length": 10.49, "rotation": -7.87, "x": 7.96}, {"name": "p23", "parent": "p2", "length": 6.68, "rotation": -77.62, "x": -0.03, "y": -1.14}, {"name": "p24", "parent": "p23", "length": 5.48, "rotation": 6.2, "x": 6.66, "y": -0.11}, {"name": "p25", "parent": "p24", "length": 5.03, "rotation": -31.74, "x": 5.48}, {"name": "p26", "parent": "p24", "length": 9.62, "rotation": 152.06, "x": 3.24, "y": 6.33}, {"name": "p27", "parent": "p26", "length": 7.87, "rotation": 0.24, "x": 9.62}, {"name": "p28", "parent": "p24", "length": 9.49, "rotation": 178.34, "x": -1.24, "y": -7.32}, {"name": "p29", "parent": "p28", "length": 7.88, "rotation": -1.8, "x": 9.49}, {"name": "p30", "parent": "p2", "length": 7.5, "rotation": 47.74, "x": 3.36, "y": 2.48}, {"name": "p31", "parent": "p30", "length": 8.97, "rotation": 1.76, "x": 7.5}, {"name": "p32", "parent": "p2", "length": 7.68, "rotation": 84.92, "x": -2.12, "y": 2.74}, {"name": "p33", "parent": "p32", "length": 6.78, "rotation": -13.69, "x": 7.68}, {"name": "p34", "parent": "p2", "length": 9.54, "rotation": 120.21, "x": -5.75, "y": 1.54}, {"name": "p35", "parent": "p34", "length": 7.41, "rotation": -29.32, "x": 9.54}, {"name": "p36", "parent": "p2", "length": 7.45, "rotation": 68.4, "x": 3.03, "y": 6.51}, {"name": "p37", "parent": "p36", "length": 7.11, "rotation": 1.93, "x": 7.45}, {"name": "p38", "parent": "p2", "length": 7.4, "rotation": 98.56, "x": -2.67, "y": 5.59}, {"name": "p39", "parent": "p38", "length": 8.66, "rotation": -3.2, "x": 7.4}, {"name": "p40", "parent": "p3", "length": 6.56, "rotation": -91.38, "x": -0.94, "y": -1.26}, {"name": "p41", "parent": "p40", "length": 5.18, "rotation": 17.33, "x": 6.56}, {"name": "p42", "parent": "p41", "length": 2.63, "rotation": -22.86, "x": 5.18}, {"name": "p43", "parent": "p3", "length": 7.68, "rotation": 66.34, "x": 4.82, "y": 2.38}, {"name": "p44", "parent": "p43", "length": 6.09, "rotation": -10.23, "x": 7.68}, {"name": "p45", "parent": "p3", "length": 6.3, "rotation": 107.53, "x": -4.34, "y": 1.82}, {"name": "p46", "parent": "p45", "length": 7.49, "rotation": -10.25, "x": 6.3}, {"name": "p47", "parent": "p41", "length": 7.47, "rotation": 141.02, "x": 2.5, "y": 9.89}, {"name": "p48", "parent": "p47", "length": 8.88, "rotation": 13.83, "x": 7.47}, {"name": "p49", "parent": "p41", "length": 9.8, "rotation": -175.17, "x": 0.89, "y": -6.66}, {"name": "p50", "parent": "p49", "length": 10.12, "rotation": -18.98, "x": 9.8}, {"name": "p51", "parent": "p3", "length": 6.65, "rotation": 76.95, "x": 4.98, "y": 7.43}, {"name": "p52", "parent": "p51", "length": 7.17, "rotation": -3.59, "x": 6.65}, {"name": "p53", "parent": "p3", "length": 7.6, "rotation": 86.42, "x": -2.68, "y": 5.77}, {"name": "p54", "parent": "p53", "length": 6.32, "rotation": 2.14, "x": 7.6}, {"name": "p55", "parent": "p4", "length": 7.06, "rotation": -78.67, "x": -1.37, "y": -0.66}, {"name": "p56", "parent": "p55", "length": 5.41, "rotation": 0.54, "x": 7.06}, {"name": "p57", "parent": "p56", "length": 3.43, "rotation": -16.82, "x": 5.41}, {"name": "p58", "parent": "p56", "length": 9.61, "rotation": -179.81, "x": -1, "y": -8.45}, {"name": "p59", "parent": "p58", "length": 8.46, "rotation": 0.55, "x": 9.61}, {"name": "p60", "parent": "p56", "length": 6.99, "rotation": 146.58, "x": 2.34, "y": 8.94}, {"name": "p61", "parent": "p60", "length": 8.69, "rotation": 5.81, "x": 6.99}, {"name": "p62", "parent": "p4", "length": 7.77, "rotation": 50.34, "x": 2.64, "y": -1.14}, {"name": "p63", "parent": "p62", "length": 7.28, "rotation": -1.92, "x": 7.89, "y": -0.06}, {"name": "p64", "parent": "p4", "length": 7.1, "rotation": 75.38, "x": -1.18, "y": 0.24}, {"name": "p65", "parent": "p64", "length": 6.46, "rotation": 3.52, "x": 7.1}, {"name": "p66", "parent": "p4", "length": 6.9, "rotation": 91.06, "x": -6.81, "y": 0.75}, {"name": "p67", "parent": "p66", "length": 6.51, "rotation": -7.81, "x": 6.9}, {"name": "p68", "parent": "p4", "length": 7.52, "rotation": 75.7, "x": 3.85, "y": 3.34}, {"name": "p69", "parent": "p68", "length": 5.49, "rotation": -2.49, "x": 7.37, "y": 0.15}, {"name": "p70", "parent": "p4", "length": 6.33, "rotation": 83.63, "x": -4.5, "y": 2.45}, {"name": "p71", "parent": "p70", "length": 7.02, "rotation": 4.46, "x": 6.33}, {"name": "p72", "parent": "p5", "length": 6.78, "rotation": -83.13, "x": -1.9, "y": -1.4}, {"name": "p73", "parent": "p72", "length": 5.4, "rotation": 9.1, "x": 6.78}, {"name": "p74", "parent": "p73", "length": 3.48, "rotation": -23.17, "x": 5.4, "color": "ff1515ff"}, {"name": "p75", "parent": "p5", "length": 9.1, "rotation": 59.7, "x": 2.21, "y": -0.86}, {"name": "p76", "parent": "p75", "length": 8.52, "rotation": -15.38, "x": 8.99, "y": -0.01}, {"name": "p77", "parent": "p5", "length": 5.88, "rotation": 115.98, "x": -7.92, "y": -0.53}, {"name": "p78", "parent": "p77", "length": 7.78, "rotation": -2.56, "x": 5.88}, {"name": "p79", "parent": "p5", "length": 6.11, "rotation": 93.34, "x": -3.56, "y": -0.21}, {"name": "p80", "parent": "p79", "length": 8.14, "rotation": -11.43, "x": 6.11}, {"name": "p81", "parent": "p5", "length": 7.06, "rotation": 80.99, "x": 1.88, "y": 2.84}, {"name": "p82", "parent": "p81", "length": 7.48, "rotation": -5.27, "x": 7.06}, {"name": "p83", "parent": "p5", "length": 7.94, "rotation": 112.62, "x": -2.5, "y": 1.02}, {"name": "p84", "parent": "p83", "length": 7.43, "rotation": -17.55, "x": 7.94}, {"name": "p85", "parent": "p73", "length": 7.16, "rotation": 143.73, "x": 2.4, "y": 8.9}, {"name": "p86", "parent": "p85", "length": 8.09, "rotation": 9.08, "x": 7.16}, {"name": "p87", "parent": "p73", "length": 6.94, "rotation": -169.11, "x": -0.68, "y": -7.27}, {"name": "p88", "parent": "p87", "length": 7.09, "rotation": -18.25, "x": 6.94}, {"name": "p89", "parent": "p74", "length": 13.8, "rotation": -168.81, "x": 11.54, "y": 3.88, "color": "ff1515ff"}, {"name": "p90", "parent": "p89", "length": 8.72, "rotation": 1.07, "x": 13.8, "color": "ff1515ff"}, {"name": "p91", "parent": "p90", "length": 11.69, "rotation": -0.23, "x": 8.72, "color": "ff1515ff"}, {"name": "glow_orange3", "parent": "hecheng", "scaleX": 2.3358, "scaleY": 2.3358}], "slots": [{"name": "nv<PERSON>i", "bone": "nws1"}, {"name": "A_rotation06", "bone": "huan2", "color": "ffc500be", "dark": "ff3b00", "blend": "additive"}, {"name": "A_glow_orange_small3", "bone": "glow_orange2", "blend": "additive"}, {"name": "a3", "bone": "p1", "color": "ffffff00", "attachment": "a3"}, {"name": "a7", "bone": "p1", "color": "ffffff00", "attachment": "a7"}, {"name": "a6", "bone": "p1", "color": "ffffff00", "attachment": "a6"}, {"name": "a2", "bone": "p1", "color": "ffffff00", "attachment": "a2"}, {"name": "a1", "bone": "p1", "color": "ffffff00", "attachment": "a1"}, {"name": "a4", "bone": "p1", "color": "ffffff00", "attachment": "a4"}, {"name": "a5", "bone": "p8", "color": "ffffff00", "attachment": "a5"}, {"name": "b8", "bone": "p5", "color": "ffffff00", "attachment": "b8"}, {"name": "b7", "bone": "p5", "color": "ffffff00", "attachment": "b7"}, {"name": "b6", "bone": "p5", "color": "ffffff00", "attachment": "b6"}, {"name": "b5", "bone": "p5", "color": "ffffff00", "attachment": "b5"}, {"name": "b4", "bone": "p5", "color": "ffffff00", "attachment": "b4"}, {"name": "b3", "bone": "p5", "color": "ffffff00", "attachment": "b3"}, {"name": "b2", "bone": "p5", "color": "ffffff00", "attachment": "b2"}, {"name": "b1", "bone": "p74", "color": "ffffff00", "attachment": "b1"}, {"name": "c7", "bone": "p3", "color": "ffffff00", "attachment": "c7"}, {"name": "c6", "bone": "p3", "color": "ffffff00", "attachment": "c6"}, {"name": "c5", "bone": "p3", "color": "ffffff00", "attachment": "c5"}, {"name": "c4", "bone": "p3", "color": "ffffff00", "attachment": "c4"}, {"name": "c3", "bone": "p3", "color": "ffffff00", "attachment": "c3"}, {"name": "c2", "bone": "p3", "color": "ffffff00", "attachment": "c2"}, {"name": "c1", "bone": "p57", "color": "ffffff00", "attachment": "c1"}, {"name": "d6", "bone": "p4", "color": "ffffff00", "attachment": "d6"}, {"name": "d5", "bone": "p4", "color": "ffffff00", "attachment": "d5"}, {"name": "d4", "bone": "p4", "color": "ffffff00", "attachment": "d4"}, {"name": "d3", "bone": "p4", "color": "ffffff00", "attachment": "d3"}, {"name": "d2", "bone": "p4", "color": "ffffff00", "attachment": "d2"}, {"name": "d1", "bone": "p42", "color": "ffffff00", "attachment": "d1"}, {"name": "e4", "bone": "p5", "color": "ffffff00", "attachment": "e4"}, {"name": "e7", "bone": "p5", "color": "ffffff00", "attachment": "e7"}, {"name": "e6", "bone": "p5", "color": "ffffff00", "attachment": "e6"}, {"name": "e5", "bone": "p5", "color": "ffffff00", "attachment": "e5"}, {"name": "e3", "bone": "p5", "color": "ffffff00", "attachment": "e3"}, {"name": "e2", "bone": "p5", "color": "ffffff00", "attachment": "e2"}, {"name": "e1", "bone": "p25", "color": "ffffff00", "attachment": "e1"}, {"name": "A_glow_orange_small4", "bone": "glow_orange3", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"d6": {"d6": {"type": "mesh", "uvs": [0.44113, 0, 0.70882, 0.05594, 0.91497, 0.24927, 0.9242, 0.48482, 0.90574, 0.69371, 0.7919, 0.74037, 0.88113, 0.84037, 0.83805, 0.9537, 0.51805, 0.95592, 0.16421, 0.91815, 0.19498, 0.76704, 0.10883, 0.73371, 0.09037, 0.4626, 0.10575, 0.19149, 0.16729, 0.0826, 0.47806, 0.06927, 0.51191, 0.21593, 0.51191, 0.47149, 0.51191, 0.77148, 0.51806, 0.86481], "triangles": [8, 9, 19, 19, 9, 10, 8, 19, 7, 19, 10, 18, 7, 19, 6, 6, 19, 5, 19, 18, 5, 5, 18, 17, 18, 10, 17, 12, 17, 10, 4, 5, 3, 12, 10, 11, 3, 5, 17, 17, 12, 16, 14, 16, 13, 14, 15, 16, 13, 16, 12, 17, 2, 3, 17, 16, 2, 16, 1, 2, 16, 15, 1, 14, 0, 15, 15, 0, 1], "vertices": [1, 60, -0.84, -0.47, 1, 1, 60, -0.05, 3.06, 1, 2, 60, 3.25, 5.96, 0.95401, 61, -4.12, 6.12, 0.04599, 2, 60, 7.48, 6.34, 0.58679, 61, 0.12, 6.34, 0.41321, 2, 60, 11.25, 6.34, 0.20581, 61, 3.88, 6.19, 0.79419, 2, 60, 12.18, 4.91, 0.08499, 61, 4.76, 4.74, 0.91501, 2, 60, 13.9, 6.18, 0.00932, 61, 6.53, 5.94, 0.99068, 2, 60, 15.97, 5.75, 0.00017, 61, 8.58, 5.43, 0.99983, 1, 61, 8.73, 1.27, 1, 1, 61, 8.16, -3.34, 1, 1, 61, 5.43, -3.01, 1, 1, 61, 4.86, -4.14, 1, 2, 60, 7.76, -4.5, 0.25876, 61, -0.01, -4.51, 0.74124, 2, 60, 2.87, -4.61, 0.92998, 61, -4.9, -4.43, 0.07002, 2, 60, 0.87, -3.93, 0.98787, 61, -6.87, -3.68, 0.01213, 1, 60, 0.37, 0.09, 1, 1, 60, 2.98, 0.69, 1, 2, 60, 7.57, 0.98, 0.60684, 61, 0.01, 0.98, 0.39316, 2, 60, 12.96, 1.31, 0.00257, 61, 5.41, 1.11, 0.99743, 1, 61, 7.09, 1.23, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 13, "height": 18}}, "A_rotation06": {"A_rotation06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}}, "c4": {"c4": {"type": "mesh", "uvs": [0.51257, 0.01123, 0.67689, 0.10223, 0.67481, 0.18456, 0.77464, 0.2019, 0.91192, 0.31239, 0.92856, 0.55073, 0.98056, 0.79122, 0.95768, 0.91472, 0.75592, 0.97539, 0.50633, 0.96022, 0.26297, 0.90606, 0.14025, 0.69589, 0.01961, 0.49873, 0.06953, 0.31673, 0.18809, 0.21706, 0.32329, 0.1824, 0.38361, 0.04806, 0.55208, 0.21489, 0.66024, 0.60488, 0.7268, 0.83672, 0.60321, 0.39926, 0.758, 0.38822, 0.7996, 0.56155, 0.862, 0.82155, 0.30456, 0.42722, 0.40648, 0.65472, 0.4564, 0.84755], "triangles": [10, 25, 26, 26, 18, 19, 23, 19, 22, 9, 19, 8, 8, 23, 7, 8, 19, 23, 10, 26, 9, 9, 26, 19, 7, 23, 6, 23, 5, 6, 24, 15, 17, 20, 17, 2, 15, 16, 17, 16, 0, 17, 2, 17, 1, 1, 17, 0, 11, 24, 25, 11, 12, 24, 25, 24, 20, 18, 20, 21, 21, 4, 5, 12, 13, 24, 13, 14, 24, 24, 17, 20, 24, 14, 15, 20, 2, 21, 21, 3, 4, 21, 2, 3, 10, 11, 25, 26, 25, 18, 19, 18, 22, 23, 22, 5, 25, 20, 18, 18, 21, 22, 22, 21, 5], "vertices": [1, 64, 6.27, 0.4, 1, 3, 62, 15.6, -4.87, 0.00025, 63, 8.49, -4.95, 0.09314, 64, 4.38, -3.85, 0.90661, 3, 62, 13.68, -4.4, 0.016, 63, 6.58, -4.47, 0.32008, 64, 2.4, -3.94, 0.66392, 3, 62, 12.74, -6.75, 0.09602, 63, 5.62, -6.81, 0.57595, 64, 2.16, -6.46, 0.32803, 3, 62, 9.42, -9.54, 0.29498, 63, 2.27, -9.57, 0.57778, 64, -0.24, -10.07, 0.12724, 3, 62, 3.74, -8.74, 0.74355, 63, -3.4, -8.7, 0.24163, 64, -5.92, -10.88, 0.01482, 1, 11, -10.41, -0.25, 1, 1, 11, -9.79, 2.7, 1, 1, 11, -4.72, 4.08, 1, 1, 11, 1.51, 3.61, 1, 1, 11, 7.57, 2.21, 1, 3, 62, 4.53, 11.26, 0.46471, 63, -2.43, 11.29, 0.52584, 64, -10.77, 8.53, 0.00946, 3, 62, 9.79, 13.2, 0.18916, 63, 2.85, 13.18, 0.74487, 64, -6.26, 11.87, 0.06597, 3, 62, 13.79, 11.06, 0.08603, 63, 6.84, 10.99, 0.76184, 64, -1.82, 10.93, 0.15213, 3, 62, 15.5, 7.65, 0.02981, 63, 8.51, 7.57, 0.67444, 64, 0.77, 8.14, 0.29575, 3, 62, 15.6, 4.17, 0.00198, 63, 8.57, 4.09, 0.34426, 64, 1.84, 4.83, 0.65376, 2, 63, 11.38, 1.91, 0.011, 64, 5.16, 3.55, 0.989, 2, 63, 6.55, -1.31, 0.0844, 64, 1.46, -0.93, 0.9156, 3, 62, 3.9, -1.9, 0.96279, 63, -3.18, -1.87, 0.03702, 64, -7.69, -4.28, 0.00019, 1, 11, -4.05, 0.74, 1, 3, 62, 9.02, -1.56, 0.0382, 63, 1.95, -1.58, 0.92352, 64, -2.86, -2.51, 0.03828, 3, 62, 8.46, -5.4, 0.29355, 63, 1.35, -5.41, 0.59923, 64, -2.33, -6.36, 0.10722, 3, 62, 4.17, -5.53, 0.76744, 63, -2.94, -5.5, 0.22161, 64, -6.4, -7.68, 0.01094, 1, 11, -7.43, 0.43, 1, 3, 62, 9.95, 5.88, 0.1381, 63, 2.95, 5.85, 0.76607, 64, -4.05, 4.89, 0.09583, 2, 62, 4.08, 4.55, 0.71118, 63, -2.94, 4.58, 0.28882, 1, 11, 2.71, 0.89, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 36, 38, 34, 40, 40, 36, 42, 44, 44, 46, 48, 50, 50, 52], "width": 25, "height": 24}}, "e4": {"e4": {"type": "mesh", "uvs": [0.15782, 0.49304, 0.03032, 0.3686, 0.10157, 0.15971, 0.24782, 0.05304, 0.52157, 0.02415, 0.71657, 0.20415, 0.96782, 0.30415, 0.76907, 0.47304, 0.92657, 0.58637, 0.83282, 0.78193, 0.82907, 0.93971, 0.66407, 0.98637, 0.45782, 0.95971, 0.40157, 0.85082, 0.16907, 0.78859, 0.15032, 0.61971, 0.51031, 0.61083, 0.44656, 0.46416, 0.38281, 0.30416, 0.60031, 0.82416], "triangles": [10, 11, 19, 11, 12, 19, 12, 13, 19, 10, 19, 9, 19, 13, 16, 13, 14, 16, 19, 16, 9, 9, 16, 8, 14, 15, 16, 17, 15, 0, 15, 17, 16, 16, 7, 8, 16, 17, 7, 17, 0, 18, 6, 7, 5, 0, 1, 18, 5, 7, 18, 7, 17, 18, 1, 2, 18, 2, 3, 18, 18, 4, 5, 18, 3, 4], "vertices": [2, 35, 4.45, -4.66, 0.90884, 36, -4.89, -4.82, 0.09116, 2, 35, 0.66, -5.7, 0.99938, 36, -8.65, -5.97, 0.00062, 1, 35, -4.44, -3.04, 1, 1, 35, -6.57, 0, 1, 1, 35, -6.11, 4.43, 1, 1, 35, -0.57, 6.08, 1, 1, 35, 3.13, 9.2, 1, 2, 35, 6.64, 4.88, 0.83818, 36, -3, 4.79, 0.16182, 2, 35, 10.27, 6.46, 0.33203, 36, 0.58, 6.48, 0.66797, 2, 35, 14.93, 3.56, 0.00625, 36, 5.33, 3.73, 0.99375, 1, 36, 9.44, 2.62, 1, 1, 36, 10.02, -0.24, 1, 1, 36, 8.51, -3.27, 1, 1, 36, 5.44, -3.42, 1, 2, 35, 12.17, -6.7, 0.04533, 36, 2.89, -6.61, 0.95467, 2, 35, 7.71, -5.72, 0.4878, 36, -1.6, -5.78, 0.5122, 1, 35, 9.07, -0.12, 1, 2, 35, 4.98, -0.01, 0.99988, 36, -4.51, -0.15, 0.00012, 1, 35, 0.55, 0.21, 1, 1, 36, 5.52, -0.16, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 32, 34, 34, 36, 32, 38], "width": 16, "height": 27}}, "A_glow_orange_small3": {"A_glow_orange_small": {"rotation": 48.17, "width": 64, "height": 64}}, "c6": {"c6": {"type": "mesh", "uvs": [0.5185, 0.03716, 0.76517, 0.08116, 0.8785, 0.29916, 0.8985, 0.56915, 0.9085, 0.80915, 0.73183, 0.85315, 0.74183, 0.97315, 0.44184, 0.95915, 0.21851, 0.93515, 0.19851, 0.88115, 0.00851, 0.85715, 0.06851, 0.58515, 0.14517, 0.26916, 0.26517, 0.10116, 0.5385, 0.27515, 0.49183, 0.57115, 0.46183, 0.85914], "triangles": [7, 16, 6, 16, 5, 6, 7, 8, 16, 8, 9, 16, 15, 16, 9, 9, 10, 11, 16, 15, 5, 15, 9, 11, 5, 3, 4, 5, 15, 3, 11, 12, 15, 15, 2, 3, 15, 14, 2, 15, 12, 14, 14, 1, 2, 12, 13, 14, 13, 0, 14, 14, 0, 1], "vertices": [1, 77, -2.99, -0.72, 1, 1, 77, -2.4, 2.31, 1, 2, 77, 1.81, 4.08, 0.95319, 78, -4.18, 4.42, 0.04681, 2, 77, 7.17, 4.83, 0.18756, 78, 1.21, 4.75, 0.81244, 1, 78, 6.01, 4.95, 1, 1, 78, 6.92, 2.85, 1, 1, 78, 9.32, 3.01, 1, 1, 78, 9.1, -0.6, 1, 1, 78, 8.67, -3.28, 1, 1, 78, 7.59, -3.54, 1, 2, 77, 13.91, -5.26, 0.00147, 78, 7.15, -5.83, 0.99853, 2, 77, 8.43, -5.05, 0.29387, 78, 1.7, -5.2, 0.70613, 2, 77, 2.05, -4.74, 0.9969, 78, -4.63, -4.39, 0.0031, 1, 77, -1.43, -3.62, 1, 1, 77, 1.72, -0.03, 1, 2, 77, 7.67, -0.02, 0.00266, 78, 1.33, -0.13, 0.99734, 1, 78, 7.1, -0.39, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 28, 28, 30, 30, 32], "width": 12, "height": 20}}, "d1": {"d1": {"x": 16.11, "y": 2.61, "rotation": -83.09, "width": 39, "height": 40}}, "d2": {"d2": {"type": "mesh", "uvs": [0.84658, 0.02545, 0.98969, 0.07758, 0.98458, 0.27078, 0.93347, 0.42411, 0.8338, 0.63724, 0.77758, 0.79977, 0.50669, 0.93317, 0.21025, 1, 0.06459, 0.83504, 0.07992, 0.60197, 0.2537, 0.45478, 0.49136, 0.29684, 0.6498, 0.13585, 0.73414, 0.04385, 0.81846, 0.20178, 0.71369, 0.36124, 0.63191, 0.53144, 0.5348, 0.72464, 0.32269, 0.84117], "triangles": [7, 18, 6, 7, 8, 18, 6, 17, 5, 6, 18, 17, 17, 18, 9, 18, 8, 9, 5, 17, 4, 9, 10, 17, 17, 16, 4, 17, 10, 16, 4, 16, 3, 10, 11, 16, 16, 15, 3, 16, 11, 15, 3, 15, 2, 15, 14, 2, 15, 11, 14, 11, 12, 14, 2, 14, 1, 12, 13, 14, 14, 0, 1, 14, 13, 0], "vertices": [1, 54, -4.78, -1.1, 1, 1, 54, -4.35, 1.88, 1, 2, 54, 1.02, 4.06, 0.999, 55, -5.29, 5.49, 0.001, 2, 54, 5.61, 5.02, 0.74185, 55, -0.6, 5.32, 0.25815, 2, 54, 12.2, 5.87, 0.01306, 55, 5.99, 4.57, 0.98694, 1, 55, 10.97, 4.35, 1, 1, 55, 15.7, 0.18, 1, 1, 55, 18.53, -4.77, 1, 1, 55, 14.07, -8.15, 1, 1, 55, 7.12, -9, 1, 2, 54, 11.25, -5.88, 0.0526, 55, 2.26, -6.62, 0.9474, 2, 54, 5.21, -3.8, 0.88455, 55, -3.1, -3.15, 0.11545, 1, 54, -0.35, -3.07, 1, 1, 54, -3.48, -2.75, 1, 1, 54, 0.28, 0.5, 1, 2, 54, 5.42, 0.64, 0.99615, 55, -1.83, 1.11, 0.00385, 2, 54, 10.7, 1.28, 0.00372, 55, 3.44, 0.47, 0.99628, 1, 55, 9.44, -0.32, 1, 1, 55, 13.51, -3.53, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 28, 28, 30, 30, 32, 32, 34, 34, 36], "width": 18, "height": 30}}, "b1": {"b1": {"x": 12.61, "y": 1.79, "rotation": -79.9, "width": 41, "height": 55}}, "b2": {"b2": {"type": "mesh", "uvs": [0.30284, 0.29971, 0.47284, 0.14971, 0.66284, 0.02145, 0.84951, 0.01058, 0.97284, 0.14101, 0.94617, 0.28449, 0.88951, 0.43449, 0.87951, 0.62145, 0.87617, 0.82144, 0.66284, 0.83231, 0.61284, 0.97579, 0.37951, 1, 0.14618, 0.96927, 0.19951, 0.80405, 0.04284, 0.65188, 0.05951, 0.4671, 0.77616, 0.07145, 0.69617, 0.20841, 0.5795, 0.37797, 0.4695, 0.54537, 0.39617, 0.8258], "triangles": [11, 12, 20, 11, 20, 10, 20, 12, 13, 10, 20, 9, 8, 9, 7, 9, 19, 7, 20, 13, 19, 19, 9, 20, 13, 14, 19, 14, 15, 19, 7, 19, 6, 15, 0, 19, 6, 19, 18, 19, 0, 18, 6, 18, 5, 0, 1, 18, 18, 17, 5, 18, 1, 17, 5, 17, 4, 1, 2, 17, 17, 16, 4, 17, 2, 16, 16, 3, 4, 16, 2, 3], "vertices": [2, 92, 3.77, -4.39, 0.90873, 93, -4.04, -3.8, 0.09127, 1, 92, -0.41, -3.41, 1, 1, 92, -4.24, -1.95, 1, 1, 92, -5.58, 0.53, 1, 1, 92, -3.55, 3.41, 1, 2, 92, -0.36, 4.34, 0.99693, 93, -6.74, 5.47, 0.00307, 2, 92, 3.15, 4.92, 0.86534, 93, -3.18, 5.49, 0.13466, 2, 92, 7.16, 6.47, 0.31201, 93, 1.02, 6.39, 0.68799, 2, 92, 11.41, 8.24, 0.04691, 93, 5.5, 7.47, 0.95309, 2, 92, 12.9, 5.4, 0.0102, 93, 6.52, 4.42, 0.9898, 1, 93, 9.9, 4.5, 1, 1, 93, 11.3, 1.24, 1, 1, 93, 11.47, -2.32, 1, 1, 93, 7.59, -2.47, 1, 2, 92, 12.75, -4.79, 0.02351, 93, 4.76, -5.61, 0.97649, 2, 92, 8.74, -6.23, 0.25943, 93, 0.58, -6.4, 0.74057, 1, 92, -3.86, 0.07, 1, 1, 92, -0.49, 0.2, 1, 2, 92, 3.79, 0.13, 0.99912, 93, -3.31, 0.66, 0.00088, 2, 92, 7.98, 0.13, 8e-05, 93, 0.83, 0, 0.99992, 1, 93, 7.35, 0.51, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 6, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 15, "height": 23}}, "b3": {"b3": {"type": "mesh", "uvs": [0.10709, 0.01105, 0.45324, 0.16105, 0.73785, 0.30423, 0.98016, 0.45651, 0.96478, 0.72696, 0.78401, 0.92241, 0.62632, 0.99968, 0.37247, 0.91105, 0.36093, 0.71332, 0.13016, 0.67014, 0.08016, 0.57696, 0.13786, 0.45878, 0.25324, 0.44514, 0.15324, 0.28378, 0.03786, 0.15651, 0.27635, 0.17697, 0.5225, 0.35652, 0.65712, 0.54743, 0.64942, 0.73379, 0.61097, 0.88606, 0.36096, 0.57697], "triangles": [7, 19, 6, 6, 19, 5, 19, 18, 5, 5, 18, 4, 7, 8, 19, 19, 8, 18, 8, 20, 18, 18, 17, 4, 18, 20, 17, 4, 17, 3, 8, 9, 20, 20, 10, 11, 11, 12, 20, 20, 9, 10, 17, 20, 16, 20, 12, 16, 17, 2, 3, 17, 16, 2, 16, 13, 15, 16, 12, 13, 15, 1, 16, 16, 1, 2, 13, 14, 15, 1, 15, 0, 15, 14, 0], "vertices": [1, 94, -6.07, -0.72, 1, 1, 94, -1.23, 2.06, 1, 2, 94, 3.15, 4.16, 0.98469, 95, -4.9, 2.76, 0.01531, 2, 94, 7.49, 5.68, 0.61354, 95, -1.26, 5.56, 0.38646, 2, 94, 12.85, 3.08, 0.02501, 95, 4.64, 4.77, 0.97499, 1, 95, 8.69, 2, 1, 1, 95, 10.18, -0.2, 1, 1, 95, 7.91, -3.29, 1, 2, 94, 9.38, -3.98, 0.16063, 95, 3.56, -3.01, 0.83937, 2, 94, 7.3, -6.33, 0.57258, 95, 2.32, -5.9, 0.42742, 2, 94, 5.16, -6.09, 0.7139, 95, 0.22, -6.34, 0.2861, 2, 94, 3.09, -4.35, 0.87377, 95, -2.3, -5.34, 0.12623, 2, 94, 3.42, -2.86, 0.94986, 95, -2.45, -3.82, 0.05014, 1, 94, -0.35, -2.6, 1, 1, 94, -3.52, -2.84, 1, 1, 94, -1.84, -0.19, 1, 1, 94, 3.06, 1.13, 1, 2, 94, 7.61, 1.03, 0.36376, 95, 0.32, 1.18, 0.63624, 1, 95, 4.38, 0.68, 1, 1, 95, 7.67, -0.15, 1, 2, 94, 6.64, -2.76, 0.5794, 95, 0.58, -2.71, 0.4206], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 13, "height": 22}}, "b4": {"b4": {"type": "mesh", "uvs": [0.16163, 0.26666, 0.31617, 0.20796, 0.38436, 0.11014, 0.52981, 0.03188, 0.67299, 0.11014, 0.68435, 0.2384, 0.80708, 0.29057, 0.97299, 0.40362, 0.97072, 0.61883, 0.97753, 0.80579, 0.97299, 0.96013, 0.79799, 0.95579, 0.51845, 0.95144, 0.2889, 0.94492, 0.07754, 0.91013, 0.07981, 0.76014, 0.02981, 0.56883, 0, 0.3884, 0.5548, 0.23406, 0.58889, 0.57971, 0.58434, 0.79058, 0.82071, 0.4558, 0.8298, 0.64058, 0.82298, 0.82753, 0.24571, 0.43623, 0.27071, 0.61232, 0.28662, 0.79493, 0.57173, 0.40571], "triangles": [12, 13, 26, 14, 26, 13, 12, 20, 11, 11, 23, 10, 18, 3, 4, 2, 3, 18, 1, 2, 18, 18, 4, 5, 27, 18, 5, 24, 0, 1, 17, 0, 24, 21, 6, 7, 16, 17, 24, 27, 21, 19, 6, 27, 5, 21, 27, 6, 24, 27, 25, 1, 27, 24, 18, 27, 1, 16, 24, 25, 25, 27, 19, 8, 21, 7, 22, 21, 8, 19, 21, 22, 15, 16, 25, 20, 25, 19, 20, 19, 22, 26, 25, 20, 15, 25, 26, 22, 8, 9, 23, 20, 22, 23, 22, 9, 14, 15, 26, 20, 12, 26, 11, 20, 23, 10, 23, 9], "vertices": [3, 79, 12.73, 8.33, 0.1121, 80, 7.19, 7.28, 0.81512, 81, -1.22, 7.4, 0.07279, 3, 79, 13.84, 4.84, 0.02003, 80, 7.74, 3.66, 0.52743, 81, 0.7, 4.29, 0.45254, 3, 79, 15.98, 3.19, 3e-05, 80, 9.59, 1.69, 0.07174, 81, 3.18, 3.2, 0.92823, 1, 81, 5.51, 0.37, 1, 2, 80, 8.15, -4.49, 0.04685, 81, 4.29, -3.05, 0.95315, 3, 79, 12.58, -3.19, 0.02638, 80, 5.22, -4.07, 0.49479, 81, 1.43, -3.81, 0.47883, 3, 79, 11.2, -5.8, 0.20274, 80, 3.44, -6.43, 0.70705, 81, 0.72, -6.68, 0.09021, 3, 79, 8.35, -9.26, 0.47894, 80, 0.08, -9.39, 0.51859, 81, -1.19, -10.73, 0.00247, 2, 79, 3.42, -8.87, 0.7892, 80, -4.73, -8.23, 0.2108, 2, 79, -0.88, -8.72, 0.96744, 80, -8.95, -7.4, 0.03256, 1, 12, -10.75, 1.98, 1, 1, 12, -6.9, 2.08, 1, 1, 12, -0.75, 2.29, 1, 1, 12, 4.3, 2.39, 1, 1, 12, 8.99, 1.83, 1, 2, 79, 1.53, 10.91, 0.66307, 80, -3.46, 11.6, 0.33693, 2, 79, 6, 11.7, 0.38095, 80, 1.08, 11.68, 0.61905, 3, 79, 10.19, 12.07, 0.22153, 80, 5.27, 11.38, 0.77526, 81, -4.6, 10.41, 0.00321, 2, 80, 5.96, -1.31, 0.14813, 81, 1.03, -0.99, 0.85187, 1, 79, 4.9, -0.55, 1, 1, 79, 0.07, -0.12, 1, 3, 79, 7.39, -5.84, 0.4747, 80, -0.33, -5.86, 0.51779, 81, -2.96, -7.64, 0.0075, 2, 79, 3.13, -5.74, 0.85079, 80, -4.51, -5.09, 0.14921, 2, 79, -1.15, -5.29, 0.98677, 80, -8.67, -3.97, 0.01323, 3, 79, 8.71, 6.75, 0.21698, 80, 2.97, 6.36, 0.77001, 81, -4.74, 4.89, 0.01301, 2, 79, 4.63, 6.48, 0.54849, 80, -1.1, 6.74, 0.45151, 2, 79, 0.42, 6.43, 0.80727, 80, -5.27, 7.35, 0.19273, 3, 79, 8.92, -0.45, 0.0009, 80, 2.03, -0.78, 0.99303, 81, -2.79, -2.05, 0.00607], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 6, 36, 38, 40, 12, 42, 42, 44, 44, 46, 0, 48, 48, 50, 50, 52, 36, 54, 54, 38], "width": 22, "height": 23}}, "b5": {"b5": {"type": "mesh", "uvs": [0.27517, 0.28888, 0.34135, 0.11805, 0.50311, 0.02013, 0.5977, 0.04876, 0.67517, 0.07221, 0.80017, 0.21388, 0.90605, 0.50971, 1, 0.74929, 0.93546, 0.89304, 0.78987, 0.93679, 0.81487, 0.72846, 0.75605, 0.47013, 0.76634, 0.66179, 0.75164, 0.87429, 0.65458, 0.98471, 0.46486, 1, 0.50192, 0.68568, 0.53486, 0.4611, 0.43192, 0.75568, 0.2981, 0.97151, 0.11692, 0.88985, 0.00574, 0.72068, 0.18074, 0.46693, 0.52476, 0.27472, 0.73064, 0.3393, 0.62329, 0.27889, 0.64094, 0.46639, 0.64388, 0.7018, 0.413, 0.29972, 0.33359, 0.50388, 0.22035, 0.69972, 0.78064, 0.32056, 0.83947, 0.50597, 0.90123, 0.71847], "triangles": [28, 1, 2, 25, 3, 4, 5, 31, 4, 23, 2, 3, 31, 24, 4, 11, 24, 31, 5, 6, 31, 11, 31, 32, 32, 31, 6, 33, 32, 6, 32, 10, 11, 33, 10, 32, 33, 6, 7, 8, 33, 7, 10, 33, 8, 9, 10, 8, 4, 24, 25, 23, 3, 25, 17, 23, 25, 26, 25, 24, 17, 25, 26, 26, 24, 11, 12, 26, 11, 27, 17, 26, 12, 27, 26, 27, 16, 17, 13, 27, 12, 14, 27, 13, 14, 15, 16, 14, 16, 27, 28, 0, 1, 2, 23, 28, 28, 23, 17, 29, 0, 28, 29, 28, 17, 22, 0, 29, 17, 18, 29, 30, 29, 18, 30, 22, 29, 21, 22, 30, 20, 21, 30, 19, 30, 18, 20, 30, 19], "vertices": [2, 83, -3.1, -5.5, 0.15622, 82, 4.55, -4.49, 0.84378, 1, 12, 6.35, -3.41, 1, 1, 12, 0.97, -6.03, 1, 1, 12, -2.27, -5.51, 1, 1, 12, -4.93, -5.08, 1, 1, 12, -9.35, -1.9, 1, 2, 85, 1.35, 2.47, 0.91067, 84, 7.33, 2.41, 0.08933, 1, 85, 7.85, 3.45, 1, 1, 85, 10.31, 0.19, 1, 1, 85, 9.56, -4.82, 1, 1, 85, 5.18, -2.27, 1, 4, 87, -2.15, 3.98, 0.09389, 86, 4.79, 4.33, 0.20687, 85, -1.33, -1.97, 0.04111, 84, 4.46, -1.91, 0.65812, 3, 87, 2.3, 5.2, 0.87561, 86, 9.39, 4.64, 0.0426, 84, 8.83, -3.39, 0.08178, 2, 87, 7.4, 5.68, 0.99688, 84, 13.32, -5.85, 0.00312, 1, 87, 10.63, 2.95, 1, 1, 87, 12.22, -3.32, 1, 3, 82, 8.3, 7.17, 3e-05, 87, 4.57, -3.52, 0.99473, 86, 9.9, -4.35, 0.00523, 3, 82, 3.17, 5.16, 0.36402, 87, -0.93, -3.44, 0.11485, 86, 4.52, -3.19, 0.52113, 3, 83, 0.32, 6.43, 0.06359, 82, 11.01, 6.1, 0.92602, 86, 11.56, -6.75, 0.01039, 2, 83, 7.16, 7.3, 0.65919, 82, 17.83, 5.13, 0.34081, 2, 83, 10.48, 1.75, 0.98089, 82, 19.57, -1.1, 0.01911, 1, 83, 10.63, -3.79, 1, 2, 83, 2.14, -4.42, 0.95468, 82, 9.88, -4.84, 0.04532, 2, 82, -0.38, 2.42, 0.56686, 86, 0.04, -3.5, 0.43314, 3, 87, -5.07, 2.53, 1e-05, 86, 1.64, 3.49, 0.25607, 84, 1.23, -1.47, 0.74392, 2, 82, -2.13, 5.28, 0.00817, 86, 0.17, -0.15, 0.99183, 2, 86, 4.67, 0.41, 0.9759, 84, 2.84, -5.47, 0.0241, 2, 87, 4.03, 1.3, 0.99282, 84, 8.08, -7.6, 0.00718, 2, 83, -6.44, -2.21, 0.00091, 82, 2.2, -0.43, 0.99909, 1, 82, 7.78, -0.01, 1, 2, 83, 4.82, 0.66, 0.96238, 82, 13.82, -0.66, 0.03762, 1, 84, 1.48, 0.27, 1, 2, 85, 0.47, 0.39, 0.88243, 84, 6.36, 0.36, 0.11757, 1, 85, 5.98, 0.56, 1], "hull": 23, "edges": [0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 4, 46, 8, 48, 4, 6, 6, 8, 6, 50, 50, 52, 52, 54, 4, 56, 56, 58, 58, 60, 8, 62, 62, 64, 64, 66], "width": 34, "height": 24}}, "b6": {"b6": {"type": "mesh", "uvs": [0.23281, 0.04465, 0.47947, 0.01465, 0.77614, 0.16965, 0.9128, 0.41965, 0.9828, 0.74714, 0.84947, 0.88714, 0.75614, 0.97714, 0.53947, 0.97714, 0.28281, 0.94964, 0.23947, 0.81464, 0.08281, 0.56715, 0.03281, 0.34715, 0.00947, 0.19715, 0.43285, 0.21717, 0.53952, 0.48467, 0.56951, 0.78716], "triangles": [5, 6, 15, 6, 7, 15, 7, 8, 15, 8, 9, 15, 5, 15, 4, 15, 9, 14, 15, 14, 4, 9, 10, 14, 14, 3, 4, 10, 11, 14, 11, 13, 14, 14, 2, 3, 14, 13, 2, 11, 12, 13, 12, 0, 13, 13, 1, 2, 13, 0, 1], "vertices": [1, 90, -2.28, -1.01, 1, 1, 90, -1.6, 2.67, 1, 2, 90, 2.82, 5.82, 0.98332, 91, -6.63, 4, 0.01668, 2, 90, 8.22, 6.06, 0.52986, 91, -1.56, 5.86, 0.47014, 2, 90, 14.74, 4.84, 0.00353, 91, 5.02, 6.66, 0.99647, 1, 91, 7.75, 4.56, 1, 1, 91, 9.49, 3.09, 1, 1, 91, 9.37, -0.16, 1, 2, 90, 15.01, -6.41, 0.00265, 91, 8.67, -3.98, 0.99735, 2, 90, 12.25, -6.11, 0.05858, 91, 5.95, -4.53, 0.94142, 2, 90, 6.79, -6.66, 0.59917, 91, 0.91, -6.69, 0.40083, 2, 90, 2.4, -5.88, 0.94625, 91, -3.51, -7.27, 0.05375, 2, 90, -0.54, -5.19, 0.99767, 91, -6.52, -7.51, 0.00233, 1, 90, 1.98, 0.65, 1, 1, 90, 7.55, 0.35, 1, 1, 91, 5.59, 0.44, 1], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 0, 26, 26, 28, 28, 30], "width": 15, "height": 20}}, "c5": {"c5": {"type": "mesh", "uvs": [0.65, 0.02193, 0.80573, 0.08732, 0.94333, 0.25428, 0.97426, 0.43514, 0.97497, 0.65575, 0.95044, 0.87279, 0.86297, 0.95766, 0.75097, 0.96183, 0.76804, 0.74479, 0.78937, 0.46514, 0.74987, 0.75645, 0.72587, 0.96427, 0.55962, 0.9795, 0.35162, 0.9482, 0.37996, 0.82153, 0.41263, 0.70153, 0.36729, 0.8137, 0.33463, 0.93022, 0.17501, 0.83056, 0.08835, 0.65317, 0.23634, 0.45143, 0.36568, 0.24795, 0.47501, 0.08622, 0.64967, 0.29665, 0.63634, 0.49317, 0.599, 0.74882, 0.48967, 0.45665, 0.535, 0.25839, 0.78967, 0.29144, 0.86567, 0.27752, 0.883, 0.46883, 0.88167, 0.72621, 0.47501, 0.25144, 0.36967, 0.45143, 0.25901, 0.64969], "triangles": [26, 32, 27, 33, 21, 32, 32, 22, 27, 27, 22, 0, 32, 21, 22, 1, 28, 23, 23, 27, 0, 1, 23, 0, 9, 28, 29, 29, 2, 3, 28, 1, 29, 29, 1, 2, 7, 8, 6, 6, 31, 5, 6, 8, 31, 5, 31, 4, 8, 9, 31, 31, 30, 4, 31, 9, 30, 30, 3, 4, 30, 9, 29, 30, 29, 3, 13, 14, 12, 12, 14, 25, 12, 25, 11, 25, 14, 15, 11, 25, 10, 15, 26, 25, 10, 25, 24, 9, 23, 28, 25, 26, 24, 9, 10, 24, 24, 26, 23, 24, 23, 9, 23, 26, 27, 17, 18, 16, 18, 34, 16, 18, 19, 34, 16, 34, 15, 34, 33, 15, 19, 20, 34, 34, 20, 33, 20, 21, 33, 15, 33, 26, 33, 32, 26], "vertices": [1, 11, -1.49, -5.88, 1, 1, 11, -6.13, -4.3, 1, 1, 11, -10.2, -0.4, 1, 2, 73, 3.1, 4.19, 0.98421, 74, -4.33, 3.63, 0.01579, 2, 73, 8.18, 4.03, 0.2943, 74, 0.71, 4.17, 0.7057, 1, 74, 5.75, 3.94, 1, 1, 74, 7.96, 1.53, 1, 1, 74, 8.4, -1.8, 1, 1, 74, 3.38, -1.8, 1, 3, 71, 2.91, 5.23, 0.20968, 72, -3.86, 5.47, 0.02509, 73, 3.6, -1.38, 0.76523, 3, 71, 9.7, 5.66, 0.12544, 72, 2.94, 5.49, 0.84555, 73, 10.26, -2.8, 0.02902, 3, 71, 14.52, 6.09, 8e-05, 72, 7.78, 5.62, 0.99989, 73, 15.01, -3.68, 3e-05, 1, 72, 9, 0.78, 1, 2, 70, 7.82, 6.82, 0.01652, 72, 9.39, -5.49, 0.98348, 3, 69, 13.13, 5.34, 0.01302, 70, 5.06, 5.56, 0.06318, 72, 6.38, -5.17, 0.9238, 4, 69, 10.37, 4.37, 0.09747, 70, 2.33, 4.51, 0.406, 71, 10.87, -4.47, 0.00637, 72, 3.49, -4.69, 0.49016, 3, 69, 13.23, 4.93, 0.00954, 70, 5.17, 5.16, 0.91701, 72, 6.27, -5.58, 0.07345, 2, 70, 7.84, 6.16, 0.98084, 72, 9.08, -6.07, 0.01916, 1, 70, 9.22, 1.04, 1, 1, 70, 7.82, -3.6, 1, 2, 69, 9.19, -3.35, 0.00472, 70, 1.41, -3.25, 0.99528, 1, 11, 7.13, -0.82, 1, 1, 11, 3.79, -4.49, 1, 2, 71, 0.14, 0.24, 0.97953, 73, -0.42, -5.43, 0.02047, 3, 71, 4.63, 0.92, 0.95071, 72, -2.41, 1.07, 0.01407, 73, 4.09, -5.99, 0.03522, 3, 71, 10.6, 1.22, 0.00518, 72, 3.57, 1, 0.99298, 73, 9.92, -7.31, 0.00184, 4, 69, 4.53, 2.65, 0.61111, 70, -3.45, 2.59, 0.00056, 71, 4.85, -3.56, 0.35216, 72, -2.47, -3.41, 0.03617, 1, 11, 2.05, -0.5, 1, 1, 11, -5.58, 0.38, 1, 1, 11, -7.86, 0.1, 1, 2, 73, 3.78, 1.43, 0.99412, 74, -3.29, 0.99, 0.00588, 2, 73, 9.7, 1.18, 0.01428, 74, 2.61, 1.55, 0.98572, 1, 11, 3.85, -0.69, 1, 2, 69, 6.69, -0.23, 0.95488, 70, -1.2, -0.22, 0.04512, 3, 69, 12.32, 0.03, 0.0003, 70, 4.42, 0.24, 0.99748, 72, 3.13, -9.44, 0.00221], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 30, 52, 52, 54, 18, 56, 58, 60, 60, 62, 64, 66, 66, 68], "width": 30, "height": 23}}, "b8": {"b8": {"type": "mesh", "uvs": [0.22333, 0.37534, 0.26909, 0.17934, 0.36237, 0.02534, 0.51725, 0, 0.71789, 0.11534, 0.79005, 0.36534, 0.87453, 0.46134, 0.95373, 0.36134, 1, 0.48534, 1, 0.70333, 0.94493, 0.91733, 0.73901, 0.99333, 0.41165, 0.99133, 0.13885, 0.96133, 0.02093, 0.76733, 0, 0.53134, 0, 0.37934, 0.14589, 0.46334, 0.51548, 0.20334, 0.52076, 0.44534, 0.50668, 0.66534, 0.5102, 0.86934, 0.37996, 0.41134, 0.27788, 0.64134, 0.20044, 0.80134, 0.63164, 0.43734, 0.7126, 0.67534, 0.8622, 0.79934], "triangles": [12, 21, 11, 21, 26, 11, 11, 27, 10, 11, 26, 27, 13, 24, 12, 12, 24, 21, 21, 23, 20, 23, 21, 24, 13, 14, 24, 10, 27, 9, 25, 26, 20, 26, 21, 20, 24, 14, 23, 9, 27, 6, 14, 17, 23, 14, 15, 17, 27, 26, 6, 23, 22, 20, 6, 8, 9, 6, 25, 5, 6, 26, 25, 17, 0, 23, 15, 16, 17, 6, 7, 8, 20, 19, 25, 20, 22, 19, 23, 0, 22, 25, 19, 18, 19, 22, 18, 5, 25, 4, 0, 1, 22, 22, 1, 18, 25, 18, 4, 1, 2, 18, 18, 3, 4, 18, 2, 3], "vertices": [3, 96, 10.06, -14.29, 0.53077, 97, -4, -14.22, 0.3368, 98, -12.66, -14.27, 0.13243, 3, 96, 1.48, -11.84, 0.94155, 97, -12.53, -11.6, 0.05346, 98, -21.2, -11.69, 0.005, 2, 96, -5.2, -7.04, 0.99963, 97, -19.13, -6.69, 0.00037, 1, 96, -6.17, 0.72, 1, 2, 96, -0.9, 10.65, 0.96934, 97, -14.5, 10.93, 0.03066, 3, 96, 10.16, 14.05, 0.50504, 97, -3.37, 14.12, 0.40823, 98, -12.14, 14.07, 0.08673, 3, 96, 14.47, 18.19, 0.15182, 97, 1.01, 18.18, 0.56938, 98, -7.78, 18.15, 0.27881, 3, 96, 10.14, 22.24, 0.09332, 97, -3.23, 22.3, 0.59609, 98, -12.04, 22.25, 0.31059, 3, 96, 15.64, 24.44, 0.08054, 97, 2.3, 24.41, 0.58001, 98, -6.51, 24.38, 0.33945, 3, 96, 25.23, 24.26, 0.02325, 97, 11.89, 24.04, 0.46263, 98, 3.08, 24.05, 0.51412, 3, 96, 34.6, 21.33, 0.00017, 97, 21.19, 20.93, 0.30401, 98, 12.39, 20.98, 0.69582, 2, 97, 24.15, 10.52, 0.12484, 98, 15.39, 10.58, 0.87516, 2, 97, 23.44, -5.83, 0.01504, 98, 14.74, -5.78, 0.98496, 3, 96, 35.76, -19.01, 0.00067, 97, 21.6, -19.41, 0.19356, 98, 12.96, -19.36, 0.80577, 3, 96, 27.11, -24.74, 0.02891, 97, 12.85, -24.98, 0.33755, 98, 4.23, -24.97, 0.63354, 3, 96, 16.71, -25.59, 0.09737, 97, 2.43, -25.64, 0.45431, 98, -6.18, -25.66, 0.44832, 3, 96, 10.02, -25.46, 0.1098, 97, -4.25, -25.38, 0.47489, 98, -12.87, -25.43, 0.41531, 3, 96, 13.86, -18.24, 0.19734, 97, -0.28, -18.23, 0.45318, 98, -8.92, -18.27, 0.34948, 2, 96, 2.77, 0.46, 0.99981, 97, -11.01, 0.67, 0.00019, 2, 96, 13.43, 0.52, 0.74993, 97, -0.36, 0.53, 0.25007, 2, 97, 9.29, -0.54, 0.07756, 98, 0.57, -0.54, 0.92244, 1, 98, 9.55, -0.67, 1, 3, 96, 11.8, -6.49, 0.6379, 97, -2.12, -6.45, 0.32085, 98, -10.81, -6.49, 0.04125, 3, 96, 21.82, -11.79, 0.07732, 97, 7.8, -11.93, 0.42417, 98, -0.87, -11.94, 0.49851, 3, 96, 28.78, -15.79, 0.01206, 97, 14.69, -16.07, 0.24652, 98, 6.03, -16.05, 0.74142, 3, 96, 13.18, 6.07, 0.49965, 97, -0.5, 6.08, 0.47432, 98, -9.25, 6.04, 0.02603, 3, 96, 23.73, 9.92, 0.02658, 97, 10.11, 9.73, 0.4504, 98, 1.36, 9.73, 0.52303, 3, 96, 29.33, 17.29, 0.00508, 97, 15.85, 17, 0.33943, 98, 7.06, 17.03, 0.65548], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 6, 36, 36, 38, 38, 40, 40, 42, 36, 44, 44, 46, 46, 48, 36, 50, 50, 52, 52, 54], "width": 50, "height": 44}}, "b7": {"b7": {"type": "mesh", "uvs": [0.04953, 0.52966, 0.11619, 0.26716, 0.28952, 0.09967, 0.61619, 0.01467, 0.92285, 0.16217, 0.95619, 0.42716, 0.89952, 0.67716, 0.74952, 0.88716, 0.36286, 1, 0.12952, 0.93466, 0.07619, 0.69466, 0.59279, 0.14466, 0.48946, 0.38466, 0.4328, 0.60216, 0.38613, 0.82466], "triangles": [8, 14, 7, 8, 9, 14, 9, 10, 14, 6, 7, 13, 7, 14, 13, 14, 10, 13, 10, 0, 13, 13, 0, 12, 13, 12, 6, 6, 12, 5, 5, 11, 4, 11, 5, 12, 0, 1, 12, 1, 2, 12, 12, 2, 11, 11, 3, 4, 11, 2, 3], "vertices": [2, 88, 7.81, -6.54, 0.26436, 89, 1.35, -6.44, 0.73564, 2, 88, 2.47, -6.64, 0.80949, 89, -3.96, -7.03, 0.19051, 2, 88, -1.35, -4.79, 0.97646, 89, -7.93, -5.54, 0.02354, 1, 88, -4.02, -0.34, 1, 1, 88, -2.08, 4.77, 1, 2, 88, 3, 6.35, 0.97944, 89, -4.63, 5.95, 0.02056, 2, 88, 8.07, 6.55, 0.56107, 89, 0.4, 6.61, 0.43893, 2, 88, 12.64, 5.21, 0.09771, 89, 5.08, 5.7, 0.90229, 1, 89, 8.95, 0.83, 1, 1, 89, 8.73, -2.9, 1, 2, 88, 10.96, -5.46, 0.03048, 89, 4.38, -5.08, 0.96952, 1, 88, -1.41, -0.15, 1, 2, 88, 3.61, -0.68, 0.98535, 89, -3.37, -0.99, 0.01465, 2, 88, 8.04, -0.61, 0.03661, 89, 1.03, -0.52, 0.96339, 2, 88, 12.54, -0.38, 0, 89, 5.49, 0.13, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 6, 22, 22, 24, 24, 26, 26, 28], "width": 15, "height": 20}}, "e6": {"e6": {"type": "mesh", "uvs": [0.76636, 0.02256, 0.96263, 0.1288, 0.97542, 0.33743, 0.90929, 0.54095, 0.70449, 0.72911, 0.54236, 0.75599, 0.47623, 0.84175, 0.46556, 0.97871, 0.1925, 0.98383, 0, 0.90319, 0.1029, 0.77263, 0.0837, 0.68047, 0.0133, 0.53071, 0.1797, 0.34767, 0.3589, 0.1416, 0.52103, 0.03792, 0.73011, 0.0904, 0.6341, 0.22224, 0.48903, 0.41936, 0.37171, 0.61135, 0.30984, 0.72399, 0.2501, 0.82511], "triangles": [8, 21, 7, 8, 9, 21, 7, 21, 6, 9, 10, 21, 21, 20, 6, 6, 20, 5, 21, 10, 20, 10, 11, 20, 4, 5, 19, 5, 20, 19, 4, 19, 3, 20, 11, 19, 11, 12, 19, 12, 13, 19, 19, 18, 3, 19, 13, 18, 3, 18, 2, 2, 18, 17, 17, 1, 2, 17, 16, 1, 13, 14, 18, 18, 14, 17, 14, 15, 17, 17, 15, 16, 16, 0, 1, 16, 15, 0], "vertices": [1, 43, -6.67, -0.57, 1, 1, 43, -5.35, 3.17, 1, 1, 43, -0.6, 5.34, 1, 2, 43, 4.48, 6.37, 0.90057, 44, -2.75, 6.47, 0.09943, 2, 43, 10, 5.33, 0.30249, 44, 2.73, 5.24, 0.69751, 2, 43, 11.55, 3.34, 0.06866, 44, 4.22, 3.2, 0.93134, 2, 43, 13.91, 3.25, 0.00088, 44, 6.57, 3.03, 0.99912, 1, 44, 9.84, 4.08, 1, 1, 44, 11.39, 0.29, 1, 1, 44, 10.52, -3.12, 1, 1, 44, 6.92, -2.82, 1, 2, 43, 12.44, -3.74, 0.02889, 44, 4.87, -3.9, 0.97111, 2, 43, 9.39, -6.14, 0.30941, 44, 1.73, -6.21, 0.69059, 2, 43, 4.2, -5.59, 0.89842, 44, -3.43, -5.48, 0.10158, 1, 43, -1.59, -5.08, 1, 1, 43, -4.91, -3.82, 1, 1, 43, -4.9, -0.42, 1, 1, 43, -1.3, -0.49, 1, 1, 43, 4.08, -0.62, 1, 2, 43, 9.19, -0.41, 0.00084, 44, 1.73, -0.46, 0.99916, 1, 44, 4.69, -0.34, 1, 1, 44, 7.38, -0.3, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 15, "height": 25}}, "d4": {"d4": {"type": "mesh", "uvs": [0.42446, 0.12954, 0.4504, 0.07456, 0.57143, 0.011, 0.6903, 0.0488, 0.70759, 0.10205, 0.76162, 0.10549, 0.8524, 0.14672, 0.89346, 0.2979, 0.9194, 0.4611, 0.95614, 0.66897, 1, 0.91808, 0.79188, 0.96446, 0.5844, 0.97305, 0.28398, 0.94213, 0.12404, 0.88372, 0, 0.80126, 0.09378, 0.61228, 0.20401, 0.39238, 0.23211, 0.24808, 0.24075, 0.16046, 0.35746, 0.13469, 0.58872, 0.11751, 0.59088, 0.27041, 0.57791, 0.42502, 0.56062, 0.72051, 0.79404, 0.72566, 0.77459, 0.4611, 0.74865, 0.279, 0.74217, 0.1742, 0.3661, 0.19482, 0.3661, 0.28072, 0.34665, 0.40613, 0.28614, 0.66554], "triangles": [9, 26, 8, 24, 23, 26, 23, 27, 26, 26, 7, 8, 9, 25, 26, 24, 26, 25, 25, 9, 10, 25, 12, 24, 11, 25, 10, 25, 11, 12, 14, 15, 32, 13, 14, 32, 12, 13, 24, 23, 32, 31, 24, 32, 23, 32, 15, 16, 13, 32, 24, 32, 17, 31, 16, 17, 32, 21, 2, 3, 21, 3, 4, 1, 2, 21, 0, 1, 21, 28, 21, 4, 0, 21, 22, 26, 27, 7, 28, 4, 5, 28, 5, 6, 29, 20, 0, 19, 20, 29, 18, 19, 29, 28, 22, 21, 29, 0, 22, 27, 28, 6, 22, 28, 27, 30, 18, 29, 30, 29, 22, 27, 6, 7, 17, 18, 30, 23, 30, 22, 23, 22, 27, 31, 17, 30, 31, 30, 23], "vertices": [3, 47, 12.65, 5.56, 0.00556, 48, 7.48, 3.5, 0.40297, 49, 0.76, 4.11, 0.59146, 2, 48, 9.32, 2.13, 0.05572, 49, 2.99, 3.57, 0.94428, 2, 48, 10.67, -2.16, 0.00035, 49, 5.9, 0.15, 0.99965, 2, 48, 8.24, -5.29, 0.12152, 49, 4.88, -3.69, 0.87848, 3, 47, 13.94, -3.19, 0.00028, 48, 6.09, -5.24, 0.36114, 49, 2.88, -4.47, 0.63858, 3, 47, 13.84, -4.86, 0.00729, 48, 5.51, -6.81, 0.55281, 49, 2.95, -6.15, 0.4399, 3, 47, 12.3, -7.72, 0.04707, 48, 3.19, -9.08, 0.67217, 49, 1.69, -9.14, 0.28076, 3, 47, 6.44, -9.13, 0.34765, 48, -2.83, -8.68, 0.57137, 49, -4.01, -11.11, 0.08099, 6, 47, 0.1, -10.09, 0.06522, 48, -9.17, -7.71, 0.03528, 49, -10.23, -12.67, 0.00039, 52, -0.78, 7.26, 0.3871, 53, -8.26, 5.89, 1e-05, 10, -11.03, -1.11, 0.512, 4, 48, -17.28, -6.57, 3e-05, 52, 7.29, 5.91, 0.33684, 53, -0.07, 5.99, 0.35113, 10, -12.17, 7, 0.312, 1, 53, 9.74, 6.11, 1, 3, 50, 10.01, 17.38, 0.00024, 51, -0.79, 17.52, 0.01809, 53, 10.71, -0.52, 0.98168, 3, 50, 12.9, 11.62, 0.02676, 51, 3.07, 12.37, 0.29782, 53, 10.23, -6.94, 0.67542, 2, 51, 7.27, 3.96, 0.92215, 53, 7.86, -16.03, 0.07785, 2, 51, 8.14, -1.42, 0.99999, 53, 4.97, -20.66, 1e-05, 2, 50, 14.03, -7.66, 0.03516, 51, 7.62, -6.41, 0.96484, 4, 48, -7.81, 18.52, 0.00462, 50, 6.12, -7.95, 0.33719, 51, -0.12, -8.11, 0.21019, 10, 14.57, 4.78, 0.448, 6, 47, 2.24, 12.15, 0.06848, 48, -0.5, 12.88, 0.14137, 49, -10.24, 9.66, 0.00296, 50, -3.11, -8.27, 0.37829, 51, -9.15, -10.05, 0.0009, 10, 11.15, -3.79, 0.408, 3, 47, 7.89, 11.41, 0.12966, 48, 4.67, 10.5, 0.76209, 49, -4.55, 9.48, 0.10825, 3, 47, 11.31, 11.23, 0.05654, 48, 7.88, 9.3, 0.76395, 49, -1.12, 9.62, 0.17952, 3, 47, 12.4, 7.63, 0.02509, 48, 7.85, 5.55, 0.66034, 49, 0.31, 6.15, 0.31457, 2, 48, 6.53, -1.53, 0.07333, 49, 1.84, -0.88, 0.92667, 1, 48, 0.78, 0.04, 1, 4, 47, 1.25, 0.53, 0.51556, 48, -4.91, 2.09, 0.00152, 50, -6.6, 2.86, 0.01091, 10, -0.44, -2.52, 0.472, 6, 47, -10.29, 0.79, 0.00143, 50, 4.17, 6.99, 0.2188, 51, -4.69, 6.26, 0.05567, 52, 5.51, -6.39, 0.14275, 53, 0.37, -6.43, 0.19735, 10, 0.09, 9.01, 0.384, 2, 53, 1.48, 0.73, 0.616, 10, -7.14, 9.21, 0.384, 5, 47, -0.01, -5.6, 0.11784, 48, -7.94, -3.39, 0.02218, 49, -10.77, -8.22, 0.00012, 52, -2.14, 2.98, 0.33986, 10, -6.54, -1.11, 0.52, 3, 47, 7.07, -4.62, 0.31264, 48, -0.89, -4.57, 0.60592, 49, -3.82, -6.56, 0.08144, 3, 47, 11.15, -4.33, 0.02774, 48, 3.09, -5.5, 0.65468, 49, 0.22, -5.87, 0.31759, 3, 47, 10.07, 7.31, 0.05382, 48, 5.52, 5.93, 0.72182, 49, -1.98, 5.6, 0.22436, 3, 47, 6.72, 7.23, 0.22012, 48, 2.3, 6.85, 0.71479, 49, -5.31, 5.2, 0.06509, 5, 47, 1.81, 7.71, 0.16064, 48, -2.23, 8.78, 0.1397, 49, -10.24, 5.21, 0.00129, 50, -4.39, -4, 0.36238, 10, 6.73, -3.26, 0.336, 3, 48, -11.45, 13.36, 0.00088, 50, 5.62, -1.66, 0.91087, 51, -1.72, -2, 0.08825], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 42, 44, 44, 46, 46, 48, 22, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64], "width": 31, "height": 39}}, "d5": {"d5": {"type": "mesh", "uvs": [0.70169, 0, 0.90268, 0.12879, 0.95191, 0.3751, 0.87806, 0.58141, 0.67191, 0.80457, 0.53345, 0.85299, 0.47807, 0.97509, 0.24115, 0.98772, 0.02884, 0.92036, 0.06884, 0.75404, 0, 0.67615, 0.035, 0.45089, 0.18269, 0.23405, 0.30576, 0.04879, 0.66883, 0.12668, 0.5519, 0.30352, 0.43806, 0.54983, 0.31499, 0.78562, 0.27192, 0.89299], "triangles": [7, 18, 6, 7, 8, 18, 6, 18, 5, 8, 9, 18, 18, 17, 5, 18, 9, 17, 4, 5, 16, 5, 17, 16, 4, 16, 3, 16, 17, 10, 17, 9, 10, 10, 11, 16, 16, 15, 3, 3, 15, 2, 2, 14, 1, 14, 2, 15, 11, 12, 16, 16, 12, 15, 12, 13, 15, 15, 13, 14, 14, 0, 1, 14, 13, 0], "vertices": [1, 58, -3.8, 0.79, 1, 1, 58, -2.01, 3.89, 1, 2, 58, 2.4, 5.57, 0.95464, 59, -4.59, 5.29, 0.04536, 2, 58, 6.44, 5.52, 0.58064, 59, -0.55, 5.5, 0.41936, 2, 58, 11.18, 3.87, 0.02257, 59, 4.27, 4.14, 0.97743, 1, 59, 5.67, 2.68, 1, 1, 59, 8.1, 2.66, 1, 1, 59, 9.21, -0.23, 1, 1, 59, 8.78, -3.24, 1, 2, 58, 12.01, -3.99, 0.02726, 59, 5.6, -3.64, 0.97274, 2, 58, 10.77, -5.19, 0.0982, 59, 4.44, -4.92, 0.9018, 2, 58, 6.5, -5.72, 0.544, 59, 0.21, -5.71, 0.456, 2, 58, 2.05, -4.77, 0.97114, 59, -4.29, -5.05, 0.02886, 1, 58, -1.74, -4.01, 1, 1, 58, -1.36, 0.92, 1, 1, 58, 2.25, 0.2, 1, 2, 58, 7.15, -0.19, 0.04205, 59, 0.51, -0.15, 0.95795, 2, 58, 11.87, -0.73, 0.00031, 59, 5.26, -0.41, 0.99969, 1, 59, 7.37, -0.36, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 28, 28, 30, 30, 32, 32, 34, 34, 36], "width": 13, "height": 19}}, "nvwashi": {"nvwashi": {"x": 1.18, "y": 1.53, "width": 138, "height": 95}}, "e1": {"e1": {"x": 13.35, "y": 6.7, "rotation": -75.96, "width": 45, "height": 40}}, "d3": {"d3": {"type": "mesh", "uvs": [0.17914, 0.01544, 0.41201, 0.06283, 0.55001, 0.18131, 0.84326, 0.27471, 1, 0.39598, 0.81451, 0.50749, 0.75988, 0.68313, 0.69664, 0.83506, 0.56726, 0.97725, 0.27114, 0.85737, 0.14177, 0.67198, 0.10439, 0.46846, 0.06989, 0.2761, 0.06702, 0.12556, 0.24241, 0.1158, 0.33154, 0.23707, 0.46379, 0.4824, 0.40054, 0.36508, 0.71391, 0.35974, 0.47241, 0.68592, 0.49829, 0.85319], "triangles": [9, 20, 8, 8, 20, 7, 9, 19, 20, 9, 10, 19, 20, 19, 7, 7, 19, 6, 6, 19, 16, 19, 10, 16, 6, 16, 5, 10, 11, 16, 16, 18, 5, 5, 18, 4, 11, 17, 16, 16, 17, 18, 11, 12, 17, 18, 3, 4, 12, 15, 17, 18, 17, 2, 17, 15, 2, 18, 2, 3, 15, 13, 14, 15, 12, 13, 15, 14, 2, 14, 1, 2, 13, 0, 14, 14, 0, 1], "vertices": [1, 56, -4.13, 0.64, 1, 1, 56, -1.34, 3.57, 1, 2, 56, 3.1, 4.24, 0.99886, 57, -7.72, 1.83, 0.00114, 2, 56, 7.64, 7.54, 0.78959, 57, -4.49, 6.42, 0.21041, 2, 56, 12.27, 8.46, 0.49957, 57, -0.42, 8.81, 0.50043, 2, 56, 14.66, 4.38, 0.14313, 57, 3.17, 5.72, 0.85687, 1, 57, 8.94, 4.67, 1, 1, 57, 13.92, 3.5, 1, 1, 57, 18.54, 1.29, 1, 1, 57, 14.44, -3.33, 1, 2, 56, 15.92, -7.61, 0.00137, 57, 8.26, -5.2, 0.99863, 2, 56, 9.43, -5.78, 0.39046, 57, 1.53, -5.59, 0.60954, 2, 56, 3.3, -4.05, 0.99058, 57, -4.83, -5.94, 0.00942, 1, 56, -1.37, -2.33, 1, 1, 56, -0.67, 0.41, 1, 1, 56, 3.58, 0.32, 1, 1, 57, 2.17, 0.14, 1, 2, 56, 7.92, -0.14, 0.99863, 57, -1.73, -0.75, 0.00137, 2, 56, 9.53, 4.61, 0.6742, 57, -1.75, 4.27, 0.3258, 1, 57, 8.88, 0.07, 1, 1, 57, 14.41, 0.31, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 28, 28, 30, 30, 34, 34, 32, 34, 36, 32, 38, 38, 40], "width": 16, "height": 33}}, "A_glow_orange_small4": {"A_glow_orange_small": {"rotation": 48.17, "width": 64, "height": 64}}, "e5": {"e5": {"type": "mesh", "uvs": [0, 0.63594, 0.13422, 0.44452, 0.33172, 0.20752, 0.46009, 0.03129, 0.62056, 0.00395, 0.69797, 0.02269, 0.77115, 0.04041, 0.92668, 0.25614, 0.95369, 0.40054, 1, 0.6481, 1, 0.91852, 0.93656, 0.94586, 0.83781, 0.94282, 0.83287, 0.6724, 0.77609, 0.42933, 0.77362, 0.69367, 0.75634, 0.93979, 0.52921, 0.92459, 0.32925, 0.88509, 0.39097, 0.7514, 0.44623, 0.6255, 0.38061, 0.74858, 0.24373, 0.93781, 0.0809, 0.80906, 0.52012, 0.40259, 0.77351, 0.22652, 0.57125, 0.19968, 0.42878, 0.18686, 0.39669, 0.30536, 0.28806, 0.55755, 0.20413, 0.70035, 0.58547, 0.70717, 0.64031, 0.42217, 0.67791, 0.21383, 0.92184, 0.66517, 0.88893, 0.41317, 0.8513, 0.24156], "triangles": [35, 36, 7, 36, 25, 6, 25, 14, 33, 26, 3, 4, 27, 3, 26, 27, 2, 3, 26, 4, 33, 33, 4, 5, 33, 5, 6, 28, 2, 27, 26, 28, 27, 33, 6, 25, 32, 26, 33, 14, 25, 36, 6, 7, 36, 34, 8, 9, 13, 35, 34, 34, 9, 10, 12, 13, 34, 11, 12, 34, 10, 11, 34, 35, 7, 8, 14, 36, 35, 34, 35, 8, 13, 14, 35, 24, 26, 32, 31, 24, 32, 14, 32, 33, 15, 32, 14, 20, 24, 31, 31, 19, 20, 17, 19, 31, 18, 19, 17, 31, 32, 15, 16, 31, 15, 17, 31, 16, 24, 28, 26, 1, 2, 28, 29, 28, 24, 29, 1, 28, 20, 29, 24, 30, 1, 29, 0, 1, 30, 21, 29, 20, 30, 29, 21, 23, 0, 30, 21, 22, 30, 23, 30, 22], "vertices": [1, 38, 9.02, -5.47, 1, 2, 38, 2.45, -5.54, 0.84206, 37, 10.12, -5.47, 0.15794, 1, 9, 7.98, 0.04, 1, 1, 9, 3.95, -4.61, 1, 1, 9, -1.18, -5.4, 1, 1, 9, -3.66, -4.95, 1, 1, 9, -6.01, -4.53, 1, 1, 9, -11.07, 1, 1, 1, 41, 5.91, 3.79, 1, 2, 41, 12.25, 1.93, 0.06209, 42, 1.41, 3, 0.93791, 1, 42, 8.44, 3, 1, 1, 42, 9.15, 0.97, 1, 1, 42, 9.07, -2.19, 1, 2, 41, 10.18, -3.04, 0.16506, 42, 2.04, -2.34, 0.83494, 3, 40, -6.13, 3.08, 0.00051, 39, 2.46, 4.44, 0.26652, 41, 3.78, -1.53, 0.73296, 3, 40, 0.37, 5.32, 0.68681, 39, 9.3, 5.08, 0.30301, 41, 9.73, -4.97, 0.01018, 1, 40, 6.59, 6.95, 1, 1, 40, 8.66, -0.03, 1, 1, 40, 9.84, -6.4, 1, 4, 38, 3, 5.9, 0.01488, 37, 10.32, 5.99, 0.03381, 40, 5.91, -5.71, 0.9488, 39, 12.07, -6.95, 0.0025, 4, 38, -0.62, 5.06, 0.21542, 37, 6.72, 5.04, 0.28278, 40, 2.23, -5.15, 0.40872, 39, 8.63, -5.53, 0.09308, 4, 38, 3.16, 5.6, 0.89414, 37, 10.49, 5.69, 0.05559, 40, 5.95, -6.05, 0.04542, 39, 12.03, -7.28, 0.00485, 1, 38, 9.75, 5.57, 1, 1, 38, 10.69, -0.56, 1, 3, 37, 0.88, 2.8, 0.57288, 40, -4.02, -4.87, 0.00891, 39, 2.62, -3.78, 0.41821, 1, 9, -6.16, 0.31, 1, 1, 9, 0.32, -0.29, 1, 1, 9, 4.89, -0.55, 1, 1, 37, 1.73, -1.81, 1, 4, 38, 1.4, 0.09, 0.99917, 37, 8.89, 0.14, 0.00023, 40, 2.27, -10.51, 0.00051, 39, 7.4, -10.75, 0.0001, 2, 38, 5.96, 0.53, 0.9998, 40, 6.67, -11.79, 0.0002, 4, 38, -1.98, 9.81, 0.00081, 37, 5.22, 9.74, 0.003, 40, 2.73, -0.24, 0.99423, 39, 10.28, -0.88, 0.00196, 2, 39, 2.72, 0.1, 0.99534, 41, 1.49, -5.23, 0.00466, 1, 9, -3.1, 0.03, 1, 1, 42, 1.86, 0.5, 1, 1, 41, 5.18, 1.82, 1, 1, 9, -8.66, 0.66, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 40, 48, 12, 50, 50, 28, 8, 52, 52, 48, 52, 54, 6, 54, 54, 56, 56, 58, 58, 60, 34, 62, 62, 64, 50, 66, 66, 52, 64, 66, 8, 10, 10, 12, 66, 10, 22, 68, 68, 70, 14, 72, 72, 50, 70, 72, 14, 16, 16, 18, 72, 12], "width": 32, "height": 26}}, "e7": {"e7": {"type": "mesh", "uvs": [0.26054, 0.00464, 0.54396, 0.02896, 0.83653, 0.1864, 1, 0.41168, 0.92339, 0.62544, 0.76567, 0.71247, 0.7291, 0.80079, 0.84567, 0.84431, 0.83882, 0.97615, 0.56453, 0.97231, 0.27425, 0.93903, 0.28111, 0.82895, 0.27425, 0.74703, 0.07311, 0.6152, 0.07083, 0.39504, 0.06168, 0.15952, 0.40451, 0.12496, 0.49366, 0.37072, 0.50509, 0.59856, 0.52337, 0.77007, 0.53938, 0.87119], "triangles": [7, 8, 20, 10, 20, 9, 7, 20, 6, 20, 8, 9, 10, 11, 20, 11, 19, 20, 20, 19, 6, 11, 12, 19, 6, 19, 5, 12, 18, 19, 19, 18, 5, 12, 13, 18, 5, 18, 4, 4, 18, 3, 13, 14, 18, 14, 17, 18, 18, 17, 3, 17, 2, 3, 17, 15, 16, 17, 14, 15, 17, 16, 2, 16, 1, 2, 15, 0, 16, 16, 0, 1], "vertices": [1, 45, -6.28, -1.71, 1, 1, 45, -5.15, 2.14, 1, 2, 45, -0.7, 5.68, 0.99821, 46, -8.41, 5.21, 0.00179, 2, 45, 5.18, 7.19, 0.79234, 46, -2.61, 7.06, 0.20766, 2, 45, 10.34, 5.41, 0.21152, 46, 2.63, 5.57, 0.78848, 2, 45, 12.2, 2.94, 0.01548, 46, 4.63, 3.2, 0.98452, 1, 46, 6.79, 2.52, 1, 1, 46, 8, 4.06, 1, 1, 46, 11.28, 3.71, 1, 1, 46, 10.88, -0.12, 1, 1, 46, 9.74, -4.1, 1, 2, 45, 14.18, -4.18, 0.0119, 46, 7, -3.79, 0.9881, 2, 45, 12.14, -4, 0.10989, 46, 4.95, -3.73, 0.89011, 2, 45, 8.49, -6.35, 0.63692, 46, 1.45, -6.28, 0.36308, 2, 45, 3.03, -5.65, 0.99443, 46, -4.04, -5.88, 0.00557, 1, 45, -2.82, -4.99, 1, 1, 45, -3.03, -0.11, 1, 1, 45, 3.22, 0.3, 1, 2, 45, 8.89, -0.3, 0.01938, 46, 1.5, -0.22, 0.98062, 2, 45, 13.17, -0.62, 0.00111, 46, 5.8, -0.3, 0.99889, 1, 46, 8.34, -0.27, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 14, "height": 25}}, "e2": {"e2": {"type": "mesh", "uvs": [0.29551, 0.46639, 0.00763, 0.34976, 0.33957, 0.23661, 0.4982, 0.02424, 0.79782, 0.07124, 0.95938, 0.25924, 0.9212, 0.40372, 0.81545, 0.49772, 0.80838, 0.66312, 0.8037, 0.77276, 0.57751, 0.88765, 0.40126, 0.98687, 0.27495, 0.96772, 0.16626, 0.83891, 0.09282, 0.62132, 0.14863, 0.50643, 0.62157, 0.2662, 0.54812, 0.48031, 0.48057, 0.64046, 0.4042, 0.86153], "triangles": [11, 19, 10, 11, 12, 19, 12, 13, 19, 9, 10, 18, 10, 19, 18, 19, 13, 18, 13, 14, 18, 15, 18, 14, 9, 18, 8, 18, 15, 0, 18, 17, 8, 8, 17, 7, 18, 0, 17, 6, 7, 16, 7, 17, 16, 16, 17, 2, 17, 0, 2, 0, 1, 2, 6, 16, 5, 2, 3, 16, 16, 4, 5, 16, 3, 4], "vertices": [2, 33, 5.72, -3.73, 0.90646, 34, -3.91, -3.71, 0.09354, 1, 33, 3.44, -8.82, 1, 1, 33, -0.51, -4.14, 1, 1, 33, -6.61, -2.66, 1, 1, 33, -6.21, 2.28, 1, 1, 33, -1.68, 5.73, 1, 1, 33, 2.27, 5.82, 1, 2, 33, 5.07, 4.61, 0.97484, 34, -4.53, 4.63, 0.02516, 2, 33, 9.49, 5.29, 0.46103, 34, -0.11, 5.29, 0.53897, 2, 33, 12.41, 5.75, 0.13186, 34, 2.82, 5.73, 0.86814, 1, 34, 6.5, 2.71, 1, 1, 34, 9.63, 0.4, 1, 1, 34, 9.47, -1.68, 1, 2, 33, 15.98, -3.97, 3e-05, 34, 6.35, -4, 0.99997, 2, 33, 10.41, -6.17, 0.29406, 34, 0.77, -6.18, 0.70594, 2, 33, 7.2, -5.85, 0.60637, 34, -2.44, -5.84, 0.39363, 1, 33, -0.53, 0.45, 1, 1, 33, 5.37, 0.32, 1, 2, 33, 9.82, 0.02, 0.1301, 34, 0.2, 0.02, 0.8699, 1, 34, 6.29, -0.14, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 32, 34, 34, 36, 14, 16, 16, 18, 36, 38], "width": 16, "height": 27}}, "c1": {"c1": {"x": 16.99, "y": 1.47, "rotation": -85.99, "width": 43, "height": 39}}, "c2": {"c2": {"type": "mesh", "uvs": [0.85986, 0.02762, 0.96233, 0.14326, 0.94681, 0.33677, 0.93749, 0.50905, 0.9406, 0.73561, 0.73255, 0.90081, 0.37855, 0.99285, 0.04939, 0.98105, 0.00282, 0.76393, 0.06492, 0.53501, 0.24502, 0.45713, 0.44686, 0.40757, 0.50276, 0.25654, 0.59902, 0.10786, 0.72323, 0.0347, 0.81018, 0.13146, 0.73566, 0.30138, 0.69219, 0.45242, 0.56798, 0.65301, 0.36924, 0.74269, 0.20156, 0.76865], "triangles": [7, 20, 6, 20, 19, 6, 6, 19, 5, 7, 8, 20, 19, 18, 5, 4, 18, 3, 4, 5, 18, 10, 19, 20, 20, 8, 9, 18, 17, 3, 20, 9, 10, 18, 10, 11, 18, 19, 10, 18, 11, 17, 3, 17, 2, 11, 12, 17, 17, 16, 2, 17, 12, 16, 16, 15, 2, 2, 15, 1, 12, 13, 16, 16, 13, 15, 15, 0, 1, 13, 14, 15, 15, 14, 0], "vertices": [1, 67, -5.1, -0.2, 1, 1, 67, -3.08, 2.64, 1, 2, 67, 1.56, 4.06, 0.94225, 68, -4.99, 4.59, 0.05775, 2, 67, 5.65, 5.41, 0.45341, 68, -0.78, 5.52, 0.54659, 2, 67, 10.93, 7.46, 0.01226, 68, 4.68, 7.03, 0.98774, 1, 68, 9.68, 4.26, 1, 1, 68, 13.62, -1.65, 1, 1, 68, 14.94, -7.78, 1, 1, 68, 9.92, -10.02, 1, 1, 68, 4.08, -10.34, 1, 2, 67, 9.07, -7.36, 0.01896, 68, 1.32, -7.53, 0.98104, 2, 67, 6.56, -4.2, 0.43016, 68, -0.85, -4.14, 0.56984, 2, 67, 2.65, -4.54, 0.97814, 68, -4.78, -4.08, 0.02186, 1, 67, -1.47, -4.14, 1, 1, 67, -4.02, -2.57, 1, 1, 67, -2.33, -0.17, 1, 2, 67, 2.14, 0, 0.99999, 68, -4.82, 0.49, 1e-05, 2, 67, 5.97, 0.55, 0.9169, 68, -0.96, 0.65, 0.0831, 1, 68, 4.49, -0.35, 1, 1, 68, 7.62, -3.42, 1, 1, 68, 9.07, -6.34, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 19, "height": 25}}, "a1": {"a1": {"type": "mesh", "uvs": [0.11317, 0.1441, 0.22132, 0.0331, 0.47178, 0.01628, 0.69947, 0.10037, 0.92716, 0.2921, 0.93286, 0.5511, 0.96986, 0.95809, 0.72793, 0.98837, 0.3807, 1, 0.08755, 1, 0.01355, 0.63182, 0.0107, 0.31564, 0.54009, 0.33246, 0.58563, 0.58473, 0.24409, 0.30219, 0.22986, 0.5881], "triangles": [10, 11, 15, 15, 12, 13, 15, 14, 12, 15, 11, 14, 4, 12, 3, 4, 13, 12, 14, 2, 12, 12, 2, 3, 11, 0, 14, 0, 1, 14, 14, 1, 2, 9, 15, 8, 8, 13, 7, 8, 15, 13, 9, 10, 15, 7, 5, 6, 7, 13, 5, 13, 4, 5], "vertices": [2, 13, 16.68, 10.35, 0.04043, 14, 10.43, 9.14, 0.95957, 2, 13, 18.62, 7.18, 0.00415, 14, 11.97, 5.75, 0.99585, 1, 14, 10.48, -0.59, 1, 2, 13, 15.12, -4.84, 0.03217, 14, 7.02, -5.75, 0.96783, 2, 13, 9.98, -9.99, 0.31102, 14, 1.3, -10.22, 0.68898, 2, 13, 4.34, -9.2, 0.67216, 14, -4.21, -8.75, 0.32784, 2, 13, -4.65, -8.67, 0.99102, 14, -13.07, -7.13, 0.00898, 1, 13, -4.27, -2.36, 1, 2, 13, -3.04, 6.59, 0.91857, 14, -9.6, 7.82, 0.08143, 2, 13, -1.79, 14.11, 0.75191, 14, -7.43, 15.13, 0.24809, 2, 13, 6.52, 14.67, 0.44517, 14, 0.88, 14.68, 0.55483, 2, 13, 13.39, 13.6, 0.14273, 14, 7.57, 12.77, 0.85727, 2, 13, 10.76, 0.09, 0.00211, 14, 3.3, -0.32, 0.99789, 2, 13, 5.09, -0.17, 0.99653, 14, -2.35, 0.12, 0.00347, 2, 13, 12.69, 7.57, 0.07325, 14, 6.13, 6.87, 0.92675, 2, 13, 6.54, 8.97, 0.44999, 14, 0.2, 9.01, 0.55001], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22, 4, 24, 24, 26], "width": 26, "height": 22}}, "a2": {"a2": {"type": "mesh", "uvs": [0.42272, 0.56542, 0.34201, 0.71142, 0.24319, 0.95342, 0.14437, 0.96342, 0, 0.95542, 0.06531, 0.66742, 0.2086, 0.31142, 0.41943, 0.00942, 0.60225, 0, 0.77684, 0.05942, 0.91848, 0.33542, 0.97283, 0.59142, 0.97364, 0.73943, 0.97448, 0.89342, 0.89378, 0.92142, 0.83284, 0.92742, 0.82625, 0.75742, 0.80319, 0.57742, 0.78672, 0.74142, 0.79166, 0.89742, 0.66813, 0.91142, 0.5199, 0.92942, 0.33707, 0.91742, 0.39472, 0.72942, 0.45895, 0.36342, 0.51495, 0.13342, 0.73236, 0.17542, 0.77025, 0.38342, 0.62695, 0.15742, 0.62531, 0.38542, 0.62366, 0.58342, 0.60719, 0.78142, 0.20531, 0.68742, 0.3239, 0.33742, 0.43589, 0.11942, 0.78507, 0.17542, 0.83778, 0.35742, 0.89707, 0.58942, 0.89872, 0.75942], "triangles": [29, 28, 26, 27, 26, 35, 26, 9, 35, 29, 25, 28, 6, 7, 34, 25, 34, 7, 24, 34, 25, 8, 25, 7, 28, 8, 9, 25, 8, 28, 26, 28, 9, 10, 35, 9, 32, 5, 6, 1, 32, 0, 1, 2, 32, 3, 5, 32, 3, 32, 2, 4, 5, 3, 33, 6, 34, 33, 34, 24, 0, 33, 24, 32, 6, 33, 32, 33, 0, 37, 10, 11, 16, 17, 37, 12, 38, 37, 12, 37, 11, 16, 37, 38, 38, 12, 13, 14, 16, 38, 14, 38, 13, 14, 15, 16, 36, 35, 10, 27, 35, 36, 17, 27, 36, 37, 36, 10, 17, 36, 37, 27, 17, 30, 0, 24, 30, 31, 0, 30, 23, 0, 31, 18, 30, 17, 18, 31, 30, 20, 18, 19, 20, 31, 18, 21, 23, 31, 21, 31, 20, 22, 23, 21, 29, 26, 27, 24, 25, 29, 30, 24, 29, 27, 30, 29], "vertices": [4, 20, 9.43, -6.72, 0.07534, 21, 1.18, -6.73, 0.29535, 22, 6.58, 3.5, 0.55295, 23, -1.86, 3.09, 0.07636, 4, 20, 13.59, -9.36, 3e-05, 21, 5.3, -9.43, 0.00187, 22, 11.42, 2.6, 0.0089, 23, 3.03, 3.66, 0.9892, 1, 23, 10.37, 5.5, 1, 1, 23, 12.76, 3.13, 1, 1, 23, 15.78, -0.75, 1, 2, 22, 14.03, -6.52, 0.00203, 23, 8.21, -4.3, 0.99797, 2, 22, 2.94, -6.01, 0.90959, 23, -2.54, -7.07, 0.09041, 1, 7, 3.82, -4.42, 1, 1, 7, -2.4, -4.61, 1, 1, 7, -8.32, -2.87, 1, 1, 24, 3.24, 3.53, 1, 2, 24, 10.62, 2.93, 0.11786, 25, 1.98, 3.08, 0.88214, 1, 25, 5.99, 2.04, 1, 1, 25, 10.16, 0.95, 1, 1, 25, 10.21, -1.9, 1, 1, 25, 9.84, -3.95, 1, 1, 25, 5.18, -2.93, 1, 4, 20, 9.45, 6.22, 0.04496, 21, 1.37, 6.2, 0.21944, 24, 8.36, -2.39, 0.42013, 25, 0.11, -2.39, 0.31547, 4, 20, 14.05, 5.78, 0.0005, 21, 5.96, 5.7, 0.91055, 24, 12.52, -4.42, 0.07919, 25, 4.4, -4.12, 0.00976, 2, 21, 10.33, 5.92, 0.99038, 24, 16.7, -5.69, 0.00962, 2, 21, 10.77, 1.72, 0.99929, 24, 15.69, -9.79, 0.00071, 2, 21, 11.33, -3.31, 0.98013, 22, 14.62, 10.58, 0.01987, 2, 21, 11.07, -9.53, 0.9492, 22, 16.78, 4.74, 0.0508, 3, 20, 14.05, -7.56, 0.00279, 21, 5.78, -7.63, 0.86655, 22, 11.17, 4.45, 0.13066, 3, 20, 3.75, -5.63, 0.25752, 21, -4.49, -5.57, 0.01251, 22, 0.9, 2.38, 0.72997, 1, 7, 0.61, -0.91, 1, 1, 7, -6.76, 0.36, 1, 3, 20, 4.04, 4.97, 0.1817, 21, -4.05, 5.02, 0.00655, 24, 2.86, -1.67, 0.81175, 1, 7, -3.19, -0.19, 1, 1, 20, 4.22, 0.04, 1, 4, 20, 9.77, 0.12, 8e-05, 21, 1.61, 0.1, 0.99802, 24, 6.52, -8.21, 0.00184, 25, -1.3, -8.33, 5e-05, 2, 21, 7.16, -0.39, 0.99813, 22, 9.64, 11.66, 0.00187, 2, 22, 12.65, -1.93, 0.00048, 23, 5.54, -0.31, 0.99952, 2, 22, 2.05, -2.12, 0.99283, 23, -4.53, -3.61, 0.00717, 1, 7, 3.3, -1.34, 1, 1, 7, -8.55, 0.38, 1, 1, 24, 2.92, 0.74, 1, 2, 24, 9.72, 0.52, 0.01891, 25, 1.26, 0.61, 0.98109, 1, 25, 5.87, -0.57, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 0, 48, 48, 50, 52, 54, 54, 34, 56, 58, 58, 60, 60, 62, 6, 64, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 22, 24, 24, 26], "width": 34, "height": 28}}, "a3": {"a3": {"type": "mesh", "uvs": [0.19202, 0.0008, 0.47414, 0.13696, 0.68226, 0.1784, 0.66839, 0.27904, 0.82101, 0.37968, 0.80252, 0.49512, 0.93664, 0.67272, 1, 0.99536, 0.74239, 1, 0.51114, 1, 0.48802, 0.7112, 0.27527, 0.58688, 0.27989, 0.49808, 0.13652, 0.38264, 0.03014, 0.19616, 0.27527, 0.16953, 0.44177, 0.34713, 0.55277, 0.48921, 0.7054, 0.67568, 0.71927, 0.8592], "triangles": [9, 19, 8, 8, 19, 7, 9, 10, 19, 19, 6, 7, 10, 18, 19, 19, 18, 6, 10, 11, 17, 10, 17, 18, 17, 11, 12, 18, 5, 6, 18, 17, 5, 5, 17, 4, 12, 16, 17, 12, 13, 16, 17, 3, 4, 17, 16, 3, 13, 15, 16, 13, 14, 15, 16, 1, 3, 16, 15, 1, 3, 1, 2, 14, 0, 15, 15, 0, 1], "vertices": [1, 16, -3.53, 1.6, 1, 1, 16, 1.82, 3.45, 1, 1, 16, 4.53, 5.64, 1, 2, 16, 6.5, 4.05, 0.97719, 17, -3.79, 3.18, 0.02281, 2, 16, 9.95, 4.68, 0.60982, 17, -0.62, 4.68, 0.39018, 2, 16, 12.18, 2.82, 0.12083, 17, 2.02, 3.46, 0.87917, 1, 17, 6.91, 4.05, 1, 1, 17, 14.87, 2.4, 1, 1, 17, 13.65, -1.53, 1, 1, 17, 12.45, -5.04, 1, 2, 16, 13.86, -4.37, 0.00977, 17, 5.5, -3.05, 0.99023, 2, 16, 9.38, -5.46, 0.36662, 17, 1.46, -5.26, 0.63338, 2, 16, 7.58, -4.16, 0.7043, 17, -0.62, -4.47, 0.2957, 2, 16, 3.9, -4.46, 0.99422, 17, -4.09, -5.71, 0.00578, 1, 16, -0.91, -3.27, 1, 1, 16, 0.72, 0.35, 1, 1, 16, 5.89, 0.09, 1, 2, 16, 9.83, -0.41, 0.05991, 17, 0.58, -0.27, 0.94009, 1, 17, 5.78, 0.53, 1, 1, 17, 10.2, -0.74, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 16, "height": 25}}, "a4": {"a4": {"type": "mesh", "uvs": [0.37602, 0.0112, 0.63482, 0.04481, 0.69514, 0.05264, 1, 0.20952, 1, 0.36936, 0.81539, 0.35456, 0.77839, 0.53808, 0.69052, 0.69496, 0.55639, 0.74232, 0.48702, 0.97912, 0.32514, 0.98208, 0.08002, 0.95544, 0.14939, 0.68312, 0.08464, 0.51144, 0.16327, 0.3516, 0.08002, 0.24504, 0.32977, 0.18584, 0.29277, 0.06744, 0.52402, 0.2332, 0.43152, 0.40192, 0.38989, 0.55288, 0.32977, 0.73344], "triangles": [9, 10, 21, 10, 11, 21, 9, 21, 8, 11, 12, 21, 7, 8, 20, 8, 21, 20, 21, 12, 20, 12, 13, 20, 7, 20, 6, 6, 20, 19, 6, 19, 5, 5, 19, 18, 18, 1, 2, 20, 13, 19, 19, 13, 14, 3, 5, 18, 14, 16, 19, 19, 16, 18, 5, 3, 4, 3, 18, 2, 14, 15, 16, 18, 16, 1, 16, 0, 1, 16, 17, 0], "vertices": [1, 18, -5.02, -4.96, 1, 1, 18, -5.62, -0.78, 1, 1, 18, -5.76, 0.2, 1, 1, 18, -3.7, 6.11, 1, 1, 18, 0.06, 7.45, 1, 2, 18, 0.71, 4.54, 0.99963, 19, -6.6, 5.38, 0.00037, 2, 18, 5.23, 5.53, 0.84465, 19, -1.99, 5.81, 0.15535, 2, 18, 9.39, 5.52, 0.37096, 19, 2.14, 5.3, 0.62904, 2, 18, 11.23, 3.89, 0.09842, 19, 3.77, 3.46, 0.90158, 1, 19, 9.79, 3.68, 1, 1, 19, 10.43, 1.17, 1, 1, 19, 10.64, -2.8, 1, 2, 18, 12.02, -2.74, 0.01375, 19, 3.76, -3.21, 0.98625, 2, 18, 8.32, -5.15, 0.51913, 19, -0.2, -5.17, 0.48087, 2, 18, 4.14, -5.31, 0.96455, 19, -4.38, -4.82, 0.03545, 1, 18, 2.07, -7.46, 1, 1, 18, -0.66, -4.19, 1, 1, 18, -3.25, -5.74, 1, 1, 18, -0.59, -0.87, 1, 1, 18, 3.88, -0.84, 1, 2, 18, 7.66, -0.21, 0.91703, 19, -0.27, -0.18, 0.08297, 1, 19, 4.35, -0.12, 1], "hull": 18, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 2, 2, 4, 2, 36, 36, 38, 38, 40, 40, 42], "width": 16, "height": 25}}, "a5": {"a5": {"x": 18.07, "y": 1.04, "rotation": -90, "width": 42, "height": 52}}, "a6": {"a6": {"type": "mesh", "uvs": [0.13728, 0.36856, 0.25278, 0.17856, 0.46628, 0.02856, 0.76028, 0.01656, 1, 0.11256, 1, 0.35656, 0.91078, 0.53256, 0.76028, 0.74056, 0.58178, 0.80656, 0.51178, 0.97256, 0.28078, 0.97256, 0.01828, 0.94656, 0.12678, 0.73056, 0.01828, 0.60856, 0.62028, 0.25456, 0.50828, 0.45056, 0.40678, 0.67656, 0.35078, 0.77056, 0.32978, 0.87456], "triangles": [10, 18, 9, 9, 18, 8, 10, 11, 18, 11, 12, 18, 18, 17, 8, 18, 12, 17, 17, 16, 8, 8, 16, 7, 17, 12, 16, 7, 16, 6, 12, 13, 16, 16, 13, 15, 16, 15, 6, 13, 0, 15, 6, 15, 5, 15, 14, 5, 15, 0, 14, 0, 1, 14, 14, 4, 5, 1, 2, 14, 14, 3, 4, 14, 2, 3], "vertices": [2, 26, 7.03, -6.52, 0.58094, 27, -1.25, -6.52, 0.41906, 2, 26, 1.41, -6.14, 0.97166, 27, -6.87, -6.16, 0.02834, 1, 26, -3.54, -3.96, 1, 1, 26, -5.11, 0.48, 1, 1, 26, -3.54, 4.89, 1, 2, 26, 3.05, 6.7, 0.98502, 27, -5.27, 6.69, 0.01498, 2, 26, 8.18, 6.63, 0.63469, 27, -0.14, 6.63, 0.36531, 2, 26, 14.43, 5.86, 0.05276, 27, 6.12, 5.87, 0.94724, 2, 26, 16.97, 3.59, 0.00137, 27, 8.66, 3.62, 0.99863, 1, 27, 13.44, 3.78, 1, 1, 27, 14.43, 0.22, 1, 1, 27, 14.85, -4.02, 1, 1, 27, 8.56, -3.97, 1, 2, 26, 14.02, -6.57, 0.0176, 27, 5.74, -6.55, 0.9824, 1, 26, 1.9, 0.09, 1, 1, 26, 7.67, -0.18, 1, 1, 27, 5.91, -0.06, 1, 1, 27, 8.68, -0.21, 1, 1, 27, 11.58, 0.24, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 6, 28, 28, 30, 30, 32, 32, 34], "width": 16, "height": 28}}, "a7": {"a7": {"type": "mesh", "uvs": [0.14665, 0.48113, 0.09602, 0.27113, 0.09602, 0.08513, 0.4504, 0, 0.76427, 0.11713, 0.88577, 0.27713, 0.95327, 0.49313, 0.95665, 0.64913, 0.7474, 0.76713, 0.91615, 0.83313, 0.8824, 0.95113, 0.64615, 0.95713, 0.32552, 0.94313, 0.30527, 0.79313, 0.15677, 0.75313, 0.4909, 0.27313, 0.5044, 0.50512, 0.50102, 0.77112, 0.6259, 0.89312], "triangles": [12, 18, 11, 11, 18, 10, 10, 18, 9, 12, 17, 18, 12, 13, 17, 18, 8, 9, 18, 17, 8, 17, 13, 16, 8, 17, 16, 16, 13, 14, 8, 16, 7, 14, 0, 16, 16, 6, 7, 0, 15, 16, 16, 5, 6, 16, 15, 5, 0, 1, 15, 15, 4, 5, 15, 2, 3, 15, 3, 4, 15, 1, 2], "vertices": [2, 28, 6.94, -6.36, 0.67954, 29, -0.14, -6.44, 0.32046, 2, 28, 1.21, -6.19, 0.99841, 29, -5.83, -7.05, 0.00159, 1, 28, -3.73, -5.33, 1, 1, 28, -5.03, 0.65, 1, 1, 28, -1.06, 5.06, 1, 2, 28, 3.53, 6.24, 0.96938, 29, -5.24, 5.57, 0.03062, 2, 28, 9.46, 6.3, 0.41644, 29, 0.63, 6.45, 0.58356, 2, 28, 13.62, 5.64, 0.06991, 29, 4.84, 6.36, 0.93009, 1, 29, 7.91, 2.91, 1, 1, 29, 9.78, 5.54, 1, 1, 29, 12.95, 4.89, 1, 1, 29, 12.98, 1.11, 1, 1, 29, 12.43, -4, 1, 2, 28, 15.67, -5.3, 0.00967, 29, 8.37, -4.19, 0.99033, 2, 28, 14.2, -7.45, 0.04553, 29, 7.21, -6.53, 0.95447, 1, 28, 2.35, 0.03, 1, 2, 28, 8.56, -0.83, 0.18512, 29, 0.71, -0.74, 0.81488, 2, 28, 15.62, -2.11, 0.00017, 29, 7.88, -1.04, 0.99983, 1, 29, 11.24, 0.85, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 6, 30, 10, 12, 30, 32, 32, 34, 34, 36], "width": 16, "height": 27}}, "e3": {"e3": {"type": "mesh", "uvs": [0.43973, 0.01812, 0.56506, 0.04379, 0.63706, 0.18846, 0.81306, 0.19546, 0.86639, 0.28646, 0.89573, 0.44279, 0.90639, 0.60145, 1, 0.69479, 1, 0.89079, 0.85039, 0.94912, 0.65839, 0.99579, 0.39439, 0.97245, 0.10373, 0.94445, 0.1144, 0.72512, 0.03706, 0.55945, 0.02373, 0.40079, 0.29306, 0.25846, 0.31173, 0.06712, 0.49038, 0.23279, 0.58638, 0.44512, 0.63704, 0.70179, 0.76238, 0.45679, 0.79171, 0.63179, 0.81571, 0.79279, 0.27171, 0.50579, 0.30904, 0.69012, 0.31971, 0.82079], "triangles": [11, 20, 10, 10, 23, 9, 12, 26, 11, 9, 23, 8, 16, 17, 18, 17, 0, 18, 18, 1, 2, 18, 0, 1, 22, 19, 21, 19, 16, 18, 16, 19, 24, 21, 5, 6, 19, 2, 21, 21, 4, 5, 4, 2, 3, 4, 21, 2, 19, 18, 2, 10, 20, 23, 11, 26, 20, 12, 13, 26, 23, 7, 8, 13, 25, 26, 26, 25, 20, 20, 22, 23, 23, 6, 7, 23, 22, 6, 25, 13, 24, 25, 19, 20, 22, 20, 19, 13, 14, 24, 25, 24, 19, 22, 21, 6, 14, 15, 24, 24, 15, 16], "vertices": [1, 32, 4.86, 1.76, 1, 2, 31, 9.15, -3.38, 0.00318, 32, 4.9, -0.94, 0.99682, 2, 31, 5.38, -3.76, 0.56105, 32, 1.9, -3.25, 0.43895, 2, 31, 4.09, -7.23, 0.91274, 32, 2.63, -6.88, 0.08726, 3, 30, 9.15, -7.52, 0.00849, 31, 1.67, -7.64, 0.94993, 32, 0.78, -8.5, 0.04158, 3, 30, 5.35, -7.38, 0.20361, 31, -2.09, -7.08, 0.79465, 32, -2.71, -10, 0.00174, 2, 30, 1.57, -6.84, 0.68523, 31, -5.78, -6.14, 0.31477, 2, 30, -1.01, -8.32, 0.88023, 31, -8.52, -7.33, 0.11977, 1, 9, -8.45, 2.76, 1, 1, 9, -5.33, 4.21, 1, 1, 9, -1.31, 5.4, 1, 1, 9, 4.24, 4.92, 1, 1, 9, 10.35, 4.34, 1, 2, 30, 1.98, 10.05, 0.93394, 31, -3.55, 10.61, 0.06606, 2, 30, 6.2, 10.85, 0.76994, 31, 0.73, 10.95, 0.23006, 3, 30, 9.99, 10.37, 0.65237, 31, 4.44, 10.06, 0.34748, 32, -6.17, 8.01, 0.00015, 3, 30, 12.21, 4.14, 0.17374, 31, 5.97, 3.63, 0.48817, 32, -1.49, 3.35, 0.33809, 2, 30, 16.63, 2.85, 5e-05, 32, 3.06, 4.08, 0.99995, 2, 31, 5.3, -0.51, 0.60234, 32, 0.12, -0.52, 0.39766, 1, 31, -0.17, -0.88, 1, 2, 30, 0.34, -0.81, 0.98571, 31, -6.36, -0.02, 0.01429, 3, 30, 5.58, -4.56, 0.20386, 31, -1.56, -4.31, 0.79471, 32, -3.71, -7.37, 0.00143, 2, 30, 1.34, -4.33, 0.76013, 31, -5.75, -3.62, 0.23987, 2, 30, -2.55, -4.06, 0.95606, 31, -9.58, -2.93, 0.04394, 2, 30, 6.48, 5.77, 0.75511, 31, 0.46, 5.86, 0.24489, 2, 30, 1.99, 5.88, 0.96542, 31, -4, 6.46, 0.03458, 2, 30, -1.13, 6.29, 0.99578, 31, -7.05, 7.2, 0.00422], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 42, 44, 44, 46, 48, 50, 50, 52], "width": 21, "height": 24}}, "c3": {"c3": {"type": "mesh", "uvs": [0.29529, 0.02653, 0.61472, 0.15384, 0.90072, 0.28115, 0.91776, 0.44257, 0.93043, 0.56267, 1, 0.79398, 0.871, 0.95356, 0.65186, 0.97687, 0.39187, 0.84418, 0.31758, 0.66667, 0.29529, 0.53936, 0.07615, 0.52501, 0.0093, 0.43715, 0.0353, 0.30626, 0.25815, 0.2955, 0.21358, 0.13053, 0.39928, 0.15205, 0.559, 0.3206, 0.59985, 0.4497, 0.25071, 0.41922, 0.62957, 0.57701, 0.70014, 0.75274], "triangles": [6, 7, 21, 7, 8, 21, 6, 21, 5, 8, 9, 21, 21, 4, 5, 9, 20, 21, 21, 20, 4, 9, 10, 20, 10, 18, 20, 20, 3, 4, 20, 18, 3, 11, 19, 10, 10, 19, 18, 11, 12, 19, 19, 17, 18, 18, 17, 3, 17, 2, 3, 12, 13, 19, 19, 14, 17, 19, 13, 14, 14, 16, 17, 17, 1, 2, 17, 16, 1, 14, 15, 16, 15, 0, 16, 1, 16, 0], "vertices": [1, 65, -4.35, -0.33, 1, 1, 65, 0.26, 3.2, 1, 2, 65, 4.75, 6.27, 0.98098, 66, -4.79, 6.31, 0.01902, 2, 65, 9.37, 5.45, 0.60396, 66, -0.19, 5.45, 0.39604, 2, 65, 12.8, 4.84, 0.10436, 66, 3.24, 4.81, 0.89564, 1, 66, 9.99, 4.18, 1, 1, 66, 14.07, 1.34, 1, 1, 66, 14.01, -1.8, 1, 1, 66, 9.41, -4.43, 1, 2, 65, 13.81, -4.2, 0.01625, 66, 4.16, -4.24, 0.98375, 2, 65, 10.14, -3.67, 0.55234, 66, 0.5, -3.68, 0.44766, 2, 65, 9.05, -6.57, 0.91531, 66, -0.62, -6.56, 0.08469, 2, 65, 6.36, -6.91, 0.979, 66, -3.32, -6.88, 0.021, 1, 65, 2.74, -5.7, 1, 1, 65, 3.14, -2.59, 1, 1, 65, -1.67, -2.12, 1, 1, 65, -0.47, 0.27, 1, 1, 65, 4.79, 1.35, 1, 2, 65, 8.57, 1.07, 0.91366, 66, -1.03, 1.08, 0.08634, 2, 65, 6.61, -3.5, 0.96937, 66, -3.03, -3.47, 0.03063, 1, 66, 2.66, 0.62, 1, 1, 66, 7.85, 0.38, 1], "hull": 16, "edges": [0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 4, 6, 6, 8, 36, 40, 40, 42], "width": 14, "height": 29}}, "c7": {"c7": {"type": "mesh", "uvs": [0.19159, 0.35506, 0.32697, 0.13822, 0.54235, 0.02243, 0.81927, 0.0098, 1, 0.11296, 1, 0.27716, 0.94235, 0.50664, 0.9085, 0.70663, 0.65312, 0.87295, 0.54851, 0.974, 0.35774, 0.97821, 0.15467, 0.93821, 0.18851, 0.78242, 0.07159, 0.70663, 0.15467, 0.46453, 0.67158, 0.2098, 0.54543, 0.44558, 0.44081, 0.66032, 0.39158, 0.83715], "triangles": [10, 18, 9, 10, 11, 18, 9, 18, 8, 11, 12, 18, 18, 17, 8, 8, 17, 7, 18, 12, 17, 12, 13, 17, 17, 16, 7, 7, 16, 6, 13, 14, 17, 17, 14, 16, 16, 15, 6, 6, 15, 5, 14, 0, 16, 0, 1, 16, 16, 1, 15, 15, 4, 5, 1, 2, 15, 15, 3, 4, 15, 2, 3], "vertices": [2, 75, 4.2, -4.99, 0.99732, 76, -2.94, -5.27, 0.00268, 1, 75, -0.21, -4.23, 1, 1, 75, -3, -2.01, 1, 1, 75, -4.07, 1.44, 1, 2, 75, -2.7, 4.17, 0.99371, 76, -10.23, 3.59, 0.00629, 2, 75, 0.33, 4.9, 0.92369, 76, -7.23, 4.44, 0.07631, 2, 75, 4.75, 5.18, 0.40731, 76, -2.83, 4.91, 0.59269, 2, 75, 8.55, 5.63, 0.00936, 76, 0.94, 5.52, 0.99064, 1, 76, 4.89, 3.19, 1, 1, 76, 7.11, 2.41, 1, 1, 76, 7.86, 0.05, 1, 2, 75, 15.09, -2.89, 0.00166, 76, 7.85, -2.7, 0.99834, 2, 75, 12.11, -3.15, 0.13998, 76, 4.88, -3.09, 0.86002, 2, 75, 11.06, -4.96, 0.36729, 76, 3.91, -4.94, 0.63271, 2, 75, 6.34, -4.97, 0.92426, 76, -0.81, -5.16, 0.07574, 2, 75, 0.07, 0.45, 0.9986, 76, -7.3, -0.02, 0.0014, 1, 75, 4.81, -0.11, 1, 2, 75, 9.09, -0.49, 0.10699, 76, 1.76, -0.56, 0.89301, 2, 75, 12.51, -0.34, 0.00233, 76, 5.16, -0.26, 0.99767], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 6, 30, 30, 32, 32, 34, 34, 36], "width": 13, "height": 19}}}}], "animations": {"animation1": {"slots": {"A_rotation06": {"twoColor": [{"light": "ffc50052", "dark": "ff3b00", "curve": "stepped"}, {"time": 0.2, "light": "ffc500ff", "dark": "ff3b00"}, {"time": 0.3667, "light": "ffc5007a", "dark": "ff3b00"}], "attachment": [{"name": "A_rotation06"}]}, "nvwashi": {"attachment": [{"name": "nv<PERSON>i"}]}, "A_glow_orange_small3": {"color": [{"time": 0.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffff52"}], "attachment": [{"time": 0.1, "name": "A_glow_orange_small"}]}, "A_glow_orange_small4": {"color": [{"time": 0.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffff52"}], "attachment": [{"time": 0.1, "name": "A_glow_orange_small"}]}}, "bones": {"glow_orange2": {"scale": [{"time": 0.1}, {"time": 0.3, "x": 2.725, "y": 3.761}, {"time": 0.3667, "x": 3.402, "y": 4.449, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.137, "y": 9.261}]}, "huan2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -82.46}], "scale": [{"x": 1.001, "y": 1.001, "curve": "stepped"}, {"time": 0.3667, "x": 1.001, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "xuanwodi": {"scale": [{"x": 0.042, "y": 0.042, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 2.233, "y": 2.233}]}, "hecheng": {"rotate": [{"angle": -15.64}], "translate": [{"x": 0.38, "y": 0.72}], "scale": [{"x": 0.307, "y": 0.173}]}, "nws1": {"translate": [{"x": 444.74, "y": -334.82}], "scale": [{"x": 0.101, "y": 0.101, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "glow_orange3": {"scale": [{"time": 0.1}, {"time": 0.3, "x": 2.725, "y": 3.761}, {"time": 0.3667, "x": 3.402, "y": 4.449, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.137, "y": 9.261}]}}}, "animation2": {"slots": {"A_rotation06": {"attachment": [{"name": "A_rotation06"}]}, "nvwashi": {"attachment": [{"name": "nv<PERSON>i"}]}, "A_glow_orange_small3": {"color": [{"color": "ffffff52"}], "attachment": [{"name": "A_glow_orange_small"}]}, "A_glow_orange_small4": {"color": [{"color": "ffffff52"}], "attachment": [{"name": "A_glow_orange_small"}]}}, "bones": {"hecheng": {"rotate": [{"angle": -15.64}], "translate": [{"x": 0.38, "y": 0.72}], "scale": [{"x": 0.307, "y": 0.173}]}, "xuanwodi": {"scale": [{"x": 2.233, "y": 2.233}]}, "huan2": {"rotate": [{"angle": -82.46}, {"time": 0.8, "angle": -143.08}, {"time": 2.7, "angle": 96.86}, {"time": 4.9667, "angle": -82.46}]}, "glow_orange2": {"scale": [{"x": 3.402, "y": 4.449}]}, "nws1": {"translate": [{"x": 444.74, "y": -334.82}]}, "glow_orange3": {"scale": [{"x": 3.402, "y": 4.449}]}}}, "animation3": {"slots": {"A_rotation06": {"attachment": [{"name": "A_rotation06"}]}, "nvwashi": {"attachment": [{"name": "nv<PERSON>i"}]}, "A_glow_orange_small3": {"color": [{"color": "ffffff52"}], "attachment": [{"name": "A_glow_orange_small"}]}, "A_glow_orange_small4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffff00"}], "attachment": [{"name": "A_glow_orange_small"}]}}, "bones": {"hecheng": {"rotate": [{"angle": -15.64}], "translate": [{"x": 0.38, "y": 0.72}], "scale": [{"x": 0.307, "y": 0.173}]}, "xuanwodi": {"scale": [{"x": 2.233, "y": 2.233}]}, "huan2": {"rotate": [{"angle": -82.46}, {"time": 0.8, "angle": -143.08}, {"time": 2.7, "angle": 96.86}, {"time": 4.9667, "angle": -82.46}]}, "glow_orange2": {"scale": [{"x": 3.402, "y": 4.449}]}, "nws1": {"translate": [{"x": 444.74, "y": -334.82}]}, "glow_orange3": {"rotate": [{"angle": 122.67}], "scale": [{"x": 3.402, "y": 4.449, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 9.486, "y": 12.407}]}}}, "ren1_1": {"slots": {"a3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "a5": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffffff"}]}, "a1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "a7": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "a4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "a2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "a6": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}}, "bones": {"p1": {"translate": [{"x": 446.12, "y": -54.8, "curve": 0.25, "c3": 0.386, "c4": 1.33}, {"time": 0.4, "x": 446.12, "y": -256.68}]}, "p11": {"rotate": [{"angle": -51.81, "curve": "stepped"}, {"time": 0.1, "angle": -51.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -18.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "p9": {"rotate": [{"angle": 49.81, "curve": "stepped"}, {"time": 0.1, "angle": 49.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "p12": {"rotate": [{"time": 0.0667, "angle": -51.81, "curve": "stepped"}, {"time": 0.1667, "angle": -51.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -18.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "p10": {"rotate": [{"time": 0.0667, "angle": 49.81, "curve": "stepped"}, {"time": 0.1667, "angle": 49.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "p15": {"rotate": [{"time": 0.0667, "angle": -31.39, "curve": "stepped"}, {"time": 0.1333, "angle": -31.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -10.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "p17": {"rotate": [{"time": 0.0667, "angle": 63.81, "curve": "stepped"}, {"time": 0.1333, "angle": 63.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "p16": {"rotate": [{"time": 0.1333, "angle": -31.39, "curve": "stepped"}, {"time": 0.2, "angle": -31.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p18": {"rotate": [{"time": 0.1333, "angle": 63.81, "curve": "stepped"}, {"time": 0.2, "angle": 63.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}}}, "ren1_2": {"slots": {"a3": {"color": [{"color": "ffffffff"}]}, "a5": {"color": [{"color": "ffffffff"}]}, "a1": {"color": [{"color": "ffffffff"}]}, "a7": {"color": [{"color": "ffffffff"}]}, "a4": {"color": [{"color": "ffffffff"}]}, "a2": {"color": [{"color": "ffffffff"}]}, "a6": {"color": [{"color": "ffffffff"}]}}, "bones": {"p1": {"translate": [{"x": 446.12, "y": -256.68, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 446.12, "y": -254.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 446.12, "y": -256.68, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 446.12, "y": -254.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 446.12, "y": -256.68}]}, "p6": {"rotate": [{}, {"time": 0.1667, "angle": 3.72}, {"time": 0.5, "angle": -3.29}, {"time": 0.6667}]}, "p19": {"rotate": [{}, {"time": 0.1667, "angle": -6.91}, {"time": 0.3333, "angle": 27.92}, {"time": 0.5, "angle": 52.83}, {"time": 0.6667}]}, "p20": {"rotate": [{}, {"time": 0.1667, "angle": -2.36}, {"time": 0.3333, "angle": -3.31}, {"time": 0.5, "angle": -0.8}, {"time": 0.6667}]}, "p21": {"rotate": [{}, {"time": 0.1667, "angle": 9.31}, {"time": 0.3333, "angle": -32.8}, {"time": 0.5, "angle": -49.71}, {"time": 0.6667}]}, "p22": {"rotate": [{}, {"time": 0.1667, "angle": 6.91}, {"time": 0.3333, "angle": 5.65}, {"time": 0.5, "angle": 6.43}, {"time": 0.6667}]}, "p7": {"rotate": [{"angle": -0.37}, {"time": 0.0667, "angle": 0.95}, {"time": 0.2333, "angle": 4.67}, {"time": 0.5667, "angle": -2.34}, {"time": 0.6667, "angle": -0.37}]}, "p9": {"rotate": [{}, {"time": 0.1667, "angle": -95.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.41, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p10": {"rotate": [{}, {"time": 0.1667, "angle": 69.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 14.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p11": {"rotate": [{}, {"time": 0.1667, "angle": 46.35, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -19.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p12": {"rotate": [{}, {"time": 0.1667, "angle": 46.35, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p16": {"rotate": [{"angle": -7.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -11.16, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -7.05}]}, "p17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p18": {"rotate": [{"angle": -7.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -11.16, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -7.05}]}, "p8": {"rotate": [{}, {"time": 0.3333, "angle": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}}}, "ren2_1": {"slots": {"e7": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "e5": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "e1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "e4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "e2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "e6": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "e3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}}, "bones": {"p2": {"translate": [{"y": 343.18, "curve": 0.25, "c3": 0.386, "c4": 1.33}, {"time": 0.4, "x": -3.16, "y": 139.71}]}, "p26": {"rotate": [{"angle": -60.97, "curve": "stepped"}, {"time": 0.2, "angle": -60.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -21.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p27": {"rotate": [{"angle": -60.97, "curve": "stepped"}, {"time": 0.2667, "angle": -60.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -7.85, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "p28": {"rotate": [{"angle": 64.03, "curve": "stepped"}, {"time": 0.2, "angle": 64.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 24.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p29": {"rotate": [{"angle": 64.03, "curve": "stepped"}, {"time": 0.2667, "angle": 64.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "p30": {"rotate": [{"angle": -31.16, "curve": "stepped"}, {"time": 0.2, "angle": -31.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -4.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p31": {"rotate": [{"angle": -31.16, "curve": "stepped"}, {"time": 0.2667, "angle": -31.16, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -4.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "p34": {"rotate": [{"angle": 44.48, "curve": "stepped"}, {"time": 0.2, "angle": 44.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.73, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p35": {"rotate": [{"angle": 44.48, "curve": "stepped"}, {"time": 0.2667, "angle": 44.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 5.73, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "p39": {"shear": [{"time": 0.7667}]}}}, "ren2_2": {"slots": {"e7": {"color": [{"color": "ffffffff"}]}, "e5": {"color": [{"color": "ffffffff"}]}, "e1": {"color": [{"color": "ffffffff"}]}, "e4": {"color": [{"color": "ffffffff"}]}, "e2": {"color": [{"color": "ffffffff"}]}, "e6": {"color": [{"color": "ffffffff"}]}, "e3": {"color": [{"color": "ffffffff"}]}}, "bones": {"p35": {"rotate": [{"angle": -3.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.15}]}, "p36": {"rotate": [{}, {"time": 0.1667, "angle": -11.66}, {"time": 0.5, "angle": 38.56}, {"time": 0.6667}]}, "p37": {"rotate": [{}, {"time": 0.1667, "angle": -11.66}, {"time": 0.5, "angle": 38.56}, {"time": 0.6667}]}, "p38": {"rotate": [{}, {"time": 0.1667, "angle": 16.93}, {"time": 0.5, "angle": -26.48}, {"time": 0.6667}]}, "p2": {"translate": [{"x": -3.16, "y": 139.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": -3.16, "y": 140.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -3.16, "y": 139.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "x": -3.16, "y": 140.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "x": -3.16, "y": 139.71}]}, "p23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p24": {"rotate": [{"angle": 0.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.01, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 0.26}]}, "p25": {"rotate": [{"angle": 0.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.01, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 0.74}]}, "p26": {"rotate": [{}, {"time": 0.1667, "angle": 24.42}, {"time": 0.3333}, {"time": 0.5, "angle": -22.37}, {"time": 0.6667}]}, "p27": {"rotate": [{"angle": -8.95}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 24.42}, {"time": 0.4}, {"time": 0.5667, "angle": -22.37}, {"time": 0.6667, "angle": -8.95}]}, "p28": {"rotate": [{}, {"time": 0.1667, "angle": -36.4}, {"time": 0.5, "angle": 23.05}, {"time": 0.6667}]}, "p29": {"rotate": [{"angle": 9.22}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -36.4}, {"time": 0.5667, "angle": 23.05}, {"time": 0.6667, "angle": 9.22}]}, "p30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p31": {"rotate": [{"angle": -3.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.15}]}, "p32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p33": {"rotate": [{"angle": -3.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.15}]}, "p34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p39": {"rotate": [{}, {"time": 0.1667, "angle": 16.93}, {"time": 0.5, "angle": -26.48}, {"time": 0.6667}]}}}, "ren3_1": {"slots": {"d2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "d1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "d3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "d4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "d5": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "d6": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}}, "bones": {"p3": {"translate": [{"x": -174.47, "y": 344.1, "curve": 0.25, "c3": 0.386, "c4": 1.33}, {"time": 0.4, "x": -174.47, "y": 140.14}]}, "p47": {"rotate": [{"angle": -35.07, "curve": "stepped"}, {"time": 0.2, "angle": -35.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.5333, "angle": -20.11, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.6333}]}, "p48": {"rotate": [{"angle": -35.07, "curve": "stepped"}, {"time": 0.2667, "angle": -35.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.6, "angle": -20.11, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.7}]}, "p43": {"rotate": [{"angle": -35.07, "curve": "stepped"}, {"time": 0.2667, "angle": -35.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.6, "angle": -20.11, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.7}]}, "p44": {"rotate": [{"angle": -35.07, "curve": "stepped"}, {"time": 0.3333, "angle": -35.07, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.6667, "angle": -20.11, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.7667}]}, "p45": {"rotate": [{"angle": 37.67, "curve": "stepped"}, {"time": 0.2667, "angle": 37.67, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.6, "angle": 21.6, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.7}]}, "p46": {"rotate": [{"angle": 37.67, "curve": "stepped"}, {"time": 0.3333, "angle": 37.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.6667, "angle": 21.6, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.7667}]}, "p49": {"rotate": [{"angle": 53.03, "curve": "stepped"}, {"time": 0.2, "angle": 53.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.5333, "angle": 30.41, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.6333}]}, "p50": {"rotate": [{"angle": -71.73, "curve": "stepped"}, {"time": 0.2667, "angle": -71.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.6, "angle": -41.13, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.7}]}}}, "ren3_2": {"slots": {"d2": {"color": [{"color": "ffffffff"}]}, "d1": {"color": [{"color": "ffffffff"}]}, "d3": {"color": [{"color": "ffffffff"}]}, "d4": {"color": [{"color": "ffffffff"}]}, "d5": {"color": [{"color": "ffffffff"}]}, "d6": {"color": [{"color": "ffffffff"}]}}, "bones": {"p3": {"translate": [{"x": -174.47, "y": 140.14, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -174.47, "y": 141.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -174.47, "y": 140.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -174.47, "y": 141.73, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -174.47, "y": 140.14}]}, "p46": {"rotate": [{"angle": -3.61, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.4667, "angle": -9.82, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.61}]}, "p47": {"rotate": [{}, {"time": 0.1667, "angle": 31.22}, {"time": 0.5, "angle": -15.85}, {"time": 0.6667}]}, "p48": {"rotate": [{"angle": -6.34}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 31.22}, {"time": 0.5667, "angle": -15.85}, {"time": 0.6667, "angle": -6.34}]}, "p49": {"rotate": [{}, {"time": 0.1667, "angle": -24.27}, {"time": 0.5, "angle": 20.19}, {"time": 0.6667}]}, "p50": {"rotate": [{"angle": -8.51}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -24.27}, {"time": 0.5667, "angle": -21.27}, {"time": 0.6667, "angle": -8.51}]}, "p51": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 39.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p53": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 29.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -38.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p42": {"rotate": [{"angle": 2.43, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 2.43}]}, "p43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p44": {"rotate": [{"angle": -3.61, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.4667, "angle": -9.82, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.61}]}, "p45": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.73, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p41": {"rotate": [{"angle": 0.93, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 3.84, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 0.93}]}}}, "ren4_1": {"slots": {"c3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "c1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "c4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "c6": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "c7": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "c2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "c5": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}}, "bones": {"p4": {"translate": [{"x": -234.11, "y": 344.74, "curve": 0.25, "c3": 0.386, "c4": 1.33}, {"time": 0.4, "x": -234.11, "y": 140.19}]}, "p58": {"rotate": [{"angle": 59.87, "curve": "stepped"}, {"time": 0.1667, "angle": 59.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 29.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "p59": {"rotate": [{"angle": 59.87, "curve": "stepped"}, {"time": 0.2333, "angle": 59.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 29.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667}]}, "p60": {"rotate": [{"angle": -44.04, "curve": "stepped"}, {"time": 0.1667, "angle": -44.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -22.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "p61": {"rotate": [{"angle": -44.04, "curve": "stepped"}, {"time": 0.2333, "angle": -44.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -22.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667}]}, "p62": {"rotate": [{"angle": -20.55, "curve": "stepped"}, {"time": 0.1667, "angle": -20.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -10.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "p63": {"rotate": [{"angle": -20.55, "curve": "stepped"}, {"time": 0.2333, "angle": -20.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -10.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667}]}, "p66": {"rotate": [{"angle": 49.07, "curve": "stepped"}, {"time": 0.1667, "angle": 49.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 24.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "p67": {"rotate": [{"angle": 49.07, "curve": "stepped"}, {"time": 0.2333, "angle": 49.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 24.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667}]}}}, "ren4_2": {"slots": {"c3": {"color": [{"color": "ffffffff"}]}, "c1": {"color": [{"color": "ffffffff"}]}, "c4": {"color": [{"color": "ffffffff"}]}, "c6": {"color": [{"color": "ffffffff"}]}, "c7": {"color": [{"color": "ffffffff"}]}, "c2": {"color": [{"color": "ffffffff"}]}, "c5": {"color": [{"color": "ffffffff"}]}}, "bones": {"p57": {"rotate": [{"angle": 1.7, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.69, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 1.7}]}, "p55": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p63": {"rotate": [{"angle": -2.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -8.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -2.08}]}, "p59": {"rotate": [{"angle": 8.18}, {"time": 0.0667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -23.59}, {"time": 0.5667, "angle": 20.45}, {"time": 0.6667, "angle": 8.18}]}, "p68": {"rotate": [{}, {"time": 0.1667, "angle": -29.59}, {"time": 0.5, "angle": 55.28}, {"time": 0.6667}]}, "p69": {"rotate": [{}, {"time": 0.1667, "angle": -11.63}, {"time": 0.5, "angle": -6.28}, {"time": 0.6667}]}, "p70": {"rotate": [{}, {"time": 0.1667, "angle": 25.56}, {"time": 0.5, "angle": -25.4}, {"time": 0.6667}]}, "p4": {"translate": [{"x": -234.11, "y": 140.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -234.11, "y": 140.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -234.11, "y": 140.19, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -234.11, "y": 140.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -234.11, "y": 140.19}]}, "p56": {"rotate": [{"angle": 0.65, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.69, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 0.65}]}, "p58": {"rotate": [{}, {"time": 0.1667, "angle": -23.59}, {"time": 0.5, "angle": 20.45}, {"time": 0.6667}]}, "p60": {"rotate": [{}, {"time": 0.1667, "angle": 27.37}, {"time": 0.5, "angle": -14.75}, {"time": 0.6667}]}, "p61": {"rotate": [{"angle": -3.81}, {"time": 0.0667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 27.37}, {"time": 0.5667, "angle": -9.51}, {"time": 0.6667, "angle": -3.81}]}, "p62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p64": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p65": {"rotate": [{"angle": -2.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -8.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -2.08}]}, "p66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p67": {"rotate": [{"angle": -2.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -8.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -2.08}]}, "p71": {"rotate": [{}, {"time": 0.1667, "angle": 11.8}, {"time": 0.5, "angle": -22.65}, {"time": 0.6667}]}}}, "ren5_1": {"slots": {"b8": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "b5": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "b7": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "b4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "b1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "b6": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "b3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "b2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}}, "bones": {"p5": {"translate": [{"x": -317.69, "y": 350.03, "curve": 0.25, "c3": 0.386, "c4": 1.33}, {"time": 0.4, "x": -317.69, "y": 141.62}]}, "p75": {"rotate": [{"angle": -24.78, "curve": "stepped"}, {"time": 0.1667, "angle": -24.78}, {"time": 0.3667}, {"time": 0.5333, "angle": -12.39}, {"time": 0.7}]}, "p76": {"rotate": [{"angle": -24.78, "curve": "stepped"}, {"time": 0.2333, "angle": -24.78}, {"time": 0.4333}, {"time": 0.6, "angle": -12.39}, {"time": 0.7667}]}, "p85": {"rotate": [{"angle": -51.77, "curve": "stepped"}, {"time": 0.1333, "angle": -51.77}, {"time": 0.3333}, {"time": 0.5, "angle": -25.89}, {"time": 0.6667}]}, "p86": {"rotate": [{"angle": -51.77, "curve": "stepped"}, {"time": 0.1667, "angle": -51.77}, {"time": 0.3667}, {"time": 0.5333, "angle": -25.89}, {"time": 0.7}]}, "p87": {"rotate": [{"angle": 40.05, "curve": "stepped"}, {"time": 0.1333, "angle": 40.05}, {"time": 0.3333}, {"time": 0.5, "angle": 20.03}, {"time": 0.6667}]}, "p88": {"rotate": [{"angle": -61.5, "curve": "stepped"}, {"time": 0.1667, "angle": -61.5}, {"time": 0.3667}, {"time": 0.5333, "angle": -30.75}, {"time": 0.7}]}, "p77": {"rotate": [{"angle": 33.27, "curve": "stepped"}, {"time": 0.1667, "angle": 33.27}, {"time": 0.3667}, {"time": 0.5333, "angle": 16.64}, {"time": 0.7}]}, "p78": {"rotate": [{"angle": 33.27, "curve": "stepped"}, {"time": 0.2333, "angle": 33.27}, {"time": 0.4333}, {"time": 0.6, "angle": 16.64}, {"time": 0.7667}]}}}, "ren5_2": {"slots": {"b8": {"color": [{"color": "ffffffff"}]}, "b5": {"color": [{"color": "ffffffff"}]}, "b7": {"color": [{"color": "ffffffff"}]}, "b2": {"color": [{"color": "ffffffff"}]}, "b6": {"color": [{"color": "ffffffff"}]}, "b3": {"color": [{"color": "ffffffff"}]}, "b1": {"color": [{"color": "ffffffff"}]}, "b4": {"color": [{"color": "ffffffff"}]}}, "bones": {"p74": {"rotate": [{"angle": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 1.59}]}, "p72": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p88": {"rotate": [{"angle": 3.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.68}, {"time": 0.6, "angle": 6.17}, {"time": 0.6667, "angle": 3.7}]}, "p89": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p90": {"rotate": [{"angle": -1.17, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.84, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -1.17}]}, "p5": {"translate": [{"x": -317.69, "y": 141.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": -317.69, "y": 142.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -317.69, "y": 141.62, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -317.69, "y": 142.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "x": -317.69, "y": 141.62}]}, "p73": {"rotate": [{"angle": 0.61, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 0.61}]}, "p75": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p76": {"rotate": [{"angle": -1.44, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -5.96, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -1.44}]}, "p77": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p78": {"rotate": [{"angle": -1.44, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -5.96, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -1.44}]}, "p79": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "p80": {"rotate": [{"angle": -1.44, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.96, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -1.44}]}, "p81": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -22.57, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.5, "angle": 53.56, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 0.6667}]}, "p82": {"rotate": [{}, {"time": 0.1667, "angle": -6.6}, {"time": 0.5, "angle": 6.33}, {"time": 0.6667}]}, "p83": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 20.15, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.5, "angle": -48.68, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 0.6667}]}, "p84": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 10.5, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.5, "angle": -3.1, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 0.6667}]}, "p85": {"rotate": [{}, {"time": 0.1667, "angle": 15.31}, {"time": 0.5, "angle": -11.51}, {"time": 0.6667}]}, "p86": {"rotate": [{"angle": -6.91}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 15.31}, {"time": 0.6, "angle": -11.51}, {"time": 0.6667, "angle": -6.91}]}, "p87": {"rotate": [{}, {"time": 0.1667, "angle": -13.68}, {"time": 0.5, "angle": 6.17}, {"time": 0.6667}]}, "p91": {"rotate": [{"angle": -3.06, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -3.06}]}}}}}