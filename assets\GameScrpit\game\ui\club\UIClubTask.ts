import { _decorator, Label, Sprite } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ClubTaskAdapter } from "./adapter/ClubTaskViewHolder";
import { ClubModule } from "../../../module/club/ClubModule";
import { divide } from "../../../lib/utils/NumbersUtils";
import MsgMgr from "../../../lib/event/MsgMgr";
import { ClubEvent } from "../../../module/club/ClubConstant";
import { ClubRewardMessage } from "../../net/protocol/Club";
import MsgEnum from "../../event/MsgEnum";
import TipMgr from "../../../lib/tips/TipMgr";
import { UIClubTips } from "../../common/UIClubTips";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { ClubAudioName } from "../../../module/club/ClubConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Mon Aug 19 2024 17:24:25 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubTask.ts
 *
 */

@ccclass("UIClubTask")
export class UIClubTask extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubTask`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  _adapter: ClubTaskAdapter;
  protected onEvtShow(): void {
    super.onEvtShow();
    this._adapter = new ClubTaskAdapter(this.getNode("task_viewholder"));
    this.getNode("task_list").getComponent(AdapterView).setAdapter(this._adapter);
    let taskList = ClubModule.config.getTaskList();
    this._adapter.setData(taskList);
    this.refreshTaskUI();
    MsgMgr.on(ClubEvent.CLUB_ACTIVITE_CHANGE, this.onTaskStateChange, this);
  }
  protected onEvtHide(): void {
    MsgMgr.off(ClubEvent.CLUB_ACTIVITE_CHANGE, this.onTaskStateChange, this);
  }
  private onTaskStateChange() {
    this.refreshTaskUI();
    let taskList = ClubModule.config.getTaskList();
    this._adapter.setData(taskList);
  }
  private refreshTaskUI() {
    let process = divide(ClubModule.data.clubFormMessage.active.activeVal, ClubModule.config.getMaxActive());
    this.getNode("task_process").getComponent(Sprite).fillRange = process;
    this.getNode("txt_active").getComponent(Label).string = `${ClubModule.data.clubFormMessage.active.activeVal}`;
    log.log(`---${ClubModule.config.getActiveByLevel(0)}---`);
    for (let i = 0; i < 5; i++) {
      this.getNode(`p_${i + 1}`)
        .getChildByName("label")
        .getComponent(Label).string = `${ClubModule.config.getActiveByLevel(i)}`;
      if (ClubModule.data.clubFormMessage.active.activeVal >= ClubModule.config.getActiveByLevel(i)) {
        this.getNode(`p_${i + 1}`).getChildByName("select").active = true;
        this.getNode(`p_${i + 1}`).getChildByName("light").active = true;
        if (this.checkIsTake(i)) {
          this.getNode(`p_${i + 1}`).getChildByName("open").active = true;
          this.getNode(`p_${i + 1}`).getChildByName("close").active = false;
          this.getNode(`p_${i + 1}`).getChildByName("light").active = false;
        }
      } else {
        this.getNode(`p_${i + 1}`).getChildByName("select").active = false;
      }
    }
  }
  private checkIsTake(index: number): boolean {
    let activeTask = ClubModule.data.clubFormMessage.active.activeTaskList;
    for (let i = 0; i < activeTask.length; i++) {
      if (activeTask[i] == index) {
        return true;
      }
    }
    return false;
  }
  private show_tips(index: number, level: number) {
    let wolrd = this.getNode(`p_${index}`).getWorldPosition();
    let args = {
      worldx: wolrd.x,
      worldy: wolrd.y - 50,
      itemList: ClubModule.config.getActiveRewardListByLevel(level),
      // keepMask: true,
    };
    this.getNode("UIClubTips").active = true;
    this.getNode("UIClubTips").getComponent(UIClubTips).setTips(args);
  }
  private on_click_p_1() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟任务点击宝箱领取奖励);
    if (this.getNode("p_1").getChildByName("open").active) {
      TipMgr.showTip("已领取");
      return;
    }
    if (!this.getNode("p_1").getChildByName("select").active) {
      this.show_tips(1, 0);
      return;
    }
    ClubModule.api.takeActiveReward(0, (data: ClubRewardMessage) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
    });
  }
  private on_click_p_2() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟任务点击宝箱领取奖励);
    if (this.getNode("p_2").getChildByName("open").active) {
      TipMgr.showTip("已领取");
      return;
    }
    if (!this.getNode("p_2").getChildByName("select").active) {
      this.show_tips(2, 1);
      return;
    }
    ClubModule.api.takeActiveReward(1, (data: ClubRewardMessage) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
    });
  }
  private on_click_p_3() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟任务点击宝箱领取奖励);
    if (this.getNode("p_3").getChildByName("open").active) {
      TipMgr.showTip("已领取");
      return;
    }
    if (!this.getNode("p_3").getChildByName("select").active) {
      this.show_tips(3, 2);
      return;
    }
    ClubModule.api.takeActiveReward(2, (data: ClubRewardMessage) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
    });
  }
  private on_click_p_4() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟任务点击宝箱领取奖励);
    if (this.getNode("p_4").getChildByName("open").active) {
      TipMgr.showTip("已领取");
      return;
    }
    if (!this.getNode("p_4").getChildByName("select").active) {
      this.show_tips(4, 3);
      return;
    }
    ClubModule.api.takeActiveReward(3, (data: ClubRewardMessage) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
    });
  }
  private on_click_p_5() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟任务点击宝箱领取奖励);
    if (this.getNode("p_5").getChildByName("open").active) {
      TipMgr.showTip("已领取");
      return;
    }
    if (!this.getNode("p_5").getChildByName("select").active) {
      this.show_tips(5, 4);
      return;
    }
    ClubModule.api.takeActiveReward(4, (data: ClubRewardMessage) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
    });
  }
}
