import { _decorator, instantiate, Label, Node, Sprite } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { RichText } from "cc";
import { LangMgr } from "../../../mgr/LangMgr";

const { ccclass, property } = _decorator;
@ccclass("FarmCollectItemViewHolder")
export class FarmCollectItemViewHolder extends ViewHolder {
  @property(RichText)
  private award: RichText;

  public init() {
    //item 创建
  }

  public updateData(position: number, data: any) {
    if (data.totalColCnt >= data.unlock) {
      this.award.string = LangMgr.txMsgCode(455, ["#00af04", data.totalColCnt, data.unlock, data.id]);
      // `总采集数量达到<color=#00af04>${data.totalColCnt}/${data.unlock}</color>解锁第${data.id}只猴子`;
    } else {
      this.award.string = LangMgr.txMsgCode(455, ["#e10000", data.totalColCnt, data.unlock, data.id]);
      // this.award.string = `总采集数量达到<color=#e10000>${data.totalColCnt}/${data.unlock}</color>解锁第${data.id}只猴子`;
    }
  }
}
