import { IAttr } from "../../game/GameDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { HorseMessage, HorseUpResponse } from "../../game/net/protocol/Horse";
import MsgMgr from "../../lib/event/MsgMgr";

import MsgEnum from "../../game/event/MsgEnum";
import { addAttrMap, AddAttrMap, InitAttrMap } from "../../lib/utils/AttrTool";
import { IConfigHorse, IConfigHorseLv } from "../../game/JsonDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HorseData {
  private _isOpen: boolean = false;

  /**坐骑信息 */
  private _horse: HorseMessage = null;

  public initHorseData(res: HorseMessage) {
    this._horse = res;
    this._isOpen = true;
  }

  /** =============================== 本地数据 ====================================== */
  /** 获取坐骑信息 */
  public get horseMessage() {
    return this._horse;
  }

  /** 设置坐骑信息 */
  public setHorseMessage(horse: HorseMessage) {
    log.log("坐骑详情  ===> ", horse);
    this._horse = horse;
  }

  /** ================================= 事件处理 ==================================== */
  /**升级或升阶的数据处理 */
  public setHorseUpResponse(data: HorseUpResponse) {
    this._horse.grade = data.grade;
    this._horse.stage = data.stage;
    this._horse.expPoint = data.expPoint;
    MsgMgr.emit(MsgEnum.ON_HORSE_UPDATE);
  }

  /**切换坐骑的数据处理 */
  public setToggleHorseRes(horseId: number) {
    this._horse.horseId = horseId;
    MsgMgr.emit(MsgEnum.ON_HORSE_UPDATE);
  }

  /**解锁坐骑的数据处理 */
  public setUnLockHorseRes(unLockHorseList: number[]) {
    this._horse.unLockHorseList = unLockHorseList;
    MsgMgr.emit(MsgEnum.ON_HORSE_UPDATE);
  }

  // 获取总等级
  public getGradeAll() {
    let gradeAll = 0;
    if (this._horse.stage > 0) {
      gradeAll = this.getConfigHorseLv(this._horse.stage - 1).lvNeed;
    }
    return gradeAll + this._horse.grade;
  }

  // 总经验
  public getExpAll() {
    let exp = 0;
    for (let stageIdx = 0; stageIdx < this._horse.stage; stageIdx++) {
      let configHorseLv = this.getConfigHorseLv(stageIdx);
      exp += configHorseLv.lvNeed * configHorseLv.expNeed;
    }
    let configHorseLv = this.getConfigHorseLv(this._horse.stage);

    return exp + configHorseLv.expNeed * this._horse.grade + this._horse.expPoint;
  }

  /** ==================================== 属性 和 繁荣度 ================================== */

  /**获取基础属性 生命... */
  public getBaseAttr(isNext: boolean = false) {
    let rs = InitAttrMap();

    // 当前坐骑阶级的配置
    const configHorseLv = this.getConfigHorseLv(this._horse.stage);

    // 加入初始属性
    AddAttrMap(rs, configHorseLv.firstList);

    // 总等级
    let gradeAll = this.getGradeAll();

    // 总经验数
    let expAll = this.getExpAll();

    // 每点经验增加
    for (let idx in configHorseLv.expAddList) {
      let attrItem = configHorseLv.expAddList[idx];
      rs[attrItem[0]] += attrItem[1] * expAll;
    }

    // 阶级增加
    for (let idx in configHorseLv.breakAddList) {
      let attrItem = configHorseLv.breakAddList[idx];
      rs[attrItem[0]] = (rs[attrItem[0]] || 0) + attrItem[1] * this._horse.stage;
    }

    // 下级增加
    if (isNext) {
      if (configHorseLv.lvNeed <= gradeAll) {
        AddAttrMap(rs, configHorseLv.expAddList);
      } else {
        AddAttrMap(rs, configHorseLv.breakAddList);
      }
    }
    return rs;
  }

  /**获取战斗抗性  */
  public getResistAttr(horseId: number, isNext = false) {
    // 基本
    let resistAttr = InitAttrMap();

    let configHorse: IConfigHorse = JsonMgr.instance.jsonList.c_horse[horseId];
    for (let idx in configHorse.firstList) {
      let attrItem = configHorse.firstList[idx];
      resistAttr[attrItem[0]] = attrItem[1] / 10000;
    }

    let stage = this._horse.stage;
    if (isNext) {
      stage++;
    }
    for (let idx in configHorse.addList) {
      let attrItem = configHorse.addList[idx];
      resistAttr[attrItem[0]] = (resistAttr[attrItem[0]] || 0) + (attrItem[1] * stage) / 10000;
    }

    return resistAttr;
  }

  public getHorseAttrMap() {
    let attr = new IAttr();
    if (!this._isOpen) {
      return attr;
    }
    let baseAttr = this.getBaseAttr();
    let resistAttr = this.getResistAttr(this._horse.horseId);

    return addAttrMap(baseAttr, resistAttr);
  }

  public getConfigHorseLv(stage: number): IConfigHorseLv {
    return JsonMgr.instance.jsonList.c_horseLv[stage];
  }

  public getConfigHorse(id: number): IConfigHorse {
    return JsonMgr.instance.jsonList.c_horse[id];
  }
}
