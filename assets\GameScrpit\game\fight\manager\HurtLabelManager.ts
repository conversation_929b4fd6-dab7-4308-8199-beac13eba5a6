import { Animation, Label, Layers, Layout, Node, Size, UITransform, _decorator, instantiate, isValid } from "cc";
import ManagerSection from "./ManagerSection";
import GameObject from "../../../lib/object/GameObject";
import FightManager, { scaleList } from "./FightManager";
import ResMgr from "../../../lib/common/ResMgr";
import { HurtDetail } from "../FightDefine";
import { GORole } from "../role/GORole";
import { PlayerModule } from "../../../module/player/PlayerModule";
import Formate from "../../../lib/utils/Formate";
const { ccclass, property } = _decorator;

@ccclass("HurtLabelManager")
export class HurtLabelManager extends ManagerSection {
  public static sectionName(): string {
    return "HurtLabelManager";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.createRoot("HurtLabelRoot", FightManager.instance);
    this.root.getComponent(UITransform).setContentSize(FightManager.instance.getComponent(UITransform).contentSize);
    this.ready();
  }

  public onStart(): void {
    super.onStart();
  }

  public callHurt(detail: HurtDetail, path: string) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    ResMgr.loadPrefab(path, (prefab) => {
      if (isValid(this.root) == false) {
        return;
      }
      let node: Node = instantiate(prefab);
      // node.layer = Layers.Enum["UILayer"];
      node.walk((child) => (child.layer = this.root.layer));
      this.setLabHurt(node, detail.isCrit, detail.hurt);
      this.setParent(node, detail.hurtRole, detail.isCrit);

      let animation = node.getComponent(Animation);
      let defaultClip = animation.defaultClip;
      defaultClip.speed = scaleList[FightManager.instance.speed];

      // 监听动画结束事件
      animation.on(
        Animation.EventType.FINISHED,
        function () {
          node.removeFromParent();
          node.destroy();
        },
        this
      );
      if (detail.isCrit == true) {
        animation.play("hurt_lab_crit");
      } else if (detail.isCrit == false) {
        animation.play("hurt_lab_common");
      }
    });
  }

  private setLabHurt(node: Node, isCrit: boolean, hurt: number) {
    if (isCrit == true) {
      node.getChildByName("lay").getChildByName("lab").getComponent(Label).string = `${Formate.format(hurt)}`;
      node.getChildByName("lay").getChildByName("lab").getComponent(Label).updateRenderData(true);
      node.getChildByName("lay").getComponent(Layout).updateLayout(true);
      let width = (node.getChildByName("lay").getChildByName("lab").getComponent(Label).string.length + 1.5) * 56;
      node.getComponent(UITransform).setContentSize(new Size(width, 50));
    } else {
      node.getChildByName("lab").getComponent(Label).string = `-${Formate.format(hurt)}`;
      node.getChildByName("lab").getComponent(Label).updateRenderData(true);
    }

    node.getComponent(Layout).updateLayout(true);
  }

  private setParent(node: Node, gorole: GORole, isCrit: boolean) {
    this.root.addChild(node);
    if (isCrit == true) {
      gorole.getCritPos(node);
    } else {
      gorole.getHurtPos(node);
    }
  }
}
