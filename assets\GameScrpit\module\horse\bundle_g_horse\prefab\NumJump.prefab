[{"__type__": "cc.Prefab", "_name": "NumJump", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "NumJump", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}], "_prefab": {"__id__": 16}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lblNum", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 83.88993835449219, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98LZIhu9JEiKnCFk/phKH2"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "x2200", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 10, "g": 116, "b": 13, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30V4Z49RFN0KzqE/b7BnZq"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "playOnLoad": false, "_clips": [{"__uuid__": "874c3c24-9428-4f5f-93e8-557347b187f8", "__expectedType__": "cc.AnimationClip"}, {"__uuid__": "ce771350-55d3-4047-92eb-e24aa2bf72c9", "__expectedType__": "cc.AnimationClip"}, {"__uuid__": "d344f7e0-b524-4323-938c-3406a07ad64f", "__expectedType__": "cc.AnimationClip"}, {"__uuid__": "2aecd8f0-ddfa-4ac9-bd9d-10348eb8274c", "__expectedType__": "cc.AnimationClip"}, {"__uuid__": "ce592c64-62f3-4a4e-a2cc-c628c92f7ac3", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "874c3c24-9428-4f5f-93e8-557347b187f8", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27iMDjh0tBZYRe9LPV9xy7"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbZdOXQSRMeJ/WxrFYz4aw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89oyB4hrhLDpE/cxunOKhW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "5bb34b3OWVCbo2uN7s9z6TN", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 13}, "lblNum": {"__id__": 5}, "anim": {"__id__": 7}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "138pN4z8FFKpwDEaPJnGsy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 15}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cd9lHkBaxFtYbhxLo7BnwD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]