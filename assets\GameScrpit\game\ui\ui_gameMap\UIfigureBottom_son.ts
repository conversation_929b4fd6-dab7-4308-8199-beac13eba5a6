import { _decorator, instantiate, Layers, Node, sp, tween, UIOpacity, UITransform, v3, Vec3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

@ccclass("UIfigureBottom_son")
export class UIfigureBottom_son extends UINode {
  protected _isSwallowTouch: boolean = false; //是否可点击穿透，false为可以
  protected _isSetParent: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_GAME_MAP}?prefab/ui/UIfigureBottom_son`;
  }
}
