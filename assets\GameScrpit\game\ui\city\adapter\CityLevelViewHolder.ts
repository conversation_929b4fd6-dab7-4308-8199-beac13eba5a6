import { CityModule } from "../../../../module/city/CityModule";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { _decorator, EventTouch, Input, instantiate, Label, Node, ProgressBar, ScrollView } from "cc";
import MsgMgr from "../../../../lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { CityRewardAdapter } from "./CityRewardAdapter";
import { CityMessage } from "../../../net/protocol/City";
import FmUtils from "../../../../lib/utils/FmUtils";
import { GoodsModule } from "../../../../module/goods/GoodsModule";
import { IConfigBuildLvReward } from "../../../JsonDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import GuideMgr from "db://assets/GameScrpit/ext_guide/GuideMgr";
const { ccclass, property } = _decorator;

@ccclass("CityLevelViewHolder")
export class CityLevelViewHolder extends ViewHolder {
  private _taskId: number;
  private _adapter: CityRewardAdapter;

  updateData(adapter: CityRewardAdapter, data: any) {
    this._adapter = adapter;
    this._taskId = Number(data);

    let config = CityModule.data.getConfigBuildLvReward(this._taskId);
    this.initTaskItem(this.node, config);
  }

  private initTaskItem(node: Node, config: IConfigBuildLvReward) {
    let totalLevel = CityModule.data.cityTotalLevel;

    node.getChildByPath("bg_9g_biaoqian_wzry1/lbl_city_lv").getComponent(Label).string = `${config.id}.建筑等级`;
    node.getChildByPath("lbl_task").getComponent(Label).string = `建筑总等级达到${config.buildId}`;

    let progressBar = node.getChildByPath("ProgressBar");
    progressBar.getComponent(ProgressBar).progress = totalLevel / config.buildId;
    progressBar.getChildByName("lbl_progress").getComponent(Label).string = `${
      totalLevel > config.buildId ? config.buildId : totalLevel
    }/${config.buildId}`;

    let content = node.getChildByPath("ScrollView/view/content");
    content.children.forEach((item) => (item.active = false));
    for (let i = 0; i < config.rewardList.length; i++) {
      let itemInfo = config.rewardList[i];
      let configItem = GoodsModule.data.getConfigItem(itemInfo[0]);

      let nodeItem = content.children[i];
      if (nodeItem == null) {
        nodeItem = instantiate(content.children[0]);
        content.addChild(nodeItem);
      }
      FmUtils.setItemNode(nodeItem, configItem.id, itemInfo[1]);
      nodeItem.active = true;
    }

    node.getChildByName("ScrollView").getComponent(ScrollView).enabled = config.rewardList.length >= 5;

    let nextIdx = CityModule.data.cityAggregateMessage.nextCityLevelRewardIndex + 1;

    node.getChildByName("btn_finish").active = false;
    node.getChildByName("btn_huangse").active = false;
    node.getChildByName("btn_lanse").active = false;

    if (nextIdx > this._taskId) {
      node.getChildByName("btn_finish").active = true;
    } else {
      if (config.buildId <= CityModule.data.cityTotalLevel) {
        node.getChildByName("btn_huangse").active = true;
        node.getChildByPath("btn_huangse").on(Input.EventType.TOUCH_END, this.click_get, this);
      } else {
        node.getChildByName("btn_lanse").active = true;
        node.getChildByPath("btn_lanse").on(Input.EventType.TOUCH_END, this.click_go, this);
      }
    }
  }

  // 前往，消耗最低的升级建筑
  private click_go(event: EventTouch) {
    AudioMgr.instance.playEffect(1882);
    let cityList = CityModule.data.cityMessageList;

    // 获取最低升级花费的建筑
    let levelUpCostMin = -1;
    let cityId = -1;
    for (let idx in cityList) {
      let cityMsg: CityMessage = cityList[idx];
      if (cityMsg && cityMsg.level > 0) {
        let cfgBuildLv = CityModule.data.getConfigBuildLv(cityMsg.cityId, cityMsg.level);
        if (levelUpCostMin < 0 || levelUpCostMin > cfgBuildLv.cost) {
          levelUpCostMin = cfgBuildLv.cost;
          cityId = cityMsg.cityId;
        }
      }
    }

    if (cityId == -1) {
      cityId = 101;
    }

    GuideMgr.startGuide({ stepId: 23, args: { buildId: cityId } });
  }

  private click_get(event: EventTouch) {
    AudioMgr.instance.playEffect(1883);
    CityModule.api.achievementReward((data: number[]) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data });
      this._adapter.setDatas(1);
    });
  }
}
