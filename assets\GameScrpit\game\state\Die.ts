import { _decorator, isValid } from "cc";
import { FSMState } from "../fight/FSM/FSMState";
import { FSMBoard, STATE } from "../fight/section/StateSection";
import { AnimationSection } from "../fight/section/AnimationSection";
const { ccclass, property } = _decorator;

@ccclass
export default class Die extends FSMState {
  private _lifeTime: number;
  public async onEnter(board: FSMBoard) {
    board.sub.getSection(AnimationSection).playAction(6, false);
  }

  // public update(board: FSMBoard, dt) {
  //     this._lifeTime -= dt;
  //     if (this._lifeTime <= 0) {
  //         this.doRemove(board)
  //     }
  // }

  private doRemove(board: FSMBoard) {
    board.sub.remove();
  }
}
