import { _decorator, sp, tween, v3 } from "cc";
import { CharacterBase, AttrRole } from "./CharacterBase";
import { Sleep } from "../../GameScrpit/game/GameDefine";
const { ccclass, property } = _decorator;

// 角色控制器
// 控制角色的移动、动画、状态切换等
@ccclass("CharacterBossShiJi")
export class CharacterBossShiJi extends CharacterBase {
  protected _spineAniDefault: string = "boss_idle";
  protected _spineAniIdle: string = "boss_idle";
  protected _spineAniWalk: string = "run1";
  protected _spineAniRun: string = "run2";
  protected _spineAniAttack: string = null;

  protected _attr: AttrRole = new AttrRole({
    walkSpeed: 200,
    runSpeed: 400,
    acceleration: 800,
    hpMax: 1,
    attack: 1,
    def: 0,
  });

  start() {
    super.start();
  }

  /*
   * 播放出场动画
   */
  public appear(cbEnd: Function) {
    // 阴影初始是大
    let nodeBgShadow = this.getNode("bg_shadow");
    nodeBgShadow.setScale(2, 2, 1);

    Sleep(0.3);
    this.spineCharacter.clearTracks();
    this.spineCharacter.clearAnimations();
    let x = this.spineCharacter.setAnimation(0, "appear", false);
    this.spineCharacter.setCompleteListener((x: sp.spine.TrackEntry) => {
      if (x.animation.name === "appear") {
        this.spineCharacter.setCompleteListener(null);
        this.idle();
        cbEnd();
      }
    });

    tween(nodeBgShadow)
      .to(0.3, { scale: v3(1, 1, 1) })
      .start();
  }

  public disappear(cbEnd: Function) {
    this.spineCharacter.setAnimation(0, "disappear", false);
    this.spineCharacter.setCompleteListener(() => {
      this.spineCharacter.setCompleteListener(null);
      this.node.active = false;
      cbEnd();
    });
  }
}
