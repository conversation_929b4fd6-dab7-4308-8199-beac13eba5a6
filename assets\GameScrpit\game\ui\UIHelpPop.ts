import { _decorator, Component, Label, Node, RichText } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { DialogZero } from "../GameDefine";
import { JsonMgr } from "../mgr/JsonMgr";
import { UITransform } from "cc";
const { ccclass, property } = _decorator;

@ccclass("UIHelpPop")
export class UIHelpPop extends UINode {
  protected _isAddToBottom: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UIHelpPop`;
  }

  private _titleId: number;
  private _desId: number;

  public init(args: any): void {
    super.init(args);
    if (!args) {
      args = {
        titleId: -1,
        desId: -1,
      };
    }

    this._titleId = args.titleId || -1;
    this._desId = args.desId || -1;
  }

  protected onEvtShow(): void {
    this.setTitle();
    this.setDes();
  }

  private setTitle() {
    if (this._titleId == -1) {
      this.getNode("titleLab").getComponent(Label).string = JsonMgr.instance.jsonList.c_help[1].helpText;
      return;
    }
    this.getNode("titleLab").getComponent(Label).string = JsonMgr.instance.jsonList.c_help[this._titleId].helpText;
  }

  private setDes() {
    if (this._desId == -1) {
      this.getNode("des_lab").getComponent(RichText).string = "";
      return;
    }
    this.getNode("des_lab").getComponent(RichText).string = JsonMgr.instance.jsonList.c_help[this._desId].helpText;
  }

  public tick(dt: any): void {
    let uiTransform = this.node.getComponent(UITransform);
    let height = this.getNode("des_lab").getComponent(UITransform).height;
    let newHeight = height + 200;

    if (newHeight > 1200) {
      newHeight = 1200;
    }

    if (uiTransform.height != newHeight) {
      uiTransform.height = newHeight;
    }
  }
}
