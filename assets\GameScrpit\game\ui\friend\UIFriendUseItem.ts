import { _decorator, Component, Label, Node, Slider, Sprite } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { FriendModule } from "../../../module/friend/FriendModule";
import { FriendVitalityMessage } from "../../net/protocol/Friend";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { DialogZero } from "../../GameDefine";
import { UIItemJoinPop } from "../ui_knappsack/UIItemJoinPop";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Tue Jun 25 2024 19:50:41 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/friend/UIFriendUseItem.ts
 *
 */

@ccclass("UIFriendUseItem")
export class UIFriendUseItem extends UINode {
  protected _openAct: boolean = true;
  private _sliderNum: number;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FRIEND}?prefab/ui/UIFriendUseItem`;
  }
  public zOrder(): number {
    return DialogZero.UIFriendDialog;
  }
  //=================================================
  protected onRegEvent(): void {
    this["item_slider"].on("slide", this.onSliderValueChanged, this);
  }

  protected onDelEvent(): void {
    this["item_slider"].off("slide", this.onSliderValueChanged, this);
  }
  private setItemNum(useNum: number) {
    let itemNum = PlayerModule.data.getItemNum(1004);
    // if (itemNum > 500) {
    //   itemNum = 500;
    // }
    this._sliderNum = useNum;
    let progress = itemNum == 0 ? 0 : this._sliderNum / itemNum;
    this["item_can_use"].getComponent(Label).string = this._sliderNum + "/" + itemNum;
    this["item_slider"].getComponent(Slider).progress = progress;
    this["fill"].getComponent(Sprite).fillRange = progress;
    this["item_numbers"].getComponent(Label).string = `拥有:${itemNum}`;
    // this.setSliderBtnState();
  }
  private onSliderValueChanged(event) {
    // 这里处理滑动条值改变的逻辑
    let progress = event.progress;
    this._sliderNum = Math.floor(progress * PlayerModule.data.getItemNum(1004));
    this.setItemNum(this._sliderNum);
  }
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.setItemNum(0);
  }
  private on_click_commit() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    FriendModule.api.addvitality(this._sliderNum, (data: FriendVitalityMessage) => {
      UIMgr.instance.back();
    });
  }
  private on_click_obtain() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
      itemId: ItemEnum.精力丹_1004,
    });
  }
  private on_click_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_minus() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._sliderNum--;
    if (this._sliderNum <= 0) {
      this._sliderNum = 0;
    }
    this.setItemNum(this._sliderNum);
  }
  private on_click_add() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._sliderNum++;
    if (this._sliderNum > PlayerModule.data.getItemNum(1004)) {
      this._sliderNum = PlayerModule.data.getItemNum(1004);
    }
    this.setItemNum(this._sliderNum);
  }
}
