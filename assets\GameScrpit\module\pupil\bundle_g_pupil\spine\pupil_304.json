{"skeleton": {"hash": "FjQLVk3fX6d3i6VUP7qGeyN4Rjs=", "spine": "3.8.75", "x": -40.39, "y": -10.83, "width": 96.94, "height": 115.77, "images": "./images/", "audio": "D:/spine导出/弟子spine/仓鼠弟子"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 90.95, "rotation": -89.73, "x": -2.38, "y": 4.11, "scaleX": 0.3501, "scaleY": 0.3501}, {"name": "bone2", "parent": "bone", "length": 27.35, "rotation": 91.41, "x": -61.36, "y": 3.86, "scaleX": 2.8486, "scaleY": 2.8486}, {"name": "bone3", "parent": "bone2", "length": 11.26, "rotation": 88.32, "x": -4.36, "y": 2.34}, {"name": "bone4", "parent": "bone3", "length": 7.45, "rotation": -3.09, "x": 11.26}, {"name": "bone5", "parent": "bone4", "length": 5.06, "rotation": 9.94, "x": 7.45}, {"name": "bone6", "parent": "bone4", "x": 7.01, "y": -16.75}, {"name": "bone7", "parent": "bone4", "x": 4.52, "y": 9.5}, {"name": "bone8", "parent": "bone2", "length": 9.22, "rotation": -108.14, "x": -16.45, "y": 1.49}, {"name": "bone9", "parent": "bone8", "length": 10.97, "rotation": 57.74, "x": 9.22}, {"name": "bone10", "parent": "bone9", "length": 6.4, "rotation": -87.56, "x": 10.97}, {"name": "bone11", "parent": "bone10", "length": 8.4, "rotation": -32.69, "x": 6.4}, {"name": "bone12", "parent": "bone2", "length": 8.78, "rotation": -75.74, "x": 15.2, "y": -2.86}, {"name": "bone13", "parent": "bone12", "length": 8.37, "rotation": -51.16, "x": 8.78}, {"name": "bone14", "parent": "bone13", "length": 6.4, "rotation": 78.94, "x": 8.37}, {"name": "bone15", "parent": "bone14", "length": 5.57, "rotation": 20.63, "x": 6.4}, {"name": "bone16", "parent": "bone2", "length": 13.49, "rotation": 22.97, "x": 21.61, "y": -10.49}, {"name": "bone17", "parent": "bone16", "length": 18.65, "rotation": 54.16, "x": 13.49}, {"name": "bone18", "parent": "bone17", "length": 15.98, "rotation": -85.31, "x": 18.65}, {"name": "bone19", "parent": "bone18", "length": 18.85, "rotation": -90.85, "x": 15.98}, {"name": "bone20", "parent": "bone19", "length": 11.95, "rotation": -40.37, "x": 18.85}, {"name": "bone21", "parent": "bone20", "length": 13.47, "rotation": -40.56, "x": 11.95}, {"name": "bone23", "parent": "bone5", "length": 9.95, "rotation": -60.37, "x": 28.59, "y": -24.57}, {"name": "bone24", "parent": "bone23", "length": 10.77, "rotation": 22.41, "x": 9.95}, {"name": "bone25", "parent": "bone24", "length": 8.89, "rotation": 27.76, "x": 10.92, "y": 0.09}, {"name": "bone26", "parent": "bone5", "length": 10.21, "rotation": 38.85, "x": 31.43, "y": 14.86}, {"name": "bone27", "parent": "bone26", "length": 9.71, "rotation": -31.16, "x": 10.21}, {"name": "bone28", "parent": "bone27", "length": 9.41, "rotation": -17.71, "x": 9.71}, {"name": "bone29", "parent": "bone6", "length": 6.45, "rotation": -145.94, "x": -1.88, "y": -3.32}, {"name": "bone30", "parent": "bone29", "length": 10.46, "rotation": -93.38, "x": 6.53, "y": 0.12}, {"name": "bone31", "parent": "bone30", "length": 4.02, "rotation": 13.23, "x": 10.46}, {"name": "bone32", "parent": "bone7", "length": 10.21, "rotation": 154.79, "x": 1.62, "y": -4.58}, {"name": "bone33", "parent": "bone32", "length": 9.72, "rotation": -37.32, "x": 9.96, "y": 0.18}, {"name": "bone22", "parent": "bone", "length": 72.99, "rotation": -87.31, "x": -4.52, "y": -27.87}, {"name": "target1", "parent": "bone22", "rotation": 177.04, "x": 1.82, "y": -10.6, "color": "ff3f00ff"}, {"name": "target2", "parent": "bone22", "rotation": 177.04, "x": 39.74, "y": 3.74, "color": "ff3f00ff"}, {"name": "bone34", "parent": "bone", "length": 50.52, "rotation": -88.46, "x": -3.59, "y": 53.92}, {"name": "target3", "parent": "bone34", "rotation": 178.19, "x": 13.64, "y": -7.33, "color": "ff3f00ff"}, {"name": "target4", "parent": "bone34", "rotation": 178.19, "x": -12.92, "y": 13.5, "color": "ff3f00ff"}], "slots": [{"name": "sd", "bone": "bone", "color": "ffffff87", "attachment": "sd"}, {"name": "s2", "bone": "bone", "attachment": "s2"}, {"name": "weiba", "bone": "bone", "attachment": "weiba"}, {"name": "j1", "bone": "bone", "attachment": "j1"}, {"name": "bd", "bone": "bone", "attachment": "bd"}, {"name": "j2", "bone": "bone", "attachment": "j2"}, {"name": "e2", "bone": "bone", "attachment": "e2"}, {"name": "tou2", "bone": "bone5", "attachment": "tou2"}, {"name": "tou1", "bone": "bone5"}, {"name": "e1", "bone": "bone", "attachment": "e1"}, {"name": "s1", "bone": "bone", "attachment": "s1"}], "ik": [{"name": "target1", "bones": ["bone8", "bone9"], "target": "target1", "stretch": true}, {"name": "target2", "order": 1, "bones": ["bone10", "bone11"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 2, "bones": ["bone12", "bone13"], "target": "target3", "bendPositive": false}, {"name": "target4", "order": 3, "bones": ["bone14", "bone15"], "target": "target4"}], "skins": [{"name": "default", "attachments": {"bd": {"bd": {"type": "mesh", "uvs": [0.39274, 0, 0.56759, 0, 0.74464, 0.0381, 0.88452, 0.18526, 0.97851, 0.38614, 1, 0.59871, 0.93698, 0.79726, 0.80365, 0.90938, 0.59601, 0.96778, 0.34902, 0.96077, 0.16979, 0.8977, 0.02991, 0.69214, 0.00586, 0.44454, 0.05614, 0.22731, 0.18946, 0.05912, 0.32685, 0.18848, 0.31457, 0.45662, 0.31983, 0.68162, 0.33562, 0.85412, 0.58476, 0.18286, 0.60932, 0.44912, 0.60757, 0.69475, 0.60581, 0.856, 0.77775, 0.18661, 0.81635, 0.43412, 0.78653, 0.66474, 0.7567, 0.8335, 0.15666, 0.21474, 0.13912, 0.45474, 0.16368, 0.68724, 0.20579, 0.83725], "triangles": [21, 17, 20, 9, 10, 18, 10, 30, 18, 9, 18, 22, 10, 11, 30, 30, 11, 29, 30, 17, 18, 18, 17, 21, 30, 29, 17, 25, 24, 5, 6, 25, 5, 8, 26, 7, 8, 22, 26, 8, 9, 22, 7, 26, 6, 22, 21, 26, 22, 18, 21, 26, 25, 6, 26, 21, 25, 21, 20, 25, 11, 28, 29, 29, 16, 17, 28, 13, 27, 27, 13, 14, 27, 14, 15, 16, 27, 15, 20, 19, 23, 24, 23, 3, 19, 2, 23, 23, 2, 3, 19, 1, 2, 0, 1, 19, 19, 16, 15, 15, 0, 19, 15, 14, 0, 28, 27, 16, 16, 19, 20, 11, 12, 28, 29, 28, 16, 20, 17, 16, 25, 20, 24, 12, 13, 28, 24, 4, 5, 20, 23, 24, 24, 3, 4], "vertices": [3, 4, 11.53, -1.24, 0.02482, 5, 3.8, -1.93, 0.93206, 6, 4.52, 15.51, 0.04312, 3, 4, 11.99, -9.82, 0.20425, 5, 2.78, -10.46, 0.2458, 6, 4.98, 6.93, 0.54995, 3, 4, 10.71, -18.61, 0.00019, 5, 0, -18.89, 0.00483, 6, 3.7, -1.85, 0.99498, 2, 3, 14.18, -26.03, 0.11517, 6, -2.68, -9.08, 0.88483, 2, 3, 4.94, -30.65, 0.71117, 6, -11.66, -14.19, 0.28883, 1, 2, 27.2, -3.42, 1, 1, 2, 23.83, -12.46, 1, 1, 2, 17.13, -17.42, 1, 1, 2, 6.85, -19.8, 1, 1, 2, -5.27, -19.13, 1, 1, 2, -13.99, -15.97, 1, 1, 2, -20.59, -6.32, 1, 2, 3, 2.26, 17.15, 0.72, 7, -14.43, 7.14, 0.28, 2, 3, 12.25, 14.68, 0.24935, 7, -4.32, 5.21, 0.75065, 3, 4, 8.27, 8.59, 0.13727, 5, 2.3, 8.31, 0.09989, 7, 3.76, -0.92, 0.76284, 3, 4, 2.7, 1.52, 0.92432, 5, -4.42, 2.32, 0.00207, 7, -1.82, -7.98, 0.07361, 2, 3, 1.7, 1.98, 0.72, 7, -14.17, -8.04, 0.28, 1, 2, -6.33, -6.25, 1, 1, 2, -5.79, -14.2, 1, 4, 3, 14.29, -11.3, 0.12556, 4, 3.64, -11.12, 0.25807, 5, -5.67, -10.3, 0.03933, 6, -3.37, 5.63, 0.57704, 3, 3, 2.05, -12.51, 0.70476, 4, -8.52, -12.99, 0.02346, 6, -15.53, 3.77, 0.27178, 1, 2, 7.79, -7.27, 1, 1, 2, 7.48, -14.68, 1, 3, 3, 14.12, -20.79, 0.07587, 4, 3.98, -20.6, 0.00383, 6, -3.03, -3.85, 0.9203, 3, 3, 2.74, -22.68, 0.75651, 4, -7.28, -23.11, 0.00117, 6, -14.29, -6.36, 0.24232, 1, 2, 16.62, -6.15, 1, 1, 2, 14.93, -13.86, 1, 3, 3, 12.82, 9.74, 0.16784, 4, 1.04, 9.81, 0.05214, 7, -3.48, 0.31, 0.78003, 2, 3, 1.79, 10.6, 0.72, 7, -14.55, 0.57, 0.28, 1, 2, -14.01, -6.28, 1, 1, 2, -12.14, -13.24, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 2, 38, 38, 40, 40, 42, 42, 34, 42, 44, 4, 46, 46, 48, 48, 50, 50, 52, 28, 54, 54, 56, 56, 58, 58, 60], "width": 140, "height": 131}}, "j1": {"j1": {"type": "mesh", "uvs": [0.45474, 0, 0.7002, 0.0124, 0.90297, 0.14958, 1, 0.3591, 1, 0.57111, 0.95633, 0.71079, 0.92075, 0.78811, 0.93498, 0.8779, 0.80336, 0.9228, 0.60415, 0.92779, 0.46897, 0.96769, 0.24129, 0.99513, 0.04564, 0.96769, 0.03141, 0.86543, 0.14169, 0.80307, 0.30177, 0.78312, 0.3907, 0.77813, 0.25908, 0.71827, 0.11323, 0.63596, 0, 0.49878, 0, 0.34164, 0.13102, 0.16455, 0.24841, 0.0548, 0.37927, 0.10469, 0.34214, 0.24287, 0.36784, 0.43912, 0.50494, 0.59332, 0.63918, 0.70346, 0.63918, 0.79158, 0.4878, 0.85766, 0.26502, 0.8857, 0.1165, 0.89571], "triangles": [30, 14, 15, 30, 15, 16, 30, 16, 29, 31, 13, 14, 31, 14, 30, 12, 13, 31, 10, 30, 29, 11, 31, 30, 11, 30, 10, 12, 31, 11, 28, 27, 6, 16, 27, 28, 29, 16, 28, 8, 28, 6, 8, 6, 7, 9, 29, 28, 9, 28, 8, 10, 29, 9, 4, 26, 3, 3, 26, 25, 18, 25, 26, 27, 26, 4, 5, 27, 4, 17, 18, 26, 16, 17, 26, 16, 26, 27, 6, 27, 5, 23, 22, 0, 23, 21, 22, 24, 21, 23, 20, 21, 24, 2, 25, 24, 20, 24, 25, 19, 20, 25, 3, 25, 2, 23, 1, 2, 1, 23, 0, 23, 2, 24, 18, 19, 25], "vertices": [2, 8, -4.59, -1.94, 0.99999, 9, -9.01, 10.65, 1e-05, 2, 8, -5.72, 3.21, 0.96647, 9, -5.25, 14.35, 0.03353, 2, 8, -2.93, 8.56, 0.79246, 9, 0.76, 14.85, 0.20754, 2, 8, 2.62, 12.37, 0.4373, 9, 6.94, 12.18, 0.5627, 3, 8, 8.83, 14.2, 0.12799, 9, 11.8, 7.91, 0.83431, 10, -7.87, 1.17, 0.0377, 3, 8, 13.19, 14.52, 0.02358, 9, 14.39, 4.39, 0.59263, 10, -4.24, 3.61, 0.38379, 3, 8, 15.67, 14.46, 0.00287, 9, 15.66, 2.26, 0.21755, 10, -2.06, 4.79, 0.77957, 2, 9, 17.93, 0.68, 0.025, 10, -0.39, 6.98, 0.975, 1, 10, 2.6, 6.02, 1, 2, 10, 5.79, 3.19, 0.91452, 11, -2.24, 2.35, 0.08548, 2, 10, 8.72, 2.07, 0.11611, 11, 0.84, 2.99, 0.88389, 1, 11, 5.79, 2.88, 1, 1, 11, 9.74, 1.26, 1, 1, 11, 9.44, -1.87, 1, 2, 10, 10.31, -6.41, 0.00066, 11, 6.76, -3.28, 0.99934, 3, 9, 6.8, -7.6, 0.03899, 10, 7.41, -4.48, 0.09458, 11, 3.27, -3.23, 0.86643, 3, 9, 7.95, -6.06, 0.24549, 10, 5.93, -3.28, 0.3734, 11, 1.38, -3.01, 0.38111, 4, 8, 17.64, 0.26, 0.00651, 9, 4.71, -6.98, 0.77401, 10, 6.7, -6.55, 0.18947, 11, 3.79, -5.34, 0.03001, 4, 8, 16.11, -3.45, 0.14177, 9, 0.76, -7.66, 0.80021, 10, 7.22, -10.52, 0.05775, 11, 6.38, -8.41, 0.00027, 3, 8, 12.78, -6.96, 0.55661, 9, -3.99, -6.72, 0.43565, 10, 6.08, -15.23, 0.00775, 2, 8, 8.18, -8.32, 0.90875, 9, -7.59, -3.55, 0.09125, 1, 8, 2.19, -7.16, 1, 1, 8, -1.73, -5.7, 1, 1, 8, -1.06, -2.58, 1, 1, 8, 3.21, -2.15, 1, 2, 8, 8.8, 0.08, 0.96904, 9, -0.16, 0.4, 0.03096, 3, 9, 5.32, -0.5, 0.98794, 10, 0.26, -5.66, 0.01194, 11, -2.11, -8.08, 0.00012, 2, 9, 9.74, -0.56, 0.77591, 10, 0.51, -1.25, 0.22409, 1, 10, 2.37, 0.7, 1, 3, 9, 11.14, -6.1, 0.00196, 10, 6.11, -0.08, 0.82877, 11, -0.2, -0.23, 0.16928, 3, 9, 8.64, -10.25, 0.00032, 10, 10.15, -2.76, 0.00117, 11, 4.65, -0.3, 0.99851, 1, 11, 7.83, -0.61, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 61, "height": 87}}, "j2": {"j2": {"type": "mesh", "uvs": [0.35732, 0.04023, 0.58821, 0, 0.81378, 0, 0.97301, 0.07848, 1, 0.33048, 0.96771, 0.53523, 0.88013, 0.68823, 0.72886, 0.77823, 0.78724, 0.82098, 0.87747, 0.90873, 0.89605, 0.98523, 0.76147, 0.99751, 0.73417, 1, 0.60545, 0.99214, 0.45551, 0.98298, 0.39713, 0.89523, 0.31221, 0.82098, 0.15563, 0.78723, 0, 0.65898, 0, 0.31698, 0.12113, 0.15948, 0.67364, 0.1334, 0.68719, 0.31029, 0.63571, 0.54922, 0.56254, 0.71003, 0.49209, 0.82031, 0.57609, 0.89382, 0.27261, 0.68247, 0.2428, 0.5676, 0.29157, 0.36084, 0.37015, 0.15637], "triangles": [26, 24, 7, 13, 26, 12, 8, 26, 7, 8, 11, 26, 9, 11, 8, 14, 26, 13, 10, 11, 9, 11, 12, 26, 16, 27, 25, 25, 24, 26, 15, 16, 25, 26, 14, 15, 26, 15, 25, 28, 19, 29, 28, 29, 23, 18, 19, 28, 24, 27, 28, 18, 28, 27, 6, 23, 5, 23, 24, 28, 7, 24, 23, 6, 7, 23, 17, 18, 27, 25, 27, 24, 17, 27, 16, 21, 1, 2, 30, 0, 1, 30, 1, 21, 20, 0, 30, 3, 22, 21, 3, 21, 2, 30, 21, 22, 22, 3, 4, 29, 20, 30, 29, 30, 22, 19, 20, 29, 5, 22, 4, 23, 29, 22, 23, 22, 5], "vertices": [4, 12, -7.94, -2.08, 0.46114, 13, -8.87, -14.33, 0.01502, 3, 2.44, -15.51, 0.256, 2, 11.22, 4.33, 0.26784, 3, 12, -7.46, 4.35, 0.47616, 3, 3.74, -21.84, 0.256, 2, 17.58, 5.44, 0.26784, 2, 12, -5.76, 10.29, 0.744, 3, 3.74, -28.01, 0.256, 3, 12, -2.13, 13.79, 0.47616, 3, 1.21, -32.37, 0.256, 2, 28.04, 2.6, 0.26784, 3, 12, 5.9, 12.26, 0.95632, 13, -11.36, 5.45, 0.04254, 15, 2.67, 20.79, 0.00114, 4, 12, 12.02, 9.6, 0.68843, 13, -5.44, 8.54, 0.27619, 14, 5.74, 15.19, 0.00641, 15, 4.73, 14.45, 0.02896, 4, 12, 16.11, 5.93, 0.32284, 13, -0.02, 9.43, 0.50362, 14, 7.65, 10.04, 0.05802, 15, 4.71, 8.96, 0.11552, 4, 12, 17.77, 1.15, 0.03922, 13, 4.74, 7.72, 0.28046, 14, 6.89, 5.04, 0.17129, 15, 2.23, 4.55, 0.50903, 4, 12, 19.53, 2.31, 0.00519, 13, 4.94, 9.83, 0.07335, 14, 8.99, 5.24, 0.04272, 15, 4.27, 3.99, 0.87874, 2, 13, 5.83, 13.48, 0.00167, 15, 7.73, 2.51, 0.99833, 1, 15, 9.25, 0.5, 1, 1, 15, 6.1, -1.45, 1, 1, 15, 5.46, -1.85, 1, 2, 14, 9.54, -2.18, 0.03367, 15, 2.18, -3.15, 0.96633, 2, 14, 6.49, -4.94, 0.54779, 15, -1.65, -4.66, 0.45221, 3, 13, 13.06, 2.48, 0.00962, 14, 3.34, -4.14, 0.90216, 15, -4.32, -2.79, 0.08822, 3, 12, 15.96, -10.2, 0.00747, 13, 12.45, -0.8, 0.38567, 14, 0, -4.16, 0.60686, 3, 12, 13.74, -14.02, 0.05983, 13, 14.03, -4.93, 0.82013, 14, -3.75, -6.5, 0.12004, 3, 12, 8.58, -16.98, 0.16733, 13, 13.1, -10.8, 0.82772, 14, -9.69, -6.72, 0.00495, 3, 12, -2.04, -13.94, 0.35721, 13, 4.08, -17.17, 0.28279, 2, 1.18, -4.32, 0.36, 4, 12, -6.02, -9.36, 0.39741, 13, -1.99, -17.39, 0.11971, 3, -1.41, -9.05, 0.192, 2, 4.65, 0.67, 0.29088, 1, 12, -2.67, 5.42, 1, 2, 12, 2.92, 4.21, 0.99927, 13, -6.95, -1.92, 0.00073, 4, 12, 9.96, 0.73, 0.26333, 13, 0.17, 1.38, 0.7167, 14, -0.22, 8.31, 0.00939, 15, -3.27, 10.11, 0.01058, 4, 12, 14.4, -2.62, 0.00084, 13, 5.57, 2.73, 0.57899, 14, 2.15, 3.27, 0.3704, 15, -2.83, 4.56, 0.04977, 1, 14, 3.39, -0.58, 1, 2, 14, 6.69, -0.56, 0.22985, 15, 0.08, -0.63, 0.77015, 3, 12, 11.36, -10.01, 0.06934, 13, 9.42, -4.27, 0.864, 14, -3.98, -1.85, 0.06666, 3, 12, 7.57, -9.78, 0.22106, 13, 6.86, -7.07, 0.77868, 14, -7.23, 0.12, 0.00026, 2, 12, 1.52, -6.66, 0.67051, 13, 0.63, -9.83, 0.32949, 2, 12, -4.24, -2.77, 0.9461, 13, -6.01, -11.88, 0.0539], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 2, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 20, 22, 22, 24, 52, 22, 54, 56, 56, 58, 58, 60], "width": 78, "height": 92}}, "sd": {"sd": {"x": 7.69, "y": 6.27, "rotation": 89.73, "width": 174, "height": 70}}, "tou1": {"tou1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.31, -12.67, 19.7, 15.56, 37.13, 13.47, 33.74, -14.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 81, "height": 50}}, "tou2": {"tou2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-8.14, -42.62, 1.23, 35.46, 51.77, 29.39, 42.4, -48.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 224, "height": 145}}, "weiba": {"weiba": {"type": "mesh", "uvs": [0.00896, 0.72272, 0.11748, 0.70269, 0.22106, 0.67958, 0.29998, 0.58715, 0.29834, 0.48856, 0.27532, 0.37918, 0.29176, 0.24977, 0.34931, 0.12961, 0.45454, 0.0418, 0.5795, 0, 0.74393, 0, 0.86724, 0.05566, 0.94123, 0.15734, 1, 0.3286, 1, 0.52609, 0.9662, 0.66591, 0.89718, 0.78651, 0.80018, 0.88613, 0.65841, 0.95604, 0.52224, 1, 0.35808, 1, 0.21072, 0.98051, 0.14356, 0.92283, 0.14543, 0.88613, 0.26295, 0.87739, 0.40285, 0.8669, 0.52224, 0.8337, 0.64349, 0.77078, 0.73862, 0.68863, 0.80578, 0.56979, 0.83376, 0.4422, 0.81324, 0.30413, 0.71064, 0.20975, 0.61551, 0.20451, 0.5353, 0.25694, 0.50172, 0.33384, 0.51664, 0.47366, 0.51556, 0.56102, 0.51478, 0.62397, 0.47001, 0.70436, 0.40845, 0.77777, 0.3357, 0.82845, 0.24861, 0.85664, 0.1688, 0.86636, 0.09139, 0.8701, 0, 0.80695, 0.11484, 0.78642, 0.22788, 0.76048, 0.32707, 0.7032, 0.37782, 0.6081, 0.39513, 0.49247, 0.38244, 0.39736, 0.39264, 0.29136, 0.44202, 0.18128, 0.54419, 0.11747, 0.67019, 0.09354, 0.78767, 0.13342, 0.85408, 0.22914, 0.91538, 0.3472, 0.91027, 0.46685, 0.88303, 0.59926, 0.82343, 0.70774, 0.73489, 0.80825, 0.64805, 0.87047, 0.54419, 0.91514, 0.43351, 0.93747, 0.3024, 0.93269, 0.19853, 0.91833], "triangles": [64, 26, 63, 65, 25, 26, 67, 23, 24, 22, 23, 67, 66, 24, 25, 67, 24, 66, 64, 65, 26, 66, 25, 65, 64, 63, 18, 21, 67, 66, 22, 67, 21, 20, 66, 65, 21, 66, 20, 19, 65, 64, 19, 64, 18, 20, 65, 19, 62, 27, 28, 63, 27, 62, 26, 27, 63, 17, 62, 16, 17, 18, 63, 17, 63, 62, 13, 58, 57, 30, 31, 58, 59, 30, 58, 59, 58, 13, 14, 59, 13, 29, 30, 59, 60, 29, 59, 60, 59, 14, 15, 60, 14, 61, 29, 60, 61, 60, 15, 28, 29, 61, 16, 61, 15, 62, 28, 61, 62, 61, 16, 55, 9, 10, 54, 8, 9, 54, 9, 55, 56, 10, 11, 55, 10, 56, 56, 11, 12, 53, 7, 8, 53, 8, 54, 33, 54, 55, 32, 55, 56, 33, 55, 32, 57, 56, 12, 32, 56, 57, 34, 53, 54, 34, 54, 33, 31, 32, 57, 57, 12, 13, 58, 31, 57, 6, 7, 53, 52, 6, 53, 52, 53, 34, 35, 52, 34, 5, 6, 52, 51, 5, 52, 51, 52, 35, 51, 35, 36, 4, 5, 51, 50, 51, 36, 4, 51, 50, 37, 50, 36, 3, 4, 50, 49, 3, 50, 49, 50, 37, 38, 49, 37, 48, 3, 49, 39, 49, 38, 48, 49, 39, 2, 3, 48, 47, 2, 48, 1, 2, 47, 40, 48, 39, 46, 0, 1, 46, 1, 47, 45, 0, 46, 41, 48, 40, 47, 48, 41, 42, 47, 41, 43, 46, 47, 42, 43, 47, 44, 45, 46, 44, 46, 43], "vertices": [3, 16, 1.2, 3.24, 0.94563, 17, -4.58, 11.86, 0.05437, 19, 22.62, -29.37, 0, 3, 16, 5.12, 2.29, 0.83385, 17, -3.04, 8.13, 0.16615, 19, 21.33, -25.54, 0, 3, 16, 8.93, 1.53, 0.64824, 17, -1.43, 4.59, 0.35176, 19, 19.96, -21.9, 0, 4, 16, 13.06, 3.6, 0.41998, 17, 2.67, 2.46, 0.57556, 18, -3.76, -15.72, 0.00446, 19, 16.02, -19.51, 0, 4, 16, 14.6, 7.12, 0.21683, 17, 6.42, 3.27, 0.75353, 18, -4.26, -11.91, 0.02964, 19, 12.21, -20.06, 0, 4, 16, 15.62, 11.34, 0.08168, 17, 10.44, 4.92, 0.81416, 18, -5.57, -7.77, 0.10416, 19, 8.09, -21.44, 0, 4, 16, 18.27, 15.68, 0.02037, 17, 15.5, 5.31, 0.72848, 18, -5.55, -2.7, 0.25115, 19, 3.02, -21.49, 0, 4, 16, 22.13, 19.06, 0.00203, 17, 20.51, 4.16, 0.53913, 18, -3.99, 2.19, 0.4588, 19, -1.9, -20, 5e-05, 3, 17, 24.61, 1.05, 0.32156, 18, -0.56, 6.03, 0.67523, 19, -5.78, -16.63, 0.00321, 3, 17, 27.09, -3.11, 0.14916, 18, 3.79, 8.16, 0.82593, 19, -7.98, -12.31, 0.02491, 3, 17, 28.26, -9, 0.04924, 18, 9.75, 8.84, 0.85804, 19, -8.75, -6.36, 0.09272, 4, 17, 27, -13.83, 0.01002, 18, 14.47, 7.2, 0.7555, 19, -7.17, -1.62, 0.2344, 20, -18.78, -18.09, 7e-05, 4, 17, 23.64, -17.25, 0.00053, 18, 17.6, 3.57, 0.55851, 19, -3.59, 1.57, 0.4323, 20, -18.11, -13.34, 0.00865, 3, 18, 20.49, -2.82, 0.33383, 19, 2.75, 4.55, 0.61967, 20, -15.21, -6.96, 0.0465, 4, 18, 21.36, -10.47, 0.15473, 19, 10.39, 5.54, 0.69883, 20, -10.03, -1.26, 0.14625, 21, -15.88, -15.26, 0.00019, 4, 18, 20.75, -16.02, 0.04957, 19, 15.95, 5.01, 0.6302, 20, -5.45, 1.94, 0.30718, 21, -14.48, -9.85, 0.01305, 4, 18, 18.78, -20.97, 0.00932, 19, 20.93, 3.11, 0.4481, 20, -0.43, 3.72, 0.47917, 21, -11.82, -5.23, 0.06342, 3, 19, 25.23, 0.1, 0.24835, 20, 4.8, 4.21, 0.5639, 21, -8.17, -1.45, 0.18775, 3, 19, 28.6, -4.69, 0.10137, 20, 10.47, 2.74, 0.51303, 21, -2.91, 1.12, 0.38559, 3, 19, 30.93, -9.4, 0.02825, 20, 15.3, 0.67, 0.35226, 21, 2.11, 2.68, 0.61949, 3, 19, 31.7, -15.34, 0.00399, 20, 19.73, -3.36, 0.17998, 21, 8.1, 2.5, 0.81603, 2, 20, 23.2, -7.55, 0.06139, 21, 13.45, 1.58, 0.93861, 2, 20, 23.5, -10.86, 0.01703, 21, 15.84, -0.74, 0.98297, 2, 20, 22.49, -11.87, 0.03105, 21, 15.73, -2.16, 0.96895, 3, 19, 27.41, -19.4, 0.00512, 20, 19.09, -9.24, 0.10305, 21, 11.43, -2.38, 0.89183, 3, 19, 26.35, -14.39, 0.03284, 20, 15.03, -6.1, 0.22611, 21, 6.31, -2.63, 0.74105, 3, 19, 24.51, -10.23, 0.11295, 20, 10.94, -4.13, 0.35673, 21, 1.91, -3.8, 0.53032, 4, 18, 9.51, -21.41, 0.00554, 19, 21.51, -6.15, 0.2616, 20, 6.01, -2.96, 0.42253, 21, -2.58, -6.11, 0.31032, 4, 18, 12.6, -17.84, 0.03452, 19, 17.89, -3.12, 0.44552, 20, 1.29, -3, 0.37939, 21, -6.15, -9.21, 0.14057, 4, 18, 14.51, -12.96, 0.11716, 19, 12.98, -1.28, 0.58208, 20, -3.64, -4.77, 0.25633, 21, -8.74, -13.77, 0.04443, 5, 17, 11.99, -15.56, 0.00359, 18, 14.96, -7.91, 0.27028, 19, 7.92, -0.9, 0.59217, 20, -7.74, -7.77, 0.12571, 21, -9.91, -18.71, 0.00825, 5, 17, 17.12, -13.78, 0.02591, 18, 13.61, -2.65, 0.46211, 19, 2.68, -2.33, 0.47075, 20, -10.8, -12.25, 0.04109, 21, -9.32, -24.11, 0.00015, 4, 17, 20, -9.39, 0.09443, 18, 9.47, 0.58, 0.61151, 19, -0.49, -6.52, 0.28684, 20, -10.51, -17.49, 0.00722, 3, 17, 19.53, -5.94, 0.23463, 18, 6, 0.39, 0.63557, 19, -0.25, -9.99, 0.12979, 4, 16, 26.23, 11.71, 0.00013, 17, 16.96, -3.47, 0.43802, 18, 3.32, -1.97, 0.52227, 19, 2.16, -12.63, 0.03959, 4, 16, 23.87, 9.5, 0.0038, 17, 13.78, -2.85, 0.65575, 18, 2.44, -5.08, 0.33363, 19, 5.28, -13.47, 0.00682, 4, 16, 22.09, 4.32, 0.02448, 17, 8.54, -4.44, 0.81346, 18, 3.6, -10.44, 0.16206, 19, 10.62, -12.23, 1e-05, 4, 16, 20.63, 1.25, 0.08741, 17, 5.19, -5.06, 0.85724, 18, 3.94, -13.82, 0.05535, 19, 14, -11.83, 0, 4, 16, 19.58, -0.97, 0.21702, 17, 2.78, -5.51, 0.77103, 18, 4.19, -16.26, 0.01195, 19, 16.44, -11.55, 0, 4, 16, 16.79, -3.14, 0.40991, 17, -0.61, -4.51, 0.58929, 18, 2.92, -19.56, 0.0008, 19, 19.75, -12.77, 0, 3, 16, 13.56, -4.8, 0.62662, 17, -3.85, -2.86, 0.37338, 19, 22.88, -14.63, 0, 3, 16, 10.32, -5.49, 0.81038, 17, -6.3, -0.64, 0.18962, 19, 25.18, -17.01, 0, 3, 16, 6.97, -5.16, 0.92768, 17, -8, 2.27, 0.07232, 19, 26.67, -20.02, 0, 3, 16, 4.16, -4.29, 0.98119, 17, -8.94, 5.05, 0.01881, 19, 27.42, -22.86, 0, 3, 16, 1.53, -3.24, 0.99742, 17, -9.63, 7.79, 0.00258, 19, 27.93, -25.65, 0, 3, 16, -0.47, 0.39, 0.98274, 17, -7.86, 11.55, 0.01726, 19, 25.91, -29.27, 0, 3, 16, 3.67, -0.63, 0.93821, 17, -6.26, 7.59, 0.06179, 19, 24.58, -25.22, 0, 3, 16, 7.85, -1.44, 0.81481, 17, -4.47, 3.74, 0.18519, 19, 23.05, -21.25, 0, 3, 16, 12.07, -0.92, 0.61728, 17, -1.58, 0.62, 0.38272, 19, 20.38, -17.95, 0, 4, 16, 15.3, 1.68, 0.38272, 17, 2.42, -0.48, 0.61054, 18, -0.85, -16.21, 0.00675, 19, 16.46, -16.58, 0, 4, 16, 17.75, 5.51, 0.18519, 17, 6.96, -0.23, 0.77549, 18, -0.73, -11.67, 0.03933, 19, 11.91, -16.53, 0, 4, 16, 18.88, 9.07, 0.06173, 17, 10.5, 0.95, 0.80909, 18, -1.61, -8.04, 0.12918, 19, 8.3, -17.47, 0, 4, 16, 20.94, 12.67, 0.01235, 17, 14.63, 1.38, 0.69455, 18, -1.7, -3.89, 0.2931, 19, 4.15, -17.63, 0, 3, 17, 19.19, 0.45, 0.48913, 18, -0.4, 0.58, 0.51086, 19, -0.33, -16.39, 1e-05, 3, 17, 22.35, -2.73, 0.2748, 18, 3.03, 3.47, 0.71284, 19, -3.28, -13.01, 0.01236, 3, 17, 24.16, -7.06, 0.11773, 18, 7.49, 4.92, 0.82052, 19, -4.79, -8.56, 0.06174, 3, 17, 23.47, -11.57, 0.03475, 18, 11.93, 3.86, 0.78005, 19, -3.8, -4.11, 0.1852, 4, 17, 20.28, -14.67, 0.0056, 18, 14.76, 0.43, 0.61168, 19, -0.41, -1.23, 0.38106, 20, -13.87, -13.41, 0.00166, 3, 18, 17.5, -3.89, 0.38271, 19, 3.87, 1.58, 0.5983, 20, -12.44, -8.5, 0.01899, 4, 18, 17.85, -8.54, 0.18518, 19, 8.52, 1.99, 0.73648, 20, -9.16, -5.18, 0.07833, 21, -12.67, -17.66, 1e-05, 4, 18, 17.44, -13.78, 0.06173, 19, 13.76, 1.66, 0.72651, 20, -4.95, -2.03, 0.20003, 21, -11.53, -12.53, 0.01174, 4, 18, 15.76, -18.23, 0.01235, 19, 18.23, 0.05, 0.57338, 20, -0.5, -0.36, 0.35506, 21, -9.22, -8.37, 0.05922, 3, 19, 22.53, -2.66, 0.35614, 20, 4.53, 0.36, 0.46505, 21, -5.88, -4.56, 0.17882, 3, 19, 25.34, -5.49, 0.16857, 20, 8.5, 0.02, 0.45898, 21, -2.64, -2.23, 0.37245, 3, 19, 27.55, -9.03, 0.05508, 20, 12.48, -1.24, 0.3399, 21, 1.21, -0.6, 0.60502, 3, 19, 28.93, -12.93, 0.01068, 20, 16.05, -3.32, 0.18487, 21, 5.27, 0.15, 0.80445, 2, 20, 19.47, -6.67, 0.06856, 21, 10.05, -0.18, 0.93144, 2, 20, 21.9, -9.64, 0.02292, 21, 13.82, -0.85, 0.97708], "hull": 46, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 0, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134], "width": 104, "height": 111}}, "s1": {"s1": {"type": "mesh", "uvs": [0.35744, 0.42231, 0.26265, 0.32016, 0.21236, 0.17927, 0.30327, 0.03661, 0.43868, 0, 0.63213, 0, 0.81203, 0.14228, 0.91648, 0.32192, 0.98612, 0.50685, 0.9958, 0.62661, 0.95517, 0.71995, 0.85071, 0.81329, 0.69209, 0.89079, 0.5238, 0.89959, 0.48704, 0.90488, 0.44836, 0.95947, 0.38839, 0.9947, 0.29554, 0.99294, 0.20655, 0.95595, 0.11757, 0.92073, 0.05567, 0.86085, 0.00731, 0.80801, 0.01698, 0.70586, 0.06534, 0.60724, 0.16593, 0.52798, 0.26072, 0.47339, 0.32455, 0.44344, 0.50158, 0.42562, 0.72914, 0.48189, 0.60834, 0.32075, 0.47068, 0.15961, 0.57463, 0.57141, 0.40607, 0.64303, 0.27122, 0.70697, 0.15884, 0.83742], "triangles": [16, 17, 15, 14, 15, 17, 33, 14, 17, 19, 34, 18, 17, 34, 33, 19, 20, 34, 34, 17, 18, 14, 33, 32, 20, 21, 34, 21, 22, 34, 34, 22, 33, 22, 23, 33, 23, 24, 33, 24, 25, 33, 32, 13, 14, 12, 13, 31, 13, 32, 31, 12, 31, 11, 31, 28, 11, 11, 28, 10, 9, 28, 8, 9, 10, 28, 33, 25, 32, 25, 26, 32, 26, 0, 32, 32, 27, 31, 32, 0, 27, 31, 27, 28, 28, 7, 8, 27, 29, 28, 28, 29, 7, 29, 27, 30, 30, 27, 0, 29, 6, 7, 0, 1, 30, 6, 30, 5, 6, 29, 30, 1, 2, 30, 2, 3, 30, 3, 4, 30, 30, 4, 5], "vertices": [3, 28, 0.52, -6.77, 0.42504, 29, 7.23, -5.58, 0.53968, 30, -4.42, -4.7, 0.03529, 3, 28, -2.58, -7.27, 0.79132, 29, 7.91, -8.65, 0.20862, 30, -4.46, -7.84, 6e-05, 2, 28, -5.98, -6.49, 0.90823, 29, 7.33, -12.09, 0.09177, 2, 28, -7.85, -3.09, 0.96854, 29, 4.05, -14.16, 0.03146, 2, 28, -7.1, -0.16, 0.99193, 29, 1.09, -13.58, 0.00807, 1, 28, -4.97, 3.39, 1, 2, 28, -0.11, 4.97, 0.98991, 29, -4.45, -6.91, 0.01009, 2, 28, 4.66, 4.71, 0.61347, 29, -4.48, -2.13, 0.38653, 2, 28, 9.16, 3.75, 0.00491, 29, -3.78, 2.41, 0.99509, 1, 29, -2.66, 5, 1, 1, 29, -0.88, 6.55, 1, 2, 29, 2.12, 7.46, 0.99067, 30, -6.41, 9.17, 0.00933, 2, 29, 5.98, 7.5, 0.90856, 30, -2.65, 8.33, 0.09144, 2, 29, 9.27, 6.02, 0.59884, 30, 0.22, 6.13, 0.40116, 2, 29, 10.02, 5.76, 0.43525, 30, 0.89, 5.71, 0.56475, 2, 29, 11.35, 6.52, 0.21605, 30, 2.36, 6.14, 0.78395, 2, 29, 12.87, 6.66, 0.11897, 30, 3.87, 5.93, 0.88103, 2, 29, 14.62, 5.7, 0.04175, 30, 5.35, 4.59, 0.95825, 2, 29, 15.9, 4.04, 0.00332, 30, 6.22, 2.69, 0.99668, 1, 30, 7.13, 0.82, 1, 1, 30, 7.21, -1.11, 1, 1, 30, 7.18, -2.73, 1, 2, 29, 16.78, -3.05, 0.00321, 30, 5.45, -4.41, 0.99679, 3, 28, 1.04, -14.37, 0.00075, 29, 14.79, -4.63, 0.06798, 30, 3.15, -5.49, 0.93127, 3, 28, 0.55, -11.56, 0.0222, 29, 12.01, -5.28, 0.32569, 30, 0.3, -5.5, 0.65211, 3, 28, 0.49, -9.16, 0.10452, 29, 9.62, -5.48, 0.60466, 30, -2.07, -5.14, 0.29082, 3, 28, 0.59, -7.63, 0.24644, 29, 8.08, -5.47, 0.64453, 30, -3.57, -4.78, 0.10903, 3, 28, 2.18, -4.16, 0.49521, 29, 4.53, -4.09, 0.50434, 30, -6.71, -2.62, 0.00046, 2, 28, 5.82, -0.66, 0.54079, 29, 0.82, -0.66, 0.45921, 2, 28, 1.24, -0.93, 0.95245, 29, 1.36, -5.21, 0.04755, 2, 28, -3.53, -1.51, 0.96622, 29, 2.22, -9.94, 0.03378, 2, 28, 5.92, -4.58, 0.01452, 29, 4.73, -0.32, 0.98548, 2, 28, 5.51, -8.54, 0.00504, 29, 8.71, -0.5, 0.99496, 2, 28, 5.32, -11.79, 0.00036, 30, 1.35, -0.84, 0.99964, 1, 30, 5.18, -0.09, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 0, 54, 54, 56, 56, 58, 58, 60, 56, 62, 62, 64, 64, 66, 66, 68], "width": 61, "height": 67}}, "s2": {"s2": {"type": "mesh", "uvs": [0.84932, 0, 0.96045, 0.07825, 1, 0.25068, 0.99338, 0.42716, 0.94811, 0.59553, 0.85756, 0.74768, 0.69086, 0.85722, 0.5324, 0.9201, 0.43979, 0.91605, 0.36364, 0.9911, 0.2628, 1, 0.1208, 0.94039, 0.02408, 0.84099, 0, 0.71725, 0.07759, 0.58945, 0.18666, 0.50831, 0.32866, 0.44948, 0.4542, 0.40079, 0.52828, 0.38253, 0.55504, 0.26893, 0.62295, 0.14113, 0.74848, 0.04579, 0.83069, 0.21964, 0.80201, 0.40949, 0.66268, 0.56299, 0.49876, 0.64782, 0.35943, 0.70841, 0.2201, 0.80132], "triangles": [25, 17, 18, 25, 18, 24, 16, 17, 25, 26, 16, 25, 15, 16, 26, 27, 15, 26, 14, 15, 27, 13, 14, 27, 12, 13, 27, 25, 24, 6, 8, 26, 25, 7, 8, 25, 27, 26, 8, 6, 7, 25, 11, 12, 27, 9, 27, 8, 10, 11, 27, 9, 10, 27, 22, 21, 0, 22, 0, 1, 20, 21, 22, 22, 1, 2, 22, 19, 20, 23, 22, 2, 23, 19, 22, 18, 19, 23, 3, 23, 2, 24, 18, 23, 4, 23, 3, 24, 23, 4, 5, 24, 4, 6, 24, 5], "vertices": [1, 31, -5.56, -1.81, 1, 2, 31, -5.15, 1.48, 0.99999, 32, -12.8, -8.13, 1e-05, 2, 31, -1.87, 4.33, 0.97714, 32, -11.93, -3.87, 0.02286, 2, 31, 2.02, 6.24, 0.82827, 32, -9.99, 0.01, 0.17173, 2, 31, 6.19, 7.24, 0.51655, 32, -7.28, 3.33, 0.48345, 2, 31, 10.52, 7.08, 0.35094, 32, -3.74, 5.83, 0.64906, 2, 31, 14.8, 4.8, 0.4958, 32, 1.05, 6.61, 0.5042, 2, 31, 17.98, 2.16, 0.3018, 32, 5.18, 6.44, 0.6982, 2, 31, 18.96, 0.13, 0.13544, 32, 7.18, 5.42, 0.86456, 2, 31, 21.46, -0.62, 0.04054, 32, 9.62, 6.34, 0.95946, 2, 31, 22.81, -2.66, 0.01444, 32, 11.94, 5.53, 0.98556, 2, 31, 23.15, -6.39, 0.0003, 32, 14.47, 2.77, 0.9997, 1, 32, 15.59, -0.42, 1, 1, 32, 14.87, -3.43, 1, 1, 32, 11.86, -5.51, 1, 2, 31, 13.04, -10.02, 0.00033, 32, 8.63, -6.24, 0.99967, 2, 31, 10.14, -7.67, 0.05987, 32, 4.9, -6.14, 0.94013, 2, 31, 7.64, -5.56, 0.37879, 32, 1.64, -5.97, 0.62121, 2, 31, 6.4, -4.2, 0.75227, 32, -0.18, -5.64, 0.24773, 2, 31, 3.63, -4.95, 0.98556, 32, -1.92, -7.91, 0.01444, 1, 31, 0.09, -4.99, 1, 1, 31, -3.42, -3.42, 1, 2, 31, -0.6, 0.36, 0.9991, 32, -8.51, -6.26, 0.0009, 2, 31, 3.84, 1.96, 0.9227, 32, -5.95, -2.3, 0.0773, 2, 31, 8.76, 0.77, 0.66033, 32, -1.32, -0.26, 0.33967, 1, 32, 3.16, 0, 1, 1, 32, 6.85, -0.03, 1, 2, 31, 19, -5.89, 0.00085, 32, 10.87, 0.65, 0.99915], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 69, "height": 70}}, "e1": {"e1": {"type": "mesh", "uvs": [0, 0.57659, 0.036, 0.39886, 0.11475, 0.24205, 0.2745, 0.08523, 0.47475, 0.00577, 0.70875, 0.00368, 0.87525, 0.09568, 0.97875, 0.33196, 1, 0.55986, 0.954, 0.75014, 0.8235, 0.91741, 0.612, 1, 0.40275, 1, 0.1575, 0.80032, 0.35312, 0.69639, 0.56842, 0.54017, 0.6805, 0.34557, 0.72474, 0.15646], "triangles": [2, 3, 15, 15, 3, 16, 16, 3, 4, 16, 17, 7, 17, 4, 5, 17, 16, 4, 17, 6, 7, 17, 5, 6, 15, 10, 11, 10, 15, 9, 9, 15, 8, 8, 16, 7, 16, 8, 15, 1, 2, 15, 13, 14, 12, 11, 12, 14, 11, 14, 15, 13, 0, 14, 15, 14, 1, 14, 0, 1], "vertices": [3, 22, -2.22, 10.87, 0.82794, 23, -7.1, 14.69, 0.01914, 24, -9.15, 21.32, 0.15292, 3, 22, 2.39, 15.15, 0.62423, 23, -1.22, 16.89, 0.05425, 24, -2.92, 20.52, 0.32152, 3, 22, 7.68, 18.02, 0.38604, 23, 4.76, 17.53, 0.06287, 24, 2.67, 18.3, 0.55109, 3, 22, 15.06, 19.34, 0.15201, 23, 12.1, 15.93, 0.02018, 24, 8.42, 13.47, 0.8278, 2, 22, 21.91, 17.71, 0.03656, 24, 11.55, 7.18, 0.96344, 3, 22, 28.03, 13.28, 0.00017, 23, 21.77, 5.39, 0.00021, 24, 12.07, -0.36, 0.99962, 2, 23, 21.81, -0.87, 0.10664, 24, 9.19, -5.92, 0.89336, 2, 23, 16.51, -7.97, 0.78799, 24, 1.19, -9.74, 0.21201, 3, 22, 24.1, -7.85, 0.01117, 23, 10.09, -12.66, 0.98861, 24, -6.67, -10.89, 0.00021, 2, 22, 18.98, -12.29, 0.13299, 23, 3.66, -14.8, 0.86701, 2, 22, 12.13, -14.46, 0.42951, 23, -3.5, -14.2, 0.57049, 2, 22, 4.93, -12.71, 0.79533, 23, -9.49, -9.83, 0.20467, 2, 22, -0.5, -8.69, 0.97914, 23, -12.98, -4.05, 0.02086, 2, 22, -2.75, 1.6, 0.99157, 24, -16.61, 15.78, 0.00843, 3, 22, 4.48, 0.75, 0.99184, 23, -4.77, 2.78, 0.00416, 24, -12.64, 9.69, 0.004, 1, 23, 3.47, -0.37, 1, 1, 24, 0.15, -0.15, 1, 2, 23, 17.49, 2.2, 0.01698, 24, 6.8, -1.19, 0.98302], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 92, "height": 99}}, "e2": {"e2": {"type": "mesh", "uvs": [1, 0.47132, 0.87705, 0.87708, 0.52621, 1, 0.33789, 1, 0.14183, 0.87708, 0.02059, 0.66463, 0.00253, 0.38136, 0.11346, 0.15934, 0.27598, 0.02345, 0.53137, 0, 0.7687, 0.08278, 0.89511, 0.24547, 0.96992, 0.38902, 0.27033, 0.24671, 0.31503, 0.44848, 0.40817, 0.62262, 0.56836, 0.78846, 0.73601, 0.8548], "triangles": [13, 11, 14, 11, 13, 10, 10, 13, 9, 9, 13, 8, 6, 7, 13, 13, 7, 8, 4, 5, 15, 16, 15, 0, 5, 14, 15, 5, 6, 14, 15, 12, 0, 15, 14, 12, 6, 13, 14, 14, 11, 12, 2, 17, 1, 2, 16, 17, 2, 3, 16, 3, 4, 16, 1, 17, 0, 4, 15, 16, 17, 16, 0], "vertices": [3, 25, 3.74, -14.38, 0.33253, 26, 1.9, -15.65, 0.46943, 27, -2.67, -17.28, 0.19804, 3, 25, -3.38, -2.82, 0.97872, 26, -10.18, -9.44, 0.01998, 27, -16.07, -15.05, 0.0013, 1, 25, -0.1, 5.99, 1, 2, 25, 3.16, 9.18, 0.99492, 26, -10.78, 4.2, 0.00508, 2, 25, 9.36, 9.62, 0.80511, 26, -5.71, 7.79, 0.19489, 2, 25, 16.31, 6.71, 0.18347, 26, 1.74, 8.9, 0.81653, 2, 26, 10.81, 7, 0.7749, 27, -1.08, 7, 0.2251, 2, 26, 17.15, 2.58, 0.02569, 27, 6.3, 4.72, 0.97431, 1, 27, 10.95, 1.04, 1, 2, 26, 19.64, -8.53, 0.0593, 27, 12.06, -5.1, 0.9407, 3, 25, 16.61, -19.54, 0.01047, 26, 15.58, -13.41, 0.22098, 27, 9.68, -10.99, 0.76855, 3, 25, 10.71, -17.88, 0.08309, 26, 9.67, -15.04, 0.41345, 27, 4.55, -14.34, 0.50346, 3, 25, 6.14, -15.79, 0.23168, 26, 4.68, -15.62, 0.48779, 27, -0.03, -16.41, 0.28053, 1, 27, 3.67, 0.77, 1, 1, 26, 6.78, 0.22, 1, 2, 26, 0.72, -0.53, 0.99931, 27, -8.4, -3.25, 0.00069, 1, 25, 3.99, 0.34, 1, 3, 25, -0.43, -0.95, 0.99408, 26, -8.61, -6.32, 0.00558, 27, -15.53, -11.59, 0.00035], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 16, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 69, "height": 93}}}}], "animations": {"idle": {"bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": -0.57, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.57}], "translate": [{"x": 0.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.92, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.26}]}, "bone5": {"rotate": [{"angle": -1.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.44}], "translate": [{"x": 0.48, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.48}]}, "bone6": {"translate": [{"x": 0.26, "y": -0.61, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 0.47, "y": -1.11, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": 0.26, "y": -0.61}]}, "bone7": {"translate": [{"x": 0.06, "y": 0.65, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 0.1, "y": 1.19, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": 0.06, "y": 0.65}]}, "bone16": {"rotate": [{"angle": 2, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 8.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 2}]}, "bone17": {"rotate": [{"angle": 5.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 8.24, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 5.9}]}, "bone18": {"rotate": [{"angle": 8.24, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 8.24}]}, "bone19": {"rotate": [{"angle": 5.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 8.24, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 5.9}]}, "bone20": {"rotate": [{"angle": 2.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 8.24, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.34}]}, "bone21": {"rotate": [{"angle": 0.09, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.9667, "angle": 8.24, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": 0.09}]}, "bone23": {"rotate": [{"angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.6}]}, "bone24": {"rotate": [{"angle": -5.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -5.44}]}, "bone25": {"rotate": [{"angle": -2.16, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.16}]}, "bone26": {"rotate": [{"angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.6}]}, "bone27": {"rotate": [{"angle": -5.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -5.44}]}, "bone28": {"rotate": [{"angle": -2.16, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.16}]}, "bone29": {"rotate": [{"angle": -3.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -3.99}]}, "bone30": {"rotate": [{"angle": -2.1, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.2, "angle": -3.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5333, "angle": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -2.1}]}, "bone32": {"rotate": [{"angle": -6.86, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.86}]}, "bone33": {"rotate": [{"angle": -4.91, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -6.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -4.91}]}}}, "train": {"slots": {"tou1": {"attachment": [{"time": 0.3, "name": "tou1"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 13.26, "curve": "stepped"}, {"time": 0.4, "angle": 13.26, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": 8.78, "y": 32.72, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3333, "x": -1.63, "y": -35.78, "curve": "stepped"}, {"time": 0.4, "x": -1.63, "y": -35.78, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone3": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 11.3, "curve": "stepped"}, {"time": 0.4, "angle": 11.3, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone4": {"rotate": [{"angle": 0.9, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "angle": 4.85, "curve": "stepped"}, {"time": 0.4333, "angle": 4.85, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 0.9}]}, "bone5": {"rotate": [{"angle": 2.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2333, "angle": 4.85, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.3667, "angle": -11.19, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.5333, "angle": 2.43}]}, "bone8": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -88.49, "curve": "stepped"}, {"time": 0.4, "angle": -88.49, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone9": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 45.06, "curve": "stepped"}, {"time": 0.4, "angle": 45.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone10": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 28.32, "curve": "stepped"}, {"time": 0.4, "angle": 28.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone11": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 0.72, "curve": "stepped"}, {"time": 0.4, "angle": 0.72, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone12": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -1.31, "curve": "stepped"}, {"time": 0.4, "angle": -1.31, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone13": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 7.93, "curve": "stepped"}, {"time": 0.4, "angle": 7.93, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone14": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -17.56, "curve": "stepped"}, {"time": 0.4, "angle": -17.56, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone15": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -4.19, "curve": "stepped"}, {"time": 0.4, "angle": -4.19, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 38.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone17": {"rotate": [{"angle": 1.9, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2333, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 14.6, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5333, "angle": 1.9}]}, "bone18": {"rotate": [{"angle": 5.37, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 14.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": 5.37}]}, "bone19": {"rotate": [{"angle": 9.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 14.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": 9.23}]}, "bone20": {"rotate": [{"angle": 12.7, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 14.6, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": 12.7}]}, "bone21": {"rotate": [{"angle": 14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 14.6}]}, "bone23": {"rotate": [{"angle": -0.93, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.1667, "angle": -9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.5333, "angle": -0.93}]}, "bone24": {"rotate": [{"angle": 1.48, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2333, "angle": -9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 15.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": 4.42, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.5333, "angle": 1.48}]}, "bone25": {"rotate": [{"angle": 7.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3, "angle": -9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 15.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 11.16, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.5333, "angle": 7.81}]}, "bone26": {"rotate": [{"angle": -0.93, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.1667, "angle": -9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.5333, "angle": -0.93}]}, "bone27": {"rotate": [{"angle": 1.48, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2333, "angle": -9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 15.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": 4.42, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.5333, "angle": 1.48}]}, "bone28": {"rotate": [{"angle": 7.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3, "angle": -9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 15.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 11.16, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.5333, "angle": 7.81}]}, "bone29": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 31.29, "curve": "stepped"}, {"time": 0.2333, "angle": 31.29, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3333, "angle": -131.19, "curve": "stepped"}, {"time": 0.4, "angle": -131.19, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}], "translate": [{"time": 0.2333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3333, "x": -7.62, "y": 6.41, "curve": "stepped"}, {"time": 0.4, "x": -7.62, "y": 6.41, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}], "scale": [{"time": 0.2333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3333, "x": 1.157, "y": 1.157, "curve": "stepped"}, {"time": 0.4, "x": 1.157, "y": 1.157, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone30": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -27.7, "curve": "stepped"}, {"time": 0.2333, "angle": -27.7, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3333, "angle": 71.18, "curve": "stepped"}, {"time": 0.4, "angle": 71.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone31": {"rotate": [{"time": 0.2333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3333, "angle": -23.64, "curve": "stepped"}, {"time": 0.4, "angle": -23.64, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone32": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 36.06, "curve": "stepped"}, {"time": 0.3, "angle": 36.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3333, "angle": 82.43, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone33": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -79.66, "curve": "stepped"}, {"time": 0.4, "angle": -79.66, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333}]}, "bone34": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 45.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -28.21, "curve": "stepped"}, {"time": 0.4, "y": -28.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}}}}}