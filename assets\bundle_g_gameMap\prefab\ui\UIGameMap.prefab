[{"__type__": "cc.Prefab", "_name": "UIGameMap", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIGameMap", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 606}], "_active": true, "_components": [{"__id__": 621}, {"__id__": 623}, {"__id__": 625}], "_prefab": {"__id__": 627}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_map", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}, {"__id__": 19}, {"__id__": 27}, {"__id__": 33}, {"__id__": 289}, {"__id__": 295}, {"__id__": 356}, {"__id__": 413}, {"__id__": 474}, {"__id__": 529}], "_active": true, "_components": [{"__id__": 603}], "_prefab": {"__id__": 605}, "_lpos": {"__type__": "cc.Vec3", "x": 1375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": -1000, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 1500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0wSORsshMabC9l6WDyzM+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "315a9984-cbfa-4748-8ad9-b5c6575d854b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52EuV0H7BNorgQ9PzsB7f+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_alignFlags": 5, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1024, "_originalHeight": 1024, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3euJdtMcpAIIHN3V69GdU2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f08+9RowVJdbqkHpH7hqDE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": 500, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 13}, "_contentSize": {"__type__": "cc.Size", "width": 1500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f39FbnC2RKR6DvrtV+CGpR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "af055776-5c4a-428d-9c43-2ae999d26d69@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28XGxOUQ5O9oo+ZzGzLEua"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 17}, "_alignFlags": 5, "_target": null, "_left": 1024, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1024, "_originalHeight": 1024, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "470qEgI+VM9JZg0KwxzuL+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4fZmDT8spEVJCRkQLNozsr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 1500, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "demqM439dO7Lua5f5T/mQm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 23}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6d8ec3c9-ca02-47b9-869a-c84e84b4341f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39BS1RPq9N2o20b93ofh+n"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 25}, "_alignFlags": 5, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 1024, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cF+tngOJCa4eWHx4f92VI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2fjG8vL0lLNK87VnsUrCIk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 29}, "_contentSize": {"__type__": "cc.Size", "width": 3500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70Op3GNxdGbreoN+a/syWp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 31}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7crPgV45JdYdFtxZHxHka"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22TRzy01JBfZ7Q5M3UcIZ6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_ground", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 34}, {"__id__": 54}, {"__id__": 119}, {"__id__": 132}, {"__id__": 147}, {"__id__": 168}, {"__id__": 185}, {"__id__": 194}, {"__id__": 203}, {"__id__": 215}, {"__id__": 228}, {"__id__": 236}, {"__id__": 250}, {"__id__": 267}], "_active": true, "_components": [{"__id__": 284}, {"__id__": 286}], "_prefab": {"__id__": 288}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 35}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 34}, "asset": {"__uuid__": "a0dd99b4-1a35-4f41-a8ff-bf4f5c0140ff", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 36}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "f2OFbV4fBDLbBg4Nn1sZo+", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 37}, {"__id__": 39}, {"__id__": 40}, {"__id__": 41}, {"__id__": 42}, {"__id__": 44}, {"__id__": 46}, {"__id__": 48}, {"__id__": 50}, {"__id__": 52}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_name"], "value": "trim_1211"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1077.87, "y": 487.297, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 43}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["f0FyHler5JfLh5EdXCc0JB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d7OrXP+SVIIp/7dOtG24f9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["defaultAnimation"], "value": "xiao_bian_da"}, {"__type__": "cc.TargetInfo", "localID": ["b2liqPevpFd7HzYOj7XDzq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["7a6+t0qLBMHLpxeKAZuUBa"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["db4uJcpylPoKf7YFjNPIwF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 53}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["b7kIwKQrFF4bkL5MYfIQ4A"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 55}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 54}, "asset": {"__uuid__": "b82e0474-073d-4310-b497-8d171eda1672", "__expectedType__": "cc.Prefab"}, "fileId": "74DwmIfmdNjosZNVKfecPn", "instance": {"__id__": 56}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "12qUromcdO2q69YkF4UTod", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 57}], "propertyOverrides": [{"__id__": 92}, {"__id__": 94}, {"__id__": 96}, {"__id__": 98}, {"__id__": 100}, {"__id__": 102}, {"__id__": 104}, {"__id__": 106}, {"__id__": 108}, {"__id__": 110}, {"__id__": 112}, {"__id__": 114}, {"__id__": 116}, {"__id__": 118}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 58}, "components": [{"__id__": 59}]}, {"__type__": "cc.TargetInfo", "localID": ["7e5hw1WcZE7KDSqsAACoFZ"]}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 54}}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 91}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "71c9dc7a-051c-48ce-a9ca-b3cd6cffb6ae@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "71a3EFUWpP0Jsxy/LwbeVH"}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 61}, "_children": [{"__id__": 74}, {"__id__": 80}], "_active": true, "_components": [{"__id__": 86}, {"__id__": 88}, {"__id__": 59}], "_prefab": {"__id__": 90}, "_lpos": {"__type__": "cc.Vec3", "x": -2.125, "y": -99.364, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d6aJGYfxNJh7PSglgAfPN+"}, {"__type__": "cc.Node", "_name": "btn_build", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 54}, "_children": [{"__id__": 62}, {"__id__": 68}, {"__id__": 60}], "_active": true, "_components": [{"__id__": 69}, {"__id__": 71}], "_prefab": {"__id__": 73}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999991, "y": 0.9999999999999991, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "05AlTgcA1KCIlO32dv7iOg"}, {"__type__": "cc.Node", "_name": "build", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 61}, "_children": [], "_active": true, "_components": [{"__id__": 63}, {"__id__": 65}], "_prefab": {"__id__": 67}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15.999999999999886, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.999999999999999, "y": 0.999999999999999, "z": 0.999999999999999}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1boCTX3gRFLZ/JmVJk65Wb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 64}, "_contentSize": {"__type__": "cc.Size", "width": 107, "height": 101}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a4D1hEVQFHDoBdpd6ELGLU"}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bX3+RGLtMpIa7C0SGyWFa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 66}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@f2df0", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": "20ZpCFt7FA76rbsIGePAC0"}, {"__type__": "cc.CompPrefabInfo", "fileId": "67VOdZHqJKPquQlEW5QcLP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1e000d55-1a3e-4a7f-84f7-2aa91c7f0c0e", "__expectedType__": "cc.Prefab"}, "fileId": "27Fg/lcZBFaJx32Z5nNI9+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1fAOP3HRxNsLGWIcRlwAI9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 70}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 129}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "33fx3eDYdC+6b9NkeRxCyK"}, {"__type__": "cc.CompPrefabInfo", "fileId": "aep7VTcrRFgbuENhykF2II"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 72}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@f95d4", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": "163wFcdEVNyK38LLcD/9iH"}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0/6QmtOhM3bcnuiCUGI4z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1e000d55-1a3e-4a7f-84f7-2aa91c7f0c0e", "__expectedType__": "cc.Prefab"}, "fileId": "55YDNyaAZPj5PYWxehin2O", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "item_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 75}, {"__id__": 77}], "_prefab": {"__id__": 79}, "_lpos": {"__type__": "cc.Vec3", "x": -31.868217905297417, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.35, "y": 0.35, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "53gLO8zbRPQpTeihdQ76YW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 76}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "26ToiTiQNAAbWd2HGVQlQL"}, {"__type__": "cc.CompPrefabInfo", "fileId": "107JIT+qtErI2rLZTQmAYv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 78}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5ddc7c6-0144-4433-8eb2-53f32cd574f2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "912iYtQWVPPobsIVItt0l6"}, {"__type__": "cc.CompPrefabInfo", "fileId": "430fO6JpBARIVep58RmuAd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1e000d55-1a3e-4a7f-84f7-2aa91c7f0c0e", "__expectedType__": "cc.Prefab"}, "fileId": "c6pSdVL/tPu6O7qk/8tk6W", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_qiyun", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 81}, {"__id__": 83}], "_prefab": {"__id__": 85}, "_lpos": {"__type__": "cc.Vec3", "x": 21.949999999999996, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bbmR1uiHlMqYmngIQQUXRk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": {"__id__": 82}, "_contentSize": {"__type__": "cc.Size", "width": 59.73643581059483, "height": 29.719999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "97Roz5gMVMMYCcSgx9N4Xv"}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2i3SiOlJM9ZUq+Xejda6w"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": {"__id__": 84}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 148, "g": 148, "b": 148, "a": 255}, "_string": "16/62", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21.384999999999998, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 255, "g": 252, "b": 252, "a": 255}, "_outlineWidth": 1, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "f0+GAgM3tBkpz1ffeJPkXG"}, {"__type__": "cc.CompPrefabInfo", "fileId": "09m9CbmF5G5bd3MaHVHvZl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1e000d55-1a3e-4a7f-84f7-2aa91c7f0c0e", "__expectedType__": "cc.Prefab"}, "fileId": "09q+26JN1PJYw8YIe6/ajW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 87}, "_contentSize": {"__type__": "cc.Size", "width": 143.63643581059483, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f9khs/TkRGxYA3pHZi7ct/"}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dCDRN+iRAVas+oXMNem/j"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 89}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 20, "_paddingRight": 20, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 4, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": "838uwAyOhFW4pbXCotK6Fm"}, {"__type__": "cc.CompPrefabInfo", "fileId": "71FuXViX9H852MCg5ZmS8S"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1e000d55-1a3e-4a7f-84f7-2aa91c7f0c0e", "__expectedType__": "cc.Prefab"}, "fileId": "7e5hw1WcZE7KDSqsAACoFZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "1059o0uIxFuotO2i10XsYk"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 93}, "propertyPath": ["_name"], "value": "city_102"}, {"__type__": "cc.TargetInfo", "localID": ["74DwmIfmdNjosZNVKfecPn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 95}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1495.317, "y": 330.496, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["74DwmIfmdNjosZNVKfecPn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["74DwmIfmdNjosZNVKfecPn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["74DwmIfmdNjosZNVKfecPn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 101}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -24.037, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["fc7TiVCBxHgqYB6/Q8Hxga"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["defaultAnimation"], "value": "a0-1"}, {"__type__": "cc.TargetInfo", "localID": ["9cIbkDmFdNC6/Oop6ko0FF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_string"], "value": "0"}, {"__type__": "cc.TargetInfo", "localID": ["3bkiZxbMpLlqW/9/LzTpnF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 107}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 15, "height": 25}}, {"__type__": "cc.TargetInfo", "localID": ["abSujoMwNKLZL6dO69sAFZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 109}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -10, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3c8W+QEAZEz7xxtuBRhjW1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 82, "height": 36}}, {"__type__": "cc.TargetInfo", "localID": ["eakNW1QlhHOZ8VyC2m97lu"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 113}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 30, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["adWXovzrRA5KT/SGDbFC9V"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 115}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79.9, "height": 37}}, {"__type__": "cc.TargetInfo", "localID": ["fbOCpJNOFIrazHeqJoKzgm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 117}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -20.000000000000004, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["40OCDuoCBCYINplBV2QNCr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 120}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "592ab18e-2b33-4906-bb7a-db140671f370", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 121}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "beNzp4Hy5DIaoiBiwIb8tj", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 122}, {"__id__": 124}, {"__id__": 125}, {"__id__": 126}, {"__id__": 127}, {"__id__": 129}, {"__id__": 131}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_name"], "value": "trim_1212"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1503.111, "y": 213.454, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 128}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d7OrXP+SVIIp/7dOtG24f9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 130}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["cbCq+OxBpHLr5E9Lg5Xngg"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 133}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 132}, "asset": {"__uuid__": "2646297f-a64b-43ab-8bd3-3d393a37e13a", "__expectedType__": "cc.Prefab"}, "fileId": "6dGnBgPq1AJ6cEpJm/jcwJ", "instance": {"__id__": 134}, "targetOverrides": []}, {"__type__": "cc.PrefabInstance", "fileId": "caFAjIE+VJ3ZosHc78VQqM", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 135}, {"__id__": 137}, {"__id__": 139}, {"__id__": 141}, {"__id__": 143}, {"__id__": 145}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 136}, "propertyPath": ["_name"], "value": "city_100"}, {"__type__": "cc.TargetInfo", "localID": ["6dGnBgPq1AJ6cEpJm/jcwJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 138}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1326.006, "y": -43.164, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["6dGnBgPq1AJ6cEpJm/jcwJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 140}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["6dGnBgPq1AJ6cEpJm/jcwJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["6dGnBgPq1AJ6cEpJm/jcwJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 144}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.984, "y": 0.984, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["732xLl8cBDXq/NCIXUHOO7"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 146}, "propertyPath": ["defaultAnimation"], "value": "a01-1"}, {"__type__": "cc.TargetInfo", "localID": ["e37ajAFYtIarzFRPhFNjU5"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 148}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 147}, "asset": {"__uuid__": "60af63f1-95f1-4454-8e11-e3f235a2b579", "__expectedType__": "cc.Prefab"}, "fileId": "81bQXHlYdOxLWMH5gurIiq", "instance": {"__id__": 149}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "003LaI5OtN2LxNliswb4Oz", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 150}, {"__id__": 152}, {"__id__": 154}, {"__id__": 156}, {"__id__": 158}, {"__id__": 160}, {"__id__": 162}, {"__id__": 164}, {"__id__": 166}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_name"], "value": "city_101"}, {"__type__": "cc.TargetInfo", "localID": ["81bQXHlYdOxLWMH5gurIiq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 153}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1026.406, "y": 232.03, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["81bQXHlYdOxLWMH5gurIiq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["81bQXHlYdOxLWMH5gurIiq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 157}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["81bQXHlYdOxLWMH5gurIiq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["defaultAnimation"], "value": "a06-1"}, {"__type__": "cc.TargetInfo", "localID": ["48kPw3lZpK5rQx6GW1EnxA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_string"], "value": "0"}, {"__type__": "cc.TargetInfo", "localID": ["3dvygkejlAGrbMmUP/9qVK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 163}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 30, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["6dJrkNPllHx6oBztn4/Zz4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79.9, "height": 37}}, {"__type__": "cc.TargetInfo", "localID": ["d7gkVmqWJFoaFpvFijdkPC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 167}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -20.000000000000004, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c9wy6Z68VF74ogW7O/jm2E"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 169}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 168}, "asset": {"__uuid__": "2a0a3eb8-8305-4fee-a5bc-fb3703361082", "__expectedType__": "cc.Prefab"}, "fileId": "2dauI56adBYroEZGpldbAo", "instance": {"__id__": 170}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "61hYCGTkRIeqk9nyx/J945", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 171}, {"__id__": 173}, {"__id__": 175}, {"__id__": 177}, {"__id__": 179}, {"__id__": 181}, {"__id__": 183}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 172}, "propertyPath": ["_name"], "value": "city_103"}, {"__type__": "cc.TargetInfo", "localID": ["2dauI56adBYroEZGpldbAo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1010.99, "y": -369.763, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["2dauI56adBYroEZGpldbAo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 176}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["2dauI56adBYroEZGpldbAo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 178}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["2dauI56adBYroEZGpldbAo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 180}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 30, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["bdgx72hvpEMbRPV8Xmx/CM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79.9, "height": 37}}, {"__type__": "cc.TargetInfo", "localID": ["e6Il33SlhAB5mnStD/5uiQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 184}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -20.000000000000004, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ab2o2g8ZxPcK8Lzf+eR2Om"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 186}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 185}, "asset": {"__uuid__": "a89f581a-6ba6-4a0b-86e2-cb8812ac2fc0", "__expectedType__": "cc.Prefab"}, "fileId": "7cnmPsowFJoIOl1mhZDtsr", "instance": {"__id__": 187}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "3chAC/0olND43TMDf1Dgdp", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 188}, {"__id__": 190}, {"__id__": 191}, {"__id__": 192}, {"__id__": 193}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["_name"], "value": "ren_zu_he_hua"}, {"__type__": "cc.TargetInfo", "localID": ["7cnmPsowFJoIOl1mhZDtsr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1569.805, "y": -559.315, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 195}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 194}, "asset": {"__uuid__": "cafc073e-**************-da8af6d1909c", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 196}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "f2dE7bU0JIcJFVAbj1U/c6", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 197}, {"__id__": 199}, {"__id__": 200}, {"__id__": 201}, {"__id__": 202}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 198}, "propertyPath": ["_name"], "value": "trim_101"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 198}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1246.435, "y": -509.693, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 198}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 198}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 198}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 204}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 203}, "asset": {"__uuid__": "ca293d4a-f2e1-4600-ac82-43dbfe973abc", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 205}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "63z8wsGtlBEJAe0awIr5xs", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 206}, {"__id__": 208}, {"__id__": 209}, {"__id__": 210}, {"__id__": 211}, {"__id__": 213}, {"__id__": 214}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_name"], "value": "trim_103"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1064.449, "y": 32.238, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 212}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["665YYeHjNDlrdUMubMcBN4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 212}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 25.137, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 216}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 215}, "asset": {"__uuid__": "a3800643-72aa-4567-bb27-aab2f282251d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 217}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d5W1sDi8FL/JjrWDkUT+ni", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 218}, {"__id__": 220}, {"__id__": 221}, {"__id__": 222}, {"__id__": 223}, {"__id__": 225}, {"__id__": 226}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 219}, "propertyPath": ["_name"], "value": "trim_104"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 219}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1668.975, "y": 26.126, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 219}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 219}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 224}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["46WagW/ltKOYV8rXn+17sw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 219}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 227}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 175, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["55bJ7IRdtBmLYHlc7wmCzf"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 229}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 228}, "asset": {"__uuid__": "a71df1dd-f010-419b-9cfb-d6de82c3e880", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 230}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "f30bR8hrtJcrK28k2CvVZ1", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 231}, {"__id__": 233}, {"__id__": 234}, {"__id__": 235}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_name"], "value": "trim_105"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1189.507, "y": -237.331, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 237}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 236}, "asset": {"__uuid__": "3ccde70f-1c45-41f0-9b90-73ce0769b2cf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 238}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "93qtEqoD5CrrxO8yIdIPBY", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 239}, {"__id__": 241}, {"__id__": 243}, {"__id__": 245}, {"__id__": 247}, {"__id__": 249}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 240}, "propertyPath": ["_name"], "value": "trim_110"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 242}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1718.621, "y": -298.381, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 244}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 246}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 248}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a03zxhQddCs56oRnGrOiuv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 246}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 251}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 250}, "asset": {"__uuid__": "eed1b9e1-d694-4961-8a45-fa8c8d3a59f7", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 252}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e0epxk3qhLybu/9zvvpPV1", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 253}, {"__id__": 255}, {"__id__": 256}, {"__id__": 257}, {"__id__": 258}, {"__id__": 260}, {"__id__": 262}, {"__id__": 264}, {"__id__": 266}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 254}, "propertyPath": ["_name"], "value": "trim_1214"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 254}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1247.647, "y": 332.165, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 254}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 254}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 259}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d7OrXP+SVIIp/7dOtG24f9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 261}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["b7kIwKQrFF4bkL5MYfIQ4A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 263}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["f0FyHler5JfLh5EdXCc0JB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 265}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["c3/5zTctlKyq9Ub7i1FBho"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 254}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 268}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "9ebb7bba-f106-4e85-870a-5e66382e85a9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 269}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a1mpmwJR5FMJ+bC+xcbO6y", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 270}, {"__id__": 272}, {"__id__": 273}, {"__id__": 274}, {"__id__": 275}, {"__id__": 277}, {"__id__": 279}, {"__id__": 281}, {"__id__": 283}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 271}, "propertyPath": ["_name"], "value": "trim_1215"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 271}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1410, "y": -267, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 271}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 271}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 276}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["f0FyHler5JfLh5EdXCc0JB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 278}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["b7kIwKQrFF4bkL5MYfIQ4A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 280}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["55bJ7IRdtBmLYHlc7wmCzf"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 282}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["af52GNnKtE4q1VD5sfpqTC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 271}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 285}, "_contentSize": {"__type__": "cc.Size", "width": 3500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8dxU6feJDNL0lFRjmK7hE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 287}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddA984/VJMk5ZvdOBFMGbl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9bzLrrppBZJT41lc29P8b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_air", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 290}, {"__id__": 292}], "_prefab": {"__id__": 294}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": {"__id__": 291}, "_contentSize": {"__type__": "cc.Size", "width": 3500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41G24Z5wdIY4Y0iqjE/ABc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": {"__id__": 293}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93MjUzaYRJfrPCmHYxDO3E"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74enCSFxNFNos7luhgxb/E", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_pupil", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 296}, {"__id__": 302}, {"__id__": 308}, {"__id__": 320}, {"__id__": 329}, {"__id__": 335}], "_active": true, "_components": [{"__id__": 351}, {"__id__": 353}], "_prefab": {"__id__": 355}, "_lpos": {"__type__": "cc.Vec3", "x": -1567.039, "y": -187.895, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999998, "y": 0.9999999999999998, "z": 0.26427061310782235}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon_city_img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 297}, {"__id__": 299}], "_prefab": {"__id__": 301}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10.967, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -0.688, "y": 0.6879999999999996, "z": 0.18181818181818177}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 298}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60TJZRUexPg6C92Z7paobJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": false, "__prefab": {"__id__": 300}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7c3a37a2-24e8-4af7-9932-93ce85d0abb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94CflJCfxOFLDLKxJ7bP6j"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0pJsHDnNKHp1Igiu5NdO4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spine_dizi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 303}, {"__id__": 305}], "_prefab": {"__id__": 307}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -44.466, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 302}, "_enabled": true, "__prefab": {"__id__": 304}, "_contentSize": {"__type__": "cc.Size", "width": 95.4000015258789, "height": 112.80999755859375}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.532494742395577, "y": 0.04077652694073784}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94Ydo08v5HOrrKw8TyioTF"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 302}, "_enabled": true, "__prefab": {"__id__": 306}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "886c27a3-6750-4b8a-addd-932db556e949", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "a01-1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ccocnoq9Nd57xTWpT4BFk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48hp99VEFGFLH9Xs6F75c2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "name_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [{"__id__": 309}], "_active": true, "_components": [{"__id__": 315}, {"__id__": 317}], "_prefab": {"__id__": 319}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -48.387, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "city_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 308}, "_children": [], "_active": true, "_components": [{"__id__": 310}, {"__id__": 312}], "_prefab": {"__id__": 314}, "_lpos": {"__type__": "cc.Vec3", "x": -2.842170943040401e-14, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.8108108108108105}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 309}, "_enabled": true, "__prefab": {"__id__": 311}, "_contentSize": {"__type__": "cc.Size", "width": 49.34999084472656, "height": 26.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c1bTP7o9Jk5LRz36c9L52"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 309}, "_enabled": true, "__prefab": {"__id__": 313}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 249, "b": 235, "a": 255}, "_string": "弟 子", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58raE+VBtKhIaWgnSKb/R0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4n2vfvBJNL5ugVjM8nukQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": {"__id__": 316}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83Mg5cCIdMZKVw62vTkAAg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": {"__id__": 318}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "53c46891-998e-4a0c-8ccd-************@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdIYoh8ppH2J8f6XO2UAEV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4BG8FT35Asa9CBC5Pe910", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 295}, "_prefab": {"__id__": 321}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 320}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 322}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8cuhqCjQtL/qy/P3UrZNhc", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 323}, {"__id__": 325}, {"__id__": 326}, {"__id__": 327}, {"__id__": 328}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 324}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 324}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 39.743, "y": 48.615, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 324}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 324}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 324}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.Node", "_name": "spine_jianzaod<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": false, "_components": [{"__id__": 330}, {"__id__": 332}], "_prefab": {"__id__": 334}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": {"__id__": 331}, "_contentSize": {"__type__": "cc.Size", "width": 224.8300018310547, "height": 672.8099975585938}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1.4115109350129516, "y": 0.4200740267064756}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcf4AM+U5H7LfPy09kaXw1"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": {"__id__": 333}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "97a427a8-5738-4e9c-9977-3758b6246877", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<PERSON><PERSON><PERSON>", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82W/ztJGhFVK9aajH/ACFt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33YQTQ/8pDtKtscKRT9YOc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_build", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [{"__id__": 336}, {"__id__": 342}], "_active": true, "_components": [{"__id__": 348}], "_prefab": {"__id__": 350}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 30.007, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999991, "y": 0.9999999999999991, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 335}, "_children": [], "_active": true, "_components": [{"__id__": 337}, {"__id__": 339}], "_prefab": {"__id__": 341}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": {"__id__": 338}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "881a0/47VObZjmwlz7rc87"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": {"__id__": 340}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7849d6ab-2361-4255-aed4-1eecfc7e9c8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbhEDjJxVKB4EtzyZazqQs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1ztO0P0NDhLqChqC1z957", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 335}, "_children": [], "_active": false, "_components": [{"__id__": 343}, {"__id__": 345}], "_prefab": {"__id__": 347}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 342}, "_enabled": true, "__prefab": {"__id__": 344}, "_contentSize": {"__type__": "cc.Size", "width": 107, "height": 101}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "202UdYtvVAZrBKBXi9UJoy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 342}, "_enabled": true, "__prefab": {"__id__": 346}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@f2df0", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f26glBWS9OgpCTNgWoB7C8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34eAujBN1I5KarBq/8ibE1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": {"__id__": 349}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eR4oSdklMHqrUV9Rohmqn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7uPeLCkxDmZbjuR0M1bbu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 352}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0JNhvKrJKiZc0OGpeMCWO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 354}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 295}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eCNwedIZNJ60uH+U5hc5R"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53xMkaWCtL7JORS1GVBBPU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_farm", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 357}, {"__id__": 363}, {"__id__": 375}, {"__id__": 393}, {"__id__": 399}], "_active": true, "_components": [{"__id__": 408}, {"__id__": 410}], "_prefab": {"__id__": 412}, "_lpos": {"__type__": "cc.Vec3", "x": -1054.438, "y": -99.032, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spine_farm", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [], "_active": true, "_components": [{"__id__": 358}, {"__id__": 360}], "_prefab": {"__id__": 362}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 359}, "_contentSize": {"__type__": "cc.Size", "width": 316.05999755859375, "height": 300.6600036621094}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.502214759709072, "y": 0.22350826890750503}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8p4zQfCJKP5jcekgxivAn"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 361}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "cb880221-0a4e-4595-8df7-c8ef357ac2a0", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "a0-1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56u+dxh7hBZrmCtmgUrNlU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e1ahwe0qJGp4mJT6crhfRd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "name_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [{"__id__": 364}], "_active": true, "_components": [{"__id__": 370}, {"__id__": 372}], "_prefab": {"__id__": 374}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -26.881, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999999, "y": 0.9999999999999999, "z": 0.9999999999999999}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "city_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 363}, "_children": [], "_active": true, "_components": [{"__id__": 365}, {"__id__": 367}], "_prefab": {"__id__": 369}, "_lpos": {"__type__": "cc.Vec3", "x": -2.842170943040401e-14, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.8108108108108105}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 366}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 26.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7hFR/G5pOMYfLF3GtnZgA"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 368}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 249, "b": 235, "a": 255}, "_string": "福地洞天", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89GPJTis1J05EVMIiu3QBy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07Ynbcr2lKa4qhemdsxzJg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 371}, "_contentSize": {"__type__": "cc.Size", "width": 138, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10uY3kDk9I8pqLzcG8ocy7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 373}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "53c46891-998e-4a0c-8ccd-************@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85uVSrN0hNxYdU9YJZVNdm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26MIWGoytPsJ0ox9CJLjCC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_build", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [{"__id__": 376}, {"__id__": 382}], "_active": true, "_components": [{"__id__": 388}, {"__id__": 390}], "_prefab": {"__id__": 392}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 30.007, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 375}, "_children": [], "_active": true, "_components": [{"__id__": 377}, {"__id__": 379}], "_prefab": {"__id__": 381}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": {"__id__": 378}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edcuLFtedCh4hiEZfuDPvq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": {"__id__": 380}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7849d6ab-2361-4255-aed4-1eecfc7e9c8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4R2vsxTRLGLEIprWQYMm0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "af8NFA/JlKOIhdwC6o/+ek", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 375}, "_children": [], "_active": false, "_components": [{"__id__": 383}, {"__id__": 385}], "_prefab": {"__id__": 387}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 384}, "_contentSize": {"__type__": "cc.Size", "width": 107, "height": 101}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75Tm8tESpF6JTzeJhc3pzq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 386}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@f2df0", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8yGXh8mVPgK9hnkiat5Gs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3mC1Re0JAWpd5zCBPOpBC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 389}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 129}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01O9/pNshI87kFnk2jUhKy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": false, "__prefab": {"__id__": 391}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@f95d4", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deAujlw3xIm6n5V7fGSyRu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8wAG591pMUo3uz0gkSPD8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spine_jianzaod<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [], "_active": false, "_components": [{"__id__": 394}, {"__id__": 396}], "_prefab": {"__id__": 398}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 393}, "_enabled": true, "__prefab": {"__id__": 395}, "_contentSize": {"__type__": "cc.Size", "width": 224.8300018310547, "height": 672.8099975585938}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1.4115109350129516, "y": 0.4200740267064756}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afezhY3FtIAawNHIQarl3I"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 393}, "_enabled": true, "__prefab": {"__id__": 397}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "97a427a8-5738-4e9c-9977-3758b6246877", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<PERSON><PERSON><PERSON>", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12kv2MXKdPDbm161xZdgX9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5aTGzI2BxE85HirCylRZV+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 356}, "_prefab": {"__id__": 400}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 399}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 401}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "82O7e+xe1MorXDjMiQxYQg", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 402}, {"__id__": 404}, {"__id__": 405}, {"__id__": 406}, {"__id__": 407}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 403}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 403}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 39.743, "y": 48.615, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 403}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 403}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 403}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 356}, "_enabled": true, "__prefab": {"__id__": 409}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04dCczPT5L77CvKgrZZnn6"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 356}, "_enabled": true, "__prefab": {"__id__": 411}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35vfSaOixHNJIaevpDDwW6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d0rfbHf+FIF6iPX/e9BITr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_pet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 414}, {"__id__": 420}, {"__id__": 426}, {"__id__": 438}, {"__id__": 447}, {"__id__": 453}], "_active": true, "_components": [{"__id__": 469}, {"__id__": 471}], "_prefab": {"__id__": 473}, "_lpos": {"__type__": "cc.Vec3", "x": -1313.957, "y": -246.956, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999998, "y": 0.9999999999999998, "z": 0.26427061310782235}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon_city_img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 413}, "_children": [], "_active": true, "_components": [{"__id__": 415}, {"__id__": 417}], "_prefab": {"__id__": 419}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10.967, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -0.688, "y": 0.6879999999999996, "z": 0.18181818181818177}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 414}, "_enabled": true, "__prefab": {"__id__": 416}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dQub7sfxOxbn3twLOLnDv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 414}, "_enabled": false, "__prefab": {"__id__": 418}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7c3a37a2-24e8-4af7-9932-93ce85d0abb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "db7JjiJPBL+qBDOcq5NaK+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c74nqvyIBGuo046t/KwKgo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spine_pet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 413}, "_children": [], "_active": true, "_components": [{"__id__": 421}, {"__id__": 423}], "_prefab": {"__id__": 425}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -44.466, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 420}, "_enabled": true, "__prefab": {"__id__": 422}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1625}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.33803076171875}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89g5iZp6ZPV47uevheDCC1"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 420}, "_enabled": true, "__prefab": {"__id__": 424}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "4b14e51d-7c5d-489d-9b48-45da13de6a31", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "lingshou_appear", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaC8mMpAxFoK82E6mZprKU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6eL1Alu49HeqOwQowHbvx3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "name_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 413}, "_children": [{"__id__": 427}], "_active": true, "_components": [{"__id__": 433}, {"__id__": 435}], "_prefab": {"__id__": 437}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -48.387, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "city_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [], "_active": true, "_components": [{"__id__": 428}, {"__id__": 430}], "_prefab": {"__id__": 432}, "_lpos": {"__type__": "cc.Vec3", "x": -2.842170943040401e-14, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.8108108108108105}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 429}, "_contentSize": {"__type__": "cc.Size", "width": 49.34999084472656, "height": 26.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84hR8OMmRCK6UENeHIpSyl"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 431}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 249, "b": 235, "a": 255}, "_string": "灵 兽", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c64tq1lZRBOL4KYXTedCVE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "847oePcDZMZIM1ur/fCvcJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 426}, "_enabled": true, "__prefab": {"__id__": 434}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2apKfWgl1ArrbPw64P2eQK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 426}, "_enabled": true, "__prefab": {"__id__": 436}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "53c46891-998e-4a0c-8ccd-************@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8XtbVOAtF+qcvNDdE0WYt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7MbOk3FFLwKIgCXPaTubU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 413}, "_prefab": {"__id__": 439}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 438}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 440}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "4ebAXguRZCAK/PG4RE+u6T", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 441}, {"__id__": 443}, {"__id__": 444}, {"__id__": 445}, {"__id__": 446}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 442}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 442}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 39.743, "y": 48.615, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 442}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 442}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 442}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.Node", "_name": "spine_jianzaod<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 413}, "_children": [], "_active": false, "_components": [{"__id__": 448}, {"__id__": 450}], "_prefab": {"__id__": 452}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 449}, "_contentSize": {"__type__": "cc.Size", "width": 224.8300018310547, "height": 672.8099975585938}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1.4115109350129516, "y": 0.4200740267064756}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58KVp7p9VKNKpcBqlgOGzm"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 451}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "97a427a8-5738-4e9c-9977-3758b6246877", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<PERSON><PERSON><PERSON>", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "836VICxvtIzKOHBUF0J69a"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfLVz9BYRPA49Pbyq1BGY3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_build", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 413}, "_children": [{"__id__": 454}, {"__id__": 460}], "_active": true, "_components": [{"__id__": 466}], "_prefab": {"__id__": 468}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 30.007, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999991, "y": 0.9999999999999991, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 453}, "_children": [], "_active": true, "_components": [{"__id__": 455}, {"__id__": 457}], "_prefab": {"__id__": 459}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 456}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6DIjO3IxO8rUnKB6SNtd0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 458}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7849d6ab-2361-4255-aed4-1eecfc7e9c8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "931J2ZelRB3onb6r1hIz3A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4ckwpXQppPUpWxpYfAPx2a", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 453}, "_children": [], "_active": false, "_components": [{"__id__": 461}, {"__id__": 463}], "_prefab": {"__id__": 465}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 460}, "_enabled": true, "__prefab": {"__id__": 462}, "_contentSize": {"__type__": "cc.Size", "width": 107, "height": 101}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18q7KRmthCxZrQUHTIGSmW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 460}, "_enabled": true, "__prefab": {"__id__": 464}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@f2df0", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62asDd3c1Cxrx5w3UACBt8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10GlJlCkpEQaw1XhUaJ1/g", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 467}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5ancap7ABHVrG5qo4vaTt3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edQcciTmdBz5Zvb1lw6w+I", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 413}, "_enabled": true, "__prefab": {"__id__": 470}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aazQ2IWm9E9avGOTTUou26"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 413}, "_enabled": true, "__prefab": {"__id__": 472}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 413}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9YU6bU7RLU4IGFFN/5OHj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8a55iO0TRJCJnz68w/B4OY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_fracture", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 475}, {"__id__": 481}, {"__id__": 493}, {"__id__": 502}, {"__id__": 508}], "_active": true, "_components": [{"__id__": 524}, {"__id__": 526}], "_prefab": {"__id__": 528}, "_lpos": {"__type__": "cc.Vec3", "x": 13.491, "y": -110.085, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999998, "y": 0.9999999999999998, "z": 0.26427061310782235}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spine_fracture", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 474}, "_children": [], "_active": true, "_components": [{"__id__": 476}, {"__id__": 478}], "_prefab": {"__id__": 480}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -44.466, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 475}, "_enabled": true, "__prefab": {"__id__": 477}, "_contentSize": {"__type__": "cc.Size", "width": 1955.7099609375, "height": 1183.8299560546875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.500002559113014, "y": 0.5379150819220793}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bdr6Gt5JEQ4lBduI/Iiso"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 475}, "_enabled": true, "__prefab": {"__id__": 479}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "c21ca3d0-d0ca-42ce-8e20-0b7725706184", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "sklx_btn", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21LoBxLS5KBK6uevEm7qsG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2ecbi9NiFC1oo4crFXzJDm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "name_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 474}, "_children": [{"__id__": 482}], "_active": true, "_components": [{"__id__": 488}, {"__id__": 490}], "_prefab": {"__id__": 492}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -124.635, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "city_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 481}, "_children": [], "_active": true, "_components": [{"__id__": 483}, {"__id__": 485}], "_prefab": {"__id__": 487}, "_lpos": {"__type__": "cc.Vec3", "x": -2.842170943040401e-14, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.8108108108108105}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 482}, "_enabled": true, "__prefab": {"__id__": 484}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 26.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "687mU5F+9DvbZii3zF+/NG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 482}, "_enabled": true, "__prefab": {"__id__": 486}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 249, "b": 235, "a": 255}, "_string": "时空裂隙", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bmjvZ5oRJF4y2QEm1g11+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "954T0zFl5IZ4O1n8dB0BRr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 481}, "_enabled": true, "__prefab": {"__id__": 489}, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfiZCTFzxMKpD0gitqrws5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 481}, "_enabled": true, "__prefab": {"__id__": 491}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "53c46891-998e-4a0c-8ccd-************@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48S24+xfNKT44IbYuF2TJp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b5h4D1DudM0bAUBNvJrX52", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 474}, "_prefab": {"__id__": 494}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 493}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 495}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "01P41B43VKt6dLEyKLF+sE", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 496}, {"__id__": 498}, {"__id__": 499}, {"__id__": 500}, {"__id__": 501}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 39.743, "y": 48.615, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.Node", "_name": "spine_jianzaod<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 474}, "_children": [], "_active": false, "_components": [{"__id__": 503}, {"__id__": 505}], "_prefab": {"__id__": 507}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 502}, "_enabled": true, "__prefab": {"__id__": 504}, "_contentSize": {"__type__": "cc.Size", "width": 224.8300018310547, "height": 672.8099975585938}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1.4115109350129516, "y": 0.4200740267064756}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22YOupsTVKRq7RcPm9qBcx"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 502}, "_enabled": true, "__prefab": {"__id__": 506}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "97a427a8-5738-4e9c-9977-3758b6246877", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<PERSON><PERSON><PERSON>", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8df1triA9ICI6Qi5PgFh4H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ee6kUzyFdDZK+zywvOl3nS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_build", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 474}, "_children": [{"__id__": 509}, {"__id__": 515}], "_active": false, "_components": [{"__id__": 521}], "_prefab": {"__id__": 523}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 30.007, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999991, "y": 0.9999999999999991, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 508}, "_children": [], "_active": true, "_components": [{"__id__": 510}, {"__id__": 512}], "_prefab": {"__id__": 514}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 509}, "_enabled": true, "__prefab": {"__id__": 511}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eFi4Ju7NDAqdgu8+xKxOy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 509}, "_enabled": true, "__prefab": {"__id__": 513}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7849d6ab-2361-4255-aed4-1eecfc7e9c8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d45qZf0MlHS7Gq8+wdwxGX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "21yqJME9dPJqK0m0IfX+sZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 508}, "_children": [], "_active": false, "_components": [{"__id__": 516}, {"__id__": 518}], "_prefab": {"__id__": 520}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 515}, "_enabled": true, "__prefab": {"__id__": 517}, "_contentSize": {"__type__": "cc.Size", "width": 107, "height": 101}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "386lYIeNlMX4UOLLGXMvmO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 515}, "_enabled": true, "__prefab": {"__id__": 519}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@f2df0", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4e/myZGRtJvJ7c9l5xfJy9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3c+124EwtDB4nejiuo4Nwg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 508}, "_enabled": true, "__prefab": {"__id__": 522}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3KJTuFAhG9aOdLHtX1CWa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aa22y+PDpN7rJ/kSWapUzd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 525}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "779sQB1/BJ0KvPOOkUuIFf"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 527}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 474}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "204tJGy3lF8LLVXV99aW5g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66VMO8RDNJzKvcgmPhHGQg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_cloud", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 530}, {"__id__": 547}, {"__id__": 564}, {"__id__": 581}], "_active": false, "_components": [{"__id__": 598}, {"__id__": 600}], "_prefab": {"__id__": 602}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "cloud_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 529}, "_children": [{"__id__": 531}], "_active": true, "_components": [{"__id__": 540}, {"__id__": 542}, {"__id__": 544}], "_prefab": {"__id__": 546}, "_lpos": {"__type__": "cc.Vec3", "x": -252, "y": -275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 530}, "_prefab": {"__id__": 532}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 531}, "asset": {"__uuid__": "d82f243e-e859-4b4c-ba09-4ba9efa5d8d0", "__expectedType__": "cc.Prefab"}, "fileId": "45HztMbNZE5rTr0Q52zK6q", "instance": {"__id__": 533}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "7b/GyiMshKWrm8kiwyXpIM", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 534}, {"__id__": 536}, {"__id__": 537}, {"__id__": 538}, {"__id__": 539}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 535}, "propertyPath": ["_name"], "value": "lizi"}, {"__type__": "cc.TargetInfo", "localID": ["45HztMbNZE5rTr0Q52zK6q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 535}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 68.067, "y": -244.484, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 535}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 535}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 535}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 530}, "_enabled": true, "__prefab": {"__id__": 541}, "_contentSize": {"__type__": "cc.Size", "width": 3500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.42842857142857144, "y": 0.3496354799513973}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11eG3u+jBBMKRA9VRkr4fb"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 530}, "_enabled": true, "__prefab": {"__id__": 543}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "aa9ad072-44a4-440a-a507-12a3953afb57", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "ya<PERSON>u", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f91FdOKURNcICWY6Ph7jRB"}, {"__type__": "294c2cSSLdODIVL20s2UnPF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 530}, "_enabled": true, "__prefab": {"__id__": 545}, "cloudId": 2, "aniKeyS": ["ya<PERSON>u", "yaozu_disappear"], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0seiIgBNGbaiC0xyJ2d7Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66ZjTTLJZC8rTFvLb5G3x2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "cloud_3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 529}, "_children": [{"__id__": 548}], "_active": true, "_components": [{"__id__": 557}, {"__id__": 559}, {"__id__": 561}], "_prefab": {"__id__": 563}, "_lpos": {"__type__": "cc.Vec3", "x": -252, "y": -275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 547}, "_prefab": {"__id__": 549}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 548}, "asset": {"__uuid__": "daf6bc35-1583-4494-a558-f237f74060d7", "__expectedType__": "cc.Prefab"}, "fileId": "45HztMbNZE5rTr0Q52zK6q", "instance": {"__id__": 550}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "faXDTyytlINKfR4ODuZHRf", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 551}, {"__id__": 553}, {"__id__": 554}, {"__id__": 555}, {"__id__": 556}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 552}, "propertyPath": ["_name"], "value": "lizi"}, {"__type__": "cc.TargetInfo", "localID": ["45HztMbNZE5rTr0Q52zK6q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 552}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 624.322, "y": 443.194, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 552}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 552}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 552}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 547}, "_enabled": true, "__prefab": {"__id__": 558}, "_contentSize": {"__type__": "cc.Size", "width": 3500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.42842857142857144, "y": 0.3496354799513973}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9d71DH3fhHQbBhCVjiRj1w"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 547}, "_enabled": true, "__prefab": {"__id__": 560}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "aa9ad072-44a4-440a-a507-12a3953afb57", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "wuzu", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88IlP8CFdGNoJfZhcGItI+"}, {"__type__": "294c2cSSLdODIVL20s2UnPF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 547}, "_enabled": true, "__prefab": {"__id__": 562}, "cloudId": 3, "aniKeyS": ["wuzu", "wuzu_disappear"], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "df90dj7+9Ge4YgEwVLHnKb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9cZrUFdD1GvZQVwCxkAV62", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "cloud_4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 529}, "_children": [{"__id__": 565}], "_active": true, "_components": [{"__id__": 574}, {"__id__": 576}, {"__id__": 578}], "_prefab": {"__id__": 580}, "_lpos": {"__type__": "cc.Vec3", "x": -252, "y": -275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 564}, "_prefab": {"__id__": 566}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 565}, "asset": {"__uuid__": "09b95525-baa8-435b-a584-d75b75e4daa9", "__expectedType__": "cc.Prefab"}, "fileId": "45HztMbNZE5rTr0Q52zK6q", "instance": {"__id__": 567}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "32Tk9kV8NNnpUxldZW1YNu", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 568}, {"__id__": 570}, {"__id__": 571}, {"__id__": 572}, {"__id__": 573}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 569}, "propertyPath": ["_name"], "value": "lizi"}, {"__type__": "cc.TargetInfo", "localID": ["45HztMbNZE5rTr0Q52zK6q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 569}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 1258.107, "y": -326.597, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 569}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 569}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 569}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 564}, "_enabled": true, "__prefab": {"__id__": 575}, "_contentSize": {"__type__": "cc.Size", "width": 3500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.42842857142857144, "y": 0.3496354799513973}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8OW9P6ZxLDY5N7VADug1X"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 564}, "_enabled": true, "__prefab": {"__id__": 577}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "aa9ad072-44a4-440a-a507-12a3953afb57", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "ming<PERSON>", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e24GMGcFZAL5/OkLPSSVAc"}, {"__type__": "294c2cSSLdODIVL20s2UnPF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 564}, "_enabled": true, "__prefab": {"__id__": 579}, "cloudId": 4, "aniKeyS": ["ming<PERSON>", "mingzu_disappear"], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cqdLUViZOYoZrZYYunBVS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39TzGr2hZFgoVeBcENcSKO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "cloud_5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 529}, "_children": [{"__id__": 582}], "_active": true, "_components": [{"__id__": 591}, {"__id__": 593}, {"__id__": 595}], "_prefab": {"__id__": 597}, "_lpos": {"__type__": "cc.Vec3", "x": -252, "y": -275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 581}, "_prefab": {"__id__": 583}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 582}, "asset": {"__uuid__": "d8d38e85-74d8-41f6-a719-b035023670f3", "__expectedType__": "cc.Prefab"}, "fileId": "45HztMbNZE5rTr0Q52zK6q", "instance": {"__id__": 584}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "9d6mzW3XRG3oUWSTPkwvd2", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 585}, {"__id__": 587}, {"__id__": 588}, {"__id__": 589}, {"__id__": 590}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 586}, "propertyPath": ["_name"], "value": "lizi"}, {"__type__": "cc.TargetInfo", "localID": ["45HztMbNZE5rTr0Q52zK6q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 586}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 1570.311, "y": 776.747, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 586}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 586}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 586}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 581}, "_enabled": true, "__prefab": {"__id__": 592}, "_contentSize": {"__type__": "cc.Size", "width": 3500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.42842857142857144, "y": 0.3496354799513973}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2uuGihj1Or5WUyfColvmx"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 581}, "_enabled": true, "__prefab": {"__id__": 594}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "aa9ad072-44a4-440a-a507-12a3953afb57", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<PERSON><PERSON>u", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cCREW0NtB4JCOu7doETIe"}, {"__type__": "294c2cSSLdODIVL20s2UnPF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 581}, "_enabled": true, "__prefab": {"__id__": 596}, "cloudId": 5, "aniKeyS": ["<PERSON><PERSON>u", "shenzu_disappear"], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86UHUEODhCXJD1Kh6Dj4Aa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d3hNzew41A5ZEIvXnZjkW2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 529}, "_enabled": true, "__prefab": {"__id__": 599}, "_contentSize": {"__type__": "cc.Size", "width": 3500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82TqwQXZhAbb6uFp6R1ieW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 529}, "_enabled": true, "__prefab": {"__id__": 601}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26S03RQRZF7JuH5eAKFz3P"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3zv+04TtPq6RCz5sUpvcn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 604}, "_contentSize": {"__type__": "cc.Size", "width": 3500, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5a7Uoij+dMFI94mBG8/XmT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0b4RrD7xVM/4plCzsuaeZ8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 607}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 606}, "asset": {"__uuid__": "482aece8-9a1d-4962-8dc0-88d0b6c5bb25", "__expectedType__": "cc.Prefab"}, "fileId": "94ZE+yztZAq7d4RQtKZJpd", "instance": {"__id__": 608}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c6E31kDBBNzb3le4orhbM6", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 609}, {"__id__": 611}, {"__id__": 612}, {"__id__": 613}, {"__id__": 614}, {"__id__": 616}, {"__id__": 618}, {"__id__": 620}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 610}, "propertyPath": ["_name"], "value": "UITalk"}, {"__type__": "cc.TargetInfo", "localID": ["94ZE+yztZAq7d4RQtKZJpd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 610}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 610}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 610}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 615}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 58, "height": 54.4}}, {"__type__": "cc.TargetInfo", "localID": ["d4EeGoE6xP65GVjh9/NSrC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 617}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 360, "height": 30.240000000000002}}, {"__type__": "cc.TargetInfo", "localID": ["1ahTdm6MJAbJ+XBrsCq8pg"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 619}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 750, "height": 1500}}, {"__type__": "cc.TargetInfo", "localID": ["3fsF7DgrRGnadc9OVhwaFR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 610}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 622}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0amabPEKxFZ6JKZFXun2sN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 624}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72qDWFvthCqpa+I5QdIg/v"}, {"__type__": "c07b1S++r9AAa0xBr3BUg5E", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 626}, "nodeGround": {"__id__": 33}, "btnMap": {"__id__": 2}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cZx2228lMG7Wb3qbVlYrc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "acon+DsXtJWKYpaDS/e91N", "instance": null, "targetOverrides": [], "nestedPrefabInstanceRoots": [{"__id__": 606}, {"__id__": 582}, {"__id__": 565}, {"__id__": 548}, {"__id__": 531}, {"__id__": 493}, {"__id__": 438}, {"__id__": 399}, {"__id__": 320}, {"__id__": 267}, {"__id__": 250}, {"__id__": 236}, {"__id__": 228}, {"__id__": 215}, {"__id__": 203}, {"__id__": 194}, {"__id__": 185}, {"__id__": 168}, {"__id__": 147}, {"__id__": 132}, {"__id__": 119}, {"__id__": 54}, {"__id__": 34}]}]