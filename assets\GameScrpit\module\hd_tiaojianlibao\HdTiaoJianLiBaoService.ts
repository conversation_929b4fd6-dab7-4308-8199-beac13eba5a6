import StorageMgr, { StorageKeyEnum } from "db://assets/platform/src/StorageHelper";
import { FirstRechargeMessage, WindowPackMessage } from "../../game/net/protocol/Activity";
import { ArrayUtils } from "../../lib/utils/ArrayUtils";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { ActivityModule } from "../activity/ActivityModule";
import { HdTiaoJianLiBaoModule } from "./HdTiaoJianLiBaoModule";
const ACTIVITY_ID = 10601;
export class HdTiaoJianLiBaoService {
  private _tickId: number;
  public async init() {}

  public canShowTiaoJianLiBao(callback: Function) {
    if (!ActivityModule.data.allActivityConfig[ACTIVITY_ID]) {
      callback(false, 0);
      return;
    }

    //
    let storageValue = ActivityModule.data.allActivityConfig[ACTIVITY_ID] as any;
    let typeMap = new Map<number, number[]>(); //存储窗口类型对应的礼包充值id
    for (let i = 0; i < storageValue.packList.length; i++) {
      if (typeMap[storageValue.packList[i].type]) {
        typeMap[storageValue.packList[i].type].push(storageValue.packList[i].id);
      } else {
        typeMap[storageValue.packList[i].type] = [storageValue.packList[i].id];
      }
    }
    HdTiaoJianLiBaoModule.api.windowPack(10601, (data: WindowPackMessage) => {
      let minCountDown = 0; // 最小倒计时
      let dialogType = "0";
      Object.keys(data.durationMap).forEach((k) => {
        //获取大于当前时间的最小倒计时
        let v = data.durationMap[k];
        let isAllCharge = ArrayUtils.isArraySubset(typeMap[k], data.redeemList);

        if (!isAllCharge && v > TimeUtils.serverTime && (minCountDown == 0 || v < minCountDown)) {
          minCountDown = v;
          dialogType = k;
        }
      });
      let isShow = minCountDown > TimeUtils.serverTime;
      callback(isShow, minCountDown, dialogType);
      // WindowsPackBuyResponse
    });
  }
}
