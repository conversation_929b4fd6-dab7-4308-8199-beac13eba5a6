import MsgEnum from "../../game/event/MsgEnum";
import { GameData } from "../../game/GameData";
import { GameDirector } from "../../game/GameDirector";
import data from "../../lib/data/data";
import MsgMgr from "../../lib/event/MsgMgr";
import { ActivityModule } from "../activity/ActivityModule";
import { FundApi } from "./FundApi";
import { FundConfig } from "./FundConfig";
import { FundData } from "./FundData";
import { FundRoute } from "./FundRoute";
import { FundService } from "./FundService";
import { FundSubscriber } from "./FundSubscriber";
import { FundViewModel } from "./FundViewModel";

export class FundModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): FundModule {
    if (!GameData.instance.FundModule) {
      GameData.instance.FundModule = new FundModule();
    }
    return GameData.instance.FundModule;
  }

  private _data = new FundData();
  private _api = new FundApi();
  private _config = new FundConfig();
  private _viewModel = new FundViewModel();
  private _service = new FundService();
  private _route = new FundRoute();
  private _subscriber = new FundSubscriber();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get route() {
    return this.instance._route;
  }

  public async init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new FundData();
    this._api = new FundApi();
    this._config = new FundConfig();
    this._viewModel = new FundViewModel();
    this._service = new FundService();
    this._route = new FundRoute();
    this._subscriber = new FundSubscriber();

    // 初始化模块
    this._subscriber.register();
    this._route.init();
    this._service.init();

    let list = [10401, 10402, 10403, 10404, 10405];

    for (let i = 0; i < list.length; i++) {
      await new Promise((res) => {
        if (GameDirector.instance.isSystemOpen(list[i])) {
          FundModule.api.fund(list[i], res);
        }
        // if (ActivityModule.service.checkActivityUnlock(list[i])) {
        // } else {
        //   res(true);
        // }
      });
    }
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
