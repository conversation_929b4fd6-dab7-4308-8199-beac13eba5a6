{"skeleton": {"hash": "YNowHNv6a+IRrrvG5iBYQ/MC0rk=", "spine": "3.8.75", "x": -309.64, "y": -23, "width": 596, "height": 501.58, "images": "./images/", "audio": "D:/spine导出/灵兽动画/玄武"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "allxuanwu", "parent": "root", "length": 358.88, "rotation": -1.58, "x": 72.3, "y": -48.17}, {"name": "bone52", "parent": "allxuanwu", "length": 219.52, "rotation": -177.85, "x": -49.62, "y": 2.54}, {"name": "bone", "parent": "bone52", "length": 346.5, "rotation": 89.43, "x": -13.9, "y": -180.56}, {"name": "bone2", "parent": "bone", "length": 109.54, "rotation": -101.73, "x": 1.65, "y": -2.48}, {"name": "bone3", "parent": "bone2", "length": 124.29, "rotation": -124.11, "x": 109.54, "scaleX": -1, "transform": "noRotationOrReflection", "color": "abe323ff"}, {"name": "bone4", "parent": "bone3", "length": 65.36, "rotation": -50.69, "x": 124.29}, {"name": "bone5", "parent": "bone4", "length": 62.02, "rotation": 32.95, "x": 65.36}, {"name": "bone6", "parent": "bone5", "length": 36.32, "rotation": 59.91, "x": 41.99, "y": 0.79}, {"name": "bone7", "parent": "bone5", "length": 28.7, "rotation": 112.14, "x": 34.22, "y": 10.74}, {"name": "bone8", "parent": "bone5", "length": 23.18, "rotation": -63.16, "x": 46.73, "y": -31.03}, {"name": "bone9", "parent": "bone8", "length": 28.2, "rotation": -46.51, "x": 22.92, "y": -0.18}, {"name": "bone10", "parent": "bone9", "length": 25.63, "rotation": -0.51, "x": 28.2}, {"name": "bone11", "parent": "bone10", "length": 23.16, "rotation": 62.65, "x": 25.63}, {"name": "bone12", "parent": "bone5", "length": 22.68, "rotation": -98.42, "x": 26.77, "y": -9.45}, {"name": "bone13", "parent": "bone12", "length": 22.64, "rotation": 29.3, "x": 22.38, "y": 0.11}, {"name": "bone14", "parent": "bone13", "length": 17.38, "rotation": -35.95, "x": 22.64}, {"name": "bone15", "parent": "bone5", "length": 24.99, "rotation": -50.68, "x": 81.11, "y": 21.53}, {"name": "bone16", "parent": "bone15", "length": 27.3, "rotation": -5.97, "x": 24.99}, {"name": "bone17", "parent": "bone16", "length": 21.8, "rotation": -22.79, "x": 27.3}, {"name": "bone18", "parent": "bone5", "length": 24.85, "rotation": -69.52, "x": 86.18, "y": -20.81}, {"name": "bone19", "parent": "bone18", "length": 27.73, "rotation": 12.61, "x": 25.09, "y": 0.21}, {"name": "bone20", "parent": "bone5", "length": 24.23, "rotation": -90.06, "x": 80.21, "y": -31.1}, {"name": "bone21", "parent": "bone20", "length": 22.78, "rotation": -15.85, "x": 24.23}, {"name": "bone22", "parent": "bone21", "length": 22.28, "rotation": -16.73, "x": 22.78}, {"name": "bone23", "parent": "bone5", "length": 25.68, "rotation": -116.52, "x": 35.32, "y": -36.93}, {"name": "bone24", "parent": "bone23", "length": 28.86, "rotation": -38.72, "x": 26.62, "y": -0.03}, {"name": "bone25", "parent": "bone24", "length": 27.44, "rotation": -18.6, "x": 28.86}, {"name": "bone26", "parent": "bone5", "length": 25.94, "rotation": -136.17, "x": 19.1, "y": -12.39}, {"name": "bone27", "parent": "bone26", "length": 24.49, "rotation": -17.55, "x": 25.94}, {"name": "bone28", "parent": "bone27", "length": 23.06, "rotation": -13.19, "x": 25.62, "y": 0.03}, {"name": "bone29", "parent": "bone3", "x": 76.15, "y": 71.95}, {"name": "bone30", "parent": "bone", "length": 82.67, "rotation": 94.06, "x": 2.33, "y": 18.48, "color": "abe323ff"}, {"name": "bone31", "parent": "bone30", "length": 101.51, "rotation": 12.38, "x": 82.74, "y": 1.06}, {"name": "bone32", "parent": "bone31", "length": 69.81, "rotation": 41.32, "x": 101.51}, {"name": "bone33", "parent": "bone32", "length": 78.64, "rotation": 65.93, "x": 69.81}, {"name": "bone34", "parent": "bone33", "length": 61.84, "rotation": -42.1, "x": 79.53, "y": -0.59}, {"name": "bone35", "parent": "bone34", "length": 52.26, "rotation": -59.46, "x": 61.84}, {"name": "bone36", "parent": "bone2", "length": 66.95, "rotation": 134.52, "x": 84.1, "y": 16.29}, {"name": "bone37", "parent": "bone36", "length": 56.67, "rotation": -68.88, "x": 66.95}, {"name": "bone38", "parent": "bone37", "length": 80.17, "rotation": -42.93, "x": 56.67}, {"name": "bone39", "parent": "bone2", "length": 63.47, "rotation": 126.51, "x": 112.8, "y": 9.85}, {"name": "bone40", "parent": "bone39", "length": 45.42, "rotation": -52.25, "x": 63.47}, {"name": "bone41", "parent": "bone40", "length": 63.96, "rotation": -57.1, "x": 45.42}, {"name": "bone42", "parent": "bone30", "length": 67.07, "rotation": -64.61, "x": 44.65, "y": -6.92}, {"name": "bone43", "parent": "bone42", "length": 37.17, "rotation": -51.79, "x": 67.48, "y": -0.23}, {"name": "bone44", "parent": "bone43", "length": 71.59, "rotation": -56.28, "x": 37.35, "y": -0.44}, {"name": "bone45", "parent": "bone30", "length": 48.92, "rotation": -55.31, "x": 2.71, "y": -5.84}, {"name": "bone46", "parent": "bone45", "length": 35.3, "rotation": -51.86, "x": 48.92}, {"name": "bone47", "parent": "bone46", "length": 47.3, "rotation": -61.89, "x": 35.3}, {"name": "bone48", "parent": "bone52", "length": 183.36, "rotation": 0.72, "x": 74.56, "y": -62.71}, {"name": "bone49", "parent": "bone52", "length": 166.91, "rotation": -1.35, "x": 93, "y": -87.97}, {"name": "bone50", "parent": "bone52", "length": 165.18, "rotation": -179.19, "x": -22.45, "y": -66.81}, {"name": "bone51", "parent": "bone52", "length": 131.63, "rotation": -179.63, "x": -12.39, "y": -87.8}, {"name": "bone53", "parent": "bone2", "length": 75.7, "rotation": -70.41, "x": 1.47, "y": -25.22}, {"name": "bone54", "parent": "bone53", "length": 72.54, "rotation": 36.18, "x": 75.7}, {"name": "bone55", "parent": "bone54", "length": 58.67, "rotation": -65.14, "x": 72.54}, {"name": "bone56", "parent": "bone55", "length": 92.97, "rotation": -59.83, "x": 58.67}, {"name": "bone57", "parent": "bone56", "length": 58.6, "rotation": 54.74, "x": 92.97}, {"name": "bone58", "parent": "bone57", "length": 53.53, "rotation": 76.09, "x": 58.6}, {"name": "bone59", "parent": "bone58", "length": 58.11, "rotation": 72.38, "x": 55.13, "y": 0.78}, {"name": "bone60", "parent": "bone2", "length": 46.54, "rotation": 84.26, "x": -0.77, "y": -12.81}, {"name": "bone61", "parent": "bone60", "length": 43, "rotation": -14.24, "x": 46.41, "y": 0.39}, {"name": "bone62", "parent": "bone30", "length": 61.78, "rotation": 58.66, "x": 19.88, "y": 22.27}, {"name": "bone63", "parent": "bone62", "length": 51.71, "rotation": 13.36, "x": 61.78}, {"name": "bone64", "parent": "bone63", "length": 37.19, "rotation": 30.68, "x": 51.71}, {"name": "bone65", "parent": "bone64", "length": 24.94, "rotation": 77.18, "x": 37.19}, {"name": "bone66", "parent": "bone30", "length": 29.02, "rotation": -124.91, "x": -1.71, "y": -10.46}, {"name": "bone67", "parent": "bone66", "length": 38.68, "rotation": -11.98, "x": 29.02}, {"name": "bone68", "parent": "bone59", "length": 13.34, "rotation": -1.76, "x": 70.47, "y": 0.4}, {"name": "bone69", "parent": "bone68", "length": 16.87, "rotation": -19.77, "x": 13.34}, {"name": "bone70", "parent": "bone69", "length": 16.49, "rotation": 6.76, "x": 16.85, "y": -0.12}, {"name": "bone71", "parent": "bone31", "length": 26.42, "rotation": -147.43, "x": 74.4, "y": -12.4}, {"name": "bone72", "parent": "bone71", "length": 30.79, "rotation": -18.36, "x": 26.18, "y": -0.21}, {"name": "bone73", "parent": "bone31", "length": 21.66, "rotation": 44.67, "x": 83.53, "y": 4.68}, {"name": "bone74", "parent": "bone73", "length": 14.52, "rotation": -25.28, "x": 21.66}, {"name": "bone75", "parent": "bone32", "length": 16.33, "rotation": -90.5, "x": 7.34, "y": -3.58}, {"name": "bone76", "parent": "bone75", "length": 24.23, "rotation": 54.11, "x": 16.33}, {"name": "bone77", "parent": "allxuanwu", "rotation": 1.58, "x": -399.52, "y": 272.75, "color": "39ff00ff"}, {"name": "bone78", "parent": "bone77", "x": 63.72, "y": 4.72, "color": "39ff00ff"}, {"name": "bone79", "parent": "bone77", "x": 129.33, "y": 34.46, "color": "39ff00ff"}, {"name": "bone80", "parent": "bone77", "x": 190.22, "y": 63.25, "color": "39ff00ff"}, {"name": "bone81", "parent": "bone77", "x": 251.34, "y": 101.42, "color": "39ff00ff"}, {"name": "bone82", "parent": "bone5", "length": 26.99, "rotation": 178.5, "x": -14.38, "y": -18.79}, {"name": "bone83", "parent": "bone82", "length": 21.53, "rotation": -7.71, "x": 27.12, "y": 0.25}, {"name": "bone84", "parent": "bone83", "length": 16.12, "rotation": 8.08, "x": 21.26, "y": 0.09}, {"name": "bone85", "parent": "allxuanwu", "rotation": 1.58, "x": 120.76, "y": 20.44, "color": "31b300ff"}, {"name": "bone86", "parent": "bone85", "x": -7.67, "y": 87.13, "color": "31b300ff"}, {"name": "bone87", "parent": "bone85", "x": -95.35, "y": 31.78, "color": "31b300ff"}, {"name": "bone88", "parent": "bone85", "x": -129.33, "y": 180.29, "color": "31b300ff"}, {"name": "bone89", "parent": "bone85", "x": -201.12, "y": 202.21, "color": "31b300ff"}, {"name": "bone90", "parent": "bone85", "x": -259.75, "y": 263.04, "color": "31b300ff"}, {"name": "bone91", "parent": "bone85", "x": -262.49, "y": 316.74, "color": "31b300ff"}, {"name": "bone92", "parent": "allxuanwu", "rotation": 1.58, "x": -141.9, "y": 561.06, "color": "4cc00bff"}, {"name": "bone93", "parent": "bone92", "x": -11.33, "y": -67.6, "color": "4cc00bff"}, {"name": "bone94", "parent": "bone92", "x": 22.12, "y": -95.28, "color": "4cc00bff"}, {"name": "bone95", "parent": "bone92", "x": 8.08, "y": -141.53, "color": "4cc00bff"}, {"name": "bone96", "parent": "bone7", "length": 17.85, "rotation": 89.44, "x": 55.56, "y": 4.92}, {"name": "bone97", "parent": "bone96", "length": 23.84, "rotation": 25.62, "x": 17.89, "y": 0.36}, {"name": "bone98", "parent": "bone97", "length": 16.35, "rotation": 20.43, "x": 23.47, "y": 0.43}], "slots": [{"name": "xuanwu01", "bone": "allxuanwu"}, {"name": "xuanwu02", "bone": "allxuanwu"}, {"name": "xuanwu03", "bone": "allxuanwu"}, {"name": "xuanwu04", "bone": "allxuanwu"}, {"name": "xuanwu05", "bone": "allxuanwu"}, {"name": "xuanwu06", "bone": "allxuanwu"}, {"name": "xuanwu07", "bone": "allxuanwu", "attachment": "xuanwu07"}, {"name": "xuanwu08", "bone": "allxuanwu", "attachment": "xuanwu08"}, {"name": "xuanwu09", "bone": "allxuanwu", "attachment": "xuanwu09"}, {"name": "xuanwu010", "bone": "allxuanwu", "attachment": "xuanwu010"}, {"name": "xuanwu013", "bone": "allxuanwu", "attachment": "xuanwu013"}, {"name": "xuanwu011", "bone": "allxuanwu"}, {"name": "xuanwu012", "bone": "allxuanwu", "attachment": "xuanwu012"}, {"name": "xuanwu014", "bone": "allxuanwu", "attachment": "xuanwu014"}, {"name": "xuanwu015", "bone": "bone5", "attachment": "xuanwu015"}, {"name": "xuanwu016", "bone": "allxuanwu", "attachment": "xuanwu016"}, {"name": "xuanwu017", "bone": "allxuanwu", "attachment": "xuanwu017"}, {"name": "xuanwu018", "bone": "allxuanwu", "attachment": "xuanwu018"}, {"name": "xuanwu019", "bone": "allxuanwu", "attachment": "xuanwu019"}, {"name": "xuanwu020", "bone": "allxuanwu", "attachment": "xuanwu020"}, {"name": "xuanwu021", "bone": "allxuanwu", "attachment": "xuanwu021"}, {"name": "xuanwu022", "bone": "allxuanwu", "attachment": "xuanwu022"}, {"name": "xuanwu023", "bone": "allxuanwu", "attachment": "xuanwu023"}, {"name": "xuanwu024", "bone": "bone5", "attachment": "xuanwu024"}, {"name": "xuanwu025", "bone": "allxuanwu", "attachment": "xuanwu025"}, {"name": "xuanwu026", "bone": "bone7", "attachment": "xuanwu026"}, {"name": "xuanwu027", "bone": "bone29", "attachment": "xuanwu027"}, {"name": "xuanwu028", "bone": "allxuanwu", "attachment": "xuanwu028"}, {"name": "xuanwu029", "bone": "allxuanwu", "attachment": "xuanwu029"}, {"name": "xuanwu030", "bone": "allxuanwu", "attachment": "xuanwu030"}, {"name": "xuanwu031", "bone": "allxuanwu", "attachment": "xuanwu031"}, {"name": "xuanwu032", "bone": "allxuanwu", "attachment": "xuanwu032"}, {"name": "xuanwu033", "bone": "allxuanwu", "attachment": "xuanwu033"}, {"name": "xuanwu034", "bone": "allxuanwu", "attachment": "xuanwu034"}, {"name": "xuanwu035", "bone": "allxuanwu"}, {"name": "xuanwu036", "bone": "bone5", "attachment": "xuanwu036"}, {"name": "xuanwu037", "bone": "bone59", "attachment": "xuanwu037"}], "skins": [{"name": "default", "attachments": {"xuanwu013": {"xuanwu013": {"type": "mesh", "uvs": [0, 0.43019, 0.10628, 0.24942, 0.2881, 0.2225, 0.47901, 0.24173, 0.67901, 0.21096, 0.80401, 0.05712, 0.91082, 0, 1, 0, 1, 0.1225, 0.93128, 0.18404, 0.84264, 0.37635, 0.69264, 0.48789, 0.49264, 0.51865, 0.33355, 0.66096, 0.18582, 0.86481, 0.10855, 1, 0.03355, 0.67635, 0.1881, 0.46096, 0.3381, 0.40327, 0.48355, 0.36096, 0.69037, 0.33788, 0.82446, 0.20327, 0.92219, 0.08019], "triangles": [17, 1, 2, 17, 2, 18, 0, 1, 17, 16, 0, 17, 14, 16, 17, 14, 17, 13, 15, 16, 14, 19, 3, 4, 18, 2, 3, 18, 3, 19, 19, 20, 11, 12, 19, 11, 18, 19, 12, 13, 17, 18, 13, 18, 12, 22, 6, 7, 5, 6, 22, 22, 7, 8, 9, 22, 8, 21, 5, 22, 21, 22, 9, 4, 5, 21, 20, 4, 21, 19, 4, 20, 10, 21, 9, 20, 21, 10, 11, 20, 10], "vertices": [2, 70, 29.49, -2.86, 3e-05, 71, 12.23, -4.21, 0.99997, 2, 70, 24.02, -6.61, 0, 71, 6.35, -7.29, 1, 2, 70, 16.03, -5.8, 0.21245, 71, -1.49, -5.55, 0.78755, 2, 70, 7.87, -3.75, 0.99296, 71, -9.35, -2.55, 0.00704, 2, 69, 11.49, -2.41, 0.8102, 70, -0.93, -2.89, 0.1898, 1, 69, 4.72, -3.06, 1, 1, 69, -0.08, -1.95, 1, 1, 69, -3.46, 0.04, 1, 1, 69, -1.84, 2.78, 1, 1, 69, 1.57, 2.63, 1, 1, 69, 7.47, 4.95, 1, 2, 69, 14.63, 4.1, 0.39207, 70, -0.17, 4.29, 0.60793, 1, 70, 8.62, 3.44, 1, 2, 70, 16.19, 5.77, 0.93527, 71, 0.03, 5.92, 0.06473, 2, 70, 23.56, 9.76, 0.20277, 71, 7.83, 9.02, 0.79723, 2, 70, 27.56, 12.58, 0.12517, 71, 12.13, 11.35, 0.87483, 2, 70, 29.23, 3.7, 0.01955, 71, 12.74, 2.34, 0.98045, 1, 71, 4.57, -0.96, 1, 2, 70, 14.74, -0.78, 0.88537, 71, -2.17, -0.41, 0.11463, 2, 70, 8.25, -0.66, 0.99962, 71, -8.61, 0.47, 0.00038, 2, 69, 12.74, 0.69, 0.98572, 70, -0.8, 0.44, 0.01428, 1, 69, 5.88, 0.67, 1, 1, 69, 0.55, 0.1, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 16], "width": 44, "height": 26}}, "xuanwu015": {"xuanwu015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [68, 30.75, 81.88, 56.21, 133.68, 27.96, 119.79, 2.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 29, "height": 59}}, "xuanwu030": {"xuanwu030": {"type": "mesh", "uvs": [0.23904, 0.00966, 0.37093, 0, 0.50013, 0.03343, 0.40054, 0.09141, 0.38708, 0.16269, 0.47052, 0.23683, 0.62663, 0.30812, 0.83119, 0.38416, 0.94154, 0.4621, 1, 0.5638, 0.99538, 0.67216, 0.92809, 0.76626, 0.83119, 0.85846, 0.70199, 0.93735, 0.57011, 0.99818, 0.4113, 1, 0.32786, 0.98392, 0.30095, 0.9364, 0.35478, 0.87937, 0.48398, 0.82424, 0.58895, 0.74059, 0.62394, 0.6541, 0.61317, 0.56285, 0.51627, 0.4602, 0.2848, 0.38036, 0.08024, 0.29766, 0, 0.20262, 0, 0.10901, 0.09552, 0.04539, 0.30189, 0.0477, 0.17741, 0.10852, 0.18571, 0.18545, 0.28944, 0.2697, 0.4658, 0.33711, 0.64215, 0.40598, 0.74797, 0.47705, 0.80895, 0.56786, 0.79291, 0.66225, 0.73946, 0.75758, 0.62987, 0.83593, 0.50691, 0.91427], "triangles": [14, 15, 40, 15, 16, 40, 14, 40, 13, 16, 17, 40, 40, 39, 13, 13, 39, 12, 17, 18, 40, 18, 19, 40, 40, 19, 39, 39, 38, 12, 12, 38, 11, 19, 20, 39, 39, 20, 38, 38, 37, 11, 38, 20, 37, 11, 37, 10, 37, 20, 21, 10, 37, 9, 9, 37, 36, 37, 21, 36, 21, 22, 36, 8, 9, 36, 36, 22, 35, 36, 35, 8, 22, 23, 35, 23, 34, 35, 35, 7, 8, 35, 34, 7, 29, 1, 2, 25, 26, 31, 32, 31, 4, 31, 27, 30, 31, 26, 27, 31, 30, 4, 4, 30, 3, 27, 28, 30, 30, 29, 3, 30, 28, 29, 3, 29, 2, 28, 0, 29, 29, 0, 1, 24, 33, 23, 23, 33, 34, 33, 6, 34, 34, 6, 7, 24, 32, 33, 24, 25, 32, 33, 5, 6, 33, 32, 5, 25, 31, 32, 32, 4, 5], "vertices": [2, 56, 64.84, 24.08, 0.72977, 57, -17.72, 17.43, 0.27023, 2, 56, 72.65, 11.97, 0.38566, 57, -3.32, 18.1, 0.61434, 2, 56, 68.18, -4.58, 0.00097, 57, 8.74, 5.92, 0.99903, 2, 56, 47.95, -0.96, 0.99992, 57, -4.56, -9.75, 8e-05, 3, 55, 77.31, -27.86, 0.01811, 56, 27.28, -7.39, 0.98189, 57, -9.38, -30.85, 0, 2, 55, 54.95, -18.66, 0.73964, 56, 9.54, -23.81, 0.26036, 1, 55, 27.81, -15.66, 1, 2, 55, -3.96, -15.38, 0.45073, 54, 81.58, -14.75, 0.54927, 1, 54, 56.57, -23.22, 1, 1, 54, 25.19, -25.21, 1, 2, 54, -7.26, -20.23, 0.63294, 61, -14.21, 17.92, 0.36706, 1, 61, 15.14, 19.62, 1, 2, 61, 44.9, 18.12, 0.66689, 62, -5.82, 16.81, 0.33311, 1, 62, 21.78, 17.61, 1, 1, 62, 44.88, 15.3, 1, 1, 62, 54.28, 1.14, 1, 1, 62, 54.83, -9.02, 1, 1, 62, 44.09, -19.04, 1, 2, 61, 66.26, -28.61, 0.00171, 62, 26.37, -23.22, 0.99829, 2, 61, 46.17, -20.44, 0.35078, 62, 4.89, -20.25, 0.64922, 3, 54, -21.86, 25.68, 0.01861, 61, 18.62, -17.33, 0.97949, 62, -22.57, -24.01, 0.0019, 2, 54, 3.59, 18.39, 0.74639, 61, -7.5, -21.63, 0.25361, 1, 54, 31.13, 15.76, 1, 2, 55, 2.9, 24.86, 0.36663, 54, 63.36, 21.78, 0.63337, 2, 55, 37.5, 25.85, 0.99446, 54, 90.71, 43.01, 0.00554, 2, 55, 70.73, 24.17, 0.88104, 56, -22.69, 8.52, 0.11896, 2, 55, 97.4, 10.32, 0.06134, 56, 1.09, 26.9, 0.93866, 1, 56, 27.55, 37.11, 1, 2, 56, 49.21, 34.51, 0.96125, 57, -34.59, 9.17, 0.03875, 2, 56, 56.51, 13.65, 0.80877, 57, -12.89, 4.99, 0.19123, 2, 56, 34.52, 19.45, 0.99946, 57, -28.95, -11.1, 0.00054, 1, 56, 13.09, 10.23, 1, 1, 55, 61.26, 2.19, 1, 2, 55, 33.46, 2.82, 0.99987, 54, 101.05, 22.03, 0.00013, 2, 55, 5.34, 3.76, 0.84374, 54, 77.8, 6.19, 0.15626, 1, 54, 54.92, -2.08, 1, 1, 54, 26.77, -4.79, 1, 2, 54, -1.33, 0.82, 0.9319, 61, -10.57, -3.64, 0.0681, 2, 54, -29.16, 10.43, 7e-05, 61, 18.7, -0.42, 0.99993, 2, 61, 44.86, -4.48, 0.58823, 62, -0.3, -5.11, 0.41177, 1, 62, 26.81, -3.82, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 4, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80], "width": 107, "height": 303}}, "xuanwu019": {"xuanwu019": {"type": "mesh", "uvs": [0, 0.39211, 0.04947, 0.24909, 0.10774, 0.12579, 0.26848, 0.03948, 0.47945, 0, 0.68841, 0.00989, 0.8371, 0.14552, 0.91546, 0.37731, 0.93053, 0.41248, 1, 0.57459, 0.96368, 0.75706, 0.81701, 0.91241, 0.63617, 1, 0.50557, 1, 0.37497, 0.88776, 0.29259, 0.74227, 0.13386, 0.63377, 0.01331, 0.56226, 0.14793, 0.39458, 0.31268, 0.37485, 0.50155, 0.40937, 0.7065, 0.48581, 0.87728, 0.6461, 0.21222, 0.2121, 0.43324, 0.19237, 0.71654, 0.28114, 0.164, 0.54746, 0.39908, 0.59185, 0.56183, 0.72747, 0.73463, 0.78419], "triangles": [16, 26, 15, 16, 17, 26, 26, 19, 27, 17, 18, 26, 17, 0, 18, 26, 18, 19, 0, 1, 18, 14, 27, 28, 14, 15, 27, 15, 26, 27, 27, 20, 28, 27, 19, 20, 12, 29, 11, 29, 12, 28, 12, 13, 28, 13, 14, 28, 29, 28, 21, 28, 20, 21, 11, 22, 10, 11, 29, 22, 22, 29, 21, 10, 22, 9, 22, 8, 9, 22, 21, 8, 21, 25, 8, 25, 7, 8, 21, 20, 25, 20, 24, 25, 25, 6, 7, 25, 24, 5, 25, 5, 6, 19, 24, 20, 18, 23, 19, 18, 1, 23, 19, 23, 24, 24, 4, 5, 1, 2, 23, 23, 3, 24, 23, 2, 3, 24, 3, 4], "vertices": [2, 25, -25.67, -16.29, 0.15033, 28, -13.41, 4.94, 0.84967, 2, 25, -19.87, -3.91, 0.55989, 28, -12.11, 18.56, 0.44011, 2, 25, -13.19, 6.7, 0.89774, 28, -9.38, 30.8, 0.10226, 1, 25, 4.44, 13.66, 1, 2, 25, 27.33, 16.3, 0.55348, 26, -9.66, 13.19, 0.44652, 3, 25, 49.85, 14.6, 0.00112, 26, 8.97, 25.95, 0.99516, 27, -27.12, 18.25, 0.00373, 2, 26, 28.98, 25.95, 0.77034, 27, -8.16, 24.63, 0.22966, 2, 26, 47.94, 14.63, 0.08789, 27, 13.42, 19.95, 0.91211, 2, 26, 51.09, 13.11, 0.037, 27, 16.89, 19.52, 0.963, 1, 27, 32.89, 17.55, 1, 2, 27, 43.84, 5.17, 0.9396, 30, 31.94, 34.61, 0.0604, 2, 27, 46.03, -15.64, 0.40787, 30, 31.61, 13.69, 0.59213, 2, 30, 24.37, -6.02, 0.99219, 29, 47.97, -11.39, 0.00781, 2, 30, 14.99, -16.55, 0.80755, 29, 36.43, -19.5, 0.19245, 3, 30, -1.77, -20.51, 0.20982, 29, 19.21, -19.54, 0.78892, 28, 38.36, -24.42, 0.00126, 3, 30, -17.25, -18.64, 0.00028, 29, 4.57, -14.18, 0.71495, 28, 26.02, -14.9, 0.28477, 2, 29, -14.94, -16.23, 0.00093, 28, 6.79, -10.97, 0.99907, 1, 28, -7.52, -8.9, 1, 2, 25, -9.72, -17.09, 0.25246, 28, 1.89, 9.56, 0.74754, 4, 25, 8.13, -16.01, 0.49096, 26, -4.44, -24.03, 0.01762, 29, -12.25, 13.52, 0.06269, 28, 18.33, 16.58, 0.42873, 6, 25, 28.4, -19.79, 0.1877, 26, 13.75, -14.3, 0.44762, 27, -9.76, -18.38, 0.02978, 30, -24.11, 17.7, 0.03271, 29, 6.18, 22.76, 0.25986, 28, 38.7, 19.84, 0.04232, 5, 25, 50.27, -27.32, 0.00044, 26, 35.52, -6.5, 0.05325, 27, 8.39, -4.03, 0.88475, 30, -4.36, 29.75, 0.04571, 29, 28.16, 29.99, 0.01585, 2, 27, 30.5, 3.07, 0.99198, 30, 18.44, 34.14, 0.00802, 2, 25, -2.19, -1.3, 0.95034, 28, 3.67, 26.96, 0.04966, 4, 25, 21.73, -0.44, 0.99154, 26, -3.57, -3.38, 0.00624, 29, -10.83, 34.14, 0.00117, 28, 25.9, 35.82, 0.00105, 2, 26, 25.65, 8.6, 0.86488, 27, -5.79, 7.13, 0.13512, 1, 28, 7.61, -2.75, 1, 5, 25, 16.76, -35.43, 0.01607, 26, 14.44, -33.79, 0.01871, 27, -2.88, -36.63, 0.00256, 30, -19.48, -1.24, 0.0054, 29, 6.37, 3.26, 0.95726, 4, 25, 33.88, -48, 9e-05, 26, 35.67, -32.89, 0.00862, 27, 16.95, -29, 0.04997, 30, 1.12, 3.94, 0.94131, 2, 27, 31.69, -16.51, 0.39268, 30, 17.27, 14.55, 0.60732], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 2, 46, 46, 48, 48, 50, 14, 16, 16, 18, 50, 16, 34, 52, 52, 54, 54, 56, 56, 58], "width": 108, "height": 88}}, "xuanwu010": {"xuanwu010": {"type": "mesh", "uvs": [0, 0.13151, 0.17771, 0.00245, 0.51285, 0, 0.94616, 0, 1, 0.30575, 1, 0.64776, 0.81075, 0.86717, 0.479, 1, 0.19125, 1, 0.1574, 0.69616, 0.07277, 0.40254, 0, 0.37673, 0.26234, 0.22831, 0.57379, 0.409, 0.61102, 0.61227, 0.46884, 0.84135], "triangles": [12, 1, 2, 0, 1, 12, 10, 11, 0, 12, 10, 0, 12, 2, 13, 13, 10, 12, 13, 2, 3, 13, 3, 4, 14, 13, 4, 14, 4, 5, 13, 9, 10, 9, 13, 14, 15, 9, 14, 6, 14, 5, 15, 14, 6, 8, 9, 15, 7, 15, 6, 8, 15, 7], "vertices": [2, 96, -63.39, 73.1, 0.06118, 94, -43.98, -0.83, 0.93882, 2, 96, -41.71, 89.62, 0.00875, 94, -22.3, 15.69, 0.99125, 2, 95, -14.87, 43.68, 0.30962, 94, 18.58, 16.01, 0.69038, 2, 95, 38, 43.68, 0.91775, 94, 71.45, 16.01, 0.08225, 3, 96, 58.61, 50.8, 0.06028, 95, 44.56, 4.54, 0.93614, 94, 78.02, -23.13, 0.00358, 2, 96, 58.61, 7.02, 0.44259, 95, 44.56, -39.23, 0.55741, 2, 96, 35.52, -21.06, 0.81675, 95, 21.48, -67.32, 0.18325, 1, 96, -4.96, -38.07, 1, 2, 96, -40.06, -38.07, 0.9919, 94, -20.65, -111.99, 0.0081, 3, 96, -44.19, 0.83, 0.8249, 95, -58.23, -45.43, 0.00876, 94, -24.78, -73.1, 0.16634, 3, 96, -54.52, 38.41, 0.26012, 95, -68.56, -7.85, 0.01847, 94, -35.1, -35.52, 0.72141, 3, 96, -63.39, 41.71, 0.19409, 95, -77.44, -4.54, 0.00602, 94, -43.98, -32.21, 0.79989, 3, 96, -31.39, 60.71, 0.08453, 95, -45.43, 14.45, 0.03872, 94, -11.98, -13.22, 0.87675, 3, 96, 6.61, 37.58, 0.17732, 95, -7.43, -8.67, 0.76174, 94, 26.02, -36.34, 0.06094, 3, 96, 11.15, 11.56, 0.6973, 95, -2.89, -34.69, 0.29888, 94, 30.56, -62.36, 0.00382, 2, 96, -6.19, -17.76, 0.9978, 94, 13.22, -91.69, 0.0022], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22, 0, 24, 24, 26, 26, 28, 28, 30], "width": 122, "height": 128}}, "xuanwu011": {"xuanwu011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-93.44, 388.73, -210.39, 385.5, -213.84, 510.46, -96.89, 513.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 125}}, "xuanwu01": {"xuanwu01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232.2, 116.62, -327.58, 101.16, -335.76, 397.05, 224.03, 412.51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 560, "height": 296}}, "xuanwu035": {"xuanwu035": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-23.96, 46.52, -152.91, 42.96, -154.76, 109.93, -25.81, 113.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 67}}, "xuanwu03": {"xuanwu03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [239.83, 57.81, -126.03, 47.7, -130.26, 200.65, 235.6, 210.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 366, "height": 153}}, "xuanwu037": {"xuanwu037": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22.16, 12.96, 49.22, -4.12, 32.66, -30.33, 5.61, -13.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 31}}, "xuanwu032": {"xuanwu032": {"type": "mesh", "uvs": [1, 0.06241, 0.88746, 0, 0.65118, 0, 0.52714, 0.15477, 0.42967, 0.43763, 0.24656, 0.69452, 0.08116, 0.84172, 0, 0.89368, 0, 1, 0.30563, 1, 0.48579, 0.94563, 0.73683, 0.79554, 0.91995, 0.54731, 0.93176, 0.22981, 0.73683, 0.18652, 0.66595, 0.50979, 0.50351, 0.73781, 0.26132, 0.87059], "triangles": [13, 1, 0, 14, 1, 13, 14, 2, 1, 4, 3, 15, 15, 14, 13, 15, 3, 14, 3, 2, 14, 10, 16, 11, 10, 17, 16, 16, 15, 11, 11, 15, 12, 5, 4, 16, 16, 4, 15, 12, 15, 13, 9, 8, 17, 8, 6, 17, 9, 17, 10, 8, 7, 6, 6, 5, 17, 17, 5, 16], "vertices": [2, 75, 19.37, -4.86, 0.112, 34, 27.37, 4.9, 0.888, 2, 75, 14.74, 5.25, 0.76, 34, 26.85, 16.02, 0.24, 2, 74, 27.42, 16.25, 0.31569, 75, -1.73, 17.15, 0.68431, 3, 72, -23.01, -30.23, 0.00023, 74, 10.34, 19.01, 0.90516, 75, -18.36, 12.36, 0.09462, 3, 73, -17.33, -26.85, 0.088, 72, 1.27, -20.23, 0.50019, 74, -15.51, 14.33, 0.41181, 3, 73, 7.75, -15.43, 0.97641, 72, 28.67, -17.29, 0.02311, 74, -42.91, 17.19, 0.00048, 1, 73, 26.59, -11.53, 1, 1, 73, 34.92, -11.16, 1, 1, 73, 39.69, -3.11, 1, 2, 73, 17.08, 10.29, 0.90186, 72, 45.63, 4.17, 0.09814, 2, 73, 1.31, 14.07, 0.10507, 72, 31.86, 12.73, 0.89493, 2, 72, 7.72, 20.36, 0.9956, 74, -30.32, -24.02, 0.0044, 3, 72, -19.09, 17.93, 0.52643, 74, -3.59, -27.26, 0.45812, 75, -11.2, -35.43, 0.01544, 4, 72, -40.85, 0.37, 0.0086, 74, 21.36, -14.65, 0.23131, 75, 5.99, -13.37, 0.48009, 34, 11.77, 2.01, 0.28, 2, 74, 16.6, 1.87, 0.98457, 75, -5.37, -0.47, 0.01543, 3, 72, -7.26, -0.73, 0.67251, 74, -11.25, -6.53, 0.32748, 75, -26.97, -19.96, 1e-05, 1, 72, 17.05, 1.89, 1, 1, 73, 14.55, -1.45, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 2, 28, 28, 30, 30, 32, 32, 34], "width": 86, "height": 88}}, "xuanwu06": {"xuanwu06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [186.85, 56.35, 13.91, 51.57, 11.68, 132.54, 184.61, 137.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 173, "height": 81}}, "xuanwu07": {"xuanwu07": {"type": "mesh", "uvs": [0.5686, 0.55211, 0.40649, 0.46133, 0.34875, 0.30933, 0.37539, 0.11722, 0.47089, 0.00111, 0.65299, 0, 0.83065, 0, 0.975, 0.20377, 0.90172, 0.28822, 0.93503, 0.41911, 0.975, 0.49933, 1, 0.55422, 0.96834, 0.61544, 0.88395, 0.73788, 0.79068, 0.86455, 0.73738, 0.94477, 0.633, 0.95955, 0.52863, 0.95111, 0.37095, 0.98066, 0.2266, 1, 0.07559, 1, 0, 0.93844, 0.0445, 0.84133, 0.13333, 0.76744, 0.2688, 0.75055, 0.40871, 0.74422, 0.51752, 0.72522, 0.55528, 0.65766, 0.56194, 0.59433, 0.10688, 0.90148, 0.27718, 0.86187, 0.44386, 0.8412, 0.57973, 0.82742, 0.68119, 0.74131, 0.72648, 0.63109, 0.76271, 0.55531, 0.69931, 0.44164, 0.61778, 0.2212], "triangles": [32, 26, 33, 31, 25, 26, 31, 26, 32, 30, 24, 25, 30, 25, 31, 23, 24, 30, 29, 22, 23, 29, 23, 30, 21, 22, 29, 17, 31, 32, 16, 17, 32, 15, 16, 32, 18, 30, 31, 18, 31, 17, 20, 21, 29, 19, 29, 30, 19, 30, 18, 20, 29, 19, 28, 0, 34, 27, 28, 34, 13, 35, 12, 34, 35, 13, 33, 27, 34, 33, 34, 13, 26, 27, 33, 14, 33, 13, 32, 33, 14, 15, 32, 14, 37, 4, 5, 3, 4, 37, 8, 6, 7, 2, 3, 37, 37, 5, 6, 8, 37, 6, 36, 37, 8, 36, 8, 9, 1, 2, 37, 1, 37, 36, 0, 1, 36, 35, 36, 9, 35, 9, 10, 0, 36, 35, 35, 10, 11, 12, 35, 11, 34, 0, 35], "vertices": [3, 41, 46.17, -27.67, 0.66471, 42, 11.28, -30.62, 0.32266, 43, 7.17, -45.29, 0.01263, 2, 41, 22.36, -44.17, 0.98181, 42, 9.75, -59.55, 0.01819, 1, 41, -3.73, -41.93, 1, 1, 41, -30.27, -25.16, 1, 1, 41, -41.18, -3.92, 1, 1, 41, -29.59, 21.61, 1, 1, 41, -18.13, 46.46, 1, 1, 41, 21.16, 52.81, 1, 1, 41, 28.85, 36.83, 1, 2, 41, 50.25, 32.6, 0.96216, 42, -33.87, 9.51, 0.03784, 2, 41, 64.63, 32.74, 0.76935, 42, -25.18, 20.96, 0.23065, 2, 41, 74.32, 32.51, 0.6263, 42, -19.06, 28.48, 0.3737, 2, 41, 81.28, 23.93, 0.43034, 42, -8.01, 28.73, 0.56966, 2, 41, 93.85, 3.81, 0.01367, 42, 15.58, 26.35, 0.98633, 2, 42, 40.41, 23.08, 0.92388, 43, -22.1, 8.33, 0.07612, 2, 42, 55.73, 21.79, 0.57225, 43, -12.7, 20.49, 0.42775, 2, 42, 65.27, 8.63, 0.12974, 43, 3.53, 21.35, 0.87026, 1, 43, 19.4, 18.47, 1, 1, 43, 44.03, 20.94, 1, 1, 43, 66.46, 21.96, 1, 1, 43, 89.61, 19.76, 1, 1, 43, 100.25, 8.74, 1, 1, 43, 91.94, -6.28, 1, 1, 43, 77.19, -16.9, 1, 1, 43, 56.17, -17.65, 1, 2, 42, 50.26, -38.11, 0.01181, 43, 34.62, -16.64, 0.98819, 3, 41, 68.34, -46.56, 0.01634, 42, 39.79, -24.66, 0.30729, 43, 17.65, -18.12, 0.67637, 3, 41, 60.84, -36.7, 0.14197, 42, 27.4, -24.55, 0.66012, 43, 10.82, -28.46, 0.19791, 3, 41, 51.95, -31.47, 0.40402, 42, 17.83, -28.37, 0.5457, 43, 8.83, -38.58, 0.05028, 1, 43, 83.3, 4.33, 1, 1, 43, 56.59, 0.42, 1, 2, 42, 61.7, -26.06, 0.00019, 43, 30.72, -0.49, 0.99981, 3, 41, 87.38, -44.8, 8e-05, 42, 50.06, -8.52, 0.01161, 43, 9.67, -0.73, 0.98831, 3, 41, 81.27, -24.77, 0.001, 42, 30.48, -1.09, 0.99187, 43, -7.2, -13.14, 0.00713, 3, 41, 67.98, -10.95, 0.05502, 42, 11.42, -3.14, 0.94415, 43, -15.83, -30.26, 0.00083, 2, 41, 59.17, -0.74, 0.97405, 42, -2.05, -3.86, 0.02595, 2, 41, 38.36, -1.89, 0.99527, 42, -13.88, -21.02, 0.00473, 1, 41, 0.67, 1.67, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 42, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 22, 70, 72, 72, 74], "width": 154, "height": 162}}, "xuanwu08": {"xuanwu08": {"type": "mesh", "uvs": [0.84966, 0.35435, 0.94089, 0.44812, 0.99943, 0.50556, 0.96267, 0.57589, 0.90412, 0.67201, 0.85034, 0.74224, 0.82243, 0.77868, 0.82652, 0.87245, 0.76661, 0.93341, 0.63046, 0.93223, 0.50247, 0.97092, 0.36359, 0.97795, 0.22472, 0.99905, 0.05997, 1, 0, 0.92403, 0.07086, 0.8197, 0.2043, 0.77282, 0.30777, 0.7611, 0.40172, 0.76227, 0.46163, 0.74469, 0.51881, 0.73296, 0.53379, 0.67904, 0.54059, 0.61809, 0.4235, 0.55714, 0.33228, 0.4364, 0.29824, 0.31449, 0.35134, 0.19493, 0.49566, 0.13281, 0.62773, 0, 0.8129, 0.13281, 0.9368, 0.29574, 0.65224, 0.21838, 0.54332, 0.32153, 0.68628, 0.49853, 0.76797, 0.5712, 0.6833, 0.71991, 0.6385, 0.80595, 0.47825, 0.85046, 0.29216, 0.87864, 0.10779, 0.91721], "triangles": [36, 20, 35, 37, 19, 20, 37, 20, 36, 18, 19, 37, 38, 16, 17, 38, 17, 18, 38, 18, 37, 39, 15, 16, 39, 16, 38, 14, 15, 39, 9, 37, 36, 8, 9, 36, 7, 8, 36, 10, 37, 9, 11, 38, 37, 10, 11, 37, 12, 39, 38, 12, 38, 11, 13, 14, 39, 13, 39, 12, 3, 34, 2, 4, 34, 3, 34, 35, 22, 5, 35, 34, 21, 22, 35, 20, 21, 35, 4, 5, 34, 6, 35, 5, 36, 35, 6, 36, 6, 7, 31, 28, 29, 27, 28, 31, 32, 27, 31, 26, 27, 32, 25, 26, 32, 0, 29, 30, 31, 29, 0, 24, 25, 32, 0, 32, 31, 33, 0, 1, 33, 32, 0, 23, 24, 32, 23, 32, 33, 34, 33, 1, 34, 1, 2, 22, 23, 33, 34, 22, 33], "vertices": [2, 47, 35.09, 34.17, 0.96802, 48, -35.41, 10.23, 0.03198, 2, 47, 53.55, 34.56, 0.67551, 48, -24.31, 24.99, 0.32449, 2, 47, 65.08, 35.07, 0.52711, 48, -17.59, 34.37, 0.47289, 2, 47, 70.37, 24.69, 0.37708, 48, -6.17, 32.13, 0.62292, 2, 47, 76.93, 9.67, 0.07671, 48, 9.7, 28.01, 0.92329, 3, 47, 80.83, -2.42, 0.00131, 48, 21.61, 23.6, 0.98816, 49, -27.27, -0.96, 0.01053, 2, 48, 27.79, 21.32, 0.91724, 49, -22.34, 3.42, 0.08276, 2, 48, 41.46, 25.04, 0.60678, 49, -19.19, 17.23, 0.39322, 2, 48, 52.19, 19.55, 0.39334, 49, -9.28, 24.11, 0.60666, 2, 48, 56.04, 2.27, 0.04359, 49, 7.77, 19.36, 0.95641, 1, 49, 25.35, 20.69, 1, 1, 49, 43.07, 17.04, 1, 1, 49, 61.33, 15.44, 1, 1, 49, 82.05, 10.04, 1, 1, 49, 86.61, -3.06, 1, 1, 49, 73.64, -15.89, 1, 1, 49, 55.05, -18.24, 1, 1, 49, 41.6, -16.47, 1, 2, 48, 37.78, -32.51, 0.00292, 49, 29.85, -13.13, 0.99708, 3, 47, 49.49, -42.06, 0.00071, 48, 33.43, -25.53, 0.0515, 49, 21.64, -13.68, 0.94779, 3, 47, 52.76, -35.16, 0.01534, 48, 30.02, -18.69, 0.28395, 49, 14, -13.47, 0.70071, 3, 47, 47.63, -28.54, 0.11538, 48, 21.65, -18.64, 0.63185, 49, 10.01, -20.83, 0.25277, 3, 47, 41, -22.09, 0.46448, 48, 12.49, -19.87, 0.49258, 49, 6.77, -29.49, 0.04294, 3, 47, 24.3, -28.2, 0.91825, 48, 6.97, -36.78, 0.08174, 49, 19.09, -42.32, 1e-05, 2, 47, 2.66, -26.04, 0.99809, 48, -8.09, -52.47, 0.00191, 1, 47, -14.47, -17.97, 1, 1, 47, -24.23, -1.29, 1, 1, 47, -19.8, 19.22, 1, 1, 47, -24.7, 45.16, 1, 1, 47, 6.01, 51.38, 1, 2, 47, 35.28, 48.55, 0.9999, 48, -46.6, 19.26, 0.0001, 1, 47, 3.01, 27, 1, 1, 47, 6.3, 6.21, 1, 1, 47, 38.77, 3.98, 1, 2, 47, 53.98, 5.39, 0.32557, 48, -1.11, 7.31, 0.67443, 1, 48, 23.25, 1.69, 1, 2, 48, 37.23, -1.04, 0.03047, 49, 1.82, 1.21, 0.96953, 1, 49, 23.68, 2.3, 1, 1, 49, 48.15, 0.15, 1, 1, 49, 72.81, -0.43, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 62, 64, 64, 66, 66, 68, 4, 68, 68, 70, 8, 10, 10, 12, 70, 10, 70, 72, 72, 74, 74, 76, 76, 78], "width": 130, "height": 151}}, "xuanwu09": {"xuanwu09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 77, 23.23, -16.85, 1, 1, 76, -6.03, -22.96, 1, 2, 76, -28.21, 11.53, 0.96503, 77, -16.77, 42.83, 0.03497, 2, 76, 21.42, 43.43, 0.03541, 77, 38.17, 21.33, 0.96459], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 59, "height": 41}}, "xuanwu033": {"xuanwu033": {"type": "mesh", "uvs": [0, 0, 0.04733, 0, 0.15278, 0, 0.14479, 0.10573, 0.22787, 0.20394, 0.3461, 0.3292, 0.49628, 0.4573, 0.70557, 0.57686, 0.87493, 0.66938, 1, 0.76475, 1, 0.91136, 0.86854, 1, 0.6209, 1, 0.47072, 1, 0.36687, 0.85442, 0.27261, 0.73201, 0.16077, 0.61245, 0.06651, 0.50142, 0, 0.37617, 0, 0.2196, 0, 0.1043, 0.06171, 0.27795, 0.16716, 0.44449, 0.32533, 0.56405, 0.51226, 0.63095, 0.64646, 0.75336, 0.78226, 0.8957], "triangles": [4, 21, 3, 3, 21, 20, 21, 19, 20, 20, 1, 3, 3, 1, 2, 20, 0, 1, 17, 18, 22, 18, 21, 22, 22, 21, 4, 18, 19, 21, 15, 16, 23, 16, 22, 23, 16, 17, 22, 23, 22, 5, 22, 4, 5, 14, 24, 25, 14, 15, 24, 25, 24, 7, 15, 23, 24, 23, 6, 24, 24, 6, 7, 23, 5, 6, 26, 12, 25, 12, 13, 25, 13, 14, 25, 12, 26, 11, 11, 26, 10, 26, 9, 10, 26, 8, 9, 26, 25, 8, 25, 7, 8], "vertices": [1, 92, -24.46, 76.32, 1, 1, 92, -8.22, 76.32, 1, 1, 92, 27.95, 76.32, 1, 3, 90, -36.17, 150.15, 0.00248, 91, 22.47, 89.32, 0.0036, 92, 25.21, 35.62, 0.99392, 4, 89, -79.46, 134.26, 0.0012, 90, -7.67, 112.34, 0.11568, 91, 50.96, 51.51, 0.26213, 92, 53.7, -2.19, 0.62099, 4, 89, -38.91, 86.04, 0.1343, 90, 32.88, 64.12, 0.55025, 91, 91.52, 3.29, 0.23075, 92, 94.26, -50.42, 0.08469, 5, 87, -109.05, 129.88, 0.00569, 89, 12.6, 36.72, 0.86773, 90, 84.39, 14.8, 0.12354, 91, 143.03, -46.03, 0.0027, 92, 145.77, -99.74, 0.00034, 3, 87, -37.26, 83.84, 0.42485, 88, 50.42, 139.19, 0.03211, 89, 84.39, -9.32, 0.54304, 2, 87, 20.82, 48.22, 0.91896, 89, 142.48, -44.94, 0.08104, 1, 87, 63.72, 11.51, 1, 2, 87, 63.72, -44.94, 0.97897, 88, 151.4, 10.41, 0.02103, 2, 87, 18.63, -79.06, 0.82613, 88, 106.31, -23.72, 0.17387, 2, 87, -66.31, -79.06, 0.13175, 88, 21.37, -23.72, 0.86825, 3, 88, -30.14, -23.72, 0.98277, 89, 3.84, -172.22, 0.01494, 90, 75.62, -194.14, 0.00229, 4, 87, -153.44, -23.02, 0.00025, 88, -65.76, 32.33, 0.62018, 89, -31.78, -116.18, 0.30352, 90, 40, -138.1, 0.07604, 3, 88, -98.09, 79.46, 0.22066, 89, -64.12, -69.05, 0.41936, 90, 7.67, -90.97, 0.35998, 4, 88, -136.45, 125.49, 0.02956, 89, -102.48, -23.02, 0.09628, 90, -30.69, -44.94, 0.83719, 91, 27.95, -105.76, 0.03697, 3, 88, -168.78, 168.24, 0.00015, 90, -63.02, -2.19, 0.56406, 91, -4.38, -63.02, 0.43579, 3, 90, -85.83, 46.03, 0.04889, 91, -27.2, -14.8, 0.94123, 92, -24.46, -68.5, 0.00987, 2, 91, -27.2, 45.48, 0.17019, 92, -24.46, -8.22, 0.82981, 1, 92, -24.46, 36.17, 1, 3, 90, -64.66, 83.84, 0.0006, 91, -6.03, 23.02, 0.59776, 92, -3.29, -30.69, 0.40164, 3, 90, -28.5, 19.73, 0.62062, 91, 30.14, -41.1, 0.37771, 92, 32.88, -94.8, 0.00168, 3, 88, -80.01, 144.12, 0.02992, 89, -46.03, -4.38, 0.40473, 90, 25.76, -26.3, 0.56535, 4, 87, -103.57, 63.02, 0.09609, 88, -15.89, 118.37, 0.10863, 89, 18.08, -30.14, 0.79011, 90, 89.87, -52.06, 0.00517, 4, 87, -57.54, 15.89, 0.43807, 88, 30.14, 71.24, 0.27511, 89, 64.12, -77.27, 0.28673, 90, 135.9, -99.19, 9e-05, 3, 87, -10.96, -38.91, 0.75446, 88, 76.72, 16.44, 0.24306, 89, 110.7, -132.07, 0.00248], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 343, "height": 385}}, "xuanwu02": {"xuanwu02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [169.16, 225.92, -249.68, 214.35, -257.44, 495.25, 161.4, 506.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 419, "height": 281}}, "xuanwu024": {"xuanwu024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [37.27, -27.67, 69.83, 32.03, 115.48, 7.13, 82.92, -52.57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 52}}, "xuanwu05": {"xuanwu05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [246.86, 93.02, 133.9, 89.9, 131.91, 161.87, 244.87, 164.99], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 113, "height": 72}}, "xuanwu036": {"xuanwu036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [30.49, -29.67, 61.61, 27.4, 112.53, -0.38, 81.41, -57.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 65, "height": 58}}, "xuanwu018": {"xuanwu018": {"type": "mesh", "uvs": [0, 0, 0.26968, 0.01117, 0.6073, 0.08476, 1, 0.29376, 1, 0.52042, 1, 0.80301, 1, 1, 0.70778, 1, 0.33399, 0.89426, 0.14509, 0.66466, 0.0647, 0.4586, 0, 0.37618, 0, 0.22017, 0, 0.09359, 0.30586, 0.2967, 0.54701, 0.50276, 0.78816, 0.75591], "triangles": [16, 15, 4, 16, 4, 5, 7, 8, 16, 7, 16, 5, 7, 5, 6, 15, 14, 2, 15, 2, 3, 15, 3, 4, 9, 10, 15, 8, 9, 15, 8, 15, 16, 13, 0, 1, 14, 12, 13, 14, 1, 2, 14, 13, 1, 11, 12, 14, 10, 11, 14, 10, 14, 15], "vertices": [1, 83, -8.6, -4.52, 1, 3, 83, -1.5, 7.6, 0.95853, 84, -29.35, 3.44, 0.04099, 85, -49.63, 10.43, 0.00048, 3, 83, 11.15, 20.85, 0.39219, 84, -18.59, 18.27, 0.54967, 85, -36.9, 23.59, 0.05814, 3, 83, 33.66, 32.27, 0.00064, 84, 2.19, 32.6, 0.65935, 85, -14.31, 34.86, 0.34, 2, 84, 17.37, 27.26, 0.35682, 85, -0.03, 27.44, 0.64318, 2, 84, 36.29, 20.59, 0.00407, 85, 17.77, 18.18, 0.99593, 1, 85, 30.18, 11.73, 1, 2, 84, 44.44, 1.61, 0.00036, 85, 23.17, -1.75, 0.99964, 3, 83, 55.83, -17.99, 0.00596, 84, 30.9, -14.23, 0.73878, 85, 7.54, -15.53, 0.25527, 2, 83, 36.85, -19.3, 0.25543, 84, 12.26, -18.08, 0.74457, 2, 83, 21.92, -16.36, 0.88743, 84, -2.92, -17.16, 0.11257, 2, 83, 15.18, -16.69, 0.99197, 84, -9.56, -18.39, 0.00803, 1, 83, 5.32, -11.64, 1, 1, 83, -2.68, -7.54, 1, 3, 83, 17.4, 0.04, 0.99885, 84, -9.6, -1.51, 0.00112, 85, -30.78, 2.74, 3e-05, 2, 84, 8.36, 5.45, 0.84843, 85, -12.01, 7.12, 0.15157, 2, 84, 29.48, 11.31, 0.00372, 85, 9.72, 9.95, 0.99628], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 26, 28, 28, 30, 30, 32], "width": 52, "height": 71}}, "xuanwu012": {"xuanwu012": {"type": "mesh", "uvs": [0, 0.92751, 0.05379, 1, 0.1045, 1, 0.13773, 0.92928, 0.23564, 0.96466, 0.37028, 0.98411, 0.54864, 0.94874, 0.73224, 0.88329, 0.88087, 0.76479, 0.97529, 0.61798, 1, 0.46055, 1, 0.2766, 0.89136, 0.12802, 0.69902, 0.03781, 0.53465, 0.00421, 0.38952, 0.00244, 0.31083, 0.07673, 0.25313, 0.14748, 0.22865, 0.25361, 0.23739, 0.30667, 0.18494, 0.3279, 0.11325, 0.34381, 0.11499, 0.42164, 0.17969, 0.49593, 0.2916, 0.45879, 0.407, 0.46586, 0.48569, 0.48178, 0.5399, 0.42341, 0.56962, 0.34912, 0.5941, 0.29783, 0.6623, 0.34735, 0.68678, 0.43402, 0.65705, 0.53661, 0.57312, 0.61974, 0.43323, 0.6622, 0.28286, 0.65512, 0.14297, 0.66396, 0.03281, 0.70642, 0, 0.80547, 0.11499, 0.78778, 0.25488, 0.78601, 0.42099, 0.78424, 0.59585, 0.74356, 0.72, 0.6675, 0.76896, 0.51008, 0.79519, 0.33143, 0.70076, 0.17932, 0.57836, 0.15809, 0.38253, 0.22531, 0.30209, 0.35796, 0.06079, 0.90452], "triangles": [48, 16, 15, 17, 16, 48, 47, 48, 15, 47, 15, 14, 18, 17, 48, 19, 18, 48, 28, 48, 29, 49, 19, 48, 48, 25, 49, 22, 21, 20, 49, 22, 20, 49, 20, 19, 28, 27, 48, 24, 22, 49, 25, 24, 49, 48, 27, 25, 26, 25, 27, 23, 22, 24, 47, 14, 13, 46, 13, 12, 47, 13, 46, 29, 47, 46, 48, 47, 29, 45, 46, 12, 45, 12, 11, 30, 29, 46, 45, 30, 46, 31, 30, 45, 45, 11, 10, 44, 31, 45, 44, 45, 10, 32, 31, 44, 9, 44, 10, 43, 32, 44, 43, 44, 9, 33, 32, 43, 8, 43, 9, 7, 43, 8, 42, 33, 43, 34, 33, 42, 41, 35, 34, 41, 34, 42, 40, 36, 35, 40, 35, 41, 39, 37, 36, 39, 36, 40, 42, 43, 7, 3, 39, 40, 6, 41, 42, 6, 42, 7, 4, 3, 40, 5, 40, 41, 5, 41, 6, 4, 40, 5, 38, 37, 39, 50, 38, 39, 0, 38, 50, 50, 39, 3, 1, 0, 50, 2, 1, 50, 3, 2, 50], "vertices": [1, 56, 29.98, 10.17, 1, 1, 56, 21.66, -3.12, 1, 2, 56, 24.86, -11.4, 0.99692, 57, -7.13, -34.96, 0.00308, 2, 56, 38.37, -12.42, 0.79591, 57, 0.54, -23.8, 0.20409, 2, 56, 38.82, -30.61, 0.2333, 57, 16.49, -32.54, 0.7667, 2, 56, 44.17, -53.81, 0.03246, 57, 39.23, -39.58, 0.96754, 2, 57, 71.02, -38.46, 0.9926, 58, -44.07, -4.28, 0.0074, 2, 57, 104.53, -32.34, 0.56685, 58, -19.73, -28.11, 0.43315, 2, 57, 133.45, -16.19, 0.04009, 58, 10.14, -42.4, 0.95991, 2, 58, 40.23, -46.02, 0.99712, 59, -49.09, 6.76, 0.00288, 2, 58, 66.57, -37.88, 0.80347, 59, -34.86, -16.85, 0.19653, 2, 58, 95.13, -23.84, 0.25664, 59, -14.36, -41.2, 0.74336, 2, 58, 109.81, 4.56, 0.0128, 59, 16.74, -48.61, 0.9872, 2, 59, 52.54, -38.87, 0.84698, 60, -38.57, -9.54, 0.15302, 2, 59, 78.29, -24.78, 0.22197, 60, -17.35, -29.82, 0.77803, 2, 59, 97.91, -8.66, 0.00218, 60, 3.96, -43.64, 0.99782, 1, 60, 22.46, -40.13, 1, 1, 60, 37.54, -35.17, 1, 1, 60, 50.96, -21.93, 1, 1, 60, 54.57, -13.35, 1, 1, 60, 64.29, -15.15, 1, 1, 60, 76.37, -19.52, 1, 1, 60, 83.3, -7.98, 1, 1, 60, 80.59, 8.94, 1, 2, 59, 60.17, 62.77, 0.00181, 60, 60.6, 13.96, 0.99819, 2, 59, 43.93, 50.69, 0.05635, 60, 44.18, 25.78, 0.94365, 2, 59, 31.62, 43.93, 0.12514, 60, 34.01, 35.46, 0.87486, 2, 59, 30.87, 30.1, 0.2195, 60, 20.6, 31.99, 0.7805, 2, 59, 35.17, 16.92, 0.47539, 60, 9.34, 23.9, 0.52461, 2, 59, 37.61, 7.37, 0.87207, 60, 0.98, 18.69, 0.12793, 2, 58, 58.07, 23.79, 0.01231, 59, 22.96, 6.23, 0.98769, 2, 58, 46.5, 13.33, 0.63777, 59, 10.03, 14.94, 0.36223, 3, 57, 100.99, 28.96, 0.00315, 58, 28.28, 10.16, 0.99593, 59, 2.58, 31.87, 0.00092, 2, 57, 84.22, 17.07, 0.53337, 58, 8.89, 17, 0.46663, 1, 57, 58.89, 13.68, 1, 1, 57, 33.09, 19.04, 1, 2, 56, 81.52, 3.24, 0.02833, 57, 8.68, 21.38, 0.97167, 2, 56, 67.73, 18.59, 0.5843, 57, -11.52, 17.17, 0.4157, 2, 56, 49.67, 17.77, 0.988, 57, -19.89, 1.15, 0.012, 2, 56, 59.77, 0.1, 0.3814, 57, 0.47, 1, 0.6186, 2, 56, 68.87, -22.63, 0.00633, 57, 24.69, -2.55, 0.99367, 1, 57, 53.44, -6.83, 1, 1, 57, 84.77, -4.71, 1, 2, 57, 108.3, 4.86, 0.00148, 58, 12.82, -9.71, 0.99852, 1, 58, 41.04, -5.38, 1, 2, 58, 70.8, 4.14, 0.02874, 59, 6.95, -10.85, 0.97126, 2, 59, 36.54, -20.34, 0.98556, 60, -25.75, 11.32, 0.01444, 2, 59, 55.29, -9.35, 0.66467, 60, -9.6, -3.22, 0.33533, 1, 60, 25.58, -11.69, 1, 2, 59, 70, 48.24, 4e-05, 60, 49.74, 0.19, 0.99996, 1, 56, 37.52, 1.68, 1], "hull": 39, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 0, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 44, 78, 100], "width": 175, "height": 173}}, "xuanwu014": {"xuanwu014": {"type": "mesh", "uvs": [0.79263, 0.65946, 0.73105, 0.46561, 0.64259, 0.39117, 0.43209, 0.18181, 0.23349, 0.2181, 0.11511, 0.31882, 0.06269, 0.44296, 0.0593, 0.53196, 0.08636, 0.63034, 0.03901, 0.67952, 0.01703, 0.78492, 0.09651, 0.9114, 0.20643, 0.9887, 0.35694, 1, 0.47532, 1, 0.57848, 0.95825, 0.71546, 0.89267, 0.79325, 0.79195, 0.86251, 0.69561, 0.90228, 0.63266, 0.94327, 0.61085, 0.9759, 0.56147, 0.98728, 0.49632, 0.99107, 0.41751, 0.99031, 0.30717, 1, 0.23362, 1, 0.17267, 0.95769, 0.10122, 0.89092, 0.0781, 0.81657, 0.09281, 0.74829, 0.14955, 0.69291, 0.22206, 0.69367, 0.31453, 0.7096, 0.407, 0.74374, 0.45744, 0.78167, 0.50998, 0.83023, 0.53835, 0.86892, 0.54465, 0.8841, 0.57197, 0.87423, 0.61716, 0.83933, 0.65499, 0.80595, 0.67285, 0.9437, 0.19148, 0.87323, 0.20556, 0.83461, 0.27313, 0.84071, 0.37636, 0.88611, 0.44206, 0.93422, 0.49462, 0.92676, 0.55656, 0.90102, 0.59222, 0.8834, 0.63164, 0.84748, 0.67481, 0.79802, 0.72643, 0.66886, 0.78109, 0.50606, 0.8054, 0.35397, 0.8108, 0.18355, 0.776], "triangles": [11, 56, 12, 11, 10, 56, 56, 10, 9, 56, 9, 8, 56, 4, 55, 55, 4, 3, 56, 8, 4, 4, 8, 5, 7, 6, 8, 8, 6, 5, 12, 55, 13, 54, 14, 55, 14, 13, 55, 12, 56, 55, 55, 3, 54, 15, 14, 54, 15, 53, 16, 15, 54, 53, 54, 2, 53, 54, 3, 2, 16, 53, 17, 17, 52, 18, 17, 53, 52, 53, 0, 52, 53, 1, 0, 53, 2, 1, 51, 52, 41, 52, 0, 41, 52, 51, 18, 51, 41, 40, 18, 50, 19, 18, 51, 50, 50, 40, 39, 50, 51, 40, 50, 49, 19, 19, 49, 20, 50, 39, 49, 39, 38, 49, 49, 48, 20, 20, 48, 21, 49, 38, 48, 38, 37, 48, 48, 47, 21, 21, 47, 22, 48, 37, 47, 37, 46, 47, 37, 36, 46, 46, 36, 45, 36, 35, 45, 35, 34, 45, 22, 47, 23, 47, 46, 23, 34, 33, 45, 46, 45, 24, 46, 24, 23, 33, 32, 45, 32, 44, 45, 24, 45, 44, 44, 31, 30, 44, 32, 31, 44, 30, 43, 24, 43, 42, 44, 43, 24, 24, 42, 25, 43, 30, 29, 42, 26, 25, 43, 28, 42, 43, 29, 28, 42, 27, 26, 42, 28, 27], "vertices": [6, 35, 2.52, 80.29, 0, 34, -2.46, 35.06, 0.00884, 33, 76.51, 24.7, 0.97224, 32, 152.18, 41.59, 0.01656, 4, -155.76, -85.04, 0.00019, 5, -180.22, -212.46, 0.00217, 4, 33, 66.43, 103.26, 0.68837, 32, 125.49, 116.17, 0.23415, 4, -109.79, -149.53, 0.0354, 5, -107.09, -242.89, 0.04208, 4, 33, 30.36, 142.93, 0.44318, 32, 81.75, 147.18, 0.32952, 4, -59.26, -167.47, 0.10985, 5, -54.72, -231.4, 0.11744, 4, 33, -52.06, 248.89, 0.10097, 32, -21.47, 233, 0.19161, 4, 63.42, -221.96, 0.19966, 5, 78.22, -212.78, 0.50776, 4, 33, -154.57, 264.99, 0.02631, 32, -125.05, 226.75, 0.06941, 4, 161.39, -187.76, 0.09932, 5, 143.29, -131.94, 0.80496, 4, 33, -224.04, 246.22, 0.00504, 32, -188.88, 193.52, 0.01787, 4, 213.77, -138.4, 0.02282, 5, 161.65, -62.36, 0.95427, 4, 33, -263.23, 209.37, 0.00031, 32, -219.26, 149.13, 0.00177, 4, 230.91, -87.42, 0.0007, 5, 149.25, -10.02, 0.99722, 1, 5, 127.53, 15.27, 1, 1, 5, 92, 32.21, 1, 1, 5, 97.04, 62.45, 1, 2, 4, 228.06, 42.61, 0.00946, 5, 78.07, 98.84, 0.99054, 2, 4, 178.13, 80.55, 0.18406, 5, 15.63, 104.64, 0.81594, 2, 4, 116.5, 97.28, 0.64061, 5, -45.53, 86.25, 0.35939, 3, 32, -81.96, -69.48, 0.02955, 4, 39.31, 85.56, 0.9611, 5, -104.85, 35.49, 0.00935, 2, 32, -20.8, -73.82, 0.41751, 4, -20.73, 73.09, 0.58249, 3, 33, -61.51, -51.08, 0.00437, 32, 33.61, -62.02, 0.9233, 4, -69.87, 46.94, 0.07234, 2, 33, 13.48, -47.64, 0.75351, 32, 106.13, -42.58, 0.24649, 1, 33, 62.79, -22.92, 1, 2, 34, 5.41, -2.77, 0.96024, 33, 107.4, 1.49, 0.03976, 1, 34, 36.31, -7.64, 1, 2, 35, -25.64, 5.28, 0.026, 34, 54.54, -21.25, 0.974, 4, 37, 6.52, -146.73, 0.00246, 36, -61.22, -80.16, 0.00062, 35, -19.64, -19.03, 0.43823, 34, 79.17, -25.69, 0.55869, 4, 37, 21.16, -126.38, 0.02351, 36, -36.26, -82.43, 0.0169, 35, -2.64, -37.45, 0.82583, 34, 102.93, -17.68, 0.13376, 4, 37, 34.08, -99.81, 0.10975, 36, -6.81, -80.07, 0.08998, 35, 20.8, -55.43, 0.79857, 34, 128.91, -3.62, 0.00169, 3, 37, 49.26, -61.44, 0.46154, 36, 33.95, -73.64, 0.1843, 35, 55.35, -77.99, 0.35416, 3, 37, 64.27, -37.85, 0.80079, 36, 61.9, -74.59, 0.08503, 35, 75.46, -97.43, 0.11418, 3, 37, 72.86, -16.73, 0.94841, 36, 84.45, -71.25, 0.02063, 35, 94.42, -110.07, 0.03096, 1, 37, 62.62, 16.28, 1, 1, 37, 33.84, 37.32, 1, 3, 37, -3.91, 46.72, 0.69471, 36, 100.1, 27.11, 0.30529, 33, 148.38, 224.45, 0, 3, 37, -44.67, 40.39, 0.16109, 36, 73.94, 59, 0.83891, 33, 108.45, 214.11, 0, 4, 37, -81.46, 26.07, 0.01065, 36, 42.91, 83.41, 0.98323, 35, 167.29, 32.53, 0.00612, 33, 73.26, 196.22, 0, 3, 36, 8.76, 77.97, 0.90988, 35, 138.3, 51.39, 0.09012, 33, 63.85, 162.94, 0, 2, 36, -24.25, 64.74, 0.6547, 35, 104.94, 63.7, 0.3453, 2, 36, -40.32, 44.49, 0.37357, 35, 79.44, 59.45, 0.62643, 3, 36, -56.89, 22.18, 0.0909, 35, 52.19, 54, 0.90904, 34, 41.8, 69.68, 6e-05, 4, 36, -63.7, -4.26, 0.00113, 35, 29.41, 38.96, 0.96725, 34, 46.24, 42.74, 0.03162, 33, 108.01, 62.63, 0, 3, 35, 16.33, 23.59, 0.76048, 34, 54.93, 24.53, 0.23952, 33, 126.56, 54.7, 0, 5, 35, 3.47, 22.72, 0.23962, 34, 50.48, 12.43, 0.76038, 33, 131.21, 42.67, 0, 32, 201.75, 70.87, 0, 5, -191.91, -268.84, 0, 3, 34, 33.46, 7.74, 1, 32, 195.46, 54.38, 0, 5, -199.88, -253.08, 0, 5, 35, -9.5, 59.23, 7e-05, 34, 11.85, 15.48, 0.71237, 33, 100.18, 19.46, 0.28754, 32, 176.43, 41.55, 1e-05, 5, -196.57, -230.37, 1e-05, 6, 35, -5.47, 77.33, 1e-05, 34, -3.02, 26.55, 0.05955, 33, 81.71, 17.94, 0.93521, 32, 158.7, 36.11, 0.00455, 4, -163.54, -81.53, 0, 5, -188.67, -213.6, 0.00068, 3, 37, 43.19, -12.26, 0.94218, 36, 73.23, -43.43, 0.03255, 35, 104.74, -81.91, 0.02527, 3, 37, 7.39, -3.39, 0.8685, 36, 62.68, -8.09, 0.1291, 35, 120.61, -48.62, 0.0024, 2, 36, 34.75, 8, 1, 33, 138.26, 157.13, 0, 2, 36, -2.98, -0.77, 0.36322, 35, 76.81, 0.84, 0.63678, 3, 37, -19.74, -87.84, 0.0058, 36, -23.85, -27.62, 0.01122, 35, 43.32, -5.1, 0.98298, 4, 37, -4.06, -115.44, 0.01057, 36, -39.65, -55.15, 0.00812, 35, 13.14, -14.93, 0.97003, 34, 88.81, 5.9, 0.01127, 2, 35, -4, 1.13, 0.23499, 34, 67.15, -3.19, 0.76501, 3, 35, -7.69, 19.63, 0.01084, 34, 48.75, 0.98, 0.98916, 33, 137.47, 32.93, 0, 5, 35, -14.9, 35.4, 1e-05, 34, 31.42, 0.84, 0.99995, 33, 124.55, 21.37, 4e-05, 32, 199.81, 48.64, 0, 5, -207.05, -252.44, 0, 4, 35, -18.01, 59.83, 4e-05, 34, 7.84, 7.96, 0.73801, 33, 102.14, 11.15, 0.26195, 5, -204.74, -227.92, 0, 4, 35, -19.86, 91.86, 0, 33, 72.1, -0.11, 0.99997, 32, 153.19, 16.41, 3e-05, 5, -199.53, -196.26, 1e-05, 3, 33, 2.14, -0.79, 0.66364, 32, 85.01, 0.75, 0.33634, 5, -165.23, -135.29, 2e-05, 1, 32, 0.24, -2.34, 1, 1, 4, 55.2, 16.59, 1, 2, 4, 144.28, 21.8, 0.11831, 5, 17.96, 36.88, 0.88169], "hull": 42, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 0, 82, 50, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 16], "width": 518, "height": 374}}, "xuanwu021": {"xuanwu021": {"type": "mesh", "uvs": [0.2723, 0.73617, 0.27298, 0.70956, 0.24862, 0.67605, 0.20464, 0.6593, 0.18434, 0.71942, 0.12886, 0.74504, 0.07473, 0.76672, 0.03684, 0.72237, 0.03346, 0.6524, 0.01071, 0.58653, 0, 0.52784, 0.00892, 0.4848, 0.04474, 0.37523, 0.10743, 0.3948, 0.15937, 0.41175, 0.18534, 0.37001, 0.2131, 0.27219, 0.25251, 0.21349, 0.31967, 0.17175, 0.40474, 0.16653, 0.49519, 0.17697, 0.56504, 0.26175, 0.61877, 0.40001, 0.61071, 0.52001, 0.58833, 0.65306, 0.51668, 0.71045, 0.45042, 0.77958, 0.39131, 0.88001, 0.36355, 0.96479, 0.28116, 0.96479, 0.23818, 0.96479, 0.17907, 1, 0.17997, 0.90479, 0.16743, 0.7861, 0.20146, 0.77566, 0.21131, 0.80958, 0.2337, 0.80306, 0.25071, 0.76393, 0.29705, 0.69012, 0.32684, 0.65573, 0.23554, 0.88366, 0.27982, 0.80756, 0.32233, 0.73533, 0.38255, 0.67342, 0.30639, 0.8785, 0.36129, 0.79853, 0.42682, 0.69921, 0.46402, 0.64504, 0.37458, 0.59215, 0.32321, 0.57797, 0.23466, 0.56765, 0.13459, 0.59731, 0.06994, 0.6244, 0.05223, 0.48768, 0.06197, 0.56894, 0.1647, 0.46446, 0.16824, 0.53669, 0.21872, 0.48897, 0.27805, 0.45543, 0.36572, 0.45672, 0.4649, 0.48639, 0.54549, 0.50702, 0.20189, 0.42318, 0.26831, 0.36256, 0.36218, 0.3445, 0.46756, 0.3316, 0.55523, 0.3574], "triangles": [1, 2, 38, 31, 32, 30, 29, 44, 28, 28, 44, 27, 44, 29, 40, 32, 40, 30, 29, 30, 40, 32, 35, 40, 35, 33, 34, 35, 32, 33, 35, 36, 40, 40, 41, 44, 40, 36, 41, 44, 45, 27, 27, 45, 26, 44, 41, 45, 36, 37, 41, 37, 0, 41, 41, 42, 45, 42, 0, 38, 42, 41, 0, 45, 46, 26, 45, 43, 46, 45, 42, 43, 0, 1, 38, 42, 39, 43, 42, 38, 39, 46, 43, 47, 39, 48, 43, 43, 48, 47, 48, 59, 60, 39, 38, 49, 39, 49, 48, 5, 6, 52, 6, 7, 52, 5, 51, 4, 5, 52, 51, 7, 8, 52, 4, 51, 3, 3, 50, 2, 38, 2, 49, 2, 50, 49, 3, 51, 50, 8, 9, 52, 9, 54, 52, 52, 54, 51, 51, 56, 50, 56, 51, 54, 56, 54, 55, 55, 13, 14, 53, 13, 55, 49, 59, 48, 54, 9, 10, 54, 10, 53, 53, 10, 11, 50, 58, 49, 49, 58, 59, 55, 54, 53, 56, 57, 50, 50, 57, 58, 56, 55, 57, 55, 62, 57, 57, 62, 58, 11, 12, 53, 53, 12, 13, 55, 14, 62, 58, 64, 59, 62, 63, 58, 58, 63, 64, 14, 15, 62, 62, 15, 63, 15, 16, 63, 25, 46, 47, 25, 26, 46, 25, 47, 24, 47, 61, 24, 24, 61, 23, 47, 60, 61, 47, 48, 60, 23, 61, 22, 61, 66, 22, 61, 60, 66, 60, 65, 66, 60, 59, 65, 59, 64, 65, 66, 21, 22, 16, 17, 63, 63, 17, 64, 66, 65, 21, 17, 18, 64, 64, 19, 65, 64, 18, 19, 65, 20, 21, 65, 19, 20], "vertices": [2, 8, 33.26, 29.97, 0.10497, 9, 31.92, -11.39, 0.89503, 2, 8, 33.22, 26.3, 0.27441, 9, 28.99, -13.6, 0.72559, 2, 8, 38.23, 21.8, 0.728, 9, 28.51, -20.32, 0.272, 1, 8, 47.13, 19.72, 1, 1, 8, 50.99, 28.12, 1, 2, 8, 62.05, 31.94, 0.99761, 9, 51.11, -32.94, 0.00239, 1, 8, 72.85, 35.21, 1, 1, 8, 80.62, 29.29, 1, 1, 8, 81.55, 19.65, 1, 1, 8, 86.35, 10.69, 1, 1, 8, 88.71, 2.64, 1, 1, 8, 87.08, -3.34, 1, 1, 8, 80.27, -18.64, 1, 2, 7, 89.96, 51.13, 0.00613, 8, 67.6, -16.27, 0.99387, 2, 7, 82.9, 43.09, 0.0584, 8, 57.11, -14.2, 0.9416, 2, 7, 85.46, 35.74, 0.19327, 8, 52.04, -20.09, 0.80673, 2, 7, 94.64, 24.38, 0.4721, 8, 46.81, -33.73, 0.5279, 2, 7, 97.96, 13.55, 0.64342, 8, 39.1, -42.03, 0.35658, 2, 7, 96.55, -1.06, 0.84009, 8, 25.75, -48.14, 0.15991, 3, 7, 88.99, -16.42, 0.97913, 8, 8.68, -49.3, 0.02087, 9, -45.8, -40.5, 0, 2, 7, 79.02, -31.69, 0.99997, 9, -56.18, -25.51, 3e-05, 2, 7, 62.03, -38.41, 0.99985, 9, -56.01, -7.24, 0.00015, 2, 7, 40.11, -38.76, 0.99949, 9, -48.06, 13.2, 0.00051, 2, 7, 26.35, -29.4, 0.9989, 9, -34.21, 22.42, 0.0011, 2, 7, 12.38, -16.66, 0.99741, 9, -17.15, 30.55, 0.00259, 1, 7, 12.33, -0.23, 1, 2, 7, 10.33, 16.03, 0.36933, 9, 13.91, 20.13, 0.63067, 2, 7, 3.85, 33.1, 0.02801, 9, 32.16, 19.7, 0.97199, 2, 7, -3.75, 43.6, 0.0007, 9, 44.75, 22.78, 0.9993, 1, 9, 55.23, 9.95, 1, 1, 9, 60.7, 3.26, 1, 1, 9, 71.97, -2.86, 1, 1, 9, 61.68, -11.04, 1, 1, 9, 50.59, -23.35, 1, 2, 8, 47.35, 35.79, 0, 9, 45.15, -18.96, 1, 2, 8, 45.25, 40.41, 2e-05, 9, 47.52, -14.47, 0.99998, 2, 8, 40.77, 39.4, 0.00196, 9, 43.98, -11.55, 0.99804, 2, 8, 37.5, 33.91, 0.02234, 9, 37.63, -12.32, 0.97766, 2, 8, 28.45, 23.49, 0.27361, 9, 23.85, -11.55, 0.72639, 2, 8, 22.59, 18.59, 0.29099, 9, 16.39, -9.92, 0.70901, 1, 9, 52.36, -4.23, 1, 2, 8, 31.49, 39.78, 0.00528, 9, 38.6, -3.98, 0.99472, 2, 8, 23.21, 29.59, 0.03942, 9, 25.47, -3.67, 0.96058, 2, 7, 29.72, 21, 0.00156, 9, 11.2, 0.3, 0.99844, 2, 7, 12.21, 47.99, 0.00016, 9, 42.8, 6.35, 0.99984, 2, 7, 16.61, 33.01, 0.02466, 9, 27.27, 7.91, 0.97534, 2, 7, 22.34, 14.89, 0.28178, 9, 8.32, 9.44, 0.71822, 2, 7, 25.32, 4.74, 0.76446, 9, -2.2, 10.5, 0.23554, 1, 8, 13.22, 9.57, 1, 1, 8, 23.59, 7.88, 1, 1, 8, 41.42, 6.92, 1, 2, 8, 61.42, 11.53, 0.9958, 9, 34.59, -44.95, 0.0042, 1, 8, 74.32, 15.6, 1, 1, 8, 78.36, -3.17, 1, 1, 8, 76.12, 7.99, 1, 2, 7, 76.01, 45.63, 0.03015, 8, 55.85, -6.95, 0.96985, 3, 7, 66.91, 49.78, 0.0035, 8, 54.88, 2.99, 0.9927, 9, 23.84, -45, 0.0038, 3, 7, 67.84, 37.72, 0.03084, 8, 44.91, -3.85, 0.96887, 9, 12.32, -41.31, 0.00028, 2, 7, 66.19, 25.03, 0.13028, 8, 33.1, -8.79, 0.86972, 2, 7, 57.59, 9.65, 0.46128, 8, 15.48, -9.07, 0.53872, 2, 7, 44.45, -5.9, 0.99998, 9, -19.26, -3.21, 2e-05, 2, 7, 34.2, -18.75, 0.99935, 9, -27.31, 11.13, 0.00065, 2, 7, 77.43, 36.34, 0.11417, 8, 48.52, -12.84, 0.88583, 2, 7, 78.38, 20.61, 0.39693, 8, 35.39, -21.55, 0.60307, 2, 7, 71.53, 2.85, 0.86285, 8, 16.6, -24.53, 0.13715, 2, 7, 62.95, -16.59, 0.99995, 9, -36.14, -16.31, 5e-05, 2, 7, 51.39, -30.36, 0.99973, 9, -44.54, -0.41, 0.00027], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 0, 76, 76, 78, 60, 80, 80, 82, 82, 84, 84, 86, 80, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 16, 24, 106, 106, 108, 110, 112, 114, 116, 116, 118, 118, 120, 120, 122, 110, 124, 124, 126, 126, 128, 128, 130, 130, 132], "width": 201, "height": 138}}, "xuanwu022": {"xuanwu022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [3, 11, 66.37, -50.62, 0.48545, 12, 38.63, -50.28, 0.16763, 13, -38.69, -34.64, 0.34692, 2, 10, -24.27, -1.63, 0.98635, 11, -31.42, -35.24, 0.01365, 2, 10, 38.33, 41.47, 0.99809, 11, -19.61, 39.84, 0.00191, 1, 13, 33.2, -10, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 99, "height": 76}}, "xuanwu023": {"xuanwu023": {"type": "mesh", "uvs": [0, 0.7972, 0.03759, 0.68295, 0.15309, 0.61108, 0.25359, 0.51525, 0.33159, 0.33833, 0.43959, 0.21118, 0.58959, 0.10061, 0.76509, 0.04163, 0.89559, 0.06559, 0.96309, 0, 1, 0, 0.9902, 0.12733, 0.9162, 0.23298, 0.8022, 0.26738, 0.7142, 0.36813, 0.8242, 0.4099, 0.9102, 0.48607, 0.9902, 0.54013, 0.9682, 0.64578, 0.8422, 0.69001, 0.7262, 0.65807, 0.6162, 0.72441, 0.5322, 0.8399, 0.4622, 0.9259, 0.3442, 1, 0.1902, 1, 0.0642, 0.94555, 0, 0.87675, 0.06856, 0.83347, 0.18637, 0.81219, 0.31978, 0.7313, 0.42026, 0.60784, 0.52768, 0.46097, 0.63857, 0.39924, 0.90365, 0.15659, 0.76331, 0.15446, 0.63164, 0.23109, 0.47051, 0.34177, 0.36309, 0.49503, 0.27473, 0.64829, 0.16731, 0.72279, 0.20369, 0.91223, 0.34403, 0.85263, 0.47051, 0.70363, 0.57619, 0.5972, 0.69054, 0.50993, 0.82222, 0.55463], "triangles": [10, 34, 9, 34, 8, 9, 35, 6, 7, 35, 7, 8, 10, 11, 34, 35, 8, 34, 36, 6, 35, 5, 6, 36, 12, 34, 11, 13, 35, 34, 13, 34, 12, 36, 35, 13, 14, 36, 13, 33, 36, 14, 37, 36, 33, 45, 33, 14, 45, 14, 15, 46, 45, 15, 46, 15, 16, 45, 32, 33, 18, 16, 17, 46, 16, 18, 20, 45, 46, 44, 45, 20, 19, 46, 18, 20, 46, 19, 37, 5, 36, 4, 5, 37, 32, 37, 33, 38, 4, 37, 38, 37, 32, 3, 4, 38, 45, 44, 32, 31, 38, 32, 31, 32, 44, 39, 3, 38, 39, 38, 31, 43, 31, 44, 21, 44, 20, 2, 3, 39, 40, 2, 39, 1, 2, 40, 43, 44, 21, 30, 39, 31, 30, 31, 43, 30, 29, 40, 30, 40, 39, 28, 1, 40, 28, 40, 29, 0, 1, 28, 22, 43, 21, 42, 30, 43, 23, 42, 43, 29, 30, 42, 27, 0, 28, 41, 29, 42, 28, 29, 41, 22, 23, 43, 26, 27, 28, 26, 28, 41, 25, 26, 41, 24, 42, 23, 41, 42, 24, 25, 41, 24], "vertices": [1, 14, -14.16, 2.16, 1, 1, 14, -8.36, 8.55, 1, 2, 14, 2.7, 9.84, 0.96214, 15, -12.4, 18.12, 0.03786, 2, 14, 13.12, 13.15, 0.49423, 15, -1.69, 15.91, 0.50577, 3, 14, 23.69, 22.46, 0.01056, 15, 12.08, 18.85, 0.96591, 16, -19.62, 9.06, 0.02352, 3, 14, 35.48, 27.61, 0, 15, 24.88, 17.58, 0.64098, 16, -8.51, 15.54, 0.35902, 3, 14, 50.26, 30.43, 4e-05, 15, 39.15, 12.8, 0.07698, 16, 5.85, 20.05, 0.92298, 2, 14, 65.85, 29.09, 0.00016, 16, 21.49, 20.53, 0.99984, 2, 14, 75.81, 23.65, 0.00023, 16, 32.01, 16.27, 0.99977, 2, 14, 82.84, 25.95, 0.00025, 16, 38.72, 19.38, 0.99975, 2, 14, 85.82, 24.86, 0.00025, 16, 41.81, 18.63, 0.99975, 2, 14, 81.95, 16.78, 0.00025, 16, 38.91, 10.17, 0.99975, 3, 14, 73.43, 12.04, 0.00022, 15, 50.35, -14.58, 0, 16, 30.99, 4.47, 0.99978, 3, 14, 63.39, 13.16, 0.00015, 15, 42.15, -8.69, 1e-05, 16, 20.89, 4.42, 0.99984, 3, 14, 53.86, 9.15, 0.00013, 15, 31.87, -7.52, 0.00058, 16, 11.88, -0.67, 0.99929, 2, 14, 61.73, 3.14, 2e-05, 16, 20.4, -5.72, 0.99998, 3, 14, 66.83, -4.41, 1e-05, 15, 36.55, -25.69, 4e-05, 16, 26.34, -12.64, 0.99995, 1, 16, 32.14, -17.93, 1, 3, 14, 67.65, -16.63, 0.00038, 15, 31.29, -36.75, 0.00162, 16, 28.57, -24.67, 0.998, 3, 14, 56.41, -15.8, 0.01461, 15, 21.9, -30.52, 0.04055, 16, 17.31, -25.15, 0.94483, 3, 14, 47.82, -10.25, 0.09997, 15, 17.11, -21.49, 0.21368, 16, 8.14, -20.64, 0.68635, 3, 14, 37.34, -11.35, 0.48265, 15, 7.44, -17.31, 0.32927, 16, -2.15, -22.94, 0.18808, 3, 14, 27.77, -16.44, 0.95216, 15, -3.4, -17.07, 0.03318, 16, -11.06, -29.11, 0.01465, 2, 14, 20.04, -20.02, 0.99992, 16, -18.32, -33.55, 8e-05, 1, 14, 8.73, -21.38, 1, 1, 14, -3.7, -16.81, 1, 1, 14, -12.56, -9.5, 1, 1, 14, -16.08, -3.07, 1, 1, 14, -9.5, -2.26, 1, 1, 14, 0.53, -4.36, 1, 1, 14, 13.25, -3, 1, 1, 15, 2.69, 0.8, 1, 1, 15, 16.51, 0.46, 1, 3, 14, 47, 9.35, 0.00123, 15, 25.99, -3.99, 0.02489, 16, 5.05, -1.26, 0.97388, 3, 14, 74.26, 17.43, 0.00023, 15, 53.72, -10.28, 0, 16, 31.19, 9.92, 0.99977, 2, 14, 62.98, 21.73, 0.00014, 16, 19.49, 12.89, 0.99986, 3, 14, 50.5, 20.61, 3e-05, 15, 34.55, 4.11, 0.03867, 16, 7.23, 10.32, 0.9613, 2, 15, 19.66, 9.62, 0.84482, 16, -8.06, 6.03, 0.15518, 2, 14, 22.45, 11.23, 0.07851, 15, 5.5, 9.67, 0.92149, 2, 14, 11.62, 3.79, 0.90937, 15, -7.59, 8.48, 0.09063, 2, 14, 1.15, 2.08, 0.99859, 15, -17.55, 12.11, 0.00141, 1, 14, -0.49, -11.45, 1, 1, 14, 12.28, -11.7, 1, 3, 14, 26.08, -5.66, 0.9096, 15, 0.4, -6.84, 0.07956, 16, -13.99, -18.59, 0.01084, 3, 14, 37.18, -1.8, 0.20175, 15, 11.97, -8.91, 0.62478, 16, -3.41, -13.48, 0.17347, 3, 14, 48.52, 0.54, 0.03832, 15, 23.01, -12.42, 0.19363, 16, 7.58, -9.84, 0.76805, 3, 14, 58.07, -6.31, 0.01186, 15, 27.98, -23.06, 0.03533, 16, 17.86, -15.53, 0.95281], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 20, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 56, 56, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 36], "width": 86, "height": 70}}, "xuanwu020": {"xuanwu020": {"type": "mesh", "uvs": [0.01645, 0.13439, 0.1436, 0.02671, 0.42968, 0, 0.68045, 0.04466, 0.83939, 0.20168, 0.92768, 0.36993, 1, 0.58304, 1, 0.79839, 0.92415, 0.96663, 0.6522, 1, 0.22837, 1, 0, 0.86568, 0.01998, 0.70641, 0.12241, 0.57182, 0.17539, 0.41704, 0.11888, 0.23085, 0.29632, 0.22421, 0.33236, 0.41388, 0.32979, 0.60029, 0.28087, 0.80959, 0.54604, 0.18169, 0.65675, 0.39263, 0.66962, 0.61337, 0.67734, 0.84065], "triangles": [20, 2, 3, 20, 3, 4, 16, 1, 2, 16, 2, 20, 15, 0, 1, 16, 15, 1, 17, 16, 20, 14, 15, 16, 21, 20, 4, 21, 4, 5, 17, 20, 21, 14, 16, 17, 18, 14, 17, 13, 14, 18, 6, 22, 21, 6, 21, 5, 17, 21, 22, 18, 17, 22, 18, 12, 13, 19, 12, 18, 22, 19, 18, 22, 6, 7, 23, 22, 7, 11, 12, 19, 8, 23, 7, 10, 11, 19, 19, 22, 23, 9, 23, 8, 9, 19, 23, 10, 19, 9], "vertices": [2, 6, 81.83, 48.4, 0.01913, 7, 40.14, 31.65, 0.98087, 1, 7, 48.41, 13.53, 1, 1, 7, 39, -11.97, 1, 2, 6, 99.79, -12.84, 0.07817, 7, 21.91, -29.5, 0.92183, 3, 5, 150.73, -78.84, 0.00351, 6, 77.75, -29.49, 0.64199, 7, -5.65, -31.49, 0.3545, 3, 5, 127.54, -66.56, 0.07194, 6, 53.55, -39.65, 0.91678, 7, -31.48, -26.85, 0.01128, 2, 5, 100.85, -48.43, 0.42161, 6, 22.62, -48.82, 0.57839, 2, 5, 78.85, -25.37, 0.89247, 6, -9.16, -51.23, 0.10753, 2, 5, 66.82, -2.43, 0.99971, 6, -34.53, -46.01, 0.00029, 1, 5, 81.9, 18.79, 1, 2, 5, 110.73, 46.29, 0.9588, 6, -44.41, 18.83, 0.0412, 2, 5, 139.98, 46.73, 0.72138, 6, -26.21, 41.74, 0.27862, 2, 5, 154.89, 28.37, 0.39806, 6, -2.57, 41.65, 0.60194, 3, 5, 161.68, 7.32, 0.06558, 6, 18.03, 33.56, 0.92462, 7, -21.46, 53.91, 0.00981, 2, 6, 41.25, 30.33, 0.80535, 7, -3.74, 38.57, 0.19465, 2, 6, 68.32, 37.71, 0.15321, 7, 23, 30.04, 0.84679, 2, 6, 70.56, 21.16, 0.15286, 7, 15.87, 14.92, 0.84714, 2, 6, 42.83, 15.65, 0.874, 7, -10.39, 25.39, 0.126, 3, 5, 144.67, -3.09, 0.01001, 6, 15.3, 13.8, 0.98804, 7, -34.5, 38.82, 0.00196, 2, 5, 126.61, 22.49, 0.78973, 6, -15.93, 16.04, 0.21027, 2, 6, 78.61, -1.77, 0.06817, 7, 10.16, -8.7, 0.93183, 2, 5, 143.65, -46.55, 0.02365, 6, 48.27, -14.51, 0.97635, 2, 5, 120.22, -23.74, 0.32769, 6, 15.79, -18.19, 0.67231, 1, 5, 96.47, 0.09, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 2, 32, 32, 34, 34, 36, 36, 38, 4, 40, 40, 42, 42, 44, 44, 46], "width": 94, "height": 148}}, "xuanwu025": {"xuanwu025": {"type": "mesh", "uvs": [0.03258, 0, 0.22599, 0.0449, 0.37105, 0.21199, 0.49718, 0.49248, 0.67378, 0.57006, 0.82934, 0.46861, 0.96599, 0.62377, 1, 1, 0.84196, 0.82667, 0.68849, 0.91619, 0.5077, 0.95199, 0.30378, 0.90425, 0.10196, 0.66554, 0, 0.29554, 0, 0, 0.0494, 0.21796, 0.17133, 0.4328, 0.33531, 0.6178, 0.5119, 0.74909, 0.67588, 0.73119, 0.84196, 0.65958], "triangles": [20, 5, 6, 4, 5, 20, 19, 4, 20, 18, 3, 4, 18, 4, 19, 8, 20, 6, 19, 20, 8, 9, 19, 8, 10, 19, 9, 8, 6, 7, 17, 16, 2, 17, 2, 3, 17, 3, 18, 10, 17, 18, 11, 17, 10, 10, 18, 19, 15, 0, 1, 14, 0, 15, 13, 14, 15, 16, 15, 1, 16, 1, 2, 12, 15, 16, 13, 15, 12, 11, 16, 17, 12, 16, 11], "vertices": [1, 97, -9.64, 1.36, 1, 2, 97, 4.33, 11.19, 0.78716, 98, -7.54, 15.63, 0.21284, 3, 97, 17.45, 15.38, 0.07815, 98, 6.1, 13.73, 0.91775, 99, -11.63, 18.53, 0.0041, 2, 98, 18.99, 8.02, 0.55791, 99, -1.54, 8.68, 0.44209, 1, 99, 13.64, 4.6, 1, 1, 99, 27.59, 6.23, 1, 1, 99, 39.02, 0.14, 1, 1, 99, 40.73, -11.78, 1, 1, 99, 27.49, -4.92, 1, 2, 98, 38.54, -0.59, 0.00731, 99, 13.76, -6.21, 0.99269, 1, 98, 23.38, -5.56, 1, 2, 97, 26.64, -4.9, 0.50084, 98, 5.62, -8.52, 0.49916, 1, 97, 8.26, -10.58, 1, 1, 97, -5.97, -7.52, 1, 1, 97, -11.84, -0.48, 1, 1, 97, -4.17, -2.88, 1, 1, 97, 8.33, -1.13, 1, 2, 98, 6.14, 0.76, 0.99993, 99, -16.13, 6.36, 7e-05, 2, 98, 22.2, 0.63, 0.69984, 99, -1.12, 0.63, 0.30016, 2, 98, 36.05, 4.7, 0.00027, 99, 13.28, -0.39, 0.99973, 1, 99, 28.05, 0.23, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 88, "height": 31}}, "xuanwu016": {"xuanwu016": {"type": "mesh", "uvs": [0.23162, 1, 0.32034, 0.87011, 0.46908, 0.72289, 0.61781, 0.5449, 0.71697, 0.35812, 0.74045, 0.19991, 0.88658, 0.07686, 1, 0.01753, 1, 0, 0.75089, 0.01973, 0.54736, 0.09664, 0.30208, 0.22628, 0.14812, 0.41965, 0, 0.624, 0, 0.78441, 0.02548, 0.90747, 0.0855, 0.93383, 0.14291, 0.83276, 0.22119, 0.64159, 0.34905, 0.44822, 0.49517, 0.27243, 0.64391, 0.13619, 0.83178, 0.0483], "triangles": [8, 22, 9, 8, 6, 22, 7, 6, 8, 21, 10, 9, 21, 9, 22, 5, 21, 22, 5, 22, 6, 20, 10, 21, 5, 20, 21, 20, 11, 10, 4, 20, 5, 19, 11, 20, 12, 11, 19, 3, 20, 4, 19, 20, 3, 18, 12, 19, 2, 19, 3, 13, 12, 18, 18, 19, 2, 14, 13, 18, 17, 14, 18, 1, 17, 18, 2, 1, 18, 15, 14, 17, 16, 15, 17, 0, 17, 1, 16, 17, 0], "vertices": [1, 17, -8.44, -5.59, 1, 1, 17, 2.84, -7.14, 1, 2, 17, 16.78, -11.76, 0.89591, 18, -6.94, -12.55, 0.10409, 3, 17, 32.9, -15.5, 0.08591, 18, 9.47, -14.59, 0.89595, 19, -10.79, -20.36, 0.01815, 2, 18, 24.98, -13.52, 0.53282, 19, 3.1, -13.36, 0.46718, 2, 18, 36.3, -9.2, 0.00974, 19, 11.86, -4.99, 0.99026, 1, 19, 25.02, -3.65, 1, 1, 19, 33.49, -4.74, 1, 1, 19, 34.33, -3.71, 1, 1, 19, 21.03, 5.2, 1, 2, 18, 37.42, 5.4, 0.01968, 19, 7.24, 8.9, 0.98032, 3, 17, 47.75, 12.33, 0.00054, 18, 21.35, 14.63, 0.96672, 19, -11.16, 11.18, 0.03274, 2, 17, 30.43, 15.94, 0.34701, 18, 3.75, 16.42, 0.65299, 2, 17, 12.47, 18.89, 0.97606, 18, -14.42, 17.48, 0.02394, 1, 17, 1.17, 14.31, 1, 1, 17, -6.88, 9.28, 1, 1, 17, -7.3, 4.97, 1, 1, 17, 1.2, 4.45, 1, 2, 17, 16.55, 5.27, 0.99109, 18, -8.94, 4.36, 0.00891, 2, 17, 33.24, 3.21, 0.02485, 18, 7.87, 4.05, 0.97515, 1, 18, 24.06, 2.07, 1, 1, 19, 10.13, 2.67, 1, 1, 19, 23.67, 0.25, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 64, "height": 76}}, "xuanwu027": {"xuanwu027": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-129.69, -16.52, -24.78, 83.57, 100.17, -47.38, -4.74, -147.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 145, "height": 181}}, "xuanwu028": {"xuanwu028": {"type": "mesh", "uvs": [0.50103, 0.13153, 0.60764, 0, 0.64265, 0, 0.81927, 0.1275, 0.92906, 0.29437, 0.86064, 0.36031, 0.93823, 0.44828, 0.99489, 0.50346, 0.96227, 0.54992, 0.90733, 0.65883, 0.83693, 0.74306, 0.81805, 0.79098, 0.81461, 0.89117, 0.75967, 0.93329, 0.61029, 0.94635, 0.47465, 0.9754, 0.2875, 0.98556, 0.14842, 0.98556, 0.05227, 0.99718, 0, 0.92457, 0.07288, 0.82002, 0.20165, 0.77646, 0.32871, 0.76339, 0.40425, 0.76193, 0.51071, 0.73289, 0.52397, 0.70047, 0.54161, 0.65738, 0.53989, 0.61382, 0.44374, 0.57751, 0.34416, 0.47732, 0.29952, 0.35098, 0.32184, 0.24062, 0.40254, 0.1564, 0.52101, 0.26531, 0.58454, 0.41052, 0.74078, 0.55719, 0.70816, 0.64286, 0.67554, 0.69659, 0.66008, 0.76193, 0.54848, 0.8418, 0.38365, 0.8723, 0.22397, 0.89263, 0.1055, 0.91586], "triangles": [24, 25, 38, 39, 24, 38, 23, 24, 39, 40, 22, 23, 40, 23, 39, 41, 21, 22, 41, 22, 40, 20, 21, 41, 42, 20, 41, 19, 20, 42, 14, 39, 38, 15, 40, 39, 15, 39, 14, 17, 42, 41, 16, 41, 40, 16, 40, 15, 17, 41, 16, 18, 19, 42, 18, 42, 17, 37, 26, 36, 25, 26, 37, 10, 36, 9, 37, 36, 10, 38, 25, 37, 38, 37, 10, 11, 38, 10, 12, 38, 11, 13, 38, 12, 13, 14, 38, 2, 33, 0, 32, 0, 33, 31, 32, 33, 2, 0, 1, 3, 33, 2, 5, 3, 4, 33, 3, 5, 34, 33, 5, 33, 30, 31, 34, 30, 33, 34, 29, 30, 8, 6, 7, 35, 34, 5, 35, 5, 6, 35, 6, 8, 28, 29, 34, 27, 28, 34, 35, 27, 34, 36, 27, 35, 26, 27, 36, 9, 35, 8, 36, 35, 9], "vertices": [1, 44, -32.91, 12.72, 1, 1, 44, -46.11, 39.64, 1, 1, 44, -43.38, 44.49, 1, 1, 44, -8.7, 57.16, 1, 1, 44, 27.2, 56.94, 1, 2, 44, 32.65, 41.37, 0.99804, 45, -54.23, -1.63, 0.00196, 2, 44, 53.12, 43.99, 0.93292, 45, -43.62, 16.07, 0.06708, 2, 44, 66.58, 46.73, 0.87036, 45, -37.45, 28.35, 0.12964, 2, 44, 71.64, 37.92, 0.80164, 45, -27.4, 26.87, 0.19836, 2, 44, 85.17, 20.25, 0.32922, 45, -5.14, 26.57, 0.67078, 2, 44, 93.46, 2.72, 0.00911, 45, 13.76, 22.24, 0.99089, 1, 45, 23.23, 22.88, 1, 2, 45, 40.86, 29.54, 0.99428, 46, -22.98, 19.56, 0.00572, 2, 45, 51.51, 24.47, 0.90634, 46, -12.86, 25.6, 0.09366, 2, 45, 62.81, 3.43, 0.15736, 46, 10.91, 23.32, 0.84264, 1, 46, 33.13, 24.41, 1, 1, 46, 62.68, 20.41, 1, 1, 46, 84.36, 16.05, 1, 1, 46, 99.78, 15.17, 1, 1, 46, 105.23, 0.15, 1, 1, 46, 89.99, -16.83, 1, 1, 46, 68.3, -20.82, 1, 1, 46, 48.01, -19.24, 1, 2, 44, 62.73, -58.94, 0.00029, 46, 36.19, -17.14, 0.99971, 3, 44, 66.3, -41.51, 0.04785, 45, 31.71, -26.47, 0.07691, 46, 18.51, -19.15, 0.87524, 3, 44, 62.03, -36.68, 0.13718, 45, 25.27, -26.83, 0.20563, 46, 15.24, -24.71, 0.65719, 3, 44, 56.35, -30.26, 0.35477, 45, 16.71, -27.32, 0.29179, 46, 10.9, -32.09, 0.35345, 3, 44, 49.08, -26.47, 0.6912, 45, 9.24, -30.68, 0.17855, 46, 9.55, -40.18, 0.13025, 3, 44, 35.62, -36.43, 0.949, 45, 8.73, -47.42, 0.0295, 46, 23.19, -49.89, 0.02151, 3, 44, 11.44, -40.95, 0.99936, 45, -2.67, -69.22, 0.00016, 46, 34.99, -71.48, 0.00048, 1, 44, -12.74, -35.46, 1, 1, 44, -29.06, -22.17, 1, 1, 44, -36.54, -3.21, 1, 1, 44, -9.45, 3.13, 1, 2, 44, 19.29, -1.5, 0.99999, 46, -4.96, -76.24, 1e-05, 2, 44, 55.51, 6.58, 0.99475, 45, -12.75, -5.19, 0.00525, 3, 44, 66.99, -5.85, 0.21614, 45, 4.12, -3.86, 0.77482, 46, -15.6, -29.54, 0.00904, 3, 44, 73.24, -15.34, 0.04487, 45, 15.43, -4.82, 0.8863, 46, -8.52, -20.67, 0.06883, 3, 44, 82.73, -23.52, 0.00302, 45, 27.73, -2.42, 0.8273, 46, -3.69, -9.11, 0.16968, 1, 46, 16.67, 2.11, 1, 1, 46, 43.49, 2.56, 1, 1, 46, 69.14, 1.29, 1, 1, 46, 88.46, 1.86, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 52, 54, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84], "width": 159, "height": 188}}, "xuanwu029": {"xuanwu029": {"type": "mesh", "uvs": [0.58682, 0.10289, 0.55932, 0.05664, 0.59932, 0.00289, 0.73432, 0, 0.91182, 0.00914, 1, 0.08664, 1, 0.18914, 0.95932, 0.30789, 0.87182, 0.42914, 0.77682, 0.54539, 0.66432, 0.65914, 0.51682, 0.77039, 0.36932, 0.86789, 0.18932, 0.95914, 0.07182, 1, 0, 1, 0, 0.97164, 0.08932, 0.90164, 0.20682, 0.79914, 0.29682, 0.70664, 0.36932, 0.58914, 0.49182, 0.48039, 0.57932, 0.37914, 0.65182, 0.26539, 0.68182, 0.16289, 0.65682, 0.12039, 0.70682, 0.05164, 0.82432, 0.10789, 0.83432, 0.18789, 0.79682, 0.28789, 0.69932, 0.40914, 0.61182, 0.51289, 0.49182, 0.64039, 0.38682, 0.74289, 0.26182, 0.84289, 0.14682, 0.92664], "triangles": [35, 13, 14, 35, 14, 16, 16, 14, 15, 16, 17, 35, 12, 35, 34, 12, 13, 35, 35, 17, 34, 17, 18, 34, 12, 33, 11, 12, 34, 33, 34, 18, 33, 18, 19, 33, 33, 32, 11, 33, 19, 32, 25, 26, 27, 25, 0, 26, 4, 26, 3, 0, 1, 26, 1, 2, 26, 26, 2, 3, 6, 27, 5, 24, 27, 28, 6, 28, 27, 24, 25, 27, 27, 26, 4, 27, 4, 5, 8, 29, 7, 29, 22, 23, 29, 28, 7, 7, 28, 6, 29, 23, 28, 23, 24, 28, 11, 32, 10, 19, 20, 32, 32, 31, 10, 10, 31, 9, 32, 20, 31, 31, 20, 21, 31, 30, 9, 9, 30, 8, 31, 21, 30, 21, 22, 30, 30, 29, 8, 30, 22, 29], "vertices": [3, 64, 63.72, 39.86, 0.00113, 65, 30.67, 28.15, 0.11663, 66, 26, 12.6, 0.88223, 2, 65, 42.82, 28.11, 0.00284, 66, 28.66, 0.74, 0.99716, 1, 66, 22.7, -12.43, 1, 2, 65, 50.13, 2.88, 0.02485, 66, 5.68, -11.98, 0.97515, 2, 65, 41.48, -17.87, 0.96186, 66, -16.47, -8.15, 0.03814, 1, 65, 19.57, -22.88, 1, 2, 64, 55.15, -15.9, 0.49947, 65, -5.16, -15.43, 0.50053, 1, 64, 24.87, -18.13, 1, 2, 63, 57.95, -16.1, 0.80262, 64, -7.44, -14.78, 0.19738, 1, 63, 26.43, -18.88, 1, 2, 63, -5.54, -19.42, 0.8716, 67, -34.87, 18.71, 0.1284, 2, 63, -38.98, -15.75, 0.0666, 67, -1.27, 17.13, 0.9334, 2, 67, 29.35, 13.77, 0.573, 68, -2.53, 13.54, 0.427, 1, 68, 29.75, 12.54, 1, 1, 68, 47.37, 8.68, 1, 1, 68, 53.52, 2.04, 1, 1, 68, 48.28, -2.82, 1, 1, 68, 27.69, -6.56, 1, 2, 67, 24.98, -12.69, 0.70565, 68, -1.32, -13.25, 0.29435, 2, 63, -37.4, 16.25, 0.0808, 67, -0.85, -14.91, 0.9192, 2, 63, -6.9, 21.7, 0.84164, 67, -30.96, -22.25, 0.15836, 1, 63, 24.53, 20.54, 1, 2, 63, 52.26, 22.44, 0.71958, 64, -4.08, 24.03, 0.28042, 4, 63, 81.93, 27.45, 0.01026, 64, 25.94, 22.06, 0.94826, 65, -10.91, 32.12, 0.0413, 66, 20.65, 54.02, 0.00018, 3, 64, 51.92, 24.6, 0.26318, 65, 12.74, 21.05, 0.60558, 66, 15.1, 28.51, 0.13124, 3, 64, 61.56, 30.24, 0.03215, 65, 23.9, 20.98, 0.41896, 66, 17.51, 17.61, 0.54889, 2, 65, 38.67, 9.95, 0.01625, 66, 10.03, 0.76, 0.98375, 2, 65, 20.83, -0.14, 0.99993, 66, -3.76, 15.92, 7e-05, 3, 64, 50.43, 4.44, 0.46811, 65, 1.16, 4.47, 0.52937, 66, -3.63, 36.12, 0.00252, 2, 64, 24.83, 2.96, 0.9989, 65, -21.6, 16.26, 0.0011, 2, 63, 52.47, 5.53, 0.93817, 64, -7.78, 7.53, 0.06183, 1, 63, 24.18, 3.35, 1, 2, 63, -11.3, 2.06, 0.75578, 67, -27.78, -2.37, 0.24422, 1, 67, 1.18, -0.49, 1, 2, 67, 30.89, -1.09, 0.11855, 68, 2.06, -0.68, 0.88145, 1, 68, 27.39, 3.04, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 2, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 126, "height": 252}}, "xuanwu04": {"xuanwu04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-1.97, 47.13, -338.84, 37.82, -342.29, 162.78, -5.42, 172.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 337, "height": 125}}, "xuanwu034": {"xuanwu034": {"type": "mesh", "uvs": [0.12554, 0.55883, 0.29031, 0.48196, 0.44814, 0.39307, 0.72392, 0.17446, 0.83744, 0.08663, 0.9494, 0, 1, 0, 1, 0.09519, 1, 0.35944, 0.8939, 0.51319, 0.76034, 0.6357, 0.57996, 0.75822, 0.41866, 0.90476, 0.29551, 1, 0.12554, 1, 0, 1, 0, 0.81107, 0.05616, 0.59967, 0.13248, 0.78945, 0.30592, 0.71018, 0.50018, 0.59487, 0.66148, 0.4267, 0.84707, 0.29458], "triangles": [14, 18, 13, 13, 18, 19, 18, 14, 16, 14, 15, 16, 16, 17, 18, 17, 0, 18, 18, 0, 19, 13, 19, 12, 12, 20, 11, 12, 19, 20, 0, 1, 19, 19, 1, 20, 1, 2, 20, 10, 11, 21, 11, 20, 21, 10, 21, 9, 20, 2, 21, 21, 22, 9, 21, 3, 22, 21, 2, 3, 9, 22, 8, 22, 7, 8, 3, 4, 22, 7, 4, 5, 7, 22, 4, 5, 6, 7], "vertices": [2, 80, -65.9, 14.79, 0.25287, 79, -0.29, 44.53, 0.74713, 3, 81, -87.08, -0.63, 0.01594, 80, -26.19, 28.17, 0.80383, 79, 39.42, 57.9, 0.18024, 3, 81, -49.04, 14.84, 0.42809, 80, 11.85, 43.63, 0.57173, 79, 77.46, 73.37, 0.00018, 2, 82, -43.71, 14.71, 0.56831, 81, 17.42, 52.88, 0.43169, 2, 82, -16.35, 29.99, 0.93616, 81, 44.78, 68.16, 0.06384, 1, 82, 10.63, 45.06, 1, 1, 82, 22.83, 45.06, 1, 1, 82, 22.83, 28.5, 1, 2, 82, 22.83, -17.48, 0.97285, 81, 83.96, 20.69, 0.02715, 2, 82, -2.74, -44.23, 0.59497, 81, 58.39, -6.06, 0.40503, 3, 82, -34.93, -65.55, 0.12452, 81, 26.2, -27.38, 0.84947, 80, 87.09, 1.41, 0.02601, 3, 81, -17.27, -48.7, 0.4365, 80, 43.62, -19.9, 0.56298, 79, 109.22, 9.83, 0.00052, 3, 81, -56.15, -74.19, 0.01366, 80, 4.74, -45.4, 0.77418, 79, 70.35, -15.67, 0.21215, 2, 80, -24.94, -61.97, 0.41381, 79, 40.67, -32.24, 0.58619, 2, 80, -65.9, -61.97, 0.03394, 79, -0.29, -32.24, 0.96606, 1, 79, -30.55, -32.24, 1, 1, 79, -30.55, 0.64, 1, 2, 80, -82.62, 7.68, 0.10658, 79, -17.01, 37.42, 0.89342, 2, 80, -64.23, -25.34, 0.02878, 79, 1.38, 4.4, 0.97122, 2, 80, -22.43, -11.54, 0.7018, 79, 43.18, 18.19, 0.2982, 2, 81, -36.5, -20.27, 0.35393, 80, 24.39, 8.52, 0.64607, 3, 82, -58.75, -29.18, 0.06809, 81, 2.37, 8.99, 0.9318, 80, 63.26, 37.78, 0.00011, 2, 82, -14.03, -6.19, 0.81916, 81, 47.1, 31.98, 0.18084], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 6, 8, 8, 10, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 32, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 14], "width": 241, "height": 174}}, "xuanwu026": {"xuanwu026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43.5, 31.99, 55.52, 17.28, 43.13, 7.15, 31.11, 21.87], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 16}}, "xuanwu017": {"xuanwu017": {"type": "mesh", "uvs": [0, 0.75296, 0.11508, 0.79179, 0.19375, 0.87843, 0.22894, 0.99194, 0.29519, 0.9322, 0.39663, 0.89336, 0.52084, 0.78283, 0.59122, 0.64243, 0.64091, 0.59762, 0.72164, 0.63048, 0.83343, 0.57372, 0.89761, 0.4363, 0.76098, 0.4124, 0.62849, 0.32577, 0.47943, 0.30784, 0.3987, 0.32876, 0.4401, 0.17939, 0.43596, 0.05691, 0.40284, 0, 0.34073, 0.11367, 0.24758, 0.23316, 0.12129, 0.30187, 0.02607, 0.44227, 0, 0.59762, 0.33496, 0.42846, 0.24162, 0.56492, 0.38159, 0.15872, 0.2783, 0.34505, 0.15886, 0.44287, 0.08623, 0.58262, 0.29282, 0.78059, 0.37675, 0.61523, 0.47521, 0.51042, 0.61724, 0.48014, 0.74636, 0.50343], "triangles": [33, 14, 13, 33, 13, 12, 34, 33, 12, 34, 12, 11, 10, 34, 11, 8, 33, 34, 9, 8, 34, 9, 34, 10, 7, 33, 8, 32, 15, 14, 32, 14, 33, 24, 15, 32, 31, 24, 32, 7, 32, 33, 6, 32, 7, 31, 32, 6, 25, 24, 31, 30, 25, 31, 5, 30, 31, 1, 25, 30, 2, 1, 30, 6, 5, 31, 4, 30, 5, 2, 30, 4, 3, 2, 4, 26, 19, 18, 17, 26, 18, 26, 17, 16, 15, 26, 16, 26, 27, 20, 26, 20, 19, 27, 26, 15, 24, 27, 15, 27, 28, 21, 27, 21, 20, 25, 28, 27, 25, 27, 24, 22, 21, 28, 29, 22, 28, 29, 28, 25, 23, 22, 29, 0, 23, 29, 1, 29, 25, 0, 29, 1], "vertices": [2, 20, -12.11, 6.13, 0.99995, 21, -35, 13.91, 5e-05, 2, 20, -5.83, -5.79, 0.70955, 22, -13.73, -1.51, 0.29045, 3, 20, -5.13, -17.05, 0.07037, 22, -9.12, -11.81, 0.92825, 23, -28.86, -20.47, 0.00138, 2, 22, -9.88, -21.6, 0.99557, 23, -26.92, -30.1, 0.00443, 2, 22, -0.99, -21.07, 0.96223, 23, -18.51, -27.16, 0.03777, 2, 22, 10.63, -23.9, 0.79462, 23, -6.56, -26.71, 0.20538, 3, 22, 27.24, -23, 0.34957, 23, 9.18, -21.3, 0.64989, 24, -6.9, -24.31, 0.00054, 3, 22, 39.59, -17.09, 0.04259, 23, 19.44, -12.24, 0.74731, 24, 0.33, -12.69, 0.2101, 3, 22, 46.26, -16.69, 0.00222, 23, 25.75, -10.03, 0.23987, 24, 5.73, -8.76, 0.75791, 2, 23, 34.15, -14.59, 0.00084, 24, 15.09, -10.7, 0.99916, 1, 24, 27.49, -5.33, 1, 1, 24, 34.03, 6.01, 1, 1, 24, 18.36, 6.8, 1, 3, 21, 28.68, -33.18, 0.00256, 23, 29.09, 11.23, 0.31946, 24, 2.81, 12.57, 0.67798, 3, 21, 21.87, -17.55, 0.1507, 23, 12.82, 16.34, 0.84369, 24, -14.24, 12.79, 0.00561, 3, 21, 16.05, -10.22, 0.63101, 22, 32.16, 15.17, 0.01877, 23, 3.48, 16.76, 0.35022, 2, 21, 28.68, -8.79, 0.99395, 23, 10.68, 27.23, 0.00605, 1, 21, 36.98, -3.78, 1, 1, 21, 39.14, 1.67, 1, 1, 21, 27.88, 3.65, 1, 2, 20, 37.41, 11.7, 0.0001, 21, 14.53, 8.52, 0.9999, 2, 20, 23.88, 19.02, 0.30948, 21, 2.93, 18.63, 0.69052, 2, 20, 8.39, 19.96, 0.79714, 21, -11.98, 22.93, 0.20286, 2, 20, -2.83, 14.17, 0.96626, 21, -24.2, 19.72, 0.03374, 4, 20, 32.27, -5.94, 0.07886, 21, 5.67, -7.56, 0.58086, 22, 22.01, 11.72, 0.20675, 23, -5.34, 10.67, 0.13353, 2, 20, 17.16, -4.95, 0.64906, 22, 7.51, 7.34, 0.35094, 2, 21, 26.95, -2.14, 0.99863, 23, 4.53, 30.29, 0.00137, 1, 21, 8.41, 1.25, 1, 2, 20, 18.27, 8.49, 0.62748, 21, -4.85, 9.57, 0.37252, 2, 20, 4.5, 7.52, 0.95817, 21, -18.49, 11.63, 0.04183, 2, 22, 4.49, -10.42, 0.95996, 23, -16.14, -15.41, 0.04004, 2, 22, 19.14, -3.52, 0.88436, 23, -3.94, -4.77, 0.11564, 2, 21, 7.55, -24.71, 0.0049, 23, 8.83, 0.84, 0.9951, 2, 21, 17.33, -37.83, 1e-05, 24, 2.39, 0.31, 0.99999, 1, 24, 17.2, -0.49, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 30, 48, 48, 50, 36, 52, 52, 54, 54, 56, 56, 58, 4, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 22], "width": 114, "height": 79}}, "xuanwu031": {"xuanwu031": {"type": "mesh", "uvs": [0.08004, 1, 0.2602, 1, 0.38826, 0.97648, 0.55974, 0.96418, 0.72687, 0.95393, 0.83975, 0.84733, 0.93525, 0.69768, 1, 0.54598, 0.94827, 0.39018, 0.9092, 0.27333, 0.97649, 0.19748, 0.82021, 0.00478, 0.64656, 0, 0.46857, 0.01708, 0.38826, 0.13188, 0.34919, 0.26718, 0.37958, 0.38198, 0.45772, 0.49268, 0.56842, 0.54803, 0.55106, 0.59928, 0.55757, 0.67308, 0.53369, 0.72228, 0.4447, 0.72843, 0.33183, 0.75508, 0.22547, 0.75508, 0.11694, 0.77353, 0.02143, 0.83708, 0, 0.94163, 0.11911, 0.90063, 0.2602, 0.85348, 0.43385, 0.83708, 0.61835, 0.81453, 0.73339, 0.72843, 0.75943, 0.62593, 0.75726, 0.53983, 0.65307, 0.35943, 0.60749, 0.17083], "triangles": [21, 20, 31, 30, 23, 22, 30, 22, 21, 30, 21, 31, 29, 24, 23, 29, 23, 30, 29, 28, 25, 29, 25, 24, 26, 25, 28, 27, 26, 28, 3, 30, 31, 3, 31, 4, 2, 29, 30, 2, 30, 3, 0, 27, 28, 1, 29, 2, 28, 29, 1, 0, 28, 1, 33, 34, 7, 18, 34, 33, 19, 18, 33, 20, 19, 33, 6, 33, 7, 32, 20, 33, 32, 33, 6, 31, 20, 32, 5, 32, 6, 5, 4, 31, 5, 31, 32, 36, 13, 12, 14, 13, 36, 9, 11, 10, 11, 36, 12, 9, 36, 11, 9, 35, 36, 36, 16, 15, 36, 15, 14, 16, 36, 35, 35, 9, 8, 17, 16, 35, 34, 35, 8, 18, 17, 35, 34, 8, 7, 34, 18, 35], "vertices": [1, 40, 96.35, 16.81, 1, 1, 40, 66.28, 22.64, 1, 1, 40, 44.1, 22.63, 1, 2, 39, 85.42, 8.79, 0.05569, 40, 15.06, 26.01, 0.94431, 2, 39, 67.18, 30.66, 0.63445, 40, -13.18, 29.61, 0.36555, 2, 39, 40.38, 34.86, 0.97989, 40, -35.67, 14.42, 0.02011, 2, 38, 100.16, 3.13, 0.02614, 39, 9.04, 32.11, 0.97386, 2, 38, 83.16, 27.17, 0.5684, 39, -19.5, 24.91, 0.4316, 2, 38, 54.82, 34.96, 0.98901, 39, -36.98, 1.28, 0.01099, 1, 38, 33.54, 40.77, 1, 1, 38, 28.26, 57.78, 1, 1, 38, -15.29, 54.22, 1, 1, 38, -31.99, 29.87, 1, 1, 38, -45.79, 2.77, 1, 1, 38, -35.81, -19.9, 1, 2, 38, -18.94, -38.67, 0.99949, 39, 5.13, -94.05, 0.00051, 2, 38, 1.23, -45.52, 0.98409, 39, 18.78, -77.7, 0.01591, 2, 38, 25.18, -45.14, 0.90647, 39, 27.06, -55.23, 0.09353, 3, 38, 43.74, -34.71, 0.58785, 39, 24.02, -34.15, 0.40894, 40, -0.65, -47.25, 0.00321, 3, 38, 49.9, -42.19, 0.30887, 39, 33.21, -31.1, 0.66229, 40, 4.01, -38.75, 0.02883, 3, 38, 61.67, -48.45, 0.08793, 39, 43.29, -22.38, 0.7211, 40, 5.45, -25.5, 0.19097, 3, 38, 66.92, -56.66, 0.01395, 39, 52.84, -20.44, 0.35699, 40, 11.12, -17.58, 0.62906, 3, 38, 59.66, -69.98, 3e-05, 39, 62.65, -32.02, 0.02203, 40, 26.18, -19.37, 0.97794, 1, 40, 45.93, -18.32, 1, 1, 40, 63.68, -21.76, 1, 1, 40, 82.43, -22.01, 1, 1, 40, 100.54, -13.87, 1, 1, 40, 107.7, 3.91, 1, 1, 40, 86.42, 0.52, 1, 1, 40, 61.26, -3.25, 1, 2, 39, 79.54, -21.98, 3e-05, 40, 31.72, -0.52, 0.99997, 2, 39, 57.78, 0.97, 0.35841, 40, 0.15, 1.46, 0.64159, 1, 39, 33.74, 7.64, 1, 1, 39, 16.22, 0.35, 1, 2, 38, 59.89, -6.92, 0.65541, 39, 3.91, -9.08, 0.34459, 2, 38, 23, -4.23, 0.99535, 39, -11.89, -42.53, 0.00465, 1, 38, -9.74, 7.64, 1], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72], "width": 170, "height": 180}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"bones": {"bone52": {"translate": [{"time": 0.2333, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.3333, "x": 37.24, "curve": 0.334, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.5, "x": -20.96, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.6333}]}, "bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -18.82, "y": 8.06, "curve": "stepped"}, {"time": 0.4, "x": -18.82, "y": 8.06, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 6.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -17.49, "curve": "stepped"}, {"time": 0.4, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 19.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6333}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 6.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 28.67, "curve": "stepped"}, {"time": 0.4, "angle": 28.67, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -39.41, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6333}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 6.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -17.49, "curve": "stepped"}, {"time": 0.4, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -10.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6333}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 6.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -17.49, "curve": "stepped"}, {"time": 0.4, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -10.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6333}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 23.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone8": {"rotate": [{"angle": 0.19, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.68, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 0.7667, "angle": 0.19}]}, "bone9": {"rotate": [{"angle": 1.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": 1.04}]}, "bone10": {"rotate": [{"angle": 2.11, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.68, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.7667, "angle": 2.11}]}, "bone11": {"rotate": [{"angle": 3.11, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 3.68, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.7667, "angle": 3.11}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone13": {"rotate": [{"angle": 2.11, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.68, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.7667, "angle": 2.11}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone16": {"rotate": [{"angle": 0.57, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.68, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 0.7667, "angle": 0.57}]}, "bone17": {"rotate": [{"angle": 1.57, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 3.68, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.7667, "angle": 1.57}]}, "bone18": {"rotate": [{"angle": 0.19, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.68, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 0.7667, "angle": 0.19}]}, "bone19": {"rotate": [{"angle": 1.57, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 3.68, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.7667, "angle": 1.57}]}, "bone20": {"rotate": [{"angle": 0.19, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.68, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 0.7667, "angle": 0.19}]}, "bone21": {"rotate": [{"angle": 1.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": 1.04}]}, "bone22": {"rotate": [{"angle": 2.11, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.68, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.7667, "angle": 2.11}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone24": {"rotate": [{"angle": 2.11, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.68, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.7667, "angle": 2.11}]}, "bone25": {"rotate": [{"angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 3.68}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone27": {"rotate": [{"angle": 2.11, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.68, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.7667, "angle": 2.11}]}, "bone28": {"rotate": [{"angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 3.68}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 7.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -12.52, "curve": "stepped"}, {"time": 0.4, "angle": -12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 7.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -12.52, "curve": "stepped"}, {"time": 0.4, "angle": -12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.67, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone33": {"rotate": [{"angle": 0.36, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -14.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 4.67, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.7667, "angle": 0.36}]}, "bone34": {"rotate": [{"angle": 1.89, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -14.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 4.67, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7667, "angle": 1.89}]}, "bone35": {"rotate": [{"angle": 3.62, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4667, "angle": -14.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 4.67, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.7667, "angle": 3.62}]}, "bone36": {"rotate": [{"angle": 0.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 39, "curve": "stepped"}, {"time": 0.4, "angle": 39, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.68}]}, "bone37": {"rotate": [{"angle": -2.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -61.67, "curve": "stepped"}, {"time": 0.4, "angle": -61.67, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -2.49}]}, "bone38": {"rotate": [{"angle": 1.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 15.46, "curve": "stepped"}, {"time": 0.4, "angle": 15.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 1.56}]}, "bone39": {"rotate": [{"angle": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 22.66, "curve": "stepped"}, {"time": 0.4, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.01}]}, "bone40": {"rotate": [{"angle": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -53.27, "curve": "stepped"}, {"time": 0.4, "angle": -53.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.14}]}, "bone41": {"rotate": [{"angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.95, "curve": "stepped"}, {"time": 0.4, "angle": 7.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.55}]}, "bone42": {"rotate": [{"angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 13.43, "curve": "stepped"}, {"time": 0.4, "angle": 13.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.46}]}, "bone43": {"rotate": [{"angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -51.2, "curve": "stepped"}, {"time": 0.4, "angle": -51.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -1.74}]}, "bone44": {"rotate": [{"angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 55.61, "curve": "stepped"}, {"time": 0.4, "angle": 55.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.33}]}, "bone45": {"rotate": [{"angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.57, "curve": "stepped"}, {"time": 0.4, "angle": 11.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.55}]}, "bone46": {"rotate": [{"angle": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -29.59, "curve": "stepped"}, {"time": 0.4, "angle": -29.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.27}]}, "bone47": {"rotate": [{"angle": -0.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 29.59, "curve": "stepped"}, {"time": 0.4, "angle": 29.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.81}]}, "bone48": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -22.3, "y": -99.59, "curve": "stepped"}, {"time": 0.4, "x": -22.3, "y": -99.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone49": {"translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -22.3, "y": -99.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone54": {"rotate": [{"curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 0.1667, "angle": 4.68, "curve": 0.312, "c2": 0.26, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "angle": -4.44, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.4767, "angle": 8.57, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 0.7667}]}, "bone55": {"rotate": [{"curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 0.1667, "angle": 4.68, "curve": 0.312, "c2": 0.26, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "angle": -4.44, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.4767, "angle": 8.57, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 0.7667}]}, "bone56": {"rotate": [{"curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 0.1667, "angle": 4.68, "curve": 0.312, "c2": 0.26, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "angle": -4.44, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.4767, "angle": 32.74, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 0.7667}]}, "bone57": {"rotate": [{"curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 0.2, "angle": 4.68, "curve": 0.312, "c2": 0.26, "c3": 0.653, "c4": 0.62}, {"time": 0.3667, "angle": -4.44, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.5, "angle": 8.57, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 0.7667}]}, "bone58": {"rotate": [{"curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 0.2, "angle": 4.68, "curve": 0.312, "c2": 0.26, "c3": 0.653, "c4": 0.62}, {"time": 0.3667, "angle": -4.44, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.5, "angle": -29.84, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 0.7667}]}, "bone59": {"rotate": [{"curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 0.2333, "angle": 4.68, "curve": 0.312, "c2": 0.26, "c3": 0.653, "c4": 0.62}, {"time": 0.4, "angle": -4.44, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.5333, "angle": -52.7, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 0.7667}]}, "bone77": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone78": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone79": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone80": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone82": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone83": {"rotate": [{"angle": 2.11, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.68, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.7667, "angle": 2.11}]}, "bone84": {"rotate": [{"angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 3.68}]}, "bone86": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone87": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone88": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone89": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone90": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone91": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone93": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone94": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone95": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 18.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone96": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 13.87, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone97": {"rotate": [{"angle": 2.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -13.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 13.87, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7667, "angle": 2.56}]}, "bone98": {"rotate": [{"angle": 9.21, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -13.46, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 13.87, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.7667, "angle": 9.21}]}}, "deform": {"default": {"xuanwu032": {"xuanwu032": [{"time": 0.7428, "vertices": [167.65701, 3.78493, 156.72917, -59.65868, 167.65701, 3.78493, 156.72917, -59.65868, 153.22214, -68.16284, 167.65701, 3.78493, -164.10587, 34.5322, 153.22214, -68.16284, 167.65701, 3.78493, -166.62822, -18.92688, -164.10587, 34.5322, 153.22214, -68.16284, -166.62822, -18.92688, -164.10587, 34.5322, 153.22214, -68.16284, -166.62822, -18.92688, -166.62822, -18.92688, -166.62822, -18.92688, -166.62822, -18.92688, -164.10587, 34.5322, -166.62822, -18.92688, -164.10587, 34.5322, -164.10587, 34.5322, 153.22214, -68.16284, -164.10587, 34.5322, 153.22214, -68.16284, 167.65701, 3.78493, -164.10587, 34.5322, 153.22214, -68.16284, 167.65701, 3.78493, 156.72917, -59.65868, 153.22214, -68.16284, 167.65701, 3.78493, -164.10587, 34.5322, 153.22214, -68.16284, 167.65701, 3.78493, -164.10587, 34.5322, -166.62822, -18.92688]}]}}}, "events": [{"time": 0.4667, "name": "atk"}]}, "boss_idle": {"slots": {"xuanwu036": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "color": "ffffff88", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"bone2": {"rotate": [{"angle": 5.12, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.22, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.12}]}, "bone4": {"rotate": [{"angle": -3.02, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -4.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -3.02}]}, "bone5": {"rotate": [{"angle": -4.22, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.22}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 11.05, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone31": {"rotate": [{"angle": 1.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 3.84, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.09}]}, "bone32": {"rotate": [{"angle": 2.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 3.84, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.75}]}, "bone33": {"rotate": [{"angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.84}]}, "bone34": {"rotate": [{"angle": 2.75, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.75}]}, "bone35": {"rotate": [{"angle": 1.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.09}]}, "bone36": {"rotate": [{"angle": 0.68}]}, "bone37": {"rotate": [{"angle": -2.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 11.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -2.49}]}, "bone38": {"rotate": [{"angle": 1.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 0.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 1.56}]}, "bone39": {"rotate": [{"angle": -0.01}]}, "bone40": {"rotate": [{"angle": -0.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 29.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -0.14}]}, "bone41": {"rotate": [{"angle": 0.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -10.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 0.55}]}, "bone42": {"rotate": [{"angle": -0.46}]}, "bone43": {"rotate": [{"angle": -1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -12.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -1.74}]}, "bone44": {"rotate": [{"angle": 2.33, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 7.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 2.33}]}, "bone45": {"rotate": [{"angle": 0.55}]}, "bone46": {"rotate": [{"angle": -0.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -0.27}]}, "bone47": {"rotate": [{"angle": -0.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 10.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -0.81}]}, "bone53": {"rotate": [{"angle": 0.72, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.69, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.72}]}, "bone54": {"rotate": [{"angle": 4.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 3.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3333, "angle": 9.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 4.99}]}, "bone55": {"rotate": [{"angle": 5.55, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -9.67, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.55}]}, "bone56": {"rotate": [{"angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.92, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.55}]}, "bone57": {"rotate": [{"angle": -0.19, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 23.68, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.19}]}, "bone58": {"rotate": [{"angle": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.31, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.36}]}, "bone59": {"rotate": [{"angle": 0.72, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -28.73, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.72}]}, "bone60": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.07, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.07, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone64": {"rotate": [{"angle": 5.61, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -12.37, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.61}]}, "bone66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.77, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone67": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.77, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone68": {"rotate": [{"angle": -6.23, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -21.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -6.23}], "scale": [{"x": 0.269, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.269, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.269, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.269, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.269, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone69": {"rotate": [{"angle": -15.72, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -21.95, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -15.72}], "scale": [{"x": 0.269, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "bone70": {"rotate": [{"angle": -21.95, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -21.95}], "scale": [{"x": 0.269, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "bone75": {"rotate": [{"angle": 3.01, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3667, "angle": 9.26, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "angle": 3.01}]}, "bone76": {"rotate": [{"angle": 7.38, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.7333, "angle": 9.26, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 7.38}]}, "bone78": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 17.77, "y": 10.37, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone79": {"translate": [{"x": 5.04, "y": 2.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 17.77, "y": 10.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 5.04, "y": 2.94}]}, "bone80": {"translate": [{"x": 12.73, "y": 7.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "x": 17.77, "y": 10.37, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 12.73, "y": 7.42}]}, "bone81": {"translate": [{"x": 17.77, "y": 10.37, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 17.77, "y": 10.37}]}, "bone82": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone83": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone84": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone86": {"translate": [{"x": 5.04, "y": 2.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 17.77, "y": 10.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 5.04, "y": 2.94}]}, "bone87": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 17.77, "y": 10.37, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone88": {"translate": [{"x": 12.73, "y": 7.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "x": 17.77, "y": 10.37, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 12.73, "y": 7.42}]}, "bone89": {"translate": [{"x": 17.77, "y": 10.37, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 17.77, "y": 10.37}]}, "bone90": {"translate": [{"x": 12.73, "y": 7.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 17.77, "y": 10.37, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 12.73, "y": 7.42}]}, "bone91": {"translate": [{"x": 5.04, "y": 2.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 17.77, "y": 10.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 5.04, "y": 2.94}]}, "bone93": {"translate": [{"x": 17.77, "y": 10.37, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 17.77, "y": 10.37}]}, "bone94": {"translate": [{"x": 12.73, "y": 7.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "x": 17.77, "y": 10.37, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 12.73, "y": 7.42}]}, "bone95": {"translate": [{"x": 5.04, "y": 2.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 17.77, "y": 10.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 5.04, "y": 2.94}]}, "bone3": {"rotate": [{"angle": -1.2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.22, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.2}]}, "bone96": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.91, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone97": {"rotate": [{"angle": 2.81, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 9.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.81}]}, "bone98": {"rotate": [{"angle": 7.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.91, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.1}]}}}, "die": {"slots": {"xuanwu034": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "xuanwu033": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "xuanwu010": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 16.28}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -11.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 29.12}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -31.55}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 23.46}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 23.46}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -41.86}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -41.86}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -41.86}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -41.86}]}, "bone35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -41.86}]}, "bone36": {"rotate": [{"angle": 0.68}]}, "bone37": {"rotate": [{"angle": -2.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -55.39}]}, "bone38": {"rotate": [{"angle": 1.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.73}]}, "bone39": {"rotate": [{"angle": -0.01}]}, "bone40": {"rotate": [{"angle": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -73.56}]}, "bone41": {"rotate": [{"angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 46.66}]}, "bone42": {"rotate": [{"angle": -0.46}]}, "bone43": {"rotate": [{"angle": -1.74}]}, "bone44": {"rotate": [{"angle": 2.33}]}, "bone45": {"rotate": [{"angle": 0.55}]}, "bone46": {"rotate": [{"angle": -0.27}]}, "bone47": {"rotate": [{"angle": -0.81}]}, "bone48": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 53.37, "y": -200.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 97.49, "y": -7.13}]}, "bone49": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 53.37, "y": -200.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 97.49, "y": -7.13}]}, "bone50": {"translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 57.46}]}, "bone51": {"translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 57.46}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.22}]}, "bone55": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 89.31}]}, "bone56": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 91.21}]}, "bone57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.22}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.22}]}, "bone59": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.22}]}}}, "hurt": {"bones": {"bone52": {"translate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "x": -4.72, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.2333, "x": 37.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3333}]}, "bone": {"translate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "x": 10.26, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.2333, "x": -21.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 6.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -7.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 6.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone4": {"rotate": [{"angle": -5.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": -5.14}]}, "bone5": {"rotate": [{"angle": -5.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": -5.14}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone33": {"rotate": [{"angle": -0.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -1.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": -0.96}]}, "bone34": {"rotate": [{"angle": -0.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -1.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": -0.96}]}, "bone35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone36": {"rotate": [{"angle": 0.68}]}, "bone37": {"rotate": [{"angle": -2.49}]}, "bone38": {"rotate": [{"angle": 1.56}]}, "bone39": {"rotate": [{"angle": -0.01}]}, "bone40": {"rotate": [{"angle": -0.14}]}, "bone41": {"rotate": [{"angle": 0.55}]}, "bone42": {"rotate": [{"angle": -0.46}]}, "bone43": {"rotate": [{"angle": -1.74}]}, "bone44": {"rotate": [{"angle": 2.33}]}, "bone45": {"rotate": [{"angle": 0.55}]}, "bone46": {"rotate": [{"angle": -0.27}]}, "bone47": {"rotate": [{"angle": -0.81}]}, "bone53": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 5.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone54": {"rotate": [{"angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.95}]}, "bone55": {"rotate": [{"angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.95}]}, "bone56": {"rotate": [{"angle": -1.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 5.2, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -1.84}]}, "bone57": {"rotate": [{"angle": -1.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 5.2, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -1.84}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 5.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone59": {"rotate": [{"angle": -4.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -4.49}]}, "bone77": {"translate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "x": -10.04, "y": -11.04, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.2333, "x": 24.28, "y": 9.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3333}]}, "bone78": {"translate": [{"x": 14.84, "y": 2.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -20.36, "y": -10.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 20.71, "y": 3.64, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": 14.84, "y": 2.6}]}, "bone79": {"translate": [{"x": 5.61, "y": -1.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "x": 20.71, "y": 3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -20.36, "y": -10.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "x": 5.61, "y": -1.71}]}, "bone80": {"translate": [{"x": -5.25, "y": -5.56, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": 20.71, "y": 3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -20.36, "y": -10.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -5.25, "y": -5.56}]}, "bone81": {"translate": [{"x": 14.84, "y": 2.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -20.36, "y": -10.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 20.71, "y": 3.64, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": 14.84, "y": 2.6}]}, "bone85": {"translate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "x": -12.05, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.2333, "x": 31.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3333}]}, "bone86": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -20.36, "y": -10.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 20.71, "y": 3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone87": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -20.36, "y": -10.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 20.71, "y": 3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone88": {"translate": [{"x": 14.84, "y": 2.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -20.36, "y": -10.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 20.71, "y": 3.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": 14.84, "y": 2.6}]}, "bone89": {"translate": [{"x": 15.37, "y": 1.74, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "x": 20.71, "y": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -20.36, "y": -10.9, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "x": 15.37, "y": 1.74}]}, "bone90": {"translate": [{"x": -5.25, "y": -5.56, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": 20.71, "y": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -20.36, "y": -10.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -5.25, "y": -5.56}]}, "bone91": {"translate": [{"x": -5.25, "y": -5.56, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": 20.71, "y": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -20.36, "y": -10.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -5.25, "y": -5.56}]}, "bone93": {"translate": [{"x": -20.36, "y": -10.9, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 20.71, "y": 3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -20.36, "y": -10.9}]}, "bone94": {"translate": [{"x": -5.25, "y": -5.56, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": 20.71, "y": 3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -20.36, "y": -10.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -5.25, "y": -5.56}]}, "bone95": {"translate": [{"x": 15.37, "y": 1.74, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "x": 20.71, "y": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -20.36, "y": -10.9, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "x": 15.37, "y": 1.74}]}}}}}