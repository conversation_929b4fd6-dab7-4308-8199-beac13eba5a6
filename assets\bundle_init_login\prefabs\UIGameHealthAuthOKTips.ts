import { _decorator, Component, Label, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { TimeUtils } from "../../GameScrpit/lib/utils/TimeUtils";
import { GameData } from "../../GameScrpit/game/GameData";
import { SceneLogin } from "../../script_game/scene_login/SceneLogin";
const { ccclass, property } = _decorator;

/**
 * i<PERSON>_huang
 * Wed Mar 05 2025 16:55:58 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_game_health/UIGameHealthAuthOKTips.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIGameHealthAuthOKTips")
export class UIGameHealthAuthOKTips extends Component {
  @property(Label)
  private lblCurTime: Label;
  @property(Label)
  private lblLeftTime: Label;

  protected start(): void {
    this.lblCurTime.string = TimeUtils.formatTimestamp(TimeUtils.serverTime, "YYYY/MM/DD HH:mm:ss");
    this.lblLeftTime.string = TimeUtils.formatTimeLeft(GameData.instance.forceLogoutTime - TimeUtils.serverTime);
  }
  private onClickOk() {
    // 点击确定后关闭窗口，显示进入游戏页面
    SceneLogin.routeMgr.back();
  }
}
