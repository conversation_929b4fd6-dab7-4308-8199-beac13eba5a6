import { _decorator, Component, Node, UITransform, v3 } from "cc";
const { ccclass, property } = _decorator;

@ccclass("hunt_buddy")
export class hunt_buddy extends Component {
  //@property(Boolean)
  isMove: boolean = true;

  //@property(Number)
  speed: number = 300;

  // @property(Node)
  boundary: Node = null;

  private _curDir: number = null;

  private _dirMoveTime: number = 0;
  update(dt: number) {
    if (this._dirMoveTime <= 0) {
      this._curDir = this.getDirection();
      this._dirMoveTime = 0.2;
    }

    if (this.isMove) {
      switch (this._curDir) {
        case 0:
          this.leftMove(dt);
        case 1:
          this.rightMove(dt);
        case 2:
          this.upMove(dt);
        case 3:
          this.downMove(dt);
      }
    }
    this._dirMoveTime -= dt;

    if (this._dirMoveTime <= 0) {
    }
  }

  private getDirection() {
    let dir = Math.floor(Math.random() * 3);
    while (dir == this._curDir) {
      dir = Math.floor(Math.random() * 3);
      if (dir != this._curDir) {
        return;
      }
    }

    switch (dir) {
      case 0:
        return 0;
      case 1:
        return 1;
      case 2:
        return 2;
      case 3:
        return 3;
    }
  }

  private leftMove(dt: number) {
    let dot = -this.speed * dt;
    if (this.isCrashBoundaryLeft(dot)) {
      this.node.setPosition(v3(this.node.getPosition().x + dot, this.node.getPosition().y, 1));
    }
  }
  private isCrashBoundaryLeft(dot: number) {
    let newX = this.node.getPosition().x + dot + this.node.getComponent(UITransform).width / 2;
    if (newX <= -this.boundary.getComponent(UITransform).width / 2) {
      this._curDir = null;
      this._dirMoveTime = 0;
      return false;
    }
    return true;
  }

  private rightMove(dt: number) {
    let dot = this.speed * dt;
    if (this.isCrashBoundaryRight(dot)) {
      this.node.setPosition(v3(this.node.getPosition().x + dot, this.node.getPosition().y, 1));
    }
  }

  private isCrashBoundaryRight(dot: number) {
    let newX = this.node.getPosition().x + dot + this.node.getComponent(UITransform).width / 2;
    if (newX >= this.boundary.getComponent(UITransform).width / 2) {
      this._curDir = null;
      this._dirMoveTime = 0;
      return false;
    }
    return true;
  }

  private upMove(dt: number) {
    let dot = this.speed * dt;
    if (this.isCrashBoundaryUp(dot)) {
      this.node.setPosition(v3(this.node.getPosition().x, this.node.getPosition().y + dot, 1));
    }
  }

  private isCrashBoundaryUp(dot: number) {
    let newY = this.node.getPosition().y + dot;
    if (newY >= this.boundary.getComponent(UITransform).height / 2) {
      this._curDir = null;
      this._dirMoveTime = 0;
      return false;
    }
    return true;
  }

  private downMove(dt: number) {
    let dot = -this.speed * dt;
    if (this.isCrashBoundaryDown(dot)) {
      this.node.setPosition(v3(this.node.getPosition().x, this.node.getPosition().y + dot, 1));
    }
  }

  private isCrashBoundaryDown(dot: number) {
    let newY = this.node.getPosition().y + dot;
    if (newY <= -this.boundary.getComponent(UITransform).height / 2) {
      this._curDir = null;
      this._dirMoveTime = 0;
      return false;
    }
    return true;
  }
}
