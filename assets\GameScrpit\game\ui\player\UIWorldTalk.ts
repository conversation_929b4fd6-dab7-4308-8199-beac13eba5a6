import { _decorator, Component, EditBox, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { PageZero } from "../../GameDefine";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import { GoodsAddRequest } from "../../net/protocol/Goods";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ChatRouteItem } from "../../../module/chat/ChatRoute";
const { ccclass, property } = _decorator;

@ccclass("UIWorldTalk")
export class UIWorldTalk extends Component {
  @property(Node)
  private uiItemAdd: Node;
  @property(EditBox)
  private editBox: EditBox;
  @property(EditBox)
  private numEditBoxt: EditBox;

  protected onEvtShow(): void {}

  private on_click_btn_add_item() {
    this.uiItemAdd.active = true;
  }

  private on_click_btn_use() {
    let resId = Number(this.editBox.string);
    let num = Number(this.numEditBoxt.string);

    let data: GoodsAddRequest = {
      resId: resId,
      num: num,
    };
    GoodsModule.api.addRes(data, (data) => {
      this.on_click_btn_close();
    });
  }

  private on_click_btn_close() {
    this.editBox.string = "";
    this.numEditBoxt.string = "";
    this.uiItemAdd.active = false;
  }
}
