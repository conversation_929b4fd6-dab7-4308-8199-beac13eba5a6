import { _decorator, Input, instantiate, Label, Node, ProgressBar, Size, Slider, UITransform } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import ResMgr from "../../../lib/common/ResMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CityCreateResponse } from "../../net/protocol/City";
import ToolExt from "../../common/ToolExt";
import Formate from "../../../lib/utils/Formate";
import { CityModule } from "../../../module/city/CityModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { CityCtrl } from "../ui_gameMap/CityCtrl";
import { ArgsCityBuildRes } from "./UICityBuildRes";
import { v3 } from "cc";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { FightModule } from "../../../module/fight/src/FightModule";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { CityAudioName } from "../../../module/city/CityConstant";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import FmUtils from "../../../lib/utils/FmUtils";

const { ccclass, property } = _decorator;

@ccclass("UICityBuild")
export class UICityBuild extends UINode {
  protected _openAct: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UICityBuild`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_HERO_ICON];
  }

  /**================================================= */
  private _cityId: number;
  // private _buildConfigData: ConfigBuildRecord;

  public init(param) {
    super.init(param);
    this._cityId = param.cityId;
  }

  protected onEvtShow(): void {
    this.refresh();
    this.setCityDes();
  }

  private async refresh() {
    let buildConfigData = CityModule.data.getConfigBuild(this._cityId);
    FmUtils.setDialogTitle(this.getNode("DialogPrimary"), buildConfigData.name);
    this.getNode("title_lab").getComponent(Label).string = `${CityModule.data.getTypeName(
      buildConfigData.type
    )} · 下辖领地`;

    this.getNode("city_bloom_lab").getComponent(Label).string = `气运 ${buildConfigData.goldProduce} / 秒`;
    ToolExt.setRaceType(this.getNode("type_icon"), buildConfigData.type, this);

    this.getNode("city_cost_lab").getComponent(Label).string = `${Formate.format(
      PlayerModule.data.getItemNum(ItemEnum.气运_1)
    )}/${Formate.format(buildConfigData.buildCost)}`;

    ToolExt.setLabColor(
      this.getNode("city_cost_lab").getComponent(Label),
      PlayerModule.data.getItemNum(ItemEnum.气运_1) < buildConfigData.buildCost ? "#e10000" : "#00af04"
    );

    // 设置城市icon
    ResMgr.loadPrefab(
      `${BundleEnum.BUNDLE_G_GAME_MAP}?prefab/building/city_${this._cityId}`,
      (pb) => {
        let nodeCity: Node = instantiate(pb);

        nodeCity.getComponent(CityCtrl).setOnlyShow({
          hideUI: true,
          cityLevel: 1,
        });
        this.getNode("city_img").addChild(nodeCity);
        nodeCity.setPosition(0, 0, 0);
        nodeCity.walk((val) => {
          val.layer = this.getNode("city_img").layer;
        });
        let aniNode = NodeTool.findByName(nodeCity, "ani");
        CityModule.service.setBuildSize(aniNode, this._cityId, 1);
      },
      this
    );

    // 进度
    let arr = CityModule.service.getCityLevelList(this._cityId);
    if (arr && arr.length > 0) {
      let chapterId = FightModule.data.chapterId;
      let index = 0;
      for (let i = 0; i < arr.length; i++) {
        if (chapterId > arr[i].id) {
          index = i + 1;
        }
      }

      let progress = index / arr.length;
      this.getNode("ProgressBar").getComponent(ProgressBar).progress = progress;
      this.getNode("Slider").getComponent(Slider).progress = progress;
      this.getNode("Slider").getComponent(Slider).enabled = false;
      this.getNode("lbl_progress").getComponent(Label).string = `${index}/${arr.length}`;
    } else {
      this.getNode("ProgressBar").active = false;
      this.getNode("Slider").active = false;
      this.getNode("tip_lab").active = false;
    }

    /**已经领取过的城池通关奖励 */
    let hasGet: boolean = CityModule.data.cityAggregateMessage.rewardCityList.indexOf(this._cityId) != -1;

    let Item = this.getNode("Item");
    this.getNode("content").removeAllChildren();
    this.getNode("award_lay").removeAllChildren();
    this.getNode("ScrollView").active = buildConfigData.rewardList.length > 6;
    this.getNode("award_lay").active = buildConfigData.rewardList.length <= 6;
    for (let i = 0; i < buildConfigData.rewardList.length; i++) {
      let reward = buildConfigData.rewardList[i];
      let itemId = reward[0];
      let config = JsonMgr.instance.getConfigItem(itemId);
      let node = instantiate(Item);
      node.active = true;

      FmUtils.setItemNode(node, config.id, reward[1]);
      //ToolExt.setItemBg(node.getChildByName("itemBg"), config.color);
      //ToolExt.setItemIcon(node.getChildByName("itemIcon"), config.id);
      //node.getChildByName("unGet").getChildByName("num").getComponent(Label).string = `${reward[1]}`;

      node.getChildByName("get").active = hasGet;
      node.getChildByName("unGet").active = !hasGet;
      node.active = true;

      if (buildConfigData.rewardList.length > 6) {
        this.getNode("content").addChild(node);
      } else {
        this.getNode("award_lay").addChild(node);
      }

      /**注册点击事件 */
      node.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.on_click_btn_reward.bind(this)), this);
    }
  }

  private on_click_btn_reward() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (CityModule.data.cityAggregateMessage.rewardCityList.indexOf(this._cityId) != -1) {
      return;
    }

    CityModule.api.taskChapterReward(this._cityId, (data: number[]) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data });
      this.refresh();
    });
  }

  private on_click_btn_build() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    // 建造

    let configBuild = CityModule.data.getConfigBuild(this._cityId);

    let lackItem = PlayerModule.service.checkitemEnought([
      ItemEnum.气运_1,
      configBuild.buildCost,
      ItemEnum.繁荣度_2,
      Number(configBuild.goldSpeed),
    ]);

    if (lackItem.length > 0) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: lackItem[0],
        needNum: lackItem[1],
      });
      return;
    }

    // 建造
    let argsCityBuildRes: ArgsCityBuildRes = {
      oldBloom: CityModule.data.getCityBloom(this._cityId),
      cityMsg: undefined,
      isCreate: true,
      rewardList: [],
    };

    CityModule.api.createCity(this._cityId, (cityCreate: CityCreateResponse) => {
      AudioMgr.instance.playEffect(CityAudioName.Effect.解锁成功);
      TipsMgr.setEnableTouch(false, 3, false);
      setTimeout(() => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: cityCreate.rewardList });
      }, 2100);
      UIMgr.instance.back();
    });
  }

  private setCityDes() {
    let c_build = JsonMgr.instance.jsonList.c_build;
    let db = c_build[this._cityId];
    let str = db.des;
    let des_lab = this.getNode("des_lab");
    // 清空现有的文字
    this.getNode("des_lay").children.forEach((element) => {
      if (element.name == "des_lab") {
        element.destroy();
      }
    });
    let max = Math.ceil(str.length / 20);
    max = max > 3 ? 3 : max;
    for (let i = 0; i < max; i++) {
      let node = instantiate(des_lab);
      let str1 = str.substring(i * 20, (i + 1) * 20);
      node.getComponent(Label).string = `${str1}`;
      this.getNode("des_lay").addChild(node);
      node.setSiblingIndex(i + 1);
      node.active = true;
      if (i == max - 1) {
        let mask = this.getNode("des_lay").getChildByName("Mask");
        let contentSize = mask.getComponent(UITransform).contentSize;
        // 动态更新label渲染，获取当前帧的高度或宽度
        node.getComponent(Label).updateRenderData(true);
        let contentSize1 = node.getComponent(UITransform).contentSize;
        mask.getComponent(UITransform).setContentSize(new Size(contentSize.width, contentSize1.y + 10));
      }
    }
  }
}
