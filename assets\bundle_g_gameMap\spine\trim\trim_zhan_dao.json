{"skeleton": {"hash": "ZhkQXRJYo9eZvgFtFEO7XvxwZl8", "spine": "3.8.75", "x": -217.18, "y": -129.24, "width": 441.19, "height": 213.33, "images": "./images/", "audio": "D:/建筑升级/栈道"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "1", "parent": "bone", "length": 242.15, "rotation": -178.62, "x": 221.7, "y": 28.22}, {"name": "2", "parent": "1", "length": 49.32, "rotation": 88.62, "x": 260.24, "y": 36.41}, {"name": "11", "parent": "bone", "length": 61.66, "rotation": -179.4, "x": 212.28, "y": 56.28}, {"name": "3", "parent": "11", "length": 69.44, "rotation": -0.6, "x": 66.84, "y": -0.7}, {"name": "5", "parent": "3", "length": 93.51, "rotation": 1.99, "x": 70.74}, {"name": "4", "parent": "5", "length": 41.07, "rotation": 29.44, "x": 100.58, "y": -2.19}, {"name": "7", "parent": "4", "length": 42.84, "rotation": -30.56, "x": 43.63, "y": -0.8}, {"name": "21", "parent": "bone", "length": 96.07, "rotation": -153.95, "x": 166.85, "y": 34.87}, {"name": "31", "parent": "21", "length": 83.84, "rotation": -22.94, "x": 98.41, "y": -1.14}, {"name": "6", "parent": "bone", "length": 97.35, "rotation": -90, "x": -44.73, "y": -25.49}, {"name": "8", "parent": "bone", "length": 96.7, "rotation": -179.62, "x": -109.63, "y": 12.8}, {"name": "yinz", "parent": "bone", "length": 415.36, "rotation": -179.82, "x": 202.75, "y": 14.75}, {"name": "yinz2", "parent": "yinz", "length": 71.39, "rotation": 89.3, "x": 243.55, "y": 55.7}, {"name": "bone2", "parent": "root", "x": 54.52, "y": -75.58}], "slots": [{"name": "yinz", "bone": "yinz", "attachment": "yinz"}, {"name": "7", "bone": "8", "attachment": "7"}, {"name": "1", "bone": "1", "attachment": "1"}, {"name": "11", "bone": "11", "attachment": "11"}, {"name": "2", "bone": "3", "attachment": "2"}, {"name": "3", "bone": "5", "attachment": "3"}, {"name": "4", "bone": "4", "attachment": "4"}, {"name": "5", "bone": "7", "attachment": "5"}, {"name": "31", "bone": "31", "attachment": "31"}, {"name": "21", "bone": "21", "attachment": "21"}, {"name": "6", "bone": "6", "attachment": "6"}, {"name": "guang", "bone": "bone2", "attachment": "guang", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"yinz": {"yinz": {"type": "mesh", "uvs": [0.48117, 0.28725, 0.99806, 0.28767, 0.99984, 0.34122, 0.99311, 0.37043, 0.90431, 0.3397, 0.86796, 0.35035, 0.6642, 0.5473, 0.54038, 0.54811, 0.49227, 0.6081, 0.48708, 0.92708, 0.465, 0.99183, 0.26204, 0.97964, 0.26934, 0.77574, 0.26, 0.72463, 0.18723, 0.565, 0.04324, 0.54643, 0.01798, 0.56485, 0.00222, 0.53847, 0.00192, 0.28719], "triangles": [9, 10, 12, 10, 11, 12, 9, 12, 8, 12, 13, 8, 8, 13, 0, 7, 8, 0, 0, 13, 14, 0, 14, 18, 16, 17, 15, 7, 0, 6, 6, 0, 5, 18, 14, 15, 15, 17, 18, 2, 3, 1, 4, 5, 1, 3, 4, 1, 1, 5, 0], "vertices": [1, 13, 214.3, -9.11, 1, 1, 13, -7.45, -8.33, 1, 1, 13, -8.17, 3.02, 1, 1, 13, -5.26, 9.21, 1, 1, 13, 32.81, 2.57, 1, 1, 13, 48.41, 4.78, 1, 2, 13, 135.95, 46.26, 0.99136, 14, -10.76, 107.47, 0.00864, 2, 13, 189.07, 46.27, 0.72316, 14, -10.1, 54.36, 0.27684, 2, 13, 209.75, 58.92, 0.23631, 14, 2.8, 33.84, 0.76369, 1, 14, 70.44, 32.22, 1, 1, 14, 84.26, 22.87, 1, 2, 13, 308.76, 137.38, 0.00176, 14, 82.47, -64.21, 0.99824, 2, 13, 305.5, 94.16, 0.13011, 14, 39.21, -61.48, 0.86989, 2, 13, 309.47, 83.31, 0.2623, 14, 28.41, -65.58, 0.7377, 2, 13, 340.59, 49.37, 0.83325, 14, -5.14, -97.11, 0.16675, 2, 13, 402.34, 45.24, 0.99941, 14, -8.52, -158.91, 0.00059, 2, 13, 413.19, 49.11, 0.99998, 14, -4.51, -169.71, 2e-05, 1, 13, 419.93, 43.5, 1, 1, 13, 419.9, -9.77, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 0], "width": 429, "height": 212}}, "31": {"31": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [0.34, 35.63, 88.21, 30.86, 84.85, -31.05, -3.02, -26.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 62}}, "11": {"11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-10.06, 18.3, 65.94, 17.5, 65.48, -26.5, -10.52, -25.7], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 44}}, "guang": {"guang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [169.49, -51.74, -259.51, -53.66, -260.45, 156.34, 168.55, 158.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 429, "height": 210}}, "1": {"1": {"type": "mesh", "uvs": [0.99441, 0.04105, 0.99439, 0.31821, 0.96614, 0.29474, 0.88063, 0.26073, 0.84686, 0.27805, 0.5996, 0.62461, 0.42513, 0.632, 0.35201, 0.63025, 0.35167, 0.96941, 0.14101, 0.99363, 0.13721, 0.94876, 0.13675, 0.4347, 0.05685, 0.43444, 0.05716, 0.19092, 0.30076, 0.00699, 1, 0.00778], "triangles": [10, 11, 7, 8, 10, 7, 9, 10, 8, 14, 15, 4, 0, 3, 15, 3, 4, 15, 2, 3, 0, 1, 2, 0, 11, 13, 14, 12, 13, 11, 5, 14, 4, 6, 7, 14, 11, 14, 7, 5, 6, 14], "vertices": [1, 2, 2.85, -11.73, 1, 1, 2, 3.7, 23.46, 1, 1, 2, 13.51, 20.24, 1, 1, 2, 43.33, 15.2, 1, 1, 2, 55.2, 17.12, 1, 2, 2, 142.41, 59.03, 0.9822, 3, 20.26, 97.88, 0.0178, 1, 2, 203.85, 58.49, 1, 2, 2, 223.97, 57.66, 0.73354, 3, 20.86, 16.31, 0.26646, 1, 3, 63.57, 31.2, 1, 1, 3, 66.64, -42.53, 1, 1, 3, 60.95, -43.86, 1, 2, 2, 299.24, 31.01, 0.76, 3, -3.97, -59.57, 0.24, 1, 2, 332.1, 30.3, 1, 1, 2, 331.25, -0.61, 1, 1, 2, 245.45, -21.91, 1, 1, 2, 0.79, -15.91, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30], "width": 350, "height": 127}}, "2": {"2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-3.1, 19.19, 72.9, 19.19, 72.9, -23.81, -3.1, -23.81], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 43}}, "3": {"3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-3.2, 18.32, 101.73, 14.67, 100.14, -31.3, -4.8, -27.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 105, "height": 46}}, "4": {"4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.84, 38.25, 58.5, 12.18, 23.04, -45.85, -19.62, -19.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 50, "height": 68}}, "5": {"5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-1.79, 18.86, 62.2, 17.89, 61.54, -26.11, -2.46, -25.14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 44}}, "6": {"6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [99.42, 36.25, 99.42, -34.75, -3.58, -34.75, -3.58, 36.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "height": 103}}, "7": {"7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-1.95, 30.72, 99.05, 30.05, 98.68, -24.95, -2.32, -24.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 101, "height": 55}}, "21": {"21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [26.01, 61.61, 109.57, 20.78, 69.17, -61.88, -14.38, -21.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 92}}}}], "animations": {"zhan_dao": {"slots": {"guang": {"attachment": [{"name": null}]}}}, "zhan_dao_canpo": {"slots": {"3": {"attachment": [{"name": null}]}, "1": {"attachment": [{"name": null}]}, "2": {"attachment": [{"name": null}]}, "yinz": {"attachment": [{"name": null}]}, "21": {"attachment": [{"name": null}]}, "31": {"attachment": [{"name": null}]}, "5": {"attachment": [{"name": null}]}, "4": {"attachment": [{"name": null}]}, "11": {"attachment": [{"name": null}]}, "7": {"attachment": [{"name": null}]}, "guang": {"attachment": [{"name": null}]}}}, "zhan_dao_jiesuo": {"slots": {"yinz": {"attachment": [{"name": null}, {"time": 0.5667, "name": "yinz"}]}, "guang": {"color": [{"time": 0.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.7333, "name": "guang"}]}}, "bones": {"1": {"scale": [{"x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "angle": -11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.1333}], "translate": [{"x": -0.07, "y": 57.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1333}], "scale": [{"x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.1333}]}, "21": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "y": 8, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "scale": [{"x": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "6": {"scale": [{"x": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "8": {"scale": [{"x": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "2": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 0.4667, "x": 0.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "3": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -30, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "translate": [{"time": 0.1333, "x": -0.42, "y": -39.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "scale": [{"x": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "5": {"rotate": [{"time": 0.2667, "angle": -24, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"time": 0.2333, "x": 0.76, "y": -21.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "scale": [{"x": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "4": {"rotate": [{"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -33, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -0.97, "y": -27.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "scale": [{"x": 0, "curve": "stepped"}, {"time": 0.3667, "x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "7": {"rotate": [{"time": 0.4667}, {"time": 0.5, "angle": -33.6}, {"time": 0.6}], "translate": [{"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -15.53, "y": -25.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "scale": [{"x": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "31": {"rotate": [{"time": 0.2333}, {"time": 0.2667, "angle": -15.06}, {"time": 0.3667}], "translate": [{"time": 0.2333, "x": -5.27, "y": -10.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "scale": [{"x": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "yinz": {"scale": [{"x": 0, "y": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.315, "y": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.76, "y": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "yinz2": {"scale": [{"x": 0, "y": 0}]}, "bone2": {"translate": [{"x": 4}], "scale": [{"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.06, "y": 1.06}]}}, "deform": {"default": {"1": {"1": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "offset": 10, "vertices": [-0.65403, -38.81956, -175.78114, -0.26839, -0.88139, -32.91463, -0.61586, -29.75638, -127.8381, -0.08916, -120.16191, -0.07861, -96.72113, 0.19385, -96.28192, 0.09399], "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "offset": 10, "vertices": [-0.64825, -26.89168, -224.16144, -0.00061, -0.40851, -16.93684, -0.17688, -7.33807, -61.16742], "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "yinz": {"yinz": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "offset": 8, "vertices": [-0.01985, -6.33199, -0.01994, -6.33241], "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}}}}}}