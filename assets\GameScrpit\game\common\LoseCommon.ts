import { _decorator, Component, Label, Node } from "cc";
import GuideMgr from "../../ext_guide/GuideMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import { LangMgr } from "../mgr/LangMgr";
const { ccclass, property } = _decorator;

@ccclass("LoseCommon")
export class LoseCommon extends Component {
  @property(Label)
  private lbl1: Label;

  protected onLoad(): void {
    this.lbl1.string = LangMgr.txMsgCode(640, []);
  }
  onClickHero() {
    let route = UIMgr.instance.getRouteByName(this.node.parent.name);
    route.callBack = null;
    GuideMgr.startGuide({ stepId: 20 });
  }

  onClickFriend() {
    let route = UIMgr.instance.getRouteByName(this.node.parent.name);
    route.callBack = null;
    GuideMgr.startGuide({ stepId: 58 });
  }

  onClickPet() {
    let route = UIMgr.instance.getRouteByName(this.node.parent.name);
    route.callBack = null;
    GuideMgr.startGuide({ stepId: 64 });
  }

  onClickSoul() {
    let route = UIMgr.instance.getRouteByName(this.node.parent.name);
    route.callBack = null;
    GuideMgr.startGuide({ stepId: 46 });
  }

  onClickHorse() {
    let route = UIMgr.instance.getRouteByName(this.node.parent.name);
    route.callBack = null;
    GuideMgr.startGuide({ stepId: 35 });
  }
}
