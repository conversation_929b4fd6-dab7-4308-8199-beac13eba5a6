import { random, randomRangeInt, Vec3 } from "cc";
import { _decorator, Component, Node } from "cc";
import AssetUtils from "../../../lib/utils/AssetUtils";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { Prefab } from "cc";
import { instantiate } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CityRouteName } from "../../../module/city/CityConstant";
import { Label } from "cc";
import { tween } from "cc";
import { UITransform } from "cc";
import { UIOpacity } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("AddNumCtrl")
export class AddNumCtrl extends Component {
  @property(UIOpacity)
  private opacity: UIOpacity;

  public flyY: number = 150;
  public _duration: number = 2;

  start() {
    this.opacity.opacity = 255;
    tween(this.node)
      .hide()
      .show()
      .by(
        this._duration,
        { position: new Vec3(0, this.flyY, 0) },
        {
          easing: "cubicOut",
          onUpdate: (target, ratio) => {
            this.opacity.opacity = 255 * this.transform(ratio);
          },
        }
      )
      .destroySelf()
      .start();
  }

  private transform(ratio) {
    if (ratio < 0.6) {
      return 1;
    }
    return 1 - (ratio - 0.6) / 0.4;
  }

  // 固定飞行一段距离
  public static async showByPos(
    worldPos: Vec3,
    value: string,
    flyY = 150,
    isRandom: boolean = false,
    duration: number = 2
  ) {
    let pb: Prefab = (await AssetUtils.loadAsset(BundleEnum.BUNDLE_G_GAME_MAP, "prefab/add_num", Prefab)) as Prefab;

    let nodeStr = instantiate(pb);
    nodeStr.active = false;

    nodeStr.getComponentInChildren(Label).string = value;

    if (!UIMgr.instance.getByName(CityRouteName.UIGameMap)) {
      log.error("没有地图");
    }
    if (!UIMgr.instance.getByName(CityRouteName.UIGameMap).node) {
      log.error("没有地图根节点");
    }
    if (!UIMgr.instance.getByName(CityRouteName.UIGameMap).node.getChildByName("btn_map")) {
      log.error("没有地图根节点 --- btn_map");
    }

    UIMgr.instance.getByName(CityRouteName.UIGameMap).node.getChildByName("btn_map").addChild(nodeStr);
    if (isRandom) {
      let x = randomRangeInt(-100, 100);
      let y = randomRangeInt(60, 120);
      worldPos.x += x;
      worldPos.y += y;
    }
    nodeStr.setWorldPosition(worldPos);
    nodeStr.getComponent(AddNumCtrl).flyY = flyY;
    nodeStr.getComponent(AddNumCtrl)._duration = duration;
    nodeStr.active = true;
  }

  // 自动按大小向上飞行一段距离
  public static showByNode(node: Node, value: string, isRandom: boolean = false, duration: number = 2) {
    let pos = node.getWorldPosition();
    let tranform = node.getComponent(UITransform);
    let size = tranform.contentSize;
    let anchorY = tranform.anchorY;
    let flyY = size.y * (1 - anchorY);

    AddNumCtrl.showByPos(pos, value, flyY, isRandom, duration);
  }
}
