import { _decorator, instantiate, Label, Node, Sprite } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { LangMgr } from "../../../mgr/LangMgr";

const { ccclass, property } = _decorator;
@ccclass("FarmCollectTitleViewHolder")
export class FarmCollectTitleViewHolder extends ViewHolder {
  @property(Label)
  private award: Label;

  public init() {
    //item 创建
  }

  public updateData(position: number, data: any) {
    //列表item更新
    this.award.string = LangMgr.txMsgCode(456, [data.remainFetch, data.todayMax]); //`今日可采集次数：${data.remainFetch}/${data.todayMax}`;
  }
}
