import { _decorator, Component, Node, Label, tween, Vec3, Tween } from "cc";
import MsgMgr from "../../../GameScrpit/lib/event/MsgMgr";
import { PlayerEvent } from "../../../GameScrpit/module/player/PlayerEvent";
import Formate from "../../../GameScrpit/lib/utils/Formate";
import { math } from "cc";
import { GameDirector } from "db://assets/GameScrpit/game/GameDirector";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const { ccclass, property } = _decorator;

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

@ccclass("UIPowerTipsLayer")
export class UIPowerTipsLayer extends Component {
  // 繁荣度节点
  @property({ type: Node })
  nodeBloomChange: Node = null;

  // 繁荣度变化
  @property({ type: Label })
  lblBloom: Label = null;

  // 繁荣度变化
  @property({ type: Label })
  lblBloomChange: Label = null;

  @property({ type: Node })
  bgUpBloom: Node = null;

  @property({ type: Node })
  bgDownBloom: Node = null;

  private _bloomAni: Tween<Node> = null;
  private _bloomChangeStart: number = 0;
  private _bloomChangeEnd: number = 0;

  /** 战力节点 */
  @property({ type: Node })
  nodePowerChange: Node = null;

  /** 最新战力 */
  @property({ type: Label })
  lblPower: Label = null;

  /** 变化战力 */
  @property({ type: Label })
  lblPowerChange: Label = null;

  @property({ type: Node })
  bgUpPower: Node = null;

  @property({ type: Node })
  bgDownPower: Node = null;

  private _powerAni: Tween<Node> = null;
  private _powerChangeStart: number = 0;
  private _powerChangeEnd: number = 0;

  start() {
    MsgMgr.on(PlayerEvent.ON_PLAYER_POWER_CHANGE, this.handlePowerChange, this);
    MsgMgr.on(PlayerEvent.ON_PLAYER_BLOOM_CHANGE, this.handleBloomChange, this);

    this.nodeBloomChange.active = false;
    this.nodePowerChange.active = false;
  }

  onDestroy() {
    MsgMgr.off(PlayerEvent.ON_PLAYER_POWER_CHANGE, this.handlePowerChange, this);
    MsgMgr.off(PlayerEvent.ON_PLAYER_BLOOM_CHANGE, this.handleBloomChange, this);
  }

  private handlePowerChange(power: number, powerChange: number) {
    if (!powerChange) {
      return;
    }

    // 非运行状态不弹
    if (!GameDirector.instance.isRunning) {
      log.info("非运行状态不弹战力变化");
      return;
    }

    if (powerChange < 0) {
      this.lblPowerChange.color = math.color("ff6161");
      this.lblBloomChange.outlineColor = math.color("7a0909");
    } else {
      this.lblPowerChange.color = math.color("74FF77");
      this.lblBloomChange.outlineColor = math.color("0a740d");
    }

    this.lblPower.string = `${Formate.format(power)}`;
    this.lblPowerChange.string = "";

    if (this.nodePowerChange.active && this._powerAni) {
      this._powerAni.stop();
      this._powerChangeStart = this._powerChangeEnd;
      this._powerChangeEnd += powerChange;
    } else {
      this._powerChangeStart = 0;
      this._powerChangeEnd = powerChange;
      this.nodePowerChange.active = true;
      this.nodePowerChange.setScale(new Vec3(0.9, 0.9, 1));
      tween(this.nodePowerChange)
        .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: "backOut" })
        .start();
    }

    const isUp = this._powerChangeEnd > 0;
    this.bgUpPower.active = isUp;
    this.bgDownPower.active = !isUp;

    this._powerAni = tween(this.nodePowerChange)
      .to(
        0.7,
        {},
        {
          onUpdate: (target, dt: number) => {
            const v = Math.abs(this._powerChangeStart + dt * (this._powerChangeEnd - this._powerChangeStart));
            this.lblPowerChange.string = `${Formate.format(v)}`;
          },
        }
      )
      .delay(1.7)
      .call(() => {
        this.nodePowerChange.active = false;
        this._powerChangeStart = 0;
        this._powerChangeEnd = 0;
      })
      .start();
  }

  /**
   *
   * @param bloom 当前繁荣度
   * @param bloomChange 变化值
   */
  private handleBloomChange(bloom: number, bloomChange: number) {
    if (bloomChange == 0) {
      return;
    }
    // AudioMgr.instance.playEffect(AudioName.Effect.战力滚动);
    this.lblBloom.string = `繁荣度：${Formate.format(bloom)}`;

    if (this.nodeBloomChange.active && this._bloomAni) {
      this._bloomAni.stop();
      this._bloomChangeStart = this._bloomChangeEnd;
      this._bloomChangeEnd += bloomChange;
    } else {
      this._bloomChangeStart = 0;
      this._bloomChangeEnd = bloomChange;
      this.nodeBloomChange.active = true;
      this.nodeBloomChange.setScale(new Vec3(0.9, 0.9, 1));
      tween(this.nodeBloomChange)
        .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: "backOut" })
        .start();
    }

    const isUp = this._bloomChangeEnd > 0;
    this.bgUpBloom.active = isUp;
    this.bgDownBloom.active = !isUp;

    this._bloomAni = tween(this.nodeBloomChange)
      .to(
        0.7,
        {},
        {
          onUpdate: (target, dt: number) => {
            const v = this._bloomChangeStart + dt * (this._bloomChangeEnd - this._bloomChangeStart);
            this.lblBloomChange.string = `${Formate.format(v)}`;
          },
        }
      )
      .delay(1.7)
      .call(() => {
        this.nodeBloomChange.active = false;
        this._bloomChangeStart = 0;
        this._bloomChangeEnd = 0;
      })
      .start();
  }
}
