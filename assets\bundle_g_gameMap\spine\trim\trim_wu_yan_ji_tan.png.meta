{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "4e0ad556-7c16-4bc0-8510-ebe7c19f5f44", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "4e0ad556-7c16-4bc0-8510-ebe7c19f5f44@6c48a", "displayName": "trim_wu_yan_ji_tan", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "4e0ad556-7c16-4bc0-8510-ebe7c19f5f44", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "4e0ad556-7c16-4bc0-8510-ebe7c19f5f44@f9941", "displayName": "trim_wu_yan_ji_tan", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -1.5, "offsetY": -2, "trimX": 0, "trimY": 4, "width": 494, "height": 170, "rawWidth": 497, "rawHeight": 174, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-247, -85, 0, 247, -85, 0, -247, 85, 0, 247, 85, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 170, 494, 170, 0, 0, 494, 0], "nuv": [0, 0, 0.993963782696177, 0, 0, 0.9770114942528736, 0.993963782696177, 0.9770114942528736], "minPos": [-247, -85, 0], "maxPos": [247, 85, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "4e0ad556-7c16-4bc0-8510-ebe7c19f5f44@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "4e0ad556-7c16-4bc0-8510-ebe7c19f5f44@6c48a"}}