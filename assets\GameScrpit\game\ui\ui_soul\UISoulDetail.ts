import { _decorator, instantiate, Label, Node, Sprite, sp, UITransform } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UINode } from "../../../lib/ui/UINode";
import { WarriorSoulMessage } from "../../net/protocol/Soul";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import MsgMgr from "../../../lib/event/MsgMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import Formate from "../../../lib/utils/Formate";
import TipMgr from "../../../lib/tips/TipMgr";
import { SoulModule } from "../../../module/soul/SoulModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { SoulRouteName } from "../../../module/soul/SoulRoute";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import MsgEnum from "../../event/MsgEnum";
import { ConfirmMsg } from "../UICostConfirm";
import { EventTouch } from "cc";
import { math } from "cc";
import { AttrEnum } from "../../GameDefine";
import { FmColor } from "../../common/FmConstant";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { SoulAudioName } from "../../../module/soul/SoulConstant";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";

const { ccclass, property } = _decorator;

const ItemKey = "_KEY_";

// 按钮状态
const ItemKeyEnum = {
  LOCK: -1, // 锁定ID
  EMPTY: 0, // 空白ID
};

@ccclass("UISoulDetail")
export class UISoulDetail extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SOUL}?prefab/ui/UISoulDetail`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  // 当前选中
  private _currentSoulId: number;

  _attrList = [];

  /**注册监听 */
  protected onRegEvent() {
    MsgMgr.on(MsgEnum.ON_SOUL_UPDATE, this.refresh, this);
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.onItemChange, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_SOUL_UPDATE, this.refresh, this);
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.onItemChange, this);
  }

  onItemChange(id: number) {
    let cfgBaseSoul = SoulModule.data.getConfigSoul();

    // 关注的id列表
    let idList = [];

    function add(id: number) {
      if (!idList.includes(id)) {
        idList.push(id);
      }
    }

    add(cfgBaseSoul.costId);

    if (idList.includes(id)) {
      this.refresh();
    }
  }

  protected onEvtShow(): void {
    let soulIdList = Object.keys(SoulModule.data.warriorSoulManageMsg.soulMap);

    if (soulIdList.length == 0) {
      UIMgr.instance.showDialog(SoulRouteName.UISoulList, {}, () => {
        soulIdList = Object.keys(SoulModule.data.warriorSoulManageMsg.soulMap);
        if (soulIdList.length == 0) {
          UIMgr.instance.back();
        } else {
          this.refresh();
        }
      });
    } else {
      this.refresh();
    }
    BadgeMgr.instance.setBadgeId(this.getNode("btn_tab_soul"), BadgeType.UITerritory.btn_soul.btn_tab_soul.id);
    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_get_soul"),
      BadgeType.UITerritory.btn_soul.btn_tab_soul.btn_get_soul.id
    );
    BadgeMgr.instance.setBadgeId(this.getNode("btn_tab_tujian"), BadgeType.UITerritory.btn_soul.btn_tab_tujian.id);
  }

  private refresh() {
    // 统一配置数据
    const configBaseSoul = SoulModule.data.getConfigSoul();

    // 列表表格节点
    const nodeContent = this.getNode("node_content");

    // 武魂map
    const soulMap = SoulModule.data.warriorSoulManageMsg.soulMap;

    // 排序
    let idList = SoulModule.data.getSoulIdList();

    // 默认选中第一个, 放生后，当前选中没有了，默认第一个
    if (!this._currentSoulId || !SoulModule.data.warriorSoulManageMsg.soulMap[this._currentSoulId]) {
      this._currentSoulId = idList[0];
    }

    // 设置slot信息
    for (let i = 0; i < configBaseSoul.maxPlace; i++) {
      let nodeItem = nodeContent.children[i];
      if (!nodeItem) {
        nodeItem = instantiate(nodeContent.children[0]);
        nodeContent.addChild(nodeItem);
      }
      nodeItem.off(Node.EventType.TOUCH_END, this.onClickItem, this);
      nodeItem.on(Node.EventType.TOUCH_END, this.onClickItem, this);

      // 初始化显示状态
      nodeItem.getChildByName("bg").active = false;
      nodeItem.getChildByName("bg_empty").active = true;
      nodeItem.getChildByName("bg_lock").active = false;
      nodeItem.getChildByName("bg_soul_icon").active = false;
      nodeItem.getChildByName("ani_paomadeng").active = false;
      nodeItem.getChildByName("lbl_level").active = false;
      nodeItem.getChildByName("bg_up").active = false;

      // 状态判断与数据显示
      if (SoulModule.data.warriorSoulManageMsg.slot <= i) {
        // 锁定状态
        nodeItem.getChildByName("bg_lock").active = true;
        nodeItem[ItemKey] = ItemKeyEnum.LOCK;
      } else {
        let soulId = idList[i];

        if (soulId) {
          nodeItem[ItemKey] = soulId;
          // 有武魂的状态
          nodeItem.getChildByName("bg").active = true;
          nodeItem.getChildByName("bg_soul_icon").active = true;
          nodeItem.getChildByName("lbl_level").active = true;

          let soulMessage = soulMap[soulId];
          let itemConfig = JsonMgr.instance.getConfigItem(soulMessage.soulTemplateId);

          // 设置品质背景
          ToolExt.setItemBg(nodeItem.getChildByName("bg"), itemConfig.color);

          // // 设置图标
          // ResMgr.loadImage(
          //   `${BundleEnum.BUNDLE_G_SOUL}?images/wh_${soulMessage.soulTemplateId}`,
          //   nodeItem.getChildByName("bg_soul_icon").getComponent(Sprite),
          //   this
          // );

          let key = "wh_" + soulMessage.soulTemplateId;
          nodeItem.getChildByName("bg_soul_icon").getComponent(sp.Skeleton).setAnimation(0, key, true);

          // 设置等级
          nodeItem.getChildByName("lbl_level").getComponent(Label).string = `Lv.${
            soulMessage.grade + soulMessage.stage * 5
          }`;

          // 设置上阵状态
          nodeItem.getChildByName("bg_up").active = soulMessage.chosen;
        } else {
          // 空置状态
          nodeItem[ItemKey] = ItemKeyEnum.EMPTY;
        }
      }
    }
    this.onSelect(this._currentSoulId || idList[0]);
  }

  /**
   * 列表点击事件
   */
  public onClickItem(e: EventTouch) {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击兽魂图标);
    // 统一配置数据
    const configBaseSoul = SoulModule.data.getConfigSoul();

    // 锁定状态
    if (e.target[ItemKey] == ItemKeyEnum.LOCK) {
      let worldPos = e.getUIStartLocation();
      let size: math.Rect = this.getNode("ScrollView").getComponent(UITransform).getBoundingBoxToWorld();
      if (!size.contains(worldPos)) {
        return;
      }

      let lackItem = PlayerModule.service.checkitemEnought(configBaseSoul.costPlaceList);
      if (lackItem.length > 0) {
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
          itemId: lackItem[0],
          needNum: lackItem[1],
        });
        return true;
      }

      let msg: ConfirmMsg = {
        msg: `确认增加一个槽位？`,
        itemList: configBaseSoul.costPlaceList,
        stopHintOption: false,
      };

      UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
        if (resp?.ok) {
          SoulModule.api.unLockSlot(() => {});
        }
      });
    } else if (e.target[ItemKey] == ItemKeyEnum.EMPTY) {
      // 空置状态
    } else if (e.target[ItemKey] > 0) {
      this.onSelect(e.target[ItemKey]);
    }
  }

  // 选中状态
  private onSelect(soulId: number) {
    // 没有灵兽就关闭这个页面
    if (!soulId) {
      return;
    }

    this._currentSoulId = soulId;

    // 当前选中信息
    let soulMsg = SoulModule.data.warriorSoulManageMsg.soulMap[this._currentSoulId];

    // 统一配置数据
    const configSoul = SoulModule.data.getConfigSoul(soulMsg.soulTemplateId);

    // 当前选中状态
    const nodeContent = this.getNode("node_content");
    let idList = SoulModule.data.getSoulIdList();
    for (let i = 0; i < idList.length; i++) {
      let nodeItem = nodeContent.children[i];
      nodeItem.getChildByName("ani_paomadeng").active = this._currentSoulId == idList[i];
    }

    // 高级粒子效果
    this.getNode("ani_color_4103x").active = configSoul.color >= 4;

    // 当前名称
    this.getNode("lbl_soul_name").getComponent(Label).string = configSoul.name;

    // 当前等级
    this.getNode("lbl_soul_level").getComponent(Label).string = `LV.${soulMsg.grade + soulMsg.stage * 5}`;

    // 基础属性
    const layoutBaseAttr = this.getNode("layout_base_attr");
    let baseAttrIdList = [AttrEnum.攻击_2, AttrEnum.生命_1, AttrEnum.防御_3];
    for (let i = 0; i < baseAttrIdList.length; i++) {
      layoutBaseAttr.children[i].getComponent(Label).string = Formate.formatAttribute(
        baseAttrIdList[i],
        soulMsg.attrMap[baseAttrIdList[i]]
      );
    }

    // 战斗属性
    const layoutAttr = this.getNode("layout_attr");
    layoutAttr.children.forEach((child) => (child.active = false));

    // 副属性条数
    let addCount = 0;
    // 找到最属性最大值
    let attrMain = [];
    // 副属性
    let attrSubIdList = [];
    Object.keys(soulMsg.attrMap).forEach((idx) => {
      let k = Number(idx);

      // 排除基础属性
      if (baseAttrIdList.indexOf(k) >= 0) {
        return;
      }
      attrSubIdList.push(k);

      //取最大值为主属性
      if (attrMain.length == 0 || attrMain[1] < soulMsg.attrMap[k]) {
        attrMain = [k, soulMsg.attrMap[k]];
      }
    });
    attrSubIdList = attrSubIdList.filter((e) => e != attrMain[0]);

    this.getNode("lbl_main_attr").getComponent(Label).string = Formate.formatAttribute(attrMain[0], attrMain[1]);

    for (let i = 0; i < attrSubIdList.length; i++) {
      let k = attrSubIdList[i];

      let nodeItem = layoutAttr.children[addCount++];
      if (!nodeItem) {
        nodeItem = instantiate(layoutAttr.children[0]);
        layoutAttr.addChild(nodeItem);
      }
      nodeItem.active = true;

      nodeItem.getComponentInChildren(Label).string = Formate.formatAttribute(k, soulMsg.attrMap[k]);
    }

    // 适配宽度
    if (addCount <= 3) {
      // layoutAttr.getComponent(UITransform).setContentSize(165 * addCount, 100);
      layoutAttr.children.forEach((child) => {
        child.getComponent(UITransform).setContentSize(660 / addCount, 50);
      });
    } else {
      // layoutAttr.getComponent(UITransform).setContentSize(660, 100);
      layoutAttr.children.forEach((child) => {
        child.getComponent(UITransform).setContentSize(660 / 4, 50);
      });
    }

    // // 设置icon
    // ResMgr.loadImage(
    //   `${BundleEnum.BUNDLE_G_SOUL}?images/wh_${soulMsg.soulTemplateId}`,
    //   this.getNode("bg_wuhun_icon").getComponent(Sprite),
    //   this
    // );
    this.getNode("bg_wuhun_icon").active = true;
    let key = "wh_" + soulMsg.soulTemplateId;
    this.getNode("bg_wuhun_icon").getComponent(sp.Skeleton).setAnimation(0, key, true);

    // 满阶状态
    let isLvMax = soulMsg.stage >= configSoul.max;
    this.getNode("bg_finish").active = isLvMax;
    this.getNode("btn_upgrade").active = !isLvMax;
    let nodeProgressLevel = this.getNode("node_progress_level");

    // 未满阶状态
    if (!isLvMax) {
      for (let i = 0; i < 5; i++) {
        nodeProgressLevel.children[i].children[0].active = i < soulMsg.grade;
      }

      // 升级按钮
      this.getNode("btn_upgrade").getChildByName("node_level_up").active = soulMsg.grade != 5;
      // 升阶按钮
      this.getNode("btn_upgrade").getChildByName("node_stage_up").active = soulMsg.grade == 5;

      // 等级
      this.getNode("soul_level_lab").getComponent(Label).string = `${soulMsg.stage}阶${soulMsg.grade}级`;

      // 消耗
      let itemNum = PlayerModule.data.getItemNum(configSoul.costId);
      let cost = configSoul.costList[soulMsg.stage];
      if (!cost) {
        cost = configSoul.costList[configSoul.costList.length - 1];
      }

      const lblItemNum = this.getNode("lbl_item_num").getComponent(Label);
      lblItemNum.string = `${Formate.format(itemNum)}/${Formate.format(cost)}`;
      if (itemNum < cost) {
        lblItemNum.color = FmColor.COLOR_RED_LIGHT;
      } else {
        lblItemNum.color = FmColor.COLOR_GREEN_LIGHT;
      }
    } else {
      for (let i = 0; i < 5; i++) {
        nodeProgressLevel.children[i].children[0].active = true;
      }
      this.getNode("soul_level_lab").getComponent(Label).string = `${soulMsg.stage}阶`;
    }

    // 上阵状态
    this.getNode("btn_go_work").getComponent(Sprite).grayscale = soulMsg.chosen;
  }

  /** 武魂升级 */
  private on_click_btn_upgrade() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    const soulMsg = SoulModule.data.warriorSoulManageMsg.soulMap[this._currentSoulId];

    const configSoul = SoulModule.data.getConfigSoul(soulMsg.soulTemplateId);

    let itemNum = PlayerModule.data.getItemNum(configSoul.costId);

    let cost = configSoul.costList[soulMsg.stage];
    if (!cost) {
      cost = configSoul.costList[configSoul.costList.length - 1];
    }

    if (itemNum < cost) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: configSoul.costId,
        needNum: cost,
      });
      return;
    }

    let nodeAniLevelUp = this.getNode("ani_level_up");
    let nodeAniProgress = this.getNode("ani_progress");
    nodeAniLevelUp.active = true;
    nodeAniProgress.active = true;
    let aniLevelUp = nodeAniLevelUp.getComponent(sp.Skeleton);
    let aniProgress = nodeAniProgress.getComponent(sp.Skeleton);
    aniLevelUp.setCompleteListener(() => {
      nodeAniLevelUp.active = false;
    });
    aniProgress.setCompleteListener(() => {
      nodeAniProgress.active = false;
    });
    nodeAniLevelUp.getComponent(sp.Skeleton).setAnimation(0, "action", false);
    nodeAniProgress.getComponent(sp.Skeleton).setAnimation(0, "animation", false);

    let key = "";
    if (this.getNode("btn_upgrade").getChildByName("node_level_up").activeInHierarchy == true) {
      key = "node_level_up";
    } else if (this.getNode("btn_upgrade").getChildByName("node_stage_up").activeInHierarchy == true) {
      key = "node_stage_up";
    }

    SoulModule.api.upgrade(soulMsg.id, (msg: WarriorSoulMessage) => {
      if (key == "node_level_up") {
        AudioMgr.instance.playEffect(SoulAudioName.Effect.升级成功);
      } else if (key == "node_stage_up") {
        AudioMgr.instance.playEffect(SoulAudioName.Effect.进阶成功);
      }
    });
  }

  /** 武魂上阵 */
  private on_click_btn_go_work() {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击上阵按钮);
    let soulMsg = SoulModule.data.warriorSoulManageMsg.soulMap[this._currentSoulId];
    if (soulMsg.chosen) {
      return;
    }
    SoulModule.api.goWork(soulMsg.id, () => {
      TipMgr.showTip(`上阵成功`);
      this.refresh();
    });
  }

  /** 武魂放生 */
  private on_click_btn_fangsheng() {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击放生按钮);
    let soulMsg = SoulModule.data.warriorSoulManageMsg.soulMap[this._currentSoulId];
    if (soulMsg.chosen) {
      TipMgr.showTip(`武魂上阵中，无法放生`);
      return;
    }

    UIMgr.instance.showDialog(SoulRouteName.UISoulFreePop, { soulId: this._currentSoulId }, () => {
      this.refresh();
    });
  }

  /** 跳转到获取武魂 */
  private on_click_btn_get_soul() {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击获取按钮);
    UIMgr.instance.showDialog(SoulRouteName.UISoulList);
  }

  private on_click_btn_title() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { cfgId: 3 });
  }
  private on_click_tab_soul_unselect() {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击页签);
    this.getNode("dialog_soul").active = true;
    this.getNode("dialog_tujian").active = false;
    this.getNode("tab_soul_select").active = true;
    this.getNode("tab_tujian_select").active = false;
    this.getNode("tab_tunshi_select").active = false;
  }
  private on_click_tab_tujian_unselect() {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击页签);
    this.getNode("dialog_soul").active = false;
    this.getNode("dialog_tujian").active = true;
    this.getNode("tab_soul_select").active = false;
    this.getNode("tab_tujian_select").active = true;
    this.getNode("tab_tunshi_select").active = false;
  }
  private on_click_tab_tunshi_unselect() {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击页签);
    TipsMgr.showTipX(522, [], "功能未解锁");
    // this.getNode("dialog_soul").active = false;
    // this.getNode("dialog_tujian").active = false;
    // this.getNode("tab_soul_select").active = false;
    // this.getNode("tab_tujian_select").active = false;
    // this.getNode("tab_tunshi_select").active = true;
  }
}
