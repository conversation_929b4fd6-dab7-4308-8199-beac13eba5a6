import {
  _decorator,
  EventTouch,
  Graphics,
  Input,
  instantiate,
  Label,
  Mask,
  Node,
  Prefab,
  ScrollView,
  Sprite,
  UIOpacity,
  UITransform,
  v2,
} from "cc";
import { avId1, DayActivityModule } from "db://assets/GameScrpit/module/day/DayActivityModule";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { RedeemMessage, RedeemRequest, RedeemResponse } from "../../../net/protocol/Activity";
import { Sleep } from "../../../GameDefine";

import ToolExt from "../../../common/ToolExt";
import { BadgeMgr, BadgeType } from "../../../mgr/BadgeMgr";
import { DayMain } from "./DayMain";
import MsgEnum from "../../../event/MsgEnum";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import GameHttpApi from "../../../httpNet/GameHttpApi";
import { GoodsRouteName } from "db://assets/GameScrpit/module/goods/GoodsRoute";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { RedeemPackVO } from "db://assets/GameScrpit/module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const db_info: string = "db_info";
@ccclass("DayMainType1")
export class DayMainType1 extends DayMain {
  @property(Node)
  private award_content1: Node = null;

  @property(Node)
  private pengz: Node = null;

  private _node_day_item_type1: Prefab = null;
  private _activityDb: Array<RedeemPackVO> = null;

  private _redeemMessage: RedeemMessage = null;

  private _tickIndex: number = 0;

  protected onLoad() {
    super.onLoad();
    MsgMgr.on(MsgEnum.ON_ACTIVITY_DAILY_GIFT_BUT_UP, this.upRedeeMap, this);
    this._redeemMessage = DayActivityModule.data.dayMessage;
  }

  async start() {
    let db = await DayActivityModule.data.getDayVO();
    this._activityDb = db[1];
    this._node_day_item_type1 = await this._assetMgr.loadPrefabSync(
      BundleEnum.BUNDLE_HD_FUND,
      "prefab/day_recharge/node_day_item_type1"
    );
    if (this.isValid == false) {
      return;
    }
    this.initMain();
  }

  private initMain() {
    this.load_day_item_type1();
  }

  private sortDb() {
    let list1 = [];
    let list2 = [];
    for (let i = 0; i < this._activityDb.length; i++) {
      let info = this._activityDb[i];
      let redem_num = this._redeemMessage.redeemMap[info.id] || 0;
      let show_bool = redem_num >= info.max ? true : false; //true 是代表已经售空
      if (show_bool == true) {
        list2.push(info);
      } else {
        list1.push(info);
      }
    }
    this._activityDb = list1.concat(list2);
  }

  private async load_day_item_type1() {
    this.sortDb();

    for (let i = 0; i < this._activityDb.length; i++) {
      await Sleep(0.01);
      if (this.isValid == false) {
        return;
      }

      let node = instantiate(this._node_day_item_type1);
      node.walk((child) => (child.layer = this.node.layer));
      this.award_content1.addChild(node);

      let btn_goumai: Node = node.getChildByPath("btn_goumai");
      btn_goumai.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.on_click_btn_goumai.bind(this)), this);

      let info = this._activityDb[i];
      this.set_day_item_type1(node, info);
      this.load_item_type1(node, info.rewardList);

      node.name = info.name;

      if (info.price == 0 && info.adNum == 0 && !info.cost) {
        BadgeMgr.instance.setBadgeId(
          node.getChildByName("btn_goumai"),
          BadgeType.UITerritory.btn_chongzhihaoli.FundBanner001.btn_type1["MLGB" + info.id].id
        );
      }
    }
  }

  private set_day_item_type1(day_item: Node, info: RedeemPackVO) {
    let lbl_day_item_name: Node = day_item.getChildByPath("lbl_day_item_name");
    let btn_goumai: Node = day_item.getChildByPath("btn_goumai");
    let lbl_price: Node = btn_goumai.getChildByPath("lbl_price");
    let yishouqin: Node = day_item.getChildByPath("yishouqin");
    let lbl_buy_max: Node = day_item.getChildByPath("lbl_buy_max");

    lbl_day_item_name.getComponent(Label).string = info.name;
    /**代表是免费礼包 ， 不要钱免费送 */
    if (info.adNum == 0 && info.price == 0) {
      lbl_price.getComponent(Label).string = "免费";
    } else if (info.price > 0) {
      /**收费礼包，要人民币 */
      lbl_price.getComponent(Label).string = (info.price % 10000) + "元";
    }

    //let is_ad = info.adNum > 0 ? true : false;
    // /**是广告礼包，判断广告次数情况 */
    // if (is_ad == true) {
    // }

    /**次数判断，是否显示购买按钮售空状态 */

    let redem_num = this._redeemMessage.redeemMap[info.id] || 0;
    let show_bool = redem_num >= info.max ? true : false; //true 是代表已经售空
    yishouqin.active = show_bool;
    btn_goumai.active = !show_bool;
    lbl_buy_max.active = !show_bool;

    if (show_bool == false) {
      let count = info.max - redem_num;
      lbl_buy_max.getComponent(Label).string = ToolExt.getMaxtypeLab(info.maxtype) + `(${count}/${info.max})`;
    }

    btn_goumai[db_info] = info;
  }

  private async load_item_type1(day_item: Node, list: number[]) {
    let rewardList = ToolExt.traAwardItemMapList(list);
    let item_content: Node = day_item.getChildByPath("ScrollView/view/item_content");
    let path = `${BundleEnum.BUNDLE_COMMON_UI}?prefabs/Item`;
    for (let i = 0; i < rewardList.length; i++) {
      let prefab = await this._assetMgr.loadPrefabSync(BundleEnum.BUNDLE_COMMON_UI, "prefabs/Item");
      if (this.isValid == false) {
        return;
      }
      let node = instantiate(prefab); //await this._plantUI.getNode(path, this.node.layer);
      node.getChildByName("Mask").getComponent(Mask).enabled = false;
      node.getChildByName("Mask").getComponent(Graphics).enabled = false;
      item_content.addChild(node);
      FmUtils.setItemNode(node, rewardList[i].id, rewardList[i].num);
    }
    if (rewardList.length <= 3) {
      day_item.getChildByName("ScrollView").getComponent(ScrollView).enabled = false;
    }
  }

  private on_click_btn_goumai(event: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let node: Node = event.target;
    let info: RedeemPackVO = node[db_info];
    if (info.price == 0) {
      let param: RedeemRequest = {
        activityId: avId1,
        redeemId: info.id,
        count: 1,
      };

      DayActivityModule.api.buyFixedPack(param, (res: RedeemResponse) => {
        let rewardList = res.rewardList;
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
        this._redeemMessage = DayActivityModule.data.dayMessage;
      });
      return;
    }

    let goodsId = info.id;
    let goodsType = 8;
    let playerId = PlayerModule.data.playerId;
    let orderAmount = info.price % 10000;
    let goodsName = info.name;
    let platformType = "TEST";
    this.onPay(goodsId, goodsType, playerId, orderAmount, goodsName, platformType);
  }

  private onPay(
    goodsId: number,
    goodsType: number,
    playerId: number,
    orderAmount: number,
    goodsName: string,
    platformType: string
  ) {
    let data = {
      goodsId: goodsId,
      goodsType: goodsType,
      playerId: playerId,
      orderAmount: orderAmount,
      goodsName: goodsName,
      platformType: platformType,
    };

    GameHttpApi.pay(data).then((resp: any) => {
      // window.open(resp.data.url);
      if (resp.code != 200) {
        let err = JSON.parse(resp.msg);
        log.log(err);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }

  update(deltaTime: number) {
    let pengz = this.pengz;
    let content_list = this.award_content1;
    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();
    const index = this._tickIndex;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();
      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });

        if (content_list.children[i].getChildByPath("ScrollView/view/item_content").children.length == 0) {
          content_list.children[i].getChildByPath("bug_tip").active = true;
        } else {
          content_list.children[i].getChildByPath("bug_tip").active = false;
        }
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
      }
      this._tickIndex = i;
    }
    if (this._tickIndex >= content_list.children.length - 1) {
      this._tickIndex = 0;
    }
  }

  private upRedeeMap() {
    this._redeemMessage = DayActivityModule.data.dayMessage;
    this.award_content1.children.forEach((val) => {
      let info = val.getChildByName("btn_goumai")[db_info];
      let redem_num = this._redeemMessage.redeemMap[info.id] || 0;
      let show_bool = redem_num >= info.max ? true : false; //true 是代表已经售空
      if (show_bool == true) {
        val.setSiblingIndex(this.award_content1.children.length);
      }
      this.set_day_item_type1(val, info);
    });

    let v = this.node.getChildByName("award_scroll").getComponent(ScrollView).getScrollOffset();
    this.node
      .getChildByName("award_scroll")
      .getComponent(ScrollView)
      .scrollToOffset(v2(v.x, v.y + 1), 0.1);
  }

  onRemove() {
    super.onRemove();
    MsgMgr.off(MsgEnum.ON_ACTIVITY_DAILY_GIFT_BUT_UP, this.upRedeeMap, this);
  }
}
