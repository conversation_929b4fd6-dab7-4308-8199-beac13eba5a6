import { AudioName } from "db://assets/platform/src/AudioHelper";
import { GameData } from "../../../game/GameData";
import { UIFightLose } from "../../../game/ui/ui_fight/UIFightLose";
import { UIFightPage } from "../../../game/ui/ui_fight/UIFightPage";
import { UIFightWin } from "../../../game/ui/ui_fight/UIFightWin";
import { UILevelBoss } from "../../../game/ui/ui_fight/UILevelBoss";
import { UILevelBossPower } from "../../../game/ui/ui_fight/UILevelBossPower";
import { UILevelBox } from "../../../game/ui/ui_fight/UILevelBox";
import { UILevelDroupLook } from "../../../game/ui/ui_fight/UILevelDroupLook";
import { UILevelGame } from "../../../game/ui/ui_fight/UILevelGame";
import { UIGameMap } from "../../../game/ui/ui_gameMap/UIGameMap";

import data from "../../../lib/data/data";
import { Recording, RecordingMap } from "../../../lib/ui/recordingMap";
import { FightApi } from "./FightApi";
import { FightData } from "./FightData";
import { fightService } from "./FightService";

export enum FightRouteItem {
  UILevelGame = "UILevelGame",
  UILevelDroupLook = "UILevelDroupLook",
  UILevelBox = "UILevelBox",
  UIFightPage = "UIFightPage",
  UIFightLose = "UIFightLose",
  UIFightWin = "UIFightWin",
  UILevelBoss = "UILevelBoss",
  UILevelBossPower = "UILevelBossPower",
}

export class FightModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): FightModule {
    if (!GameData.instance.FightModule) {
      GameData.instance.FightModule = new FightModule();
      GameData.instance.FightModule.onViewLoad();
    }
    return GameData.instance.FightModule;
  }
  private _data = new FightData();
  private _api = new FightApi();
  private _service = new fightService();

  public nameMap = null;
  public fightData = null;
  public posMap = null;
  public goRoleNodeMap = null;

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
  public init(data?: any, completedCallback?: Function) {
    // 初始化模块
    this._data = new FightData();
    this._api = new FightApi();

    // 初始化数据
    FightModule.api.getChapter((data) => {
      completedCallback && completedCallback();
    });
  }
  protected onViewLoad(): void {
    let data = new Recording();
    data = {
      node: UILevelGame,
      uiName: FightRouteItem.UILevelGame,
      keep: false,
      relevanceUIList: [UIGameMap],
      music: AudioName.Sound.战斗,
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UILevelDroupLook,
      uiName: FightRouteItem.UILevelDroupLook,
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UILevelBox,
      uiName: FightRouteItem.UILevelBox,
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UIFightPage,
      uiName: FightRouteItem.UIFightPage,
      keep: false,
      relevanceUIList: [],
      music: AudioName.Sound.战斗,
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UIFightLose,
      uiName: FightRouteItem.UIFightLose,
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UIFightWin,
      uiName: FightRouteItem.UIFightWin,
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UILevelBoss,
      uiName: FightRouteItem.UILevelBoss,
      keep: false,
      relevanceUIList: [],
      music: AudioName.Sound.战斗,
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UILevelBossPower,
      uiName: FightRouteItem.UILevelBossPower,
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);
  }
}
