import { _decorator, EventTouch, Label, RichText, sp, tween, UIOpacity } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { FractureModule } from "../../../module/fracture/FractureModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { FractureState } from "../../../module/fracture/FractureService";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import Formate from "../../../lib/utils/Formate";
import { FightData } from "../../fight/FightDefine";
import { ItemCost } from "../../common/ItemCost";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import {
  FractureEventResponse,
  FractureFightResponse,
  FractureSkipResponse,
  FractureTrapResponse,
} from "../../net/protocol/Activity";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { MessageComponent } from "../../../../platform/src/core/ui/components/MessageComponent";
import { UIFractureAnswerSuccess } from "./UIFractureAnswerSuccess";
import { LangMgr } from "../../mgr/LangMgr";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";
import {
  FRACTURE_ACTIVITYID,
  FractureActivityConfig,
  FractureFloorMap,
} from "../../../module/fracture/FractureConstant";
import { FractureDrawAni } from "./FractureDrawAni";
import { Net_Code } from "../../mgr/ApiHandler";
import { ActivityModule } from "../../../module/activity/ActivityModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { persistentWith } from "../../../lib/decorators/persistent";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { FractureFightAwardPreviewAdapter } from "./adapter/FractureFightAwardPreviewViewHolder";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { UIFractureDrawPreview } from "./UIFractureDrawPreview";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { RouteNode, RouteShowArgs, routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIFractureGift } from "./UIFractureGift";
import { UIFractureRank } from "./UIFractureRank";
import { UIFractureShop } from "./UIFractureShop";
import { UIFractureLog } from "./UIFractureLog";
import { UIFractureTipAttr } from "./UIFractureTipAttr";
import { UIFractureSkipTips } from "./UIFractureSkipTips";
import { UIFractureFight } from "./UIFractureFight";
import { UIFractureFightSuccess } from "./UIFractureFightSuccess";
import { UIFractureFightFaild } from "./UIFractureFightFaild";
import { UIFractureUnlockSuccess } from "./UIFractureUnlockSuccess";
import FmUtils from "../../../lib/utils/FmUtils";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);

enum UIState {
  IDLE,
  BUSY,
}
const nextHops: RouteNode[] = [
  {
    via: "btn_libao",
    des: UIFractureGift,
  },
  {
    via: "btn_paihang",
    des: UIFractureRank,
  },
  {
    via: "btn_duihuan",
    des: UIFractureShop,
  },
  {
    via: "btn_log",
    des: UIFractureLog,
  },
  {
    via: "btn_baoxiang_jianliyulan",
    des: UIFractureDrawPreview,
    desPageTag: "baoxiang",
  },
  {
    via: "btn_jianliyulan",
    des: UIFractureDrawPreview,
    desPageTag: "draw",
  },
];
@ccclass("UIFractureSearch")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureSearch",
  nextHop: nextHops,
  exit: "dialog_close",
})
export class UIFractureSearch extends BaseCtrl {
  public playShowAni: boolean = true;
  private _uiState: UIState = UIState.IDLE;

  @persistentWith(false)
  private _skip_monster_fight: boolean;
  @persistentWith(false)
  private _skip_npc_fight: boolean;

  start() {
    super.start();
    this.updateState();
    MsgMgr.on(MsgEnum.ON_CLOSE_GET_AWARD, this.onCloseAwardMsg, this);
    MsgMgr.on(MsgEnum.ON_FRACTURE_FLOOR_UNLOCK, this.onUnLockFloorMsg, this);
    MsgMgr.on(MsgEnum.ON_FRACTURE_UPDATE, this.onFractureUpdate, this);
  }

  update(deltaTime: number) {}

  protected onDestroy(): void {
    super.onDestroy();
    MsgMgr.off(MsgEnum.ON_CLOSE_GET_AWARD, this.onCloseAwardMsg, this);
    MsgMgr.off(MsgEnum.ON_FRACTURE_FLOOR_UNLOCK, this.onUnLockFloorMsg, this);
    MsgMgr.off(MsgEnum.ON_FRACTURE_UPDATE, this.onFractureUpdate, this);
  }
  private updateState() {
    let state = FractureModule.service.getFractureState();
    this.getNode("node_search").active = false;
    this.getNode("node_barrier").active = false;
    this.getNode("node_old_man").active = false;
    this.getNode("node_draw").active = false;
    this.getNode("node_monster").active = false;
    this.getNode("node_npc_player").active = false;
    this.getNode("node_baoxiang").active = false;

    // 如果不在活动时间内
    if (!this.checkActivityValid()) {
      state = FractureState.SEARCH;
    }

    // 剩余次数提示 ---START

    let remainCount =
      FractureModule.data.fractureData.remainFloorCountMap[FractureModule.data.fractureData.floorId] + "";
    let maxCount = FractureModule.service.getCurrentSearchCount() + "";
    this.getNode("lbl_tips").getComponent(MessageComponent).args = [remainCount, maxCount];
    // 剩余次数提示 ---END

    // 背景切换 ---START
    let difficulty = FractureModule.service.getSelectedDifficulty();
    let floorList: number[] = FractureFloorMap[difficulty];
    let floorIndex = floorList.findIndex((val) => {
      return val == FractureModule.data.fractureData.floorId;
    });
    if (floorIndex == 0) {
      this.getNode("bg_floor1").active = true;
      this.getNode("bg_floor2").active = false;
    } else {
      this.getNode("bg_floor1").active = false;
      this.getNode("bg_floor2").active = true;
    }
    // 背景切换 ---END

    switch (state) {
      case FractureState.SEARCH:
        this.getNode("node_search").active = true;
        this.updateSearch(); // 调用搜索状态的更新逻辑
        break;
      case FractureState.BARRIER:
        this.getNode("node_barrier").active = true;
        this.updateBarrier(); // 调用障碍物状态的更新逻辑
        break;
      case FractureState.OLD_MAN:
        this.getNode("node_old_man").active = true;
        this.updateOldMan(); // 调用时光老人状态的更新逻辑
        break;
      case FractureState.DRAW:
        this.getNode("node_draw").active = true;
        this.updateDraw(); // 调用抽奖状态的更新逻辑
        break;
      case FractureState.MONSTER:
        this.getNode("node_monster").active = true;
        this.updateMonster(); // 调用MONSTER状态的更新逻辑
        break;
      case FractureState.PLAYER:
        this.getNode("node_npc_player").active = true;
        this.updatePlayer(); // 调用玩家状态的更新逻辑
        break;
      case FractureState.BAOXIANG:
        this.getNode("node_baoxiang").active = true;
        this.updateBaoxiang(); // 调用宝箱状态的更新逻辑
        break;
      default:
        break;
    }
  }

  /**
   * 更新搜索状态
   * @returns
   */
  private updateSearch() {
    // 切换按钮的显示状态---START
    let difficulty = FractureModule.service.getSelectedDifficulty();
    let isEnable1 = FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[difficulty][0]] >= 0;
    let isEnable2 = FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[difficulty][1]] >= 0;
    this.getNode("btn_tab1").getComponent(FmButton).btnEnable = isEnable1;
    this.getNode("btn_tab2").getComponent(FmButton).btnEnable = isEnable2;
    this.getNode("btn_tab1").getComponent(FmButton).selected = false;
    this.getNode("btn_tab2").getComponent(FmButton).selected = false;
    let floorList: number[] = FractureFloorMap[difficulty];
    let floorIndex = floorList.findIndex((val) => {
      return val == FractureModule.data.fractureData.floorId;
    });
    this.getNode(`btn_tab${floorIndex + 1}`).getComponent(FmButton).selected = true;
    this.getNode("lbl_tips").active = floorIndex === 0;
    // 切换按钮的显示状态---END

    // 难度按钮显示状态---START
    this.getNode("btn_level1").getComponent(FmButton).btnEnable = true;
    this.getNode("btn_level2").getComponent(FmButton).btnEnable = FractureModule.service.getUnlockedDifficulty() >= 2;
    this.getNode("btn_level3").getComponent(FmButton).btnEnable = FractureModule.service.getUnlockedDifficulty() >= 3;
    this.getNode("btn_level1").getComponent(FmButton).selected = FractureModule.service.getSelectedDifficulty() == 1;
    this.getNode("btn_level2").getComponent(FmButton).selected = FractureModule.service.getSelectedDifficulty() == 2;
    this.getNode("btn_level3").getComponent(FmButton).selected = FractureModule.service.getSelectedDifficulty() == 3;
    // 难度按钮显示状态---END

    // 显示活动时间---START
    let activity = ActivityModule.data.allActivityConfig[FRACTURE_ACTIVITYID] as FractureActivityConfig;
    this.getNode("lbl_date").getComponent(Label).string = `${LangMgr.txMsgCode(271)}${TimeUtils.formatTimestamp(
      activity.startTime,
      "MM-DD HH:mm"
    )}~${TimeUtils.formatTimestamp(activity.endTime, "MM-DD HH:mm")}`;
    // 显示活动时间---END

    // 显示打完boss的解锁提示---START
    this.getNode("lbl_boss_tips").active = true;
    if (floorIndex == 0) {
      if (FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[difficulty][1]] >= 0) {
        this.getNode("lbl_boss_tips").getComponent(MessageComponent).messageKey = 514;
      } else {
        this.getNode("lbl_boss_tips").getComponent(MessageComponent).messageKey = 513;
        this.getNode("lbl_boss_tips").getComponent(MessageComponent).args = ["0", "1"];
      }
    } else {
      if (difficulty == 1) {
        this.getNode("lbl_boss_tips").getComponent(MessageComponent).messageKey = 511;
        if (FractureModule.service.getUnlockedDifficulty() >= 2) {
          this.getNode("lbl_boss_tips").getComponent(MessageComponent).args = ["1", "1"];
        } else {
          this.getNode("lbl_boss_tips").getComponent(MessageComponent).args = ["0", "1"];
        }
      } else if (difficulty == 2) {
        this.getNode("lbl_boss_tips").getComponent(MessageComponent).messageKey = 512;
        if (FractureModule.service.getUnlockedDifficulty() >= 3) {
          this.getNode("lbl_boss_tips").getComponent(MessageComponent).args = ["1", "1"];
        } else {
          this.getNode("lbl_boss_tips").getComponent(MessageComponent).args = ["0", "1"];
        }
      } else {
        this.getNode("lbl_boss_tips").active = false;
      }
    }
    // 显示打完boss的解锁提示---END

    // 背景动画切换 --Start
    if (floorIndex === 1) {
      this.getNode("spine_shi_kong_bg_ani").getComponent(sp.Skeleton).setAnimation(0, "sk_bg_ani", true);
    } else {
      this.getNode("spine_shi_kong_bg_ani").getComponent(sp.Skeleton).setAnimation(0, "sk_bg_ani2", true);
    }

    let floorId = FractureModule.data.fractureData.floorId;
    let remainCount = FractureModule.data.fractureData.remainFloorCountMap[floorId] ?? 0;
    if (remainCount <= 0) {
      this.getNode("node_finish").active = true;
      this.getNode("btn_search").active = false;
      this.getNode("item_search_cost").active = false;
      return;
    } else {
      this.getNode("node_finish").active = false;
      this.getNode("btn_search").active = true;
      this.getNode("item_search_cost").active = true;
    }
    let costItem = FractureModule.config.getSearchItemCost(floorId);
    if (costItem.length == 0) {
      this.getNode("item_search_cost").active = false;
      return;
    }
    this.getNode("item_search_cost").getComponent(ItemCost).setItemId(costItem[0], costItem[1]);
  }

  /**
   * 更新路障状态
   */
  private updateBarrier() {
    this.getNode("node_barrier_img").getComponent(UIOpacity).opacity = 255;
    if (FractureModule.data.fractureData.roadMessage.assistCount > 0) {
      this.getNode("lbl_barrier_cd").active = false;
      this.getNode("lbl_barrier_free").active = true;
      this.getNode("item_barrier_cost").active = false;
      this.getNode("node_barrier_cd").active = false;
      this.getNode("btn_club_help").active = false;
      return;
    }
    if (FractureModule.data.fractureData.roadMessage.roadDeadline > TimeUtils.serverTime) {
      this.getNode("lbl_barrier_free").active = false;
      this.getNode("item_barrier_cost").active = true;
      this.getNode("lbl_barrier_cd").active = true;
      this.getNode("node_barrier_cd").active = true;
      this.getNode("btn_club_help").active = !FractureModule.data.fractureData.roadMessage.assist;
      let event = JsonMgr.instance.jsonList.c_fracture_even[FractureModule.data.fractureData.choiceId];
      this.getNode("item_barrier_cost").getComponent(ItemCost).setItemId(event.costDo[0], event.costDo[1]);
      FmUtils.setCd(
        this.getNode("lbl_barrier_cd"),
        FractureModule.data.fractureData.roadMessage.roadDeadline,
        false,
        () => {
          this.updateState();
        }
      );
    } else {
      this.getNode("btn_club_help").active = false;
      this.getNode("node_barrier_cd").active = false;
      this.getNode("lbl_barrier_cd").active = false;
      this.getNode("lbl_barrier_free").active = true;
      this.getNode("item_barrier_cost").active = false;
    }
  }

  /**
   * 更新时光老人状态
   */
  private updateOldMan() {
    let fractureMessage = FractureModule.data.fractureData;
    let event = JsonMgr.instance.jsonList.c_fracture_even[fractureMessage.choiceId];

    let desc = LangMgr.txMsgCode(Number(event.desc));
    let answer1 = LangMgr.txMsgCode(Number(event.answer1));
    let answer2 = LangMgr.txMsgCode(Number(event.answer2));
    this.getNode("lbl_old_man_question").getComponent(Label).string = `${desc}`;
    this.getNode("lbl_old_man_answer1").getComponent(Label).string = `${answer1}`;
    this.getNode("lbl_old_man_answer2").getComponent(Label).string = `${answer2}`;
  }

  /**
   * 更新抽奖状态
   */
  private updateDraw() {
    this.getNode("node_draw").getComponent(FractureDrawAni).init();
  }

  /**
   * 更新小怪状态
   */
  private updateMonster() {
    let monsterId = FractureModule.data.fractureData.fightMessage.monsterId;
    let monster = JsonMgr.instance.jsonList.c_monster[monsterId];
    let monsterShow = JsonMgr.instance.jsonList.c_monsterShow[monster.monsterShowId];
    this.getNode("check_monster_skip").active = this._skip_monster_fight;
    this.getNode("node_monster_name").getComponent(Label).string = monsterShow.name;
    this.getNode("node_monster_image").destroyAllChildren();
    ToolExt.loadUIRole(this.getNode("node_monster_image"), monster.monsterShowId, -1, "renderScale1", this as any);
    this.getNode("lbl_monster_zhanli").getComponent(Label).string = `${Formate.format(
      ToolExt.levelBossPower(monster.attr)
    )}`;
    this.getNode("lbl_my_zhanli_monster").getComponent(Label).string = `${Formate.format(
      PlayerModule.data._playerBattleAttrResponse.power
    )}`;
    if (FractureModule.data.fractureData.fightMessage.fightColdTime > TimeUtils.serverTime) {
      this.getNode("lbl_monster_cd").active = true;
      FmUtils.setCd(this.getNode("lbl_monster_cd"), FractureModule.data.fractureData.fightMessage.fightColdTime);
    } else {
      this.getNode("lbl_monster_cd").active = false;
    }
    if (FractureModule.data.fractureData.fightMessage.fightCount > 0) {
      this.getNode("item_monster_cost").active = true;
      this.getNode("item_monster_cost").getComponent(ItemCost).setItemId(20022, 1);
    } else {
      this.getNode("item_monster_cost").active = false;
    }
    let fracture = JsonMgr.instance.jsonList.c_fracture[FractureModule.data.fractureData.fractureId];

    let adapter = new FractureFightAwardPreviewAdapter(this.getNode("preview_viewholder"));
    this.getNode("node_monster_preview_list").getComponent(AdapterView).setAdapter(adapter);
    adapter.setData(fracture.reward02List);
  }

  // 玩家状态的更新逻辑
  private updatePlayer() {
    let playerMessage = FractureModule.data.fractureData.fightMessage.npcMessage;
    this.getNode("check_player_skip").active = this._skip_npc_fight;
    this.getNode("lbl_player_server").getComponent(Label).string = `${playerMessage.serverName}`;
    this.getNode("node_player_name").getComponent(Label).string = `${playerMessage.nickname}`;
    this.getNode("lbl_player_zhanli").getComponent(Label).string = `${Formate.format(playerMessage.power)}`;

    let roleSkinId = playerMessage.avatarList[0];
    let horseId = playerMessage.horseId;
    this.getNode("node_player_image").destroyAllChildren();
    ToolExt.loadUIRole(this.getNode("node_player_image"), roleSkinId, horseId, "renderScale16", this as any);
    FmUtils.setHeaderNode(this.getNode("btn_npc_header"), playerMessage);
    let configLeader = PlayerModule.data.getConfigLeaderData(playerMessage.level);
    this.getNode("rich_player_level").getComponent(RichText).string = `${configLeader.jingjie2}`;
    this.getNode("lbl_my_zhanli_npc").getComponent(Label).string = `${Formate.format(
      PlayerModule.data._playerBattleAttrResponse.power
    )}`;
    if (FractureModule.data.fractureData.fightMessage.fightColdTime > TimeUtils.serverTime) {
      this.getNode("lbl_player_cd").active = true;
      FmUtils.setCd(this.getNode("lbl_player_cd"), FractureModule.data.fractureData.fightMessage.fightColdTime);
    } else {
      this.getNode("lbl_player_cd").active = false;
    }
    let fracture = JsonMgr.instance.jsonList.c_fracture[FractureModule.data.fractureData.fractureId];
    if (FractureModule.data.fractureData.fightMessage.fightCount > 0) {
      this.getNode("item_player_cost").active = true;
      this.getNode("item_player_cost").getComponent(ItemCost).setItemId(fracture.cost02[0], fracture.cost02[1]);
    } else {
      this.getNode("item_player_cost").active = false;
    }

    let adapter = new FractureFightAwardPreviewAdapter(this.getNode("preview_viewholder"));
    this.getNode("node_player_preview_list").getComponent(AdapterView).setAdapter(adapter);
    adapter.setData(fracture.reward02List);
  }

  /**
   * 更新宝箱状态
   */
  private updateBaoxiang() {
    this.getNode("spine_baoxiang").getComponent(sp.Skeleton).setAnimation(0, "baoxiang", false);
  }

  private onFractureUpdate() {
    this.updateState();
  }
  /**
   * 搜索
   */
  private on_click_btn_search() {
    AudioMgr.instance.playEffect(1764);
    if (!this.checkActivityValid()) {
      return;
    }
    if (FractureModule.service.getFractureState() != FractureState.SEARCH) {
      log.log("不在搜索状态");
      return;
    }
    if (this._uiState == UIState.BUSY) {
      return;
    }
    let isEnough = this.getNode("item_search_cost").getComponent(ItemCost).isEnough();
    if (this.getNode("item_search_cost").active && !isEnough) {
      RouteManager.uiRouteCtrl.showRoute(UIFractureGift);
      return;
    }
    this._uiState = UIState.BUSY;
    FractureModule.api.searchFracture(
      (data) => {
        log.log("搜索结果", data);
        this.getNode("node_search_ani").active = true;

        tween(this.node)
          .delay(1.5)
          .call(() => {
            this.updateState();
          })
          .start();
        this.getNode("node_search_ani").getComponent(sp.Skeleton).setAnimation(0, "sousuo", false);
        AudioMgr.instance.playEffect(1765);
        this._uiState = UIState.IDLE;
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        this._uiState = UIState.IDLE;
        return false;
      }
    );
  }

  private on_click_btn_libao() {
    AudioMgr.instance.playEffect(1768);
    if (!this.checkActivityValid()) {
      return;
    }
    RouteManager.uiRouteCtrl.showRoute(UIFractureGift);
  }
  private on_click_btn_paihang() {
    AudioMgr.instance.playEffect(1770);
    RouteManager.uiRouteCtrl.showRoute(UIFractureRank);
  }
  private on_click_btn_duihuan() {
    AudioMgr.instance.playEffect(1772);
    RouteManager.uiRouteCtrl.showRoute(UIFractureShop);
  }
  private on_click_btn_log() {
    AudioMgr.instance.playEffect(1774);
    if (!this.checkActivityValid()) {
      return;
    }
    RouteManager.uiRouteCtrl.showRoute(UIFractureLog);
  }

  private checkActivityValid() {
    let activity = ActivityModule.data.allActivityConfig[FRACTURE_ACTIVITYID] as FractureActivityConfig;
    if (TimeUtils.serverTime > activity.publicityTime) {
      TipsMgr.showTipX(583);
      return false;
    }
    return true;
  }
  private onCloseAwardMsg() {
    this.updateState();
  }
  private onUnLockFloorMsg(floorId: number[]) {
    this.updateState();
    // 如果是第二层解锁，弹出提示
    let isSecondFloor = false;
    for (let i = 0; i < floorId.length; i++) {
      if (floorId[i] % 2 == 0) {
        isSecondFloor = true;
        break;
      }
    }
    if (isSecondFloor) {
      RouteManager.uiRouteCtrl.showRoute(UIFractureUnlockSuccess);
    }
  }
  private on_click_btn_switch_floor() {
    AudioMgr.instance.playEffect(1763);
    if (FractureModule.service.getFractureState() != FractureState.SEARCH) {
      log.log("不在搜索状态");
      return;
    }
    if (this._uiState == UIState.BUSY) {
      return;
    }
    //
    let difficulty = FractureModule.service.getSelectedDifficulty();
    let floorId = FractureModule.data.fractureData.floorId;
    let floorList: number[] = FractureFloorMap[difficulty];
    let index = floorList.findIndex((val) => {
      return val == floorId;
    });
    index = (index + 1) % floorList.length;
    if (!(FractureModule.data.fractureData.remainFloorCountMap[floorList[index]] >= 0)) {
      //
      TipsMgr.showTip(LangMgr.txMsgCode(513, ["0", "1"]));
      return;
    }
    this._uiState = UIState.BUSY;
    FractureModule.api.travelFloor(
      floorList[index],
      (data) => {
        this.updateState();
        this._uiState = UIState.IDLE;
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        this._uiState = UIState.IDLE;
        return false;
      }
    );
  }
  private on_click_btn_level1(e: EventTouch) {
    //
    AudioMgr.instance.playEffect(1762);
    if (FractureModule.service.getFractureState() != FractureState.SEARCH) {
      log.log("不在搜索状态");
      return;
    }
    if (this._uiState == UIState.BUSY) {
      return;
    }
    if (e.target.getComponent(FmButton).btnEnable == false) {
      TipsMgr.showTip("未解锁");
      return;
    }
    if (e.target.getComponent(FmButton).selected) return;
    let floorId = FractureFloorMap[1][0];

    // 当楼层1已经探索完成，并且楼层2已经解锁
    if (
      FractureModule.data.fractureData.remainFloorCountMap[floorId] == 0 &&
      FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[1][1]]
    ) {
      floorId = FractureFloorMap[1][1];
    }
    this._uiState = UIState.BUSY;
    FractureModule.api.travelFloor(
      floorId,
      (data) => {
        this.updateState();
        this._uiState = UIState.IDLE;
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        this._uiState = UIState.IDLE;
        return false;
      }
    );
  }
  private on_click_btn_level2(e: EventTouch) {
    //
    AudioMgr.instance.playEffect(1762);
    if (FractureModule.service.getFractureState() != FractureState.SEARCH) {
      log.log("不在搜索状态");
      return;
    }
    if (this._uiState == UIState.BUSY) {
      return;
    }
    if (e.target.getComponent(FmButton).btnEnable == false) {
      TipsMgr.showTip(LangMgr.txMsgCode(519));
      return;
    }
    if (e.target.getComponent(FmButton).selected) return;
    let floorId = FractureFloorMap[2][0];
    // 当楼层1已经探索完成，并且楼层2已经解锁
    if (
      FractureModule.data.fractureData.remainFloorCountMap[floorId] == 0 &&
      FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[2][1]]
    ) {
      floorId = FractureFloorMap[2][1];
    }
    this._uiState = UIState.BUSY;
    FractureModule.api.travelFloor(
      floorId,
      (data) => {
        this.updateState();
        this._uiState = UIState.IDLE;
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        this._uiState = UIState.IDLE;
        return false;
      }
    );
  }
  private on_click_btn_level3(e: EventTouch) {
    //
    AudioMgr.instance.playEffect(1762);
    if (FractureModule.service.getFractureState() != FractureState.SEARCH) {
      log.log("不在搜索状态");
      return;
    }
    if (this._uiState == UIState.BUSY) {
      return;
    }
    if (e.target.getComponent(FmButton).btnEnable == false) {
      TipsMgr.showTip(LangMgr.txMsgCode(520));
      return;
    }
    if (e.target.getComponent(FmButton).selected) return;
    let floorId = FractureFloorMap[3][0];
    // 当楼层1已经探索完成，并且楼层2已经解锁
    if (
      FractureModule.data.fractureData.remainFloorCountMap[floorId] == 0 &&
      FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[3][1]]
    ) {
      floorId = FractureFloorMap[3][1];
    }
    this._uiState = UIState.BUSY;
    FractureModule.api.travelFloor(
      floorId,
      (data) => {
        this.updateState();
        this._uiState = UIState.IDLE;
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        this._uiState = UIState.IDLE;
        return false;
      }
    );
  }

  // 路过战斗
  private on_click_btn_check_monster_skip() {
    this.getNode("check_monster_skip").active = !this.getNode("check_monster_skip").active;
    this._skip_monster_fight = this.getNode("check_monster_skip").active;
  }

  // 挑战小怪
  private on_click_btn_monster_fight() {
    AudioMgr.instance.playEffect(1766);
    if (FractureModule.service.getFractureState() != FractureState.MONSTER) {
      log.log("不在打小怪状态");
      return;
    }
    if (this.getNode("lbl_monster_cd").active) {
      log.log("cd中");
      return;
    }
    if (
      this.getNode("item_monster_cost").active &&
      !this.getNode("item_monster_cost").getComponent(ItemCost).isEnough()
    ) {
      RouteManager.uiRouteCtrl.showRoute(UIFractureGift);
      return;
    }
    let monsterId = FractureModule.data.fractureData.fightMessage.monsterId;
    let monsterShowId = JsonMgr.instance.jsonList.c_monster[monsterId].monsterShowId;
    let enemyName = JsonMgr.instance.jsonList.c_monsterShow[monsterShowId].name;
    let fractureId = FractureModule.data.fractureData.fractureId;
    let bossCallback = (res: FractureFightResponse) => {
      log.log("boss战斗的数据====", res);
      if (this._skip_monster_fight) {
        if (res.win == false) {
          log.log("时空boss跳过战斗结束-失败");
          RouteManager.uiRouteCtrl.showRoute(UIFractureFightFaild, {
            onCloseBack: () => {
              this.updateState();
            },
          });
        } else {
          log.log("时空boss跳过战斗结束-成功");
          let args: RouteShowArgs = {
            payload: {
              enemyName: enemyName,
              fractureId: fractureId,
              rewardList: res.rewardMessage.rewardList,
            },
            onCloseBack: () => {
              this.updateState();
            },
          };
          RouteManager.uiRouteCtrl.showRoute(UIFractureFightSuccess, args);
        }
        return;
      }
      let data = JSON.parse(res.replay);
      let args: FightData = {
        fightData: data,
        win: res.win,
        clubBossInfo: res,
        resAddList: res.rewardMessage.rewardList,
        // buddyList: this._buddyList,
      };

      RouteManager.uiRouteCtrl.showRoute(UIFractureFight, {
        payload: {
          data: args,
          bossId: monsterId,
          raw: res,
          enemyName: enemyName,
          fractureId: fractureId,
        },
        onCloseBack: (args) => {
          this.updateState();
        },
      });
    };
    FractureModule.api.answerFightMonsterBossPlayer(bossCallback);
  }
  // 绕过小怪
  private on_click_btn_monster_skip() {
    AudioMgr.instance.playEffect(1767);
    if (FractureModule.service.getFractureState() != FractureState.MONSTER) {
      log.log("不在打小怪状态");
      return;
    }
    if (FractureModule.config.fracture_skip_tips) {
      FractureModule.api.answerSkip((data: FractureSkipResponse) => {
        log.log("跳过战斗", data);
        this.updateState();
      });
    } else {
      RouteManager.uiRouteCtrl.showRoute(UIFractureSkipTips, {
        onCloseBack: (args) => {
          if (args) {
            FractureModule.api.answerSkip((data: FractureSkipResponse) => {
              log.log("跳过战斗", data);
              this.updateState();
            });
          }
        },
      });
    }
  }

  // 查看小怪属性
  private on_click_btn_monster_zhanli() {
    if (FractureModule.service.getFractureState() != FractureState.MONSTER) {
      log.log("不在打小怪状态");
      return;
    }
    let monsterId = FractureModule.data.fractureData.fightMessage.monsterId;
    let monster = JsonMgr.instance.jsonList.c_monster[monsterId];
    let args: RouteShowArgs = {
      payload: {
        attr: monster.attr,
      },
    };
    RouteManager.uiRouteCtrl.showRoute(UIFractureTipAttr, args);
  }

  private on_click_btn_check_player_skip() {
    AudioMgr.instance.playEffect(1767);
    this.getNode("check_player_skip").active = !this.getNode("check_player_skip").active;
    this._skip_npc_fight = this.getNode("check_player_skip").active;
  }
  // 挑战玩家
  private on_click_btn_player_fight() {
    AudioMgr.instance.playEffect(1766);
    if (FractureModule.service.getFractureState() != FractureState.PLAYER) {
      log.log("不在打玩家状态");
      return;
    }
    if (this.getNode("lbl_player_cd").active) {
      log.log("cd中");
      return;
    }

    if (
      this.getNode("item_player_cost").active &&
      !this.getNode("item_player_cost").getComponent(ItemCost).isEnough()
    ) {
      RouteManager.uiRouteCtrl.showRoute(UIFractureGift);
      return;
    }
    let monsterId = FractureModule.data.fractureData.fightMessage.monsterId;
    let nickName = FractureModule.data.fractureData.fightMessage.npcMessage.nickname;
    let fractueId = FractureModule.data.fractureData.fractureId;
    let bossCallback = (res: FractureFightResponse) => {
      log.log("boss战斗的数据====", res);
      if (this._skip_npc_fight) {
        if (res.win == false) {
          log.log("时空boss跳过战斗结束-失败");
          RouteManager.uiRouteCtrl.showRoute(UIFractureFightFaild, {
            onCloseBack: () => {
              this.updateState();
            },
          });
        } else {
          log.log("时空boss跳过战斗结束-成功");
          let args: RouteShowArgs = {
            payload: {
              enemyName: nickName,
              fractureId: fractueId,
              rewardList: res.rewardMessage.rewardList,
            },
            onCloseBack: () => {
              this.updateState();
            },
          };
          RouteManager.uiRouteCtrl.showRoute(UIFractureFightSuccess, args);
        }
        return;
      }
      let data = JSON.parse(res.replay);
      let args: FightData = {
        fightData: data,
        win: res.win,
        clubBossInfo: res,
        resAddList: res.rewardMessage.rewardList,
        // buddyList: this._buddyList,
      };
      RouteManager.uiRouteCtrl.showRoute(UIFractureFight, {
        payload: {
          data: args,
          bossId: monsterId,
          enemyName: nickName,
          fractureId: fractueId,
        },
        onCloseBack: (args) => {
          this.updateState();
        },
      });
    };
    FractureModule.api.answerFightMonsterBossPlayer(bossCallback);
  }
  // 绕过玩家
  private on_click_btn_player_skip() {
    if (FractureModule.service.getFractureState() != FractureState.PLAYER) {
      log.log("不在打玩家状态");
      return;
    }
    if (FractureModule.config.fracture_skip_tips) {
      FractureModule.api.answerSkip((data: FractureSkipResponse) => {
        log.log("跳过战斗", data);
        this.updateState();
      });
      return;
    } else {
      RouteManager.uiRouteCtrl.showRoute(UIFractureSkipTips, {
        onCloseBack: (args) => {
          if (args) {
            FractureModule.api.answerSkip((data: FractureSkipResponse) => {
              log.log("跳过战斗", data);
              this.updateState();
            });
          }
        },
      });
    }
  }

  // 查看玩家属性
  private on_click_btn_npc_zhanli() {
    //
    if (FractureModule.service.getFractureState() != FractureState.PLAYER) {
      return;
    }
    let playerMessage = FractureModule.data.fractureData.fightMessage.npcMessage;
    let args: RouteShowArgs = {
      payload: {
        attrMap: playerMessage.battleAttrMap,
      },
    };
    RouteManager.uiRouteCtrl.showRoute(UIFractureTipAttr, args);
  }

  private on_click_btn_baoxiang_open() {
    //
    AudioMgr.instance.playEffect(1779);
    if (FractureModule.service.getFractureState() != FractureState.BAOXIANG) {
      log.log("不在宝箱状态");
      return;
    }
    FractureModule.api.answerDrawOrBox((data) => {
      log.log("打开宝箱", data);
      this.getNode("spine_baoxiang").getComponent(sp.Skeleton).setAnimation(0, "baoxiang_kai", false);
      setTimeout(() => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
      }, 300);
    });
  }

  private on_click_btn_baoxiang_jianliyulan() {
    //
    RouteManager.uiRouteCtrl.showRoute(UIFractureDrawPreview);
  }

  private on_click_btn_old_man_answer1() {
    if (FractureModule.service.getFractureState() != FractureState.OLD_MAN) {
      log.log("不在时空老人状态");
      return;
    }
    let eventId = FractureModule.data.fractureData.choiceId;
    FractureModule.api.answerEvent(1, (data: FractureEventResponse) => {
      log.log("回答事件", data);
      this.updateState();
      RouteManager.uiRouteCtrl.showRoute(UIFractureAnswerSuccess, {
        payload: UIFractureAnswerSuccess.Param.create(eventId, data.rewardMessage.rewardList, "rewardMsg1"),
      });
      // MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
    });
  }
  private on_click_btn_old_man_answer2() {
    if (FractureModule.service.getFractureState() != FractureState.OLD_MAN) {
      return;
    }
    let eventId = FractureModule.data.fractureData.choiceId;
    FractureModule.api.answerEvent(2, (data: FractureEventResponse) => {
      log.log("回答事件", data);
      this.updateState();
      RouteManager.uiRouteCtrl.showRoute(UIFractureAnswerSuccess, {
        payload: UIFractureAnswerSuccess.Param.create(eventId, data.rewardMessage.rewardList, "rewardMsg2"),
      });
      // MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
    });
  }

  // 抽奖
  private on_click_btn_draw() {
    //
    // setTimeout(() => {
    //   this.getNode("node_draw")
    //     .getComponent(FractureDrawAni)
    //     .startDraw(1016, () => {
    //       // this.updateState();
    //       MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: [1016, 1] });
    //     });
    // }, 1000);

    if (FractureModule.service.getFractureState() != FractureState.DRAW) {
      log.log("不在抽奖状态");
      return;
    }
    FractureModule.api.answerDrawOrBox((data) => {
      log.log("抽奖", data);
      //播放动画
      this.getNode("node_draw")
        .getComponent(FractureDrawAni)
        .startDraw(data.rewardMessage.rewardList[0], () => {
          // this.updateState();
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
        });
    });
  }

  private on_click_btn_jianliyulan() {
    //
    if (FractureModule.data.fractureData.fractureId < 0) {
      log.log("当前状态不可预览");
      return;
    }
    RouteManager.uiRouteCtrl.showRoute(UIFractureDrawPreview);
  }

  private on_click_btn_barrier_clear() {
    AudioMgr.instance.playEffect(1778);
    if (FractureModule.service.getFractureState() != FractureState.BARRIER) {
      log.log("不在路障状态");
      return;
    }
    if (
      this.getNode("item_barrier_cost").active &&
      !this.getNode("item_barrier_cost").getComponent(ItemCost).isEnough()
    ) {
      RouteManager.uiRouteCtrl.showRoute(UIFractureGift);
      return;
    }
    let callback = (data: FractureTrapResponse) => {
      // log.log("清除路障", data);
      this.getNode("spine_clear").active = true;
      this.getNode("spine_clear").getComponent(sp.Skeleton).setAnimation(0, "qingli", false);
      this.getNode("spine_clear")
        .getComponent(sp.Skeleton)
        .setCompleteListener(() => {
          this.getNode("spine_clear").active = false;
        });
      tween(this.getNode("node_barrier_img").getComponent(UIOpacity))
        .delay(0.5)
        .to(0.8, { opacity: 0 }, { easing: "quintIn" })
        .call(() => {
          if (data.rewardMessage?.rewardList?.length > 0) {
            MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
          } else {
            log.warn("清除路障奖励为空", data);
            this.updateState();
          }
        })
        .start();
    };

    if (FractureModule.data.fractureData.roadMessage.roadDeadline > TimeUtils.serverTime) {
      FractureModule.api.answerTrap(1, (data: FractureTrapResponse) => {
        callback(data);
      });
    } else {
      FractureModule.api.answerTrap(0, (data: FractureTrapResponse) => {
        callback(data);
      });
    }
  }

  // 路障战盟求助
  private on_click_btn_club_help() {
    //
    if (FractureModule.service.getFractureState() != FractureState.BARRIER) {
      log.log("不在路障状态");
      return;
    }
    if (FractureModule.data.fractureData.roadMessage.assist) {
      TipsMgr.showTip("已发起求助");
      return;
    }
    FractureModule.api.answerTrap(2, () => {
      this.updateState();
    });
  }

  private on_click_btn_help() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 24 });
  }
}
