import { _decorator } from "cc";
import FSMMachine from "./FSMMachine";
const { ccclass, property } = _decorator;

@ccclass("FSMState")
export class FSMState {
  protected _id: number;
  protected _idArray: Array<number> = new Array<number>();
  protected _machine: FSMMachine;

  protected _exit: boolean = true;

  public constructor(id: number) {
    this._id = id;
  }

  public getId() {
    return this._id;
  }

  //添加状态转换
  public addTranslate(id: number) {
    if (!this.isExistTranslate(id)) {
      this._idArray.push(id);
    }
  }

  //移除状态转换
  public removeTranslate(id) {
    if (this.isExistTranslate(id)) {
      for (let i = 0; i < this._idArray.length; i++) {
        if (this._idArray[i] == id) {
          this._idArray.splice(i, 1);
          break;
        }
      }
    }
  }

  public addMachine(machine: FSMMachine) {
    this._machine = machine;
  }

  public translate() {}

  public async onEnter(board) {}
  public onExit(board) {
    this._exit = false;
  }
  public update(board, dt) {}

  //切换状态
  protected switchState(id: number) {
    // if (this.isExistTranslate(id)) {  这里貌似可以不用判断，machine中的逻辑有进行判断
    this._machine.translateState(id);
    // }
  }

  //判断状态是否存在对应的可转转状态数组当中
  public isExistTranslate(id: number): boolean {
    let isExist = false;
    for (let i = 0; i < this._idArray.length; i++) {
      if (this._idArray[i] == id) {
        isExist = true;
        break;
      }
    }
    return isExist;
  }

  //这边是额外条件， 默认返回 true  ,子类可进行重写
  extSwitchStateCheck() {
    return true;
  }
}
