// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Player.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RewardMessage } from "./Comm";

export const protobufPackage = "sim";

/**  */
export interface BubbleMessage {
  /** 气泡ID */
  bubbleId: number;
  /** 有限期截止时间 -1表示没有失效时间 */
  expireTime: number;
  /** 是否选中 */
  chosen: boolean;
}

/**  */
export interface ClubSimpleMessage {
  /** 仙盟ID */
  id: number;
  /** 仙盟名称 */
  name: string;
  /** 旗帜 */
  avatar: string;
  /** 等级 */
  level: number;
  /** 已有人数 */
  cnt: number;
  /** 最大人数 */
  maxCnt: number;
}

/**  */
export interface DecorationMessage {
  /** 装饰物ID */
  decorationId: number;
  /** 有限期截止时间 -1表示没有失效时间 */
  expireTime: number;
}

/**  */
export interface HeadFrameMessage {
  /** 头像框ID */
  headFrameId: number;
  /** 有限期截止时间 -1表示没有失效时间 */
  expireTime: number;
  /** 是否选中 */
  chosen: boolean;
}

/**  */
export interface HeadShowMessage {
  /** 头像ID */
  headShowId: number;
  /** 有限期截止时间 -1表示没有失效时间 */
  expireTime: number;
  /** 是否选中 */
  chosen: boolean;
}

/**  */
export interface HeroSimpleMessage {
  /** 英雄编号 */
  heroId: number;
  /** 英雄等级 */
  level: number;
  /** 英雄突破几次 */
  breakTopLevel: number;
}

/**  */
export interface HorseSimpleMessage {
  /** 当前使用的坐骑编号 */
  horseId: number;
  /** 升阶的阶数 */
  stage: number;
  /** 当阶的已升级的次数 */
  grade: number;
}

/**  */
export interface OfflineEnergyMessage {
  /** 离线期间获得气运 */
  energy: number;
  /** 间隔时间 */
  spanTime: number;
  /** 气运库存 */
  totalEnergy: number;
}

/**  */
export interface PetSimpleMessage {
  /** 宠物模版ID */
  petId: number;
  /** 宠物等级 */
  level: number;
  /** 觉醒次数 */
  awakeCount: number;
  /** 生效的皮肤ID */
  chosenSkinId: number;
}

/**  */
export interface PlayVipExpResponse {
  vipExp: number;
  vipLevel: number;
}

/**  */
export interface PlayerBaseMessage {
  userId: number;
  /** 角色名称 */
  nickname: string;
  /** 头像 */
  avatarList: number[];
  /** 性别 */
  sex: number;
  /** 当前等级 */
  level: number;
  /** 会员等级 */
  vipLevel: number;
  /** 是否对外隐藏贵族 */
  hiddenVip: boolean;
}

/**  */
export interface PlayerBattleAttrResponse {
  battleAttrMap: { [key: number]: number };
  power: number;
  /** 总繁荣度 */
  speed: number;
  /** 三界繁荣度 */
  citySpeed: number;
  /** 弟子繁荣度 */
  pupilSpeed: number;
}

export interface PlayerBattleAttrResponse_BattleAttrMapEntry {
  key: number;
  value: number;
}

/** 创建用户的提交信息 */
export interface PlayerCreateMessage {
  /** 角色名称 */
  name: string;
  /** 性别 */
  sex: number;
}

/**  */
export interface PlayerDailyTreasureResponse {
  /** 弹窗展示新增资源 偶数Index为资源ID，奇数Index为数量 */
  resAddList: number[];
  /** 最近一次玩家领取每日宝箱的时间 前端需要更新到玩家信息的对应字段中 */
  lastDailyTreasureTs: number;
  battleAttrMap: { [key: number]: number };
  power: number;
}

export interface PlayerDailyTreasureResponse_BattleAttrMapEntry {
  key: number;
  value: number;
}

/**  */
export interface PlayerDataMessage {
  id: number;
  /** 角色名称 */
  nickname: string;
  /** 当前等级 */
  level: number;
  /** VIP会员等级 */
  vipLevel: number;
  /** 充值获得的积分 */
  vipExp: number;
  /** 所在服务器ID */
  serverId: number;
  /** 性别 */
  sex: number;
  /** 创建时间戳 */
  createTs: number;
  /** 注册区域IP归属 */
  regArea: string;
  /** 上一次领取每日宝箱的时间 */
  lastDailyTreasureTs: number;
  /** 修改名称的次数 */
  renameCount: number;
  /** 更新时间戳 */
  lastAddEnergyTs: number;
  /** key: 模板ID，可叠加 */
  bigNumItemMap: { [key: number]: number };
  /** key: 模板ID，可叠加 */
  itemMap: { [key: number]: number };
  /** 记录有播放过的动画的模块 */
  animationList: number[];
  /** 引导ID */
  guideId: number;
  /** 全局权限配置 */
  configurationIdList: number[];
}

export interface PlayerDataMessage_BigNumItemMapEntry {
  key: number;
  value: number;
}

export interface PlayerDataMessage_ItemMapEntry {
  key: number;
  value: number;
}

/**  */
export interface PlayerDetailMessage {
  /** 玩家基础信息 */
  simpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 武魂 武魂ID为-1表示不存在 */
  soulMessage:
    | SoulSimpleMessage
    | undefined;
  /** 坐骑 坐骑ID为-1表示不存在 */
  horseMessage:
    | HorseSimpleMessage
    | undefined;
  /** 所在仙盟 ID为-1表示未加入仙盟 */
  clubSimpleMessage:
    | ClubSimpleMessage
    | undefined;
  /** 战力排行 */
  powerRank: number;
  /** 繁荣度排行 */
  energySpeedRank: number;
  /** 演武场排行 */
  competeRank: number;
  heroCount: number;
  friendCount: number;
}

/**  */
export interface PlayerEnergyUpdateMessage {
  /** 总气运 */
  totalEnergy: number;
  /** 服务器时间戳 */
  nowStamp: number;
}

/**  */
export interface PlayerLevelUpResponse {
  /** 当前等级 */
  level: number;
  /** 奖励列表 */
  rewardMessage: RewardMessage | undefined;
}

/**  */
export interface PlayerRankBoardMessage {
  /** 是否领取本服一键点赞排行榜奖励 */
  takeCurServerRankReward: boolean;
}

/**  */
export interface PlayerRankMessage {
  point: number;
  rank: number;
  rankList: PlayerSimpleMessage[];
}

/**  */
export interface PlayerRenameResponse {
  /** 新的角色名称 */
  nickname: string;
  /** 修改名称的次数 */
  renameCount: number;
}

/**  */
export interface PlayerSimpleMessage {
  userId: number;
  /** 角色名称 */
  nickname: string;
  /** 头像 */
  avatarList: number[];
  /** 性别 */
  sex: number;
  /** 当前等级 */
  level: number;
  /** vip等级 */
  vipLevel: number;
  /** 所在服务器ID */
  serverId: number;
  /** 所在服务器的名称 */
  serverName: string;
  /** 注册区域IP归属 */
  regArea: string;
  /** 战斗属性 */
  battleAttrMap: { [key: number]: number };
  /** 赚速 */
  energySpeed: number;
  /** 总战力 */
  power: number;
  /** 最近一次登录的时间 */
  lastLoginTime: number;
  /** 坐骑的模板ID */
  horseId: number;
  /** 是否对外隐藏贵族 */
  hiddenVip: boolean;
}

export interface PlayerSimpleMessage_BattleAttrMapEntry {
  key: number;
  value: number;
}

/**  */
export interface PlayerTempleMessage {
  /** 玩家基础信息 */
  simpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 最强的武将列表 */
  heroList: HeroSimpleMessage[];
  /** 坐骑 坐骑ID为-1表示不存在 */
  horseMessage:
    | HorseSimpleMessage
    | undefined;
  /** 最强的灵兽列表 */
  petList: PetSimpleMessage[];
}

/**  */
export interface PlayerUpdateEnergyRequestMessage {
  autoClick: number;
  manualClick: number;
}

/**  */
export interface SkinMessage {
  /** 皮肤ID */
  skinId: number;
  /** 有限期截止时间 -1表示没有失效时间 */
  expireTime: number;
  /** 叠了几层 */
  level: number;
  /** 数量(一级) */
  stock: number;
  /** 是否选中 */
  chosen: boolean;
}

/**  */
export interface SoulSimpleMessage {
  id: number;
  /** 武魂的模板ID */
  soulTemplateId: number;
  /** 阶 */
  stage: number;
  /** 等级 */
  grade: number;
  /** 属性集合 */
  attrMap: { [key: number]: number };
}

export interface SoulSimpleMessage_AttrMapEntry {
  key: number;
  value: number;
}

/**  */
export interface TitleMessage {
  /** 称号ID */
  titleId: number;
  /** 有限期截止时间 -1表示没有失效时间 */
  expireTime: number;
  /** 是否选中 */
  chosen: boolean;
}

function createBaseBubbleMessage(): BubbleMessage {
  return { bubbleId: 0, expireTime: 0, chosen: false };
}

export const BubbleMessage: MessageFns<BubbleMessage> = {
  encode(message: BubbleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bubbleId !== 0) {
      writer.uint32(8).int64(message.bubbleId);
    }
    if (message.expireTime !== 0) {
      writer.uint32(16).int64(message.expireTime);
    }
    if (message.chosen !== false) {
      writer.uint32(24).bool(message.chosen);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BubbleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBubbleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.bubbleId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.expireTime = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.chosen = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BubbleMessage>, I>>(base?: I): BubbleMessage {
    return BubbleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BubbleMessage>, I>>(object: I): BubbleMessage {
    const message = createBaseBubbleMessage();
    message.bubbleId = object.bubbleId ?? 0;
    message.expireTime = object.expireTime ?? 0;
    message.chosen = object.chosen ?? false;
    return message;
  },
};

function createBaseClubSimpleMessage(): ClubSimpleMessage {
  return { id: 0, name: "", avatar: "", level: 0, cnt: 0, maxCnt: 0 };
}

export const ClubSimpleMessage: MessageFns<ClubSimpleMessage> = {
  encode(message: ClubSimpleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.avatar !== "") {
      writer.uint32(26).string(message.avatar);
    }
    if (message.level !== 0) {
      writer.uint32(32).int32(message.level);
    }
    if (message.cnt !== 0) {
      writer.uint32(40).int32(message.cnt);
    }
    if (message.maxCnt !== 0) {
      writer.uint32(48).int32(message.maxCnt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubSimpleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubSimpleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.avatar = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.cnt = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.maxCnt = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubSimpleMessage>, I>>(base?: I): ClubSimpleMessage {
    return ClubSimpleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubSimpleMessage>, I>>(object: I): ClubSimpleMessage {
    const message = createBaseClubSimpleMessage();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.avatar = object.avatar ?? "";
    message.level = object.level ?? 0;
    message.cnt = object.cnt ?? 0;
    message.maxCnt = object.maxCnt ?? 0;
    return message;
  },
};

function createBaseDecorationMessage(): DecorationMessage {
  return { decorationId: 0, expireTime: 0 };
}

export const DecorationMessage: MessageFns<DecorationMessage> = {
  encode(message: DecorationMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.decorationId !== 0) {
      writer.uint32(8).int64(message.decorationId);
    }
    if (message.expireTime !== 0) {
      writer.uint32(16).int64(message.expireTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DecorationMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDecorationMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.decorationId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.expireTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<DecorationMessage>, I>>(base?: I): DecorationMessage {
    return DecorationMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DecorationMessage>, I>>(object: I): DecorationMessage {
    const message = createBaseDecorationMessage();
    message.decorationId = object.decorationId ?? 0;
    message.expireTime = object.expireTime ?? 0;
    return message;
  },
};

function createBaseHeadFrameMessage(): HeadFrameMessage {
  return { headFrameId: 0, expireTime: 0, chosen: false };
}

export const HeadFrameMessage: MessageFns<HeadFrameMessage> = {
  encode(message: HeadFrameMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.headFrameId !== 0) {
      writer.uint32(8).int64(message.headFrameId);
    }
    if (message.expireTime !== 0) {
      writer.uint32(16).int64(message.expireTime);
    }
    if (message.chosen !== false) {
      writer.uint32(24).bool(message.chosen);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeadFrameMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeadFrameMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.headFrameId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.expireTime = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.chosen = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeadFrameMessage>, I>>(base?: I): HeadFrameMessage {
    return HeadFrameMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeadFrameMessage>, I>>(object: I): HeadFrameMessage {
    const message = createBaseHeadFrameMessage();
    message.headFrameId = object.headFrameId ?? 0;
    message.expireTime = object.expireTime ?? 0;
    message.chosen = object.chosen ?? false;
    return message;
  },
};

function createBaseHeadShowMessage(): HeadShowMessage {
  return { headShowId: 0, expireTime: 0, chosen: false };
}

export const HeadShowMessage: MessageFns<HeadShowMessage> = {
  encode(message: HeadShowMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.headShowId !== 0) {
      writer.uint32(8).int64(message.headShowId);
    }
    if (message.expireTime !== 0) {
      writer.uint32(16).int64(message.expireTime);
    }
    if (message.chosen !== false) {
      writer.uint32(24).bool(message.chosen);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeadShowMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeadShowMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.headShowId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.expireTime = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.chosen = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeadShowMessage>, I>>(base?: I): HeadShowMessage {
    return HeadShowMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeadShowMessage>, I>>(object: I): HeadShowMessage {
    const message = createBaseHeadShowMessage();
    message.headShowId = object.headShowId ?? 0;
    message.expireTime = object.expireTime ?? 0;
    message.chosen = object.chosen ?? false;
    return message;
  },
};

function createBaseHeroSimpleMessage(): HeroSimpleMessage {
  return { heroId: 0, level: 0, breakTopLevel: 0 };
}

export const HeroSimpleMessage: MessageFns<HeroSimpleMessage> = {
  encode(message: HeroSimpleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.heroId !== 0) {
      writer.uint32(8).int64(message.heroId);
    }
    if (message.level !== 0) {
      writer.uint32(16).int32(message.level);
    }
    if (message.breakTopLevel !== 0) {
      writer.uint32(24).int32(message.breakTopLevel);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeroSimpleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeroSimpleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.heroId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.breakTopLevel = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeroSimpleMessage>, I>>(base?: I): HeroSimpleMessage {
    return HeroSimpleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeroSimpleMessage>, I>>(object: I): HeroSimpleMessage {
    const message = createBaseHeroSimpleMessage();
    message.heroId = object.heroId ?? 0;
    message.level = object.level ?? 0;
    message.breakTopLevel = object.breakTopLevel ?? 0;
    return message;
  },
};

function createBaseHorseSimpleMessage(): HorseSimpleMessage {
  return { horseId: 0, stage: 0, grade: 0 };
}

export const HorseSimpleMessage: MessageFns<HorseSimpleMessage> = {
  encode(message: HorseSimpleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.horseId !== 0) {
      writer.uint32(8).int64(message.horseId);
    }
    if (message.stage !== 0) {
      writer.uint32(16).int32(message.stage);
    }
    if (message.grade !== 0) {
      writer.uint32(24).int32(message.grade);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HorseSimpleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHorseSimpleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.horseId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.stage = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.grade = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HorseSimpleMessage>, I>>(base?: I): HorseSimpleMessage {
    return HorseSimpleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HorseSimpleMessage>, I>>(object: I): HorseSimpleMessage {
    const message = createBaseHorseSimpleMessage();
    message.horseId = object.horseId ?? 0;
    message.stage = object.stage ?? 0;
    message.grade = object.grade ?? 0;
    return message;
  },
};

function createBaseOfflineEnergyMessage(): OfflineEnergyMessage {
  return { energy: 0, spanTime: 0, totalEnergy: 0 };
}

export const OfflineEnergyMessage: MessageFns<OfflineEnergyMessage> = {
  encode(message: OfflineEnergyMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.energy !== 0) {
      writer.uint32(9).double(message.energy);
    }
    if (message.spanTime !== 0) {
      writer.uint32(16).int64(message.spanTime);
    }
    if (message.totalEnergy !== 0) {
      writer.uint32(25).double(message.totalEnergy);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OfflineEnergyMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOfflineEnergyMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.energy = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.spanTime = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.totalEnergy = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<OfflineEnergyMessage>, I>>(base?: I): OfflineEnergyMessage {
    return OfflineEnergyMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OfflineEnergyMessage>, I>>(object: I): OfflineEnergyMessage {
    const message = createBaseOfflineEnergyMessage();
    message.energy = object.energy ?? 0;
    message.spanTime = object.spanTime ?? 0;
    message.totalEnergy = object.totalEnergy ?? 0;
    return message;
  },
};

function createBasePetSimpleMessage(): PetSimpleMessage {
  return { petId: 0, level: 0, awakeCount: 0, chosenSkinId: 0 };
}

export const PetSimpleMessage: MessageFns<PetSimpleMessage> = {
  encode(message: PetSimpleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.petId !== 0) {
      writer.uint32(8).int64(message.petId);
    }
    if (message.level !== 0) {
      writer.uint32(16).int32(message.level);
    }
    if (message.awakeCount !== 0) {
      writer.uint32(24).int32(message.awakeCount);
    }
    if (message.chosenSkinId !== 0) {
      writer.uint32(32).int64(message.chosenSkinId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PetSimpleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePetSimpleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.petId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.awakeCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.chosenSkinId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PetSimpleMessage>, I>>(base?: I): PetSimpleMessage {
    return PetSimpleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PetSimpleMessage>, I>>(object: I): PetSimpleMessage {
    const message = createBasePetSimpleMessage();
    message.petId = object.petId ?? 0;
    message.level = object.level ?? 0;
    message.awakeCount = object.awakeCount ?? 0;
    message.chosenSkinId = object.chosenSkinId ?? 0;
    return message;
  },
};

function createBasePlayVipExpResponse(): PlayVipExpResponse {
  return { vipExp: 0, vipLevel: 0 };
}

export const PlayVipExpResponse: MessageFns<PlayVipExpResponse> = {
  encode(message: PlayVipExpResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.vipExp !== 0) {
      writer.uint32(8).int32(message.vipExp);
    }
    if (message.vipLevel !== 0) {
      writer.uint32(16).int32(message.vipLevel);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayVipExpResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayVipExpResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.vipExp = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.vipLevel = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayVipExpResponse>, I>>(base?: I): PlayVipExpResponse {
    return PlayVipExpResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayVipExpResponse>, I>>(object: I): PlayVipExpResponse {
    const message = createBasePlayVipExpResponse();
    message.vipExp = object.vipExp ?? 0;
    message.vipLevel = object.vipLevel ?? 0;
    return message;
  },
};

function createBasePlayerBaseMessage(): PlayerBaseMessage {
  return { userId: 0, nickname: "", avatarList: [], sex: 0, level: 0, vipLevel: 0, hiddenVip: false };
}

export const PlayerBaseMessage: MessageFns<PlayerBaseMessage> = {
  encode(message: PlayerBaseMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== 0) {
      writer.uint32(8).int64(message.userId);
    }
    if (message.nickname !== "") {
      writer.uint32(18).string(message.nickname);
    }
    writer.uint32(26).fork();
    for (const v of message.avatarList) {
      writer.int64(v);
    }
    writer.join();
    if (message.sex !== 0) {
      writer.uint32(32).int32(message.sex);
    }
    if (message.level !== 0) {
      writer.uint32(40).int32(message.level);
    }
    if (message.vipLevel !== 0) {
      writer.uint32(48).int32(message.vipLevel);
    }
    if (message.hiddenVip !== false) {
      writer.uint32(56).bool(message.hiddenVip);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerBaseMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerBaseMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nickname = reader.string();
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.avatarList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.avatarList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.sex = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.vipLevel = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.hiddenVip = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerBaseMessage>, I>>(base?: I): PlayerBaseMessage {
    return PlayerBaseMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerBaseMessage>, I>>(object: I): PlayerBaseMessage {
    const message = createBasePlayerBaseMessage();
    message.userId = object.userId ?? 0;
    message.nickname = object.nickname ?? "";
    message.avatarList = object.avatarList?.map((e) => e) || [];
    message.sex = object.sex ?? 0;
    message.level = object.level ?? 0;
    message.vipLevel = object.vipLevel ?? 0;
    message.hiddenVip = object.hiddenVip ?? false;
    return message;
  },
};

function createBasePlayerBattleAttrResponse(): PlayerBattleAttrResponse {
  return { battleAttrMap: {}, power: 0, speed: 0, citySpeed: 0, pupilSpeed: 0 };
}

export const PlayerBattleAttrResponse: MessageFns<PlayerBattleAttrResponse> = {
  encode(message: PlayerBattleAttrResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.battleAttrMap).forEach(([key, value]) => {
      PlayerBattleAttrResponse_BattleAttrMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    if (message.power !== 0) {
      writer.uint32(17).double(message.power);
    }
    if (message.speed !== 0) {
      writer.uint32(25).double(message.speed);
    }
    if (message.citySpeed !== 0) {
      writer.uint32(33).double(message.citySpeed);
    }
    if (message.pupilSpeed !== 0) {
      writer.uint32(41).double(message.pupilSpeed);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerBattleAttrResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerBattleAttrResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = PlayerBattleAttrResponse_BattleAttrMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.battleAttrMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.power = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.speed = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.citySpeed = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.pupilSpeed = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerBattleAttrResponse>, I>>(base?: I): PlayerBattleAttrResponse {
    return PlayerBattleAttrResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerBattleAttrResponse>, I>>(object: I): PlayerBattleAttrResponse {
    const message = createBasePlayerBattleAttrResponse();
    message.battleAttrMap = Object.entries(object.battleAttrMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.power = object.power ?? 0;
    message.speed = object.speed ?? 0;
    message.citySpeed = object.citySpeed ?? 0;
    message.pupilSpeed = object.pupilSpeed ?? 0;
    return message;
  },
};

function createBasePlayerBattleAttrResponse_BattleAttrMapEntry(): PlayerBattleAttrResponse_BattleAttrMapEntry {
  return { key: 0, value: 0 };
}

export const PlayerBattleAttrResponse_BattleAttrMapEntry: MessageFns<PlayerBattleAttrResponse_BattleAttrMapEntry> = {
  encode(
    message: PlayerBattleAttrResponse_BattleAttrMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerBattleAttrResponse_BattleAttrMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerBattleAttrResponse_BattleAttrMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerBattleAttrResponse_BattleAttrMapEntry>, I>>(
    base?: I,
  ): PlayerBattleAttrResponse_BattleAttrMapEntry {
    return PlayerBattleAttrResponse_BattleAttrMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerBattleAttrResponse_BattleAttrMapEntry>, I>>(
    object: I,
  ): PlayerBattleAttrResponse_BattleAttrMapEntry {
    const message = createBasePlayerBattleAttrResponse_BattleAttrMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePlayerCreateMessage(): PlayerCreateMessage {
  return { name: "", sex: 0 };
}

export const PlayerCreateMessage: MessageFns<PlayerCreateMessage> = {
  encode(message: PlayerCreateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.sex !== 0) {
      writer.uint32(16).int32(message.sex);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerCreateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerCreateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.sex = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerCreateMessage>, I>>(base?: I): PlayerCreateMessage {
    return PlayerCreateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerCreateMessage>, I>>(object: I): PlayerCreateMessage {
    const message = createBasePlayerCreateMessage();
    message.name = object.name ?? "";
    message.sex = object.sex ?? 0;
    return message;
  },
};

function createBasePlayerDailyTreasureResponse(): PlayerDailyTreasureResponse {
  return { resAddList: [], lastDailyTreasureTs: 0, battleAttrMap: {}, power: 0 };
}

export const PlayerDailyTreasureResponse: MessageFns<PlayerDailyTreasureResponse> = {
  encode(message: PlayerDailyTreasureResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    if (message.lastDailyTreasureTs !== 0) {
      writer.uint32(16).int64(message.lastDailyTreasureTs);
    }
    Object.entries(message.battleAttrMap).forEach(([key, value]) => {
      PlayerDailyTreasureResponse_BattleAttrMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork())
        .join();
    });
    if (message.power !== 0) {
      writer.uint32(33).double(message.power);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerDailyTreasureResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerDailyTreasureResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lastDailyTreasureTs = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = PlayerDailyTreasureResponse_BattleAttrMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.battleAttrMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.power = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerDailyTreasureResponse>, I>>(base?: I): PlayerDailyTreasureResponse {
    return PlayerDailyTreasureResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerDailyTreasureResponse>, I>>(object: I): PlayerDailyTreasureResponse {
    const message = createBasePlayerDailyTreasureResponse();
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.lastDailyTreasureTs = object.lastDailyTreasureTs ?? 0;
    message.battleAttrMap = Object.entries(object.battleAttrMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.power = object.power ?? 0;
    return message;
  },
};

function createBasePlayerDailyTreasureResponse_BattleAttrMapEntry(): PlayerDailyTreasureResponse_BattleAttrMapEntry {
  return { key: 0, value: 0 };
}

export const PlayerDailyTreasureResponse_BattleAttrMapEntry: MessageFns<
  PlayerDailyTreasureResponse_BattleAttrMapEntry
> = {
  encode(
    message: PlayerDailyTreasureResponse_BattleAttrMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerDailyTreasureResponse_BattleAttrMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerDailyTreasureResponse_BattleAttrMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerDailyTreasureResponse_BattleAttrMapEntry>, I>>(
    base?: I,
  ): PlayerDailyTreasureResponse_BattleAttrMapEntry {
    return PlayerDailyTreasureResponse_BattleAttrMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerDailyTreasureResponse_BattleAttrMapEntry>, I>>(
    object: I,
  ): PlayerDailyTreasureResponse_BattleAttrMapEntry {
    const message = createBasePlayerDailyTreasureResponse_BattleAttrMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePlayerDataMessage(): PlayerDataMessage {
  return {
    id: 0,
    nickname: "",
    level: 0,
    vipLevel: 0,
    vipExp: 0,
    serverId: 0,
    sex: 0,
    createTs: 0,
    regArea: "",
    lastDailyTreasureTs: 0,
    renameCount: 0,
    lastAddEnergyTs: 0,
    bigNumItemMap: {},
    itemMap: {},
    animationList: [],
    guideId: 0,
    configurationIdList: [],
  };
}

export const PlayerDataMessage: MessageFns<PlayerDataMessage> = {
  encode(message: PlayerDataMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.nickname !== "") {
      writer.uint32(18).string(message.nickname);
    }
    if (message.level !== 0) {
      writer.uint32(24).int32(message.level);
    }
    if (message.vipLevel !== 0) {
      writer.uint32(32).int32(message.vipLevel);
    }
    if (message.vipExp !== 0) {
      writer.uint32(40).int32(message.vipExp);
    }
    if (message.serverId !== 0) {
      writer.uint32(48).int64(message.serverId);
    }
    if (message.sex !== 0) {
      writer.uint32(56).int32(message.sex);
    }
    if (message.createTs !== 0) {
      writer.uint32(64).int64(message.createTs);
    }
    if (message.regArea !== "") {
      writer.uint32(74).string(message.regArea);
    }
    if (message.lastDailyTreasureTs !== 0) {
      writer.uint32(80).int64(message.lastDailyTreasureTs);
    }
    if (message.renameCount !== 0) {
      writer.uint32(88).int32(message.renameCount);
    }
    if (message.lastAddEnergyTs !== 0) {
      writer.uint32(96).int64(message.lastAddEnergyTs);
    }
    Object.entries(message.bigNumItemMap).forEach(([key, value]) => {
      PlayerDataMessage_BigNumItemMapEntry.encode({ key: key as any, value }, writer.uint32(106).fork()).join();
    });
    Object.entries(message.itemMap).forEach(([key, value]) => {
      PlayerDataMessage_ItemMapEntry.encode({ key: key as any, value }, writer.uint32(114).fork()).join();
    });
    writer.uint32(122).fork();
    for (const v of message.animationList) {
      writer.int64(v);
    }
    writer.join();
    if (message.guideId !== 0) {
      writer.uint32(128).int64(message.guideId);
    }
    writer.uint32(138).fork();
    for (const v of message.configurationIdList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerDataMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerDataMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nickname = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.vipLevel = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.vipExp = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.serverId = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.sex = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.createTs = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.regArea = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.lastDailyTreasureTs = longToNumber(reader.int64());
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.renameCount = reader.int32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.lastAddEnergyTs = longToNumber(reader.int64());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          const entry13 = PlayerDataMessage_BigNumItemMapEntry.decode(reader, reader.uint32());
          if (entry13.value !== undefined) {
            message.bigNumItemMap[entry13.key] = entry13.value;
          }
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          const entry14 = PlayerDataMessage_ItemMapEntry.decode(reader, reader.uint32());
          if (entry14.value !== undefined) {
            message.itemMap[entry14.key] = entry14.value;
          }
          continue;
        }
        case 15: {
          if (tag === 120) {
            message.animationList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 122) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.animationList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.guideId = longToNumber(reader.int64());
          continue;
        }
        case 17: {
          if (tag === 136) {
            message.configurationIdList.push(reader.int32());

            continue;
          }

          if (tag === 138) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.configurationIdList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerDataMessage>, I>>(base?: I): PlayerDataMessage {
    return PlayerDataMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerDataMessage>, I>>(object: I): PlayerDataMessage {
    const message = createBasePlayerDataMessage();
    message.id = object.id ?? 0;
    message.nickname = object.nickname ?? "";
    message.level = object.level ?? 0;
    message.vipLevel = object.vipLevel ?? 0;
    message.vipExp = object.vipExp ?? 0;
    message.serverId = object.serverId ?? 0;
    message.sex = object.sex ?? 0;
    message.createTs = object.createTs ?? 0;
    message.regArea = object.regArea ?? "";
    message.lastDailyTreasureTs = object.lastDailyTreasureTs ?? 0;
    message.renameCount = object.renameCount ?? 0;
    message.lastAddEnergyTs = object.lastAddEnergyTs ?? 0;
    message.bigNumItemMap = Object.entries(object.bigNumItemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.itemMap = Object.entries(object.itemMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.animationList = object.animationList?.map((e) => e) || [];
    message.guideId = object.guideId ?? 0;
    message.configurationIdList = object.configurationIdList?.map((e) => e) || [];
    return message;
  },
};

function createBasePlayerDataMessage_BigNumItemMapEntry(): PlayerDataMessage_BigNumItemMapEntry {
  return { key: 0, value: 0 };
}

export const PlayerDataMessage_BigNumItemMapEntry: MessageFns<PlayerDataMessage_BigNumItemMapEntry> = {
  encode(message: PlayerDataMessage_BigNumItemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerDataMessage_BigNumItemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerDataMessage_BigNumItemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerDataMessage_BigNumItemMapEntry>, I>>(
    base?: I,
  ): PlayerDataMessage_BigNumItemMapEntry {
    return PlayerDataMessage_BigNumItemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerDataMessage_BigNumItemMapEntry>, I>>(
    object: I,
  ): PlayerDataMessage_BigNumItemMapEntry {
    const message = createBasePlayerDataMessage_BigNumItemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePlayerDataMessage_ItemMapEntry(): PlayerDataMessage_ItemMapEntry {
  return { key: 0, value: 0 };
}

export const PlayerDataMessage_ItemMapEntry: MessageFns<PlayerDataMessage_ItemMapEntry> = {
  encode(message: PlayerDataMessage_ItemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerDataMessage_ItemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerDataMessage_ItemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerDataMessage_ItemMapEntry>, I>>(base?: I): PlayerDataMessage_ItemMapEntry {
    return PlayerDataMessage_ItemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerDataMessage_ItemMapEntry>, I>>(
    object: I,
  ): PlayerDataMessage_ItemMapEntry {
    const message = createBasePlayerDataMessage_ItemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePlayerDetailMessage(): PlayerDetailMessage {
  return {
    simpleMessage: undefined,
    soulMessage: undefined,
    horseMessage: undefined,
    clubSimpleMessage: undefined,
    powerRank: 0,
    energySpeedRank: 0,
    competeRank: 0,
    heroCount: 0,
    friendCount: 0,
  };
}

export const PlayerDetailMessage: MessageFns<PlayerDetailMessage> = {
  encode(message: PlayerDetailMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.simpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.simpleMessage, writer.uint32(10).fork()).join();
    }
    if (message.soulMessage !== undefined) {
      SoulSimpleMessage.encode(message.soulMessage, writer.uint32(18).fork()).join();
    }
    if (message.horseMessage !== undefined) {
      HorseSimpleMessage.encode(message.horseMessage, writer.uint32(26).fork()).join();
    }
    if (message.clubSimpleMessage !== undefined) {
      ClubSimpleMessage.encode(message.clubSimpleMessage, writer.uint32(34).fork()).join();
    }
    if (message.powerRank !== 0) {
      writer.uint32(40).int32(message.powerRank);
    }
    if (message.energySpeedRank !== 0) {
      writer.uint32(48).int32(message.energySpeedRank);
    }
    if (message.competeRank !== 0) {
      writer.uint32(56).int32(message.competeRank);
    }
    if (message.heroCount !== 0) {
      writer.uint32(64).int32(message.heroCount);
    }
    if (message.friendCount !== 0) {
      writer.uint32(72).int32(message.friendCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerDetailMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerDetailMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.simpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.soulMessage = SoulSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.horseMessage = HorseSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.clubSimpleMessage = ClubSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.powerRank = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.energySpeedRank = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.competeRank = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.heroCount = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.friendCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerDetailMessage>, I>>(base?: I): PlayerDetailMessage {
    return PlayerDetailMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerDetailMessage>, I>>(object: I): PlayerDetailMessage {
    const message = createBasePlayerDetailMessage();
    message.simpleMessage = (object.simpleMessage !== undefined && object.simpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.simpleMessage)
      : undefined;
    message.soulMessage = (object.soulMessage !== undefined && object.soulMessage !== null)
      ? SoulSimpleMessage.fromPartial(object.soulMessage)
      : undefined;
    message.horseMessage = (object.horseMessage !== undefined && object.horseMessage !== null)
      ? HorseSimpleMessage.fromPartial(object.horseMessage)
      : undefined;
    message.clubSimpleMessage = (object.clubSimpleMessage !== undefined && object.clubSimpleMessage !== null)
      ? ClubSimpleMessage.fromPartial(object.clubSimpleMessage)
      : undefined;
    message.powerRank = object.powerRank ?? 0;
    message.energySpeedRank = object.energySpeedRank ?? 0;
    message.competeRank = object.competeRank ?? 0;
    message.heroCount = object.heroCount ?? 0;
    message.friendCount = object.friendCount ?? 0;
    return message;
  },
};

function createBasePlayerEnergyUpdateMessage(): PlayerEnergyUpdateMessage {
  return { totalEnergy: 0, nowStamp: 0 };
}

export const PlayerEnergyUpdateMessage: MessageFns<PlayerEnergyUpdateMessage> = {
  encode(message: PlayerEnergyUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalEnergy !== 0) {
      writer.uint32(9).double(message.totalEnergy);
    }
    if (message.nowStamp !== 0) {
      writer.uint32(16).int64(message.nowStamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerEnergyUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerEnergyUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.totalEnergy = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.nowStamp = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerEnergyUpdateMessage>, I>>(base?: I): PlayerEnergyUpdateMessage {
    return PlayerEnergyUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerEnergyUpdateMessage>, I>>(object: I): PlayerEnergyUpdateMessage {
    const message = createBasePlayerEnergyUpdateMessage();
    message.totalEnergy = object.totalEnergy ?? 0;
    message.nowStamp = object.nowStamp ?? 0;
    return message;
  },
};

function createBasePlayerLevelUpResponse(): PlayerLevelUpResponse {
  return { level: 0, rewardMessage: undefined };
}

export const PlayerLevelUpResponse: MessageFns<PlayerLevelUpResponse> = {
  encode(message: PlayerLevelUpResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.level !== 0) {
      writer.uint32(8).int32(message.level);
    }
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerLevelUpResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerLevelUpResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerLevelUpResponse>, I>>(base?: I): PlayerLevelUpResponse {
    return PlayerLevelUpResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerLevelUpResponse>, I>>(object: I): PlayerLevelUpResponse {
    const message = createBasePlayerLevelUpResponse();
    message.level = object.level ?? 0;
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBasePlayerRankBoardMessage(): PlayerRankBoardMessage {
  return { takeCurServerRankReward: false };
}

export const PlayerRankBoardMessage: MessageFns<PlayerRankBoardMessage> = {
  encode(message: PlayerRankBoardMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.takeCurServerRankReward !== false) {
      writer.uint32(8).bool(message.takeCurServerRankReward);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerRankBoardMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerRankBoardMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.takeCurServerRankReward = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerRankBoardMessage>, I>>(base?: I): PlayerRankBoardMessage {
    return PlayerRankBoardMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerRankBoardMessage>, I>>(object: I): PlayerRankBoardMessage {
    const message = createBasePlayerRankBoardMessage();
    message.takeCurServerRankReward = object.takeCurServerRankReward ?? false;
    return message;
  },
};

function createBasePlayerRankMessage(): PlayerRankMessage {
  return { point: 0, rank: 0, rankList: [] };
}

export const PlayerRankMessage: MessageFns<PlayerRankMessage> = {
  encode(message: PlayerRankMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.point !== 0) {
      writer.uint32(9).double(message.point);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    for (const v of message.rankList) {
      PlayerSimpleMessage.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerRankMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerRankMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.point = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rankList.push(PlayerSimpleMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerRankMessage>, I>>(base?: I): PlayerRankMessage {
    return PlayerRankMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerRankMessage>, I>>(object: I): PlayerRankMessage {
    const message = createBasePlayerRankMessage();
    message.point = object.point ?? 0;
    message.rank = object.rank ?? 0;
    message.rankList = object.rankList?.map((e) => PlayerSimpleMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBasePlayerRenameResponse(): PlayerRenameResponse {
  return { nickname: "", renameCount: 0 };
}

export const PlayerRenameResponse: MessageFns<PlayerRenameResponse> = {
  encode(message: PlayerRenameResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nickname !== "") {
      writer.uint32(10).string(message.nickname);
    }
    if (message.renameCount !== 0) {
      writer.uint32(16).int32(message.renameCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerRenameResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerRenameResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.nickname = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.renameCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerRenameResponse>, I>>(base?: I): PlayerRenameResponse {
    return PlayerRenameResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerRenameResponse>, I>>(object: I): PlayerRenameResponse {
    const message = createBasePlayerRenameResponse();
    message.nickname = object.nickname ?? "";
    message.renameCount = object.renameCount ?? 0;
    return message;
  },
};

function createBasePlayerSimpleMessage(): PlayerSimpleMessage {
  return {
    userId: 0,
    nickname: "",
    avatarList: [],
    sex: 0,
    level: 0,
    vipLevel: 0,
    serverId: 0,
    serverName: "",
    regArea: "",
    battleAttrMap: {},
    energySpeed: 0,
    power: 0,
    lastLoginTime: 0,
    horseId: 0,
    hiddenVip: false,
  };
}

export const PlayerSimpleMessage: MessageFns<PlayerSimpleMessage> = {
  encode(message: PlayerSimpleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== 0) {
      writer.uint32(8).int64(message.userId);
    }
    if (message.nickname !== "") {
      writer.uint32(18).string(message.nickname);
    }
    writer.uint32(26).fork();
    for (const v of message.avatarList) {
      writer.int64(v);
    }
    writer.join();
    if (message.sex !== 0) {
      writer.uint32(32).int32(message.sex);
    }
    if (message.level !== 0) {
      writer.uint32(40).int32(message.level);
    }
    if (message.vipLevel !== 0) {
      writer.uint32(48).int32(message.vipLevel);
    }
    if (message.serverId !== 0) {
      writer.uint32(56).int64(message.serverId);
    }
    if (message.serverName !== "") {
      writer.uint32(66).string(message.serverName);
    }
    if (message.regArea !== "") {
      writer.uint32(74).string(message.regArea);
    }
    Object.entries(message.battleAttrMap).forEach(([key, value]) => {
      PlayerSimpleMessage_BattleAttrMapEntry.encode({ key: key as any, value }, writer.uint32(82).fork()).join();
    });
    if (message.energySpeed !== 0) {
      writer.uint32(89).double(message.energySpeed);
    }
    if (message.power !== 0) {
      writer.uint32(97).double(message.power);
    }
    if (message.lastLoginTime !== 0) {
      writer.uint32(104).int64(message.lastLoginTime);
    }
    if (message.horseId !== 0) {
      writer.uint32(112).int64(message.horseId);
    }
    if (message.hiddenVip !== false) {
      writer.uint32(120).bool(message.hiddenVip);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerSimpleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerSimpleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nickname = reader.string();
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.avatarList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.avatarList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.sex = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.vipLevel = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.serverId = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.serverName = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.regArea = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          const entry10 = PlayerSimpleMessage_BattleAttrMapEntry.decode(reader, reader.uint32());
          if (entry10.value !== undefined) {
            message.battleAttrMap[entry10.key] = entry10.value;
          }
          continue;
        }
        case 11: {
          if (tag !== 89) {
            break;
          }

          message.energySpeed = reader.double();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.power = reader.double();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.lastLoginTime = longToNumber(reader.int64());
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.horseId = longToNumber(reader.int64());
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.hiddenVip = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerSimpleMessage>, I>>(base?: I): PlayerSimpleMessage {
    return PlayerSimpleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerSimpleMessage>, I>>(object: I): PlayerSimpleMessage {
    const message = createBasePlayerSimpleMessage();
    message.userId = object.userId ?? 0;
    message.nickname = object.nickname ?? "";
    message.avatarList = object.avatarList?.map((e) => e) || [];
    message.sex = object.sex ?? 0;
    message.level = object.level ?? 0;
    message.vipLevel = object.vipLevel ?? 0;
    message.serverId = object.serverId ?? 0;
    message.serverName = object.serverName ?? "";
    message.regArea = object.regArea ?? "";
    message.battleAttrMap = Object.entries(object.battleAttrMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.energySpeed = object.energySpeed ?? 0;
    message.power = object.power ?? 0;
    message.lastLoginTime = object.lastLoginTime ?? 0;
    message.horseId = object.horseId ?? 0;
    message.hiddenVip = object.hiddenVip ?? false;
    return message;
  },
};

function createBasePlayerSimpleMessage_BattleAttrMapEntry(): PlayerSimpleMessage_BattleAttrMapEntry {
  return { key: 0, value: 0 };
}

export const PlayerSimpleMessage_BattleAttrMapEntry: MessageFns<PlayerSimpleMessage_BattleAttrMapEntry> = {
  encode(message: PlayerSimpleMessage_BattleAttrMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerSimpleMessage_BattleAttrMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerSimpleMessage_BattleAttrMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerSimpleMessage_BattleAttrMapEntry>, I>>(
    base?: I,
  ): PlayerSimpleMessage_BattleAttrMapEntry {
    return PlayerSimpleMessage_BattleAttrMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerSimpleMessage_BattleAttrMapEntry>, I>>(
    object: I,
  ): PlayerSimpleMessage_BattleAttrMapEntry {
    const message = createBasePlayerSimpleMessage_BattleAttrMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePlayerTempleMessage(): PlayerTempleMessage {
  return { simpleMessage: undefined, heroList: [], horseMessage: undefined, petList: [] };
}

export const PlayerTempleMessage: MessageFns<PlayerTempleMessage> = {
  encode(message: PlayerTempleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.simpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.simpleMessage, writer.uint32(10).fork()).join();
    }
    for (const v of message.heroList) {
      HeroSimpleMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.horseMessage !== undefined) {
      HorseSimpleMessage.encode(message.horseMessage, writer.uint32(26).fork()).join();
    }
    for (const v of message.petList) {
      PetSimpleMessage.encode(v!, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerTempleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerTempleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.simpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.heroList.push(HeroSimpleMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.horseMessage = HorseSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.petList.push(PetSimpleMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerTempleMessage>, I>>(base?: I): PlayerTempleMessage {
    return PlayerTempleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerTempleMessage>, I>>(object: I): PlayerTempleMessage {
    const message = createBasePlayerTempleMessage();
    message.simpleMessage = (object.simpleMessage !== undefined && object.simpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.simpleMessage)
      : undefined;
    message.heroList = object.heroList?.map((e) => HeroSimpleMessage.fromPartial(e)) || [];
    message.horseMessage = (object.horseMessage !== undefined && object.horseMessage !== null)
      ? HorseSimpleMessage.fromPartial(object.horseMessage)
      : undefined;
    message.petList = object.petList?.map((e) => PetSimpleMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBasePlayerUpdateEnergyRequestMessage(): PlayerUpdateEnergyRequestMessage {
  return { autoClick: 0, manualClick: 0 };
}

export const PlayerUpdateEnergyRequestMessage: MessageFns<PlayerUpdateEnergyRequestMessage> = {
  encode(message: PlayerUpdateEnergyRequestMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.autoClick !== 0) {
      writer.uint32(8).int32(message.autoClick);
    }
    if (message.manualClick !== 0) {
      writer.uint32(16).int32(message.manualClick);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlayerUpdateEnergyRequestMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlayerUpdateEnergyRequestMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.autoClick = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.manualClick = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PlayerUpdateEnergyRequestMessage>, I>>(
    base?: I,
  ): PlayerUpdateEnergyRequestMessage {
    return PlayerUpdateEnergyRequestMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlayerUpdateEnergyRequestMessage>, I>>(
    object: I,
  ): PlayerUpdateEnergyRequestMessage {
    const message = createBasePlayerUpdateEnergyRequestMessage();
    message.autoClick = object.autoClick ?? 0;
    message.manualClick = object.manualClick ?? 0;
    return message;
  },
};

function createBaseSkinMessage(): SkinMessage {
  return { skinId: 0, expireTime: 0, level: 0, stock: 0, chosen: false };
}

export const SkinMessage: MessageFns<SkinMessage> = {
  encode(message: SkinMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.skinId !== 0) {
      writer.uint32(8).int64(message.skinId);
    }
    if (message.expireTime !== 0) {
      writer.uint32(16).int64(message.expireTime);
    }
    if (message.level !== 0) {
      writer.uint32(24).int32(message.level);
    }
    if (message.stock !== 0) {
      writer.uint32(32).int32(message.stock);
    }
    if (message.chosen !== false) {
      writer.uint32(40).bool(message.chosen);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SkinMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSkinMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.skinId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.expireTime = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.stock = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.chosen = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SkinMessage>, I>>(base?: I): SkinMessage {
    return SkinMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkinMessage>, I>>(object: I): SkinMessage {
    const message = createBaseSkinMessage();
    message.skinId = object.skinId ?? 0;
    message.expireTime = object.expireTime ?? 0;
    message.level = object.level ?? 0;
    message.stock = object.stock ?? 0;
    message.chosen = object.chosen ?? false;
    return message;
  },
};

function createBaseSoulSimpleMessage(): SoulSimpleMessage {
  return { id: 0, soulTemplateId: 0, stage: 0, grade: 0, attrMap: {} };
}

export const SoulSimpleMessage: MessageFns<SoulSimpleMessage> = {
  encode(message: SoulSimpleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.soulTemplateId !== 0) {
      writer.uint32(16).int64(message.soulTemplateId);
    }
    if (message.stage !== 0) {
      writer.uint32(24).int32(message.stage);
    }
    if (message.grade !== 0) {
      writer.uint32(32).int32(message.grade);
    }
    Object.entries(message.attrMap).forEach(([key, value]) => {
      SoulSimpleMessage_AttrMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulSimpleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulSimpleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.soulTemplateId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.stage = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.grade = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = SoulSimpleMessage_AttrMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.attrMap[entry5.key] = entry5.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulSimpleMessage>, I>>(base?: I): SoulSimpleMessage {
    return SoulSimpleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulSimpleMessage>, I>>(object: I): SoulSimpleMessage {
    const message = createBaseSoulSimpleMessage();
    message.id = object.id ?? 0;
    message.soulTemplateId = object.soulTemplateId ?? 0;
    message.stage = object.stage ?? 0;
    message.grade = object.grade ?? 0;
    message.attrMap = Object.entries(object.attrMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseSoulSimpleMessage_AttrMapEntry(): SoulSimpleMessage_AttrMapEntry {
  return { key: 0, value: 0 };
}

export const SoulSimpleMessage_AttrMapEntry: MessageFns<SoulSimpleMessage_AttrMapEntry> = {
  encode(message: SoulSimpleMessage_AttrMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoulSimpleMessage_AttrMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoulSimpleMessage_AttrMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SoulSimpleMessage_AttrMapEntry>, I>>(base?: I): SoulSimpleMessage_AttrMapEntry {
    return SoulSimpleMessage_AttrMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoulSimpleMessage_AttrMapEntry>, I>>(
    object: I,
  ): SoulSimpleMessage_AttrMapEntry {
    const message = createBaseSoulSimpleMessage_AttrMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTitleMessage(): TitleMessage {
  return { titleId: 0, expireTime: 0, chosen: false };
}

export const TitleMessage: MessageFns<TitleMessage> = {
  encode(message: TitleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.titleId !== 0) {
      writer.uint32(8).int64(message.titleId);
    }
    if (message.expireTime !== 0) {
      writer.uint32(16).int64(message.expireTime);
    }
    if (message.chosen !== false) {
      writer.uint32(24).bool(message.chosen);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TitleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTitleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.titleId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.expireTime = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.chosen = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TitleMessage>, I>>(base?: I): TitleMessage {
    return TitleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TitleMessage>, I>>(object: I): TitleMessage {
    const message = createBaseTitleMessage();
    message.titleId = object.titleId ?? 0;
    message.expireTime = object.expireTime ?? 0;
    message.chosen = object.chosen ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
