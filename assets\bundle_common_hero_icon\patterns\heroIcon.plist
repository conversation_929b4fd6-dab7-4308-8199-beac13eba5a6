<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>S0202.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{522,82}</string>
                <key>spriteSourceSize</key>
                <string>{522,82}</string>
                <key>textureRect</key>
                <string>{{1781,1},{522,82}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S0202_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,0}</string>
                <key>spriteSize</key>
                <string>{277,47}</string>
                <key>spriteSourceSize</key>
                <string>{279,47}</string>
                <key>textureRect</key>
                <string>{{1,1051},{277,47}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0249.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{41,197}</string>
                <key>spriteSourceSize</key>
                <string>{41,197}</string>
                <key>textureRect</key>
                <string>{{1,1100},{41,197}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S0250.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{25,25}</string>
                <key>spriteSourceSize</key>
                <string>{25,25}</string>
                <key>textureRect</key>
                <string>{{1751,494},{25,25}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0305.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,3}</string>
                <key>spriteSize</key>
                <string>{338,491}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{735,520},{338,491}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0307.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,3}</string>
                <key>spriteSize</key>
                <string>{336,491}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{1443,1},{336,491}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0309.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,3}</string>
                <key>spriteSize</key>
                <string>{338,491}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{1103,1},{338,491}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0311.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,3}</string>
                <key>spriteSize</key>
                <string>{336,491}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{1075,520},{336,491}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0313.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,3}</string>
                <key>spriteSize</key>
                <string>{336,491}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{1413,494},{336,491}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0366.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{366,523}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{1,1},{366,523}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0367.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,3}</string>
                <key>spriteSize</key>
                <string>{364,517}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{369,520},{364,517}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0368.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{366,523}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{1,526},{366,523}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0369.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,3}</string>
                <key>spriteSize</key>
                <string>{366,517}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{369,1},{366,517}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0370.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,3}</string>
                <key>spriteSize</key>
                <string>{364,517}</string>
                <key>spriteSourceSize</key>
                <string>{366,523}</string>
                <key>textureRect</key>
                <string>{{737,1},{364,517}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S1269.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{33,151}</string>
                <key>spriteSourceSize</key>
                <string>{33,151}</string>
                <key>textureRect</key>
                <string>{{1751,904},{33,151}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S1270.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{33,151}</string>
                <key>spriteSourceSize</key>
                <string>{33,151}</string>
                <key>textureRect</key>
                <string>{{1751,939},{33,151}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S1271.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{33,151}</string>
                <key>spriteSourceSize</key>
                <string>{33,151}</string>
                <key>textureRect</key>
                <string>{{369,1039},{33,151}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S1272.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{33,151}</string>
                <key>spriteSourceSize</key>
                <string>{33,151}</string>
                <key>textureRect</key>
                <string>{{522,1039},{33,151}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S1273.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{33,151}</string>
                <key>spriteSourceSize</key>
                <string>{33,151}</string>
                <key>textureRect</key>
                <string>{{1971,366},{33,151}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jn_wujiang_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{104,104}</string>
                <key>spriteSourceSize</key>
                <string>{104,104}</string>
                <key>textureRect</key>
                <string>{{1865,366},{104,104}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jn_wujiang_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{104,104}</string>
                <key>spriteSourceSize</key>
                <string>{104,104}</string>
                <key>textureRect</key>
                <string>{{1822,974},{104,104}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jn_wujiang_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{104,104}</string>
                <key>spriteSourceSize</key>
                <string>{104,104}</string>
                <key>textureRect</key>
                <string>{{1900,519},{104,104}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jn_wujiang_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{104,104}</string>
                <key>spriteSourceSize</key>
                <string>{104,104}</string>
                <key>textureRect</key>
                <string>{{1900,625},{104,104}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jn_wujiang_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{104,104}</string>
                <key>spriteSourceSize</key>
                <string>{104,104}</string>
                <key>textureRect</key>
                <string>{{1900,731},{104,104}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>pingzhi_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-4,-7}</string>
                <key>spriteSize</key>
                <string>{123,363}</string>
                <key>spriteSourceSize</key>
                <string>{147,377}</string>
                <key>textureRect</key>
                <string>{{735,1013},{123,363}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>pingzhi_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-2,-1}</string>
                <key>spriteSize</key>
                <string>{129,357}</string>
                <key>spriteSourceSize</key>
                <string>{147,377}</string>
                <key>textureRect</key>
                <string>{{1100,1013},{129,357}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>pingzhi_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,3}</string>
                <key>spriteSize</key>
                <string>{139,363}</string>
                <key>spriteSourceSize</key>
                <string>{147,377}</string>
                <key>textureRect</key>
                <string>{{1865,1},{139,363}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>pingzhi_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,8}</string>
                <key>spriteSize</key>
                <string>{147,361}</string>
                <key>spriteSourceSize</key>
                <string>{147,377}</string>
                <key>textureRect</key>
                <string>{{1459,987},{147,361}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>pingzhi_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{147,377}</string>
                <key>spriteSourceSize</key>
                <string>{147,377}</string>
                <key>textureRect</key>
                <string>{{1751,525},{147,377}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>heroIcon.png</string>
            <key>size</key>
            <string>{2005,1143}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:9ef89911f9aab5073c52fb86b94a231d:4c07fd075e3cba27124221217a46d596:bd2a985a614f37a26e549e6e03cf4027$</string>
            <key>textureFileName</key>
            <string>heroIcon.png</string>
        </dict>
    </dict>
</plist>
