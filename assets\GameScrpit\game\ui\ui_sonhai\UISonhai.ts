import {
  _decorator,
  animation,
  Animation,
  Component,
  Input,
  isValid,
  Label,
  Node,
  ProgressBar,
  sp,
  Sprite,
  tween,
  v3,
} from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { sonhai_achieveId, sonhai_activityId, SonhaiModule } from "../../../module/sonhai/SonhaiModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ToolExt from "../../common/ToolExt";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import { SonhaiRouteName } from "../../../module/sonhai/SonhaiRoute";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import MsgMgr from "../../../lib/event/MsgMgr";
import { SonhaiAudioName, SonhaiMsgEnum } from "../../../module/sonhai/SonhaiConfig";
import MsgEnum from "../../event/MsgEnum";
import { AdventureDrawResponse } from "../../net/protocol/Activity";
import { PlayerModule } from "../../../module/player/PlayerModule";
import Formate from "../../../lib/utils/Formate";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import StorageMgr, { StorageKeyEnum } from "db://assets/platform/src/StorageHelper";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { AdventureVO, AdventureSmallPrizeSubVO } from "../../../module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UISonhai")
export class UISonhai extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_SONHAI}?prefab/ui/UISonhai`;
  }
  private _draw_tickerId_list: number[] = [];
  private _tickList: number[] = [];

  private _animation_block_tickerId: number = null;

  private _guide_tickerId: number = null;

  private _sonhaidb: AdventureVO = null;

  private _smallNodeMap: Map<number, Node> = new Map();

  /**10次 == 0 就是不10次 其余数字为10次 */
  private isTen: number = 0;

  protected onRegEvent(): void {
    MsgMgr.on(SonhaiMsgEnum.BIG_AWARD_ID_CHANGE, this.setBigAward, this);
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setNeedItemNum, this);
    MsgMgr.on(SonhaiMsgEnum.TOTAL_DRAW_COUNT_CHANGE, this.setBigAward, this);
    MsgMgr.on(SonhaiMsgEnum.GUIDE_CLICK_DRAW, this.loadGuideChouJiang, this);
    MsgMgr.on(SonhaiMsgEnum.SONHAI_LEIJI_REFRESH, this.setLeiJiAward, this);

    BadgeMgr.instance.setBadgeId(this.getNode("btn_xslb"), BadgeType.UITerritory.btn_fd_shtx.btn_xslb.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_shtx_renwu"), BadgeType.UITerritory.btn_fd_shtx.btn_shtx_renwu.id);
    BadgeMgr.instance.setBadgeId(
      this.getNode("leiji_award_item"),
      BadgeType.UITerritory.btn_fd_shtx.leiji_award_item.id
    );
    BadgeMgr.instance.setBadgeId(this.getNode("xiezhi"), BadgeType.UITerritory.btn_fd_shtx.xiezhi.id);
  }

  protected onDelEvent(): void {
    MsgMgr.off(SonhaiMsgEnum.BIG_AWARD_ID_CHANGE, this.setBigAward, this);
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setNeedItemNum, this);
    MsgMgr.off(SonhaiMsgEnum.TOTAL_DRAW_COUNT_CHANGE, this.setBigAward, this);
    MsgMgr.off(SonhaiMsgEnum.GUIDE_CLICK_DRAW, this.loadGuideChouJiang, this);
    MsgMgr.off(SonhaiMsgEnum.SONHAI_LEIJI_REFRESH, this.setLeiJiAward, this);
  }
  protected async onEvtShow() {
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    log.log("db=====", db);
    this.isTen = StorageMgr.loadNum(StorageKeyEnum.山海探险10次);
    this.setTen_gou();

    this._sonhaidb = db;
    this.acTimeShow();
    this.getNode("animation_block").active = false;
    this.getNode("guideLayer").setPosition(v3(0, 0, 0));
    this.getNode("guideLayer").active = true;
    this.setNeedIcon();
    this.setNeedItemNum();

    this.setBigAward();

    this.setSmallAward();

    this.setLeiJiAward();

    this.getNode("xiezhi").getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle2", true);

    TickerMgr.setTimeout(0.3, this.check_big_award.bind(this));
  }

  private acTimeShow() {
    let startTime = TimeUtils.formatTimestamp(this._sonhaidb.startTime, "MM/DD HH:mm:ss");
    let publicityTime = TimeUtils.formatTimestamp(this._sonhaidb.publicityTime, "MM/DD HH:mm:ss");
    this.getNode("ac_time").getComponent(Label).string = "活动时间:" + startTime + "--" + publicityTime;
  }

  private setNeedIcon() {
    ToolExt.setItemIcon(this.getNode("needItem_icon"), this._sonhaidb.costList[0], this);
  }

  private setNeedItemNum() {
    let id = this._sonhaidb.costList[0];
    let num = PlayerModule.data.getItemNum(id);
    this.getNode("needItem_num").getComponent(Label).string = Formate.format(num);
  }

  /**设置累计大奖 */
  private setLeiJiAward() {
    let count = SonhaiModule.data.getAchieveMap(sonhai_achieveId).targetVal;
    let achieveTakeList = SonhaiModule.data.getAchieveMap(sonhai_achieveId).basicTakeList;
    let index = achieveTakeList[achieveTakeList.length - 1] + 1;
    if (achieveTakeList.length == 0) {
      index = 0;
    }
    let achieveVO = null;
    for (let i = 0; i < this._sonhaidb.achieveVOList.length; i++) {
      if (this._sonhaidb.achieveVOList[i].id == sonhai_achieveId) {
        achieveVO = this._sonhaidb.achieveVOList[i];
        break;
      }
    }

    let info = achieveVO.basicRewardList[index];
    FmUtils.setItemNode(this.getNode("leiji_award_item"), info[0], info[1]);

    let require = achieveVO.requireList[index];
    if (count >= require) {
      this.award_snake(this.getNode("leiji_award_item"), true);
    } else {
      this.award_snake(this.getNode("leiji_award_item"), false);
    }
  }

  private award_snake(targetNode: Node, is: boolean) {
    // 获取目标节点的初始位置
    const nodeStartPos = targetNode.getPosition();
    if (!targetNode["nodeStartPos"]) {
      targetNode["nodeStartPos"] = nodeStartPos;
    }
    let ani = targetNode.getComponent(Animation);
    if (is == false) {
      ani.stop();
      targetNode.setRotation(0, 0, 0, 0);
      return;
    }
    targetNode.setRotation(0, 0, 0, 0);
    ani.play("ani_dou");
  }

  /**设置小奖 */
  private async setSmallAward() {
    let subList: Array<AdventureSmallPrizeSubVO> = this._sonhaidb.smallPrizeVO.subList;
    for (let i = 0; i < subList.length; i++) {
      let node = this.getNode("small_list").children[i];
      let info: AdventureSmallPrizeSubVO = subList[i];
      FmUtils.setItemNode(node.getChildByPath("ani_layer/Item"), info.rewardList[0], info.rewardList[1]);
      this._smallNodeMap.set(info.id, node);

      node.on(
        Input.EventType.TOUCH_START,
        () => {
          AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击道具图标);
        },
        this
      );

      let id = TickerMgr.setTimeout(Math.random(), () => {
        if (isValid(this.node) == false) {
          return;
        }
        node.getChildByPath("ani_layer/shang_hai_pao_light").active = true;
        node.getChildByPath("ani_layer/shang_hai_pao_light").getComponent(sp.Skeleton).timeScale = 0.6;
        node
          .getChildByPath("ani_layer/shang_hai_pao_light")
          .getComponent(sp.Skeleton)
          .setAnimation(0, "animation", true);
      });
      let animation = node.getComponent(Animation);
      let index = Math.round(Math.random());
      let name = animation.clips[index].name;
      animation.play(name);
      animation.getState(name).setTime(Math.random() * 2);
      this._tickList.push(id);
    }
  }

  //设置大奖
  private async setBigAward() {
    let bigPrizeVO = this._sonhaidb.bigPrizeVO;
    let round = bigPrizeVO.round;

    let curCostCount = SonhaiModule.data.curCostCount;

    this.getNode("lbl_big_award_bar").getComponent(Label).string = curCostCount + "/" + round;
    this.getNode("big_award_progress").getComponent(ProgressBar).progress = curCostCount / round;

    let curBigPrizeId = SonhaiModule.data.curBigPrizeId;
    if (curBigPrizeId <= 0) {
      this.getNode("btn_pich_big_award").getComponent(Sprite).enabled = true;
      this.getNode("big_award_item").active = false;
    } else {
      this.getNode("btn_pich_big_award").getComponent(Sprite).enabled = false;
      this.getNode("big_award_item").active = true;
      let info = await SonhaiModule.service.getBigPrizeBubInfo(curBigPrizeId);
      let id = info.rewardList[0];
      let num = info.rewardList[1];
      FmUtils.setItemNode(this.getNode("big_award_item"), id, num);
      this.getNode("big_award_item")["drawItemId"] = info.id;
    }
  }

  private check_big_award() {
    let curBigPrizeId = SonhaiModule.data.curBigPrizeId;
    if (curBigPrizeId <= 0) {
      this.loadGuide(this.getNode("big_award_item"));
    }
  }

  private loadGuideChouJiang() {
    this.loadGuide(this.getNode("xiezhi"));
  }

  private loadGuide(node: Node) {
    if (this._guide_tickerId) {
      TickerMgr.carryTicker(this._guide_tickerId);
      this._guide_tickerId = null;
    }

    let pos = ToolExt.transferOfAxes(node, this.getNode("guideLayer"));
    let guide_skt = ToolExt.clone(this.getNode("guide_skt"), this);

    this.getNode("guideLayer").addChild(guide_skt);
    guide_skt.active = true;

    guide_skt.setPosition(pos);
    guide_skt.getComponent(sp.Skeleton).setAnimation(0, "animation", true);

    this._guide_tickerId = TickerMgr.setTimeout(3, () => {
      guide_skt.destroy();
      this._guide_tickerId = null;
    });
  }

  private on_click_btn_pich_big_award() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(SonhaiRouteName.UISonhaiBigAward);
  }
  private on_click_btn_shtx_gailv() {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击左侧获奖概率活动任务);
    UIMgr.instance.showDialog(SonhaiRouteName.UISonhaiAwarrdRate);
  }

  private on_click_btn_shtx_renwu() {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击左侧获奖概率活动任务);
    UIMgr.instance.showDialog(SonhaiRouteName.UISonhaiTask);
  }

  private on_click_btn_leiji() {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击右上方累计大奖);
    UIMgr.instance.showDialog(SonhaiRouteName.UISonhaiLeiJi);
  }

  private on_click_btn_help() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 15 });
  }

  private on_click_btn_adventureDraw() {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击图标进行探险);
    let id = this._sonhaidb.costList[0];
    let num = PlayerModule.data.getItemNum(id);

    if (num <= 0) {
      UIMgr.instance.showDialog(SonhaiRouteName.UISonhaiXslb);
      //UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: id });
      return;
    }

    if (SonhaiModule.data.curBigPrizeId <= 0) {
      this.loadGuide(this.getNode("big_award_item"));
      TipMgr.showTip("请先选中大奖");
      return;
    }

    this.getNode("animation_block").active = true;
    this._animation_block_tickerId = TickerMgr.setTimeout(3, () => {
      this.getNode("animation_block").active = false;
      TipMgr.showTip("网络请求超时");
    });

    let bool = false;

    if (this.isTen != 0) {
      bool = true;
    }

    let param = {
      /** 活动ID */
      activityId: sonhai_activityId,
      /** 是否十连抽奖 */
      tenth: bool,
    };

    SonhaiModule.api.adventureDraw(param, (res: AdventureDrawResponse) => {
      log.log("抽奖回调=============", res);
      if (this._animation_block_tickerId) {
        TickerMgr.clearTicker(this._animation_block_tickerId);
      }
      //MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
      this.drawAni(res.drawItemList, res.rewardList);
      //TipMgr.showTip("后续补动画，火球射击道具id为" + res.drawItemId + "的道具");
    });
  }

  private drawAni(drawItemList: Array<number>, rewardList: Array<number> = []) {
    this.getNode("xiezhi").getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_attack2", false);
    this.getNode("xiezhi")
      .getChildByName("render")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry) => {
        if (trackEntry.animation.name == "boss_attack2") {
          this.getNode("xiezhi").getChildByName("render").getComponent(sp.Skeleton).setCompleteListener(null);

          for (let i = 0; i < drawItemList.length; i++) {
            let id = TickerMgr.setTimeout(0.035, () => {
              let bullet = ToolExt.clone(this.getNode("feidan"), this);
              bullet.setParent(this.getNode("guadian"));
              bullet.setPosition(v3(0, 0, 0));
              bullet.active = true;
              if (i == drawItemList.length - 1) {
                this.goItemBullet(bullet, drawItemList[i], rewardList, true);
                this.getNode("xiezhi")
                  .getChildByName("render")
                  .getComponent(sp.Skeleton)
                  .setAnimation(0, "boss_idle2", true);
              } else {
                this.goItemBullet(bullet, drawItemList[i], rewardList, false);
              }
            });
            this._draw_tickerId_list.push(id);
          }
        }
      });
  }

  private goItemBullet(bullet: Node, drawItemId: number, rewardList: Array<number> = [], bool: boolean) {
    let item = null;

    if (drawItemId == this.getNode("big_award_item")["drawItemId"]) {
      item = this.getNode("big_award_item");
    } else {
      item = this._smallNodeMap.get(drawItemId);
    }

    if (!item) {
      if (bool) {
        this.getNode("animation_block").active = false;
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
      }

      return;
    }

    let newPos = ToolExt.transferOfAxes(bullet, this.getNode("small_list"));
    bullet.setParent(this.getNode("small_list"));
    bullet.setPosition(newPos);

    let angle = this.calculateAngle(
      bullet.getPosition().x,
      bullet.getPosition().y,
      item.getPosition().x,
      item.getPosition().y
    );

    tween(bullet)
      .parallel(
        tween(bullet)
          .by(0.05, { position: v3(0, 55, 0), angle: angle })
          .start()
      )
      .to(0.5, { position: item.getPosition() })
      .call(() => {
        bullet.getChildByName("render").active = false;
        bullet.getChildByName("daoju_boom").active = true;
        bullet.getChildByName("daoju_boom").getComponent(sp.Skeleton).setAnimation(0, "animation", false);

        bullet
          .getChildByName("daoju_boom")
          .getComponent(sp.Skeleton)
          .setCompleteListener((trackEntry) => {
            if (trackEntry.animation.name == "animation") {
              bullet.getChildByName("daoju_boom").getComponent(sp.Skeleton).setCompleteListener(null);
              bullet.destroy();

              if (bool) {
                this.getNode("animation_block").active = false;
                MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
              }
            }
          });
      })
      .start();
  }

  private on_click_btn_xslb() {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击左下方限时礼包山海商店);
    UIMgr.instance.showDialog(SonhaiRouteName.UISonhaiXslb);
  }

  private on_click_btn_shsd() {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击左下方限时礼包山海商店);
    UIMgr.instance.showDialog(SonhaiRouteName.UISonhaiSd);
  }

  private on_click_btn_back() {
    AudioMgr.instance.playEffect(520);
    UIMgr.instance.back();
  }

  private on_click_btn_add_item() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let id = this._sonhaidb.costList[0];
    UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: id });
  }

  on_click_btn_ten() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this.isTen == 0) {
      this.isTen = 1;
    } else {
      this.isTen = 0;
    }
    StorageMgr.saveItem(StorageKeyEnum.山海探险10次, this.isTen + "");
    this.setTen_gou();
  }

  private setTen_gou() {
    if (this.isTen == 0) {
      this.getNode("ten_gou").active = false;
    } else {
      this.getNode("ten_gou").active = true;
    }
  }

  calculateAngle(x1, y1, x2, y2) {
    var deltaX = x2 - x1;
    var deltaY = y2 - y1;
    return (Math.atan2(deltaY, deltaX) * 180) / Math.PI;
  }

  protected onEvtClose(): void {
    this._draw_tickerId_list.forEach((val) => {
      TickerMgr.clearTimeout(val);
    });
    this._tickList.forEach((val) => {
      TickerMgr.clearTimeout(val);
    });
  }
}
