import { _decorator, instantiate, Label, Node, Sprite } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PupilMarryltemViewHolder } from "./PupilMarryltemViewHolder";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class PupilMarryAdapter extends ListAdapter {
  // 常规节点
  item1: Node;

  constructor(item1: Node) {
    super();
    this.item1 = item1;
  }

  // 所有缓存节点
  datas: any[] = [];
  setDatas(data: any[]) {
    if (!data) {
      return;
    }
    log.warn(data);
    this.datas = data;
    this.notifyDataSetChanged();
  }

  getCount(): number {
    return this.datas.length;
  }

  onCreateView(viewType: number): Node {
    let item: Node;
    item = instantiate(this.item1);
    item.getComponent(PupilMarryltemViewHolder).init();
    item.active = true;
    return item;
  }

  onBindData(node: Node, position: number): void {
    let itemData = this.datas[position];
    node.getComponent(PupilMarryltemViewHolder).updateData(position, itemData);
  }
}
