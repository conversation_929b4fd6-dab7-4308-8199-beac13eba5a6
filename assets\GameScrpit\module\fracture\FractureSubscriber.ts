import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import {
  FractureLogAssistResponse,
  FractureRoadAssistResponse,
  RedeemBuyMessage,
} from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import { FRACTURE_ACTIVITYID } from "./FractureConstant";
import { FractureModule } from "./FractureModule";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
export class FractureSubscriber {
  private redeemBuyCallback(data: RedeemBuyMessage) {
    //
    if (data.activityId == FRACTURE_ACTIVITYID) {
      FractureModule.data.fractureData.redeemMap = data.redeemMap;
      MsgMgr.emit(MsgEnum.ON_FRACTURE_BUY_UPDATE, data);
    }
  }
  private fractureLogAssistCallback(data: FractureLogAssistResponse) {
    //
    // 新增属性检测逻辑
    const moduleMap = FractureModule.data.fractureData.remainFloorCountMap;
    const responseMap = data.remainFloorCountMap;

    // 找出新增的楼层ID
    const extraFloorIds = Object.keys(responseMap).filter(
      (key) => !Object.prototype.hasOwnProperty.call(moduleMap, key)
    );

    if (extraFloorIds.length > 0) {
      log.log("新增楼层ID:", extraFloorIds);
      // 实际项目中这里可以触发相关业务逻辑
      MsgMgr.emit(MsgEnum.ON_FRACTURE_FLOOR_UNLOCK, extraFloorIds);
    }
    log.log("协助信息", data);
    // 更新本地数据
    FractureModule.data.fractureData.remainFloorCountMap = responseMap;
    MsgMgr.emit(MsgEnum.ON_FRACTURE_UPDATE, data);
    TipsMgr.topRouteCtrl.showPrefab(BundleEnum.BUNDLE_G_FRACTURE, "prefab/ui/TopFractureHelpTips", {
      playerName: data?.assistUserMessage?.nickname,
    });
  }

  private fractureRoadAssistCallback(data: FractureRoadAssistResponse) {
    // 当路障被协助后更新数据
    log.log("路障协助信息", data);
    FractureModule.api.fractureInfo(() => {
      MsgMgr.emit(MsgEnum.ON_FRACTURE_UPDATE, data);
    });
    TipsMgr.topRouteCtrl.showPrefab(BundleEnum.BUNDLE_G_FRACTURE, "prefab/ui/TopFractureHelpTips", {
      playerName: data?.assistUserMessage?.nickname,
    });
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(RedeemBuyMessage, ActivityCmd.RedeemBuyMessage, this.redeemBuyCallback);
    ApiHandler.instance.subscribe(
      FractureLogAssistResponse,
      ActivityCmd.FractureLogAssist,
      this.fractureLogAssistCallback
    );
    ApiHandler.instance.subscribe(
      FractureRoadAssistResponse,
      ActivityCmd.FractureRoadAssist,
      this.fractureRoadAssistCallback
    );
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ActivityCmd.RedeemBuyMessage, this.redeemBuyCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.FractureLogAssist, this.fractureLogAssistCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.FractureRoadAssist, this.fractureRoadAssistCallback);
  }
}
