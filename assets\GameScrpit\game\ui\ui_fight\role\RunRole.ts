import { _decorator, instantiate, Node, Prefab, sp, tween, v3 } from "cc";
import { JsonMgr } from "../../../mgr/JsonMgr";
import ResMgr from "../../../../lib/common/ResMgr";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { UIOpacity } from "cc";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { FightAudioName } from "db://assets/GameScrpit/module/fight/src/FightConfig";
const { ccclass, property } = _decorator;
export enum roleState {
  NULL = "null",
  IN = "in",
  IDLE = "idle",
  ATTACK = "attack",
}

@ccclass("runRole")
export class RunRole extends Node {
  protected _res_prefab: Prefab;
  // 冲击波
  protected _chongjibo_prefab: Prefab;

  protected _skinId: number;
  protected _roleSpineId: number;
  protected _db1;

  protected _role: Node;
  protected _renderSkt: sp.Skeleton;

  protected _node_congjibo: Node;

  protected _node_yan_wu: Node;

  protected _state: roleState = null;
  public get state() {
    return this._state;
  }
  public constructor(args = null) {
    super();
    this._skinId = args.skinId || 0;
    this._roleSpineId = args.id || 0;
    this._db1 = args.db1 || null;
  }

  public loadRenderNode() {
    let db = JsonMgr.instance.jsonList.c_spineShow[this._roleSpineId];
    ResMgr.loadPrefab(db.prefabPath, (prefab) => {
      if (this.isValid == false) {
        return;
      }
      this._res_prefab = prefab;
      this._res_prefab.addRef();
      this._role = instantiate(prefab);
      this.addChild(this._role);
      this.walk((child) => (child.layer = this.layer));

      this._role.setScale(v3(this._db1.renderScale3[0], this._db1.renderScale3[1], this._db1.renderScale3[2]));
      this._renderSkt = this._role.getChildByName("render").getComponent(sp.Skeleton);

      ResMgr.loadPrefab(`${BundleEnum.BUNDLE_G_FIGHT}?prefab/PbChongJiBo`, (prefab) => {
        if (this.isValid == false) {
          return;
        }
        this._chongjibo_prefab = prefab;
        this._chongjibo_prefab.addRef();
        this._node_congjibo = instantiate(prefab);
        this.addChild(this._node_congjibo);

        this._node_congjibo.setPosition(0, 104);
        this._node_congjibo.active = false;
        this.roleStateIdle();

        this._node_congjibo.walk((child) => (child.layer = this.layer));
      });

      if (db.showYanWu === 1) {
        ResMgr.loadPrefab(`${BundleEnum.BUNDLE_G_FIGHT}?prefab/RunYanWu`, (pb: Prefab) => {
          if (this.isValid == false) {
            return;
          }
          this._node_yan_wu = instantiate(pb);
          this._node_yan_wu.active = false;
          this.addChild(this._node_yan_wu);
        });
      }
    });
  }

  public playRun() {
    this.roleStateAttack();
  }

  public playWalk() {
    this.roleStateIdle();
  }

  /**
   * 攻击
   */
  public attack() {
    const uiOpacity = this._node_congjibo.getChildByName("spine_chongjibo").getComponent(UIOpacity);
    tween(uiOpacity).to(0.3, { opacity: 192 }).to(0.2, { opacity: 64 }).start();

    let nodeAttackLight = this._node_congjibo.getChildByName("spine_shouji");
    nodeAttackLight.active = true;

    const spine = nodeAttackLight.getComponent(sp.Skeleton);
    spine.setAnimation(0, "animation", false);
  }

  protected roleStateIdle() {
    AudioMgr.instance.playEffect(FightAudioName.Effect.跑步);
    this._state = roleState.IDLE;
    if (this._node_yan_wu) {
      this._node_yan_wu.active = false;
    }
    let db = JsonMgr.instance.jsonList.c_spineShow[this._roleSpineId];
    this._renderSkt.timeScale = db.gameTimeScale[0];
    this._renderSkt.setAnimation(0, "run1", true);

    // 渐隐冲击波
    const uiOpacity = this._node_congjibo.getChildByName("spine_chongjibo").getComponent(UIOpacity);
    tween(uiOpacity).to(0.6, { opacity: 0 }).start();
  }

  protected roleStateAttack() {
    let db1 = JsonMgr.instance.jsonList.c_leaderSkin[this._skinId];
    let musicId = db1.music;
    AudioMgr.instance.playEffect(musicId);

    this._state = roleState.ATTACK;
    if (this._node_yan_wu) {
      this._node_yan_wu.active = true;
    }
    let db = JsonMgr.instance.jsonList.c_spineShow[this._roleSpineId];
    this._renderSkt.timeScale = db.gameTimeScale[1];
    this._renderSkt.setAnimation(0, "run2", true);

    if (this._node_congjibo) {
      this._node_congjibo.active = true;
    }

    // 渐显冲击波
    const uiOpacity = this._node_congjibo.getChildByName("spine_chongjibo").getComponent(UIOpacity);
    tween(uiOpacity).to(0.3, { opacity: 64 }).start();
  }

  public onRemove() {
    if (this._res_prefab) {
      this._res_prefab.decRef();
    }
  }
}
