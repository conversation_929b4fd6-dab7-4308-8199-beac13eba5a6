export interface ConfigSoulRecord {
  id: number;
  /**名称 */
  name: string;
  /**品质 */
  color: number;
  /**阶级上限 */
  max: number;
  /**每阶升级消耗 */
  costList: number[];
  /**初始属性 */
  firstAttrList: number[][];
  /**升级属性增加 */
  addAttrList: number[][];
  /** 初始主属性权重*/
  firstRateList: number[];
  /**随机到主属性时两个权重增加 */
  addRateList: number[];
  /** 初始主属性*/
  firstMasterAttrList: number[];
  /**副属性集合 */
  sunAttrList: number[];
  /**进阶主副属性增加 */
  addMasterSonAttrList: number[];
  /**放生获得初始升级道具 */
  freeRewardList: number[];
  /**价格购买 */
  priceList: number[];
  /**免费刷新概率 */
  freeRefreshRate: number;
  /** 高级刷新概率*/
  costRefreshRate: number;
  /**每日免费刷新次数 */
  freeRefreshMax: number;
  /**每日高级刷新次数 */
  costRefreshMax: number;
  /**高级刷新消耗 */
  costRefreshCostList: number[];
  /**升级道具ID */
  costId: number;
  /**刷出金魂的保底次数 */
  min: number;
  /**放生返还比例 */
  returnRate: number;
  /**初始槽位数量 */
  firstPlace: number;
  /**槽位上限 */
  maxPlace: number;
  /**解锁槽位消耗 */
  costPlaceList: number[];
}
