import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ler<PERSON><PERSON>, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ChatPackAddRequest, ChatPackFindRequest, ChatPackMessage } from "../../game/net/protocol/Chat";
import { ChatSubCmd } from "./ChatConstant";
import { ChatModule } from "./ChatModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ChatApi {
  public messageList(
    start: number,
    end: number,
    marketType: number,
    success?: ApiHandlerSuccess,
    error?: ApiHandlerFail
  ) {
    let data: ChatPackFindRequest = {
      start: start,
      end: end,
      marketType: marketType,
    };
    ApiHandler.instance.list(
      ChatPackMessage,
      ChatSubCmd.messageList,
      ChatPackFindRequest.encode(data),
      (data: ChatPackMessage[]) => {
        //
        ChatModule.data.setMarketMessage(marketType, data);
        success && success(data);
      }
    );
  }
  public addMessage(
    marketType: number,
    messageType: number,
    content: string,
    mentionedUsers: number[],
    success?: ApiHandlerSuccess,
    error?: ApiHandlerFail
  ) {
    let data: ChatPackAddRequest = {
      marketType: marketType,
      messageType: messageType,
      content: content,
      mentionedUsers: mentionedUsers,
    };
    ApiHandler.instance.request(
      ChatPackMessage,
      ChatSubCmd.addMessage,
      ChatPackAddRequest.encode(data),
      (data: ChatPackMessage) => {
        //
        ChatModule.data.addNewMessage(data.marketType, data);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        error && error(errorCode, msg, data);
        return true;
      }
    );
  }

  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }
}
