[{"__type__": "cc.Prefab", "_name": "UICityDetail", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UICityDetail", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 20}, {"__id__": 511}, {"__id__": 535}, {"__id__": 559}, {"__id__": 621}, {"__id__": 629}, {"__id__": 645}, {"__id__": 653}, {"__id__": 895}], "_active": true, "_components": [{"__id__": 903}, {"__id__": 905}, {"__id__": 907}], "_prefab": {"__id__": 909}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "nodetop", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}], "_active": true, "_components": [{"__id__": 15}, {"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_top_9", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": 2, "y": 775.8780000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdwRFCXvJLAKiR601s/Xww"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cf78fec6-6c36-4490-bada-d546757d9f16@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bar5MUJrtGt4W35acxckJL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36KvQ1RzJPb6xU5ZU4xSEZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "JZSJguanquan", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 10}, {"__id__": 12}], "_prefab": {"__id__": 14}, "_lpos": {"__type__": "cc.Vec3", "x": 2, "y": 364.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 11}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 823}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9buZjHaDVLXqBvFXfj0qgH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 13}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "78e1175f-1957-41ea-8c69-a3d24d20fba8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77AptuxD9IdrOLN8llbEki"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0fKl3It8NChYnNFiyWRQ76", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 16}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "867zxx/NJKm6RhY8fmDpZ9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 18}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 700, "_bottom": 700, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe5P3ozsxALp7qgOKwXXP9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "77n8c/jqFLNpJMM1LPo5YZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_bottom", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 21}, {"__id__": 35}, {"__id__": 161}, {"__id__": 169}, {"__id__": 203}, {"__id__": 231}, {"__id__": 241}, {"__id__": 337}, {"__id__": 426}], "_active": true, "_components": [{"__id__": 504}, {"__id__": 506}, {"__id__": 508}], "_prefab": {"__id__": 510}, "_lpos": {"__type__": "cc.Vec3", "x": 2, "y": 56.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_zhensh<PERSON>wujiang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 22}], "_active": true, "_components": [{"__id__": 28}, {"__id__": 30}, {"__id__": 32}], "_prefab": {"__id__": 34}, "_lpos": {"__type__": "cc.Vec3", "x": -2, "y": -241.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lab", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 23}, {"__id__": 25}], "_prefab": {"__id__": 27}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0.452, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 24}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 38.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cr7NOnEpDzIN4tfXlYSZO"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 26}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "镇守武将", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 72, "g": 107, "b": 146, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3d91G2P5lOHpCcUcHSNjVX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbwvjFDAlI6KxLZ/zQ45dh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 29}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9whJk1PZB3r2uDqXXAwQT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 31}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "87337177-a03b-4048-a0e0-9ab2ddb80345@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23E3xtMn1CYJsAj3Zhu89K"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": false, "__prefab": {"__id__": 33}, "_alignFlags": 4, "_target": null, "_left": 0.006666666666666667, "_right": 0, "_top": 0, "_bottom": 544, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 8, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0TEcQ+sFEmaW6zxSIkHWj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "368ksQUQJFOKtiTktUCFaW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_hero", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 36}, {"__id__": 60}, {"__id__": 84}, {"__id__": 108}, {"__id__": 132}], "_active": true, "_components": [{"__id__": 156}, {"__id__": 158}], "_prefab": {"__id__": 160}, "_lpos": {"__type__": "cc.Vec3", "x": 7.533999999999992, "y": -121.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_hero_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 35}, "_children": [{"__id__": 37}, {"__id__": 51}], "_active": true, "_components": [{"__id__": 57}], "_prefab": {"__id__": 59}, "_lpos": {"__type__": "cc.Vec3", "x": -269.976, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_hero", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [{"__id__": 38}], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}, {"__id__": 48}], "_prefab": {"__id__": 50}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "image", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 37}, "_children": [], "_active": true, "_components": [{"__id__": 39}, {"__id__": 41}], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": -3, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 40}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eZP5Ezp5JNqqDDwfvRTd6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 42}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81ggq1+apE24ctvGREu/1N"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a9zkZtQrRPVqmkV7hn2p0G", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 45}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17YlYK50VLKISgkXygc2Hz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 47}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-42e9-ae29-d04abbf58c77@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7aDw0+f1GhrR+FGf7L2SN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 49}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19LXoDRYhNF6a/XCN4/dPT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6snlXXCVKS6unWFCpOYI1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_add", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 54}], "_prefab": {"__id__": 56}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -62.928, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 53}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 58.239999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6J/aZgoFOI75aP6TkxUsA"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 55}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "繁荣度\n+15%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1KcTxdFxKeL9jwPE1xlif"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cHDQ9kPdPRaZNH86TBArO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 58}, "_contentSize": {"__type__": "cc.Size", "width": 102, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86ygB0ZORHHbEKjiUmVeFA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0b4DBVAedKdKf2zWqDYpXO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_hero_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 35}, "_children": [{"__id__": 61}, {"__id__": 75}], "_active": true, "_components": [{"__id__": 81}], "_prefab": {"__id__": 83}, "_lpos": {"__type__": "cc.Vec3", "x": -136.355, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_hero", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [{"__id__": 62}], "_active": true, "_components": [{"__id__": 68}, {"__id__": 70}, {"__id__": 72}], "_prefab": {"__id__": 74}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "image", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 61}, "_children": [], "_active": true, "_components": [{"__id__": 63}, {"__id__": 65}], "_prefab": {"__id__": 67}, "_lpos": {"__type__": "cc.Vec3", "x": -3, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 64}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fr/H4qyJOWYGksAQW+50+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 66}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97NBsrbyNPlalbhiE03Oo2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "486XeU7XRFBYfZBEiqnUrK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 69}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5LHHTJo9MQapybaJXL9fJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 71}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-42e9-ae29-d04abbf58c77@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9XkhpS0BFOpVAMtzrbe/2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 73}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68a48KR8VAw68lyk69tGGT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23KcR4Ku9KK6sHYrSCKwUG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_add", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 76}, {"__id__": 78}], "_prefab": {"__id__": 80}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -62.928, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": {"__id__": 77}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 58.239999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25u/UVuuNB/qFNZV99Wcyx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": {"__id__": 79}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "繁荣度\n+15%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bqf9TCcdJornTpGht2Bn+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbF/2YuzNJgaAk2VbDFZrl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 82}, "_contentSize": {"__type__": "cc.Size", "width": 102, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcF5+qe15ICa8am2qbKmq7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fcagUCBHBAjKvBq4okVJed", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_hero_3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 35}, "_children": [{"__id__": 85}, {"__id__": 99}], "_active": true, "_components": [{"__id__": 105}], "_prefab": {"__id__": 107}, "_lpos": {"__type__": "cc.Vec3", "x": -4.355, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_hero", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 84}, "_children": [{"__id__": 86}], "_active": true, "_components": [{"__id__": 92}, {"__id__": 94}, {"__id__": 96}], "_prefab": {"__id__": 98}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "image", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 85}, "_children": [], "_active": true, "_components": [{"__id__": 87}, {"__id__": 89}], "_prefab": {"__id__": 91}, "_lpos": {"__type__": "cc.Vec3", "x": -3, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": {"__id__": 88}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86OelUnPNC9pdur1F2hv43"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": {"__id__": 90}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47kOdJpDFN7Ll6XYrE8u6c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51HrXh2ytD5L0Celxlg6/r", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 93}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feg21pelhKpbAxDnla29bo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 95}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-42e9-ae29-d04abbf58c77@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2d+aYyl1FZIRTtzFlCXkN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 97}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25yDk/YU5LqqgtDWggeQ4C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8bGfNv7NFJ9KTrM27SIsSv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_add", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 84}, "_children": [], "_active": true, "_components": [{"__id__": 100}, {"__id__": 102}], "_prefab": {"__id__": 104}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -62.928, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": {"__id__": 101}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 58.239999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "527JrhjJxChp8F/U38oxWF"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": {"__id__": 103}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "繁荣度\n+15%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fq5n3xHpKTYStXt/NBIXi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8egq+NLn9EGoknST9CVseF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 106}, "_contentSize": {"__type__": "cc.Size", "width": 102, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4tiFd6cBFbbXTLvuJXpDd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c25b+nrJlHJ7FtQT+M1FQF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_hero_4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 35}, "_children": [{"__id__": 109}, {"__id__": 123}], "_active": true, "_components": [{"__id__": 129}], "_prefab": {"__id__": 131}, "_lpos": {"__type__": "cc.Vec3", "x": 127.645, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_hero", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 108}, "_children": [{"__id__": 110}], "_active": true, "_components": [{"__id__": 116}, {"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "image", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 111}, {"__id__": 113}], "_prefab": {"__id__": 115}, "_lpos": {"__type__": "cc.Vec3", "x": -3, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 112}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aZjeASFtIKpeeSA6vo7O0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 114}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaGhoA9E5K7ZLCJdt6dsmw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7azyQYeBFG96kFvX3C6md8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 117}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62MjVZ0bxEuoEtEwLfV42I"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 119}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-42e9-ae29-d04abbf58c77@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1hM+y82tGeZaY22Sx93ml"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 121}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "232eweE89ET5f1nnilhu2t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "da80BdO5FBp5X754QTcyvu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_add", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 108}, "_children": [], "_active": true, "_components": [{"__id__": 124}, {"__id__": 126}], "_prefab": {"__id__": 128}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -62.928, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 125}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 58.239999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31wdSxxLJN37MengMoAL/H"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 127}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "繁荣度\n+15%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89bhGGJhJIapu94juilyYN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cExpuBF5LPLnTlNUYrMbj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 130}, "_contentSize": {"__type__": "cc.Size", "width": 102, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eZsU5oKJAcqAxy9BNKfFN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "660wpgafZPJYkvGfnhsQhT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_hero_5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 35}, "_children": [{"__id__": 133}, {"__id__": 147}], "_active": true, "_components": [{"__id__": 153}], "_prefab": {"__id__": 155}, "_lpos": {"__type__": "cc.Vec3", "x": 258.53, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_hero", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 132}, "_children": [{"__id__": 134}], "_active": true, "_components": [{"__id__": 140}, {"__id__": 142}, {"__id__": 144}], "_prefab": {"__id__": 146}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.193}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "image", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 137}], "_prefab": {"__id__": 139}, "_lpos": {"__type__": "cc.Vec3", "x": -3, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 136}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abpla7SLNMXK/FWLGTZ+cz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 138}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbsQGbhGdNXauh/umxt1HD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c37ab38ApKzr/G4jaV6gP2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 141}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2jfXW1o9GMa07km4hK5Eh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 143}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-42e9-ae29-d04abbf58c77@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24SsM5T8xAVaPKlM9vF+tR"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 145}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5dWaiqQZO7JD7Dm4Kw4PL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1geHKjHVJ1pFfFeTfDzof", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_add", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 132}, "_children": [], "_active": true, "_components": [{"__id__": 148}, {"__id__": 150}], "_prefab": {"__id__": 152}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -62.928, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 149}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 58.239999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17izAnyaREQLqplpJBVLsm"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 151}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "繁荣度\n+15%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3mZbJ8TFEDo6mA/6kxdb1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "137GswLRFE1r+kMjLvsgZp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 154}, "_contentSize": {"__type__": "cc.Size", "width": 102, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaFU5Lci5I47XKsofd55UA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2XjzBgY9MMY6rWjA8fTFM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 157}, "_contentSize": {"__type__": "cc.Size", "width": 630, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "729ApERJ9EiaOSXQeEiwVJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": false, "__prefab": {"__id__": 159}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 613, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a4XVuXYtC87t9Il/dLnEo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7794JiaWlOVKA5l1GkVcdR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_city_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 162}, {"__id__": 164}, {"__id__": 166}], "_prefab": {"__id__": 168}, "_lpos": {"__type__": "cc.Vec3", "x": -2, "y": -311.32300000000004, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 161}, "_enabled": true, "__prefab": {"__id__": 163}, "_contentSize": {"__type__": "cc.Size", "width": 118, "height": 51.88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdXVFnQ49Or7nVQQavMe7/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 161}, "_enabled": true, "__prefab": {"__id__": 165}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "君天城", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 38, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 38, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2UdJQZPVIsYaNcKP7X8bH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 161}, "_enabled": false, "__prefab": {"__id__": 167}, "_alignFlags": 4, "_target": null, "_left": 17.37900000000002, "_right": 0, "_top": 0, "_bottom": 469.23699999999997, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29wJLnWUxPyKMnJsJTzwih"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2rcXob8ZDH4J/fJums4Cz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_city_speed", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 170}, {"__id__": 182}], "_active": true, "_components": [{"__id__": 196}, {"__id__": 198}, {"__id__": 200}], "_prefab": {"__id__": 202}, "_lpos": {"__type__": "cc.Vec3", "x": -318.468, "y": -289.329, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "item_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 169}, "_children": [{"__id__": 171}], "_active": true, "_components": [{"__id__": 177}, {"__id__": 179}], "_prefab": {"__id__": 181}, "_lpos": {"__type__": "cc.Vec3", "x": -25.99999999999997, "y": -1.1709999999998786, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon_ji<PERSON><PERSON>_shang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 170}, "_children": [], "_active": false, "_components": [{"__id__": 172}, {"__id__": 174}], "_prefab": {"__id__": 176}, "_lpos": {"__type__": "cc.Vec3", "x": 13.148999999999887, "y": -2.25100000000009, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": {"__id__": 173}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4NYvbacNKqrKBgXDcUXP7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": {"__id__": 175}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8c18a60e-a689-4dc9-b9d0-5b098ee6bc90@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33uudxt0xC/YCPUiCzx61h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbZzQWo59NsK7ArpjmIP5f", "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 178}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78f4Yb7D1O/7hSz0iMtQiI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 180}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5ddc7c6-0144-4433-8eb2-53f32cd574f2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66Q846AOtH2b5LtntpRrgW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dm/Y0TmBDSIEUhqzjuolP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_wenhao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 169}, "_children": [{"__id__": 183}], "_active": true, "_components": [{"__id__": 189}, {"__id__": 191}, {"__id__": 193}], "_prefab": {"__id__": 195}, "_lpos": {"__type__": "cc.Vec3", "x": 116.04702587890625, "y": -0.233, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 184}, {"__id__": 186}], "_prefab": {"__id__": 188}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 185}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bky4CwcJFS7eIfYSER8y6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 187}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "de1ad3e4-6d05-46d2-a61c-5c7b4dcbe4bc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aIUDutIZG4J+8wWjRvLWR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1tk1F1KpJbrWtHkouXxPw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 182}, "_enabled": true, "__prefab": {"__id__": 190}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dcNnIvBhBNqeLEkLlm2+f"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 182}, "_enabled": true, "__prefab": {"__id__": 192}, "_alignFlags": 32, "_target": null, "_left": 0, "_right": -49.247038085937504, "_top": 0, "_bottom": 454.334, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29tA8Np1dI545fVaAeHAQD"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 182}, "_enabled": true, "__prefab": {"__id__": 194}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cciHxb+1BCQY1bDzqZkr1J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1cJ2Z+Af1ObJqCdUNu7S+1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 169}, "_enabled": true, "__prefab": {"__id__": 197}, "_contentSize": {"__type__": "cc.Size", "width": 98.79998779296875, "height": 34.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9u6qcTyRAKK36xKgyf08z"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 169}, "_enabled": true, "__prefab": {"__id__": 199}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "气运0/秒", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6ICia4qBI2aDTn9khqfn/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 169}, "_enabled": false, "__prefab": {"__id__": 201}, "_alignFlags": 4, "_target": null, "_left": 0.288, "_right": 0, "_top": 0, "_bottom": 500.051, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 8, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66FpZ4vk5Ksr9MJXYvSbdl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0c9AiStuRCI4CpWTu5CF+G", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "progress_bar_worker", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 204}, {"__id__": 210}, {"__id__": 216}], "_active": true, "_components": [{"__id__": 222}, {"__id__": 224}, {"__id__": 226}, {"__id__": 228}], "_prefab": {"__id__": 230}, "_lpos": {"__type__": "cc.Vec3", "x": 284.1, "y": -297.527, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 203}, "_children": [], "_active": true, "_components": [{"__id__": 205}, {"__id__": 207}], "_prefab": {"__id__": 209}, "_lpos": {"__type__": "cc.Vec3", "x": -56.657, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 206}, "_contentSize": {"__type__": "cc.Size", "width": 113, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86y9L+kXdAib3o6VRh/kWC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 208}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e9aec5f7-5383-4611-ae79-58b767786e79@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47CErG5+FP56eNVES2WDVC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "08zYPP1FxK0YXVIPeZdqP8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_worker", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 203}, "_children": [], "_active": true, "_components": [{"__id__": 211}, {"__id__": 213}], "_prefab": {"__id__": 215}, "_lpos": {"__type__": "cc.Vec3", "x": -116.037, "y": 2.398, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 212}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 34.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4diLeRshVGkIFL2twmSkbX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 214}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "族人", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97HmHWz/NJEJAtgQpVuyxC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7cr4ZtioJN9ahj3I0jwJS0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_worker_num", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 203}, "_children": [], "_active": true, "_components": [{"__id__": 217}, {"__id__": 219}], "_prefab": {"__id__": 221}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2.77, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 218}, "_contentSize": {"__type__": "cc.Size", "width": 96.79995727539062, "height": 32.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "df3xsBHadKF6qyGdGc1Gsp"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 220}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "81/1000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 13, "g": 47, "b": 73, "a": 255}, "_outlineWidth": 1, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddCkmMfU9Ieo2L+YflEP4W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59PqIfmeFGrbNiLBpbd9qp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 203}, "_enabled": true, "__prefab": {"__id__": 223}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93wUf7vdJP4p6vBcMCSYPZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 203}, "_enabled": true, "__prefab": {"__id__": 225}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b033d061-3493-4903-959d-d7083db917c2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1awczIA3pBMYBRiTYK4uYK"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 203}, "_enabled": true, "__prefab": {"__id__": 227}, "_barSprite": {"__id__": 207}, "_mode": 0, "_totalLength": 119, "_progress": 1, "_reverse": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9atpSrEP9HILMa+pW4lAoP"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 203}, "_enabled": false, "__prefab": {"__id__": 229}, "_alignFlags": 4, "_target": null, "_left": 0.64592, "_right": 0, "_top": 0, "_bottom": 497.973, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49hCfc2xtI0po7HOOOw3VQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51kszFPEJHk6bD0Cl4GFdx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 232}, {"__id__": 234}, {"__id__": 236}, {"__id__": 238}], "_prefab": {"__id__": 240}, "_lpos": {"__type__": "cc.Vec3", "x": 327.5, "y": -246.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 233}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2d7nLI5hEPaYANPo887LR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 235}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "749c5d7e-ac59-431d-8557-fc934e24f580@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3adcrGweFF+qglUW5GqQp9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 237}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 18.000000000000004, "_top": 0, "_bottom": 516, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 32, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2R9eb1x5FiIIsV7+R0znG"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 239}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8d2DAtTDtM56P2+BiI/Fva"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43TvsWh+1ADaXpxs8AAsC8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_shuxing", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 242}, {"__id__": 250}, {"__id__": 256}, {"__id__": 288}], "_active": true, "_components": [{"__id__": 330}, {"__id__": 332}, {"__id__": 334}], "_prefab": {"__id__": 336}, "_lpos": {"__type__": "cc.Vec3", "x": 1.2629999999999768, "y": -454, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "line_fengexian", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 241}, "_children": [], "_active": true, "_components": [{"__id__": 243}, {"__id__": 245}, {"__id__": 247}], "_prefab": {"__id__": 249}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 242}, "_enabled": true, "__prefab": {"__id__": 244}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f75OPYm2BM4o+055hZw+zG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 242}, "_enabled": true, "__prefab": {"__id__": 246}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "00d12958-0162-4e36-bcca-987ef08c03b8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5auvnfLuxLcobAmW75hHgU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 242}, "_enabled": false, "__prefab": {"__id__": 248}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 92, "_bottom": 89, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 7, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92MCChf85Ka7zZ20QgsPuP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fHLbFnrtGb7ks+IcwEnAw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_shuxing", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 241}, "_children": [], "_active": true, "_components": [{"__id__": 251}, {"__id__": 253}], "_prefab": {"__id__": 255}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 68.813, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": {"__id__": 252}, "_contentSize": {"__type__": "cc.Size", "width": 61.09999084472656, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6Zlt2FzlO0L48ILreoqAU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": {"__id__": 254}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "属 性", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "314lDQdqNJ6KZVdl4QEFMF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cb2Q2cqRdIFKYNuYh5qtNk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout_attr_base", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 241}, "_children": [{"__id__": 257}, {"__id__": 263}, {"__id__": 269}, {"__id__": 275}], "_active": true, "_components": [{"__id__": 281}, {"__id__": 283}, {"__id__": 285}], "_prefab": {"__id__": 287}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 20.558, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_baseattr1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 256}, "_children": [], "_active": true, "_components": [{"__id__": 258}, {"__id__": 260}], "_prefab": {"__id__": 262}, "_lpos": {"__type__": "cc.Vec3", "x": -255, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 257}, "_enabled": true, "__prefab": {"__id__": 259}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46XMylQhxHWpNmvagJfalA"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 257}, "_enabled": true, "__prefab": {"__id__": 261}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "攻击 +400", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "457FccInNKt4Tp0mtcfJGi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8fH5WLH2NB3q/2Q8sDWiyH", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_baseattr2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 256}, "_children": [], "_active": true, "_components": [{"__id__": 264}, {"__id__": 266}], "_prefab": {"__id__": 268}, "_lpos": {"__type__": "cc.Vec3", "x": -85, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 265}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ehaDe0KlCUra2i2xQhx/x"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 267}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "血量 +400", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48n7j2p4hFSLJbqzRqM9hu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4JQpIfnZBVqWIf11B+sHm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_baseattr3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 256}, "_children": [], "_active": true, "_components": [{"__id__": 270}, {"__id__": 272}], "_prefab": {"__id__": 274}, "_lpos": {"__type__": "cc.Vec3", "x": 85, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": {"__id__": 271}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2e4jLkX6tIPb/dnUtjFw4r"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": {"__id__": 273}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "防御 +400", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bfPCxc11KBoAzoP7hVi4F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06ZhVSm4hHGpkDE0Ni4gXm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_baseattr4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 256}, "_children": [], "_active": true, "_components": [{"__id__": 276}, {"__id__": 278}], "_prefab": {"__id__": 280}, "_lpos": {"__type__": "cc.Vec3", "x": 255, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 277}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fRsNfGWNG44vi9wZ5Tuzv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 279}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "敏捷 +400", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48ca1t1IVKq5px8BJLdmVL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b97AHakMdEFKU5BojjJSd0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 282}, "_contentSize": {"__type__": "cc.Size", "width": 680, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69E9Ad/PlJkZ4ONPq7aS1R"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 284}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abmEHpLztIRJjnBEEjEPci"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": false, "__prefab": {"__id__": 286}, "_alignFlags": 0, "_target": null, "_left": 0.028125511596180135, "_right": 0.06914603531279317, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 631.6319122314453, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 40, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "697cA1BwZNJZbTPP1wBrKA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75u0SnovxHC6sDvAOvb3jo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout_attr_rate", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 241}, "_children": [{"__id__": 289}, {"__id__": 295}, {"__id__": 301}, {"__id__": 307}, {"__id__": 313}, {"__id__": 319}], "_active": true, "_components": [{"__id__": 325}, {"__id__": 327}], "_prefab": {"__id__": 329}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -42.2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attr1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 288}, "_children": [], "_active": true, "_components": [{"__id__": 290}, {"__id__": 292}], "_prefab": {"__id__": 294}, "_lpos": {"__type__": "cc.Vec3", "x": -208, "y": 16.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": {"__id__": 291}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8drdAxge1Au4T3x8di2gYx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": {"__id__": 293}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "抗击晕 +1% ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43NZzAuzlLuYNMwF5Vchw+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cm51toGtPr6fY/p+/87Xw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_attr2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 288}, "_children": [], "_active": true, "_components": [{"__id__": 296}, {"__id__": 298}], "_prefab": {"__id__": 300}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 16.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 297}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41HwTXkXFFk7QNOHeo11vu"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 299}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "抗击晕 +1% ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aalg+yVZRDB7vs2XxeVylW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3aYf9S5DVAP4Ili0gRg/LV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_attr3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 288}, "_children": [], "_active": true, "_components": [{"__id__": 302}, {"__id__": 304}], "_prefab": {"__id__": 306}, "_lpos": {"__type__": "cc.Vec3", "x": 208, "y": 16.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 301}, "_enabled": true, "__prefab": {"__id__": 303}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3ZWqrektHDbDJeHaRDScm"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 301}, "_enabled": true, "__prefab": {"__id__": 305}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "抗击晕 +1% ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6h/t+szFBfJg0Y3mk89BR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48VfsLlDRH6IN+SYLjkX2j", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_attr4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 288}, "_children": [], "_active": true, "_components": [{"__id__": 308}, {"__id__": 310}], "_prefab": {"__id__": 312}, "_lpos": {"__type__": "cc.Vec3", "x": -208, "y": -16.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 309}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72YTVMELdC6avqd1GdjURl"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 311}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "抗击晕 +1% ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fasjKys9VBw4k3FKBYEWrJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7ayQ5a7CtN2I2zErPbl54+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_attr5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 288}, "_children": [], "_active": true, "_components": [{"__id__": 314}, {"__id__": 316}], "_prefab": {"__id__": 318}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -16.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 313}, "_enabled": true, "__prefab": {"__id__": 315}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92yjUg6sBGMrf54zBLVhOD"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 313}, "_enabled": true, "__prefab": {"__id__": 317}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "抗击晕 +1% ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6lxocpjBCeIeKEAXi/1wN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1bkn/D2FMgrq8NJstCxs2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_attr6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 288}, "_children": [], "_active": true, "_components": [{"__id__": 320}, {"__id__": 322}], "_prefab": {"__id__": 324}, "_lpos": {"__type__": "cc.Vec3", "x": 208, "y": -16.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 321}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baG3nMHFNFXK1a9iHhDi6k"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 323}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "抗击晕 +1% ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "519eRwLPBKEZIMaab0tX2l"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33lP9+ksJLja+pdb+klcFR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 326}, "_contentSize": {"__type__": "cc.Size", "width": 624, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84wdcAfo5Jupk/ObGpI58a"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 328}, "_resizeMode": 1, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3ocg1sddPF6OqWKL4j8LU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edaM+Bv2tCc46SvRtnDWY/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": true, "__prefab": {"__id__": 331}, "_contentSize": {"__type__": "cc.Size", "width": 684.674, "height": 185}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dl3G1ER9HgbrNUKN/mVkU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": true, "__prefab": {"__id__": 333}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "62b3993e-39e2-4c6a-9917-9ab22127a087@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55dXZ4JnlPio2aihmKe0zj"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": false, "__prefab": {"__id__": 335}, "_alignFlags": 4, "_target": null, "_left": 0.01133333333333326, "_right": 0.01133333333333326, "_top": 0, "_bottom": 260.00000000000006, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 733, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 40, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6GMINn4ZD1aVFv3zL20Jb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98TSeIRA9FNK3K/CVFJ7cG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_shengji", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 338}, {"__id__": 363}, {"__id__": 369}, {"__id__": 397}], "_active": true, "_components": [{"__id__": 421}, {"__id__": 423}], "_prefab": {"__id__": 425}, "_lpos": {"__type__": "cc.Vec3", "x": -166.16900000000004, "y": -634.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_shengji", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 337}, "_children": [{"__id__": 339}, {"__id__": 350}], "_active": true, "_components": [{"__id__": 356}, {"__id__": 358}, {"__id__": 360}], "_prefab": {"__id__": 362}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 338}, "_prefab": {"__id__": 340}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 339}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 341}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "1ccOz+FtJPAbmti+rT1hCh", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 342}, {"__id__": 344}, {"__id__": 345}, {"__id__": 346}, {"__id__": 347}, {"__id__": 348}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 343}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 343}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 51.204, "y": 55.355, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 343}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 343}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 343}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 349}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["d62NA3LF1OTZbxn67P5fPN"]}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 338}, "_children": [], "_active": true, "_components": [{"__id__": 351}, {"__id__": 353}], "_prefab": {"__id__": 355}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": {"__id__": 352}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90tjYZL/pMRJrZcUumYXoK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": {"__id__": 354}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "升 级", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 244, "g": 181, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1eLibUFRExb6gPGZTP41F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2hHmP2/9KHKyppuX65Ag/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 357}, "_contentSize": {"__type__": "cc.Size", "width": 146, "height": 146}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caGOob4dNPRLxz7lUKeGpl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 359}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c94231f5-a78e-40be-9b61-81f0cb5ff4a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dLAkdBHBMxJskl1Z4hnSP"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 361}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fqNX7dr1E4r3JTz2ZgQSb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5a9PE3U6NPlJe8Tplaol3n", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "up_finish", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 337}, "_children": [], "_active": false, "_components": [{"__id__": 364}, {"__id__": 366}], "_prefab": {"__id__": 368}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 365}, "_contentSize": {"__type__": "cc.Size", "width": 138, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbwzwHfSdForNirwzRQP5T"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 367}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b64b1cc2-f57e-4eab-af3f-d441bb2b7c88@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70D97fkHFAC79+8Az30V0x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bb9HlqLdN861Vi4auZtTy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "progress_bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 337}, "_children": [{"__id__": 370}, {"__id__": 378}, {"__id__": 384}], "_active": true, "_components": [{"__id__": 390}, {"__id__": 392}, {"__id__": 394}], "_prefab": {"__id__": 396}, "_lpos": {"__type__": "cc.Vec3", "x": 13.728, "y": -86.494, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 369}, "_children": [], "_active": true, "_components": [{"__id__": 371}, {"__id__": 373}, {"__id__": 375}], "_prefab": {"__id__": 377}, "_lpos": {"__type__": "cc.Vec3", "x": -59.932, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 372}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65VK1XUfpFiZB17vtUEFxs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 374}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e3270091-1ec7-4ae5-b123-3f83b87dd997@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9e6mlZvAFI8oHRyqFru+qW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 376}, "_alignFlags": 0, "_target": null, "_left": 1, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4aWl3TE8pGtpzNWBNDSVKg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3b5TIGLIpJY4+ZEoaWMFIb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "item_1016", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 369}, "_children": [], "_active": true, "_components": [{"__id__": 379}, {"__id__": 381}], "_prefab": {"__id__": 383}, "_lpos": {"__type__": "cc.Vec3", "x": -59.536, "y": 2.024, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.35, "y": 0.35, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 378}, "_enabled": true, "__prefab": {"__id__": 380}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4c49N5NIlKbY2HE3jYiqlQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 378}, "_enabled": true, "__prefab": {"__id__": 382}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b28d1f47-827b-42cc-9497-269b05eb493a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caNxXltlBCHrralvmWQN+7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58oDnynb9CPZeXN45lquca", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_item_cost", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 369}, "_children": [], "_active": true, "_components": [{"__id__": 385}, {"__id__": 387}], "_prefab": {"__id__": 389}, "_lpos": {"__type__": "cc.Vec3", "x": -41.294, "y": 3.063, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 386}, "_contentSize": {"__type__": "cc.Size", "width": 74.02496337890625, "height": 29.09}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53HBUEIMxHi562SgHcuPz/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 388}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "37/397", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 21.5, "_fontSize": 21.5, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21.5, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 168}, "_outlineWidth": 1, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96njsOdNJPlLuUdBiTRxtQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "757EHANnRFuJq0nLlOESr7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 391}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79rMbEKUJFHZD0zUr6nfNT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 393}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "99f35055-177c-4092-9d8c-8c45196c3b92@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06rSiAlv1C9qK+sEa6sYJy"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 395}, "_barSprite": {"__id__": 373}, "_mode": 0, "_totalLength": 132, "_progress": 1, "_reverse": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3FdQeWFNMjovwH13Yv4TY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5D/x0EudNIbU12+6TatUC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout_up_add", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 337}, "_children": [{"__id__": 398}, {"__id__": 404}, {"__id__": 410}], "_active": true, "_components": [{"__id__": 416}, {"__id__": 418}], "_prefab": {"__id__": 420}, "_lpos": {"__type__": "cc.Vec3", "x": 2.924, "y": -123.102, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_from", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 399}, {"__id__": 401}], "_prefab": {"__id__": 403}, "_lpos": {"__type__": "cc.Vec3", "x": -45.399993896484375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": {"__id__": 400}, "_contentSize": {"__type__": "cc.Size", "width": 56.79998779296875, "height": 34.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37668m7sFKTYcjLlQBXe/f"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": {"__id__": 402}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "15倍", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 72, "g": 83, "b": 117, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2asgfekelOt6SlnkmLVXig"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37vph1FddPIqfi3NLKm52h", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 405}, {"__id__": 407}], "_prefab": {"__id__": 409}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 404}, "_enabled": true, "__prefab": {"__id__": 406}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 19}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09RYWaip9ClLmfhTCoY6jM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 404}, "_enabled": true, "__prefab": {"__id__": 408}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "722dfddc-ce86-44f5-84e8-bb234ab03a75@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1BqMpqX5ILZPaNTr0HqQ4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2f/CA0iStJU5fPb/opNBki", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_to", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 411}, {"__id__": 413}], "_prefab": {"__id__": 415}, "_lpos": {"__type__": "cc.Vec3", "x": 45.399993896484375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 412}, "_contentSize": {"__type__": "cc.Size", "width": 56.79998779296875, "height": 34.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8nlg9JB1DE7kCcc8mRG26"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 414}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 79, "g": 237, "b": 109, "a": 255}, "_string": "20倍", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 60, "g": 143, "b": 15, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daXRfstWpAfYnkI3gOEEIe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40r1IibRJAh5v2s3ntnqk8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 397}, "_enabled": true, "__prefab": {"__id__": 417}, "_contentSize": {"__type__": "cc.Size", "width": 147.5999755859375, "height": 17}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60ZtPcAY5IOLkLxODhxT/G"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 397}, "_enabled": true, "__prefab": {"__id__": 419}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 2, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fK5ZqhotES6vs0CzBySnU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3RWfKEDpJjqQCXIw0agfS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 422}, "_contentSize": {"__type__": "cc.Size", "width": 175, "height": 156}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4edByxftO94MTMz8c85S+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 337}, "_enabled": false, "__prefab": {"__id__": 424}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 89.99999999999989, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bRsoIb3dO9a+3MGG2X7rC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a17JisB31Iz7q1Bca7AcXj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_haozhao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 427}, {"__id__": 465}, {"__id__": 471}], "_active": true, "_components": [{"__id__": 499}, {"__id__": 501}], "_prefab": {"__id__": 503}, "_lpos": {"__type__": "cc.Vec3", "x": 163.54200000000003, "y": -634.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_haozhao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [{"__id__": 428}, {"__id__": 440}], "_active": true, "_components": [{"__id__": 460}, {"__id__": 462}], "_prefab": {"__id__": 464}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_haozhao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [{"__id__": 429}], "_active": true, "_components": [{"__id__": 435}, {"__id__": 437}], "_prefab": {"__id__": 439}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 428}, "_children": [], "_active": true, "_components": [{"__id__": 430}, {"__id__": 432}], "_prefab": {"__id__": 434}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -47, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 431}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cjqeWr2dOWJKergGT3QFM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 433}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "号 召", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 34, "g": 73, "b": 112, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6d2+za6qdHZKEUzTek76Bf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "41jg5MFeZPyrSP+NfEjaoa", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 436}, "_contentSize": {"__type__": "cc.Size", "width": 147, "height": 146}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bgap6PRxPi54R8gwntDY3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 438}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bf074345-16e6-411e-ae82-3b11078aad30@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acEa7dzJJEfI/sTbEAg4si"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37xPyd82BDEJCi0kg831JJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_energy_cost", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [{"__id__": 441}, {"__id__": 447}], "_active": true, "_components": [{"__id__": 455}, {"__id__": 457}], "_prefab": {"__id__": 459}, "_lpos": {"__type__": "cc.Vec3", "x": 10.338, "y": -84.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_item", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 440}, "_children": [], "_active": true, "_components": [{"__id__": 442}, {"__id__": 444}], "_prefab": {"__id__": 446}, "_lpos": {"__type__": "cc.Vec3", "x": -57.682, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.4, "y": 0.4, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 441}, "_enabled": true, "__prefab": {"__id__": 443}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83bpCWXedA1pm2i2i5Ub0/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 441}, "_enabled": true, "__prefab": {"__id__": 445}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5ddc7c6-0144-4433-8eb2-53f32cd574f2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4735CxL9hLdqzWRqpZFRQl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "12/zevdL1JC4zIuiZBIeAC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_haozhao_cost", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 440}, "_children": [], "_active": true, "_components": [{"__id__": 448}, {"__id__": 450}, {"__id__": 452}], "_prefab": {"__id__": 454}, "_lpos": {"__type__": "cc.Vec3", "x": -40, "y": 0.923, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 449}, "_contentSize": {"__type__": "cc.Size", "width": 104.12496948242188, "height": 29.09}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "142ieZZIhGLrbOiYduWRpU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 451}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "700.9万亿", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 21.5, "_fontSize": 21.5, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21.5, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 168}, "_outlineWidth": 1, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43YAajnvhImrdxBHz+aOUi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 453}, "_alignFlags": 8, "_target": null, "_left": 20, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 8, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1T+vAQHVNMp+DM4nGTV5c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "12c2OCyGdFML1Kmqun9q3X", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 440}, "_enabled": true, "__prefab": {"__id__": 456}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01MtowVe1Ki5lQJeFPFGtN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 440}, "_enabled": true, "__prefab": {"__id__": 458}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8a1cd6ad-6301-4d51-a76e-1da37e624092@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35b1AGxZ1H1YBRUEjVKJxu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1flPT/wUFK0IA0J+78Q523", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 461}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cceIQDcSdIdr0kssMRFQ/K"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 463}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 427}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfYhh2g5BDT6LMIsocUoNF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6yHF5X39OzJbv2+rZ+9A3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spine_zhao_huan_btn_action", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [], "_active": false, "_components": [{"__id__": 466}, {"__id__": 468}], "_prefab": {"__id__": 470}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 465}, "_enabled": true, "__prefab": {"__id__": 467}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1646}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.7206000162760416, "y": 0.11373025330998918}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82IE1XkbNPWoohBBg6VeiI"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 465}, "_enabled": true, "__prefab": {"__id__": 469}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "4332ee9f-9253-48a4-b474-cb3767f20581", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19eXLOuF9BRKqzJi6CanHI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37aYsiCzZOVqiXzDB4Uc9o", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_choose", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [{"__id__": 472}, {"__id__": 484}], "_active": true, "_components": [{"__id__": 490}, {"__id__": 492}, {"__id__": 494}, {"__id__": 496}], "_prefab": {"__id__": 498}, "_lpos": {"__type__": "cc.Vec3", "x": 1.616, "y": -124, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_duigou", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 471}, "_children": [{"__id__": 473}], "_active": true, "_components": [{"__id__": 479}, {"__id__": 481}], "_prefab": {"__id__": 483}, "_lpos": {"__type__": "cc.Vec3", "x": -52.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_choose_icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 472}, "_children": [], "_active": true, "_components": [{"__id__": 474}, {"__id__": 476}], "_prefab": {"__id__": 478}, "_lpos": {"__type__": "cc.Vec3", "x": 5.465, "y": 6.816, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 475}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91D2Xh1mJFvZqHh3Y9sIWe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 477}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "42951d8d-0d4f-4912-89a3-69f4a18cd38a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7AsRnRlBK4pkUy26cTz9T"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1cbaRfoB5KyL+24GHW2RU0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 480}, "_contentSize": {"__type__": "cc.Size", "width": 29, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d12ybWuDtG94WRbhqNhgLe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 482}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-462c-a584-87c23a70f4d0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdW63NmJpPLoiZjBsBJh0P"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5ckjymD+5KAb/+lYLfddao", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 471}, "_children": [], "_active": true, "_components": [{"__id__": 485}, {"__id__": 487}], "_prefab": {"__id__": 489}, "_lpos": {"__type__": "cc.Vec3", "x": 17, "y": 1.723, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 486}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 34.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cXxFbOD5PxY11Rh49OLAM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 488}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "自动号召", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 72, "g": 83, "b": 117, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dkGkLObRD0JQyjx0P0JO9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90RvKQh3RJeaUdqQwUSvqJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 491}, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bhUTo9OtGzJb2QEaCt/9w"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 493}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 5, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dgtosMUxCh5zXOhOfmBxl"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 495}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 471}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fpDMtrNJFG6E6Ubc8oe87"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 497}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -68, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42z3PqhLJHyqWJvgz+OuVb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bsDWoziRIMoOcDtGR16pe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 426}, "_enabled": true, "__prefab": {"__id__": 500}, "_contentSize": {"__type__": "cc.Size", "width": 175, "height": 156}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91TjlesQFGCpi1yA/3ZGEU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 426}, "_enabled": false, "__prefab": {"__id__": 502}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 89.99999999999989, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feXv0AFIFA6pPheO62yvz7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8WYKxvchIHLfa7frDf/g7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 505}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1613}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9esokXme1DIJlREpEPe6q2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 507}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5229cd30-f33d-4149-bdfc-64f196b856d4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3FR/WQndGe5/9+baDwaFv"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 509}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": -56.5, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 1613, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7079gi5UpHgb2uEp9Z+Oil"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3fs4zaYfpGEJQUfTVr54Rh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_fanrongdu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 512}, {"__id__": 520}], "_active": true, "_components": [{"__id__": 528}, {"__id__": 530}, {"__id__": 532}], "_prefab": {"__id__": 534}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 699.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_city_type", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 511}, "_children": [], "_active": true, "_components": [{"__id__": 513}, {"__id__": 515}, {"__id__": 517}], "_prefab": {"__id__": 519}, "_lpos": {"__type__": "cc.Vec3", "x": -94.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 512}, "_enabled": true, "__prefab": {"__id__": 514}, "_contentSize": {"__type__": "cc.Size", "width": 47, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcnzRWjolBy7zwmKh3GBvT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 512}, "_enabled": true, "__prefab": {"__id__": 516}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b575898d-d88c-4b18-ac55-302c4f3d26a6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fuGWBsvZG5ZixR6lqPg61"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 512}, "_enabled": true, "__prefab": {"__id__": 518}, "_alignFlags": 10, "_target": null, "_left": -16, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 8, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17Lfw+lLlFyI9PPh+1tnP1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78iodrL0lPF7uZye1wDKqt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_city_bloom", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 511}, "_children": [], "_active": true, "_components": [{"__id__": 521}, {"__id__": 523}, {"__id__": 525}], "_prefab": {"__id__": 527}, "_lpos": {"__type__": "cc.Vec3", "x": 20.399978637695312, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 520}, "_enabled": true, "__prefab": {"__id__": 522}, "_contentSize": {"__type__": "cc.Size", "width": 154.79995727539062, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaVmpRDKBIjZv0KfEg+XLM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 520}, "_enabled": true, "__prefab": {"__id__": 524}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 244, "b": 200, "a": 255}, "_string": "繁荣度 :199.3", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0K8yvnl9FMaZfPkNNdmhQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 520}, "_enabled": true, "__prefab": {"__id__": 526}, "_alignFlags": 8, "_target": null, "_left": 45, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 8, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73iWFPNn1A1aoBdXboXXaG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "20kDNUiZFAE5qr0ie0MOB7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 511}, "_enabled": true, "__prefab": {"__id__": 529}, "_contentSize": {"__type__": "cc.Size", "width": 204, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "206vLKeDJFSqFlCJUcp0sv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 511}, "_enabled": true, "__prefab": {"__id__": 531}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d248438b-579b-4f3f-a265-576968a5333f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25Ehn3bmBFFbEr+K90s9dN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 511}, "_enabled": true, "__prefab": {"__id__": 533}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 31, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9b5n1OUHBK1JDft9pkPVIi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53AcsAtjRLgIfBnEVKph3P", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 536}, {"__id__": 544}], "_active": true, "_components": [{"__id__": 552}, {"__id__": 554}, {"__id__": 556}], "_prefab": {"__id__": 558}, "_lpos": {"__type__": "cc.Vec3", "x": -334.886, "y": 698.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 535}, "_children": [], "_active": true, "_components": [{"__id__": 537}, {"__id__": 539}, {"__id__": 541}], "_prefab": {"__id__": 543}, "_lpos": {"__type__": "cc.Vec3", "x": 27.07000000000005, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 536}, "_enabled": true, "__prefab": {"__id__": 538}, "_contentSize": {"__type__": "cc.Size", "width": 97.5, "height": 26}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebfVE5OBNHoomyxl0A/5pn"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 536}, "_enabled": true, "__prefab": {"__id__": 540}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "60.63亿", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6wdoxEddJQ5lw3qQd3gej"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 536}, "_enabled": false, "__prefab": {"__id__": 542}, "_alignFlags": 8, "_target": null, "_left": 27.07, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19P/VPgvtBW4L8ZKAAWC23"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "075j7D2BdAKq2uh0OJokb9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 535}, "_children": [], "_active": true, "_components": [{"__id__": 545}, {"__id__": 547}, {"__id__": 549}], "_prefab": {"__id__": 551}, "_lpos": {"__type__": "cc.Vec3", "x": -5, "y": 0.8820000000000618, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 546}, "_contentSize": {"__type__": "cc.Size", "width": 55, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fVGphKpJOZqWLQLr/I9x/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 548}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5ddc7c6-0144-4433-8eb2-53f32cd574f2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35SZkc3ABGc5rzFp1BqXZM"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": false, "__prefab": {"__id__": 550}, "_alignFlags": 9, "_target": null, "_left": -32.5, "_right": 0, "_top": -7.882, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69j3pv+DhOabGE4xl6aSA6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2vLYVtdpO04u2hcGpQR35", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 535}, "_enabled": true, "__prefab": {"__id__": 553}, "_contentSize": {"__type__": "cc.Size", "width": 179, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94MTwZq6xPmKdRboKasq/8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 535}, "_enabled": true, "__prefab": {"__id__": 555}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "26f310f5-9827-48dc-bff5-3b7bb284f8fd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15xJUv8o1BLaFmwId6acWO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 535}, "_enabled": true, "__prefab": {"__id__": 557}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 31, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3z5QlyMpESIOnYLCB3i1I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aQAJ8t5pIS5IPFJk9MTLL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout_right", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 560}], "_active": true, "_components": [{"__id__": 616}, {"__id__": 618}], "_prefab": {"__id__": 620}, "_lpos": {"__type__": "cc.Vec3", "x": 300.654, "y": 660.787, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_hao_zhao_box", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 559}, "_children": [{"__id__": 561}, {"__id__": 597}], "_active": true, "_components": [{"__id__": 609}, {"__id__": 611}, {"__id__": 613}], "_prefab": {"__id__": 615}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "open", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 560}, "_children": [{"__id__": 562}], "_active": false, "_components": [{"__id__": 592}, {"__id__": 594}], "_prefab": {"__id__": 596}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_baoXiang<PERSON><PERSON>_yuan", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 561}, "_children": [{"__id__": 563}], "_active": true, "_components": [{"__id__": 587}, {"__id__": 589}], "_prefab": {"__id__": 591}, "_lpos": {"__type__": "cc.Vec3", "x": 42.936000000000035, "y": 39.40499999999997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 562}, "_prefab": {"__id__": 564}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 563}, "asset": {"__uuid__": "e17507d2-bcb4-45fb-a2a0-9e3a71ded9e3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 565}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "05CDS+3lhOz5SPLC5UDXyB", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 566}, {"__id__": 568}, {"__id__": 570}, {"__id__": 572}, {"__id__": 574}, {"__id__": 576}, {"__id__": 578}, {"__id__": 580}, {"__id__": 582}, {"__id__": 584}, {"__id__": 586}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 567}, "propertyPath": ["_name"], "value": "lbl_hao_zhao_box_num"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 569}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 571}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 573}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 575}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 22, "height": 41.8}}, {"__type__": "cc.TargetInfo", "localID": ["37yPOG8eBDz5uIrky+3j+J"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 577}, "propertyPath": ["_fontSize"], "value": 30}, {"__type__": "cc.TargetInfo", "localID": ["19hdUq4URCBrycxPzY5/Yd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 579}, "propertyPath": ["_actualFontSize"], "value": 31.75}, {"__type__": "cc.TargetInfo", "localID": ["19hdUq4URCBrycxPzY5/Yd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 581}, "propertyPath": ["_lineHeight"], "value": 30}, {"__type__": "cc.TargetInfo", "localID": ["19hdUq4URCBrycxPzY5/Yd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 583}, "propertyPath": ["_string"], "value": "0"}, {"__type__": "cc.TargetInfo", "localID": ["19hdUq4URCBrycxPzY5/Yd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 585}, "propertyPath": ["_cacheMode"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["19hdUq4URCBrycxPzY5/Yd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 573}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 562}, "_enabled": true, "__prefab": {"__id__": 588}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebj0HSjUtBA7gu+K6OWey8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 562}, "_enabled": true, "__prefab": {"__id__": 590}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ce386fd0-9389-4fe3-b648-5fd234fba3bb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ff31KzhyFOe6/X98XPbGWV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2axOamTZGMYSrxGQWafDW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 561}, "_enabled": true, "__prefab": {"__id__": 593}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56Po+j1YhLTbimn2NrkvAh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 561}, "_enabled": true, "__prefab": {"__id__": 595}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e09003be-2cbb-4cad-a160-53e004838ec8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eT2ff0hZFS5NV6nQH9IST"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "502EKV1t5K9qSwNtdpDZ26", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "no_open", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 560}, "_children": [{"__id__": 598}], "_active": false, "_components": [{"__id__": 604}, {"__id__": 606}], "_prefab": {"__id__": 608}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon_suo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 597}, "_children": [], "_active": true, "_components": [{"__id__": 599}, {"__id__": 601}], "_prefab": {"__id__": 603}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 598}, "_enabled": true, "__prefab": {"__id__": 600}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9B9rng+dFgL+8nb0ucTga"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 598}, "_enabled": true, "__prefab": {"__id__": 602}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "33ea29d6-5c1c-4e07-8c6d-09c5154daa2c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bd4mQg2Q1DiLsI7puH35Yv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "867djzISpFC6EFSqZjuAY+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 597}, "_enabled": true, "__prefab": {"__id__": 605}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48NbS6j5FMjISyRFfFTwql"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 597}, "_enabled": true, "__prefab": {"__id__": 607}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "804d3ebb-7783-4145-9cfc-70a036a3dbc8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93WfBaZ9lM3ar1V76wVo7U"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dehr48mONPLJOUr3yV2TOY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 560}, "_enabled": true, "__prefab": {"__id__": 610}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cb/qDQqIdOF6XT4eRg33KD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 560}, "_enabled": true, "__prefab": {"__id__": 612}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e09003be-2cbb-4cad-a160-53e004838ec8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fl/lKYRVD6p6NHCVj767r"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 560}, "_enabled": true, "__prefab": {"__id__": 614}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.05, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30G59xDBJOZqFqfxwlwLIZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6e9Kt2sNVLs4X6w2CyF14j", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 559}, "_enabled": true, "__prefab": {"__id__": 617}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45bR+IK8JOtrVxOEVkCqU8"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 559}, "_enabled": true, "__prefab": {"__id__": 619}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 32.712999999999965, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85SGJlPjFIz64ned4JbX3+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "admwV1jdZGO4d0Axnz1Ns2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_build_area", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 622}, {"__id__": 624}, {"__id__": 626}], "_prefab": {"__id__": 628}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 142.735, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 621}, "_enabled": true, "__prefab": {"__id__": 623}, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 512}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b48D374INGTrTER5O8pVWx"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 621}, "_enabled": true, "__prefab": {"__id__": 625}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12QXUO3IJPfbqc/iafE8Lt"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 621}, "_enabled": true, "__prefab": {"__id__": 627}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 892.735, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0Otugup5NxYhb2pXBsRSn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "708jo31PZI9ZXhMcWgqYhc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_yulan", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 630}], "_active": true, "_components": [{"__id__": 636}, {"__id__": 638}, {"__id__": 640}, {"__id__": 642}], "_prefab": {"__id__": 644}, "_lpos": {"__type__": "cc.Vec3", "x": -314, "y": 91.55199999999991, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 629}, "_children": [], "_active": true, "_components": [{"__id__": 631}, {"__id__": 633}], "_prefab": {"__id__": 635}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -29.88, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 630}, "_enabled": true, "__prefab": {"__id__": 632}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4PPtEQdNJHYaG+GhwL9kw"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 630}, "_enabled": true, "__prefab": {"__id__": 634}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 253, "b": 247, "a": 255}, "_string": "预览", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 33, "g": 64, "b": 103, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19Yn/ZkFRL+oLaIFkS/nQ6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3VEcuc1pIYp2hWA2u3p80", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 637}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24+LaY67tAxbxzMdJcxPTS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 639}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "54110acf-91d5-4993-8a8e-6cad282faa35@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46Ew4DS0dGuJmT4ODEqVEc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 641}, "_alignFlags": 4, "_target": null, "_left": 0.04133333333333333, "_right": 0, "_top": 0, "_bottom": 811.5519999999999, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 8, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e33xrdDc1BxLhiWz7VUJZy"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 643}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfm7f1AmVLzqe9uODhxAUZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbb8QUxsNIDKIvNxQo5/S4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_bloom_add", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 646}, {"__id__": 648}, {"__id__": 650}], "_prefab": {"__id__": 652}, "_lpos": {"__type__": "cc.Vec3", "x": 0.2950000000000159, "y": -181.26300000000003, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 645}, "_enabled": true, "__prefab": {"__id__": 647}, "_contentSize": {"__type__": "cc.Size", "width": 154.699951171875, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84LQq2/BFCpYdgA0eQHVHn"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 645}, "_enabled": true, "__prefab": {"__id__": 649}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 69, "g": 106, "b": 144, "a": 255}, "_string": "繁荣度 +30%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84MgaNQGBJFLyvPpZQYZ1x"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 645}, "_enabled": true, "__prefab": {"__id__": 651}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 552.357, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cGMX8nQJNVZYFCJ6pu9Zh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f6ioYTQBhEaaj0bb1gVyyf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 654}, {"__id__": 662}], "_active": false, "_components": [{"__id__": 890}, {"__id__": 892}], "_prefab": {"__id__": 894}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_close_info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 653}, "_children": [], "_active": true, "_components": [{"__id__": 655}, {"__id__": 657}, {"__id__": 659}], "_prefab": {"__id__": 661}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 654}, "_enabled": true, "__prefab": {"__id__": 656}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35+HOkiV5CzpTnbYkc2D4w"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 654}, "_enabled": true, "__prefab": {"__id__": 658}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eHaZFTBBI2rC0tl6nCkpZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 654}, "_enabled": true, "__prefab": {"__id__": 660}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3VMnIfaBG87IPTNbso4ZA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04NkhGv05Bi5OB6UxaLxyF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout_info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 653}, "_children": [{"__id__": 663}, {"__id__": 703}, {"__id__": 721}, {"__id__": 781}, {"__id__": 799}, {"__id__": 859}, {"__id__": 877}], "_active": true, "_components": [{"__id__": 883}, {"__id__": 885}, {"__id__": 887}], "_prefab": {"__id__": 889}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 662}, "_children": [{"__id__": 664}, {"__id__": 682}, {"__id__": 694}], "_active": true, "_components": [{"__id__": 700}], "_prefab": {"__id__": 702}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 75.88, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 663}, "_children": [{"__id__": 665}, {"__id__": 671}], "_active": true, "_components": [{"__id__": 677}, {"__id__": 679}], "_prefab": {"__id__": 681}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 127.835, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 664}, "_children": [], "_active": true, "_components": [{"__id__": 666}, {"__id__": 668}], "_prefab": {"__id__": 670}, "_lpos": {"__type__": "cc.Vec3", "x": -150, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 665}, "_enabled": true, "__prefab": {"__id__": 667}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72o23kKFFHBYM+zuoEhU11"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 665}, "_enabled": true, "__prefab": {"__id__": 669}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "897fa295-fcb3-4615-9572-5d2cd1056943@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cqe1sS6JBHrLqy0QL2yHJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94POnRO7VEd5Xyqw0LD4qn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 664}, "_children": [], "_active": true, "_components": [{"__id__": 672}, {"__id__": 674}], "_prefab": {"__id__": 676}, "_lpos": {"__type__": "cc.Vec3", "x": 150, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 671}, "_enabled": true, "__prefab": {"__id__": 673}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1VbDmu8ZLPL0QeovM4XwQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 671}, "_enabled": true, "__prefab": {"__id__": 675}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "897fa295-fcb3-4615-9572-5d2cd1056943@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aLGuVjnxCmZod0VQD+4DV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f70PTg+xtI96VtzvMBDmJk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 664}, "_enabled": true, "__prefab": {"__id__": 678}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38744ORrpCRJG9Igt6qVky"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 664}, "_enabled": true, "__prefab": {"__id__": 680}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "据点详情", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfuEJBMelDUbnHhetcZlQE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "83TBaNga1FHakuzyc01UbH", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_qi_yun", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 663}, "_children": [{"__id__": 683}], "_active": true, "_components": [{"__id__": 689}, {"__id__": 691}], "_prefab": {"__id__": 693}, "_lpos": {"__type__": "cc.Vec3", "x": -278.868, "y": 66.78, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_qi_yun_value", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 682}, "_children": [], "_active": true, "_components": [{"__id__": 684}, {"__id__": 686}], "_prefab": {"__id__": 688}, "_lpos": {"__type__": "cc.Vec3", "x": 192.561, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 683}, "_enabled": true, "__prefab": {"__id__": 685}, "_contentSize": {"__type__": "cc.Size", "width": 97.49995422363281, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cadyzEFpNPz6jzJdX2YaRl"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 683}, "_enabled": true, "__prefab": {"__id__": 687}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 143, "b": 60, "a": 255}, "_string": "5195/秒", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8xfIzOalDvJRFyOJJAFgu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36igcKBWpNw5hwMx/yrahc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 682}, "_enabled": true, "__prefab": {"__id__": 690}, "_contentSize": {"__type__": "cc.Size", "width": 191.09999084472656, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "481pcHhRpE/ZFdq13Xr5Ao"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 682}, "_enabled": true, "__prefab": {"__id__": 692}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "建筑气运产出 ：", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9ucQdkfZDurTCOVNKrlei"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61ZMbhcj5BRrFbHkYFmBW2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 663}, "_children": [], "_active": true, "_components": [{"__id__": 695}, {"__id__": 697}], "_prefab": {"__id__": 699}, "_lpos": {"__type__": "cc.Vec3", "x": -278.868, "y": 54.448, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 694}, "_enabled": true, "__prefab": {"__id__": 696}, "_contentSize": {"__type__": "cc.Size", "width": 512.199951171875, "height": 149.95999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c15U/Pl81NW6V88b11Xafn"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 694}, "_enabled": true, "__prefab": {"__id__": 698}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "气运产出=基础气运产出x其他加成\n基础气运产出=族人气运x族人数量+战将加成\n战将加成=战将总战力/1000", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57vvscAIRE7Kadxk53+zVi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cjQX7aJhKO4VRl8gUGEci", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 663}, "_enabled": true, "__prefab": {"__id__": 701}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0esyT8EK5KBIWru1Ch0DGZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8XL54yn9Nj57wamsMPIi3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_title_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 662}, "_children": [{"__id__": 704}, {"__id__": 710}], "_active": false, "_components": [{"__id__": 716}, {"__id__": 718}], "_prefab": {"__id__": 720}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -61.11999999999997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 703}, "_children": [], "_active": true, "_components": [{"__id__": 705}, {"__id__": 707}], "_prefab": {"__id__": 709}, "_lpos": {"__type__": "cc.Vec3", "x": -90, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 704}, "_enabled": true, "__prefab": {"__id__": 706}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 12}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61F2qQ7kJLTIcX55GLru2y"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 704}, "_enabled": true, "__prefab": {"__id__": 708}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8bd324c-660f-4f00-b9e9-4b92e3b611c6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bAWv7EfdMfKnXgvNSuuq/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "93jFXf94RDCrhkfbvCAKwp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 703}, "_children": [], "_active": true, "_components": [{"__id__": 711}, {"__id__": 713}], "_prefab": {"__id__": 715}, "_lpos": {"__type__": "cc.Vec3", "x": 90, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 710}, "_enabled": true, "__prefab": {"__id__": 712}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 12}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afq4HwrgxDr7RhTzLU85tV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 710}, "_enabled": true, "__prefab": {"__id__": 714}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8bd324c-660f-4f00-b9e9-4b92e3b611c6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6W4Eglp5Om57bfxBYZXFO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "denw6xMudE7r2IN8Y7XLf4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 703}, "_enabled": true, "__prefab": {"__id__": 717}, "_contentSize": {"__type__": "cc.Size", "width": 103.98666204112936, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08SkPao8pPl6hNB7SZ7Fxc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 703}, "_enabled": true, "__prefab": {"__id__": 719}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "族人气运", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0f8HhZ8RDNbvX1T0WRU4x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "71mQLOEPxOgacarfj36KGw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout_base", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 662}, "_children": [{"__id__": 722}, {"__id__": 740}, {"__id__": 758}], "_active": false, "_components": [{"__id__": 776}, {"__id__": 778}], "_prefab": {"__id__": 780}, "_lpos": {"__type__": "cc.Vec3", "x": 50, "y": 3.8800000000000097, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 721}, "_children": [{"__id__": 723}], "_active": true, "_components": [{"__id__": 737}], "_prefab": {"__id__": 739}, "_lpos": {"__type__": "cc.Vec3", "x": -330, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 722}, "_children": [{"__id__": 724}], "_active": true, "_components": [{"__id__": 732}, {"__id__": 734}], "_prefab": {"__id__": 736}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_value", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 723}, "_children": [], "_active": true, "_components": [{"__id__": 725}, {"__id__": 727}, {"__id__": 729}], "_prefab": {"__id__": 731}, "_lpos": {"__type__": "cc.Vec3", "x": 64.426, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 724}, "_enabled": true, "__prefab": {"__id__": 726}, "_contentSize": {"__type__": "cc.Size", "width": 62.39198503795757, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fra4Gc39PkLXjoxoO73/p"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 724}, "_enabled": true, "__prefab": {"__id__": 728}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "+500", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ehp47gUBGB7rBCIEQ61jo"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 724}, "_enabled": true, "__prefab": {"__id__": 730}, "_alignFlags": 8, "_target": null, "_left": 64.426, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dvga+oa9E+6GccjfnSAsJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8JCtRXg1PULoGGqhR4iPj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 723}, "_enabled": true, "__prefab": {"__id__": 733}, "_contentSize": {"__type__": "cc.Size", "width": 51.99333102056468, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdiNPPvzBPPIEmuwO4Y1Xk"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 723}, "_enabled": true, "__prefab": {"__id__": 735}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "攻击", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14XMSgq15GRZY6h8+eDIpp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "804AbzK49Iop5t9SnvaPv+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 722}, "_enabled": true, "__prefab": {"__id__": 738}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "40VJIwPVFHlZAfxU14a1+E"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35D05D5mtPC7+phq+gB+xE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 721}, "_children": [{"__id__": 741}], "_active": true, "_components": [{"__id__": 755}], "_prefab": {"__id__": 757}, "_lpos": {"__type__": "cc.Vec3", "x": -110, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 740}, "_children": [{"__id__": 742}], "_active": true, "_components": [{"__id__": 750}, {"__id__": 752}], "_prefab": {"__id__": 754}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_value", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 741}, "_children": [], "_active": true, "_components": [{"__id__": 743}, {"__id__": 745}, {"__id__": 747}], "_prefab": {"__id__": 749}, "_lpos": {"__type__": "cc.Vec3", "x": 64.426, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 742}, "_enabled": true, "__prefab": {"__id__": 744}, "_contentSize": {"__type__": "cc.Size", "width": 62.39198503795757, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34IVijo7VDoIppT31q7nlj"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 742}, "_enabled": true, "__prefab": {"__id__": 746}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "+500", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fEJxbRctMt7igGtFzZc79"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 742}, "_enabled": true, "__prefab": {"__id__": 748}, "_alignFlags": 8, "_target": null, "_left": 64.426, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4246PlxEVKZ6ZdviXF1uKd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7dS8Pquq1I96bZXFuJND+I", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 751}, "_contentSize": {"__type__": "cc.Size", "width": 51.99333102056468, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91Qyg0quVFkIJpNVETwMPO"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 753}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "攻击", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0d01EuiZtAR4pMVrJg7h/R"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cci0umDB5DdrFaNiYifxwf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 740}, "_enabled": true, "__prefab": {"__id__": 756}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cYTenfaJC66cOkh3T+nHc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ed+pnGwrxC/bj+1cU/dxzh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 721}, "_children": [{"__id__": 759}], "_active": true, "_components": [{"__id__": 773}], "_prefab": {"__id__": 775}, "_lpos": {"__type__": "cc.Vec3", "x": 110, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 758}, "_children": [{"__id__": 760}], "_active": true, "_components": [{"__id__": 768}, {"__id__": 770}], "_prefab": {"__id__": 772}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_value", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 759}, "_children": [], "_active": true, "_components": [{"__id__": 761}, {"__id__": 763}, {"__id__": 765}], "_prefab": {"__id__": 767}, "_lpos": {"__type__": "cc.Vec3", "x": 64.426, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 760}, "_enabled": true, "__prefab": {"__id__": 762}, "_contentSize": {"__type__": "cc.Size", "width": 62.39198503795757, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85n2q5v9ZGvodpO3C8hBCJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 760}, "_enabled": true, "__prefab": {"__id__": 764}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "+500", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91d7jZqWlOKZ9nE63/knKB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 760}, "_enabled": true, "__prefab": {"__id__": 766}, "_alignFlags": 8, "_target": null, "_left": 64.426, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8WyNhIGZKRZ7zVFXDiKtB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8ed3mbU2dHK4LMi+TT36wa", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 759}, "_enabled": true, "__prefab": {"__id__": 769}, "_contentSize": {"__type__": "cc.Size", "width": 51.99333102056468, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0e3OD5R0tPNbOJnYLNIvNP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 759}, "_enabled": true, "__prefab": {"__id__": 771}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "攻击", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82Nu8OVlRKHI74ZBg/XINy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4fWRK+kJhOyZwy547Smuih", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 758}, "_enabled": true, "__prefab": {"__id__": 774}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09x2ubCqNIRa2Ply6buPun"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72z9E1VUZMV431FHuWTiYe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 721}, "_enabled": true, "__prefab": {"__id__": 777}, "_contentSize": {"__type__": "cc.Size", "width": 660, "height": 65}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaE+W29BlLsaLbxofgM5rQ"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 721}, "_enabled": true, "__prefab": {"__id__": 779}, "_resizeMode": 1, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 218, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 5, "_paddingBottom": 20, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 2, "_constraintNum": 3, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80bWgdQbdINaM3tN3hjrZz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2ckHJsLOhMFqK7lvniotOe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_title_3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 662}, "_children": [{"__id__": 782}, {"__id__": 788}], "_active": false, "_components": [{"__id__": 794}, {"__id__": 796}], "_prefab": {"__id__": 798}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -80.01999999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 781}, "_children": [], "_active": true, "_components": [{"__id__": 783}, {"__id__": 785}], "_prefab": {"__id__": 787}, "_lpos": {"__type__": "cc.Vec3", "x": -90, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 782}, "_enabled": true, "__prefab": {"__id__": 784}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 12}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6vj8oHlpO37LZGihvHgyv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 782}, "_enabled": true, "__prefab": {"__id__": 786}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8bd324c-660f-4f00-b9e9-4b92e3b611c6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7acmhZGZpEC6dXlmN1d2Iy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfgN4CqhJC6p4RFK7ojNnv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 781}, "_children": [], "_active": true, "_components": [{"__id__": 789}, {"__id__": 791}], "_prefab": {"__id__": 793}, "_lpos": {"__type__": "cc.Vec3", "x": 90, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 788}, "_enabled": true, "__prefab": {"__id__": 790}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 12}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32DLzWpf9NsIjE/9gA51Ei"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 788}, "_enabled": true, "__prefab": {"__id__": 792}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8bd324c-660f-4f00-b9e9-4b92e3b611c6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acNr7MrwZBPanD/P7Rnu9A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9fEsupwbZM9ao2byqCXI2o", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 781}, "_enabled": true, "__prefab": {"__id__": 795}, "_contentSize": {"__type__": "cc.Size", "width": 103.98666204112936, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65SMtg7SVPqqp5HRE1cQnG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 781}, "_enabled": true, "__prefab": {"__id__": 797}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "其他加成", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cjD7Zs8NAVYjZP3Jyg/Q8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54tLEW7q9HnpdtUpO/YBjo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout_other", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 662}, "_children": [{"__id__": 800}, {"__id__": 818}, {"__id__": 836}], "_active": false, "_components": [{"__id__": 854}, {"__id__": 856}], "_prefab": {"__id__": 858}, "_lpos": {"__type__": "cc.Vec3", "x": 50, "y": -98.91999999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 799}, "_children": [{"__id__": 801}], "_active": true, "_components": [{"__id__": 815}], "_prefab": {"__id__": 817}, "_lpos": {"__type__": "cc.Vec3", "x": -330, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attr1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 800}, "_children": [{"__id__": 802}], "_active": true, "_components": [{"__id__": 810}, {"__id__": 812}], "_prefab": {"__id__": 814}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attack_value", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 801}, "_children": [], "_active": true, "_components": [{"__id__": 803}, {"__id__": 805}, {"__id__": 807}], "_prefab": {"__id__": 809}, "_lpos": {"__type__": "cc.Vec3", "x": 64.426, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 802}, "_enabled": true, "__prefab": {"__id__": 804}, "_contentSize": {"__type__": "cc.Size", "width": 62.39198503795757, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59EznYE61MOZoXhzLk30GL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 802}, "_enabled": true, "__prefab": {"__id__": 806}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "+500", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71Xq+xiblInY+XAjARTbI/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 802}, "_enabled": true, "__prefab": {"__id__": 808}, "_alignFlags": 8, "_target": null, "_left": 64.426, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eZaGKwQ5Mv6pLR0PPblnl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daQeGdBXRLwrvREugRFoLI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 801}, "_enabled": true, "__prefab": {"__id__": 811}, "_contentSize": {"__type__": "cc.Size", "width": 51.99333102056468, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74Iim4RNxA5oi5tsnxGukj"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 801}, "_enabled": true, "__prefab": {"__id__": 813}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "攻击", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76CBCrdQxOXaiHPll73G0R"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d7LUPI6tZJF7nKb8OVZnEI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 800}, "_enabled": true, "__prefab": {"__id__": 816}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3lJpOhOZJi6tjj4/NZeTf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbdLD72hZBH6OFjPhPI5eX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 799}, "_children": [{"__id__": 819}], "_active": true, "_components": [{"__id__": 833}], "_prefab": {"__id__": 835}, "_lpos": {"__type__": "cc.Vec3", "x": -110, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attr1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 818}, "_children": [{"__id__": 820}], "_active": true, "_components": [{"__id__": 828}, {"__id__": 830}], "_prefab": {"__id__": 832}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attack_value", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 819}, "_children": [], "_active": true, "_components": [{"__id__": 821}, {"__id__": 823}, {"__id__": 825}], "_prefab": {"__id__": 827}, "_lpos": {"__type__": "cc.Vec3", "x": 64.426, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 820}, "_enabled": true, "__prefab": {"__id__": 822}, "_contentSize": {"__type__": "cc.Size", "width": 62.39198503795757, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6ywp6jOJLvrom3W/y/mdh"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 820}, "_enabled": true, "__prefab": {"__id__": 824}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "+500", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "399DhQJJRMjYAt22MQ7yyq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 820}, "_enabled": true, "__prefab": {"__id__": 826}, "_alignFlags": 8, "_target": null, "_left": 64.426, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9a5Kfjj7JPKJK11CRRxFIr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4aNOZJixNHgaqL6kY4j7fh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 819}, "_enabled": true, "__prefab": {"__id__": 829}, "_contentSize": {"__type__": "cc.Size", "width": 51.99333102056468, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37fsulEAtADaPPMIwjySWL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 819}, "_enabled": true, "__prefab": {"__id__": 831}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "攻击", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42/192DIpJG7Cr1A7hNQQw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dbEWMkz3NBpL4a8dR98YO5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 818}, "_enabled": true, "__prefab": {"__id__": 834}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4abIs0uZtE0qdfrrkBEHkn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "03pn9M/9hCHr0pnl8blfsM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 799}, "_children": [{"__id__": 837}], "_active": true, "_components": [{"__id__": 851}], "_prefab": {"__id__": 853}, "_lpos": {"__type__": "cc.Vec3", "x": 110, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attr1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 836}, "_children": [{"__id__": 838}], "_active": true, "_components": [{"__id__": 846}, {"__id__": 848}], "_prefab": {"__id__": 850}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_attack_value", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 837}, "_children": [], "_active": true, "_components": [{"__id__": 839}, {"__id__": 841}, {"__id__": 843}], "_prefab": {"__id__": 845}, "_lpos": {"__type__": "cc.Vec3", "x": 64.426, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 838}, "_enabled": true, "__prefab": {"__id__": 840}, "_contentSize": {"__type__": "cc.Size", "width": 62.39198503795757, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "df841le2BDHqnND6o8wazL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 838}, "_enabled": true, "__prefab": {"__id__": 842}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "+500", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bansMFSPBIUL6SCPLJemN+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 838}, "_enabled": true, "__prefab": {"__id__": 844}, "_alignFlags": 8, "_target": null, "_left": 64.426, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13mEzW2gdGGraej/jdQFTj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0dcIhErBP440GRD9H+1Oq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 837}, "_enabled": true, "__prefab": {"__id__": 847}, "_contentSize": {"__type__": "cc.Size", "width": 51.99333102056468, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "453tki2ypKnaCUz6D9nWwz"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 837}, "_enabled": true, "__prefab": {"__id__": 849}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "攻击", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26.043333333333333, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4abrciZM5CLpN/xvJ3T47J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8e/HpA+29MBp6Eg2Bx/zFL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 836}, "_enabled": true, "__prefab": {"__id__": 852}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81+uo3d2hNQKQMmVJqYN6p"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aauYM4IvtAdaHtrZf9P6cx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 799}, "_enabled": true, "__prefab": {"__id__": 855}, "_contentSize": {"__type__": "cc.Size", "width": 660, "height": 65}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04nYAko11CmoQUvpq9qmtp"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 799}, "_enabled": true, "__prefab": {"__id__": 857}, "_resizeMode": 1, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 218, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 5, "_paddingBottom": 20, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 2, "_constraintNum": 3, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12h/6FQFtK0IFn4MdhbJh5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aa5Jl/3npCcKZ7K6MX9kiI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_title_4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 662}, "_children": [{"__id__": 860}, {"__id__": 866}], "_active": true, "_components": [{"__id__": 872}, {"__id__": 874}], "_prefab": {"__id__": 876}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -103.02000000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 859}, "_children": [], "_active": true, "_components": [{"__id__": 861}, {"__id__": 863}], "_prefab": {"__id__": 865}, "_lpos": {"__type__": "cc.Vec3", "x": -90, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 860}, "_enabled": true, "__prefab": {"__id__": 862}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 12}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50q/6IUnFN55ElAeEXg18R"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 860}, "_enabled": true, "__prefab": {"__id__": 864}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8bd324c-660f-4f00-b9e9-4b92e3b611c6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a850gZR3BKk54fc3yyk+IS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27dVg076ZGgJFY81ja78Eb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 859}, "_children": [], "_active": true, "_components": [{"__id__": 867}, {"__id__": 869}], "_prefab": {"__id__": 871}, "_lpos": {"__type__": "cc.Vec3", "x": 90, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 866}, "_enabled": true, "__prefab": {"__id__": 868}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 12}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbY1gnUeVMPratSaU0qDUu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 866}, "_enabled": true, "__prefab": {"__id__": 870}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8bd324c-660f-4f00-b9e9-4b92e3b611c6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c00wrSH1tNhplj0LUUkO/N"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98oJu0pBdM/rHINU8fFYhC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 859}, "_enabled": true, "__prefab": {"__id__": 873}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66FfnoFvVO45TIyg6hmltE"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 859}, "_enabled": true, "__prefab": {"__id__": 875}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "据点简介", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d42Abwn3FCBJ55JLktm72i"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceMMFW2MhDU5jbe9oJ6l2V", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_desc", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 662}, "_children": [], "_active": true, "_components": [{"__id__": 878}, {"__id__": 880}], "_prefab": {"__id__": 882}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -121.92000000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 877}, "_enabled": true, "__prefab": {"__id__": 879}, "_contentSize": {"__type__": "cc.Size", "width": 570, "height": 103.95999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fj0J3d6FJZppgZQigh7/U"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 877}, "_enabled": true, "__prefab": {"__id__": 881}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "位于昆仑山脚，乃三界灵气聚合之地，大量人族涌聚，周边更是妖物环伺，虎视眈眈；", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44UBmbFUZH5YoDJ3/QeK9L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbPSzsEm1Fy5Ka00wTXM6y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 662}, "_enabled": true, "__prefab": {"__id__": 884}, "_contentSize": {"__type__": "cc.Size", "width": 645, "height": 491.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8uDXNuCdCH6V5Xeq6Apxi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 662}, "_enabled": true, "__prefab": {"__id__": 886}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e2efc2c1-2d71-491c-8c2e-b7e8309ea73c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3d15wQTS9O5poxRlyn1PwP"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 662}, "_enabled": true, "__prefab": {"__id__": 888}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 10, "_paddingBottom": 20, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19EBHW2hVDHbKSSqKB8ehA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48uuNJiD9GuKGFXz+nsT7e", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 891}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9uSwn5SpAl4EXWnBpCf/q"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 893}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9jvoX1mJDPIAHFfq8DRTE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06Nusb8hRFhZVsgMDdRtYA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_auto_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 896}, {"__id__": 898}, {"__id__": 900}], "_prefab": {"__id__": 902}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 895}, "_enabled": true, "__prefab": {"__id__": 897}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7FXgz+5lOzKSNgnaZp8k7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 895}, "_enabled": true, "__prefab": {"__id__": 899}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17HXwG9NpC1KYGUi/IpqO6"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 895}, "_enabled": true, "__prefab": {"__id__": 901}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14VnQ6mrtPKZPKGzPz15Rq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffToaUQ71P7KYLUJg5Ld4q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 904}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17btq+rthPSpMQ+2bGgtem"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 906}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 1500, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5cuZlbUZLq6bKQQFWknpr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 908}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "de39ibm9xFqKtFdWGsup6J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8kq1zg7BAHICrUKQdlXu4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 563}, {"__id__": 339}]}]