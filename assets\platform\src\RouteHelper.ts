import { Prefab, instantiate, Node, AssetManager, isValid } from "cc";

import { BundleEnum, <PERSON>sHel<PERSON> } from "./ResHelper";
import { EventMgr, MsgEnum } from "./EventHelper";
import { MaskCtrl } from "./ctrl/MaskCtrl";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
// 路由状态
export enum RouteStatusEnum {
  LOADING = 0,
  SHOW = 1,
  HIDE = 2,
  DESTORY = 3,
}

// 路由类型
export enum RouteTypeEnum {
  DIALOG = 0,
  PAGE = 1,
}

// 路由结构
export interface RouteItem {
  // 键名
  key: string;
  // bundle名
  bundle: string;
  // 资源路径
  url: string;
  // 初始化的参数
  args?: any;
  // 初始化节点
  node?: Node;
  // 路由状态
  status?: RouteStatusEnum;
  // 类型
  type?: RouteTypeEnum;
  // 关闭回调
  closeFunction?: Function;
}

// 登录模块路由配置
export const RoutePublic = {
  mask: {
    key: `${BundleEnum.BUNDLE_PUB}/prefab/Mask`,
    bundle: BundleEnum.BUNDLE_PUB,
    url: "prefab/Mask",
  } as RouteItem,
};

export function createRouteItem(bundleName: string, url: string, args = {}): RouteItem {
  return Object.assign({ key: `${bundleName}/${url}`, bundle: bundleName, url: url }, args);
}

// 路由管理器
export class RouteMgr {
  // 路由根节点
  public nodeRoute: Node;

  // 路由堆栈
  private _routeStack: RouteItem[] = [];

  // 初始化信息
  public static create(nodeRoute: Node): RouteMgr {
    let instance = new RouteMgr();
    instance.nodeRoute = nodeRoute;
    return instance;
  }

  /** 按key找节点，重复问题暂未处理 */
  public findByKey(key: string) {
    return this._routeStack.find((route) => route.key === key);
  }

  /**
   * 打开对话框
   * @param route 要打开的路由
   * @param closeFunction 返回回调
   * @param afterShowFunction 显示后的回调
   * @returns
   */
  public showDialog(route: RouteItem, closeFunction?: Function, afterShowFunction?: Function) {
    this.show(route, RouteTypeEnum.DIALOG, closeFunction, afterShowFunction);
  }

  /**
   * 打开页面
   * @param route 要打开的路由
   * @param closeFunction 返回回调
   * @param afterShowFunction 显示后的回调
   * @returns
   */
  public showPage(route: RouteItem, closeFunction?: Function, afterShowFunction?: Function) {
    this.show(route, RouteTypeEnum.PAGE, closeFunction, afterShowFunction);
  }

  /**
   * 打开prefab
   * @param route 要打开的路由
   * @param pageType 页面类型
   * @param closeFunction 返回回调
   * @param afterShowFunction 显示后的回调
   * @returns
   */
  public show(route: RouteItem, pageType: RouteTypeEnum, closeFunction?: Function, afterShowFunction?: Function) {
    let routeExist = this.findByKey(route.key);
    if (routeExist && routeExist.status !== RouteStatusEnum.SHOW) {
      return;
    }

    // 复制路由具体信息
    let routeCopy = JSON.parse(JSON.stringify(route));
    routeCopy.status = RouteStatusEnum.LOADING;
    routeCopy.type = pageType;
    routeCopy.closeFunction = closeFunction;

    this._routeStack.push(routeCopy);

    ResHelper.loadBundle(routeCopy.bundle, (bundle: AssetManager.Bundle) => {
      bundle.preload(routeCopy.url, Prefab);

      bundle.load(routeCopy.url, Prefab, (err, prefab) => {
        if (err) {
          log.error(err);
          return;
        }

        if (!isValid(this.nodeRoute)) {
          log.error("路由-父节点无效");
          return;
        }

        // 创建节点
        const node = instantiate(prefab);
        node["routeItem"] = routeCopy;

        node.setParent(this.nodeRoute);
        node.active = true;
        routeCopy.node = node;

        // 更新遮罩
        if (routeCopy.key != RoutePublic.mask.key) {
          this.updateMaskIndex();
        }

        // 如果会出现快速点击多开的情况，尝试延后设置状态
        setTimeout(() => {
          routeCopy.status = RouteStatusEnum.SHOW;
          afterShowFunction && afterShowFunction();
          EventMgr.emit(MsgEnum.ROUTE_UPDATE, routeCopy);
        }, 1);
      });
    });
  }

  // 替换当前路由
  public replaceDailog(route: RouteItem, afterShowFunction?: Function) {
    let routeRemove: RouteItem;
    if (this._routeStack.length > 0) {
      routeRemove = this._routeStack[this._routeStack.length - 1];
    }

    this.showDialog(route, null, () => {
      this.removeByKey(routeRemove.key);
      afterShowFunction && afterShowFunction();
    });
  }

  // 路由回退
  public back(args: any = {}) {
    // 不存在路由栈，直接返回
    if (this._routeStack.length <= 0) return;

    const routeItem = this._routeStack[this._routeStack.length - 1];

    // 空为异常，直接取出
    if (!routeItem) {
      this._routeStack.pop();
    }

    // 加载中的不回退
    if (routeItem.status != RouteStatusEnum.SHOW) {
      return;
    }

    // 释放路由
    this.removeByKey(routeItem.key, args);
  }

  // 关闭第一个找到的路由
  private removeByKey(key: string, args: any = {}) {
    for (let i = this._routeStack.length - 1; i >= 0; i--) {
      let routeRemove = this._routeStack[i];
      if (key == routeRemove.key) {
        if (routeRemove.status == RouteStatusEnum.SHOW) {
          this._routeStack.splice(i, 1);
          routeRemove.closeFunction && routeRemove.closeFunction(args);
          routeRemove.node.removeFromParent();
          this.updateMaskIndex();

          // 关闭route不是遮罩，发事件通知
          if (routeRemove.key != RoutePublic.mask.key) {
            EventMgr.emit(MsgEnum.ROUTE_UPDATE, this._routeStack[this._routeStack.length - 1]);
          }
        }
        return;
      }
    }
  }

  // 更新遮罩
  private updateMaskIndex() {
    // 是否需要遮罩
    let routeLast: RouteItem;
    for (let i = this._routeStack.length - 1; i >= 0; i--) {
      let routeIdx = this._routeStack[i];
      if (routeIdx.type == RouteTypeEnum.DIALOG && routeIdx.key != RoutePublic.mask.key && routeIdx.node) {
        routeLast = routeIdx;
        break;
      }
    }

    const routeMask = this.findByKey(RoutePublic.mask.key);

    // 不需要，就退出
    if (!routeLast) {
      // 退出前检查是否已经存在，存在就删除mask
      if (routeMask) {
        this.removeByKey(RoutePublic.mask.key);
      }
      return;
    }

    // 遮罩存在，就设置位置
    if (routeMask) {
      // 设置
      routeMask.node.getComponent(MaskCtrl).routeMgr = this;

      routeMask.node.parent = routeLast.node.parent;
      // 自动关闭
      routeMask.args = { clickClose: routeLast.args?.clickClose || false };

      let dialogIdx = routeLast.node.getSiblingIndex();
      let maskIdx = routeMask.node.getSiblingIndex();
      if (dialogIdx < maskIdx) {
        routeMask.node.setSiblingIndex(dialogIdx);
      } else {
        routeMask.node.setSiblingIndex(dialogIdx - 1);
      }

      EventMgr.emit(MsgEnum.ROUTE_UPDATE, routeMask);
      // 排序
      this._routeStack.sort((a, b) => {
        if (isValid(a.node) && isValid(b.node)) {
          return a.node.getSiblingIndex() - b.node.getSiblingIndex();
        }
        return 0;
      });

      return;
    }

    this.showDialog(RoutePublic.mask, null, () => {
      this.updateMaskIndex();
    });
  }
}
