import { _decorator, Label, Node, UITransform } from "cc";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { CityModule } from "../../../module/city/CityModule";

const { ccclass, property } = _decorator;

@ccclass("UILevelDroupLook")
export class UILevelDroupLook extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FIGHT}?prefab/ui/UILevelDroupLook`;
  }

  protected _isAddToTop: boolean = true;

  private _tickIdList: { [key: number]: number } = Object.create(null);

  private _nodeIndex: number = 0;

  private loadMap: Map<number, boolean> = new Map<number, boolean>();
  protected onEvtShow(): void {
    let c_event = JsonMgr.instance.jsonList.c_event;
    let list = Object.keys(c_event);
    for (let i = 0; i < list.length; i++) {
      let data = c_event[list[i]];
      this.loadCityReward(data);
    }
  }

  private loadCityReward(data) {
    let rewardRateList = data.rewardRateList;
    let cityInfo = JsonMgr.instance.jsonList.c_build[data.id];
    for (let i = 0; i < rewardRateList.length; i++) {
      let info = rewardRateList[i];
      let id = info[0];
      if (this.loadMap.has(id)) {
        continue;
      }
      this.loadMap.set(id, true);
      let tickeriId = TickerMgr.setTimeout(0.016 * this._nodeIndex, () => {
        let node = ToolExt.clone(this["item"], this);
        this["emptyLay"].addChild(node);
        node.active = true;

        let itemInfo = JsonMgr.instance.getConfigItem(id);

        ToolExt.setItemBg(node["bg2"], itemInfo.color, this);
        ToolExt.setItemIcon(node["itemIcon2"], itemInfo.id, this);

        let playerCityInfo = CityModule.data.cityMessageMap.get(cityInfo.id);

        if (playerCityInfo) {
          let name = node["name2"];
          name.getComponent(Label).string = "已解锁";
          name.active = true;
        } else {
          let need = node["need"];
          need.getComponent(Label).string = "通关" + cityInfo.name + "解锁";
          need.active = true;
        }

        this.setLay(node.getChildByName("bg1"), this["content"].getChildByName("bgLay"));
        this.setLay(node.getChildByName("itemIcon1"), this["content"].getChildByName("iconLay"));
        this.setLay(node.getChildByName("name1"), this["content"].getChildByName("nameLay"));
        delete this._tickIdList[tickeriId];
        TickerMgr.clearTimeout(tickeriId);
      });
      this._tickIdList[tickeriId] = tickeriId;
      this._nodeIndex++;
    }
  }

  public tick(dt: number): void {
    if (
      this["content"].getComponent(UITransform).contentSize.height !=
      this["emptyLay"].getComponent(UITransform).contentSize.height
    ) {
      this["content"].getComponent(UITransform).setContentSize(this["emptyLay"].getComponent(UITransform).contentSize);
    }
  }

  private setLay(valNode: Node, parentN: Node) {
    valNode.setParent(parentN);
  }

  protected onEvtClose(): void {
    let list = Object.keys(this._tickIdList);
    for (let i = 0; i < list.length; i++) {
      TickerMgr.clearTimeout(this._tickIdList[list[i]]);
    }
  }
}
