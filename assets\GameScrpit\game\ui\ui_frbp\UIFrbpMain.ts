import { _decorator, Component, Label, Node, RichText } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import TipMgr from "../../../lib/tips/TipMgr";
import { activityId, FrbpModule } from "../../../module/frbp/FrbpModule";
import { FrbpRouteItem } from "../../../module/frbp/FrbpRoute";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ToolExt from "../../common/ToolExt";
import Formate from "../../../lib/utils/Formate";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIFrbpMain")
export class UIFrbpMain extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FRBP}?prefab/ui/UIFrbpMain`;
  }

  private _vo: any = null;

  protected async onEvtShow() {
    TipsMgr.setEnableTouch(false, 60);
    this.getNode("bottom_layer").active = false;
    this._vo = await FrbpModule.data.getVO(
      activityId,
      () => {
        TipsMgr.setEnableTouch(true);
      },
      () => {
        UIMgr.instance.back();
        TipsMgr.setEnableTouch(true);
      }
    );
    TipsMgr.setEnableTouch(true);

    log.log("繁荣比拼配置输出======", this._vo);
    this.setTime();
    this.getNode("bottom_layer").active = true;
    FrbpModule.api.getRankList(activityId, this.initMain.bind(this));
  }

  private initMain() {
    this.setMyInfo();
    this.setRankList(0);
    this.setRankList(1);
    this.setRankList(2);
  }

  private setTime() {
    let startTime = TimeUtils.formatTimestamp(this._vo.startTime, "YYYY/MM/DD");
    let publicityTime = TimeUtils.formatTimestamp(this._vo.publicityTime, "YYYY/MM/DD");
    this.getNode("lbl_ac_time").getComponent(Label).string = "活动时间:" + startTime + "--" + publicityTime;
  }

  private setMyInfo() {
    this.getNode("lbl_my_rank").getComponent(Label).string = String(FrbpModule.data.SimpleRankMessage.rank);
    this.getNode("lbl_frd_zf").getComponent(Label).string =
      "繁荣度涨幅:" + Formate.format(FrbpModule.data.SimpleRankMessage.point);
  }

  private setRankList(index: number) {
    if (index >= FrbpModule.data.SimpleRankMessage.rankList.length) {
      return;
    }

    let rankInfo = FrbpModule.data.SimpleRankMessage.rankList[index];
    let node = this.getNode("role_point_" + index);
    node.getChildByPath("ming_ci/Label").getComponent(Label).string = rankInfo.detailMessage.simpleMessage.nickname;

    let avatarList = rankInfo.detailMessage.simpleMessage.avatarList;
    if (avatarList[3] != -1) {
      node.getChildByName("lv_root").active = false;
      node.getChildByName("title_root").active = true;
      PlayerModule.service.createTitle(node.getChildByName("title_root"), avatarList[3], (node: Node, db) => {});
    } else {
      node.getChildByName("lv_root").active = true;
      node.getChildByName("title_root").active = false;
      let level = rankInfo.detailMessage.simpleMessage.level;
      let info = PlayerModule.data.getConfigLeaderData(level);
      node.getChildByPath("lv_root/lab_lv_jingjie").getComponent(RichText).string = info.jingjie2;
    }

    ToolExt.loadUIRole(
      node.getChildByName("role_root"),
      avatarList[0] == -1 ? 1701 : avatarList[0],
      rankInfo.detailMessage.horseMessage.horseId,
      "renderScale23",
      this
    );
  }

  private on_click_btn_op_award() {
    AudioMgr.instance.playEffect(2001);
    UIMgr.instance.showDialog(FrbpRouteItem.UIFrbpAward, { vo: this._vo });
  }

  private on_click_btn_op_rank() {
    AudioMgr.instance.playEffect(2002);
    UIMgr.instance.showDialog(FrbpRouteItem.UIFrbpRank, { vo: this._vo });
  }

  private on_click_btn_op_lb() {
    AudioMgr.instance.playEffect(2003);
    UIMgr.instance.showDialog(FrbpRouteItem.UIFrbpLb, { vo: this._vo });
  }

  private on_click_btn_op_cj() {
    AudioMgr.instance.playEffect(2004);
    UIMgr.instance.showDialog(FrbpRouteItem.UIFrbpCj, { vo: this._vo });
  }

  private on_click_btn_op_ts() {
    AudioMgr.instance.playEffect(2005);
    UIMgr.instance.showDialog(FrbpRouteItem.UIFrbpTs, { vo: this._vo });
  }

  private on_click_btn_op_shop() {
    AudioMgr.instance.playEffect(2006);
    UIMgr.instance.showDialog(FrbpRouteItem.UIFrbpSd, { vo: this._vo });
  }

  private on_click_btn_back() {
    AudioMgr.instance.playEffect(520);
    UIMgr.instance.back();
  }

  private on_click_btn_help() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 22 });
  }
}
