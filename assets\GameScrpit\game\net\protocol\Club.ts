// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Club.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RewardMessage } from "./Comm";
import { PlayerSimpleMessage } from "./Player";

export const protobufPackage = "sim";

/**  */
export interface AuditOptionMessage {
  /** 手动审核 */
  manual: boolean;
  /** 自动拒绝 */
  autoRefuse: boolean;
  /** 等级下限 (单位万 ，如10万，这里值为10) */
  levelLowerLimit: number;
  /** 战力下限 (单位万 ，如10万，这里值为10) */
  powerLowerLimit: number;
}

/**  */
export interface BargainRecordMessage {
  userId: number;
  /** 角色名称 */
  nickname: string;
  /** 砍掉的价格 */
  deductPrice: number;
  /** 是否付费 */
  paid: boolean;
  /** 砍价时间 */
  createTime: number;
}

/**  */
export interface ClubActiveMessage {
  /** 当日活跃度 */
  activeVal: number;
  /** 领取奖励的活跃度索引集合 */
  activeTaskList: number[];
}

/**  */
export interface ClubAdjustPositionMessage {
  userId: number;
  position: number;
}

/**  */
export interface ClubApplyMessage {
  /** 玩家信息 */
  simpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 审核的截止时间 */
  deadline: number;
}

/**  */
export interface ClubBargainMessage {
  /** 触发的砍价商品ID，如果小于等于0说明没有触发砍价活动，以下属性无效 */
  bargainId: number;
  /** 砍价参与日志 */
  recordMap: { [key: number]: BargainRecordMessage };
  /** 结束时间 */
  endTime: number;
}

export interface ClubBargainMessage_RecordMapEntry {
  key: number;
  value: BargainRecordMessage | undefined;
}

/**  */
export interface ClubBasicInfoMessage {
  /** 仙盟名称 */
  name: string;
  /** 口号 */
  slogan: string;
  /** 旗帜 */
  avatar: string;
}

/**  */
export interface ClubBossBuddyMessage {
  /**  */
  bossTrain:
    | ClubBossTrainMessage
    | undefined;
  /** 战友信息 */
  buddyList: PlayerSimpleMessage[];
}

/**  */
export interface ClubBossChanceMessage {
  /** 剩余免费挑战次数 */
  remainCnt: number;
  /** 剩余的道具挑战次数 */
  remainItemCnt: number;
}

/**  */
export interface ClubBossResponse {
  /** 是否胜利 */
  isWin: boolean;
  /** 录像回放 */
  replay: string;
  /** 对BOSS造成的伤害 */
  damage: number;
  /** 今日贡献 */
  todayContribute: number;
  /** 历史贡献 */
  totalContribute: number;
  /** 获取的资源 */
  resAddList: number[];
  /** 战友信息 */
  buddyList: PlayerSimpleMessage[];
  /** 剩余的挑战次数 */
  bossChance:
    | ClubBossChanceMessage
    | undefined;
  /** BOSS的信息 */
  bossTrain:
    | ClubBossTrainMessage
    | undefined;
  /** 已经解锁的BOSS数量 */
  unlockBossCnt: number;
}

/**  */
export interface ClubBossTrainMessage {
  /** 号召的英雄的索引 -1则还未召唤 */
  bossIndex: number;
  /** 剩余的BOSS血量 */
  remainHp: number;
  /** 造成的总伤害血量 */
  damageHp: number;
  /** 击杀的玩家ID 当remainHp=0时才有值，且当血量为0时，切换到血魂状态 */
  killUserMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 是否有领取击杀奖励 */
  isTakeKill: boolean;
  /** 是否有参与战斗 */
  isHurt: boolean;
  /** 已经领取过的BOSS总伤害的奖励集合 */
  hurtRewardIndexList: number[];
}

/**  */
export interface ClubCreateRequest {
  /** 仙盟名称 */
  name: string;
  /** 口号 */
  slogan: string;
  /** 旗帜 */
  avatar: string;
}

/**  */
export interface ClubDeadRewardResponse {
  /** 获取的资源 */
  resAddList: number[];
  /** BOSS的信息 */
  isTakeKill: boolean;
}

/**  */
export interface ClubDonateMessage {
  /** 完成了几次 从0开始 */
  count: number;
  /** 下一次可以捐献的最早时间 默认从0开始 */
  nextDonateDeadline: number;
}

/**  */
export interface ClubExamineMessage {
  /** 0 - 审核拒绝 (clubMessage无值)  1 - 审核同意且用户加入成功(clubMessage有值) 2 - 审核同意但用户已加入其他仙盟(clubMessage无值) */
  code: number;
  /** 仙盟信息 */
  clubMessage: ClubMessage | undefined;
}

/**  */
export interface ClubExamineRequest {
  /** 申请的用户ID */
  userId: number;
  /** 审核通过与否 */
  pass: boolean;
}

/**  */
export interface ClubFormMessage {
  /** key-clubId val:申请加入仙盟的截止时间 */
  applyMap: { [key: number]: number };
  /** 申请加入新的仙盟的冷却时间 */
  joinColdStamp: number;
  /** 加入的仙盟 小于等于0表示还未加入仙盟 */
  clubId: number;
  /** 加入的仙盟信息 只有当clubId不为-1时有值 */
  clubMessage:
    | ClubMessage
    | undefined;
  /** 距离隔天重置的倒计时 (活跃度任务右侧栏的倒计时时间) */
  remainResetStamp: number;
  /** 剩余的打BOSS的次数 */
  bossChance:
    | ClubBossChanceMessage
    | undefined;
  /** 活跃 */
  active:
    | ClubActiveMessage
    | undefined;
  /** 仙盟任务完成情况 */
  dailyTask:
    | ClubTaskMessage
    | undefined;
  /** 捐献任务的进度 key:配置表ID */
  donateMap: { [key: number]: ClubDonateMessage };
  /** 被踢出仙盟的弹窗信息，如果有就不是空的 */
  popUpMessage: ClubPopUpMessage | undefined;
}

export interface ClubFormMessage_ApplyMapEntry {
  key: number;
  value: number;
}

export interface ClubFormMessage_DonateMapEntry {
  key: number;
  value: ClubDonateMessage | undefined;
}

/**  */
export interface ClubHurtRewardRequest {
  bossId: number;
  /** 伤害阶段的索引 从0开始 */
  hurtIndex: number;
}

/**  */
export interface ClubHurtRewardResponse {
  /** 只返回领取的BOSS伤害奖励的变动情况 */
  hurtRewardTakeList: number[];
  /** 获取的资源 */
  resAddList: number[];
}

/**  */
export interface ClubJoinResponse {
  /** 0 - 成功加入 ,1 - 待审核 2-被拒绝 */
  code: number;
  /** 申请的记录和时长 */
  applyMap: { [key: number]: number };
  /** 成功加入或已加入仙盟后，加入的仙盟的信息,前端需要更新clubID属性 */
  clubMessage: ClubMessage | undefined;
}

export interface ClubJoinResponse_ApplyMapEntry {
  key: number;
  value: number;
}

/**  */
export interface ClubLogMessage {
  /** 1 - 捐献 2 - BOSS召唤 3 - 加入仙盟 4 - 退出仙盟 5-BOSS击杀 */
  eventType: number;
  /** 用户 */
  name: string;
  /** 数值参数1 捐献-仙玉数量 / BOSS召唤(击杀)-BOSS索引 */
  param1: number;
  /** 数值参数2 捐献-经验值 */
  param2: number;
  /** 发生时间 */
  timeStamp: number;
}

/**  */
export interface ClubMemberMessage {
  /** 玩家信息 */
  simpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 是否在线 */
  isOnline: boolean;
  /** 职位 */
  position: number;
  /** 今日贡献 */
  todayContribute: number;
  /** 历史贡献 */
  totalContribute: number;
  /** 加入仙盟时间 */
  joinTime: number;
  /** 当日活跃度 */
  activeVal: number;
}

/**  */
export interface ClubMessage {
  /** 仙盟ID */
  id: number;
  /** 仙盟名称 */
  name: string;
  /** 口号 */
  slogan: string;
  /** 旗帜 */
  avatar: string;
  /** 服务器名称 */
  serverName: string;
  /** 等级 */
  level: number;
  /** 仙盟经验 */
  exp: number;
  /** 总战力 */
  totalPower: number;
  /** 杀死的BOSS数量 */
  killBossCnt: number;
  /** 已经解锁的BOSS数量 */
  unlockBossCnt: number;
  /** 审核设置 */
  auditOption:
    | AuditOptionMessage
    | undefined;
  /** 联盟砍价 */
  bargain:
    | ClubBargainMessage
    | undefined;
  /** 成员的信息 */
  memberList: ClubMemberMessage[];
}

/**  */
export interface ClubPopUpMessage {
  /** 战盟名称 */
  clubName: string;
}

/**  */
export interface ClubRankMessage {
  /** 如果积分为0，就说明没有加入仙盟 */
  point: number;
  /** 未上榜 -1 */
  rank: number;
  rankList: ClubSimplePowerMessage[];
}

/**  */
export interface ClubRewardMessage {
  /** 获得的奖励 弹窗显示 */
  rewardMessage:
    | RewardMessage
    | undefined;
  /** 捐献进度 key:配置的ID */
  donateMap: { [key: number]: ClubDonateMessage };
  /** 活跃 */
  active:
    | ClubActiveMessage
    | undefined;
  /** 日常任务的完成情况 */
  dailyTask:
    | ClubTaskMessage
    | undefined;
  /** 仙盟等级 */
  clubLevel: number;
  /** 仙盟的总经验 */
  clubExp: number;
  /** 今日贡献 */
  todayContribute: number;
  /** 历史贡献 */
  totalContribute: number;
  /** 是否触发神秘商人(只在领取活动度任务奖励时有效) */
  trigger: boolean;
}

export interface ClubRewardMessage_DonateMapEntry {
  key: number;
  value: ClubDonateMessage | undefined;
}

/**  */
export interface ClubSimplePowerMessage {
  /** 仙盟ID */
  id: number;
  /** 仙盟名称 */
  name: string;
  /** 口号 */
  slogan: string;
  /** 旗帜 */
  avatar: string;
  /** 服务器名称 */
  serverName: string;
  /** 等级 */
  level: number;
  /** 总战力 */
  totalPower: number;
}

/**  */
export interface ClubTaskMessage {
  /** 子任务及其完成次数 */
  taskCntMap: { [key: number]: number };
  /** 领取过仙盟奖励的任务ID集合 */
  completeTaskList: number[];
}

export interface ClubTaskMessage_TaskCntMapEntry {
  key: number;
  value: number;
}

function createBaseAuditOptionMessage(): AuditOptionMessage {
  return { manual: false, autoRefuse: false, levelLowerLimit: 0, powerLowerLimit: 0 };
}

export const AuditOptionMessage: MessageFns<AuditOptionMessage> = {
  encode(message: AuditOptionMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.manual !== false) {
      writer.uint32(8).bool(message.manual);
    }
    if (message.autoRefuse !== false) {
      writer.uint32(16).bool(message.autoRefuse);
    }
    if (message.levelLowerLimit !== 0) {
      writer.uint32(24).int32(message.levelLowerLimit);
    }
    if (message.powerLowerLimit !== 0) {
      writer.uint32(32).int64(message.powerLowerLimit);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AuditOptionMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAuditOptionMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.manual = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.autoRefuse = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.levelLowerLimit = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.powerLowerLimit = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AuditOptionMessage>, I>>(base?: I): AuditOptionMessage {
    return AuditOptionMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuditOptionMessage>, I>>(object: I): AuditOptionMessage {
    const message = createBaseAuditOptionMessage();
    message.manual = object.manual ?? false;
    message.autoRefuse = object.autoRefuse ?? false;
    message.levelLowerLimit = object.levelLowerLimit ?? 0;
    message.powerLowerLimit = object.powerLowerLimit ?? 0;
    return message;
  },
};

function createBaseBargainRecordMessage(): BargainRecordMessage {
  return { userId: 0, nickname: "", deductPrice: 0, paid: false, createTime: 0 };
}

export const BargainRecordMessage: MessageFns<BargainRecordMessage> = {
  encode(message: BargainRecordMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== 0) {
      writer.uint32(8).int64(message.userId);
    }
    if (message.nickname !== "") {
      writer.uint32(18).string(message.nickname);
    }
    if (message.deductPrice !== 0) {
      writer.uint32(24).int64(message.deductPrice);
    }
    if (message.paid !== false) {
      writer.uint32(32).bool(message.paid);
    }
    if (message.createTime !== 0) {
      writer.uint32(40).int64(message.createTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BargainRecordMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBargainRecordMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nickname = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.deductPrice = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.paid = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.createTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BargainRecordMessage>, I>>(base?: I): BargainRecordMessage {
    return BargainRecordMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BargainRecordMessage>, I>>(object: I): BargainRecordMessage {
    const message = createBaseBargainRecordMessage();
    message.userId = object.userId ?? 0;
    message.nickname = object.nickname ?? "";
    message.deductPrice = object.deductPrice ?? 0;
    message.paid = object.paid ?? false;
    message.createTime = object.createTime ?? 0;
    return message;
  },
};

function createBaseClubActiveMessage(): ClubActiveMessage {
  return { activeVal: 0, activeTaskList: [] };
}

export const ClubActiveMessage: MessageFns<ClubActiveMessage> = {
  encode(message: ClubActiveMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activeVal !== 0) {
      writer.uint32(8).int64(message.activeVal);
    }
    writer.uint32(18).fork();
    for (const v of message.activeTaskList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubActiveMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubActiveMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activeVal = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.activeTaskList.push(reader.int32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.activeTaskList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubActiveMessage>, I>>(base?: I): ClubActiveMessage {
    return ClubActiveMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubActiveMessage>, I>>(object: I): ClubActiveMessage {
    const message = createBaseClubActiveMessage();
    message.activeVal = object.activeVal ?? 0;
    message.activeTaskList = object.activeTaskList?.map((e) => e) || [];
    return message;
  },
};

function createBaseClubAdjustPositionMessage(): ClubAdjustPositionMessage {
  return { userId: 0, position: 0 };
}

export const ClubAdjustPositionMessage: MessageFns<ClubAdjustPositionMessage> = {
  encode(message: ClubAdjustPositionMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== 0) {
      writer.uint32(8).int64(message.userId);
    }
    if (message.position !== 0) {
      writer.uint32(16).int32(message.position);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubAdjustPositionMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubAdjustPositionMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.position = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubAdjustPositionMessage>, I>>(base?: I): ClubAdjustPositionMessage {
    return ClubAdjustPositionMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubAdjustPositionMessage>, I>>(object: I): ClubAdjustPositionMessage {
    const message = createBaseClubAdjustPositionMessage();
    message.userId = object.userId ?? 0;
    message.position = object.position ?? 0;
    return message;
  },
};

function createBaseClubApplyMessage(): ClubApplyMessage {
  return { simpleMessage: undefined, deadline: 0 };
}

export const ClubApplyMessage: MessageFns<ClubApplyMessage> = {
  encode(message: ClubApplyMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.simpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.simpleMessage, writer.uint32(10).fork()).join();
    }
    if (message.deadline !== 0) {
      writer.uint32(16).int64(message.deadline);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubApplyMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubApplyMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.simpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.deadline = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubApplyMessage>, I>>(base?: I): ClubApplyMessage {
    return ClubApplyMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubApplyMessage>, I>>(object: I): ClubApplyMessage {
    const message = createBaseClubApplyMessage();
    message.simpleMessage = (object.simpleMessage !== undefined && object.simpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.simpleMessage)
      : undefined;
    message.deadline = object.deadline ?? 0;
    return message;
  },
};

function createBaseClubBargainMessage(): ClubBargainMessage {
  return { bargainId: 0, recordMap: {}, endTime: 0 };
}

export const ClubBargainMessage: MessageFns<ClubBargainMessage> = {
  encode(message: ClubBargainMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bargainId !== 0) {
      writer.uint32(8).int64(message.bargainId);
    }
    Object.entries(message.recordMap).forEach(([key, value]) => {
      ClubBargainMessage_RecordMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    if (message.endTime !== 0) {
      writer.uint32(24).int64(message.endTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubBargainMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubBargainMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.bargainId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = ClubBargainMessage_RecordMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.recordMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.endTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubBargainMessage>, I>>(base?: I): ClubBargainMessage {
    return ClubBargainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubBargainMessage>, I>>(object: I): ClubBargainMessage {
    const message = createBaseClubBargainMessage();
    message.bargainId = object.bargainId ?? 0;
    message.recordMap = Object.entries(object.recordMap ?? {}).reduce<{ [key: number]: BargainRecordMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = BargainRecordMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.endTime = object.endTime ?? 0;
    return message;
  },
};

function createBaseClubBargainMessage_RecordMapEntry(): ClubBargainMessage_RecordMapEntry {
  return { key: 0, value: undefined };
}

export const ClubBargainMessage_RecordMapEntry: MessageFns<ClubBargainMessage_RecordMapEntry> = {
  encode(message: ClubBargainMessage_RecordMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      BargainRecordMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubBargainMessage_RecordMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubBargainMessage_RecordMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = BargainRecordMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubBargainMessage_RecordMapEntry>, I>>(
    base?: I,
  ): ClubBargainMessage_RecordMapEntry {
    return ClubBargainMessage_RecordMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubBargainMessage_RecordMapEntry>, I>>(
    object: I,
  ): ClubBargainMessage_RecordMapEntry {
    const message = createBaseClubBargainMessage_RecordMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? BargainRecordMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseClubBasicInfoMessage(): ClubBasicInfoMessage {
  return { name: "", slogan: "", avatar: "" };
}

export const ClubBasicInfoMessage: MessageFns<ClubBasicInfoMessage> = {
  encode(message: ClubBasicInfoMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.slogan !== "") {
      writer.uint32(18).string(message.slogan);
    }
    if (message.avatar !== "") {
      writer.uint32(26).string(message.avatar);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubBasicInfoMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubBasicInfoMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.slogan = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.avatar = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubBasicInfoMessage>, I>>(base?: I): ClubBasicInfoMessage {
    return ClubBasicInfoMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubBasicInfoMessage>, I>>(object: I): ClubBasicInfoMessage {
    const message = createBaseClubBasicInfoMessage();
    message.name = object.name ?? "";
    message.slogan = object.slogan ?? "";
    message.avatar = object.avatar ?? "";
    return message;
  },
};

function createBaseClubBossBuddyMessage(): ClubBossBuddyMessage {
  return { bossTrain: undefined, buddyList: [] };
}

export const ClubBossBuddyMessage: MessageFns<ClubBossBuddyMessage> = {
  encode(message: ClubBossBuddyMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bossTrain !== undefined) {
      ClubBossTrainMessage.encode(message.bossTrain, writer.uint32(10).fork()).join();
    }
    for (const v of message.buddyList) {
      PlayerSimpleMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubBossBuddyMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubBossBuddyMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bossTrain = ClubBossTrainMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.buddyList.push(PlayerSimpleMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubBossBuddyMessage>, I>>(base?: I): ClubBossBuddyMessage {
    return ClubBossBuddyMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubBossBuddyMessage>, I>>(object: I): ClubBossBuddyMessage {
    const message = createBaseClubBossBuddyMessage();
    message.bossTrain = (object.bossTrain !== undefined && object.bossTrain !== null)
      ? ClubBossTrainMessage.fromPartial(object.bossTrain)
      : undefined;
    message.buddyList = object.buddyList?.map((e) => PlayerSimpleMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseClubBossChanceMessage(): ClubBossChanceMessage {
  return { remainCnt: 0, remainItemCnt: 0 };
}

export const ClubBossChanceMessage: MessageFns<ClubBossChanceMessage> = {
  encode(message: ClubBossChanceMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.remainCnt !== 0) {
      writer.uint32(8).int64(message.remainCnt);
    }
    if (message.remainItemCnt !== 0) {
      writer.uint32(16).int32(message.remainItemCnt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubBossChanceMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubBossChanceMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.remainCnt = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.remainItemCnt = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubBossChanceMessage>, I>>(base?: I): ClubBossChanceMessage {
    return ClubBossChanceMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubBossChanceMessage>, I>>(object: I): ClubBossChanceMessage {
    const message = createBaseClubBossChanceMessage();
    message.remainCnt = object.remainCnt ?? 0;
    message.remainItemCnt = object.remainItemCnt ?? 0;
    return message;
  },
};

function createBaseClubBossResponse(): ClubBossResponse {
  return {
    isWin: false,
    replay: "",
    damage: 0,
    todayContribute: 0,
    totalContribute: 0,
    resAddList: [],
    buddyList: [],
    bossChance: undefined,
    bossTrain: undefined,
    unlockBossCnt: 0,
  };
}

export const ClubBossResponse: MessageFns<ClubBossResponse> = {
  encode(message: ClubBossResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isWin !== false) {
      writer.uint32(8).bool(message.isWin);
    }
    if (message.replay !== "") {
      writer.uint32(18).string(message.replay);
    }
    if (message.damage !== 0) {
      writer.uint32(25).double(message.damage);
    }
    if (message.todayContribute !== 0) {
      writer.uint32(32).int64(message.todayContribute);
    }
    if (message.totalContribute !== 0) {
      writer.uint32(40).int64(message.totalContribute);
    }
    writer.uint32(50).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    for (const v of message.buddyList) {
      PlayerSimpleMessage.encode(v!, writer.uint32(58).fork()).join();
    }
    if (message.bossChance !== undefined) {
      ClubBossChanceMessage.encode(message.bossChance, writer.uint32(66).fork()).join();
    }
    if (message.bossTrain !== undefined) {
      ClubBossTrainMessage.encode(message.bossTrain, writer.uint32(74).fork()).join();
    }
    if (message.unlockBossCnt !== 0) {
      writer.uint32(80).int32(message.unlockBossCnt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubBossResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubBossResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isWin = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.damage = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.todayContribute = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.totalContribute = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag === 49) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 50) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.buddyList.push(PlayerSimpleMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.bossChance = ClubBossChanceMessage.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.bossTrain = ClubBossTrainMessage.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.unlockBossCnt = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubBossResponse>, I>>(base?: I): ClubBossResponse {
    return ClubBossResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubBossResponse>, I>>(object: I): ClubBossResponse {
    const message = createBaseClubBossResponse();
    message.isWin = object.isWin ?? false;
    message.replay = object.replay ?? "";
    message.damage = object.damage ?? 0;
    message.todayContribute = object.todayContribute ?? 0;
    message.totalContribute = object.totalContribute ?? 0;
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.buddyList = object.buddyList?.map((e) => PlayerSimpleMessage.fromPartial(e)) || [];
    message.bossChance = (object.bossChance !== undefined && object.bossChance !== null)
      ? ClubBossChanceMessage.fromPartial(object.bossChance)
      : undefined;
    message.bossTrain = (object.bossTrain !== undefined && object.bossTrain !== null)
      ? ClubBossTrainMessage.fromPartial(object.bossTrain)
      : undefined;
    message.unlockBossCnt = object.unlockBossCnt ?? 0;
    return message;
  },
};

function createBaseClubBossTrainMessage(): ClubBossTrainMessage {
  return {
    bossIndex: 0,
    remainHp: 0,
    damageHp: 0,
    killUserMessage: undefined,
    isTakeKill: false,
    isHurt: false,
    hurtRewardIndexList: [],
  };
}

export const ClubBossTrainMessage: MessageFns<ClubBossTrainMessage> = {
  encode(message: ClubBossTrainMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bossIndex !== 0) {
      writer.uint32(8).int32(message.bossIndex);
    }
    if (message.remainHp !== 0) {
      writer.uint32(17).double(message.remainHp);
    }
    if (message.damageHp !== 0) {
      writer.uint32(25).double(message.damageHp);
    }
    if (message.killUserMessage !== undefined) {
      PlayerSimpleMessage.encode(message.killUserMessage, writer.uint32(34).fork()).join();
    }
    if (message.isTakeKill !== false) {
      writer.uint32(40).bool(message.isTakeKill);
    }
    if (message.isHurt !== false) {
      writer.uint32(48).bool(message.isHurt);
    }
    writer.uint32(58).fork();
    for (const v of message.hurtRewardIndexList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubBossTrainMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubBossTrainMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.bossIndex = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.remainHp = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.damageHp = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.killUserMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isTakeKill = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isHurt = reader.bool();
          continue;
        }
        case 7: {
          if (tag === 56) {
            message.hurtRewardIndexList.push(reader.int32());

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.hurtRewardIndexList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubBossTrainMessage>, I>>(base?: I): ClubBossTrainMessage {
    return ClubBossTrainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubBossTrainMessage>, I>>(object: I): ClubBossTrainMessage {
    const message = createBaseClubBossTrainMessage();
    message.bossIndex = object.bossIndex ?? 0;
    message.remainHp = object.remainHp ?? 0;
    message.damageHp = object.damageHp ?? 0;
    message.killUserMessage = (object.killUserMessage !== undefined && object.killUserMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.killUserMessage)
      : undefined;
    message.isTakeKill = object.isTakeKill ?? false;
    message.isHurt = object.isHurt ?? false;
    message.hurtRewardIndexList = object.hurtRewardIndexList?.map((e) => e) || [];
    return message;
  },
};

function createBaseClubCreateRequest(): ClubCreateRequest {
  return { name: "", slogan: "", avatar: "" };
}

export const ClubCreateRequest: MessageFns<ClubCreateRequest> = {
  encode(message: ClubCreateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.slogan !== "") {
      writer.uint32(18).string(message.slogan);
    }
    if (message.avatar !== "") {
      writer.uint32(26).string(message.avatar);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubCreateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubCreateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.slogan = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.avatar = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubCreateRequest>, I>>(base?: I): ClubCreateRequest {
    return ClubCreateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubCreateRequest>, I>>(object: I): ClubCreateRequest {
    const message = createBaseClubCreateRequest();
    message.name = object.name ?? "";
    message.slogan = object.slogan ?? "";
    message.avatar = object.avatar ?? "";
    return message;
  },
};

function createBaseClubDeadRewardResponse(): ClubDeadRewardResponse {
  return { resAddList: [], isTakeKill: false };
}

export const ClubDeadRewardResponse: MessageFns<ClubDeadRewardResponse> = {
  encode(message: ClubDeadRewardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    if (message.isTakeKill !== false) {
      writer.uint32(16).bool(message.isTakeKill);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubDeadRewardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubDeadRewardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isTakeKill = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubDeadRewardResponse>, I>>(base?: I): ClubDeadRewardResponse {
    return ClubDeadRewardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubDeadRewardResponse>, I>>(object: I): ClubDeadRewardResponse {
    const message = createBaseClubDeadRewardResponse();
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.isTakeKill = object.isTakeKill ?? false;
    return message;
  },
};

function createBaseClubDonateMessage(): ClubDonateMessage {
  return { count: 0, nextDonateDeadline: 0 };
}

export const ClubDonateMessage: MessageFns<ClubDonateMessage> = {
  encode(message: ClubDonateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    if (message.nextDonateDeadline !== 0) {
      writer.uint32(16).int64(message.nextDonateDeadline);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubDonateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubDonateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.nextDonateDeadline = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubDonateMessage>, I>>(base?: I): ClubDonateMessage {
    return ClubDonateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubDonateMessage>, I>>(object: I): ClubDonateMessage {
    const message = createBaseClubDonateMessage();
    message.count = object.count ?? 0;
    message.nextDonateDeadline = object.nextDonateDeadline ?? 0;
    return message;
  },
};

function createBaseClubExamineMessage(): ClubExamineMessage {
  return { code: 0, clubMessage: undefined };
}

export const ClubExamineMessage: MessageFns<ClubExamineMessage> = {
  encode(message: ClubExamineMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== 0) {
      writer.uint32(8).int32(message.code);
    }
    if (message.clubMessage !== undefined) {
      ClubMessage.encode(message.clubMessage, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubExamineMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubExamineMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.code = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.clubMessage = ClubMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubExamineMessage>, I>>(base?: I): ClubExamineMessage {
    return ClubExamineMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubExamineMessage>, I>>(object: I): ClubExamineMessage {
    const message = createBaseClubExamineMessage();
    message.code = object.code ?? 0;
    message.clubMessage = (object.clubMessage !== undefined && object.clubMessage !== null)
      ? ClubMessage.fromPartial(object.clubMessage)
      : undefined;
    return message;
  },
};

function createBaseClubExamineRequest(): ClubExamineRequest {
  return { userId: 0, pass: false };
}

export const ClubExamineRequest: MessageFns<ClubExamineRequest> = {
  encode(message: ClubExamineRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== 0) {
      writer.uint32(8).int64(message.userId);
    }
    if (message.pass !== false) {
      writer.uint32(16).bool(message.pass);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubExamineRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubExamineRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pass = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubExamineRequest>, I>>(base?: I): ClubExamineRequest {
    return ClubExamineRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubExamineRequest>, I>>(object: I): ClubExamineRequest {
    const message = createBaseClubExamineRequest();
    message.userId = object.userId ?? 0;
    message.pass = object.pass ?? false;
    return message;
  },
};

function createBaseClubFormMessage(): ClubFormMessage {
  return {
    applyMap: {},
    joinColdStamp: 0,
    clubId: 0,
    clubMessage: undefined,
    remainResetStamp: 0,
    bossChance: undefined,
    active: undefined,
    dailyTask: undefined,
    donateMap: {},
    popUpMessage: undefined,
  };
}

export const ClubFormMessage: MessageFns<ClubFormMessage> = {
  encode(message: ClubFormMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.applyMap).forEach(([key, value]) => {
      ClubFormMessage_ApplyMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    if (message.joinColdStamp !== 0) {
      writer.uint32(16).int64(message.joinColdStamp);
    }
    if (message.clubId !== 0) {
      writer.uint32(24).int64(message.clubId);
    }
    if (message.clubMessage !== undefined) {
      ClubMessage.encode(message.clubMessage, writer.uint32(34).fork()).join();
    }
    if (message.remainResetStamp !== 0) {
      writer.uint32(40).int64(message.remainResetStamp);
    }
    if (message.bossChance !== undefined) {
      ClubBossChanceMessage.encode(message.bossChance, writer.uint32(50).fork()).join();
    }
    if (message.active !== undefined) {
      ClubActiveMessage.encode(message.active, writer.uint32(58).fork()).join();
    }
    if (message.dailyTask !== undefined) {
      ClubTaskMessage.encode(message.dailyTask, writer.uint32(66).fork()).join();
    }
    Object.entries(message.donateMap).forEach(([key, value]) => {
      ClubFormMessage_DonateMapEntry.encode({ key: key as any, value }, writer.uint32(74).fork()).join();
    });
    if (message.popUpMessage !== undefined) {
      ClubPopUpMessage.encode(message.popUpMessage, writer.uint32(82).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubFormMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubFormMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = ClubFormMessage_ApplyMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.applyMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.joinColdStamp = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.clubId = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.clubMessage = ClubMessage.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.remainResetStamp = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.bossChance = ClubBossChanceMessage.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.active = ClubActiveMessage.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.dailyTask = ClubTaskMessage.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          const entry9 = ClubFormMessage_DonateMapEntry.decode(reader, reader.uint32());
          if (entry9.value !== undefined) {
            message.donateMap[entry9.key] = entry9.value;
          }
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.popUpMessage = ClubPopUpMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubFormMessage>, I>>(base?: I): ClubFormMessage {
    return ClubFormMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubFormMessage>, I>>(object: I): ClubFormMessage {
    const message = createBaseClubFormMessage();
    message.applyMap = Object.entries(object.applyMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.joinColdStamp = object.joinColdStamp ?? 0;
    message.clubId = object.clubId ?? 0;
    message.clubMessage = (object.clubMessage !== undefined && object.clubMessage !== null)
      ? ClubMessage.fromPartial(object.clubMessage)
      : undefined;
    message.remainResetStamp = object.remainResetStamp ?? 0;
    message.bossChance = (object.bossChance !== undefined && object.bossChance !== null)
      ? ClubBossChanceMessage.fromPartial(object.bossChance)
      : undefined;
    message.active = (object.active !== undefined && object.active !== null)
      ? ClubActiveMessage.fromPartial(object.active)
      : undefined;
    message.dailyTask = (object.dailyTask !== undefined && object.dailyTask !== null)
      ? ClubTaskMessage.fromPartial(object.dailyTask)
      : undefined;
    message.donateMap = Object.entries(object.donateMap ?? {}).reduce<{ [key: number]: ClubDonateMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = ClubDonateMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.popUpMessage = (object.popUpMessage !== undefined && object.popUpMessage !== null)
      ? ClubPopUpMessage.fromPartial(object.popUpMessage)
      : undefined;
    return message;
  },
};

function createBaseClubFormMessage_ApplyMapEntry(): ClubFormMessage_ApplyMapEntry {
  return { key: 0, value: 0 };
}

export const ClubFormMessage_ApplyMapEntry: MessageFns<ClubFormMessage_ApplyMapEntry> = {
  encode(message: ClubFormMessage_ApplyMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubFormMessage_ApplyMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubFormMessage_ApplyMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubFormMessage_ApplyMapEntry>, I>>(base?: I): ClubFormMessage_ApplyMapEntry {
    return ClubFormMessage_ApplyMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubFormMessage_ApplyMapEntry>, I>>(
    object: I,
  ): ClubFormMessage_ApplyMapEntry {
    const message = createBaseClubFormMessage_ApplyMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseClubFormMessage_DonateMapEntry(): ClubFormMessage_DonateMapEntry {
  return { key: 0, value: undefined };
}

export const ClubFormMessage_DonateMapEntry: MessageFns<ClubFormMessage_DonateMapEntry> = {
  encode(message: ClubFormMessage_DonateMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      ClubDonateMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubFormMessage_DonateMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubFormMessage_DonateMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ClubDonateMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubFormMessage_DonateMapEntry>, I>>(base?: I): ClubFormMessage_DonateMapEntry {
    return ClubFormMessage_DonateMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubFormMessage_DonateMapEntry>, I>>(
    object: I,
  ): ClubFormMessage_DonateMapEntry {
    const message = createBaseClubFormMessage_DonateMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? ClubDonateMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseClubHurtRewardRequest(): ClubHurtRewardRequest {
  return { bossId: 0, hurtIndex: 0 };
}

export const ClubHurtRewardRequest: MessageFns<ClubHurtRewardRequest> = {
  encode(message: ClubHurtRewardRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bossId !== 0) {
      writer.uint32(8).int64(message.bossId);
    }
    if (message.hurtIndex !== 0) {
      writer.uint32(16).int32(message.hurtIndex);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubHurtRewardRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubHurtRewardRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.bossId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hurtIndex = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubHurtRewardRequest>, I>>(base?: I): ClubHurtRewardRequest {
    return ClubHurtRewardRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubHurtRewardRequest>, I>>(object: I): ClubHurtRewardRequest {
    const message = createBaseClubHurtRewardRequest();
    message.bossId = object.bossId ?? 0;
    message.hurtIndex = object.hurtIndex ?? 0;
    return message;
  },
};

function createBaseClubHurtRewardResponse(): ClubHurtRewardResponse {
  return { hurtRewardTakeList: [], resAddList: [] };
}

export const ClubHurtRewardResponse: MessageFns<ClubHurtRewardResponse> = {
  encode(message: ClubHurtRewardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.hurtRewardTakeList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubHurtRewardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubHurtRewardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.hurtRewardTakeList.push(reader.int32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.hurtRewardTakeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 17) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubHurtRewardResponse>, I>>(base?: I): ClubHurtRewardResponse {
    return ClubHurtRewardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubHurtRewardResponse>, I>>(object: I): ClubHurtRewardResponse {
    const message = createBaseClubHurtRewardResponse();
    message.hurtRewardTakeList = object.hurtRewardTakeList?.map((e) => e) || [];
    message.resAddList = object.resAddList?.map((e) => e) || [];
    return message;
  },
};

function createBaseClubJoinResponse(): ClubJoinResponse {
  return { code: 0, applyMap: {}, clubMessage: undefined };
}

export const ClubJoinResponse: MessageFns<ClubJoinResponse> = {
  encode(message: ClubJoinResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== 0) {
      writer.uint32(8).int32(message.code);
    }
    Object.entries(message.applyMap).forEach(([key, value]) => {
      ClubJoinResponse_ApplyMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    if (message.clubMessage !== undefined) {
      ClubMessage.encode(message.clubMessage, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubJoinResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubJoinResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.code = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = ClubJoinResponse_ApplyMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.applyMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.clubMessage = ClubMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubJoinResponse>, I>>(base?: I): ClubJoinResponse {
    return ClubJoinResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubJoinResponse>, I>>(object: I): ClubJoinResponse {
    const message = createBaseClubJoinResponse();
    message.code = object.code ?? 0;
    message.applyMap = Object.entries(object.applyMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.clubMessage = (object.clubMessage !== undefined && object.clubMessage !== null)
      ? ClubMessage.fromPartial(object.clubMessage)
      : undefined;
    return message;
  },
};

function createBaseClubJoinResponse_ApplyMapEntry(): ClubJoinResponse_ApplyMapEntry {
  return { key: 0, value: 0 };
}

export const ClubJoinResponse_ApplyMapEntry: MessageFns<ClubJoinResponse_ApplyMapEntry> = {
  encode(message: ClubJoinResponse_ApplyMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubJoinResponse_ApplyMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubJoinResponse_ApplyMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubJoinResponse_ApplyMapEntry>, I>>(base?: I): ClubJoinResponse_ApplyMapEntry {
    return ClubJoinResponse_ApplyMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubJoinResponse_ApplyMapEntry>, I>>(
    object: I,
  ): ClubJoinResponse_ApplyMapEntry {
    const message = createBaseClubJoinResponse_ApplyMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseClubLogMessage(): ClubLogMessage {
  return { eventType: 0, name: "", param1: 0, param2: 0, timeStamp: 0 };
}

export const ClubLogMessage: MessageFns<ClubLogMessage> = {
  encode(message: ClubLogMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.eventType !== 0) {
      writer.uint32(8).int32(message.eventType);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.param1 !== 0) {
      writer.uint32(24).int32(message.param1);
    }
    if (message.param2 !== 0) {
      writer.uint32(32).int32(message.param2);
    }
    if (message.timeStamp !== 0) {
      writer.uint32(40).int64(message.timeStamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubLogMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubLogMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.eventType = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.param1 = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.param2 = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.timeStamp = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubLogMessage>, I>>(base?: I): ClubLogMessage {
    return ClubLogMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubLogMessage>, I>>(object: I): ClubLogMessage {
    const message = createBaseClubLogMessage();
    message.eventType = object.eventType ?? 0;
    message.name = object.name ?? "";
    message.param1 = object.param1 ?? 0;
    message.param2 = object.param2 ?? 0;
    message.timeStamp = object.timeStamp ?? 0;
    return message;
  },
};

function createBaseClubMemberMessage(): ClubMemberMessage {
  return {
    simpleMessage: undefined,
    isOnline: false,
    position: 0,
    todayContribute: 0,
    totalContribute: 0,
    joinTime: 0,
    activeVal: 0,
  };
}

export const ClubMemberMessage: MessageFns<ClubMemberMessage> = {
  encode(message: ClubMemberMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.simpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.simpleMessage, writer.uint32(10).fork()).join();
    }
    if (message.isOnline !== false) {
      writer.uint32(16).bool(message.isOnline);
    }
    if (message.position !== 0) {
      writer.uint32(24).int32(message.position);
    }
    if (message.todayContribute !== 0) {
      writer.uint32(32).int64(message.todayContribute);
    }
    if (message.totalContribute !== 0) {
      writer.uint32(40).int64(message.totalContribute);
    }
    if (message.joinTime !== 0) {
      writer.uint32(48).int64(message.joinTime);
    }
    if (message.activeVal !== 0) {
      writer.uint32(56).int64(message.activeVal);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubMemberMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubMemberMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.simpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isOnline = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.position = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.todayContribute = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.totalContribute = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.joinTime = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.activeVal = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubMemberMessage>, I>>(base?: I): ClubMemberMessage {
    return ClubMemberMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubMemberMessage>, I>>(object: I): ClubMemberMessage {
    const message = createBaseClubMemberMessage();
    message.simpleMessage = (object.simpleMessage !== undefined && object.simpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.simpleMessage)
      : undefined;
    message.isOnline = object.isOnline ?? false;
    message.position = object.position ?? 0;
    message.todayContribute = object.todayContribute ?? 0;
    message.totalContribute = object.totalContribute ?? 0;
    message.joinTime = object.joinTime ?? 0;
    message.activeVal = object.activeVal ?? 0;
    return message;
  },
};

function createBaseClubMessage(): ClubMessage {
  return {
    id: 0,
    name: "",
    slogan: "",
    avatar: "",
    serverName: "",
    level: 0,
    exp: 0,
    totalPower: 0,
    killBossCnt: 0,
    unlockBossCnt: 0,
    auditOption: undefined,
    bargain: undefined,
    memberList: [],
  };
}

export const ClubMessage: MessageFns<ClubMessage> = {
  encode(message: ClubMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.slogan !== "") {
      writer.uint32(26).string(message.slogan);
    }
    if (message.avatar !== "") {
      writer.uint32(34).string(message.avatar);
    }
    if (message.serverName !== "") {
      writer.uint32(42).string(message.serverName);
    }
    if (message.level !== 0) {
      writer.uint32(48).int32(message.level);
    }
    if (message.exp !== 0) {
      writer.uint32(56).int64(message.exp);
    }
    if (message.totalPower !== 0) {
      writer.uint32(65).double(message.totalPower);
    }
    if (message.killBossCnt !== 0) {
      writer.uint32(72).int32(message.killBossCnt);
    }
    if (message.unlockBossCnt !== 0) {
      writer.uint32(80).int32(message.unlockBossCnt);
    }
    if (message.auditOption !== undefined) {
      AuditOptionMessage.encode(message.auditOption, writer.uint32(90).fork()).join();
    }
    if (message.bargain !== undefined) {
      ClubBargainMessage.encode(message.bargain, writer.uint32(98).fork()).join();
    }
    for (const v of message.memberList) {
      ClubMemberMessage.encode(v!, writer.uint32(106).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.slogan = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.avatar = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.serverName = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.exp = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.totalPower = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.killBossCnt = reader.int32();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.unlockBossCnt = reader.int32();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.auditOption = AuditOptionMessage.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.bargain = ClubBargainMessage.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.memberList.push(ClubMemberMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubMessage>, I>>(base?: I): ClubMessage {
    return ClubMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubMessage>, I>>(object: I): ClubMessage {
    const message = createBaseClubMessage();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.slogan = object.slogan ?? "";
    message.avatar = object.avatar ?? "";
    message.serverName = object.serverName ?? "";
    message.level = object.level ?? 0;
    message.exp = object.exp ?? 0;
    message.totalPower = object.totalPower ?? 0;
    message.killBossCnt = object.killBossCnt ?? 0;
    message.unlockBossCnt = object.unlockBossCnt ?? 0;
    message.auditOption = (object.auditOption !== undefined && object.auditOption !== null)
      ? AuditOptionMessage.fromPartial(object.auditOption)
      : undefined;
    message.bargain = (object.bargain !== undefined && object.bargain !== null)
      ? ClubBargainMessage.fromPartial(object.bargain)
      : undefined;
    message.memberList = object.memberList?.map((e) => ClubMemberMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseClubPopUpMessage(): ClubPopUpMessage {
  return { clubName: "" };
}

export const ClubPopUpMessage: MessageFns<ClubPopUpMessage> = {
  encode(message: ClubPopUpMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.clubName !== "") {
      writer.uint32(10).string(message.clubName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubPopUpMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubPopUpMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.clubName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubPopUpMessage>, I>>(base?: I): ClubPopUpMessage {
    return ClubPopUpMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubPopUpMessage>, I>>(object: I): ClubPopUpMessage {
    const message = createBaseClubPopUpMessage();
    message.clubName = object.clubName ?? "";
    return message;
  },
};

function createBaseClubRankMessage(): ClubRankMessage {
  return { point: 0, rank: 0, rankList: [] };
}

export const ClubRankMessage: MessageFns<ClubRankMessage> = {
  encode(message: ClubRankMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.point !== 0) {
      writer.uint32(9).double(message.point);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    for (const v of message.rankList) {
      ClubSimplePowerMessage.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubRankMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubRankMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.point = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rankList.push(ClubSimplePowerMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubRankMessage>, I>>(base?: I): ClubRankMessage {
    return ClubRankMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubRankMessage>, I>>(object: I): ClubRankMessage {
    const message = createBaseClubRankMessage();
    message.point = object.point ?? 0;
    message.rank = object.rank ?? 0;
    message.rankList = object.rankList?.map((e) => ClubSimplePowerMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseClubRewardMessage(): ClubRewardMessage {
  return {
    rewardMessage: undefined,
    donateMap: {},
    active: undefined,
    dailyTask: undefined,
    clubLevel: 0,
    clubExp: 0,
    todayContribute: 0,
    totalContribute: 0,
    trigger: false,
  };
}

export const ClubRewardMessage: MessageFns<ClubRewardMessage> = {
  encode(message: ClubRewardMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(10).fork()).join();
    }
    Object.entries(message.donateMap).forEach(([key, value]) => {
      ClubRewardMessage_DonateMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    if (message.active !== undefined) {
      ClubActiveMessage.encode(message.active, writer.uint32(26).fork()).join();
    }
    if (message.dailyTask !== undefined) {
      ClubTaskMessage.encode(message.dailyTask, writer.uint32(34).fork()).join();
    }
    if (message.clubLevel !== 0) {
      writer.uint32(40).int32(message.clubLevel);
    }
    if (message.clubExp !== 0) {
      writer.uint32(48).int64(message.clubExp);
    }
    if (message.todayContribute !== 0) {
      writer.uint32(56).int64(message.todayContribute);
    }
    if (message.totalContribute !== 0) {
      writer.uint32(64).int64(message.totalContribute);
    }
    if (message.trigger !== false) {
      writer.uint32(72).bool(message.trigger);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubRewardMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubRewardMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = ClubRewardMessage_DonateMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.donateMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.active = ClubActiveMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.dailyTask = ClubTaskMessage.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.clubLevel = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.clubExp = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.todayContribute = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.totalContribute = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.trigger = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubRewardMessage>, I>>(base?: I): ClubRewardMessage {
    return ClubRewardMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubRewardMessage>, I>>(object: I): ClubRewardMessage {
    const message = createBaseClubRewardMessage();
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    message.donateMap = Object.entries(object.donateMap ?? {}).reduce<{ [key: number]: ClubDonateMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = ClubDonateMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.active = (object.active !== undefined && object.active !== null)
      ? ClubActiveMessage.fromPartial(object.active)
      : undefined;
    message.dailyTask = (object.dailyTask !== undefined && object.dailyTask !== null)
      ? ClubTaskMessage.fromPartial(object.dailyTask)
      : undefined;
    message.clubLevel = object.clubLevel ?? 0;
    message.clubExp = object.clubExp ?? 0;
    message.todayContribute = object.todayContribute ?? 0;
    message.totalContribute = object.totalContribute ?? 0;
    message.trigger = object.trigger ?? false;
    return message;
  },
};

function createBaseClubRewardMessage_DonateMapEntry(): ClubRewardMessage_DonateMapEntry {
  return { key: 0, value: undefined };
}

export const ClubRewardMessage_DonateMapEntry: MessageFns<ClubRewardMessage_DonateMapEntry> = {
  encode(message: ClubRewardMessage_DonateMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      ClubDonateMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubRewardMessage_DonateMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubRewardMessage_DonateMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ClubDonateMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubRewardMessage_DonateMapEntry>, I>>(
    base?: I,
  ): ClubRewardMessage_DonateMapEntry {
    return ClubRewardMessage_DonateMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubRewardMessage_DonateMapEntry>, I>>(
    object: I,
  ): ClubRewardMessage_DonateMapEntry {
    const message = createBaseClubRewardMessage_DonateMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? ClubDonateMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseClubSimplePowerMessage(): ClubSimplePowerMessage {
  return { id: 0, name: "", slogan: "", avatar: "", serverName: "", level: 0, totalPower: 0 };
}

export const ClubSimplePowerMessage: MessageFns<ClubSimplePowerMessage> = {
  encode(message: ClubSimplePowerMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.slogan !== "") {
      writer.uint32(26).string(message.slogan);
    }
    if (message.avatar !== "") {
      writer.uint32(34).string(message.avatar);
    }
    if (message.serverName !== "") {
      writer.uint32(42).string(message.serverName);
    }
    if (message.level !== 0) {
      writer.uint32(48).int32(message.level);
    }
    if (message.totalPower !== 0) {
      writer.uint32(57).double(message.totalPower);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubSimplePowerMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubSimplePowerMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.slogan = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.avatar = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.serverName = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.totalPower = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubSimplePowerMessage>, I>>(base?: I): ClubSimplePowerMessage {
    return ClubSimplePowerMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubSimplePowerMessage>, I>>(object: I): ClubSimplePowerMessage {
    const message = createBaseClubSimplePowerMessage();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.slogan = object.slogan ?? "";
    message.avatar = object.avatar ?? "";
    message.serverName = object.serverName ?? "";
    message.level = object.level ?? 0;
    message.totalPower = object.totalPower ?? 0;
    return message;
  },
};

function createBaseClubTaskMessage(): ClubTaskMessage {
  return { taskCntMap: {}, completeTaskList: [] };
}

export const ClubTaskMessage: MessageFns<ClubTaskMessage> = {
  encode(message: ClubTaskMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.taskCntMap).forEach(([key, value]) => {
      ClubTaskMessage_TaskCntMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    writer.uint32(18).fork();
    for (const v of message.completeTaskList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubTaskMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubTaskMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = ClubTaskMessage_TaskCntMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.taskCntMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.completeTaskList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.completeTaskList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubTaskMessage>, I>>(base?: I): ClubTaskMessage {
    return ClubTaskMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubTaskMessage>, I>>(object: I): ClubTaskMessage {
    const message = createBaseClubTaskMessage();
    message.taskCntMap = Object.entries(object.taskCntMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.completeTaskList = object.completeTaskList?.map((e) => e) || [];
    return message;
  },
};

function createBaseClubTaskMessage_TaskCntMapEntry(): ClubTaskMessage_TaskCntMapEntry {
  return { key: 0, value: 0 };
}

export const ClubTaskMessage_TaskCntMapEntry: MessageFns<ClubTaskMessage_TaskCntMapEntry> = {
  encode(message: ClubTaskMessage_TaskCntMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClubTaskMessage_TaskCntMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClubTaskMessage_TaskCntMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ClubTaskMessage_TaskCntMapEntry>, I>>(base?: I): ClubTaskMessage_TaskCntMapEntry {
    return ClubTaskMessage_TaskCntMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClubTaskMessage_TaskCntMapEntry>, I>>(
    object: I,
  ): ClubTaskMessage_TaskCntMapEntry {
    const message = createBaseClubTaskMessage_TaskCntMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
