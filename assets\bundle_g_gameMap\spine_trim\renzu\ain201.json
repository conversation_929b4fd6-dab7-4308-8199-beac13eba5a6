{"skeleton": {"hash": "i++OjtPKT7BKjHD7tW/wzhGk1Pc", "spine": "3.8.75", "x": -23.56, "y": -5.98, "width": 47.02, "height": 33.04, "images": "./images/", "audio": "D:/spine/蝴蝶"}, "bones": [{"name": "root", "scaleX": 0.02, "scaleY": 0.02}, {"name": "st", "parent": "root", "length": 374.21, "rotation": -89.81, "x": 1.64, "y": 595.34}, {"name": "st2", "parent": "st", "length": 75.38, "rotation": 1.69, "x": 382.85, "y": -1.26}, {"name": "st3", "parent": "st2", "length": 64.23, "rotation": -2.98, "x": 79.04, "y": -1.36}, {"name": "st4", "parent": "st3", "length": 50.77, "rotation": 5.29, "x": 74.11, "y": 0.19}, {"name": "st5", "parent": "st", "length": 69.21, "rotation": 158.01, "x": -28.6, "y": 14.5}, {"name": "st6", "parent": "st5", "length": 61.07, "rotation": -9.53, "x": 79.04, "y": -0.14}, {"name": "st7", "parent": "st6", "length": 60.34, "rotation": -20.8, "x": 66.26, "y": -1.26}, {"name": "st8", "parent": "st7", "length": 67.11, "rotation": -23.53, "x": 60.34}, {"name": "st9", "parent": "st8", "length": 49.99, "rotation": -34.7, "x": 74.25, "y": -2.61}, {"name": "st10", "parent": "st9", "length": 32.82, "rotation": -54.97, "x": 56.88, "y": -5.51}, {"name": "st11", "parent": "st", "length": 72.03, "rotation": -159.28, "x": -33.95, "y": -4.47}, {"name": "st12", "parent": "st11", "length": 67.52, "rotation": 13.14, "x": 76.1, "y": 0.06}, {"name": "st13", "parent": "st12", "length": 59.34, "rotation": 10.44, "x": 71.09, "y": 1.24}, {"name": "st14", "parent": "st13", "length": 57.87, "rotation": 25.66, "x": 63.61, "y": 1.11}, {"name": "st15", "parent": "st14", "length": 59.19, "rotation": 44.78, "x": 64.73, "y": 3.28}, {"name": "st16", "parent": "st15", "length": 26.63, "rotation": 58.55, "x": 64.58, "y": 0.83}, {"name": "c1", "parent": "st", "length": 239.76, "rotation": 130.84, "x": 130.78, "y": 44.43}, {"name": "c2", "parent": "c1", "length": 238.68, "rotation": -4.83, "x": 263.42, "y": -5.02}, {"name": "c3", "parent": "c2", "length": 249.75, "rotation": -19.8, "x": 257.12, "y": -10.59}, {"name": "c4", "parent": "c3", "length": 239.7, "rotation": -14.7, "x": 261.28, "y": -10.74}, {"name": "c5", "parent": "st", "length": 289.01, "rotation": -135.19, "x": 129.05, "y": -44.97}, {"name": "c6", "parent": "c5", "length": 286.85, "rotation": 13.39, "x": 313.93, "y": 8.3}, {"name": "c7", "parent": "c6", "length": 229.02, "rotation": 10.57, "x": 306.55, "y": 12.12}, {"name": "c8", "parent": "c7", "length": 238.19, "rotation": 15.95, "x": 242.68, "y": 10.29}, {"name": "c9", "parent": "st", "length": 172.2, "rotation": 31.24, "x": 295.51, "y": 44.14}, {"name": "c10", "parent": "c9", "length": 199.75, "rotation": 20.76, "x": 192.32, "y": 6.84}, {"name": "c11", "parent": "c10", "length": 191.09, "rotation": 17.82, "x": 235.07, "y": 3.3}, {"name": "c12", "parent": "c11", "length": 215.17, "rotation": 6.83, "x": 232.24, "y": 6.28}, {"name": "c13", "parent": "st", "length": 188.85, "rotation": -36.4, "x": 300.7, "y": -34.78}, {"name": "c14", "parent": "c13", "length": 206.26, "rotation": -17.37, "x": 204.06, "y": -2.35}, {"name": "c15", "parent": "c14", "length": 228.43, "rotation": -24.04, "x": 227.47, "y": 1.26}, {"name": "c16", "parent": "c15", "length": 225.2, "rotation": -0.53, "x": 254.1, "y": 2.72}], "slots": [{"name": "c1", "bone": "c1", "attachment": "c1"}, {"name": "c3", "bone": "c9", "attachment": "c3"}, {"name": "c2", "bone": "c5", "attachment": "c2"}, {"name": "c4", "bone": "c13", "attachment": "c4"}, {"name": "st", "bone": "st", "attachment": "st"}], "skins": [{"name": "default", "attachments": {"st": {"st": {"type": "mesh", "uvs": [0.26351, 0.02221, 0.32662, 0.05367, 0.40618, 0.10926, 0.48422, 0.20136, 0.51659, 0.21052, 0.59113, 0.11286, 0.68336, 0.04861, 0.79868, 0.01917, 0.86484, 0.01336, 0.93011, 0.02029, 0.98802, 0.04592, 1, 0.0771, 1, 0.0875, 0.97662, 0.10985, 0.91551, 0.11399, 0.89365, 0.10951, 0.88688, 0.08774, 0.97284, 0.06794, 0.9028, 0.03451, 0.7914, 0.03707, 0.74914, 0.0468, 0.69124, 0.06948, 0.65471, 0.0895, 0.61636, 0.11988, 0.58517, 0.15401, 0.55175, 0.20155, 0.53992, 0.22266, 0.60426, 0.27114, 0.53625, 0.35294, 0.53491, 0.3966, 0.53664, 0.68302, 0.52046, 0.75262, 0.52277, 0.81665, 0.52635, 0.89675, 0.5288, 0.9515, 0.51908, 0.98974, 0.50481, 1, 0.49135, 1, 0.4798, 0.99251, 0.45494, 0.93932, 0.45995, 0.87166, 0.46649, 0.78332, 0.44612, 0.75874, 0.43439, 0.36301, 0.43501, 0.35801, 0.3755, 0.25351, 0.41655, 0.23339, 0.45704, 0.2181, 0.40655, 0.15033, 0.38486, 0.13031, 0.30191, 0.0654, 0.27113, 0.04848, 0.21059, 0.02488, 0.16263, 0.01554, 0.0906, 0.02121, 0.05106, 0.03698, 0.09491, 0.08418, 0.03167, 0.09717, 0, 0.06998, 0, 0.06391, 0.00686, 0.03648, 0.09775, 0.00123, 0.19218, 0.00086], "triangles": [55, 58, 59, 55, 59, 60, 57, 58, 55, 56, 57, 55, 54, 61, 53, 61, 55, 60, 54, 55, 61, 53, 61, 62, 52, 62, 0, 53, 62, 52, 52, 0, 51, 0, 1, 51, 50, 51, 1, 2, 50, 1, 49, 50, 2, 2, 3, 48, 49, 2, 48, 47, 48, 3, 47, 3, 4, 26, 46, 47, 17, 18, 10, 17, 10, 11, 17, 11, 12, 16, 14, 15, 16, 17, 14, 12, 13, 17, 17, 13, 14, 18, 8, 9, 10, 18, 9, 19, 7, 8, 19, 8, 18, 19, 20, 7, 7, 20, 6, 21, 6, 20, 22, 6, 21, 22, 5, 6, 23, 5, 22, 24, 5, 23, 4, 5, 24, 25, 4, 24, 26, 4, 25, 26, 47, 4, 27, 46, 26, 39, 33, 34, 34, 38, 39, 35, 38, 34, 36, 37, 38, 35, 36, 38, 40, 32, 33, 39, 40, 33, 31, 42, 30, 41, 42, 31, 41, 31, 32, 40, 41, 32, 28, 44, 46, 45, 46, 44, 46, 27, 28, 28, 43, 44, 29, 43, 28, 43, 30, 42, 30, 43, 29], "vertices": [5, 5, 122.87, 188.97, 0, 10, -118.98, -347.64, 0, 12, 136.23, 8.87, 0, 13, 65.44, -4.29, 0.29378, 14, -0.68, -5.66, 0.70622, 4, 5, 110.34, 148.81, 0, 10, -85.03, -322.81, 0, 12, 95.96, -3.3, 0.00024, 13, 23.64, -8.97, 0.99976, 3, 5, 82.05, 93.2, 0, 11, 111.79, -3.02, 4e-05, 12, 34.05, -11.11, 0.99996, 3, 5, 24.87, 26.87, 0.13264, 10, 56.2, -275.57, 0, 11, 24.78, -12.97, 0.86736, 4, 5, 23.91, 8.46, 0.83731, 10, 67.92, -261.33, 0, 11, 11.58, -25.85, 0.16269, 12, -68.71, -10.57, 1e-05, 1, 6, 34.65, 9.29, 1, 2, 7, 37.63, 12.17, 1, 10, -42.43, -143.31, 0, 3, 7, 99.95, -4.82, 7e-05, 8, 38.24, 11.39, 0.99776, 9, -37.57, -8.99, 0.00217, 2, 8, 72.59, 7.67, 0.64506, 9, -7.21, 7.5, 0.35494, 3, 7, 153, -47.28, 0, 8, 103.83, -6.37, 0.00035, 9, 26.47, 13.75, 0.99965, 4, 7, 163.36, -82.73, 0, 8, 127.48, -34.74, 0, 9, 62.06, 3.89, 0.31179, 10, -4.72, 9.64, 0.68821, 3, 7, 152.09, -107.31, 0, 8, 126.96, -61.77, 0, 10, 22.31, 8.96, 1, 2, 7, 146.7, -114.24, 0, 10, 30.8, 6.74, 1, 1, 10, 45.98, -9.73, 1, 1, 10, 41.36, -41.18, 1, 1, 10, 34.84, -51.16, 1, 1, 10, 16.18, -49.89, 1, 2, 4, -690.07, 302.48, 0, 10, 11.27, -2.66, 1, 2, 9, 17.4, -2.4, 0.99993, 10, -25.2, -30.54, 7e-05, 2, 8, 30.85, -2.31, 1, 10, -37.71, -86.8, 0, 4, 4, -716.3, 188.44, 0, 7, 65.41, -7.5, 0.18614, 8, 7.65, -4.85, 0.81386, 14, -243.88, -71.42, 0, 4, 7, 30.03, -4.23, 0.99768, 8, -26.1, -15.97, 0.00232, 10, -24.36, -143.83, 0, 14, -222.22, -43.25, 0, 3, 4, -683.92, 137.12, 0, 7, 4.75, -5.98, 1, 10, -12.79, -166.38, 0, 3, 4, -659.8, 115.47, 0, 6, 36.37, -4.93, 1, 10, 6.98, -192.06, 0, 1, 6, 3.38, -6.14, 1, 2, 5, 37.68, -5.61, 1, 14, -192.26, 86.09, 0, 4, 1, -43.64, 27.64, 0.01834, 4, -576.17, 69.73, 0, 5, 18.87, -6.55, 0.98166, 10, 80.89, -252.26, 0, 4, 1, -2.62, 60.77, 0.4217, 4, -532.94, 99.92, 0, 5, -6.77, -52.63, 0.5783, 10, 128.89, -230.45, 0, 4, 1, 66.31, 25.38, 0.98016, 4, -466.65, 59.81, 0, 5, -83.93, -45.62, 0.01984, 10, 186.77, -281.96, 0, 2, 1, 103.16, 24.57, 1, 10, 222.25, -291.96, 0, 2, 1, 344.9, 24.66, 1, 10, 456.32, -352.34, 0, 2, 2, 21.26, 16.75, 1, 10, 511.03, -375.32, 0, 3, 2, 75.32, 16.17, 0.68997, 3, -4.63, 17.31, 0.31003, 10, 563.61, -387.85, 0, 3, 3, 62.93, 20.46, 0.73669, 4, -9.27, 21.21, 0.26331, 14, -379.19, 642.41, 0, 2, 4, 36.91, 19.1, 1, 14, -396.08, 685.45, 0, 1, 4, 68.74, 11.73, 1, 2, 4, 76.83, 3.75, 1, 10, 710.96, -436.04, 0, 2, 4, 76.32, -3.19, 1, 10, 709.19, -442.77, 0, 2, 4, 69.59, -8.69, 1, 10, 701.57, -446.95, 0, 2, 4, 23.87, -18.23, 1, 10, 654.88, -448.01, 0, 3, 3, 42.42, -14.27, 0.99096, 4, -32.89, -11.48, 0.00904, 10, 600.3, -431.04, 0, 1, 2, 46.25, -11.99, 1, 3, 2, 25.16, -21.83, 0.99816, 3, -52.74, -23.25, 0.00184, 4, -128.47, -11.65, 0, 2, 1, 74.63, -27.31, 0.99972, 11, -93.48, 59.78, 0.00028, 2, 1, 70.42, -26.98, 0.99826, 11, -89.65, 57.98, 0.00174, 3, 1, -17.88, -57.45, 0.48471, 4, -556.4, -16.95, 0, 11, 3.72, 55.24, 0.51529, 2, 1, -34.8, -36.17, 0.28569, 11, 12.01, 29.36, 0.71431, 2, 1, -47.63, -15.2, 0.02087, 11, 16.59, 5.2, 0.97913, 1, 12, 5.23, 8.13, 1, 1, 12, 25.5, 7.96, 1, 2, 13, 25.53, 7.08, 0.99896, 14, -31.74, 21.87, 0.00104, 1, 13, 46.86, 8.43, 1, 2, 13, 83, 16.8, 0.00589, 14, 24.28, 5.75, 0.99411, 2, 14, 50.28, 6.75, 0.92684, 15, -7.81, 12.64, 0.07316, 1, 15, 27.98, 1.29, 1, 3, 5, 70.51, 286.32, 0, 12, 187.4, 106.85, 0, 15, 52.13, 4.73, 1, 4, 5, 41.94, 250.47, 0, 12, 141.69, 110.37, 0, 15, 48.36, 50.42, 0.1915, 16, 33.84, 39.71, 0.8085, 4, 5, 19.62, 276.76, 0, 12, 150.91, 143.6, 0, 15, 82.63, 46.58, 0.04863, 16, 48.45, 8.46, 0.95137, 2, 13, 132.14, 121.15, 0, 16, 27.51, -10.41, 1, 1, 16, 22.42, -10.99, 1, 1, 16, -0.98, -10.09, 1, 3, 12, 198.88, 69.96, 0, 14, 85.93, 6.79, 0.00918, 15, 17.52, -12.45, 0.99082, 4, 4, -775.99, -95.91, 0, 5, 125.91, 229.9, 0, 10, -145.75, -378.75, 0, 14, 40.12, -10.09, 1], "hull": 63, "edges": [0, 124, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 78, 80, 80, 82, 64, 66, 66, 68], "width": 517, "height": 844}}, "c1": {"c1": {"type": "mesh", "uvs": [0.83467, 0, 0.90754, 0.0122, 0.95431, 0.03913, 0.98043, 0.06724, 0.99458, 0.10874, 0.99157, 0.15859, 0.97029, 0.23149, 0.91837, 0.32103, 0.90654, 0.35252, 0.88346, 0.42936, 0.88174, 0.43856, 0.87205, 0.50901, 0.87155, 0.52131, 0.87727, 0.58695, 0.8805, 0.60007, 0.89695, 0.62719, 0.92039, 0.65265, 0.96312, 0.68329, 0.98581, 0.71538, 0.99829, 0.7515, 0.99844, 0.81255, 0.97931, 0.86476, 0.94738, 0.91119, 0.89311, 0.95522, 0.82624, 0.98009, 0.77201, 0.99, 0.68031, 0.99293, 0.60349, 0.99538, 0.49272, 0.99892, 0.37958, 0.98891, 0.29061, 0.97618, 0.19383, 0.96233, 0.09566, 0.93402, 0.08723, 0.92723, 0.00233, 0.90949, 0.0001, 0.8784, 0.00274, 0.7613, 0.04909, 0.66607, 0.11792, 0.55022, 0.16176, 0.48288, 0.27725, 0.34416, 0.36031, 0.25291, 0.45413, 0.16465, 0.53075, 0.10381, 0.61568, 0.05028, 0.69327, 0.0177, 0.76338, 0.00178, 0.80677, 0, 0.11782, 0.66227, 0.18631, 0.75263, 0.18312, 0.87948, 0.2787, 0.46244, 0.36631, 0.65184, 0.40453, 0.85342, 0.47781, 0.32516, 0.58294, 0.58407, 0.61002, 0.81171, 0.71037, 0.25565, 0.81391, 0.76827], "triangles": [33, 35, 36, 33, 34, 35, 56, 27, 28, 27, 56, 26, 56, 28, 53, 26, 56, 25, 24, 25, 58, 25, 56, 58, 24, 58, 23, 23, 58, 22, 22, 58, 21, 20, 21, 16, 16, 21, 58, 20, 17, 19, 19, 17, 18, 16, 17, 20, 52, 55, 56, 56, 55, 58, 58, 15, 16, 58, 14, 15, 58, 13, 14, 58, 55, 13, 55, 12, 13, 12, 55, 11, 11, 55, 57, 11, 57, 10, 10, 57, 9, 9, 57, 8, 8, 57, 7, 7, 57, 6, 57, 0, 6, 57, 46, 47, 57, 47, 0, 5, 6, 1, 0, 1, 6, 44, 45, 57, 57, 45, 46, 4, 5, 2, 2, 5, 1, 4, 2, 3, 53, 52, 56, 55, 52, 54, 55, 54, 57, 54, 40, 41, 41, 42, 54, 54, 43, 57, 54, 42, 43, 43, 44, 57, 28, 29, 53, 29, 30, 53, 49, 52, 53, 51, 38, 39, 38, 51, 52, 52, 51, 54, 39, 40, 51, 54, 51, 40, 53, 30, 50, 32, 50, 31, 30, 31, 50, 32, 33, 50, 50, 33, 49, 49, 33, 36, 48, 49, 36, 50, 49, 53, 48, 36, 37, 49, 48, 52, 52, 48, 38, 48, 37, 38], "vertices": [2, 19, 659.3, 390.72, 0.29386, 20, 283.08, 489.34, 0.70614, 2, 19, 736.2, 354.67, 0.24161, 20, 366.61, 473.99, 0.75839, 2, 19, 779.87, 312.18, 0.2176, 20, 419.64, 443.98, 0.7824, 2, 19, 800.36, 275.21, 0.20592, 20, 448.84, 413.42, 0.79408, 2, 19, 803.64, 228.57, 0.19523, 20, 463.85, 369.14, 0.80477, 2, 19, 785.45, 179.05, 0.18311, 20, 458.83, 316.63, 0.81689, 2, 19, 740.21, 112.11, 0.15957, 20, 432.06, 240.39, 0.84043, 2, 19, 656.15, 38.28, 0.10476, 20, 369.5, 147.64, 0.89524, 2, 19, 633.69, 10.22, 0.07716, 20, 354.9, 114.81, 0.92284, 2, 19, 585.29, -60.12, 0.01827, 20, 325.94, 34.48, 0.98173, 2, 19, 580.64, -68.89, 0.01335, 20, 323.67, 24.82, 0.98665, 2, 19, 548.95, -137.11, 0, 20, 310.32, -49.21, 1, 1, 20, 309.36, -62.17, 1, 2, 19, 531.49, -217.76, 0.00013, 20, 313.91, -131.65, 0.99987, 3, 18, 678.23, -408.92, 0, 19, 531.15, -232.1, 0.00049, 20, 317.22, -145.61, 0.99951, 2, 19, 541.25, -264.93, 0.00083, 20, 335.32, -174.8, 0.99917, 2, 19, 559.57, -298.34, 0.00058, 20, 361.53, -202.47, 0.99942, 1, 20, 409.77, -236.25, 1, 2, 19, 613.18, -383.16, 0.00061, 20, 434.91, -270.9, 0.99939, 3, 18, 693.31, -618.1, 4e-05, 19, 616.21, -423.81, 0.00236, 20, 448.16, -309.44, 0.9976, 3, 18, 655.38, -670.24, 0.00066, 19, 598.19, -485.71, 0.0081, 20, 446.44, -373.89, 0.99124, 3, 18, 605.03, -701.71, 0.00221, 19, 561.48, -532.38, 0.01696, 20, 422.79, -428.35, 0.98083, 3, 18, 546.4, -719.56, 0.00517, 19, 512.37, -569.04, 0.03024, 20, 384.58, -476.28, 0.96459, 3, 18, 468.48, -720.17, 0.01165, 19, 439.26, -596.01, 0.05379, 20, 320.72, -520.92, 0.93456, 3, 18, 390.81, -695.87, 0.02349, 19, 357.95, -599.46, 0.0888, 20, 242.94, -544.9, 0.8877, 4, 17, 540.2, -698.24, 4e-05, 18, 334.21, -667.43, 0.03795, 19, 295.07, -591.87, 0.12421, 20, 180.2, -553.53, 0.8378, 4, 17, 458.47, -631.22, 0.00289, 18, 247.14, -607.54, 0.07923, 19, 192.85, -565.03, 0.20025, 20, 74.51, -553.51, 0.71763, 4, 17, 390.01, -575.09, 0.01385, 18, 174.18, -557.37, 0.13413, 19, 107.22, -542.55, 0.26466, 20, -14.02, -553.5, 0.58736, 4, 17, 291.29, -494.13, 0.05956, 18, 68.99, -485.03, 0.23718, 19, -16.27, -510.12, 0.31063, 20, -141.69, -553.48, 0.39263, 4, 17, 199.9, -400.61, 0.17891, 18, -29.95, -399.54, 0.33325, 19, -138.32, -463.21, 0.26865, 20, -271.66, -539.09, 0.21919, 4, 17, 131.4, -323.19, 0.36464, 18, -104.73, -328.17, 0.33879, 19, -232.86, -421.39, 0.18199, 20, -373.71, -522.64, 0.11459, 4, 17, 56.89, -238.97, 0.64582, 18, -186.07, -250.53, 0.22698, 19, -335.69, -375.9, 0.08397, 20, -484.73, -504.74, 0.04323, 4, 17, -8.79, -142.18, 0.90166, 18, -259.68, -159.62, 0.06917, 19, -435.75, -315.31, 0.02029, 20, -596.89, -471.53, 0.00887, 4, 17, -11.42, -130.4, 0.92035, 18, -263.29, -148.1, 0.05638, 19, -443.05, -305.69, 0.01628, 20, -606.39, -464.08, 0.00699, 1, 1, 217.85, 23.32, 1, 1, 1, 185, 20.87, 1, 1, 1, 61.36, 24.31, 1, 2, 17, 136.46, 106.49, 0.99549, 18, -135.9, 100.41, 0.00451, 2, 17, 276.59, 146.73, 0.465, 18, 0.34, 152.32, 0.535, 3, 17, 361.37, 167.23, 0.14494, 18, 83.09, 179.89, 0.85389, 19, -228.27, 120.25, 0.00117, 3, 17, 557.9, 190.4, 0.0002, 18, 276.97, 219.54, 0.64011, 19, -59.3, 223.24, 0.35968, 3, 18, 411.09, 240.8, 0.26899, 19, 59.69, 288.69, 0.72551, 20, -270.99, 238.45, 0.0055, 3, 18, 553.35, 252.2, 0.08215, 19, 189.67, 347.61, 0.82108, 20, -160.23, 328.43, 0.09677, 3, 18, 662.52, 251.93, 0.02624, 19, 292.48, 384.34, 0.72873, 20, -70.11, 390.06, 0.24503, 3, 18, 774.86, 239.78, 0.00488, 19, 402.29, 410.96, 0.57711, 20, 29.35, 443.68, 0.41801, 3, 18, 867.31, 214.76, 0.00035, 19, 497.75, 418.75, 0.45372, 20, 119.71, 475.45, 0.54593, 2, 19, 579.98, 412.09, 0.36407, 20, 200.93, 489.88, 0.63593, 2, 19, 628.46, 399.79, 0.31877, 20, 250.95, 490.28, 0.68123, 2, 17, 198.83, 57.55, 0.96156, 18, -69.63, 56.9, 0.03844, 4, 17, 195.72, -66.23, 0.71604, 18, -62.3, -66.7, 0.25191, 19, -281.52, -161.01, 0.02335, 20, -486.87, -283.14, 0.00869, 4, 17, 105.02, -164.88, 0.71064, 18, -144.36, -172.64, 0.20116, 19, -322.84, -288.49, 0.05983, 20, -494.48, -416.93, 0.02837, 3, 17, 477.17, 95.09, 0.00763, 18, 204.56, 117.76, 0.90693, 19, -92.94, 102.95, 0.08544, 4, 17, 422.01, -122.05, 0.00292, 18, 167.89, -103.25, 0.53289, 19, -52.56, -117.42, 0.41142, 20, -276.48, -182.85, 0.05276, 4, 17, 315.51, -311.54, 0.10959, 18, 77.74, -301.04, 0.35057, 19, -70.37, -334.05, 0.31878, 20, -238.71, -396.91, 0.22106, 3, 18, 475.27, 99.3, 0.10367, 19, 168.02, 177.3, 0.87249, 20, -137.94, 158.21, 0.02384, 3, 18, 411.56, -192.87, 0.00325, 19, 207.06, -119.18, 0.40639, 20, -24.92, -118.65, 0.59036, 4, 17, 523, -433.7, 0.00419, 18, 294.78, -405.29, 0.09064, 19, 169.15, -358.6, 0.28499, 20, -0.81, -359.85, 0.62018, 3, 18, 734.83, 0.34, 0.00051, 19, 445.75, 172.12, 0.38029, 20, 132.01, 223.7, 0.6192, 3, 18, 511.43, -506.96, 0.00914, 19, 407.43, -380.86, 0.05106, 20, 235.32, -320.9, 0.93979], "hull": 48, "edges": [0, 94, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 56, 58, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 54, 56, 50, 52, 52, 54, 58, 60, 60, 62], "width": 1152, "height": 1056}}, "c2": {"c2": {"type": "mesh", "uvs": [0.19088, 0, 0.24576, 0.0047, 0.31871, 0.02178, 0.39902, 0.06179, 0.47394, 0.10956, 0.5837, 0.20084, 0.71001, 0.33436, 0.83871, 0.49207, 0.92112, 0.62509, 0.9934, 0.74558, 0.99914, 0.83508, 0.99949, 0.9161, 0.88385, 0.93587, 0.86553, 0.93819, 0.78381, 0.96869, 0.70748, 0.98284, 0.61284, 0.98744, 0.55429, 1, 0.49492, 1, 0.41251, 0.99798, 0.33883, 0.99617, 0.2434, 0.99383, 0.1749, 0.98516, 0.10278, 0.95432, 0.05421, 0.91679, 0.01623, 0.86081, 0, 0.80115, 0, 0.78252, 0.00484, 0.7309, 0.03597, 0.68598, 0.08018, 0.65341, 0.0999, 0.63268, 0.11007, 0.61839, 0.1205, 0.59767, 0.12776, 0.55023, 0.12776, 0.53856, 0.12327, 0.47547, 0.12087, 0.45765, 0.10967, 0.40455, 0.10659, 0.3948, 0.08227, 0.32327, 0.07791, 0.31942, 0.02994, 0.23036, 0.00977, 0.16831, 0.00454, 0.10993, 0.01957, 0.06845, 0.05335, 0.03151, 0.11045, 0.00989, 0.16896, 0, 0.83368, 0.67064, 0.78139, 0.78729, 0.75603, 0.88652, 0.68105, 0.51047, 0.5733, 0.69327, 0.54794, 0.85519, 0.50833, 0.38338, 0.40691, 0.61493, 0.36413, 0.82211, 0.3379, 0.25106, 0.17151, 0.77162], "triangles": [12, 10, 11, 10, 50, 9, 43, 44, 45, 46, 43, 45, 47, 42, 43, 46, 47, 43, 0, 41, 42, 42, 47, 48, 42, 48, 0, 1, 41, 0, 1, 58, 41, 40, 41, 58, 39, 40, 58, 38, 39, 58, 37, 38, 58, 36, 37, 58, 56, 35, 36, 56, 36, 58, 34, 35, 56, 33, 34, 56, 59, 33, 56, 32, 33, 59, 31, 32, 59, 30, 31, 59, 29, 26, 27, 57, 59, 56, 57, 56, 53, 28, 29, 27, 59, 25, 26, 26, 29, 59, 59, 29, 30, 24, 25, 59, 23, 24, 59, 21, 22, 59, 23, 59, 22, 57, 21, 59, 20, 21, 57, 19, 20, 57, 54, 19, 57, 54, 18, 19, 58, 2, 3, 58, 3, 4, 58, 1, 2, 5, 58, 4, 55, 5, 6, 55, 58, 5, 56, 58, 55, 53, 56, 55, 52, 55, 6, 52, 6, 7, 49, 52, 7, 52, 53, 55, 53, 52, 49, 54, 57, 53, 54, 53, 50, 51, 16, 54, 15, 16, 51, 54, 17, 18, 16, 17, 54, 49, 7, 8, 50, 53, 49, 51, 54, 50, 49, 9, 50, 9, 49, 8, 10, 12, 50, 13, 51, 50, 12, 13, 50, 14, 51, 13, 15, 51, 14], "vertices": [3, 22, 935.94, -211.61, 0.00075, 23, 577.68, -335.39, 0.32215, 24, 227.14, -424.41, 0.6771, 3, 22, 879.23, -240.7, 0.00375, 23, 516.59, -353.58, 0.40171, 24, 163.41, -425.11, 0.59454, 3, 22, 797.84, -269.64, 0.01431, 23, 431.28, -367.1, 0.52236, 24, 77.66, -414.68, 0.46334, 3, 22, 696.54, -282.46, 0.04485, 23, 329.34, -361.13, 0.66744, 24, -18.72, -380.93, 0.28771, 3, 22, 596.26, -285.05, 0.10809, 23, 230.29, -345.28, 0.76399, 24, -109.6, -338.48, 0.12792, 3, 22, 437.59, -269.73, 0.33501, 23, 77.12, -301.11, 0.6591, 24, -244.74, -253.94, 0.00588, 3, 21, 599.16, -156.65, 0.00087, 22, 239.27, -226.54, 0.86993, 23, -109.91, -222.27, 0.1292, 2, 21, 376.24, -144.49, 0.30916, 22, 25.23, -163.07, 0.69084, 2, 21, 209.62, -112.83, 0.99594, 22, -129.53, -93.68, 0.00406, 1, 1, 28.08, -29.37, 1, 1, 1, 122.45, -23.04, 1, 1, 1, 207.84, -22.92, 1, 4, 21, 8.52, 149.31, 0.889, 22, -264.44, 207.91, 0.08832, 23, -525.38, 297.2, 0.01294, 24, -659.7, 486.88, 0.00974, 4, 21, 21.79, 166.04, 0.84489, 22, -247.66, 221.11, 0.1221, 23, -506.46, 307.1, 0.01868, 24, -638.78, 491.2, 0.01433, 4, 21, 65.97, 255.69, 0.60767, 22, -183.91, 298.09, 0.28889, 23, -429.68, 371.08, 0.05635, 24, -547.38, 531.63, 0.04709, 4, 21, 117.93, 328.73, 0.40539, 22, -116.45, 357.11, 0.39852, 23, -352.53, 416.73, 0.10189, 24, -460.66, 554.32, 0.0942, 4, 21, 191.99, 409.65, 0.19993, 22, -25.66, 418.68, 0.44115, 23, -252, 460.6, 0.16925, 24, -351.94, 568.88, 0.18967, 4, 21, 230.57, 466.95, 0.11802, 22, 25.14, 465.48, 0.41319, 23, -193.47, 497.29, 0.20278, 24, -285.58, 588.08, 0.26601, 4, 21, 279.18, 515.57, 0.06722, 22, 83.69, 501.52, 0.36265, 23, -129.3, 521.97, 0.22153, 24, -217.1, 594.18, 0.3486, 4, 21, 348.18, 581.55, 0.02463, 22, 166.09, 549.72, 0.27034, 23, -39.45, 554.24, 0.22089, 24, -121.85, 600.53, 0.48414, 4, 21, 409.85, 640.52, 0.00725, 22, 239.75, 592.81, 0.18923, 23, 40.86, 583.09, 0.19331, 24, -36.7, 606.21, 0.61021, 4, 21, 489.74, 716.92, 0.00046, 22, 335.16, 648.62, 0.10833, 23, 144.89, 620.45, 0.1389, 24, 73.59, 613.55, 0.7523, 3, 22, 407.5, 682.42, 0.07009, 23, 222.2, 640.41, 0.10242, 24, 153.41, 611.5, 0.82749, 3, 22, 495.67, 698.5, 0.04077, 23, 311.82, 640.04, 0.06875, 24, 239.48, 586.53, 0.89047, 3, 22, 564.3, 694.29, 0.02574, 23, 378.52, 623.32, 0.04919, 24, 299.02, 552.13, 0.92507, 3, 22, 632.68, 667.09, 0.01454, 23, 440.74, 584.03, 0.03339, 24, 348.05, 497.26, 0.95207, 3, 22, 681.64, 623.39, 0.00806, 23, 480.86, 532.09, 0.02369, 24, 372.36, 436.29, 0.96825, 3, 22, 691.93, 606.66, 0.00677, 23, 487.91, 513.76, 0.02171, 24, 374.1, 416.73, 0.97152, 3, 22, 715.68, 557.39, 0.00411, 23, 502.21, 460.97, 0.01759, 24, 373.35, 362.04, 0.9783, 3, 22, 709.78, 498.18, 0.00314, 23, 485.56, 403.84, 0.01631, 24, 341.64, 311.69, 0.98055, 3, 22, 684.18, 442.11, 0.00332, 23, 450.1, 353.42, 0.01719, 24, 293.7, 272.95, 0.97948, 3, 22, 676.18, 411.53, 0.00311, 23, 436.63, 324.83, 0.01674, 24, 272.89, 249.16, 0.98015, 3, 22, 674.05, 392.53, 0.00267, 23, 431.05, 306.54, 0.01534, 24, 262.5, 233.11, 0.98199, 3, 22, 675.21, 367.6, 0.0017, 23, 427.62, 281.82, 0.01154, 24, 252.41, 210.28, 0.98676, 3, 22, 694.25, 320.62, 0.00021, 23, 437.72, 232.14, 0.00317, 24, 248.48, 159.74, 0.99662, 3, 22, 700.7, 310.14, 6e-05, 23, 442.13, 220.66, 0.00171, 24, 249.57, 147.48, 0.99823, 1, 24, 260.64, 81.71, 1, 1, 24, 265.09, 63.25, 1, 2, 23, 512.39, 96.34, 0.00148, 24, 282.97, 8.65, 0.99852, 2, 23, 519.4, 88.03, 0.00383, 24, 287.43, -1.26, 0.99617, 2, 23, 572.75, 27.77, 0.04619, 24, 322.17, -73.87, 0.95381, 2, 23, 578.92, 25.81, 0.05044, 24, 327.56, -77.45, 0.94956, 2, 23, 664.47, -41.87, 0.10657, 24, 391.23, -166.02, 0.89343, 2, 23, 709.75, -94.52, 0.1327, 24, 420.3, -229.09, 0.8673, 2, 23, 737.49, -149.78, 0.15217, 24, 431.79, -289.85, 0.84783, 2, 23, 736.93, -196.84, 0.16693, 24, 418.33, -334.94, 0.83307, 2, 23, 714.4, -247.22, 0.18925, 24, 382.82, -377.19, 0.81075, 2, 23, 660.87, -292.23, 0.23302, 24, 318.99, -405.76, 0.76698, 3, 22, 957.56, -198.3, 0.00028, 23, 601.37, -326.28, 0.29581, 24, 252.43, -422.15, 0.70391, 1, 21, 247.27, -7.28, 1, 4, 21, 203.15, 122.47, 0.6324, 22, -81.32, 136.72, 0.31751, 23, -358.43, 193.63, 0.02958, 24, -527.61, 341.43, 0.02052, 4, 21, 149.96, 217.19, 0.55234, 22, -111.13, 241.19, 0.33637, 23, -368.57, 301.79, 0.06136, 24, -507.65, 448.22, 0.04993, 2, 21, 491.62, -1.68, 0.00228, 22, 170.54, -50.87, 0.99772, 4, 21, 443.61, 222.79, 0.01477, 22, 175.83, 178.62, 0.56651, 23, -97.95, 187.65, 0.29304, 24, -278.81, 264.12, 0.12568, 4, 21, 343.7, 364.22, 0.08475, 22, 111.4, 339.34, 0.44002, 23, -131.81, 357.47, 0.22967, 24, -264.71, 436.7, 0.24556, 2, 22, 411.09, -60.13, 0.19663, 23, 89.52, -90.2, 0.80337, 3, 22, 383.2, 209.27, 0.0678, 23, 111.52, 179.74, 0.48766, 24, -79.56, 198.97, 0.44454, 4, 21, 518.87, 490.08, 0.00454, 22, 310.96, 421.21, 0.17919, 23, 79.38, 401.34, 0.23962, 24, -49.59, 420.87, 0.57664, 3, 22, 652.27, -75.47, 0.0231, 23, 323.79, -149.52, 0.60319, 24, 34.08, -175.94, 0.37372, 3, 22, 528.81, 492.79, 0.03138, 23, 306.66, 431.74, 0.06968, 24, 177.3, 387.66, 0.89895], "hull": 49, "edges": [0, 96, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 40, 42, 36, 38, 38, 40], "width": 1158, "height": 1054}}, "c3": {"c3": {"type": "mesh", "uvs": [0.00953, 0, 0.09674, 0.02728, 0.14145, 0.05353, 0.23181, 0.08464, 0.25663, 0.0919, 0.40252, 0.12243, 0.41393, 0.11972, 0.55617, 0.14322, 0.66139, 0.18162, 0.71533, 0.22585, 0.73543, 0.24947, 0.74924, 0.29437, 0.75779, 0.37497, 0.72556, 0.54847, 0.72316, 0.67366, 0.74223, 0.71119, 0.7649, 0.7347, 0.77751, 0.74498, 0.86396, 0.80048, 0.88593, 0.80444, 0.94559, 0.80986, 0.97645, 0.83, 0.99912, 0.88909, 0.99905, 0.949, 0.95696, 0.9904, 0.89349, 0.96831, 0.83306, 0.84729, 0.80319, 0.81623, 0.72206, 0.76252, 0.69689, 0.76838, 0.65753, 0.85137, 0.53386, 0.9212, 0.53136, 0.92583, 0.4529, 0.99883, 0.40431, 0.9988, 0.36444, 0.99002, 0.29113, 0.94276, 0.22901, 0.87108, 0.17216, 0.78393, 0.10867, 0.65394, 0.06163, 0.52876, 0, 0.31537, 0, 0.28525, 0.0075, 0.03325, 0.00741, 0, 0.12338, 0.16328, 0.07313, 0.31687, 0.27414, 0.2495, 0.21522, 0.54321, 0.44914, 0.29262, 0.35558, 0.71566, 0.59816, 0.35459, 0.52885, 0.72375], "triangles": [42, 43, 45, 43, 0, 1, 43, 44, 0, 46, 41, 42, 52, 32, 33, 25, 20, 24, 23, 24, 22, 24, 20, 21, 25, 26, 19, 26, 18, 19, 25, 19, 20, 22, 24, 21, 32, 52, 31, 31, 52, 30, 30, 52, 29, 26, 27, 18, 27, 28, 17, 28, 16, 17, 27, 17, 18, 28, 29, 14, 28, 15, 16, 28, 14, 15, 14, 29, 52, 14, 52, 13, 13, 52, 51, 51, 8, 9, 52, 49, 51, 12, 13, 51, 11, 51, 9, 11, 9, 10, 12, 51, 11, 49, 7, 51, 51, 7, 8, 50, 33, 34, 36, 50, 35, 52, 33, 50, 50, 34, 35, 36, 37, 50, 37, 38, 50, 38, 48, 50, 50, 49, 52, 50, 48, 49, 49, 6, 7, 38, 39, 48, 49, 47, 5, 5, 6, 49, 47, 49, 48, 39, 40, 48, 40, 46, 48, 48, 46, 47, 47, 3, 4, 47, 4, 5, 46, 45, 47, 47, 45, 3, 45, 2, 3, 40, 41, 46, 45, 46, 42, 43, 1, 45, 45, 1, 2], "vertices": [1, 1, 214.43, 25.74, 1, 4, 25, -14.86, 95.44, 0.95771, 26, -162.31, 156.3, 0.0417, 27, -331.48, 267.29, 0.00057, 28, -528.7, 326.16, 1e-05, 4, 25, 25.08, 126.52, 0.85047, 26, -113.95, 171.2, 0.14301, 27, -280.88, 266.67, 0.00573, 28, -478.53, 319.53, 0.00078, 4, 25, 93.06, 197.12, 0.45754, 26, -25.35, 213.12, 0.47217, 27, -183.7, 279.46, 0.05599, 28, -380.53, 320.68, 0.0143, 4, 25, 110.98, 216.97, 0.35229, 26, -1.56, 225.32, 0.53938, 27, -157.32, 283.79, 0.08454, 28, -353.81, 321.85, 0.02379, 4, 25, 209.29, 337.97, 0.04356, 26, 133.26, 303.61, 0.47136, 27, -5.01, 317.06, 0.31167, 28, -198.63, 336.78, 0.17341, 4, 25, 214.01, 349.23, 0.03524, 26, 141.67, 312.47, 0.44784, 27, 5.71, 322.92, 0.32371, 28, -187.29, 341.32, 0.19321, 4, 25, 306.21, 469.42, 0.00039, 26, 270.49, 392.16, 0.19012, 27, 152.74, 359.36, 0.31117, 28, -36.97, 360.03, 0.49831, 3, 26, 374.56, 439.81, 0.08626, 27, 266.4, 372.86, 0.19927, 28, 77.49, 359.92, 0.71447, 3, 26, 438.15, 451.02, 0.05754, 27, 330.37, 364.07, 0.14947, 28, 139.96, 343.6, 0.79299, 3, 26, 464.83, 451.37, 0.05054, 27, 355.88, 356.24, 0.13564, 28, 164.35, 332.78, 0.81382, 3, 26, 495.13, 436.17, 0.04344, 27, 380.07, 332.5, 0.12058, 28, 185.55, 306.34, 0.83599, 3, 26, 535.93, 398.35, 0.03254, 27, 407.34, 284, 0.09526, 28, 206.87, 254.95, 0.8722, 3, 26, 581.39, 284.08, 0.00913, 27, 415.64, 161.3, 0.02674, 28, 200.52, 132.13, 0.96413, 3, 26, 631.65, 215.16, 0.00045, 27, 442.39, 80.31, 0.00021, 28, 217.46, 48.53, 0.99934, 2, 26, 663.27, 207.35, 1e-05, 28, 242.94, 28.24, 0.99999, 1, 28, 269.96, 18.12, 1, 1, 28, 284.56, 14.34, 1, 1, 28, 382.31, -1.62, 1, 1, 28, 405.58, 1.05, 1, 1, 28, 467.94, 11.83, 1, 1, 28, 502.89, 5.92, 1, 1, 28, 535.42, -27.8, 1, 1, 28, 544.64, -67.54, 1, 1, 28, 507.65, -105.14, 1, 1, 28, 438.78, -105.79, 1, 1, 28, 357.7, -40.11, 1, 1, 28, 322.08, -26.72, 1, 1, 28, 230.1, -10.66, 1, 1, 28, 205.05, -20.61, 1, 2, 27, 418.43, -57.18, 0.00477, 28, 177.33, -85.13, 0.99523, 2, 27, 311.6, -146.62, 0.34288, 28, 60.63, -161.25, 0.65712, 2, 27, 310.19, -150.49, 0.36175, 28, 58.76, -164.92, 0.63825, 2, 27, 249.08, -225.61, 0.6912, 28, -10.83, -232.24, 0.3088, 3, 26, 500.59, -166.77, 0.00163, 27, 200.72, -243.18, 0.79072, 28, -60.94, -243.94, 0.20764, 3, 26, 463.57, -187.92, 0.00934, 27, 159, -251.98, 0.85497, 28, -103.41, -247.72, 0.1357, 3, 26, 382.5, -210.08, 0.06522, 27, 75.04, -248.26, 0.89549, 28, -186.34, -234.05, 0.03929, 3, 26, 300.6, -211.84, 0.21545, 27, -3.47, -224.87, 0.7806, 28, -261.51, -201.5, 0.00396, 2, 26, 216.66, -201.86, 0.50118, 27, -80.32, -189.67, 0.49882, 3, 25, 355.87, -116.32, 0.01574, 26, 109.27, -173.13, 0.87126, 27, -173.77, -129.46, 0.113, 3, 25, 257.15, -114.37, 0.22099, 26, 17.65, -136.32, 0.77561, 27, -249.72, -66.36, 0.0034, 1, 1, 429.16, 14.94, 1, 1, 1, 408.65, 15.01, 1, 1, 1, 237.07, 23.52, 1, 1, 1, 214.42, 23.49, 1, 4, 25, 78.88, 71.22, 0.8709, 26, -83.25, 100.42, 0.12539, 27, -273.31, 189.89, 0.00333, 28, -480.15, 242.4, 0.00038, 1, 25, 140.38, -28.73, 1, 4, 25, 212.23, 176.82, 0.14935, 26, 78.88, 151.89, 0.6998, 27, -103.21, 189.26, 0.12174, 28, -311.33, 221.56, 0.02912, 2, 26, 152.2, -44.39, 0.96745, 27, -93.49, -20.03, 0.03255, 4, 25, 333.93, 319.66, 0.00605, 26, 243.31, 242.3, 0.30677, 27, 81, 225.01, 0.42834, 28, -124.17, 235.16, 0.25884, 3, 26, 341.62, -46.06, 0.01332, 27, 86.33, -79.6, 0.98131, 28, -155.08, -67.93, 0.00537, 3, 26, 393.86, 305.69, 0.08109, 27, 243.73, 239.28, 0.23434, 28, 39.1, 229.98, 0.68457, 2, 27, 260.66, -22.07, 0.07573, 28, 24.85, -31.52, 0.92427], "hull": 45, "edges": [0, 88, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88], "width": 1059, "height": 681}}, "c4": {"c4": {"type": "mesh", "uvs": [0.98999, 0, 0.99445, 0, 1, 0.20972, 1, 0.26004, 0.95152, 0.46854, 0.89798, 0.62064, 0.84395, 0.74042, 0.78184, 0.84897, 0.71766, 0.92601, 0.66348, 0.97082, 0.59959, 0.9997, 0.54482, 0.99849, 0.47854, 0.93549, 0.46062, 0.91703, 0.39363, 0.88018, 0.33609, 0.83916, 0.30037, 0.76361, 0.27731, 0.7589, 0.23219, 0.78937, 0.19005, 0.81784, 0.16612, 0.84362, 0.10897, 0.9603, 0.05177, 0.9895, 0.00231, 0.95506, 0, 0.90414, 0, 0.88743, 0.02399, 0.82574, 0.05347, 0.80751, 0.09853, 0.79859, 0.16052, 0.78632, 0.18143, 0.77316, 0.22144, 0.7388, 0.25182, 0.71271, 0.275, 0.67002, 0.27356, 0.56368, 0.27298, 0.54662, 0.24974, 0.42086, 0.24192, 0.35301, 0.251, 0.28115, 0.27028, 0.22115, 0.41306, 0.12954, 0.5055, 0.13041, 0.51084, 0.13007, 0.57577, 0.1106, 0.67621, 0.1017, 0.68297, 0.10227, 0.77725, 0.07284, 0.84106, 0.03585, 0.83858, 0.17089, 0.88683, 0.34462, 0.68179, 0.23604, 0.71969, 0.52921, 0.5026, 0.30119, 0.56979, 0.67307, 0.36303, 0.35005, 0.43712, 0.71651], "triangles": [0, 2, 48, 0, 1, 2, 3, 49, 2, 54, 39, 40, 38, 39, 54, 37, 38, 54, 36, 37, 54, 35, 36, 54, 55, 34, 35, 55, 33, 34, 55, 54, 52, 54, 55, 35, 16, 17, 33, 32, 33, 17, 55, 16, 33, 18, 31, 32, 18, 32, 17, 30, 31, 18, 19, 30, 18, 29, 30, 19, 15, 16, 55, 20, 29, 19, 14, 15, 55, 24, 25, 26, 13, 55, 53, 14, 55, 13, 22, 23, 24, 20, 21, 28, 20, 28, 29, 27, 28, 21, 22, 26, 27, 22, 27, 21, 22, 24, 26, 52, 40, 41, 54, 40, 52, 43, 52, 42, 52, 41, 42, 52, 51, 53, 55, 52, 53, 7, 8, 53, 7, 53, 51, 12, 13, 53, 12, 53, 11, 8, 9, 53, 53, 10, 11, 9, 10, 53, 50, 44, 45, 46, 50, 45, 49, 51, 50, 51, 49, 5, 52, 43, 50, 43, 44, 50, 6, 51, 5, 7, 51, 6, 51, 52, 50, 48, 46, 47, 0, 48, 47, 2, 49, 48, 4, 49, 3, 50, 46, 48, 49, 50, 48, 5, 49, 4], "vertices": [1, 1, 206.25, -27.98, 1, 1, 1, 206.27, -23.23, 1, 1, 1, 348.05, -17.79, 1, 1, 1, 382.07, -17.9, 1, 2, 29, 199.71, 103.47, 0.51022, 30, -35.75, 99.7, 0.48978, 3, 29, 316.35, 118.21, 0.01734, 30, 71.17, 148.59, 0.92034, 31, -202.76, 70.9, 0.06231, 3, 30, 165.54, 179.59, 0.63673, 31, -129.2, 137.65, 0.36324, 32, -384.53, 131.39, 3e-05, 3, 30, 262.33, 199.38, 0.23661, 31, -48.86, 195.14, 0.75196, 32, -304.72, 189.62, 0.01143, 3, 30, 348.25, 200.71, 0.05262, 31, 29.07, 231.35, 0.89023, 32, -227.13, 226.55, 0.05715, 3, 30, 412.67, 190.82, 0.00795, 31, 91.93, 248.56, 0.86802, 32, -164.43, 244.34, 0.12403, 2, 31, 162.57, 253.05, 0.77088, 32, -93.83, 249.47, 0.22912, 2, 31, 219.37, 239.74, 0.66793, 32, -36.92, 236.69, 0.33207, 2, 31, 279.19, 183.01, 0.42244, 32, 23.42, 180.51, 0.57756, 2, 31, 295.15, 166.73, 0.31793, 32, 39.54, 164.38, 0.68207, 2, 31, 359.5, 127.1, 0.05429, 32, 104.25, 125.34, 0.94571, 2, 31, 413.41, 86.88, 0.00066, 32, 158.52, 85.62, 0.99934, 1, 32, 185.26, 27.82, 1, 1, 32, 208.64, 19.66, 1, 1, 32, 259.9, 29.95, 1, 1, 32, 307.77, 39.57, 1, 1, 32, 336.29, 51.39, 1, 1, 32, 412.07, 116.08, 1, 1, 32, 475.74, 122.89, 1, 1, 32, 522.51, 89.28, 1, 1, 32, 517.85, 55.09, 1, 1, 32, 515.53, 44.04, 1, 1, 32, 481.95, 8.47, 1, 1, 32, 448.7, 2.85, 1, 1, 32, 400.5, 6.81, 1, 1, 32, 334.18, 12.25, 1, 1, 32, 310.55, 8.12, 1, 1, 32, 264.09, -5.86, 1, 3, 30, 661.9, -209.85, 1e-05, 31, 482.74, -15.86, 9e-05, 32, 228.8, -16.47, 0.9999, 3, 30, 624.9, -218.42, 0.00097, 31, 452.44, -38.75, 0.00824, 32, 198.71, -39.65, 0.9908, 3, 30, 583.45, -277.17, 0.01193, 31, 438.52, -109.29, 0.08905, 32, 185.44, -110.31, 0.89902, 3, 30, 577.11, -286.82, 0.01512, 31, 436.65, -120.69, 0.10949, 32, 183.68, -121.73, 0.87539, 3, 30, 546.56, -369.93, 0.03987, 31, 442.61, -209.03, 0.23307, 32, 190.44, -210.01, 0.72706, 3, 30, 526.03, -411.78, 0.05134, 31, 440.9, -255.62, 0.27403, 32, 189.17, -256.61, 0.67463, 3, 30, 489.41, -445.13, 0.06358, 31, 421.04, -300.99, 0.30884, 32, 169.73, -302.17, 0.62758, 3, 30, 448.81, -465.58, 0.07489, 31, 392.29, -336.2, 0.3351, 32, 141.3, -337.64, 0.59, 4, 29, 353.57, -494.6, 0.00128, 30, 289.68, -425.14, 0.18374, 31, 230.49, -364.09, 0.46836, 32, -20.23, -367.02, 0.34662, 4, 29, 295.89, -414.82, 0.01647, 30, 210.81, -366.23, 0.31462, 31, 134.46, -342.41, 0.4864, 32, -116.46, -346.22, 0.18251, 4, 29, 292.35, -410.37, 0.01861, 30, 206.09, -363.04, 0.32583, 31, 128.85, -341.42, 0.48347, 32, -122.08, -345.28, 0.17209, 4, 29, 240.87, -362.35, 0.05655, 30, 142.63, -332.58, 0.45045, 31, 58.49, -339.45, 0.41255, 32, -192.46, -343.96, 0.08045, 4, 29, 172.83, -279.59, 0.20657, 30, 52.98, -273.92, 0.55801, 31, -47.28, -322.38, 0.22022, 32, -298.38, -327.87, 0.0152, 4, 29, 168.88, -273.55, 0.22432, 30, 47.41, -269.33, 0.55752, 31, -54.24, -320.47, 0.20533, 32, -305.35, -326.01, 0.01283, 4, 29, 93.51, -204.29, 0.57577, 30, -45.2, -225.74, 0.37075, 31, -156.58, -318.37, 0.05323, 32, -407.71, -324.86, 0.00025, 3, 29, 33.2, -164.23, 0.82323, 30, -114.73, -205.51, 0.1645, 31, -228.31, -328.22, 0.01227, 3, 29, 108.41, -112.43, 0.74519, 30, -58.41, -133.62, 0.23921, 31, -206.16, -239.62, 0.0156, 3, 29, 172.82, -1.6, 0.99285, 30, -30.04, -8.61, 0.00712, 31, -231.17, -113.9, 3e-05, 4, 29, 242.59, -221.15, 0.14343, 30, 102.11, -197.31, 0.6178, 31, -33.62, -232.41, 0.22757, 32, -285.54, -237.77, 0.01119, 2, 30, 187.27, -13.86, 0.95134, 31, -30.56, -30.18, 0.04866, 4, 29, 390.86, -349.12, 0.00625, 30, 281.83, -275.16, 0.24617, 31, 162.23, -230.31, 0.55534, 32, -89.73, -233.87, 0.19224, 2, 31, 146.23, 30.58, 0.98007, 32, -108.13, 26.86, 0.01993, 3, 30, 421.05, -336.82, 0.08102, 31, 314.49, -229.92, 0.36602, 32, 62.52, -232.08, 0.55296, 1, 32, 36.19, 26.57, 1], "hull": 48, "edges": [0, 94, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 60, 62, 62, 64, 34, 36, 36, 38, 54, 56, 56, 58], "width": 1065, "height": 676}}}}], "animations": {"ain201": {"bones": {"root": {"rotate": [{"angle": 20.4}], "translate": [{"x": -12.28, "y": -4.57}, {"time": 1.5667, "x": -51.43, "y": -15.24, "curve": "stepped"}, {"time": 2.7, "x": -51.43, "y": -15.24}, {"time": 4.2667, "x": -85.79, "y": -5.9}, {"time": 5.6333, "x": -93.47, "y": -8.75}, {"time": 6.2, "x": -85.79, "y": -5.9}, {"time": 7.7667, "x": -53.1, "y": 13.16}, {"time": 8.5333, "x": -51.32, "y": 8.38}, {"time": 10.8667, "x": -12.28, "y": -4.57}]}, "c5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 10.8667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 10.8667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "x": 21.6, "y": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 10.8667}]}, "c6": {"rotate": [{"angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0333}, {"time": 0.5333, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 0.9, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.9333}, {"time": 1.4, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.8333}, {"time": 2.3, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 2.7, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.7333}, {"time": 3.2333, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.6, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 3.6333}, {"time": 4.1333, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 4.5333}, {"time": 5, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 5.4333, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 5.4667}, {"time": 5.9333, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 6.3667}, {"time": 6.8333, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 7.2667, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 7.3}, {"time": 7.7667, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 8.1667, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 8.2}, {"time": 8.6333, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 9.1}, {"time": 9.5333, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 9.9667, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 10}, {"time": 10.4667, "angle": 7.03, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 10.8667, "angle": 0.19}], "scale": [{"x": 0.987, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 0.9, "x": 0.987, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 2.7, "x": 0.987, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.6, "x": 0.987, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 5.4333, "x": 0.987, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 7.2667, "x": 0.987, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 7.3, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 8.1667, "x": 0.987, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 9.9667, "x": 0.987, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "x": 0.54, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 10.8667, "x": 0.987}]}, "c7": {"rotate": [{"angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.0667}, {"time": 0.5333, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 0.9, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.9667}, {"time": 1.4333, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.8333, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.9}, {"time": 2.3333, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.7, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7667}, {"time": 3.2333, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 3.6, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 3.6667}, {"time": 4.1667, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 4.5333, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 4.6}, {"time": 5.0333, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5.4333, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 5.5333}, {"time": 5.9667, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 6.3667, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 6.4667}, {"time": 6.8667, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 7.2667, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 7.3333}, {"time": 7.7667, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 8.1667, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 8.2333}, {"time": 8.6667, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 9.1, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 9.1667}, {"time": 9.5667, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 9.9667, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 10.0667}, {"time": 10.4667, "angle": 7.03, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 10.8667, "angle": 0.64}], "scale": [{"x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 0.9, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.8333, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.7, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 3.6, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 4.5333, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5.4333, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 6.3667, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 7.2667, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 8.1667, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 9.1, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 9.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.5667, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 9.9667, "x": 0.958, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 10.0667, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "x": 0.54, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 10.8667, "x": 0.958}]}, "c8": {"rotate": [{"angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.1}, {"time": 0.5667, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 0.9, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1}, {"time": 1.4333, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.8333, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.9}, {"time": 2.3667, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 2.7, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.8333}, {"time": 3.2667, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 3.6, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 3.7}, {"time": 4.1667, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 4.5333, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 4.6}, {"time": 5.0667, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 5.4333, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 5.5667}, {"time": 5.9667, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 6.3667, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 6.4667}, {"time": 6.9, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 7.2667, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 7.3667}, {"time": 7.8, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 8.1667, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 8.2667}, {"time": 8.6667, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 9.1, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 9.1667}, {"time": 9.6, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 9.9667, "angle": 1.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 10.1}, {"time": 10.5, "angle": 7.03, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 10.8667, "angle": 1.22}], "scale": [{"x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 0.9, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.8333, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 2.7, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 3.6, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 4.5333, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 5.4333, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 5.5667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 6.3667, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 7.2667, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 7.3667, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 8.1667, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 8.2667, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 9.1, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 9.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 9.9667, "x": 0.92, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 10.1, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "x": 0.54, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 10.8667, "x": 0.92}]}, "c1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 10.8667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "x": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 10.8667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "x": -20.4, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 10.8667}]}, "c2": {"rotate": [{"angle": -0.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333}, {"time": 0.5333, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.9, "angle": -0.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.9333}, {"time": 1.4, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.8333}, {"time": 2.3, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.7, "angle": -0.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.7333}, {"time": 3.2333, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.6, "angle": -0.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.6333}, {"time": 4.1333, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4.5333}, {"time": 5, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5.4333, "angle": -0.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.4667}, {"time": 5.9333, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.3667}, {"time": 6.8333, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 7.2667, "angle": -0.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 7.3}, {"time": 7.7667, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 8.1667, "angle": -0.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.2}, {"time": 8.6333, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 9.1}, {"time": 9.5333, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 9.9667, "angle": -0.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 10}, {"time": 10.4667, "angle": -9.58, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 10.8667, "angle": -0.41}], "scale": [{"x": 0.981, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.9, "x": 0.981, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.7, "x": 0.981, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.6, "x": 0.981, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5.4333, "x": 0.981, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 7.2667, "x": 0.981, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 7.3, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 8.1667, "x": 0.981, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 9.9667, "x": 0.981, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "x": 0.56, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 10.8667, "x": 0.981}]}, "c3": {"rotate": [{"angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667}, {"time": 0.5667, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.9, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.9667}, {"time": 1.4333, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.8333, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.9}, {"time": 2.3667, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.7, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7667}, {"time": 3.2667, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.6, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667}, {"time": 4.1667, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.5333, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.6}, {"time": 5.0667, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.4333, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.5333}, {"time": 5.9667, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.3667, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.4667}, {"time": 6.9, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 7.2667, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.3333}, {"time": 7.8, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.1667, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333}, {"time": 8.6667, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.1, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.1667}, {"time": 9.6, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.9667, "angle": -1.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.0667}, {"time": 10.5, "angle": -9.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10.8667, "angle": -1.25}], "scale": [{"x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.9, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.8333, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.7, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.6, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.5333, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.4333, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.3667, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 7.2667, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.1667, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.1, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.9667, "x": 0.943, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.0667, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "x": 0.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10.8667, "x": 0.943}]}, "c4": {"rotate": [{"angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.6, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333}, {"time": 1.4667, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8333, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333}, {"time": 2.3667, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.7, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8333}, {"time": 3.3, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7333}, {"time": 4.2, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5333, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6667}, {"time": 5.0667, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.4333, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.5667}, {"time": 6, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.3667, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5}, {"time": 6.9, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.2667, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3667}, {"time": 7.8333, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3}, {"time": 8.7333, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.1, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.2}, {"time": 9.6, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.9667, "angle": -2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.1}, {"time": 10.5333, "angle": -9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.8667, "angle": -2.32}], "scale": [{"x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8333, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.7, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5333, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.4333, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.5667, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.3667, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.2667, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.1, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.2, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.9667, "x": 0.893, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.1, "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "x": 0.56, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.8667, "x": 0.893}]}, "c9": {"rotate": [{"angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.6, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333}, {"time": 1.4667, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8333, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333}, {"time": 2.3667, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.7, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8333}, {"time": 3.3, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7333}, {"time": 4.2, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5333, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6667}, {"time": 5.0667, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.4333, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.5667}, {"time": 6, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.3667, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5}, {"time": 6.9, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.2667, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3667}, {"time": 7.8333, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3}, {"time": 8.7333, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.1, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.2}, {"time": 9.6, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.9667, "angle": -2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.1}, {"time": 10.5333, "angle": -9.11, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.8667, "angle": -2.21}], "scale": [{"x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.6, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333}, {"time": 1.4667, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8333, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333}, {"time": 2.3667, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.7, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8333}, {"time": 3.3, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7333}, {"time": 4.2, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5333, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6667}, {"time": 5.0667, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.4333, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.5667}, {"time": 6, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.3667, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5}, {"time": 6.9, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.2667, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3667}, {"time": 7.8333, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3}, {"time": 8.7333, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.1, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.2}, {"time": 9.6, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.9667, "x": 0.956, "y": 0.971, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.1}, {"time": 10.5333, "x": 0.82, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.8667, "x": 0.956, "y": 0.971}], "shear": [{"x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.6, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333}, {"time": 1.4667, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8333, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333}, {"time": 2.3667, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.7, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8333}, {"time": 3.3, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7333}, {"time": 4.2, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5333, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6667}, {"time": 5.0667, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.4333, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.5667}, {"time": 6, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.3667, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5}, {"time": 6.9, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.2667, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3667}, {"time": 7.8333, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3}, {"time": 8.7333, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.1, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.2}, {"time": 9.6, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.9667, "x": -2.62, "y": -4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.1}, {"time": 10.5333, "x": -10.8, "y": -20.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.8667, "x": -2.62, "y": -4.94}]}, "c10": {"rotate": [{"angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 0.1, "angle": -0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1333}, {"time": 0.6333, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 0.9, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 1.0333}, {"time": 1.5333, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 1.8333, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 1.9333, "angle": -0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.9667}, {"time": 2.4333, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 2.7, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 2.8333, "angle": -0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.8667}, {"time": 3.3333, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 3.6, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 3.7333}, {"time": 4.2333, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 4.5333, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 4.6667, "angle": -0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 4.7}, {"time": 5.1667, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 5.4333, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 5.5667, "angle": -0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 5.6}, {"time": 6.0333, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 6.3667, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 6.5, "angle": -0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 6.5333}, {"time": 6.9667, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 7.2667, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 7.3667, "angle": -0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 7.4333}, {"time": 7.8667, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 8.1667, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 8.3}, {"time": 8.7667, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 9.1, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 9.2, "angle": -0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 9.2333}, {"time": 9.6667, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 9.9667, "angle": -3.05, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 10.1, "angle": -0.25, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 10.1333}, {"time": 10.6, "angle": -9.11, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 10.8667, "angle": -3.05}], "scale": [{"x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 0.1, "x": 0.995, "y": 0.997, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 0.9, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 1.8333, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 1.9333, "x": 0.995, "y": 0.997, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 2.7, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 2.8333, "x": 0.995, "y": 0.997, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 3.6, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 4.5333, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 4.6667, "x": 0.995, "y": 0.997, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 4.7, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 5.4333, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 5.5667, "x": 0.995, "y": 0.997, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 6.3667, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 6.5, "x": 0.995, "y": 0.997, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 7.2667, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 7.3667, "x": 0.995, "y": 0.997, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 7.4333, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 8.1667, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 8.3, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 9.1, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 9.2, "x": 0.995, "y": 0.997, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 9.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 9.9667, "x": 0.94, "y": 0.96, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 10.1, "x": 0.995, "y": 0.997, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 10.1333, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "x": 0.82, "y": 0.88, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 10.8667, "x": 0.94, "y": 0.96}]}, "c11": {"rotate": [{"angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 0.1, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2}, {"time": 0.6667, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 0.9, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 1.0333, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.1}, {"time": 1.5667, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 1.8333, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 1.9333, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2}, {"time": 2.4333, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 2.7, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 2.8333, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.9}, {"time": 3.3667, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 3.6, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 3.7333, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 3.8667}, {"time": 4.2667, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 4.5333, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 4.6667, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 4.7333}, {"time": 5.1667, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 5.4333, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 5.5667, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 5.6333}, {"time": 6.1, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 6.3667, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 6.5, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 6.5667}, {"time": 6.9667, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 7.2667, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 7.3667, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 7.4667}, {"time": 7.9, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8.1667, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 8.3, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 8.3667}, {"time": 8.8, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 9.1, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 9.2, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 9.2667}, {"time": 9.6667, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 9.9667, "angle": -4.02, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 10.1, "angle": -0.83, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 10.1667}, {"time": 10.6333, "angle": -9.11, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 10.8667, "angle": -4.02}], "scale": [{"x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 0.1, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 0.9, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 1.0333, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 1.8333, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 1.9333, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 2.7, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 2.8333, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 3.6, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 3.7333, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 4.5333, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 4.6667, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 4.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 5.4333, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 5.5667, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 5.6333, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 6.3667, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 6.5, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 7.2667, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 7.3667, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8.1667, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 8.3, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 8.3667, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 9.1, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 9.2, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 9.9667, "x": 0.921, "y": 0.947, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 10.1, "x": 0.984, "y": 0.989, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "x": 0.82, "y": 0.88, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 10.8667, "x": 0.921, "y": 0.947}]}, "c12": {"rotate": [{"angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 0.1, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2333}, {"time": 0.7, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 0.9, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 1.0333, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.1}, {"time": 1.6, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 1.8333, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 1.9333, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.0333}, {"time": 2.5333, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 2.7, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 2.8333, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.9333}, {"time": 3.4, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 3.6, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 3.7333, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 3.8667}, {"time": 4.3, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 4.5333, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 4.6667, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 4.8}, {"time": 5.2333, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 5.4333, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 5.5667, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 5.6667}, {"time": 6.1333, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 6.3667, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 6.5, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 6.6}, {"time": 7.0333, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 7.2667, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 7.3667, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 7.5}, {"time": 7.9333, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 8.1667, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 8.3, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 8.3667}, {"time": 8.8333, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 9.1, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 9.2, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 9.3}, {"time": 9.8, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 9.9667, "angle": -5, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 10.1, "angle": -1.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 10.2}, {"time": 10.6667, "angle": -9.11, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 10.8667, "angle": -5}], "scale": [{"x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 0.1, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 0.9, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 1.0333, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 1.8333, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 1.9333, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 2.7, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 2.8333, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 3.6, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 3.7333, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 4.5333, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 4.6667, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 5.4333, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 5.5667, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 6.3667, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 6.5, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 7.2667, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 7.3667, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 8.1667, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 8.3, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 8.3667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 9.1, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 9.2, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 9.3, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 9.9667, "x": 0.901, "y": 0.934, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 10.1, "x": 0.969, "y": 0.979, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 10.2, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": 0.82, "y": 0.88, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 10.8667, "x": 0.901, "y": 0.934}]}, "c13": {"rotate": [{"angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.6, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333}, {"time": 1.4667, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8333, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333}, {"time": 2.3667, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.7, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8333}, {"time": 3.3, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7333}, {"time": 4.2, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5333, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6667}, {"time": 5.0667, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.4333, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.5667}, {"time": 6, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.3667, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5}, {"time": 6.9, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.2667, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3667}, {"time": 7.8333, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3}, {"time": 8.7333, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.1, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.2}, {"time": 9.6, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.9667, "angle": 1.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.1}, {"time": 10.5333, "angle": 7.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.8667, "angle": 1.71}], "scale": [{"x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.6, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333}, {"time": 1.4667, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8333, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333}, {"time": 2.3667, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.7, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8333}, {"time": 3.3, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7333}, {"time": 4.2, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5333, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6667}, {"time": 5.0667, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.4333, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.5667}, {"time": 6, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.3667, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5}, {"time": 6.9, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.2667, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3667}, {"time": 7.8333, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3}, {"time": 8.7333, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.1, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.2}, {"time": 9.6, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.9667, "x": 0.961, "y": 0.981, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.1}, {"time": 10.5333, "x": 0.84, "y": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.8667, "x": 0.961, "y": 0.981}], "shear": [{"x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.6, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333}, {"time": 1.4667, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8333, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333}, {"time": 2.3667, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.7, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8333}, {"time": 3.3, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7333}, {"time": 4.2, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5333, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6667}, {"time": 5.0667, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.4333, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.5667}, {"time": 6, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.3667, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5}, {"time": 6.9, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.2667, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3667}, {"time": 7.8333, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3}, {"time": 8.7333, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.1, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.2}, {"time": 9.6, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.9667, "x": 2.62, "y": 3.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.1}, {"time": 10.5333, "x": 10.8, "y": 15.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.8667, "x": 2.62, "y": 3.78}]}, "c14": {"rotate": [{"angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 0.1, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1333}, {"time": 0.6333, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 0.9, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 1.0333}, {"time": 1.5333, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 1.8333, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 1.9333, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.9667}, {"time": 2.4333, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 2.7, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 2.8333, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.8667}, {"time": 3.3333, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 3.6, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 3.7333}, {"time": 4.2333, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 4.5333, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 4.6667, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 4.7}, {"time": 5.1667, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 5.4333, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 5.5667, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 5.6}, {"time": 6.0333, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 6.3667, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 6.5, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 6.5333}, {"time": 6.9667, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 7.2667, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 7.3667, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 7.4333}, {"time": 7.8667, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 8.1667, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 8.3}, {"time": 8.7667, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 9.1, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 9.2, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 9.2333}, {"time": 9.6667, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 9.9667, "angle": 2.37, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 10.1, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 10.1333}, {"time": 10.6, "angle": 7.07, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 10.8667, "angle": 2.37}], "scale": [{"x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 0.1, "x": 0.996, "y": 0.998, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 0.9, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 1.8333, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 1.9333, "x": 0.996, "y": 0.998, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 2.7, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 2.8333, "x": 0.996, "y": 0.998, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 3.6, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 4.5333, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 4.6667, "x": 0.996, "y": 0.998, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 4.7, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 5.4333, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 5.5667, "x": 0.996, "y": 0.998, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 6.3667, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 6.5, "x": 0.996, "y": 0.998, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 7.2667, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 7.3667, "x": 0.996, "y": 0.998, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 7.4333, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 8.1667, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 8.3, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 9.1, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 9.2, "x": 0.996, "y": 0.998, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 9.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 9.9667, "x": 0.946, "y": 0.973, "curve": 0.366, "c2": 0.46, "c3": 0.714, "c4": 0.86}, {"time": 10.1, "x": 0.996, "y": 0.998, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 10.1333, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "x": 0.84, "y": 0.92, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 10.8667, "x": 0.946, "y": 0.973}]}, "c15": {"rotate": [{"angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 0.1, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2}, {"time": 0.6667, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 0.9, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 1.0333, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.1}, {"time": 1.5667, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 1.8333, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 1.9333, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2}, {"time": 2.4333, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 2.7, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 2.8333, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.9}, {"time": 3.3667, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 3.6, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 3.7333, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 3.8667}, {"time": 4.2667, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 4.5333, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 4.6667, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 4.7333}, {"time": 5.1667, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 5.4333, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 5.5667, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 5.6333}, {"time": 6.1, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 6.3667, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 6.5, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 6.5667}, {"time": 6.9667, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 7.2667, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 7.3667, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 7.4667}, {"time": 7.9, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8.1667, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 8.3, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 8.3667}, {"time": 8.8, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 9.1, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 9.2, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 9.2667}, {"time": 9.6667, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 9.9667, "angle": 3.12, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 10.1, "angle": 0.64, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 10.1667}, {"time": 10.6333, "angle": 7.07, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 10.8667, "angle": 3.12}], "scale": [{"x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 0.1, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 0.9, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 1.0333, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 1.8333, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 1.9333, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 2.7, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 2.8333, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 3.6, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 3.7333, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 4.5333, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 4.6667, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 4.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 5.4333, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 5.5667, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 5.6333, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 6.3667, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 6.5, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 7.2667, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 7.3667, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8.1667, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 8.3, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 8.3667, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 9.1, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 9.2, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 9.9667, "x": 0.929, "y": 0.965, "curve": 0.353, "c2": 0.4, "c3": 0.701, "c4": 0.78}, {"time": 10.1, "x": 0.985, "y": 0.993, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "x": 0.84, "y": 0.92, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 10.8667, "x": 0.929, "y": 0.965}]}, "c16": {"rotate": [{"angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 0.1, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2333}, {"time": 0.7, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 0.9, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 1.0333, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.1}, {"time": 1.6, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 1.8333, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 1.9333, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.0333}, {"time": 2.5333, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 2.7, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 2.8333, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.9333}, {"time": 3.4, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 3.6, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 3.7333, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 3.8667}, {"time": 4.3, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 4.5333, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 4.6667, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 4.8}, {"time": 5.2333, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 5.4333, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 5.5667, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 5.6667}, {"time": 6.1333, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 6.3667, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 6.5, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 6.6}, {"time": 7.0333, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 7.2667, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 7.3667, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 7.5}, {"time": 7.9333, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 8.1667, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 8.3, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 8.3667}, {"time": 8.8333, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 9.1, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 9.2, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 9.3}, {"time": 9.8, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 9.9667, "angle": 3.88, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 10.1, "angle": 1.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 10.2}, {"time": 10.6667, "angle": 7.07, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 10.8667, "angle": 3.88}], "scale": [{"x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 0.1, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 0.9, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 1.0333, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 1.8333, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 1.9333, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 2.7, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 2.8333, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 3.6, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 3.7333, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 4.5333, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 4.6667, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 5.4333, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 5.5667, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 6.3667, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 6.5, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 7.2667, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 7.3667, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 8.1667, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 8.3, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 8.3667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 9.1, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 9.2, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 9.3, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 9.9667, "x": 0.912, "y": 0.956, "curve": 0.342, "c2": 0.36, "c3": 0.689, "c4": 0.74}, {"time": 10.1, "x": 0.972, "y": 0.986, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 10.2, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": 0.84, "y": 0.92, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 10.8667, "x": 0.912, "y": 0.956}]}, "st": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 10.8667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 10.8667}]}, "st5": {"rotate": [{"angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 2.63}, {"time": 0.6333, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.0667, "angle": 2.63}, {"time": 1.5333, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.8333, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9667, "angle": 2.63}, {"time": 2.4333, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.7, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9, "angle": 2.63}, {"time": 3.3333, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.6, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.8333, "angle": 2.63}, {"time": 4.2333, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.5333, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.7, "angle": 2.63}, {"time": 5.1667, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.4333, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.6333, "angle": 2.63}, {"time": 6.0333, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.3667, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.5333, "angle": 2.63}, {"time": 6.9667, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.2667, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.4667, "angle": 2.63}, {"time": 7.8667, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1667, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.3333, "angle": 2.63}, {"time": 8.7667, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.1, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.2333, "angle": 2.63}, {"time": 9.6667, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.9667, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.1667, "angle": 2.63}, {"time": 10.6, "angle": -8.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.8667, "angle": -1.56}]}, "st6": {"rotate": [{"angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 0.2, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 1.0667, "angle": 2.15, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.1, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8333, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 1.9667, "angle": 2.15, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 2.9, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 3.8333, "angle": 2.15, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.8667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5333, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 4.7, "angle": 2.15, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.7333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.4333, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 5.6333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.3667, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 6.5333, "angle": 2.15, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 6.5667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.2667, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 7.4667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 8.3333, "angle": 2.15, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.3667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.1, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 9.2333, "angle": 2.15, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 9.2667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.9667, "angle": -3, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 10.1667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "angle": -8.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.8667, "angle": -3}]}, "st7": {"rotate": [{"angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.2, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 1.0667, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.8333, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 1.9667, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.0333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.7, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 2.9, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 3.8333, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.9, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.5333, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 4.7, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.8, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.4333, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 5.6333, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.7, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.3667, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 6.5333, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 7.2667, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 7.4667, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.5333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.1667, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 8.3333, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.4333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.1, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 9.2333, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.3, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.9667, "angle": -4.52, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 10.1667, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.2333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "angle": -8.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10.8667, "angle": -4.52}]}, "st8": {"rotate": [{"angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.2, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.9, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.0667, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.2, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.8333, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.9667, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.7, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 2.9, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 3.8333, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.9333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.5333, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 4.7, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.8667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.4333, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 5.6333, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.7333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.3667, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 6.5333, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.6667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 7.2667, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 7.4667, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.5667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8.1667, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 8.3333, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.4667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 9.1, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 9.2333, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.4, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 9.9667, "angle": -5.98, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 10.1667, "angle": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.2667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "angle": -8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 10.8667, "angle": -5.98}]}, "st9": {"rotate": [{"angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.2, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.9, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 1.0667, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.3, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.8333, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 1.9667, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.2, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.7, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 2.9, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.0667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.6, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 3.8333, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.5333, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 4.7, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.9, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.4333, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 5.6333, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.8333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.3667, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 6.5333, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.7, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.2667, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 7.4667, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.6, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1667, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 8.3333, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.5333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.1, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 9.2333, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.4333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.9667, "angle": -7.28, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 10.1667, "angle": -1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.3, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "angle": -8.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.8667, "angle": -7.28}]}, "st10": {"rotate": [{"angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.2, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.9, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 1.0667, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.8333, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 1.9667, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.7, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 2.9, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.6, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 3.8333, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4.5333, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 4.7, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.4333, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 5.6333, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.8333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.3667, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 6.5333, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.7667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 7.2667, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 7.4667, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 8.1667, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 8.3333, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.5333, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 9.1, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 9.2333, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.4667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 9.9667, "angle": -8.3, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 10.1667, "angle": -3.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.3667, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "angle": -8.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10.8667, "angle": -8.3}]}, "st11": {"rotate": [{"angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2}, {"time": 0.6333, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.0667}, {"time": 1.5333, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.8333, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9667}, {"time": 2.4333, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.7, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9}, {"time": 3.3333, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.6, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.8333}, {"time": 4.2333, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.5333, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.7}, {"time": 5.1667, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.4333, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.6333}, {"time": 6.0333, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.3667, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.5333}, {"time": 6.9667, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.2667, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.4667}, {"time": 7.8667, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1667, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.3333}, {"time": 8.7667, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.1, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.2333}, {"time": 9.6667, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.9667, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.1667}, {"time": 10.6, "angle": 12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.8667, "angle": 4.6}]}, "st12": {"rotate": [{"angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 1.0667, "angle": 0.53, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8333, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 1.9667, "angle": 0.53, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 3.8333, "angle": 0.53, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5333, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 4.7, "angle": 0.53, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.4333, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 5.6333, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.3667, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 6.5333, "angle": 0.53, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.2667, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 8.3333, "angle": 0.53, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.3667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.1, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 9.2333, "angle": 0.53, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.9667, "angle": 6.18, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "angle": 12.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.8667, "angle": 6.18}]}, "st13": {"rotate": [{"angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.2, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 1.0667, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.8333, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 1.9667, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.7, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 2.9, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 3.8333, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.9, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.5333, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 4.7, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.4333, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 5.6333, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.7, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.3667, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 6.5333, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 7.2667, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 7.4667, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.5333, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.1667, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 8.3333, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.4333, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.1, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 9.2333, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.3, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.9667, "angle": 7.84, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 10.1667, "angle": 1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.2333, "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "angle": 12.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10.8667, "angle": 7.84}]}, "st14": {"rotate": [{"angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.2, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.9, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.0667, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.8333, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.9667, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.7, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 2.9, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 3.8333, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.5333, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 4.7, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.4333, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 5.6333, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.7333, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.3667, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 6.5333, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 7.2667, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 7.4667, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8.1667, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 8.3333, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 9.1, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 9.2333, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.4, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 9.9667, "angle": 9.44, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 10.1667, "angle": 3.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "angle": 12.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 10.8667, "angle": 9.44}]}, "st15": {"rotate": [{"angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.2, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.9, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 1.0667, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.8333, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 1.9667, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.7, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 2.9, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.6, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 3.8333, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.5333, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 4.7, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.4333, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 5.6333, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.3667, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 6.5333, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.2667, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 7.4667, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1667, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 8.3333, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.1, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 9.2333, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.9667, "angle": 10.87, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 10.1667, "angle": 4.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.3, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "angle": 12.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.8667, "angle": 10.87}]}, "st16": {"rotate": [{"angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.2, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.9, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 1.0667, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.8333, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 1.9667, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.7, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 2.9, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.6, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 3.8333, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4.5333, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 4.7, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.4333, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 5.6333, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.3667, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 6.5333, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 7.2667, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 7.4667, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6333, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 8.1667, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 8.3333, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 9.1, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 9.2333, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.4667, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 9.9667, "angle": 11.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 10.1667, "angle": 6.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.3667, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "angle": 12.49, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10.8667, "angle": 11.98}]}, "st2": {"rotate": [{"angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 10.8667, "angle": -3.5}]}, "st3": {"rotate": [{"angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.9, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.9667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.8333, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.9, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.7, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.6, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.5333, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.6, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.4333, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.5333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.3667, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.4667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 7.2667, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.3333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.1667, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.1, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.1667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.9667, "angle": -2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.0667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": 3.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10.8667, "angle": -2.6}]}, "st4": {"rotate": [{"angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.0667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.8333, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.7, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.6, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.8333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.5333, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.7, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.4333, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.6333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.3667, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.5333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.2667, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.4667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1667, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.3333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.1, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.2333, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.9667, "angle": -0.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.1667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 3.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.8667, "angle": -0.95}]}}}}}