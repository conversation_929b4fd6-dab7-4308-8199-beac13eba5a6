import { _decorator, EditBox, Label, NodeEventType } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ClubModule } from "../../../module/club/ClubModule";
import { UIClubAvatar } from "./UIClubAvatar";
import { ClubRouteItem } from "../../../module/club/ClubRoute";
import TipMgr from "../../../lib/tips/TipMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { ItemCost } from "../../common/ItemCost";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Wed Aug 07 2024 20:43:00 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubCreate.ts
 *
 */

@ccclass("UIClubCreate")
export class UIClubCreate extends UINode {
  protected _openAct: boolean = true; //打开动作
  private avatar: string = "1_1";
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubCreate`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.getNode("UIClubAvatar").getComponent(UIClubAvatar).setAvatar(this.avatar);
    let cost = ClubModule.config.getClubCreateCost();
    this.getNode("ItemCost").getComponent(ItemCost).setItemId(ItemEnum.仙玉_6, cost[1]);
    this.getNode("club_notice").on(
      EditBox.EventType.TEXT_CHANGED,
      (editBox: EditBox) => {
        this.getNode("lbl_notice_nums").getComponent(
          Label
        ).string = `字数：${editBox.string.length}/${editBox.maxLength}`;
      },
      this
    );
  }
  private on_click_btn_avatar_edit() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let callback = (data: string) => {
      this.getNode("UIClubAvatar").getComponent(UIClubAvatar).setAvatar(data);
      this.avatar = data;
    };
    UIMgr.instance.showDialog(ClubRouteItem.UIClubEditAvatar, { avatar: this.avatar, callback });
  }
  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_btn_create() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let cost = ClubModule.config.getClubCreateCost();
    if (!this.getNode("ItemCost").getComponent(ItemCost).isEnough()) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: cost[0],
        needNum: cost[1],
      });
      return;
    }
    if (
      !this.getNode("club_name").getComponent(EditBox).string ||
      this.getNode("club_name").getComponent(EditBox).string.length < 1
    ) {
      TipMgr.showTip("请输入战盟名称");
      return;
    }
    if (
      !this.getNode("club_notice").getComponent(EditBox).string ||
      this.getNode("club_notice").getComponent(EditBox).string.length < 1
    ) {
      TipMgr.showTip("请输入战盟公告");
      return;
    }
    ClubModule.api.createClub(
      this.getNode("club_name").getComponent(EditBox).string,
      this.getNode("club_notice").getComponent(EditBox).string,
      this.avatar,
      () => {
        ClubModule.api.ownClub(() => {
          UIMgr.instance.back();
          UIMgr.instance.showPage(ClubRouteItem.UIClubMain);
        });
      },
      (errorCode: number, msg: string[], data: any): boolean => {
        TipsMgr.showErrX(errorCode, msg);
        this.getNode("club_name").getComponent(EditBox).string = "";
        this.getNode("club_notice").getComponent(EditBox).string = "";
        return true;
      }
    );
  }
}
