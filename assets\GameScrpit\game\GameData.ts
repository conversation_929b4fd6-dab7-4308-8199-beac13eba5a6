import { IConfigGuidePath } from "./JsonDefine";

/**
 * 所有游戏数据都存在这里
 */
export class GameData {
  public static _instance: GameData;
  public static get instance(): any {
    if (!GameData._instance) {
      GameData._instance = new GameData();
    }
    return GameData._instance;
  }

  // 当前正在引导的步骤，空为不在引导状态
  private _guideStepNow: IConfigGuidePath;
  public static get guideStepNow(): IConfigGuidePath {
    return GameData._instance._guideStepNow;
  }
  public static set guideStepNow(step: IConfigGuidePath) {
    GameData._instance._guideStepNow = step;
  }

  public static clear() {
    GameData._instance = null;
  }
}
