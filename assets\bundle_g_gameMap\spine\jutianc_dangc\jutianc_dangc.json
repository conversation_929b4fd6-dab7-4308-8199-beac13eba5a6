{"skeleton": {"hash": "vhV1+gDZsIomtbr8qgicsiC3brs=", "spine": "3.8.75", "x": -147.75, "y": -39.59, "width": 67, "height": 42}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 178.04, "y": -76.3, "scaleX": 0.9201, "scaleY": 0.9201}, {"name": "bone5", "parent": "root", "length": 2.64, "rotation": 0.43, "x": 21.77, "y": -85.22, "scaleX": 3.684, "scaleY": 3.684}, {"name": "bone2", "parent": "bone5", "length": 4.6, "rotation": 91.06, "x": -0.33, "y": 0.94}, {"name": "bone3", "parent": "bone5", "length": 3.23, "rotation": -107.75, "x": -1.14, "y": -1.26}, {"name": "bone4", "parent": "bone5", "length": 2.86, "rotation": -75.04, "x": 0.38, "y": -1.47}, {"name": "bone6", "parent": "root", "length": 7.11, "rotation": 1.79, "x": 59.05, "y": -83.12, "scaleX": 3.684, "scaleY": 3.684}, {"name": "bone7", "parent": "bone6", "length": 2.75, "rotation": -95.55, "x": -1.99, "y": -1.91}, {"name": "bone8", "parent": "bone6", "length": 2.88, "rotation": -85.01, "x": 2.01, "y": -1.92}, {"name": "bone9", "parent": "bone6", "length": 2.69, "rotation": -154.84, "x": -3.78, "y": 0.9}, {"name": "bone10", "parent": "root", "length": 4.28, "rotation": -1.01, "x": -63.3, "y": -51.98, "scaleX": 3.226, "scaleY": 3.226}, {"name": "bone11", "parent": "bone10", "length": 2.23, "rotation": 88.43, "x": -0.53, "y": 3.22}, {"name": "bone12", "parent": "bone10", "length": 2.7, "rotation": -91.12, "x": -1.76, "y": -2.33}, {"name": "bone13", "parent": "bone10", "length": 2.78, "rotation": -87.45, "x": 0.59, "y": -2.16}, {"name": "bone14", "parent": "root", "rotation": 28.5, "x": 84.6, "y": -52.11, "scaleX": 1.941, "scaleY": 1.941, "transform": "onlyTranslation"}, {"name": "bone15", "parent": "bone14", "length": 3.92, "rotation": 89.12, "x": -0.68, "y": 3.69}, {"name": "bone16", "parent": "root", "x": -24.78, "y": -82.4}], "slots": [{"name": "c1", "bone": "bone"}, {"name": "l1", "bone": "root"}, {"name": "r1", "bone": "root"}, {"name": "xian", "bone": "bone14"}, {"name": "root", "bone": "bone16", "attachment": "root"}, {"name": "r2", "bone": "bone2"}], "path": [{"name": "root1", "bones": ["bone14"], "target": "root", "rotation": -180}], "skins": [{"name": "default", "attachments": {"r2": {"r2": {"type": "mesh", "uvs": [0.30479, 0.22003, 0.32479, 0, 0.44229, 0, 0.63479, 0, 0.62604, 0.19822, 0.89854, 0.22367, 0.88229, 0.40912, 1, 0.46731, 1, 0.8964, 0.64302, 0.86598, 0.64052, 1, 0.56177, 1, 0.50052, 1, 0.47427, 0.72598, 0.46177, 1, 0.38677, 1, 0.31177, 1, 0.30802, 0.91689, 0, 0.9278, 0, 0.46598, 0.07177, 0.4587, 0.10177, 0.22416, 0.33006, 0.77244, 0.33631, 0.51426, 0.63381, 0.73608, 0.63006, 0.51608, 0.47131, 0.59426], "triangles": [21, 23, 20, 22, 18, 20, 6, 7, 8, 8, 24, 6, 18, 19, 20, 6, 4, 5, 24, 13, 26, 24, 25, 6, 24, 26, 25, 8, 9, 24, 13, 11, 12, 9, 13, 24, 9, 11, 13, 10, 11, 9, 22, 23, 26, 22, 26, 13, 20, 23, 22, 17, 18, 22, 15, 16, 17, 15, 22, 13, 15, 17, 22, 14, 15, 13, 4, 2, 3, 2, 0, 1, 4, 0, 2, 0, 26, 23, 25, 4, 6, 4, 26, 0, 25, 26, 4, 0, 23, 21], "vertices": [1, 3, 3.01, 2.57, 1, 2, 3, 5.42, 2.19, 0.99549, 4, -6.76, -3.71, 0.00451, 1, 3, 5.37, 0.31, 1, 2, 3, 5.29, -2.77, 0.99323, 5, -6.98, 3.9, 0.00677, 1, 3, 3.12, -2.57, 1, 1, 2, 6.54, 3.79, 1, 1, 2, 6.27, 1.75, 1, 1, 2, 8.15, 1.09, 1, 1, 2, 8.11, -3.63, 1, 2, 3, -4.23, -2.65, 0.04602, 5, 2.24, 1.5, 0.95398, 2, 3, -5.71, -2.57, 0.0006, 5, 3.65, 1.07, 0.9994, 1, 5, 3.32, -0.15, 1, 1, 5, 3.06, -1.09, 1, 3, 3, -2.63, 0.01, 0.001, 4, 0.15, 0.95, 0.43279, 5, 0.04, -0.7, 0.56622, 1, 4, 3.09, 1.65, 1, 1, 4, 3.44, 0.51, 1, 1, 4, 3.8, -0.64, 1, 2, 3, -4.65, 2.72, 0.00343, 4, 2.95, -0.97, 0.99657, 1, 2, -7.89, -3.85, 1, 1, 2, -7.85, 1.23, 1, 1, 2, -6.7, 1.3, 1, 1, 2, -6.2, 3.88, 1, 2, 3, -3.08, 2.33, 0.02631, 4, 1.32, -1.1, 0.97369, 2, 3, -0.24, 2.15, 0.50652, 4, -1.42, -1.85, 0.49348, 2, 3, -2.8, -2.54, 0.12271, 5, 0.83, 1.74, 0.87729, 2, 3, -0.38, -2.54, 0.50247, 5, -1.52, 2.32, 0.49753, 3, 3, -1.18, 0.02, 0.45479, 4, -1.22, 0.47, 0.27617, 5, -1.37, -0.36, 0.26904], "hull": 22}}, "l1": {"l1": {"type": "mesh", "uvs": [0.00215, 0.71857, 0.17715, 0.71191, 0.28048, 0.58746, 0.32215, 1, 0.45216, 1, 0.57881, 1, 0.60881, 0.81635, 0.66715, 0.81191, 0.69215, 1, 0.82549, 1, 0.97548, 1, 0.95548, 0.80746, 1, 0.72524, 1, 0.40747, 1, 0.13191, 0.73715, 0.0808, 0.46548, 0.08969, 0.40215, 0, 0, 0, 0.36049, 0.38303, 0.61882, 0.3897, 0.45392, 0.38544, 0.81607, 0.3989, 0.44462, 0.21786, 0.29462, 0.20009, 0.09628, 0.38675], "triangles": [7, 20, 22, 22, 14, 13, 6, 21, 20, 20, 16, 15, 20, 23, 16, 20, 21, 23, 20, 15, 22, 22, 15, 14, 19, 23, 21, 23, 24, 16, 22, 13, 12, 21, 2, 19, 0, 25, 1, 0, 18, 25, 1, 25, 2, 2, 25, 19, 25, 24, 19, 25, 18, 24, 19, 24, 23, 24, 17, 16, 24, 18, 17, 21, 4, 2, 5, 4, 6, 21, 6, 4, 3, 2, 4, 9, 11, 10, 22, 11, 9, 9, 8, 7, 6, 20, 7, 9, 7, 22, 11, 22, 12], "vertices": [1, 9, 4.7, 1.14, 1, 1, 9, 2.8, 2.04, 1, 1, 9, 1.18, 1.6, 1, 1, 7, 3.02, -1.51, 1, 2, 8, 2.53, -4.48, 0.00011, 7, 2.92, 0.04, 0.99989, 2, 8, 2.71, -2.97, 0.0211, 7, 2.82, 1.56, 0.9789, 2, 8, 1.11, -2.42, 0.29907, 7, 1.15, 1.81, 0.70093, 2, 8, 1.15, -1.72, 0.70275, 7, 1.06, 2.51, 0.29725, 2, 8, 2.87, -1.62, 0.98215, 7, 2.73, 2.92, 0.01785, 2, 8, 3.06, -0.03, 0.99999, 7, 2.63, 4.51, 1e-05, 1, 8, 3.27, 1.76, 1, 1, 8, 1.52, 1.72, 1, 2, 8, 0.85, 2.34, 0.99992, 9, -5.95, 6.62, 8e-05, 1, 6, 4.51, 0.3, 1, 1, 6, 4.58, 2.78, 1, 1, 6, 1.44, 3.34, 1, 1, 6, -1.82, 3.36, 1, 2, 7, -6.02, -1.15, 0.00876, 9, -2.51, -2.45, 0.99124, 1, 9, 1.79, -4.63, 1, 3, 8, -3.12, -4.92, 0.02291, 7, -2.55, -1.42, 0.072, 9, -0.5, 0.4, 0.90509, 1, 6, -0.06, 0.6, 1, 1, 6, -2.04, 0.7, 1, 1, 6, 2.3, 0.44, 1, 1, 6, -2.1, 2.21, 1, 2, 7, -4.14, -2.32, 0.00058, 9, -0.55, -1.43, 0.99942, 1, 9, 2.34, -1.01, 1], "hull": 19}}, "xian": {"xian": {"type": "mesh", "uvs": [0.10921, 0.50648, 0.10753, 0.28789, 0.19153, 0.17335, 0.26041, 0, 0.45025, 0, 0.43345, 0.15139, 0.56113, 0.18003, 0.52081, 0.44539, 0.58801, 0.52557, 0.58213, 0.64394, 1, 0.65327, 1, 1, 0.5941, 1, 0.2925, 1, 0, 1, 0, 0.66246, 0.05272, 0.61196, 0.2901, 0.54251, 0.4149, 0.51978, 0.2141, 0.52797, 0.2933, 0.38524, 0.2885, 0.73615, 0.2893, 0.61978, 0.3013, 0.85069, 0.5813, 0.83433], "triangles": [5, 3, 4, 2, 3, 5, 20, 2, 5, 1, 2, 20, 7, 5, 6, 20, 5, 7, 0, 1, 20, 18, 20, 7, 19, 0, 20, 17, 19, 20, 18, 17, 20, 22, 19, 17, 8, 18, 7, 18, 22, 17, 16, 0, 19, 9, 18, 8, 22, 16, 19, 22, 18, 21, 24, 9, 10, 9, 24, 21, 21, 18, 9, 24, 23, 21, 15, 23, 14, 21, 16, 22, 21, 15, 16, 21, 23, 15, 13, 14, 23, 11, 12, 24, 23, 24, 12, 13, 23, 12, 11, 24, 10], "vertices": [2, 14, -5.4, 3.58, 0.24288, 15, -0.18, 4.72, 0.75712, 2, 14, -5.44, 8.39, 2e-05, 15, 4.63, 4.84, 0.99998, 1, 15, 7.18, 2.77, 1, 1, 15, 11.02, 1.11, 1, 1, 15, 11.09, -3.63, 1, 1, 15, 7.76, -3.27, 1, 1, 15, 7.17, -6.47, 1, 2, 14, 4.89, 4.93, 0.26164, 15, 1.32, -5.55, 0.73836, 2, 14, 6.57, 3.16, 0.54819, 15, -0.42, -7.26, 0.45181, 2, 14, 6.42, 0.56, 0.8385, 15, -3.02, -7.15, 0.1615, 1, 14, 16.87, 0.36, 1, 1, 14, 16.87, -7.27, 1, 1, 14, 6.72, -7.27, 1, 1, 14, -0.82, -7.27, 1, 2, 14, -8.13, -7.27, 0.99387, 15, -11.08, 7.28, 0.00613, 2, 14, -8.13, 0.15, 0.75874, 15, -3.65, 7.4, 0.24126, 2, 14, -6.81, 1.26, 0.63769, 15, -2.52, 6.1, 0.36231, 2, 14, -0.88, 2.79, 0.18834, 15, -0.9, 0.19, 0.81166, 2, 14, 2.24, 3.29, 0.32733, 15, -0.36, -2.93, 0.67267, 2, 14, -2.78, 3.11, 0.1938, 15, -0.61, 2.09, 0.8062, 1, 15, 2.56, 0.16, 1, 2, 14, -0.92, -1.47, 0.98674, 15, -5.16, 0.16, 0.01326, 2, 14, -0.9, 1.09, 0.67539, 15, -2.6, 0.18, 0.32461, 1, 14, -0.6, -3.99, 1, 2, 14, 6.4, -3.63, 0.99004, 15, -7.21, -7.19, 0.00996], "hull": 17}}, "root": {"root": {"type": "path", "lengths": [185.57, 291.84, 323.14, 351.9, 410.48, 511.57, 866.06, 990.58], "vertexCount": 24, "vertices": [403.12, 190.74, 326.85, 149.32, 319.58, 145.37, 247.57, 102.74, 160.17, 68.06, 138.82, 59.58, 97.09, 55.57, 55.01, 56.74, 44.51, 57.04, 34, 57.66, 23.77, 58.61, 13.9, 59.53, 4.31, 60.76, -4.74, 62.31, -28.49, 66.38, -48.47, 72.66, -59.65, 81.35, -86.22, 101.99, -87.03, 144.88, -56.02, 169.88, -12.43, 205.01, 73.7, 150.06, 292.4, 222.01, 374.86, 249.14]}}, "c1": {"c1": {"x": -0.25, "y": 1.58, "width": 37, "height": 26}}, "r1": {"r1": {"type": "mesh", "uvs": [0.00332, 0.33328, 0.17832, 0.38136, 0, 0.40636, 0, 0.81597, 0.10332, 0.81212, 0.09082, 1, 0.25748, 1, 0.46998, 1, 0.46998, 0.81212, 0.50748, 1, 0.73247, 1, 0.93664, 1, 0.94497, 0.79481, 1, 0.78135, 1, 0.45636, 0.85331, 0.40444, 1, 0.37174, 1, 0, 0, 0, 0.46581, 0.68905, 0.24915, 0.71405, 0.71581, 0.71789, 0.51164, 0.39675, 0.50331, 0.15444, 0.23665, 0.54482, 0.48248, 0.54097, 0.75747, 0.52944], "triangles": [21, 19, 26, 26, 22, 15, 22, 24, 1, 19, 24, 25, 26, 14, 13, 26, 15, 14, 25, 22, 26, 25, 24, 22, 26, 19, 25, 24, 3, 2, 2, 1, 24, 20, 24, 19, 23, 18, 17, 0, 18, 23, 23, 17, 16, 1, 0, 23, 22, 1, 23, 23, 15, 22, 16, 15, 23, 21, 26, 13, 12, 21, 13, 8, 19, 21, 21, 10, 8, 8, 10, 9, 21, 12, 10, 11, 10, 12, 20, 3, 24, 20, 19, 8, 6, 4, 20, 20, 4, 3, 8, 6, 20, 5, 4, 6, 6, 8, 7], "vertices": [1, 11, 0.21, 3.11, 1, 1, 10, -2.58, 2.91, 1, 1, 10, -3.64, 2.56, 1, 1, 12, 0.47, -1.78, 1, 1, 12, 0.39, -1.16, 1, 1, 12, 2.84, -1.15, 1, 1, 12, 2.8, -0.15, 1, 1, 12, 2.75, 1.13, 1, 3, 12, 0.31, 1.04, 0.54634, 13, 0.44, -1.34, 0.45221, 11, -5.88, 0.04, 0.00146, 1, 13, 2.89, -1.18, 1, 1, 13, 2.92, 0.17, 1, 1, 13, 2.96, 1.4, 1, 1, 13, 0.29, 1.52, 1, 2, 13, 0.13, 1.85, 0.992, 11, -5.34, -3.12, 0.008, 1, 10, 2.37, 2.02, 1, 1, 10, 1.48, 2.68, 1, 1, 11, -0.02, -2.88, 1, 1, 11, 4.81, -2.67, 1, 1, 11, 4.54, 3.33, 1, 3, 12, -1.29, 0.95, 0.12216, 13, -1.16, -1.32, 0.09945, 10, -0.78, -1.06, 0.77839, 2, 12, -0.91, -0.33, 0.94613, 13, -0.87, -2.63, 0.05387, 2, 12, -0.97, 2.47, 0.0507, 13, -0.75, 0.17, 0.9493, 1, 11, -0.48, 0.03, 1, 1, 11, 2.67, 0.22, 1, 1, 10, -2.19, 0.79, 1, 1, 10, -0.72, 0.86, 1, 1, 10, 0.93, 1.04, 1], "hull": 19}}}}], "animations": {"animation1": {"slots": {"c1": {"color": [{"color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 6.4667, "color": "ffffffff"}, {"time": 7.5333, "color": "ffffff00"}], "attachment": [{"name": "c1"}]}}, "bones": {"bone": {"rotate": [{"angle": -4.52, "curve": "stepped"}, {"time": 4, "angle": -4.52, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "angle": -2.64}], "translate": [{"x": 27.61, "y": 10.84, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -31.48, "y": -12.64, "curve": "stepped"}, {"time": 4, "x": -31.48, "y": -12.64, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "x": -41.32, "y": -24.56, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "x": -87.58, "y": -56.07}], "scale": [{"x": 0.866, "y": 0.866}]}}}, "animation2": {"slots": {"l1": {"color": [{"color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 9, "color": "ffffffff"}, {"time": 10, "color": "ffffff00"}], "attachment": [{"name": "l1"}]}, "r2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00"}], "attachment": [{"name": "r2"}]}}, "bones": {"bone2": {"rotate": [{"angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4333, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.6667, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.3333, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.4333, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.6667, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.3333, "angle": -2.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "angle": -10.54, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10, "angle": -2.55}]}, "bone4": {"rotate": [{"angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.4333, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.7667}, {"time": 1.1, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.4333}, {"time": 1.7667, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1}, {"time": 2.4333, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.7667}, {"time": 3.1, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4333}, {"time": 3.7667, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.1}, {"time": 4.4333, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.6667, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.7667}, {"time": 5.1, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.4333}, {"time": 5.7667, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.1}, {"time": 6.4333, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.7667}, {"time": 7.1, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.3333, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.4333}, {"time": 7.7667, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.1}, {"time": 8.4333, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.6667, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.7667}, {"time": 9.1, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.3333, "angle": -4.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.4333}, {"time": 9.7667, "angle": -17.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10, "angle": -4.13}]}, "bone3": {"rotate": [{"angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.4333, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.7667}, {"time": 1.1, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.4333}, {"time": 1.7667, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1}, {"time": 2.4333, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.7667}, {"time": 3.1, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4333}, {"time": 3.7667, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.1}, {"time": 4.4333, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.6667, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.7667}, {"time": 5.1, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.4333}, {"time": 5.7667, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.1}, {"time": 6.4333, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.7667}, {"time": 7.1, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.3333, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.4333}, {"time": 7.7667, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.1}, {"time": 8.4333, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.6667, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.7667}, {"time": 9.1, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.3333, "angle": 5.15, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.4333}, {"time": 9.7667, "angle": 21.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10, "angle": 5.15}]}, "bone6": {"translate": [{"x": 56.73, "y": 4.75}, {"time": 6.0333, "x": -51.88, "y": 8.33}, {"time": 8.4, "x": -94.48, "y": 17.8}, {"time": 10, "x": -123.28, "y": 26.45}]}, "bone8": {"rotate": [{"angle": 0.69}, {"time": 0.1667, "angle": 27.28}, {"time": 0.3333, "angle": 0.69}, {"time": 0.5, "angle": 27.28}, {"time": 0.6667, "angle": 0.69}, {"time": 0.8333, "angle": 27.28}, {"time": 1, "angle": 0.69}, {"time": 1.1667, "angle": 27.28}, {"time": 1.3333, "angle": 0.69}, {"time": 1.5, "angle": 27.28}, {"time": 1.6667, "angle": 0.69}, {"time": 1.8333, "angle": 27.28}, {"time": 2, "angle": 0.69}, {"time": 2.1667, "angle": 27.28}, {"time": 2.3333, "angle": 0.69}, {"time": 2.5, "angle": 27.28}, {"time": 2.6667, "angle": 0.69}, {"time": 2.8333, "angle": 27.28}, {"time": 3, "angle": 0.69}, {"time": 3.1667, "angle": 27.28}, {"time": 3.3333, "angle": 0.69}, {"time": 3.5, "angle": 27.28}, {"time": 3.6667, "angle": 0.69}, {"time": 3.8333, "angle": 27.28}, {"time": 4, "angle": 0.69}, {"time": 4.1667, "angle": 27.28}, {"time": 4.3333, "angle": 0.69}, {"time": 4.5, "angle": 27.28}, {"time": 4.6667, "angle": 0.69}, {"time": 4.8333, "angle": 27.28}, {"time": 5, "angle": 0.69}, {"time": 5.1667, "angle": 27.28}, {"time": 5.3333, "angle": 0.69}, {"time": 5.5, "angle": 27.28}, {"time": 5.6667, "angle": 0.69}, {"time": 5.8333, "angle": 27.28}, {"time": 6, "angle": 0.69}, {"time": 6.1667, "angle": 27.28}, {"time": 6.3333, "angle": 0.69}, {"time": 6.5, "angle": 27.28}, {"time": 6.6667, "angle": 0.69}, {"time": 6.8333, "angle": 27.28}, {"time": 7, "angle": 0.69}, {"time": 7.1667, "angle": 27.28}, {"time": 7.3333, "angle": 0.69}, {"time": 7.5, "angle": 27.28}, {"time": 7.6667, "angle": 0.69}, {"time": 7.8333, "angle": 27.28}, {"time": 8, "angle": 0.69}, {"time": 8.1667, "angle": 27.28}, {"time": 8.3333, "angle": 0.69}, {"time": 8.5, "angle": 27.28}, {"time": 8.6667, "angle": 0.69}, {"time": 8.8333, "angle": 27.28}, {"time": 9, "angle": 0.69}, {"time": 9.1667, "angle": 27.28}, {"time": 9.3333, "angle": 0.69}, {"time": 9.5, "angle": 27.28}, {"time": 9.6667, "angle": 0.69}, {"time": 9.8333, "angle": 27.28}, {"time": 10}]}, "bone7": {"rotate": [{"angle": -1.03}, {"time": 0.1667, "angle": -36.05}, {"time": 0.3333, "angle": -1.03}, {"time": 0.5, "angle": -36.05}, {"time": 0.6667, "angle": -1.03}, {"time": 0.8333, "angle": -36.05}, {"time": 1, "angle": -1.03}, {"time": 1.1667, "angle": -36.05}, {"time": 1.3333, "angle": -1.03}, {"time": 1.5, "angle": -36.05}, {"time": 1.6667, "angle": -1.03}, {"time": 1.8333, "angle": -36.05}, {"time": 2, "angle": -1.03}, {"time": 2.1667, "angle": -36.05}, {"time": 2.3333, "angle": -1.03}, {"time": 2.5, "angle": -36.05}, {"time": 2.6667, "angle": -1.03}, {"time": 2.8333, "angle": -36.05}, {"time": 3, "angle": -1.03}, {"time": 3.1667, "angle": -36.05}, {"time": 3.3333, "angle": -1.03}, {"time": 3.5, "angle": -36.05}, {"time": 3.6667, "angle": -1.03}, {"time": 3.8333, "angle": -36.05}, {"time": 4, "angle": -1.03}, {"time": 4.1667, "angle": -36.05}, {"time": 4.3333, "angle": -1.03}, {"time": 4.5, "angle": -36.05}, {"time": 4.6667, "angle": -1.03}, {"time": 4.8333, "angle": -36.05}, {"time": 5, "angle": -1.03}, {"time": 5.1667, "angle": -36.05}, {"time": 5.3333, "angle": -1.03}, {"time": 5.5, "angle": -36.05}, {"time": 5.6667, "angle": -1.03}, {"time": 5.8333, "angle": -36.05}, {"time": 6, "angle": -1.03}, {"time": 6.1667, "angle": -36.05}, {"time": 6.3333, "angle": -1.03}, {"time": 6.5, "angle": -36.05}, {"time": 6.6667, "angle": -1.03}, {"time": 6.8333, "angle": -36.05}, {"time": 7, "angle": -1.03}, {"time": 7.1667, "angle": -36.05}, {"time": 7.3333, "angle": -1.03}, {"time": 7.5, "angle": -36.05}, {"time": 7.6667, "angle": -1.03}, {"time": 7.8333, "angle": -36.05}, {"time": 8, "angle": -1.03}, {"time": 8.1667, "angle": -36.05}, {"time": 8.3333, "angle": -1.03}, {"time": 8.5, "angle": -36.05}, {"time": 8.6667, "angle": -1.03}, {"time": 8.8333, "angle": -36.05}, {"time": 9, "angle": -1.03}, {"time": 9.1667, "angle": -36.05}, {"time": 9.3333, "angle": -1.03}, {"time": 9.5, "angle": -36.05}, {"time": 9.6667, "angle": -1.03}, {"time": 9.8333, "angle": -36.05}, {"time": 10, "angle": -1.03}]}, "bone9": {"rotate": [{"angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.6667, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.8667, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 7.3333, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.5333, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.6667, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.8667, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.3333, "angle": 10.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 16.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10, "angle": 10.14}]}, "bone5": {"translate": [{"x": 77.52, "y": 5.64}, {"time": 5.9667, "x": -27.43, "y": 9.62}, {"time": 8.1667, "x": -67.3, "y": 17.27}, {"time": 10, "x": -98.37, "y": 28.65}]}}}, "animation3": {"slots": {"r1": {"color": [{"color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 13.9667, "color": "ffffffff"}, {"time": 14.7333, "color": "ffffff00"}], "attachment": [{"name": "r1"}]}}, "bones": {"bone12": {"rotate": [{"angle": 20.63}, {"time": 0.3333, "angle": -14.1}, {"time": 0.6667, "angle": 20.63}, {"time": 1, "angle": -14.1}, {"time": 1.3333, "angle": 20.63}, {"time": 1.6667, "angle": -14.1}, {"time": 2, "angle": 20.63}, {"time": 2.3333, "angle": -14.1}, {"time": 2.6667, "curve": "stepped"}, {"time": 4.7333}, {"time": 5.0667, "angle": -14.1}, {"time": 5.4}, {"time": 5.7333, "angle": -14.1}, {"time": 6.0667}, {"time": 6.4, "angle": -14.1}, {"time": 6.7333, "curve": "stepped"}, {"time": 7.4}, {"time": 7.7333, "angle": -14.1}, {"time": 8.0667}, {"time": 8.4, "angle": -14.1}, {"time": 8.7333}, {"time": 9.0667, "angle": -14.1}, {"time": 9.4}, {"time": 9.7333, "angle": -14.1}, {"time": 10.0667, "curve": "stepped"}, {"time": 11.2333}, {"time": 11.5667, "angle": -14.1}, {"time": 11.9}, {"time": 12.2333, "angle": -14.1}, {"time": 12.5667}, {"time": 12.9, "angle": -14.1}, {"time": 13.2333}, {"time": 13.5667, "angle": -14.1}, {"time": 13.9}, {"time": 14.2333, "angle": -14.1}, {"time": 14.5667}]}, "bone13": {"rotate": [{"angle": -22.3}, {"time": 0.3333, "angle": 18.09}, {"time": 0.6667, "angle": -22.3}, {"time": 1, "angle": 18.09}, {"time": 1.3333, "angle": -22.3}, {"time": 1.6667, "angle": 18.09}, {"time": 2, "angle": -22.3}, {"time": 2.3333, "angle": 18.09}, {"time": 2.6667, "angle": -22.3, "curve": "stepped"}, {"time": 4.7333, "angle": -22.3}, {"time": 5.0667, "angle": 18.09}, {"time": 5.4, "angle": -22.3}, {"time": 5.7333, "angle": 18.09}, {"time": 6.0667, "angle": -22.3}, {"time": 6.4, "angle": 18.09}, {"time": 6.7333, "angle": -22.3, "curve": "stepped"}, {"time": 7.4, "angle": -22.3}, {"time": 7.7333, "angle": 18.09}, {"time": 8.0667, "angle": -22.3}, {"time": 8.4, "angle": 18.09}, {"time": 8.7333, "angle": -22.3}, {"time": 9.0667, "angle": 18.09}, {"time": 9.4, "angle": -22.3}, {"time": 9.7333, "angle": 18.09}, {"time": 10.0667, "angle": -22.3, "curve": "stepped"}, {"time": 11.2333, "angle": -22.3}, {"time": 11.5667, "angle": 18.09}, {"time": 11.9, "angle": -22.3}, {"time": 12.2333, "angle": 18.09}, {"time": 12.5667, "angle": -22.3}, {"time": 12.9, "angle": 18.09}, {"time": 13.2333, "angle": -22.3}, {"time": 13.5667, "angle": 18.09}, {"time": 13.9, "angle": -22.3}, {"time": 14.2333, "angle": 18.09}, {"time": 14.5667, "angle": -22.3}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 4.7333}, {"time": 5.0667, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 5.4}, {"time": 5.7333, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 6.0667}, {"time": 6.4, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "curve": "stepped"}, {"time": 7.4}, {"time": 7.7333, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 8.0667}, {"time": 8.4, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 8.7333}, {"time": 9.0667, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 9.4}, {"time": 9.7333, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 10.0667, "curve": "stepped"}, {"time": 11.2333}, {"time": 11.5667, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 11.9}, {"time": 12.2333, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 12.5667}, {"time": 12.9, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 13.2333}, {"time": 13.5667, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 13.9}, {"time": 14.2333, "angle": 14.74, "curve": 0.25, "c3": 0.75}, {"time": 14.5667}]}, "bone10": {"translate": [{"x": -16.46, "y": 8.98}, {"time": 2.3333, "x": 7.85, "curve": "stepped"}, {"time": 4.7333, "x": 7.85}, {"time": 6.2333, "x": 7.85, "y": -10.66, "curve": "stepped"}, {"time": 7.4, "x": 7.85, "y": -10.66}, {"time": 10, "x": 37.03, "y": -25.43, "curve": "stepped"}, {"time": 11.2667, "x": 37.03, "y": -25.43}, {"time": 14.7333, "x": 89.8, "y": -29.22}]}}}, "animation4": {"slots": {"xian": {"color": [{"color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.1667, "color": "ffffffff"}, {"time": 4.7, "color": "ffffff00"}], "attachment": [{"name": "xian"}]}}, "bones": {"bone14": {"rotate": [{"angle": -137.78, "curve": "stepped"}, {"time": 3.8333, "angle": -137.78}, {"time": 3.8667, "angle": -15.24}], "scale": [{"time": 3.8333, "curve": "stepped"}, {"time": 3.8667, "x": -1}]}, "bone15": {"translate": [{"x": -0.1, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -0.1, "y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -0.1, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -0.1, "y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -0.1, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "x": -0.1, "y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -0.1, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": -0.1, "y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -0.1, "y": -0.86}]}}, "path": {"root1": {"position": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8667, "position": 0.3149, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "position": 0.4092, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "position": 1}], "mix": [{"time": 3.8, "curve": "stepped"}, {"time": 3.8667, "rotateMix": 0, "translateMix": 0.972}]}}}, "animation5": {"slots": {"xian": {"color": [{"color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 3.2, "color": "ffffff00"}], "attachment": [{"name": "xian"}]}}, "bones": {"bone14": {"rotate": [{"angle": -137.78}, {"time": 2.1333, "angle": -91.9}], "scale": [{"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "x": 0.613, "y": 0.613}]}, "bone15": {"translate": [{"x": -0.1, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -0.1, "y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -0.1, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -0.1, "y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -0.1, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "x": -0.1, "y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -0.1, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": -0.1, "y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -0.1, "y": -0.86}]}, "bone16": {"translate": [{"time": 1.9333}, {"time": 3.3333, "y": 34.51}]}}, "path": {"root1": {"position": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9, "position": 0.3149, "curve": 0.268, "c3": 0.618, "c4": 0.42}, {"time": 1.9333, "position": 0.3488}]}}, "deform": {"default": {"root": {"root": [{"offset": 6, "vertices": [-10.26339, 19.09086, 0, 0, 5.356, -2.67799, 21.11406, -21.72594, -24.82391, -17.81194, -24.8105, -17.49959, -64.87687, -19.30004, -56.60489, -22.02603, -54.07998, -29.59886]}]}}}}}}