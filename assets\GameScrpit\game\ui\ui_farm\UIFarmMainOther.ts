import { _decorator, Label } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UINode } from "../../../lib/ui/UINode";
import { FarmSimpleMessage } from "../../net/protocol/Farm";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { FarmRouteName } from "../../../module/farm/FarmRoute";
import MsgMgr from "../../../lib/event/MsgMgr";
import { FarmEvent } from "../../../module/farm/FarmEvent";
import { FarmSlotUITool } from "../../../module/farm/FarmSlotUITool";
import { Input } from "cc";
import { FarmModule } from "../../../module/farm/FarmModule";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIFarmMainOther")
export class UIFarmMainOther extends UINode {
  protected _openAct: boolean = true;
  private tickId: number = 0;
  private refreshCount: number = 0;
  private loadingTs: number = 0;
  private lastAddAttentionTs: number = 0;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FARM}?prefab/ui/UIFarmMainOther`;
  }

  private _farm: FarmSimpleMessage;

  public init(args: any): void {
    super.init(args);

    this._farm = args.farm;
  }

  protected onRegEvent() {
    MsgMgr.on(FarmEvent.FARM_REFRESH, this.refreshFarm, this);
    MsgMgr.on(FarmEvent.OTHER_FARM_REFRESH, this.refreshFarm, this);

    for (let i = 1; i <= 5; i++) {
      this.getNode("p" + i).on(Input.EventType.TOUCH_END, this.onClickHulu, this);
    }
  }

  protected onDelEvent() {
    MsgMgr.off(FarmEvent.FARM_REFRESH, this.refreshFarm, this);
    MsgMgr.off(FarmEvent.OTHER_FARM_REFRESH, this.refreshFarm, this);

    TickerMgr.clearTicker(this.tickId);
  }

  protected onEvtShow(): void {
    this.getNode("lbl_name").getComponent(Label).string = `${this._farm.simpleMessage.nickname}的福地`;

    this.refreshFarm();

    this.tickId = TickerMgr.setInterval(1, this.checkFarmInfo.bind(this), false);
  }

  private refreshFarm() {
    if (this.loadingTs > new Date().getTime()) {
      return;
    } else {
      this.loadingTs = new Date().getTime() + 7000;
    }

    // 更新猴子状态
    let beeMetric = FarmModule.data.farmTrainMessage.beeMetric;
    this.getNode("lbl_xian").getComponent(Label).string = `闲 [${beeMetric.relaxBeeCnt}]`;
    this.getNode("lbl_zong").getComponent(Label).string = `总 [${beeMetric.totalBeeCnt}]`;

    FarmModule.api.getOtherFarm(this._farm.simpleMessage.userId, (data) => {
      this.loadingTs = 0;
      // 刷新界面

      this._farm = data;
      const slotList = this._farm.slotList;

      for (let i = 0; i < 5; i++) {
        const pNode = this.getNode("p" + (i + 1));
        if (i >= slotList.length) {
          pNode.active = false;
          continue;
        }
        FarmSlotUITool.updateSlot(slotList[i], pNode, this.refreshCount);
      }

      this.refreshCount++;
    });
  }

  private on_click_btn_find() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.replaceDialog(FarmRouteName.UIFarmFind);
  }

  private onClickHulu(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let rank = Number(event.target.name.replace("p", "")) - 1;

    const slotMsg = this._farm.slotList[rank];
    if (slotMsg.gourdId == -1) {
      return;
    }

    UIMgr.instance.showDialog(FarmRouteName.UIFarmCollect, {
      rank: rank,
      slotMsg: this._farm.slotList[rank],
    });
  }

  private on_click_btn_log() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(FarmRouteName.UIFarmLog);
  }

  private checkFarmInfo() {
    const slotList = this._farm.slotList;

    for (let i = 0; i < 5; i++) {
      const pNode = this.getNode("p" + (i + 1));
      if (i >= slotList.length) {
        pNode.active = false;
        continue;
      }

      let takeTs = slotList[i].ownCollectorMessage?.endTime || -1;
      if (takeTs < 0) {
        takeTs = slotList[i].otherCollectorMessage?.endTime || -1;
      }

      if (takeTs > 0) {
        if (takeTs < TimeUtils.serverTime) {
          this.refreshFarm();
          return;
        }
      }
    }

    if (this.lastAddAttentionTs < new Date().getTime()) {
      log.log("addAttention:" + this._farm.simpleMessage.userId);
      this.lastAddAttentionTs = new Date().getTime() + 3000;
      FarmModule.api.addAttention(this._farm.simpleMessage.userId);
    }
  }

  private on_click_btn_manage() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(FarmRouteName.UIFarmBeeManage);
  }
}
