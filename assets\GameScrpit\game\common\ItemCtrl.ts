import { _decorator, assetManager, Color, Component, Label, Node, math, Sprite, UITransform, Widget } from "cc";

import ResMgr from "../../lib/common/ResMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import Formate from "../../lib/utils/Formate";
import { PlayerModule } from "../../module/player/PlayerModule";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { JsonMgr } from "../mgr/JsonMgr";
import { FmColor } from "./FmConstant";
import { TipsMgr } from "../../../platform/src/TipsHelper";
import { FriendModule } from "../../module/friend/FriendModule";
import { RewardRouteEnum } from "../../ext_reward/RewardDefine";
import { NodeTool } from "../../lib/utils/NodeTool";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property, executeInEditMode, type } = _decorator;

const qualityList = ["", "S0325", "S0328", "S0326", "S0324", "S0327"];

@ccclass("ItemCtrl")
@executeInEditMode
export class ItemCtrl extends Component {
  @property(Sprite)
  private bgColor: Sprite;

  @property(Sprite)
  private bgIcon: Sprite;

  @property(Label)
  private lblHas: Label;
  @property(Label)
  private lblName: Label;

  @property(Node)
  private nodeMask: Node;

  // @property({ type: [Color], displayName: "道具颜色" })
  // public itemColor: Color[] = [FmColor.COLOR_BLUE_LIGHT];
  @property({ type: Color, tooltip: "品质对应字体颜色" }) public itemColors: Color[] = [
    math.color("#92d564"),
    math.color("#70bff7"),
    math.color("#d0b0f7"),
    math.color("#f4b500"),
    math.color("#ffa9a9"),
  ];
  //"#115b21", "#165fa8", "#7529bd", "#965509", "#c53737"
  @property({ type: Color, tooltip: "品质对应字体描边颜色" }) public itemOutlineColors: Color[] = [
    math.color("#115b21"),
    math.color("#165fa8"),
    math.color("#7529bd"),
    math.color("#965509"),
    math.color("#c53737"),
  ];

  // 道具ID
  private _itemId: number;

  // 就否停止弹出
  private stopPop: boolean;

  private _deps = [];

  public get itemName(): Label {
    return this.lblName;
  }
  public get itemNum(): Label {
    return this.lblHas;
  }
  public get itemColor(): Sprite {
    return this.bgColor;
  }
  public get itemIcon(): Sprite {
    return this.bgIcon;
  }

  public get itemId() {
    return this._itemId;
  }

  public addDeps(uuid: string) {
    this._deps.push(uuid);
  }

  protected onDestroy(): void {
    for (let i = 0; i < this._deps.length; i++) {
      let uuid = this._deps[i];
      let res = assetManager.assets.get(uuid);
      res.decRef();
      res = null;
    }
    // this.onClickBlack();
  }
  protected onLoad(): void {
    let heightScale =
      this.node.getComponent(UITransform).height /
      this.node.getChildByName("node_effect")?.getComponent(UITransform).height;
    let widthScale =
      this.node.getComponent(UITransform).width /
      this.node.getChildByName("node_effect")?.getComponent(UITransform).width;
    this.node.getChildByName("node_effect")?.setScale(widthScale, heightScale);

    // this.nodeMask = this.node.getChildByName("Mask");
    // this.nodeMask.active = false;
  }

  protected start(): void {
    if (this.node.parent) {
      this.node.walk((child) => (child.layer = this.node.parent.layer));
    }
  }

  // 点击显示详情
  private onShowDetail() {
    // 禁用启用点击事件
    if (this.stopPop) {
      return;
    }
    AudioMgr.instance.playEffect(519);

    let worldPosition = this.node.getWorldPosition();
    let left = this.getComponent(UITransform).width * this.node.scale.x * this.getComponent(UITransform).anchorX;
    let top = this.getComponent(UITransform).height * this.node.scale.y * (1 - this.getComponent(UITransform).anchorY);
    // this.setTips(worldPosition.x - left, worldPosition.y + top);
    TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopItemDetail, {
      itemId: this._itemId,
      toWorldX: worldPosition.x - left,
      toWorldY: worldPosition.y + top,
    });
  }

  public showDetail() {
    this.stopPop = false;
    this.onShowDetail();
    this.stopPop = true;
  }

  private _sevnId: number;
  public setItemId(id: number, num: number = -1, stopPop: boolean = false) {
    this._itemId = id;

    if (this._itemId > 1000000) {
      this._sevnId = this._itemId;
      this._itemId = this._sevnId % 1000000;
    }

    let info = JsonMgr.instance.getConfigItem(this._itemId);
    this.stopPop = stopPop;

    if (!info) {
      this.node.active = false;
      log.error("未找到该道具信息", this._itemId);
      return;
    }
    if (this.node.getChildByPath("node_effect")) {
      if (info.color > 3 && info.closeSpecial != 1) {
        this.node.getChildByPath("node_effect").active = true;
      } else {
        this.node.getChildByPath("node_effect").active = false;
      }
    }

    if (info.goodsType == 6 && FriendModule.data.getFriendMessage(info.id) && this._sevnId) {
      this.bgColor.node.active = false;
      this.bgIcon.node.parent = this.nodeMask;
      this.bgIcon.getComponent(Widget).left = 0;
      this.bgIcon.getComponent(Widget).top = 0;
      this.bgIcon.getComponent(Widget).right = 0;
      this.bgIcon.getComponent(Widget).bottom = 0;
      this.nodeMask.active = true;

      this.node.getChildByName("lay_friend").active = true;
      this.node.getChildByName("lbl_num").active = false;

      this.node.getChildByName("lay_friend").getChildByName("lbl_friend").getComponent(Label).string = `${num}`;

      this.node.getChildByName("lay_friend").getChildByName("spr_friend").active = true;

      let friendItemId = Math.floor(this._sevnId / 1000000);

      let friendInfo = JsonMgr.instance.getConfigItem(friendItemId);

      // 设置图标
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_COMMON_ITEM,
        `autoItem/${friendInfo.iconId}`,
        this.node.getChildByName("lay_friend").getChildByName("spr_friend").getComponent(Sprite),
        (sf, err) => {
          if (err) {
            ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/item_0`, this.bgIcon);
          }
        }
      );

      this.stopPop = true;
    } else {
      this.bgColor.node.active = true;

      this.node.getChildByName("lay_friend").active = false;
      this.node.getChildByName("lbl_num").active = true;
    }
    // 设置等级
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/${this.getItemQuality(info.color)}`, this.bgColor);

    // 设置图标
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/${info.iconId}`, this.bgIcon, (asset, err) => {
      if (err) {
        ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/item_0`, this.bgIcon);
      }
    });

    // 设置数量
    if (num < 0) {
      //this.node.getChildByName("bg_num").active = false;
      this.node.getChildByName("lbl_num").active = false;
    } else {
      this.lblHas.string = `${Formate.format(num)}`;
    }
    if (this.lblName) {
      this.lblName.string = info.name;
      this.lblName.color = math.color(this.itemColors[info.color - 1]);
      this.lblName.outlineColor = math.color(this.itemOutlineColors[info.color - 1]);
    }
  }
  //设置需要多少个道具
  public setItemNeed(id: number, need: number, stopPop: boolean = false) {
    this._itemId = id;
    let info = JsonMgr.instance.getConfigItem(id);
    this.stopPop = stopPop;
    // 设置等级
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/${this.getItemQuality(info.color)}`, this.bgColor);

    // 设置图标
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/${info.iconId}`, this.bgIcon);
    if (this.node.getChildByPath("node_effect")) {
      if (info.goodsType != 6 && info.color > 3 && info.closeSpecial != 1) {
        this.node.getChildByPath("node_effect").active = true;
      } else {
        this.node.getChildByPath("node_effect").active = false;
      }
    }

    let itemNum = PlayerModule.data.getItemNum(id);
    this.lblHas.string = `${Formate.format(itemNum)}/${Formate.format(need)}`;
    if (need <= itemNum) {
      this.lblHas.color = FmColor.COLOR_GREEN_DARK;
    } else {
      this.lblHas.color = FmColor.COLOR_RED_DARK;
    }
  }

  public flyEffect() {
    let wordPos = this.node.getWorldPosition();

    let nodeParent = NodeTool.findByName(UIMgr.instance.uiRoot, "UIMain");
    let btn_knapsack = NodeTool.findByName(nodeParent, "btn_knapsack");
    let endWord = btn_knapsack.getWorldPosition();

    TipsMgr.topRouteCtrl.show(
      RewardRouteEnum.TopItemAwardFly,
      { startWordPos: wordPos, endWordPos: endWord },
      () => {}
    );
  }

  /**获取道具品质框 */
  public getItemQuality(quality: number) {
    let key = qualityList[quality];
    return key;
  }
}
