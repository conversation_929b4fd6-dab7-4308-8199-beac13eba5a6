{"skeleton": {"hash": "mYyZ/Svqg8F6cR5+V8wb/zba28U=", "spine": "3.8.75", "x": -110.81, "y": -22, "width": 139.31, "height": 82, "images": "D:/spine导出/进度条驾鹤西去动画", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 18.44, "rotation": -2.37, "x": 4.25, "y": -22.46}, {"name": "bone2", "parent": "bone", "length": 9.62, "rotation": 12.15, "x": 0.18, "y": 13.97}, {"name": "bone3", "parent": "bone2", "length": 8.94, "rotation": 67.54, "x": 11.67, "y": 0.31}, {"name": "bone4", "parent": "bone3", "length": 9.68, "rotation": 43.87, "x": 8.94}, {"name": "bone5", "parent": "bone4", "length": 9.41, "rotation": -59.53, "x": 9.68}, {"name": "bone6", "parent": "bone5", "length": 7.56, "rotation": -88.22, "x": 9.41}, {"name": "bone7", "parent": "bone", "length": 5.52, "rotation": 173.28, "x": -26.09, "y": 18.77}, {"name": "bone8", "parent": "bone7", "length": 10.13, "rotation": 1.05, "x": 5.52}, {"name": "bone9", "parent": "bone", "length": 8.94, "rotation": -176.93, "x": -15.78, "y": 6.98}, {"name": "bone10", "parent": "bone9", "length": 9.05, "rotation": -0.01, "x": 8.94}, {"name": "bone11", "parent": "bone", "length": 11.69, "rotation": 96.11, "x": -12.19, "y": 41.39}, {"name": "bone12", "parent": "bone6", "length": 7.74, "rotation": 146.31, "x": -2.73, "y": 4.48}, {"name": "bone13", "parent": "bone2", "length": 17.85, "rotation": 130.41, "x": -13.17, "y": -0.05}, {"name": "bone14", "parent": "bone13", "length": 29.13, "rotation": 3.14, "x": 17.85}, {"name": "bone15", "parent": "bone", "length": 18.8, "rotation": 143.04, "x": -2.1, "y": 15.92}, {"name": "bone16", "parent": "bone15", "length": 29.91, "rotation": 0.91, "x": 18.8}, {"name": "bone23", "parent": "bone", "rotation": -9.95, "x": -72.93, "y": -2.76}, {"name": "bone17", "parent": "bone23", "rotation": -9.95, "x": 42.54, "y": 26.96}, {"name": "bone18", "parent": "bone23", "rotation": -9.95, "x": 27.3, "y": 26.65}, {"name": "bone19", "parent": "bone23", "rotation": -9.95, "x": 11.21, "y": 26.18}, {"name": "bone20", "parent": "bone23", "rotation": -9.95, "x": -3.91, "y": 25.55}, {"name": "bone21", "parent": "bone23", "rotation": -9.95, "x": -19.82, "y": 25.09}, {"name": "bone22", "parent": "bone23", "rotation": -9.95, "x": -35.13, "y": 24.45}], "slots": [{"name": "jdtfx3", "bone": "root", "attachment": "jdtfx2"}, {"name": "jdtfx1", "bone": "root", "attachment": "jdtfx1"}, {"name": "jdtfx2", "bone": "root", "attachment": "jdtfx2"}, {"name": "xy", "bone": "root", "attachment": "xy"}], "skins": [{"name": "default", "attachments": {"xy": {"xy": {"type": "mesh", "uvs": [1, 1, 0.83333, 1, 0.66667, 1, 0.5, 1, 0.33333, 1, 0.16667, 1, 0, 1, 0, 0, 0.16667, 0, 0.33333, 0, 0.5, 0, 0.66667, 0, 0.83333, 0, 1, 0], "triangles": [6, 7, 8, 5, 6, 8, 5, 8, 9, 4, 5, 9, 4, 9, 10, 3, 4, 10, 3, 10, 11, 2, 3, 11, 2, 11, 12, 1, 2, 12, 1, 12, 13, 0, 1, 13], "vertices": [2, 18, 1.38, -11.44, 0.85923, 19, 16.62, -11.14, 0.14077, 3, 18, -13.27, -12.05, 0.20315, 19, 1.96, -11.75, 0.70002, 20, 18.06, -11.27, 0.09682, 3, 19, -12.69, -12.35, 0.24938, 20, 3.41, -11.88, 0.68838, 21, 18.53, -11.26, 0.06224, 4, 19, -27.34, -12.96, 2e-05, 20, -11.25, -12.49, 0.28726, 21, 3.87, -11.86, 0.66738, 22, 19.77, -11.4, 0.04534, 4, 20, -25.9, -13.1, 0.0008, 21, -10.78, -12.47, 0.32853, 22, 5.12, -12, 0.64427, 23, 20.43, -11.37, 0.0264, 3, 21, -25.44, -13.08, 0.00067, 22, -9.53, -12.61, 0.40384, 23, 5.78, -11.98, 0.59549, 2, 22, -24.19, -13.22, 0.00191, 23, -8.88, -12.58, 0.99809, 1, 23, -9.79, 9.4, 1, 3, 21, -26.35, 8.91, 3e-05, 22, -10.45, 9.37, 0.39522, 23, 4.87, 10, 0.60474, 3, 21, -11.69, 9.51, 0.31162, 22, 4.21, 9.98, 0.67836, 23, 19.52, 10.61, 0.01001, 3, 20, -12.16, 9.49, 0.26062, 21, 2.96, 10.12, 0.69842, 22, 18.86, 10.58, 0.04096, 3, 19, -13.6, 9.63, 0.23711, 20, 2.5, 10.1, 0.71978, 21, 17.61, 10.73, 0.04311, 3, 18, -14.18, 9.93, 0.16033, 19, 1.05, 10.23, 0.77986, 20, 17.15, 10.71, 0.05981, 2, 18, 0.47, 10.54, 0.82901, 19, 15.71, 10.84, 0.17099], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 88, "height": 22}}, "jdtfx1": {"jdtfx1": {"type": "mesh", "uvs": [0.74321, 0.67873, 0.71769, 0.62868, 0.71173, 0.55221, 0.76023, 0.48896, 0.7832, 0.47158, 0.71344, 0.33395, 0.77531, 0.31423, 0.8487, 0.29085, 0.95079, 0.46671, 1, 0.52302, 1, 0.56542, 1, 0.66621, 0.91676, 0.59462, 0.85806, 0.57446, 0.82999, 0.5974, 0.8453, 0.62868, 0.90145, 0.65996, 0.95505, 0.7246, 0.9663, 0.84081, 0.86415, 0.89694, 0.69331, 1, 0.51185, 0.96929, 0.44146, 0.95738, 0.3217, 0.9732, 0.22308, 1, 0.15439, 1, 0.13137, 0.93897, 0.11388, 0.89262, 0.19314, 0.85089, 0.30938, 0.87391, 0.25528, 0.84468, 0.14084, 0.81278, 0.00353, 0.75815, 0.05845, 0.71425, 0.13248, 0.69961, 0.21487, 0.69083, 0.13487, 0.64498, 0.00114, 0.58254, 0.02024, 0.53474, 0.13248, 0.49376, 0.17786, 0.43522, 0.22681, 0.43815, 0.17836, 0.36706, 0.16771, 0.27273, 0.02917, 0.16969, 0.10554, 0.0681, 0.2281, 0.01876, 0.36486, 0, 0.4803, 0.03182, 0.48208, 0.1218, 0.64193, 0.16824, 0.71298, 0.25386, 0.70925, 0.33046, 0.71134, 0.34339, 0.72716, 0.38022, 0.70955, 0.40656, 0.66447, 0.44778, 0.61032, 0.50072, 0.56644, 0.5212, 0.56793, 0.52852, 0.60196, 0.53072, 0.66315, 0.54242, 0.68703, 0.5573, 0.69971, 0.61038, 0.7109, 0.62408, 0.72374, 0.65067, 0.72463, 0.68335, 0.73478, 0.68238, 0.37608, 0.49126, 0.43978, 0.51018, 0.51796, 0.51965, 0.29935, 0.48298, 0.51651, 0.38834, 0.78754, 0.62684, 0.8262, 0.67106, 0.84939, 0.74415, 0.78644, 0.79559, 0.41202, 0.90208, 0.48271, 0.94179, 0.31704, 0.92284, 0.2088, 0.93457, 0.59316, 0.82086, 0.2342, 0.77123, 0.12707, 0.75769, 0.77295, 0.56745, 0.82262, 0.51165, 0.90877, 0.50785, 0.96232, 0.58267, 0.86763, 0.46663, 0.81641, 0.38038], "triangles": [5, 6, 89, 89, 6, 7, 4, 5, 89, 7, 8, 89, 19, 76, 18, 76, 81, 66, 65, 66, 64, 59, 60, 63, 59, 70, 58, 63, 81, 59, 59, 81, 70, 81, 69, 70, 69, 82, 68, 37, 38, 36, 35, 36, 71, 82, 83, 35, 31, 82, 30, 30, 82, 29, 69, 81, 82, 79, 29, 77, 82, 77, 29, 63, 66, 81, 21, 78, 81, 20, 76, 19, 21, 81, 20, 22, 78, 21, 78, 77, 81, 22, 77, 78, 81, 77, 82, 20, 81, 76, 68, 82, 35, 38, 39, 36, 71, 36, 39, 41, 39, 40, 71, 39, 41, 71, 68, 35, 61, 63, 60, 62, 63, 61, 64, 66, 63, 76, 66, 67, 43, 45, 46, 44, 45, 43, 51, 72, 50, 42, 72, 41, 72, 49, 50, 52, 72, 51, 72, 52, 53, 55, 72, 53, 55, 53, 54, 43, 46, 47, 43, 72, 42, 47, 48, 43, 49, 43, 48, 49, 72, 43, 56, 72, 55, 57, 72, 56, 72, 58, 70, 57, 58, 72, 69, 68, 72, 68, 71, 72, 71, 41, 72, 72, 70, 69, 83, 33, 34, 32, 33, 83, 83, 34, 35, 31, 83, 82, 32, 83, 31, 80, 28, 29, 27, 28, 80, 26, 27, 80, 25, 26, 80, 24, 80, 23, 25, 80, 24, 80, 29, 79, 23, 79, 77, 23, 77, 22, 80, 79, 23, 88, 89, 8, 4, 89, 88, 86, 88, 8, 85, 88, 86, 86, 8, 9, 87, 86, 9, 86, 12, 13, 9, 10, 87, 87, 12, 86, 87, 10, 11, 12, 87, 11, 85, 4, 88, 3, 4, 85, 84, 3, 85, 2, 3, 84, 13, 85, 86, 84, 85, 13, 14, 84, 13, 2, 84, 1, 73, 84, 14, 73, 1, 84, 73, 14, 15, 74, 73, 15, 74, 15, 16, 73, 0, 1, 0, 73, 74, 75, 74, 16, 75, 0, 74, 67, 0, 76, 75, 16, 17, 75, 76, 0, 75, 17, 18, 76, 75, 18], "vertices": [5, 2, 8.37, 11.6, 0.21935, 3, 9.17, 7.35, 0.11597, 4, 5.27, 5.14, 0.65707, 5, -6.67, -1.2, 0.00209, 7, -30.83, -11.99, 0.00553, 6, 2, 7.39, 15.93, 0.02299, 3, 12.8, 9.92, 0.0068, 4, 9.66, 4.48, 0.75339, 5, -3.87, 2.25, 0.20916, 7, -28.49, -15.77, 0.0003, 11, -11.96, -15.05, 0.00736, 2, 4, 15.23, 1.57, 0.06156, 5, 1.46, 5.58, 0.93844, 2, 5, 7.57, 5.18, 0.98077, 6, -5.24, -1.69, 0.01923, 2, 5, 9.55, 4.51, 0.79399, 6, -4.5, 0.28, 0.20601, 1, 12, 11.26, 2.94, 1, 1, 12, 10.6, -1.46, 1, 1, 12, 9.83, -6.69, 1, 2, 5, 15.23, -5.19, 0.01591, 6, 5.37, 5.66, 0.98409, 1, 6, 10.38, 3, 1, 1, 6, 11.93, -0.11, 1, 3, 4, -2.77, -10.11, 0, 5, 2.4, -15.86, 0.00013, 6, 15.63, -7.5, 0.99987, 3, 4, 5.14, -8.38, 0.00228, 5, 4.92, -8.16, 0.07476, 6, 8.02, -4.74, 0.92296, 3, 4, 8.6, -5.87, 0.07463, 5, 4.51, -3.91, 0.51399, 6, 3.76, -5.02, 0.41138, 3, 4, 7.96, -3.29, 0.54499, 5, 1.96, -3.15, 0.39616, 6, 2.92, -7.55, 0.05885, 3, 4, 5.23, -2.84, 0.95055, 5, 0.19, -5.27, 0.04531, 6, 4.98, -9.38, 0.00414, 2, 3, 13, -2.65, 0.27896, 4, 1.09, -4.73, 0.72104, 2, 3, 8.62, -7.32, 0.91683, 4, -5.3, -5.05, 0.08317, 2, 2, 20.85, -4.04, 0.00789, 3, -0.51, -10.14, 0.99211, 1, 1, 14.77, 9.53, 1, 1, 1, 3.68, 0.61, 1, 1, 1, -8.57, 2.62, 1, 2, 2, -15.43, -7.48, 0.10149, 9, -2.26, 3.7, 0.89851, 2, 9, 5.78, 4.9, 0.83332, 10, -3.16, 4.9, 0.16668, 2, 9, 12.41, 7.02, 0.07693, 10, 3.47, 7.02, 0.92307, 1, 10, 8.07, 6.97, 1, 1, 10, 9.56, 1.94, 1, 1, 10, 10.68, -1.87, 1, 2, 9, 14.27, -5.23, 0.00408, 10, 5.33, -5.23, 0.99592, 1, 1, -22.45, 9.88, 1, 1, 1, -26.17, 12.12, 1, 3, 9, 17.73, -8.4, 0.00673, 8, 1.86, 5.21, 0.66949, 7, 7.29, 5.24, 0.32377, 1, 8, 11.6, 2.06, 1, 1, 8, 8.46, -2.02, 1, 3, 8, 3.72, -3.9, 0.92568, 7, 9.31, -3.83, 0.06819, 11, -15.2, 24.45, 0.00613, 1, 1, -29.4, 24.62, 1, 1, 1, -34.91, 28.15, 1, 1, 1, -44.07, 32.89, 1, 1, 1, -42.96, 36.86, 1, 1, 1, -35.58, 40.53, 1, 1, 1, -32.74, 45.45, 1, 2, 11, 5.78, 16.75, 0.536, 1, -29.46, 45.35, 0.464, 1, 11, 11.81, 19.61, 1, 1, 11, 19.57, 19.81, 1, 2, 7, 23.01, -45.64, 0.00051, 11, 28.61, 28.52, 0.99949, 1, 11, 36.59, 22.87, 1, 1, 11, 40.09, 14.42, 1, 1, 11, 41.03, 5.17, 1, 1, 11, 37.92, -2.38, 1, 1, 11, 30.55, -2.01, 1, 1, 11, 26.05, -12.45, 1, 1, 11, 18.73, -16.74, 1, 2, 4, 30.87, -7.7, 0, 11, 12.48, -16.08, 1, 2, 4, 29.9, -7.27, 0, 11, 11.41, -16.15, 1, 2, 4, 26.76, -6.61, 0, 11, 8.33, -17.01, 1, 1, 11, 6.25, -15.7, 1, 3, 2, 6.39, 31.16, 0.00052, 4, 24.2, -0.15, 0.00069, 11, 3.08, -12.46, 0.99879, 3, 2, 2.08, 27.49, 0.01895, 4, 22.36, 5.2, 0.01114, 11, -1.02, -8.56, 0.9699, 5, 2, -1.1, 26.34, 0.08038, 4, 22.45, 8.58, 0.04181, 9, -11.07, -31.96, 0.00078, 7, -17.09, -22.87, 0.01173, 11, -2.5, -5.51, 0.86529, 1, 1, -6.31, 38.89, 1, 1, 1, -4.03, 38.81, 1, 1, 1, 0.11, 38.02, 1, 1, 1, 1.76, 36.86, 1, 1, 1, 2.79, 32.55, 1, 1, 1, 3.58, 31.46, 1, 1, 1, 4.53, 29.32, 1, 2, 11, -16.46, -15.22, 0.008, 1, 4.7, 26.64, 0.992, 5, 2, 7.77, 11.4, 0.31031, 3, 8.76, 7.84, 0.11998, 4, 5.3, 5.78, 0.56049, 9, -22.19, -18.6, 0, 7, -30.32, -11.6, 0.00922, 2, 11, 0.78, 7.05, 0.416, 1, -19.28, 41.41, 0.584, 2, 11, -1.05, 2.89, 0.352, 1, -14.95, 40.04, 0.648, 2, 11, -2.16, -2.28, 0.416, 1, -9.69, 39.48, 0.584, 2, 11, 1.8, 12.14, 0.392, 1, -24.45, 41.88, 0.608, 1, 11, 8.59, -2.89, 1, 5, 2, 12.02, 15.29, 0.00325, 3, 13.97, 5.39, 0.00151, 4, 7.37, 0.4, 0.9942, 7, -33.09, -16.66, 5e-05, 11, -12.11, -19.73, 0.00099, 4, 2, 13.96, 11.27, 0.00053, 4, 2.92, 0.06, 0.99937, 7, -36.22, -13.49, 0, 11, -15.9, -22.08, 9e-05, 1, 3, 5.5, -0.76, 1, 1, 1, 9.22, 17.62, 1, 1, 1, -15.48, 7.85, 1, 1, 1, -10.62, 4.8, 1, 1, 9, 6.04, 0.77, 1, 1, 10, 4.36, 1.64, 1, 1, 1, -3.63, 15.01, 1, 1, 1, -27.83, 18.08, 1, 2, 8, 3.41, 0.87, 0.99917, 7, 8.91, 0.93, 0.00083, 1, 5, 2.31, 1.38, 1, 1, 5, 7.91, 0.62, 1, 1, 6, 4.36, 1.38, 1, 2, 5, 7.23, -10.38, 0.01078, 6, 10.31, -2.5, 0.98922, 2, 5, 12.59, -0.28, 0.21321, 6, 0.38, 3.17, 0.78679, 3, 5, 17.19, 6.1, 0.17999, 6, -5.85, 7.96, 0.25201, 12, 4.53, -1.16, 0.568], "hull": 68, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 0, 134, 136, 138, 138, 140, 136, 142, 142, 82, 146, 148, 148, 150, 150, 152, 58, 154, 154, 156, 40, 42, 42, 44, 156, 42, 154, 158, 158, 160, 50, 52, 52, 54, 52, 160, 152, 162, 164, 166, 166, 64, 146, 168, 168, 170, 170, 172, 172, 174, 176, 178, 10, 12, 12, 14, 178, 12], "width": 67, "height": 82}}, "jdtfx2": {"jdtfx2": {"type": "mesh", "uvs": [0.63619, 1, 0.83504, 1, 0.94963, 0.94496, 1, 0.93669, 1, 0.75193, 0.89233, 0.63611, 0.81313, 0.60854, 0.70528, 0.3576, 0.52665, 0.1756, 0.3396, 0.04324, 0.13146, 0.00128, 0, 0, 0, 0.27373, 0.05335, 0.49573, 0.23218, 0.73791, 0.46857, 1, 0.10474, 0.16273, 0.26918, 0.36455, 0.44596, 0.54282, 0.65151, 0.80855], "triangles": [1, 5, 2, 3, 2, 4, 2, 5, 4, 0, 19, 1, 14, 18, 15, 14, 17, 18, 14, 13, 17, 18, 8, 7, 18, 17, 8, 13, 16, 17, 13, 12, 16, 17, 16, 9, 17, 9, 8, 9, 16, 10, 12, 11, 16, 16, 11, 10, 19, 6, 1, 1, 6, 5, 0, 15, 19, 15, 18, 19, 19, 7, 6, 19, 18, 7], "vertices": [1, 1, -17.9, 8.59, 1, 1, 1, -7.17, 9.03, 1, 1, 1, -1.07, 11.1, 1, 1, 1, 1.64, 11.49, 1, 1, 1, 1.39, 17.58, 1, 1, 13, -0.35, -12.87, 1, 2, 13, 3.52, -10.83, 0.99654, 14, -14.91, -10.03, 0.00346, 2, 13, 13.29, -13.46, 0.78923, 14, -5.29, -13.19, 0.21077, 2, 13, 24.55, -11.9, 0.15388, 14, 6.03, -12.25, 0.84612, 2, 13, 35.1, -8.79, 0, 14, 16.74, -9.72, 1, 1, 14, 26.59, -4.12, 1, 1, 14, 32.3, 0.09, 1, 1, 14, 26.91, 7.33, 1, 1, 14, 20.22, 11.49, 1, 2, 13, 24.89, 12.53, 0.09058, 14, 7.71, 12.13, 0.90942, 2, 13, 9.54, 11.01, 0.91759, 14, -7.7, 11.45, 0.08241, 1, 14, 24.56, 1.02, 1, 1, 14, 13.46, 1.06, 1, 1, 14, 2.29, 0.07, 1, 1, 13, 6, -0.17, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 22, 32, 32, 34, 34, 36, 36, 38, 38, 2], "width": 54, "height": 33}}, "jdtfx3": {"jdtfx2": {"type": "mesh", "uvs": [0.63619, 1, 0.83504, 1, 0.94963, 0.94496, 1, 0.93669, 1, 0.75193, 0.89233, 0.63611, 0.81313, 0.60854, 0.70528, 0.3576, 0.52665, 0.1756, 0.3396, 0.04324, 0.13146, 0.00128, 0, 0, 0, 0.27373, 0.05335, 0.49573, 0.23218, 0.73791, 0.46857, 1, 0.10474, 0.16273, 0.26918, 0.36455, 0.44596, 0.54282, 0.65151, 0.80855], "triangles": [0, 19, 1, 19, 6, 1, 1, 5, 2, 1, 6, 5, 0, 15, 19, 14, 18, 15, 15, 18, 19, 3, 2, 4, 2, 5, 4, 19, 7, 6, 19, 18, 7, 14, 17, 18, 14, 13, 17, 18, 8, 7, 18, 17, 8, 13, 16, 17, 13, 12, 16, 17, 16, 9, 17, 9, 8, 9, 16, 10, 12, 11, 16, 16, 11, 10], "vertices": [1, 15, 3.85, 5.77, 1, 1, 15, -4.45, -1.03, 1, 1, 15, -8.09, -6.36, 1, 1, 15, -10.02, -8.29, 1, 1, 15, -6.16, -13.01, 1, 1, 15, 0.76, -12.28, 1, 2, 15, 4.65, -10.27, 0.99991, 16, -14.32, -10.05, 9e-05, 2, 15, 14.4, -12.99, 0.84406, 16, -4.61, -12.92, 0.15594, 2, 15, 25.67, -11.52, 0.17516, 16, 6.68, -11.63, 0.82484, 2, 15, 36.25, -8.5, 7e-05, 16, 17.31, -8.77, 0.99993, 1, 16, 26.98, -2.87, 1, 1, 16, 32.56, 1.5, 1, 1, 16, 26.95, 8.58, 1, 2, 15, 38.74, 12.85, 3e-05, 16, 20.14, 12.53, 0.99997, 2, 15, 26.21, 12.91, 0.13613, 16, 7.61, 12.79, 0.86387, 2, 15, 10.85, 11.51, 0.93376, 16, -7.77, 11.64, 0.06624, 1, 16, 24.8, 2.2, 1, 1, 16, 13.7, 1.9, 1, 2, 15, 21.36, 0.62, 0.02496, 16, 2.57, 0.58, 0.97504, 1, 15, 7.22, 0.36, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 22, 32, 32, 34, 34, 36, 36, 38, 38, 2], "width": 54, "height": 33}}}}], "animations": {"animation": {"slots": {"xy": {"color": [{"color": "ffffff55"}]}}, "bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -2.02, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": -2.02, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": -2.02, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "bone4": {"rotate": [{"angle": -1.99, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.85, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -1.99, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -9.85, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -1.99, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -9.85, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3, "angle": -1.99}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 47.01, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 47.01, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 47.01, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.88, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 6.88, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 6.88, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 29.16, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 29.16, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 29.16, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "bone14": {"rotate": [{"angle": 13.29, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 29.16, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "angle": 13.29, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 29.16, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 13.29, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 29.16, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3, "angle": 13.29}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 29.16, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 29.16, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 29.16, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "bone16": {"rotate": [{"angle": 13.29, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 29.16, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "angle": 13.29, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 29.16, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 13.29, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 29.16, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3, "angle": 13.29}]}, "bone18": {"translate": [{"x": 2.72, "y": 2.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 9.57, "y": 8.77, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3, "x": 2.72, "y": 2.49}]}, "bone19": {"translate": [{"x": 15.01, "y": 6.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 20.95, "y": 9.24, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3, "x": 15.01, "y": 6.62}]}, "bone20": {"translate": [{"x": 31.3, "y": 9.95, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 31.3, "y": 9.95}]}, "bone21": {"translate": [{"x": 26.7, "y": 7.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "x": 37.28, "y": 10.2, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3, "x": 26.7, "y": 7.31}]}, "bone22": {"translate": [{"x": 13.02, "y": 3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "x": 45.88, "y": 10.56, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3, "x": 13.02, "y": 3}]}}}}}