import { UIAddVitalityPop } from "./prefab/ui/UIAddVitalityPop";
import { UIPupilAddResPop } from "./prefab/ui/UIPupilAddResPop";
import { UIPupilDetailPop } from "./prefab/ui/UIPupilDetailPop";
import { UIPupilWorkSlotPop } from "./prefab/ui/UIPupilWorkSlotPop";
import { UIPupilMarryResPop } from "./prefab/ui/UIPupilMarryResPop";
import { UIPupilNewAdultList } from "./prefab/ui/UIPupilNewAdultList";
import { UIPupilPage } from "./prefab/ui/UIPupilPage";
import { UIPupilRatePreview } from "./prefab/ui/UIPupilRatePreview";
import { UIPupilWorkSlot } from "./prefab/ui/UIPupilWorkSlot";
import { Recording, RecordingMap } from "../../../lib/ui/recordingMap";
import { UIPupilRankList } from "./prefab/ui/UIPupilRankList";
import { UIPupilMarryPage } from "./prefab/ui/UIPupilMarryPage";

export enum PupilRouteName {
  UIPupilPage = "UIPupilPage",
  UIPupilAddResPop = "UIPupilAddResPop",
  UIAddVitalityPop = "UIAddVitalityPop",
  UIPupilDetailPop = "UIPupilDetailPop",
  UIPupilRatePreview = "UIPupilRatePreview",
  UIPupilWorkSlot = "UIPupilWorkSlot",
  UIPupilWorkSlotPop = "UIPupilWorkSlotPop",
  UIPupilMarryPage = "UIPupilMarryPage",
  UIPupilMarryResPop = "UIPupilMarryResPop",
  UIPupilNewAdultList = "UIPupilNewAdultList",
  UIPupilRankList = "UIPupilRankList",
}

export class PupilRoute {
  rotueTables: Recording[] = [
    {
      node: UIPupilPage,
      uiName: PupilRouteName.UIPupilPage,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPupilAddResPop,
      uiName: PupilRouteName.UIPupilAddResPop,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIAddVitalityPop,
      uiName: PupilRouteName.UIAddVitalityPop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPupilDetailPop,
      uiName: PupilRouteName.UIPupilDetailPop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPupilRatePreview,
      uiName: PupilRouteName.UIPupilRatePreview,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIPupilMarryPage,
      uiName: PupilRouteName.UIPupilMarryPage,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPupilMarryResPop,
      uiName: PupilRouteName.UIPupilMarryResPop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPupilWorkSlot,
      uiName: PupilRouteName.UIPupilWorkSlot,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPupilWorkSlotPop,
      uiName: PupilRouteName.UIPupilWorkSlotPop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPupilNewAdultList,
      uiName: PupilRouteName.UIPupilNewAdultList,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPupilRankList,
      uiName: PupilRouteName.UIPupilRankList,
      keep: false,
      relevanceUIList: [],
    },
  ];
  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
