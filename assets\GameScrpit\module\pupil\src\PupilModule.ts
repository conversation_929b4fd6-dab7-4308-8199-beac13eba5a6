import { GameData } from "../../../game/GameData";
import { PupilSlotMessage } from "../../../game/net/protocol/Pupil";
import data from "../../../lib/data/data";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { PlayerModule } from "../../player/PlayerModule";
import { PupilApi } from "./PupilApi";
import { PupilData } from "./PupilData";
import { PupilRoute } from "./PupilRoute";
import { PupilService } from "./PupilService";
import { PupilSubScriber } from "./PupilSubScriber";

export class PupilModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): PupilModule {
    if (!GameData.instance.PupilModule) {
      GameData.instance.PupilModule = new PupilModule();
    }
    return GameData.instance.PupilModule;
  }
  private _data = new PupilData();
  private _api = new PupilApi();
  private _service: PupilService = new PupilService();
  private _route = new PupilRoute();
  private _subScriber = new PupilSubScriber();
  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }

  public init(data?: any, completedCallback?: Function) {
    this._data = new PupilData();
    this._api = new PupilApi();
    this._service = new PupilService();
    this._route = new PupilRoute();
    this._subScriber = new PupilSubScriber();

    // 徒弟模块数据
    PupilModule.api.getTrain(() => {
      // 所有徒弟信息缓存列表
      PupilModule.api.getAllPupil(() => {
        this._route.init();
        this._subScriber.register();
        PupilModule.service.init();

        completedCallback && completedCallback();

        // 准备好后，定时刷新是否要更新状态
        TickerMgr.setInterval(
          1,
          () => {
            this._service.schedule();
          },
          false
        );
      });
    });
  }

  /**更新弟子信息 - 联姻申请等 */
  private upPupilTrainMessage() {
    // 体力恢复
    for (let i = 0; i < PupilModule.data.pupilTrainMsg.trainSlotList.length; i++) {
      let leaderConfig = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level);
      let pupilSlotMessage: PupilSlotMessage = PupilModule.data.pupilTrainMsg.trainSlotList[i];
      let config = PupilModule.data.getConfigPupil(1);
      let newTime = pupilSlotMessage.lastUpdateTime + config.time * 1000;
      let chaTime = (newTime - TimeUtils.serverTime) / 1000;
      if (pupilSlotMessage.vitality < leaderConfig.trainMax && chaTime <= 0) {
        PupilModule.api.getTrain((data) => {});
        return;
      }
    }

    // 联姻申请
    let clubMarryApplyList = Object.keys(PupilModule.data.pupilTrainMsg.clubMarryApplyMap).map(Number);
    for (let i = 0; i < clubMarryApplyList.length; i++) {
      let key = clubMarryApplyList[i];
      if (TimeUtils.serverTime >= PupilModule.data.pupilTrainMsg.clubMarryApplyMap[key]) {
        PupilModule.api.getTrain((data) => {});
        return;
      }
    }

    let localMarryApplyList = Object.keys(PupilModule.data.pupilTrainMsg.localMarryApplyMap).map(Number);
    for (let i = 0; i < localMarryApplyList.length; i++) {
      let key = localMarryApplyList[i];
      if (TimeUtils.serverTime >= PupilModule.data.pupilTrainMsg.localMarryApplyMap[key]) {
        PupilModule.api.getTrain((data) => {});
        return;
      }
    }

    let crossMarryApplyList = Object.keys(PupilModule.data.pupilTrainMsg.crossMarryApplyMap).map(Number);
    for (let i = 0; i < crossMarryApplyList.length; i++) {
      let key = crossMarryApplyList[i];
      if (TimeUtils.serverTime >= PupilModule.data.pupilTrainMsg.crossMarryApplyMap[key]) {
        PupilModule.api.getTrain((data) => {});
        return;
      }
    }
  }
}
