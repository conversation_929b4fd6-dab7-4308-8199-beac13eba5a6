import { JsonMgr } from "../../game/mgr/JsonMgr";
import { BaseActivityConfigVO } from "../activity/ActivityConfig";
/**
 * 本地配置文件数据结构
 * 数据基础处理，过滤等，按id取
 */
export class TimeTaskConfig {}

export class TimeTaskSubVO {
  /**
   * 任务ID
   */
  id: number;

  /**
   * 基础任务ID(基础任务表中的任务类型ID)
   */
  taskTypeId: number;

  /**
   * 完成的阶段要求集合
   */
  completeList: Array<number>;

  /**
   * 奖励集合
   */
  rewardList: number[][];
}
export class TimeTaskVO extends BaseActivityConfigVO {
  subList: Array<TimeTaskSubVO>;

  issue: number;
}
