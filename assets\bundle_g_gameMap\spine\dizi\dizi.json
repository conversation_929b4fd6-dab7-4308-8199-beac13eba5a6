{"skeleton": {"hash": "+ViLxwUJL0+hPs3XFLEty8VBhlw", "spine": "3.8.75", "x": -50.8, "y": -4.6, "width": 95.4, "height": 112.81, "images": "./images/", "audio": "D:/建筑升级/弟子"}, "bones": [{"name": "root", "scaleX": 0.2, "scaleY": 0.2}, {"name": "st1", "parent": "root", "x": -1.02, "y": 228.38}, {"name": "st2", "parent": "st1", "length": 60.07, "rotation": 95.75, "x": 2.33, "y": 8.99}, {"name": "st3", "parent": "st1", "length": 78.77, "rotation": -87.5, "x": 10.07, "y": -22.4}, {"name": "s2", "parent": "st2", "length": 106.39, "rotation": -43.36, "x": 26.64, "y": -27.32, "scaleX": 0.52}, {"name": "s1", "parent": "st2", "length": 88.58, "rotation": 47.93, "x": 32.63, "y": 40.8}, {"name": "t1", "parent": "st2", "length": 142.46, "rotation": 7.87, "x": 66.32, "y": 6.29}, {"name": "d1", "parent": "root", "x": 1.78, "y": 7.34}, {"name": "j2", "parent": "d1", "x": -129.01, "y": 126.05}, {"name": "j3", "parent": "d1", "x": -68.48, "y": 126.82}, {"name": "j1", "parent": "d1", "x": 89.82, "y": 134.58}, {"name": "s3", "parent": "s2", "length": 103.25, "rotation": 0.88, "x": 112.54, "y": -1.05}, {"name": "t2", "parent": "st2", "rotation": 7.87, "x": 110.03, "y": 48.81, "color": "abe323ff"}, {"name": "yanj", "parent": "t2", "x": -2.47, "y": -0.57}, {"name": "f2", "parent": "t1", "length": 34.92, "rotation": 30.88, "x": 162.61, "y": 42}, {"name": "f1", "parent": "t1", "length": 43.23, "rotation": -97.91, "x": 198.67, "y": -33.27}, {"name": "f3", "parent": "f1", "length": 59.49, "rotation": -50.06, "x": 51.94, "y": -4.23}, {"name": "f4", "parent": "f3", "length": 43.82, "rotation": -6.41, "x": 72.67, "y": -0.49}, {"name": "f5", "parent": "f4", "length": 53.64, "rotation": 47.18, "x": 57.61, "y": 4.79}, {"name": "f6", "parent": "f2", "length": 22.72, "rotation": -43.64, "x": 35.92, "y": -3.37}, {"name": "f7", "parent": "f6", "length": 14.4, "rotation": -27.42, "x": 29.79, "y": -2.82}], "slots": [{"name": "d1", "bone": "d1", "attachment": "d1"}, {"name": "j1", "bone": "j1", "attachment": "j1"}, {"name": "j2", "bone": "j2", "attachment": "j2"}, {"name": "j3", "bone": "j3", "attachment": "j3"}, {"name": "st1", "bone": "st1", "attachment": "st1"}, {"name": "s1", "bone": "s1", "attachment": "s1"}, {"name": "f1", "bone": "f1", "attachment": "f1"}, {"name": "t1", "bone": "t1", "attachment": "t1"}, {"name": "s2", "bone": "s2", "attachment": "s2"}, {"name": "guan", "bone": "t2", "attachment": "guan"}, {"name": "yanj", "bone": "yanj", "attachment": "yanj"}, {"name": "f2", "bone": "f2", "attachment": "f2"}], "transform": [{"name": "t2", "bones": ["t1"], "target": "t2", "x": -49.11, "y": -36.14, "rotateMix": -0.233, "translateMix": -0.233, "scaleMix": -0.233, "shearMix": -0.233}], "skins": [{"name": "default", "attachments": {"yanj": {"yanj": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-39.89, -65.2, -8.48, 64.49, 47.5, 50.93, 16.09, -78.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 139, "height": 60}}, "j1": {"j1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [120.4, -13.92, -51.6, -13.92, -51.6, 185.08, 120.4, 185.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 172, "height": 199}}, "j2": {"j2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40.23, -7.39, -76.77, -7.39, -76.77, 147.61, 40.23, 147.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 155}}, "j3": {"j3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.7, -9.16, -181.3, -9.16, -181.3, 305.84, 15.7, 305.84], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 197, "height": 315}}, "f1": {"f1": {"type": "mesh", "uvs": [0.29823, 0, 0.43295, 0.13553, 0.60284, 0.30643, 0.77419, 0.47881, 0.93103, 0.63659, 1, 0.8509, 1, 1, 0.89852, 1, 0.72117, 1, 0.61319, 0.99975, 0.51017, 0.99951, 0.42165, 0.76907, 0.34481, 0.56901, 0.28855, 0.42255, 0.15923, 0.34317, 0, 0.35152, 0, 0.26967, 0.09448, 0.01513, 0.19751, 0.00695, 0.28497, 0, 0.44671, 0.41885, 0.60768, 0.60742, 0.82901, 0.84312], "triangles": [7, 5, 6, 8, 22, 7, 7, 22, 5, 22, 8, 21, 22, 4, 5, 22, 3, 4, 21, 8, 9, 9, 10, 21, 10, 11, 21, 21, 3, 22, 11, 20, 21, 21, 2, 3, 11, 12, 20, 20, 2, 21, 12, 13, 20, 13, 1, 20, 13, 0, 1, 20, 1, 2, 13, 14, 19, 14, 18, 19, 13, 19, 0, 15, 16, 14, 16, 17, 14, 14, 17, 18], "vertices": [2, 15, 53.98, 32.18, 0.73545, 16, -26.61, 24.94, 0.26455, 3, 15, 84.15, 8.87, 0.03665, 16, 10.63, 33.11, 0.96312, 17, -65.4, 26.46, 0.00023, 3, 16, 57.59, 43.4, 0.63238, 17, -19.88, 41.93, 0.34555, 18, -25.43, 82.08, 0.02207, 3, 16, 104.95, 53.78, 0.03452, 17, 26.03, 57.53, 0.54435, 18, 17.22, 59.01, 0.42113, 2, 17, 68.05, 71.81, 0.09134, 18, 56.25, 37.9, 0.90866, 2, 17, 103.25, 64.42, 0.00101, 18, 74.77, 7.06, 0.99899, 1, 18, 76.15, -15.11, 1, 1, 18, 51.84, -16.63, 1, 2, 17, 78.12, -1.46, 0.00285, 18, 9.36, -19.29, 0.99715, 2, 17, 61.69, -21.5, 0.565, 18, -16.5, -20.87, 0.435, 3, 16, 113.87, -46, 1e-05, 17, 46.02, -40.63, 0.94635, 18, -41.18, -22.37, 0.05364, 2, 16, 74.68, -36.29, 0.22709, 17, 5.99, -35.36, 0.77291, 3, 15, 56.67, -53.29, 0.00109, 16, 40.65, -27.87, 0.93395, 17, -28.76, -30.78, 0.06496, 2, 15, 45.41, -30.24, 0.19234, 16, 15.74, -21.7, 0.80766, 2, 15, 15.7, -15.38, 0.98046, 16, -14.72, -34.94, 0.01954, 1, 15, -22.45, -12.81, 1, 1, 15, -21.23, -0.68, 1, 1, 15, 5.1, 34.8, 1, 2, 15, 29.83, 33.55, 0.98287, 16, -43.17, 7.31, 0.01713, 2, 15, 50.82, 32.5, 0.77509, 16, -28.88, 22.72, 0.22491, 2, 16, 42.5, 5.23, 0.99777, 17, -30.61, 2.31, 0.00223, 3, 16, 89.77, 12.14, 0.0259, 17, 15.59, 14.46, 0.92542, 18, -21.47, 37.4, 0.04867, 2, 17, 76.39, 33.38, 0.01405, 18, 33.74, 5.66, 0.98595], "hull": 20, "edges": [0, 38, 8, 10, 10, 12, 26, 28, 28, 30, 30, 32, 32, 34, 0, 2, 2, 4, 4, 6, 6, 8, 24, 26, 20, 22, 22, 24, 16, 18, 18, 20, 12, 14, 14, 16, 34, 36, 36, 38], "width": 240, "height": 149}}, "f2": {"f2": {"type": "mesh", "uvs": [0.32621, 0, 0.41944, 0, 0.35012, 0.19354, 0.27121, 0.4138, 0.36928, 0.57484, 0.47596, 0.75001, 0.68737, 0.83969, 1, 0.9723, 1, 1, 0.39032, 1, 0.21976, 0.99952, 0.11277, 0.80949, 0, 0.60918, 0, 0.43108, 0, 0.30493, 0.1188, 0.16054, 0.23334, 0.02134], "triangles": [2, 0, 1, 16, 0, 2, 15, 16, 2, 2, 14, 15, 3, 14, 2, 13, 14, 3, 12, 13, 3, 12, 3, 4, 11, 12, 4, 11, 4, 5, 9, 10, 11, 5, 9, 11, 6, 9, 5, 6, 7, 8, 9, 6, 8], "vertices": [1, 20, 15.72, 2.74, 1, 1, 20, 17.72, -1.26, 1, 2, 19, 29.9, -8.54, 0.04003, 20, 2.73, -5.03, 0.95997, 1, 19, 12.78, -4.5, 1, 2, 14, 29.8, -10, 0.28972, 19, 0.15, -9.02, 0.71028, 2, 14, 16.46, -4.08, 0.9978, 19, -13.59, -13.94, 0.0022, 1, 14, 4.36, -6.41, 1, 1, 14, -13.53, -9.87, 1, 1, 14, -15.07, -8.36, 1, 1, 14, 5.43, 12.52, 1, 1, 14, 11.2, 18.33, 1, 1, 14, 25.37, 11.61, 1, 2, 14, 40.31, 4.52, 0.79963, 19, -2.27, 8.74, 0.20037, 2, 14, 50.22, -5.21, 0.02123, 19, 11.62, 8.54, 0.97877, 2, 19, 21.46, 8.39, 0.99441, 20, -12.55, 6.11, 0.00559, 2, 19, 32.64, 2.52, 0.12548, 20, 0.07, 6.05, 0.87452, 1, 20, 12.24, 5.99, 1], "hull": 17, "edges": [0, 32, 0, 2, 14, 16, 16, 18, 18, 20, 6, 8, 8, 10, 20, 22, 22, 24, 24, 26, 26, 28, 2, 4, 4, 6, 28, 30, 30, 32, 10, 12, 12, 14], "width": 48, "height": 78}}, "d1": {"d1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [221.22, -30.34, -255.78, -30.34, -255.78, 163.66, 221.22, 163.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 477, "height": 194}}, "guan": {"guan": {"type": "mesh", "uvs": [0.93888, 0, 1, 0, 1, 0.07605, 0.64508, 0.44214, 0.61129, 0.65397, 0.70797, 1, 0.5874, 1, 0.4248, 0.88893, 0.60038, 0.72451, 0.63331, 0.43868, 0.06967, 0.40431, 0.00158, 0.16047], "triangles": [0, 1, 2, 9, 10, 11, 0, 9, 11, 3, 9, 0, 2, 3, 0, 4, 9, 3, 4, 8, 9, 6, 7, 8, 8, 4, 5, 6, 8, 5], "vertices": [41.56, -56.15, 40.01, -62.57, 33.03, -60.87, 8.43, -15.48, -10.17, -7.22, -44.41, -9.67, -41.34, 2.99, -27.01, 17.59, -16.37, -4.5, 9.04, -14.32, 26.53, 44.08, 50.66, 45.8], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 108, "height": 105}}, "s1": {"s1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-27.57, 12.94, 53, 72.16, 112.82, -9.22, 32.24, -68.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 101}}, "s2": {"s2": {"type": "mesh", "uvs": [0.87239, 0, 0.95372, 0.17256, 1, 0.27076, 1, 0.37958, 0.85337, 0.60504, 0.72982, 0.79501, 0.52243, 0.87683, 0.21023, 1, 0.13757, 1, 0, 0.86672, 0, 0.84519, 0.07688, 0.69875, 0.16816, 0.5249, 0.29019, 0.29245, 0.41482, 0.05508, 0.68699, 0], "triangles": [12, 13, 4, 4, 13, 3, 2, 3, 13, 1, 14, 15, 1, 15, 0, 13, 1, 2, 13, 14, 1, 11, 6, 7, 9, 10, 7, 11, 7, 10, 5, 6, 12, 9, 7, 8, 6, 11, 12, 5, 12, 4], "vertices": [1, 11, 146.46, 17.69, 1, 1, 11, 103.19, -7.15, 1, 1, 11, 78.57, -21.29, 1, 2, 4, 162.5, -32.34, 0.00284, 11, 49.47, -32.05, 0.99716, 2, 4, 97.17, -41.63, 0.40669, 11, -16, -40.34, 0.59331, 2, 4, 42.1, -49.46, 0.78599, 11, -71.18, -47.32, 0.21401, 2, 4, 12.72, -38.18, 0.90434, 11, -100.39, -35.58, 0.09566, 2, 4, -31.52, -21.2, 0.99706, 11, -144.36, -17.92, 0.00294, 2, 4, -34.2, -14.3, 0.99953, 11, -146.93, -10.98, 0.00047, 1, 4, -3.83, 12.51, 1, 1, 4, 1.89, 14.73, 1, 1, 4, 43.65, 22.51, 1, 2, 4, 93.23, 31.74, 0.79275, 11, -18.8, 33.09, 0.20725, 2, 4, 159.53, 44.09, 0.14123, 11, 47.67, 44.41, 0.85877, 2, 4, 227.22, 56.7, 0.00464, 11, 115.56, 55.98, 0.99536, 2, 4, 251.89, 36.51, 4e-05, 11, 139.91, 35.41, 0.99996], "hull": 16, "edges": [0, 30, 4, 6, 14, 16, 16, 18, 18, 20, 28, 30, 26, 28, 24, 26, 20, 22, 22, 24, 6, 8, 8, 10, 10, 12, 12, 14, 0, 2, 2, 4], "width": 98, "height": 144}}, "st1": {"st1": {"type": "mesh", "uvs": [0.59063, 0, 0.72704, 0.2233, 0.74326, 0.37341, 0.75696, 0.50016, 0.87092, 0.59816, 1, 0.70916, 1, 0.74077, 0.8101, 0.86918, 0.61665, 1, 0.53871, 1, 0.36675, 0.91495, 0.22716, 0.84591, 0.08791, 0.66876, 0, 0.55692, 0, 0.45461, 0.19919, 0.22062, 0.34596, 0.04822, 0.50815, 0], "triangles": [15, 1, 2, 1, 15, 16, 16, 17, 1, 17, 0, 1, 12, 15, 2, 15, 12, 14, 8, 9, 7, 9, 10, 7, 10, 3, 7, 3, 11, 2, 11, 3, 10, 7, 4, 6, 7, 3, 4, 2, 11, 12, 4, 5, 6, 12, 13, 14], "vertices": [1, 2, 67.68, -22.81, 1, 2, 3, -55.03, 36.53, 0.08506, 2, 20.87, -44.15, 0.91494, 2, 3, -25.05, 38.31, 0.4591, 2, -9.16, -44.22, 0.5409, 2, 3, 0.26, 39.8, 0.8997, 2, -34.52, -44.28, 0.1003, 2, 3, 20.69, 60.59, 0.99937, 2, -56.1, -63.87, 0.00063, 1, 3, 43.83, 84.12, 1, 1, 3, 50.11, 83.85, 1, 1, 3, 74.06, 46.68, 1, 1, 3, 98.47, 8.83, 1, 1, 3, 97.82, -5.97, 1, 2, 3, 79.48, -37.87, 0.9802, 2, -109.22, 37.76, 0.0198, 2, 3, 64.6, -63.77, 0.90429, 2, -92.89, 62.77, 0.09571, 2, 3, 28.22, -88.66, 0.6555, 2, -55.16, 85.56, 0.3445, 2, 3, 5.26, -104.37, 0.5046, 2, -31.35, 99.95, 0.4954, 2, 3, -15.08, -103.48, 0.43301, 2, -11.09, 97.91, 0.56699, 2, 3, -59.95, -63.64, 0.08259, 2, 31.45, 55.59, 0.91741, 1, 2, 62.79, 24.4, 1, 1, 2, 69.25, -7.22, 1], "hull": 18, "edges": [0, 34, 0, 2, 10, 12, 16, 18, 26, 28, 32, 34, 12, 14, 14, 16, 28, 30, 30, 32, 18, 20, 20, 22, 22, 24, 24, 26, 2, 4, 4, 6, 6, 8, 8, 10], "width": 190, "height": 199}}, "t1": {"t1": {"type": "mesh", "uvs": [0.64468, 0, 0.96554, 0.16176, 1, 0.4708, 1, 0.58368, 0.65974, 0.98902, 0.2194, 1, 0.19425, 1, 0, 0.61371, 0, 0.54511, 0.10289, 0.15565, 0.45452, 0], "triangles": [2, 0, 1, 10, 7, 8, 2, 4, 0, 3, 4, 2, 9, 10, 8, 7, 5, 6, 10, 0, 4, 4, 7, 10, 4, 5, 7], "vertices": [186.07, -63.57, 141, -137.76, 77.08, -136.56, 54.21, -133.01, -14.86, -36.22, -0.21, 72.91, 0.75, 79.13, 86.45, 114.98, 100.34, 112.82, 175.3, 75.17, 193.35, -16.59], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 250, "height": 205}}}}], "animations": {"a0-1": {"slots": {"yanj": {"attachment": [{"name": null}]}, "guan": {"attachment": [{"name": null}]}, "t1": {"attachment": [{"name": null}]}, "j1": {"attachment": [{"name": null}]}, "s2": {"attachment": [{"name": null}]}, "j2": {"attachment": [{"name": null}]}, "s1": {"attachment": [{"name": null}]}, "f1": {"attachment": [{"name": null}]}, "f2": {"attachment": [{"name": null}]}, "j3": {"attachment": [{"name": null}]}, "st1": {"attachment": [{"name": null}]}}, "bones": {"j2": {"scale": [{"y": 1.1}]}, "s2": {"rotate": [{"angle": -3.6}], "translate": [{"x": 9.35, "y": -0.8}]}, "s1": {"rotate": [{"angle": 13.2}], "translate": [{"x": 1.42, "y": -5.45}]}, "f1": {"rotate": [{"angle": 3.02}]}, "f3": {"rotate": [{"angle": 4.23}]}, "f4": {"rotate": [{"angle": 3.64}]}, "f5": {"rotate": [{"angle": 3.49}]}, "f6": {"rotate": [{"angle": -5.19}]}, "f7": {"rotate": [{"angle": -13.1}]}}}, "a01": {"bones": {"st1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 28, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 72, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "y": -12, "curve": 0.25, "c3": 0.75}, {"time": 1.2333}], "scale": [{"x": 0, "y": 0.4, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "st2": {"scale": [{"x": 0, "y": 0.4, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0.4}, {"time": 0.6667}]}, "d1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 152, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 18, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "scale": [{"x": 0, "y": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.1, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.942, "y": 0.962, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.02, "y": 1.02}, {"time": 0.5}]}, "j2": {"translate": [{}, {"time": 0.9, "y": 20, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 2, "curve": 0.25, "c3": 0.75}, {"time": 1.2}], "scale": [{"x": 0, "y": 0.4, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.94, "y": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": 1.1}]}, "j3": {"translate": [{}, {"time": 0.9667, "y": 136, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "y": 4, "curve": 0.25, "c3": 0.75}, {"time": 1.3}], "scale": [{"x": 0, "y": 0.4, "curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.1, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.96, "y": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "j1": {"translate": [{}, {"time": 0.7667, "y": 130, "curve": "stepped"}, {"time": 0.8333, "y": 130}, {"time": 0.9667}], "scale": [{"x": 0, "y": 0.4, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0.4}, {"time": 0.9333, "x": 1.084, "y": 1.067, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.902, "y": 0.953, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "s2": {"rotate": [{}, {"time": 0.5, "angle": 138}, {"time": 0.7, "angle": 145.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 136.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 145.2, "curve": 0.25, "c4": 0.81}, {"time": 1.3333, "angle": -3.6}], "translate": [{"x": 9.35, "y": -0.8}], "scale": [{"y": -1, "curve": "stepped"}, {"time": 0.7, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.845, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 0.654, "y": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "shear": [{"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -43.2, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "s1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 49.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -3.95, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 13.2}], "translate": [{}, {"time": 0.7333, "x": 1.24, "y": -9.92}, {"time": 1.2, "x": 1.42, "y": -5.45}], "scale": [{}, {"time": 0.7333, "x": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.86, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.1333}], "shear": [{"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 9.6, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2333}]}, "s3": {"rotate": [{"time": 0.6333}, {"time": 0.7667, "angle": 8.4}, {"time": 0.8667}], "shear": [{}, {"time": 0.7667, "x": -8.4, "y": 2.4}, {"time": 0.9}]}, "st3": {"scale": [{"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.84, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "t2": {"translate": [{"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.28, "y": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "yanj": {"scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.1333}]}, "f1": {"rotate": [{"angle": 3.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 12.07, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 2.77, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.02}]}, "f3": {"rotate": [{"angle": 4.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 3.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 12.07, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 4.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 4.23}]}, "f4": {"rotate": [{"angle": 3.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 12.07, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 2.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 3.64}]}, "f5": {"rotate": [{"angle": 3.49, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": 2.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 12.07, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 1.3333, "angle": 3.49}]}, "t1": {"rotate": [{"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "f2": {"rotate": [{"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 5.38, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -18.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "f6": {"rotate": [{"angle": -5.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 5.38, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -18.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -5.19}]}, "f7": {"rotate": [{"angle": -13.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.38, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -18.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -13.1}]}}}, "a01-1": {"bones": {"j2": {"scale": [{"y": 1.1}]}, "s2": {"rotate": [{"angle": -3.6}], "translate": [{"x": 9.35, "y": -0.8}]}, "s1": {"rotate": [{"angle": 13.2}], "translate": [{"x": 1.42, "y": -5.45}]}, "f1": {"rotate": [{"angle": 3.02}]}, "f3": {"rotate": [{"angle": 4.23}]}, "f4": {"rotate": [{"angle": 3.64}]}, "f5": {"rotate": [{"angle": 3.49}]}, "f6": {"rotate": [{"angle": -5.19}]}, "f7": {"rotate": [{"angle": -13.1}]}}}}}