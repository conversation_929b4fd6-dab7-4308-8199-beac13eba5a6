import MsgEnum from "../../game/event/MsgEnum";
import { <PERSON>piHandler } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import {
  AchieveTargetValResponse,
  DayTaskResponse,
  RedeemBuyMessage,
  RedeemLimitUpdateMessage,
} from "../../game/net/protocol/Activity";
import { CommLongListMessage } from "../../game/net/protocol/Comm";
import MsgMgr from "../../lib/event/MsgMgr";
import { FundModule } from "../fund/FundModule";
import { SonhaiMsgEnum } from "./SonhaiConfig";
import { sonhai_achieveId, sonhai_activityId, SonhaiModule } from "./SonhaiModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class SonhaiSubscriber {
  private upActivityDb(rs: CommLongListMessage) {
    for (let i = 0; i < rs.longList.length; i++) {
      if (rs.longList.includes(sonhai_activityId)) {
        SonhaiModule.data.upVO(sonhai_activityId);
        return;
      }
    }
  }

  private taskOk(rs: DayTaskResponse) {
    SonhaiModule.data.targetValList = rs.targetValList;
  }

  private lbBuyOver(rs: RedeemBuyMessage) {
    let list = [sonhai_activityId];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }
    SonhaiModule.data.chosenMap = rs.chosenMap;
    SonhaiModule.data.redeemMap = rs.redeemMap;
    //log.log("山海探险礼包购买成功", rs);
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList });
    MsgMgr.emit(SonhaiMsgEnum.SONHAI_GIFTPACKAGE_REFRESH);
  }

  private AchieveUp(rs: AchieveTargetValResponse) {
    let list = [sonhai_activityId];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }
    /** achieveId*/
    let data = SonhaiModule.data.getAchieveMap(rs.achieveId);
    if (data) {
      data.targetVal = rs.targetVal;
    }
  }

  private RedeemLimitUpdate(rs: RedeemLimitUpdateMessage) {
    let list = [sonhai_activityId];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }

    /** achieveId*/
    SonhaiModule.data.limitMap = rs.limitMap;
  }

  public register() {
    ApiHandler.instance.subscribe(CommLongListMessage, ActivityCmd.ActivityUp, this.upActivityDb);
    //订阅服务器消息
    ApiHandler.instance.subscribe(DayTaskResponse, ActivityCmd.ShenHaiJingTask, this.taskOk);

    //订阅服务器消息
    ApiHandler.instance.subscribe(RedeemBuyMessage, ActivityCmd.RedeemBuyMessage, this.lbBuyOver);

    ApiHandler.instance.subscribe(AchieveTargetValResponse, ActivityCmd.FundTargetUp, this.AchieveUp);

    ApiHandler.instance.subscribe(RedeemLimitUpdateMessage, ActivityCmd.RedeemLimitUpdate, this.RedeemLimitUpdate);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ActivityCmd.ActivityUp, this.upActivityDb);
    ApiHandler.instance.unSubscribe(ActivityCmd.ShenHaiJingTask, this.taskOk);
    ApiHandler.instance.unSubscribe(ActivityCmd.RedeemBuyMessage, this.lbBuyOver);
    ApiHandler.instance.unSubscribe(ActivityCmd.FundTargetUp, this.AchieveUp);
    ApiHandler.instance.unSubscribe(ActivityCmd.RedeemLimitUpdate, this.RedeemLimitUpdate);
  }
}
