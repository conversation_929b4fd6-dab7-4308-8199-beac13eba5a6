{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "c6071f62-6f8e-4fd2-8ad9-996fefa33f2c", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "c6071f62-6f8e-4fd2-8ad9-996fefa33f2c@6c48a", "displayName": "bg_tujian_1", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "c6071f62-6f8e-4fd2-8ad9-996fefa33f2c", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "c6071f62-6f8e-4fd2-8ad9-996fefa33f2c@f9941", "displayName": "bg_tujian_1", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 651, "height": 246, "rawWidth": 651, "rawHeight": 246, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-325.5, -123, 0, 325.5, -123, 0, -325.5, 123, 0, 325.5, 123, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 246, 651, 246, 0, 0, 651, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-325.5, -123, 0], "maxPos": [325.5, 123, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "c6071f62-6f8e-4fd2-8ad9-996fefa33f2c@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "c6071f62-6f8e-4fd2-8ad9-996fefa33f2c@6c48a", "compressSettings": {"useCompressTexture": true, "presetId": "95SlLFjTFJR7gLn6MNMc7O"}}}