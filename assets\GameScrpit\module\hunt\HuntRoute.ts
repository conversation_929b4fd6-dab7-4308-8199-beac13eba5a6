import { UIHuntBossRank } from "../../game/ui/ui_hunt/UIHuntBossRank";
import { UIHuntBossFinish } from "../../game/ui/ui_hunt/UIHuntBossFinish";
import { UIHuntFight } from "../../game/ui/ui_hunt/UIHuntFight";
import { UIHuntHelp } from "../../game/ui/ui_hunt/UIHuntHelp";
import { UIHuntMain } from "../../game/ui/ui_hunt/UIHuntMain";
import { UIHuntPreparePrimitive } from "../../game/ui/ui_hunt/UIHuntPreparePrimitive";
import { UIHuntPrepareSpirit } from "../../game/ui/ui_hunt/UIHuntPrepareSpirit";
import { UIHuntRank } from "../../game/ui/ui_hunt/UIHuntRank";
import { UIHuntShop } from "../../game/ui/ui_hunt/UIHuntShop";
import { UIHuntSummon } from "../../game/ui/ui_hunt/UIHuntSummon";
import { UIPrepareFight } from "../../game/ui/ui_hunt/UIPrepareFight";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
import { UIHuntBossStruck } from "../../game/ui/ui_hunt/UIHuntBossStruck";
import { UIPrepareWin } from "../../game/ui/ui_hunt/UIPrepareWin";
import { AudioName } from "db://assets/platform/src/AudioHelper";
import { UIHuntPrimitvieLose } from "../../game/ui/ui_hunt/UIHuntPrimitvieLose";
import { UIHuntPrepareLose } from "../../game/ui/ui_hunt/UIHuntPrepareLose";

export enum HuntRouteName {
  UIHuntMain = "UIHuntMain",
  UIHuntShop = "UIHuntShop",
  UIHuntRank = "UIHuntRank",
  UIHuntPrepareSpirit = "UIHuntPrepareSpirit",
  UIHuntBossRank = "UIHuntBossRank",
  UIHuntPreparePrimitive = "UIHuntPreparePrimitive",
  UIHuntHelp = "UIHuntHelp",
  UIHuntFight = "UIHuntFight",
  UIHuntBossFinish = "UIHuntBossFinish",
  UIHuntSummon = "UIHuntSummon",
  UIPrepareFight = "UIPrepareFight",
  UIHuntBossStruck = "UIHuntBossStruck",
  UIPrepareWin = "UIPrepareWin",
  UIHuntPrimitvieLose = "UIHuntPrimitvieLose",
  UIHuntPrepareLose = "UIHuntPrepareLose",
}
export class HuntRoute {
  rotueTables: Recording[] = [
    {
      node: UIHuntMain,
      uiName: HuntRouteName.UIHuntMain,
      keep: false,
      relevanceUIList: [],
      music: AudioName.Sound.天荒古境,
    },

    {
      node: UIHuntShop,
      uiName: HuntRouteName.UIHuntShop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHuntRank,
      uiName: HuntRouteName.UIHuntRank,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHuntPrepareSpirit,
      uiName: HuntRouteName.UIHuntPrepareSpirit,
      keep: false,
      relevanceUIList: [],
      music: AudioName.Sound.战斗,
    },
    {
      node: UIHuntBossRank,
      uiName: HuntRouteName.UIHuntBossRank,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHuntPreparePrimitive,
      uiName: HuntRouteName.UIHuntPreparePrimitive,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHuntHelp,
      uiName: HuntRouteName.UIHuntHelp,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHuntFight,
      uiName: HuntRouteName.UIHuntFight,
      keep: false,
      relevanceUIList: [],
      music: AudioName.Sound.战斗,
    },
    {
      node: UIHuntBossFinish,
      uiName: HuntRouteName.UIHuntBossFinish,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHuntSummon,
      uiName: HuntRouteName.UIHuntSummon,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPrepareFight,
      uiName: HuntRouteName.UIPrepareFight,
      keep: false,
      relevanceUIList: [],
      music: AudioName.Sound.战斗,
    },

    {
      node: UIHuntBossStruck,
      uiName: HuntRouteName.UIHuntBossStruck,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIPrepareWin,
      uiName: HuntRouteName.UIPrepareWin,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIHuntPrimitvieLose,
      uiName: HuntRouteName.UIHuntPrimitvieLose,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIHuntPrepareLose,
      uiName: HuntRouteName.UIHuntPrepareLose,
      keep: false,
      relevanceUIList: [],
    },
  ];
  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
