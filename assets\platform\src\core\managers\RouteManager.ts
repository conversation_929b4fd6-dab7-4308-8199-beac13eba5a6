import { director, is<PERSON><PERSON><PERSON> } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { RouteCtrl } from "../RouteCtrl";
import { RouteConfig } from "./RouteTableManager";

const log = Logger.getLoger(LOG_LEVEL.DEBUG);
// 全局路由表（整合所有模块的路由配置）
export const GlobalRouteTable: Record<string, RouteConfig> = {};

// 路由表装饰器：自动将模块路由表合并到全局表
export function routeTable() {
  return function (target: any, propertyKey: string) {
    // 在类初始化时执行（需确保类被实例化）
    // const originalInit = target.init;
    // target.init = async function (...args: any[]) {
    //   // 执行原有init逻辑（如RecordingMap注册）
    //   await originalInit?.apply(this, args);
    // };
    // 将当前模块的路由表合并到全局表
    const moduleRouteTable = target[propertyKey];
    for (const routeKey in moduleRouteTable) {
      // let key = moduleRouteTable[routeKey].url; //moduleRouteTable[routeKey].bundle +
      GlobalRouteTable[routeKey] = moduleRouteTable[routeKey];
    }
    log.log("RouteTable=======", GlobalRouteTable);
  };
}

export class RouteManager {
  private static _instance: RouteManager;
  public static get instance(): RouteManager {
    if (!this._instance) {
      this._instance = new RouteManager();
    }
    return this._instance;
  }

  private static _uiRouteCtrl: RouteCtrl;
  private static _topRouteCtrl: RouteCtrl;

  public static get uiRouteCtrl(): RouteCtrl {
    if (!isValid(RouteManager._uiRouteCtrl)) {
      RouteManager._uiRouteCtrl = director.getScene().getChildByPath("canvas_ui/ui_root")?.getComponent(RouteCtrl);
    }
    return RouteManager._uiRouteCtrl;
  }
  public static get topRouteCtrl(): RouteCtrl {
    if (!RouteManager._topRouteCtrl) {
      RouteManager._topRouteCtrl = director.getScene().getChildByPath("canvas_top/top_root").getComponent(RouteCtrl);
    }
    return RouteManager._topRouteCtrl;
  }
}
