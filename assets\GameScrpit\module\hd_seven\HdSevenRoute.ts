import { UIHdSeven } from "../../game/ui/ui_hd/UIHdSeven";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum HdSevenRouteItem {
  UIHdSeven = "UIHdSeven",
}

export class HdSevenRoute {
  rotueTables: Recording[] = [
    {
      node: UIHdSeven,
      uiName: HdSevenRouteItem.UIHdSeven,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
