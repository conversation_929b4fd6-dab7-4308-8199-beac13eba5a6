import { JsonMgr } from "../../game/mgr/JsonMgr";
import { CompeteLogMessage } from "../../game/net/protocol/Compete";
import { ByteValueList } from "../../game/net/protocol/ExternalMessage";
import { UIAzst } from "../../game/ui/ui_azst/UIAzst";
import { UIAzstAward } from "../../game/ui/ui_azst/UIAzstAward";
import { UIAzstFight } from "../../game/ui/ui_azst/UIAzstFight";
import { UIAzstLog } from "../../game/ui/ui_azst/UIAzstLog";
import { UIAzstLogFight } from "../../game/ui/ui_azst/UIAzstLogFight";
import { UIAzstPk } from "../../game/ui/ui_azst/UIAzstPk";
import { UIAzstLose } from "../../game/ui/ui_azst/UIAzstLose";
import data from "../../lib/data/data";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
import { PlayerModule } from "../player/PlayerModule";
import { AzstApi } from "./AzstApi";
import { AzstData } from "./AzstData";
import { AzstService } from "./AzstService";
import { UIAzstWin } from "../../game/ui/ui_azst/UIAzstWin";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { GameData } from "../../game/GameData";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { AudioName } from "db://assets/platform/src/AudioHelper";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class AzstModule extends data {
  protected saveKey(): string {
    return this.constructor.name;
  }

  private constructor() {
    super();
  }

  public static get instance(): AzstModule {
    if (!GameData.instance.AzstModule) {
      GameData.instance.AzstModule = new AzstModule();
      GameData.instance.AzstModule.onViewLoad();
      GameData.instance.AzstModule.getCompetJsom();
      GameData.instance.AzstModule.onViewLoad();
    }
    return GameData.instance.AzstModule;
  }

  /**模块需求请求多个协议才算完成，用来记录请求完成了几个协议 */
  private _socketCount: number = 0;

  /**配置表上恢复时间 -- 秒 */
  private _jsonTime: number = 0;

  /**配置表上最大次数 */
  private _jsonMaxCount: number = 0;

  private _api = new AzstApi();
  private _data = new AzstData();
  private _service = new AzstService();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected onViewLoad() {
    let data: Recording = {
      node: UIAzst,
      uiName: "UIAzst",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = {
      node: UIAzstAward,
      uiName: "UIAzstAward",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = {
      node: UIAzstLog,
      uiName: "UIAzstLog",
      keep: false,
      relevanceUIList: [UIAzst],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = {
      node: UIAzstPk,
      uiName: "UIAzstPk",
      keep: false,
      relevanceUIList: [UIAzst],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = {
      node: UIAzstLose,
      uiName: "UIAzstLose",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = {
      node: UIAzstWin,
      uiName: "UIAzstWin",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = {
      node: UIAzstFight,
      uiName: "UIAzstFight",
      keep: false,
      relevanceUIList: [],
      music: AudioName.Sound.战斗,
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = {
      node: UIAzstLogFight,
      uiName: "UIAzstLogFight",
      keep: false,
      relevanceUIList: [],
      music: 1092,
    };
    RecordingMap.instance.addRecording(data.uiName, data);
  }

  private getCompetJsom() {
    let c_compete = JsonMgr.instance.jsonList.c_compete;
    let list = Object.keys(c_compete);
    let oneIndex = list[0];
    this._jsonTime = c_compete[oneIndex].time;
    this._jsonMaxCount = c_compete[oneIndex].freeMax;
  }

  public init(data?: any, completedCallback?: Function) {
    // 模块初始化
    this._api = new AzstApi();
    this._data = new AzstData();
    this._service = new AzstService();

    // 模块数据初始化
    AzstModule.api.getAzstMessage((data) => {
      this.chckFreeCount();
      this.settleTime();

      AzstModule.api.getLogMessage((data: ByteValueList) => {
        let arr: CompeteLogMessage[] = [];
        for (let i = 0; i < data.values.length; i++) {
          arr.push(CompeteLogMessage.decode(data.values[i]));
        }
        AzstModule.data.logMessage = arr;

        completedCallback && completedCallback();
      });
    });

    this._service.init();
  }

  private chckFreeCount() {
    TickerMgr.setInterval(
      1,
      () => {
        let freeFreshCount = AzstModule.data.freeFreshCount;
        if (freeFreshCount == -1) {
          return;
        }

        if (freeFreshCount < this._jsonMaxCount) {
          let num = this._jsonTime * 1000 + AzstModule.data.lastUpdateTime;
          if (TimeUtils.serverTime > num) {
            AzstModule.api.postSyncRefreshCount();
          }
        }
      },
      false
    );
  }

  private settleTime() {
    TickerMgr.setInterval(
      1,
      () => {
        AzstModule.data.dailyRewardDeadline = AzstModule.data.dailyRewardDeadline - 1000;
        AzstModule.data.weekRewardDeadline = AzstModule.data.weekRewardDeadline - 1000;
      },
      false
    );
  }
}
