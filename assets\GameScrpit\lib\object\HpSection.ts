import {
  _decorator,
  isValid,
  Label,
  math,
  Node,
  Prefab,
  Sprite,
  SpriteFrame,
  Tween,
  tween,
  UIOpacity,
  UITransform,
  v3,
  Vec3,
} from "cc";
import { Section } from "./Section";
import GameObject from "./GameObject";
import FightManager, { scaleList } from "../../game/fight/manager/FightManager";
import { HpManager } from "../../game/fight/manager/HpManager";
import RenderSection from "./RenderSection";
import { GORole } from "../../game/fight/role/GORole";
import ResMgr from "../common/ResMgr";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";
import DirectSection, { DIRECT } from "./DirectSection";
import ToolExt from "../../game/common/ToolExt";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { IConfigBloodPlace } from "../../game/JsonDefine";
import { AssetMgr } from "db://assets/platform/src/ResHelper";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass
export default class HPSection extends Section {
  protected _render: Node = null;
  private _initcallBack;

  private _mirroring: boolean = false;

  private _renderName: string;

  public static sectionName(): string {
    return "HPSection";
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    console.log("HpSection onInit");
    this.onMsg("OnInitHP", this.OnInitHP.bind(this));
    this.onMsg("OnHurt", this.onHurt.bind(this));
    this.onMsg("OnDirectChange", this.onDirectChange.bind(this));
    this._initcallBack = args.callBack;
    this._mirroring = args.mirroring || false;
    this._renderName = args.renderName;
    FightManager.instance.getSection(HpManager).callHpRender(this.initHpRender.bind(this));
  }

  private initHpRender(node) {
    this._render = node;
    this._render.active = false;

    this.onDirectChange();
    if (this._mirroring == true) {
      node.getChildByName("lbl_infinite").active = true;
      node.getChildByName("lbl_infinite").getComponent(Label).string = "无限";
    }

    let sprKey = "bg_zhandou_jindutiao_2";
    if (this.getSub().getSection(DirectSection).direct == DIRECT.RIGHT) {
      sprKey = "bg_zhandou_jindutiao_1";
    }

    this.assetMgr.loadSpriteFrame(BundleEnum.RESOURCES, `images/${sprKey}`, (sp: SpriteFrame) => {
      if (isValid(this.getSub()) == false) return;
      this._render.getChildByName("bar").getComponent(Sprite).spriteFrame = sp;
      if (this._render.getComponent(UIOpacity)) {
        this._render.getComponent(UIOpacity).opacity = 255;
      }
      this.OnInitHP();
      this._initcallBack();
    });

    this.assetMgr.loadSpriteFrame(BundleEnum.RESOURCES, `images/${sprKey}`, (sp: SpriteFrame) => {
      if (isValid(this.getSub()) == false) return;
      this._render.getChildByName("bar_opacity").getComponent(Sprite).spriteFrame = sp;
    });

    this.ready();
  }

  private _bar_tween = null;
  private _bar_opacity_tween = null;

  private OnInitHP() {
    if (this._render) {
      if (isValid(this.getSub()) == false) return;
      let roundDetail = (this.getSub() as GORole).getRoundDetail();
      let bar = this._render.getChildByName("bar");
      let bar_opacity = this._render.getChildByName("bar_opacity");
      let fillrange = roundDetail.a / roundDetail.d;

      bar.getComponent(Sprite).fillRange = fillrange;
      bar_opacity.getComponent(Sprite).fillRange = fillrange;
      this.setRoleName();
    }
  }

  public setRoleName() {
    let sub = this.getSub() as GORole;
    this._render.getChildByName("lab_Name").active = !sub.getIsPlayerRole();
    let str = sub.getDbRoleName();
    if (this._renderName) {
      str = this._renderName;
    }
    this._render.getChildByName("lab_Name").getComponent(Label).string = str;
  }

  private onHurt() {
    if (this._render) {
      if (isValid(this.getSub()) == false) return;
      let roundDetail = (this.getSub() as GORole).getRoundDetail();
      let bar = this._render.getChildByName("bar");
      let bar_opacity = this._render.getChildByName("bar_opacity");
      let fillrange = roundDetail.a / roundDetail.d;

      this._bar_tween = tween(bar.getComponent(Sprite))
        .to(0.4 / scaleList[FightManager.instance.speed], { fillRange: fillrange })
        .start();
      this._bar_opacity_tween = tween(bar_opacity.getComponent(Sprite))
        .to(0.7 / scaleList[FightManager.instance.speed], { fillRange: fillrange })
        .start();
    }
  }

  public getRender(): Node {
    return this._render;
  }

  public setRenderActive(bool: boolean) {
    this._render.active = bool;
  }

  public onRemove(): void {
    log.log("血条onRemove");
    if (this._bar_tween) {
      this._bar_tween.stop();
    }
    if (this._bar_opacity_tween) {
      this._bar_opacity_tween.stop();
    }

    this.assetMgr.release();
    this.offMsg("OnInitHP", this.OnInitHP.bind(this));
    this.offMsg("OnHurt", this.onHurt.bind(this));
    this.offMsg("OnDirectChange", this.onDirectChange.bind(this));
  }

  public updateSelf(dt: any): void {
    let sub = this.getSub() as GORole;
    let subRender = sub.getSection(RenderSection);

    let dbId = sub.getDetail().dbId;
    let skinDb = subRender.getSkinDb(dbId);
    let placeDb = JsonMgr.instance.jsonList.c_bloodPlace[skinDb.placeId];
    let sceneId = sub.getDetail().sceneId;
    let posY = subRender.getPlacePos(sceneId, placeDb);

    let subRenderIns = subRender.getRender();
    let newPos = ToolExt.transferOfAxes(subRenderIns, this._render.parent);
    let pos = v3(newPos.x, newPos.y + posY, 0);
    this._render.setPosition(pos);
    if (subRenderIns.getComponent(UIOpacity)) {
      if (subRenderIns.getComponent(UIOpacity).opacity < 255) {
        this._render.active = false;
      } else if (subRenderIns.getComponent(UIOpacity).opacity >= 255) {
        this._render.active = true;
      }
    }
  }

  private onDirectChange() {
    let sub = this.getSub() as GORole;
    let subDirect = sub.getSection(DirectSection);
    if (subDirect.getDirect() == DIRECT.LEFT) {
      let scale = this._render.getScale();
      this._render.setScale(Math.abs(scale.x), scale.y, 1);
      this._render.setScale(scale.x * -1, scale.y, 1);

      scale = this._render.getChildByName("lab_Name").getScale();
      this._render.getChildByName("lab_Name").setScale(scale.x * -1, scale.y, scale.z);
    } else if (subDirect.getDirect() == DIRECT.RIGHT) {
      let scale = this._render.getScale();
      this._render.setScale(Math.abs(scale.x), scale.y, 1);
      this._render.setScale(scale.x * 1, scale.y, 1);

      scale = this._render.getChildByName("lab_Name").getScale();
      this._render.getChildByName("lab_Name").setScale(scale.x * 1, scale.y, scale.z);
    }
  }
}
