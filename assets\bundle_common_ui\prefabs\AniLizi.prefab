[{"__type__": "cc.Prefab", "_name": "AniLizi", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "AniLizi", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": -0.6755000000000008, "y": -0.727000000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "particle1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}], "_prefab": {"__id__": 7}, "_lpos": {"__type__": "cc.Vec3", "x": -62.84449999540864, "y": 165.253, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44GQEKqp9K4qv0jT6eGnlI"}, {"__type__": "cc.ParticleSystem2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 1, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "duration": -1, "emissionRate": 800, "life": 0.3, "lifeVar": 0.9, "angle": 360, "angleVar": 360, "startSize": 20, "startSizeVar": 40, "endSize": 40, "endSizeVar": 0, "startSpin": -47.369998931884766, "startSpinVar": 0, "endSpin": -47.369998931884766, "endSpinVar": -142.11000061035156, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "speed": 0, "speedVar": 100, "tangentialAccel": -92.11000061035156, "tangentialAccelVar": 65.79000091552734, "radialAccel": -671.0499877929688, "radialAccelVar": 65.79000091552734, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "playOnLoad": true, "autoRemoveOnFinish": false, "_preview": true, "preview": true, "_custom": true, "_file": {"__uuid__": "c97dd2e8-f84c-4c44-ab58-7ae8e4ba73c9", "__expectedType__": "cc.ParticleAsset"}, "_spriteFrame": {"__uuid__": "5e96ec0b-920a-4acf-8925-ef682885a6c9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_totalParticles": 75, "_startColor": {"__type__": "cc.Color", "r": 202, "g": 128, "b": 86, "a": 163}, "_startColorVar": {"__type__": "cc.Color", "r": 229, "g": 255, "b": 173, "a": 198}, "_endColor": {"__type__": "cc.Color", "r": 253, "g": 32, "b": 85, "a": 214}, "_endColorVar": {"__type__": "cc.Color", "r": 107, "g": 249, "b": 249, "a": 188}, "_positionType": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23DTxibJhNvpke0PIkwiG+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76QEgdme1B/ZC2kDzZsKmC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "particle2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}, {"__id__": 11}], "_prefab": {"__id__": 13}, "_lpos": {"__type__": "cc.Vec3", "x": 62.84449999540864, "y": -165.253, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69314Xm9FOn42Z6H6FfQi/"}, {"__type__": "cc.ParticleSystem2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 12}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 1, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "duration": -1, "emissionRate": 800, "life": 0.3, "lifeVar": 0.9, "angle": 360, "angleVar": 360, "startSize": 20, "startSizeVar": 40, "endSize": 40, "endSizeVar": 0, "startSpin": -47.369998931884766, "startSpinVar": 0, "endSpin": -47.369998931884766, "endSpinVar": -142.11000061035156, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "speed": 0, "speedVar": 100, "tangentialAccel": -92.11000061035156, "tangentialAccelVar": 65.79000091552734, "radialAccel": -671.0499877929688, "radialAccelVar": 65.79000091552734, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "playOnLoad": true, "autoRemoveOnFinish": false, "_preview": true, "preview": true, "_custom": true, "_file": {"__uuid__": "c97dd2e8-f84c-4c44-ab58-7ae8e4ba73c9", "__expectedType__": "cc.ParticleAsset"}, "_spriteFrame": {"__uuid__": "5e96ec0b-920a-4acf-8925-ef682885a6c9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_totalParticles": 75, "_startColor": {"__type__": "cc.Color", "r": 202, "g": 128, "b": 86, "a": 163}, "_startColorVar": {"__type__": "cc.Color", "r": 229, "g": 255, "b": 173, "a": 198}, "_endColor": {"__type__": "cc.Color", "r": 253, "g": 32, "b": 85, "a": 214}, "_endColorVar": {"__type__": "cc.Color", "r": 107, "g": 249, "b": 249, "a": 188}, "_positionType": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abkMxA8Z5NmZ561lTwk1tp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "925D5V+6xFQYGRxK+nzigV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 15}, "_contentSize": {"__type__": "cc.Size", "width": 136.20899999999997, "height": 330.506}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0lNpt6hJAPo0QRGnzFhWG"}, {"__type__": "9e15a6dxsNC54JenVbSxmqv", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 17}, "node1": {"__id__": 2}, "node2": {"__id__": 8}, "speed": 200, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fozqFNKdGrLmdeI/c5saX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": []}]