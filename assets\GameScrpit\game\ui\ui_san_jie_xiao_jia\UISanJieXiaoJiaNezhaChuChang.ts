import { _decorator } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

@ccclass("UISanJieXiaoJiaNezhaChuChang")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_SAN_JIE_XIAO_JIA,
  url: "prefab/ui/UISanJieXiaoJiaNezhaChuChang",
  nextHop: [],
  exit: "",
  transparent: true,
})
export class UISanJieXiaoJiaNezhaChuChang extends BaseCtrl {
  start() {
    super.start();
  }

  update(deltaTime: number) {}

  private on_click_btn_blank() {
    this.closeBack();
  }
}
