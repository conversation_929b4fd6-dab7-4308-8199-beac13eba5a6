import StorageMgr, { StorageKeyEnum } from "db://assets/platform/src/StorageHelper";
import ToolExt from "../../../game/common/ToolExt";
import { IAttr } from "../../../game/GameDefine";
import { JsonMgr } from "../../../game/mgr/JsonMgr";
import { PupilMessage, PupilSlotMessage, PupilTrainMessage } from "../../../game/net/protocol/Pupil";
import { IConfigLeaderRecord } from "../../player/PlayerConfig";
import { PlayerModule } from "../../player/PlayerModule";
import { PupilModule } from "./PupilModule";

export interface IConfigPupilSkin {
  id: number;
  spineId: number;
  headId: string;
  sex: number;
}

export interface IConfigPupilName {
  id: number;
  des: string;
  sex: number;
  skinId: number;
}

/**
 * 弟子配置
 */
export interface IConfigPupil {
  id: number;
  /**品质描述 */
  des: string;
  /**天资背景 */
  talentBg: string;
  /**品质颜色 */
  color: number;
  /**培养获得阅历加成 */
  rewardAdd: number;
  /**徒弟成年运势品质加成 */
  rate: number;
  /**培养1次进度增加 */
  trainAdd: number;
  /**随机招徒抽中权重 */
  weight: number;
  /**解锁的仙缘值 */
  unlock: number;
  cost: number;
  firstNum: number;
  /**出师领悟随机属性范围值 */
  rewardAttrList: number[];
  bookIdList: number[];
  bookNum: number;
  rewardList: number[];
  rewardAttrIdList: number[];
  /**初始随机抗性ID和值 */
  firstDefendList: number[][];
  /**成年基础运势 */
  basicSpeed: number;
  /**徒弟培养成年总进度 */
  finish: number;
  /**成年未婚徒弟上限 */
  max: number;
  /**联姻徒弟赚速增加倍数上限 */
  maxAdd: number;
  /**解锁徒弟培养槽位所需主角等级 */
  unlockPlaceList: number[];
  /**任命徒弟槽位解锁所需徒弟出师数量 */
  unlockWorkPlaceList: number[];
  /**任命徒弟额外加成百分比 */
  placeAdd: number[];
  /**活力恢复时间 */
  time: number;
  /**结伴发布时间 */
  time2: number;
  showList: number[];
  unlockFast: number;
  doubleRate: number;
}

export class PupilData {
  // 剩余刷新次数
  public localMarryRefreshCnt: number;

  private _allPupilMap: { [key: number]: PupilMessage } = {};

  public get allPupilMap(): { [key: number]: PupilMessage } {
    return this._allPupilMap;
  }

  private _pupilTrainMsg: PupilTrainMessage = null;

  public get pupilTrainMsg(): PupilTrainMessage {
    return this._pupilTrainMsg;
  }

  public set pupilTrainMsg(value: PupilTrainMessage) {
    this._pupilTrainMsg = value;
  }

  /** =============================== 繁荣度 =================================== */

  /**获取某一弟子的繁荣度 */
  public getPupilBloom(pupilId: number) {
    let pupilMsg = this._allPupilMap[pupilId];
    // 未成年加成计算
    if (pupilMsg.ownInfo.adultAttrList.length <= 0) {
      return 0;
    }

    let bloom = 0;
    // 成年弟子
    let config = PupilModule.data.getConfigPupil(pupilMsg.ownInfo.talentId);
    let playerLv = PlayerModule.data.getPlayerInfo().level;

    let leaderConfig: IConfigLeaderRecord = PlayerModule.data.getConfigLeaderData(playerLv);
    bloom = ((config.basicSpeed * config.rate) / 10000) * (1 + leaderConfig.trainAdd / 10000);
    return bloom;
  }

  /**获取总的繁荣度 */
  public getTotalBloom() {
    let totalBloom = 0;
    let list = Object.keys(this._allPupilMap).map(Number);
    for (let i = 0; i < list.length; i++) {
      totalBloom += this.getPupilBloom(list[i]);
    }
    return totalBloom;
  }

  /**获取某一弟子的属性  --  加成到主角身上 */
  public getPupilWorkAttrMap(pupilId: number): IAttr {
    let attr = new IAttr();

    let pupilMsg = this._allPupilMap[pupilId];
    if (!pupilMsg || pupilMsg.ownInfo.adultAttrList.length == 0) {
      return attr;
    }

    let config = PupilModule.data.getConfigPupil(pupilMsg.ownInfo.talentId);

    let base_attr = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.basicAttrList);
    let init_attr = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.initAttrList);
    let adult_attr = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.adultAttrList);
    let partner_basicAttrList = ToolExt.traAwardItemMapList(pupilMsg.partnerInfo?.basicAttrList ?? []);
    let partner_initAttrList = ToolExt.traAwardItemMapList(pupilMsg.partnerInfo?.initAttrList ?? []);
    let partner_adultAttrList = ToolExt.traAwardItemMapList(pupilMsg.partnerInfo?.adultAttrList ?? []);

    // 基础属性附加到主角身上
    base_attr.forEach((item) => {
      attr[item.id] += item.num;
    });

    // 联姻的两个徒弟(包含对方的)基础属性附加到主角身上
    if (pupilMsg.partnerInfo) {
      partner_basicAttrList.forEach((item) => {
        attr[item.id] =
          item.num > (attr[item.id] * config.maxAdd) / 10000
            ? attr[item.id] + (attr[item.id] * config.maxAdd) / 10000
            : attr[item.id] + item.num;
      });
    }

    function getMax() {
      let max = adult_attr[0]["num"];
      for (let i = 0; i < init_attr.length; i++) {
        let num = init_attr[i]["num"];
        if (num > max) {
          max = num;
        }
      }
      return max;
    }

    // 任命
    if (this.pupilTrainMsg.workSlotList.indexOf(pupilId) != -1) {
      let max = getMax();

      init_attr.forEach((item) => {
        attr[item.id] += item.num;
      });
      adult_attr.forEach((item) => {
        attr[item.id] += item.num;
      });
      partner_initAttrList.forEach((item) => {
        attr[item.id] =
          item.num > (max * config.maxAdd) / 10000
            ? attr[item.id] + (max * config.maxAdd) / 10000
            : attr[item.id] + item.num;
      });
      partner_adultAttrList.forEach((item) => {
        attr[item.id] =
          item.num > (max * config.maxAdd) / 10000
            ? attr[item.id] + (max * config.maxAdd) / 10000
            : attr[item.id] + item.num;
      });

      // 任命加成
      let list = Object.keys(attr).map(Number);
      let index = this.pupilTrainMsg.workSlotList.indexOf(pupilId);
      for (let i = 0; i < list.length; i++) {
        let key = list[i];
        attr[key] *= 1 + config.placeAdd[index] / 10000;
      }
    }

    return attr;
  }

  private addAttr(attrTotal: IAttr, attr: IAttr) {
    let list = Object.keys(attrTotal);
    for (let i = 0; i < list.length; i++) {
      let id = Number(list[i]);
      attrTotal[id] += attr[id];
    }
  }

  public getPupilTotalAttrMap(): IAttr {
    let attrAll: IAttr = new IAttr();
    let list = Object.keys(this._allPupilMap).map(Number);
    for (let i = 0; i < list.length; i++) {
      let attrPupil = this.getPupilWorkAttrMap(list[i]);
      this.addAttr(attrAll, attrPupil);
    }

    return attrAll;
  }

  public getConfigPulilSkin(id: number): IConfigPupilSkin {
    return JsonMgr.instance.jsonList.c_pupilSkin[id];
  }

  public getConfigPupilName(id: number): IConfigPupilName {
    return JsonMgr.instance.jsonList.c_pupilName[id];
  }

  public getConfigPupil(id: number): IConfigPupil {
    return JsonMgr.instance.jsonList.c_pupil[id];
  }
}
