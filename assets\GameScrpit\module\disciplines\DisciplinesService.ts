import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { DisciplinesMsgEnum } from "./DisciplinesConfig";
import { DisciplinesModule } from "./DisciplinesModule";

/**
 * 模块逻辑处理
 */
export class DisciplinesService {
  private _tickId: number = null;
  init() {
    if (this._tickId) {
      TickerMgr.clearInterval(this._tickId);
    }
    MsgMgr.off(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE, this.updatePopover, this);
    MsgMgr.on(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE, this.updatePopover, this);
    this._tickId = TickerMgr.setInterval(3, this.updatePopover.bind(this), false);
  }

  /**
   * 红点更新方法
   */
  private updatePopover() {
    this.xiuXingFundCb();
    this.fuZeCb();
    this.taskCb();
    this.liBaoCb();
  }

  //**基金红点 */
  private async xiuXingFundCb() {
    let achieveVO = await DisciplinesModule.data.getDisFundOV();
    let achievementId1 = achieveVO.id;
    let AchieveData = DisciplinesModule.data.getAchieveData(achievementId1);

    /**计算到了任务完成到哪里 */
    let targetIndex = -1;
    for (let i = 0; i < achieveVO.requireList.length; i++) {
      if (AchieveData.targetVal >= achieveVO.requireList[i]) {
        targetIndex = i;
      } else {
        break;
      }
    }
    /**开始判断免费的是否领取过了 */
    let bool = false;
    if (AchieveData.basicTakeList.indexOf(targetIndex) == -1) {
      bool = true;
    }

    if (bool == false && AchieveData.paid == true) {
      if (AchieveData.paidTakeList.indexOf(targetIndex) == -1) {
        bool = true;
      }
    }

    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_xiuxing.btn_type1.id, bool);
  }

  //**福泽红点 */
  private async fuZeCb() {
    let signVO = await DisciplinesModule.data.getdb().then((res) => res.signVO);
    let daySign = DisciplinesModule.data.daySign;
    let maxDay = daySign.basicList.length;

    let bool = false;
    if (daySign.sign == false && maxDay <= signVO.basicRewardList.length) {
      bool = true;
    }

    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_xiuxing.btn_type2.id, bool);
  }

  /**任务红点 */
  private async taskCb() {
    let taskVO = await DisciplinesModule.data.getDisTaskVO();
    let dayTask = DisciplinesModule.data.getDayTask();
    let takeList = taskVO.takeList;
    let bool = false;
    for (let i = 0; i < takeList.length; i++) {
      //1是可领取
      if (dayTask.takeList.indexOf(i) == -1) {
        if (takeList[i][1] <= dayTask.targetValList[i]) {
          bool = true;
          break;
        }
      }
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_xiuxing.btn_type3.id, bool);
  }

  /**礼包红点 */
  private async liBaoCb() {
    let redeemList = await DisciplinesModule.data.getRedeemList();
    let redeemMap = await DisciplinesModule.data.redeemMap;

    let bool = false;

    let info = redeemList[0];
    let num = redeemMap[info.id] || 0;
    if (num < info.max) {
      bool = true;
    }

    let leaderRecharge = await DisciplinesModule.data.getLeaderRecharge();
    if (leaderRecharge.numerator >= leaderRecharge.denominator && leaderRecharge.take == false) {
      bool = true;
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_xiuxing.btn_type4.id, bool);
  }
}
