import { UITransform } from "cc";
import { CCInteger } from "cc";
import { math } from "cc";
import { _decorator, Component, Node } from "cc";
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass("AniLiziCtrl")
@executeInEditMode
export class AniLiziCtrl extends Component {
  @property(Node)
  node1: Node = null;

  @property(Node)
  node2: Node = null;

  @property(CCInteger)
  speed: number = 100;

  time: number = 0;
  timeMax: number = 100;

  contentSize: math.Size;

  start() {
    this.time = 0;
    let uiTransform = this.node.getComponent(UITransform);
    this.contentSize = uiTransform.contentSize;

    this.timeMax = ((this.contentSize.width + this.contentSize.height) * 2) / this.speed;
  }

  update(deltaTime: number) {
    this.time += deltaTime;
    if (this.timeMax < this.time) {
      this.time -= this.timeMax;
    }

    let s = this.time * this.speed;

    let x = 0;
    let y = 0;
    if (s < this.contentSize.width) {
      x = s - this.contentSize.width / 2;
      y = this.contentSize.height / 2;
    } else if (s < this.contentSize.width + this.contentSize.height) {
      x = this.contentSize.width / 2;
      y = this.contentSize.height / 2 - (s - this.contentSize.width);
    } else if (s < this.contentSize.width * 2 + this.contentSize.height) {
      x = this.contentSize.width / 2 - 2 - (s - this.contentSize.width - this.contentSize.height);
      y = -this.contentSize.height / 2;
    } else {
      x = -this.contentSize.width / 2;
      y = s - this.contentSize.width * 2 - this.contentSize.height - this.contentSize.height / 2;
    }
    this.node1.setPosition(x, y);
    this.node2.setPosition(-x, -y);
  }
}
