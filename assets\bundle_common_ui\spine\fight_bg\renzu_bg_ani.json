{"skeleton": {"hash": "N8og+LZccCc2T31Sz/286VdksHA=", "spine": "3.8.75", "x": -391.97, "y": -521.84, "width": 789.18, "height": 1047.53}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "y": -24.46, "scaleX": 0.7096, "scaleY": 0.7096}, {"name": "strall", "parent": "all", "length": 128.08, "rotation": -0.44, "x": -352.3, "y": -14.09}, {"name": "bone", "parent": "strall", "length": 135.7, "rotation": 176.13, "x": 0.51, "y": 134.83}, {"name": "bone2", "parent": "bone", "length": 43.92, "rotation": -128.91, "x": -2.2, "y": -3.08}, {"name": "bone3", "parent": "bone2", "length": 44.52, "rotation": 19.44, "x": 43.92}, {"name": "bone4", "parent": "bone3", "x": 28.97, "y": 108.69}, {"name": "bone5", "parent": "bone3", "x": 73.81, "y": -31.61}, {"name": "bone6", "parent": "bone3", "length": 16.47, "rotation": 10.15, "x": 42.4, "y": 1.19}, {"name": "bone7", "parent": "bone", "length": 47.28, "rotation": 136.23, "x": -28.63, "y": 14.59}, {"name": "bone8", "parent": "bone7", "length": 49.29, "rotation": -76.54, "x": 47.02, "y": 0.24}, {"name": "bone9", "parent": "bone8", "length": 41.89, "rotation": 80.66, "x": 49.29}, {"name": "bone10", "parent": "bone", "length": 58.66, "rotation": 46.83, "x": 35.5, "y": 15.11}, {"name": "bone11", "parent": "bone10", "length": 53.93, "rotation": 88.07, "x": 58.4, "y": 0.68}, {"name": "bone12", "parent": "bone11", "length": 41.7, "rotation": -89.71, "x": 50.87, "y": -2.08}, {"name": "bone13", "parent": "bone4", "length": 100, "rotation": -173.16, "x": 2.02, "y": 4.65}, {"name": "bone14", "parent": "bone13", "length": 97.71, "rotation": 27.66, "x": 94.9, "y": -0.02}, {"name": "bone15", "parent": "bone14", "length": 37.48, "rotation": -0.24, "x": 97.71}, {"name": "bone16", "parent": "bone5", "length": 70.48, "rotation": -143.07, "x": -9.4, "y": 10.56}, {"name": "bone17", "parent": "bone16", "length": 86.28, "rotation": -3.45, "x": 70.48}, {"name": "bone18", "parent": "bone17", "length": 32.49, "rotation": -7.07, "x": 89.16, "y": -0.87}, {"name": "bone19", "parent": "bone", "x": -37.2, "y": -0.5}, {"name": "bone20", "parent": "bone", "x": 65.14, "y": -5.48}, {"name": "bone21", "parent": "bone", "x": 0.46, "y": 10.8}, {"name": "bone22", "parent": "bone", "length": 20.69, "rotation": 91.1, "x": 10.53, "y": 24.92}, {"name": "bone23", "parent": "bone22", "length": 13.79, "rotation": -12.95, "x": 20.69}, {"name": "bone24", "parent": "bone23", "length": 16.59, "rotation": -8.67, "x": 13.81, "y": -0.22}, {"name": "bone25", "parent": "bone21", "length": 17.89, "rotation": 93.29, "x": -13.4, "y": 12.35}, {"name": "bone26", "parent": "bone25", "length": 16.97, "rotation": -17.46, "x": 18.11, "y": -0.04}, {"name": "bone27", "parent": "bone26", "length": 17.71, "rotation": -24.09, "x": 16.97}, {"name": "bone28", "parent": "bone2", "x": 27.66, "y": -7.08}, {"name": "bone29", "parent": "bone2", "x": 56.76, "y": -2.9}, {"name": "bone30", "parent": "bone6", "x": 0.18, "y": -29.2}, {"name": "bone31", "parent": "bone6", "x": 26.29, "y": -26.12}, {"name": "ljio1", "parent": "strall", "rotation": 0.44, "x": 31.93, "y": 43.16}, {"name": "ljio2", "parent": "ljio1", "x": 30.44, "y": -29.41}, {"name": "rjio1", "parent": "strall", "rotation": 0.44, "x": -45.72, "y": 42.31}, {"name": "rjio2", "parent": "rjio1", "x": -36.12, "y": -24.77}, {"name": "strength", "parent": "all", "length": 102.07, "rotation": -1.71, "x": 334.28, "y": -21.34}, {"name": "strall2", "parent": "strength", "rotation": 1.28, "x": 5, "y": 7.39, "scaleX": -1}, {"name": "bone32", "parent": "strall2", "length": 135.7, "rotation": 176.13, "x": 0.51, "y": 134.83}, {"name": "bone33", "parent": "bone32", "length": 43.92, "rotation": -128.91, "x": -2.2, "y": -3.08}, {"name": "bone34", "parent": "bone33", "length": 44.52, "rotation": 19.44, "x": 43.92}, {"name": "bone35", "parent": "bone34", "x": 28.97, "y": 108.69}, {"name": "bone36", "parent": "bone35", "length": 100, "rotation": -173.16, "x": 2.02, "y": 4.65}, {"name": "bone37", "parent": "bone36", "length": 97.71, "rotation": 27.66, "x": 94.9, "y": -0.02}, {"name": "bone38", "parent": "bone37", "length": 37.48, "rotation": -0.24, "x": 97.71}, {"name": "bone39", "parent": "bone34", "x": 73.81, "y": -31.61}, {"name": "bone40", "parent": "bone39", "length": 70.48, "rotation": -143.07, "x": -9.4, "y": 10.56}, {"name": "bone41", "parent": "bone40", "length": 86.28, "rotation": -3.45, "x": 70.48}, {"name": "bone42", "parent": "bone41", "length": 32.49, "rotation": -7.07, "x": 89.16, "y": -0.87}, {"name": "bone43", "parent": "bone34", "length": 16.47, "rotation": 10.15, "x": 42.4, "y": 1.19}, {"name": "bone44", "parent": "bone43", "x": 0.18, "y": -29.2}, {"name": "bone45", "parent": "bone43", "x": 26.29, "y": -26.12}, {"name": "bone46", "parent": "bone33", "x": 27.66, "y": -7.08}, {"name": "bone47", "parent": "bone33", "x": 56.76, "y": -2.9}, {"name": "bone48", "parent": "bone32", "length": 47.28, "rotation": 136.23, "x": -28.63, "y": 14.59}, {"name": "bone49", "parent": "bone48", "length": 49.29, "rotation": -76.54, "x": 47.02, "y": 0.24}, {"name": "bone50", "parent": "bone49", "length": 41.89, "rotation": 80.66, "x": 49.29}, {"name": "bone51", "parent": "bone32", "length": 58.66, "rotation": 46.83, "x": 35.5, "y": 15.11}, {"name": "bone52", "parent": "bone51", "length": 53.93, "rotation": 88.07, "x": 58.4, "y": 0.68}, {"name": "bone53", "parent": "bone52", "length": 41.7, "rotation": -89.71, "x": 50.87, "y": -2.08}, {"name": "bone54", "parent": "bone32", "x": -37.2, "y": -0.5}, {"name": "bone55", "parent": "bone32", "x": 65.14, "y": -5.48}, {"name": "bone56", "parent": "bone32", "x": 0.46, "y": 10.8}, {"name": "bone57", "parent": "bone56", "length": 17.89, "rotation": 93.29, "x": -13.4, "y": 12.35}, {"name": "bone58", "parent": "bone57", "length": 16.97, "rotation": -17.46, "x": 18.11, "y": -0.04}, {"name": "bone59", "parent": "bone58", "length": 17.71, "rotation": -24.09, "x": 16.97}, {"name": "bone60", "parent": "bone32", "length": 20.69, "rotation": 91.1, "x": 10.53, "y": 24.92}, {"name": "bone61", "parent": "bone60", "length": 13.79, "rotation": -12.95, "x": 20.69}, {"name": "bone62", "parent": "bone61", "length": 16.59, "rotation": -8.67, "x": 13.81, "y": -0.22}, {"name": "target1", "parent": "strength", "rotation": 1.71, "x": -27.73, "y": 51.37}, {"name": "target2", "parent": "target1", "x": -29.03, "y": -28.65}, {"name": "target3", "parent": "strength", "rotation": 1.71, "x": 50.38, "y": 50.69}, {"name": "target4", "parent": "target3", "x": 36.95, "y": -24.13}, {"name": "bone63", "parent": "root", "length": 12.01, "rotation": 90.39, "x": -306.63, "y": 143.61}, {"name": "bone64", "parent": "bone63", "length": 12.01, "x": 12.01}, {"name": "bone65", "parent": "bone64", "length": 12.01, "x": 12.01}, {"name": "bone66", "parent": "bone65", "length": 12.01, "x": 12.01}, {"name": "bone71", "parent": "root", "length": 45.94, "rotation": 154.84, "x": -31.28, "y": 435.47, "scaleX": 1.0625}, {"name": "bone72", "parent": "bone71", "length": 52.2, "rotation": 42.72, "x": 45.94}, {"name": "bone73", "parent": "bone72", "length": 60.34, "rotation": 22.78, "x": 52.2}, {"name": "bone74", "parent": "bone73", "length": 59.18, "rotation": 24.46, "x": 60.82, "y": -0.41}, {"name": "bone75", "parent": "bone74", "length": 45.17, "rotation": -14.71, "x": 59.18}, {"name": "bone76", "parent": "bone75", "length": 61.81, "rotation": -80.05, "x": 45.17}, {"name": "bone77", "parent": "bone76", "length": 72.04, "rotation": 25.45, "x": 61.81}, {"name": "bone78", "parent": "bone77", "length": 50.57, "rotation": 35.23, "x": 72.04}, {"name": "bone79", "parent": "root", "length": 47.23, "rotation": 17.73, "x": 47.34, "y": 427.34, "scaleX": 1.067}, {"name": "bone80", "parent": "bone79", "length": 54.73, "rotation": -38.7, "x": 47.23}, {"name": "bone81", "parent": "bone80", "length": 77.55, "rotation": -18.47, "x": 54.18, "y": -0.82}, {"name": "bone82", "parent": "bone81", "length": 64.79, "rotation": 19.38, "x": 77.55}, {"name": "bone83", "parent": "bone82", "length": 72.66, "rotation": 55.86, "x": 64.79}, {"name": "bone84", "parent": "bone83", "length": 72.07, "rotation": -28.49, "x": 74.4, "y": -0.06}, {"name": "bone85", "parent": "root", "x": -144.61, "y": 339.25}, {"name": "bone86", "parent": "root", "x": 135.46, "y": 388.86}, {"name": "bone87", "parent": "root", "x": -144.61, "y": 339.25}, {"name": "bone88", "parent": "root", "x": 135.46, "y": 388.86}, {"name": "bone89", "parent": "root", "length": 12.01, "rotation": 90.39, "x": 307.07, "y": 139.18}, {"name": "bone90", "parent": "bone89", "length": 12.01, "x": 12.01}, {"name": "bone91", "parent": "bone90", "length": 12.01, "x": 12.01}, {"name": "bone92", "parent": "bone91", "length": 12.01, "x": 12.01}], "slots": [{"name": "20250609-100149", "bone": "root", "attachment": "20250609-100149"}, {"name": "ren_bg_01", "bone": "bone86", "attachment": "ren_bg_01"}, {"name": "ren_bg_1", "bone": "bone88", "attachment": "ren_bg_01"}, {"name": "ren_bg_02", "bone": "bone85", "attachment": "ren_bg_02"}, {"name": "ren_bg_2", "bone": "bone87", "attachment": "ren_bg_02"}, {"name": "ren_bg_03", "bone": "root", "attachment": "ren_bg_03"}, {"name": "ren_bg_04", "bone": "root", "attachment": "ren_bg_04"}, {"name": "ren_bg_05", "bone": "root"}, {"name": "ren_bg_06", "bone": "bone63", "attachment": "ren_bg_06"}, {"name": "ren_bg_6", "bone": "bone89", "attachment": "ren_bg_06"}, {"name": "shitouren01", "bone": "bone7", "attachment": "shitouren01"}, {"name": "shitouren1", "bone": "bone48", "attachment": "shitouren01"}, {"name": "shitouren02", "bone": "bone10", "attachment": "shitouren02"}, {"name": "shitouren2", "bone": "bone51", "attachment": "shitouren02"}, {"name": "shitouren03", "bone": "bone21", "attachment": "shitouren03"}, {"name": "shitouren3", "bone": "bone56", "attachment": "shitouren03"}, {"name": "shitouren04", "bone": "bone16", "attachment": "shitouren04"}, {"name": "shitouren4", "bone": "bone40", "attachment": "shitouren04"}, {"name": "shitouren05", "bone": "bone2", "attachment": "shitouren05"}, {"name": "shitouren5", "bone": "bone33", "attachment": "shitouren05"}, {"name": "shitouren06", "bone": "bone6", "attachment": "shitouren06"}, {"name": "shitouren6", "bone": "bone43", "attachment": "shitouren06"}, {"name": "shitouren07", "bone": "bone13", "attachment": "shitouren07"}, {"name": "shitouren7", "bone": "bone36", "attachment": "shitouren07"}], "ik": [{"name": "ljio1", "bones": ["bone7", "bone8"], "target": "ljio1", "bendPositive": false}, {"name": "ljio2", "order": 1, "bones": ["bone9"], "target": "ljio2"}, {"name": "rjio1", "order": 2, "bones": ["bone10", "bone11"], "target": "rjio1"}, {"name": "rjio2", "order": 3, "bones": ["bone12"], "target": "rjio2"}, {"name": "target1", "order": 4, "bones": ["bone48", "bone49"], "target": "target1", "bendPositive": false}, {"name": "target2", "order": 5, "bones": ["bone50"], "target": "target2"}, {"name": "target3", "order": 6, "bones": ["bone51", "bone52"], "target": "target3"}, {"name": "target4", "order": 7, "bones": ["bone53"], "target": "target4"}], "transform": [{"name": "body", "order": 9, "bones": ["bone29"], "target": "bone28", "x": 29.1, "y": 4.18, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "face", "order": 8, "bones": ["bone31"], "target": "bone30", "x": 26.11, "y": 3.09, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}], "skins": [{"name": "default", "attachments": {"20250609-100149": {"20250609-100149": {"y": 1.16, "width": 750, "height": 1046}}, "shitouren1": {"shitouren01": {"type": "mesh", "uvs": [0.16567, 0.42802, 0.18906, 0.41071, 0.15611, 0.39093, 0.06765, 0.31029, 0.0364, 0.17812, 0.0964, 0.05874, 0.2614, 0.00843, 0.43389, 0.00843, 0.58522, 0.0717, 0.67422, 0.1672, 0.79122, 0.29954, 0.81922, 0.33501, 0.81222, 0.37594, 0.78039, 0.49641, 0.77703, 0.56181, 0.67776, 0.65644, 0.66705, 0.66303, 0.67515, 0.6723, 0.78205, 0.7627, 0.93, 0.83287, 0.99905, 0.91649, 0.95537, 0.95783, 0.7648, 0.9746, 0.54351, 0.99407, 0.34615, 0.96559, 0.12412, 0.92287, 0.03113, 0.90086, 0, 0.85166, 0.04812, 0.76475, 0.07911, 0.6841, 0.12387, 0.64809, 0.10666, 0.62382, 0.06412, 0.55226, 0.10009, 0.48029, 0.63794, 0.37078, 0.58576, 0.54114, 0.51122, 0.667, 0.58576, 0.7865, 0.71063, 0.89075, 0.20931, 0.79159, 0.41617, 0.85897, 0.43399, 0.24027, 0.28603, 0.12958], "triangles": [24, 40, 23, 23, 38, 22, 38, 40, 37, 38, 23, 40, 21, 22, 19, 40, 24, 39, 22, 38, 19, 21, 19, 20, 24, 25, 39, 39, 25, 27, 39, 27, 28, 27, 25, 26, 38, 18, 19, 38, 37, 18, 37, 40, 36, 28, 29, 39, 29, 30, 39, 40, 39, 36, 39, 30, 36, 37, 17, 18, 17, 36, 16, 17, 37, 36, 36, 35, 16, 35, 36, 31, 16, 35, 15, 36, 30, 31, 31, 32, 35, 32, 33, 35, 33, 0, 35, 35, 34, 13, 35, 0, 34, 0, 1, 34, 1, 2, 34, 12, 34, 11, 34, 10, 11, 34, 9, 10, 15, 35, 14, 14, 35, 13, 13, 34, 12, 2, 41, 34, 2, 3, 41, 34, 41, 9, 3, 42, 41, 3, 4, 42, 41, 42, 8, 41, 8, 9, 8, 42, 7, 4, 5, 42, 5, 6, 42, 42, 6, 7], "vertices": [2, 56, 27.12, -32.09, 0.23282, 57, 27, -26.69, 0.76718, 2, 56, 26.83, -29.07, 0.37352, 57, 23.99, -26.29, 0.62648, 2, 56, 23, -29.53, 0.54619, 57, 23.56, -30.13, 0.45381, 2, 56, 10.06, -28.37, 0.82845, 57, 19.51, -42.48, 0.17155, 2, 56, -4.47, -19.04, 0.97273, 57, 7.13, -54.52, 0.02727, 2, 56, -12.41, -4.82, 0.99991, 57, -8.51, -59.03, 9e-05, 1, 56, -7.55, 10.32, 1, 2, 56, 2.59, 21.62, 0.99742, 57, -30.87, -38.44, 0.00258, 2, 56, 17.56, 26.08, 0.95369, 57, -31.84, -22.85, 0.04631, 2, 56, 31.96, 23.69, 0.76849, 57, -26.24, -9.37, 0.23151, 2, 56, 51.54, 19.95, 0.34103, 57, -18.18, 8.86, 0.65897, 2, 56, 56.59, 18.73, 0.3978, 57, -15.84, 13.51, 0.6022, 3, 56, 60.11, 14.74, 0.53614, 57, -11.17, 16.04, 0.46327, 58, 6.41, 62.22, 0.00059, 3, 56, 69.81, 2.28, 0.84297, 57, 3.16, 22.66, 0.11878, 58, 15.19, 49.1, 0.03826, 3, 56, 75.89, -3.57, 0.69557, 57, 10.24, 27.26, 0.20829, 58, 20.84, 42.83, 0.09614, 3, 56, 79.15, -18.22, 0.26288, 57, 25.25, 27.12, 0.32932, 58, 23.04, 27.98, 0.40781, 3, 56, 79.15, -19.49, 0.21329, 57, 26.49, 26.84, 0.30781, 58, 22.95, 26.71, 0.4789, 3, 56, 80.52, -19.76, 0.171, 57, 27.06, 28.11, 0.26713, 58, 24.29, 26.35, 0.56187, 3, 56, 95.48, -20.55, 0.02769, 57, 31.21, 42.5, 0.04262, 58, 39.16, 24.5, 0.92969, 3, 56, 110.91, -16.9, 0.00245, 57, 31.14, 58.36, 0.00107, 58, 54.81, 27.03, 0.99648, 2, 56, 123, -19.58, 3e-05, 58, 66.68, 23.5, 0.99997, 1, 58, 67.62, 16.99, 1, 1, 58, 57.06, 3.78, 1, 1, 58, 44.8, -11.55, 1, 1, 58, 29.76, -20.98, 1, 1, 58, 11.88, -30.59, 1, 1, 58, 4.02, -34.24, 1, 2, 57, 80.11, -7.26, 0.00191, 58, -2.36, -31.58, 0.99809, 2, 57, 68.5, -10.23, 0.08166, 58, -7.1, -20.57, 0.91834, 2, 57, 58.42, -13.97, 0.39218, 58, -12.37, -11.19, 0.60782, 3, 56, 45.8, -53.79, 0.00077, 57, 52.35, -13.41, 0.72477, 58, -12.77, -5.11, 0.27446, 3, 56, 42.45, -52.83, 0.00397, 57, 50.66, -16.45, 0.88457, 58, -16.03, -3.91, 0.11146, 3, 56, 33.08, -49.45, 0.02413, 57, 45.25, -24.81, 0.97355, 58, -25.14, 0.12, 0.00233, 2, 56, 28.29, -40.89, 0.07827, 57, 35.83, -27.55, 0.92173, 3, 56, 49.38, 3.77, 0.25476, 57, -2.91, 3.1, 0.74513, 58, -5.09, 52.05, 0.0001, 3, 56, 62.67, -14.32, 0.30969, 57, 17.72, 11.95, 0.61431, 58, 6.88, 33.05, 0.076, 3, 56, 70.37, -30.04, 0.07485, 57, 34.78, 15.9, 0.45723, 58, 13.44, 16.82, 0.46792, 3, 56, 86.23, -35.45, 0.02389, 57, 43.64, 30.12, 0.05779, 58, 28.87, 10.29, 0.91832, 3, 56, 103.58, -36.26, 0.00265, 57, 48.34, 46.84, 0.0022, 58, 46.12, 8.25, 0.99515, 2, 57, 63.19, 3.37, 0.00585, 58, 5.5, -13.2, 0.99415, 1, 58, 24.63, -6.81, 1, 1, 56, 24.86, 1.65, 1, 1, 56, 5.53, 1.5, 1], "hull": 34}}, "ren_bg_01": {"ren_bg_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [239.47, -1.2, -0.53, -1.2, -0.53, 136.8, 239.47, 136.8], "hull": 4}}, "ren_bg_02": {"ren_bg_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.65, -1.56, -230.35, -1.56, -230.35, 186.44, 5.65, 186.44], "hull": 4}}, "shitouren5": {"shitouren05": {"type": "mesh", "uvs": [0.91492, 0.20614, 1, 0.42477, 1, 0.6301, 0.89527, 0.82212, 0.78091, 0.92478, 0.74874, 0.99892, 0.60758, 1, 0.37886, 0.98752, 0.16979, 0.89626, 0.02863, 0.74037, 0, 0.52363, 0, 0.32021, 0.16979, 0.12439, 0.32883, 0.02553, 0.5236, 0, 0.70943, 0.05785, 0.72771, 0.77774, 0.50822, 0.7762, 0.34106, 0.61381, 0.14338, 0.4437, 0.9312, 0.5767, 0.78294, 0.56587, 0.52857, 0.46999, 0.82219, 0.34627, 0.51985, 0.22255, 0.25821, 0.28286, 0.15501, 0.71124, 0.2553, 0.76073], "triangles": [20, 1, 2, 25, 12, 13, 11, 12, 25, 19, 11, 25, 10, 11, 19, 18, 25, 22, 19, 25, 18, 10, 19, 26, 1, 20, 23, 25, 24, 22, 24, 13, 14, 23, 15, 0, 24, 15, 23, 23, 0, 1, 22, 24, 23, 21, 22, 23, 20, 21, 23, 17, 22, 21, 16, 17, 21, 3, 21, 20, 3, 20, 2, 16, 21, 3, 4, 16, 3, 24, 14, 15, 25, 13, 24, 26, 19, 18, 27, 26, 18, 8, 26, 27, 17, 18, 22, 27, 18, 17, 7, 27, 17, 8, 27, 7, 4, 6, 16, 6, 17, 16, 5, 6, 4, 7, 17, 6, 9, 26, 8, 9, 10, 26], "vertices": [3, 42, 103.15, 2.79, 0.5213, 47, 29.34, 34.4, 0.1787, 55, 83.5, 39.85, 0.3, 2, 47, 1.2, 4.99, 0.7, 55, 66.75, 2.75, 0.3, 3, 42, 42.69, -40.86, 0.5544, 47, -31.12, -9.24, 0.1456, 55, 41.01, -21.43, 0.3, 3, 41, 60.58, -32.97, 0.12489, 42, 4.74, -36.63, 0.57511, 55, 3.82, -30.07, 0.3, 3, 41, 33.38, -29.8, 0.57282, 42, -19.86, -24.6, 0.12718, 55, -23.38, -26.91, 0.3, 3, 41, 20.05, -34.24, 0.6771, 42, -33.9, -24.35, 0.0229, 55, -36.71, -31.35, 0.3, 3, 41, 2.23, -15.54, 0.56, 54, -25.43, -8.46, 0.14, 55, -54.53, -12.65, 0.3, 3, 41, -24.86, 16.43, 0.56, 54, -52.52, 23.51, 0.14, 55, -81.62, 19.33, 0.3, 3, 41, -39.62, 55.07, 0.63, 54, -67.28, 62.15, 0.07, 55, -96.38, 57.96, 0.3, 4, 41, -37.76, 92.25, 0.59204, 43, -75.29, 5.49, 0.07296, 54, -65.42, 99.33, 0.035, 55, -94.52, 95.15, 0.3, 4, 41, -14.18, 121.6, 0.09476, 43, -43.29, 25.31, 0.57024, 54, -41.84, 128.68, 0.035, 55, -70.94, 124.49, 0.3, 4, 41, 11.33, 145.55, 0.00953, 43, -11.27, 39.42, 0.65547, 54, -16.34, 152.63, 0.035, 55, -45.44, 148.45, 0.3, 4, 42, 61.05, 133.24, 0.13355, 43, 32.08, 24.56, 0.49645, 54, 29.49, 153.04, 0.07, 55, 0.39, 148.86, 0.3, 3, 42, 88.34, 113.46, 0.5768, 43, 59.37, 4.78, 0.1232, 55, 32.71, 139.29, 0.3, 2, 42, 106.73, 82.61, 0.7, 55, 60.31, 116.32, 0.3, 3, 42, 111.33, 47.48, 0.56, 54, 105.44, 88.9, 0.14, 55, 76.34, 84.72, 0.3, 3, 41, 45.15, -5.39, 0.36293, 42, -0.64, -5.49, 0.43707, 54, 17.49, 1.69, 0.2, 3, 41, 17.84, 24.07, 0.74749, 42, -16.58, 31.37, 0.05251, 54, -9.82, 31.15, 0.2, 4, 41, 17.25, 65.48, 0.64207, 42, -3.35, 70.63, 0.14062, 43, -32.33, -38.06, 0.11731, 54, -10.41, 72.56, 0.1, 4, 41, 13.81, 111.88, 0.07088, 42, 8.84, 115.53, 0.00082, 43, -20.13, 6.84, 0.8783, 54, -13.85, 118.96, 0.05, 2, 42, 46.02, -25.63, 0.576, 47, -27.79, 5.98, 0.424, 2, 42, 36.79, -0.05, 0.8, 54, 50.97, 19.27, 0.2, 4, 41, 58.78, 57.41, 0.17625, 42, 33.12, 49.2, 0.56534, 43, 4.15, -59.49, 0.05841, 54, 31.12, 64.49, 0.2, 3, 42, 74.25, 8.6, 0.69581, 47, 0.44, 40.21, 0.10419, 54, 83.41, 39.9, 0.2, 4, 41, 88.7, 87.72, 0.0139, 42, 71.42, 67.81, 0.71056, 43, 42.45, -40.87, 0.07553, 54, 61.04, 94.8, 0.2, 4, 41, 48.36, 115.51, 0.00093, 42, 42.63, 107.45, 0.04086, 43, 13.66, -1.24, 0.85822, 54, 20.7, 122.59, 0.1, 4, 41, -18.27, 78.83, 0.85427, 42, -32.42, 95.03, 0.00453, 43, -61.39, -13.65, 0.0912, 54, -45.93, 85.91, 0.05, 2, 41, -11.91, 59.62, 0.98392, 42, -32.81, 74.8, 0.01608], "hull": 16}}, "ren_bg_04": {"ren_bg_04": {"type": "mesh", "uvs": [0.87205, 0, 0.94951, 0, 0.99999, 0.09014, 1, 0.09332, 1, 0.17768, 0.983, 0.20011, 0.90906, 0.10007, 0.87347, 0.0929, 0.82939, 0.08402, 0.75551, 0.12846, 0.72066, 0.17483, 0.67864, 0.27818, 0.6353, 0.38475, 0.61181, 0.46928, 0.56347, 0.68609, 0.51199, 0.91698, 0.45805, 1, 0.41469, 1, 0.37075, 1, 0.3311, 0.91021, 0.30602, 0.83233, 0.26088, 0.79105, 0.20857, 0.7432, 0.15254, 0.76451, 0.09125, 0.82689, 0.05729, 0.89003, 0.00822, 1, 0, 1, 0, 0.85852, 0, 0.72122, 0.05465, 0.65892, 0.10242, 0.62561, 0.21348, 0.62527, 0.29388, 0.67123, 0.3327, 0.73877, 0.38319, 0.82663, 0.41553, 0.85729, 0.45758, 0.82598, 0.49335, 0.74753, 0.51262, 0.61627, 0.53927, 0.43473, 0.60386, 0.26141, 0.65509, 0.18632, 0.68991, 0.13529, 0.72832, 0.07899, 0.80157, 0, 0.985, 0.14152, 0.92567, 0.04985, 0.874, 0.03839, 0.81786, 0.04412, 0.74003, 0.10571, 0.70048, 0.16443, 0.62202, 0.31195, 0.57927, 0.45661, 0.66484, 0.23144, 0.541, 0.658, 0.50464, 0.82844, 0.45296, 0.91581, 0.41533, 0.94016, 0.37131, 0.88716, 0.31772, 0.78261, 0.27052, 0.72102, 0.21119, 0.69238, 0.13292, 0.69811, 0.07359, 0.74394, 0.02894, 0.81125], "triangles": [64, 30, 31, 65, 29, 30, 64, 65, 30, 65, 64, 24, 28, 29, 65, 25, 65, 24, 28, 26, 27, 25, 28, 65, 25, 26, 28, 32, 63, 31, 61, 62, 32, 62, 63, 32, 33, 61, 32, 62, 23, 63, 22, 62, 61, 64, 31, 63, 22, 23, 62, 61, 33, 60, 21, 22, 61, 21, 61, 60, 24, 64, 63, 24, 63, 23, 60, 33, 34, 60, 34, 35, 20, 21, 60, 59, 60, 35, 19, 20, 60, 59, 19, 60, 36, 59, 35, 58, 36, 57, 58, 59, 36, 18, 19, 59, 18, 59, 58, 17, 18, 58, 17, 58, 16, 55, 56, 38, 37, 38, 56, 57, 36, 37, 57, 37, 56, 15, 56, 14, 57, 56, 15, 16, 57, 15, 58, 57, 16, 53, 40, 41, 13, 53, 12, 53, 39, 40, 55, 39, 53, 14, 55, 53, 13, 14, 53, 38, 39, 55, 55, 14, 56, 51, 43, 44, 10, 51, 50, 51, 54, 42, 51, 42, 43, 41, 42, 54, 11, 54, 51, 11, 51, 10, 52, 41, 54, 52, 54, 11, 12, 52, 11, 52, 53, 41, 12, 53, 52, 49, 45, 0, 49, 0, 48, 8, 49, 48, 7, 8, 48, 50, 44, 45, 50, 45, 49, 9, 50, 49, 9, 49, 8, 51, 44, 50, 10, 50, 9, 47, 48, 0, 1, 47, 0, 6, 7, 48, 47, 6, 48, 46, 1, 2, 46, 2, 3, 47, 1, 46, 46, 3, 4, 46, 6, 47, 5, 46, 4, 5, 6, 46], "vertices": [3, 79, 46.91, -1.73, 0.48226, 80, -0.46, -1.93, 0.51774, 84, -57.91, -200.74, 0, 1, 79, 22.51, -13.19, 1, 1, 79, 0.67, -8.01, 1, 1, 79, 0.46, -7.57, 1, 2, 79, -5.1, 4.27, 1, 84, -110.24, -199.11, 0, 2, 79, -1.22, 9.93, 1, 84, -106.85, -193.15, 0, 2, 79, 28.66, 6.84, 0.99655, 80, -8.06, 16.75, 0.00345, 2, 79, 40.34, 11.1, 0.58048, 80, 3.42, 11.95, 0.41952, 4, 79, 54.81, 16.37, 0.00334, 80, 17.62, 6.01, 0.99665, 81, -29.55, 18.93, 0, 84, -51.55, -182.04, 0, 1, 80, 44.22, 4.82, 1, 2, 80, 57.95, 8.01, 0.02209, 81, 8.4, 5.16, 0.97791, 1, 81, 29.92, 7.91, 1, 2, 81, 52.1, 10.73, 0.99602, 82, -3.32, 13.75, 0.00398, 2, 81, 66.81, 15.43, 0.22635, 82, 12.01, 11.93, 0.77365, 2, 82, 49.59, 11.02, 0.99181, 83, -12.08, 8.22, 0.00819, 1, 83, 26.86, 17.44, 1, 2, 83, 48.78, 11.3, 0.63305, 84, -10.5, 5.5, 0.36695, 2, 83, 58.46, -0.28, 0.04354, 84, 2.57, 13.04, 0.95646, 1, 84, 15.81, 20.67, 1, 1, 84, 34.72, 15.51, 1, 2, 84, 48.31, 9.41, 0.98811, 85, -8.15, 14.3, 0.01189, 2, 84, 65.12, 11.71, 0.11712, 85, 8.02, 9.16, 0.88288, 1, 85, 26.75, 3.2, 1, 2, 85, 45.93, 8.02, 0.99993, 86, -16.71, 21.62, 7e-05, 2, 85, 66.43, 19.34, 0.40172, 86, 6.57, 19.04, 0.59828, 2, 85, 77.44, 30.03, 0.03668, 86, 21.73, 21.41, 0.96332, 2, 85, 93.12, 48.36, 0, 86, 45.12, 27.34, 1, 2, 85, 95.97, 48.59, 0, 86, 47.58, 25.88, 1, 1, 86, 36.37, 7.03, 1, 2, 83, 117.9, -138.7, 0, 86, 25.5, -11.27, 1, 3, 83, 98.29, -130.31, 0, 85, 81.17, -5.61, 0.07462, 86, 4.22, -9.85, 0.92538, 3, 83, 83.66, -120.86, 0, 85, 65.01, -12.07, 0.98283, 86, -12.71, -5.8, 0.01717, 1, 85, 26.48, -15.16, 1, 2, 84, 64.44, -10.11, 0.39352, 85, -1.97, -10.26, 0.60648, 2, 83, 45.7, -48.14, 0, 84, 47.51, -7.79, 1, 1, 84, 25.48, -4.77, 1, 2, 83, 41.3, -14.24, 0.15492, 84, 13.36, -6.27, 0.84508, 2, 83, 28.19, -6.13, 0.97421, 84, 3.11, -17.79, 0.02579, 3, 82, 68.59, -7, 0.01261, 83, 10.88, -4.39, 0.98739, 84, -1.6, -34.54, 0, 2, 82, 47.33, -9.6, 1, 84, 2.75, -55.51, 0, 2, 82, 17.92, -13.19, 1, 84, 8.77, -84.52, 0, 2, 81, 48.07, -10.92, 0.8038, 82, -15.96, -4.29, 0.1962, 2, 81, 26.94, -8.25, 1, 84, -6.92, -138.01, 0, 2, 81, 12.59, -6.44, 1, 84, -13.47, -150.91, 0, 2, 80, 50.92, -5.35, 0.79327, 81, -3.25, -4.43, 0.20673, 1, 80, 22.93, -9.33, 1, 2, 79, 2.01, 1.42, 1, 84, -102.92, -201.36, 0, 1, 79, 26.74, -2.67, 1, 2, 79, 43.77, 3.37, 0.60921, 80, 0.69, 3.95, 0.39079, 1, 80, 19.58, -1.1, 1, 2, 80, 48.29, -0.17, 0.96471, 81, -3.68, 1.36, 0.03529, 2, 81, 12.71, -0.61, 1, 84, -18.91, -148.84, 0, 2, 81, 48.32, -0.86, 0.98281, 82, -11.56, 4.76, 0.01719, 2, 81, 74.17, 6.6, 0.0072, 82, 15.06, 0.85, 0.9928, 2, 81, 28.89, -0.72, 1, 84, -13.35, -133.64, 0, 1, 82, 48.98, 2.09, 1, 1, 83, 17.98, 6.67, 1, 1, 83, 39.9, 1.57, 1, 1, 84, 7.01, 4.89, 1, 1, 84, 24.38, 5.43, 1, 2, 84, 48.63, 0.7, 0.99956, 85, -11.6, 6.29, 0.00044, 1, 85, 5.53, -1.93, 1, 1, 85, 26.46, -4.73, 1, 2, 83, 85.47, -105.51, 0, 85, 53.54, -1.7, 1, 2, 85, 73.57, 7.01, 0.16458, 86, 5.28, 4.84, 0.83542, 2, 85, 88.24, 18.63, 0.0041, 86, 23.97, 5.88, 0.9959], "hull": 46}}, "ren_bg_1": {"ren_bg_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [239.47, -1.2, -0.53, -1.2, -0.53, 136.8, 239.47, 136.8], "hull": 4}}, "ren_bg_2": {"ren_bg_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.65, -1.56, -230.35, -1.56, -230.35, 186.44, 5.65, 186.44], "hull": 4}}, "shitouren2": {"shitouren02": {"type": "mesh", "uvs": [0.76267, 0.46553, 0.75012, 0.44799, 0.78883, 0.43957, 0.89699, 0.38199, 0.98472, 0.2718, 0.98832, 0.12984, 0.78883, 0.02659, 0.5605, 0.01865, 0.41509, 0.05042, 0.33573, 0.22102, 0.25482, 0.35031, 0.23888, 0.37579, 0.23729, 0.42563, 0.24999, 0.57777, 0.29762, 0.65647, 0.3135, 0.67746, 0.27698, 0.70106, 0.05788, 0.8296, 0.00231, 0.91485, 0.17696, 0.96601, 0.4294, 0.9883, 0.64375, 0.92797, 0.78029, 0.85714, 0.76124, 0.74566, 0.73915, 0.69011, 0.72631, 0.65778, 0.74215, 0.62142, 0.76917, 0.55941, 0.58735, 0.23615, 0.45239, 0.40665, 0.50796, 0.6611, 0.52384, 0.77521, 0.26028, 0.85391, 0.48774, 0.56852], "triangles": [19, 32, 20, 20, 31, 21, 20, 32, 31, 18, 17, 19, 19, 17, 32, 22, 21, 23, 21, 31, 23, 32, 16, 31, 32, 17, 16, 16, 30, 31, 31, 30, 24, 16, 15, 30, 15, 14, 30, 31, 24, 23, 30, 25, 24, 14, 33, 30, 25, 30, 26, 26, 30, 33, 14, 13, 33, 26, 33, 27, 13, 29, 33, 13, 12, 29, 33, 0, 27, 33, 29, 0, 29, 1, 0, 1, 29, 2, 29, 28, 2, 2, 28, 3, 12, 11, 29, 11, 10, 29, 10, 9, 29, 29, 9, 28, 3, 28, 4, 28, 6, 4, 4, 6, 5, 9, 8, 28, 8, 7, 28, 28, 7, 6], "vertices": [2, 59, 31.02, 36.55, 0.3624, 60, 35.54, 27.8, 0.6376, 2, 59, 30.57, 33.78, 0.47777, 60, 32.76, 28.22, 0.52223, 2, 59, 26.44, 35.71, 0.63811, 60, 34.64, 32.38, 0.36189, 2, 59, 11.88, 37.5, 0.85689, 60, 36.26, 46.96, 0.14311, 2, 59, -5.54, 32.22, 0.9631, 60, 30.77, 64.31, 0.0369, 2, 59, -18.4, 17.43, 0.99792, 60, 15.83, 76.99, 0.00208, 1, 59, -10.05, -8.07, 1, 1, 59, 9.25, -25.58, 1, 1, 59, 24.79, -32.82, 1, 1, 59, 46.81, -20.53, 1, 2, 59, 65.32, -12.73, 0.99991, 61, 5.78, -64.12, 9e-05, 3, 59, 68.97, -11.19, 0.99031, 60, -11.74, -10.71, 0.00857, 61, 9.39, -62.5, 0.00112, 3, 59, 73.51, -6.02, 0.81886, 60, -6.52, -15.19, 0.17143, 61, 13.81, -57.22, 0.00972, 3, 59, 85.84, 11.04, 0.10281, 60, 10.69, -27.31, 0.72454, 61, 25.72, -39.87, 0.17264, 3, 59, 88.62, 22.85, 0.0158, 60, 22.54, -29.95, 0.5372, 61, 28.22, -27.99, 0.447, 3, 59, 89.08, 26.24, 0.00516, 60, 25.93, -30.38, 0.38738, 61, 28.6, -24.6, 0.60746, 3, 59, 94.37, 26.08, 0.00058, 60, 25.83, -35.66, 0.19862, 61, 33.88, -24.63, 0.8008, 2, 60, 23.83, -66.23, 0.00073, 61, 64.48, -26.26, 0.99927, 1, 61, 76.75, -20.97, 1, 1, 61, 65.54, -3.07, 1, 2, 60, 67.54, -47.19, 7e-05, 61, 44.9, 17.22, 0.99993, 2, 60, 76.5, -22.98, 0.1157, 61, 20.59, 25.89, 0.8843, 2, 60, 78.74, -4.74, 0.39938, 61, 2.32, 27.9, 0.60062, 2, 60, 65.43, 3.29, 0.73363, 61, -5.54, 14.5, 0.26637, 3, 59, 52.92, 58.64, 0.00084, 60, 57.9, 6.17, 0.984, 61, -8.33, 6.93, 0.01516, 2, 59, 51.19, 54.28, 0.00737, 60, 53.51, 7.85, 0.99263, 2, 59, 46.59, 51.58, 0.03227, 60, 50.76, 12.42, 0.96773, 2, 59, 38.75, 46.98, 0.11042, 60, 46.06, 20.2, 0.88958, 1, 59, 26.11, -0.56, 1, 2, 59, 52.99, 7.67, 0.35402, 60, 6.92, 5.49, 0.64598, 2, 60, 38.16, -11.75, 0.5055, 61, 9.83, -12.59, 0.4945, 1, 61, 18.2, 0.87, 1, 2, 60, 40.99, -50.45, 0.00906, 61, 48.48, -9.29, 0.99094, 2, 60, 26.79, -5.48, 0.93245, 61, 3.69, -24.03, 0.06755], "hull": 28}}, "shitouren3": {"shitouren03": {"type": "mesh", "uvs": [0.00767, 0.05234, 0.07959, 0.00316, 0.15859, 0.06129, 0.2989, 0.12091, 0.40502, 0.16861, 0.41917, 0.19097, 0.50878, 0.19544, 0.55359, 0.21333, 0.69272, 0.18202, 0.79169, 0.13322, 0.85898, 0.10004, 0.87902, 0.17457, 0.87666, 0.26251, 0.83343, 0.30233, 0.79413, 0.33853, 0.71513, 0.37431, 0.70598, 0.38774, 0.70797, 0.49689, 0.71661, 0.64887, 0.71328, 0.72863, 0.68273, 0.80504, 0.65019, 0.76809, 0.61764, 0.87808, 0.56783, 0.9293, 0.51137, 0.99059, 0.48149, 0.92426, 0.47617, 0.87976, 0.44695, 0.93098, 0.40555, 0.85763, 0.37504, 0.75094, 0.37958, 0.67543, 0.40945, 0.62782, 0.37068, 0.58685, 0.34507, 0.50738, 0.34818, 0.4024, 0.36292, 0.35236, 0.28463, 0.3273, 0.22158, 0.30712, 0.10819, 0.26694, 0.02765, 0.18924, 0.00328, 0.1035, 0.07188, 0.10497, 0.15633, 0.16531, 0.27504, 0.21637, 0.39254, 0.26897, 0.55409, 0.28754, 0.48479, 0.27958, 0.69728, 0.27207, 0.81355, 0.22256, 0.47943, 0.38501, 0.42191, 0.36799, 0.55164, 0.37418, 0.63364, 0.38346, 0.54797, 0.49022, 0.54307, 0.63565, 0.5443, 0.80894, 0.53206, 0.91414, 0.44883, 0.4469, 0.45373, 0.53045, 0.47576, 0.6372, 0.45863, 0.75324, 0.43782, 0.83678, 0.63854, 0.50105, 0.63976, 0.63565], "triangles": [55, 60, 54, 61, 29, 60, 61, 60, 55, 28, 29, 61, 26, 61, 55, 27, 61, 26, 28, 61, 27, 21, 55, 54, 22, 55, 21, 56, 26, 55, 23, 56, 55, 25, 26, 56, 22, 23, 55, 24, 25, 56, 24, 56, 23, 54, 62, 63, 63, 17, 18, 19, 63, 18, 21, 63, 19, 20, 21, 19, 21, 54, 63, 59, 31, 58, 54, 59, 58, 60, 31, 59, 30, 31, 60, 29, 30, 60, 60, 59, 54, 57, 49, 53, 58, 57, 53, 34, 50, 57, 58, 33, 57, 57, 33, 34, 58, 32, 33, 31, 32, 58, 54, 58, 53, 62, 52, 16, 62, 16, 17, 53, 52, 62, 54, 53, 62, 63, 62, 17, 52, 47, 15, 9, 10, 11, 48, 9, 11, 8, 9, 48, 12, 48, 11, 47, 8, 48, 13, 48, 12, 14, 47, 48, 13, 14, 48, 15, 47, 14, 16, 52, 15, 57, 50, 49, 53, 51, 52, 49, 50, 46, 50, 34, 35, 44, 43, 4, 44, 4, 5, 7, 8, 47, 46, 5, 6, 44, 5, 46, 45, 7, 47, 46, 6, 7, 45, 46, 7, 50, 44, 46, 51, 46, 45, 52, 45, 47, 51, 45, 52, 51, 49, 46, 35, 36, 44, 35, 44, 50, 53, 49, 51, 41, 0, 1, 41, 1, 2, 40, 0, 41, 42, 41, 2, 42, 2, 3, 39, 40, 41, 43, 42, 3, 43, 3, 4, 42, 39, 41, 38, 39, 42, 37, 42, 43, 38, 42, 37, 36, 43, 44, 37, 43, 36], "vertices": [1, 63, 7.69, -4.43, 1, 2, 63, -1.53, -10.35, 0.99801, 64, 63.14, -26.63, 0.00199, 2, 63, -12.55, -5, 0.94031, 64, 52.12, -21.28, 0.05969, 2, 63, -31.77, -0.11, 0.64324, 64, 32.9, -16.39, 0.35676, 2, 63, -46.33, 3.87, 0.30805, 64, 18.34, -12.42, 0.69195, 2, 63, -48.4, 6.09, 0.23041, 64, 16.27, -10.2, 0.76959, 3, 63, -60.41, 5.66, 0.03001, 64, 4.26, -10.62, 0.96606, 62, 41.93, 0.67, 0.00393, 3, 63, -66.54, 7.1, 0.00429, 64, -1.87, -9.18, 0.9517, 62, 35.8, 2.11, 0.04401, 2, 64, -20.21, -13.89, 0.33163, 62, 17.46, -2.6, 0.66837, 2, 64, -33.04, -20.05, 0.02051, 62, 4.62, -8.75, 0.97949, 2, 62, -4.11, -12.93, 1, 65, -27.5, 37.24, 0, 2, 62, -7.38, -5.26, 1, 65, -19.23, 38.3, 0, 2, 62, -7.76, 4.06, 0.99772, 65, -10.15, 36.14, 0.00228, 2, 64, -39.97, -2.59, 0.01265, 62, -2.3, 8.7, 0.98735, 2, 64, -35, 1.63, 0.05838, 62, 2.66, 12.93, 0.94162, 2, 64, -24.73, 6.21, 0.23856, 62, 12.93, 17.5, 0.76144, 3, 64, -23.62, 7.72, 0.13683, 62, 14.05, 19.01, 0.38176, 65, -1.68, 11.09, 0.48141, 3, 62, 12.91, 30.53, 0.00487, 65, 9.71, 9.06, 0.99422, 66, -10.74, 6.16, 0.00091, 2, 65, 25.73, 6.99, 0.06677, 66, 5.16, 9, 0.93323, 2, 66, 13.61, 9.44, 0.95427, 67, -6.92, 7.25, 0.04573, 2, 66, 22.09, 6.22, 0.6464, 67, 2.14, 7.77, 0.3536, 2, 66, 18.66, 1.47, 0.30318, 67, 0.94, 2.03, 0.69682, 1, 67, 13.22, 4.11, 1, 2, 67, 21.26, 1.06, 0.95044, 70, 16.06, 17.17, 0.04956, 2, 67, 30.68, -2.23, 0.80945, 70, 24.03, 11.18, 0.19055, 2, 67, 26.6, -9.22, 0.61868, 70, 18.02, 5.76, 0.38132, 2, 67, 22.88, -12.2, 0.22292, 70, 13.56, 4.05, 0.77708, 2, 67, 29.54, -12.87, 0.00057, 70, 19.7, 1.39, 0.99943, 2, 69, 26.09, -7.85, 0.009, 70, 13.3, -5.69, 0.991, 3, 68, 32.54, -15.72, 1e-05, 69, 15.07, -12.66, 0.45865, 70, 3.13, -12.11, 0.54134, 3, 68, 24.74, -13.83, 0.0612, 69, 7.04, -12.57, 0.76444, 70, -4.82, -13.23, 0.17436, 3, 68, 20.41, -9.07, 0.44525, 69, 1.75, -8.9, 0.53531, 70, -10.61, -10.4, 0.01944, 2, 68, 15.28, -13.5, 0.87577, 69, -2.25, -14.36, 0.12423, 3, 63, -41.02, 40.28, 0.00228, 68, 6.42, -15.53, 0.9885, 69, -10.43, -18.33, 0.00921, 3, 63, -40.6, 29.15, 0.20504, 64, 24.08, 12.87, 0.33196, 68, -4.5, -13.33, 0.46299, 2, 63, -42.17, 23.71, 0.36238, 64, 22.51, 7.43, 0.63762, 2, 63, -31.51, 21.85, 0.59225, 64, 33.17, 5.57, 0.40775, 2, 63, -22.92, 20.35, 0.7399, 64, 41.75, 4.07, 0.2601, 2, 63, -7.45, 17.24, 0.92231, 64, 57.22, 0.96, 0.07769, 2, 63, 3.93, 9.84, 0.99137, 64, 68.6, -6.44, 0.00863, 1, 63, 7.87, 1.02, 1, 3, 63, -1.31, 0.49, 0.99259, 64, 63.36, -15.79, 0.00358, 68, -41.57, -44.79, 0.00383, 2, 63, -13.07, 6.02, 0.9039, 64, 51.6, -10.26, 0.0961, 2, 63, -29.34, 10.22, 0.6545, 64, 35.33, -6.06, 0.3455, 2, 63, -45.46, 14.6, 0.27424, 64, 19.21, -1.68, 0.72576, 2, 64, -2.52, -1.34, 0.97753, 62, 35.14, 9.95, 0.02247, 2, 63, -57.87, 14.79, 0.03782, 64, 6.8, -1.49, 0.96218, 3, 64, -21.53, -4.42, 0.23967, 62, 16.13, 6.88, 0.4433, 65, -13.93, 12.38, 0.31704, 3, 64, -36.68, -10.82, 0.00563, 62, 0.99, 0.47, 0.98411, 65, -15.98, 28.7, 0.01027, 3, 63, -58, 25.99, 0.01885, 64, 6.68, 9.71, 0.97315, 68, -3.49, 4.33, 0.008, 2, 63, -50.18, 24.77, 0.17785, 64, 14.5, 8.49, 0.82215, 2, 64, -2.88, 7.84, 0.99497, 62, 34.78, 19.13, 0.00503, 3, 64, -13.92, 7.99, 0.70499, 62, 23.75, 19.29, 0.27809, 68, -0.32, 24.75, 0.01692, 5, 64, -3.32, 20.14, 0.03355, 65, 4.76, -11.82, 0.4554, 68, 9, 11.6, 0.50119, 69, -14, 8.68, 0.00228, 66, -9.2, -15.23, 0.00758, 5, 65, 19.74, -15.52, 0.10492, 68, 24.11, 8.47, 0.20422, 69, 1.43, 9.02, 0.43053, 66, 6.2, -14.27, 0.23848, 67, -4, -17.43, 0.02185, 4, 69, 19.75, 10.37, 0.07338, 66, 24.45, -12.19, 0.01735, 67, 11.8, -8.07, 0.60355, 70, 4.28, 11.36, 0.30572, 2, 67, 22.28, -3.9, 0.80178, 70, 15.52, 12.15, 0.19822, 2, 63, -54.4, 32.84, 0.00036, 68, 2.33, -0.77, 0.99964, 2, 68, 11.17, -1.55, 0.99716, 69, -8.93, -3.64, 0.00284, 4, 65, 18.11, -24.39, 0.00018, 69, 2.17, 0.03, 0.99932, 66, 7.3, -23.23, 0.00045, 67, 0.66, -25.15, 5e-05, 2, 69, 14.59, -1.47, 0.25486, 70, 0.97, -1.11, 0.74514, 2, 69, 23.61, -3.68, 0.00448, 70, 10.21, -1.94, 0.99552, 3, 65, 8.3, -0.15, 0.99869, 68, 12.08, 23.39, 0.00124, 69, -13.63, 20.87, 7e-05, 5, 65, 22.31, -2.82, 0.03008, 68, 26.19, 21.26, 0.0075, 69, 0.59, 21.95, 0.01286, 66, 4.84, -1.39, 0.94939, 67, -10.5, -6.22, 0.00018], "hull": 41}}, "shitouren4": {"shitouren04": {"type": "mesh", "uvs": [0.32564, 0, 0.53415, 0.00689, 0.6472, 0.06029, 0.71252, 0.16838, 0.6472, 0.28282, 0.70247, 0.33242, 0.66981, 0.40108, 0.80798, 0.48247, 0.80507, 0.59756, 0.8019, 0.60719, 0.859, 0.61626, 1, 0.70946, 0.87839, 0.80695, 0.83599, 0.81738, 0.8372, 0.83087, 0.8568, 0.90361, 0.78607, 0.97701, 0.62457, 0.9967, 0.45136, 0.98247, 0.31566, 0.92611, 0.31392, 0.88031, 0.34523, 0.82923, 0.35393, 0.81162, 0.3348, 0.7579, 0.23389, 0.71035, 0.29304, 0.69361, 0.27216, 0.65486, 0.27738, 0.63901, 0.19387, 0.55359, 0.21671, 0.45901, 0.10725, 0.36104, 0, 0.25442, 0.00606, 0.12899, 0.14236, 0.02655, 0.36331, 0.10181, 0.3819, 0.21366, 0.41494, 0.31923, 0.46863, 0.42689, 0.5471, 0.59622, 0.55123, 0.61399, 0.5946, 0.70702, 0.61318, 0.79378, 0.62144, 0.81468, 0.5946, 0.89726], "triangles": [18, 43, 17, 17, 43, 16, 18, 19, 43, 16, 43, 15, 14, 43, 42, 43, 14, 15, 19, 20, 43, 20, 21, 43, 43, 21, 42, 42, 13, 14, 42, 21, 22, 28, 29, 37, 7, 37, 6, 37, 29, 36, 29, 30, 36, 37, 36, 6, 6, 36, 5, 5, 36, 4, 30, 31, 35, 30, 35, 36, 35, 32, 34, 32, 35, 31, 36, 35, 4, 4, 35, 3, 32, 33, 34, 3, 34, 2, 3, 35, 34, 33, 0, 34, 34, 1, 2, 34, 0, 1, 42, 22, 41, 40, 12, 13, 13, 42, 41, 22, 23, 41, 11, 12, 40, 23, 40, 41, 40, 13, 41, 23, 24, 40, 24, 25, 40, 11, 40, 10, 40, 9, 10, 25, 39, 40, 40, 39, 9, 25, 26, 39, 26, 27, 39, 27, 38, 39, 27, 28, 38, 39, 38, 9, 9, 38, 8, 8, 38, 7, 38, 28, 37, 7, 38, 37], "vertices": [1, 48, -40.26, 5.74, 1, 1, 48, -32.8, 30.33, 1, 1, 48, -17, 40.92, 1, 1, 48, 10.41, 42.76, 1, 1, 48, 35.66, 28.61, 1, 2, 49, -23.46, 31.13, 0.0235, 48, 48.94, 32.49, 0.9765, 2, 49, -7.69, 24.36, 0.31704, 48, 64.27, 24.78, 0.68296, 2, 49, 14.67, 37.78, 0.9277, 48, 87.4, 36.83, 0.0723, 2, 49, 42.18, 32.71, 0.99997, 50, -50.76, 27.54, 3e-05, 1, 49, 44.42, 31.93, 1, 1, 49, 47.78, 38.48, 1, 1, 49, 73.03, 51.76, 1, 1, 49, 93.85, 33.02, 1, 1, 49, 95.47, 27.45, 1, 2, 49, 98.73, 27.05, 0.33061, 50, 6.06, 28.88, 0.66939, 2, 49, 116.56, 26.44, 0.03189, 50, 23.83, 30.48, 0.96811, 1, 50, 41.25, 20.97, 1, 1, 50, 45.11, 0.91, 1, 1, 50, 40.68, -20.22, 1, 2, 49, 110.72, -40.09, 0.01726, 50, 26.23, -36.26, 0.98274, 2, 49, 99.72, -38.42, 0.07377, 50, 15.11, -35.96, 0.92623, 2, 49, 88.13, -32.53, 0.2926, 50, 2.88, -31.55, 0.7074, 1, 49, 84.1, -30.76, 1, 1, 49, 70.83, -30.87, 1, 1, 49, 57.35, -41.16, 1, 1, 49, 54.57, -33.3, 1, 2, 49, 44.85, -34.25, 0.9992, 48, 113.19, -36.88, 0.0008, 2, 49, 41.17, -32.96, 0.99675, 48, 109.59, -35.38, 0.00325, 2, 49, 18.97, -39.59, 0.89262, 48, 87.04, -40.66, 0.10738, 2, 49, -3.21, -32.95, 0.42178, 48, 65.3, -32.69, 0.57822, 2, 49, -28.94, -42.2, 0.02204, 48, 39.05, -40.39, 0.97796, 1, 48, 10.82, -47.34, 1, 1, 48, -18.69, -39.67, 1, 1, 48, -39.11, -17.68, 1, 1, 48, -15.12, 4.62, 1, 1, 48, 11.87, 0.66, 1, 1, 48, 37.77, -1.22, 1, 1, 48, 64.75, -0.74, 1, 1, 49, 36.51, 1.49, 1, 1, 49, 40.85, 1.26, 1, 1, 49, 64.03, 2.71, 1, 1, 49, 85.2, 1.41, 1, 2, 49, 90.38, 1.55, 0.10146, 50, 0.91, 2.55, 0.89854, 1, 50, 20.81, -1.67, 1], "hull": 34}}, "ren_bg_6": {"ren_bg_06": {"type": "mesh", "uvs": [0.48318, 0, 0.69457, 0.14937, 0.77398, 0.29893, 0.95409, 0.38502, 0.95405, 0.59589, 0.81299, 0.73057, 0.8476, 0.76133, 1, 0.79005, 1, 0.93229, 0.90787, 0.9767, 0.75911, 1, 0.49858, 1, 0.21921, 1, 0, 0.9423, 0, 0.83033, 0.17162, 0.74809, 0.22402, 0.72299, 0.09676, 0.63945, 0.01014, 0.59801, 0.00894, 0.44039, 0.30438, 0.2719, 0.36487, 0.18512, 0.23508, 0, 0.49412, 0.89258, 0.5343, 0.74216, 0.45841, 0.60868, 0.49858, 0.46674, 0.57448, 0.30784, 0.53876, 0.18284, 0.41376, 0.05572], "triangles": [28, 20, 21, 28, 1, 2, 21, 29, 28, 21, 22, 29, 28, 29, 1, 29, 0, 1, 29, 22, 0, 4, 26, 3, 2, 3, 26, 2, 26, 27, 19, 20, 26, 26, 20, 27, 20, 28, 27, 27, 28, 2, 5, 25, 4, 16, 17, 25, 17, 18, 25, 25, 26, 4, 25, 18, 26, 18, 19, 26, 6, 9, 10, 12, 23, 11, 10, 11, 23, 10, 23, 6, 23, 24, 6, 24, 5, 6, 23, 12, 14, 9, 6, 8, 23, 14, 15, 14, 12, 13, 6, 7, 8, 23, 15, 24, 15, 16, 24, 16, 25, 24, 24, 25, 5], "vertices": [1, 100, 15.93, -0.26, 1, 2, 99, 19.09, -6.11, 0.01486, 100, 7.08, -6.11, 0.98514, 3, 98, 22.26, -8.28, 0.0305, 99, 10.25, -8.28, 0.74665, 100, -1.76, -8.28, 0.22285, 4, 97, 29.16, -13.29, 9e-05, 98, 17.15, -13.29, 0.22232, 99, 5.14, -13.29, 0.77146, 100, -6.88, -13.29, 0.00613, 3, 97, 16.72, -13.2, 0.14576, 98, 4.71, -13.2, 0.66774, 99, -7.31, -13.2, 0.1865, 3, 97, 8.8, -9.2, 0.77029, 98, -3.21, -9.2, 0.22125, 99, -15.22, -9.2, 0.00847, 3, 97, 6.98, -10.15, 0.92424, 98, -5.03, -10.15, 0.0746, 99, -17.05, -10.15, 0.00116, 2, 97, 5.26, -14.41, 0.99454, 98, -6.76, -14.41, 0.00546, 1, 97, -3.13, -14.35, 1, 1, 97, -5.74, -11.75, 1, 1, 97, -7.08, -7.58, 1, 1, 97, -7.03, -0.28, 1, 1, 97, -6.98, 7.54, 1, 1, 97, -3.53, 13.65, 1, 1, 97, 3.07, 13.61, 1, 2, 97, 7.89, 8.77, 0.92845, 98, -4.12, 8.77, 0.07155, 3, 97, 9.36, 7.29, 0.76061, 98, -2.65, 7.29, 0.2386, 99, -14.66, 7.29, 0.0008, 3, 97, 14.31, 10.82, 0.14677, 98, 2.3, 10.82, 0.78973, 99, -9.71, 10.82, 0.0635, 3, 97, 16.78, 13.23, 0.05295, 98, 4.76, 13.23, 0.81265, 99, -7.25, 13.23, 0.1344, 2, 98, 14.06, 13.2, 0.48765, 99, 2.05, 13.2, 0.51235, 3, 98, 23.95, 4.86, 0.00374, 99, 11.93, 4.86, 0.64038, 100, -0.08, 4.86, 0.35588, 2, 99, 17.04, 3.13, 0.00648, 100, 5.03, 3.13, 0.99352, 1, 100, 15.98, 6.69, 1, 1, 97, -0.7, -0.2, 1, 2, 97, 8.17, -1.39, 0.99423, 98, -3.84, -1.39, 0.00577, 2, 97, 16.06, 0.68, 0.00075, 98, 4.05, 0.68, 0.99925, 2, 98, 12.41, -0.5, 0.17922, 99, 0.4, -0.5, 0.82078, 3, 98, 21.78, -2.69, 0.00043, 99, 9.76, -2.69, 0.8812, 100, -2.25, -2.69, 0.11837, 1, 100, 5.13, -1.74, 1, 1, 100, 12.65, 1.71, 1], "hull": 23}}, "shitouren6": {"shitouren06": {"type": "mesh", "uvs": [0.37555, 0.00783, 0.56089, 0.07007, 0.76364, 0.13816, 0.98745, 0.25772, 1, 0.31298, 0.97189, 0.46812, 0.89912, 0.62424, 0.84723, 0.75191, 0.82356, 0.87833, 0.78903, 0.96393, 0.72063, 0.9743, 0.66897, 0.98214, 0.53027, 0.98852, 0.43097, 0.9931, 0.29715, 0.88576, 0.18341, 0.79451, 0.08761, 0.73668, 0.08769, 0.61619, 0.08775, 0.52539, 0.0845, 0.50811, 0.01082, 0.39033, 0.01079, 0.28302, 0.11112, 0.04072, 0.23283, 0.00826, 0.39437, 0.28807, 0.59514, 0.33298, 0.77806, 0.34556, 0.89629, 0.33119, 0.73791, 0.25035, 0.60406, 0.18208, 0.43464, 0.09694, 0.30068, 0.21262, 0.19583, 0.1192, 0.73322, 0.53289, 0.74104, 0.63528, 0.70583, 0.74712, 0.6804, 0.88416, 0.55518, 0.76564, 0.41341, 0.79035, 0.54203, 0.88569, 0.42218, 0.87863, 0.39065, 0.44007, 0.57335, 0.49186, 0.83788, 0.51069, 0.82988, 0.61966, 0.75867, 0.74789, 0.55492, 0.62674, 0.41422, 0.65711, 0.29818, 0.55665, 0.26481, 0.70617, 0.49475, 0.22619, 0.30475, 0.10079, 0.85332, 0.2492, 0.67047, 0.16177, 0.50475, 0.08239, 0.20332, 0.26761, 0.19332, 0.40681, 0.20904, 0.54141, 0.16904, 0.63805, 0.27475, 0.783, 0.73904, 0.88194], "triangles": [54, 0, 1, 30, 0, 54, 51, 23, 0, 51, 0, 30, 32, 22, 23, 32, 23, 51, 53, 1, 2, 54, 1, 53, 29, 54, 53, 30, 54, 29, 31, 32, 51, 50, 30, 29, 51, 30, 50, 3, 52, 2, 53, 2, 52, 28, 53, 52, 29, 53, 28, 55, 32, 31, 21, 22, 32, 21, 32, 55, 50, 31, 51, 24, 31, 50, 27, 52, 3, 27, 3, 4, 25, 50, 29, 25, 29, 28, 24, 50, 25, 26, 28, 52, 26, 52, 27, 25, 28, 26, 56, 20, 21, 55, 56, 21, 24, 55, 31, 41, 24, 25, 24, 56, 55, 41, 56, 24, 5, 27, 4, 42, 41, 25, 19, 20, 56, 43, 26, 27, 43, 27, 5, 57, 18, 19, 26, 42, 25, 33, 26, 43, 33, 42, 26, 48, 57, 56, 57, 19, 56, 41, 48, 56, 17, 18, 57, 44, 33, 43, 6, 43, 5, 44, 43, 6, 42, 47, 41, 46, 42, 33, 47, 48, 41, 34, 33, 44, 46, 33, 34, 58, 17, 57, 42, 46, 47, 48, 58, 57, 49, 48, 47, 49, 58, 48, 16, 17, 58, 16, 58, 49, 35, 46, 34, 45, 34, 44, 35, 34, 45, 7, 45, 44, 6, 7, 44, 37, 46, 35, 47, 46, 37, 38, 59, 49, 47, 38, 49, 15, 16, 49, 37, 38, 47, 59, 15, 49, 8, 45, 7, 39, 40, 38, 60, 35, 45, 60, 45, 8, 36, 37, 35, 36, 35, 60, 37, 39, 38, 36, 39, 37, 14, 59, 38, 14, 38, 40, 15, 59, 14, 9, 60, 8, 10, 36, 60, 10, 60, 9, 11, 39, 36, 11, 36, 10, 12, 40, 39, 12, 39, 11, 13, 40, 12, 14, 40, 13], "vertices": [2, 51, 82.57, 15.11, 0.7, 53, 56.28, 41.23, 0.3, 2, 51, 79.71, -2.94, 0.7, 53, 53.42, 23.18, 0.3, 2, 51, 76.58, -22.68, 0.7, 53, 50.29, 3.43, 0.3, 2, 51, 68.25, -45.66, 0.7, 53, 41.96, -19.54, 0.3, 2, 51, 62.45, -48.24, 0.7, 53, 36.16, -22.12, 0.3, 2, 51, 44.81, -49.88, 0.7, 53, 18.52, -23.77, 0.3, 2, 51, 26.1, -47.6, 0.7, 53, -0.18, -21.49, 0.3, 3, 51, 10.97, -46.41, 0.6384, 53, -15.32, -20.3, 0.2736, 52, 10.79, -17.21, 0.088, 2, 51, -3.42, -47.69, 0.7, 53, -29.71, -21.57, 0.3, 2, 51, -13.56, -46.91, 0.7, 53, -39.85, -20.8, 0.3, 3, 51, -16.17, -41.14, 0.6608, 53, -42.45, -15.02, 0.2832, 52, -16.35, -11.93, 0.056, 3, 51, -18.14, -36.78, 0.6048, 53, -44.42, -10.66, 0.2592, 52, -18.32, -7.57, 0.136, 3, 51, -21.81, -24.68, 0.5712, 53, -48.1, 1.43, 0.2448, 52, -21.99, 4.52, 0.184, 3, 51, -24.44, -16.02, 0.6216, 53, -50.73, 10.09, 0.2664, 52, -24.62, 13.18, 0.112, 3, 51, -15.52, -1.33, 0.6776, 53, -41.81, 24.79, 0.2904, 52, -15.7, 27.87, 0.032, 2, 51, -7.94, 11.16, 0.7, 53, -34.23, 37.27, 0.3, 2, 51, -3.64, 21.17, 0.7, 53, -29.93, 47.29, 0.3, 2, 51, 9.59, 24.37, 0.7, 53, -16.7, 50.49, 0.3, 2, 51, 19.56, 26.78, 0.7, 53, -6.72, 52.9, 0.3, 2, 51, 21.39, 27.53, 0.7, 53, -4.9, 53.65, 0.3, 2, 51, 32.75, 37.18, 0.7, 53, 6.46, 63.3, 0.3, 2, 51, 44.53, 40.04, 0.7, 53, 18.24, 66.16, 0.3, 2, 51, 73.29, 37.62, 0.7, 53, 47, 63.74, 0.3, 2, 51, 79.46, 27.72, 0.7, 53, 53.18, 53.84, 0.3, 2, 51, 52.2, 5.98, 0.812, 52, 52.02, 35.19, 0.188, 2, 51, 51.57, -12.97, 0.8, 52, 51.39, 16.24, 0.2, 2, 51, 54.11, -29.48, 0.7, 52, 53.93, -0.27, 0.3, 2, 51, 58.22, -39.55, 0.88, 52, 58.05, -10.35, 0.12, 2, 51, 63.71, -23.39, 0.7, 52, 63.53, 5.81, 0.3, 2, 51, 68.33, -9.74, 0.7, 52, 68.15, 19.47, 0.3, 2, 51, 74.05, 7.51, 0.94, 52, 73.87, 36.72, 0.06, 2, 51, 58.48, 16.28, 0.952, 52, 58.3, 45.48, 0.048, 2, 51, 66.49, 28.04, 0.952, 52, 66.31, 57.24, 0.048, 2, 51, 32.58, -30.5, 0.7, 52, 32.4, -1.3, 0.3, 2, 51, 21.5, -33.92, 0.7, 52, 21.32, -4.71, 0.3, 2, 51, 8.46, -33.78, 0.7, 52, 8.28, -4.58, 0.3, 2, 51, -7.13, -35.18, 0.7, 52, -7.31, -5.98, 0.3, 2, 51, 3.2, -20.95, 0.8, 52, 3.02, 8.25, 0.2, 2, 51, -2.55, -9.07, 0.86, 52, -2.73, 20.13, 0.14, 2, 51, -10.27, -22.98, 0.8, 52, -10.45, 6.22, 0.2, 2, 51, -12.06, -12.2, 0.86, 52, -12.24, 17.01, 0.14, 2, 51, 35.43, 2.27, 0.828, 52, 35.25, 31.47, 0.172, 2, 51, 33.66, -15.27, 0.8, 52, 33.48, 13.93, 0.2, 2, 51, 37.26, -39.17, 0.912, 52, 37.08, -9.96, 0.088, 2, 51, 25.12, -41.36, 0.88, 52, 24.94, -12.15, 0.12, 2, 51, 9.51, -38.48, 0.784, 52, 9.33, -9.27, 0.216, 2, 51, 18.45, -17.23, 0.8, 52, 18.27, 11.97, 0.2, 2, 51, 12.1, -5.6, 0.828, 52, 11.92, 23.61, 0.172, 2, 51, 20.64, 7.34, 0.86, 52, 20.46, 36.55, 0.14, 2, 51, 3.51, 6.31, 0.9, 52, 3.33, 35.52, 0.1, 2, 51, 61.15, -1.25, 0.8, 52, 60.97, 27.96, 0.2, 2, 51, 70.84, 18.9, 0.952, 52, 70.66, 48.1, 0.048, 2, 51, 66.31, -33.57, 0.912, 52, 66.13, -4.37, 0.088, 2, 51, 71.99, -15.07, 0.96, 52, 71.81, 14.13, 0.04, 2, 51, 77.15, 1.7, 0.936, 52, 76.97, 30.9, 0.064, 2, 51, 50.35, 23.43, 0.9, 52, 50.17, 52.63, 0.1, 2, 51, 34.85, 20.6, 0.9, 52, 34.67, 49.81, 0.1, 2, 51, 20.4, 15.63, 0.9, 52, 20.22, 44.83, 0.1, 2, 51, 8.93, 16.59, 0.95, 52, 8.75, 45.8, 0.05, 2, 51, -4.72, 3.39, 0.9, 52, -4.9, 32.59, 0.1, 2, 51, -5.63, -40.31, 0.872, 52, -5.81, -11.1, 0.128], "hull": 24}}, "shitouren7": {"shitouren07": {"type": "mesh", "uvs": [0.75375, 0, 0.94196, 0.06871, 1, 0.17886, 0.95256, 0.28309, 0.80677, 0.33757, 0.77496, 0.4264, 0.70604, 0.4868, 0.76435, 0.54247, 0.75268, 0.58074, 0.74845, 0.59458, 0.80412, 0.61117, 0.95786, 0.6917, 0.86618, 0.78519, 0.85448, 0.79712, 0.93666, 0.8362, 0.94461, 0.88002, 0.95521, 0.92266, 0.82797, 0.9878, 0.66362, 1, 0.37999, 1, 0.24745, 0.92621, 0.2371, 0.85785, 0.22771, 0.84646, 0.13248, 0.80391, 0, 0.76856, 0.10432, 0.70863, 0.107, 0.67567, 0.15663, 0.65709, 0.14321, 0.6457, 0.04396, 0.55521, 0.04128, 0.50547, 0.13651, 0.42239, 0.07507, 0.30959, 0.15792, 0.25498, 0.11442, 0.16891, 0.19107, 0.08376, 0.47485, 0, 0.55629, 0.14901, 0.48086, 0.29812, 0.42372, 0.42782, 0.52643, 0.20804, 0.62029, 0.07344, 0.45142, 0.36496, 0.39629, 0.51872, 0.43058, 0.60246, 0.43515, 0.61982, 0.46258, 0.7291, 0.50143, 0.79242, 0.51058, 0.81489, 0.55858, 0.91599], "triangles": [48, 47, 13, 21, 22, 48, 49, 48, 13, 21, 48, 49, 20, 21, 49, 49, 15, 17, 13, 15, 49, 14, 15, 13, 17, 15, 16, 19, 20, 49, 18, 49, 17, 19, 49, 18, 41, 36, 0, 41, 0, 1, 37, 36, 41, 35, 36, 37, 40, 35, 37, 34, 35, 40, 33, 34, 40, 2, 41, 1, 2, 37, 41, 2, 40, 37, 3, 40, 2, 38, 33, 40, 4, 38, 40, 3, 4, 40, 42, 33, 38, 42, 38, 4, 32, 33, 42, 31, 32, 42, 5, 42, 4, 39, 31, 42, 39, 42, 5, 6, 39, 5, 43, 31, 39, 43, 39, 6, 30, 31, 43, 29, 30, 43, 6, 44, 43, 6, 7, 44, 7, 8, 44, 9, 44, 8, 28, 29, 43, 45, 44, 9, 44, 28, 43, 28, 44, 45, 27, 28, 45, 46, 45, 9, 27, 45, 46, 46, 9, 10, 46, 10, 11, 25, 26, 27, 25, 27, 46, 24, 25, 46, 12, 46, 11, 47, 46, 12, 13, 47, 12, 23, 24, 46, 22, 23, 46, 47, 22, 46, 22, 47, 48], "vertices": [1, 44, -45.13, 11.15, 1, 1, 44, -33.5, 39.48, 1, 1, 44, -5.91, 55.52, 1, 2, 45, -19.52, 76.12, 0.00017, 44, 23.95, 58.36, 0.99983, 1, 44, 44, 45.25, 1, 2, 45, 10.44, 49.54, 0.344, 44, 62.82, 48.72, 0.656, 2, 45, 20.47, 40.51, 0.65753, 44, 75.9, 45.38, 0.34247, 3, 45, 34.31, 46.35, 0.83929, 44, 85.44, 56.98, 0.16035, 46, -63.6, 46.09, 0.00035, 1, 45, 42.03, 44.27, 1, 1, 45, 45.77, 43.02, 1, 1, 45, 51.67, 49.04, 1, 1, 45, 77.59, 63.84, 1, 1, 45, 101.34, 47.58, 1, 2, 45, 104.37, 45.5, 0.38882, 46, 6.46, 45.53, 0.61118, 2, 45, 117.13, 53.62, 0.14678, 46, 19.19, 53.71, 0.85322, 2, 45, 129.46, 52.31, 0.06829, 46, 31.52, 52.44, 0.93171, 2, 45, 141.52, 51.38, 0.02573, 46, 43.59, 51.57, 0.97427, 2, 45, 156.58, 32.21, 0.00011, 46, 58.73, 32.46, 0.99989, 1, 46, 58.34, 11.47, 1, 1, 46, 51.84, -23.67, 1, 2, 45, 125.9, -36.42, 0.03807, 46, 28.33, -36.3, 0.96193, 2, 45, 106.71, -34.11, 0.31813, 46, 9.14, -34.08, 0.68187, 1, 45, 103.34, -34.68, 1, 1, 45, 89.31, -44.23, 1, 1, 45, 76.41, -58.78, 1, 1, 45, 62.25, -42.71, 1, 2, 45, 53.18, -40.65, 0.99122, 46, -44.36, -40.84, 0.00878, 2, 45, 49.2, -33.53, 0.9981, 46, -48.37, -33.74, 0.0019, 2, 45, 45.73, -34.59, 0.99969, 46, -51.84, -34.81, 0.00031, 2, 45, 18.77, -42.36, 0.97294, 44, 112.86, -28.81, 0.02706, 2, 45, 6.37, -40.84, 0.88374, 44, 101.17, -33.21, 0.11626, 2, 45, -4.36, -29.97, 0.26443, 44, 86.62, -28.57, 0.73557, 1, 44, 63.29, -45.24, 1, 1, 44, 45.52, -39.74, 1, 1, 44, 23.89, -52.05, 1, 1, 44, -1.89, -49.8, 1, 1, 44, -34.9, -22.47, 1, 1, 44, 2.32, -0.42, 1, 2, 45, -26.42, 16.93, 0.0001, 44, 45.31, 2.73, 0.9999, 2, 45, 7.09, 3.62, 0.06701, 44, 81.17, 6.49, 0.93299, 1, 44, 19.33, 0.83, 1, 1, 44, -20.42, 1.09, 1, 2, 45, -8.67, 9.82, 0.00491, 44, 64.33, 4.67, 0.99509, 2, 45, 16.82, 3.23, 0.97949, 44, 89.97, 10.67, 0.02051, 2, 45, 40.52, 3.24, 0.99853, 44, 110.95, 21.68, 0.00147, 2, 45, 45.42, 2.9, 0.99939, 44, 115.45, 23.65, 0.00061, 1, 45, 76.33, 0.56, 1, 1, 45, 94.79, 2.05, 1, 2, 45, 101.23, 2, 0.03323, 46, 3.5, 2.01, 0.96677, 1, 46, 32.64, 2.77, 1], "hull": 37}}, "ren_bg_03": {"ren_bg_03": {"type": "mesh", "uvs": [0, 0.15416, 0, 0.09895, 0.06844, 0, 0.19065, 0, 0.26301, 0.07753, 0.35665, 0.23338, 0.41042, 0.35711, 0.44943, 0.4469, 0.51426, 0.59188, 0.61286, 0.7007, 0.64416, 0.70474, 0.70479, 0.52539, 0.76033, 0.36113, 0.86358, 0.2314, 0.94563, 0.20872, 1, 0.22408, 1, 0.37685, 1, 0.55709, 0.96284, 0.54479, 0.93543, 0.56195, 0.87347, 0.62244, 0.8441, 0.68167, 0.75611, 0.91439, 0.68451, 1, 0.61207, 1, 0.56485, 1, 0.44264, 0.729, 0.38333, 0.54796, 0.3243, 0.3678, 0.27414, 0.25803, 0.24174, 0.21466, 0.15858, 0.10337, 0.09628, 0.08077, 0, 0.19253, 0.08122, 0.03925, 0.17394, 0.04809, 0.26786, 0.163, 0.34383, 0.30089, 0.45166, 0.58657, 0.53181, 0.75982, 0.39839, 0.44544, 0.60429, 0.84357, 0.67667, 0.83296, 0.7335, 0.71098, 0.80408, 0.52005, 0.87168, 0.4016, 0.95004, 0.36625], "triangles": [46, 14, 15, 13, 14, 46, 46, 15, 16, 45, 13, 46, 12, 13, 45, 18, 46, 16, 18, 16, 17, 19, 45, 46, 19, 46, 18, 20, 45, 19, 44, 45, 20, 44, 12, 45, 11, 12, 44, 21, 44, 20, 43, 11, 44, 43, 44, 21, 10, 11, 43, 42, 10, 43, 22, 43, 21, 42, 43, 22, 23, 42, 22, 24, 42, 23, 39, 8, 9, 38, 8, 39, 41, 39, 9, 41, 9, 10, 41, 10, 42, 25, 39, 41, 25, 26, 39, 24, 41, 42, 25, 41, 24, 37, 29, 36, 5, 37, 36, 37, 5, 6, 28, 29, 37, 40, 37, 6, 28, 37, 40, 40, 6, 7, 27, 28, 40, 8, 38, 7, 40, 7, 38, 38, 27, 40, 26, 27, 38, 26, 38, 39, 3, 35, 2, 31, 32, 35, 36, 4, 5, 35, 3, 4, 36, 35, 4, 30, 35, 36, 31, 35, 30, 30, 36, 29, 35, 34, 2, 32, 34, 35, 34, 0, 1, 34, 1, 2, 32, 33, 0, 32, 0, 34], "vertices": [1, 87, -1, 0.27, 1, 1, 87, 0.88, 6.16, 1, 2, 87, 25.83, 9.82, 0.99994, 88, -22.83, -5.71, 6e-05, 1, 88, 14.94, 8.76, 1, 1, 88, 40.41, 9.23, 1, 2, 88, 75.6, 4.02, 0.07819, 89, 18.79, 11.38, 0.92181, 1, 89, 41.33, 11.99, 1, 1, 89, 57.7, 12.42, 1, 2, 89, 84.59, 13.52, 0.08874, 90, 11.12, 10.41, 0.91126, 2, 90, 45.96, 10.16, 0.95419, 91, -2.16, 21.28, 0.04581, 2, 90, 55.84, 13.29, 0.52504, 91, 5.98, 14.86, 0.47496, 1, 91, 34.01, 19.41, 1, 2, 91, 59.68, 23.58, 0.91799, 92, -24.21, 13.76, 0.08201, 1, 92, 11.53, 23.82, 1, 1, 92, 38.8, 22.88, 1, 1, 92, 56.43, 18.88, 1, 1, 92, 54.25, 1.91, 1, 1, 92, 51.68, -18.11, 1, 1, 92, 39.65, -15.18, 1, 2, 91, 93.52, -28.57, 0.00508, 92, 30.41, -15.93, 0.99492, 2, 91, 72.93, -22.07, 0.39246, 92, 9.21, -20.04, 0.60754, 2, 91, 61.16, -21.76, 0.83576, 92, -1.28, -25.38, 0.16424, 2, 90, 98.7, 3.94, 0.00027, 91, 22.29, -25.86, 0.99973, 2, 90, 79.73, -13.2, 0.31097, 91, -2.54, -19.78, 0.68903, 2, 90, 57.21, -21.42, 0.98412, 91, -21.98, -5.75, 0.01588, 1, 90, 42.53, -26.78, 1, 2, 89, 76.03, -13.41, 0.69293, 90, -5.88, -12.14, 0.30707, 1, 89, 47.99, -10.22, 1, 1, 89, 20.08, -7.05, 1, 2, 88, 51.09, -8.33, 0.13891, 89, -0.55, -8.1, 0.86109, 2, 88, 39.33, -7.63, 0.92259, 89, -11.92, -11.16, 0.07741, 2, 87, 50.73, -10.3, 0.01861, 88, 9.17, -5.85, 0.98139, 1, 87, 31.86, -1.61, 1, 1, 87, -2.31, -3.82, 1, 2, 87, 28.53, 4.34, 0.99994, 88, -17.31, -8.3, 6e-05, 1, 88, 11.7, 1.76, 1, 1, 88, 45.34, 0.86, 1, 2, 88, 74.34, -4.56, 0.01558, 89, 20.31, 2.85, 0.98442, 2, 89, 68.2, 0.81, 0.99907, 90, -8.55, 3.87, 0.00092, 1, 90, 23.03, -5.26, 1, 1, 89, 44.54, 1.82, 1, 1, 90, 48.78, -5.85, 1, 1, 91, 6.3, -3.08, 1, 1, 91, 29.55, -3.01, 1, 1, 91, 61.01, 0.67, 1, 1, 92, 11.77, 4.57, 1, 1, 92, 38, 5.19, 1], "hull": 34}}, "ren_bg_06": {"ren_bg_06": {"type": "mesh", "uvs": [0.48318, 0, 0.69457, 0.14937, 0.77398, 0.29893, 0.95409, 0.38502, 0.95405, 0.59589, 0.81299, 0.73057, 0.8476, 0.76133, 1, 0.79005, 1, 0.93229, 0.90787, 0.9767, 0.75911, 1, 0.49858, 1, 0.21921, 1, 0, 0.9423, 0, 0.83033, 0.17162, 0.74809, 0.22402, 0.72299, 0.09676, 0.63945, 0.01014, 0.59801, 0.00894, 0.44039, 0.30438, 0.2719, 0.36487, 0.18512, 0.23508, 0, 0.49412, 0.89258, 0.5343, 0.74216, 0.45841, 0.60868, 0.49858, 0.46674, 0.57448, 0.30784, 0.53876, 0.18284, 0.41376, 0.05572], "triangles": [28, 20, 21, 28, 1, 2, 21, 29, 28, 21, 22, 29, 28, 29, 1, 29, 0, 1, 29, 22, 0, 4, 26, 3, 2, 3, 26, 2, 26, 27, 19, 20, 26, 26, 20, 27, 20, 28, 27, 27, 28, 2, 5, 25, 4, 16, 17, 25, 17, 18, 25, 25, 26, 4, 25, 18, 26, 18, 19, 26, 6, 9, 10, 12, 23, 11, 10, 11, 23, 10, 23, 6, 23, 24, 6, 24, 5, 6, 23, 12, 14, 9, 6, 8, 23, 14, 15, 14, 12, 13, 6, 7, 8, 23, 15, 24, 15, 16, 24, 16, 25, 24, 24, 25, 5], "vertices": [1, 78, 15.93, -0.26, 1, 2, 77, 19.09, -6.11, 0.01486, 78, 7.08, -6.11, 0.98514, 3, 76, 22.26, -8.28, 0.0305, 77, 10.25, -8.28, 0.74665, 78, -1.76, -8.28, 0.22285, 4, 75, 29.16, -13.29, 9e-05, 76, 17.15, -13.29, 0.22232, 77, 5.14, -13.29, 0.77146, 78, -6.88, -13.29, 0.00613, 3, 75, 16.72, -13.2, 0.14576, 76, 4.71, -13.2, 0.66774, 77, -7.31, -13.2, 0.1865, 3, 75, 8.8, -9.2, 0.77029, 76, -3.21, -9.2, 0.22125, 77, -15.22, -9.2, 0.00847, 3, 75, 6.98, -10.15, 0.92424, 76, -5.03, -10.15, 0.0746, 77, -17.05, -10.15, 0.00116, 2, 75, 5.26, -14.41, 0.99454, 76, -6.76, -14.41, 0.00546, 1, 75, -3.13, -14.35, 1, 1, 75, -5.74, -11.75, 1, 1, 75, -7.08, -7.58, 1, 1, 75, -7.03, -0.28, 1, 1, 75, -6.98, 7.54, 1, 1, 75, -3.53, 13.65, 1, 1, 75, 3.07, 13.61, 1, 2, 75, 7.89, 8.77, 0.92845, 76, -4.12, 8.77, 0.07155, 3, 75, 9.36, 7.29, 0.76061, 76, -2.65, 7.29, 0.2386, 77, -14.66, 7.29, 0.0008, 3, 75, 14.31, 10.82, 0.14677, 76, 2.3, 10.82, 0.78973, 77, -9.71, 10.82, 0.0635, 3, 75, 16.78, 13.23, 0.05295, 76, 4.76, 13.23, 0.81265, 77, -7.25, 13.23, 0.1344, 2, 76, 14.06, 13.2, 0.48765, 77, 2.05, 13.2, 0.51235, 3, 76, 23.95, 4.86, 0.00374, 77, 11.93, 4.86, 0.64038, 78, -0.08, 4.86, 0.35588, 2, 77, 17.04, 3.13, 0.00648, 78, 5.03, 3.13, 0.99352, 1, 78, 15.98, 6.69, 1, 1, 75, -0.7, -0.2, 1, 2, 75, 8.17, -1.39, 0.99423, 76, -3.84, -1.39, 0.00577, 2, 75, 16.06, 0.68, 0.00075, 76, 4.05, 0.68, 0.99925, 2, 76, 12.41, -0.5, 0.17922, 77, 0.4, -0.5, 0.82078, 3, 76, 21.78, -2.69, 0.00043, 77, 9.76, -2.69, 0.8812, 78, -2.25, -2.69, 0.11837, 1, 78, 5.13, -1.74, 1, 1, 78, 12.65, 1.71, 1], "hull": 23}}, "shitouren01": {"shitouren01": {"type": "mesh", "uvs": [0.16567, 0.42802, 0.18906, 0.41071, 0.15611, 0.39093, 0.06765, 0.31029, 0.0364, 0.17812, 0.0964, 0.05874, 0.2614, 0.00843, 0.43389, 0.00843, 0.58522, 0.0717, 0.67422, 0.1672, 0.79122, 0.29954, 0.81922, 0.33501, 0.81222, 0.37594, 0.78039, 0.49641, 0.77703, 0.56181, 0.67776, 0.65644, 0.66705, 0.66303, 0.67515, 0.6723, 0.78205, 0.7627, 0.93, 0.83287, 0.99905, 0.91649, 0.95537, 0.95783, 0.7648, 0.9746, 0.54351, 0.99407, 0.34615, 0.96559, 0.12412, 0.92287, 0.03113, 0.90086, 0, 0.85166, 0.04812, 0.76475, 0.07911, 0.6841, 0.12387, 0.64809, 0.10666, 0.62382, 0.06412, 0.55226, 0.10009, 0.48029, 0.63794, 0.37078, 0.58576, 0.54114, 0.51122, 0.667, 0.58576, 0.7865, 0.71063, 0.89075, 0.20931, 0.79159, 0.41617, 0.85897, 0.43399, 0.24027, 0.28603, 0.12958], "triangles": [24, 40, 23, 23, 38, 22, 38, 40, 37, 38, 23, 40, 21, 22, 19, 40, 24, 39, 22, 38, 19, 21, 19, 20, 24, 25, 39, 39, 25, 27, 39, 27, 28, 27, 25, 26, 38, 18, 19, 38, 37, 18, 37, 40, 36, 28, 29, 39, 29, 30, 39, 40, 39, 36, 39, 30, 36, 37, 17, 18, 17, 36, 16, 17, 37, 36, 36, 35, 16, 35, 36, 31, 16, 35, 15, 36, 30, 31, 31, 32, 35, 32, 33, 35, 33, 0, 35, 35, 34, 13, 35, 0, 34, 0, 1, 34, 1, 2, 34, 12, 34, 11, 34, 10, 11, 34, 9, 10, 15, 35, 14, 14, 35, 13, 13, 34, 12, 2, 41, 34, 2, 3, 41, 34, 41, 9, 3, 42, 41, 3, 4, 42, 41, 42, 8, 41, 8, 9, 8, 42, 7, 4, 5, 42, 5, 6, 42, 42, 6, 7], "vertices": [2, 9, 27.12, -32.09, 0.23282, 10, 27, -26.69, 0.76718, 2, 9, 26.83, -29.07, 0.37352, 10, 23.99, -26.29, 0.62648, 2, 9, 23, -29.53, 0.54619, 10, 23.56, -30.13, 0.45381, 2, 9, 10.06, -28.37, 0.82845, 10, 19.51, -42.48, 0.17155, 2, 9, -4.47, -19.04, 0.97273, 10, 7.13, -54.52, 0.02727, 2, 9, -12.41, -4.82, 0.99991, 10, -8.51, -59.03, 9e-05, 1, 9, -7.55, 10.32, 1, 2, 9, 2.59, 21.62, 0.99742, 10, -30.87, -38.44, 0.00258, 2, 9, 17.56, 26.08, 0.95369, 10, -31.84, -22.85, 0.04631, 2, 9, 31.96, 23.69, 0.76849, 10, -26.24, -9.37, 0.23151, 2, 9, 51.54, 19.95, 0.34103, 10, -18.18, 8.86, 0.65897, 2, 9, 56.59, 18.73, 0.3978, 10, -15.84, 13.51, 0.6022, 3, 9, 60.11, 14.74, 0.53614, 10, -11.17, 16.04, 0.46327, 11, 6.41, 62.22, 0.00059, 3, 9, 69.81, 2.28, 0.84297, 10, 3.16, 22.66, 0.11878, 11, 15.19, 49.1, 0.03826, 3, 9, 75.89, -3.57, 0.69557, 10, 10.24, 27.26, 0.20829, 11, 20.84, 42.83, 0.09614, 3, 9, 79.15, -18.22, 0.26288, 10, 25.25, 27.12, 0.32932, 11, 23.04, 27.98, 0.40781, 3, 9, 79.15, -19.49, 0.21329, 10, 26.49, 26.84, 0.30781, 11, 22.95, 26.71, 0.4789, 3, 9, 80.52, -19.76, 0.171, 10, 27.06, 28.11, 0.26713, 11, 24.29, 26.35, 0.56187, 3, 9, 95.48, -20.55, 0.02769, 10, 31.21, 42.5, 0.04262, 11, 39.16, 24.5, 0.92969, 3, 9, 110.91, -16.9, 0.00245, 10, 31.14, 58.36, 0.00107, 11, 54.81, 27.03, 0.99648, 2, 9, 123, -19.58, 3e-05, 11, 66.68, 23.5, 0.99997, 1, 11, 67.62, 16.99, 1, 1, 11, 57.06, 3.78, 1, 1, 11, 44.8, -11.55, 1, 1, 11, 29.76, -20.98, 1, 1, 11, 11.88, -30.59, 1, 1, 11, 4.02, -34.24, 1, 2, 10, 80.11, -7.26, 0.00191, 11, -2.36, -31.58, 0.99809, 2, 10, 68.5, -10.23, 0.08166, 11, -7.1, -20.57, 0.91834, 2, 10, 58.42, -13.97, 0.39218, 11, -12.37, -11.19, 0.60782, 3, 9, 45.8, -53.79, 0.00077, 10, 52.35, -13.41, 0.72477, 11, -12.77, -5.11, 0.27446, 3, 9, 42.45, -52.83, 0.00397, 10, 50.66, -16.45, 0.88457, 11, -16.03, -3.91, 0.11146, 3, 9, 33.08, -49.45, 0.02413, 10, 45.25, -24.81, 0.97355, 11, -25.14, 0.12, 0.00233, 2, 9, 28.29, -40.89, 0.07827, 10, 35.83, -27.55, 0.92173, 3, 9, 49.38, 3.77, 0.25476, 10, -2.91, 3.1, 0.74513, 11, -5.09, 52.05, 0.0001, 3, 9, 62.67, -14.32, 0.30969, 10, 17.72, 11.95, 0.61431, 11, 6.88, 33.05, 0.076, 3, 9, 70.37, -30.04, 0.07485, 10, 34.78, 15.9, 0.45723, 11, 13.44, 16.82, 0.46792, 3, 9, 86.23, -35.45, 0.02389, 10, 43.64, 30.12, 0.05779, 11, 28.87, 10.29, 0.91832, 3, 9, 103.58, -36.26, 0.00265, 10, 48.34, 46.84, 0.0022, 11, 46.12, 8.25, 0.99515, 2, 10, 63.19, 3.37, 0.00585, 11, 5.5, -13.2, 0.99415, 1, 11, 24.63, -6.81, 1, 1, 9, 24.86, 1.65, 1, 1, 9, 5.53, 1.5, 1], "hull": 34}}, "shitouren02": {"shitouren02": {"type": "mesh", "uvs": [0.76267, 0.46553, 0.75012, 0.44799, 0.78883, 0.43957, 0.89699, 0.38199, 0.98472, 0.2718, 0.98832, 0.12984, 0.78883, 0.02659, 0.5605, 0.01865, 0.41509, 0.05042, 0.33573, 0.22102, 0.25482, 0.35031, 0.23888, 0.37579, 0.23729, 0.42563, 0.24999, 0.57777, 0.29762, 0.65647, 0.3135, 0.67746, 0.27698, 0.70106, 0.05788, 0.8296, 0.00231, 0.91485, 0.17696, 0.96601, 0.4294, 0.9883, 0.64375, 0.92797, 0.78029, 0.85714, 0.76124, 0.74566, 0.73915, 0.69011, 0.72631, 0.65778, 0.74215, 0.62142, 0.76917, 0.55941, 0.58735, 0.23615, 0.45239, 0.40665, 0.50796, 0.6611, 0.52384, 0.77521, 0.26028, 0.85391, 0.48774, 0.56852], "triangles": [19, 32, 20, 20, 31, 21, 20, 32, 31, 18, 17, 19, 19, 17, 32, 22, 21, 23, 21, 31, 23, 32, 16, 31, 32, 17, 16, 16, 30, 31, 31, 30, 24, 16, 15, 30, 15, 14, 30, 31, 24, 23, 30, 25, 24, 14, 33, 30, 25, 30, 26, 26, 30, 33, 14, 13, 33, 26, 33, 27, 13, 29, 33, 13, 12, 29, 33, 0, 27, 33, 29, 0, 29, 1, 0, 1, 29, 2, 29, 28, 2, 2, 28, 3, 12, 11, 29, 11, 10, 29, 10, 9, 29, 29, 9, 28, 3, 28, 4, 28, 6, 4, 4, 6, 5, 9, 8, 28, 8, 7, 28, 28, 7, 6], "vertices": [2, 12, 31.02, 36.55, 0.3624, 13, 35.54, 27.8, 0.6376, 2, 12, 30.57, 33.78, 0.47777, 13, 32.76, 28.22, 0.52223, 2, 12, 26.44, 35.71, 0.63811, 13, 34.64, 32.38, 0.36189, 2, 12, 11.88, 37.5, 0.85689, 13, 36.26, 46.96, 0.14311, 2, 12, -5.54, 32.22, 0.9631, 13, 30.77, 64.31, 0.0369, 2, 12, -18.4, 17.43, 0.99792, 13, 15.83, 76.99, 0.00208, 1, 12, -10.05, -8.07, 1, 1, 12, 9.25, -25.58, 1, 1, 12, 24.79, -32.82, 1, 1, 12, 46.81, -20.53, 1, 2, 12, 65.32, -12.73, 0.99991, 14, 5.78, -64.12, 9e-05, 3, 12, 68.97, -11.19, 0.99031, 13, -11.74, -10.71, 0.00857, 14, 9.39, -62.5, 0.00112, 3, 12, 73.51, -6.02, 0.81886, 13, -6.52, -15.19, 0.17143, 14, 13.81, -57.22, 0.00972, 3, 12, 85.84, 11.04, 0.10281, 13, 10.69, -27.31, 0.72454, 14, 25.72, -39.87, 0.17264, 3, 12, 88.62, 22.85, 0.0158, 13, 22.54, -29.95, 0.5372, 14, 28.22, -27.99, 0.447, 3, 12, 89.08, 26.24, 0.00516, 13, 25.93, -30.38, 0.38738, 14, 28.6, -24.6, 0.60746, 3, 12, 94.37, 26.08, 0.00058, 13, 25.83, -35.66, 0.19862, 14, 33.88, -24.63, 0.8008, 2, 13, 23.83, -66.23, 0.00073, 14, 64.48, -26.26, 0.99927, 1, 14, 76.75, -20.97, 1, 1, 14, 65.54, -3.07, 1, 2, 13, 67.54, -47.19, 7e-05, 14, 44.9, 17.22, 0.99993, 2, 13, 76.5, -22.98, 0.1157, 14, 20.59, 25.89, 0.8843, 2, 13, 78.74, -4.74, 0.39938, 14, 2.32, 27.9, 0.60062, 2, 13, 65.43, 3.29, 0.73363, 14, -5.54, 14.5, 0.26637, 3, 12, 52.92, 58.64, 0.00084, 13, 57.9, 6.17, 0.984, 14, -8.33, 6.93, 0.01516, 2, 12, 51.19, 54.28, 0.00737, 13, 53.51, 7.85, 0.99263, 2, 12, 46.59, 51.58, 0.03227, 13, 50.76, 12.42, 0.96773, 2, 12, 38.75, 46.98, 0.11042, 13, 46.06, 20.2, 0.88958, 1, 12, 26.11, -0.56, 1, 2, 12, 52.99, 7.67, 0.35402, 13, 6.92, 5.49, 0.64598, 2, 13, 38.16, -11.75, 0.5055, 14, 9.83, -12.59, 0.4945, 1, 14, 18.2, 0.87, 1, 2, 13, 40.99, -50.45, 0.00906, 14, 48.48, -9.29, 0.99094, 2, 13, 26.79, -5.48, 0.93245, 14, 3.69, -24.03, 0.06755], "hull": 28}}, "shitouren03": {"shitouren03": {"type": "mesh", "uvs": [0.00767, 0.05234, 0.07959, 0.00316, 0.15859, 0.06129, 0.2989, 0.12091, 0.40502, 0.16861, 0.41917, 0.19097, 0.50878, 0.19544, 0.55359, 0.21333, 0.69272, 0.18202, 0.79169, 0.13322, 0.85898, 0.10004, 0.87902, 0.17457, 0.87666, 0.26251, 0.83343, 0.30233, 0.79413, 0.33853, 0.71513, 0.37431, 0.70598, 0.38774, 0.70797, 0.49689, 0.71661, 0.64887, 0.71328, 0.72863, 0.68273, 0.80504, 0.65019, 0.76809, 0.61764, 0.87808, 0.56783, 0.9293, 0.51137, 0.99059, 0.48149, 0.92426, 0.47617, 0.87976, 0.44695, 0.93098, 0.40555, 0.85763, 0.37504, 0.75094, 0.37958, 0.67543, 0.40945, 0.62782, 0.37068, 0.58685, 0.34507, 0.50738, 0.34818, 0.4024, 0.36292, 0.35236, 0.28463, 0.3273, 0.22158, 0.30712, 0.10819, 0.26694, 0.02765, 0.18924, 0.00328, 0.1035, 0.07188, 0.10497, 0.15633, 0.16531, 0.27504, 0.21637, 0.39254, 0.26897, 0.55409, 0.28754, 0.48479, 0.27958, 0.69728, 0.27207, 0.81355, 0.22256, 0.47943, 0.38501, 0.42191, 0.36799, 0.55164, 0.37418, 0.63364, 0.38346, 0.54797, 0.49022, 0.54307, 0.63565, 0.5443, 0.80894, 0.53206, 0.91414, 0.44883, 0.4469, 0.45373, 0.53045, 0.47576, 0.6372, 0.45863, 0.75324, 0.43782, 0.83678, 0.63854, 0.50105, 0.63976, 0.63565], "triangles": [55, 60, 54, 61, 29, 60, 61, 60, 55, 28, 29, 61, 26, 61, 55, 27, 61, 26, 28, 61, 27, 21, 55, 54, 22, 55, 21, 56, 26, 55, 23, 56, 55, 25, 26, 56, 22, 23, 55, 24, 25, 56, 24, 56, 23, 54, 62, 63, 63, 17, 18, 19, 63, 18, 21, 63, 19, 20, 21, 19, 21, 54, 63, 59, 31, 58, 54, 59, 58, 60, 31, 59, 30, 31, 60, 29, 30, 60, 60, 59, 54, 57, 49, 53, 58, 57, 53, 34, 50, 57, 58, 33, 57, 57, 33, 34, 58, 32, 33, 31, 32, 58, 54, 58, 53, 62, 52, 16, 62, 16, 17, 53, 52, 62, 54, 53, 62, 63, 62, 17, 52, 47, 15, 9, 10, 11, 48, 9, 11, 8, 9, 48, 12, 48, 11, 47, 8, 48, 13, 48, 12, 14, 47, 48, 13, 14, 48, 15, 47, 14, 16, 52, 15, 57, 50, 49, 53, 51, 52, 49, 50, 46, 50, 34, 35, 44, 43, 4, 44, 4, 5, 7, 8, 47, 46, 5, 6, 44, 5, 46, 45, 7, 47, 46, 6, 7, 45, 46, 7, 50, 44, 46, 51, 46, 45, 52, 45, 47, 51, 45, 52, 51, 49, 46, 35, 36, 44, 35, 44, 50, 53, 49, 51, 41, 0, 1, 41, 1, 2, 40, 0, 41, 42, 41, 2, 42, 2, 3, 39, 40, 41, 43, 42, 3, 43, 3, 4, 42, 39, 41, 38, 39, 42, 37, 42, 43, 38, 42, 37, 36, 43, 44, 37, 43, 36], "vertices": [1, 22, 7.69, -4.43, 1, 2, 22, -1.53, -10.35, 0.99801, 23, 63.14, -26.63, 0.00199, 2, 22, -12.55, -5, 0.94031, 23, 52.12, -21.28, 0.05969, 2, 22, -31.77, -0.11, 0.64324, 23, 32.9, -16.39, 0.35676, 2, 22, -46.33, 3.87, 0.30805, 23, 18.34, -12.42, 0.69195, 2, 22, -48.4, 6.09, 0.23041, 23, 16.27, -10.2, 0.76959, 3, 22, -60.41, 5.66, 0.03001, 23, 4.26, -10.62, 0.96606, 21, 41.93, 0.67, 0.00393, 3, 22, -66.54, 7.1, 0.00429, 23, -1.87, -9.18, 0.9517, 21, 35.8, 2.11, 0.04401, 2, 23, -20.21, -13.89, 0.33163, 21, 17.46, -2.6, 0.66837, 2, 23, -33.04, -20.05, 0.02051, 21, 4.62, -8.75, 0.97949, 2, 21, -4.11, -12.93, 1, 27, -27.5, 37.24, 0, 2, 21, -7.38, -5.26, 1, 27, -19.23, 38.3, 0, 2, 21, -7.76, 4.06, 0.99772, 27, -10.15, 36.14, 0.00228, 2, 23, -39.97, -2.59, 0.01265, 21, -2.3, 8.7, 0.98735, 2, 23, -35, 1.63, 0.05838, 21, 2.66, 12.93, 0.94162, 2, 23, -24.73, 6.21, 0.23856, 21, 12.93, 17.5, 0.76144, 3, 23, -23.62, 7.72, 0.13683, 21, 14.05, 19.01, 0.38176, 27, -1.68, 11.09, 0.48141, 3, 21, 12.91, 30.53, 0.00487, 27, 9.71, 9.06, 0.99422, 28, -10.74, 6.16, 0.00091, 2, 27, 25.73, 6.99, 0.06677, 28, 5.16, 9, 0.93323, 2, 28, 13.61, 9.44, 0.95427, 29, -6.92, 7.25, 0.04573, 2, 28, 22.09, 6.22, 0.6464, 29, 2.14, 7.77, 0.3536, 2, 28, 18.66, 1.47, 0.30318, 29, 0.94, 2.03, 0.69682, 1, 29, 13.22, 4.11, 1, 2, 29, 21.26, 1.06, 0.95044, 26, 16.06, 17.17, 0.04956, 2, 29, 30.68, -2.23, 0.80945, 26, 24.03, 11.18, 0.19055, 2, 29, 26.6, -9.22, 0.61868, 26, 18.02, 5.76, 0.38132, 2, 29, 22.88, -12.2, 0.22292, 26, 13.56, 4.05, 0.77708, 2, 29, 29.54, -12.87, 0.00057, 26, 19.7, 1.39, 0.99943, 2, 25, 26.09, -7.85, 0.009, 26, 13.3, -5.69, 0.991, 3, 24, 32.54, -15.72, 1e-05, 25, 15.07, -12.66, 0.45865, 26, 3.13, -12.11, 0.54134, 3, 24, 24.74, -13.83, 0.0612, 25, 7.04, -12.57, 0.76444, 26, -4.82, -13.23, 0.17436, 3, 24, 20.41, -9.07, 0.44525, 25, 1.75, -8.9, 0.53531, 26, -10.61, -10.4, 0.01944, 2, 24, 15.28, -13.5, 0.87577, 25, -2.25, -14.36, 0.12423, 3, 22, -41.02, 40.28, 0.00228, 24, 6.42, -15.53, 0.9885, 25, -10.43, -18.33, 0.00921, 3, 22, -40.6, 29.15, 0.20504, 23, 24.08, 12.87, 0.33196, 24, -4.5, -13.33, 0.46299, 2, 22, -42.17, 23.71, 0.36238, 23, 22.51, 7.43, 0.63762, 2, 22, -31.51, 21.85, 0.59225, 23, 33.17, 5.57, 0.40775, 2, 22, -22.92, 20.35, 0.7399, 23, 41.75, 4.07, 0.2601, 2, 22, -7.45, 17.24, 0.92231, 23, 57.22, 0.96, 0.07769, 2, 22, 3.93, 9.84, 0.99137, 23, 68.6, -6.44, 0.00863, 1, 22, 7.87, 1.02, 1, 3, 22, -1.31, 0.49, 0.99259, 23, 63.36, -15.79, 0.00358, 24, -41.57, -44.79, 0.00383, 2, 22, -13.07, 6.02, 0.9039, 23, 51.6, -10.26, 0.0961, 2, 22, -29.34, 10.22, 0.6545, 23, 35.33, -6.06, 0.3455, 2, 22, -45.46, 14.6, 0.27424, 23, 19.21, -1.68, 0.72576, 2, 23, -2.52, -1.34, 0.97753, 21, 35.14, 9.95, 0.02247, 2, 22, -57.87, 14.79, 0.03782, 23, 6.8, -1.49, 0.96218, 3, 23, -21.53, -4.42, 0.23967, 21, 16.13, 6.88, 0.4433, 27, -13.93, 12.38, 0.31704, 3, 23, -36.68, -10.82, 0.00563, 21, 0.99, 0.47, 0.98411, 27, -15.98, 28.7, 0.01027, 3, 22, -58, 25.99, 0.01885, 23, 6.68, 9.71, 0.97315, 24, -3.49, 4.33, 0.008, 2, 22, -50.18, 24.77, 0.17785, 23, 14.5, 8.49, 0.82215, 2, 23, -2.88, 7.84, 0.99497, 21, 34.78, 19.13, 0.00503, 3, 23, -13.92, 7.99, 0.70499, 21, 23.75, 19.29, 0.27809, 24, -0.32, 24.75, 0.01692, 5, 23, -3.32, 20.14, 0.03355, 27, 4.76, -11.82, 0.4554, 24, 9, 11.6, 0.50119, 25, -14, 8.68, 0.00228, 28, -9.2, -15.23, 0.00758, 5, 27, 19.74, -15.52, 0.10492, 24, 24.11, 8.47, 0.20422, 25, 1.43, 9.02, 0.43053, 28, 6.2, -14.27, 0.23848, 29, -4, -17.43, 0.02185, 4, 25, 19.75, 10.37, 0.07338, 28, 24.45, -12.19, 0.01735, 29, 11.8, -8.07, 0.60355, 26, 4.28, 11.36, 0.30572, 2, 29, 22.28, -3.9, 0.80178, 26, 15.52, 12.15, 0.19822, 2, 22, -54.4, 32.84, 0.00036, 24, 2.33, -0.77, 0.99964, 2, 24, 11.17, -1.55, 0.99716, 25, -8.93, -3.64, 0.00284, 4, 27, 18.11, -24.39, 0.00018, 25, 2.17, 0.03, 0.99932, 28, 7.3, -23.23, 0.00045, 29, 0.66, -25.15, 5e-05, 2, 25, 14.59, -1.47, 0.25486, 26, 0.97, -1.11, 0.74514, 2, 25, 23.61, -3.68, 0.00448, 26, 10.21, -1.94, 0.99552, 3, 27, 8.3, -0.15, 0.99869, 24, 12.08, 23.39, 0.00124, 25, -13.63, 20.87, 7e-05, 5, 27, 22.31, -2.82, 0.03008, 24, 26.19, 21.26, 0.0075, 25, 0.59, 21.95, 0.01286, 28, 4.84, -1.39, 0.94939, 29, -10.5, -6.22, 0.00018], "hull": 41}}, "shitouren04": {"shitouren04": {"type": "mesh", "uvs": [0.32564, 0, 0.53415, 0.00689, 0.6472, 0.06029, 0.71252, 0.16838, 0.6472, 0.28282, 0.70247, 0.33242, 0.66981, 0.40108, 0.80798, 0.48247, 0.80507, 0.59756, 0.8019, 0.60719, 0.859, 0.61626, 1, 0.70946, 0.87839, 0.80695, 0.83599, 0.81738, 0.8372, 0.83087, 0.8568, 0.90361, 0.78607, 0.97701, 0.62457, 0.9967, 0.45136, 0.98247, 0.31566, 0.92611, 0.31392, 0.88031, 0.34523, 0.82923, 0.35393, 0.81162, 0.3348, 0.7579, 0.23389, 0.71035, 0.29304, 0.69361, 0.27216, 0.65486, 0.27738, 0.63901, 0.19387, 0.55359, 0.21671, 0.45901, 0.10725, 0.36104, 0, 0.25442, 0.00606, 0.12899, 0.14236, 0.02655, 0.36331, 0.10181, 0.3819, 0.21366, 0.41494, 0.31923, 0.46863, 0.42689, 0.5471, 0.59622, 0.55123, 0.61399, 0.5946, 0.70702, 0.61318, 0.79378, 0.62144, 0.81468, 0.5946, 0.89726], "triangles": [18, 43, 17, 17, 43, 16, 18, 19, 43, 16, 43, 15, 14, 43, 42, 43, 14, 15, 19, 20, 43, 20, 21, 43, 43, 21, 42, 42, 13, 14, 42, 21, 22, 28, 29, 37, 7, 37, 6, 37, 29, 36, 29, 30, 36, 37, 36, 6, 6, 36, 5, 5, 36, 4, 30, 31, 35, 30, 35, 36, 35, 32, 34, 32, 35, 31, 36, 35, 4, 4, 35, 3, 32, 33, 34, 3, 34, 2, 3, 35, 34, 33, 0, 34, 34, 1, 2, 34, 0, 1, 42, 22, 41, 40, 12, 13, 13, 42, 41, 22, 23, 41, 11, 12, 40, 23, 40, 41, 40, 13, 41, 23, 24, 40, 24, 25, 40, 11, 40, 10, 40, 9, 10, 25, 39, 40, 40, 39, 9, 25, 26, 39, 26, 27, 39, 27, 38, 39, 27, 28, 38, 39, 38, 9, 9, 38, 8, 8, 38, 7, 38, 28, 37, 7, 38, 37], "vertices": [1, 18, -40.26, 5.74, 1, 1, 18, -32.8, 30.33, 1, 1, 18, -17, 40.92, 1, 1, 18, 10.41, 42.76, 1, 1, 18, 35.66, 28.61, 1, 2, 19, -23.46, 31.13, 0.0235, 18, 48.94, 32.49, 0.9765, 2, 19, -7.69, 24.36, 0.31704, 18, 64.27, 24.78, 0.68296, 2, 19, 14.67, 37.78, 0.9277, 18, 87.4, 36.83, 0.0723, 2, 19, 42.18, 32.71, 0.99997, 20, -50.76, 27.54, 3e-05, 1, 19, 44.42, 31.93, 1, 1, 19, 47.78, 38.48, 1, 1, 19, 73.03, 51.76, 1, 1, 19, 93.85, 33.02, 1, 1, 19, 95.47, 27.45, 1, 2, 19, 98.73, 27.05, 0.33061, 20, 6.06, 28.88, 0.66939, 2, 19, 116.56, 26.44, 0.03189, 20, 23.83, 30.48, 0.96811, 1, 20, 41.25, 20.97, 1, 1, 20, 45.11, 0.91, 1, 1, 20, 40.68, -20.22, 1, 2, 19, 110.72, -40.09, 0.01726, 20, 26.23, -36.26, 0.98274, 2, 19, 99.72, -38.42, 0.07377, 20, 15.11, -35.96, 0.92623, 2, 19, 88.13, -32.53, 0.2926, 20, 2.88, -31.55, 0.7074, 1, 19, 84.1, -30.76, 1, 1, 19, 70.83, -30.87, 1, 1, 19, 57.35, -41.16, 1, 1, 19, 54.57, -33.3, 1, 2, 19, 44.85, -34.25, 0.9992, 18, 113.19, -36.88, 0.0008, 2, 19, 41.17, -32.96, 0.99675, 18, 109.59, -35.38, 0.00325, 2, 19, 18.97, -39.59, 0.89262, 18, 87.04, -40.66, 0.10738, 2, 19, -3.21, -32.95, 0.42178, 18, 65.3, -32.69, 0.57822, 2, 19, -28.94, -42.2, 0.02204, 18, 39.05, -40.39, 0.97796, 1, 18, 10.82, -47.34, 1, 1, 18, -18.69, -39.67, 1, 1, 18, -39.11, -17.68, 1, 1, 18, -15.12, 4.62, 1, 1, 18, 11.87, 0.66, 1, 1, 18, 37.77, -1.22, 1, 1, 18, 64.75, -0.74, 1, 1, 19, 36.51, 1.49, 1, 1, 19, 40.85, 1.26, 1, 1, 19, 64.03, 2.71, 1, 1, 19, 85.2, 1.41, 1, 2, 19, 90.38, 1.55, 0.10146, 20, 0.91, 2.55, 0.89854, 1, 20, 20.81, -1.67, 1], "hull": 34}}, "shitouren05": {"shitouren05": {"type": "mesh", "uvs": [0.91492, 0.20614, 1, 0.42477, 1, 0.6301, 0.89527, 0.82212, 0.78091, 0.92478, 0.74874, 0.99892, 0.60758, 1, 0.37886, 0.98752, 0.16979, 0.89626, 0.02863, 0.74037, 0, 0.52363, 0, 0.32021, 0.16979, 0.12439, 0.32883, 0.02553, 0.5236, 0, 0.70943, 0.05785, 0.72771, 0.77774, 0.50822, 0.7762, 0.34106, 0.61381, 0.14338, 0.4437, 0.9312, 0.5767, 0.78294, 0.56587, 0.52857, 0.46999, 0.82219, 0.34627, 0.51985, 0.22255, 0.25821, 0.28286, 0.15501, 0.71124, 0.2553, 0.76073], "triangles": [20, 1, 2, 25, 12, 13, 11, 12, 25, 19, 11, 25, 10, 11, 19, 18, 25, 22, 19, 25, 18, 10, 19, 26, 1, 20, 23, 25, 24, 22, 24, 13, 14, 23, 15, 0, 24, 15, 23, 23, 0, 1, 22, 24, 23, 21, 22, 23, 20, 21, 23, 17, 22, 21, 16, 17, 21, 3, 21, 20, 3, 20, 2, 16, 21, 3, 4, 16, 3, 24, 14, 15, 25, 13, 24, 26, 19, 18, 27, 26, 18, 8, 26, 27, 17, 18, 22, 27, 18, 17, 7, 27, 17, 8, 27, 7, 4, 6, 16, 6, 17, 16, 5, 6, 4, 7, 17, 6, 9, 26, 8, 9, 10, 26], "vertices": [3, 5, 103.15, 2.79, 0.5213, 7, 29.34, 34.4, 0.1787, 31, 83.5, 39.85, 0.3, 2, 7, 1.2, 4.99, 0.7, 31, 66.75, 2.75, 0.3, 3, 5, 42.69, -40.86, 0.5544, 7, -31.12, -9.24, 0.1456, 31, 41.01, -21.43, 0.3, 3, 4, 60.58, -32.97, 0.12489, 5, 4.74, -36.63, 0.57511, 31, 3.82, -30.07, 0.3, 3, 4, 33.38, -29.8, 0.57282, 5, -19.86, -24.6, 0.12718, 31, -23.38, -26.91, 0.3, 3, 4, 20.05, -34.24, 0.6771, 5, -33.9, -24.35, 0.0229, 31, -36.71, -31.35, 0.3, 3, 4, 2.23, -15.54, 0.56, 30, -25.43, -8.46, 0.14, 31, -54.53, -12.65, 0.3, 3, 4, -24.86, 16.43, 0.56, 30, -52.52, 23.51, 0.14, 31, -81.62, 19.33, 0.3, 3, 4, -39.62, 55.07, 0.63, 30, -67.28, 62.15, 0.07, 31, -96.38, 57.96, 0.3, 4, 4, -37.76, 92.25, 0.59204, 6, -75.29, 5.49, 0.07296, 30, -65.42, 99.33, 0.035, 31, -94.52, 95.15, 0.3, 4, 4, -14.18, 121.6, 0.09476, 6, -43.29, 25.31, 0.57024, 30, -41.84, 128.68, 0.035, 31, -70.94, 124.49, 0.3, 4, 4, 11.33, 145.55, 0.00953, 6, -11.27, 39.42, 0.65547, 30, -16.34, 152.63, 0.035, 31, -45.44, 148.45, 0.3, 4, 5, 61.05, 133.24, 0.13355, 6, 32.08, 24.56, 0.49645, 30, 29.49, 153.04, 0.07, 31, 0.39, 148.86, 0.3, 3, 5, 88.34, 113.46, 0.5768, 6, 59.37, 4.78, 0.1232, 31, 32.71, 139.29, 0.3, 2, 5, 106.73, 82.61, 0.7, 31, 60.31, 116.32, 0.3, 3, 5, 111.33, 47.48, 0.56, 30, 105.44, 88.9, 0.14, 31, 76.34, 84.72, 0.3, 3, 4, 45.15, -5.39, 0.36293, 5, -0.64, -5.49, 0.43707, 30, 17.49, 1.69, 0.2, 3, 4, 17.84, 24.07, 0.74749, 5, -16.58, 31.37, 0.05251, 30, -9.82, 31.15, 0.2, 4, 4, 17.25, 65.48, 0.64207, 5, -3.35, 70.63, 0.14062, 6, -32.33, -38.06, 0.11731, 30, -10.41, 72.56, 0.1, 4, 4, 13.81, 111.88, 0.07088, 5, 8.84, 115.53, 0.00082, 6, -20.13, 6.84, 0.8783, 30, -13.85, 118.96, 0.05, 2, 5, 46.02, -25.63, 0.576, 7, -27.79, 5.98, 0.424, 2, 5, 36.79, -0.05, 0.8, 30, 50.97, 19.27, 0.2, 4, 4, 58.78, 57.41, 0.17625, 5, 33.12, 49.2, 0.56534, 6, 4.15, -59.49, 0.05841, 30, 31.12, 64.49, 0.2, 3, 5, 74.25, 8.6, 0.69581, 7, 0.44, 40.21, 0.10419, 30, 83.41, 39.9, 0.2, 4, 4, 88.7, 87.72, 0.0139, 5, 71.42, 67.81, 0.71056, 6, 42.45, -40.87, 0.07553, 30, 61.04, 94.8, 0.2, 4, 4, 48.36, 115.51, 0.00093, 5, 42.63, 107.45, 0.04086, 6, 13.66, -1.24, 0.85822, 30, 20.7, 122.59, 0.1, 4, 4, -18.27, 78.83, 0.85427, 5, -32.42, 95.03, 0.00453, 6, -61.39, -13.65, 0.0912, 30, -45.93, 85.91, 0.05, 2, 4, -11.91, 59.62, 0.98392, 5, -32.81, 74.8, 0.01608], "hull": 16}}, "shitouren06": {"shitouren06": {"type": "mesh", "uvs": [0.37555, 0.00783, 0.56089, 0.07007, 0.76364, 0.13816, 0.98745, 0.25772, 1, 0.31298, 0.97189, 0.46812, 0.89912, 0.62424, 0.84723, 0.75191, 0.82356, 0.87833, 0.78903, 0.96393, 0.72063, 0.9743, 0.66897, 0.98214, 0.53027, 0.98852, 0.43097, 0.9931, 0.29715, 0.88576, 0.18341, 0.79451, 0.08761, 0.73668, 0.08769, 0.61619, 0.08775, 0.52539, 0.0845, 0.50811, 0.01082, 0.39033, 0.01079, 0.28302, 0.11112, 0.04072, 0.23283, 0.00826, 0.39437, 0.28807, 0.59514, 0.33298, 0.77806, 0.34556, 0.89629, 0.33119, 0.73791, 0.25035, 0.60406, 0.18208, 0.43464, 0.09694, 0.30068, 0.21262, 0.19583, 0.1192, 0.73322, 0.53289, 0.74104, 0.63528, 0.70583, 0.74712, 0.6804, 0.88416, 0.55518, 0.76564, 0.41341, 0.79035, 0.54203, 0.88569, 0.42218, 0.87863, 0.39065, 0.44007, 0.57335, 0.49186, 0.83788, 0.51069, 0.82988, 0.61966, 0.75867, 0.74789, 0.55492, 0.62674, 0.41422, 0.65711, 0.29818, 0.55665, 0.26481, 0.70617, 0.49475, 0.22619, 0.30475, 0.10079, 0.85332, 0.2492, 0.67047, 0.16177, 0.50475, 0.08239, 0.20332, 0.26761, 0.19332, 0.40681, 0.20904, 0.54141, 0.16904, 0.63805, 0.27475, 0.783, 0.73904, 0.88194], "triangles": [54, 0, 1, 30, 0, 54, 51, 23, 0, 51, 0, 30, 32, 22, 23, 32, 23, 51, 53, 1, 2, 54, 1, 53, 29, 54, 53, 30, 54, 29, 31, 32, 51, 50, 30, 29, 51, 30, 50, 3, 52, 2, 53, 2, 52, 28, 53, 52, 29, 53, 28, 55, 32, 31, 21, 22, 32, 21, 32, 55, 50, 31, 51, 24, 31, 50, 27, 52, 3, 27, 3, 4, 25, 50, 29, 25, 29, 28, 24, 50, 25, 26, 28, 52, 26, 52, 27, 25, 28, 26, 56, 20, 21, 55, 56, 21, 24, 55, 31, 41, 24, 25, 24, 56, 55, 41, 56, 24, 5, 27, 4, 42, 41, 25, 19, 20, 56, 43, 26, 27, 43, 27, 5, 57, 18, 19, 26, 42, 25, 33, 26, 43, 33, 42, 26, 48, 57, 56, 57, 19, 56, 41, 48, 56, 17, 18, 57, 44, 33, 43, 6, 43, 5, 44, 43, 6, 42, 47, 41, 46, 42, 33, 47, 48, 41, 34, 33, 44, 46, 33, 34, 58, 17, 57, 42, 46, 47, 48, 58, 57, 49, 48, 47, 49, 58, 48, 16, 17, 58, 16, 58, 49, 35, 46, 34, 45, 34, 44, 35, 34, 45, 7, 45, 44, 6, 7, 44, 37, 46, 35, 47, 46, 37, 38, 59, 49, 47, 38, 49, 15, 16, 49, 37, 38, 47, 59, 15, 49, 8, 45, 7, 39, 40, 38, 60, 35, 45, 60, 45, 8, 36, 37, 35, 36, 35, 60, 37, 39, 38, 36, 39, 37, 14, 59, 38, 14, 38, 40, 15, 59, 14, 9, 60, 8, 10, 36, 60, 10, 60, 9, 11, 39, 36, 11, 36, 10, 12, 40, 39, 12, 39, 11, 13, 40, 12, 14, 40, 13], "vertices": [2, 8, 82.57, 15.11, 0.7, 33, 56.28, 41.23, 0.3, 2, 8, 79.71, -2.94, 0.7, 33, 53.42, 23.18, 0.3, 2, 8, 76.58, -22.68, 0.7, 33, 50.29, 3.43, 0.3, 2, 8, 68.25, -45.66, 0.7, 33, 41.96, -19.54, 0.3, 2, 8, 62.45, -48.24, 0.7, 33, 36.16, -22.12, 0.3, 2, 8, 44.81, -49.88, 0.7, 33, 18.52, -23.77, 0.3, 2, 8, 26.1, -47.6, 0.7, 33, -0.18, -21.49, 0.3, 3, 8, 10.97, -46.41, 0.6384, 33, -15.32, -20.3, 0.2736, 32, 10.79, -17.21, 0.088, 2, 8, -3.42, -47.69, 0.7, 33, -29.71, -21.57, 0.3, 2, 8, -13.56, -46.91, 0.7, 33, -39.85, -20.8, 0.3, 3, 8, -16.17, -41.14, 0.6608, 33, -42.45, -15.02, 0.2832, 32, -16.35, -11.93, 0.056, 3, 8, -18.14, -36.78, 0.6048, 33, -44.42, -10.66, 0.2592, 32, -18.32, -7.57, 0.136, 3, 8, -21.81, -24.68, 0.5712, 33, -48.1, 1.43, 0.2448, 32, -21.99, 4.52, 0.184, 3, 8, -24.44, -16.02, 0.6216, 33, -50.73, 10.09, 0.2664, 32, -24.62, 13.18, 0.112, 3, 8, -15.52, -1.33, 0.6776, 33, -41.81, 24.79, 0.2904, 32, -15.7, 27.87, 0.032, 2, 8, -7.94, 11.16, 0.7, 33, -34.23, 37.27, 0.3, 2, 8, -3.64, 21.17, 0.7, 33, -29.93, 47.29, 0.3, 2, 8, 9.59, 24.37, 0.7, 33, -16.7, 50.49, 0.3, 2, 8, 19.56, 26.78, 0.7, 33, -6.72, 52.9, 0.3, 2, 8, 21.39, 27.53, 0.7, 33, -4.9, 53.65, 0.3, 2, 8, 32.75, 37.18, 0.7, 33, 6.46, 63.3, 0.3, 2, 8, 44.53, 40.04, 0.7, 33, 18.24, 66.16, 0.3, 2, 8, 73.29, 37.62, 0.7, 33, 47, 63.74, 0.3, 2, 8, 79.46, 27.72, 0.7, 33, 53.18, 53.84, 0.3, 2, 8, 52.2, 5.98, 0.812, 32, 52.02, 35.19, 0.188, 2, 8, 51.57, -12.97, 0.8, 32, 51.39, 16.24, 0.2, 2, 8, 54.11, -29.48, 0.7, 32, 53.93, -0.27, 0.3, 2, 8, 58.22, -39.55, 0.88, 32, 58.05, -10.35, 0.12, 2, 8, 63.71, -23.39, 0.7, 32, 63.53, 5.81, 0.3, 2, 8, 68.33, -9.74, 0.7, 32, 68.15, 19.47, 0.3, 2, 8, 74.05, 7.51, 0.94, 32, 73.87, 36.72, 0.06, 2, 8, 58.48, 16.28, 0.952, 32, 58.3, 45.48, 0.048, 2, 8, 66.49, 28.04, 0.952, 32, 66.31, 57.24, 0.048, 2, 8, 32.58, -30.5, 0.7, 32, 32.4, -1.3, 0.3, 2, 8, 21.5, -33.92, 0.7, 32, 21.32, -4.71, 0.3, 2, 8, 8.46, -33.78, 0.7, 32, 8.28, -4.58, 0.3, 2, 8, -7.13, -35.18, 0.7, 32, -7.31, -5.98, 0.3, 2, 8, 3.2, -20.95, 0.8, 32, 3.02, 8.25, 0.2, 2, 8, -2.55, -9.07, 0.86, 32, -2.73, 20.13, 0.14, 2, 8, -10.27, -22.98, 0.8, 32, -10.45, 6.22, 0.2, 2, 8, -12.06, -12.2, 0.86, 32, -12.24, 17.01, 0.14, 2, 8, 35.43, 2.27, 0.828, 32, 35.25, 31.47, 0.172, 2, 8, 33.66, -15.27, 0.8, 32, 33.48, 13.93, 0.2, 2, 8, 37.26, -39.17, 0.912, 32, 37.08, -9.96, 0.088, 2, 8, 25.12, -41.36, 0.88, 32, 24.94, -12.15, 0.12, 2, 8, 9.51, -38.48, 0.784, 32, 9.33, -9.27, 0.216, 2, 8, 18.45, -17.23, 0.8, 32, 18.27, 11.97, 0.2, 2, 8, 12.1, -5.6, 0.828, 32, 11.92, 23.61, 0.172, 2, 8, 20.64, 7.34, 0.86, 32, 20.46, 36.55, 0.14, 2, 8, 3.51, 6.31, 0.9, 32, 3.33, 35.52, 0.1, 2, 8, 61.15, -1.25, 0.8, 32, 60.97, 27.96, 0.2, 2, 8, 70.84, 18.9, 0.952, 32, 70.66, 48.1, 0.048, 2, 8, 66.31, -33.57, 0.912, 32, 66.13, -4.37, 0.088, 2, 8, 71.99, -15.07, 0.96, 32, 71.81, 14.13, 0.04, 2, 8, 77.15, 1.7, 0.936, 32, 76.97, 30.9, 0.064, 2, 8, 50.35, 23.43, 0.9, 32, 50.17, 52.63, 0.1, 2, 8, 34.85, 20.6, 0.9, 32, 34.67, 49.81, 0.1, 2, 8, 20.4, 15.63, 0.9, 32, 20.22, 44.83, 0.1, 2, 8, 8.93, 16.59, 0.95, 32, 8.75, 45.8, 0.05, 2, 8, -4.72, 3.39, 0.9, 32, -4.9, 32.59, 0.1, 2, 8, -5.63, -40.31, 0.872, 32, -5.81, -11.1, 0.128], "hull": 24}}, "shitouren07": {"shitouren07": {"type": "mesh", "uvs": [0.75375, 0, 0.94196, 0.06871, 1, 0.17886, 0.95256, 0.28309, 0.80677, 0.33757, 0.77496, 0.4264, 0.70604, 0.4868, 0.76435, 0.54247, 0.75268, 0.58074, 0.74845, 0.59458, 0.80412, 0.61117, 0.95786, 0.6917, 0.86618, 0.78519, 0.85448, 0.79712, 0.93666, 0.8362, 0.94461, 0.88002, 0.95521, 0.92266, 0.82797, 0.9878, 0.66362, 1, 0.37999, 1, 0.24745, 0.92621, 0.2371, 0.85785, 0.22771, 0.84646, 0.13248, 0.80391, 0, 0.76856, 0.10432, 0.70863, 0.107, 0.67567, 0.15663, 0.65709, 0.14321, 0.6457, 0.04396, 0.55521, 0.04128, 0.50547, 0.13651, 0.42239, 0.07507, 0.30959, 0.15792, 0.25498, 0.11442, 0.16891, 0.19107, 0.08376, 0.47485, 0, 0.55629, 0.14901, 0.48086, 0.29812, 0.42372, 0.42782, 0.52643, 0.20804, 0.62029, 0.07344, 0.45142, 0.36496, 0.39629, 0.51872, 0.43058, 0.60246, 0.43515, 0.61982, 0.46258, 0.7291, 0.50143, 0.79242, 0.51058, 0.81489, 0.55858, 0.91599], "triangles": [48, 47, 13, 21, 22, 48, 49, 48, 13, 21, 48, 49, 20, 21, 49, 49, 15, 17, 13, 15, 49, 14, 15, 13, 17, 15, 16, 19, 20, 49, 18, 49, 17, 19, 49, 18, 41, 36, 0, 41, 0, 1, 37, 36, 41, 35, 36, 37, 40, 35, 37, 34, 35, 40, 33, 34, 40, 2, 41, 1, 2, 37, 41, 2, 40, 37, 3, 40, 2, 38, 33, 40, 4, 38, 40, 3, 4, 40, 42, 33, 38, 42, 38, 4, 32, 33, 42, 31, 32, 42, 5, 42, 4, 39, 31, 42, 39, 42, 5, 6, 39, 5, 43, 31, 39, 43, 39, 6, 30, 31, 43, 29, 30, 43, 6, 44, 43, 6, 7, 44, 7, 8, 44, 9, 44, 8, 28, 29, 43, 45, 44, 9, 44, 28, 43, 28, 44, 45, 27, 28, 45, 46, 45, 9, 27, 45, 46, 46, 9, 10, 46, 10, 11, 25, 26, 27, 25, 27, 46, 24, 25, 46, 12, 46, 11, 47, 46, 12, 13, 47, 12, 23, 24, 46, 22, 23, 46, 47, 22, 46, 22, 47, 48], "vertices": [1, 15, -45.13, 11.15, 1, 1, 15, -33.5, 39.48, 1, 1, 15, -5.91, 55.52, 1, 2, 16, -19.52, 76.12, 0.00017, 15, 23.95, 58.36, 0.99983, 1, 15, 44, 45.25, 1, 2, 16, 10.44, 49.54, 0.344, 15, 62.82, 48.72, 0.656, 2, 16, 20.47, 40.51, 0.65753, 15, 75.9, 45.38, 0.34247, 3, 16, 34.31, 46.35, 0.83929, 15, 85.44, 56.98, 0.16035, 17, -63.6, 46.09, 0.00035, 1, 16, 42.03, 44.27, 1, 1, 16, 45.77, 43.02, 1, 1, 16, 51.67, 49.04, 1, 1, 16, 77.59, 63.84, 1, 1, 16, 101.34, 47.58, 1, 2, 16, 104.37, 45.5, 0.38882, 17, 6.46, 45.53, 0.61118, 2, 16, 117.13, 53.62, 0.14678, 17, 19.19, 53.71, 0.85322, 2, 16, 129.46, 52.31, 0.06829, 17, 31.52, 52.44, 0.93171, 2, 16, 141.52, 51.38, 0.02573, 17, 43.59, 51.57, 0.97427, 2, 16, 156.58, 32.21, 0.00011, 17, 58.73, 32.46, 0.99989, 1, 17, 58.34, 11.47, 1, 1, 17, 51.84, -23.67, 1, 2, 16, 125.9, -36.42, 0.03807, 17, 28.33, -36.3, 0.96193, 2, 16, 106.71, -34.11, 0.31813, 17, 9.14, -34.08, 0.68187, 1, 16, 103.34, -34.68, 1, 1, 16, 89.31, -44.23, 1, 1, 16, 76.41, -58.78, 1, 1, 16, 62.25, -42.71, 1, 2, 16, 53.18, -40.65, 0.99122, 17, -44.36, -40.84, 0.00878, 2, 16, 49.2, -33.53, 0.9981, 17, -48.37, -33.74, 0.0019, 2, 16, 45.73, -34.59, 0.99969, 17, -51.84, -34.81, 0.00031, 2, 16, 18.77, -42.36, 0.97294, 15, 112.86, -28.81, 0.02706, 2, 16, 6.37, -40.84, 0.88374, 15, 101.17, -33.21, 0.11626, 2, 16, -4.36, -29.97, 0.26443, 15, 86.62, -28.57, 0.73557, 1, 15, 63.29, -45.24, 1, 1, 15, 45.52, -39.74, 1, 1, 15, 23.89, -52.05, 1, 1, 15, -1.89, -49.8, 1, 1, 15, -34.9, -22.47, 1, 1, 15, 2.32, -0.42, 1, 2, 16, -26.42, 16.93, 0.0001, 15, 45.31, 2.73, 0.9999, 2, 16, 7.09, 3.62, 0.06701, 15, 81.17, 6.49, 0.93299, 1, 15, 19.33, 0.83, 1, 1, 15, -20.42, 1.09, 1, 2, 16, -8.67, 9.82, 0.00491, 15, 64.33, 4.67, 0.99509, 2, 16, 16.82, 3.23, 0.97949, 15, 89.97, 10.67, 0.02051, 2, 16, 40.52, 3.24, 0.99853, 15, 110.95, 21.68, 0.00147, 2, 16, 45.42, 2.9, 0.99939, 15, 115.45, 23.65, 0.00061, 1, 16, 76.33, 0.56, 1, 1, 16, 94.79, 2.05, 1, 2, 16, 101.23, 2, 0.03323, 17, 3.5, 2.01, 0.96677, 1, 17, 32.64, 2.77, 1], "hull": 37}}}}], "animations": {"str_defeat": {"slots": {"ren_bg_2": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff"}]}, "ren_bg_1": {"color": [{"color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffffac"}]}, "ren_bg_01": {"color": [{"color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffff52"}]}, "ren_bg_02": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 39.57, "y": -47.02}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.15}]}, "bone3": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -5.15}]}, "bone6": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -33.01}]}, "bone7": {"rotate": [{"angle": -0.03}]}, "bone8": {"rotate": [{"angle": -0.39}]}, "bone9": {"rotate": [{"angle": 0.37}]}, "bone10": {"rotate": [{"angle": -2.72}]}, "bone11": {"rotate": [{"angle": 1.24}]}, "bone12": {"rotate": [{"angle": -0.99}]}, "bone13": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.71}]}, "bone14": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 37}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -26.72}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 73.17}]}, "bone37": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 37}]}, "bone36": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.71}]}, "bone41": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 73.17}]}, "bone40": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -26.72}]}, "bone43": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -33.01}]}, "bone34": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.15}]}, "bone33": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -5.15}]}, "bone50": {"rotate": [{"time": 0.0667, "angle": 0.37}]}, "bone49": {"rotate": [{"time": 0.0667, "angle": -0.39}]}, "bone48": {"rotate": [{"time": 0.0667, "angle": -0.03}]}, "bone53": {"rotate": [{"time": 0.0667, "angle": -0.99}]}, "bone52": {"rotate": [{"time": 0.0667, "angle": 1.24}]}, "bone51": {"rotate": [{"time": 0.0667, "angle": -2.72}]}, "bone32": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 39.57, "y": -47.02}]}, "bone63": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "bone66": {"translate": [{"x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.95}], "scale": [{"y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 1.973}]}, "bone65": {"translate": [{"x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": 2.73}], "scale": [{"y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "y": 1.292}]}, "bone64": {"translate": [{"x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "x": 0.68}], "scale": [{"y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "y": 1.055}]}, "bone89": {"scale": [{"y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 1.104}]}, "bone85": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -15.04, "y": 10.34, "curve": "stepped"}, {"time": 4}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 4}]}, "bone78": {"rotate": [{"angle": -5.79, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.78, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -5.79}]}, "bone76": {"rotate": [{"angle": -5.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.6667, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -6.7, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -5.79}]}, "bone92": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4}]}, "bone71": {"rotate": [{"angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.78}]}, "bone91": {"translate": [{"x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": 1.59}], "scale": [{"y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "y": 1.17}]}, "bone86": {"translate": [{"x": 16.68, "y": 8.65, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.7, "x": 18.63, "y": 9.66, "curve": "stepped"}, {"time": 0.7333, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 4, "x": 16.68, "y": 8.65}], "scale": [{"x": 1.133, "y": 1.133, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.7, "x": 1.149, "y": 1.149, "curve": "stepped"}, {"time": 0.7333, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 4, "x": 1.133, "y": 1.133}]}, "bone90": {"translate": [{"x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": 1.77}], "scale": [{"y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "y": 1.142}]}, "bone72": {"rotate": [{"angle": 1.87, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -6.7, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 1.87}]}, "bone75": {"rotate": [{"angle": -4.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -4.01}]}, "bone79": {"rotate": [{"angle": -4.07, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.2667, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 4.53, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "angle": -4.07}]}, "bone77": {"rotate": [{"angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.7}]}, "bone73": {"rotate": [{"angle": 0.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -6.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.09}]}, "bone74": {"rotate": [{"angle": -1.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -6.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.96}]}, "bone82": {"rotate": [{"angle": 1.53, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.2667, "angle": -0.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 4.53, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "angle": 1.53}]}, "bone80": {"rotate": [{"angle": -2.51, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 0.2667, "angle": -3.82, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 4.53, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 4, "angle": -2.51}]}, "bone83": {"rotate": [{"angle": 3.35, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.2667, "angle": 1.91, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.6, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 4.53, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 3.35}]}, "bone81": {"rotate": [{"angle": -0.51, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.2667, "angle": -2.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9333, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 4.53, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": -0.51}]}, "bone84": {"rotate": [{"angle": 4.48, "curve": 0.301, "c2": 0.12, "c3": 0.638, "c4": 0.48}, {"time": 0.2667, "angle": 3.64, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.9333, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 4.53, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 4, "angle": 4.48}]}, "bone87": {"translate": [{"x": -7.6, "y": 5.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": -15.04, "y": 10.34, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -7.6, "y": 5.23}], "scale": [{"x": 1.076, "y": 1.076, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.076, "y": 1.076}]}, "bone88": {"translate": [{"x": 4.94, "y": 2.56, "curve": 0.323, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7333, "x": 9.42, "y": 4.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "x": 18.63, "y": 9.66, "curve": "stepped"}, {"time": 2.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 4.94, "y": 2.56}], "scale": [{"x": 1.039, "y": 1.039, "curve": 0.323, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7333, "x": 1.075, "y": 1.075, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "x": 1.149, "y": 1.149, "curve": "stepped"}, {"time": 2.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 1.039, "y": 1.039}]}}}, "str_idle": {"slots": {"ren_bg_2": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff"}]}, "ren_bg_1": {"color": [{"color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffffac"}]}, "ren_bg_01": {"color": [{"color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffff52"}]}, "ren_bg_02": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.03, "y": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -0.03, "y": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "bone3": {"rotate": [{"angle": 0.75, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.75, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.75}]}, "bone4": {"translate": [{"x": 1.46, "y": 2.69, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 2.48, "y": 4.57, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "x": 1.46, "y": 2.69, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": 2.48, "y": 4.57, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "x": 1.46, "y": 2.69}]}, "bone5": {"translate": [{"x": 2.82, "y": -1.2, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 4.79, "y": -2.03, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "x": 2.82, "y": -1.2, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": 4.79, "y": -2.03, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "x": 2.82, "y": -1.2}]}, "bone6": {"rotate": [{"angle": 1.88, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.88, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 2.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.88}]}, "bone7": {"rotate": [{"angle": -0.03}]}, "bone8": {"rotate": [{"angle": -0.39}]}, "bone9": {"rotate": [{"angle": 0.37}]}, "bone10": {"rotate": [{"angle": -2.72}]}, "bone11": {"rotate": [{"angle": 1.24}]}, "bone12": {"rotate": [{"angle": -0.99}]}, "bone13": {"rotate": [{"angle": -2.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.71, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -7.71, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -2.19}]}, "bone14": {"rotate": [{"angle": -5.52, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5.52, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -7.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -5.52}]}, "bone16": {"rotate": [{"angle": -5.06, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.3333, "angle": -6.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.53, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2, "angle": -5.06, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 2.3333, "angle": -6.01, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -2.53, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 4, "angle": -5.06}]}, "bone17": {"rotate": [{"angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 4.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 3.34}]}, "bone22": {"rotate": [{"angle": 13.89, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 20.59, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 13.89, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": 20.59, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "angle": 13.89}]}, "bone23": {"rotate": [{"angle": 20.15, "curve": 0.267, "c2": 0.1, "c3": 0.663, "c4": 0.66}, {"time": 0.6333, "angle": 4.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 20.59, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": 20.15, "curve": 0.267, "c2": 0.1, "c3": 0.663, "c4": 0.66}, {"time": 2.6333, "angle": 4.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 20.59, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 4, "angle": 20.15}]}, "bone24": {"rotate": [{"angle": 15.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 20.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "angle": 14.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 15.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.3, "angle": 20.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.6333, "angle": 14.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 3.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": 15.6}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 20.59, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 20.59, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "bone26": {"rotate": [{"angle": 4.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 20.59, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 4.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 20.59, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": 4.99}]}, "bone27": {"rotate": [{"angle": 14.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 20.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 14.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 20.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 14.75}]}, "bone28": {"translate": [{"x": 3.89, "y": -2.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 7.78, "y": -5.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 3.89, "y": -2.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 7.78, "y": -5.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 3.89, "y": -2.66}]}, "bone30": {"translate": [{"x": 1.7, "y": -2.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.4, "y": -4.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.7, "y": -2.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 3.4, "y": -4.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.7, "y": -2.32}]}, "bone37": {"rotate": [{"angle": -5.52, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5.52, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -7.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -5.52}]}, "bone36": {"rotate": [{"angle": -2.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.71, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -7.71, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -2.19}]}, "bone35": {"translate": [{"x": 1.46, "y": 2.69, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 2.48, "y": 4.57, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "x": 1.46, "y": 2.69, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": 2.48, "y": 4.57, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "x": 1.46, "y": 2.69}]}, "bone41": {"rotate": [{"angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 4.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 3.34}]}, "bone40": {"rotate": [{"angle": -5.06, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.3333, "angle": -6.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.53, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2, "angle": -5.06, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 2.3333, "angle": -6.01, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -2.53, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 4, "angle": -5.06}]}, "bone39": {"translate": [{"x": 2.82, "y": -1.2, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 4.79, "y": -2.03, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "x": 2.82, "y": -1.2, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": 4.79, "y": -2.03, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "x": 2.82, "y": -1.2}]}, "bone44": {"translate": [{"x": 1.7, "y": -2.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.4, "y": -4.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.7, "y": -2.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 3.4, "y": -4.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.7, "y": -2.32}]}, "bone43": {"rotate": [{"angle": 1.88, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.88, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 2.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.88}]}, "bone34": {"rotate": [{"angle": 0.75, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.75, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.75}]}, "bone46": {"translate": [{"x": 3.89, "y": -2.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 7.78, "y": -5.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 3.89, "y": -2.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 7.78, "y": -5.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 3.89, "y": -2.66}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "bone50": {"rotate": [{"angle": 0.37}]}, "bone49": {"rotate": [{"angle": -0.39}]}, "bone48": {"rotate": [{"angle": -0.03}]}, "bone53": {"rotate": [{"angle": -0.99}]}, "bone52": {"rotate": [{"angle": 1.24}]}, "bone51": {"rotate": [{"angle": -2.72}]}, "bone59": {"rotate": [{"angle": 14.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 20.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 14.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 20.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 14.75}]}, "bone58": {"rotate": [{"angle": 4.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 20.59, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 4.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 20.59, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": 4.99}]}, "bone57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 20.59, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 20.59, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "bone62": {"rotate": [{"angle": 15.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 20.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "angle": 14.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 15.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.3, "angle": 20.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.6333, "angle": 14.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 3.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": 15.6}]}, "bone61": {"rotate": [{"angle": 20.15, "curve": 0.267, "c2": 0.1, "c3": 0.663, "c4": 0.66}, {"time": 0.6333, "angle": 4.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 20.59, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": 20.15, "curve": 0.267, "c2": 0.1, "c3": 0.663, "c4": 0.66}, {"time": 2.6333, "angle": 4.99, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 20.59, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 4, "angle": 20.15}]}, "bone60": {"rotate": [{"angle": 13.89, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 20.59, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 13.89, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": 20.59, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "angle": 13.89}]}, "bone32": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.03, "y": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -0.03, "y": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "bone63": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "bone66": {"translate": [{"x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.95}], "scale": [{"y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 1.973}]}, "bone65": {"translate": [{"x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": 2.73}], "scale": [{"y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "y": 1.292}]}, "bone64": {"translate": [{"x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "x": 0.68}], "scale": [{"y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "y": 1.055}]}, "bone89": {"scale": [{"y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 1.104}]}, "bone85": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -15.04, "y": 10.34, "curve": "stepped"}, {"time": 4}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 4}]}, "bone78": {"rotate": [{"angle": -5.79, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.78, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -5.79}]}, "bone76": {"rotate": [{"angle": -5.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.6667, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -6.7, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -5.79}]}, "bone92": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4}]}, "bone71": {"rotate": [{"angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.78}]}, "bone91": {"translate": [{"x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": 1.59}], "scale": [{"y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "y": 1.17}]}, "bone86": {"translate": [{"x": 16.68, "y": 8.65, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.7, "x": 18.63, "y": 9.66, "curve": "stepped"}, {"time": 0.7333, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 4, "x": 16.68, "y": 8.65}], "scale": [{"x": 1.133, "y": 1.133, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.7, "x": 1.149, "y": 1.149, "curve": "stepped"}, {"time": 0.7333, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 4, "x": 1.133, "y": 1.133}]}, "bone90": {"translate": [{"x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": 1.77}], "scale": [{"y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "y": 1.142}]}, "bone72": {"rotate": [{"angle": 1.87, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -6.7, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 1.87}]}, "bone75": {"rotate": [{"angle": -4.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -4.01}]}, "bone79": {"rotate": [{"angle": -4.07, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.2667, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 4.53, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "angle": -4.07}]}, "bone77": {"rotate": [{"angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.7}]}, "bone73": {"rotate": [{"angle": 0.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -6.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.09}]}, "bone74": {"rotate": [{"angle": -1.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -6.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.96}]}, "bone82": {"rotate": [{"angle": 1.53, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.2667, "angle": -0.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 4.53, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "angle": 1.53}]}, "bone80": {"rotate": [{"angle": -2.51, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 0.2667, "angle": -3.82, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 4.53, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 4, "angle": -2.51}]}, "bone83": {"rotate": [{"angle": 3.35, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.2667, "angle": 1.91, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.6, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 4.53, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 3.35}]}, "bone81": {"rotate": [{"angle": -0.51, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.2667, "angle": -2.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9333, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 4.53, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": -0.51}]}, "bone84": {"rotate": [{"angle": 4.48, "curve": 0.301, "c2": 0.12, "c3": 0.638, "c4": 0.48}, {"time": 0.2667, "angle": 3.64, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.9333, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 4.53, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 4, "angle": 4.48}]}, "bone87": {"translate": [{"x": -7.6, "y": 5.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": -15.04, "y": 10.34, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -7.6, "y": 5.23}], "scale": [{"x": 1.076, "y": 1.076, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.076, "y": 1.076}]}, "bone88": {"translate": [{"x": 4.94, "y": 2.56, "curve": 0.323, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7333, "x": 9.42, "y": 4.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "x": 18.63, "y": 9.66, "curve": "stepped"}, {"time": 2.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 4.94, "y": 2.56}], "scale": [{"x": 1.039, "y": 1.039, "curve": 0.323, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7333, "x": 1.075, "y": 1.075, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "x": 1.149, "y": 1.149, "curve": "stepped"}, {"time": 2.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 1.039, "y": 1.039}]}}}, "str_win": {"slots": {"ren_bg_2": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff"}]}, "ren_bg_1": {"color": [{"color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffffac"}]}, "ren_bg_01": {"color": [{"color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffff52"}]}, "ren_bg_02": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.21, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": -11.59, "y": -0.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.3333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 8.73, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.6333, "angle": 16.34, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.8, "angle": 8.73, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.3333}]}, "bone3": {"rotate": [{"angle": 1, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.73, "curve": 0.243, "c3": 0.684, "c4": 0.73}, {"time": 1.3333, "angle": 1}]}, "bone4": {"translate": [{"time": 0.6333, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.7333, "x": 10.36, "y": -0.14, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.8}]}, "bone5": {"translate": [{"time": 0.6333, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.7333, "x": 10.36, "y": -0.14, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.8}]}, "bone6": {"rotate": [{"time": 0.1667, "curve": 0.312, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 0.3333, "angle": 15.12, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.6667}]}, "bone7": {"rotate": [{"angle": -0.03}]}, "bone8": {"rotate": [{"angle": -0.39}]}, "bone9": {"rotate": [{"angle": 0.37}]}, "bone10": {"rotate": [{"angle": -2.72}]}, "bone11": {"rotate": [{"angle": 1.24}]}, "bone12": {"rotate": [{"angle": -0.99}]}, "bone13": {"rotate": [{"angle": 19.52, "curve": "stepped"}, {"time": 0.0667, "angle": 19.52, "curve": 0.331, "c2": 0.33, "c3": 0.664, "c4": 0.66}, {"time": 0.2333, "angle": 11.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 38.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 154.31, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 19.52}]}, "bone14": {"rotate": [{"angle": -0.24, "curve": "stepped"}, {"time": 0.0667, "angle": -0.24, "curve": 0.33, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 79.75, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.56, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -0.24}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -38.31, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": 136.24, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone17": {"rotate": [{"angle": 6.98, "curve": 0.326, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 131.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 22.53, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 1.3333, "angle": 6.98}]}, "ljio1": {"translate": [{"curve": 0.314, "c3": 0.649, "c4": 0.35}, {"time": 0.1667, "x": 9.31, "y": 0.07, "curve": 0.326, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 0.4667, "x": -8.26, "y": -0.06, "curve": 0.305, "c2": 0.2, "c3": 0.642, "c4": 0.55}, {"time": 1.3333}]}, "bone37": {"rotate": [{"angle": -0.24, "curve": "stepped"}, {"time": 0.1333, "angle": -0.24, "curve": 0.33, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 79.75, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -0.56, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 1.4, "angle": -0.24}]}, "bone36": {"rotate": [{"angle": 19.52, "curve": "stepped"}, {"time": 0.1333, "angle": 19.52, "curve": 0.331, "c2": 0.33, "c3": 0.664, "c4": 0.66}, {"time": 0.3, "angle": 11.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 38.04, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 154.31, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 19.52}]}, "bone35": {"translate": [{"time": 0.7, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.8, "x": 10.36, "y": -0.14, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.8667}]}, "bone41": {"rotate": [{"angle": 6.98, "curve": "stepped"}, {"time": 0.0667, "angle": 6.98, "curve": 0.326, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 131.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 22.53, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 1.4, "angle": 6.98}]}, "bone40": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -38.31, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6, "angle": 136.24, "curve": 0.25, "c3": 0.75}, {"time": 1.4}]}, "bone39": {"translate": [{"time": 0.7, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.8, "x": 10.36, "y": -0.14, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.8667}]}, "bone43": {"rotate": [{"time": 0.2333, "curve": 0.312, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 0.4, "angle": 15.12, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7333}]}, "bone34": {"rotate": [{"angle": 1, "curve": "stepped"}, {"time": 0.0667, "angle": 1, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.73, "curve": 0.243, "c3": 0.684, "c4": 0.73}, {"time": 1.4, "angle": 1}]}, "bone33": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.73, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.7, "angle": 16.34, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.8667, "angle": 8.73, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.4}]}, "bone50": {"rotate": [{"angle": 0.37}]}, "bone49": {"rotate": [{"angle": -0.39}]}, "bone48": {"rotate": [{"angle": -0.03}]}, "bone53": {"rotate": [{"angle": -0.99}]}, "bone52": {"rotate": [{"angle": 1.24}]}, "bone51": {"rotate": [{"angle": -2.72}]}, "bone32": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 7.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 13.09, "curve": 0.25, "c3": 0.75}, {"time": 1.4}], "translate": [{"time": 0.0667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4, "x": -11.59, "y": -0.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.4}]}, "bone63": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "bone66": {"translate": [{"x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.95}], "scale": [{"y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 1.973}]}, "bone65": {"translate": [{"x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": 2.73}], "scale": [{"y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "y": 1.292}]}, "bone64": {"translate": [{"x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "x": 0.68}], "scale": [{"y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "y": 1.055}]}, "bone89": {"scale": [{"y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 1.104, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 1.104}]}, "bone85": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -15.04, "y": 10.34, "curve": "stepped"}, {"time": 4}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 4}]}, "bone78": {"rotate": [{"angle": -5.79, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.78, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -5.79}]}, "bone76": {"rotate": [{"angle": -5.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.6667, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -6.7, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -5.79}]}, "bone92": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.95, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": 1.973, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4}]}, "bone71": {"rotate": [{"angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.78}]}, "bone91": {"translate": [{"x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3333, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "x": 1.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "x": 4.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6667, "x": 2.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": 1.59}], "scale": [{"y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3333, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "y": 1.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "y": 1.461, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6667, "y": 1.292, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "y": 1.17}]}, "bone86": {"translate": [{"x": 16.68, "y": 8.65, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.7, "x": 18.63, "y": 9.66, "curve": "stepped"}, {"time": 0.7333, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 4, "x": 16.68, "y": 8.65}], "scale": [{"x": 1.133, "y": 1.133, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.7, "x": 1.149, "y": 1.149, "curve": "stepped"}, {"time": 0.7333, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 4, "x": 1.133, "y": 1.133}]}, "bone90": {"translate": [{"x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1333, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.3333, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "x": 1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4667, "x": 2.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6667, "x": 0.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": 1.77}], "scale": [{"y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1333, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.3333, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "y": 1.142, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4667, "y": 1.225, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6667, "y": 1.055, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "y": 1.142}]}, "bone72": {"rotate": [{"angle": 1.87, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -6.7, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 1.87}]}, "bone75": {"rotate": [{"angle": -4.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -4.01}]}, "bone79": {"rotate": [{"angle": -4.07, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.2667, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 4.53, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "angle": -4.07}]}, "bone77": {"rotate": [{"angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.7}]}, "bone73": {"rotate": [{"angle": 0.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -6.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.09}]}, "bone74": {"rotate": [{"angle": -1.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -6.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.96}]}, "bone82": {"rotate": [{"angle": 1.53, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.2667, "angle": -0.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 4.53, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "angle": 1.53}]}, "bone80": {"rotate": [{"angle": -2.51, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 0.2667, "angle": -3.82, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 4.53, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 4, "angle": -2.51}]}, "bone83": {"rotate": [{"angle": 3.35, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.2667, "angle": 1.91, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.6, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 4.53, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 3.35}]}, "bone81": {"rotate": [{"angle": -0.51, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.2667, "angle": -2.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9333, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 4.53, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": -0.51}]}, "bone84": {"rotate": [{"angle": 4.48, "curve": 0.301, "c2": 0.12, "c3": 0.638, "c4": 0.48}, {"time": 0.2667, "angle": 3.64, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.9333, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 4.53, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 4, "angle": 4.48}]}, "bone87": {"translate": [{"x": -7.6, "y": 5.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": -15.04, "y": 10.34, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -7.6, "y": 5.23}], "scale": [{"x": 1.076, "y": 1.076, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.076, "y": 1.076}]}, "bone88": {"translate": [{"x": 4.94, "y": 2.56, "curve": 0.323, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7333, "x": 9.42, "y": 4.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "x": 18.63, "y": 9.66, "curve": "stepped"}, {"time": 2.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 4.94, "y": 2.56}], "scale": [{"x": 1.039, "y": 1.039, "curve": 0.323, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7333, "x": 1.075, "y": 1.075, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "x": 1.149, "y": 1.149, "curve": "stepped"}, {"time": 2.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 1.039, "y": 1.039}]}}}}}