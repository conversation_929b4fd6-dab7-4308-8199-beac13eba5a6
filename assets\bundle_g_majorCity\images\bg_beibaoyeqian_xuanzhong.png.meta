{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "2cc4bdb0-5269-4dc1-8fd3-1772a9b9c862", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "2cc4bdb0-5269-4dc1-8fd3-1772a9b9c862@6c48a", "displayName": "bg_be<PERSON><PERSON><PERSON><PERSON><PERSON>_xuanzhong", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "2cc4bdb0-5269-4dc1-8fd3-1772a9b9c862", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "2cc4bdb0-5269-4dc1-8fd3-1772a9b9c862@f9941", "displayName": "bg_be<PERSON><PERSON><PERSON><PERSON><PERSON>_xuanzhong", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 184, "height": 73, "rawWidth": 184, "rawHeight": 73, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-92, -36.5, 0, 92, -36.5, 0, -92, 36.5, 0, 92, 36.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 73, 184, 73, 0, 0, 184, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-92, -36.5, 0], "maxPos": [92, 36.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "2cc4bdb0-5269-4dc1-8fd3-1772a9b9c862@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "2cc4bdb0-5269-4dc1-8fd3-1772a9b9c862@6c48a"}}