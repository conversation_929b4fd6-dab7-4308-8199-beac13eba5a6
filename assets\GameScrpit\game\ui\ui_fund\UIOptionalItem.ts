import { _decorator, instantiate, Label, math } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { RedeemChosenRequest } from "../../net/protocol/Activity";
import { DisciplinesModule } from "../../../module/disciplines/DisciplinesModule";
import { CommIntegerListMessage } from "../../net/protocol/Comm";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const colors: any[] = [, "42a200", "70bff7", "ae6cfd", "f4b500", "e93515"];
@ccclass("UIOptionalItem")
@routeConfig({
  bundle: BundleEnum.BUNDLE_HD_FUND,
  url: "prefab/ui/UIOptionalItem",
  nextHop: [],
  exit: "dialog_close",
})
export class UIOptionalItem extends BaseCtrl {
  public playShowAni: boolean = true;

  private _chosenList: Array<number> = null;

  private _activityId: number = null;

  private _redeemId: number = null;

  private _index: number = null;

  public init(args: any): void {
    super.init(args);
    this._chosenList = args.payload.chosenList;
    this._activityId = args.payload.activityId;
    this._redeemId = args.payload.redeemId;

    log.log("自选数组", this._chosenList);
  }

  protected start(): void {
    super.start();
    this.getNode("lbl_item_name_des").getComponent(Label).string = "";
    this.getNode("lbl_item_yongyou").getComponent(Label).string = "";
    this.getNode("lbl_item_des").getComponent(Label).string = "";

    let list = ToolExt.traAwardItemMapList(this._chosenList);
    for (let i = 0; i < list.length; i++) {
      let node = instantiate(this.getNode("pitch_item"));
      let item = node.getChildByName("Item");
      FmUtils.setItemNode(item, list[i].id, list[i].num);
      node.getChildByName("btn_pitch")["info"] = list[i];
      node.getChildByName("btn_pitch")["index"] = i;
      node.active = true;
      this.getNode("content").addChild(node);
    }
  }

  on_click_btn_pitch(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let info = event.target["info"];
    this._index = event.target["index"];
    this.setShowItme(info);
  }

  private setShowItme(info) {
    this.getNode("showPoint").destroyAllChildren();
    let node = instantiate(this.getNode("show_Item"));
    this.getNode("showPoint").addChild(node);
    FmUtils.setItemNode(node, info.id, info.num);
    node.active = true;

    let itemdb = JsonMgr.instance.getConfigItem(info.id);

    this.getNode("lbl_item_name_des").getComponent(Label).string = itemdb.name;

    this.getNode("lbl_item_yongyou").getComponent(Label).string = "拥有数量：" + PlayerModule.data.getItemNum(info.id);

    this.getNode("lbl_item_des").getComponent(Label).string = itemdb.des == "" ? "暂无描述" : itemdb.des;
    this.getNode("lbl_item_name_des").getComponent(Label).color = math.color(colors[itemdb.color]);
  }

  private on_click_btn_ok() {
    AudioMgr.instance.playEffect(1347);

    this.closeBack({
      activityId: this._activityId,
      redeemId: this._redeemId,
      chosenIndexList: [this._index],
      count: 1,
    });
  }
  //   export interface RedeemChosenRequest {
  //     /** 活动ID */
  //     activityId: number;
  //     /** 商品ID */
  //     redeemId: number;
  //     /** 自选的道具所在的索引顺序,从0开始 */
  //     chosenIndex: number;
  //     /** 购买数量 */
  //     count: number;
  //   }

  private on_click_btn_no() {
    AudioMgr.instance.playEffect(1347);
    this.closeBack();
  }

  on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    this.closeBack();
  }
}
