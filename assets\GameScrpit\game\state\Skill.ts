import { _decorator, isValid } from "cc";
import { FSMState } from "../fight/FSM/FSMState";
import { FSMBoard, STATE } from "../fight/section/StateSection";
const { ccclass, property } = _decorator;

@ccclass
export default class Skill extends FSMState {
  public async onEnter(board: FSMBoard) {}

  private onSkillOver(board: FSMBoard, overSkillId) {
    this.switchState(STATE.IDLE);
    return;
  }

  public update(board: FSMBoard, dt) {}

  public onExit(board: any): void {}
}
