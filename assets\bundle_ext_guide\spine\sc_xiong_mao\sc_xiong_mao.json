{"skeleton": {"hash": "cj9p2gdE/gh2XImvOv3Bc8nFYXg", "spine": "3.8.99", "x": -259.41, "y": -35.93, "width": 464.94, "height": 473.47, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 20.68, "y": -120.42, "scaleX": 2.6443, "scaleY": 2.6443}, {"name": "bone2", "parent": "bone", "x": 1.75, "y": 82.36, "color": "ff0000ff"}, {"name": "bone3", "parent": "bone2", "length": 25.06, "rotation": 3.49, "color": "0044ffff"}, {"name": "bone4", "parent": "bone3", "length": 21.35, "rotation": 87.33, "x": 0.36, "y": 0.89}, {"name": "bone5", "parent": "bone4", "length": 44.28, "rotation": 1.95, "x": 21.35}, {"name": "bone6", "parent": "bone5", "length": 18.06, "rotation": -7.61, "x": 44.28}, {"name": "bone7", "parent": "bone5", "x": -0.33, "y": -6.8, "color": "abe323ff"}, {"name": "bone8", "parent": "bone5", "x": 25.37, "y": -7.7, "color": "abe323ff"}, {"name": "bone9", "parent": "bone5", "length": 30.66, "rotation": -116.57, "x": 23.69, "y": -25.52}, {"name": "a8", "parent": "bone9", "length": 15, "rotation": 155.09, "x": 41.17, "y": -6.79}, {"name": "a9", "parent": "a8", "length": 14.52, "rotation": -22.51, "x": 15}, {"name": "bone10", "parent": "bone5", "length": 27.86, "rotation": 135.84, "x": 30.41, "y": 32.54}, {"name": "bone11", "parent": "bone10", "length": 27.75, "rotation": 21.71, "x": 27.86}, {"name": "bone12", "parent": "bone11", "length": 15.44, "rotation": -94.09, "x": 27.75, "transform": "noRotationOrReflection"}, {"name": "a10", "parent": "bone12", "length": 58.81, "rotation": -37.88, "x": -32.13, "y": 32.99}, {"name": "a12", "parent": "bone5", "length": 29.75, "rotation": 90.42, "x": 26.75, "y": 29.66}, {"name": "a11", "parent": "a10", "length": 16.8, "rotation": -87.72, "x": -83.27, "y": -5.12}, {"name": "a13", "parent": "a11", "length": 13.96, "rotation": 7.56, "x": 16.8}, {"name": "a14", "parent": "a13", "length": 10.09, "rotation": 6.26, "x": 13.96}, {"name": "a15", "parent": "a11", "length": 12.42, "rotation": -22.62, "x": 10.1, "y": -4.84}, {"name": "a16", "parent": "a15", "length": 10.53, "rotation": 13.06, "x": 12.42}, {"name": "a17", "parent": "a10", "length": 11.55, "rotation": 41.97, "x": -79.12, "y": 7.03}, {"name": "a18", "parent": "a17", "length": 20.35, "x": 11.55}, {"name": "bone13", "parent": "bone3", "length": 17.04, "rotation": -92.66, "x": 0.65, "y": -0.89, "color": "ffe907ff"}, {"name": "bone14", "parent": "bone6", "x": 1.64, "y": -7.04, "color": "abe323ff"}, {"name": "bone15", "parent": "bone6", "x": 24.34, "y": -5.86}, {"name": "bone16", "parent": "bone13", "x": 3.36, "y": 16.96}, {"name": "bone17", "parent": "bone16", "length": 13.94, "rotation": 1.9}, {"name": "bone18", "parent": "bone17", "length": 11.72, "rotation": -1.65, "x": 13.94}, {"name": "bone34", "parent": "bone", "length": 11.35, "x": 22.15, "y": 44.08}, {"name": "bone35", "parent": "bone34", "length": 7.22, "rotation": 90, "x": -0.39}, {"name": "zj1", "parent": "bone35", "rotation": -178.92, "x": 8.68, "y": 1.42, "color": "ff3f00ff"}, {"name": "bone19", "parent": "zj1", "length": 10.17, "rotation": 46.56}, {"name": "bone20", "parent": "bone16", "length": 14.61, "rotation": 25.73, "x": 0.29, "y": 0.14}, {"name": "bone21", "parent": "bone20", "length": 13.55, "rotation": -50.53, "x": 14.61, "color": "abe323ff"}, {"name": "bone22", "parent": "bone13", "x": -1.2, "y": -20.6}, {"name": "bone23", "parent": "bone22", "length": 17.23, "rotation": -15.28, "x": 0.17}, {"name": "bone24", "parent": "bone23", "length": 16, "rotation": -2.42, "x": 17.23}, {"name": "bone32", "parent": "bone", "length": 12.38, "x": -29.71, "y": 42.28}, {"name": "bone33", "parent": "bone32", "length": 8.65, "rotation": 87.44, "x": -0.13, "y": -0.13}, {"name": "yj1", "parent": "bone33", "rotation": 165.69, "x": 8.21, "y": -2.36, "color": "ff3f00ff"}, {"name": "bone25", "parent": "yj1", "length": 6.85, "rotation": -0.65}, {"name": "a4", "parent": "bone3", "length": 17.88, "rotation": 88.57, "x": 12.5, "y": -11.32, "color": "0044ffff"}, {"name": "bone28", "parent": "a4", "length": 13.95, "rotation": -179.24, "x": 2.3, "y": 11.95}, {"name": "bone29", "parent": "bone28", "length": 6.73, "rotation": 1.57, "x": 13.95}, {"name": "bone30", "parent": "a4", "length": 10.22, "rotation": -169.43, "x": 2.24, "y": -13.7}, {"name": "bone31", "parent": "bone30", "length": 7.56, "rotation": 4.57, "x": 10.19, "y": -0.17}, {"name": "zj", "parent": "bone35", "rotation": -177.27, "x": 20.39, "y": 1.64, "color": "ff3f00ff"}, {"name": "yj", "parent": "bone33", "rotation": 168.11, "x": 23.71, "y": -6.31, "color": "ff3f00ff"}, {"name": "bone26", "parent": "bone22", "length": 18.68, "rotation": -40.79, "x": -0.12, "y": -0.44}, {"name": "bone27", "parent": "bone26", "length": 18.62, "rotation": 50.33, "x": 18.68, "color": "abe323ff"}, {"name": "a2", "parent": "bone6", "x": 18.07, "y": 3.18}, {"name": "bone36", "parent": "bone13", "x": 3.55, "y": -19.6, "color": "ffe907ff"}, {"name": "bone37", "parent": "bone36", "length": 15.13, "rotation": -3.72, "color": "ffe907ff"}, {"name": "bone38", "parent": "bone37", "length": 11.18, "rotation": 0.93, "x": 15.13, "color": "ffe907ff"}, {"name": "bone40", "parent": "bone", "length": 12.58, "rotation": 1.74, "x": -18.25, "y": 43.13, "color": "ff8888ff"}, {"name": "bone41", "parent": "bone40", "length": 5.97, "rotation": 88.26, "y": 0.13, "color": "ff8888ff"}, {"name": "w2", "parent": "bone41", "rotation": 178.05, "x": 8.12, "y": -0.01, "color": "ffe907ff"}, {"name": "bone39", "parent": "w2", "length": 6.2, "rotation": 44.46, "x": -0.13, "color": "ffe907ff"}, {"name": "bone42", "parent": "bone36", "length": 16.16, "rotation": 22.31, "x": -0.64, "y": -0.38, "color": "ffe907ff"}, {"name": "bone43", "parent": "bone42", "length": 13.88, "rotation": -54.36, "x": 16.16, "color": "ffe907ff"}, {"name": "w1", "parent": "bone41", "rotation": 177.11, "x": 19.3, "y": -0.39, "color": "ffe907ff"}, {"name": "a19", "parent": "bone12", "length": 58.81, "rotation": -37.88, "x": -32.13, "y": 32.99}, {"name": "a20", "parent": "a19", "length": 16.8, "rotation": -87.72, "x": -83.27, "y": -5.12}, {"name": "a21", "parent": "a20", "length": 13.96, "rotation": 7.56, "x": 16.8}, {"name": "a22", "parent": "a21", "length": 10.09, "rotation": 6.26, "x": 13.96}, {"name": "a23", "parent": "a20", "length": 12.42, "rotation": -22.62, "x": 10.1, "y": -4.84}, {"name": "a24", "parent": "a23", "length": 10.53, "rotation": 13.06, "x": 12.42}, {"name": "gunguang2", "parent": "bone", "length": 154.39, "rotation": 2.15, "x": -25.35, "y": 54.92, "scaleX": 1.4839, "scaleY": 1.4839}, {"name": "gunguang", "parent": "gunguang2", "rotation": -2.15, "x": 155.93, "y": -2.48}, {"name": "xmlight", "parent": "root", "x": 30.63, "y": 202.65, "scaleX": 2.6443, "scaleY": 2.6443}, {"name": "lonxmegjux<PERSON>ll", "parent": "root", "x": -0.74, "y": 3.02, "scaleX": 1.6198, "scaleY": 1.6198, "color": "ff0000ff"}, {"name": "lizi_zonxmeg", "parent": "lonxmegjux<PERSON>ll", "length": 1224.32, "x": -198.73, "y": 46.85}, {"name": "0", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm1", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm2", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm3", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm4", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm5", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm6", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm7", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm8", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm9", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm10", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm11", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm1xm2", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm1xm3", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm1xm4", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xma_zhuti", "parent": "lonxmegjux<PERSON>ll", "length": 1873.15, "x": -198.73, "y": 46.85}, {"name": "shuyxme_zonxmeg", "parent": "lonxmegjux<PERSON>ll", "length": 2022.54, "x": -198.73, "y": 46.85}, {"name": "xmbonxmexm6", "parent": "shuyxme_zonxmeg"}, {"name": "xmbonxmexm7", "parent": "shuyxme_zonxmeg"}, {"name": "xmbonxmexm8", "parent": "shuyxme_zonxmeg"}, {"name": "xmbonxmexm9", "parent": "shuyxme_zonxmeg"}, {"name": "shuyxme_zonxmegxm2", "parent": "lonxmegjux<PERSON>ll", "length": 2022.54, "x": -198.73, "y": 46.85}, {"name": "xmbonxmexm1xm5", "parent": "shuyxme_zonxmegxm2"}, {"name": "xmbonxmexm1xm6", "parent": "shuyxme_zonxmegxm2"}, {"name": "xmbonxmexm1xm7", "parent": "shuyxme_zonxmegxm2"}, {"name": "xmbonxmexm1xm8", "parent": "shuyxme_zonxmegxm2"}, {"name": "xme", "parent": "lonxmegjux<PERSON>ll", "length": 1038.97, "x": -198.73, "y": 46.85}, {"name": "xmef", "parent": "lonxmegjux<PERSON>ll", "length": 1098.68, "x": -198.73, "y": 46.85}, {"name": "xmexishou_zhonxmeg", "parent": "lonxmegjux<PERSON>ll", "length": 1650.24, "x": -198.73, "y": 46.85}, {"name": "xmexishou_xm1", "parent": "xmexishou_zhonxmeg", "length": 566.19}, {"name": "xmexishou_xm2", "parent": "xmexishou_zhonxmeg", "length": 566.19}, {"name": "xmexishou_xm3", "parent": "xmexishou_zhonxmeg", "length": 566.19}, {"name": "xmexishou_xm4", "parent": "xmexishou_zhonxmeg", "length": 566.19}, {"name": "xmexishou_xm5", "parent": "xmexishou_zhonxmeg", "length": 566.19}], "slots": [{"name": "a10", "bone": "a10", "attachment": "a10"}, {"name": "a16", "bone": "a19", "color": "17ff00ff", "blend": "additive"}, {"name": "a14", "bone": "bone23", "attachment": "a14"}, {"name": "a13", "bone": "bone17", "attachment": "a13"}, {"name": "a15", "bone": "bone17", "color": "ffffff00", "attachment": "a13"}, {"name": "a12", "bone": "a12", "attachment": "a12"}, {"name": "a11", "bone": "a18", "attachment": "a11"}, {"name": "a9", "bone": "bone9", "attachment": "a9"}, {"name": "a8", "bone": "a8", "attachment": "a8"}, {"name": "a7", "bone": "a9", "attachment": "a7"}, {"name": "a6", "bone": "bone4", "attachment": "a6"}, {"name": "a5", "bone": "bone30", "attachment": "a5"}, {"name": "a4", "bone": "a4", "attachment": "a4"}, {"name": "a3", "bone": "bone28", "attachment": "a3"}, {"name": "a2", "bone": "a2", "attachment": "a2"}, {"name": "a2_1", "bone": "a2"}, {"name": "a1", "bone": "bone6", "attachment": "a1"}, {"name": "a0", "bone": "bone10", "attachment": "a0"}, {"name": "gunguang/dsd_18", "bone": "gunguang"}, {"name": "xiongmaolight", "bone": "xmlight", "color": "50ff00ff", "blend": "additive"}, {"name": "xmc_0", "bone": "xmexishou_xm2"}, {"name": "xmazhuti_000xm2", "bone": "xma_zhuti"}, {"name": "xmc_0000", "bone": "xmexishou_xm1"}, {"name": "xmc_xm1", "bone": "xmexishou_xm3"}, {"name": "xmc_xm2", "bone": "xmexishou_xm4"}, {"name": "xmc_xm3", "bone": "xmexishou_xm5"}, {"name": "xme", "bone": "xme", "color": "ffffffa8", "blend": "additive"}, {"name": "xmef", "bone": "xmef", "blend": "additive"}, {"name": "shuyxme0xm1", "bone": "xmbonxmexm6"}, {"name": "shuyxmexm9", "bone": "xmbonxmexm1xm5"}, {"name": "shuyxmexm1", "bone": "xmbonxmexm7"}, {"name": "shuyxmexm10", "bone": "xmbonxmexm1xm6"}, {"name": "shuyxmexm2", "bone": "xmbonxmexm8"}, {"name": "shuyxmexm11", "bone": "xmbonxmexm1xm7"}, {"name": "shuyxmexm3", "bone": "xmbonxmexm9"}, {"name": "shuyxmexm1xm2", "bone": "xmbonxmexm1xm8"}, {"name": "lizi", "bone": "0", "blend": "additive"}, {"name": "lizixm2", "bone": "xm1", "blend": "additive"}, {"name": "lizixm3", "bone": "xm2", "blend": "additive"}, {"name": "lizixm4", "bone": "xm3", "blend": "additive"}, {"name": "lizixm5", "bone": "xm4", "blend": "additive"}, {"name": "lizixm6", "bone": "xm5", "blend": "additive"}, {"name": "lizixm7", "bone": "xm6", "blend": "additive"}, {"name": "lizixm8", "bone": "xm7", "blend": "additive"}, {"name": "lizixm9", "bone": "xm8", "blend": "additive"}, {"name": "lizixm10", "bone": "xm9", "blend": "additive"}, {"name": "lizixm11", "bone": "xm10", "blend": "additive"}, {"name": "lizixm1xm2", "bone": "xm11", "blend": "additive"}, {"name": "lizixm1xm3", "bone": "xm1xm2", "blend": "additive"}, {"name": "lizixm1xm4", "bone": "xm1xm3", "blend": "additive"}, {"name": "lizixm1xm5", "bone": "xm1xm4", "blend": "additive"}], "ik": [{"name": "w1", "order": 10, "bones": ["bone37"], "target": "w1", "compress": true, "stretch": true}, {"name": "w2", "order": 11, "bones": ["bone38"], "target": "w2", "compress": true, "stretch": true}, {"name": "w3", "order": 8, "bones": ["bone42", "bone43"], "target": "w2", "bendPositive": false}, {"name": "yj", "order": 2, "bones": ["bone23"], "target": "yj", "compress": true, "stretch": true}, {"name": "yj1", "order": 3, "bones": ["bone24"], "target": "yj1", "compress": true, "stretch": true}, {"name": "yj2", "bones": ["bone26", "bone27"], "target": "yj1"}, {"name": "zj", "order": 6, "bones": ["bone17"], "target": "zj", "compress": true, "stretch": true}, {"name": "zj1", "order": 7, "bones": ["bone18"], "target": "zj1", "compress": true, "stretch": true}, {"name": "zj2", "order": 4, "bones": ["bone20", "bone21"], "target": "zj1", "bendPositive": false}], "transform": [{"name": "b", "order": 18, "bones": ["a12"], "target": "bone8", "rotation": 90.42, "x": 1.37, "y": 37.36, "shearY": 360, "rotateMix": 0, "translateMix": 0.1, "scaleMix": 0, "shearMix": 0}, {"name": "q1", "order": 15, "bones": ["a4"], "target": "bone7", "rotation": -0.7, "x": -33.08, "y": -4.77, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "s", "order": 12, "bones": ["bone8"], "target": "bone7", "x": 25.7, "y": -0.9, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "s1", "order": 14, "bones": ["bone9"], "target": "bone8", "rotation": -116.57, "x": -1.69, "y": -17.82, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "s2", "order": 16, "bones": ["bone10"], "target": "bone7", "rotation": 135.84, "x": 30.74, "y": 39.34, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "t", "order": 13, "bones": ["bone15"], "target": "bone14", "x": 22.71, "y": 1.18, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "t01", "order": 17, "bones": ["a2"], "target": "bone14", "x": 16.43, "y": 10.22, "rotateMix": 0, "translateMix": 0.3, "scaleMix": 0, "shearMix": 0}, {"name": "w", "order": 9, "bones": ["w1"], "target": "bone43", "rotation": 27.2, "x": 4.02, "y": -5.27, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "yj", "order": 1, "bones": ["yj"], "target": "bone27", "rotation": -26.27, "x": 4.59, "y": 7.68, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "zj", "order": 5, "bones": ["zj"], "target": "bone21", "rotation": 27.93, "x": 3.05, "y": -5.19, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}], "skins": [{"name": "default", "attachments": {"a0": {"a0": {"type": "mesh", "uvs": [0.90058, 0.07634, 0.98218, 0.17219, 0.98175, 0.38545, 0.92877, 0.45832, 0.71314, 0.54729, 0.63059, 0.61489, 0.62992, 0.6876, 0.55626, 0.7066, 0.59592, 0.82853, 0.53249, 0.93135, 0.43483, 0.97401, 0.24099, 0.9955, 0.04711, 0.91178, 0.00133, 0.84864, 0.04848, 0.73351, 0.01395, 0.68757, 0.03658, 0.5112, 0.09275, 0.47383, 0.24551, 0.22639, 0.5586, 0.01129, 0.75735, 0.01266, 0.66029, 0.24288, 0.47252, 0.41405, 0.34733, 0.58955, 0.31291, 0.72605], "triangles": [24, 12, 13, 8, 9, 24, 10, 24, 9, 14, 24, 13, 11, 24, 10, 11, 12, 24, 21, 19, 20, 21, 20, 0, 21, 0, 1, 18, 19, 21, 2, 21, 1, 22, 18, 21, 3, 21, 2, 4, 22, 21, 3, 4, 21, 17, 18, 22, 23, 17, 22, 5, 23, 22, 4, 5, 22, 5, 7, 23, 6, 7, 5, 23, 16, 17, 24, 23, 7, 23, 15, 16, 24, 15, 23, 14, 15, 24, 8, 24, 7], "vertices": [1, 12, -5.86, 1.18, 1, 1, 12, -3.16, 9.43, 1, 2, 13, -9.67, 25.81, 0.01386, 12, 9.33, 20.41, 0.98614, 2, 13, -3.35, 25.04, 0.05522, 12, 15.49, 22.02, 0.94478, 3, 13, 7.1, 16.41, 0.48524, 12, 28.39, 17.87, 0.51469, 14, -15.47, 21.36, 6e-05, 3, 13, 13.57, 13.99, 0.82081, 12, 35.3, 18.02, 0.1575, 14, -9.89, 17.29, 0.02169, 3, 13, 18.92, 15.87, 0.85773, 12, 39.57, 21.74, 0.05378, 14, -4.23, 17.66, 0.08849, 3, 13, 21.66, 12.62, 0.75203, 12, 43.32, 19.74, 0.02079, 14, -2.47, 13.8, 0.22718, 2, 13, 29.89, 17.84, 0.25038, 14, 6.86, 16.61, 0.74962, 2, 13, 38.6, 17.32, 0.06554, 14, 15.11, 13.76, 0.93446, 2, 13, 43.51, 13.48, 0.01312, 14, 18.8, 8.74, 0.98688, 1, 14, 21.22, -1.58, 1, 2, 13, 45.99, -7.87, 0.00198, 14, 15.45, -12.49, 0.99802, 2, 13, 42.19, -11.86, 0.03213, 14, 10.72, -15.31, 0.96787, 2, 13, 32.87, -12.49, 0.39227, 14, 1.58, -13.41, 0.60773, 2, 13, 30.13, -15.45, 0.62398, 14, -1.86, -15.52, 0.37602, 3, 13, 16.76, -18.94, 0.97692, 12, 50.44, -11.39, 0.00153, 14, -15.67, -15.28, 0.02154, 3, 13, 13, -17.06, 0.978, 12, 46.25, -11.04, 0.01736, 14, -18.8, -12.46, 0.00465, 2, 13, -7.95, -15.8, 0.21171, 12, 26.32, -17.62, 0.78829, 1, 12, 2.55, -16.03, 1, 1, 12, -4.47, -7.91, 1, 1, 12, 12.47, 0.03, 1, 1, 13, 1.7, 0.68, 1, 1, 13, 16.86, -1.08, 1, 2, 13, 27.51, 0.76, 0.56853, 14, -0.02, 0.8, 0.43147], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 42, 42, 44, 44, 46, 46, 48], "width": 90, "height": 129}}, "a1": {"a1": {"type": "mesh", "uvs": [0.4171, 0.00536, 0.45412, 0.08938, 0.46376, 0.14018, 0.65659, 0.08255, 0.69581, 0.21045, 0.68149, 0.24497, 0.91336, 0.37937, 0.98913, 0.49079, 0.98994, 0.57618, 0.96479, 0.60671, 0.80337, 0.61508, 0.81603, 0.72211, 0.7882, 0.83082, 0.71828, 0.91422, 0.6043, 0.98412, 0.47442, 0.9848, 0.36013, 0.87504, 0.24733, 0.76669, 0.20795, 0.68711, 0.20881, 0.5491, 0.0355, 0.42071, 0, 0.31055, 0, 0.28649, 0.08197, 0.22461, 0.22899, 0.19641, 0.3515, 0.19641, 0.31965, 0.10254, 0.10031, 0.33899, 0.20574, 0.33092, 0.32063, 0.33899, 0.44227, 0.35109, 0.58014, 0.37528, 0.73017, 0.42368, 0.8356, 0.47207, 0.914, 0.53256, 0.5727, 0.52248, 0.65515, 0.63136, 0.61054, 0.7342, 0.39563, 0.53054, 0.37941, 0.71, 0.72003, 0.56684, 0.71868, 0.75033, 0.54508, 0.73218, 0.54238, 0.84308, 0.49237, 0.62128, 0.57212, 0.6354], "triangles": [15, 43, 14, 15, 16, 43, 14, 43, 37, 14, 37, 13, 37, 43, 42, 13, 41, 12, 13, 37, 41, 16, 39, 43, 16, 17, 39, 43, 39, 42, 42, 39, 44, 12, 41, 11, 17, 18, 39, 37, 36, 41, 41, 10, 11, 41, 40, 10, 41, 36, 40, 42, 45, 37, 37, 45, 36, 42, 44, 45, 18, 19, 39, 39, 38, 44, 39, 19, 38, 45, 35, 36, 45, 44, 35, 36, 35, 40, 44, 38, 35, 10, 34, 9, 10, 33, 34, 10, 40, 33, 9, 34, 8, 34, 7, 8, 40, 32, 33, 40, 35, 32, 20, 27, 19, 27, 28, 19, 19, 29, 38, 19, 28, 29, 33, 6, 34, 34, 6, 7, 38, 30, 35, 38, 29, 30, 35, 31, 32, 35, 30, 31, 33, 32, 6, 31, 5, 32, 32, 5, 6, 20, 21, 27, 30, 2, 31, 5, 2, 3, 5, 31, 2, 29, 25, 30, 30, 25, 2, 27, 22, 23, 27, 23, 28, 28, 24, 29, 29, 24, 25, 28, 23, 24, 27, 21, 22, 5, 3, 4, 25, 1, 2, 25, 26, 1, 26, 0, 1], "vertices": [2, 6, 48.99, 11.93, 0.9, 26, 24.65, 17.79, 0.1, 2, 6, 44.17, 8.14, 0.9, 26, 19.83, 14, 0.1, 2, 6, 41.15, 7.01, 0.9, 26, 16.81, 12.87, 0.1, 2, 6, 46.14, -10.18, 0.9, 26, 21.8, -4.32, 0.1, 2, 6, 38.67, -14.4, 0.9, 26, 14.32, -8.54, 0.1, 2, 6, 36.46, -13.28, 0.9, 26, 12.12, -7.42, 0.1, 2, 6, 30.07, -34.99, 0.9, 26, 5.73, -29.13, 0.1, 2, 6, 23.88, -42.44, 0.9, 26, -0.46, -36.58, 0.1, 2, 6, 18.7, -42.95, 0.9, 26, -5.65, -37.09, 0.1, 2, 6, 16.65, -40.83, 0.9, 26, -7.7, -34.97, 0.1, 3, 6, 14.9, -26.23, 0.8424, 26, -9.44, -20.37, 0.0936, 25, 13.26, -19.19, 0.064, 3, 6, 8.49, -27.93, 0.8424, 26, -15.85, -22.07, 0.0936, 25, 6.85, -20.89, 0.064, 3, 6, 1.67, -25.97, 0.8424, 26, -22.67, -20.11, 0.0936, 25, 0.03, -18.93, 0.064, 3, 6, -3.94, -20.06, 0.7992, 26, -28.28, -14.2, 0.0888, 25, -5.58, -13.02, 0.112, 3, 6, -9.06, -10.08, 0.7992, 26, -33.4, -4.23, 0.0888, 25, -10.7, -3.04, 0.112, 3, 6, -10.1, 1.69, 0.7992, 26, -34.44, 7.55, 0.0888, 25, -11.74, 8.73, 0.112, 3, 6, -4.31, 12.62, 0.7992, 26, -28.65, 18.48, 0.0888, 25, -5.95, 19.66, 0.112, 3, 6, 1.41, 23.4, 0.8568, 26, -22.93, 29.26, 0.0952, 25, -0.23, 30.44, 0.048, 3, 6, 5.95, 27.39, 0.83885, 26, -18.4, 33.24, 0.08715, 25, 4.31, 34.42, 0.074, 3, 6, 14.34, 28.02, 0.85142, 26, -10, 33.88, 0.07708, 25, 12.7, 35.06, 0.0715, 3, 6, 20.81, 44.4, 0.88233, 26, -3.53, 50.25, 0.09167, 25, 19.17, 51.43, 0.026, 2, 6, 27.24, 48.18, 0.9, 26, 2.89, 54.04, 0.1, 2, 6, 28.7, 48.31, 0.9, 26, 4.35, 54.16, 0.1, 2, 6, 33.09, 41.19, 0.9, 26, 8.75, 47.05, 0.1, 2, 6, 35.93, 28.01, 0.9, 26, 11.59, 33.86, 0.1, 2, 6, 36.87, 16.9, 0.9, 26, 12.53, 22.75, 0.1, 2, 6, 42.33, 20.27, 0.9, 26, 17.99, 26.13, 0.1, 2, 6, 26.28, 38.94, 0.976, 25, 24.64, 45.98, 0.024, 2, 6, 27.58, 29.42, 0.96533, 25, 25.94, 36.46, 0.03467, 2, 6, 27.97, 18.96, 0.896, 25, 26.33, 26, 0.104, 2, 6, 28.17, 7.87, 0.79867, 25, 26.53, 14.91, 0.20133, 2, 6, 27.76, -4.76, 0.7355, 25, 26.12, 2.28, 0.2645, 2, 6, 25.97, -18.61, 0.84683, 25, 24.33, -11.57, 0.15317, 2, 6, 23.84, -28.42, 0.83889, 25, 22.2, -21.38, 0.16111, 2, 6, 20.76, -35.84, 0.96133, 25, 19.13, -28.8, 0.03867, 2, 6, 18.75, -4.84, 0.693, 25, 17.12, 2.2, 0.307, 2, 6, 12.77, -12.88, 0.632, 25, 11.13, -5.84, 0.368, 2, 6, 6.18, -9.36, 0.648, 25, 4.54, -2.32, 0.352, 3, 6, 16.9, 11.17, 0.67289, 26, -7.44, 17.03, 0.03178, 25, 15.27, 18.21, 0.29533, 3, 6, 5.87, 11.72, 0.66859, 26, -18.47, 17.58, 0.01541, 25, 4.23, 18.76, 0.316, 2, 6, 17.19, -18.43, 0.82556, 25, 15.55, -11.39, 0.17444, 2, 6, 6.03, -19.25, 0.66933, 25, 4.39, -12.21, 0.33067, 2, 6, 5.8, -3.42, 0.704, 25, 4.16, 3.62, 0.296, 2, 6, -0.97, -3.74, 0.688, 25, -2.6, 3.3, 0.312, 2, 6, 12.13, 1.94, 0.704, 25, 10.49, 8.97, 0.296, 2, 6, 11.89, -5.37, 0.648, 25, 10.25, 1.67, 0.352], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 62, 70, 70, 72, 72, 74, 38, 76, 76, 78, 64, 80, 80, 82, 30, 32, 32, 34, 24, 26, 26, 28, 74, 84, 84, 86, 70, 88, 88, 90, 20, 22, 22, 24], "width": 151, "height": 100}}, "a2": {"a2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.15, -23.54, -8.94, 9.34, 8.99, 10.86, 11.78, -22.02], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 52, "height": 28}}, "a2_1": {"a2_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-4.12, -21.99, -6.69, 8.24, 9.59, 9.62, 12.15, -20.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 52, "height": 28}}, "a3": {"a3": {"type": "mesh", "uvs": [0.44367, 0.03935, 0.5672, 0.03028, 0.69333, 0.05306, 0.68656, 0.17217, 0.70598, 0.34504, 0.75891, 0.50739, 0.83498, 0.64367, 0.92656, 0.75494, 1, 0.85406, 0.9457, 0.9604, 0.55278, 0.96043, 0.33131, 0.96044, 0.05373, 0.92091, 0.03422, 0.84862, 0.09071, 0.73456, 0.13383, 0.63678, 0.18346, 0.5045, 0.2308, 0.34567, 0.29088, 0.15023, 0.53667, 0.82355, 0.53344, 0.66579, 0.527, 0.51963, 0.527, 0.35259, 0.53022, 0.17859], "triangles": [18, 0, 23, 23, 0, 1, 3, 1, 2, 10, 11, 19, 11, 12, 19, 10, 19, 9, 9, 19, 8, 12, 13, 19, 19, 7, 8, 13, 14, 19, 19, 14, 20, 19, 6, 7, 19, 20, 6, 14, 15, 20, 20, 16, 21, 20, 15, 16, 21, 17, 22, 21, 16, 17, 20, 5, 6, 20, 21, 5, 21, 4, 5, 21, 22, 4, 22, 23, 4, 23, 3, 4, 17, 18, 22, 22, 18, 23, 23, 1, 3], "vertices": [1, 4, -7.26, 0.27, 1, 1, 4, -7.06, -1.96, 1, 1, 4, -7.66, -4.22, 1, 1, 44, 1.16, 3.31, 1, 1, 44, 5.49, 3.45, 1, 2, 44, 9.59, 4.2, 0.90125, 45, -4.24, 4.32, 0.09875, 2, 44, 13.06, 5.4, 0.38554, 45, -0.74, 5.42, 0.61446, 2, 44, 15.92, 6.91, 0.05925, 45, 2.16, 6.85, 0.94075, 2, 44, 18.46, 8.11, 0.00285, 45, 4.73, 7.98, 0.99715, 1, 45, 7.31, 6.8, 1, 2, 44, 20.72, -0.06, 3e-05, 45, 6.77, -0.25, 0.99997, 2, 44, 20.53, -4.05, 0.06322, 45, 6.46, -4.22, 0.93678, 2, 44, 19.29, -8.99, 0.20542, 45, 5.09, -9.13, 0.79458, 2, 44, 17.47, -9.25, 0.24271, 45, 3.27, -9.34, 0.75729, 2, 44, 14.67, -8.09, 0.43014, 45, 0.5, -8.11, 0.56986, 2, 44, 12.27, -7.2, 0.70513, 45, -1.88, -7.15, 0.29487, 2, 44, 9.01, -6.14, 0.95901, 45, -5.11, -6, 0.04099, 1, 44, 5.09, -5.09, 1, 1, 4, -9.99, 3.06, 1, 2, 44, 17.29, -0.19, 0.00148, 45, 3.33, -0.28, 0.99852, 1, 44, 13.35, -0.05, 1, 1, 44, 9.69, 0.02, 1, 1, 44, 5.52, 0.22, 1, 1, 44, 1.18, 0.49, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 22, 24, 24, 26, 34, 36, 30, 32, 32, 34, 26, 28, 28, 30, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 20, 38, 38, 40, 40, 42, 42, 44, 44, 46, 38, 16, 38, 26, 38, 24], "width": 29, "height": 40}}, "a4": {"a4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-12.24, -11.55, -11.34, 13.43, 14.64, 12.49, 13.74, -12.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 25, "height": 26}}, "a5": {"a5": {"type": "mesh", "uvs": [0.23244, 0.02913, 0.50448, 0.08971, 0.57259, 0.25377, 0.63272, 0.43006, 0.71234, 0.59542, 0.82184, 0.70004, 0.96397, 0.78754, 0.97765, 0.87901, 0.86716, 0.9314, 0.67596, 0.95802, 0.46781, 0.95726, 0.23905, 0.95626, 0.06067, 0.93788, 0.01988, 0.86461, 0.01318, 0.75365, 0.00993, 0.61245, 0.01689, 0.46367, 0.02732, 0.30731, 0.05821, 0.11489, 0.45051, 0.80913, 0.42321, 0.6427, 0.37886, 0.44852, 0.32427, 0.25183], "triangles": [22, 0, 1, 18, 0, 22, 22, 1, 2, 17, 18, 22, 21, 22, 2, 21, 2, 3, 17, 22, 21, 16, 17, 21, 20, 21, 3, 20, 3, 4, 15, 16, 21, 20, 14, 15, 19, 20, 4, 19, 4, 5, 15, 21, 20, 19, 14, 20, 14, 11, 13, 8, 5, 6, 8, 6, 7, 9, 19, 5, 11, 12, 13, 14, 19, 11, 9, 10, 19, 11, 19, 10, 5, 8, 9], "vertices": [1, 4, -6.31, -25.71, 1, 1, 4, -7.77, -30.32, 1, 1, 46, 3.45, 4.47, 1, 2, 47, -2.17, 4.94, 0.00067, 46, 7.63, 4.58, 0.99933, 2, 47, 1.86, 5.1, 0.36637, 46, 11.63, 5.07, 0.63363, 2, 47, 4.71, 6.17, 0.78752, 46, 14.39, 6.36, 0.21248, 2, 47, 7.35, 7.88, 0.92653, 46, 16.88, 8.28, 0.07347, 2, 47, 9.42, 7.48, 0.94844, 46, 18.98, 8.04, 0.05156, 2, 47, 10.02, 5.33, 0.96178, 46, 19.75, 5.95, 0.03822, 2, 47, 9.64, 2.05, 0.99008, 46, 19.64, 2.64, 0.00992, 1, 47, 8.58, -1.33, 1, 1, 47, 7.41, -5.04, 1, 1, 47, 6.11, -7.81, 1, 1, 47, 4.29, -7.97, 1, 2, 47, 1.82, -7.33, 0.98872, 46, 12.59, -7.33, 0.01128, 2, 47, -1.3, -6.42, 0.81465, 46, 9.4, -6.67, 0.18535, 2, 47, -4.53, -5.29, 0.37524, 46, 6.09, -5.81, 0.62476, 2, 47, -7.91, -4.06, 0.06614, 46, 2.62, -4.85, 0.93386, 1, 4, -8.24, -22.72, 1, 1, 47, 5.24, -0.6, 1, 2, 47, 1.45, 0.09, 0.98543, 46, 11.62, 0.03, 0.01457, 1, 46, 7.1, 0.28, 1, 1, 46, 2.48, 0.36, 1], "hull": 19, "edges": [0, 36, 0, 2, 10, 12, 8, 10, 6, 8, 2, 4, 4, 6, 34, 36, 32, 34, 30, 32, 28, 30, 24, 26, 26, 28, 22, 24, 20, 22, 16, 18, 18, 20, 12, 14, 14, 16, 20, 38, 38, 40, 40, 42, 42, 44], "width": 17, "height": 23}}, "a6": {"a6": {"type": "mesh", "uvs": [0.52932, 0.00686, 0.70111, 0.0358, 0.82442, 0.15938, 0.88182, 0.23406, 0.88205, 0.40164, 0.9363, 0.53787, 0.93593, 0.60572, 1, 0.66336, 1, 0.66437, 0.95771, 0.73867, 0.95741, 0.76789, 1, 0.83359, 1, 0.86075, 0.91137, 0.92021, 0.80654, 0.88484, 0.79749, 0.91212, 0.75241, 0.99067, 0.59137, 0.99051, 0.5474, 0.95159, 0.3517, 0.95266, 0.20946, 0.91632, 0.06434, 0.85052, 0.02515, 0.79015, 0.06246, 0.6669, 0.09597, 0.55622, 0.13075, 0.44132, 0.11726, 0.4131, 0.01059, 0.34981, 0.01055, 0.23742, 0.01053, 0.19243, 0.0657, 0.14252, 0.17157, 0.11524, 0.20418, 0.07867, 0.22952, 0.01989, 0.59313, 0.19672, 0.62083, 0.32057, 0.64853, 0.46211, 0.67425, 0.63726, 0.6881, 0.76641, 0.69403, 0.8938, 0.28483, 0.17195, 0.35606, 0.23565, 0.40354, 0.33295, 0.39959, 0.42849, 0.35606, 0.5258, 0.34617, 0.63195, 0.33825, 0.74695, 0.24922, 0.57888, 0.15425, 0.57711, 0.08896, 0.22857, 0.17997, 0.26042, 0.23537, 0.33295, 0.25318, 0.43026, 0.22944, 0.51165, 0.7262, 0.19571, 0.76775, 0.27178, 0.77962, 0.33547, 0.84095, 0.39563, 0.78358, 0.44973, 0.82117, 0.5895, 0.84491, 0.70273, 0.87063, 0.82304, 0.32457, 0.85135], "triangles": [34, 0, 1, 41, 40, 0, 44, 43, 36, 36, 43, 35, 43, 42, 35, 5, 58, 4, 4, 58, 57, 48, 53, 47, 47, 53, 44, 53, 48, 25, 48, 24, 25, 53, 52, 44, 44, 52, 43, 53, 25, 52, 36, 35, 56, 58, 56, 57, 25, 26, 52, 26, 51, 52, 43, 52, 42, 42, 52, 51, 26, 27, 50, 26, 50, 51, 50, 27, 49, 57, 3, 4, 57, 56, 3, 27, 28, 49, 35, 55, 56, 56, 55, 3, 51, 41, 42, 42, 34, 35, 42, 41, 34, 41, 50, 40, 41, 51, 50, 35, 54, 55, 35, 34, 54, 55, 2, 3, 55, 54, 2, 49, 31, 50, 50, 31, 40, 28, 29, 49, 34, 41, 0, 29, 30, 49, 49, 30, 31, 34, 1, 54, 54, 1, 2, 31, 32, 40, 32, 33, 40, 44, 36, 37, 36, 56, 58, 45, 47, 44, 47, 23, 48, 23, 24, 48, 40, 33, 0, 46, 23, 47, 46, 47, 45, 37, 45, 44, 45, 37, 38, 38, 37, 60, 59, 36, 58, 58, 5, 59, 61, 60, 10, 6, 59, 5, 37, 59, 60, 60, 59, 6, 38, 46, 45, 38, 60, 61, 10, 60, 9, 8, 9, 6, 9, 60, 6, 6, 7, 8, 61, 10, 11, 59, 37, 36, 23, 46, 21, 46, 20, 21, 22, 23, 21, 17, 39, 16, 16, 39, 15, 17, 18, 39, 19, 62, 18, 14, 61, 13, 13, 61, 12, 15, 39, 14, 39, 38, 14, 14, 38, 61, 61, 11, 12, 20, 62, 19, 62, 46, 18, 62, 20, 46, 18, 46, 38, 18, 38, 39], "vertices": [4, 5, 58.62, -2.21, 0.54536, 6, 14.51, -0.29, 0.27964, 7, 58.95, 4.59, 0.1, 8, 33.25, 5.49, 0.075, 3, 5, 54.85, -18.02, 0.57353, 6, 12.86, -16.47, 0.32647, 8, 29.47, -10.33, 0.1, 3, 5, 41.46, -28.86, 0.67451, 6, 1.03, -28.98, 0.22549, 8, 16.08, -21.16, 0.1, 4, 4, 55.92, -32.66, 0.03333, 5, 33.44, -33.82, 0.7635, 6, -6.26, -34.95, 0.10316, 8, 8.07, -26.12, 0.1, 4, 4, 38.5, -32.43, 0.13333, 5, 16.03, -33, 0.74338, 6, -23.63, -36.45, 0.02328, 8, -9.34, -25.3, 0.1, 3, 4, 24.26, -37.28, 0.33333, 5, 1.64, -37.35, 0.56667, 8, -23.74, -29.65, 0.1, 3, 4, 17.2, -37.14, 0.56667, 5, -5.41, -36.98, 0.33333, 8, -30.78, -29.28, 0.1, 3, 4, 11.12, -43.01, 0.76667, 5, -11.68, -42.64, 0.13333, 8, -37.06, -34.94, 0.1, 4, 24, -12.78, 42.64, 0.03333, 4, 11.02, -43.01, 0.83333, 5, -11.79, -42.64, 0.03333, 8, -37.16, -34.94, 0.1, 3, 24, -5.11, 38.6, 0.13333, 4, 3.35, -38.97, 0.76667, 8, -44.69, -30.64, 0.1, 3, 24, -2.07, 38.52, 0.33333, 4, 0.31, -38.9, 0.56667, 8, -47.73, -30.46, 0.1, 3, 24, 4.82, 42.39, 0.56667, 4, -6.58, -42.76, 0.33333, 8, -54.74, -34.09, 0.1, 3, 24, 7.64, 42.35, 0.76667, 4, -9.4, -42.72, 0.13333, 8, -57.56, -33.95, 0.1, 4, 24, 13.71, 34.01, 0.85973, 4, -15.47, -34.39, 0.03307, 7, -37.64, -26.32, 0.008, 8, -63.34, -25.42, 0.0992, 3, 24, 9.89, 24.32, 0.8136, 7, -33.49, -16.76, 0.096, 8, -59.2, -15.86, 0.0904, 3, 24, 12.71, 23.44, 0.8136, 7, -36.29, -15.78, 0.096, 8, -61.99, -14.88, 0.0904, 3, 24, 20.82, 19.13, 0.7056, 7, -44.25, -11.2, 0.216, 8, -69.95, -10.3, 0.0784, 3, 24, 20.59, 4.15, 0.7848, 7, -43.51, 3.76, 0.128, 8, -69.21, 4.66, 0.0872, 3, 24, 16.48, 0.12, 0.6624, 7, -39.27, 7.65, 0.264, 8, -64.97, 8.55, 0.0736, 3, 24, 16.33, -18.08, 0.7272, 7, -38.5, 25.83, 0.192, 8, -64.2, 26.73, 0.0808, 4, 24, 12.36, -31.25, 0.832, 4, -14.13, 30.87, 0.032, 7, -34.09, 38.86, 0.04, 8, -59.79, 39.76, 0.096, 4, 24, 5.32, -44.65, 0.76667, 4, -7.1, 44.27, 0.1, 5, -26.93, 45.21, 0.03333, 8, -52.3, 52.91, 0.1, 4, 24, -1.01, -48.2, 0.56667, 4, -0.77, 47.83, 0.2, 5, -20.48, 48.55, 0.13333, 8, -45.86, 56.25, 0.1, 4, 24, -13.78, -44.54, 0.33333, 4, 12, 44.17, 0.23333, 5, -7.85, 44.47, 0.33333, 8, -33.22, 52.16, 0.1, 4, 24, -25.24, -41.26, 0.13333, 4, 23.47, 40.89, 0.2, 5, 3.5, 40.8, 0.56667, 8, -21.87, 48.5, 0.1, 4, 24, -37.15, -37.85, 0.03333, 4, 35.37, 37.49, 0.1, 5, 15.28, 36.99, 0.76667, 8, -10.1, 44.69, 0.1, 3, 4, 38.32, 38.7, 0.03333, 5, 18.27, 38.1, 0.86667, 8, -7.1, 45.8, 0.1, 2, 5, 25.32, 47.69, 0.9, 8, -0.05, 55.39, 0.1, 3, 5, 37, 47.13, 0.863, 7, 37.33, 53.93, 0.062, 8, 11.63, 54.83, 0.075, 2, 5, 41.67, 46.91, 0.9, 8, 16.3, 54.61, 0.1, 2, 5, 46.61, 41.53, 0.9, 8, 21.24, 49.23, 0.1, 4, 5, 48.97, 31.56, 0.83201, 6, 0.47, 31.91, 0.02499, 7, 49.3, 38.36, 0.068, 8, 23.59, 39.26, 0.075, 3, 5, 52.62, 28.35, 0.79092, 6, 4.52, 29.21, 0.10908, 8, 27.25, 36.05, 0.1, 3, 5, 58.61, 25.7, 0.66667, 6, 10.81, 27.37, 0.23333, 8, 33.24, 33.4, 0.1, 5, 4, 60.19, -5.87, 0.02222, 5, 38.61, -7.19, 0.48081, 6, -4.66, -7.87, 0.19697, 7, 38.94, -0.39, 0.26667, 8, 13.24, 0.51, 0.03333, 4, 4, 47.27, -8.26, 0.08889, 5, 25.63, -9.14, 0.43839, 6, -17.28, -11.53, 0.07272, 7, 25.95, -2.34, 0.4, 5, 24, -34.29, 10.26, 0.02222, 4, 32.52, -10.63, 0.2, 5, 10.8, -11, 0.35557, 6, -31.73, -15.34, 0.02221, 7, 11.13, -4.2, 0.4, 4, 24, -16.04, 12.39, 0.09852, 4, 14.27, -12.76, 0.38, 5, -7.51, -12.51, 0.28148, 7, -7.18, -5.71, 0.24, 4, 24, -2.59, 13.48, 0.22463, 4, 0.82, -13.86, 0.46978, 5, -20.99, -13.15, 0.11093, 7, -20.66, -6.35, 0.19467, 4, 24, 10.67, 13.84, 0.29335, 4, -12.43, -14.22, 0.49333, 5, -34.25, -13.06, 0.04132, 7, -33.92, -6.26, 0.172, 3, 5, 42.57, 21.33, 0.78533, 7, 42.9, 28.13, 0.18133, 8, 17.2, 29.03, 0.03333, 2, 5, 35.63, 15.03, 0.728, 7, 35.96, 21.83, 0.272, 3, 4, 46.27, 11.96, 0.02022, 5, 25.31, 11.11, 0.70778, 7, 25.64, 17.91, 0.272, 3, 4, 36.34, 12.47, 0.07583, 5, 15.41, 11.95, 0.65217, 7, 15.73, 18.75, 0.272, 4, 24, -28.06, -17.03, 0.02022, 4, 26.28, 16.66, 0.1686, 5, 5.49, 16.49, 0.54518, 7, 5.82, 23.29, 0.266, 4, 24, -17.03, -18.11, 0.09437, 4, 15.26, 17.74, 0.33198, 5, -5.49, 17.94, 0.30165, 7, -5.16, 24.74, 0.272, 4, 24, -5.08, -19.02, 0.2277, 4, 3.31, 18.65, 0.48681, 5, -17.4, 19.25, 0.13348, 7, -17.07, 26.05, 0.152, 3, 4, 20.91, 26.68, 0.0765, 5, 0.46, 26.68, 0.69683, 7, 0.79, 33.48, 0.22667, 3, 4, 21.22, 35.5, 0.02089, 5, 1.07, 35.49, 0.76044, 7, 1.4, 42.29, 0.21867, 3, 5, 37.57, 39.81, 0.80133, 7, 37.9, 46.61, 0.16533, 8, 12.19, 47.5, 0.03333, 2, 5, 33.85, 31.51, 0.752, 7, 34.18, 38.31, 0.248, 2, 5, 26.07, 26.73, 0.752, 7, 26.4, 33.53, 0.248, 2, 5, 15.88, 25.56, 0.752, 7, 16.21, 32.36, 0.248, 2, 5, 7.53, 28.18, 0.78133, 7, 7.86, 34.98, 0.21867, 2, 5, 38.12, -19.55, 0.908, 7, 38.45, -12.75, 0.092, 2, 5, 30.03, -23.03, 0.91733, 7, 30.36, -16.23, 0.08267, 2, 5, 23.36, -23.81, 0.91733, 7, 23.69, -17.01, 0.08267, 2, 5, 16.84, -29.21, 0.932, 7, 17.17, -22.41, 0.068, 4, 24, -35.39, 22.84, 0.04481, 4, 33.63, -23.21, 0.49187, 5, 11.48, -23.61, 0.28732, 7, 11.81, -16.81, 0.176, 4, 24, -20.81, 26.12, 0.11178, 4, 19.04, -26.49, 0.52619, 5, -3.21, -26.4, 0.2367, 7, -2.88, -19.6, 0.12533, 4, 24, -9, 28.16, 0.24256, 4, 7.23, -28.53, 0.53393, 5, -15.08, -28.04, 0.11151, 7, -14.75, -21.24, 0.112, 4, 24, 3.55, 30.37, 0.31538, 4, -5.31, -30.75, 0.53692, 5, -27.69, -29.82, 0.04769, 7, -27.36, -23.02, 0.1, 4, 24, 5.75, -20.45, 0.29444, 4, -7.53, 20.07, 0.50644, 5, -28.18, 21.04, 0.04711, 7, -27.85, 27.84, 0.152], "hull": 34, "edges": [0, 66, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 50, 52, 52, 54, 58, 60, 60, 62, 62, 64, 64, 66, 48, 50, 44, 46, 46, 48, 0, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 62, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 88, 94, 94, 96, 54, 56, 56, 58, 56, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 96, 108, 110, 110, 112, 112, 114, 116, 118, 118, 120, 120, 122, 92, 124], "width": 154, "height": 172}}, "a7": {"a7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-9.12, -8.29, 0.54, 20.12, 35.57, 8.21, 25.91, -20.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 49, "height": 59}}, "a8": {"a8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-9.38, -3.58, 13.05, 21.97, 35.59, 2.17, 13.16, -23.37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 54, "height": 49}}, "a9": {"a9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44.89, -8.08, -1.77, -28.67, -18.73, 9.76, 27.93, 30.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 72}}, "a10": {"a10": {"type": "mesh", "uvs": [0.71146, 0, 0.74214, 0.01678, 0.76839, 0.03546, 0.79086, 0.05913, 0.80411, 0.0855, 0.81557, 0.1155, 0.82912, 0.15096, 0.85228, 0.13796, 0.92733, 0.01651, 0.96392, 0.02749, 0.99412, 0.05483, 0.99429, 0.12074, 0.92088, 0.16491, 0.85037, 0.23552, 0.36735, 0.7121, 0.26198, 0.84006, 0.23122, 0.8359, 0.11988, 0.98356, 0.06221, 0.98379, 0.02568, 0.96836, 0.01212, 0.9561, 0.00108, 0.93612, 0.11746, 0.85554, 0.15915, 0.81736, 0.20978, 0.70566, 0.28071, 0.69752, 0.31237, 0.66737, 0.51738, 0.44605, 0.79409, 0.17511, 0.77178, 0.15469, 0.73395, 0.1355, 0.69219, 0.11313, 0.65688, 0.09371, 0.6269, 0.07532, 0.60613, 0.05528, 0.58995, 0.02673, 0.62381, 0.03192, 0.65769, 0.03828, 0.68515, 0.04905, 0.71014, 0.06262, 0.74331, 0.07622, 0.72912, 0.04959, 0.70887, 0.02534, 0.69399, 0, 0.7676, 0.10703, 0.79424, 0.13736, 0.81668, 0.16769], "triangles": [40, 41, 2, 42, 43, 0, 42, 0, 1, 41, 42, 1, 41, 1, 2, 45, 44, 5, 40, 2, 3, 44, 40, 3, 44, 3, 4, 44, 4, 5, 32, 37, 38, 34, 35, 36, 33, 36, 37, 34, 36, 33, 32, 33, 37, 32, 38, 39, 31, 32, 39, 31, 39, 40, 30, 31, 40, 44, 30, 40, 29, 30, 44, 45, 29, 44, 12, 7, 8, 11, 12, 8, 9, 11, 8, 11, 9, 10, 12, 13, 6, 12, 6, 7, 46, 6, 13, 28, 46, 13, 14, 26, 27, 14, 27, 13, 16, 24, 25, 23, 24, 16, 25, 26, 14, 15, 16, 25, 14, 15, 25, 19, 20, 21, 22, 19, 21, 16, 17, 22, 16, 22, 23, 18, 19, 22, 17, 18, 22, 27, 28, 13, 46, 45, 6, 46, 28, 45, 45, 5, 6, 28, 29, 45], "vertices": [3, 18, 13.04, -14.05, 3e-05, 20, 22.66, 1.46, 0.11754, 21, 10.3, -0.89, 0.88243, 2, 20, 17.62, -1.58, 0.2886, 21, 4.7, -2.71, 0.7114, 3, 17, 20.27, -13.21, 0.01204, 20, 12.61, -3.81, 0.52219, 21, -0.68, -3.75, 0.46576, 4, 15, -94.92, -20.27, 0.03704, 17, 14.68, -12.25, 0.03613, 20, 7.08, -5.07, 0.69692, 21, -6.35, -3.73, 0.22991, 4, 15, -92.8, -15.45, 0.14815, 17, 9.94, -9.94, 0.07225, 20, 1.82, -4.76, 0.70168, 21, -11.41, -2.24, 0.07792, 4, 15, -90, -10.4, 0.37037, 17, 5.01, -6.94, 0.08429, 20, -3.89, -3.89, 0.53171, 21, -16.77, -0.1, 0.01363, 1, 15, -86.69, -4.43, 1, 1, 15, -90.99, -3.19, 1, 1, 15, -115.65, -8.82, 1, 1, 15, -118.18, -3.02, 1, 1, 15, -117.79, 3.99, 1, 1, 15, -108.89, 12.03, 1, 1, 15, -94.87, 8.45, 1, 1, 15, -77.58, 8.45, 1, 1, 15, 39.89, 7.55, 1, 1, 15, 68.76, 10.28, 1, 1, 15, 71.57, 6.02, 1, 1, 15, 103.76, 10.41, 1, 1, 15, 110.12, 3.41, 1, 1, 15, 112.03, -2.92, 1, 1, 15, 111.86, -6.07, 1, 1, 15, 110.37, -9.84, 1, 1, 15, 86.7, -5.46, 1, 1, 15, 76.96, -5.02, 1, 1, 15, 56.3, -12.45, 1, 1, 15, 47.42, -4.79, 1, 1, 15, 39.86, -4.6, 1, 1, 15, -12.57, -6.53, 1, 1, 15, -79.58, -5.77, 1, 4, 15, -79.9, -10.97, 0.37047, 17, 5.98, 3.14, 0.4593, 18, -10.31, 4.53, 0.16263, 19, -23.63, 7.15, 0.00761, 4, 15, -78.34, -17.92, 0.14823, 17, 12.99, 4.41, 0.45737, 18, -3.19, 4.87, 0.33693, 19, -16.52, 6.72, 0.05746, 4, 15, -76.79, -25.73, 0.03708, 17, 20.86, 5.65, 0.31399, 18, 4.77, 5.07, 0.46232, 19, -8.58, 6.04, 0.18661, 4, 15, -75.55, -32.4, 1e-05, 17, 27.57, 6.63, 0.14022, 18, 11.55, 5.15, 0.45051, 19, -1.83, 5.39, 0.40926, 3, 17, 33.49, 7.19, 0.0351, 18, 17.5, 4.93, 0.30639, 19, 4.05, 4.52, 0.65852, 3, 17, 38.44, 6.56, 0.0007, 18, 22.32, 3.66, 0.1448, 19, 8.71, 2.72, 0.8545, 2, 18, 27.33, 0.67, 0.0843, 19, 13.36, -0.8, 0.9157, 3, 18, 22.13, -1.49, 0.16381, 19, 7.95, -2.37, 0.83492, 20, 24.2, 16.89, 0.00127, 4, 17, 33.91, -1.22, 0.0027, 18, 16.81, -3.46, 0.34115, 19, 2.45, -3.75, 0.62959, 20, 20.59, 12.5, 0.02656, 5, 17, 29.19, -2.59, 0.01811, 18, 11.95, -4.2, 0.48436, 19, -2.46, -3.95, 0.38291, 20, 16.76, 9.43, 0.09457, 21, 6.36, 8.2, 0.02005, 5, 17, 24.46, -3.3, 0.068, 18, 7.17, -4.28, 0.47307, 19, -7.22, -3.52, 0.16965, 20, 12.67, 6.95, 0.20637, 21, 1.81, 6.71, 0.08291, 6, 15, -87.39, -23.99, 0.02778, 17, 18.7, -4.87, 0.14065, 18, 1.24, -5.08, 0.28674, 19, -13.2, -3.66, 0.03738, 20, 7.95, 3.28, 0.29176, 21, -3.62, 4.21, 0.21569, 5, 17, 23.58, -7.12, 0.068, 18, 5.79, -7.95, 0.16136, 19, -8.99, -7.01, 0.00508, 20, 13.32, 3.09, 0.2424, 21, 1.57, 2.81, 0.52317, 4, 17, 28.95, -8.39, 0.01811, 18, 10.95, -9.92, 0.05895, 20, 18.77, 3.98, 0.1396, 21, 7.08, 2.44, 0.78334, 4, 17, 33.78, -10.38, 0.0027, 18, 15.47, -12.53, 0.01167, 20, 23.99, 4, 0.07699, 21, 12.17, 1.28, 0.90864, 6, 15, -85.89, -17.28, 0.12963, 17, 12.05, -3.1, 0.29803, 18, -5.11, -2.45, 0.16179, 19, -19.23, -0.36, 0.00508, 20, 1.13, 2.36, 0.32256, 21, -10.46, 4.85, 0.08291, 5, 15, -84.71, -10.34, 0.26852, 17, 5.16, -1.64, 0.38749, 18, -11.75, -0.1, 0.05957, 20, -5.79, 1.06, 0.26437, 21, -17.5, 5.15, 0.02005, 1, 15, -83.06, -3.91, 1], "hull": 44, "edges": [0, 86, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 68, 70, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 84, 86, 80, 82, 82, 84, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 80, 88, 88, 90, 90, 92], "width": 275, "height": 304}}, "a11": {"a11": {"type": "mesh", "uvs": [0.96115, 0, 0.98518, 0, 0.98696, 0.13042, 0.90222, 0.14488, 0.93331, 0.49502, 0.75178, 0.83936, 0.53643, 0.88059, 0.31547, 0.83179, 0.20174, 0.65039, 0.21773, 0.43072, 0.38457, 0.27403, 0.57548, 0.29277, 0.57193, 0.25679, 0.56798, 0.21693, 0.56356, 0.17219, 0.55864, 0.12243, 0.60631, 0.0683, 0.63806, 0.06541, 0.64345, 0.13643, 0.64138, 0.18388, 0.63953, 0.22806, 0.63911, 0.26466, 0.71942, 0.22121, 0.81917, 0.1982, 0.76366, 0.18257, 0.75139, 0.08364], "triangles": [18, 15, 16, 16, 17, 18, 11, 12, 21, 4, 11, 21, 3, 0, 2, 0, 1, 2, 4, 22, 23, 5, 6, 11, 6, 7, 11, 5, 11, 4, 11, 8, 10, 11, 7, 8, 10, 8, 9, 22, 4, 21, 23, 3, 4, 3, 23, 25, 23, 24, 25, 3, 25, 0, 21, 12, 20, 12, 13, 20, 20, 13, 19, 13, 14, 19, 19, 14, 18, 18, 14, 15], "vertices": [1, 23, -19.32, 24, 1, 1, 23, -19.32, 25.59, 1, 1, 23, -11.75, 25.71, 1, 1, 23, -10.92, 20.11, 1, 1, 23, 9.39, 22.16, 1, 1, 23, 29.36, 10.18, 1, 1, 23, 31.76, -4.03, 1, 1, 23, 28.93, -18.61, 1, 1, 23, 18.4, -26.12, 1, 1, 23, 5.66, -25.06, 1, 2, 22, 8.13, -14.05, 0.00349, 23, -3.42, -14.05, 0.99651, 1, 23, -2.34, -1.45, 1, 1, 22, 7.13, -1.69, 1, 1, 22, 4.81, -1.95, 1, 1, 22, 2.22, -2.24, 1, 1, 15, -77.9, 4.68, 1, 1, 15, -82.34, 4.92, 1, 1, 15, -83.86, 6.36, 1, 1, 22, 0.14, 3.03, 1, 1, 22, 2.9, 2.9, 1, 2, 22, 5.46, 2.78, 0.99727, 23, -6.09, 2.78, 0.00273, 2, 22, 7.58, 2.75, 0.008, 23, -3.97, 2.75, 0.992, 1, 23, -6.49, 8.05, 1, 1, 23, -7.82, 14.63, 1, 1, 23, -8.73, 10.97, 1, 1, 23, -14.47, 10.16, 1], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 30, 32, 32, 34, 16, 18, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 28, 30, 26, 28, 22, 24, 24, 26, 34, 36, 36, 38, 38, 40], "width": 109, "height": 96}}, "a12": {"a12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-14.94, 38.8, 60.94, 34.58, 56.83, -39.3, -19.05, -35.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 168}}, "a13": {"a13": {"type": "mesh", "uvs": [0.77717, 0.09017, 0.90629, 0.32117, 0.97018, 0.55179, 0.9, 0.64502, 0.6957, 0.80493, 0.78353, 0.81118, 0.78905, 0.9496, 0.69279, 0.97903, 0.33135, 0.97894, 0.18974, 0.93852, 0.16448, 0.85283, 0.14183, 0.776, 0.10726, 0.65873, 0.06304, 0.50875, 0.0224, 0.37088, 0.02405, 0.19963, 0.30811, 0.05567, 0.5537, 0.01037, 0.40924, 0.24072, 0.45901, 0.44199, 0.47472, 0.65263, 0.46424, 0.80242, 0.75496, 0.58008], "triangles": [18, 16, 17, 15, 16, 18, 14, 15, 18, 21, 20, 4, 8, 9, 10, 21, 8, 10, 7, 21, 4, 6, 7, 4, 6, 4, 5, 8, 21, 7, 4, 22, 3, 10, 11, 21, 11, 12, 20, 21, 11, 20, 4, 20, 22, 3, 22, 2, 20, 19, 22, 18, 1, 19, 0, 18, 17, 1, 18, 0, 14, 18, 19, 13, 14, 19, 22, 19, 1, 2, 22, 1, 12, 13, 19, 20, 12, 19], "vertices": [4, 28, -6.64, 15.42, 0.55397, 29, -21.01, 14.82, 0.1028, 33, -11.74, 33.95, 0.00989, 4, 2.02, -32.52, 0.33333, 4, 28, 4.46, 20.32, 0.61723, 29, -10.06, 20.04, 0.23513, 33, -0.42, 29.58, 0.03652, 4, -8.91, -37.79, 0.11111, 3, 28, 15.42, 22.48, 0.50252, 29, 0.83, 22.51, 0.36809, 33, 8.86, 23.38, 0.12939, 3, 28, 19.65, 19.33, 0.31812, 29, 5.16, 19.48, 0.34696, 33, 9.64, 18.16, 0.33492, 3, 28, 26.75, 10.4, 0.15045, 29, 12.51, 10.76, 0.24257, 33, 8.36, 6.82, 0.60698, 3, 28, 27.22, 14.07, 0.0501, 29, 12.87, 14.45, 0.12379, 33, 11.29, 9.09, 0.82612, 3, 28, 33.73, 13.99, 0.004, 29, 19.38, 14.55, 0.05556, 33, 15.84, 4.44, 0.94045, 3, 28, 34.92, 9.89, 0.00043, 29, 20.69, 10.49, 0.08869, 33, 13.78, 0.69, 0.91088, 3, 28, 34.19, -5.28, 0.00095, 29, 20.4, -4.69, 0.21358, 33, 2.56, -9.53, 0.78547, 3, 28, 32.01, -11.13, 0.00728, 29, 18.39, -10.6, 0.39945, 33, -3.11, -12.13, 0.59327, 3, 28, 27.94, -11.99, 0.03964, 29, 14.34, -11.59, 0.57368, 33, -6.61, -9.87, 0.38668, 3, 28, 24.29, -12.77, 0.13979, 29, 10.71, -12.47, 0.65302, 33, -9.74, -7.85, 0.20719, 3, 28, 18.71, -13.96, 0.32612, 29, 5.17, -13.82, 0.58888, 33, -14.53, -4.75, 0.085, 4, 28, 11.58, -15.48, 0.46037, 29, -1.91, -15.54, 0.40606, 33, -20.65, -0.79, 0.02246, 4, -17.22, -2.25, 0.11111, 4, 28, 5.03, -16.88, 0.45623, 29, -8.42, -17.13, 0.20851, 33, -26.28, 2.84, 0.00193, 4, -10.72, -0.64, 0.33333, 4, 28, -3.01, -16.43, 0.33357, 29, -16.47, -16.9, 0.06977, 4, -2.67, -0.82, 0.58333, 7, -23.71, 6.79, 0.01333, 5, 28, -9.2, -4.19, 0.34594, 29, -23.01, -4.85, 0.01322, 33, -27.39, 21.88, 0, 4, 3.93, -12.85, 0.60417, 7, -17.52, -5.45, 0.03667, 5, 28, -10.83, 6.22, 0.37342, 29, -24.94, 5.5, 0.02891, 33, -21.21, 30.4, 0.00101, 4, 5.91, -23.19, 0.58333, 7, -15.89, -15.86, 0.01333, 5, 28, -0.31, -0.36, 0.46632, 29, -14.23, -0.77, 0.09677, 33, -18.39, 18.31, 0.00227, 4, -4.83, -16.97, 0.38042, 7, -26.42, -9.27, 0.05422, 5, 28, 9.24, 1.28, 0.52645, 29, -4.73, 1.15, 0.20732, 33, -10.48, 12.73, 0.11038, 4, -14.32, -18.93, 0.12562, 7, -35.97, -10.9, 0.03022, 4, 28, 19.16, 1.47, 0.31657, 29, 5.18, 1.62, 0.36247, 33, -3.32, 5.86, 0.27296, 7, -45.89, -11.09, 0.048, 4, 28, 26.17, 0.69, 0.15809, 29, 12.21, 1.05, 0.38812, 33, 1.1, 0.36, 0.40579, 7, -52.9, -10.31, 0.048, 3, 28, 16.32, 13.39, 0.47804, 29, 1.99, 13.45, 0.43593, 33, 3.08, 16.31, 0.08603], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 28, 30, 30, 32, 32, 34, 26, 28, 24, 26, 22, 24, 18, 20, 20, 22, 32, 36, 36, 38, 38, 40, 40, 42, 4, 44], "width": 67, "height": 75}}, "a14": {"a14": {"type": "mesh", "uvs": [0.49496, 0.02234, 0.77861, 0.05171, 0.97938, 0.23807, 0.97931, 0.40144, 0.97923, 0.59008, 0.97915, 0.77342, 0.86834, 0.82017, 0.63808, 0.8213, 0.55361, 0.98031, 0.21206, 0.98021, 0.10266, 0.94617, 0.16938, 0.82086, 0.16942, 0.70713, 0.02092, 0.61365, 0.02101, 0.45886, 0.11447, 0.2458, 0.1921, 0.06883, 0.28637, 0.00074, 0.53686, 0.29664, 0.45882, 0.57435, 0.40586, 0.76823], "triangles": [17, 18, 16, 3, 18, 2, 18, 17, 0, 18, 1, 2, 18, 0, 1, 9, 20, 8, 10, 11, 9, 9, 11, 20, 4, 19, 3, 3, 19, 18, 8, 20, 7, 6, 7, 19, 11, 12, 20, 4, 5, 6, 19, 7, 20, 4, 6, 19, 20, 12, 19, 19, 12, 14, 12, 13, 14, 19, 15, 18, 18, 15, 16, 14, 15, 19], "vertices": [4, 37, -5.99, -7.4, 0.16926, 38, -22.88, -8.37, 0.00519, 4, 6.98, 25.79, 0.80556, 7, -13.16, 33.06, 0.02, 4, 37, -7.89, 5.88, 0.18442, 38, -25.35, 4.81, 0.07308, 4, 5.32, 12.48, 0.6875, 7, -15.27, 19.82, 0.055, 4, 37, -1.23, 17.34, 0.30698, 38, -19.17, 16.55, 0.08969, 4, -4.13, 3.18, 0.58333, 7, -25.03, 10.84, 0.02, 3, 37, 6.68, 19.38, 0.4291, 38, -11.35, 18.92, 0.23757, 4, -12.3, 3.3, 0.33333, 3, 37, 15.82, 21.73, 0.45684, 38, -2.33, 21.65, 0.43205, 4, -21.73, 3.44, 0.11111, 3, 37, 24.7, 24.01, 0.3754, 38, 6.45, 24.31, 0.61167, 42, -9.83, 24.2, 0.01293, 3, 37, 28.26, 19.55, 0.22404, 38, 10.19, 20, 0.65541, 42, -6.03, 19.94, 0.12055, 3, 37, 31.02, 9.09, 0.10863, 38, 13.39, 9.66, 0.55633, 42, -2.72, 9.63, 0.33505, 3, 37, 39.71, 7.23, 0.03958, 38, 22.15, 8.17, 0.33255, 42, 6.06, 8.24, 0.62788, 3, 37, 43.71, -8.32, 0.00783, 38, 26.81, -7.19, 0.18467, 42, 10.89, -7.07, 0.8075, 3, 37, 43.34, -13.72, 0.02009, 38, 26.67, -12.61, 0.18299, 42, 10.82, -12.48, 0.79691, 3, 37, 36.49, -12.25, 0.07431, 38, 19.76, -11.42, 0.3321, 42, 3.9, -11.38, 0.59359, 3, 37, 30.99, -13.67, 0.17604, 38, 14.32, -13.07, 0.49115, 42, -1.53, -13.09, 0.33281, 3, 37, 28.2, -21.59, 0.33481, 38, 11.88, -21.11, 0.53339, 42, -3.88, -21.15, 0.13181, 3, 37, 20.71, -23.52, 0.52655, 38, 4.47, -23.35, 0.44386, 42, -11.26, -23.48, 0.02959, 4, 37, 9.29, -21.92, 0.61626, 38, -7, -22.24, 0.26951, 42, -22.74, -22.5, 0.00312, 4, -3.94, 43.83, 0.11111, 4, 37, -0.19, -20.6, 0.54568, 38, -16.53, -21.32, 0.12078, 42, -32.28, -21.69, 0.00021, 4, 4.86, 40.06, 0.33333, 3, 37, -4.59, -17.16, 0.29971, 38, -21.07, -18.07, 0.03363, 4, 8.2, 35.58, 0.66667, 5, 37, 6.8, -2.07, 0.26795, 38, -10.33, -2.51, 0.26022, 42, -26.3, -2.8, 0.00861, 4, -6.76, 24.02, 0.39921, 7, -26.96, 31.76, 0.064, 5, 37, 21.16, -2.15, 0.22298, 38, 4.02, -1.98, 0.55987, 42, -11.95, -2.12, 0.02324, 4, -20.6, 27.88, 0.13258, 7, -40.65, 36.09, 0.06133, 4, 37, 31.17, -2.14, 0.17852, 38, 14.02, -1.55, 0.71381, 42, -1.96, -1.57, 0.03567, 7, -50.21, 39.04, 0.072], "hull": 18, "edges": [2, 4, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 2, 36, 36, 38, 38, 40, 4, 6, 6, 8, 8, 10, 28, 30, 30, 32, 2, 0, 0, 34], "width": 76, "height": 80}}, "a15": {"a13": {"type": "mesh", "uvs": [0.77717, 0.09017, 0.90629, 0.32117, 0.97018, 0.55179, 0.9, 0.64502, 0.6957, 0.80493, 0.78353, 0.81118, 0.78905, 0.9496, 0.69279, 0.97903, 0.33135, 0.97894, 0.18974, 0.93852, 0.16448, 0.85283, 0.14183, 0.776, 0.10726, 0.65873, 0.06304, 0.50875, 0.0224, 0.37088, 0.02405, 0.19963, 0.30811, 0.05567, 0.5537, 0.01037, 0.40924, 0.24072, 0.45901, 0.44199, 0.47472, 0.65263, 0.46424, 0.80242, 0.75496, 0.58008], "triangles": [4, 22, 3, 6, 4, 5, 6, 7, 4, 7, 21, 4, 20, 12, 19, 4, 20, 22, 21, 11, 20, 11, 12, 20, 10, 11, 21, 8, 21, 7, 21, 8, 10, 8, 9, 10, 21, 20, 4, 12, 13, 19, 2, 22, 1, 22, 19, 1, 13, 14, 19, 14, 18, 19, 1, 18, 0, 0, 18, 17, 18, 1, 19, 20, 19, 22, 3, 22, 2, 14, 15, 18, 15, 16, 18, 18, 16, 17], "vertices": [2, 54, -8.07, 9.38, 0.99994, 59, -17.5, 30.85, 6e-05, 3, 54, 2.5, 15.34, 0.94364, 55, -12.38, 15.55, 0.03293, 59, -5.83, 27.51, 0.02343, 3, 54, 13.2, 18.57, 0.70123, 55, -1.63, 18.6, 0.18556, 59, 3.97, 22.16, 0.11322, 3, 54, 17.72, 15.85, 0.55791, 55, 2.85, 15.8, 0.26464, 59, 5.21, 17.03, 0.17745, 3, 54, 25.66, 7.65, 0.03331, 55, 10.65, 7.48, 0.10692, 59, 4.95, 5.63, 0.85977, 3, 54, 25.77, 11.35, 0.00341, 55, 10.82, 11.18, 0.00557, 59, 7.66, 8.15, 0.99102, 1, 59, 12.62, 3.92, 1, 1, 59, 10.9, 0.01, 1, 2, 55, 19.34, -7.53, 0.89775, 59, 0.64, -11.18, 0.10225, 3, 54, 33, -13.25, 0.00551, 55, 17.65, -13.54, 0.99162, 59, -4.78, -14.28, 0.00287, 2, 54, 29.03, -14.52, 0.03307, 55, 13.66, -14.74, 0.96693, 2, 54, 25.47, -15.65, 0.10117, 55, 10.08, -15.81, 0.89883, 2, 54, 20.04, -17.37, 0.3045, 55, 4.62, -17.45, 0.6955, 2, 54, 13.09, -19.58, 0.63903, 55, -2.36, -19.55, 0.36097, 2, 54, 6.71, -21.62, 0.84501, 55, -8.78, -21.48, 0.15499, 2, 54, -1.33, -21.95, 0.94654, 55, -16.82, -21.68, 0.05346, 2, 54, -8.69, -10.38, 0.99957, 55, -23.99, -9.99, 0.00043, 1, 54, -11.34, -0.18, 1, 2, 54, -0.22, -5.7, 0.99729, 55, -15.44, -5.45, 0.00271, 2, 54, 9.12, -3.13, 0.97347, 55, -6.06, -3.03, 0.02653, 2, 54, 18.98, -1.98, 0.00734, 55, 3.81, -2.04, 0.99266, 1, 55, 10.86, -2.24, 1, 3, 54, 14.98, 9.61, 0.56893, 55, 0, 9.61, 0.29296, 59, -1.15, 14.6, 0.13811], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 28, 30, 30, 32, 32, 34, 26, 28, 24, 26, 22, 24, 18, 20, 20, 22, 32, 36, 36, 38, 38, 40, 40, 42, 4, 44], "width": 67, "height": 75}}, "a16": {"a10": {"type": "mesh", "uvs": [0.82912, 0.15096, 0.85228, 0.13796, 0.92733, 0.01651, 0.96392, 0.02749, 0.99412, 0.05483, 0.99429, 0.12074, 0.92088, 0.16491, 0.85037, 0.23552, 0.36735, 0.7121, 0.26198, 0.84006, 0.23122, 0.8359, 0.11988, 0.98356, 0.06221, 0.98379, 0.02568, 0.96836, 0.01212, 0.9561, 0.00108, 0.93612, 0.11746, 0.85554, 0.15915, 0.81736, 0.20978, 0.70566, 0.28071, 0.69752, 0.31237, 0.66737, 0.51738, 0.44605, 0.79409, 0.17511], "triangles": [6, 1, 2, 5, 6, 2, 3, 5, 2, 5, 3, 4, 6, 7, 0, 6, 0, 1, 22, 0, 7, 21, 22, 7, 8, 20, 21, 8, 21, 7, 10, 18, 19, 17, 18, 10, 19, 20, 8, 9, 10, 19, 8, 9, 19, 13, 14, 15, 16, 13, 15, 10, 11, 16, 10, 16, 17, 12, 13, 16, 11, 12, 16], "vertices": [1, 63, -86.69, -4.43, 1, 1, 63, -90.99, -3.19, 1, 1, 63, -115.65, -8.82, 1, 1, 63, -118.18, -3.02, 1, 1, 63, -117.79, 3.99, 1, 1, 63, -108.89, 12.03, 1, 1, 63, -94.87, 8.45, 1, 1, 63, -77.58, 8.45, 1, 1, 63, 39.89, 7.55, 1, 1, 63, 68.76, 10.28, 1, 1, 63, 71.57, 6.02, 1, 1, 63, 103.76, 10.41, 1, 1, 63, 110.12, 3.41, 1, 1, 63, 112.03, -2.92, 1, 1, 63, 111.86, -6.07, 1, 1, 63, 110.37, -9.84, 1, 1, 63, 86.7, -5.46, 1, 1, 63, 76.96, -5.02, 1, 1, 63, 56.3, -12.45, 1, 1, 63, 47.42, -4.79, 1, 1, 63, 39.86, -4.6, 1, 1, 63, -12.57, -6.53, 1, 1, 63, -79.58, -5.77, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0], "width": 275, "height": 304}}, "gunguang/dsd_18": {"gunguang/dsd_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66.65, -24.93, -244.35, -24.93, -244.35, 152.07, 66.65, 152.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 311, "height": 177}, "gunguang/dsd_19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66.65, -24.93, -244.35, -24.93, -244.35, 152.07, 66.65, 152.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 311, "height": 177}, "gunguang/dsd_21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66.65, -24.93, -244.35, -24.93, -244.35, 152.07, 66.65, 152.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 311, "height": 177}, "gunguang/dsd_23": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66.65, -24.93, -244.35, -24.93, -244.35, 152.07, 66.65, 152.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 311, "height": 177}, "gunguang/dsd_25": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66.65, -24.93, -244.35, -24.93, -244.35, 152.07, 66.65, 152.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 311, "height": 177}, "gunguang/dsd_27": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66.65, -24.93, -244.35, -24.93, -244.35, 152.07, 66.65, 152.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 311, "height": 177}, "gunguang/dsd_29": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66.65, -24.93, -244.35, -24.93, -244.35, 152.07, 66.65, 152.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 311, "height": 177}}, "lizi": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm2": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm3": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm4": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm5": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm2": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm3": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm4": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm5": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm6": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm7": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm8": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm9": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm10": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm11": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "shuyxme0xm1": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm1": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm1xm2": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm2": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm3": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm9": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm10": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm11": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "xiongmaolight": {"xiongmaolight": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [70.03, -81.65, -105.97, -81.65, -105.97, 101.35, 70.03, 101.35], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 176, "height": 183}}, "xmazhuti_000xm2": {"azhuti_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}}, "xmc_0": {"c_0000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}}, "xmc_0000": {"c_0000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}}, "xmc_xm1": {"c_0000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}}, "xmc_xm2": {"c_0000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}}, "xmc_xm3": {"c_0000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}, "c_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.5, -175, -76.5, -175, -76.5, 175, 76.5, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 175}}, "xme": {"e": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128.44, -195.93, -128.44, -195.93, -128.44, 195.93, 128.44, 195.93], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 254}}, "xmef": {"f": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80.65, -76.69, -96.92, -76.69, -87.11, 79.25, 90.45, 79.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 115, "height": 116}}}}], "events": {"atk": {}}, "animations": {"panda_appear": {"slots": {"a0": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a2_1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "color": "ffffff00"}], "attachment": [{"time": 0.8667, "name": "a2_1"}, {"time": 1.1333, "name": null}, {"time": 2.2, "name": "a2_1"}, {"time": 2.4667, "name": null}]}, "a3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a6": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a9": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a10": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a11": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a12": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a13": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a14": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffffff"}]}, "a16": {"color": [{"color": "17ff0000", "curve": "stepped"}, {"time": 0.9333, "color": "17ff0000", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "17ff00ff"}]}, "lizi": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm1xm2": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.6, "name": null}]}, "lizixm1xm3": {"attachment": [{"time": 1.3, "name": "lizi"}, {"time": 2.3333, "name": null}]}, "lizixm1xm4": {"attachment": [{"time": 1.2667, "name": "lizi"}, {"time": 2.2333, "name": null}]}, "lizixm1xm5": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.6667, "name": null}]}, "lizixm2": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm3": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm4": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm5": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm6": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm7": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 2.0667, "name": null}]}, "lizixm8": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.8667, "name": null}]}, "lizixm9": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.7667, "name": null}]}, "lizixm10": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.7333, "name": null}]}, "lizixm11": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "shuyxme0xm1": {"attachment": [{"name": "shuye01"}, {"time": 0.9, "name": null}]}, "shuyxmexm1": {"attachment": [{"time": 0.1667, "name": "shuye01"}, {"time": 1.3333, "name": null}]}, "shuyxmexm1xm2": {"color": [{"time": 1.8, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}], "attachment": [{"time": 1.0333, "name": "shuye01"}, {"time": 2.0667, "name": null}]}, "shuyxmexm2": {"attachment": [{"time": 0.2667, "name": "shuye01"}, {"time": 0.9, "name": null}]}, "shuyxmexm3": {"attachment": [{"time": 0.2667, "name": "shuye01"}, {"time": 1.2333, "name": null}]}, "shuyxmexm9": {"color": [{"time": 1.6, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00"}], "attachment": [{"time": 1.0333, "name": "shuye01"}, {"time": 1.9667, "name": null}]}, "shuyxmexm10": {"color": [{"time": 1.4, "color": "ffffffff"}, {"time": 1.6333, "color": "ffffff00"}], "attachment": [{"time": 1.0333, "name": "shuye01"}, {"time": 1.6667, "name": null}]}, "shuyxmexm11": {"color": [{"time": 1.8, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00"}], "attachment": [{"time": 1.0333, "name": "shuye01"}, {"time": 2.1333, "name": null}]}, "xiongmaolight": {"color": [{"time": 0.9667, "color": "4fff0000", "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "color": "50ff00ff", "curve": 0.25, "c3": 0.75}, {"time": 1.5, "color": "4fff0000"}], "attachment": [{"time": 0.9667, "name": "xiongmaolight"}]}, "xmazhuti_000xm2": {"attachment": [{"time": 0.2, "name": "azhuti_0002"}, {"time": 0.2333, "name": "azhuti_0003"}, {"time": 0.3, "name": "azhuti_0004"}, {"time": 0.4, "name": "azhuti_0006"}, {"time": 0.5, "name": "azhuti_0007"}, {"time": 0.6, "name": "azhuti_0009"}, {"time": 0.7, "name": "azhuti_0011"}, {"time": 0.8, "name": "azhuti_0012"}, {"time": 0.9, "name": "azhuti_0013"}, {"time": 1, "name": "azhuti_0014"}, {"time": 1.1, "name": "azhu<PERSON>_0015"}, {"time": 1.2, "name": "azhuti_0016"}, {"time": 1.2667, "name": "az<PERSON><PERSON>_0017"}, {"time": 1.3667, "name": "azhuti_0019"}, {"time": 1.4667, "name": "azhuti_0021"}, {"time": 1.5333, "name": null}]}, "xmc_0": {"attachment": [{"time": 0.1, "name": "c_0000"}, {"time": 0.2, "name": "c_0001"}, {"time": 0.3, "name": "c_0002"}, {"time": 0.4, "name": "c_0003"}, {"time": 0.5, "name": "c_0004"}, {"time": 0.6, "name": "c_0005"}, {"time": 0.7, "name": "c_0006"}, {"time": 0.8, "name": "c_0007"}, {"time": 0.9, "name": "c_0008"}, {"time": 1, "name": "c_0009"}, {"time": 1.1, "name": null}]}, "xmc_0000": {"attachment": [{"name": "c_0000"}, {"time": 0.1, "name": "c_0001"}, {"time": 0.2, "name": "c_0002"}, {"time": 0.3, "name": "c_0003"}, {"time": 0.4, "name": "c_0004"}, {"time": 0.5, "name": "c_0005"}, {"time": 0.6, "name": "c_0006"}, {"time": 0.7, "name": "c_0007"}, {"time": 0.8, "name": "c_0008"}, {"time": 0.9, "name": "c_0009"}, {"time": 1, "name": null}]}, "xmc_xm1": {"attachment": [{"time": 0.2333, "name": "c_0000"}, {"time": 0.3333, "name": "c_0001"}, {"time": 0.4333, "name": "c_0002"}, {"time": 0.5333, "name": "c_0003"}, {"time": 0.6333, "name": "c_0004"}, {"time": 0.7333, "name": "c_0005"}, {"time": 0.8333, "name": "c_0006"}, {"time": 0.9333, "name": "c_0007"}, {"time": 1.0333, "name": "c_0008"}, {"time": 1.1333, "name": "c_0009"}, {"time": 1.2333, "name": null}]}, "xmc_xm2": {"attachment": [{"time": 0.6, "name": "c_0000"}, {"time": 0.7, "name": "c_0001"}, {"time": 0.8, "name": "c_0002"}, {"time": 0.8667, "name": "c_0003"}, {"time": 0.9333, "name": "c_0004"}, {"time": 1, "name": "c_0005"}, {"time": 1.0667, "name": "c_0006"}, {"time": 1.1333, "name": "c_0007"}, {"time": 1.2, "name": "c_0008"}, {"time": 1.2667, "name": "c_0009"}, {"time": 1.3333, "name": null}]}, "xmc_xm3": {"attachment": [{"time": 0.2, "name": "c_0000"}, {"time": 0.3, "name": "c_0001"}, {"time": 0.4, "name": "c_0002"}, {"time": 0.5, "name": "c_0003"}, {"time": 0.6, "name": "c_0004"}, {"time": 0.7, "name": "c_0005"}, {"time": 0.8, "name": "c_0006"}, {"time": 0.9, "name": "c_0007"}, {"time": 1, "name": "c_0008"}, {"time": 1.1, "name": "c_0009"}, {"time": 1.2, "name": null}]}, "xme": {"color": [{"time": 1, "color": "ffffffff"}, {"time": 1.1333, "color": "08a11c00"}], "attachment": [{"time": 0.9, "name": "e"}, {"time": 1.1667, "name": null}]}, "xmef": {"color": [{"time": 1.0667, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0.9333, "name": "f"}]}}, "bones": {"bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone4": {"translate": [{"x": 0.02, "y": 0.39, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.13, "y": 2.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.02, "y": 0.39, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.13, "y": 2.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.02, "y": 0.39}]}, "bone5": {"translate": [{"x": 1.29, "y": -0.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.57, "y": -0.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 1.29, "y": -0.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 2.57, "y": -0.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.29, "y": -0.02}]}, "bone6": {"rotate": [{"angle": 1.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 1.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.77}]}, "bone7": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 3, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone9": {"rotate": [{"angle": 3.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.4667, "angle": 0.77, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8, "angle": 0.77, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.18}]}, "a8": {"rotate": [{"angle": 2.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 3.28, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4667, "angle": 2.08, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 2.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "angle": 3.28, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.8, "angle": 2.08, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 2.49}]}, "a9": {"rotate": [{"angle": 1.21, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 3.28, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "angle": 3.14, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.0667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 1.21, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.7333, "angle": 3.28, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.8, "angle": 3.14, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.4, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 1.21}]}, "bone10": {"rotate": [{"angle": -1.81, "curve": 0.298, "c2": 0.21, "c3": 0.645, "c4": 0.59}, {"time": 0.2, "angle": -0.87, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -0.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5667, "angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -2.03, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "angle": -1.81, "curve": 0.298, "c2": 0.21, "c3": 0.645, "c4": 0.59}, {"time": 1.5333, "angle": -0.87, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 1.7, "angle": -0.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9, "angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -2.03, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": -1.81}]}, "bone11": {"rotate": [{"angle": 3.35, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": 3.07, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.2, "angle": 3.34, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 0.3667, "angle": 4.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7667, "angle": 6.42, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": 3.35, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 1.4333, "angle": 3.07, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.5333, "angle": 3.34, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 1.7, "angle": 4.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.1, "angle": 6.42, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": 3.35}]}, "a12": {"rotate": [{"angle": 0.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 1.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.21}]}, "a17": {"rotate": [{"angle": -2.35, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -5.16, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.3, "angle": -4.2, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.8667, "angle": 6.48, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3, "angle": -1.62, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 1.3333, "angle": -2.35, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "angle": -5.16, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.6333, "angle": -4.2, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 2.2, "angle": 6.48, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6333, "angle": -1.62, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 2.6667, "angle": -2.35}]}, "a18": {"rotate": [{"angle": -1.7, "curve": 0.333, "c2": 0.33, "c3": 0.698, "c4": 0.77}, {"time": 0.3, "angle": -5.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 0.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3, "angle": -1.26, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 1.3333, "angle": -1.7, "curve": 0.333, "c2": 0.33, "c3": 0.698, "c4": 0.77}, {"time": 1.6333, "angle": -5.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 0.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6333, "angle": -1.26, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 2.6667, "angle": -1.7}]}, "bone14": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.71, "y": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.71, "y": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone20": {"rotate": [{"angle": -0.13}]}, "bone21": {"rotate": [{"angle": -1.11}]}, "bone28": {"rotate": [{"angle": 3.45, "curve": 0.327, "c2": 0.31, "c3": 0.673, "c4": 0.69}, {"time": 0.2, "angle": -4.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4333, "angle": -10.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 9.45, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 3.45, "curve": 0.327, "c2": 0.31, "c3": 0.673, "c4": 0.69}, {"time": 1.5333, "angle": -4.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.7667, "angle": -10.19, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 9.45, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 3.45}]}, "bone29": {"rotate": [{"angle": 16.72, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2, "angle": 9.33, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6667, "angle": -13.78, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 16.72, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.5333, "angle": 9.33, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2, "angle": -13.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 16.72}]}, "bone30": {"rotate": [{"angle": -3.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 7.96, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -3.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "angle": -8.15, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 7.96, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -3.24}]}, "bone31": {"rotate": [{"angle": 12.34, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 21.43, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 12.34, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 21.43, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 12.34}]}, "bone26": {"rotate": [{"angle": -1.27}]}, "bone27": {"rotate": [{"angle": 2.71}]}, "a11": {"rotate": [{"angle": -4.61, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.0667, "angle": -0.63, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.3, "angle": -4.85, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -4.61, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 1.9667, "angle": 9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.4, "angle": -0.63, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.6333, "angle": -4.85, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": -4.61}]}, "a13": {"rotate": [{"angle": -13.27, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 13.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.0667, "angle": 4.66, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.3333, "angle": -13.27, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 13.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.4, "angle": 4.66, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 2.6667, "angle": -13.27}]}, "a14": {"rotate": [{"angle": 5.3, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "angle": -17.66, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.0667, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1, "angle": 15.36, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 5.3, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 1.7667, "angle": -17.66, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.4, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.4333, "angle": 15.36, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 5.3}]}, "a15": {"rotate": [{"angle": -3.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -12.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.0667, "angle": 9.84, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 1.1333, "angle": 6.9, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -3.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "angle": -12.54, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.4, "angle": 9.84, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 2.4667, "angle": 6.9, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -3.04}]}, "a16": {"rotate": [{"angle": 9.27, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": -17.66, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.0667, "angle": 12.63, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 1.1333, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1667, "angle": 15.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 9.27, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "angle": -17.66, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.4, "angle": 12.63, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 2.4667, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.5, "angle": 15.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 9.27}]}, "a22": {"rotate": [{"angle": 5.3, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "angle": -17.66, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.0667, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1, "angle": 15.36, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 5.3, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 1.7667, "angle": -17.66, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.4, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.4333, "angle": 15.36, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 5.3}]}, "a21": {"rotate": [{"angle": -13.27, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 13.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.0667, "angle": 4.66, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.3333, "angle": -13.27, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 13.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.4, "angle": 4.66, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 2.6667, "angle": -13.27}]}, "a24": {"rotate": [{"angle": 9.27, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": -17.66, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.0667, "angle": 12.63, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 1.1333, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1667, "angle": 15.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 9.27, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "angle": -17.66, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.4, "angle": 12.63, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 2.4667, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.5, "angle": 15.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 9.27}]}, "a23": {"rotate": [{"angle": -3.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -12.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.0667, "angle": 9.84, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 1.1333, "angle": 6.9, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -3.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "angle": -12.54, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.4, "angle": 9.84, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 2.4667, "angle": 6.9, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -3.04}]}, "a20": {"rotate": [{"angle": -4.61, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.0667, "angle": -0.63, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.3, "angle": -4.85, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -4.61, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 1.9667, "angle": 9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.4, "angle": -0.63, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.6333, "angle": -4.85, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": -4.61}]}, "xmlight": {"scale": [{"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 1.307, "y": 1.307}]}, "xmexishou_xm1": {"rotate": [{"angle": -121.41}], "translate": [{"x": -35.45, "y": -58.05}], "scale": [{"x": 2, "y": 2}]}, "xmexishou_xm2": {"rotate": [{"time": 0.1, "angle": 108.88}], "translate": [{"time": 0.1, "x": 359.57, "y": 245.57}], "scale": [{"x": 2, "y": 2}]}, "xme": {"translate": [{"time": 0.9, "x": 176.64, "y": 121.74}], "scale": [{"time": 0.9, "x": 1.366, "y": 1.366}, {"time": 1, "x": 1.702, "y": 1.455}, {"time": 1.1333, "x": 4.037, "y": 2.96}]}, "xmbonxmexm6": {"rotate": [{}, {"time": 0.8667, "angle": 177.61}], "translate": [{"x": -456.92, "y": 255.61, "curve": 0.193, "c2": 0.4, "c3": 0.75}, {"time": 0.8667, "x": 291.49, "y": -56.76}], "scale": [{}, {"time": 0.8667, "x": 0.444, "y": 0.444}]}, "xmbonxmexm1xm5": {"rotate": [{"time": 1.0333, "angle": 115.56}, {"time": 1.9333, "angle": 150}], "translate": [{"time": 1.0333, "x": 97.63, "y": 254.93, "curve": 0.159, "c2": 0.29, "c3": 0.75}, {"time": 1.9333, "x": -166.31, "y": 653.78}], "scale": [{"time": 1.0333}, {"time": 1.9333, "x": 2.789, "y": 2.789}]}, "xmbonxmexm1xm6": {"rotate": [{"time": 1.0333, "angle": 134.45}, {"time": 1.6333, "angle": 90}], "translate": [{"time": 1.0333, "x": 320.02, "y": 273.92, "curve": 0.226, "c2": 0.35, "c3": 0.75}, {"time": 1.6333, "x": 420, "y": 435.21}]}, "xmbonxmexm1xm7": {"rotate": [{"time": 1.0333, "angle": 93.22}, {"time": 2.1, "angle": 160}], "translate": [{"time": 1.0333, "x": 97.63, "y": 132.89, "curve": 0.198, "c2": 0.38, "c3": 0.75}, {"time": 2.1, "x": -359.87, "y": 241.4}], "scale": [{"time": 1.0333}, {"time": 2.1, "x": 1.6, "y": 1.6}]}, "xmbonxmexm1xm8": {"rotate": [{"time": 1.0333}, {"time": 2.0333, "angle": 180}], "translate": [{"time": 1.0333, "x": 108.48, "y": 54.24, "curve": 0.15, "c2": 0.24, "c3": 0.75}, {"time": 2.0333, "x": -71.03, "y": -412.23}], "scale": [{"time": 1.0333}, {"time": 2.0333, "x": 2.347, "y": 2.347}]}, "lizi_zonxmeg": {"translate": [{"x": 171.52, "y": 120.6}]}, "xmbonxmexm7": {"translate": [{"time": 0.1667, "x": -49.58, "y": 756.71, "curve": 0.388, "c2": 0.02, "c3": 0.723, "c4": 0.45}, {"time": 0.6, "x": -162.36, "y": 579.98, "curve": 0.387, "c2": 0.32, "c3": 0.722, "c4": 0.67}, {"time": 0.9667, "x": -98.92, "y": 281.6, "curve": 0.418, "c2": 0.41, "c3": 0.753, "c4": 0.77}, {"time": 1.3, "x": 243.61, "y": 53.9}], "scale": [{"time": 0.1667}, {"time": 1.3, "x": 0.488, "y": 0.488}]}, "xma_zhuti": {"translate": [{"time": 0.2, "x": 123.05, "y": 265.66}], "scale": [{"time": 0.2, "x": 1.5, "y": 1.5}]}, "xmexishou_xm5": {"rotate": [{"time": 0.2, "angle": 164}], "translate": [{"time": 0.2, "x": -125.68, "y": 536.89}], "scale": [{"time": 0.2, "x": 2, "y": 2}]}, "xmexishou_xm3": {"rotate": [{"time": 0.2333, "angle": -30.26}], "translate": [{"time": 0.2333, "x": 409.36, "y": -190.6}], "scale": [{"time": 0.2333, "x": 2}]}, "xmbonxmexm8": {"translate": [{"time": 0.2667, "x": 732.99, "y": 362.19}, {"time": 0.8667, "x": 291.04, "y": 133.66}], "scale": [{"time": 0.2667}, {"time": 0.8667, "x": 0.567, "y": 0.567}]}, "xmbonxmexm9": {"rotate": [{"time": 0.2667}, {"time": 1.2, "angle": 180}], "translate": [{"time": 0.2667, "x": -213.92, "y": -415.79}, {"time": 1.2, "x": 304.31, "y": -18.08}], "scale": [{"time": 0.2667, "x": 2.181, "y": 2.181}, {"time": 1.2, "x": 0.778, "y": 0.778}]}, "xmexishou_xm4": {"rotate": [{"time": 0.6, "angle": -64.88}], "translate": [{"time": 0.6, "x": 252.89, "y": -266.94}], "scale": [{"time": 0.6, "x": 2, "y": 1.4}]}, "xmef": {"translate": [{"time": 0.9333, "x": 207.67, "y": 126.51}], "scale": [{"time": 0.9333, "x": 2.374, "y": 2.374}, {"time": 1.2, "x": 5.988, "y": 5.988}]}, "0": {"translate": [{"time": 1.0333, "x": -128.64, "y": 123.28, "curve": 0.159, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": -341.04, "y": 217.29}], "scale": [{"time": 1.0333}, {"time": 1.5333, "x": 0.438, "y": 0.438}]}, "xm1": {"translate": [{"time": 1.0333, "x": -91.12}, {"time": 1.5333, "x": -360.97}]}, "xm2": {"translate": [{"time": 1.0333, "x": 101.84, "y": -77.72}, {"time": 1.5333, "x": 349.06, "y": -42.9}]}, "xm3": {"translate": [{"time": 1.0333, "x": 58.2, "y": -46.45, "curve": 0.264, "c2": 0.47, "c3": 0.75}, {"time": 1.5333, "x": 339.2, "y": -125.48}]}, "xm4": {"translate": [{"time": 1.0333, "x": -5.36, "y": -80.4, "curve": 0.174, "c2": 0.4, "c3": 0.75}, {"time": 1.5333, "x": -214.28, "y": -334.75}]}, "xm5": {"translate": [{"time": 1.0333, "x": 96.48, "y": -91.12, "curve": 0.164, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": 338.51, "y": -312.8}]}, "xm6": {"translate": [{"time": 1.0333, "x": -37.52, "y": 155.44, "curve": 0.15, "c2": 0.35, "c3": 0.75}, {"time": 2.0333, "x": -339.46, "y": 276.76}], "scale": [{"time": 1.0333}, {"time": 2.0333, "x": 0.282, "y": 0.282}]}, "xm7": {"translate": [{"time": 1.0333, "x": 77.72, "y": -120.6, "curve": 0.231, "c2": 0.4, "c3": 0.75}, {"time": 1.8333, "x": 367.26, "y": 42.26}], "scale": [{"time": 1.0333}, {"time": 1.8333, "x": 0.367, "y": 0.367}]}, "xm8": {"translate": [{"time": 1.0333, "x": -115.58, "y": -102.08, "curve": 0.24, "c2": 0.43, "c3": 0.75}, {"time": 1.7333, "x": -400.6, "y": -29.7}], "scale": [{"time": 1.0333}, {"time": 1.7333, "x": 1.2, "y": 1.2}]}, "xm9": {"translate": [{"time": 1.0333, "x": 91.12, "y": -134}, {"time": 1.7, "x": 281.13, "y": -231.27}]}, "xm10": {"translate": [{"time": 1.0333, "x": -128.64, "y": 238.52, "curve": 0.088, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": -357.79, "y": 402.97}], "scale": [{"time": 1.0333}, {"time": 1.5333, "x": 0.164, "y": 0.164}]}, "xm11": {"translate": [{"time": 1.0333, "x": -40.2, "y": -99.16}, {"time": 1.5667, "x": -239.26, "y": -180.59}], "scale": [{"time": 1.0333}, {"time": 1.5667, "x": 0.188, "y": 0.188}]}, "xm1xm4": {"translate": [{"time": 1.0333, "curve": 0.207, "c2": 0.44, "c3": 0.75}, {"time": 1.6333, "x": -321.2, "y": -131.2}], "scale": [{"time": 1.0333}, {"time": 1.6333, "x": 0, "y": 0}]}, "xm1xm3": {"translate": [{"time": 1.2667, "x": -54.29, "y": 22.62, "curve": 0.183, "c2": 0.31, "c3": 0.75}, {"time": 2.2, "x": -160.6, "y": 126.67}], "scale": [{"time": 1.2667}, {"time": 2.2, "x": 0.224, "y": 0.224}]}, "xm1xm2": {"translate": [{"time": 1.3, "y": 122.15, "curve": 0.142, "c2": 0.3, "c3": 0.594, "c4": 0.73}, {"time": 1.7333, "x": 29.41, "y": 344.23, "curve": 0.39, "c2": 0.6, "c3": 0.756}, {"time": 2.3, "y": 395.85}], "scale": [{"time": 1.3}, {"time": 2.3, "x": 0.255, "y": 0.255}]}}}, "panda_idle": {"slots": {"a2_1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "color": "ffffff00"}], "attachment": [{"time": 0.8667, "name": "a2_1"}, {"time": 1.1333, "name": null}, {"time": 2.2, "name": "a2_1"}, {"time": 2.4667, "name": null}]}}, "bones": {"a21": {"rotate": [{"angle": -13.27, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 13.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.0667, "angle": 4.66, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.3333, "angle": -13.27, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 13.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.4, "angle": 4.66, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 2.6667, "angle": -13.27}]}, "a24": {"rotate": [{"angle": 9.27, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": -17.66, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.0667, "angle": 12.63, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 1.1333, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1667, "angle": 15.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 9.27, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "angle": -17.66, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.4, "angle": 12.63, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 2.4667, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.5, "angle": 15.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 9.27}]}, "a23": {"rotate": [{"angle": -3.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -12.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.0667, "angle": 9.84, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 1.1333, "angle": 6.9, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -3.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "angle": -12.54, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.4, "angle": 9.84, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 2.4667, "angle": 6.9, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -3.04}]}, "a20": {"rotate": [{"angle": -4.61, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.0667, "angle": -0.63, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.3, "angle": -4.85, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -4.61, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 1.9667, "angle": 9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.4, "angle": -0.63, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.6333, "angle": -4.85, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": -4.61}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone4": {"translate": [{"x": 0.02, "y": 0.39, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.13, "y": 2.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.02, "y": 0.39, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.13, "y": 2.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.02, "y": 0.39}]}, "bone5": {"translate": [{"x": 1.29, "y": -0.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.57, "y": -0.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 1.29, "y": -0.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 2.57, "y": -0.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.29, "y": -0.02}]}, "bone6": {"rotate": [{"angle": 1.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 1.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.77}]}, "bone7": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 3, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone9": {"rotate": [{"angle": 3.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.4667, "angle": 0.77, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.8, "angle": 0.77, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.18}]}, "a8": {"rotate": [{"angle": 2.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 3.28, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4667, "angle": 2.08, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 2.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "angle": 3.28, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.8, "angle": 2.08, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 2.49}]}, "a9": {"rotate": [{"angle": 1.21, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 3.28, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "angle": 3.14, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.0667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 1.21, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.7333, "angle": 3.28, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.8, "angle": 3.14, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.4, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 1.21}]}, "bone10": {"rotate": [{"angle": -1.81, "curve": 0.298, "c2": 0.21, "c3": 0.645, "c4": 0.59}, {"time": 0.2, "angle": -0.87, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -0.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5667, "angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -2.03, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "angle": -1.81, "curve": 0.298, "c2": 0.21, "c3": 0.645, "c4": 0.59}, {"time": 1.5333, "angle": -0.87, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 1.7, "angle": -0.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9, "angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -2.03, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": -1.81}]}, "bone11": {"rotate": [{"angle": 3.35, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": 3.07, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.2, "angle": 3.34, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 0.3667, "angle": 4.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7667, "angle": 6.42, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": 3.35, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 1.4333, "angle": 3.07, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.5333, "angle": 3.34, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 1.7, "angle": 4.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.1, "angle": 6.42, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": 3.35}]}, "a12": {"rotate": [{"angle": 0.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 1.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.21}]}, "a17": {"rotate": [{"angle": -2.35, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -5.16, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.3, "angle": -4.2, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.8667, "angle": 6.48, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3, "angle": -1.62, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 1.3333, "angle": -2.35, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "angle": -5.16, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.6333, "angle": -4.2, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 2.2, "angle": 6.48, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6333, "angle": -1.62, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 2.6667, "angle": -2.35}]}, "a18": {"rotate": [{"angle": -1.7, "curve": 0.333, "c2": 0.33, "c3": 0.698, "c4": 0.77}, {"time": 0.3, "angle": -5.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 0.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3, "angle": -1.26, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 1.3333, "angle": -1.7, "curve": 0.333, "c2": 0.33, "c3": 0.698, "c4": 0.77}, {"time": 1.6333, "angle": -5.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 0.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6333, "angle": -1.26, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 2.6667, "angle": -1.7}]}, "bone14": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.71, "y": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.71, "y": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone20": {"rotate": [{"angle": -0.13}]}, "bone21": {"rotate": [{"angle": -1.11}]}, "bone28": {"rotate": [{"angle": 3.45, "curve": 0.327, "c2": 0.31, "c3": 0.673, "c4": 0.69}, {"time": 0.2, "angle": -4.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4333, "angle": -10.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 9.45, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 3.45, "curve": 0.327, "c2": 0.31, "c3": 0.673, "c4": 0.69}, {"time": 1.5333, "angle": -4.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.7667, "angle": -10.19, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 9.45, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 3.45}]}, "bone29": {"rotate": [{"angle": 16.72, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2, "angle": 9.33, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6667, "angle": -13.78, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 16.72, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.5333, "angle": 9.33, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2, "angle": -13.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 16.72}]}, "bone30": {"rotate": [{"angle": -3.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 7.96, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -3.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "angle": -8.15, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 7.96, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -3.24}]}, "bone31": {"rotate": [{"angle": 12.34, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 21.43, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 12.34, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 21.43, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 12.34}]}, "a22": {"rotate": [{"angle": 5.3, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "angle": -17.66, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.0667, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1, "angle": 15.36, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 5.3, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 1.7667, "angle": -17.66, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.4, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.4333, "angle": 15.36, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 5.3}]}, "bone26": {"rotate": [{"angle": -1.27}]}, "bone27": {"rotate": [{"angle": 2.71}]}, "a11": {"rotate": [{"angle": -4.61, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.0667, "angle": -0.63, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.3, "angle": -4.85, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -4.61, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 1.9667, "angle": 9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.4, "angle": -0.63, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.6333, "angle": -4.85, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": -4.61}]}, "a13": {"rotate": [{"angle": -13.27, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 13.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.0667, "angle": 4.66, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.3333, "angle": -13.27, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 13.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.4, "angle": 4.66, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 2.6667, "angle": -13.27}]}, "a14": {"rotate": [{"angle": 5.3, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "angle": -17.66, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.0667, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1, "angle": 15.36, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 5.3, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 1.7667, "angle": -17.66, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.4, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.4333, "angle": 15.36, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 5.3}]}, "a15": {"rotate": [{"angle": -3.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -12.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.0667, "angle": 9.84, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 1.1333, "angle": 6.9, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -3.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "angle": -12.54, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.4, "angle": 9.84, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 2.4667, "angle": 6.9, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -3.04}]}, "a16": {"rotate": [{"angle": 9.27, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": -17.66, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.0667, "angle": 12.63, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 1.1333, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1667, "angle": 15.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 9.27, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "angle": -17.66, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.4, "angle": 12.63, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 2.4667, "angle": 14.79, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.5, "angle": 15.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 9.27}]}}}}}