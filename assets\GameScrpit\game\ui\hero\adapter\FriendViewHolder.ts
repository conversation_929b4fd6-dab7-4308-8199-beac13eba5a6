import { _decorator, Label, Node, Sprite } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { HeroModule } from "../../../../module/hero/HeroModule";
import { JsonMgr } from "../../../mgr/JsonMgr";
import Formate from "../../../../lib/utils/Formate";
import { FriendModule } from "../../../../module/friend/FriendModule";
import { times } from "../../../../lib/utils/NumbersUtils";
import { FriendExpandViewHolder } from "./FriendExpandViewHolder";
import ResMgr from "../../../../lib/common/ResMgr";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { AttrEnum, HeroAttrEnum } from "../../../GameDefine";
const { ccclass, property } = _decorator;
@ccclass("FriendViewHolder")
export class FriendViewHolder extends ViewHolder {
  @property(Node)
  private powerAdd: Node;
  @property(Node)
  private select: Node;
  @property(Node)
  private head: Node;
  @property(Node)
  private nodeLock: Node;
  @property(Node)
  private info: Node;

  private heroId: number;
  private friendId: number;

  public setSelect(select: boolean, expand: Node): boolean {
    // if (this.nodeLock.active) {
    //   return;
    // }
    let heroMessage = HeroModule.data.getHeroMessage(this.heroId);
    if (!heroMessage) {
      return;
    }
    if (select) {
      this.select.active = true;
      expand.getComponent(FriendExpandViewHolder).updateData(this.heroId, this.friendId);
    } else {
      this.select.active = false;
    }
    return true;
  }
  public init() {}
  public updateData(heroId: number, friendId: number) {
    this.heroId = heroId;
    this.friendId = friendId;
    let friendMessage = FriendModule.data.getFriendMessage(friendId);
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_FRIEND, `half/friend_${friendId}`, this.head.getComponent(Sprite));
    if (friendMessage) {
      this.nodeLock.active = false;
      if (heroMessage) {
        this.info.active = true;
        this.powerAdd.getComponent(Label).string = `战力+${Formate.format(this.calcHeroPowerAdd(heroId, friendId))}`;
      } else {
        this.info.active = false;
      }
    } else {
      this.nodeLock.active = true;
      this.info.active = false;
    }
  }
  private calcHeroPowerAdd(heroId: number, friendId: number): number {
    let heroBaseAttr = HeroModule.service.getHeroBaseAttr(heroId); //英雄基础属性
    let friendAttrAdd = FriendModule.service.getHeroAttrAddByFriendId(friendId);
    // 挚友属性增加部分
    let attack =
      times(heroBaseAttr[AttrEnum.攻击_2], friendAttrAdd[HeroAttrEnum.战将攻击百分比_12] / 10000) +
      friendAttrAdd[AttrEnum.攻击_2];
    let defense =
      times(heroBaseAttr[AttrEnum.防御_3], friendAttrAdd[HeroAttrEnum.战将防御百分比_13] / 10000) +
      friendAttrAdd[AttrEnum.防御_3];
    let blood =
      times(heroBaseAttr[AttrEnum.生命_1], friendAttrAdd[HeroAttrEnum.战将生命百分比_11] / 10000) +
      friendAttrAdd[AttrEnum.生命_1];
    let attackPowerRate = JsonMgr.instance.jsonList.c_attribute[2].powerRate1List[1];
    let defensePowerRate = JsonMgr.instance.jsonList.c_attribute[3].powerRate1List[1];
    let bloodPowerRate = JsonMgr.instance.jsonList.c_attribute[1].powerRate1List[1];
    let powerAdd = attack * attackPowerRate + defense * defensePowerRate + blood * bloodPowerRate;
    return powerAdd;
  }
}
