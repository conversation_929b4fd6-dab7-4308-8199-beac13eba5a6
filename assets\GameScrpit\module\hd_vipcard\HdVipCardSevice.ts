import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { HdVipMsgEnum } from "./HdVipCardConfig";
import { HdVipCardModule } from "./HdVipCardModule";

export class HdVipCardSevice {
  private _tickId: number = null;
  public async init() {
    if (this._tickId) {
      TickerMgr.clearInterval(this._tickId);
    }

    MsgMgr.off(HdVipMsgEnum.HDVIPMSGENUM_RED_DOT_UPDATE, this.updatePopover, this);
    MsgMgr.on(HdVipMsgEnum.HDVIPMSGENUM_RED_DOT_UPDATE, this.updatePopover, this);

    this._tickId = TickerMgr.setInterval(3, this.updatePopover.bind(this), false);
  }

  private updatePopover() {
    this.monthAward();
    this.lifeAward();
  }

  private monthAward() {
    let bool = false;
    if (HdVipCardModule.data.isMonthCard == true && HdVipCardModule.data.vipCardMessage.takeMonthDailyReward == false) {
      bool = true;
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_card.btn_month_select.id, bool);
  }

  private lifeAward() {
    let bool = false;
    if (HdVipCardModule.data.isLifeCard == true && HdVipCardModule.data.vipCardMessage.takeLifeDailyReward == false) {
      bool = true;
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_card.btn_year_unselect.id, bool);
  }

  //   /**  */
  // export interface VipCardMessage {
  //   /** 是否有购买终身卡 */
  //   life: boolean;
  //   /** 月卡的截止有效时间 */
  //   deadline: number;
  //   /** 是否有领取月卡的日常道具奖励 */
  //   takeMonthDailyReward: boolean;
  //   /** 是否有领取终身卡卡的日常道具奖励 */
  //   takeLifeDailyReward: boolean;
  // }
}
