import { _decorator } from "cc";
import GamePlay from "./GamePlay";
import { ActionInfo, PlayerBackInfo, RoundInfo } from "../FightDefine";
import FightManager from "../manager/FightManager";
import { ObjectManager } from "../manager/ObjectManager";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { MovementInfo } from "../section/StateSection";
import { JsonMgr } from "../../mgr/JsonMgr";
import RenderSection from "../../../lib/object/RenderSection";
import HPSection from "../../../lib/object/HpSection";
import { FightModule } from "../../../module/fight/src/FightModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

@ccclass("GPWatch")
export class GPWatch extends GamePlay {
  public play() {
    MsgMgr.on(MsgEnum.ON_FIGHT_SKIP, this.fightSkip, this);
    MsgMgr.on(MsgEnum.ON_FIGHT_SPEED, this.upFightSpeed, this);
    this._fightData = FightModule.instance.fightData;
    this._posMap = FightModule.instance.posMap;
    this._roundIndex = 0;
    this.initPlay();
  }

  public exit() {
    MsgMgr.off(MsgEnum.ON_FIGHT_SPEED, this.upFightSpeed, this);
    MsgMgr.off(MsgEnum.ON_FIGHT_SKIP, this.fightSkip, this);
  }

  // protected async startGame() {
  //   this._fightShowTimeTickerId = TickerMgr.setInterval(
  //     0.05,
  //     () => {
  //       this.fightShowTimeFun(null);
  //     },
  //     true
  //   );
  //   this._allRole.forEach((val) => {
  //     val.fightStartAni();
  //   });
  // }

  private async initPlay() {
    let queueList = this.newQueueList();
    await this.loadRolObject(queueList);
    await new Promise((res) => TickerMgr.setTimeout(0.2, res));
    await this.startGame();

    await this.roundFight(this._fightData.e);
    await new Promise((res) => TickerMgr.setTimeout(0.5, res));
    MsgMgr.emit(MsgEnum.ON_FIGHT_END);
  }

  protected async loadRolObject(queueList: Array<PlayerBackInfo>) {
    for (let i = 0; i < queueList.length; i++) {
      let role = await FightManager.instance.getSection(ObjectManager).callObject(queueList[i]);
      role
        .getSection(HPSection)
        .getRender()
        .children.forEach((val) => {
          val.active = false;
        });
      role.getSection(RenderSection).getHpPoint().getChildByName("lab_Name").active = false;
      this._allRole.set(queueList[i].dir, role);
    }
  }

  protected async roundFight(actionInfoList: Array<RoundInfo>) {
    for (this._roundIndex; this._roundIndex < actionInfoList.length; ) {
      if (FightManager.instance.fightOver == true) {
        return;
      }
      let roundInfo = actionInfoList[this._roundIndex];
      if (!roundInfo) {
        return;
      }
      MsgMgr.emit(MsgEnum.ON_FIGHT_ROUND_UPDATE, roundInfo.a);
      await this.upAllRoleState(roundInfo.b);
      await this.roundAtk(roundInfo.c);
      await this._roundIndex++;
      if (this._roundIndex >= actionInfoList.length) {
        this._roundIndex = 0;
      }
    }
  }

  /**普通攻击 --- 切换攻击角色的状态*/
  protected async atkCommon(info: ActionInfo, resolve: Function) {
    //log.log("info", info);
    //log.log("this._allRole", this._allRole);
    let role1 = this._allRole.get(info.a);
    role1.setSiblingIndex(1);
    let role2 = this._allRole.get(info.b);
    role2.setSiblingIndex(0);

    let spineShowDB = JsonMgr.instance.jsonList.c_spineShow[role1.getSpineId()];
    let skillIdList = spineShowDB.skillIdList;

    let index = 0;

    if (info.c == 1) {
      index = 0;
    } else {
      index = skillIdList.length - 1;
    }

    let skillId = skillIdList[index];

    let obj: MovementInfo = {
      hurtRole: role2,
      skillId: skillId,
      actionType: info.c,
      movementInfo: info.d,
      resolveBack: resolve,
    };

    await role1.emitMsg("OnMovementInfo", obj);
    await this.changeRoleAtkState(skillId, role1);
  }
}
