syntax = "proto3";
package sim;
import "Player.proto";

// 
message FarmBeeMetricMessage {
  // 还可以雇佣的蜜蜂数量
  int32 canHireBeeCnt = 1;
  // 空闲蜜蜂数量
  int32 relaxBeeCnt = 2;
  // 总蜜蜂数量
  int32 totalBeeCnt = 3;
}

// 
message FarmCollectRequest {
  // 采集的用户
  int64 farmUserId = 1;
  // 采集的葫芦索引，从0开始
  int32 rank = 2;
  // 葫芦的唯一标识
  int64 identifyId = 3;
  // 采集的蜜蜂的数量 数量0就是找回
  int32 beeCnf = 4;
}

// 
message FarmCollectResponse {
  // 当前正在进行采摘的福地指定的槽位信息
  FarmSlotMessage farmSlot = 1;
  // 剩余的采集次数
  int32 remainFetch = 2;
  // 需要删除的采集记录 默认-1
  int64 delDispatchKey = 3;
  // 需要更新的自己的的蜜蜂游历信息 指当前采集的信息
  map<int64,FarmDispatchMessage> updateDispatchMap = 4;
  // 自己福地的蜜蜂汇总统计信息
  FarmBeeMetricMessage farmBeeMetric = 5;
}

// 
message FarmCollectorMessage {
  // 当前采集用户的信息
  sim.PlayerBaseMessage playerBaseMessage = 1;
  // 蜜蜂数量
  int32 beeNum = 2;
  // 采集的结束时间 -1表示抢夺失败
  int64 endTime = 3;
  // 开始时长
  int64 startTime = 4;
  // 剩余的时长
  int64 remainTime = 5;
}

// 
message FarmDispatchMessage {
  // 福地主人信息
  sim.PlayerBaseMessage farmerMessage = 1;
  // 竞争对手的信息
  sim.PlayerBaseMessage otherMessage = 2;
  // 葫芦的质量
  int32 gourdId = 3;
  // 自己派遣的蜜蜂数量
  int32 ownBeeCnt = 4;
  // 对手派遣的蜜蜂数量
  int32 otherBeeCnt = 5;
  // 是否是己方获胜
  bool win = 6;
  // 采集结束时间
  int64 endTime = 7;
  // 开始时长
  int64 startTime = 8;
  // 剩余的时长
  int64 remainTime = 9;
}

// 
message FarmEnemyResponse {
  // 仇恨值集合
  map<int64,int32> hatredMap = 1;
  // 邻居
  repeated FarmSimpleMessage farmList = 2;
}

// 
message FarmFundBuyResponse {
  int32 fundIndex = 1;
  // 
  FarmFundMessage farmFundMessage = 2;
}

// 
message FarmFundMessage {
  // 采集次数
  int32 count = 1;
  // 是否已经充值
  bool recharge = 2;
  // 是否已经领取了基金成就奖励
  bool take = 3;
}

// 
message FarmLogMessage {
  // 福地主人
  sim.PlayerBaseMessage farmUserMessage = 1;
  // 采集人员
  sim.PlayerBaseMessage collectorMessage = 2;
  // 葫芦ID
  int32 gourdId = 3;
  // 采集结束的时间
  int64 endTime = 4;
}

// 
message FarmNeighborResponse {
  // 下次冷却刷新的截止时间
  int64 nextColdRefreshStamp = 1;
  // 邻居
  repeated FarmSimpleMessage farmList = 2;
}

// 
message FarmRecallAllResponse {
  // 玩家的蜜蜂的统计信息
  FarmBeeMetricMessage farmBeeMetric = 1;
  // 撤回的蜜蜂所在的槽位的变更后的信息
  repeated FarmSlotMessage updateSlotList = 2;
}

// 
message FarmRecallResponse {
  // 召回后的槽位信息
  FarmSlotMessage farmSlot = 1;
  // 玩家的dispatchMap 移除的蜜蜂派遣信息key
  int64 delDispatchKey = 2;
  // 玩家的蜜蜂的统计信息
  FarmBeeMetricMessage farmBeeMetric = 3;
}

// 
message FarmRewardMessage {
  // 神迹技能生效的次数
  int32 trigger = 1;
  repeated double rewardList = 2;
}

// 
message FarmSimpleMessage {
  // 福地主人信息
  sim.PlayerSimpleMessage simpleMessage = 1;
  // 槽位信息
  repeated FarmSlotMessage slotList = 2;
}

// 
message FarmSlotMessage {
  int64 userId = 1;
  // 槽位的索引
  int32 rank = 2;
  // 葫芦品种ID；当为-1时，说明葫芦已经被采集完了，则下面几个属性都没有作用
  int32 gourdId = 3;
  // 槽位里的葫芦的唯一标识 gourdId相等，但identifyId不同，就不是同一个葫芦
  int64 identifyId = 4;
  // 表示单个蜜蜂在没有其他蜜蜂干扰的情况下完成采集需要的时间
  int64 dragTime = 5;
  // 标识存活的截止时间，-1表示无过期时间 专属葫芦是有过期时间的
  int64 deadlineStamp = 6;
  // 自身的采集信息 如果userID为-1表示福地主人没有在采集
  FarmCollectorMessage ownCollectorMessage = 7;
  // 其他玩家的采集信息 如果userID为-1表示没有第三方在采集
  FarmCollectorMessage otherCollectorMessage = 8;
}

// 
message FarmTrainMessage {
  // 槽位的情况
  repeated FarmSlotMessage slotList = 1;
  // 蜜蜂派遣情况 key:此次采集的葫芦的唯一标识
  map<int64,FarmDispatchMessage> dispatchMap = 2;
  // 蜜蜂统计信息
  FarmBeeMetricMessage beeMetric = 3;
  // 待领取的奖励
  repeated int32 rewardGourdList = 4;
  // 仇恨值集合
  map<int64,int32> hatredMap = 5;
  // 当日剩余的采集次数
  int32 remainFetch = 6;
  // 总采集次数
  int32 totalColCnt = 7;
  // 剩下的广告刷新的次数
  int64 remainAdsRefreshCnt = 8;
  // 下次探寻的冷却时间
  int64 nextColdRefreshStamp = 9;
  // 基金
  repeated FarmFundMessage fundList = 10;
}

// 
message FundRewardResponse {
  // 福地基金列表
  repeated FarmFundMessage fundList = 1;
  // 奖励列表
  repeated double rewardList = 2;
}

