import StorageMgr from "db://assets/platform/src/StorageHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
// 持久化数据
export function persistent(target: any, propertyName: string) {
  let t = target;
  let realValue = target[propertyName];
  // 重写原来的get，通过Storage里面拿值
  const getter = function () {
    // let str = localStorage.getItem(propertyName);
    // log.log(`***************getter1 ${realValue}****************`, target);
    if (realValue) {
      return realValue;
    }

    let str = StorageMgr.loadStr(propertyName);
    if (!str) {
      return realValue;
    }
    let storageValue = JSON.parse(str);
    // log.log(`***************getter2 ${storageValue}****************`, target);
    return storageValue;
  };

  // 重写set，使用 Storage API 来存储
  const setter = function (newVal: any) {
    realValue = newVal;
    StorageMgr.saveItem(propertyName, JSON.stringify(newVal));
    // localStorage.setItem(propertyName, JSON.stringify(newVal));
    // log.log(`***************setter ${realValue}****************`, target);
  };
  return {
    set: setter,
    get: getter,
  } as any;
}
export function persistentWith<T>(defaultValue: T) {
  return function persistent(target: any, propertyName: string) {
    let realValue = target[propertyName];
    // 重写原来的get，通过Storage里面拿值
    const getter = function () {
      let str = StorageMgr.loadStr(propertyName);
      // let str = localStorage.getItem(propertyName);
      // log.log(`***************getter1 ${defaultValue}****************`, target);
      if (!str) {
        return defaultValue;
      }

      let storageValue = JSON.parse(str);
      if (!storageValue) {
        return realValue;
      }
      // log.log(`***************getter2 ${storageValue}****************`, target);
      return storageValue;
    };

    // 重写set，使用 Storage API 来存储
    const setter = function (newVal: any) {
      realValue = newVal;
      StorageMgr.saveItem(propertyName, JSON.stringify(newVal));
      // localStorage.setItem(propertyName, JSON.stringify(newVal));
      // log.log(`***************setter ${realValue}****************`, target);
    };
    return {
      set: setter,
      get: getter,
    } as any;
  };
}

export function persistentDevWith<T>(defaultValue: T) {
  return function persistent(target: any, propertyName: string) {
    let realValue = target[propertyName];
    // 重写原来的get，通过Storage里面拿值
    const getter = function () {
      // let str = StorageMgr.loadStr(propertyName);
      let str = localStorage.getItem(propertyName);
      // log.log(`***************getter1 ${defaultValue}****************`, target);
      if (!str) {
        return defaultValue;
      }

      let storageValue = JSON.parse(str);
      if (!storageValue) {
        return realValue;
      }
      // log.log(`***************getter2 ${storageValue}****************`, target);
      return storageValue;
    };

    // 重写set，使用 Storage API 来存储
    const setter = function (newVal: any) {
      realValue = newVal;
      // StorageMgr.saveItem(propertyName, JSON.stringify(newVal));
      localStorage.setItem(propertyName, JSON.stringify(newVal));
      // log.log(`***************setter ${realValue}****************`, target);
    };
    return {
      set: setter,
      get: getter,
    } as any;
  };
}
