{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ca911bbd-03b4-4fa9-a0b8-4813627e5e9a", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "ca911bbd-03b4-4fa9-a0b8-4813627e5e9a@6c48a", "displayName": "bg_xuanzhong<PERSON>obian", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ca911bbd-03b4-4fa9-a0b8-4813627e5e9a", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ca911bbd-03b4-4fa9-a0b8-4813627e5e9a@f9941", "displayName": "bg_xuanzhong<PERSON>obian", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 123, "height": 216, "rawWidth": 123, "rawHeight": 216, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-61.5, -108, 0, 61.5, -108, 0, -61.5, 108, 0, 61.5, 108, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 216, 123, 216, 0, 0, 123, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-61.5, -108, 0], "maxPos": [61.5, 108, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ca911bbd-03b4-4fa9-a0b8-4813627e5e9a@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ca911bbd-03b4-4fa9-a0b8-4813627e5e9a@6c48a"}}