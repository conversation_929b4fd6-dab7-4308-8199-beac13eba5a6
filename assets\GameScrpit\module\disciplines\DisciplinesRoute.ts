import { UIDisciplines } from "../../game/ui/ui_fund/UIDisciplines";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
export enum DisciplinesRouteItem {
  UIDisciplines = "UIDisciplines",
}
export class DisciplinesRoute {
  rotueTables: Recording[] = [
    {
      node: UIDisciplines,
      uiName: DisciplinesRouteItem.UIDisciplines,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
