import { math } from "cc";

export class VecUtil {
  /**
   * 计算向量与 X 轴的夹角（单位：度）
   * @param vec 向量
   * @returns 向量与 X 轴的夹角（单位：度）
   */
  public static calculateAngleWithXAxis(vec: math.Vec2): number {
    // 使用 atan2 计算弧度值
    const angleRad = Math.atan2(vec.y, vec.x);

    // 将弧度转换为度
    const angleDeg = angleRad * (180 / Math.PI);

    // 如果需要角度范围在 [0, 360]，可以进行调整
    const normalizedAngle = (angleDeg + 360) % 360;

    return normalizedAngle;
  }
}
