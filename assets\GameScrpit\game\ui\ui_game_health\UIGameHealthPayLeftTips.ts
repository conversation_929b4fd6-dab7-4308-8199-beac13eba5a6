import { _decorator, Component, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

/**
 * i<PERSON>_huang
 * Wed Mar 05 2025 16:56:21 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_game_health/UIGameHealthPayLeftTips.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIGameHealthPayLeftTips")
export class UIGameHealthPayLeftTips extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_GAME_HEALTH}?prefab/ui/UIGameHealthPayLeftTips`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    // do something
  }
}
