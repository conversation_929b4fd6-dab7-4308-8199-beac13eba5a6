import { IConfigShop } from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
/**
 * 本地配置文件数据结构
 * 数据基础处理，过滤等，按id取
 */
export class ShengDianConfig {
  private _shopList: IConfigShop[] = [];
  public getShengDianShopList() {
    if (this._shopList.length == 0) {
      Object.values(JsonMgr.instance.jsonList.c_shop).forEach((val: IConfigShop) => {
        if (val.type == 12) {
          this._shopList.push(val);
        }
      });
    }
    return this._shopList;
  }
}

export const ShengDianAudioName = {
  Effect: {
    点击点赞按钮: 1122,
    点击名就宫: 1123,
    点击无望宫: 1124,
    点击沐晨宫: 1125,
    点击神迹: 1126,
    点击荣耀阁: 1127,
    点击逆袭之路: 1129,
  },
  Sound: {
    圣殿背景音乐: 1121,
  },
};
