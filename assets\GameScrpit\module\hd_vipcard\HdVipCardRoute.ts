import { UICardMain } from "../../game/ui/ui_month/UICardMain";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum HdVipCardRouteItem {
  UICardMain = "UICardMain",
}
export class HdVipCardRoute {


  rotueTables: Recording[] = [
    {
      node: UICardMain,
      uiName: HdVipCardRouteItem.UICardMain,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
