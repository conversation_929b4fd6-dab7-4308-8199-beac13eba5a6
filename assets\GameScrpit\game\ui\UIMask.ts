import { _decorator } from "cc";
import { UINode } from "../../lib/ui/UINode";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { UIMgr, UIType } from "../../lib/ui/UIMgr";
import { UITransform } from "cc";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";

const { ccclass, property } = _decorator;

@ccclass("UIMask")
export class UIMask extends UINode {
  private anyOtherClose: boolean = true;

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI];
  }

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UIMask`;
  }

  private on_click_btn_back() {
    // 关闭音效 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(504);
    this.anyOtherClose && UIMgr.instance.back();
  }

  public onShow(): void {
    const pageInfo = UIMgr.instance.getLastPageInfo();
    if (pageInfo && pageInfo.uiType == UIType.DIALOG) {
      if (pageInfo.uiNode && pageInfo.uiNode.node) {
        if (pageInfo.args && pageInfo.args.anyOtherClose == false) {
          this.anyOtherClose = false;
        } else {
          this.anyOtherClose = true;

          // 显示点击任意位置关闭
          const hintNode = this.node.getChildByName("hint");
          if (!pageInfo.uiNode?.node || !pageInfo.uiNode?.node.components) {
            return;
          }

          const pageTransform = pageInfo.uiNode.node.getComponent(UITransform);
          const scale = pageInfo.uiNode.node.scale;
          const pos = pageInfo.uiNode.node.position;

          // 缩放
          const height = pageTransform.height * scale.y;

          // 锚点以下有多少
          const anchorDown = height * pageTransform.anchorY;

          // 加上节点位置
          const heightDown = -pos.y + anchorDown;

          hintNode.setPosition(0, -heightDown - 30);
        }
      }
    }
  }
}
