import { _decorator, EventTouch, Node } from "cc";
import CenterHttpApi from "../../GameScrpit/game/httpNet/CenterHttpApi";
import { instantiate } from "cc";
import { Label } from "cc";
import { SceneLogin } from "../../script_game/scene_login/SceneLogin";
import { Sleep } from "../../GameScrpit/game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";

const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

class FuWuQi {
  serverGroup: string;
  serverId: number;
  serverName: string;
  status: string;
  websocket: string;
}

@ccclass("PageServer")
export class PageServer extends BaseCtrl {
  playShowAni: boolean = true;

  serverInfo: Array<{ group: string; serverList: Array<FuWuQi> }> = [];

  private _content_left: Node = null;
  private _content_right: Node = null;
  private _btn_group: Node;
  private _btn_server: Node;

  private _select_group_idx = -1;

  protected start(): void {
    super.start();

    this._content_left = this.getNode("content_left");
    this._content_right = this.getNode("content_right");
    this._btn_group = this.getNode("btn_group");
    this._btn_server = this.getNode("btn_server");

    this._btn_group.active = false;
    this._btn_server.active = false;
    CenterHttpApi.serverList().then((res: any) => {
      if (res.code == 200) {
        this.serverInfo = res.data;
        this.loadArea();
      }
    });
  }

  private async loadArea() {
    for (let i = 0; i < this.serverInfo.length; i++) {
      if (this.isValid == false) return;

      await Sleep(0.01);

      let node = instantiate(this._btn_group);
      this._content_left.addChild(node);
      node.active = true;
      node["index"] = i;

      let info = this.serverInfo[i];
      node.getChildByPath("bg_active/lbl_group_name").getComponent(Label).string = info.group;
      node.getChildByPath("bg_unactive/lbl_group_name").getComponent(Label).string = info.group;
    }

    this.setPich(0);
  }

  private setPich(groupIdx: number) {
    this._select_group_idx = groupIdx;

    for (let i = 0; i < this._content_left.children.length; i++) {
      const node = this._content_left.children[i];
      node.getChildByPath("bg_active").active = groupIdx == i;
      node.getChildByPath("bg_unactive").active = groupIdx != i;
    }

    // 设置区域服
    const serverList = this.serverInfo[groupIdx].serverList;
    this._content_right.children.forEach((node) => {
      node.active = false;
    });
    for (let i = 0; i < serverList.length; i++) {
      let nodeServer = this._content_right.children[i];
      if (!nodeServer) {
        nodeServer = instantiate(this._btn_server);
        this._content_right.addChild(nodeServer);
      }
      nodeServer.active = true;

      let server = serverList[i];
      nodeServer.getChildByPath("lbl_name").getComponent(Label).string = server.serverName;
      nodeServer.getChildByName("bg_1").active = server.status == "1";
      nodeServer.getChildByName("bg_2").active = server.status == "2";
      nodeServer.getChildByName("bg_3").active = server.status == "3";
      nodeServer.getChildByName("bg_4").active = server.status == "9";
    }
  }

  private on_click_btn_group(event: EventTouch) {
    const targetNode: Node = event.target;
    this.setPich(targetNode.getSiblingIndex());
  }

  private on_click_btn_server(event: EventTouch) {
    const targetNode: Node = event.target;
    SceneLogin.routeMgr.back(this.serverInfo[this._select_group_idx].serverList[targetNode.getSiblingIndex()]);
  }

  on_click_btn_close() {
    SceneLogin.routeMgr.back();
  }
}
