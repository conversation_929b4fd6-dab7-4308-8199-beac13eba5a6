import { _decorator, Node, sp, tween, v3 } from "cc";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { roleState, RunRole } from "./RunRole";
import { tweenTagEnum } from "../../../GameDefine";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("runRoleSwk")
export class runRoleSwk extends RunRole {
  protected roleStateAttack() {
    let db1 = JsonMgr.instance.jsonList.c_leaderSkin[this._skinId];
    let musicId = db1.music;
    AudioMgr.instance.playEffect(musicId);

    this._state = roleState.ATTACK;
    let db = JsonMgr.instance.jsonList.c_spineShow[this._roleSpineId];
    this._renderSkt.getComponent(sp.Skeleton).timeScale = db.gameTimeScale[1];
    this._renderSkt.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
      //清空监听
      if ("run2" == trackEntry.animation.name) {
        this._renderSkt.getComponent(sp.Skeleton).setAnimation(0, "run3", true);
        this._renderSkt.getComponent(sp.Skeleton).setCompleteListener(null);
      }
    });
    this._renderSkt.getComponent(sp.Skeleton).setAnimation(0, "run2", false);
  }

  protected roleStateIn() {
    this._state = roleState.IN;
    let db = JsonMgr.instance.jsonList.c_spineShow[this._roleSpineId];
    this._renderSkt.timeScale = db.gameTimeScale[0];
    this._renderSkt.setAnimation(0, "run1", true);
    tween(this as Node)
      .tag(tweenTagEnum.UILevelGame_Tag)
      .to(1, { position: v3(0, 0, 0) })
      .start();
  }
  protected roleStateIdle() {
    if (this._state == roleState.IDLE || this._state == roleState.NULL) {
      return;
    }
    this._state = roleState.IDLE;
    let db = JsonMgr.instance.jsonList.c_spineShow[this._roleSpineId];
    this._renderSkt.timeScale = db.gameTimeScale[0];
    this._renderSkt.setAnimation(0, "run1", true);
  }

  /**
   * 攻击
   */
  public attack() {}
}
