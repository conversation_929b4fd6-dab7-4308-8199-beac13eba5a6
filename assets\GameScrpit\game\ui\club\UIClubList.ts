import { _decorator, EditBox } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ClubListAdapter } from "./adapter/ClubListViewHolder";
import { ClubRouteItem } from "../../../module/club/ClubRoute";
import { ClubModule } from "../../../module/club/ClubModule";
import { ClubMessage } from "../../net/protocol/Club";
import MsgMgr from "../../../lib/event/MsgMgr";
import { ClubEvent } from "../../../module/club/ClubConstant";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Wed Aug 07 2024 09:32:43 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubList.ts
 *
 */

@ccclass("UIClubList")
export class UIClubList extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubList`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }

  private _adapter: ClubListAdapter;
  private refreshLeftTimes() {
    let leftTime = (ClubModule.data?.clubFormMessage?.joinColdStamp ?? 0) - new Date().getTime();
    log.log(`++++++++++++${leftTime}++++++++++++`);
    if (leftTime < 0) {
      this.getNode("layout_left_times").active = false;
    } else {
      this.getNode("layout_left_times").active = true;
      FmUtils.setCd(this.getNode("lbl_cd"), ClubModule.data?.clubFormMessage?.joinColdStamp, false, () => {
        this.getNode("layout_left_times").active = false;
      });
    }
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this._adapter = new ClubListAdapter(this["club_list_viewholder"]);
    this["club_list"].getComponent(AdapterView).setAdapter(this._adapter);
    ClubModule.api.listClub((data: ClubMessage[]) => {
      //
      log.log(data);
      this._adapter.setData(data);
    });
    this.refreshLeftTimes();
    MsgMgr.on(ClubEvent.CLUB_APPLY_STATE_CHANGE, this.onApplyStateChange, this);
    this.getNode("search_box").on(EditBox.EventType.EDITING_RETURN, () => {
      log.log("on_click_search_btn"); //
      let key = this.getNode("search_box").getComponent(EditBox).string;
      if (key == "" || !key) {
        ClubModule.api.listClub((data: ClubMessage[]) => {
          this._adapter.setData(data);
        });
      } else {
        ClubModule.api.searchByKey(key, (data: ClubMessage[]) => {
          this._adapter.setData(data);
        });
      }
    });
  }
  protected onEvtHide(): void {
    super.onEvtHide();
    MsgMgr.off(ClubEvent.CLUB_APPLY_STATE_CHANGE, this.onApplyStateChange, this);
  }

  private onApplyStateChange() {
    this._adapter.notifyDataSetChanged();
  }
  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_create_club() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    //创建联盟
    if (this["layout_left_times"].active) {
      TipMgr.showTip("冷却时间未到，不可以加入或创建新的战盟");
      return;
    }
    UIMgr.instance.showDialog(ClubRouteItem.UIClubCreate);
  }
  private on_click_btn_search() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let key = this.getNode("search_box").getComponent(EditBox).string;
    if (key == "" || !key) {
      ClubModule.api.listClub((data: ClubMessage[]) => {
        this._adapter.setData(data);
      });
    } else {
      ClubModule.api.searchByKey(key, (data: ClubMessage[]) => {
        this._adapter.setData(data);
      });
    }
  }
}
