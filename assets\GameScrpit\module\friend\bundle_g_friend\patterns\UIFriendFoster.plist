<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>bg_tianmingyinguodi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{361,56}</string>
                <key>spriteSourceSize</key>
                <string>{361,56}</string>
                <key>textureRect</key>
                <string>{{974,197},{361,56}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_xypy_dibu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{730,252}</string>
                <key>spriteSourceSize</key>
                <string>{730,252}</string>
                <key>textureRect</key>
                <string>{{1,1},{730,252}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_xypy_shuzidi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{97,29}</string>
                <key>spriteSourceSize</key>
                <string>{97,29}</string>
                <key>textureRect</key>
                <string>{{1288,1},{97,29}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_xypy_tubiaodi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{162,136}</string>
                <key>spriteSourceSize</key>
                <string>{162,136}</string>
                <key>textureRect</key>
                <string>{{1124,1},{162,136}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_xypy_zuobiananniudi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{101,389}</string>
                <key>spriteSourceSize</key>
                <string>{101,389}</string>
                <key>textureRect</key>
                <string>{{733,1},{101,389}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_jineng.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{91,92}</string>
                <key>spriteSourceSize</key>
                <string>{91,92}</string>
                <key>textureRect</key>
                <string>{{881,104},{91,92}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_meiming.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{91,92}</string>
                <key>spriteSourceSize</key>
                <string>{91,92}</string>
                <key>textureRect</key>
                <string>{{974,104},{91,92}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_zengsong.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{146,146}</string>
                <key>spriteSourceSize</key>
                <string>{146,146}</string>
                <key>textureRect</key>
                <string>{{733,104},{146,146}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_yuanfen.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{41,35}</string>
                <key>spriteSourceSize</key>
                <string>{41,35}</string>
                <key>textureRect</key>
                <string>{{1288,100},{41,35}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UIFriendFoster.png</string>
            <key>size</key>
            <string>{1336,254}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:a6f78c9b9a61f7838112f77704696b07:51c0cb64d1e50243fe7e512afa9df86e:06767d36a6c0bc71c555184fb645c73b$</string>
            <key>textureFileName</key>
            <string>UIFriendFoster.png</string>
        </dict>
    </dict>
</plist>
