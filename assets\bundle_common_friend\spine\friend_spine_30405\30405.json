{"skeleton": {"hash": "7up+IqZDYektBsBqRdxk5Zoy2aE", "spine": "3.8.75", "x": -313.24, "y": -506.76, "width": 617.99, "height": 1181.92, "images": "./images/", "audio": "D:/仙友spine"}, "bones": [{"name": "root", "scaleX": 0.82, "scaleY": 0.82}, {"name": "bone", "parent": "root", "length": 441.17, "rotation": 0.28, "x": -141.8, "y": -775.51}, {"name": "图层 12", "parent": "bone", "length": 225.43, "rotation": 69.04, "x": 191.79, "y": 494.03}, {"name": "图层 14", "parent": "图层 12", "length": 281.08, "rotation": 46.07, "x": 353.4, "y": 85.86}, {"name": "图层 9", "parent": "图层 14", "length": 227.39, "rotation": -176.33, "x": 106.76, "y": -70.63}, {"name": "图层 4", "parent": "图层 9", "length": 184.05, "rotation": 150.95, "x": 228.34, "y": 9.3}, {"name": "图层 21", "parent": "图层 14", "length": 218.49, "rotation": 156.22, "x": 143.73, "y": 133.2}, {"name": "图层 2", "parent": "图层 21", "length": 129.4, "rotation": -29.45, "x": 225.71, "y": -5.11}, {"name": "图层 5", "parent": "图层 21", "length": 106.93, "rotation": 1.68, "x": 111.44, "y": -9.27}, {"name": "图层 6", "parent": "图层 14", "length": 197.1, "rotation": -14.99, "x": 188.56, "y": -18.46}, {"name": "图层 54", "parent": "图层 6", "length": 76.99, "rotation": -71.3, "x": 264.16, "y": 68.71}, {"name": "图层 24", "parent": "图层 54", "rotation": 71.3, "x": -47.05, "y": -11.22}, {"name": "图层 25", "parent": "图层 54", "rotation": 71.3, "x": 31.18, "y": 56.17}, {"name": "图层 26", "parent": "图层 54", "rotation": 71.3, "x": 127.65, "y": -35.43}, {"name": "图层 22", "parent": "图层 6", "length": 55.7, "rotation": 162, "x": 26.76, "y": 57.46}, {"name": "图层 23", "parent": "图层 14", "length": 43.1, "rotation": 69.51, "x": 113.9, "y": 196.26}, {"name": "图层 13", "parent": "图层 14", "x": -70.01, "y": -38.19}, {"name": "图层 11", "parent": "图层 12", "length": 144.01, "rotation": -57.57, "x": 155.08, "y": 57.86}, {"name": "图层 19", "parent": "图层 11", "length": 52.43, "rotation": -124.04, "x": -17.13, "y": -20.68}, {"name": "图层 20", "parent": "图层 11", "length": 86.89, "rotation": -96.68, "x": 123.19, "y": -38.45}, {"name": "图层 58", "parent": "图层 12", "length": 86.44, "rotation": -105.34, "x": -20.75, "y": -34.11}, {"name": "图层 10", "parent": "图层 58", "length": 333.48, "rotation": -26.01, "x": -27.25, "y": -12.72}, {"name": "图层 27", "parent": "图层 58", "length": 110.72, "rotation": -30.96, "x": 104.43, "y": 36.44}, {"name": "图层 28", "parent": "图层 12", "length": 79.39, "rotation": -175.92, "x": -19.31, "y": 9.93}, {"name": "bone2", "parent": "root", "length": 150.71, "rotation": -92.05, "x": -482.53, "y": 786.1}, {"name": "图层 15", "parent": "图层 6", "length": 54.21, "rotation": 56.21, "x": 156.31, "y": 174.9}, {"name": "图层 29", "parent": "图层 6", "length": 73.76, "rotation": -79.76, "x": 180.18, "y": -132.09}, {"name": "bone3", "parent": "root", "length": 157.38, "rotation": -90.73, "x": -461.46, "y": 101.27}, {"name": "图层 33", "parent": "bone3", "x": 124.28, "y": 202.81}, {"name": "图层 31", "parent": "bone3", "x": 164.55, "y": 170.8}, {"name": "图层 35", "parent": "bone3", "x": 146.61, "y": 261.84}, {"name": "图层 32", "parent": "bone3", "x": 138.74, "y": 303.71}, {"name": "图层 34", "parent": "bone3", "x": 214.09, "y": 235.42}, {"name": "图层 36", "parent": "bone3", "x": 1.44, "y": 127.82}, {"name": "图层 30", "parent": "bone3", "x": -209.15, "y": 796.57}, {"name": "bone4", "parent": "root", "length": 122.77, "rotation": -88.53, "x": 436.3, "y": 711.57}, {"name": "图层 37", "parent": "bone4", "x": -32.78, "y": -746.29}, {"name": "图层 38", "parent": "bone4", "x": -125.27, "y": -670.47}, {"name": "图层 39", "parent": "bone4", "x": -69.4, "y": -210.19}, {"name": "图层 40", "parent": "bone4", "x": 59.26, "y": -185.15}, {"name": "图层 41", "parent": "bone4", "x": 240.92, "y": -139.44}, {"name": "图层 42", "parent": "图层 6", "length": 33.86, "rotation": 153.41, "x": 180.83, "y": 130.71}, {"name": "图层 44", "parent": "图层 14", "x": 61.69, "y": -12.25}, {"name": "图层 45", "parent": "图层 12", "x": 96.23, "y": -70.36}, {"name": "图层 3", "parent": "图层 2", "length": 129.4, "x": 129.4}, {"name": "图层 7", "parent": "图层 3", "length": 129.4, "x": 129.4}, {"name": "图层 8", "parent": "图层 7", "length": 129.4, "x": 129.4}, {"name": "图层 46", "parent": "图层 8", "length": 83.01, "rotation": -2.48, "x": 133.36, "y": -2.69}, {"name": "图层 43", "parent": "图层 15", "length": 54.21, "x": 54.21}, {"name": "图层 47", "parent": "图层 43", "length": 54.21, "x": 54.21}, {"name": "图层 48", "parent": "图层 47", "length": 54.21, "x": 54.21}, {"name": "图层 49", "parent": "图层 29", "length": 73.76, "x": 73.76}, {"name": "图层 51", "parent": "图层 49", "length": 78.83, "rotation": -30.1, "x": 77.53, "y": -2.85}, {"name": "图层 50", "parent": "图层 51", "length": 74.1, "rotation": 115.32, "x": 89.89, "y": 12.52, "scaleX": 2, "scaleY": 1.04}, {"name": "图层 53", "parent": "图层 50", "length": 55.86, "rotation": 1, "x": 79.78, "y": -0.77}, {"name": "图层 52", "parent": "bone3", "x": 183.56, "y": 205.04}, {"name": "图层 55", "parent": "bone3", "x": 152.78, "y": 123.18}, {"name": "图层 57", "parent": "图层 22", "length": 28.29, "rotation": 16.25, "x": 2.33, "y": 5.71}, {"name": "图层 59", "parent": "图层 22", "length": 58.57, "rotation": -33.49, "x": 59.58, "y": -0.59}, {"name": "图层 56", "parent": "图层 59", "length": 72.04, "rotation": -9.98, "x": 61.28, "y": -1.39}, {"name": "图层 61", "parent": "图层 56", "length": 63.65, "rotation": 7.14, "x": 77.89, "y": 1.83}, {"name": "图层 60", "parent": "图层 61", "length": 54.96, "rotation": 11.54, "x": 68.04, "y": -0.48}, {"name": "图层 62", "parent": "图层 19", "length": 52.43, "x": 52.43}, {"name": "图层 63", "parent": "图层 62", "length": 52.43, "x": 52.43}, {"name": "图层 64", "parent": "图层 63", "length": 52.43, "x": 52.43}, {"name": "图层 65", "parent": "图层 20", "length": 86.89, "x": 86.89}, {"name": "图层 67", "parent": "图层 65", "length": 87.17, "rotation": 5.37, "x": 91.21, "y": -0.3}, {"name": "图层 66", "parent": "图层 67", "length": 77.44, "rotation": 16.87, "x": 93.33, "y": 2.31}, {"name": "图层 69", "parent": "图层 66", "length": 88.24, "rotation": 30.22, "x": 81.74, "y": 0.95}, {"name": "图层 68", "parent": "图层 69", "length": 52.86, "rotation": -17.72, "x": 94.82, "y": 0.17}, {"name": "图层 71", "parent": "图层 23", "length": 68.58, "rotation": -43.23, "x": 45.42, "y": -1.81}, {"name": "图层 70", "parent": "图层 71", "length": 70.69, "rotation": -23.89, "x": 70.14, "y": 0.71}, {"name": "图层 73", "parent": "图层 70", "length": 87.35, "rotation": -11.42, "x": 73.86, "y": -0.25}, {"name": "图层 72", "parent": "图层 73", "length": 72.17, "rotation": 10.8, "x": 89.82, "y": 1.01}, {"name": "图层 75", "parent": "图层 72", "length": 58.88, "rotation": 25.32, "x": 74.62, "y": 2.03}, {"name": "图层 76", "parent": "图层 6", "length": 46.46, "rotation": -43.51, "x": 198.88, "y": -131.41}, {"name": "图层 74", "parent": "图层 76", "length": 29.46, "rotation": 17.28, "x": 50.22, "y": 0.43}, {"name": "图层 78", "parent": "图层 74", "length": 25.45, "rotation": 31.25, "x": 34.28, "y": 0.05}, {"name": "图层 79", "parent": "图层 6", "length": 78.18, "rotation": 144.42, "x": 131.78, "y": 69.49}, {"name": "图层 77", "parent": "图层 79", "length": 72.97, "rotation": -23.63, "x": 85.52, "y": 0.53}, {"name": "图层 81", "parent": "图层 77", "length": 82.87, "rotation": -20.67, "x": 92.34, "y": -9.93}, {"name": "图层 80", "parent": "图层 81", "length": 69.97, "rotation": -11.83, "x": 89.17, "y": 3.28}, {"name": "图层 83", "parent": "图层 80", "length": 36.41, "rotation": -38.22, "x": 85.31, "y": -4.48}, {"name": "图层 84", "parent": "图层 6", "length": 86.73, "rotation": 134.26, "x": 26.06, "y": 107.68}, {"name": "图层 82", "parent": "图层 84", "length": 99.7, "rotation": -35.17, "x": 97.95, "y": -3.05}, {"name": "图层 86", "parent": "图层 82", "length": 79.31, "rotation": -1.66, "x": 115.01, "y": -2.62}, {"name": "图层 85", "parent": "图层 86", "length": 36.61, "rotation": 38.94, "x": 88.85, "y": -1.4}, {"name": "图层 88", "parent": "图层 6", "length": 63.58, "rotation": 164.84, "x": 90.07, "y": -134.36}, {"name": "图层 87", "parent": "图层 88", "length": 84.59, "rotation": -31.61, "x": 75.9, "y": -1.62}, {"name": "图层 90", "parent": "图层 87", "length": 61.96, "rotation": -21.16, "x": 91.52, "y": -4.07}, {"name": "图层 89", "parent": "图层 90", "length": 56.06, "rotation": -2.53, "x": 67.38, "y": -0.32}, {"name": "图层 92", "parent": "图层 89", "length": 41.62, "rotation": 5.76, "x": 60.51, "y": -0.74}, {"name": "图层 91", "parent": "图层 28", "length": 79.39, "rotation": 13.2, "x": 79.39}, {"name": "图层 93", "parent": "图层 91", "length": 79.39, "rotation": 6, "x": 79.39}, {"name": "图层 94", "parent": "图层 93", "length": 79.39, "rotation": 7.2, "x": 79.39}, {"name": "图层 96", "parent": "图层 9", "length": 77.86, "rotation": -15.71, "x": 106.78, "y": 96.36}, {"name": "图层 95", "parent": "图层 96", "length": 70.33, "rotation": -7.59, "x": 85.18, "y": 0.24}, {"name": "图层 98", "parent": "图层 95", "length": 64.85, "rotation": -5.2, "x": 70.91, "y": -0.7}, {"name": "图层 97", "parent": "图层 98", "length": 68.06, "rotation": -1.65, "x": 72.55, "y": -0.08}, {"name": "图层 100", "parent": "图层 97", "length": 72.34, "rotation": 1.08, "x": 76.71, "y": -1.69}, {"name": "图层 99", "parent": "图层 100", "length": 74.14, "rotation": -3.81, "x": 79.73}, {"name": "图层 102", "parent": "图层 99", "length": 65.19, "rotation": -10.05, "x": 84.53, "y": 4.81}, {"name": "图层 101", "parent": "图层 102", "length": 55.99, "rotation": 10.49, "x": 71.18, "y": 3.17}, {"name": "图层 104", "parent": "图层 45", "length": 15.82, "rotation": -157.07, "x": -3.02, "y": -2.66}, {"name": "图层 103", "parent": "图层 104", "length": 18.29, "rotation": -1.27, "x": 17.07, "y": 0.11}, {"name": "图层 106", "parent": "图层 103", "length": 19.9, "rotation": -9.48, "x": 19.85, "y": 0.13}, {"name": "图层 105", "parent": "图层 4", "length": 77.23, "rotation": 41.39, "x": 192.5, "y": 0.34}, {"name": "图层 108", "parent": "图层 105", "length": 55.06, "rotation": 40.41, "x": 90.2, "y": -1.62}, {"name": "图层 109", "parent": "图层 105", "length": 65.7, "rotation": 84.79, "x": 88.85, "y": 8.73}, {"name": "图层 17", "parent": "图层 14", "x": 42.96, "y": 79.94, "color": "abe323ff"}, {"name": "图层 18", "parent": "图层 14", "x": -106.27, "y": -31.81}, {"name": "图层 110", "parent": "图层 87", "length": 63.91, "rotation": -12.54, "x": 85.35, "y": -11.01}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "d8", "bone": "图层 55", "attachment": "d8"}, {"name": "d7", "bone": "图层 52", "attachment": "d7"}, {"name": "ww", "bone": "图层 110", "attachment": "ww"}, {"name": "d6", "bone": "图层 36", "attachment": "d6"}, {"name": "d5", "bone": "图层 35", "attachment": "d5"}, {"name": "d4", "bone": "图层 34", "attachment": "d4"}, {"name": "d3", "bone": "图层 33", "attachment": "d3"}, {"name": "d2", "bone": "图层 32", "attachment": "d2"}, {"name": "d1", "bone": "图层 31", "attachment": "d1"}, {"name": "c6", "bone": "图层 30", "attachment": "c6"}, {"name": "c5", "bone": "图层 41", "attachment": "c5"}, {"name": "c4", "bone": "图层 40", "attachment": "c4"}, {"name": "c3", "bone": "图层 39", "attachment": "c3"}, {"name": "c2", "bone": "图层 38", "attachment": "c2"}, {"name": "c1", "bone": "图层 37", "attachment": "c1"}, {"name": "b9", "bone": "图层 101", "attachment": "b9"}, {"name": "b8", "bone": "图层 109", "attachment": "b8"}, {"name": "b7", "bone": "图层 28", "attachment": "b7"}, {"name": "a5", "bone": "图层 60", "attachment": "a5"}, {"name": "b6", "bone": "图层 58", "attachment": "b6"}, {"name": "b5", "bone": "图层 18", "attachment": "b5"}, {"name": "b4", "bone": "图层 44", "attachment": "b4"}, {"name": "b3", "bone": "图层 42", "attachment": "b3"}, {"name": "b2", "bone": "图层 78", "attachment": "b2"}, {"name": "b1", "bone": "图层 68", "attachment": "b1"}, {"name": "a9", "bone": "图层 106", "attachment": "a9"}, {"name": "a8", "bone": "图层 21", "attachment": "a8"}, {"name": "a7", "bone": "图层 75", "attachment": "a7"}, {"name": "a6", "bone": "图层 46", "attachment": "a6"}, {"name": "a4", "bone": "图层 15", "attachment": "a4"}, {"name": "a3", "bone": "图层 57", "attachment": "a3"}, {"name": "a2", "bone": "图层 92", "attachment": "a2"}, {"name": "a1", "bone": "图层 54", "attachment": "a1"}], "transform": [{"name": "图层 17", "bones": ["图层 13"], "target": "图层 17", "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"a3": {"a3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [25.75, 2.75, 23.8, -10.1, 2.05, -6.79, 4, 6.06], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 13, "height": 22}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [370, -613.45, -382, -613.45, -382, 821.81, 370, 821.81], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 270, "height": 453}}, "b1": {"b1": {"type": "mesh", "uvs": [0.65922, 0.01865, 0.66146, 0.04812, 0.66458, 0.08934, 0.67107, 0.17491, 0.67559, 0.23459, 0.68047, 0.29892, 0.6854, 0.364, 0.68958, 0.41911, 0.69345, 0.47019, 0.69718, 0.51939, 0.69999, 0.55644, 0.70303, 0.59665, 0.70477, 0.61963, 0.70692, 0.6479, 0.73355, 0.67317, 0.76301, 0.70112, 0.79086, 0.72755, 0.85154, 0.73948, 0.91295, 0.75155, 0.95125, 0.75907, 0.99824, 0.76831, 0.9982, 0.82581, 0.99815, 0.90845, 0.99809, 1, 0.92797, 0.99911, 0.91269, 0.98396, 0.88158, 0.95314, 0.84012, 0.91207, 0.80383, 0.87612, 0.76777, 0.8404, 0.73073, 0.8037, 0.69628, 0.76957, 0.66018, 0.7338, 0.63027, 0.70417, 0.59715, 0.67136, 0.55959, 0.63416, 0.52171, 0.59663, 0.50831, 0.58335, 0.51081, 0.56557, 0.51632, 0.52636, 0.52274, 0.48072, 0.52994, 0.42949, 0.53842, 0.36911, 0.54583, 0.31639, 0.54639, 0.3032, 0.38034, 0.40545, 0.24154, 0.49093, 0.08593, 0.58676, 0.04777, 0.53127, 0, 0.46182, 0, 0.45832, 0.01481, 0.40128, 0.03778, 0.3128, 0.11437, 0.21674, 0.1825, 0.13128, 0.20301, 0.10556, 0.22223, 0.08146, 0.57492, 0.01571, 0.65783, 0.00026, 0.57067, 0.09853, 0.55488, 0.18943, 0.25661, 0.19536, 0.38705, 0.16177, 0.57233, 0.0662, 0.21673, 0.27441, 0.37957, 0.22039, 0.14602, 0.36938, 0.38202, 0.27939, 0.55051, 0.24796, 0.0919, 0.44685, 0.27379, 0.38964, 0.06484, 0.50525, 0.22794, 0.46055, 0.07762, 0.53981, 0.16781, 0.51001, 0.31677, 0.11754, 0.44044, 0.08943, 0.57387, 0.03622, 0.59492, 0.0392, 0.62875, 0.03169, 0.59018, 0.07085, 0.62198, 0.06227, 0.59086, 0.10841, 0.61657, 0.10573, 0.5849, 0.18985, 0.60734, 0.19628, 0.58538, 0.25156, 0.61069, 0.25118, 0.58608, 0.30973, 0.61893, 0.30926, 0.58848, 0.37617, 0.632, 0.37904, 0.59729, 0.42876, 0.64289, 0.42958, 0.60817, 0.47888, 0.65377, 0.47806, 0.62254, 0.52343, 0.66374, 0.52444, 0.63521, 0.56213, 0.66944, 0.56313, 0.65169, 0.5968, 0.66944, 0.5958, 0.66894, 0.6257, 0.68705, 0.62251, 0.68585, 0.64836, 0.69631, 0.64549, 0.71362, 0.67996, 0.74221, 0.71056, 0.76233, 0.73386, 0.7877, 0.7728, 0.82956, 0.75238, 0.84604, 0.79777, 0.88831, 0.76546, 0.88124, 0.81752, 0.92754, 0.77802, 0.92603, 0.84984, 0.96427, 0.79478, 0.9565, 0.88351, 0.98364, 0.83657, 0.98055, 0.93632], "triangles": [114, 18, 19, 116, 19, 20, 114, 19, 116, 21, 116, 20, 118, 116, 21, 116, 115, 113, 116, 113, 114, 115, 116, 118, 117, 115, 118, 22, 118, 21, 117, 118, 22, 27, 113, 115, 27, 115, 117, 119, 117, 22, 26, 27, 117, 26, 117, 119, 25, 26, 119, 24, 25, 119, 23, 119, 22, 24, 119, 23, 108, 15, 16, 110, 16, 17, 112, 17, 18, 109, 108, 16, 109, 16, 110, 31, 108, 109, 112, 18, 114, 112, 111, 110, 112, 110, 17, 109, 110, 111, 30, 31, 109, 113, 111, 112, 113, 112, 114, 29, 30, 109, 29, 109, 111, 28, 29, 111, 28, 111, 113, 27, 28, 113, 103, 11, 12, 102, 101, 103, 105, 103, 12, 105, 12, 13, 104, 102, 103, 104, 103, 105, 34, 35, 102, 34, 102, 104, 106, 13, 14, 33, 34, 104, 104, 106, 33, 13, 104, 105, 106, 104, 13, 107, 14, 15, 106, 14, 107, 32, 33, 106, 32, 106, 107, 108, 107, 15, 31, 32, 107, 31, 107, 108, 93, 7, 8, 95, 93, 8, 94, 93, 95, 40, 92, 94, 95, 8, 9, 96, 94, 95, 97, 96, 95, 9, 97, 95, 39, 40, 94, 39, 94, 96, 97, 9, 10, 98, 96, 97, 99, 98, 97, 10, 99, 97, 38, 39, 96, 38, 96, 98, 11, 101, 99, 100, 98, 99, 36, 38, 98, 37, 38, 36, 11, 99, 10, 101, 100, 99, 36, 98, 100, 103, 101, 11, 102, 100, 101, 35, 36, 100, 35, 100, 102, 89, 5, 6, 90, 88, 89, 91, 90, 89, 43, 88, 90, 42, 43, 90, 6, 91, 89, 91, 6, 7, 92, 90, 91, 93, 92, 91, 41, 42, 90, 41, 90, 92, 7, 93, 91, 94, 92, 93, 40, 41, 92, 84, 60, 82, 83, 85, 84, 3, 85, 83, 85, 3, 4, 68, 65, 60, 87, 85, 4, 86, 84, 85, 86, 85, 87, 68, 60, 84, 86, 68, 84, 67, 65, 68, 87, 4, 5, 44, 67, 68, 44, 68, 86, 89, 87, 5, 88, 86, 87, 88, 87, 89, 44, 86, 88, 44, 70, 67, 43, 44, 88, 70, 44, 45, 66, 52, 64, 70, 66, 67, 51, 52, 66, 69, 51, 66, 69, 66, 70, 50, 51, 69, 72, 69, 70, 72, 70, 45, 49, 50, 69, 46, 72, 45, 71, 49, 69, 71, 69, 72, 74, 71, 72, 74, 72, 46, 48, 49, 71, 73, 71, 74, 48, 71, 73, 47, 73, 74, 48, 73, 47, 47, 74, 46, 64, 65, 67, 52, 53, 64, 66, 64, 67, 54, 55, 61, 53, 54, 61, 65, 61, 62, 64, 53, 61, 64, 61, 65, 79, 57, 58, 0, 79, 58, 78, 77, 57, 79, 78, 57, 79, 0, 1, 81, 78, 79, 81, 79, 1, 57, 77, 56, 63, 77, 78, 80, 63, 78, 81, 80, 78, 77, 76, 56, 81, 1, 2, 63, 76, 77, 63, 62, 76, 59, 63, 80, 83, 80, 81, 83, 81, 2, 82, 59, 80, 83, 82, 80, 75, 56, 76, 55, 56, 75, 62, 75, 76, 59, 62, 63, 83, 2, 3, 60, 62, 59, 60, 59, 82, 84, 82, 83, 61, 55, 75, 61, 75, 62, 65, 62, 60], "vertices": [1, 17, 174.51, 40.36, 1, 1, 17, 172, 23.15, 1, 3, 17, 168.48, -0.92, 0.94638, 19, -42.54, 40.63, 0.05362, 68, -238.34, 307, 0, 2, 17, 161.19, -50.88, 0.09208, 19, 7.93, 39.19, 0.90792, 2, 19, 43.13, 38.19, 0.99524, 65, -43.76, 38.19, 0.00476, 3, 19, 81.07, 37.11, 0.6485, 65, -5.82, 37.11, 0.35114, 66, -93.11, 46.32, 0.00036, 3, 19, 119.46, 36.01, 0.04313, 65, 32.57, 36.01, 0.89595, 66, -54.99, 41.64, 0.06092, 2, 65, 65.07, 35.09, 0.57584, 66, -22.71, 37.68, 0.42416, 2, 65, 95.2, 34.23, 0.02725, 66, 7.2, 34.01, 0.97275, 1, 66, 36.02, 30.47, 1, 3, 66, 57.72, 27.81, 0.99354, 67, -26.68, 34.74, 0.00646, 68, -76.68, 83.76, 0, 3, 66, 81.27, 24.92, 0.71251, 67, -4.98, 25.14, 0.28749, 68, -62.77, 64.55, 0, 3, 66, 94.73, 23.27, 0.23231, 67, 7.42, 19.65, 0.76769, 68, -54.81, 53.56, 0, 2, 67, 22.67, 12.9, 0.99999, 68, -45.03, 40.05, 1e-05, 2, 67, 41.61, 17.12, 0.99984, 68, -26.54, 34.17, 0.00016, 2, 67, 62.54, 21.79, 0.84364, 68, -6.1, 27.67, 0.15636, 3, 67, 82.34, 26.21, 0.19927, 68, 13.23, 21.52, 0.8006, 69, -84.21, -4.5, 0.00013, 3, 67, 101.58, 48.16, 0.00092, 68, 40.91, 30.81, 0.94926, 69, -60.68, 12.77, 0.04982, 2, 68, 68.92, 40.21, 0.59911, 69, -36.86, 30.25, 0.40089, 2, 68, 86.39, 46.07, 0.24478, 69, -22, 41.15, 0.75522, 2, 68, 107.82, 53.26, 0.04752, 69, -3.77, 54.53, 0.95248, 1, 69, 22.24, 32.83, 1, 1, 69, 59.61, 1.65, 1, 1, 69, 101.02, -32.89, 1, 2, 68, 153.12, -79.04, 0.01049, 69, 79.65, -57.71, 0.98951, 2, 68, 142.31, -75.35, 0.02435, 69, 68.23, -57.48, 0.97565, 2, 68, 120.31, -67.83, 0.11506, 69, 44.98, -57.02, 0.88494, 2, 68, 90.99, -57.82, 0.43878, 69, 14.01, -56.41, 0.56122, 2, 68, 65.32, -49.05, 0.82263, 69, -13.11, -55.87, 0.17737, 2, 68, 39.82, -40.34, 0.98996, 69, -40.06, -55.34, 0.01004, 1, 68, 13.62, -31.4, 1, 2, 67, 84.07, -24.39, 0.38721, 68, -10.75, -23.07, 0.61279, 2, 67, 57.61, -29.71, 0.98738, 68, -36.28, -14.35, 0.01262, 2, 66, 137.39, -19.97, 0.00177, 67, 35.7, -34.11, 0.99823, 3, 66, 115.59, -31.68, 0.21417, 67, 11.43, -38.99, 0.78583, 68, -80.86, 0.87, 0, 3, 66, 90.86, -44.96, 0.82355, 67, -16.09, -44.51, 0.17645, 68, -107.42, 9.95, 0, 4, 65, 162.3, -52.24, 0.00231, 66, 65.91, -58.36, 0.99417, 67, -43.84, -50.09, 0.00351, 68, -134.21, 19.1, 0, 3, 65, 153.95, -57.78, 0.00822, 66, 57.09, -63.09, 0.99178, 68, -143.69, 22.33, 0, 3, 65, 143.62, -55.69, 0.02046, 66, 47, -60.05, 0.97954, 68, -148.33, 31.8, 0, 3, 65, 120.84, -51.09, 0.11796, 66, 24.75, -53.33, 0.88204, 68, -158.56, 52.66, 0, 3, 65, 94.33, -45.73, 0.48431, 66, -1.14, -45.52, 0.51569, 68, -170.46, 76.95, 0, 3, 65, 64.57, -39.71, 0.97333, 66, -30.21, -36.74, 0.02667, 68, -183.82, 104.21, 0, 5, 18, 54.53, 150.75, 0.00016, 62, 2.1, 150.75, 0.00022, 19, 116.39, -32.62, 0.01039, 65, 29.5, -32.62, 0.98924, 68, -199.57, 136.35, 0, 6, 17, 86.96, -120.56, 0.00541, 18, 24.49, 142.17, 0.01806, 62, -27.94, 142.17, 0.01781, 63, -80.37, 142.17, 0.00024, 19, 85.77, -26.43, 0.53924, 65, -1.13, -26.43, 0.41924, 6, 17, 88.8, -113, 0.01698, 18, 17.2, 139.46, 0.04499, 62, -35.23, 139.46, 0.04399, 63, -87.66, 139.46, 0.00101, 19, 78.05, -25.49, 0.75689, 65, -8.84, -25.49, 0.13615, 5, 17, 0.62, -156.18, 0.00086, 18, 102.35, 90.56, 0.09313, 62, 49.92, 90.56, 0.44473, 63, -2.51, 90.56, 0.25237, 19, 131.19, -108.05, 0.20891, 3, 62, 121.09, 49.69, 0.02979, 63, 68.66, 49.69, 0.9595, 19, 175.61, -177.06, 0.01071, 1, 63, 148.45, 3.87, 1, 1, 63, 124.98, -25.02, 1, 2, 62, 148.02, -61.18, 0.00226, 63, 95.59, -61.18, 0.99774, 2, 62, 146.12, -61.96, 0.00284, 63, 93.68, -61.96, 0.99716, 2, 62, 112.41, -68.31, 0.0598, 63, 59.98, -68.31, 0.9402, 4, 18, 112.55, -78.15, 0.03308, 62, 60.12, -78.15, 0.4054, 63, 7.69, -78.15, 0.56152, 68, -414.63, 38.8, 0, 5, 17, -98.36, -22.07, 0.00322, 18, 46.63, -66.53, 0.52121, 62, -5.8, -66.53, 0.43844, 63, -58.23, -66.53, 0.03712, 68, -414.83, 105.74, 0, 4, 17, -56.96, 20.73, 0.35297, 18, -12.01, -56.19, 0.6262, 62, -64.44, -56.19, 0.02082, 68, -415.01, 165.29, 0, 1, 17, -44.51, 33.61, 1, 1, 17, -32.83, 45.69, 1, 1, 17, 136.32, 50.07, 1, 1, 17, 176.08, 51.1, 1, 1, 17, 124.44, 2.72, 1, 4, 17, 106.32, -48.21, 0.10428, 18, -46.3, 117.71, 0.01033, 62, -98.73, 117.71, 0.00304, 19, 11.66, -15.61, 0.88235, 4, 17, -30.76, -23.27, 0.01821, 18, 9.78, -9.85, 0.98013, 62, -42.65, -9.85, 0.00167, 68, -365.55, 152.03, 0, 4, 17, 32.9, -16.3, 0.84826, 18, -31.64, 39.01, 0.09697, 62, -84.07, 39.01, 0.00396, 19, -11.5, -92.24, 0.05081, 1, 17, 129.08, 21.2, 1, 3, 18, 59.92, -9.41, 0.14263, 62, 7.49, -9.41, 0.85737, 68, -356.26, 102.75, 0, 4, 17, 22.46, -49.39, 0.34185, 18, 1.63, 48.88, 0.41068, 62, -50.8, 48.88, 0.05677, 19, 22.59, -98.77, 0.1907, 3, 62, 71.77, -18.74, 0.10042, 63, 19.34, -18.74, 0.89958, 68, -354.09, 37.83, 0, 5, 17, 16.5, -83.65, 0.09536, 18, 33.35, 63.12, 0.36381, 62, -19.08, 63.12, 0.2384, 63, -71.51, 63.12, 0.01033, 19, 57.3, -100.71, 0.2921, 4, 17, 97.31, -81.54, 0.03918, 18, -13.63, 128.9, 0.03159, 62, -66.07, 128.9, 0.01999, 19, 45.81, -20.69, 0.90924, 2, 62, 123.58, -24.81, 0.00258, 63, 71.15, -24.81, 0.99742, 4, 18, 112.61, 40.99, 0.01563, 62, 60.18, 40.99, 0.39581, 63, 7.75, 40.99, 0.52785, 19, 117.51, -156.79, 0.06072, 1, 63, 107.77, -23.45, 1, 3, 62, 106.95, 37.03, 0.03921, 63, 54.51, 37.03, 0.94795, 19, 157.23, -181.81, 0.01284, 1, 63, 124.34, -10.21, 1, 2, 63, 92.12, 22.1, 0.99969, 19, 183.77, -212.35, 0.00031, 1, 17, 6.07, 15.89, 1, 1, 17, 65.99, 20.34, 1, 1, 17, 133.38, 38.34, 1, 1, 17, 142.64, 34.62, 1, 1, 17, 159.01, 35.74, 1, 1, 17, 136.68, 16.82, 1, 1, 17, 152.25, 18.75, 1, 3, 17, 132.49, -4.9, 0.93774, 19, -34.4, 5.34, 0.06226, 68, -261.35, 279.04, 0, 3, 17, 144.56, -5.8, 0.92578, 19, -34.91, 17.44, 0.07422, 68, -252.07, 286.82, 0, 4, 17, 120, -51.3, 0.00443, 18, -51.39, 130.77, 0.00059, 62, -103.83, 130.77, 0.00017, 19, 13.14, -1.67, 0.99481, 1, 19, 17.84, 8.43, 1, 4, 17, 112.82, -86.93, 0.00329, 18, -17.85, 144.77, 0.00378, 62, -70.28, 144.77, 0.00267, 19, 49.36, -4.66, 0.99026, 1, 19, 50.18, 7.13, 1, 6, 17, 106.16, -120.54, 0.00116, 18, 13.73, 158.07, 0.0044, 62, -38.7, 158.07, 0.00427, 63, -91.14, 158.07, 2e-05, 19, 83.52, -7.36, 0.69824, 65, -3.37, -7.36, 0.2919, 3, 19, 84.6, 7.94, 0.63008, 65, -2.29, 7.94, 0.36991, 66, -92.32, 16.96, 1e-05, 2, 65, 35.71, -9.71, 1, 68, -177.62, 145.39, 0, 3, 19, 126.08, 10.39, 0.00074, 65, 39.19, 10.39, 0.97298, 66, -50.79, 15.52, 0.02628, 2, 65, 66.92, -8.34, 1, 68, -157.52, 121.46, 0, 2, 65, 69.29, 12.83, 0.71356, 66, -20.6, 15.12, 0.28644, 2, 66, 5.02, -6.08, 1, 68, -137.38, 99.28, 0, 1, 66, 8.4, 14.95, 1, 3, 65, 123.51, -1.53, 0.00145, 66, 32.04, -4.24, 0.99855, 68, -117.63, 80.75, 0, 1, 66, 36.11, 14.58, 1, 1, 66, 55.53, -2.55, 1, 3, 66, 59.01, 13.07, 0.99603, 67, -29.72, 20.26, 0.00397, 68, -86.6, 72.78, 0, 3, 66, 77.01, 1.32, 0.99354, 67, -15.9, 3.8, 0.00646, 68, -82.95, 51.6, 0, 3, 66, 77.93, 9.58, 0.90346, 67, -12.63, 11.43, 0.09654, 68, -76.27, 56.55, 0, 2, 67, 2.92, 3.14, 1, 68, -67.01, 41.56, 0, 3, 66, 94.89, 14.82, 0.16183, 67, 5.13, 11.52, 0.83817, 68, -60.89, 47.69, 0, 2, 67, 18.4, 4.03, 1, 68, -53.19, 34.54, 0, 3, 66, 108.99, 16.62, 0.00125, 67, 19.14, 9.15, 0.99874, 68, -49.97, 38.59, 0, 1, 67, 40.89, 7.02, 1, 2, 67, 63.03, 10.61, 0.9211, 68, -11.31, 17.76, 0.0789, 2, 67, 79.53, 12.67, 0.34481, 68, 3.99, 11.23, 0.65519, 1, 68, 26.29, -1.76, 1, 3, 67, 103.63, 35.56, 0.00105, 68, 36.33, 18.89, 0.98225, 69, -61.41, 0.02, 0.0167, 2, 68, 57.18, 0.46, 0.99905, 69, -35.94, -11.18, 0.00095, 2, 68, 63.61, 27.12, 0.77638, 69, -37.93, 16.17, 0.22362, 1, 68, 77.29, -0.53, 1, 2, 68, 83.04, 30.71, 0.38243, 69, -20.51, 25.5, 0.61757, 2, 68, 105.16, -5.36, 0.02715, 69, 11.53, -2.12, 0.97285, 2, 68, 102.81, 31.59, 0.06249, 69, -1.95, 32.36, 0.93751, 2, 68, 127.81, -14.45, 0.00784, 69, 35.88, -3.88, 0.99216, 1, 69, 22.75, 23.55, 1, 2, 68, 153.98, -34.66, 0.00315, 69, 66.96, -15.17, 0.99685], "hull": 59, "edges": [46, 48, 86, 88, 98, 100, 112, 114, 114, 116, 118, 120, 108, 122, 122, 124, 126, 118, 124, 126, 104, 106, 106, 108, 106, 128, 128, 130, 130, 120, 104, 132, 132, 134, 88, 136, 136, 120, 134, 136, 100, 102, 102, 104, 102, 138, 138, 140, 140, 88, 98, 142, 142, 144, 88, 90, 144, 90, 94, 96, 96, 98, 96, 146, 146, 148, 90, 92, 92, 94, 148, 92, 108, 110, 110, 112, 110, 150, 150, 152, 114, 154, 154, 126, 152, 154, 154, 156, 156, 158, 0, 116, 158, 0, 126, 160, 160, 162, 0, 2, 162, 2, 118, 164, 164, 166, 2, 4, 166, 4, 120, 168, 168, 170, 4, 6, 170, 6, 136, 172, 172, 174, 6, 8, 174, 8, 88, 176, 176, 178, 8, 10, 178, 10, 84, 86, 84, 180, 180, 182, 10, 12, 182, 12, 82, 84, 82, 184, 184, 186, 12, 14, 186, 14, 80, 82, 80, 188, 188, 190, 14, 16, 190, 16, 78, 80, 78, 192, 192, 194, 16, 18, 194, 18, 74, 76, 76, 78, 76, 196, 196, 198, 18, 20, 198, 20, 72, 74, 72, 200, 200, 202, 20, 22, 202, 22, 70, 72, 70, 204, 204, 206, 22, 24, 24, 26, 206, 24, 68, 70, 68, 208, 208, 210, 210, 26, 66, 68, 66, 212, 26, 28, 212, 28, 64, 66, 64, 214, 28, 30, 30, 32, 214, 30, 62, 64, 62, 216, 216, 32, 60, 62, 60, 218, 218, 220, 32, 34, 220, 34, 58, 60, 58, 222, 222, 224, 34, 36, 224, 36, 56, 58, 56, 226, 226, 228, 36, 38, 38, 40, 228, 38, 54, 56, 54, 230, 230, 232, 232, 40, 52, 54, 52, 234, 234, 236, 40, 42, 236, 42, 48, 50, 50, 52, 50, 238, 42, 44, 44, 46, 238, 44], "width": 280, "height": 353}}, "d1": {"d1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.44, 10.47, 17.83, -20.52, -20.16, -21.01, -20.56, 9.99], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 38}}, "d2": {"d2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [13.96, 18.71, 14.38, -14.29, -16.62, -14.68, -17.03, 18.31], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 33, "height": 31}}, "d3": {"d3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [19.63, 24.48, 20.31, -29.51, -16.69, -29.98, -17.37, 24.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 54, "height": 37}}, "d4": {"d4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11.56, 12.91, 11.85, -10.09, -13.15, -10.4, -13.44, 12.59], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 23, "height": 25}}, "d5": {"d5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.23, 10.09, 8.57, -16.9, -11.43, -17.16, -11.77, 9.84], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 27, "height": 20}}, "d6": {"d6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.97, 19.83, 13.47, -20.17, -13.52, -20.51, -14.03, 19.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 27}}, "d7": {"d7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.96, 13.96, 16.34, -16.04, -15.66, -16.44, -16.04, 13.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 32}}, "d8": {"d8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [7.85, 7.32, 8.02, -5.68, -5.98, -5.86, -6.15, 7.14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 13, "height": 14}}, "b7": {"b7": {"type": "mesh", "uvs": [0.81349, 0.11694, 0.99459, 0.32191, 0.99189, 0.40827, 0.98973, 0.47734, 0.98725, 0.5567, 0.98375, 0.66871, 0.98004, 0.78725, 0.97618, 0.91068, 0.97454, 0.96318, 0.97346, 0.99759, 0.01276, 0.99801, 0, 0.99886, 0.02722, 0.93934, 0.0734, 0.8384, 0.13608, 0.70138, 0.22027, 0.58985, 0.28546, 0.5035, 0.36453, 0.39874, 0.44135, 0.29698, 0.53897, 0.18125, 0.63845, 0.00074, 0.76633, 0.2493, 0.45801, 0.31419, 0.75609, 0.39204, 0.76761, 0.50594, 0.78424, 0.63282, 0.80599, 0.77268, 0.83285, 0.90965, 0.84309, 0.95867], "triangles": [15, 16, 26, 6, 26, 5, 27, 26, 6, 14, 15, 26, 27, 14, 26, 13, 14, 27, 7, 27, 6, 28, 27, 7, 13, 27, 28, 12, 13, 28, 8, 28, 7, 9, 28, 8, 10, 12, 28, 10, 28, 9, 10, 11, 12, 16, 17, 25, 5, 25, 4, 26, 25, 5, 16, 25, 26, 17, 18, 22, 3, 23, 2, 24, 23, 3, 22, 23, 24, 17, 22, 24, 4, 24, 3, 25, 24, 4, 17, 24, 25, 21, 20, 0, 19, 20, 21, 22, 18, 19, 21, 0, 1, 23, 19, 21, 2, 23, 21, 22, 19, 23, 1, 2, 21], "vertices": [3, 23, -20.19, 29.54, 0.98996, 92, -90.2, 51.5, 0.00928, 93, -163.28, 68.95, 0.00075, 4, 23, 32.02, 125.61, 0.55998, 92, -17.44, 133.11, 0.33936, 93, -82.38, 142.51, 0.09633, 94, -142.64, 161.66, 0.00434, 4, 23, 63.63, 133.84, 0.41933, 92, 15.22, 133.9, 0.40344, 93, -49.83, 139.88, 0.15628, 94, -110.67, 154.97, 0.02095, 4, 23, 88.91, 140.42, 0.28089, 92, 41.33, 134.53, 0.42555, 93, -23.79, 137.77, 0.23582, 94, -85.1, 149.62, 0.05774, 4, 23, 117.96, 147.98, 0.15086, 92, 71.34, 135.26, 0.37799, 93, 6.13, 135.36, 0.33092, 94, -55.72, 143.47, 0.14023, 4, 23, 158.96, 158.64, 0.0453, 92, 113.7, 136.28, 0.22698, 93, 48.36, 131.95, 0.37316, 94, -14.25, 134.8, 0.35455, 4, 23, 202.35, 169.93, 0.0063, 92, 158.52, 137.36, 0.08613, 93, 93.05, 128.34, 0.24215, 94, 29.64, 125.62, 0.66542, 4, 23, 247.54, 181.69, 2e-05, 92, 205.19, 138.49, 0.01611, 93, 139.59, 124.58, 0.07845, 94, 75.33, 116.06, 0.90542, 3, 92, 225.04, 138.97, 0.00561, 93, 159.38, 122.99, 0.04481, 94, 94.77, 111.99, 0.94959, 3, 92, 238.05, 139.29, 0.00354, 93, 172.35, 121.94, 0.03782, 94, 107.51, 109.33, 0.95864, 3, 92, 262.5, -269.24, 0.05524, 93, 153.96, -286.91, 0.39889, 94, 38.03, -293.99, 0.54587, 3, 92, 263.14, -274.65, 0.05517, 93, 154.04, -292.35, 0.3989, 94, 37.41, -299.4, 0.54594, 3, 92, 240, -264.41, 0.05676, 93, 132.09, -279.75, 0.39916, 94, 17.22, -284.15, 0.54408, 3, 92, 200.74, -247.04, 0.07067, 93, 94.86, -258.37, 0.40428, 94, -17.03, -258.27, 0.52506, 4, 23, 273.98, -183.88, 0.00029, 92, 147.46, -223.46, 0.12067, 93, 44.33, -229.35, 0.42316, 94, -63.53, -223.14, 0.45587, 4, 23, 223.33, -161.56, 0.00975, 92, 103.24, -190.15, 0.21706, 93, 3.84, -191.61, 0.43479, 94, -98.97, -180.63, 0.3384, 4, 23, 184.11, -144.27, 0.0433, 92, 69.01, -164.37, 0.33819, 93, -27.51, -162.39, 0.40184, 94, -126.41, -147.71, 0.21667, 4, 23, 136.54, -123.3, 0.16614, 92, 27.48, -133.1, 0.48418, 93, -65.54, -126.94, 0.26381, 94, -159.69, -107.78, 0.08587, 4, 23, 90.33, -102.93, 0.45184, 92, -12.86, -102.71, 0.43807, 93, -102.48, -92.51, 0.09235, 94, -192.03, -68.98, 0.01774, 4, 23, 36.52, -75.58, 0.91338, 92, -58.99, -63.8, 0.08212, 93, -144.3, -48.98, 0.00436, 94, -228.06, -20.56, 0.00014, 1, 23, -40.97, -54.47, 1, 3, 23, 33.5, 24.59, 0.95725, 92, -39.06, 34.41, 0.03874, 93, -114.21, 46.61, 0.00401, 4, 23, 94.53, -94.28, 0.44638, 92, -6.79, -95.24, 0.44361, 93, -95.66, -85.71, 0.09239, 94, -184.42, -63.1, 0.01762, 4, 23, 86.45, 35.82, 0.29037, 92, 15.05, 33.26, 0.67761, 93, -60.51, 39.81, 0.03199, 94, -133.81, 57.03, 2e-05, 4, 23, 126.31, 52.83, 0.05561, 92, 57.74, 40.72, 0.7045, 93, -17.27, 42.76, 0.22958, 94, -90.55, 54.53, 0.01031, 4, 23, 170.25, 73.32, 0.01259, 92, 105.2, 50.64, 0.20585, 93, 30.96, 47.66, 0.6761, 94, -42.08, 53.36, 0.10545, 4, 23, 218.26, 97.3, 0.00089, 92, 157.42, 63.02, 0.04208, 93, 84.19, 54.52, 0.30727, 94, 11.59, 53.49, 0.64976, 3, 92, 208.42, 77.52, 0.00566, 93, 136.43, 63.61, 0.03622, 94, 64.56, 55.96, 0.95812, 3, 92, 226.66, 82.97, 0.0015, 93, 155.14, 67.12, 0.01673, 94, 83.56, 57.1, 0.98177], "hull": 21, "edges": [0, 40, 0, 2, 18, 20, 20, 22, 36, 38, 38, 40, 38, 42, 2, 4, 42, 4, 44, 46, 4, 6, 46, 6, 34, 36, 34, 48, 6, 8, 48, 8, 32, 34, 32, 50, 8, 10, 50, 10, 28, 30, 30, 32, 30, 52, 10, 12, 52, 12, 26, 28, 26, 54, 12, 14, 54, 14, 22, 24, 24, 26, 24, 56, 14, 16, 16, 18, 56, 16], "width": 255, "height": 226}}, "b8": {"b8": {"type": "mesh", "uvs": [0.47814, 0.04764, 0.86324, 0.21835, 0.88889, 0.30006, 0.92236, 0.40668, 0.97129, 0.56254, 1, 0.71184, 1, 0.83856, 0.85647, 0.99783, 0.73678, 0.99791, 0.60181, 0.92924, 0.53665, 0.78373, 0.65624, 0.42527, 0.65623, 0.3206, 0.28694, 0.18202, 0.22291, 0.17968, 0.10933, 0.23749, 0.0132, 0.28641, 0.01172, 0.23598, 0.01011, 0.18115, 0.14184, 0.07639, 0, 0.03532, 0, 0.01205, 0.01967, 0.00185, 0.25363, 0.066, 0.42533, 0.06906, 0.13615, 0.12719, 0.39219, 0.09506, 0.22953, 0.15013, 0.40123, 0.13789, 0.60004, 0.26791, 0.72655, 0.22508, 0.80414, 0.22126, 0.68739, 0.24955, 0.74764, 0.29544, 0.79885, 0.41322], "triangles": [25, 23, 26, 27, 25, 26, 28, 27, 26, 27, 15, 25, 25, 18, 19, 17, 18, 25, 13, 27, 28, 14, 27, 13, 25, 15, 17, 27, 14, 15, 16, 17, 15, 20, 21, 22, 23, 22, 0, 19, 20, 22, 24, 23, 0, 23, 19, 22, 26, 23, 24, 25, 19, 23, 30, 28, 26, 31, 0, 1, 24, 0, 31, 30, 24, 31, 30, 26, 24, 30, 29, 28, 32, 29, 30, 33, 30, 31, 32, 30, 33, 31, 1, 2, 12, 29, 32, 13, 28, 29, 12, 13, 29, 33, 31, 2, 12, 32, 33, 34, 33, 2, 34, 2, 3, 12, 33, 34, 11, 12, 34, 34, 3, 4, 11, 34, 4, 10, 11, 4, 10, 4, 5, 6, 10, 5, 9, 10, 6, 7, 9, 6, 8, 9, 7], "vertices": [2, 106, 85.79, -11.82, 0.41891, 107, -9.97, -4.91, 0.58109, 2, 5, 209.88, -14.57, 0.01014, 106, 3.18, -22.68, 0.98986, 2, 5, 183.65, -18.75, 0.79678, 106, -19.26, -8.48, 0.20322, 1, 5, 149.42, -24.21, 1, 1, 5, 99.39, -32.19, 1, 1, 5, 51.47, -36.87, 1, 1, 5, 10.79, -36.87, 1, 1, 5, -40.34, -13.47, 1, 1, 5, -40.36, 6.04, 1, 1, 5, -18.32, 28.04, 1, 1, 5, 28.39, 38.66, 1, 1, 5, 143.46, 19.17, 1, 3, 5, 177.06, 19.17, 0.73191, 106, 0.87, 24.33, 0.26798, 108, 7.55, 89.04, 0.00011, 2, 106, 74.04, 40.08, 0.19162, 108, 29.87, 17.6, 0.80838, 2, 106, 81.5, 47.41, 0.02485, 108, 37.85, 10.83, 0.97515, 1, 108, 63.75, 14.88, 1, 1, 108, 85.67, 18.3, 1, 1, 108, 76.3, 5.09, 1, 1, 108, 66.13, -9.27, 1, 2, 107, 42.97, 12.04, 0.69492, 108, 28.94, -23.73, 0.30508, 1, 107, 67.73, 2.29, 1, 1, 107, 68.8, -5.1, 1, 1, 107, 66.09, -8.8, 1, 2, 107, 25.41, 6.14, 0.78843, 108, 12.27, -15.66, 0.21157, 3, 106, 86.33, -0.82, 0.23699, 107, -2.43, 3.12, 0.65422, 108, -9.74, 1.65, 0.10879, 2, 107, 41.56, 28.31, 0.10465, 108, 39.32, -11.12, 0.89535, 2, 106, 83.63, 8.75, 0.24202, 108, -0.46, 5.2, 0.75798, 2, 106, 87.91, 40.33, 0.01642, 108, 31.38, 3.81, 0.98358, 2, 106, 72.35, 16.74, 0.4829, 108, 6.47, 17.17, 0.5171, 3, 5, 193.97, 28.33, 0.16035, 106, 19.61, 20.02, 0.82692, 108, 4.96, 69.98, 0.01272, 1, 106, 16.29, -4.54, 1, 1, 106, 8.85, -14.84, 1, 3, 5, 199.86, 14.09, 0.06274, 106, 14.62, 5.44, 0.93691, 108, -10.01, 73.63, 0.00036, 2, 5, 185.13, 4.27, 0.80268, 106, -2.93, 7.82, 0.19732, 1, 5, 147.33, -4.08, 1], "hull": 23, "edges": [0, 44, 0, 2, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 36, 38, 38, 40, 40, 42, 42, 44, 38, 46, 46, 48, 48, 0, 32, 34, 34, 36, 34, 50, 50, 52, 28, 30, 30, 32, 30, 54, 54, 56, 56, 58, 58, 24, 52, 60, 2, 62, 62, 60, 48, 62, 24, 64, 64, 60, 24, 66, 2, 4, 66, 4, 22, 68, 4, 6, 6, 8, 68, 6], "width": 97, "height": 192}}, "b9": {"b9": {"type": "mesh", "uvs": [0.18421, 0, 0.24968, 0.05521, 0.29542, 0.09378, 0.32135, 0.11565, 0.36723, 0.11445, 0.49409, 0.08886, 0.67266, 0.05284, 0.7631, 0.14559, 0.83004, 0.21423, 0.99681, 0.38526, 0.9969, 0.47744, 0.99696, 0.54462, 0.99703, 0.62356, 0.99708, 0.6773, 0.9971, 0.69726, 0.90713, 0.72901, 0.82292, 0.80238, 0.76119, 0.85617, 0.69279, 0.91576, 0.5961, 1, 0.58453, 1, 0.49139, 0.95517, 0.49349, 0.88144, 0.49502, 0.82805, 0.575, 0.7879, 0.48386, 0.74122, 0.37846, 0.68723, 0.24022, 0.61642, 0.12584, 0.55784, 0.13137, 0.4925, 0.13749, 0.42024, 0.14384, 0.34515, 0.14942, 0.27923, 0.15499, 0.21339, 0.15919, 0.1638, 0.12362, 0.13041, 0.05098, 0.08724, 0, 0.05695, 0, 0.03012, 0.10279, 0, 0.1141, 0.04063, 0.15756, 0.0835, 0.1829, 0.1279, 0.18411, 0.18398, 0.233, 0.22558, 0.3626, 0.16918, 0.28354, 0.2747, 0.44634, 0.23247, 0.37165, 0.33384, 0.52932, 0.29346, 0.45991, 0.40887, 0.6731, 0.40211, 0.46429, 0.50431, 0.74629, 0.50599, 0.49408, 0.56141, 0.71055, 0.56561, 0.52585, 0.62859, 0.64302, 0.62691, 0.51394, 0.6899, 0.59139, 0.68486, 0.52982, 0.73524, 0.62714, 0.73272, 0.64605, 0.78768, 0.76898, 0.79173, 0.60774, 0.83291, 0.69874, 0.84168, 0.59816, 0.89704, 0.46582, 0.38613, 0.55409, 0.38945, 0.59331, 0.34383, 0.53475, 0.10459, 0.6279, 0.18267, 0.69298, 0.25034, 0.84863, 0.39297, 0.90003, 0.48847, 0.8939, 0.55217, 0.88202, 0.62465, 0.81089, 0.68077, 0.76799, 0.73086], "triangles": [21, 19, 20, 18, 21, 66, 18, 19, 21, 23, 24, 64, 65, 62, 63, 64, 62, 65, 17, 65, 63, 17, 63, 16, 22, 23, 64, 66, 22, 64, 66, 64, 65, 18, 66, 65, 18, 65, 17, 21, 22, 66, 15, 77, 14, 78, 59, 77, 78, 77, 15, 61, 59, 78, 60, 58, 59, 60, 59, 61, 25, 26, 58, 25, 58, 60, 62, 61, 78, 24, 60, 61, 24, 61, 62, 25, 60, 24, 16, 63, 78, 62, 78, 63, 15, 16, 78, 64, 24, 62, 28, 29, 52, 28, 52, 54, 27, 28, 54, 76, 55, 75, 76, 75, 12, 57, 54, 55, 57, 55, 76, 56, 54, 57, 27, 54, 56, 76, 12, 13, 77, 57, 76, 77, 76, 13, 59, 56, 57, 59, 57, 77, 26, 27, 56, 58, 26, 56, 59, 58, 56, 77, 13, 14, 74, 73, 10, 53, 73, 74, 52, 51, 53, 74, 10, 11, 75, 53, 74, 75, 74, 11, 54, 52, 53, 55, 54, 53, 55, 53, 75, 75, 11, 12, 8, 9, 73, 51, 68, 69, 73, 51, 69, 50, 68, 51, 73, 9, 10, 52, 50, 51, 53, 51, 73, 69, 49, 72, 72, 71, 8, 73, 72, 8, 73, 69, 72, 70, 5, 6, 70, 6, 7, 70, 4, 5, 71, 70, 7, 45, 70, 71, 71, 7, 8, 47, 71, 72, 68, 67, 69, 40, 39, 0, 38, 39, 40, 40, 0, 1, 37, 38, 40, 41, 40, 1, 36, 37, 40, 36, 40, 41, 41, 1, 2, 42, 41, 2, 42, 2, 3, 35, 36, 41, 35, 41, 42, 34, 35, 42, 45, 3, 4, 3, 45, 43, 3, 43, 42, 34, 42, 43, 33, 34, 43, 44, 43, 45, 33, 43, 44, 47, 45, 71, 44, 45, 47, 46, 44, 47, 32, 33, 44, 32, 44, 46, 46, 47, 49, 48, 46, 49, 48, 49, 69, 31, 32, 46, 31, 46, 48, 67, 48, 69, 50, 48, 67, 50, 67, 68, 30, 31, 48, 30, 48, 50, 30, 50, 52, 29, 30, 52, 70, 45, 4, 49, 47, 72], "vertices": [1, 4, -11.07, 37.48, 1, 2, 4, 30.52, 35.71, 0.99935, 95, -56.99, -79.03, 0.00065, 1, 4, 59.58, 34.48, 1, 1, 4, 76.05, 33.79, 1, 3, 4, 81.69, 45.61, 0.60686, 95, -10.41, -55.65, 0.39314, 99, -299.93, -80.37, 0, 3, 4, 84.18, 85.59, 0.07761, 95, -18.84, -16.49, 0.92239, 99, -317.18, -44.21, 0, 2, 95, -30.71, 38.64, 1, 98, -264.81, -1.46, 0, 3, 95, 36.06, 49.28, 0.91421, 96, -55.16, 42.13, 0.08579, 98, -202.79, 25.49, 0, 4, 95, 85.48, 57.16, 0.31487, 96, -7.21, 56.46, 0.67191, 97, -82.97, 49.86, 0.01323, 98, -156.9, 45.44, 0, 3, 96, 112.25, 92.18, 0.15742, 97, 32.76, 96.24, 0.61451, 98, -42.54, 95.14, 0.22808, 4, 96, 174.07, 85.96, 0.01136, 97, 94.89, 95.65, 0.3118, 98, 19.58, 96.33, 0.63575, 99, -55.28, 99.09, 0.04109, 4, 97, 140.17, 95.22, 0.08792, 98, 64.85, 97.2, 0.65157, 99, -10, 99.1, 0.24974, 100, -96.12, 92.91, 0.01076, 5, 97, 193.37, 94.72, 0.00608, 98, 118.04, 98.23, 0.30686, 99, 43.21, 99.12, 0.54528, 100, -43.04, 96.47, 0.14161, 101, -141.61, 68, 0.00016, 5, 97, 229.59, 94.37, 2e-05, 98, 154.26, 98.92, 0.13767, 99, 79.43, 99.14, 0.51709, 100, -6.9, 98.9, 0.33533, 101, -106.44, 76.69, 0.0099, 4, 98, 167.71, 99.18, 0.11176, 99, 92.88, 99.14, 0.48701, 100, 6.52, 99.8, 0.38505, 101, -93.38, 79.92, 0.01618, 4, 98, 189.59, 73.95, 0.04603, 99, 114.29, 73.5, 0.30658, 100, 29.59, 75.64, 0.56749, 101, -66.46, 60.15, 0.0799, 4, 98, 239.48, 50.89, 0.00058, 99, 163.73, 49.5, 0.0172, 100, 80.52, 54.98, 0.36736, 101, -12.7, 48.7, 0.61486, 3, 100, 117.86, 39.84, 0.00819, 101, 26.71, 40.3, 0.93419, 102, -36.97, 44.61, 0.05762, 2, 101, 70.38, 31, 0.17884, 102, 4.28, 27.51, 0.82116, 2, 101, 132.1, 17.85, 0, 102, 62.57, 3.33, 1, 2, 101, 132.89, 14.65, 0, 102, 62.77, 0.04, 1, 2, 101, 109.91, -18.37, 0.03492, 102, 34.16, -28.23, 0.96508, 3, 100, 139.94, -35.15, 0.00238, 101, 61.53, -29.69, 0.9227, 102, -15.48, -30.55, 0.07492, 2, 100, 104, -37.11, 0.09548, 101, 26.48, -37.89, 0.90452, 3, 99, 153.98, -21.16, 0.00045, 100, 75.49, -16.17, 0.84274, 101, -5.25, -22.24, 0.15681, 3, 4, 467.12, -130.49, 0.00293, 99, 122.51, -47.13, 0.14857, 100, 45.82, -44.18, 0.8485, 4, 4, 420.72, -139.08, 0.03333, 98, 164.28, -77.22, 0.0037, 99, 86.13, -77.17, 0.67016, 100, 11.51, -76.57, 0.29281, 4, 4, 359.87, -150.34, 0.15264, 98, 117.31, -117.52, 0.08464, 99, 38.4, -116.57, 0.7455, 100, -33.49, -119.06, 0.01722, 3, 4, 309.52, -159.66, 0.27453, 98, 78.44, -150.85, 0.14792, 99, -1.09, -149.17, 0.57755, 4, 4, 271.79, -136.9, 0.41032, 97, 102.6, -151.11, 0.00351, 98, 34.38, -150.11, 0.17212, 99, -45.12, -147.59, 0.41405, 4, 4, 230.06, -111.72, 0.65541, 97, 53.92, -148.89, 0.00708, 98, -14.34, -149.28, 0.13106, 99, -93.83, -145.85, 0.20645, 3, 4, 186.7, -85.56, 0.88764, 98, -64.98, -148.43, 0.04684, 99, -144.44, -144.04, 0.06551, 3, 4, 148.63, -62.59, 0.98113, 98, -109.44, -147.68, 0.00709, 99, -188.87, -142.45, 0.01178, 3, 4, 110.61, -39.65, 0.99979, 98, -153.83, -146.93, 1e-05, 99, -233.24, -140.86, 0.0002, 3, 4, 81.98, -22.37, 1, 98, -187.27, -146.36, 0, 99, -266.67, -139.66, 0, 3, 4, 57.38, -20.3, 1, 98, -209.58, -156.92, 0, 99, -289.17, -149.8, 0, 2, 4, 21.89, -24.27, 1, 98, -238.28, -178.17, 0, 2, 4, -3.01, -27.06, 1, 98, -258.42, -193.08, 0, 2, 4, -18.82, -18.28, 1, 98, -276.5, -193.42, 0, 1, 4, -22.34, 17.19, 1, 1, 4, 3.17, 6.71, 1, 2, 4, 34.44, 3.5, 0.99999, 95, -44.5, -108.97, 1e-05, 3, 4, 64.11, -4.72, 1, 98, -211.59, -140.06, 0, 99, -290.86, -132.9, 0, 2, 4, 97.32, -22.77, 1, 98, -173.81, -139, 0, 3, 4, 128.59, -24.21, 0.99956, 98, -146.04, -124.54, 5e-05, 99, -225.03, -118.63, 0.00039, 3, 4, 113.3, 26.54, 0.96, 95, 25.18, -65.45, 0.04, 99, -263.04, -81.69, 0, 3, 4, 164.53, -27.69, 0.99193, 98, -113.21, -109.52, 0.00292, 99, -191.92, -104.22, 0.00515, 4, 4, 162.18, 26.69, 0.85337, 95, 72.19, -52.08, 0.11398, 96, -5.96, -53.57, 0.03265, 99, -220.39, -57.82, 0, 4, 4, 211.57, -25.1, 0.96146, 97, -3.65, -81.58, 0.00309, 98, -73.83, -83.66, 0.01709, 99, -152.06, -79.11, 0.01836, 5, 4, 209.6, 27.4, 0.76548, 95, 117.65, -38.56, 0.0089, 96, 37.31, -34.17, 0.22562, 98, -101.89, -39.24, 0, 99, -179.28, -34.17, 0, 4, 4, 267.99, -27.67, 0.50651, 97, 47.16, -56.92, 0.28106, 98, -23.74, -57.55, 0.14646, 99, -101.49, -53.95, 0.06597, 3, 96, 114.29, -0.75, 0.00101, 97, 43.21, 3.88, 0.99686, 98, -29.45, 3.11, 0.00213, 4, 4, 324.83, -57.82, 0.17764, 97, 111.5, -56.31, 0.0295, 98, 40.55, -55.09, 0.45392, 99, -37.17, -52.71, 0.33895, 3, 97, 113.42, 24.04, 0.04284, 98, 40.16, 25.29, 0.94954, 99, -36.03, 27.66, 0.00762, 4, 4, 362.59, -69.09, 0.07391, 97, 150.07, -48.2, 0.0001, 98, 78.87, -45.88, 0.19453, 99, 1.32, -44.22, 0.73146, 3, 97, 153.51, 13.46, 0.00154, 98, 80.53, 15.86, 0.36097, 99, 4.15, 17.48, 0.63749, 4, 4, 406.57, -83.16, 0.02227, 98, 123.97, -35.97, 0.00206, 99, 46.6, -35.16, 0.93845, 100, -30.72, -37.29, 0.03723, 2, 4, 421.8, -53.42, 0.00034, 99, 45.47, -1.77, 0.99966, 3, 4, 441.04, -106.2, 0.00876, 99, 87.92, -38.56, 0.45132, 100, 10.73, -37.93, 0.53991, 3, 4, 448.79, -85.25, 0.00212, 99, 84.52, -16.48, 0.30951, 100, 5.88, -16.13, 0.68837, 3, 4, 469.96, -117.08, 0.00174, 99, 118.48, -34.03, 0.09846, 100, 40.93, -31.38, 0.89979, 3, 4, 481.94, -92.01, 4e-05, 99, 116.79, -6.3, 0.00144, 100, 37.39, -3.82, 0.99852, 2, 100, 73.99, 4.03, 0.84425, 101, -10.25, -2.61, 0.15575, 4, 98, 232.6, 35.38, 0.00039, 99, 156.56, 34.13, 0.01343, 100, 74.38, 39.16, 0.41035, 101, -15.99, 32.05, 0.57583, 2, 100, 105.13, -4.84, 0.03234, 101, 21.97, -5.91, 0.96766, 3, 100, 109.31, 21.43, 0.00333, 101, 21.5, 20.68, 0.9704, 102, -45.67, 26.27, 0.02628, 2, 101, 64.59, 1.79, 0.90628, 102, -6.74, -0.15, 0.09372, 4, 4, 255.41, -18.75, 0.82857, 97, 31.85, -55.09, 0.1233, 98, -39.1, -56.16, 0.03141, 99, -116.82, -52.27, 0.01672, 4, 4, 269.58, 2.15, 0.63449, 97, 34.34, -29.96, 0.34325, 98, -37.34, -30.97, 0.01587, 99, -114.58, -27.12, 0.00639, 3, 4, 248.13, 26.85, 0.55357, 96, 72.92, -19.43, 0.16721, 97, 3.7, -18.47, 0.27922, 3, 4, 99.07, 90.57, 0.03011, 95, -5.85, -7.66, 0.96989, 99, -306.58, -32.63, 0, 3, 95, 51.48, 6.02, 0.9829, 96, -34.16, 1.28, 0.0171, 98, -177.08, -12.56, 0, 4, 95, 100.14, 13.54, 0.01552, 96, 13.08, 15.15, 0.98131, 97, -59.02, 10.55, 0.00317, 98, -131.82, 6.84, 0, 3, 96, 113.18, 49.64, 0.12332, 97, 37.54, 53.96, 0.69327, 98, -36.55, 53.01, 0.18341, 4, 96, 178.7, 57.75, 0.00425, 97, 102.05, 67.97, 0.24139, 98, 27.53, 68.87, 0.70678, 99, -47.84, 71.48, 0.04757, 4, 97, 144.97, 65.8, 0.05727, 98, 70.49, 67.93, 0.64375, 99, -4.91, 69.73, 0.28871, 100, -89.09, 63.95, 0.01027, 5, 97, 193.78, 61.93, 0.00326, 98, 119.4, 65.47, 0.25864, 99, 43.94, 66.35, 0.60933, 100, -40.12, 63.82, 0.12854, 101, -133.04, 36.35, 0.00024, 4, 98, 157.6, 45.91, 0.05905, 99, 81.77, 46.07, 0.47947, 100, -1.03, 46.11, 0.44586, 101, -91.46, 25.73, 0.01563, 4, 98, 191.58, 34.33, 0.01002, 99, 115.53, 33.85, 0.11647, 100, 33.46, 36.15, 0.77928, 101, -55.75, 21.95, 0.09423], "hull": 40, "edges": [0, 78, 6, 8, 28, 30, 38, 40, 40, 42, 46, 48, 68, 70, 74, 76, 76, 78, 74, 80, 80, 0, 70, 72, 72, 74, 72, 82, 0, 2, 82, 2, 70, 84, 2, 4, 4, 6, 84, 4, 68, 86, 86, 6, 66, 68, 66, 88, 88, 90, 64, 66, 64, 92, 92, 94, 12, 14, 62, 64, 62, 96, 96, 98, 14, 16, 16, 18, 60, 62, 60, 100, 100, 102, 56, 58, 58, 60, 58, 104, 104, 106, 18, 20, 56, 108, 108, 110, 20, 22, 54, 56, 54, 112, 112, 114, 22, 24, 52, 54, 52, 116, 116, 118, 24, 26, 26, 28, 48, 50, 50, 52, 50, 120, 120, 122, 48, 124, 124, 126, 30, 32, 126, 32, 46, 128, 128, 130, 32, 34, 130, 34, 42, 44, 44, 46, 44, 132, 34, 36, 36, 38, 132, 36, 86, 88, 88, 92, 92, 96, 96, 134, 134, 136, 136, 138, 138, 98, 98, 94, 94, 90, 90, 6, 8, 10, 10, 12, 12, 140, 140, 90, 10, 140, 14, 142, 142, 94, 140, 142, 16, 144, 144, 98, 142, 144, 18, 146, 146, 102, 144, 146, 20, 148, 148, 106, 146, 148, 22, 150, 150, 110, 148, 150, 24, 152, 152, 114, 150, 152, 26, 154, 154, 118, 152, 154, 30, 156, 156, 122, 154, 156, 156, 126, 126, 130, 130, 132, 132, 42], "width": 171, "height": 404}}, "b3": {"b3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [87.37, 9.02, 106.88, -58.2, 36.78, -78.56, 17.26, -11.33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 73}}, "a1": {"a1": {"type": "mesh", "uvs": [0.69741, 0, 0.79468, 0.09482, 0.87264, 0.17082, 0.91462, 0.21173, 0.9667, 0.32093, 1, 0.39076, 1, 0.40771, 0.94817, 0.46828, 0.906, 0.44519, 0.85671, 0.4182, 0.78547, 0.37919, 0.7474, 0.35833, 0.69075, 0.37058, 0.65149, 0.37907, 0.61459, 0.38704, 0.58082, 0.39434, 0.5385, 0.40349, 0.43371, 0.51995, 0.38975, 0.52017, 0.3642, 0.61295, 0.34813, 0.67128, 0.32951, 0.73888, 0.30278, 0.83595, 0.20047, 0.99464, 0.11543, 0.9384, 0.09802, 0.86921, 0.07684, 0.78503, 0.02446, 0.57688, 0, 0.47965, 0, 0.43426, 0.06986, 0.33464, 0.08822, 0.30846, 0.13926, 0.23568, 0.22188, 0.11786, 0.26002, 0.06346, 0.38028, 0.04149, 0.5064, 0.01845, 0.60737, 0, 0.19274, 0.90606, 0.27476, 0.82238, 0.13949, 0.85843, 0.28328, 0.749, 0.24494, 0.67562, 0.30565, 0.66274, 0.2673, 0.60739, 0.32908, 0.60352, 0.2918, 0.52499, 0.14162, 0.46577, 0.35784, 0.46062, 0.22576, 0.26751, 0.4079, 0.3203, 0.28328, 0.16967, 0.46542, 0.24305, 0.39832, 0.14392, 0.52294, 0.25979, 0.50696, 0.10273, 0.55809, 0.2482, 0.60176, 0.11689, 0.61561, 0.26494, 0.68697, 0.14392, 0.67845, 0.29197, 0.73703, 0.16195, 0.72638, 0.28296, 0.81053, 0.25464, 0.77218, 0.31772, 0.8755, 0.30227, 0.91598, 0.3409, 0.95965, 0.37437], "triangles": [63, 1, 2, 61, 1, 63, 62, 61, 63, 2, 65, 63, 3, 65, 2, 64, 62, 63, 66, 65, 3, 4, 66, 3, 11, 62, 64, 67, 66, 4, 65, 10, 64, 65, 64, 63, 11, 64, 10, 67, 4, 5, 67, 5, 6, 9, 10, 65, 9, 65, 66, 8, 9, 66, 8, 66, 67, 7, 8, 67, 7, 67, 6, 57, 55, 36, 35, 36, 55, 53, 35, 55, 34, 35, 53, 33, 34, 53, 51, 33, 53, 32, 33, 51, 54, 52, 53, 51, 53, 52, 55, 54, 53, 49, 32, 51, 50, 51, 52, 49, 51, 50, 31, 32, 49, 47, 31, 49, 47, 49, 48, 30, 31, 47, 29, 30, 47, 28, 29, 47, 46, 47, 48, 46, 48, 18, 28, 47, 46, 27, 28, 46, 45, 46, 18, 44, 27, 46, 44, 46, 45, 19, 45, 18, 43, 44, 45, 20, 45, 19, 43, 45, 20, 42, 27, 44, 42, 44, 43, 21, 43, 20, 41, 42, 43, 41, 43, 21, 26, 27, 42, 41, 40, 42, 40, 26, 42, 22, 41, 21, 39, 41, 22, 41, 39, 40, 25, 26, 40, 38, 40, 39, 24, 25, 40, 24, 40, 38, 22, 23, 38, 24, 38, 23, 22, 38, 39, 37, 57, 36, 37, 59, 57, 61, 59, 0, 0, 59, 37, 1, 61, 0, 56, 55, 57, 56, 54, 55, 58, 57, 59, 56, 57, 58, 62, 59, 61, 60, 58, 59, 62, 60, 59, 12, 60, 62, 12, 62, 11, 13, 58, 60, 13, 60, 12, 14, 56, 58, 14, 58, 13, 54, 56, 14, 15, 54, 14, 52, 54, 15, 16, 52, 15, 50, 52, 16, 48, 49, 50, 17, 48, 50, 16, 17, 50, 18, 48, 17], "vertices": [3, 10, 128.81, 49.54, 0.60603, 12, 25.02, -94.61, 0.19588, 13, 80.86, 26.13, 0.19809, 3, 10, 143.19, 13.98, 0.39866, 12, -4.06, -119.63, 0.05026, 13, 51.78, 1.11, 0.55108, 3, 10, 154.72, -14.53, 0.08109, 12, -27.37, -139.69, 0.00437, 13, 28.47, -18.95, 0.91454, 3, 10, 160.93, -29.88, 0.00901, 12, -39.92, -150.48, 0.00022, 13, 15.92, -29.75, 0.99077, 1, 13, -14.24, -40.47, 1, 1, 13, -33.53, -47.32, 1, 1, 13, -37.77, -46.54, 1, 1, 13, -50.03, -28.11, 1, 1, 13, -41.92, -16.44, 1, 2, 10, 119.89, -67.06, 0.00048, 13, -32.45, -2.79, 0.99952, 2, 10, 105.6, -47.76, 0.11276, 13, -18.75, 16.93, 0.88724, 2, 10, 97.96, -37.45, 0.36569, 13, -11.43, 27.47, 0.63431, 2, 10, 81.25, -31.71, 0.75396, 13, -11.35, 45.13, 0.24604, 2, 10, 69.67, -27.73, 0.90955, 13, -11.3, 57.38, 0.09045, 2, 10, 58.79, -24, 0.9795, 13, -11.24, 68.89, 0.0205, 2, 10, 48.83, -20.57, 0.99787, 13, -11.2, 79.42, 0.00213, 1, 10, 36.35, -16.29, 1, 2, 10, -6.15, -26.49, 0.87345, 11, -1.35, -43.64, 0.12655, 2, 10, -17.97, -19.98, 0.57681, 11, 1.03, -30.35, 0.42319, 2, 10, -36.28, -36.75, 0.05245, 11, -20.73, -18.38, 0.94755, 2, 10, -47.8, -47.3, 0.00233, 11, -34.41, -10.86, 0.99767, 1, 11, -50.27, -2.14, 1, 1, 11, -73.04, 10.39, 1, 1, 11, -107.01, 48.56, 1, 1, 11, -88.25, 71.66, 1, 1, 11, -70, 73.74, 1, 2, 11, -47.8, 76.28, 0.99885, 12, -136.71, 128.78, 0.00115, 2, 11, 7.11, 82.55, 0.94643, 12, -81.81, 135.05, 0.05357, 2, 11, 32.76, 85.48, 0.89822, 12, -56.16, 137.97, 0.10178, 3, 10, -111.91, 57.28, 0.00036, 11, 44.09, 83.4, 0.88094, 12, -44.82, 135.89, 0.1187, 3, 10, -80.87, 68.96, 0.01926, 11, 65.11, 57.73, 0.74725, 12, -23.81, 110.23, 0.23348, 3, 10, -72.71, 72.03, 0.02944, 11, 70.63, 50.99, 0.68501, 12, -18.28, 103.49, 0.28555, 3, 10, -50.03, 80.56, 0.05258, 11, 85.99, 32.24, 0.47198, 12, -2.93, 84.74, 0.47544, 3, 10, -13.31, 94.38, 0.01843, 11, 110.84, 1.89, 0.15447, 12, 21.93, 54.39, 0.8271, 3, 10, 3.64, 100.75, 0.00138, 11, 122.32, -12.12, 0.08306, 12, 33.4, 40.38, 0.91556, 3, 10, 38.62, 87.68, 0.0036, 11, 121.14, -49.44, 0.00677, 12, 32.23, 3.06, 0.98963, 3, 10, 75.3, 73.96, 0.2911, 12, 30.99, -36.09, 0.70131, 13, 86.84, 84.65, 0.00759, 3, 10, 104.66, 62.98, 0.55935, 12, 30.01, -67.42, 0.3643, 13, 85.85, 53.32, 0.07635, 1, 11, -84.46, 46.83, 1, 1, 11, -68.1, 18.23, 1, 1, 11, -69.61, 60.73, 1, 1, 11, -50.24, 12.29, 1, 2, 11, -29.78, 20.5, 0.99969, 12, -118.69, 73, 0.00031, 2, 10, -58.14, -39.06, 0.00013, 11, -29.93, 1.58, 0.99987, 2, 11, -13.97, 10.62, 0.99909, 12, -102.89, 63.12, 0.00091, 2, 10, -44.54, -29.42, 0.029, 11, -16.43, -8.21, 0.971, 3, 10, -44.84, -6.42, 0.04775, 11, 5.25, -0.56, 0.9423, 12, -83.66, 51.94, 0.00994, 3, 10, -77.81, 29.14, 0.01503, 11, 28.37, 42.08, 0.8655, 12, -60.54, 94.58, 0.11946, 3, 10, -19.17, -2, 0.6156, 11, 17.68, -23.45, 0.36322, 12, -71.24, 29.05, 0.02119, 3, 10, -30.75, 60.58, 0.10998, 11, 73.24, 7.58, 0.36035, 12, -15.68, 60.08, 0.52968, 3, 10, 11.59, 21.67, 0.7175, 11, 49.96, -45, 0.01666, 12, -38.96, 7.5, 0.26584, 3, 10, -3.24, 73.71, 0.04141, 11, 94.5, -14.28, 0.12518, 12, 5.58, 38.22, 0.83341, 2, 10, 36.56, 30.23, 0.53328, 12, -22.85, -13.41, 0.46672, 2, 11, 94.55, -50.19, 0.00334, 12, 5.64, 2.31, 0.99666, 2, 10, 49.92, 17.93, 0.80912, 12, -30.22, -30.01, 0.19088, 3, 10, 65.04, 55.17, 0.31723, 12, 9.91, -32.39, 0.67924, 13, 65.75, 88.35, 0.00353, 2, 10, 60.78, 15.25, 0.86994, 12, -29.27, -41.16, 0.13006, 3, 10, 88.72, 37.88, 0.63857, 12, 1.12, -60.36, 0.30726, 13, 56.96, 60.37, 0.05417, 2, 10, 74.14, 2.95, 0.98877, 12, -36.64, -57.76, 0.01123, 3, 10, 108.24, 19.16, 0.65744, 12, -10.36, -84.86, 0.10589, 13, 45.48, 35.88, 0.23667, 3, 10, 87.66, -12.43, 0.80793, 12, -46.88, -75.49, 0.00057, 13, 8.97, 45.24, 0.1915, 3, 10, 119.44, 7.68, 0.50864, 12, -17.64, -99.15, 0.05001, 13, 38.2, 21.59, 0.44135, 3, 10, 101.63, -17.58, 0.53214, 12, -47.28, -90.38, 0.00211, 13, 8.56, 30.36, 0.46575, 3, 10, 127.71, -23.86, 0.10309, 12, -44.87, -117.09, 0.00217, 13, 10.97, 3.65, 0.89474, 3, 10, 109.63, -32.14, 0.23138, 12, -58.5, -102.62, 3e-05, 13, -2.66, 18.12, 0.76858, 1, 13, -4.53, -13.78, 1, 1, 13, -16.42, -24.24, 1, 1, 13, -27.2, -35.89, 1], "hull": 38, "edges": [0, 74, 10, 12, 12, 14, 32, 34, 34, 36, 44, 46, 46, 48, 56, 58, 48, 76, 76, 78, 78, 44, 48, 50, 50, 80, 80, 82, 42, 44, 82, 42, 50, 52, 52, 84, 84, 86, 40, 42, 86, 40, 52, 54, 54, 56, 54, 88, 88, 90, 36, 38, 38, 40, 90, 38, 56, 92, 92, 36, 58, 60, 60, 94, 94, 96, 96, 36, 60, 62, 62, 98, 98, 100, 100, 32, 62, 64, 64, 102, 102, 104, 30, 32, 104, 30, 64, 66, 66, 68, 66, 106, 106, 108, 28, 30, 108, 28, 68, 70, 70, 110, 110, 112, 112, 28, 70, 72, 72, 74, 72, 114, 114, 116, 116, 28, 74, 118, 118, 120, 26, 28, 120, 26, 0, 122, 122, 124, 22, 24, 24, 26, 124, 24, 0, 2, 2, 126, 126, 128, 128, 22, 2, 4, 4, 6, 4, 130, 20, 22, 130, 20, 6, 132, 18, 20, 132, 18, 6, 8, 8, 10, 8, 134, 14, 16, 16, 18, 134, 16], "width": 184, "height": 152}}, "ww": {"ww": {"x": 92.66, "y": 9.08, "rotation": 138.91, "width": 181, "height": 160}}, "a4": {"a4": {"type": "mesh", "uvs": [0.31531, 0.2159, 0.38285, 0.30697, 0.46915, 0.42334, 0.54149, 0.52088, 0.58132, 0.54431, 0.62973, 0.57279, 0.70022, 0.61427, 0.76063, 0.60198, 0.82486, 0.58892, 0.89736, 0.57417, 1, 0.83708, 1, 0.93709, 0.95099, 1, 0.90946, 1, 0.805, 0.93802, 0.71447, 0.88431, 0.64628, 0.84385, 0.58012, 0.80459, 0.528, 0.77367, 0.47189, 0.74038, 0.38921, 0.69133, 0.30359, 0.64053, 0.20729, 0.58339, 0.12097, 0.53217, 0.05509, 0.49308, 0.00242, 0.46183, 0.00307, 0.2949, 0.00422, 0, 0.03261, 0, 0.14218, 0.06168, 0.24303, 0.11845, 0.87294, 0.79291, 0.79719, 0.75864, 0.73582, 0.72437, 0.67673, 0.68766, 0.61234, 0.6485, 0.56688, 0.60934, 0.52522, 0.57385, 0.44567, 0.49553, 0.36007, 0.40986, 0.26082, 0.35969, 0.17976, 0.33032, 0.08582, 0.30095, 0.02598, 0.29483], "triangles": [43, 27, 28, 42, 43, 28, 26, 27, 43, 29, 42, 28, 41, 29, 30, 40, 41, 30, 42, 29, 41, 0, 40, 30, 25, 26, 43, 24, 43, 42, 25, 43, 24, 23, 42, 41, 24, 42, 23, 22, 41, 40, 23, 41, 22, 40, 0, 1, 39, 40, 1, 39, 1, 2, 38, 39, 2, 38, 2, 3, 21, 40, 39, 21, 39, 38, 22, 40, 21, 20, 21, 38, 19, 20, 38, 37, 38, 3, 4, 37, 3, 36, 4, 5, 36, 37, 4, 35, 36, 5, 34, 5, 6, 35, 5, 34, 37, 19, 38, 18, 19, 37, 36, 18, 37, 18, 36, 35, 17, 18, 35, 17, 35, 34, 16, 17, 34, 16, 34, 33, 33, 6, 7, 34, 6, 33, 32, 7, 8, 33, 7, 32, 31, 8, 9, 32, 8, 31, 31, 9, 10, 15, 16, 33, 15, 33, 32, 13, 31, 10, 14, 32, 31, 15, 32, 14, 10, 12, 13, 14, 31, 13, 11, 12, 10], "vertices": [2, 49, 52.09, -28.97, 0.44313, 50, -2.12, -28.97, 0.55687, 2, 49, 32.6, -23.21, 0.93267, 50, -21.61, -23.21, 0.06733, 2, 48, 61.91, -15.85, 0.09182, 49, 7.7, -15.85, 0.90818, 2, 48, 41.03, -9.68, 0.98899, 49, -13.18, -9.68, 0.01101, 1, 48, 31.26, -10.25, 1, 2, 25, 73.59, -10.95, 0.00421, 48, 19.38, -10.95, 0.99579, 2, 25, 56.29, -11.97, 0.58221, 48, 2.08, -11.97, 0.41779, 2, 25, 44.18, -19.12, 0.99618, 48, -10.03, -19.12, 0.00382, 1, 25, 31.3, -26.73, 1, 1, 25, 16.76, -35.31, 1, 1, 25, -19.92, -10.21, 1, 1, 25, -25.6, 2.91, 1, 1, 25, -18.78, 15.66, 1, 1, 25, -9.97, 19.47, 1, 1, 25, 15.69, 20.92, 1, 2, 25, 37.94, 22.17, 0.89288, 48, -16.27, 22.17, 0.10712, 2, 25, 54.69, 23.11, 0.36868, 48, 0.48, 23.11, 0.63132, 2, 25, 70.95, 24.02, 0.02228, 48, 16.74, 24.02, 0.97772, 2, 48, 29.54, 24.74, 0.98529, 49, -24.66, 24.74, 0.01471, 2, 48, 43.33, 25.52, 0.81942, 49, -10.88, 25.52, 0.18058, 2, 48, 63.64, 26.66, 0.21711, 49, 9.44, 26.66, 0.78289, 3, 48, 84.68, 27.84, 0.00463, 49, 30.47, 27.84, 0.95359, 50, -23.74, 27.84, 0.04178, 2, 49, 54.13, 29.17, 0.45953, 50, -0.08, 29.17, 0.54047, 2, 49, 75.34, 30.36, 0.04023, 50, 21.14, 30.36, 0.95977, 1, 50, 37.32, 31.27, 1, 1, 50, 50.26, 32, 1, 1, 50, 59.6, 10.03, 1, 1, 50, 76.09, -28.78, 1, 1, 50, 70.07, -31.39, 1, 1, 50, 43.34, -33.34, 1, 2, 49, 72.95, -35.13, 0.0518, 50, 18.74, -35.13, 0.9482, 1, 25, 9.52, -4.36, 1, 1, 25, 27.53, -1.91, 1, 1, 25, 42.49, -0.78, 1, 1, 48, 2.89, -0.18, 1, 1, 48, 18.77, 0.58, 1, 1, 48, 30.63, -0.39, 1, 2, 48, 41.48, -1.23, 0.99986, 49, -12.73, -1.23, 0.00014, 2, 48, 62.79, -4.22, 0.02331, 49, 8.58, -4.22, 0.97669, 2, 49, 31.59, -7.61, 0.99529, 50, -22.62, -7.61, 0.00471, 2, 49, 55.48, -5.1, 0.32872, 50, 1.27, -5.1, 0.67128, 1, 50, 20.13, -1.52, 1, 1, 50, 41.71, 3.24, 1, 1, 50, 54.75, 7.92, 1], "hull": 31, "edges": [18, 20, 20, 22, 22, 24, 24, 26, 54, 56, 18, 62, 62, 26, 16, 18, 16, 64, 26, 28, 64, 28, 12, 14, 14, 16, 14, 66, 28, 30, 66, 30, 12, 68, 30, 32, 68, 32, 10, 12, 10, 70, 32, 34, 70, 34, 6, 8, 8, 10, 8, 72, 34, 36, 72, 36, 6, 74, 36, 38, 74, 38, 6, 4, 4, 76, 38, 40, 76, 40, 4, 2, 2, 78, 40, 42, 78, 42, 2, 0, 0, 60, 0, 80, 42, 44, 80, 44, 60, 82, 44, 46, 82, 46, 56, 58, 58, 60, 58, 84, 46, 48, 48, 50, 84, 48, 56, 86, 86, 50, 50, 52, 52, 54, 86, 52], "width": 138, "height": 85}}, "b2": {"b2": {"type": "mesh", "uvs": [0.77441, 0.00427, 0.95218, 0.00371, 0.96532, 0.06957, 0.98023, 0.14435, 0.99672, 0.227, 0.9967, 0.35992, 0.99669, 0.49617, 0.99666, 0.70767, 0.99664, 0.87393, 0.99814, 0.91516, 0.87913, 0.85427, 0.75836, 0.79248, 0.65006, 0.73707, 0.56434, 0.77382, 0.45851, 0.81919, 0.36557, 0.85903, 0.29541, 0.8891, 0.23789, 0.91377, 0.14117, 0.9946, 0.06455, 0.8809, 0, 0.7851, 0.00652, 0.77582, 0.02385, 0.71744, 0.05954, 0.5972, 0.0929, 0.48479, 0.13658, 0.33765, 0.13959, 0.32751, 0.20028, 0.2539, 0.47081, 0.39867, 0.68068, 0.51098, 0.83714, 0.59471, 0.79723, 0.48673, 0.75916, 0.38371, 0.71782, 0.27186, 0.681, 0.17225, 0.68081, 0.05631, 0.68072, 0.00456, 0.13272, 0.79071, 0.18708, 0.71416, 0.22883, 0.61424, 0.23951, 0.48319, 0.23854, 0.35214, 0.46473, 0.56364, 0.5657, 0.56623, 0.25137, 0.6546, 0.32344, 0.61197, 0.69967, 0.57013, 0.86179, 0.76865, 0.93946, 0.74011, 0.88606, 0.54288, 0.855, 0.39885, 0.82878, 0.27558, 0.80548, 0.17567, 0.78995, 0.08743], "triangles": [35, 36, 0, 53, 0, 1, 53, 1, 2, 35, 0, 53, 34, 35, 53, 52, 53, 2, 52, 2, 3, 34, 53, 52, 33, 34, 52, 51, 52, 3, 51, 3, 4, 33, 52, 51, 32, 33, 51, 5, 51, 4, 50, 51, 5, 32, 51, 50, 31, 32, 50, 6, 50, 5, 49, 50, 6, 31, 50, 49, 30, 31, 49, 7, 49, 6, 48, 49, 7, 30, 46, 29, 43, 29, 46, 12, 43, 46, 30, 49, 48, 47, 30, 48, 11, 46, 30, 11, 30, 47, 12, 46, 11, 10, 47, 48, 11, 47, 10, 8, 48, 7, 10, 48, 8, 10, 8, 9, 28, 41, 27, 26, 27, 41, 42, 40, 41, 25, 26, 41, 40, 25, 41, 28, 42, 41, 43, 42, 28, 29, 43, 28, 13, 42, 43, 13, 43, 12, 14, 45, 42, 14, 42, 13, 15, 45, 14, 24, 25, 40, 45, 40, 42, 39, 24, 40, 39, 40, 45, 44, 39, 45, 44, 45, 15, 16, 44, 15, 38, 44, 16, 17, 37, 38, 16, 17, 38, 18, 37, 17, 19, 37, 18, 23, 24, 39, 38, 23, 39, 38, 39, 44, 37, 22, 23, 38, 37, 23, 21, 22, 37, 19, 21, 37, 20, 21, 19], "vertices": [1, 54, 61.37, 12.08, 1, 1, 54, 47.35, -34.57, 1, 2, 53, 113.75, -34.28, 0.00041, 54, 33.39, -34.1, 0.99959, 2, 53, 97.89, -34.02, 0.04227, 54, 17.53, -33.56, 0.95773, 2, 53, 80.36, -33.73, 0.27472, 54, 0.01, -32.97, 0.72528, 2, 53, 54.14, -26.28, 0.87828, 54, -26.07, -25.06, 0.12172, 1, 53, 27.28, -18.65, 1, 2, 52, 102.19, 2.38, 0.39149, 53, -14.43, -6.79, 0.60851, 2, 52, 107.79, -31.24, 0.98967, 53, -47.21, 2.53, 0.01033, 1, 52, 109.59, -39.51, 1, 1, 52, 75.37, -32.56, 1, 1, 52, 40.65, -25.5, 1, 2, 51, 76.14, -24.21, 0.12532, 52, 9.51, -19.18, 0.87468, 2, 51, 51.5, -22.98, 0.87052, 52, -12.42, -30.47, 0.12948, 2, 26, 94.85, -21.46, 0.01402, 51, 21.09, -21.46, 0.98598, 2, 26, 68.14, -20.13, 0.72162, 51, -5.62, -20.13, 0.27838, 2, 26, 47.98, -19.13, 0.99925, 51, -25.78, -19.13, 0.00075, 1, 26, 31.45, -18.3, 1, 1, 26, 0.81, -24.47, 1, 2, 75, -18.86, -3.26, 0.36175, 26, -10.62, 4.74, 0.63825, 1, 75, -12.07, 22.28, 1, 2, 75, -9.5, 21.82, 0.99861, 26, -17.91, 30.5, 0.00139, 1, 75, 3.12, 24.38, 1, 2, 75, 29.1, 29.66, 0.87697, 76, -11.48, 34.18, 0.12303, 3, 75, 53.4, 34.59, 0.32634, 76, 13.18, 31.67, 0.60256, 77, -1.63, 37.98, 0.0711, 3, 75, 85.2, 41.04, 0.00799, 76, 45.47, 28.39, 0.16437, 77, 24.27, 18.42, 0.82763, 3, 75, 87.39, 41.49, 0.00539, 76, 47.69, 28.16, 0.13529, 77, 26.05, 17.07, 0.85932, 1, 77, 36.17, -2.97, 1, 3, 76, 58.42, -63.13, 0.08989, 51, 54.63, 58.02, 0.65832, 77, -12.15, -66.53, 0.25179, 4, 76, 51.96, -124.73, 0.00041, 51, 100.33, 16.21, 0.29137, 77, -49.63, -115.84, 0.0039, 52, 10.16, 27.92, 0.70433, 2, 52, 55.27, 18.04, 0.64812, 53, 19.79, 28.92, 0.35188, 3, 52, 40.85, 38.07, 0.0734, 53, 44.07, 33.39, 0.92081, 54, -35.1, 34.78, 0.00579, 3, 52, 27.09, 57.19, 0.00396, 53, 67.24, 37.66, 0.78413, 54, -11.86, 38.64, 0.21192, 2, 53, 92.39, 42.29, 0.28387, 54, 13.36, 42.83, 0.71613, 2, 53, 114.79, 46.41, 0.06499, 54, 35.83, 46.56, 0.93501, 2, 53, 137.67, 39.97, 0.00419, 54, 58.6, 39.72, 0.99581, 1, 54, 68.75, 36.67, 1, 2, 75, 6.83, -8.81, 0.67225, 26, 13.37, 15.46, 0.32775, 4, 75, 28.11, -12.71, 0.71362, 26, 32.84, 24.9, 0.27893, 76, -25.01, -5.98, 0.00743, 51, -40.92, 24.9, 2e-05, 5, 75, 51.52, -11.11, 0.36256, 26, 50.77, 40.03, 0.11672, 76, -2.19, -11.4, 0.48533, 51, -23, 40.03, 0.03458, 77, -37.12, 9.13, 0.00082, 4, 26, 62.97, 64.14, 0.00728, 76, 24.46, -6.89, 0.88366, 51, -10.79, 64.14, 0.03214, 77, -12, -0.83, 0.07692, 2, 51, -1.57, 89.38, 0.01751, 77, 13.97, -7.72, 0.9825, 4, 26, 114.91, 26.96, 0.00047, 76, 25.43, -70.76, 0.07488, 51, 41.15, 26.96, 0.82638, 77, -44.31, -55.94, 0.09827, 4, 76, 32.46, -97.52, 0.01109, 51, 66.85, 16.71, 0.94558, 77, -52.18, -82.46, 0.02916, 52, -19.05, 11.57, 0.01417, 5, 75, 47.96, -20.8, 0.3478, 26, 53.63, 30.11, 0.31128, 76, -8.46, -19.6, 0.28174, 51, -20.13, 30.11, 0.05757, 77, -46.73, 5.38, 0.00162, 5, 75, 66.07, -32.56, 0.05369, 26, 75.19, 31.33, 0.18597, 76, 5.33, -36.21, 0.35297, 51, 1.43, 31.33, 0.35261, 77, -43.56, -15.98, 0.05476, 3, 51, 100.92, 3.03, 0.08394, 77, -62.71, -117.64, 0.00038, 52, 17.29, 16.82, 0.91568, 1, 52, 67.8, -16.03, 1, 2, 52, 87.83, -6.75, 0.85399, 53, -16.54, 10.1, 0.14601, 2, 52, 66.75, 30.72, 0.16379, 53, 26.35, 13.13, 0.83621, 3, 52, 53.5, 58.45, 0.00776, 53, 57.08, 13.25, 0.98634, 54, -22.45, 14.41, 0.00591, 2, 53, 83.35, 13.25, 0.35947, 54, 3.82, 13.96, 0.64053, 2, 53, 104.8, 13.79, 0.04153, 54, 25.27, 14.13, 0.95847, 2, 53, 123.36, 12.94, 0.00319, 54, 43.82, 12.95, 0.99681], "hull": 37, "edges": [16, 18, 34, 36, 40, 42, 52, 54, 42, 44, 44, 74, 74, 34, 44, 46, 46, 76, 32, 34, 76, 32, 46, 48, 48, 78, 30, 32, 48, 50, 50, 52, 50, 80, 28, 30, 36, 38, 38, 40, 38, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 54, 52, 82, 82, 84, 24, 26, 26, 28, 84, 26, 54, 56, 56, 86, 86, 24, 30, 88, 88, 78, 76, 88, 28, 90, 90, 80, 88, 90, 90, 84, 84, 86, 56, 58, 58, 60, 58, 92, 22, 24, 92, 22, 86, 92, 60, 94, 18, 20, 20, 22, 94, 20, 92, 60, 94, 96, 14, 16, 96, 14, 60, 98, 12, 14, 98, 12, 60, 62, 62, 100, 8, 10, 10, 12, 100, 10, 62, 64, 64, 102, 102, 8, 64, 66, 66, 68, 66, 104, 6, 8, 104, 6, 68, 106, 2, 4, 4, 6, 106, 4, 68, 70, 70, 72, 2, 0, 0, 72, 70, 0], "width": 164, "height": 123}}, "b4": {"b4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-23.24, -2.59, -14.66, 15.48, 10.64, 3.47, 2.06, -14.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 28}}, "b6": {"b6": {"type": "mesh", "uvs": [0.27718, 0, 0.32962, 0, 0.72308, 0.06264, 0.77216, 0.19202, 0.81573, 0.30689, 0.87092, 0.45237, 0.85906, 0.536, 0.83771, 0.68662, 0.91484, 0.82922, 0.95885, 0.91059, 1, 0.98667, 1, 0.99722, 0.7471, 0.99731, 0.60017, 0.99736, 0.51274, 0.99823, 0.45738, 0.94394, 0.37318, 0.86136, 0.25931, 0.74968, 0.20827, 0.69962, 0.15992, 0.65221, 0.04627, 0.49626, 0.01187, 0.37221, 0.00423, 0.26347, 0.10684, 0.08327, 0.09555, 0.20321, 0.23256, 0.11368, 0.41572, 0.15581, 0.60465, 0.27693, 0.66522, 0.17161, 0.65369, 0.37348, 0.71282, 0.298, 0.70561, 0.50688, 0.76762, 0.44194, 0.76185, 0.59641, 0.79791, 0.56305, 0.12151, 0.32433, 0.26862, 0.23656, 0.42582, 0.27167, 0.2066, 0.45422, 0.3465, 0.34364, 0.48206, 0.36997, 0.29458, 0.5771, 0.42726, 0.46124, 0.44283, 0.72191, 0.56454, 0.60817, 0.5602, 0.82508, 0.658, 0.7034, 0.66886, 0.92825, 0.76232, 0.81185, 0.84491, 0.90973, 0.2669, 0.55403, 0.40057, 0.4106, 0.58788, 0.39669], "triangles": [30, 28, 3, 27, 28, 30, 30, 3, 4, 29, 27, 30, 32, 30, 4, 29, 30, 32, 32, 4, 5, 31, 29, 32, 52, 29, 31, 6, 32, 5, 34, 32, 6, 31, 32, 34, 33, 31, 34, 7, 34, 6, 33, 34, 7, 36, 25, 26, 24, 25, 36, 27, 37, 26, 36, 26, 37, 35, 24, 36, 21, 22, 24, 39, 36, 37, 37, 52, 40, 39, 37, 40, 35, 21, 24, 29, 37, 27, 29, 52, 37, 51, 39, 40, 38, 35, 36, 38, 36, 39, 42, 51, 40, 20, 21, 35, 20, 35, 38, 40, 52, 31, 42, 40, 31, 50, 38, 39, 50, 39, 51, 41, 50, 51, 42, 41, 51, 44, 42, 31, 44, 31, 33, 19, 20, 38, 19, 38, 50, 18, 19, 50, 18, 50, 41, 46, 44, 33, 46, 33, 7, 43, 42, 44, 41, 42, 43, 17, 18, 41, 17, 41, 43, 48, 46, 7, 45, 43, 44, 45, 44, 46, 48, 7, 8, 16, 17, 43, 15, 16, 43, 49, 48, 8, 49, 8, 9, 47, 46, 48, 45, 46, 47, 45, 15, 43, 9, 10, 11, 12, 47, 48, 12, 48, 49, 11, 12, 49, 11, 49, 9, 47, 14, 45, 14, 15, 45, 13, 47, 12, 47, 13, 14, 25, 23, 0, 26, 1, 2, 25, 0, 1, 26, 25, 1, 28, 26, 2, 28, 2, 3, 24, 23, 25, 24, 22, 23, 28, 27, 26], "vertices": [1, 20, -56.49, -13.86, 1, 1, 20, -39.61, -1.58, 1, 1, 20, 99.08, 73.96, 1, 2, 20, 139.76, 51.23, 0.336, 22, 22.69, 30.86, 0.664, 1, 22, 64.04, 32.14, 1, 2, 21, 215.67, 125.52, 0.00089, 22, 116.41, 33.76, 0.99911, 2, 21, 237.62, 108.53, 0.08426, 22, 139.74, 18.73, 0.91574, 2, 21, 277.14, 77.93, 0.64925, 22, 181.75, -8.35, 0.35075, 2, 21, 332.72, 83.19, 0.95469, 22, 236.67, 1.68, 0.04531, 2, 21, 364.43, 86.18, 0.99307, 22, 268.01, 7.41, 0.00693, 1, 21, 394.08, 88.99, 1, 1, 21, 397.13, 87.37, 1, 1, 21, 349.96, -1.55, 1, 1, 21, 322.56, -53.21, 1, 1, 21, 306.5, -84.08, 1, 1, 21, 280.49, -95.22, 1, 1, 21, 240.92, -112.16, 1, 1, 21, 187.42, -135.06, 1, 1, 21, 163.44, -145.33, 1, 1, 21, 140.72, -155.06, 1, 1, 21, 74.47, -171.11, 1, 1, 21, 32.22, -164.18, 1, 2, 20, -93.67, -147.43, 0.384, 21, -0.61, -150.19, 0.616, 1, 20, -95.3, -75.76, 1, 2, 20, -75.87, -110.12, 0.44, 21, -0.98, -108.85, 0.56, 2, 20, -48.99, -54.37, 0.392, 21, -1.27, -46.96, 0.608, 2, 20, 18.07, -22.63, 0.4595, 21, 45.08, 10.97, 0.5405, 3, 20, 102.18, -10.44, 0.6575, 21, 115.31, 58.82, 0.13747, 22, 22.19, -41.36, 0.20504, 2, 20, 101.42, 31.59, 0.44089, 22, -0.09, -5.71, 0.55911, 3, 20, 136.53, -24.49, 0.15438, 21, 152.35, 61.25, 0.26622, 22, 58.87, -35.73, 0.5794, 3, 20, 141.05, 9.31, 0.0186, 21, 141.58, 93.61, 0.00795, 22, 45.35, -4.43, 0.97345, 3, 20, 178.9, -47.62, 0.00732, 21, 200.57, 59.05, 0.34658, 22, 107.1, -33.77, 0.6461, 3, 20, 186.37, -15.92, 0.00039, 21, 193.38, 90.81, 0.00864, 22, 97.2, -2.75, 0.99097, 2, 21, 236.92, 65.1, 0.41316, 22, 142.8, -24.6, 0.58684, 2, 21, 234.02, 82.89, 0.21475, 22, 138.37, -7.13, 0.78525, 1, 21, 38.85, -118.3, 1, 1, 21, 40.95, -53.13, 1, 1, 21, 80.42, -3.24, 1, 1, 21, 92.24, -108.3, 1, 1, 21, 86.41, -42.16, 1, 3, 20, 80.62, -63.74, 0.00554, 21, 119.31, 1.46, 0.99395, 22, 31.12, -98.16, 0.00051, 1, 21, 144.15, -96.21, 1, 1, 21, 135.45, -31.8, 1, 1, 21, 213.64, -66.29, 1, 1, 21, 203.5, -6.07, 1, 1, 21, 265.34, -40.85, 1, 2, 21, 248.45, 12.19, 0.95826, 22, 158.85, -76.32, 0.04174, 1, 21, 315.42, -18.47, 1, 2, 21, 299.24, 32.23, 0.93905, 22, 207.72, -51.97, 0.06095, 2, 21, 342.92, 46.26, 0.98267, 22, 250.02, -34.22, 0.01733, 1, 21, 132.32, -102.4, 1, 3, 20, 62.2, -93.56, 0.00038, 21, 115.84, -33.42, 0.99958, 22, 30.67, -133.21, 3e-05, 3, 20, 119.81, -46.03, 0.06167, 21, 146.77, 34.56, 0.60102, 22, 55.62, -62.81, 0.33731], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 20, 22, 26, 28, 38, 40, 40, 42, 42, 44, 44, 46, 42, 48, 48, 50, 50, 52, 52, 54, 54, 56, 4, 6, 56, 6, 54, 58, 58, 60, 6, 8, 8, 10, 60, 8, 58, 62, 62, 64, 64, 10, 62, 66, 66, 68, 10, 12, 12, 14, 68, 12, 66, 14, 40, 70, 70, 72, 72, 74, 74, 58, 38, 76, 76, 78, 78, 80, 80, 62, 34, 82, 82, 84, 84, 62, 32, 34, 32, 86, 86, 88, 88, 66, 28, 30, 30, 32, 30, 90, 90, 92, 92, 14, 28, 94, 94, 96, 14, 16, 96, 16, 22, 24, 24, 26, 24, 98, 16, 18, 18, 20, 98, 18, 34, 36, 36, 38, 36, 100, 100, 102, 102, 80, 80, 104, 104, 62], "width": 238, "height": 196}}, "a2": {"a2": {"type": "mesh", "uvs": [0.60123, 0, 0.65658, 0, 0.83379, 0.01796, 0.91461, 0.11108, 0.95798, 0.16106, 0.9626, 0.15132, 1, 0.16572, 1, 0.16868, 0.84714, 0.24135, 0.81618, 0.25647, 0.7939, 0.26188, 0.78123, 0.25854, 0.76666, 0.26467, 0.74749, 0.26513, 0.74329, 0.26128, 0.73885, 0.24684, 0.61914, 0.22478, 0.5869, 0.21884, 0.56657, 0.26372, 0.43599, 0.33406, 0.21542, 0.35501, 0.25918, 0.42606, 0.15395, 0.4431, 0.15143, 0.48242, 0.17312, 1, 0.14001, 1, 0.15166, 0.46297, 0.05668, 0.41854, 0.00047, 0.39225, 0.00058, 0.37168, 0.00083, 0.32587, 0.00114, 0.27035, 0.00129, 0.24205, 0.0015, 0.20469, 0.09458, 0.19461, 0.03775, 0.22211, 0.0947, 0.25353, 0.1977, 0.26128, 0.26839, 0.23947, 0.41812, 0.19649, 0.3841, 0.18527, 0.38228, 0.13636, 0.43048, 0.0904, 0.42922, 0.08927, 0.43864, 0.08647, 0.48343, 0.03725, 0.77918, 0.18683, 0.83418, 0.17183, 0.55918, 0.19496, 0.77673, 0.21433, 0.77918, 0.24621, 0.59269, 0.19372, 0.60702, 0.21054, 0.71073, 0.21683, 0.82684, 0.20683, 0.59009, 0.17715, 0.61991, 0.16728, 0.65323, 0.15772, 0.69533, 0.15742, 0.71988, 0.17087, 0.68773, 0.18372, 0.64381, 0.19183, 0.6012, 0.18372, 0.76906, 0.16218, 0.77586, 0.14322, 0.8041, 0.13122, 0.82723, 0.13243, 0.84492, 0.12913, 0.84628, 0.13539, 0.83131, 0.14252, 0.82417, 0.15974, 0.79457, 0.16514, 0.72106, 0.08516, 0.63853, 0.05237, 0.52327, 0.0862, 0.47689, 0.14654, 0.69992, 0.08969, 0.63921, 0.08795, 0.62012, 0.1298, 0.58192, 0.17061, 0.48098, 0.25431, 0.29819, 0.29791, 0.15223, 0.29861, 0.04242, 0.28256, 0.5771, 0.08701, 0.52326, 0.15319, 0.44892, 0.22327, 0.27962, 0.2709, 0.2644, 0.27411, 0.17303, 0.27961, 0.05748, 0.26495, 0.51162, 0.27045, 0.32351, 0.32358, 0.16049, 0.33137, 0.0754, 0.40694, 0.77291, 0.07987, 0.91014, 0.1542, 0.89124, 0.19578, 0.84405, 0.2362, 0.58879, 0.16327, 0.61548, 0.15982, 0.64648, 0.15396, 0.69417, 0.1486, 0.71468, 0.1464, 0.7452, 0.16103, 0.76667, 0.13689, 0.79576, 0.12616, 0.82724, 0.12348], "triangles": [86, 87, 38, 88, 37, 38, 87, 88, 38, 89, 37, 88, 81, 87, 80, 88, 87, 81, 29, 30, 93, 94, 29, 93, 28, 29, 94, 27, 28, 94, 93, 82, 81, 93, 30, 82, 20, 93, 92, 94, 93, 20, 22, 94, 20, 21, 22, 20, 27, 94, 22, 26, 27, 22, 23, 26, 22, 23, 25, 26, 25, 23, 24, 92, 81, 80, 93, 81, 92, 19, 92, 91, 20, 92, 19, 48, 17, 80, 48, 80, 86, 17, 91, 80, 18, 91, 17, 87, 86, 80, 92, 80, 91, 19, 91, 18, 35, 33, 34, 32, 33, 35, 90, 32, 35, 36, 90, 35, 31, 32, 90, 83, 31, 90, 83, 90, 82, 30, 31, 83, 30, 83, 82, 89, 36, 37, 90, 36, 89, 82, 90, 89, 81, 82, 89, 81, 89, 88, 85, 86, 39, 86, 38, 39, 44, 45, 74, 42, 43, 44, 75, 44, 74, 42, 44, 75, 41, 42, 75, 85, 75, 74, 85, 74, 84, 40, 41, 75, 39, 40, 75, 86, 85, 48, 85, 39, 75, 50, 15, 49, 14, 15, 50, 49, 46, 54, 98, 54, 97, 8, 98, 97, 54, 98, 50, 54, 50, 49, 98, 11, 50, 9, 98, 8, 9, 11, 98, 12, 14, 50, 10, 11, 9, 11, 12, 50, 13, 14, 12, 95, 2, 3, 107, 95, 3, 106, 95, 107, 67, 107, 3, 65, 106, 107, 66, 65, 107, 66, 107, 67, 68, 67, 3, 66, 67, 68, 69, 66, 68, 64, 106, 65, 96, 68, 3, 3, 4, 96, 65, 69, 64, 69, 65, 66, 70, 64, 69, 70, 71, 64, 4, 5, 6, 4, 6, 7, 69, 96, 70, 96, 69, 68, 47, 70, 96, 47, 46, 71, 47, 71, 70, 97, 47, 96, 97, 96, 4, 54, 46, 47, 54, 47, 97, 97, 4, 7, 8, 97, 7, 73, 0, 1, 95, 73, 1, 45, 0, 73, 2, 95, 1, 72, 73, 95, 74, 45, 73, 84, 74, 73, 76, 77, 73, 84, 73, 77, 72, 76, 73, 78, 84, 77, 106, 105, 72, 106, 72, 95, 64, 105, 106, 105, 103, 76, 105, 76, 72, 76, 78, 77, 102, 76, 103, 102, 78, 76, 85, 84, 78, 101, 78, 102, 58, 102, 103, 57, 101, 102, 58, 57, 102, 78, 99, 85, 100, 78, 101, 104, 103, 105, 104, 105, 64, 59, 58, 103, 63, 104, 64, 71, 63, 64, 100, 99, 78, 56, 100, 101, 56, 101, 57, 79, 85, 99, 104, 59, 103, 56, 55, 99, 56, 99, 100, 79, 99, 55, 60, 57, 58, 60, 58, 59, 62, 55, 56, 46, 63, 71, 61, 56, 57, 61, 57, 60, 62, 56, 61, 59, 46, 60, 104, 46, 59, 63, 46, 104, 61, 60, 46, 51, 55, 62, 51, 62, 61, 48, 85, 79, 48, 79, 55, 48, 55, 51, 52, 51, 61, 46, 53, 61, 49, 53, 46, 52, 61, 53, 17, 48, 51, 17, 51, 52, 16, 52, 53, 17, 52, 16, 15, 53, 49, 16, 53, 15], "vertices": [6, 9, 273.27, 7.99, 0.95052, 87, -139.59, -185.31, 0.00669, 78, -150.86, -32.31, 0.04279, 79, -203.39, -124.85, 0, 82, -113.69, -436.81, 0, 84, -219.32, -282.3, 0, 6, 9, 267.43, -23.86, 0.95883, 87, -142.28, -153.05, 0.03545, 78, -164.64, -3.01, 0.00572, 79, -227.76, -103.52, 0, 82, -141.86, -452.77, 0, 84, -249.85, -271.49, 0, 2, 9, 228.5, -122.12, 0.69783, 87, -130.41, -48.03, 0.30217, 2, 9, 115.19, -149.39, 0.09483, 87, -28.17, 7.93, 0.90517, 2, 87, 26.7, 37.97, 1, 82, -386.1, -379.4, 0, 2, 87, 15.37, 39.74, 1, 82, -382.96, -390.42, 0, 2, 87, 29.97, 62.91, 1, 82, -410.11, -386.88, 0, 3, 87, 33.35, 63.19, 0.99959, 88, -70.21, 32.9, 0.00041, 82, -411.79, -383.93, 0, 2, 88, 49.76, 10.19, 1, 82, -374.97, -267.52, 0, 2, 88, 87.2, 10.95, 1, 82, -371.27, -230.25, 0, 2, 88, 101.81, 6.01, 1, 82, -364.64, -216.33, 0, 2, 88, 109.33, -3.03, 1, 82, -354.76, -209.94, 0, 8, 9, -52.4, -26.42, 0.05506, 87, 165.74, -66.92, 0.00061, 88, 110.74, -8.53, 0.85951, 89, 19.52, 2.78, 0.08478, 78, 93.99, 185.17, 1e-05, 79, -66.26, 172.54, 0, 82, -349.14, -209.2, 0, 83, -41.27, 149.78, 3e-05, 8, 9, -44.83, -18.15, 0.09821, 87, 160.6, -76.87, 0.00109, 88, 111.57, -19.7, 0.74943, 89, 24.34, -7.33, 0.15121, 78, 92.64, 174.04, 1e-05, 79, -63.03, 161.81, 0, 82, -337.94, -209.69, 0, 83, -40.64, 138.6, 5e-05, 8, 9, -36.11, -17.08, 0.22642, 87, 152.46, -80.19, 0.0025, 88, 106.38, -26.79, 0.42234, 89, 22.06, -15.82, 0.3486, 78, 86.17, 168.1, 2e-05, 79, -66.57, 153.77, 1e-05, 82, -331.52, -215.69, 0, 83, -45.96, 131.6, 0.00011, 7, 9, -19, -20.22, 0.35293, 87, 135.13, -81.63, 0.0039, 88, 92.38, -37.1, 0.09956, 89, 12.72, -30.49, 0.54339, 78, 70.43, 160.7, 3e-05, 79, -78.03, 140.69, 1e-05, 83, -60.15, 121.55, 0.00018, 8, 9, 18.46, 44.1, 0.72168, 88, 113.58, -108.46, 1e-05, 89, 58.25, -89.38, 0.00015, 78, 77.39, 86.58, 0.05036, 79, -41.94, 75.57, 0.0152, 82, -249.58, -218.25, 0, 83, -40.23, 49.82, 0.2126, 84, -143.4, -36.39, 0, 8, 9, 28.55, 61.42, 0.37336, 88, 119.29, -127.67, 1e-05, 89, 70.51, -105.24, 8e-05, 78, 79.26, 66.62, 0.08506, 79, -32.23, 58.04, 0.03968, 82, -229.82, -214.87, 0, 83, -34.87, 30.5, 0.50181, 84, -127.89, -49.09, 0, 5, 9, -19.81, 82.39, 0.01948, 88, 167.69, -106.8, 0, 89, 108.12, -68.3, 1e-05, 83, 13.9, 50.5, 0.98046, 84, -99.55, -4.65, 4e-05, 3, 9, -85.17, 172.05, 2e-05, 83, 123.73, 34.72, 0.25553, 84, -0.69, 45.72, 0.74445, 3, 82, -117.55, 27.8, 0, 84, 128.95, 25.25, 0.0896, 85, 13.12, 28.27, 0.9104, 3, 82, -179.9, 85.9, 2e-05, 85, 13.64, 113.49, 0.98523, 86, 13.71, 136.63, 0.01475, 3, 82, -135.95, 133.2, 5e-05, 85, 78.21, 113.19, 0.80063, 86, 63.74, 95.82, 0.19931, 3, 82, -156.85, 173.08, 0.0018, 85, 93.39, 155.57, 0.85278, 86, 102.18, 119.24, 0.14542, 3, 82, -459.8, 681.97, 0.00766, 85, 262.63, 723.11, 0.99234, 86, 590.51, 454.34, 0, 3, 82, -442.95, 691.52, 0.00766, 85, 281.07, 717.18, 0.99234, 86, 601.12, 438.14, 0, 3, 82, -145.99, 153.65, 0.00016, 85, 86.45, 134.42, 0.7892, 86, 83.5, 107.15, 0.21063, 3, 82, -72.59, 136.82, 2e-05, 85, 123.78, 69.02, 0.37873, 86, 71.43, 32.83, 0.62124, 4, 89, 466.45, -122.06, 0, 82, -29.15, 126.86, 0.0018, 85, 145.87, 30.31, 0.01614, 86, 64.29, -11.17, 0.98206, 3, 89, 453.76, -141.87, 0, 82, -17.61, 106.36, 0.02534, 86, 44.58, -24, 0.97466, 3, 89, 425.5, -186, 0, 82, 8.1, 60.69, 0.33264, 86, 0.66, -52.6, 0.66736, 3, 89, 391.25, -239.49, 0, 82, 39.26, 5.35, 0.99086, 86, -52.57, -87.26, 0.00914, 3, 81, 114.48, -56.56, 0.00434, 82, 55.14, -22.87, 0.9953, 86, -79.7, -104.93, 0.00037, 2, 82, 76.11, -60.11, 0.99929, 86, -115.52, -128.25, 0.00071, 2, 82, 34.42, -96.99, 0.99913, 86, -155, -89.02, 0.00087, 4, 79, 212.01, -150.68, 0, 81, 89.95, -75.89, 0.01367, 82, 47.83, -53.23, 0.98575, 86, -110.47, -99.59, 0.00058, 4, 79, 210.6, -101.7, 0, 81, 62.44, -35.33, 0.59131, 82, 1.12, -38.38, 0.40867, 86, -98.66, -52.02, 2e-05, 3, 79, 171.1, -55.34, 1e-05, 81, 4.22, -17.47, 0.20206, 80, 89.72, -14.68, 0.79794, 3, 79, 123.54, -46.89, 0.00251, 81, -40.42, -35.89, 0.00036, 80, 42.25, -23.56, 0.99713, 4, 78, 98.14, -33.6, 0.34014, 79, 25.24, -26.21, 0.65393, 81, -134.44, -71.28, 0, 80, -57.02, -38.92, 0.00592, 4, 89, 149.99, -201.34, 0, 78, 94.99, -57.08, 0.58316, 79, 31.77, -48.98, 0.41684, 84, -28.87, -124.88, 0, 3, 78, 44.81, -81.85, 0.85193, 79, -4.27, -91.79, 0.14807, 84, -46.53, -177.97, 0, 4, 9, 189.59, 124.9, 0.10896, 78, -14.77, -78.7, 0.86742, 79, -60.11, -112.79, 0.02362, 84, -90.65, -218.13, 0, 4, 9, 190.99, 125.4, 0.11049, 78, -15.63, -79.92, 0.86627, 79, -60.41, -114.25, 0.02323, 84, -90.39, -219.6, 0, 4, 9, 193.15, 119.4, 0.13989, 78, -20.88, -76.3, 0.84328, 79, -66.67, -113.03, 0.01682, 84, -96.65, -220.78, 0, 3, 9, 243.8, 83.46, 0.56276, 78, -82.98, -76.54, 0.43724, 84, -140.14, -265.12, 0, 4, 9, 44.26, -55.82, 0.4921, 87, 64.76, -63.82, 0.27068, 88, 23.11, -58.81, 0.22542, 89, -44.04, -75.74, 0.0118, 4, 9, 55.33, -90.56, 0.202, 87, 44.99, -33.18, 0.70541, 88, -9.79, -43.08, 0.09218, 89, -80.4, -72.95, 0.00041, 7, 9, 58.35, 72.45, 0.67991, 88, 106.91, -156.93, 0, 89, 69.53, -137, 2e-05, 78, 61.44, 40.32, 0.13328, 79, -38.01, 26.8, 0.05103, 82, -202.24, -230.64, 0, 83, -47.77, 1.47, 0.13576, 4, 9, 13.57, -48.73, 0.4029, 87, 96.23, -62.64, 0.08541, 88, 49.29, -41.31, 0.42682, 89, -25.94, -49.97, 0.08487, 7, 9, -22.55, -43.56, 0.13508, 87, 132.45, -58.18, 0.00354, 88, 77.81, -18.53, 0.48137, 89, -7.57, -18.44, 0.37994, 78, 59.74, 181.74, 1e-05, 79, -96.26, 155.68, 0, 83, -74.38, 140.37, 5e-05, 8, 9, 56.2, 52.9, 0.82907, 88, 94.15, -141.98, 0, 89, 52.23, -127.66, 2e-05, 78, 51.82, 57.47, 0.07294, 79, -53.7, 38.65, 0.01539, 82, -218.6, -241.54, 0, 83, -60.27, 16.65, 0.08258, 84, -140.68, -75.05, 0, 8, 9, 35.76, 48.14, 0.81273, 88, 104.67, -123.82, 0, 89, 55.49, -106.93, 4e-05, 78, 65.67, 73.24, 0.04148, 79, -47.33, 58.65, 0.01243, 82, -235.38, -228.93, 0, 83, -49.42, 34.62, 0.13331, 84, -142.15, -54.11, 0, 4, 9, 17.73, -10.24, 0.90896, 87, 102.29, -100.87, 0.01195, 88, 74.49, -70.7, 0.0493, 89, 8.17, -68.28, 0.02979, 7, 9, 16.72, -79.11, 0.14291, 87, 85.25, -34.13, 0.1391, 88, 25, -22.79, 0.70232, 89, -55.28, -41.47, 0.01568, 78, 7.11, 187.81, 0, 79, -146.91, 140.14, 0, 83, -127.25, 137.07, 0, 7, 9, 75.12, 50.98, 0.83543, 88, 79.78, -154.45, 0, 89, 43.33, -144.47, 1e-05, 78, 35.31, 48.02, 0.11396, 79, -65.04, 23.38, 0.00855, 82, -207.93, -257.28, 0, 83, -74.86, 4.44, 0.04206, 7, 9, 83.07, 31.78, 0.83241, 88, 60.35, -147.1, 0, 89, 22.56, -144.63, 0, 78, 17.67, 59.01, 0.13125, 79, -85.6, 26.37, 0.00445, 82, -217.54, -275.7, 0, 83, -94.15, 12.15, 0.03188, 6, 9, 90.32, 10.63, 0.93076, 89, 0.24, -143.4, 0, 78, -0.53, 71.99, 0.05984, 79, -107.47, 30.97, 0.00083, 82, -229.11, -294.83, 0, 83, -114.35, 21.72, 0.00856, 3, 9, 86.21, -13.65, 0.94691, 87, 35.3, -115.5, 0.04859, 88, 25.1, -118.26, 0.0045, 4, 9, 68.48, -25, 0.8589, 87, 49.45, -99.91, 0.10685, 88, 28.98, -97.57, 0.03375, 89, -24.58, -109.77, 0.0005, 4, 9, 57.41, -3.84, 0.98752, 87, 65.66, -117.43, 0.00768, 88, 51.97, -104, 0.0045, 89, -0.81, -107.46, 0.0003, 6, 9, 52.93, 23.1, 0.84654, 78, 37.14, 83.61, 0.06463, 79, -77.62, 56.71, 0.00863, 82, -243.55, -258.16, 0, 83, -79.32, 39.79, 0.0802, 84, -169.58, -67.11, 0, 8, 9, 66.55, 45.94, 0.80022, 88, 81.99, -144.75, 0, 89, 41.89, -134.64, 1e-05, 78, 39.35, 57.11, 0.11632, 79, -64.97, 33.32, 0.01286, 82, -217.3, -253.94, 0, 83, -72.48, 14.1, 0.07059, 84, -149.18, -84.16, 0, 4, 9, 73.06, -55.09, 0.58795, 87, 37.15, -72.06, 0.34892, 88, 3.91, -80.3, 0.06274, 89, -54.19, -102.71, 0.0004, 3, 9, 93.68, -62.92, 0.56419, 87, 15.2, -69.9, 0.42033, 88, -15.91, -89.96, 0.01547, 3, 9, 104.2, -81.64, 0.42777, 87, 0.15, -54.58, 0.56915, 88, -36.77, -84.8, 0.00308, 3, 9, 100.39, -94.7, 0.30668, 87, 0.41, -40.98, 0.69197, 88, -43.67, -73.08, 0.00134, 2, 9, 102.24, -105.56, 0.2312, 87, -4.22, -30.98, 0.7688, 3, 9, 95.05, -105.05, 0.20587, 87, 2.86, -29.59, 0.79398, 88, -47.55, -62.1, 0.00015, 3, 9, 88.61, -94.97, 0.26243, 87, 11.72, -37.64, 0.73091, 88, -35.79, -64.31, 0.00666, 3, 9, 69.98, -87.3, 0.2719, 87, 31.7, -40.17, 0.68565, 88, -17.45, -55.99, 0.04245, 4, 9, 67.04, -69.16, 0.43373, 87, 39.28, -56.91, 0.48035, 88, -2.22, -66.28, 0.08522, 89, -64.96, -91.85, 0.0007, 2, 9, 164.8, -43.38, 0.84418, 87, -48.33, -107.36, 0.15582, 5, 9, 210.41, -2.66, 0.99345, 87, -81.7, -158.59, 0.00655, 79, -180.36, -65.39, 0, 82, -162.21, -395.45, 0, 84, -219.91, -218.54, 0, 4, 9, 184.51, 70.65, 0.3464, 78, -42.22, -31.62, 0.65304, 79, -104.13, -80.66, 0.00056, 84, -143.43, -204.55, 0, 3, 78, 31.8, -26.81, 0.94151, 79, -38.26, -46.58, 0.05849, 84, -94.82, -148.53, 0, 2, 9, 161.93, -30.28, 0.90065, 87, -42.13, -119.26, 0.09935, 3, 9, 170.31, 4.29, 0.97968, 78, -69.27, 30.61, 0.02032, 82, -182.62, -360.23, 0, 6, 9, 125.23, 23.92, 0.90488, 89, -0.57, -180.74, 0, 78, -21.19, 40.87, 0.0934, 79, -113.93, -5.82, 1e-05, 82, -196.51, -313.07, 0, 83, -129.2, -12.56, 0.00171, 7, 9, 83.35, 54.33, 0.72819, 88, 76.59, -162.74, 0, 89, 43.35, -153.36, 1e-05, 78, 30.57, 40.51, 0.21563, 79, -66.37, 14.6, 0.00867, 82, -200.08, -261.44, 0, 83, -78.2, -3.79, 0.04749, 3, 88, 188.71, -153.5, 0, 89, 144.58, -104.27, 0, 83, 34.08, 3.42, 1, 5, 89, 261.57, -119.6, 0, 81, -47.57, 32.83, 0.02901, 82, -127.47, -52.9, 0, 84, 61.51, -20.17, 0.72793, 80, 49.35, 45.16, 0.24306, 4, 89, 334.03, -164.77, 0, 81, 36.96, 20.73, 0.75754, 85, 28.55, -44.48, 0.23162, 86, -73.97, 4.39, 0.01084, 4, 89, 378.38, -214.74, 0, 82, 11.36, 5.59, 0.96499, 85, 84.09, -81.62, 0.00023, 86, -54.12, -59.4, 0.03478, 3, 9, 177.92, 39.84, 0.63449, 78, -54.78, -2.73, 0.36551, 84, -172.8, -193.17, 0, 6, 9, 109.14, 84.48, 0.00202, 88, 80.9, -202.18, 0, 89, 61.6, -188.59, 0, 78, 27.14, 0.98, 0.9975, 82, -160.4, -261.85, 0, 83, -74.6, -43.3, 0.00048, 4, 79, 31.86, 8.7, 0.87849, 82, -162.09, -170.66, 0, 83, 15.95, -32.42, 0.12135, 84, -50.11, -71.24, 0.00016, 5, 89, 254.15, -151.5, 0, 81, -41.49, 0.65, 0.03962, 82, -102.79, -74.42, 0, 84, 61.44, -52.91, 0.12484, 80, 48.69, 12.42, 0.83554, 6, 89, 263.63, -153.18, 0, 81, -32.13, 2.93, 0.10716, 82, -96.85, -66.84, 0, 84, 71.06, -52.43, 0.11802, 85, -42.49, -51.06, 0.00317, 80, 58.32, 12.74, 0.77165, 4, 89, 312.1, -176.57, 0, 81, 21.65, 1.08, 0.9961, 85, 10.31, -61.44, 0.00373, 86, -98.82, 2.66, 0.00017, 3, 79, 235.59, -106.2, 0, 81, 85.94, -25.7, 0.15408, 82, 13.63, -16.28, 0.84592, 3, 88, 192.95, -128.12, 0, 89, 139.37, -79.08, 0, 83, 38.77, 28.72, 1, 2, 9, -61.49, 234.61, 0, 84, 57.34, 12.46, 1, 4, 89, 350.08, -130.56, 0, 81, 37.84, 58.5, 0.09941, 85, 35.43, -7.32, 0.89084, 86, -45.27, 28.97, 0.00974, 3, 82, -75.58, 119.88, 0, 85, 109.29, 59.74, 0.40481, 86, 54.33, 34.71, 0.59519, 2, 9, 165.27, -74.3, 0.68112, 87, -56.87, -77.64, 0.31888, 2, 87, 21.2, 9.42, 1, 82, -357.88, -372.42, 0, 3, 87, 69.53, 2.36, 0.54755, 88, -7.52, 0.05, 0.45245, 82, -371.72, -325.59, 0, 2, 88, 46.08, 5.24, 1, 82, -370.49, -271.76, 0, 7, 9, 90.88, 48.86, 0.83531, 88, 67.45, -164.48, 0, 89, 35.45, -158.28, 0, 78, 21.26, 40.58, 0.13341, 79, -74.92, 10.93, 0.00479, 82, -199.44, -270.72, 0, 83, -87.37, -5.36, 0.02648, 7, 9, 91.94, 32.79, 0.89235, 88, 55.01, -154.25, 0, 89, 20.16, -153.23, 0, 78, 11.04, 53.03, 0.08819, 79, -89.27, 18.24, 0.0025, 82, -211.08, -281.86, 0, 83, -99.62, 5.09, 0.01696, 7, 9, 95.26, 13.74, 0.88933, 88, 38.86, -143.62, 0, 89, 1.26, -149.14, 0, 78, -2.73, 66.59, 0.09911, 79, -107.33, 25.14, 0.0011, 82, -223.56, -296.62, 0, 83, -115.57, 16.01, 0.01045, 8, 9, 96.26, -14.81, 0.93655, 87, 25.3, -117.01, 0.05363, 88, 17.38, -124.79, 0.00385, 89, -25.57, -139.34, 0, 78, -20.16, 89.23, 0.00586, 79, -132.37, 38.89, 0, 82, -244.8, -315.72, 0, 83, -136.72, 35.22, 0.00011, 8, 9, 96.56, -27.06, 0.84293, 87, 21.8, -105.26, 0.14598, 88, 8.24, -116.62, 0.0067, 89, -37.04, -135.02, 0, 78, -27.53, 99.02, 0.00431, 79, -143.05, 44.9, 0, 82, -254, -323.82, 0, 83, -145.7, 43.56, 8e-05, 8, 9, 76.87, -41.6, 0.70996, 87, 37, -86.08, 0.24516, 88, 11.13, -92.31, 0.04361, 89, -43.12, -111.31, 0.00034, 78, -19.98, 122.3, 0.00091, 79, -145.47, 69.26, 0, 82, -277.79, -318.06, 0, 83, -142.37, 67.81, 2e-05, 8, 9, 101.77, -58.93, 0.60948, 87, 8.43, -75.86, 0.37699, 88, -18.55, -98.58, 0.0133, 89, -68.54, -127.87, 0, 78, -50.32, 121.91, 0.00022, 79, -173.1, 56.74, 0, 82, -275.1, -348.28, 0, 83, -172.17, 62.07, 0, 8, 9, 110.77, -77.89, 0.47975, 87, -5.21, -59.92, 0.51617, 88, -38.53, -92.16, 0.00405, 89, -89.49, -129.09, 0, 78, -68.67, 132.09, 3e-05, 79, -193.99, 58.71, 0, 82, -283.85, -367.35, 0, 83, -192.02, 68.86, 0, 8, 9, 110.47, -96.56, 0.32937, 87, -9.8, -41.82, 0.66977, 88, -51.92, -79.15, 0.00085, 89, -106.67, -121.8, 0, 78, -79.28, 147.44, 1e-05, 79, -209.87, 68.53, 0, 82, -298.36, -379.1, 0, 83, -205.18, 82.1, 0], "hull": 46, "edges": [0, 90, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 86, 88, 88, 90, 84, 86, 92, 94, 92, 98, 98, 100, 100, 30, 102, 96, 30, 32, 32, 34, 32, 104, 104, 102, 104, 106, 106, 98, 98, 108, 94, 108, 108, 100, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 92, 122, 122, 102, 120, 122, 122, 124, 124, 110, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 126, 144, 146, 146, 148, 148, 150, 150, 78, 152, 154, 154, 156, 158, 96, 96, 160, 160, 162, 162, 164, 164, 166, 166, 62, 148, 168, 168, 154, 168, 170, 170, 172, 172, 174, 176, 178, 178, 180, 62, 64, 64, 66, 180, 64, 34, 182, 182, 184, 184, 186, 60, 62, 186, 60, 56, 58, 58, 60, 186, 58, 40, 188, 52, 54, 54, 56, 188, 54, 188, 44, 174, 176, 144, 190, 100, 22, 4, 6, 6, 8, 6, 192, 192, 194, 194, 196, 196, 22, 156, 198, 198, 158, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 118, 208, 126, 208, 210, 210, 212, 212, 214, 214, 134, 28, 30, 26, 28, 22, 24, 24, 26, 16, 18, 18, 20, 20, 22], "width": 351, "height": 686}}, "c1": {"c1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.17, 12.35, 8.55, -11.64, -10.44, -11.15, -9.83, 12.84], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 24, "height": 19}}, "c2": {"c2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [3.55, 11.57, 3.07, -7.43, -3.93, -7.25, -3.44, 11.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 7}}, "c3": {"c3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.39, 7.48, 3.83, -14.51, -6.17, -14.25, -5.61, 7.74], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 22, "height": 10}}, "c4": {"c4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [6.37, 5.61, 5.98, -9.38, -10.01, -8.97, -9.63, 6.02], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 16}}, "c5": {"c5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.03, 9.11, 11.47, -12.88, -12.53, -12.26, -11.96, 9.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 22, "height": 24}}, "c6": {"c6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [3.62, 15.35, 3.98, -12.65, -17.02, -12.91, -17.38, 15.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 21}}, "a5": {"a5": {"type": "mesh", "uvs": [1, 0.06352, 1, 0.07136, 0.9804, 0.11222, 0.96034, 0.15402, 0.93477, 0.20733, 0.90691, 0.2654, 0.88696, 0.30698, 0.86696, 0.34867, 0.77383, 0.40443, 0.69199, 0.45345, 0.59539, 0.5113, 0.5163, 0.55866, 0.42478, 0.61347, 0.35062, 0.65787, 0.27085, 0.70565, 0.28347, 0.74041, 0.28859, 0.80549, 0.29257, 0.8559, 0.29481, 0.88444, 0.29749, 0.91837, 0.13778, 1, 0.09894, 1, 0.07308, 0.9817, 0, 0.92997, 0, 0.846, 0.07887, 0.71759, 0.15478, 0.65525, 0.22813, 0.59502, 0.27974, 0.55264, 0.373, 0.47606, 0.43083, 0.42857, 0.47987, 0.38829, 0.54426, 0.33542, 0.58574, 0.30135, 0.65473, 0.2528, 0.72269, 0.20497, 0.77123, 0.17081, 0.78011, 0.1293, 0.78999, 0.08312, 0.80029, 0.03496, 0.80685, 0.00425, 0.92586, 0.00851, 0.91616, 0.03662, 0.91616, 0.08373, 0.90373, 0.14397, 0.89041, 0.19108, 0.84158, 0.24416, 0.79985, 0.28173, 0.72453, 0.34142, 0.63813, 0.38457, 0.53623, 0.44111, 0.47088, 0.47311, 0.39557, 0.53634, 0.33354, 0.58842, 0.26376, 0.6338, 0.2261, 0.68044, 0.1973, 0.75409, 0.18401, 0.82477, 0.21281, 0.87536, 0.26044, 0.90073], "triangles": [20, 59, 19, 59, 20, 22, 20, 21, 22, 22, 58, 59, 22, 23, 58, 58, 24, 57, 58, 23, 24, 59, 18, 19, 59, 58, 18, 58, 17, 18, 58, 57, 17, 57, 16, 17, 24, 25, 57, 57, 56, 16, 57, 25, 56, 56, 15, 16, 25, 26, 56, 56, 14, 15, 56, 55, 14, 56, 26, 55, 55, 54, 14, 14, 54, 13, 55, 26, 54, 54, 53, 13, 13, 53, 12, 26, 27, 54, 54, 27, 53, 53, 52, 12, 12, 52, 11, 27, 28, 53, 53, 28, 52, 28, 29, 52, 52, 51, 11, 11, 51, 10, 52, 29, 51, 51, 50, 10, 10, 50, 9, 29, 30, 51, 51, 30, 50, 50, 49, 9, 9, 49, 8, 30, 31, 50, 49, 31, 32, 49, 50, 31, 32, 33, 49, 49, 48, 8, 8, 48, 7, 49, 33, 48, 48, 47, 7, 7, 47, 6, 33, 34, 48, 48, 34, 47, 47, 46, 6, 6, 46, 5, 34, 35, 47, 47, 35, 46, 5, 46, 4, 35, 36, 46, 4, 46, 45, 46, 36, 45, 4, 45, 3, 45, 44, 3, 45, 36, 44, 36, 37, 44, 3, 44, 2, 37, 38, 44, 44, 43, 2, 44, 38, 43, 2, 43, 1, 38, 39, 43, 39, 42, 43, 43, 42, 1, 42, 0, 1, 42, 41, 0, 39, 40, 42, 42, 40, 41], "vertices": [1, 14, 1.89, 28.95, 1, 1, 14, 4.47, 29.29, 1, 1, 14, 18.49, 26.75, 1, 1, 14, 32.84, 24.15, 1, 2, 14, 51.14, 20.84, 0.88692, 58, -18.86, 13.21, 0.11308, 2, 14, 71.07, 17.23, 0.15305, 58, -0.25, 21.2, 0.84695, 2, 14, 85.34, 14.64, 0.0023, 58, 13.08, 26.91, 0.9977, 1, 58, 26.44, 32.65, 1, 2, 58, 54.05, 29.16, 0.88842, 59, -12.42, 28.83, 0.11158, 2, 58, 78.31, 26.1, 0.28526, 59, 12, 30.02, 0.71474, 3, 58, 106.94, 22.48, 0.00831, 59, 40.83, 31.42, 0.92507, 60, -33.09, 33.97, 0.06663, 2, 59, 64.43, 32.56, 0.54073, 60, -9.53, 32.17, 0.45927, 2, 59, 91.74, 33.89, 0.02391, 60, 17.73, 30.09, 0.97609, 2, 60, 39.83, 28.41, 0.98731, 61, -21.87, 33.95, 0.01269, 2, 60, 63.59, 26.59, 0.50324, 61, 1.05, 27.42, 0.49676, 2, 60, 69.95, 36.62, 0.19323, 61, 9.29, 35.98, 0.80677, 2, 60, 84.72, 52.44, 0.0283, 61, 26.93, 48.51, 0.9717, 2, 60, 96.16, 64.68, 0.00236, 61, 40.59, 58.22, 0.99764, 2, 60, 102.64, 71.62, 8e-05, 61, 48.32, 63.72, 0.99992, 1, 61, 57.52, 70.26, 1, 1, 61, 99.48, 54.69, 1, 1, 61, 104.12, 47.38, 1, 1, 61, 102.07, 39.26, 1, 1, 61, 96.3, 16.3, 1, 1, 61, 72.75, 1.37, 1, 1, 61, 27.33, -6.61, 1, 1, 61, 0.79, -3.4, 1, 1, 60, 43.74, -5.75, 1, 1, 60, 25.62, -7.22, 1, 2, 59, 72.05, -8.85, 0.90146, 60, -7.12, -9.87, 0.09854, 1, 59, 52.11, -13.01, 1, 1, 59, 35.2, -16.54, 1, 2, 58, 70.42, -24.49, 0.03513, 59, 13, -21.17, 0.96487, 2, 58, 55.81, -24.95, 0.38452, 59, -1.31, -24.15, 0.61548, 3, 14, 74.35, -39.07, 0.00709, 58, 33.55, -23.94, 0.91256, 59, -23.4, -27.02, 0.08036, 3, 14, 56.61, -26.14, 0.25013, 58, 11.62, -22.95, 0.74967, 59, -45.17, -29.84, 0.0002, 2, 14, 43.94, -16.91, 0.82016, 58, -4.04, -22.25, 0.17984, 2, 14, 30.02, -16.77, 0.99598, 58, -15.73, -29.81, 0.00402, 1, 14, 14.53, -16.62, 1, 1, 14, -1.62, -16.45, 1, 1, 14, -11.92, -16.35, 1, 1, 14, -14.03, 10.14, 1, 1, 14, -4.49, 9.23, 1, 1, 14, 11.01, 11.3, 1, 1, 14, 31.2, 11.2, 1, 2, 14, 47.1, 10.32, 0.98016, 58, -16.43, 2.21, 0.01984, 1, 58, 4.01, 5.58, 1, 1, 58, 19.53, 6.76, 1, 2, 58, 45.5, 7.13, 0.99951, 59, -17.02, 5.65, 0.00049, 2, 58, 68.96, 2.02, 0.11891, 59, 6.97, 4.69, 0.88109, 3, 58, 98.04, -2.78, 0.0006, 59, 36.45, 5, 0.99743, 60, -40.72, 8.3, 0.00197, 2, 59, 54.46, 4.11, 0.98176, 60, -22.96, 5.17, 0.01824, 2, 59, 80.72, 9.88, 0.04047, 60, 3.81, 7.64, 0.95953, 1, 60, 25.86, 9.67, 1, 2, 60, 47.51, 8.91, 0.99168, 61, -18.24, 13.31, 0.00832, 2, 60, 64.48, 13.6, 0.58933, 61, -0.67, 14.51, 0.41067, 2, 60, 86.55, 25.94, 0.06547, 61, 23.42, 22.18, 0.93453, 2, 60, 105.51, 40.08, 0.0039, 61, 44.83, 32.25, 0.9961, 2, 60, 113.15, 56.35, 6e-05, 61, 55.57, 46.66, 0.99994, 1, 61, 56.99, 60.14, 1], "hull": 42, "edges": [0, 82, 0, 2, 28, 30, 38, 40, 40, 42, 46, 48, 48, 50, 80, 82, 78, 80, 78, 84, 84, 2, 76, 78, 76, 86, 2, 4, 86, 4, 72, 74, 74, 76, 74, 88, 4, 6, 88, 6, 72, 90, 6, 8, 90, 8, 70, 72, 70, 92, 8, 10, 92, 10, 66, 68, 68, 70, 68, 94, 10, 12, 12, 14, 94, 12, 66, 96, 96, 14, 64, 66, 64, 98, 14, 16, 98, 16, 62, 64, 62, 100, 16, 18, 100, 18, 60, 62, 60, 102, 18, 20, 102, 20, 58, 60, 58, 104, 20, 22, 104, 22, 56, 58, 56, 106, 22, 24, 106, 24, 54, 56, 54, 108, 24, 26, 26, 28, 108, 26, 50, 52, 52, 54, 52, 110, 110, 28, 50, 112, 112, 30, 48, 114, 30, 32, 114, 32, 46, 116, 32, 34, 116, 34, 42, 44, 44, 46, 44, 118, 34, 36, 36, 38, 118, 36], "width": 133, "height": 199}}, "a6": {"a6": {"type": "mesh", "uvs": [0.78994, 0, 0.89852, 0.07142, 0.99919, 0.13763, 0.9917, 0.18791, 0.98153, 0.25621, 0.97167, 0.32246, 0.95701, 0.42089, 0.89093, 0.50111, 0.81805, 0.58959, 0.78655, 0.64392, 0.79826, 0.82775, 0.71041, 0.92873, 0.29958, 0.99895, 0.0023, 0.99975, 0.00213, 0.84976, 0.00196, 0.70478, 0.00158, 0.38093, 0.10739, 0.35746, 0.26299, 0.32296, 0.4024, 0.29205, 0.48536, 0.24528, 0.56571, 0.19998, 0.62044, 0.16913, 0.57566, 0.10622, 0.54585, 0.06433, 0.52301, 0.03223, 0.67026, 0, 0.67698, 0.04695, 0.81589, 0.04877, 0.68, 0.0907, 0.83099, 0.09434, 0.69661, 0.15085, 0.83703, 0.15723, 0.68, 0.22103, 0.82646, 0.23014, 0.63923, 0.28118, 0.79173, 0.29394, 0.56804, 0.363, 0.73204, 0.38956, 0.49982, 0.43215, 0.63169, 0.46222, 0.30935, 0.49317, 0.40752, 0.56039, 0.09836, 0.52855, 0.20532, 0.65059, 0.34836, 0.72754, 0.54488, 0.78266, 0.54584, 0.59087, 0.17367, 0.79704, 0.35034, 0.86294, 0.12603, 0.90248, 0.20146, 0.94921], "triangles": [50, 14, 48, 13, 14, 50, 13, 50, 51, 48, 15, 44, 48, 44, 45, 14, 15, 48, 49, 45, 46, 48, 45, 49, 50, 48, 49, 11, 46, 10, 49, 46, 11, 51, 50, 49, 12, 51, 49, 12, 49, 11, 13, 51, 12, 43, 16, 17, 43, 17, 41, 42, 39, 47, 44, 43, 41, 44, 41, 42, 15, 16, 43, 15, 43, 44, 45, 44, 42, 45, 42, 47, 46, 45, 47, 46, 47, 9, 46, 9, 10, 39, 19, 37, 18, 19, 39, 40, 37, 38, 39, 37, 40, 41, 18, 39, 17, 18, 41, 40, 38, 7, 42, 41, 39, 8, 40, 7, 47, 39, 40, 47, 40, 8, 9, 47, 8, 21, 22, 33, 34, 32, 3, 33, 31, 34, 4, 34, 3, 35, 21, 33, 20, 21, 35, 36, 33, 34, 35, 33, 36, 4, 36, 34, 5, 36, 4, 37, 20, 35, 19, 20, 37, 38, 35, 36, 37, 35, 38, 5, 38, 36, 6, 38, 5, 7, 38, 6, 27, 26, 0, 25, 26, 27, 1, 28, 0, 27, 0, 28, 24, 25, 27, 29, 27, 28, 24, 27, 29, 30, 28, 1, 29, 28, 30, 23, 24, 29, 30, 1, 2, 31, 29, 30, 23, 29, 31, 32, 30, 2, 31, 30, 32, 22, 23, 31, 3, 32, 2, 33, 22, 31, 34, 31, 32], "vertices": [1, 8, -8.63, 33.13, 1, 2, 8, 41.36, 73.97, 0.87473, 7, -100.31, 24.9, 0.12527, 2, 8, 87.71, 111.85, 0.54224, 7, -80.21, 81.28, 0.45776, 3, 8, 120.96, 106.92, 0.3595, 7, -49.19, 94.26, 0.63832, 44, -178.59, 94.26, 0.00218, 3, 8, 166.14, 100.23, 0.10059, 7, -7.07, 111.89, 0.86968, 44, -136.46, 111.89, 0.02972, 3, 8, 209.97, 93.74, 0.0091, 7, 33.8, 128.99, 0.87652, 44, -95.59, 128.99, 0.11438, 3, 7, 94.52, 154.39, 0.65536, 44, -34.88, 154.39, 0.3359, 45, -164.27, 154.39, 0.00874, 4, 7, 154.16, 155.85, 0.37636, 44, 24.77, 155.85, 0.55487, 45, -104.63, 155.85, 0.06653, 46, -234.02, 155.85, 0.00224, 4, 7, 219.96, 157.46, 0.11362, 44, 90.56, 157.46, 0.57146, 45, -38.83, 157.46, 0.27655, 46, -168.23, 157.46, 0.03837, 4, 7, 257.87, 163.17, 0.03612, 44, 128.47, 163.17, 0.40348, 45, -0.92, 163.17, 0.4476, 46, -130.32, 163.17, 0.11279, 3, 44, 234.53, 224.5, 0.06828, 45, 105.14, 224.5, 0.46279, 46, -24.26, 224.5, 0.46893, 3, 44, 310.5, 224.69, 0.02289, 45, 181.1, 224.69, 0.35768, 46, 51.7, 224.69, 0.61943, 3, 45, 299.58, 100.49, 0.03574, 46, 170.18, 100.49, 0.86064, 47, 32.32, 104.67, 0.10362, 1, 47, 93.11, 1.79, 1, 3, 45, 267.56, -51.65, 3e-05, 46, 138.17, -51.65, 0.06563, 47, 6.92, -48.71, 0.93434, 4, 44, 311.61, -96.8, 0.00783, 45, 182.21, -96.8, 0.26383, 46, 52.82, -96.8, 0.57872, 47, -76.39, -97.52, 0.14962, 3, 44, 120.96, -197.67, 0.52266, 45, -8.43, -197.67, 0.47722, 46, -137.83, -197.67, 0.00012, 3, 7, 216.68, -167.35, 0.00064, 44, 87.28, -167.35, 0.60185, 45, -42.12, -167.35, 0.39751, 3, 7, 167.14, -122.77, 0.05136, 44, 37.75, -122.77, 0.77911, 45, -91.65, -122.77, 0.16953, 4, 8, 176.61, -133.57, 0.00131, 7, 122.77, -82.83, 0.4035, 44, -6.63, -82.83, 0.57135, 45, -136.02, -82.83, 0.02384, 4, 8, 147.43, -98.49, 0.03908, 7, 79.65, -67.89, 0.84436, 44, -49.75, -67.89, 0.11614, 45, -179.14, -67.89, 0.00042, 3, 8, 119.16, -64.51, 0.26008, 7, 37.89, -53.41, 0.73791, 44, -91.51, -53.41, 0.00201, 2, 8, 99.91, -41.37, 0.76236, 7, 9.45, -43.56, 0.23764, 1, 8, 57.05, -56.93, 1, 1, 8, 28.51, -67.29, 1, 1, 8, 6.64, -75.24, 1, 1, 8, -11.39, -14.91, 1, 1, 8, 19.98, -14, 1, 2, 8, 24.39, 41.68, 0.98363, 7, -98.13, -11.51, 0.01637, 1, 8, 49.13, -14.46, 1, 2, 8, 55.04, 46, 0.91753, 7, -74.13, 8.03, 0.08247, 1, 8, 89.51, -10.1, 1, 2, 8, 96.99, 46.02, 0.61376, 7, -38.23, 29.73, 0.38624, 2, 8, 135.79, -19.44, 0.04983, 7, 28.83, -6.24, 0.95017, 3, 8, 145.23, 38.99, 0.08167, 7, 6.7, 48.65, 0.91116, 44, -122.7, 48.65, 0.00717, 2, 8, 174.85, -38.1, 0.0014, 7, 71.9, -2.03, 0.9986, 3, 8, 186.85, 22.61, 0.00048, 7, 50.79, 56.15, 0.95039, 44, -78.61, 56.15, 0.04913, 3, 7, 133.45, -1.88, 0.23265, 44, 4.06, -1.88, 0.76734, 45, -125.34, -1.88, 1e-05, 3, 7, 118.3, 64.67, 0.60844, 44, -11.09, 64.67, 0.38837, 45, -140.49, 64.67, 0.00319, 2, 44, 57.59, -4.63, 0.99955, 45, -71.81, -4.63, 0.00045, 4, 7, 179.93, 51.6, 0.10894, 44, 50.54, 51.6, 0.86395, 45, -78.86, 51.6, 0.02689, 46, -208.25, 51.6, 0.00023, 2, 44, 129.28, -53.35, 0.52252, 45, -0.11, -53.35, 0.47748, 3, 44, 150.44, 2.45, 0.0038, 45, 21.04, 2.45, 0.99618, 46, -108.35, 2.45, 2e-05, 4, 44, 189.73, -117.35, 0.2534, 45, 60.33, -117.35, 0.69864, 46, -69.06, -117.35, 0.04525, 47, -197.27, -123.32, 0.00271, 4, 44, 241.52, -41.37, 0.01345, 45, 112.13, -41.37, 0.7927, 46, -17.27, -41.37, 0.18399, 47, -148.81, -45.17, 0.00986, 3, 44, 259.98, 33.41, 0.00154, 45, 130.59, 33.41, 0.50586, 46, 1.19, 33.41, 0.4926, 3, 44, 255.55, 120.41, 0.04286, 45, 126.15, 120.41, 0.47313, 46, -3.24, 120.41, 0.48401, 4, 7, 271.82, 61.1, 0.00561, 44, 142.42, 61.1, 0.38625, 45, 13.03, 61.1, 0.58531, 46, -116.37, 61.1, 0.02283, 2, 46, 74.92, -7.07, 0.97689, 47, -58.2, -6.91, 0.02311, 3, 44, 339.35, 76.23, 0.00035, 45, 209.95, 76.23, 0.10233, 46, 80.56, 76.23, 0.89731, 3, 45, 275.35, 8.79, 0.00026, 46, 145.95, 8.79, 0.3518, 47, 12.08, 12.02, 0.64794, 3, 45, 288.71, 50.14, 0.00794, 46, 159.31, 50.14, 0.69922, 47, 23.64, 53.9, 0.29284], "hull": 27, "edges": [0, 52, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 50, 52, 48, 50, 48, 54, 54, 56, 0, 2, 2, 4, 56, 2, 44, 46, 46, 48, 46, 58, 58, 60, 60, 4, 44, 62, 62, 64, 4, 6, 64, 6, 42, 44, 42, 66, 66, 68, 6, 8, 68, 8, 38, 40, 40, 42, 40, 70, 70, 72, 8, 10, 10, 12, 72, 10, 38, 74, 74, 76, 76, 12, 36, 38, 36, 78, 78, 80, 12, 14, 14, 16, 80, 14, 32, 34, 34, 36, 34, 82, 82, 84, 32, 86, 86, 88, 88, 90, 90, 92, 92, 20, 18, 94, 94, 84, 30, 32, 30, 96, 96, 98, 98, 22, 26, 28, 28, 30, 28, 100, 100, 102, 102, 24], "width": 241, "height": 399}}, "a7": {"a7": {"type": "mesh", "uvs": [0.07792, 0, 0.11461, 0.03718, 0.17896, 0.10241, 0.22667, 0.15076, 0.2796, 0.20441, 0.33736, 0.26295, 0.38936, 0.31566, 0.42307, 0.37742, 0.46303, 0.45063, 0.49733, 0.51347, 0.54055, 0.59266, 0.58279, 0.67005, 0.62046, 0.73907, 0.6589, 0.75636, 0.72291, 0.78515, 0.76215, 0.8028, 0.80755, 0.82322, 0.83678, 0.83637, 0.91513, 0.80417, 0.99232, 0.77245, 0.99856, 0.96073, 0.92585, 0.96795, 0.85659, 0.97483, 0.74753, 0.9217, 0.6726, 0.88521, 0.59308, 0.84647, 0.51006, 0.80603, 0.40245, 0.75361, 0.27385, 0.69097, 0.26379, 0.61052, 0.25381, 0.53072, 0.24481, 0.45878, 0.23494, 0.37991, 0.22776, 0.3225, 0.22059, 0.2652, 0.16832, 0.23204, 0.10582, 0.21187, 0.05763, 0.19631, 0.00367, 0.1789, 0.00367, 0.09515, 0.00368, 0, 0.92072, 0.88587, 0.848, 0.89454, 0.78464, 0.88299, 0.71193, 0.85192, 0.66414, 0.82231, 0.59143, 0.79124, 0.54572, 0.73851, 0.49794, 0.67567, 0.43354, 0.58826, 0.3951, 0.5153, 0.3629, 0.45462, 0.33174, 0.37949, 0.30889, 0.31954, 0.28915, 0.27114, 0.23514, 0.21506, 0.17593, 0.17894, 0.10633, 0.13921, 0.04712, 0.1132], "triangles": [39, 40, 0, 1, 39, 0, 58, 39, 1, 2, 58, 1, 57, 58, 2, 56, 57, 2, 38, 39, 58, 38, 58, 57, 3, 56, 2, 37, 38, 57, 36, 37, 57, 55, 56, 3, 56, 36, 57, 35, 36, 56, 4, 55, 3, 35, 56, 55, 34, 35, 55, 54, 4, 5, 55, 4, 54, 34, 55, 54, 53, 54, 5, 53, 5, 6, 33, 34, 54, 33, 54, 53, 52, 53, 6, 32, 33, 53, 32, 53, 52, 52, 6, 7, 51, 52, 7, 51, 7, 8, 31, 32, 52, 31, 52, 51, 50, 51, 8, 50, 8, 9, 30, 31, 51, 30, 51, 50, 49, 50, 9, 49, 9, 10, 29, 30, 50, 29, 50, 49, 28, 29, 49, 48, 49, 10, 48, 10, 11, 28, 49, 48, 47, 48, 11, 47, 11, 12, 27, 28, 48, 27, 48, 47, 26, 27, 47, 26, 47, 46, 46, 47, 12, 46, 12, 13, 45, 13, 14, 46, 13, 45, 25, 46, 45, 26, 46, 25, 44, 45, 14, 44, 14, 15, 16, 44, 15, 43, 16, 17, 43, 44, 16, 24, 45, 44, 25, 45, 24, 23, 44, 43, 24, 44, 23, 41, 18, 19, 17, 18, 41, 42, 43, 17, 41, 42, 17, 41, 19, 20, 21, 41, 20, 42, 41, 21, 22, 42, 21, 23, 43, 42, 22, 23, 42], "vertices": [1, 74, 65.61, -36.78, 1, 1, 74, 49.7, -31.61, 1, 2, 73, 103.96, -9.01, 0.01289, 74, 21.8, -22.53, 0.98711, 2, 73, 82.38, -11.77, 0.43329, 74, 1.11, -15.79, 0.56671, 1, 73, 58.44, -14.84, 1, 2, 72, 124.97, -10.8, 0.00385, 73, 32.31, -18.19, 0.99615, 2, 72, 102.43, -18.16, 0.33784, 73, 8.79, -21.2, 0.66216, 2, 72, 77.99, -20.05, 0.99941, 73, -15.57, -18.48, 0.00059, 1, 72, 49.03, -22.3, 1, 2, 71, 92.76, -28.78, 0.06568, 72, 24.17, -24.22, 0.93432, 2, 71, 61.56, -24.95, 0.77591, 72, -7.17, -26.65, 0.22409, 2, 70, 89.97, -31.28, 0.0168, 71, 31.08, -21.22, 0.9832, 2, 70, 66.46, -17.22, 0.69865, 71, 3.89, -17.88, 0.30135, 2, 70, 54.7, -18.34, 0.99194, 71, -6.4, -23.67, 0.00806, 1, 70, 35.13, -20.2, 1, 2, 15, 47.65, -33.2, 0.01152, 70, 23.12, -21.35, 0.98848, 2, 15, 36.62, -24.65, 0.21876, 70, 9.23, -22.67, 0.78124, 2, 15, 29.53, -19.15, 0.61662, 70, 0.3, -23.52, 0.38338, 2, 15, 8.37, -29.33, 0.98769, 70, -8.15, -45.43, 0.01231, 2, 15, -12.48, -39.35, 1, 70, -16.48, -67.01, 0, 1, 15, -8.12, 30.38, 1, 1, 15, 10.8, 31.45, 1, 1, 15, 28.82, 32.47, 1, 2, 15, 55.17, 10.43, 0.51175, 70, -1.28, 15.59, 0.48825, 1, 70, 22.28, 16.96, 1, 2, 70, 47.29, 18.41, 0.94203, 71, -28.07, 6.93, 0.05797, 2, 70, 73.4, 19.93, 0.12828, 71, -4.81, 18.89, 0.87172, 2, 71, 25.33, 34.4, 0.98992, 72, -54.42, 24.36, 0.01008, 2, 71, 61.35, 52.92, 0.69711, 72, -22.78, 49.65, 0.30289, 2, 71, 88.97, 41.31, 0.25249, 72, 6.59, 43.73, 0.74751, 3, 71, 116.37, 29.79, 0.00484, 72, 35.72, 37.87, 0.99425, 73, -46.24, 46.34, 0.00092, 2, 72, 61.99, 32.58, 0.91203, 73, -21.43, 36.22, 0.08797, 2, 72, 90.78, 26.78, 0.23431, 73, 5.76, 25.13, 0.76569, 1, 73, 25.56, 17.06, 1, 2, 73, 45.32, 9, 0.99475, 74, -23.5, 18.84, 0.00525, 2, 73, 62.42, 15.38, 0.53489, 74, -5.31, 17.29, 0.46511, 2, 73, 76.44, 26.31, 0.01029, 74, 12.03, 21.18, 0.98971, 1, 74, 25.41, 24.17, 1, 1, 74, 40.39, 27.53, 1, 1, 74, 59.31, 2.88, 1, 1, 74, 80.81, -25.12, 1, 1, 15, 9.52, 1, 1, 1, 15, 28.48, 2.6, 1, 2, 15, 44.41, -3.07, 0.12742, 70, 0.12, -1.61, 0.87258, 1, 70, 21.98, 0.98, 1, 1, 70, 38.47, 0.01, 1, 2, 70, 60.33, 2.61, 0.95544, 71, -9.74, -2.24, 0.04456, 2, 70, 81.72, -5.42, 0.0094, 71, 13.06, -0.92, 0.9906, 1, 71, 39.44, -0.88, 1, 1, 72, 2.18, -0.61, 1, 1, 72, 30.94, 1.28, 1, 2, 72, 54.88, 2.91, 0.99855, 73, -33.97, 8.41, 0.00145, 2, 72, 83.89, 2.77, 0.9542, 73, -5.5, 2.84, 0.0458, 2, 72, 106.9, 2.16, 0.00647, 73, 16.99, -2.07, 0.99353, 1, 73, 35.29, -5.73, 1, 1, 73, 60.16, -2.83, 1, 1, 74, 5.13, 0.47, 1, 1, 74, 28.35, -0.28, 1, 1, 74, 46.34, 1.37, 1], "hull": 41, "edges": [0, 80, 38, 40, 68, 70, 34, 36, 36, 38, 36, 82, 40, 42, 42, 44, 82, 42, 34, 84, 84, 44, 32, 34, 32, 86, 44, 46, 86, 46, 30, 32, 30, 88, 46, 48, 88, 48, 28, 30, 28, 90, 48, 50, 90, 50, 24, 26, 26, 28, 26, 92, 50, 52, 92, 52, 24, 94, 52, 54, 54, 56, 94, 54, 22, 24, 22, 96, 96, 56, 20, 22, 20, 98, 56, 58, 98, 58, 18, 20, 18, 100, 58, 60, 100, 60, 16, 18, 16, 102, 60, 62, 102, 62, 12, 14, 14, 16, 14, 104, 62, 64, 104, 64, 12, 106, 64, 66, 66, 68, 106, 66, 10, 12, 10, 108, 108, 68, 8, 10, 8, 110, 110, 70, 6, 8, 6, 112, 70, 72, 112, 72, 4, 6, 4, 114, 72, 74, 74, 76, 114, 74, 0, 2, 2, 4, 2, 116, 116, 76, 76, 78, 78, 80, 0, 78], "width": 154, "height": 222}}, "a8": {"a8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [266.42, 50.43, 263.45, -55.13, -23.32, -47.07, -20.35, 58.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "height": 163}}, "a9": {"a9": {"type": "mesh", "uvs": [0.47633, 0, 0.62035, 0.01705, 0.71591, 0.17588, 0.79613, 0.30923, 0.88999, 0.46525, 0.96034, 0.58219, 0.98239, 0.61884, 0.85234, 0.79639, 0.77426, 0.90297, 0.70319, 1, 0.70082, 1, 0.25414, 0.94301, 0.12828, 0.8259, 0.01478, 0.72029, 0.02927, 0.58677, 0.04086, 0.47989, 0.05539, 0.34599, 0.06784, 0.23128, 0.07892, 0.12919, 0.20302, 0, 0.35296, 0.10728, 0.38559, 0.22783, 0.45086, 0.37326, 0.47125, 0.48042, 0.49981, 0.60098, 0.51204, 0.70814, 0.49165, 0.82295, 0.4631, 0.91098], "triangles": [13, 24, 25, 7, 25, 6, 25, 12, 13, 26, 25, 7, 26, 12, 25, 8, 26, 7, 27, 12, 26, 27, 26, 8, 11, 12, 27, 10, 27, 8, 11, 27, 10, 9, 10, 8, 23, 22, 4, 15, 22, 23, 24, 23, 4, 24, 4, 5, 14, 15, 23, 24, 14, 23, 6, 25, 24, 6, 24, 5, 13, 14, 24, 21, 20, 2, 17, 18, 21, 21, 2, 3, 22, 21, 3, 16, 17, 21, 22, 16, 21, 22, 3, 4, 15, 16, 22, 20, 19, 0, 20, 0, 1, 18, 19, 20, 20, 1, 2, 18, 20, 21], "vertices": [2, 43, 15.11, 0.67, 0.97716, 103, -18, 4, 0.02284, 2, 43, 15.76, -4.94, 0.92147, 103, -16.41, 9.42, 0.07853, 3, 43, 5, -12.88, 0.32551, 103, -3.41, 12.54, 0.66019, 104, -20.75, 11.98, 0.0143, 4, 43, -4.03, -19.55, 0.00595, 103, 7.5, 15.16, 0.75632, 104, -9.9, 14.84, 0.23657, 105, -31.76, 9.61, 0.00115, 3, 103, 20.27, 18.23, 0.17288, 104, 2.8, 18.19, 0.7431, 105, -19.79, 15.01, 0.08402, 3, 103, 29.84, 20.53, 0.01213, 104, 12.32, 20.7, 0.67526, 105, -10.82, 19.05, 0.3126, 3, 103, 32.84, 21.25, 0.00324, 104, 15.3, 21.49, 0.61068, 105, -8, 20.32, 0.38608, 2, 104, 29.59, 16.31, 0.11183, 105, 6.95, 17.56, 0.88817, 2, 104, 38.18, 13.19, 0.00364, 105, 15.93, 15.91, 0.99636, 1, 105, 24.1, 14.4, 1, 1, 105, 24.11, 14.31, 1, 1, 105, 22.06, -3.16, 1, 2, 104, 31.52, -11.24, 0.08978, 105, 13.39, -9.29, 0.91022, 3, 103, 39.61, -15.81, 0.00119, 104, 22.89, -15.41, 0.46546, 105, 5.56, -14.83, 0.53335, 3, 103, 28.82, -14.84, 0.07578, 104, 12.09, -14.68, 0.84266, 105, -5.21, -15.88, 0.08155, 4, 43, -27.1, 2.42, 0.00061, 103, 20.19, -14.06, 0.37472, 104, 3.44, -14.09, 0.62432, 105, -13.84, -16.73, 0.00034, 3, 43, -16.75, 5.73, 0.08392, 103, 9.38, -13.08, 0.83013, 104, -7.4, -13.35, 0.08595, 3, 43, -7.89, 8.57, 0.51229, 103, 0.11, -12.24, 0.48769, 104, -16.68, -12.72, 3e-05, 2, 43, -0.01, 11.1, 0.95068, 103, -8.14, -11.5, 0.04932, 1, 43, 11.45, 10.38, 1, 2, 43, 5.33, 1.98, 0.995, 103, -9.5, -1.02, 0.005, 2, 43, -3.37, -2.63, 0.00571, 103, 0.3, -0.17, 0.99429, 2, 103, 12.17, 1.85, 0.93805, 104, -4.93, 1.63, 0.06195, 3, 103, 20.88, 2.28, 0.00085, 104, 3.76, 2.26, 0.99593, 105, -16.22, -0.55, 0.00323, 2, 104, 13.54, 3.18, 0.9202, 105, -6.72, 1.97, 0.0798, 2, 104, 22.23, 3.5, 0.07451, 105, 1.79, 3.72, 0.92549, 1, 105, 11.11, 4.33, 1, 1, 105, 18.32, 4.31, 1], "hull": 20, "edges": [0, 38, 0, 2, 18, 20, 20, 22, 36, 38, 36, 40, 40, 2, 34, 36, 34, 42, 2, 4, 42, 4, 32, 34, 32, 44, 4, 6, 44, 6, 30, 32, 30, 46, 6, 8, 46, 8, 26, 28, 28, 30, 28, 48, 8, 10, 10, 12, 48, 10, 26, 50, 50, 12, 22, 24, 24, 26, 24, 52, 12, 14, 52, 14, 22, 54, 14, 16, 16, 18, 54, 16], "width": 22, "height": 48}}, "b5": {"b5": {"type": "mesh", "uvs": [0.66815, 0.086, 0.78456, 0.17644, 0.84014, 0.21961, 0.88561, 0.25493, 0.90352, 0.26885, 0.93853, 0.29605, 0.96694, 0.31812, 0.99318, 0.3385, 0.989, 0.36541, 0.98449, 0.39442, 0.9814, 0.41432, 0.97676, 0.44419, 0.97198, 0.47501, 0.96667, 0.50922, 0.95298, 0.59738, 0.92996, 0.67646, 0.94985, 0.76613, 0.96965, 0.85536, 0.98206, 0.91131, 0.99354, 0.96304, 0.79004, 0.9978, 0.62911, 0.93378, 0.30704, 0.80567, 0.33273, 0.76139, 0.36888, 0.69907, 0.4081, 0.63146, 0.40131, 0.59871, 0.39658, 0.5759, 0.35451, 0.53276, 0.3358, 0.51358, 0.25721, 0.43302, 0.17622, 0.34999, 0.11933, 0.29168, 0.07216, 0.24331, 0.01001, 0.27182, 0.01631, 0.18556, 0.2287, 0.1463, 0.3021, 0.10202, 0.31333, 0, 0.33463, 0, 0.41765, 0.00631, 0.66307, 0.40302, 0.66372, 0.36521, 0.73124, 0.39043, 0.73239, 0.43619, 0.66944, 0.44793, 0.58933, 0.42857, 0.59161, 0.39219, 0.53668, 0.3787, 0.66029, 0.33646, 0.76329, 0.37577, 0.7713, 0.43326, 0.73926, 0.46318, 0.67516, 0.47257, 0.5424, 0.45027, 0.49319, 0.37401, 0.65571, 0.31945, 0.78847, 0.35934, 0.8125, 0.43795, 0.75871, 0.48371, 0.66143, 0.50601, 0.48174, 0.49955, 0.44626, 0.43854, 0.38217, 0.42622, 0.47259, 0.35875, 0.59848, 0.31241, 0.70378, 0.31123, 0.79763, 0.34937, 0.83654, 0.44147, 0.78389, 0.49075, 0.66486, 0.51657, 0.51035, 0.52126, 0.37988, 0.50366, 0.95475, 0.33903, 0.96208, 0.36251, 0.90711, 0.35546, 0.88329, 0.32447, 0.90986, 0.3038, 0.88604, 0.2869, 0.85305, 0.32447, 0.8897, 0.3672, 0.95841, 0.38317, 0.94192, 0.41464, 0.85947, 0.38082, 0.82557, 0.3301, 0.85214, 0.28408, 0.86771, 0.26388, 0.80541, 0.27938, 0.80175, 0.33198, 0.84664, 0.40524, 0.89153, 0.43859, 0.95567, 0.42732, 0.49097, 0.2582, 0.64162, 0.23967, 0.54521, 0.29403, 0.68742, 0.26994, 0.62253, 0.65328, 0.80203, 0.66871, 0.76572, 0.57085, 0.59037, 0.59797, 0.61549, 0.72988, 0.79892, 0.75357, 0.61549, 0.81473, 0.8019, 0.84149, 0.61549, 0.88429, 0.80489, 0.9034, 0.80285, 0.95414, 0.44479, 0.55275, 0.56791, 0.56846, 0.69021, 0.55512, 0.7841, 0.52964, 0.87958, 0.50052, 0.86774, 0.47018], "triangles": [72, 61, 71, 11, 112, 90, 69, 68, 112, 70, 60, 59, 69, 59, 58, 11, 91, 10, 10, 82, 9, 11, 90, 91, 90, 82, 91, 112, 68, 90, 53, 54, 45, 54, 46, 45, 52, 44, 51, 58, 57, 68, 69, 58, 68, 58, 52, 51, 67, 89, 68, 8, 74, 7, 80, 75, 81, 89, 84, 83, 82, 80, 81, 89, 83, 82, 90, 89, 82, 9, 82, 81, 83, 80, 82, 81, 75, 74, 81, 74, 8, 9, 81, 8, 75, 73, 74, 90, 68, 89, 58, 51, 50, 44, 43, 51, 45, 44, 52, 45, 41, 44, 61, 54, 60, 62, 54, 61, 60, 54, 53, 53, 45, 52, 59, 53, 52, 58, 59, 52, 91, 82, 10, 59, 60, 53, 71, 61, 60, 72, 62, 61, 70, 59, 69, 71, 60, 70, 33, 35, 36, 40, 37, 39, 37, 38, 39, 40, 0, 37, 92, 37, 0, 92, 36, 37, 69, 112, 111, 12, 112, 11, 103, 101, 16, 101, 15, 16, 110, 69, 111, 28, 72, 107, 108, 70, 109, 20, 106, 19, 20, 21, 106, 106, 18, 19, 106, 105, 18, 106, 21, 105, 22, 104, 21, 21, 104, 105, 105, 17, 18, 104, 103, 105, 105, 103, 17, 104, 22, 102, 22, 23, 102, 104, 102, 103, 103, 16, 17, 102, 101, 103, 102, 23, 100, 23, 24, 100, 102, 100, 101, 101, 97, 15, 101, 100, 97, 100, 24, 96, 100, 96, 97, 96, 24, 25, 15, 97, 14, 96, 98, 97, 97, 98, 14, 25, 99, 96, 96, 99, 98, 25, 26, 99, 26, 108, 99, 26, 107, 108, 26, 27, 107, 99, 109, 98, 99, 108, 109, 14, 98, 13, 27, 28, 107, 98, 111, 13, 98, 110, 111, 98, 109, 110, 13, 111, 12, 109, 70, 110, 108, 71, 70, 70, 69, 110, 107, 71, 108, 111, 112, 12, 107, 72, 71, 95, 93, 2, 28, 29, 72, 29, 30, 72, 30, 31, 63, 64, 31, 94, 31, 92, 94, 31, 32, 92, 94, 92, 93, 92, 32, 36, 32, 33, 36, 34, 35, 33, 93, 92, 0, 93, 1, 2, 93, 0, 1, 94, 93, 95, 72, 63, 62, 54, 62, 48, 48, 62, 55, 54, 48, 46, 46, 41, 45, 62, 63, 55, 41, 43, 44, 43, 50, 51, 46, 47, 41, 46, 48, 47, 63, 64, 55, 63, 31, 64, 50, 57, 58, 57, 67, 68, 89, 88, 84, 41, 42, 43, 41, 47, 42, 47, 48, 42, 89, 67, 88, 43, 42, 50, 83, 84, 80, 80, 84, 79, 48, 49, 42, 48, 56, 49, 48, 55, 56, 57, 50, 49, 55, 65, 56, 55, 64, 65, 80, 79, 75, 75, 79, 76, 50, 42, 49, 74, 73, 7, 57, 56, 66, 57, 66, 67, 64, 94, 65, 75, 76, 73, 67, 66, 88, 76, 77, 73, 73, 6, 7, 73, 77, 6, 57, 49, 56, 84, 88, 87, 88, 66, 87, 84, 85, 79, 84, 87, 85, 76, 79, 78, 76, 78, 77, 78, 79, 85, 56, 65, 66, 77, 5, 6, 66, 65, 95, 65, 94, 95, 66, 95, 87, 77, 78, 5, 78, 4, 5, 78, 85, 4, 85, 86, 4, 85, 87, 86, 86, 87, 2, 87, 95, 2, 86, 3, 4, 86, 2, 3, 72, 30, 63], "vertices": [2, 109, 85.63, -155.47, 0.368, 3, 128.59, -75.53, 0.632, 2, 16, 126.18, -47.56, 0.136, 109, 13.21, -165.69, 0.864, 2, 16, 91.61, -52.44, 0.43575, 109, -21.36, -170.56, 0.56425, 2, 16, 63.32, -56.43, 0.88983, 109, -49.65, -174.55, 0.11017, 2, 16, 52.18, -58, 0.93702, 109, -60.79, -176.13, 0.06298, 2, 16, 30.4, -61.07, 0.93474, 109, -82.57, -179.2, 0.06526, 3, 16, 12.73, -63.56, 0.86705, 109, -100.24, -181.69, 0.02095, 110, 48.99, -69.94, 0.112, 3, 16, -3.59, -65.87, 0.54838, 109, -116.57, -183.99, 0.00362, 110, 32.67, -72.24, 0.448, 3, 16, -19.39, -56.77, 0.28785, 109, -132.36, -174.9, 0.00015, 110, 16.88, -63.14, 0.712, 3, 16, -36.41, -46.97, 0.04762, 109, -149.38, -165.1, 0.00038, 110, -0.14, -53.34, 0.952, 3, 16, -48.08, -40.24, 0.04186, 109, -161.05, -158.37, 0.00614, 110, -11.82, -46.62, 0.952, 2, 2, 308.53, -59.23, 0.424, 110, -29.35, -36.52, 0.576, 2, 2, 288.48, -65.03, 0.888, 110, -47.43, -26.11, 0.112, 1, 2, 266.23, -71.46, 1, 1, 2, 208.88, -88.05, 1, 1, 2, 156.13, -99.45, 1, 1, 2, 101.93, -127.27, 1, 1, 2, 48.01, -154.95, 1, 1, 2, 14.19, -172.31, 1, 1, 2, -17.07, -188.36, 1, 1, 2, -63.89, -130.78, 1, 1, 2, -43.14, -63.42, 1, 1, 2, -1.59, 71.37, 1, 1, 2, 29.51, 73.62, 1, 1, 2, 73.28, 76.78, 1, 1, 2, 120.77, 80.2, 1, 1, 2, 140.62, 90.2, 1, 1, 2, 154.45, 97.17, 1, 4, 16, -27.31, 189.99, 0.03597, 109, -140.29, 71.86, 0.60633, 2, 176.55, 121.08, 0.2857, 110, 8.95, 183.62, 0.072, 2, 16, -12.84, 190.29, 0.056, 109, -125.81, 72.16, 0.944, 2, 16, 47.94, 191.54, 0.184, 109, -65.03, 73.41, 0.816, 2, 16, 110.59, 192.83, 0.01658, 109, -2.38, 74.7, 0.98342, 1, 109, 41.62, 75.61, 1, 2, 109, 78.11, 76.36, 0.808, 3, 121.07, 156.3, 0.192, 1, 109, 69.95, 104.03, 1, 2, 109, 121.61, 77.1, 0.384, 3, 164.57, 157.04, 0.616, 3, 16, 227.02, 117.47, 0.00028, 109, 114.05, -0.65, 0.29572, 3, 157.01, 79.29, 0.704, 3, 16, 243.13, 81.72, 0.00185, 109, 130.16, -36.41, 0.21415, 3, 173.12, 43.53, 0.784, 3, 16, 303.68, 48.68, 0.00048, 109, 190.71, -69.45, 0.32752, 3, 233.67, 10.49, 0.672, 3, 16, 300.52, 42.02, 0.00046, 109, 187.55, -76.11, 0.25554, 3, 230.51, 3.83, 0.744, 3, 16, 284.36, 17.9, 0.00184, 109, 171.39, -100.23, 0.18216, 3, 214.35, -20.29, 0.816, 2, 16, 6.03, 55.99, 0.808, 110, 42.29, 49.62, 0.192, 2, 16, 28.99, 44.85, 0.88172, 109, -83.98, -73.28, 0.11828, 3, 16, 3.59, 31.04, 0.78553, 109, -109.38, -87.09, 0.10247, 110, 39.86, 24.67, 0.112, 3, 16, -24.48, 43.92, 0.18071, 109, -137.45, -74.2, 0.01929, 110, 11.78, 37.55, 0.8, 3, 16, -22.3, 67, 0.17869, 109, -135.27, -51.13, 0.01331, 110, 13.96, 60.62, 0.808, 3, 16, 1.39, 86.44, 0.76548, 109, -111.58, -31.69, 0.05052, 110, 37.65, 80.06, 0.184, 2, 16, 23.23, 75.19, 0.90445, 109, -89.74, -42.93, 0.09555, 2, 16, 39.61, 88.46, 0.82957, 109, -73.36, -29.67, 0.17043, 2, 16, 47.03, 37.6, 0.92187, 109, -65.94, -80.53, 0.07813, 3, 16, 7.78, 16.78, 0.83066, 109, -105.19, -101.35, 0.06534, 110, 44.05, 10.4, 0.104, 3, 16, -28.46, 30.91, 0.0986, 109, -141.44, -87.22, 0.0054, 110, 7.8, 24.54, 0.896, 3, 16, -41.96, 49.59, 0.06589, 109, -154.93, -68.54, 0.00611, 110, -5.7, 43.21, 0.928, 3, 16, -38.17, 72.34, 0.01477, 109, -151.14, -45.79, 0.00123, 110, -1.91, 65.97, 0.984, 3, 16, -4.88, 107.39, 0.16425, 109, -117.85, -10.74, 0.01975, 110, 31.38, 101.01, 0.816, 2, 16, 48.93, 100.7, 0.79975, 109, -64.05, -17.43, 0.20025, 2, 16, 58.09, 34.11, 0.87426, 109, -54.88, -84.02, 0.12574, 3, 16, 14.07, 4.15, 0.83032, 109, -98.91, -113.97, 0.04968, 110, 50.33, -2.22, 0.12, 3, 16, -37.44, 19.39, 0.11739, 109, -150.41, -98.74, 0.00261, 110, -1.18, 13.02, 0.88, 3, 16, -57.36, 49.45, 0.07802, 109, -170.34, -68.68, 0.00998, 110, -21.1, 43.08, 0.912, 3, 16, -56.53, 86.31, 0.10198, 109, -169.5, -31.82, 0.01802, 110, -20.27, 79.94, 0.88, 3, 16, -25.94, 140.61, 0.25894, 109, -138.91, 22.48, 0.02906, 110, 10.33, 134.24, 0.712, 3, 16, 16.53, 134.04, 0.47463, 109, -96.44, 15.92, 0.06936, 110, 52.8, 127.67, 0.456, 3, 16, 33.55, 150.51, 0.7978, 109, -79.42, 32.38, 0.1622, 110, 69.82, 144.14, 0.04, 2, 16, 61.28, 102.72, 0.74571, 109, -51.69, -15.4, 0.25429, 2, 16, 70.87, 49.96, 0.7759, 109, -42.1, -68.17, 0.2241, 2, 16, 55.97, 16.7, 0.8861, 109, -57.01, -101.42, 0.1139, 3, 16, 18.79, -1.6, 0.80916, 109, -94.18, -119.72, 0.05484, 110, 55.05, -7.97, 0.136, 3, 16, -43.15, 12.9, 0.13506, 109, -156.12, -105.23, 0.02494, 110, -6.89, 6.52, 0.84, 2, 2, 255.55, -7.9, 0.448, 110, -29.13, 37.24, 0.552, 4, 16, -63.48, 88.29, 0.00361, 109, -176.45, -29.83, 0.00116, 2, 224.7, 24.48, 0.48483, 110, -27.21, 81.92, 0.5104, 2, 2, 202.86, 73.37, 0.536, 110, -7.16, 131.57, 0.464, 4, 16, -13.33, 173.64, 0.02488, 109, -126.3, 55.51, 0.00621, 2, 198.03, 119.8, 0.49115, 110, 22.93, 167.27, 0.47776, 3, 16, 1.79, -53.7, 0.60978, 109, -111.18, -171.83, 0.00622, 110, 38.05, -60.08, 0.384, 3, 16, -13.62, -49.2, 0.4315, 109, -126.59, -167.33, 0.0005, 110, 22.64, -55.57, 0.568, 3, 16, -1.17, -34.05, 0.42908, 109, -114.14, -152.18, 0.00292, 110, 35.09, -40.43, 0.568, 3, 16, 21.27, -35.58, 0.89137, 109, -91.71, -153.71, 0.04463, 110, 57.53, -41.95, 0.064, 2, 16, 29.93, -49.86, 0.93135, 109, -83.05, -167.99, 0.06865, 2, 16, 43.77, -47.31, 0.95039, 109, -69.2, -165.44, 0.04961, 2, 16, 25.75, -26.13, 0.93425, 109, -87.22, -144.25, 0.06575, 3, 16, -5.75, -25.22, 0.46287, 109, -118.72, -143.34, 0.00113, 110, 30.52, -31.59, 0.536, 2, 16, -25.68, -42.07, 0.16, 110, 10.59, -48.45, 0.84, 3, 16, -42.42, -27.81, 0.11328, 109, -155.39, -145.94, 0.00672, 110, -6.16, -34.18, 0.88, 2, 16, -9.57, -11.82, 0.272, 110, 26.7, -18.2, 0.728, 2, 16, 26.39, -15.91, 0.92373, 109, -86.58, -134.03, 0.07627, 2, 16, 50.52, -37.53, 0.91178, 109, -62.45, -155.66, 0.08822, 2, 16, 60.52, -48.24, 0.9213, 109, -52.45, -166.37, 0.0787, 2, 16, 60.31, -24.28, 0.91596, 109, -52.66, -142.41, 0.08404, 2, 16, 28.78, -7.92, 0.95871, 109, -84.19, -126.04, 0.04129, 2, 16, -22.56, -0.75, 0.12, 110, 13.71, -7.12, 0.88, 3, 16, -49.55, -5.13, 0.03483, 109, -162.52, -123.26, 0.00517, 110, -13.29, -11.5, 0.96, 3, 16, -52.19, -28.44, 0.09498, 109, -165.16, -146.57, 0.01702, 110, -15.93, -34.81, 0.888, 2, 16, 119.87, 67.88, 0.35295, 109, 6.9, -50.25, 0.64705, 2, 16, 108.83, 15.42, 0.42304, 109, -4.14, -102.71, 0.57696, 2, 16, 89.98, 61.29, 0.45304, 109, -22.99, -56.83, 0.54696, 2, 16, 83.57, 9.87, 0.48794, 109, -29.4, -108.26, 0.51206, 1, 2, 133.2, 5.58, 1, 1, 2, 145.39, -56.2, 1, 1, 2, 202.75, -21.11, 1, 1, 2, 164.19, 29.18, 1, 1, 2, 83.97, -10.4, 1, 1, 2, 91.42, -75.42, 1, 1, 2, 30.38, -30.63, 1, 1, 2, 36.27, -97.35, 1, 1, 2, -13.55, -47.21, 1, 1, 2, -2.47, -113.08, 1, 1, 2, -34.75, -124.51, 1, 2, 2, 174.96, 87.09, 0.864, 110, -16.63, 161.18, 0.136, 2, 2, 180.08, 43.49, 0.808, 110, -44.48, 127.24, 0.192, 2, 2, 203.46, 7.08, 0.832, 110, -54.48, 85.15, 0.168, 2, 2, 231.02, -17.24, 0.856, 110, -52.87, 48.43, 0.144, 2, 2, 261.08, -41.2, 0.904, 110, -49.28, 10.16, 0.096, 2, 2, 278.79, -30.13, 0.4, 110, -29.03, 5.08, 0.6], "hull": 41, "edges": [0, 80, 28, 30, 38, 40, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 94, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 96, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 110, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 126, 12, 14, 12, 146, 14, 148, 148, 150, 150, 152, 152, 154, 154, 12, 10, 12, 10, 156, 156, 158, 158, 160, 160, 162, 14, 16, 162, 16, 16, 18, 18, 164, 164, 166, 166, 168, 168, 170, 8, 10, 170, 8, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 18, 20, 182, 20, 64, 66, 64, 184, 184, 186, 0, 2, 186, 2, 62, 64, 62, 188, 188, 190, 2, 4, 190, 4, 60, 62, 60, 126, 58, 60, 58, 144, 4, 6, 6, 8, 6, 172, 50, 192, 192, 194, 194, 30, 26, 28, 26, 196, 196, 198, 50, 52, 52, 54, 198, 52, 48, 50, 48, 200, 200, 202, 30, 32, 202, 32, 44, 46, 46, 48, 46, 204, 204, 206, 32, 34, 206, 34, 44, 208, 208, 210, 34, 36, 36, 38, 210, 36, 40, 42, 42, 44, 42, 212, 212, 38, 54, 56, 56, 58, 54, 214, 214, 142, 56, 214, 214, 216, 216, 218, 218, 220, 220, 222, 24, 26, 222, 24, 138, 224, 20, 22, 22, 24, 224, 22], "width": 207, "height": 405}}}}], "animations": {"animation1": {"slots": {"c5": {"color": [{"color": "ffffff13"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}, {"time": 3.9667, "color": "ffffffff"}, {"time": 5.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffffff"}, {"time": 8.0667, "color": "ffffff00"}, {"time": 8.1667, "color": "ffffff13"}]}, "c4": {"color": [{"color": "ffffffec"}, {"time": 1.2667, "color": "ffffff00"}, {"time": 2.6333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "color": "ffffffff"}, {"time": 6.7333, "color": "ffffff00"}, {"time": 8.0667, "color": "ffffffff"}, {"time": 8.1667, "color": "ffffffec"}]}, "d6": {"color": [{"color": "ffffff06", "curve": 0.28, "c2": 0.13, "c3": 0.754}, {"time": 1.2333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "color": "ffffff00", "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 8.1667, "color": "ffffff06"}]}, "d7": {"color": [{"color": "fffffff4", "curve": 0.288, "c2": 0.16, "c3": 0.755}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "color": "ffffffff", "curve": 0.305, "c3": 0.641, "c4": 0.36}, {"time": 8.1667, "color": "fffffff4"}]}, "d5": {"color": [{"color": "ffffffe4", "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "color": "ffffff00", "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 8.1667, "color": "ffffffe4"}]}, "d4": {"color": [{"color": "ffffff6a", "curve": 0.38, "c2": 0.53, "c3": 0.746}, {"time": 0.6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.4, "color": "ffffffff", "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 8.1667, "color": "ffffff6a"}]}, "d2": {"color": [{"color": "fffffffc", "curve": 0.26, "c2": 0.05, "c3": 0.751}, {"time": 1.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "color": "ffffffff", "curve": 0.326, "c3": 0.659, "c4": 0.34}, {"time": 8.1667, "color": "fffffffc"}]}, "c2": {"color": [{"color": "ffffff88"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "color": "ffffffff"}, {"time": 4.7333, "color": "ffffff00"}, {"time": 6.0667, "color": "ffffffff"}, {"time": 7.4333, "color": "ffffff00"}, {"time": 8.1667, "color": "ffffff88"}]}, "c3": {"color": [{"color": "ffffff31"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "color": "ffffffff"}, {"time": 5.7333, "color": "ffffff00"}, {"time": 7.0667, "color": "ffffffff"}, {"time": 8.1667, "color": "ffffff31"}]}, "c1": {"color": [{"color": "ffffffba"}, {"time": 1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "color": "ffffffff"}, {"time": 3.7, "color": "ffffff00"}, {"time": 5.0667, "color": "ffffffff"}, {"time": 6.4333, "color": "ffffff00"}, {"time": 7.8, "color": "ffffffff"}, {"time": 8.1667, "color": "ffffffba"}]}, "d3": {"color": [{"color": "ffffff8b", "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.6333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "color": "ffffff00", "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8.1667, "color": "ffffff8b"}]}, "d1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "color": "ffffff00"}]}, "d8": {"color": [{"color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.8667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8.1667, "color": "ffffff52"}]}, "c6": {"color": [{"color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff00"}, {"time": 4.0667, "color": "ffffffff"}, {"time": 5.4333, "color": "ffffff00"}, {"time": 6.8, "color": "ffffffff"}, {"time": 8.1667, "color": "ffffff00"}]}}, "bones": {"图层 12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": "stepped"}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.07, "y": 14, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -0.04, "y": -18, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.07, "y": 14, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -0.04, "y": -18, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": -0.04, "y": 22.55, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "x": -0.04, "y": -32, "curve": 0, "c2": 0.22, "c3": 0.778}, {"time": 7.0667, "x": -0.03, "y": -9.16, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "x": -0.03, "y": -21.65, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.03, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -4.84, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 15.95, "curve": 0.25, "c3": 0.75}, {"time": 7.8333}], "scale": [{"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": 1.082, "y": 0.994, "curve": 0.25, "c3": 0.75}, {"time": 6.8333}]}, "图层 27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -10.63, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -10.99, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 26.75, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": 1.082, "y": 0.924, "curve": 0.25, "c3": 0.75}, {"time": 6.8333}]}, "图层 28": {"rotate": [{"angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -1.32}], "scale": [{"x": 1.02, "curve": "stepped"}, {"time": 5.5, "x": 1.02, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 6.3333, "x": 1.08, "curve": 0.323, "c2": 0.3, "c3": 0.677, "c4": 0.7}, {"time": 7.3333, "x": 1.02}]}, "图层 91": {"rotate": [{"angle": -0.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": -0.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": -0.88}], "scale": [{"x": 1.02}]}, "图层 93": {"rotate": [{"angle": -0.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "angle": -0.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": -0.14}], "scale": [{"x": 1.02}]}, "图层 94": {"rotate": [{"angle": 0.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 0.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 0.6}], "scale": [{"x": 1.02}]}, "图层 46": {"rotate": [{"angle": -4.68, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.9333}, {"time": 2.2667, "angle": 10.24, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -8.09, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 8.1667, "angle": -4.68}], "scale": [{"x": 1.028, "y": 1.063, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.9333, "x": 1.04, "y": 1.04}, {"time": 2.2667, "x": 1.04, "y": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": 1.06, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "x": 1.02, "y": 1.08, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 8.1667, "x": 1.028, "y": 1.063}]}, "图层 19": {"rotate": [{"angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.75, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -4.95, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -6}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.074, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.074, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 1.094, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 62": {"rotate": [{"angle": 1.24, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": 6.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": 6.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": -0.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333}, {"time": 7.1667, "angle": -4.95, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": 1.24}], "scale": [{"x": 1.014, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.074, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.017, "y": 1.011, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.074, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 1.017, "y": 1.011, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.094, "y": 1.062, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.014}]}, "图层 63": {"rotate": [{"angle": 3.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": 6.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": 6.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "angle": -2.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667}, {"time": 7.5, "angle": -4.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": 3.37}], "scale": [{"x": 1.037, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.074, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.047, "y": 1.031, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.074, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "x": 1.047, "y": 1.031, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.094, "y": 1.062, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "x": 1.037}]}, "图层 64": {"rotate": [{"angle": 5.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": 6.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.03, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": 6.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": -4.03, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5}, {"time": 7.8333, "angle": -4.95, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 5.5}], "scale": [{"x": 1.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.074, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.076, "y": 1.051, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 1.074, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "x": 1.076, "y": 1.051, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.094, "y": 1.062, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.06}]}, "图层 68": {"rotate": [{"angle": 12.67, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 13.99, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 3.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.1667, "angle": 12.67}]}, "图层 21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -1.19, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 8.1667}], "translate": [{"x": -1.02, "y": 2.64, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -3.66, "y": 1.61, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -0.19, "y": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -3.85, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "x": -0.09, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.41, "y": 3.57, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -2.24, "y": 5.19, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.4, "y": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": -1.24, "y": 2.51}]}, "图层 6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "translate": [{"x": 1.81, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.64, "y": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -0.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.81, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.82, "y": -0.84, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 3.62, "y": -1.72, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -1.79, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 1.81, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 5.4, "y": -2.61, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "x": -1.92, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": 5.46, "y": -2.49, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": 1.81, "y": -0.86}]}, "图层 9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.59, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 14": {"rotate": [{"angle": -0.03, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 7.77, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -0.03}]}, "图层 104": {"rotate": [{"angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -8.37}]}, "图层 103": {"rotate": [{"angle": -4.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.46, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -4.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 3.46, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "angle": -4.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 3.46, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": -4.4}]}, "图层 106": {"rotate": [{"angle": 1.28, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 1.28, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 3.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 1.28, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 3.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 1.28}]}, "图层 4": {"rotate": [{"angle": -12, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -12, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -12, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -12}]}, "图层 79": {"rotate": [{"angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -7.9, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -3.68}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.247, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.207, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.3333, "x": 1.018, "y": 1.06, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 8.1667}]}, "图层 77": {"rotate": [{"angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -3.68}, {"time": 1.6667, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -3.68}, {"time": 4.3333, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "angle": -3.68}, {"time": 7.1667, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": -2.69}], "scale": [{"x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.005}]}, "图层 81": {"rotate": [{"angle": -0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -3.68}, {"time": 2, "angle": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -3.68}, {"time": 4.6667, "angle": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "angle": -0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "angle": -3.68}, {"time": 7.5, "angle": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": -0.99}], "scale": [{"x": 1.014, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.027, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.014, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.027, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "x": 1.014, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.027, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "x": 1.014}]}, "图层 80": {"rotate": [{"angle": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.7, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.7, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 1.7, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 0.7}], "scale": [{"x": 1.022, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "x": 1.027, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.022, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "x": 1.027, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "x": 1.022, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5}, {"time": 7.8333, "x": 1.027, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.022}]}, "图层 83": {"rotate": [{"angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 1.7}], "scale": [{"x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": 1.027}]}, "图层 84": {"rotate": [{"angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -19.9, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -3.68}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 1.292, "y": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 82": {"rotate": [{"angle": -3.35, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -3.68}, {"time": 1.5, "angle": 1.7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -3.35, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "angle": -3.68}, {"time": 4.1667, "angle": 1.7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.5, "angle": -3.35, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "angle": -3.68}, {"time": 7, "angle": 1.7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "angle": -3.35}], "scale": [{"x": 1.002, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.027, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 1.002, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 1.027, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.5, "x": 1.002, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.027, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "x": 1.002}]}, "图层 86": {"rotate": [{"angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -3.68}, {"time": 1.6667, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -3.68}, {"time": 4.3333, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "angle": -3.68}, {"time": 7.1667, "angle": 26.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": -2.69}], "scale": [{"x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.005}]}, "图层 85": {"rotate": [{"angle": -1.88, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -3.68}, {"time": 1.8333, "angle": 1.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.88, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "angle": -3.68}, {"time": 4.5, "angle": 1.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "angle": -1.88, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "angle": -3.68}, {"time": 7.3333, "angle": 1.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": -1.88}], "scale": [{"x": 1.009, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.027, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.009, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.027, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "x": 1.009, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.027, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "x": 1.009}]}, "图层 59": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 56": {"rotate": [{"angle": 2.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 7.63, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "angle": 7.63, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "angle": 2.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6}, {"time": 7.3333, "angle": 7.63, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": 2.56}], "scale": [{"x": 1.018, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.052, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.018, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.052, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "x": 1.018, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.052, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "x": 1.018}]}, "图层 61": {"rotate": [{"angle": 6.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": 7.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 6.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": 7.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 6.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5}, {"time": 7.8333, "angle": 7.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 6.22}], "scale": [{"x": 1.043, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.052, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.043, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 1.052, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "x": 1.043, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.052, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.043}]}, "图层 60": {"rotate": [{"angle": 7.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 7.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.5, "angle": 7.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "angle": 7.15}], "scale": [{"x": 1.049, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 1.049, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.5, "x": 1.049, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "x": 1.049}]}, "图层 88": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.02, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 2.02, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 87": {"rotate": [{"angle": 0.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 2.02, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": 2.66, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 2.02, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": 0.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.7667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.1667, "angle": 2.31, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 7.1, "angle": -0.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 7.5, "angle": 2.2, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 8.1667, "angle": 0.26}]}, "图层 90": {"rotate": [{"angle": 0.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 3.22, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 0.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 3.22, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": 0.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.0333, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 3.22, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1667, "angle": 0.74}]}, "图层 89": {"rotate": [{"angle": 1.28, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 3.22, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 1.28, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 3.22, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": 1.28, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": 3.22, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.1667, "angle": 1.28}]}, "图层 92": {"rotate": [{"angle": 1.76, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 3.22, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 1.76, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 3.22, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": 1.76, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": 3.22, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1667, "angle": 1.76}]}, "图层 25": {"translate": [{"x": -0.43, "y": -0.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": -2.33, "y": -4.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -0.43, "y": -0.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "x": -2.33, "y": -4.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": -0.43, "y": -0.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333}, {"time": 7.1667, "x": -2.33, "y": -4.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": -0.43, "y": -0.85}], "scale": [{"x": 0.982, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.9, "y": 1.001, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.982, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 0.9, "y": 1.001, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 0.982, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 0.9, "y": 1.001, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 0.982}], "shear": [{}, {"time": 1.3333, "x": 12}, {"time": 2.6667}, {"time": 4, "x": 6}, {"time": 5.5}, {"time": 6.8333, "y": 7.2}, {"time": 8.1667}]}, "图层 26": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "shear": [{}, {"time": 1.3333, "x": 12}, {"time": 2.6667}, {"time": 4, "x": 6}, {"time": 5.5}, {"time": 6.8333, "y": 7.2}, {"time": 8.1667}]}, "图层 24": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "shear": [{}, {"time": 1.3333, "x": 12}, {"time": 2.6667}, {"time": 4, "x": 6}, {"time": 5.5}, {"time": 6.8333, "y": 7.2}, {"time": 8.1667}]}, "图层 42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"time": 5.5}, {"time": 6.3333, "x": 1.18}, {"time": 8.1667}]}, "图层 70": {"rotate": [{"angle": 2.44, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 8.92, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "angle": 2.44, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": 8.92, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 5.5, "angle": 2.44, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 5.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 8.92, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 8.1667, "angle": 2.44}]}, "图层 73": {"rotate": [{"angle": 6.2, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 8.92, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 6.2, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 8.92, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 5.5, "angle": 6.2, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "angle": 8.92, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 8.1667, "angle": 6.2}]}, "图层 72": {"rotate": [{"angle": 8.84, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 8.92, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.6667, "angle": 8.84, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 3.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 8.92, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 5.5, "angle": 8.84, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "angle": 8.92, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 8.1667, "angle": 8.84}]}, "图层 75": {"rotate": [{"angle": 6.76, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 6.76, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0667, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.5, "angle": 6.76, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.9, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "angle": 6.76}]}, "图层 23": {"rotate": [{}, {"time": 1.3333, "angle": -10.8}, {"time": 2.6667}, {"time": 4, "angle": -6}, {"time": 5.5}, {"time": 6.8333, "angle": -3.6}, {"time": 8.1667}], "translate": [{"x": -1.71, "y": -3.61}, {"time": 1.3333, "x": 4.18, "y": 10.18}, {"time": 2.6667, "x": -1.71, "y": -3.61}, {"time": 4, "x": 1.84, "y": 4.66}, {"time": 5.5, "x": -1.71, "y": -3.61}, {"time": 6.8333, "x": -0.42, "y": -0.9}, {"time": 8.1667, "x": -1.71, "y": -3.61}]}, "图层 57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "translate": [{"time": 6.1667}, {"time": 6.6667, "x": 11.85, "y": -1.88}, {"time": 7.0667, "x": 0.78, "y": -0.18}, {"time": 8.1667}]}, "图层 11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.53, "curve": 0.328, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 7.5667, "angle": -0.39, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 8.1667}], "translate": [{"x": 1.07, "y": -1.69}, {"time": 0.5, "x": -0.31, "y": 0.63}, {"time": 1.3333, "x": 3.5, "y": 2.91}, {"time": 2.6667, "x": 3.7, "y": 0.41}, {"time": 4, "x": 3.13, "y": 2.04}, {"time": 5.5, "x": 3.19, "y": 0.86}, {"time": 6.3333, "x": 4.42, "y": -3.39}, {"time": 6.6667, "x": 13.27, "y": 0.2}, {"time": 6.8333, "x": 11.61, "y": -1.47}, {"time": 7.1667, "x": 5.28, "y": -3.26}, {"time": 7.4, "x": 9.04, "y": -2.49}, {"time": 7.6667, "x": 0.89, "y": -1.53}, {"time": 7.9333, "x": -0.92, "y": -0.43}, {"time": 8.1667, "x": 1.07, "y": -1.69}], "scale": [{"time": 2.6667}, {"time": 4, "x": 0.98}, {"time": 5.5}, {"time": 6.3333, "x": 0.968}, {"time": 6.8333, "x": 0.96}, {"time": 7.9, "x": 1.012}, {"time": 8.1667}]}, "图层 58": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 1.06, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 15": {"rotate": [{"angle": 4.32, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 4.32, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 4.32, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 4.32}], "translate": [{}, {"time": 1.3333, "x": 6, "y": -18}, {"time": 2.6667}, {"time": 4, "x": 6, "y": -10}, {"time": 5.5}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.3333, "x": 1.059, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6.8333, "x": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 43": {"rotate": [{"angle": 3.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 4.32}, {"time": 1.6667, "angle": -0.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 3.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 4.32}, {"time": 4.3333, "angle": -0.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": 3.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "angle": 4.32}, {"time": 7.1667, "angle": -0.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": 3.35}], "scale": [{"x": 1.007, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.007, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 1.007, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.007}]}, "图层 47": {"rotate": [{"angle": 1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 4.32}, {"time": 2, "angle": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 4.32}, {"time": 4.6667, "angle": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "angle": 1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "angle": 4.32}, {"time": 7.5, "angle": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": 1.69}], "scale": [{"x": 1.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "x": 1.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "x": 1.02}]}, "图层 48": {"rotate": [{"angle": 0.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": 4.32}, {"time": 2.3333, "angle": -0.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": 4.32}, {"time": 5, "angle": -0.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 0.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": 4.32}, {"time": 7.8333, "angle": -0.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 0.04}], "scale": [{"x": 1.033, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.033, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "x": 1.033, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.033}]}, "图层 45": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -9.82, "y": 10.41, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -9.82, "y": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": -23.82, "y": -5.59, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 96": {"rotate": [{"angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 8.69, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 4.46}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 95": {"rotate": [{"angle": 3.93, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 1.5667, "angle": -0.71, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.9, "angle": 4.46}, {"time": 4.2333, "angle": -0.71, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 5.4333}, {"time": 7, "angle": 8.69, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "angle": 3.93}], "scale": [{"x": 1.002, "y": 1.002, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 1.5667, "x": 1.02, "y": 1.02, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 1.02, "y": 1.02, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.04, "y": 1.04, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "x": 1.002, "y": 1.002}]}, "图层 98": {"rotate": [{"angle": 2.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.8, "angle": -0.71, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 3.1333, "angle": 4.46}, {"time": 4.4667, "angle": -0.71, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.6}, {"time": 7.1667, "angle": 8.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": 2.89}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.8, "x": 1.02, "y": 1.02, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "x": 1.02, "y": 1.02, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.04, "y": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.006, "y": 1.006}]}, "图层 97": {"rotate": [{"angle": 1.7, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 2.0333, "angle": -0.71, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "angle": 4.46}, {"time": 4.7, "angle": -0.71, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.7667}, {"time": 7.3333, "angle": 8.69, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": 1.7}], "scale": [{"x": 1.011, "y": 1.011, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 2.0333, "x": 1.02, "y": 1.02, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 1.02, "y": 1.02, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.04, "y": 1.04, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "x": 1.011, "y": 1.011}]}, "图层 100": {"rotate": [{"angle": 0.54, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": 4.46}, {"time": 2.2667, "angle": -0.71, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6, "angle": 4.46}, {"time": 4.9333, "angle": -0.71, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.9333}, {"time": 7.5, "angle": 8.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": 0.54}], "scale": [{"x": 1.015, "y": 1.015, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 1.02, "y": 1.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": 1.02, "y": 1.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "x": 1.015, "y": 1.015}]}, "图层 99": {"rotate": [{"angle": -0.39, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": 4.46}, {"time": 2.5, "angle": -0.71, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.8333, "angle": 4.46}, {"time": 5.1667, "angle": -0.71, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.1}, {"time": 7.6667, "angle": 8.69, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8.1667, "angle": -0.39}], "scale": [{"x": 1.019, "y": 1.019, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.02, "y": 1.02, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.02, "y": 1.02, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 1.04, "y": 1.04, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8.1667, "x": 1.019, "y": 1.019}]}, "图层 102": {"rotate": [{"angle": -0.63, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.4, "angle": 4.46, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.7333, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 4.46, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 5.5, "angle": 7.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.2667}, {"time": 7.8333, "angle": 8.69, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": -0.63}], "scale": [{"x": 1.02, "y": 1.02, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.4, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.7333, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 5.5, "x": 1.033, "y": 1.033, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.04, "y": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.02, "y": 1.02}]}, "图层 101": {"rotate": [{"angle": 0.1, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.6333, "angle": 4.46, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.9667, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 4.46, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.5, "angle": 8.14, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 6.4333}, {"time": 8, "angle": 8.69, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 8.1667, "angle": 0.1}], "scale": [{"x": 1.017, "y": 1.017, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.6333, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.9667, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.5, "x": 1.037, "y": 1.037, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 6.4333, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 1.04, "y": 1.04, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 8.1667, "x": 1.017, "y": 1.017}]}, "图层 29": {"rotate": [{"angle": -4.15, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.15, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.29, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -4.15, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -4.29, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -4.15}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 49": {"rotate": [{"angle": -4.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": -4.15}, {"time": 1.6, "angle": -4.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -4.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": -4.15}, {"time": 4.2667, "angle": -4.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -4.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.7667, "angle": -4.15}, {"time": 7.1, "angle": -4.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.1667, "angle": -4.17}], "scale": [{"x": 1.008, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 1.06, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "x": 1.008, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": 1.06, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "x": 1.008, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "x": 1.06, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.1667, "x": 1.008}]}, "图层 51": {"rotate": [{"angle": -4.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -4.15}, {"time": 1.8667, "angle": -4.29, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -4.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "angle": -4.15}, {"time": 4.5333, "angle": -4.29, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": -4.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.0333, "angle": -4.15}, {"time": 7.3667, "angle": -4.29, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1667, "angle": -4.2}], "scale": [{"x": 1.022, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 1.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 1.022, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "x": 1.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "x": 1.022, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.0333, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "x": 1.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1667, "x": 1.022}]}, "图层 50": {"rotate": [{"angle": -20.8, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": -4.15}, {"time": 2.1333, "angle": -30.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -20.8, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "angle": -4.15}, {"time": 4.8, "angle": -30.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -20.8, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.3, "angle": -4.15}, {"time": 7.6333, "angle": -30.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.1667, "angle": -20.8}], "scale": [{"x": 1.038, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 1.06, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 1.038, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "x": 1.06, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "x": 1.038, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "x": 1.06, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.1667, "x": 1.038}]}, "图层 53": {"rotate": [{"angle": -27.06, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": -4.15}, {"time": 2.4, "angle": -30.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": -27.06, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "angle": -4.15}, {"time": 5.0667, "angle": -30.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -27.06, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.5667, "angle": -4.15}, {"time": 7.9, "angle": -30.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1667, "angle": -27.06}], "scale": [{"x": 1.052, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "x": 1.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 1.052, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": 1.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "x": 1.052, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "x": 1.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1667, "x": 1.052}]}, "图层 76": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 74": {"rotate": [{"angle": 4.87, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 14.51, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 4.87, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 14.51, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "angle": 4.87, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 14.51, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": 4.87}]}, "图层 78": {"rotate": [{"angle": 11.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 14.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 11.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 14.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 11.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 14.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 11.83}]}, "图层 105": {"rotate": [{"angle": 2.08, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": 19.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 28.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -41.55, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 8.1667, "angle": 2.08}]}, "图层 108": {"rotate": [{"angle": -9.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -9.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -28.35, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": -9.52}]}, "图层 109": {"rotate": [{"angle": -18.79, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.3333, "angle": -9.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -18.79, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 3, "angle": -9.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -28.35, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8.1667, "angle": -18.79}]}, "图层 38": {"translate": [{"x": 43.48, "y": -55.23, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.6333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 2}, {"time": 3.3667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8.1667, "x": 43.48, "y": -55.23}]}, "图层 37": {"translate": [{"x": 89.29, "y": -90.62, "curve": 0.312, "c2": 0.26, "c3": 0.757}, {"time": 0.9667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 1}, {"time": 2.5333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": 79.29, "y": -100.72, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 8.1667, "x": 89.29, "y": -90.62}]}, "图层 39": {"translate": [{"x": 171.51, "y": -7.54, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.2333, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 3}, {"time": 4.0333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "x": 79.29, "y": -100.72, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 8.1667, "x": 171.51, "y": -7.54}]}, "图层 40": {"translate": [{"x": 72.27, "y": -91.81, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 4}, {"time": 5.7, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "curve": 0.243, "c3": 0.692, "c4": 0.76}, {"time": 8.1667, "x": 72.27, "y": -91.81}]}, "图层 41": {"translate": [{"x": 1.77, "y": -2.24, "curve": 0.277, "c2": 0.12, "c3": 0.753}, {"time": 1.4333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}, {"time": 6.8667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.314, "c3": 0.648, "c4": 0.35}, {"time": 8.1667, "x": 1.77, "y": -2.24}]}, "图层 30": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 32": {"scale": [{"x": 1.283, "y": 1.126, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 1.3, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}, {"time": 4.0333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 8.1667, "x": 1.283, "y": 1.126}]}, "图层 35": {"scale": [{"x": 1.208, "y": 1.092, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 1.5667, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 4.3333}, {"time": 7.0333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 8.1667, "x": 1.208, "y": 1.092}]}, "图层 34": {"scale": [{"x": 1.462, "y": 1.206, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 0.5667, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}, {"time": 6.0333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.243, "c3": 0.676, "c4": 0.7}, {"time": 8.1667, "x": 1.462, "y": 1.206}]}, "图层 52": {"scale": [{"x": 1.31, "y": 1.138, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 8.1667, "x": 1.31, "y": 1.138}]}, "图层 33": {"scale": [{"x": 1.112, "y": 1.05, "curve": 0.338, "c2": 0.35, "c3": 0.758}, {"time": 1.9667, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 2}, {"time": 4.7, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 8.1667, "x": 1.112, "y": 1.05}]}, "图层 31": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 55": {"scale": [{"x": 1.062, "y": 1.027, "curve": 0.316, "c2": 0.27, "c3": 0.757}, {"time": 2.2, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 8.1667, "x": 1.062, "y": 1.027}]}, "图层 36": {"scale": [{"x": 1.007, "y": 1.003, "curve": 0.266, "c2": 0.07, "c3": 0.752}, {"time": 2.5667, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}, {"time": 8.0333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.322, "c3": 0.655, "c4": 0.34}, {"time": 8.1667, "x": 1.007, "y": 1.003}]}, "图层 22": {"rotate": [{}, {"time": 1.3667, "angle": 8.46}, {"time": 2.7333}, {"time": 4.1333, "angle": 8.46}, {"time": 5.5}, {"time": 6.8333, "angle": -13.2}, {"time": 8.1667}]}, "图层 17": {"translate": [{"time": 1.6667}, {"time": 2.6667, "x": -6, "y": -12}, {"time": 4.3333, "x": 2}, {"time": 5.5, "x": -6}, {"time": 6.5}, {"time": 7, "x": 8}, {"time": 7.3333, "x": -4}, {"time": 7.8333, "x": 6}, {"time": 8.1667}], "scale": [{"time": 5.8333}, {"time": 6.5, "y": 0.98}, {"time": 7}]}, "图层 13": {"translate": [{"x": -13.44, "y": -15.53, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.5667, "x": -15.86, "y": -12.15, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -2.97, "y": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "x": -8.41, "y": -2.68, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "x": 1.44, "y": -7.42, "curve": 0.25, "c3": 0.902, "c4": 0.89}, {"time": 6.1667, "x": -26.22, "y": -8.02, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "x": 27.45, "y": -13.8, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "x": -16.48, "y": -18.31, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 7.3333, "x": 2.81, "y": -16.64, "curve": 0.35, "c2": 0.42, "c3": 0.685, "c4": 0.76}, {"time": 7.3667, "x": 5.17, "y": -16.5, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 7.4, "x": 6.19, "y": -16.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 7.7, "x": -19.41, "y": -15.85, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 8.1, "x": -13.44, "y": -15.53}], "scale": [{"y": 1.043, "curve": 0.318, "c2": 0.19, "c3": 0.652, "c4": 0.53}, {"time": 1.5667, "y": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "y": 1.057, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "y": 1.038, "curve": 0.246, "c3": 0.715, "c4": 0.85}, {"time": 7.3333, "y": 1.04, "curve": "stepped"}, {"time": 7.4, "y": 1.04, "curve": 0.323, "c3": 0.657, "c4": 0.34}, {"time": 8.1, "y": 1.043}]}, "图层 20": {"rotate": [{"angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.17, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -1.31, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 15.1, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 3.8}]}, "图层 65": {"rotate": [{"angle": 5.32, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.4, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 5.32}]}, "图层 67": {"rotate": [{"angle": 8.04, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 8.04}]}, "图层 66": {"rotate": [{"angle": 10.94, "curve": 0.354, "c2": 0.41, "c3": 0.757}, {"time": 1.2, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 10.94}]}, "图层 69": {"rotate": [{"angle": 13.33, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.6, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 13.33}]}, "图层 2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.93, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -8.09, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.04, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.06, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 1.02, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": 1.04, "y": 1.04}]}, "图层 3": {"rotate": [{"angle": -0.61, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.2333}, {"time": 1.5667, "angle": 10.24, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "angle": -8.09, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 8.1667, "angle": -0.61}], "scale": [{"x": 1.039, "y": 1.043, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.2333, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 1.08, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 1.1, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "x": 1.04, "y": 1.1, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 8.1667, "x": 1.039, "y": 1.043}]}, "图层 7": {"rotate": [{"angle": -1.76, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.4667}, {"time": 1.8, "angle": 10.24, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": -8.09, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 8.1667, "angle": -1.76}], "scale": [{"x": 1.036, "y": 1.049, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.4667, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 1.1, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "x": 1.08, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "x": 1.02, "y": 1.08, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 8.1667, "x": 1.036, "y": 1.049}]}, "图层 8": {"rotate": [{"angle": -3.19, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.7}, {"time": 2.0333, "angle": 10.24, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "angle": -8.09, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 8.1667, "angle": -3.19}], "scale": [{"x": 1.032, "y": 1.056, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.7, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 1.08, "y": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 1.06, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "x": 1.04, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "x": 1.02, "y": 1.08, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 8.1667, "x": 1.032, "y": 1.056}]}, "bone": {"translate": [{"x": 0.03, "y": -6}]}, "图层 18": {"translate": [{"x": -15.46, "y": -15.64, "curve": 0.327, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 1.6, "x": -15.86, "y": -12.15, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -5.92, "y": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "x": -12.85, "y": -2.68, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.44, "y": -7.42, "curve": 0.25, "c3": 0.902, "c4": 0.89}, {"time": 6.2, "x": -32.2, "y": -8.02, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 27.45, "y": -13.8, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "x": -18.49, "y": -18.31, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 7.3667, "x": 2.81, "y": -16.64, "curve": 0.35, "c2": 0.42, "c3": 0.685, "c4": 0.76}, {"time": 7.4, "x": 5.17, "y": -16.5, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 7.4333, "x": 6.19, "y": -16.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 7.7333, "x": -19.41, "y": -15.85, "curve": 0.288, "c3": 0.627, "c4": 0.38}, {"time": 8.1667, "x": -15.46, "y": -15.64}], "scale": [{"y": 1.043, "curve": 0.333, "c2": 0.32, "c3": 0.666, "c4": 0.65}, {"time": 1.6, "y": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "y": 1.057, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 1.038, "curve": 0.246, "c3": 0.715, "c4": 0.85}, {"time": 7.3667, "y": 1.04, "curve": "stepped"}, {"time": 7.4333, "y": 1.04, "curve": 0.324, "c3": 0.657, "c4": 0.34}, {"time": 8.1667, "y": 1.043}]}}, "deform": {"default": {"b6": {"b6": [{"offset": 48, "vertices": [-14.98685, -7.31392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, -46.10843, -22.502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 20, "vertices": [-3.71002, -9.69829, -2.22711, -10.14213, -3.71002, -9.69829, -2.22711, -10.14213, -3.71002, -9.69829, -2.22711, -10.14213, -3.71002, -9.69829, -3.71002, -9.69829, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.39767, -6.10722, -3.71002, -9.69829, -2.22711, -10.14213, 0, 0, 0, 0, -8.39767, -6.10722, -3.71002, -9.69829, -2.22711, -10.14213, 0, 0, 0, 0, 0, 0, -24.51302, 3.32494, -22.36237, -10.57818, -20.54126, -13.78578, 0, 0, 0, 0, 0, 0, -3.71002, -9.69829, -2.22711, -10.14213, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.39767, -6.10722, -3.71002, -9.69829, -2.22711, -10.14213], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 48, "vertices": [-14.98685, -7.31392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, -46.10843, -22.502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045], "curve": 0.25, "c3": 0.75}, {"time": 4, "offset": 20, "vertices": [-3.7886, -9.90438, -2.2739, -10.35748, -3.7886, -9.90438, -2.2739, -10.35748, -3.7886, -9.90438, -2.2739, -10.35748, -3.7886, -9.90438, -3.7886, -9.90438, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.57576, -6.23685, -3.7886, -9.90438, -2.2739, -10.35748, 0, 0, 0, 0, -8.57576, -6.23685, -3.7886, -9.90438, -2.2739, -10.35748, 0, 0, 0, 0, 0, 0, -20.84383, -0.21217, -17.35846, -11.5436, -15.44876, -13.9959, 0, 0, 0, 0, 0, 0, -3.7886, -9.90438, -2.2739, -10.35748, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.57576, -6.23685, -3.7886, -9.90438, -2.2739, -10.35748], "curve": 0.25, "c3": 0.75}, {"time": 5.5, "offset": 12, "vertices": [-6.46664, -12.18376, -4.85489, -12.91162, -3.23297, -6.09177, -2.42725, -6.45587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, -46.10843, -22.502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045], "curve": 0.245, "c3": 0.639, "c4": 0.56}, {"time": 6.3333, "offset": 12, "vertices": [-2.30028, -4.33394, -1.72695, -4.59285, -1.15001, -2.16693, -0.86341, -2.29645, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.33104, -2.60167, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.86363, -4.26452, -4.50457, -4.87147, -4.54921, -5.41461, 0, 0, 0, 0, 0, 0, -30.10667, -0.39517, -27.48988, -7.72092, -27.29202, -8.5823, -2.34336, 3.22247, -2.8288, 2.34322, -2.80709, 2.65003, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.35499, -1.1424, -1.80162, -2.88954, -1.56039, -3.40734, 0, 0, 0, 0, 0, 0, -16.40143, -8.00428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.33104, -2.60167, -5.9322, -1e-05, -5.33104, -2.60167, -5.08699, -3.05219, -8.70751, -7.34052, -5.70807, -8.67917, -4.83356, -8.17738], "curve": 0.376, "c2": 0.52, "c3": 0.733, "c4": 0.95}, {"time": 6.8333, "offset": 12, "vertices": [-0.0552, -0.10401, -0.04144, -0.11022, -0.0276, -0.052, -0.02072, -0.05511, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12794, -0.06244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.02329, -6.56248, -6.93189, -7.49649, -7.00058, -8.33231, 0, 0, 0, 0, 0, 0, -37.34342, -0.60811, -34.22722, -7.94024, -34.29244, -8.58329, -3.6061, 4.95892, -4.35311, 3.60589, -4.31971, 4.07802, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.36246, -1.75798, 5.30333, -0.50543, 5.30486, -0.61977, 0, 0, 0, 0, 0, 0, -0.39361, -0.19209, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12794, -0.06244, -0.14236, 0, -0.12794, -0.06244, -0.12208, -0.07325, -10.58743, -2.80649, -9.2947, -5.05956, -8.89028, -5.28579], "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 8.1667, "offset": 48, "vertices": [-14.98685, -7.31392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, -46.10843, -22.502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045]}]}, "b1": {"b1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3667, "offset": 188, "vertices": [-4.86363, -6.69867, -5.13557, -6.22744, -7.11494, -4.08714, -6.76486, -4.18135, -7.26654, 2.3248, -6.28714, 13.65219, -8.60908, -11.85727, -9.09052, -11.02313, -9.38609, -10.44058, -12.59427, -7.23453, -11.97444, -7.40141, -7.15638, 15.53976, -9.79937, -13.49664, -10.34738, -12.54719, -10.68378, -11.88408, -14.33549, -8.23479, -13.63011, -8.42477, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.1834, 10.42484, 13.25766, 9.22161, 13.28677, 8.42543, 13.81918, -7.86461, 2.77326, -17.35742, 13.1834, 10.42484, 13.25766, 9.22161, 13.28677, 8.42543, 13.81918, -7.86461, 2.77326, -17.35742, 13.1834, 10.42484, 13.25766, 9.22161, 13.81918, -7.86461, -6.33263, -14.86802, -4.92192, -6.84972, 0, 0, 0, 0, 0, 0, -3.14348, 6.82594, -4.30441, -5.92853, -4.54517, -5.51138, -6.29698, -3.61711, 13.78973, -8.8292, 1.41801, 16.30345, 2.68985, 16.11494, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.41801, 16.30345, 2.68985, 16.11494, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 2.68985, 16.11494, 3.50359, 15.92866, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.08406, 11.0396, -6.96156, -9.58819, -7.3508, -8.91375, -10.18404, -5.85025, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.78973, -8.8292], "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "offset": 254, "vertices": [13.1834, 10.42484, 13.25766, 9.22161, 13.28677, 8.42543, 13.81918, -7.86461, 2.77326, -17.35742, 13.1834, 10.42484, 13.25766, 9.22161, 13.28677, 8.42543, 13.81918, -7.86461, 2.77326, -17.35742, 13.1834, 10.42484, 13.25766, 9.22161, 13.81918, -7.86461, -14.56822, -27.56375, -17.34148, -10.20633, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.78973, -8.8292, 1.41801, 16.30345, 2.68985, 16.11494, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.41801, 16.30345, 2.68985, 16.11494, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 2.68985, 16.11494, 3.50359, 15.92866, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.78973, -8.8292], "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "b9": {"b9": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 22, "vertices": [0.51399, 10.89006, -2.29042, 10.05635, 1.31198, 3.09145, 0.84331, 3.18387, 0.41226, 3.1499, 1.96455, -2.73613, 2.28705, -2.38457, 2.4606, -2.11324, 2.48323, -1.9967, 5.84641, -16.6076, 7.28172, -15.67747, 7.65034, -15.15858, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.81386, 1.59433, -3.92611, 1.01747, -3.88073, 0.48695, -3.26213, -4.13951, -2.59338, -4.47372, -2.11187, -4.6156, -1.92549, -4.59975, 0.81481, -16.40337, 2.34507, -15.95279, 2.81279, -15.58688], "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "offset": 144, "vertices": [-0.19843, 4.79401, -1.60663, 4.38298, -1.85623, 4.20166, 0.26622, 3.66318, -0.98466, 3.48608, -0.84077, 3.47264, -1.04617, 3.35757, 0.89514, 2.11627, 0.11822, 2.26425, 0.20078, 2.22496, 0.05594, 2.20087, 1.36909, 0.80598, 0.99783, 1.20956, 1.02826, 1.15088, 0.96057, -0.31783, 1.00433, 0.04434, 0.99709, 0.03079, 0.98277, -0.01207, 0.44267, -1.58951, 0.96973, -1.31952, 0.94497, -1.32138, 0.87979, -1.34422], "curve": 0.25, "c3": 0.75}, {"time": 4.3, "offset": 22, "vertices": [9.37019, 2.16277, 8.09812, 4.1898, 10.15575, -5.33968, 10.60973, -3.7464, 10.58436, -2.51712, 10.79507, -10.95479, 12.02413, -9.10481, 12.57254, -7.81618, 12.49226, -7.52472, 15.48215, -22.702, 17.17663, -20.79575, 17.2076, -20.24506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.22617, -6.78992, 6.03122, -5.8508, 6.39721, -5.00862, 5.76491, -12.30934, 7.33447, -11.13055, 8.17092, -10.21852, 8.1902, -9.95206, 10.64616, -22.55948, 12.43681, -21.09172, 12.54312, -20.60262], "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "offset": 22, "vertices": [-16.1006, -11.20377, -15.49007, -8.83768, -18.53335, -0.91385, -17.86331, -0.87326, -16.85789, 0.67871, -20.56922, 6.00851, -19.82345, 5.79971, -19.06299, 6.05878, -18.13937, 7.11327, -27.77257, 22.07199, -26.39537, 21.99057, -24.24246, 23.07008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.69475, 22.31448, -5.40277, 19.49809, -5.51514, 19.19167, -3.38212, 18.19124, -3.47754, 15.80014, -1.93121, 15.74301, -2.05374, 15.51978, 2.4059, 11.70961, 1.76744, 10.30365, 2.67825, 9.89214, 2.55054, 9.79073, 6.60641, 5.95035, 5.60439, 5.39232, 5.96762, 4.74307, 6.64098, -0.21246, 5.95163, 0.40585, 5.81207, 0.00803, 5.6716, -0.53111, 6.008, -7.56055, 6.0388, -6.21307, 5.47136, -6.43092, 4.7417, -6.78426, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.80332, 8.07043, -5.89777, 6.69873, -5.30054, 6.88654, -4.53705, 7.21222, 0, 0, 0, 0, 0, 0, -7.6796, 13.39067, -7.09358, 11.48135, -5.86142, 11.86317, -5.89413, 11.65361, 0, 0, 0, 0, -7.9556, 18.20882, -5.84314, 15.99944, -5.91568, 15.73549, -8.61775, 2.11711, -7.25925, 2.26825, -7.18137, 2.17389, -9.10162, 21.66512, -6.64087, 19.0173, -6.73138, 18.70561, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -11.97422, 2.15918, -11.54022, 2.08685, -10.66826, 2.92935, -14.13707, 9.4921, -13.62271, 9.15521, -12.98486, 9.16901, -12.03162, 9.74562, -21.04904, 23.59836, -19.85191, 23.31812, -17.79683, 23.93983], "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "a6": {"a6": [{"vertices": [-14.48491, -17.44824, -20.84433, -11.37363, -11.93087, -17.51558, -27.29558, -6.12274, -19.10181, -15.90701, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.05769, 5.30163, 0.10284, 5.10716, 0.14102, 5.05815, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0, 0, 0, 0, 0, 0, -1.13412, -11.28604, 0.26914, -14.17492, -7.81256, -17.29392, -8.37496, -13.03518, -16.12007, -13.08112, -7.52743, -16.98037], "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "vertices": [-4.93921, -6.62608, 4.57322, -10.71581, 7.33935, -6.74633, 11.69221, -13.77618, 13.71773, -6.33038, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16.79266, 13.888, 16.60019, 12.87276, 16.54443, 12.62453, 17.05372, 15.75657, 16.79266, 13.888, 16.60019, 12.87276, 17.05372, 15.75657, 16.79266, 13.888, 16.60019, 12.87276, 26.36754, 6.40346, 17.05372, 15.75657, 16.79266, 13.888, 16.60019, 12.87276, 26.36754, 6.40346, 17.05372, 15.75657, 16.79266, 13.888, 16.60019, 12.87276, 26.36754, 6.40346, 17.05372, 15.75657, 16.79266, 13.888, 0, 0, 0, 0, 0, 0, 20.3549, -17.50133, 14.41412, -14.94707, 0.58894, -9.00293, 10.51009, -13.26855, 4.37238, -10.62959, 7.15947, -6.75823], "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "vertices": [-4.93921, -6.62608, 4.57322, -10.71581, 7.33935, -6.74633, 11.69221, -13.77618, 13.71773, -6.33038, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43.63794, 17.25904, 42.57242, 15.31829, 42.29742, 14.84757, 45.40331, 20.89504, 43.63794, 17.25904, 42.57242, 15.31829, 45.40331, 20.89504, 43.63794, 17.25904, 42.57242, 15.31829, 59.65632, -3.92261, 45.40331, 20.89504, 43.63794, 17.25904, 42.57242, 15.31829, 59.65632, -3.92261, 45.40331, 20.89504, 43.63794, 17.25904, 42.57242, 15.31829, 59.65632, -3.92261, 45.40331, 20.89504, 43.63794, 17.25904, 0, 0, 0, 0, 0, 0, 20.3549, -17.50133, 14.41412, -14.94707, 0.58894, -9.00293, 10.51009, -13.26855, 4.37238, -10.62959, 7.15947, -6.75823], "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "vertices": [-19.11447, -23.02492, -27.50644, -15.00878, -15.74413, -23.11378, -36.01959, -8.07965, -25.20699, -20.99109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07613, 6.9961, 0.13571, 6.73947, 0.1861, 6.6748, -0.48517, 7.44502, -0.07613, 6.9961, 0.13571, 6.73947, -0.48517, 7.44502, -0.07613, 6.9961, 0.13571, 6.73947, 3.23737, 7.66203, -0.48517, 7.44502, -0.07613, 6.9961, 0.13571, 6.73947, 3.23737, 7.66203, -0.48517, 7.44502, -0.07613, 6.9961, 0.13571, 6.73947, 3.23737, 7.66203, -0.48517, 7.44502, -0.07613, 6.9961, 0, 0, 0, 0, 0, 0, -1.4966, -14.8932, 0.35517, -18.70541, -10.30955, -22.82128, -11.0517, -17.20139, -21.27224, -17.26202, -9.93329, -22.40751], "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8.1667, "vertices": [-14.48491, -17.44824, -20.84433, -11.37363, -11.93087, -17.51558, -27.29558, -6.12274, -19.10181, -15.90701, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.05769, 5.30163, 0.10284, 5.10716, 0.14102, 5.05815, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0, 0, 0, 0, 0, 0, -1.13412, -11.28604, 0.26914, -14.17492, -7.81256, -17.29392, -8.37496, -13.03518, -16.12007, -13.08112, -7.52743, -16.98037]}]}, "a2": {"a2": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "offset": 32, "vertices": [5.27995, -2.37932, -0.20233, 5.47259, 5.27995, -2.37932, -0.20233, 5.47259, 5.27995, -2.37932, -0.20233, 5.47259, 5.27995, -2.37932, 5.76126, 0.58775, -0.20233, 5.47259, 5.76126, 0.58775, -0.20233, 5.47259, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.18359, 10.60225, 17.14073, 2.5423, 12.25551, 12.02228, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.97748, -7.82753, 2.86075, -7.86539, -7.92193, -2.54422, 0, 0, 0, 0, -15.84479, -12.90329, -11.65161, 16.30197, -9.15746, 4.1434, -9.69513, -2.45001, -5.69476, -8.20079, -7.6917, 5.99808, 1.07319, -12.13013, 8.36844, -8.82918, 12.04413, -1.19238, -1.96075, -11.96152, -5.52773, -14.23526, 4.54108, -14.58051, 0.47482, -15.26404, -3.41763, -6.19897, 1.19153, -6.97752, -0.7186, -7.04196, 5.13565, -1.94594, 5.40508, 0.37177, 3.97437, 3.65016, 4.80627, 2.45395, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 3.51801, -0.63331, -3.5676, -0.20605, -2.97055, -1.98706, -2.10367, -2.88957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 0.47533, -2.74704, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 0.47533, -2.74704, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 3.51801, -0.63331, -3.5676, -0.20605, -2.97055, -1.98706, -2.10367, -2.88957, -2.93555, -1.49153, -2.01924, -2.57883, -2.86995, -2.0408, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 0.47533, -2.74704, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 0.47533, -2.74704, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 0, 0, 0, 0, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.82878, 8.96931, -8.12372, 5.32765, -9.58005, -1.29387, -6.07758, 7.53568, -6.4023, 7.3067, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.45575, -2.94415, -1.70258, 7.79825, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741], "curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 32, "vertices": [10.55991, -4.75864, -0.40466, 10.94518, 10.55991, -4.75864, -0.40466, 10.94518, 10.55991, -4.75864, -0.40466, 10.94518, 10.55991, -4.75864, 11.52252, 1.17549, -0.40466, 10.94518, 11.52252, 1.17549, -0.40466, 10.94518, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -0.77354, 2.38496, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -0.77354, 2.38496, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, 0.73573, 2.4015, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -0.77354, 2.38496, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -0.77354, 2.38496, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0, 0, 0, 0, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015], "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "offset": 374, "vertices": [0.72495, -1.18977, -0.97672, 0.99366, -1.34164, 0.37579, -1.38959, -0.10298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.44766, -1.64303, 4.3269, 6.28183, 1.93004, 7.37836, 5.13211, 5.51628, 2.47331, 7.09125, -6.41434, 3.89839, 4.09923, 6.35747, -10.27209, -1.37304, 3.45789, 9.76978, 7.56064, 6.86684, 4.13321, 9.29221, -8.23007, 5.96898, 6.2585, 8.12732, -9.7012, 2.36376, 9.98477, -0.09483, 8.70749, 4.88868, 0, 0, 0, 0, 0, 0, 0, 0, 3.3381, -1.46861, -3.58453, 0.67104, -3.44272, -1.2019, -2.82951, -2.30136, 4.40347, -2.61809, -4.97595, -0.28139, -4.36367, -2.22687, 1.31393, -4.68225, -4.8522, -1.17926, -3.26973, -3.6992, -2.67062, -2.12383, 0.5051, 3.37487, -0.67346, 3.34532, 1.00684, 3.25217, -0.35382, 3.38437, -3.36093, 0.38058, 0.43166, 3.38313, -1.50348, 3.04228, 0, 0, 0, 0, 0, 0, 0, 0, -2.34692, -1.29395, 1.99103, 1.79379, 0.83414, 2.5469, -2.65387, 0.37302, 2.66919, 0.24037, 2.19547, 1.53701, -3.41156, 0.47955, 3.43109, 0.30897, 2.8222, 1.9758, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.36176, -0.33191, -2.37549, -0.21397, -1.95367, -1.36777, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.55118, -0.30913, 1.65521, 1.9659, 0.88785, 2.41164, 1.89352, 1.67899, 1.05215, 2.29068, -2.02295, 1.50288, 1.57574, 1.99561, -6.45523, 1.50781, 5.74991, 3.2994, 4.28476, 5.0582, 6.03363, 2.34721, 4.51344, 4.54086, -3.53616, 5.3204, 5.53633, 3.40324, -7.53766, 0.58011, 5.90887, 4.71597, 3.95245, 6.44443, 6.40482, 3.72081, 4.31594, 5.94931, -4.94489, 5.43004, 5.66586, 4.82242, -9.12238, -0.30893, 8.81296, 2.37575, 6.46185, 6.447, 3.88358, 8.26021, 7.20285, 5.34238, 4.406, 7.7543, -6.68317, 5.90169, 6.17326, 6.56326, -4.63849, -1.15952, 4.25311, 2.18416, 2.60202, 4.01128, 1.08244, 4.65703, 3.11859, 3.54866, 1.42996, 4.49234, -4.091, 2.33643, 2.46066, 4.05786], "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "offset": 114, "vertices": [1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, -0.45934, -1.07793, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -3.06873, -0.28802, 2.92313, 0.97851, 2.04813, 2.30341, 1.18016, 2.84702, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, -0.45934, -1.07793, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -1.9801, 0.15877, 1.96466, 0.29585, 1.55655, 1.23432, 1.0661, 1.67584, 1.7471, 1.00015, 1.18339, 1.60907, 1.49287, 1.26189, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 1.08862, 0.44678, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 1.08862, 0.44678, -0.95847, -0.68266, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793], "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "b5": {"b5": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "offset": 12, "vertices": [-3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -3e-05, -3.06289, -5e-05, -3.06281, 0, -3.06289, -3e-05, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281], "curve": 0.25, "c3": 0.75}, {"time": 1, "offset": 12, "vertices": [0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 12, "vertices": [1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70617, 9e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70617, 9e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70617, 9e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626], "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "offset": 12, "vertices": [-2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44203, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44203, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842], "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "offset": 12, "vertices": [3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88753, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88753, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88753, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239], "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "offset": 5, "vertices": [2.0804, -1e-05, 2.22456, 1.78104, 0.36111, 1.78125, 0.28282, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97247, 2.77155, 4.97248, 2.77154, 4.97251, 2.82317, 4.97247, 2.77155, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97247, 2.77155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8e-05, 3.79059, 1e-05, 4.05337], "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "offset": 12, "vertices": [-2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51761, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51761, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51761, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.16431, 1.76371, 3.16433, 1.79657, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743], "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}}}}, "animation2": {"slots": {"c5": {"color": [{"color": "ffffff13"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}, {"time": 3.9667, "color": "ffffffff"}, {"time": 5.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffffff"}, {"time": 8.0667, "color": "ffffff00"}, {"time": 8.1667, "color": "ffffff13"}], "attachment": [{"name": null}]}, "c4": {"color": [{"color": "ffffffec"}, {"time": 1.2667, "color": "ffffff00"}, {"time": 2.6333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "color": "ffffffff"}, {"time": 6.7333, "color": "ffffff00"}, {"time": 8.0667, "color": "ffffffff"}, {"time": 8.1667, "color": "ffffffec"}], "attachment": [{"name": null}]}, "d6": {"color": [{"color": "ffffff06", "curve": 0.28, "c2": 0.13, "c3": 0.754}, {"time": 1.2333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "color": "ffffff00", "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 8.1667, "color": "ffffff06"}], "attachment": [{"name": null}]}, "d7": {"color": [{"color": "fffffff4", "curve": 0.288, "c2": 0.16, "c3": 0.755}, {"time": 1.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "color": "ffffffff", "curve": 0.305, "c3": 0.641, "c4": 0.36}, {"time": 8.1667, "color": "fffffff4"}], "attachment": [{"name": null}]}, "d5": {"color": [{"color": "ffffffe4", "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "color": "ffffff00", "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 8.1667, "color": "ffffffe4"}], "attachment": [{"name": null}]}, "d4": {"color": [{"color": "ffffff6a", "curve": 0.38, "c2": 0.53, "c3": 0.746}, {"time": 0.6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.4, "color": "ffffffff", "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 8.1667, "color": "ffffff6a"}], "attachment": [{"name": null}]}, "d2": {"color": [{"color": "fffffffc", "curve": 0.26, "c2": 0.05, "c3": 0.751}, {"time": 1.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "color": "ffffffff", "curve": 0.326, "c3": 0.659, "c4": 0.34}, {"time": 8.1667, "color": "fffffffc"}], "attachment": [{"name": null}]}, "bg": {"attachment": [{"name": null}]}, "c2": {"color": [{"color": "ffffff88"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "color": "ffffffff"}, {"time": 4.7333, "color": "ffffff00"}, {"time": 6.0667, "color": "ffffffff"}, {"time": 7.4333, "color": "ffffff00"}, {"time": 8.1667, "color": "ffffff88"}], "attachment": [{"name": null}]}, "c3": {"color": [{"color": "ffffff31"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "color": "ffffffff"}, {"time": 5.7333, "color": "ffffff00"}, {"time": 7.0667, "color": "ffffffff"}, {"time": 8.1667, "color": "ffffff31"}], "attachment": [{"name": null}]}, "c1": {"color": [{"color": "ffffffba"}, {"time": 1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "color": "ffffffff"}, {"time": 3.7, "color": "ffffff00"}, {"time": 5.0667, "color": "ffffffff"}, {"time": 6.4333, "color": "ffffff00"}, {"time": 7.8, "color": "ffffffff"}, {"time": 8.1667, "color": "ffffffba"}], "attachment": [{"name": null}]}, "d3": {"color": [{"color": "ffffff8b", "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.6333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "color": "ffffff00", "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8.1667, "color": "ffffff8b"}], "attachment": [{"name": null}]}, "d1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "color": "ffffff00"}], "attachment": [{"name": null}]}, "d8": {"color": [{"color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.8667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8.1667, "color": "ffffff52"}], "attachment": [{"name": null}]}, "c6": {"color": [{"color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff00"}, {"time": 4.0667, "color": "ffffffff"}, {"time": 5.4333, "color": "ffffff00"}, {"time": 6.8, "color": "ffffffff"}, {"time": 8.1667, "color": "ffffff00"}], "attachment": [{"name": null}]}}, "bones": {"图层 12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": "stepped"}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.07, "y": 14, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -0.04, "y": -18, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.07, "y": 14, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -0.04, "y": -18, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": -0.04, "y": 22.55, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "x": -0.04, "y": -32, "curve": 0, "c2": 0.22, "c3": 0.778}, {"time": 7.0667, "x": -0.03, "y": -9.16, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "x": -0.03, "y": -21.65, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.03, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -4.84, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 15.95, "curve": 0.25, "c3": 0.75}, {"time": 7.8333}], "scale": [{"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": 1.082, "y": 0.994, "curve": 0.25, "c3": 0.75}, {"time": 6.8333}]}, "图层 27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -10.63, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -10.99, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 26.75, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": 1.082, "y": 0.924, "curve": 0.25, "c3": 0.75}, {"time": 6.8333}]}, "图层 28": {"rotate": [{"angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -1.32}], "scale": [{"x": 1.02, "curve": "stepped"}, {"time": 5.5, "x": 1.02, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 6.3333, "x": 1.08, "curve": 0.323, "c2": 0.3, "c3": 0.677, "c4": 0.7}, {"time": 7.3333, "x": 1.02}]}, "图层 91": {"rotate": [{"angle": -0.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": -0.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": -0.88}], "scale": [{"x": 1.02}]}, "图层 93": {"rotate": [{"angle": -0.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "angle": -0.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": -0.14}], "scale": [{"x": 1.02}]}, "图层 94": {"rotate": [{"angle": 0.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 0.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 0.6}], "scale": [{"x": 1.02}]}, "图层 46": {"rotate": [{"angle": -4.68, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.9333}, {"time": 2.2667, "angle": 10.24, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -8.09, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 8.1667, "angle": -4.68}], "scale": [{"x": 1.028, "y": 1.063, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.9333, "x": 1.04, "y": 1.04}, {"time": 2.2667, "x": 1.04, "y": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": 1.06, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "x": 1.02, "y": 1.08, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 8.1667, "x": 1.028, "y": 1.063}]}, "图层 19": {"rotate": [{"angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.75, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -4.95, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -6}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.074, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.074, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 1.094, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 62": {"rotate": [{"angle": 1.24, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": 6.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": 6.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": -0.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333}, {"time": 7.1667, "angle": -4.95, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": 1.24}], "scale": [{"x": 1.014, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.074, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.017, "y": 1.011, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.074, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 1.017, "y": 1.011, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.094, "y": 1.062, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.014}]}, "图层 63": {"rotate": [{"angle": 3.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": 6.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": 6.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "angle": -2.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667}, {"time": 7.5, "angle": -4.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": 3.37}], "scale": [{"x": 1.037, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.074, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.047, "y": 1.031, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.074, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "x": 1.047, "y": 1.031, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.094, "y": 1.062, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "x": 1.037}]}, "图层 64": {"rotate": [{"angle": 5.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": 6.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.03, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": 6.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": -4.03, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5}, {"time": 7.8333, "angle": -4.95, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 5.5}], "scale": [{"x": 1.06, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.074, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.076, "y": 1.051, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 1.074, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "x": 1.076, "y": 1.051, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.094, "y": 1.062, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.06}]}, "图层 68": {"rotate": [{"angle": 12.67, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 13.99, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 3.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.1667, "angle": 12.67}]}, "图层 21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -1.19, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 8.1667}], "translate": [{"x": -1.02, "y": 2.64, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -3.66, "y": 1.61, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -0.19, "y": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -3.85, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "x": -0.09, "y": -0.14, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.41, "y": 3.57, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -2.24, "y": 5.19, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.4, "y": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": -1.24, "y": 2.51}]}, "图层 6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "translate": [{"x": 1.81, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.64, "y": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -0.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.81, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.82, "y": -0.84, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 3.62, "y": -1.72, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -1.79, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 1.81, "y": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 5.4, "y": -2.61, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "x": -1.92, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": 5.46, "y": -2.49, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": 1.81, "y": -0.86}]}, "图层 9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.59, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 14": {"rotate": [{"angle": -0.03, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 7.77, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -0.03}]}, "图层 104": {"rotate": [{"angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -8.37}]}, "图层 103": {"rotate": [{"angle": -4.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.46, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -4.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 3.46, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "angle": -4.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 3.46, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": -4.4}]}, "图层 106": {"rotate": [{"angle": 1.28, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 1.28, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 3.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 1.28, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 3.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 1.28}]}, "图层 4": {"rotate": [{"angle": -12, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -12, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -12, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -12}]}, "图层 79": {"rotate": [{"angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -7.9, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -3.68}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.247, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.207, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.3333, "x": 1.018, "y": 1.06, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 8.1667}]}, "图层 77": {"rotate": [{"angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -3.68}, {"time": 1.6667, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -3.68}, {"time": 4.3333, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "angle": -3.68}, {"time": 7.1667, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": -2.69}], "scale": [{"x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.005}]}, "图层 81": {"rotate": [{"angle": -0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -3.68}, {"time": 2, "angle": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -3.68}, {"time": 4.6667, "angle": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "angle": -0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "angle": -3.68}, {"time": 7.5, "angle": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": -0.99}], "scale": [{"x": 1.014, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.027, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.014, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.027, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "x": 1.014, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.027, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "x": 1.014}]}, "图层 80": {"rotate": [{"angle": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.7, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.7, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 1.7, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 0.7}], "scale": [{"x": 1.022, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "x": 1.027, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.022, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "x": 1.027, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "x": 1.022, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5}, {"time": 7.8333, "x": 1.027, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.022}]}, "图层 83": {"rotate": [{"angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 1.7}], "scale": [{"x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": 1.027}]}, "图层 84": {"rotate": [{"angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -19.9, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -3.68}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 1.292, "y": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 82": {"rotate": [{"angle": -3.35, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -3.68}, {"time": 1.5, "angle": 1.7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -3.35, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "angle": -3.68}, {"time": 4.1667, "angle": 1.7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.5, "angle": -3.35, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "angle": -3.68}, {"time": 7, "angle": 1.7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "angle": -3.35}], "scale": [{"x": 1.002, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.027, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 1.002, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 1.027, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.5, "x": 1.002, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.027, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "x": 1.002}]}, "图层 86": {"rotate": [{"angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -3.68}, {"time": 1.6667, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -3.68}, {"time": 4.3333, "angle": 1.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": -2.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "angle": -3.68}, {"time": 7.1667, "angle": 26.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": -2.69}], "scale": [{"x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 1.005, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.027, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.005}]}, "图层 85": {"rotate": [{"angle": -1.88, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -3.68}, {"time": 1.8333, "angle": 1.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.88, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "angle": -3.68}, {"time": 4.5, "angle": 1.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "angle": -1.88, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "angle": -3.68}, {"time": 7.3333, "angle": 1.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": -1.88}], "scale": [{"x": 1.009, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.027, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.009, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.027, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "x": 1.009, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.027, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "x": 1.009}]}, "图层 59": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 56": {"rotate": [{"angle": 2.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 7.63, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "angle": 7.63, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "angle": 2.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6}, {"time": 7.3333, "angle": 7.63, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": 2.56}], "scale": [{"x": 1.018, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.052, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.018, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.052, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "x": 1.018, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.052, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "x": 1.018}]}, "图层 61": {"rotate": [{"angle": 6.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": 7.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 6.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": 7.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 6.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5}, {"time": 7.8333, "angle": 7.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 6.22}], "scale": [{"x": 1.043, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.052, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.043, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 1.052, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "x": 1.043, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.052, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.043}]}, "图层 60": {"rotate": [{"angle": 7.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 7.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.5, "angle": 7.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "angle": 7.15}], "scale": [{"x": 1.049, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 1.049, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.5, "x": 1.049, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "x": 1.052, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "x": 1.049}]}, "图层 88": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.02, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 2.02, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 87": {"rotate": [{"angle": 0.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 2.02, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": 2.66, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 2.02, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": 0.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.7667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.1667, "angle": 2.31, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 7.1, "angle": -0.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 7.5, "angle": 2.2, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 8.1667, "angle": 0.26}]}, "图层 90": {"rotate": [{"angle": 0.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 3.22, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 0.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 3.22, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": 0.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.0333, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 3.22, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1667, "angle": 0.74}]}, "图层 89": {"rotate": [{"angle": 1.28, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 3.22, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 1.28, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 3.22, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": 1.28, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": 3.22, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.1667, "angle": 1.28}]}, "图层 92": {"rotate": [{"angle": 1.76, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 3.22, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 1.76, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 3.22, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": 1.76, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": 3.22, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1667, "angle": 1.76}]}, "图层 25": {"translate": [{"x": -0.43, "y": -0.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": -2.33, "y": -4.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -0.43, "y": -0.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "x": -2.33, "y": -4.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": -0.43, "y": -0.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333}, {"time": 7.1667, "x": -2.33, "y": -4.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": -0.43, "y": -0.85}], "scale": [{"x": 0.982, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.9, "y": 1.001, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.982, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 0.9, "y": 1.001, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 0.982, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 0.9, "y": 1.001, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 0.982}], "shear": [{}, {"time": 1.3333, "x": 12}, {"time": 2.6667}, {"time": 4, "x": 6}, {"time": 5.5}, {"time": 6.8333, "y": 7.2}, {"time": 8.1667}]}, "图层 26": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "shear": [{}, {"time": 1.3333, "x": 12}, {"time": 2.6667}, {"time": 4, "x": 6}, {"time": 5.5}, {"time": 6.8333, "y": 7.2}, {"time": 8.1667}]}, "图层 24": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": -2.33, "y": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 0.9, "y": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "shear": [{}, {"time": 1.3333, "x": 12}, {"time": 2.6667}, {"time": 4, "x": 6}, {"time": 5.5}, {"time": 6.8333, "y": 7.2}, {"time": 8.1667}]}, "图层 42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"time": 5.5}, {"time": 6.3333, "x": 1.18}, {"time": 8.1667}]}, "图层 70": {"rotate": [{"angle": 2.44, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 8.92, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "angle": 2.44, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": 8.92, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 5.5, "angle": 2.44, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 5.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 8.92, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 8.1667, "angle": 2.44}]}, "图层 73": {"rotate": [{"angle": 6.2, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 8.92, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 6.2, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 8.92, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 5.5, "angle": 6.2, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "angle": 8.92, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 8.1667, "angle": 6.2}]}, "图层 72": {"rotate": [{"angle": 8.84, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 8.92, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.6667, "angle": 8.84, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 3.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 8.92, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 5.5, "angle": 8.84, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "angle": 8.92, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 8.1667, "angle": 8.84}]}, "图层 75": {"rotate": [{"angle": 6.76, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 6.76, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0667, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.5, "angle": 6.76, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.9, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.1667, "angle": 6.76}]}, "图层 23": {"rotate": [{}, {"time": 1.3333, "angle": -10.8}, {"time": 2.6667}, {"time": 4, "angle": -6}, {"time": 5.5}, {"time": 6.8333, "angle": -3.6}, {"time": 8.1667}], "translate": [{"x": -1.71, "y": -3.61}, {"time": 1.3333, "x": 4.18, "y": 10.18}, {"time": 2.6667, "x": -1.71, "y": -3.61}, {"time": 4, "x": 1.84, "y": 4.66}, {"time": 5.5, "x": -1.71, "y": -3.61}, {"time": 6.8333, "x": -0.42, "y": -0.9}, {"time": 8.1667, "x": -1.71, "y": -3.61}]}, "图层 57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "translate": [{"time": 6.1667}, {"time": 6.6667, "x": 11.85, "y": -1.88}, {"time": 7.0667, "x": 0.78, "y": -0.18}, {"time": 8.1667}]}, "图层 11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.53, "curve": 0.328, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 7.5667, "angle": -0.39, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 8.1667}], "translate": [{"x": 1.07, "y": -1.69}, {"time": 0.5, "x": -0.31, "y": 0.63}, {"time": 1.3333, "x": 3.5, "y": 2.91}, {"time": 2.6667, "x": 3.7, "y": 0.41}, {"time": 4, "x": 3.13, "y": 2.04}, {"time": 5.5, "x": 3.19, "y": 0.86}, {"time": 6.3333, "x": 4.42, "y": -3.39}, {"time": 6.6667, "x": 13.27, "y": 0.2}, {"time": 6.8333, "x": 11.61, "y": -1.47}, {"time": 7.1667, "x": 5.28, "y": -3.26}, {"time": 7.4, "x": 9.04, "y": -2.49}, {"time": 7.6667, "x": 0.89, "y": -1.53}, {"time": 7.9333, "x": -0.92, "y": -0.43}, {"time": 8.1667, "x": 1.07, "y": -1.69}], "scale": [{"time": 2.6667}, {"time": 4, "x": 0.98}, {"time": 5.5}, {"time": 6.3333, "x": 0.968}, {"time": 6.8333, "x": 0.96}, {"time": 7.9, "x": 1.012}, {"time": 8.1667}]}, "图层 58": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 1.06, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 15": {"rotate": [{"angle": 4.32, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 4.32, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 4.32, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 4.32}], "translate": [{}, {"time": 1.3333, "x": 6, "y": -18}, {"time": 2.6667}, {"time": 4, "x": 6, "y": -10}, {"time": 5.5}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.3333, "x": 1.059, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6.8333, "x": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 43": {"rotate": [{"angle": 3.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 4.32}, {"time": 1.6667, "angle": -0.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 3.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 4.32}, {"time": 4.3333, "angle": -0.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "angle": 3.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "angle": 4.32}, {"time": 7.1667, "angle": -0.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": 3.35}], "scale": [{"x": 1.007, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.007, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.5, "x": 1.007, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.007}]}, "图层 47": {"rotate": [{"angle": 1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 4.32}, {"time": 2, "angle": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 4.32}, {"time": 4.6667, "angle": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "angle": 1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "angle": 4.32}, {"time": 7.5, "angle": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": 1.69}], "scale": [{"x": 1.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5, "x": 1.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "x": 1.02}]}, "图层 48": {"rotate": [{"angle": 0.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": 4.32}, {"time": 2.3333, "angle": -0.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": 4.32}, {"time": 5, "angle": -0.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 0.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": 4.32}, {"time": 7.8333, "angle": -0.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 0.04}], "scale": [{"x": 1.033, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.033, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "x": 1.033, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.033}]}, "图层 45": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -9.82, "y": 10.41, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -9.82, "y": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": -23.82, "y": -5.59, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 96": {"rotate": [{"angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 8.69, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 4.46}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 95": {"rotate": [{"angle": 3.93, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 1.5667, "angle": -0.71, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.9, "angle": 4.46}, {"time": 4.2333, "angle": -0.71, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 5.4333}, {"time": 7, "angle": 8.69, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "angle": 3.93}], "scale": [{"x": 1.002, "y": 1.002, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 1.5667, "x": 1.02, "y": 1.02, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 1.02, "y": 1.02, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.04, "y": 1.04, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.1667, "x": 1.002, "y": 1.002}]}, "图层 98": {"rotate": [{"angle": 2.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.8, "angle": -0.71, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 3.1333, "angle": 4.46}, {"time": 4.4667, "angle": -0.71, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.6}, {"time": 7.1667, "angle": 8.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "angle": 2.89}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.8, "x": 1.02, "y": 1.02, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "x": 1.02, "y": 1.02, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.04, "y": 1.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.1667, "x": 1.006, "y": 1.006}]}, "图层 97": {"rotate": [{"angle": 1.7, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 2.0333, "angle": -0.71, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "angle": 4.46}, {"time": 4.7, "angle": -0.71, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.7667}, {"time": 7.3333, "angle": 8.69, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": 1.7}], "scale": [{"x": 1.011, "y": 1.011, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 2.0333, "x": 1.02, "y": 1.02, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 1.02, "y": 1.02, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.04, "y": 1.04, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "x": 1.011, "y": 1.011}]}, "图层 100": {"rotate": [{"angle": 0.54, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": 4.46}, {"time": 2.2667, "angle": -0.71, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6, "angle": 4.46}, {"time": 4.9333, "angle": -0.71, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.9333}, {"time": 7.5, "angle": 8.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "angle": 0.54}], "scale": [{"x": 1.015, "y": 1.015, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 1.02, "y": 1.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": 1.02, "y": 1.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1667, "x": 1.015, "y": 1.015}]}, "图层 99": {"rotate": [{"angle": -0.39, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": 4.46}, {"time": 2.5, "angle": -0.71, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.8333, "angle": 4.46}, {"time": 5.1667, "angle": -0.71, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.1}, {"time": 7.6667, "angle": 8.69, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8.1667, "angle": -0.39}], "scale": [{"x": 1.019, "y": 1.019, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.02, "y": 1.02, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.02, "y": 1.02, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 1.04, "y": 1.04, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8.1667, "x": 1.019, "y": 1.019}]}, "图层 102": {"rotate": [{"angle": -0.63, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.4, "angle": 4.46, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.7333, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 4.46, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 5.5, "angle": 7.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.2667}, {"time": 7.8333, "angle": 8.69, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": -0.63}], "scale": [{"x": 1.02, "y": 1.02, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.4, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.7333, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 5.5, "x": 1.033, "y": 1.033, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.04, "y": 1.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "x": 1.02, "y": 1.02}]}, "图层 101": {"rotate": [{"angle": 0.1, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.6333, "angle": 4.46, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.9667, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 4.46, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.5, "angle": 8.14, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 6.4333}, {"time": 8, "angle": 8.69, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 8.1667, "angle": 0.1}], "scale": [{"x": 1.017, "y": 1.017, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.6333, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.9667, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.5, "x": 1.037, "y": 1.037, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 6.4333, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 1.04, "y": 1.04, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 8.1667, "x": 1.017, "y": 1.017}]}, "图层 29": {"rotate": [{"angle": -4.15, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.15, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.29, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -4.15, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -4.29, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -4.15}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 49": {"rotate": [{"angle": -4.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": -4.15}, {"time": 1.6, "angle": -4.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -4.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": -4.15}, {"time": 4.2667, "angle": -4.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -4.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.7667, "angle": -4.15}, {"time": 7.1, "angle": -4.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.1667, "angle": -4.17}], "scale": [{"x": 1.008, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 1.06, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "x": 1.008, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": 1.06, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "x": 1.008, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "x": 1.06, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.1667, "x": 1.008}]}, "图层 51": {"rotate": [{"angle": -4.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -4.15}, {"time": 1.8667, "angle": -4.29, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -4.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "angle": -4.15}, {"time": 4.5333, "angle": -4.29, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "angle": -4.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.0333, "angle": -4.15}, {"time": 7.3667, "angle": -4.29, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1667, "angle": -4.2}], "scale": [{"x": 1.022, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 1.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 1.022, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "x": 1.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.5, "x": 1.022, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.0333, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "x": 1.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1667, "x": 1.022}]}, "图层 50": {"rotate": [{"angle": -20.8, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": -4.15}, {"time": 2.1333, "angle": -30.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -20.8, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "angle": -4.15}, {"time": 4.8, "angle": -30.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "angle": -20.8, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.3, "angle": -4.15}, {"time": 7.6333, "angle": -30.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.1667, "angle": -20.8}], "scale": [{"x": 1.038, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 1.06, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 1.038, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "x": 1.06, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.5, "x": 1.038, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "x": 1.06, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.1667, "x": 1.038}]}, "图层 53": {"rotate": [{"angle": -27.06, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": -4.15}, {"time": 2.4, "angle": -30.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": -27.06, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "angle": -4.15}, {"time": 5.0667, "angle": -30.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "angle": -27.06, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.5667, "angle": -4.15}, {"time": 7.9, "angle": -30.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1667, "angle": -27.06}], "scale": [{"x": 1.052, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "x": 1.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 1.052, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": 1.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.5, "x": 1.052, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "x": 1.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1667, "x": 1.052}]}, "图层 76": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 74": {"rotate": [{"angle": 4.87, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 14.51, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 4.87, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 14.51, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.5, "angle": 4.87, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 14.51, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": 4.87}]}, "图层 78": {"rotate": [{"angle": 11.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 14.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 11.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 14.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "angle": 11.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 14.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.1667, "angle": 11.83}]}, "图层 105": {"rotate": [{"angle": 2.08, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": 19.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 28.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -41.55, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 8.1667, "angle": 2.08}]}, "图层 108": {"rotate": [{"angle": -9.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -9.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -28.35, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.1667, "angle": -9.52}]}, "图层 109": {"rotate": [{"angle": -18.79, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.3333, "angle": -9.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -18.79, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 3, "angle": -9.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -28.35, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8.1667, "angle": -18.79}]}, "图层 38": {"translate": [{"x": 43.48, "y": -55.23, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.6333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 2}, {"time": 3.3667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8.1667, "x": 43.48, "y": -55.23}]}, "图层 37": {"translate": [{"x": 89.29, "y": -90.62, "curve": 0.312, "c2": 0.26, "c3": 0.757}, {"time": 0.9667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 1}, {"time": 2.5333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": 79.29, "y": -100.72, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 8.1667, "x": 89.29, "y": -90.62}]}, "图层 39": {"translate": [{"x": 171.51, "y": -7.54, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.2333, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 3}, {"time": 4.0333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "x": 79.29, "y": -100.72, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 8.1667, "x": 171.51, "y": -7.54}]}, "图层 40": {"translate": [{"x": 72.27, "y": -91.81, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 4}, {"time": 5.7, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "curve": 0.243, "c3": 0.692, "c4": 0.76}, {"time": 8.1667, "x": 72.27, "y": -91.81}]}, "图层 41": {"translate": [{"x": 1.77, "y": -2.24, "curve": 0.277, "c2": 0.12, "c3": 0.753}, {"time": 1.4333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}, {"time": 6.8667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.314, "c3": 0.648, "c4": 0.35}, {"time": 8.1667, "x": 1.77, "y": -2.24}]}, "图层 30": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": 79.29, "y": -100.72, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "x": 178.97, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 32": {"scale": [{"x": 1.283, "y": 1.126, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 1.3, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}, {"time": 4.0333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 8.1667, "x": 1.283, "y": 1.126}]}, "图层 35": {"scale": [{"x": 1.208, "y": 1.092, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 1.5667, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 4.3333}, {"time": 7.0333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 8.1667, "x": 1.208, "y": 1.092}]}, "图层 34": {"scale": [{"x": 1.462, "y": 1.206, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 0.5667, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}, {"time": 6.0333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.243, "c3": 0.676, "c4": 0.7}, {"time": 8.1667, "x": 1.462, "y": 1.206}]}, "图层 52": {"scale": [{"x": 1.31, "y": 1.138, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 8.1667, "x": 1.31, "y": 1.138}]}, "图层 33": {"scale": [{"x": 1.112, "y": 1.05, "curve": 0.338, "c2": 0.35, "c3": 0.758}, {"time": 1.9667, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 2}, {"time": 4.7, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 8.1667, "x": 1.112, "y": 1.05}]}, "图层 31": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "图层 55": {"scale": [{"x": 1.062, "y": 1.027, "curve": 0.316, "c2": 0.27, "c3": 0.757}, {"time": 2.2, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 8.1667, "x": 1.062, "y": 1.027}]}, "图层 36": {"scale": [{"x": 1.007, "y": 1.003, "curve": 0.266, "c2": 0.07, "c3": 0.752}, {"time": 2.5667, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}, {"time": 8.0333, "x": 1.54, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.322, "c3": 0.655, "c4": 0.34}, {"time": 8.1667, "x": 1.007, "y": 1.003}]}, "图层 22": {"rotate": [{}, {"time": 1.3667, "angle": 8.46}, {"time": 2.7333}, {"time": 4.1333, "angle": 8.46}, {"time": 5.5}, {"time": 6.8333, "angle": -13.2}, {"time": 8.1667}]}, "图层 17": {"translate": [{"time": 1.6667}, {"time": 2.6667, "x": -6, "y": -12}, {"time": 4.3333, "x": 2}, {"time": 5.5, "x": -6}, {"time": 6.5}, {"time": 7, "x": 8}, {"time": 7.3333, "x": -4}, {"time": 7.8333, "x": 6}, {"time": 8.1667}], "scale": [{"time": 5.8333}, {"time": 6.5, "y": 0.98}, {"time": 7}]}, "图层 13": {"translate": [{"x": -13.44, "y": -15.53, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.5667, "x": -15.86, "y": -12.15, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -2.97, "y": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "x": -8.41, "y": -2.68, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "x": 1.44, "y": -7.42, "curve": 0.25, "c3": 0.902, "c4": 0.89}, {"time": 6.1667, "x": -26.22, "y": -8.02, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "x": 27.45, "y": -13.8, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "x": -16.48, "y": -18.31, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 7.3333, "x": 2.81, "y": -16.64, "curve": 0.35, "c2": 0.42, "c3": 0.685, "c4": 0.76}, {"time": 7.3667, "x": 5.17, "y": -16.5, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 7.4, "x": 6.19, "y": -16.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 7.7, "x": -19.41, "y": -15.85, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 8.1, "x": -13.44, "y": -15.53}], "scale": [{"y": 1.043, "curve": 0.318, "c2": 0.19, "c3": 0.652, "c4": 0.53}, {"time": 1.5667, "y": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "y": 1.057, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "y": 1.038, "curve": 0.246, "c3": 0.715, "c4": 0.85}, {"time": 7.3333, "y": 1.04, "curve": "stepped"}, {"time": 7.4, "y": 1.04, "curve": 0.323, "c3": 0.657, "c4": 0.34}, {"time": 8.1, "y": 1.043}]}, "图层 20": {"rotate": [{"angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.17, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 5, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -1.31, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 15.1, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 3.8}]}, "图层 65": {"rotate": [{"angle": 5.32, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.4, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 5.32}]}, "图层 67": {"rotate": [{"angle": 8.04, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 8.04}]}, "图层 66": {"rotate": [{"angle": 10.94, "curve": 0.354, "c2": 0.41, "c3": 0.757}, {"time": 1.2, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 10.94}]}, "图层 69": {"rotate": [{"angle": 13.33, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.6, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 13.33}]}, "图层 2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.93, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -8.09, "curve": 0.25, "c3": 0.75}, {"time": 8.1667}], "scale": [{"x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.04, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.06, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 1.02, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": 1.04, "y": 1.04}]}, "图层 3": {"rotate": [{"angle": -0.61, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.2333}, {"time": 1.5667, "angle": 10.24, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "angle": -8.09, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 8.1667, "angle": -0.61}], "scale": [{"x": 1.039, "y": 1.043, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.2333, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 1.08, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 1.1, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "x": 1.04, "y": 1.1, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 8.1667, "x": 1.039, "y": 1.043}]}, "图层 7": {"rotate": [{"angle": -1.76, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.4667}, {"time": 1.8, "angle": 10.24, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": -8.09, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 8.1667, "angle": -1.76}], "scale": [{"x": 1.036, "y": 1.049, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.4667, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 1.1, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "x": 1.08, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "x": 1.02, "y": 1.08, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 8.1667, "x": 1.036, "y": 1.049}]}, "图层 8": {"rotate": [{"angle": -3.19, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.7}, {"time": 2.0333, "angle": 10.24, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "angle": -8.09, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 8.1667, "angle": -3.19}], "scale": [{"x": 1.032, "y": 1.056, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.7, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 1.08, "y": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 1.06, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "x": 1.04, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "x": 1.02, "y": 1.08, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 8.1667, "x": 1.032, "y": 1.056}]}, "bone": {"translate": [{"x": 0.03, "y": -6}]}, "图层 18": {"translate": [{"x": -15.46, "y": -15.64, "curve": 0.327, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 1.6, "x": -15.86, "y": -12.15, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -5.92, "y": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "x": -12.85, "y": -2.68, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.44, "y": -7.42, "curve": 0.25, "c3": 0.902, "c4": 0.89}, {"time": 6.2, "x": -32.2, "y": -8.02, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 27.45, "y": -13.8, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "x": -18.49, "y": -18.31, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 7.3667, "x": 2.81, "y": -16.64, "curve": 0.35, "c2": 0.42, "c3": 0.685, "c4": 0.76}, {"time": 7.4, "x": 5.17, "y": -16.5, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 7.4333, "x": 6.19, "y": -16.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 7.7333, "x": -19.41, "y": -15.85, "curve": 0.288, "c3": 0.627, "c4": 0.38}, {"time": 8.1667, "x": -15.46, "y": -15.64}], "scale": [{"y": 1.043, "curve": 0.333, "c2": 0.32, "c3": 0.666, "c4": 0.65}, {"time": 1.6, "y": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "y": 1.057, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 1.038, "curve": 0.246, "c3": 0.715, "c4": 0.85}, {"time": 7.3667, "y": 1.04, "curve": "stepped"}, {"time": 7.4333, "y": 1.04, "curve": 0.324, "c3": 0.657, "c4": 0.34}, {"time": 8.1667, "y": 1.043}]}}, "deform": {"default": {"b6": {"b6": [{"offset": 48, "vertices": [-14.98685, -7.31392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, -46.10843, -22.502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 20, "vertices": [-3.71002, -9.69829, -2.22711, -10.14213, -3.71002, -9.69829, -2.22711, -10.14213, -3.71002, -9.69829, -2.22711, -10.14213, -3.71002, -9.69829, -3.71002, -9.69829, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.39767, -6.10722, -3.71002, -9.69829, -2.22711, -10.14213, 0, 0, 0, 0, -8.39767, -6.10722, -3.71002, -9.69829, -2.22711, -10.14213, 0, 0, 0, 0, 0, 0, -24.51302, 3.32494, -22.36237, -10.57818, -20.54126, -13.78578, 0, 0, 0, 0, 0, 0, -3.71002, -9.69829, -2.22711, -10.14213, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.39767, -6.10722, -3.71002, -9.69829, -2.22711, -10.14213], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 48, "vertices": [-14.98685, -7.31392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, -46.10843, -22.502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045], "curve": 0.25, "c3": 0.75}, {"time": 4, "offset": 20, "vertices": [-3.7886, -9.90438, -2.2739, -10.35748, -3.7886, -9.90438, -2.2739, -10.35748, -3.7886, -9.90438, -2.2739, -10.35748, -3.7886, -9.90438, -3.7886, -9.90438, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.57576, -6.23685, -3.7886, -9.90438, -2.2739, -10.35748, 0, 0, 0, 0, -8.57576, -6.23685, -3.7886, -9.90438, -2.2739, -10.35748, 0, 0, 0, 0, 0, 0, -20.84383, -0.21217, -17.35846, -11.5436, -15.44876, -13.9959, 0, 0, 0, 0, 0, 0, -3.7886, -9.90438, -2.2739, -10.35748, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.57576, -6.23685, -3.7886, -9.90438, -2.2739, -10.35748], "curve": 0.25, "c3": 0.75}, {"time": 5.5, "offset": 12, "vertices": [-6.46664, -12.18376, -4.85489, -12.91162, -3.23297, -6.09177, -2.42725, -6.45587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, -46.10843, -22.502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045], "curve": 0.245, "c3": 0.639, "c4": 0.56}, {"time": 6.3333, "offset": 12, "vertices": [-2.30028, -4.33394, -1.72695, -4.59285, -1.15001, -2.16693, -0.86341, -2.29645, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.33104, -2.60167, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.86363, -4.26452, -4.50457, -4.87147, -4.54921, -5.41461, 0, 0, 0, 0, 0, 0, -30.10667, -0.39517, -27.48988, -7.72092, -27.29202, -8.5823, -2.34336, 3.22247, -2.8288, 2.34322, -2.80709, 2.65003, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.35499, -1.1424, -1.80162, -2.88954, -1.56039, -3.40734, 0, 0, 0, 0, 0, 0, -16.40143, -8.00428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.33104, -2.60167, -5.9322, -1e-05, -5.33104, -2.60167, -5.08699, -3.05219, -8.70751, -7.34052, -5.70807, -8.67917, -4.83356, -8.17738], "curve": 0.376, "c2": 0.52, "c3": 0.733, "c4": 0.95}, {"time": 6.8333, "offset": 12, "vertices": [-0.0552, -0.10401, -0.04144, -0.11022, -0.0276, -0.052, -0.02072, -0.05511, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12794, -0.06244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.02329, -6.56248, -6.93189, -7.49649, -7.00058, -8.33231, 0, 0, 0, 0, 0, 0, -37.34342, -0.60811, -34.22722, -7.94024, -34.29244, -8.58329, -3.6061, 4.95892, -4.35311, 3.60589, -4.31971, 4.07802, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.36246, -1.75798, 5.30333, -0.50543, 5.30486, -0.61977, 0, 0, 0, 0, 0, 0, -0.39361, -0.19209, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12794, -0.06244, -0.14236, 0, -0.12794, -0.06244, -0.12208, -0.07325, -10.58743, -2.80649, -9.2947, -5.05956, -8.89028, -5.28579], "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 8.1667, "offset": 48, "vertices": [-14.98685, -7.31392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, 0, 0, 0, 0, 0, 0, -46.10843, -22.502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.98685, -7.31392, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045, -16.67686, -2e-05, -14.98685, -7.31392, -14.30078, -8.58045]}]}, "b1": {"b1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3667, "offset": 188, "vertices": [-4.86363, -6.69867, -5.13557, -6.22744, -7.11494, -4.08714, -6.76486, -4.18135, -7.26654, 2.3248, -6.28714, 13.65219, -8.60908, -11.85727, -9.09052, -11.02313, -9.38609, -10.44058, -12.59427, -7.23453, -11.97444, -7.40141, -7.15638, 15.53976, -9.79937, -13.49664, -10.34738, -12.54719, -10.68378, -11.88408, -14.33549, -8.23479, -13.63011, -8.42477, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.1834, 10.42484, 13.25766, 9.22161, 13.28677, 8.42543, 13.81918, -7.86461, 2.77326, -17.35742, 13.1834, 10.42484, 13.25766, 9.22161, 13.28677, 8.42543, 13.81918, -7.86461, 2.77326, -17.35742, 13.1834, 10.42484, 13.25766, 9.22161, 13.81918, -7.86461, -6.33263, -14.86802, -4.92192, -6.84972, 0, 0, 0, 0, 0, 0, -3.14348, 6.82594, -4.30441, -5.92853, -4.54517, -5.51138, -6.29698, -3.61711, 13.78973, -8.8292, 1.41801, 16.30345, 2.68985, 16.11494, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.41801, 16.30345, 2.68985, 16.11494, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 2.68985, 16.11494, 3.50359, 15.92866, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.08406, 11.0396, -6.96156, -9.58819, -7.3508, -8.91375, -10.18404, -5.85025, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.78973, -8.8292], "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "offset": 254, "vertices": [13.1834, 10.42484, 13.25766, 9.22161, 13.28677, 8.42543, 13.81918, -7.86461, 2.77326, -17.35742, 13.1834, 10.42484, 13.25766, 9.22161, 13.28677, 8.42543, 13.81918, -7.86461, 2.77326, -17.35742, 13.1834, 10.42484, 13.25766, 9.22161, 13.81918, -7.86461, -14.56822, -27.56375, -17.34148, -10.20633, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.78973, -8.8292, 1.41801, 16.30345, 2.68985, 16.11494, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.41801, 16.30345, 2.68985, 16.11494, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 2.68985, 16.11494, 3.50359, 15.92866, 13.82452, 4.61028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.78973, -8.8292], "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "b9": {"b9": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 22, "vertices": [0.51399, 10.89006, -2.29042, 10.05635, 1.31198, 3.09145, 0.84331, 3.18387, 0.41226, 3.1499, 1.96455, -2.73613, 2.28705, -2.38457, 2.4606, -2.11324, 2.48323, -1.9967, 5.84641, -16.6076, 7.28172, -15.67747, 7.65034, -15.15858, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.81386, 1.59433, -3.92611, 1.01747, -3.88073, 0.48695, -3.26213, -4.13951, -2.59338, -4.47372, -2.11187, -4.6156, -1.92549, -4.59975, 0.81481, -16.40337, 2.34507, -15.95279, 2.81279, -15.58688], "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "offset": 144, "vertices": [-0.19843, 4.79401, -1.60663, 4.38298, -1.85623, 4.20166, 0.26622, 3.66318, -0.98466, 3.48608, -0.84077, 3.47264, -1.04617, 3.35757, 0.89514, 2.11627, 0.11822, 2.26425, 0.20078, 2.22496, 0.05594, 2.20087, 1.36909, 0.80598, 0.99783, 1.20956, 1.02826, 1.15088, 0.96057, -0.31783, 1.00433, 0.04434, 0.99709, 0.03079, 0.98277, -0.01207, 0.44267, -1.58951, 0.96973, -1.31952, 0.94497, -1.32138, 0.87979, -1.34422], "curve": 0.25, "c3": 0.75}, {"time": 4.3, "offset": 22, "vertices": [9.37019, 2.16277, 8.09812, 4.1898, 10.15575, -5.33968, 10.60973, -3.7464, 10.58436, -2.51712, 10.79507, -10.95479, 12.02413, -9.10481, 12.57254, -7.81618, 12.49226, -7.52472, 15.48215, -22.702, 17.17663, -20.79575, 17.2076, -20.24506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.22617, -6.78992, 6.03122, -5.8508, 6.39721, -5.00862, 5.76491, -12.30934, 7.33447, -11.13055, 8.17092, -10.21852, 8.1902, -9.95206, 10.64616, -22.55948, 12.43681, -21.09172, 12.54312, -20.60262], "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "offset": 22, "vertices": [-16.1006, -11.20377, -15.49007, -8.83768, -18.53335, -0.91385, -17.86331, -0.87326, -16.85789, 0.67871, -20.56922, 6.00851, -19.82345, 5.79971, -19.06299, 6.05878, -18.13937, 7.11327, -27.77257, 22.07199, -26.39537, 21.99057, -24.24246, 23.07008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.69475, 22.31448, -5.40277, 19.49809, -5.51514, 19.19167, -3.38212, 18.19124, -3.47754, 15.80014, -1.93121, 15.74301, -2.05374, 15.51978, 2.4059, 11.70961, 1.76744, 10.30365, 2.67825, 9.89214, 2.55054, 9.79073, 6.60641, 5.95035, 5.60439, 5.39232, 5.96762, 4.74307, 6.64098, -0.21246, 5.95163, 0.40585, 5.81207, 0.00803, 5.6716, -0.53111, 6.008, -7.56055, 6.0388, -6.21307, 5.47136, -6.43092, 4.7417, -6.78426, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.80332, 8.07043, -5.89777, 6.69873, -5.30054, 6.88654, -4.53705, 7.21222, 0, 0, 0, 0, 0, 0, -7.6796, 13.39067, -7.09358, 11.48135, -5.86142, 11.86317, -5.89413, 11.65361, 0, 0, 0, 0, -7.9556, 18.20882, -5.84314, 15.99944, -5.91568, 15.73549, -8.61775, 2.11711, -7.25925, 2.26825, -7.18137, 2.17389, -9.10162, 21.66512, -6.64087, 19.0173, -6.73138, 18.70561, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -11.97422, 2.15918, -11.54022, 2.08685, -10.66826, 2.92935, -14.13707, 9.4921, -13.62271, 9.15521, -12.98486, 9.16901, -12.03162, 9.74562, -21.04904, 23.59836, -19.85191, 23.31812, -17.79683, 23.93983], "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "a6": {"a6": [{"vertices": [-14.48491, -17.44824, -20.84433, -11.37363, -11.93087, -17.51558, -27.29558, -6.12274, -19.10181, -15.90701, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.05769, 5.30163, 0.10284, 5.10716, 0.14102, 5.05815, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0, 0, 0, 0, 0, 0, -1.13412, -11.28604, 0.26914, -14.17492, -7.81256, -17.29392, -8.37496, -13.03518, -16.12007, -13.08112, -7.52743, -16.98037], "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "vertices": [-4.93921, -6.62608, 4.57322, -10.71581, 7.33935, -6.74633, 11.69221, -13.77618, 13.71773, -6.33038, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16.79266, 13.888, 16.60019, 12.87276, 16.54443, 12.62453, 17.05372, 15.75657, 16.79266, 13.888, 16.60019, 12.87276, 17.05372, 15.75657, 16.79266, 13.888, 16.60019, 12.87276, 26.36754, 6.40346, 17.05372, 15.75657, 16.79266, 13.888, 16.60019, 12.87276, 26.36754, 6.40346, 17.05372, 15.75657, 16.79266, 13.888, 16.60019, 12.87276, 26.36754, 6.40346, 17.05372, 15.75657, 16.79266, 13.888, 0, 0, 0, 0, 0, 0, 20.3549, -17.50133, 14.41412, -14.94707, 0.58894, -9.00293, 10.51009, -13.26855, 4.37238, -10.62959, 7.15947, -6.75823], "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "vertices": [-4.93921, -6.62608, 4.57322, -10.71581, 7.33935, -6.74633, 11.69221, -13.77618, 13.71773, -6.33038, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43.63794, 17.25904, 42.57242, 15.31829, 42.29742, 14.84757, 45.40331, 20.89504, 43.63794, 17.25904, 42.57242, 15.31829, 45.40331, 20.89504, 43.63794, 17.25904, 42.57242, 15.31829, 59.65632, -3.92261, 45.40331, 20.89504, 43.63794, 17.25904, 42.57242, 15.31829, 59.65632, -3.92261, 45.40331, 20.89504, 43.63794, 17.25904, 42.57242, 15.31829, 59.65632, -3.92261, 45.40331, 20.89504, 43.63794, 17.25904, 0, 0, 0, 0, 0, 0, 20.3549, -17.50133, 14.41412, -14.94707, 0.58894, -9.00293, 10.51009, -13.26855, 4.37238, -10.62959, 7.15947, -6.75823], "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "vertices": [-19.11447, -23.02492, -27.50644, -15.00878, -15.74413, -23.11378, -36.01959, -8.07965, -25.20699, -20.99109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07613, 6.9961, 0.13571, 6.73947, 0.1861, 6.6748, -0.48517, 7.44502, -0.07613, 6.9961, 0.13571, 6.73947, -0.48517, 7.44502, -0.07613, 6.9961, 0.13571, 6.73947, 3.23737, 7.66203, -0.48517, 7.44502, -0.07613, 6.9961, 0.13571, 6.73947, 3.23737, 7.66203, -0.48517, 7.44502, -0.07613, 6.9961, 0.13571, 6.73947, 3.23737, 7.66203, -0.48517, 7.44502, -0.07613, 6.9961, 0, 0, 0, 0, 0, 0, -1.4966, -14.8932, 0.35517, -18.70541, -10.30955, -22.82128, -11.0517, -17.20139, -21.27224, -17.26202, -9.93329, -22.40751], "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8.1667, "vertices": [-14.48491, -17.44824, -20.84433, -11.37363, -11.93087, -17.51558, -27.29558, -6.12274, -19.10181, -15.90701, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.05769, 5.30163, 0.10284, 5.10716, 0.14102, 5.05815, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0.10284, 5.10716, 2.45328, 5.80627, -0.36766, 5.64182, -0.05769, 5.30163, 0, 0, 0, 0, 0, 0, -1.13412, -11.28604, 0.26914, -14.17492, -7.81256, -17.29392, -8.37496, -13.03518, -16.12007, -13.08112, -7.52743, -16.98037]}]}, "a2": {"a2": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "offset": 32, "vertices": [5.27995, -2.37932, -0.20233, 5.47259, 5.27995, -2.37932, -0.20233, 5.47259, 5.27995, -2.37932, -0.20233, 5.47259, 5.27995, -2.37932, 5.76126, 0.58775, -0.20233, 5.47259, 5.76126, 0.58775, -0.20233, 5.47259, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.18359, 10.60225, 17.14073, 2.5423, 12.25551, 12.02228, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.97748, -7.82753, 2.86075, -7.86539, -7.92193, -2.54422, 0, 0, 0, 0, -15.84479, -12.90329, -11.65161, 16.30197, -9.15746, 4.1434, -9.69513, -2.45001, -5.69476, -8.20079, -7.6917, 5.99808, 1.07319, -12.13013, 8.36844, -8.82918, 12.04413, -1.19238, -1.96075, -11.96152, -5.52773, -14.23526, 4.54108, -14.58051, 0.47482, -15.26404, -3.41763, -6.19897, 1.19153, -6.97752, -0.7186, -7.04196, 5.13565, -1.94594, 5.40508, 0.37177, 3.97437, 3.65016, 4.80627, 2.45395, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 3.51801, -0.63331, -3.5676, -0.20605, -2.97055, -1.98706, -2.10367, -2.88957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 0.47533, -2.74704, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 0.47533, -2.74704, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 3.51801, -0.63331, -3.5676, -0.20605, -2.97055, -1.98706, -2.10367, -2.88957, -2.93555, -1.49153, -2.01924, -2.57883, -2.86995, -2.0408, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 0.47533, -2.74704, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 0.47533, -2.74704, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 0, 0, 0, 0, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.82878, 8.96931, -8.12372, 5.32765, -9.58005, -1.29387, -6.07758, 7.53568, -6.4023, 7.3067, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.45575, -2.94415, -1.70258, 7.79825, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741, 2.64893, 0.91045, -2.36237, -1.50437, -1.27393, -2.49451, -0.33507, -2.78093, -1.37503, -2.26157, -0.30336, -2.62891, 2.51422, -0.78329, -1.20102, -2.51741], "curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 32, "vertices": [10.55991, -4.75864, -0.40466, 10.94518, 10.55991, -4.75864, -0.40466, 10.94518, 10.55991, -4.75864, -0.40466, 10.94518, 10.55991, -4.75864, 11.52252, 1.17549, -0.40466, 10.94518, 11.52252, 1.17549, -0.40466, 10.94518, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -0.77354, 2.38496, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -0.77354, 2.38496, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, 0.73573, 2.4015, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -0.77354, 2.38496, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -0.77354, 2.38496, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0, 0, 0, 0, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015, -2.2598, -1.103, 1.92145, 1.62212, 0.84967, 2.36679, 0.07228, 2.51355, 1.03012, 2.2323, 0.07702, 2.45731, -2.21106, 0.9695, 0.73573, 2.4015], "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "offset": 374, "vertices": [0.72495, -1.18977, -0.97672, 0.99366, -1.34164, 0.37579, -1.38959, -0.10298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.44766, -1.64303, 4.3269, 6.28183, 1.93004, 7.37836, 5.13211, 5.51628, 2.47331, 7.09125, -6.41434, 3.89839, 4.09923, 6.35747, -10.27209, -1.37304, 3.45789, 9.76978, 7.56064, 6.86684, 4.13321, 9.29221, -8.23007, 5.96898, 6.2585, 8.12732, -9.7012, 2.36376, 9.98477, -0.09483, 8.70749, 4.88868, 0, 0, 0, 0, 0, 0, 0, 0, 3.3381, -1.46861, -3.58453, 0.67104, -3.44272, -1.2019, -2.82951, -2.30136, 4.40347, -2.61809, -4.97595, -0.28139, -4.36367, -2.22687, 1.31393, -4.68225, -4.8522, -1.17926, -3.26973, -3.6992, -2.67062, -2.12383, 0.5051, 3.37487, -0.67346, 3.34532, 1.00684, 3.25217, -0.35382, 3.38437, -3.36093, 0.38058, 0.43166, 3.38313, -1.50348, 3.04228, 0, 0, 0, 0, 0, 0, 0, 0, -2.34692, -1.29395, 1.99103, 1.79379, 0.83414, 2.5469, -2.65387, 0.37302, 2.66919, 0.24037, 2.19547, 1.53701, -3.41156, 0.47955, 3.43109, 0.30897, 2.8222, 1.9758, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.36176, -0.33191, -2.37549, -0.21397, -1.95367, -1.36777, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.55118, -0.30913, 1.65521, 1.9659, 0.88785, 2.41164, 1.89352, 1.67899, 1.05215, 2.29068, -2.02295, 1.50288, 1.57574, 1.99561, -6.45523, 1.50781, 5.74991, 3.2994, 4.28476, 5.0582, 6.03363, 2.34721, 4.51344, 4.54086, -3.53616, 5.3204, 5.53633, 3.40324, -7.53766, 0.58011, 5.90887, 4.71597, 3.95245, 6.44443, 6.40482, 3.72081, 4.31594, 5.94931, -4.94489, 5.43004, 5.66586, 4.82242, -9.12238, -0.30893, 8.81296, 2.37575, 6.46185, 6.447, 3.88358, 8.26021, 7.20285, 5.34238, 4.406, 7.7543, -6.68317, 5.90169, 6.17326, 6.56326, -4.63849, -1.15952, 4.25311, 2.18416, 2.60202, 4.01128, 1.08244, 4.65703, 3.11859, 3.54866, 1.42996, 4.49234, -4.091, 2.33643, 2.46066, 4.05786], "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "offset": 114, "vertices": [1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, -0.45934, -1.07793, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -3.06873, -0.28802, 2.92313, 0.97851, 2.04813, 2.30341, 1.18016, 2.84702, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, -0.45934, -1.07793, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -1.9801, 0.15877, 1.96466, 0.29585, 1.55655, 1.23432, 1.0661, 1.67584, 1.7471, 1.00015, 1.18339, 1.60907, 1.49287, 1.26189, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 1.08862, 0.44678, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 0.2193, -1.14989, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 1.08862, 0.44678, -0.95847, -0.68266, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793, 1.08862, 0.44678, -0.95847, -0.68266, -0.49158, -1.06909, -0.11406, -1.17117, -0.57556, -0.94951, -0.14999, -1.09979, 1.04044, -0.37904, -0.45934, -1.07793], "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}, "b5": {"b5": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "offset": 12, "vertices": [-3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -3e-05, -3.06289, -5e-05, -3.06281, 0, -3.06289, -3e-05, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281, 0, -3.06289, -5e-05, -3.06281], "curve": 0.25, "c3": 0.75}, {"time": 1, "offset": 12, "vertices": [0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147, -7e-05, 0.91138, 6e-05, 0.91147], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 12, "vertices": [1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70617, 9e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70617, 9e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70617, 9e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626, -6e-05, 1.70618, 8e-05, 1.70626], "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "offset": 12, "vertices": [-2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44203, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44203, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842, -2.56224, -2.44204, -2.56212, -2.53842], "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "offset": 12, "vertices": [3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88753, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88753, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88753, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239, 3.1889, 1.88752, 3.18892, 1.89239], "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "offset": 5, "vertices": [2.0804, -1e-05, 2.22456, 1.78104, 0.36111, 1.78125, 0.28282, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97247, 2.77155, 4.97248, 2.77154, 4.97251, 2.82317, 4.97247, 2.77155, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97247, 2.77155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 4.97248, 2.77154, 4.97251, 2.82317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8e-05, 3.79059, 1e-05, 4.05337], "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "offset": 12, "vertices": [-2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51761, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51761, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51761, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.16431, 1.76371, 3.16433, 1.79657, -2.43877, -2.51762, -2.43884, -2.72743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743, -2.43877, -2.51762, -2.43884, -2.72743], "curve": 0.25, "c3": 0.75}, {"time": 8.1667}]}}}}}}