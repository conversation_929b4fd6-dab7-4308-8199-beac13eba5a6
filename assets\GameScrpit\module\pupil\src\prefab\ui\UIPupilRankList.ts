import { _decorator, Label } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import { PupilModule } from "../../PupilModule";
import { PupilMessage, PupilRankResponse } from "../../../../../game/net/protocol/Pupil";
import { JsonMgr } from "../../../../../game/mgr/JsonMgr";
import ToolExt from "../../../../../game/common/ToolExt";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { formatNumber } from "../../../../../lib/utils/NumbersUtils";
import { AdapterView } from "../../../../../../platform/src/core/ui/adapter_view/AdapterView";
import { PupilRankAdapter } from "../../adapter/PupilRankAdapter";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIPupilRankList")
export class UIPupilRankList extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilRankList`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  protected onEvtShow(): void {
    let scrollList: AdapterView = this.getNode("list_rank").getComponent(AdapterView);

    let adapter: PupilRankAdapter = new PupilRankAdapter(this.getNode("rank_item"));
    scrollList.setAdapter(adapter);
    PupilModule.api.rank((data: PupilRankResponse) => {
      log.log("弟子排行榜======", data);

      let index = data.rank - 1;
      let myInfo = index >= 0 ? data.pupilList[index] : null;
      this.setMy(myInfo, data.rank);
      adapter.setDatas(data.pupilList);
    });
  }

  private setMy(info: PupilMessage, rank: number) {
    this["rank_lab"].getComponent(Label).string = `我的排名：${rank > 0 ? rank : "未上榜"}`;

    if (rank < 1) {
      this["adult_attr_lab"].active = false;
      return;
    }
    this["adult_attr_lab"].active = true;
    let adultAttrList = info == null ? PupilModule.service.bestAttr : info.ownInfo.adultAttrList;
    log.log("我的属性======", adultAttrList);
    let attrMap = ToolExt.traAwardItemMapList(adultAttrList)[0];
    let c_attribute = JsonMgr.instance.jsonList.c_attribute;
    let str = `属性：${c_attribute[attrMap["id"]]["name"]}:${formatNumber(attrMap["num"] * 100)}%`;
    this["adult_attr_lab"].getComponent(Label).string = str;
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
