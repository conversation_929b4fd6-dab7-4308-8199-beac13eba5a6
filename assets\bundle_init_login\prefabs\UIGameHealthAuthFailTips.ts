import { _decorator, Component, Node, System } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { SceneLogin } from "../../script_game/scene_login/SceneLogin";
import { RouteSceneLogin } from "../../script_game/scene_login/RouteSceneLogin";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Wed Mar 05 2025 16:55:44 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_game_health/UIGameHealthAuthFailTips.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIGameHealthAuthFailTips")
export class UIGameHealthAuthFailTips extends Component {
  protected start(): void {}
  private onClickOk() {
    SceneLogin.routeMgr.back();
    SceneLogin.routeMgr.showDialog(RouteSceneLogin.PageLogin);
  }
}
