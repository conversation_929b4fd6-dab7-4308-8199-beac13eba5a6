import { _decorator, Input, instantiate, Label, Node, sp, tween, UIOpacity, v3, Vec3 } from "cc";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { TravelResUpdateMessage } from "../../net/protocol/Friend";
import { TravelModule } from "../../../module/travel/TravelModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import { TravelAudioName, TravelMsgEnum } from "../../../module/travel/TravelConfig";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { FmConfig, PageZero } from "../../GameDefine";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { HorseModule } from "../../../module/horse/HorseModule";
import MsgEnum from "../../event/MsgEnum";
import StorageMgr, { StorageKeyEnum } from "../../../../platform/src/StorageHelper";
import { UseTypeEnum } from "../UIItemUse";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Formate from "../../../lib/utils/Formate";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const tilidan: number = 1006;

@ccclass("UITravel")
export class UITravel extends UINode {
  protected _openAct: boolean = true; //打开动作
  public zOrder(): number {
    return PageZero.UITravel;
  }

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_TRAVEL}?prefab/ui/UITravel`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }
  private _role: Node = null;

  /**游历10次 == 0 就是不10次 其余数字为10次 */
  private _tenTravel: number = 0;

  /**定时器id */
  private _tickId_list: number[] = [];

  private _is_click: boolean = true;

  protected onEvtShow(): void {
    this.getNode("btn_test1").active = FmConfig.isDebug;

    this._tenTravel = StorageMgr.loadNum(StorageKeyEnum.游历10次);
    if (!PlayerModule.service.hasRight(112) == false) {
      this._tenTravel = 0;
    }
    StorageMgr.saveItem(StorageKeyEnum.游历10次, JSON.stringify(this._tenTravel));

    this.getNode("animation_block").active = false;
    this.addRole();

    this.setPlace();
    this.setTen();
    this.setVitality();
  }

  protected onRegEvent(): void {
    this.getNode("bg_map").children.forEach((val) => {
      val.on(Input.EventType.TOUCH_END, this.touchSite, this);
    });
    MsgMgr.on(TravelMsgEnum.TRAVEL_VITALITY_UPDATE, this.setVitality, this);
    MsgMgr.on(TravelMsgEnum.TRAVEL_TOTAL_UPDATE, this.onEvtShow, this);
  }

  protected onDelEvent(): void {
    this.getNode("bg_map").children.forEach((val) => {
      val.off(Input.EventType.TOUCH_END, this.touchSite, this);
    });
    MsgMgr.off(TravelMsgEnum.TRAVEL_VITALITY_UPDATE, this.setVitality, this);
    MsgMgr.off(TravelMsgEnum.TRAVEL_TOTAL_UPDATE, this.onEvtShow, this);
  }

  private touchSite(event) {
    AudioMgr.instance.playEffect(TravelAudioName.Effect.点击地点图标);
    let node: Node = event.target;
    let list = node.name.split("_");
    let id = list[2];
    UIMgr.instance.showDialog("UISiteDetail", { keepMask: true, id: id });
  }

  private async addRole() {
    this.getNode("btn_travel").active = false;
    this._role = await ToolExt.loadUIRole(
      this.getNode("roleLayer"),
      PlayerModule.data.skin.skinId,
      HorseModule.data.horseMessage?.horseId || -1,
      "renderScale6",
      this
    );

    this._role.active = false;
    this._role.addComponent(UIOpacity);
    this._role.setPosition(ToolExt.transferOfAxes(this.getNode("btn_travel"), this._role.parent));
    this.getNode("btn_travel").active = true;
  }

  private setPlace() {
    let c_travlePlace = JsonMgr.instance.jsonList.c_travlePlace;
    for (let i in c_travlePlace) {
      let node = this.getNode("btn_site_" + i);
      node.getChildByName("Label").getComponent(Label).string = c_travlePlace[i].name;
    }
  }

  private setTen() {
    this.getNode("gou").active = this._tenTravel == 0 ? false : true;
  }

  private setVitality() {
    if (TravelModule.data.vitality > 0) {
      let str = "体力:" + Formate.format(TravelModule.data.vitality, 0) + "/" + TravelModule.data.vitalitySize;
      this.getNode("coutTime").active = false;
      this.getNode("powerLab").active = true;
      this.getNode("powerLab").getComponent(Label).string = str;
    } else {
      this.getNode("coutTime").active = true;
      this.getNode("powerLab").active = false;
      let c_travle = JsonMgr.instance.jsonList.c_travle;
      let renew = c_travle[1].renew;
      let newTime = TravelModule.data.lastUpdateTime + renew * 1000;

      let svenTime = TimeUtils.formatTimestamp(TravelModule.data.lastUpdateTime);
      //log.error("服务端给的游历开始恢复的起始时间", svenTime);

      FmUtils.setCd(this.getNode("coutTime"), newTime, true);
    }
  }

  private on_click_btn_travel() {
    AudioMgr.instance.playEffect(TravelAudioName.Effect.点击游历按钮);
    if (this._is_click == false) {
      return;
    }
    if (TravelModule.data.vitality <= 0) {
      let args = { itemId: tilidan, type: UseTypeEnum.UITravel };
      UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, args, null, () => {
        //弹窗礼包事件
        MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, tilidan);
      });
      return;
    }

    let param = this._tenTravel == 0 ? false : true;
    let success = this.postTravel_success.bind(this);
    this._is_click = false;
    this.getNode("animation_block").active = true;

    this._role.setPosition(ToolExt.transferOfAxes(this.getNode("btn_travel"), this._role.parent));

    TravelModule.api.postTravel(param, success, () => {
      this._is_click = true;
      this.getNode("animation_block").active = false;
    });
  }

  private postTravel_success(res: TravelResUpdateMessage) {
    let placeId = res.placeId == -1 ? 101 : res.placeId;

    let node = this.getNode("btn_site_" + placeId);
    let pos = ToolExt.transferOfAxes(node.getChildByName("crash"), this._role.parent);
    this._role.active = true;

    let distance = Vec3.distance(pos, this._role.getPosition());
    let time = distance / 700;
    this._role
      .getChildByName("renderPoint")
      .getChildByName("render")
      .getComponent(sp.Skeleton)
      .setAnimation(0, "boss_idle", true);
    tween(this._role)
      .to(time, { position: pos })
      .call(() => {
        this.postTravel_award(res);
      })
      .start();
  }

  private postTravel_award(res: TravelResUpdateMessage) {
    tween(this._role.getComponent(UIOpacity))
      .to(0.1, { opacity: 127 })
      .to(0.1, { opacity: 255 })
      .to(0.1, { opacity: 50 })
      .delay(0.1)
      .call(() => {
        this._role.setPosition(ToolExt.transferOfAxes(this.getNode("btn_travel"), this._role.parent));
        this._role.getComponent(UIOpacity).opacity = 255;
        this._role.active = false;
        this._is_click = true;

        if (res.isTalk == true) {
          UIMgr.instance.showDialog("UITravelFriend", { resAddList: res.resAddList }, () => {
            if (res.resAddList.length > 2) {
              UIMgr.instance.showDialog("UITravelGet", { resAddList: res.resAddList }, () => {
                this.getNode("animation_block").active = false;
              });
            }
          });

          return;
        }
        UIMgr.instance.showDialog("UITravelGet", { resAddList: res.resAddList }, () => {
          this.getNode("animation_block").active = false;
        });
      })
      .start();
  }

  private on_click_btn_ten() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let c_right = JsonMgr.instance.jsonList.c_right;
    if (PlayerModule.service.hasRight(112) == false) {
      TipsMgr.showTipX(115, [c_right[112].openList[0][1]]);
      this._tenTravel = 0;
    } else {
      this._tenTravel = this._tenTravel == 0 ? 1 : 0;
    }
    this.setTen();
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    if (this._is_click == false) {
      return;
    }
    UIMgr.instance.back();
  }

  private on_click_btn_test1() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._is_click == false) {
      return;
    }
    TravelModule.api.testReseTravelVitality();
  }

  private on_click_btn_addVit() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._is_click == false) {
      return;
    }
    UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: tilidan, type: UseTypeEnum.UITravel });
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 11 });
  }

  protected onEvtClose(): void {
    for (let i = 0; i < this._tickId_list.length; i++) {
      TickerMgr.clearTicker(this._tickId_list[i]);
    }
    StorageMgr.saveItem(StorageKeyEnum.游历10次, JSON.stringify(this._tenTravel));
  }
}
