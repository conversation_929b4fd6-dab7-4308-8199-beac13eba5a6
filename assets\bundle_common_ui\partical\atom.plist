<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>angle</key>
	<real>360</real>
	<key>angleVariance</key>
	<real>360</real>
	<key>blendFuncDestination</key>
	<integer>1</integer>
	<key>blendFuncSource</key>
	<integer>2</integer>
	<key>duration</key>
	<real>-1</real>
	<key>emitterType</key>
	<real>0.0</real>
	<key>finishColorAlpha</key>
	<real>0.8399999737739563</real>
	<key>finishColorBlue</key>
	<real>0.0771484375</real>
	<key>finishColorGreen</key>
	<real>0.63492840528488159</real>
	<key>finishColorRed</key>
	<real>0.68082684278488159</real>
	<key>finishColorVarianceAlpha</key>
	<real>0.74000000953674316</real>
	<key>finishColorVarianceBlue</key>
	<real>0.98000001907348633</real>
	<key>finishColorVarianceGreen</key>
	<real>0.98000001907348633</real>
	<key>finishColorVarianceRed</key>
	<real>0.41999998688697815</real>
	<key>finishParticleSize</key>
	<real>30.319999694824219</real>
	<key>finishParticleSizeVariance</key>
	<real>0.0</real>
	<key>gravityx</key>
	<real>0.25</real>
	<key>gravityy</key>
	<real>0.86000001430511475</real>
	<key>maxParticles</key>
	<real>200</real>
	<key>maxRadius</key>
	<real>100</real>
	<key>maxRadiusVariance</key>
	<real>0.0</real>
	<key>minRadius</key>
	<real>0.0</real>
	<key>particleLifespan</key>
	<real>0.20000000298023224</real>
	<key>particleLifespanVariance</key>
	<real>0.5</real>
	<key>radialAccelVariance</key>
	<real>65.790000915527344</real>
	<key>radialAcceleration</key>
	<real>-671.04998779296875</real>
	<key>rotatePerSecond</key>
	<real>0.0</real>
	<key>rotatePerSecondVariance</key>
	<real>0.0</real>
	<key>rotationEnd</key>
	<real>-47.369998931884766</real>
	<key>rotationEndVariance</key>
	<real>-142.11000061035156</real>
	<key>rotationStart</key>
	<real>-47.369998931884766</real>
	<key>rotationStartVariance</key>
	<real>0.0</real>
	<key>sourcePositionVariancex</key>
	<real>7</real>
	<key>sourcePositionVariancey</key>
	<real>7</real>
	<key>sourcePositionx</key>
	<real>373.72775268554688</real>
	<key>sourcePositiony</key>
	<real>478.40472412109375</real>
	<key>speed</key>
	<real>0.0</real>
	<key>speedVariance</key>
	<real>190.78999328613281</real>
	<key>startColorAlpha</key>
	<real>0.63999998569488525</real>
	<key>startColorBlue</key>
	<real>0.3375650942325592</real>
	<key>startColorGreen</key>
	<real>0.78792315721511841</real>
	<key>startColorRed</key>
	<real>0.794921875</real>
	<key>startColorVarianceAlpha</key>
	<real>0.77999997138977051</real>
	<key>startColorVarianceBlue</key>
	<real>0.68000000715255737</real>
	<key>startColorVarianceGreen</key>
	<real>1</real>
	<key>startColorVarianceRed</key>
	<real>0.89999997615814209</real>
	<key>startParticleSize</key>
	<real>3.369999885559082</real>
	<key>startParticleSizeVariance</key>
	<real>50</real>
	<key>tangentialAccelVariance</key>
	<real>65.790000915527344</real>
	<key>tangentialAcceleration</key>
	<real>-92.110000610351562</real>
	<key>textureFileName</key>
	<string>atom.png</string>
</dict>
</plist>
