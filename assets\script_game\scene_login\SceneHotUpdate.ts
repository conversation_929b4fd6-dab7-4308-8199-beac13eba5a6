import { _decorator, Component, director, Label, ProgressBar, sp, sys } from "cc";
import {
  GGHotUpdateInstance,
  GGHotUpdateInstanceObserver,
} from "db://gg-hot-update/scripts/hotupdate/GGHotUpdateInstance";
import { ggHotUpdateManager } from "db://gg-hot-update/scripts/hotupdate/GGHotUpdateManager";
import {
  GGHotUpdateInstanceEnum,
  GGHotUpdateInstanceState,
} from "db://gg-hot-update/scripts/hotupdate/GGHotUpdateType";
import { PlatformHttp } from "../../platform/src/lib/http";
import { FmConfig, GameConfig } from "../../GameScrpit/game/GameDefine";
import { ScreenUtil } from "../../GameScrpit/lib/utils/ScreenUtil";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("SceneHotUpdate")
export class SceneHotUpdate extends Component {
  buildInInstance: GGHotUpdateInstance = null;

  @property(ProgressBar)
  progress: ProgressBar = null;

  @property(Label)
  lblProgress: Label = null;

  @property(sp.Skeleton)
  game_loading: sp.Skeleton = null;

  protected onLoad(): void {
    this.progress.node.active = false;
    this.lblProgress.node.active = false;

    ScreenUtil.adaptScreen();

    this.game_loading.setCompleteListener((res: sp.spine.TrackEntry) => {
      if (res.animation.name === "logo_loading_01") {
        this.game_loading.setCompleteListener(null);
        FmConfig.lodingIs = true;
        this.game_loading.setAnimation(0, "logo_loading_02", true);
      }
    });
    setTimeout(() => {
      if (this.isValid == false) {
        return;
      }

      this.game_loading.setCompleteListener(null);
      FmConfig.lodingIs = true;
    }, 3000);

    this.game_loading.setAnimation(0, "logo_loading_01", true);
  }

  async start() {
    // 非安卓直接进入登录场景
    if (![sys.Platform.ANDROID].includes(sys.platform)) {
      let id = setInterval(() => {
        if (FmConfig.lodingIs) {
          clearInterval(id);
          director.loadScene("SceneLogin");
        }
      }, 1000);
      return;
    }

    // 获取最新版本信息
    let versionInfo: any = await PlatformHttp.GET({ url: GameConfig.hotUpdateUrl });
    // let versionInfo: any = { url: "http://192.168.3.110:10086" };
    if (!versionInfo?.url) {
      // 正常流程进不来
      log.log("获取版本信息失败,默认读取最后的");
      versionInfo = { url: "http://m.xmallx.cn/last" };
    }
    log.log("获取版本信息成功", versionInfo);

    this.startHotUpdate(versionInfo.url);
  }

  startHotUpdate(url: string) {
    // 初始化热更新管理器
    ggHotUpdateManager.init({
      enableLog: FmConfig.isDebug,
      packageUrl: url,
    });

    // 获取主包的热更新实例
    this.buildInInstance = ggHotUpdateManager.getInstance(GGHotUpdateInstanceEnum.BuildIn);

    // 创建一个热更新实例观察者
    const observer: GGHotUpdateInstanceObserver = {
      onGGHotUpdateInstanceCallBack: (instance) => {
        switch (instance.state) {
          case GGHotUpdateInstanceState.Idle:
            break;
          case GGHotUpdateInstanceState.CheckUpdateInProgress:
            break;
          case GGHotUpdateInstanceState.CheckUpdateFailedParseLocalProjectManifestError:
          case GGHotUpdateInstanceState.CheckUpdateFailedParseRemoteVersionManifestError:
          case GGHotUpdateInstanceState.CheckUpdateFailedDownloadRemoteProjectManifestError:
          case GGHotUpdateInstanceState.CheckUpdateFailedParseRemoteProjectManifestError: {
            log.log("检查更新：失败", instance.state);

            instance.unregister(observer);

            this.buildInInstance.destroy();
            setTimeout(() => {
              director.loadScene("SceneLogin");
            }, 1000);
            break;
          }
          case GGHotUpdateInstanceState.CheckUpdateSucNewVersionFound: {
            log.log("检查更新：成功，发现新版本，将开始热更新");
            instance.unregister(observer);

            // 显示进度条
            this.progress.node.active = true;
            this.lblProgress.node.active = true;
            this.updateRes();
            break;
          }
          case GGHotUpdateInstanceState.CheckUpdateSucAlreadyUpToDate: {
            log.log("检查更新：成功，当前已经是最新版本");

            this.buildInInstance.destroy();

            let id = setInterval(() => {
              if (FmConfig.lodingIs) {
                clearInterval(id);
                director.loadScene("SceneLogin");
              }
            }, 1000);

            // setTimeout(() => {}, 1000);
            break;
          }
        }
      },
    };

    // 注册热更新实例观察者到主包中，监听主包热更新实例的状态
    this.buildInInstance.register(observer);

    // 开始检查更新
    this.buildInInstance.checkUpdate();
  }

  updateRes() {
    // 热更新失败后，重试间隔(秒)
    const hotUpdateRetryIntervalInSecond = 2;
    // 热更新失败后，最大重试次数
    const hotUpdateRetryMaxTimes = 3;
    // 热更新失败后，累计重试次数
    let hotUpdateRetryCurTimes = 0;

    // 创建一个热更新实例观察者
    const observer: GGHotUpdateInstanceObserver = {
      onGGHotUpdateInstanceCallBack: (instance) => {
        switch (instance.state) {
          // ...
          case GGHotUpdateInstanceState.CheckUpdateSucNewVersionFound: {
            log.log("检查更新：成功，发现新版本，将开始热更新");
            instance.hotUpdate();
            break;
          }
          // ...
          case GGHotUpdateInstanceState.HotUpdateInProgress: {
            let info = "热更新：进度回调：";
            info += ` 总字节数：${instance.totalBytes}`;
            info += ` 已下载字节数: ${instance.downloadedBytes}`;
            info += ` 总下载文件数：${instance.totalFiles}`;
            info += ` 下载成功文件数：${instance.downloadSucFiles.length}`;
            info += ` 下载失败文件数：${instance.downloadFailedFiles.length}`;
            info += ` 当前下载速度：${(instance.downloadSpeedInSecond / 1024 / 1024).toFixed(2)} MB/s`;
            info += ` 当前剩余时间：${instance.downloadRemainTimeInSecond}s`;
            log.log(info);

            let progressNow = instance.downloadedBytes / instance.totalBytes;
            if (progressNow < 0.18) {
              progressNow = 0.09 + progressNow / 2;
            }
            this.progress.progress = progressNow;

            // 下载速度
            this.lblProgress.string = `正在更新：${(instance.downloadSpeedInSecond / 1024 / 1024).toFixed(2)} MB/s (${(
              instance.downloadedBytes /
              1024 /
              1024
            ).toFixed(2)}MB/${(instance.totalBytes / 1024 / 1024).toFixed(2)}MB) `;
            break;
          }
          case GGHotUpdateInstanceState.HotUpdateSuc: {
            log.log("热更新：成功，将重启游戏");
            instance.unregister(observer);
            // 如果是主包，那么新资源需要重启后才可以生效，此时我们建议等一小段时间在重启，比如这样子
            this.scheduleOnce(() => {
              ggHotUpdateManager.restartGame();
            });
            // 如果是子包，此时可以加载子包的 AssetBundle 资源了
            // e.g. assetManager.loadBundle(instance.name);
            break;
          }
          case GGHotUpdateInstanceState.HotUpdateFailed: {
            log.log("热更新：失败");

            hotUpdateRetryCurTimes++;
            if (hotUpdateRetryCurTimes < hotUpdateRetryMaxTimes) {
              log.log("热更新：失败，将重试");
              this.scheduleOnce(() => {
                hotUpdateRetryCurTimes++;
                instance.hotUpdate();
              }, hotUpdateRetryIntervalInSecond);
            } else {
              instance.unregister(observer);
            }

            break;
          }
        }
      },
    };

    // 注册观察者
    this.buildInInstance.register(observer);

    // 开始热更新
    this.buildInInstance.hotUpdate();
  }

  protected onDestroy(): void {
    if (this.game_loading.isValid == true) {
      this.game_loading.setCompleteListener(null);
    }
  }
}
