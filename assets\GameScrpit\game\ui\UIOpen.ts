import { _decorator, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { tween } from "cc";
import { Vec3 } from "cc";
import { UIMgr } from "../../lib/ui/UIMgr";
import { v3 } from "cc";
import { UITransform } from "cc";
import { UIOpacity } from "cc";
import ToolExt from "../common/ToolExt";
import { randomRangeInt } from "cc";
import { Label } from "cc";

const { ccclass, property } = _decorator;

/**
 *
 * hopewsw
 * Wed Oct 09 2024 19:56:44 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/UIOpen.ts
 *
 */
export interface ArgsUIOpen {
  nodeIconIn: Node;
  worldPos: Vec3;
  name: string;
}

@ccclass("UIOpen")
export class UIOpen extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UIOpen`;
  }

  private args: ArgsUIOpen;
  private _nodeIconIn: Node;
  private _isHide = false;

  //=================================================
  public init(args: ArgsUIOpen): void {
    super.init(args);

    this.args = args;

    this._nodeIconIn = args.nodeIconIn;
    this._nodeIconIn.off("click");
    this._nodeIconIn.setPosition(0, 0);
    this._nodeIconIn.active = true;
  }

  protected onEvtShow(): void {
    super.onEvtShow();
    this.getNode("node_icon").addChild(this._nodeIconIn);

    let nodeInSize = this._nodeIconIn.getComponent(UITransform).contentSize;
    let nodeSize = this.getNode("node_icon").getComponent(UITransform).contentSize;
    let rate = nodeSize.width / nodeInSize.width;

    this.getNode("node_icon").setScale(rate, rate, 1);

    this.getNode("lbl_jiesuogongneng").getComponent(Label).string = this.args.name;
  }

  private playOpacityNodeAni(node: Node) {
    let uiOpacity = node.addComponent(UIOpacity);
    tween(uiOpacity).to(0.3, { opacity: 0 }).start();
  }

  private on_click_btn_go() {
    if (this._isHide) {
      return;
    }
    this._isHide = true;

    const nodeIcon = this.getNode("node_icon");

    this.playOpacityNodeAni(nodeIcon);
    this.playOpacityNodeAni(this.getNode("node_other"));

    // 缩小
    tween(nodeIcon)
      .to(0.3, { scale: v3(0.2, 0.2, 0.2) })
      .delay(0.15)
      .start();

    let pointCtrl: Vec3 = v3();
    pointCtrl.x = randomRangeInt(nodeIcon.worldPosition.x - 400, nodeIcon.worldPosition.x + 400);
    if (this.args.worldPos.y > nodeIcon.worldPosition.y) {
      pointCtrl.y = nodeIcon.worldPosition.y - 500;
    } else {
      pointCtrl.y = nodeIcon.worldPosition.y + 500;
    }

    let pointStart = nodeIcon.worldPosition;

    tween(this.getNode("Particle2D"))
      .delay(0.5)
      .to(
        1,
        {},
        {
          onUpdate: (target: Node, ratio) => {
            target.setWorldPosition(ToolExt.twoBezier(ratio, pointStart, pointCtrl, this.args.worldPos));
          },
        }
      )
      .delay(0.3)
      .call(() => {
        UIMgr.instance.back();
      })
      .start();
  }
}
