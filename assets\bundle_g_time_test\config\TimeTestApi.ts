import { _decorator, Component, Node } from "cc";
import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess, Net_Code } from "../../GameScrpit/game/mgr/ApiHandler";
import {
  ActivityTakeResponse,
  TimeTaskMessage,
  TimeTaskTakeRequest,
  TimeTestRequest,
} from "../../GameScrpit/game/net/protocol/Activity";
import { ActivityCmd } from "../../GameScrpit/game/net/cmd/CmdData";
import { LongValue } from "../../GameScrpit/game/net/protocol/ExternalMessage";
import { ActivityID } from "../../GameScrpit/module/activity/ActivityConstant";
import { TimeTestModule } from "./TimeTestModule";
const { ccclass, property } = _decorator;

@ccclass("TimeTestApi")
export class TimeTestApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   console.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       console.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       console.log(`${errorCode}`);
  //       console.log(data);
  //     }
  //   );
  // }
  // 路由: 14 - 35  --- 【获取限时任务信息】 --- 【ActivityAction:950】【timeTaskInfo】
  //     方法参数: LongValue
  //     方法返回值: TimeTaskMessage
  // timeTaskInfo = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 35),
  // 获取限时测试任务数据 (使用正式版路由)
  timeTestInfo(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    // 请求服务器
    ApiHandler.instance.requestSync(
      TimeTaskMessage,
      ActivityCmd.timeTaskInfo,
      LongValue.encode({ value: ActivityID.TIME_TASK_1 }),
      (data: TimeTaskMessage) => {
        // 保存到本地数据模块
        TimeTestModule.data.timeTestMessage = data;
        success && success(data);
      }
    );
  }

  // 路由: 14 - 36  --- 【领取限时任务奖励】 --- 【ActivityAction:951】【timeTaskTake】
  //     方法参数: LongValue
  //     方法返回值: TimeTaskMessage
  timeTaskTake(taskId: number, index: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    // 构建测试版本专用的请求
    let request: TimeTestRequest = {
      activityId: ActivityID.TIME_TASK_1,
      taskId: taskId,
      index: index,
      takeAll: false,
    };

    // 请求服务器
    ApiHandler.instance.requestSync(
      ActivityTakeResponse,
      ActivityCmd.timeTestTake,
      TimeTestRequest.encode(request),
      (data: ActivityTakeResponse) => {
        TimeTestModule.data.updateTakeList(taskId, data?.takeList);
        success && success(data);
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        error && error(errorCode, msg, data);
        return false;
      }
    );
  }
}
