import { _decorator, Component, Node } from "cc";
import { <PERSON>piHandler, ApiHandlerFail, ApiHandlerSuccess, Net_Code } from "../../GameScrpit/game/mgr/ApiHandler";
import {
  ActivityTakeResponse,
  TimeTaskMessage,
  TimeTaskTakeRequest,
} from "../../GameScrpit/game/net/protocol/Activity";
import { ActivityCmd } from "../../GameScrpit/game/net/cmd/CmdData";
import { LongValue } from "../../GameScrpit/game/net/protocol/ExternalMessage";
import { ActivityID } from "../../GameScrpit/module/activity/ActivityConstant";
import { TimeTestModule } from "./TimeTestModule";
const { ccclass, property } = _decorator;

@ccclass("TimeTestApi")
export class TimeTestApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   console.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       console.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       console.log(`${errorCode}`);
  //       console.log(data);
  //     }
  //   );
  // }
  // 路由: 14 - 35  --- 【获取限时任务信息】 --- 【ActivityAction:950】【timeTaskInfo】
  //     方法参数: LongValue
  //     方法返回值: TimeTaskMessage
  // timeTaskInfo = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 35),
  // 获取限时测试任务数据 (完全使用正式版的请求方式)
  timeTestInfo(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    console.log("=== 开始请求 TimeTest 数据 ===");
    console.log("请求路由:", "ActivityCmd.timeTaskInfo");
    console.log("请求参数:", { value: ActivityID.TIME_TASK_1 });

    ApiHandler.instance.requestSync(
      TimeTaskMessage,
      ActivityCmd.timeTaskInfo,
      LongValue.encode({ value: ActivityID.TIME_TASK_1 }),
      (data: TimeTaskMessage) => {
        //
        TimeTestModule.data.timeTestMessage = data;
        console.log("=== TimeTest API 数据获取成功 ===");
        console.log("完整数据:", data);
        console.log("活动ID:", data.activityId);
        console.log("completeMap:", data.completeMap);
        console.log("completeMap 类型:", typeof data.completeMap);
        console.log("completeMap 是否为空对象:", Object.keys(data.completeMap || {}).length === 0);
        console.log("completeMap 键数量:", Object.keys(data.completeMap || {}).length);

        // 打印每个任务的详细信息
        if (data.completeMap && Object.keys(data.completeMap).length > 0) {
          Object.keys(data.completeMap).forEach((taskId) => {
            const taskData = data.completeMap[parseInt(taskId)];
            console.log(`任务 ${taskId}:`, {
              targetVal: taskData.targetVal,
              takeList: taskData.takeList,
            });
          });
        } else {
          console.warn("⚠️ completeMap 为空或不存在！这可能是服务器数据问题");
          console.warn("尝试创建默认数据...");

          // 临时解决方案：创建默认的任务数据
          if (!data.completeMap) {
            data.completeMap = {};
          }

          // 为常见的 taskId 创建默认数据
          const defaultTaskIds = [101, 102, 103, 104, 105];
          defaultTaskIds.forEach((taskId) => {
            if (!data.completeMap[taskId]) {
              data.completeMap[taskId] = {
                targetVal: 0, // 默认进度为0
                takeList: [], // 默认没有领取任何奖励
              };
              console.log(`创建默认任务数据: taskId=${taskId}`);
            }
          });
        }
        console.log("=== 数据打印结束 ===");

        success && success(data);
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        console.error("=== TimeTest API 请求失败 ===");
        console.error("错误码:", errorCode);
        console.error("错误信息:", msg);
        console.error("错误数据:", data);

        // 请求失败时也创建默认数据
        const defaultData: TimeTaskMessage = {
          activityId: ActivityID.TIME_TASK_1,
          completeMap: {},
        };

        const defaultTaskIds = [101, 102, 103, 104, 105];
        defaultTaskIds.forEach((taskId) => {
          defaultData.completeMap[taskId] = {
            targetVal: 0,
            takeList: [],
          };
          console.log(`请求失败，创建默认任务数据: taskId=${taskId}`);
        });

        TimeTestModule.data.timeTestMessage = defaultData;
        console.log("=== 使用默认数据 ===");

        error && error(errorCode, msg, data);
        return false;
      }
    );
  }

  // 路由: 14 - 36  --- 【领取限时任务完成奖励】 --- 【ActivityAction:970】【takeTimeTask】
  //     方法参数: TimeTaskTakeRequest
  //     方法返回值: ActivityTakeResponse
  // takeTimeTask = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 36),
  takeTimeTask(taskId: number, index: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let request: TimeTaskTakeRequest = {
      activityId: ActivityID.TIME_TASK_1,
      taskId: taskId,
      index: index,
      takeAll: false,
    };
    console.log(request);
    ApiHandler.instance.requestSync(
      ActivityTakeResponse,
      ActivityCmd.takeTimeTask,
      TimeTaskTakeRequest.encode(request),
      (data: ActivityTakeResponse) => {
        //
        TimeTestModule.data.updateTakeList(taskId, data?.takeList);
        success && success(data);
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        //
        error && error(errorCode, msg, data);
        return false;
      }
    );
  }
}
