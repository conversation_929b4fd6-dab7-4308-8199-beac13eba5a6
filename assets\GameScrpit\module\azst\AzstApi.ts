import { <PERSON><PERSON><PERSON>and<PERSON> } from "../../game/mgr/ApiHandler";
import { CompeteActionSubCmd } from "../../game/net/cmd/CmdData";
import {
  CompeteBattleResponse,
  CompeteExtraMessage,
  CompeteLogMessage,
  CompeteOwnRankMessage,
  CompeteReplayMessage,
  CompeteSyncRefreshCountResponse,
} from "../../game/net/protocol/Compete";
import { <PERSON><PERSON>Val<PERSON>, ByteValueList, LongValue } from "../../game/net/protocol/ExternalMessage";
import { AzstModule } from "./AzstModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class AzstApi {
  /**返回玩家免费刷新次数,对手信息,最近一次免费刷新的时间 */
  public getAzstMessage(success) {
    ApiHandler.instance.requestSync(
      CompeteExtraMessage,
      CompeteActionSubCmd.extraInfo,
      null, //入参
      (res: CompeteExtraMessage) => {
        AzstModule.data.azstMessage = res;
        success && success(res);
      }
    );
  }

  /**获取日志信息 */
  public getLogMessage(success) {
    ApiHandler.instance.request(ByteValueList, CompeteActionSubCmd.getLog, null, (res: ByteValueList) => {
      let arr: CompeteLogMessage[] = [];
      for (let i = 0; i < res.values.length; i++) {
        arr.push(CompeteLogMessage.decode(res.values[i]));
      }
      AzstModule.data.logMessage = arr;
      success && success(res);
    });
  }

  /**获取本服玩家排名和信息 */
  public getRankInfo(success) {
    ApiHandler.instance.request(
      CompeteOwnRankMessage,
      CompeteActionSubCmd.rankInfo,
      null,
      (res: CompeteOwnRankMessage) => {
        AzstModule.data.rankMessage = res;
        success && success(res);
      }
    );
  }

  /**同步前后端的剩余免费刷新次数的进度 */
  public postSyncRefreshCount() {
    ApiHandler.instance.request(
      CompeteSyncRefreshCountResponse,
      CompeteActionSubCmd.syncRefreshCount,
      null, //入参
      (res: CompeteSyncRefreshCountResponse) => {
        AzstModule.data.freeFreshCount = res.freeFreshCount;
        AzstModule.data.lastUpdateTime = res.lastUpdateTime;
      }
    );
  }

  /**演武场战斗 */
  public azstFight(userId: number, success) {
    ApiHandler.instance.requestSync(
      CompeteReplayMessage,
      CompeteActionSubCmd.fight,
      LongValue.encode({ value: userId }),
      (res: CompeteReplayMessage) => {
        success && success(res);
      }
    );
  }

  /**免费刷新对手 */
  public freeUp(freebool: boolean, success?) {
    ApiHandler.instance.requestSync(
      CompeteExtraMessage,
      CompeteActionSubCmd.free,
      BoolValue.encode({ value: freebool }),
      (res: CompeteExtraMessage) => {
        log.log("刷新对手===", res);
        AzstModule.data.battlerList = res.battlerList;
        AzstModule.data.lastUpdateTime = res.lastUpdateTime;
        AzstModule.data.freeFreshCount = res.freeFreshCount;
        success && success();
      }
    );
  }

  /**切磋 */
  public revenge(logId: number, success) {
    ApiHandler.instance.requestSync(
      CompeteReplayMessage, //消息类型用于decode
      CompeteActionSubCmd.revenge, //接口路由
      LongValue.encode({ value: logId }),
      (res: CompeteReplayMessage) => {
        success && success(res);
      },
      (code, msg) => {
        log.log(`${code} ${msg}`);
        return true;
      }
    );
  }

  /**对战后获取积分扣减、获取对手等 */
  public afterFight(userId: number, success) {
    ApiHandler.instance.requestSync(
      CompeteBattleResponse, //消息类型用于decode
      CompeteActionSubCmd.afterFight, //接口路由
      LongValue.encode({ value: userId }),
      (res: CompeteBattleResponse) => {
        if (res.battlerList.length != 0) {
          AzstModule.data.battlerList = res.battlerList;
        }
        AzstModule.data.setPointRank(res.point, res.rank);
        success && success(res);
      },
      (code, msg) => {
        log.error(`=========对战后获取积分扣减、获取对手等失败=========`);
        log.error(`${code} ${msg}`);
        return true;
      }
    );
  }

  /**切磋后库存扣减，，前端需要将日志的revenge属性设为true */
  public afterRevenge(logId: number, success) {
    ApiHandler.instance.requestSync(
      CompeteBattleResponse, //消息类型用于decode
      CompeteActionSubCmd.afterRevenge, //接口路由
      LongValue.encode({ value: logId }),
      (res: CompeteBattleResponse) => {
        if (res.battlerList.length != 0) {
          AzstModule.data.battlerList = res.battlerList;
        }
        AzstModule.data.setLogRevenge(logId);
        AzstModule.data.setPointRank(res.point, res.rank);
        success && success(res);
      },
      (code, msg) => {
        log.error(`切磋结束了请求失败-------`);
        log.error(`${code} ${msg}`);
        return true;
      }
    );
  }
}
