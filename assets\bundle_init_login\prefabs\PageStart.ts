import { _decorator, Component, is<PERSON><PERSON><PERSON>, <PERSON>de, tween, UIOpacity } from "cc";
import { RouteItem } from "../../platform/src/RouteHelper";
import { EventMgr, MsgEnum } from "../../platform/src/EventHelper";
import CenterHttpApi from "../../GameScrpit/game/httpNet/CenterHttpApi";
import StorageMgr, { StorageKeyEnum } from "../../platform/src/StorageHelper";
import { Label } from "cc";
import GameHttpApi from "../../GameScrpit/game/httpNet/GameHttpApi";
import SocketClient from "../../GameScrpit/lib/socket/SocketClient";
import { GHttp } from "../../GameScrpit/lib/http/GHttp";
import { director } from "cc";
import { Scene } from "cc";
import { RouteSceneLogin } from "../../script_game/scene_login/RouteSceneLogin";
import { SceneLogin } from "../../script_game/scene_login/SceneLogin";
import { BundleEnum, ResHelper } from "../../platform/src/ResHelper";
import { AssetManager } from "cc";
import { TipsMgr } from "../../platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { LangMgr } from "../../GameScrpit/game/mgr/LangMgr";

const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

export type ServerItem = {
  serverId: "";
  serverName: "";
  serverGroup: "";
  websocket: "";
  status: "";
  gameHttpUrl?: string;
};

@ccclass("PageStart")
export class PageStart extends Component {
  @property(Label)
  lblName: Label;

  @property(Node)
  btn_huanyingwanjia: Node;

  @property(Label)
  playerName: Label;

  @property(Label)
  lab1: Label;

  @property(Label)
  lab2: Label;

  private welcomeTw;

  selectServer: ServerItem;

  cd: number = 0;

  start() {
    this.lab1.string = LangMgr.txMsgCode(262, []);
    this.lab2.string = LangMgr.txMsgCode(263, []);

    this.btn_huanyingwanjia.active = false;
    this.btn_huanyingwanjia.getComponent(UIOpacity).opacity = 0;
    this.changeServer(this.selectServer);
    this.onLogin();
    EventMgr.on(MsgEnum.ROUTE_UPDATE, this.onRouteShow, this);
    ResHelper.loadBundle(BundleEnum.SCENE_LOADING, (bundle: AssetManager.Bundle) => {
      bundle.preloadScene("SceneLoading");
    });
  }

  onLogin() {
    if (this.cd > new Date().getTime()) {
      // todo  显示加载界面
      TipsMgr.showTip("==== 正在登录中");
      return;
    }
    TipsMgr.setEnableTouch(false, 5);
    let account = StorageMgr.loadStr(StorageKeyEnum.LoginName);
    account = account == "" ? null : StorageMgr.loadStr(StorageKeyEnum.LoginName);

    let psd = StorageMgr.loadStr(StorageKeyEnum.Psd);
    psd = psd == "" ? null : StorageMgr.loadStr(StorageKeyEnum.Psd);

    if (account) {
      log.log("==== 登录数据", account, psd);
      CenterHttpApi.login(account, psd).then(async (res: any) => {
        TipsMgr.setEnableTouch(true);
        if (res.code === 200) {
          log.log("==== 登录成功");
          StorageMgr.saveItem(StorageKeyEnum.LoginName, account);
          StorageMgr.saveItem(StorageKeyEnum.Psd, psd);
          StorageMgr.userId = res.data.accountId;
          this.httpSever();
          this.welcomeHit(account);
        } else {
          TipsMgr.showTip("登录失败");
        }
      });
    } else {
      TipsMgr.setEnableTouch(true);
    }
  }

  private welcomeHit(account: string) {
    this.playerName.string = account;

    let node = this.btn_huanyingwanjia;
    node.active = true;

    let opComp = node.getComponent(UIOpacity);
    opComp.opacity = 0;
    this.welcomeTw = tween(opComp)
      .to(0.5, { opacity: 255 })
      .delay(1.5)
      .to(0.5, { opacity: 0 })
      .call(() => {
        node.active = false;
      })
      .start();
  }

  onRouteShow(routeItem: RouteItem = null) {
    if (routeItem.key != RouteSceneLogin.PageStart.key) {
      return;
    }
    this.httpSever();
  }

  private httpSever() {
    TipsMgr.setEnableTouch(false, 5);
    CenterHttpApi.serverList().then((res: any) => {
      TipsMgr.setEnableTouch(true);
      if (res.code == 200) {
        let jsonStr = StorageMgr.loadStr(StorageKeyEnum.SelectServer);
        if (jsonStr) {
          let serverInfo = JSON.parse(jsonStr);
          if (typeof serverInfo == "string") {
            this.changeServer(res.data[0].serverList[0]);
          } else {
            this.changeServer(serverInfo);
          }
        } else {
          this.changeServer(res.data[0].serverList[0]);
        }
      }
    });
  }

  changeServer(serverInfo: ServerItem) {
    if (!isValid(this.lblName.node)) {
      return;
    }
    if (!serverInfo || !serverInfo.serverId) {
      this.node.getChildByPath("btn_switch/lbl_denglu").getComponent(Label).string = "未登录";
      this.lblName.string = "";
      return;
    } else {
      this.node.getChildByPath("btn_switch/lbl_denglu").getComponent(Label).string = "已登录";
    }
    this.selectServer = serverInfo;
    this.lblName.string = serverInfo.serverName;
    StorageMgr.saveItem(StorageKeyEnum.SelectServer, JSON.stringify(this.selectServer));
  }

  onSwitchArea() {
    SceneLogin.routeMgr.showDialog(RouteSceneLogin.PageServer, (args) => {
      if (args) {
        this.changeServer(args);
      }
    });
  }

  onGoLogin() {
    SceneLogin.routeMgr.showDialog(RouteSceneLogin.PageLogin);
  }

  onGoRegister() {
    SceneLogin.routeMgr.showDialog(RouteSceneLogin.PageRegister);
  }

  async onStart() {
    if (!(this.selectServer?.websocket || null)) {
      this.onGoLogin();
      return;
    }

    if (this.cd > new Date().getTime()) {
      // todo  显示加载界面
      TipsMgr.showTip("正在连接中");
      return;
    }

    // 冷却3秒
    this.cd = new Date().getTime() + 3000;

    // log.info("this.selectServer", this.selectServer);

    // 设置HTTP接口
    GameHttpApi.gameHttpUrl =
      "http" + this.selectServer.websocket.substring(2, this.selectServer.websocket.length - 9) + "api";

    if (this.selectServer.gameHttpUrl) {
      GameHttpApi.gameHttpUrl = this.selectServer.gameHttpUrl;
    }

    log.info("GameHttpApi.gameHttpUrl", GameHttpApi.gameHttpUrl);
    log.info("selectServer.websocket", this.selectServer?.websocket);
    log.info("selectServer.serverId", this.selectServer?.serverId);
    log.info("selectServer.serverName", this.selectServer?.serverName);

    SocketClient.ins.onOpenCallback = () => {
      SocketClient.ins.onOpenCallback = null;
      ResHelper.loadBundle("scene_loading", (bundle: AssetManager.Bundle) => {
        director.loadScene("SceneLoading", (error: null | Error, scene?: Scene) => {});
      });
    };

    await SocketClient.ins.connect(
      this.selectServer.websocket + "?t=" + GHttp.tokenInfo.token + "&i=" + GHttp.tokenInfo.id
    );
  }

  private openGongGao() {
    CenterHttpApi.announcement(0)
      .then((resp: any) => {
        if (resp.code != 200) {
          log.error(resp);
          return;
        }
        if (isValid(SceneLogin.instance?.node) && SceneLogin.routeMgr) {
          const routeInfo = RouteSceneLogin.PageNotice;
          routeInfo.args = resp.data;
          SceneLogin.routeMgr.showDialog(routeInfo);
        }
      })
      .catch(() => {
        log.error("公告获取失败");
      });
    return;
  }

  protected onDestroy() {
    if (this.welcomeTw) {
      this.welcomeTw.stop();
    }
  }
}
