{"skeleton": {"hash": "znjw1b+tz6w8rdWOqhno+yxnvTA", "spine": "3.8.75", "x": -28.33, "y": -2.96, "width": 64.75, "height": 54.81, "images": "./images/", "audio": "D:/spine导出/小偷"}, "bones": [{"name": "root", "scaleX": 0.5, "scaleY": 0.5}, {"name": "all", "parent": "root", "x": 82.25, "y": 27.37, "scaleX": 0.2975, "scaleY": 0.2975}, {"name": "bone", "parent": "all", "length": 81.55, "rotation": 0.49, "x": -266.44, "y": -4.12}, {"name": "bone2", "parent": "bone", "length": 34.92, "rotation": 94.16, "x": -2.22, "y": 3.51}, {"name": "bone3", "parent": "bone2", "length": 26.42, "rotation": 0.27, "x": 34.92}, {"name": "bone4", "parent": "bone3", "length": 17.89, "rotation": -0.38, "x": 26.42}, {"name": "bone5", "parent": "bone3", "x": 11.43, "y": -32.23}, {"name": "bone6", "parent": "bone5", "length": 28.43, "rotation": -162.69, "x": -7.23, "y": -4.78, "color": "abe323ff"}, {"name": "bone7", "parent": "bone6", "length": 32.27, "rotation": -20.72, "x": 28.43}, {"name": "bone8", "parent": "bone3", "x": 12.46, "y": 19.38}, {"name": "bone9", "parent": "bone8", "length": 29.65, "rotation": 151.45, "x": -12.12, "y": 4.45}, {"name": "bone10", "parent": "bone9", "length": 28.3, "rotation": -115.83, "x": 29.65}, {"name": "bone11", "parent": "bone10", "rotation": 130.54, "x": 27.17, "y": -6.11, "transform": "noRotationOrReflection"}, {"name": "bone12", "parent": "bone11", "x": -15.53, "y": 24.72, "color": "ff0000ff"}, {"name": "bone13", "parent": "bone11", "x": 2.93, "y": -30.14, "color": "ff0000ff"}, {"name": "bone14", "parent": "bone3", "x": 23.91, "y": -7.98, "scaleX": 1.0965, "scaleY": 1.0965, "color": "ff0000ff"}, {"name": "bone15", "parent": "bone14", "x": -97.46, "y": -58.69, "color": "ff0000ff"}, {"name": "bone16", "parent": "bone", "length": 13.32, "rotation": -128.43, "x": -33.61, "y": -8.63}, {"name": "bone17", "parent": "bone", "length": 13.53, "rotation": -63.92, "x": 37.06, "y": -7.81}, {"name": "bone18", "parent": "bone", "length": 58.47, "rotation": -89.46, "x": -19.15, "y": -2.85}, {"name": "bone19", "parent": "bone18", "length": 32.04, "rotation": -70.52, "x": 58.47}, {"name": "bone20", "parent": "bone", "length": 58.47, "rotation": -75.07, "x": 14.33, "y": -7.78, "color": "abe323ff"}, {"name": "bone21", "parent": "bone20", "length": 32.04, "rotation": -79.31, "x": 58.47}, {"name": "bone22", "parent": "all", "x": -211.18, "y": -26.64, "scaleX": -1.6511, "scaleY": 1.6511}, {"name": "bone25", "parent": "all", "length": 554.36, "rotation": 135.31, "x": -266.42, "y": -875.26, "scaleX": 1.5782, "scaleY": 1.5782}, {"name": "bone24", "parent": "bone25", "length": 174.02, "rotation": -97.67, "x": 546.36, "y": -9.95}, {"name": "bone26", "parent": "all", "length": 554.36, "rotation": -134.94, "x": -259.68, "y": -870.11, "scaleX": -1.5524, "scaleY": 1.5524}, {"name": "bone27", "parent": "bone26", "length": 174.02, "rotation": -97.67, "x": 546.36, "y": -9.95}, {"name": "bone29", "parent": "all", "x": -278.88, "y": 125.84}, {"name": "bone23", "parent": "bone29", "rotation": 5.65, "x": -27.15, "y": 29.42, "scaleX": 1.4676, "scaleY": 1.4676}, {"name": "bone28", "parent": "bone29", "rotation": 61.4, "x": -31.27, "y": -18.74, "scaleX": 1.6623, "scaleY": 1.6623}, {"name": "bone30", "parent": "all", "rotation": -155.25, "x": -268.5, "y": 104.44}, {"name": "bone31", "parent": "bone30", "rotation": 5.65, "x": -27.15, "y": 29.42, "scaleX": 2.0888, "scaleY": 2.0888}, {"name": "bone32", "parent": "bone30", "rotation": 61.4, "x": -31.27, "y": -18.74, "scaleX": 1.4676, "scaleY": 1.4676}], "slots": [{"name": "sd", "bone": "all", "color": "ffffff84", "attachment": "sd"}, {"name": "d2", "bone": "all", "attachment": "d2"}, {"name": "s4", "bone": "all", "attachment": "s4"}, {"name": "j2", "bone": "all"}, {"name": "j1", "bone": "bone18", "attachment": "j1"}, {"name": "j4", "bone": "bone20", "attachment": "j1"}, {"name": "b1", "bone": "all", "attachment": "b1"}, {"name": "s3", "bone": "all", "attachment": "s3"}, {"name": "s2", "bone": "all", "attachment": "s2"}, {"name": "d1", "bone": "all", "attachment": "d1"}, {"name": "s1", "bone": "all", "attachment": "s1"}, {"name": "tou", "bone": "bone4", "attachment": "tou"}, {"name": "shoujikl", "bone": "bone4"}, {"name": "yanwu/qipaoyan_00036", "bone": "bone22"}, {"name": "bao1", "bone": "bone4", "attachment": "bao1"}, {"name": "bao2", "bone": "bone4", "attachment": "bao2"}, {"name": "bao3", "bone": "bone4", "attachment": "bao3"}, {"name": "quantou", "bone": "bone24"}, {"name": "quantou2", "bone": "bone27"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "bone23", "color": "ffffff00", "attachment": "<PERSON><PERSON><PERSON><PERSON>", "blend": "additive"}, {"name": "shoujiq3", "bone": "bone31", "color": "ffffff00", "attachment": "<PERSON><PERSON><PERSON><PERSON>", "blend": "additive"}, {"name": "shoujiq2", "bone": "bone28", "color": "ffffff00", "attachment": "<PERSON><PERSON><PERSON><PERSON>", "blend": "additive"}, {"name": "shoujiq4", "bone": "bone32", "color": "ffffff00", "attachment": "<PERSON><PERSON><PERSON><PERSON>", "blend": "additive"}], "transform": [{"name": "bone6", "order": 1, "bones": ["bone9"], "target": "bone6", "rotation": -45.85, "x": -14.42, "y": -59.23, "rotateMix": -0.533, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "bone20", "bones": ["bone18"], "target": "bone20", "rotation": -14.39, "x": -13.39, "y": -31.08, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"j1": {"j1": {"type": "mesh", "uvs": [0.52406, 0.01104, 0.7779, 0.01123, 0.95246, 0.13745, 0.98478, 0.24454, 0.98462, 0.35524, 0.95616, 0.47891, 0.9336, 0.57698, 0.84167, 0.64385, 0.8418, 0.69098, 0.84189, 0.72667, 0.87339, 0.79143, 0.8733, 0.91474, 0.74643, 0.95944, 0.5366, 0.98865, 0.27276, 0.98862, 0.15061, 0.97445, 0.05664, 0.94021, 0.0156, 0.90685, 0.01503, 0.8053, 0.10972, 0.73562, 0.27091, 0.67767, 0.33209, 0.67169, 0.33151, 0.64784, 0.33086, 0.62097, 0.23719, 0.55947, 0.23798, 0.40213, 0.30131, 0.26132, 0.39113, 0.10718, 0.687, 0.15007, 0.61478, 0.30248, 0.57764, 0.43995, 0.56113, 0.6342, 0.56113, 0.73432, 0.56113, 0.6865, 0.54462, 0.818, 0.37335, 0.8434, 0.17113, 0.85535, 0.71589, 0.85685, 0.70764, 0.7403, 0.69732, 0.69397], "triangles": [21, 33, 32, 36, 19, 20, 20, 21, 35, 13, 34, 37, 14, 35, 13, 13, 35, 34, 15, 36, 14, 14, 36, 35, 15, 16, 36, 16, 17, 36, 17, 18, 36, 36, 20, 35, 18, 19, 36, 34, 35, 32, 13, 37, 12, 34, 38, 37, 12, 37, 11, 11, 37, 10, 32, 35, 21, 10, 37, 38, 34, 32, 38, 21, 22, 31, 30, 29, 5, 5, 29, 4, 3, 4, 29, 3, 29, 28, 30, 26, 29, 3, 28, 2, 26, 27, 29, 29, 27, 28, 27, 0, 28, 28, 1, 2, 28, 0, 1, 24, 25, 30, 23, 24, 30, 10, 38, 9, 32, 39, 38, 38, 39, 9, 32, 33, 39, 39, 8, 9, 8, 39, 7, 7, 39, 31, 21, 31, 33, 39, 33, 31, 22, 23, 31, 6, 7, 30, 7, 31, 30, 31, 23, 30, 6, 30, 5, 25, 26, 30], "vertices": [1, 19, -4.8, -11.56, 1, 1, 19, -5.62, 4.41, 1, 1, 19, 4.78, 15.97, 1, 1, 19, 14.01, 18.51, 1, 1, 19, 23.71, 19.02, 1, 1, 19, 34.69, 17.74, 1, 1, 19, 43.37, 16.65, 1, 2, 19, 49.46, 11.04, 0.99891, 20, -13.41, -4.81, 0.00109, 2, 19, 53.58, 11.17, 0.95584, 20, -12.17, -0.89, 0.04416, 2, 19, 56.69, 11.28, 0.83862, 20, -11.23, 2.08, 0.16138, 2, 19, 62.28, 13.47, 0.53579, 20, -11.43, 8.08, 0.46421, 2, 19, 72.99, 13.8, 0.21139, 20, -8.17, 18.29, 0.78861, 2, 19, 77.14, 5.94, 0.07818, 20, 0.62, 19.57, 0.92182, 1, 20, 14.29, 17.38, 1, 1, 20, 29.92, 11.58, 1, 1, 20, 36.71, 7.76, 1, 1, 20, 41.24, 2.92, 1, 1, 20, 42.66, -0.7, 1, 1, 20, 39.64, -8.99, 1, 2, 19, 57.31, -34.35, 0.00252, 20, 31.99, -12.55, 0.99748, 2, 19, 52.69, -24.31, 0.09808, 20, 20.99, -13.56, 0.90192, 2, 19, 52.43, -20.62, 0.25646, 20, 17.42, -12.57, 0.74354, 2, 19, 50.54, -20.8, 0.4052, 20, 16.97, -14.41, 0.5948, 2, 19, 48.37, -21.01, 0.56146, 20, 16.44, -16.53, 0.43854, 2, 19, 43.45, -27.18, 0.77579, 20, 20.62, -23.23, 0.22421, 2, 19, 30.01, -27.76, 0.92351, 20, 16.68, -36.09, 0.07649, 2, 19, 17.67, -24.43, 0.98715, 20, 9.43, -46.62, 0.01285, 2, 19, 3.99, -19.49, 1, 20, 0.21, -57.87, 0, 1, 19, 6.74, -0.68, 1, 2, 19, 20.22, -4.53, 0.99952, 20, -8.48, -37.57, 0.00048, 2, 19, 32.29, -6.24, 0.98376, 20, -2.85, -26.76, 0.01624, 2, 19, 49.23, -6.63, 0.75464, 20, 3.17, -10.93, 0.24536, 2, 19, 57.82, -6.25, 0.15411, 20, 5.67, -2.69, 0.84589, 2, 19, 53.74, -6.43, 0.50838, 20, 4.49, -6.61, 0.49162, 1, 20, 8.77, 3.66, 1, 1, 20, 19.62, 1.95, 1, 1, 20, 31.92, -1.51, 1, 2, 19, 68.27, 3.72, 0.13109, 20, -0.24, 10.48, 0.86891, 2, 19, 58.16, 2.86, 0.79025, 20, -2.81, 0.66, 0.20975, 1, 19, 54.15, 2.08, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 48, 50, 50, 52, 52, 54, 46, 48, 38, 40, 40, 42, 2, 56, 56, 58, 58, 60, 60, 62, 8, 10, 10, 12, 62, 66, 66, 64, 14, 16, 16, 18, 42, 44, 44, 46, 64, 68, 68, 70, 70, 72], "width": 63, "height": 87}}, "j2": {"j2": {"type": "mesh", "uvs": [0.55547, 0, 0.72594, 0.08813, 0.81731, 0.2643, 0.87881, 0.38285, 0.9099, 0.44279, 0.90953, 0.53331, 0.90934, 0.5788, 0.8196, 0.63171, 0.81889, 0.70538, 0.9569, 0.78535, 0.98448, 0.88125, 0.95645, 0.9442, 0.80155, 0.98959, 0.44372, 0.98965, 0.30554, 0.96648, 0.21754, 0.91579, 0.21768, 0.82276, 0.25919, 0.71904, 0.24471, 0.65334, 0.10192, 0.5884, 0.05496, 0.46143, 0.04395, 0.35137, 0.01021, 0.19695, 0.04972, 0.10381, 0.2129, 0.02507, 0.35562, 0.13023, 0.44502, 0.29021, 0.50191, 0.42353, 0.51275, 0.54273, 0.55338, 0.64311, 0.56422, 0.73251, 0.5886, 0.84386, 0.79178, 0.81406, 0.75115, 0.91131, 0.38271, 0.8423, 0.49378, 0.91601], "triangles": [25, 24, 0, 25, 0, 1, 23, 24, 25, 26, 25, 1, 26, 1, 2, 22, 26, 21, 25, 22, 23, 26, 22, 25, 27, 26, 2, 27, 2, 3, 21, 26, 27, 20, 21, 27, 4, 27, 3, 4, 28, 27, 5, 28, 4, 20, 27, 28, 5, 7, 28, 19, 20, 28, 6, 7, 5, 29, 28, 7, 18, 19, 28, 18, 28, 29, 8, 29, 7, 17, 18, 29, 30, 29, 8, 17, 29, 30, 32, 30, 8, 32, 8, 9, 34, 17, 30, 16, 17, 34, 31, 30, 32, 34, 30, 31, 32, 9, 10, 33, 31, 32, 33, 32, 10, 15, 16, 34, 35, 34, 31, 11, 33, 10, 14, 15, 34, 14, 34, 35, 12, 33, 11, 13, 14, 35, 35, 12, 13, 33, 35, 31, 12, 35, 33], "vertices": [-242.98, -3.31, -233.6, -11.68, -228.58, -28.42, -225.19, -39.68, -223.48, -45.37, -223.5, -53.97, -223.51, -58.29, -228.45, -63.32, -228.49, -70.32, -220.9, -77.92, -219.38, -87.03, -220.92, -93.01, -229.44, -97.32, -249.12, -97.33, -256.72, -95.12, -261.56, -90.31, -261.56, -81.47, -259.27, -71.62, -260.07, -65.38, -267.92, -59.21, -270.51, -47.14, -271.11, -36.69, -272.97, -22.02, -270.79, -13.17, -261.82, -5.69, -253.97, -15.68, -249.05, -30.88, -245.92, -43.54, -245.33, -54.87, -243.09, -64.4, -242.5, -72.9, -241.16, -83.48, -229.98, -80.64, -232.22, -89.88, -252.48, -83.33, -246.37, -90.33], "hull": 25, "edges": [0, 48, 0, 2, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 34, 36, 36, 38, 48, 50, 50, 52, 2, 4, 4, 6, 6, 8, 52, 54, 54, 56, 8, 10, 10, 12, 56, 58, 58, 60, 60, 62, 64, 66, 68, 70], "width": 55, "height": 95}}, "j4": {"j1": {"type": "mesh", "uvs": [0.52406, 0.01104, 0.7779, 0.01123, 0.95246, 0.13745, 0.98478, 0.24454, 0.98462, 0.35524, 0.95616, 0.47891, 0.9336, 0.57698, 0.84167, 0.64385, 0.8418, 0.69098, 0.84189, 0.72667, 0.87339, 0.79143, 0.8733, 0.91474, 0.74643, 0.95944, 0.5366, 0.98865, 0.27276, 0.98862, 0.15061, 0.97445, 0.05664, 0.94021, 0.0156, 0.90685, 0.01503, 0.8053, 0.10972, 0.73562, 0.27091, 0.67767, 0.33209, 0.67169, 0.33151, 0.64784, 0.33086, 0.62097, 0.23719, 0.55947, 0.23798, 0.40213, 0.30131, 0.26132, 0.39113, 0.10718, 0.687, 0.15007, 0.61478, 0.30248, 0.57764, 0.43995, 0.56113, 0.6342, 0.56113, 0.73432, 0.56113, 0.6865, 0.54462, 0.818, 0.37335, 0.8434, 0.17113, 0.85535, 0.71589, 0.85685, 0.70764, 0.7403, 0.69732, 0.69397], "triangles": [21, 33, 32, 36, 19, 20, 20, 21, 35, 13, 34, 37, 14, 35, 13, 13, 35, 34, 15, 36, 14, 14, 36, 35, 15, 16, 36, 16, 17, 36, 17, 18, 36, 36, 20, 35, 18, 19, 36, 34, 35, 32, 13, 37, 12, 34, 38, 37, 12, 37, 11, 11, 37, 10, 32, 35, 21, 10, 37, 38, 34, 32, 38, 21, 22, 31, 30, 29, 5, 5, 29, 4, 3, 4, 29, 3, 29, 28, 30, 26, 29, 3, 28, 2, 26, 27, 29, 29, 27, 28, 27, 0, 28, 28, 1, 2, 28, 0, 1, 24, 25, 30, 23, 24, 30, 10, 38, 9, 32, 39, 38, 38, 39, 9, 32, 33, 39, 39, 8, 9, 8, 39, 7, 7, 39, 31, 21, 31, 33, 39, 33, 31, 22, 23, 31, 6, 7, 30, 7, 31, 30, 31, 23, 30, 6, 30, 5, 25, 26, 30], "vertices": [1, 21, -4.8, -11.56, 1, 1, 21, -5.62, 4.41, 1, 1, 21, 4.78, 15.97, 1, 1, 21, 14.01, 18.51, 1, 1, 21, 23.71, 19.02, 1, 1, 21, 34.69, 17.74, 1, 1, 21, 43.37, 16.65, 1, 2, 21, 49.46, 11.04, 0.99891, 22, -13.41, -4.81, 0.00109, 2, 21, 53.58, 11.17, 0.95584, 22, -12.17, -0.89, 0.04416, 2, 21, 56.69, 11.28, 0.83862, 22, -11.23, 2.08, 0.16138, 2, 21, 62.28, 13.47, 0.53579, 22, -11.43, 8.08, 0.46421, 2, 21, 72.99, 13.8, 0.21139, 22, -8.17, 18.29, 0.78861, 2, 21, 77.14, 5.94, 0.07818, 22, 0.62, 19.57, 0.92182, 1, 22, 14.29, 17.38, 1, 1, 22, 29.92, 11.58, 1, 1, 22, 36.71, 7.76, 1, 1, 22, 41.24, 2.92, 1, 1, 22, 42.66, -0.7, 1, 1, 22, 39.64, -8.99, 1, 2, 21, 57.31, -34.35, 0.00252, 22, 31.99, -12.55, 0.99748, 2, 21, 52.69, -24.31, 0.09808, 22, 20.99, -13.56, 0.90192, 2, 21, 52.43, -20.62, 0.25646, 22, 17.42, -12.57, 0.74354, 2, 21, 50.54, -20.8, 0.4052, 22, 16.97, -14.41, 0.5948, 2, 21, 48.37, -21.01, 0.56146, 22, 16.44, -16.53, 0.43854, 2, 21, 43.45, -27.18, 0.77579, 22, 20.62, -23.23, 0.22421, 2, 21, 30.01, -27.76, 0.92351, 22, 16.68, -36.09, 0.07649, 2, 21, 17.67, -24.43, 0.98715, 22, 9.43, -46.62, 0.01285, 2, 21, 3.99, -19.49, 1, 22, 0.21, -57.87, 0, 1, 21, 6.74, -0.68, 1, 2, 21, 20.22, -4.53, 0.99952, 22, -8.48, -37.57, 0.00048, 2, 21, 32.29, -6.24, 0.98376, 22, -2.85, -26.76, 0.01624, 2, 21, 49.23, -6.63, 0.75464, 22, 3.17, -10.93, 0.24536, 2, 21, 57.82, -6.25, 0.15411, 22, 5.67, -2.69, 0.84589, 2, 21, 53.74, -6.43, 0.50838, 22, 4.49, -6.61, 0.49162, 1, 22, 8.77, 3.66, 1, 1, 22, 19.62, 1.95, 1, 1, 22, 31.92, -1.51, 1, 2, 21, 68.27, 3.72, 0.13109, 22, -0.24, 10.48, 0.86891, 2, 21, 58.16, 2.86, 0.79025, 22, -2.81, 0.66, 0.20975, 1, 21, 54.15, 2.08, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 48, 50, 50, 52, 52, 54, 46, 48, 38, 40, 40, 42, 2, 56, 56, 58, 58, 60, 60, 62, 8, 10, 10, 12, 62, 66, 66, 64, 14, 16, 16, 18, 42, 44, 44, 46, 64, 68, 68, 70, 70, 72], "width": 63, "height": 87}}, "d1": {"d1": {"type": "mesh", "uvs": [0.9588, 0.01301, 0.99279, 0.0295, 0.98384, 0.07473, 0.80257, 0.20478, 0.6035, 0.34759, 0.44316, 0.5522, 0.4309, 0.62752, 0.42135, 0.79002, 0.4107, 0.97118, 0.19051, 0.99592, 0.01388, 0.87479, 0.01247, 0.77254, 0.09692, 0.6737, 0.16517, 0.59381, 0.26834, 0.47305, 0.42314, 0.29187, 0.54012, 0.15495, 0.68167, 0.07846, 0.80351, 0.01262, 0.822, 0.08983, 0.73076, 0.14672, 0.57594, 0.23577, 0.46259, 0.34214, 0.33541, 0.5054, 0.26629, 0.64393, 0.22206, 0.78246, 0.19994, 0.88635], "triangles": [20, 21, 16, 3, 21, 20, 3, 19, 2, 2, 19, 1, 19, 18, 0, 1, 19, 0, 3, 20, 19, 17, 18, 19, 3, 4, 21, 15, 16, 21, 20, 16, 17, 20, 17, 19, 24, 23, 6, 24, 14, 23, 6, 23, 5, 5, 22, 4, 5, 23, 22, 23, 14, 22, 14, 15, 22, 22, 21, 4, 22, 15, 21, 9, 26, 8, 9, 10, 26, 8, 26, 7, 7, 26, 25, 25, 26, 11, 26, 10, 11, 25, 24, 7, 7, 24, 6, 11, 12, 25, 25, 12, 24, 24, 12, 13, 24, 13, 14], "vertices": [2, 14, -4.4, -19.38, 0.144, 4, 27.6, 7.75, 0.856, 2, 14, -6.86, -20.32, 0.144, 4, 26.15, 5.55, 0.856, 2, 14, -9.07, -17.63, 0.144, 4, 22.78, 6.45, 0.856, 3, 12, -5.64, -31.98, 0.00304, 14, -8.57, -1.84, 0.38896, 4, 13.99, 19.58, 0.608, 3, 12, -5.09, -14.64, 0.31217, 14, -8.02, 15.51, 0.24783, 4, 4.33, 34, 0.44, 3, 13, 5.71, -20.97, 0.23694, 12, -9.82, 3.76, 0.76296, 14, -12.75, 33.9, 0.0001, 2, 13, 1.9, -16.61, 0.52682, 12, -13.63, 8.11, 0.47318, 2, 13, -7.06, -8.09, 0.94527, 12, -22.59, 16.63, 0.05473, 1, 13, -17.05, 1.41, 1, 1, 13, -8.75, 14.01, 1, 1, 13, 6.05, 17.15, 1, 2, 13, 12.02, 12.18, 0.99408, 12, -3.51, 36.9, 0.00592, 2, 13, 14, 2.93, 0.90899, 12, -1.53, 27.65, 0.09101, 2, 13, 15.59, -4.54, 0.69689, 12, 0.06, 20.18, 0.30311, 2, 13, 18.01, -15.84, 0.22416, 12, 2.48, 8.88, 0.77584, 3, 12, 6.1, -8.07, 0.6365, 14, 3.17, 22.07, 0.2035, 4, 9.6, 45.86, 0.16, 3, 12, 8.84, -20.88, 0.16988, 14, 5.91, 9.27, 0.54212, 4, 19.29, 37.04, 0.288, 3, 12, 7, -31.97, 0.00069, 14, 4.07, -1.83, 0.57531, 4, 24.25, 26.95, 0.424, 2, 14, 2.49, -11.38, 0.144, 4, 28.53, 18.27, 0.856, 2, 14, -2.79, -8.52, 0.592, 4, 22.58, 17.52, 0.408, 3, 12, 0.89, -31.14, 0.00071, 14, -2.04, -0.99, 0.37529, 4, 18.8, 24.07, 0.624, 3, 12, 2.59, -18.74, 0.19531, 14, -0.35, 11.41, 0.38069, 4, 12.96, 35.14, 0.424, 3, 12, 1.45, -7.62, 0.63846, 14, -1.48, 22.52, 0.15354, 4, 5.57, 43.51, 0.208, 2, 13, 13.18, -17.71, 0.21159, 12, -2.35, 7.01, 0.78841, 2, 13, 8.23, -7.29, 0.69988, 12, -7.3, 17.43, 0.30012, 2, 13, 2.18, 1.84, 0.99052, 12, -13.35, 26.56, 0.00948, 1, 13, -2.84, 8.11, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 30, 32, 28, 30, 26, 28, 32, 34, 34, 36, 4, 6, 6, 8, 12, 14, 14, 16, 22, 24, 24, 26, 2, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 68, "height": 76}}, "d2": {"d2": {"type": "mesh", "uvs": [0.45975, 0, 0.62909, 0.0269, 0.74687, 0.0657, 0.8218, 0.13284, 0.93265, 0.29081, 0.97752, 0.40177, 0.99868, 0.51174, 0.9743, 0.65607, 0.90248, 0.77022, 0.80248, 0.86887, 0.65439, 0.94612, 0.49798, 0.99704, 0.36613, 0.98901, 0.23838, 0.95823, 0.13714, 0.90804, 0.04342, 0.79582, 0.00682, 0.65219, 0.00701, 0.45377, 0.02564, 0.34166, 0.06788, 0.21694, 0.17031, 0.11511, 0.32699, 0.02823, 0.4561, 0, 0.24801, 0.2255, 0.36001, 0.15058, 0.48601, 0.13678, 0.66601, 0.18213, 0.77601, 0.31817, 0.80801, 0.4759, 0.79401, 0.61194, 0.70201, 0.72235, 0.55001, 0.80516, 0.37601, 0.81305, 0.22601, 0.72235, 0.16001, 0.55279, 0.17001, 0.40689, 0.20001, 0.29648, 0.45201, 0.30831, 0.58801, 0.33197, 0.66201, 0.46801, 0.62001, 0.608, 0.46601, 0.6494, 0.33001, 0.57842, 0.28801, 0.43055, 0.39001, 0.33, 0.46801, 0.46013], "triangles": [5, 28, 27, 39, 27, 28, 28, 5, 6, 40, 45, 39, 29, 39, 28, 29, 28, 6, 40, 39, 29, 41, 45, 40, 7, 29, 6, 30, 40, 29, 8, 29, 7, 30, 29, 8, 15, 16, 33, 31, 41, 40, 31, 40, 30, 32, 42, 41, 32, 41, 31, 33, 42, 32, 9, 30, 8, 14, 15, 33, 13, 14, 33, 10, 31, 30, 10, 30, 9, 32, 13, 33, 12, 13, 32, 11, 32, 31, 11, 31, 10, 12, 32, 11, 25, 0, 1, 24, 21, 22, 20, 21, 24, 22, 0, 25, 24, 22, 25, 26, 1, 2, 26, 2, 3, 25, 1, 26, 23, 20, 24, 19, 20, 23, 36, 19, 23, 37, 24, 25, 38, 37, 25, 44, 23, 24, 27, 26, 3, 27, 3, 4, 37, 44, 24, 36, 23, 44, 26, 38, 25, 38, 26, 27, 18, 19, 36, 35, 18, 36, 43, 36, 44, 35, 36, 43, 17, 18, 35, 45, 37, 38, 44, 37, 45, 43, 44, 45, 39, 38, 27, 45, 38, 39, 34, 17, 35, 34, 35, 43, 42, 43, 45, 34, 43, 42, 16, 17, 34, 33, 34, 42, 16, 34, 33, 5, 27, 4, 41, 42, 45], "vertices": [2, 15, 22.88, -17.74, 0.94135, 16, 121.36, 20.94, 0.05865, 2, 15, 17.08, -40.87, 0.87897, 16, 115.56, -2.18, 0.12103, 2, 15, 10.23, -56.71, 0.78769, 16, 108.71, -18.03, 0.21231, 2, 15, -0.09, -66.28, 0.67135, 16, 98.38, -27.59, 0.32865, 2, 15, -23.61, -79.72, 0.53636, 16, 74.87, -41.03, 0.46364, 2, 15, -39.73, -84.59, 0.40462, 16, 58.75, -45.91, 0.59538, 2, 15, -55.43, -86.19, 0.2837, 16, 43.05, -47.51, 0.7163, 2, 15, -75.42, -81.07, 0.18009, 16, 23.06, -42.39, 0.81991, 2, 15, -90.6, -69.75, 0.09667, 16, 7.88, -31.06, 0.90333, 2, 15, -103.26, -54.71, 0.04672, 16, -4.79, -16.02, 0.95328, 2, 15, -112.35, -33.26, 0.04255, 16, -13.88, 5.42, 0.95745, 2, 15, -117.64, -10.99, 0.08266, 16, -19.16, 27.7, 0.91734, 2, 15, -114.94, 7.18, 0.15177, 16, -16.47, 45.86, 0.84823, 2, 15, -109.1, 24.5, 0.23615, 16, -10.62, 63.18, 0.76385, 2, 15, -100.84, 37.91, 0.32816, 16, -2.36, 76.59, 0.67184, 2, 15, -83.96, 49.53, 0.44021, 16, 14.52, 88.22, 0.55979, 2, 15, -63.34, 52.87, 0.56979, 16, 35.13, 91.55, 0.43021, 2, 15, -35.47, 50.44, 0.70954, 16, 63, 89.13, 0.29046, 2, 15, -19.95, 46.51, 0.82873, 16, 78.53, 85.19, 0.17127, 2, 15, -2.93, 39.15, 0.91658, 16, 95.55, 77.83, 0.08342, 2, 15, 10.16, 23.73, 0.96507, 16, 108.63, 62.42, 0.03493, 2, 15, 20.5, 0.98, 0.98501, 16, 118.97, 39.67, 0.01499, 2, 15, 22.92, -17.24, 0.9757, 16, 121.4, 21.45, 0.0243, 2, 15, -6.28, 14.31, 0.94021, 16, 92.2, 52.99, 0.05979, 2, 15, 2.91, -2.11, 0.94673, 16, 101.39, 36.58, 0.05327, 2, 15, 3.35, -19.73, 0.88319, 16, 101.83, 18.96, 0.11681, 2, 15, -5.16, -44.11, 0.75096, 16, 93.31, -5.42, 0.24904, 2, 15, -25.58, -57.7, 0.57344, 16, 72.89, -19.01, 0.42656, 2, 15, -48.12, -60.22, 0.38985, 16, 50.35, -21.54, 0.61015, 2, 15, -67.07, -56.64, 0.24074, 16, 31.41, -17.95, 0.75926, 2, 15, -81.48, -42.56, 0.15871, 16, 16.99, -3.88, 0.84129, 2, 15, -91.31, -20.51, 0.16436, 16, 7.17, 18.17, 0.83564, 2, 15, -90.34, 3.68, 0.25936, 16, 8.14, 42.36, 0.74064, 2, 15, -75.81, 23.36, 0.41724, 16, 22.66, 62.04, 0.58276, 2, 15, -51.21, 30.45, 0.59767, 16, 47.27, 69.13, 0.40233, 2, 15, -30.83, 27.3, 0.75745, 16, 67.65, 65.99, 0.24255, 2, 15, -15.68, 21.81, 0.87522, 16, 82.8, 60.5, 0.12478, 2, 15, -20.34, -12.94, 0.74648, 16, 78.14, 25.74, 0.25352, 2, 15, -25.28, -31.49, 0.63712, 16, 73.19, 7.19, 0.36288, 2, 15, -45.28, -40.1, 0.49179, 16, 53.2, -1.41, 0.50821, 2, 15, -64.44, -32.59, 0.39601, 16, 34.03, 6.1, 0.60399, 2, 15, -68.42, -10.76, 0.41367, 16, 30.05, 27.92, 0.58633, 2, 15, -56.83, 7.22, 0.52967, 16, 41.64, 45.9, 0.47033, 2, 15, -35.56, 11.25, 0.67343, 16, 62.92, 49.93, 0.32657, 2, 15, -22.65, -4.1, 0.75978, 16, 75.83, 34.59, 0.24022, 2, 15, -41.86, -13.33, 0.59882, 16, 56.62, 25.36, 0.40118], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 46, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 74], "width": 139, "height": 141}}, "b1": {"b1": {"type": "mesh", "uvs": [0.69802, 0.00734, 0.84126, 0.07888, 0.87839, 0.13431, 0.90826, 0.19598, 0.90828, 0.47463, 0.94392, 0.6178, 0.93893, 0.73543, 0.99056, 0.85669, 0.99046, 0.90751, 0.92603, 0.93965, 0.65493, 0.98955, 0.35313, 0.99119, 0.09999, 0.92775, 0.00947, 0.89284, 0.01, 0.83422, 0.09171, 0.67028, 0.08398, 0.63475, 0.11211, 0.53747, 0.11212, 0.44433, 0.17238, 0.19635, 0.24407, 0.12382, 0.32103, 0.07212, 0.47393, 0.01044, 0.55889, 0.00981, 0.62462, 0.02045, 0.43053, 0.11751, 0.40367, 0.27644, 0.38833, 0.43731, 0.38257, 0.60593, 0.37873, 0.78811, 0.71253, 0.13302, 0.69718, 0.2745, 0.6991, 0.447, 0.70102, 0.58848, 0.72212, 0.76098, 0.29816, 0.14077, 0.25788, 0.25318, 0.2502, 0.4218, 0.24061, 0.57879, 0.20416, 0.7571], "triangles": [14, 15, 39, 11, 39, 29, 6, 34, 5, 39, 15, 38, 34, 28, 33, 29, 28, 34, 5, 34, 33, 29, 39, 28, 28, 39, 38, 16, 17, 38, 15, 16, 38, 10, 29, 34, 33, 4, 5, 38, 17, 37, 9, 34, 6, 10, 34, 9, 8, 9, 7, 7, 9, 6, 12, 39, 11, 13, 14, 12, 12, 14, 39, 26, 31, 32, 3, 31, 2, 2, 31, 30, 30, 1, 2, 26, 25, 31, 31, 25, 30, 24, 25, 23, 25, 22, 23, 37, 36, 26, 36, 18, 19, 36, 35, 26, 26, 35, 25, 30, 25, 24, 19, 20, 36, 36, 20, 35, 35, 21, 25, 35, 20, 21, 24, 0, 30, 30, 0, 1, 25, 21, 22, 11, 29, 10, 32, 33, 27, 33, 28, 27, 27, 26, 32, 28, 38, 27, 33, 32, 4, 38, 37, 27, 37, 17, 18, 32, 31, 4, 37, 18, 36, 27, 37, 26, 4, 31, 3], "vertices": [2, 3, 60.98, -26.32, 0.09807, 4, 25.94, -26.44, 0.90193, 2, 3, 52.93, -39.75, 0.22204, 4, 17.82, -39.83, 0.77796, 3, 3, 47.28, -42.94, 0.27553, 4, 12.15, -43, 0.71647, 18, -55.26, 27.17, 0.008, 2, 3, 41.08, -45.37, 0.34205, 4, 5.94, -45.4, 0.65795, 2, 3, 14.14, -43.18, 0.72856, 4, -20.99, -43.09, 0.27144, 4, 3, 0.01, -45.54, 0.08622, 4, -35.13, -45.38, 0.01577, 18, -10.45, 11.94, 0.02976, 2, 43.2, 6.83, 0.86825, 4, 3, -11.32, -44.13, 0.00804, 4, -46.45, -43.91, 0.00154, 18, -0.46, 6.4, 0.12104, 2, 42.61, -4.58, 0.86939, 1, 18, 12.32, 5.67, 1, 1, 18, 16.73, 3.45, 1, 3, 3, -30.96, -41.26, 0.00773, 17, -34.15, 68.38, 0.00017, 18, 16.69, -3.59, 0.9921, 3, 3, -33.64, -14.39, 0.40785, 17, -14, 50.4, 0.06717, 18, 9.14, -29.52, 0.52498, 3, 3, -31.4, 15.1, 0.38023, 17, 4.31, 27.18, 0.55684, 18, -3.94, -56.04, 0.06293, 3, 3, -23.25, 39.33, 0.00479, 17, 14.71, 3.83, 0.99506, 18, -20.54, -75.48, 0.00015, 1, 17, 17.5, -5.25, 1, 1, 17, 12.98, -8.71, 1, 4, 3, 1.7, 38.11, 0.07942, 4, -33.04, 38.27, 0.00195, 17, -4.48, -12.17, 0.008, 2, -40.36, 2.45, 0.91063, 4, 3, 5.2, 38.59, 0.11704, 4, -29.54, 38.73, 0.00436, 17, -6.73, -14.88, 0.008, 2, -41.09, 5.9, 0.8706, 3, 3, 14.38, 35.08, 0.20435, 4, -20.38, 35.17, 0.02164, 2, -38.25, 15.31, 0.77401, 2, 3, 23.39, 34.34, 0.79883, 4, -11.38, 34.4, 0.20117, 2, 3, 46.88, 26.51, 0.26097, 4, 12.08, 26.45, 0.73903, 2, 3, 53.33, 18.94, 0.09465, 4, 18.49, 18.85, 0.90535, 2, 3, 57.71, 11.01, 0.01112, 4, 22.84, 10.91, 0.98888, 1, 4, 27.52, -4.54, 1, 2, 3, 61.85, -12.71, 0.01413, 4, 26.87, -12.84, 0.98587, 2, 3, 60.3, -19.05, 0.05215, 4, 25.28, -19.17, 0.94785, 1, 4, 17.53, 0.59, 1, 3, 3, 37.3, 4.55, 0.20789, 4, 2.4, 4.53, 0.78633, 17, -53.41, -11.55, 0.00579, 2, 3, 21.87, 7.31, 0.98532, 4, -13.02, 7.37, 0.01468, 2, 3, 5.62, 9.2, 0.33298, 2, -11.8, 8.45, 0.66702, 4, 3, -11.97, 11, 0.23418, 17, -12.76, 17.04, 0.05738, 18, -20.44, -44.99, 0.01136, 2, -12.33, -9.22, 0.69708, 2, 3, 48.72, -26.75, 0.18149, 4, 13.67, -26.81, 0.81851, 2, 3, 35.16, -24.14, 0.4458, 4, 0.12, -24.14, 0.5542, 2, 3, 18.47, -22.97, 0.83081, 4, -16.56, -22.89, 0.16919, 3, 3, 4.77, -22.05, 0.25439, 4, -30.25, -21.9, 0.01052, 2, 19.42, 9.87, 0.73508, 5, 3, -12.07, -22.75, 0.15896, 4, -47.1, -22.53, 0.00019, 17, -35.53, 41.96, 0.00205, 18, -7.74, -13.71, 0.088, 2, 21.35, -6.88, 0.75079, 3, 3, 51.26, 13.79, 0.05659, 4, 16.4, 13.71, 0.93541, 17, -57.43, -27.79, 0.008, 2, 3, 40.71, 18.6, 0.31563, 4, 5.87, 18.58, 0.68437, 2, 3, 24.47, 20.68, 0.81273, 4, -10.36, 20.73, 0.18727, 3, 3, 9.37, 22.85, 0.24159, 4, -25.45, 22.97, 0.00714, 2, -25.69, 11.2, 0.75127, 5, 3, -7.58, 27.81, 0.05224, 4, -42.38, 28.01, 0, 17, -4.62, 1.7, 0.15053, 18, -30.78, -58.94, 6e-05, 2, -29.41, -6.07, 0.79716], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 44, 50, 50, 52, 52, 54, 54, 56, 56, 58, 60, 62, 62, 64, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 78], "width": 98, "height": 97}}, "quantou2": {"quantou": {"x": 108.87, "y": 2.93, "scaleX": 1.6313, "scaleY": 1.6313, "rotation": -31.55, "width": 117, "height": 110}}, "sd": {"sd": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-187.15, -111.89, -350.15, -111.89, -350.15, -61.89, -187.15, -61.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 163, "height": 50}}, "shoujiq4": {"shoujiq": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.45, -7.09, -70.55, -7.09, -70.55, 68.91, 4.45, 68.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 75, "height": 76}}, "tou": {"tou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-40.55, -67.25, -30.66, 57.35, 148.77, 43.11, 138.88, -81.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 180}}, "shoujikl": {"shoujikl": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-7.49, -30.91, -1.71, 41.86, 68.07, 36.32, 62.29, -36.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 73, "height": 70}}, "shoujiq3": {"shoujiq": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.45, -7.09, -70.55, -7.09, -70.55, 68.91, 4.45, 68.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 75, "height": 76}}, "yanwu/qipaoyan_00036": {"yanwu/qipaoyan_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [98, -50, -97, -50, -97, 51, 98, 51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 195, "height": 101}, "yanwu/qipaoyan_00037": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [98, -50, -97, -50, -97, 51, 98, 51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 195, "height": 101}, "yanwu/qipaoyan_00038": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [98, -50, -97, -50, -97, 51, 98, 51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 195, "height": 101}, "yanwu/qipaoyan_00040": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [98, -50, -97, -50, -97, 51, 98, 51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 195, "height": 101}, "yanwu/qipaoyan_00042": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [98, -50, -97, -50, -97, 51, 98, 51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 195, "height": 101}, "yanwu/qipaoyan_00044": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [98, -50, -97, -50, -97, 51, 98, 51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 195, "height": 101}, "yanwu/qipaoyan_00046": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [98, -50, -97, -50, -97, 51, 98, 51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 195, "height": 101}, "yanwu/qipaoyan_00048": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [98, -50, -97, -50, -97, 51, 98, 51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 195, "height": 101}}, "s1": {"s1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 12, -26.02, -5.9, 1, 1, 12, 0.63, 25.26, 1, 1, 12, 27.99, 1.86, 1, 1, 12, 1.34, -29.3, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 41, "height": 36}}, "s2": {"s2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 12, -25.38, 4.08, 1, 1, 12, -3.93, 29.16, 1, 1, 12, 21.91, 7.06, 1, 1, 12, 0.46, -18.02, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 33, "height": 34}}, "s3": {"s3": {"type": "mesh", "uvs": [0.35202, 0.0216, 0.4804, 0.06224, 0.65377, 0.1788, 0.81684, 0.3347, 0.87614, 0.41939, 0.9321, 0.49931, 0.93215, 0.68245, 0.97804, 0.75186, 0.97763, 0.86953, 0.95369, 0.90542, 0.82069, 0.95305, 0.56593, 0.98968, 0.22052, 0.9892, 0.06739, 0.86122, 0.06764, 0.7706, 0.15319, 0.69031, 0.16467, 0.54665, 0.15799, 0.49645, 0.15196, 0.45112, 0.02247, 0.25039, 0.02243, 0.1459, 0.02241, 0.08664, 0.09128, 0.054, 0.22608, 0.02153, 0.30645, 0.1132, 0.409, 0.20516, 0.53291, 0.3809, 0.59272, 0.53007, 0.60127, 0.7242, 0.57991, 0.84477, 0.56179, 0.45294], "triangles": [24, 23, 0, 24, 0, 1, 22, 23, 24, 20, 21, 22, 20, 22, 24, 25, 24, 1, 25, 1, 2, 19, 20, 24, 19, 24, 25, 26, 25, 2, 26, 2, 3, 26, 3, 4, 18, 19, 25, 18, 25, 26, 30, 26, 4, 18, 26, 30, 17, 18, 30, 27, 30, 4, 27, 4, 5, 17, 30, 27, 16, 17, 27, 27, 5, 6, 28, 27, 6, 15, 16, 27, 28, 15, 27, 29, 15, 28, 14, 15, 29, 13, 14, 29, 7, 28, 6, 7, 29, 28, 8, 29, 7, 9, 29, 8, 10, 29, 9, 12, 13, 29, 11, 12, 29, 11, 29, 10], "vertices": [2, 7, -10.53, 7.22, 0.816, 6, 4.97, -8.53, 0.184, 2, 7, -4.93, 11.03, 0.816, 6, 0.76, -13.84, 0.184, 1, 7, 7.88, 14.04, 1, 2, 8, -9.66, 12.65, 0.21164, 7, 23.87, 15.25, 0.78836, 2, 8, -1.8, 15.06, 0.58426, 7, 32.07, 14.72, 0.41574, 2, 8, 5.61, 17.33, 0.9359, 7, 39.81, 14.22, 0.0641, 1, 8, 22.46, 16.89, 1, 1, 8, 28.89, 18.74, 1, 1, 8, 39.72, 18.43, 1, 1, 8, 42.99, 17.29, 1, 1, 8, 47.22, 11.33, 1, 1, 8, 50.29, 0.03, 1, 1, 8, 49.85, -15.16, 1, 1, 8, 37.9, -21.58, 1, 1, 8, 29.56, -21.35, 1, 2, 8, 22.28, -17.4, 0.99838, 7, 43.12, -24.15, 0.00162, 2, 8, 9.08, -16.54, 0.88337, 7, 31.07, -18.69, 0.11663, 2, 8, 4.46, -16.72, 0.62686, 7, 26.68, -17.21, 0.37314, 2, 8, 0.28, -16.87, 0.11937, 7, 22.72, -15.88, 0.88063, 2, 8, -18.33, -22.08, 0.00297, 7, 3.47, -14.17, 0.99703, 2, 7, -5.43, -10.53, 0.816, 6, -5.18, 6.9, 0.184, 2, 7, -10.47, -8.47, 0.816, 6, 0.25, 6.43, 0.184, 2, 7, -12.11, -4.53, 0.816, 6, 2.98, 3.15, 0.184, 2, 7, -12.63, 2.09, 0.816, 6, 5.45, -3.01, 0.184, 1, 7, -3.48, 2.17, 1, 1, 7, 6.06, 3.15, 1, 2, 8, -5.74, 0.05, 0.0008, 7, 23.08, 2.08, 0.9992, 1, 8, 8.05, 2.32, 1, 1, 8, 25.91, 2.23, 1, 1, 8, 36.98, 1, 1, 2, 8, 0.92, 1.15, 0.48333, 7, 29.7, 0.75, 0.51667], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 42, 44, 44, 46, 46, 48, 38, 40, 40, 42, 48, 50, 50, 52, 54, 56, 56, 58, 6, 8, 8, 10, 52, 60, 60, 54, 32, 34, 34, 36], "width": 44, "height": 92}}, "s4": {"s4": {"type": "mesh", "uvs": [0.93349, 0.03235, 1, 0.24513, 1, 0.25494, 0.95371, 0.43782, 0.83142, 0.78614, 0.70938, 0.91737, 0.52453, 0.98458, 0.29133, 0.98359, 0.10766, 0.88553, 0.02212, 0.74889, 0.00697, 0.61625, 0.04993, 0.47819, 0.14582, 0.35046, 0.33841, 0.19631, 0.53261, 0.19633, 0.60217, 0.22678, 0.78438, 0.03232, 0.89477, 0.1605, 0.79354, 0.37624, 0.61711, 0.57349, 0.40597, 0.60123, 0.22954, 0.51493, 0.25557, 0.77073, 0.42043, 0.81388, 0.6576, 0.77073, 0.37126, 0.34542, 0.54769, 0.367], "triangles": [25, 13, 14, 12, 13, 25, 25, 14, 26, 21, 12, 25, 11, 12, 21, 20, 25, 26, 21, 25, 20, 10, 22, 9, 21, 10, 11, 22, 21, 20, 22, 10, 21, 24, 23, 20, 22, 20, 23, 8, 9, 22, 7, 22, 23, 8, 22, 7, 6, 23, 24, 7, 23, 6, 17, 16, 0, 15, 16, 17, 17, 0, 1, 17, 1, 2, 26, 14, 15, 18, 15, 17, 18, 17, 2, 26, 15, 18, 3, 18, 2, 19, 26, 18, 20, 26, 19, 19, 4, 24, 18, 4, 19, 18, 3, 4, 24, 20, 19, 5, 24, 4, 6, 24, 5], "vertices": [1, 10, -23.7, 2.91, 1, 1, 10, -13.54, 12.07, 1, 1, 10, -12.99, 12.31, 1, 1, 10, -1.56, 14.03, 1, 1, 10, 21.09, 15.26, 1, 2, 10, 31.6, 11.2, 0.96874, 11, -10.93, -3.13, 0.03126, 2, 10, 40.17, 1.84, 0.18831, 11, -6.24, 8.67, 0.81169, 1, 11, 3.66, 20.15, 1, 1, 11, 15.96, 25.33, 1, 1, 11, 25.91, 24.14, 1, 1, 11, 32.7, 19.63, 1, 1, 11, 37.29, 12.03, 1, 2, 10, 14.6, -36.22, 0.00345, 11, 39.16, 2.23, 0.99655, 2, 10, 0.97, -28.52, 0.19145, 11, 38.17, -13.39, 0.80855, 2, 10, -4.09, -16.95, 0.56127, 11, 29.96, -22.98, 0.43873, 2, 10, -4.2, -12.07, 0.77734, 11, 25.61, -25.21, 0.22266, 2, 10, -19.81, -5.97, 0.99956, 11, 26.93, -41.92, 0.00044, 1, 10, -15.53, 3.74, 1, 1, 10, -0.83, 2.98, 1, 2, 10, 14.79, -2.7, 0.90896, 11, 8.91, -12.2, 0.09104, 2, 10, 21.84, -14.59, 0.00896, 11, 16.54, -0.68, 0.99104, 1, 11, 28, 4.62, 1, 1, 11, 15.04, 13.47, 1, 1, 11, 6.07, 7.04, 1, 1, 10, 24.76, 4.54, 1, 2, 10, 8.45, -22.92, 0.16888, 11, 29.87, -9.1, 0.83112, 2, 10, 5.06, -11.88, 0.62505, 11, 21.41, -16.96, 0.37495], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 50, 52], "width": 65, "height": 61}}, "bao2": {"bao2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.97, -33.56, 29.58, 37.22, 52.51, 35.4, 46.89, -35.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "height": 23}}, "bao3": {"bao3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64.59, -60.25, 68.47, -11.4, 118.31, -15.36, 114.44, -64.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 49, "height": 50}}, "quantou": {"quantou": {"x": 108.87, "y": 2.93, "scaleX": 1.6313, "scaleY": 1.6313, "rotation": -31.55, "width": 117, "height": 110}}, "bao1": {"bao1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [41.85, 10.94, 44.3, 41.85, 77.2, 39.24, 74.74, 8.33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 33}}, "shoujiq2": {"shoujiq": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.45, -7.09, -70.55, -7.09, -70.55, 68.91, 4.45, 68.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 75, "height": 76}}, "shoujiq": {"shoujiq": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.45, -7.09, -70.55, -7.09, -70.55, 68.91, 4.45, 68.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 75, "height": 76}}}}], "animations": {"atk1": {"slots": {"shoujiq4": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "bao3": {"attachment": [{"name": null}]}, "shoujiq3": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "shoujiq": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}]}, "quantou2": {"color": [{"color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.4667, "color": "ffffff00"}]}, "shoujiq2": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}]}, "bao1": {"attachment": [{"name": null}, {"time": 0.1667, "name": "bao1"}]}, "bao2": {"attachment": [{"name": null}]}, "shoujikl": {"attachment": [{"time": 0.1, "name": "shoujikl"}, {"time": 0.5, "name": null}]}, "quantou": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": "quantou"}]}}, "bones": {"bone4": {"rotate": [{"time": 0.1, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.1667, "angle": -9.54, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.2333, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3, "angle": 5.87, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3667}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone2": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -22.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone3": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone18": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -23.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 35.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.12}]}, "bone25": {"rotate": [{"angle": 0.25, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.1, "angle": -38.34, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.3333}]}, "bone24": {"rotate": [{"angle": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 25.93, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667}]}, "bone26": {"rotate": [{"time": 0.1333, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667, "angle": 37.48, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.5}]}, "bone27": {"rotate": [{"time": 0.1, "curve": 0.588, "c2": 0.14, "c3": 0.829, "c4": 0.63}, {"time": 0.2333, "angle": 14.22, "curve": 0.346, "c2": 0.56, "c3": 0.597}, {"time": 0.4667}]}, "bone29": {"scale": [{"x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2}]}, "bone30": {"scale": [{"x": 0.23, "y": 0.23, "curve": "stepped"}, {"time": 0.1, "x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333}]}, "bone15": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 52.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -37.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 53.72, "y": -25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -3.55, "y": 87.85, "curve": 0.317, "c3": 0.651, "c4": 0.35}, {"time": 0.4333}]}}}, "atk1_1": {"slots": {"shoujiq4": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}]}, "bao3": {"attachment": [{"name": null}]}, "shoujiq3": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}]}, "shoujiq": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "quantou2": {"color": [{"color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"name": "quantou"}]}, "shoujiq2": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "bao2": {"attachment": [{"name": null}]}, "shoujikl": {"attachment": [{"name": "shoujikl"}, {"time": 0.5, "name": null}]}, "quantou": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}]}}, "bones": {"bone4": {"rotate": [{"time": 0.1, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.1667, "angle": -9.54, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.2333, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3, "angle": 29.87, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3667}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone2": {"rotate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone3": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone18": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 35.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.12}]}, "bone25": {"rotate": [{"angle": 0.25, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.1, "angle": -38.34, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.3333}]}, "bone24": {"rotate": [{"angle": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 25.93, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667}]}, "bone26": {"rotate": [{"time": 0.1333, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667, "angle": 37.48, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.5}]}, "bone27": {"rotate": [{"time": 0.1, "curve": 0.588, "c2": 0.14, "c3": 0.829, "c4": 0.63}, {"time": 0.2333, "angle": 14.22, "curve": 0.346, "c2": 0.56, "c3": 0.597}, {"time": 0.4667}]}, "bone29": {"scale": [{"x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2}]}, "bone30": {"scale": [{"x": 0.23, "y": 0.23, "curve": "stepped"}, {"time": 0.1, "x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333}]}, "bone15": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 52.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -37.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 53.72, "y": -25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -3.55, "y": 87.85, "curve": 0.317, "c3": 0.651, "c4": 0.35}, {"time": 0.4333}]}}}, "atk2": {"slots": {"shoujiq4": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "bao3": {"attachment": [{"name": null}]}, "shoujiq3": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "shoujiq": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}]}, "quantou2": {"color": [{"color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.4667, "color": "ffffff00"}]}, "shoujiq2": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}]}, "bao2": {"attachment": [{"name": null}, {"time": 0.1667, "name": "bao2"}]}, "shoujikl": {"attachment": [{"time": 0.1, "name": "shoujikl"}, {"time": 0.5, "name": null}]}, "quantou": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": "quantou"}]}}, "bones": {"bone4": {"rotate": [{"time": 0.1, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.1667, "angle": -9.54, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.2333, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3, "angle": 5.87, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3667}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone20": {"rotate": [{"angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.12}]}, "bone25": {"rotate": [{"angle": 0.25, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.1, "angle": -38.34, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.3333}]}, "bone26": {"rotate": [{"time": 0.1333, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667, "angle": 37.48, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.5}]}, "bone29": {"scale": [{"x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2}]}, "bone30": {"scale": [{"x": 0.23, "y": 0.23, "curve": "stepped"}, {"time": 0.1, "x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333}]}, "bone15": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 52.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -37.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 53.72, "y": -25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -3.55, "y": 87.85, "curve": 0.317, "c3": 0.651, "c4": 0.35}, {"time": 0.4333}]}, "bone27": {"rotate": [{"time": 0.1, "curve": 0.588, "c2": 0.14, "c3": 0.829, "c4": 0.63}, {"time": 0.2333, "angle": 14.22, "curve": 0.346, "c2": 0.56, "c3": 0.597}, {"time": 0.4667}]}, "bone18": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -23.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 35.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone24": {"rotate": [{"angle": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 25.93, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667}]}, "bone2": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -22.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}}}, "atk2_1": {"slots": {"shoujiq4": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}]}, "bao3": {"attachment": [{"name": null}]}, "shoujiq3": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}]}, "shoujiq": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "quantou2": {"color": [{"color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"name": "quantou"}]}, "shoujiq2": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "shoujikl": {"attachment": [{"name": "shoujikl"}, {"time": 0.5, "name": null}]}, "quantou": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}]}}, "bones": {"bone4": {"rotate": [{"time": 0.1, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.1667, "angle": -9.54, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.2333, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3, "angle": 29.87, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3667}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone20": {"rotate": [{"angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.12}]}, "bone25": {"rotate": [{"angle": 0.25, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.1, "angle": -38.34, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.3333}]}, "bone26": {"rotate": [{"time": 0.1333, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667, "angle": 37.48, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.5}]}, "bone29": {"scale": [{"x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2}]}, "bone30": {"scale": [{"x": 0.23, "y": 0.23, "curve": "stepped"}, {"time": 0.1, "x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333}]}, "bone15": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 52.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -37.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 53.72, "y": -25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -3.55, "y": 87.85, "curve": 0.317, "c3": 0.651, "c4": 0.35}, {"time": 0.4333}]}, "bone27": {"rotate": [{"time": 0.1, "curve": 0.588, "c2": 0.14, "c3": 0.829, "c4": 0.63}, {"time": 0.2333, "angle": 14.22, "curve": 0.346, "c2": 0.56, "c3": 0.597}, {"time": 0.4667}]}, "bone24": {"rotate": [{"angle": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 25.93, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667}]}, "bone18": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 35.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}}}, "atk3": {"slots": {"shoujiq4": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "bao3": {"attachment": [{"name": null}, {"time": 0.1667, "name": "bao3"}]}, "shoujiq3": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "shoujiq": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}]}, "quantou2": {"color": [{"color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.4667, "color": "ffffff00"}]}, "shoujiq2": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}]}, "shoujikl": {"attachment": [{"time": 0.1, "name": "shoujikl"}, {"time": 0.5, "name": null}]}, "quantou": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": "quantou"}]}}, "bones": {"bone4": {"rotate": [{"time": 0.1, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.1667, "angle": -9.54, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.2333, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3, "angle": 5.87, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3667}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone15": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 52.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -37.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 53.72, "y": -25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -3.55, "y": 87.85, "curve": 0.317, "c3": 0.651, "c4": 0.35}, {"time": 0.4333}]}, "bone20": {"rotate": [{"angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.12}]}, "bone25": {"rotate": [{"angle": 0.25, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.1, "angle": -38.34, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.3333}]}, "bone26": {"rotate": [{"time": 0.1333, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667, "angle": 37.48, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.5}]}, "bone29": {"scale": [{"x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2}]}, "bone30": {"scale": [{"x": 0.23, "y": 0.23, "curve": "stepped"}, {"time": 0.1, "x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333}]}, "bone27": {"rotate": [{"time": 0.1, "curve": 0.588, "c2": 0.14, "c3": 0.829, "c4": 0.63}, {"time": 0.2333, "angle": 14.22, "curve": 0.346, "c2": 0.56, "c3": 0.597}, {"time": 0.4667}]}, "bone18": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -23.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 35.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone24": {"rotate": [{"angle": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 25.93, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667}]}, "bone2": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -22.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}}}, "atk3_1": {"slots": {"shoujiq4": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}]}, "shoujiq3": {"color": [{"time": 0.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}]}, "shoujiq": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "quantou2": {"color": [{"color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"name": "quantou"}]}, "shoujiq2": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "shoujikl": {"attachment": [{"name": "shoujikl"}, {"time": 0.5, "name": null}]}, "quantou": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333, "color": "ffffff00"}]}}, "bones": {"bone4": {"rotate": [{"time": 0.1, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.1667, "angle": -9.54, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.2333, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3, "angle": 29.87, "curve": 0.469, "c2": 0.03, "c3": 0.415, "c4": 0.99}, {"time": 0.3667}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone15": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 52.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -37.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 53.72, "y": -25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -3.55, "y": 87.85, "curve": 0.317, "c3": 0.651, "c4": 0.35}, {"time": 0.4333}]}, "bone20": {"rotate": [{"angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.12}]}, "bone25": {"rotate": [{"angle": 0.25, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.1, "angle": -38.34, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.3333}]}, "bone26": {"rotate": [{"time": 0.1333, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667, "angle": 37.48, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.5}]}, "bone29": {"scale": [{"x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2}]}, "bone30": {"scale": [{"x": 0.23, "y": 0.23, "curve": "stepped"}, {"time": 0.1, "x": 0.23, "y": 0.23, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3333}]}, "bone27": {"rotate": [{"time": 0.1, "curve": 0.588, "c2": 0.14, "c3": 0.829, "c4": 0.63}, {"time": 0.2333, "angle": 14.22, "curve": 0.346, "c2": 0.56, "c3": 0.597}, {"time": 0.4667}]}, "bone24": {"rotate": [{"angle": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 25.93, "curve": 0.737, "c2": 0.16, "c3": 0.75}, {"time": 0.2667}]}, "bone18": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 35.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}}}, "idle_0": {"slots": {"bao3": {"attachment": [{"name": null}]}, "bao1": {"attachment": [{"name": null}]}, "bao2": {"attachment": [{"name": null}]}}, "bones": {"bone4": {"rotate": [{"angle": -1.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "angle": -2.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -1.62}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone3": {"rotate": [{"angle": -0.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.6667, "angle": -2.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -0.64}]}, "bone5": {"translate": [{"x": 0.68, "y": -1.21, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.7333, "x": 1.49, "y": -2.65, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "x": 0.68, "y": -1.21}]}, "bone6": {"rotate": [{"angle": 2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.6667, "angle": 10.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 2.84}]}, "bone7": {"rotate": [{"angle": 7.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "angle": 10.02, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 7.18}]}, "bone8": {"translate": [{"x": 0.35, "y": 1.2, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.7333, "x": 0.77, "y": 2.63, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "x": 0.35, "y": 1.2}]}}}, "idle_1": {"slots": {"bao3": {"attachment": [{"name": null}]}, "bao2": {"attachment": [{"name": null}]}}, "bones": {"bone4": {"rotate": [{"angle": -1.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "angle": -2.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -1.62}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone3": {"rotate": [{"angle": -0.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.6667, "angle": -2.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -0.64}]}, "bone5": {"translate": [{"x": 0.68, "y": -1.21, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.7333, "x": 1.49, "y": -2.65, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "x": 0.68, "y": -1.21}]}, "bone6": {"rotate": [{"angle": 2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.6667, "angle": 10.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 2.84}]}, "bone7": {"rotate": [{"angle": 7.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "angle": 10.02, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 7.18}]}, "bone8": {"translate": [{"x": 0.35, "y": 1.2, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.7333, "x": 0.77, "y": 2.63, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "x": 0.35, "y": 1.2}]}}}, "idle_2": {"slots": {"bao3": {"attachment": [{"name": null}]}}, "bones": {"bone4": {"rotate": [{"angle": -1.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "angle": -2.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -1.62}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone3": {"rotate": [{"angle": -0.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.6667, "angle": -2.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -0.64}]}, "bone5": {"translate": [{"x": 0.68, "y": -1.21, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.7333, "x": 1.49, "y": -2.65, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "x": 0.68, "y": -1.21}]}, "bone6": {"rotate": [{"angle": 2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.6667, "angle": 10.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 2.84}]}, "bone7": {"rotate": [{"angle": 7.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "angle": 10.02, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 7.18}]}, "bone8": {"translate": [{"x": 0.35, "y": 1.2, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.7333, "x": 0.77, "y": 2.63, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "x": 0.35, "y": 1.2}]}}}, "idle_3": {"bones": {"bone4": {"rotate": [{"angle": -1.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "angle": -2.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -1.62}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone3": {"rotate": [{"angle": -0.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.6667, "angle": -2.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -0.64}]}, "bone5": {"translate": [{"x": 0.68, "y": -1.21, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.7333, "x": 1.49, "y": -2.65, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "x": 0.68, "y": -1.21}]}, "bone6": {"rotate": [{"angle": 2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.6667, "angle": 10.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 2.84}]}, "bone7": {"rotate": [{"angle": 7.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "angle": 10.02, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 7.18}]}, "bone8": {"translate": [{"x": 0.35, "y": 1.2, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.7333, "x": 0.77, "y": 2.63, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "x": 0.35, "y": 1.2}]}}}, "run": {"slots": {"yanwu/qipaoyan_00036": {"attachment": [{"name": "yanwu/qipaoyan_00036"}, {"time": 0.0333, "name": "yanwu/qipaoyan_00037"}, {"time": 0.0667, "name": "yanwu/qipaoyan_00040"}, {"time": 0.1, "name": "yanwu/qipaoyan_00042"}, {"time": 0.1333, "name": "yanwu/qipaoyan_00044"}, {"time": 0.1667, "name": "yanwu/qipaoyan_00048"}, {"time": 0.2, "name": "yanwu/qipaoyan_00036"}, {"time": 0.2333, "name": "yanwu/qipaoyan_00037"}, {"time": 0.2667, "name": "yanwu/qipaoyan_00040"}, {"time": 0.3, "name": "yanwu/qipaoyan_00042"}, {"time": 0.3333, "name": "yanwu/qipaoyan_00044"}, {"time": 0.3667, "name": "yanwu/qipaoyan_00046"}, {"time": 0.4, "name": "yanwu/qipaoyan_00048"}]}}, "bones": {"bone": {"rotate": [{"angle": -2.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -2.32}], "translate": [{"x": 1.12, "y": 35.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 2.61, "y": 7.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.12, "y": 35.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 2.61, "y": 7.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.12, "y": 35.04}]}, "bone2": {"rotate": [{"angle": -12.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -12.77}]}, "bone3": {"rotate": [{"angle": 9.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.1333, "angle": -5.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "angle": -11.65, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.4, "angle": 9.87}]}, "bone4": {"rotate": [{"angle": 4.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": 4.29}]}, "bone6": {"rotate": [{"angle": -26.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 65.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -26.04}]}, "bone7": {"rotate": [{"angle": -14.16, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -36.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3, "angle": 8.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -14.16}]}, "bone10": {"rotate": [{"angle": 3.84, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.2333, "angle": 39.81, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.4, "angle": 3.84}]}, "bone15": {"rotate": [{"angle": -4.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -19.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -4.38}], "translate": [{"x": -0.03, "y": 6.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "x": -0.61, "y": 24.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 0.55, "y": -12.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": -0.03, "y": 6.3}]}, "bone16": {"rotate": [{"angle": -23.16, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -32.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.31, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -23.16}]}, "bone17": {"rotate": [{"angle": 17.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 30.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": 17.22}]}, "bone18": {"translate": [{"x": 6.46, "y": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 14.5, "y": -2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 6.46, "y": 1.64}]}, "bone19": {"rotate": [{"angle": -12.46, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -12.46}]}, "bone20": {"rotate": [{"angle": 32.21, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -62.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 32.21}], "translate": [{"x": -3.16, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -12.05, "y": 3.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -3.16, "y": 2.69}]}, "bone21": {"rotate": [{"angle": 39.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": -5.16, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 12.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": 6.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": 39.06}]}}}}}