import { _decorator } from "cc";
import { main_tag } from "./main_tag";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import GuideMgr from "db://assets/GameScrpit/ext_guide/GuideMgr";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("main_tag3")
export class main_tag3 extends main_tag {
  click_btn_task_go(event) {
    // 处理按钮点击事件，例如打开任务界面或执行其他操作
    log.log("武将");
    GuideMgr.startGuide({ stepId: 20 });
  }
}
