{"skeleton": {"hash": "jeTAExDSHKUhEQJrN4l8V7n46SI=", "spine": "3.8.75", "x": -346.94, "y": -645.13, "width": 694.47, "height": 1290.13, "images": "./images/", "audio": "D:/spine导出/知己/孙尚香"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 209.51, "rotation": 0.69, "x": 18.04, "y": -137.59}, {"name": "bone2", "parent": "bone", "length": 157.76, "rotation": 89.77, "x": -36.52, "y": 6.75}, {"name": "bone3", "parent": "bone2", "length": 112.89, "rotation": -6.23, "x": 157.76}, {"name": "bone4", "parent": "bone3", "length": 80.81, "rotation": 3.98, "x": 112.89}, {"name": "bone5", "parent": "bone3", "x": 110.2, "y": -135.87}, {"name": "bone6", "parent": "bone3", "x": 45.34, "y": 31.92}, {"name": "bone7", "parent": "bone6", "length": 229.82, "rotation": -176.74, "x": -49.6, "y": 32.95}, {"name": "bone8", "parent": "bone7", "length": 243.96, "rotation": -69.95, "x": 229.82}, {"name": "bone9", "parent": "bone8", "length": 60.33, "rotation": 146.58, "x": 243.96, "transform": "noRotationOrReflection"}, {"name": "bone10", "parent": "bone5", "length": 248.26, "rotation": -145.89, "x": 32.79, "y": -16.01}, {"name": "bone11", "parent": "bone10", "length": 275.66, "rotation": 5.65, "x": 248.26}, {"name": "bone12", "parent": "bone4", "length": 58.33, "rotation": 161.93, "x": 89.43, "y": -108.91}, {"name": "bone13", "parent": "bone12", "length": 49.58, "rotation": -26.39, "x": 58.33}, {"name": "bone14", "parent": "bone13", "length": 46.04, "rotation": 21.8, "x": 50.13, "y": -0.53}, {"name": "bone15", "parent": "bone14", "length": 29.05, "rotation": 28.96, "x": 46.04}, {"name": "bone16", "parent": "bone4", "x": 256.74, "y": -91.4}, {"name": "bone17", "parent": "bone16", "length": 26.6, "rotation": 149.32, "x": -50, "y": -16.36}, {"name": "bone18", "parent": "bone17", "length": 27.58, "rotation": -23.84, "x": 26.6}, {"name": "bone19", "parent": "bone16", "length": 29.04, "rotation": 122.41, "x": -38.02, "y": 8.51}, {"name": "bone20", "parent": "bone19", "length": 37.7, "rotation": -7.52, "x": 29.04}, {"name": "bone21", "parent": "bone16", "length": 43.14, "rotation": 116.23, "x": -21.04, "y": 52.92}, {"name": "bone22", "parent": "bone21", "length": 60.88, "rotation": 6.85, "x": 43.14}, {"name": "bone23", "parent": "bone16", "length": 28.21, "rotation": -165.68, "x": -55.32, "y": -42.04}, {"name": "bone24", "parent": "bone23", "length": 34.86, "rotation": -33.08, "x": 28.21}, {"name": "bone25", "parent": "bone16", "x": -15.05, "y": 156.68}, {"name": "bone26", "parent": "bone25", "length": 41.08, "rotation": 149.92, "x": -57.88, "y": 40.65}, {"name": "bone27", "parent": "bone26", "length": 43.6, "rotation": 10.29, "x": 41.08}, {"name": "bone28", "parent": "bone16", "length": 150.08, "rotation": 114.33, "x": 5.86, "y": 121.48}, {"name": "bone29", "parent": "bone28", "length": 138.85, "rotation": 6.29, "x": 150.08}, {"name": "bone30", "parent": "bone29", "length": 125.39, "rotation": 17.39, "x": 139.68, "y": -0.45}, {"name": "bone31", "parent": "bone30", "length": 125.26, "rotation": 34.25, "x": 125.39}, {"name": "bone32", "parent": "bone31", "length": 146.29, "rotation": 51.14, "x": 125.26}, {"name": "bone33", "parent": "bone32", "length": 175.92, "rotation": 41.01, "x": 146.29}, {"name": "bone34", "parent": "bone33", "length": 200.42, "rotation": 3.34, "x": 175.92}, {"name": "bone35", "parent": "bone34", "length": 173.74, "rotation": -3.72, "x": 200.33, "y": -0.2}, {"name": "bone36", "parent": "bone35", "length": 199.37, "rotation": 3.26, "x": 173.74}, {"name": "bone37", "parent": "bone4", "x": 125.92, "y": -68.6}, {"name": "bone38", "parent": "bone4", "x": 94.24, "y": -62.14, "color": "abe323ff"}, {"name": "bone39", "parent": "bone4", "x": 155.39, "y": -68.69}, {"name": "bone40", "parent": "bone", "length": 88.29, "rotation": -82.22, "x": -24.37, "y": -27.5}, {"name": "bone41", "parent": "bone40", "length": 110.8, "rotation": 48.51, "x": 64.4, "y": 47.7}, {"name": "bone42", "parent": "bone41", "length": 82.85, "rotation": -26.67, "x": 110.8}, {"name": "bone43", "parent": "bone42", "length": 85.57, "rotation": -9.31, "x": 82.85}, {"name": "bone44", "parent": "bone", "x": 142.68, "y": -25.29}, {"name": "bone45", "parent": "bone44", "length": 94.88, "rotation": -65.18, "x": 36.39, "y": -49.09}, {"name": "bone46", "parent": "bone45", "length": 82.41, "rotation": -2.58, "x": 94.88}, {"name": "bone47", "parent": "bone46", "length": 82.79, "rotation": 6.65, "x": 82.41}, {"name": "bone48", "parent": "bone", "length": 424.81, "rotation": 163.46, "x": 73.61, "y": -261.55}, {"name": "bone49", "parent": "bone48", "length": 280.91, "rotation": 77.01, "x": 424.81}, {"name": "bone50", "parent": "bone", "length": 185.54, "rotation": 162.67, "x": -103.96, "y": -333.13}, {"name": "bone51", "parent": "bone50", "length": 314.9, "rotation": 86.08, "x": 185.54}, {"name": "bone52", "parent": "bone4", "x": 184.83, "y": -151.39}, {"name": "bone53", "parent": "bone4", "length": 70.82, "rotation": 138.14, "x": 191.22, "y": 8.33}, {"name": "bone54", "parent": "bone53", "length": 64.94, "rotation": -7.61, "x": 70.82}, {"name": "bone55", "parent": "bone54", "length": 58.9, "rotation": 10.72, "x": 64.94}, {"name": "bone56", "parent": "bone55", "length": 54.3, "rotation": 28.01, "x": 58.9}, {"name": "bone57", "parent": "bone56", "length": 53.04, "rotation": 42.51, "x": 54.3}, {"name": "bone58", "parent": "bone57", "length": 37.2, "rotation": 36.7, "x": 53.04}, {"name": "bone59", "parent": "bone2", "x": 161.49, "y": 31.18, "color": "abe323ff"}, {"name": "bone60", "parent": "bone2", "x": 242.58, "y": -0.72}], "slots": [{"name": "cut", "bone": "root", "attachment": "cut"}, {"name": "sunshangxiang01", "bone": "root", "attachment": "sunshangxiang01"}, {"name": "sunshangxiang02", "bone": "bone28", "attachment": "sunshangxiang02"}, {"name": "sunshangxiang03", "bone": "root", "attachment": "sunshangxiang03"}, {"name": "sunshangxiang04", "bone": "bone4", "attachment": "sunshangxiang04"}, {"name": "sunshangxiang05", "bone": "root", "attachment": "sunshangxiang05"}, {"name": "sunshangxiang06", "bone": "bone7", "attachment": "sunshangxiang06"}, {"name": "sunshangxiang07", "bone": "bone40", "attachment": "sunshangxiang07"}, {"name": "sunshangxiang08", "bone": "bone50", "attachment": "sunshangxiang08"}, {"name": "sunshangxiang09", "bone": "bone48", "attachment": "sunshangxiang09"}, {"name": "sunshangxiang010", "bone": "root", "attachment": "sunshangxiang010"}, {"name": "sunshangxiang011", "bone": "bone35", "attachment": "sunshangxiang011"}, {"name": "sunshangxiang012", "bone": "root", "attachment": "sunshangxiang012"}, {"name": "sunshangxiang014", "bone": "bone52", "attachment": "sunshangxiang014"}, {"name": "sunshangxiang015", "bone": "bone4", "attachment": "sunshangxiang015"}, {"name": "sunshangxiang016", "bone": "bone37", "attachment": "sunshangxiang016"}, {"name": "sunshangxiang017", "bone": "bone4", "attachment": "sunshangxiang017"}, {"name": "sunshangxiang018", "bone": "root", "attachment": "sunshangxiang018"}, {"name": "sunshangxiang019", "bone": "root", "attachment": "sunshangxiang019"}, {"name": "sunshangxiang020", "bone": "root", "attachment": "sunshangxiang020"}, {"name": "sunshangxiang21", "bone": "bone16", "attachment": "sunshangxiang021"}, {"name": "sunshangxiang021", "bone": "bone16"}, {"name": "sunshangxiang022", "bone": "bone25", "attachment": "sunshangxiang022"}, {"name": "sunshangxiang013", "bone": "bone10", "attachment": "sunshangxiang013"}], "transform": [{"name": "bd", "order": 2, "bones": ["bone60"], "target": "bone59", "x": 81.09, "y": -31.9, "rotateMix": -0.988, "translateMix": -0.988, "scaleMix": -0.988, "shearMix": -0.988}, {"name": "bone59", "order": 3, "bones": ["bone6"], "target": "bone59", "rotation": -6.23, "x": 43.7, "y": 3.83, "shearY": 70.5, "rotateMix": 0.1, "translateMix": 0.1, "scaleMix": 0.1, "shearMix": 0.1}, {"name": "bone591", "order": 4, "bones": ["bone5"], "target": "bone59", "rotation": -6.23, "x": 91.07, "y": -178.21, "shearY": 360, "rotateMix": 0, "translateMix": 0.213, "scaleMix": 0, "shearMix": 0}, {"name": "eye", "order": 1, "bones": ["bone37"], "target": "bone38", "x": 31.68, "y": -6.46, "rotateMix": 0.132, "translateMix": 0.132, "scaleMix": 0.132, "shearMix": 0.132}, {"name": "face", "bones": ["bone39"], "target": "bone38", "x": 61.15, "y": -6.55, "rotateMix": -0.7, "translateMix": -0.7, "scaleMix": -0.7, "shearMix": -0.7}], "skins": [{"name": "default", "attachments": {"sunshangxiang011": {"sunshangxiang011": {"type": "mesh", "uvs": [0.29257, 0.29483, 0.28638, 0.21931, 0.28465, 0.16566, 0.28329, 0.12357, 0.37444, 0.09615, 0.49068, 0.11924, 0.55158, 0.19292, 0.57141, 0.24751, 0.55901, 0.3103, 0.61974, 0.32304, 0.66807, 0.28846, 0.70153, 0.26025, 0.69409, 0.30393, 0.69161, 0.34487, 0.7449, 0.34032, 0.84597, 0.34714, 0.94695, 0.37823, 1, 0.45597, 1, 0.55523, 0.96813, 0.65091, 0.91764, 0.72984, 0.86714, 0.79801, 0.88343, 0.83509, 0.83456, 0.85661, 0.7857, 0.93076, 0.69611, 0.98219, 0.54952, 1, 0.42573, 1, 0.25959, 0.99295, 0.1358, 0.95109, 0.07716, 0.89608, 0.03481, 0.86618, 0.05761, 0.84107, 0.01689, 0.77409, 0, 0.70114, 0, 0.64971, 0.06572, 0.59267, 0.14843, 0.54495, 0.19865, 0.50953, 0.23213, 0.44808, 0.28924, 0.40831, 0.30401, 0.37, 0.29712, 0.32373, 0.37502, 0.20779, 0.38044, 0.28644, 0.38722, 0.35413, 0.39129, 0.41884, 0.38451, 0.46563, 0.30723, 0.52635, 0.23808, 0.58807, 0.21503, 0.67667, 0.21639, 0.7583, 0.26249, 0.86184, 0.27469, 0.94247, 0.48593, 0.33201, 0.51702, 0.37522, 0.53146, 0.40702, 0.61363, 0.39397, 0.69025, 0.39397, 0.81573, 0.43474, 0.89124, 0.52687, 0.84793, 0.62471, 0.78131, 0.72255, 0.72134, 0.81386, 0.64139, 0.9386, 0.53035, 0.45268, 0.46595, 0.53828, 0.4393, 0.62389, 0.4615, 0.73967, 0.4715, 0.83424, 0.44596, 0.93127, 0.68692, 0.4486, 0.68692, 0.54644, 0.6525, 0.65324, 0.5992, 0.77554, 0.56477, 0.88398, 0.65836, 0.3321, 0.68285, 0.29756, 0.32026, 0.41714, 0.28711, 0.46582, 0.21251, 0.53711, 0.07516, 0.62492, 0.07043, 0.69533, 0.10477, 0.77271, 0.15331, 0.86486, 0.15805, 0.9092], "triangles": [38, 39, 79, 80, 38, 79, 48, 80, 79, 37, 38, 80, 49, 80, 48, 81, 36, 37, 81, 37, 80, 49, 81, 80, 77, 10, 11, 12, 77, 11, 76, 9, 10, 76, 10, 77, 76, 77, 12, 13, 76, 12, 57, 9, 76, 57, 55, 9, 58, 76, 13, 57, 76, 58, 59, 14, 15, 59, 15, 16, 58, 13, 14, 59, 58, 14, 71, 57, 58, 71, 58, 59, 59, 16, 17, 60, 59, 17, 72, 71, 59, 65, 57, 71, 72, 59, 60, 72, 65, 71, 60, 17, 18, 61, 72, 60, 61, 60, 18, 19, 61, 18, 73, 72, 61, 20, 61, 19, 62, 61, 20, 2, 3, 4, 43, 4, 5, 43, 5, 6, 2, 4, 43, 1, 2, 43, 7, 44, 43, 7, 43, 6, 1, 43, 44, 0, 1, 44, 7, 54, 44, 42, 0, 44, 8, 54, 7, 45, 44, 54, 42, 44, 45, 41, 42, 45, 55, 54, 8, 9, 55, 8, 55, 45, 54, 56, 55, 57, 78, 41, 45, 40, 41, 78, 55, 46, 45, 46, 55, 56, 78, 45, 46, 65, 46, 56, 65, 56, 57, 47, 78, 46, 47, 46, 65, 79, 40, 78, 79, 78, 47, 48, 79, 47, 66, 47, 65, 48, 47, 66, 66, 65, 72, 67, 48, 66, 49, 48, 67, 73, 66, 72, 67, 66, 73, 79, 39, 40, 35, 36, 81, 50, 49, 67, 50, 81, 49, 82, 35, 81, 82, 81, 50, 34, 35, 82, 62, 73, 61, 68, 67, 73, 50, 67, 68, 51, 50, 68, 82, 50, 51, 83, 82, 51, 33, 34, 82, 33, 82, 83, 74, 68, 73, 74, 73, 62, 21, 62, 20, 63, 74, 62, 63, 62, 21, 69, 68, 74, 52, 51, 68, 32, 33, 83, 23, 63, 21, 23, 21, 22, 69, 52, 68, 84, 83, 51, 84, 51, 52, 32, 83, 84, 75, 69, 74, 75, 74, 63, 30, 32, 84, 31, 32, 30, 85, 84, 52, 30, 84, 85, 24, 63, 23, 64, 75, 63, 70, 52, 69, 70, 69, 75, 24, 64, 63, 53, 52, 70, 85, 52, 53, 29, 30, 85, 25, 64, 24, 53, 29, 85, 28, 29, 53, 27, 53, 70, 28, 53, 27, 26, 70, 75, 26, 75, 64, 27, 70, 26, 26, 64, 25], "vertices": [2, 3, 138.71, 35.94, 0.02109, 4, 28.26, 34.06, 0.97891, 1, 4, 61.71, 37.13, 1, 1, 4, 85.5, 38.44, 1, 1, 4, 117.09, 40.7, 1, 1, 4, 124.81, 9.54, 1, 1, 4, 115.31, -28.25, 1, 2, 4, 76.12, -48.92, 0.99102, 5, 82.02, 92.36, 0.00898, 3, 3, 168.76, -52.38, 0.01196, 4, 52.09, -56.14, 0.94729, 5, 58.55, 83.49, 0.04075, 3, 3, 140.62, -51.17, 0.15405, 4, 24.1, -52.97, 0.64281, 5, 30.41, 84.7, 0.20314, 4, 2, 286.17, -85.88, 2e-05, 3, 136.98, -71.43, 0.18456, 4, 19.07, -72.93, 0.2641, 5, 26.78, 64.44, 0.55132, 3, 3, 153.84, -85.56, 0.08119, 4, 34.91, -88.2, 0.13085, 5, 43.64, 50.31, 0.78796, 3, 3, 180.59, -117.32, 0.02354, 4, 59.39, -121.74, 0.0919, 5, 70.39, 18.55, 0.88455, 3, 3, 150.91, -116.67, 0.04123, 4, 29.82, -119.03, 0.09024, 5, 40.7, 19.2, 0.86853, 3, 3, 134.76, -119.31, 0.04083, 4, 13.52, -120.54, 0.05738, 5, 24.55, 16.56, 0.90178, 3, 3, 133.53, -128.26, 0.01614, 4, 11.67, -129.39, 0.0242, 5, 23.32, 7.61, 0.95966, 2, 4, 10.68, -146.98, 0.00203, 5, 23.55, -10.01, 0.99797, 3, 2, 260.81, -192.35, 0.00639, 5, 13.13, -44.16, 0.69361, 60, 18.23, -191.64, 0.3, 4, 2, 226.16, -209.37, 0.03666, 3, 90.73, -200.71, 0.00149, 5, -19.47, -64.84, 0.66185, 60, -16.42, -208.65, 0.3, 4, 2, 182.09, -209.02, 0.108, 3, 46.88, -205.14, 0.01734, 5, -63.32, -69.27, 0.57466, 60, -60.49, -208.3, 0.3, 4, 2, 139.69, -198.29, 0.21226, 3, 3.57, -199.08, 0.03417, 5, -106.63, -63.21, 0.45357, 60, -102.89, -197.57, 0.3, 4, 2, 104.78, -181.55, 0.32707, 3, -32.95, -186.23, 0.03711, 5, -143.16, -50.35, 0.33582, 60, -137.8, -180.83, 0.3, 4, 2, 74.65, -164.85, 0.44798, 3, -64.72, -172.89, 0.02512, 5, -174.93, -37.02, 0.2269, 60, -167.94, -164.13, 0.3, 3, 2, 58.14, -170.02, 0.688, 3, -80.57, -179.83, 0.02774, 5, -190.77, -43.96, 0.28426, 3, 2, 48.71, -154.02, 0.73239, 3, -91.68, -164.95, 0.02153, 5, -201.88, -29.07, 0.24608, 3, 2, 15.92, -137.83, 0.83777, 3, -126.04, -152.41, 0.00575, 5, -236.24, -16.54, 0.15648, 3, 2, -6.68, -108.44, 0.89772, 3, -151.69, -125.65, 0.00039, 5, -261.9, 10.22, 0.1019, 2, 2, -14.21, -60.59, 0.95893, 5, -274.57, 56.97, 0.04107, 2, 2, -13.88, -20.23, 0.99358, 5, -278.63, 97.13, 0.00642, 2, 2, -10.32, 33.9, 0.99254, 6, -214.11, -24.49, 0.00746, 2, 2, 8.59, 74.11, 0.95511, 6, -199.68, 17.53, 0.04489, 2, 2, 33.16, 93.03, 0.90784, 6, -177.31, 39, 0.09216, 2, 2, 46.55, 106.73, 0.88328, 6, -165.49, 54.08, 0.11672, 3, 2, 57.64, 99.21, 0.60296, 6, -153.64, 47.8, 0.09704, 60, -184.94, 99.92, 0.3, 4, 2, 87.48, 112.24, 0.42872, 6, -125.4, 64, 0.14248, 59, -74.01, 81.06, 0.184, 60, -155.1, 112.96, 0.2448, 4, 2, 119.92, 117.49, 0.36746, 6, -93.72, 72.74, 0.20374, 59, -41.57, 86.31, 0.184, 60, -122.66, 118.21, 0.2448, 4, 2, 142.75, 117.31, 0.32268, 6, -71.01, 75.04, 0.23172, 59, -18.74, 86.13, 0.208, 60, -99.83, 118.02, 0.2376, 4, 2, 167.9, 95.68, 0.21649, 6, -43.65, 56.27, 0.23711, 59, 6.41, 64.5, 0.352, 60, -74.68, 96.4, 0.1944, 5, 2, 188.87, 68.55, 0.13612, 3, 23.49, 71.52, 0.00013, 6, -19.86, 31.57, 0.38456, 59, 27.38, 37.37, 0.256, 60, -53.71, 69.27, 0.2232, 3, 2, 204.47, 52.05, 0.04062, 6, -2.56, 16.87, 0.65938, 60, -38.11, 52.77, 0.3, 3, 3, 69.03, 48.7, 0.17711, 6, 25.68, 8.75, 0.52289, 60, -10.91, 41.64, 0.3, 3, 3, 88.47, 31.95, 0.73387, 4, -22.14, 33.57, 0.0361, 6, 45.12, -7.99, 0.23003, 3, 3, 105.88, 28.87, 0.60276, 4, -4.98, 29.29, 0.35655, 6, 62.53, -11.07, 0.04069, 3, 3, 126.1, 33.18, 0.12728, 4, 15.48, 32.18, 0.87074, 6, 82.74, -6.77, 0.00198, 1, 4, 67.72, 8.4, 1, 1, 4, 32.87, 5.55, 1, 3, 3, 115.62, 2.59, 0.21682, 4, 2.9, 2.4, 0.7827, 6, 72.27, -37.35, 0.00048, 2, 3, 87.17, -1.62, 0.99872, 5, -23.04, 134.26, 0.00128, 4, 2, 223.48, -8.69, 4e-05, 3, 66.28, -1.51, 0.69915, 5, -43.93, 134.36, 0.00081, 59, 61.99, -39.87, 0.3, 4, 2, 196.72, 16.72, 0.00321, 3, 36.92, 20.85, 0.38918, 6, -6.43, -19.1, 0.30762, 59, 35.23, -14.46, 0.3, 4, 2, 169.49, 39.48, 0.21667, 3, 7.38, 40.52, 0.1156, 6, -35.97, 0.57, 0.36773, 59, 8.01, 8.3, 0.3, 4, 2, 130.22, 47.3, 0.51045, 3, -32.51, 44.03, 0.00698, 6, -75.86, 4.09, 0.18257, 59, -31.27, 16.12, 0.3, 3, 2, 93.97, 47.15, 0.60999, 6, -111.88, 0, 0.09001, 59, -67.52, 15.97, 0.3, 3, 2, 47.88, 32.49, 0.6805, 6, -156.1, -19.58, 0.0195, 59, -113.6, 1.31, 0.3, 2, 2, 12.05, 28.8, 0.99087, 6, -191.32, -27.14, 0.00913, 3, 3, 128.63, -28.43, 0.18556, 4, 13.72, -29.46, 0.72308, 5, 18.42, 107.44, 0.09136, 4, 2, 263.27, -52.21, 0.00075, 3, 110.56, -40.45, 0.46098, 4, -5.14, -40.19, 0.34777, 5, 0.36, 95.42, 0.1905, 4, 2, 249.12, -56.81, 0.00536, 3, 96.99, -46.55, 0.58033, 4, -19.1, -45.33, 0.17751, 5, -13.22, 89.32, 0.2368, 4, 2, 254.69, -83.64, 0.00791, 3, 105.45, -72.62, 0.33643, 4, -12.47, -71.93, 0.16402, 5, -4.76, 63.25, 0.49164, 4, 2, 254.49, -108.62, 0.00861, 3, 107.96, -97.47, 0.17455, 4, -11.69, -96.89, 0.08066, 5, -2.25, 38.4, 0.73618, 4, 2, 236.07, -149.38, 0.02307, 3, 94.07, -139.99, 0.02726, 4, -28.51, -138.35, 0.00045, 5, -16.14, -4.12, 0.94922, 3, 2, 194.96, -173.66, 0.12925, 3, 55.84, -168.6, 0.04098, 5, -54.36, -32.73, 0.82977, 3, 2, 151.64, -159.2, 0.30836, 3, 11.2, -158.92, 0.08134, 5, -99, -23.05, 0.61029, 3, 2, 108.37, -137.13, 0.54595, 3, -34.2, -141.68, 0.07318, 5, -144.41, -5.81, 0.38087, 3, 2, 67.99, -117.26, 0.75173, 3, -76.51, -126.31, 0.03035, 5, -186.71, 9.56, 0.21792, 3, 2, 12.81, -90.75, 0.90878, 3, -134.23, -105.95, 0.00092, 5, -244.44, 29.92, 0.09031, 4, 2, 228.85, -56.28, 0.02237, 3, 76.78, -48.23, 0.69667, 4, -39.37, -45.61, 0.05385, 5, -33.42, 87.64, 0.22712, 4, 2, 191.01, -34.98, 0.06477, 3, 36.85, -31.17, 0.66989, 5, -73.35, 104.71, 0.06535, 59, 29.52, -66.16, 0.2, 4, 2, 153.07, -25.99, 0.43816, 3, -1.84, -26.35, 0.32945, 5, -112.04, 109.53, 0.03239, 59, -8.42, -57.17, 0.2, 4, 2, 101.61, -32.82, 0.74405, 3, -52.25, -38.72, 0.02324, 5, -162.46, 97.15, 0.03271, 59, -59.88, -64, 0.2, 3, 2, 59.59, -35.74, 0.96719, 3, -93.7, -46.19, 0.00242, 5, -203.91, 89.68, 0.03039, 2, 2, 16.58, -27.07, 0.98817, 5, -247.61, 93.63, 0.01183, 4, 2, 230.25, -107.34, 0.0428, 3, 83.72, -98.83, 0.25533, 4, -35.97, -96.57, 0.03559, 5, -26.49, 37.04, 0.66629, 5, 2, 186.81, -106.99, 0.17421, 3, 40.5, -103.2, 0.26379, 4, -79.39, -97.92, 0.00136, 5, -69.71, 32.67, 0.46064, 59, 25.32, -138.17, 0.1, 4, 2, 139.48, -95.39, 0.43259, 3, -7.81, -96.81, 0.18795, 5, -118.02, 39.06, 0.27946, 59, -22.01, -126.57, 0.1, 4, 2, 85.32, -77.58, 0.72791, 3, -63.58, -84.98, 0.04254, 5, -173.79, 50.89, 0.12955, 59, -76.17, -108.76, 0.1, 3, 2, 37.27, -65.97, 0.92889, 3, -112.62, -78.66, 0.00295, 5, -222.82, 57.21, 0.06816, 4, 2, 283.25, -111.16, 2e-05, 3, 136.83, -96.88, 0.09065, 4, 17.14, -98.31, 0.11576, 5, 26.62, 38.99, 0.79358, 3, 3, 152.06, -103.37, 0.05039, 4, 31.89, -105.85, 0.10124, 5, 41.86, 32.5, 0.84837, 3, 3, 85.59, 21.5, 0.81875, 4, -25.74, 23.34, 0.02075, 6, 42.24, -18.45, 0.16049, 2, 3, 63, 30.08, 0.44511, 6, 19.65, -9.87, 0.55489, 4, 2, 192.19, 47.63, 0.09296, 3, 29.06, 51.09, 0.01993, 6, -14.29, 11.14, 0.6871, 59, 30.7, 16.45, 0.2, 3, 2, 153.56, 92.72, 0.41641, 6, -57.59, 51.76, 0.38359, 59, -7.93, 61.54, 0.2, 3, 2, 122.31, 94.51, 0.51726, 6, -88.85, 50.15, 0.28274, 59, -39.18, 63.33, 0.2, 3, 2, 87.87, 83.59, 0.63139, 6, -121.9, 35.56, 0.16861, 59, -73.62, 52.41, 0.2, 2, 2, 46.83, 68.09, 0.91759, 6, -161.02, 15.7, 0.08241, 2, 2, 27.13, 66.71, 0.94573, 6, -180.45, 12.18, 0.05427], "hull": 43, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 0, 84, 8, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 108, 110, 110, 112, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 114, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 142, 144, 144, 146, 146, 148, 148, 150, 152, 154, 154, 22, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 2, 4, 4, 6], "width": 326, "height": 444}}, "sunshangxiang012": {"sunshangxiang012": {"type": "mesh", "uvs": [0, 0.3839, 0.08598, 0.18533, 0.22913, 0.06678, 0.42105, 0, 0.64285, 0.03714, 0.77185, 0.12902, 0.83791, 0.21793, 0.84106, 0.39872, 0.84578, 0.57655, 0.90241, 0.73066, 0.95747, 0.87589, 1, 0.9974, 0.91657, 0.96184, 0.79544, 0.85514, 0.63499, 0.73955, 0.51071, 0.66546, 0.37228, 0.71584, 0.25745, 0.78401, 0.16306, 0.92627, 0.13947, 1, 0.08598, 1, 0.06711, 0.7929, 0.0498, 0.55876, 0.15719, 0.45598, 0.29119, 0.36859, 0.47157, 0.32732, 0.6365, 0.38558, 0.76663, 0.48511, 0.18038, 0.62833, 0.33628, 0.54822, 0.49863, 0.49482, 0.6468, 0.56279, 0.77178, 0.66474, 0.85682, 0.7667], "triangles": [19, 20, 18, 20, 21, 18, 12, 10, 11, 13, 33, 12, 12, 33, 10, 10, 33, 9, 18, 28, 17, 18, 21, 28, 14, 32, 13, 13, 32, 33, 21, 22, 28, 17, 29, 16, 17, 28, 29, 32, 8, 33, 33, 8, 9, 14, 31, 32, 14, 15, 31, 15, 16, 30, 16, 29, 30, 15, 30, 31, 31, 27, 32, 32, 27, 8, 22, 23, 28, 28, 24, 29, 28, 23, 24, 27, 7, 8, 30, 26, 31, 31, 26, 27, 22, 0, 23, 29, 25, 30, 29, 24, 25, 30, 25, 26, 27, 6, 7, 27, 5, 6, 27, 26, 5, 0, 1, 23, 24, 23, 2, 26, 4, 5, 26, 25, 4, 23, 1, 2, 24, 3, 25, 24, 2, 3, 25, 3, 4], "vertices": [2, 2, 9.9, 74.37, 0.93528, 40, -55.75, -79.66, 0.06472, 2, 2, 37.12, 51.79, 0.99314, 40, -79.56, -53.51, 0.00686, 1, 2, 53.18, 14.44, 1, 2, 2, 62, -35.53, 0.85771, 40, -92.02, 36.42, 0.14229, 2, 2, 56.41, -93.15, 0.46352, 40, -78.46, 92.71, 0.53648, 2, 2, 43.46, -126.59, 0.30575, 40, -60.98, 124.01, 0.69425, 2, 2, 31.06, -143.67, 0.25775, 40, -46.31, 139.2, 0.74225, 2, 2, 6.1, -144.28, 0.2008, 40, -21.51, 136.33, 0.7992, 2, 2, -18.45, -145.32, 0.11452, 40, 2.94, 133.93, 0.88548, 2, 2, -39.83, -159.87, 0.0403, 40, 26.15, 145.36, 0.9597, 2, 2, -59.99, -174.02, 0.00739, 40, 48.08, 156.57, 0.99261, 1, 40, 66.29, 165.03, 1, 2, 2, -71.76, -163.29, 0.00826, 40, 58.24, 144.3, 0.99174, 2, 2, -56.79, -131.92, 0.05855, 40, 39.04, 115.32, 0.94145, 2, 2, -40.5, -90.33, 0.16021, 40, 17.12, 76.41, 0.83979, 2, 2, -30.02, -58.1, 0.21737, 40, 2.24, 45.96, 0.78263, 2, 2, -36.69, -22.06, 0.02218, 40, 3.82, 9.33, 0.97782, 2, 2, -45.85, 7.87, 0.24438, 40, 8.72, -21.59, 0.75562, 2, 2, -65.29, 32.57, 0.34123, 40, 24.53, -48.75, 0.65877, 2, 2, -75.41, 38.79, 0.33448, 40, 33.69, -56.32, 0.66552, 2, 2, -75.3, 52.69, 0.33682, 40, 31.64, -70.07, 0.66318, 2, 2, -46.68, 57.37, 0.44942, 40, 2.65, -70.72, 0.55058, 2, 2, -14.34, 61.61, 0.7832, 40, -29.97, -70.41, 0.2168, 2, 2, -0.38, 33.58, 0.89165, 40, -39.89, -40.7, 0.10835, 2, 2, 11.4, -1.36, 0.99736, 40, -46.69, -4.46, 0.00264, 2, 2, 16.72, -48.3, 0.60923, 40, -45.41, 42.76, 0.39077, 2, 2, 8.34, -91.11, 0.31769, 40, -31.14, 83.99, 0.68231, 2, 2, -5.67, -124.84, 0.18047, 40, -12.57, 115.43, 0.81953, 2, 2, -24.21, 27.74, 0.59352, 40, -15.48, -38.24, 0.40648, 2, 2, -13.48, -12.88, 0.51178, 40, -20.44, 3.48, 0.48822, 2, 2, -6.45, -55.15, 0.35659, 40, -21.51, 46.32, 0.64341, 2, 2, -16.14, -93.6, 0.20669, 40, -6.56, 83.04, 0.79331, 2, 2, -30.47, -125.98, 0.10803, 40, 12.14, 113.11, 0.89197, 2, 2, -44.71, -147.98, 0.04735, 40, 29.32, 132.9, 0.95265], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 42, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 260, "height": 138}}, "cut": {"cut": {"type": "clipping", "end": "cut", "vertexCount": 4, "vertices": [-346.01, 644.74, 346.83, 645.16, 347.64, -645.22, -346.94, -645.12], "color": "ce3a3aff"}}, "sunshangxiang014": {"sunshangxiang014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-78.5, -42.84, -81.38, 49.12, 70.55, 53.86, 73.42, -38.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 55, "height": 91}}, "sunshangxiang015": {"sunshangxiang015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46.36, -132.08, 44.17, -62.11, 127.13, -59.52, 129.32, -129.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 49}}, "sunshangxiang016": {"sunshangxiang016": {"type": "mesh", "uvs": [0, 0.19256, 0.063, 0.04025, 0.16619, 0, 0.33864, 0.01408, 0.51816, 0.08547, 0.64961, 0.18066, 0.79928, 0.32597, 0.90352, 0.47471, 0.97597, 0.60857, 0.9424, 0.82274, 0.86112, 0.9804, 0.77454, 0.93281, 0.64909, 0.86141, 0.5201, 0.78407, 0.37168, 0.67698, 0.2321, 0.57585, 0.08544, 0.47471, 0, 0.34977, 0.12078, 0.22781, 0.44413, 0.42116, 0.58548, 0.52528, 0.69503, 0.60857, 0.90706, 0.80192, 0.80811, 0.71566, 0.2851, 0.31408], "triangles": [10, 22, 9, 10, 11, 22, 11, 23, 22, 11, 12, 23, 12, 21, 23, 12, 13, 21, 9, 22, 8, 22, 23, 8, 14, 19, 13, 13, 20, 21, 13, 19, 20, 23, 7, 8, 23, 21, 7, 14, 15, 19, 21, 6, 7, 21, 20, 6, 15, 24, 19, 15, 16, 24, 20, 5, 6, 20, 19, 5, 16, 18, 24, 16, 17, 18, 24, 3, 19, 19, 4, 5, 19, 3, 4, 17, 0, 18, 18, 2, 24, 24, 2, 3, 0, 1, 18, 18, 1, 2], "vertices": [2, 37, 30.58, 87.04, 0.544, 39, 1.11, 87.13, 0.456, 2, 37, 42.86, 79.04, 0.544, 39, 13.4, 79.13, 0.456, 1, 37, 46.47, 65.42, 1, 1, 37, 46.08, 42.46, 1, 1, 37, 41.19, 18.42, 1, 1, 37, 34.21, 0.71, 1, 1, 37, 23.36, -19.54, 1, 1, 37, 12.05, -33.77, 1, 2, 37, 1.78, -43.73, 0.496, 39, -27.69, -43.64, 0.504, 2, 37, -15.27, -39.79, 0.544, 39, -44.74, -39.7, 0.456, 2, 37, -28.06, -29.38, 0.544, 39, -57.52, -29.29, 0.456, 1, 37, -24.66, -17.75, 1, 1, 37, -19.54, -0.9, 1, 1, 37, -13.97, 16.44, 1, 1, 37, -6.13, 36.44, 1, 1, 37, 1.28, 55.24, 1, 1, 37, 8.65, 74.99, 1, 2, 37, 18.16, 86.65, 0.544, 39, -11.31, 86.74, 0.456, 1, 37, 28.29, 70.9, 1, 1, 37, 14.37, 27.44, 1, 1, 37, 6.74, 8.39, 1, 1, 37, 0.62, -6.38, 1, 1, 37, -13.77, -35.04, 1, 1, 37, -7.37, -21.68, 1, 1, 37, 22.17, 48.84, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 38, 40, 40, 42, 44, 18], "width": 133, "height": 79}}, "sunshangxiang017": {"sunshangxiang017": {"type": "mesh", "uvs": [0.16446, 0.67209, 0.18239, 0.74313, 0.23103, 0.82094, 0.31809, 0.90777, 0.43458, 0.97994, 0.49603, 1, 0.56132, 0.98445, 0.63942, 0.9416, 0.73031, 0.88973, 0.80584, 0.81079, 0.86089, 0.70028, 0.90314, 0.6439, 0.94026, 0.58639, 0.98635, 0.52662, 1, 0.40596, 0.99463, 0.34223, 0.98572, 0.23634, 0.86839, 0.09554, 0.72555, 0.02963, 0.5606, 0, 0.33954, 0.01016, 0.14738, 0.13149, 0.04025, 0.30973, 0.04365, 0.41458, 0, 0.45952, 0.00114, 0.56287, 0.04025, 0.6168, 0.10827, 0.67821, 0.12017, 0.50895, 0.14228, 0.59133, 0.07426, 0.55388, 0.10317, 0.6123, 0.7636, 0.1854, 0.76858, 0.33362, 0.73867, 0.46317, 0.70502, 0.54551, 0.6776, 0.6004, 0.67635, 0.66408, 0.66762, 0.71238, 0.63023, 0.74422, 0.61528, 0.79363, 0.60281, 0.83315, 0.56542, 0.87267, 0.52678, 0.93745, 0.53706, 0.69363, 0.69343, 0.75255, 0.7509, 0.77578, 0.43061, 0.65629, 0.29307, 0.6455, 0.90157, 0.23718, 0.91276, 0.39076, 0.89691, 0.49259, 0.86987, 0.54269, 0.85868, 0.56076, 0.78222, 0.6678, 0.77943, 0.70065, 0.69458, 0.8378, 0.675, 0.86408, 0.61813, 0.90104, 0.57431, 0.93225, 0.4531, 0.9265, 0.43818, 0.85669, 0.42606, 0.79756, 0.41953, 0.75403, 0.30063, 0.73831, 0.32751, 0.81863, 0.38012, 0.88969, 0.27778, 0.53684, 0.44139, 0.53652, 0.43816, 0.56352, 0.5616, 0.55641, 0.60355, 0.57631, 0.69471, 0.609, 0.80006, 0.54247, 0.73189, 0.56085, 0.89328, 0.58904, 0.88145, 0.63377, 0.84111, 0.67053, 0.73467, 0.63438, 0.73815, 0.60436, 0.78337, 0.60803, 0.83067, 0.62274, 0.86324, 0.65037, 0.70995, 0.60737, 0.7371, 0.62389, 0.78492, 0.65354, 0.84054, 0.65999, 0.85473, 0.64315, 0.86445, 0.60841, 0.83957, 0.58478, 0.7898, 0.57059, 0.73564, 0.58349, 0.73019, 0.68736, 0.69773, 0.67215, 0.6618, 0.81698, 0.64591, 0.85531, 0.51659, 0.76951, 0.49034, 0.82123, 0.48481, 0.862, 0.5394, 0.54889, 0.54259, 0.49623, 0.49158, 0.44708, 0.40709, 0.43374, 0.34411, 0.43795, 0.30027, 0.43374, 0.33375, 0.485, 0.36803, 0.51519, 0.49237, 0.53976, 0.49477, 0.50817, 0.42701, 0.48148, 0.36723, 0.47095, 0.33933, 0.45691, 0.4844, 0.56644, 0.53382, 0.56995, 0.56013, 0.49763, 0.53462, 0.43304, 0.41984, 0.40355, 0.33438, 0.40355, 0.2459, 0.42391, 0.28576, 0.4871, 0.35192, 0.54327, 0.15089, 0.42275, 0.19376, 0.53382, 0.21898, 0.66154, 0.23789, 0.74484, 0.27193, 0.81925, 0.1409, 0.30563, 0.23421, 0.29008, 0.41577, 0.26898, 0.60616, 0.51998, 0.61499, 0.4067, 0.60616, 0.30896, 0.5759, 0.15347, 0.37416, 0.14681, 0.20142, 0.209, 0.80777, 0.47472], "triangles": [4, 60, 5, 5, 43, 6, 5, 60, 43, 6, 59, 7, 6, 43, 59, 3, 66, 4, 4, 66, 60, 59, 58, 7, 7, 57, 8, 7, 58, 57, 60, 98, 43, 43, 42, 59, 43, 98, 42, 59, 42, 58, 66, 61, 60, 60, 61, 98, 2, 125, 3, 3, 65, 66, 3, 125, 65, 58, 95, 57, 42, 41, 58, 58, 41, 95, 57, 56, 8, 9, 56, 46, 9, 8, 56, 66, 65, 61, 41, 42, 97, 57, 95, 56, 42, 98, 97, 98, 61, 97, 65, 62, 61, 61, 62, 97, 95, 94, 56, 95, 41, 94, 46, 56, 45, 97, 96, 41, 41, 40, 94, 41, 96, 40, 97, 62, 96, 2, 124, 125, 2, 1, 124, 125, 64, 65, 125, 124, 64, 65, 63, 62, 65, 64, 63, 40, 39, 94, 56, 94, 45, 94, 39, 45, 46, 55, 9, 9, 55, 10, 10, 55, 54, 86, 77, 54, 86, 54, 85, 10, 54, 77, 62, 63, 96, 40, 96, 39, 45, 92, 46, 46, 92, 55, 96, 44, 39, 96, 63, 44, 63, 47, 44, 63, 64, 47, 39, 38, 45, 93, 45, 38, 93, 92, 45, 64, 124, 123, 39, 44, 38, 124, 1, 123, 1, 0, 123, 123, 48, 64, 64, 48, 47, 38, 37, 93, 38, 44, 37, 55, 92, 54, 10, 82, 11, 82, 76, 11, 10, 77, 82, 47, 112, 44, 112, 113, 44, 44, 71, 37, 71, 113, 70, 71, 44, 113, 92, 78, 54, 92, 93, 78, 26, 31, 27, 27, 31, 0, 78, 93, 72, 78, 72, 83, 37, 72, 93, 31, 29, 0, 0, 29, 123, 77, 86, 82, 54, 78, 85, 37, 36, 72, 37, 71, 36, 29, 122, 123, 123, 67, 48, 123, 122, 67, 85, 81, 86, 86, 87, 82, 86, 81, 87, 48, 120, 47, 47, 69, 112, 47, 120, 69, 78, 84, 85, 84, 80, 85, 85, 80, 81, 82, 87, 76, 48, 67, 120, 120, 67, 119, 76, 75, 11, 11, 75, 12, 87, 88, 76, 87, 81, 88, 78, 83, 84, 76, 88, 75, 84, 79, 80, 84, 83, 79, 81, 89, 88, 81, 80, 89, 26, 30, 31, 26, 25, 30, 31, 30, 29, 91, 83, 74, 88, 89, 75, 80, 90, 89, 90, 80, 91, 79, 83, 91, 83, 72, 74, 80, 79, 91, 35, 74, 36, 36, 74, 72, 36, 71, 35, 30, 28, 29, 29, 28, 122, 89, 53, 75, 12, 75, 52, 75, 53, 52, 52, 51, 12, 12, 51, 13, 53, 89, 73, 91, 74, 90, 71, 129, 35, 71, 70, 129, 89, 90, 73, 90, 74, 73, 112, 107, 113, 113, 99, 70, 113, 107, 99, 69, 68, 112, 112, 68, 107, 69, 120, 68, 68, 120, 106, 25, 24, 30, 135, 73, 74, 53, 73, 52, 129, 70, 114, 30, 24, 28, 107, 108, 99, 70, 99, 114, 99, 100, 114, 99, 108, 100, 74, 35, 34, 35, 129, 34, 119, 105, 120, 120, 105, 106, 51, 52, 135, 52, 73, 135, 135, 74, 34, 107, 68, 108, 67, 122, 119, 106, 109, 68, 68, 109, 108, 28, 121, 122, 122, 118, 119, 122, 121, 118, 13, 51, 14, 129, 130, 34, 129, 114, 130, 105, 110, 106, 106, 110, 109, 24, 23, 28, 28, 23, 121, 109, 101, 108, 108, 101, 100, 130, 114, 115, 101, 115, 100, 114, 100, 115, 51, 50, 14, 51, 135, 50, 119, 104, 105, 119, 118, 104, 105, 111, 110, 105, 104, 111, 110, 102, 109, 109, 102, 101, 50, 135, 33, 111, 103, 110, 110, 103, 102, 135, 34, 33, 34, 130, 33, 111, 104, 103, 102, 116, 101, 101, 116, 115, 104, 117, 103, 103, 117, 102, 102, 117, 116, 104, 118, 117, 130, 115, 131, 117, 118, 127, 23, 126, 121, 127, 118, 126, 118, 121, 126, 23, 22, 126, 115, 116, 131, 130, 131, 33, 50, 15, 14, 117, 128, 116, 116, 128, 131, 117, 127, 128, 33, 49, 50, 50, 49, 15, 49, 16, 15, 131, 32, 33, 33, 32, 49, 126, 22, 21, 128, 132, 131, 131, 132, 32, 126, 134, 127, 126, 21, 134, 128, 127, 133, 127, 134, 133, 128, 133, 132, 16, 49, 17, 49, 32, 17, 134, 21, 133, 132, 18, 32, 32, 18, 17, 133, 19, 132, 132, 19, 18, 21, 20, 133, 133, 20, 19], "vertices": [2, 4, 101.17, 36.67, 0.8, 39, -54.22, 105.37, 0.2, 2, 4, 84.6, 32.44, 0.8, 39, -70.79, 101.14, 0.2, 2, 4, 66.64, 21.81, 0.8, 39, -88.75, 90.5, 0.2, 2, 4, 46.81, 3.16, 0.8, 39, -108.58, 71.85, 0.2, 2, 4, 30.61, -21.47, 0.8, 39, -124.78, 47.22, 0.2, 2, 4, 26.3, -34.33, 0.8, 39, -129.1, 34.36, 0.2, 2, 4, 30.37, -47.73, 0.8, 39, -125.02, 20.97, 0.2, 2, 4, 40.94, -63.57, 0.8, 39, -114.45, 5.12, 0.2, 2, 4, 53.71, -82, 0.8, 39, -101.68, -13.3, 0.2, 2, 4, 72.74, -97.04, 0.8, 39, -82.65, -28.35, 0.2, 2, 4, 99.05, -107.62, 0.8, 39, -56.34, -38.93, 0.2, 2, 4, 112.57, -115.95, 0.8, 39, -42.82, -47.26, 0.2, 2, 4, 126.32, -123.21, 0.8, 39, -29.07, -54.51, 0.2, 2, 4, 140.66, -132.3, 0.8, 39, -14.74, -63.61, 0.2, 2, 4, 169.09, -134.24, 0.8, 39, 13.69, -65.55, 0.2, 2, 4, 184.02, -132.67, 0.8, 39, 28.63, -63.97, 0.2, 2, 4, 208.84, -130.04, 0.8, 39, 53.44, -61.35, 0.2, 2, 4, 241.15, -104.73, 0.8, 39, 85.76, -36.04, 0.2, 2, 4, 255.71, -74.7, 0.8, 39, 100.31, -6, 0.2, 2, 4, 261.6, -40.35, 0.8, 39, 106.21, 28.34, 0.2, 2, 4, 257.78, 5.31, 0.8, 39, 102.39, 74, 0.2, 2, 4, 228.04, 44.18, 0.8, 39, 72.65, 112.87, 0.2, 2, 4, 185.48, 65.03, 0.8, 39, 30.09, 133.73, 0.2, 2, 4, 160.88, 63.56, 0.8, 39, 5.49, 132.25, 0.2, 2, 4, 150.04, 72.26, 0.8, 39, -5.35, 140.96, 0.2, 2, 4, 125.77, 71.27, 0.8, 39, -29.62, 139.96, 0.2, 2, 4, 113.36, 62.78, 0.8, 39, -42.03, 131.47, 0.2, 2, 4, 99.37, 48.26, 0.8, 39, -56.02, 116.95, 0.2, 2, 4, 139.21, 47.04, 0.93, 38, 44.96, 109.18, 0.07, 2, 4, 120, 41.86, 0.93, 38, 25.75, 104, 0.07, 2, 4, 128.35, 56.21, 0.93, 38, 34.11, 118.35, 0.07, 2, 4, 114.82, 49.8, 0.93, 38, 20.58, 111.94, 0.07, 2, 4, 219.36, -83.71, 0.7, 38, 125.12, -21.57, 0.3, 2, 4, 184.58, -85.83, 0.7, 38, 90.34, -23.69, 0.3, 2, 4, 153.96, -80.59, 0.7, 38, 59.71, -18.45, 0.3, 2, 4, 134.4, -74.24, 0.7, 38, 40.16, -12.1, 0.3, 2, 4, 121.33, -68.97, 0.7, 38, 27.09, -6.83, 0.3, 2, 4, 106.37, -69.18, 0.7, 38, 12.12, -7.03, 0.3, 2, 4, 94.96, -67.72, 0.7, 38, 0.72, -5.58, 0.3, 2, 4, 87.24, -60.22, 0.7, 38, -7, 1.92, 0.3, 2, 4, 75.54, -57.49, 0.7, 38, -18.7, 4.65, 0.3, 2, 4, 66.18, -55.2, 0.7, 38, -28.07, 6.94, 0.3, 2, 4, 56.65, -47.76, 0.7, 38, -37.59, 14.38, 0.3, 2, 4, 41.19, -40.24, 0.7, 38, -53.06, 21.9, 0.3, 2, 4, 98.52, -40.57, 0.75, 38, 4.28, 21.57, 0.25, 2, 4, 85.7, -73.36, 0.75, 38, -8.55, -11.22, 0.25, 2, 4, 80.61, -85.42, 0.85, 38, -13.63, -23.28, 0.15, 2, 4, 106.6, -18.27, 0.82, 38, 12.36, 43.87, 0.18, 2, 4, 108.25, 10.26, 0.85, 38, 14, 72.4, 0.15, 2, 4, 208.09, -112.64, 0.75, 38, 113.85, -50.5, 0.25, 2, 4, 172.09, -116.08, 0.75, 38, 77.85, -53.94, 0.25, 2, 4, 148.07, -113.55, 0.85, 38, 53.83, -51.41, 0.15, 2, 4, 136.13, -108.32, 0.85, 38, 41.88, -46.18, 0.15, 2, 4, 131.81, -106.14, 0.85, 38, 37.57, -44, 0.15, 2, 4, 106.18, -91.11, 0.85, 38, 11.93, -28.97, 0.15, 2, 4, 98.44, -90.77, 0.85, 38, 4.2, -28.63, 0.15, 2, 4, 65.68, -74.22, 0.85, 38, -28.57, -12.08, 0.15, 2, 4, 59.38, -70.36, 0.85, 38, -34.87, -8.22, 0.15, 2, 4, 50.33, -58.87, 0.75, 38, -43.91, 3.27, 0.25, 2, 4, 42.72, -50.03, 0.75, 38, -51.53, 12.11, 0.25, 2, 4, 43.28, -24.91, 0.75, 38, -50.96, 37.23, 0.25, 2, 4, 59.58, -21.31, 0.75, 38, -34.66, 40.83, 0.25, 2, 4, 73.39, -18.37, 0.82, 38, -20.85, 43.77, 0.18, 2, 4, 83.58, -16.7, 0.82, 38, -10.67, 45.44, 0.18, 2, 4, 86.5, 8.02, 0.85, 38, -7.74, 70.16, 0.15, 2, 4, 67.81, 1.86, 0.85, 38, -26.44, 64, 0.15, 2, 4, 51.46, -9.54, 0.85, 38, -42.79, 52.6, 0.15, 2, 4, 133.67, 14.22, 0.85, 38, 39.43, 76.36, 0.15, 2, 4, 134.81, -19.63, 0.82, 38, 40.56, 42.51, 0.18, 2, 4, 128.44, -19.16, 0.82, 38, 34.2, 42.98, 0.18, 2, 4, 130.91, -44.64, 0.75, 38, 36.67, 17.5, 0.25, 2, 4, 126.51, -53.47, 0.75, 38, 32.26, 8.67, 0.25, 2, 4, 119.42, -72.57, 0.75, 38, 25.18, -10.43, 0.25, 2, 4, 135.73, -93.88, 0.75, 38, 41.48, -31.74, 0.25, 2, 4, 130.97, -79.91, 0.75, 38, 36.73, -17.77, 0.25, 2, 4, 125.39, -113.51, 0.91, 38, 31.15, -51.37, 0.09, 1, 4, 114.81, -111.39, 1, 2, 4, 105.91, -103.31, 0.91, 38, 11.67, -41.17, 0.09, 2, 4, 113.72, -81.02, 0.75, 38, 19.47, -18.88, 0.25, 2, 4, 120.79, -81.52, 0.75, 38, 26.55, -19.38, 0.25, 2, 4, 120.22, -90.91, 0.85, 38, 25.98, -28.77, 0.15, 2, 4, 117.07, -100.8, 0.87, 38, 22.83, -38.66, 0.13, 2, 4, 110.79, -107.74, 0.91, 38, 16.55, -45.6, 0.09, 2, 4, 119.9, -75.71, 0.75, 38, 25.66, -13.57, 0.25, 2, 4, 116.2, -81.45, 0.752, 38, 21.95, -19.31, 0.248, 2, 4, 109.54, -91.56, 0.85, 38, 15.3, -29.42, 0.15, 2, 4, 108.39, -103.12, 0.91, 38, 14.14, -40.98, 0.09, 2, 4, 112.43, -105.93, 0.91, 38, 18.19, -43.79, 0.09, 2, 4, 120.66, -107.68, 0.91, 38, 26.41, -45.54, 0.09, 2, 4, 126.05, -102.36, 0.85, 38, 31.8, -40.22, 0.15, 2, 4, 129.06, -91.96, 0.85, 38, 34.81, -29.82, 0.15, 2, 4, 125.68, -80.85, 0.75, 38, 31.43, -18.71, 0.25, 2, 4, 101.24, -80.49, 0.85, 38, 7, -18.35, 0.15, 2, 4, 104.61, -73.66, 0.75, 38, 10.36, -11.52, 0.25, 2, 4, 70.36, -67.29, 0.75, 38, -23.89, -5.15, 0.25, 2, 4, 61.25, -64.28, 0.75, 38, -32.99, -2.14, 0.25, 2, 4, 80.57, -36.89, 0.75, 38, -13.68, 25.25, 0.25, 2, 4, 68.25, -31.84, 0.75, 38, -26, 30.3, 0.25, 2, 4, 58.64, -31, 0.75, 38, -35.61, 31.14, 0.25, 2, 4, 132.54, -40, 0.75, 38, 38.29, 22.14, 0.25, 2, 4, 144.93, -40.27, 0.75, 38, 50.68, 21.87, 0.25, 2, 4, 156.14, -29.35, 0.82, 38, 61.9, 32.79, 0.18, 2, 4, 158.73, -11.77, 0.82, 38, 64.48, 50.37, 0.18, 2, 4, 157.33, 1.22, 0.85, 38, 63.09, 63.36, 0.15, 2, 4, 158.04, 10.33, 0.85, 38, 63.79, 72.47, 0.15, 2, 4, 146.21, 3.02, 0.85, 38, 51.97, 65.16, 0.15, 2, 4, 139.34, -4.29, 0.85, 38, 45.1, 57.85, 0.15, 2, 4, 134.38, -30.2, 0.82, 38, 40.13, 31.94, 0.18, 2, 4, 141.81, -30.46, 0.82, 38, 47.57, 31.68, 0.18, 2, 4, 147.64, -16.25, 0.82, 38, 53.4, 45.89, 0.18, 2, 4, 149.73, -3.8, 0.85, 38, 55.48, 58.34, 0.15, 2, 4, 152.85, 2.07, 0.85, 38, 58.6, 64.21, 0.15, 2, 4, 128.06, -28.75, 0.82, 38, 33.81, 33.4, 0.18, 2, 4, 127.55, -39, 0.75, 38, 33.31, 23.14, 0.25, 1, 4, 144.71, -43.91, 1, 2, 4, 159.72, -38.16, 0.75, 38, 65.47, 23.98, 0.25, 2, 4, 165.9, -14.19, 0.82, 38, 71.66, 47.95, 0.18, 2, 4, 165.35, 3.49, 0.85, 38, 71.1, 65.63, 0.15, 2, 4, 159.99, 21.65, 0.85, 38, 65.75, 83.79, 0.15, 2, 4, 145.41, 12.94, 0.792, 38, 51.16, 75.08, 0.208, 2, 4, 132.64, -1.16, 0.85, 38, 38.4, 60.98, 0.15, 2, 4, 159.65, 41.31, 0.87, 38, 65.41, 103.45, 0.13, 2, 4, 133.84, 31.63, 0.87, 38, 39.6, 93.77, 0.13, 2, 4, 104, 25.47, 0.87, 38, 9.76, 87.61, 0.13, 2, 4, 84.56, 20.95, 0.87, 38, -9.68, 83.09, 0.13, 2, 4, 67.3, 13.36, 0.87, 38, -26.94, 75.5, 0.13, 2, 4, 187.1, 44.24, 0.87, 38, 92.85, 106.38, 0.13, 2, 4, 191.35, 25.05, 0.85, 38, 97.11, 87.19, 0.15, 2, 4, 197.48, -12.36, 0.82, 38, 103.24, 49.78, 0.18, 2, 4, 139.76, -53.6, 0.75, 38, 45.51, 8.54, 0.25, 2, 4, 166.42, -54.59, 0.75, 38, 72.18, 7.55, 0.25, 2, 4, 189.32, -52.05, 0.75, 38, 95.08, 10.09, 0.25, 2, 4, 225.65, -44.64, 0.75, 38, 131.41, 17.5, 0.25, 2, 4, 225.91, -2.86, 0.82, 38, 131.67, 59.28, 0.18, 2, 4, 210.18, 32.43, 0.85, 38, 115.94, 94.57, 0.15, 2, 4, 151.69, -94.98, 0.75, 38, 57.45, -32.84, 0.25], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 46, 56, 56, 58, 60, 62, 36, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 88, 76, 76, 90, 90, 92, 88, 94, 94, 96, 34, 98, 98, 100, 100, 102, 102, 104, 104, 106, 108, 110, 110, 92, 92, 112, 112, 114, 114, 116, 116, 118, 120, 122, 122, 124, 124, 126, 96, 128, 128, 130, 130, 132, 134, 96, 136, 138, 138, 94, 140, 142, 106, 146, 146, 148, 148, 144, 106, 150, 150, 152, 108, 156, 158, 160, 160, 162, 152, 164, 164, 154, 144, 166, 166, 158, 166, 168, 168, 170, 170, 172, 162, 174, 174, 164, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 166, 156, 184, 144, 186, 186, 90, 90, 188, 190, 116, 88, 192, 194, 196, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 136, 136, 214, 198, 216, 216, 218, 218, 220, 220, 222, 138, 224, 224, 226, 140, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 138, 242, 244, 244, 246, 246, 248, 248, 250, 242, 252, 236, 254, 232, 256, 142, 258, 258, 260, 260, 262, 262, 264, 266, 268, 28, 30, 30, 32, 98, 30, 148, 270, 270, 100], "width": 207, "height": 235}}, "sunshangxiang018": {"sunshangxiang018": {"type": "mesh", "uvs": [1, 0, 0.96973, 0.15421, 0.88047, 0.31516, 0.7661, 0.46635, 0.60152, 0.58991, 0.40068, 0.64194, 0.28073, 0.71835, 0.25005, 0.85166, 0.30305, 1, 0.24447, 1, 0.16915, 1, 0.03805, 0.87605, 0, 0.77525, 0.00457, 0.62243, 0.15242, 0.46798, 0.31699, 0.37368, 0.45368, 0.32654, 0.58757, 0.21111, 0.69915, 0.09405, 0.82468, 0.00951, 0.93626, 0, 0.83026, 0.12169, 0.73542, 0.24687, 0.59594, 0.39807, 0.41184, 0.47286, 0.24726, 0.55252, 0.14126, 0.67283, 0.105, 0.82565, 0.19147, 0.92319], "triangles": [8, 28, 7, 10, 28, 9, 8, 9, 28, 10, 11, 28, 11, 27, 28, 28, 27, 7, 11, 12, 27, 7, 27, 6, 27, 26, 6, 27, 12, 26, 12, 13, 26, 6, 26, 5, 26, 25, 5, 26, 13, 25, 5, 24, 4, 5, 25, 24, 13, 14, 25, 24, 23, 4, 4, 23, 3, 25, 14, 24, 14, 15, 24, 24, 16, 23, 24, 15, 16, 3, 23, 2, 23, 16, 22, 23, 22, 2, 22, 16, 17, 2, 22, 1, 1, 22, 21, 17, 18, 22, 22, 18, 21, 21, 20, 1, 1, 20, 0, 18, 19, 21, 21, 19, 20], "vertices": [1, 12, 2.52, 7.08, 1, 1, 12, 27.14, 12.91, 1, 2, 12, 54.69, 13.85, 0.85491, 13, -9.42, 10.79, 0.14509, 2, 12, 81.56, 12, 6e-05, 13, 15.48, 21.08, 0.99994, 2, 13, 40.7, 24.81, 0.98995, 14, 0.65, 27.03, 0.01005, 2, 13, 60.34, 17.74, 0.3201, 14, 16.26, 13.17, 0.6799, 3, 13, 77.19, 18.86, 0.00102, 14, 32.32, 7.95, 0.97947, 15, -8.15, 13.6, 0.01952, 2, 14, 53.31, 14.29, 0.01341, 15, 13.28, 8.98, 0.98659, 1, 15, 37.78, 12.1, 1, 1, 15, 37.34, 6.55, 1, 1, 15, 36.78, -0.58, 1, 1, 15, 15.66, -11.41, 1, 2, 14, 51.8, -12.49, 0.4109, 15, -1.01, -13.72, 0.5891, 1, 14, 28.94, -22.4, 1, 2, 13, 57.77, -19.05, 0.01073, 14, 0.21, -20.04, 0.98927, 2, 13, 35.85, -19.34, 0.66204, 14, -20.25, -12.16, 0.33796, 3, 12, 70.21, -23.66, 0.0436, 13, 21.15, -15.91, 0.9212, 14, -32.62, -3.52, 0.03521, 2, 12, 48.19, -18.08, 0.78699, 13, -1.05, -20.71, 0.21301, 1, 12, 26.65, -14.59, 1, 1, 12, 9.63, -8.06, 1, 1, 12, 4.58, 1.39, 1, 1, 12, 26.65, -1.35, 1, 2, 12, 48.9, -2.89, 0.97993, 13, -7.16, -6.78, 0.02007, 1, 13, 19.45, 1.86, 1, 2, 13, 40.52, -1.43, 0.96381, 14, -9.26, 2.73, 0.03619, 1, 14, 9.03, -6.13, 1, 1, 14, 31.05, -7.18, 1, 1, 15, 7.97, -4.42, 1, 1, 15, 24.46, 2.52, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56], "width": 95, "height": 163}}, "sunshangxiang019": {"sunshangxiang019": {"type": "mesh", "uvs": [0.63124, 0.20164, 0.71714, 0.14056, 0.79577, 0.08041, 0.89255, 0.03137, 0.99054, 0, 1, 0.01656, 0.97723, 0.09429, 0.924, 0.15629, 0.85868, 0.21644, 0.7752, 0.28122, 0.67479, 0.34414, 0.56955, 0.40152, 0.45144, 0.47597, 0.37954, 0.54606, 0.27804, 0.62047, 0.23293, 0.69595, 0.25407, 0.77791, 0.32174, 0.82967, 0.4162, 0.86418, 0.48668, 0.89006, 0.50713, 0.93213, 0.51952, 0.98994, 0.43279, 1, 0.33366, 0.97762, 0.24445, 0.91981, 0.18374, 0.84778, 0.154, 0.79471, 0.07842, 0.77291, 0.01523, 0.69993, 0, 0.63359, 0.00037, 0.57198, 0.07099, 0.53028, 0.17011, 0.5009, 0.24198, 0.47247, 0.2903, 0.41371, 0.36712, 0.34357, 0.46355, 0.27396, 0.56318, 0.22968, 0.93073, 0.06387, 0.85264, 0.11845, 0.77859, 0.17613, 0.70454, 0.24101, 0.62376, 0.28632, 0.51471, 0.34605, 0.41373, 0.39857, 0.30872, 0.49229, 0.21447, 0.57262, 0.16466, 0.66324, 0.18216, 0.7549, 0.23332, 0.83008, 0.30199, 0.89187, 0.407, 0.93718, 0.46893, 0.96396, 0.15254, 0.55305, 0.09061, 0.61484, 0.0758, 0.68796, 0.10946, 0.73945], "triangles": [38, 3, 4, 38, 4, 5, 6, 38, 5, 39, 2, 3, 39, 3, 38, 7, 39, 38, 7, 38, 6, 40, 1, 2, 40, 2, 39, 8, 39, 7, 40, 39, 8, 41, 0, 1, 41, 1, 40, 9, 41, 40, 9, 40, 8, 42, 37, 0, 42, 0, 41, 10, 42, 41, 10, 41, 9, 43, 36, 37, 43, 37, 42, 11, 43, 42, 35, 36, 43, 44, 35, 43, 10, 11, 42, 34, 35, 44, 11, 12, 44, 11, 44, 43, 45, 34, 44, 45, 44, 12, 33, 34, 45, 13, 45, 12, 53, 31, 32, 46, 32, 33, 46, 33, 45, 46, 45, 13, 53, 32, 46, 54, 31, 53, 30, 31, 54, 14, 46, 13, 29, 30, 54, 46, 54, 53, 47, 46, 14, 47, 54, 46, 55, 29, 54, 55, 54, 47, 15, 47, 14, 28, 29, 55, 56, 55, 47, 48, 56, 47, 15, 48, 47, 56, 28, 55, 27, 28, 56, 48, 15, 16, 26, 56, 48, 27, 56, 26, 48, 49, 26, 49, 16, 17, 16, 49, 48, 25, 26, 49, 50, 49, 17, 50, 17, 18, 24, 49, 50, 25, 49, 24, 51, 50, 18, 51, 18, 19, 51, 19, 20, 52, 51, 20, 23, 50, 51, 24, 50, 23, 52, 20, 21, 22, 51, 52, 23, 51, 22, 22, 52, 21], "vertices": [2, 53, 67.57, -14.02, 0.57412, 54, -1.37, -14.33, 0.42588, 1, 53, 43.57, -12.58, 1, 1, 53, 20.78, -12.04, 1, 1, 53, -2.43, -6.76, 1, 1, 53, -22.37, 1.97, 1, 1, 53, -20.5, 6.44, 1, 1, 53, -2.21, 17.44, 1, 1, 53, 17.35, 21.02, 1, 1, 53, 38.26, 22.45, 1, 2, 53, 62.63, 22.06, 0.84181, 54, -11.04, 20.78, 0.15819, 2, 53, 89.04, 18.8, 0.04296, 54, 15.57, 21.05, 0.95704, 2, 54, 42.02, 19.54, 0.99237, 55, -18.89, 23.46, 0.00763, 2, 54, 73.39, 19.95, 0.1433, 55, 12.01, 18.03, 0.8567, 2, 55, 35.87, 19.04, 0.99608, 56, -11.4, 27.63, 0.00392, 2, 55, 64.55, 16.19, 0.12612, 56, 12.58, 11.64, 0.87388, 2, 56, 34.34, 7, 0.99437, 57, -9.99, 18.65, 0.00563, 2, 56, 54.84, 16, 0.14514, 57, 11.21, 11.43, 0.85486, 2, 57, 30.15, 16.51, 0.90656, 58, -8.48, 26.92, 0.09344, 2, 57, 47.84, 28.67, 0.27927, 58, 12.96, 26.09, 0.72073, 2, 57, 61.07, 37.72, 0.04765, 58, 28.98, 25.44, 0.95235, 2, 57, 72.93, 35.71, 0.00593, 58, 37.29, 16.74, 0.99407, 1, 58, 45.76, 3.52, 1, 1, 58, 30.49, -5.99, 1, 1, 58, 9.46, -8.53, 1, 1, 57, 43.16, -9.28, 1, 1, 57, 20.22, -10.42, 1, 2, 56, 63.68, -3.05, 0.0789, 57, 4.85, -8.59, 0.9211, 2, 56, 61.34, -19.44, 0.77415, 57, -7.95, -19.09, 0.22585, 2, 56, 45.06, -36.33, 0.99471, 57, -31.36, -20.54, 0.00529, 1, 56, 28.38, -43.24, 1, 2, 55, 91.67, -35.52, 0.01474, 56, 12.25, -46.75, 0.98526, 2, 55, 73.77, -31.78, 0.11787, 56, -1.8, -35.04, 0.88213, 2, 55, 54.57, -21.45, 0.65607, 56, -13.9, -16.91, 0.34393, 2, 55, 39.21, -15.21, 0.99566, 56, -24.53, -4.18, 0.00434, 2, 54, 88.71, -13.74, 0.00117, 55, 20.8, -17.92, 0.99883, 2, 54, 64.67, -18.54, 0.58959, 55, -3.72, -18.17, 0.41041, 1, 54, 37.57, -20.72, 1, 2, 53, 82.64, -18.93, 0.02401, 54, 14.22, -17.2, 0.97599, 1, 53, -1.53, 4.92, 1, 1, 53, 20.11, 3.43, 1, 1, 53, 41.77, 3.12, 1, 2, 53, 64.83, 4.14, 0.95272, 54, -6.49, 3.31, 0.04728, 1, 54, 14.03, 2.41, 1, 1, 54, 41.48, 0.91, 1, 2, 54, 66.44, -1.07, 0.29979, 55, 1.27, -1.32, 0.70021, 1, 55, 34.35, -1.36, 1, 2, 55, 63.27, -2.05, 0.11629, 56, 2.89, -3.86, 0.88371, 1, 56, 28.82, -8.56, 1, 2, 56, 52.02, 0.27, 0.84227, 57, -1.5, 1.74, 0.15773, 3, 56, 69.41, 14.88, 2e-05, 57, 21.19, 0.76, 0.9999, 58, -25.08, 19.64, 7e-05, 2, 57, 42.57, 4.68, 0.93551, 58, -5.6, 10.01, 0.06449, 2, 57, 63.85, 17.26, 0.03133, 58, 18.98, 7.38, 0.96867, 2, 57, 76.41, 24.67, 0.00061, 58, 33.48, 5.81, 0.99939, 2, 55, 67.54, -15.11, 0.24868, 56, 0.53, -17.39, 0.75132, 2, 55, 88.37, -13.99, 0.00887, 56, 19.45, -26.19, 0.99113, 2, 56, 39.23, -24.91, 0.99553, 57, -27.94, -8.18, 0.00447, 2, 56, 51.21, -15.18, 0.88694, 57, -12.54, -9.1, 0.11306], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 8, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 106, 108, 108, 110, 110, 112], "width": 205, "height": 268}}, "sunshangxiang21": {"sunshangxiang021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-100.09, 79.83, -102.84, 167.78, -61.86, 169.06, -59.11, 81.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 41}}, "sunshangxiang01": {"sunshangxiang01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [347, -645, -346, -645, -346, 645, 347, 645], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 415, "height": 774}}, "sunshangxiang02": {"sunshangxiang02": {"type": "mesh", "uvs": [0.31452, 0.09519, 0.3655, 0.03235, 0.41875, 0, 0.472, 0, 0.53204, 0.04112, 0.54224, 0.11419, 0.50485, 0.17702, 0.45387, 0.21502, 0.39156, 0.23109, 0.32925, 0.29685, 0.2624, 0.33338, 0.16837, 0.37576, 0.13552, 0.45467, 0.12419, 0.56865, 0.14911, 0.64756, 0.22728, 0.65195, 0.32698, 0.66948, 0.43688, 0.67094, 0.57396, 0.70309, 0.70992, 0.71478, 0.85879, 0.74752, 0.94879, 0.78092, 1, 0.79046, 1, 0.88588, 1, 0.97493, 0.88221, 1, 0.74289, 1, 0.5974, 1, 0.47904, 1, 0.33849, 0.97016, 0.23369, 0.92937, 0.12887, 0.87031, 0.05052, 0.7863, 0, 0.67736, 0, 0.55922, 0, 0.42928, 0, 0.31508, 0.04159, 0.26537, 0.10231, 0.22568, 0.13973, 0.16882, 0.21043, 0.12483, 0.27032, 0.10767, 0.46963, 0.07803, 0.39291, 0.12301, 0.32782, 0.16948, 0.24297, 0.21146, 0.15114, 0.26543, 0.09186, 0.32241, 0.05467, 0.42885, 0.05002, 0.55029, 0.07094, 0.66423, 0.13487, 0.73919, 0.22437, 0.76618, 0.33045, 0.80791, 0.45347, 0.84227, 0.57902, 0.85535, 0.71599, 0.87007, 0.86309, 0.88363, 0.93926, 0.88181], "triangles": [42, 2, 3, 42, 3, 4, 42, 4, 5, 43, 1, 2, 43, 2, 42, 0, 1, 43, 44, 0, 43, 6, 42, 5, 45, 40, 41, 41, 0, 44, 45, 41, 44, 7, 43, 42, 7, 42, 6, 8, 44, 43, 8, 43, 7, 45, 46, 39, 45, 39, 40, 38, 39, 46, 9, 44, 8, 45, 44, 9, 47, 37, 38, 47, 38, 46, 10, 45, 9, 46, 45, 10, 11, 46, 10, 47, 46, 11, 47, 36, 37, 12, 48, 47, 48, 36, 47, 35, 36, 48, 11, 12, 47, 49, 35, 48, 49, 48, 12, 34, 35, 49, 13, 49, 12, 50, 49, 13, 50, 13, 14, 50, 33, 34, 50, 34, 49, 51, 50, 14, 15, 51, 14, 52, 15, 16, 52, 51, 15, 32, 33, 50, 32, 50, 51, 53, 16, 17, 52, 16, 53, 54, 17, 18, 53, 17, 54, 55, 18, 19, 54, 18, 55, 56, 19, 20, 55, 19, 56, 31, 32, 51, 31, 51, 52, 21, 57, 20, 58, 21, 22, 58, 57, 21, 56, 20, 57, 58, 22, 23, 30, 52, 53, 31, 52, 30, 29, 53, 54, 30, 53, 29, 58, 23, 24, 28, 54, 55, 29, 54, 28, 27, 55, 56, 28, 55, 27, 26, 56, 57, 27, 56, 26, 25, 57, 58, 25, 58, 24, 26, 57, 25], "vertices": [2, 28, 124.05, -51.55, 0.82277, 29, -31.53, -48.39, 0.17723, 1, 28, 68.15, -73.19, 1, 1, 28, 18.17, -75.54, 1, 1, 28, -23.63, -58.19, 1, 1, 28, -60.38, -13.6, 1, 1, 28, -49.94, 34.2, 1, 1, 28, -4.71, 60.26, 1, 1, 28, 44.91, 66.78, 1, 2, 28, 97.89, 56.26, 0.95227, 29, -45.71, 61.65, 0.04773, 2, 28, 163.42, 75.99, 0.29516, 29, 21.58, 74.07, 0.70484, 3, 28, 225.13, 76.45, 0.01292, 29, 82.97, 67.77, 0.95738, 30, -33.73, 82.05, 0.0297, 3, 29, 166.46, 53.69, 0.13105, 30, 41.74, 43.67, 0.85958, 31, -44.57, 83.17, 0.00937, 3, 30, 98.6, 59.49, 0.54525, 31, 11.34, 64.24, 0.44452, 32, -21.46, 129.02, 0.01023, 4, 30, 159.5, 104.51, 0.01362, 31, 87.01, 67.19, 0.55468, 32, 28.31, 71.94, 0.42904, 33, -41.82, 131.7, 0.00265, 3, 31, 134.79, 96.69, 0.04139, 32, 81.26, 53.26, 0.78974, 33, -14.12, 82.85, 0.16887, 2, 32, 127.54, 101.03, 0.18834, 33, 52.14, 88.54, 0.81166, 3, 32, 192.45, 156.72, 0.00159, 33, 137.67, 87.98, 0.82042, 34, -33.05, 90.06, 0.17799, 2, 33, 230.43, 99.04, 0.11617, 34, 60.2, 95.69, 0.88383, 2, 34, 177.92, 82.78, 0.74995, 35, -27.74, 81.36, 0.25005, 3, 34, 293.74, 83.26, 0.0047, 35, 87.8, 89.34, 0.97433, 36, -80.72, 94.09, 0.02097, 2, 35, 216.09, 85.06, 0.15526, 36, 47.12, 82.51, 0.84474, 1, 36, 125.12, 66.58, 1, 1, 36, 169, 63.73, 1, 1, 36, 173.94, 1.04, 1, 1, 36, 178.55, -57.47, 1, 2, 35, 258.3, -77.11, 0.01678, 36, 80.03, -81.79, 0.98322, 3, 34, 334.96, -102.26, 0.00138, 35, 140.96, -93.11, 0.81953, 36, -38.03, -91.09, 0.17909, 2, 34, 211.61, -110.98, 0.43862, 35, 18.43, -109.81, 0.56138, 3, 33, 293.87, -111.39, 0.00376, 34, 111.25, -118.08, 0.95354, 35, -81.25, -123.41, 0.0427, 2, 33, 172.86, -107.26, 0.46307, 34, -9.31, -106.9, 0.53693, 2, 33, 81.06, -92.07, 0.96173, 34, -100.06, -86.38, 0.03827, 2, 32, 179.61, -57.07, 0.34495, 33, -12.3, -64.93, 0.65505, 2, 32, 93.99, -70.12, 0.99871, 33, -85.47, -18.6, 0.00129, 2, 31, 175.14, -25.05, 0.04552, 32, 11.79, -54.55, 0.95448, 1, 31, 98.37, -37.94, 1, 2, 30, 166.22, -35.26, 0.21901, 31, 13.91, -52.12, 0.78099, 2, 30, 111.89, -87.33, 0.92788, 31, -60.3, -64.59, 0.07212, 3, 29, 225.79, -62, 0.00887, 30, 63.78, -84.47, 0.98944, 31, -98.46, -35.15, 0.00169, 2, 29, 167.96, -60.03, 0.32629, 30, 9.18, -65.31, 0.67371, 2, 29, 122.02, -77.52, 0.85045, 30, -39.88, -68.26, 0.14955, 2, 28, 213.25, -67.42, 0.00977, 29, 55.4, -73.94, 0.99023, 2, 28, 161.9, -58.36, 0.2824, 29, 5.35, -59.3, 0.7176, 1, 28, -2.06, -11.47, 1, 1, 28, 69.53, -9.08, 1, 1, 28, 132.37, -2, 1, 1, 29, 58.7, -10.59, 1, 2, 29, 144.23, -17.06, 0.50609, 30, -0.62, -17.21, 0.49391, 2, 29, 206.48, -8.47, 0.00151, 30, 61.35, -27.61, 0.99849, 2, 30, 133.87, -1.9, 0.23218, 31, 5.94, -6.34, 0.76782, 3, 30, 194.38, 50.62, 0.00016, 31, 85.52, 3.01, 0.98775, 32, -22.59, 32.84, 0.01209, 1, 32, 45.36, -3.72, 1, 2, 32, 118.38, 4.11, 0.98466, 33, -18.36, 21.41, 0.01534, 2, 32, 182.19, 49.19, 0.01238, 33, 59.37, 13.56, 0.98762, 1, 33, 152.33, -2.11, 1, 1, 34, 82.23, -15.93, 1, 2, 34, 189.29, -17, 0.81223, 35, -9.93, -17.48, 0.18777, 1, 35, 106.74, -11.36, 1, 1, 36, 57.81, -6.62, 1, 1, 36, 122.26, -0.34, 1], "hull": 42, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 0, 82, 8, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116], "width": 510, "height": 395}}, "sunshangxiang03": {"sunshangxiang03": {"type": "mesh", "uvs": [0.00536, 0.04275, 0.06913, 0.0593, 0.16934, 0.02747, 0.29991, 0, 0.45477, 0, 0.53372, 0.03766, 0.52917, 0.15222, 0.47906, 0.28843, 0.54435, 0.33935, 0.61419, 0.43483, 0.58382, 0.48829, 0.56257, 0.53157, 0.6734, 0.62705, 0.74552, 0.70967, 0.85516, 0.73572, 0.90997, 0.80466, 0.91546, 0.86747, 1, 0.89505, 1, 0.9548, 1, 0.9931, 0.90449, 1, 0.7437, 1, 0.48423, 1, 0.50981, 0.92875, 0.40748, 0.81079, 0.3216, 0.64992, 0.26679, 0.51511, 0.18091, 0.39714, 0.0731, 0.28377, 0.01645, 0.20717, 0, 0.11678, 0.19108, 0.14393, 0.25494, 0.25723, 0.36334, 0.15015, 0.30691, 0.36182, 0.3782, 0.51373, 0.38562, 0.28836, 0.44948, 0.40042, 0.47324, 0.52991, 0.51333, 0.64945, 0.60095, 0.77645, 0.71976, 0.88851, 0.8044, 0.79637, 0.85193, 0.88851], "triangles": [21, 43, 20, 19, 20, 18, 20, 43, 16, 22, 23, 21, 23, 41, 21, 21, 41, 43, 20, 16, 18, 16, 17, 18, 23, 40, 41, 23, 24, 40, 41, 42, 43, 43, 15, 16, 43, 42, 15, 42, 40, 13, 42, 41, 40, 24, 39, 40, 24, 25, 39, 42, 14, 15, 42, 13, 14, 40, 12, 13, 40, 39, 12, 25, 35, 39, 25, 26, 35, 35, 38, 39, 39, 11, 12, 39, 38, 11, 11, 38, 10, 10, 38, 37, 26, 34, 35, 26, 27, 34, 38, 35, 37, 35, 34, 37, 10, 37, 9, 37, 8, 9, 34, 36, 37, 37, 7, 8, 37, 36, 7, 27, 32, 34, 27, 28, 32, 34, 32, 36, 6, 7, 33, 32, 33, 36, 7, 36, 33, 28, 31, 32, 28, 29, 31, 32, 31, 33, 31, 30, 1, 31, 29, 30, 6, 33, 5, 31, 3, 33, 33, 4, 5, 33, 3, 4, 1, 2, 31, 31, 2, 3, 30, 0, 1], "vertices": [1, 44, -41.78, 51.32, 1, 1, 44, -22.66, 45.15, 1, 2, 44, 7.63, 56.21, 0.9663, 45, -107.64, 18.11, 0.0337, 2, 44, 47.05, 65.6, 0.80627, 45, -99.61, 57.83, 0.19373, 2, 44, 93.66, 65.03, 0.63255, 45, -79.53, 99.9, 0.36745, 2, 44, 117.26, 51.23, 0.57914, 45, -57.1, 115.52, 0.42086, 2, 44, 115.4, 10.12, 0.44516, 45, -20.57, 96.57, 0.55484, 3, 44, 99.73, -38.59, 0.09629, 45, 17.06, 61.9, 0.90338, 46, -80.52, 58.33, 0.00032, 3, 44, 119.16, -57.11, 0.0089, 45, 42.03, 71.76, 0.97081, 46, -56.03, 69.3, 0.02029, 2, 45, 82.01, 75.97, 0.86597, 46, -16.27, 75.31, 0.13403, 3, 45, 95.4, 59.45, 0.72974, 46, -2.16, 59.41, 0.27011, 47, -77.12, 68.8, 0.00015, 3, 45, 106.67, 46.98, 0.41042, 46, 9.66, 47.47, 0.58156, 47, -66.76, 55.57, 0.00803, 3, 45, 151.97, 62.33, 0.00547, 46, 54.23, 64.84, 0.72142, 47, -20.49, 67.66, 0.27311, 2, 46, 90, 73.27, 0.2666, 47, 16.03, 71.9, 0.7334, 2, 46, 111.47, 100.02, 0.06877, 47, 40.45, 95.98, 0.93123, 2, 46, 140.7, 105.57, 0.0236, 47, 70.12, 98.12, 0.9764, 2, 46, 162.11, 98.31, 0.00486, 47, 90.54, 88.42, 0.99514, 1, 47, 111.71, 105.67, 1, 1, 47, 130.37, 95.08, 1, 1, 47, 142.33, 88.29, 1, 1, 47, 130.29, 62.07, 1, 1, 47, 106.4, 19.97, 1, 1, 47, 67.85, -47.95, 1, 1, 47, 49.41, -28.63, 1, 2, 46, 83.79, -34.58, 0.55766, 47, -2.63, -34.51, 0.44234, 2, 45, 113.77, -36.77, 0.11852, 46, 20.53, -35.89, 0.88148, 2, 45, 62.98, -30.82, 0.98272, 46, -30.47, -32.22, 0.01728, 2, 44, 9.52, -76.54, 0.04756, 45, 13.63, -35.91, 0.95244, 2, 44, -22.44, -35.45, 0.73217, 45, -37.08, -47.66, 0.26783, 2, 44, -39.16, -7.74, 0.9828, 45, -69.24, -51.2, 0.0172, 1, 44, -43.72, 24.76, 1, 2, 44, 13.68, 14.32, 0.94012, 45, -67.09, 6.01, 0.05988, 2, 44, 32.41, -26.58, 0.31586, 45, -22.1, 5.84, 0.68414, 2, 44, 65.5, 11.47, 0.53592, 45, -42.74, 51.85, 0.46408, 1, 45, 18.52, 3.78, 1, 2, 45, 76.98, -0.34, 1, 46, -17.86, -1.15, 0, 2, 44, 71.6, -38.23, 0.09581, 45, 4.93, 36.52, 0.90419, 3, 44, 90.34, -78.69, 0.00131, 45, 49.51, 36.54, 0.98358, 46, -46.96, 34.46, 0.01511, 2, 45, 94.55, 22.97, 0.56299, 46, -1.36, 22.94, 0.43701, 3, 45, 138.47, 15.38, 0.00507, 46, 42.86, 17.33, 0.97672, 47, -37.28, 21.79, 0.01821, 2, 46, 95.13, 23.85, 0.17901, 47, 15.39, 22.22, 0.82099, 2, 46, 146.11, 41.11, 0.0059, 47, 68.03, 33.46, 0.9941, 2, 46, 125.57, 77.47, 0.05671, 47, 51.84, 71.95, 0.94329, 2, 46, 161.61, 77.75, 0.00452, 47, 87.67, 68.06, 0.99548], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 4, 62, 62, 64, 64, 68, 68, 70, 72, 74, 70, 78, 78, 80, 80, 82, 84, 86], "width": 301, "height": 359}}, "sunshangxiang04": {"sunshangxiang04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [33.33, -131.48, 26.12, 99.4, 282.99, 107.43, 290.21, -123.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 231, "height": 257}}, "sunshangxiang05": {"sunshangxiang05": {"type": "mesh", "uvs": [0, 0.90157, 0.05958, 0.80697, 0.09929, 0.83738, 0.98037, 0, 1, 0, 1, 0.04009, 1, 0.08415, 0.14297, 0.89147, 0.14511, 0.9246, 0.09436, 0.97736, 0.0495, 1, 0, 1, 0, 0.95712, 0.05751, 0.91172, 0.12374, 0.86754], "triangles": [3, 4, 5, 5, 2, 3, 2, 5, 14, 5, 6, 14, 7, 14, 6, 13, 0, 1, 13, 1, 2, 13, 2, 14, 12, 0, 13, 13, 8, 9, 14, 8, 13, 7, 8, 14, 10, 12, 13, 10, 11, 12, 13, 9, 10], "vertices": [1, 9, 107.05, 12.7, 1, 1, 9, 103.34, -73.62, 1, 1, 9, 62.77, -73.43, 1, 1, 9, -220.95, -1003.14, 1, 1, 9, -234.87, -1012.33, 1, 1, 9, -251.22, -987.57, 1, 1, 9, -269.18, -960.36, 1, 1, 9, 9.74, -60.47, 1, 1, 9, -5.28, -41.01, 1, 1, 9, 9.2, 15.33, 1, 1, 9, 31.81, 50.32, 1, 1, 9, 66.92, 73.5, 1, 1, 9, 84.4, 47.01, 1, 1, 9, 62.11, -7.96, 1, 1, 9, 33.13, -66.25, 1], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 24, 26, 26, 28, 28, 10], "width": 510, "height": 444}}, "sunshangxiang06": {"sunshangxiang06": {"type": "mesh", "uvs": [0.36565, 0.58326, 0.25847, 0.53749, 0.1632, 0.45889, 0.23465, 0.3594, 0.33588, 0.24598, 0.46291, 0.16539, 0.47879, 0.08281, 0.54429, 0.02211, 0.53635, 0, 0.63956, 0, 0.68124, 0.01515, 0.78247, 0.02211, 0.86186, 0.08878, 0.91545, 0.18727, 0.97103, 0.29373, 0.98492, 0.39621, 0.91545, 0.47979, 1, 0.54147, 1, 0.62007, 0.98691, 0.6927, 0.95118, 0.74643, 0.88568, 0.7524, 0.81026, 0.831, 0.71102, 0.90164, 0.5562, 0.95835, 0.35926, 1, 0.17901, 1, 0.04002, 0.96165, 0, 0.91592, 0, 0.87565, 0.11603, 0.82666, 0.25284, 0.7842, 0.34623, 0.76243, 0.40052, 0.72106, 0.29628, 0.71127, 0.25067, 0.67861, 0.29628, 0.63071, 0.35274, 0.60785, 0.46201, 0.65337, 0.19219, 0.91663, 0.40098, 0.85303, 0.52786, 0.78943, 0.60977, 0.7218, 0.64671, 0.64693, 0.63707, 0.55274, 0.63707, 0.42071, 0.66919, 0.09126, 0.63868, 0.17499, 0.62583, 0.30219, 0.55035, 0.17821, 0.47325, 0.27482, 0.38974, 0.38914, 0.38813, 0.47206, 0.47165, 0.57833, 0.84225, 0.58875, 0.84225, 0.68836, 0.77666, 0.48237, 0.83646, 0.37406, 0.82489, 0.28509, 0.77666, 0.17194, 0.73614, 0.08684], "triangles": [7, 8, 9, 60, 10, 11, 60, 11, 12, 10, 7, 9, 46, 10, 60, 46, 7, 10, 6, 7, 46, 59, 60, 12, 46, 49, 6, 49, 5, 6, 46, 60, 59, 47, 46, 59, 46, 47, 49, 59, 12, 13, 50, 5, 49, 4, 5, 50, 58, 59, 13, 58, 13, 14, 48, 49, 47, 50, 49, 48, 57, 58, 14, 51, 4, 50, 3, 4, 51, 57, 14, 15, 48, 57, 45, 47, 59, 58, 58, 48, 47, 2, 3, 51, 51, 50, 48, 52, 2, 51, 51, 48, 45, 16, 57, 15, 57, 56, 45, 58, 57, 48, 56, 57, 16, 1, 2, 52, 44, 45, 56, 52, 51, 45, 44, 52, 45, 53, 52, 44, 0, 1, 52, 53, 0, 52, 54, 56, 16, 54, 16, 17, 44, 56, 54, 54, 17, 18, 43, 44, 54, 53, 44, 43, 53, 37, 0, 38, 53, 43, 38, 37, 53, 55, 54, 18, 43, 54, 55, 19, 55, 18, 38, 34, 36, 35, 36, 34, 38, 36, 37, 33, 34, 38, 42, 38, 43, 42, 43, 55, 33, 38, 42, 20, 55, 19, 21, 55, 20, 41, 33, 42, 32, 33, 41, 21, 22, 42, 21, 42, 55, 41, 42, 22, 40, 32, 41, 31, 32, 40, 23, 41, 22, 40, 41, 23, 39, 28, 29, 39, 30, 31, 39, 31, 40, 39, 29, 30, 24, 40, 23, 27, 28, 39, 26, 27, 39, 25, 39, 40, 25, 40, 24, 26, 39, 25], "vertices": [151.09, -45.05, 133.96, -67.04, 103.77, -87.25, 63.89, -74.84, 18.25, -56.78, -14.65, -33.04, -47.38, -31.33, -71.9, -19.42, -80.56, -21.38, -81.45, -0.96, -75.83, 7.55, -73.96, 27.69, -48.35, 44.55, -9.94, 56.85, 31.59, 69.69, 71.91, 74.21, 105.49, 61.92, 129.1, 79.71, 160.11, 81.07, 188.89, 79.74, 210.4, 73.6, 213.32, 60.75, 245, 47.19, 273.73, 28.78, 297.46, -0.86, 315.6, -39.1, 317.16, -74.76, 303.24, -102.91, 285.54, -111.62, 269.65, -112.32, 249.31, -90.21, 231.37, -63.89, 221.96, -45.79, 205.17, -35.77, 202.21, -56.56, 189.72, -66.14, 170.42, -57.95, 160.91, -47.18, 177.92, -24.77, 284.15, -73.59, 257.24, -33.39, 231.04, -9.39, 203.65, 5.64, 173.78, 11.65, 136.69, 8.11, 84.59, 5.83, -45.69, 6.48, -12.39, 1.89, 37.92, 1.55, -10.35, -15.52, 28.44, -29.1, 74.28, -43.64, 107.01, -42.53, 148.23, -24.17, 149.12, 49.32, 188.43, 51.05, 107.71, 34.51, 64.45, 44.46, 29.44, 40.63, -14.79, 29.13, -48.02, 19.65], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 66, 76, 54, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 20, 92, 92, 94, 94, 96, 96, 90, 98, 100, 100, 102, 102, 104, 104, 106, 108, 110, 108, 112, 112, 114, 114, 116, 116, 118, 118, 120], "width": 198, "height": 395}}, "sunshangxiang07": {"sunshangxiang07": {"type": "mesh", "uvs": [0.13846, 0.06734, 0.23706, 0, 0.40469, 0, 0.5493, 0.06518, 0.70378, 0.24061, 0.81553, 0.43554, 0.93385, 0.64564, 1, 0.75177, 0.98315, 0.88822, 0.87633, 0.99868, 0.65612, 1, 0.45234, 0.99868, 0.23542, 0.93587, 0.08423, 0.81674, 0.03, 0.62398, 0, 0.44854, 0, 0.26444, 0.05137, 0.14315, 0.14668, 0.24928, 0.19105, 0.44421, 0.28965, 0.66513, 0.42276, 0.82974, 0.28965, 0.12366, 0.41783, 0.23628, 0.5493, 0.43121, 0.6742, 0.65647, 0.75143, 0.84923], "triangles": [22, 1, 2, 23, 2, 3, 22, 2, 23, 22, 18, 0, 22, 0, 1, 17, 0, 18, 16, 17, 18, 24, 3, 4, 23, 3, 24, 24, 4, 5, 19, 18, 22, 19, 22, 23, 15, 16, 18, 15, 18, 19, 14, 15, 19, 25, 24, 5, 25, 5, 6, 20, 19, 23, 20, 23, 24, 14, 19, 20, 13, 14, 20, 21, 20, 24, 21, 24, 25, 26, 25, 6, 8, 26, 6, 7, 8, 6, 12, 13, 20, 12, 20, 21, 21, 10, 11, 25, 10, 21, 12, 21, 11, 9, 26, 8, 25, 26, 10, 10, 26, 9], "vertices": [-49.27, -13.29, -59.57, 16.76, -52.5, 64.17, -32.42, 103, 11.74, 141.09, 58.29, 166.47, 108.37, 193.22, 133.93, 208.54, 162.51, 199.42, 181.72, 165.67, 172.72, 103.33, 163.85, 45.73, 141.23, -13.62, 109.29, -52.59, 65.64, -61.76, 26.72, -64.64, -12.8, -58.76, -36.67, -40.35, -9.87, -16.78, 33.84, -10.46, 85.41, 10.37, 126.35, 42.76, -30.81, 27.68, -1.24, 60.34, 46.14, 91.3, 99.75, 119.43, 144.38, 135.11], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 50, 52], "width": 171, "height": 130}}, "sunshangxiang08": {"sunshangxiang08": {"type": "mesh", "uvs": [0.42831, 0.39747, 0.50172, 0.43643, 0.60618, 0.44941, 0.73746, 0.41973, 0.88286, 0.38078, 1, 0.33441, 0.9845, 0.26578, 0.87863, 0.18973, 0.74028, 0.11368, 0.62312, 0.05247, 0.49325, 0.00609, 0.37749, 0, 0.29279, 0.0098, 0.22221, 0.09884, 0.17704, 0.23054, 0.12198, 0.40118, 0.0754, 0.5607, 0.03023, 0.72579, 0, 0.88716, 0, 1, 0.10787, 1, 0.25186, 1, 0.30973, 0.86119, 0.35491, 0.68684, 0.3902, 0.52361, 0.15728, 0.80926, 0.20104, 0.60893, 0.2448, 0.43086, 0.29844, 0.29175, 0.37467, 0.18045, 0.53418, 0.22312, 0.65276, 0.26578, 0.77416, 0.31771], "triangles": [20, 25, 21, 21, 25, 22, 25, 20, 18, 20, 19, 18, 18, 17, 25, 22, 25, 23, 25, 17, 26, 25, 26, 23, 26, 17, 16, 23, 26, 24, 16, 15, 26, 26, 27, 24, 26, 15, 27, 24, 27, 0, 3, 2, 31, 31, 2, 30, 2, 1, 30, 1, 0, 30, 15, 14, 27, 27, 28, 0, 27, 14, 28, 3, 32, 4, 3, 31, 32, 28, 29, 0, 0, 29, 30, 32, 7, 4, 4, 6, 5, 4, 7, 6, 31, 8, 32, 32, 8, 7, 14, 13, 28, 28, 13, 29, 30, 9, 31, 31, 9, 8, 29, 10, 30, 30, 10, 9, 13, 12, 29, 29, 11, 10, 29, 12, 11], "vertices": [2, 50, 121.72, 70.49, 0.4974, 51, 65.96, 68.49, 0.5026, 2, 50, 82.47, 74.17, 0.89104, 51, 66.95, 107.91, 0.10896, 2, 50, 31.21, 63.99, 0.99349, 51, 53.29, 158.34, 0.00651, 1, 50, -28.21, 34.5, 1, 1, 50, -93.36, -0.37, 1, 1, 50, -144.23, -33.91, 1, 1, 50, -129.38, -56.63, 1, 1, 50, -70.61, -69.15, 1, 1, 50, 3.65, -77.04, 1, 1, 50, 66.2, -82.57, 1, 1, 50, 133.2, -80.89, 1, 2, 50, 189.09, -66.6, 0.91707, 51, -66.2, -8.1, 0.08293, 2, 50, 228.44, -50.97, 0.56208, 51, -47.91, -46.29, 0.43792, 2, 50, 252.46, -8.57, 0.11106, 51, -3.98, -67.35, 0.88894, 1, 51, 50.66, -70.89, 1, 1, 51, 120.84, -73.85, 1, 1, 51, 185.6, -74.34, 1, 1, 51, 252.08, -73.44, 1, 1, 51, 314.63, -66.06, 1, 1, 51, 354.67, -51.04, 1, 1, 51, 335.81, -0.74, 1, 1, 51, 310.63, 66.4, 1, 1, 51, 251.26, 74.91, 1, 1, 51, 181.48, 72.77, 1, 2, 50, 126.22, 121.73, 0.04934, 51, 117.39, 67.51, 0.95066, 1, 51, 259.48, -3.09, 1, 1, 51, 180.74, -9.34, 1, 1, 51, 109.9, -12.63, 1, 1, 51, 51.15, -6.13, 1, 1, 50, 170.86, -0.67, 1, 1, 50, 90.12, -7.92, 1, 1, 50, 28.91, -9.33, 1, 1, 50, -34.65, -7.78, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 40, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 498, "height": 379}}, "sunshangxiang09": {"sunshangxiang09": {"type": "mesh", "uvs": [0.28497, 0.30414, 0.37169, 0.32215, 0.46089, 0.36118, 0.56124, 0.42572, 0.67769, 0.49627, 0.78919, 0.5413, 0.88087, 0.5413, 0.96464, 0.48672, 1, 0.40492, 1, 0.29872, 0.96109, 0.21404, 0.86159, 0.13081, 0.74906, 0.08632, 0.64364, 0.05761, 0.52164, 0.03035, 0.38897, 0.00738, 0.27656, 0, 0.22581, 0.00355, 0.18442, 0.03288, 0.12897, 0.06505, 0.08915, 0.10762, 0.05791, 0.17385, 0.02234, 0.25641, 0, 0.35206, 0, 0.47914, 0, 0.60068, 0, 0.74334, 0, 1, 0.06616, 0.78246, 0.13903, 0.56692, 0.18618, 0.41371, 0.23548, 0.31763, 0.85365, 0.33824, 0.72464, 0.26779, 0.56655, 0.19514, 0.41209, 0.1489, 0.28489, 0.12248, 0.19403, 0.15331, 0.1377, 0.24577, 0.08864, 0.38007, 0.03776, 0.50776], "triangles": [27, 26, 28, 29, 28, 25, 29, 25, 40, 25, 28, 26, 25, 24, 40, 40, 39, 29, 29, 39, 30, 7, 6, 32, 6, 5, 32, 5, 4, 32, 40, 24, 39, 4, 33, 32, 4, 3, 33, 7, 32, 8, 24, 23, 39, 3, 34, 33, 3, 2, 34, 31, 30, 38, 32, 9, 8, 23, 22, 39, 30, 39, 38, 39, 22, 38, 1, 35, 2, 2, 35, 34, 32, 10, 9, 32, 11, 10, 32, 33, 11, 1, 0, 35, 0, 31, 37, 31, 38, 37, 37, 36, 0, 0, 36, 35, 34, 13, 33, 33, 12, 11, 33, 13, 12, 22, 21, 38, 21, 20, 38, 38, 20, 37, 37, 20, 19, 35, 14, 34, 34, 14, 13, 36, 17, 16, 17, 36, 18, 19, 18, 37, 36, 37, 18, 36, 15, 35, 35, 15, 14, 36, 16, 15], "vertices": [2, 48, 348.8, 66.92, 0.74404, 49, 48.11, 89.11, 0.25596, 2, 48, 295.7, 61.23, 0.98515, 49, 30.63, 139.57, 0.01485, 1, 48, 238.27, 65.25, 1, 1, 48, 170.85, 79.73, 1, 1, 48, 93.2, 94.43, 1, 1, 48, 21.93, 97.65, 1, 1, 48, -31.61, 82.46, 1, 1, 48, -73.06, 42.27, 1, 1, 48, -82.52, -3.02, 1, 1, 48, -67.99, -54.2, 1, 1, 48, -33.69, -88.56, 1, 1, 48, 35.8, -112.19, 1, 1, 48, 107.6, -114.98, 1, 1, 48, 173.08, -111.34, 1, 1, 48, 248.06, -104.27, 1, 1, 48, 328.66, -93.35, 1, 2, 48, 395.31, -78.28, 0.99979, 49, -82.9, 11.14, 0.00021, 2, 48, 424.47, -68.15, 0.9519, 49, -66.48, -14.99, 0.0481, 2, 48, 444.62, -47.16, 0.70627, 49, -41.49, -29.91, 0.29373, 2, 48, 472.6, -22.46, 0.16663, 49, -11.14, -51.62, 0.83337, 2, 48, 490.03, 4.66, 0.00644, 49, 19.2, -62.5, 0.99356, 1, 49, 57.41, -63.11, 1, 1, 49, 104.06, -62.07, 1, 1, 49, 152.58, -50.83, 1, 1, 49, 208.35, -20.12, 1, 1, 49, 261.69, 9.25, 1, 1, 49, 324.29, 43.72, 1, 1, 49, 436.93, 105.75, 1, 1, 49, 322.09, 88.36, 1, 1, 49, 206.17, 75.02, 1, 2, 48, 391.5, 136.1, 0.01701, 49, 125.12, 63.06, 0.98299, 2, 48, 375.85, 81.62, 0.33359, 49, 68.52, 66.05, 0.66641, 1, 48, 12.06, -10.9, 1, 1, 48, 97.04, -23.47, 1, 1, 48, 199.29, -32.29, 1, 1, 48, 295.81, -28.97, 1, 1, 48, 373.7, -20.62, 1, 2, 48, 422.53, 9.29, 0.18297, 49, 8.54, 4.31, 0.81703, 1, 49, 65.62, -3.3, 1, 2, 48, 453.06, 136.05, 1e-05, 49, 138.92, 3.07, 0.99999, 1, 49, 209.86, 6.87, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 16, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 46, 48, 48, 50, 50, 52, 52, 54], "width": 607, "height": 501}}, "sunshangxiang020": {"sunshangxiang020": {"type": "mesh", "uvs": [0, 0.30256, 0.05716, 0.19615, 0.14161, 0.10156, 0.27111, 0.02931, 0.4006, 0, 0.53122, 0, 0.68099, 0.03325, 0.81674, 0.11715, 0.90741, 0.23951, 0.96968, 0.35803, 1, 0.53264, 1, 0.66904, 1, 0.75889, 0.97277, 0.84408, 0.9772, 0.75873, 0.97645, 0.69344, 0.97438, 0.64039, 0.95737, 0.70478, 0.92703, 0.77198, 0.89728, 0.83879, 0.85563, 0.9108, 0.82068, 0.95939, 0.79242, 0.98282, 0.78349, 0.9056, 0.77382, 0.85788, 0.81398, 0.8145, 0.83332, 0.75723, 0.84224, 0.73467, 0.80283, 0.7312, 0.75003, 0.69823, 0.71656, 0.6696, 0.77011, 0.6453, 0.80655, 0.6132, 0.83445, 0.57814, 0.84445, 0.5368, 0.83673, 0.50047, 0.82073, 0.48514, 0.78722, 0.53288, 0.76293, 0.57663, 0.73222, 0.59496, 0.68579, 0.60746, 0.639, 0.60663, 0.58931, 0.57784, 0.54737, 0.54061, 0.49904, 0.51402, 0.45255, 0.49062, 0.34567, 0.53977, 0.22152, 0.5642, 0.09438, 0.54152, 0, 0.48044, 0, 0.39318, 0.05188, 0.44499, 0.05298, 0.35765, 0.11023, 0.25106, 0.22031, 0.1496, 0.38433, 0.09052, 0.54726, 0.09052, 0.70358, 0.15602, 0.83898, 0.28188, 0.91164, 0.40389, 0.96202, 0.5364, 0.87661, 0.45034, 0.90573, 0.52621, 0.89796, 0.60887, 0.84458, 0.66322, 0.77178, 0.68247, 0.89893, 0.69493, 0.88049, 0.77193, 0.84361, 0.84327, 0.81837, 0.89083, 0.7887, 0.37032, 0.7403, 0.43432, 0.68759, 0.5046, 0.61122, 0.53095, 0.77902, 0.47824, 0.73492, 0.53722, 0.82527, 0.41299, 0.66608, 0.56483, 0.76827, 0.32891, 0.68329, 0.33393, 0.58218, 0.37911, 0.50796, 0.43934, 0.55422, 0.47448, 0.63811, 0.44185, 0.71879, 0.38413, 0.72094, 0.25015, 0.61445, 0.22882, 0.49613, 0.26897, 0.38319, 0.32921, 0.2767, 0.43085, 0.17775, 0.47227, 0.09923, 0.47227, 0.15086, 0.35681, 0.27025, 0.25768, 0.38749, 0.18364, 0.52625, 0.16732, 0.6123, 0.17862], "triangles": [56, 5, 6, 55, 3, 4, 56, 55, 4, 56, 4, 5, 54, 2, 3, 54, 3, 55, 57, 6, 7, 56, 6, 57, 95, 55, 56, 96, 95, 56, 57, 96, 56, 94, 55, 95, 54, 55, 94, 85, 86, 96, 58, 85, 57, 7, 58, 57, 85, 96, 57, 53, 1, 2, 53, 2, 54, 93, 54, 94, 53, 54, 93, 87, 94, 95, 86, 87, 95, 86, 95, 96, 8, 58, 7, 0, 1, 53, 78, 85, 58, 88, 93, 94, 88, 94, 87, 79, 86, 85, 79, 85, 78, 92, 53, 93, 52, 0, 53, 52, 53, 92, 70, 78, 58, 80, 87, 86, 80, 86, 79, 84, 79, 78, 84, 78, 70, 50, 0, 52, 59, 8, 9, 58, 8, 59, 76, 70, 58, 59, 76, 58, 89, 93, 88, 92, 93, 89, 71, 84, 70, 71, 70, 76, 81, 87, 80, 88, 87, 81, 83, 80, 79, 83, 79, 84, 83, 84, 71, 51, 50, 52, 61, 76, 59, 91, 52, 92, 51, 52, 91, 90, 92, 89, 91, 92, 90, 82, 81, 80, 82, 80, 83, 74, 71, 76, 49, 50, 51, 36, 74, 76, 36, 76, 61, 45, 88, 81, 46, 89, 88, 35, 36, 61, 72, 83, 71, 75, 72, 71, 44, 45, 81, 44, 81, 82, 62, 61, 59, 35, 61, 62, 73, 82, 83, 73, 83, 72, 59, 9, 10, 60, 62, 59, 37, 74, 36, 74, 75, 71, 10, 60, 59, 34, 35, 62, 37, 75, 74, 45, 46, 88, 43, 44, 82, 43, 82, 73, 48, 51, 91, 49, 51, 48, 47, 90, 89, 47, 89, 46, 48, 91, 90, 47, 48, 90, 77, 73, 72, 77, 72, 75, 38, 75, 37, 42, 43, 73, 42, 73, 77, 39, 77, 75, 39, 75, 38, 41, 42, 77, 40, 77, 39, 41, 77, 40, 63, 34, 62, 63, 62, 60, 33, 34, 63, 16, 60, 10, 63, 60, 16, 64, 33, 63, 32, 33, 64, 16, 10, 11, 64, 65, 31, 64, 31, 32, 29, 30, 31, 15, 16, 11, 66, 63, 16, 17, 66, 16, 64, 63, 66, 65, 29, 31, 28, 65, 64, 29, 65, 28, 27, 28, 64, 27, 64, 66, 12, 14, 15, 12, 15, 11, 67, 27, 66, 26, 27, 67, 18, 66, 17, 67, 66, 18, 19, 67, 18, 67, 25, 26, 68, 67, 19, 68, 25, 67, 13, 14, 12, 69, 25, 68, 24, 25, 69, 23, 24, 69, 20, 68, 19, 69, 68, 20, 21, 69, 20, 23, 69, 21, 22, 23, 21], "vertices": [2, 21, 118.88, -36.58, 0.00245, 22, 70.84, -45.36, 0.99755, 2, 21, 97.51, -50.72, 0.04899, 22, 47.94, -56.84, 0.95101, 2, 21, 71.23, -59.97, 0.21692, 22, 20.74, -62.89, 0.78308, 3, 16, 16.95, 112.98, 0.00203, 21, 37.07, -60.63, 0.59139, 22, -13.25, -59.48, 0.40658, 3, 16, 23.89, 82.36, 0.04591, 21, 6.54, -53.32, 0.85144, 22, -42.69, -48.58, 0.10265, 3, 16, 24.86, 51.28, 0.22994, 21, -21.76, -40.46, 0.7661, 22, -69.26, -32.43, 0.00396, 3, 16, 19.2, 15.45, 0.73017, 19, -24.81, -52.03, 0.00287, 21, -51.4, -19.53, 0.26697, 4, 16, 3.1, -17.38, 0.86638, 23, -62.71, -9.44, 0.02965, 17, -46.19, -26.22, 0.08512, 19, -43.9, -20.84, 0.01885, 4, 16, -21.17, -39.73, 0.26625, 23, -33.66, 6.21, 0.36694, 17, -36.72, 5.39, 0.33621, 19, -49.75, 11.63, 0.0306, 3, 16, -44.88, -55.3, 0.01818, 23, -6.84, 15.43, 0.95492, 17, -24.28, 30.87, 0.0269, 2, 23, 29.5, 14.75, 0.60955, 24, -6.97, 13.06, 0.39045, 1, 24, 19.08, 22.83, 1, 2, 23, 74.55, 4.74, 1e-05, 24, 36.24, 29.26, 0.99999, 2, 23, 90.11, -5.36, 1e-05, 24, 54.79, 29.3, 0.99999, 2, 23, 73.34, -0.55, 1e-05, 24, 38.12, 24.17, 0.99999, 2, 23, 60.3, 2.16, 0, 24, 25.71, 19.33, 1, 2, 23, 49.63, 4.03, 0.00035, 24, 15.75, 15.07, 0.99965, 1, 24, 29.47, 15.89, 1, 1, 24, 44.84, 13.94, 1, 1, 24, 60.09, 12.1, 1, 1, 24, 77.32, 7.97, 1, 1, 24, 89.53, 3.66, 1, 1, 24, 96.36, -0.95, 1, 1, 24, 82.36, -8.47, 1, 1, 24, 74.05, -14.05, 1, 1, 24, 62.41, -8.21, 1, 1, 24, 49.85, -8, 1, 1, 24, 44.8, -7.63, 1, 1, 24, 47.43, -16.66, 1, 1, 24, 45.54, -30.79, 1, 2, 23, 42.14, -57.16, 0, 24, 42.87, -40.29, 1, 3, 23, 40.06, -43.65, 0.00111, 24, 33.76, -30.1, 0.99808, 17, 50.67, 22.26, 0.00081, 3, 23, 35.55, -33.76, 0.02354, 24, 24.58, -24.28, 0.9669, 17, 40.49, 26.06, 0.00955, 3, 23, 30.01, -25.73, 0.13349, 24, 15.55, -20.58, 0.8189, 17, 30.89, 27.83, 0.04761, 3, 23, 22.29, -21.58, 0.3726, 24, 6.82, -21.31, 0.45582, 17, 22.49, 25.31, 0.17157, 3, 23, 14.66, -21.76, 0.4254, 24, 0.52, -25.63, 0.15269, 17, 17.23, 19.78, 0.42191, 4, 23, 10.78, -24.8, 0.24077, 24, -1.07, -30.29, 0.04436, 17, 16.63, 14.89, 0.70502, 18, -15.13, 9.59, 0.00985, 4, 23, 18.56, -34.7, 0.01811, 24, 10.85, -34.34, 0.00153, 17, 29.13, 13.39, 0.44042, 18, -3.09, 13.27, 0.53994, 3, 23, 26.02, -42.28, 0.00034, 17, 39.76, 13.3, 0.06288, 18, 6.67, 17.49, 0.93678, 1, 18, 14.82, 16.54, 1, 1, 18, 25.43, 12.54, 1, 2, 18, 34.6, 6.22, 0.98789, 20, 8.5, 45.04, 0.01211, 3, 18, 41.19, -5.23, 0.75979, 19, 50.55, 32.46, 0.00048, 20, 17.08, 35, 0.23973, 2, 18, 45.28, -17.08, 0.38122, 20, 23.28, 24.1, 0.61878, 2, 18, 51.84, -27.98, 0.09576, 20, 31.73, 14.6, 0.90424, 3, 18, 58.4, -38.09, 0.00387, 20, 40.04, 5.86, 0.97338, 22, -1.27, 43.37, 0.02276, 2, 20, 67.37, 5.11, 0.4005, 22, 25.68, 38.73, 0.5995, 2, 20, 96.5, -1.9, 0.06576, 22, 53.51, 27.64, 0.93424, 2, 20, 122.52, -18.02, 0.00012, 22, 76.97, 7.97, 0.99988, 1, 22, 89.69, -14.35, 1, 1, 22, 80.44, -29.56, 1, 1, 22, 75.38, -14.11, 1, 2, 21, 112.05, -21.13, 0.00056, 22, 65.9, -29.2, 0.99944, 2, 21, 90.65, -35.29, 0.0425, 22, 42.97, -40.71, 0.9575, 2, 21, 58.23, -43.3, 0.31631, 22, 9.83, -44.79, 0.68369, 3, 16, 5.32, 85.65, 0.02244, 21, 17.71, -38.11, 0.83723, 22, -29.79, -34.81, 0.14033, 4, 16, 6.53, 46.89, 0.24233, 19, 8.53, -58.19, 0.01302, 21, -17.59, -22.07, 0.74403, 22, -62.93, -14.67, 0.00061, 4, 16, -5.67, 9.29, 0.73183, 17, -25.04, -44.68, 0.00273, 19, -16.68, -27.74, 0.16512, 21, -45.93, 5.49, 0.10032, 4, 16, -30.32, -23.72, 0.23191, 23, -28.75, -11.56, 0.2038, 17, -20.68, -3.71, 0.46496, 19, -31.34, 10.77, 0.09933, 3, 16, -54.66, -41.78, 0.00075, 23, -0.71, -0.08, 0.99353, 17, -8.97, 24.24, 0.00572, 2, 23, 28.28, 5.76, 0.63989, 24, -3.08, 4.86, 0.36011, 3, 23, 6.74, -10.28, 0.6993, 24, -12.38, -20.33, 0.02336, 17, 3.5, 22.3, 0.27734, 3, 23, 23.35, -6.87, 0.6934, 24, -0.33, -8.41, 0.26113, 17, 12.84, 36.45, 0.04547, 3, 23, 39.41, -12.33, 0.01958, 24, 16.11, -4.22, 0.97445, 17, 28.06, 43.95, 0.00597, 3, 23, 47.48, -27.14, 0.00357, 24, 30.96, -12.22, 0.99424, 17, 44.23, 39.18, 0.00219, 3, 23, 47.55, -44.9, 3e-05, 24, 40.72, -27.07, 0.99987, 17, 56.85, 26.67, 0.0001, 1, 24, 32.47, 2.16, 1, 1, 24, 48.72, 3.57, 1, 1, 24, 65.43, 0.46, 1, 1, 24, 76.62, -1.76, 1, 3, 16, -48.73, -12.33, 0.00755, 17, 0.96, -4.12, 0.9081, 19, -11.85, 20.2, 0.08434, 3, 17, 18.16, -6.83, 0.78626, 18, -4.96, -9.66, 0.08085, 19, 4.71, 25.57, 0.1329, 4, 17, 36.99, -9.72, 0.0116, 18, 13.43, -4.69, 0.91595, 19, 22.81, 31.52, 0.0536, 20, -10.3, 30.43, 0.01886, 3, 18, 31.54, -10.29, 0.7164, 19, 41.19, 26.89, 0.02918, 20, 8.53, 28.25, 0.25442, 4, 23, 7.25, -34.18, 0.03046, 24, 1.1, -40.08, 0.00322, 17, 20.77, 5.76, 0.96618, 18, -7.65, 2.91, 0.00014, 2, 17, 36.56, 3.36, 0.0004, 18, 7.75, 7.1, 0.9996, 4, 16, -57.16, -21.3, 5e-05, 23, -3.35, -20.55, 0.18837, 24, -15.23, -34.45, 0.00354, 17, 3.63, 7.9, 0.80804, 1, 18, 24.51, 2.7, 1, 4, 16, -40.44, -7.2, 0.0625, 23, -23.04, -30.08, 0.0067, 17, -3.55, -12.76, 0.55228, 19, -11.97, 10.46, 0.37851, 3, 17, 8.17, -29.27, 0.01212, 18, -5.03, -34.22, 0.0025, 19, 5.96, 1.04, 0.98538, 4, 16, -52.06, 36.75, 1e-05, 19, 31.36, -3.29, 0.20661, 20, 2.73, -2.96, 0.76494, 21, -0.8, 34.97, 0.02844, 2, 18, 41.62, -39.48, 0.00594, 20, 23.8, 1.42, 0.99406, 3, 18, 36.44, -27.41, 0.18271, 19, 47, 10.06, 0.00955, 20, 16.49, 12.33, 0.80774, 4, 17, 32.52, -26.52, 0.02964, 18, 16.13, -21.87, 0.33502, 19, 26.43, 14.51, 0.42504, 20, -4.49, 14.04, 0.2103, 3, 17, 12.27, -16.65, 0.41251, 18, -6.38, -21.02, 0.05933, 19, 3.91, 14.15, 0.52816, 5, 16, -24.73, 4.56, 0.29234, 23, -41.17, -37.59, 0.002, 17, -11.06, -30.89, 0.06256, 19, -10.46, -9.11, 0.61454, 21, -41.75, 24.68, 0.02856, 4, 16, -21.17, 30.03, 0.17073, 19, 9.14, -25.76, 0.4239, 20, -16.36, -28.15, 0.01033, 21, -20.48, 10.23, 0.39503, 3, 19, 37.54, -33.05, 0.02399, 20, 12.76, -31.66, 0.08348, 21, 8.55, 6.04, 0.89253, 3, 20, 42.3, -30.9, 0.08648, 21, 38.11, 6.1, 0.77209, 22, -4.27, 6.66, 0.14143, 2, 20, 73.75, -21.77, 0.08337, 22, 28.16, 11.21, 0.91663, 2, 20, 98.73, -23.23, 0.00783, 22, 52.67, 6.2, 0.99217, 1, 22, 68.64, -3.51, 1, 2, 21, 90.78, -11.65, 0.0027, 22, 45.91, -17.25, 0.9973, 2, 21, 56.54, -18.3, 0.18954, 22, 11.12, -19.77, 0.81046, 3, 16, -13.65, 84.31, 0.00422, 21, 24.89, -20.51, 0.88172, 22, -20.57, -18.18, 0.11406, 4, 16, -9.29, 51.4, 0.09232, 19, 20.81, -47.25, 0.01705, 21, -6.56, -9.87, 0.89053, 22, -50.51, -3.88, 0.0001, 4, 16, -10.95, 30.86, 0.29507, 19, 4.36, -34.83, 0.23243, 20, -19.9, -37.77, 0.00047, 21, -24.25, 0.7, 0.47202], "hull": 51, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 0, 100, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 122, 124, 124, 126, 126, 128, 128, 130, 126, 132, 132, 134, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 148, 150, 148, 152, 150, 154, 154, 84, 156, 158, 158, 160, 160, 162, 164, 166, 166, 168, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 170], "width": 238, "height": 204}}, "sunshangxiang022": {"sunshangxiang022": {"type": "mesh", "uvs": [0.34276, 0.55077, 0.25669, 0.49353, 0.2198, 0.2155, 0.40629, 0, 0.7137, 0, 1, 0.07485, 0.91249, 0.35288, 0.57844, 0.64563, 0.51901, 0.58839, 0.40834, 0.6211, 0.44113, 0.68652, 0.4022, 0.75848, 0.27718, 0.77647, 0.24849, 0.89259, 0.25054, 1, 0.09889, 1, 0, 0.93511, 0.05175, 0.81899, 0.15217, 0.74049, 0.25874, 0.64563, 0.27103, 0.57203, 0.38136, 0.5772, 0.33736, 0.6453, 0.34669, 0.70063, 0.26136, 0.7251, 0.19336, 0.79, 0.14669, 0.87513, 0.56772, 0.29663], "triangles": [27, 3, 4, 2, 3, 27, 6, 4, 5, 27, 4, 6, 1, 2, 27, 0, 1, 27, 8, 21, 0, 27, 8, 0, 8, 27, 6, 9, 21, 8, 22, 20, 0, 22, 0, 21, 22, 21, 9, 19, 20, 22, 7, 8, 6, 22, 9, 10, 23, 22, 10, 23, 24, 19, 23, 19, 22, 18, 19, 24, 11, 23, 10, 12, 24, 23, 12, 23, 11, 25, 18, 24, 25, 24, 12, 17, 18, 25, 26, 17, 25, 13, 25, 12, 26, 25, 13, 16, 17, 26, 15, 16, 26, 26, 13, 14, 15, 26, 14], "vertices": [2, 25, -53.72, 47.66, 0.03, 26, -0.09, -8.15, 0.97, 2, 25, -42.54, 61.96, 0.177, 26, -2.59, -26.13, 0.823, 2, 25, 13.69, 69.7, 0.7242, 26, -47.37, -61, 0.2758, 2, 25, 58.36, 40.86, 0.96384, 26, -100.48, -58.44, 0.03616, 1, 25, 59.91, -8.91, 1, 1, 25, 46.17, -55.74, 1, 2, 25, -10.68, -43.34, 0.98782, 26, -82.93, 49.02, 0.01218, 2, 25, -71.77, 8.9, 0.26845, 26, -3.89, 34.44, 0.73155, 2, 25, -60.46, 18.88, 0.19674, 26, -8.67, 20.13, 0.80326, 2, 25, -67.65, 36.59, 0.0036, 26, 6.43, 8.41, 0.9964, 1, 26, 14.9, 19.93, 1, 1, 26, 30.64, 22.28, 1, 2, 26, 44.43, 7.01, 0.26354, 27, 4.55, 6.3, 0.73646, 1, 27, 28.18, 10.64, 1, 1, 27, 48.34, 18.97, 1, 1, 27, 57.37, -3.88, 1, 1, 27, 51.01, -23.62, 1, 1, 27, 26, -24.49, 1, 2, 26, 48.92, -14.04, 0.07766, 27, 5.2, -15.22, 0.92234, 2, 26, 23.45, -9.55, 0.99739, 27, -19.05, -6.24, 0.00261, 1, 26, 9.71, -15.74, 1, 1, 26, 1.17, -0.01, 1, 1, 26, 16.67, 1.23, 1, 1, 26, 25.41, 8.45, 1, 1, 26, 36.93, -0.67, 1, 1, 27, 12.1, -5.32, 1, 1, 27, 30.95, -6, 1, 2, 25, -1.01, 12.85, 0.90485, 26, -63.14, -4.44, 0.09515], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 162, "height": 203}}, "sunshangxiang013": {"sunshangxiang013": {"type": "mesh", "uvs": [0, 0.18075, 0, 0.10904, 0.039, 0.03174, 0.11882, 0, 0.21898, 0, 0.27376, 0.02988, 0.32385, 0.01684, 0.40054, 0.02056, 0.38958, 0.05968, 0.43653, 0.10811, 0.532, 0.1435, 0.66309, 0.16023, 0.8119, 0.18128, 0.79717, 0.25405, 0.7456, 0.32068, 0.68667, 0.37504, 0.76034, 0.40047, 0.78096, 0.43641, 0.75297, 0.46885, 0.67193, 0.47587, 0.73042, 0.51858, 0.80161, 0.58333, 0.87146, 0.65686, 0.93996, 0.73439, 1, 0.80153, 1, 0.88306, 1, 1, 0.87146, 0.90144, 0.74923, 0.80953, 0.62297, 0.7272, 0.54372, 0.66405, 0.47656, 0.58013, 0.39999, 0.4938, 0.31672, 0.47702, 0.24284, 0.45304, 0.24015, 0.41627, 0.17568, 0.35952, 0.09643, 0.33155, 0.01176, 0.27, 0, 0.21997, 0.12764, 0.0754, 0.14633, 0.15668, 0.20528, 0.2277, 0.29156, 0.29786, 0.39652, 0.36289, 0.47417, 0.41765, 0.53024, 0.49209, 0.59351, 0.55541, 0.67135, 0.63631, 0.7569, 0.70918, 0.84581, 0.78305, 0.91626, 0.85792, 0.59347, 0.41173, 0.72174, 0.43031, 0.35924, 0.43363, 0.56447, 0.3666, 0.29343, 0.39713, 0.2187, 0.0532, 0.28674, 0.12289, 0.38378, 0.19258, 0.50758, 0.2344, 0.63139, 0.26958, 0.3927, 0.26692, 0.54885, 0.31604], "triangles": [26, 27, 25, 27, 51, 25, 27, 28, 51, 51, 28, 50, 51, 24, 25, 51, 50, 24, 28, 49, 50, 28, 29, 49, 50, 23, 24, 50, 49, 23, 49, 22, 23, 29, 48, 49, 29, 30, 48, 49, 48, 22, 30, 47, 48, 30, 31, 47, 48, 21, 22, 48, 20, 21, 48, 47, 20, 31, 46, 47, 31, 32, 46, 47, 19, 20, 47, 46, 19, 33, 54, 32, 32, 45, 46, 32, 54, 45, 46, 52, 19, 46, 45, 52, 33, 34, 54, 19, 53, 18, 19, 52, 53, 18, 53, 17, 34, 56, 54, 34, 35, 56, 53, 16, 17, 54, 44, 45, 54, 56, 44, 52, 15, 53, 53, 15, 16, 45, 55, 52, 45, 44, 55, 35, 36, 56, 52, 55, 15, 36, 43, 56, 56, 43, 44, 61, 14, 15, 44, 63, 55, 61, 15, 63, 63, 15, 55, 43, 62, 44, 44, 62, 63, 36, 37, 43, 37, 42, 43, 37, 38, 42, 14, 61, 13, 62, 60, 63, 63, 60, 61, 43, 42, 62, 38, 39, 42, 61, 11, 13, 61, 60, 11, 42, 59, 62, 62, 59, 60, 13, 11, 12, 60, 10, 11, 60, 59, 10, 39, 41, 42, 42, 58, 59, 42, 41, 58, 39, 0, 41, 59, 9, 10, 59, 58, 9, 0, 1, 41, 1, 40, 41, 58, 40, 57, 58, 41, 40, 57, 5, 58, 58, 8, 9, 8, 5, 6, 8, 58, 5, 1, 2, 40, 2, 3, 40, 40, 3, 57, 8, 6, 7, 57, 4, 5, 57, 3, 4], "vertices": [1, 10, 4.15, -76.86, 1, 1, 10, -31.71, -57.53, 1, 1, 10, -64.1, -25.09, 1, 1, 10, -67.16, 7.22, 1, 1, 10, -51.09, 37.02, 1, 1, 10, -27.37, 45.26, 1, 1, 10, -25.86, 63.67, 1, 1, 10, -11.69, 85.49, 1, 1, 10, 6.11, 71.68, 1, 1, 10, 37.85, 72.6, 1, 1, 10, 70.86, 91.46, 1, 1, 10, 100.26, 125.95, 1, 1, 10, 134.65, 164.55, 1, 1, 10, 168.67, 140.55, 1, 2, 10, 193.71, 107.25, 0.99445, 11, -43.73, 112.1, 0.00555, 2, 10, 211.43, 75.06, 0.91079, 11, -29.26, 78.32, 0.08921, 2, 10, 235.96, 90.12, 0.77049, 11, -3.37, 90.9, 0.22951, 2, 10, 257.24, 86.57, 0.7216, 11, 17.46, 85.27, 0.2784, 2, 10, 268.97, 69.5, 0.67534, 11, 27.45, 67.12, 0.32466, 2, 10, 259.48, 43.5, 0.42329, 11, 15.45, 42.18, 0.57671, 2, 10, 290.22, 49.38, 0.01813, 11, 46.61, 45.01, 0.98187, 1, 11, 90.56, 44.41, 1, 1, 11, 138.39, 40.65, 1, 1, 11, 187.85, 35.23, 1, 1, 11, 230.81, 30.75, 1, 1, 11, 269.21, 4.86, 1, 1, 11, 324.29, -32.26, 1, 1, 11, 253.59, -37, 1, 1, 11, 187.2, -42.08, 1, 1, 11, 124.57, -51.33, 1, 1, 11, 79.86, -53.5, 1, 2, 10, 280.26, -42.73, 0.05363, 11, 27.64, -45.68, 0.94637, 2, 10, 224.82, -42.24, 0.90569, 11, -27.48, -39.73, 0.09431, 2, 10, 203.07, -62.49, 0.99933, 11, -51.12, -57.74, 0.00067, 1, 10, 179.23, -78.01, 1, 1, 10, 160.42, -68.9, 1, 1, 10, 121.71, -72.78, 1, 1, 10, 95.01, -88.82, 1, 1, 10, 50.65, -97.42, 1, 1, 10, 23.75, -87.43, 1, 1, 10, -28.05, -10.49, 1, 1, 10, 15.59, -26.84, 1, 1, 10, 60.55, -28.44, 1, 1, 10, 109.47, -21.69, 1, 1, 10, 158.82, -7.99, 1, 1, 10, 198.65, 0.35, 1, 2, 10, 244.86, -3.03, 0.76968, 11, -3.68, -2.68, 0.23032, 1, 11, 38.1, -5.05, 1, 1, 11, 90.9, -8.92, 1, 1, 11, 141.39, -8.07, 1, 1, 11, 192.98, -6.6, 1, 1, 11, 241.55, -10.62, 1, 2, 10, 214.83, 37.44, 0.92689, 11, -29.58, 40.55, 0.07311, 2, 10, 244.69, 70.59, 0.73152, 11, 3.4, 70.6, 0.26848, 1, 10, 188.2, -38.15, 1, 2, 10, 187.61, 40.98, 0.98969, 11, -56.32, 46.75, 0.01031, 1, 10, 159.4, -47.89, 1, 1, 10, -24.54, 22.59, 1, 1, 10, 21.22, 24.05, 1, 1, 10, 71.62, 34.13, 1, 1, 10, 112.39, 59.69, 1, 2, 10, 149.84, 87.04, 0.99992, 11, -89.38, 96.31, 8e-05, 1, 10, 110.22, 16.75, 1, 2, 10, 159.83, 49.96, 0.9997, 11, -83.08, 58.42, 0.0003], "hull": 40, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 0, 78, 6, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 50, 90, 104, 104, 106, 90, 108, 88, 110, 88, 112, 8, 114, 114, 116, 116, 118, 118, 120, 120, 122, 124, 126], "width": 338, "height": 568}}, "sunshangxiang010": {"sunshangxiang010": {"type": "mesh", "uvs": [0, 0.07468, 0.12168, 0.14706, 0.25206, 0.25446, 0.35599, 0.46458, 0.43158, 0.66537, 0.49393, 0.90117, 0.56952, 1, 0.73392, 1, 0.91154, 0.99222, 1, 0.93386, 1, 0.76342, 0.94178, 0.55096, 0.87753, 0.3315, 0.80572, 0.13539, 0.69612, 0, 0.51094, 0, 0.25773, 0, 0.07633, 0, 0, 0, 0.47693, 0.20076, 0.59786, 0.42256, 0.65644, 0.63735, 0.71124, 0.85448, 0.6829, 0.18675, 0.77927, 0.39921, 0.84918, 0.60466, 0.90209, 0.81479], "triangles": [0, 18, 17, 1, 17, 16, 0, 17, 1, 23, 15, 14, 23, 14, 13, 19, 16, 15, 19, 15, 23, 2, 1, 16, 2, 16, 19, 24, 23, 13, 24, 13, 12, 20, 19, 23, 20, 23, 24, 3, 2, 19, 3, 19, 20, 24, 12, 11, 25, 24, 11, 21, 20, 24, 21, 24, 25, 4, 3, 20, 4, 20, 21, 25, 11, 10, 26, 25, 10, 22, 21, 25, 22, 25, 26, 5, 4, 21, 5, 21, 22, 26, 10, 9, 8, 26, 9, 7, 22, 26, 6, 5, 22, 8, 7, 26, 6, 22, 7], "vertices": [1, 41, -32.62, -62.97, 1, 2, 41, 7.43, -57.83, 0.99702, 42, -66.41, -98.08, 0.00298, 2, 41, 54.28, -58.37, 0.86878, 42, -24.3, -77.54, 0.13122, 3, 41, 108.05, -84.07, 0.26735, 42, 35.28, -76.36, 0.63372, 43, -34.59, -83.05, 0.09893, 3, 41, 153.48, -112.5, 0.02268, 42, 88.63, -81.37, 0.3581, 43, 18.87, -79.36, 0.61922, 2, 42, 147.31, -94.06, 0.03234, 43, 78.83, -82.4, 0.96766, 2, 42, 179.36, -86.62, 0.00316, 43, 109.25, -69.87, 0.99684, 1, 43, 126.86, -23.97, 1, 1, 43, 144.13, 26.29, 1, 1, 43, 140.42, 56.04, 1, 1, 43, 101.91, 70.82, 1, 2, 42, 141.7, 64.31, 0.03481, 43, 47.67, 72.98, 0.96519, 2, 42, 86.16, 74.52, 0.49296, 43, -8.79, 74.07, 0.50704, 3, 41, 177.37, 56, 0.02887, 42, 34.35, 79.93, 0.88833, 43, -60.8, 71.03, 0.0828, 3, 41, 132.04, 65.62, 0.22435, 42, -10.47, 68.17, 0.77397, 43, -103.12, 52.17, 0.00168, 2, 41, 85.62, 35.44, 0.89052, 42, -38.41, 20.36, 0.10948, 2, 41, 22.14, -5.82, 0.99995, 42, -76.61, -45, 5e-05, 1, 41, -23.34, -35.38, 1, 1, 41, -42.47, -47.82, 1, 2, 41, 103.57, -10.84, 0.70807, 42, -1.6, -12.93, 0.29193, 3, 41, 163.14, -36.13, 0.00424, 42, 62.99, -8.79, 0.98978, 43, -18.18, -11.89, 0.00598, 2, 42, 116.71, -19.9, 0.0196, 43, 36.63, -14.16, 0.9804, 1, 43, 91.55, -17.69, 1, 3, 41, 153.36, 25.57, 0.04769, 42, 26.55, 41.95, 0.92843, 43, -62.35, 32.29, 0.02388, 2, 42, 85.48, 40.89, 0.44428, 43, -4.02, 40.77, 0.55572, 2, 42, 138.95, 33.85, 0.01045, 43, 49.89, 42.48, 0.98955, 1, 43, 103.03, 39.03, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 32, 38, 38, 40, 40, 42, 42, 44, 30, 46, 46, 48, 48, 50, 50, 52], "width": 179, "height": 145}}}}], "animations": {"animation1": {"bones": {"bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone3": {"rotate": [{"angle": -0.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.2}], "translate": [{"x": 0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 2.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.47}]}, "bone4": {"rotate": [{"angle": -0.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.06, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.06, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.53}], "translate": [{"x": 0.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.55}]}, "bone5": {"rotate": [{"angle": -0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.06, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -1.06, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -0.36}], "translate": [{"x": 1.25, "y": -1.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 3.71, "y": -4.64, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.25, "y": -1.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 3.71, "y": -4.64, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 1.25, "y": -1.56}]}, "bone6": {"rotate": [{"angle": -0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.06, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -1.06, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -0.36}], "translate": [{"x": 1.72, "y": 1.77, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 5.14, "y": 5.29, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.72, "y": 1.77, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 5.14, "y": 5.29, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 1.72, "y": 1.77}]}, "bone7": {"rotate": [{"angle": -0.86, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -1.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.86, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -1.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.86}]}, "bone8": {"rotate": [{"angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 12.28, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 12.28, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -1.06}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone10": {"rotate": [{"angle": 2.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 3.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 2.5}]}, "bone11": {"rotate": [{"angle": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.23}]}, "bone12": {"rotate": [{"angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.46, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -4.46, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.82}]}, "bone13": {"rotate": [{"angle": -2.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.23}]}, "bone14": {"rotate": [{"angle": -3.63, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -4.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.63, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -4.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.63}]}, "bone15": {"rotate": [{"angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -4.46}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone17": {"rotate": [{"angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.99}]}, "bone18": {"rotate": [{"angle": -2.67, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.67, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -5.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.67}]}, "bone19": {"rotate": [{"angle": -2.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -5.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.69}]}, "bone20": {"rotate": [{"angle": -4.37, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.37, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.37, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.37, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -4.37}]}, "bone21": {"rotate": [{"angle": -4.38, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.37, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.38, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.37, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -4.38}]}, "bone22": {"rotate": [{"angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.6667, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -5.37}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.37, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.37, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone24": {"rotate": [{"angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.99}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone28": {"rotate": [{"angle": 1.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 2.46, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.6667, "angle": -1.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 2.46, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4.3333, "angle": -1.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 1.77}]}, "bone29": {"rotate": [{"angle": 0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 2.24, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2, "angle": -1.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 2.24, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 4.6667, "angle": -1.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.44}]}, "bone30": {"rotate": [{"angle": -0.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": 2.02, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.3333, "angle": -1.45, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": 2.02, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5, "angle": -1.45, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.81}]}, "bone31": {"rotate": [{"angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -1.36}]}, "bone32": {"rotate": [{"angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 2.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.02}]}, "bone33": {"rotate": [{"angle": 0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 2.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.44}]}, "bone34": {"rotate": [{"angle": 1.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 1.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 2.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 1.82}]}, "bone35": {"rotate": [{"angle": 2.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 2.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.58}]}, "bone36": {"rotate": [{"angle": 1.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 2.46, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.6667, "angle": -1.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 2.46, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4.3333, "angle": -1.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 1.77}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone38": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -11.14, "y": -8.36, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -11.14, "y": -8.36, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone41": {"rotate": [{"angle": 0.72, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.72, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.72}]}, "bone42": {"rotate": [{"angle": 6.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 13.11, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 6.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 13.11, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 6.56}]}, "bone43": {"rotate": [{"angle": 10.53, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 12.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 10.53, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 12.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 10.53}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone45": {"rotate": [{"angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.3}]}, "bone46": {"rotate": [{"angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.3}]}, "bone47": {"rotate": [{"angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.3}]}, "bone48": {"rotate": [{"angle": -1.28}]}, "bone49": {"rotate": [{"angle": -2.18, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.18, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.18}]}, "bone50": {"rotate": [{"angle": -0.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.2}]}, "bone51": {"rotate": [{"angle": -4.1, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -7.69, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": -4.1, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -7.69, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.3333, "angle": -4.1}]}, "bone52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": 4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": 4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bone53": {"rotate": [{"angle": -1.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -7.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.47}]}, "bone54": {"rotate": [{"angle": -3.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -7.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.99}]}, "bone55": {"rotate": [{"angle": -6.51, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -7.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -6.51, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -7.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -6.51}]}, "bone56": {"rotate": [{"angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -7.98}]}, "bone57": {"rotate": [{"angle": -6.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -6.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -6.51}]}, "bone58": {"rotate": [{"angle": -4.25, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.6333, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": -4.25, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.3, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "angle": -4.25}]}, "bone60": {"rotate": [{"time": 1.3333, "angle": -1.06}]}, "bone59": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.15, "y": 18.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.15, "y": 18.16, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}}, "deform": {"default": {"sunshangxiang017": {"sunshangxiang017": [{"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "offset": 272, "vertices": [8.41129, 0.64589, 8.40364, 0.74887, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.03738, -1.00942, -1.02481, -1.02207, -4.05763, 0.23921, -4.05917, 0.18928, -4.23648, 0.36166, -4.23955, 0.30957, 0, 0, 0, 0, 0, 0, 0, 0, 2.74287, -1.08512, 2.75624, -1.05145, 5.37384, -1.51204, 5.39247, -1.44613, 2.51883, -0.03217, 2.51962, -0.0014, -0.08047, -1.48702, -0.06209, -1.48792, -6.58444, 4.61788, -6.64008, 4.53675, -9.53127, 4.51878, -9.58533, 4.40151, -10.58192, 0.69395, -10.58911, 0.56407, -5.0388, -1.5429, -5.01897, -1.60464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.41446, 8.10371, -4.51259, 8.04885, -10.3093, 5.8354, -10.37872, 5.70843, -6.88545, 2.30629, -6.91273, 2.22164, -1.60854, 1.95443, -1.63217, 1.93454, 0, 0, 0, 0, 5.42818, -2.08973, 5.45464, -2.02315, 8.04507, -3.3512, 8.08592, -3.25236, 3.5341, 0.10913, 3.53264, 0.15243, -1.879, 1.15581, -1.89247, 1.13263, -2.39502, 0.91083, -2.40466, 0.88129, 0.48297, -0.92535, 0.49506, -0.91944, 1.00438, 0.91317, 0.9933, 0.9254], "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}}}}, "animation2": {"slots": {"sunshangxiang01": {"attachment": [{"name": null}]}}, "bones": {"bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone3": {"rotate": [{"angle": -0.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.2}], "translate": [{"x": 0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 2.57, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.47}]}, "bone4": {"rotate": [{"angle": -0.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.06, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.06, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.53}], "translate": [{"x": 0.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.55}]}, "bone5": {"rotate": [{"angle": -0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.06, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -1.06, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -0.36}], "translate": [{"x": 1.25, "y": -1.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 3.71, "y": -4.64, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.25, "y": -1.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 3.71, "y": -4.64, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 1.25, "y": -1.56}]}, "bone6": {"rotate": [{"angle": -0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.06, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -1.06, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -0.36}], "translate": [{"x": 1.72, "y": 1.77, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 5.14, "y": 5.29, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.72, "y": 1.77, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 5.14, "y": 5.29, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 1.72, "y": 1.77}]}, "bone7": {"rotate": [{"angle": -0.86, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -1.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.86, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -1.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.86}]}, "bone8": {"rotate": [{"angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 12.28, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 12.28, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -1.06}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone10": {"rotate": [{"angle": 2.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.5, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 3.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 2.5}]}, "bone11": {"rotate": [{"angle": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.23}]}, "bone12": {"rotate": [{"angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.46, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -4.46, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.82}]}, "bone13": {"rotate": [{"angle": -2.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.23}]}, "bone14": {"rotate": [{"angle": -3.63, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -4.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.63, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -4.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.63}]}, "bone15": {"rotate": [{"angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -4.46}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone17": {"rotate": [{"angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.99}]}, "bone18": {"rotate": [{"angle": -2.67, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.67, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -5.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.67}]}, "bone19": {"rotate": [{"angle": -2.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -5.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.69}]}, "bone20": {"rotate": [{"angle": -4.37, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.37, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.37, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.37, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -4.37}]}, "bone21": {"rotate": [{"angle": -4.38, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.37, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.38, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.37, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -4.38}]}, "bone22": {"rotate": [{"angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.6667, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -5.37}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.37, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.37, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone24": {"rotate": [{"angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.99}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -13.28, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone28": {"rotate": [{"angle": 1.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 2.46, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.6667, "angle": -1.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 2.46, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4.3333, "angle": -1.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 1.77}]}, "bone29": {"rotate": [{"angle": 0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 2.24, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2, "angle": -1.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 2.24, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 4.6667, "angle": -1.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.44}]}, "bone30": {"rotate": [{"angle": -0.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": 2.02, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.3333, "angle": -1.45, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": 2.02, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5, "angle": -1.45, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.81}]}, "bone31": {"rotate": [{"angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -1.36}]}, "bone32": {"rotate": [{"angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 2.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.02}]}, "bone33": {"rotate": [{"angle": 0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 2.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.44}]}, "bone34": {"rotate": [{"angle": 1.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 1.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 2.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 1.82}]}, "bone35": {"rotate": [{"angle": 2.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 2.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.58}]}, "bone36": {"rotate": [{"angle": 1.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 2.46, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.6667, "angle": -1.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 2.46, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4.3333, "angle": -1.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 1.77}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone38": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -11.14, "y": -8.36, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -11.14, "y": -8.36, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone41": {"rotate": [{"angle": 0.72, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.72, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.72}]}, "bone42": {"rotate": [{"angle": 6.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 13.11, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 6.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 13.11, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 6.56}]}, "bone43": {"rotate": [{"angle": 10.53, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 12.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 10.53, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 12.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 10.53}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone45": {"rotate": [{"angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.3}]}, "bone46": {"rotate": [{"angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.3}]}, "bone47": {"rotate": [{"angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.3}]}, "bone48": {"rotate": [{"angle": -1.28}]}, "bone49": {"rotate": [{"angle": -2.18, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.18, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.18}]}, "bone50": {"rotate": [{"angle": -0.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.2}]}, "bone51": {"rotate": [{"angle": -4.1, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -7.69, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": -4.1, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -7.69, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.3333, "angle": -4.1}]}, "bone52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": 4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": 4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bone53": {"rotate": [{"angle": -1.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -7.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.47}]}, "bone54": {"rotate": [{"angle": -3.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -7.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.99}]}, "bone55": {"rotate": [{"angle": -6.51, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -7.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -6.51, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -7.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -6.51}]}, "bone56": {"rotate": [{"angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -7.98}]}, "bone57": {"rotate": [{"angle": -6.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -6.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -6.51}]}, "bone58": {"rotate": [{"angle": -4.25, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.6333, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": -4.25, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.3, "angle": -7.98, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "angle": -4.25}]}, "bone60": {"rotate": [{"time": 1.3333, "angle": -1.06}]}, "bone59": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.15, "y": 18.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.15, "y": 18.16, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}}, "deform": {"default": {"sunshangxiang017": {"sunshangxiang017": [{"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "offset": 272, "vertices": [8.41129, 0.64589, 8.40364, 0.74887, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.03738, -1.00942, -1.02481, -1.02207, -4.05763, 0.23921, -4.05917, 0.18928, -4.23648, 0.36166, -4.23955, 0.30957, 0, 0, 0, 0, 0, 0, 0, 0, 2.74287, -1.08512, 2.75624, -1.05145, 5.37384, -1.51204, 5.39247, -1.44613, 2.51883, -0.03217, 2.51962, -0.0014, -0.08047, -1.48702, -0.06209, -1.48792, -6.58444, 4.61788, -6.64008, 4.53675, -9.53127, 4.51878, -9.58533, 4.40151, -10.58192, 0.69395, -10.58911, 0.56407, -5.0388, -1.5429, -5.01897, -1.60464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.41446, 8.10371, -4.51259, 8.04885, -10.3093, 5.8354, -10.37872, 5.70843, -6.88545, 2.30629, -6.91273, 2.22164, -1.60854, 1.95443, -1.63217, 1.93454, 0, 0, 0, 0, 5.42818, -2.08973, 5.45464, -2.02315, 8.04507, -3.3512, 8.08592, -3.25236, 3.5341, 0.10913, 3.53264, 0.15243, -1.879, 1.15581, -1.89247, 1.13263, -2.39502, 0.91083, -2.40466, 0.88129, 0.48297, -0.92535, 0.49506, -0.91944, 1.00438, 0.91317, 0.9933, 0.9254], "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}}}}}}