{"skeleton": {"hash": "aTtLLdW4zsnATrOfbuhp3Ko6z8Y=", "spine": "3.8.75", "images": "./xulieaz/", "audio": ""}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "scaleX": 0.9272, "scaleY": 0.9272}, {"name": "card", "parent": "all"}, {"name": "light", "parent": "all"}, {"name": "card2", "parent": "all"}, {"name": "bone", "parent": "root", "x": 0.4, "y": -1.46, "scaleX": 0.9285, "scaleY": 0.9285}], "slots": [{"name": "images/wj_bg_kapai_0", "bone": "card", "color": "ffae00ff", "blend": "additive"}, {"name": "images/wj_bg_kapai_1", "bone": "card2", "color": "ffae00ff", "blend": "additive"}, {"name": "images/light", "bone": "light", "color": "ffc300ff"}, {"name": "images/light2", "bone": "light", "color": "ffc300ff", "blend": "additive"}, {"name": "images/images1/export_01", "bone": "bone", "color": "ffcd009c", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"images/images1/export_01": {"images/images1/export_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_23": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_25": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_26": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_27": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}, "images/images1/export_29": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -118, -82, -118, -82, 118, 83, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 236}}, "images/wj_bg_kapai_1": {"images/wj_bg_kapai_0": {"x": 0.5, "width": 165, "height": 236}}, "images/light2": {"images/light": {"x": 0.5, "y": 0.5, "width": 189, "height": 259}}, "images/wj_bg_kapai_0": {"images/wj_bg_kapai_0": {"x": 0.5, "width": 165, "height": 236}}, "images/light": {"images/light": {"x": 0.5, "y": 0.5, "width": 189, "height": 259}}}}], "animations": {"animation": {"slots": {"images/wj_bg_kapai_1": {"color": [{"color": "ffad0000", "curve": "stepped"}, {"time": 0.2667, "color": "ffad0000", "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "color": "ffad0030", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffad0000"}], "attachment": [{"name": "images/wj_bg_kapai_0"}]}, "images/images1/export_01": {"attachment": [{"time": 0.0333, "name": "images/images1/export_04"}, {"time": 0.0667, "name": "images/images1/export_05"}, {"time": 0.1, "name": "images/images1/export_06"}, {"time": 0.1333, "name": "images/images1/export_07"}, {"time": 0.1667, "name": "images/images1/export_08"}, {"time": 0.2, "name": "images/images1/export_09"}, {"time": 0.2333, "name": "images/images1/export_10"}, {"time": 0.2667, "name": "images/images1/export_11"}, {"time": 0.3, "name": "images/images1/export_12"}, {"time": 0.3333, "name": "images/images1/export_13"}, {"time": 0.3667, "name": "images/images1/export_14"}, {"time": 0.4, "name": "images/images1/export_15"}, {"time": 0.4333, "name": "images/images1/export_16"}, {"time": 0.4667, "name": "images/images1/export_17"}, {"time": 0.5, "name": "images/images1/export_18"}, {"time": 0.5333, "name": "images/images1/export_19"}, {"time": 0.5667, "name": "images/images1/export_20"}, {"time": 0.6, "name": "images/images1/export_21"}, {"time": 0.6333, "name": "images/images1/export_22"}, {"time": 0.6667, "name": "images/images1/export_23"}, {"time": 0.7, "name": "images/images1/export_24"}, {"time": 0.7333, "name": "images/images1/export_25"}, {"time": 0.7667, "name": "images/images1/export_26"}, {"time": 0.8, "name": "images/images1/export_27"}, {"time": 0.8333, "name": "images/images1/export_28"}, {"time": 0.8667, "name": "images/images1/export_29"}, {"time": 0.9, "name": null}]}, "images/light": {"color": [{"color": "ffc30000", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffc30097", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffc30000"}], "attachment": [{"name": "images/light"}]}, "images/light2": {"color": [{"color": "ffc30000"}], "attachment": [{"name": "images/light"}]}, "images/wj_bg_kapai_0": {"color": [{"color": "ffad0000", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffae00ff", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffad0000"}], "attachment": [{"name": "images/wj_bg_kapai_0"}]}}, "bones": {"card": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.194, "y": 1.194}]}, "card2": {"scale": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.194, "y": 1.194}]}}}}}