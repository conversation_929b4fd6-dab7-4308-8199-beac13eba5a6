import { UIDhamaFund } from "../../game/ui/ui_fund/UIDhamaFund";
import { UIFriendFund } from "../../game/ui/ui_fund/UIFriendFund";
import { UIHeroFund } from "../../game/ui/ui_fund/UIHeroFund";
import { UILevelFund } from "../../game/ui/ui_fund/UILevelFund";
import { UIRoleFund } from "../../game/ui/ui_fund/UIRoleFund";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
export enum FundRouteItem {
  UIFriendFund = "UIFriendFund",
  UIRoleFund = "UIRoleFund",
  UIDhamaFund = "UIDhamaFund",
  UIHeroFund = "UIHeroFund",
  UILevelFund = "UILevelFund",
}
export class FundRoute {
  rotueTables: Recording[] = [
    {
      node: UIFriendFund,
      uiName: FundRouteItem.UIFriendFund,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIRoleFund,
      uiName: FundRouteItem.UIRoleFund,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIDhamaFund,
      uiName: FundRouteItem.UIDhamaFund,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIHeroFund,
      uiName: FundRouteItem.UIHeroFund,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UILevelFund,
      uiName: FundRouteItem.UILevelFund,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
