{"skeleton": {"hash": "qbYJWFv/jYMKQVPrTcpEjLlUqfs=", "spine": "3.8.75", "x": -667.95, "y": -325.13, "width": 943.81, "height": 1417.31}, "bones": [{"name": "root"}, {"name": "<PERSON>an<PERSON>", "parent": "root", "x": 37.55, "y": -53.62}, {"name": "bone4", "parent": "<PERSON>an<PERSON>", "x": -38.56, "y": 59.53, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "bone", "parent": "bone4", "length": 10.38, "rotation": 2.58, "x": 0.27, "y": 2.97}, {"name": "bone2", "parent": "bone", "length": 9.15, "rotation": 21.58, "x": 8.39, "y": 9.54}, {"name": "bone3", "parent": "bone", "length": 7.92, "rotation": 152.37, "x": -8.02, "y": 9.18}, {"name": "bone5", "parent": "<PERSON>an<PERSON>", "x": -38.56, "y": 59.53, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "bone6", "parent": "bone5", "length": 10.38, "rotation": 2.58, "x": 0.27, "y": 2.97}, {"name": "bone7", "parent": "bone6", "length": 9.15, "rotation": 21.58, "x": 8.39, "y": 9.54}, {"name": "bone8", "parent": "bone6", "length": 7.92, "rotation": 152.37, "x": -8.02, "y": 9.18}, {"name": "xiezi", "parent": "root", "x": -170.99, "y": 55.57, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "bone9", "parent": "xiezi", "length": 12.39, "rotation": -0.27, "x": 2.45, "y": 5.52, "scaleX": 0.6, "scaleY": 0.6}, {"name": "bone10", "parent": "bone9", "length": 3.34, "rotation": -91.78, "x": -10.29, "y": -2.13}, {"name": "bone11", "parent": "bone9", "length": 3.5, "rotation": -102.26, "x": -13.24, "y": 0.04}, {"name": "bone12", "parent": "bone9", "length": 6.27, "rotation": 79.39, "x": -11.35, "y": 7.41}, {"name": "bone13", "parent": "bone12", "length": 5.96, "rotation": -47.11, "x": 6.27}, {"name": "bone14", "parent": "bone13", "length": 4.88, "rotation": -92.95, "x": 5.96}, {"name": "xiezi2", "parent": "root", "x": -170.99, "y": 55.57, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "bone15", "parent": "xiezi2", "length": 12.39, "rotation": -0.27, "x": 2.45, "y": 5.52, "scaleX": 0.6, "scaleY": 0.6}, {"name": "bone16", "parent": "bone15", "length": 3.34, "rotation": -91.78, "x": -10.29, "y": -2.13}, {"name": "bone17", "parent": "bone15", "length": 3.5, "rotation": -102.26, "x": -13.24, "y": 0.04}, {"name": "bone18", "parent": "bone15", "length": 6.27, "rotation": 79.39, "x": -11.35, "y": 7.41}, {"name": "bone19", "parent": "bone18", "length": 5.96, "rotation": -47.11, "x": 6.27}, {"name": "bone20", "parent": "bone19", "length": 4.88, "rotation": -92.95, "x": 5.96}, {"name": "pangxie", "parent": "root", "x": 60.86, "y": -321.44, "scaleX": 1.1178, "scaleY": 1.1178}, {"name": "bone21", "parent": "pangxie", "length": 14.72, "rotation": 0.26, "x": -0.31, "y": 9.44}, {"name": "bone22", "parent": "bone21", "length": 3.63, "rotation": -89.53, "x": -7.54, "y": -2.64}, {"name": "bone23", "parent": "bone21", "length": 2.21, "rotation": -90.26, "x": -3, "y": -4.69}, {"name": "bone24", "parent": "bone21", "length": 3.87, "rotation": -94.34, "x": 6.17, "y": -2.8}, {"name": "bone25", "parent": "bone21", "length": 4.1, "rotation": -94.11, "x": 9.71, "y": -2.54}, {"name": "bone26", "parent": "bone21", "length": 5.62, "rotation": 99.43, "x": -9.41, "y": 4.44}, {"name": "bone27", "parent": "bone21", "length": 5.73, "rotation": 85.69, "x": 9, "y": 5.26}, {"name": "bone28", "parent": "root", "length": 14.14, "rotation": 1.23, "x": -16.92, "y": -184.85, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "bone29", "parent": "bone28", "length": 2.77, "rotation": 92.5, "x": -1.73, "y": -1.88}, {"name": "bone30", "parent": "bone29", "length": 3.38, "rotation": -12.58, "x": 2.77}, {"name": "bone31", "parent": "bone30", "length": 4.66, "rotation": -27.77, "x": 3.38, "y": 0.02}, {"name": "bone32", "parent": "root", "length": 14.14, "rotation": 1.23, "x": -16.92, "y": -184.85, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "bone33", "parent": "bone32", "length": 2.77, "rotation": 92.5, "x": -1.73, "y": -1.88}, {"name": "bone34", "parent": "bone33", "length": 3.38, "rotation": -12.58, "x": 2.77}, {"name": "bone35", "parent": "bone34", "length": 4.66, "rotation": -27.77, "x": 3.38, "y": 0.02}, {"name": "bone40", "parent": "root", "x": -96.14, "y": -189.08, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "bone36", "parent": "bone40", "length": 10.53, "rotation": 1.03, "x": 0.18, "y": 5.76, "scaleX": 0.523, "scaleY": 0.523}, {"name": "bone37", "parent": "bone36", "length": 4.11, "rotation": -108.8, "x": -5.47, "y": -1.84}, {"name": "bone38", "parent": "bone36", "length": 3.96, "rotation": -99.32, "x": 2.07, "y": -5.17}, {"name": "bone39", "parent": "bone36", "length": 4.81, "rotation": -86.04, "x": 5.74, "y": -3.9}, {"name": "bone41", "parent": "root", "x": 10.83, "y": -25.22, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "bone42", "parent": "bone41", "length": 10.53, "rotation": 1.03, "x": 0.18, "y": 5.76, "scaleX": 0.523, "scaleY": 0.523}, {"name": "bone43", "parent": "bone42", "length": 4.11, "rotation": -108.8, "x": -5.47, "y": -1.84}, {"name": "bone44", "parent": "bone42", "length": 3.96, "rotation": -99.32, "x": 2.07, "y": -5.17}, {"name": "bone45", "parent": "bone42", "length": 4.81, "rotation": -86.04, "x": 5.74, "y": -3.9}, {"name": "bone46", "parent": "root", "x": 52.77, "y": 15.57, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "bone49", "parent": "bone46", "x": 0.16, "y": 11.04}, {"name": "bone47", "parent": "bone49", "length": 8.16, "rotation": -11.31, "x": 7.04, "y": 0.96}, {"name": "bone48", "parent": "bone49", "length": 6.97, "rotation": -170.75, "x": -7.04, "y": 0.64}, {"name": "bone50", "parent": "root", "x": 99.2, "y": 36.41, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "zhu", "parent": "bone50", "length": 7.48, "rotation": 2.02, "x": 3.28, "y": 5.8}, {"name": "bone51", "parent": "zhu", "x": -5.66, "y": 4.34}, {"name": "bone52", "parent": "root", "x": 99.2, "y": 36.41, "scaleX": 1.5968, "scaleY": 1.5968}, {"name": "zhu2", "parent": "bone52", "length": 7.48, "rotation": 2.02, "x": 3.28, "y": 5.8}, {"name": "bone53", "parent": "zhu2", "x": -5.66, "y": 4.34}, {"name": "bone54", "parent": "root", "x": -166.22, "y": -116.37, "scaleX": 0.8153, "scaleY": 0.8153}, {"name": "bone55", "parent": "bone54", "length": 21.23, "rotation": -177.8, "y": 24.07}, {"name": "bone56", "parent": "bone55", "length": 11.2, "rotation": -81.71, "x": -5.49, "y": -4.69}, {"name": "bone57", "parent": "bone55", "length": 6.07, "rotation": 17.45, "x": -0.83, "y": 10.24}, {"name": "bone58", "parent": "bone57", "length": 8.13, "rotation": -2.13, "x": 6.07}, {"name": "bone59", "parent": "bone56", "length": 8.65, "rotation": 121.56, "x": 7.81, "y": 9.22}, {"name": "bone60", "parent": "root", "x": -130.01, "y": -120.82, "scaleX": 1.2229, "scaleY": 1.2229}, {"name": "bone61", "parent": "bone60", "length": 10.58, "rotation": 177.55, "x": -0.3, "y": 12.99}, {"name": "bone62", "parent": "bone61", "length": 3.78, "rotation": 76.22, "x": -1.22, "y": 2.46}, {"name": "bone63", "parent": "bone62", "length": 4.75, "rotation": -85.51, "x": 4.73, "y": -3.41}, {"name": "bone64", "parent": "bone61", "length": 8.17, "rotation": -60.4, "x": -1.89, "y": -2.4}, {"name": "bone65", "parent": "bone64", "length": 4.14, "rotation": 21.01, "x": 10.02, "y": 0.6}, {"name": "bone66", "parent": "bone65", "length": 4.29, "rotation": -12.76, "x": 4.76}, {"name": "bone67", "parent": "root", "x": -68.59, "y": -126.97, "scaleX": 1.3525, "scaleY": 1.3525}, {"name": "bone68", "parent": "bone67", "length": 6.91, "rotation": 175.87, "x": -2.41, "y": 7.97}, {"name": "bone69", "parent": "bone68", "length": 2.84, "rotation": 94.94, "x": -2.79, "y": 0.32}, {"name": "bone70", "parent": "bone69", "length": 4.52, "rotation": -92.07, "x": 3.12, "y": -3.08}, {"name": "bone71", "parent": "bone68", "length": 6.85, "rotation": -82.18, "x": -2.42, "y": -2.7}, {"name": "bone72", "parent": "root", "x": -72.92, "y": -164.44, "scaleX": 1.3525, "scaleY": 1.3525}, {"name": "bone73", "parent": "bone72", "length": 6.26, "rotation": 179.34, "x": -2.28, "y": 8.84}, {"name": "bone74", "parent": "bone73", "length": 3.71, "rotation": 90.66, "x": -3.7, "y": -0.62}, {"name": "bone75", "parent": "bone74", "length": 5.13, "rotation": -85.57, "x": 4.57, "y": -3.35}, {"name": "bone76", "parent": "bone73", "length": 5.14, "rotation": -83.31, "x": -3.12, "y": -4}, {"name": "flyline", "parent": "root", "x": 259.58, "y": 517.17}, {"name": "bone77", "parent": "root", "rotation": -140.98, "x": 439.45, "y": 471.42, "scaleX": 1.2787, "scaleY": 1.2787, "transform": "noRotationOrReflection"}, {"name": "bone78", "parent": "bone77", "rotation": 7.06, "x": -1.13, "y": 1.63, "transform": "noRotationOrReflection"}, {"name": "flyline1", "parent": "root", "x": -608.56, "y": 555.02}, {"name": "bone79", "parent": "root", "rotation": 0.05, "x": -712.8, "y": 769.67, "scaleX": 1.2787, "scaleY": 1.2787, "transform": "noRotationOrReflection"}, {"name": "bone80", "parent": "bone79", "x": 4.44, "y": 1.85, "transform": "noRotationOrReflection"}], "slots": [{"name": "sd", "bone": "bone4", "attachment": "sd"}, {"name": "sd2", "bone": "bone5", "attachment": "sd"}, {"name": "sd3", "bone": "xiezi", "attachment": "sd"}, {"name": "sd4", "bone": "xiezi2", "attachment": "sd"}, {"name": "sd5", "bone": "pangxie", "attachment": "sd"}, {"name": "sd6", "bone": "bone28", "attachment": "sd"}, {"name": "sd7", "bone": "bone32", "attachment": "sd"}, {"name": "sd8", "bone": "bone36", "attachment": "sd"}, {"name": "sd9", "bone": "bone42", "attachment": "sd"}, {"name": "sd10", "bone": "bone46", "attachment": "sd"}, {"name": "sd11", "bone": "zhu", "attachment": "sd"}, {"name": "sd12", "bone": "zhu2", "attachment": "sd"}, {"name": "bf", "bone": "bone", "attachment": "bf"}, {"name": "bf2", "bone": "bone6", "attachment": "bf"}, {"name": "xz", "bone": "bone9", "attachment": "xz"}, {"name": "xz2", "bone": "bone15", "attachment": "xz"}, {"name": "px", "bone": "bone21", "attachment": "px"}, {"name": "lg", "bone": "bone28", "attachment": "lg"}, {"name": "lg2", "bone": "bone32", "attachment": "lg"}, {"name": "zz", "bone": "bone36", "attachment": "zz"}, {"name": "zz2", "bone": "bone42", "attachment": "zz"}, {"name": "subf", "bone": "bone49", "attachment": "subf"}, {"name": "yz", "bone": "zhu", "attachment": "yz"}, {"name": "yz2", "bone": "zhu2", "attachment": "yz"}, {"name": "kapaixg1", "bone": "bone55", "attachment": "kapaixg1"}, {"name": "kapaixg2", "bone": "bone61", "attachment": "kapaixg2"}, {"name": "kapaixg3", "bone": "root"}, {"name": "kapaixg4", "bone": "root"}, {"name": "flyline", "bone": "flyline", "attachment": "flyline"}, {"name": "xian1", "bone": "bone78", "attachment": "xian1"}, {"name": "flyline1", "bone": "flyline1", "attachment": "flyline1"}, {"name": "xian2", "bone": "bone80", "attachment": "xian2"}], "path": [{"name": "flyline", "bones": ["bone77"], "target": "flyline", "rotation": 168.9, "position": 0.0513}, {"name": "flyline1", "order": 1, "bones": ["bone79"], "target": "flyline1"}], "skins": [{"name": "default", "attachments": {"zz": {"zz": {"type": "mesh", "uvs": [0, 0.83233, 0, 1, 0.40284, 1, 0.46319, 0.82473, 0.53472, 0.8298, 0.55037, 1, 0.74707, 1, 0.7873, 0.81713, 0.83872, 0.9742, 0.98624, 0.97927, 1, 0.75887, 1, 0.45487, 0.91326, 0.40155, 0.87225, 0.37634, 0.68225, 0, 0.2866, 0, 0.12343, 0.21421, 0.12119, 0.35101, 0, 0.44727, 0.40037, 0.35806, 0.49648, 0.57086, 0.57695, 0.58099, 0.77589, 0.54806], "triangles": [3, 1, 20, 17, 20, 0, 19, 15, 14, 4, 21, 7, 21, 22, 7, 22, 13, 12, 19, 14, 13, 13, 22, 21, 22, 12, 11, 13, 21, 19, 4, 20, 21, 21, 20, 19, 3, 20, 4, 17, 19, 20, 17, 16, 19, 16, 15, 19, 9, 8, 10, 8, 7, 10, 10, 22, 11, 7, 6, 4, 6, 5, 4, 7, 22, 10, 2, 1, 3, 0, 20, 1, 0, 18, 17], "vertices": [1, 42, 6.15, -2.34, 1, 1, 42, 8.54, -1.58, 1, 1, 42, 6.45, 4.94, 1, 2, 42, 3.64, 5.12, 0.272, 41, -1.79, -6.93, 0.728, 2, 43, 2.27, -2.32, 0.336, 41, -0.58, -7.03, 0.664, 2, 42, 5.69, 7.33, 0.00398, 43, 4.75, -1.68, 0.99602, 2, 43, 4.27, 1.62, 0.99874, 44, 5.54, -3.15, 0.00126, 1, 43, 1.46, 1.91, 1, 1, 44, 5.29, -1.56, 1, 1, 44, 5.58, 0.93, 1, 1, 44, 2.31, 1.45, 1, 1, 44, -2.23, 1.85, 1, 1, 41, 5.97, -0.72, 1, 1, 41, 5.28, -0.33, 1, 1, 41, 2.15, 5.37, 1, 1, 41, -4.57, 5.49, 1, 1, 41, -7.4, 2.33, 1, 1, 41, -7.48, 0.28, 1, 1, 42, 0.65, -4.11, 1, 1, 41, -2.73, 0.09, 1, 1, 41, -1.16, -3.13, 1, 1, 41, 0.21, -3.31, 1, 1, 41, 3.6, -2.88, 1], "hull": 19}}, "sd2": {"sd": {"x": 0.09, "y": -30.12, "width": 22, "height": 12}}, "sd3": {"sd": {"y": 2.54, "scaleX": 1.0193, "width": 22, "height": 12}}, "xian2": {"xian2": {"x": -12.08, "y": 8.08, "scaleX": -0.6, "scaleY": 0.6, "width": 60, "height": 51}}, "sd5": {"sd": {"y": 2.5, "scaleX": 1.2413, "scaleY": 0.9658, "width": 22, "height": 12}}, "sd6": {"sd": {"x": -0.1, "y": -4.67, "scaleX": 0.9107, "scaleY": 0.8398, "width": 22, "height": 12}}, "sd7": {"sd": {"x": -0.1, "y": -4.67, "scaleX": 0.9107, "scaleY": 0.8398, "width": 22, "height": 12}}, "sd8": {"sd": {"x": -1.11, "y": -8.51, "width": 22, "height": 12}}, "sd9": {"sd": {"x": -1.11, "y": -8.51, "width": 22, "height": 12}}, "sd": {"sd": {"x": 0.09, "y": -30.12, "width": 22, "height": 12}}, "zz2": {"zz": {"type": "mesh", "uvs": [0, 0.83233, 0, 1, 0.40284, 1, 0.46319, 0.82473, 0.53472, 0.8298, 0.55037, 1, 0.74707, 1, 0.7873, 0.81713, 0.83872, 0.9742, 0.98624, 0.97927, 1, 0.75887, 1, 0.45487, 0.91326, 0.40155, 0.87225, 0.37634, 0.68225, 0, 0.2866, 0, 0.12343, 0.21421, 0.12119, 0.35101, 0, 0.44727, 0.40037, 0.35806, 0.49648, 0.57086, 0.57695, 0.58099, 0.77589, 0.54806], "triangles": [3, 1, 20, 17, 20, 0, 19, 15, 14, 4, 21, 7, 21, 22, 7, 22, 13, 12, 19, 14, 13, 13, 22, 21, 22, 12, 11, 13, 21, 19, 4, 20, 21, 21, 20, 19, 3, 20, 4, 17, 19, 20, 17, 16, 19, 16, 15, 19, 9, 8, 10, 8, 7, 10, 10, 22, 11, 7, 6, 4, 6, 5, 4, 7, 22, 10, 2, 1, 3, 0, 20, 1, 0, 18, 17], "vertices": [1, 47, 6.15, -2.34, 1, 1, 47, 8.54, -1.58, 1, 1, 47, 6.45, 4.94, 1, 2, 47, 3.64, 5.12, 0.272, 46, -1.79, -6.93, 0.728, 2, 48, 2.27, -2.32, 0.336, 46, -0.58, -7.03, 0.664, 2, 47, 5.69, 7.33, 0.00398, 48, 4.75, -1.68, 0.99602, 2, 48, 4.27, 1.62, 0.99874, 49, 5.54, -3.15, 0.00126, 1, 48, 1.46, 1.91, 1, 1, 49, 5.29, -1.56, 1, 1, 49, 5.58, 0.93, 1, 1, 49, 2.31, 1.45, 1, 1, 49, -2.23, 1.85, 1, 1, 46, 5.97, -0.72, 1, 1, 46, 5.28, -0.33, 1, 1, 46, 2.15, 5.37, 1, 1, 46, -4.57, 5.49, 1, 1, 46, -7.4, 2.33, 1, 1, 46, -7.48, 0.28, 1, 1, 47, 0.65, -4.11, 1, 1, 46, -2.73, 0.09, 1, 1, 46, -1.16, -3.13, 1, 1, 46, 0.21, -3.31, 1, 1, 46, 3.6, -2.88, 1], "hull": 19}}, "xian1": {"xian1": {"x": 8.02, "y": 5.49, "scaleX": 0.6, "scaleY": 0.6, "width": 63, "height": 59}}, "px": {"px": {"type": "mesh", "uvs": [0.30353, 0.07082, 0.31389, 0.21582, 0.35739, 0.36345, 0.45267, 0.28173, 0.60596, 0.35027, 0.67224, 0.18682, 0.68053, 0, 0.8131, 0, 1, 0.171, 1, 0.40036, 1, 0.56909, 0.91874, 0.71145, 0.95189, 0.80899, 0.85867, 1, 0.77374, 1, 0.76546, 0.94081, 0.73853, 1, 0.60803, 1, 0.62667, 0.86172, 0.53967, 0.90127, 0.46925, 0.88281, 0.44025, 1, 0.3901, 1, 0.33668, 1, 0.31389, 0.91972, 0.28696, 1, 0.22725, 1, 0.17925, 1, 0.11711, 0.78527, 0.14196, 0.722, 0.06325, 0.68772, 0, 0.58227, 0, 0.22373, 0.20203, 0.02864, 0.23639, 0.49553, 0.13489, 0.34263, 0.7936, 0.44808, 0.8371, 0.25563, 0.60996, 0.74386, 0.77146, 0.67995, 0.77417, 0.83541, 0.31953, 0.85613, 0.24489, 0.71277, 0.3904, 0.86876], "triangles": [42, 28, 29, 29, 30, 34, 10, 11, 36, 42, 29, 34, 24, 41, 43, 38, 19, 20, 18, 19, 38, 18, 38, 40, 38, 39, 40, 40, 39, 11, 11, 39, 36, 4, 2, 3, 38, 43, 42, 38, 4, 39, 38, 2, 4, 42, 2, 38, 20, 43, 38, 43, 41, 42, 42, 34, 2, 39, 4, 36, 20, 21, 43, 26, 42, 41, 34, 35, 2, 4, 5, 36, 13, 15, 40, 13, 40, 12, 13, 14, 15, 40, 11, 12, 15, 18, 40, 15, 16, 18, 16, 17, 18, 21, 22, 43, 43, 22, 24, 22, 23, 24, 25, 26, 24, 24, 26, 41, 28, 26, 27, 28, 42, 26, 30, 31, 34, 31, 35, 34, 31, 32, 35, 35, 1, 2, 1, 33, 0, 1, 35, 33, 35, 32, 33, 36, 9, 10, 36, 37, 9, 36, 5, 37, 37, 8, 9, 37, 5, 7, 5, 6, 7, 37, 7, 8], "vertices": [3, 31, 7.07, 14.76, 0.00173, 30, 8.16, -5.64, 0.99707, 28, -15.3, -12.55, 0.00121, 4, 31, 3.91, 14.24, 0.01751, 30, 4.97, -5.39, 0.96744, 26, -12.84, 2.73, 0.00028, 28, -12.14, -12.03, 0.01476, 1, 25, -3.71, 6.97, 1, 2, 27, -13.45, 1.9, 0.016, 25, -1.03, 8.76, 0.984, 1, 25, 3.25, 7.23, 1, 3, 31, 5.25, 4.28, 0.98185, 30, 3.91, -15.39, 0.01015, 27, -15.54, 8.05, 0.00801, 2, 31, 9.37, 4.34, 1, 30, 7.92, -16.31, 0, 1, 31, 9.63, 0.64, 1, 2, 31, 6.25, -4.85, 0.99983, 29, -13.95, 3.6, 0.00017, 1, 31, 1.21, -5.21, 1, 1, 31, -2.49, -5.47, 1, 1, 25, 11.97, -0.76, 1, 2, 31, -7.85, -4.5, 0.01834, 29, 0.14, 3.2, 0.98166, 1, 29, 4.51, 0.88, 1, 2, 28, 4.15, 2.04, 0.10943, 29, 4.67, -1.49, 0.89057, 2, 28, 2.87, 1.71, 0.49868, 29, 3.39, -1.81, 0.50132, 2, 28, 4.22, 1.06, 0.93838, 29, 4.74, -2.48, 0.06162, 2, 27, 2.35, 6.25, 0.00109, 28, 4.48, -2.59, 0.99891, 1, 25, 3.78, -4.03, 1, 1, 25, 1.34, -4.89, 1, 1, 25, -0.63, -4.47, 1, 3, 26, 4.45, 6.05, 0.00018, 27, 2.35, 1.55, 0.99408, 28, 4.81, -7.28, 0.00574, 2, 27, 2.35, 0.15, 0.9999, 28, 4.91, -8.68, 0.0001, 3, 26, 4.42, 3.15, 0.03845, 27, 2.35, -1.35, 0.96155, 28, 5.02, -10.17, 1e-05, 2, 26, 2.64, 2.54, 0.44668, 27, 0.58, -1.98, 0.55332, 2, 26, 4.4, 1.76, 0.90528, 27, 2.35, -2.74, 0.09472, 2, 26, 4.38, 0.09, 0.99589, 27, 2.35, -4.41, 0.00411, 1, 26, 4.36, -1.26, 1, 1, 26, -0.39, -2.94, 1, 1, 25, -9.78, -0.89, 1, 1, 30, -4.08, 3.28, 1, 1, 30, -1.5, 4.63, 1, 1, 30, 6.28, 3.31, 1, 2, 31, 7.79, 17.66, 2e-05, 30, 9.55, -2.99, 0.99998, 1, 25, -7.11, 4.08, 1, 1, 30, 3.06, 0.02, 1, 1, 25, 8.5, 5.05, 1, 2, 31, 4.07, -0.43, 0.99999, 29, -11.79, -0.82, 1e-05, 1, 25, 3.33, -1.43, 1, 1, 25, 7.85, -0.05, 1, 2, 28, 0.54, 1.79, 0.47747, 29, 1.06, -1.72, 0.52253, 1, 25, -4.82, -3.87, 1, 1, 25, -6.89, -0.7, 1, 1, 25, -2.83, -4.15, 1], "hull": 34}}, "lg2": {"lg": {"type": "mesh", "uvs": [0, 0.5718, 0.06498, 0.4918, 0.11623, 0.45291, 0.06748, 0.31624, 0.17282, 0, 0.57032, 0, 0.93032, 0.0179, 0.95282, 0.25457, 1, 0.51124, 1, 0.79124, 0.76796, 0.70114, 0.61921, 0.63003, 0.68421, 0.72225, 0.71796, 0.83558, 0.64796, 0.95336, 0.47171, 0.98892, 0.27921, 1, 0.01921, 1, 0, 0.90003, 0, 0.70781, 0.28421, 0.82447, 0.32421, 0.63114, 0.37046, 0.53225, 0.50171, 0.29892, 0.73546, 0.18892, 0.73796, 0.47336], "triangles": [24, 5, 6, 24, 6, 7, 23, 4, 5, 23, 5, 24, 3, 4, 23, 2, 3, 23, 25, 24, 7, 23, 24, 25, 25, 7, 8, 22, 23, 25, 10, 25, 8, 11, 25, 10, 10, 8, 9, 22, 2, 23, 11, 22, 25, 21, 2, 22, 21, 22, 11, 21, 19, 0, 21, 1, 2, 1, 21, 0, 21, 11, 20, 20, 19, 21, 18, 19, 20, 14, 12, 13, 20, 11, 12, 14, 15, 12, 15, 20, 12, 16, 17, 18, 20, 16, 18, 16, 20, 15], "vertices": [3, 37, 5.36, 5.16, 0.27113, 38, 1.4, 5.6, 0.6688, 39, -4.35, 4.02, 0.06007, 3, 37, 6.73, 4.03, 0.12522, 38, 2.99, 4.8, 0.67039, 39, -2.57, 4.04, 0.20438, 3, 37, 7.37, 3.17, 0.04354, 38, 3.81, 4.09, 0.51992, 39, -1.52, 3.8, 0.43654, 3, 37, 9.88, 3.78, 8e-05, 38, 6.12, 5.24, 0.16018, 39, -0.01, 5.9, 0.83974, 2, 38, 12, 4.45, 6e-05, 39, 5.56, 7.94, 0.99994, 1, 39, 9.36, 2.84, 1, 2, 38, 13.55, -7.57, 0.00054, 39, 12.54, -1.98, 0.99946, 2, 38, 9.39, -8.58, 0.03858, 39, 9.33, -4.81, 0.96142, 2, 38, 4.94, -10.04, 0.15937, 39, 6.07, -8.17, 0.84063, 2, 38, -0.04, -10.82, 0.21734, 39, 2.03, -11.18, 0.78266, 3, 37, 2.23, -6.95, 0.02743, 38, 0.99, -6.9, 0.23784, 39, 1.12, -7.23, 0.73473, 3, 37, 3.67, -4.66, 0.25337, 38, 1.89, -4.35, 0.3502, 39, 0.72, -4.56, 0.39643, 3, 37, 1.94, -5.59, 0.68631, 38, 0.41, -5.63, 0.24122, 39, 0.01, -6.38, 0.07247, 3, 37, -0.13, -5.99, 0.85848, 38, -1.52, -6.48, 0.13094, 39, -1.3, -8.03, 0.01058, 2, 37, -2.17, -4.74, 0.94162, 38, -3.79, -5.7, 0.05838, 2, 37, -2.63, -1.88, 0.99345, 38, -4.85, -3.01, 0.00655, 1, 37, -2.63, 1.2, 1, 2, 37, -2.35, 5.36, 0.98882, 38, -6.16, 4.11, 0.01118, 2, 37, -0.54, 5.54, 0.94226, 38, -4.43, 4.69, 0.05774, 3, 37, 2.91, 5.32, 0.57934, 38, -1.01, 5.22, 0.41788, 39, -6.31, 2.56, 0.00278, 1, 37, 0.52, 0.92, 1, 1, 38, 1.15, 0.31, 1, 3, 37, 5.68, -0.8, 0.00106, 38, 3.02, -0.15, 0.99617, 39, -0.24, -0.31, 0.00277, 1, 39, 4.38, 0.51, 1, 2, 38, 10.02, -4.97, 0.00775, 39, 8.2, -1.31, 0.99225, 3, 37, 6.36, -6.74, 0.01531, 38, 4.97, -5.79, 0.15386, 39, 4.12, -4.4, 0.83083], "hull": 20}}, "yz2": {"yz": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0.5, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0, 1, 0.5, 0.66667, 0.5, 0.33333, 0.5], "triangles": [2, 11, 10, 2, 3, 11, 3, 4, 11, 11, 4, 6, 4, 5, 6, 0, 1, 9, 1, 10, 9, 9, 10, 8, 10, 7, 8, 1, 2, 10, 10, 11, 7, 11, 6, 7], "vertices": [2, 59, -6.35, -8.72, 0.80274, 58, -12.01, -4.38, 0.19726, 2, 59, -1.02, -8.91, 0.56159, 58, -6.68, -4.57, 0.43841, 2, 59, 4.31, -9.1, 0.08676, 58, -1.35, -4.76, 0.91324, 1, 58, 3.98, -4.95, 1, 2, 59, 9.87, -2.79, 0.02921, 58, 4.21, 1.55, 0.97079, 2, 59, 10.1, 3.71, 0.24988, 58, 4.44, 8.05, 0.75012, 2, 59, 4.77, 3.9, 0.57365, 58, -0.89, 8.23, 0.42635, 2, 59, -0.56, 4.08, 0.98853, 58, -6.22, 8.42, 0.01147, 1, 59, -5.89, 4.27, 1, 2, 59, -6.12, -2.22, 0.95597, 58, -11.78, 2.11, 0.04403, 2, 59, -0.79, -2.41, 0.8533, 58, -6.45, 1.93, 0.1467, 2, 59, 4.54, -2.6, 0.21879, 58, -1.12, 1.74, 0.78121], "hull": 10}}, "kapaixg1": {"kapaixg1": {"type": "mesh", "uvs": [0.34695, 0.51086, 0.24895, 0.49536, 0.16695, 0.55736, 0.09695, 0.54358, 0, 0.4523, 0, 0.3748, 0.08495, 0.39719, 0.09295, 0.30936, 0.06295, 0.23703, 0.12695, 0.15091, 0.33095, 0.02864, 0.60895, 0, 0.83695, 0, 1, 0.06825, 1, 0.28869, 1, 0.43164, 1, 0.54875, 1, 0.71063, 0.85695, 0.82602, 0.76295, 0.91902, 0.70295, 1, 0.46095, 1, 0.21895, 1, 0.14695, 0.92936, 0.13495, 0.83808, 0.25495, 0.7623, 0.28095, 0.68308, 0.33495, 0.61936, 0.35495, 0.56425, 0.11295, 0.4678, 0.22495, 0.37136, 0.45695, 0.22497, 0.67095, 0.28525, 0.71895, 0.45747, 0.68695, 0.56425, 0.63095, 0.78469, 0.51695, 0.87597, 0.37495, 0.9173], "triangles": [22, 37, 21, 22, 23, 37, 37, 23, 25, 23, 24, 25, 21, 36, 20, 20, 36, 19, 21, 37, 36, 36, 35, 19, 19, 35, 18, 37, 25, 36, 25, 26, 36, 26, 27, 36, 36, 27, 35, 35, 27, 34, 18, 35, 34, 1, 2, 29, 2, 3, 29, 3, 4, 29, 31, 0, 30, 29, 30, 1, 0, 1, 30, 4, 6, 29, 29, 6, 30, 4, 5, 6, 6, 7, 30, 31, 30, 9, 30, 7, 9, 7, 8, 9, 9, 10, 31, 17, 18, 34, 34, 27, 28, 34, 16, 17, 34, 33, 16, 33, 34, 0, 33, 0, 32, 32, 0, 31, 28, 0, 34, 33, 15, 16, 15, 33, 14, 33, 32, 14, 14, 32, 13, 32, 12, 13, 31, 11, 32, 32, 11, 12, 31, 10, 11], "vertices": [4, 62, -0.84, 11.07, 0.2193, 65, 6.11, 6.41, 0.59424, 63, 2.15, -13.77, 0.17502, 64, -3.41, -13.91, 0.01144, 4, 62, 0.26, 13.96, 0.04093, 65, 7.99, 3.96, 0.93012, 63, 4.82, -15.32, 0.02816, 64, -0.68, -15.36, 0.00079, 3, 62, -1.47, 16.86, 0.00137, 65, 11.37, 3.91, 0.99696, 63, 7.96, -14.07, 0.00167, 2, 65, 12.65, 2.09, 0.99994, 63, 9.84, -15.27, 6e-05, 1, 65, 12.68, -2.36, 1, 1, 65, 10.82, -4.43, 1, 1, 65, 9.4, -2.07, 1, 1, 65, 7.1, -4.25, 1, 1, 65, 6.04, -6.81, 1, 2, 62, 13.14, 15.42, 0.00296, 65, 2.5, -7.78, 0.99704, 2, 62, 16.32, 8.4, 0.26822, 65, -5.15, -6.82, 0.73178, 2, 62, 15.76, -0.26, 0.94868, 65, -12.24, -1.81, 0.05132, 1, 62, 14.47, -7.21, 1, 1, 62, 11.14, -11.74, 1, 2, 62, 3.33, -10.29, 0.99851, 63, -19.61, -14.5, 0.00149, 2, 62, -1.73, -9.35, 0.94891, 63, -17.88, -9.65, 0.05109, 2, 62, -5.87, -8.59, 0.82063, 63, -16.46, -5.68, 0.17937, 2, 62, -11.6, -7.52, 0.61278, 63, -14.5, -0.19, 0.38722, 2, 62, -14.88, -2.41, 0.33587, 63, -8.93, 2.23, 0.66413, 2, 62, -17.64, 1.07, 0.09422, 63, -5.06, 4.4, 0.90578, 2, 62, -20.17, 3.43, 0.0145, 63, -2.33, 6.52, 0.9855, 2, 63, 4.74, 4, 0.84833, 64, -1.47, 3.95, 0.15167, 1, 64, 5.68, 1.69, 1, 2, 65, 20.8, 13.44, 0.0001, 64, 7.04, -1.41, 0.9999, 4, 62, -11.23, 19.68, 0.00093, 65, 18.88, 10.75, 0.00244, 63, 12.3, -4.89, 0.01836, 64, 6.41, -4.66, 0.97828, 4, 62, -9.23, 15.52, 0.03461, 65, 14.29, 11.22, 0.03119, 63, 7.88, -6.21, 0.34595, 64, 2.04, -6.14, 0.58825, 4, 62, -6.57, 14.21, 0.10402, 65, 11.78, 9.64, 0.08766, 63, 6.16, -8.62, 0.53689, 64, 0.41, -8.61, 0.27142, 4, 62, -4.62, 12.15, 0.19686, 65, 9, 9.06, 0.18262, 63, 3.81, -10.22, 0.50816, 64, -1.88, -10.3, 0.11236, 4, 62, -2.78, 11.18, 0.26214, 65, 7.21, 8, 0.33394, 63, 2.56, -11.88, 0.36167, 64, -3.06, -12, 0.04225, 2, 65, 10.46, 0.4, 0.99994, 63, 8.46, -17.67, 6e-05, 4, 62, 4.78, 13.88, 0.00094, 65, 5.55, 0.15, 0.99854, 63, 4.02, -19.78, 0.00051, 64, -1.31, -19.84, 1e-05, 3, 62, 8.65, 5.84, 0.38164, 65, -3.32, 1.05, 0.61819, 63, -4.53, -22.32, 0.00017, 1, 62, 5.31, -0.28, 1, 3, 62, -1.05, -0.62, 0.96852, 65, -3.74, 12.7, 0.00076, 63, -9.36, -11.71, 0.03072, 3, 62, -4.65, 1.06, 0.71526, 65, -0.43, 14.89, 0.02591, 63, -7.13, -8.42, 0.25883, 3, 62, -12.14, 4.21, 0.15012, 65, 6.17, 19.62, 0.00733, 63, -2.83, -1.53, 0.84256, 1, 63, 1.6, 0.38, 1, 2, 63, 6.25, 0.3, 0.32238, 64, 0.17, 0.3, 0.67762], "hull": 29}}, "yz": {"yz": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0.5, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0, 1, 0.5, 0.66667, 0.5, 0.33333, 0.5], "triangles": [2, 11, 10, 2, 3, 11, 3, 4, 11, 11, 4, 6, 4, 5, 6, 0, 1, 9, 1, 10, 9, 9, 10, 8, 10, 7, 8, 1, 2, 10, 10, 11, 7, 11, 6, 7], "vertices": [2, 56, -6.35, -8.72, 0.80274, 55, -12.01, -4.38, 0.19726, 2, 56, -1.02, -8.91, 0.56159, 55, -6.68, -4.57, 0.43841, 2, 56, 4.31, -9.1, 0.08676, 55, -1.35, -4.76, 0.91324, 1, 55, 3.98, -4.95, 1, 2, 56, 9.87, -2.79, 0.02921, 55, 4.21, 1.55, 0.97079, 2, 56, 10.1, 3.71, 0.24988, 55, 4.44, 8.05, 0.75012, 2, 56, 4.77, 3.9, 0.57365, 55, -0.89, 8.23, 0.42635, 2, 56, -0.56, 4.08, 0.98853, 55, -6.22, 8.42, 0.01147, 1, 56, -5.89, 4.27, 1, 2, 56, -6.12, -2.22, 0.95597, 55, -11.78, 2.11, 0.04403, 2, 56, -0.79, -2.41, 0.8533, 55, -6.45, 1.93, 0.1467, 2, 56, 4.54, -2.6, 0.21879, 55, -1.12, 1.74, 0.78121], "hull": 10}}, "kapaixg3": {"kapaixg3": {"type": "mesh", "uvs": [0.26443, 0.64364, 0.11263, 0.63704, 0, 0.59579, 0, 0.50174, 0.16543, 0.40274, 0.26003, 0.25259, 0.41183, 0, 0.72423, 0, 0.74183, 0.24929, 1, 0.37964, 1, 0.47699, 0.90242, 0.50669, 0.89582, 0.63209, 0.95082, 0.73769, 1, 0.85154, 1, 0.96374, 0.80343, 1, 0.43383, 1, 0.11483, 0.98024, 0, 0.89609, 0.11483, 0.79874, 0.20503, 0.70964, 0.43084, 0.70509, 0.58751, 0.72676, 0.76529, 0.70093], "triangles": [22, 17, 20, 22, 20, 21, 19, 20, 18, 17, 18, 20, 21, 0, 22, 12, 24, 11, 11, 23, 22, 24, 12, 13, 17, 23, 16, 23, 24, 16, 15, 16, 14, 14, 16, 24, 14, 24, 13, 24, 23, 11, 17, 22, 23, 22, 0, 4, 1, 4, 0, 22, 8, 11, 8, 4, 5, 5, 6, 8, 6, 7, 8, 4, 1, 3, 4, 8, 22, 1, 2, 3, 10, 11, 9, 11, 8, 9], "vertices": [3, 77, 0.62, 5.63, 0.20699, 75, -3.38, -5.75, 0.71974, 76, 2.9, -6.39, 0.07327, 3, 77, 0.95, 8.34, 0.91446, 75, -3.57, -8.48, 0.00018, 76, 5.64, -6.49, 0.08536, 2, 77, 2.07, 10.3, 0.93753, 76, 7.69, -7.44, 0.06247, 2, 77, 4.33, 10.16, 0.94619, 76, 7.74, -9.69, 0.05381, 2, 77, 6.51, 7.03, 0.97222, 76, 4.81, -12.13, 0.02778, 2, 77, 9.99, 5.1, 0.99875, 76, 3.19, -15.77, 0.00125, 1, 77, 15.87, 1.99, 1, 1, 77, 15.51, -3.62, 1, 1, 77, 9.51, -3.56, 1, 2, 77, 6.09, -7.99, 0.99993, 75, -9.53, 7.57, 7e-05, 2, 77, 3.76, -7.84, 0.99402, 75, -7.19, 7.54, 0.00598, 2, 77, 3.16, -6.05, 0.95911, 75, -6.5, 5.78, 0.04089, 2, 77, 0.17, -5.73, 0.14478, 75, -3.49, 5.61, 0.85522, 2, 77, -2.42, -6.56, 0.30465, 75, -0.95, 6.57, 0.69535, 2, 77, -5.21, -7.27, 0.08733, 75, 1.8, 7.41, 0.91267, 2, 77, -7.9, -7.09, 0.02149, 75, 4.49, 7.38, 0.97851, 2, 77, -8.54, -3.51, 0.00107, 75, 5.31, 3.83, 0.99893, 2, 75, 5.22, -2.83, 0.11738, 76, -0.33, 2.09, 0.88262, 1, 76, 5.42, 1.74, 1, 2, 77, -5.12, 10.76, 0.00029, 76, 7.53, -0.23, 0.99971, 3, 77, -2.92, 8.55, 0.05592, 75, 0.31, -8.5, 0.17171, 76, 5.51, -2.61, 0.77237, 3, 77, -0.89, 6.79, 0.23198, 75, -1.81, -6.85, 0.32396, 76, 3.94, -4.79, 0.44406, 3, 77, -1.04, 2.73, 0.16518, 75, -1.86, -2.78, 0.74592, 76, -0.12, -4.99, 0.08889, 3, 77, -1.74, -0.05, 0.10508, 75, -1.3, 0.03, 0.84804, 76, -2.96, -4.53, 0.04688, 3, 77, -1.33, -3.28, 0.06424, 75, -1.88, 3.24, 0.92285, 76, -6.14, -5.22, 0.0129], "hull": 22}}, "kapaixg4": {"kapaixg4": {"type": "mesh", "uvs": [0.20065, 0.59827, 0.08186, 0.69427, 0.0564, 0.81427, 0.04962, 0.95426, 0.27362, 0.98493, 0.60961, 1, 0.8947, 0.97693, 1, 0.9316, 1, 0.77027, 1, 0.60627, 0.88282, 0.56093, 0.9507, 0.43827, 0.88452, 0.2316, 0.71822, 0.07294, 0.45858, 0.00094, 0.21592, 0, 0.0598, 0.11294, 0, 0.27427, 0, 0.4436, 0, 0.53827, 0.09883, 0.59427], "triangles": [5, 4, 0, 1, 0, 4, 2, 1, 4, 3, 2, 4, 10, 9, 8, 8, 5, 10, 7, 6, 8, 8, 6, 5, 20, 18, 17, 20, 19, 18, 12, 0, 13, 10, 12, 11, 16, 14, 17, 20, 17, 0, 17, 14, 0, 15, 14, 16, 0, 14, 13, 10, 0, 12, 0, 10, 5], "vertices": [3, 82, 0.44, 7.8, 0.64774, 80, -3, -8.35, 0.01865, 81, 4.4, -7.94, 0.33361, 3, 82, -1.72, 10.4, 0.24373, 80, -0.58, -10.7, 0.00905, 81, 6.93, -5.71, 0.74722, 3, 82, -4.67, 11.22, 0.07896, 80, 2.44, -11.2, 0.00025, 81, 7.67, -2.73, 0.9208, 2, 82, -8.17, 11.72, 0.00127, 81, 8.07, 0.77, 0.99873, 1, 81, 3.71, 1.89, 1, 2, 80, 7.12, -0.25, 0.67048, 81, -2.89, 2.78, 0.32952, 1, 80, 6.54, 5.39, 1, 2, 82, -9.57, -7.05, 0.00289, 80, 5.4, 7.48, 0.99711, 2, 82, -5.53, -7.48, 0.09261, 80, 1.33, 7.48, 0.90739, 2, 82, -1.42, -7.91, 0.32857, 80, -2.8, 7.48, 0.67143, 2, 82, -0.04, -5.73, 0.61232, 80, -3.94, 5.16, 0.38768, 2, 82, 2.89, -7.39, 0.93679, 80, -7.04, 6.5, 0.06321, 1, 82, 8.21, -6.63, 1, 1, 82, 12.53, -3.78, 1, 1, 82, 14.87, 1.14, 1, 2, 82, 15.4, 5.92, 0.99964, 81, 2.93, -22.95, 0.00036, 2, 82, 12.9, 9.29, 0.99159, 81, 6.24, -20.35, 0.00841, 2, 82, 8.98, 10.9, 0.96375, 81, 7.73, -16.39, 0.03625, 2, 82, 4.73, 11.35, 0.8992, 81, 8.06, -12.13, 0.1008, 2, 82, 2.36, 11.6, 0.86082, 81, 8.24, -9.75, 0.13918, 3, 82, 0.75, 9.8, 0.81005, 80, -3.1, -10.36, 0.00215, 81, 6.4, -8.19, 0.18781], "hull": 21}}, "subf": {"subf": {"type": "mesh", "uvs": [0.24613, 0.77168, 0.3402, 1, 0.58316, 1, 0.64909, 0.73668, 0.87971, 0.749, 1, 0.62392, 1, 0.35401, 0.78023, 0, 0.68075, 0.13676, 0.6193, 0, 0.2916, 0, 0.25729, 0.1639, 0.16774, 0.1574, 0, 0.39789, 0, 0.72939, 0.24573, 0.51814, 0.64151, 0.3199, 0.82639, 0.46614, 0.11862, 0.50839], "triangles": [18, 13, 12, 15, 18, 12, 14, 13, 18, 14, 18, 0, 17, 7, 6, 8, 7, 17, 16, 8, 17, 17, 6, 5, 4, 17, 5, 3, 17, 4, 11, 15, 12, 3, 16, 17, 16, 9, 8, 3, 0, 16, 16, 10, 9, 11, 16, 15, 10, 16, 11, 3, 1, 0, 2, 1, 3, 16, 0, 15, 18, 15, 0], "vertices": [1, 51, -5.34, -3.32, 1, 1, 51, -2.8, -6.06, 1, 1, 51, 3.76, -6.06, 1, 1, 51, 5.54, -2.9, 1, 1, 52, 5.42, -3, 1, 1, 52, 8.31, -0.89, 1, 1, 52, 7.67, 2.29, 1, 2, 51, 9.08, 5.94, 0.00075, 52, 1.02, 5.29, 0.99925, 1, 52, -1.29, 3.15, 1, 1, 51, 4.73, 5.94, 1, 1, 51, -4.12, 5.94, 1, 1, 51, -5.04, 3.98, 1, 1, 53, -0.13, -3.44, 1, 1, 53, 4.8, -1.32, 1, 1, 53, 5.44, 2.61, 1, 1, 51, -5.36, -0.27, 1, 1, 51, 5.33, 2.11, 1, 1, 52, 3.34, 0.05, 1, 1, 53, 1.85, 0.51, 1], "hull": 15}}, "flyline": {"flyline": {"type": "path", "lengths": [392.71, 740.18, 1124.04, 1294.52, 1523.61, 2655.46], "vertexCount": 18, "vertices": [179.38, 195.94, 56.22, 178.25, 18.47, 172.83, -165.3, 88.78, -327.2, 106, -605.91, 135.64, -213.83, 180.92, -217.68, 251.64, -222.94, 348.25, -476.04, 237.3, -585.11, 265.46, -651.66, 282.65, -652.86, 357.95, -705.51, 381.82, -770.35, 411.22, -919.01, 408.62, -931.9, 409.02, -1056.27, 412.83]}}, "xz": {"xz": {"type": "mesh", "uvs": [0.01471, 0.35416, 0.17163, 0, 0.63586, 0, 0.7797, 0.31874, 0.50182, 0.38604, 0.30894, 0.36124, 0.29913, 0.28333, 0.22721, 0.32937, 0.28932, 0.46041, 0.40374, 0.48166, 0.4935, 0.4175, 0.67119, 0.38, 0.92389, 0.63506, 1, 0.71839, 1, 0.96006, 0.53158, 1, 0.26856, 1, 0.22702, 0.91372, 0.21471, 1, 0.12548, 1, 0.10933, 0.90706, 0.12087, 0.82206, 0.0801, 0.95622, 0, 0.94539, 0, 0.78706, 0.03702, 0.67539, 0, 0.49118, 0.18346, 0.59368, 0.1073, 0.41868, 0.17884, 0.19368, 0.36807, 0.12118, 0.55499, 0.26118, 0.06807, 0.77618, 0.03576, 0.91118, 0.17192, 0.87368, 0.16038, 0.95868, 0.1373, 0.69118, 0.21346, 0.71618, 0.33346, 0.71118, 0.55499, 0.72618, 0.78807, 0.74118], "triangles": [24, 25, 32, 36, 25, 27, 21, 32, 36, 21, 36, 37, 35, 34, 17, 34, 21, 37, 34, 37, 17, 38, 37, 27, 17, 37, 38, 38, 8, 9, 16, 17, 38, 16, 38, 15, 15, 40, 14, 15, 39, 40, 14, 40, 13, 40, 12, 13, 12, 40, 11, 9, 10, 39, 40, 39, 11, 39, 10, 11, 15, 38, 39, 38, 9, 39, 37, 36, 27, 32, 25, 36, 27, 8, 38, 25, 26, 27, 27, 28, 8, 31, 4, 6, 3, 4, 31, 4, 5, 6, 31, 2, 3, 6, 30, 31, 31, 30, 2, 30, 1, 2, 6, 29, 30, 29, 1, 30, 26, 28, 27, 26, 0, 28, 28, 7, 8, 7, 28, 29, 28, 0, 29, 0, 1, 29, 7, 29, 6, 23, 33, 22, 23, 24, 33, 21, 22, 33, 33, 24, 32, 21, 33, 32, 19, 35, 18, 18, 35, 17, 35, 19, 20, 35, 20, 34, 20, 21, 34], "vertices": [1, 14, 1.5, 3.65, 1, 1, 15, 2.04, 4.03, 1, 1, 16, 2.04, 6.43, 1, 1, 16, 10.54, 5.98, 1, 3, 14, 3.14, -8.93, 0.00553, 15, 4.41, -8.37, 0.00683, 16, 8.44, -1.12, 0.98764, 3, 14, 2.78, -3.89, 0.13346, 15, 0.47, -5.21, 0.18823, 16, 5.49, -5.21, 0.67832, 3, 14, 4.57, -3.29, 0.30936, 15, 1.25, -3.49, 0.29667, 16, 3.73, -4.53, 0.39398, 3, 14, 3.13, -1.66, 0.92583, 15, -0.92, -3.44, 0.04395, 16, 3.79, -6.7, 0.03023, 3, 14, 0.34, -3.84, 0.04884, 15, -1.22, -6.96, 0.00716, 11, -7.51, 7.04, 0.944, 1, 11, -4.53, 6.55, 1, 1, 11, -2.2, 8.1, 1, 1, 11, 2.41, 9.02, 1, 1, 11, 9.01, 2.93, 1, 1, 11, 11, 0.94, 1, 1, 11, 11.03, -4.86, 1, 1, 11, -1.15, -5.88, 1, 1, 11, -7.98, -5.91, 1, 1, 11, -9.07, -3.84, 1, 2, 12, 3.76, 1.03, 1, 13, 5, 5.03, 0, 2, 12, 3.84, -1.29, 1, 13, 5.51, 2.77, 0, 2, 12, 1.63, -1.79, 0.9826, 13, 3.42, 1.88, 0.0174, 3, 12, -0.42, -1.57, 0.46061, 13, 1.36, 1.73, 0.53746, 14, -9.01, -1.18, 0.00194, 1, 13, 4.74, 1.39, 1, 1, 13, 4.93, -0.7, 1, 1, 13, 1.22, -1.52, 1, 4, 12, -3.86, -3.87, 0.00072, 13, -1.6, -1.17, 0.19562, 14, -5.96, 1.63, 0.02766, 11, -14.04, 1.85, 0.776, 2, 13, -5.71, -3.06, 0.008, 14, -1.8, 3.41, 0.992, 1, 11, -10.24, 3.83, 1, 1, 14, 0.43, 0.99, 1, 2, 14, 6.09, 0.19, 0.71534, 15, -0.26, -0.01, 0.28466, 3, 14, 8.73, -4.32, 0.00255, 15, 4.83, -1.14, 0.48564, 16, 1.2, -1.07, 0.51181, 1, 16, 6.5, 1.54, 1, 1, 11, -13.22, -0.56, 1, 1, 13, 3.93, 0.03, 1, 1, 11, -10.51, -2.89, 1, 1, 12, 2.82, -0.42, 1, 1, 11, -11.43, 1.49, 1, 1, 11, -9.45, 0.9, 1, 1, 11, -6.33, 1.03, 1, 1, 11, -0.57, 0.7, 1, 1, 11, 5.49, 0.37, 1], "hull": 27}}, "flyline1": {"flyline1": {"type": "path", "lengths": [280.62, 438.07, 721.14, 887.73, 1118.98, 2167.75], "vertexCount": 18, "vertices": [-54.49, 516.82, -27.07, 506.96, 62.93, 474.58, 162.32, 494.33, 232.23, 413.46, 309.28, 324.35, 239.18, 308.02, 289.56, 273.01, 381.31, 209.24, 472.31, 291.11, 566.76, 264.53, 623.49, 248.57, 631.16, 179.89, 680.96, 146.52, 769.45, 87.21, 832.55, 87.62, 899.62, 81.55, 928.63, 78.93]}}, "sd4": {"sd": {"y": 2.54, "scaleX": 1.0193, "width": 22, "height": 12}}, "kapaixg2": {"kapaixg2": {"type": "mesh", "uvs": [0.21436, 0.6028, 0.12819, 0.51902, 0.16475, 0.41071, 0, 0.31263, 0, 0.11441, 0.07858, 0, 0.26136, 0, 0.42064, 0.12463, 0.5773, 0.23089, 0.73658, 0.23702, 1, 0.42297, 1, 0.5088, 0.83058, 0.64571, 0.94286, 0.67228, 0.9768, 0.82963, 0.8358, 1, 0.53292, 1, 0.18825, 1, 0.06031, 0.86845, 0.16997, 0.71519, 0.6058, 0.72139, 0.73108, 0.71421, 0.37969, 0.68552, 0.35066, 0.31368, 0.19942, 0.17379, 0.55386, 0.50976, 0.58136, 0.84573, 0.32011, 0.8541, 0.79066, 0.84334, 0.80746, 0.48107], "triangles": [19, 0, 22, 27, 19, 22, 18, 19, 27, 17, 18, 27, 17, 27, 16, 13, 28, 21, 28, 13, 14, 26, 22, 20, 27, 22, 26, 16, 27, 26, 15, 28, 14, 26, 20, 21, 21, 28, 26, 15, 26, 28, 16, 26, 15, 24, 5, 6, 24, 6, 7, 4, 5, 24, 3, 4, 24, 2, 3, 24, 23, 24, 7, 23, 2, 24, 25, 2, 23, 29, 9, 10, 29, 10, 11, 25, 23, 8, 25, 0, 2, 1, 2, 0, 12, 29, 11, 22, 0, 25, 25, 8, 9, 25, 9, 29, 12, 21, 25, 12, 25, 29, 20, 22, 25, 21, 20, 25, 13, 21, 12, 23, 7, 8], "vertices": [3, 70, 4.36, 7.52, 0.60965, 68, -2.89, -8.98, 0.0681, 69, 4.95, -8.03, 0.32225, 5, 70, 7.27, 8.12, 0.99439, 71, 0.13, 8.01, 0.00418, 72, -6.28, 6.79, 7e-05, 68, -4.59, -11.41, 0.00013, 69, 7.24, -9.92, 0.00123, 5, 70, 9.57, 6.06, 0.9895, 71, 1.53, 5.26, 0.00848, 72, -4.31, 4.42, 0.00151, 68, -7.68, -11.49, 1e-05, 69, 7.08, -13, 0.0005, 4, 70, 13.6, 7.99, 0.00403, 71, 5.99, 5.62, 0.34182, 72, -0.04, 5.75, 0.65308, 69, 11.11, -14.93, 0.00107, 2, 71, 9.64, 1.54, 0.00124, 72, 4.42, 2.58, 0.99876, 2, 71, 10.48, -1.94, 0.00083, 72, 6.01, -0.63, 0.99917, 2, 71, 7.54, -4.58, 0.16135, 72, 3.73, -3.85, 0.83865, 3, 70, 14.07, -2.46, 0.03138, 71, 2.68, -4.31, 0.85947, 72, -1.07, -4.66, 0.10915, 1, 70, 9.92, -4.14, 1, 1, 70, 8.2, -7.12, 1, 1, 70, 1.04, -9.84, 1, 2, 70, -1.07, -8.76, 0.99878, 68, -10.12, 6.59, 0.00122, 2, 70, -2.76, -3.78, 0.70599, 68, -5.47, 4.14, 0.29401, 2, 70, -4.52, -5.61, 0.43771, 68, -5.44, 6.67, 0.56229, 2, 70, -8.72, -4.28, 0.21038, 68, -1.48, 8.59, 0.78962, 2, 70, -11.52, 0.58, 0.0243, 68, 3.89, 6.98, 0.9757, 2, 68, 5.71, 0.7, 0.91845, 69, -4.02, 1.3, 0.08155, 1, 69, 3.27, 2.82, 1, 4, 70, -0.64, 13.83, 0.00328, 71, -5.21, 16.18, 0.00042, 68, 5.08, -10.12, 0.00011, 69, 6.71, -0.17, 0.99619, 3, 70, 2.04, 9.79, 0.22509, 68, 0.36, -9.03, 0.07193, 69, 5.26, -4.8, 0.70298, 4, 70, -2.41, 1.49, 0.38814, 71, -11.28, 5.29, 0.00012, 68, -2.11, 0.06, 0.60424, 69, -4, -6.55, 0.0075, 2, 70, -3.46, -1.01, 0.49129, 68, -3.05, 2.6, 0.50871, 3, 70, 0.7, 5.39, 0.44142, 68, -1.69, -4.91, 0.24418, 69, 0.99, -6.52, 0.3144, 4, 70, 10.12, 1.26, 0.7025, 71, 0.33, 0.58, 0.29681, 72, -4.45, -0.41, 0.00027, 69, 3.69, -16.44, 0.00042, 1, 72, 0.59, 0.02, 1, 1, 70, 3.3, -0.17, 1, 1, 68, 1.33, 0.51, 1, 4, 70, -2.85, 8.65, 0.03338, 71, -9.13, 12.14, 0.00287, 68, 3.13, -4.84, 0.09832, 69, 1.3, -1.7, 0.86543, 2, 70, -7.22, -0.53, 0.12798, 68, 0.01, 4.83, 0.87202, 2, 70, 1.51, -5.41, 0.99756, 68, -9.69, 2.39, 0.00244], "hull": 20}}, "bf2": {"bf": {"type": "mesh", "uvs": [0.22954, 0.67269, 0.2579, 1, 0.66917, 1, 0.76608, 0.74581, 0.72826, 0.55569, 0.90554, 0.55569, 1, 0.24369, 1, 0, 0.67626, 0, 0.67626, 0.13644, 0.54863, 0, 0.3099, 0, 0.30754, 0.16569, 0.22954, 0.11206, 0, 0.12669, 0, 0.42894, 0.13972, 0.71656, 0.26736, 0.40944, 0.69045, 0.33631], "triangles": [6, 5, 18, 6, 18, 7, 9, 8, 7, 17, 16, 15, 15, 14, 17, 14, 13, 17, 17, 13, 12, 18, 9, 7, 18, 10, 9, 2, 0, 17, 2, 17, 4, 2, 4, 3, 2, 1, 0, 17, 18, 4, 10, 18, 12, 18, 17, 12, 5, 4, 18, 11, 10, 12, 0, 16, 17], "vertices": [1, 7, -7.96, 5.28, 1, 1, 7, -7.26, 0, 1, 1, 7, 6.3, -0.61, 1, 1, 7, 9.68, 3.31, 1, 1, 7, 8.57, 6.41, 1, 1, 8, 4.35, -5.37, 1, 1, 8, 9.23, -2.09, 1, 1, 8, 10.83, 1.47, 1, 1, 8, 1.08, 5.84, 1, 1, 7, 7.15, 13.18, 1, 1, 7, 3.04, 15.56, 1, 1, 7, -4.83, 15.91, 1, 1, 7, -5.02, 13.27, 1, 2, 7, -7.56, 14.24, 0.00015, 9, 1.93, -4.69, 0.99985, 1, 9, 8.7, -1.27, 1, 1, 9, 6.65, 3.11, 1, 1, 9, 0.52, 5.33, 1, 1, 7, -6.52, 9.43, 1, 1, 7, 7.48, 9.97, 1], "hull": 17}}, "sd10": {"sd": {"x": 0.95, "y": -22.99, "width": 22, "height": 12}}, "sd11": {"sd": {"x": -3.15, "y": -4.38, "width": 22, "height": 12}}, "sd12": {"sd": {"x": -3.15, "y": -4.38, "width": 22, "height": 12}}, "bf": {"bf": {"type": "mesh", "uvs": [0.22954, 0.67269, 0.2579, 1, 0.66917, 1, 0.76608, 0.74581, 0.72826, 0.55569, 0.90554, 0.55569, 1, 0.24369, 1, 0, 0.67626, 0, 0.67626, 0.13644, 0.54863, 0, 0.3099, 0, 0.30754, 0.16569, 0.22954, 0.11206, 0, 0.12669, 0, 0.42894, 0.13972, 0.71656, 0.26736, 0.40944, 0.69045, 0.33631], "triangles": [6, 5, 18, 6, 18, 7, 9, 8, 7, 17, 16, 15, 15, 14, 17, 14, 13, 17, 17, 13, 12, 18, 9, 7, 18, 10, 9, 2, 0, 17, 2, 17, 4, 2, 4, 3, 2, 1, 0, 17, 18, 4, 10, 18, 12, 18, 17, 12, 5, 4, 18, 11, 10, 12, 0, 16, 17], "vertices": [1, 3, -7.96, 5.28, 1, 1, 3, -7.26, 0, 1, 1, 3, 6.3, -0.61, 1, 1, 3, 9.68, 3.31, 1, 1, 3, 8.57, 6.41, 1, 1, 4, 4.35, -5.37, 1, 1, 4, 9.23, -2.09, 1, 1, 4, 10.83, 1.47, 1, 1, 4, 1.08, 5.84, 1, 1, 3, 7.15, 13.18, 1, 1, 3, 3.04, 15.56, 1, 1, 3, -4.83, 15.91, 1, 1, 3, -5.02, 13.27, 1, 2, 3, -7.56, 14.24, 0.00015, 5, 1.93, -4.69, 0.99985, 1, 5, 8.7, -1.27, 1, 1, 5, 6.65, 3.11, 1, 1, 5, 0.52, 5.33, 1, 1, 3, -6.52, 9.43, 1, 1, 3, 7.48, 9.97, 1], "hull": 17}}, "lg": {"lg": {"type": "mesh", "uvs": [0, 0.5718, 0.06498, 0.4918, 0.11623, 0.45291, 0.06748, 0.31624, 0.17282, 0, 0.57032, 0, 0.93032, 0.0179, 0.95282, 0.25457, 1, 0.51124, 1, 0.79124, 0.76796, 0.70114, 0.61921, 0.63003, 0.68421, 0.72225, 0.71796, 0.83558, 0.64796, 0.95336, 0.47171, 0.98892, 0.27921, 1, 0.01921, 1, 0, 0.90003, 0, 0.70781, 0.28421, 0.82447, 0.32421, 0.63114, 0.37046, 0.53225, 0.50171, 0.29892, 0.73546, 0.18892, 0.73796, 0.47336], "triangles": [24, 5, 6, 24, 6, 7, 23, 4, 5, 23, 5, 24, 3, 4, 23, 2, 3, 23, 25, 24, 7, 23, 24, 25, 25, 7, 8, 22, 23, 25, 10, 25, 8, 11, 25, 10, 10, 8, 9, 22, 2, 23, 11, 22, 25, 21, 2, 22, 21, 22, 11, 21, 19, 0, 21, 1, 2, 1, 21, 0, 21, 11, 20, 20, 19, 21, 18, 19, 20, 14, 12, 13, 20, 11, 12, 14, 15, 12, 15, 20, 12, 16, 17, 18, 20, 16, 18, 16, 20, 15], "vertices": [3, 33, 5.36, 5.16, 0.27113, 34, 1.4, 5.6, 0.6688, 35, -4.35, 4.02, 0.06007, 3, 33, 6.73, 4.03, 0.12522, 34, 2.99, 4.8, 0.67039, 35, -2.57, 4.04, 0.20438, 3, 33, 7.37, 3.17, 0.04354, 34, 3.81, 4.09, 0.51992, 35, -1.52, 3.8, 0.43654, 3, 33, 9.88, 3.78, 8e-05, 34, 6.12, 5.24, 0.16018, 35, -0.01, 5.9, 0.83974, 2, 34, 12, 4.45, 6e-05, 35, 5.56, 7.94, 0.99994, 1, 35, 9.36, 2.84, 1, 2, 34, 13.55, -7.57, 0.00054, 35, 12.54, -1.98, 0.99946, 2, 34, 9.39, -8.58, 0.03858, 35, 9.33, -4.81, 0.96142, 2, 34, 4.94, -10.04, 0.15937, 35, 6.07, -8.17, 0.84063, 2, 34, -0.04, -10.82, 0.21734, 35, 2.03, -11.18, 0.78266, 3, 33, 2.23, -6.95, 0.02743, 34, 0.99, -6.9, 0.23784, 35, 1.12, -7.23, 0.73473, 3, 33, 3.67, -4.66, 0.25337, 34, 1.89, -4.35, 0.3502, 35, 0.72, -4.56, 0.39643, 3, 33, 1.94, -5.59, 0.68631, 34, 0.41, -5.63, 0.24122, 35, 0.01, -6.38, 0.07247, 3, 33, -0.13, -5.99, 0.85848, 34, -1.52, -6.48, 0.13094, 35, -1.3, -8.03, 0.01058, 2, 33, -2.17, -4.74, 0.94162, 34, -3.79, -5.7, 0.05838, 2, 33, -2.63, -1.88, 0.99345, 34, -4.85, -3.01, 0.00655, 1, 33, -2.63, 1.2, 1, 2, 33, -2.35, 5.36, 0.98882, 34, -6.16, 4.11, 0.01118, 2, 33, -0.54, 5.54, 0.94226, 34, -4.43, 4.69, 0.05774, 3, 33, 2.91, 5.32, 0.57934, 34, -1.01, 5.22, 0.41788, 35, -6.31, 2.56, 0.00278, 1, 33, 0.52, 0.92, 1, 1, 34, 1.15, 0.31, 1, 3, 33, 5.68, -0.8, 0.00106, 34, 3.02, -0.15, 0.99617, 35, -0.24, -0.31, 0.00277, 1, 35, 4.38, 0.51, 1, 2, 34, 10.02, -4.97, 0.00775, 35, 8.2, -1.31, 0.99225, 3, 33, 6.36, -6.74, 0.01531, 34, 4.97, -5.79, 0.15386, 35, 4.12, -4.4, 0.83083], "hull": 20}}, "xz2": {"xz": {"type": "mesh", "uvs": [0.01471, 0.35416, 0.17163, 0, 0.63586, 0, 0.7797, 0.31874, 0.50182, 0.38604, 0.30894, 0.36124, 0.29913, 0.28333, 0.22721, 0.32937, 0.28932, 0.46041, 0.40374, 0.48166, 0.4935, 0.4175, 0.67119, 0.38, 0.92389, 0.63506, 1, 0.71839, 1, 0.96006, 0.53158, 1, 0.26856, 1, 0.22702, 0.91372, 0.21471, 1, 0.12548, 1, 0.10933, 0.90706, 0.12087, 0.82206, 0.0801, 0.95622, 0, 0.94539, 0, 0.78706, 0.03702, 0.67539, 0, 0.49118, 0.18346, 0.59368, 0.1073, 0.41868, 0.17884, 0.19368, 0.36807, 0.12118, 0.55499, 0.26118, 0.06807, 0.77618, 0.03576, 0.91118, 0.17192, 0.87368, 0.16038, 0.95868, 0.1373, 0.69118, 0.21346, 0.71618, 0.33346, 0.71118, 0.55499, 0.72618, 0.78807, 0.74118], "triangles": [24, 25, 32, 36, 25, 27, 21, 32, 36, 21, 36, 37, 35, 34, 17, 34, 21, 37, 34, 37, 17, 38, 37, 27, 17, 37, 38, 38, 8, 9, 16, 17, 38, 16, 38, 15, 15, 40, 14, 15, 39, 40, 14, 40, 13, 40, 12, 13, 12, 40, 11, 9, 10, 39, 40, 39, 11, 39, 10, 11, 15, 38, 39, 38, 9, 39, 37, 36, 27, 32, 25, 36, 27, 8, 38, 25, 26, 27, 27, 28, 8, 31, 4, 6, 3, 4, 31, 4, 5, 6, 31, 2, 3, 6, 30, 31, 31, 30, 2, 30, 1, 2, 6, 29, 30, 29, 1, 30, 26, 28, 27, 26, 0, 28, 28, 7, 8, 7, 28, 29, 28, 0, 29, 0, 1, 29, 7, 29, 6, 23, 33, 22, 23, 24, 33, 21, 22, 33, 33, 24, 32, 21, 33, 32, 19, 35, 18, 18, 35, 17, 35, 19, 20, 35, 20, 34, 20, 21, 34], "vertices": [1, 21, 1.5, 3.65, 1, 1, 22, 2.04, 4.03, 1, 1, 23, 2.04, 6.43, 1, 1, 23, 10.54, 5.98, 1, 3, 21, 3.14, -8.93, 0.00553, 22, 4.41, -8.37, 0.00683, 23, 8.44, -1.12, 0.98764, 3, 21, 2.78, -3.89, 0.13346, 22, 0.47, -5.21, 0.18823, 23, 5.49, -5.21, 0.67832, 3, 21, 4.57, -3.29, 0.30936, 22, 1.25, -3.49, 0.29667, 23, 3.73, -4.53, 0.39398, 3, 21, 3.13, -1.66, 0.92583, 22, -0.92, -3.44, 0.04395, 23, 3.79, -6.7, 0.03023, 3, 21, 0.34, -3.84, 0.04884, 22, -1.22, -6.96, 0.00716, 18, -7.51, 7.04, 0.944, 1, 18, -4.53, 6.55, 1, 1, 18, -2.2, 8.1, 1, 1, 18, 2.41, 9.02, 1, 1, 18, 9.01, 2.93, 1, 1, 18, 11, 0.94, 1, 1, 18, 11.03, -4.86, 1, 1, 18, -1.15, -5.88, 1, 1, 18, -7.98, -5.91, 1, 1, 18, -9.07, -3.84, 1, 2, 19, 3.76, 1.03, 1, 20, 5, 5.03, 0, 2, 19, 3.84, -1.29, 1, 20, 5.51, 2.77, 0, 2, 19, 1.63, -1.79, 0.9826, 20, 3.42, 1.88, 0.0174, 3, 19, -0.42, -1.57, 0.46061, 20, 1.36, 1.73, 0.53746, 21, -9.01, -1.18, 0.00194, 1, 20, 4.74, 1.39, 1, 1, 20, 4.93, -0.7, 1, 1, 20, 1.22, -1.52, 1, 4, 19, -3.86, -3.87, 0.00072, 20, -1.6, -1.17, 0.19562, 21, -5.96, 1.63, 0.02766, 18, -14.04, 1.85, 0.776, 2, 20, -5.71, -3.06, 0.008, 21, -1.8, 3.41, 0.992, 1, 18, -10.24, 3.83, 1, 1, 21, 0.43, 0.99, 1, 2, 21, 6.09, 0.19, 0.71534, 22, -0.26, -0.01, 0.28466, 3, 21, 8.73, -4.32, 0.00255, 22, 4.83, -1.14, 0.48564, 23, 1.2, -1.07, 0.51181, 1, 23, 6.5, 1.54, 1, 1, 18, -13.22, -0.56, 1, 1, 20, 3.93, 0.03, 1, 1, 18, -10.51, -2.89, 1, 1, 19, 2.82, -0.42, 1, 1, 18, -11.43, 1.49, 1, 1, 18, -9.45, 0.9, 1, 1, 18, -6.33, 1.03, 1, 1, 18, -0.57, 0.7, 1, 1, 18, 5.49, 0.37, 1], "hull": 27}}}}], "animations": {"animation1": {"slots": {"lg2": {"attachment": [{"name": null}]}, "bf2": {"color": [{"time": 2.0333, "color": "ffffffff"}, {"time": 2.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.2, "color": "ffffffff"}]}, "kapaixg2": {"attachment": [{"name": null}]}, "sd4": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 13.7333, "color": "ffffffff"}, {"time": 14.3333, "color": "ffffff00"}]}, "sd12": {"attachment": [{"name": null}]}, "sd6": {"attachment": [{"name": null}]}, "sd": {"color": [{"color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 13.7667, "color": "ffffffff"}, {"time": 14.2667, "color": "ffffff00"}]}, "bf": {"color": [{"time": 13.8333, "color": "ffffffff"}, {"time": 14.3333, "color": "ffffff00"}]}, "px": {"color": [{"color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 11.4333, "color": "ffffffff"}, {"time": 12.2, "color": "ffffff00"}]}, "xian1": {"attachment": [{"name": null}]}, "yz2": {"attachment": [{"name": null}]}, "sd8": {"attachment": [{"name": null}]}, "zz": {"attachment": [{"name": null}]}, "zz2": {"attachment": [{"name": null}]}, "yz": {"attachment": [{"name": null}]}, "xz2": {"color": [{"time": 10.0667, "color": "ffffffff"}, {"time": 10.6333, "color": "ffffff00"}, {"time": 11.3, "color": "ffffffff"}]}, "xian2": {"attachment": [{"name": null}]}, "sd7": {"attachment": [{"name": null}]}, "lg": {"attachment": [{"name": null}]}, "kapaixg1": {"attachment": [{"name": null}]}, "sd3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 10, "color": "ffffff00"}, {"time": 10.7, "color": "ffffffff", "curve": "stepped"}, {"time": 10.9667, "color": "ffffffff"}, {"time": 11.5667, "color": "ffffff00"}]}, "sd2": {"color": [{"time": 2.0333, "color": "ffffffff"}, {"time": 2.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.9333, "color": "ffffffff"}]}, "sd10": {"attachment": [{"name": null}]}, "sd9": {"attachment": [{"name": null}]}, "xz": {"color": [{"color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 13.7667, "color": "ffffffff"}, {"time": 14.3333, "color": "ffffff00"}]}, "sd5": {"color": [{"color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 11.4333, "color": "ffffffff"}, {"time": 12.2, "color": "ffffff00"}]}, "subf": {"attachment": [{"name": null}]}, "sd11": {"attachment": [{"name": null}]}}, "bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 11, "curve": 0.25, "c3": 0.75}, {"time": 11.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 12, "curve": 0.25, "c3": 0.75}, {"time": 12.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "curve": 0.25, "c3": 0.75}, {"time": 12.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 13, "curve": 0.25, "c3": 0.75}, {"time": 13.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "curve": 0.25, "c3": 0.75}, {"time": 13.5, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 13.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.8333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 14, "curve": 0.25, "c3": 0.75}, {"time": 14.1667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 14.3333}], "scale": [{"x": 0.56, "y": 0.6}]}, "bone3": {"rotate": [{"angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.4333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.5, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.6, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.6667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.7667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.8333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.9333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 14, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 14.1, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 14.1667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "angle": -44.51}]}, "bone2": {"rotate": [{"angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.4333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.5, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.6, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.6667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.7667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.8333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.9333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 14, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 14.1, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 14.1667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "angle": 43.54}]}, "bone4": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -106.96, "y": -19.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "x": -184.18, "y": -56.06, "curve": "stepped"}, {"time": 5.1333, "x": -184.18, "y": -56.06, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 7.4333, "x": -117.59, "y": -110.95, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 9.5333, "x": -4.58, "y": -141.28, "curve": "stepped"}, {"time": 10.6667, "x": -4.58, "y": -141.28}, {"time": 12.3667, "x": 32.93, "y": -79.06}, {"time": 14.2667, "x": 112.28, "y": -30.32}], "scale": [{"time": 5.1, "curve": "stepped"}, {"time": 5.1333, "x": -1}]}, "bone6": {"translate": [{"y": -1.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 8.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 8.8667, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 10.2, "curve": 0.25, "c3": 0.75}, {"time": 10.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 10.8667, "curve": 0.25, "c3": 0.75}, {"time": 11.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 11.2, "curve": 0.25, "c3": 0.75}, {"time": 11.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 11.5333, "curve": 0.25, "c3": 0.75}, {"time": 11.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 11.8667, "curve": 0.25, "c3": 0.75}, {"time": 12.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 12.2, "curve": 0.25, "c3": 0.75}, {"time": 12.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 12.5333, "curve": 0.25, "c3": 0.75}, {"time": 12.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 12.8667, "curve": 0.25, "c3": 0.75}, {"time": 13.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 13.2, "curve": 0.25, "c3": 0.75}, {"time": 13.3667, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 13.5333, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 13.8667, "curve": 0.25, "c3": 0.75}, {"time": 14.0333, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 14.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 14.3333, "y": -1.48}], "scale": [{"x": 0.56, "y": 0.6}]}, "bone7": {"rotate": [{"angle": -9.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 8.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 10.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 11.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 12.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 12.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.3667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.4667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.5333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.6333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.8, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 13.8667, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 13.9667, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 14.0333, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 14.1333, "angle": -61.69, "curve": 0.25, "c3": 0.75}, {"time": 14.2, "angle": 43.54, "curve": 0.25, "c3": 0.75}, {"time": 14.3, "angle": -61.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 14.3333, "angle": -9.07}]}, "bone8": {"rotate": [{"angle": 16.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 8.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 10.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 11.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 12.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 12.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.3667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.4667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.5333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.6333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.8, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 13.8667, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 13.9667, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 14.0333, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 14.1333, "angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 14.2, "angle": -44.51, "curve": 0.25, "c3": 0.75}, {"time": 14.3, "angle": 76.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 14.3333, "angle": 16.05}]}, "bone5": {"translate": [{"x": 123.5, "y": 56.41, "curve": "stepped"}, {"time": 0.9333, "x": 123.5, "y": 56.41, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 147.05, "y": 108.12}, {"time": 2.5333, "x": -157.25, "y": 69.98, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "x": -200.89, "y": 0.52, "curve": "stepped"}, {"time": 5.1667, "x": -200.89, "y": 0.52, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -25.49, "y": -27.56, "curve": "stepped"}, {"time": 9.7333, "x": -25.49, "y": -27.56, "curve": 0.25, "c3": 0.75}, {"time": 14.1667, "x": 123.5, "y": 56.41}]}, "bone10": {"rotate": [{"angle": 27.66}, {"time": 0.3333, "angle": -59.55}, {"time": 0.6667, "angle": 27.66}, {"time": 1, "angle": -59.55}, {"time": 1.3333, "angle": 27.66}, {"time": 1.6667, "angle": -59.55}, {"time": 2, "angle": 27.66}, {"time": 2.3333, "angle": -59.55}, {"time": 2.6667, "angle": 27.66}, {"time": 3, "angle": -59.55}, {"time": 3.3333, "angle": 27.66}, {"time": 3.6667, "angle": -59.55}, {"time": 4, "angle": 27.66}, {"time": 4.3333, "angle": -59.55}, {"time": 4.6667, "angle": 27.66}, {"time": 5, "angle": -59.55}, {"time": 5.3333, "angle": 27.66}, {"time": 5.6667, "angle": -59.55}, {"time": 6, "angle": 27.66}, {"time": 6.3333, "angle": -59.55}, {"time": 6.6667, "angle": 27.66}, {"time": 7, "angle": -59.55}, {"time": 7.3333, "angle": 27.66}, {"time": 7.6667, "angle": -59.55}, {"time": 8, "angle": 27.66}, {"time": 8.3333, "angle": -59.55}, {"time": 8.6667, "angle": 27.66}, {"time": 9, "angle": -59.55}, {"time": 9.3333, "angle": 27.66}, {"time": 9.6667, "angle": -59.55}, {"time": 10, "angle": 27.66}, {"time": 10.3333, "angle": -59.55}, {"time": 10.6667, "angle": 27.66}, {"time": 11, "angle": -59.55}, {"time": 11.3333, "angle": 27.66}, {"time": 11.6667, "angle": -59.55}, {"time": 12, "angle": 27.66}, {"time": 12.3333, "angle": -59.55}, {"time": 12.6667, "angle": 27.66}, {"time": 13, "angle": -59.55}, {"time": 13.3333, "angle": 27.66}, {"time": 13.6667, "angle": -59.55}, {"time": 14, "angle": 27.66}]}, "bone13": {"rotate": [{"angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.6667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.3333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.6667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.3333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.4667, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.1333, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.6667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 11.1333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 11.8, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.1333, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.6667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 13.1333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 13.3333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 13.4667, "curve": 0.25, "c3": 0.75}, {"time": 13.8, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 14, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 14.1333, "curve": "stepped"}, {"time": 14.3, "curve": "stepped"}, {"time": 14.3333, "angle": -2.41}]}, "bone14": {"rotate": [{"angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.6667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.8667, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 7.3333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.5333, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.6667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.8667, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.3333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.2, "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10.6667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.8667, "curve": 0.25, "c3": 0.75}, {"time": 11.2, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 11.5333, "curve": 0.25, "c3": 0.75}, {"time": 11.8667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 12.2, "curve": 0.25, "c3": 0.75}, {"time": 12.5333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12.6667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 12.8667, "curve": 0.25, "c3": 0.75}, {"time": 13.2, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 13.3333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 13.5333, "curve": 0.25, "c3": 0.75}, {"time": 13.8667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 14, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 14.2, "curve": "stepped"}, {"time": 14.3, "curve": "stepped"}, {"time": 14.3333, "angle": -4.15}]}, "bone11": {"rotate": [{"angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 18.77}, {"time": 0.5333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "angle": 18.77}, {"time": 1.2, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "angle": 18.77}, {"time": 1.8667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.2, "angle": 18.77}, {"time": 2.5333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8667, "angle": 18.77}, {"time": 3.2, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.5333, "angle": 18.77}, {"time": 3.8667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2, "angle": 18.77}, {"time": 4.5333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.6667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8667, "angle": 18.77}, {"time": 5.2, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.5333, "angle": 18.77}, {"time": 5.8667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.2, "angle": 18.77}, {"time": 6.5333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.8667, "angle": 18.77}, {"time": 7.2, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 7.3333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.5333, "angle": 18.77}, {"time": 7.8667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.2, "angle": 18.77}, {"time": 8.5333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.6667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.8667, "angle": 18.77}, {"time": 9.2, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.3333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.5333, "angle": 18.77}, {"time": 9.8667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.2, "angle": 18.77}, {"time": 10.5333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10.6667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.8667, "angle": 18.77}, {"time": 11.2, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 11.5333, "angle": 18.77}, {"time": 11.8667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 12.2, "angle": 18.77}, {"time": 12.5333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12.6667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 12.8667, "angle": 18.77}, {"time": 13.2, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 13.3333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 13.5333, "angle": 18.77}, {"time": 13.8667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 14, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 14.2, "angle": 18.77, "curve": "stepped"}, {"time": 14.3, "angle": 18.77, "curve": "stepped"}, {"time": 14.3333, "angle": -29.84}]}, "bone12": {"rotate": [{"angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.6667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.3333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.7333, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 7.3333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.4, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.4, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.6667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.3333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.4, "curve": 0.25, "c3": 0.75}, {"time": 9.7333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.0667, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10.6667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.7333, "curve": 0.25, "c3": 0.75}, {"time": 11.0667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.4, "curve": 0.25, "c3": 0.75}, {"time": 11.7333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12.0667, "curve": 0.25, "c3": 0.75}, {"time": 12.4, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12.6667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12.7333, "curve": 0.25, "c3": 0.75}, {"time": 13.0667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 13.3333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 13.4, "curve": 0.25, "c3": 0.75}, {"time": 13.7333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 14, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 14.0667, "curve": "stepped"}, {"time": 14.3, "curve": "stepped"}, {"time": 14.3333, "angle": -0.85}]}, "bone9": {"rotate": [{"angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 13, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 13.6667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 14, "angle": -2.33}]}, "xiezi": {"translate": [{}, {"time": 3.0667, "x": 113.68, "y": -25.76, "curve": "stepped"}, {"time": 4.0333, "x": 113.68, "y": -25.76}, {"time": 7.3, "x": 154.56, "y": -95.76, "curve": "stepped"}, {"time": 8.2333, "x": 154.56, "y": -95.76}, {"time": 11.2, "x": 187.29, "y": -196.54}, {"time": 14.3333, "x": 261.13, "y": -271.32}]}, "bone15": {"rotate": [{"angle": 3.01, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 9.3, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "angle": -2.33, "curve": "stepped"}, {"time": 10.6333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 12.3, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 12.6333, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 12.9667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 13.6333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 13.9667, "angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 14.3, "angle": 3.25, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 14.3333, "angle": 3.01}]}, "bone16": {"rotate": [{"angle": -50.83}, {"time": 0.3, "angle": 27.66}, {"time": 0.6333, "angle": -59.55}, {"time": 0.9667, "angle": 27.66}, {"time": 1.3, "angle": -59.55}, {"time": 1.6333, "angle": 27.66}, {"time": 1.9667, "angle": -59.55}, {"time": 2.3, "angle": 27.66}, {"time": 2.6333, "angle": -59.55}, {"time": 2.9667, "angle": 27.66}, {"time": 3.3, "angle": -59.55}, {"time": 3.6333, "angle": 27.66}, {"time": 3.9667, "angle": -59.55}, {"time": 4.3, "angle": 27.66}, {"time": 4.6333, "angle": -59.55}, {"time": 4.9667, "angle": 27.66}, {"time": 5.3, "angle": -59.55}, {"time": 5.6333, "angle": 27.66}, {"time": 5.9667, "angle": -59.55}, {"time": 6.3, "angle": 27.66}, {"time": 6.6333, "angle": -59.55}, {"time": 6.9667, "angle": 27.66}, {"time": 7.3, "angle": -59.55}, {"time": 7.6333, "angle": 27.66}, {"time": 7.9667, "angle": -59.55}, {"time": 8.3, "angle": 27.66}, {"time": 8.6333, "angle": -59.55}, {"time": 8.9667, "angle": 27.66}, {"time": 9.3, "angle": -59.55}, {"time": 9.6333, "angle": 27.66}, {"time": 9.9667, "angle": -59.55}, {"time": 10.3, "angle": 27.66, "curve": "stepped"}, {"time": 10.6333, "angle": 27.66}, {"time": 10.9667, "angle": -59.55}, {"time": 11.3, "angle": 27.66}, {"time": 11.6333, "angle": -59.55}, {"time": 11.9667, "angle": 27.66}, {"time": 12.3, "angle": -59.55}, {"time": 12.6333, "angle": 27.66}, {"time": 12.9667, "angle": -59.55}, {"time": 13.3, "angle": 27.66}, {"time": 13.6333, "angle": -59.55}, {"time": 13.9667, "angle": 27.66}, {"time": 14.3, "angle": -59.55}, {"time": 14.3333, "angle": -50.83}]}, "bone17": {"rotate": [{"angle": -19.68}, {"time": 0.1667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "angle": 18.77}, {"time": 0.8333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1667, "angle": 18.77}, {"time": 1.5, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8333, "angle": 18.77}, {"time": 2.1667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5, "angle": 18.77}, {"time": 2.8333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.9667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "angle": 18.77}, {"time": 3.5, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.8333, "angle": 18.77}, {"time": 4.1667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.3, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.5, "angle": 18.77}, {"time": 4.8333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.9667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.1667, "angle": 18.77}, {"time": 5.5, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.6333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.8333, "angle": 18.77}, {"time": 6.1667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.3, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "angle": 18.77}, {"time": 6.8333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.9667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.1667, "angle": 18.77}, {"time": 7.5, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 7.6333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.8333, "angle": 18.77}, {"time": 8.1667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.3, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.5, "angle": 18.77}, {"time": 8.8333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.9667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.1667, "angle": 18.77}, {"time": 9.5, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.6333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.8333, "angle": 18.77}, {"time": 10.1667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10.3, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.5, "angle": 18.77, "curve": "stepped"}, {"time": 10.6, "angle": 18.77, "curve": "stepped"}, {"time": 10.6333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.8333, "angle": 18.77}, {"time": 11.1667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 11.5, "angle": 18.77}, {"time": 11.8333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.9667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 12.1667, "angle": 18.77}, {"time": 12.5, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12.6333, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 12.8333, "angle": 18.77}, {"time": 13.1667, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 13.3, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 13.5, "angle": 18.77}, {"time": 13.8333, "angle": -58.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 13.9667, "angle": -29.84, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 14.1667, "angle": 18.77}, {"time": 14.3333, "angle": -19.68}]}, "bone20": {"rotate": [{"angle": -3.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.9667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.3, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.9667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.6333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.3, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.9667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 7.6333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.3, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.5, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.9667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.6333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.8333, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10.3, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.5, "curve": "stepped"}, {"time": 10.6, "curve": "stepped"}, {"time": 10.6333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.8333, "curve": 0.25, "c3": 0.75}, {"time": 11.1667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 11.5, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.9667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 12.1667, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12.6333, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 12.8333, "curve": 0.25, "c3": 0.75}, {"time": 13.1667, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 13.3, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 13.5, "curve": 0.25, "c3": 0.75}, {"time": 13.8333, "angle": -6.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 13.9667, "angle": -4.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 14.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 14.3333, "angle": -3.28}]}, "bone19": {"rotate": [{"angle": -4.97, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.3, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.9667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.6333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.3, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.9667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.6333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.3, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.9667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.6333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.7667, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.3, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.4333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.9667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.6333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 10.1, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.3, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.4333, "curve": "stepped"}, {"time": 10.6, "curve": "stepped"}, {"time": 10.6333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.7667, "curve": 0.25, "c3": 0.75}, {"time": 11.1, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 11.4333, "curve": 0.25, "c3": 0.75}, {"time": 11.7667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.9667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.1, "curve": 0.25, "c3": 0.75}, {"time": 12.4333, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.6333, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.7667, "curve": 0.25, "c3": 0.75}, {"time": 13.1, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 13.3, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 13.4333, "curve": 0.25, "c3": 0.75}, {"time": 13.7667, "angle": -6.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 13.9667, "angle": -2.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 14.1, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 14.3333, "angle": -4.97}]}, "bone18": {"rotate": [{"angle": -6.28, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.9667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.3, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.9667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.6333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.3, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.9667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.0333, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.6333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.7, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.3, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.9667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.0333, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 7.6333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.7, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.3, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.3667, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.9667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.0333, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.6333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10.3, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.3667, "curve": "stepped"}, {"time": 10.6, "curve": "stepped"}, {"time": 10.6333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 11.0333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.3667, "curve": 0.25, "c3": 0.75}, {"time": 11.7, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.9667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12.0333, "curve": 0.25, "c3": 0.75}, {"time": 12.3667, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12.6333, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12.7, "curve": 0.25, "c3": 0.75}, {"time": 13.0333, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 13.3, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 13.3667, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "angle": -6.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 13.9667, "angle": -0.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 14.0333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 14.3333, "angle": -6.28}]}, "xiezi2": {"translate": [{"x": 196.88, "y": -140.48}, {"time": 0.3667, "x": 187.71, "y": -143.87, "curve": "stepped"}, {"time": 1.2667, "x": 187.71, "y": -143.87}, {"time": 4.3667, "x": 73.06, "y": -138.81, "curve": "stepped"}, {"time": 5, "x": 73.06, "y": -138.81}, {"time": 7.5, "x": -8.43, "y": -169.16, "curve": "stepped"}, {"time": 8.3333, "x": -8.43, "y": -169.16}, {"time": 10.6, "x": -8.43, "y": -111.76, "curve": "stepped"}, {"time": 10.6333, "x": 289.43, "y": -106.22}, {"time": 14.3333, "x": 196.88, "y": -140.48}], "scale": [{"x": -1}]}, "bone26": {"rotate": [{"angle": 26.46}, {"time": 0.6667, "angle": -12.54}, {"time": 1.3333, "angle": 26.46}, {"time": 2, "angle": -12.54}, {"time": 2.6667, "angle": 26.46}, {"time": 3.3333, "angle": -12.54}, {"time": 4, "angle": 26.46}, {"time": 4.6667, "angle": -12.54}, {"time": 5.3333, "angle": 26.46}, {"time": 6, "angle": -12.54}, {"time": 6.6667, "angle": 26.46}, {"time": 7.3333, "angle": -12.54}, {"time": 8, "angle": 26.46}, {"time": 8.6667, "angle": -12.54}, {"time": 9.3333, "angle": 26.46}, {"time": 10, "angle": -12.54}, {"time": 10.6667, "angle": 26.46}, {"time": 11.3333, "angle": -12.54}, {"time": 12, "angle": 26.46}]}, "bone22": {"rotate": [{"angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": -25.64}, {"time": 0.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.9333, "angle": -25.64}, {"time": 1.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.6, "angle": -25.64}, {"time": 1.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.2667, "angle": -25.64}, {"time": 2.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.9333, "angle": -25.64}, {"time": 3.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.6, "angle": -25.64}, {"time": 3.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.2667, "angle": -25.64}, {"time": 4.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.9333, "angle": -25.64}, {"time": 5.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.6, "angle": -25.64}, {"time": 5.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.2667, "angle": -25.64}, {"time": 6.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.9333, "angle": -25.64}, {"time": 7.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.6, "angle": -25.64}, {"time": 7.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 8.2667, "angle": -25.64}, {"time": 8.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 8.9333, "angle": -25.64}, {"time": 9.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.6, "angle": -25.64}, {"time": 9.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 10.2667, "angle": -25.64}, {"time": 10.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 10.9333, "angle": -25.64}, {"time": 11.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 11.6, "angle": -25.64}, {"time": 11.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 12, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 12.2667, "angle": -25.64}]}, "bone23": {"rotate": [{"angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "angle": 27.13}]}, "bone25": {"rotate": [{"angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 27.13, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -25.64, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "angle": 27.13}]}, "bone24": {"rotate": [{"angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": -25.64}, {"time": 0.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.9333, "angle": -25.64}, {"time": 1.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.6, "angle": -25.64}, {"time": 1.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.2667, "angle": -25.64}, {"time": 2.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.9333, "angle": -25.64}, {"time": 3.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.6, "angle": -25.64}, {"time": 3.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.2667, "angle": -25.64}, {"time": 4.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.9333, "angle": -25.64}, {"time": 5.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.6, "angle": -25.64}, {"time": 5.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.2667, "angle": -25.64}, {"time": 6.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.9333, "angle": -25.64}, {"time": 7.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.6, "angle": -25.64}, {"time": 7.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 8.2667, "angle": -25.64}, {"time": 8.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 8.9333, "angle": -25.64}, {"time": 9.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.6, "angle": -25.64}, {"time": 9.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 10.2667, "angle": -25.64}, {"time": 10.6, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.6667, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 10.9333, "angle": -25.64}, {"time": 11.2667, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.3333, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 11.6, "angle": -25.64}, {"time": 11.9333, "angle": 27.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 12, "angle": 20.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 12.2667, "angle": -25.64}]}, "bone27": {"rotate": [{"angle": -40.26}, {"time": 0.6667, "angle": 14.52}, {"time": 1.3333, "angle": -40.26}, {"time": 2, "angle": 14.52}, {"time": 2.6667, "angle": -40.26}, {"time": 3.3333, "angle": 14.52}, {"time": 4, "angle": -40.26}, {"time": 4.6667, "angle": 14.52}, {"time": 5.3333, "angle": -40.26}, {"time": 6, "angle": 14.52}, {"time": 6.6667, "angle": -40.26}, {"time": 7.3333, "angle": 14.52}, {"time": 8, "angle": -40.26}, {"time": 8.6667, "angle": 14.52}, {"time": 9.3333, "angle": -40.26}, {"time": 10, "angle": 14.52}, {"time": 10.6667, "angle": -40.26}, {"time": 11.3333, "angle": 14.52}, {"time": 12, "angle": -40.26}]}, "pangxie": {"translate": [{"x": -19.08, "y": -43.1}, {"time": 1.4667, "x": -2.52, "y": 0.32, "curve": "stepped"}, {"time": 1.8333, "x": -2.52, "y": 0.32}, {"time": 6.1, "x": 125.84, "y": 35.91}, {"time": 8.1667, "x": 186.23, "y": 55.54}, {"time": 8.8, "x": 205.03, "y": 60.04}, {"time": 10.2, "x": 243.22, "y": 77.31}, {"time": 12.2, "x": 288.01, "y": 109.79}]}}}, "animation2": {"slots": {"bf2": {"attachment": [{"name": null}]}, "kapaixg2": {"attachment": [{"name": null}]}, "sd4": {"attachment": [{"name": null}]}, "sd12": {"color": [{"time": 4.3667, "color": "ffffffff"}, {"time": 4.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 6.9333, "color": "ffffff00"}, {"time": 7.5, "color": "ffffffff"}]}, "sd6": {"color": [{"time": 8.1333, "color": "ffffffff"}, {"time": 8.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 12.1333, "color": "ffffff00"}, {"time": 12.8, "color": "ffffffff"}]}, "sd": {"attachment": [{"name": null}]}, "bf": {"attachment": [{"name": null}]}, "lg2": {"color": [{"time": 3.9333, "color": "ffffffff"}, {"time": 4.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 5.3333, "color": "ffffff00"}, {"time": 5.9, "color": "ffffffff"}]}, "xian1": {"attachment": [{"name": null}]}, "yz2": {"color": [{"time": 4.3667, "color": "ffffffff"}, {"time": 4.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 6.9333, "color": "ffffff00"}, {"time": 7.5, "color": "ffffffff"}]}, "sd8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 4.0667, "color": "ffffff00"}, {"time": 4.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 9.9667, "color": "ffffffff"}, {"time": 10.4, "color": "ffffff00"}]}, "zz": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 4.0667, "color": "ffffff00"}, {"time": 4.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 9.9667, "color": "ffffffff"}, {"time": 10.4, "color": "ffffff00"}]}, "zz2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.8, "color": "ffffffff", "curve": "stepped"}, {"time": 7.2, "color": "ffffffff"}, {"time": 7.6333, "color": "ffffff00"}]}, "yz": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 8.3, "color": "ffffffff"}, {"time": 8.8667, "color": "ffffff00"}]}, "xz2": {"attachment": [{"name": null}]}, "xian2": {"attachment": [{"name": null}]}, "sd7": {"color": [{"time": 3.9333, "color": "ffffffff"}, {"time": 4.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 5.3333, "color": "ffffff00"}, {"time": 5.9, "color": "ffffffff"}]}, "lg": {"color": [{"time": 8.1333, "color": "ffffffff"}, {"time": 8.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 12.1333, "color": "ffffff00"}, {"time": 12.8, "color": "ffffffff"}]}, "kapaixg1": {"attachment": [{"name": null}]}, "sd3": {"attachment": [{"name": null}]}, "sd2": {"attachment": [{"name": null}]}, "sd10": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 12.0667, "color": "ffffffff"}, {"time": 13.4667, "color": "ffffff00"}]}, "subf": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 12.0667, "color": "ffffffff"}, {"time": 13.0333, "color": "ffffff00"}]}, "sd9": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.8, "color": "ffffffff", "curve": "stepped"}, {"time": 7.2, "color": "ffffffff"}, {"time": 7.6333, "color": "ffffff00"}]}, "xz": {"attachment": [{"name": null}]}, "sd5": {"attachment": [{"name": null}]}, "px": {"attachment": [{"name": null}]}, "sd11": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 8.3, "color": "ffffffff"}, {"time": 8.8667, "color": "ffffff00"}]}}, "bones": {"bone29": {"translate": [{"x": 0.01, "y": 0.5}, {"time": 0.1333}, {"time": 0.3, "x": 0.01, "y": 0.62}, {"time": 0.4667}, {"time": 0.6333, "x": 0.01, "y": 0.62}, {"time": 0.8}, {"time": 0.9667, "x": 0.01, "y": 0.62}, {"time": 1.1333}, {"time": 1.3, "x": 0.01, "y": 0.62}, {"time": 1.4667}, {"time": 1.6333, "x": 0.01, "y": 0.62}, {"time": 1.8}, {"time": 1.9667, "x": 0.01, "y": 0.62}, {"time": 2.1333}, {"time": 2.3, "x": 0.01, "y": 0.62}, {"time": 2.4667}, {"time": 2.6333, "x": 0.01, "y": 0.62}, {"time": 2.8}, {"time": 2.9667, "x": 0.01, "y": 0.62}, {"time": 3.1333}, {"time": 3.3, "x": 0.01, "y": 0.62}, {"time": 3.4667, "curve": "stepped"}, {"time": 5.1333}, {"time": 5.3, "x": 0.01, "y": 0.62}, {"time": 5.4667}, {"time": 5.6333, "x": 0.01, "y": 0.62}, {"time": 5.8}, {"time": 5.9667, "x": 0.01, "y": 0.62}, {"time": 6.1333}, {"time": 6.3, "x": 0.01, "y": 0.62}, {"time": 6.4667}, {"time": 6.6333, "x": 0.01, "y": 0.62}, {"time": 6.8}, {"time": 6.9667, "x": 0.01, "y": 0.62}, {"time": 7.1333}, {"time": 7.3, "x": 0.01, "y": 0.62}, {"time": 7.4667}, {"time": 7.6333, "x": 0.01, "y": 0.62}, {"time": 7.8}, {"time": 7.9667, "x": 0.01, "y": 0.62}, {"time": 8.1333}, {"time": 8.3, "x": 0.01, "y": 0.62}, {"time": 8.4667}, {"time": 8.6333, "x": 0.01, "y": 0.62}, {"time": 8.8}, {"time": 8.9667, "x": 0.01, "y": 0.62}, {"time": 9.1333}, {"time": 9.3, "x": 0.01, "y": 0.62}, {"time": 9.4667}, {"time": 9.6333, "x": 0.01, "y": 0.62}, {"time": 9.8}, {"time": 9.9667, "x": 0.01, "y": 0.62}, {"time": 10.1333}, {"time": 10.3, "x": 0.01, "y": 0.62}, {"time": 10.4667}, {"time": 10.6333, "x": 0.01, "y": 0.62}, {"time": 10.8}, {"time": 10.9667, "x": 0.01, "y": 0.62}, {"time": 11.1333}, {"time": 11.3, "x": 0.01, "y": 0.62}, {"time": 11.4667}, {"time": 11.6333, "x": 0.01, "y": 0.62}, {"time": 11.8}, {"time": 11.9667, "x": 0.01, "y": 0.62}, {"time": 12.1333, "curve": 0.25, "c3": 0.75}, {"time": 12.3, "x": 0.01, "y": 0.62}, {"time": 12.4667}, {"time": 12.6333, "x": 0.01, "y": 0.62}, {"time": 12.8}, {"time": 12.9667, "x": 0.01, "y": 0.62}, {"time": 13.1333}, {"time": 13.3, "x": 0.01, "y": 0.62}, {"time": 13.4667}, {"time": 13.6333, "x": 0.01, "y": 0.62}, {"time": 13.8}, {"time": 13.9667, "x": 0.01, "y": 0.62}, {"time": 14.1333}, {"time": 14.3, "x": 0.01, "y": 0.62}, {"time": 14.4667}, {"time": 14.6333, "x": 0.01, "y": 0.62}, {"time": 14.8}, {"time": 14.9667, "x": 0.01, "y": 0.62}, {"time": 15.1333}, {"time": 15.3, "x": 0.01, "y": 0.62}, {"time": 15.4667}, {"time": 15.6333, "x": 0.01, "y": 0.62}, {"time": 15.8}, {"time": 15.9667, "x": 0.01, "y": 0.62}, {"time": 16, "x": 0.01, "y": 0.5}]}, "bone30": {"rotate": [{"angle": 4.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 11.1333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 11.8, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 12.1333}, {"time": 12.4667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 13.1333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 13.4667, "curve": 0.25, "c3": 0.75}, {"time": 13.8, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 14.1333, "curve": 0.25, "c3": 0.75}, {"time": 14.4667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 14.8, "curve": 0.25, "c3": 0.75}, {"time": 15.1333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 15.4667, "curve": 0.25, "c3": 0.75}, {"time": 15.8, "angle": 12.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 16, "angle": 4.57}]}, "bone31": {"rotate": [{"angle": 11.18}, {"time": 0.0333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3667}, {"time": 0.7, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.8, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.0333}, {"time": 1.3667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.4667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.7}, {"time": 2.0333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.1333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.3667}, {"time": 2.7, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.8, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 3.0333}, {"time": 3.3667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.4667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 3.7}, {"time": 4.0333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.1333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.3667}, {"time": 4.7, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.8, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 5.0333}, {"time": 5.3667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.4667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 5.7}, {"time": 6.0333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.1333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.3667}, {"time": 6.7, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.8, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 7.0333}, {"time": 7.3667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 7.4667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 7.7}, {"time": 8.0333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8.1333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 8.3667}, {"time": 8.7, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8.8, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 9.0333}, {"time": 9.3667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 9.4667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 9.7}, {"time": 10.0333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 10.1333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 10.3667}, {"time": 10.7, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 10.8, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 11.0333}, {"time": 11.3667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 11.4667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 11.7}, {"time": 12.0333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 12.1333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 12.3667}, {"time": 12.7, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 12.8, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 13.0333}, {"time": 13.3667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 13.4667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 13.7}, {"time": 14.0333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 14.1333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 14.3667}, {"time": 14.7, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 14.8, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 15.0333}, {"time": 15.3667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 15.4667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 15.7}, {"time": 16, "angle": 11.18}]}, "bone28": {"translate": [{"x": -73.97, "y": -49.21}, {"time": 3.3333, "x": -10.64, "y": -72.96, "curve": "stepped"}, {"time": 4.8667, "x": -10.64, "y": -72.96}, {"time": 6.9, "x": 20.34, "y": -52.27}, {"time": 8.9667, "x": 35.06, "y": -21.22, "curve": "stepped"}, {"time": 12.1, "x": 35.06, "y": -21.22}, {"time": 12.1333, "x": -147.44, "y": -21.66}, {"time": 16, "x": -73.97, "y": -49.21}]}, "bone35": {"rotate": [{"angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333}, {"time": 0.5667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9}, {"time": 1.2333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.5667}, {"time": 1.9, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.2333}, {"time": 2.5667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.9}, {"time": 3.2333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 3.5667}, {"time": 3.9, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.2333}, {"time": 4.5667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.6667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.9}, {"time": 5.2333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 5.5667}, {"time": 5.9, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.2333}, {"time": 6.5667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.9}, {"time": 7.2333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 7.3333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 7.5667}, {"time": 7.9, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 8.2333}, {"time": 8.5667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8.6667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 8.9}, {"time": 9.2333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 9.3333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 9.5667}, {"time": 9.9, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 10, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 10.2333}, {"time": 10.5667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 10.6667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 10.9}, {"time": 11.2333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 11.3333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 11.5667}, {"time": 11.9, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 12, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 12.2333}, {"time": 12.5667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 12.6667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 12.9}, {"time": 13.2333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 13.3333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 13.5667}, {"time": 13.9, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 14, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 14.2333}, {"time": 14.5667, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 14.6667, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 14.9}, {"time": 15.2333, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 15.3333, "angle": 9.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 15.5667}, {"time": 15.9, "angle": 12.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 16, "angle": 9.41}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 12, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "curve": 0.25, "c3": 0.75}, {"time": 13, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "curve": 0.25, "c3": 0.75}, {"time": 13.6667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 14, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 14.6667, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 15.3333, "curve": 0.25, "c3": 0.75}, {"time": 15.6667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 16}]}, "bone33": {"translate": [{}, {"time": 0.1667, "x": 0.01, "y": 0.62}, {"time": 0.3333}, {"time": 0.5, "x": 0.01, "y": 0.62}, {"time": 0.6667}, {"time": 0.8333, "x": 0.01, "y": 0.62}, {"time": 1}, {"time": 1.1667, "x": 0.01, "y": 0.62}, {"time": 1.3333}, {"time": 1.5, "x": 0.01, "y": 0.62}, {"time": 1.6667}, {"time": 1.8333, "x": 0.01, "y": 0.62}, {"time": 2}, {"time": 2.1667, "x": 0.01, "y": 0.62}, {"time": 2.3333}, {"time": 2.5, "x": 0.01, "y": 0.62}, {"time": 2.6667}, {"time": 2.8333, "x": 0.01, "y": 0.62}, {"time": 3}, {"time": 3.1667, "x": 0.01, "y": 0.62}, {"time": 3.3333}, {"time": 3.5, "x": 0.01, "y": 0.62}, {"time": 3.6667}, {"time": 3.8333, "x": 0.01, "y": 0.62}, {"time": 4}, {"time": 4.1667, "x": 0.01, "y": 0.62}, {"time": 4.3333}, {"time": 4.5, "x": 0.01, "y": 0.62}, {"time": 4.6667}, {"time": 4.8333, "x": 0.01, "y": 0.62}, {"time": 5}, {"time": 5.1667, "x": 0.01, "y": 0.62}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 0.01, "y": 0.62}, {"time": 5.6667}, {"time": 5.8333, "x": 0.01, "y": 0.62}, {"time": 6}, {"time": 6.1667, "x": 0.01, "y": 0.62}, {"time": 6.3333}, {"time": 6.5, "x": 0.01, "y": 0.62}, {"time": 6.6667}, {"time": 6.8333, "x": 0.01, "y": 0.62}, {"time": 7}, {"time": 7.1667, "x": 0.01, "y": 0.62}, {"time": 7.3333}, {"time": 7.5, "x": 0.01, "y": 0.62}, {"time": 7.6667}, {"time": 7.8333, "x": 0.01, "y": 0.62}, {"time": 8}, {"time": 8.1667, "x": 0.01, "y": 0.62}, {"time": 8.3333}, {"time": 8.5, "x": 0.01, "y": 0.62}, {"time": 8.6667}, {"time": 8.8333, "x": 0.01, "y": 0.62}, {"time": 9}, {"time": 9.1667, "x": 0.01, "y": 0.62}, {"time": 9.3333}, {"time": 9.5, "x": 0.01, "y": 0.62}, {"time": 10.6667}, {"time": 10.8333, "x": 0.01, "y": 0.62}, {"time": 11}, {"time": 11.1667, "x": 0.01, "y": 0.62}, {"time": 11.3333}, {"time": 11.5, "x": 0.01, "y": 0.62}, {"time": 11.6667}, {"time": 11.8333, "x": 0.01, "y": 0.62}, {"time": 12}, {"time": 12.1667, "x": 0.01, "y": 0.62}, {"time": 12.3333}, {"time": 12.5, "x": 0.01, "y": 0.62}, {"time": 12.6667}, {"time": 12.8333, "x": 0.01, "y": 0.62}, {"time": 13}, {"time": 13.1667, "x": 0.01, "y": 0.62}, {"time": 13.3333}, {"time": 13.5, "x": 0.01, "y": 0.62}, {"time": 13.6667}, {"time": 13.8333, "x": 0.01, "y": 0.62}, {"time": 14}, {"time": 14.1667, "x": 0.01, "y": 0.62}, {"time": 15.6667}, {"time": 15.8333, "x": 0.01, "y": 0.62}, {"time": 16}]}, "bone32": {"translate": [{"x": 26.13, "y": -21.38}, {"time": 4.5667, "x": -85.2, "y": -21.38}, {"time": 5.3333, "x": -30.69, "y": 71.61}, {"time": 9.5667, "x": -12.54, "y": 9.24, "curve": "stepped"}, {"time": 10.5667, "x": -12.54, "y": 9.24}, {"time": 14.1, "x": 38.32, "y": -21.38, "curve": "stepped"}, {"time": 15.5, "x": 38.32, "y": -21.38}, {"time": 16, "x": 26.13, "y": -21.38}], "scale": [{"x": -1, "curve": "stepped"}, {"time": 4.5667, "x": -1}, {"time": 5.3, "curve": "stepped"}, {"time": 15.5333, "curve": "stepped"}, {"time": 15.5667, "x": -1}]}, "bone38": {"rotate": [{"time": 3.7333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.8, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.9667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.0333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.2, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.2667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.4333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.7333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.9, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.1333, "angle": 4.29, "curve": "stepped"}, {"time": 5.7, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.7667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.9333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.2333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.4, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.4667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.7, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.8667, "angle": 4.29, "curve": "stepped"}, {"time": 7.3667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.4333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.6, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.8333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.9, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.0667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.1333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.3, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 8.5, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.5333, "angle": 4.29, "curve": "stepped"}, {"time": 9.0667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.1333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.3667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.5333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.6, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 9.7333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.7667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.8333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.0667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 10.2, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.2333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.3, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": 38.35}], "translate": [{"time": 3.7333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.9667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.0333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.2, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.4333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.7333, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.9, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.1333, "x": -0.01, "y": 0.31, "curve": "stepped"}, {"time": 5.7, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.9333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.4, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.8667, "x": -0.01, "y": 0.31, "curve": "stepped"}, {"time": 7.3667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.4333, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.6, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.8333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.0667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.3, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3667, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.5333, "x": -0.01, "y": 0.31, "curve": "stepped"}, {"time": 9.0667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.1333, "curve": 0.25, "c3": 0.75}, {"time": 9.2333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.3667, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.5333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.7667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.8333, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.0667, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.2333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.3, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "x": -0.02, "y": 0.62}]}, "bone39": {"rotate": [{"time": 3.7333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": -29.78, "curve": "stepped"}, {"time": 5.7, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "angle": -29.78, "curve": "stepped"}, {"time": 7.3667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "angle": -29.78, "curve": "stepped"}, {"time": 9.0667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 9.3, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 9.9, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 10.3667, "angle": 38.35}], "translate": [{"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "curve": "stepped"}, {"time": 5.7, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "curve": "stepped"}, {"time": 7.3667, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "curve": 0.25, "c3": 0.75}, {"time": 8.4, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": "stepped"}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 9.3, "curve": 0.25, "c3": 0.75}, {"time": 9.4, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.1, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -0.02, "y": 0.62}]}, "bone36": {"rotate": [{"time": 4.0667, "angle": -2.41}, {"time": 4.1667, "angle": -3.16}, {"time": 4.3, "angle": -2.41}, {"time": 4.4, "angle": -3.16}, {"time": 4.5333, "angle": -2.41}, {"time": 4.6333, "angle": -3.16}, {"time": 4.7667, "angle": -2.41}, {"time": 4.8667, "angle": -3.16}, {"time": 5, "angle": -2.41}, {"time": 5.1, "angle": -3.16}, {"time": 5.2333, "angle": -2.41}, {"time": 5.3333, "angle": -3.16}, {"time": 5.6667, "angle": -2.41}, {"time": 5.7667, "angle": -3.16}, {"time": 5.9, "angle": -2.41}, {"time": 6, "angle": -3.16}, {"time": 6.1333, "angle": -2.41}, {"time": 6.2333, "angle": -3.16}, {"time": 6.3667, "angle": -2.41}, {"time": 6.4667, "angle": -3.16}, {"time": 6.6, "angle": -2.41}, {"time": 6.7, "angle": -3.16}, {"time": 6.8333, "angle": -2.41}, {"time": 6.9333, "angle": -3.16}, {"time": 7.4333, "angle": -2.41}, {"time": 7.5333, "angle": -3.16}, {"time": 7.6667, "angle": -2.41}, {"time": 7.7667, "angle": -3.16}, {"time": 7.9, "angle": -2.41}, {"time": 8, "angle": -3.16}, {"time": 8.1333, "angle": -2.41}, {"time": 8.2333, "angle": -3.16}, {"time": 8.3667, "angle": -2.41}, {"time": 8.4667, "angle": -3.16}, {"time": 8.6, "angle": -2.41}, {"time": 8.7, "angle": -3.16}, {"time": 9.1333, "angle": -2.41}, {"time": 9.2333, "angle": -3.16}, {"time": 9.3667, "angle": -2.41}, {"time": 9.4667, "angle": -3.16}, {"time": 9.6, "angle": -2.41}, {"time": 9.7, "angle": -3.16}, {"time": 9.8333, "angle": -2.41}, {"time": 9.9333, "angle": -3.16}, {"time": 10.0667, "angle": -2.41}, {"time": 10.1667, "angle": -3.16}, {"time": 10.3, "angle": -2.41}, {"time": 10.4, "angle": -3.16}], "translate": [{"time": 4.0667}, {"time": 4.1667, "y": 0.38}, {"time": 4.3}, {"time": 4.4, "y": 0.38}, {"time": 4.5333}, {"time": 4.6333, "y": 0.38}, {"time": 4.7667}, {"time": 4.8667, "y": 0.38}, {"time": 5}, {"time": 5.1, "y": 0.38}, {"time": 5.2333}, {"time": 5.3333, "y": 0.38}, {"time": 5.6667}, {"time": 5.7667, "y": 0.38}, {"time": 5.9}, {"time": 6, "y": 0.38}, {"time": 6.1333}, {"time": 6.2333, "y": 0.38}, {"time": 6.3667}, {"time": 6.4667, "y": 0.38}, {"time": 6.6}, {"time": 6.7, "y": 0.38}, {"time": 6.8333}, {"time": 6.9333, "y": 0.38}, {"time": 7.4333}, {"time": 7.5333, "y": 0.38}, {"time": 7.6667}, {"time": 7.7667, "y": 0.38}, {"time": 7.9}, {"time": 8, "y": 0.38}, {"time": 8.1333}, {"time": 8.2333, "y": 0.38}, {"time": 8.3667}, {"time": 8.4667, "y": 0.38}, {"time": 8.6}, {"time": 8.7, "y": 0.38}, {"time": 9.1333}, {"time": 9.2333, "y": 0.38}, {"time": 9.3667}, {"time": 9.4667, "y": 0.38}, {"time": 9.6}, {"time": 9.7, "y": 0.38}, {"time": 9.8333}, {"time": 9.9333, "y": 0.38}, {"time": 10.0667}, {"time": 10.1667, "y": 0.38}, {"time": 10.3}, {"time": 10.4, "y": 0.38}]}, "bone40": {"translate": [{"time": 3.8667}, {"time": 5.1333, "x": -77.2, "y": -2.87, "curve": "stepped"}, {"time": 5.7, "x": -77.2, "y": -2.87}, {"time": 6.8667, "x": -39.05, "y": -33.61, "curve": "stepped"}, {"time": 7.4, "x": -39.05, "y": -33.61}, {"time": 8.5333, "x": 13.73, "y": -0.94, "curve": "stepped"}, {"time": 9.0667, "x": 13.73, "y": -0.94}, {"time": 10.3333, "x": 69.89, "y": -24.46}], "scale": [{"time": 5.6667, "curve": "stepped"}, {"time": 5.7, "x": -1}]}, "bone42": {"rotate": [{"time": 1.3, "angle": -2.41}, {"time": 1.4, "angle": -3.16}, {"time": 1.5333, "angle": -2.41}, {"time": 1.6333, "angle": -3.16}, {"time": 1.7667, "angle": -2.41}, {"time": 1.8667, "angle": -3.16}, {"time": 2, "angle": -2.41}, {"time": 2.1, "angle": -3.16}, {"time": 2.2333, "angle": -2.41}, {"time": 2.3333, "angle": -3.16}, {"time": 2.4667, "angle": -2.41}, {"time": 2.5667, "angle": -3.16}, {"time": 2.9, "angle": -2.41}, {"time": 3, "angle": -3.16}, {"time": 3.1333, "angle": -2.41}, {"time": 3.2333, "angle": -3.16}, {"time": 3.3667, "angle": -2.41}, {"time": 3.4667, "angle": -3.16}, {"time": 3.6, "angle": -2.41}, {"time": 3.7, "angle": -3.16}, {"time": 3.8333, "angle": -2.41}, {"time": 3.9333, "angle": -3.16}, {"time": 4.0667, "angle": -2.41}, {"time": 4.1667, "angle": -3.16}, {"time": 4.6667, "angle": -2.41}, {"time": 4.7667, "angle": -3.16}, {"time": 4.9, "angle": -2.41}, {"time": 5, "angle": -3.16}, {"time": 5.1333, "angle": -2.41}, {"time": 5.2333, "angle": -3.16}, {"time": 5.3667, "angle": -2.41}, {"time": 5.4667, "angle": -3.16}, {"time": 5.6, "angle": -2.41}, {"time": 5.7, "angle": -3.16}, {"time": 5.8333, "angle": -2.41}, {"time": 5.9333, "angle": -3.16}, {"time": 6.3667, "angle": -2.41}, {"time": 6.4667, "angle": -3.16}, {"time": 6.6, "angle": -2.41}, {"time": 6.7, "angle": -3.16}, {"time": 6.8333, "angle": -2.41}, {"time": 6.9333, "angle": -3.16}, {"time": 7.0667, "angle": -2.41}, {"time": 7.1667, "angle": -3.16}, {"time": 7.3, "angle": -2.41}, {"time": 7.4, "angle": -3.16}, {"time": 7.5333, "angle": -2.41}, {"time": 7.6333, "angle": -3.16}], "translate": [{"time": 1.3}, {"time": 1.4, "y": 0.38}, {"time": 1.5333}, {"time": 1.6333, "y": 0.38}, {"time": 1.7667}, {"time": 1.8667, "y": 0.38}, {"time": 2}, {"time": 2.1, "y": 0.38}, {"time": 2.2333}, {"time": 2.3333, "y": 0.38}, {"time": 2.4667}, {"time": 2.5667, "y": 0.38}, {"time": 2.9}, {"time": 3, "y": 0.38}, {"time": 3.1333}, {"time": 3.2333, "y": 0.38}, {"time": 3.3667}, {"time": 3.4667, "y": 0.38}, {"time": 3.6}, {"time": 3.7, "y": 0.38}, {"time": 3.8333}, {"time": 3.9333, "y": 0.38}, {"time": 4.0667}, {"time": 4.1667, "y": 0.38}, {"time": 4.6667}, {"time": 4.7667, "y": 0.38}, {"time": 4.9}, {"time": 5, "y": 0.38}, {"time": 5.1333}, {"time": 5.2333, "y": 0.38}, {"time": 5.3667}, {"time": 5.4667, "y": 0.38}, {"time": 5.6}, {"time": 5.7, "y": 0.38}, {"time": 5.8333}, {"time": 5.9333, "y": 0.38}, {"time": 6.3667}, {"time": 6.4667, "y": 0.38}, {"time": 6.6}, {"time": 6.7, "y": 0.38}, {"time": 6.8333}, {"time": 6.9333, "y": 0.38}, {"time": 7.0667}, {"time": 7.1667, "y": 0.38}, {"time": 7.3}, {"time": 7.4, "y": 0.38}, {"time": 7.5333}, {"time": 7.6333, "y": 0.38}]}, "bone44": {"rotate": [{"time": 0.9667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.7333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.9, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3667, "angle": 4.29, "curve": "stepped"}, {"time": 2.9333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.4, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.7, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.8667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.1, "angle": 4.29, "curve": "stepped"}, {"time": 4.6, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.0667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.1333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.7667, "angle": 4.29, "curve": "stepped"}, {"time": 6.3, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.5333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.6, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.7667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.8333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.0667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.2333, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": 38.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4667, "angle": 4.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.5333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 38.35}], "translate": [{"time": 0.9667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.9, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3667, "x": -0.01, "y": 0.31, "curve": "stepped"}, {"time": 2.9333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.4, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.8667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.1, "x": -0.01, "y": 0.31, "curve": "stepped"}, {"time": 4.6, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.0667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.1333, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.5333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.7667, "x": -0.01, "y": 0.31, "curve": "stepped"}, {"time": 6.3, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.5333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.7667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.0667, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.2333, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4667, "x": -0.01, "y": 0.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.5333, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "x": -0.02, "y": 0.62}]}, "bone45": {"rotate": [{"time": 0.9667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -29.78, "curve": "stepped"}, {"time": 2.9333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "angle": -29.78, "curve": "stepped"}, {"time": 4.6, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -29.78, "curve": "stepped"}, {"time": 6.3, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 38.35, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": -29.78, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 38.35}], "translate": [{"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": "stepped"}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "curve": "stepped"}, {"time": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -0.02, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "x": -0.02, "y": 0.62}]}, "bone41": {"translate": [{"time": 1.1}, {"time": 2.3667, "x": -77.2, "y": -2.87, "curve": "stepped"}, {"time": 2.9333, "x": -77.2, "y": -2.87}, {"time": 4.1, "x": -39.05, "y": -33.61, "curve": "stepped"}, {"time": 4.6333, "x": -39.05, "y": -33.61}, {"time": 5.7667, "x": 13.73, "y": -0.94, "curve": "stepped"}, {"time": 6.3, "x": 13.73, "y": -0.94}, {"time": 7.5667, "x": 69.89, "y": -24.46}], "scale": [{"time": 2.9, "curve": "stepped"}, {"time": 2.9333, "x": -1}]}, "bone5": {"translate": [{"x": -347.36}]}, "bone4": {"translate": [{"x": -314.91}]}, "xiezi": {"translate": [{"x": -154.52}]}, "xiezi2": {"translate": [{"x": -116.28}]}, "bone48": {"rotate": [{"angle": 32.7, "curve": "stepped"}, {"time": 2.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 9.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 11.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 11.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 11.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 12.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 12.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 12.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 12.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 13.1333, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 13.4667, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 13.6333, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 13.8, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 13.9667, "angle": -54.84, "curve": 0.25, "c3": 0.75}, {"time": 14.1333, "angle": 32.7}]}, "bone47": {"rotate": [{"angle": -35.03, "curve": "stepped"}, {"time": 2.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 9.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 11.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 11.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 11.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 12.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 12.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 12.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 12.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 13.1333, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 13.4667, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 13.6333, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 13.8, "angle": -35.03, "curve": 0.25, "c3": 0.75}, {"time": 13.9667, "angle": 36.76, "curve": 0.25, "c3": 0.75}, {"time": 14.1333, "angle": -35.03}]}, "bone49": {"translate": [{"time": 2.1333}, {"time": 2.3, "y": -1.2}, {"time": 2.4667}, {"time": 2.6333, "y": -1.2}, {"time": 2.8}, {"time": 2.9667, "y": -1.2}, {"time": 3.1333}, {"time": 3.3, "y": -1.2}, {"time": 3.4667}, {"time": 3.6333, "y": -1.2}, {"time": 3.8}, {"time": 3.9667, "y": -1.2}, {"time": 4.1333}, {"time": 4.3, "y": -1.2}, {"time": 4.4667}, {"time": 4.6333, "y": -1.2}, {"time": 4.8}, {"time": 4.9667, "y": -1.2}, {"time": 5.1333}, {"time": 5.3, "y": -1.2}, {"time": 5.4667}, {"time": 5.6333, "y": -1.2}, {"time": 5.8}, {"time": 5.9667, "y": -1.2}, {"time": 6.1333}, {"time": 6.3, "y": -1.2}, {"time": 6.4667}, {"time": 6.6333, "y": -1.2}, {"time": 6.8}, {"time": 6.9667, "y": -1.2}, {"time": 7.1333}, {"time": 7.3, "y": -1.2}, {"time": 7.4667}, {"time": 7.6333, "y": -1.2}, {"time": 7.8}, {"time": 7.9667, "y": -1.2}, {"time": 8.1333}, {"time": 8.3, "y": -1.2}, {"time": 8.4667}, {"time": 8.6333, "y": -1.2}, {"time": 8.8}, {"time": 8.9667, "y": -1.2}, {"time": 9.1333}, {"time": 9.3, "y": -1.2}, {"time": 9.4667}, {"time": 9.6333, "y": -1.2}, {"time": 9.8}, {"time": 9.9667, "y": -1.2}, {"time": 10.1333}, {"time": 10.3, "y": -1.2}, {"time": 10.4667}, {"time": 10.6333, "y": -1.2}, {"time": 10.8}, {"time": 10.9667, "y": -1.2}, {"time": 11.1333}, {"time": 11.3, "y": -1.2}, {"time": 11.4667}, {"time": 11.6333, "y": -1.2}, {"time": 11.8}, {"time": 11.9667, "y": -1.2}, {"time": 12.1333}, {"time": 12.3, "y": -1.2}, {"time": 12.4667}, {"time": 12.6333, "y": -1.2}, {"time": 12.8}, {"time": 12.9667, "y": -1.2}, {"time": 13.1333}, {"time": 13.3, "y": -1.2}, {"time": 13.4667}, {"time": 13.6333, "y": -1.2}, {"time": 13.8}, {"time": 13.9667, "y": -1.2}, {"time": 14.1333}]}, "bone46": {"translate": [{"x": 38.84, "y": 61.03, "curve": "stepped"}, {"time": 2.1333, "x": 38.84, "y": 61.03}, {"time": 3.0667, "x": -10.08, "y": 50.26}, {"time": 3.8333, "x": -52.18, "y": 35.1}, {"time": 4.5333, "x": -80.85, "y": 49.49}, {"time": 5.0333, "x": -92.13, "y": 61.03, "curve": "stepped"}, {"time": 5.8333, "x": -92.13, "y": 61.03}, {"time": 8.1, "x": -201.04, "y": 29.58, "curve": "stepped"}, {"time": 8.8667, "x": -201.04, "y": 29.58}, {"time": 10.7667, "x": -98.94, "y": -33.18}, {"time": 13.4333, "x": 60.7, "y": -52.59}], "scale": [{"time": 8.8333, "curve": "stepped"}, {"time": 8.8667, "x": -1}]}, "bone51": {"translate": [{"time": 0.7667, "x": 1.39, "y": -0.39}, {"time": 0.9333, "x": -1.44, "y": 0.61}, {"time": 1.1, "x": 1.39, "y": -0.39}, {"time": 1.2667, "x": -1.44, "y": 0.61}, {"time": 1.4333, "x": 1.39, "y": -0.39}, {"time": 1.6, "x": -1.44, "y": 0.61}, {"time": 1.7667, "x": 1.39, "y": -0.39}, {"time": 1.9333, "x": -1.44, "y": 0.61}, {"time": 2.1, "x": 1.39, "y": -0.39}, {"time": 2.2667, "x": -1.44, "y": 0.61}, {"time": 2.4333, "x": 1.39, "y": -0.39}, {"time": 2.6, "x": -1.44, "y": 0.61}, {"time": 2.7667, "x": 1.39, "y": -0.39}, {"time": 2.9333, "x": -1.44, "y": 0.61}, {"time": 3.1, "x": 1.39, "y": -0.39}, {"time": 3.2667, "x": -1.44, "y": 0.61}, {"time": 3.4333, "x": 1.39, "y": -0.39}, {"time": 3.6, "x": -1.44, "y": 0.61}, {"time": 3.7667, "x": 1.39, "y": -0.39}, {"time": 3.9333, "x": -1.44, "y": 0.61}, {"time": 4.1, "x": 1.39, "y": -0.39}, {"time": 4.2667, "x": -1.44, "y": 0.61}, {"time": 4.4333, "x": 1.39, "y": -0.39}, {"time": 4.6, "x": -1.44, "y": 0.61}, {"time": 4.7667, "x": 1.39, "y": -0.39}, {"time": 4.9333, "x": -1.44, "y": 0.61}, {"time": 5.1, "x": 1.39, "y": -0.39}, {"time": 5.2667, "x": -1.44, "y": 0.61}, {"time": 5.4333, "x": 1.39, "y": -0.39}, {"time": 5.6, "x": -1.44, "y": 0.61}, {"time": 5.7667, "x": 1.39, "y": -0.39}, {"time": 5.9333, "x": -1.44, "y": 0.61}, {"time": 6.1, "x": 1.39, "y": -0.39}, {"time": 6.2667, "x": -1.44, "y": 0.61}, {"time": 6.4333, "x": 1.39, "y": -0.39}, {"time": 6.6, "x": -1.44, "y": 0.61}, {"time": 6.7667, "x": 1.39, "y": -0.39}, {"time": 6.9333, "x": -1.44, "y": 0.61}, {"time": 7.1, "x": 1.39, "y": -0.39}, {"time": 7.2667, "x": -1.44, "y": 0.61}, {"time": 7.4333, "x": 1.39, "y": -0.39}, {"time": 7.6, "x": -1.44, "y": 0.61}, {"time": 7.7667, "x": 1.39, "y": -0.39}, {"time": 7.9333, "x": -1.44, "y": 0.61}, {"time": 8.1, "x": 1.39, "y": -0.39}, {"time": 8.2667, "x": -1.44, "y": 0.61}, {"time": 8.4333, "x": 1.39, "y": -0.39}, {"time": 8.6, "x": -1.44, "y": 0.61}, {"time": 8.7667, "x": 1.39, "y": -0.39}, {"time": 8.9333, "x": -1.44, "y": 0.61}]}, "zhu": {"translate": [{"time": 0.7667}, {"time": 0.8667, "y": 1.12}, {"time": 1}, {"time": 1.0667, "y": 1.12}, {"time": 1.2}, {"time": 1.2667, "y": 1.12}, {"time": 1.4333}, {"time": 1.5333, "y": 1.12}, {"time": 1.6667}, {"time": 1.7333, "y": 1.12}, {"time": 1.8667}, {"time": 1.9333, "y": 1.12}, {"time": 2.1}, {"time": 2.2, "y": 1.12}, {"time": 2.3333}, {"time": 2.4, "y": 1.12}, {"time": 2.5333}, {"time": 2.6, "y": 1.12}, {"time": 2.7667}, {"time": 2.8667, "y": 1.12}, {"time": 3}, {"time": 3.0667, "y": 1.12}, {"time": 3.2}, {"time": 3.2667, "y": 1.12}, {"time": 3.4333}, {"time": 3.5333, "y": 1.12}, {"time": 3.6667}, {"time": 3.7333, "y": 1.12}, {"time": 3.8667}, {"time": 3.9333, "y": 1.12}, {"time": 4.1}, {"time": 4.2, "y": 1.12}, {"time": 4.3333}, {"time": 4.4, "y": 1.12}, {"time": 4.5333}, {"time": 4.6, "y": 1.12}, {"time": 4.7667}, {"time": 4.8667, "y": 1.12}, {"time": 5}, {"time": 5.0667, "y": 1.12}, {"time": 5.2}, {"time": 5.2667, "y": 1.12}, {"time": 5.4333}, {"time": 5.5333, "y": 1.12}, {"time": 5.6667}, {"time": 5.7333, "y": 1.12}, {"time": 5.8667}, {"time": 5.9333, "y": 1.12}, {"time": 6.1}, {"time": 6.2, "y": 1.12}, {"time": 6.3333}, {"time": 6.4, "y": 1.12}, {"time": 6.5333}, {"time": 6.6, "y": 1.12}, {"time": 6.7667}, {"time": 6.8667, "y": 1.12}, {"time": 7}, {"time": 7.0667, "y": 1.12}, {"time": 7.2}, {"time": 7.2667, "y": 1.12}, {"time": 7.4333}, {"time": 7.5333, "y": 1.12}, {"time": 7.6667}, {"time": 7.7333, "y": 1.12}, {"time": 7.8667}, {"time": 7.9333, "y": 1.12}, {"time": 8.1}, {"time": 8.2, "y": 1.12}, {"time": 8.3333}, {"time": 8.4, "y": 1.12}, {"time": 8.5333}, {"time": 8.6, "y": 1.12}, {"time": 8.7667}, {"time": 8.8667, "y": 1.12}]}, "bone50": {"translate": [{"time": 0.7667}, {"time": 3.4667, "x": -142.48, "curve": "stepped"}, {"time": 3.8667, "x": -142.48}, {"time": 5.1667, "x": -142.48, "y": -41.66, "curve": "stepped"}, {"time": 5.6, "x": -142.48, "y": -41.66}, {"time": 6.9, "x": -75.82, "y": -59}, {"time": 8.2333, "x": -14.28, "y": -40.58}], "scale": [{"time": 3.8667, "curve": "stepped"}, {"time": 3.9, "x": -1}]}, "bone53": {"translate": [{"x": 0.82, "y": -0.19}, {"time": 0.1333, "x": -1.44, "y": 0.61}, {"time": 0.3, "x": 1.39, "y": -0.39}, {"time": 0.4667, "x": -1.44, "y": 0.61}, {"time": 0.6333, "x": 1.39, "y": -0.39}, {"time": 0.8, "x": -1.44, "y": 0.61}, {"time": 0.9667, "x": 1.39, "y": -0.39}, {"time": 1.1333, "x": -1.44, "y": 0.61}, {"time": 1.3, "x": 1.39, "y": -0.39}, {"time": 1.4667, "x": -1.44, "y": 0.61}, {"time": 1.6333, "x": 1.39, "y": -0.39}, {"time": 1.8, "x": -1.44, "y": 0.61}, {"time": 1.9667, "x": 1.39, "y": -0.39}, {"time": 2.1333, "x": -1.44, "y": 0.61}, {"time": 2.3, "x": 1.39, "y": -0.39}, {"time": 2.4667, "x": -1.44, "y": 0.61}, {"time": 2.6333, "x": 1.39, "y": -0.39}, {"time": 2.8, "x": -1.44, "y": 0.61}, {"time": 2.9667, "x": 1.39, "y": -0.39}, {"time": 3.1333, "x": -1.44, "y": 0.61}, {"time": 3.3, "x": 1.39, "y": -0.39}, {"time": 3.4667, "x": -1.44, "y": 0.61}, {"time": 3.6333, "x": 1.39, "y": -0.39}, {"time": 3.8, "x": -1.44, "y": 0.61}, {"time": 3.9667, "x": 1.39, "y": -0.39}, {"time": 4.1333, "x": -1.44, "y": 0.61}, {"time": 4.3, "x": 1.39, "y": -0.39}, {"time": 4.4667, "x": -1.44, "y": 0.61}, {"time": 4.6333, "x": 1.39, "y": -0.39}, {"time": 4.8, "x": -1.44, "y": 0.61}, {"time": 4.9667, "x": 1.39, "y": -0.39, "curve": "stepped"}, {"time": 6.9333, "x": 1.39, "y": -0.39}, {"time": 7.1, "x": -1.44, "y": 0.61}, {"time": 7.2667, "x": 1.39, "y": -0.39}, {"time": 7.4333, "x": -1.44, "y": 0.61}, {"time": 7.6, "x": 1.39, "y": -0.39}, {"time": 7.7667, "x": -1.44, "y": 0.61}, {"time": 7.9333, "x": 1.39, "y": -0.39}, {"time": 8.1, "x": -1.44, "y": 0.61}, {"time": 8.2667, "x": 1.39, "y": -0.39}, {"time": 8.4333, "x": -1.44, "y": 0.61}, {"time": 8.6, "x": 1.39, "y": -0.39}, {"time": 8.7667, "x": -1.44, "y": 0.61}, {"time": 8.9333, "x": 1.39, "y": -0.39}, {"time": 9.1, "x": -1.44, "y": 0.61}, {"time": 9.2667, "x": 1.39, "y": -0.39}, {"time": 9.4333, "x": -1.44, "y": 0.61}, {"time": 9.6, "x": 1.39, "y": -0.39}, {"time": 9.7667, "x": -1.44, "y": 0.61}, {"time": 9.9333, "x": 1.39, "y": -0.39}, {"time": 10.1, "x": -1.44, "y": 0.61}, {"time": 10.2667, "x": 1.39, "y": -0.39}, {"time": 10.4333, "x": -1.44, "y": 0.61}, {"time": 10.6, "x": 1.39, "y": -0.39}, {"time": 10.7667, "x": -1.44, "y": 0.61}, {"time": 10.9333, "x": 1.39, "y": -0.39}, {"time": 11.1, "x": -1.44, "y": 0.61}, {"time": 11.2667, "x": 1.39, "y": -0.39}, {"time": 11.4333, "x": -1.44, "y": 0.61}, {"time": 11.6, "x": 1.39, "y": -0.39}, {"time": 11.7667, "x": -1.44, "y": 0.61}, {"time": 11.9333, "x": 1.39, "y": -0.39}, {"time": 12.1, "x": -1.44, "y": 0.61}, {"time": 12.2667, "x": 1.39, "y": -0.39}, {"time": 12.4333, "x": -1.44, "y": 0.61}, {"time": 12.6, "x": 1.39, "y": -0.39}, {"time": 12.7667, "x": -1.44, "y": 0.61}, {"time": 12.9333, "x": 1.39, "y": -0.39}, {"time": 13.1, "x": -1.44, "y": 0.61}, {"time": 13.2667, "x": 1.39, "y": -0.39}, {"time": 13.4333, "x": -1.44, "y": 0.61}, {"time": 13.6, "x": 1.39, "y": -0.39}, {"time": 13.7667, "x": -1.44, "y": 0.61}, {"time": 13.9333, "x": 1.39, "y": -0.39}, {"time": 14.1, "x": -1.44, "y": 0.61}, {"time": 14.2667, "x": 1.39, "y": -0.39}, {"time": 14.4333, "x": -1.44, "y": 0.61}, {"time": 14.6, "x": 1.39, "y": -0.39}, {"time": 14.7667, "x": -1.44, "y": 0.61}, {"time": 14.9333, "x": 1.39, "y": -0.39, "curve": "stepped"}, {"time": 14.9667, "x": 1.39, "y": -0.39}, {"time": 15.1, "x": -1.44, "y": 0.61, "curve": "stepped"}, {"time": 15.1333, "x": -1.44, "y": 0.61}, {"time": 15.3, "x": 1.39, "y": -0.39}, {"time": 15.4667, "x": -1.44, "y": 0.61}, {"time": 15.6333, "x": 1.39, "y": -0.39}, {"time": 15.8, "x": -1.44, "y": 0.61}, {"time": 15.9667, "x": 1.39, "y": -0.39}, {"time": 16, "x": 0.82, "y": -0.19}]}, "zhu2": {"translate": [{"y": 0.56}, {"time": 0.0667}, {"time": 0.1333, "y": 1.12}, {"time": 0.3}, {"time": 0.4, "y": 1.12}, {"time": 0.5333}, {"time": 0.6, "y": 1.12}, {"time": 0.7333}, {"time": 0.8, "y": 1.12}, {"time": 0.9667}, {"time": 1.0667, "y": 1.12}, {"time": 1.2}, {"time": 1.2667, "y": 1.12}, {"time": 1.4}, {"time": 1.4667, "y": 1.12}, {"time": 1.6333}, {"time": 1.7333, "y": 1.12}, {"time": 1.8667}, {"time": 1.9333, "y": 1.12}, {"time": 2.0667}, {"time": 2.1333, "y": 1.12}, {"time": 2.3}, {"time": 2.4, "y": 1.12}, {"time": 2.5333}, {"time": 2.6, "y": 1.12}, {"time": 2.7333}, {"time": 2.8, "y": 1.12}, {"time": 2.9667}, {"time": 3.0667, "y": 1.12}, {"time": 3.2}, {"time": 3.2667, "y": 1.12}, {"time": 3.4}, {"time": 3.4667, "y": 1.12}, {"time": 3.6333}, {"time": 3.7333, "y": 1.12}, {"time": 3.8667}, {"time": 3.9333, "y": 1.12}, {"time": 4.0667}, {"time": 4.1333, "y": 1.12}, {"time": 4.3}, {"time": 4.4, "y": 1.12}, {"time": 4.5333}, {"time": 4.6, "y": 1.12}, {"time": 4.7333}, {"time": 4.8, "y": 1.12}, {"time": 4.9667, "curve": "stepped"}, {"time": 6.9333}, {"time": 7.0333, "y": 1.12}, {"time": 7.1667}, {"time": 7.2333, "y": 1.12}, {"time": 7.3667}, {"time": 7.4333, "y": 1.12}, {"time": 7.6}, {"time": 7.7, "y": 1.12}, {"time": 7.8333}, {"time": 7.9, "y": 1.12}, {"time": 8.0333}, {"time": 8.1, "y": 1.12}, {"time": 8.2667}, {"time": 8.3667, "y": 1.12}, {"time": 8.5}, {"time": 8.5667, "y": 1.12}, {"time": 8.7}, {"time": 8.7667, "y": 1.12}, {"time": 8.9333}, {"time": 9.0333, "y": 1.12}, {"time": 9.1667}, {"time": 9.2333, "y": 1.12}, {"time": 9.3667}, {"time": 9.4333, "y": 1.12}, {"time": 9.6}, {"time": 9.7, "y": 1.12}, {"time": 9.8333}, {"time": 9.9, "y": 1.12}, {"time": 10.0333}, {"time": 10.1, "y": 1.12}, {"time": 10.2667}, {"time": 10.3667, "y": 1.12}, {"time": 10.5}, {"time": 10.5667, "y": 1.12}, {"time": 10.7}, {"time": 10.7667, "y": 1.12}, {"time": 10.9333}, {"time": 11.0333, "y": 1.12}, {"time": 11.1667}, {"time": 11.2333, "y": 1.12}, {"time": 11.3667}, {"time": 11.4333, "y": 1.12}, {"time": 11.6}, {"time": 11.7, "y": 1.12}, {"time": 11.8333}, {"time": 11.9, "y": 1.12}, {"time": 12.0333}, {"time": 12.1, "y": 1.12}, {"time": 12.2667}, {"time": 12.3667, "y": 1.12}, {"time": 12.5}, {"time": 12.5667, "y": 1.12}, {"time": 12.7}, {"time": 12.7667, "y": 1.12}, {"time": 12.9333}, {"time": 13.0333, "y": 1.12}, {"time": 13.1667}, {"time": 13.2333, "y": 1.12}, {"time": 13.3667}, {"time": 13.4333, "y": 1.12}, {"time": 13.6}, {"time": 13.7, "y": 1.12}, {"time": 13.8333}, {"time": 13.9, "y": 1.12}, {"time": 14.0333}, {"time": 14.1, "y": 1.12}, {"time": 14.2667}, {"time": 14.3667, "y": 1.12}, {"time": 14.5}, {"time": 14.5667, "y": 1.12}, {"time": 14.7}, {"time": 14.7667, "y": 1.12}, {"time": 14.9333, "curve": "stepped"}, {"time": 14.9667}, {"time": 15.0333, "y": 1.12, "curve": "stepped"}, {"time": 15.0667, "y": 1.12}, {"time": 15.2}, {"time": 15.2667, "y": 1.12}, {"time": 15.4}, {"time": 15.4667, "y": 1.12}, {"time": 15.6333}, {"time": 15.7333, "y": 1.12}, {"time": 15.8667}, {"time": 15.9333, "y": 1.12}, {"time": 16, "y": 0.56}]}, "bone52": {"translate": [{"x": -135.89, "y": -17.23}, {"time": 0.6, "x": -126.84, "y": -27.09, "curve": "stepped"}, {"time": 1.6667, "x": -126.84, "y": -27.09}, {"time": 4.9333, "x": -244.95, "y": -27.09, "curve": "stepped"}, {"time": 4.9667, "x": -304.14, "y": 17.83, "curve": "stepped"}, {"time": 6.9333, "x": -304.14, "y": 17.83}, {"time": 9.3667, "x": -219.22, "y": -25.22, "curve": "stepped"}, {"time": 10.9, "x": -219.22, "y": -25.22}, {"time": 13.2667, "x": -161.55, "y": 10.72, "curve": "stepped"}, {"time": 14.3, "x": -161.55, "y": 10.72}, {"time": 16, "x": -135.89, "y": -17.23}], "scale": [{"x": -1, "curve": "stepped"}, {"time": 1.6333, "x": -1}, {"time": 1.6667, "curve": "stepped"}, {"time": 4.9333, "curve": "stepped"}, {"time": 4.9667, "x": -1}]}}}, "animation3": {"slots": {"bf2": {"color": [{"color": "ffffff00"}]}, "kapaixg2": {"color": [{"color": "ffffffd5"}, {"time": 0.7, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 12.5, "color": "ffffffff"}, {"time": 12.6667, "color": "ffffffd5"}]}, "sd4": {"color": [{"color": "ffffff00"}]}, "sd12": {"color": [{"color": "ffffff00"}]}, "sd6": {"color": [{"color": "ffffff00"}]}, "sd": {"color": [{"color": "ffffff00"}]}, "bf": {"color": [{"color": "ffffff00"}]}, "lg2": {"color": [{"color": "ffffff00"}]}, "xian1": {"attachment": [{"name": null}]}, "yz2": {"color": [{"color": "ffffff00"}]}, "sd8": {"color": [{"color": "ffffff00"}]}, "zz": {"color": [{"color": "ffffff00"}]}, "zz2": {"color": [{"color": "ffffff00"}]}, "yz": {"color": [{"color": "ffffff00"}]}, "xz2": {"color": [{"color": "ffffff00"}]}, "xian2": {"attachment": [{"name": null}]}, "sd7": {"color": [{"color": "ffffff00"}]}, "lg": {"color": [{"color": "ffffff00"}]}, "kapaixg1": {"color": [{"time": 4.6667, "color": "ffffffff"}, {"time": 5.4, "color": "ffffff00", "curve": "stepped"}, {"time": 7.6, "color": "ffffff00"}, {"time": 8.2333, "color": "ffffffff"}]}, "sd3": {"color": [{"color": "ffffff00"}]}, "sd2": {"color": [{"color": "ffffff00"}]}, "sd10": {"color": [{"color": "ffffff00"}]}, "subf": {"color": [{"color": "ffffff00"}]}, "sd9": {"color": [{"color": "ffffff00"}]}, "xz": {"color": [{"color": "ffffff00"}]}, "sd5": {"color": [{"color": "ffffff00"}]}, "px": {"color": [{"color": "ffffff00"}]}, "sd11": {"color": [{"color": "ffffff00"}]}}, "bones": {"bone4": {"translate": [{"x": -314.86}]}, "bone5": {"translate": [{"x": -302.88}]}, "xiezi": {"translate": [{"x": -172.1}]}, "xiezi2": {"translate": [{"x": -213}]}, "bone46": {"translate": [{"x": -394.76}]}, "bone41": {"translate": [{"x": -361.82}]}, "bone52": {"translate": [{"x": -413.41}]}, "bone50": {"translate": [{"x": -407.48}]}, "bianfu": {"translate": [{"x": -304.74}]}, "bone56": {"rotate": [{"angle": 7.76, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 10.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 11.6, "curve": 0.25, "c3": 0.75}, {"time": 11.9333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "curve": 0.25, "c3": 0.75}, {"time": 12.6, "angle": 8.92, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 12.6667, "angle": 7.76}]}, "bone59": {"rotate": [{"angle": -0.43, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3667, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9333, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.7, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.2667, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.3667, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.9333, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0333, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.2667, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.3667, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.9333, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.0333, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.6, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.7, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.2667, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.3667, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.9333, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.0333, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.6, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.7, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.2667, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3667, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.9333, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.0333, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.6, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.7, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.2667, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.3667, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.9333, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 11.0333, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 11.3667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 11.7, "angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 12.0333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12.2667, "angle": -7.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.3667, "angle": -10.01, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 12.6667, "angle": -0.43}], "translate": [{"x": -0.99, "y": 0.96}, {"time": 0.0333, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3667, "x": -0.2, "y": -1.08}, {"time": 0.7, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9333, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0333, "x": -0.2, "y": -1.08}, {"time": 1.3667, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.7, "x": -0.2, "y": -1.08}, {"time": 2.0333, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.2667, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.3667, "x": -0.2, "y": -1.08}, {"time": 2.7, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.9333, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0333, "x": -0.2, "y": -1.08}, {"time": 3.3667, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.7, "x": -0.2, "y": -1.08}, {"time": 4.0333, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.2667, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.3667, "x": -0.2, "y": -1.08}, {"time": 4.7, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.9333, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.0333, "x": -0.2, "y": -1.08}, {"time": 5.3667, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.6, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.7, "x": -0.2, "y": -1.08}, {"time": 6.0333, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.2667, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.3667, "x": -0.2, "y": -1.08}, {"time": 6.7, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.9333, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.0333, "x": -0.2, "y": -1.08}, {"time": 7.3667, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 7.6, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.7, "x": -0.2, "y": -1.08}, {"time": 8.0333, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.2667, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3667, "x": -0.2, "y": -1.08}, {"time": 8.7, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.9333, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.0333, "x": -0.2, "y": -1.08}, {"time": 9.3667, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.6, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.7, "x": -0.2, "y": -1.08}, {"time": 10.0333, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.2667, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.3667, "x": -0.2, "y": -1.08}, {"time": 10.7, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.9333, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 11.0333, "x": -0.2, "y": -1.08}, {"time": 11.3667, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 11.7, "x": -0.2, "y": -1.08}, {"time": 12.0333, "x": -1.08, "y": 1.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12.2667, "x": -0.41, "y": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.3667, "x": -0.2, "y": -1.08}, {"time": 12.6667, "x": -0.99, "y": 0.96}]}, "bone57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 12, "curve": 0.25, "c3": 0.75}, {"time": 12.3, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 12.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "curve": 0.25, "c3": 0.75}, {"time": 10.9667, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 12, "curve": 0.25, "c3": 0.75}, {"time": 12.3, "x": -1.17, "y": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 12.6667}]}, "bone58": {"rotate": [{"angle": -3.35}, {"time": 0.0333, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.2667, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.4}, {"time": 0.7, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.9333, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 1.0667}, {"time": 1.3667, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1.6, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 1.7333}, {"time": 2.0333, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2.2667, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 2.4}, {"time": 2.7, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2.9333, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 3.0667}, {"time": 3.3667, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 3.6, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 3.7333}, {"time": 4.0333, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4.2667, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.4}, {"time": 4.7, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4.9333, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 5.0667}, {"time": 5.3667, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 5.6, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 5.7333}, {"time": 6.0333, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 6.2667, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 6.4}, {"time": 6.7, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 6.9333, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 7.0667}, {"time": 7.3667, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 7.6, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 7.7333}, {"time": 8.0333, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8.2667, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 8.4}, {"time": 8.7, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8.9333, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 9.0667}, {"time": 9.3667, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 9.6, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 9.7333}, {"time": 10.0333, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 10.2667, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 10.4}, {"time": 10.7, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 10.9333, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 11.0667}, {"time": 11.3667, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 11.6, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 11.7333}, {"time": 12.0333, "angle": -3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 12.2667, "angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 12.4}, {"time": 12.6667, "angle": -3.35}], "translate": [{"x": -2.4, "y": 0.2, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.2667, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.9333, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1.6, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2.2667, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2.9333, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 3.6, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4.2667, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4.9333, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 5.0667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 5.6, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 5.7333, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 6.2667, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 6.9333, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 7.0667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 7.6, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 7.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8.2667, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8.9333, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 9.6, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 9.7333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 10.2667, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 10.4, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 10.9333, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 11.0667, "curve": 0.25, "c3": 0.75}, {"time": 11.3667, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 11.6, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 11.7333, "curve": 0.25, "c3": 0.75}, {"time": 12.0333, "x": -2.53, "y": 0.21, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 12.2667, "x": -0.81, "y": 0.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 12.4, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 12.6667, "x": -2.4, "y": 0.2}]}, "bone54": {"translate": [{"x": 135.18, "y": 31.47}, {"time": 1.4667, "x": 195.72, "y": 42.54}, {"time": 3.1, "x": 263.14, "y": 49.11}, {"time": 4.2667, "x": 314.06, "y": 35.01}, {"time": 5.5333, "x": 363.59, "y": 15.4, "curve": "stepped"}, {"time": 7.5667, "x": 363.59, "y": 15.4, "curve": "stepped"}, {"time": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "x": 22.73, "y": 2.57}, {"time": 8.6333, "x": 30.78, "y": 12.93}, {"time": 9.1, "x": 37.18, "y": 24.77}, {"time": 10.1667, "x": 67.72, "y": 41.05}, {"time": 11.7333, "x": 133.8, "y": 31.22, "curve": "stepped"}, {"time": 12.6333, "x": 133.8, "y": 31.22}, {"time": 12.6667, "x": 135.18, "y": 31.47}]}, "bone64": {"rotate": [{"angle": 3.52, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": "stepped"}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 11.1333, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 11.8, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": 9.57, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.6667, "angle": 3.52}]}, "bone66": {"rotate": [{"angle": 13.06}, {"time": 0.0667, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4}, {"time": 0.7333, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.8, "angle": 14.21, "curve": "stepped"}, {"time": 2.8, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.0667}, {"time": 3.4, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.4667, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333}, {"time": 4.0667, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.1333, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.4}, {"time": 4.7333, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.8, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.0667}, {"time": 5.4, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.4667, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.7333}, {"time": 6.0667, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.1333, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.4}, {"time": 6.7333, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.8, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.0667}, {"time": 7.4, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.4667, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.7333}, {"time": 8.0667, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1333, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 8.4}, {"time": 8.7333, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.8, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.0667}, {"time": 9.4, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.4667, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.7333}, {"time": 10.0667, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.1333, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 10.4}, {"time": 10.7333, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.8, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 11.0667}, {"time": 11.4, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.4667, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 11.7333}, {"time": 12.0667, "angle": 16.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 12.1333, "angle": 14.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 12.4}, {"time": 12.6667, "angle": 13.06}], "translate": [{"x": -0.83, "y": -0.08, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.8, "x": -0.83, "y": -0.08, "curve": "stepped"}, {"time": 2.8, "x": -0.83, "y": -0.08}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.4667, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.1333, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.8, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.0667, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.4667, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.7333, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.1333, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.8, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.0667, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.4667, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.1333, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.8, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.4, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.4667, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.7333, "curve": 0.25, "c3": 0.75}, {"time": 10.0667, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.1333, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 10.4, "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.8, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 11.0667, "curve": 0.25, "c3": 0.75}, {"time": 11.4, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.4667, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 11.7333, "curve": 0.25, "c3": 0.75}, {"time": 12.0667, "x": -0.96, "y": -0.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 12.1333, "x": -0.83, "y": -0.08, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 12.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12.6667, "x": -0.83, "y": -0.08}]}, "bone65": {"rotate": [{"angle": 10.3, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.1333, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.8, "angle": 4.35, "curve": "stepped"}, {"time": 2.8, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.4667, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.1333, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.4667, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.1333, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.2667, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.8, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.4667, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1333, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.2667, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.8, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.9333, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.4667, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.1333, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.8, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.9333, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.4667, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 11.6, "curve": 0.25, "c3": 0.75}, {"time": 11.9333, "angle": 11.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.1333, "angle": 4.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.2667, "curve": 0.25, "c3": 0.75}, {"time": 12.6, "angle": 11.83, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 12.6667, "angle": 10.3}], "translate": [{"x": -0.95, "y": -0.23, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.1333, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667}, {"time": 0.6, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.8, "x": -0.4, "y": -0.1, "curve": "stepped"}, {"time": 2.8, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333}, {"time": 3.2667, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.4667, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.6}, {"time": 3.9333, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.1333, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.2667}, {"time": 4.6, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.9333}, {"time": 5.2667, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.4667, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.6}, {"time": 5.9333, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.1333, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.2667}, {"time": 6.6, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.8, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.9333}, {"time": 7.2667, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.4667, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.6}, {"time": 7.9333, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.1333, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.2667}, {"time": 8.6, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.8, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.9333}, {"time": 9.2667, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.4667, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.6}, {"time": 9.9333, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.1333, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.2667}, {"time": 10.6, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.8, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.9333}, {"time": 11.2667, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.4667, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 11.6}, {"time": 11.9333, "x": -1.09, "y": -0.26, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.1333, "x": -0.4, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.2667}, {"time": 12.6, "x": -1.09, "y": -0.26, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 12.6667, "x": -0.95, "y": -0.23}]}, "bone63": {"rotate": [{"angle": -14.18, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.1333, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3}, {"time": 0.6333, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8, "angle": -7.39, "curve": "stepped"}, {"time": 2.8, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667}, {"time": 3.3, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.4667, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6333}, {"time": 3.9667, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.1333, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3}, {"time": 4.6333, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9667}, {"time": 5.3, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.4667, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6333}, {"time": 5.9667, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1333, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3}, {"time": 6.6333, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.8, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.9667}, {"time": 7.3, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4667, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6333}, {"time": 7.9667, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1333, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3}, {"time": 8.6333, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.8, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.9667}, {"time": 9.3, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.4667, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.6333}, {"time": 9.9667, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.1333, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.3}, {"time": 10.6333, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.8, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.9667}, {"time": 11.3, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 11.4667, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 11.6333}, {"time": 11.9667, "angle": -14.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12.1333, "angle": -7.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12.3}, {"time": 12.6333, "angle": -14.78, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 12.6667, "angle": -14.18}], "translate": [{"x": -0.37, "y": 0.95, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.1333, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8, "x": -0.2, "y": 0.5, "curve": "stepped"}, {"time": 2.8, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.4667, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.1333, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.4667, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1333, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.8, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.9667, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4667, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6333, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1333, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.3, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.8, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.9667, "curve": 0.25, "c3": 0.75}, {"time": 9.3, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.4667, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.6333, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.1333, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.3, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.8, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.9667, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 11.4667, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 11.6333, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": -0.39, "y": 0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12.1333, "x": -0.2, "y": 0.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12.3, "curve": 0.25, "c3": 0.75}, {"time": 12.6333, "x": -0.39, "y": 0.99, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 12.6667, "x": -0.37, "y": 0.95}]}, "bone60": {"translate": [{"x": 44.59, "y": -9.01}, {"time": 0.5, "x": 20.9, "y": -8.43, "curve": "stepped"}, {"time": 2.7333, "x": 20.9, "y": -8.43, "curve": "stepped"}, {"time": 2.8, "x": 305.17, "y": 133.04}, {"time": 4.6, "x": 302.81, "y": 80.38}, {"time": 5.8, "x": 271.76, "y": 55.76}, {"time": 7.5667, "x": 208.23, "y": 42.66, "curve": "stepped"}, {"time": 8.5333, "x": 208.23, "y": 42.66}, {"time": 10.9, "x": 128.32, "y": -11.05}, {"time": 12.6667, "x": 44.59, "y": -9.01}], "scale": [{"x": -1}]}}}, "animation4": {"slots": {"bf2": {"attachment": [{"name": null}]}, "kapaixg4": {"color": [{"time": 3.9333, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00", "curve": "stepped"}, {"time": 5.1667, "color": "ffffff00"}, {"time": 5.7333, "color": "ffffffff"}], "attachment": [{"name": "kapaixg4"}]}, "sd4": {"attachment": [{"name": null}]}, "sd12": {"attachment": [{"name": null}]}, "sd6": {"attachment": [{"name": null}]}, "xz2": {"attachment": [{"name": null}]}, "kapaixg2": {"attachment": [{"name": null}]}, "sd": {"attachment": [{"name": null}]}, "bf": {"attachment": [{"name": null}]}, "lg2": {"attachment": [{"name": null}]}, "xian1": {"attachment": [{"name": null}]}, "yz2": {"attachment": [{"name": null}]}, "sd8": {"attachment": [{"name": null}]}, "zz": {"attachment": [{"name": null}]}, "zz2": {"attachment": [{"name": null}]}, "yz": {"attachment": [{"name": null}]}, "kapaixg1": {"attachment": [{"name": null}]}, "xian2": {"attachment": [{"name": null}]}, "sd7": {"attachment": [{"name": null}]}, "lg": {"attachment": [{"name": null}]}, "kapaixg3": {"color": [{"color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 8.7333, "color": "ffffffff"}, {"time": 9.3333, "color": "ffffff00"}], "attachment": [{"name": "kapaixg3"}]}, "sd3": {"attachment": [{"name": null}]}, "sd2": {"attachment": [{"name": null}]}, "sd10": {"attachment": [{"name": null}]}, "subf": {"attachment": [{"name": null}]}, "sd9": {"attachment": [{"name": null}]}, "xz": {"attachment": [{"name": null}]}, "sd5": {"attachment": [{"name": null}]}, "px": {"attachment": [{"name": null}]}, "sd11": {"attachment": [{"name": null}]}}, "bones": {"bone46": {"translate": [{"x": 431.46}]}, "bone28": {"translate": [{"x": 431.46}]}, "pangxie": {"translate": [{"x": 431.46}]}, "xiezi": {"translate": [{"x": 431.46}]}, "xiezi2": {"translate": [{"x": 431.46}]}, "bone32": {"translate": [{"x": 431.46}]}, "bianfu": {"translate": [{"x": 431.46}]}, "bone40": {"translate": [{"x": 431.46}]}, "bone41": {"translate": [{"x": 431.46}]}, "bone50": {"translate": [{"x": 431.46}]}, "bone52": {"translate": [{"x": 431.46}]}, "bone54": {"translate": [{"x": 431.46}]}, "bone60": {"translate": [{"x": 431.46}]}, "bone72": {"translate": [{"x": 24.55, "y": 105.48}, {"time": 1.3667, "x": -27.93, "y": 121.71}, {"time": 2.5333, "x": -87.32, "y": 118.63}, {"time": 4.5, "x": -196.06, "y": 141.39, "curve": "stepped"}, {"time": 4.5333, "x": 218.35, "y": 125.8, "curve": "stepped"}, {"time": 5.2, "x": 218.35, "y": 125.8}, {"time": 6.9667, "x": 143.59, "y": 92.01}, {"time": 9.1667, "x": 56.55, "y": 95.59}, {"time": 10, "x": 24.55, "y": 105.48}], "scale": [{"x": -1}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 10}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": -0.03, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 10}]}, "bone69": {"translate": [{"x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.6667, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.9, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 5.5667, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.9, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 7.3333, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 7.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8.6667, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.2333, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 9.3333, "x": 0.28, "y": 0.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 9.5667, "curve": 0.25, "c3": 0.75}, {"time": 9.9, "x": 0.37, "y": 0.03, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 10, "x": 0.28, "y": 0.02}]}, "bone70": {"rotate": [{"angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333}, {"time": 0.4667, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8}, {"time": 1.1333, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4667}, {"time": 1.8, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1333}, {"time": 2.4667, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8}, {"time": 3.1333, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4667}, {"time": 3.8, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.1333}, {"time": 4.4667, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.6667, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.8}, {"time": 5.1333, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.4667}, {"time": 5.8, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1333}, {"time": 6.4667, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.8}, {"time": 7.1333, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.3333, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.4667}, {"time": 7.8, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.1333}, {"time": 8.4667, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.6667, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.8}, {"time": 9.1333, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.3333, "angle": -4.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.4667}, {"time": 9.8, "angle": -11.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10, "angle": -4.4}], "translate": [{"y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.6667, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.3333, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.6667, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.3333, "y": 0.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.4667, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "x": 0.01, "y": 0.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10, "y": 0.15}]}, "bone67": {"translate": [{"x": -139.33, "y": 94.65}, {"time": 2.2, "x": -79.83, "y": 65.53}, {"time": 4.3667, "x": 2.46, "y": 55.4}, {"time": 6.9333, "x": -57.67, "y": 21.85}, {"time": 9.3333, "x": -130.84, "y": -17.45}], "scale": [{"time": 4.3333, "curve": "stepped"}, {"time": 4.3667, "x": -1}]}, "bone76": {"rotate": [{"angle": 0.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8667, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.8667, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.0333, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5333, "curve": "stepped"}, {"time": 5.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.8667, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.0333, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.5333, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.2, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3667, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.8667, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.0333, "curve": 0.25, "c3": 0.75}, {"time": 8.3667, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.5333, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.7, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.2, "angle": 5.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.3667, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.8667, "angle": 5.06, "curve": 0.359, "c2": 0.42, "c3": 0.718, "c4": 0.85}, {"time": 10, "angle": 0.41}]}, "bone75": {"translate": [{"x": -0.23, "y": 0.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": "stepped"}, {"time": 5.2}, {"time": 5.5333, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.8667, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "x": -0.61, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10, "x": -0.23, "y": 0.23}]}, "bone74": {"translate": [{"x": 0.28, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": "stepped"}, {"time": 5.2}, {"time": 5.5333, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.8667, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "x": 0.75, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10, "x": 0.28}]}}}, "animation5": {"slots": {"bf2": {"attachment": [{"name": null}]}, "kapaixg2": {"attachment": [{"name": null}]}, "sd4": {"attachment": [{"name": null}]}, "sd12": {"attachment": [{"name": null}]}, "sd6": {"attachment": [{"name": null}]}, "xz2": {"attachment": [{"name": null}]}, "sd": {"attachment": [{"name": null}]}, "bf": {"attachment": [{"name": null}]}, "lg2": {"attachment": [{"name": null}]}, "xian1": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00"}]}, "yz2": {"attachment": [{"name": null}]}, "sd8": {"attachment": [{"name": null}]}, "zz": {"attachment": [{"name": null}]}, "zz2": {"attachment": [{"name": null}]}, "yz": {"attachment": [{"name": null}]}, "kapaixg1": {"attachment": [{"name": null}]}, "xian2": {"color": [{"time": 0.7333, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffff"}]}, "sd7": {"attachment": [{"name": null}]}, "lg": {"attachment": [{"name": null}]}, "sd3": {"attachment": [{"name": null}]}, "sd2": {"attachment": [{"name": null}]}, "sd10": {"attachment": [{"name": null}]}, "subf": {"attachment": [{"name": null}]}, "sd9": {"attachment": [{"name": null}]}, "xz": {"attachment": [{"name": null}]}, "sd5": {"attachment": [{"name": null}]}, "px": {"attachment": [{"name": null}]}, "sd11": {"attachment": [{"name": null}]}}, "bones": {"bone28": {"translate": [{"x": -588.25}]}, "bone32": {"translate": [{"x": -588.25}]}, "bone46": {"translate": [{"x": -588.25}]}, "pangxie": {"translate": [{"x": -588.25}]}, "xiezi": {"translate": [{"x": -588.25}]}, "xiezi2": {"translate": [{"x": -588.25}]}, "bianfu": {"translate": [{"x": -588.25}]}, "bone40": {"translate": [{"x": -588.25}]}, "bone41": {"translate": [{"x": -588.25}]}, "bone50": {"translate": [{"x": -588.25}]}, "bone52": {"translate": [{"x": -588.25}]}, "bone54": {"translate": [{"x": -588.25}]}, "bone60": {"translate": [{"x": -588.25}]}, "bone67": {"translate": [{"x": -588.25}]}, "bone72": {"translate": [{"x": -588.25}]}, "flyline": {"translate": [{"x": 224.68, "y": -769.19}]}, "bone77": {"rotate": [{"angle": 148.04}], "translate": [{"x": 26.7, "y": -568.25}], "scale": [{"x": 1.5, "y": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": "stepped"}, {"time": 2.5123, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 0.1, "y": 0.1}]}, "flyline1": {"translate": [{"x": 200.02, "y": -787.63}]}, "bone80": {"scale": [{"time": 0.3333}, {"time": 0.9333, "x": 1.5, "y": 1.5, "curve": "stepped"}, {"time": 1.1667, "x": 1.5, "y": 1.5, "curve": "stepped"}, {"time": 1.2, "x": 0.1, "y": 0.1}, {"time": 1.7}]}}, "path": {"flyline": {"position": [{"position": 0.0513, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "position": 0.5962, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "position": 0.7115, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "position": 0.9551}]}, "flyline1": {"position": [{"position": 0.566}, {"time": 0.1333, "position": 0.5833, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "position": 1, "curve": "stepped"}, {"time": 1.1667, "position": 1}, {"time": 1.2}, {"time": 1.9333, "position": 0.4359}, {"time": 2.9333, "position": 0.566}]}}, "deform": {"default": {"flyline": {"flyline": [{"offset": 6, "vertices": [-14.71249, 20.08911, 70.70486, 18.79506, 262.5777, 7.23364, -126.20255, -23.03763, -196.00504, -30.43001, -212.53159, -108.31126]}]}}}}}}