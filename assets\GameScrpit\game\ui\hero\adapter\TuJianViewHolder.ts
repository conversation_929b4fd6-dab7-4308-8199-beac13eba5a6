import { _decorator, EventTouch, Label, math, Node, Sprite } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ItemCtrl } from "../../../common/ItemCtrl";
import { HeroModule } from "../../../../module/hero/HeroModule";
import { UIHeroMainTuJian } from "../UIHeroMainTuJian";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { divide } from "../../../../lib/utils/NumbersUtils";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import TipMgr from "db://assets/GameScrpit/lib/tips/TipMgr";
import { IConfigHeroPicture } from "../../../JsonDefine";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { HeroRouteItem } from "db://assets/GameScrpit/module/hero/HeroRoute";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("TuJianViewHolder")
export class TuJianViewHolder extends ViewHolder {
  @property(Label)
  private lblTitle: Label; //标题 未完成 外#9e0e0e 内#ff8181 完成 外#105319 内#87f59f
  @property(Label)
  private lblAttrAdd: Label;
  @property(Node)
  private nodeHeros: Node;
  @property(Node)
  private btnUpgrade: Node;
  @property(Node)
  private btnActivate: Node;
  @property(Node)
  private btnUpgradeDisable: Node;
  @property(Node)
  private btnActivateDisable: Node;

  private _context: UIHeroMainTuJian;
  private _data: IConfigHeroPicture;

  updateData(data: IConfigHeroPicture, context: UIHeroMainTuJian) {
    // log.log("TujainViewholder", data);
    this._data = data;
    this._context = context;
    let pictureLv = HeroModule.data.pictureMessage.pictureMap[data.id] ?? 0;
    let index = 0;
    let collectNum = 0;
    let collectLvNum = 0;
    for (; index < data.heroId.length && index < this.nodeHeros.children.length; index++) {
      let node = this.nodeHeros.children[index];
      node.active = true;
      node.getComponent(ItemCtrl).setItemId(data.heroId[index]);
      let heroMessage = HeroModule.data.getHeroMessage(data.heroId[index]);
      if (heroMessage) {
        collectNum++;
        node.getChildByName("lbl_level").getComponent(Label).string = `${heroMessage.level}级`;
        node.getChildByName("lbl_level").getComponent(Label).color = node.getComponent(ItemCtrl).itemName.color;
        node.getChildByName("lbl_level").getComponent(Label).outlineColor =
          node.getComponent(ItemCtrl).itemName.outlineColor;
        node.getChildByName("lbl_level").getComponent(Label).enableOutline = true;
        node.getComponent(ItemCtrl).itemName.enableOutline = true;

        node.getComponentsInChildren(Sprite).forEach((item) => {
          item.color = math.color("#FFFFFF");
        });
        if (pictureLv > 0 && pictureLv < data.level.length && heroMessage.level >= data.level[pictureLv]) {
          // 未满级前判断是否满足升级条件
          collectLvNum++;
        }
      } else {
        node.getChildByName("lbl_level").getComponent(Label).string = `未获得`;
        node.getChildByName("lbl_level").getComponent(Label).color = math.color("#838D98");
        node.getComponent(ItemCtrl).itemName.color = math.color("#838D98");
        node.getComponent(ItemCtrl).itemName.enableOutline = false;
        node.getChildByName("lbl_level").getComponent(Label).enableOutline = false;
        node.getComponentsInChildren(Sprite).forEach((item) => {
          item.color = math.color("#6E6E6E");
        });
      }
    }
    this.lblTitle.string = `【${data.name}】`;
    this.btnActivate.active = false;
    this.btnActivateDisable.active = false;
    this.btnUpgrade.active = false;
    this.btnUpgradeDisable.active = false;
    if (collectNum < data.heroId.length) {
      this.lblTitle.color = math.color("#607e74");
      this.lblAttrAdd.string = "当前羁绊未激活";
      this.btnActivateDisable.active = true;
    } else {
      this.lblTitle.color = math.color("#3973ae");
      // 只显示最后一个等级的属性加成
      let attrs = HeroModule.data.getHeroPictureLastestAttrMap(data.id);
      this.lblAttrAdd.string = ``;
      Object.keys(attrs).forEach((key) => {
        if (attrs[key] > 0) {
          let config = JsonMgr.instance.jsonList.c_attribute[key];
          if (config.type1 == 2) {
            this.lblAttrAdd.string += `${config.name} +${divide(attrs[key], 100)}% `;
          } else {
            this.lblAttrAdd.string += `${config.name} +${attrs[key]} `;
          }
        }
      });
      if (pictureLv == data.level.length) {
        //满级
      } else if (collectLvNum == data.heroId.length) {
        //可升级
        this.btnUpgrade.active = true;
      } else if (pictureLv > 0) {
        //不可升级
        this.btnUpgradeDisable.active = true;
      } else {
        //可激活
        this.btnActivate.active = true;
        this.lblAttrAdd.string = "当前羁绊未激活";
      }
    }
    for (; index < this.nodeHeros.children.length; index++) {
      this.nodeHeros.children[index].active = false;
    }
  }
  private onClickDetail(e: EventTouch) {
    log.log("onClickDetail");
    UIMgr.instance.showDialog(HeroRouteItem.UIHeroTujianDetail, { data: this._data });
    // this._context.showTips(this._data);
  }
  private onClickUpgrade() {
    log.log("onClickUpgrade", this._data.id);
    HeroModule.api.lvPicture(this._data.id, () => {
      AudioMgr.instance.playEffect(AudioName.Effect.武将升级);
      TipMgr.showTip("升级成功");
      this.updateData(this._data, this._context);
    });
  }
  private onClickActivate() {
    log.log("onClickActivate", this._data.id);
    HeroModule.api.lvPicture(this._data.id, () => {
      AudioMgr.instance.playEffect(AudioName.Effect.武将升级);
      TipMgr.showTip("激活成功");
      this.updateData(this._data, this._context);
    });
  }
}
