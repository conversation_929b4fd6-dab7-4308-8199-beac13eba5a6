"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const code_injection_1 = require("./core/code-injection");
const manifest_generator_1 = require("./core/manifest-generator");
const package_generator_1 = require("./core/package-generator");
// //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// 解析参数
// 构建目录绝对路径 e.g. /Users/<USER>/game/build/android/data
let assetsRootDirPath = "";
// 热更包配置文件路径 e.g. /Users/<USER>/game/gg-hot-update-config.json
let configFilePath = "";
// 热更包输出目录路径 e.g. /Users/<USER>/game/build/android/hotupdate
let outputDirPath = "";
// 热更包输出目录路径 e.g. /Users/<USER>/game/build/android/hotupdate.zip
let outputZipPath = "";
let i = 2;
while (i < process.argv.length) {
  const arg = process.argv[i];
  switch (arg) {
    case "-assetsRootDirPath":
      assetsRootDirPath = process.argv[i + 1];
      i += 2;
      break;
    case "-configPath":
      configFilePath = process.argv[i + 1];
      i += 2;
      break;
    case "-outputDirPath":
      outputDirPath = process.argv[i + 1];
      outputZipPath = outputDirPath + ".zip";
      i += 2;
      break;
    default:
      i++;
      break;
  }
}
console.log("当前执行node命令的目录路径", process.cwd());
console.log("可执行文件路径", process.argv[0]);
console.log("将执行的脚本路径", process.argv[1]);
console.log("传入参数数组\n", process.argv);
console.log("");
console.log("");
console.log("构建目录绝对路径", assetsRootDirPath);
console.log("热更包配置文件路径", configFilePath);
console.log("热更包输出目录路径", outputDirPath);
console.log("热更包输出Zip路径", outputZipPath);
console.log("");
console.log("");
// //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// 执行构建任务
// 生成 project.manifest 和 version.manifest
manifest_generator_1.manifestGenerator.generate(assetsRootDirPath, configFilePath, outputDirPath);
// 注入代码到 main.js
code_injection_1.codeInjection.injectCodeToMainJs(assetsRootDirPath);
// 打包最终热更包到输出目录
package_generator_1.packageGenerator.generate(outputZipPath, outputDirPath);
