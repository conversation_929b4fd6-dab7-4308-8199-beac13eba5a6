import { _decorator, instantiate, Node, NodeEventType, tween, UITransform, Vec3, view } from "cc";

import FmUtils from "../../lib/utils/FmUtils";
import { StartUp } from "../../lib/StartUp";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Fri Sep 13 2024 14:24:21 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubTips.ts
 *
 */

@ccclass("UIClubTips")
export class UIClubTips extends BaseCtrl {
  @property(Node)
  item: Node;
  @property(Node)
  bgTipContent: Node;
  @property(Node)
  indicator: Node;
  @property(Node)
  tipContent: Node;

  private toWorldX: number = 0;
  private toWorldY: number = 0;
  private itemList: number[];
  //=================================================
  public setTips(args: any): void {
    this.toWorldX = args.worldx;
    this.toWorldY = args.worldy;
    this.itemList = args.itemList;

    this.bgTipContent.removeAllChildren();
    for (let i = 0; i < this.itemList.length; i++) {
      let itemId = this.itemList[i];
      i++;
      let item = instantiate(this.item);
      item.active = true;
      FmUtils.setItemNode(item, itemId, this.itemList[i], false);
      this.bgTipContent.addChild(item);
    }
    this.indicator.setWorldPosition(new Vec3(this.toWorldX, this.toWorldY - 2));
    let sceneWidth = StartUp.instance.getVisibleSize().width;
    let right =
      this.tipContent.getComponent(UITransform).width * (1 - this.tipContent.getComponent(UITransform).anchorX);
    if (this.toWorldX + right > sceneWidth - 30) {
      this.toWorldX = sceneWidth - right - 30;
    }
    let left = this.tipContent.getComponent(UITransform).width * this.tipContent.getComponent(UITransform).anchorX;
    if (this.toWorldX - left < 30) {
      this.toWorldX = left + 30;
    }
    this.tipContent.setWorldPosition(new Vec3(this.toWorldX, this.toWorldY));
    tween(this.tipContent)
      .set({ scale: new Vec3(0.5, 0.5, 0.5) })
      .to(0.1, { scale: new Vec3(1, 1, 1) })
      .start();
    tween(this.tipContent)
      .set({ worldPosition: this.indicator.getWorldPosition() })
      .to(0.1, { worldPosition: new Vec3(this.toWorldX, this.toWorldY) })
      .start();
  }
  protected start(): void {
    this.node.on(NodeEventType.TOUCH_END, () => {
      this.node.active = false;
    });
  }
}
