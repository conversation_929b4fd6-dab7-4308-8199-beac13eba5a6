import { FmConfig } from "../../game/GameDefine";
import { UIMgr } from "../../lib/ui/UIMgr";
import { GameHealthRouteItem } from "./GameHealthRoute";
import { GameHealthModule } from "./GameHealthModule";
import { GameData } from "../../game/GameData";

/**
 * 模块逻辑处理
 */
export class GameHealthService {
  public checkPay(money: number): boolean {
    if (!FmConfig.gameHealthCheck) {
      return true;
    }
    if (GameData.instance.age >= 18) {
      return true;
    }
    //
    if (money > 100) {
      UIMgr.instance.showDialog(GameHealthRouteItem.UIGameHealthPayTips);
      return false;
    }
    if (money + GameHealthModule.data.curMonthRechargeTotal > 400) {
      UIMgr.instance.showDialog(GameHealthRouteItem.UIGameHealthPayLeftTips);
      return false;
    }
    return true;
  }
}
