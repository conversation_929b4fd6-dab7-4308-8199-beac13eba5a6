import { _decorator, RichText, sp } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { AudioMgr, AudioName } from "../../platform/src/AudioHelper";
import { TipsMgr } from "../../platform/src/TipsHelper";
import ToolExt from "../game/common/ToolExt";
import { JsonMgr } from "../game/mgr/JsonMgr";
import GuideMgr from "../ext_guide/GuideMgr";
const { ccclass, property } = _decorator;

@ccclass("TopJinJieCG")
export class TopJinJieCG extends BaseCtrl {
  private _skinId: number;
  @property(sp.Skeleton)
  aniTytanchuang: sp.Skeleton;

  public init(args) {
    super.init(args);
    TipsMgr.setEnableTouch(false, 0.3);

    this._skinId = args;
  }

  start() {
    super.start();
    this.playeAction();
    this.getNode("btn_go").active = false;
    AudioMgr.instance.playEffect(AudioName.Effect.获得奖励);
    this.scheduleOnce(() => {
      this.getNode("btn_go").active = true;
    }, 1);
    TipsMgr.setEnableTouch(false, 0.2);
  }

  private playeAction() {
    this.aniTytanchuang.getComponent(sp.Skeleton).setCompleteListener(() => {
      this.aniTytanchuang.getComponent(sp.Skeleton).setAnimation(0, "zi_jinjiechenggong_1", true);
    });
    this.aniTytanchuang.setAnimation(0, "zi_jinjiechenggong", false);

    ToolExt.loadUIRole(this.getNode("rolePoint"), this._skinId, -1, "", null);

    let db_leaderSkin = JsonMgr.instance.jsonList.c_leaderSkin[this._skinId];
    let lv = db_leaderSkin.unlock[1];

    let db_leader = JsonMgr.instance.jsonList.c_leader[lv];
    this.getNode("rich_name").getComponent(RichText).string = db_leader.jingjie2;
  }

  private on_click_btn_go() {
    GuideMgr.startGuide({ stepId: 70 });
    this.closeBack();
  }

  private on_click_btn_close() {
    this.closeBack();
  }
}
