import { _decorator, Label, Slider, Sprite } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { DialogZero } from "../../GameDefine";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ItemUseMessage } from "../../net/protocol/Item";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { IConfigItem } from "db://assets/GameScrpit/game/JsonDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import FmUtils from "../../../lib/utils/FmUtils";
const { ccclass, property } = _decorator;
const redColor: string = "#FF5244";
const greencolor: string = "#6CCA87";

@ccclass("UIItemJoinPop")
export class UIItemJoinPop extends UINode {
  protected _openAct: boolean = true; //打开动作

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_KNAPSACK}?prefab/ui/UIItemJoinPop`;
  }

  private _itemId: number = null;
  /**当前要合成的数量 */
  private _sliderNum: number = null;
  /**拥有的碎片数量 */
  private _itemMax: number = null;
  /**单个合成需要 */
  private _joinNeedNum: number = null;
  /**可以合成的最大值 */
  private _joinMax: number = null;

  /**上面的物品的数据 */
  private _topInfo: IConfigItem = null;
  /**下面物品的数据 */
  private _bottomInfo: IConfigItem = null;

  public init(args: any): void {
    super.init(args);
    this._itemId = args.itemId;
  }

  protected onRegEvent(): void {
    this["Slider"].on("slide", this.onSliderValueChanged, this);
  }

  protected onDelEvent(): void {
    this["Slider"].off("slide", this.onSliderValueChanged, this);
  }

  protected onEvtShow(): void {
    this.getTopItemData();
    this.getBottomItemData();
    this.getVariate();
    this.setTopItem();
    this.setBottomItem();
    this.setSliderNum();
    this.setItemNum();
  }

  private getTopItemData() {
    this._topInfo = JsonMgr.instance.getConfigItem(this._itemId);
  }

  private getBottomItemData() {
    let bottomId = this._topInfo.type1Son1List[0][0];
    this._bottomInfo = JsonMgr.instance.getConfigItem(bottomId);
  }

  private getVariate() {
    this._itemMax = PlayerModule.data.getItemNum(this._bottomInfo.id);
    this._joinNeedNum = this._topInfo.type1Son1List[0][1];
    this._joinMax = Math.floor(this._itemMax / this._joinNeedNum);
    this._sliderNum = 1;

    if (this._joinMax > 0) {
      this.getNode("sliderMask").active = false;
    } else {
      this.getNode("sliderMask").active = true;
    }
  }

  private setTopItem() {
    this["itemName"].getComponent(Label).string = this._topInfo.name;
    /**设置物品品质 */

    FmUtils.setItemNode(this["btn_topItem"], this._topInfo.id, PlayerModule.data.getItemNum(this._topInfo.id));

    // ToolExt.setItemBg(this["btn_topItem"], this._topInfo.color, this);
    // ToolExt.setItemIcon(this["btn_topItem"].getChildByName("icon"), this._topInfo.id, this);
    this.getNode("joinItemNum").getComponent(Label).string =
      "已拥有：" + PlayerModule.data.getItemNum(this._topInfo.id);
    this.getNode("detailyContent").getComponent(Label).string = this._topInfo.des;
  }

  private setBottomItem() {
    /**设置物品品质 */
    ToolExt.setItemBg(this["btn_bottomItem"], this._bottomInfo.color, this);
    ToolExt.setItemIcon(this["btn_bottomItem"].getChildByName("icon"), this._bottomInfo.id, this);
  }

  private setSliderNum() {
    this.getNode("silderNum").getComponent(Label).string = this._sliderNum + "/" + this._joinMax;
  }

  private setItemNum() {
    let sliderNum = this._sliderNum > this._joinMax ? 0 : this._sliderNum;
    let joinMax = this._joinMax == 0 ? 1 : this._joinMax;
    let proogress = sliderNum / joinMax;
    this["Slider"].getComponent(Slider).progress = proogress;
    this["fillSpr"].getComponent(Sprite).fillRange = proogress;
    this.getNode("myItemNum").getComponent(Label).string =
      PlayerModule.data.getItemNum(this._bottomInfo.id) + "/" + this._sliderNum * this._joinNeedNum;
  }

  private onSliderValueChanged(event) {
    // 这里处理滑动条值改变的逻辑
    let progress = event.progress;
    this._sliderNum = Math.floor(this._joinMax * progress);
    this.setItemNum();
    this.setSliderNum();
  }

  private on_click_btn_use() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._sliderNum > 0 && this._joinNeedNum * this._sliderNum > this._itemMax) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: this._itemId,
        needNum: this._joinNeedNum * this._sliderNum,
      });
      //UIMgr.instance.openPage(UIPath.gainItemPopup[0], UIPath.gainItemPopup[1], this._bottomInfo.id);
      return;
    }
    let callback = (res) => {
      if (res.values.length > 0) {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.values });
      }
      UIMgr.instance.back();
    };

    let info: ItemUseMessage = {
      itemId: this._itemId,
      num: this._sliderNum,
      heroId: 0,
      cityId: 0,
      choiceItemMap: {},
    };
    PlayerModule.api.useItem(info, callback);
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_topItem() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let itemId = this._topInfo.id;
    UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: itemId });
  }

  private on_click_btn_bottomItem() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let itemId = this._bottomInfo.id;
    UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
      itemId: itemId,
    });
  }

  private on_click_btn_add() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._joinMax == 0 || this._sliderNum >= this._joinMax) {
      return;
    }
    this._sliderNum++;
    this.setItemNum();
    this.setSliderNum();
  }

  private on_click_btn_minus() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._joinMax == 0 || this._sliderNum <= 1) {
      return;
    }
    this._sliderNum--;
    this.setItemNum();
    this.setSliderNum();
  }

  public tick(dt: any): void {}
}
