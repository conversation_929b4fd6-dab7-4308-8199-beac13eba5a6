import { JsonMgr } from "../../game/mgr/JsonMgr";
import { times } from "../../lib/utils/NumbersUtils";
import { GameHealthModule } from "./GameHealthModule";

export class GameHealthData {
  private _curMonthRechargeTotal: number = 0;

  public get curMonthRechargeTotal(): number {
    return this._curMonthRechargeTotal;
  }

  public set curMonthRechargeTotal(val: number) {
    this._curMonthRechargeTotal = val;
  }
}
