import { _decorator, Component, instantiate, Label, Node } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { JsonMgr } from "../../mgr/JsonMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { LangMgr } from "../../mgr/LangMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import FmUtils from "../../../lib/utils/FmUtils";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.STOP);
interface Param {
  eventId: number;
  rewardList: number[];
  answerMsg: string;
}
@ccclass("UIFractureAnswerSuccess")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureAnswerSuccess",
  nextHop: [],
  exit: "",
})
export class UIFractureAnswerSuccess extends BaseCtrl {
  public playShowAni: boolean = true;
  static Param = class {
    static create(eventId: number, rewardList: number[], answerMsg: string) {
      let param = new UIFractureAnswerSuccess.Param(eventId, rewardList, answerMsg);
      return param;
    }
    constructor(eventId: number, rewardList: number[], answerMsg: string) {
      this.eventId = eventId;
      this.rewardList = rewardList;
      this.answerMsg = answerMsg;
    }
    eventId: number;
    rewardList: number[];
    answerMsg: string;
  };

  @property(Node)
  private lblDes: Node = null;
  @property(Node)
  private itemLayout: Node = null;

  private _payload: Param = null;
  init(args: RouteShowArgs): void {
    super.init(args);
    this._payload = args.payload;
    log.log("this._payload", this._payload);
  }

  start() {
    super.start();
    AudioMgr.instance.playEffect(AudioName.Effect.获得奖励);
    let event = JsonMgr.instance.jsonList.c_fracture_even[this._payload.eventId];
    let descId = event[this._payload.answerMsg];
    let descMsg = LangMgr.txMsgCode(descId);
    log.log("descMsg", descMsg);
    this.lblDes.getComponent(Label).string = descMsg;
    let i = 0;
    let childrenIndex = 0;
    for (; i < this._payload.rewardList.length; i += 2) {
      let item = this.itemLayout.children[childrenIndex];
      if (!item) {
        item = instantiate(this.itemLayout.children[0]);
        item.active = true;
      }
      FmUtils.setItemNode(item, this._payload.rewardList[i], this._payload.rewardList[i + 1]);
      childrenIndex++;
    }
    for (; childrenIndex < this.itemLayout.children.length; childrenIndex++) {
      this.itemLayout.children[childrenIndex].active = false;
    }
  }

  update(deltaTime: number) {}
}
