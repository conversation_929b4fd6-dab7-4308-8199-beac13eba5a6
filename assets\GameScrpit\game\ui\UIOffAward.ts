import {
  _decorator,
  color,
  Component,
  director,
  instantiate,
  Label,
  Node,
  RichText,
  Skeleton,
  sp,
  tween,
  v3,
} from "cc";
import { UINode } from "../../lib/ui/UINode";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { UIMgr } from "../../lib/ui/UIMgr";
import { OfflineEnergyMessage } from "../net/protocol/Player";
import data from "../../lib/data/data";
import Formate from "../../lib/utils/Formate";
import { JsonMgr } from "../mgr/JsonMgr";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { LangMgr } from "../mgr/LangMgr";
import { NodeTool } from "../../lib/utils/NodeTool";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { RewardRouteEnum } from "../../ext_reward/RewardDefine";
const { ccclass, property } = _decorator;

@ccclass("UIOffAward")
export class UIOffAward extends UINode {
  protected _resetLayer: boolean = false; // 重置层级

  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAINPAGE}?prefab/ui/UIOffAward`;
  }

  private _offData: OfflineEnergyMessage = null;

  public init(args: any): void {
    super.init(args);
    this._offData = args.offData;
  }

  protected onEvtShow(): void {
    this.setMaxTime();

    this.getNode("time_rich").getComponent(Label).string = LangMgr.txMsgCode(219, [
      TimeUtils.timeformatHMS(this._offData.spanTime),
    ]);

    this.getNode("item1_num").getComponent(Label).string = Formate.format(this._offData.energy);

    this.getNode("node_main")
      .getComponent(sp.Skeleton)
      .setEventListener((animation, event) => {
        if (event["data"].name == "sj_zi_chuxian") {
          //
          this.getNode("node_content").active = true;
        }
        if (event["data"].name == "sj_saoguang_chuxian") {
          //
          this.getNode("node_saoguang_tx").active = true;
        }
      });
    this.getNode("node_main")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("chu_xian" == trackEntry.animation.name) {
          this.getNode("node_main").getComponent(sp.Skeleton).setAnimation(0, "chi_xu", true);
        }
      });
  }

  private setMaxTime() {
    let db = JsonMgr.instance.jsonList.c_buildCrystal;
    let list = Object.keys(db);
    let data = db[list[0]];

    let maxTime = data.rewardMaxTime;

    let time = this.convertTime2(maxTime);

    this.getNode("maxTime").getComponent(Label).string = LangMgr.txMsgCode(220, [time]);
  }

  convertTime(time) {
    let hours = Math.floor(time / 3600);
    let minutes = Math.floor((time % 3600) / 60);
    //let seconds = Math.floor(time % 60);

    if (hours < 1) {
      return minutes + LangMgr.txMsgCode(221, []);
    }
    return hours + LangMgr.txMsgCode(222, []) + minutes + LangMgr.txMsgCode(221, []);
  }

  convertTime2(time) {
    let hours = Math.floor(time / 3600);
    return hours + LangMgr.txMsgCode(222, []);
  }

  private on_click_btn_get_award() {
    let nodeUIMain = NodeTool.findByName(director.getScene(), "UIMain");
    let nodeQiyunTarget = NodeTool.findByName(nodeUIMain, "icon_item1");
    this.getNode("node_saoguang_tx").active = false;
    let flyQiyun = instantiate(this.getNode("node_item_qiyun"));
    flyQiyun.parent = this.node;
    flyQiyun.worldPosition = this.getNode("node_item_qiyun").worldPosition;
    let qiyunWorldPos = this.getNode("node_item_qiyun").worldPosition;
    let targetWordPos = v3(qiyunWorldPos.x + 120, qiyunWorldPos.y + 80, qiyunWorldPos.z);
    tween(flyQiyun)
      .delay(0.2)
      .call(() => {
        TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopItemFlyAni, {
          itemList: [1, 1],
          wPosition: targetWordPos,
        });
      })
      .delay(0.1)
      .hide()
      .start();
    tween(this.getNode("node_main"))
      .to(0.3, { scale: v3(0, 0, 0) })
      .delay(0.6)
      .call(() => {
        UIMgr.instance.back();
      })
      .start();
  }
}
