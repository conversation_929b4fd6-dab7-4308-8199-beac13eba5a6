import { Label } from "cc";
import { _decorator, Component, Node } from "cc";
import { tween } from "cc";
import { v3 } from "cc";
import { MainTaskModule } from "../../MainTaskModule";
const { ccclass, property } = _decorator;

@ccclass("TipTaskFinish")
export class TipTaskFinish extends Component {
  @property(Label)
  private lblTask: Label;

  start() {
    this.lblTask.string = MainTaskModule.service.getTaskDesc(false);

    // 播放出现动画
    tween(this.node.getChildByName("bg"))
      .set({ scale: v3(0.5, 0.5, 1) })
      .to(0.3, { scale: v3(1, 1, 1) }, { easing: "backOut" })
      .start();

    tween(this.node.getChildByName("layout"))
      .set({ scale: v3(0.25, 0.25, 1) })
      .to(0.5, { scale: v3(1, 1, 1) }, { easing: "backOut" })
      .start();
  }
}
