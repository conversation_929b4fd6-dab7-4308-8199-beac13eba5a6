{"skeleton": {"hash": "k18zqwsUDACxBvXyefB0ijMVx84=", "spine": "3.8.75", "x": -249.94, "y": -23.69, "width": 302.98, "height": 337.39, "images": "", "audio": "D:/spine导出/府邸剑_兽魂动画"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 67.36, "rotation": -0.96, "x": -175.45, "y": 237.19}, {"name": "bone2", "parent": "bone", "length": 22.88, "rotation": 176.91, "x": -7.65, "y": 0.14}, {"name": "bone3", "parent": "bone2", "length": 19.35, "rotation": -99.84, "x": 22.88}, {"name": "bone4", "parent": "bone3", "length": 15.57, "rotation": -9.01, "x": 19.35}, {"name": "bone5", "parent": "bone", "length": 13.28, "rotation": 14.15, "x": -1.39, "y": 0.44}, {"name": "bone6", "parent": "bone5", "length": 12.45, "rotation": -0.06, "x": 13.28}, {"name": "bone7", "parent": "bone6", "length": 17.4, "rotation": -28.62, "x": 15.37, "y": 7.2}, {"name": "bone8", "parent": "bone7", "length": 12.08, "rotation": 36.04, "x": 17.4}, {"name": "bone9", "parent": "bone8", "length": 19.15, "rotation": 112.73, "x": 12.08}, {"name": "bone10", "parent": "bone9", "length": 15.57, "rotation": 56.42, "x": 19.15}, {"name": "bone11", "parent": "bone2", "length": 15.64, "rotation": 32.6, "x": 8.18, "y": 13.13}, {"name": "bone12", "parent": "bone11", "length": 15.73, "rotation": 74.06, "x": 15.64}, {"name": "bone13", "parent": "bone2", "length": 11.09, "rotation": 104.54, "x": 29.53, "y": 14.85}, {"name": "bone14", "parent": "bone13", "length": 13.33, "rotation": -11.36, "x": 11.09}, {"name": "bone15", "parent": "bone6", "length": 14.07, "rotation": -82.1, "x": 4.82, "y": -4.24}, {"name": "bone16", "parent": "bone15", "length": 12.35, "rotation": 72.71, "x": 14.07}, {"name": "bone17", "parent": "bone16", "length": 16, "rotation": -89.41, "x": 12.35}, {"name": "bone18", "parent": "bone6", "length": 9.34, "rotation": -64.28, "x": -4.34, "y": -16.83}, {"name": "bone19", "parent": "bone18", "length": 11.16, "rotation": -33.66, "x": 9.34}, {"name": "bone20", "parent": "bone", "length": 14.53, "rotation": 117.53, "x": -58.1, "y": -6.43}, {"name": "bone21", "parent": "bone20", "length": 18.89, "rotation": -16.32, "x": 14.02, "y": -0.5}, {"name": "bone22", "parent": "bone", "length": 7.03, "rotation": 10.13, "x": 41.94, "y": 40.05}, {"name": "bone23", "parent": "bone22", "length": 8.94, "rotation": 148.77, "x": -3.35, "y": 7.35}, {"name": "bone24", "parent": "bone", "x": 0.09, "y": -26.03}, {"name": "bone25", "parent": "bone24", "x": -27.15, "y": -17.26}, {"name": "bone26", "parent": "bone24", "x": -16.89, "y": -19.61}, {"name": "bone27", "parent": "bone24", "x": -5.32, "y": -16.47}, {"name": "bone28", "parent": "root", "length": 67.36, "rotation": -0.96, "x": -175.45, "y": 237.19}, {"name": "bone29", "parent": "bone28", "length": 22.88, "rotation": 176.91, "x": -7.65, "y": 0.14}, {"name": "bone30", "parent": "bone29", "length": 19.35, "rotation": -99.84, "x": 22.88}, {"name": "bone31", "parent": "bone30", "length": 15.57, "rotation": -9.01, "x": 19.35}, {"name": "bone32", "parent": "bone29", "length": 15.64, "rotation": 32.6, "x": 8.18, "y": 13.13}, {"name": "bone33", "parent": "bone32", "length": 15.73, "rotation": 74.06, "x": 15.64}, {"name": "bone34", "parent": "bone29", "length": 11.09, "rotation": 104.54, "x": 29.53, "y": 14.85}, {"name": "bone35", "parent": "bone34", "length": 13.33, "rotation": -11.36, "x": 11.09}, {"name": "bone36", "parent": "bone28", "length": 13.28, "rotation": 14.15, "x": -1.39, "y": 0.44}, {"name": "bone37", "parent": "bone36", "length": 12.45, "rotation": -0.06, "x": 13.28}, {"name": "bone38", "parent": "bone37", "length": 17.4, "rotation": -28.62, "x": 15.37, "y": 7.2}, {"name": "bone39", "parent": "bone38", "length": 12.08, "rotation": 36.04, "x": 17.4}, {"name": "bone40", "parent": "bone39", "length": 19.15, "rotation": 112.73, "x": 12.08}, {"name": "bone41", "parent": "bone40", "length": 15.57, "rotation": 56.42, "x": 19.15}, {"name": "bone42", "parent": "bone37", "length": 14.07, "rotation": -82.1, "x": 4.82, "y": -4.24}, {"name": "bone43", "parent": "bone42", "length": 12.35, "rotation": 72.71, "x": 14.07}, {"name": "bone44", "parent": "bone43", "length": 16, "rotation": -89.41, "x": 12.35}, {"name": "bone45", "parent": "bone37", "length": 9.34, "rotation": -64.28, "x": -4.34, "y": -16.83}, {"name": "bone46", "parent": "bone45", "length": 11.16, "rotation": -33.66, "x": 9.34}, {"name": "bone47", "parent": "bone28", "length": 14.53, "rotation": 117.53, "x": -58.1, "y": -6.43}, {"name": "bone48", "parent": "bone47", "length": 18.89, "rotation": -16.32, "x": 14.02, "y": -0.5}, {"name": "bone49", "parent": "bone28", "length": 7.03, "rotation": 10.13, "x": 41.94, "y": 40.05}, {"name": "bone50", "parent": "bone49", "length": 8.94, "rotation": 148.77, "x": -3.35, "y": 7.35}, {"name": "bone51", "parent": "bone28", "x": 0.09, "y": -26.03}, {"name": "bone52", "parent": "bone51", "x": -27.15, "y": -17.26}, {"name": "bone53", "parent": "bone51", "x": -16.89, "y": -19.61}, {"name": "bone54", "parent": "bone51", "x": -5.32, "y": -16.47}, {"name": "bone56", "parent": "root", "x": -5.6, "y": -35.32}, {"name": "bone55", "parent": "bone56", "length": 161.66, "rotation": 89.44, "x": 5.84, "y": 34.89}, {"name": "bone57", "parent": "bone56", "length": 231.12, "rotation": 88.55, "x": 7.02, "y": 41.87, "scaleX": 0.5531, "scaleY": 0.1855}, {"name": "bone58", "parent": "bone56", "length": 161.66, "rotation": 89.44, "x": 5.84, "y": 34.89}, {"name": "bone59", "parent": "bone56", "length": 231.12, "rotation": 88.55, "x": 7.02, "y": 41.87, "scaleX": 0.5531, "scaleY": 0.1855}], "slots": [{"name": "tu", "bone": "bone56", "attachment": "tu"}, {"name": "b<PERSON><PERSON><PERSON>", "bone": "bone55", "attachment": "b<PERSON><PERSON><PERSON>"}, {"name": "baojian2", "bone": "bone58", "color": "00b0ffff", "attachment": "b<PERSON><PERSON><PERSON>", "blend": "additive"}, {"name": "qiling2", "bone": "bone28", "color": "def7ffff", "dark": "5c5c5c", "attachment": "qiling"}, {"name": "qiling", "bone": "bone", "color": "ffffff00", "attachment": "qiling", "blend": "additive"}, {"name": "fabao_dzzjx_skill_ccyan1_2", "bone": "bone57", "color": "3b00ffa9", "dark": "0099ff", "attachment": "fabao_dzzjx_skill_ccyan1_44", "blend": "additive"}, {"name": "fabao_dzzjx_skill_ccyan1_3", "bone": "bone59", "color": "3b00ffa9", "dark": "0099ff", "attachment": "fabao_dzzjx_skill_ccyan1_44", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"tu": {"tu": {"x": 4.78, "y": 33.63, "width": 51, "height": 44}}, "fabao_dzzjx_skill_ccyan1_2": {"fabao_dzzjx_skill_ccyan1_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [523.97, 60.31, 266.59, -258.84, -15.97, -30.96, 241.41, 288.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_26": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_30": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_32": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_34": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_36": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_38": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_40": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_42": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_44": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}}, "fabao_dzzjx_skill_ccyan1_3": {"fabao_dzzjx_skill_ccyan1_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [523.97, 60.31, 266.59, -258.84, -15.97, -30.96, 241.41, 288.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_26": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_30": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_32": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_34": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_36": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_38": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_40": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_42": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}, "fabao_dzzjx_skill_ccyan1_44": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [294.38, -256.17, -19.01, 8.18, 215.04, 285.65, 528.43, 21.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 91}}, "qiling": {"qiling": {"type": "mesh", "uvs": [0.20113, 0.81328, 0.22383, 0.85839, 0.21879, 0.90479, 0.23266, 0.94991, 0.28057, 0.97311, 0.33983, 0.99888, 0.40539, 1, 0.48105, 1, 0.54283, 0.98084, 0.58696, 0.93186, 0.63361, 0.89835, 0.70422, 0.86097, 0.77609, 0.84551, 0.83031, 0.82102, 0.882, 0.77462, 0.92361, 0.72951, 0.93874, 0.66764, 0.9337, 0.60577, 0.882, 0.57097, 0.81139, 0.54906, 0.79248, 0.51555, 0.84292, 0.53231, 0.911, 0.55808, 0.97152, 0.55551, 0.998, 0.50008, 1, 0.43822, 0.96018, 0.39568, 0.91857, 0.34799, 0.87444, 0.31964, 0.93244, 0.31835, 0.98792, 0.30933, 1, 0.29364, 1, 0.25966, 0.95247, 0.2321, 0.90395, 0.2174, 0.84644, 0.19077, 0.8087, 0.17148, 0.73862, 0.1476, 0.70088, 0.16046, 0.69279, 0.1926, 0.73143, 0.222, 0.78355, 0.23761, 0.78984, 0.26057, 0.73682, 0.25598, 0.67572, 0.25047, 0.63618, 0.2468, 0.60563, 0.24496, 0.59395, 0.2174, 0.58587, 0.20179, 0.59934, 0.1724, 0.58856, 0.13198, 0.5625, 0.12923, 0.55711, 0.09341, 0.55621, 0.04472, 0.53914, 0.01441, 0.51758, 0, 0.48253, 0, 0.46007, 0.02452, 0.45108, 0.06585, 0.45917, 0.09432, 0.43134, 0.08762, 0.39573, 0.07376, 0.36691, 0.04949, 0.34599, 0.02118, 0.32395, 0, 0.29173, 0, 0.26686, 0.01771, 0.24878, 0.05065, 0.24765, 0.08647, 0.2533, 0.12807, 0.23352, 0.13385, 0.21882, 0.14771, 0.19373, 0.14851, 0.17425, 0.15432, 0.17547, 0.18709, 0.18156, 0.21612, 0.1844, 0.24682, 0.18836, 0.28054, 0.18836, 0.30566, 0.19066, 0.33079, 0.20795, 0.34963, 0.22715, 0.36023, 0.21793, 0.38889, 0.21563, 0.41794, 0.22369, 0.436, 0.23022, 0.44699, 0.21332, 0.46623, 0.1954, 0.49067, 0.1846, 0.51907, 0.18254, 0.54905, 0.16607, 0.54326, 0.14086, 0.54116, 0.1244, 0.53117, 0.14755, 0.52591, 0.15064, 0.50014, 0.13881, 0.47174, 0.12492, 0.44018, 0.10331, 0.41651, 0.09044, 0.39022, 0.08221, 0.34867, 0.06369, 0.33079, 0.03951, 0.33868, 0.02099, 0.38443, 0, 0.40074, 0, 0.43387, 0, 0.49803, 0, 0.56903, 0.00658, 0.64266, 0.03385, 0.69262, 0.07763, 0.73348, 0.12463, 0.76466, 0.16813, 0.79277, 0.6041, 0.30044, 0.5908, 0.35484, 0.58062, 0.39964, 0.56262, 0.43964, 0.52271, 0.46204, 0.4961, 0.46444, 0.5368, 0.47724, 0.58845, 0.45244, 0.63228, 0.44284, 0.68706, 0.44044, 0.63619, 0.41004, 0.60488, 0.38924, 0.59315, 0.34524, 0.63619, 0.29404, 0.67532, 0.29964, 0.71132, 0.28284, 0.76375, 0.29004, 0.82997, 0.30298, 0.64142, 0.34832, 0.6947, 0.39036, 0.7377, 0.40565, 0.75733, 0.36169, 0.80501, 0.3636, 0.86203, 0.39418, 0.91998, 0.44578, 0.91718, 0.48114, 0.84707, 0.48592, 0.76855, 0.47445, 0.72929, 0.47445, 0.50203, 0.69761, 0.54467, 0.70158, 0.57103, 0.67938, 0.57491, 0.72139, 0.61988, 0.73486, 0.6098, 0.77053, 0.61368, 0.80382, 0.56018, 0.79906, 0.52063, 0.81571, 0.51598, 0.78084, 0.46869, 0.78955, 0.42371, 0.79114, 0.39037, 0.78401, 0.39037, 0.75309, 0.45628, 0.76023, 0.50745, 0.74041, 0.51443, 0.71346, 0.56864, 0.75851, 0.52527, 0.75773, 0.54581, 0.73128, 0.47733, 0.77095, 0.42483, 0.77562, 0.32668, 0.65662, 0.3107, 0.69784, 0.29168, 0.72351, 0.30157, 0.77951, 0.31299, 0.80673, 0.29777, 0.84251, 0.31527, 0.88995, 0.31679, 0.93039, 0.36776, 0.63234, 0.42482, 0.6129, 0.47352, 0.62534, 0.49711, 0.65568, 0.48417, 0.56503, 0.52754, 0.56037, 0.58537, 0.54559, 0.64395, 0.53315, 0.70026, 0.52148, 0.74895, 0.5207, 0.72945, 0.56348, 0.715, 0.61092, 0.70967, 0.65681, 0.74924, 0.64515, 0.79945, 0.64437, 0.79184, 0.6887, 0.79032, 0.73148, 0.78348, 0.77581, 0.61685, 0.66692, 0.63891, 0.70581, 0.67163, 0.72526, 0.66782, 0.75948, 0.65945, 0.82326, 0.65032, 0.85592, 0.59402, 0.63814, 0.62826, 0.6257, 0.6488, 0.67081, 0.68532, 0.69881, 0.71804, 0.71359, 0.72489, 0.7587, 0.73935, 0.80148, 0.40892, 0.54809, 0.33511, 0.54964, 0.2849, 0.56753, 0.24838, 0.56987, 0.18446, 0.59553, 0.20196, 0.65076, 0.22022, 0.69898, 0.21338, 0.76198, 0.16851, 0.72853, 0.1297, 0.67487, 0.11373, 0.63831, 0.0909, 0.58076, 0.08177, 0.54264, 0.07644, 0.49676, 0.06731, 0.4462, 0.05818, 0.39253, 0.33711, 0.47964, 0.27776, 0.46253, 0.40482, 0.47342, 0.23287, 0.51076, 0.50145, 0.51698, 0.49004, 0.18882, 0.49156, 0.26115, 0.49384, 0.36149, 0.48167, 0.41826, 0.44363, 0.43304, 0.37591, 0.41126, 0.31428, 0.39882, 0.22678, 0.22071, 0.36982, 0.23627, 0.4345, 0.30004, 0.36221, 0.33582, 0.25721, 0.29693, 0.30971, 0.14916, 0.43069, 0.1616, 0.30134, 0.07682, 0.53341, 0.08227, 0.54102, 0.20593, 0.54939, 0.32338], "triangles": [162, 155, 151, 161, 156, 150, 157, 142, 160, 161, 155, 156, 156, 157, 160, 162, 154, 155, 151, 155, 161, 150, 156, 159, 152, 162, 151, 150, 159, 148, 151, 161, 150, 148, 158, 146, 148, 159, 158, 146, 158, 145, 158, 160, 144, 159, 160, 158, 159, 156, 160, 8, 149, 9, 149, 148, 9, 10, 9, 148, 149, 150, 148, 151, 150, 149, 152, 151, 149, 8, 7, 149, 149, 7, 152, 6, 152, 7, 169, 152, 6, 170, 3, 2, 4, 170, 5, 170, 2, 169, 169, 6, 170, 4, 3, 170, 168, 167, 169, 170, 6, 5, 129, 42, 35, 42, 41, 35, 41, 36, 35, 41, 40, 36, 39, 38, 40, 40, 37, 36, 40, 38, 37, 29, 28, 34, 28, 129, 34, 29, 33, 30, 29, 34, 33, 31, 30, 32, 129, 35, 34, 30, 33, 32, 139, 134, 135, 139, 132, 134, 134, 132, 133, 120, 122, 121, 121, 131, 132, 121, 122, 131, 122, 130, 131, 122, 123, 130, 132, 131, 133, 28, 134, 129, 130, 126, 131, 133, 126, 127, 133, 131, 126, 129, 134, 128, 134, 133, 128, 133, 127, 128, 130, 125, 126, 128, 42, 129, 126, 44, 127, 126, 125, 44, 125, 45, 44, 127, 43, 128, 128, 43, 42, 127, 44, 43, 25, 137, 136, 136, 138, 135, 135, 27, 136, 136, 26, 25, 136, 27, 26, 135, 134, 28, 135, 28, 27, 22, 21, 137, 22, 137, 23, 137, 21, 138, 23, 137, 24, 24, 137, 25, 136, 137, 138, 19, 180, 20, 21, 20, 138, 179, 121, 140, 180, 139, 20, 180, 140, 139, 20, 139, 138, 138, 139, 135, 140, 132, 139, 140, 121, 132, 12, 188, 13, 12, 201, 188, 13, 188, 14, 201, 200, 188, 188, 187, 14, 188, 200, 187, 15, 14, 186, 200, 199, 187, 14, 187, 186, 187, 199, 186, 15, 186, 16, 16, 186, 185, 16, 18, 17, 18, 16, 185, 186, 184, 185, 198, 183, 199, 199, 184, 186, 199, 183, 184, 183, 182, 184, 185, 184, 181, 184, 182, 181, 185, 181, 19, 185, 19, 18, 183, 196, 182, 10, 194, 11, 194, 193, 11, 11, 193, 201, 11, 201, 12, 201, 192, 200, 192, 201, 193, 193, 147, 192, 192, 191, 200, 191, 199, 200, 147, 146, 192, 146, 145, 192, 191, 198, 199, 192, 145, 191, 194, 147, 193, 148, 146, 147, 10, 148, 147, 10, 147, 194, 157, 141, 142, 143, 142, 174, 191, 145, 190, 158, 144, 145, 145, 144, 190, 191, 190, 198, 142, 143, 144, 144, 189, 190, 144, 143, 189, 190, 197, 198, 190, 189, 197, 198, 197, 183, 143, 195, 189, 143, 174, 195, 183, 197, 196, 197, 189, 196, 189, 195, 196, 160, 142, 144, 174, 173, 176, 174, 176, 195, 176, 177, 195, 195, 177, 196, 196, 177, 178, 177, 176, 118, 176, 222, 118, 177, 119, 178, 177, 118, 119, 222, 117, 118, 19, 181, 180, 196, 178, 182, 182, 178, 181, 181, 178, 179, 181, 179, 180, 119, 120, 178, 178, 121, 179, 178, 120, 121, 179, 140, 180, 210, 110, 211, 110, 109, 211, 109, 108, 211, 210, 211, 207, 108, 212, 211, 108, 107, 212, 211, 212, 207, 212, 206, 207, 107, 213, 212, 107, 106, 213, 206, 90, 89, 90, 206, 91, 91, 206, 213, 91, 213, 92, 213, 206, 212, 205, 206, 89, 106, 214, 213, 213, 214, 92, 214, 105, 215, 214, 106, 105, 214, 215, 92, 93, 92, 94, 95, 94, 92, 95, 92, 215, 105, 216, 215, 216, 104, 217, 217, 104, 102, 104, 216, 105, 215, 96, 95, 96, 216, 97, 96, 215, 216, 97, 217, 98, 97, 216, 217, 104, 103, 102, 102, 101, 217, 217, 99, 98, 99, 101, 100, 99, 217, 101, 117, 116, 118, 115, 114, 119, 124, 123, 113, 125, 112, 46, 113, 240, 124, 124, 240, 112, 116, 226, 115, 220, 228, 227, 227, 226, 117, 115, 226, 114, 226, 227, 225, 226, 225, 114, 232, 225, 227, 229, 233, 228, 227, 228, 232, 228, 233, 232, 225, 240, 114, 82, 81, 229, 81, 234, 229, 229, 234, 233, 232, 224, 225, 225, 224, 240, 81, 80, 234, 80, 79, 234, 233, 231, 232, 233, 234, 231, 79, 78, 234, 224, 239, 240, 47, 239, 48, 239, 46, 240, 78, 77, 234, 47, 46, 239, 232, 231, 224, 224, 236, 223, 236, 224, 231, 77, 230, 234, 231, 234, 230, 69, 235, 230, 231, 230, 235, 70, 69, 71, 77, 76, 230, 224, 223, 239, 74, 71, 230, 74, 72, 71, 230, 76, 75, 231, 235, 236, 69, 230, 71, 230, 75, 74, 49, 48, 239, 239, 223, 51, 49, 51, 50, 51, 49, 239, 236, 59, 223, 223, 59, 238, 57, 238, 58, 51, 238, 52, 51, 223, 238, 55, 57, 56, 57, 55, 238, 74, 73, 72, 235, 61, 236, 236, 60, 59, 236, 61, 60, 69, 237, 235, 61, 237, 62, 61, 235, 237, 69, 68, 237, 238, 59, 58, 238, 53, 52, 68, 67, 237, 238, 54, 53, 238, 55, 54, 67, 66, 237, 66, 65, 237, 237, 63, 62, 237, 64, 63, 237, 65, 64, 218, 229, 228, 117, 220, 227, 117, 226, 116, 114, 240, 113, 112, 240, 46, 125, 46, 45, 124, 112, 130, 114, 113, 123, 130, 123, 124, 119, 114, 123, 112, 125, 130, 119, 123, 120, 120, 123, 122, 118, 116, 115, 118, 115, 119, 163, 205, 204, 221, 205, 89, 89, 88, 221, 205, 221, 204, 203, 204, 219, 204, 221, 219, 203, 219, 218, 218, 220, 202, 88, 87, 221, 87, 86, 221, 86, 85, 221, 221, 85, 219, 219, 229, 218, 218, 228, 220, 229, 219, 84, 219, 85, 84, 229, 84, 82, 84, 83, 82, 176, 173, 175, 172, 202, 175, 171, 204, 203, 171, 203, 202, 175, 202, 222, 203, 218, 202, 222, 220, 117, 220, 222, 202, 175, 222, 176, 1, 0, 168, 168, 0, 166, 168, 166, 167, 166, 0, 209, 0, 111, 209, 111, 210, 209, 111, 110, 210, 209, 165, 166, 1, 169, 2, 169, 1, 168, 209, 208, 165, 209, 210, 208, 210, 207, 208, 165, 208, 164, 205, 163, 207, 163, 208, 207, 207, 206, 205, 153, 154, 162, 152, 169, 153, 169, 167, 153, 167, 166, 153, 166, 154, 153, 154, 166, 165, 152, 153, 162, 171, 202, 172, 164, 208, 163, 154, 165, 164, 155, 141, 156, 174, 141, 172, 141, 155, 154, 174, 172, 173, 172, 141, 154, 164, 163, 154, 163, 171, 154, 154, 171, 172, 156, 141, 157, 142, 141, 174, 171, 163, 204, 173, 172, 175], "vertices": [3, 13, 18.91, -12.23, 0.1636, 14, 10.08, -10.45, 0.75244, 20, -30.02, 2.19, 0.08396, 5, 12, 16.3, -17.1, 0.00032, 13, 25.47, -10.26, 0.01751, 14, 16.12, -7.23, 0.86975, 20, -36.87, 2.11, 0.00943, 25, -15.87, 3.19, 0.10299, 5, 12, 22.26, -19.15, 0.0368, 14, 22.39, -7.83, 0.52745, 20, -42.16, 5.54, 2e-05, 19, 18.84, -63.26, 0.00058, 25, -16.46, -3.09, 0.43515, 5, 12, 28.63, -18.61, 0.03686, 14, 28.45, -5.83, 0.19294, 19, 25.08, -61.91, 0.00168, 25, -14.44, -9.14, 0.76034, 26, -24.71, -6.8, 0.00817, 1, 25, -7.78, -12.16, 1, 1, 25, 0.45, -15.51, 1, 1, 25, 9.5, -15.5, 1, 2, 25, 19.94, -15.33, 0.09874, 26, 9.68, -12.98, 0.90126, 2, 26, 18.16, -10.25, 0.49993, 27, 6.59, -13.39, 0.50007, 6, 11, -2.72, 43.64, 0.00017, 12, 36.92, 29.64, 0.00115, 18, 24.68, -25.83, 0.00013, 19, 27.08, -12.99, 0.00165, 26, 24.14, -3.54, 0.01583, 27, 12.57, -6.67, 0.98106, 6, 11, -10.54, 42.74, 0.01827, 12, 33.91, 36.91, 0.09406, 18, 25.19, -17.97, 0.01577, 19, 23.16, -6.17, 0.35676, 17, 31.18, -24.05, 3e-05, 27, 18.93, -2.04, 0.51511, 4, 11, -21.51, 42.97, 0.00167, 12, 31.11, 47.52, 0.01381, 19, 19.02, 3.99, 0.83966, 17, 26.89, -13.96, 0.14486, 3, 19, 17.84, 14.05, 0.3227, 16, 8.7, -25.6, 2e-05, 17, 25.56, -3.91, 0.67729, 2, 19, 15.22, 21.81, 0.02954, 17, 22.83, 3.8, 0.97046, 1, 17, 17.12, 11.39, 1, 3, 16, 30.04, -11.3, 0.01553, 17, 11.49, 17.58, 0.98278, 7, 36.68, -27.17, 0.0017, 4, 6, 38.48, -26.56, 0.00197, 16, 32.67, -3.11, 0.10254, 17, 3.32, 20.29, 0.88275, 7, 36.46, -18.56, 0.01274, 4, 6, 39.7, -18.27, 0.01279, 16, 32.52, 5.27, 0.21485, 17, -5.07, 20.23, 0.74101, 7, 33.56, -10.7, 0.03135, 5, 6, 33.82, -12.07, 0.04786, 15, 11.75, 27.65, 0.00061, 16, 25.71, 10.43, 0.3519, 17, -10.29, 13.47, 0.52723, 7, 25.43, -8.08, 0.0724, 5, 6, 25.01, -6.98, 0.18374, 15, 5.49, 19.62, 0.03364, 16, 16.18, 14.02, 0.39186, 17, -13.98, 3.98, 0.10707, 7, 15.25, -7.83, 0.28369, 5, 6, 23.49, -1.98, 0.1412, 15, 0.33, 18.81, 0.01417, 16, 13.88, 18.7, 0.09356, 17, -18.69, 1.72, 0.00907, 7, 11.53, -4.17, 0.742, 2, 7, 18.84, -4.49, 0.40187, 8, -1.48, -4.48, 0.59813, 1, 8, 6.1, -11.03, 1, 2, 8, 14.04, -13.64, 0.96698, 9, -13.34, 3.46, 0.03302, 2, 8, 20.09, -7.92, 0.71444, 9, -10.4, -4.33, 0.28556, 2, 8, 23.28, -0.19, 0.26567, 9, -4.51, -10.25, 0.73433, 3, 8, 20.15, 7.11, 0.02034, 9, 3.44, -10.19, 0.97252, 22, 12.63, -18.5, 0.00714, 2, 9, 12.06, -10.43, 0.88628, 22, 7.98, -11.23, 0.11372, 3, 9, 19.03, -8.62, 0.40086, 10, -7.25, -4.67, 0.02406, 22, 2.58, -6.48, 0.57508, 2, 9, 13.66, -14.56, 0.03416, 22, 10.51, -7.58, 0.96584, 2, 9, 9.3, -20.97, 0.00078, 22, 18.26, -7.6, 0.99922, 1, 22, 20.25, -5.78, 1, 1, 22, 20.98, -1.25, 1, 2, 22, 15.09, 3.47, 0.99181, 23, -17.78, -6.25, 0.00819, 2, 22, 8.8, 6.5, 0.82994, 23, -10.83, -5.57, 0.17006, 2, 22, 1.54, 11.31, 0.15476, 23, -2.13, -5.92, 0.84524, 1, 23, 3.68, -6.38, 1, 1, 23, 13.85, -5.73, 1, 1, 23, 18.03, -2.17, 1, 1, 23, 17.43, 2.27, 1, 1, 23, 11, 3.95, 1, 4, 4, 45.03, -45.53, 0.00072, 10, 3.25, -17.7, 0.05015, 22, -8.04, 6.45, 0.03693, 23, 3.54, 3.2, 0.9122, 5, 4, 42.52, -47.53, 0.00555, 9, 32.84, -5.59, 0.00058, 10, 2.92, -14.49, 0.31667, 22, -7.68, 3.25, 0.21788, 23, 1.57, 5.75, 0.45931, 6, 2, -31.81, -44.04, 1e-05, 4, 40.24, -40.55, 0.0476, 5, 37.29, 33.87, 0.00409, 10, 10.02, -16.34, 0.74562, 22, -14.8, 5.03, 0.09128, 23, 8.58, 7.92, 0.11139, 7, 2, -23.34, -44.19, 0.00245, 4, 37.64, -32.5, 0.16148, 6, 15.93, 36.53, 0.00117, 5, 29.25, 36.52, 0.02407, 10, 18.21, -18.49, 0.75355, 22, -23.01, 7.1, 0.0222, 23, 16.68, 10.4, 0.03508, 7, 2, -17.87, -44.3, 0.00357, 4, 35.98, -27.28, 0.55075, 6, 10.73, 38.25, 0.00277, 5, 24.05, 38.24, 0.02535, 10, 23.5, -19.9, 0.40365, 22, -28.31, 8.46, 0.00423, 23, 21.92, 11.99, 0.00968, 1, 4, 34.56, -23.3, 1, 1, 4, 37.36, -20.36, 1, 1, 4, 38.87, -18.52, 1, 1, 4, 43.25, -18.68, 1, 1, 4, 47.7, -15.19, 1, 1, 4, 46.64, -11.73, 1, 1, 4, 50.8, -9.17, 1, 1, 4, 56.81, -6.49, 1, 1, 4, 59.66, -2.73, 1, 1, 4, 60.3, 0.77, 1, 1, 4, 58.41, 5.22, 1, 1, 4, 54.16, 6.79, 1, 1, 4, 48.54, 5.76, 1, 1, 4, 45.43, 3.24, 1, 1, 4, 44.77, 7.13, 1, 1, 4, 44.58, 12.38, 1, 1, 4, 46.05, 17.32, 1, 1, 4, 48.45, 21.47, 1, 1, 4, 49.9, 25.38, 1, 1, 4, 48.17, 29.48, 1, 1, 4, 44.63, 31.71, 1, 1, 4, 39.56, 32.28, 1, 1, 4, 35.04, 30.54, 1, 1, 4, 30.17, 27.63, 1, 1, 4, 28.39, 29.84, 1, 1, 4, 25.88, 30.98, 1, 1, 4, 24.43, 34.13, 1, 1, 4, 22.67, 36.3, 1, 1, 4, 18.66, 34.43, 1, 1, 4, 15.37, 32.13, 1, 1, 4, 11.71, 30.15, 1, 1, 4, 7.73, 27.88, 1, 1, 4, 4.6, 26.56, 1, 1, 4, 1.6, 24.94, 1, 1, 4, 0.19, 21.76, 1, 4, 13, -40.57, 2.43, 2e-05, 3, 22.19, 18.54, 0.02064, 4, -0.1, 18.76, 0.97912, 20, 23.08, -28.37, 0.00022, 4, 13, -37, 0.48, 0.00303, 3, 18.13, 18.85, 0.5347, 4, -4.16, 18.42, 0.45085, 20, 20.19, -25.5, 0.01142, 4, 13, -33.2, -0.55, 0.0092, 3, 14.25, 18.22, 0.66558, 4, -7.9, 17.19, 0.30332, 20, 16.82, -23.46, 0.02189, 5, 13, -30.6, 0.1, 0.01793, 3, 12.15, 16.55, 0.73844, 4, -9.71, 15.22, 0.20984, 21, 6.54, -21.91, 4e-05, 20, 14.14, -23.37, 0.03375, 5, 13, -28.98, 0.72, 0.0291, 3, 10.92, 15.32, 0.78717, 4, -10.72, 13.81, 0.13585, 21, 4.92, -22.54, 0.00019, 20, 12.41, -23.51, 0.04769, 5, 13, -26.85, -2.05, 0.06533, 3, 7.84, 16.96, 0.78916, 4, -14.02, 14.95, 0.04922, 21, 2.78, -19.78, 0.00128, 20, 11.13, -20.27, 0.09501, 5, 13, -24.05, -5.08, 0.10681, 3, 4.05, 18.57, 0.71984, 4, -18.03, 15.94, 0.01233, 21, -0.03, -16.76, 0.00464, 20, 9.29, -16.58, 0.15639, 6, 13, -20.56, -7.25, 0.14919, 2, 41.7, -3.23, 0.00241, 3, -0.03, 19.09, 0.59004, 4, -22.14, 15.82, 0.00204, 21, -3.53, -14.61, 0.01354, 20, 6.53, -13.53, 0.24277, 5, 13, -16.63, -8.26, 0.19229, 2, 41.7, 0.83, 0.00699, 3, -4.03, 18.4, 0.35793, 21, -7.47, -13.61, 0.03825, 20, 3.03, -11.47, 0.40455, 5, 13, -17.81, -10.35, 0.14149, 2, 44.02, 0.21, 0.00251, 3, -3.82, 20.79, 0.22176, 21, -6.29, -11.51, 0.10363, 20, 4.75, -9.78, 0.53061, 5, 13, -18.72, -13.72, 0.05964, 2, 47.51, 0.17, 0.00013, 3, -4.38, 24.24, 0.08719, 21, -5.39, -8.14, 0.26999, 20, 6.56, -6.8, 0.58305, 4, 13, -20.46, -15.71, 0.0105, 3, -3.61, 26.77, 0.01853, 21, -3.66, -6.15, 0.67714, 20, 8.78, -5.37, 0.29383, 4, 13, -20.58, -12.44, 0.00208, 3, -2.16, 23.84, 0.00612, 21, -3.53, -9.42, 0.91693, 20, 7.99, -8.54, 0.07488, 2, 3, 1.32, 24.26, 0.00121, 21, -0.19, -10.45, 0.99879, 1, 21, 3.88, -9.53, 1, 1, 21, 8.41, -8.4, 1, 1, 21, 12.09, -6.03, 1, 1, 21, 15.89, -4.92, 1, 1, 21, 21.62, -4.8, 1, 1, 21, 24.45, -2.71, 1, 1, 21, 23.99, 0.76, 1, 2, 21, 18.37, 4.38, 0.98543, 20, 32.88, -1.46, 0.01457, 2, 21, 16.72, 7.62, 0.9484, 20, 32.21, 2.11, 0.0516, 2, 21, 12.32, 8.41, 0.84707, 20, 28.21, 4.11, 0.15293, 2, 21, 3.79, 9.95, 0.16397, 20, 20.46, 7.99, 0.83603, 2, 14, -22.47, -38.71, 0.00015, 20, 11.89, 12.27, 0.99985, 3, 13, -8.62, -34.44, 0.00966, 14, -12.55, -37.65, 0.01653, 20, 2.59, 15.9, 0.97381, 3, 13, -1.31, -31.97, 0.07107, 14, -5.86, -33.78, 0.05721, 20, -5.13, 15.56, 0.87173, 3, 13, 5.22, -27.03, 0.19863, 14, -0.44, -27.66, 0.14368, 20, -12.76, 12.62, 0.65769, 3, 13, 10.54, -21.42, 0.30624, 14, 3.67, -21.11, 0.28681, 20, -19.43, 8.7, 0.40695, 3, 13, 15.36, -16.21, 0.28786, 14, 7.38, -15.05, 0.51001, 20, -25.5, 5.03, 0.20214, 1, 4, 27.58, -26.02, 1, 2, 4, 20.1, -27.18, 0.98707, 10, 32.14, -6.58, 0.01293, 1, 4, 13.98, -28.25, 1, 7, 2, -9.58, -17.61, 0.03275, 3, 22.9, -28.97, 0.00148, 4, 8.04, -28.06, 0.75927, 6, -5.07, 15.21, 0.03499, 5, 8.22, 15.21, 0.10699, 7, -21.78, -2.76, 0.0148, 10, 37.9, 4.05, 0.04972, 7, 2, -4.3, -14.21, 0.13777, 3, 18.64, -24.35, 0.013, 4, 3.11, -24.16, 0.53675, 6, -11.12, 13.51, 0.028, 5, 2.17, 13.53, 0.22353, 7, -26.28, -7.15, 0.01139, 10, 43.84, 6.1, 0.04957, 7, 2, -0.66, -13.62, 0.22426, 3, 17.45, -20.87, 0.03166, 4, 1.39, -20.91, 0.4896, 6, -14.77, 14.03, 0.016, 5, -1.48, 14.05, 0.19054, 7, -29.73, -8.44, 0.00719, 10, 47.51, 5.8, 0.04074, 7, 2, -6.38, -12.3, 0.19243, 3, 17.12, -26.73, 0.01218, 4, 1.98, -26.75, 0.1893, 6, -9.69, 11.07, 0.05591, 5, 3.6, 11.08, 0.45922, 7, -23.86, -8.61, 0.02051, 10, 42.27, 8.45, 0.07045, 7, 2, -13.25, -16.14, 0.05926, 3, 22.08, -32.85, 0.00128, 4, 7.84, -32.01, 0.20717, 6, -1.99, 12.72, 0.17959, 5, 11.3, 12.72, 0.33707, 7, -17.88, -3.48, 0.06894, 10, 34.68, 6.35, 0.1467, 6, 2, -19.2, -17.86, 0.02417, 4, 11.39, -37.08, 0.06739, 6, 4.19, 12.6, 0.28227, 5, 17.48, 12.6, 0.20068, 7, -12.4, -0.61, 0.1878, 10, 28.5, 6.09, 0.23769, 6, 2, -26.71, -18.72, 0.00341, 4, 14.63, -43.92, 0.01528, 6, 11.63, 11.2, 0.18151, 5, 24.92, 11.19, 0.03951, 7, -5.2, 1.72, 0.48954, 10, 20.99, 7.05, 0.27075, 7, 2, -19.42, -22.31, 0.02296, 4, 15.68, -35.86, 0.09876, 6, 5.72, 16.79, 0.16463, 5, 19.02, 16.79, 0.14912, 7, -13.06, 3.8, 0.15411, 10, 27.22, 1.82, 0.41025, 23, 13.64, 32.41, 0.00016, 8, 2, -14.91, -24.81, 0.01576, 3, 30.9, -33, 4e-05, 4, 16.58, -30.78, 0.69258, 6, 2.16, 20.51, 0.04053, 5, 15.45, 20.51, 0.06861, 7, -17.97, 5.35, 0.02848, 10, 31, -1.68, 0.15387, 23, 18.7, 31.43, 0.00014, 2, 4, 21.42, -26.98, 0.82541, 10, 31.6, -7.8, 0.17459, 8, 2, -18.32, -37.93, 0.00411, 4, 30.1, -29.76, 0.63831, 6, 9.28, 32.04, 0.00549, 5, 22.59, 32.03, 0.02429, 7, -17.24, 18.89, 0.00178, 10, 24.58, -13.61, 0.31756, 22, -29.33, 2.17, 0.00241, 23, 19.52, 17.9, 0.00606, 8, 2, -23.76, -37.56, 0.00349, 4, 31.51, -35.03, 0.13564, 6, 14.37, 30.08, 0.0047, 5, 27.68, 30.07, 0.02768, 7, -11.84, 19.6, 0.00083, 10, 19.38, -11.96, 0.78375, 22, -24.12, 0.56, 0.01702, 23, 14.24, 16.57, 0.0269, 6, 2, -28.55, -40.17, 0.00057, 4, 35.53, -38.72, 0.07562, 5, 33.03, 31.14, 0.00994, 10, 14.1, -13.36, 0.80113, 22, -18.85, 2.01, 0.05017, 23, 10.48, 12.6, 0.06257, 5, 4, 37.45, -45.77, 0.01877, 5, 39.86, 28.54, 0.00068, 10, 7.13, -11.18, 0.67951, 22, -11.86, -0.1, 0.15389, 23, 3.41, 10.78, 0.14715, 5, 4, 39.4, -54.86, 0.00043, 9, 24.87, -5.69, 0.12702, 10, -1.58, -7.92, 0.26021, 22, -3.12, -3.28, 0.57505, 23, -5.71, 8.97, 0.03729, 1, 10, 25.1, -6.27, 1, 1, 10, 18.81, 0.56, 1, 1, 10, 13.31, 3.6, 1, 1, 10, 9.64, -1.79, 1, 1, 10, 3.2, -0.43, 1, 2, 9, 12.88, -0.47, 0.99656, 22, -0.71, -16.14, 0.00344, 2, 9, 2.32, -1.52, 0.99995, 22, 6.07, -24.29, 5e-05, 2, 8, 10.55, -1.61, 0.99473, 9, -0.89, 2.04, 0.00527, 4, 7, 17.72, 1.7, 0.26482, 8, 1.26, 1.19, 0.68068, 9, 5.28, 9.52, 0.0505, 10, 0.26, 16.82, 0.004, 3, 7, 6.86, 0.3, 0.99293, 9, 13.83, 16.35, 0.00153, 10, 10.68, 13.47, 0.00554, 3, 6, 16.26, 5.41, 0.09946, 16, 5.54, 24.81, 8e-05, 7, 1.64, -1.15, 0.90046, 6, 11, -7.54, 10.26, 0.42386, 12, 3.5, 25.1, 0.10254, 2, -3.7, 17.72, 0.01891, 5, -7.86, -16.79, 0.07656, 18, -7.3, -15.11, 0.29431, 19, -5.47, -21.8, 0.08381, 6, 11, -12.45, 13.54, 0.23525, 12, 5.31, 30.73, 0.07652, 2, -9.6, 17.84, 0.00724, 5, -2.26, -18.65, 0.08191, 18, -3.19, -10.87, 0.48255, 19, -4.41, -15.99, 0.11653, 6, 11, -17.08, 12.65, 0.13285, 12, 3.18, 34.93, 0.03232, 2, -13.02, 14.59, 0.00526, 5, 1.97, -16.56, 0.11419, 18, -3.24, -6.15, 0.6543, 19, -7.06, -12.1, 0.06107, 6, 11, -14.84, 17.89, 0.13897, 12, 8.83, 34.22, 0.06905, 2, -13.96, 20.21, 0.0004, 5, 1.2, -22.21, 0.0282, 18, 1.51, -9.3, 0.55634, 19, -1.36, -12.08, 0.20704, 4, 11, -19.42, 22.45, 0.04442, 12, 11.96, 39.88, 0.03142, 18, 6.82, -5.6, 0.54471, 19, 1.01, -6.06, 0.37945, 4, 11, -15.9, 26.02, 0.06541, 12, 16.36, 37.47, 0.07236, 18, 9.7, -9.71, 0.27009, 19, 5.68, -7.88, 0.59214, 5, 11, -14.22, 30.22, 0.04707, 12, 20.86, 37.01, 0.08487, 18, 13.53, -12.11, 0.11375, 19, 10.2, -7.76, 0.59409, 27, 15.97, 10.67, 0.16022, 6, 11, -8.04, 26.13, 0.08866, 12, 18.62, 29.94, 0.13355, 5, -3.18, -31.95, 0.00101, 18, 8.4, -17.46, 0.13649, 19, 8.89, -15.05, 0.28157, 27, 8.58, 11.19, 0.35873, 6, 11, -2.17, 25.49, 0.07328, 12, 19.62, 24.13, 0.13901, 5, -9, -32.89, 0.00051, 18, 6.73, -23.12, 0.06672, 19, 10.64, -20.69, 0.1421, 27, 3.16, 8.85, 0.57839, 6, 11, -3.86, 21.05, 0.1757, 12, 14.89, 24.53, 0.19701, 5, -8.55, -28.16, 0.00519, 18, 2.66, -20.66, 0.16155, 19, 5.89, -20.9, 0.19434, 27, 2.44, 13.55, 0.26622, 6, 11, 2.44, 18.97, 0.20588, 12, 14.61, 17.9, 0.28997, 5, -15.18, -27.82, 0.0023, 18, -0.52, -26.48, 0.09475, 19, 6.47, -27.51, 0.12578, 27, -4.07, 12.26, 0.28133, 7, 11, 7.99, 16.19, 0.22834, 12, 13.46, 11.8, 0.4478, 5, -21.27, -26.61, 0.00051, 18, -4.25, -31.45, 0.05563, 19, 6.12, -33.71, 0.07868, 26, 1.3, 15.08, 0.07973, 27, -10.27, 11.94, 0.10932, 5, 11, 11.57, 13.14, 0.24767, 12, 11.52, 7.52, 0.66244, 18, -7.88, -34.43, 0.03341, 19, 4.75, -38.2, 0.04465, 26, -3.32, 15.96, 0.01184, 5, 11, 9.58, 9.48, 0.42633, 12, 7.45, 8.43, 0.50952, 5, -24.58, -20.56, 0.00017, 18, -11.13, -31.81, 0.03229, 19, 0.59, -37.83, 0.03169, 5, 11, 2.05, 14.67, 0.40051, 12, 10.37, 17.1, 0.3448, 5, -15.94, -23.58, 0.00703, 18, -4.68, -25.33, 0.12981, 19, 2.37, -28.86, 0.11785, 6, 11, -5.43, 15.7, 0.33753, 12, 9.3, 24.57, 0.19218, 2, -4.85, 23.43, 0.00049, 5, -8.45, -22.58, 0.02678, 18, -2.33, -18.16, 0.27617, 19, 0.35, -21.58, 0.16684, 6, 11, -8.02, 12.96, 0.3521, 12, 5.96, 26.31, 0.12447, 2, -5.55, 19.73, 0.008, 5, -6.69, -19.26, 0.05859, 18, -4.56, -15.12, 0.33418, 19, -3.19, -20.29, 0.12266, 1, 24, 4.33, 0.21, 1, 2, 27, 3.66, 16.69, 0.05492, 24, -1.66, 0.22, 0.94508, 1, 24, 1.12, 3.83, 1, 2, 27, -2.92, 14.79, 0.14018, 24, -8.24, -1.68, 0.85982, 2, 27, -10.15, 14.04, 0.00419, 24, -15.48, -2.43, 0.99581, 5, 11, 11.07, -6.16, 0.64051, 12, -7.18, 2.7, 0.00059, 13, 1.27, 8.65, 0.20007, 2, 20.83, 13.91, 0.15797, 3, -13.35, -4.4, 0.00086, 4, 11, 15.67, -2.33, 0.55146, 12, -2.23, -0.67, 0.21127, 13, 6.34, 5.47, 0.22211, 2, 22.64, 19.61, 0.01516, 4, 11, 19.63, -0.54, 0.09215, 12, 0.58, -3.99, 0.2347, 13, 9.27, 2.26, 0.6424, 14, -2.23, 1.86, 0.03075, 2, 12, 8.25, -4.31, 0.42071, 14, 5.31, 3.34, 0.57929, 2, 12, 12.18, -3.57, 0.57831, 14, 8.96, 4.97, 0.42169, 4, 12, 16.44, -6.68, 0.15043, 14, 13.82, 2.94, 0.46673, 25, -5.7, 5.5, 0.2755, 26, -15.96, 7.85, 0.10734, 7, 11, 27.51, 20.75, 3e-05, 12, 23.22, -5.72, 0.02498, 14, 20.19, 5.45, 0.02893, 18, -3.25, -51.47, 4e-05, 19, 18.05, -49.82, 0.00129, 25, -3.18, -0.86, 0.87479, 26, -13.44, 1.49, 0.06994, 1, 25, -2.88, -6.31, 1, 1, 11, 4.53, -6.33, 1, 5, 11, -3.64, -4.88, 0.99695, 13, -2.06, 23.04, 0, 2, 7.74, 7.06, 0.003, 5, -15.63, -3.22, 2e-05, 18, -22.89, -16.24, 3e-05, 6, 11, -8.74, -0.19, 0.70545, 12, -6.88, 23.39, 0.00429, 2, 0.92, 8.26, 0.18067, 5, -9.47, -6.39, 0.05963, 18, -17.37, -12.06, 0.04662, 19, -15.54, -24.84, 0.00334, 6, 11, -9.65, 4.96, 0.41852, 12, -2.17, 25.68, 0.03707, 2, -2.62, 12.12, 0.116, 5, -7.23, -11.12, 0.16707, 18, -12.14, -12.09, 0.23205, 19, -11.17, -21.97, 0.02929, 5, 11, -13.93, -6.64, 0.00077, 12, -14.5, 26.6, 0, 2, 0.03, 0.04, 0.99902, 18, -22.79, -5.81, 0.0002, 19, -23.52, -22.64, 0, 7, 2, -5.9, -1.01, 0.03381, 3, 5.92, -28.19, 0.00034, 4, -8.85, -29.94, 0.00168, 6, -13.49, 0.44, 0.00031, 5, -0.21, 0.45, 0.96295, 7, -22.09, -19.76, 0.00019, 10, 45.42, 19.3, 0.00073, 7, 2, -13.72, -3.56, 0.00292, 3, 9.77, -35.45, 4e-05, 4, -3.91, -36.52, 0.00171, 6, -5.26, 0.57, 0.00566, 5, 8.02, 0.57, 0.98675, 7, -14.93, -15.71, 0.00115, 10, 37.22, 18.68, 0.00178, 5, 2, -21.66, -5.81, 0.00032, 4, 0.78, -43.31, 0.00065, 6, 2.99, 0.36, 0.99403, 7, -7.59, -11.93, 0.00341, 10, 28.96, 18.38, 0.00159, 1, 6, 10.92, 0.13, 1, 5, 6, 17.48, -1.29, 0.53255, 15, -1.17, 12.95, 0.03129, 16, 7.84, 18.4, 0.07874, 17, -18.45, -4.32, 0.0016, 7, 5.92, -6.44, 0.35582, 3, 6, 13.55, -6.3, 0.50806, 15, 3.25, 8.37, 0.2887, 16, 4.77, 12.82, 0.20324, 3, 6, 10.15, -12.09, 0.10485, 15, 8.51, 4.2, 0.6121, 16, 2.36, 6.55, 0.28305, 3, 6, 8.03, -17.95, 0.00085, 15, 14.03, 1.29, 0.28716, 16, 1.22, 0.42, 0.71199, 4, 6, 13.71, -17.66, 0.02163, 15, 14.52, 6.96, 0.04769, 16, 6.78, 1.64, 0.92867, 7, 10.45, -22.62, 0.00201, 5, 6, 20.48, -19.13, 0.01388, 15, 16.91, 13.46, 0.00424, 16, 13.7, 1.29, 0.51259, 17, -1.27, 1.36, 0.4587, 7, 17.1, -20.67, 0.01058, 2, 16, 12.26, -4.62, 0.00037, 17, 4.62, -0.14, 0.99963, 3, 19, 2.68, 17.4, 0.01654, 16, 11.67, -10.37, 0.01667, 17, 10.36, -0.78, 0.96679, 3, 19, 8.56, 15.92, 0.08715, 16, 10.34, -16.28, 0.01347, 17, 16.25, -2.18, 0.89939, 7, 11, -23.44, 14.2, 0.00474, 12, 2.92, 41.47, 0.00087, 2, -19.21, 12.47, 1e-05, 5, 8.51, -16.37, 0.01523, 18, -0.59, -0.18, 0.97477, 19, -8.16, -5.65, 0.00172, 15, 10.71, -11.15, 0.00266, 5, 11, -23.6, 20.26, 0.00555, 12, 8.71, 43.29, 0.00303, 5, 10.27, -22.17, 0.00017, 18, 5.41, -1.1, 0.95718, 19, -2.66, -3.09, 0.03406, 5, 18, 10.29, 0.77, 0.28016, 19, 0.36, 1.17, 0.65194, 15, 20.77, -6.92, 0.01013, 16, -4.62, -8.46, 0.04381, 17, 8.28, -17.05, 0.01397, 3, 19, 4.92, 0.23, 0.9942, 16, -5.45, -13.03, 0.00304, 17, 12.85, -17.93, 0.00276, 5, 11, -18.51, 35.54, 0.00942, 12, 24.8, 42.6, 0.03315, 18, 19.54, -8.84, 0.01156, 19, 13.39, -1.7, 0.94519, 17, 21.34, -19.73, 0.00069, 6, 11, -15.3, 38.82, 0.01641, 12, 28.83, 40.41, 0.07253, 18, 22.18, -12.58, 0.01584, 19, 17.66, -3.36, 0.67217, 17, 25.65, -21.32, 0.005, 27, 21.14, 3.72, 0.21805, 7, 11, -22.52, 9.28, 0.0358, 12, -1.56, 39.24, 0.00279, 2, -15.79, 8.81, 0.00064, 5, 6.33, -11.87, 0.27187, 18, -5.59, -0.19, 0.59736, 19, -12.32, -8.43, 0.00201, 15, 5.95, -12.7, 0.08952, 4, 11, -27.48, 10.06, 0.00213, 5, 11.31, -11.31, 0.16162, 18, -3.93, 4.54, 0.41552, 15, 6.08, -7.68, 0.42074, 4, 5, 12.68, -17.89, 0.00279, 18, 2.59, 2.93, 0.72755, 15, 12.78, -7.22, 0.24372, 16, -7.28, -0.92, 0.02593, 5, 18, 8.69, 4.48, 0.33891, 19, -3.02, 3.37, 0.23815, 15, 18.12, -3.88, 0.11908, 16, -2.5, -5.02, 0.27261, 17, 4.86, -14.9, 0.03126, 5, 18, 13.08, 6.75, 0.05138, 19, -0.63, 7.69, 0.32922, 15, 21.6, -0.38, 0.00459, 16, 1.88, -7.3, 0.41448, 17, 7.19, -10.55, 0.20033, 4, 18, 18.41, 3.66, 0.00402, 19, 5.52, 8.08, 0.53563, 16, 2.42, -13.44, 0.12515, 17, 13.34, -10.07, 0.33519, 3, 19, 11.46, 9.54, 0.51653, 16, 4.03, -19.34, 0.02379, 17, 19.25, -8.52, 0.45968, 6, 2, 10.55, -1.51, 0.96304, 3, 3.6, -11.9, 0.03132, 4, -13.7, -14.22, 0.00454, 5, -15.77, 5.8, 0.00071, 7, -38.31, -22.54, 0, 10, 61.27, 14.9, 0.00039, 2, 2, 20.69, -0.58, 0.84632, 3, 0.95, -2.06, 0.15368, 5, 11, 10.39, -19.48, 0.02099, 13, -11.6, 5.17, 0.18485, 2, 27.43, 2.32, 0.29592, 3, -3.06, 4.09, 0.45135, 20, -5.51, -22.98, 0.04688, 6, 11, 14.97, -21.62, 0.00277, 13, -12.21, 0.16, 0.3014, 2, 32.44, 2.99, 0.10398, 3, -4.58, 8.9, 0.44656, 21, -11.85, -22.05, 0.00156, 20, -3.54, -18.34, 0.14373, 6, 13, -10.41, -9.14, 0.30781, 14, -19.28, -13.2, 0.00186, 2, 40.99, 7.07, 0.01018, 3, -10.06, 16.63, 0.17551, 21, -13.69, -12.76, 0.01272, 20, -2.7, -8.9, 0.49192, 5, 13, -2.64, -8.13, 0.60487, 14, -11.86, -10.67, 0.03294, 2, 38.06, 14.33, 0.00257, 3, -16.72, 12.5, 0.04413, 20, -10.45, -7.72, 0.31549, 4, 13, 4.22, -6.83, 0.7227, 14, -5.39, -8.05, 0.12912, 3, -22.43, 8.49, 0.00053, 20, -17.39, -7.07, 0.14764, 3, 13, 12.41, -9.31, 0.32617, 14, 3.13, -8.87, 0.55979, 20, -24.58, -2.42, 0.11404, 3, 13, 6.84, -14.58, 0.40169, 14, -1.29, -15.13, 0.27845, 20, -17.77, 1.1, 0.31986, 4, 13, -1.25, -18.52, 0.2482, 14, -8.46, -20.59, 0.09632, 3, -22.27, 21.4, 0.00134, 20, -8.9, 2.65, 0.65414, 3, 13, -6.51, -19.79, 0.08622, 14, -13.36, -22.87, 0.03079, 20, -3.5, 2.42, 0.88299, 2, 14, -21.08, -26.14, 0.00035, 20, 4.86, 1.76, 0.99965, 1, 20, 10.03, 0.59, 1, 1, 21, 2.08, -0.46, 1, 1, 21, 9.03, -0.43, 1, 1, 21, 16.38, -0.48, 1, 3, 3, 10.19, -0.06, 0.99997, 5, -23.31, 17.06, 3e-05, 10, 69.48, 4.12, 1e-05, 4, 13, -25.72, 6.78, 0.01809, 3, 10.46, 8.45, 0.89366, 4, -10.1, 6.95, 0.05929, 20, 7.6, -28.44, 0.02896, 7, 2, 11.82, -11.52, 0.3788, 3, 13.24, -8.93, 0.392, 4, -4.63, -9.77, 0.19241, 6, -27.31, 15.72, 0.00041, 5, -14.02, 15.74, 0.02797, 7, -41.55, -12.97, 0.00046, 10, 60.13, 4.87, 0.00794, 6, 13, -20.45, -0.49, 0.11232, 2, 35.14, -4.82, 0.00558, 3, 2.65, 12.9, 0.74352, 4, -18.51, 10.12, 0.00658, 21, -3.61, -21.37, 0.00263, 20, 4.55, -19.99, 0.12937, 7, 2, -1.89, -6.6, 0.50083, 3, 10.74, -23.28, 0.02126, 4, -4.86, -24.35, 0.06095, 6, -15.66, 6.96, 0.00709, 5, -2.38, 6.97, 0.38574, 7, -27.12, -15.08, 0.00406, 10, 47.98, 12.92, 0.02007, 1, 4, 35.34, -5.65, 1, 1, 4, 26.42, -9.65, 1, 1, 4, 14.07, -15.21, 1, 1, 4, 6.35, -16.65, 1, 1, 4, 2.47, -12.59, 1, 1, 4, 1.54, -2.83, 1, 4, 13, -33.26, 13.31, 8e-05, 3, 20.02, 5.62, 0.46766, 4, -0.22, 5.65, 0.53014, 20, 13.04, -36.8, 0.00212, 1, 4, 17.23, 26.14, 1, 1, 4, 22.98, 7.14, 1, 1, 4, 18.52, -4.44, 1, 1, 4, 10.19, 2.87, 1, 1, 4, 9.39, 18.26, 1, 1, 4, 30.58, 19.35, 1, 1, 4, 35.53, 3.32, 1, 1, 4, 39.13, 24.22, 1, 1, 4, 50.92, -5.57, 1, 1, 4, 35.95, -13.03, 1, 1, 4, 21.79, -20.27, 1], "hull": 112, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 0, 222, 92, 224, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 224, 248, 248, 226, 246, 248, 224, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 56, 224, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 282, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 8, 326, 342, 342, 344, 344, 346, 346, 348, 348, 282, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 378, 380, 380, 382, 382, 384, 384, 386, 386, 388, 286, 390, 390, 392, 392, 394, 394, 396, 396, 398, 398, 400, 400, 402, 402, 24, 350, 404, 404, 406, 406, 408, 408, 410, 178, 412, 412, 414, 414, 416, 416, 418, 418, 0, 418, 420, 420, 422, 422, 424, 424, 426, 118, 446, 446, 448, 448, 450, 450, 452, 452, 454], "width": 138, "height": 135}}, "qiling2": {"qiling": {"type": "mesh", "uvs": [0.20113, 0.81328, 0.22383, 0.85839, 0.21879, 0.90479, 0.23266, 0.94991, 0.28057, 0.97311, 0.33983, 0.99888, 0.40539, 1, 0.48105, 1, 0.54283, 0.98084, 0.58696, 0.93186, 0.63361, 0.89835, 0.70422, 0.86097, 0.77609, 0.84551, 0.83031, 0.82102, 0.882, 0.77462, 0.92361, 0.72951, 0.93874, 0.66764, 0.9337, 0.60577, 0.882, 0.57097, 0.81139, 0.54906, 0.79248, 0.51555, 0.84292, 0.53231, 0.911, 0.55808, 0.97152, 0.55551, 0.998, 0.50008, 1, 0.43822, 0.96018, 0.39568, 0.91857, 0.34799, 0.87444, 0.31964, 0.93244, 0.31835, 0.98792, 0.30933, 1, 0.29364, 1, 0.25966, 0.95247, 0.2321, 0.90395, 0.2174, 0.84644, 0.19077, 0.8087, 0.17148, 0.73862, 0.1476, 0.70088, 0.16046, 0.69279, 0.1926, 0.73143, 0.222, 0.78355, 0.23761, 0.78984, 0.26057, 0.73682, 0.25598, 0.67572, 0.25047, 0.63618, 0.2468, 0.60563, 0.24496, 0.59395, 0.2174, 0.58587, 0.20179, 0.59934, 0.1724, 0.58856, 0.13198, 0.5625, 0.12923, 0.55711, 0.09341, 0.55621, 0.04472, 0.53914, 0.01441, 0.51758, 0, 0.48253, 0, 0.46007, 0.02452, 0.45108, 0.06585, 0.45917, 0.09432, 0.43134, 0.08762, 0.39573, 0.07376, 0.36691, 0.04949, 0.34599, 0.02118, 0.32395, 0, 0.29173, 0, 0.26686, 0.01771, 0.24878, 0.05065, 0.24765, 0.08647, 0.2533, 0.12807, 0.23352, 0.13385, 0.21882, 0.14771, 0.19373, 0.14851, 0.17425, 0.15432, 0.17547, 0.18709, 0.18156, 0.21612, 0.1844, 0.24682, 0.18836, 0.28054, 0.18836, 0.30566, 0.19066, 0.33079, 0.20795, 0.34963, 0.22715, 0.36023, 0.21793, 0.38889, 0.21563, 0.41794, 0.22369, 0.436, 0.23022, 0.44699, 0.21332, 0.46623, 0.1954, 0.49067, 0.1846, 0.51907, 0.18254, 0.54905, 0.16607, 0.54326, 0.14086, 0.54116, 0.1244, 0.53117, 0.14755, 0.52591, 0.15064, 0.50014, 0.13881, 0.47174, 0.12492, 0.44018, 0.10331, 0.41651, 0.09044, 0.39022, 0.08221, 0.34867, 0.06369, 0.33079, 0.03951, 0.33868, 0.02099, 0.38443, 0, 0.40074, 0, 0.43387, 0, 0.49803, 0, 0.56903, 0.00658, 0.64266, 0.03385, 0.69262, 0.07763, 0.73348, 0.12463, 0.76466, 0.16813, 0.79277, 0.6041, 0.30044, 0.5908, 0.35484, 0.58062, 0.39964, 0.56262, 0.43964, 0.52271, 0.46204, 0.4961, 0.46444, 0.5368, 0.47724, 0.58845, 0.45244, 0.63228, 0.44284, 0.68706, 0.44044, 0.63619, 0.41004, 0.60488, 0.38924, 0.59315, 0.34524, 0.63619, 0.29404, 0.67532, 0.29964, 0.71132, 0.28284, 0.76375, 0.29004, 0.82997, 0.30298, 0.64142, 0.34832, 0.6947, 0.39036, 0.7377, 0.40565, 0.75733, 0.36169, 0.80501, 0.3636, 0.86203, 0.39418, 0.91998, 0.44578, 0.91718, 0.48114, 0.84707, 0.48592, 0.76855, 0.47445, 0.72929, 0.47445, 0.50203, 0.69761, 0.54467, 0.70158, 0.57103, 0.67938, 0.57491, 0.72139, 0.61988, 0.73486, 0.6098, 0.77053, 0.61368, 0.80382, 0.56018, 0.79906, 0.52063, 0.81571, 0.51598, 0.78084, 0.46869, 0.78955, 0.42371, 0.79114, 0.39037, 0.78401, 0.39037, 0.75309, 0.45628, 0.76023, 0.50745, 0.74041, 0.51443, 0.71346, 0.56864, 0.75851, 0.52527, 0.75773, 0.54581, 0.73128, 0.47733, 0.77095, 0.42483, 0.77562, 0.32668, 0.65662, 0.3107, 0.69784, 0.29168, 0.72351, 0.30157, 0.77951, 0.31299, 0.80673, 0.29777, 0.84251, 0.31527, 0.88995, 0.31679, 0.93039, 0.36776, 0.63234, 0.42482, 0.6129, 0.47352, 0.62534, 0.49711, 0.65568, 0.48417, 0.56503, 0.52754, 0.56037, 0.58537, 0.54559, 0.64395, 0.53315, 0.70026, 0.52148, 0.74895, 0.5207, 0.72945, 0.56348, 0.715, 0.61092, 0.70967, 0.65681, 0.74924, 0.64515, 0.79945, 0.64437, 0.79184, 0.6887, 0.79032, 0.73148, 0.78348, 0.77581, 0.61685, 0.66692, 0.63891, 0.70581, 0.67163, 0.72526, 0.66782, 0.75948, 0.65945, 0.82326, 0.65032, 0.85592, 0.59402, 0.63814, 0.62826, 0.6257, 0.6488, 0.67081, 0.68532, 0.69881, 0.71804, 0.71359, 0.72489, 0.7587, 0.73935, 0.80148, 0.40892, 0.54809, 0.33511, 0.54964, 0.2849, 0.56753, 0.24838, 0.56987, 0.18446, 0.59553, 0.20196, 0.65076, 0.22022, 0.69898, 0.21338, 0.76198, 0.16851, 0.72853, 0.1297, 0.67487, 0.11373, 0.63831, 0.0909, 0.58076, 0.08177, 0.54264, 0.07644, 0.49676, 0.06731, 0.4462, 0.05818, 0.39253, 0.33711, 0.47964, 0.27776, 0.46253, 0.40482, 0.47342, 0.23287, 0.51076, 0.50145, 0.51698, 0.49004, 0.18882, 0.49156, 0.26115, 0.49384, 0.36149, 0.48167, 0.41826, 0.44363, 0.43304, 0.37591, 0.41126, 0.31428, 0.39882, 0.22678, 0.22071, 0.36982, 0.23627, 0.4345, 0.30004, 0.36221, 0.33582, 0.25721, 0.29693, 0.30971, 0.14916, 0.43069, 0.1616, 0.30134, 0.07682, 0.53341, 0.08227, 0.54102, 0.20593, 0.54939, 0.32338], "triangles": [162, 155, 151, 161, 156, 150, 157, 142, 160, 161, 155, 156, 156, 157, 160, 162, 154, 155, 151, 155, 161, 150, 156, 159, 152, 162, 151, 150, 159, 148, 151, 161, 150, 148, 158, 146, 148, 159, 158, 146, 158, 145, 158, 160, 144, 159, 160, 158, 159, 156, 160, 8, 149, 9, 149, 148, 9, 10, 9, 148, 149, 150, 148, 151, 150, 149, 152, 151, 149, 8, 7, 149, 149, 7, 152, 6, 152, 7, 169, 152, 6, 170, 3, 2, 4, 170, 5, 170, 2, 169, 169, 6, 170, 4, 3, 170, 168, 167, 169, 170, 6, 5, 129, 42, 35, 42, 41, 35, 41, 36, 35, 41, 40, 36, 39, 38, 40, 40, 37, 36, 40, 38, 37, 29, 28, 34, 28, 129, 34, 29, 33, 30, 29, 34, 33, 31, 30, 32, 129, 35, 34, 30, 33, 32, 139, 134, 135, 139, 132, 134, 134, 132, 133, 120, 122, 121, 121, 131, 132, 121, 122, 131, 122, 130, 131, 122, 123, 130, 132, 131, 133, 28, 134, 129, 130, 126, 131, 133, 126, 127, 133, 131, 126, 129, 134, 128, 134, 133, 128, 133, 127, 128, 130, 125, 126, 128, 42, 129, 126, 44, 127, 126, 125, 44, 125, 45, 44, 127, 43, 128, 128, 43, 42, 127, 44, 43, 25, 137, 136, 136, 138, 135, 135, 27, 136, 136, 26, 25, 136, 27, 26, 135, 134, 28, 135, 28, 27, 22, 21, 137, 22, 137, 23, 137, 21, 138, 23, 137, 24, 24, 137, 25, 136, 137, 138, 19, 180, 20, 21, 20, 138, 179, 121, 140, 180, 139, 20, 180, 140, 139, 20, 139, 138, 138, 139, 135, 140, 132, 139, 140, 121, 132, 12, 188, 13, 12, 201, 188, 13, 188, 14, 201, 200, 188, 188, 187, 14, 188, 200, 187, 15, 14, 186, 200, 199, 187, 14, 187, 186, 187, 199, 186, 15, 186, 16, 16, 186, 185, 16, 18, 17, 18, 16, 185, 186, 184, 185, 198, 183, 199, 199, 184, 186, 199, 183, 184, 183, 182, 184, 185, 184, 181, 184, 182, 181, 185, 181, 19, 185, 19, 18, 183, 196, 182, 10, 194, 11, 194, 193, 11, 11, 193, 201, 11, 201, 12, 201, 192, 200, 192, 201, 193, 193, 147, 192, 192, 191, 200, 191, 199, 200, 147, 146, 192, 146, 145, 192, 191, 198, 199, 192, 145, 191, 194, 147, 193, 148, 146, 147, 10, 148, 147, 10, 147, 194, 157, 141, 142, 143, 142, 174, 191, 145, 190, 158, 144, 145, 145, 144, 190, 191, 190, 198, 142, 143, 144, 144, 189, 190, 144, 143, 189, 190, 197, 198, 190, 189, 197, 198, 197, 183, 143, 195, 189, 143, 174, 195, 183, 197, 196, 197, 189, 196, 189, 195, 196, 160, 142, 144, 174, 173, 176, 174, 176, 195, 176, 177, 195, 195, 177, 196, 196, 177, 178, 177, 176, 118, 176, 222, 118, 177, 119, 178, 177, 118, 119, 222, 117, 118, 19, 181, 180, 196, 178, 182, 182, 178, 181, 181, 178, 179, 181, 179, 180, 119, 120, 178, 178, 121, 179, 178, 120, 121, 179, 140, 180, 210, 110, 211, 110, 109, 211, 109, 108, 211, 210, 211, 207, 108, 212, 211, 108, 107, 212, 211, 212, 207, 212, 206, 207, 107, 213, 212, 107, 106, 213, 206, 90, 89, 90, 206, 91, 91, 206, 213, 91, 213, 92, 213, 206, 212, 205, 206, 89, 106, 214, 213, 213, 214, 92, 214, 105, 215, 214, 106, 105, 214, 215, 92, 93, 92, 94, 95, 94, 92, 95, 92, 215, 105, 216, 215, 216, 104, 217, 217, 104, 102, 104, 216, 105, 215, 96, 95, 96, 216, 97, 96, 215, 216, 97, 217, 98, 97, 216, 217, 104, 103, 102, 102, 101, 217, 217, 99, 98, 99, 101, 100, 99, 217, 101, 117, 116, 118, 115, 114, 119, 124, 123, 113, 125, 112, 46, 113, 240, 124, 124, 240, 112, 116, 226, 115, 220, 228, 227, 227, 226, 117, 115, 226, 114, 226, 227, 225, 226, 225, 114, 232, 225, 227, 229, 233, 228, 227, 228, 232, 228, 233, 232, 225, 240, 114, 82, 81, 229, 81, 234, 229, 229, 234, 233, 232, 224, 225, 225, 224, 240, 81, 80, 234, 80, 79, 234, 233, 231, 232, 233, 234, 231, 79, 78, 234, 224, 239, 240, 47, 239, 48, 239, 46, 240, 78, 77, 234, 47, 46, 239, 232, 231, 224, 224, 236, 223, 236, 224, 231, 77, 230, 234, 231, 234, 230, 69, 235, 230, 231, 230, 235, 70, 69, 71, 77, 76, 230, 224, 223, 239, 74, 71, 230, 74, 72, 71, 230, 76, 75, 231, 235, 236, 69, 230, 71, 230, 75, 74, 49, 48, 239, 239, 223, 51, 49, 51, 50, 51, 49, 239, 236, 59, 223, 223, 59, 238, 57, 238, 58, 51, 238, 52, 51, 223, 238, 55, 57, 56, 57, 55, 238, 74, 73, 72, 235, 61, 236, 236, 60, 59, 236, 61, 60, 69, 237, 235, 61, 237, 62, 61, 235, 237, 69, 68, 237, 238, 59, 58, 238, 53, 52, 68, 67, 237, 238, 54, 53, 238, 55, 54, 67, 66, 237, 66, 65, 237, 237, 63, 62, 237, 64, 63, 237, 65, 64, 218, 229, 228, 117, 220, 227, 117, 226, 116, 114, 240, 113, 112, 240, 46, 125, 46, 45, 124, 112, 130, 114, 113, 123, 130, 123, 124, 119, 114, 123, 112, 125, 130, 119, 123, 120, 120, 123, 122, 118, 116, 115, 118, 115, 119, 163, 205, 204, 221, 205, 89, 89, 88, 221, 205, 221, 204, 203, 204, 219, 204, 221, 219, 203, 219, 218, 218, 220, 202, 88, 87, 221, 87, 86, 221, 86, 85, 221, 221, 85, 219, 219, 229, 218, 218, 228, 220, 229, 219, 84, 219, 85, 84, 229, 84, 82, 84, 83, 82, 176, 173, 175, 172, 202, 175, 171, 204, 203, 171, 203, 202, 175, 202, 222, 203, 218, 202, 222, 220, 117, 220, 222, 202, 175, 222, 176, 1, 0, 168, 168, 0, 166, 168, 166, 167, 166, 0, 209, 0, 111, 209, 111, 210, 209, 111, 110, 210, 209, 165, 166, 1, 169, 2, 169, 1, 168, 209, 208, 165, 209, 210, 208, 210, 207, 208, 165, 208, 164, 205, 163, 207, 163, 208, 207, 207, 206, 205, 153, 154, 162, 152, 169, 153, 169, 167, 153, 167, 166, 153, 166, 154, 153, 154, 166, 165, 152, 153, 162, 171, 202, 172, 164, 208, 163, 154, 165, 164, 155, 141, 156, 174, 141, 172, 141, 155, 154, 174, 172, 173, 172, 141, 154, 164, 163, 154, 163, 171, 154, 154, 171, 172, 156, 141, 157, 142, 141, 174, 171, 163, 204, 173, 172, 175], "vertices": [3, 34, 18.91, -12.23, 0.1636, 35, 10.08, -10.45, 0.75244, 47, -30.02, 2.19, 0.08396, 5, 33, 16.3, -17.1, 0.00032, 34, 25.47, -10.26, 0.01751, 35, 16.12, -7.23, 0.86975, 47, -36.87, 2.11, 0.00943, 52, -15.87, 3.19, 0.10299, 5, 33, 22.26, -19.15, 0.0368, 35, 22.39, -7.83, 0.52745, 47, -42.16, 5.54, 2e-05, 46, 18.84, -63.26, 0.00058, 52, -16.46, -3.09, 0.43515, 5, 33, 28.63, -18.61, 0.03686, 35, 28.45, -5.83, 0.19294, 46, 25.08, -61.91, 0.00168, 52, -14.44, -9.14, 0.76034, 53, -24.71, -6.8, 0.00817, 1, 52, -7.78, -12.16, 1, 1, 52, 0.45, -15.51, 1, 1, 52, 9.5, -15.5, 1, 2, 52, 19.94, -15.33, 0.09874, 53, 9.68, -12.98, 0.90126, 2, 53, 18.16, -10.25, 0.49993, 54, 6.59, -13.39, 0.50007, 6, 32, -2.72, 43.64, 0.00017, 33, 36.92, 29.64, 0.00115, 45, 24.68, -25.83, 0.00013, 46, 27.08, -12.99, 0.00165, 53, 24.14, -3.54, 0.01583, 54, 12.57, -6.67, 0.98106, 6, 32, -10.54, 42.74, 0.01827, 33, 33.91, 36.91, 0.09406, 45, 25.19, -17.97, 0.01577, 46, 23.16, -6.17, 0.35676, 44, 31.18, -24.05, 3e-05, 54, 18.93, -2.04, 0.51511, 4, 32, -21.51, 42.97, 0.00167, 33, 31.11, 47.52, 0.01381, 46, 19.02, 3.99, 0.83966, 44, 26.89, -13.96, 0.14486, 3, 46, 17.84, 14.05, 0.3227, 43, 8.7, -25.6, 2e-05, 44, 25.56, -3.91, 0.67729, 2, 46, 15.22, 21.81, 0.02954, 44, 22.83, 3.8, 0.97046, 1, 44, 17.12, 11.39, 1, 3, 43, 30.04, -11.3, 0.01553, 44, 11.49, 17.58, 0.98278, 38, 36.68, -27.17, 0.0017, 4, 37, 38.48, -26.56, 0.00197, 43, 32.67, -3.11, 0.10254, 44, 3.32, 20.29, 0.88275, 38, 36.46, -18.56, 0.01274, 4, 37, 39.7, -18.27, 0.01279, 43, 32.52, 5.27, 0.21485, 44, -5.07, 20.23, 0.74101, 38, 33.56, -10.7, 0.03135, 5, 37, 33.82, -12.07, 0.04786, 42, 11.75, 27.65, 0.00061, 43, 25.71, 10.43, 0.3519, 44, -10.29, 13.47, 0.52723, 38, 25.43, -8.08, 0.0724, 5, 37, 25.01, -6.98, 0.18374, 42, 5.49, 19.62, 0.03364, 43, 16.18, 14.02, 0.39186, 44, -13.98, 3.98, 0.10707, 38, 15.25, -7.83, 0.28369, 5, 37, 23.49, -1.98, 0.1412, 42, 0.33, 18.81, 0.01417, 43, 13.88, 18.7, 0.09356, 44, -18.69, 1.72, 0.00907, 38, 11.53, -4.17, 0.742, 2, 38, 18.84, -4.49, 0.40187, 39, -1.48, -4.48, 0.59813, 1, 39, 6.1, -11.03, 1, 2, 39, 14.04, -13.64, 0.96698, 40, -13.34, 3.46, 0.03302, 2, 39, 20.09, -7.92, 0.71444, 40, -10.4, -4.33, 0.28556, 2, 39, 23.28, -0.19, 0.26567, 40, -4.51, -10.25, 0.73433, 3, 39, 20.15, 7.11, 0.02034, 40, 3.44, -10.19, 0.97252, 49, 12.63, -18.5, 0.00714, 2, 40, 12.06, -10.43, 0.88628, 49, 7.98, -11.23, 0.11372, 3, 40, 19.03, -8.62, 0.40086, 41, -7.25, -4.67, 0.02406, 49, 2.58, -6.48, 0.57508, 2, 40, 13.66, -14.56, 0.03416, 49, 10.51, -7.58, 0.96584, 2, 40, 9.3, -20.97, 0.00078, 49, 18.26, -7.6, 0.99922, 1, 49, 20.25, -5.78, 1, 1, 49, 20.98, -1.25, 1, 2, 49, 15.09, 3.47, 0.99181, 50, -17.78, -6.25, 0.00819, 2, 49, 8.8, 6.5, 0.82994, 50, -10.83, -5.57, 0.17006, 2, 49, 1.54, 11.31, 0.15476, 50, -2.13, -5.92, 0.84524, 1, 50, 3.68, -6.38, 1, 1, 50, 13.85, -5.73, 1, 1, 50, 18.03, -2.17, 1, 1, 50, 17.43, 2.27, 1, 1, 50, 11, 3.95, 1, 4, 31, 45.03, -45.53, 0.00072, 41, 3.25, -17.7, 0.05015, 49, -8.04, 6.45, 0.03693, 50, 3.54, 3.2, 0.9122, 5, 31, 42.52, -47.53, 0.00555, 40, 32.84, -5.59, 0.00058, 41, 2.92, -14.49, 0.31667, 49, -7.68, 3.25, 0.21788, 50, 1.57, 5.75, 0.45931, 6, 29, -31.81, -44.04, 1e-05, 31, 40.24, -40.55, 0.0476, 36, 37.29, 33.87, 0.00409, 41, 10.02, -16.34, 0.74562, 49, -14.8, 5.03, 0.09128, 50, 8.58, 7.92, 0.11139, 7, 29, -23.34, -44.19, 0.00245, 31, 37.64, -32.5, 0.16148, 37, 15.93, 36.53, 0.00117, 36, 29.25, 36.52, 0.02407, 41, 18.21, -18.49, 0.75355, 49, -23.01, 7.1, 0.0222, 50, 16.68, 10.4, 0.03508, 7, 29, -17.87, -44.3, 0.00357, 31, 35.98, -27.28, 0.55075, 37, 10.73, 38.25, 0.00277, 36, 24.05, 38.24, 0.02535, 41, 23.5, -19.9, 0.40365, 49, -28.31, 8.46, 0.00423, 50, 21.92, 11.99, 0.00968, 1, 31, 34.56, -23.3, 1, 1, 31, 37.36, -20.36, 1, 1, 31, 38.87, -18.52, 1, 1, 31, 43.25, -18.68, 1, 1, 31, 47.7, -15.19, 1, 1, 31, 46.64, -11.73, 1, 1, 31, 50.8, -9.17, 1, 1, 31, 56.81, -6.49, 1, 1, 31, 59.66, -2.73, 1, 1, 31, 60.3, 0.77, 1, 1, 31, 58.41, 5.22, 1, 1, 31, 54.16, 6.79, 1, 1, 31, 48.54, 5.76, 1, 1, 31, 45.43, 3.24, 1, 1, 31, 44.77, 7.13, 1, 1, 31, 44.58, 12.38, 1, 1, 31, 46.05, 17.32, 1, 1, 31, 48.45, 21.47, 1, 1, 31, 49.9, 25.38, 1, 1, 31, 48.17, 29.48, 1, 1, 31, 44.63, 31.71, 1, 1, 31, 39.56, 32.28, 1, 1, 31, 35.04, 30.54, 1, 1, 31, 30.17, 27.63, 1, 1, 31, 28.39, 29.84, 1, 1, 31, 25.88, 30.98, 1, 1, 31, 24.43, 34.13, 1, 1, 31, 22.67, 36.3, 1, 1, 31, 18.66, 34.43, 1, 1, 31, 15.37, 32.13, 1, 1, 31, 11.71, 30.15, 1, 1, 31, 7.73, 27.88, 1, 1, 31, 4.6, 26.56, 1, 1, 31, 1.6, 24.94, 1, 1, 31, 0.19, 21.76, 1, 4, 34, -40.57, 2.43, 2e-05, 30, 22.19, 18.54, 0.02064, 31, -0.1, 18.76, 0.97912, 47, 23.08, -28.37, 0.00022, 4, 34, -37, 0.48, 0.00303, 30, 18.13, 18.85, 0.5347, 31, -4.16, 18.42, 0.45085, 47, 20.19, -25.5, 0.01142, 4, 34, -33.2, -0.55, 0.0092, 30, 14.25, 18.22, 0.66558, 31, -7.9, 17.19, 0.30332, 47, 16.82, -23.46, 0.02189, 5, 34, -30.6, 0.1, 0.01793, 30, 12.15, 16.55, 0.73844, 31, -9.71, 15.22, 0.20984, 48, 6.54, -21.91, 4e-05, 47, 14.14, -23.37, 0.03375, 5, 34, -28.98, 0.72, 0.0291, 30, 10.92, 15.32, 0.78717, 31, -10.72, 13.81, 0.13585, 48, 4.92, -22.54, 0.00019, 47, 12.41, -23.51, 0.04769, 5, 34, -26.85, -2.05, 0.06533, 30, 7.84, 16.96, 0.78916, 31, -14.02, 14.95, 0.04922, 48, 2.78, -19.78, 0.00128, 47, 11.13, -20.27, 0.09501, 5, 34, -24.05, -5.08, 0.10681, 30, 4.05, 18.57, 0.71984, 31, -18.03, 15.94, 0.01233, 48, -0.03, -16.76, 0.00464, 47, 9.29, -16.58, 0.15639, 6, 34, -20.56, -7.25, 0.14919, 29, 41.7, -3.23, 0.00241, 30, -0.03, 19.09, 0.59004, 31, -22.14, 15.82, 0.00204, 48, -3.53, -14.61, 0.01354, 47, 6.53, -13.53, 0.24277, 5, 34, -16.63, -8.26, 0.19229, 29, 41.7, 0.83, 0.00699, 30, -4.03, 18.4, 0.35793, 48, -7.47, -13.61, 0.03825, 47, 3.03, -11.47, 0.40455, 5, 34, -17.81, -10.35, 0.14149, 29, 44.02, 0.21, 0.00251, 30, -3.82, 20.79, 0.22176, 48, -6.29, -11.51, 0.10363, 47, 4.75, -9.78, 0.53061, 5, 34, -18.72, -13.72, 0.05964, 29, 47.51, 0.17, 0.00013, 30, -4.38, 24.24, 0.08719, 48, -5.39, -8.14, 0.26999, 47, 6.56, -6.8, 0.58305, 4, 34, -20.46, -15.71, 0.0105, 30, -3.61, 26.77, 0.01853, 48, -3.66, -6.15, 0.67714, 47, 8.78, -5.37, 0.29383, 4, 34, -20.58, -12.44, 0.00208, 30, -2.16, 23.84, 0.00612, 48, -3.53, -9.42, 0.91693, 47, 7.99, -8.54, 0.07488, 2, 30, 1.32, 24.26, 0.00121, 48, -0.19, -10.45, 0.99879, 1, 48, 3.88, -9.53, 1, 1, 48, 8.41, -8.4, 1, 1, 48, 12.09, -6.03, 1, 1, 48, 15.89, -4.92, 1, 1, 48, 21.62, -4.8, 1, 1, 48, 24.45, -2.71, 1, 1, 48, 23.99, 0.76, 1, 2, 48, 18.37, 4.38, 0.98543, 47, 32.88, -1.46, 0.01457, 2, 48, 16.72, 7.62, 0.9484, 47, 32.21, 2.11, 0.0516, 2, 48, 12.32, 8.41, 0.84707, 47, 28.21, 4.11, 0.15293, 2, 48, 3.79, 9.95, 0.16397, 47, 20.46, 7.99, 0.83603, 2, 35, -22.47, -38.71, 0.00015, 47, 11.89, 12.27, 0.99985, 3, 34, -8.62, -34.44, 0.00966, 35, -12.55, -37.65, 0.01653, 47, 2.59, 15.9, 0.97381, 3, 34, -1.31, -31.97, 0.07107, 35, -5.86, -33.78, 0.05721, 47, -5.13, 15.56, 0.87173, 3, 34, 5.22, -27.03, 0.19863, 35, -0.44, -27.66, 0.14368, 47, -12.76, 12.62, 0.65769, 3, 34, 10.54, -21.42, 0.30624, 35, 3.67, -21.11, 0.28681, 47, -19.43, 8.7, 0.40695, 3, 34, 15.36, -16.21, 0.28786, 35, 7.38, -15.05, 0.51001, 47, -25.5, 5.03, 0.20214, 1, 31, 27.58, -26.02, 1, 2, 31, 20.1, -27.18, 0.98707, 41, 32.14, -6.58, 0.01293, 1, 31, 13.98, -28.25, 1, 7, 29, -9.58, -17.61, 0.03275, 30, 22.9, -28.97, 0.00148, 31, 8.04, -28.06, 0.75927, 37, -5.07, 15.21, 0.03499, 36, 8.22, 15.21, 0.10699, 38, -21.78, -2.76, 0.0148, 41, 37.9, 4.05, 0.04972, 7, 29, -4.3, -14.21, 0.13777, 30, 18.64, -24.35, 0.013, 31, 3.11, -24.16, 0.53675, 37, -11.12, 13.51, 0.028, 36, 2.17, 13.53, 0.22353, 38, -26.28, -7.15, 0.01139, 41, 43.84, 6.1, 0.04957, 7, 29, -0.66, -13.62, 0.22426, 30, 17.45, -20.87, 0.03166, 31, 1.39, -20.91, 0.4896, 37, -14.77, 14.03, 0.016, 36, -1.48, 14.05, 0.19054, 38, -29.73, -8.44, 0.00719, 41, 47.51, 5.8, 0.04074, 7, 29, -6.38, -12.3, 0.19243, 30, 17.12, -26.73, 0.01218, 31, 1.98, -26.75, 0.1893, 37, -9.69, 11.07, 0.05591, 36, 3.6, 11.08, 0.45922, 38, -23.86, -8.61, 0.02051, 41, 42.27, 8.45, 0.07045, 7, 29, -13.25, -16.14, 0.05926, 30, 22.08, -32.85, 0.00128, 31, 7.84, -32.01, 0.20717, 37, -1.99, 12.72, 0.17959, 36, 11.3, 12.72, 0.33707, 38, -17.88, -3.48, 0.06894, 41, 34.68, 6.35, 0.1467, 6, 29, -19.2, -17.86, 0.02417, 31, 11.39, -37.08, 0.06739, 37, 4.19, 12.6, 0.28227, 36, 17.48, 12.6, 0.20068, 38, -12.4, -0.61, 0.1878, 41, 28.5, 6.09, 0.23769, 6, 29, -26.71, -18.72, 0.00341, 31, 14.63, -43.92, 0.01528, 37, 11.63, 11.2, 0.18151, 36, 24.92, 11.19, 0.03951, 38, -5.2, 1.72, 0.48954, 41, 20.99, 7.05, 0.27075, 7, 29, -19.42, -22.31, 0.02296, 31, 15.68, -35.86, 0.09876, 37, 5.72, 16.79, 0.16463, 36, 19.02, 16.79, 0.14912, 38, -13.06, 3.8, 0.15411, 41, 27.22, 1.82, 0.41025, 50, 13.64, 32.41, 0.00016, 8, 29, -14.91, -24.81, 0.01576, 30, 30.9, -33, 4e-05, 31, 16.58, -30.78, 0.69258, 37, 2.16, 20.51, 0.04053, 36, 15.45, 20.51, 0.06861, 38, -17.97, 5.35, 0.02848, 41, 31, -1.68, 0.15387, 50, 18.7, 31.43, 0.00014, 2, 31, 21.42, -26.98, 0.82541, 41, 31.6, -7.8, 0.17459, 8, 29, -18.32, -37.93, 0.00411, 31, 30.1, -29.76, 0.63831, 37, 9.28, 32.04, 0.00549, 36, 22.59, 32.03, 0.02429, 38, -17.24, 18.89, 0.00178, 41, 24.58, -13.61, 0.31756, 49, -29.33, 2.17, 0.00241, 50, 19.52, 17.9, 0.00606, 8, 29, -23.76, -37.56, 0.00349, 31, 31.51, -35.03, 0.13564, 37, 14.37, 30.08, 0.0047, 36, 27.68, 30.07, 0.02768, 38, -11.84, 19.6, 0.00083, 41, 19.38, -11.96, 0.78375, 49, -24.12, 0.56, 0.01702, 50, 14.24, 16.57, 0.0269, 6, 29, -28.55, -40.17, 0.00057, 31, 35.53, -38.72, 0.07562, 36, 33.03, 31.14, 0.00994, 41, 14.1, -13.36, 0.80113, 49, -18.85, 2.01, 0.05017, 50, 10.48, 12.6, 0.06257, 5, 31, 37.45, -45.77, 0.01877, 36, 39.86, 28.54, 0.00068, 41, 7.13, -11.18, 0.67951, 49, -11.86, -0.1, 0.15389, 50, 3.41, 10.78, 0.14715, 5, 31, 39.4, -54.86, 0.00043, 40, 24.87, -5.69, 0.12702, 41, -1.58, -7.92, 0.26021, 49, -3.12, -3.28, 0.57505, 50, -5.71, 8.97, 0.03729, 1, 41, 25.1, -6.27, 1, 1, 41, 18.81, 0.56, 1, 1, 41, 13.31, 3.6, 1, 1, 41, 9.64, -1.79, 1, 1, 41, 3.2, -0.43, 1, 2, 40, 12.88, -0.47, 0.99656, 49, -0.71, -16.14, 0.00344, 2, 40, 2.32, -1.52, 0.99995, 49, 6.07, -24.29, 5e-05, 2, 39, 10.55, -1.61, 0.99473, 40, -0.89, 2.04, 0.00527, 4, 38, 17.72, 1.7, 0.26482, 39, 1.26, 1.19, 0.68068, 40, 5.28, 9.52, 0.0505, 41, 0.26, 16.82, 0.004, 3, 38, 6.86, 0.3, 0.99293, 40, 13.83, 16.35, 0.00153, 41, 10.68, 13.47, 0.00554, 3, 37, 16.26, 5.41, 0.09946, 43, 5.54, 24.81, 8e-05, 38, 1.64, -1.15, 0.90046, 6, 32, -7.54, 10.26, 0.42386, 33, 3.5, 25.1, 0.10254, 29, -3.7, 17.72, 0.01891, 36, -7.86, -16.79, 0.07656, 45, -7.3, -15.11, 0.29431, 46, -5.47, -21.8, 0.08381, 6, 32, -12.45, 13.54, 0.23525, 33, 5.31, 30.73, 0.07652, 29, -9.6, 17.84, 0.00724, 36, -2.26, -18.65, 0.08191, 45, -3.19, -10.87, 0.48255, 46, -4.41, -15.99, 0.11653, 6, 32, -17.08, 12.65, 0.13285, 33, 3.18, 34.93, 0.03232, 29, -13.02, 14.59, 0.00526, 36, 1.97, -16.56, 0.11419, 45, -3.24, -6.15, 0.6543, 46, -7.06, -12.1, 0.06107, 6, 32, -14.84, 17.89, 0.13897, 33, 8.83, 34.22, 0.06905, 29, -13.96, 20.21, 0.0004, 36, 1.2, -22.21, 0.0282, 45, 1.51, -9.3, 0.55634, 46, -1.36, -12.08, 0.20704, 4, 32, -19.42, 22.45, 0.04442, 33, 11.96, 39.88, 0.03142, 45, 6.82, -5.6, 0.54471, 46, 1.01, -6.06, 0.37945, 4, 32, -15.9, 26.02, 0.06541, 33, 16.36, 37.47, 0.07236, 45, 9.7, -9.71, 0.27009, 46, 5.68, -7.88, 0.59214, 5, 32, -14.22, 30.22, 0.04707, 33, 20.86, 37.01, 0.08487, 45, 13.53, -12.11, 0.11375, 46, 10.2, -7.76, 0.59409, 54, 15.97, 10.67, 0.16022, 6, 32, -8.04, 26.13, 0.08866, 33, 18.62, 29.94, 0.13355, 36, -3.18, -31.95, 0.00101, 45, 8.4, -17.46, 0.13649, 46, 8.89, -15.05, 0.28157, 54, 8.58, 11.19, 0.35873, 6, 32, -2.17, 25.49, 0.07328, 33, 19.62, 24.13, 0.13901, 36, -9, -32.89, 0.00051, 45, 6.73, -23.12, 0.06672, 46, 10.64, -20.69, 0.1421, 54, 3.16, 8.85, 0.57839, 6, 32, -3.86, 21.05, 0.1757, 33, 14.89, 24.53, 0.19701, 36, -8.55, -28.16, 0.00519, 45, 2.66, -20.66, 0.16155, 46, 5.89, -20.9, 0.19434, 54, 2.44, 13.55, 0.26622, 6, 32, 2.44, 18.97, 0.20588, 33, 14.61, 17.9, 0.28997, 36, -15.18, -27.82, 0.0023, 45, -0.52, -26.48, 0.09475, 46, 6.47, -27.51, 0.12578, 54, -4.07, 12.26, 0.28133, 7, 32, 7.99, 16.19, 0.22834, 33, 13.46, 11.8, 0.4478, 36, -21.27, -26.61, 0.00051, 45, -4.25, -31.45, 0.05563, 46, 6.12, -33.71, 0.07868, 53, 1.3, 15.08, 0.07973, 54, -10.27, 11.94, 0.10932, 5, 32, 11.57, 13.14, 0.24767, 33, 11.52, 7.52, 0.66244, 45, -7.88, -34.43, 0.03341, 46, 4.75, -38.2, 0.04465, 53, -3.32, 15.96, 0.01184, 5, 32, 9.58, 9.48, 0.42633, 33, 7.45, 8.43, 0.50952, 36, -24.58, -20.56, 0.00017, 45, -11.13, -31.81, 0.03229, 46, 0.59, -37.83, 0.03169, 5, 32, 2.05, 14.67, 0.40051, 33, 10.37, 17.1, 0.3448, 36, -15.94, -23.58, 0.00703, 45, -4.68, -25.33, 0.12981, 46, 2.37, -28.86, 0.11785, 6, 32, -5.43, 15.7, 0.33753, 33, 9.3, 24.57, 0.19218, 29, -4.85, 23.43, 0.00049, 36, -8.45, -22.58, 0.02678, 45, -2.33, -18.16, 0.27617, 46, 0.35, -21.58, 0.16684, 6, 32, -8.02, 12.96, 0.3521, 33, 5.96, 26.31, 0.12447, 29, -5.55, 19.73, 0.008, 36, -6.69, -19.26, 0.05859, 45, -4.56, -15.12, 0.33418, 46, -3.19, -20.29, 0.12266, 1, 51, 4.33, 0.21, 1, 2, 54, 3.66, 16.69, 0.05492, 51, -1.66, 0.22, 0.94508, 1, 51, 1.12, 3.83, 1, 2, 54, -2.92, 14.79, 0.14018, 51, -8.24, -1.68, 0.85982, 2, 54, -10.15, 14.04, 0.00419, 51, -15.48, -2.43, 0.99581, 5, 32, 11.07, -6.16, 0.64051, 33, -7.18, 2.7, 0.00059, 34, 1.27, 8.65, 0.20007, 29, 20.83, 13.91, 0.15797, 30, -13.35, -4.4, 0.00086, 4, 32, 15.67, -2.33, 0.55146, 33, -2.23, -0.67, 0.21127, 34, 6.34, 5.47, 0.22211, 29, 22.64, 19.61, 0.01516, 4, 32, 19.63, -0.54, 0.09215, 33, 0.58, -3.99, 0.2347, 34, 9.27, 2.26, 0.6424, 35, -2.23, 1.86, 0.03075, 2, 33, 8.25, -4.31, 0.42071, 35, 5.31, 3.34, 0.57929, 2, 33, 12.18, -3.57, 0.57831, 35, 8.96, 4.97, 0.42169, 4, 33, 16.44, -6.68, 0.15043, 35, 13.82, 2.94, 0.46673, 52, -5.7, 5.5, 0.2755, 53, -15.96, 7.85, 0.10734, 7, 32, 27.51, 20.75, 3e-05, 33, 23.22, -5.72, 0.02498, 35, 20.19, 5.45, 0.02893, 45, -3.25, -51.47, 4e-05, 46, 18.05, -49.82, 0.00129, 52, -3.18, -0.86, 0.87479, 53, -13.44, 1.49, 0.06994, 1, 52, -2.88, -6.31, 1, 1, 32, 4.53, -6.33, 1, 5, 32, -3.64, -4.88, 0.99695, 34, -2.06, 23.04, 0, 29, 7.74, 7.06, 0.003, 36, -15.63, -3.22, 2e-05, 45, -22.89, -16.24, 3e-05, 6, 32, -8.74, -0.19, 0.70545, 33, -6.88, 23.39, 0.00429, 29, 0.92, 8.26, 0.18067, 36, -9.47, -6.39, 0.05963, 45, -17.37, -12.06, 0.04662, 46, -15.54, -24.84, 0.00334, 6, 32, -9.65, 4.96, 0.41852, 33, -2.17, 25.68, 0.03707, 29, -2.62, 12.12, 0.116, 36, -7.23, -11.12, 0.16707, 45, -12.14, -12.09, 0.23205, 46, -11.17, -21.97, 0.02929, 5, 32, -13.93, -6.64, 0.00077, 33, -14.5, 26.6, 0, 29, 0.03, 0.04, 0.99902, 45, -22.79, -5.81, 0.0002, 46, -23.52, -22.64, 0, 7, 29, -5.9, -1.01, 0.03381, 30, 5.92, -28.19, 0.00034, 31, -8.85, -29.94, 0.00168, 37, -13.49, 0.44, 0.00031, 36, -0.21, 0.45, 0.96295, 38, -22.09, -19.76, 0.00019, 41, 45.42, 19.3, 0.00073, 7, 29, -13.72, -3.56, 0.00292, 30, 9.77, -35.45, 4e-05, 31, -3.91, -36.52, 0.00171, 37, -5.26, 0.57, 0.00566, 36, 8.02, 0.57, 0.98675, 38, -14.93, -15.71, 0.00115, 41, 37.22, 18.68, 0.00178, 5, 29, -21.66, -5.81, 0.00032, 31, 0.78, -43.31, 0.00065, 37, 2.99, 0.36, 0.99403, 38, -7.59, -11.93, 0.00341, 41, 28.96, 18.38, 0.00159, 1, 37, 10.92, 0.13, 1, 5, 37, 17.48, -1.29, 0.53255, 42, -1.17, 12.95, 0.03129, 43, 7.84, 18.4, 0.07874, 44, -18.45, -4.32, 0.0016, 38, 5.92, -6.44, 0.35582, 3, 37, 13.55, -6.3, 0.50806, 42, 3.25, 8.37, 0.2887, 43, 4.77, 12.82, 0.20324, 3, 37, 10.15, -12.09, 0.10485, 42, 8.51, 4.2, 0.6121, 43, 2.36, 6.55, 0.28305, 3, 37, 8.03, -17.95, 0.00085, 42, 14.03, 1.29, 0.28716, 43, 1.22, 0.42, 0.71199, 4, 37, 13.71, -17.66, 0.02163, 42, 14.52, 6.96, 0.04769, 43, 6.78, 1.64, 0.92867, 38, 10.45, -22.62, 0.00201, 5, 37, 20.48, -19.13, 0.01388, 42, 16.91, 13.46, 0.00424, 43, 13.7, 1.29, 0.51259, 44, -1.27, 1.36, 0.4587, 38, 17.1, -20.67, 0.01058, 2, 43, 12.26, -4.62, 0.00037, 44, 4.62, -0.14, 0.99963, 3, 46, 2.68, 17.4, 0.01654, 43, 11.67, -10.37, 0.01667, 44, 10.36, -0.78, 0.96679, 3, 46, 8.56, 15.92, 0.08715, 43, 10.34, -16.28, 0.01347, 44, 16.25, -2.18, 0.89939, 7, 32, -23.44, 14.2, 0.00474, 33, 2.92, 41.47, 0.00087, 29, -19.21, 12.47, 1e-05, 36, 8.51, -16.37, 0.01523, 45, -0.59, -0.18, 0.97477, 46, -8.16, -5.65, 0.00172, 42, 10.71, -11.15, 0.00266, 5, 32, -23.6, 20.26, 0.00555, 33, 8.71, 43.29, 0.00303, 36, 10.27, -22.17, 0.00017, 45, 5.41, -1.1, 0.95718, 46, -2.66, -3.09, 0.03406, 5, 45, 10.29, 0.77, 0.28016, 46, 0.36, 1.17, 0.65194, 42, 20.77, -6.92, 0.01013, 43, -4.62, -8.46, 0.04381, 44, 8.28, -17.05, 0.01397, 3, 46, 4.92, 0.23, 0.9942, 43, -5.45, -13.03, 0.00304, 44, 12.85, -17.93, 0.00276, 5, 32, -18.51, 35.54, 0.00942, 33, 24.8, 42.6, 0.03315, 45, 19.54, -8.84, 0.01156, 46, 13.39, -1.7, 0.94519, 44, 21.34, -19.73, 0.00069, 6, 32, -15.3, 38.82, 0.01641, 33, 28.83, 40.41, 0.07253, 45, 22.18, -12.58, 0.01584, 46, 17.66, -3.36, 0.67217, 44, 25.65, -21.32, 0.005, 54, 21.14, 3.72, 0.21805, 7, 32, -22.52, 9.28, 0.0358, 33, -1.56, 39.24, 0.00279, 29, -15.79, 8.81, 0.00064, 36, 6.33, -11.87, 0.27187, 45, -5.59, -0.19, 0.59736, 46, -12.32, -8.43, 0.00201, 42, 5.95, -12.7, 0.08952, 4, 32, -27.48, 10.06, 0.00213, 36, 11.31, -11.31, 0.16162, 45, -3.93, 4.54, 0.41552, 42, 6.08, -7.68, 0.42074, 4, 36, 12.68, -17.89, 0.00279, 45, 2.59, 2.93, 0.72755, 42, 12.78, -7.22, 0.24372, 43, -7.28, -0.92, 0.02593, 5, 45, 8.69, 4.48, 0.33891, 46, -3.02, 3.37, 0.23815, 42, 18.12, -3.88, 0.11908, 43, -2.5, -5.02, 0.27261, 44, 4.86, -14.9, 0.03126, 5, 45, 13.08, 6.75, 0.05138, 46, -0.63, 7.69, 0.32922, 42, 21.6, -0.38, 0.00459, 43, 1.88, -7.3, 0.41448, 44, 7.19, -10.55, 0.20033, 4, 45, 18.41, 3.66, 0.00402, 46, 5.52, 8.08, 0.53563, 43, 2.42, -13.44, 0.12515, 44, 13.34, -10.07, 0.33519, 3, 46, 11.46, 9.54, 0.51653, 43, 4.03, -19.34, 0.02379, 44, 19.25, -8.52, 0.45968, 6, 29, 10.55, -1.51, 0.96304, 30, 3.6, -11.9, 0.03132, 31, -13.7, -14.22, 0.00454, 36, -15.77, 5.8, 0.00071, 38, -38.31, -22.54, 0, 41, 61.27, 14.9, 0.00039, 2, 29, 20.69, -0.58, 0.84632, 30, 0.95, -2.06, 0.15368, 5, 32, 10.39, -19.48, 0.02099, 34, -11.6, 5.17, 0.18485, 29, 27.43, 2.32, 0.29592, 30, -3.06, 4.09, 0.45135, 47, -5.51, -22.98, 0.04688, 6, 32, 14.97, -21.62, 0.00277, 34, -12.21, 0.16, 0.3014, 29, 32.44, 2.99, 0.10398, 30, -4.58, 8.9, 0.44656, 48, -11.85, -22.05, 0.00156, 47, -3.54, -18.34, 0.14373, 6, 34, -10.41, -9.14, 0.30781, 35, -19.28, -13.2, 0.00186, 29, 40.99, 7.07, 0.01018, 30, -10.06, 16.63, 0.17551, 48, -13.69, -12.76, 0.01272, 47, -2.7, -8.9, 0.49192, 5, 34, -2.64, -8.13, 0.60487, 35, -11.86, -10.67, 0.03294, 29, 38.06, 14.33, 0.00257, 30, -16.72, 12.5, 0.04413, 47, -10.45, -7.72, 0.31549, 4, 34, 4.22, -6.83, 0.7227, 35, -5.39, -8.05, 0.12912, 30, -22.43, 8.49, 0.00053, 47, -17.39, -7.07, 0.14764, 3, 34, 12.41, -9.31, 0.32617, 35, 3.13, -8.87, 0.55979, 47, -24.58, -2.42, 0.11404, 3, 34, 6.84, -14.58, 0.40169, 35, -1.29, -15.13, 0.27845, 47, -17.77, 1.1, 0.31986, 4, 34, -1.25, -18.52, 0.2482, 35, -8.46, -20.59, 0.09632, 30, -22.27, 21.4, 0.00134, 47, -8.9, 2.65, 0.65414, 3, 34, -6.51, -19.79, 0.08622, 35, -13.36, -22.87, 0.03079, 47, -3.5, 2.42, 0.88299, 2, 35, -21.08, -26.14, 0.00035, 47, 4.86, 1.76, 0.99965, 1, 47, 10.03, 0.59, 1, 1, 48, 2.08, -0.46, 1, 1, 48, 9.03, -0.43, 1, 1, 48, 16.38, -0.48, 1, 3, 30, 10.19, -0.06, 0.99997, 36, -23.31, 17.06, 3e-05, 41, 69.48, 4.12, 1e-05, 4, 34, -25.72, 6.78, 0.01809, 30, 10.46, 8.45, 0.89366, 31, -10.1, 6.95, 0.05929, 47, 7.6, -28.44, 0.02896, 7, 29, 11.82, -11.52, 0.3788, 30, 13.24, -8.93, 0.392, 31, -4.63, -9.77, 0.19241, 37, -27.31, 15.72, 0.00041, 36, -14.02, 15.74, 0.02797, 38, -41.55, -12.97, 0.00046, 41, 60.13, 4.87, 0.00794, 6, 34, -20.45, -0.49, 0.11232, 29, 35.14, -4.82, 0.00558, 30, 2.65, 12.9, 0.74352, 31, -18.51, 10.12, 0.00658, 48, -3.61, -21.37, 0.00263, 47, 4.55, -19.99, 0.12937, 7, 29, -1.89, -6.6, 0.50083, 30, 10.74, -23.28, 0.02126, 31, -4.86, -24.35, 0.06095, 37, -15.66, 6.96, 0.00709, 36, -2.38, 6.97, 0.38574, 38, -27.12, -15.08, 0.00406, 41, 47.98, 12.92, 0.02007, 1, 31, 35.34, -5.65, 1, 1, 31, 26.42, -9.65, 1, 1, 31, 14.07, -15.21, 1, 1, 31, 6.35, -16.65, 1, 1, 31, 2.47, -12.59, 1, 1, 31, 1.54, -2.83, 1, 4, 34, -33.26, 13.31, 8e-05, 30, 20.02, 5.62, 0.46766, 31, -0.22, 5.65, 0.53014, 47, 13.04, -36.8, 0.00212, 1, 31, 17.23, 26.14, 1, 1, 31, 22.98, 7.14, 1, 1, 31, 18.52, -4.44, 1, 1, 31, 10.19, 2.87, 1, 1, 31, 9.39, 18.26, 1, 1, 31, 30.58, 19.35, 1, 1, 31, 35.53, 3.32, 1, 1, 31, 39.13, 24.22, 1, 1, 31, 50.92, -5.57, 1, 1, 31, 35.95, -13.03, 1, 1, 31, 21.79, -20.27, 1], "hull": 112, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 0, 222, 92, 224, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 224, 248, 248, 226, 246, 248, 224, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 56, 224, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 282, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 8, 326, 342, 342, 344, 344, 346, 346, 348, 348, 282, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 378, 380, 380, 382, 382, 384, 384, 386, 386, 388, 286, 390, 390, 392, 392, 394, 394, 396, 396, 398, 398, 400, 400, 402, 402, 24, 350, 404, 404, 406, 406, 408, 408, 410, 178, 412, 412, 414, 414, 416, 416, 418, 418, 0, 418, 420, 420, 422, 422, 424, 424, 426, 118, 446, 446, 448, 448, 450, 450, 452, 452, 454], "width": 138, "height": 135}}, "baojian2": {"baojian": {"x": 104.9, "y": -5.46, "rotation": -89.44, "width": 61, "height": 227}}, "baojian": {"baojian": {"x": 104.9, "y": -5.46, "rotation": -89.44, "width": 61, "height": 227}}}}], "animations": {"animation1": {"slots": {"qiling": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "color": "ffffff41", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "color": "ffffff41", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "color": "ffffff41", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "baojian": {"attachment": [{"name": null}]}, "tu": {"attachment": [{"name": null}]}, "baojian2": {"color": [{"color": "00b0ff00"}]}, "fabao_dzzjx_skill_ccyan1_2": {"twoColor": [{"light": "3b00ffa9", "dark": "0099ff"}], "attachment": [{"name": null}]}, "fabao_dzzjx_skill_ccyan1_3": {"twoColor": [{"light": "3a00ff00", "dark": "0099ff"}]}}, "bones": {"bone": {"translate": [{"x": 173.96, "y": -180.24}], "scale": [{"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.193, "y": 1.193}]}, "bone3": {"rotate": [{"angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2667, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.78}]}, "bone4": {"rotate": [{"angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.8667, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 0.14}]}, "bone7": {"rotate": [{"angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 1.4667, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.7333, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.8, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.0667, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.7333, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -0.63}]}, "bone8": {"rotate": [{"angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.4667, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.8, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.3333, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 2.41}]}, "bone21": {"rotate": [{"angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 5.92}], "translate": [{"x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "x": 3.49, "y": -0.66}]}, "bone10": {"rotate": [{"angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1333, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 1.4667, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.8, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 2.8, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1333, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -3.84}]}, "bone9": {"rotate": [{"angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1333, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 1.4667, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.6, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.8, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -0.59}]}, "bone20": {"rotate": [{"angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.5}]}, "bone22": {"translate": [{"x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "x": 0.48, "y": -0.09}]}, "bone23": {"translate": [{"x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "x": -1.22, "y": 0.8}]}, "bone31": {"rotate": [{"angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.8667, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 0.14}]}, "bone30": {"rotate": [{"angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2667, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.78}]}, "bone41": {"rotate": [{"angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1333, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 1.4667, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.8, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 2.8, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1333, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -3.84}]}, "bone40": {"rotate": [{"angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1333, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 1.4667, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.6, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.8, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -0.59}]}, "bone39": {"rotate": [{"angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.4667, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.8, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.3333, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 2.41}]}, "bone38": {"rotate": [{"angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 1.4667, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.7333, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.8, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.0667, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.7333, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -0.63}]}, "bone48": {"rotate": [{"angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 5.92}], "translate": [{"x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "x": 3.49, "y": -0.66}]}, "bone47": {"rotate": [{"angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.5}]}, "bone50": {"translate": [{"x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "x": -1.22, "y": 0.8}]}, "bone49": {"translate": [{"x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "x": 0.48, "y": -0.09}]}, "bone28": {"translate": [{"x": 173.96, "y": -180.24}]}, "bone56": {"translate": [{"x": 173.96, "y": -180.24}]}}}, "animation2": {"slots": {"qiling": {"attachment": [{"name": null}]}, "baojian2": {"color": [{"color": "00b0ff00", "curve": "stepped"}, {"time": 2.5333, "color": "00b0ff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "color": "00b0ffff", "curve": 0.25, "c3": 0.75}, {"time": 3, "color": "00b0ff00"}]}, "fabao_dzzjx_skill_ccyan1_2": {"twoColor": [{"light": "3b00ffa9", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "light": "3a00ff00", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "light": "3b00ffa9", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 2, "light": "3a00ff00", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "light": "3b00ffa9", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "light": "3a00ff00", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 4, "light": "3b00ffa9", "dark": "0099ff"}], "attachment": [{"name": "fabao_dzzjx_skill_ccyan1_24"}, {"time": 0.0667, "name": "fabao_dzzjx_skill_ccyan1_26"}, {"time": 0.1333, "name": "fabao_dzzjx_skill_ccyan1_28"}, {"time": 0.1667, "name": "fabao_dzzjx_skill_ccyan1_30"}, {"time": 0.2333, "name": "fabao_dzzjx_skill_ccyan1_32"}, {"time": 0.3, "name": "fabao_dzzjx_skill_ccyan1_34"}, {"time": 0.3667, "name": "fabao_dzzjx_skill_ccyan1_36"}, {"time": 0.4333, "name": "fabao_dzzjx_skill_ccyan1_38"}, {"time": 0.5, "name": "fabao_dzzjx_skill_ccyan1_40"}, {"time": 0.5667, "name": "fabao_dzzjx_skill_ccyan1_42"}, {"time": 0.6333, "name": "fabao_dzzjx_skill_ccyan1_44"}, {"time": 0.6667, "name": "fabao_dzzjx_skill_ccyan1_02"}, {"time": 0.7333, "name": "fabao_dzzjx_skill_ccyan1_04"}, {"time": 0.8, "name": "fabao_dzzjx_skill_ccyan1_06"}, {"time": 0.8667, "name": "fabao_dzzjx_skill_ccyan1_08"}, {"time": 0.9333, "name": "fabao_dzzjx_skill_ccyan1_10"}, {"time": 1, "name": "fabao_dzzjx_skill_ccyan1_12"}, {"time": 1.0667, "name": "fabao_dzzjx_skill_ccyan1_14"}, {"time": 1.1333, "name": "fabao_dzzjx_skill_ccyan1_16"}, {"time": 1.2, "name": "fabao_dzzjx_skill_ccyan1_18"}, {"time": 1.2667, "name": "fabao_dzzjx_skill_ccyan1_20"}, {"time": 1.3333, "name": "fabao_dzzjx_skill_ccyan1_24"}, {"time": 1.4, "name": "fabao_dzzjx_skill_ccyan1_26"}, {"time": 1.4667, "name": "fabao_dzzjx_skill_ccyan1_28"}, {"time": 1.5, "name": "fabao_dzzjx_skill_ccyan1_30"}, {"time": 1.5667, "name": "fabao_dzzjx_skill_ccyan1_32"}, {"time": 1.6333, "name": "fabao_dzzjx_skill_ccyan1_34"}, {"time": 1.7, "name": "fabao_dzzjx_skill_ccyan1_36"}, {"time": 1.7667, "name": "fabao_dzzjx_skill_ccyan1_38"}, {"time": 1.8333, "name": "fabao_dzzjx_skill_ccyan1_40"}, {"time": 1.9, "name": "fabao_dzzjx_skill_ccyan1_42"}, {"time": 1.9667, "name": "fabao_dzzjx_skill_ccyan1_44"}, {"time": 2, "name": "fabao_dzzjx_skill_ccyan1_02"}, {"time": 2.0667, "name": "fabao_dzzjx_skill_ccyan1_04"}, {"time": 2.1333, "name": "fabao_dzzjx_skill_ccyan1_06"}, {"time": 2.2, "name": "fabao_dzzjx_skill_ccyan1_08"}, {"time": 2.2667, "name": "fabao_dzzjx_skill_ccyan1_10"}, {"time": 2.3333, "name": "fabao_dzzjx_skill_ccyan1_12"}, {"time": 2.4, "name": "fabao_dzzjx_skill_ccyan1_14"}, {"time": 2.4667, "name": "fabao_dzzjx_skill_ccyan1_16"}, {"time": 2.5333, "name": "fabao_dzzjx_skill_ccyan1_18"}, {"time": 2.6, "name": "fabao_dzzjx_skill_ccyan1_20"}, {"time": 2.6667, "name": "fabao_dzzjx_skill_ccyan1_24"}, {"time": 2.7333, "name": "fabao_dzzjx_skill_ccyan1_26"}, {"time": 2.8, "name": "fabao_dzzjx_skill_ccyan1_28"}, {"time": 2.8333, "name": "fabao_dzzjx_skill_ccyan1_30"}, {"time": 2.9, "name": "fabao_dzzjx_skill_ccyan1_32"}, {"time": 2.9667, "name": "fabao_dzzjx_skill_ccyan1_34"}, {"time": 3.0333, "name": "fabao_dzzjx_skill_ccyan1_36"}, {"time": 3.1, "name": "fabao_dzzjx_skill_ccyan1_38"}, {"time": 3.1667, "name": "fabao_dzzjx_skill_ccyan1_40"}, {"time": 3.2333, "name": "fabao_dzzjx_skill_ccyan1_42"}, {"time": 3.3, "name": "fabao_dzzjx_skill_ccyan1_44"}, {"time": 3.3333, "name": "fabao_dzzjx_skill_ccyan1_02"}, {"time": 3.4, "name": "fabao_dzzjx_skill_ccyan1_04"}, {"time": 3.4667, "name": "fabao_dzzjx_skill_ccyan1_06"}, {"time": 3.5333, "name": "fabao_dzzjx_skill_ccyan1_08"}, {"time": 3.6, "name": "fabao_dzzjx_skill_ccyan1_10"}, {"time": 3.6667, "name": "fabao_dzzjx_skill_ccyan1_12"}, {"time": 3.7333, "name": "fabao_dzzjx_skill_ccyan1_14"}, {"time": 3.8, "name": "fabao_dzzjx_skill_ccyan1_16"}, {"time": 3.8667, "name": "fabao_dzzjx_skill_ccyan1_18"}, {"time": 3.9333, "name": "fabao_dzzjx_skill_ccyan1_20"}, {"time": 4, "name": "fabao_dzzjx_skill_ccyan1_22"}]}, "fabao_dzzjx_skill_ccyan1_3": {"twoColor": [{"light": "3a00ff00", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "light": "3b00ffa9", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "light": "3a00ff00", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 2, "light": "3b00ffa9", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "light": "3a00ff00", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "light": "3b00ffa9", "dark": "0099ff", "curve": 0.25, "c3": 0.75}, {"time": 4, "light": "3a00ff00", "dark": "0099ff"}], "attachment": [{"time": 0.0333, "name": "fabao_dzzjx_skill_ccyan1_02"}, {"time": 0.1, "name": "fabao_dzzjx_skill_ccyan1_04"}, {"time": 0.1667, "name": "fabao_dzzjx_skill_ccyan1_06"}, {"time": 0.2333, "name": "fabao_dzzjx_skill_ccyan1_08"}, {"time": 0.3, "name": "fabao_dzzjx_skill_ccyan1_10"}, {"time": 0.3667, "name": "fabao_dzzjx_skill_ccyan1_12"}, {"time": 0.4333, "name": "fabao_dzzjx_skill_ccyan1_14"}, {"time": 0.5, "name": "fabao_dzzjx_skill_ccyan1_16"}, {"time": 0.5667, "name": "fabao_dzzjx_skill_ccyan1_18"}, {"time": 0.6333, "name": "fabao_dzzjx_skill_ccyan1_20"}, {"time": 0.7, "name": "fabao_dzzjx_skill_ccyan1_24"}, {"time": 0.7667, "name": "fabao_dzzjx_skill_ccyan1_26"}, {"time": 0.8333, "name": "fabao_dzzjx_skill_ccyan1_28"}, {"time": 0.8667, "name": "fabao_dzzjx_skill_ccyan1_30"}, {"time": 0.9333, "name": "fabao_dzzjx_skill_ccyan1_32"}, {"time": 1, "name": "fabao_dzzjx_skill_ccyan1_34"}, {"time": 1.0667, "name": "fabao_dzzjx_skill_ccyan1_36"}, {"time": 1.1333, "name": "fabao_dzzjx_skill_ccyan1_38"}, {"time": 1.2, "name": "fabao_dzzjx_skill_ccyan1_40"}, {"time": 1.2667, "name": "fabao_dzzjx_skill_ccyan1_42"}, {"time": 1.3333, "name": "fabao_dzzjx_skill_ccyan1_44"}, {"time": 1.3667, "name": "fabao_dzzjx_skill_ccyan1_02"}, {"time": 1.4333, "name": "fabao_dzzjx_skill_ccyan1_04"}, {"time": 1.5, "name": "fabao_dzzjx_skill_ccyan1_06"}, {"time": 1.5667, "name": "fabao_dzzjx_skill_ccyan1_08"}, {"time": 1.6333, "name": "fabao_dzzjx_skill_ccyan1_10"}, {"time": 1.7, "name": "fabao_dzzjx_skill_ccyan1_12"}, {"time": 1.7667, "name": "fabao_dzzjx_skill_ccyan1_14"}, {"time": 1.8333, "name": "fabao_dzzjx_skill_ccyan1_16"}, {"time": 1.9, "name": "fabao_dzzjx_skill_ccyan1_18"}, {"time": 1.9667, "name": "fabao_dzzjx_skill_ccyan1_20"}, {"time": 2.0333, "name": "fabao_dzzjx_skill_ccyan1_22"}, {"time": 2.1, "name": "fabao_dzzjx_skill_ccyan1_24"}, {"time": 2.1667, "name": "fabao_dzzjx_skill_ccyan1_26"}, {"time": 2.2333, "name": "fabao_dzzjx_skill_ccyan1_28"}, {"time": 2.2667, "name": "fabao_dzzjx_skill_ccyan1_30"}, {"time": 2.3333, "name": "fabao_dzzjx_skill_ccyan1_32"}, {"time": 2.4, "name": "fabao_dzzjx_skill_ccyan1_34"}, {"time": 2.4667, "name": "fabao_dzzjx_skill_ccyan1_36"}, {"time": 2.5333, "name": "fabao_dzzjx_skill_ccyan1_38"}, {"time": 2.6, "name": "fabao_dzzjx_skill_ccyan1_40"}, {"time": 2.6667, "name": "fabao_dzzjx_skill_ccyan1_42"}, {"time": 2.7333, "name": "fabao_dzzjx_skill_ccyan1_44"}, {"time": 2.7667, "name": "fabao_dzzjx_skill_ccyan1_02"}, {"time": 2.8333, "name": "fabao_dzzjx_skill_ccyan1_04"}, {"time": 2.9, "name": "fabao_dzzjx_skill_ccyan1_06"}, {"time": 2.9667, "name": "fabao_dzzjx_skill_ccyan1_08"}, {"time": 3.0333, "name": "fabao_dzzjx_skill_ccyan1_10"}, {"time": 3.1, "name": "fabao_dzzjx_skill_ccyan1_12"}, {"time": 3.1667, "name": "fabao_dzzjx_skill_ccyan1_14"}, {"time": 3.2333, "name": "fabao_dzzjx_skill_ccyan1_16"}, {"time": 3.3, "name": "fabao_dzzjx_skill_ccyan1_18"}, {"time": 3.3667, "name": "fabao_dzzjx_skill_ccyan1_20"}, {"time": 3.4333, "name": "fabao_dzzjx_skill_ccyan1_24"}, {"time": 3.5, "name": "fabao_dzzjx_skill_ccyan1_26"}, {"time": 3.5667, "name": "fabao_dzzjx_skill_ccyan1_28"}, {"time": 3.6, "name": "fabao_dzzjx_skill_ccyan1_30"}, {"time": 3.6667, "name": "fabao_dzzjx_skill_ccyan1_32"}, {"time": 3.7333, "name": "fabao_dzzjx_skill_ccyan1_34"}, {"time": 3.8, "name": "fabao_dzzjx_skill_ccyan1_36"}, {"time": 3.8667, "name": "fabao_dzzjx_skill_ccyan1_38"}, {"time": 3.9333, "name": "fabao_dzzjx_skill_ccyan1_40"}, {"time": 4, "name": "fabao_dzzjx_skill_ccyan1_42"}]}, "qiling2": {"attachment": [{"name": null}]}}, "bones": {"bone3": {"rotate": [{"angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2667, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.78}]}, "bone4": {"rotate": [{"angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.8667, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 0.14}]}, "bone7": {"rotate": [{"angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 1.4667, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.7333, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.8, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.0667, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.7333, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -0.63}]}, "bone8": {"rotate": [{"angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.4667, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.8, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.3333, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 2.41}]}, "bone21": {"rotate": [{"angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 5.92}], "translate": [{"x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "x": 3.49, "y": -0.66}]}, "bone10": {"rotate": [{"angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1333, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 1.4667, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.8, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 2.8, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1333, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -3.84}]}, "bone9": {"rotate": [{"angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1333, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 1.4667, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.6, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.8, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -0.59}]}, "bone20": {"rotate": [{"angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.5}]}, "bone22": {"translate": [{"x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "x": 0.48, "y": -0.09}]}, "bone23": {"translate": [{"x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "x": -1.22, "y": 0.8}]}, "bone31": {"rotate": [{"angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.8667, "angle": -0.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 0.14}]}, "bone30": {"rotate": [{"angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2667, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 2.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "angle": 4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6, "angle": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.78}]}, "bone41": {"rotate": [{"angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1333, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 1.4667, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.8, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -3.84, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 2.8, "angle": -1.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1333, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -5.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -3.84}]}, "bone40": {"rotate": [{"angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1333, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 1.4667, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.6, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.59, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.8, "angle": 1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -5.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -0.59}]}, "bone39": {"rotate": [{"angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.4667, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.8, "angle": 1.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.3333, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 2.41}]}, "bone38": {"rotate": [{"angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 1.4667, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.7333, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -0.63, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.8, "angle": -2.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.0667, "angle": -5.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.7333, "angle": 2.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -0.63}]}, "bone48": {"rotate": [{"angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 5.92, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 6.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 5.92}], "translate": [{"x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 3.49, "y": -0.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 4.01, "y": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "x": 3.49, "y": -0.66}]}, "bone47": {"rotate": [{"angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 2.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 6.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.5}]}, "bone50": {"translate": [{"x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "x": -1.22, "y": 0.8, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -5.05, "y": 3.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "x": -1.22, "y": 0.8}]}, "bone49": {"translate": [{"x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 0.48, "y": -0.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.2, "x": 3.72, "y": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "x": 0.48, "y": -0.09}]}, "bone55": {"rotate": [{"time": 2.5667, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2.6, "angle": 0.71, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 2.6667, "angle": -0.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.7, "curve": "stepped"}, {"time": 2.8459, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2.8792, "angle": 0.71, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 2.9459, "angle": -0.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.9792}]}, "bone58": {"scale": [{"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.073, "y": 1.605}]}}}}}