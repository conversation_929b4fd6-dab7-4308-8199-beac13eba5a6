
CCEffect %{
techniques:
  - passes:
      - vert: sprite-vs:vert
        frag: sprite-fs:frag
        depthStencilState:
          depthTest: false
          depthWrite: false
        blendState:
          targets:
            - blend: true
              blendSrc: src_alpha
              blendDst: one_minus_src_alpha
              blendDstAlpha: one_minus_src_alpha
        rasterizerState:
          cullMode: none
        properties:
          mask: { value: white }
          # 强度
          strength: { value: 1.0 }

}%

CCProgram sprite-vs %{
precision highp float;
#include <builtin/uniforms/cc-global>
#include <common/common-define>
#if USE_LOCAL
  #include <builtin/uniforms/cc-local>
#endif

in vec3 a_position;
in vec2 a_texCoord;
in vec4 a_color;

out vec4 v_color;
out vec2 v_uv0;

#if USE_TEXTURE
  in vec2 a_uv0;
#endif

vec4 vert() {
  vec4 pos = vec4(a_position, 1);
  
  #if USE_LOCAL
    pos = cc_matWorld * pos;
  #endif
  
  #if USE_PIXEL_ALIGNMENT
    pos = cc_matView * pos;
    pos.xyz = floor(pos.xyz);
    pos = cc_matProj * pos;
  #else
    pos = cc_matViewProj * pos;
  #endif
  v_color = a_color;
  v_uv0 = a_texCoord;
  #if SAMPLE_FROM_RT
    CC_HANDLE_RT_SAMPLE_FLIP(v_uv0);
  #endif
  
  return pos;
}
}%

CCProgram sprite-fs %{
precision highp float;
#include <builtin/internal/embedded-alpha>
#include <builtin/internal/alpha-test>
#include <builtin/uniforms/cc-global>

in vec4 v_color;

#if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D mask;
  #pragma builtin(local)
  layout(set = 2, binding = 11)uniform sampler2D cc_spriteTexture;
#endif

uniform ARGS {
  float strength;
};
const float PI = 3.1415926535;

vec4 frag() {
  vec2 uv = v_uv0;
  vec4 o = CCSampleWithAlphaSeparated(cc_spriteTexture, uv);
  vec4 maskColor = texture(mask, uv);
  // vec4 mainColor = texture(cc_spriteTexture, uv);
  o.a *= maskColor.r;
  return o;
}


}%
