import { Node } from "cc";
import { JsonMgr } from "../../../game/mgr/JsonMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { EventActionModule } from "./EventActionModule";
import Logger, { LOG_LEVEL } from "../../../lib/utils/Logger";
import ResMgr from "../../../lib/common/ResMgr";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const log = Logger.getLoger(LOG_LEVEL.WARN);
/**
 * 模块逻辑处理
 */
export class EventActionService {
  private _checkTimer: number;
  private _lastTriggerDay: number = -1;

  private _attachEventMap: Map<number, Node> = new Map();
  public init() {
    // 初始化时立即检查一次
    this.checkTime();
    this._checkTimer = TickerMgr.setInterval(60, () => this.checkTime(), false);
  }

  // 新增时间检查方法
  private checkTime() {
    let db = JsonMgr.instance.jsonList.c_event2[101];

    const now = new Date(TimeUtils.serverTime);
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentDay = now.getDate();

    // 精确判断整点（允许误差1分钟内）
    const isExactTime = (currentHour === db.time[0] || currentHour === db.time[1]) && currentMinute < 1;

    // 每天只触发一次，且满足整点条件
    if (isExactTime && currentDay !== this._lastTriggerDay) {
      this._lastTriggerDay = currentDay;
      this.handleSpecialTimeEvent();
    }
  }

  private handleSpecialTimeEvent() {
    // 触发事件逻辑...
    EventActionModule.api.getEventInfo();
  }

  /**
   * 绑定事件到节点（一般是三界）
   * @param parent
   */
  public attachEvent(parent: Node, id: number) {
    if (id != 102 && id != 202 && id != 203) {
      return;
    }
    if (this._attachEventMap.get(id)) {
      log.log("事件已经加载");
      return;
    }
    let db = JsonMgr.instance.jsonList.c_event2[id];
    if (!db) {
      log.error("不存在配置===== Evnet", id);
      return;
    }

    if (db.prefabPath.length < 2) {
      log.error("没有预制体路径配置===== Event", id);
      return;
    }
    ResMgr.setNodePrefab(db.prefabPath[0], db.prefabPath[1], parent, (node) => {
      this._attachEventMap.set(id, node);
      node.getComponent(BaseCtrl)?.init({ eventId: id });
      return false;
    });
  }

  // 销毁时清理
  public destroy() {
    if (this._checkTimer) {
      TickerMgr.clearInterval(this._checkTimer);
    }
  }
}
