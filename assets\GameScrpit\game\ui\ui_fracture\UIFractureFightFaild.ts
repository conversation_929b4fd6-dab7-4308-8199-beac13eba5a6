import { _decorator } from "cc";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

@ccclass("UIFractureFightFaild")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureFightFaild",
  nextHop: [],
  exit: "",
})
export class UIFractureFightFaild extends BaseCtrl {
  start() {
    super.start();
    AudioMgr.instance.playEffect(AudioName.Effect.战斗失败);
  }

  update(deltaTime: number) {}
}
