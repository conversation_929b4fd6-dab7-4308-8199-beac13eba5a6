import { instantiate, Prefab, Vec3 } from "cc";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { TopFinger } from "./TopFinger";
import { AssetMgr, BundleEnum } from "../../platform/src/ResHelper";
import { Sleep } from "../game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

enum GuideActionType {
  Guide = 1,
  callBack = 2,
}

interface IGuideAction {
  type: GuideActionType;
  args: any;
}

export default class GuideMgr {
  private static topFinger: TopFinger = null;
  private static actionList: IGuideAction[] = [];

  public static async create() {
    const assetMgr = AssetMgr.create();
    await new Promise((reslove, reject) => {
      assetMgr.loadPrefab(BundleEnum.BUNDLE_EXT_GUIDE, "prefab/top/TopFinger", async (pb: Prefab, err) => {
        if (pb) {
          GuideMgr.topFinger = instantiate(pb).getComponent(TopFinger);
          GuideMgr.topFinger.node.parent = TipsMgr.getTipsRoot();
        }
        reslove(1);
      });
    });
  }

  public static startGuide(
    args: { stepId: number; args?: any; isForce?: boolean; startPos?: Vec3 },
    cleanOld: boolean = true
  ) {
    TipsMgr.setEnableTouch(false, 1);
    if (cleanOld) {
      GuideMgr.actionList = [];
    }
    GuideMgr.topFinger.init(args);
    GuideMgr.topFinger.node.active = true;
    GuideMgr.topFinger.startGuide();
  }

  public static unshiftGuide(args: { stepId: number; args?: any; isForce?: boolean; startPos?: Vec3 }) {
    TipsMgr.setEnableTouch(false, 1);

    if (!GuideMgr.isGuiding()) {
      GuideMgr.startGuide(args);
    } else {
      GuideMgr.actionList.unshift({ type: GuideActionType.Guide, args });
    }
  }

  public static unshiftCallBack(func: Function) {
    GuideMgr.actionList.unshift({ type: GuideActionType.callBack, args: func });
  }

  public static addCallBack(func: Function) {
    GuideMgr.actionList.push({ type: GuideActionType.callBack, args: func });
  }

  public static endGuide() {
    TipsMgr.setEnableTouch(false, 1);
    if (GuideMgr.actionList.length) {
      const action = GuideMgr.actionList.shift();
      log.info("endGuide", GuideMgr.actionList.length, action);
      if (action.type === GuideActionType.Guide) {
        GuideMgr.startGuide(action.args, false);
      } else if (action.type === GuideActionType.callBack) {
        GuideMgr.topFinger.node.active = false;
        action.args();
      }
    } else {
      TipsMgr.setEnableTouch(true);
      GuideMgr.topFinger.node.active = false;
    }
  }

  public static clearAll() {
    GuideMgr.topFinger.node.active = false;
    GuideMgr.actionList = [];
  }

  public static isGuiding() {
    return GuideMgr.topFinger.node.active;
  }
}
