import { _decorator } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ClubLogAdapter } from "./adapter/ClubLogViewHolder";
import { ClubModule } from "../../../module/club/ClubModule";
import { ClubLogMessage } from "../../net/protocol/Club";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";

import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { LinearLayoutManager } from "db://assets/platform/src/core/ui/adapter_view/layout_manager/LinearLayoutManager";
const { ccclass, property } = _decorator;

/**
 *
 * i<PERSON>_huang
 * Thu Aug 22 2024 19:45:44 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubLog.ts
 *
 */

@ccclass("UIClubLog")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_CLUB,
  url: "prefab/ui/UIClubLog",
  nextHop: [],
  exit: "",
})
export class UIClubLog extends BaseCtrl {
  public playShowAni: boolean = true; //打开动作

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  private _adapter: ClubLogAdapter;
  protected start(): void {
    super.start();
    this._adapter = new ClubLogAdapter(this.getNode("log_viewholder"));
    this.getNode("log_list").addComponent(LinearLayoutManager);
    this.getNode("log_list").getComponent(AdapterView).setAdapter(this._adapter);
    ClubModule.api.log((data: ClubLogMessage[]) => {
      this._adapter.setData(data);
    });
  }
}
