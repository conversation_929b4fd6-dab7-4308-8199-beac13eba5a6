{"skeleton": {"hash": "ajIycbMgKfweYCM2VLRI5vV/tTc", "spine": "3.8.75", "x": -324.72, "y": -145.14, "width": 615.82, "height": 1032.38, "images": "./images/", "audio": "D:/spine/青蛇"}, "bones": [{"name": "root", "scaleX": 0.82, "scaleY": 0.82}, {"name": "st", "parent": "root", "length": 212.61, "rotation": 11.87, "x": -76.96, "y": 187.01}, {"name": "st2", "parent": "st", "length": 304.2, "rotation": 73.1, "x": 24.5, "y": 23.2}, {"name": "st3", "parent": "st2", "length": 138.22, "rotation": -52.26, "x": 93.14, "y": -96.77}, {"name": "st4", "parent": "st2", "length": 91.35, "rotation": -17.64, "x": 323.33, "y": -36.88}, {"name": "t1", "parent": "st4", "length": 155.79, "rotation": 22.28, "x": 79.58, "y": 5.49}, {"name": "t4", "parent": "t1", "length": 49.19, "rotation": 92.8, "x": 76.49, "y": 17.41}, {"name": "t3", "parent": "t1", "length": 41.93, "rotation": -63.29, "x": 86.3, "y": -28.36}, {"name": "t5", "parent": "t1", "length": 49.36, "rotation": 53.18, "x": 179.69, "y": 8.56}, {"name": "t6", "parent": "t5", "length": 36.35, "rotation": 69.94, "x": 54, "y": 3.52}, {"name": "t7", "parent": "t6", "length": 41.78, "rotation": 15.8, "x": 43, "y": 0.92}, {"name": "t8", "parent": "t7", "length": 44.66, "rotation": 22.44, "x": 46.43, "y": 0.23}, {"name": "t9", "parent": "t8", "length": 29.85, "rotation": 31.71, "x": 50.17, "y": 1.9}, {"name": "t10", "parent": "t1", "length": 34.89, "rotation": -22.96, "x": 186.29, "y": 2.05}, {"name": "t11", "parent": "t10", "length": 30.41, "rotation": -77.69, "x": 35.39, "y": -8.51}, {"name": "t12", "parent": "t11", "length": 33.46, "rotation": -34.84, "x": 44.25, "y": -2.49}, {"name": "t13", "parent": "t12", "length": 34.61, "rotation": -21.87, "x": 38.15, "y": -3.53}, {"name": "t15", "parent": "t5", "length": 39.32, "rotation": 126.15, "x": -43.26, "y": 156.89}, {"name": "t2", "parent": "t5", "length": 55.28, "rotation": 120.87, "x": -34.4, "y": 163.97}, {"name": "t14", "parent": "t2", "length": 65.85, "rotation": -4.34, "x": 55.28}, {"name": "t16", "parent": "t14", "length": 59.63, "rotation": 0.07, "x": 65.85}, {"name": "t17", "parent": "t16", "length": 49.39, "rotation": 2.09, "x": 59.63}, {"name": "t18", "parent": "t17", "length": 58.81, "rotation": 3.77, "x": 49.39}, {"name": "t19", "parent": "t18", "length": 50.34, "rotation": -18.07, "x": 58.81}, {"name": "t20", "parent": "t19", "length": 44.83, "rotation": -37.81, "x": 50.34}, {"name": "t21", "parent": "t20", "length": 40.16, "rotation": -9.82, "x": 44.83}, {"name": "t22", "parent": "t21", "length": 39.09, "rotation": -5.07, "x": 40.16}, {"name": "t23", "parent": "t22", "length": 37.71, "rotation": 46.48, "x": 39.09}, {"name": "t24", "parent": "t23", "length": 38.32, "rotation": 51.53, "x": 37.71}, {"name": "t26", "parent": "t5", "length": 66.64, "rotation": 132.02, "x": -45.11, "y": 151.47}, {"name": "t27", "parent": "t26", "length": 39.17, "rotation": 50.36, "x": 66.64}, {"name": "t28", "parent": "t27", "length": 46.16, "rotation": -0.31, "x": 39.17}, {"name": "t29", "parent": "t28", "length": 60.7, "rotation": -40.19, "x": 46.16}, {"name": "t30", "parent": "t29", "length": 51.2, "rotation": -9.98, "x": 56.78, "y": -1.14}, {"name": "t31", "parent": "t30", "length": 43.33, "rotation": 13.15, "x": 51.2}, {"name": "t32", "parent": "t31", "length": 40.64, "rotation": 31.33, "x": 43.33}, {"name": "t33", "parent": "t32", "length": 18.82, "rotation": -34.12, "x": 40.64}, {"name": "ys", "parent": "st2", "length": 342.02, "rotation": -156.85, "x": 310.17, "y": -8.58}, {"name": "ys2", "parent": "ys", "length": 131.93, "rotation": 131.53, "x": 337.05, "y": -0.74}, {"name": "ys4", "parent": "ys2", "length": 165.14, "rotation": 20.16, "x": 133.6, "y": 0.11}, {"name": "ys3", "parent": "ys4", "length": 81.64, "rotation": 32.28, "x": 161.58, "y": -1.4}, {"name": "ys5", "parent": "ys3", "length": 48.22, "rotation": -15.85, "x": 81.64}, {"name": "ys7", "parent": "ys5", "length": 44.88, "rotation": 3.17, "x": 55.47, "y": 1.53}, {"name": "ys8", "parent": "ys5", "length": 43.47, "rotation": 4.53, "x": 56.3, "y": 25.95}, {"name": "ys9", "parent": "ys3", "length": 49.04, "rotation": 21.33, "x": 54.32, "y": 33.68}, {"name": "ys10", "parent": "ys9", "length": 33.06, "rotation": 1.19, "x": 49.04}, {"name": "ys11", "parent": "ys", "length": 53.43, "rotation": -35.16, "x": 202.14, "y": -37.79}, {"name": "ys12", "parent": "ys11", "length": 47.55, "rotation": -10.86, "x": 53.43}, {"name": "ys13", "parent": "ys12", "length": 47.78, "rotation": -4.42, "x": 47.55}, {"name": "ys14", "parent": "ys13", "length": 53.62, "rotation": -13.93, "x": 47.78}, {"name": "ys15", "parent": "ys14", "length": 43.68, "rotation": -7.97, "x": 53.62}, {"name": "ys16", "parent": "ys15", "length": 49.03, "rotation": -10.95, "x": 43.68}, {"name": "ys17", "parent": "ys16", "length": 37.58, "rotation": -9.59, "x": 49.03}, {"name": "zs", "parent": "st2", "length": 50.16, "rotation": -117.17, "x": 353.84, "y": -127.12}, {"name": "zs2", "parent": "zs", "length": 55.16, "rotation": -53.72, "x": 45.94, "y": -11.01}, {"name": "zs3", "parent": "zs2", "length": 50.27, "rotation": 22.88, "x": 55.16}, {"name": "zs4", "parent": "zs3", "length": 63.69, "rotation": -14.13, "x": 50.27}, {"name": "zs5", "parent": "zs4", "length": 46.48, "rotation": -8.96, "x": 63.69}, {"name": "zs6", "parent": "zs5", "length": 59.74, "rotation": -3.88, "x": 46.48}, {"name": "sh3", "parent": "root", "x": 215.97, "y": 36.4}, {"name": "sh4", "parent": "root", "x": 237.92, "y": 102.25}, {"name": "sh2", "parent": "root", "x": 268.65, "y": 197.95}, {"name": "sh1", "parent": "root", "x": -265.52, "y": 166.35}, {"name": "bone", "parent": "root", "x": 9.85, "y": 138.65}, {"name": "sh5", "parent": "root", "x": -265.52, "y": 166.35}, {"name": "sh6", "parent": "root", "x": -265.52, "y": 166.35}, {"name": "sh7", "parent": "root", "x": 268.65, "y": 197.95}, {"name": "sh8", "parent": "root", "x": 268.65, "y": 197.95}, {"name": "sh9", "parent": "root", "x": 215.97, "y": 36.4}, {"name": "sh10", "parent": "root", "x": 215.97, "y": 36.4}, {"name": "sh11", "parent": "root", "x": 237.92, "y": 102.25}, {"name": "sh12", "parent": "root", "x": 237.92, "y": 102.25}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "sh1", "bone": "sh1", "attachment": "sh1"}, {"name": "sh5", "bone": "sh5", "attachment": "sh1"}, {"name": "sh6", "bone": "sh6", "attachment": "sh1"}, {"name": "sh2", "bone": "sh2", "attachment": "sh2"}, {"name": "sh7", "bone": "sh7", "attachment": "sh2"}, {"name": "sh8", "bone": "sh8", "attachment": "sh2"}, {"name": "sh3", "bone": "sh3", "attachment": "sh3"}, {"name": "sh9", "bone": "sh9", "attachment": "sh3"}, {"name": "sh10", "bone": "sh10", "attachment": "sh3"}, {"name": "sh4", "bone": "sh4", "attachment": "sh4"}, {"name": "sh11", "bone": "sh11", "attachment": "sh4"}, {"name": "sh12", "bone": "sh12", "attachment": "sh4"}, {"name": "t2", "bone": "t2", "attachment": "t2"}, {"name": "zs", "bone": "zs", "attachment": "zs"}, {"name": "st", "bone": "st", "attachment": "st"}, {"name": "t1", "bone": "t1", "attachment": "t1"}, {"name": "t3", "bone": "t3", "attachment": "t3"}, {"name": "t4", "bone": "t4", "attachment": "t4"}, {"name": "t5", "bone": "t10", "attachment": "t5"}, {"name": "t6", "bone": "t26", "attachment": "t6"}, {"name": "t7", "bone": "t15", "attachment": "t7"}, {"name": "ys", "bone": "ys11", "attachment": "ys"}, {"name": "ys2", "bone": "ys4", "attachment": "ys2"}, {"name": "ys3", "bone": "ys3", "attachment": "ys3"}, {"name": "ys4", "bone": "ys7", "attachment": "ys4"}, {"name": "ys5", "bone": "ys8", "attachment": "ys5"}, {"name": "ys6", "bone": "ys9", "attachment": "ys6"}], "skins": [{"name": "default", "attachments": {"sh5": {"sh1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.52, -57.35, -123.48, -57.35, -123.48, 44.65, 76.52, 44.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 102}}, "sh6": {"sh1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.52, -57.35, -123.48, -57.35, -123.48, 44.65, 76.52, 44.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 102}}, "sh7": {"sh2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [86.35, -53.95, -82.65, -53.95, -82.65, 51.05, 86.35, 51.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 169, "height": 105}}, "t5": {"t5": {"type": "mesh", "uvs": [0.45803, 0, 0.53254, 0.00848, 0.60679, 0.01692, 0.6703, 0.02415, 0.72078, 0.04605, 0.77577, 0.06991, 0.81892, 0.10752, 0.86687, 0.14931, 0.89848, 0.17686, 0.94689, 0.21905, 0.96076, 0.25657, 0.9743, 0.29317, 0.98712, 0.32785, 0.99958, 0.36155, 0.99118, 0.38908, 0.97986, 0.42614, 0.96928, 0.4608, 0.95873, 0.49537, 0.91983, 0.52345, 0.93444, 0.54873, 0.95707, 0.58791, 0.98242, 0.6318, 0.99628, 0.65581, 0.9646, 0.64481, 0.92384, 0.63067, 0.88753, 0.61807, 0.84749, 0.60418, 0.82705, 0.62396, 0.80563, 0.6447, 0.80137, 0.67062, 0.79571, 0.70505, 0.79217, 0.72661, 0.7583, 0.72666, 0.73034, 0.7267, 0.73238, 0.65888, 0.83342, 0.54923, 0.84184, 0.50295, 0.84199, 0.41605, 0.81484, 0.4027, 0.77672, 0.33025, 0.74495, 0.26987, 0.65917, 0.29204, 0.58901, 0.2976, 0.45973, 0.30783, 0.46633, 0.32694, 0.4324, 0.39567, 0.4027, 0.4558, 0.36681, 0.5285, 0.37076, 0.53518, 0.4164, 0.5808, 0.39359, 0.6127, 0.36854, 0.64772, 0.33928, 0.68863, 0.36828, 0.71748, 0.40121, 0.75024, 0.42968, 0.77855, 0.42773, 0.79134, 0.37359, 0.7972, 0.31015, 0.77589, 0.24729, 0.75477, 0.209, 0.74191, 0.16671, 0.69992, 0.12681, 0.66031, 0.08881, 0.62259, 0.05247, 0.58651, 0.03583, 0.54295, 0.0195, 0.5002, 0, 0.44915, 0, 0.42464, 0.06641, 0.37393, 0.05834, 0.33491, 0.06956, 0.29508, 0.08433, 0.24268, 0.09378, 0.20916, 0.12049, 0.17163, 0.15098, 0.12881, 0.17889, 0.08961, 0.21638, 0.07273, 0.27688, 0.04549, 0.32749, 0.0227, 0.3779, 0, 0.48337, 0.19948, 0.31231, 0.20306, 0.17332, 0.26854, 0.51009, 0.15067, 0.32656, 0.12924, 0.18401, 0.17924, 0.45129, 0.07566, 0.31052, 0.07447, 0.23212, 0.0959, 0.37289, 0.03518, 0.45486, 0.02804, 0.5386, 0.04471, 0.61523, 0.03995, 0.59562, 0.10662, 0.70254, 0.0959, 0.61879, 0.18639, 0.75065, 0.16972, 0.84865, 0.19234, 0.83974, 0.27687, 0.88963, 0.29949, 0.84687, 0.37688, 0.9128, 0.40188, 0.35377, 0.27845, 0.23144, 0.38744, 0.13146, 0.46215, 0.08148, 0.53598, 0.2278, 0.67778, 0.30672, 0.67074, 0.2791, 0.73315, 0.19228, 0.6268, 0.31067, 0.61361, 0.3723, 0.35775, 0.37093, 0.43019, 0.32976, 0.49989, 0.23917, 0.58334], "triangles": [40, 96, 97, 41, 96, 40, 42, 96, 41, 42, 43, 81, 112, 103, 43, 112, 43, 44, 45, 112, 44, 113, 104, 112, 113, 112, 45, 46, 113, 45, 114, 104, 113, 114, 113, 46, 16, 36, 37, 47, 114, 46, 35, 36, 18, 115, 114, 47, 115, 47, 48, 49, 111, 48, 28, 34, 35, 33, 34, 32, 100, 99, 10, 11, 100, 10, 39, 40, 99, 100, 39, 99, 101, 39, 100, 13, 102, 12, 12, 102, 100, 12, 100, 11, 14, 102, 13, 101, 100, 102, 38, 39, 101, 37, 38, 101, 37, 101, 102, 15, 102, 14, 16, 102, 15, 16, 37, 102, 17, 36, 16, 18, 36, 17, 35, 18, 19, 26, 35, 19, 25, 26, 19, 20, 25, 19, 27, 35, 26, 24, 25, 20, 24, 20, 21, 27, 28, 35, 23, 24, 21, 23, 21, 22, 29, 34, 28, 30, 34, 29, 30, 32, 34, 31, 32, 30, 98, 97, 7, 98, 7, 8, 98, 8, 9, 99, 40, 98, 99, 98, 9, 99, 9, 10, 95, 93, 4, 95, 4, 5, 95, 5, 6, 97, 95, 6, 97, 6, 7, 96, 95, 97, 40, 97, 98, 91, 0, 1, 93, 2, 3, 92, 1, 2, 92, 2, 93, 91, 1, 92, 93, 3, 4, 87, 91, 92, 94, 92, 93, 94, 93, 95, 94, 84, 87, 94, 87, 92, 96, 94, 95, 84, 94, 96, 96, 42, 84, 105, 67, 68, 105, 68, 69, 105, 104, 114, 66, 67, 105, 106, 66, 105, 65, 66, 106, 115, 105, 114, 106, 105, 115, 64, 65, 106, 63, 64, 106, 111, 115, 48, 49, 50, 111, 106, 110, 63, 115, 110, 106, 110, 115, 111, 51, 111, 50, 62, 63, 110, 111, 107, 110, 108, 111, 51, 108, 107, 111, 62, 110, 107, 52, 108, 51, 61, 62, 107, 109, 107, 108, 109, 108, 52, 109, 52, 53, 60, 61, 107, 60, 107, 109, 59, 60, 109, 58, 109, 53, 58, 53, 54, 59, 109, 58, 55, 57, 54, 57, 58, 54, 56, 57, 55, 83, 69, 70, 83, 70, 71, 104, 103, 112, 69, 83, 104, 105, 69, 104, 74, 75, 86, 86, 72, 73, 86, 73, 74, 83, 72, 86, 83, 86, 82, 83, 82, 103, 71, 72, 83, 104, 83, 103, 91, 80, 0, 90, 79, 80, 90, 80, 91, 78, 79, 90, 88, 78, 90, 87, 90, 91, 88, 90, 87, 89, 77, 78, 89, 78, 88, 76, 77, 89, 75, 76, 89, 85, 88, 87, 89, 88, 85, 85, 87, 84, 86, 75, 89, 86, 89, 85, 82, 86, 85, 82, 85, 81, 103, 82, 81, 81, 85, 84, 42, 81, 84, 43, 103, 81], "vertices": [5, 8, 97.58, -70.44, 0.27319, 9, -54.53, -66.3, 0.36696, 10, -112.14, -38.13, 0.00012, 13, 83.14, 81.29, 0.34076, 14, -77.55, 65.8, 0.01896, 4, 8, 77.6, -80.86, 0.27225, 9, -71.16, -51.11, 0.21816, 13, 88.47, 59.41, 0.44046, 14, -55.03, 66.34, 0.06913, 4, 8, 57.7, -91.24, 0.21047, 9, -87.74, -35.98, 0.10147, 13, 93.78, 37.6, 0.50448, 14, -32.59, 66.88, 0.18357, 4, 8, 40.68, -100.11, 0.14015, 9, -101.92, -23.03, 0.04743, 13, 98.33, 18.95, 0.4831, 14, -13.4, 67.34, 0.32933, 5, 8, 22.79, -101.43, 0.07872, 9, -109.29, -6.68, 0.01906, 13, 95.32, 1.26, 0.39744, 14, 3.24, 60.64, 0.50433, 15, -69.72, 28.38, 0.00046, 5, 8, 3.31, -102.86, 0.02724, 9, -117.32, 11.13, 0.00337, 13, 92.05, -18, 0.24474, 14, 21.36, 53.33, 0.70162, 15, -50.68, 32.74, 0.02303, 4, 8, -17.08, -97.28, 0.00298, 13, 81.75, -36.45, 0.08138, 14, 37.19, 39.33, 0.75227, 15, -29.69, 30.29, 0.16337, 3, 13, 70.3, -56.96, 0.00242, 14, 54.79, 23.77, 0.35353, 15, -6.36, 27.58, 0.64405, 2, 14, 66.38, 13.52, 0.05943, 15, 9.02, 25.79, 0.94057, 2, 15, 32.57, 23.04, 0.97663, 16, -15.08, 22.58, 0.02337, 2, 15, 47.46, 14.36, 0.48267, 16, 1.97, 20.07, 0.51733, 2, 15, 61.99, 5.89, 0.05419, 16, 18.61, 17.63, 0.94581, 1, 16, 34.38, 15.31, 1, 1, 16, 49.69, 13.05, 1, 1, 16, 60.11, 6.08, 1, 1, 16, 74.13, -3.29, 1, 1, 16, 87.24, -12.06, 1, 1, 16, 100.32, -20.81, 1, 1, 16, 107.52, -36.28, 1, 1, 16, 119.61, -36.52, 1, 1, 16, 138.33, -36.9, 1, 1, 16, 159.31, -37.32, 1, 1, 16, 170.79, -37.55, 1, 1, 16, 162.67, -44.43, 1, 1, 16, 152.24, -53.28, 1, 1, 16, 142.94, -61.17, 1, 1, 16, 132.69, -69.87, 1, 1, 16, 138.55, -78.85, 1, 1, 16, 144.69, -88.26, 1, 1, 16, 154.91, -93.81, 1, 1, 16, 168.48, -101.19, 1, 1, 16, 176.98, -105.8, 1, 1, 16, 173.18, -115.15, 1, 1, 5, -28.99, -38.9, 1, 1, 5, 1.26, -39.3, 1, 1, 5, 50.37, -69.08, 1, 2, 16, 90.26, -54.33, 0.008, 5, 71.03, -71.44, 0.992, 1, 5, 109.78, -71.23, 1, 3, 13, -39.6, -87.53, 0.00116, 14, 61.21, -90.12, 0.00018, 16, 45.83, -44.85, 0.99866, 4, 13, -14.43, -64.29, 0.02811, 14, 43.87, -60.58, 0.03782, 15, 32.87, -47.89, 0.05, 16, 11.62, -43.13, 0.88407, 1, 5, 174.78, -41.86, 1, 1, 5, 164.71, -16.37, 1, 1, 5, 162.09, 4.53, 1, 1, 5, 157.27, 43.02, 1, 1, 5, 148.76, 40.99, 1, 1, 5, 118.04, 50.89, 1, 1, 5, 91.16, 59.56, 1, 1, 5, 58.66, 70.03, 1, 1, 5, 55.69, 68.83, 1, 1, 5, 35.44, 55.09, 1, 8, 8, -52.37, 158.81, 0.00012, 10, 105.34, 128.43, 0.01009, 11, 103.39, 96.01, 0.00751, 12, 94.74, 52.1, 0.98227, 13, -175.35, -9.41, 0, 14, -44.06, -206.08, 0, 15, 43.83, -217.55, 0, 16, 84.99, -196.5, 0, 7, 10, 121.98, 133.18, 0.00314, 11, 120.58, 94.04, 0.00125, 12, 108.33, 41.39, 0.99561, 13, -192.64, -8.75, 0, 14, -48.4, -222.84, 0, 15, 49.84, -233.78, 0, 16, 96.62, -209.32, 0, 5, 10, 141.43, 138.73, 0.00013, 12, 124.22, 28.87, 0.99987, 13, -212.85, -7.98, 0, 14, -53.46, -242.42, 0, 16, 110.21, -224.3, 0, 3, 12, 138.67, 34.48, 1, 13, -221.24, -21.01, 0, 14, -42.52, -253.39, 0, 2, 12, 155.08, 40.85, 1, 14, -30.09, -265.86, 0, 1, 12, 169.26, 46.35, 1, 1, 12, 174.7, 44.53, 1, 1, 12, 173.71, 28.22, 1, 1, 12, 160.28, 11.86, 1, 1, 12, 146.99, -4.35, 1, 1, 12, 138.88, -14.22, 1, 1, 12, 117.85, -22.41, 1, 1, 12, 98, -30.13, 1, 1, 12, 79.1, -37.48, 1, 1, 12, 61.03, -44.51, 1, 1, 12, 40.99, -45.09, 1, 1, 12, 21.31, -45.65, 1, 2, 11, 72.66, -38.65, 0.08616, 12, -2.17, -46.32, 0.91384, 2, 11, 62.33, -42.22, 0.175, 12, -12.84, -43.92, 0.825, 3, 10, 90.11, -15.14, 0.00196, 11, 34.5, -30.88, 0.83324, 12, -30.56, -19.65, 0.1648, 4, 10, 78.66, -28.47, 0.065, 11, 18.83, -38.83, 0.9267, 12, -48.06, -18.18, 0.0083, 15, -114.66, -203.02, 0, 4, 9, 114.01, -18.2, 0.0033, 10, 63.13, -37.73, 0.28768, 11, 0.94, -41.45, 0.70903, 15, -125.09, -188.25, 0, 4, 9, 97.67, -35.48, 0.06592, 10, 42.7, -49.9, 0.64794, 11, -22.58, -44.91, 0.28614, 15, -138.8, -168.82, 0, 4, 9, 87.22, -46.53, 0.16274, 10, 29.64, -57.69, 0.70929, 11, -37.64, -47.12, 0.12798, 15, -147.57, -156.4, 0, 5, 9, 71.47, -56.3, 0.3424, 10, 11.82, -62.81, 0.6204, 11, -56.05, -45.05, 0.03568, 13, -27.01, 143.29, 0.00153, 15, -154.05, -139.03, 0, 6, 8, 135.71, 30.65, 0.00314, 9, 53.51, -67.45, 0.56859, 10, -8.5, -68.65, 0.41331, 11, -77.07, -42.69, 0.00254, 13, -5.88, 142.52, 0.01242, 15, -161.43, -119.21, 0, 5, 8, 139.66, 11.69, 0.01796, 9, 37.05, -77.67, 0.6876, 10, -27.11, -74, 0.26232, 13, 13.47, 141.82, 0.03212, 15, -168.2, -101.07, 0, 5, 8, 135.32, -1.06, 0.03965, 9, 23.58, -77.96, 0.72138, 10, -40.15, -70.61, 0.18534, 13, 24.81, 134.54, 0.05363, 15, -165.82, -87.81, 0, 4, 8, 128.3, -21.64, 0.10958, 9, 1.85, -78.43, 0.69789, 10, -61.19, -65.15, 0.07414, 13, 43.11, 122.81, 0.11839, 5, 8, 122.44, -38.85, 0.18086, 9, -16.33, -78.82, 0.6046, 10, -78.8, -60.58, 0.02455, 13, 58.42, 112.99, 0.18997, 14, -113.79, 48.41, 2e-05, 5, 8, 116.59, -56, 0.23036, 9, -34.44, -79.21, 0.5086, 10, -96.33, -56.02, 0.00612, 13, 73.67, 103.21, 0.2524, 14, -100.98, 61.23, 0.00252, 2, 8, 37.76, -4.14, 0.95714, 13, 4.45, 39.09, 0.04286, 4, 9, 30.97, -13.6, 0.8709, 10, -15.53, -10.7, 0.12655, 13, -17.22, 85.26, 0.00255, 15, -104.19, -107.75, 0, 4, 9, 81.6, -11.43, 0.02048, 10, 33.79, -22.4, 0.79533, 11, -20.33, -16.08, 0.18418, 15, -112.06, -157.82, 0, 4, 8, 44.58, -26.3, 0.58967, 9, -31.24, -1.38, 0.06036, 13, 27.6, 40.41, 0.34966, 14, -49.45, 2.82, 0.00031, 4, 8, 93.92, -0.84, 0.06644, 9, 9.6, -39, 0.79668, 10, -43.01, -29.32, 0.07065, 13, 14.69, 94.41, 0.06624, 5, 9, 57.39, -43.21, 0.40991, 10, 1.83, -46.38, 0.57252, 11, -59.01, -26.05, 0.01457, 13, -22.63, 124.57, 0.003, 15, -138.43, -127.8, 0, 5, 8, 78.77, -42.35, 0.31319, 9, -34.59, -39, 0.3695, 10, -85.53, -17.29, 0.00071, 13, 51.36, 69.76, 0.30656, 14, -73.06, 32.29, 0.01004, 4, 8, 112.5, -17.4, 0.11572, 9, 0.41, -62.13, 0.70118, 10, -58.15, -49.07, 0.06351, 13, 35.22, 108.48, 0.11959, 5, 8, 125.33, 4.34, 0.03325, 9, 25.23, -66.72, 0.7209, 10, -35.51, -60.25, 0.1987, 13, 17.18, 126.14, 0.04714, 15, -155.14, -91.64, 0, 5, 8, 108.29, -42.6, 0.215, 9, -24.7, -66.82, 0.54606, 10, -83.58, -46.75, 0.01153, 13, 58.67, 98.36, 0.22612, 14, -99.44, 45.54, 0.00128, 5, 8, 90.77, -59.91, 0.27807, 9, -46.97, -56.29, 0.36794, 10, -102.15, -30.56, 0.00024, 13, 71.28, 77.2, 0.33631, 14, -76.08, 53.34, 0.01744, 4, 8, 66.39, -69.08, 0.27783, 9, -63.94, -36.54, 0.18582, 13, 74.35, 51.34, 0.45965, 14, -50.16, 50.83, 0.07671, 4, 8, 49.49, -84.58, 0.19525, 9, -84.3, -25.98, 0.08352, 13, 85.35, 31.22, 0.51002, 14, -28.16, 57.29, 0.21122, 4, 8, 36.16, -57.36, 0.22641, 9, -63.31, -4.13, 0.05061, 13, 55.74, 24.8, 0.59863, 14, -28.2, 26.98, 0.12434, 5, 8, 13.68, -80.43, 0.05346, 9, -92.69, 9.08, 0.00991, 13, 72.76, -2.56, 0.36774, 14, 2.16, 37.77, 0.56813, 15, -57.55, 9, 0.00076, 3, 8, 9.15, -33.2, 0.05153, 9, -49.87, 29.53, 0.00014, 13, 25.81, 4.36, 0.94833, 2, 14, 22.53, 8.21, 0.99026, 15, -23.93, -3.62, 0.00974, 2, 14, 53.13, 3.9, 0.04382, 15, 3.64, 10.32, 0.95618, 4, 13, 14.87, -72.09, 0.02201, 14, 57.75, -33.61, 0.03525, 15, 28.86, -17.83, 0.27732, 16, -3.3, -16.73, 0.66542, 3, 13, 11.5, -89.74, 0.00264, 14, 74.27, -40.67, 0.00283, 16, 11.67, -6.79, 0.99453, 3, 13, -25.24, -91.72, 0.00201, 14, 68.37, -76.99, 0.00098, 16, 38.79, -31.66, 0.99702, 1, 16, 56.55, -17.69, 1, 9, 8, 47.22, 47.26, 0.1288, 9, 38.76, 21.36, 0.34031, 10, 1.49, 20.83, 0.52234, 11, -33.67, 36.2, 0.00454, 12, -53.31, 73.24, 0.004, 13, -43.19, 60.58, 1e-05, 14, -84.26, -62.04, 0, 15, -71.45, -122.29, 0, 16, -57.48, -151.04, 0, 9, 8, 46.86, 108.02, 0.01045, 9, 95.71, 42.54, 0.00111, 10, 62.05, 25.7, 0.1598, 11, 24.16, 17.58, 0.747, 12, -13.89, 27.01, 0.08163, 13, -102.27, 74.78, 1e-05, 14, -110.73, -116.73, 0, 15, -61.93, -182.3, 0, 16, -26.29, -203.18, 0, 1, 12, 12.08, -9.37, 1, 1, 12, 40.94, -31.13, 1, 4, 12, 112.21, -2.48, 1, 13, -221.58, 24.44, 0, 14, -87, -244.03, 0, 16, 93.14, -253.22, 0, 5, 10, 141.88, 126.18, 0.00035, 12, 114.31, 21.16, 0.99965, 13, -209.38, 4.09, 0, 14, -64.51, -236.45, 0, 16, 99.15, -230.26, 0, 3, 12, 139.66, 7.02, 1, 13, -238.19, 0.62, 0, 14, -67.26, -265.34, 0, 4, 12, 87.7, -7.81, 1, 13, -204.9, 43.17, 0, 14, -101.74, -223.74, 0, 16, 68.09, -254.4, 0, 8, 8, -32.94, 174.08, 6e-05, 10, 122.01, 110.19, 0.00565, 11, 111.83, 72.78, 0.00369, 12, 89.71, 27.9, 0.99061, 13, -185.52, 13.11, 0, 14, -68.24, -211.22, 0, 15, 26.92, -235.57, 0, 16, 76.01, -219.52, 0, 10, 8, 21.44, 72.09, 0.00402, 9, 53.24, 54.1, 0.00043, 10, 24.33, 48.38, 0.06143, 11, -2.04, 52.94, 0.28716, 12, -17.59, 70.87, 0.03138, 13, -73.47, 41.49, 0, 14, -72.07, -95.69, 0, 15, -42.22, -142.94, 0, 16, -22.66, -159.32, 0, 5, 134.83, 68.92, 0.61558, 10, 8, 2.23, 98.07, 0.00226, 9, 71.06, 81.06, 0.00024, 10, 48.81, 69.47, 0.03462, 11, 28.64, 63.09, 0.16183, 12, 13.84, 63.38, 0.01768, 13, -103.29, 29.06, 0, 14, -66.28, -127.48, 0, 15, -19.31, -165.73, 0, 16, 7.09, -171.93, 0, 5, 102.51, 69.1, 0.78336, 10, 8, -6.8, 130.24, 0.00041, 9, 98.19, 100.57, 4e-05, 10, 80.23, 80.86, 0.00632, 11, 62.02, 61.62, 0.02955, 12, 41.47, 44.58, 0.16386, 13, -136.7, 28, 0, 14, -72.37, -160.34, 0, 15, -5.53, -196.18, 0, 16, 31.22, -195.05, 0, 5, 71.35, 81.16, 0.79981, 10, 8, -7.81, 176.21, 0.00014, 9, 141.02, 117.28, 1e-05, 10, 126, 85.28, 0.00216, 11, 106.01, 48.24, 0.0101, 12, 71.86, 10.08, 0.71419, 13, -181.57, 38.03, 0, 14, -91.73, -202.04, 0, 15, 2.39, -241.47, 0, 16, 55.44, -234.13, 0, 5, 33.94, 107.9, 0.27339], "hull": 81, "edges": [0, 160, 34, 36, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 80, 82, 86, 88, 94, 96, 96, 98, 112, 114, 134, 136, 136, 138, 138, 140, 110, 112, 132, 134, 128, 130, 130, 132, 126, 128, 124, 126, 120, 122, 122, 124, 118, 120, 114, 116, 116, 118, 104, 106, 106, 108, 108, 110, 98, 100, 100, 102, 102, 104, 150, 152, 146, 148, 148, 150, 144, 146, 140, 142, 142, 144, 152, 154, 154, 156, 156, 158, 158, 160, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 82, 84, 84, 86, 84, 162, 162, 164, 164, 166, 166, 138, 84, 168, 168, 170, 170, 172, 172, 144, 168, 174, 174, 176, 176, 178, 178, 150, 174, 180, 180, 156, 174, 182, 182, 2, 174, 184, 184, 186, 186, 8, 168, 188, 188, 190, 190, 12, 84, 192, 192, 194, 194, 196, 196, 18, 80, 198, 198, 200, 200, 22, 76, 78, 78, 80, 78, 202, 202, 204, 204, 28, 86, 206, 206, 208, 208, 210, 210, 212, 212, 128, 124, 214, 214, 216, 216, 102, 120, 218, 218, 104, 126, 220, 220, 222, 222, 100, 206, 224, 224, 226, 226, 228, 228, 230, 230, 220, 88, 90, 90, 92, 92, 94], "width": 298, "height": 446}}, "sh9": {"sh3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114.03, -48.4, -145.97, -48.4, -145.97, 57.6, 114.03, 57.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 106}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.84958, 0, 0.5, 0, 0, 1, 0, 1, 0.5, 0.19926, 0.83359, 0.4137, 0.85249, 0.67199, 0.83941, 0.84256, 0.79144, 0.84256, 0.68824, 0.27236, 0.66789, 0.17489, 0.71295, 0.1822, 0.59521, 0.88155, 0.54579, 0.34547, 0.54143], "triangles": [6, 15, 5, 5, 16, 4, 0, 10, 6, 0, 1, 8, 1, 2, 7, 2, 3, 13, 16, 3, 4, 1, 7, 8, 8, 9, 0, 9, 10, 0, 6, 10, 11, 11, 15, 6, 9, 8, 12, 2, 13, 7, 11, 10, 9, 12, 8, 7, 7, 13, 12, 11, 9, 12, 13, 14, 12, 13, 3, 14, 12, 16, 11, 11, 16, 15, 12, 14, 16, 14, 3, 16, 5, 15, 16], "vertices": [1, 0, 355, -177, 1, 1, 0, -396, -177, 1, 1, 0, -396, 12.38, 1, 1, 0, -396, 452.5, 1, 1, 0, -396, 1082, 1, 1, 0, 355, 1082, 1, 1, 0, 355, 452.5, 1, 2, 63, -256.2, -106.14, 0.94473, 0, -246.35, 32.51, 0.05527, 1, 63, -95.16, -129.93, 1, 1, 63, 98.82, -113.46, 1, 2, 63, 226.92, -53.07, 0.81583, 0, 236.77, 85.58, 0.18417, 2, 63, 226.92, 76.86, 0.85597, 0, 236.77, 215.51, 0.14403, 1, 63, -201.3, 102.48, 1, 2, 63, -274.5, 45.75, 0.93637, 0, -264.65, 184.4, 0.06363, 2, 63, -269.01, 193.98, 0.8221, 0, -259.16, 332.63, 0.1779, 2, 63, 256.2, 256.2, 0.92485, 0, 266.05, 394.85, 0.07515, 2, 63, -146.4, 261.69, 0.91418, 0, -136.55, 400.34, 0.08582], "hull": 7, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 0, 2, 4, 4, 6], "width": 367, "height": 616}}, "t7": {"t7": {"type": "mesh", "uvs": [0.97509, 0.56108, 0.57429, 0.97561, 0.49809, 0.98844, 0.32976, 0.9578, 0, 0.56183, 0, 0.54066, 0.36026, 0.02072, 0.5179, 0.01707], "triangles": [7, 4, 5, 7, 5, 6, 0, 4, 7, 3, 4, 0, 1, 3, 0, 2, 3, 1], "vertices": [1, 17, 24.13, 14.56, 1, 1, 17, 45.48, 3.74, 1, 1, 17, 46.17, 1.61, 1, 1, 17, 44.69, -3.13, 1, 1, 17, 24.67, -12.73, 1, 1, 17, 23.59, -12.75, 1, 1, 17, -3.1, -3.16, 1, 1, 17, -3.37, 1.25, 1], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 28, "height": 51}}, "t3": {"t3": {"type": "mesh", "uvs": [0.71266, 0, 0.96199, 0.13321, 0.99472, 0.32225, 0.86735, 0.77332, 0.64212, 0.97834, 0.07398, 0.97715, 0.01789, 0.90827, 0.01856, 0.70462, 0.13774, 0.4172, 0.57697, 0, 0.47109, 0.41202, 0.50788, 0.5552, 0.49373, 0.70861, 0.41166, 0.81429, 0.15411, 0.84156, 0.1626, 0.68475, 0.28996, 0.50747, 0.26732, 0.45975, 0.46826, 0.31316, 0.72015, 0.22793, 0.81568, 0.48842, 0.68934, 0.7216, 0.51002, 0.81487, 0.42647, 0.85414, 0.17787, 0.8885], "triangles": [19, 0, 1, 9, 0, 19, 18, 9, 19, 19, 1, 2, 10, 18, 19, 18, 8, 9, 17, 8, 18, 10, 17, 18, 20, 19, 2, 10, 19, 20, 16, 17, 10, 11, 10, 20, 16, 10, 11, 15, 8, 17, 15, 17, 16, 7, 8, 15, 12, 16, 11, 21, 11, 20, 12, 11, 21, 3, 20, 2, 21, 20, 3, 13, 16, 12, 15, 16, 13, 22, 12, 21, 13, 12, 22, 14, 7, 15, 14, 15, 13, 23, 13, 22, 24, 14, 13, 24, 13, 23, 6, 7, 14, 6, 14, 24, 5, 6, 24, 4, 22, 21, 4, 21, 3, 23, 22, 4, 5, 24, 23, 4, 5, 23], "vertices": [43.97, 19.25, 53.21, 8.14, 51.08, -0.09, 36.23, -14.89, 21.53, -17.68, -5.43, -4.29, -6.76, -0.25, -2.75, 7.76, 8.52, 16.3, 37.52, 22.44, 24.45, 8.67, 23.41, 2.16, 19.75, -3.56, 13.78, -5.8, 1.02, -0.82, 4.48, 5.16, 13.99, 9.16, 13.84, 11.58, 26.25, 12.64, 39.88, 10.08, 39.34, -2.44, 28.79, -8.66, 18.45, -8.13, 13.71, -7.72, 1.23, -3.23], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 20, 30, 34, 34, 36, 36, 38, 38, 2, 4, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 12], "width": 53, "height": 44}}, "ys4": {"ys4": {"type": "mesh", "uvs": [0.56865, 0.12384, 0.71874, 0.42607, 0.82149, 0.63297, 0.91606, 0.82341, 0.99607, 0.98452, 0.68081, 0.98468, 0.39375, 0.98482, 0.13429, 0.98495, 0.10431, 0.81663, 0.07073, 0.62811, 0.06175, 0.57774, 0.02577, 0.37572, 0.12463, 0.12503], "triangles": [5, 3, 4, 5, 6, 3, 7, 8, 6, 3, 6, 8, 8, 2, 3, 2, 9, 1, 1, 11, 0, 11, 12, 0, 9, 2, 8, 1, 9, 10, 1, 10, 11], "vertices": [1, 42, 51.12, -7.17, 1, 1, 42, 31.35, -8.16, 1, 2, 42, 17.82, -8.83, 0.976, 41, 73.75, -6.3, 0.024, 2, 42, 5.36, -9.45, 0.872, 41, 61.34, -7.61, 0.128, 1, 41, 50.85, -8.71, 1, 1, 41, 51.8, 0.06, 1, 1, 41, 52.66, 8.05, 1, 1, 41, 53.45, 15.28, 1, 2, 42, 9.5, 12.9, 0.952, 41, 64.25, 14.94, 0.048, 1, 42, 21.56, 11.86, 1, 1, 42, 24.78, 11.58, 1, 1, 42, 37.7, 10.46, 1, 1, 42, 53.08, 5.11, 1], "hull": 13, "edges": [0, 24, 22, 24, 12, 14, 8, 10, 10, 12, 0, 2, 2, 4, 4, 6, 6, 8, 20, 22, 18, 20, 14, 16, 16, 18], "width": 28, "height": 64}}, "ys2": {"ys2": {"type": "mesh", "uvs": [0.82738, 0, 0.99955, 0.02951, 0.81286, 0.79303, 0.57865, 1, 0.53389, 1, 0.09553, 0.48007, 0.32404, 0.02187, 0.64311, 0], "triangles": [4, 5, 2, 3, 4, 2, 7, 0, 1, 6, 7, 1, 5, 6, 1, 2, 5, 1], "vertices": [1, 39, 172.22, -12.73, 1, 1, 39, 167.49, -28.27, 1, 1, 39, -23.91, -46.74, 1, 1, 38, 72.49, -61.28, 1, 1, 38, 70.59, -58.03, 1, 1, 38, 164.61, 39.68, 1, 1, 39, 159.34, 27.91, 1, 1, 39, 169.48, 2.51, 1], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 84, "height": 251}}, "ys3": {"ys3": {"type": "mesh", "uvs": [0.19087, 0.32198, 0.30408, 0.31452, 0.38912, 0.30892, 0.49527, 0.30193, 0.62556, 0.30631, 0.66819, 0.37663, 0.71197, 0.44883, 0.73717, 0.49038, 0.77738, 0.5567, 0.80774, 0.60679, 0.99874, 0.92179, 0.91095, 0.98737, 0.82921, 1, 0.78993, 1, 0.55418, 0.96365, 0.32098, 0.8194, 0.32609, 0.78547, 0.3341, 0.73216, 0.35251, 0.67606, 0.37033, 0.62179, 0.34913, 0.57939, 0.32722, 0.53555, 0.30997, 0.45277, 0.20995, 0.44012, 0.0582, 0.40081, 0.08436, 0.329, 0.5221, 0.4988, 0.57039, 0.55284], "triangles": [13, 14, 9, 10, 12, 13, 11, 12, 10, 9, 10, 13, 19, 20, 27, 20, 26, 27, 20, 21, 26, 27, 7, 8, 27, 6, 7, 27, 26, 6, 21, 22, 26, 22, 2, 26, 2, 3, 26, 26, 5, 6, 5, 3, 4, 5, 26, 3, 23, 1, 22, 22, 1, 2, 23, 24, 0, 24, 25, 0, 23, 0, 1, 15, 16, 14, 16, 17, 14, 14, 17, 9, 9, 17, 18, 9, 27, 8, 19, 27, 18, 18, 27, 9], "vertices": [1, 41, 56.88, 49.55, 1, 1, 41, 56.71, 33.85, 1, 1, 41, 56.59, 22.06, 1, 1, 41, 56.44, 7.34, 1, 1, 41, 53.58, -10.44, 1, 1, 41, 38.47, -14.7, 1, 2, 40, 98.52, -24.63, 0.04, 41, 22.96, -19.08, 0.96, 2, 40, 89.24, -24.62, 0.18837, 41, 14.03, -21.61, 0.81163, 2, 40, 74.43, -24.6, 0.66032, 41, -0.22, -25.63, 0.33968, 2, 40, 63.25, -24.58, 0.90737, 41, -10.98, -28.67, 0.09263, 1, 39, 168.67, -25.87, 1, 1, 39, 153.17, -16.35, 1, 1, 39, 148.6, -5.71, 1, 1, 39, 147.64, -0.38, 1, 1, 39, 149.29, 32.97, 1, 2, 40, 47.74, 54.21, 0.99989, 41, -47.43, 42.89, 0.00011, 2, 40, 53.98, 50.92, 0.99713, 41, -40.52, 41.43, 0.00287, 2, 40, 63.79, 45.75, 0.97394, 41, -29.67, 39.13, 0.02606, 2, 40, 73.59, 39.02, 0.88626, 41, -18.41, 35.34, 0.11374, 2, 40, 83.08, 32.52, 0.64092, 41, -7.51, 31.67, 0.35908, 2, 40, 92.31, 31.93, 0.36046, 41, 1.54, 33.63, 0.63954, 2, 40, 101.85, 31.32, 0.16888, 41, 10.89, 35.65, 0.83112, 2, 40, 118.63, 27.08, 0.01275, 41, 28.18, 36.15, 0.98725, 2, 40, 126.24, 38.88, 3e-05, 41, 32.28, 49.59, 0.99997, 1, 41, 42.65, 69.52, 1, 1, 41, 57.03, 64.32, 1, 2, 40, 98.79, 3.54, 0.00832, 41, 15.53, 8.09, 0.99168, 1, 41, 3.68, 2.68, 1], "hull": 26, "edges": [6, 8, 20, 22, 22, 24, 24, 26, 26, 28, 48, 50, 48, 46, 46, 44, 44, 42, 28, 30, 12, 14, 14, 16, 16, 18, 18, 20, 42, 52, 52, 12, 38, 40, 40, 42, 40, 54, 54, 14, 30, 32, 32, 34, 34, 36, 36, 38, 0, 50, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12], "width": 138, "height": 207}}, "ys": {"ys": {"type": "mesh", "uvs": [0.69066, 0.04473, 0.70922, 0.11875, 0.723, 0.17372, 0.73688, 0.22906, 0.74853, 0.27552, 0.7652, 0.34202, 0.80122, 0.392, 0.83557, 0.37993, 0.86906, 0.36817, 0.91531, 0.39283, 0.95381, 0.41335, 0.99372, 0.43464, 0.99448, 0.49542, 0.995, 0.53785, 0.99593, 0.61247, 0.99692, 0.6921, 0.99743, 0.7333, 0.99807, 0.78475, 0.99874, 0.83921, 0.97862, 0.90899, 0.9546, 0.91454, 0.90152, 0.92679, 0.85198, 0.93823, 0.77629, 0.95571, 0.71152, 0.97066, 0.65197, 0.98441, 0.60304, 0.98571, 0.5386, 0.98741, 0.45673, 0.98958, 0.3855, 0.99147, 0.33573, 0.99279, 0.26882, 0.99456, 0.21157, 0.99608, 0.16638, 0.99728, 0.10435, 0.99892, 0.0637, 1, 0.04591, 1, 0.02671, 0.96594, 0, 0.91857, 0, 0.908, 0.03395, 0.88795, 0.06804, 0.86782, 0.10726, 0.84466, 0.14194, 0.82418, 0.19774, 0.79122, 0.24012, 0.72077, 0.2927, 0.63803, 0.3355, 0.57067, 0.37313, 0.51143, 0.39761, 0.47291, 0.44134, 0.46828, 0.49965, 0.46209, 0.51913, 0.40106, 0.53542, 0.35003, 0.49741, 0.25926, 0.46934, 0.19222, 0.48521, 0.12235, 0.50614, 0.03023, 0.58055, 0.00898, 0.58731, 0.10042, 0.60869, 0.15918, 0.62914, 0.22066, 0.64308, 0.27943, 0.65702, 0.30746, 0.5715, 0.38069, 0.67469, 0.36984, 0.6059, 0.44217, 0.5464, 0.53077, 0.4488, 0.62751, 0.36235, 0.65283, 0.39209, 0.58954, 0.46181, 0.5371, 0.30007, 0.72787, 0.34004, 0.78935, 0.44508, 0.71702, 0.53153, 0.64017, 0.59009, 0.56242, 0.65331, 0.49099, 0.71001, 0.42318, 0.76578, 0.39877, 0.26531, 0.79965, 0.30663, 0.86048, 0.37475, 0.83875, 0.48083, 0.76381, 0.56682, 0.67367, 0.63941, 0.59112, 0.69524, 0.51618, 0.76894, 0.46187, 0.7989, 0.45336, 0.84541, 0.42517, 0.21112, 0.86635, 0.2653, 0.91905, 0.36729, 0.91801, 0.45334, 0.87978, 0.51815, 0.82192, 0.59358, 0.73822, 0.66688, 0.65246, 0.72531, 0.57083, 0.78587, 0.50471, 0.13003, 0.9059, 0.17976, 0.95819, 0.34911, 0.96212, 0.06685, 0.93205, 0.11255, 0.96996, 0.42463, 0.95456, 0.49675, 0.91058, 0.58502, 0.84148, 0.56242, 0.93885, 0.68298, 0.74202, 0.68406, 0.8708, 0.75187, 0.66245, 0.77878, 0.76505, 0.79278, 0.87917, 0.81323, 0.59963, 0.83476, 0.72108, 0.84337, 0.8195, 0.84768, 0.49075, 0.86382, 0.57869, 0.88428, 0.70119, 0.89504, 0.80484, 0.8745, 0.41863, 0.90331, 0.4985, 0.92708, 0.57837, 0.94077, 0.63932, 0.95157, 0.71428, 0.95229, 0.78995, 0.95445, 0.8579, 0.9767, 0.54176, 0.97877, 0.61332, 0.96944, 0.69394, 0.88758, 0.53974, 0.90312, 0.62138, 0.92178, 0.75139, 0.9197, 0.85116], "triangles": [72, 46, 69, 69, 47, 70, 46, 47, 69, 47, 48, 70, 49, 50, 71, 71, 48, 49, 71, 70, 48, 45, 46, 72, 120, 8, 9, 89, 7, 8, 120, 89, 8, 6, 7, 89, 88, 6, 89, 116, 89, 120, 88, 89, 116, 10, 121, 120, 10, 120, 9, 116, 120, 121, 98, 88, 116, 130, 116, 121, 121, 11, 127, 10, 11, 121, 11, 12, 127, 127, 12, 13, 122, 130, 121, 127, 122, 121, 117, 116, 130, 117, 130, 122, 98, 116, 117, 117, 113, 98, 127, 13, 14, 128, 122, 127, 14, 128, 127, 131, 117, 122, 123, 131, 122, 128, 123, 122, 128, 14, 15, 129, 123, 128, 15, 129, 128, 117, 118, 113, 118, 131, 123, 124, 118, 123, 131, 118, 117, 129, 124, 123, 118, 114, 113, 129, 15, 16, 125, 124, 129, 132, 118, 124, 16, 125, 129, 90, 44, 80, 43, 44, 90, 99, 42, 43, 99, 43, 90, 41, 42, 99, 38, 39, 40, 102, 40, 41, 102, 41, 99, 38, 40, 102, 100, 99, 90, 100, 90, 91, 37, 38, 102, 103, 102, 99, 103, 99, 100, 100, 91, 31, 32, 100, 31, 33, 103, 100, 33, 100, 32, 102, 35, 36, 34, 103, 33, 36, 37, 102, 35, 102, 103, 34, 35, 103, 80, 45, 72, 44, 45, 80, 81, 80, 73, 90, 80, 81, 91, 90, 81, 91, 81, 92, 93, 101, 92, 101, 91, 92, 29, 101, 104, 101, 31, 91, 30, 101, 29, 30, 31, 101, 74, 73, 72, 80, 72, 73, 83, 82, 73, 93, 82, 83, 81, 73, 82, 92, 81, 82, 92, 82, 93, 104, 93, 105, 104, 101, 93, 28, 104, 105, 27, 28, 105, 29, 104, 28, 109, 107, 106, 84, 83, 74, 83, 84, 95, 74, 72, 69, 94, 83, 95, 83, 73, 74, 106, 94, 95, 94, 93, 83, 105, 93, 94, 105, 94, 106, 107, 105, 106, 27, 105, 107, 27, 107, 26, 69, 70, 68, 74, 69, 68, 75, 68, 67, 84, 75, 76, 74, 68, 75, 84, 74, 75, 51, 52, 64, 66, 67, 51, 66, 51, 64, 67, 66, 77, 71, 51, 67, 76, 67, 77, 85, 76, 77, 68, 71, 67, 75, 67, 76, 84, 76, 85, 71, 50, 51, 68, 70, 71, 59, 58, 0, 57, 58, 59, 59, 0, 1, 56, 57, 59, 60, 59, 1, 60, 1, 2, 60, 55, 56, 60, 56, 59, 61, 60, 2, 61, 2, 3, 54, 55, 60, 54, 60, 61, 62, 61, 3, 62, 3, 4, 54, 61, 62, 63, 62, 4, 63, 4, 5, 53, 54, 62, 53, 62, 63, 65, 63, 5, 64, 53, 63, 65, 64, 63, 79, 5, 6, 65, 5, 79, 52, 53, 64, 78, 65, 79, 66, 64, 65, 66, 65, 78, 88, 79, 6, 87, 79, 88, 78, 79, 87, 77, 66, 78, 98, 87, 88, 86, 77, 78, 86, 78, 87, 86, 87, 98, 97, 86, 98, 113, 97, 98, 85, 77, 86, 85, 86, 97, 96, 85, 97, 110, 97, 113, 96, 97, 110, 84, 85, 96, 114, 110, 113, 95, 84, 96, 108, 96, 110, 95, 96, 108, 111, 110, 114, 108, 110, 111, 125, 16, 17, 132, 124, 125, 118, 119, 114, 119, 132, 125, 132, 119, 118, 115, 111, 114, 119, 115, 114, 125, 17, 18, 133, 119, 125, 126, 133, 125, 18, 126, 125, 109, 108, 111, 112, 111, 115, 109, 111, 112, 19, 126, 18, 20, 126, 19, 119, 21, 115, 133, 21, 119, 22, 112, 115, 20, 21, 133, 20, 133, 126, 21, 22, 115, 23, 109, 112, 23, 112, 22, 24, 109, 23, 106, 108, 109, 108, 106, 95, 25, 109, 24, 25, 107, 109, 26, 107, 25], "vertices": [1, 37, 4.29, 68.33, 1, 1, 37, 43.11, 65.33, 1, 1, 37, 71.94, 63.11, 1, 2, 37, 100.96, 60.87, 0.99948, 38, 202.66, 135.89, 0.00052, 2, 37, 125.32, 58.99, 0.99078, 38, 185.1, 118.9, 0.00922, 2, 37, 160.2, 56.29, 0.91101, 38, 159.96, 94.58, 0.08899, 2, 37, 190.04, 65.37, 0.5828, 38, 146.96, 66.22, 0.4172, 2, 37, 189.48, 83.51, 0.31715, 38, 160.91, 54.61, 0.68285, 2, 37, 188.94, 101.2, 0.20194, 38, 174.51, 43.28, 0.79806, 2, 37, 208.07, 119.13, 0.08, 38, 175.25, 17.08, 0.92, 2, 37, 223.99, 134.05, 0.01376, 38, 175.87, -4.73, 0.98624, 1, 38, 176.5, -27.35, 1, 1, 38, 149.89, -43.36, 1, 2, 37, 290.82, 133.72, 0.0036, 38, 131.31, -54.55, 0.9964, 2, 37, 327.2, 122.31, 0.06499, 38, 98.64, -74.21, 0.93501, 3, 37, 366.03, 110.12, 0.29124, 49, -200.58, 159.21, 3e-05, 38, 63.77, -95.19, 0.70873, 3, 37, 386.12, 103.81, 0.44515, 49, -186.21, 174.6, 0.03745, 38, 45.73, -106.05, 0.5174, 3, 37, 411.2, 95.94, 0.57734, 49, -168.26, 193.8, 0.13454, 38, 23.21, -119.61, 0.28812, 6, 37, 437.76, 87.6, 0.59811, 48, -45.55, 243.77, 0.00025, 49, -149.25, 214.14, 0.12302, 50, -230.58, 183.96, 0.07336, 51, -304.21, 128.53, 0.06869, 38, -0.64, -133.96, 0.13657, 7, 37, 468.54, 67.01, 0.59724, 48, -10.07, 254.38, 0.00196, 49, -117.37, 232.98, 0.01626, 50, -201.62, 207.04, 0.12101, 51, -280.15, 156.69, 0.1408, 52, -350.68, 99.67, 0.06137, 38, -36.46, -143.35, 0.06137, 7, 37, 467.52, 54.78, 0.60133, 48, -1.29, 245.8, 0.00309, 49, -106.78, 226.77, 0.0189, 50, -190.28, 202.35, 0.12066, 51, -268.13, 154.24, 0.14061, 52, -338.42, 99.27, 0.06258, 38, -44.94, -134.48, 0.05283, 7, 37, 465.27, 27.76, 0.61272, 48, 18.11, 226.86, 0.01023, 49, -83.4, 213.05, 0.03397, 50, -165.21, 192.01, 0.10963, 51, -241.55, 148.84, 0.1396, 52, -311.32, 98.37, 0.06936, 38, -63.68, -114.87, 0.02449, 7, 37, 463.17, 2.54, 0.60186, 48, 36.21, 209.18, 0.02272, 49, -61.57, 200.24, 0.05857, 50, -141.83, 182.35, 0.09697, 51, -216.76, 143.8, 0.13862, 52, -286.03, 97.53, 0.07587, 38, -81.16, -96.58, 0.00539, 6, 37, 459.95, -35.98, 0.53431, 48, 63.87, 182.16, 0.04932, 49, -28.23, 180.68, 0.11494, 50, -106.09, 167.59, 0.08814, 51, -178.87, 136.1, 0.1243, 52, -247.39, 96.25, 0.08899, 7, 37, 457.21, -68.96, 0.43804, 48, 87.54, 159.04, 0.06983, 49, 0.31, 163.93, 0.17855, 50, -75.51, 154.97, 0.09247, 51, -146.45, 129.51, 0.10219, 52, -214.32, 95.15, 0.11795, 38, -130.73, -44.71, 0.00095, 7, 37, 454.68, -99.27, 0.34584, 48, 109.3, 137.79, 0.07605, 49, 26.55, 148.54, 0.2385, 50, -47.39, 143.36, 0.10906, 51, -116.64, 123.45, 0.0784, 52, -183.92, 94.14, 0.15, 38, -151.75, -22.72, 0.00215, 7, 37, 447.75, -122.59, 0.26963, 48, 122.86, 117.59, 0.06963, 49, 44.57, 132.2, 0.27725, 50, -27.28, 129.67, 0.14975, 51, -94.29, 113.84, 0.08115, 52, -160.28, 88.38, 0.15, 38, -164.61, -2.07, 0.0026, 7, 37, 438.62, -153.3, 0.16863, 48, 140.72, 90.99, 0.04494, 49, 68.31, 110.68, 0.29455, 50, -0.79, 111.65, 0.23474, 51, -64.86, 101.18, 0.1113, 52, -129.15, 80.79, 0.14368, 38, -181.55, 25.12, 0.00215, 7, 37, 427.02, -192.31, 0.06891, 48, 163.41, 57.2, 0.01087, 49, 98.47, 83.35, 0.19754, 50, 32.87, 88.76, 0.35456, 51, -27.47, 85.09, 0.21725, 52, -89.61, 71.16, 0.15, 38, -203.06, 59.68, 0.00086, 7, 37, 416.93, -226.26, 0.02234, 48, 183.15, 27.8, 0.00039, 49, 124.7, 59.56, 0.07153, 50, 62.15, 68.84, 0.32656, 51, 5.06, 71.09, 0.41229, 52, -55.2, 62.77, 0.16672, 38, -221.78, 89.74, 0.00017, 6, 37, 409.88, -249.98, 0.00602, 49, 143.04, 42.94, 0.01549, 50, 82.6, 54.92, 0.15764, 51, 27.79, 61.31, 0.56128, 52, -31.16, 56.92, 0.25956, 38, -234.86, 110.74, 1e-05, 4, 37, 400.4, -281.87, 0.00018, 50, 110.11, 36.21, 0.01433, 51, 58.35, 48.16, 0.37293, 52, 1.16, 49.04, 0.61257, 2, 51, 84.5, 36.91, 0.08462, 52, 28.82, 42.3, 0.91538, 2, 51, 105.13, 28.03, 0.00095, 52, 50.64, 36.98, 0.99905, 1, 52, 80.61, 29.68, 1, 1, 52, 100.25, 24.9, 1, 1, 52, 108.78, 22.57, 1, 1, 52, 113.4, 3.27, 1, 1, 52, 119.84, -23.58, 1, 1, 52, 118.42, -28.79, 1, 1, 52, 99.45, -34.23, 1, 2, 51, 121.69, -52.54, 0.00121, 52, 80.39, -39.7, 0.99879, 3, 50, 130.45, -72.89, 0.00192, 51, 99.03, -55.09, 0.02898, 52, 58.47, -45.99, 0.9691, 5, 48, 175.62, -120.2, 0.0004, 49, 153.02, -85.9, 0.00068, 50, 110.35, -71.3, 0.01633, 51, 78.99, -57.34, 0.12315, 52, 39.09, -51.55, 0.85943, 8, 46, 213.64, -149.62, 0.00021, 47, 185.52, -116.76, 0.00157, 48, 146.57, -105.77, 0.00933, 49, 121.34, -78.89, 0.02207, 50, 78, -68.74, 0.11426, 51, 46.75, -60.97, 0.37068, 52, 7.9, -60.5, 0.36189, 2, -68.39, 137.5, 0.12, 8, 46, 173.04, -140.03, 0.00577, 47, 143.85, -114.99, 0.01488, 48, 104.88, -107.22, 0.04678, 49, 81.23, -90.32, 0.10087, 50, 39.86, -85.63, 0.20449, 51, 12.51, -84.79, 0.20726, 52, -21.89, -89.69, 0.03595, 2, -30.68, 119.67, 0.384, 1, 2, 13.73, 97.35, 1, 1, 2, 49.88, 79.18, 1, 1, 2, 81.68, 63.2, 1, 1, 2, 102.35, 52.81, 1, 7, 46, 20.38, -82.21, 0.11753, 47, -16.97, -86.97, 0.06482, 48, -57.62, -91.69, 0.0252, 49, -80.24, -114.36, 0.00695, 50, -116.72, -131.8, 0.00147, 51, -132.45, -159.86, 2e-05, 2, 106.62, 31.37, 0.784, 6, 37, 177.47, -88.22, 0.02604, 46, 8.87, -55.44, 0.77796, 47, -33.32, -62.84, 0.15035, 48, -75.78, -68.89, 0.03719, 49, -103.35, -96.6, 0.00754, 50, -142.07, -117.42, 0.00091, 5, 37, 150.84, -69.32, 0.32878, 46, -23.79, -55.32, 0.65365, 47, -65.42, -68.87, 0.01522, 48, -107.32, -77.38, 0.00222, 49, -131.91, -112.43, 0.00013, 3, 37, 128.57, -53.52, 0.73023, 46, -51.09, -55.22, 0.2696, 47, -92.25, -73.92, 0.00017, 2, 37, 78.61, -57.05, 0.98447, 46, -89.9, -86.87, 0.01553, 2, 37, 41.72, -59.66, 0.99992, 46, -118.57, -110.24, 8e-05, 1, 37, 10.23, -41.05, 1, 1, 37, -31.27, -16.53, 1, 1, 37, -30.09, 21.99, 1, 1, 37, 15.36, 10.66, 1, 1, 37, 47.21, 11.42, 1, 1, 37, 80.23, 11.31, 1, 2, 37, 110.92, 8.56, 0.99997, 38, 156.89, 163.11, 3e-05, 2, 37, 126.69, 10.69, 0.99916, 38, 148.03, 149.9, 0.00084, 3, 37, 149.04, -41.35, 0.60429, 46, -41.37, -33.48, 0.39492, 47, -86.79, -50.74, 0.00078, 2, 37, 159.72, 9.12, 0.99306, 38, 124.96, 126.22, 0.00694, 3, 37, 184.21, -34.87, 0.27545, 46, -16.34, -7.93, 0.72424, 47, -67.03, -20.93, 0.0003, 5, 46, 35.62, -22.93, 0.81035, 47, -13.18, -25.88, 0.17245, 48, -58.55, -30.49, 0.01488, 49, -95.87, -55.18, 0.00221, 50, -140.4, -75.36, 0.00011, 6, 46, 97.09, -54.83, 0.10864, 47, 53.21, -45.62, 0.31967, 48, 9.16, -45.05, 0.43435, 49, -26.64, -53.02, 0.10984, 50, -72.14, -63.63, 0.0225, 51, -101.63, -84.47, 0.005, 7, 46, 122.05, -92.12, 0.06575, 47, 84.74, -77.54, 0.12497, 48, 43.06, -74.44, 0.25588, 49, 13.34, -73.39, 0.2557, 50, -29.72, -78.26, 0.11883, 51, -57.2, -90.78, 0.04288, 2, 9.23, 62.2, 0.136, 7, 46, 86.8, -87.46, 0.177, 47, 49.25, -79.61, 0.25717, 48, 7.83, -79.24, 0.25976, 49, -19.7, -86.53, 0.1286, 50, -60.62, -95.85, 0.04511, 51, -84.2, -113.92, 0.01236, 2, 42.74, 50.31, 0.12, 7, 46, 51.03, -62.18, 0.68028, 47, 9.35, -61.52, 0.07472, 48, -33.34, -64.29, 0.02742, 49, -63.26, -81.92, 0.007, 50, -104.4, -97.33, 0.00159, 51, -126.9, -123.67, 0.00012, 2, 72.48, 18.15, 0.20887, 8, 46, 167.78, -110.48, 0.01237, 47, 133.12, -86.96, 0.02923, 48, 92.02, -80.1, 0.09, 49, 62.22, -67.1, 0.21104, 50, 17.82, -65.26, 0.32665, 51, -13, -68.99, 0.20372, 52, -49.68, -78.35, 0.01499, 2, -31.68, 89.68, 0.112, 7, 46, 192, -82.28, 0.00168, 47, 151.59, -54.7, 0.00522, 48, 107.95, -46.52, 0.02381, 49, 69.59, -30.67, 0.14039, 50, 20.07, -28.16, 0.67646, 51, -17.83, -32.13, 0.14961, 52, -60.58, -42.82, 0.00284, 6, 46, 141.37, -43.2, 0.00791, 47, 94.5, -25.86, 0.02108, 48, 48.8, -22.16, 0.40458, 49, 6.32, -21.26, 0.54332, 50, -43.89, -27.61, 0.01879, 51, -80.73, -43.74, 0.00432, 5, 46, 91.23, -13.62, 0.00755, 47, 39.69, -6.26, 0.8892, 48, -7.36, -6.85, 0.10145, 49, -51.87, -19.91, 0.00153, 50, -101.71, -34.34, 0.00028, 2, 37, 240.17, -61.44, 0.01445, 46, 44.71, 2.56, 0.98555, 2, 37, 215.25, -20.23, 0.53427, 46, 0.61, 21.91, 0.46573, 2, 37, 191.08, 17.33, 0.94841, 38, 110.31, 97.29, 0.05159, 2, 37, 187.85, 47.55, 0.77449, 38, 135.08, 79.67, 0.22551, 8, 46, 207.92, -116.25, 0.00071, 47, 173.62, -85.06, 0.00335, 48, 132.25, -75.09, 0.01619, 49, 100.06, -52.55, 0.04293, 50, 53.28, -45.61, 0.2406, 51, 18.09, -42.95, 0.48879, 52, -23.36, -47.51, 0.10341, 2, -69.74, 103.67, 0.104, 6, 47, 191.48, -52.37, 5e-05, 48, 147.54, -41.12, 0.00059, 49, 106.72, -15.9, 0.00058, 50, 54.8, -8.39, 0.03348, 51, 12.51, -6.12, 0.96368, 52, -34.99, -12.12, 0.00162, 3, 37, 341.1, -207.07, 0.00045, 50, 20.84, 2.41, 0.99955, 38, -157.14, 133.78, 1e-05, 4, 37, 321.09, -145.05, 0.01228, 48, 59.51, 5.64, 0.07076, 49, 10.03, 8.3, 0.91626, 38, -97.45, 107.63, 0.0007, 5, 37, 290.6, -90.11, 0.09031, 47, 46.61, 17.25, 0.49195, 48, -2.27, 17.13, 0.41316, 49, -52.7, 4.58, 0.0016, 38, -36.11, 94.03, 0.00298, 4, 37, 261.73, -42.71, 0.40679, 46, 51.56, 30.29, 0.27019, 47, -7.55, 29.4, 0.31994, 48, -57.2, 25.06, 0.00308, 3, 37, 233.97, -4.43, 0.95335, 46, 6.81, 45.61, 0.04544, 47, -54.38, 36.01, 0.00121, 2, 37, 218.98, 39.02, 0.70318, 38, 108.05, 62.03, 0.29682, 2, 37, 219.48, 54.52, 0.52166, 38, 119.32, 51.38, 0.47834, 2, 37, 212.97, 80.97, 0.27393, 38, 143.43, 38.71, 0.72607, 5, 48, 175.45, -79.63, 0.00061, 49, 143.09, -46.56, 0.00085, 50, 95.06, -33.71, 0.01814, 51, 56.84, -23.34, 0.24883, 52, 11.59, -21.71, 0.73157, 3, 50, 88.96, 3.88, 0.00018, 51, 43.72, 12.41, 0.6937, 52, -7.3, 11.35, 0.30612, 6, 37, 378.44, -223.19, 0.01127, 49, 105.28, 26.19, 0.03663, 50, 47.54, 33.09, 0.41347, 51, -2.5, 33.22, 0.47104, 52, -56.35, 24.18, 0.06751, 38, -193.96, 116.52, 8e-05, 6, 37, 373.17, -176.47, 0.05476, 48, 116.89, 25.78, 0.01153, 49, 60.88, 41.65, 0.37228, 50, 1.42, 42.26, 0.4494, 51, -49.52, 33.46, 0.11081, 38, -155.49, 89.48, 0.00121, 7, 37, 355.08, -136.66, 0.09943, 47, 124.88, 31.32, 0.00033, 48, 74.68, 37.19, 0.12997, 49, 17.17, 42.57, 0.65155, 50, -42, 37.11, 0.11355, 51, -91.17, 20.16, 0.00074, 38, -113.7, 76.63, 0.00444, 6, 37, 326.09, -87.73, 0.24311, 47, 69.54, 44.44, 0.1196, 48, 18.5, 46, 0.45209, 49, -39.49, 37.6, 0.16546, 50, -97.42, 24.34, 0.00466, 38, -57.85, 65.89, 0.01509, 6, 37, 295.77, -39.48, 0.62874, 46, 77.53, 52.53, 0.01703, 47, 13.76, 56.13, 0.2861, 48, -38.01, 53.36, 0.0622, 49, -96.11, 31.15, 0.00264, 38, -1.63, 56.59, 0.00328, 2, 37, 265.16, 1.09, 0.99642, 38, 49.04, 52.61, 0.00358, 2, 37, 242.4, 40.2, 0.56321, 38, 93.41, 43.71, 0.43679, 2, 51, 101.91, -21.93, 0.00258, 52, 55.79, -12.82, 0.99742, 2, 51, 90.71, 12.7, 0.00045, 52, 38.98, 19.46, 0.99955, 6, 37, 397.05, -238.79, 0.00853, 49, 127.39, 36.22, 0.02384, 50, 68.05, 46.09, 0.21505, 51, 15.17, 49.88, 0.54865, 52, -41.7, 43.54, 0.20389, 38, -217.98, 112.93, 3e-05, 1, 52, 89.6, -8.19, 1, 1, 52, 72.78, 16.48, 1, 7, 37, 405.05, -201.91, 0.04301, 48, 156.81, 34.15, 0.00385, 49, 97.61, 59.38, 0.14655, 50, 35.34, 64.91, 0.40023, 51, -20.51, 62.14, 0.29877, 52, -78.93, 49.69, 0.1071, 38, -195.68, 82.49, 0.00051, 7, 37, 394.84, -160.86, 0.09715, 48, 118.66, 52.42, 0.03092, 49, 56.18, 67.94, 0.30791, 50, -6.88, 67.64, 0.36816, 51, -62.48, 56.81, 0.17985, 52, -119.42, 37.44, 0.0141, 38, -158.17, 62.91, 0.00191, 7, 37, 374.92, -108.18, 0.22102, 47, 118.16, 65.37, 0.00526, 48, 65.36, 70.62, 0.16126, 49, 0.07, 72.78, 0.44614, 50, -63.11, 64.65, 0.15396, 51, -117.13, 43.2, 0.00339, 38, -105.54, 42.89, 0.00898, 7, 37, 418.71, -134.33, 0.18173, 48, 113.42, 87.73, 0.05658, 49, 42.6, 100.95, 0.27349, 50, -24.9, 98.44, 0.26059, 51, -86.03, 83.63, 0.15706, 52, -147.1, 59.97, 0.06785, 38, -154.15, 27.45, 0.0027, 6, 37, 341.75, -46.11, 0.58287, 47, 50.47, 84.62, 0.08802, 48, -3.62, 84.59, 0.15566, 49, -70.24, 69.73, 0.11468, 50, -132.33, 51.9, 0.00575, 38, -37.08, 26.56, 0.05303, 7, 37, 404.46, -66.06, 0.42214, 47, 108.37, 115.88, 0.00344, 48, 51.71, 120.22, 0.09717, 49, -25.12, 117.64, 0.23481, 50, -94.28, 105.59, 0.1777, 51, -155.5, 77.47, 0.05803, 38, -93.59, -7.15, 0.0067, 4, 37, 313.76, -0.92, 0.99834, 47, -1.49, 95.85, 0.00103, 48, -56.28, 91.79, 0.00057, 49, -123.09, 64.05, 6e-05, 5, 37, 367.75, -4.51, 0.8403, 47, 38.59, 132.21, 0.00234, 48, -19.13, 131.12, 0.02478, 49, -96.49, 111.17, 0.12819, 50, -164.07, 89.29, 0.00439, 6, 37, 425.33, -16.03, 0.59891, 47, 86.87, 165.64, 2e-05, 48, 26.44, 168.18, 0.03887, 49, -61.19, 158.1, 0.1272, 50, -135.61, 140.66, 0.15679, 51, -202.74, 104.06, 0.07822, 2, 37, 292.73, 38.05, 0.09091, 38, 58.42, 7.46, 0.90909, 5, 37, 355.04, 28.92, 0.51286, 48, -52.99, 142.62, 0.00032, 49, -132.13, 114.17, 0.01585, 50, -199.78, 87.33, 0.00028, 38, 10.27, -33.13, 0.4707, 5, 37, 404.17, 17.35, 0.71965, 48, -12.78, 173.12, 0.01438, 49, -100.44, 153.46, 0.18608, 50, -173.84, 130.63, 0.03817, 38, -30.96, -62.23, 0.04171, 2, 37, 245.17, 71.62, 0.16107, 38, 115.09, 20.8, 0.83893, 1, 38, 80.36, -8.83, 1, 3, 37, 353.03, 55.47, 0.30727, 49, -156.93, 123.85, 0.00062, 38, 31.48, -49.23, 0.69211, 5, 37, 405.04, 44.08, 0.67524, 48, -32.84, 190.82, 0.0037, 49, -124.17, 165.81, 0.1692, 50, -199.05, 139.57, 0.00541, 38, -11.52, -80.61, 0.14645, 2, 37, 214.3, 95.75, 0.15761, 38, 153.62, 27.92, 0.84239, 1, 38, 125.64, -5.06, 1, 2, 37, 300, 95.2, 0.01262, 38, 96.39, -35.88, 0.98738, 2, 37, 331.72, 91.98, 0.1031, 38, 72.95, -57.48, 0.8969, 3, 37, 369.79, 85.18, 0.38049, 49, -176.47, 151.82, 0.00119, 38, 42.6, -81.47, 0.61833, 5, 37, 406.65, 73.49, 0.59829, 48, -54.48, 210.8, 0.00022, 49, -149.99, 179.99, 0.14692, 50, -226.58, 150.04, 0.0002, 38, 9.42, -101.32, 0.25437, 6, 37, 439.99, 63.71, 0.62887, 48, -25.71, 230.27, 0.00168, 49, -126.75, 205.82, 0.09715, 50, -207.14, 178.84, 0.09822, 51, -280.22, 127.95, 0.07953, 38, -20, -119.79, 0.09453, 2, 37, 289.89, 124.46, 0.00408, 38, 124.99, -47.7, 0.99592, 2, 37, 324.96, 114.06, 0.06505, 38, 93.96, -67.07, 0.93495, 3, 37, 362.68, 96.85, 0.29537, 49, -190.07, 150.45, 4e-05, 38, 56.06, -83.88, 0.70458, 1, 38, 103.5, -8.96, 1, 2, 37, 317.19, 77.05, 0.04185, 38, 71.41, -36.71, 0.95815, 5, 37, 383.21, 65.2, 0.52869, 48, -63.02, 187.44, 7e-05, 49, -152.66, 155.27, 0.0723, 50, -225.8, 125.19, 0.00014, 38, 18.76, -78.27, 0.3988, 6, 37, 431.35, 48.37, 0.64837, 48, -19.39, 213.83, 0.00406, 49, -116.65, 191.38, 0.11405, 50, -195.15, 165.94, 0.08579, 51, -265.99, 117.57, 0.06538, 38, -25.76, -103.15, 0.08235], "hull": 59, "edges": [0, 116, 10, 12, 36, 38, 70, 72, 76, 78, 88, 90, 114, 116, 110, 112, 112, 114, 112, 118, 118, 0, 110, 120, 0, 2, 120, 2, 106, 108, 108, 110, 108, 122, 2, 4, 122, 4, 106, 124, 4, 6, 124, 6, 6, 8, 8, 10, 8, 126, 126, 128, 128, 102, 10, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 94, 94, 96, 96, 98, 96, 140, 140, 142, 142, 102, 90, 92, 92, 94, 92, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 12, 90, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 12, 176, 178, 178, 16, 88, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 176, 86, 88, 86, 198, 198, 200, 200, 62, 62, 202, 202, 186, 82, 204, 204, 206, 206, 66, 78, 80, 80, 82, 72, 74, 74, 76, 66, 68, 68, 70, 186, 208, 208, 58, 188, 210, 56, 58, 210, 56, 190, 212, 212, 214, 54, 56, 214, 54, 192, 216, 216, 218, 218, 50, 194, 220, 220, 222, 222, 224, 224, 46, 196, 226, 226, 228, 228, 230, 44, 46, 230, 44, 178, 232, 232, 234, 234, 236, 236, 238, 42, 44, 238, 42, 16, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 38, 40, 40, 42, 252, 40, 22, 254, 254, 256, 256, 258, 258, 250, 26, 28, 28, 30, 34, 36, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 60, 62, 62, 64, 64, 66, 102, 104, 104, 106, 98, 100, 100, 102, 16, 18, 18, 20, 20, 22, 12, 14, 14, 16, 22, 24, 24, 26, 30, 32, 32, 34, 82, 84, 84, 86], "width": 497, "height": 511}}, "ys5": {"ys5": {"type": "mesh", "uvs": [0.72502, 0.01725, 0.77782, 0.53825, 0.83351, 0.63923, 0.88995, 0.74156, 0.94055, 0.8333, 1, 0.9411, 1, 0.96598, 0.81213, 0.97411, 0.66271, 0.98057, 0.43821, 0.99028, 0.36785, 0.84259, 0.28561, 0.66997, 0.28533, 0.47107, 0.2849, 0.16369, 0.3986, 0.01767], "triangles": [4, 7, 8, 9, 10, 8, 6, 7, 5, 7, 4, 5, 4, 8, 3, 3, 8, 10, 10, 2, 3, 2, 11, 1, 2, 10, 11, 13, 0, 1, 13, 14, 0, 1, 11, 12, 1, 12, 13], "vertices": [1, 43, 48.33, -8.99, 1, 1, 43, 19.83, -5.45, 1, 1, 43, 14.01, -6.33, 1, 2, 43, 8.11, -7.21, 0.896, 41, 64.95, 19.4, 0.104, 2, 43, 2.82, -8.01, 0.784, 41, 59.74, 18.19, 0.216, 1, 41, 53.62, 16.77, 1, 1, 41, 52.26, 16.91, 1, 1, 41, 52.53, 23.5, 1, 1, 41, 52.75, 28.74, 1, 1, 41, 53.07, 36.61, 1, 1, 43, 6.07, 11.78, 1, 1, 43, 15.93, 12.83, 1, 1, 43, 26.68, 10.79, 1, 1, 43, 43.29, 7.65, 1, 1, 43, 50.44, 2.24, 1], "hull": 15, "edges": [0, 28, 0, 2, 10, 12, 26, 28, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 6, 8, 8, 10, 2, 4, 4, 6], "width": 35, "height": 55}}, "ys6": {"ys6": {"type": "mesh", "uvs": [0.08218, 0.03639, 0.26047, 0.03686, 0.51309, 0.18839, 0.57876, 0.27047, 0.62645, 0.33008, 0.72799, 0.45699, 0.795, 0.46898, 0.83603, 0.50598, 0.98647, 0.64164, 0.98669, 0.79553, 0.92791, 0.94697, 0.78343, 0.98308, 0.62816, 0.91301, 0.53446, 0.87073, 0.4963, 0.79596, 0.37511, 0.5585, 0.33149, 0.47302, 0.27731, 0.41643, 0.07489, 0.20504, 0.04742, 0.0675], "triangles": [12, 7, 8, 12, 8, 9, 9, 11, 12, 10, 11, 9, 6, 7, 5, 16, 17, 3, 17, 18, 1, 18, 0, 1, 17, 2, 3, 17, 1, 2, 18, 19, 0, 14, 7, 12, 5, 7, 14, 14, 12, 13, 14, 15, 5, 15, 4, 5, 15, 16, 4, 16, 3, 4], "vertices": [1, 45, 39.15, -0.52, 1, 1, 45, 29.99, -9.76, 1, 2, 44, 57.63, -14.09, 0.04341, 45, 8.3, -14.27, 0.95659, 2, 44, 49.51, -13, 0.39883, 45, 0.2, -13.01, 0.60117, 2, 44, 43.61, -12.21, 0.79908, 45, -5.68, -12.1, 0.20092, 1, 44, 31.04, -10.53, 1, 2, 44, 26.98, -13.42, 0.424, 40, 84.33, 30.99, 0.576, 1, 40, 80.42, 29.34, 1, 1, 40, 66.11, 23.3, 1, 1, 40, 54.55, 27.97, 1, 1, 40, 44.8, 36.56, 1, 1, 40, 46.06, 47.44, 1, 1, 44, 9.23, 20.15, 1, 1, 44, 16.42, 22.77, 1, 1, 44, 22.73, 20.63, 1, 2, 44, 42.78, 13.83, 0.88261, 45, -5.97, 13.96, 0.11739, 2, 44, 50, 11.38, 0.45633, 45, 1.19, 11.36, 0.54367, 2, 44, 56.05, 11.11, 0.09601, 45, 7.24, 10.96, 0.90399, 1, 45, 29.8, 9.45, 1, 1, 45, 39.14, 3.06, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 10, 12, 16, 18, 18, 20, 20, 22, 36, 38, 32, 34, 34, 36, 30, 32, 4, 6, 6, 8, 8, 10, 26, 28, 28, 30, 22, 24, 24, 26, 12, 14, 14, 16], "width": 73, "height": 81}}, "t1": {"t1": {"type": "mesh", "uvs": [0.85696, 0, 0.94934, 0.0712, 0.9977, 0.22938, 0.9845, 0.65347, 0.7671, 0.98634, 0.60726, 0.98547, 0.26622, 0.84093, 0.18212, 0.73271, 0.0383, 0.54699, 0.00245, 0.4172, 0.04705, 0.21075, 0.12681, 0.11822, 0.25651, 0.05134, 0.80117, 0, 0.47845, 0.4114, 0.35042, 0.39122, 0.25227, 0.38388, 0.16266, 0.40223, 0.68115, 0.39122, 0.74943, 0.32516, 0.84331, 0.27378, 0.92866, 0.24992, 0.59794, 0.516, 0.66195, 0.6316, 0.66621, 0.67564, 0.65128, 0.70684, 0.59367, 0.705, 0.50832, 0.8151, 0.59153, 0.81694, 0.67262, 0.80593, 0.73236, 0.79125, 0.77077, 0.76923], "triangles": [0, 1, 13, 21, 1, 2, 1, 20, 13, 21, 20, 1, 19, 12, 13, 19, 13, 20, 16, 11, 12, 12, 15, 16, 10, 11, 16, 15, 12, 14, 14, 12, 19, 17, 10, 16, 18, 14, 19, 9, 10, 17, 22, 14, 18, 8, 9, 17, 23, 22, 18, 3, 21, 2, 20, 21, 3, 19, 20, 3, 18, 19, 3, 23, 18, 3, 31, 24, 23, 22, 15, 14, 26, 22, 23, 26, 23, 24, 25, 26, 24, 7, 17, 16, 8, 17, 7, 3, 31, 23, 25, 24, 31, 30, 25, 31, 29, 25, 30, 15, 22, 26, 28, 27, 26, 28, 26, 25, 28, 25, 29, 15, 7, 16, 15, 26, 7, 27, 6, 26, 26, 6, 7, 5, 28, 29, 27, 28, 5, 6, 27, 5, 4, 30, 31, 29, 30, 4, 5, 29, 4, 4, 31, 3], "vertices": [1, 5, 183.22, -53.21, 1, 1, 5, 169.09, -69.19, 1, 1, 5, 137.51, -77.73, 1, 1, 5, 52.68, -76.04, 1, 1, 5, -14.15, -39.1, 1, 1, 5, -14.17, -11.61, 1, 1, 5, 14.34, 47.25, 1, 1, 5, 35.88, 61.86, 1, 1, 5, 72.86, 86.85, 1, 1, 5, 98.77, 93.2, 1, 1, 5, 140.12, 85.81, 1, 1, 5, 158.71, 72.22, 1, 1, 5, 172.24, 50, 1, 1, 5, 183.15, -43.61, 1, 1, 5, 100.49, 11.33, 1, 1, 5, 104.38, 33.38, 1, 1, 5, 105.73, 50.27, 1, 1, 5, 101.96, 65.66, 1, 1, 5, 104.77, -23.5, 1, 1, 5, 118.06, -35.16, 1, 1, 5, 128.45, -51.23, 1, 1, 5, 133.32, -65.88, 1, 1, 5, 79.72, -9.36, 1, 1, 5, 56.67, -20.53, 1, 1, 5, 47.87, -21.32, 1, 1, 5, 41.61, -18.8, 1, 1, 5, 41.91, -8.89, 1, 1, 5, 19.79, 5.64, 1, 1, 5, 19.52, -8.67, 1, 1, 5, 21.82, -22.6, 1, 1, 5, 24.83, -32.86, 1, 1, 5, 29.27, -39.43, 1], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 28, 30, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62], "width": 172, "height": 200}}, "sh8": {"sh2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [86.35, -53.95, -82.65, -53.95, -82.65, 51.05, 86.35, 51.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 169, "height": 105}}, "t4": {"t4": {"type": "mesh", "uvs": [0.8261, 0.083, 0.9325, 0.27166, 0.97586, 0.58925, 0.95784, 0.68323, 0.64997, 0.97511, 0.39705, 0.97373, 0.01443, 0.63333, 0.01401, 0.33185, 0.15939, 0.17355, 0.4788, 0.00403, 0.31128, 0.48097, 0.36143, 0.6315, 0.47143, 0.75019, 0.58952, 0.71834, 0.68172, 0.57939, 0.70069, 0.52755, 0.70761, 0.32466, 0.49893, 0.31308, 0.22717, 0.37097, 0.47628, 0.22045, 0.70275, 0.22913, 0.4051, 0.39703, 0.62776, 0.32023, 0.28324, 0.72541, 0.477, 0.84194, 0.68665, 0.77941, 0.84706, 0.62025], "triangles": [19, 8, 9, 20, 19, 9, 0, 20, 9, 20, 0, 1, 22, 17, 19, 20, 22, 19, 16, 20, 1, 22, 20, 16, 18, 8, 19, 21, 18, 19, 21, 19, 17, 10, 18, 21, 15, 22, 16, 14, 22, 15, 26, 16, 1, 26, 1, 2, 15, 16, 26, 11, 10, 21, 18, 6, 7, 18, 7, 8, 3, 26, 2, 13, 17, 22, 13, 22, 14, 12, 21, 17, 12, 11, 21, 23, 18, 10, 23, 10, 11, 6, 18, 23, 17, 13, 12, 26, 25, 14, 26, 14, 15, 13, 14, 25, 24, 12, 13, 11, 12, 24, 23, 11, 24, 24, 13, 25, 5, 23, 24, 6, 23, 5, 4, 24, 25, 3, 4, 25, 3, 25, 26, 5, 24, 4], "vertices": [4.15, -17.28, -2.78, -9.81, -5.22, 2.37, -3.84, 5.89, 17.54, 16.09, 34.72, 15.32, 60.17, 1.3, 59.72, -10.15, 49.59, -15.74, 27.62, -21.27, 39.76, -3.64, 36.6, 2.22, 29.31, 7.04, 21.24, 6.17, 14.75, 1.16, 13.38, -0.76, 12.59, -8.44, 26.75, -9.48, 45.3, -8.05, 28.14, -13.06, 12.77, -12.08, 33.26, -6.56, 18.01, -8.84, 42.06, 5.56, 29.08, 10.54, 14.74, 8.77, 3.58, 3.18], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 12, 36, 36, 38, 38, 40, 40, 2, 30, 28, 20, 42, 42, 34, 32, 44, 44, 34, 12, 46, 46, 48, 48, 50, 50, 52, 52, 4], "width": 68, "height": 38}}, "st": {"st": {"type": "mesh", "uvs": [0.63668, 0.00067, 0.68641, 0.09744, 0.72581, 0.17412, 0.76853, 0.25726, 0.79863, 0.31582, 0.81796, 0.35344, 0.85997, 0.4352, 0.896, 0.50531, 0.92636, 0.5644, 0.90707, 0.63619, 0.86976, 0.77498, 0.86999, 0.85768, 0.87014, 0.91317, 0.8591, 0.93915, 0.71901, 0.9561, 0.55997, 0.97534, 0.35793, 0.99979, 0.20509, 0.99816, 0.0885, 0.99691, 0.03547, 0.93469, 0.03232, 0.81391, 0.02979, 0.71685, 0.02706, 0.61225, 0.02379, 0.48729, 0.02021, 0.34991, 0.13711, 0.23827, 0.25327, 0.12734, 0.38499, 0.00154, 0.20156, 0.54563, 0.41279, 0.52862, 0.61093, 0.44968, 0.19221, 0.65125, 0.43335, 0.64275, 0.6857, 0.5966, 0.18286, 0.74354, 0.43522, 0.7399, 0.71187, 0.68039, 0.19034, 0.86256, 0.43709, 0.86256, 0.68944, 0.80062, 0.17352, 0.93907, 0.43335, 0.93057, 0.70252, 0.89778, 0.24136, 0.41349, 0.41235, 0.39084, 0.59496, 0.33476, 0.2978, 0.20425, 0.57006, 0.23445, 0.51528, 0.13882], "triangles": [48, 27, 0, 48, 0, 1, 26, 27, 48, 48, 1, 2, 46, 26, 48, 47, 48, 2, 46, 48, 47, 47, 2, 3, 45, 47, 3, 45, 3, 4, 44, 46, 47, 44, 47, 45, 5, 45, 4, 44, 45, 30, 5, 30, 45, 30, 5, 6, 7, 33, 30, 7, 30, 6, 33, 7, 8, 29, 30, 33, 9, 33, 8, 32, 29, 33, 36, 33, 9, 32, 33, 36, 35, 32, 36, 10, 36, 9, 39, 35, 36, 39, 36, 10, 25, 26, 46, 43, 25, 46, 43, 46, 44, 24, 25, 43, 23, 24, 43, 29, 44, 30, 43, 44, 29, 28, 23, 43, 28, 43, 29, 22, 23, 28, 28, 29, 32, 31, 22, 28, 31, 28, 32, 21, 22, 31, 31, 32, 35, 34, 21, 31, 34, 31, 35, 20, 21, 34, 37, 34, 35, 20, 34, 37, 37, 35, 38, 19, 20, 37, 39, 10, 11, 38, 35, 39, 42, 39, 11, 38, 39, 42, 42, 11, 12, 41, 37, 38, 41, 38, 42, 40, 19, 37, 40, 37, 41, 13, 42, 12, 14, 42, 13, 15, 41, 42, 14, 15, 42, 18, 19, 40, 16, 17, 40, 18, 40, 17, 41, 16, 40, 16, 41, 15], "vertices": [2, 3, 211.37, 264.33, 0.00092, 4, 122.91, -29.39, 0.99908, 2, 3, 199.55, 217.76, 0.02685, 4, 86.72, -61, 0.97315, 2, 3, 190.18, 180.86, 0.09912, 4, 58.04, -86.05, 0.90088, 3, 2, 314.7, -152.92, 0.00998, 3, 180.02, 140.84, 0.24889, 4, 26.94, -113.2, 0.74113, 3, 2, 288.03, -164.51, 0.02652, 3, 172.86, 112.66, 0.39828, 4, 5.04, -132.33, 0.5752, 3, 2, 270.9, -171.96, 0.0332, 3, 168.26, 94.55, 0.50847, 4, -9.03, -144.62, 0.45833, 3, 2, 233.67, -188.14, 0.02545, 3, 158.27, 55.21, 0.76031, 4, -39.61, -171.32, 0.21425, 3, 2, 201.74, -202.02, 0.00609, 3, 149.7, 21.46, 0.94023, 4, -65.83, -194.23, 0.05368, 2, 1, 279.82, 128.33, 0.00028, 3, 142.48, -6.98, 0.99972, 2, 1, 267.09, 96.45, 0.04217, 3, 119.24, -32.24, 0.95783, 2, 1, 242.46, 34.83, 0.60603, 3, 74.31, -81.07, 0.39397, 2, 1, 234.52, -3.3, 0.97515, 3, 53.32, -113.89, 0.02485, 1, 1, 229.19, -28.89, 1, 1, 1, 223.36, -40.17, 1, 1, 1, 179.77, -39.16, 1, 1, 1, 130.28, -38.02, 1, 1, 1, 67.41, -36.57, 1, 1, 1, 21.8, -26.19, 1, 2, 1, -12.99, -18.28, 0.99965, 2, -50.58, 23.82, 0.00035, 2, 1, -22.84, 13.74, 0.70902, 2, -22.81, 42.55, 0.29098, 2, 1, -12.08, 69.61, 0.01168, 2, 33.77, 48.5, 0.98832, 1, 2, 79.25, 53.28, 1, 1, 2, 128.25, 58.43, 1, 1, 2, 186.79, 64.59, 1, 1, 2, 251.15, 71.36, 1, 2, 2, 306.67, 40.33, 0.99826, 4, -39.28, 68.53, 0.00174, 2, 2, 361.83, 9.51, 0.34094, 4, 22.63, 55.87, 0.65906, 2, 2, 424.39, -25.45, 0.00169, 4, 92.84, 41.52, 0.99831, 1, 2, 164.19, 7.99, 1, 3, 2, 177.83, -55.69, 0.62494, 3, 19.35, 92.12, 0.33188, 4, -132.95, -62.02, 0.04318, 3, 2, 220.19, -112.83, 0.20981, 3, 90.46, 90.64, 0.54669, 4, -75.28, -103.64, 0.2435, 1, 2, 114.38, 6.48, 1, 3, 2, 124.84, -66.67, 0.37306, 3, -4.4, 43.49, 0.62276, 4, -180.13, -88.55, 0.00418, 3, 2, 153.26, -141.69, 0.02859, 3, 72.32, 20.06, 0.95579, 4, -130.31, -151.42, 0.01561, 1, 2, 70.83, 5.52, 1, 3, 1, 115.74, 78.36, 0.11416, 2, 79.31, -71.25, 0.23274, 3, -28.65, 4.68, 0.65311, 2, 1, 204.35, 88.37, 0.07335, 3, 57.73, -17.48, 0.92665, 3, 1, 30.52, 37.24, 0.01371, 2, 15.19, -1.68, 0.98583, 3, -122.91, -3.44, 0.00046, 3, 1, 104.41, 21.7, 0.82657, 2, 21.81, -76.89, 0.07371, 3, -59.38, -44.24, 0.09972, 2, 1, 185.98, 34.36, 0.67462, 3, 21.36, -61.42, 0.32538, 2, 1, 18.07, 3.03, 0.91652, 2, -21.16, 0.29, 0.08348, 1, 1, 96.7, -9.41, 1, 1, 1, 180.48, -11.24, 1, 1, 2, 227.25, 1.32, 1, 3, 2, 242.47, -49.87, 0.63716, 3, 54.31, 146.8, 0.1383, 4, -73.12, -36.88, 0.22455, 3, 2, 273.68, -103.22, 0.13673, 3, 115.6, 138.83, 0.27434, 4, -27.21, -78.26, 0.58893, 2, 2, 326.94, -7.24, 0.55563, 4, -5.54, 29.33, 0.44437, 3, 2, 320.08, -91.48, 0.01587, 3, 134.72, 182.7, 0.09937, 4, 13.45, -53.02, 0.88476, 2, 3, 144.95, 229.66, 0.01109, 4, 48.55, -20.19, 0.98891], "hull": 28, "edges": [0, 54, 36, 38, 24, 26, 46, 48, 46, 56, 56, 58, 58, 60, 60, 10, 44, 46, 44, 62, 62, 64, 64, 66, 14, 16, 66, 14, 42, 44, 42, 68, 68, 70, 70, 72, 16, 18, 18, 20, 72, 18, 38, 40, 40, 42, 40, 74, 74, 76, 76, 78, 78, 20, 38, 80, 80, 82, 82, 84, 20, 22, 22, 24, 84, 22, 26, 28, 32, 34, 34, 36, 28, 30, 30, 32, 48, 86, 86, 88, 88, 90, 90, 6, 10, 12, 12, 14, 6, 8, 8, 10, 48, 50, 50, 92, 92, 94, 4, 6, 94, 4, 50, 52, 52, 54, 52, 96, 0, 2, 2, 4, 96, 2], "width": 306, "height": 471}}, "t2": {"t2": {"type": "mesh", "uvs": [0.92827, 0.01069, 0.98912, 0.07115, 0.99847, 0.1136, 0.97567, 0.15714, 0.9479, 0.21019, 0.84827, 0.23991, 0.82322, 0.26546, 0.81246, 0.29902, 0.80391, 0.32569, 0.80066, 0.34829, 0.79665, 0.38254, 0.79098, 0.43104, 0.78387, 0.49177, 0.7761, 0.5582, 0.77142, 0.59823, 0.76738, 0.63273, 0.75535, 0.65757, 0.72226, 0.71203, 0.69551, 0.75606, 0.65809, 0.80205, 0.61768, 0.85172, 0.57473, 0.87236, 0.53493, 0.89149, 0.50989, 0.90352, 0.48256, 0.8958, 0.42171, 0.90018, 0.36606, 0.90419, 0.32723, 0.90699, 0.37376, 0.934, 0.3977, 0.9479, 0.3481, 0.97464, 0.30811, 0.9962, 0.28953, 1, 0.2289, 0.98714, 0.16774, 0.97418, 0.09048, 0.9578, 0.06638, 0.90838, 0.06745, 0.86789, 8e-05, 0.8433, 0.00596, 0.80234, 0.07073, 0.77647, 0.12698, 0.76084, 0.20243, 0.73987, 0.26385, 0.72281, 0.34666, 0.72749, 0.41512, 0.73137, 0.47436, 0.73472, 0.50298, 0.732, 0.54426, 0.71134, 0.59897, 0.68397, 0.64524, 0.66082, 0.65538, 0.64635, 0.67362, 0.58532, 0.69019, 0.52984, 0.70294, 0.48719, 0.7025, 0.48336, 0.7099, 0.44238, 0.71848, 0.39483, 0.72663, 0.34966, 0.73105, 0.32514, 0.73678, 0.2934, 0.74325, 0.25754, 0.75285, 0.23107, 0.74408, 0.16929, 0.75776, 0.11061, 0.77089, 0.05431, 0.81163, 0.0005, 0.67148, 0.68881, 0.59474, 0.73997, 0.49402, 0.80872, 0.35253, 0.8295, 0.24221, 0.84389, 0.2542, 0.93182, 0.13669, 0.80073], "triangles": [65, 66, 0, 1, 65, 0, 31, 32, 72, 30, 31, 72, 72, 32, 33, 33, 34, 72, 72, 27, 30, 30, 28, 29, 30, 27, 28, 72, 34, 36, 34, 35, 36, 72, 36, 71, 72, 71, 27, 26, 27, 70, 27, 71, 70, 71, 36, 37, 71, 37, 73, 38, 39, 37, 37, 40, 73, 37, 39, 40, 73, 42, 71, 71, 42, 43, 40, 41, 73, 73, 41, 42, 26, 70, 25, 70, 43, 44, 70, 71, 43, 70, 44, 45, 23, 24, 22, 24, 25, 69, 24, 69, 22, 69, 25, 70, 22, 69, 21, 21, 69, 20, 20, 69, 19, 68, 69, 47, 69, 45, 46, 69, 70, 45, 69, 46, 47, 68, 47, 48, 19, 69, 68, 19, 68, 18, 68, 67, 18, 68, 49, 67, 68, 48, 49, 18, 67, 17, 17, 67, 16, 49, 50, 67, 50, 51, 67, 67, 51, 16, 16, 51, 15, 51, 52, 15, 15, 52, 14, 14, 52, 13, 52, 53, 13, 13, 53, 12, 12, 53, 54, 12, 55, 56, 12, 56, 11, 12, 54, 55, 56, 57, 11, 11, 57, 10, 57, 58, 10, 10, 58, 9, 58, 59, 9, 9, 59, 8, 59, 60, 8, 8, 60, 7, 7, 60, 61, 6, 7, 61, 61, 62, 6, 6, 62, 5, 4, 5, 63, 5, 62, 63, 4, 63, 3, 3, 63, 64, 2, 3, 64, 64, 1, 2, 1, 64, 65], "vertices": [1, 8, -45.2, 140.32, 1, 1, 8, -78.67, 151.8, 1, 2, 18, 32.43, 49.53, 0.89527, 19, -26.54, 47.66, 0.10473, 2, 18, 54.29, 44.53, 0.61555, 19, -4.37, 44.32, 0.38445, 2, 18, 80.9, 38.43, 0.18311, 19, 22.63, 40.26, 0.81689, 1, 19, 42.81, 11.22, 1, 1, 19, 56.52, 5.55, 1, 1, 20, 7.34, 5.14, 1, 1, 20, 20.59, 4.8, 1, 1, 20, 31.58, 5.79, 1, 1, 20, 48.19, 7.58, 1, 2, 20, 71.69, 10.12, 0.00174, 21, 12.42, 9.67, 0.99826, 2, 21, 41.95, 11.77, 0.85221, 22, -6.65, 12.24, 0.14779, 1, 22, 25.74, 12.41, 1, 1, 22, 45.25, 12.51, 1, 2, 22, 62.07, 12.6, 0.40942, 23, -0.81, 12.99, 0.59058, 2, 22, 74.42, 9.72, 0.01705, 23, 11.82, 14.08, 0.98295, 2, 23, 40.38, 14.47, 0.9758, 24, -16.75, 5.32, 0.0242, 3, 23, 63.46, 14.79, 0.1369, 24, 1.3, 19.73, 0.86187, 25, -46.26, 12.01, 0.00123, 2, 24, 22.82, 33.26, 0.83128, 25, -27.36, 29.02, 0.16872, 5, 24, 46.07, 47.88, 0.39687, 25, -6.94, 47.39, 0.5861, 26, -51.11, 43.05, 0.0166, 27, -30.9, 95.05, 0.00039, 28, 31.74, 112.85, 5e-05, 5, 24, 63.12, 49.8, 0.19711, 25, 9.52, 52.19, 0.71931, 26, -35.13, 49.28, 0.07415, 27, -15.38, 87.76, 0.00654, 28, 35.69, 96.16, 0.00289, 5, 24, 78.91, 51.59, 0.0737, 25, 24.79, 56.64, 0.71878, 26, -20.32, 55.06, 0.17164, 27, -0.99, 81, 0.02364, 28, 39.35, 80.69, 0.01226, 5, 24, 88.85, 52.71, 0.04241, 25, 34.39, 59.44, 0.68301, 26, -11, 58.7, 0.21956, 27, 8.06, 76.75, 0.03561, 28, 41.65, 70.96, 0.01941, 5, 24, 94.73, 45.09, 0.02322, 25, 41.48, 52.94, 0.6061, 26, -3.36, 52.85, 0.27977, 27, 9.08, 67.18, 0.05744, 28, 34.79, 64.21, 0.03347, 5, 24, 112.96, 37.29, 0.00042, 25, 60.77, 48.36, 0.29931, 26, 16.26, 49.99, 0.40895, 27, 20.52, 50.98, 0.16953, 28, 29.23, 45.18, 0.12179, 4, 25, 78.42, 44.17, 0.09278, 26, 34.2, 47.37, 0.2955, 27, 30.98, 36.17, 0.27287, 28, 24.13, 27.77, 0.33886, 4, 25, 90.73, 41.24, 0.01543, 26, 46.73, 45.54, 0.07876, 27, 38.28, 25.83, 0.15771, 28, 20.58, 15.62, 0.7481, 4, 25, 80.91, 58.65, 0.00023, 26, 35.41, 62.02, 0.00252, 27, 42.43, 45.38, 0.00445, 28, 38.47, 24.54, 0.99279, 1, 28, 47.68, 29.12, 1, 1, 28, 53.54, 9.3, 1, 1, 28, 58.27, -6.68, 1, 1, 28, 57.67, -12.95, 1, 2, 27, 87.8, 16.9, 0.00154, 28, 44.39, -28.71, 0.99846, 2, 27, 91.92, -3.48, 0.05781, 28, 30.99, -44.61, 0.94219, 2, 27, 97.12, -29.23, 0.19365, 28, 14.06, -64.7, 0.80635, 2, 27, 79.91, -47.72, 0.39268, 28, -11.11, -62.73, 0.60732, 2, 27, 62.54, -56.97, 0.66876, 28, -29.16, -54.88, 0.33124, 2, 27, 62.7, -81.86, 0.82721, 28, -48.55, -70.49, 0.17279, 3, 26, 134.81, -29.71, 4e-05, 27, 44.37, -89.86, 0.85573, 28, -66.22, -61.11, 0.14423, 3, 26, 111.34, -36.64, 0.01215, 27, 23.19, -77.62, 0.88866, 28, -69.81, -36.91, 0.09919, 3, 26, 91.8, -39.44, 0.06615, 27, 7.7, -65.38, 0.89002, 28, -69.86, -17.17, 0.04383, 3, 26, 65.58, -43.2, 0.29846, 27, -13.08, -48.96, 0.70002, 28, -69.93, 9.31, 0.00152, 3, 25, 80.15, -49.99, 0.00036, 26, 44.24, -46.26, 0.59368, 27, -30, -35.59, 0.40595, 3, 25, 55.62, -38.87, 0.0824, 26, 18.83, -37.35, 0.84724, 27, -41.04, -11.03, 0.07036, 2, 25, 35.35, -29.68, 0.46616, 26, -2.17, -29.98, 0.53384, 3, 24, 58.67, -24.44, 0.05674, 25, 17.81, -21.72, 0.85263, 26, -20.35, -23.61, 0.09062, 3, 24, 49.93, -21.05, 0.28434, 25, 8.62, -19.87, 0.70392, 26, -29.67, -22.58, 0.01174, 3, 23, 62.45, -38.81, 0.02218, 24, 33.36, -23.24, 0.83916, 25, -7.34, -24.85, 0.13866, 3, 23, 43.31, -27.63, 0.36318, 24, 11.39, -26.14, 0.63673, 25, -28.49, -31.46, 9e-05, 3, 22, 78.96, -25.7, 0.00573, 23, 27.13, -18.18, 0.89088, 24, -7.19, -28.6, 0.10339, 3, 22, 71.68, -23.01, 0.04967, 23, 19.37, -17.89, 0.92565, 24, -13.51, -33.12, 0.02468, 2, 22, 41.63, -19.59, 0.91956, 23, -10.26, -23.95, 0.08044, 2, 21, 64.75, -15.5, 0.01821, 22, 14.31, -16.47, 0.98179, 2, 21, 43.64, -14.49, 0.79669, 22, -6.69, -14.08, 0.20331, 2, 21, 41.82, -14.9, 0.86252, 22, -8.53, -14.37, 0.13748, 1, 21, 21.77, -15.49, 1, 2, 20, 58.72, -16.21, 0.59499, 21, -1.5, -16.17, 0.40501, 1, 20, 36.66, -17.66, 1, 2, 19, 90.56, -18.42, 0.00754, 20, 24.68, -18.45, 0.99246, 2, 19, 75.06, -19.46, 0.19867, 20, 9.18, -19.47, 0.80133, 2, 19, 57.54, -20.63, 0.80929, 20, -8.34, -20.62, 0.19071, 2, 19, 44.32, -19.96, 0.99059, 20, -21.55, -19.93, 0.00941, 2, 18, 68.44, -29.4, 0.15157, 19, 15.34, -28.32, 0.84843, 2, 18, 39.6, -28.14, 0.86207, 19, -13.5, -29.25, 0.13793, 1, 18, 11.94, -26.94, 1, 1, 8, -12.11, 159.23, 1, 4, 22, 91.81, -16.1, 0.00198, 23, 36.36, -5.07, 0.82656, 24, -7.94, -12.57, 0.17144, 25, -49.85, -21.39, 2e-05, 3, 23, 68.93, -18.34, 0.03439, 24, 25.92, -3.09, 0.84074, 25, -18.1, -6.27, 0.12487, 5, 24, 70.75, 10.03, 0.07011, 25, 23.83, 14.3, 0.75254, 26, -17.53, 12.8, 0.1472, 27, -29.71, 49.87, 0.01915, 28, -2.89, 83.82, 0.011, 4, 25, 70.41, 8.49, 0.07783, 26, 29.38, 11.13, 0.50036, 27, 1.38, 14.71, 0.17113, 28, -11.09, 37.6, 0.25068, 4, 25, 106.43, 3.13, 0.04763, 26, 65.73, 8.97, 0.30656, 27, 24.85, -13.14, 0.36522, 28, -18.29, 1.9, 0.28058, 4, 25, 117.06, 44.7, 0.01079, 26, 72.65, 51.32, 0.05887, 27, 60.32, 11.01, 0.1276, 28, 22.68, -10.85, 0.80274, 4, 25, 131.64, -28.08, 0.00941, 26, 93.6, -19.89, 0.09914, 27, 23.12, -53.22, 0.73781, 28, -50.75, -21.67, 0.15364], "hull": 67, "edges": [0, 132, 0, 2, 2, 4, 8, 10, 10, 12, 16, 18, 30, 32, 46, 48, 62, 64, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 92, 94, 100, 102, 108, 110, 122, 124, 124, 126, 130, 132, 4, 6, 6, 8, 126, 128, 128, 130, 120, 122, 12, 14, 14, 16, 118, 120, 116, 118, 18, 20, 114, 116, 20, 22, 110, 112, 112, 114, 22, 24, 106, 108, 24, 26, 102, 104, 104, 106, 26, 28, 28, 30, 32, 34, 34, 36, 98, 100, 94, 96, 96, 98, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 90, 92, 86, 88, 88, 90, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 64, 66, 66, 68, 68, 70, 80, 82, 82, 84, 84, 86], "width": 324, "height": 486}}, "t6": {"t6": {"type": "mesh", "uvs": [0.1523, 0.00559, 0.30455, 0.07772, 0.33759, 0.13739, 0.363, 0.18329, 0.39767, 0.24592, 0.40868, 0.26321, 0.47187, 0.28112, 0.57374, 0.30999, 0.62461, 0.35541, 0.68329, 0.4078, 0.74602, 0.46381, 0.79817, 0.51975, 0.85094, 0.57635, 0.77881, 0.63328, 0.72947, 0.67222, 0.75073, 0.7222, 0.76917, 0.76554, 0.83072, 0.79111, 0.90561, 0.82222, 0.98776, 0.85634, 0.98866, 0.90099, 0.98939, 0.93673, 0.93878, 0.96415, 0.88379, 0.99393, 0.85181, 0.99737, 0.82812, 0.96742, 0.7996, 0.93135, 0.76003, 0.88132, 0.71956, 0.83014, 0.67267, 0.77085, 0.63679, 0.72548, 0.62049, 0.66823, 0.60687, 0.62041, 0.59672, 0.58475, 0.58693, 0.5317, 0.57928, 0.49027, 0.55124, 0.43784, 0.52471, 0.38825, 0.47512, 0.36452, 0.40126, 0.32918, 0.28715, 0.31093, 0.19372, 0.29598, 0.14043, 0.28745, 0.09163, 0.2386, 0.04266, 0.1896, 0.00147, 0.14837, 0.00647, 0.09152, 0.01164, 0.03264, 0.01124, 0.0059], "triangles": [47, 48, 0, 46, 0, 1, 24, 25, 23, 23, 25, 22, 25, 26, 22, 21, 22, 20, 22, 26, 20, 27, 20, 26, 19, 20, 18, 18, 20, 27, 18, 28, 17, 18, 27, 28, 28, 16, 17, 28, 29, 16, 29, 15, 16, 29, 30, 15, 30, 14, 15, 30, 31, 14, 13, 14, 32, 14, 31, 32, 12, 13, 33, 13, 32, 33, 33, 34, 11, 33, 11, 12, 34, 10, 11, 34, 35, 10, 35, 9, 10, 35, 36, 9, 36, 37, 9, 37, 8, 9, 37, 38, 8, 39, 6, 38, 38, 7, 8, 38, 6, 7, 39, 5, 6, 39, 40, 5, 5, 40, 4, 40, 41, 4, 4, 41, 3, 43, 3, 41, 41, 42, 43, 3, 43, 2, 2, 44, 45, 1, 45, 46, 43, 44, 2, 2, 45, 1, 0, 46, 47], "vertices": [1, 8, -46.54, 145.18, 1, 1, 8, -81.81, 148.83, 1, 2, 29, 43.06, 32.95, 0.70869, 30, 10.33, 39.18, 0.29131, 2, 29, 58.79, 35.96, 0.34872, 30, 22.69, 28.98, 0.65128, 3, 29, 80.26, 40.07, 0.02821, 30, 39.54, 15.08, 0.63912, 31, 0.29, 15.08, 0.33267, 3, 29, 86.2, 41.45, 0.00531, 30, 44.4, 11.38, 0.26734, 31, 5.17, 11.4, 0.72736, 1, 31, 17.41, 12.67, 1, 2, 31, 37.16, 14.7, 0.99652, 32, -16.36, 5.42, 0.00348, 2, 31, 53.01, 7.2, 0.17564, 32, 0.59, 9.92, 0.82436, 1, 32, 20.15, 15.11, 1, 1, 32, 41.06, 20.66, 1, 2, 32, 61.49, 24.47, 0.97624, 33, 0.19, 26.04, 0.02376, 2, 32, 82.16, 28.33, 0.65926, 33, 19.88, 33.43, 0.34074, 3, 32, 97.56, 11.62, 0.17107, 33, 37.94, 19.64, 0.82295, 34, -8.44, 22.14, 0.00599, 3, 32, 108.09, 0.2, 0.00975, 33, 50.29, 10.21, 0.59157, 34, 1.44, 10.15, 0.39868, 1, 34, 18.54, 8.44, 1, 2, 34, 33.36, 6.96, 0.9347, 35, -4.9, 11.13, 0.0653, 2, 34, 44.74, 14.29, 0.14703, 35, 8.64, 11.46, 0.85297, 1, 35, 25.11, 11.88, 1, 2, 35, 43.17, 12.33, 0.62309, 36, -4.82, 11.62, 0.37691, 2, 35, 53.09, 1.07, 0.00481, 36, 9.71, 7.87, 0.99519, 1, 36, 21.34, 4.87, 1, 2, 35, 60.54, -20.53, 0.0008, 36, 27.99, -5.83, 0.9992, 1, 36, 35.23, -17.46, 1, 2, 35, 56.65, -38.64, 0.0005, 36, 34.93, -23.01, 0.9995, 2, 35, 47.02, -33.66, 0.06585, 36, 24.16, -24.28, 0.93415, 2, 35, 35.43, -27.66, 0.34129, 36, 11.2, -25.81, 0.65871, 2, 35, 19.35, -19.33, 0.86802, 36, -6.78, -27.94, 0.13198, 3, 34, 51.43, -7.72, 0.08946, 35, 2.91, -10.81, 0.91021, 36, -25.17, -30.11, 0.00033, 1, 34, 30.03, -9.2, 1, 1, 34, 13.65, -10.34, 1, 2, 33, 47.44, -8.15, 0.85032, 34, -5.51, -7.08, 0.14968, 1, 33, 31.24, -9.14, 1, 1, 33, 19.16, -9.88, 1, 1, 33, 1.25, -10.08, 1, 2, 32, 42.48, -9.01, 0.7911, 33, -12.73, -10.23, 0.2089, 1, 32, 24.23, -9.16, 1, 2, 31, 45.47, -11.6, 0.30208, 32, 6.97, -9.3, 0.69792, 2, 31, 33.99, -9.93, 0.94266, 32, -2.88, -15.44, 0.05734, 1, 31, 16.89, -7.45, 1, 2, 30, 36.59, -13.58, 0.69265, 31, -2.5, -13.6, 0.30735, 2, 30, 20.68, -18.53, 0.99937, 31, -18.38, -18.63, 0.00063, 1, 30, 11.61, -21.35, 1, 2, 29, 73.44, -11.57, 0.4189, 30, -4.57, -12.62, 0.5811, 2, 29, 56.33, -18.48, 0.99479, 30, -20.81, -3.85, 0.00521, 1, 29, 41.94, -24.3, 1, 1, 29, 22.98, -21.85, 1, 1, 29, 3.34, -19.31, 1, 1, 8, -27.51, 159.76, 1], "hull": 49, "edges": [0, 96, 0, 2, 8, 10, 46, 48, 94, 96, 88, 90, 2, 4, 4, 6, 6, 8, 84, 86, 86, 88, 82, 84, 78, 80, 80, 82, 10, 12, 12, 14, 74, 76, 76, 78, 14, 16, 70, 72, 72, 74, 16, 18, 18, 20, 66, 68, 68, 70, 20, 22, 22, 24, 64, 66, 60, 62, 62, 64, 24, 26, 26, 28, 28, 30, 30, 32, 58, 60, 56, 58, 32, 34, 34, 36, 36, 38, 54, 56, 52, 54, 38, 40, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52, 90, 92, 92, 94], "width": 170, "height": 336}}, "sh10": {"sh3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114.03, -48.4, -145.97, -48.4, -145.97, 57.6, 114.03, 57.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 106}}, "sh11": {"sh4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [93.08, -45.25, -140.92, -45.25, -140.92, 64.75, 93.08, 64.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 234, "height": 110}}, "sh12": {"sh4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [93.08, -45.25, -140.92, -45.25, -140.92, 64.75, 93.08, 64.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 234, "height": 110}}, "zs": {"zs": {"type": "mesh", "uvs": [0.44243, 0.03762, 0.63891, 0.08779, 0.62473, 0.17696, 0.6106, 0.26578, 0.66238, 0.2841, 0.77382, 0.34623, 0.86817, 0.39882, 0.99221, 0.46797, 0.99219, 0.5282, 0.99217, 0.62258, 0.99215, 0.73975, 0.99213, 0.83226, 0.9679, 0.91311, 0.943, 0.99622, 0.58471, 0.9836, 0.55278, 0.95262, 0.5695, 0.82302, 0.58565, 0.69773, 0.59794, 0.6025, 0.60846, 0.52088, 0.62957, 0.47875, 0.54152, 0.4256, 0.45109, 0.37102, 0.38694, 0.29568, 0.33795, 0.23815, 0.2758, 0.16516, 0.22466, 0.11486, 0.13299, 0.07256, 0.05279, 0.03555, 0, 0.01565, 0, 0.00519, 0.31516, 0.00512], "triangles": [31, 28, 30, 28, 29, 30, 23, 24, 3, 24, 25, 2, 27, 28, 31, 0, 26, 27, 1, 25, 26, 16, 17, 10, 11, 16, 10, 12, 16, 11, 15, 16, 12, 14, 15, 12, 13, 14, 12, 9, 18, 8, 17, 18, 9, 10, 17, 9, 20, 21, 6, 20, 6, 7, 8, 20, 7, 19, 20, 8, 18, 19, 8, 4, 22, 23, 22, 4, 5, 21, 22, 5, 21, 5, 6, 3, 24, 2, 4, 23, 3, 2, 25, 1, 0, 27, 31, 1, 26, 0], "vertices": [1, 53, 18.18, 22.77, 1, 1, 53, 48.82, 19.47, 1, 2, 53, 65.44, -10.21, 0.23688, 54, 10.89, 16.19, 0.76312, 2, 54, 44.52, 12.05, 0.85062, 55, -5.12, 15.24, 0.14938, 2, 54, 51.94, 17.91, 0.38506, 55, 3.99, 17.75, 0.61494, 2, 55, 31.31, 19.23, 0.99423, 56, -23.08, 14.02, 0.00577, 2, 55, 54.43, 20.49, 0.39106, 56, -0.97, 20.89, 0.60894, 1, 56, 28.11, 29.91, 1, 2, 56, 50.48, 24.81, 0.96043, 57, -16.9, 22.45, 0.03957, 2, 56, 85.54, 16.82, 0.01744, 57, 18.97, 20.02, 0.98256, 2, 57, 63.51, 16.99, 0.02178, 58, 15.84, 18.11, 0.97822, 1, 58, 51.09, 18.1, 1, 1, 58, 81.89, 15.12, 1, 1, 58, 113.56, 12.06, 1, 1, 58, 108.75, -32.01, 1, 1, 58, 96.95, -35.94, 1, 1, 58, 47.57, -33.88, 1, 2, 57, 44.16, -31.81, 0.52804, 58, -0.17, -31.89, 0.47196, 3, 56, 67.31, -28.76, 0.2671, 57, 8.06, -27.85, 0.7312, 58, -36.45, -30.38, 0.00171, 3, 56, 37.28, -20.59, 0.91306, 57, -22.88, -24.45, 0.01494, 2, 184.18, -190.18, 0.072, 3, 55, 68.26, -19.48, 0.00485, 56, 22.2, -14.49, 0.85915, 2, 200.39, -191.36, 0.136, 3, 55, 45.3, -19.95, 0.46505, 56, 0.05, -20.56, 0.24695, 2, 219.62, -178.79, 0.288, 2, 55, 21.72, -20.43, 0.56, 2, 239.36, -165.89, 0.44, 3, 54, 53.93, -16.2, 0.26174, 55, -7.44, -14.45, 0.09826, 2, 267.26, -155.51, 0.64, 1, 2, 288.56, -147.59, 1, 1, 2, 315.6, -137.54, 1, 1, 2, 334.14, -129.59, 1, 1, 2, 349.2, -116.94, 1, 2, 2, 362.38, -105.88, 0.528, 4, 58.13, -53.93, 0.472, 1, 4, 62.62, -45.01, 1, 1, 4, 66.3, -43.47, 1, 1, 53, -1.66, 24.91, 1], "hull": 32, "edges": [6, 8, 26, 28, 28, 30, 38, 40, 50, 52, 56, 58, 58, 60, 60, 62, 14, 16, 36, 38, 16, 18, 34, 36, 18, 20, 20, 22, 30, 32, 32, 34, 22, 24, 24, 26, 8, 10, 10, 12, 12, 14, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 2, 4, 4, 6, 2, 0, 0, 62, 52, 54, 54, 56], "width": 123, "height": 381}}, "sh1": {"sh1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76.52, -57.35, -123.48, -57.35, -123.48, 44.65, 76.52, 44.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 102}}, "sh2": {"sh2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [86.35, -53.95, -82.65, -53.95, -82.65, 51.05, 86.35, 51.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 169, "height": 105}}, "sh3": {"sh3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114.03, -48.4, -145.97, -48.4, -145.97, 57.6, 114.03, 57.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 106}}, "sh4": {"sh4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [93.08, -45.25, -140.92, -45.25, -140.92, 64.75, 93.08, 64.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 234, "height": 110}}}}], "animations": {"animation": {"slots": {"sh10": {"color": [{"color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "color": "ffffffff"}, {"time": 5.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "color": "ffffff7f"}]}, "sh8": {"color": [{"color": "ffffffa5"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff3f"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffa5"}, {"time": 3.9, "color": "ffffffff"}, {"time": 5.1667, "color": "ffffff3f"}, {"time": 5.5667, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffffa5"}]}, "sh12": {"color": [{"color": "ffffffb8"}, {"time": 0.4667, "color": "ffffffff", "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1.9, "color": "ffffff10"}, {"time": 2.1, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffb8"}, {"time": 3.8, "color": "ffffffff", "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.2333, "color": "ffffff10"}, {"time": 5.4333, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffffb8"}]}, "sh11": {"color": [{"color": "ffffffc0", "curve": 0.331, "c2": 0.33, "c3": 0.716, "c4": 0.83}, {"time": 0.9667, "color": "ffffff10"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 2.8333, "color": "ffffffff", "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "color": "ffffffc0", "curve": 0.331, "c2": 0.33, "c3": 0.716, "c4": 0.83}, {"time": 4.3, "color": "ffffff10"}, {"time": 4.5, "color": "ffffff00"}, {"time": 6.1667, "color": "ffffffff", "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "color": "ffffffc0"}]}, "sh2": {"color": [{"color": "ffffff3f"}, {"time": 0.4, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff3f"}, {"time": 3.7333, "color": "ffffff00"}, {"time": 5.4, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff3f"}]}, "sh7": {"color": [{"color": "ffffffcc"}, {"time": 0.9, "color": "ffffff3f"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 3, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffcc"}, {"time": 4.2333, "color": "ffffff3f"}, {"time": 4.6667, "color": "ffffff00"}, {"time": 6.3333, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffffcc"}]}, "sh3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "color": "ffffff00"}]}, "sh1": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 5, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff00"}]}, "sh4": {"color": [{"color": "ffffff10", "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffffff", "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3333, "color": "ffffff10", "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5333, "color": "ffffff00"}, {"time": 5.2, "color": "ffffffff", "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "color": "ffffff10"}]}, "sh9": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "color": "ffffffff"}]}, "sh5": {"color": [{"color": "ffffff9f"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff9f"}, {"time": 4.3667, "color": "ffffff00"}, {"time": 6.0333, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff9f"}]}, "sh6": {"color": [{"color": "ffffffbf"}, {"time": 0.4, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffbf"}, {"time": 3.7333, "color": "ffffffff"}, {"time": 5.4, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffffbf"}]}}, "bones": {"st": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.82, "y": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.82, "y": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "st2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys": {"rotate": [{"angle": 0.23, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 2.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3333, "angle": 0.23, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 2.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": 0.23}], "translate": [{"x": -0.28, "y": 0.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -7.86, "y": 10.76, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3333, "x": -0.28, "y": 0.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "x": -7.86, "y": 10.76, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "x": -0.28, "y": 0.22}]}, "ys3": {"rotate": [{"angle": 0.41, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2333, "angle": -10.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.9, "angle": 3.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "angle": 0.41, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5667, "angle": -10.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 6.2333, "angle": 3.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 0.41}], "scale": [{"x": 0.996, "y": 0.996, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2333, "x": 0.98, "y": 0.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "x": 0.996, "y": 0.996, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5667, "x": 0.98, "y": 0.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 6.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "x": 0.996, "y": 0.996}]}, "st4": {"rotate": [{"angle": 0.05, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 7.2, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.2667, "angle": 0.2, "curve": 0.344, "c2": 0.44, "c3": 0.677, "c4": 0.77}, {"time": 3.3333, "angle": 0.05, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 7.2, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 6.6, "angle": 0.2, "curve": 0.344, "c2": 0.44, "c3": 0.677, "c4": 0.77}, {"time": 6.6667, "angle": 0.05}]}, "t1": {"rotate": [{"angle": 0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": 0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": 0.05}], "translate": [{"x": -0.08, "y": -0.03, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.88, "y": -0.69, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": -0.08, "y": -0.03, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -1.88, "y": -0.69, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.08, "y": -0.03}], "shear": [{"x": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -2.4, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -2.4, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.05}]}, "ys7": {"rotate": [{"angle": -5.73, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -14.92, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 3.3333, "angle": -5.73, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 4.2333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": -14.92, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 6.6667, "angle": -5.73}]}, "ys8": {"rotate": [{"angle": -7.04, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -12.52, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": -7.04, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "angle": -12.52, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 6.6667, "angle": -7.04}]}, "ys4": {"rotate": [{"angle": -4.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -6, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.5667, "angle": -1.83, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.1667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -4.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -6, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4.9, "angle": -1.83, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 5.5, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -4.55}]}, "ys2": {"rotate": [{"angle": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3333, "angle": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": -0.44}]}, "st3": {"translate": [{"x": 0.66, "y": -0.34, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 6.4, "y": -3.32, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 3.3333, "x": 0.66, "y": -0.34, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": 6.4, "y": -3.32, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 6.6667, "x": 0.66, "y": -0.34}]}, "t26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t29": {"rotate": [{"angle": -0.15, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.3667, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 5.05, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "angle": -0.15, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 3.7, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 5.05, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 6.6667, "angle": -0.15}]}, "t30": {"rotate": [{"angle": 1.15, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.6, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 5.95, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 3.3333, "angle": 1.15, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 3.9333, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 5.95, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 6.6667, "angle": 1.15}]}, "t31": {"rotate": [{"angle": 2.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 6.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 2.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 6.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 2.81}]}, "t32": {"rotate": [{"angle": 5.48, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 1.0667, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 8.53, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 3.3333, "angle": 5.48, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 4.4, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "angle": 8.53, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 6.6667, "angle": 5.48}]}, "t33": {"rotate": [{"angle": 5.22, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 1.3, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 6.35, "curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 3.3333, "angle": 5.22, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 4.6333, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": 6.35, "curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 6.6667, "angle": 5.22}]}, "t15": {"rotate": [{"angle": -1.58, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 4.8, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3333, "angle": -1.58, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9667, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 4.8, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": -1.58}]}, "t4": {"translate": [{"x": 0.09, "y": -2.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "x": 0.02, "y": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.8, "y": 2.81, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 0.09, "y": -2.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "x": 0.02, "y": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.8, "y": 2.81, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 0.09, "y": -2.41}], "scale": [{"x": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 0.998}]}, "t3": {"rotate": [{"angle": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.05}], "translate": [{"y": -2.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "x": 0.02, "y": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -0.37, "y": 2.94, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "y": -2.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "x": 0.02, "y": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -0.37, "y": 2.94, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "y": -2.4}], "scale": [{"x": 1.002, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.04, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 1.002, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.04, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 1.002}]}, "t6": {"rotate": [{"angle": -0.11, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -2.49, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": -0.11, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -2.49, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.11}], "translate": [{"x": -0.08, "y": 0.07, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.8, "y": 1.63, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": -0.08, "y": 0.07, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -1.8, "y": 1.63, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.08, "y": 0.07}], "scale": [{"x": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.96, "y": 0.99, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.96, "y": 0.99, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 0.998}]}, "t8": {"shear": [{"x": 0.05, "y": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.2, "y": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 0.05, "y": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.2, "y": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 0.05, "y": -0.05}]}, "zs": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 6.86, "y": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 6.86, "y": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys11": {"rotate": [{"angle": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": -0.72}]}, "ys12": {"rotate": [{"angle": -0.84, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 0.49, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": -0.84, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 0.49, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.84}]}, "ys13": {"rotate": [{"angle": -0.56, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 0.49, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -0.56, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 0.49, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.56}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys14": {"rotate": [{"angle": -0.39, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": -0.39, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 0.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "angle": -0.39}]}, "ys15": {"rotate": [{"angle": -0.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 0.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -0.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 0.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -0.21}]}, "ys16": {"rotate": [{"angle": -0.02, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "angle": -0.02, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.3333, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 0.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -0.02}], "scale": [{"x": 0.96, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3333, "x": 0.96, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "x": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "x": 0.96}]}, "ys17": {"rotate": [{"angle": 0.15, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 0.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 0.15, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 0.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 0.15}]}, "zs3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "sh4": {"translate": [{"x": 68.12, "y": -68.66}, {"time": 0.1667, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 0.2}, {"time": 3.3333, "x": 68.12, "y": -68.66}, {"time": 3.5, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 3.5333}, {"time": 6.6667, "x": 68.12, "y": -68.66}], "scale": [{"x": 1.57, "y": 1.342}, {"time": 0.1667, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 0.2}, {"time": 3.3333, "x": 1.57, "y": 1.342}, {"time": 3.5, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 3.5333}, {"time": 6.6667, "x": 1.57, "y": 1.342}]}, "sh3": {"translate": [{}, {"time": 3.2667, "y": -55.87, "curve": "stepped"}, {"time": 3.3333}, {"time": 6.6, "y": -55.87, "curve": "stepped"}, {"time": 6.6667}], "scale": [{}, {"time": 3.2667, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 3.3333}, {"time": 6.6, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 6.6667}]}, "bone": {"translate": [{}, {"time": 1.6667, "x": 0.59, "y": -13.99}, {"time": 3.3333}, {"time": 5, "x": 0.59, "y": -13.99}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "sh2": {"translate": [{"x": 42.94, "y": 35.78}, {"time": 0.3667, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 0.4}, {"time": 3.3333, "x": 42.94, "y": 35.78}, {"time": 3.7, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 3.7333}, {"time": 6.6667, "x": 42.94, "y": 35.78}], "scale": [{"x": 0.947, "y": 1.425}, {"time": 0.3667, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 0.4}, {"time": 3.3333, "x": 0.947, "y": 1.425}, {"time": 3.7, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 3.7333}, {"time": 6.6667, "x": 0.947, "y": 1.425}]}, "sh1": {"translate": [{}, {"time": 3.2667, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 3.3333}, {"time": 6.6, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 6.6667}], "scale": [{}, {"time": 3.2667, "y": 1.44, "curve": "stepped"}, {"time": 3.3333}, {"time": 6.6, "y": 1.44, "curve": "stepped"}, {"time": 6.6667}]}, "ys5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -13.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys10": {"rotate": [{"angle": -2.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -8.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -2.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -8.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -2.03}]}, "t2": {"rotate": [{"angle": -0.78, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -2.9, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.3333, "angle": -0.78, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 3.4667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": -2.9, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 6.6667, "angle": -0.78}]}, "t14": {"rotate": [{"angle": -0.66, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 0.7, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": -0.66, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 0.7, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.66}]}, "t16": {"rotate": [{"angle": -0.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -0.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": -0.54}]}, "t17": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 4.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 0.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 4.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 0.49}]}, "t18": {"rotate": [{"angle": 0.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": 0.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 3.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "angle": 0.68}]}, "t19": {"rotate": [{"angle": 1.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 3.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 1.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 3.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 1.19}]}, "t20": {"rotate": [{"angle": 1.69, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "angle": 1.69, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.3333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": 1.69}]}, "t21": {"rotate": [{"angle": 2.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 3.1, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 2.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 3.1, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 2.18}]}, "t22": {"rotate": [{"angle": 3.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.3333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 4.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 3.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.6667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 4.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.6667, "angle": 3.65}]}, "t23": {"rotate": [{"angle": 2.94, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 3.1, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 2.94, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 3.1, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 2.94}]}, "t24": {"rotate": [{"angle": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 5.5}]}, "sh5": {"translate": [{"x": -56.23, "y": 16.87}, {"time": 1, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 1.0333}, {"time": 3.3333, "x": -56.23, "y": 16.87}, {"time": 4.3333, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 4.3667}, {"time": 6.6667, "x": -56.23, "y": 16.87}], "scale": [{"y": 1.306}, {"time": 1, "y": 1.44, "curve": "stepped"}, {"time": 1.0333}, {"time": 3.3333, "y": 1.306}, {"time": 4.3333, "y": 1.44, "curve": "stepped"}, {"time": 4.3667}, {"time": 6.6667, "y": 1.306}]}, "sh6": {"translate": [{"x": -30.67, "y": 9.2}, {"time": 2.0333, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 2.0667}, {"time": 3.3333, "x": -30.67, "y": 9.2}, {"time": 5.3667, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 5.4}, {"time": 6.6667, "x": -30.67, "y": 9.2}], "scale": [{"y": 1.167}, {"time": 2.0333, "y": 1.44, "curve": "stepped"}, {"time": 2.0667}, {"time": 3.3333, "y": 1.167}, {"time": 5.3667, "y": 1.44, "curve": "stepped"}, {"time": 5.4}, {"time": 6.6667, "y": 1.167}]}, "sh7": {"translate": [{"x": 29.44, "y": 24.53}, {"time": 0.9, "x": 42.94, "y": 35.78}, {"time": 1.3, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 1.3333}, {"time": 3.3333, "x": 29.44, "y": 24.53}, {"time": 4.2333, "x": 42.94, "y": 35.78}, {"time": 4.6333, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 4.6667}, {"time": 6.6667, "x": 29.44, "y": 24.53}], "scale": [{"x": 0.964, "y": 1.292}, {"time": 0.9, "x": 0.947, "y": 1.425}, {"time": 1.3, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 1.3333}, {"time": 3.3333, "x": 0.964, "y": 1.292}, {"time": 4.2333, "x": 0.947, "y": 1.425}, {"time": 4.6333, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 4.6667}, {"time": 6.6667, "x": 0.964, "y": 1.292}]}, "sh8": {"translate": [{"x": 15.95, "y": 13.29}, {"time": 1.8333, "x": 42.94, "y": 35.78}, {"time": 2.2, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 2.2333}, {"time": 3.3333, "x": 15.95, "y": 13.29}, {"time": 5.1667, "x": 42.94, "y": 35.78}, {"time": 5.5333, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 5.5667}, {"time": 6.6667, "x": 15.95, "y": 13.29}], "scale": [{"x": 0.98, "y": 1.158}, {"time": 1.8333, "x": 0.947, "y": 1.425}, {"time": 2.2, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 2.2333}, {"time": 3.3333, "x": 0.98, "y": 1.158}, {"time": 5.1667, "x": 0.947, "y": 1.425}, {"time": 5.5333, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 5.5667}, {"time": 6.6667, "x": 0.98, "y": 1.158}]}, "sh9": {"translate": [{"y": -28.29}, {"time": 1.6333, "y": -55.87, "curve": "stepped"}, {"time": 1.6667}, {"time": 3.3333, "y": -28.29}, {"time": 4.9667, "y": -55.87, "curve": "stepped"}, {"time": 5}, {"time": 6.6667, "y": -28.29}], "scale": [{"x": 1.304, "y": 1.213}, {"time": 1.6333, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 1.6667}, {"time": 3.3333, "x": 1.304, "y": 1.213}, {"time": 4.9667, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 5}, {"time": 6.6667, "x": 1.304, "y": 1.213}]}, "sh10": {"translate": [{"y": -14.14}, {"time": 0.8333, "y": -28.29}, {"time": 2.4333, "y": -55.87, "curve": "stepped"}, {"time": 2.5}, {"time": 3.3333, "y": -14.14}, {"time": 4.1667, "y": -28.29}, {"time": 5.7667, "y": -55.87, "curve": "stepped"}, {"time": 5.8333}, {"time": 6.6667, "y": -14.14}], "scale": [{"x": 1.152, "y": 1.106}, {"time": 0.8333, "x": 1.304, "y": 1.213}, {"time": 2.4333, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 2.5}, {"time": 3.3333, "x": 1.152, "y": 1.106}, {"time": 4.1667, "x": 1.304, "y": 1.213}, {"time": 5.7667, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 5.8333}, {"time": 6.6667, "x": 1.152, "y": 1.106}]}, "sh11": {"translate": [{"x": 47.23, "y": -47.61}, {"time": 0.9667, "x": 68.12, "y": -68.66}, {"time": 1.1333, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 1.1667}, {"time": 3.3333, "x": 47.23, "y": -47.61}, {"time": 4.3, "x": 68.12, "y": -68.66}, {"time": 4.4667, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 4.5}, {"time": 6.6667, "x": 47.23, "y": -47.61}], "scale": [{"x": 1.395, "y": 1.237}, {"time": 0.9667, "x": 1.57, "y": 1.342}, {"time": 1.1333, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 1.1667}, {"time": 3.3333, "x": 1.395, "y": 1.237}, {"time": 4.3, "x": 1.57, "y": 1.342}, {"time": 4.4667, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 4.5}, {"time": 6.6667, "x": 1.395, "y": 1.237}]}, "sh12": {"translate": [{"x": 26.34, "y": -26.55}, {"time": 1.9, "x": 68.12, "y": -68.66}, {"time": 2.0667, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 2.1}, {"time": 3.3333, "x": 26.34, "y": -26.55}, {"time": 5.2333, "x": 68.12, "y": -68.66}, {"time": 5.4, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 5.4333}, {"time": 6.6667, "x": 26.34, "y": -26.55}], "scale": [{"x": 1.22, "y": 1.132}, {"time": 1.9, "x": 1.57, "y": 1.342}, {"time": 2.0667, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 2.1}, {"time": 3.3333, "x": 1.22, "y": 1.132}, {"time": 5.2333, "x": 1.57, "y": 1.342}, {"time": 5.4, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 5.4333}, {"time": 6.6667, "x": 1.22, "y": 1.132}]}, "ys9": {"rotate": [{"angle": 11, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "angle": 12, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3333, "angle": 11, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.5667, "angle": 12, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": 11}]}}, "deform": {"default": {"t5": {"t5": [{"vertices": [-0.11057, 0.14563, 0.09609, 0.15968, 0.13618, 0.12742, -0.16779, -0.07255, 0.03504, -0.17941, -0.11177, 0.1472, 0.09699, 0.16135, -0.16967, -0.07328, 0.0354, -0.18139, -0.32749, -0.01567, -0.14586, 0.29956, -0.06315, -0.32174, 0.30086, -0.13032, -0.572, 0.05794, -0.1727, 0.55629, -0.19314, -0.5415, 0.48785, -0.30416, -0.96168, -0.04745, -0.429, 0.87957, -0.18411, -0.94514, 0.88409, -0.38133, 0.94343, 0.19172, -1.0322, -0.09455, -0.50223, 0.92723, -0.15528, -1.02487, 0.96813, -0.3702, 1.00605, 0.24891, -0.96168, -0.04744, -0.1841, -0.94515, 0.8841, -0.38132, 0.94344, 0.19172, -0.35378, -1.2223, 1.11866, -0.60623, 1.26442, 0.1411, 0.65312, -0.35577, 0.73923, 0.08072, 0.24722, -0.49916, 0.41528, -0.3709, 0.32725, -0.41665, 0.45883, -0.26452, 0.32725, -0.41665, 0.45883, -0.26452, 0.45883, -0.26452, 0.20977, -0.68588, 0.07256, -0.62972, 0.06464, 0.15804, 0.20081, 0.49096, 0.20081, 0.49096, 0.20312, 0.4967, 0.65096, 0.52242, 0.35518, 0.60631, 0.19735, 0.6293, 0.1966, 0.60685, 0.18332, 0.62288, 0.1662, 0.64352, 0.15096, 0.66191, 0.14247, 0.34827, 0.16317, 0.33866, 0.07103, 0.17363, 0.07062, 0.17262, 0.08076, 0.19742, 0, 0, 0, 0, 0, 0, 0.23256, 0.12354, -0.00241, 0.35328, 0, 0, 0, 0, 0, 0, -0.19719, 0.45695, -0.48854, -0.09519, -0.18856, -0.46076, -0.458, 1.06092, -1.13426, -0.22123, -0.80463, -0.82987, -0.43764, -1.06966, -0.2205, 0.97686, 0.05114, 0.66473, -0.04581, 0.63973, -0.00352, 0.48565, 0.00238, 0.3518, 0, 0, 0, 0, -0.0001, -1e-05, -3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, -0.07731, 0.18883, 0.03329, 0.20134, -0.1197, 0.29231, 0.05171, 0.31162, -0.18033, 0.42659, -0.00392, 0.4632, 0.23997, 0.39617, -0.30574, 0.30888, -0.16474, 0.40226, 0.07117, 0.42884, 0.29267, 0.30183, -0.17562, 0.09932, -0.14189, 0.14336, -0.07646, 0.18671, 0.13584, 0.14009, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10668, 0.07762, 0.08005, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10669, 0.07762, 0.08005, 0.10895, 0.18125, 0.15421, 0.14476, 0.19774, 0.07493, -0.1906, -0.08232, 0.14904, -0.14458, -0.1579, 0.20796, 0.13702, 0.22795, 0.19394, 0.18204, 0.24867, 0.09423, -0.2397, -0.10352, 0.18742, -0.18183, -0.24492, -0.004, -0.1012, 0.22733, -0.03545, 0.24627, -0.05474, -0.2388, 0.24149, 0.04085, -0.08037, 0.10586, 0.06975, 0.11603, 0.09873, 0.09266, -0.12201, -0.05269, 0.09541, -0.09255, -0.09607, 0.12653, 0.08337, 0.13869, 0.11802, 0.11076, -0.14585, -0.06298, -0.092, 0.1212, 0.07997, 0.13287, 0.11332, 0.10602, -0.13963, -0.06036, 0.02915, -0.14928, -0.21852, 0.28781, 0.18973, 0.31553, 0.26868, 0.25189, -0.33169, -0.14331, 0.06923, -0.35461, 0.73769, 0.54651, -0.35387, 0.8471, 0.67955, -0.3843, 0.54941, -0.55481, -0.29934, 0.69321, -0.52575, -0.54221, 0.63326, 0.58221, 0.76834, 0.38765, 0.85756, 0.06531, 0.41331, -0.72851, 0.58067, 0.44099, 0.65615, -0.37106, -0.28902, 0.6694, -0.71565, -0.13964, 0.19669, 0.61354, 0.66881, 0.05417, 0.65849, -0.12995, -0.54852, 0.33788, 0.38667, 0.64332, 0.5473, 0.51376, 0.70183, 0.26596, -0.6765, -0.29217, 0.52897, -0.51309, -0.18415, 0.39356, 0.3051, 0.32438, 0.38198, 0.22904, -0.42616, -0.08458, -0.00828, -0.43439, -0.38807, 0.18453, 0.02301, 0.43397, 0.14036, 0.4113, -0.27207, -0.33263, -0.5734, 0.4286, 0.18382, 0.70151, 0.36799, 0.62496, -0.5534, -0.45413, 0.63141, -0.33736, -0.63524, 0.55674, 0.28231, 0.80884, 0.49198, 0.70141, -0.69258, -0.48351, 0.32464, -0.77973, -0.5275, 0.69475, 0.45771, 0.76153, 0.64786, 0.60817, -0.80079, -0.34584, 0.16707, -0.85613, -0.30028, 0.0649, -0.05715, 0.30564, -0.13485, -0.27606, 0.24092, -0.19056, -0.38357, 0.03865, -0.11533, 0.37335, -0.12935, -0.3632, 0.32723, -0.20378, -0.27884, 0.18064, 0.06269, 0.3304, -0.24213, -0.2275, 0.17062, -0.28504, -0.07953, 0.34272, 0.29866, 0.20743, -0.35172, 0.00479, -0.07974, -0.34257, 0.1302, -0.32701, -0.09849, -0.11661, -0.1513, 0.04682, 0.08975, -0.12363, 0.082, -0.30777, 0.24304, -0.20618, 0.49379, -0.34102, 0.60007, 0.00199, -0.15764, 0.36521, -0.39046, -0.07616, -0.27701, -0.28573, -0.15064, -0.36824, 0.05359, -0.12404, 0.13261, 0.02591, 0.05114, 0.12503, -0.14179, 0.32854, -0.35126, -0.06846, -0.13555, -0.33128, 0.07388, 0.18062, 0.31334, 0.73186, 0.82651, -0.01037, 0.79267, -0.235, 0.64276, -0.51962, 0.27355, -0.77988, -0.63549, 0.47945, -0.60393, -0.51865, -0.19941, -0.77098, 0.10202, -0.78957, 0.24264, 0.57539, 0.65053, -0.00345, 0.62514, -0.18046, 0.50871, -0.40536, 0.21968, -0.6122, -0.50053, 0.3733, -0.4715, -0.40936, -0.15317, -0.60553, 0.08338, -0.61888, 0.28352, 0.2386, 0.05758, 0.347, 0.03323, 0.2002, 0.07784, -0.18022, 0.19267, 0.0376, 0.07431, 0.18168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02392, 0.14449, 0.0562, -0.13008, 0.13906, 0.02714, 0.04776, 0.28782, 0.11191, -0.2591, 0.27699, 0.05406, 0.10682, 0.26119, -0.26646, -0.20229, -0.24326, 0.24579, -0.13107, 0.32005, 0.05663, 0.34121, 0.13265, -0.30716, 0.32838, 0.06406, 0.23288, 0.24016, 0.12664, 0.30964, 0.34483, 0.2618, 0.38712, -0.22176, 0.31223, -0.31877, 0.16682, -0.41371, -0.07559, -0.43966, -0.17158, 0.39749, -0.4249, -0.0829, -0.30138, -0.31092, -0.16397, -0.40071, -0.00308, 0.43292, 0.07622, 0.05786, 0.08431, -0.04974, 0.06781, -0.07085, 0.03543, -0.09124, -0.01798, -0.09618, -0.0378, 0.08782, -0.09388, -0.0183, -0.06665, -0.06886, -0.03633, -0.08858, -0.0008, 0.09567, -0.02401, -0.01819, -0.02708, 0.01535, -0.02175, 0.02211, -0.01177, 0.0288, 0.005, 0.03074, 0.012, -0.02769, 0.02959, 0.00578, 0.02095, 0.02158, 0.01137, 0.02788, 0.00015, -0.03014, -0.27947, -0.41569, -0.5115, 0.09971, -0.4649, 0.23517, -0.34005, 0.3949, -0.08188, 0.51474, 0.33676, -0.37089, 0.43416, 0.24988, 0.21353, 0.45302, 0.0294, 0.50003, 0.16525, -0.47284], "curve": 0.348, "c2": 0.4, "c3": 0.682, "c4": 0.74}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "vertices": [-1.33154, 1.75366, 1.15707, 1.92291, 1.63989, 1.53442, -2.02057, -0.87361, 0.42201, -2.16046, -1.34592, 1.77264, 1.16797, 1.94305, -2.04321, -0.88242, 0.42625, -2.18433, -3.94373, -0.18866, -1.75647, 3.6073, -0.76044, -3.8744, 3.62296, -1.56934, -6.88803, 0.69769, -2.07971, 6.69891, -2.32581, -6.52083, 5.8747, -3.66272, -11.58063, -0.57141, -5.16608, 10.59192, -2.21704, -11.3815, 10.64626, -4.59198, 11.36093, 2.30872, -12.42981, -1.13855, -6.04791, 11.16577, -1.86987, -12.34154, 11.65838, -4.45795, 12.11496, 2.99744, -11.58063, -0.57129, -2.21692, -11.38161, 10.64637, -4.59186, 11.36102, 2.30872, -4.26031, -14.71904, 13.47106, -7.30029, 15.22632, 1.69916, 7.86499, -4.28418, 8.90186, 0.97205, 2.97708, -6.01093, 5.00079, -4.46637, 3.9408, -5.01733, 5.52527, -3.18542, 3.9408, -5.01733, 5.52527, -3.18542, 5.52527, -3.18542, 2.52612, -8.25946, 0.87372, -7.58322, 0.77844, 1.90317, 2.41821, 5.91223, 2.41821, 5.91223, 2.44598, 5.98132, 7.83899, 6.29099, 4.27716, 7.30124, 2.37646, 7.57813, 2.36749, 7.30774, 2.20752, 7.50082, 2.0014, 7.7493, 1.81793, 7.97086, 1.7157, 4.19388, 1.9649, 4.07816, 0.85535, 2.09082, 0.8504, 2.0787, 0.97253, 2.37741, 0, 0, 0, 0, 0, 0, 2.80048, 1.48773, -0.02905, 4.25428, 0, 0, 0, 0, 0, 0, -2.37457, 5.50259, -5.88306, -1.14624, -2.27063, -5.54852, -5.51526, 12.77574, -13.6589, -2.66412, -9.68942, -9.99341, -5.27008, -12.88098, -2.65527, 11.7634, 0.61578, 8.0048, -0.55164, 7.70369, 2.06743, 5.86275, 0.02402, 4.95053, 4.48993, 8.02096, -0.05579, 8.12894, 4.94708, 0.81725, -0.00037, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, -0.93103, 2.27393, 0.40088, 2.42455, -1.44147, 3.52008, 0.62274, 3.75257, -2.17151, 5.13702, -0.04724, 5.57791, 2.88977, 4.77072, -3.6817, 3.71954, -1.98383, 4.84412, 0.85699, 5.16412, 3.52438, 3.6347, -2.11478, 1.19604, -1.70868, 1.72638, -0.92072, 2.24835, 1.63583, 1.68695, -1.20837, 0.68341, -0.97638, 0.98645, -0.52612, 1.28467, 0.93469, 0.96393, -1.2084, 0.68341, -0.97638, 0.98645, -0.52612, 1.28473, 0.93472, 0.96393, 1.31198, 2.18268, 1.85699, 1.74316, 2.38116, 0.90234, -2.29523, -0.99132, 1.79471, -1.74109, -1.9014, 2.50427, 1.64996, 2.745, 2.33545, 2.19214, 2.99451, 1.13477, -2.88647, -1.24666, 2.25699, -2.18958, -2.94934, -0.04822, -1.21872, 2.73749, -0.42688, 2.96558, -0.65924, -2.87561, 2.90805, 0.49188, -0.96786, 1.27472, 0.8399, 1.39728, 1.1889, 1.11584, -1.4693, -0.63454, 1.1489, -1.1145, -1.15683, 1.52374, 1.004, 1.6701, 1.4212, 1.33374, -1.75629, -0.75845, -1.10791, 1.45953, 0.96298, 1.60004, 1.36462, 1.27673, -1.68146, -0.72688, 0.35101, -1.79761, -2.6315, 3.46588, 2.28476, 3.79968, 3.23547, 3.03326, -3.9942, -1.72581, 0.83364, -4.27026, 10.44598, 4.52307, -1.8894, 11.22525, 6.82751, -6.88354, 4.69727, -8.48254, -1.23285, 9.37206, -8.186, -4.73059, 7.62579, 7.01105, 9.25238, 4.66815, 10.32684, 0.78647, 4.97717, -8.77283, 6.99252, 5.31042, 7.9014, -4.46838, -3.48047, 8.061, -8.6179, -1.68158, 2.36862, 7.38831, 8.05383, 0.65228, 7.92963, -1.56482, -6.60529, 4.06883, 4.65628, 7.74689, 6.5907, 6.18677, 8.45148, 3.20273, -8.14648, -3.51831, 6.36996, -6.17871, -2.21753, 4.73926, 3.67404, 3.90619, 4.59979, 2.75818, -5.1319, -1.01848, -0.09969, -5.23102, -4.67319, 2.22211, 0.27713, 5.22589, 1.69019, 4.95294, -3.27625, -4.00562, -6.90491, 5.16119, 2.21356, 8.44763, 4.43134, 7.52582, -6.66412, -5.4687, 7.60345, -4.06256, -7.64963, 6.70435, 3.39957, 9.74017, 5.9245, 8.44647, -8.34015, -5.82253, 3.9094, -9.38959, -6.3522, 8.36627, 5.51178, 9.17047, 7.80164, 7.32367, -9.64319, -4.16464, 2.01184, -10.30963, -3.61606, 0.78149, -0.6882, 3.68054, -1.62384, -3.3244, 2.90117, -2.29474, -4.61905, 0.46545, -1.38882, 4.49597, -1.55768, -4.37364, 3.94057, -2.45398, -3.35782, 2.17523, 0.75488, 3.97876, -2.91571, -2.73964, 2.05458, -3.4325, -0.9577, 4.12708, 3.59653, 2.49792, -4.23541, 0.05766, -0.96026, -4.12524, 1.5679, -3.93793, -1.18597, -1.40417, -1.82196, 0.56378, 1.08081, -1.48875, 0.9874, -3.70624, 2.92667, -2.48279, 5.94627, -4.10663, 7.22614, 0.02399, -1.89838, 4.3979, -4.70194, -0.91711, -3.33578, -3.44073, -1.81403, -4.43442, 0.64532, -1.49368, 1.59691, 0.31201, 0.61578, 1.50562, -1.70746, 3.95638, -4.22991, -0.8244, -1.63226, -3.98929, 0.88965, 2.17508, 9.61384, 10.7322, 14.13177, -4.83204, 12.28498, -8.49629, 8.24201, -12.55261, 0.41178, -15.0103, -8.11862, 11.90322, -13.36022, -5.39321, -7.8849, -12.06212, -2.8264, -14.12861, 2.92194, 6.92896, 7.83377, -0.0415, 7.52795, -2.17316, 6.12598, -4.88141, 2.64545, -7.37216, -6.02747, 4.49538, -5.67788, -4.92957, -1.84445, -7.29187, 1.00403, -7.45258, 3.41418, 2.8733, 0.69336, 4.17861, 0.40021, 2.41085, 0.93732, -2.17024, 2.32017, 0.45282, 0.89484, 2.18781, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28802, 1.73997, 0.67682, -1.56641, 1.67453, 0.32684, 0.57513, 3.466, 1.34766, -3.12009, 3.33559, 0.65094, 1.28638, 3.14532, -3.20877, -2.43604, -2.92932, 2.95978, -1.57837, 3.8541, 0.68188, 4.10887, 1.59741, -3.69881, 3.95434, 0.77136, 2.80441, 2.89209, 1.52502, 3.72873, 6.46905, 4.90914, 7.27706, -4.1521, 5.87192, -5.9756, 3.20929, -7.78915, -1.36677, -8.31166, -3.22043, 7.45549, -7.96962, -1.55666, -5.65298, -5.83177, -3.07741, -7.51561, -0.05658, 8.12044, 3.86266, 2.93126, 4.34132, -2.48188, 3.50418, -3.56974, 1.95144, -4.66818, -0.79669, -4.99504, -1.92188, 4.45109, -4.75829, -0.92929, -3.37609, -3.48311, -1.8383, -4.48783, -0.0347, 4.84853, -0.28912, -0.21899, -0.32605, 0.18481, -0.26196, 0.26624, -0.14178, 0.34683, 0.06018, 0.37016, 0.14447, -0.33348, 0.35635, 0.06964, 0.25232, 0.25983, 0.13696, 0.33575, 0.00183, -0.36301, -3.36545, -5.0058, -6.15955, 1.20068, -5.59839, 2.83197, -4.09497, 4.7554, -0.98596, 6.19857, 4.0553, -4.46628, 5.22819, 3.00903, 2.57132, 5.45532, 0.354, 6.02139, 1.98993, -5.69405], "curve": 0.243, "c3": 0.676, "c4": 0.7}, {"time": 3.3333, "vertices": [-0.11057, 0.14563, 0.09609, 0.15968, 0.13618, 0.12742, -0.16779, -0.07255, 0.03504, -0.17941, -0.11177, 0.1472, 0.09699, 0.16135, -0.16967, -0.07328, 0.0354, -0.18139, -0.32749, -0.01567, -0.14586, 0.29956, -0.06315, -0.32174, 0.30086, -0.13032, -0.572, 0.05794, -0.1727, 0.55629, -0.19314, -0.5415, 0.48785, -0.30416, -0.96168, -0.04745, -0.429, 0.87957, -0.18411, -0.94514, 0.88409, -0.38133, 0.94343, 0.19172, -1.0322, -0.09455, -0.50223, 0.92723, -0.15528, -1.02487, 0.96813, -0.3702, 1.00605, 0.24891, -0.96168, -0.04744, -0.1841, -0.94515, 0.8841, -0.38132, 0.94344, 0.19172, -0.35378, -1.2223, 1.11866, -0.60623, 1.26442, 0.1411, 0.65312, -0.35577, 0.73923, 0.08072, 0.24722, -0.49916, 0.41528, -0.3709, 0.32725, -0.41665, 0.45883, -0.26452, 0.32725, -0.41665, 0.45883, -0.26452, 0.45883, -0.26452, 0.20977, -0.68588, 0.07256, -0.62972, 0.06464, 0.15804, 0.20081, 0.49096, 0.20081, 0.49096, 0.20312, 0.4967, 0.65096, 0.52242, 0.35518, 0.60631, 0.19735, 0.6293, 0.1966, 0.60685, 0.18332, 0.62288, 0.1662, 0.64352, 0.15096, 0.66191, 0.14247, 0.34827, 0.16317, 0.33866, 0.07103, 0.17363, 0.07062, 0.17262, 0.08076, 0.19742, 0, 0, 0, 0, 0, 0, 0.23256, 0.12354, -0.00241, 0.35328, 0, 0, 0, 0, 0, 0, -0.19719, 0.45695, -0.48854, -0.09519, -0.18856, -0.46076, -0.458, 1.06092, -1.13426, -0.22123, -0.80463, -0.82987, -0.43764, -1.06966, -0.2205, 0.97686, 0.05114, 0.66473, -0.04581, 0.63973, -0.00352, 0.48565, 0.00238, 0.3518, 0, 0, 0, 0, -0.0001, -1e-05, -3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, -0.07731, 0.18883, 0.03329, 0.20134, -0.1197, 0.29231, 0.05171, 0.31162, -0.18033, 0.42659, -0.00392, 0.4632, 0.23997, 0.39617, -0.30574, 0.30888, -0.16474, 0.40226, 0.07117, 0.42884, 0.29267, 0.30183, -0.17562, 0.09932, -0.14189, 0.14336, -0.07646, 0.18671, 0.13584, 0.14009, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10668, 0.07762, 0.08005, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10669, 0.07762, 0.08005, 0.10895, 0.18125, 0.15421, 0.14476, 0.19774, 0.07493, -0.1906, -0.08232, 0.14904, -0.14458, -0.1579, 0.20796, 0.13702, 0.22795, 0.19394, 0.18204, 0.24867, 0.09423, -0.2397, -0.10352, 0.18742, -0.18183, -0.24492, -0.004, -0.1012, 0.22733, -0.03545, 0.24627, -0.05474, -0.2388, 0.24149, 0.04085, -0.08037, 0.10586, 0.06975, 0.11603, 0.09873, 0.09266, -0.12201, -0.05269, 0.09541, -0.09255, -0.09607, 0.12653, 0.08337, 0.13869, 0.11802, 0.11076, -0.14585, -0.06298, -0.092, 0.1212, 0.07997, 0.13287, 0.11332, 0.10602, -0.13963, -0.06036, 0.02915, -0.14928, -0.21852, 0.28781, 0.18973, 0.31553, 0.26868, 0.25189, -0.33169, -0.14331, 0.06923, -0.35461, 0.73769, 0.54651, -0.35387, 0.8471, 0.67955, -0.3843, 0.54941, -0.55481, -0.29934, 0.69321, -0.52575, -0.54221, 0.63326, 0.58221, 0.76834, 0.38765, 0.85756, 0.06531, 0.41331, -0.72851, 0.58067, 0.44099, 0.65615, -0.37106, -0.28902, 0.6694, -0.71565, -0.13964, 0.19669, 0.61354, 0.66881, 0.05417, 0.65849, -0.12995, -0.54852, 0.33788, 0.38667, 0.64332, 0.5473, 0.51376, 0.70183, 0.26596, -0.6765, -0.29217, 0.52897, -0.51309, -0.18415, 0.39356, 0.3051, 0.32438, 0.38198, 0.22904, -0.42616, -0.08458, -0.00828, -0.43439, -0.38807, 0.18453, 0.02301, 0.43397, 0.14036, 0.4113, -0.27207, -0.33263, -0.5734, 0.4286, 0.18382, 0.70151, 0.36799, 0.62496, -0.5534, -0.45413, 0.63141, -0.33736, -0.63524, 0.55674, 0.28231, 0.80884, 0.49198, 0.70141, -0.69258, -0.48351, 0.32464, -0.77973, -0.5275, 0.69475, 0.45771, 0.76153, 0.64786, 0.60817, -0.80079, -0.34584, 0.16707, -0.85613, -0.30028, 0.0649, -0.05715, 0.30564, -0.13485, -0.27606, 0.24092, -0.19056, -0.38357, 0.03865, -0.11533, 0.37335, -0.12935, -0.3632, 0.32723, -0.20378, -0.27884, 0.18064, 0.06269, 0.3304, -0.24213, -0.2275, 0.17062, -0.28504, -0.07953, 0.34272, 0.29866, 0.20743, -0.35172, 0.00479, -0.07974, -0.34257, 0.1302, -0.32701, -0.09849, -0.11661, -0.1513, 0.04682, 0.08975, -0.12363, 0.082, -0.30777, 0.24304, -0.20618, 0.49379, -0.34102, 0.60007, 0.00199, -0.15764, 0.36521, -0.39046, -0.07616, -0.27701, -0.28573, -0.15064, -0.36824, 0.05359, -0.12404, 0.13261, 0.02591, 0.05114, 0.12503, -0.14179, 0.32854, -0.35126, -0.06846, -0.13555, -0.33128, 0.07388, 0.18062, 0.31334, 0.73186, 0.82651, -0.01037, 0.79267, -0.235, 0.64276, -0.51962, 0.27355, -0.77988, -0.63549, 0.47945, -0.60393, -0.51865, -0.19941, -0.77098, 0.10202, -0.78957, 0.24264, 0.57539, 0.65053, -0.00345, 0.62514, -0.18046, 0.50871, -0.40536, 0.21968, -0.6122, -0.50053, 0.3733, -0.4715, -0.40936, -0.15317, -0.60553, 0.08338, -0.61888, 0.28352, 0.2386, 0.05758, 0.347, 0.03323, 0.2002, 0.07784, -0.18022, 0.19267, 0.0376, 0.07431, 0.18168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02392, 0.14449, 0.0562, -0.13008, 0.13906, 0.02714, 0.04776, 0.28782, 0.11191, -0.2591, 0.27699, 0.05406, 0.10682, 0.26119, -0.26646, -0.20229, -0.24326, 0.24579, -0.13107, 0.32005, 0.05663, 0.34121, 0.13265, -0.30716, 0.32838, 0.06406, 0.23288, 0.24016, 0.12664, 0.30964, 0.34483, 0.2618, 0.38712, -0.22176, 0.31223, -0.31877, 0.16682, -0.41371, -0.07559, -0.43966, -0.17158, 0.39749, -0.4249, -0.0829, -0.30138, -0.31092, -0.16397, -0.40071, -0.00308, 0.43292, 0.07622, 0.05786, 0.08431, -0.04974, 0.06781, -0.07085, 0.03543, -0.09124, -0.01798, -0.09618, -0.0378, 0.08782, -0.09388, -0.0183, -0.06665, -0.06886, -0.03633, -0.08858, -0.0008, 0.09567, -0.02401, -0.01819, -0.02708, 0.01535, -0.02175, 0.02211, -0.01177, 0.0288, 0.005, 0.03074, 0.012, -0.02769, 0.02959, 0.00578, 0.02095, 0.02158, 0.01137, 0.02788, 0.00015, -0.03014, -0.27947, -0.41569, -0.5115, 0.09971, -0.4649, 0.23517, -0.34005, 0.3949, -0.08188, 0.51474, 0.33676, -0.37089, 0.43416, 0.24988, 0.21353, 0.45302, 0.0294, 0.50003, 0.16525, -0.47284], "curve": 0.348, "c2": 0.4, "c3": 0.682, "c4": 0.74}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "vertices": [-1.33154, 1.75366, 1.15707, 1.92291, 1.63989, 1.53442, -2.02057, -0.87361, 0.42201, -2.16046, -1.34592, 1.77264, 1.16797, 1.94305, -2.04321, -0.88242, 0.42625, -2.18433, -3.94373, -0.18866, -1.75647, 3.6073, -0.76044, -3.8744, 3.62296, -1.56934, -6.88803, 0.69769, -2.07971, 6.69891, -2.32581, -6.52083, 5.8747, -3.66272, -11.58063, -0.57141, -5.16608, 10.59192, -2.21704, -11.3815, 10.64626, -4.59198, 11.36093, 2.30872, -12.42981, -1.13855, -6.04791, 11.16577, -1.86987, -12.34154, 11.65838, -4.45795, 12.11496, 2.99744, -11.58063, -0.57129, -2.21692, -11.38161, 10.64637, -4.59186, 11.36102, 2.30872, -4.26031, -14.71904, 13.47106, -7.30029, 15.22632, 1.69916, 7.86499, -4.28418, 8.90186, 0.97205, 2.97708, -6.01093, 5.00079, -4.46637, 3.9408, -5.01733, 5.52527, -3.18542, 3.9408, -5.01733, 5.52527, -3.18542, 5.52527, -3.18542, 2.52612, -8.25946, 0.87372, -7.58322, 0.77844, 1.90317, 2.41821, 5.91223, 2.41821, 5.91223, 2.44598, 5.98132, 7.83899, 6.29099, 4.27716, 7.30124, 2.37646, 7.57813, 2.36749, 7.30774, 2.20752, 7.50082, 2.0014, 7.7493, 1.81793, 7.97086, 1.7157, 4.19388, 1.9649, 4.07816, 0.85535, 2.09082, 0.8504, 2.0787, 0.97253, 2.37741, 0, 0, 0, 0, 0, 0, 2.80048, 1.48773, -0.02905, 4.25428, 0, 0, 0, 0, 0, 0, -2.37457, 5.50259, -5.88306, -1.14624, -2.27063, -5.54852, -5.51526, 12.77574, -13.6589, -2.66412, -9.68942, -9.99341, -5.27008, -12.88098, -2.65527, 11.7634, 0.61578, 8.0048, -0.55164, 7.70369, 2.06743, 5.86275, 0.02402, 4.95053, 4.48993, 8.02096, -0.05579, 8.12894, 4.94708, 0.81725, -0.00037, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, -0.93103, 2.27393, 0.40088, 2.42455, -1.44147, 3.52008, 0.62274, 3.75257, -2.17151, 5.13702, -0.04724, 5.57791, 2.88977, 4.77072, -3.6817, 3.71954, -1.98383, 4.84412, 0.85699, 5.16412, 3.52438, 3.6347, -2.11478, 1.19604, -1.70868, 1.72638, -0.92072, 2.24835, 1.63583, 1.68695, -1.20837, 0.68341, -0.97638, 0.98645, -0.52612, 1.28467, 0.93469, 0.96393, -1.2084, 0.68341, -0.97638, 0.98645, -0.52612, 1.28473, 0.93472, 0.96393, 1.31198, 2.18268, 1.85699, 1.74316, 2.38116, 0.90234, -2.29523, -0.99132, 1.79471, -1.74109, -1.9014, 2.50427, 1.64996, 2.745, 2.33545, 2.19214, 2.99451, 1.13477, -2.88647, -1.24666, 2.25699, -2.18958, -2.94934, -0.04822, -1.21872, 2.73749, -0.42688, 2.96558, -0.65924, -2.87561, 2.90805, 0.49188, -0.96786, 1.27472, 0.8399, 1.39728, 1.1889, 1.11584, -1.4693, -0.63454, 1.1489, -1.1145, -1.15683, 1.52374, 1.004, 1.6701, 1.4212, 1.33374, -1.75629, -0.75845, -1.10791, 1.45953, 0.96298, 1.60004, 1.36462, 1.27673, -1.68146, -0.72688, 0.35101, -1.79761, -2.6315, 3.46588, 2.28476, 3.79968, 3.23547, 3.03326, -3.9942, -1.72581, 0.83364, -4.27026, 10.44598, 4.52307, -1.8894, 11.22525, 6.82751, -6.88354, 4.69727, -8.48254, -1.23285, 9.37206, -8.186, -4.73059, 7.62579, 7.01105, 9.25238, 4.66815, 10.32684, 0.78647, 4.97717, -8.77283, 6.99252, 5.31042, 7.9014, -4.46838, -3.48047, 8.061, -8.6179, -1.68158, 2.36862, 7.38831, 8.05383, 0.65228, 7.92963, -1.56482, -6.60529, 4.06883, 4.65628, 7.74689, 6.5907, 6.18677, 8.45148, 3.20273, -8.14648, -3.51831, 6.36996, -6.17871, -2.21753, 4.73926, 3.67404, 3.90619, 4.59979, 2.75818, -5.1319, -1.01848, -0.09969, -5.23102, -4.67319, 2.22211, 0.27713, 5.22589, 1.69019, 4.95294, -3.27625, -4.00562, -6.90491, 5.16119, 2.21356, 8.44763, 4.43134, 7.52582, -6.66412, -5.4687, 7.60345, -4.06256, -7.64963, 6.70435, 3.39957, 9.74017, 5.9245, 8.44647, -8.34015, -5.82253, 3.9094, -9.38959, -6.3522, 8.36627, 5.51178, 9.17047, 7.80164, 7.32367, -9.64319, -4.16464, 2.01184, -10.30963, -3.61606, 0.78149, -0.6882, 3.68054, -1.62384, -3.3244, 2.90117, -2.29474, -4.61905, 0.46545, -1.38882, 4.49597, -1.55768, -4.37364, 3.94057, -2.45398, -3.35782, 2.17523, 0.75488, 3.97876, -2.91571, -2.73964, 2.05458, -3.4325, -0.9577, 4.12708, 3.59653, 2.49792, -4.23541, 0.05766, -0.96026, -4.12524, 1.5679, -3.93793, -1.18597, -1.40417, -1.82196, 0.56378, 1.08081, -1.48875, 0.9874, -3.70624, 2.92667, -2.48279, 5.94627, -4.10663, 7.22614, 0.02399, -1.89838, 4.3979, -4.70194, -0.91711, -3.33578, -3.44073, -1.81403, -4.43442, 0.64532, -1.49368, 1.59691, 0.31201, 0.61578, 1.50562, -1.70746, 3.95638, -4.22991, -0.8244, -1.63226, -3.98929, 0.88965, 2.17508, 9.61384, 10.7322, 14.13177, -4.83204, 12.28498, -8.49629, 8.24201, -12.55261, 0.41178, -15.0103, -8.11862, 11.90322, -13.36022, -5.39321, -7.8849, -12.06212, -2.8264, -14.12861, 2.92194, 6.92896, 7.83377, -0.0415, 7.52795, -2.17316, 6.12598, -4.88141, 2.64545, -7.37216, -6.02747, 4.49538, -5.67788, -4.92957, -1.84445, -7.29187, 1.00403, -7.45258, 3.41418, 2.8733, 0.69336, 4.17861, 0.40021, 2.41085, 0.93732, -2.17024, 2.32017, 0.45282, 0.89484, 2.18781, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28802, 1.73997, 0.67682, -1.56641, 1.67453, 0.32684, 0.57513, 3.466, 1.34766, -3.12009, 3.33559, 0.65094, 1.28638, 3.14532, -3.20877, -2.43604, -2.92932, 2.95978, -1.57837, 3.8541, 0.68188, 4.10887, 1.59741, -3.69881, 3.95434, 0.77136, 2.80441, 2.89209, 1.52502, 3.72873, 6.46905, 4.90914, 7.27706, -4.1521, 5.87192, -5.9756, 3.20929, -7.78915, -1.36677, -8.31166, -3.22043, 7.45549, -7.96962, -1.55666, -5.65298, -5.83177, -3.07741, -7.51561, -0.05658, 8.12044, 3.86266, 2.93126, 4.34132, -2.48188, 3.50418, -3.56974, 1.95144, -4.66818, -0.79669, -4.99504, -1.92188, 4.45109, -4.75829, -0.92929, -3.37609, -3.48311, -1.8383, -4.48783, -0.0347, 4.84853, -0.28912, -0.21899, -0.32605, 0.18481, -0.26196, 0.26624, -0.14178, 0.34683, 0.06018, 0.37016, 0.14447, -0.33348, 0.35635, 0.06964, 0.25232, 0.25983, 0.13696, 0.33575, 0.00183, -0.36301, -3.36545, -5.0058, -6.15955, 1.20068, -5.59839, 2.83197, -4.09497, 4.7554, -0.98596, 6.19857, 4.0553, -4.46628, 5.22819, 3.00903, 2.57132, 5.45532, 0.354, 6.02139, 1.98993, -5.69405], "curve": 0.243, "c3": 0.676, "c4": 0.7}, {"time": 6.6667, "vertices": [-0.11057, 0.14563, 0.09609, 0.15968, 0.13618, 0.12742, -0.16779, -0.07255, 0.03504, -0.17941, -0.11177, 0.1472, 0.09699, 0.16135, -0.16967, -0.07328, 0.0354, -0.18139, -0.32749, -0.01567, -0.14586, 0.29956, -0.06315, -0.32174, 0.30086, -0.13032, -0.572, 0.05794, -0.1727, 0.55629, -0.19314, -0.5415, 0.48785, -0.30416, -0.96168, -0.04745, -0.429, 0.87957, -0.18411, -0.94514, 0.88409, -0.38133, 0.94343, 0.19172, -1.0322, -0.09455, -0.50223, 0.92723, -0.15528, -1.02487, 0.96813, -0.3702, 1.00605, 0.24891, -0.96168, -0.04744, -0.1841, -0.94515, 0.8841, -0.38132, 0.94344, 0.19172, -0.35378, -1.2223, 1.11866, -0.60623, 1.26442, 0.1411, 0.65312, -0.35577, 0.73923, 0.08072, 0.24722, -0.49916, 0.41528, -0.3709, 0.32725, -0.41665, 0.45883, -0.26452, 0.32725, -0.41665, 0.45883, -0.26452, 0.45883, -0.26452, 0.20977, -0.68588, 0.07256, -0.62972, 0.06464, 0.15804, 0.20081, 0.49096, 0.20081, 0.49096, 0.20312, 0.4967, 0.65096, 0.52242, 0.35518, 0.60631, 0.19735, 0.6293, 0.1966, 0.60685, 0.18332, 0.62288, 0.1662, 0.64352, 0.15096, 0.66191, 0.14247, 0.34827, 0.16317, 0.33866, 0.07103, 0.17363, 0.07062, 0.17262, 0.08076, 0.19742, 0, 0, 0, 0, 0, 0, 0.23256, 0.12354, -0.00241, 0.35328, 0, 0, 0, 0, 0, 0, -0.19719, 0.45695, -0.48854, -0.09519, -0.18856, -0.46076, -0.458, 1.06092, -1.13426, -0.22123, -0.80463, -0.82987, -0.43764, -1.06966, -0.2205, 0.97686, 0.05114, 0.66473, -0.04581, 0.63973, -0.00352, 0.48565, 0.00238, 0.3518, 0, 0, 0, 0, -0.0001, -1e-05, -3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, -0.07731, 0.18883, 0.03329, 0.20134, -0.1197, 0.29231, 0.05171, 0.31162, -0.18033, 0.42659, -0.00392, 0.4632, 0.23997, 0.39617, -0.30574, 0.30888, -0.16474, 0.40226, 0.07117, 0.42884, 0.29267, 0.30183, -0.17562, 0.09932, -0.14189, 0.14336, -0.07646, 0.18671, 0.13584, 0.14009, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10668, 0.07762, 0.08005, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10669, 0.07762, 0.08005, 0.10895, 0.18125, 0.15421, 0.14476, 0.19774, 0.07493, -0.1906, -0.08232, 0.14904, -0.14458, -0.1579, 0.20796, 0.13702, 0.22795, 0.19394, 0.18204, 0.24867, 0.09423, -0.2397, -0.10352, 0.18742, -0.18183, -0.24492, -0.004, -0.1012, 0.22733, -0.03545, 0.24627, -0.05474, -0.2388, 0.24149, 0.04085, -0.08037, 0.10586, 0.06975, 0.11603, 0.09873, 0.09266, -0.12201, -0.05269, 0.09541, -0.09255, -0.09607, 0.12653, 0.08337, 0.13869, 0.11802, 0.11076, -0.14585, -0.06298, -0.092, 0.1212, 0.07997, 0.13287, 0.11332, 0.10602, -0.13963, -0.06036, 0.02915, -0.14928, -0.21852, 0.28781, 0.18973, 0.31553, 0.26868, 0.25189, -0.33169, -0.14331, 0.06923, -0.35461, 0.73769, 0.54651, -0.35387, 0.8471, 0.67955, -0.3843, 0.54941, -0.55481, -0.29934, 0.69321, -0.52575, -0.54221, 0.63326, 0.58221, 0.76834, 0.38765, 0.85756, 0.06531, 0.41331, -0.72851, 0.58067, 0.44099, 0.65615, -0.37106, -0.28902, 0.6694, -0.71565, -0.13964, 0.19669, 0.61354, 0.66881, 0.05417, 0.65849, -0.12995, -0.54852, 0.33788, 0.38667, 0.64332, 0.5473, 0.51376, 0.70183, 0.26596, -0.6765, -0.29217, 0.52897, -0.51309, -0.18415, 0.39356, 0.3051, 0.32438, 0.38198, 0.22904, -0.42616, -0.08458, -0.00828, -0.43439, -0.38807, 0.18453, 0.02301, 0.43397, 0.14036, 0.4113, -0.27207, -0.33263, -0.5734, 0.4286, 0.18382, 0.70151, 0.36799, 0.62496, -0.5534, -0.45413, 0.63141, -0.33736, -0.63524, 0.55674, 0.28231, 0.80884, 0.49198, 0.70141, -0.69258, -0.48351, 0.32464, -0.77973, -0.5275, 0.69475, 0.45771, 0.76153, 0.64786, 0.60817, -0.80079, -0.34584, 0.16707, -0.85613, -0.30028, 0.0649, -0.05715, 0.30564, -0.13485, -0.27606, 0.24092, -0.19056, -0.38357, 0.03865, -0.11533, 0.37335, -0.12935, -0.3632, 0.32723, -0.20378, -0.27884, 0.18064, 0.06269, 0.3304, -0.24213, -0.2275, 0.17062, -0.28504, -0.07953, 0.34272, 0.29866, 0.20743, -0.35172, 0.00479, -0.07974, -0.34257, 0.1302, -0.32701, -0.09849, -0.11661, -0.1513, 0.04682, 0.08975, -0.12363, 0.082, -0.30777, 0.24304, -0.20618, 0.49379, -0.34102, 0.60007, 0.00199, -0.15764, 0.36521, -0.39046, -0.07616, -0.27701, -0.28573, -0.15064, -0.36824, 0.05359, -0.12404, 0.13261, 0.02591, 0.05114, 0.12503, -0.14179, 0.32854, -0.35126, -0.06846, -0.13555, -0.33128, 0.07388, 0.18062, 0.31334, 0.73186, 0.82651, -0.01037, 0.79267, -0.235, 0.64276, -0.51962, 0.27355, -0.77988, -0.63549, 0.47945, -0.60393, -0.51865, -0.19941, -0.77098, 0.10202, -0.78957, 0.24264, 0.57539, 0.65053, -0.00345, 0.62514, -0.18046, 0.50871, -0.40536, 0.21968, -0.6122, -0.50053, 0.3733, -0.4715, -0.40936, -0.15317, -0.60553, 0.08338, -0.61888, 0.28352, 0.2386, 0.05758, 0.347, 0.03323, 0.2002, 0.07784, -0.18022, 0.19267, 0.0376, 0.07431, 0.18168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02392, 0.14449, 0.0562, -0.13008, 0.13906, 0.02714, 0.04776, 0.28782, 0.11191, -0.2591, 0.27699, 0.05406, 0.10682, 0.26119, -0.26646, -0.20229, -0.24326, 0.24579, -0.13107, 0.32005, 0.05663, 0.34121, 0.13265, -0.30716, 0.32838, 0.06406, 0.23288, 0.24016, 0.12664, 0.30964, 0.34483, 0.2618, 0.38712, -0.22176, 0.31223, -0.31877, 0.16682, -0.41371, -0.07559, -0.43966, -0.17158, 0.39749, -0.4249, -0.0829, -0.30138, -0.31092, -0.16397, -0.40071, -0.00308, 0.43292, 0.07622, 0.05786, 0.08431, -0.04974, 0.06781, -0.07085, 0.03543, -0.09124, -0.01798, -0.09618, -0.0378, 0.08782, -0.09388, -0.0183, -0.06665, -0.06886, -0.03633, -0.08858, -0.0008, 0.09567, -0.02401, -0.01819, -0.02708, 0.01535, -0.02175, 0.02211, -0.01177, 0.0288, 0.005, 0.03074, 0.012, -0.02769, 0.02959, 0.00578, 0.02095, 0.02158, 0.01137, 0.02788, 0.00015, -0.03014, -0.27947, -0.41569, -0.5115, 0.09971, -0.4649, 0.23517, -0.34005, 0.3949, -0.08188, 0.51474, 0.33676, -0.37089, 0.43416, 0.24988, 0.21353, 0.45302, 0.0294, 0.50003, 0.16525, -0.47284]}]}, "t2": {"t2": [{"offset": 102, "vertices": [-0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, -0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, 0.40561, -0.66316, 0.46335, -0.62429, -0.12595, -0.76691, -0.67407, -0.38706, 0.57236, -0.22934, 0.5906, -0.17724, 0.28364, -0.54742, -0.2451, -0.56578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61216, 0.34135, -0.61216, 0.34135, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.23596, -0.0937, -0.22175, 0.12366, -0.20618, 0.51912, 0.27205, 0.48779, 0.05997, 0.80355, 0.66059, 0.46116, 0.2428, 0.81257, 0.78309, 0.32513, -0.27489, 0.69312, 0.30687, 0.68007, 0.72089, 0.19177, -0.21912, 0.52206, 0.22281, 0.521, 0.54448, 0.15642, -0.12995, 0.53582, 0.29472, 0.46652, 0.54769, 0.06624, 0.03248, 0.65788, 0.49542, 0.4348, 0.64992, -0.10937, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.44489, 0.5868, 0.34061, 0.65287, 0.28081, 0.68072, -0.31114, 0.23589, -0.34617, 0.18059, -0.36094, 0.14886, -0.09019, 0.32075, -0.26551, 0.20131, -0.29541, 0.15411, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.59543, -0.45922, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.35539, -0.75701, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, 0.94577, -1.11945, 1.04219, -1.0305, -0.01492, -1.46511, -1.14513, -0.91434, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, -0.45809, -0.2719, -0.4319, -0.31186, -0.52442, 0.09342, -0.259, 0.46545, -0.30287, 0.41645, -0.33892, 0.38751, 0.0428, 0.51343, 0.42488, 0.29115], "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "offset": 102, "vertices": [-0.39032, -12.62598, 1.72471, -12.51413, 2.83794, -12.31027, -6.86739, -10.60141, -12.55664, -1.37654, -0.39032, -12.62598, 1.72471, -12.51413, 2.83794, -12.31027, -6.86739, -10.60141, -12.55664, -1.37654, 4.45408, -7.28235, 5.0882, -6.85556, -1.38306, -8.42169, -7.40213, -4.25039, 6.28526, -2.51843, 6.48556, -1.94629, 3.11476, -6.01135, -2.69147, -6.21304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.72235, 3.74847, -6.72235, 3.74847, -6.72235, 3.74847, -7.153, -2.84146, -6.72235, 3.74847, -7.153, -2.84146, -6.72235, 3.74847, -2.59116, -1.02896, -2.43509, 1.35796, -2.26413, 5.70065, 2.98746, 5.35661, 0.65855, 8.82404, 7.25415, 5.06413, 2.66629, 8.92303, 8.59937, 3.57038, -3.01863, 7.61133, 3.36984, 7.46805, 7.91629, 2.10586, -2.40624, 5.73291, 2.44676, 5.72128, 5.97913, 1.71764, -1.42704, 5.884, 3.23639, 5.12299, 6.01431, 0.72739, 0.35664, 7.2244, 5.44035, 4.77472, 7.13693, -1.20099, 3.7403, 7.16937, 3.08362, 7.47516, 7.51521, 2.98615, 3.7403, 7.16937, 3.08362, 7.47516, 7.51521, 2.98615, 3.7403, 7.16937, 3.08362, 7.47516, 4.88545, 6.44388, 3.7403, 7.16937, 3.08362, 7.47516, -3.41672, 2.59039, -3.80141, 1.98306, -3.96362, 1.6347, -0.99036, 3.52222, -2.91567, 2.2106, -3.24396, 1.69235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.5386, -5.0428, 7.72238, -2.92735, 7.92249, 2.32648, 7.42268, 3.61691, 7.72238, -2.92735, 7.92249, 2.32648, 7.42268, 3.61691, 3.90265, -8.31296, 5.23675, -7.5444, 5.89106, -7.0462, -0.96196, -9.13214, -7.68628, -5.02525, 10.3858, -12.29306, 11.4446, -11.31619, -0.16382, -16.08887, -12.57501, -10.04065, 5.23675, -7.5444, 5.89106, -7.0462, -0.96196, -9.13214, -7.68628, -5.02525, -5.03041, -2.98578, -4.74284, -3.42468, -5.75882, 1.02591, -2.84415, 5.11122, -3.32586, 4.57321, -3.72174, 4.25534, 0.46997, 5.63818, 4.66571, 3.19725], "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 3.3333, "offset": 102, "vertices": [-0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, -0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, 0.40561, -0.66316, 0.46335, -0.62429, -0.12595, -0.76691, -0.67407, -0.38706, 0.57236, -0.22934, 0.5906, -0.17724, 0.28364, -0.54742, -0.2451, -0.56578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61216, 0.34135, -0.61216, 0.34135, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.23596, -0.0937, -0.22175, 0.12366, -0.20618, 0.51912, 0.27205, 0.48779, 0.05997, 0.80355, 0.66059, 0.46116, 0.2428, 0.81257, 0.78309, 0.32513, -0.27489, 0.69312, 0.30687, 0.68007, 0.72089, 0.19177, -0.21912, 0.52206, 0.22281, 0.521, 0.54448, 0.15642, -0.12995, 0.53582, 0.29472, 0.46652, 0.54769, 0.06624, 0.03248, 0.65788, 0.49542, 0.4348, 0.64992, -0.10937, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.44489, 0.5868, 0.34061, 0.65287, 0.28081, 0.68072, -0.31114, 0.23589, -0.34617, 0.18059, -0.36094, 0.14886, -0.09019, 0.32075, -0.26551, 0.20131, -0.29541, 0.15411, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.59543, -0.45922, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.35539, -0.75701, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, 0.94577, -1.11945, 1.04219, -1.0305, -0.01492, -1.46511, -1.14513, -0.91434, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, -0.45809, -0.2719, -0.4319, -0.31186, -0.52442, 0.09342, -0.259, 0.46545, -0.30287, 0.41645, -0.33892, 0.38751, 0.0428, 0.51343, 0.42488, 0.29115], "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "offset": 102, "vertices": [-0.39032, -12.62598, 1.72471, -12.51413, 2.83794, -12.31027, -6.86739, -10.60141, -12.55664, -1.37654, -0.39032, -12.62598, 1.72471, -12.51413, 2.83794, -12.31027, -6.86739, -10.60141, -12.55664, -1.37654, 4.45408, -7.28235, 5.0882, -6.85556, -1.38306, -8.42169, -7.40213, -4.25039, 6.28526, -2.51843, 6.48556, -1.94629, 3.11476, -6.01135, -2.69147, -6.21304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.72235, 3.74847, -6.72235, 3.74847, -6.72235, 3.74847, -7.153, -2.84146, -6.72235, 3.74847, -7.153, -2.84146, -6.72235, 3.74847, -2.59116, -1.02896, -2.43509, 1.35796, -2.26413, 5.70065, 2.98746, 5.35661, 0.65855, 8.82404, 7.25415, 5.06413, 2.66629, 8.92303, 8.59937, 3.57038, -3.01863, 7.61133, 3.36984, 7.46805, 7.91629, 2.10586, -2.40624, 5.73291, 2.44676, 5.72128, 5.97913, 1.71764, -1.42704, 5.884, 3.23639, 5.12299, 6.01431, 0.72739, 0.35664, 7.2244, 5.44035, 4.77472, 7.13693, -1.20099, 3.7403, 7.16937, 3.08362, 7.47516, 7.51521, 2.98615, 3.7403, 7.16937, 3.08362, 7.47516, 7.51521, 2.98615, 3.7403, 7.16937, 3.08362, 7.47516, 4.88545, 6.44388, 3.7403, 7.16937, 3.08362, 7.47516, -3.41672, 2.59039, -3.80141, 1.98306, -3.96362, 1.6347, -0.99036, 3.52222, -2.91567, 2.2106, -3.24396, 1.69235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.5386, -5.0428, 7.72238, -2.92735, 7.92249, 2.32648, 7.42268, 3.61691, 7.72238, -2.92735, 7.92249, 2.32648, 7.42268, 3.61691, 3.90265, -8.31296, 5.23675, -7.5444, 5.89106, -7.0462, -0.96196, -9.13214, -7.68628, -5.02525, 10.3858, -12.29306, 11.4446, -11.31619, -0.16382, -16.08887, -12.57501, -10.04065, 5.23675, -7.5444, 5.89106, -7.0462, -0.96196, -9.13214, -7.68628, -5.02525, -5.03041, -2.98578, -4.74284, -3.42468, -5.75882, 1.02591, -2.84415, 5.11122, -3.32586, 4.57321, -3.72174, 4.25534, 0.46997, 5.63818, 4.66571, 3.19725], "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 6.6667, "offset": 102, "vertices": [-0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, -0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, 0.40561, -0.66316, 0.46335, -0.62429, -0.12595, -0.76691, -0.67407, -0.38706, 0.57236, -0.22934, 0.5906, -0.17724, 0.28364, -0.54742, -0.2451, -0.56578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61216, 0.34135, -0.61216, 0.34135, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.23596, -0.0937, -0.22175, 0.12366, -0.20618, 0.51912, 0.27205, 0.48779, 0.05997, 0.80355, 0.66059, 0.46116, 0.2428, 0.81257, 0.78309, 0.32513, -0.27489, 0.69312, 0.30687, 0.68007, 0.72089, 0.19177, -0.21912, 0.52206, 0.22281, 0.521, 0.54448, 0.15642, -0.12995, 0.53582, 0.29472, 0.46652, 0.54769, 0.06624, 0.03248, 0.65788, 0.49542, 0.4348, 0.64992, -0.10937, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.44489, 0.5868, 0.34061, 0.65287, 0.28081, 0.68072, -0.31114, 0.23589, -0.34617, 0.18059, -0.36094, 0.14886, -0.09019, 0.32075, -0.26551, 0.20131, -0.29541, 0.15411, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.59543, -0.45922, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.35539, -0.75701, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, 0.94577, -1.11945, 1.04219, -1.0305, -0.01492, -1.46511, -1.14513, -0.91434, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, -0.45809, -0.2719, -0.4319, -0.31186, -0.52442, 0.09342, -0.259, 0.46545, -0.30287, 0.41645, -0.33892, 0.38751, 0.0428, 0.51343, 0.42488, 0.29115]}]}, "st": {"st": [{"offset": 24, "vertices": [-0.33852, -0.10051, -0.12772, -0.32921, -0.31129, -0.16675, 0, 0, 0, 0, 0, 0, -0.08391, -0.10448, 0.03126, -0.1303, -0.06111, -0.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23087, -0.04855, 0.02561, -0.23453, 0.03936, -0.36046, -0.23595, -0.16692, -0.43958, -0.048, 0, 0, 0.0179, -0.16397, 0.05062, -0.15698, 0, 0, 0, 0, 0, 0, 0, 0, -0.28259, -0.03086, -0.28259, -0.03086, -0.14856, -0.24235, -0.27055, -0.08724, -0.35224, 0.02508, -0.23543, -0.26318, -0.35006, -0.04651, -0.25119, -0.02743, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0394, -0.36074, 0.11132, -0.34538, 0.01433, -0.13118, 0.1125, -0.06896, 0.04049, -0.12559], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 24, "vertices": [-7.9553, -2.36194, -3.00136, -7.73655, -7.31525, -3.91864, 0, 0, 0, 0, 0, 0, -1.97198, -2.4553, 0.73471, -3.06194, -1.43599, -2.80266, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.42552, -1.14093, 0.60179, -5.51137, 0.92506, -8.47076, -5.54483, -3.92268, -10.33005, -1.12797, 0, 0, 0.42074, -3.85323, 1.18949, -3.6891, 0, 0, 0, 0, 0, 0, 0, 0, -6.6408, -0.72509, -6.6408, -0.72509, -3.49124, -5.6952, -6.3579, -2.05018, -8.27759, 0.58947, -5.53255, -6.18483, -8.22639, -1.09291, -5.90301, -0.64457, -5.90301, -0.64457, -3.10329, -5.06239, -5.65144, -1.8224, -5.90301, -0.64457, -3.10329, -5.06239, -5.65144, -1.8224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.92599, -8.47748, 2.61601, -8.11654, 0.33665, -3.08263, 2.64377, -1.62052, 0.95149, -2.95134], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "offset": 24, "vertices": [-0.33852, -0.10051, -0.12772, -0.32921, -0.31129, -0.16675, 0, 0, 0, 0, 0, 0, -0.08391, -0.10448, 0.03126, -0.1303, -0.06111, -0.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23087, -0.04855, 0.02561, -0.23453, 0.03936, -0.36046, -0.23595, -0.16692, -0.43958, -0.048, 0, 0, 0.0179, -0.16397, 0.05062, -0.15698, 0, 0, 0, 0, 0, 0, 0, 0, -0.28259, -0.03086, -0.28259, -0.03086, -0.14856, -0.24235, -0.27055, -0.08724, -0.35224, 0.02508, -0.23543, -0.26318, -0.35006, -0.04651, -0.25119, -0.02743, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0394, -0.36074, 0.11132, -0.34538, 0.01433, -0.13118, 0.1125, -0.06896, 0.04049, -0.12559], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "offset": 24, "vertices": [-7.9553, -2.36194, -3.00136, -7.73655, -7.31525, -3.91864, 0, 0, 0, 0, 0, 0, -1.97198, -2.4553, 0.73471, -3.06194, -1.43599, -2.80266, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.42552, -1.14093, 0.60179, -5.51137, 0.92506, -8.47076, -5.54483, -3.92268, -10.33005, -1.12797, 0, 0, 0.42074, -3.85323, 1.18949, -3.6891, 0, 0, 0, 0, 0, 0, 0, 0, -6.6408, -0.72509, -6.6408, -0.72509, -3.49124, -5.6952, -6.3579, -2.05018, -8.27759, 0.58947, -5.53255, -6.18483, -8.22639, -1.09291, -5.90301, -0.64457, -5.90301, -0.64457, -3.10329, -5.06239, -5.65144, -1.8224, -5.90301, -0.64457, -3.10329, -5.06239, -5.65144, -1.8224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.92599, -8.47748, 2.61601, -8.11654, 0.33665, -3.08263, 2.64377, -1.62052, 0.95149, -2.95134], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "offset": 24, "vertices": [-0.33852, -0.10051, -0.12772, -0.32921, -0.31129, -0.16675, 0, 0, 0, 0, 0, 0, -0.08391, -0.10448, 0.03126, -0.1303, -0.06111, -0.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23087, -0.04855, 0.02561, -0.23453, 0.03936, -0.36046, -0.23595, -0.16692, -0.43958, -0.048, 0, 0, 0.0179, -0.16397, 0.05062, -0.15698, 0, 0, 0, 0, 0, 0, 0, 0, -0.28259, -0.03086, -0.28259, -0.03086, -0.14856, -0.24235, -0.27055, -0.08724, -0.35224, 0.02508, -0.23543, -0.26318, -0.35006, -0.04651, -0.25119, -0.02743, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0394, -0.36074, 0.11132, -0.34538, 0.01433, -0.13118, 0.1125, -0.06896, 0.04049, -0.12559]}]}, "t4": {"t4": [{"curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 20, "vertices": [2.93847, 0.36981, 3.05876, -0.49347, 3.05876, -0.49347, 3.05876, -0.49347, 3.05876, -0.49347, 3.05876, -0.49347, 2.93847, 0.36981, 2.93847, 0.36981, 0, 0, 0, 0, 0, 0, 4.84141, 0.42664, 4.25396, 0.30115], "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "vertices": [2.10067, 9.29126, 1.61528, 7.54437, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.54664, 3.95801, 0.80325, 6.35944, -0.60095, 10.58679, 0.8122, 4.32141, -2.01556, 0.95795, -1.92178, -2.52826, -0.06393, -1.20886, 0.79285, 2.75806, 0.52771, 4.1839, 0.95454, 11.06836, -0.20436, 12.84546, -1.81922, 7.44684, -1.15986, 11.96362, 0.63502, 12.0766, 1.43684, 9.27368, 2.32929, 12.50177, -1.18621, -3.43671, -1.09255, -5.49414, -0.20629, -3.72534], "curve": 0.25, "c3": 0.75}, {"time": 4.8333}]}, "t3": {"t3": [{"curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 20, "vertices": [-3.94296, 0.49988, -2.4281, 0.11523, -2.4281, 0.11523, -2.87051, 1.01038, 0, 0, 0, 0, -2.09412, -0.45319], "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "vertices": [-0.65472, -6.99591, 0, 0, 0, 0, -0.36868, 2.5047, -0.36868, 2.5047, 0, 0, 0, 0, 2.58047, -2.72003, -3.58762, -8.31702, 0.26428, -9.26117, -5.29523, -13.34723, -3.90411, -7.56708, -0.73172, -1.9502, 0.31302, 1.34991, 0, 0, -2.22467, -5.98749, -5.05984, -11.51593, -3.87897, -11.24103, -5.08307, -14.24908, -6.0965, -12.26404, -0.85608, -1.5177, -0.16858, 4.03375, 0.48279, 2.54242, 0.53833, 3.10364, 0.49039, 1.66193], "curve": 0.25, "c3": 0.75}, {"time": 4.8333}]}, "t1": {"t1": [{"offset": 6, "vertices": [0.06346, -0.06025, 0.05458, 0.16459, -0.06764, 0.43771, 0.07837, 0.07036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13282, 0.40073, 0.09567, 0.33097, 0.05925, 0.33296, 0.02629, 0.32399, 0.07839, 0.19958, 0.03901, 0.20556, -0.04905, 0.21303, -0.04469, 0.2145, 0.01399, 0.28076, 0.01313, 0.25436, 0.01313, 0.25436, 0.04843, 0.24528, 0.01313, 0.25436, 0.05141, 0.15676, 0.05113, 0.19561, 0.00464, 0.26257, -0.03857, 0.25773, -0.02429, 0.25696], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 6, "vertices": [1.49127, -1.41582, 1.28271, 3.86789, -1.58954, 10.28619, 1.84174, 1.65356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.12128, 9.41725, 2.24835, 7.77778, 1.39233, 7.82453, 0.61786, 7.61375, 1.8421, 4.69017, 0.91675, 4.83077, -1.15259, 5.00621, -1.05011, 5.04073, 0.32874, 6.59782, 0.30847, 5.97747, 0.30853, 5.97743, 1.13812, 5.76416, 0.30853, 5.97743, 2.22327, 3.6909, 1.20154, 4.59673, -0.90613, 6.16335, -0.86407, 6.05706, 0.52881, 6.04602], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "offset": 6, "vertices": [0.06346, -0.06025, 0.05458, 0.16459, -0.06764, 0.43771, 0.07837, 0.07036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13282, 0.40073, 0.09567, 0.33097, 0.05925, 0.33296, 0.02629, 0.32399, 0.07839, 0.19958, 0.03901, 0.20556, -0.04905, 0.21303, -0.04469, 0.2145, 0.01399, 0.28076, 0.01313, 0.25436, 0.01313, 0.25436, 0.04843, 0.24528, 0.01313, 0.25436, 0.05141, 0.15676, 0.05113, 0.19561, 0.00464, 0.26257, -0.03857, 0.25773, -0.02429, 0.25696], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "offset": 6, "vertices": [1.49127, -1.41582, 1.28271, 3.86789, -1.58954, 10.28619, 1.84174, 1.65356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.12128, 9.41725, 2.24835, 7.77778, 1.39233, 7.82453, 0.61786, 7.61375, 1.8421, 4.69017, 0.91675, 4.83077, -1.15259, 5.00621, -1.05011, 5.04073, 0.32874, 6.59782, 0.30847, 5.97747, 0.30853, 5.97743, 1.13812, 5.76416, 0.30853, 5.97743, 2.22327, 3.6909, 1.20154, 4.59673, -0.90613, 6.16335, -0.86407, 6.05706, 0.52881, 6.04602], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "offset": 6, "vertices": [0.06346, -0.06025, 0.05458, 0.16459, -0.06764, 0.43771, 0.07837, 0.07036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13282, 0.40073, 0.09567, 0.33097, 0.05925, 0.33296, 0.02629, 0.32399, 0.07839, 0.19958, 0.03901, 0.20556, -0.04905, 0.21303, -0.04469, 0.2145, 0.01399, 0.28076, 0.01313, 0.25436, 0.01313, 0.25436, 0.04843, 0.24528, 0.01313, 0.25436, 0.05141, 0.15676, 0.05113, 0.19561, 0.00464, 0.26257, -0.03857, 0.25773, -0.02429, 0.25696]}]}, "ys": {"ys": [{"offset": 10, "vertices": [2.0726, 0.14207, -1.21023, -1.68863, 2.31651, 0.55242, -1.04905, -2.13802, 1.9565, -0.89355, -1.9348, -0.93971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.49368, -1.53677, 0.84908, -1.37274, 1.15492, -1.12761, 1.30346, -0.95203, 1.59402, -0.67728, 1.6884, -0.38575, -0.87066, 1.35917, -0.49499, -1.54079, 0.85129, -1.37634, 1.15794, -1.13059, 1.30687, -0.95453, 1.59818, -0.67906, 1.6928, -0.38677, -0.87293, 1.36273, -0.87136, -2.71237, 1.49861, -2.42287, 2.0384, -1.99024, 2.30059, -1.68034, 2.81342, -1.1954, 2.97998, -0.68086, -0.81721, -2.54384, 1.40549, -2.27232, 1.91175, -1.86658, 2.15763, -1.57592, 2.63859, -1.12112, 2.79484, -0.63856, -1.44121, 2.24986, -0.37895, -1.17961, 0.65175, -1.0537, 0.8865, -0.86556, 1.00053, -0.73079, 1.22356, -0.51989, 1.296, -0.29611, -0.66831, 1.0433, -0.4331, -1.34814, 0.74486, -1.20425, 1.01315, -0.98923, 1.14348, -0.8352, 1.39837, -0.59416, 1.48115, -0.33842, -0.76379, 1.19235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.65444, 2.03708, -1.53094, 1.49476, -1.72784, 1.26202, -2.11304, 0.89782, -2.23823, 0.5114, 1.15405, -1.80174, 0.94552, 2.94318, -2.49639, 1.82336, -3.05293, 1.29715, -3.23376, 0.73885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.6572, -1.15185, 2.83834, 0.57302, 2.68826, 1.07751, 2.60049, 1.27467, 2.21662, 1.8638, 1.92926, 2.16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98946, -0.13178, -1.36841, -1.45015, 1.98946, -0.13178, 1.70787, 1.02862, 1.49359, 1.32084, 1.23908, -0.29708, -1.01806, -0.76629, 1.45731, -0.20135, 1.31099, 0.66748, 1.16856, 0.89379, 0.55153, 0.16903, 0.51185, 0.26609, 0.49061, 0.30346, 0.40301, 0.41276, 0.34026, 0.46585, 1.77055, 0.54264, 1.64329, 0.85425, 1.57509, 0.97423, 1.29385, 1.32511, 1.09239, 1.49557, 0.84545, 1.68109, 1.96636, -0.09648, 1.95162, 0.26116, 1.92671, 0.40575, 1.77213, 0.85813, 1.63219, 1.10131, 1.50557, 1.40061, -1.92414, 0.41769, 1.41921, 0.62639, 1.28258, 0.87302, 1.21399, 0.9661, 0.94536, 1.23018, 0.76094, 1.35208, 0.52256, 1.47524, -1.50291, -0.38513, 1.70583, 1.35496, 1.43268, 1.64146, 1.30641, 1.74357, 0.84769, 2.00702, 0.55385, 2.10715, 0.14017, 2.17488, -1.90524, -1.05682, 0.2823, -0.92148, 0.44455, -0.85513, 0.50696, -0.81965, 0.68959, -0.67331, 0.77828, -0.56847, 0.95177, -0.4044, 1.00811, -0.23033, -0.1274, 0.9553, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 0.27823, 0.93645, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 1.25909, 1.48487, 0.9697, 1.68833, 0.84127, 1.75585, 0.39326, 1.90681, 0.11829, 1.94337, 1.0003, 0.28457, 0.65821, 0.80511, 0.46769, 1.45591, -0.44809, 1.46207, 0.46769, 1.45591, 0.82477, -1.28768, 2.19456, -0.70503, -1.94101, -1.24331, 0.22531, -0.73543, 0.35479, -0.68248, 0.4046, -0.65416, 0.55036, -0.53737, 0.62114, -0.45369, 0.7596, -0.32276, 0.80457, -0.18383, -0.10168, 0.76242, 1.27964, 0.66523, 1.22655, 0.75865, 1.00754, 1.03192, 0.85065, 1.16465, 0.65838, 1.30911, 0.41736, 1.40466, 2.01826, -0.64838, 1.25033, 1.71186, -1.78506, -1.14342, 1.69155, 0.56504, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 0.78882, 1.59948, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 0.66752, 1.65377, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 1.69155, 0.56504, -0.64139, -1.66409, 1.48729, 0.45598, -0.59543, -1.43722, 0, 0, 0, 0, 0.4046, -0.65416, 0.55035, -0.53736, 0.62114, -0.45368, 0.7596, -0.32275, 0.80457, -0.18383, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, 1.3753, -0.44182, 1.00914, 1.03354, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, -1.21637, -0.77915, 0.98604, 2.19215, -1.02839, 2.17269, -1.52176, 1.86072, -1.77075, 1.62556, -2.23659, 1.24584, 1.06245, -2.15619, 1.36913, 2.06908, -0.5067, 2.4288, -0.68616, 2.38435, -1.24065, 2.14863, -1.53341, 1.9505, -2.0529, 1.61109, 0.72361, -2.37323, 1.62453, 1.98703, -0.26868, 2.55254, -0.45802, 2.52547, -1.05324, 2.34057, -1.37518, 2.16713, 0.49771, -2.51793, 1.87994, 1.90498, 0.45411, 2.63761, -0.03065, 2.67627, -0.22987, 2.66658, -0.86583, 2.53252, 0.27182, -2.66262, 2.13538, 1.82291, 0.04592, -2.80731, 1.12762, 0.87873, -0.04048, -1.42907, 0, 0, 0, 0, 0, 0, 0, 0, 0.73064, 2.27419, -1.70917, 1.66877, -1.92897, 1.40893, -2.35905, 1.00233, -2.49881, 0.57094, 1.28835, -2.01149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.54136, -1.68517, 0.81643, -1.57046, 0.93107, -1.5053, 1.26644, -1.23653, 1.42934, -1.04399, 1.74795, -0.7427, -0.95473, 1.49044, -0.05411, -0.16855, 0.08169, -0.15704, 0.09311, -0.1505, 0.12664, -0.12366, -0.48981, -1.52473, 0.73872, -1.42091, 0.84241, -1.36195, 1.14585, -1.11879, 1.29324, -0.94458, -0.70508, -2.19477, 1.06332, -2.04534, 1.21262, -1.96048, 1.64939, -1.61045, 1.86156, -1.35968, 2.2765, -0.96729, -0.27069, -0.84259, -0.47737, 0.74522, -0.6548, -2.03829, 1.12618, -1.82072, 1.53182, -1.49563, 1.72885, -1.26273, -1.1548, 1.80274, -0.97833, -3.04538, 1.68261, -2.72033, 2.28866, -2.2346, 2.58305, -1.88665, -1.72537, 2.69345, -0.27325, -0.85062, -0.48192, 0.75232, -0.77063, 1.20301, -0.70765, -2.20279, 1.65544, -1.61633, -1.24799, 1.94822, -0.65739, -2.04636, 1.13064, -1.82792, 1.53787, -1.50153, 1.73568, -1.26771, -1.15938, 1.80985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.29729, 0.0249, 0.80668, -1.01615, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, 1.20417, 0.67689, -1.29729, 0.0249], "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "offset": 10, "vertices": [8.55733, 0.58656, -4.99678, -6.972, 9.56439, 2.28084, -4.3313, -8.82741, 8.07795, -3.68929, -7.9884, -3.87985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0383, -6.345, 3.50566, -5.66776, 4.76842, -4.65567, 5.38171, -3.93073, 6.58135, -2.79633, 6.97102, -1.59268, -3.59476, 5.61171, -2.0437, -6.3616, 3.5148, -5.68262, 4.78088, -4.66795, 5.39579, -3.94104, 6.59854, -2.8037, 6.98923, -1.59691, -3.60416, 5.62643, -3.59766, -11.19881, 6.18742, -10.00349, 8.41614, -8.21729, 9.49863, -6.93776, 11.61599, -4.93553, 12.30371, -2.81113, -3.37408, -10.50298, 5.80296, -9.38193, 7.89321, -7.70671, 8.90841, -6.50665, 10.89418, -4.62888, 11.53929, -2.63646, -5.95044, 9.28919, -1.56461, -4.87036, 2.69092, -4.35051, 3.66017, -3.57373, 4.13097, -3.01727, 5.05181, -2.1465, 5.35089, -1.22258, -2.75931, 4.30756, -1.78818, -5.56619, 3.07536, -4.97208, 4.18309, -4.0843, 4.72117, -3.44835, 5.77358, -2.45317, 6.11537, -1.39725, -3.15353, 4.92298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.70204, 8.41068, -6.32094, 6.17156, -7.13388, 5.21063, -8.72431, 3.7069, -9.24116, 2.11144, 4.76481, -7.43899, 3.90386, 12.15177, -10.30704, 7.52827, -12.60487, 5.35567, -13.35151, 3.05054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.38126, -1.25197, 0.56236, -4.21362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.97099, -4.75574, 11.71891, 2.36588, 11.09925, 4.4488, 10.73688, 5.26284, 9.15197, 7.69525, 7.96551, 8.91818, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.21404, -0.54408, -5.64986, -5.98737, 8.21404, -0.54408, 7.05144, 4.24694, 6.1667, 5.45346, 5.11588, -1.22657, -4.20334, -3.16385, 6.01694, -0.83135, 5.41279, 2.75586, 4.82472, 3.69026, 2.27716, 0.69787, 2.11334, 1.09862, 2.02564, 1.25291, 1.66395, 1.7042, 1.40486, 1.92339, 7.31023, 2.24043, 6.7848, 3.52701, 6.50319, 4.0224, 5.34203, 5.47111, 4.51025, 6.17489, 3.49069, 6.94088, 8.11869, -0.39833, 8.0578, 1.07826, 7.95497, 1.67525, 7.31675, 3.54303, 6.73896, 4.54707, 6.21619, 5.78284, -7.94435, 1.72456, 5.16194, 4.8615, 4.19341, 5.71349, 3.76788, 6.0172, 2.21984, 6.76118, 1.24923, 7.02529, -0.25516, 7.15389, -5.89029, -3.94892, 7.04301, 5.59433, 5.91522, 6.77723, 5.3939, 7.19884, 3.49992, 8.28654, 2.28675, 8.69998, 0.57872, 8.97961, -7.86633, -4.36339, 1.16556, -3.80461, 1.83544, -3.53064, 2.09313, -3.38416, 2.84718, -2.77996, 3.21336, -2.34707, 3.92966, -1.66969, 4.16228, -0.95099, -0.52603, 3.94424, 3.79517, 1.16314, 3.52229, 1.83107, 3.37613, 2.08823, 2.7733, 2.84036, 2.34149, 3.20575, 1.81219, 3.60339, 1.14876, 3.86642, 3.79517, 1.16314, 3.52229, 1.83107, 3.37613, 2.08823, 2.7733, 2.84036, 2.34149, 3.20575, 1.81219, 3.60339, 5.19853, 6.13073, 4.00367, 6.97073, 3.47342, 7.24952, 1.62371, 7.87279, 0.48838, 8.02377, 4.13001, 1.17495, 2.71762, 3.32411, 1.93099, 6.01114, -1.85007, 6.03657, 1.93099, 6.01114, 3.40528, -5.31654, 9.06089, -2.91092, -8.014, -5.13335, 0.93025, -3.03645, 1.46485, -2.81781, 1.6705, -2.7009, 2.27231, -2.21868, 2.56456, -1.8732, 3.13624, -1.3326, 3.32189, -0.75901, -0.41981, 3.14789, 5.28338, 2.74657, 5.06416, 3.13231, 4.15991, 4.26057, 3.51216, 4.80858, 2.71832, 5.40505, 1.7232, 5.79956, 8.33298, -2.67703, 5.16235, 7.06789, -7.37012, -4.72093, 6.98403, 2.33293, 2.75605, 6.82808, 1.02907, 7.29086, -2.64818, -6.87068, 6.98403, 2.33293, 3.25687, 6.60392, 2.75605, 6.82808, 1.02907, 7.29086, -2.64818, -6.87068, 6.98403, 2.33293, 4.39758, 5.90512, 3.25687, 6.60392, 2.75605, 6.82808, 6.98403, 2.33293, 4.39758, 5.90512, 3.25687, 6.60392, 6.98403, 2.33293, -2.64818, -6.87068, 6.14072, 1.88263, -2.4584, -5.93399, 0, 0, 0, 0, 1.67051, -2.70088, 2.2723, -2.21863, 2.56455, -1.87316, 3.13623, -1.33256, 3.32191, -0.759, 3.51778, 4.81621, 2.7226, 5.41364, 1.72587, 5.80879, 5.67831, -1.82417, 4.16653, 4.26726, 3.51778, 4.81621, 2.7226, 5.41364, 1.72587, 5.80879, -5.02215, -3.21695, 4.07113, 9.05091, -4.24602, 8.97057, -6.28302, 7.6825, -7.31104, 6.71159, -9.23442, 5.14379, 4.38662, -8.90243, 5.65284, 8.54277, -2.09205, 10.028, -2.83302, 9.84448, -5.12236, 8.87124, -6.33112, 8.05321, -8.47599, 6.65185, 2.98762, -9.79856, 6.70733, 8.20402, -1.10932, 10.53888, -1.89107, 10.42712, -4.3486, 9.66373, -5.67784, 8.94763, 2.05494, -10.39598, 7.76186, 7.86527, 1.87491, 10.89012, -0.12656, 11.04976, -0.9491, 11.00974, -3.57483, 10.45622, 1.12227, -10.9934, 8.81653, 7.5264, 0.18961, -11.5908, 4.65569, 3.6281, -0.16712, -5.90034, 0, 0, 0, 0, 0, 0, 0, 0, 3.01665, 9.38966, -7.05679, 6.89, -7.96432, 5.81718, -9.74003, 4.13842, -10.31705, 2.35731, 5.31931, -8.305, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.23517, -6.95772, 3.37086, -6.48409, 3.84419, -6.21507, 5.22887, -5.10536, 5.90143, -4.31041, 7.21693, -3.06645, -3.94188, 6.1537, -0.22339, -0.69591, 0.33727, -0.64839, 0.38441, -0.6214, 0.52287, -0.51056, -2.02231, -6.2953, 3.05, -5.86664, 3.47815, -5.6232, 4.73097, -4.61926, 5.33952, -3.89996, -2.91113, -9.06172, 4.39023, -8.44479, 5.00665, -8.09441, 6.80998, -6.64919, 7.68597, -5.61382, 9.39918, -3.99371, -1.11761, -3.47887, -1.97096, 3.07687, -2.70352, -8.41568, 4.64977, -7.51738, 6.32456, -6.17513, 7.13804, -5.21355, -4.76793, 7.44312, -4.03931, -12.57373, 6.94713, -11.23164, 9.44938, -9.2262, 10.66484, -7.78957, -7.1237, 11.1207, -1.1282, -3.51204, -1.98973, 3.10615, -3.18175, 4.96696, -2.92172, -9.09483, 6.83494, -6.67346, -5.15268, 8.04378, -2.7142, -8.44897, 4.66815, -7.5471, 6.34953, -6.19951, 7.16624, -5.23412, -4.78682, 7.47249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.35622, 0.10279, 3.3306, -4.19545, -5.35622, 0.10279, 3.3306, -4.19545, 5.35364, -0.18742, 5.24089, 1.10843, 5.03027, 1.842, -5.35622, 0.10279, 3.3306, -4.19545, 5.35364, -0.18742, 5.24089, 1.10843, 5.03027, 1.842, 4.97175, 2.79474, -5.35622, 0.10279], "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "offset": 10, "vertices": [2.0726, 0.14207, -1.21023, -1.68863, 2.31651, 0.55242, -1.04905, -2.13802, 1.9565, -0.89355, -1.9348, -0.93971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.49368, -1.53677, 0.84908, -1.37274, 1.15492, -1.12761, 1.30346, -0.95203, 1.59402, -0.67728, 1.6884, -0.38575, -0.87066, 1.35917, -0.49499, -1.54079, 0.85129, -1.37634, 1.15794, -1.13059, 1.30687, -0.95453, 1.59818, -0.67906, 1.6928, -0.38677, -0.87293, 1.36273, -0.87136, -2.71237, 1.49861, -2.42287, 2.0384, -1.99024, 2.30059, -1.68034, 2.81342, -1.1954, 2.97998, -0.68086, -0.81721, -2.54384, 1.40549, -2.27232, 1.91175, -1.86658, 2.15763, -1.57592, 2.63859, -1.12112, 2.79484, -0.63856, -1.44121, 2.24986, -0.37895, -1.17961, 0.65175, -1.0537, 0.8865, -0.86556, 1.00053, -0.73079, 1.22356, -0.51989, 1.296, -0.29611, -0.66831, 1.0433, -0.4331, -1.34814, 0.74486, -1.20425, 1.01315, -0.98923, 1.14348, -0.8352, 1.39837, -0.59416, 1.48115, -0.33842, -0.76379, 1.19235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.65444, 2.03708, -1.53094, 1.49476, -1.72784, 1.26202, -2.11304, 0.89782, -2.23823, 0.5114, 1.15405, -1.80174, 0.94552, 2.94318, -2.49639, 1.82336, -3.05293, 1.29715, -3.23376, 0.73885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.6572, -1.15185, 2.83834, 0.57302, 2.68826, 1.07751, 2.60049, 1.27467, 2.21662, 1.8638, 1.92926, 2.16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98946, -0.13178, -1.36841, -1.45015, 1.98946, -0.13178, 1.70787, 1.02862, 1.49359, 1.32084, 1.23908, -0.29708, -1.01806, -0.76629, 1.45731, -0.20135, 1.31099, 0.66748, 1.16856, 0.89379, 0.55153, 0.16903, 0.51185, 0.26609, 0.49061, 0.30346, 0.40301, 0.41276, 0.34026, 0.46585, 1.77055, 0.54264, 1.64329, 0.85425, 1.57509, 0.97423, 1.29385, 1.32511, 1.09239, 1.49557, 0.84545, 1.68109, 1.96636, -0.09648, 1.95162, 0.26116, 1.92671, 0.40575, 1.77213, 0.85813, 1.63219, 1.10131, 1.50557, 1.40061, -1.92414, 0.41769, 1.41921, 0.62639, 1.28258, 0.87302, 1.21399, 0.9661, 0.94536, 1.23018, 0.76094, 1.35208, 0.52256, 1.47524, -1.50291, -0.38513, 1.70583, 1.35496, 1.43268, 1.64146, 1.30641, 1.74357, 0.84769, 2.00702, 0.55385, 2.10715, 0.14017, 2.17488, -1.90524, -1.05682, 0.2823, -0.92148, 0.44455, -0.85513, 0.50696, -0.81965, 0.68959, -0.67331, 0.77828, -0.56847, 0.95177, -0.4044, 1.00811, -0.23033, -0.1274, 0.9553, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 0.27823, 0.93645, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 1.25909, 1.48487, 0.9697, 1.68833, 0.84127, 1.75585, 0.39326, 1.90681, 0.11829, 1.94337, 1.0003, 0.28457, 0.65821, 0.80511, 0.46769, 1.45591, -0.44809, 1.46207, 0.46769, 1.45591, 0.82477, -1.28768, 2.19456, -0.70503, -1.94101, -1.24331, 0.22531, -0.73543, 0.35479, -0.68248, 0.4046, -0.65416, 0.55036, -0.53737, 0.62114, -0.45369, 0.7596, -0.32276, 0.80457, -0.18383, -0.10168, 0.76242, 1.27964, 0.66523, 1.22655, 0.75865, 1.00754, 1.03192, 0.85065, 1.16465, 0.65838, 1.30911, 0.41736, 1.40466, 2.01826, -0.64838, 1.25033, 1.71186, -1.78506, -1.14342, 1.69155, 0.56504, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 0.78882, 1.59948, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 0.66752, 1.65377, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 1.69155, 0.56504, -0.64139, -1.66409, 1.48729, 0.45598, -0.59543, -1.43722, 0, 0, 0, 0, 0.4046, -0.65416, 0.55035, -0.53736, 0.62114, -0.45368, 0.7596, -0.32275, 0.80457, -0.18383, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, 1.3753, -0.44182, 1.00914, 1.03354, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, -1.21637, -0.77915, 0.98604, 2.19215, -1.02839, 2.17269, -1.52176, 1.86072, -1.77075, 1.62556, -2.23659, 1.24584, 1.06245, -2.15619, 1.36913, 2.06908, -0.5067, 2.4288, -0.68616, 2.38435, -1.24065, 2.14863, -1.53341, 1.9505, -2.0529, 1.61109, 0.72361, -2.37323, 1.62453, 1.98703, -0.26868, 2.55254, -0.45802, 2.52547, -1.05324, 2.34057, -1.37518, 2.16713, 0.49771, -2.51793, 1.87994, 1.90498, 0.45411, 2.63761, -0.03065, 2.67627, -0.22987, 2.66658, -0.86583, 2.53252, 0.27182, -2.66262, 2.13538, 1.82291, 0.04592, -2.80731, 1.12762, 0.87873, -0.04048, -1.42907, 0, 0, 0, 0, 0, 0, 0, 0, 0.73064, 2.27419, -1.70917, 1.66877, -1.92897, 1.40893, -2.35905, 1.00233, -2.49881, 0.57094, 1.28835, -2.01149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.54136, -1.68517, 0.81643, -1.57046, 0.93107, -1.5053, 1.26644, -1.23653, 1.42934, -1.04399, 1.74795, -0.7427, -0.95473, 1.49044, -0.05411, -0.16855, 0.08169, -0.15704, 0.09311, -0.1505, 0.12664, -0.12366, -0.48981, -1.52473, 0.73872, -1.42091, 0.84241, -1.36195, 1.14585, -1.11879, 1.29324, -0.94458, -0.70508, -2.19477, 1.06332, -2.04534, 1.21262, -1.96048, 1.64939, -1.61045, 1.86156, -1.35968, 2.2765, -0.96729, -0.27069, -0.84259, -0.47737, 0.74522, -0.6548, -2.03829, 1.12618, -1.82072, 1.53182, -1.49563, 1.72885, -1.26273, -1.1548, 1.80274, -0.97833, -3.04538, 1.68261, -2.72033, 2.28866, -2.2346, 2.58305, -1.88665, -1.72537, 2.69345, -0.27325, -0.85062, -0.48192, 0.75232, -0.77063, 1.20301, -0.70765, -2.20279, 1.65544, -1.61633, -1.24799, 1.94822, -0.65739, -2.04636, 1.13064, -1.82792, 1.53787, -1.50153, 1.73568, -1.26771, -1.15938, 1.80985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.29729, 0.0249, 0.80668, -1.01615, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, 1.20417, 0.67689, -1.29729, 0.0249], "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "offset": 10, "vertices": [8.55733, 0.58656, -4.99678, -6.972, 9.56439, 2.28084, -4.3313, -8.82741, 8.07795, -3.68929, -7.9884, -3.87985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0383, -6.345, 3.50566, -5.66776, 4.76842, -4.65567, 5.38171, -3.93073, 6.58135, -2.79633, 6.97102, -1.59268, -3.59476, 5.61171, -2.0437, -6.3616, 3.5148, -5.68262, 4.78088, -4.66795, 5.39579, -3.94104, 6.59854, -2.8037, 6.98923, -1.59691, -3.60416, 5.62643, -3.59766, -11.19881, 6.18742, -10.00349, 8.41614, -8.21729, 9.49863, -6.93776, 11.61599, -4.93553, 12.30371, -2.81113, -3.37408, -10.50298, 5.80296, -9.38193, 7.89321, -7.70671, 8.90841, -6.50665, 10.89418, -4.62888, 11.53929, -2.63646, -5.95044, 9.28919, -1.56461, -4.87036, 2.69092, -4.35051, 3.66017, -3.57373, 4.13097, -3.01727, 5.05181, -2.1465, 5.35089, -1.22258, -2.75931, 4.30756, -1.78818, -5.56619, 3.07536, -4.97208, 4.18309, -4.0843, 4.72117, -3.44835, 5.77358, -2.45317, 6.11537, -1.39725, -3.15353, 4.92298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.70204, 8.41068, -6.32094, 6.17156, -7.13388, 5.21063, -8.72431, 3.7069, -9.24116, 2.11144, 4.76481, -7.43899, 3.90386, 12.15177, -10.30704, 7.52827, -12.60487, 5.35567, -13.35151, 3.05054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.38126, -1.25197, 0.56236, -4.21362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.97099, -4.75574, 11.71891, 2.36588, 11.09925, 4.4488, 10.73688, 5.26284, 9.15197, 7.69525, 7.96551, 8.91818, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.21404, -0.54408, -5.64986, -5.98737, 8.21404, -0.54408, 7.05144, 4.24694, 6.1667, 5.45346, 5.11588, -1.22657, -4.20334, -3.16385, 6.01694, -0.83135, 5.41279, 2.75586, 4.82472, 3.69026, 2.27716, 0.69787, 2.11334, 1.09862, 2.02564, 1.25291, 1.66395, 1.7042, 1.40486, 1.92339, 7.31023, 2.24043, 6.7848, 3.52701, 6.50319, 4.0224, 5.34203, 5.47111, 4.51025, 6.17489, 3.49069, 6.94088, 8.11869, -0.39833, 8.0578, 1.07826, 7.95497, 1.67525, 7.31675, 3.54303, 6.73896, 4.54707, 6.21619, 5.78284, -7.94435, 1.72456, 5.16194, 4.8615, 4.19341, 5.71349, 3.76788, 6.0172, 2.21984, 6.76118, 1.24923, 7.02529, -0.25516, 7.15389, -5.89029, -3.94892, 7.04301, 5.59433, 5.91522, 6.77723, 5.3939, 7.19884, 3.49992, 8.28654, 2.28675, 8.69998, 0.57872, 8.97961, -7.86633, -4.36339, 1.16556, -3.80461, 1.83544, -3.53064, 2.09313, -3.38416, 2.84718, -2.77996, 3.21336, -2.34707, 3.92966, -1.66969, 4.16228, -0.95099, -0.52603, 3.94424, 3.79517, 1.16314, 3.52229, 1.83107, 3.37613, 2.08823, 2.7733, 2.84036, 2.34149, 3.20575, 1.81219, 3.60339, 1.14876, 3.86642, 3.79517, 1.16314, 3.52229, 1.83107, 3.37613, 2.08823, 2.7733, 2.84036, 2.34149, 3.20575, 1.81219, 3.60339, 5.19853, 6.13073, 4.00367, 6.97073, 3.47342, 7.24952, 1.62371, 7.87279, 0.48838, 8.02377, 4.13001, 1.17495, 2.71762, 3.32411, 1.93099, 6.01114, -1.85007, 6.03657, 1.93099, 6.01114, 3.40528, -5.31654, 9.06089, -2.91092, -8.014, -5.13335, 0.93025, -3.03645, 1.46485, -2.81781, 1.6705, -2.7009, 2.27231, -2.21868, 2.56456, -1.8732, 3.13624, -1.3326, 3.32189, -0.75901, -0.41981, 3.14789, 5.28338, 2.74657, 5.06416, 3.13231, 4.15991, 4.26057, 3.51216, 4.80858, 2.71832, 5.40505, 1.7232, 5.79956, 8.33298, -2.67703, 5.16235, 7.06789, -7.37012, -4.72093, 6.98403, 2.33293, 2.75605, 6.82808, 1.02907, 7.29086, -2.64818, -6.87068, 6.98403, 2.33293, 3.25687, 6.60392, 2.75605, 6.82808, 1.02907, 7.29086, -2.64818, -6.87068, 6.98403, 2.33293, 4.39758, 5.90512, 3.25687, 6.60392, 2.75605, 6.82808, 6.98403, 2.33293, 4.39758, 5.90512, 3.25687, 6.60392, 6.98403, 2.33293, -2.64818, -6.87068, 6.14072, 1.88263, -2.4584, -5.93399, 0, 0, 0, 0, 1.67051, -2.70088, 2.2723, -2.21863, 2.56455, -1.87316, 3.13623, -1.33256, 3.32191, -0.759, 3.51778, 4.81621, 2.7226, 5.41364, 1.72587, 5.80879, 5.67831, -1.82417, 4.16653, 4.26726, 3.51778, 4.81621, 2.7226, 5.41364, 1.72587, 5.80879, -5.02215, -3.21695, 4.07113, 9.05091, -4.24602, 8.97057, -6.28302, 7.6825, -7.31104, 6.71159, -9.23442, 5.14379, 4.38662, -8.90243, 5.65284, 8.54277, -2.09205, 10.028, -2.83302, 9.84448, -5.12236, 8.87124, -6.33112, 8.05321, -8.47599, 6.65185, 2.98762, -9.79856, 6.70733, 8.20402, -1.10932, 10.53888, -1.89107, 10.42712, -4.3486, 9.66373, -5.67784, 8.94763, 2.05494, -10.39598, 7.76186, 7.86527, 1.87491, 10.89012, -0.12656, 11.04976, -0.9491, 11.00974, -3.57483, 10.45622, 1.12227, -10.9934, 8.81653, 7.5264, 0.18961, -11.5908, 4.65569, 3.6281, -0.16712, -5.90034, 0, 0, 0, 0, 0, 0, 0, 0, 3.01665, 9.38966, -7.05679, 6.89, -7.96432, 5.81718, -9.74003, 4.13842, -10.31705, 2.35731, 5.31931, -8.305, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.23517, -6.95772, 3.37086, -6.48409, 3.84419, -6.21507, 5.22887, -5.10536, 5.90143, -4.31041, 7.21693, -3.06645, -3.94188, 6.1537, -0.22339, -0.69591, 0.33727, -0.64839, 0.38441, -0.6214, 0.52287, -0.51056, -2.02231, -6.2953, 3.05, -5.86664, 3.47815, -5.6232, 4.73097, -4.61926, 5.33952, -3.89996, -2.91113, -9.06172, 4.39023, -8.44479, 5.00665, -8.09441, 6.80998, -6.64919, 7.68597, -5.61382, 9.39918, -3.99371, -1.11761, -3.47887, -1.97096, 3.07687, -2.70352, -8.41568, 4.64977, -7.51738, 6.32456, -6.17513, 7.13804, -5.21355, -4.76793, 7.44312, -4.03931, -12.57373, 6.94713, -11.23164, 9.44938, -9.2262, 10.66484, -7.78957, -7.1237, 11.1207, -1.1282, -3.51204, -1.98973, 3.10615, -3.18175, 4.96696, -2.92172, -9.09483, 6.83494, -6.67346, -5.15268, 8.04378, -2.7142, -8.44897, 4.66815, -7.5471, 6.34953, -6.19951, 7.16624, -5.23412, -4.78682, 7.47249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.35622, 0.10279, 3.3306, -4.19545, -5.35622, 0.10279, 3.3306, -4.19545, 5.35364, -0.18742, 5.24089, 1.10843, 5.03027, 1.842, -5.35622, 0.10279, 3.3306, -4.19545, 5.35364, -0.18742, 5.24089, 1.10843, 5.03027, 1.842, 4.97175, 2.79474, -5.35622, 0.10279], "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "offset": 10, "vertices": [2.0726, 0.14207, -1.21023, -1.68863, 2.31651, 0.55242, -1.04905, -2.13802, 1.9565, -0.89355, -1.9348, -0.93971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.49368, -1.53677, 0.84908, -1.37274, 1.15492, -1.12761, 1.30346, -0.95203, 1.59402, -0.67728, 1.6884, -0.38575, -0.87066, 1.35917, -0.49499, -1.54079, 0.85129, -1.37634, 1.15794, -1.13059, 1.30687, -0.95453, 1.59818, -0.67906, 1.6928, -0.38677, -0.87293, 1.36273, -0.87136, -2.71237, 1.49861, -2.42287, 2.0384, -1.99024, 2.30059, -1.68034, 2.81342, -1.1954, 2.97998, -0.68086, -0.81721, -2.54384, 1.40549, -2.27232, 1.91175, -1.86658, 2.15763, -1.57592, 2.63859, -1.12112, 2.79484, -0.63856, -1.44121, 2.24986, -0.37895, -1.17961, 0.65175, -1.0537, 0.8865, -0.86556, 1.00053, -0.73079, 1.22356, -0.51989, 1.296, -0.29611, -0.66831, 1.0433, -0.4331, -1.34814, 0.74486, -1.20425, 1.01315, -0.98923, 1.14348, -0.8352, 1.39837, -0.59416, 1.48115, -0.33842, -0.76379, 1.19235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.65444, 2.03708, -1.53094, 1.49476, -1.72784, 1.26202, -2.11304, 0.89782, -2.23823, 0.5114, 1.15405, -1.80174, 0.94552, 2.94318, -2.49639, 1.82336, -3.05293, 1.29715, -3.23376, 0.73885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.6572, -1.15185, 2.83834, 0.57302, 2.68826, 1.07751, 2.60049, 1.27467, 2.21662, 1.8638, 1.92926, 2.16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98946, -0.13178, -1.36841, -1.45015, 1.98946, -0.13178, 1.70787, 1.02862, 1.49359, 1.32084, 1.23908, -0.29708, -1.01806, -0.76629, 1.45731, -0.20135, 1.31099, 0.66748, 1.16856, 0.89379, 0.55153, 0.16903, 0.51185, 0.26609, 0.49061, 0.30346, 0.40301, 0.41276, 0.34026, 0.46585, 1.77055, 0.54264, 1.64329, 0.85425, 1.57509, 0.97423, 1.29385, 1.32511, 1.09239, 1.49557, 0.84545, 1.68109, 1.96636, -0.09648, 1.95162, 0.26116, 1.92671, 0.40575, 1.77213, 0.85813, 1.63219, 1.10131, 1.50557, 1.40061, -1.92414, 0.41769, 1.41921, 0.62639, 1.28258, 0.87302, 1.21399, 0.9661, 0.94536, 1.23018, 0.76094, 1.35208, 0.52256, 1.47524, -1.50291, -0.38513, 1.70583, 1.35496, 1.43268, 1.64146, 1.30641, 1.74357, 0.84769, 2.00702, 0.55385, 2.10715, 0.14017, 2.17488, -1.90524, -1.05682, 0.2823, -0.92148, 0.44455, -0.85513, 0.50696, -0.81965, 0.68959, -0.67331, 0.77828, -0.56847, 0.95177, -0.4044, 1.00811, -0.23033, -0.1274, 0.9553, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 0.27823, 0.93645, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 1.25909, 1.48487, 0.9697, 1.68833, 0.84127, 1.75585, 0.39326, 1.90681, 0.11829, 1.94337, 1.0003, 0.28457, 0.65821, 0.80511, 0.46769, 1.45591, -0.44809, 1.46207, 0.46769, 1.45591, 0.82477, -1.28768, 2.19456, -0.70503, -1.94101, -1.24331, 0.22531, -0.73543, 0.35479, -0.68248, 0.4046, -0.65416, 0.55036, -0.53737, 0.62114, -0.45369, 0.7596, -0.32276, 0.80457, -0.18383, -0.10168, 0.76242, 1.27964, 0.66523, 1.22655, 0.75865, 1.00754, 1.03192, 0.85065, 1.16465, 0.65838, 1.30911, 0.41736, 1.40466, 2.01826, -0.64838, 1.25033, 1.71186, -1.78506, -1.14342, 1.69155, 0.56504, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 0.78882, 1.59948, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 0.66752, 1.65377, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 1.69155, 0.56504, -0.64139, -1.66409, 1.48729, 0.45598, -0.59543, -1.43722, 0, 0, 0, 0, 0.4046, -0.65416, 0.55035, -0.53736, 0.62114, -0.45368, 0.7596, -0.32275, 0.80457, -0.18383, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, 1.3753, -0.44182, 1.00914, 1.03354, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, -1.21637, -0.77915, 0.98604, 2.19215, -1.02839, 2.17269, -1.52176, 1.86072, -1.77075, 1.62556, -2.23659, 1.24584, 1.06245, -2.15619, 1.36913, 2.06908, -0.5067, 2.4288, -0.68616, 2.38435, -1.24065, 2.14863, -1.53341, 1.9505, -2.0529, 1.61109, 0.72361, -2.37323, 1.62453, 1.98703, -0.26868, 2.55254, -0.45802, 2.52547, -1.05324, 2.34057, -1.37518, 2.16713, 0.49771, -2.51793, 1.87994, 1.90498, 0.45411, 2.63761, -0.03065, 2.67627, -0.22987, 2.66658, -0.86583, 2.53252, 0.27182, -2.66262, 2.13538, 1.82291, 0.04592, -2.80731, 1.12762, 0.87873, -0.04048, -1.42907, 0, 0, 0, 0, 0, 0, 0, 0, 0.73064, 2.27419, -1.70917, 1.66877, -1.92897, 1.40893, -2.35905, 1.00233, -2.49881, 0.57094, 1.28835, -2.01149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.54136, -1.68517, 0.81643, -1.57046, 0.93107, -1.5053, 1.26644, -1.23653, 1.42934, -1.04399, 1.74795, -0.7427, -0.95473, 1.49044, -0.05411, -0.16855, 0.08169, -0.15704, 0.09311, -0.1505, 0.12664, -0.12366, -0.48981, -1.52473, 0.73872, -1.42091, 0.84241, -1.36195, 1.14585, -1.11879, 1.29324, -0.94458, -0.70508, -2.19477, 1.06332, -2.04534, 1.21262, -1.96048, 1.64939, -1.61045, 1.86156, -1.35968, 2.2765, -0.96729, -0.27069, -0.84259, -0.47737, 0.74522, -0.6548, -2.03829, 1.12618, -1.82072, 1.53182, -1.49563, 1.72885, -1.26273, -1.1548, 1.80274, -0.97833, -3.04538, 1.68261, -2.72033, 2.28866, -2.2346, 2.58305, -1.88665, -1.72537, 2.69345, -0.27325, -0.85062, -0.48192, 0.75232, -0.77063, 1.20301, -0.70765, -2.20279, 1.65544, -1.61633, -1.24799, 1.94822, -0.65739, -2.04636, 1.13064, -1.82792, 1.53787, -1.50153, 1.73568, -1.26771, -1.15938, 1.80985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.29729, 0.0249, 0.80668, -1.01615, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, 1.20417, 0.67689, -1.29729, 0.0249]}]}}}}, "animation2": {"slots": {"sh10": {"color": [{"color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "color": "ffffffff"}, {"time": 5.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "color": "ffffff7f"}], "attachment": [{"name": null}]}, "sh8": {"color": [{"color": "ffffffa5"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff3f"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffa5"}, {"time": 3.9, "color": "ffffffff"}, {"time": 5.1667, "color": "ffffff3f"}, {"time": 5.5667, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffffa5"}], "attachment": [{"name": null}]}, "sh12": {"color": [{"color": "ffffffb8"}, {"time": 0.4667, "color": "ffffffff", "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1.9, "color": "ffffff10"}, {"time": 2.1, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffb8"}, {"time": 3.8, "color": "ffffffff", "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.2333, "color": "ffffff10"}, {"time": 5.4333, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffffb8"}], "attachment": [{"name": null}]}, "sh11": {"color": [{"color": "ffffffc0", "curve": 0.331, "c2": 0.33, "c3": 0.716, "c4": 0.83}, {"time": 0.9667, "color": "ffffff10"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 2.8333, "color": "ffffffff", "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "color": "ffffffc0", "curve": 0.331, "c2": 0.33, "c3": 0.716, "c4": 0.83}, {"time": 4.3, "color": "ffffff10"}, {"time": 4.5, "color": "ffffff00"}, {"time": 6.1667, "color": "ffffffff", "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "color": "ffffffc0"}], "attachment": [{"name": null}]}, "sh2": {"color": [{"color": "ffffff3f"}, {"time": 0.4, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff3f"}, {"time": 3.7333, "color": "ffffff00"}, {"time": 5.4, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff3f"}], "attachment": [{"name": null}]}, "sh7": {"color": [{"color": "ffffffcc"}, {"time": 0.9, "color": "ffffff3f"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 3, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffcc"}, {"time": 4.2333, "color": "ffffff3f"}, {"time": 4.6667, "color": "ffffff00"}, {"time": 6.3333, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffffcc"}], "attachment": [{"name": null}]}, "sh3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "color": "ffffff00"}], "attachment": [{"name": null}]}, "sh1": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 5, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff00"}], "attachment": [{"name": null}]}, "sh4": {"color": [{"color": "ffffff10", "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffffff", "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3333, "color": "ffffff10", "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5333, "color": "ffffff00"}, {"time": 5.2, "color": "ffffffff", "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "color": "ffffff10"}], "attachment": [{"name": null}]}, "bg": {"attachment": [{"name": null}]}, "sh9": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "color": "ffffffff"}], "attachment": [{"name": null}]}, "sh5": {"color": [{"color": "ffffff9f"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff9f"}, {"time": 4.3667, "color": "ffffff00"}, {"time": 6.0333, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff9f"}], "attachment": [{"name": null}]}, "sh6": {"color": [{"color": "ffffffbf"}, {"time": 0.4, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffbf"}, {"time": 3.7333, "color": "ffffffff"}, {"time": 5.4, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffffbf"}], "attachment": [{"name": null}]}}, "bones": {"st": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.82, "y": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.82, "y": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "st2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys": {"rotate": [{"angle": 0.23, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 2.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3333, "angle": 0.23, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 2.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": 0.23}], "translate": [{"x": -0.28, "y": 0.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -7.86, "y": 10.76, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3333, "x": -0.28, "y": 0.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "x": -7.86, "y": 10.76, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "x": -0.28, "y": 0.22}]}, "ys3": {"rotate": [{"angle": 0.41, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2333, "angle": -10.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.9, "angle": 3.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "angle": 0.41, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5667, "angle": -10.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 6.2333, "angle": 3.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 0.41}], "scale": [{"x": 0.996, "y": 0.996, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2333, "x": 0.98, "y": 0.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "x": 0.996, "y": 0.996, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5667, "x": 0.98, "y": 0.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 6.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "x": 0.996, "y": 0.996}]}, "st4": {"rotate": [{"angle": 0.05, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 7.2, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.2667, "angle": 0.2, "curve": 0.344, "c2": 0.44, "c3": 0.677, "c4": 0.77}, {"time": 3.3333, "angle": 0.05, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 7.2, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 6.6, "angle": 0.2, "curve": 0.344, "c2": 0.44, "c3": 0.677, "c4": 0.77}, {"time": 6.6667, "angle": 0.05}]}, "t1": {"rotate": [{"angle": 0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": 0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": 0.05}], "translate": [{"x": -0.08, "y": -0.03, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.88, "y": -0.69, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": -0.08, "y": -0.03, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -1.88, "y": -0.69, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.08, "y": -0.03}], "shear": [{"x": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -2.4, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -2.4, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.05}]}, "ys7": {"rotate": [{"angle": -5.73, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -14.92, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 3.3333, "angle": -5.73, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 4.2333, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": -14.92, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 6.6667, "angle": -5.73}]}, "ys8": {"rotate": [{"angle": -7.04, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -12.52, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": -7.04, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "angle": -12.52, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 6.6667, "angle": -7.04}]}, "ys4": {"rotate": [{"angle": -4.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -6, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.5667, "angle": -1.83, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.1667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -4.55, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -6, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4.9, "angle": -1.83, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 5.5, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -4.55}]}, "ys2": {"rotate": [{"angle": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3333, "angle": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": -0.44}]}, "st3": {"translate": [{"x": 0.66, "y": -0.34, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 6.4, "y": -3.32, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 3.3333, "x": 0.66, "y": -0.34, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": 6.4, "y": -3.32, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 6.6667, "x": 0.66, "y": -0.34}]}, "t26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t29": {"rotate": [{"angle": -0.15, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.3667, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 5.05, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "angle": -0.15, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 3.7, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 5.05, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 6.6667, "angle": -0.15}]}, "t30": {"rotate": [{"angle": 1.15, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.6, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 5.95, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 3.3333, "angle": 1.15, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 3.9333, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 5.95, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 6.6667, "angle": 1.15}]}, "t31": {"rotate": [{"angle": 2.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 6.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 2.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 6.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 2.81}]}, "t32": {"rotate": [{"angle": 5.48, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 1.0667, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 8.53, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 3.3333, "angle": 5.48, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 4.4, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "angle": 8.53, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 6.6667, "angle": 5.48}]}, "t33": {"rotate": [{"angle": 5.22, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 1.3, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 6.35, "curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 3.3333, "angle": 5.22, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 4.6333, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": 6.35, "curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 6.6667, "angle": 5.22}]}, "t15": {"rotate": [{"angle": -1.58, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 4.8, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3333, "angle": -1.58, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9667, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 4.8, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": -1.58}]}, "t4": {"translate": [{"x": 0.09, "y": -2.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "x": 0.02, "y": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.8, "y": 2.81, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 0.09, "y": -2.41, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "x": 0.02, "y": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.8, "y": 2.81, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 0.09, "y": -2.41}], "scale": [{"x": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 0.998}]}, "t3": {"rotate": [{"angle": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.05}], "translate": [{"y": -2.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "x": 0.02, "y": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -0.37, "y": 2.94, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "y": -2.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "x": 0.02, "y": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -0.37, "y": 2.94, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "y": -2.4}], "scale": [{"x": 1.002, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.04, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 1.002, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.04, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 1.002}]}, "t6": {"rotate": [{"angle": -0.11, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -2.49, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": -0.11, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -2.49, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.11}], "translate": [{"x": -0.08, "y": 0.07, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.8, "y": 1.63, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": -0.08, "y": 0.07, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -1.8, "y": 1.63, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.08, "y": 0.07}], "scale": [{"x": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.96, "y": 0.99, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.96, "y": 0.99, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 0.998}]}, "t8": {"shear": [{"x": 0.05, "y": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.2, "y": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "x": 0.05, "y": -0.05, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.2, "y": -1.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": 0.05, "y": -0.05}]}, "zs": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 6.86, "y": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 6.86, "y": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys11": {"rotate": [{"angle": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": -0.72}]}, "ys12": {"rotate": [{"angle": -0.84, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 0.49, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": -0.84, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 0.49, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.84}]}, "ys13": {"rotate": [{"angle": -0.56, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 0.49, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -0.56, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 0.49, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.56}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys14": {"rotate": [{"angle": -0.39, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": -0.39, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 0.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "angle": -0.39}]}, "ys15": {"rotate": [{"angle": -0.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 0.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -0.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 0.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -0.21}]}, "ys16": {"rotate": [{"angle": -0.02, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "angle": -0.02, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.3333, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 0.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -0.02}], "scale": [{"x": 0.96, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3333, "x": 0.96, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "x": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "x": 0.96}]}, "ys17": {"rotate": [{"angle": 0.15, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 0.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 0.15, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 0.49, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 0.15}]}, "zs3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "sh4": {"translate": [{"x": 68.12, "y": -68.66}, {"time": 0.1667, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 0.2}, {"time": 3.3333, "x": 68.12, "y": -68.66}, {"time": 3.5, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 3.5333}, {"time": 6.6667, "x": 68.12, "y": -68.66}], "scale": [{"x": 1.57, "y": 1.342}, {"time": 0.1667, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 0.2}, {"time": 3.3333, "x": 1.57, "y": 1.342}, {"time": 3.5, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 3.5333}, {"time": 6.6667, "x": 1.57, "y": 1.342}]}, "sh3": {"translate": [{}, {"time": 3.2667, "y": -55.87, "curve": "stepped"}, {"time": 3.3333}, {"time": 6.6, "y": -55.87, "curve": "stepped"}, {"time": 6.6667}], "scale": [{}, {"time": 3.2667, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 3.3333}, {"time": 6.6, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 6.6667}]}, "bone": {"translate": [{}, {"time": 1.6667, "x": 0.59, "y": -13.99}, {"time": 3.3333}, {"time": 5, "x": 0.59, "y": -13.99}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "sh2": {"translate": [{"x": 42.94, "y": 35.78}, {"time": 0.3667, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 0.4}, {"time": 3.3333, "x": 42.94, "y": 35.78}, {"time": 3.7, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 3.7333}, {"time": 6.6667, "x": 42.94, "y": 35.78}], "scale": [{"x": 0.947, "y": 1.425}, {"time": 0.3667, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 0.4}, {"time": 3.3333, "x": 0.947, "y": 1.425}, {"time": 3.7, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 3.7333}, {"time": 6.6667, "x": 0.947, "y": 1.425}]}, "sh1": {"translate": [{}, {"time": 3.2667, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 3.3333}, {"time": 6.6, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 6.6667}], "scale": [{}, {"time": 3.2667, "y": 1.44, "curve": "stepped"}, {"time": 3.3333}, {"time": 6.6, "y": 1.44, "curve": "stepped"}, {"time": 6.6667}]}, "ys5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -13.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys10": {"rotate": [{"angle": -2.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -8.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -2.03, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -8.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -2.03}]}, "t2": {"rotate": [{"angle": -0.78, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -2.9, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.3333, "angle": -0.78, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 3.4667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": -2.9, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 6.6667, "angle": -0.78}]}, "t14": {"rotate": [{"angle": -0.66, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 0.7, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "angle": -0.66, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 0.7, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.66}]}, "t16": {"rotate": [{"angle": -0.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -0.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": -0.54}]}, "t17": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 4.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 0.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 4.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": 0.49}]}, "t18": {"rotate": [{"angle": 0.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": 0.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 3.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "angle": 0.68}]}, "t19": {"rotate": [{"angle": 1.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 3.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 1.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 3.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 1.19}]}, "t20": {"rotate": [{"angle": 1.69, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "angle": 1.69, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.3333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": 1.69}]}, "t21": {"rotate": [{"angle": 2.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.1667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 3.1, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3333, "angle": 2.18, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 3.1, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 2.18}]}, "t22": {"rotate": [{"angle": 3.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.3333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 4.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 3.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.6667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 4.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.6667, "angle": 3.65}]}, "t23": {"rotate": [{"angle": 2.94, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 3.1, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.3333, "angle": 2.94, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.8333, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 3.1, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6.6667, "angle": 2.94}]}, "t24": {"rotate": [{"angle": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -0.72, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 5.5}]}, "sh5": {"translate": [{"x": -56.23, "y": 16.87}, {"time": 1, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 1.0333}, {"time": 3.3333, "x": -56.23, "y": 16.87}, {"time": 4.3333, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 4.3667}, {"time": 6.6667, "x": -56.23, "y": 16.87}], "scale": [{"y": 1.306}, {"time": 1, "y": 1.44, "curve": "stepped"}, {"time": 1.0333}, {"time": 3.3333, "y": 1.306}, {"time": 4.3333, "y": 1.44, "curve": "stepped"}, {"time": 4.3667}, {"time": 6.6667, "y": 1.306}]}, "sh6": {"translate": [{"x": -30.67, "y": 9.2}, {"time": 2.0333, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 2.0667}, {"time": 3.3333, "x": -30.67, "y": 9.2}, {"time": 5.3667, "x": -80.76, "y": 24.23, "curve": "stepped"}, {"time": 5.4}, {"time": 6.6667, "x": -30.67, "y": 9.2}], "scale": [{"y": 1.167}, {"time": 2.0333, "y": 1.44, "curve": "stepped"}, {"time": 2.0667}, {"time": 3.3333, "y": 1.167}, {"time": 5.3667, "y": 1.44, "curve": "stepped"}, {"time": 5.4}, {"time": 6.6667, "y": 1.167}]}, "sh7": {"translate": [{"x": 29.44, "y": 24.53}, {"time": 0.9, "x": 42.94, "y": 35.78}, {"time": 1.3, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 1.3333}, {"time": 3.3333, "x": 29.44, "y": 24.53}, {"time": 4.2333, "x": 42.94, "y": 35.78}, {"time": 4.6333, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 4.6667}, {"time": 6.6667, "x": 29.44, "y": 24.53}], "scale": [{"x": 0.964, "y": 1.292}, {"time": 0.9, "x": 0.947, "y": 1.425}, {"time": 1.3, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 1.3333}, {"time": 3.3333, "x": 0.964, "y": 1.292}, {"time": 4.2333, "x": 0.947, "y": 1.425}, {"time": 4.6333, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 4.6667}, {"time": 6.6667, "x": 0.964, "y": 1.292}]}, "sh8": {"translate": [{"x": 15.95, "y": 13.29}, {"time": 1.8333, "x": 42.94, "y": 35.78}, {"time": 2.2, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 2.2333}, {"time": 3.3333, "x": 15.95, "y": 13.29}, {"time": 5.1667, "x": 42.94, "y": 35.78}, {"time": 5.5333, "x": 48.46, "y": 40.38, "curve": "stepped"}, {"time": 5.5667}, {"time": 6.6667, "x": 15.95, "y": 13.29}], "scale": [{"x": 0.98, "y": 1.158}, {"time": 1.8333, "x": 0.947, "y": 1.425}, {"time": 2.2, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 2.2333}, {"time": 3.3333, "x": 0.98, "y": 1.158}, {"time": 5.1667, "x": 0.947, "y": 1.425}, {"time": 5.5333, "x": 0.94, "y": 1.48, "curve": "stepped"}, {"time": 5.5667}, {"time": 6.6667, "x": 0.98, "y": 1.158}]}, "sh9": {"translate": [{"y": -28.29}, {"time": 1.6333, "y": -55.87, "curve": "stepped"}, {"time": 1.6667}, {"time": 3.3333, "y": -28.29}, {"time": 4.9667, "y": -55.87, "curve": "stepped"}, {"time": 5}, {"time": 6.6667, "y": -28.29}], "scale": [{"x": 1.304, "y": 1.213}, {"time": 1.6333, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 1.6667}, {"time": 3.3333, "x": 1.304, "y": 1.213}, {"time": 4.9667, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 5}, {"time": 6.6667, "x": 1.304, "y": 1.213}]}, "sh10": {"translate": [{"y": -14.14}, {"time": 0.8333, "y": -28.29}, {"time": 2.4333, "y": -55.87, "curve": "stepped"}, {"time": 2.5}, {"time": 3.3333, "y": -14.14}, {"time": 4.1667, "y": -28.29}, {"time": 5.7667, "y": -55.87, "curve": "stepped"}, {"time": 5.8333}, {"time": 6.6667, "y": -14.14}], "scale": [{"x": 1.152, "y": 1.106}, {"time": 0.8333, "x": 1.304, "y": 1.213}, {"time": 2.4333, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 2.5}, {"time": 3.3333, "x": 1.152, "y": 1.106}, {"time": 4.1667, "x": 1.304, "y": 1.213}, {"time": 5.7667, "x": 1.6, "y": 1.42, "curve": "stepped"}, {"time": 5.8333}, {"time": 6.6667, "x": 1.152, "y": 1.106}]}, "sh11": {"translate": [{"x": 47.23, "y": -47.61}, {"time": 0.9667, "x": 68.12, "y": -68.66}, {"time": 1.1333, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 1.1667}, {"time": 3.3333, "x": 47.23, "y": -47.61}, {"time": 4.3, "x": 68.12, "y": -68.66}, {"time": 4.4667, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 4.5}, {"time": 6.6667, "x": 47.23, "y": -47.61}], "scale": [{"x": 1.395, "y": 1.237}, {"time": 0.9667, "x": 1.57, "y": 1.342}, {"time": 1.1333, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 1.1667}, {"time": 3.3333, "x": 1.395, "y": 1.237}, {"time": 4.3, "x": 1.57, "y": 1.342}, {"time": 4.4667, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 4.5}, {"time": 6.6667, "x": 1.395, "y": 1.237}]}, "sh12": {"translate": [{"x": 26.34, "y": -26.55}, {"time": 1.9, "x": 68.12, "y": -68.66}, {"time": 2.0667, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 2.1}, {"time": 3.3333, "x": 26.34, "y": -26.55}, {"time": 5.2333, "x": 68.12, "y": -68.66}, {"time": 5.4, "x": 71.75, "y": -72.33, "curve": "stepped"}, {"time": 5.4333}, {"time": 6.6667, "x": 26.34, "y": -26.55}], "scale": [{"x": 1.22, "y": 1.132}, {"time": 1.9, "x": 1.57, "y": 1.342}, {"time": 2.0667, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 2.1}, {"time": 3.3333, "x": 1.22, "y": 1.132}, {"time": 5.2333, "x": 1.57, "y": 1.342}, {"time": 5.4, "x": 1.6, "y": 1.36, "curve": "stepped"}, {"time": 5.4333}, {"time": 6.6667, "x": 1.22, "y": 1.132}]}, "ys9": {"rotate": [{"angle": 11, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "angle": 12, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3333, "angle": 11, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.5667, "angle": 12, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": 11}]}}, "deform": {"default": {"t5": {"t5": [{"vertices": [-0.11057, 0.14563, 0.09609, 0.15968, 0.13618, 0.12742, -0.16779, -0.07255, 0.03504, -0.17941, -0.11177, 0.1472, 0.09699, 0.16135, -0.16967, -0.07328, 0.0354, -0.18139, -0.32749, -0.01567, -0.14586, 0.29956, -0.06315, -0.32174, 0.30086, -0.13032, -0.572, 0.05794, -0.1727, 0.55629, -0.19314, -0.5415, 0.48785, -0.30416, -0.96168, -0.04745, -0.429, 0.87957, -0.18411, -0.94514, 0.88409, -0.38133, 0.94343, 0.19172, -1.0322, -0.09455, -0.50223, 0.92723, -0.15528, -1.02487, 0.96813, -0.3702, 1.00605, 0.24891, -0.96168, -0.04744, -0.1841, -0.94515, 0.8841, -0.38132, 0.94344, 0.19172, -0.35378, -1.2223, 1.11866, -0.60623, 1.26442, 0.1411, 0.65312, -0.35577, 0.73923, 0.08072, 0.24722, -0.49916, 0.41528, -0.3709, 0.32725, -0.41665, 0.45883, -0.26452, 0.32725, -0.41665, 0.45883, -0.26452, 0.45883, -0.26452, 0.20977, -0.68588, 0.07256, -0.62972, 0.06464, 0.15804, 0.20081, 0.49096, 0.20081, 0.49096, 0.20312, 0.4967, 0.65096, 0.52242, 0.35518, 0.60631, 0.19735, 0.6293, 0.1966, 0.60685, 0.18332, 0.62288, 0.1662, 0.64352, 0.15096, 0.66191, 0.14247, 0.34827, 0.16317, 0.33866, 0.07103, 0.17363, 0.07062, 0.17262, 0.08076, 0.19742, 0, 0, 0, 0, 0, 0, 0.23256, 0.12354, -0.00241, 0.35328, 0, 0, 0, 0, 0, 0, -0.19719, 0.45695, -0.48854, -0.09519, -0.18856, -0.46076, -0.458, 1.06092, -1.13426, -0.22123, -0.80463, -0.82987, -0.43764, -1.06966, -0.2205, 0.97686, 0.05114, 0.66473, -0.04581, 0.63973, -0.00352, 0.48565, 0.00238, 0.3518, 0, 0, 0, 0, -0.0001, -1e-05, -3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, -0.07731, 0.18883, 0.03329, 0.20134, -0.1197, 0.29231, 0.05171, 0.31162, -0.18033, 0.42659, -0.00392, 0.4632, 0.23997, 0.39617, -0.30574, 0.30888, -0.16474, 0.40226, 0.07117, 0.42884, 0.29267, 0.30183, -0.17562, 0.09932, -0.14189, 0.14336, -0.07646, 0.18671, 0.13584, 0.14009, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10668, 0.07762, 0.08005, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10669, 0.07762, 0.08005, 0.10895, 0.18125, 0.15421, 0.14476, 0.19774, 0.07493, -0.1906, -0.08232, 0.14904, -0.14458, -0.1579, 0.20796, 0.13702, 0.22795, 0.19394, 0.18204, 0.24867, 0.09423, -0.2397, -0.10352, 0.18742, -0.18183, -0.24492, -0.004, -0.1012, 0.22733, -0.03545, 0.24627, -0.05474, -0.2388, 0.24149, 0.04085, -0.08037, 0.10586, 0.06975, 0.11603, 0.09873, 0.09266, -0.12201, -0.05269, 0.09541, -0.09255, -0.09607, 0.12653, 0.08337, 0.13869, 0.11802, 0.11076, -0.14585, -0.06298, -0.092, 0.1212, 0.07997, 0.13287, 0.11332, 0.10602, -0.13963, -0.06036, 0.02915, -0.14928, -0.21852, 0.28781, 0.18973, 0.31553, 0.26868, 0.25189, -0.33169, -0.14331, 0.06923, -0.35461, 0.73769, 0.54651, -0.35387, 0.8471, 0.67955, -0.3843, 0.54941, -0.55481, -0.29934, 0.69321, -0.52575, -0.54221, 0.63326, 0.58221, 0.76834, 0.38765, 0.85756, 0.06531, 0.41331, -0.72851, 0.58067, 0.44099, 0.65615, -0.37106, -0.28902, 0.6694, -0.71565, -0.13964, 0.19669, 0.61354, 0.66881, 0.05417, 0.65849, -0.12995, -0.54852, 0.33788, 0.38667, 0.64332, 0.5473, 0.51376, 0.70183, 0.26596, -0.6765, -0.29217, 0.52897, -0.51309, -0.18415, 0.39356, 0.3051, 0.32438, 0.38198, 0.22904, -0.42616, -0.08458, -0.00828, -0.43439, -0.38807, 0.18453, 0.02301, 0.43397, 0.14036, 0.4113, -0.27207, -0.33263, -0.5734, 0.4286, 0.18382, 0.70151, 0.36799, 0.62496, -0.5534, -0.45413, 0.63141, -0.33736, -0.63524, 0.55674, 0.28231, 0.80884, 0.49198, 0.70141, -0.69258, -0.48351, 0.32464, -0.77973, -0.5275, 0.69475, 0.45771, 0.76153, 0.64786, 0.60817, -0.80079, -0.34584, 0.16707, -0.85613, -0.30028, 0.0649, -0.05715, 0.30564, -0.13485, -0.27606, 0.24092, -0.19056, -0.38357, 0.03865, -0.11533, 0.37335, -0.12935, -0.3632, 0.32723, -0.20378, -0.27884, 0.18064, 0.06269, 0.3304, -0.24213, -0.2275, 0.17062, -0.28504, -0.07953, 0.34272, 0.29866, 0.20743, -0.35172, 0.00479, -0.07974, -0.34257, 0.1302, -0.32701, -0.09849, -0.11661, -0.1513, 0.04682, 0.08975, -0.12363, 0.082, -0.30777, 0.24304, -0.20618, 0.49379, -0.34102, 0.60007, 0.00199, -0.15764, 0.36521, -0.39046, -0.07616, -0.27701, -0.28573, -0.15064, -0.36824, 0.05359, -0.12404, 0.13261, 0.02591, 0.05114, 0.12503, -0.14179, 0.32854, -0.35126, -0.06846, -0.13555, -0.33128, 0.07388, 0.18062, 0.31334, 0.73186, 0.82651, -0.01037, 0.79267, -0.235, 0.64276, -0.51962, 0.27355, -0.77988, -0.63549, 0.47945, -0.60393, -0.51865, -0.19941, -0.77098, 0.10202, -0.78957, 0.24264, 0.57539, 0.65053, -0.00345, 0.62514, -0.18046, 0.50871, -0.40536, 0.21968, -0.6122, -0.50053, 0.3733, -0.4715, -0.40936, -0.15317, -0.60553, 0.08338, -0.61888, 0.28352, 0.2386, 0.05758, 0.347, 0.03323, 0.2002, 0.07784, -0.18022, 0.19267, 0.0376, 0.07431, 0.18168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02392, 0.14449, 0.0562, -0.13008, 0.13906, 0.02714, 0.04776, 0.28782, 0.11191, -0.2591, 0.27699, 0.05406, 0.10682, 0.26119, -0.26646, -0.20229, -0.24326, 0.24579, -0.13107, 0.32005, 0.05663, 0.34121, 0.13265, -0.30716, 0.32838, 0.06406, 0.23288, 0.24016, 0.12664, 0.30964, 0.34483, 0.2618, 0.38712, -0.22176, 0.31223, -0.31877, 0.16682, -0.41371, -0.07559, -0.43966, -0.17158, 0.39749, -0.4249, -0.0829, -0.30138, -0.31092, -0.16397, -0.40071, -0.00308, 0.43292, 0.07622, 0.05786, 0.08431, -0.04974, 0.06781, -0.07085, 0.03543, -0.09124, -0.01798, -0.09618, -0.0378, 0.08782, -0.09388, -0.0183, -0.06665, -0.06886, -0.03633, -0.08858, -0.0008, 0.09567, -0.02401, -0.01819, -0.02708, 0.01535, -0.02175, 0.02211, -0.01177, 0.0288, 0.005, 0.03074, 0.012, -0.02769, 0.02959, 0.00578, 0.02095, 0.02158, 0.01137, 0.02788, 0.00015, -0.03014, -0.27947, -0.41569, -0.5115, 0.09971, -0.4649, 0.23517, -0.34005, 0.3949, -0.08188, 0.51474, 0.33676, -0.37089, 0.43416, 0.24988, 0.21353, 0.45302, 0.0294, 0.50003, 0.16525, -0.47284], "curve": 0.348, "c2": 0.4, "c3": 0.682, "c4": 0.74}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "vertices": [-1.33154, 1.75366, 1.15707, 1.92291, 1.63989, 1.53442, -2.02057, -0.87361, 0.42201, -2.16046, -1.34592, 1.77264, 1.16797, 1.94305, -2.04321, -0.88242, 0.42625, -2.18433, -3.94373, -0.18866, -1.75647, 3.6073, -0.76044, -3.8744, 3.62296, -1.56934, -6.88803, 0.69769, -2.07971, 6.69891, -2.32581, -6.52083, 5.8747, -3.66272, -11.58063, -0.57141, -5.16608, 10.59192, -2.21704, -11.3815, 10.64626, -4.59198, 11.36093, 2.30872, -12.42981, -1.13855, -6.04791, 11.16577, -1.86987, -12.34154, 11.65838, -4.45795, 12.11496, 2.99744, -11.58063, -0.57129, -2.21692, -11.38161, 10.64637, -4.59186, 11.36102, 2.30872, -4.26031, -14.71904, 13.47106, -7.30029, 15.22632, 1.69916, 7.86499, -4.28418, 8.90186, 0.97205, 2.97708, -6.01093, 5.00079, -4.46637, 3.9408, -5.01733, 5.52527, -3.18542, 3.9408, -5.01733, 5.52527, -3.18542, 5.52527, -3.18542, 2.52612, -8.25946, 0.87372, -7.58322, 0.77844, 1.90317, 2.41821, 5.91223, 2.41821, 5.91223, 2.44598, 5.98132, 7.83899, 6.29099, 4.27716, 7.30124, 2.37646, 7.57813, 2.36749, 7.30774, 2.20752, 7.50082, 2.0014, 7.7493, 1.81793, 7.97086, 1.7157, 4.19388, 1.9649, 4.07816, 0.85535, 2.09082, 0.8504, 2.0787, 0.97253, 2.37741, 0, 0, 0, 0, 0, 0, 2.80048, 1.48773, -0.02905, 4.25428, 0, 0, 0, 0, 0, 0, -2.37457, 5.50259, -5.88306, -1.14624, -2.27063, -5.54852, -5.51526, 12.77574, -13.6589, -2.66412, -9.68942, -9.99341, -5.27008, -12.88098, -2.65527, 11.7634, 0.61578, 8.0048, -0.55164, 7.70369, 2.06743, 5.86275, 0.02402, 4.95053, 4.48993, 8.02096, -0.05579, 8.12894, 4.94708, 0.81725, -0.00037, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, -0.93103, 2.27393, 0.40088, 2.42455, -1.44147, 3.52008, 0.62274, 3.75257, -2.17151, 5.13702, -0.04724, 5.57791, 2.88977, 4.77072, -3.6817, 3.71954, -1.98383, 4.84412, 0.85699, 5.16412, 3.52438, 3.6347, -2.11478, 1.19604, -1.70868, 1.72638, -0.92072, 2.24835, 1.63583, 1.68695, -1.20837, 0.68341, -0.97638, 0.98645, -0.52612, 1.28467, 0.93469, 0.96393, -1.2084, 0.68341, -0.97638, 0.98645, -0.52612, 1.28473, 0.93472, 0.96393, 1.31198, 2.18268, 1.85699, 1.74316, 2.38116, 0.90234, -2.29523, -0.99132, 1.79471, -1.74109, -1.9014, 2.50427, 1.64996, 2.745, 2.33545, 2.19214, 2.99451, 1.13477, -2.88647, -1.24666, 2.25699, -2.18958, -2.94934, -0.04822, -1.21872, 2.73749, -0.42688, 2.96558, -0.65924, -2.87561, 2.90805, 0.49188, -0.96786, 1.27472, 0.8399, 1.39728, 1.1889, 1.11584, -1.4693, -0.63454, 1.1489, -1.1145, -1.15683, 1.52374, 1.004, 1.6701, 1.4212, 1.33374, -1.75629, -0.75845, -1.10791, 1.45953, 0.96298, 1.60004, 1.36462, 1.27673, -1.68146, -0.72688, 0.35101, -1.79761, -2.6315, 3.46588, 2.28476, 3.79968, 3.23547, 3.03326, -3.9942, -1.72581, 0.83364, -4.27026, 10.44598, 4.52307, -1.8894, 11.22525, 6.82751, -6.88354, 4.69727, -8.48254, -1.23285, 9.37206, -8.186, -4.73059, 7.62579, 7.01105, 9.25238, 4.66815, 10.32684, 0.78647, 4.97717, -8.77283, 6.99252, 5.31042, 7.9014, -4.46838, -3.48047, 8.061, -8.6179, -1.68158, 2.36862, 7.38831, 8.05383, 0.65228, 7.92963, -1.56482, -6.60529, 4.06883, 4.65628, 7.74689, 6.5907, 6.18677, 8.45148, 3.20273, -8.14648, -3.51831, 6.36996, -6.17871, -2.21753, 4.73926, 3.67404, 3.90619, 4.59979, 2.75818, -5.1319, -1.01848, -0.09969, -5.23102, -4.67319, 2.22211, 0.27713, 5.22589, 1.69019, 4.95294, -3.27625, -4.00562, -6.90491, 5.16119, 2.21356, 8.44763, 4.43134, 7.52582, -6.66412, -5.4687, 7.60345, -4.06256, -7.64963, 6.70435, 3.39957, 9.74017, 5.9245, 8.44647, -8.34015, -5.82253, 3.9094, -9.38959, -6.3522, 8.36627, 5.51178, 9.17047, 7.80164, 7.32367, -9.64319, -4.16464, 2.01184, -10.30963, -3.61606, 0.78149, -0.6882, 3.68054, -1.62384, -3.3244, 2.90117, -2.29474, -4.61905, 0.46545, -1.38882, 4.49597, -1.55768, -4.37364, 3.94057, -2.45398, -3.35782, 2.17523, 0.75488, 3.97876, -2.91571, -2.73964, 2.05458, -3.4325, -0.9577, 4.12708, 3.59653, 2.49792, -4.23541, 0.05766, -0.96026, -4.12524, 1.5679, -3.93793, -1.18597, -1.40417, -1.82196, 0.56378, 1.08081, -1.48875, 0.9874, -3.70624, 2.92667, -2.48279, 5.94627, -4.10663, 7.22614, 0.02399, -1.89838, 4.3979, -4.70194, -0.91711, -3.33578, -3.44073, -1.81403, -4.43442, 0.64532, -1.49368, 1.59691, 0.31201, 0.61578, 1.50562, -1.70746, 3.95638, -4.22991, -0.8244, -1.63226, -3.98929, 0.88965, 2.17508, 9.61384, 10.7322, 14.13177, -4.83204, 12.28498, -8.49629, 8.24201, -12.55261, 0.41178, -15.0103, -8.11862, 11.90322, -13.36022, -5.39321, -7.8849, -12.06212, -2.8264, -14.12861, 2.92194, 6.92896, 7.83377, -0.0415, 7.52795, -2.17316, 6.12598, -4.88141, 2.64545, -7.37216, -6.02747, 4.49538, -5.67788, -4.92957, -1.84445, -7.29187, 1.00403, -7.45258, 3.41418, 2.8733, 0.69336, 4.17861, 0.40021, 2.41085, 0.93732, -2.17024, 2.32017, 0.45282, 0.89484, 2.18781, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28802, 1.73997, 0.67682, -1.56641, 1.67453, 0.32684, 0.57513, 3.466, 1.34766, -3.12009, 3.33559, 0.65094, 1.28638, 3.14532, -3.20877, -2.43604, -2.92932, 2.95978, -1.57837, 3.8541, 0.68188, 4.10887, 1.59741, -3.69881, 3.95434, 0.77136, 2.80441, 2.89209, 1.52502, 3.72873, 6.46905, 4.90914, 7.27706, -4.1521, 5.87192, -5.9756, 3.20929, -7.78915, -1.36677, -8.31166, -3.22043, 7.45549, -7.96962, -1.55666, -5.65298, -5.83177, -3.07741, -7.51561, -0.05658, 8.12044, 3.86266, 2.93126, 4.34132, -2.48188, 3.50418, -3.56974, 1.95144, -4.66818, -0.79669, -4.99504, -1.92188, 4.45109, -4.75829, -0.92929, -3.37609, -3.48311, -1.8383, -4.48783, -0.0347, 4.84853, -0.28912, -0.21899, -0.32605, 0.18481, -0.26196, 0.26624, -0.14178, 0.34683, 0.06018, 0.37016, 0.14447, -0.33348, 0.35635, 0.06964, 0.25232, 0.25983, 0.13696, 0.33575, 0.00183, -0.36301, -3.36545, -5.0058, -6.15955, 1.20068, -5.59839, 2.83197, -4.09497, 4.7554, -0.98596, 6.19857, 4.0553, -4.46628, 5.22819, 3.00903, 2.57132, 5.45532, 0.354, 6.02139, 1.98993, -5.69405], "curve": 0.243, "c3": 0.676, "c4": 0.7}, {"time": 3.3333, "vertices": [-0.11057, 0.14563, 0.09609, 0.15968, 0.13618, 0.12742, -0.16779, -0.07255, 0.03504, -0.17941, -0.11177, 0.1472, 0.09699, 0.16135, -0.16967, -0.07328, 0.0354, -0.18139, -0.32749, -0.01567, -0.14586, 0.29956, -0.06315, -0.32174, 0.30086, -0.13032, -0.572, 0.05794, -0.1727, 0.55629, -0.19314, -0.5415, 0.48785, -0.30416, -0.96168, -0.04745, -0.429, 0.87957, -0.18411, -0.94514, 0.88409, -0.38133, 0.94343, 0.19172, -1.0322, -0.09455, -0.50223, 0.92723, -0.15528, -1.02487, 0.96813, -0.3702, 1.00605, 0.24891, -0.96168, -0.04744, -0.1841, -0.94515, 0.8841, -0.38132, 0.94344, 0.19172, -0.35378, -1.2223, 1.11866, -0.60623, 1.26442, 0.1411, 0.65312, -0.35577, 0.73923, 0.08072, 0.24722, -0.49916, 0.41528, -0.3709, 0.32725, -0.41665, 0.45883, -0.26452, 0.32725, -0.41665, 0.45883, -0.26452, 0.45883, -0.26452, 0.20977, -0.68588, 0.07256, -0.62972, 0.06464, 0.15804, 0.20081, 0.49096, 0.20081, 0.49096, 0.20312, 0.4967, 0.65096, 0.52242, 0.35518, 0.60631, 0.19735, 0.6293, 0.1966, 0.60685, 0.18332, 0.62288, 0.1662, 0.64352, 0.15096, 0.66191, 0.14247, 0.34827, 0.16317, 0.33866, 0.07103, 0.17363, 0.07062, 0.17262, 0.08076, 0.19742, 0, 0, 0, 0, 0, 0, 0.23256, 0.12354, -0.00241, 0.35328, 0, 0, 0, 0, 0, 0, -0.19719, 0.45695, -0.48854, -0.09519, -0.18856, -0.46076, -0.458, 1.06092, -1.13426, -0.22123, -0.80463, -0.82987, -0.43764, -1.06966, -0.2205, 0.97686, 0.05114, 0.66473, -0.04581, 0.63973, -0.00352, 0.48565, 0.00238, 0.3518, 0, 0, 0, 0, -0.0001, -1e-05, -3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, -0.07731, 0.18883, 0.03329, 0.20134, -0.1197, 0.29231, 0.05171, 0.31162, -0.18033, 0.42659, -0.00392, 0.4632, 0.23997, 0.39617, -0.30574, 0.30888, -0.16474, 0.40226, 0.07117, 0.42884, 0.29267, 0.30183, -0.17562, 0.09932, -0.14189, 0.14336, -0.07646, 0.18671, 0.13584, 0.14009, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10668, 0.07762, 0.08005, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10669, 0.07762, 0.08005, 0.10895, 0.18125, 0.15421, 0.14476, 0.19774, 0.07493, -0.1906, -0.08232, 0.14904, -0.14458, -0.1579, 0.20796, 0.13702, 0.22795, 0.19394, 0.18204, 0.24867, 0.09423, -0.2397, -0.10352, 0.18742, -0.18183, -0.24492, -0.004, -0.1012, 0.22733, -0.03545, 0.24627, -0.05474, -0.2388, 0.24149, 0.04085, -0.08037, 0.10586, 0.06975, 0.11603, 0.09873, 0.09266, -0.12201, -0.05269, 0.09541, -0.09255, -0.09607, 0.12653, 0.08337, 0.13869, 0.11802, 0.11076, -0.14585, -0.06298, -0.092, 0.1212, 0.07997, 0.13287, 0.11332, 0.10602, -0.13963, -0.06036, 0.02915, -0.14928, -0.21852, 0.28781, 0.18973, 0.31553, 0.26868, 0.25189, -0.33169, -0.14331, 0.06923, -0.35461, 0.73769, 0.54651, -0.35387, 0.8471, 0.67955, -0.3843, 0.54941, -0.55481, -0.29934, 0.69321, -0.52575, -0.54221, 0.63326, 0.58221, 0.76834, 0.38765, 0.85756, 0.06531, 0.41331, -0.72851, 0.58067, 0.44099, 0.65615, -0.37106, -0.28902, 0.6694, -0.71565, -0.13964, 0.19669, 0.61354, 0.66881, 0.05417, 0.65849, -0.12995, -0.54852, 0.33788, 0.38667, 0.64332, 0.5473, 0.51376, 0.70183, 0.26596, -0.6765, -0.29217, 0.52897, -0.51309, -0.18415, 0.39356, 0.3051, 0.32438, 0.38198, 0.22904, -0.42616, -0.08458, -0.00828, -0.43439, -0.38807, 0.18453, 0.02301, 0.43397, 0.14036, 0.4113, -0.27207, -0.33263, -0.5734, 0.4286, 0.18382, 0.70151, 0.36799, 0.62496, -0.5534, -0.45413, 0.63141, -0.33736, -0.63524, 0.55674, 0.28231, 0.80884, 0.49198, 0.70141, -0.69258, -0.48351, 0.32464, -0.77973, -0.5275, 0.69475, 0.45771, 0.76153, 0.64786, 0.60817, -0.80079, -0.34584, 0.16707, -0.85613, -0.30028, 0.0649, -0.05715, 0.30564, -0.13485, -0.27606, 0.24092, -0.19056, -0.38357, 0.03865, -0.11533, 0.37335, -0.12935, -0.3632, 0.32723, -0.20378, -0.27884, 0.18064, 0.06269, 0.3304, -0.24213, -0.2275, 0.17062, -0.28504, -0.07953, 0.34272, 0.29866, 0.20743, -0.35172, 0.00479, -0.07974, -0.34257, 0.1302, -0.32701, -0.09849, -0.11661, -0.1513, 0.04682, 0.08975, -0.12363, 0.082, -0.30777, 0.24304, -0.20618, 0.49379, -0.34102, 0.60007, 0.00199, -0.15764, 0.36521, -0.39046, -0.07616, -0.27701, -0.28573, -0.15064, -0.36824, 0.05359, -0.12404, 0.13261, 0.02591, 0.05114, 0.12503, -0.14179, 0.32854, -0.35126, -0.06846, -0.13555, -0.33128, 0.07388, 0.18062, 0.31334, 0.73186, 0.82651, -0.01037, 0.79267, -0.235, 0.64276, -0.51962, 0.27355, -0.77988, -0.63549, 0.47945, -0.60393, -0.51865, -0.19941, -0.77098, 0.10202, -0.78957, 0.24264, 0.57539, 0.65053, -0.00345, 0.62514, -0.18046, 0.50871, -0.40536, 0.21968, -0.6122, -0.50053, 0.3733, -0.4715, -0.40936, -0.15317, -0.60553, 0.08338, -0.61888, 0.28352, 0.2386, 0.05758, 0.347, 0.03323, 0.2002, 0.07784, -0.18022, 0.19267, 0.0376, 0.07431, 0.18168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02392, 0.14449, 0.0562, -0.13008, 0.13906, 0.02714, 0.04776, 0.28782, 0.11191, -0.2591, 0.27699, 0.05406, 0.10682, 0.26119, -0.26646, -0.20229, -0.24326, 0.24579, -0.13107, 0.32005, 0.05663, 0.34121, 0.13265, -0.30716, 0.32838, 0.06406, 0.23288, 0.24016, 0.12664, 0.30964, 0.34483, 0.2618, 0.38712, -0.22176, 0.31223, -0.31877, 0.16682, -0.41371, -0.07559, -0.43966, -0.17158, 0.39749, -0.4249, -0.0829, -0.30138, -0.31092, -0.16397, -0.40071, -0.00308, 0.43292, 0.07622, 0.05786, 0.08431, -0.04974, 0.06781, -0.07085, 0.03543, -0.09124, -0.01798, -0.09618, -0.0378, 0.08782, -0.09388, -0.0183, -0.06665, -0.06886, -0.03633, -0.08858, -0.0008, 0.09567, -0.02401, -0.01819, -0.02708, 0.01535, -0.02175, 0.02211, -0.01177, 0.0288, 0.005, 0.03074, 0.012, -0.02769, 0.02959, 0.00578, 0.02095, 0.02158, 0.01137, 0.02788, 0.00015, -0.03014, -0.27947, -0.41569, -0.5115, 0.09971, -0.4649, 0.23517, -0.34005, 0.3949, -0.08188, 0.51474, 0.33676, -0.37089, 0.43416, 0.24988, 0.21353, 0.45302, 0.0294, 0.50003, 0.16525, -0.47284], "curve": 0.348, "c2": 0.4, "c3": 0.682, "c4": 0.74}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "vertices": [-1.33154, 1.75366, 1.15707, 1.92291, 1.63989, 1.53442, -2.02057, -0.87361, 0.42201, -2.16046, -1.34592, 1.77264, 1.16797, 1.94305, -2.04321, -0.88242, 0.42625, -2.18433, -3.94373, -0.18866, -1.75647, 3.6073, -0.76044, -3.8744, 3.62296, -1.56934, -6.88803, 0.69769, -2.07971, 6.69891, -2.32581, -6.52083, 5.8747, -3.66272, -11.58063, -0.57141, -5.16608, 10.59192, -2.21704, -11.3815, 10.64626, -4.59198, 11.36093, 2.30872, -12.42981, -1.13855, -6.04791, 11.16577, -1.86987, -12.34154, 11.65838, -4.45795, 12.11496, 2.99744, -11.58063, -0.57129, -2.21692, -11.38161, 10.64637, -4.59186, 11.36102, 2.30872, -4.26031, -14.71904, 13.47106, -7.30029, 15.22632, 1.69916, 7.86499, -4.28418, 8.90186, 0.97205, 2.97708, -6.01093, 5.00079, -4.46637, 3.9408, -5.01733, 5.52527, -3.18542, 3.9408, -5.01733, 5.52527, -3.18542, 5.52527, -3.18542, 2.52612, -8.25946, 0.87372, -7.58322, 0.77844, 1.90317, 2.41821, 5.91223, 2.41821, 5.91223, 2.44598, 5.98132, 7.83899, 6.29099, 4.27716, 7.30124, 2.37646, 7.57813, 2.36749, 7.30774, 2.20752, 7.50082, 2.0014, 7.7493, 1.81793, 7.97086, 1.7157, 4.19388, 1.9649, 4.07816, 0.85535, 2.09082, 0.8504, 2.0787, 0.97253, 2.37741, 0, 0, 0, 0, 0, 0, 2.80048, 1.48773, -0.02905, 4.25428, 0, 0, 0, 0, 0, 0, -2.37457, 5.50259, -5.88306, -1.14624, -2.27063, -5.54852, -5.51526, 12.77574, -13.6589, -2.66412, -9.68942, -9.99341, -5.27008, -12.88098, -2.65527, 11.7634, 0.61578, 8.0048, -0.55164, 7.70369, 2.06743, 5.86275, 0.02402, 4.95053, 4.48993, 8.02096, -0.05579, 8.12894, 4.94708, 0.81725, -0.00037, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, 0.40088, 2.42455, -0.93103, 2.27393, 0.40088, 2.42455, -1.44147, 3.52008, 0.62274, 3.75257, -2.17151, 5.13702, -0.04724, 5.57791, 2.88977, 4.77072, -3.6817, 3.71954, -1.98383, 4.84412, 0.85699, 5.16412, 3.52438, 3.6347, -2.11478, 1.19604, -1.70868, 1.72638, -0.92072, 2.24835, 1.63583, 1.68695, -1.20837, 0.68341, -0.97638, 0.98645, -0.52612, 1.28467, 0.93469, 0.96393, -1.2084, 0.68341, -0.97638, 0.98645, -0.52612, 1.28473, 0.93472, 0.96393, 1.31198, 2.18268, 1.85699, 1.74316, 2.38116, 0.90234, -2.29523, -0.99132, 1.79471, -1.74109, -1.9014, 2.50427, 1.64996, 2.745, 2.33545, 2.19214, 2.99451, 1.13477, -2.88647, -1.24666, 2.25699, -2.18958, -2.94934, -0.04822, -1.21872, 2.73749, -0.42688, 2.96558, -0.65924, -2.87561, 2.90805, 0.49188, -0.96786, 1.27472, 0.8399, 1.39728, 1.1889, 1.11584, -1.4693, -0.63454, 1.1489, -1.1145, -1.15683, 1.52374, 1.004, 1.6701, 1.4212, 1.33374, -1.75629, -0.75845, -1.10791, 1.45953, 0.96298, 1.60004, 1.36462, 1.27673, -1.68146, -0.72688, 0.35101, -1.79761, -2.6315, 3.46588, 2.28476, 3.79968, 3.23547, 3.03326, -3.9942, -1.72581, 0.83364, -4.27026, 10.44598, 4.52307, -1.8894, 11.22525, 6.82751, -6.88354, 4.69727, -8.48254, -1.23285, 9.37206, -8.186, -4.73059, 7.62579, 7.01105, 9.25238, 4.66815, 10.32684, 0.78647, 4.97717, -8.77283, 6.99252, 5.31042, 7.9014, -4.46838, -3.48047, 8.061, -8.6179, -1.68158, 2.36862, 7.38831, 8.05383, 0.65228, 7.92963, -1.56482, -6.60529, 4.06883, 4.65628, 7.74689, 6.5907, 6.18677, 8.45148, 3.20273, -8.14648, -3.51831, 6.36996, -6.17871, -2.21753, 4.73926, 3.67404, 3.90619, 4.59979, 2.75818, -5.1319, -1.01848, -0.09969, -5.23102, -4.67319, 2.22211, 0.27713, 5.22589, 1.69019, 4.95294, -3.27625, -4.00562, -6.90491, 5.16119, 2.21356, 8.44763, 4.43134, 7.52582, -6.66412, -5.4687, 7.60345, -4.06256, -7.64963, 6.70435, 3.39957, 9.74017, 5.9245, 8.44647, -8.34015, -5.82253, 3.9094, -9.38959, -6.3522, 8.36627, 5.51178, 9.17047, 7.80164, 7.32367, -9.64319, -4.16464, 2.01184, -10.30963, -3.61606, 0.78149, -0.6882, 3.68054, -1.62384, -3.3244, 2.90117, -2.29474, -4.61905, 0.46545, -1.38882, 4.49597, -1.55768, -4.37364, 3.94057, -2.45398, -3.35782, 2.17523, 0.75488, 3.97876, -2.91571, -2.73964, 2.05458, -3.4325, -0.9577, 4.12708, 3.59653, 2.49792, -4.23541, 0.05766, -0.96026, -4.12524, 1.5679, -3.93793, -1.18597, -1.40417, -1.82196, 0.56378, 1.08081, -1.48875, 0.9874, -3.70624, 2.92667, -2.48279, 5.94627, -4.10663, 7.22614, 0.02399, -1.89838, 4.3979, -4.70194, -0.91711, -3.33578, -3.44073, -1.81403, -4.43442, 0.64532, -1.49368, 1.59691, 0.31201, 0.61578, 1.50562, -1.70746, 3.95638, -4.22991, -0.8244, -1.63226, -3.98929, 0.88965, 2.17508, 9.61384, 10.7322, 14.13177, -4.83204, 12.28498, -8.49629, 8.24201, -12.55261, 0.41178, -15.0103, -8.11862, 11.90322, -13.36022, -5.39321, -7.8849, -12.06212, -2.8264, -14.12861, 2.92194, 6.92896, 7.83377, -0.0415, 7.52795, -2.17316, 6.12598, -4.88141, 2.64545, -7.37216, -6.02747, 4.49538, -5.67788, -4.92957, -1.84445, -7.29187, 1.00403, -7.45258, 3.41418, 2.8733, 0.69336, 4.17861, 0.40021, 2.41085, 0.93732, -2.17024, 2.32017, 0.45282, 0.89484, 2.18781, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28802, 1.73997, 0.67682, -1.56641, 1.67453, 0.32684, 0.57513, 3.466, 1.34766, -3.12009, 3.33559, 0.65094, 1.28638, 3.14532, -3.20877, -2.43604, -2.92932, 2.95978, -1.57837, 3.8541, 0.68188, 4.10887, 1.59741, -3.69881, 3.95434, 0.77136, 2.80441, 2.89209, 1.52502, 3.72873, 6.46905, 4.90914, 7.27706, -4.1521, 5.87192, -5.9756, 3.20929, -7.78915, -1.36677, -8.31166, -3.22043, 7.45549, -7.96962, -1.55666, -5.65298, -5.83177, -3.07741, -7.51561, -0.05658, 8.12044, 3.86266, 2.93126, 4.34132, -2.48188, 3.50418, -3.56974, 1.95144, -4.66818, -0.79669, -4.99504, -1.92188, 4.45109, -4.75829, -0.92929, -3.37609, -3.48311, -1.8383, -4.48783, -0.0347, 4.84853, -0.28912, -0.21899, -0.32605, 0.18481, -0.26196, 0.26624, -0.14178, 0.34683, 0.06018, 0.37016, 0.14447, -0.33348, 0.35635, 0.06964, 0.25232, 0.25983, 0.13696, 0.33575, 0.00183, -0.36301, -3.36545, -5.0058, -6.15955, 1.20068, -5.59839, 2.83197, -4.09497, 4.7554, -0.98596, 6.19857, 4.0553, -4.46628, 5.22819, 3.00903, 2.57132, 5.45532, 0.354, 6.02139, 1.98993, -5.69405], "curve": 0.243, "c3": 0.676, "c4": 0.7}, {"time": 6.6667, "vertices": [-0.11057, 0.14563, 0.09609, 0.15968, 0.13618, 0.12742, -0.16779, -0.07255, 0.03504, -0.17941, -0.11177, 0.1472, 0.09699, 0.16135, -0.16967, -0.07328, 0.0354, -0.18139, -0.32749, -0.01567, -0.14586, 0.29956, -0.06315, -0.32174, 0.30086, -0.13032, -0.572, 0.05794, -0.1727, 0.55629, -0.19314, -0.5415, 0.48785, -0.30416, -0.96168, -0.04745, -0.429, 0.87957, -0.18411, -0.94514, 0.88409, -0.38133, 0.94343, 0.19172, -1.0322, -0.09455, -0.50223, 0.92723, -0.15528, -1.02487, 0.96813, -0.3702, 1.00605, 0.24891, -0.96168, -0.04744, -0.1841, -0.94515, 0.8841, -0.38132, 0.94344, 0.19172, -0.35378, -1.2223, 1.11866, -0.60623, 1.26442, 0.1411, 0.65312, -0.35577, 0.73923, 0.08072, 0.24722, -0.49916, 0.41528, -0.3709, 0.32725, -0.41665, 0.45883, -0.26452, 0.32725, -0.41665, 0.45883, -0.26452, 0.45883, -0.26452, 0.20977, -0.68588, 0.07256, -0.62972, 0.06464, 0.15804, 0.20081, 0.49096, 0.20081, 0.49096, 0.20312, 0.4967, 0.65096, 0.52242, 0.35518, 0.60631, 0.19735, 0.6293, 0.1966, 0.60685, 0.18332, 0.62288, 0.1662, 0.64352, 0.15096, 0.66191, 0.14247, 0.34827, 0.16317, 0.33866, 0.07103, 0.17363, 0.07062, 0.17262, 0.08076, 0.19742, 0, 0, 0, 0, 0, 0, 0.23256, 0.12354, -0.00241, 0.35328, 0, 0, 0, 0, 0, 0, -0.19719, 0.45695, -0.48854, -0.09519, -0.18856, -0.46076, -0.458, 1.06092, -1.13426, -0.22123, -0.80463, -0.82987, -0.43764, -1.06966, -0.2205, 0.97686, 0.05114, 0.66473, -0.04581, 0.63973, -0.00352, 0.48565, 0.00238, 0.3518, 0, 0, 0, 0, -0.0001, -1e-05, -3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, 0.03329, 0.20134, -0.07731, 0.18883, 0.03329, 0.20134, -0.1197, 0.29231, 0.05171, 0.31162, -0.18033, 0.42659, -0.00392, 0.4632, 0.23997, 0.39617, -0.30574, 0.30888, -0.16474, 0.40226, 0.07117, 0.42884, 0.29267, 0.30183, -0.17562, 0.09932, -0.14189, 0.14336, -0.07646, 0.18671, 0.13584, 0.14009, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10668, 0.07762, 0.08005, -0.10035, 0.05675, -0.08108, 0.08192, -0.04369, 0.10669, 0.07762, 0.08005, 0.10895, 0.18125, 0.15421, 0.14476, 0.19774, 0.07493, -0.1906, -0.08232, 0.14904, -0.14458, -0.1579, 0.20796, 0.13702, 0.22795, 0.19394, 0.18204, 0.24867, 0.09423, -0.2397, -0.10352, 0.18742, -0.18183, -0.24492, -0.004, -0.1012, 0.22733, -0.03545, 0.24627, -0.05474, -0.2388, 0.24149, 0.04085, -0.08037, 0.10586, 0.06975, 0.11603, 0.09873, 0.09266, -0.12201, -0.05269, 0.09541, -0.09255, -0.09607, 0.12653, 0.08337, 0.13869, 0.11802, 0.11076, -0.14585, -0.06298, -0.092, 0.1212, 0.07997, 0.13287, 0.11332, 0.10602, -0.13963, -0.06036, 0.02915, -0.14928, -0.21852, 0.28781, 0.18973, 0.31553, 0.26868, 0.25189, -0.33169, -0.14331, 0.06923, -0.35461, 0.73769, 0.54651, -0.35387, 0.8471, 0.67955, -0.3843, 0.54941, -0.55481, -0.29934, 0.69321, -0.52575, -0.54221, 0.63326, 0.58221, 0.76834, 0.38765, 0.85756, 0.06531, 0.41331, -0.72851, 0.58067, 0.44099, 0.65615, -0.37106, -0.28902, 0.6694, -0.71565, -0.13964, 0.19669, 0.61354, 0.66881, 0.05417, 0.65849, -0.12995, -0.54852, 0.33788, 0.38667, 0.64332, 0.5473, 0.51376, 0.70183, 0.26596, -0.6765, -0.29217, 0.52897, -0.51309, -0.18415, 0.39356, 0.3051, 0.32438, 0.38198, 0.22904, -0.42616, -0.08458, -0.00828, -0.43439, -0.38807, 0.18453, 0.02301, 0.43397, 0.14036, 0.4113, -0.27207, -0.33263, -0.5734, 0.4286, 0.18382, 0.70151, 0.36799, 0.62496, -0.5534, -0.45413, 0.63141, -0.33736, -0.63524, 0.55674, 0.28231, 0.80884, 0.49198, 0.70141, -0.69258, -0.48351, 0.32464, -0.77973, -0.5275, 0.69475, 0.45771, 0.76153, 0.64786, 0.60817, -0.80079, -0.34584, 0.16707, -0.85613, -0.30028, 0.0649, -0.05715, 0.30564, -0.13485, -0.27606, 0.24092, -0.19056, -0.38357, 0.03865, -0.11533, 0.37335, -0.12935, -0.3632, 0.32723, -0.20378, -0.27884, 0.18064, 0.06269, 0.3304, -0.24213, -0.2275, 0.17062, -0.28504, -0.07953, 0.34272, 0.29866, 0.20743, -0.35172, 0.00479, -0.07974, -0.34257, 0.1302, -0.32701, -0.09849, -0.11661, -0.1513, 0.04682, 0.08975, -0.12363, 0.082, -0.30777, 0.24304, -0.20618, 0.49379, -0.34102, 0.60007, 0.00199, -0.15764, 0.36521, -0.39046, -0.07616, -0.27701, -0.28573, -0.15064, -0.36824, 0.05359, -0.12404, 0.13261, 0.02591, 0.05114, 0.12503, -0.14179, 0.32854, -0.35126, -0.06846, -0.13555, -0.33128, 0.07388, 0.18062, 0.31334, 0.73186, 0.82651, -0.01037, 0.79267, -0.235, 0.64276, -0.51962, 0.27355, -0.77988, -0.63549, 0.47945, -0.60393, -0.51865, -0.19941, -0.77098, 0.10202, -0.78957, 0.24264, 0.57539, 0.65053, -0.00345, 0.62514, -0.18046, 0.50871, -0.40536, 0.21968, -0.6122, -0.50053, 0.3733, -0.4715, -0.40936, -0.15317, -0.60553, 0.08338, -0.61888, 0.28352, 0.2386, 0.05758, 0.347, 0.03323, 0.2002, 0.07784, -0.18022, 0.19267, 0.0376, 0.07431, 0.18168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02392, 0.14449, 0.0562, -0.13008, 0.13906, 0.02714, 0.04776, 0.28782, 0.11191, -0.2591, 0.27699, 0.05406, 0.10682, 0.26119, -0.26646, -0.20229, -0.24326, 0.24579, -0.13107, 0.32005, 0.05663, 0.34121, 0.13265, -0.30716, 0.32838, 0.06406, 0.23288, 0.24016, 0.12664, 0.30964, 0.34483, 0.2618, 0.38712, -0.22176, 0.31223, -0.31877, 0.16682, -0.41371, -0.07559, -0.43966, -0.17158, 0.39749, -0.4249, -0.0829, -0.30138, -0.31092, -0.16397, -0.40071, -0.00308, 0.43292, 0.07622, 0.05786, 0.08431, -0.04974, 0.06781, -0.07085, 0.03543, -0.09124, -0.01798, -0.09618, -0.0378, 0.08782, -0.09388, -0.0183, -0.06665, -0.06886, -0.03633, -0.08858, -0.0008, 0.09567, -0.02401, -0.01819, -0.02708, 0.01535, -0.02175, 0.02211, -0.01177, 0.0288, 0.005, 0.03074, 0.012, -0.02769, 0.02959, 0.00578, 0.02095, 0.02158, 0.01137, 0.02788, 0.00015, -0.03014, -0.27947, -0.41569, -0.5115, 0.09971, -0.4649, 0.23517, -0.34005, 0.3949, -0.08188, 0.51474, 0.33676, -0.37089, 0.43416, 0.24988, 0.21353, 0.45302, 0.0294, 0.50003, 0.16525, -0.47284]}]}, "t2": {"t2": [{"offset": 102, "vertices": [-0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, -0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, 0.40561, -0.66316, 0.46335, -0.62429, -0.12595, -0.76691, -0.67407, -0.38706, 0.57236, -0.22934, 0.5906, -0.17724, 0.28364, -0.54742, -0.2451, -0.56578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61216, 0.34135, -0.61216, 0.34135, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.23596, -0.0937, -0.22175, 0.12366, -0.20618, 0.51912, 0.27205, 0.48779, 0.05997, 0.80355, 0.66059, 0.46116, 0.2428, 0.81257, 0.78309, 0.32513, -0.27489, 0.69312, 0.30687, 0.68007, 0.72089, 0.19177, -0.21912, 0.52206, 0.22281, 0.521, 0.54448, 0.15642, -0.12995, 0.53582, 0.29472, 0.46652, 0.54769, 0.06624, 0.03248, 0.65788, 0.49542, 0.4348, 0.64992, -0.10937, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.44489, 0.5868, 0.34061, 0.65287, 0.28081, 0.68072, -0.31114, 0.23589, -0.34617, 0.18059, -0.36094, 0.14886, -0.09019, 0.32075, -0.26551, 0.20131, -0.29541, 0.15411, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.59543, -0.45922, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.35539, -0.75701, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, 0.94577, -1.11945, 1.04219, -1.0305, -0.01492, -1.46511, -1.14513, -0.91434, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, -0.45809, -0.2719, -0.4319, -0.31186, -0.52442, 0.09342, -0.259, 0.46545, -0.30287, 0.41645, -0.33892, 0.38751, 0.0428, 0.51343, 0.42488, 0.29115], "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "offset": 102, "vertices": [-0.39032, -12.62598, 1.72471, -12.51413, 2.83794, -12.31027, -6.86739, -10.60141, -12.55664, -1.37654, -0.39032, -12.62598, 1.72471, -12.51413, 2.83794, -12.31027, -6.86739, -10.60141, -12.55664, -1.37654, 4.45408, -7.28235, 5.0882, -6.85556, -1.38306, -8.42169, -7.40213, -4.25039, 6.28526, -2.51843, 6.48556, -1.94629, 3.11476, -6.01135, -2.69147, -6.21304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.72235, 3.74847, -6.72235, 3.74847, -6.72235, 3.74847, -7.153, -2.84146, -6.72235, 3.74847, -7.153, -2.84146, -6.72235, 3.74847, -2.59116, -1.02896, -2.43509, 1.35796, -2.26413, 5.70065, 2.98746, 5.35661, 0.65855, 8.82404, 7.25415, 5.06413, 2.66629, 8.92303, 8.59937, 3.57038, -3.01863, 7.61133, 3.36984, 7.46805, 7.91629, 2.10586, -2.40624, 5.73291, 2.44676, 5.72128, 5.97913, 1.71764, -1.42704, 5.884, 3.23639, 5.12299, 6.01431, 0.72739, 0.35664, 7.2244, 5.44035, 4.77472, 7.13693, -1.20099, 3.7403, 7.16937, 3.08362, 7.47516, 7.51521, 2.98615, 3.7403, 7.16937, 3.08362, 7.47516, 7.51521, 2.98615, 3.7403, 7.16937, 3.08362, 7.47516, 4.88545, 6.44388, 3.7403, 7.16937, 3.08362, 7.47516, -3.41672, 2.59039, -3.80141, 1.98306, -3.96362, 1.6347, -0.99036, 3.52222, -2.91567, 2.2106, -3.24396, 1.69235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.5386, -5.0428, 7.72238, -2.92735, 7.92249, 2.32648, 7.42268, 3.61691, 7.72238, -2.92735, 7.92249, 2.32648, 7.42268, 3.61691, 3.90265, -8.31296, 5.23675, -7.5444, 5.89106, -7.0462, -0.96196, -9.13214, -7.68628, -5.02525, 10.3858, -12.29306, 11.4446, -11.31619, -0.16382, -16.08887, -12.57501, -10.04065, 5.23675, -7.5444, 5.89106, -7.0462, -0.96196, -9.13214, -7.68628, -5.02525, -5.03041, -2.98578, -4.74284, -3.42468, -5.75882, 1.02591, -2.84415, 5.11122, -3.32586, 4.57321, -3.72174, 4.25534, 0.46997, 5.63818, 4.66571, 3.19725], "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 3.3333, "offset": 102, "vertices": [-0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, -0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, 0.40561, -0.66316, 0.46335, -0.62429, -0.12595, -0.76691, -0.67407, -0.38706, 0.57236, -0.22934, 0.5906, -0.17724, 0.28364, -0.54742, -0.2451, -0.56578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61216, 0.34135, -0.61216, 0.34135, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.23596, -0.0937, -0.22175, 0.12366, -0.20618, 0.51912, 0.27205, 0.48779, 0.05997, 0.80355, 0.66059, 0.46116, 0.2428, 0.81257, 0.78309, 0.32513, -0.27489, 0.69312, 0.30687, 0.68007, 0.72089, 0.19177, -0.21912, 0.52206, 0.22281, 0.521, 0.54448, 0.15642, -0.12995, 0.53582, 0.29472, 0.46652, 0.54769, 0.06624, 0.03248, 0.65788, 0.49542, 0.4348, 0.64992, -0.10937, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.44489, 0.5868, 0.34061, 0.65287, 0.28081, 0.68072, -0.31114, 0.23589, -0.34617, 0.18059, -0.36094, 0.14886, -0.09019, 0.32075, -0.26551, 0.20131, -0.29541, 0.15411, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.59543, -0.45922, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.35539, -0.75701, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, 0.94577, -1.11945, 1.04219, -1.0305, -0.01492, -1.46511, -1.14513, -0.91434, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, -0.45809, -0.2719, -0.4319, -0.31186, -0.52442, 0.09342, -0.259, 0.46545, -0.30287, 0.41645, -0.33892, 0.38751, 0.0428, 0.51343, 0.42488, 0.29115], "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "offset": 102, "vertices": [-0.39032, -12.62598, 1.72471, -12.51413, 2.83794, -12.31027, -6.86739, -10.60141, -12.55664, -1.37654, -0.39032, -12.62598, 1.72471, -12.51413, 2.83794, -12.31027, -6.86739, -10.60141, -12.55664, -1.37654, 4.45408, -7.28235, 5.0882, -6.85556, -1.38306, -8.42169, -7.40213, -4.25039, 6.28526, -2.51843, 6.48556, -1.94629, 3.11476, -6.01135, -2.69147, -6.21304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.72235, 3.74847, -6.72235, 3.74847, -6.72235, 3.74847, -7.153, -2.84146, -6.72235, 3.74847, -7.153, -2.84146, -6.72235, 3.74847, -2.59116, -1.02896, -2.43509, 1.35796, -2.26413, 5.70065, 2.98746, 5.35661, 0.65855, 8.82404, 7.25415, 5.06413, 2.66629, 8.92303, 8.59937, 3.57038, -3.01863, 7.61133, 3.36984, 7.46805, 7.91629, 2.10586, -2.40624, 5.73291, 2.44676, 5.72128, 5.97913, 1.71764, -1.42704, 5.884, 3.23639, 5.12299, 6.01431, 0.72739, 0.35664, 7.2244, 5.44035, 4.77472, 7.13693, -1.20099, 3.7403, 7.16937, 3.08362, 7.47516, 7.51521, 2.98615, 3.7403, 7.16937, 3.08362, 7.47516, 7.51521, 2.98615, 3.7403, 7.16937, 3.08362, 7.47516, 4.88545, 6.44388, 3.7403, 7.16937, 3.08362, 7.47516, -3.41672, 2.59039, -3.80141, 1.98306, -3.96362, 1.6347, -0.99036, 3.52222, -2.91567, 2.2106, -3.24396, 1.69235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.5386, -5.0428, 7.72238, -2.92735, 7.92249, 2.32648, 7.42268, 3.61691, 7.72238, -2.92735, 7.92249, 2.32648, 7.42268, 3.61691, 3.90265, -8.31296, 5.23675, -7.5444, 5.89106, -7.0462, -0.96196, -9.13214, -7.68628, -5.02525, 10.3858, -12.29306, 11.4446, -11.31619, -0.16382, -16.08887, -12.57501, -10.04065, 5.23675, -7.5444, 5.89106, -7.0462, -0.96196, -9.13214, -7.68628, -5.02525, -5.03041, -2.98578, -4.74284, -3.42468, -5.75882, 1.02591, -2.84415, 5.11122, -3.32586, 4.57321, -3.72174, 4.25534, 0.46997, 5.63818, 4.66571, 3.19725], "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 6.6667, "offset": 102, "vertices": [-0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, -0.03554, -1.14977, 0.15706, -1.13958, 0.25843, -1.12102, -0.62537, -0.9654, -1.14346, -0.12535, 0.40561, -0.66316, 0.46335, -0.62429, -0.12595, -0.76691, -0.67407, -0.38706, 0.57236, -0.22934, 0.5906, -0.17724, 0.28364, -0.54742, -0.2451, -0.56578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61216, 0.34135, -0.61216, 0.34135, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.65138, -0.25875, -0.61216, 0.34135, -0.23596, -0.0937, -0.22175, 0.12366, -0.20618, 0.51912, 0.27205, 0.48779, 0.05997, 0.80355, 0.66059, 0.46116, 0.2428, 0.81257, 0.78309, 0.32513, -0.27489, 0.69312, 0.30687, 0.68007, 0.72089, 0.19177, -0.21912, 0.52206, 0.22281, 0.521, 0.54448, 0.15642, -0.12995, 0.53582, 0.29472, 0.46652, 0.54769, 0.06624, 0.03248, 0.65788, 0.49542, 0.4348, 0.64992, -0.10937, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.68436, 0.27193, 0.34061, 0.65287, 0.28081, 0.68072, 0.44489, 0.5868, 0.34061, 0.65287, 0.28081, 0.68072, -0.31114, 0.23589, -0.34617, 0.18059, -0.36094, 0.14886, -0.09019, 0.32075, -0.26551, 0.20131, -0.29541, 0.15411, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.59543, -0.45922, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.70323, -0.26658, 0.72145, 0.21186, 0.67594, 0.32937, 0.35539, -0.75701, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, 0.94577, -1.11945, 1.04219, -1.0305, -0.01492, -1.46511, -1.14513, -0.91434, 0.47688, -0.68702, 0.53646, -0.64165, -0.0876, -0.83161, -0.69994, -0.45762, -0.45809, -0.2719, -0.4319, -0.31186, -0.52442, 0.09342, -0.259, 0.46545, -0.30287, 0.41645, -0.33892, 0.38751, 0.0428, 0.51343, 0.42488, 0.29115]}]}, "st": {"st": [{"offset": 24, "vertices": [-0.33852, -0.10051, -0.12772, -0.32921, -0.31129, -0.16675, 0, 0, 0, 0, 0, 0, -0.08391, -0.10448, 0.03126, -0.1303, -0.06111, -0.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23087, -0.04855, 0.02561, -0.23453, 0.03936, -0.36046, -0.23595, -0.16692, -0.43958, -0.048, 0, 0, 0.0179, -0.16397, 0.05062, -0.15698, 0, 0, 0, 0, 0, 0, 0, 0, -0.28259, -0.03086, -0.28259, -0.03086, -0.14856, -0.24235, -0.27055, -0.08724, -0.35224, 0.02508, -0.23543, -0.26318, -0.35006, -0.04651, -0.25119, -0.02743, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0394, -0.36074, 0.11132, -0.34538, 0.01433, -0.13118, 0.1125, -0.06896, 0.04049, -0.12559], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 24, "vertices": [-7.9553, -2.36194, -3.00136, -7.73655, -7.31525, -3.91864, 0, 0, 0, 0, 0, 0, -1.97198, -2.4553, 0.73471, -3.06194, -1.43599, -2.80266, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.42552, -1.14093, 0.60179, -5.51137, 0.92506, -8.47076, -5.54483, -3.92268, -10.33005, -1.12797, 0, 0, 0.42074, -3.85323, 1.18949, -3.6891, 0, 0, 0, 0, 0, 0, 0, 0, -6.6408, -0.72509, -6.6408, -0.72509, -3.49124, -5.6952, -6.3579, -2.05018, -8.27759, 0.58947, -5.53255, -6.18483, -8.22639, -1.09291, -5.90301, -0.64457, -5.90301, -0.64457, -3.10329, -5.06239, -5.65144, -1.8224, -5.90301, -0.64457, -3.10329, -5.06239, -5.65144, -1.8224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.92599, -8.47748, 2.61601, -8.11654, 0.33665, -3.08263, 2.64377, -1.62052, 0.95149, -2.95134], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "offset": 24, "vertices": [-0.33852, -0.10051, -0.12772, -0.32921, -0.31129, -0.16675, 0, 0, 0, 0, 0, 0, -0.08391, -0.10448, 0.03126, -0.1303, -0.06111, -0.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23087, -0.04855, 0.02561, -0.23453, 0.03936, -0.36046, -0.23595, -0.16692, -0.43958, -0.048, 0, 0, 0.0179, -0.16397, 0.05062, -0.15698, 0, 0, 0, 0, 0, 0, 0, 0, -0.28259, -0.03086, -0.28259, -0.03086, -0.14856, -0.24235, -0.27055, -0.08724, -0.35224, 0.02508, -0.23543, -0.26318, -0.35006, -0.04651, -0.25119, -0.02743, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0394, -0.36074, 0.11132, -0.34538, 0.01433, -0.13118, 0.1125, -0.06896, 0.04049, -0.12559], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "offset": 24, "vertices": [-7.9553, -2.36194, -3.00136, -7.73655, -7.31525, -3.91864, 0, 0, 0, 0, 0, 0, -1.97198, -2.4553, 0.73471, -3.06194, -1.43599, -2.80266, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.42552, -1.14093, 0.60179, -5.51137, 0.92506, -8.47076, -5.54483, -3.92268, -10.33005, -1.12797, 0, 0, 0.42074, -3.85323, 1.18949, -3.6891, 0, 0, 0, 0, 0, 0, 0, 0, -6.6408, -0.72509, -6.6408, -0.72509, -3.49124, -5.6952, -6.3579, -2.05018, -8.27759, 0.58947, -5.53255, -6.18483, -8.22639, -1.09291, -5.90301, -0.64457, -5.90301, -0.64457, -3.10329, -5.06239, -5.65144, -1.8224, -5.90301, -0.64457, -3.10329, -5.06239, -5.65144, -1.8224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.92599, -8.47748, 2.61601, -8.11654, 0.33665, -3.08263, 2.64377, -1.62052, 0.95149, -2.95134], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "offset": 24, "vertices": [-0.33852, -0.10051, -0.12772, -0.32921, -0.31129, -0.16675, 0, 0, 0, 0, 0, 0, -0.08391, -0.10448, 0.03126, -0.1303, -0.06111, -0.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23087, -0.04855, 0.02561, -0.23453, 0.03936, -0.36046, -0.23595, -0.16692, -0.43958, -0.048, 0, 0, 0.0179, -0.16397, 0.05062, -0.15698, 0, 0, 0, 0, 0, 0, 0, 0, -0.28259, -0.03086, -0.28259, -0.03086, -0.14856, -0.24235, -0.27055, -0.08724, -0.35224, 0.02508, -0.23543, -0.26318, -0.35006, -0.04651, -0.25119, -0.02743, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, -0.25119, -0.02743, -0.13206, -0.21542, -0.24049, -0.07755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0394, -0.36074, 0.11132, -0.34538, 0.01433, -0.13118, 0.1125, -0.06896, 0.04049, -0.12559]}]}, "t4": {"t4": [{"curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 20, "vertices": [2.93847, 0.36981, 3.05876, -0.49347, 3.05876, -0.49347, 3.05876, -0.49347, 3.05876, -0.49347, 3.05876, -0.49347, 2.93847, 0.36981, 2.93847, 0.36981, 0, 0, 0, 0, 0, 0, 4.84141, 0.42664, 4.25396, 0.30115], "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "vertices": [2.10067, 9.29126, 1.61528, 7.54437, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.54664, 3.95801, 0.80325, 6.35944, -0.60095, 10.58679, 0.8122, 4.32141, -2.01556, 0.95795, -1.92178, -2.52826, -0.06393, -1.20886, 0.79285, 2.75806, 0.52771, 4.1839, 0.95454, 11.06836, -0.20436, 12.84546, -1.81922, 7.44684, -1.15986, 11.96362, 0.63502, 12.0766, 1.43684, 9.27368, 2.32929, 12.50177, -1.18621, -3.43671, -1.09255, -5.49414, -0.20629, -3.72534], "curve": 0.25, "c3": 0.75}, {"time": 4.8333}]}, "t3": {"t3": [{"curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 20, "vertices": [-3.94296, 0.49988, -2.4281, 0.11523, -2.4281, 0.11523, -2.87051, 1.01038, 0, 0, 0, 0, -2.09412, -0.45319], "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "vertices": [-0.65472, -6.99591, 0, 0, 0, 0, -0.36868, 2.5047, -0.36868, 2.5047, 0, 0, 0, 0, 2.58047, -2.72003, -3.58762, -8.31702, 0.26428, -9.26117, -5.29523, -13.34723, -3.90411, -7.56708, -0.73172, -1.9502, 0.31302, 1.34991, 0, 0, -2.22467, -5.98749, -5.05984, -11.51593, -3.87897, -11.24103, -5.08307, -14.24908, -6.0965, -12.26404, -0.85608, -1.5177, -0.16858, 4.03375, 0.48279, 2.54242, 0.53833, 3.10364, 0.49039, 1.66193], "curve": 0.25, "c3": 0.75}, {"time": 4.8333}]}, "t1": {"t1": [{"offset": 6, "vertices": [0.06346, -0.06025, 0.05458, 0.16459, -0.06764, 0.43771, 0.07837, 0.07036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13282, 0.40073, 0.09567, 0.33097, 0.05925, 0.33296, 0.02629, 0.32399, 0.07839, 0.19958, 0.03901, 0.20556, -0.04905, 0.21303, -0.04469, 0.2145, 0.01399, 0.28076, 0.01313, 0.25436, 0.01313, 0.25436, 0.04843, 0.24528, 0.01313, 0.25436, 0.05141, 0.15676, 0.05113, 0.19561, 0.00464, 0.26257, -0.03857, 0.25773, -0.02429, 0.25696], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 6, "vertices": [1.49127, -1.41582, 1.28271, 3.86789, -1.58954, 10.28619, 1.84174, 1.65356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.12128, 9.41725, 2.24835, 7.77778, 1.39233, 7.82453, 0.61786, 7.61375, 1.8421, 4.69017, 0.91675, 4.83077, -1.15259, 5.00621, -1.05011, 5.04073, 0.32874, 6.59782, 0.30847, 5.97747, 0.30853, 5.97743, 1.13812, 5.76416, 0.30853, 5.97743, 2.22327, 3.6909, 1.20154, 4.59673, -0.90613, 6.16335, -0.86407, 6.05706, 0.52881, 6.04602], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3333, "offset": 6, "vertices": [0.06346, -0.06025, 0.05458, 0.16459, -0.06764, 0.43771, 0.07837, 0.07036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13282, 0.40073, 0.09567, 0.33097, 0.05925, 0.33296, 0.02629, 0.32399, 0.07839, 0.19958, 0.03901, 0.20556, -0.04905, 0.21303, -0.04469, 0.2145, 0.01399, 0.28076, 0.01313, 0.25436, 0.01313, 0.25436, 0.04843, 0.24528, 0.01313, 0.25436, 0.05141, 0.15676, 0.05113, 0.19561, 0.00464, 0.26257, -0.03857, 0.25773, -0.02429, 0.25696], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "offset": 6, "vertices": [1.49127, -1.41582, 1.28271, 3.86789, -1.58954, 10.28619, 1.84174, 1.65356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.12128, 9.41725, 2.24835, 7.77778, 1.39233, 7.82453, 0.61786, 7.61375, 1.8421, 4.69017, 0.91675, 4.83077, -1.15259, 5.00621, -1.05011, 5.04073, 0.32874, 6.59782, 0.30847, 5.97747, 0.30853, 5.97743, 1.13812, 5.76416, 0.30853, 5.97743, 2.22327, 3.6909, 1.20154, 4.59673, -0.90613, 6.16335, -0.86407, 6.05706, 0.52881, 6.04602], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "offset": 6, "vertices": [0.06346, -0.06025, 0.05458, 0.16459, -0.06764, 0.43771, 0.07837, 0.07036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13282, 0.40073, 0.09567, 0.33097, 0.05925, 0.33296, 0.02629, 0.32399, 0.07839, 0.19958, 0.03901, 0.20556, -0.04905, 0.21303, -0.04469, 0.2145, 0.01399, 0.28076, 0.01313, 0.25436, 0.01313, 0.25436, 0.04843, 0.24528, 0.01313, 0.25436, 0.05141, 0.15676, 0.05113, 0.19561, 0.00464, 0.26257, -0.03857, 0.25773, -0.02429, 0.25696]}]}, "ys": {"ys": [{"offset": 10, "vertices": [2.0726, 0.14207, -1.21023, -1.68863, 2.31651, 0.55242, -1.04905, -2.13802, 1.9565, -0.89355, -1.9348, -0.93971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.49368, -1.53677, 0.84908, -1.37274, 1.15492, -1.12761, 1.30346, -0.95203, 1.59402, -0.67728, 1.6884, -0.38575, -0.87066, 1.35917, -0.49499, -1.54079, 0.85129, -1.37634, 1.15794, -1.13059, 1.30687, -0.95453, 1.59818, -0.67906, 1.6928, -0.38677, -0.87293, 1.36273, -0.87136, -2.71237, 1.49861, -2.42287, 2.0384, -1.99024, 2.30059, -1.68034, 2.81342, -1.1954, 2.97998, -0.68086, -0.81721, -2.54384, 1.40549, -2.27232, 1.91175, -1.86658, 2.15763, -1.57592, 2.63859, -1.12112, 2.79484, -0.63856, -1.44121, 2.24986, -0.37895, -1.17961, 0.65175, -1.0537, 0.8865, -0.86556, 1.00053, -0.73079, 1.22356, -0.51989, 1.296, -0.29611, -0.66831, 1.0433, -0.4331, -1.34814, 0.74486, -1.20425, 1.01315, -0.98923, 1.14348, -0.8352, 1.39837, -0.59416, 1.48115, -0.33842, -0.76379, 1.19235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.65444, 2.03708, -1.53094, 1.49476, -1.72784, 1.26202, -2.11304, 0.89782, -2.23823, 0.5114, 1.15405, -1.80174, 0.94552, 2.94318, -2.49639, 1.82336, -3.05293, 1.29715, -3.23376, 0.73885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.6572, -1.15185, 2.83834, 0.57302, 2.68826, 1.07751, 2.60049, 1.27467, 2.21662, 1.8638, 1.92926, 2.16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98946, -0.13178, -1.36841, -1.45015, 1.98946, -0.13178, 1.70787, 1.02862, 1.49359, 1.32084, 1.23908, -0.29708, -1.01806, -0.76629, 1.45731, -0.20135, 1.31099, 0.66748, 1.16856, 0.89379, 0.55153, 0.16903, 0.51185, 0.26609, 0.49061, 0.30346, 0.40301, 0.41276, 0.34026, 0.46585, 1.77055, 0.54264, 1.64329, 0.85425, 1.57509, 0.97423, 1.29385, 1.32511, 1.09239, 1.49557, 0.84545, 1.68109, 1.96636, -0.09648, 1.95162, 0.26116, 1.92671, 0.40575, 1.77213, 0.85813, 1.63219, 1.10131, 1.50557, 1.40061, -1.92414, 0.41769, 1.41921, 0.62639, 1.28258, 0.87302, 1.21399, 0.9661, 0.94536, 1.23018, 0.76094, 1.35208, 0.52256, 1.47524, -1.50291, -0.38513, 1.70583, 1.35496, 1.43268, 1.64146, 1.30641, 1.74357, 0.84769, 2.00702, 0.55385, 2.10715, 0.14017, 2.17488, -1.90524, -1.05682, 0.2823, -0.92148, 0.44455, -0.85513, 0.50696, -0.81965, 0.68959, -0.67331, 0.77828, -0.56847, 0.95177, -0.4044, 1.00811, -0.23033, -0.1274, 0.9553, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 0.27823, 0.93645, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 1.25909, 1.48487, 0.9697, 1.68833, 0.84127, 1.75585, 0.39326, 1.90681, 0.11829, 1.94337, 1.0003, 0.28457, 0.65821, 0.80511, 0.46769, 1.45591, -0.44809, 1.46207, 0.46769, 1.45591, 0.82477, -1.28768, 2.19456, -0.70503, -1.94101, -1.24331, 0.22531, -0.73543, 0.35479, -0.68248, 0.4046, -0.65416, 0.55036, -0.53737, 0.62114, -0.45369, 0.7596, -0.32276, 0.80457, -0.18383, -0.10168, 0.76242, 1.27964, 0.66523, 1.22655, 0.75865, 1.00754, 1.03192, 0.85065, 1.16465, 0.65838, 1.30911, 0.41736, 1.40466, 2.01826, -0.64838, 1.25033, 1.71186, -1.78506, -1.14342, 1.69155, 0.56504, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 0.78882, 1.59948, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 0.66752, 1.65377, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 1.69155, 0.56504, -0.64139, -1.66409, 1.48729, 0.45598, -0.59543, -1.43722, 0, 0, 0, 0, 0.4046, -0.65416, 0.55035, -0.53736, 0.62114, -0.45368, 0.7596, -0.32275, 0.80457, -0.18383, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, 1.3753, -0.44182, 1.00914, 1.03354, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, -1.21637, -0.77915, 0.98604, 2.19215, -1.02839, 2.17269, -1.52176, 1.86072, -1.77075, 1.62556, -2.23659, 1.24584, 1.06245, -2.15619, 1.36913, 2.06908, -0.5067, 2.4288, -0.68616, 2.38435, -1.24065, 2.14863, -1.53341, 1.9505, -2.0529, 1.61109, 0.72361, -2.37323, 1.62453, 1.98703, -0.26868, 2.55254, -0.45802, 2.52547, -1.05324, 2.34057, -1.37518, 2.16713, 0.49771, -2.51793, 1.87994, 1.90498, 0.45411, 2.63761, -0.03065, 2.67627, -0.22987, 2.66658, -0.86583, 2.53252, 0.27182, -2.66262, 2.13538, 1.82291, 0.04592, -2.80731, 1.12762, 0.87873, -0.04048, -1.42907, 0, 0, 0, 0, 0, 0, 0, 0, 0.73064, 2.27419, -1.70917, 1.66877, -1.92897, 1.40893, -2.35905, 1.00233, -2.49881, 0.57094, 1.28835, -2.01149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.54136, -1.68517, 0.81643, -1.57046, 0.93107, -1.5053, 1.26644, -1.23653, 1.42934, -1.04399, 1.74795, -0.7427, -0.95473, 1.49044, -0.05411, -0.16855, 0.08169, -0.15704, 0.09311, -0.1505, 0.12664, -0.12366, -0.48981, -1.52473, 0.73872, -1.42091, 0.84241, -1.36195, 1.14585, -1.11879, 1.29324, -0.94458, -0.70508, -2.19477, 1.06332, -2.04534, 1.21262, -1.96048, 1.64939, -1.61045, 1.86156, -1.35968, 2.2765, -0.96729, -0.27069, -0.84259, -0.47737, 0.74522, -0.6548, -2.03829, 1.12618, -1.82072, 1.53182, -1.49563, 1.72885, -1.26273, -1.1548, 1.80274, -0.97833, -3.04538, 1.68261, -2.72033, 2.28866, -2.2346, 2.58305, -1.88665, -1.72537, 2.69345, -0.27325, -0.85062, -0.48192, 0.75232, -0.77063, 1.20301, -0.70765, -2.20279, 1.65544, -1.61633, -1.24799, 1.94822, -0.65739, -2.04636, 1.13064, -1.82792, 1.53787, -1.50153, 1.73568, -1.26771, -1.15938, 1.80985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.29729, 0.0249, 0.80668, -1.01615, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, 1.20417, 0.67689, -1.29729, 0.0249], "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "offset": 10, "vertices": [8.55733, 0.58656, -4.99678, -6.972, 9.56439, 2.28084, -4.3313, -8.82741, 8.07795, -3.68929, -7.9884, -3.87985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0383, -6.345, 3.50566, -5.66776, 4.76842, -4.65567, 5.38171, -3.93073, 6.58135, -2.79633, 6.97102, -1.59268, -3.59476, 5.61171, -2.0437, -6.3616, 3.5148, -5.68262, 4.78088, -4.66795, 5.39579, -3.94104, 6.59854, -2.8037, 6.98923, -1.59691, -3.60416, 5.62643, -3.59766, -11.19881, 6.18742, -10.00349, 8.41614, -8.21729, 9.49863, -6.93776, 11.61599, -4.93553, 12.30371, -2.81113, -3.37408, -10.50298, 5.80296, -9.38193, 7.89321, -7.70671, 8.90841, -6.50665, 10.89418, -4.62888, 11.53929, -2.63646, -5.95044, 9.28919, -1.56461, -4.87036, 2.69092, -4.35051, 3.66017, -3.57373, 4.13097, -3.01727, 5.05181, -2.1465, 5.35089, -1.22258, -2.75931, 4.30756, -1.78818, -5.56619, 3.07536, -4.97208, 4.18309, -4.0843, 4.72117, -3.44835, 5.77358, -2.45317, 6.11537, -1.39725, -3.15353, 4.92298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.70204, 8.41068, -6.32094, 6.17156, -7.13388, 5.21063, -8.72431, 3.7069, -9.24116, 2.11144, 4.76481, -7.43899, 3.90386, 12.15177, -10.30704, 7.52827, -12.60487, 5.35567, -13.35151, 3.05054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.38126, -1.25197, 0.56236, -4.21362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.97099, -4.75574, 11.71891, 2.36588, 11.09925, 4.4488, 10.73688, 5.26284, 9.15197, 7.69525, 7.96551, 8.91818, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.21404, -0.54408, -5.64986, -5.98737, 8.21404, -0.54408, 7.05144, 4.24694, 6.1667, 5.45346, 5.11588, -1.22657, -4.20334, -3.16385, 6.01694, -0.83135, 5.41279, 2.75586, 4.82472, 3.69026, 2.27716, 0.69787, 2.11334, 1.09862, 2.02564, 1.25291, 1.66395, 1.7042, 1.40486, 1.92339, 7.31023, 2.24043, 6.7848, 3.52701, 6.50319, 4.0224, 5.34203, 5.47111, 4.51025, 6.17489, 3.49069, 6.94088, 8.11869, -0.39833, 8.0578, 1.07826, 7.95497, 1.67525, 7.31675, 3.54303, 6.73896, 4.54707, 6.21619, 5.78284, -7.94435, 1.72456, 5.16194, 4.8615, 4.19341, 5.71349, 3.76788, 6.0172, 2.21984, 6.76118, 1.24923, 7.02529, -0.25516, 7.15389, -5.89029, -3.94892, 7.04301, 5.59433, 5.91522, 6.77723, 5.3939, 7.19884, 3.49992, 8.28654, 2.28675, 8.69998, 0.57872, 8.97961, -7.86633, -4.36339, 1.16556, -3.80461, 1.83544, -3.53064, 2.09313, -3.38416, 2.84718, -2.77996, 3.21336, -2.34707, 3.92966, -1.66969, 4.16228, -0.95099, -0.52603, 3.94424, 3.79517, 1.16314, 3.52229, 1.83107, 3.37613, 2.08823, 2.7733, 2.84036, 2.34149, 3.20575, 1.81219, 3.60339, 1.14876, 3.86642, 3.79517, 1.16314, 3.52229, 1.83107, 3.37613, 2.08823, 2.7733, 2.84036, 2.34149, 3.20575, 1.81219, 3.60339, 5.19853, 6.13073, 4.00367, 6.97073, 3.47342, 7.24952, 1.62371, 7.87279, 0.48838, 8.02377, 4.13001, 1.17495, 2.71762, 3.32411, 1.93099, 6.01114, -1.85007, 6.03657, 1.93099, 6.01114, 3.40528, -5.31654, 9.06089, -2.91092, -8.014, -5.13335, 0.93025, -3.03645, 1.46485, -2.81781, 1.6705, -2.7009, 2.27231, -2.21868, 2.56456, -1.8732, 3.13624, -1.3326, 3.32189, -0.75901, -0.41981, 3.14789, 5.28338, 2.74657, 5.06416, 3.13231, 4.15991, 4.26057, 3.51216, 4.80858, 2.71832, 5.40505, 1.7232, 5.79956, 8.33298, -2.67703, 5.16235, 7.06789, -7.37012, -4.72093, 6.98403, 2.33293, 2.75605, 6.82808, 1.02907, 7.29086, -2.64818, -6.87068, 6.98403, 2.33293, 3.25687, 6.60392, 2.75605, 6.82808, 1.02907, 7.29086, -2.64818, -6.87068, 6.98403, 2.33293, 4.39758, 5.90512, 3.25687, 6.60392, 2.75605, 6.82808, 6.98403, 2.33293, 4.39758, 5.90512, 3.25687, 6.60392, 6.98403, 2.33293, -2.64818, -6.87068, 6.14072, 1.88263, -2.4584, -5.93399, 0, 0, 0, 0, 1.67051, -2.70088, 2.2723, -2.21863, 2.56455, -1.87316, 3.13623, -1.33256, 3.32191, -0.759, 3.51778, 4.81621, 2.7226, 5.41364, 1.72587, 5.80879, 5.67831, -1.82417, 4.16653, 4.26726, 3.51778, 4.81621, 2.7226, 5.41364, 1.72587, 5.80879, -5.02215, -3.21695, 4.07113, 9.05091, -4.24602, 8.97057, -6.28302, 7.6825, -7.31104, 6.71159, -9.23442, 5.14379, 4.38662, -8.90243, 5.65284, 8.54277, -2.09205, 10.028, -2.83302, 9.84448, -5.12236, 8.87124, -6.33112, 8.05321, -8.47599, 6.65185, 2.98762, -9.79856, 6.70733, 8.20402, -1.10932, 10.53888, -1.89107, 10.42712, -4.3486, 9.66373, -5.67784, 8.94763, 2.05494, -10.39598, 7.76186, 7.86527, 1.87491, 10.89012, -0.12656, 11.04976, -0.9491, 11.00974, -3.57483, 10.45622, 1.12227, -10.9934, 8.81653, 7.5264, 0.18961, -11.5908, 4.65569, 3.6281, -0.16712, -5.90034, 0, 0, 0, 0, 0, 0, 0, 0, 3.01665, 9.38966, -7.05679, 6.89, -7.96432, 5.81718, -9.74003, 4.13842, -10.31705, 2.35731, 5.31931, -8.305, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.23517, -6.95772, 3.37086, -6.48409, 3.84419, -6.21507, 5.22887, -5.10536, 5.90143, -4.31041, 7.21693, -3.06645, -3.94188, 6.1537, -0.22339, -0.69591, 0.33727, -0.64839, 0.38441, -0.6214, 0.52287, -0.51056, -2.02231, -6.2953, 3.05, -5.86664, 3.47815, -5.6232, 4.73097, -4.61926, 5.33952, -3.89996, -2.91113, -9.06172, 4.39023, -8.44479, 5.00665, -8.09441, 6.80998, -6.64919, 7.68597, -5.61382, 9.39918, -3.99371, -1.11761, -3.47887, -1.97096, 3.07687, -2.70352, -8.41568, 4.64977, -7.51738, 6.32456, -6.17513, 7.13804, -5.21355, -4.76793, 7.44312, -4.03931, -12.57373, 6.94713, -11.23164, 9.44938, -9.2262, 10.66484, -7.78957, -7.1237, 11.1207, -1.1282, -3.51204, -1.98973, 3.10615, -3.18175, 4.96696, -2.92172, -9.09483, 6.83494, -6.67346, -5.15268, 8.04378, -2.7142, -8.44897, 4.66815, -7.5471, 6.34953, -6.19951, 7.16624, -5.23412, -4.78682, 7.47249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.35622, 0.10279, 3.3306, -4.19545, -5.35622, 0.10279, 3.3306, -4.19545, 5.35364, -0.18742, 5.24089, 1.10843, 5.03027, 1.842, -5.35622, 0.10279, 3.3306, -4.19545, 5.35364, -0.18742, 5.24089, 1.10843, 5.03027, 1.842, 4.97175, 2.79474, -5.35622, 0.10279], "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "offset": 10, "vertices": [2.0726, 0.14207, -1.21023, -1.68863, 2.31651, 0.55242, -1.04905, -2.13802, 1.9565, -0.89355, -1.9348, -0.93971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.49368, -1.53677, 0.84908, -1.37274, 1.15492, -1.12761, 1.30346, -0.95203, 1.59402, -0.67728, 1.6884, -0.38575, -0.87066, 1.35917, -0.49499, -1.54079, 0.85129, -1.37634, 1.15794, -1.13059, 1.30687, -0.95453, 1.59818, -0.67906, 1.6928, -0.38677, -0.87293, 1.36273, -0.87136, -2.71237, 1.49861, -2.42287, 2.0384, -1.99024, 2.30059, -1.68034, 2.81342, -1.1954, 2.97998, -0.68086, -0.81721, -2.54384, 1.40549, -2.27232, 1.91175, -1.86658, 2.15763, -1.57592, 2.63859, -1.12112, 2.79484, -0.63856, -1.44121, 2.24986, -0.37895, -1.17961, 0.65175, -1.0537, 0.8865, -0.86556, 1.00053, -0.73079, 1.22356, -0.51989, 1.296, -0.29611, -0.66831, 1.0433, -0.4331, -1.34814, 0.74486, -1.20425, 1.01315, -0.98923, 1.14348, -0.8352, 1.39837, -0.59416, 1.48115, -0.33842, -0.76379, 1.19235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.65444, 2.03708, -1.53094, 1.49476, -1.72784, 1.26202, -2.11304, 0.89782, -2.23823, 0.5114, 1.15405, -1.80174, 0.94552, 2.94318, -2.49639, 1.82336, -3.05293, 1.29715, -3.23376, 0.73885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.6572, -1.15185, 2.83834, 0.57302, 2.68826, 1.07751, 2.60049, 1.27467, 2.21662, 1.8638, 1.92926, 2.16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98946, -0.13178, -1.36841, -1.45015, 1.98946, -0.13178, 1.70787, 1.02862, 1.49359, 1.32084, 1.23908, -0.29708, -1.01806, -0.76629, 1.45731, -0.20135, 1.31099, 0.66748, 1.16856, 0.89379, 0.55153, 0.16903, 0.51185, 0.26609, 0.49061, 0.30346, 0.40301, 0.41276, 0.34026, 0.46585, 1.77055, 0.54264, 1.64329, 0.85425, 1.57509, 0.97423, 1.29385, 1.32511, 1.09239, 1.49557, 0.84545, 1.68109, 1.96636, -0.09648, 1.95162, 0.26116, 1.92671, 0.40575, 1.77213, 0.85813, 1.63219, 1.10131, 1.50557, 1.40061, -1.92414, 0.41769, 1.41921, 0.62639, 1.28258, 0.87302, 1.21399, 0.9661, 0.94536, 1.23018, 0.76094, 1.35208, 0.52256, 1.47524, -1.50291, -0.38513, 1.70583, 1.35496, 1.43268, 1.64146, 1.30641, 1.74357, 0.84769, 2.00702, 0.55385, 2.10715, 0.14017, 2.17488, -1.90524, -1.05682, 0.2823, -0.92148, 0.44455, -0.85513, 0.50696, -0.81965, 0.68959, -0.67331, 0.77828, -0.56847, 0.95177, -0.4044, 1.00811, -0.23033, -0.1274, 0.9553, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 0.27823, 0.93645, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 1.25909, 1.48487, 0.9697, 1.68833, 0.84127, 1.75585, 0.39326, 1.90681, 0.11829, 1.94337, 1.0003, 0.28457, 0.65821, 0.80511, 0.46769, 1.45591, -0.44809, 1.46207, 0.46769, 1.45591, 0.82477, -1.28768, 2.19456, -0.70503, -1.94101, -1.24331, 0.22531, -0.73543, 0.35479, -0.68248, 0.4046, -0.65416, 0.55036, -0.53737, 0.62114, -0.45369, 0.7596, -0.32276, 0.80457, -0.18383, -0.10168, 0.76242, 1.27964, 0.66523, 1.22655, 0.75865, 1.00754, 1.03192, 0.85065, 1.16465, 0.65838, 1.30911, 0.41736, 1.40466, 2.01826, -0.64838, 1.25033, 1.71186, -1.78506, -1.14342, 1.69155, 0.56504, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 0.78882, 1.59948, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 0.66752, 1.65377, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 1.69155, 0.56504, -0.64139, -1.66409, 1.48729, 0.45598, -0.59543, -1.43722, 0, 0, 0, 0, 0.4046, -0.65416, 0.55035, -0.53736, 0.62114, -0.45368, 0.7596, -0.32275, 0.80457, -0.18383, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, 1.3753, -0.44182, 1.00914, 1.03354, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, -1.21637, -0.77915, 0.98604, 2.19215, -1.02839, 2.17269, -1.52176, 1.86072, -1.77075, 1.62556, -2.23659, 1.24584, 1.06245, -2.15619, 1.36913, 2.06908, -0.5067, 2.4288, -0.68616, 2.38435, -1.24065, 2.14863, -1.53341, 1.9505, -2.0529, 1.61109, 0.72361, -2.37323, 1.62453, 1.98703, -0.26868, 2.55254, -0.45802, 2.52547, -1.05324, 2.34057, -1.37518, 2.16713, 0.49771, -2.51793, 1.87994, 1.90498, 0.45411, 2.63761, -0.03065, 2.67627, -0.22987, 2.66658, -0.86583, 2.53252, 0.27182, -2.66262, 2.13538, 1.82291, 0.04592, -2.80731, 1.12762, 0.87873, -0.04048, -1.42907, 0, 0, 0, 0, 0, 0, 0, 0, 0.73064, 2.27419, -1.70917, 1.66877, -1.92897, 1.40893, -2.35905, 1.00233, -2.49881, 0.57094, 1.28835, -2.01149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.54136, -1.68517, 0.81643, -1.57046, 0.93107, -1.5053, 1.26644, -1.23653, 1.42934, -1.04399, 1.74795, -0.7427, -0.95473, 1.49044, -0.05411, -0.16855, 0.08169, -0.15704, 0.09311, -0.1505, 0.12664, -0.12366, -0.48981, -1.52473, 0.73872, -1.42091, 0.84241, -1.36195, 1.14585, -1.11879, 1.29324, -0.94458, -0.70508, -2.19477, 1.06332, -2.04534, 1.21262, -1.96048, 1.64939, -1.61045, 1.86156, -1.35968, 2.2765, -0.96729, -0.27069, -0.84259, -0.47737, 0.74522, -0.6548, -2.03829, 1.12618, -1.82072, 1.53182, -1.49563, 1.72885, -1.26273, -1.1548, 1.80274, -0.97833, -3.04538, 1.68261, -2.72033, 2.28866, -2.2346, 2.58305, -1.88665, -1.72537, 2.69345, -0.27325, -0.85062, -0.48192, 0.75232, -0.77063, 1.20301, -0.70765, -2.20279, 1.65544, -1.61633, -1.24799, 1.94822, -0.65739, -2.04636, 1.13064, -1.82792, 1.53787, -1.50153, 1.73568, -1.26771, -1.15938, 1.80985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.29729, 0.0249, 0.80668, -1.01615, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, 1.20417, 0.67689, -1.29729, 0.0249], "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "offset": 10, "vertices": [8.55733, 0.58656, -4.99678, -6.972, 9.56439, 2.28084, -4.3313, -8.82741, 8.07795, -3.68929, -7.9884, -3.87985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0383, -6.345, 3.50566, -5.66776, 4.76842, -4.65567, 5.38171, -3.93073, 6.58135, -2.79633, 6.97102, -1.59268, -3.59476, 5.61171, -2.0437, -6.3616, 3.5148, -5.68262, 4.78088, -4.66795, 5.39579, -3.94104, 6.59854, -2.8037, 6.98923, -1.59691, -3.60416, 5.62643, -3.59766, -11.19881, 6.18742, -10.00349, 8.41614, -8.21729, 9.49863, -6.93776, 11.61599, -4.93553, 12.30371, -2.81113, -3.37408, -10.50298, 5.80296, -9.38193, 7.89321, -7.70671, 8.90841, -6.50665, 10.89418, -4.62888, 11.53929, -2.63646, -5.95044, 9.28919, -1.56461, -4.87036, 2.69092, -4.35051, 3.66017, -3.57373, 4.13097, -3.01727, 5.05181, -2.1465, 5.35089, -1.22258, -2.75931, 4.30756, -1.78818, -5.56619, 3.07536, -4.97208, 4.18309, -4.0843, 4.72117, -3.44835, 5.77358, -2.45317, 6.11537, -1.39725, -3.15353, 4.92298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.70204, 8.41068, -6.32094, 6.17156, -7.13388, 5.21063, -8.72431, 3.7069, -9.24116, 2.11144, 4.76481, -7.43899, 3.90386, 12.15177, -10.30704, 7.52827, -12.60487, 5.35567, -13.35151, 3.05054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.38126, -1.25197, 0.56236, -4.21362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.97099, -4.75574, 11.71891, 2.36588, 11.09925, 4.4488, 10.73688, 5.26284, 9.15197, 7.69525, 7.96551, 8.91818, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.21404, -0.54408, -5.64986, -5.98737, 8.21404, -0.54408, 7.05144, 4.24694, 6.1667, 5.45346, 5.11588, -1.22657, -4.20334, -3.16385, 6.01694, -0.83135, 5.41279, 2.75586, 4.82472, 3.69026, 2.27716, 0.69787, 2.11334, 1.09862, 2.02564, 1.25291, 1.66395, 1.7042, 1.40486, 1.92339, 7.31023, 2.24043, 6.7848, 3.52701, 6.50319, 4.0224, 5.34203, 5.47111, 4.51025, 6.17489, 3.49069, 6.94088, 8.11869, -0.39833, 8.0578, 1.07826, 7.95497, 1.67525, 7.31675, 3.54303, 6.73896, 4.54707, 6.21619, 5.78284, -7.94435, 1.72456, 5.16194, 4.8615, 4.19341, 5.71349, 3.76788, 6.0172, 2.21984, 6.76118, 1.24923, 7.02529, -0.25516, 7.15389, -5.89029, -3.94892, 7.04301, 5.59433, 5.91522, 6.77723, 5.3939, 7.19884, 3.49992, 8.28654, 2.28675, 8.69998, 0.57872, 8.97961, -7.86633, -4.36339, 1.16556, -3.80461, 1.83544, -3.53064, 2.09313, -3.38416, 2.84718, -2.77996, 3.21336, -2.34707, 3.92966, -1.66969, 4.16228, -0.95099, -0.52603, 3.94424, 3.79517, 1.16314, 3.52229, 1.83107, 3.37613, 2.08823, 2.7733, 2.84036, 2.34149, 3.20575, 1.81219, 3.60339, 1.14876, 3.86642, 3.79517, 1.16314, 3.52229, 1.83107, 3.37613, 2.08823, 2.7733, 2.84036, 2.34149, 3.20575, 1.81219, 3.60339, 5.19853, 6.13073, 4.00367, 6.97073, 3.47342, 7.24952, 1.62371, 7.87279, 0.48838, 8.02377, 4.13001, 1.17495, 2.71762, 3.32411, 1.93099, 6.01114, -1.85007, 6.03657, 1.93099, 6.01114, 3.40528, -5.31654, 9.06089, -2.91092, -8.014, -5.13335, 0.93025, -3.03645, 1.46485, -2.81781, 1.6705, -2.7009, 2.27231, -2.21868, 2.56456, -1.8732, 3.13624, -1.3326, 3.32189, -0.75901, -0.41981, 3.14789, 5.28338, 2.74657, 5.06416, 3.13231, 4.15991, 4.26057, 3.51216, 4.80858, 2.71832, 5.40505, 1.7232, 5.79956, 8.33298, -2.67703, 5.16235, 7.06789, -7.37012, -4.72093, 6.98403, 2.33293, 2.75605, 6.82808, 1.02907, 7.29086, -2.64818, -6.87068, 6.98403, 2.33293, 3.25687, 6.60392, 2.75605, 6.82808, 1.02907, 7.29086, -2.64818, -6.87068, 6.98403, 2.33293, 4.39758, 5.90512, 3.25687, 6.60392, 2.75605, 6.82808, 6.98403, 2.33293, 4.39758, 5.90512, 3.25687, 6.60392, 6.98403, 2.33293, -2.64818, -6.87068, 6.14072, 1.88263, -2.4584, -5.93399, 0, 0, 0, 0, 1.67051, -2.70088, 2.2723, -2.21863, 2.56455, -1.87316, 3.13623, -1.33256, 3.32191, -0.759, 3.51778, 4.81621, 2.7226, 5.41364, 1.72587, 5.80879, 5.67831, -1.82417, 4.16653, 4.26726, 3.51778, 4.81621, 2.7226, 5.41364, 1.72587, 5.80879, -5.02215, -3.21695, 4.07113, 9.05091, -4.24602, 8.97057, -6.28302, 7.6825, -7.31104, 6.71159, -9.23442, 5.14379, 4.38662, -8.90243, 5.65284, 8.54277, -2.09205, 10.028, -2.83302, 9.84448, -5.12236, 8.87124, -6.33112, 8.05321, -8.47599, 6.65185, 2.98762, -9.79856, 6.70733, 8.20402, -1.10932, 10.53888, -1.89107, 10.42712, -4.3486, 9.66373, -5.67784, 8.94763, 2.05494, -10.39598, 7.76186, 7.86527, 1.87491, 10.89012, -0.12656, 11.04976, -0.9491, 11.00974, -3.57483, 10.45622, 1.12227, -10.9934, 8.81653, 7.5264, 0.18961, -11.5908, 4.65569, 3.6281, -0.16712, -5.90034, 0, 0, 0, 0, 0, 0, 0, 0, 3.01665, 9.38966, -7.05679, 6.89, -7.96432, 5.81718, -9.74003, 4.13842, -10.31705, 2.35731, 5.31931, -8.305, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.23517, -6.95772, 3.37086, -6.48409, 3.84419, -6.21507, 5.22887, -5.10536, 5.90143, -4.31041, 7.21693, -3.06645, -3.94188, 6.1537, -0.22339, -0.69591, 0.33727, -0.64839, 0.38441, -0.6214, 0.52287, -0.51056, -2.02231, -6.2953, 3.05, -5.86664, 3.47815, -5.6232, 4.73097, -4.61926, 5.33952, -3.89996, -2.91113, -9.06172, 4.39023, -8.44479, 5.00665, -8.09441, 6.80998, -6.64919, 7.68597, -5.61382, 9.39918, -3.99371, -1.11761, -3.47887, -1.97096, 3.07687, -2.70352, -8.41568, 4.64977, -7.51738, 6.32456, -6.17513, 7.13804, -5.21355, -4.76793, 7.44312, -4.03931, -12.57373, 6.94713, -11.23164, 9.44938, -9.2262, 10.66484, -7.78957, -7.1237, 11.1207, -1.1282, -3.51204, -1.98973, 3.10615, -3.18175, 4.96696, -2.92172, -9.09483, 6.83494, -6.67346, -5.15268, 8.04378, -2.7142, -8.44897, 4.66815, -7.5471, 6.34953, -6.19951, 7.16624, -5.23412, -4.78682, 7.47249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.35622, 0.10279, 3.3306, -4.19545, -5.35622, 0.10279, 3.3306, -4.19545, 5.35364, -0.18742, 5.24089, 1.10843, 5.03027, 1.842, -5.35622, 0.10279, 3.3306, -4.19545, 5.35364, -0.18742, 5.24089, 1.10843, 5.03027, 1.842, 4.97175, 2.79474, -5.35622, 0.10279], "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "offset": 10, "vertices": [2.0726, 0.14207, -1.21023, -1.68863, 2.31651, 0.55242, -1.04905, -2.13802, 1.9565, -0.89355, -1.9348, -0.93971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.49368, -1.53677, 0.84908, -1.37274, 1.15492, -1.12761, 1.30346, -0.95203, 1.59402, -0.67728, 1.6884, -0.38575, -0.87066, 1.35917, -0.49499, -1.54079, 0.85129, -1.37634, 1.15794, -1.13059, 1.30687, -0.95453, 1.59818, -0.67906, 1.6928, -0.38677, -0.87293, 1.36273, -0.87136, -2.71237, 1.49861, -2.42287, 2.0384, -1.99024, 2.30059, -1.68034, 2.81342, -1.1954, 2.97998, -0.68086, -0.81721, -2.54384, 1.40549, -2.27232, 1.91175, -1.86658, 2.15763, -1.57592, 2.63859, -1.12112, 2.79484, -0.63856, -1.44121, 2.24986, -0.37895, -1.17961, 0.65175, -1.0537, 0.8865, -0.86556, 1.00053, -0.73079, 1.22356, -0.51989, 1.296, -0.29611, -0.66831, 1.0433, -0.4331, -1.34814, 0.74486, -1.20425, 1.01315, -0.98923, 1.14348, -0.8352, 1.39837, -0.59416, 1.48115, -0.33842, -0.76379, 1.19235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.65444, 2.03708, -1.53094, 1.49476, -1.72784, 1.26202, -2.11304, 0.89782, -2.23823, 0.5114, 1.15405, -1.80174, 0.94552, 2.94318, -2.49639, 1.82336, -3.05293, 1.29715, -3.23376, 0.73885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.6572, -1.15185, 2.83834, 0.57302, 2.68826, 1.07751, 2.60049, 1.27467, 2.21662, 1.8638, 1.92926, 2.16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98946, -0.13178, -1.36841, -1.45015, 1.98946, -0.13178, 1.70787, 1.02862, 1.49359, 1.32084, 1.23908, -0.29708, -1.01806, -0.76629, 1.45731, -0.20135, 1.31099, 0.66748, 1.16856, 0.89379, 0.55153, 0.16903, 0.51185, 0.26609, 0.49061, 0.30346, 0.40301, 0.41276, 0.34026, 0.46585, 1.77055, 0.54264, 1.64329, 0.85425, 1.57509, 0.97423, 1.29385, 1.32511, 1.09239, 1.49557, 0.84545, 1.68109, 1.96636, -0.09648, 1.95162, 0.26116, 1.92671, 0.40575, 1.77213, 0.85813, 1.63219, 1.10131, 1.50557, 1.40061, -1.92414, 0.41769, 1.41921, 0.62639, 1.28258, 0.87302, 1.21399, 0.9661, 0.94536, 1.23018, 0.76094, 1.35208, 0.52256, 1.47524, -1.50291, -0.38513, 1.70583, 1.35496, 1.43268, 1.64146, 1.30641, 1.74357, 0.84769, 2.00702, 0.55385, 2.10715, 0.14017, 2.17488, -1.90524, -1.05682, 0.2823, -0.92148, 0.44455, -0.85513, 0.50696, -0.81965, 0.68959, -0.67331, 0.77828, -0.56847, 0.95177, -0.4044, 1.00811, -0.23033, -0.1274, 0.9553, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 0.27823, 0.93645, 0.9192, 0.28172, 0.8531, 0.44349, 0.81771, 0.50577, 0.6717, 0.68794, 0.56711, 0.77644, 0.43892, 0.87275, 1.25909, 1.48487, 0.9697, 1.68833, 0.84127, 1.75585, 0.39326, 1.90681, 0.11829, 1.94337, 1.0003, 0.28457, 0.65821, 0.80511, 0.46769, 1.45591, -0.44809, 1.46207, 0.46769, 1.45591, 0.82477, -1.28768, 2.19456, -0.70503, -1.94101, -1.24331, 0.22531, -0.73543, 0.35479, -0.68248, 0.4046, -0.65416, 0.55036, -0.53737, 0.62114, -0.45369, 0.7596, -0.32276, 0.80457, -0.18383, -0.10168, 0.76242, 1.27964, 0.66523, 1.22655, 0.75865, 1.00754, 1.03192, 0.85065, 1.16465, 0.65838, 1.30911, 0.41736, 1.40466, 2.01826, -0.64838, 1.25033, 1.71186, -1.78506, -1.14342, 1.69155, 0.56504, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 0.78882, 1.59948, 0.66752, 1.65377, 0.24924, 1.76586, -0.64139, -1.66409, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 0.66752, 1.65377, 1.69155, 0.56504, 1.0651, 1.43023, 0.78882, 1.59948, 1.69155, 0.56504, -0.64139, -1.66409, 1.48729, 0.45598, -0.59543, -1.43722, 0, 0, 0, 0, 0.4046, -0.65416, 0.55035, -0.53736, 0.62114, -0.45368, 0.7596, -0.32275, 0.80457, -0.18383, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, 1.3753, -0.44182, 1.00914, 1.03354, 0.85201, 1.16649, 0.65942, 1.31119, 0.41801, 1.4069, -1.21637, -0.77915, 0.98604, 2.19215, -1.02839, 2.17269, -1.52176, 1.86072, -1.77075, 1.62556, -2.23659, 1.24584, 1.06245, -2.15619, 1.36913, 2.06908, -0.5067, 2.4288, -0.68616, 2.38435, -1.24065, 2.14863, -1.53341, 1.9505, -2.0529, 1.61109, 0.72361, -2.37323, 1.62453, 1.98703, -0.26868, 2.55254, -0.45802, 2.52547, -1.05324, 2.34057, -1.37518, 2.16713, 0.49771, -2.51793, 1.87994, 1.90498, 0.45411, 2.63761, -0.03065, 2.67627, -0.22987, 2.66658, -0.86583, 2.53252, 0.27182, -2.66262, 2.13538, 1.82291, 0.04592, -2.80731, 1.12762, 0.87873, -0.04048, -1.42907, 0, 0, 0, 0, 0, 0, 0, 0, 0.73064, 2.27419, -1.70917, 1.66877, -1.92897, 1.40893, -2.35905, 1.00233, -2.49881, 0.57094, 1.28835, -2.01149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.54136, -1.68517, 0.81643, -1.57046, 0.93107, -1.5053, 1.26644, -1.23653, 1.42934, -1.04399, 1.74795, -0.7427, -0.95473, 1.49044, -0.05411, -0.16855, 0.08169, -0.15704, 0.09311, -0.1505, 0.12664, -0.12366, -0.48981, -1.52473, 0.73872, -1.42091, 0.84241, -1.36195, 1.14585, -1.11879, 1.29324, -0.94458, -0.70508, -2.19477, 1.06332, -2.04534, 1.21262, -1.96048, 1.64939, -1.61045, 1.86156, -1.35968, 2.2765, -0.96729, -0.27069, -0.84259, -0.47737, 0.74522, -0.6548, -2.03829, 1.12618, -1.82072, 1.53182, -1.49563, 1.72885, -1.26273, -1.1548, 1.80274, -0.97833, -3.04538, 1.68261, -2.72033, 2.28866, -2.2346, 2.58305, -1.88665, -1.72537, 2.69345, -0.27325, -0.85062, -0.48192, 0.75232, -0.77063, 1.20301, -0.70765, -2.20279, 1.65544, -1.61633, -1.24799, 1.94822, -0.65739, -2.04636, 1.13064, -1.82792, 1.53787, -1.50153, 1.73568, -1.26771, -1.15938, 1.80985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.29729, 0.0249, 0.80668, -1.01615, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, -1.29729, 0.0249, 0.80668, -1.01615, 1.29666, -0.04539, 1.26935, 0.26846, 1.21834, 0.44613, 1.20417, 0.67689, -1.29729, 0.0249]}]}}}}}}