// 引入核心模块(fs)
const writeFile = require("fs").writeFile;
const fs = require("fs");

function genCodeFile(fileName, codeTemplate) {
  writeFile(fileName, codeTemplate, (error) => {
    // 创建失败
    if (error) {
      console.log(`${fileName}创建失败：${error}`);
    }

    // 创建成功
    console.log(`${fileName}创建成功！`);
  });
}

const codeTemplate = [
  {
    name: "Api",
    template: (moduleName) => {
      return `import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ${moduleName}Module } from "./${moduleName}Module";

export class ${moduleName}Api {

  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   console.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       console.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       console.log(\`\${errorCode}\`);
  //       console.log(data);
  //     }
  //   );
  // }
}`;
    },
  },
  {
    name: "Data",
    template: (moduleName) => {
      return `import { JsonMgr } from "../../game/mgr/JsonMgr";
import { times } from "../../lib/utils/NumbersUtils";
import { ${moduleName}Module } from "./${moduleName}Module";

export class ${moduleName}Data {

}`;
    },
  },
  {
    name: "Config",
    template: (moduleName) => {
      return `import { JsonMgr } from "../../game/mgr/JsonMgr";
/** 
 * 本地配置文件数据结构
 * 数据基础处理，过滤等，按id取
 */
export class ${moduleName}Config {

}`;
    },
  },
  {
    name: "Service",
    template: (moduleName) => {
      return `

/**
 * 模块逻辑处理
 */
export class ${moduleName}Service {


}`;
    },
  },
  {
    name: "Constant",
    template: (moduleName) => {
      return `/** 模块内部常量定义放这里，例如存储状态 **/`;
    },
  },

  {
    name: "Route",
    template: (moduleName) => {
      return `import { UINavigate } from "../../game/ui/UINavigate";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export class ${moduleName}Route {

  rotueTables: Recording[] = [];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}`;
    },
  },
  {
    name: "Subscriber",
    template: (moduleName) => {
      return `import { ApiHandler } from "../../game/mgr/ApiHandler";
import { ${moduleName}Module } from "./${moduleName}Module";

export class ${moduleName}Subscriber {


  public register() {
    //订阅服务器消息
  }
  public unRegister() {
    //取消订阅服务器消息
  }
}`;
    },
  },
  {
    name: "ViewModel",
    template: (moduleName) => {
      return `export class ${moduleName}ViewModel {

}`;
    },
  },
  {
    name: "Module",
    template: (moduleName) => {
      return `import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { ${moduleName}Api } from "./${moduleName}Api";
import { ${moduleName}Config } from "./${moduleName}Config";
import { ${moduleName}Data } from "./${moduleName}Data";
import { ${moduleName}Route } from "./${moduleName}Route";
import { ${moduleName}Service } from "./${moduleName}Service";
import { ${moduleName}Subscriber } from "./${moduleName}Subscriber";
import { ${moduleName}ViewModel } from "./${moduleName}ViewModel";

export class ${moduleName}Module extends data {
  private constructor() {
    super();
  }
  public static get instance(): ${moduleName}Module {
    if (!GameData.instance.${moduleName}Module) {
      GameData.instance.${moduleName}Module = new ${moduleName}Module();
    }
    return GameData.instance.${moduleName}Module;
  }
  private _data = new ${moduleName}Data();
  private _api = new ${moduleName}Api();
  private _service = new ${moduleName}Service();
  private _subscriber = new ${moduleName}Subscriber();
  private _route = new ${moduleName}Route();
  private _viewModel = new ${moduleName}ViewModel();
  private _config = new ${moduleName}Config();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any) {
    if(this._subscriber){
      this._subscriber.unRegister();
    }
    this._data = new ${moduleName}Data();
    this._api = new ${moduleName}Api();
    this._service = new ${moduleName}Service();
    this._subscriber = new ${moduleName}Subscriber();
    this._route = new ${moduleName}Route();
    this._viewModel = new ${moduleName}ViewModel();
    this._config = new ${moduleName}Config();

    // 模块初始化
    this._subscriber.register();
    this._route.init();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }

}`;
    },
  },
];

const readline = require("readline").createInterface({
  input: process.stdin,
  output: process.stdout,
});

readline.question(`请输入模块名`, (input) => {
  // 创建文件夹
  let moduleName = input.trim();
  const filePath = `./assets/GameScrpit/module/${moduleName}`;
  fs.mkdir(filePath, (err) => {
    if (err) {
      console.log(err);
    } else {
      console.log("创建模块目录成功:" + filePath);

      const moduleNameBig = moduleName[0].toUpperCase() + moduleName.substring(1);
      for (let idx in codeTemplate) {
        const templateInfo = codeTemplate[idx];
        const fullPath = `${filePath}/${moduleNameBig}${templateInfo.name}.ts`;
        genCodeFile(fullPath, templateInfo.template(moduleNameBig));
      }
    }
  });

  readline.close();
});
