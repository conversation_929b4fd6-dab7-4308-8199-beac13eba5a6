import { _decorator, Component, find, Input, instantiate, Label, Node, RichText, ScrollView, v3 } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import ToolExt from "../../../common/ToolExt";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { MainTaskModule } from "db://assets/GameScrpit/module/mainTask/MainTaskModule";

const { ccclass, property } = _decorator;
@ccclass("DisTaskViewHolder")
export class DisTaskViewHolder extends ViewHolder {
  @property(Node)
  award_item: Node = null;

  public init() {}

  updateData(data: any, position: number) {
    this.node["objInfo"] = data;
    //***创建设置道具奖励====== */
    let reward = ToolExt.traAwardItemMapList(data.rewardList);
    if (reward.length < 4) {
      this.node.getChildByName("renwu_ScrollView").getComponent(ScrollView).enabled = false;
    }

    let renwu_content = find("renwu_ScrollView/view/renwu_content", this.node);

    let num = renwu_content.children.length - reward.length;
    if (num > 0) {
      for (let j = 0; j < num; j++) {
        let award_item = renwu_content.children[0];
        award_item.removeFromParent();
        award_item.destroy();
      }
    }
    for (let j = 0; j < reward.length; j++) {
      let award_item = renwu_content.children[j];
      if (!award_item) {
        award_item = instantiate(this.award_item);
        award_item.setPosition(v3(0, 0, 0));
        renwu_content.addChild(award_item);
        award_item.active = true;
      }
      let info = reward[j];
      FmUtils.setItemNode(award_item, info.id, info.num);
    }
    //***创建设置道具奖励====== */
    /**设置任务的信息===== */
    let task_info = data.takeList;
    let task = MainTaskModule.data.getConfigTask(task_info[0]);
    let str = task.des;
    str = str.replace("s%", String(task_info[1]));
    this.node.getChildByPath("bg_biaoqxx/lbl_renwu_name").getComponent(Label).string = str;
    /**设置任务的信息===== */
    /**任务完成的进度 */
    let lbl_task_bar = this.node.getChildByPath("btn_renwu_go/lbl_task_bar");
    lbl_task_bar.getComponent(Label).string = data.dayVal + "/" + task_info[1];
    let color = task_info[1] > data.dayVal ? "#B76E38" : "#00af04";
    ToolExt.setLabColor(lbl_task_bar.getComponent(Label), color);
    /**任务完成的进度 */
    /**按钮的显示状态，是否已经领取过 */
    if (data.state == 2) {
      this.node.getChildByName("btn_renwu_get").active = false;
      this.node.getChildByName("btn_renwu_go").active = false;
      this.node.getChildByName("bg_yilingqu").active = true;
    } else {
      this.node.getChildByName("bg_yilingqu").active = false;
      /**按钮的显示状态，是还在进行还是达到了可以领的进度 */
      if (data.state == 0) {
        /**还在进行中 */
        this.node.getChildByName("btn_renwu_go").active = true;
        this.node.getChildByName("btn_renwu_get").active = false;
      } else {
        this.node.getChildByName("btn_renwu_go").active = false;
        this.node.getChildByName("btn_renwu_get").active = true;
      }
    }
  }
}
