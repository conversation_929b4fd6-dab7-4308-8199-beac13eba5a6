import { Input, KeyCode, input } from "cc";
import GameObject from "./GameObject";
import { Section } from "./Section";

export default class KeyboardSection extends Section {
  private _keyDict: Map<[KeyCode], boolean> = new Map<[KeyCode], boolean>();
  public static sectionName(): string {
    return "KeyboardSection";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);

    input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    input.on(Input.EventType.KEY_UP, this.onKeyUp, this);
    this.ready();
  }

  protected onKeyDown(event) {
    let keyCode = event.keyCode;
    let isKeyDown = this._keyDict.has(keyCode);
    if (!isKeyDown) {
      this.onKeyStart(event);
    }
    this._keyDict.set(keyCode, true);
  }
  protected onKeyUp(event) {
    let keyCode = event.keyCode;
    let isKeyDown = this._keyDict.has(keyCode);
    if (isKeyDown) {
      this.onKeyRelease(event);
      this._keyDict.delete(keyCode);
    }
  }
  public updateSelf(dt) {
    this._keyDict.forEach((isDown, keyCode) => {
      if (isDown) {
        this.onKeyPress(keyCode);
      }
    });
  }
  //被按下
  protected onKeyStart(event) {
    let keyCode = event.keyCode;
    this.emitMsg("OnKeyStart", keyCode);
  }

  //被按住
  protected onKeyPress(keyCode) {
    this.emitMsg("OnKeyPress", keyCode);
  }

  //被放开
  protected onKeyRelease(event) {
    let keyCode = event.keyCode;
    this.emitMsg("OnKeyRelease", keyCode);
  }
}
