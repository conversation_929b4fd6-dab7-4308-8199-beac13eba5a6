{"skeleton": {"hash": "M1dCQYA1H5uS7OPMd/8LYF2yIG4", "spine": "3.8.75", "x": -429.21, "y": -441.18, "width": 856.14, "height": 866.4, "images": "./images/", "audio": "D:/spine/蜘蛛精"}, "bones": [{"name": "root", "scaleX": 0.53, "scaleY": 0.53}, {"name": "bone", "parent": "root", "length": 852.42, "rotation": -0.55, "x": -388, "y": -877.93}, {"name": "shenti_1", "parent": "bone", "length": 147.21, "rotation": 103.9, "x": 470.42, "y": 789.03}, {"name": "shenti_2", "parent": "shenti_1", "length": 98.14, "rotation": -44.4, "x": 147.21}, {"name": "shenti_3", "parent": "shenti_2", "length": 49.07, "rotation": 33.6, "x": 98.14}, {"name": "ya<PERSON>i", "parent": "shenti_1", "length": 138.56, "rotation": -81.13, "x": -23.22, "y": 61.97}, {"name": "shent3", "parent": "shenti_1", "length": 454.56, "rotation": -155.99, "x": -18.32, "y": -0.72}, {"name": "shenti4", "parent": "shent3", "length": 529.25, "rotation": 103.33, "x": 436.96, "y": 28.2}, {"name": "yifu2", "parent": "ya<PERSON>i", "length": 141.64, "rotation": -2.56, "x": 7.2, "y": -19.1}, {"name": "tui16", "parent": "shent3", "length": 229.82, "rotation": 145.29, "x": 383.79, "y": -102.72}, {"name": "tui15", "parent": "tui16", "length": 385.02, "rotation": 156.98, "x": 231.88, "y": 15.85}, {"name": "tui25", "parent": "shent3", "length": 223.59, "rotation": -31.77, "x": 114.73, "y": -180.92}, {"name": "tui24", "parent": "tui25", "length": 193.93, "rotation": -166.23, "x": 221.04, "y": -5.71}, {"name": "tui23", "parent": "tui24", "length": 307.85, "rotation": 163.03, "x": 191.38, "y": 16.62}, {"name": "tui27", "parent": "shent3", "length": 293.98, "rotation": 161.93, "x": 301.42, "y": -342.77}, {"name": "tui26", "parent": "tui27", "length": 427.8, "rotation": 110.73, "x": 299.48, "y": 8.36}, {"name": "tui28", "parent": "shent3", "length": 284.7, "rotation": 166.44, "x": 351.42, "y": -418.6}, {"name": "tui30", "parent": "tui28", "length": 249.74, "rotation": 131.3, "x": 291.76, "y": 3.96}, {"name": "tui21", "parent": "shent3", "length": 178.57, "rotation": -19.41, "x": 305.25, "y": -5.77}, {"name": "tui18", "parent": "tui21", "length": 213.44, "rotation": 129.75, "x": 188.29, "y": 1.11}, {"name": "tui17", "parent": "tui18", "length": 349.41, "rotation": -112.98, "x": 226, "y": 8.9}, {"name": "tui22", "parent": "shent3", "length": 181.79, "rotation": -2.32, "x": 347.85, "y": 38.05}, {"name": "tui20", "parent": "tui22", "length": 327.38, "rotation": 106.99, "x": 200.68, "y": -8.3}, {"name": "tui19", "parent": "tui20", "length": 103.86, "rotation": -77.39, "x": 329.87, "y": -3.19}, {"name": "texiao1", "parent": "shent3", "length": 118.26, "rotation": -35.15, "x": 116.5, "y": 188.39}, {"name": "texiao2", "parent": "shenti4", "length": 166.39, "rotation": -41.61, "x": 332.37, "y": 202.63}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "shenti_3", "length": 202.96, "rotation": -129.18, "x": 58.85, "y": -29.07}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON><PERSON>", "length": 205.76, "rotation": 85.75, "x": 202.33, "y": 0.84}, {"name": "you<PERSON>ou", "parent": "<PERSON><PERSON><PERSON><PERSON>", "length": 11.81, "rotation": 175.89, "x": 215.27, "y": -1.42}, {"name": "youshou2", "parent": "<PERSON><PERSON><PERSON><PERSON>", "x": 201.14, "y": 18.1}, {"name": "youshou4", "parent": "youshou2", "length": 42.52, "rotation": 73.42, "x": 24.68, "y": 11.8}, {"name": "youshou5", "parent": "youshou2", "length": 45.82, "rotation": -59.21, "x": 26.32, "y": -8.04}, {"name": "zhizhusi2", "parent": "youshou2", "length": 108.28, "rotation": 168.6, "x": 47.03, "y": -6.46}, {"name": "zhizhusi3", "parent": "zhizhusi2", "length": 108.28, "rotation": -3.6, "x": 108.28}, {"name": "zhizhusi4", "parent": "zhizhusi3", "length": 108.28, "rotation": 1.2, "x": 108.58, "y": -1.39}, {"name": "zhizhusi5", "parent": "zhizhusi4", "length": 108.28, "rotation": 2.4, "x": 108.34, "y": 0.8}, {"name": "zhizhusi6", "parent": "zhizhusi5", "length": 108.28, "x": 108.28, "y": 1.84}, {"name": "zhizhusi7", "parent": "zhizhusi6", "length": 108.28, "x": 108.28, "y": 2.81}, {"name": "zhizhusi8", "parent": "zhizhusi7", "length": 108.28, "rotation": 2.4, "x": 108.28, "y": 3.03}, {"name": "zhizhusi9", "parent": "zhizhusi8", "length": 108.28, "rotation": -2.4, "x": 108.52, "y": 3.44}, {"name": "zhizhusi11", "parent": "zhizhusi2", "length": 92.2, "rotation": 30.14, "x": 60.86, "y": -56.03}, {"name": "zhizhusi12", "parent": "zhizhusi2", "length": 107.13, "rotation": -20.78, "x": 43.42, "y": 47.55}, {"name": "<PERSON><PERSON><PERSON>", "parent": "shenti_3", "length": 147.25, "rotation": 113.45, "x": -15.98, "y": 85.23}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "length": 270.52, "rotation": -96.96, "x": 150.94, "y": -3.62}, {"name": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 75.68, "rotation": 25.53, "x": 272.62, "y": 3.6}, {"name": "zuoshou3", "parent": "<PERSON><PERSON><PERSON>", "length": 22.65, "rotation": -3.46, "x": 95.31, "y": 2.46}, {"name": "zuoshou8", "parent": "zuoshou3", "length": 22.65, "x": 22.65}, {"name": "zuoshou9", "parent": "zuoshou8", "length": 22.65, "x": 22.65}, {"name": "zuoshou4", "parent": "zuoshou9", "length": 33.97, "x": 22.65}, {"name": "zuoshou5", "parent": "<PERSON><PERSON><PERSON>", "length": 40.43, "rotation": -116.31, "x": 88.5, "y": -13.77}, {"name": "zuoshou6", "parent": "zuoshou5", "length": 26.96, "x": 40.43}, {"name": "zuoshou7", "parent": "zuoshou6", "length": 13.48, "x": 26.96}, {"name": "zhizhusi1", "parent": "zuoshou3", "length": 201.33, "rotation": 142.79, "x": 1.21, "y": 7.06}, {"name": "zhizhusi10", "parent": "zhizhusi1", "length": 134.22, "x": 201.33}, {"name": "zhizhusi13", "parent": "zhizhusi10", "length": 67.11, "x": 134.22}, {"name": "zhizhu4", "parent": "zhizhusi13", "length": 28.78, "rotation": -6.75, "x": 87.49, "y": -3.27}, {"name": "zhizhu4tou", "parent": "zhizhu4", "length": 22.12, "rotation": -2.11, "x": 34.21, "y": -3.09}, {"name": "tui14", "parent": "zhizhu4", "length": 21.26, "rotation": -109.66, "x": 11.05, "y": -19.59}, {"name": "tui31", "parent": "tui14", "length": 23.03, "rotation": 67.52, "x": 21.23, "y": 2.58}, {"name": "tui13", "parent": "zhizhu4", "length": 12.06, "rotation": -80.29, "x": 20.02, "y": -26.34}, {"name": "tui32", "parent": "tui13", "length": 22.7, "rotation": 75.88, "x": 14.36, "y": 3.1}, {"name": "tui12", "parent": "zhizhu4", "length": 15.66, "rotation": -63.71, "x": 34.33, "y": -15.07}, {"name": "tui33", "parent": "tui12", "length": 16.07, "rotation": 56.27, "x": 18.24, "y": -0.07}, {"name": "tui11", "parent": "zhizhu4", "length": 22.7, "rotation": 138.72, "x": 10.69, "y": 16.36}, {"name": "tui34", "parent": "tui11", "length": 20.06, "rotation": -86.01, "x": 24.35, "y": -2.4}, {"name": "tui10", "parent": "zhizhu4", "length": 18.26, "rotation": 97.38, "x": 17.77, "y": 18.63}, {"name": "tui35", "parent": "tui10", "length": 19.08, "rotation": -66.02, "x": 18.03, "y": -2.87}, {"name": "tui9", "parent": "zhizhu4", "length": 13.97, "rotation": 86.96, "x": 31.5, "y": 12.61}, {"name": "tui36", "parent": "tui9", "length": 19.4, "rotation": -51.19, "x": 15.21, "y": -2.76}, {"name": "zhizhusi15", "parent": "zhizhusi9", "length": 82.76, "rotation": 19.08, "x": 1.24, "y": 4.01}, {"name": "shenti1", "parent": "zhizhusi9", "length": 147.09, "rotation": 67.9, "x": 148.74, "y": -21.35}, {"name": "shenti2", "parent": "shenti1", "length": 88.25, "x": 147.09}, {"name": "shenti3", "parent": "shenti2", "length": 58.83, "x": 88.25}, {"name": "shenti5", "parent": "shenti3", "length": 29.42, "x": 58.83}, {"name": "heshangtou1", "parent": "shenti1", "length": 183.75, "rotation": -168.43, "x": -20.65, "y": -8.1}, {"name": "<PERSON><PERSON>i", "parent": "heshangtou1", "length": 73.27, "rotation": 98.99, "x": 198.48, "y": -6.13}, {"name": "zuiba2", "parent": "heshangtou1", "length": 57.59, "rotation": -86.66, "x": 45, "y": 31.94}, {"name": "zhengyan2", "parent": "heshangtou1", "x": 98.57, "y": 13.72}, {"name": "meimao2", "parent": "heshangtou1", "x": 142.13, "y": 17.8}, {"name": "tou2", "parent": "shenti_3", "length": 230.56, "rotation": 39.13, "x": 90.29, "y": 18.82}, {"name": "bone2", "parent": "root", "length": 270.04, "x": 370.42, "y": -828.15}, {"name": "zhizhu2", "parent": "bone2", "length": 108.16, "rotation": -23.67, "x": 80.06, "y": 360.96}, {"name": "zhizhu5", "parent": "zhizhu2", "length": 40.66, "rotation": 139.38, "x": -8.24, "y": 5.28}, {"name": "tui3", "parent": "zhizhu2", "length": 57.6, "rotation": -171.35, "x": -31.21, "y": -40.35}, {"name": "tui5", "parent": "tui3", "length": 75.07, "rotation": 92.49, "x": 57.16, "y": 6.9}, {"name": "tui4", "parent": "zhizhu2", "length": 34.33, "rotation": -137.89, "x": -26, "y": -45.47}, {"name": "tui7", "parent": "tui4", "length": 66.09, "rotation": 52.39, "x": 39.05, "y": 5.58}, {"name": "tu2", "parent": "zhizhu2", "length": 100.87, "rotation": -83.55, "x": -36.96, "y": -33.98}, {"name": "tui1", "parent": "zhizhu2", "length": 96.59, "rotation": -62.3, "x": -34.48, "y": -32.89}, {"name": "tui6", "parent": "zhizhu2", "length": 33.5, "rotation": 88.03, "x": 107.01, "y": 60.49}, {"name": "tui29", "parent": "tui6", "length": 65.75, "rotation": -131.09, "x": 28.25, "y": -7.21}, {"name": "tui8", "parent": "zhizhu2", "length": 33.11, "rotation": 75.34, "x": 74.53, "y": 65.38}, {"name": "tui38", "parent": "tui8", "length": 79.07, "rotation": -98.83, "x": 30.82, "y": -4.89}, {"name": "tui37", "parent": "zhizhu2", "length": 26.82, "rotation": 59.51, "x": 37.66, "y": 63.72}, {"name": "tui40", "parent": "tui37", "length": 90.27, "rotation": -87.36, "x": 26.6, "y": -1.33}, {"name": "tui39", "parent": "zhizhu2", "length": 27.13, "rotation": 102.11, "x": 37.19, "y": 54.28}, {"name": "tui42", "parent": "tui39", "length": 97.53, "rotation": -133.27, "x": 26.56, "y": -6.28}, {"name": "yifu4", "parent": "shenti_2", "length": 145.8, "rotation": -34.81, "x": 55.2, "y": -21.38}, {"name": "yifu6", "parent": "yifu4", "length": 168.48, "rotation": -135.17, "x": -14.57, "y": -2.94}, {"name": "yifu1", "parent": "yifu4", "length": 68.37, "rotation": -59.59, "x": 147.08, "y": -140.05}, {"name": "yifu3", "parent": "yifu1", "length": 42.73, "x": 68.37}, {"name": "yifu5", "parent": "yifu3", "length": 25.64, "x": 42.73}, {"name": "yifu7", "parent": "yifu5", "length": 17.09, "x": 25.64}, {"name": "yifu8", "parent": "yifu7", "length": 8.55, "x": 17.09}, {"name": "bone3", "parent": "root", "length": 253.42, "x": -475.66, "y": -840.47}, {"name": "tengman", "parent": "bone3", "length": 88.55, "rotation": 122.01, "x": -125.63, "y": 112.63}, {"name": "tengman3", "parent": "bone3", "length": 33.82, "rotation": 16.11, "x": -275.08, "y": 1436.06}, {"name": "tengman2", "parent": "tengman3", "length": 66.1, "rotation": -65.99, "x": 96.65, "y": -16.65}, {"name": "tengman5", "parent": "tengman2", "length": 67.99, "rotation": 91.86, "x": 203.62, "y": 9.68}, {"name": "tengman4", "parent": "tengman5", "length": 49.11, "rotation": -83.41, "x": 209.3, "y": -13.52}, {"name": "tengman7", "parent": "tengman4", "length": 23.43, "rotation": 97.73, "x": 54.74, "y": 16.53}, {"name": "tengman6", "parent": "bone3", "length": 47.39, "rotation": -54.36, "x": -269.91, "y": 1309.02}, {"name": "tengman9", "parent": "tengman6", "length": 42.33, "rotation": 66.25, "x": 156.88, "y": 5.55}, {"name": "tengman10", "parent": "tengman9", "length": 23.17, "rotation": -53.07, "x": 51.65, "y": -8.65}, {"name": "tengman8", "parent": "tengman9", "x": 51.65, "y": -8.65}, {"name": "tengman12", "parent": "tengman10", "length": 18.48, "rotation": 53.71, "x": 29.35, "y": 3.41}, {"name": "zhizhuwang7", "parent": "bone3", "length": 90.06, "rotation": -149.62, "x": 179.05, "y": 1050.51}, {"name": "bian", "parent": "bone3", "length": 330.21, "rotation": 3.72, "x": 717.53, "y": 1428.25}, {"name": "zhizhuwang6", "parent": "bone3", "length": 131.52, "rotation": -93.5, "x": 1154.21, "y": 1176.42}, {"name": "zhizhuwang9", "parent": "zhizhuwang6", "length": 132.33, "rotation": -144.74, "x": -31.76, "y": -7.31}, {"name": "zhizhuwang10", "parent": "zhizhuwang6", "length": 109.41, "rotation": -173.32, "x": -49.85, "y": 2.13}, {"name": "zhizhuwang11", "parent": "zhizhuwang6", "length": 109.58, "rotation": 168.37, "x": -37.66, "y": 15.9}, {"name": "cao", "parent": "bone3", "length": 73.68, "rotation": -91.68, "x": 1212.2, "y": 168.67}, {"name": "cao3", "parent": "cao", "length": 67.06, "rotation": -174.95, "x": -27.89, "y": -9.49}, {"name": "cao4", "parent": "cao3", "length": 40.24, "rotation": -3.6, "x": 67.06}, {"name": "cao5", "parent": "cao4", "length": 26.83, "rotation": -6, "x": 40.24}, {"name": "cao6", "parent": "cao5", "length": 13.41, "rotation": -10.8, "x": 26.83}, {"name": "cao7", "parent": "cao", "length": 101.77, "rotation": -123.7, "x": -1.46, "y": -23.88}, {"name": "cao8", "parent": "cao7", "length": 61.06, "rotation": 22.8, "x": 101.77}, {"name": "cao9", "parent": "cao8", "length": 40.71, "rotation": 2.4, "x": 61.06}, {"name": "cao10", "parent": "cao9", "length": 20.35, "rotation": 16.8, "x": 40.71}, {"name": "cao11", "parent": "cao", "length": 70.95, "rotation": -115.05, "x": 50.62, "y": -26.68}, {"name": "cao12", "parent": "cao11", "length": 47.3, "rotation": 26.4, "x": 70.95}, {"name": "cao13", "parent": "cao12", "length": 23.65, "rotation": 16.8, "x": 47.3}, {"name": "cao2", "parent": "bone3", "length": 43.65, "rotation": -2.73, "x": 896.21, "y": 99.03}, {"name": "cao15", "parent": "cao2", "length": 52.43, "rotation": 128.46, "x": 20.34, "y": 9.28}, {"name": "cao16", "parent": "cao15", "length": 34.95, "x": 52.43}, {"name": "cao17", "parent": "cao16", "length": 17.48, "x": 34.95}, {"name": "cao18", "parent": "cao2", "length": 30.14, "rotation": 89.77, "x": 38.71, "y": 16.39}, {"name": "cao19", "parent": "cao18", "length": 20.09, "x": 30.14}, {"name": "cao20", "parent": "cao19", "length": 10.05, "x": 20.09}, {"name": "cao21", "parent": "cao2", "length": 33.45, "rotation": 50.87, "x": 54.31, "y": 16.1}, {"name": "cao22", "parent": "cao21", "length": 22.3, "x": 33.45}, {"name": "cao23", "parent": "cao22", "length": 11.15, "x": 22.3}, {"name": "qianjing2", "parent": "bone3", "length": 385.93, "rotation": 15.28, "x": 763.06, "y": 136.59}, {"name": "qianjing1", "parent": "bone3", "length": 341.33, "rotation": -30.02, "x": -234.93, "y": 292.03}, {"name": "fazhang", "parent": "bone3", "length": 333.56, "rotation": -20.94, "x": 101.11, "y": 228.51}, {"name": "<PERSON><PERSON><PERSON>", "parent": "fazhang", "length": 306.39, "rotation": -0.79, "x": 16.96, "y": -12.04}, {"name": "zhizhu1", "parent": "bone3", "length": 64.57, "rotation": -136.33, "x": 154.51, "y": 1504.7}, {"name": "youshou3", "parent": "root", "x": -204.67, "y": 307.69, "color": "ff3f00ff"}, {"name": "zuoshou2", "parent": "root", "x": 436.57, "y": 235.48, "color": "ff3f00ff"}, {"name": "tui2", "parent": "root", "x": -29.58, "y": -346.87, "color": "ff3f00ff"}, {"name": "tui41", "parent": "root", "x": -428.38, "y": -551.94, "color": "ff3f00ff"}, {"name": "tui43", "parent": "root", "x": -251.36, "y": -604.05, "color": "ff3f00ff"}, {"name": "tui44", "parent": "root", "x": 85.32, "y": -607.39, "color": "ff3f00ff"}, {"name": "tui45", "parent": "root", "x": 449.38, "y": -354.22, "color": "ff3f00ff"}, {"name": "tui46", "parent": "root", "x": 645.24, "y": -276.38, "color": "ff3f00ff"}, {"name": "xiaotui1", "parent": "root", "x": 332.42, "y": -555.62, "color": "ff3f00ff"}, {"name": "xiaotui2", "parent": "root", "x": 351.95, "y": -576.69, "color": "ff3f00ff"}, {"name": "xiaotui3", "parent": "root", "x": 573.51, "y": -486.98, "color": "ff3f00ff"}, {"name": "xiaotui4", "parent": "root", "x": 588.96, "y": -480.31, "color": "ff3f00ff"}, {"name": "xiaotui5", "parent": "root", "x": 621.38, "y": -474.25, "color": "ff3f00ff"}, {"name": "xiaotui6", "parent": "root", "x": 617.44, "y": -492.74, "color": "ff3f00ff"}, {"name": "youxiaobi3", "parent": "<PERSON><PERSON><PERSON><PERSON>", "length": 34.19, "rotation": -13.69, "x": 40.62, "y": 39.75}, {"name": "youxiaobi4", "parent": "youxiaobi3", "length": 20.52, "x": 34.19}, {"name": "youxiaobi5", "parent": "youxiaobi4", "length": 13.68, "x": 20.52}, {"name": "youxiaobi6", "parent": "youxiaobi5", "length": 6.84, "x": 13.68}, {"name": "youxiaobi7", "parent": "<PERSON><PERSON><PERSON><PERSON>", "length": 89.23, "rotation": -67.33, "x": -15.54, "y": -36.87}, {"name": "youxiaobi8", "parent": "youxiaobi7", "length": 54.91, "x": 89.23}, {"name": "youxiaobi9", "parent": "youxiaobi8", "length": 34.32, "x": 54.91}, {"name": "youxiaobi10", "parent": "youxiaobi9", "length": 20.59, "x": 34.32}, {"name": "youxiaobi11", "parent": "youxiaobi10", "length": 13.73, "x": 20.59}, {"name": "youxiaobi12", "parent": "youxiaobi11", "length": 6.86, "x": 13.73}, {"name": "yifu10", "parent": "yifu4", "length": 65.51, "rotation": -108.72, "x": 160.33, "y": -9.91}, {"name": "yifu11", "parent": "yifu10", "length": 40.32, "rotation": 20.4, "x": 65.51}, {"name": "yifu12", "parent": "yifu11", "length": 25.2, "rotation": 9.6, "x": 40.32}, {"name": "yifu13", "parent": "yifu12", "length": 15.12, "x": 25.2}, {"name": "yifu14", "parent": "yifu13", "length": 10.08, "x": 15.12}, {"name": "yifu15", "parent": "yifu14", "length": 5.04, "x": 10.08}, {"name": "yifu16", "parent": "yifu4", "length": 44.8, "rotation": -89.87, "x": -10.27, "y": -14.93}, {"name": "yifu17", "parent": "yifu16", "length": 27.57, "x": 44.8}, {"name": "yifu18", "parent": "yifu17", "length": 17.23, "x": 27.57}, {"name": "yifu19", "parent": "yifu18", "length": 10.34, "x": 17.23}, {"name": "yifu20", "parent": "yifu19", "length": 6.89, "x": 10.34}, {"name": "yifu21", "parent": "yifu20", "length": 3.45, "x": 6.89}, {"name": "yifu22", "parent": "yifu6", "length": 54.82, "rotation": -3.78, "x": 60.35, "y": 50.11}, {"name": "yifu23", "parent": "yifu22", "length": 33.74, "x": 54.82}, {"name": "yifu24", "parent": "yifu23", "length": 21.09, "x": 33.74}, {"name": "yifu25", "parent": "yifu24", "length": 12.65, "x": 21.09}, {"name": "yifu26", "parent": "yifu25", "length": 8.43, "x": 12.65}, {"name": "yifu27", "parent": "yifu26", "length": 4.22, "x": 8.43}, {"name": "yifu28", "parent": "yifu6", "length": 74.45, "rotation": -11.18, "x": 107.07, "y": -16.12}, {"name": "yifu29", "parent": "yifu28", "length": 45.82, "x": 74.45}, {"name": "yifu30", "parent": "yifu29", "length": 28.64, "x": 45.82}, {"name": "yifu31", "parent": "yifu30", "length": 17.18, "x": 28.64}, {"name": "yifu32", "parent": "yifu31", "length": 11.45, "x": 17.18}, {"name": "yifu33", "parent": "yifu32", "length": 5.73, "x": 11.45}, {"name": "zuoxiaobi3", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 96.46, "rotation": 116.31, "x": 134.56, "y": 42.55}, {"name": "zuoxiaobi4", "parent": "zuoxiaobi3", "length": 59.36, "x": 96.46}, {"name": "zuoxiaobi5", "parent": "zuoxiaobi4", "length": 37.1, "x": 59.36}, {"name": "zuoxiaobi6", "parent": "zuoxiaobi5", "length": 22.26, "x": 37.1}, {"name": "zuoxiaobi7", "parent": "zuoxiaobi6", "length": 14.84, "x": 22.26}, {"name": "zuoxiaobi8", "parent": "zuoxiaobi7", "length": 7.42, "x": 14.84}, {"name": "zuoxiaobi9", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 50.4, "rotation": 105.62, "x": -38.62, "y": 13.78}, {"name": "zuoxiaobi10", "parent": "zuoxiaobi9", "length": 31.01, "x": 50.4}, {"name": "zuoxiaobi11", "parent": "zuoxiaobi10", "length": 19.38, "x": 31.01}, {"name": "zuoxiaobi12", "parent": "zuoxiaobi11", "length": 11.63, "x": 19.38}, {"name": "zuoxiaobi13", "parent": "zuoxiaobi12", "length": 7.75, "x": 11.63}, {"name": "zuoxiaobi14", "parent": "zuoxiaobi13", "length": 3.88, "x": 7.75}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "bone3", "x": 22.43, "y": 107.62}, {"name": "zhizhuwang3", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 160.04, "rotation": -27.35, "x": -79.38, "y": 150.03}, {"name": "zhizhuwang4", "parent": "zhizhuwang3", "length": 96.03, "x": 160.04}, {"name": "zhizhuwang5", "parent": "zhizhuwang4", "length": 64.02, "x": 96.03}, {"name": "zhizhuwang8", "parent": "zhizhuwang5", "length": 32.01, "x": 64.02}, {"name": "zhizhuwang12", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 89.01, "rotation": -54.87, "x": 1133.82, "y": 1225.74}, {"name": "zhizhuwang13", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 97.65, "rotation": -6.34, "x": 1052.94, "y": 1406.37}, {"name": "zhizhuwang14", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 97.09, "rotation": -91.59, "x": 680.89, "y": 1403.67}, {"name": "temang2", "parent": "bone3", "length": 101.55, "rotation": -61.62, "x": 592.75, "y": 1574.26}, {"name": "temang4", "parent": "temang2", "length": 104.59, "rotation": 76.1, "x": 99.55, "y": 9.25}, {"name": "temang5", "parent": "temang4", "length": 62.76, "rotation": -24, "x": 104.59}, {"name": "temang6", "parent": "temang5", "length": 41.84, "rotation": -19.2, "x": 62.76}, {"name": "temang7", "parent": "temang6", "length": 20.92, "rotation": 40.8, "x": 41.84}, {"name": "tou3", "parent": "tou2", "x": 4.93, "y": -220.07, "color": "abe323ff"}, {"name": "<PERSON><PERSON>gy<PERSON>", "parent": "tou3", "x": 96.05, "y": 241.58}, {"name": "meimao1", "parent": "tou2", "x": 124.56, "y": 18.54}, {"name": "zui1", "parent": "tou3", "length": 44.44, "rotation": -93.39, "x": 46.06, "y": 273.79}, {"name": "toufa1", "parent": "tou2", "x": 177.16, "y": -138.78}, {"name": "fashi2", "parent": "toufa1", "x": -18.55, "y": 13.46}, {"name": "fashi1", "parent": "tou2", "length": 64.97, "rotation": 171.87, "x": 88.34, "y": -159.54}, {"name": "tengman11", "parent": "tengman2", "length": 66.1, "x": 66.1}, {"name": "tengman13", "parent": "tengman11", "length": 66.1, "x": 66.1}, {"name": "tengman14", "parent": "tengman5", "length": 67.99, "x": 67.99}, {"name": "tengman15", "parent": "tengman14", "length": 67.99, "x": 67.99}, {"name": "tengman16", "parent": "tengman7", "length": 23.43, "x": 23.43}, {"name": "tengman17", "parent": "tengman16", "length": 23.43, "x": 23.43}, {"name": "tengman18", "parent": "tengman3", "length": 33.82, "x": 33.82}, {"name": "tengman19", "parent": "tengman18", "length": 33.82, "x": 33.82}, {"name": "tengman20", "parent": "tengman6", "length": 47.39, "x": 47.39}, {"name": "tengman21", "parent": "tengman20", "length": 47.39, "x": 47.39}, {"name": "tengman22", "parent": "tengman", "length": 88.55, "x": 88.55}, {"name": "tengman23", "parent": "tengman22", "length": 88.55, "x": 88.55}, {"name": "liu<PERSON>", "parent": "tou2", "length": 27.86, "rotation": -70.82, "x": 186.56, "y": -21.32}, {"name": "liuhai3", "parent": "liu<PERSON>", "length": 23.7, "rotation": -51.76, "x": 29.05, "y": -3.09}, {"name": "liuhai2", "parent": "liuhai3", "length": 91.39, "rotation": -52.87, "x": 27.19, "y": -4.82}, {"name": "liuhai4", "parent": "liuhai2", "length": 54.83, "rotation": 2.4, "x": 91.39}, {"name": "liuhai5", "parent": "liuhai4", "length": 36.56, "rotation": 18, "x": 54.83}, {"name": "liuhai6", "parent": "liuhai5", "length": 18.28, "rotation": 9.6, "x": 36.56}, {"name": "liuhai7", "parent": "tou2", "length": 57.25, "rotation": 157.63, "x": 63.74, "y": -75.52}, {"name": "liuhai8", "parent": "liuhai7", "length": 34.35, "rotation": -19.2, "x": 57.25}, {"name": "liuhai9", "parent": "liuhai8", "length": 22.9, "rotation": 34.8, "x": 34.35}, {"name": "liuhai10", "parent": "liuhai9", "length": 11.45, "rotation": 9.6, "x": 22.9}, {"name": "toufa3", "parent": "tou2", "length": 38.62, "rotation": 159.39, "x": 42.94, "y": 71.44}, {"name": "toufa4", "parent": "toufa3", "length": 25.75, "rotation": -15.6, "x": 38.62}, {"name": "toufa5", "parent": "toufa4", "length": 12.87, "rotation": -26.4, "x": 25.75}, {"name": "yifu34", "parent": "yifu2", "length": 204.68, "rotation": -104.28, "x": 1.44, "y": -17.99}, {"name": "yifu35", "parent": "yifu34", "length": 126.42, "rotation": 12, "x": 204.68}, {"name": "yifu36", "parent": "yifu35", "length": 78.26, "rotation": 4.8, "x": 126.42}, {"name": "yifu37", "parent": "yifu36", "length": 48.16, "x": 78.26}, {"name": "yifu38", "parent": "yifu37", "length": 30.1, "x": 48.16}, {"name": "yifu39", "parent": "yifu38", "length": 18.06, "rotation": 30, "x": 30.1}, {"name": "yifu40", "parent": "yifu39", "length": 12.04, "x": 18.06}, {"name": "yifu41", "parent": "yifu40", "length": 6.02, "x": 12.04}, {"name": "yifu42", "parent": "yifu2", "length": 236.34, "rotation": -110.73, "x": 149.34, "y": -15.54}, {"name": "yifu43", "parent": "yifu42", "length": 145.98, "rotation": 4.8, "x": 236.34}, {"name": "yifu44", "parent": "yifu43", "length": 90.37, "rotation": 14.4, "x": 145.98}, {"name": "yifu45", "parent": "yifu44", "length": 55.61, "rotation": 7.2, "x": 90.37}, {"name": "yifu46", "parent": "yifu45", "length": 34.76, "rotation": 1.2, "x": 55.61}, {"name": "yifu47", "parent": "yifu46", "length": 20.85, "rotation": 4.8, "x": 34.76}, {"name": "yifu48", "parent": "yifu47", "length": 13.9, "rotation": 6, "x": 20.85}, {"name": "yifu49", "parent": "yifu48", "length": 6.95, "rotation": 4.8, "x": 13.9}, {"name": "shenti_4", "parent": "shenti_2", "length": 25.31, "rotation": 163.22, "x": 87.51, "y": 27.51}, {"name": "shenti_6", "parent": "shenti_2", "length": 14.45, "rotation": -179.91, "x": -10.71, "y": 65.05}, {"name": "shenti_5", "parent": "shenti_4", "length": 25.31, "x": 25.31}, {"name": "shenti_7", "parent": "shenti_5", "length": 25.31, "x": 25.31}, {"name": "shenti_8", "parent": "shenti_6", "length": 14.45, "x": 14.45}, {"name": "shenti_9", "parent": "shenti_8", "length": 14.45, "x": 14.45}], "slots": [{"name": "tiankong", "bone": "root", "attachment": "tiankong"}, {"name": "beijing", "bone": "root", "attachment": "beijing"}, {"name": "zhizhuwang7", "bone": "zhizhuwang7", "attachment": "zhizhuwang7"}, {"name": "zhizhuwang6", "bone": "zhizhuwang11", "attachment": "zhizhuwang6"}, {"name": "bian", "bone": "bian", "attachment": "bian"}, {"name": "temang2", "bone": "temang4", "attachment": "temang2"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "fazhang", "bone": "fazhang", "attachment": "fazhang"}, {"name": "yifu5", "bone": "yifu28", "attachment": "yifu5"}, {"name": "yifu4", "bone": "yifu16", "attachment": "yifu4"}, {"name": "shenti4", "bone": "shenti4", "attachment": "shenti4"}, {"name": "texiao2", "bone": "texiao2", "attachment": "texiao2"}, {"name": "shent3", "bone": "shent3", "attachment": "shent3"}, {"name": "texiao1", "bone": "texiao1", "attachment": "texiao1"}, {"name": "tui29", "bone": "tui30", "attachment": "tui29"}, {"name": "tui28", "bone": "tui28", "attachment": "tui28"}, {"name": "tui27", "bone": "tui27", "attachment": "tui27"}, {"name": "tui26", "bone": "tui26", "attachment": "tui26"}, {"name": "tui25", "bone": "tui25", "attachment": "tui25"}, {"name": "tui24", "bone": "tui24", "attachment": "tui24"}, {"name": "tui23", "bone": "tui23", "attachment": "tui23"}, {"name": "tui22", "bone": "tui22", "attachment": "tui22"}, {"name": "tui21", "bone": "tui21", "attachment": "tui21"}, {"name": "tui20", "bone": "tui20", "attachment": "tui20"}, {"name": "tui19", "bone": "tui19", "attachment": "tui19"}, {"name": "tui18", "bone": "tui18", "attachment": "tui18"}, {"name": "tui17", "bone": "tui17", "attachment": "tui17"}, {"name": "toufa3", "bone": "toufa3", "attachment": "toufa3"}, {"name": "yifu2", "bone": "yifu42", "attachment": "yifu2"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "zuoxiaobi9", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "zuoshou5", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "shenti_1", "bone": "shenti_6", "attachment": "shenti_1"}, {"name": "ya<PERSON>i", "bone": "ya<PERSON>i", "attachment": "ya<PERSON>i"}, {"name": "tui16", "bone": "tui16", "attachment": "tui16"}, {"name": "tui15", "bone": "tui15", "attachment": "tui15"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "yifu1", "bone": "yifu1", "attachment": "yifu1"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "youxiaobi7", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "you<PERSON>ou", "bone": "youshou5", "attachment": "you<PERSON>ou"}, {"name": "toufa1", "bone": "toufa1", "attachment": "toufa1"}, {"name": "fashi2", "bone": "fashi2", "attachment": "fashi2"}, {"name": "tou2", "bone": "tou2", "attachment": "tou2"}, {"name": "meimao1", "bone": "meimao1", "attachment": "meimao1"}, {"name": "<PERSON><PERSON>gy<PERSON>", "bone": "<PERSON><PERSON>gy<PERSON>", "attachment": "<PERSON><PERSON>gy<PERSON>"}, {"name": "biyan1", "bone": "root"}, {"name": "zui1", "bone": "zui1", "attachment": "zui1"}, {"name": "liuhai2", "bone": "liuhai7", "attachment": "liuhai2"}, {"name": "liu<PERSON>", "bone": "liu<PERSON>", "attachment": "liu<PERSON>"}, {"name": "fashi1", "bone": "fashi1", "attachment": "fashi1"}, {"name": "shenti1", "bone": "shenti1", "attachment": "shenti1"}, {"name": "heshangtou1", "bone": "heshangtou1", "attachment": "heshangtou1"}, {"name": "biyan2", "bone": "root"}, {"name": "zhengyan2", "bone": "zhengyan2", "attachment": "zhengyan2"}, {"name": "zuiba2", "bone": "zuiba2", "attachment": "zuiba2"}, {"name": "meimao2", "bone": "meimao2", "attachment": "meimao2"}, {"name": "<PERSON><PERSON>i", "bone": "<PERSON><PERSON>i", "attachment": "<PERSON><PERSON>i"}, {"name": "zhizhusi2", "bone": "zhizhusi15", "attachment": "zhizhusi2"}, {"name": "zhizhusi1", "bone": "zhizhusi1", "attachment": "zhizhusi1"}, {"name": "tui14", "bone": "tui31", "attachment": "tui14"}, {"name": "tui13", "bone": "tui32", "attachment": "tui13"}, {"name": "tui12", "bone": "tui33", "attachment": "tui12"}, {"name": "tui11", "bone": "tui34", "attachment": "tui11"}, {"name": "tui10", "bone": "tui35", "attachment": "tui10"}, {"name": "tui9", "bone": "tui36", "attachment": "tui9"}, {"name": "zhizhu4", "bone": "zhizhu4", "attachment": "zhizhu4"}, {"name": "zhizhu4tou", "bone": "zhizhu4tou", "attachment": "zhizhu4tou"}, {"name": "qianjing2", "bone": "qianjing2", "attachment": "qianjing2"}, {"name": "qianjing1", "bone": "qianjing1", "attachment": "qianjing1"}, {"name": "tengman", "bone": "tengman12", "attachment": "tengman"}, {"name": "tui8", "bone": "tui42", "attachment": "tui8"}, {"name": "tui7", "bone": "tui40", "attachment": "tui7"}, {"name": "tui6", "bone": "tui38", "attachment": "tui6"}, {"name": "tui5", "bone": "tui29", "attachment": "tui5"}, {"name": "zhizhu3", "bone": "zhizhu5", "attachment": "zhizhu3"}, {"name": "zhizhu2", "bone": "zhizhu2", "attachment": "zhizhu2"}, {"name": "tui4", "bone": "tui7", "attachment": "tui4"}, {"name": "tui3", "bone": "tui5", "attachment": "tui3"}, {"name": "tu2", "bone": "tu2", "attachment": "tu2"}, {"name": "tui1", "bone": "tui1", "attachment": "tui1"}, {"name": "zhizhu1", "bone": "zhizhu1", "attachment": "zhizhu1"}, {"name": "cao", "bone": "cao21", "attachment": "cao"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "zhizhuwang14", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "ik": [{"name": "tui1", "order": 2, "bones": ["tui25", "tui24"], "target": "tui2", "bendPositive": false}, {"name": "tui2", "order": 3, "bones": ["tui27", "tui26"], "target": "tui41"}, {"name": "tui3", "order": 4, "bones": ["tui28", "tui30"], "target": "tui43"}, {"name": "tui4", "order": 5, "bones": ["tui16", "tui15"], "target": "tui44"}, {"name": "tui5", "order": 6, "bones": ["tui21", "tui18"], "target": "tui45"}, {"name": "tui6", "order": 7, "bones": ["tui22", "tui20"], "target": "tui46"}, {"name": "xiaotui1", "order": 8, "bones": ["tui3", "tui5"], "target": "xiaotui1"}, {"name": "xiaotui2", "order": 9, "bones": ["tui4", "tui7"], "target": "xiaotui2"}, {"name": "xiaotui3", "order": 10, "bones": ["tui39", "tui42"], "target": "xiaotui3", "bendPositive": false}, {"name": "xiaotui4", "order": 11, "bones": ["tui37", "tui40"], "target": "xiaotui4", "bendPositive": false}, {"name": "xiaotui5", "order": 12, "bones": ["tui8", "tui38"], "target": "xiaotui5", "bendPositive": false}, {"name": "xiaotui6", "order": 13, "bones": ["tui6", "tui29"], "target": "xiaotui6", "bendPositive": false}, {"name": "you<PERSON>ou", "bones": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "target": "youshou3", "bendPositive": false}, {"name": "<PERSON><PERSON><PERSON>", "order": 1, "bones": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "target": "zuoshou2"}], "transform": [{"name": "meimao", "order": 15, "bones": ["meimao1"], "target": "tou3", "x": 119.63, "y": 238.61, "rotateMix": 0, "translateMix": 0.7, "scaleMix": 0, "shearMix": 0}, {"name": "toufa", "order": 14, "bones": ["toufa1"], "target": "tou3", "x": 178.92, "y": 81.01, "rotateMix": 0, "translateMix": -0.5, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"youshou": {"youshou": {"type": "mesh", "uvs": [0.64529, 0.00465, 0.66925, 0.02353, 0.66934, 0.07667, 0.66947, 0.14896, 0.66412, 0.20051, 0.65723, 0.26698, 0.70242, 0.23578, 0.74201, 0.20844, 0.7821, 0.19907, 0.80487, 0.2336, 0.81031, 0.30683, 0.81722, 0.39977, 0.92149, 0.36743, 0.96405, 0.36721, 0.98586, 0.41124, 0.98509, 0.44764, 0.96043, 0.50537, 0.94484, 0.54186, 0.98424, 0.54198, 0.99274, 0.58644, 0.99262, 0.63257, 0.96369, 0.68968, 0.93136, 0.75351, 0.91029, 0.7951, 0.87985, 0.82745, 0.83284, 0.87741, 0.77986, 0.89859, 0.74729, 0.91161, 0.6863, 0.93598, 0.63796, 0.94859, 0.56996, 0.96632, 0.54056, 0.97146, 0.54089, 0.95553, 0.60331, 0.91238, 0.50437, 0.89413, 0.49733, 0.86797, 0.41869, 0.77809, 0.30759, 0.6963, 0.2983, 0.53865, 0.19602, 0.60133, 0.19562, 0.66092, 0.17423, 0.63259, 0.14963, 0.60003, 0.12711, 0.55883, 0.09515, 0.50038, 0.0661, 0.43057, 0.03837, 0.36393, 0.02307, 0.29349, 0.01491, 0.27206, 0.01493, 0.20485, 0.07525, 0.11462, 0.11019, 0.12787, 0.12762, 0.15614, 0.18, 0.19834, 0.22757, 0.22989, 0.29217, 0.2876, 0.33715, 0.23887, 0.38373, 0.1884, 0.44688, 0.11998, 0.49677, 0.06592, 0.54007, 0.03945, 0.60892, 0.01173, 0.25471, 0.53824, 0.27386, 0.47772, 0.3418, 0.37228, 0.41829, 0.30751, 0.49989, 0.26635, 0.49071, 0.34257, 0.45384, 0.43996, 0.63325, 0.33522, 0.55724, 0.45515, 0.4924, 0.53956, 0.48035, 0.66439, 0.47812, 0.78653, 0.48462, 0.58819, 0.54585, 0.65615, 0.64628, 0.51639, 0.71263, 0.41752, 0.69013, 0.46887, 0.20591, 0.3901, 0.19349, 0.4716, 0.1959, 0.56089, 0.23554, 0.4057, 0.2538, 0.43187, 0.80985, 0.47609, 0.74479, 0.53554, 0.68629, 0.59496, 0.65233, 0.64899, 0.60045, 0.71626, 0.52552, 0.77051, 0.55026, 0.78876, 0.71185, 0.70908, 0.63846, 0.75151, 0.85692, 0.61372, 0.78031, 0.66405, 0.88597, 0.56103, 0.84293, 0.7031, 0.81399, 0.76893, 0.75209, 0.81392, 0.70486, 0.84964, 0.6478, 0.88541], "triangles": [3, 61, 2, 1, 61, 0, 1, 2, 61, 3, 60, 61, 3, 59, 60, 3, 58, 59, 4, 58, 3, 66, 58, 4, 57, 58, 66, 65, 56, 57, 5, 66, 4, 53, 48, 49, 50, 52, 49, 51, 52, 50, 53, 49, 52, 47, 48, 53, 66, 65, 57, 55, 56, 65, 69, 66, 5, 67, 65, 66, 67, 66, 69, 47, 79, 46, 54, 47, 53, 64, 55, 65, 54, 79, 47, 79, 54, 55, 82, 79, 55, 64, 82, 55, 5, 6, 10, 5, 10, 69, 7, 8, 9, 9, 6, 7, 9, 10, 6, 45, 46, 79, 83, 82, 64, 67, 64, 65, 68, 64, 67, 70, 67, 69, 70, 69, 77, 68, 67, 70, 80, 45, 79, 80, 79, 82, 80, 82, 83, 63, 83, 64, 63, 64, 68, 80, 83, 63, 44, 45, 80, 62, 80, 63, 38, 63, 68, 62, 63, 38, 71, 68, 70, 38, 68, 71, 43, 44, 80, 81, 43, 80, 62, 81, 80, 74, 38, 71, 42, 43, 81, 39, 81, 62, 42, 81, 39, 39, 62, 38, 41, 42, 39, 40, 41, 39, 74, 37, 38, 72, 37, 74, 36, 37, 72, 73, 36, 72, 89, 73, 72, 36, 73, 35, 77, 69, 10, 77, 10, 11, 14, 12, 13, 78, 70, 77, 84, 77, 11, 78, 77, 84, 12, 15, 11, 14, 15, 12, 15, 84, 11, 16, 84, 15, 76, 70, 78, 85, 78, 84, 76, 78, 85, 71, 70, 76, 16, 95, 84, 17, 95, 16, 85, 84, 95, 86, 76, 85, 93, 85, 95, 94, 86, 85, 18, 19, 17, 76, 74, 71, 76, 86, 74, 86, 75, 74, 87, 75, 86, 93, 94, 85, 87, 86, 94, 72, 74, 75, 17, 19, 95, 20, 21, 19, 19, 93, 95, 21, 93, 19, 96, 94, 93, 96, 93, 21, 91, 87, 94, 88, 75, 87, 88, 87, 91, 92, 88, 91, 22, 96, 21, 96, 91, 94, 97, 96, 22, 97, 91, 96, 89, 72, 75, 89, 75, 88, 90, 89, 88, 92, 90, 88, 23, 97, 22, 98, 91, 97, 92, 91, 98, 24, 97, 23, 98, 97, 24, 99, 92, 98, 90, 35, 73, 90, 73, 89, 25, 98, 24, 100, 92, 99, 90, 92, 100, 33, 35, 90, 33, 34, 35, 26, 98, 25, 99, 98, 26, 27, 99, 26, 100, 99, 27, 90, 100, 33, 28, 100, 27, 29, 33, 100, 28, 29, 100, 30, 32, 33, 29, 30, 33, 31, 32, 30], "vertices": [1, 30, 57.96, -71.01, 1, 2, 31, -1.2, 96.96, 0.00083, 30, 53.61, -72.02, 0.99917, 2, 31, 0.36, 88.27, 0.01161, 30, 46.16, -67.28, 0.98839, 2, 31, 2.48, 76.46, 0.05384, 30, 36.04, -60.84, 0.94616, 2, 31, 3.28, 67.91, 0.11265, 30, 29.2, -55.64, 0.88735, 2, 31, 4.31, 56.89, 0.30437, 30, 20.39, -48.93, 0.69563, 2, 31, 9.32, 63.04, 0.42727, 30, 21.53, -56.78, 0.57273, 2, 31, 13.71, 68.43, 0.45094, 30, 22.52, -63.66, 0.54906, 2, 31, 18.68, 70.9, 0.45604, 30, 20.97, -69, 0.54396, 2, 31, 22.67, 65.79, 0.46267, 30, 14.51, -68.47, 0.53733, 2, 31, 25.51, 53.95, 0.51864, 30, 3.87, -62.54, 0.48136, 2, 31, 29.12, 38.92, 0.76676, 30, -9.63, -55.02, 0.23324, 2, 31, 41.83, 46.64, 0.92013, 30, -12.57, -69.6, 0.07987, 2, 31, 47.4, 47.66, 0.92849, 30, -15.58, -74.39, 0.07151, 2, 31, 51.54, 40.98, 0.93514, 30, -23.3, -72.9, 0.06486, 2, 31, 52.5, 35.01, 0.9409, 30, -28.34, -69.57, 0.0591, 2, 31, 50.95, 25, 0.95867, 30, -34.66, -61.65, 0.04133, 2, 31, 49.97, 18.67, 0.98186, 30, -38.65, -56.64, 0.01814, 2, 31, 55.13, 19.57, 0.99185, 30, -41.48, -61.05, 0.00815, 2, 31, 57.54, 12.5, 0.99716, 30, -48.31, -58.03, 0.00284, 3, 31, 58.87, 4.96, 0.99988, 29, 60.72, -56.07, 0, 30, -54.76, -53.9, 0.00011, 2, 31, 56.74, -5.05, 0.99553, 29, 51.03, -59.36, 0.00447, 2, 31, 54.36, -16.23, 0.9671, 29, 40.2, -63.05, 0.0329, 2, 31, 52.82, -23.52, 0.93709, 29, 33.15, -65.45, 0.06291, 2, 31, 49.77, -29.52, 0.90405, 29, 26.44, -65.9, 0.09595, 2, 31, 45.07, -38.78, 0.84849, 29, 16.08, -66.6, 0.15151, 2, 31, 38.75, -43.47, 0.80411, 29, 8.81, -63.58, 0.19589, 2, 31, 34.86, -46.36, 0.77161, 29, 4.34, -61.72, 0.22839, 2, 31, 27.59, -51.77, 0.71704, 29, -4.03, -58.23, 0.28296, 2, 31, 21.62, -54.95, 0.68831, 29, -9.82, -54.74, 0.31169, 2, 31, 13.24, -59.44, 0.6744, 29, -17.96, -49.83, 0.3256, 2, 31, 9.54, -60.96, 0.67381, 29, -21.17, -47.43, 0.32619, 2, 31, 9.12, -58.35, 0.67388, 29, -19.14, -45.74, 0.32612, 2, 31, 16.03, -49.85, 0.66873, 29, -8.29, -47.32, 0.33127, 2, 31, 2.55, -49.17, 0.57682, 29, -14.61, -35.39, 0.42318, 2, 31, 0.86, -45.06, 0.5553, 29, -11.94, -31.84, 0.4447, 2, 31, -12.05, -32.2, 0.28876, 29, -7.51, -14.17, 0.71124, 2, 29, -6.92, 5.89, 0.97051, 30, -14.68, 28.6, 0.02949, 2, 29, 12.06, 23.95, 0.10068, 30, 8.05, 15.57, 0.89932, 2, 29, -4.71, 27.43, 0.00112, 30, 6.59, 32.63, 0.99888, 2, 29, -12.22, 20.99, 0, 30, -1.72, 38, 1, 1, 30, 3.78, 37.87, 1, 2, 29, -8.59, 32.23, 1e-05, 30, 10.09, 37.72, 0.99999, 1, 30, 17.47, 36.56, 1, 1, 30, 27.94, 34.93, 1, 1, 30, 39.78, 31.95, 1, 1, 30, 51.09, 29.11, 1, 1, 30, 62.05, 24.54, 1, 1, 30, 65.63, 23.54, 1, 1, 30, 75.03, 17.54, 1, 1, 30, 83.34, 2.72, 1, 1, 30, 78.99, -0.01, 1, 1, 30, 73.79, 0.56, 1, 1, 30, 64.14, -1.55, 1, 1, 30, 56.32, -4.07, 1, 2, 31, -42.89, 45.01, 0.00234, 30, 43.62, -6.16, 0.99766, 2, 31, -38.42, 54.02, 0.01469, 30, 47.22, -15.55, 0.98531, 2, 31, -33.79, 63.35, 0.02377, 30, 50.95, -25.28, 0.97623, 2, 31, -27.51, 76.01, 0.02095, 30, 56.01, -38.47, 0.97905, 2, 31, -22.55, 86.01, 0.01073, 30, 60.01, -48.89, 0.98927, 2, 31, -17.65, 91.34, 0.00553, 30, 60.62, -56.11, 0.99447, 2, 31, -9.44, 97.48, 0.00059, 30, 59.57, -66.3, 0.99941, 2, 29, 8.32, 28.38, 0.02696, 30, 11.22, 20.42, 0.97304, 2, 29, 17.58, 33.03, 0.01142, 30, 18.32, 12.87, 0.98858, 2, 31, -33.93, 32.32, 0.00473, 30, 28.22, -4.16, 0.99527, 2, 31, -25.8, 44.69, 0.04514, 30, 31.81, -18.52, 0.95486, 2, 31, -16.31, 53.32, 0.09764, 30, 31.74, -31.35, 0.90236, 2, 31, -15.29, 40.65, 0.13388, 30, 21.73, -23.51, 0.86612, 2, 31, -17.29, 23.88, 0.0977, 30, 10.73, -10.68, 0.9023, 2, 31, 3.16, 45.18, 0.36602, 30, 12.56, -40.15, 0.63398, 2, 31, -3.31, 23.81, 0.40952, 30, 1.21, -20.92, 0.59048, 3, 31, -9.34, 8.5, 0.37963, 29, 28.84, 4.34, 0.00991, 30, -5.96, -6.12, 0.61046, 3, 31, -7.28, -12.18, 0.49339, 29, 12.13, -8.01, 0.45523, 30, -22.57, 6.38, 0.05138, 2, 31, -4.02, -32.2, 0.43047, 29, -3.4, -21.06, 0.56953, 3, 31, -8.94, 0.37, 0.53178, 29, 22.06, -0.16, 0.12094, 30, -12.21, -0.9, 0.34727, 3, 31, 1.05, -9.31, 0.80407, 29, 18.86, -13.7, 0.191, 30, -26.1, -1.7, 0.00493, 2, 31, 10.13, 15.87, 0.79928, 30, -13.73, -25.44, 0.20072, 2, 31, 15.95, 33.58, 0.64841, 30, -4.64, -41.7, 0.35159, 2, 31, 14.49, 24.66, 0.73287, 30, -10.21, -34.6, 0.26713, 1, 30, 35.45, 12.67, 1, 2, 29, 11.35, 41.78, 0.00129, 30, 24.93, 21.33, 0.99871, 2, 29, 0.35, 31.83, 0.0041, 30, 12.26, 29.03, 0.9959, 1, 30, 31.14, 10.73, 1, 2, 29, 21.59, 40.03, 0.0006, 30, 26.18, 11.02, 0.9994, 2, 31, 30.38, 26.27, 0.87491, 30, -19.79, -47.38, 0.12509, 2, 31, 23.59, 15.04, 0.92076, 30, -23.45, -34.77, 0.07924, 2, 31, 17.66, 3.96, 0.98658, 30, -27.58, -22.91, 0.01342, 2, 31, 14.79, -5.66, 0.96484, 29, 29.03, -23.63, 0.03516, 2, 31, 9.95, -17.86, 0.76863, 29, 16.07, -25.73, 0.23137, 2, 31, 1.72, -28.47, 0.53207, 29, 2.74, -24.09, 0.46793, 2, 31, 5.49, -30.88, 0.58159, 29, 2.6, -28.56, 0.41841, 2, 31, 24.33, -14.09, 0.91673, 29, 26.67, -36.15, 0.08327, 2, 31, 15.95, -22.73, 0.77466, 29, 14.96, -33.38, 0.22534, 2, 31, 40.55, 4.88, 0.99706, 30, -42.42, -40.37, 0.00294, 2, 31, 31.98, -5.13, 0.98856, 29, 38.28, -38.14, 0.01144, 2, 31, 42.82, 14.17, 0.98188, 30, -37.12, -48.33, 0.01812, 2, 31, 41.32, -10.06, 0.97746, 29, 38.83, -48.68, 0.02254, 2, 31, 39.44, -21.49, 0.91721, 29, 28.05, -52.92, 0.08279, 2, 31, 32.65, -30.28, 0.83607, 29, 17.02, -51.58, 0.16393, 2, 31, 27.5, -37.22, 0.76738, 29, 8.42, -50.72, 0.23262, 2, 31, 21.07, -44.4, 0.6993, 29, -1.03, -48.87, 0.3007], "hull": 62, "edges": [0, 122, 0, 2, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 118, 120, 120, 122, 116, 118, 114, 116, 110, 112, 112, 114, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 30, 32, 32, 34, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 133, "height": 166}}, "zuiba2": {"zuiba2": {"type": "mesh", "uvs": [0.86284, 0.05056, 0.93577, 0.15422, 0.99093, 0.23265, 0.98994, 0.37573, 0.98868, 0.55664, 0.9245, 0.66251, 0.82577, 0.82537, 0.69929, 0.90768, 0.57558, 0.98819, 0.43657, 0.98792, 0.27301, 0.98761, 0.11184, 0.9075, 0.00084, 0.77571, 0.01544, 0.62147, 0.02917, 0.47628, 0.17157, 0.39234, 0.34329, 0.29112, 0.48223, 0.15346, 0.62459, 0.01242, 0.79562, 0.01092], "triangles": [3, 1, 2, 0, 1, 19, 19, 1, 3, 4, 5, 3, 18, 19, 17, 3, 5, 19, 15, 13, 14, 11, 13, 15, 12, 13, 11, 19, 5, 17, 5, 16, 17, 5, 6, 16, 7, 16, 6, 9, 10, 15, 16, 9, 15, 11, 15, 10, 7, 9, 16, 8, 9, 7], "vertices": [77.2, 13.02, 78.89, 2.4, 80.17, -5.64, 74.27, -15.45, 66.82, -27.86, 57.32, -32.09, 42.71, -38.6, 29.12, -38.23, 15.83, -37.87, 4.59, -31.22, -8.64, -23.39, -18.44, -10.17, -22.07, 4.21, -14.62, 14.14, -7.61, 23.49, 7.33, 22.48, 25.35, 21.25, 42.2, 24.11, 59.45, 27.03, 73.36, 18.96], "hull": 20, "edges": [0, 38, 20, 22, 22, 24, 36, 38, 28, 30, 30, 32, 32, 34, 34, 36, 0, 2, 2, 4, 24, 26, 26, 28, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8], "width": 94, "height": 80}}, "zhizhu2": {"zhizhu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [178.95, -28.35, 3.1, -105.43, -58.32, 34.69, 117.53, 111.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 192, "height": 153}}, "meimao1": {"meimao1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-59.04, -24.59, 19.42, 63.54, 85.14, 5.03, 6.69, -83.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 118, "height": 88}}, "meimao2": {"meimao2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-59.53, -31.9, -17.95, 49.05, 38.98, 19.81, -2.6, -61.14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 64}}, "zhizhu4": {"zhizhu4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40.7, 24.79, 43.15, -24.15, -13.78, -26.99, -16.22, 21.95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 49, "height": 57}}, "zuoshou": {"zuoshou": {"type": "mesh", "uvs": [0.01618, 0.00415, 0.03004, 0.01631, 0.03715, 0.05697, 0.04447, 0.09878, 0.05952, 0.1438, 0.08131, 0.20899, 0.1095, 0.24012, 0.16917, 0.29566, 0.19898, 0.35083, 0.23601, 0.38348, 0.28979, 0.41678, 0.31316, 0.42177, 0.33923, 0.34753, 0.40256, 0.32548, 0.40178, 0.26098, 0.3777, 0.18731, 0.366, 0.16156, 0.39401, 0.16172, 0.41951, 0.19164, 0.48599, 0.2891, 0.57434, 0.25024, 0.64871, 0.26153, 0.67867, 0.26446, 0.75961, 0.26427, 0.84064, 0.26938, 0.89069, 0.28726, 0.57089, 0.34292, 0.53617, 0.36376, 0.54127, 0.3847, 0.73971, 0.34824, 0.85506, 0.36261, 0.9405, 0.38768, 0.97458, 0.40527, 0.98222, 0.41861, 0.88494, 0.40213, 0.91222, 0.42493, 0.9356, 0.44446, 0.96757, 0.47118, 0.85935, 0.4705, 0.7794, 0.46999, 0.73079, 0.48496, 0.6656, 0.50503, 0.71719, 0.54541, 0.77374, 0.58967, 0.83614, 0.6385, 0.84397, 0.66789, 0.85018, 0.71278, 0.85193, 0.7361, 0.88837, 0.78609, 0.8884, 0.8595, 0.88842, 0.92143, 0.8624, 0.98223, 0.83294, 0.99474, 0.8003, 0.9967, 0.77846, 0.99196, 0.74639, 0.96383, 0.69141, 0.91561, 0.63997, 0.87049, 0.56647, 0.80603, 0.5286, 0.7824, 0.48507, 0.75526, 0.4641, 0.74401, 0.43542, 0.74084, 0.41642, 0.69714, 0.39176, 0.66143, 0.37574, 0.63821, 0.3484, 0.59861, 0.33474, 0.58085, 0.29119, 0.56175, 0.21008, 0.48324, 0.11209, 0.41698, 0.07457, 0.38631, 0.05861, 0.34591, 0.04389, 0.30867, 0.02843, 0.26953, 0.01515, 0.20863, 0.00501, 0.16213, 0.00508, 0.12705, 0.00515, 0.09452, 0.01635, 0.03651], "triangles": [9, 70, 8, 70, 71, 8, 71, 72, 8, 72, 7, 8, 72, 73, 7, 11, 69, 10, 69, 70, 9, 69, 9, 10, 38, 36, 37, 38, 35, 36, 38, 34, 35, 38, 39, 34, 39, 30, 34, 34, 31, 33, 31, 32, 33, 34, 30, 31, 40, 41, 29, 40, 29, 39, 39, 29, 30, 41, 64, 28, 28, 13, 27, 27, 13, 19, 67, 13, 28, 29, 41, 28, 27, 19, 26, 25, 23, 24, 25, 26, 23, 26, 22, 23, 26, 21, 22, 26, 20, 21, 26, 19, 20, 13, 14, 19, 19, 14, 18, 14, 15, 18, 15, 17, 18, 15, 16, 17, 73, 6, 7, 73, 74, 6, 74, 5, 6, 74, 75, 5, 75, 4, 5, 75, 76, 4, 76, 77, 4, 77, 3, 4, 77, 78, 3, 78, 2, 3, 78, 79, 2, 79, 1, 2, 79, 0, 1, 28, 66, 67, 12, 13, 11, 68, 11, 67, 13, 67, 11, 68, 69, 11, 52, 53, 55, 50, 51, 52, 55, 53, 54, 50, 52, 55, 50, 55, 49, 55, 56, 49, 49, 56, 48, 48, 56, 47, 56, 57, 47, 57, 58, 47, 58, 46, 47, 46, 43, 45, 45, 43, 44, 43, 46, 58, 58, 59, 43, 59, 42, 43, 59, 60, 42, 42, 60, 41, 60, 63, 41, 63, 60, 61, 63, 61, 62, 41, 63, 64, 28, 64, 65, 28, 65, 66], "vertices": [1, 48, 54.15, -20.14, 1, 1, 48, 50.65, -20.38, 1, 1, 48, 43.57, -15.88, 1, 1, 48, 36.28, -11.26, 1, 1, 48, 27.59, -7.26, 1, 1, 48, 15, -1.45, 1, 1, 48, 6.9, -1.08, 1, 1, 47, 13.98, -1.68, 1, 3, 49, -37.49, 41.83, 0.00098, 46, 24.64, 1.69, 0.33835, 47, 2, 1.69, 0.66067, 3, 49, -33.28, 33.43, 0.00154, 46, 15.27, 1.08, 0.76381, 47, -7.38, 1.08, 0.23465, 3, 45, 26.47, -1.71, 0.04511, 49, -26.27, 23.97, 0.00479, 46, 3.82, -1.71, 0.9501, 4, 45, 22.95, -4.19, 0.6554, 49, -22.61, 21.69, 0.07894, 46, 0.3, -4.19, 0.26383, 47, -22.34, -4.19, 0.00184, 4, 45, 31.29, -17.67, 0.60259, 49, -13.44, 34.61, 0.38907, 46, 8.65, -17.67, 0.00497, 47, -14, -17.67, 0.00337, 3, 45, 27.23, -29.17, 0.4218, 49, -1.26, 35.33, 0.57484, 47, -18.06, -29.17, 0.00336, 3, 45, 37.24, -37.71, 0.35477, 49, 2.73, 47.87, 0.64132, 47, -8.05, -37.71, 0.00392, 3, 45, 51.4, -44.34, 0.35827, 49, 3.34, 63.49, 0.6375, 47, 6.1, -44.34, 0.00423, 3, 45, 56.73, -46.22, 0.35877, 49, 3, 69.14, 0.63698, 47, 11.44, -46.22, 0.00425, 3, 45, 53.41, -49.97, 0.35857, 49, 7.75, 67.54, 0.63719, 47, 8.12, -49.97, 0.00424, 3, 45, 45.81, -49.4, 0.35724, 49, 10.17, 60.31, 0.63857, 47, 0.52, -49.4, 0.00418, 3, 45, 23.01, -45.3, 0.29581, 49, 15.25, 37.7, 0.7006, 47, -22.29, -45.3, 0.00359, 3, 45, 18.59, -62.43, 0.19484, 49, 32.75, 40.28, 0.80084, 47, -26.71, -62.43, 0.00431, 3, 45, 8.1, -70.94, 0.16094, 49, 44.67, 33.92, 0.8341, 47, -37.19, -70.94, 0.00496, 3, 45, 4.12, -74.59, 0.14655, 49, 49.57, 31.67, 0.84817, 47, -41.17, -74.59, 0.00528, 3, 45, -5.37, -85.54, 0.11654, 49, 63.35, 27.17, 0.87751, 47, -50.66, -85.54, 0.00596, 3, 45, -15.69, -95.78, 0.10109, 49, 76.79, 21.64, 0.8926, 47, -60.98, -95.78, 0.00631, 3, 45, -24.33, -100.13, 0.09836, 49, 84.16, 15.37, 0.89527, 47, -69.62, -100.13, 0.00637, 3, 45, 4.74, -49.53, 0.20002, 49, 26.24, 22.51, 0.79623, 47, -40.55, -49.53, 0.00375, 3, 45, 5.63, -42.05, 0.19185, 49, 19.01, 20.42, 0.80558, 47, -39.67, -42.05, 0.00257, 3, 45, 1.81, -39.93, 0.11881, 49, 18.53, 16.08, 0.87973, 47, -43.49, -39.93, 0.00147, 2, 49, 54.6, 12.02, 0.02262, 50, 14.16, 12.02, 0.97738, 1, 51, 5.9, 2.77, 1, 1, 51, 18.82, -6.88, 1, 1, 51, 23.49, -12.2, 1, 1, 51, 23.94, -15.21, 1, 2, 50, 35.41, -6.56, 0.04792, 51, 8.45, -6.56, 0.95208, 2, 50, 38.59, -12.51, 0.08804, 51, 11.64, -12.51, 0.91196, 2, 50, 41.32, -17.6, 0.05945, 51, 14.36, -17.6, 0.94055, 2, 50, 45.04, -24.57, 0.02688, 51, 18.09, -24.57, 0.97312, 2, 50, 26.69, -18.37, 0.38437, 51, -0.26, -18.37, 0.61563, 4, 44, 52.39, -55.67, 0.0009, 49, 53.57, -13.79, 0.0239, 50, 13.13, -13.79, 0.91318, 51, -13.82, -13.79, 0.06201, 4, 44, 56.32, -47.33, 0.02061, 49, 44.35, -13.97, 0.26397, 50, 3.91, -13.97, 0.71432, 51, -23.04, -13.97, 0.0011, 3, 44, 61.59, -36.14, 0.22378, 49, 31.98, -14.2, 0.70326, 50, -8.45, -14.2, 0.07296, 3, 44, 49.24, -36.94, 0.60853, 49, 38.17, -24.92, 0.391, 50, -2.26, -24.92, 0.00047, 2, 44, 35.71, -37.82, 0.80264, 49, 44.96, -36.66, 0.19736, 2, 44, 20.77, -38.79, 0.90341, 49, 52.44, -49.62, 0.09659, 2, 44, 15.52, -35.58, 0.92497, 49, 51.9, -55.75, 0.07503, 2, 44, 8.21, -29.94, 0.95989, 49, 50.09, -64.8, 0.04011, 2, 44, 4.6, -26.83, 0.97626, 49, 48.89, -69.41, 0.02374, 2, 44, -7.24, -24.32, 0.99514, 49, 51.89, -81.14, 0.00486, 2, 44, -17.91, -13.82, 0.99988, 49, 47.21, -95.36, 0.00012, 1, 44, -26.92, -4.95, 1, 1, 44, -32.49, 7.07, 1, 1, 44, -30.61, 12.62, 1, 1, 44, -26.79, 17.06, 1, 1, 44, -23.36, 19.17, 1, 1, 44, -15.24, 19.23, 1, 1, 44, -1.33, 19.34, 1, 1, 44, 11.69, 19.44, 1, 1, 44, 30.29, 19.59, 1, 1, 44, 38.48, 21.04, 1, 1, 44, 47.9, 22.7, 1, 2, 44, 52.17, 23.77, 0.99829, 45, -44.35, 18.66, 0.00171, 2, 44, 56.23, 26.97, 0.99461, 45, -40.48, 22.1, 0.00539, 2, 44, 64.97, 23.14, 0.96139, 45, -31.53, 18.81, 0.03861, 2, 44, 73.25, 21.17, 0.85591, 45, -23.14, 17.34, 0.14409, 2, 44, 78.64, 19.89, 0.73007, 45, -17.69, 16.39, 0.26993, 2, 44, 87.83, 17.71, 0.39917, 45, -8.38, 14.77, 0.60083, 3, 44, 92.13, 16.91, 0.21836, 45, -4.04, 14.23, 0.77821, 46, -26.69, 14.23, 0.00343, 3, 44, 100.38, 19.73, 0.03768, 45, 4.02, 17.54, 0.91374, 46, -18.63, 17.54, 0.04858, 4, 45, 25.63, 17.95, 0.25152, 49, -44.06, 15.56, 0.00033, 46, 2.98, 17.95, 0.67378, 47, -19.67, 17.95, 0.07437, 4, 48, -20.59, 22.29, 0.01094, 49, -56.49, 33.89, 0.00095, 46, 24.7, 22.29, 0.38641, 47, 2.05, 22.29, 0.6017, 4, 48, -11.47, 23.23, 0.07448, 49, -60.91, 41.93, 0.00063, 46, 33.83, 23.23, 0.20653, 47, 11.18, 23.23, 0.71836, 4, 48, -3.38, 19.97, 0.24333, 49, -61.04, 50.65, 0.0003, 46, 41.92, 19.97, 0.07993, 47, 19.27, 19.97, 0.67644, 4, 48, 4.08, 16.96, 0.56151, 49, -61.16, 58.7, 8e-05, 46, 49.37, 16.96, 0.0146, 47, 26.73, 16.96, 0.4238, 3, 48, 11.92, 13.8, 0.86989, 49, -61.29, 67.14, 1e-05, 47, 34.56, 13.8, 0.13011, 1, 48, 22.84, 7.42, 1, 1, 48, 31.18, 2.55, 1, 1, 48, 36.57, -2.16, 1, 1, 48, 41.56, -6.53, 1, 1, 48, 49.16, -15.82, 1], "hull": 80, "edges": [0, 158, 0, 2, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 60, 62, 62, 64, 64, 66, 66, 68, 88, 90, 90, 92, 92, 94, 94, 96, 100, 102, 102, 104, 104, 106, 106, 108, 120, 122, 122, 124, 124, 126, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 156, 158, 146, 148, 142, 144, 144, 146, 148, 150, 150, 152, 6, 8, 8, 10, 152, 154, 154, 156, 2, 4, 4, 6, 24, 26, 22, 24, 18, 20, 20, 22, 56, 58, 58, 60, 52, 54, 54, 56, 74, 76, 76, 78, 68, 70, 70, 72, 72, 74, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 112, 114, 114, 116, 116, 118, 118, 120, 130, 132, 126, 128, 128, 130, 96, 98, 98, 100, 108, 110, 110, 112], "width": 179, "height": 204}}, "fazhang": {"fazhang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [415.3, 4.71, 13.71, -148.98, -67.43, 63.02, 334.17, 216.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 430, "height": 227}}, "heshangtou1": {"heshangtou1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-54.25, -55.1, 41.69, 131.7, 237.39, 31.19, 141.44, -155.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 210, "height": 220}}, "tou2": {"tou2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-121.83, -53.8, 80.97, 174.01, 292.35, -14.16, 89.55, -241.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 305, "height": 283}}, "tu2": {"tu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [167.56, 62.4, 184.14, 8.91, -2.12, -48.81, -18.7, 4.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 195}}, "tui6": {"tui6": {"type": "mesh", "uvs": [0.18259, 0.00132, 0.23107, 0.0426, 0.27111, 0.07668, 0.30642, 0.10674, 0.36861, 0.15968, 0.50204, 0.27327, 0.57036, 0.33143, 0.63562, 0.38698, 0.70301, 0.46576, 0.757, 0.52886, 0.84361, 0.65114, 0.91281, 0.74884, 0.94419, 0.82473, 0.98485, 0.92307, 0.99592, 1, 0.96914, 0.98204, 0.86224, 0.8532, 0.74805, 0.71559, 0.62017, 0.56148, 0.52622, 0.48552, 0.40993, 0.39152, 0.31415, 0.31408, 0.27865, 0.30304, 0.24296, 0.29194, 0.2074, 0.28088, 0.18971, 0.2996, 0.15688, 0.33435, 0.11512, 0.37855, 0.06001, 0.43687, 0.0022, 0.37134, 0.04111, 0.3103, 0.07922, 0.25051, 0.11797, 0.14643, 0.1481, 0.06551, 0.16794, 0.01222], "triangles": [15, 13, 14, 15, 16, 13, 16, 12, 13, 16, 11, 12, 16, 17, 11, 17, 10, 11, 17, 9, 10, 17, 18, 9, 18, 8, 9, 18, 19, 8, 19, 7, 8, 19, 6, 7, 19, 20, 6, 20, 5, 6, 20, 21, 5, 5, 21, 4, 4, 21, 22, 22, 23, 4, 23, 24, 4, 24, 3, 4, 28, 29, 27, 29, 30, 27, 27, 30, 26, 25, 26, 31, 26, 30, 31, 25, 31, 24, 31, 32, 24, 24, 32, 3, 2, 32, 33, 2, 33, 1, 3, 32, 2, 33, 34, 1, 34, 0, 1], "vertices": [1, 91, 46.43, 18.83, 1, 1, 91, 45.14, 10.14, 1, 2, 91, 44.07, 2.97, 0.93013, 92, -9.89, 11.82, 0.06987, 2, 91, 43.13, -3.36, 0.60053, 92, -3.49, 11.91, 0.39947, 2, 91, 41.48, -14.5, 0.03087, 92, 7.77, 12.07, 0.96913, 1, 92, 31.94, 12.41, 1, 1, 92, 44.31, 12.58, 1, 1, 92, 56.13, 12.74, 1, 1, 92, 70.76, 10.67, 1, 1, 92, 82.47, 9.02, 1, 1, 92, 103.64, 4.15, 1, 1, 92, 120.55, 0.27, 1, 1, 92, 131.79, -4.8, 1, 1, 92, 146.35, -11.37, 1, 1, 92, 155.98, -18.41, 1, 1, 92, 151.68, -18.98, 1, 1, 92, 128.04, -15.3, 1, 1, 92, 102.8, -11.35, 1, 1, 92, 74.53, -6.94, 1, 1, 92, 57.96, -7.6, 1, 1, 92, 37.46, -8.42, 1, 2, 91, 18.54, -23.74, 0.03036, 92, 20.58, -9.09, 0.96964, 2, 91, 17.15, -19.2, 0.12433, 92, 16.31, -11.19, 0.87567, 2, 91, 15.76, -14.63, 0.32834, 92, 12.03, -13.3, 0.67166, 2, 91, 14.37, -10.07, 0.69256, 92, 7.76, -15.4, 0.30744, 2, 91, 10.74, -10.1, 0.89261, 92, 8.37, -18.98, 0.10739, 2, 91, 4, -10.15, 0.98979, 92, 9.5, -25.63, 0.01021, 1, 91, -4.58, -10.22, 1, 1, 91, -15.9, -10.3, 1, 1, 91, -12.38, 1.6, 1, 1, 91, -1.98, 3.54, 1, 1, 91, 8.21, 5.44, 1, 1, 91, 23.84, 11.47, 1, 1, 91, 35.98, 16.16, 1, 1, 91, 43.98, 19.24, 1], "hull": 35, "edges": [0, 68, 26, 28, 28, 30, 56, 58, 46, 48, 0, 2, 2, 4, 66, 68, 62, 64, 64, 66, 58, 60, 60, 62, 52, 54, 54, 56, 40, 42, 8, 10, 36, 38, 38, 40, 10, 12, 12, 14, 14, 16, 16, 18, 34, 36, 18, 20, 20, 22, 30, 32, 32, 34, 22, 24, 24, 26, 48, 50, 50, 52, 4, 6, 6, 8, 42, 44, 44, 46], "width": 125, "height": 154}}, "cao": {"cao": {"type": "mesh", "uvs": [0.98668, 0, 0.99554, 0, 0.97688, 0.11378, 0.97294, 0.18928, 0.969, 0.26479, 0.9765, 0.30788, 0.9977, 0.34315, 0.99789, 0.99956, 0.92466, 0.99676, 0.88581, 0.91327, 0.83044, 0.88627, 0.77102, 0.87533, 0.71779, 0.86553, 0.66797, 0.85509, 0.56783, 0.8724, 0.50511, 0.88323, 0.4833, 0.89074, 0.41959, 0.9176, 0.38112, 0.93879, 0.32772, 0.99682, 0.19454, 0.99687, 0.18701, 0.93863, 0.17694, 0.86082, 0.16432, 0.81844, 0.14768, 0.76255, 0.10717, 0.68861, 0.08658, 0.65103, 0.05296, 0.58965, 0.05434, 0.58012, 0.09256, 0.59463, 0.14182, 0.6563, 0.19215, 0.71932, 0.24514, 0.78565, 0.2886, 0.84008, 0.28871, 0.76833, 0.28879, 0.71971, 0.27616, 0.64024, 0.27538, 0.61787, 0.30676, 0.67868, 0.32519, 0.77036, 0.32598, 0.85004, 0.35397, 0.81598, 0.377, 0.78797, 0.40545, 0.75336, 0.43573, 0.71652, 0.45093, 0.71297, 0.47521, 0.7073, 0.43612, 0.78472, 0.40024, 0.85575, 0.4804, 0.80028, 0.58284, 0.72938, 0.67918, 0.71303, 0.757, 0.69982, 0.78986, 0.69424, 0.83749, 0.68615, 0.79139, 0.64898, 0.72922, 0.59885, 0.67281, 0.56538, 0.61147, 0.52899, 0.56995, 0.50847, 0.50441, 0.4876, 0.43919, 0.46684, 0.42184, 0.46653, 0.29772, 0.51081, 0.31948, 0.48549, 0.38004, 0.44277, 0.45794, 0.41571, 0.51809, 0.42051, 0.5834, 0.42573, 0.64866, 0.44259, 0.71805, 0.46052, 0.7895, 0.50422, 0.83402, 0.53144, 0.86682, 0.56543, 0.89864, 0.59841, 0.88872, 0.51325, 0.88002, 0.43854, 0.86995, 0.35208, 0.8928, 0.26801, 0.91969, 0.16904, 0.94171, 0.11477, 0.96868, 0.04832, 0.54896, 0.78566], "triangles": [33, 19, 21, 19, 20, 21, 25, 30, 24, 25, 26, 30, 26, 29, 30, 26, 27, 29, 27, 28, 29, 23, 24, 31, 24, 30, 31, 33, 22, 32, 33, 21, 22, 22, 23, 32, 23, 31, 32, 35, 38, 39, 35, 36, 38, 38, 36, 37, 33, 34, 39, 34, 35, 39, 19, 33, 40, 40, 33, 39, 47, 45, 46, 43, 44, 47, 47, 44, 45, 47, 42, 43, 47, 48, 42, 19, 40, 18, 18, 40, 48, 18, 48, 17, 48, 40, 41, 17, 48, 16, 16, 48, 49, 41, 42, 48, 15, 16, 49, 15, 82, 14, 15, 49, 82, 13, 14, 50, 14, 82, 50, 50, 82, 49, 13, 51, 12, 12, 51, 52, 13, 50, 51, 10, 54, 9, 11, 53, 10, 10, 53, 54, 12, 52, 11, 11, 52, 53, 62, 64, 65, 62, 63, 64, 61, 66, 60, 61, 62, 66, 62, 65, 66, 60, 67, 59, 59, 67, 68, 60, 66, 67, 58, 69, 57, 57, 69, 70, 59, 68, 58, 58, 68, 69, 9, 54, 74, 54, 73, 74, 54, 55, 73, 56, 71, 55, 55, 72, 73, 55, 71, 72, 57, 70, 56, 56, 70, 71, 79, 80, 3, 3, 80, 2, 80, 81, 2, 1, 81, 0, 1, 2, 81, 78, 79, 4, 4, 79, 3, 6, 76, 5, 5, 77, 78, 78, 4, 5, 5, 76, 77, 74, 75, 6, 6, 75, 76, 7, 74, 6, 8, 9, 7, 74, 7, 9], "vertices": [2, 125, 78.11, -10.99, 7e-05, 126, 52.43, -1.19, 0.99993, 2, 125, 78.51, -14.62, 7e-05, 126, 53.5, -4.68, 0.99993, 1, 126, 19.05, -7.19, 1, 2, 125, 21.8, -11.45, 0.68135, 126, -2.79, -12.19, 0.31865, 2, 124, 38.36, -12.13, 0.61298, 125, -0.6, -12.26, 0.38702, 3, 123, 91.68, -16.85, 0.01313, 124, 25.62, -15.27, 0.98442, 125, -12.94, -16.72, 0.00244, 2, 122, -110.52, 8.26, 0.288, 124, 15.22, -24.05, 0.712, 1, 122, 83.69, 14.06, 1, 2, 122, 83.75, -16.13, 0.79518, 131, -23.59, 25.54, 0.20482, 2, 122, 59.52, -32.85, 0.18006, 131, 1.82, 10.67, 0.81994, 2, 131, 25.79, 13.8, 0.99991, 132, -34.31, 32.44, 9e-05, 2, 131, 49.11, 21.92, 0.84583, 132, -9.81, 29.35, 0.15417, 3, 131, 70, 29.2, 0.28691, 132, 12.13, 26.57, 0.70556, 133, -25.98, 35.6, 0.00753, 3, 131, 89.72, 35.67, 0.01869, 132, 32.68, 23.61, 0.79025, 133, -7.18, 26.83, 0.19107, 4, 132, 73.9, 28.97, 0.00246, 133, 33.84, 20.05, 0.92299, 141, 70.88, -64.56, 0.07403, 143, 15.13, -64.56, 0.00053, 3, 133, 59.53, 15.8, 0.6275, 141, 51.24, -47.45, 0.37019, 143, -4.5, -47.45, 0.00231, 3, 133, 68.78, 15.38, 0.49943, 141, 43.59, -42.24, 0.49771, 143, -12.15, -42.24, 0.00286, 4, 133, 96.2, 15.57, 0.15898, 141, 20.16, -28, 0.83613, 143, -35.59, -28, 0.0027, 134, 88.75, 14.06, 0.00219, 4, 133, 113.18, 17.1, 0.03214, 141, 4.91, -20.38, 0.88343, 143, -50.83, -20.38, 0.00095, 134, 73.21, 7.04, 0.08348, 3, 141, -22.57, -15.45, 0.11758, 143, -78.31, -15.45, 3e-05, 134, 52.06, -11.16, 0.8824, 1, 134, -2.75, -13.78, 1, 3, 135, 12.11, 24.88, 0.12771, 136, -40.32, 24.88, 1e-05, 134, -6.67, 3.29, 0.87228, 2, 135, 33.23, 14.79, 0.90855, 134, -11.91, 26.1, 0.09145, 3, 135, 46.44, 11.69, 0.84711, 136, -5.98, 11.69, 0.14882, 134, -17.7, 38.38, 0.00407, 1, 136, 11.45, 7.59, 1, 2, 136, 38.96, 8.36, 0.15791, 137, 4.01, 8.36, 0.84209, 1, 137, 17.99, 8.75, 1, 2, 136, 75.79, 9.38, 0.00028, 137, 40.83, 9.38, 0.99972, 2, 136, 77.74, 7.27, 0.00029, 137, 42.79, 7.27, 0.99971, 2, 136, 65.06, -3, 0.00032, 137, 30.11, -3, 0.99968, 2, 136, 38.39, -8.82, 0.23082, 137, 3.43, -8.82, 0.76918, 3, 138, 51.65, 46.56, 0.00157, 135, 63.56, -14.76, 0.09049, 136, 11.13, -14.76, 0.90793, 3, 138, 33.17, 23.75, 0.16417, 135, 34.88, -21.01, 0.80666, 136, -17.55, -21.01, 0.02916, 2, 138, 18.01, 5.03, 0.86834, 135, 11.34, -26.14, 0.13166, 1, 139, 9.08, 6.08, 1, 2, 139, 23.45, 6.79, 0.06124, 140, 3.35, 6.79, 0.93876, 2, 139, 46.67, 13.2, 0.00031, 140, 26.58, 13.2, 0.99969, 1, 140, 33.17, 13.87, 1, 1, 140, 15.87, 0.03, 1, 3, 142, -6.78, 30.05, 1e-05, 138, 39.39, -8.96, 0.06513, 139, 9.25, -8.96, 0.93486, 3, 141, 9.32, 14.07, 0.39083, 138, 15.86, -10.5, 0.6091, 140, -34.38, -10.5, 7e-05, 5, 141, 24.52, 12.21, 0.80035, 142, -8.92, 12.21, 0.0762, 143, -31.22, 12.21, 4e-05, 138, 26.52, -21.5, 0.12341, 140, -23.72, -21.5, 1e-05, 4, 141, 37.03, 10.68, 0.3426, 142, 3.58, 10.68, 0.65224, 138, 35.29, -30.54, 0.00516, 140, -14.95, -30.54, 0, 2, 142, 19.03, 8.78, 0.71213, 143, -3.26, 8.78, 0.28787, 2, 142, 35.48, 6.77, 0.0088, 143, 13.18, 6.77, 0.9912, 1, 143, 18.14, 2.8, 1, 3, 142, 48.37, -3.53, 0.00019, 143, 26.07, -3.53, 0.99981, 134, 108.68, 77.33, 0, 3, 142, 20.55, -6.82, 0.74775, 143, -1.75, -6.82, 0.25225, 134, 93.68, 53.68, 0, 4, 133, 98.66, -4.24, 0.0813, 141, 28.47, -9.85, 0.81315, 142, -4.97, -9.85, 0.10364, 143, -27.27, -9.85, 0.00192, 3, 133, 62.34, -10.63, 0.60567, 141, 62.74, -23.49, 0.39191, 143, 7, -23.49, 0.00243, 3, 132, 67.97, -13.4, 0.48, 133, 15.91, -18.8, 0.52, 143, 50.79, -40.92, 0, 1, 132, 28.31, -18.47, 1, 3, 127, 53.48, 34.93, 0.09884, 131, 77.64, -21.88, 0.4733, 132, -3.73, -22.57, 0.42786, 3, 127, 43.4, 25.75, 0.2854, 131, 66.3, -29.44, 0.59829, 132, -17.26, -24.3, 0.11631, 3, 127, 28.79, 12.43, 0.78563, 131, 49.85, -40.41, 0.21309, 132, -36.87, -26.81, 0.00128, 2, 127, 50.64, 14.46, 0.99801, 131, 71.76, -41.69, 0.00199, 2, 127, 80.12, 17.19, 0.9687, 128, -13.3, 24.24, 0.0313, 2, 127, 104.8, 22.57, 0.25662, 128, 11.54, 19.63, 0.74338, 1, 128, 38.55, 14.62, 1, 2, 128, 56.58, 12.42, 0.6999, 129, -3.96, 12.6, 0.3001, 1, 129, 23.71, 11.29, 1, 1, 130, 12.97, 6.52, 1, 2, 128, 118.83, 13.6, 0, 130, 20.06, 5.61, 1, 3, 128, 165.89, 37.53, 0, 129, 106.31, 33.11, 3e-05, 130, 72.37, 12.73, 0.99997, 3, 128, 158.77, 28.26, 0, 129, 98.8, 24.15, 3e-05, 130, 62.6, 6.32, 0.99997, 3, 128, 137.17, 10.49, 0, 129, 76.48, 7.29, 1e-05, 130, 36.36, -3.36, 0.99999, 3, 128, 107.59, -4.32, 0, 129, 46.31, -6.27, 0.15778, 130, 3.55, -7.62, 0.84222, 1, 129, 21.67, -9.25, 1, 3, 128, 56.5, -12.69, 0.76409, 129, -5.09, -12.48, 0.23588, 130, -47.45, 1.29, 3e-05, 1, 128, 29.17, -13.67, 1, 2, 127, 107.58, -13.53, 0.38602, 128, 0.11, -14.72, 0.61398, 2, 127, 76.09, -20.03, 0.99954, 128, -31.44, -8.51, 0.00046, 2, 123, 29.07, 45.63, 0.02134, 127, 56.47, -24.08, 0.97866, 2, 123, 18.23, 32.73, 0.12631, 127, 39.63, -23.7, 0.87369, 2, 123, 7.71, 20.22, 0.55363, 127, 23.29, -23.33, 0.44637, 2, 123, 33.12, 22.82, 0.98416, 127, 41.21, -41.52, 0.01584, 2, 123, 55.4, 25.1, 0.79589, 124, -13.21, 24.32, 0.20411, 2, 123, 81.19, 27.74, 0.14135, 124, 12.36, 28.57, 0.85865, 2, 124, 37.28, 19.26, 0.73884, 125, -4.95, 18.84, 0.26116, 2, 125, 25.37, 11.01, 0.73323, 126, -3.49, 10.54, 0.26677, 1, 126, 14.53, 6.57, 1, 2, 125, 63.09, -5.17, 4e-05, 126, 36.59, 1.71, 0.99996, 3, 133, 34.02, -6.78, 0.93076, 141, 84.81, -41.64, 0.06876, 143, 29.07, -41.64, 0.00049], "hull": 82, "edges": [0, 162, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 54, 56, 56, 58, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 116, 118, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 58, 60, 60, 62, 62, 64, 64, 66, 48, 50, 50, 52, 52, 54, 44, 46, 46, 48, 40, 42, 42, 44, 66, 68, 68, 70, 88, 90, 90, 92, 92, 94, 94, 96, 84, 86, 86, 88, 80, 82, 82, 84, 96, 98, 98, 100, 98, 164, 100, 102, 102, 104, 20, 22, 22, 24, 26, 28, 28, 30, 118, 120, 120, 122, 132, 134, 134, 136, 136, 138, 138, 140, 112, 114, 114, 116, 140, 142, 142, 144, 108, 110, 110, 112, 144, 146, 146, 148, 104, 106, 106, 108, 152, 154, 148, 150, 150, 152, 154, 156, 156, 158, 158, 160, 160, 162, 4, 6, 6, 8], "width": 412, "height": 296}}, "toufa3": {"toufa3": {"type": "mesh", "uvs": [0.00194, 0, 0.09971, 0, 0.37738, 0.0375, 0.52914, 0.11879, 0.67533, 0.1971, 0.78306, 0.30709, 0.90965, 0.43634, 0.93349, 0.53488, 0.95138, 0.6088, 0.97595, 0.71034, 0.84854, 0.78858, 0.71017, 0.87354, 0.51203, 0.92745, 0.32532, 0.97824, 0.10515, 0.9814, 0.26068, 0.89988, 0.41394, 0.81955, 0.44713, 0.72742, 0.48624, 0.61884, 0.36973, 0.48858, 0.26489, 0.37136, 0.12842, 0.21879, 0.03895, 0.11875], "triangles": [14, 15, 13, 13, 15, 12, 15, 16, 12, 12, 16, 11, 11, 16, 10, 16, 17, 10, 10, 17, 9, 9, 18, 8, 18, 9, 17, 18, 7, 8, 18, 19, 7, 19, 6, 7, 19, 5, 6, 19, 20, 5, 20, 4, 5, 4, 21, 3, 4, 20, 21, 3, 22, 2, 3, 21, 22, 22, 1, 2, 22, 0, 1], "vertices": [1, 251, -11.91, -8.08, 1, 1, 251, -10.46, -4.34, 1, 1, 251, -2.87, 4.94, 1, 1, 251, 6.95, 7.82, 1, 1, 251, 16.41, 10.6, 1, 2, 251, 28.26, 10.77, 0.99783, 252, -12.88, 7.59, 0.00217, 2, 251, 42.19, 10.97, 0.35289, 252, 0.48, 11.52, 0.64711, 2, 251, 51.74, 8.34, 0.00826, 252, 10.39, 11.56, 0.99174, 2, 252, 17.81, 11.59, 0.97186, 253, -12.26, 6.85, 0.02814, 2, 252, 28.02, 11.62, 0.54099, 253, -3.14, 11.42, 0.45901, 2, 252, 35.31, 5.68, 0.04464, 253, 6.04, 9.33, 0.95536, 1, 253, 16, 7.07, 1, 1, 253, 23.94, 1.41, 1, 1, 253, 31.42, -3.92, 1, 1, 253, 34.94, -12.24, 1, 1, 253, 25.05, -9.2, 1, 2, 252, 36.7, -12.36, 0.00247, 253, 15.3, -6.2, 0.99753, 2, 252, 27.65, -10.12, 0.23885, 253, 6.21, -8.22, 0.76115, 2, 252, 17, -7.49, 0.96523, 253, -4.51, -10.6, 0.03477, 2, 251, 39.11, -11.57, 0.33637, 252, 3.58, -11.01, 0.66363, 2, 251, 26.62, -11.36, 0.96221, 252, -8.5, -14.17, 0.03779, 1, 251, 10.38, -11.1, 1, 1, 251, -0.28, -10.93, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 26, 28, 42, 44, 40, 42, 36, 38, 38, 40, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 32, 34, 34, 36, 18, 20, 20, 22, 22, 24, 24, 26, 28, 30, 30, 32, 4, 6, 6, 8], "width": 41, "height": 100}}, "shenti_1": {"shenti_1": {"type": "mesh", "uvs": [0.61256, 0, 0.63622, 0, 0.6693, 0.05369, 0.69724, 0.09903, 0.76833, 0.10838, 0.91261, 0.10893, 0.96702, 0.13856, 0.97334, 0.22818, 0.94437, 0.28873, 0.91538, 0.34932, 0.88669, 0.40927, 0.93758, 0.4448, 0.87096, 0.48527, 0.90155, 0.53075, 0.96814, 0.55993, 0.94751, 0.59124, 0.85446, 0.59422, 0.74781, 0.59764, 0.80823, 0.64004, 0.85389, 0.67208, 0.91725, 0.69576, 0.96297, 0.71285, 0.99912, 0.78048, 0.98947, 0.85024, 0.8876, 0.93093, 0.80392, 0.96411, 0.60474, 1, 0.57973, 1, 0.41412, 0.99299, 0.36765, 0.94136, 0.37184, 0.87105, 0.37617, 0.79849, 0.38543, 0.78591, 0.3364, 0.74717, 0.27845, 0.70138, 0.20698, 0.64491, 0.17195, 0.64055, 0.11087, 0.61919, 0.04078, 0.59468, 0.01908, 0.55643, 0, 0.52279, 0, 0.50871, 0.02396, 0.46593, 0.09887, 0.42183, 0.1525, 0.39026, 0.19751, 0.37366, 0.2303, 0.3276, 0.25487, 0.29308, 0.31398, 0.25712, 0.39566, 0.2441, 0.49891, 0.22765, 0.52578, 0.20495, 0.55548, 0.17985, 0.52544, 0.14317, 0.48885, 0.09848, 0.47679, 0.07198, 0.54317, 0.01341], "triangles": [43, 35, 37, 37, 39, 43, 35, 36, 37, 37, 38, 39, 42, 39, 41, 39, 40, 41, 43, 39, 42, 49, 45, 46, 35, 44, 45, 44, 35, 43, 12, 49, 50, 12, 50, 51, 47, 48, 49, 49, 46, 47, 9, 10, 52, 52, 12, 51, 52, 3, 4, 3, 53, 2, 2, 53, 0, 0, 1, 2, 8, 9, 4, 4, 9, 52, 7, 8, 4, 7, 5, 6, 5, 7, 4, 53, 3, 52, 56, 0, 54, 0, 53, 54, 54, 55, 56, 45, 12, 17, 13, 16, 17, 16, 13, 15, 13, 17, 12, 10, 12, 52, 15, 13, 14, 12, 10, 11, 12, 45, 49, 25, 26, 30, 30, 26, 27, 29, 30, 27, 27, 28, 29, 25, 30, 32, 30, 31, 32, 25, 19, 24, 19, 32, 18, 19, 25, 32, 23, 24, 22, 22, 24, 20, 24, 19, 20, 21, 22, 20, 32, 17, 18, 32, 33, 17, 33, 34, 17, 34, 35, 17, 17, 35, 45], "vertices": [1, 4, 112.64, 8.64, 1, 1, 4, 112.41, 3.4, 1, 1, 4, 91.86, -3.04, 1, 1, 4, 74.51, -8.48, 1, 1, 4, 70.28, -24.09, 1, 1, 4, 68.65, -56.08, 1, 2, 3, 183.02, -24.82, 0.00018, 4, 56.96, -67.65, 0.99983, 2, 3, 154.8, -43.46, 0.0524, 4, 23.14, -67.55, 0.9476, 3, 2, 206.68, -127.83, 0.0009, 3, 131.93, -49.72, 0.2193, 4, 0.62, -60.11, 0.77981, 3, 2, 185.94, -116.29, 0.01353, 3, 109.04, -55.99, 0.53501, 4, -21.91, -52.67, 0.45146, 3, 2, 165.42, -104.88, 0.0682, 3, 86.39, -62.19, 0.80091, 4, -44.2, -45.3, 0.13089, 3, 2, 149.78, -112.78, 0.10504, 3, 80.74, -78.78, 0.83835, 4, -58.09, -55.99, 0.05661, 3, 2, 138.35, -94.87, 0.18601, 3, 60.05, -73.98, 0.79574, 4, -72.67, -40.54, 0.01825, 3, 2, 120.1, -97.52, 0.29076, 3, 48.86, -88.64, 0.70838, 4, -90.1, -46.56, 0.00086, 2, 2, 105.99, -109.36, 0.30862, 3, 47.06, -106.98, 0.69138, 2, 2, 95.56, -102.18, 0.31221, 3, 34.59, -109.15, 0.68779, 2, 2, 99.23, -81.82, 0.34848, 3, 22.97, -92.03, 0.65152, 2, 2, 103.44, -58.49, 0.61588, 3, 9.65, -72.41, 0.38412, 2, 2, 84.79, -67.85, 0.87973, 3, 2.87, -92.15, 0.12027, 2, 2, 70.7, -74.92, 0.95185, 3, -2.25, -107.07, 0.04815, 2, 2, 58.77, -86.55, 0.98447, 3, -2.64, -123.72, 0.01553, 2, 2, 50.16, -94.94, 0.99272, 3, -2.92, -135.74, 0.00728, 2, 2, 23.5, -96.86, 0.99956, 3, -20.62, -155.77, 0.00044, 1, 2, -1.6, -88.71, 1, 1, 2, -25.98, -59.68, 1, 1, 2, -33.86, -38.72, 1, 1, 2, -36.82, 7.43, 1, 1, 2, -35.54, 12.83, 1, 1, 2, -24.48, 48, 1, 1, 2, -3.16, 53.54, 1, 2, 2, 22.42, 46.51, 0.99987, 275, 82.28, 118.96, 0.00013, 3, 2, 48.81, 39.27, 0.99156, 274, 72.78, 105.71, 0.0003, 275, 58.33, 105.71, 0.00814, 3, 2, 52.95, 36.17, 0.98488, 274, 67.66, 105.03, 0.00081, 275, 53.21, 105.03, 0.01431, 3, 2, 69.67, 43.39, 0.93392, 274, 60.74, 88.19, 0.00629, 275, 46.29, 88.19, 0.05979, 3, 2, 89.44, 51.92, 0.81437, 274, 52.55, 68.27, 0.02304, 275, 38.1, 68.27, 0.16259, 5, 2, 113.82, 62.45, 0.50055, 271, 56.91, 43.71, 0.00117, 273, 96.03, 50.76, 0.00016, 274, 42.46, 43.71, 0.05707, 275, 28.01, 43.71, 0.44104, 4, 2, 117.21, 69.63, 0.37125, 271, 59.5, 36.2, 0.00032, 274, 45.05, 36.2, 0.05447, 275, 30.6, 36.2, 0.57395, 3, 2, 128.18, 80.97, 0.17263, 274, 45.12, 20.43, 0.02434, 275, 30.67, 20.43, 0.80302, 2, 2, 140.76, 93.98, 0.02898, 275, 30.76, 2.33, 0.97102, 2, 274, 35.32, -9.22, 0.01821, 275, 20.87, -9.22, 0.98179, 3, 271, 41.07, -19.38, 0.0182, 274, 26.62, -19.38, 0.17963, 275, 12.17, -19.38, 0.80216, 3, 271, 36.52, -22.11, 0.04676, 274, 22.07, -22.11, 0.25403, 275, 7.62, -22.11, 0.69922, 4, 271, 19.95, -25.84, 0.29723, 272, 106.17, -26.53, 0.112, 274, 5.5, -25.84, 0.36425, 275, -8.95, -25.84, 0.22651, 5, 271, -2.86, -20.14, 0.55082, 272, 82.68, -27.69, 0.42733, 273, 57.37, -27.69, 0.00919, 274, -17.31, -20.14, 0.01162, 275, -31.76, -20.14, 0.00104, 2, 271, -19.19, -16.05, 0.488, 272, 65.87, -28.52, 0.512, 4, 270, 79.57, -26.45, 0.00153, 271, -29.7, -10.7, 0.38445, 272, 54.26, -26.45, 0.07956, 273, 28.94, -26.45, 0.53446, 4, 270, 62.52, -34.44, 0.31733, 271, -48.33, -13.4, 0.02673, 272, 37.21, -34.44, 0.24238, 273, 11.89, -34.44, 0.41355, 4, 270, 49.74, -40.42, 0.36125, 271, -62.3, -15.41, 0.00302, 272, 24.43, -40.42, 0.36952, 273, -0.89, -40.42, 0.26621, 3, 270, 30.91, -41.66, 0.3866, 272, 5.6, -41.66, 0.47423, 273, -19.71, -41.66, 0.13917, 4, 4, 22.84, 60.84, 0.00109, 270, 14.18, -33.12, 0.62027, 272, -11.13, -33.12, 0.33982, 273, -36.45, -33.12, 0.03882, 3, 4, 28.02, 37.66, 0.21229, 270, -6.98, -22.33, 0.75623, 272, -32.29, -22.33, 0.03148, 3, 4, 36.31, 31.32, 0.50928, 270, -17.14, -24.67, 0.48825, 272, -42.45, -24.67, 0.00248, 2, 4, 45.47, 24.31, 0.81363, 270, -28.38, -27.26, 0.18637, 2, 4, 59.58, 30.36, 0.96646, 270, -32.72, -41.99, 0.03354, 2, 4, 76.77, 37.73, 0.99783, 270, -38.01, -59.93, 0.00217, 2, 4, 86.87, 39.96, 0.99994, 270, -42.73, -69.13, 6e-05, 1, 4, 108.27, 24.26, 1], "hull": 57, "edges": [0, 112, 0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 62, 64, 70, 72, 80, 82, 82, 84, 88, 90, 94, 96, 108, 110, 110, 112, 68, 70, 64, 66, 66, 68, 34, 36, 36, 38, 38, 40, 40, 42, 58, 60, 60, 62, 18, 20, 14, 16, 16, 18, 104, 106, 106, 108, 2, 4, 4, 6, 100, 102, 102, 104, 96, 98, 98, 100, 90, 92, 92, 94, 84, 86, 86, 88, 72, 74, 74, 76, 76, 78, 78, 80, 30, 32, 32, 34], "width": 222, "height": 377}}, "zuodabi": {"zuodabi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39.37, 109.97, 229.02, 17.49, 168.97, -105.65, -20.68, -13.17], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 211, "height": 137}}, "yingzi": {"yingzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [410.31, 7.76, 14.6, -149.99, -55.39, 25.57, 340.32, 183.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 426, "height": 189}}, "tui11": {"tui11": {"type": "mesh", "uvs": [0.11237, 0.5346, 0.23064, 0.2553, 0.29611, 0.1007, 0.32927, 0.02239, 0.39826, 0.07443, 0.48048, 0.13645, 0.63109, 0.32333, 0.69399, 0.40138, 0.79314, 0.5244, 0.88818, 0.64232, 0.97586, 0.81029, 0.95446, 0.91261, 0.8307, 0.89556, 0.74151, 0.88328, 0.59829, 0.86355, 0.51738, 0.78732, 0.47122, 0.68956, 0.39926, 0.74971, 0.35842, 0.78384, 0.23565, 0.88645, 0.09978, 1, 0.09576, 1, 0.00927, 0.84228, 0.01092, 0.77417], "triangles": [10, 11, 9, 11, 12, 9, 9, 12, 8, 12, 13, 8, 8, 13, 7, 13, 14, 7, 7, 14, 6, 14, 15, 6, 15, 16, 6, 17, 4, 16, 16, 5, 6, 16, 4, 5, 19, 20, 0, 20, 23, 0, 21, 22, 23, 20, 21, 23, 19, 0, 18, 0, 1, 18, 18, 1, 17, 1, 4, 17, 4, 1, 2, 4, 2, 3], "vertices": [1, 63, 5.05, 5.66, 1, 1, 63, 16.58, 5.81, 1, 2, 63, 22.96, 5.9, 0.98468, 64, -8.37, -0.81, 0.01532, 2, 63, 26.19, 5.94, 0.90036, 64, -8.19, 2.42, 0.09964, 2, 63, 28.29, 1.53, 0.4131, 64, -3.65, 4.2, 0.5869, 2, 63, 30.8, -3.71, 0.00222, 64, 1.76, 6.33, 0.99778, 1, 64, 13.09, 8.56, 1, 1, 64, 17.82, 9.49, 1, 1, 64, 25.28, 10.95, 1, 1, 64, 32.43, 12.35, 1, 1, 64, 40.17, 12.29, 1, 1, 64, 41.05, 9.02, 1, 1, 64, 34.38, 4.06, 1, 1, 64, 29.58, 0.49, 1, 1, 64, 21.86, -5.25, 1, 2, 63, 18.51, -19.09, 0.01462, 64, 16.25, -6.99, 0.98538, 2, 63, 18.46, -14.83, 0.13324, 64, 11.99, -6.75, 0.86676, 2, 63, 13.8, -12.62, 0.51902, 64, 9.47, -11.24, 0.48098, 2, 63, 11.16, -11.37, 0.70934, 64, 8.04, -13.78, 0.29066, 2, 63, 3.23, -7.61, 0.98036, 64, 3.74, -21.44, 0.01964, 1, 63, -5.56, -3.45, 1, 1, 63, -5.75, -3.26, 1, 1, 63, -6.39, 4.19, 1, 1, 63, -4.84, 5.54, 1], "hull": 24, "edges": [18, 20, 20, 22, 28, 30, 30, 32, 40, 42, 42, 44, 44, 46, 10, 12, 16, 18, 22, 24, 24, 26, 26, 28, 32, 34, 38, 40, 2, 0, 0, 46, 6, 8, 8, 10, 2, 4, 4, 6, 34, 36, 36, 38, 12, 14, 14, 16], "width": 67, "height": 30}}, "tui12": {"tui12": {"type": "mesh", "uvs": [0.81944, 0.13459, 0.97061, 0.14845, 0.97688, 0.19252, 0.98295, 0.23518, 0.89859, 0.30922, 0.76919, 0.42281, 0.72387, 0.46259, 0.69082, 0.4916, 0.62468, 0.54966, 0.54812, 0.61686, 0.37714, 0.76694, 0.16036, 0.95723, 0.05335, 0.99812, 0.05344, 0.87953, 0.18382, 0.76493, 0.23124, 0.60038, 0.27701, 0.44158, 0.27711, 0.36586, 0.27722, 0.2796, 0.2773, 0.21656, 0.31814, 0.20571, 0.50969, 0.15486, 0.64596, 0.11868], "triangles": [12, 13, 11, 11, 14, 10, 11, 13, 14, 14, 15, 10, 10, 15, 9, 15, 16, 9, 9, 16, 8, 8, 16, 17, 18, 19, 20, 21, 17, 18, 7, 8, 17, 7, 17, 21, 18, 20, 21, 7, 21, 6, 6, 21, 5, 5, 21, 22, 5, 0, 4, 5, 22, 0, 4, 2, 3, 2, 0, 1, 2, 4, 0], "vertices": [1, 61, -0.32, -3.26, 1, 1, 61, -5.08, -0.58, 1, 1, 61, -4.57, 1.17, 1, 1, 61, -4.08, 2.86, 1, 1, 61, -0.09, 4.44, 1, 2, 61, 6.04, 6.86, 0.98308, 62, -1.01, 14, 0.01692, 2, 61, 8.19, 7.71, 0.93387, 62, 0.89, 12.68, 0.06613, 2, 61, 9.75, 8.33, 0.86435, 62, 2.27, 11.72, 0.13565, 2, 61, 12.88, 9.56, 0.62505, 62, 5.04, 9.81, 0.37495, 2, 61, 16.51, 11, 0.28066, 62, 8.24, 7.59, 0.71934, 2, 61, 24.6, 14.19, 0.00091, 62, 15.4, 2.63, 0.99909, 1, 62, 24.47, -3.65, 1, 1, 62, 26.81, -7.14, 1, 1, 62, 22.02, -8.01, 1, 1, 62, 16.56, -4.23, 1, 1, 62, 9.62, -3.76, 1, 1, 62, 2.92, -3.3, 1, 2, 61, 21.37, -2.33, 0.03259, 62, -0.14, -3.85, 0.96741, 2, 61, 19.96, -5.57, 0.29779, 62, -3.62, -4.48, 0.70221, 2, 61, 18.93, -7.94, 0.431, 62, -6.16, -4.94, 0.569, 2, 61, 17.4, -7.76, 0.49862, 62, -6.86, -3.57, 0.50138, 2, 61, 10.24, -6.93, 0.97135, 62, -10.15, 2.84, 0.02865, 1, 61, 5.15, -6.34, 1], "hull": 23, "edges": [22, 24, 24, 26, 26, 28, 2, 0, 0, 44, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 28, 30, 30, 32, 36, 38, 38, 40, 32, 34, 34, 36, 40, 42, 42, 44], "width": 36, "height": 41}}, "tui13": {"tui13": {"type": "mesh", "uvs": [0.3054, 0.03814, 0.36649, 0.03838, 0.5332, 0.03904, 0.75388, 0.03991, 0.95674, 0.04071, 0.95729, 0.11196, 0.95798, 0.19909, 0.79389, 0.25043, 0.65068, 0.29525, 0.61016, 0.31849, 0.57828, 0.37122, 0.48206, 0.53041, 0.37429, 0.70869, 0.23964, 0.93143, 0.01239, 0.98163, 0.17135, 0.76268, 0.2289, 0.60611, 0.28229, 0.46083, 0.28222, 0.35405, 0.28214, 0.21797, 0.28207, 0.11668], "triangles": [14, 15, 13, 13, 15, 12, 15, 16, 12, 12, 16, 11, 16, 17, 11, 10, 11, 18, 11, 17, 18, 9, 10, 19, 10, 18, 19, 9, 19, 2, 2, 19, 1, 0, 1, 20, 9, 2, 8, 8, 3, 7, 8, 2, 3, 7, 5, 6, 5, 3, 4, 5, 7, 3, 1, 19, 20], "vertices": [2, 59, 17.09, -7.98, 0.93818, 60, -10.08, -5.35, 0.06182, 2, 59, 14.3, -7.63, 0.97258, 60, -10.42, -2.56, 0.02742, 1, 59, 6.69, -6.68, 1, 1, 59, -3.38, -5.43, 1, 1, 59, -12.64, -4.28, 1, 1, 59, -12.24, -0.74, 1, 1, 59, -11.75, 3.59, 1, 2, 59, -3.95, 5.24, 0.99895, 60, -2.39, 18.29, 0.00105, 2, 59, 2.85, 6.68, 0.85486, 60, 0.66, 12.04, 0.14514, 2, 59, 4.84, 7.61, 0.65843, 60, 2.05, 10.33, 0.34157, 2, 59, 6.61, 10.06, 0.35087, 60, 4.85, 9.21, 0.64913, 2, 59, 11.96, 17.43, 0.00686, 60, 13.31, 5.83, 0.99314, 1, 60, 22.78, 2.04, 1, 1, 60, 34.61, -2.69, 1, 1, 60, 38.43, -12.74, 1, 1, 60, 26.64, -6.88, 1, 1, 60, 18.54, -5.24, 1, 1, 60, 11.02, -3.73, 1, 1, 60, 5.73, -4.4, 1, 2, 59, 19.23, 0.82, 0.23832, 60, -1.02, -5.27, 0.76168, 2, 59, 18.62, -4.2, 0.78272, 60, -6.05, -5.91, 0.21728], "hull": 21, "edges": [0, 40, 16, 18, 26, 28, 28, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 24, 26, 38, 40, 34, 36, 36, 38, 30, 32, 32, 34], "width": 46, "height": 50}}, "tui14": {"tui14": {"type": "mesh", "uvs": [0.78802, 0.24824, 0.86148, 0.29291, 1, 0.37715, 1, 0.43142, 0.96472, 0.5436, 0.81698, 0.61203, 0.64981, 0.68946, 0.61509, 0.70554, 0.584, 0.71994, 0.53283, 0.74364, 0.47154, 0.77202, 0.37493, 0.81677, 0.21495, 0.89928, 0.0247, 0.99741, 0.01279, 0.89522, 0.10126, 0.73859, 0.23424, 0.50316, 0.35931, 0.28174, 0.41112, 0.19001, 0.46721, 0.0907, 0.5155, 0.00522, 0.57198, 0.04726, 0.63318, 0.09283], "triangles": [13, 14, 12, 14, 15, 12, 11, 12, 16, 12, 15, 16, 11, 16, 10, 9, 10, 17, 10, 16, 17, 9, 17, 8, 8, 17, 18, 18, 7, 8, 7, 18, 22, 21, 19, 20, 19, 22, 18, 7, 22, 6, 21, 22, 19, 6, 0, 5, 6, 22, 0, 5, 1, 4, 5, 0, 1, 4, 1, 3, 1, 2, 3], "vertices": [1, 57, 7.51, -4.44, 1, 1, 57, 3.01, -4.52, 1, 1, 57, -5.48, -4.67, 1, 1, 57, -6.25, -2.81, 1, 1, 57, -5.98, 1.79, 1, 2, 57, 0.83, 7.36, 0.94835, 58, -3.38, 20.67, 0.05165, 2, 57, 8.54, 13.65, 0.52348, 58, 5.38, 15.96, 0.47652, 2, 57, 10.14, 14.96, 0.40319, 58, 7.2, 14.98, 0.59681, 2, 57, 11.57, 16.13, 0.30285, 58, 8.83, 14.11, 0.69715, 2, 57, 13.93, 18.06, 0.16795, 58, 11.51, 12.66, 0.83205, 2, 57, 16.75, 20.37, 0.06542, 58, 14.72, 10.94, 0.93458, 2, 57, 21.2, 24.01, 0.00571, 58, 19.79, 8.21, 0.99429, 1, 58, 28.4, 3.92, 1, 1, 58, 38.63, -1.18, 1, 1, 58, 36.44, -4.33, 1, 1, 58, 28.77, -4.86, 1, 1, 58, 17.25, -5.66, 1, 2, 57, 29.61, 6.06, 0.02852, 58, 6.42, -6.41, 0.97148, 2, 57, 28.18, 1.79, 0.29923, 58, 1.93, -6.73, 0.70077, 2, 57, 26.64, -2.83, 0.84823, 58, -2.93, -7.06, 0.15177, 2, 57, 25.3, -6.8, 0.9972, 58, -7.11, -7.35, 0.0028, 1, 57, 21.73, -6.6, 1, 1, 57, 17.87, -6.38, 1], "hull": 23, "edges": [0, 44, 4, 6, 6, 8, 26, 28, 16, 18, 12, 14, 14, 16, 8, 10, 10, 12, 0, 2, 2, 4, 40, 42, 42, 44, 38, 40, 36, 38, 22, 24, 24, 26, 28, 30, 30, 32, 18, 20, 20, 22, 32, 34, 34, 36], "width": 57, "height": 37}}, "tui15": {"tui15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [403.65, 215.4, 475.71, 21.35, -52.08, -174.63, -124.14, 19.42], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 207, "height": 563}}, "tui16": {"tui16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.65, -24.31, -30.72, 60.6, 251.98, 47.53, 248.05, -37.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 85, "height": 283}}, "tui17": {"tui17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [480.2, -36.94, 317.87, -271.19, -154.74, 56.31, 7.59, 290.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 285, "height": 575}}, "zhizhuwang": {"zhizhuwang": {"type": "mesh", "uvs": [0.94717, 0, 0.98184, 0.04348, 0.9842, 0.07732, 0.95013, 0.09352, 0.95526, 0.15087, 0.99947, 0.17879, 0.9995, 0.26267, 0.99735, 0.26578, 0.94923, 0.16137, 0.94972, 0.09555, 0.93567, 0.07248, 0.9054, 0.0462, 0.88402, 0.03641, 0.68724, 0.03775, 0.68696, 0.05696, 0.67067, 0.09501, 0.65721, 0.1122, 0.67871, 0.16164, 0.69754, 0.17464, 0.74278, 0.17845, 0.74037, 0.179, 0.70153, 0.1787, 0.6779, 0.24751, 0.43004, 0.99664, 0.4399, 0.99935, 0.21735, 0.99937, 0.19044, 0.99198, 0.18057, 0.98044, 0.16784, 0.96556, 0.15202, 0.94707, 0.15995, 0.93899, 0.17239, 0.9263, 0.177, 0.91374, 0.15332, 0.89449, 0.13039, 0.87586, 0.10471, 0.85498, 0.08384, 0.83802, 0.07982, 0.8258, 0.10511, 0.8437, 0.14279, 0.87036, 0.16695, 0.88745, 0.20673, 0.91046, 0.22803, 0.92083, 0.24726, 0.9302, 0.26472, 0.9387, 0.28979, 0.95091, 0.30796, 0.95857, 0.35128, 0.97201, 0.39244, 0.98478, 0.42966, 0.99633, 0.66993, 0.27136, 0.69505, 0.19302, 0.65416, 0.1324, 0.64193, 0.03168, 0.84791, 0.03649, 0.90587, 0.03191, 0.94658, 0], "triangles": [55, 12, 54, 13, 53, 54, 13, 54, 12, 11, 12, 55, 14, 53, 13, 10, 55, 56, 11, 55, 10, 56, 0, 1, 10, 56, 1, 10, 1, 2, 3, 10, 2, 15, 53, 14, 9, 10, 3, 16, 53, 15, 52, 53, 16, 3, 4, 9, 8, 9, 4, 52, 16, 17, 20, 21, 18, 8, 4, 5, 19, 20, 18, 51, 17, 18, 51, 18, 21, 52, 17, 51, 22, 51, 21, 6, 7, 8, 6, 8, 5, 22, 50, 51, 38, 36, 37, 35, 36, 38, 35, 38, 39, 34, 35, 39, 33, 39, 40, 34, 39, 33, 32, 40, 41, 33, 40, 32, 28, 30, 31, 29, 30, 28, 41, 31, 32, 42, 31, 41, 42, 27, 31, 28, 31, 27, 25, 26, 27, 22, 23, 50, 23, 49, 50, 42, 25, 27, 25, 42, 43, 25, 43, 44, 25, 44, 45, 25, 45, 46, 25, 46, 47, 25, 47, 48, 23, 25, 48, 23, 48, 49, 25, 23, 24], "vertices": [-73.46, 440.08, -10.24, 493.94, 40.01, 498.89, 65.51, 448.4, 150.61, 458.47, 190.29, 526, 315.06, 529.51, 319.77, 526.42, 166.47, 449.85, 68.56, 447.87, 34.83, 425.82, -3, 379.29, -16.67, 346.79, -6.47, 51.38, 22.11, 51.76, 79.4, 28.88, 105.51, 9.37, 178.17, 43.7, 196.71, 72.5, 200.49, 140.58, 201.41, 137, 202.58, 78.66, 305.93, 46.03, 1430.54, -295.16, 1434.15, -280.24, 1443.46, -614.39, 1433.59, -655.1, 1416.84, -670.4, 1395.24, -690.12, 1368.4, -714.63, 1356.05, -703.07, 1336.65, -684.9, 1317.77, -678.51, 1290.14, -714.86, 1263.38, -750.05, 1233.4, -789.48, 1209.04, -821.51, 1191.03, -828.04, 1216.6, -789.33, 1254.68, -731.66, 1279.1, -694.68, 1311.66, -634, 1326.2, -601.6, 1339.33, -572.34, 1351.25, -545.77, 1368.36, -507.63, 1379, -480.03, 1397.19, -414.43, 1414.46, -352.11, 1430.09, -295.74, 341.73, 35.05, 224.15, 69.53, 135.69, 5.63, -13.62, -16.9, -15.05, 292.57, -24.28, 379.41, -73.44, 439.2], "hull": 57, "edges": [0, 112, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 62, 64, 72, 74, 80, 82, 90, 92, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 96, 98, 92, 94, 94, 96, 78, 80, 74, 76, 76, 78, 70, 72, 68, 70, 64, 66, 66, 68, 58, 60, 60, 62, 56, 58, 82, 84, 84, 86, 52, 54, 54, 56, 86, 88, 88, 90], "width": 378, "height": 374}}, "tui19": {"tui19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [130.26, -43.88, -33.29, -121.43, -121.97, 65.61, 41.57, 143.16], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 181, "height": 207}}, "tui1": {"tui1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [163.83, 12.82, 161.23, -24.09, -33.29, -10.39, -30.69, 26.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 37, "height": 195}}, "yifu1": {"yifu1": {"type": "mesh", "uvs": [0.08188, 0.01613, 0.17374, 0.10185, 0.27488, 0.19624, 0.3678, 0.23236, 0.49625, 0.27447, 0.61062, 0.31196, 0.73974, 0.37813, 0.84224, 0.43065, 0.95588, 0.53683, 0.96508, 0.66993, 0.97254, 0.77799, 0.95419, 0.92209, 0.92792, 1, 0.9037, 1, 0.85324, 0.92036, 0.80704, 0.84743, 0.7634, 0.77855, 0.69599, 0.67215, 0.61123, 0.56622, 0.55858, 0.5054, 0.49648, 0.43368, 0.42771, 0.37084, 0.26745, 0.25949, 0.12853, 0.16297, 0.01043, 0.0809, 0, 0.04335, 0, 0.02236, 0.03757, 0.00105], "triangles": [17, 8, 9, 16, 17, 9, 16, 9, 10, 15, 16, 10, 14, 15, 10, 11, 14, 10, 13, 14, 11, 12, 13, 11, 17, 7, 8, 18, 19, 6, 7, 17, 18, 7, 18, 6, 21, 3, 4, 20, 4, 5, 21, 4, 20, 19, 20, 5, 19, 5, 6, 25, 26, 27, 0, 24, 25, 0, 25, 27, 1, 24, 0, 23, 24, 1, 23, 1, 2, 22, 23, 2, 22, 2, 3, 22, 3, 21], "vertices": [1, 99, -3.81, 9.51, 1, 1, 99, 16.04, 6.18, 1, 1, 99, 37.9, 2.51, 1, 1, 99, 53.11, 5.98, 1, 2, 99, 73.38, 11.84, 0.09623, 100, 5.02, 11.84, 0.90377, 2, 100, 23.07, 17.05, 0.99208, 101, -19.66, 17.05, 0.00792, 3, 100, 45.74, 19.71, 0.35315, 101, 3.01, 19.71, 0.62252, 102, -22.63, 19.71, 0.02433, 4, 100, 63.74, 21.83, 0.00852, 101, 21.01, 21.83, 0.62943, 102, -4.63, 21.83, 0.36039, 103, -21.72, 21.83, 0.00166, 3, 101, 45.58, 17.69, 0.01402, 102, 19.94, 17.69, 0.53026, 103, 2.85, 17.69, 0.45573, 1, 103, 16.83, 0.52, 1, 2, 102, 45.27, -13.42, 0.00091, 103, 28.18, -13.42, 0.99909, 1, 103, 39.73, -34.56, 1, 1, 103, 43.92, -47.46, 1, 1, 103, 40.86, -49.63, 1, 2, 102, 43.91, -43.4, 0.01319, 103, 26.82, -43.4, 0.98681, 3, 101, 56.7, -37.69, 0.00056, 102, 31.06, -37.69, 0.09387, 103, 13.97, -37.69, 0.90558, 3, 101, 44.55, -32.3, 0.02809, 102, 18.92, -32.3, 0.28197, 103, 1.82, -32.3, 0.68994, 3, 101, 25.8, -23.98, 0.3922, 102, 0.16, -23.98, 0.45457, 103, -16.93, -23.98, 0.15323, 4, 100, 47.63, -17.27, 0.20903, 101, 4.9, -17.27, 0.76742, 102, -20.74, -17.27, 0.02345, 103, -37.83, -17.27, 0.0001, 2, 100, 35.12, -13.78, 0.83447, 101, -7.61, -13.78, 0.16553, 1, 100, 20.37, -9.67, 1, 2, 99, 74.01, -7.36, 0.04266, 100, 5.64, -7.36, 0.95734, 1, 99, 43.05, -6.71, 1, 1, 99, 16.22, -6.15, 1, 1, 99, -6.59, -5.67, 1, 1, 99, -11.53, -1.53, 1, 1, 99, -13.55, 1.31, 1, 1, 99, -10.86, 7.57, 1], "hull": 28, "edges": [0, 54, 4, 6, 14, 16, 20, 22, 22, 24, 24, 26, 34, 36, 40, 42, 48, 50, 50, 52, 52, 54, 16, 18, 18, 20, 10, 12, 12, 14, 6, 8, 8, 10, 42, 44, 44, 46, 46, 48, 0, 2, 2, 4, 30, 32, 32, 34, 36, 38, 38, 40, 26, 28, 28, 30], "width": 155, "height": 166}}, "yifu2": {"yifu2": {"type": "mesh", "uvs": [0.42638, 0.00636, 0.47032, 0.01545, 0.49492, 0.05464, 0.4841, 0.11981, 0.4724, 0.19023, 0.46619, 0.22257, 0.46714, 0.29185, 0.4679, 0.34761, 0.46908, 0.43356, 0.4698, 0.48618, 0.47224, 0.50818, 0.47541, 0.572, 0.47756, 0.61522, 0.50712, 0.67747, 0.52385, 0.71268, 0.53078, 0.72755, 0.54951, 0.75663, 0.57081, 0.7897, 0.59526, 0.82767, 0.60943, 0.84967, 0.61738, 0.8567, 0.64289, 0.89145, 0.68757, 0.93344, 0.7389, 0.96724, 0.78493, 0.99754, 0.73883, 0.99154, 0.70515, 0.98102, 0.66555, 0.96866, 0.63156, 0.94968, 0.61158, 0.96457, 0.56065, 0.94174, 0.52304, 0.92487, 0.48978, 0.91692, 0.43409, 0.91077, 0.37168, 0.90389, 0.35332, 0.87783, 0.33337, 0.84951, 0.3126, 0.82003, 0.29556, 0.79586, 0.27709, 0.77318, 0.24848, 0.73947, 0.21599, 0.70118, 0.18556, 0.65011, 0.15612, 0.60071, 0.12446, 0.54758, 0.08366, 0.47912, 0.05535, 0.38734, 0.04019, 0.33819, 0.02171, 0.27827, 0.02378, 0.23142, 0.02515, 0.20027, 0.01791, 0.17199, 0.00383, 0.11126, 0.00729, 0.09739, 0.02554, 0.07433, 0.20089, 0.03384, 0.36481, 0.00895, 0.3485, 0.00243], "triangles": [27, 22, 23, 26, 27, 23, 25, 26, 23, 25, 23, 24, 22, 28, 21, 27, 28, 22, 29, 30, 28, 33, 18, 32, 20, 31, 32, 33, 34, 35, 20, 32, 19, 21, 31, 20, 30, 31, 21, 28, 30, 21, 36, 37, 17, 35, 36, 18, 39, 40, 14, 38, 39, 15, 37, 38, 16, 37, 16, 17, 36, 17, 18, 33, 35, 18, 19, 32, 18, 40, 13, 14, 39, 14, 15, 38, 15, 16, 41, 42, 12, 40, 41, 13, 44, 45, 9, 43, 44, 11, 42, 43, 12, 45, 8, 9, 44, 9, 10, 44, 10, 11, 43, 11, 12, 41, 12, 13, 3, 4, 0, 4, 5, 54, 49, 5, 6, 47, 6, 7, 46, 7, 8, 5, 50, 51, 5, 49, 50, 6, 48, 49, 47, 48, 6, 46, 47, 7, 45, 46, 8, 56, 57, 0, 54, 52, 53, 51, 52, 54, 0, 4, 56, 4, 55, 56, 4, 54, 55, 51, 54, 5, 2, 0, 1, 2, 3, 0], "vertices": [1, 8, 156.26, 14.5, 1, 1, 8, 169.06, 3.7, 1, 1, 8, 168.83, -23.06, 1, 2, 8, 151.06, -61.34, 0.24, 262, 42.23, 17.82, 0.76, 1, 262, 87.72, 14.5, 1, 1, 262, 108.61, 12.67, 1, 1, 262, 153.28, 13.85, 1, 1, 262, 189.24, 14.8, 1, 2, 262, 244.66, 16.26, 0.13709, 263, 9.65, 15.51, 0.86291, 1, 263, 43.53, 13.56, 1, 1, 263, 57.75, 13.51, 1, 1, 263, 98.9, 11.97, 1, 1, 263, 126.77, 10.92, 1, 1, 264, 25.55, 12.9, 1, 1, 264, 49, 11.51, 1, 1, 264, 58.88, 10.88, 1, 2, 264, 78.79, 11.4, 0.94507, 265, -10.06, 12.76, 0.05493, 2, 264, 101.42, 11.99, 0.04231, 265, 12.47, 10.51, 0.95769, 1, 265, 38.35, 7.92, 1, 2, 265, 53.33, 6.43, 0.6594, 266, -2.14, 6.47, 0.3406, 2, 265, 58.65, 7.05, 0.27451, 266, 3.19, 6.99, 0.72549, 1, 266, 27.31, 5.13, 1, 2, 267, 24.44, 5.28, 0.06961, 268, 4.11, 4.88, 0.93039, 2, 268, 32.56, 6.23, 0, 269, 19.11, 4.65, 1, 2, 268, 58.06, 7.45, 0, 269, 44.63, 3.73, 1, 2, 268, 45.01, -3.29, 0, 269, 30.72, -5.88, 1, 1, 269, 17.63, -10.25, 1, 4, 261, 94.35, 9.71, 0.00204, 267, 39.75, -13.24, 0.02874, 268, 17.41, -15.15, 0.35811, 269, 2.23, -15.39, 0.6111, 5, 266, 59.1, -15.26, 0.04411, 261, 77.25, 11.94, 0.04774, 267, 22.99, -17.25, 0.48297, 268, 0.32, -17.37, 0.41061, 269, -14.99, -16.18, 0.01456, 4, 266, 64.51, -25.93, 0.08599, 261, 77.51, -0.02, 0.08366, 267, 27.48, -28.33, 0.54579, 268, 3.63, -28.87, 0.28456, 4, 266, 43.22, -35.62, 0.26606, 261, 54.11, 0.47, 0.2323, 267, 5.45, -36.21, 0.41467, 268, -19.1, -34.4, 0.08698, 5, 265, 83.99, -42.2, 0.00344, 266, 27.49, -42.78, 0.30985, 261, 36.84, 0.82, 0.50109, 267, -10.82, -42.02, 0.17093, 268, -35.9, -38.48, 0.0147, 5, 265, 74.27, -50.73, 0.00788, 266, 17.6, -51.11, 0.16863, 261, 24.31, -2.4, 0.76484, 267, -21.38, -49.5, 0.05696, 268, -47.17, -44.81, 0.00169, 3, 259, 36.28, -11.47, 0.04438, 260, 18.22, -11.47, 0.25672, 261, 6.18, -11.47, 0.6989, 3, 259, 15.95, -21.62, 0.63821, 260, -2.11, -21.62, 0.26146, 261, -14.15, -21.62, 0.10033, 3, 258, 36.69, -10.47, 0.27239, 259, 0.47, -12.37, 0.72201, 261, -29.63, -12.37, 0.0056, 1, 258, 17.09, -10.18, 1, 2, 257, 44.84, -9.86, 0.7247, 258, -3.32, -9.86, 0.2753, 1, 257, 28.11, -9.61, 1, 2, 256, 90.34, -10.2, 0.00093, 257, 12.07, -10.2, 0.99907, 2, 256, 66.34, -11.45, 0.98806, 257, -11.92, -11.45, 0.01194, 1, 256, 39.1, -12.87, 1, 2, 255, 131.77, -10.08, 0.19127, 256, 4.49, -10.49, 0.80873, 1, 255, 98.22, -10.6, 1, 1, 255, 62.15, -11.15, 1, 1, 255, 15.65, -11.86, 1, 1, 254, 162.58, -12.86, 1, 1, 254, 130.5, -15.28, 1, 1, 254, 91.41, -18.23, 1, 1, 254, 61.39, -14.67, 1, 1, 254, 41.44, -12.29, 1, 2, 8, -17, -37.06, 0.36, 254, 23.03, -13.16, 0.64, 1, 8, -8.55, 1.52, 1, 1, 8, -4.38, 9.53, 1, 1, 8, 6.76, 21.34, 1, 1, 8, 74.49, 24.89, 1, 1, 8, 135, 20.32, 1, 1, 8, 130.93, 26.24, 1], "hull": 58, "edges": [0, 114, 0, 2, 2, 4, 8, 10, 18, 20, 28, 30, 38, 40, 40, 42, 42, 44, 48, 50, 54, 56, 56, 58, 62, 64, 76, 78, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 90, 92, 4, 6, 6, 8, 96, 98, 98, 100, 10, 12, 12, 14, 14, 16, 16, 18, 86, 88, 88, 90, 24, 26, 26, 28, 64, 66, 66, 68, 58, 60, 60, 62, 44, 46, 46, 48, 34, 36, 36, 38, 68, 70, 70, 72, 20, 22, 22, 24, 82, 84, 84, 86, 92, 94, 94, 96, 50, 52, 52, 54, 30, 32, 32, 34, 72, 74, 74, 76, 78, 80, 80, 82], "width": 357, "height": 645}}, "temang2": {"temang2": {"type": "mesh", "uvs": [0.25764, 0.17525, 0.24307, 0.4551, 0.2702, 0.5241, 0.29181, 0.57905, 0.33132, 0.5234, 0.376, 0.46047, 0.39985, 0.4269, 0.46355, 0.4223, 0.52212, 0.41808, 0.5766, 0.41415, 0.63521, 0.40992, 0.71664, 0.45394, 0.77383, 0.48487, 0.84515, 0.52343, 0.86932, 0.53651, 0.95465, 0.58265, 1, 0.60717, 1, 0.6423, 0.98709, 0.72375, 0.95025, 0.82435, 0.92671, 0.88863, 0.86167, 0.88928, 0.82724, 0.88962, 0.79605, 0.83956, 0.73355, 0.73923, 0.68098, 0.7072, 0.62976, 0.70693, 0.57721, 0.70665, 0.5182, 0.73072, 0.44752, 0.8218, 0.39609, 0.88807, 0.34261, 0.95698, 0.30922, 1, 0.2531, 1, 0.23117, 0.95984, 0.15598, 0.82217, 0.06469, 0.40942, 0.04192, 0.43382, 0.00044, 0.40158, 0.07438, 0.00714, 0.21138, 0.00756], "triangles": [15, 16, 17, 18, 15, 17, 19, 14, 15, 19, 15, 18, 20, 21, 19, 23, 12, 13, 19, 21, 14, 14, 23, 13, 21, 23, 14, 22, 23, 21, 26, 27, 9, 10, 26, 9, 25, 26, 10, 11, 25, 10, 24, 11, 12, 25, 11, 24, 24, 12, 23, 8, 9, 27, 28, 7, 8, 28, 8, 27, 29, 6, 7, 29, 7, 28, 5, 6, 29, 4, 5, 29, 30, 4, 29, 3, 4, 30, 31, 3, 30, 3, 31, 32, 36, 38, 39, 37, 38, 36, 0, 39, 40, 39, 1, 36, 0, 1, 39, 35, 1, 2, 35, 36, 1, 3, 34, 35, 3, 35, 2, 32, 34, 3, 32, 33, 34], "vertices": [1, 217, 30.3, 35.32, 1, 2, 217, 59, 14.26, 0.9821, 218, -4.88, 40.57, 0.0179, 2, 217, 70.98, 18.15, 0.79671, 218, 1.77, 29.87, 0.20329, 2, 217, 80.52, 21.24, 0.36348, 218, 7.07, 21.35, 0.63652, 2, 217, 80.66, 36.26, 0.02315, 218, 21.67, 24.82, 0.97685, 1, 218, 38.19, 28.74, 1, 1, 218, 47.01, 30.84, 1, 1, 218, 67.88, 26.05, 1, 2, 218, 87.07, 21.64, 0.99739, 219, -24.81, 12.64, 0.00261, 2, 218, 104.91, 17.55, 0.61628, 219, -6.85, 16.16, 0.38372, 2, 218, 124.12, 13.14, 0.01565, 219, 12.49, 19.94, 0.98435, 1, 219, 40.39, 19, 1, 3, 219, 59.99, 18.33, 0.73681, 220, -8.65, 16.4, 0.26312, 221, -27.5, 45.4, 6e-05, 3, 219, 84.42, 17.5, 0.01811, 220, 14.7, 23.66, 0.88164, 221, -5.08, 35.64, 0.10025, 2, 220, 22.62, 26.11, 0.76241, 221, 2.52, 32.33, 0.23759, 2, 220, 50.55, 34.79, 0.08219, 221, 29.33, 20.64, 0.91781, 2, 220, 65.4, 39.41, 0.00063, 221, 43.59, 14.43, 0.99937, 1, 221, 42.66, 10.1, 1, 1, 221, 36.27, 0.98, 1, 1, 221, 21.52, -8.83, 1, 1, 221, 12.09, -15.09, 1, 2, 220, 41.72, -14.1, 0.82074, 221, -9.3, -10.6, 0.17926, 3, 219, 86.12, -28.99, 4e-05, 220, 31.6, -19.7, 0.99813, 221, -20.62, -8.22, 0.00183, 2, 219, 74.74, -24.51, 0.03577, 220, 19.38, -19.2, 0.96423, 2, 219, 51.94, -15.51, 0.81941, 220, -5.11, -18.21, 0.18059, 1, 219, 33.85, -14.46, 1, 2, 218, 112.99, -22.64, 0.08912, 219, 16.88, -17.27, 0.91088, 2, 218, 95.9, -18.19, 0.71001, 219, -0.55, -20.16, 0.28999, 2, 218, 75.94, -16.17, 0.99841, 219, -19.6, -26.43, 0.00159, 1, 218, 50.08, -21.34, 1, 2, 217, 131.43, 33.56, 0.02571, 218, 31.26, -25.11, 0.97429, 2, 217, 130.53, 13.63, 0.28646, 218, 11.69, -29.02, 0.71354, 2, 217, 129.97, 1.18, 0.58399, 218, -0.53, -31.47, 0.41601, 2, 217, 121, -15.41, 0.94392, 218, -18.79, -26.75, 0.05608, 2, 217, 113.05, -19.49, 0.99443, 218, -24.66, -20.01, 0.00557, 1, 217, 85.78, -33.47, 1, 1, 217, 25.45, -35.74, 1, 1, 217, 24.51, -43.93, 1, 1, 217, 14.31, -54.26, 1, 1, 217, -17.6, -8.78, 1, 1, 217, 4.32, 31.69, 1], "hull": 41, "edges": [0, 80, 0, 2, 32, 34, 34, 36, 48, 50, 54, 56, 64, 66, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 56, 58, 62, 64, 66, 68, 68, 70, 6, 8, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 44, 46, 46, 48, 40, 42, 42, 44, 36, 38, 38, 40, 28, 30, 30, 32, 24, 26, 26, 28, 50, 52, 52, 54, 58, 60, 60, 62], "width": 336, "height": 126}}, "youxiaobi": {"youxiaobi": {"type": "mesh", "uvs": [0.58123, 0.01572, 0.60977, 0.0633, 0.59182, 0.10973, 0.56113, 0.14889, 0.51929, 0.20226, 0.48617, 0.25055, 0.47441, 0.2677, 0.50718, 0.28894, 0.57439, 0.33249, 0.68767, 0.4059, 0.75677, 0.48308, 0.82484, 0.55912, 0.86661, 0.63206, 0.91116, 0.70985, 0.96023, 0.79554, 0.96851, 0.86184, 0.97466, 0.91102, 0.97996, 0.95342, 0.96182, 0.97238, 0.90395, 0.96545, 0.80841, 0.95401, 0.73846, 0.93114, 0.67017, 0.90881, 0.62055, 0.89817, 0.50659, 0.88389, 0.41697, 0.86089, 0.3627, 0.84696, 0.28457, 0.82692, 0.21221, 0.80835, 0.16307, 0.79574, 0.12895, 0.75818, 0.09169, 0.71717, 0.06425, 0.65332, 0.04333, 0.60464, 0.00369, 0.51239, 0.00364, 0.43538, 0.00361, 0.36414, 0.05997, 0.33802, 0.11437, 0.30939, 0.17196, 0.27908, 0.19587, 0.25791, 0.23065, 0.20356, 0.27733, 0.21, 0.32273, 0.21626, 0.34434, 0.19444, 0.40499, 0.12839, 0.4572, 0.07154, 0.51676, 0.00668, 0.56344, 0.00642, 0.34515, 0.23131, 0.39834, 0.24783, 0.44508, 0.26664], "triangles": [18, 19, 17, 17, 19, 16, 16, 19, 15, 15, 19, 20, 15, 20, 21, 15, 21, 14, 14, 21, 22, 14, 22, 13, 13, 22, 12, 12, 22, 23, 12, 23, 24, 12, 24, 11, 11, 24, 10, 24, 25, 10, 25, 9, 10, 26, 9, 25, 27, 8, 26, 31, 7, 27, 28, 31, 27, 51, 31, 39, 28, 29, 30, 28, 30, 31, 39, 31, 32, 43, 49, 42, 42, 49, 40, 42, 40, 41, 49, 39, 40, 33, 38, 39, 35, 37, 38, 38, 34, 35, 35, 36, 37, 9, 26, 8, 7, 8, 27, 7, 51, 6, 31, 51, 7, 49, 50, 39, 51, 39, 50, 39, 32, 33, 38, 33, 34, 6, 51, 5, 51, 50, 5, 5, 50, 4, 45, 4, 50, 43, 44, 49, 45, 50, 44, 44, 50, 49, 4, 45, 3, 45, 46, 3, 3, 46, 2, 1, 2, 47, 0, 47, 48, 46, 47, 2, 1, 47, 0], "vertices": [1, 27, 214.23, 3.87, 1, 1, 27, 208.42, -10.79, 1, 1, 27, 195.1, -16.26, 1, 1, 27, 181.27, -17.88, 1, 4, 27, 162.43, -20.08, 0.99836, 167, 53.1, 170.69, 0.00031, 168, -36.13, 170.69, 0.00093, 169, -91.04, 170.69, 0.0004, 4, 27, 146.17, -22.99, 0.97734, 167, 49.51, 154.56, 0.00588, 168, -39.71, 154.56, 0.01172, 169, -94.62, 154.56, 0.00506, 4, 27, 140.39, -24.02, 0.94763, 167, 48.24, 148.84, 0.01422, 168, -40.99, 148.84, 0.0266, 169, -95.9, 148.84, 0.01155, 4, 27, 141.14, -34.43, 0.86965, 167, 58.13, 145.52, 0.03512, 168, -31.1, 145.52, 0.06589, 169, -86.01, 145.52, 0.02934, 5, 27, 142.68, -55.77, 0.72214, 167, 78.41, 138.71, 0.0675, 168, -10.81, 138.71, 0.14191, 169, -65.72, 138.71, 0.06818, 170, -100.04, 138.71, 0.00027, 5, 27, 145.27, -91.73, 0.48937, 167, 112.6, 127.24, 0.08618, 168, 23.37, 127.24, 0.26334, 169, -31.54, 127.24, 0.15596, 170, -65.86, 127.24, 0.00515, 5, 27, 139.65, -119.9, 0.32852, 167, 136.43, 111.2, 0.06993, 168, 47.2, 111.2, 0.32799, 169, -7.71, 111.2, 0.2535, 170, -42.03, 111.2, 0.02006, 7, 27, 134.11, -147.66, 0.19658, 167, 159.91, 95.39, 0.03773, 168, 70.68, 95.39, 0.32898, 169, 15.77, 95.39, 0.37572, 170, -18.55, 95.39, 0.05661, 171, -39.14, 95.39, 0.00192, 172, -52.87, 95.39, 0.00246, 7, 27, 124.87, -169.75, 0.11269, 167, 176.73, 78.35, 0.01427, 168, 87.5, 78.35, 0.25733, 169, 32.59, 78.35, 0.46194, 170, -1.73, 78.35, 0.11892, 171, -22.32, 78.35, 0.01587, 172, -36.05, 78.35, 0.01897, 7, 27, 115.01, -193.31, 0.04702, 167, 194.67, 60.18, 0.00138, 168, 105.44, 60.18, 0.1233, 169, 50.53, 60.18, 0.43902, 170, 16.21, 60.18, 0.22621, 171, -4.38, 60.18, 0.0739, 172, -18.11, 60.18, 0.08917, 6, 27, 104.16, -219.26, 0.01108, 168, 125.2, 40.16, 0.02066, 169, 70.29, 40.16, 0.20254, 170, 35.97, 40.16, 0.23292, 171, 15.38, 40.16, 0.20796, 172, 1.66, 40.16, 0.32484, 6, 27, 90.8, -233.61, 0.00211, 168, 133.3, 22.3, 0.00124, 169, 78.39, 22.3, 0.05025, 170, 44.07, 22.3, 0.07482, 171, 23.48, 22.3, 0.20592, 172, 9.76, 22.3, 0.66565, 5, 27, 80.9, -244.26, 0.00014, 169, 84.4, 9.06, 0.00402, 170, 50.08, 9.06, 0.00086, 171, 29.49, 9.06, 0.02784, 172, 15.77, 9.06, 0.96714, 1, 172, 20.95, -2.36, 1, 1, 172, 18.3, -9.1, 1, 3, 170, 37.96, -11.78, 0.01701, 171, 17.37, -11.78, 0.30467, 172, 3.64, -11.78, 0.67832, 3, 169, 48.08, -16.2, 0.03967, 170, 13.77, -16.2, 0.74249, 171, -6.83, -16.2, 0.21784, 3, 169, 29.04, -15.39, 0.76739, 170, -5.28, -15.39, 0.23148, 171, -25.87, -15.39, 0.00114, 2, 168, 65.35, -14.6, 0.07236, 169, 10.44, -14.6, 0.92764, 2, 168, 52.36, -15.58, 0.65156, 169, -2.55, -15.58, 0.34844, 2, 167, 112.67, -20.68, 0.0055, 168, 23.44, -20.68, 0.9945, 2, 167, 88.85, -21.4, 0.5498, 168, -0.38, -21.4, 0.4502, 2, 167, 74.42, -21.84, 0.94447, 168, -14.8, -21.84, 0.05553, 1, 167, 53.66, -22.47, 1, 1, 167, 34.42, -23.05, 1, 1, 167, 21.36, -23.45, 1, 1, 167, 9.64, -15.68, 1, 2, 27, -23.39, -36.74, 0.00257, 167, -3.15, -7.2, 0.99743, 2, 27, -13.78, -19.16, 0.37833, 167, -15.67, 8.45, 0.62167, 2, 27, -6.45, -5.76, 0.84816, 167, -25.21, 20.38, 0.15184, 2, 27, 7.44, 19.64, 0.86664, 163, -27.48, -27.39, 0.13336, 2, 27, 24.55, 34.47, 0.36317, 163, -14.37, -8.93, 0.63683, 1, 163, -2.23, 8.14, 1, 1, 163, 13.93, 6.06, 1, 2, 163, 30.12, 4.88, 0.99213, 164, -4.08, 4.88, 0.00787, 1, 164, 13.05, 3.63, 1, 3, 164, 21.63, 5.17, 0.23843, 165, 1.11, 5.17, 0.75217, 166, -12.57, 5.17, 0.0094, 2, 165, 17.6, 13.04, 0.12589, 166, 3.92, 13.04, 0.87411, 2, 27, 120.32, 25.07, 0.04554, 166, 12.52, 4.6, 0.95446, 2, 27, 126.51, 15.12, 0.58806, 166, 20.89, -3.61, 0.41194, 2, 27, 134.97, 15.15, 0.87524, 166, 29.1, -1.58, 0.12476, 2, 27, 159.77, 16.17, 0.99985, 166, 52.96, 5.28, 0.00015, 1, 27, 181.12, 17.04, 1, 1, 27, 205.48, 18.04, 1, 1, 27, 213.33, 9.09, 1, 2, 27, 126.91, 7.9, 0.81329, 166, 22.98, -10.53, 0.18671, 4, 27, 132.11, -5.54, 0.99364, 167, 27.99, 148.32, 0.00173, 168, -61.24, 148.32, 0.00326, 169, -116.15, 148.32, 0.00137, 4, 27, 135.73, -18.16, 0.96363, 167, 41.04, 146.79, 0.01, 168, -48.19, 146.79, 0.01847, 169, -103.1, 146.79, 0.00791], "hull": 49, "edges": [0, 96, 0, 2, 2, 4, 34, 36, 44, 46, 46, 48, 72, 74, 78, 80, 80, 82, 86, 88, 94, 96, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 16, 18, 66, 68, 82, 84, 84, 86, 68, 70, 70, 72, 74, 76, 76, 78, 92, 94, 88, 90, 90, 92, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 50, 52, 52, 54, 58, 60, 60, 62, 54, 56, 56, 58, 62, 64, 64, 66], "width": 255, "height": 294}}, "yifu5": {"yifu5": {"type": "mesh", "uvs": [0.75424, 0.00331, 0.84195, 0.00357, 0.92846, 0.06143, 0.98709, 0.15668, 0.99386, 0.22332, 1, 0.28374, 1, 0.30346, 0.96712, 0.38266, 0.93661, 0.45615, 0.88207, 0.53025, 0.82256, 0.61111, 0.75667, 0.70065, 0.71205, 0.75169, 0.61808, 0.79367, 0.56837, 0.81588, 0.50387, 0.84469, 0.40282, 0.86065, 0.3434, 0.87004, 0.28376, 0.87946, 0.23531, 0.88711, 0.18881, 0.90489, 0.14993, 0.91976, 0.10264, 0.9433, 0.07677, 0.95617, 0.04561, 0.97168, 0.01508, 0.99796, 0.00363, 0.98756, 0.01423, 0.95233, 0.02477, 0.91729, 0.0531, 0.87187, 0.07491, 0.83691, 0.10234, 0.79294, 0.14675, 0.75273, 0.16745, 0.73398, 0.20903, 0.70412, 0.24843, 0.67583, 0.28591, 0.64892, 0.32499, 0.62085, 0.36326, 0.58421, 0.40117, 0.5479, 0.42509, 0.49361, 0.44657, 0.44485, 0.43592, 0.43359, 0.41133, 0.35328, 0.3856, 0.26924, 0.35982, 0.18505, 0.36135, 0.10451, 0.42281, 0.05977, 0.48148, 0.03848, 0.56608, 0.02122, 0.65531, 0.00302], "triangles": [25, 26, 24, 26, 27, 24, 24, 27, 23, 22, 23, 28, 23, 27, 28, 22, 28, 29, 21, 22, 29, 20, 21, 30, 21, 29, 30, 19, 20, 31, 30, 31, 20, 18, 19, 31, 18, 31, 32, 18, 32, 33, 17, 18, 34, 35, 16, 17, 34, 18, 33, 17, 34, 35, 15, 16, 36, 16, 35, 36, 14, 15, 37, 15, 36, 37, 37, 38, 13, 13, 38, 39, 12, 39, 11, 11, 39, 10, 39, 40, 10, 40, 41, 10, 4, 41, 42, 49, 42, 43, 48, 43, 44, 14, 37, 13, 12, 13, 39, 10, 41, 9, 9, 41, 8, 8, 41, 7, 5, 7, 41, 6, 7, 5, 5, 41, 4, 49, 4, 42, 49, 43, 48, 2, 0, 1, 50, 0, 4, 50, 4, 49, 45, 47, 48, 3, 0, 2, 4, 0, 3, 48, 44, 45, 47, 45, 46], "vertices": [2, 98, -14.73, -16.46, 0.99272, 191, -119.41, -23.95, 0.00728, 3, 98, -21.11, 0.35, 0.99936, 185, -78, -55.03, 0.00057, 191, -128.94, -8.69, 8e-05, 2, 98, -11.98, 22.87, 0.95084, 185, -70.38, -31.96, 0.04916, 2, 98, 9.22, 43.9, 0.69818, 185, -50.61, -9.58, 0.30182, 2, 98, 26.57, 52.06, 0.41157, 185, -33.83, -0.29, 0.58843, 2, 98, 42.31, 59.46, 0.11514, 185, -18.62, 8.13, 0.88486, 2, 98, 47.59, 61.49, 0.05474, 185, -13.49, 10.51, 0.94526, 1, 185, 9.98, 13.93, 1, 1, 185, 31.74, 17.1, 1, 2, 185, 55.74, 15.88, 0.44992, 186, 0.92, 15.88, 0.55008, 2, 186, 27.1, 14.55, 0.81516, 187, -6.63, 14.55, 0.18484, 3, 187, 22.36, 13.08, 0.42886, 188, 1.28, 13.08, 0.54152, 189, -11.38, 13.08, 0.02962, 3, 188, 18.41, 10.92, 0.1955, 189, 5.76, 10.92, 0.61151, 190, -2.67, 10.92, 0.193, 5, 98, 207.01, 38.92, 0.03932, 189, 24.78, -1.5, 0.00133, 190, 16.35, -1.5, 0.87433, 192, 12.92, 73.37, 0.05962, 193, -32.89, 73.37, 0.02539, 4, 98, 216.61, 31.69, 0.07936, 190, 26.41, -8.08, 0.69857, 192, 23.75, 68.14, 0.14898, 193, -22.07, 68.14, 0.07308, 5, 98, 229.08, 22.32, 0.08277, 190, 39.46, -16.61, 0.52448, 192, 37.79, 61.37, 0.23658, 193, -8.02, 61.37, 0.15529, 194, -36.66, 61.37, 0.00087, 5, 98, 240.79, 4.63, 0.0548, 190, 52.32, -33.49, 0.30399, 192, 52.71, 46.28, 0.27368, 193, 6.9, 46.28, 0.3458, 194, -21.74, 46.28, 0.02174, 5, 98, 247.68, -5.77, 0.0302, 190, 59.87, -43.42, 0.18246, 192, 61.49, 37.41, 0.20861, 193, 15.67, 37.41, 0.50397, 194, -12.97, 37.41, 0.07476, 5, 98, 254.59, -16.21, 0.01139, 190, 67.46, -53.38, 0.08663, 192, 70.29, 28.51, 0.09351, 193, 24.47, 28.51, 0.58824, 194, -4.16, 28.51, 0.22023, 6, 98, 260.21, -24.69, 0.00304, 190, 73.62, -61.47, 0.03329, 192, 77.44, 21.28, 0.02223, 193, 31.63, 21.28, 0.45184, 194, 2.99, 21.28, 0.48405, 195, -14.19, 21.28, 0.00555, 6, 98, 268.39, -31.76, 0.00022, 190, 82.26, -67.98, 0.00725, 192, 86.84, 15.93, 0.00058, 193, 41.03, 15.93, 0.13412, 194, 12.39, 15.93, 0.72417, 195, -4.79, 15.93, 0.13365, 5, 190, 89.47, -73.42, 0.00091, 193, 48.89, 11.47, 0.01321, 194, 20.25, 11.47, 0.3724, 195, 3.07, 11.47, 0.57418, 196, -8.38, 11.47, 0.0393, 2, 195, 13.95, 6.86, 0.24538, 196, 2.5, 6.86, 0.75462, 2, 195, 19.91, 4.35, 0.00907, 196, 8.45, 4.35, 0.99093, 1, 196, 15.62, 1.32, 1, 1, 196, 25.34, 0.04, 1, 1, 196, 24.07, -3.53, 1, 1, 196, 14.35, -7.09, 1, 2, 195, 16.15, -10.62, 0.1597, 196, 4.69, -10.62, 0.8403, 3, 194, 19.2, -12.65, 0.37534, 195, 2.02, -12.65, 0.57266, 196, -9.43, -12.65, 0.052, 3, 193, 36.97, -14.22, 0.11478, 194, 8.33, -14.22, 0.7723, 195, -8.85, -14.22, 0.11291, 2, 193, 23.29, -16.19, 0.76106, 194, -5.34, -16.19, 0.23894, 3, 192, 54.49, -14.64, 0.05517, 193, 8.68, -14.64, 0.942, 194, -19.96, -14.64, 0.00283, 2, 192, 47.68, -13.92, 0.32562, 193, 1.86, -13.92, 0.67438, 2, 192, 35.88, -11.28, 0.94161, 193, -9.93, -11.28, 0.05839, 1, 192, 24.71, -8.77, 1, 1, 192, 14.08, -6.39, 1, 2, 191, 77.45, -3.91, 0.06471, 192, 2.99, -3.91, 0.93529, 1, 191, 64.36, -2.88, 1, 1, 191, 51.4, -1.86, 1, 1, 191, 35.61, -6.01, 1, 1, 191, 21.42, -9.75, 1, 1, 191, 19.85, -13.32, 1, 2, 98, 104.26, -46.01, 0.08202, 191, 3.04, -29.87, 0.91798, 2, 98, 83.64, -59.59, 0.38532, 191, -14.56, -47.19, 0.61468, 2, 98, 62.98, -73.2, 0.60366, 191, -32.18, -64.55, 0.39634, 2, 98, 41.29, -81.2, 0.70827, 191, -51.9, -76.6, 0.29173, 2, 98, 24.79, -74.06, 0.77027, 191, -69.48, -72.79, 0.22973, 2, 98, 14.77, -65.02, 0.82256, 191, -81.07, -65.87, 0.17744, 2, 98, 3.92, -50.61, 0.89842, 191, -94.5, -53.84, 0.10158, 2, 98, -7.52, -35.42, 0.95941, 191, -108.67, -41.15, 0.04059], "hull": 51, "edges": [2, 4, 4, 6, 10, 12, 22, 24, 48, 50, 50, 52, 82, 84, 90, 92, 92, 94, 94, 96, 16, 18, 18, 20, 20, 22, 24, 26, 30, 32, 32, 34, 56, 58, 42, 44, 88, 90, 84, 86, 86, 88, 96, 98, 98, 100, 2, 0, 0, 100, 6, 8, 8, 10, 12, 14, 14, 16, 26, 28, 28, 30, 38, 40, 40, 42, 34, 36, 36, 38, 66, 68, 68, 70, 62, 64, 64, 66, 58, 60, 60, 62, 70, 72, 72, 74, 74, 76, 76, 78, 52, 54, 54, 56, 44, 46, 46, 48, 78, 80, 80, 82], "width": 205, "height": 287}}, "fashi1": {"fashi1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [191.87, -35.25, 139.36, -114.42, -32.32, -0.57, 20.19, 78.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 95, "height": 206}}, "tui4": {"tui4": {"type": "mesh", "uvs": [0.47128, 0.12157, 0.59731, 0.11897, 0.79814, 0.11483, 0.9517, 0.11167, 0.9844, 0.11223, 0.98902, 0.15521, 0.89799, 0.18347, 0.82035, 0.20757, 0.73201, 0.23499, 0.65045, 0.2603, 0.6042, 0.27465, 0.53953, 0.29472, 0.47355, 0.3152, 0.45247, 0.33552, 0.35457, 0.42585, 0.28685, 0.48835, 0.22863, 0.54206, 0.17738, 0.61304, 0.12082, 0.73084, 0.05135, 0.87551, 0.01316, 0.83814, 0.01326, 0.72169, 0.01332, 0.64147, 0.06714, 0.51834, 0.10627, 0.42879, 0.20759, 0.31575, 0.3194, 0.19099, 0.34978, 0.15709, 0.39565, 0.12312], "triangles": [19, 20, 18, 20, 21, 18, 21, 22, 18, 18, 22, 17, 22, 23, 17, 17, 23, 16, 16, 23, 15, 23, 24, 15, 15, 24, 14, 24, 25, 14, 14, 25, 13, 13, 25, 12, 25, 26, 12, 12, 26, 11, 11, 26, 27, 11, 27, 28, 10, 11, 28, 10, 28, 0, 10, 0, 9, 9, 0, 1, 9, 1, 8, 7, 1, 2, 7, 8, 1, 7, 2, 6, 5, 3, 4, 5, 6, 3, 6, 2, 3], "vertices": [1, 85, 30.05, -16.27, 1, 1, 85, 20.85, -14.62, 1, 1, 85, 6.2, -11.99, 1, 1, 85, -5, -9.97, 1, 1, 85, -7.33, -9.31, 1, 1, 85, -5.73, -1.07, 1, 1, 85, 2.09, 2.74, 1, 2, 85, 8.76, 5.99, 0.99969, 86, -15.92, 25.77, 0.00031, 2, 85, 16.36, 9.69, 0.95507, 86, -8.72, 21.35, 0.04493, 2, 85, 23.36, 13.1, 0.7303, 86, -2.07, 17.27, 0.2697, 2, 85, 27.34, 15.04, 0.5014, 86, 1.69, 14.96, 0.4986, 2, 85, 32.9, 17.75, 0.18338, 86, 6.96, 11.73, 0.81662, 2, 85, 38.57, 20.51, 0.02156, 86, 12.34, 8.43, 0.97844, 2, 85, 41, 24.01, 0.00083, 86, 16.59, 8.25, 0.99917, 1, 86, 35.61, 7.2, 1, 1, 86, 48.77, 6.47, 1, 1, 86, 60.08, 5.84, 1, 1, 86, 74.39, 6.81, 1, 1, 86, 97.47, 10.4, 1, 1, 86, 125.8, 14.81, 1, 1, 86, 119.84, 9.75, 1, 1, 86, 98.39, 2.29, 1, 1, 86, 83.62, -2.84, 1, 1, 86, 59.63, -6.97, 1, 1, 86, 42.19, -9.97, 1, 1, 86, 18.9, -10.13, 1, 2, 85, 44.1, -5.68, 0.63208, 86, -6.79, -10.31, 0.36792, 2, 85, 40.39, -11.6, 0.93691, 86, -13.77, -10.35, 0.06309, 2, 85, 35.56, -17.26, 0.99944, 86, -21.14, -9.32, 0.00056], "hull": 29, "edges": [6, 8, 8, 10, 24, 26, 32, 34, 38, 40, 54, 56, 2, 4, 4, 6, 10, 12, 16, 18, 2, 0, 0, 56, 52, 54, 48, 50, 50, 52, 26, 28, 44, 46, 46, 48, 28, 30, 30, 32, 40, 42, 42, 44, 34, 36, 36, 38, 22, 24, 18, 20, 20, 22, 12, 14, 14, 16], "width": 74, "height": 195}}, "liuhai2": {"liuhai2": {"type": "mesh", "uvs": [0.20204, 0.01016, 0.26147, 0.06954, 0.32741, 0.13543, 0.38076, 0.18874, 0.41352, 0.26447, 0.43738, 0.31965, 0.49725, 0.45808, 0.52947, 0.53257, 0.55907, 0.601, 0.60998, 0.67986, 0.67527, 0.78101, 0.78509, 0.8507, 0.87115, 0.90532, 0.98337, 0.97654, 0.96343, 1, 0.92633, 1, 0.82522, 0.96527, 0.75569, 0.94138, 0.6186, 0.89429, 0.47951, 0.81634, 0.33612, 0.73599, 0.22969, 0.67634, 0.22961, 0.62856, 0.22952, 0.56984, 0.22932, 0.43842, 0.22919, 0.35978, 0.22911, 0.30763, 0.19979, 0.25149, 0.15611, 0.1994, 0.11482, 0.15015, 0.03349, 0.05314, 0.01034, 0.00225], "triangles": [18, 10, 11, 17, 18, 11, 17, 11, 12, 16, 17, 12, 16, 12, 13, 15, 16, 13, 14, 15, 13, 9, 20, 8, 19, 20, 9, 10, 19, 9, 18, 19, 10, 23, 24, 6, 23, 6, 7, 23, 7, 8, 22, 23, 8, 20, 21, 22, 8, 20, 22, 30, 31, 0, 1, 29, 30, 1, 30, 0, 29, 1, 2, 28, 29, 2, 28, 2, 3, 27, 28, 3, 27, 3, 4, 26, 27, 4, 26, 4, 5, 25, 26, 5, 24, 25, 5, 24, 5, 6], "vertices": [1, 247, -6.39, 4.48, 1, 1, 247, 3.03, 5.84, 1, 1, 247, 13.47, 7.35, 1, 1, 247, 21.92, 8.57, 1, 1, 247, 32.87, 7.31, 1, 1, 247, 40.85, 6.38, 1, 2, 247, 60.86, 4.06, 0.11075, 248, 2.07, 5.02, 0.88925, 1, 248, 12.66, 7.39, 1, 2, 248, 22.38, 9.56, 0.93144, 249, -4.38, 14.68, 0.06856, 2, 248, 33.58, 13.3, 0.29355, 249, 6.96, 11.36, 0.70645, 2, 249, 21.51, 7.11, 0.59912, 250, -0.19, 7.24, 0.40088, 1, 250, 12.56, 6.1, 1, 1, 250, 22.56, 5.2, 1, 1, 250, 35.59, 4.03, 1, 1, 250, 36.93, 0.65, 1, 1, 250, 35.01, -1.31, 1, 1, 250, 26.25, -3.19, 1, 1, 250, 20.22, -4.48, 1, 1, 250, 8.34, -7.03, 1, 2, 249, 17.33, -7.64, 0.96965, 250, -6.77, -6.61, 0.03035, 2, 248, 41.51, -6.98, 0.23077, 249, 1.9, -9.82, 0.76923, 2, 248, 33.03, -14.84, 0.90496, 249, -9.55, -11.43, 0.09504, 2, 248, 26.24, -14.83, 0.99101, 249, -15.12, -7.55, 0.00899, 2, 247, 69.29, -19.88, 0.00939, 248, 17.91, -14.82, 0.99061, 2, 247, 51.67, -13.73, 0.62759, 248, -0.75, -14.8, 0.37241, 2, 247, 41.13, -10.04, 0.98131, 248, -11.92, -14.79, 0.01869, 1, 247, 34.14, -7.6, 1, 1, 247, 25.9, -7.01, 1, 1, 247, 17.85, -7.62, 1, 1, 247, 10.24, -8.19, 1, 1, 247, -4.75, -9.31, 1, 1, 247, -12.14, -8.54, 1], "hull": 32, "edges": [0, 62, 26, 28, 28, 30, 52, 54, 60, 62, 46, 48, 10, 12, 40, 42, 36, 38, 38, 40, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 34, 36, 30, 32, 32, 34, 42, 44, 44, 46, 12, 14, 14, 16, 6, 8, 8, 10, 48, 50, 50, 52, 0, 2, 58, 60, 2, 4, 4, 6, 54, 56, 56, 58], "width": 74, "height": 142}}, "biyan1": {"biyan1": {"x": 8.5, "y": 292, "width": 137, "height": 90}}, "toufa1": {"toufa1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-91.7, -20.56, 6.71, 89.98, 90.36, 15.51, -8.05, -95.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 148, "height": 112}}, "beijing": {"beijing": {"type": "mesh", "uvs": [1, 0.91247, 0.98078, 0.9253, 0.92057, 0.93615, 0.86833, 0.91985, 0.82415, 0.89104, 0.82765, 0.90704, 0.81458, 0.93027, 0.77831, 0.94552, 0.78631, 0.96926, 0.79289, 0.99797, 0.78749, 0.99938, 0.75859, 0.99955, 0.74931, 0.97168, 0.73048, 0.97442, 0.73007, 1, 0.32984, 0.99957, 0.2696, 0.94855, 0.23422, 0.9419, 0.17636, 0.84665, 0.16795, 0.83773, 0.14694, 0.8354, 0.13999, 0.83544, 0.10006, 0.85678, 0.09996, 0.86567, 0.1239, 0.9095, 0.13536, 0.93004, 0.17028, 0.98161, 0.17587, 0.99256, 0.16653, 0.98236, 0.1095, 0.89418, 0.09318, 0.8947, 0.08638, 0.88046, 0.07798, 0.88198, 0.061, 0.90813, 0.03338, 0.89533, 0.02126, 0.9137, 0.00879, 1, 0.0011, 1, 0, 0.9987, 0, 0.9791, 0.02081, 0.91479, 0.014, 0.89782, 0, 0.8818, 0.00066, 0.53033, 0.03556, 0.51987, 0.04042, 0.49837, 0.00044, 0.47499, 0.0004, 0.44973, 0.03913, 0.44503, 0.04702, 0.45774, 0.11957, 0.40414, 0.10386, 0.40182, 0.08767, 0.39943, 0.07312, 0.3882, 0.05797, 0.37651, 0.04469, 0.36626, 0.0434, 0.34947, 0.04207, 0.33227, 0.05336, 0.32583, 0.06656, 0.31792, 0.05908, 0.30655, 0.05057, 0.29362, 0.06088, 0.29559, 0.07213, 0.30833, 0.08133, 0.31874, 0.0725, 0.33267, 0.06454, 0.34522, 0.07421, 0.35314, 0.08356, 0.36081, 0.09415, 0.35973, 0.10495, 0.35863, 0.1177, 0.36706, 0.13531, 0.37869, 0.14783, 0.37062, 0.15475, 0.36616, 0.19196, 0.36436, 0.17859, 0.35319, 0.17164, 0.33144, 0.16503, 0.31077, 0.17411, 0.29452, 0.18237, 0.27974, 0.17553, 0.26597, 0.16005, 0.26919, 0.14522, 0.27227, 0.13882, 0.2666, 0.13118, 0.25983, 0.1279, 0.24673, 0.12488, 0.23472, 0.12421, 0.22911, 0.13242, 0.23846, 0.14102, 0.24826, 0.1533, 0.25195, 0.15858, 0.24359, 0.16291, 0.23672, 0.16602, 0.24404, 0.16812, 0.24896, 0.18799, 0.2407, 0.20145, 0.2351, 0.21229, 0.23059, 0.21014, 0.24856, 0.20729, 0.27236, 0.20431, 0.2972, 0.21127, 0.30731, 0.22113, 0.32164, 0.22698, 0.29664, 0.2329, 0.27135, 0.24166, 0.26144, 0.2366, 0.23587, 0.23298, 0.21763, 0.22818, 0.19337, 0.23218, 0.1803, 0.23986, 0.18478, 0.24813, 0.16425, 0.25649, 0.1435, 0.26435, 0.12401, 0.26652, 0.0992, 0.28301, 0.09497, 0.29204, 0.0839, 0.27018, 0.06407, 0.28468, 0.07005, 0.29673, 0.08065, 0.30164, 0.06513, 0.30785, 0.04551, 0.31579, 0.02042, 0.32208, 0.00056, 0.58253, 0.00031, 0.58256, 0.02595, 0.60524, 0.03535, 0.69677, 0.00037, 1, 0.00021], "triangles": [124, 125, 126, 117, 119, 120, 118, 119, 117, 109, 110, 111, 108, 109, 111, 87, 88, 89, 107, 108, 111, 92, 93, 94, 86, 87, 89, 86, 89, 90, 99, 97, 98, 85, 86, 90, 112, 107, 111, 106, 107, 112, 81, 95, 96, 84, 85, 90, 84, 90, 91, 95, 91, 92, 95, 82, 91, 94, 95, 92, 82, 95, 81, 83, 84, 91, 82, 83, 91, 99, 96, 97, 100, 96, 99, 81, 96, 100, 80, 81, 100, 101, 80, 100, 79, 80, 101, 60, 61, 62, 60, 62, 63, 59, 60, 63, 59, 63, 64, 101, 77, 78, 101, 78, 79, 77, 101, 102, 65, 59, 64, 58, 59, 65, 66, 58, 65, 66, 56, 57, 66, 57, 58, 103, 76, 77, 103, 77, 102, 75, 76, 103, 55, 56, 66, 54, 55, 66, 54, 66, 67, 54, 67, 68, 53, 54, 68, 69, 53, 68, 52, 53, 69, 71, 51, 69, 71, 69, 70, 50, 51, 71, 52, 69, 51, 72, 50, 71, 46, 47, 48, 46, 48, 49, 45, 46, 49, 73, 50, 72, 50, 19, 44, 45, 49, 50, 50, 44, 45, 19, 20, 44, 123, 124, 126, 122, 123, 126, 121, 122, 126, 126, 120, 121, 21, 44, 20, 75, 73, 74, 75, 50, 73, 75, 19, 50, 18, 19, 75, 21, 43, 44, 22, 43, 21, 43, 22, 42, 23, 31, 22, 22, 32, 42, 31, 32, 22, 4, 128, 129, 127, 128, 4, 24, 29, 23, 31, 23, 29, 30, 31, 29, 34, 42, 32, 41, 42, 34, 33, 34, 32, 0, 4, 129, 35, 41, 34, 40, 41, 35, 0, 3, 4, 0, 2, 3, 127, 120, 126, 120, 116, 117, 120, 127, 116, 116, 113, 114, 114, 115, 116, 113, 116, 127, 127, 4, 113, 6, 4, 5, 113, 106, 112, 113, 4, 106, 106, 104, 105, 106, 4, 104, 4, 103, 104, 4, 75, 103, 4, 18, 75, 17, 18, 16, 1, 2, 0, 16, 18, 4, 6, 7, 4, 16, 4, 13, 4, 7, 13, 12, 7, 8, 12, 13, 7, 36, 40, 35, 36, 39, 40, 28, 25, 26, 29, 24, 25, 29, 25, 28, 28, 26, 27, 36, 37, 38, 10, 8, 9, 11, 12, 8, 10, 11, 8, 15, 16, 13, 38, 39, 36, 14, 15, 13], "vertices": [749, -624.54, 720.13, -643.81, 629.7, -660.1, 551.23, -635.62, 484.87, -592.34, 490.13, -616.37, 470.5, -651.27, 416.03, -674.18, 428.04, -709.83, 437.92, -752.96, 429.8, -755.07, 386.4, -755.32, 372.47, -713.46, 344.19, -717.58, 343.57, -756, -257.59, -755.35, -348.06, -678.72, -401.2, -668.73, -488.1, -525.67, -500.73, -512.27, -532.29, -508.77, -542.74, -508.83, -602.71, -540.89, -602.87, -554.24, -566.9, -620.07, -549.69, -650.92, -497.24, -728.37, -488.84, -744.83, -502.88, -729.51, -588.54, -597.06, -613.05, -597.83, -623.25, -576.45, -635.87, -578.73, -661.37, -618.01, -702.87, -598.78, -721.06, -626.38, -739.79, -756, -751.34, -756, -753, -754.04, -753, -724.6, -721.74, -628.01, -731.98, -602.52, -753, -578.46, -752.01, -50.56, -699.59, -34.84, -692.28, -2.56, -752.34, 32.57, -752.39, 70.51, -694.23, 77.57, -682.37, 58.47, -573.4, 138.98, -597, 142.47, -621.32, 146.06, -643.17, 162.92, -665.93, 180.49, -685.87, 195.87, -687.82, 221.09, -689.82, 246.94, -672.85, 256.6, -653.03, 268.48, -664.26, 285.56, -677.05, 304.99, -661.56, 302.03, -644.66, 282.89, -630.84, 267.25, -644.1, 246.33, -656.05, 227.48, -641.54, 215.58, -627.5, 204.06, -611.58, 205.68, -595.36, 207.33, -576.22, 194.68, -549.76, 177.2, -530.96, 189.33, -520.57, 196.03, -464.67, 198.73, -484.75, 215.51, -495.19, 248.18, -505.12, 279.23, -491.49, 303.63, -479.08, 325.83, -489.36, 346.51, -512.61, 341.68, -534.88, 337.05, -544.48, 345.57, -555.96, 355.73, -560.9, 375.41, -565.43, 393.45, -566.44, 401.88, -554.11, 387.83, -541.19, 373.12, -522.74, 367.56, -514.81, 380.13, -508.31, 390.44, -503.63, 379.45, -500.49, 372.06, -470.64, 384.47, -450.42, 392.88, -434.14, 399.65, -437.37, 372.66, -441.65, 336.92, -446.12, 299.6, -435.68, 284.43, -420.86, 262.9, -412.07, 300.44, -403.18, 338.43, -390.02, 353.32, -397.63, 391.72, -403.06, 419.12, -410.27, 455.56, -404.26, 475.2, -392.73, 468.46, -380.3, 499.29, -367.74, 530.46, -355.95, 559.74, -352.68, 597, -327.92, 603.35, -314.35, 619.99, -347.18, 649.76, -325.4, 640.79, -307.32, 624.86, -299.94, 648.18, -290.61, 677.64, -278.68, 715.33, -269.24, 745.16, 121.97, 745.53, 122, 707.03, 156.07, 692.9, 293.56, 745.44, 749, 745.68], "hull": 130, "edges": [0, 258, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 114, 116, 116, 118, 122, 124, 148, 150, 150, 152, 160, 162, 174, 176, 180, 182, 210, 212, 218, 220, 220, 222, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 170, 172, 172, 174, 176, 178, 178, 180, 162, 164, 164, 166, 186, 188, 188, 190, 182, 184, 184, 186, 166, 168, 168, 170, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 156, 158, 158, 160, 152, 154, 154, 156, 206, 208, 208, 210, 144, 146, 146, 148, 140, 142, 142, 144, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 136, 138, 138, 140, 132, 134, 134, 136, 110, 112, 112, 114, 128, 130, 130, 132, 124, 126, 126, 128, 118, 120, 120, 122, 216, 218, 212, 214, 214, 216, 222, 224, 224, 226, 226, 228, 240, 242, 242, 244, 244, 246, 246, 248], "width": 378, "height": 378}}, "zhizhu3": {"zhizhu3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-83.79, -45.05, -24.36, 78.39, 93.67, 21.56, 34.24, -101.88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 137, "height": 131}}, "bian": {"bian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [351.99, -136.91, -45.17, -111.08, -31.67, 96.48, 365.49, 70.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 398, "height": 208}}, "yifu4": {"yifu4": {"type": "mesh", "uvs": [0.6421, 0, 0.66196, 0.04156, 0.68024, 0.10697, 0.69598, 0.16328, 0.69596, 0.24355, 0.69594, 0.32397, 0.69592, 0.3973, 0.70872, 0.4536, 0.72689, 0.50524, 0.75233, 0.57755, 0.76939, 0.61963, 0.80129, 0.67542, 0.83421, 0.71983, 0.85673, 0.7502, 0.91457, 0.79666, 0.94742, 0.82305, 0.98678, 0.85467, 1, 0.8706, 0.99637, 0.89963, 0.95079, 0.89975, 0.86579, 0.89999, 0.82179, 0.87992, 0.77692, 0.85946, 0.71813, 0.83264, 0.67092, 0.81111, 0.64132, 0.82214, 0.61327, 0.86475, 0.57759, 0.91894, 0.55054, 0.96003, 0.49279, 0.99583, 0.4236, 0.99593, 0.33866, 0.99604, 0.26496, 0.97484, 0.17948, 0.95024, 0.14744, 0.91871, 0.10453, 0.87648, 0.07424, 0.80938, 0.05545, 0.72643, 0.036, 0.6406, 0.01875, 0.56447, 0.00393, 0.49906, 0.00331, 0.41112, 0.00289, 0.3513, 0.10005, 0.31602, 0.2038, 0.29854, 0.31367, 0.28004, 0.40476, 0.21494, 0.47175, 0.14681, 0.54909, 0.06815, 0.59326, 0.02323, 0.63393, 0], "triangles": [30, 31, 27, 29, 30, 27, 28, 29, 27, 31, 32, 27, 26, 27, 32, 26, 32, 25, 34, 32, 33, 35, 32, 34, 25, 32, 45, 45, 35, 36, 44, 36, 37, 44, 37, 38, 44, 38, 39, 39, 40, 43, 20, 15, 19, 20, 14, 15, 20, 21, 14, 19, 16, 18, 19, 15, 16, 18, 16, 17, 21, 13, 14, 21, 22, 13, 13, 22, 12, 22, 23, 12, 12, 23, 11, 23, 24, 11, 24, 10, 11, 24, 9, 10, 24, 25, 9, 9, 25, 8, 8, 25, 7, 6, 49, 1, 7, 25, 6, 5, 6, 1, 4, 5, 1, 4, 1, 2, 4, 2, 3, 35, 45, 32, 45, 36, 44, 44, 39, 43, 25, 45, 6, 6, 45, 46, 6, 46, 47, 6, 47, 48, 6, 48, 49, 1, 50, 0, 49, 50, 1, 40, 41, 43, 41, 42, 43], "vertices": [2, 97, 180.48, 30.87, 0.59466, 173, -45.08, 6, 0.40534, 2, 97, 181.18, 19.67, 0.53287, 173, -34.7, 10.26, 0.46713, 2, 97, 179.18, 3.43, 0.26217, 173, -18.68, 13.57, 0.73783, 2, 97, 177.45, -10.54, 0.0241, 173, -4.89, 16.42, 0.9759, 1, 173, 14.2, 14.61, 1, 1, 173, 33.34, 12.79, 1, 2, 173, 50.79, 11.13, 0.97768, 174, -9.92, 15.57, 0.02232, 2, 173, 64.5, 13.21, 0.41297, 174, 3.65, 12.74, 0.58703, 2, 173, 77.24, 16.81, 0.00773, 174, 16.84, 11.67, 0.99227, 2, 174, 35.32, 10.16, 0.82178, 175, -3.23, 10.86, 0.17822, 2, 174, 46.33, 9.82, 0.06308, 175, 7.56, 8.68, 0.93692, 2, 175, 23.29, 7.8, 0.65405, 176, -1.91, 7.8, 0.34595, 2, 176, 11.76, 8.7, 0.6541, 177, -3.36, 8.7, 0.3459, 3, 176, 21.11, 9.32, 0.03277, 177, 5.99, 9.32, 0.7533, 178, -4.09, 9.32, 0.21394, 1, 178, 13.78, 15.29, 1, 1, 178, 23.92, 18.67, 1, 1, 178, 36.08, 22.73, 1, 1, 178, 41.2, 23.36, 1, 1, 178, 46.3, 18.56, 1, 1, 178, 39.38, 8.77, 1, 2, 177, 36.55, -9.48, 0.00057, 178, 26.47, -9.48, 0.99943, 6, 175, 66.25, -16.13, 0.00276, 176, 41.05, -16.13, 0.00768, 177, 25.93, -16.13, 0.06121, 178, 15.85, -16.13, 0.92693, 181, 93.41, 147.52, 0, 184, 58.94, 147.52, 0.00142, 8, 174, 98.79, -13.35, 0.00026, 175, 55.42, -22.92, 0.0495, 176, 30.23, -22.92, 0.11156, 177, 15.11, -22.92, 0.23815, 178, 5.03, -22.92, 0.58853, 180, 111.67, 138.78, 0.00012, 181, 84.1, 138.78, 0.00017, 184, 49.64, 138.78, 0.0117, 9, 97, 117.36, -158.92, 9e-05, 174, 86.28, -24.48, 0.02412, 175, 41.24, -31.8, 0.26606, 176, 16.04, -31.8, 0.28403, 177, 0.93, -31.8, 0.20174, 178, -9.15, -31.8, 0.16114, 180, 99.47, 127.31, 0.0017, 181, 71.9, 127.31, 0.00162, 184, 37.44, 127.31, 0.0595, 9, 97, 108.13, -149.14, 0.0021, 174, 76.24, -33.42, 0.10013, 175, 29.85, -38.94, 0.43251, 176, 4.65, -38.94, 0.19088, 177, -10.46, -38.94, 0.07831, 178, -20.54, -38.94, 0.03096, 180, 89.68, 118.11, 0.0058, 181, 62.11, 118.11, 0.00513, 184, 27.65, 118.11, 0.15419, 9, 97, 99.95, -148.37, 0.00417, 174, 75.23, -41.57, 0.14772, 175, 27.49, -46.81, 0.42001, 176, 2.29, -46.81, 0.11174, 177, -12.82, -46.81, 0.03324, 178, -22.9, -46.81, 0.00594, 180, 88.88, 109.93, 0.00912, 181, 61.31, 109.93, 0.00822, 184, 26.85, 109.93, 0.25985, 9, 97, 89.05, -154.64, 0.00377, 174, 81.18, -52.65, 0.15401, 175, 31.51, -58.73, 0.3548, 176, 6.32, -58.73, 0.06343, 177, -8.8, -58.73, 0.01159, 178, -18.88, -58.73, 0.00019, 180, 95.13, 99.02, 0.00936, 181, 67.57, 99.02, 0.00922, 184, 33.1, 99.02, 0.39363, 9, 97, 75.19, -162.63, 0.00158, 174, 88.76, -66.74, 0.13435, 175, 36.63, -73.88, 0.28943, 176, 11.44, -73.88, 0.03908, 177, -3.68, -73.88, 0.00327, 180, 103.09, 85.14, 0.0059, 181, 75.52, 85.14, 0.00682, 182, 58.29, 85.14, 0, 184, 41.06, 85.14, 0.51957, 8, 97, 64.69, -168.68, 0.00035, 174, 94.5, -77.42, 0.11909, 175, 40.51, -85.37, 0.25444, 176, 15.32, -85.37, 0.03029, 177, 0.2, -85.37, 0.00119, 180, 109.12, 74.62, 0.00302, 181, 81.55, 74.62, 0.00426, 184, 47.09, 74.62, 0.58737, 7, 174, 95.59, -94.82, 0.10108, 175, 38.69, -102.71, 0.21251, 176, 13.49, -102.71, 0.02233, 177, -1.63, -102.71, 0.00015, 180, 110.68, 57.25, 0.00057, 181, 83.11, 57.25, 0.00181, 184, 48.65, 57.25, 0.66153, 7, 97, 30.71, -162.86, 1e-05, 174, 87.69, -111.21, 0.07968, 175, 28.16, -117.55, 0.16091, 176, 2.96, -117.55, 0.01505, 180, 103.22, 40.66, 7e-05, 181, 75.65, 40.66, 0.00114, 184, 41.19, 40.66, 0.74314, 4, 174, 77.98, -131.33, 0.04429, 175, 15.24, -135.77, 0.08653, 176, -9.96, -135.77, 0.00701, 184, 32.04, 20.28, 0.86217, 4, 174, 64.98, -146.57, 0.01339, 175, -0.12, -148.63, 0.02625, 176, -25.32, -148.63, 0.00193, 184, 19.45, 4.69, 0.95843, 4, 181, 39.32, -13.39, 0.02201, 182, 22.09, -13.39, 0.07719, 183, 11.75, -13.39, 0.11892, 184, 4.86, -13.39, 0.78188, 5, 180, 56.55, -17.97, 0.00118, 181, 28.98, -17.97, 0.2, 182, 11.75, -17.97, 0.26174, 183, 1.42, -17.97, 0.26184, 184, -5.48, -17.97, 0.27524, 5, 180, 42.71, -24.12, 0.08318, 181, 15.14, -24.12, 0.59487, 182, -2.09, -24.12, 0.19031, 183, -12.42, -24.12, 0.10541, 184, -19.32, -24.12, 0.02622, 5, 179, 69.62, -24.79, 0.00185, 180, 24.82, -24.79, 0.5146, 181, -2.75, -24.79, 0.44065, 182, -19.98, -24.79, 0.02545, 183, -30.32, -24.79, 0.01746, 4, 179, 49.51, -21.15, 0.24149, 180, 4.71, -21.15, 0.72164, 181, -22.86, -21.15, 0.03684, 183, -50.42, -21.15, 3e-05, 2, 179, 28.71, -17.38, 0.92831, 180, -16.09, -17.38, 0.07169, 1, 179, 10.26, -14.04, 1, 2, 97, -21.46, -9.36, 0.10088, 179, -5.6, -11.17, 0.89912, 2, 97, -13.01, 9.89, 0.90289, 179, -24.82, -2.69, 0.09711, 1, 97, -7.27, 22.98, 1, 1, 97, 19.5, 20.23, 1, 1, 97, 46.11, 12.88, 1, 1, 97, 74.29, 5.1, 1, 1, 97, 102.51, 9.51, 1, 1, 97, 125.25, 17.16, 1, 2, 97, 151.5, 26, 0.8618, 173, -31.17, -19.89, 0.1382, 2, 97, 166.49, 31.05, 0.66608, 173, -40.77, -7.32, 0.33392, 2, 97, 178.52, 31.74, 0.59638, 173, -45.28, 3.86, 0.40362], "hull": 51, "edges": [0, 100, 0, 2, 12, 14, 18, 20, 20, 22, 32, 34, 34, 36, 48, 50, 56, 58, 70, 72, 84, 86, 90, 92, 98, 100, 26, 28, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 86, 88, 88, 90, 92, 94, 94, 96, 96, 98, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18, 22, 24, 24, 26, 28, 30, 30, 32, 36, 38, 38, 40], "width": 263, "height": 239}}, "maozi": {"maozi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [26.13, 91.69, 139.95, 8.46, 50.24, -114.23, -63.58, -31.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 141, "height": 152}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [156.77, -137.08, -39.5, -56.93, 31.95, 118.04, 228.22, 37.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 212, "height": 189}}, "tui20": {"tui20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [103.12, -226.99, -69.17, -6.27, 205.94, 208.47, 378.23, -12.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 280, "height": 349}}, "tui21": {"tui21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [245.69, 29.2, 203.8, -100.19, -58.78, -15.16, -16.89, 114.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 136, "height": 276}}, "tui22": {"tui22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [239.98, 12.25, 129.76, -144.97, -71.67, -3.75, 38.55, 153.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 192, "height": 246}}, "tui23": {"tui23": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [401.02, 53.88, 396.47, -55.03, -120.08, -33.43, -115.53, 75.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 109, "height": 517}}, "tui24": {"tui24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-25.77, -17.93, 9.7, 83.02, 219.15, 9.43, 183.68, -91.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 107, "height": 222}}, "tui25": {"tui25": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [266.46, 23.47, 258.29, -60.14, -14.41, -33.47, -6.24, 50.13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 84, "height": 274}}, "tui26": {"tui26": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [245.23, 384.58, 605.96, 81.74, 245.9, -347.16, -114.84, -44.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 329, "height": 392}}, "tui27": {"tui27": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-79.98, -27.82, -15.92, 155.3, 309.73, 41.37, 245.66, -141.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 345}}, "tui28": {"tui28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-61.3, 11.07, 3.67, 158.38, 301.03, 27.25, 236.07, -120.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 161, "height": 325}}, "tui29": {"tui29": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [290.57, 240.7, 435.43, -71.31, 54.48, -248.18, -90.38, 63.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 252}}, "texiao1": {"texiao1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [180.86, 55.74, 175.79, -76.16, -39.06, -67.9, -33.98, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 132, "height": 215}}, "zhizhu4tou": {"zhizhu4tou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50.47, 30, 55.24, -24.79, -0.55, -29.64, -5.32, 25.15], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 55, "height": 56}}, "tui10": {"tui10": {"type": "mesh", "uvs": [0.57572, 0.03191, 0.58597, 0.10574, 0.59704, 0.18548, 0.62326, 0.37437, 0.66585, 0.45905, 0.76659, 0.65929, 0.82177, 0.55342, 0.86486, 0.65943, 0.96353, 0.90215, 0.97311, 0.95879, 0.74285, 0.9362, 0.63285, 0.92542, 0.59644, 0.84455, 0.46032, 0.54226, 0.42013, 0.45302, 0.40868, 0.40151, 0.39731, 0.3504, 0.28681, 0.35051, 0.16225, 0.35064, 0.03772, 0.35076, 0.03712, 0.22621, 0.03636, 0.07021, 0.09945, 0.07449, 0.26692, 0.05186, 0.44862, 0.02732, 0.52532, 0.01696], "triangles": [9, 10, 8, 7, 8, 10, 10, 5, 7, 5, 10, 12, 10, 11, 12, 12, 4, 5, 12, 13, 4, 5, 6, 7, 13, 3, 4, 13, 14, 3, 14, 15, 3, 3, 16, 2, 16, 3, 15, 1, 2, 24, 19, 20, 18, 23, 17, 18, 18, 20, 22, 18, 22, 23, 17, 23, 16, 1, 25, 0, 24, 25, 1, 24, 2, 16, 16, 23, 24, 20, 21, 22], "vertices": [2, 65, 23.04, 5.72, 0.99957, 66, -5.81, 8.06, 0.00043, 2, 65, 23.35, 2.73, 0.9399, 66, -2.95, 7.13, 0.0601, 2, 65, 23.68, -0.5, 0.61632, 66, 0.13, 6.13, 0.38368, 2, 65, 24.47, -8.14, 0.00458, 66, 7.43, 3.74, 0.99542, 1, 66, 11.49, 4.11, 1, 1, 66, 21.08, 4.98, 1, 1, 66, 18.75, 9.57, 1, 1, 66, 23.56, 9.56, 1, 1, 66, 34.59, 9.52, 1, 1, 66, 36.83, 8.89, 1, 1, 66, 30.21, -1.41, 1, 1, 66, 27.05, -6.33, 1, 1, 66, 23.29, -6.48, 1, 1, 66, 9.22, -7.05, 1, 2, 65, 13.49, -10.43, 0.02538, 66, 5.06, -7.22, 0.97462, 2, 65, 13.05, -8.32, 0.11269, 66, 2.96, -6.77, 0.88731, 2, 65, 12.61, -6.24, 0.40024, 66, 0.88, -6.32, 0.59976, 2, 65, 6.77, -5.78, 0.95359, 66, -1.91, -11.47, 0.04641, 1, 65, 0.19, -5.27, 1, 1, 65, -6.39, -4.75, 1, 1, 65, -6.03, 0.22, 1, 1, 65, -5.58, 6.44, 1, 1, 65, -2.26, 6.01, 1, 1, 65, 6.66, 6.21, 1, 1, 65, 16.34, 6.43, 1, 1, 65, 20.42, 6.52, 1], "hull": 26, "edges": [0, 50, 10, 12, 16, 18, 42, 44, 48, 50, 44, 46, 46, 48, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 4, 6, 0, 2, 2, 4, 28, 30, 30, 32, 26, 28, 6, 8, 8, 10, 22, 24, 24, 26, 18, 20, 20, 22, 12, 14, 14, 16], "width": 53, "height": 40}}, "qianjing1": {"qianjing1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [500.04, 49.26, 67.13, -200.93, -114, 112.5, 318.91, 362.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 300, "height": 217}}, "qianjing2": {"qianjing2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431.55, -171.93, -188.72, -2.47, -118.35, 255.09, 501.92, 85.64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 450, "height": 186}}, "biyan2": {"biyan2": {"x": -344.5, "y": -169.5, "width": 95, "height": 65}}, "zhizhu1": {"zhizhu1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-3.11, 136, 169.78, -29.02, 87.61, -115.1, -85.27, 49.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 239, "height": 119}}, "shenti1": {"shenti1": {"type": "mesh", "uvs": [0.73554, 0.01043, 0.73548, 0.02524, 0.64141, 0.02507, 0.71365, 0.10235, 0.81061, 0.14488, 0.84656, 0.20686, 0.8683, 0.23067, 0.9675, 0.27207, 0.9638, 0.32153, 0.95977, 0.3754, 0.95707, 0.41146, 0.94597, 0.49839, 0.94133, 0.53474, 0.93525, 0.58232, 0.93093, 0.61612, 0.92341, 0.67504, 0.92544, 0.70718, 0.89722, 0.74484, 0.86824, 0.81645, 0.84746, 0.86777, 0.7743, 0.90681, 0.64869, 0.91105, 0.62672, 0.9051, 0.54324, 0.85397, 0.42449, 0.78124, 0.39767, 0.69167, 0.37081, 0.60198, 0.34389, 0.51206, 0.29416, 0.43386, 0.22506, 0.43838, 0.20103, 0.34563, 0.16371, 0.29666, 0.09606, 0.25723, 0.02351, 0.21494, 0.0232, 0.13468, 0.16161, 0.11454, 0.25278, 0.10128, 0.26712, 0.09394, 0.31635, 0.05077, 0.48902, 0.0241, 0.57783, 0.01039], "triangles": [21, 22, 20, 20, 22, 19, 22, 23, 19, 19, 23, 18, 18, 23, 17, 17, 23, 24, 24, 25, 17, 16, 17, 15, 17, 25, 15, 15, 25, 14, 14, 25, 26, 14, 26, 13, 13, 26, 12, 12, 26, 27, 12, 27, 11, 11, 27, 10, 10, 27, 28, 28, 9, 10, 6, 28, 5, 5, 28, 4, 3, 4, 30, 29, 30, 28, 39, 3, 37, 3, 39, 2, 39, 37, 38, 39, 40, 2, 30, 4, 28, 37, 31, 36, 37, 3, 30, 9, 28, 6, 9, 6, 8, 37, 30, 31, 8, 6, 7, 31, 32, 36, 36, 32, 35, 32, 33, 35, 33, 34, 35, 1, 2, 0, 2, 40, 0], "vertices": [1, 70, -24.54, 100.32, 1, 1, 70, -17.74, 98.4, 1, 2, 70, -24.27, 75.32, 0.99993, 71, -171.36, 75.32, 7e-05, 2, 70, 16.19, 83.14, 0.98632, 71, -130.9, 83.14, 0.01368, 2, 70, 42.38, 101.48, 0.94741, 71, -104.71, 101.48, 0.05259, 2, 70, 73.32, 102.35, 0.86171, 71, -73.77, 102.35, 0.13829, 2, 70, 85.75, 104.63, 0.79154, 71, -61.34, 104.63, 0.20846, 3, 70, 111.58, 123.68, 0.65472, 71, -35.51, 123.68, 0.34519, 72, -123.76, 123.68, 9e-05, 3, 70, 134.05, 116.42, 0.56338, 71, -13.04, 116.42, 0.43352, 72, -101.29, 116.42, 0.0031, 3, 70, 158.52, 108.51, 0.39785, 71, 11.43, 108.51, 0.58314, 72, -76.82, 108.51, 0.019, 3, 70, 174.9, 103.21, 0.27942, 71, 27.81, 103.21, 0.67607, 72, -60.44, 103.21, 0.04452, 4, 70, 214.07, 89.32, 0.06692, 71, 66.99, 89.32, 0.72262, 72, -21.27, 89.32, 0.20834, 73, -80.1, 89.32, 0.00212, 4, 70, 230.45, 83.51, 0.02553, 71, 83.36, 83.51, 0.62435, 72, -4.89, 83.51, 0.33643, 73, -63.72, 83.51, 0.01369, 4, 70, 251.89, 75.91, 0.00344, 71, 104.8, 75.91, 0.4079, 72, 16.55, 75.91, 0.52325, 73, -42.28, 75.91, 0.06541, 4, 70, 267.12, 70.51, 0.00014, 71, 120.03, 70.51, 0.24319, 72, 31.78, 70.51, 0.60707, 73, -27.05, 70.51, 0.1496, 3, 71, 146.59, 61.09, 0.05256, 72, 58.33, 61.09, 0.50488, 73, -0.5, 61.09, 0.44255, 3, 71, 161.49, 57.46, 0.01494, 72, 73.24, 57.46, 0.35324, 73, 14.4, 57.46, 0.63183, 3, 71, 176.85, 45.7, 0.00098, 72, 88.6, 45.7, 0.15405, 73, 29.77, 45.7, 0.84497, 2, 72, 119.5, 29.38, 0.00114, 73, 60.67, 29.38, 0.99886, 1, 73, 82.82, 17.69, 1, 1, 73, 95.73, -5.29, 1, 2, 72, 147.89, -36.68, 4e-05, 73, 89.05, -36.68, 0.99996, 2, 72, 143.65, -41.32, 0.00074, 73, 84.81, -41.32, 0.99926, 2, 72, 114.43, -55.25, 0.05029, 73, 55.59, -55.25, 0.94971, 3, 71, 161.11, -75.07, 0.00678, 72, 72.86, -75.07, 0.34459, 73, 14.03, -75.07, 0.64863, 3, 71, 118.13, -70.16, 0.14817, 72, 29.87, -70.16, 0.61701, 73, -28.96, -70.16, 0.23482, 4, 70, 222.16, -65.23, 0.00035, 71, 75.08, -65.23, 0.6355, 72, -13.18, -65.23, 0.34374, 73, -72.01, -65.23, 0.02041, 3, 70, 179.01, -60.29, 0.11307, 71, 31.92, -60.29, 0.85187, 72, -56.33, -60.29, 0.03507, 2, 70, 139.67, -62.46, 0.64739, 71, -7.41, -62.46, 0.35261, 2, 70, 137, -80.01, 0.76104, 71, -10.08, -80.01, 0.23896, 2, 70, 92.75, -74, 0.94308, 71, -54.34, -74, 0.05692, 2, 70, 67.69, -76.88, 0.99294, 71, -79.4, -76.88, 0.00706, 2, 70, 44.93, -88.43, 0.99999, 71, -102.16, -88.43, 1e-05, 1, 70, 20.52, -100.81, 1, 1, 70, -16.37, -90.58, 1, 1, 70, -16.12, -54, 1, 1, 70, -15.95, -29.91, 1, 1, 70, -18.34, -25.44, 1, 1, 70, -34.79, -7.81, 1, 1, 70, -35.18, 38.02, 1, 1, 70, -35.39, 61.59, 1], "hull": 41, "edges": [0, 80, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 32, 34, 38, 40, 40, 42, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 72, 74, 74, 76, 30, 32, 42, 44, 20, 22, 22, 24, 24, 26, 14, 16, 52, 54, 48, 50, 50, 52, 34, 36, 36, 38, 44, 46, 46, 48, 62, 64, 64, 66, 76, 78, 78, 80, 68, 70, 70, 72, 26, 28, 28, 30, 16, 18, 18, 20], "width": 255, "height": 477}}, "tui9": {"tui9": {"type": "mesh", "uvs": [0.53149, 0.06666, 0.55515, 0.12803, 0.63431, 0.33337, 0.67724, 0.44473, 0.77123, 0.68855, 0.90496, 0.83163, 1, 0.93332, 1, 0.94319, 0.82474, 0.94333, 0.70325, 0.83773, 0.47659, 0.64073, 0.34249, 0.52417, 0.23371, 0.42961, 0.17785, 0.38106, 0.09389, 0.30809, 0.01924, 0.2432, 0.02201, 0.05409, 0.1198, 0.04404, 0.31644, 0.02382, 0.41674, 0.01351, 0.50741, 0.00418], "triangles": [8, 5, 7, 8, 9, 5, 5, 6, 7, 9, 4, 5, 9, 10, 4, 10, 3, 4, 2, 10, 11, 10, 2, 3, 2, 11, 1, 1, 19, 0, 0, 19, 20, 19, 1, 18, 1, 11, 12, 18, 1, 12, 12, 13, 18, 14, 17, 13, 13, 17, 18, 14, 15, 17, 17, 15, 16], "vertices": [2, 67, 17.18, 4.66, 0.95072, 68, -4.55, 6.18, 0.04928, 2, 67, 18.41, 2.63, 0.75645, 68, -2.2, 5.87, 0.24355, 2, 67, 22.54, -4.17, 0.00988, 68, 5.69, 4.82, 0.99012, 1, 68, 9.96, 4.25, 1, 1, 68, 19.32, 3.01, 1, 1, 68, 26.65, 5.12, 1, 1, 68, 31.86, 6.62, 1, 1, 68, 32.15, 6.43, 1, 1, 68, 28.06, 0.1, 1, 1, 68, 22.12, -2.28, 1, 2, 67, 16.9, -15.57, 0.00068, 68, 11.03, -6.72, 0.99932, 2, 67, 10.74, -12.1, 0.15585, 68, 4.48, -9.34, 0.84415, 2, 67, 5.75, -9.29, 0.60109, 68, -0.84, -11.47, 0.39891, 2, 67, 3.18, -7.85, 0.81202, 68, -3.57, -12.56, 0.18798, 2, 67, -0.67, -5.68, 0.96949, 68, -7.68, -14.21, 0.03051, 2, 67, -4.1, -3.75, 0.99887, 68, -11.33, -15.67, 0.00113, 1, 67, -4.66, 2.84, 1, 1, 67, -0.51, 3.63, 1, 1, 67, 7.82, 5.2, 1, 1, 67, 12.08, 6, 1, 2, 67, 15.92, 6.73, 0.99792, 68, -6.95, 6.5, 0.00208], "hull": 21, "edges": [12, 14, 14, 16, 30, 32, 32, 34, 34, 36, 6, 8, 2, 4, 4, 6, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 36, 38, 38, 40, 2, 0, 0, 40], "width": 43, "height": 35}}, "tui8": {"tui8": {"type": "mesh", "uvs": [0.063, 0, 0.12063, 0.02941, 0.17772, 0.06812, 0.24388, 0.11298, 0.35658, 0.18939, 0.45637, 0.25705, 0.60879, 0.4158, 0.74868, 0.56152, 0.85177, 0.66889, 0.95657, 0.77805, 0.97793, 0.8837, 0.99637, 0.97495, 0.9642, 1, 0.95381, 1, 0.86257, 0.89024, 0.77893, 0.78961, 0.69279, 0.68598, 0.59816, 0.59793, 0.50063, 0.50718, 0.37772, 0.3928, 0.32737, 0.35063, 0.24952, 0.28543, 0.21325, 0.25505, 0.18766, 0.23362, 0.16764, 0.25129, 0.14896, 0.26777, 0.08655, 0.32283, 0.00957, 0.39075, 0.00961, 0.2719, 0.00965, 0.13083, 0.02262, 0.09071, 0.0391, 0.03973, 0.05194, 0, 0.04966, 0.09202], "triangles": [12, 13, 11, 11, 14, 10, 11, 13, 14, 14, 9, 10, 14, 15, 9, 15, 8, 9, 15, 16, 8, 16, 7, 8, 16, 17, 7, 17, 18, 7, 18, 6, 7, 18, 19, 6, 19, 5, 6, 19, 20, 5, 20, 21, 5, 21, 4, 5, 21, 22, 4, 22, 23, 4, 3, 23, 33, 23, 3, 4, 3, 33, 2, 27, 28, 26, 26, 28, 25, 25, 28, 24, 24, 28, 29, 24, 29, 23, 33, 23, 29, 2, 33, 1, 29, 30, 33, 0, 1, 31, 31, 32, 0, 30, 31, 33, 1, 33, 31], "vertices": [2, 95, 42.87, 8.3, 0.99988, 96, -21.83, 1.38, 0.00012, 2, 95, 39.45, 1.76, 0.93372, 96, -14.78, 3.53, 0.06628, 2, 95, 34.61, -4.97, 0.47987, 96, -6.58, 4.81, 0.52013, 1, 96, 2.91, 6.29, 1, 1, 96, 19.09, 8.82, 1, 1, 96, 33.41, 11.06, 1, 1, 96, 62.26, 9.56, 1, 1, 96, 88.74, 8.19, 1, 1, 96, 108.26, 7.17, 1, 1, 96, 128.1, 6.14, 1, 1, 96, 142.64, -1.46, 1, 1, 96, 155.2, -8.03, 1, 1, 96, 156.48, -12.91, 1, 1, 96, 155.88, -13.77, 1, 1, 96, 136.75, -11.57, 1, 1, 96, 119.22, -9.55, 1, 1, 96, 101.16, -7.47, 1, 1, 96, 84.57, -7.47, 1, 1, 96, 67.47, -7.47, 1, 1, 96, 45.93, -7.48, 1, 1, 96, 37.69, -7.9, 1, 2, 95, 2.96, -18.05, 0.00995, 96, 24.95, -8.54, 0.99005, 2, 95, 6.91, -13.61, 0.11285, 96, 19.02, -8.84, 0.88715, 2, 95, 9.7, -10.48, 0.41393, 96, 14.83, -9.05, 0.58607, 2, 95, 6.67, -8.97, 0.75581, 96, 15.89, -12.27, 0.24419, 2, 95, 3.83, -7.57, 0.90434, 96, 16.88, -15.28, 0.09566, 1, 95, -5.63, -2.87, 1, 1, 95, -17.3, 2.93, 1, 1, 95, 0.71, 6.17, 1, 1, 95, 22.09, 10.02, 1, 1, 95, 28.4, 9.83, 1, 1, 95, 36.42, 9.59, 1, 1, 95, 42.68, 9.4, 1, 1, 95, 28.69, 7.11, 1], "hull": 33, "edges": [0, 64, 0, 2, 22, 24, 24, 26, 62, 64, 58, 60, 60, 62, 60, 66, 50, 52, 52, 54, 54, 56, 56, 58, 6, 8, 8, 10, 38, 40, 40, 42, 10, 12, 36, 38, 12, 14, 32, 34, 34, 36, 14, 16, 16, 18, 30, 32, 26, 28, 28, 30, 18, 20, 20, 22, 46, 48, 48, 50, 42, 44, 44, 46, 2, 4, 4, 6], "width": 101, "height": 154}}, "shenti4": {"shenti4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [158.24, -344.19, -180.11, 68.94, 256.23, 426.3, 594.58, 13.17], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 224, "height": 236}}, "texiao2": {"texiao2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [256.21, -201.11, -168.41, -133.29, -130.87, 101.73, 293.74, 33.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 430, "height": 238}}, "zui1": {"zui1": {"type": "mesh", "uvs": [0.98406, 0.01494, 0.98454, 0.20846, 0.98496, 0.37956, 0.92816, 0.54679, 0.85568, 0.76018, 0.72818, 0.8877, 0.61589, 1, 0.55112, 1, 0.36789, 0.97282, 0.21173, 0.94966, 0.01487, 0.80668, 0.01568, 0.61866, 0.16787, 0.46906, 0.3513, 0.28875, 0.61062, 0.13378, 0.81064, 0.01425], "triangles": [2, 14, 1, 1, 15, 0, 1, 14, 15, 3, 14, 2, 3, 13, 14, 4, 13, 3, 5, 13, 4, 12, 13, 5, 8, 9, 12, 11, 12, 9, 10, 11, 9, 5, 8, 12, 7, 8, 5, 6, 7, 5], "vertices": [63.6, 7.95, 56.07, -1.64, 49.41, -10.12, 40.12, -16.21, 28.26, -23.97, 17.08, -25.38, 7.23, -26.62, 4.08, -24.13, -3.78, -15.75, -10.47, -8.6, -14.47, 6.03, -7.09, 15.3, 6.16, 16.85, 22.12, 18.72, 40.79, 16.42, 55.19, 14.64], "hull": 16, "edges": [0, 30, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 2, 2, 4, 14, 16, 16, 18, 4, 6, 6, 8, 8, 10, 10, 12], "width": 62, "height": 63}}, "tui5": {"tui5": {"type": "mesh", "uvs": [0.1425, 0.09626, 0.1426, 0.05331, 0.14271, 0.00018, 0.18646, 0.00934, 0.24278, 0.02112, 0.32482, 0.05685, 0.4174, 0.09718, 0.48406, 0.14408, 0.57029, 0.20476, 0.63989, 0.28786, 0.70776, 0.36892, 0.76861, 0.44157, 0.85802, 0.61588, 0.93591, 0.76774, 0.96298, 0.85114, 0.98726, 0.92592, 0.98836, 0.99584, 0.95638, 0.98135, 0.8617, 0.86214, 0.76534, 0.7408, 0.6337, 0.57504, 0.56184, 0.48455, 0.47605, 0.40385, 0.44113, 0.37101, 0.41965, 0.3508, 0.40298, 0.33513, 0.36029, 0.33711, 0.31893, 0.33903, 0.23651, 0.34285, 0.10724, 0.34885, 0, 0.31294, 0, 0.29397, 0.08752, 0.19443, 0.14242, 0.13199], "triangles": [17, 15, 16, 17, 18, 15, 18, 14, 15, 18, 13, 14, 18, 19, 13, 19, 12, 13, 19, 20, 12, 20, 11, 12, 20, 21, 11, 21, 10, 11, 21, 22, 10, 22, 23, 10, 10, 23, 9, 23, 24, 9, 24, 25, 9, 26, 27, 6, 26, 8, 25, 25, 8, 9, 8, 26, 7, 7, 26, 6, 28, 29, 31, 32, 27, 28, 32, 28, 31, 27, 32, 6, 6, 32, 33, 6, 33, 0, 0, 5, 6, 0, 1, 5, 30, 31, 29, 1, 4, 5, 1, 3, 4, 1, 2, 3], "vertices": [1, 89, 33.37, 13.55, 1, 1, 89, 40.15, 16.78, 1, 1, 89, 48.55, 20.77, 1, 1, 89, 48.56, 17.04, 1, 1, 89, 48.56, 12.24, 1, 2, 89, 45.64, 3.85, 0.98761, 90, -19.78, 5.79, 0.01239, 2, 89, 42.34, -5.63, 0.6781, 90, -10.48, 9.55, 0.3219, 2, 89, 37.14, -13.79, 0.10338, 90, -0.91, 11.03, 0.89662, 1, 90, 11.47, 12.93, 1, 1, 90, 26.95, 12.11, 1, 1, 90, 42.04, 11.31, 1, 1, 90, 55.57, 10.59, 1, 1, 90, 86.32, 4.87, 1, 1, 90, 113.1, -0.12, 1, 1, 90, 127.33, -3.97, 1, 1, 90, 140.09, -7.43, 1, 1, 90, 151.37, -12.18, 1, 1, 90, 148.06, -13.44, 1, 1, 90, 126.02, -11.9, 1, 1, 90, 103.58, -10.33, 1, 1, 90, 72.93, -8.18, 1, 1, 90, 56.19, -7.01, 1, 1, 90, 40.61, -7.5, 1, 2, 89, -0.12, -27.91, 0.00601, 90, 34.27, -7.7, 0.99399, 2, 89, 2.36, -24.9, 0.03587, 90, 30.37, -7.82, 0.96413, 2, 89, 4.28, -22.56, 0.11943, 90, 27.34, -7.91, 0.88057, 2, 89, 2.55, -19.74, 0.29446, 90, 26.36, -11.07, 0.70554, 2, 89, 0.88, -17.01, 0.46107, 90, 25.41, -14.13, 0.53893, 2, 89, -2.46, -11.57, 0.7622, 90, 23.52, -20.22, 0.2378, 2, 89, -7.69, -3.04, 0.9838, 90, 20.55, -29.78, 0.0162, 1, 89, -5.58, 7.12, 1, 1, 89, -2.58, 8.55, 1, 1, 89, 16.04, 9.97, 1, 1, 89, 27.72, 10.86, 1], "hull": 34, "edges": [30, 32, 32, 34, 58, 60, 60, 62, 42, 44, 48, 50, 54, 56, 56, 58, 44, 46, 46, 48, 40, 42, 22, 24, 24, 26, 38, 40, 34, 36, 36, 38, 26, 28, 28, 30, 16, 18, 18, 20, 20, 22, 12, 14, 14, 16, 4, 2, 4, 6, 6, 8, 2, 0, 0, 66, 8, 10, 10, 12, 62, 64, 64, 66, 50, 52, 52, 54], "width": 77, "height": 175}}, "zhizhuwang6": {"zhizhuwang6": {"type": "mesh", "uvs": [0.66889, 0.01934, 0.66897, 0.0919, 0.66903, 0.15383, 0.6691, 0.22124, 0.73381, 0.17676, 0.80646, 0.12682, 0.87896, 0.08283, 0.97572, 0.02411, 0.9215, 0.10127, 0.86485, 0.18191, 0.82365, 0.24055, 0.89762, 0.23297, 0.99479, 0.22303, 0.99488, 0.27516, 0.99499, 0.34512, 0.90305, 0.31736, 0.8046, 0.28763, 0.80729, 0.36108, 0.80941, 0.41918, 0.81033, 0.44449, 0.85235, 0.49459, 0.888, 0.53711, 0.87114, 0.56991, 0.85346, 0.6043, 0.82217, 0.66515, 0.78156, 0.74412, 0.75196, 0.8017, 0.73127, 0.84193, 0.71897, 0.86584, 0.58466, 0.89331, 0.50594, 0.9094, 0.45115, 0.92061, 0.47774, 0.94878, 0.50083, 0.97325, 0.52242, 1, 0.50604, 1, 0.46035, 0.98039, 0.43615, 0.94747, 0.40195, 0.90095, 0.39181, 0.85805, 0.37941, 0.80562, 0.36028, 0.72471, 0.3602, 0.61195, 0.36016, 0.55311, 0.36013, 0.50391, 0.36011, 0.47712, 0.31467, 0.4205, 0.25732, 0.35398, 0.16997, 0.25265, 0.07006, 0.13675, 0, 0.05548, 0, 0.03926, 0.03785, 0.03062, 0.1321, 0.07218, 0.27365, 0.13461, 0.38915, 0.16316, 0.53446, 0.19908, 0.53201, 0.1422, 0.52909, 0.07415, 0.52608, 0.00396, 0.59348, 0.00506], "triangles": [36, 33, 35, 34, 35, 33, 36, 32, 33, 36, 37, 32, 37, 31, 32, 37, 38, 31, 31, 38, 30, 29, 30, 39, 30, 38, 39, 28, 29, 27, 40, 27, 29, 29, 39, 40, 27, 40, 26, 40, 41, 26, 26, 41, 25, 25, 41, 24, 41, 42, 24, 24, 42, 23, 42, 43, 23, 23, 43, 22, 43, 20, 22, 22, 20, 21, 20, 44, 19, 20, 43, 44, 44, 45, 19, 45, 18, 19, 18, 46, 17, 16, 17, 3, 3, 17, 46, 15, 13, 14, 15, 16, 11, 16, 10, 11, 15, 11, 13, 16, 3, 10, 11, 12, 13, 3, 4, 10, 10, 4, 9, 4, 5, 9, 9, 5, 8, 5, 6, 8, 8, 6, 7, 56, 3, 47, 56, 2, 3, 56, 57, 2, 57, 1, 2, 1, 58, 0, 0, 58, 60, 58, 1, 57, 58, 59, 60, 18, 45, 46, 3, 46, 47, 56, 48, 55, 56, 47, 48, 48, 54, 55, 48, 49, 54, 49, 53, 54, 49, 50, 53, 50, 52, 53, 50, 51, 52], "vertices": [1, 120, 127.44, -15.43, 1, 1, 120, 99.98, -13.92, 1, 1, 120, 76.54, -12.63, 1, 2, 120, 51.03, -11.22, 0.67604, 121, 61.13, 21.33, 0.32396, 2, 120, 67.25, -23.27, 0.0502, 121, 80.31, 14.99, 0.9498, 1, 121, 101.84, 7.87, 1, 1, 121, 121.19, 0.19, 1, 1, 121, 147.02, -10.07, 1, 1, 121, 116.36, -8.7, 1, 1, 121, 84.31, -7.27, 1, 1, 121, 61.01, -6.24, 1, 1, 121, 67.1, -17.77, 1, 1, 121, 75.1, -32.92, 1, 1, 121, 56.03, -38.09, 1, 1, 121, 30.45, -45.03, 1, 1, 121, 36.47, -27.02, 1, 1, 121, 42.92, -7.73, 1, 2, 121, 16.18, -15.45, 0.99783, 118, -50.39, 34.29, 0.00217, 2, 121, -4.98, -21.55, 0.81238, 118, -28.43, 36, 0.18762, 2, 121, -14.21, -24.21, 0.61258, 118, -18.87, 36.75, 0.38742, 2, 121, -30.65, -36.14, 0.2946, 118, -0.36, 45.12, 0.7054, 2, 121, -44.6, -46.27, 0.17436, 118, 15.35, 52.23, 0.82564, 2, 121, -57.36, -46.71, 0.11675, 118, 27.94, 50.09, 0.88325, 2, 121, -70.73, -47.18, 0.0621, 118, 41.13, 47.85, 0.9379, 2, 121, -94.4, -48.01, 0.01154, 118, 64.48, 43.89, 0.98846, 2, 121, -125.12, -49.08, 3e-05, 118, 94.78, 38.75, 0.99997, 1, 118, 116.87, 35, 1, 1, 118, 132.31, 32.38, 1, 1, 118, 141.49, 30.82, 1, 1, 118, 153.29, 8.4, 1, 1, 118, 160.2, -4.74, 1, 1, 118, 165.02, -13.89, 1, 1, 118, 175.4, -8.67, 1, 1, 118, 184.41, -4.14, 1, 1, 118, 194.3, 0.19, 1, 1, 118, 194.47, -2.63, 1, 1, 118, 187.54, -10.93, 1, 1, 118, 175.34, -15.84, 1, 1, 118, 158.1, -22.79, 1, 1, 118, 141.98, -25.53, 1, 1, 118, 122.27, -28.87, 1, 1, 118, 91.87, -34.03, 1, 2, 119, -49.18, 70.7, 0.00743, 118, 49.21, -36.65, 0.99257, 2, 119, -30.21, 58.97, 0.08012, 118, 26.95, -38.02, 0.91988, 2, 119, -14.35, 49.16, 0.27818, 118, 8.34, -39.17, 0.72182, 2, 119, -5.72, 43.82, 0.46028, 118, -1.79, -39.79, 0.53972, 2, 119, 16.64, 39.17, 0.82631, 118, -22.73, -48.9, 0.17369, 2, 119, 43.27, 34.29, 0.98482, 118, -47.3, -60.29, 0.01518, 1, 119, 83.83, 26.85, 1, 1, 119, 130.22, 18.34, 1, 1, 119, 162.76, 12.37, 1, 1, 119, 167.98, 9.14, 1, 1, 119, 167.34, 1.88, 1, 1, 119, 145.41, -3.62, 1, 2, 119, 112.48, -11.86, 0.99697, 120, 87.59, 54.87, 0.00303, 2, 119, 92.83, -23.06, 0.88003, 120, 75.68, 35.64, 0.11997, 2, 119, 68.1, -37.14, 0.17855, 120, 60.7, 11.44, 0.82145, 1, 120, 82.25, 10.66, 1, 1, 120, 108.03, 9.73, 1, 1, 120, 134.62, 8.77, 1, 1, 120, 133.56, -2.78, 1], "hull": 61, "edges": [0, 120, 66, 68, 68, 70, 70, 72, 90, 92, 100, 102, 102, 104, 118, 120, 82, 84, 80, 82, 50, 52, 56, 58, 76, 78, 78, 80, 72, 74, 74, 76, 62, 64, 64, 66, 46, 48, 48, 50, 38, 40, 40, 42, 84, 86, 92, 94, 98, 100, 94, 96, 96, 98, 108, 110, 110, 112, 104, 106, 106, 108, 116, 118, 112, 114, 114, 116, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 86, 88, 88, 90, 42, 44, 44, 46, 58, 60, 60, 62, 52, 54, 54, 56], "width": 172, "height": 379}}, "zhizhuwang7": {"zhizhuwang7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-82.49, 168.95, 196.16, 5.61, 121.82, -121.21, -156.83, 42.14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 323, "height": 147}}, "tui3": {"tui3": {"type": "mesh", "uvs": [0.11619, 0.1905, 0.20102, 0.08147, 0.2459, 0.02378, 0.30176, 0.03528, 0.36545, 0.04839, 0.73707, 0.1249, 0.9669, 0.17222, 0.99, 0.21395, 0.98978, 0.2802, 0.92912, 0.28075, 0.74273, 0.25804, 0.49324, 0.22763, 0.41523, 0.21813, 0.39072, 0.23645, 0.36334, 0.25691, 0.28862, 0.31276, 0.24197, 0.46488, 0.19404, 0.62118, 0.1939, 0.73625, 0.21617, 0.85586, 0.23431, 0.95328, 0.213, 1, 0.20516, 1, 0.20135, 0.99031, 0.07469, 0.80425, 0.0423, 0.69514, 0, 0.55268, 0, 0.50971, 0.01473, 0.43292, 0.04325, 0.28425], "triangles": [20, 21, 23, 21, 22, 23, 20, 24, 19, 20, 23, 24, 24, 18, 19, 24, 25, 18, 18, 25, 17, 25, 26, 17, 17, 26, 16, 26, 27, 16, 27, 28, 16, 16, 28, 15, 28, 29, 15, 29, 0, 15, 15, 0, 14, 14, 0, 13, 13, 0, 1, 1, 2, 3, 8, 9, 7, 7, 9, 10, 11, 5, 10, 10, 6, 7, 10, 5, 6, 12, 13, 3, 3, 13, 1, 12, 3, 4, 5, 11, 4, 11, 12, 4], "vertices": [2, 83, 69.74, 13.49, 0.0186, 84, 5.81, -12.95, 0.9814, 2, 83, 67.41, -7.03, 0.97047, 84, -14.53, -9.39, 0.02953, 1, 83, 66.18, -17.88, 1, 1, 83, 60.63, -17.38, 1, 1, 83, 54.3, -16.8, 1, 1, 83, 17.39, -13.45, 1, 1, 83, -5.44, -11.38, 1, 1, 83, -9.52, -4.95, 1, 1, 83, -12.69, 6.2, 1, 1, 83, -7.29, 7.85, 1, 1, 83, 10.47, 8.8, 1, 2, 83, 34.24, 10.08, 0.95964, 84, 4.55, 22.69, 0.04036, 2, 83, 41.67, 10.47, 0.66278, 84, 4.5, 15.24, 0.33722, 2, 83, 42.98, 14.19, 0.34784, 84, 8.12, 13.71, 0.65216, 2, 83, 44.44, 18.33, 0.14156, 84, 12.17, 12.01, 0.85844, 2, 83, 48.43, 29.64, 0.00067, 84, 23.22, 7.34, 0.99933, 1, 84, 50.15, 8.88, 1, 1, 84, 77.81, 10.46, 1, 1, 84, 97.47, 14.82, 1, 1, 84, 117.46, 21.38, 1, 1, 84, 133.74, 26.73, 1, 1, 84, 142.15, 26.57, 1, 1, 84, 142.31, 25.85, 1, 1, 84, 140.73, 25.14, 1, 1, 84, 111.5, 6.58, 1, 1, 84, 93.51, -0.5, 1, 1, 84, 70.03, -9.75, 1, 1, 84, 62.69, -11.38, 1, 1, 84, 49.27, -12.96, 1, 1, 84, 23.3, -16.02, 1], "hull": 30, "edges": [12, 14, 14, 16, 16, 18, 34, 36, 40, 42, 42, 44, 44, 46, 46, 48, 52, 54, 4, 2, 22, 24, 28, 30, 8, 10, 10, 12, 18, 20, 20, 22, 54, 56, 56, 58, 30, 32, 32, 34, 48, 50, 50, 52, 36, 38, 38, 40, 2, 0, 0, 58, 4, 6, 6, 8, 24, 26, 26, 28], "width": 93, "height": 175}}, "tengman": {"tengman": {"type": "mesh", "uvs": [0.18998, 0.01715, 0.21407, 0.02669, 0.23643, 0.04046, 0.25367, 0.05108, 0.27898, 0.06667, 0.29651, 0.07746, 0.31975, 0.08578, 0.34932, 0.07783, 0.36579, 0.0734, 0.39442, 0.0657, 0.4164, 0.05979, 0.42945, 0.06447, 0.44323, 0.06941, 0.45987, 0.07537, 0.47362, 0.0803, 0.47323, 0.07415, 0.47229, 0.05933, 0.47124, 0.04289, 0.49675, 0.02652, 0.51124, 0.01722, 0.51855, 0.01771, 0.52546, 0.02482, 0.53673, 0.0364, 0.54259, 0.03792, 0.58489, 0.05733, 0.61497, 0.07114, 0.62425, 0.07539, 0.64016, 0.07168, 0.66681, 0.06546, 0.70428, 0.04838, 0.71458, 0.04369, 0.72902, 0.04782, 0.77355, 0.06501, 0.80245, 0.07616, 0.82078, 0.08324, 0.84508, 0.08208, 0.85163, 0.07249, 0.85885, 0.06189, 0.88428, 0.04998, 0.91298, 0.03653, 0.94705, 0.02776, 0.97961, 0.01937, 0.97065, 0.02613, 0.96214, 0.03308, 0.94803, 0.04462, 0.92656, 0.05358, 0.90393, 0.06302, 0.89464, 0.07045, 0.89301, 0.08258, 0.89106, 0.09711, 0.85815, 0.1039, 0.82469, 0.11106, 0.80041, 0.11625, 0.78687, 0.11915, 0.7758, 0.11617, 0.74196, 0.10706, 0.71458, 0.09969, 0.70284, 0.09653, 0.69279, 0.09922, 0.67439, 0.10414, 0.6641, 0.10969, 0.6449, 0.12004, 0.60054, 0.14395, 0.59057, 0.15174, 0.59647, 0.17052, 0.57706, 0.17506, 0.53242, 0.18551, 0.50077, 0.19292, 0.45185, 0.20437, 0.38593, 0.20698, 0.32733, 0.19585, 0.29314, 0.18233, 0.25867, 0.1687, 0.22793, 0.15079, 0.203, 0.13627, 0.17109, 0.11768, 0.14261, 0.10108, 0.10284, 0.09241, 0.09704, 0.10303, 0.08988, 0.11615, 0.0677, 0.11185, 0.04201, 0.10687, 0.02141, 0.11511, 0.00409, 0.12204, 0.00379, 0.14298, 0.03691, 0.14409, 0.05291, 0.14463, 0.06734, 0.14358, 0.09425, 0.14162, 0.1196, 0.13977, 0.11342, 0.14607, 0.11184, 0.15826, 0.11082, 0.16611, 0.1264, 0.1678, 0.13645, 0.17901, 0.14531, 0.1889, 0.16142, 0.20688, 0.17004, 0.2165, 0.17642, 0.22361, 0.17948, 0.22704, 0.18717, 0.22823, 0.20489, 0.23099, 0.2172, 0.23291, 0.23449, 0.22898, 0.2527, 0.22484, 0.26933, 0.22107, 0.27532, 0.22239, 0.28216, 0.22391, 0.28278, 0.22827, 0.2835, 0.23334, 0.29881, 0.23767, 0.31583, 0.24249, 0.32692, 0.24563, 0.34087, 0.24256, 0.35837, 0.2387, 0.37007, 0.23612, 0.37218, 0.24093, 0.37836, 0.25507, 0.37842, 0.2655, 0.37845, 0.27225, 0.36538, 0.2691, 0.34652, 0.26455, 0.32743, 0.25996, 0.31787, 0.25766, 0.31551, 0.26087, 0.31091, 0.2671, 0.30506, 0.27504, 0.2854, 0.30172, 0.01456, 0.73307, 0.00488, 0.75024, 0.04725, 0.76495, 0.1306, 0.7939, 0.13751, 0.79571, 0.20347, 0.78812, 0.24391, 0.78347, 0.26145, 0.78186, 0.2562, 0.81291, 0.25152, 0.84061, 0.31354, 0.86782, 0.37358, 0.89415, 0.47101, 0.94156, 0.58483, 0.99695, 0.58425, 0.99981, 0.338, 0.99961, 0.17505, 0.99947, 0.03974, 0.99936, 0.01994, 0.99639, 0.00128, 0.94589, 0.00136, 0.87573, 0.00144, 0.80495, 0.00148, 0.76992, 0, 0.74264, 0.02745, 0.71131, 0.29572, 0.28474, 0.27046, 0.27657, 0.25117, 0.27034, 0.23295, 0.26445, 0.20454, 0.25526, 0.19693, 0.24993, 0.16261, 0.25085, 0.12655, 0.25182, 0.124, 0.24189, 0.12061, 0.2287, 0.12108, 0.22714, 0.09258, 0.20782, 0.07425, 0.19539, 0.06577, 0.18965, 0.0515, 0.19064, 0.02843, 0.19224, 0.02127, 0.18801, 0.00179, 0.17652, 0.00172, 0.03366, 0.05392, 0.02723, 0.10963, 0.02036, 0.13223, 0.01823, 0.13793, 0, 0.15105, 0.00173, 0.36154, 0.08666, 0.37676, 0.0978, 0.4072, 0.11077, 0.45104, 0.1185, 0.5101, 0.11327, 0.54906, 0.10099, 0.55576, 0.0878, 0.54115, 0.07392], "triangles": [142, 143, 141, 143, 140, 141, 140, 143, 139, 139, 143, 144, 138, 139, 144, 138, 144, 147, 144, 145, 147, 147, 145, 146, 138, 147, 137, 132, 137, 148, 137, 132, 136, 137, 147, 148, 148, 149, 132, 149, 131, 132, 131, 149, 130, 149, 150, 130, 136, 132, 133, 133, 134, 136, 136, 134, 135, 150, 129, 130, 150, 151, 129, 129, 151, 128, 128, 151, 152, 128, 152, 127, 120, 118, 119, 120, 121, 118, 121, 117, 118, 121, 122, 117, 117, 113, 116, 113, 117, 122, 113, 114, 116, 122, 112, 113, 114, 115, 116, 152, 153, 127, 127, 153, 126, 153, 154, 126, 126, 154, 125, 125, 154, 155, 125, 155, 124, 124, 155, 156, 123, 124, 156, 156, 109, 110, 123, 110, 111, 110, 123, 156, 123, 112, 122, 123, 111, 112, 109, 102, 103, 109, 104, 108, 109, 103, 104, 108, 106, 107, 108, 104, 106, 104, 105, 106, 157, 102, 156, 109, 156, 102, 157, 158, 102, 159, 100, 158, 158, 101, 102, 158, 100, 101, 160, 161, 159, 159, 99, 100, 159, 161, 99, 161, 162, 99, 99, 163, 98, 163, 99, 162, 163, 97, 98, 163, 96, 97, 163, 164, 96, 164, 95, 96, 95, 165, 94, 95, 164, 165, 93, 94, 92, 92, 94, 166, 166, 94, 165, 168, 169, 167, 166, 167, 170, 92, 166, 170, 92, 170, 86, 92, 86, 91, 91, 86, 87, 169, 170, 167, 170, 85, 86, 170, 84, 85, 84, 170, 83, 91, 87, 88, 91, 88, 90, 90, 88, 89, 37, 38, 46, 46, 38, 45, 38, 39, 45, 45, 39, 44, 39, 40, 44, 44, 40, 43, 43, 40, 42, 41, 42, 40, 48, 36, 47, 36, 37, 47, 47, 37, 46, 50, 34, 35, 50, 51, 34, 50, 35, 49, 49, 35, 48, 35, 36, 48, 53, 54, 52, 52, 54, 51, 51, 54, 34, 54, 55, 34, 55, 33, 34, 55, 56, 33, 56, 32, 33, 56, 57, 32, 183, 26, 60, 60, 26, 59, 26, 27, 59, 59, 27, 58, 183, 25, 26, 58, 27, 57, 31, 32, 28, 28, 29, 31, 29, 30, 31, 27, 28, 57, 32, 57, 28, 63, 181, 62, 181, 182, 62, 62, 182, 61, 61, 182, 60, 181, 180, 14, 181, 14, 182, 182, 183, 60, 182, 14, 183, 183, 14, 184, 183, 184, 25, 14, 15, 184, 184, 15, 23, 18, 22, 17, 18, 21, 22, 16, 23, 15, 184, 24, 25, 184, 23, 24, 22, 23, 16, 22, 16, 17, 21, 19, 20, 21, 18, 19, 67, 68, 180, 66, 67, 180, 66, 180, 63, 65, 66, 63, 65, 63, 64, 181, 63, 180, 69, 70, 68, 180, 68, 70, 180, 70, 71, 71, 179, 180, 179, 71, 72, 178, 179, 73, 177, 178, 6, 6, 178, 73, 72, 73, 179, 73, 74, 6, 6, 75, 5, 6, 74, 75, 180, 179, 14, 13, 179, 178, 179, 13, 14, 13, 178, 12, 12, 9, 11, 177, 9, 12, 9, 10, 11, 12, 178, 177, 9, 177, 8, 6, 7, 177, 177, 7, 8, 75, 76, 5, 3, 76, 77, 76, 4, 5, 76, 3, 4, 2, 77, 173, 3, 77, 2, 2, 174, 1, 1, 174, 0, 173, 174, 2, 174, 176, 0, 174, 175, 176, 77, 172, 173, 83, 170, 171, 83, 171, 82, 79, 80, 78, 82, 171, 81, 80, 81, 78, 78, 81, 77, 77, 81, 171, 171, 172, 77], "vertices": [2, 235, 83.62, 43.65, 0.06692, 236, 49.8, 43.65, 0.93308, 2, 235, 92.33, 26.73, 0.02251, 236, 58.51, 26.73, 0.97749, 2, 236, 64.6, 4.17, 0.76582, 107, -4.53, 40.99, 0.23418, 2, 236, 69.3, -13.23, 0.40231, 107, 13.27, 38.2, 0.59769, 5, 236, 76.21, -38.77, 0.09253, 107, 39.41, 34.11, 0.86276, 229, -26.69, 34.11, 0.0424, 108, 29.75, 163.33, 0.00046, 231, -38.24, 163.33, 0.00184, 5, 236, 80.99, -56.45, 0.01744, 107, 57.51, 31.28, 0.65841, 229, -8.59, 31.28, 0.30558, 108, 26.34, 145.33, 0.00521, 231, -41.66, 145.33, 0.01336, 6, 236, 89.74, -71.55, 0.00073, 107, 74.86, 33.13, 0.17579, 229, 8.76, 33.13, 0.7387, 108, 27.62, 127.93, 0.02581, 231, -40.37, 127.93, 0.05843, 232, -108.37, 127.93, 0.00054, 6, 107, 76.36, 52.82, 0.02767, 229, 10.27, 52.82, 0.75022, 230, -55.83, 52.82, 0.00562, 108, 47.25, 125.78, 0.05933, 231, -20.74, 125.78, 0.15242, 232, -88.73, 125.78, 0.00474, 6, 107, 77.2, 63.79, 0.00634, 229, 11.1, 63.79, 0.71094, 230, -54.99, 63.79, 0.00653, 108, 58.19, 124.59, 0.06893, 231, -9.8, 124.59, 0.19837, 232, -77.8, 124.59, 0.00888, 6, 107, 78.66, 82.86, 0.00013, 229, 12.56, 82.86, 0.63725, 230, -53.54, 82.86, 0.00575, 108, 77.2, 122.51, 0.07509, 231, 9.2, 122.51, 0.26355, 232, -58.79, 122.51, 0.01822, 5, 229, 13.68, 97.49, 0.6021, 230, -52.42, 97.49, 0.00659, 108, 91.79, 120.92, 0.07708, 231, 23.8, 120.92, 0.29043, 232, -44.2, 120.92, 0.0238, 5, 229, 23.43, 98.53, 0.58536, 230, -42.66, 98.53, 0.0081, 108, 92.51, 111.14, 0.0779, 231, 24.51, 111.14, 0.30105, 232, -43.48, 111.14, 0.02759, 5, 229, 33.72, 99.62, 0.5296, 230, -32.37, 99.62, 0.01227, 108, 93.26, 100.82, 0.07933, 231, 25.27, 100.82, 0.33742, 232, -42.72, 100.82, 0.04138, 5, 229, 46.16, 100.93, 0.42398, 230, -19.94, 100.93, 0.01776, 108, 94.18, 88.35, 0.07873, 231, 26.18, 88.35, 0.40899, 232, -41.81, 88.35, 0.07055, 5, 229, 56.43, 102.02, 0.30522, 230, -9.67, 102.02, 0.01983, 108, 94.93, 78.05, 0.07236, 231, 26.94, 78.05, 0.49226, 232, -41.06, 78.05, 0.11033, 5, 229, 49.47, 107.61, 0.22675, 230, -16.63, 107.61, 0.01043, 108, 100.74, 84.82, 0.04154, 231, 32.75, 84.82, 0.54283, 232, -35.25, 84.82, 0.17845, 5, 229, 32.69, 121.08, 0.15029, 230, -33.4, 121.08, 0.00181, 108, 114.75, 101.15, 0.01033, 231, 46.76, 101.15, 0.57379, 232, -21.24, 101.15, 0.26377, 5, 229, 14.09, 136.03, 0.12438, 230, -52.01, 136.03, 7e-05, 108, 130.29, 119.26, 0.00159, 231, 62.3, 119.26, 0.57426, 232, -5.7, 119.26, 0.29971, 3, 229, 4.83, 161.91, 0.11242, 231, 88.47, 127.67, 0.5704, 232, 20.47, 127.67, 0.31718, 4, 229, -0.42, 176.6, 0.11079, 108, 171.32, 132.44, 0, 231, 103.32, 132.44, 0.57027, 232, 35.33, 132.44, 0.31893, 4, 229, 2.68, 179.17, 0.11087, 108, 173.79, 129.26, 0, 231, 105.79, 129.26, 0.57031, 232, 37.8, 129.26, 0.31882, 3, 229, 12.98, 175.39, 0.11148, 231, 101.68, 119.09, 0.56984, 232, 33.68, 119.09, 0.31867, 4, 229, 29.76, 169.23, 0.11203, 108, 162.97, 102.52, 1e-05, 231, 94.98, 102.52, 0.56375, 232, 26.98, 102.52, 0.32421, 3, 229, 33.5, 170.24, 0.11097, 231, 95.86, 98.75, 0.5603, 232, 27.87, 98.75, 0.32873, 3, 229, 69.81, 169.62, 0.08701, 231, 94.06, 62.48, 0.50268, 232, 26.07, 62.48, 0.41031, 3, 229, 95.63, 169.18, 0.04548, 231, 92.78, 36.68, 0.34722, 232, 24.79, 36.68, 0.60731, 3, 229, 103.6, 169.04, 0.026, 231, 92.39, 28.73, 0.24214, 232, 24.39, 28.73, 0.73187, 3, 229, 105.03, 179.11, 0.01311, 231, 102.4, 26.96, 0.1211, 232, 34.41, 26.96, 0.86578, 3, 229, 107.44, 195.97, 0.00333, 231, 119.18, 24.01, 0.03027, 232, 51.18, 24.01, 0.9664, 5, 229, 101.59, 227.47, 2e-05, 231, 150.85, 28.84, 0.00014, 232, 82.85, 28.84, 0.99916, 110, 10.72, 95.16, 0.00068, 237, 43.73, 395.23, 0, 4, 232, 91.56, 30.17, 0.99458, 109, -41.31, 23.14, 0.00458, 110, 19.48, 94.29, 0.00085, 237, 41.45, 403.74, 0, 4, 232, 93.37, 20.48, 0.94273, 109, -31.47, 23.83, 0.05528, 110, 18.83, 84.45, 0.00199, 237, 50.88, 406.61, 0, 4, 232, 94.62, -14.2, 0.02681, 109, 3.12, 21.09, 0.93515, 110, 11.47, 50.54, 0.03804, 237, 85.21, 411.69, 0, 3, 109, 25.58, 19.32, 0.80306, 110, 6.69, 28.52, 0.19694, 237, 107.49, 414.99, 0, 3, 109, 39.82, 18.2, 0.45783, 110, 3.66, 14.57, 0.54217, 237, 121.62, 417.08, 0, 3, 109, 48.59, 28.17, 0.02875, 110, 12.36, 4.54, 0.97125, 237, 127.94, 428.76, 0, 4, 109, 42.03, 40.96, 9e-05, 110, 25.92, 9.31, 0.18196, 233, 2.49, 9.31, 0.81795, 237, 118.69, 439.76, 0, 3, 233, 17.45, 14.58, 0.8003, 234, -5.98, 14.58, 0.1997, 237, 108.48, 451.9, 0, 5, 109, 33.7, 77.15, 0, 110, 62.9, 12.7, 1e-05, 233, 39.48, 12.7, 0.01209, 234, 16.05, 12.7, 0.98791, 237, 102.46, 473.17, 0, 2, 110, 87.77, 10.58, 0, 234, 40.92, 10.58, 1, 2, 233, 85.18, 2.28, 0.00045, 234, 61.75, 2.28, 0.99955, 4, 109, 43.05, 144.64, 0, 110, 128.52, -5.66, 0, 233, 105.09, -5.66, 0.00054, 234, 81.66, -5.66, 0.99946, 4, 109, 45.9, 134.08, 0, 110, 117.67, -7.05, 0, 233, 94.24, -7.05, 0.00052, 234, 70.81, -7.05, 0.99949, 2, 233, 83.29, -8.82, 0.00045, 234, 59.86, -8.82, 0.99955, 2, 233, 65.11, -11.74, 0.00047, 234, 41.68, -11.74, 0.99953, 1, 234, 24.42, -9.27, 1, 2, 233, 29.65, -6.66, 0.07686, 234, 6.22, -6.66, 0.92314, 2, 233, 17.88, -8.45, 0.93042, 234, -5.55, -8.45, 0.06958, 2, 110, 26.18, -17.48, 0.42128, 233, 2.75, -17.48, 0.57872, 3, 110, 8.04, -28.3, 0.94215, 233, -15.39, -28.3, 0.05663, 234, -38.82, -28.3, 0.00122, 3, 109, 74.84, 9.11, 0.17828, 110, -10.05, -18.92, 0.82118, 234, -56.91, -18.92, 0.00053, 4, 232, 70.52, -82.42, 0.00242, 109, 68.12, -10.67, 0.7659, 110, -28.75, -9.59, 0.23159, 234, -75.61, -9.59, 0.0001, 4, 232, 55.7, -79.21, 0.01407, 109, 63.23, -25.03, 0.93869, 110, -42.33, -2.82, 0.04722, 234, -89.18, -2.82, 2e-05, 4, 232, 47.43, -77.43, 0.02159, 109, 60.51, -33.04, 0.96385, 110, -49.89, 0.95, 0.01456, 234, -96.75, 0.95, 1e-05, 4, 232, 45.86, -70.21, 0.03151, 109, 53.16, -33.77, 0.96487, 110, -49.62, 8.34, 0.00362, 234, -96.48, 8.34, 0, 2, 232, 41.07, -48.11, 0.1514, 109, 30.66, -35.99, 0.8486, 2, 232, 37.19, -30.24, 0.43633, 109, 12.46, -37.79, 0.56367, 2, 232, 35.53, -22.57, 0.62689, 109, 4.65, -38.56, 0.37311, 3, 231, 96.87, -21.82, 0.00926, 232, 28.87, -21.82, 0.77804, 109, 3.15, -45.09, 0.2127, 3, 231, 84.68, -20.46, 0.10837, 232, 16.69, -20.46, 0.83508, 109, 0.39, -57.04, 0.05656, 4, 231, 75.15, -22.71, 0.3291, 232, 7.16, -22.71, 0.65054, 109, 1.53, -66.77, 0.02037, 237, 103.33, 325.7, 0, 4, 231, 57.37, -26.91, 0.80239, 232, -10.62, -26.91, 0.19478, 109, 3.67, -84.91, 0.00284, 237, 109.47, 308.49, 0, 3, 108, 84.29, -36.61, 0.31836, 231, 16.29, -36.61, 0.68164, 237, 123.66, 268.74, 0, 3, 108, 72.7, -41.4, 0.60668, 231, 4.71, -41.4, 0.39332, 237, 129.7, 257.75, 0, 3, 108, 56.86, -63.79, 0.87805, 231, -11.14, -63.79, 0.12195, 237, 153.71, 244.48, 0, 3, 108, 44.63, -61.66, 0.91314, 231, -23.37, -61.66, 0.08686, 237, 152.93, 232.09, 0, 4, 230, 127.59, 28.01, 0.01887, 108, 16.5, -56.74, 0.97314, 231, -51.5, -56.74, 0.008, 237, 151.16, 203.59, 0, 3, 230, 124.76, 7.97, 0.1405, 108, -3.44, -53.25, 0.8595, 237, 149.9, 183.39, 0, 2, 230, 120.37, -23.01, 0.5182, 108, -34.26, -47.87, 0.4818, 2, 230, 100.24, -52.78, 0.8889, 108, -63.35, -26.78, 0.1111, 2, 229, 133.53, -66.66, 0.02872, 230, 67.43, -66.66, 0.97128, 2, 229, 106.58, -68.18, 0.12153, 230, 40.49, -68.18, 0.87847, 4, 235, 58.36, -177.94, 9e-05, 107, 145.52, -69.72, 0.00395, 229, 79.42, -69.72, 0.32016, 230, 13.32, -69.72, 0.6758, 4, 235, 49.56, -148.35, 0.00349, 107, 114.91, -65.71, 0.0527, 229, 48.81, -65.71, 0.59793, 230, -17.29, -65.71, 0.34588, 5, 235, 42.43, -124.36, 0.01699, 107, 90.09, -62.46, 0.19985, 229, 23.99, -62.46, 0.65352, 230, -42.11, -62.46, 0.12963, 237, -10.95, 100.12, 0, 7, 106, 67.12, -93.65, 0.00705, 235, 33.3, -93.65, 0.06731, 107, 58.32, -58.3, 0.53425, 229, -7.78, -58.3, 0.37646, 230, -73.88, -58.3, 0.0149, 111, 4.44, 101.78, 3e-05, 237, -42.95, 101.78, 0, 7, 106, 58.97, -66.23, 0.07196, 235, 25.15, -66.23, 0.18801, 107, 29.96, -54.59, 0.65348, 229, -36.14, -54.59, 0.08539, 230, -102.24, -54.59, 0.00013, 111, -24.12, 103.27, 0.00103, 237, -71.51, 103.27, 0, 6, 106, 41.76, -48.15, 0.32726, 235, 7.94, -48.15, 0.33253, 107, 6.44, -62.96, 0.32762, 229, -59.66, -62.96, 0.00649, 111, -46.91, 93.09, 0.00609, 237, -94.3, 93.09, 0, 7, 106, 34.46, -62.09, 0.52988, 235, 0.64, -62.09, 0.24049, 236, -33.18, -62.09, 0.00015, 107, 16.2, -75.3, 0.21487, 229, -49.89, -75.3, 0.00111, 111, -36.21, 81.55, 0.01349, 237, -83.6, 81.55, 0, 6, 106, 25.45, -79.3, 0.61931, 235, -8.37, -79.3, 0.19661, 236, -42.19, -79.3, 0.00026, 107, 28.26, -90.53, 0.16646, 111, -23.01, 67.3, 0.01735, 237, -70.4, 67.3, 0, 6, 106, 15.63, -69.97, 0.6291, 235, -18.19, -69.97, 0.18932, 236, -52, -69.97, 0.00031, 107, 15.74, -95.7, 0.1619, 111, -35.08, 61.17, 0.01936, 237, -82.47, 61.17, 0, 6, 106, 4.26, -59.17, 0.72754, 235, -29.56, -59.17, 0.12633, 236, -63.38, -59.17, 0.00063, 107, 1.25, -101.69, 0.11217, 111, -49.06, 54.06, 0.03333, 237, -96.45, 54.06, 0, 6, 106, -9.79, -67.55, 0.76725, 235, -43.61, -67.55, 0.06888, 236, -77.43, -67.55, 0.00088, 107, 3.19, -117.94, 0.07426, 111, -45.85, 38.02, 0.08872, 237, -93.24, 38.02, 1e-05, 6, 106, -21.59, -74.6, 0.66551, 235, -55.41, -74.6, 0.05077, 236, -89.23, -74.6, 0.0008, 107, 4.82, -131.59, 0.05849, 111, -43.16, 24.54, 0.22442, 237, -90.55, 24.54, 1e-05, 6, 106, -30.18, -103.75, 0.13981, 235, -64, -103.75, 0.00939, 236, -97.82, -103.75, 0.00018, 107, 27.95, -151.3, 0.01146, 111, -18.55, 6.7, 0.83916, 237, -65.94, 6.7, 0, 5, 106, -13.39, -110.28, 0.00879, 235, -47.2, -110.28, 0.00071, 236, -81.02, -110.28, 1e-05, 107, 40.75, -138.61, 0.0008, 111, -6.79, 20.35, 0.98969, 6, 106, -5.27, -113.43, 0.0021, 235, -39.09, -113.43, 0.00022, 236, -72.91, -113.43, 0, 107, 46.94, -132.48, 0.00022, 111, -1.1, 26.94, 0.99498, 237, -48.49, 26.94, 0.00247, 6, 106, 2.67, -114.14, 0.00024, 235, -31.15, -114.14, 6e-05, 236, -64.97, -114.14, 0, 107, 50.81, -125.52, 5e-05, 111, 2.22, 34.18, 0.98973, 237, -45.17, 34.18, 0.00992, 4, 235, -16.35, -115.46, 0, 107, 58.04, -112.54, 0, 111, 8.41, 47.7, 0.97638, 237, -38.98, 47.7, 0.02362, 5, 106, 31.41, -116.7, 0, 236, -36.23, -116.7, 0, 107, 64.84, -100.31, 0, 111, 14.23, 60.42, 0.97447, 237, -33.16, 60.42, 0.02552, 2, 111, 19.71, 52.38, 0.96438, 237, -27.68, 52.38, 0.03562, 2, 111, 33.59, 41.37, 0.82036, 237, -13.8, 41.37, 0.17964, 2, 111, 42.52, 34.29, 0.6184, 237, -4.86, 34.29, 0.3816, 3, 111, 49.43, 39.72, 0.46027, 237, 2.04, 39.72, 0.53836, 238, -45.35, 39.72, 0.00137, 3, 111, 65.83, 34.67, 0.16146, 237, 18.44, 34.67, 0.8198, 238, -28.95, 34.67, 0.01874, 3, 111, 80.29, 30.21, 0.01945, 237, 32.9, 30.21, 0.86753, 238, -14.49, 30.21, 0.11302, 2, 237, 59.19, 22.1, 0.24196, 238, 11.8, 22.1, 0.75804, 2, 237, 73.26, 17.77, 0.01428, 238, 25.87, 17.77, 0.98572, 2, 238, 36.27, 14.56, 0.94463, 112, -2.15, 27.27, 0.05537, 2, 238, 41.27, 13.02, 0.86826, 112, -1.55, 22.07, 0.13174, 2, 238, 45.11, 15.4, 0.69638, 112, 2.17, 19.51, 0.30362, 2, 238, 53.96, 20.87, 0.24809, 112, 10.74, 13.62, 0.75191, 2, 238, 60.1, 24.67, 0.06333, 112, 16.7, 9.53, 0.93667, 2, 238, 60.93, 35.61, 0.00621, 112, 27.04, 13.17, 0.99379, 1, 112, 37.94, 17.01, 1, 2, 238, 62.61, 57.64, 2e-05, 112, 47.89, 20.51, 0.99998, 2, 238, 66.06, 59.16, 1e-05, 112, 50.67, 17.96, 0.99999, 3, 238, 70.01, 60.89, 0, 112, 53.84, 15.05, 0.99358, 113, -17.62, 15.99, 0.00642, 3, 238, 75.36, 57.47, 0, 112, 52.87, 8.78, 0.94876, 113, -13.2, 11.44, 0.05124, 3, 238, 81.56, 53.51, 0, 112, 51.73, 1.5, 0.65138, 113, -8.06, 6.16, 0.34862, 2, 113, 2.32, 6.9, 0.99871, 115, -13.18, 23.85, 0.00129, 2, 113, 13.86, 7.71, 0.91034, 115, -5.69, 15.03, 0.08966, 2, 113, 21.39, 8.25, 0.56413, 115, -0.81, 9.28, 0.43587, 2, 113, 24.14, 16.58, 0.15937, 115, 7.54, 11.99, 0.84063, 2, 113, 27.6, 27.04, 0.05284, 115, 18.01, 15.4, 0.94716, 2, 113, 29.9, 34.03, 0.03516, 115, 25.01, 17.68, 0.96484, 2, 113, 35.36, 29.53, 0.02673, 115, 24.61, 10.61, 0.97327, 1, 115, 23.44, -10.13, 1, 1, 115, 20.18, -24.91, 1, 1, 115, 18.08, -34.48, 1, 2, 113, 59.5, -3.65, 0.00045, 115, 12.15, -28.48, 0.99955, 3, 113, 47.47, -5.43, 0.03152, 115, 3.6, -19.83, 0.96848, 240, 684.82, -581.27, 0, 4, 112, 67.08, -41.19, 0.00225, 113, 35.29, -7.22, 0.31952, 115, -5.05, -11.08, 0.67823, 240, 695.96, -576.03, 1e-05, 4, 112, 62.69, -36.86, 0.01402, 113, 29.19, -8.12, 0.94092, 115, -9.38, -6.69, 0.04503, 240, 701.53, -573.41, 3e-05, 3, 112, 60.48, -41.15, 0.03346, 113, 31.29, -12.47, 0.96646, 240, 698.26, -569.85, 8e-05, 3, 112, 56.18, -49.49, 0.05559, 113, 35.38, -20.92, 0.9442, 240, 691.91, -562.94, 0.00021, 3, 112, 50.7, -60.11, 0.0617, 113, 40.57, -31.67, 0.93783, 240, 683.83, -554.15, 0.00047, 3, 112, 32.3, -95.79, 0.03251, 113, 58.04, -67.82, 0.92975, 240, 656.65, -524.6, 0.03774, 1, 240, 203.7, -68.4, 1, 1, 240, 185.34, -50.74, 1, 1, 240, 155.07, -58.9, 1, 2, 240, 95.51, -74.95, 0.98681, 239, 184.06, -74.95, 0.01328, 2, 240, 91.3, -76.73, 0.98068, 239, 179.84, -76.73, 0.01939, 3, 112, -156.55, -777.28, 0, 240, 81.68, -112.88, 0.9459, 239, 170.23, -112.88, 0.05411, 3, 112, -133.71, -775.19, 1e-05, 240, 75.79, -135.04, 0.94216, 239, 164.34, -135.04, 0.05783, 3, 112, -123.93, -774.86, 1e-05, 240, 72.74, -144.35, 0.94292, 239, 161.29, -144.35, 0.05708, 4, 112, -135.99, -818.36, 0, 240, 36.04, -118.06, 0.87987, 239, 124.59, -118.06, 0.12001, 105, 213.14, -118.06, 0.00013, 4, 112, -146.75, -857.17, 0, 240, 3.3, -94.61, 0.60628, 239, 91.85, -94.61, 0.37129, 105, 180.4, -94.61, 0.02243, 5, 112, -121.99, -902.72, 0, 113, 610.42, -675.95, 0, 240, -47.99, -102.19, 0.15251, 239, 40.56, -102.19, 0.60995, 105, 129.1, -102.19, 0.23754, 5, 112, -98.02, -946.83, 0, 113, 660.08, -683.29, 0, 240, -97.64, -109.53, 0.01608, 239, -9.1, -109.53, 0.36259, 105, 79.45, -109.53, 0.62134, 4, 112, -60.52, -1025.02, 0, 113, 745.12, -700.28, 0, 239, -95.42, -117.85, 0.0273, 105, -6.88, -117.85, 0.9727, 3, 112, -16.71, -1116.37, 0, 113, 844.47, -720.14, 0, 105, -107.72, -127.57, 1, 3, 112, -17.87, -1120.36, 0, 113, 846.96, -723.47, 0, 105, -111.07, -125.1, 1, 3, 112, -148.42, -1092.58, 0, 113, 746.32, -811.14, 0, 105, -40.08, -12.08, 1, 2, 239, -81.66, 62.71, 0.09569, 105, 6.89, 62.71, 0.90431, 3, 113, 624.43, -917.32, 0, 239, -42.65, 124.82, 0.34274, 105, 45.9, 124.82, 0.65726, 3, 113, 613.51, -921.14, 0, 239, -33.3, 131.63, 0.35451, 105, 55.25, 131.63, 0.64549, 4, 113, 557.65, -872.66, 0, 240, -54.36, 101.38, 0.01126, 239, 34.19, 101.38, 0.64388, 105, 122.73, 101.38, 0.34486, 4, 113, 490.65, -796.01, 0, 240, 31.95, 47.38, 0.81841, 239, 120.5, 47.38, 0.1798, 105, 209.04, 47.38, 0.00179, 1, 240, 119.01, -7.08, 1, 1, 240, 162.11, -34.05, 1, 1, 240, 196.1, -54.34, 1, 1, 240, 226.76, -91.05, 1, 3, 112, 42.85, -72.84, 0.05721, 113, 46.03, -45.6, 0.94121, 240, 674.57, -542.4, 0.00159, 3, 112, 31.9, -58.43, 0.12653, 113, 27.93, -45.7, 0.87325, 240, 691.87, -537.07, 0.00022, 3, 112, 23.53, -47.42, 0.26946, 113, 14.1, -45.77, 0.73045, 240, 705.09, -533, 0.0001, 3, 112, 15.62, -37.02, 0.46424, 113, 1.04, -45.84, 0.53572, 240, 717.57, -529.15, 4e-05, 3, 112, 3.3, -20.81, 0.80114, 113, -19.32, -45.96, 0.19886, 240, 737.03, -523.15, 1e-05, 3, 112, 0.86, -12.39, 0.92623, 113, -27.52, -42.85, 0.07377, 240, 745.78, -523.76, 0, 3, 238, 64.03, -14.55, 0.72353, 112, -17.62, -9.86, 0.27455, 113, -40.64, -56.1, 0.00192, 2, 238, 53.78, -31.25, 0.96618, 112, -37.03, -7.21, 0.03382, 1, 238, 41.26, -23.98, 1, 1, 238, 24.64, -14.32, 1, 1, 238, 22.95, -12.79, 1, 2, 237, 38.55, -9.01, 0.85709, 238, -8.84, -9.01, 0.14291, 1, 237, 18.12, -6.58, 1, 2, 111, 56.05, -5.46, 0.20742, 237, 8.66, -5.46, 0.79258, 2, 111, 52.71, -12.58, 0.55535, 237, 5.32, -12.58, 0.44465, 2, 111, 47.32, -24.1, 0.83476, 237, -0.07, -24.1, 0.16524, 2, 111, 40.07, -23.68, 0.92783, 237, -7.32, -23.68, 0.07217, 1, 111, 20.37, -22.54, 1, 6, 106, 12.76, 48.95, 0.68747, 235, -21.06, 48.95, 0.23402, 236, -54.87, 48.95, 0.07808, 107, -94.06, -49.93, 0.00035, 111, -148.12, 98.21, 8e-05, 237, -195.51, 98.21, 0, 6, 106, 42.54, 50.07, 0.36331, 235, 8.72, 50.07, 0.38689, 236, -25.1, 50.07, 0.24967, 107, -82.96, -22.28, 0.0001, 111, -139.22, 126.65, 2e-05, 237, -186.61, 126.65, 0, 6, 106, 74.3, 51.26, 0.04823, 235, 40.49, 51.26, 0.29016, 236, 6.67, 51.26, 0.6616, 107, -71.12, 7.22, 1e-05, 111, -129.72, 156.98, 0, 237, -177.11, 156.98, 0, 5, 106, 86.94, 50.84, 0.01008, 235, 53.12, 50.84, 0.18598, 236, 19.3, 50.84, 0.80394, 111, -125.11, 168.75, 0, 237, -172.5, 168.75, 0, 2, 235, 63.42, 75.39, 0.11187, 236, 29.6, 75.39, 0.88813, 2, 235, 69.56, 71, 0.11426, 236, 35.74, 71, 0.88574, 6, 107, 90.43, 49.63, 0.01841, 229, 24.33, 49.63, 0.75346, 230, -41.77, 49.63, 0.01385, 108, 43.61, 111.83, 0.06403, 231, -24.39, 111.83, 0.14589, 232, -92.38, 111.83, 0.00436, 6, 107, 108.11, 45.52, 1e-05, 229, 42.01, 45.52, 0.70234, 230, -24.09, 45.52, 0.06039, 108, 38.92, 94.29, 0.09352, 231, -29.07, 94.29, 0.14011, 232, -97.07, 94.29, 0.00363, 5, 229, 67.03, 46.01, 0.46374, 230, 0.93, 46.01, 0.18481, 108, 38.6, 69.27, 0.19515, 231, -29.39, 69.27, 0.15294, 232, -97.39, 69.27, 0.00336, 5, 229, 90.92, 56.95, 0.2046, 230, 24.82, 56.95, 0.17968, 108, 48.76, 45.04, 0.41261, 231, -19.24, 45.04, 0.19903, 232, -87.23, 45.04, 0.00408, 5, 229, 105.74, 86.31, 0.10046, 230, 39.65, 86.31, 0.03449, 108, 77.63, 29.27, 0.23521, 231, 9.63, 29.27, 0.61989, 232, -58.36, 29.27, 0.00996, 5, 229, 105.73, 113.95, 0.06908, 230, 39.63, 113.95, 0.00796, 108, 105.25, 28.39, 0.02325, 231, 37.25, 28.39, 0.83675, 232, -30.74, 28.39, 0.06296, 5, 229, 93.43, 129.06, 0.0835, 230, 27.34, 129.06, 0.00343, 108, 120.75, 40.18, 0.00866, 231, 52.76, 40.18, 0.67225, 232, -15.24, 40.18, 0.23216, 5, 229, 72.94, 135.98, 0.11477, 230, 6.84, 135.98, 0.00223, 108, 128.33, 60.44, 0.00888, 231, 60.34, 60.44, 0.58097, 232, -7.66, 60.44, 0.29315], "hull": 177, "edges": [10, 12, 38, 40, 44, 46, 60, 62, 68, 70, 82, 84, 92, 94, 98, 100, 124, 126, 126, 128, 136, 138, 138, 140, 152, 154, 166, 168, 178, 180, 184, 186, 254, 256, 256, 258, 262, 264, 268, 270, 282, 284, 290, 292, 292, 294, 300, 302, 302, 304, 304, 306, 314, 316, 324, 326, 340, 342, 346, 348, 348, 350, 350, 352, 274, 276, 276, 278, 278, 280, 280, 282, 284, 286, 286, 288, 288, 290, 294, 296, 296, 298, 298, 300, 258, 260, 260, 262, 270, 272, 272, 274, 264, 266, 266, 268, 342, 344, 344, 346, 122, 124, 46, 48, 62, 64, 100, 102, 74, 76, 76, 78, 88, 90, 90, 92, 94, 96, 96, 98, 70, 72, 72, 74, 84, 86, 86, 88, 78, 80, 80, 82, 64, 66, 66, 68, 106, 108, 108, 110, 102, 104, 104, 106, 56, 58, 58, 60, 110, 112, 112, 114, 114, 116, 116, 118, 52, 54, 54, 56, 48, 50, 50, 52, 118, 120, 120, 122, 40, 42, 42, 44, 34, 36, 36, 38, 32, 34, 24, 26, 26, 28, 28, 30, 30, 32, 20, 22, 22, 24, 16, 18, 18, 20, 12, 14, 14, 16, 2, 4, 4, 6, 6, 8, 8, 10, 2, 0, 0, 352, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 148, 150, 150, 152, 144, 146, 146, 148, 140, 142, 142, 144, 132, 134, 134, 136, 128, 130, 130, 132, 190, 192, 326, 328, 320, 322, 322, 324, 316, 318, 318, 320, 312, 314, 306, 308, 250, 252, 252, 254, 224, 226, 222, 224, 218, 220, 220, 222, 214, 216, 216, 218, 204, 206, 202, 204, 206, 208, 208, 210, 192, 194, 186, 188, 188, 190, 176, 178, 172, 174, 174, 176, 168, 170, 170, 172, 180, 182, 182, 184, 332, 334, 334, 336, 328, 330, 330, 332, 336, 338, 338, 340, 194, 196, 196, 198, 198, 200, 200, 202, 210, 212, 212, 214, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 308, 310, 310, 312], "width": 325, "height": 870}}, "youshangbi": {"youshangbi": {"type": "mesh", "uvs": [0.54672, 0.26327, 0.5885, 0.17404, 0.63341, 0.13317, 0.73331, 0.4112, 0.77912, 0.41247, 0.92949, 0.49325, 1, 0.72387, 1, 0.82947, 0.93222, 0.92701, 0.77448, 1, 0.76585, 1, 0.53287, 0.73649, 0.53347, 0.58688, 0.42953, 0.51681, 0.25302, 0.42624, 0.15736, 0.37716, 0.10229, 0.3489, 0.03758, 0.3157, 0.01755, 0.25421, 0, 0.20031, 0, 0.16545, 0.01547, 0.06555, 0.06719, 0.03533, 0.12502, 0.00153, 0.2355, 0.0073], "triangles": [22, 19, 20, 21, 22, 20, 18, 19, 22, 16, 17, 18, 22, 24, 18, 24, 16, 18, 23, 24, 22, 14, 15, 24, 15, 16, 24, 1, 2, 3, 0, 1, 3, 0, 14, 24, 13, 14, 0, 12, 13, 0, 3, 12, 0, 12, 4, 5, 7, 8, 6, 6, 12, 5, 4, 12, 3, 8, 10, 6, 6, 11, 12, 10, 11, 6, 9, 10, 8], "vertices": [101.94, 41.68, 98.48, 61.67, 101.42, 74.2, 152.66, 41.97, 160.86, 47.74, 197.07, 54.24, 237.4, 25.88, 250.19, 8.68, 250.09, -16.07, 231.21, -48.57, 229.7, -49.7, 156.83, -37.22, 138.82, -12.77, 112.06, -14.94, 70.07, -23.25, 47.31, -27.75, 34.21, -30.35, 18.82, -33.39, 7.85, -26, -1.76, -19.51, -5.98, -13.83, -15.37, 4.46, -9.94, 16.15, -3.87, 29.21, 16.25, 42.71], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 38, 40, 40, 42, 46, 48, 32, 34, 30, 32, 34, 36, 36, 38, 42, 44, 44, 46, 26, 28, 28, 30], "width": 219, "height": 203}}, "tui7": {"tui7": {"type": "mesh", "uvs": [0.2012, 0.00744, 0.23986, 0.01437, 0.28559, 0.05202, 0.33092, 0.08933, 0.37114, 0.12244, 0.46461, 0.19304, 0.58185, 0.28158, 0.65573, 0.3686, 0.72989, 0.45593, 0.80491, 0.57467, 0.87765, 0.68981, 0.91734, 0.75264, 0.9539, 0.8105, 0.99055, 0.96068, 0.99104, 0.98446, 0.96639, 0.9883, 0.86959, 0.88003, 0.7673, 0.76563, 0.64634, 0.63033, 0.50649, 0.47391, 0.42336, 0.38093, 0.34223, 0.29656, 0.27531, 0.22695, 0.25727, 0.20819, 0.22363, 0.21846, 0.16399, 0.23669, 0.09732, 0.25706, 0.01259, 0.23486, 0.00398, 0.20454, 0.06574, 0.15392, 0.13009, 0.10117, 0.14579, 0.06527, 0.16034, 0.03203, 0.17328, 0.00244], "triangles": [15, 13, 14, 15, 16, 13, 16, 12, 13, 16, 11, 12, 16, 17, 11, 17, 10, 11, 17, 9, 10, 17, 18, 9, 18, 8, 9, 18, 19, 8, 19, 7, 8, 19, 20, 7, 20, 6, 7, 20, 5, 6, 20, 21, 5, 21, 22, 5, 22, 4, 5, 22, 23, 4, 23, 3, 4, 23, 2, 3, 25, 26, 29, 29, 26, 28, 29, 30, 25, 25, 30, 24, 2, 24, 30, 2, 30, 31, 2, 31, 32, 32, 33, 0, 26, 27, 28, 23, 24, 2, 32, 1, 2, 0, 1, 32], "vertices": [1, 93, 30.42, 14.84, 1, 1, 93, 33.64, 11.24, 1, 2, 93, 34.88, 3.43, 0.78014, 94, -4.37, 8.49, 0.21986, 2, 93, 36.11, -4.32, 0.08906, 94, 3.43, 9.36, 0.91094, 1, 94, 10.34, 10.13, 1, 1, 94, 25.67, 12.51, 1, 1, 94, 44.9, 15.5, 1, 1, 94, 60.66, 14.49, 1, 1, 94, 76.47, 13.47, 1, 1, 94, 96.02, 9.63, 1, 1, 94, 114.97, 5.9, 1, 1, 94, 125.31, 3.87, 1, 1, 94, 134.84, 2, 1, 1, 94, 155.14, -8.43, 1, 1, 94, 157.95, -10.59, 1, 1, 94, 156.52, -13.3, 1, 1, 94, 136.55, -12.5, 1, 1, 94, 115.44, -11.67, 1, 1, 94, 90.47, -10.67, 1, 1, 94, 61.61, -9.53, 1, 1, 94, 44.46, -8.85, 1, 1, 94, 28.46, -8.77, 1, 2, 93, 18.61, -16.98, 0.0834, 94, 15.26, -8.71, 0.9166, 2, 93, 18.47, -13.42, 0.27419, 94, 11.7, -8.69, 0.72581, 2, 93, 14.24, -12.26, 0.63737, 94, 10.34, -12.86, 0.36263, 2, 93, 6.75, -10.21, 0.93665, 94, 7.94, -20.24, 0.06335, 2, 93, -1.62, -7.91, 0.99911, 94, 5.26, -28.5, 0.00089, 1, 93, -8.07, 0.82, 1, 1, 93, -6.28, 5.1, 1, 1, 93, 4.25, 6.81, 1, 1, 93, 15.21, 8.59, 1, 1, 93, 19.9, 11.8, 1, 1, 93, 24.23, 14.78, 1, 1, 93, 28.09, 17.43, 1], "hull": 34, "edges": [24, 26, 26, 28, 28, 30, 52, 54, 54, 56, 64, 66, 2, 0, 0, 66, 2, 4, 60, 62, 62, 64, 46, 48, 44, 46, 40, 42, 42, 44, 48, 50, 50, 52, 56, 58, 58, 60, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 38, 40, 16, 18, 36, 38, 18, 20, 34, 36, 30, 32, 32, 34, 20, 22, 22, 24], "width": 122, "height": 149}}, "zhizhusi1": {"zhizhusi1": {"type": "mesh", "uvs": [0.17685, 0.00535, 0.18261, 0.03423, 0.24748, 0.06224, 0.53871, 0.05823, 0.57751, 0.20483, 0.60318, 0.30183, 0.6346, 0.42052, 0.72753, 0.57349, 0.84192, 0.76177, 0.91363, 0.87981, 0.94377, 0.92941, 0.94171, 0.99661, 0.87981, 0.99981, 0.79459, 0.98705, 0.79254, 0.93374, 0.78952, 0.85489, 0.66828, 0.70116, 0.54147, 0.54037, 0.47061, 0.46545, 0.38179, 0.37153, 0.18126, 0.19536, 0.01382, 0.04826, 0.05829, 0.03053, 0.05829, 0.00793], "triangles": [15, 8, 9, 14, 15, 9, 14, 9, 10, 13, 14, 10, 11, 13, 10, 12, 13, 11, 7, 17, 18, 16, 17, 7, 7, 8, 16, 15, 16, 8, 22, 23, 0, 1, 22, 0, 21, 22, 1, 21, 1, 2, 20, 21, 2, 20, 2, 3, 4, 20, 3, 5, 19, 20, 5, 20, 4, 19, 5, 6, 18, 19, 6, 7, 18, 6], "vertices": [2, 52, -26.34, 1.93, 1, 53, -227.67, 1.93, 0, 2, 52, -13.36, 1.25, 1, 53, -214.69, 1.25, 0, 1, 52, -0.64, 2.59, 1, 1, 52, -1.77, 12.6, 1, 2, 52, 64.14, 9.44, 1, 53, -137.19, 9.44, 0, 1, 52, 107.75, 7.35, 1, 1, 52, 161.11, 4.8, 1, 1, 53, 28.67, 3.29, 1, 1, 53, 113.46, 1.42, 1, 1, 54, 32.4, 0.26, 1, 2, 52, 390.3, -0.23, 0, 54, 54.74, -0.23, 1, 2, 52, 420.46, -2.35, 0, 54, 84.91, -2.35, 1, 2, 52, 421.76, -4.55, 0, 54, 86.2, -4.55, 1, 2, 52, 415.83, -7.05, 0, 54, 80.28, -7.05, 1, 2, 52, 391.9, -5.5, 0, 54, 56.34, -5.5, 1, 2, 52, 356.48, -3.19, 0, 54, 20.93, -3.19, 1, 2, 52, 287.19, -2.62, 0, 53, 85.85, -2.62, 1, 1, 53, 13.38, -2.02, 1, 1, 52, 180.91, -2.13, 1, 1, 52, 138.54, -2.28, 1, 1, 52, 58.98, -3.71, 1, 2, 52, -7.45, -4.91, 1, 53, -208.78, -4.91, 0, 2, 52, -15.31, -2.86, 1, 53, -216.64, -2.86, 0, 2, 52, -25.45, -2.17, 1, 53, -226.79, -2.17, 0], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 20, 22, 22, 24, 24, 26, 42, 44, 44, 46, 38, 40, 40, 42, 6, 8, 8, 10, 10, 12, 34, 36, 36, 38, 12, 14, 14, 16, 30, 32, 32, 34, 26, 28, 28, 30, 16, 18, 18, 20], "width": 34, "height": 450}}, "zhizhusi2": {"zhizhusi2": {"type": "mesh", "uvs": [0.9249, 0.00149, 0.92969, 0.00412, 0.93244, 0.01257, 0.93298, 0.02825, 0.93659, 0.0392, 0.95337, 0.02673, 0.95532, 0.0315, 0.93955, 0.05025, 0.92246, 0.07056, 0.94248, 0.06251, 0.95347, 0.05809, 0.95176, 0.05292, 0.96709, 0.07562, 0.96179, 0.08899, 0.95362, 0.1096, 0.94755, 0.12493, 0.94058, 0.14251, 0.96095, 0.13419, 0.97997, 0.12642, 0.97771, 0.11182, 0.9751, 0.09496, 0.99326, 0.0936, 0.99881, 0.10197, 0.99878, 0.11411, 0.99288, 0.1188, 0.98765, 0.12488, 0.97821, 0.13587, 0.98304, 0.14086, 0.9913, 0.14655, 0.9976, 0.1509, 0.99754, 0.16837, 0.99284, 0.17463, 0.98995, 0.1747, 0.98997, 0.16576, 0.98165, 0.16416, 0.98656, 0.14583, 0.97741, 0.13548, 0.9551, 0.15229, 0.92481, 0.16792, 0.91279, 0.17412, 0.8987, 0.18138, 0.90503, 0.19227, 0.909, 0.19911, 0.93415, 0.19315, 0.96563, 0.18569, 0.96276, 0.19446, 0.93817, 0.20283, 0.89677, 0.21692, 0.8642, 0.228, 0.83655, 0.24046, 0.80675, 0.25388, 0.79942, 0.25572, 0.7794, 0.26735, 0.76771, 0.27414, 0.75618, 0.28218, 0.73307, 0.29831, 0.69709, 0.32383, 0.64693, 0.36115, 0.59815, 0.40424, 0.56276, 0.4355, 0.48818, 0.50526, 0.41696, 0.57188, 0.33734, 0.65295, 0.29289, 0.70043, 0.25271, 0.74654, 0.23611, 0.76837, 0.21476, 0.7996, 0.20372, 0.81573, 0.18873, 0.83764, 0.18315, 0.84706, 0.17357, 0.86321, 0.17049, 0.87824, 0.16825, 0.88916, 0.16843, 0.89689, 0.17147, 0.91247, 0.17463, 0.92867, 0.16475, 0.92196, 0.16049, 0.91206, 0.16047, 0.90929, 0.1488, 0.91063, 0.14892, 0.90011, 0.14535, 0.89767, 0.13664, 0.902, 0.11256, 0.91394, 0.11245, 0.90237, 0.11232, 0.88884, 0.09736, 0.90596, 0.08419, 0.92102, 0.07914, 0.92679, 0.06986, 0.93741, 0.05855, 0.95035, 0.04304, 0.96909, 0.02927, 0.98573, 0.01746, 1, 0.01407, 0.99678, 0.02581, 0.98172, 0.03863, 0.96526, 0.06042, 0.9373, 0.0598, 0.91202, 0.03193, 0.93805, 0, 0.96789, 0, 0.96026, 0.00458, 0.95014, 0.03564, 0.92202, 0.07895, 0.88281, 0.11548, 0.84929, 0.14964, 0.81794, 0.17855, 0.79316, 0.21818, 0.75105, 0.25137, 0.71578, 0.38766, 0.58431, 0.44783, 0.52627, 0.52756, 0.45195, 0.6087, 0.37631, 0.65828, 0.33318, 0.69417, 0.30196, 0.7012, 0.29269, 0.71618, 0.2837, 0.7448, 0.26122, 0.76861, 0.24251, 0.78007, 0.23164, 0.79262, 0.21974, 0.80044, 0.20492, 0.81358, 0.18002, 0.82096, 0.16276, 0.83095, 0.13451, 0.84201, 0.10324, 0.84903, 0.07821, 0.85405, 0.06034, 0.85013, 0.05837, 0.85111, 0.05084, 0.86283, 0.05085, 0.87032, 0.05738, 0.8788, 0.07223, 0.87858, 0.087, 0.87504, 0.08392, 0.86953, 0.0747, 0.85971, 0.05824, 0.85554, 0.07662, 0.85173, 0.10439, 0.84273, 0.13124, 0.83541, 0.15309, 0.83899, 0.15957, 0.85352, 0.15295, 0.86779, 0.14645, 0.88261, 0.13589, 0.89094, 0.12651, 0.90223, 0.10152, 0.91301, 0.07769, 0.90501, 0.06559, 0.89605, 0.05811, 0.89502, 0.05335, 0.89882, 0.05346, 0.89868, 0.05812, 0.9134, 0.07523, 0.92018, 0.05363, 0.92708, 0.03162, 0.92735, 0.01073, 0.92477, 0.00643], "triangles": [67, 107, 66, 68, 107, 67, 69, 106, 68, 70, 106, 69, 70, 81, 105, 81, 70, 71, 72, 81, 71, 80, 81, 72, 73, 80, 72, 82, 85, 81, 78, 80, 73, 79, 80, 78, 74, 77, 78, 74, 78, 73, 83, 84, 82, 76, 77, 74, 76, 74, 75, 106, 107, 68, 105, 106, 70, 85, 104, 105, 81, 85, 105, 84, 85, 82, 86, 104, 85, 87, 98, 104, 86, 87, 104, 98, 103, 104, 88, 98, 87, 89, 97, 98, 99, 103, 98, 88, 89, 98, 99, 102, 103, 90, 97, 89, 90, 96, 97, 100, 102, 99, 100, 101, 102, 91, 96, 90, 92, 95, 96, 92, 96, 91, 93, 94, 95, 92, 93, 95, 62, 63, 109, 64, 109, 63, 108, 109, 64, 65, 108, 64, 66, 107, 108, 66, 108, 65, 61, 110, 111, 62, 110, 61, 110, 62, 109, 111, 112, 60, 61, 111, 60, 58, 113, 57, 58, 59, 113, 59, 112, 113, 60, 112, 59, 56, 115, 116, 55, 56, 116, 55, 116, 117, 114, 115, 56, 57, 114, 56, 57, 113, 114, 51, 120, 121, 50, 51, 121, 52, 119, 120, 52, 120, 51, 53, 118, 119, 52, 53, 119, 54, 118, 53, 55, 117, 118, 54, 55, 118, 45, 43, 44, 46, 43, 45, 47, 40, 41, 47, 41, 42, 46, 47, 42, 46, 42, 43, 47, 48, 40, 48, 49, 123, 50, 121, 49, 158, 0, 1, 157, 158, 1, 157, 1, 2, 3, 156, 157, 3, 157, 2, 4, 5, 6, 156, 3, 4, 7, 4, 6, 155, 156, 4, 155, 4, 7, 12, 10, 11, 150, 151, 152, 153, 150, 152, 149, 153, 154, 150, 153, 149, 7, 8, 155, 8, 154, 155, 148, 149, 154, 12, 9, 10, 13, 9, 12, 13, 8, 9, 14, 8, 13, 19, 20, 21, 19, 21, 22, 22, 24, 19, 23, 24, 22, 25, 19, 24, 14, 154, 8, 18, 19, 25, 36, 17, 18, 26, 36, 18, 26, 18, 25, 14, 15, 154, 15, 148, 154, 15, 147, 148, 16, 147, 15, 146, 147, 16, 28, 35, 27, 36, 26, 27, 35, 36, 27, 37, 16, 17, 37, 17, 36, 33, 35, 28, 33, 28, 29, 34, 35, 33, 38, 146, 16, 39, 145, 146, 38, 16, 37, 30, 33, 29, 38, 39, 146, 31, 33, 30, 32, 33, 31, 40, 145, 39, 144, 145, 40, 144, 40, 143, 40, 48, 143, 142, 143, 48, 137, 130, 131, 137, 131, 132, 128, 129, 130, 137, 128, 130, 136, 137, 132, 136, 132, 133, 138, 128, 137, 127, 128, 138, 135, 136, 133, 134, 135, 133, 139, 127, 138, 126, 127, 139, 140, 126, 139, 125, 126, 140, 141, 125, 140, 124, 125, 141, 124, 141, 142, 142, 123, 124, 48, 123, 142, 122, 123, 49, 121, 122, 49], "vertices": [1, 32, -25.08, -67.66, 1, 1, 32, -27, -63.89, 1, 1, 32, -25.18, -57.94, 1, 1, 32, -18.92, -49.15, 1, 1, 32, -16.59, -41.42, 1, 1, 32, -32.45, -40.01, 1, 1, 32, -31.66, -36.46, 1, 1, 32, -13.81, -33.96, 1, 1, 32, 5.54, -31.26, 1, 1, 32, -10.5, -25.86, 1, 1, 32, -19.31, -22.89, 1, 1, 32, -20.4, -26.54, 1, 1, 32, -20.54, -6.69, 1, 1, 32, -11.56, -2.01, 1, 1, 32, 2.28, 5.22, 1, 1, 32, 12.57, 10.59, 1, 1, 32, 24.37, 16.75, 1, 1, 32, 7.99, 22.18, 1, 1, 32, -7.29, 27.25, 1, 1, 32, -12.01, 18.2, 1, 1, 32, -17.46, 7.75, 1, 1, 32, -29.51, 15.88, 1, 1, 32, -29.5, 23.15, 1, 1, 32, -24.37, 29.75, 1, 1, 32, -18.67, 29.41, 1, 1, 32, -12.8, 30.17, 1, 1, 32, -2.21, 31.53, 1, 1, 32, -3.16, 36.61, 1, 1, 32, -5.98, 43.75, 1, 1, 32, -8.14, 49.19, 1, 1, 32, -0.75, 58.67, 1, 1, 32, 4.86, 59.78, 1, 1, 32, 6.71, 58.41, 1, 1, 32, 2.94, 53.55, 1, 1, 32, 7.53, 48.61, 1, 1, 32, -3.29, 41.03, 1, 1, 32, -1.87, 30.93, 1, 1, 32, 19.31, 29.18, 1, 2, 32, 45.03, 22.87, 0.98989, 41, 10.26, -22.5, 0.01011, 2, 32, 55.24, 20.37, 0.93788, 41, 20.69, -21.22, 0.06212, 2, 32, 67.2, 17.44, 0.65862, 41, 32.91, -19.72, 0.34138, 2, 32, 67.79, 26.45, 0.22781, 41, 30.26, -11.08, 0.77219, 2, 32, 68.16, 32.12, 0.05875, 41, 28.6, -5.65, 0.94125, 1, 41, 8.18, -3.72, 1, 1, 41, -17.38, -1.3, 1, 1, 41, -13.42, 3.81, 1, 1, 41, 7.04, 3.59, 1, 1, 41, 41.51, 3.22, 1, 1, 41, 68.63, 2.93, 1, 1, 41, 92.26, 4.69, 1, 2, 41, 117.73, 6.59, 0.47998, 33, 46.71, 14.91, 0.52002, 2, 41, 123.7, 6.09, 0.26026, 33, 52.27, 12.67, 0.73974, 2, 41, 141.33, 9.08, 0.01958, 33, 70, 10.32, 0.98042, 1, 33, 80.36, 8.95, 1, 1, 33, 91.09, 8.37, 1, 1, 33, 112.6, 7.19, 1, 3, 40, 193.87, -51.92, 0, 33, 146.25, 5.6, 4e-05, 34, 37.81, 6.21, 0.99996, 1, 34, 85.35, 3.99, 1, 2, 33, 242.72, 7.06, 0, 35, 26.14, 3.76, 1, 2, 33, 278.19, 9.01, 0, 35, 61.65, 3.47, 1, 1, 36, 29.88, 3.15, 1, 3, 34, 319.09, 16.06, 0, 36, 102.93, 4.59, 0.04438, 37, -5.35, 1.78, 0.95562, 2, 37, 79.11, 6.99, 0.99928, 38, -28.98, 5.17, 0.00072, 2, 34, 451.11, 30.92, 0, 38, 19.22, 7.27, 1, 3, 38, 64.22, 10.84, 0.99295, 39, -44.57, 5.53, 0.00486, 69, -42.8, 16.41, 0.00219, 3, 38, 84.04, 13.78, 0.84299, 39, -24.89, 9.31, 0.07922, 69, -22.97, 13.55, 0.07779, 1, 69, 4.35, 11.04, 1, 1, 69, 18.47, 9.74, 1, 1, 69, 37.64, 7.97, 1, 1, 69, 45.51, 7.79, 1, 1, 69, 58.99, 7.47, 1, 1, 69, 69, 11.07, 1, 1, 69, 76.26, 13.69, 1, 1, 69, 80.63, 16.73, 1, 1, 69, 88.27, 24.63, 1, 1, 69, 96.22, 32.84, 1, 1, 69, 96.68, 23.71, 1, 1, 69, 92.84, 17.13, 1, 1, 69, 91.26, 16.08, 1, 1, 69, 97.13, 8.78, 1, 2, 39, 85.66, 38.38, 0.01778, 69, 91.02, 4.9, 0.98222, 2, 39, 86.9, 35.31, 0.07731, 69, 91.18, 1.59, 0.92269, 2, 39, 94.22, 33.4, 0.28821, 69, 97.48, -2.6, 0.71179, 2, 39, 114.47, 28.13, 0.52763, 69, 114.89, -14.2, 0.47237, 2, 39, 109.67, 21.78, 0.55322, 69, 108.28, -18.63, 0.44678, 2, 39, 104.06, 14.36, 0.78079, 69, 100.56, -23.82, 0.21921, 2, 39, 120.72, 16.36, 0.98222, 69, 116.95, -27.37, 0.01778, 1, 39, 135.38, 18.12, 1, 1, 39, 141, 18.79, 1, 1, 39, 151.33, 20.04, 1, 1, 39, 163.93, 21.55, 1, 1, 39, 181.62, 24.17, 1, 1, 39, 197.33, 26.49, 1, 1, 39, 210.8, 28.48, 1, 1, 39, 211.59, 25.07, 1, 1, 39, 197.83, 22.61, 1, 1, 39, 182.8, 19.93, 1, 1, 39, 157.25, 15.36, 1, 1, 39, 147.01, 1.3, 1, 1, 39, 175.58, 1.84, 1, 1, 39, 208.32, 2.47, 1, 1, 39, 205.11, -1.68, 1, 1, 39, 197.95, -4.95, 1, 1, 39, 166.48, -5.07, 1, 1, 39, 122.61, -5.24, 1, 1, 39, 85.42, -5.62, 1, 1, 39, 50.63, -5.99, 1, 1, 39, 21.92, -5.34, 1, 1, 38, 87.31, -4.56, 1, 2, 34, 484.28, 20.35, 0, 38, 51.4, -6.03, 1, 1, 37, 18.41, -5.78, 1, 2, 40, 397.93, -183.9, 0, 36, 64.23, -5.15, 1, 3, 40, 326.56, -144.17, 0, 34, 199.29, -0.18, 0, 35, 90.83, -4.78, 1, 1, 35, 7.71, -6.28, 1, 1, 34, 66.83, -6.47, 1, 1, 34, 31.01, -7.42, 1, 1, 34, 22.74, -9.37, 1, 2, 40, 162.55, -48.2, 0, 34, 9.4, -7.5, 1, 3, 40, 139.6, -32.85, 1e-05, 33, 90.52, -8.68, 0.96876, 34, -18.2, -6.9, 0.03122, 3, 40, 120.51, -20.08, 0.00178, 33, 67.55, -8.66, 0.9982, 34, -41.17, -6.41, 2e-05, 3, 40, 110.13, -14.42, 0.08471, 33, 55.78, -9.72, 0.91528, 34, -52.96, -7.22, 0, 3, 40, 98.77, -8.22, 0.43823, 33, 42.89, -10.87, 0.56177, 34, -65.88, -8.1, 0, 1, 40, 86.96, -6.27, 1, 1, 40, 67.14, -3, 1, 1, 40, 53.92, -2.02, 1, 1, 40, 32.91, -1.95, 1, 1, 40, 9.65, -1.87, 1, 1, 40, -8.42, -3.16, 1, 1, 40, -21.31, -4.09, 1, 1, 40, -21.39, -7.5, 1, 1, 40, -26.48, -8.72, 1, 1, 40, -30.01, -0.05, 1, 1, 40, -28.1, 7.19, 1, 1, 40, -21.19, 17.32, 1, 1, 40, -11.71, 20.98, 1, 1, 40, -12.61, 17.56, 1, 1, 40, -16.83, 11.1, 1, 1, 40, -24.35, -0.44, 1, 1, 40, -11.39, 1.25, 1, 1, 40, 7.46, 5.62, 1, 1, 40, 27.27, 5.93, 1, 3, 40, 43.41, 6.17, 0.89905, 32, 95.3, -28.9, 0.09758, 33, -11.14, -29.66, 0.00337, 3, 40, 46.46, 10.5, 0.63936, 32, 95.76, -23.62, 0.34136, 33, -11, -24.36, 0.01928, 2, 40, 37.86, 19.54, 0.19847, 32, 83.79, -20.12, 0.80153, 2, 40, 29.42, 28.42, 0.04538, 32, 72.03, -16.68, 0.95462, 2, 40, 18.23, 36.64, 0.00246, 32, 58.23, -15.19, 0.99754, 1, 32, 49.01, -16.22, 1, 1, 32, 31.35, -24.3, 1, 1, 32, 14.52, -32, 1, 1, 32, 14.47, -42.5, 1, 1, 32, 16.99, -50.95, 1, 1, 32, 15.64, -54.04, 1, 1, 32, 13.28, -52.13, 1, 1, 32, 15.34, -49.66, 1, 1, 32, 13.23, -33.15, 1, 1, 32, -0.14, -41.59, 1, 1, 32, -13.77, -50.2, 1, 1, 32, -22.73, -61.43, 1, 1, 32, -22.91, -65.04, 1], "hull": 159, "edges": [0, 316, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 40, 42, 42, 44, 44, 46, 46, 48, 52, 54, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 88, 90, 100, 102, 110, 112, 112, 114, 122, 124, 124, 126, 126, 128, 128, 130, 144, 146, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 186, 188, 194, 196, 200, 202, 202, 204, 212, 214, 230, 232, 232, 234, 246, 248, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 274, 276, 276, 278, 282, 284, 288, 290, 290, 292, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 312, 314, 314, 316, 252, 254, 254, 256, 248, 250, 250, 252, 284, 286, 286, 288, 278, 280, 280, 282, 292, 294, 294, 296, 32, 34, 34, 36, 36, 38, 38, 40, 48, 50, 50, 52, 84, 86, 86, 88, 90, 92, 92, 94, 94, 96, 80, 82, 82, 84, 74, 76, 308, 310, 310, 312, 16, 18, 18, 20, 12, 14, 14, 16, 24, 26, 26, 28, 28, 30, 30, 32, 242, 244, 244, 246, 96, 98, 98, 100, 238, 240, 240, 242, 234, 236, 236, 238, 106, 108, 108, 110, 102, 104, 104, 106, 76, 78, 78, 80, 270, 272, 272, 274, 54, 56, 56, 58, 226, 228, 228, 230, 114, 116, 116, 118, 222, 224, 224, 226, 118, 120, 120, 122, 218, 220, 220, 222, 214, 216, 216, 218, 130, 132, 174, 176, 176, 178, 178, 180, 180, 182, 192, 194, 204, 206, 206, 208, 196, 198, 198, 200, 208, 210, 210, 212, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 146, 148, 148, 150, 140, 142, 142, 144, 136, 138, 138, 140, 132, 134, 134, 136, 188, 190, 190, 192, 182, 184, 184, 186], "width": 335, "height": 288}}, "tui18": {"tui18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.55, -182.72, -107.21, -4.38, 151.42, 159.15, 264.19, -19.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 211, "height": 306}}, "zhengyan": {"zhengyan": {"type": "mesh", "uvs": [0.94913, 0.01581, 0.99336, 0.00678, 0.9931, 0.09752, 0.99279, 0.20827, 0.96354, 0.28327, 0.93499, 0.35646, 0.86249, 0.43669, 0.79679, 0.46641, 0.73209, 0.49568, 0.65673, 0.52977, 0.59523, 0.55759, 0.50138, 0.60004, 0.43491, 0.65092, 0.40424, 0.70743, 0.36283, 0.78371, 0.33102, 0.84232, 0.29889, 0.90153, 0.2417, 0.98172, 0.20077, 0.98866, 0.149, 0.99744, 0.11875, 0.97177, 0.07261, 0.89663, 0.03675, 0.83821, 0, 0.77834, 0, 0.77376, 0.02536, 0.74457, 0.0522, 0.71368, 0.10473, 0.65321, 0.14485, 0.60701, 0.20251, 0.60462, 0.23731, 0.60317, 0.31382, 0.63918, 0.39623, 0.67798, 0.57826, 0.54082, 0.57835, 0.44682, 0.57846, 0.32747, 0.59297, 0.2561, 0.60654, 0.18936, 0.65686, 0.12053, 0.70577, 0.05362, 0.8046, 0.03937, 0.90366, 0.02509], "triangles": [2, 0, 1, 2, 41, 0, 2, 3, 41, 4, 41, 3, 4, 40, 41, 5, 6, 40, 4, 5, 40, 40, 38, 39, 6, 7, 40, 7, 38, 40, 36, 37, 38, 7, 36, 38, 8, 35, 36, 8, 34, 35, 36, 7, 8, 9, 34, 8, 10, 33, 34, 9, 10, 34, 11, 33, 10, 11, 32, 33, 12, 32, 11, 13, 32, 12, 23, 24, 25, 14, 31, 32, 14, 32, 13, 22, 25, 26, 23, 25, 22, 15, 31, 14, 21, 26, 27, 22, 26, 21, 30, 15, 29, 15, 30, 31, 27, 29, 21, 20, 21, 29, 16, 29, 15, 16, 18, 29, 28, 29, 27, 17, 18, 16, 18, 20, 29, 19, 20, 18], "vertices": [0.27, -87.33, -3.18, -92.55, -9.86, -86.55, -18.02, -79.22, -20.85, -71.23, -23.6, -63.43, -22.78, -50.56, -18.87, -41.74, -15.01, -33.04, -10.51, -22.92, -6.85, -14.66, -1.25, -2.05, 1.18, 8.25, -0.15, 15.18, -1.93, 24.53, -3.31, 31.71, -4.69, 38.97, -5.3, 50.23, -2, 54.97, 2.17, 60.96, 6.88, 62.43, 16.73, 62.31, 24.39, 62.21, 32.24, 62.12, 32.58, 61.81, 32.38, 57.24, 32.16, 52.4, 31.74, 42.93, 31.42, 35.69, 26.24, 29.5, 23.1, 25.77, 13.32, 20.14, 2.78, 14.08, -4.03, -13.99, 2.92, -20.18, 11.73, -28.05, 15.66, -34.27, 19.33, -40.08, 19.74, -49.87, 20.13, -59.39, 11.98, -70.66, 3.82, -81.96], "hull": 42, "edges": [10, 12, 22, 24, 32, 34, 38, 40, 46, 48, 64, 66, 20, 22, 18, 20, 16, 18, 12, 14, 14, 16, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 82, 78, 80, 80, 82, 74, 76, 76, 78, 66, 68, 68, 70, 70, 72, 72, 74, 60, 62, 62, 64, 24, 26, 26, 28, 28, 30, 30, 32, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 52, 54, 54, 56, 56, 58, 58, 60, 48, 50, 50, 52], "width": 140, "height": 99}}, "liuhai": {"liuhai": {"type": "mesh", "uvs": [0.14032, 0.00597, 0.20001, 0.00637, 0.24486, 0.00668, 0.28055, 0.07211, 0.34343, 0.18741, 0.40164, 0.29415, 0.45825, 0.39795, 0.50172, 0.47766, 0.5304, 0.53025, 0.57293, 0.61823, 0.61537, 0.6682, 0.66865, 0.73092, 0.72272, 0.79458, 0.75962, 0.83802, 0.82385, 0.88642, 0.8683, 0.91992, 0.90598, 0.93806, 0.95636, 0.96232, 1, 0.9637, 1, 0.97245, 0.98614, 0.99416, 0.91336, 0.99324, 0.83884, 0.9923, 0.75116, 0.94354, 0.67969, 0.9038, 0.62685, 0.87443, 0.59087, 0.82474, 0.54929, 0.76732, 0.50583, 0.70732, 0.4682, 0.65536, 0.41427, 0.5647, 0.36051, 0.47434, 0.29589, 0.36571, 0.23972, 0.2713, 0.18846, 0.18514, 0.14242, 0.15416, 0.1111, 0.13308, 0.0966, 0.16892, 0.08225, 0.20441, 0.06255, 0.25312, 0.03086, 0.33148, 0.00411, 0.36941, 0.0044, 0.19986, 0.04477, 0.11942, 0.07138, 0.06642, 0.10185, 0.00571], "triangles": [20, 17, 18, 22, 14, 15, 21, 16, 17, 15, 16, 21, 22, 15, 21, 19, 20, 18, 21, 17, 20, 12, 24, 11, 24, 12, 13, 23, 24, 13, 23, 13, 14, 23, 14, 22, 28, 29, 8, 9, 28, 8, 27, 28, 9, 27, 9, 10, 26, 27, 10, 26, 10, 11, 25, 26, 11, 24, 25, 11, 34, 2, 3, 33, 34, 3, 33, 3, 4, 32, 33, 4, 32, 4, 5, 31, 32, 5, 31, 5, 6, 30, 31, 6, 30, 6, 7, 29, 30, 7, 29, 7, 8, 36, 45, 0, 44, 45, 36, 35, 0, 1, 36, 0, 35, 34, 35, 1, 34, 1, 2, 43, 44, 36, 37, 43, 36, 38, 43, 37, 42, 43, 38, 39, 42, 38, 40, 42, 39, 41, 42, 40], "vertices": [2, 241, 44.78, -5.47, 0.01672, 242, 11.6, 10.88, 0.98328, 2, 242, 25.15, 8.65, 0.95704, 243, -11.98, 6.51, 0.04296, 2, 242, 35.33, 6.98, 0.46369, 243, -4.5, 13.61, 0.53631, 2, 242, 42.02, -3.17, 0.03892, 243, 7.63, 12.82, 0.96108, 1, 243, 29, 11.42, 1, 1, 243, 48.78, 10.12, 1, 1, 243, 68.02, 8.86, 1, 2, 243, 82.8, 7.9, 0.9905, 244, -8.25, 8.25, 0.0095, 2, 243, 92.54, 7.26, 0.39636, 244, 1.46, 7.2, 0.60364, 1, 244, 16.77, 4.62, 1, 1, 244, 28.62, 5.94, 1, 2, 244, 43.49, 7.59, 0.9944, 245, -8.44, 10.73, 0.0056, 2, 244, 58.59, 9.27, 0.11291, 245, 6.44, 7.66, 0.88709, 1, 245, 16.59, 5.56, 1, 2, 245, 32.78, 5.34, 0.88341, 246, -2.84, 5.89, 0.11659, 1, 246, 8.19, 3.87, 1, 1, 246, 17.2, 3.52, 1, 1, 246, 29.24, 3.05, 1, 1, 246, 39.04, 5.26, 1, 1, 246, 39.32, 4.09, 1, 1, 246, 36.93, 0.44, 1, 1, 246, 20.65, -3.42, 1, 2, 245, 41.7, -6.61, 0.08937, 246, 3.97, -7.38, 0.91063, 1, 245, 20.54, -8.48, 1, 2, 244, 61.05, -8.5, 0.2086, 245, 3.29, -10.01, 0.7914, 2, 244, 49.27, -13.51, 0.95574, 245, -9.46, -11.13, 0.04426, 1, 244, 38.56, -13.88, 1, 1, 244, 26.19, -14.3, 1, 2, 243, 105.25, -14.17, 0.03817, 244, 13.25, -14.73, 0.96183, 2, 243, 94.07, -15.01, 0.43135, 244, 2.05, -15.11, 0.56865, 2, 243, 76.52, -14.63, 0.99804, 244, -15.46, -13.99, 0.00196, 1, 243, 59.03, -14.24, 1, 1, 243, 38, -13.78, 1, 1, 243, 19.73, -13.38, 1, 2, 242, 18.66, -15.11, 0.23183, 243, 3.05, -13.01, 0.76817, 2, 242, 8.87, -9.25, 0.87161, 243, -7.53, -17.28, 0.12839, 3, 241, 26.29, -8.08, 0.08583, 242, 2.21, -5.26, 0.90859, 243, -14.73, -20.18, 0.00558, 2, 241, 20.38, -7.56, 0.69098, 242, -1.85, -9.58, 0.30902, 2, 241, 14.53, -7.05, 0.94453, 242, -5.88, -13.86, 0.05547, 2, 241, 6.49, -6.34, 0.99987, 242, -11.41, -19.73, 0.00013, 1, 241, -6.43, -5.2, 1, 1, 241, -13.97, -2.36, 1, 1, 241, 6.35, 8.9, 1, 1, 241, 20.5, 6.15, 1, 2, 241, 29.82, 4.35, 0.9447, 242, -5.36, 5.2, 0.0553, 2, 241, 40.5, 2.28, 0.17254, 242, 2.87, 12.31, 0.82746], "hull": 46, "edges": [16, 18, 34, 36, 36, 38, 38, 40, 80, 82, 82, 84, 84, 86, 68, 70, 70, 72, 0, 90, 0, 2, 2, 4, 86, 88, 88, 90, 72, 74, 74, 76, 76, 78, 78, 80, 4, 6, 66, 68, 6, 8, 64, 66, 8, 10, 62, 64, 10, 12, 58, 60, 60, 62, 12, 14, 14, 16, 56, 58, 54, 56, 18, 20, 50, 52, 52, 54, 20, 22, 48, 50, 22, 24, 24, 26, 44, 46, 46, 48, 26, 28, 28, 30, 40, 42, 42, 44, 30, 32, 32, 34], "width": 230, "height": 137}}, "zuoxiaobi": {"zuoxiaobi": {"type": "mesh", "uvs": [0.63266, 0.00171, 0.68141, 0.00457, 0.77648, 0.2753, 0.79154, 0.30844, 0.84685, 0.31985, 0.86604, 0.36093, 0.88857, 0.40914, 0.91194, 0.45915, 0.91744, 0.46519, 0.94813, 0.47731, 0.96498, 0.52675, 0.98019, 0.57135, 1, 0.62946, 1, 0.67304, 0.99319, 0.7287, 0.98596, 0.78783, 0.98239, 0.81706, 0.93579, 0.83904, 0.85018, 0.87942, 0.77315, 0.91575, 0.70833, 0.94632, 0.64285, 0.97721, 0.57786, 1, 0.53382, 1, 0.41154, 0.98121, 0.30991, 0.95032, 0.2238, 0.92415, 0.13925, 0.89845, 0.10931, 0.89301, 0.07274, 0.88731, 0.00701, 0.87706, 0.00699, 0.84157, 0.00697, 0.7889, 0.08406, 0.69865, 0.13898, 0.65669, 0.20869, 0.60341, 0.29266, 0.55392, 0.42116, 0.47817, 0.50588, 0.43371, 0.59331, 0.38782, 0.64717, 0.35956, 0.66052, 0.35369, 0.57404, 0.06244, 0.57435, 0.03117, 0.59832, 0.00339, 0.66231, 0.38346, 0.6727, 0.43055, 0.69068, 0.47547], "triangles": [28, 32, 33, 27, 28, 33, 29, 32, 28, 29, 30, 31, 29, 31, 32, 22, 23, 21, 23, 24, 21, 26, 34, 35, 27, 33, 34, 26, 27, 34, 21, 24, 20, 36, 20, 24, 36, 24, 25, 36, 25, 35, 25, 26, 35, 20, 36, 19, 19, 36, 47, 19, 47, 18, 36, 37, 47, 37, 38, 47, 38, 46, 47, 38, 39, 46, 39, 45, 46, 5, 46, 45, 39, 40, 45, 40, 41, 45, 18, 14, 17, 16, 17, 15, 14, 15, 17, 11, 18, 47, 8, 47, 7, 7, 47, 6, 6, 47, 5, 3, 4, 5, 10, 47, 8, 10, 8, 9, 10, 11, 47, 18, 13, 14, 11, 13, 18, 11, 12, 13, 45, 3, 5, 46, 5, 47, 45, 41, 3, 41, 42, 2, 1, 2, 42, 1, 42, 0, 41, 2, 3, 0, 42, 43, 44, 0, 43], "vertices": [1, 43, 276.18, -3.32, 1, 1, 43, 270.78, -15.63, 1, 1, 43, 164.74, -6.7, 1, 1, 43, 151.46, -6.5, 1, 1, 43, 142.38, -19.46, 1, 1, 43, 125.87, -19.34, 1, 1, 43, 106.48, -19.21, 1, 1, 43, 86.38, -19.07, 1, 1, 43, 83.71, -19.75, 1, 1, 43, 76.59, -26.22, 1, 1, 43, 57.27, -24.46, 1, 1, 43, 39.84, -22.87, 1, 1, 43, 17.14, -20.8, 1, 2, 43, 1.44, -15.38, 0.9991, 203, -38.87, -30.73, 0.0009, 2, 43, -18, -6.69, 0.70047, 203, -25.27, -14.35, 0.29953, 2, 43, -38.64, 2.53, 0.08472, 203, -10.83, 3.05, 0.91528, 1, 203, -3.68, 11.65, 1, 1, 203, 11.62, 11.25, 1, 1, 203, 39.73, 10.52, 1, 1, 204, 14.63, 9.86, 1, 2, 204, 35.92, 9.31, 0.0823, 205, 4.91, 9.31, 0.9177, 4, 205, 26.41, 8.75, 0.07913, 206, 7.03, 8.75, 0.87821, 207, -4.6, 8.75, 0.00314, 208, -12.35, 8.75, 0.03953, 1, 208, 7.28, 5.73, 1, 7, 198, 89.33, 141.35, 0.0053, 199, 29.97, 141.35, 0.01055, 200, -7.13, 141.35, 0.01034, 207, 25, -1.16, 0.0256, 208, 17.25, -1.16, 0.94614, 201, -29.39, 141.35, 0.00118, 202, -44.24, 141.35, 0.0009, 11, 197, 204.33, 112.4, 0, 204, 110.62, -26.17, 2e-05, 198, 107.87, 112.4, 0.03469, 205, 79.6, -26.17, 0.00011, 199, 48.51, 112.4, 0.0793, 206, 60.22, -26.17, 0.00015, 200, 11.41, 112.4, 0.08459, 207, 48.59, -26.17, 0.19294, 208, 40.84, -26.17, 0.58318, 201, -10.85, 112.4, 0.01295, 202, -25.7, 112.4, 0.01208, 10, 204, 126.92, -51.74, 5e-05, 198, 119.14, 84.24, 0.04143, 205, 95.9, -51.74, 0.00029, 199, 59.78, 84.24, 0.14135, 206, 76.52, -51.74, 0.00015, 200, 22.68, 84.24, 0.18796, 207, 64.89, -51.74, 0.16773, 208, 57.14, -51.74, 0.37181, 201, 0.42, 84.24, 0.0423, 202, -14.43, 84.24, 0.04694, 7, 198, 128.69, 60.39, 0.02138, 199, 69.33, 60.39, 0.13869, 200, 32.22, 60.39, 0.28357, 207, 78.7, -73.41, 0.10439, 208, 70.94, -73.41, 0.21273, 201, 9.96, 60.39, 0.10246, 202, -4.88, 60.39, 0.13678, 7, 198, 138.06, 36.97, 0.00199, 199, 78.7, 36.97, 0.05097, 200, 41.6, 36.97, 0.24308, 207, 92.26, -94.68, 0.03942, 208, 84.5, -94.68, 0.07976, 201, 19.34, 36.97, 0.18521, 202, 4.5, 36.97, 0.39957, 7, 198, 142.38, 29.65, 0.00017, 199, 83.01, 29.65, 0.02134, 200, 45.91, 29.65, 0.15962, 207, 97.85, -101.07, 0.02246, 208, 90.1, -101.07, 0.04601, 201, 23.65, 29.65, 0.17177, 202, 8.81, 29.65, 0.57864, 6, 199, 88.53, 20.97, 0.00348, 200, 51.43, 20.97, 0.05888, 207, 104.89, -108.58, 0.00904, 208, 97.14, -108.58, 0.01899, 201, 29.17, 20.97, 0.08907, 202, 14.33, 20.97, 0.82053, 3, 207, 117.54, -122.07, 0.00045, 208, 109.78, -122.07, 0.00118, 202, 24.26, 5.37, 0.99837, 1, 202, 14.64, -4.14, 1, 3, 200, 37.47, -18.24, 0.11075, 201, 15.21, -18.24, 0.53007, 202, 0.37, -18.24, 0.35918, 4, 198, 94.57, -27.33, 0.00081, 199, 35.21, -27.33, 0.54799, 200, -1.89, -27.33, 0.4281, 201, -24.15, -27.33, 0.0231, 3, 198, 72.59, -27.82, 0.15399, 199, 13.22, -27.82, 0.79244, 200, -23.88, -27.82, 0.05357, 2, 198, 44.67, -28.45, 0.85696, 199, -14.69, -28.45, 0.14304, 2, 197, 111.49, -25.27, 0.09655, 198, 15.03, -25.27, 0.90345, 2, 197, 66.13, -20.41, 0.99977, 198, -30.34, -20.41, 0.00023, 1, 197, 37.7, -15.74, 1, 1, 197, 8.37, -10.93, 1, 2, 43, 146, 37.38, 0.20275, 197, -9.7, -7.96, 0.79725, 2, 43, 146.91, 33.19, 0.39332, 197, -13.87, -6.92, 0.60668, 1, 43, 259.56, 19.47, 1, 1, 43, 270.8, 15.51, 1, 1, 43, 278.65, 5.82, 1, 2, 43, 136.03, 36.42, 0.13501, 197, -6.15, 1.4, 0.86499, 6, 43, 118.13, 39.57, 0.2046, 203, -17.36, -157.91, 4e-05, 197, 4.61, 16.05, 0.79493, 204, -67.76, -157.91, 0.00037, 205, -98.77, -157.91, 1e-05, 207, -129.79, -157.91, 5e-05, 6, 43, 100.34, 40.48, 0.37894, 203, -11.7, -141.02, 0.00133, 197, 13.31, 31.59, 0.61593, 204, -62.1, -141.02, 0.00284, 205, -93.11, -141.02, 0.00026, 207, -124.12, -141.02, 0.0007], "hull": 45, "edges": [0, 88, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 24, 26, 42, 44, 44, 46, 46, 48, 54, 56, 64, 66, 80, 82, 82, 84, 84, 86, 86, 88, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 26, 28, 28, 30, 30, 32, 18, 20, 20, 22, 22, 24, 8, 10, 10, 12, 12, 14], "width": 275, "height": 381}}, "zhengyan2": {"zhengyan2": {"type": "mesh", "uvs": [0.67201, 0.00945, 0.7342, 0.00946, 0.85506, 0.09429, 0.9607, 0.16844, 0.99194, 0.25037, 0.9917, 0.34551, 0.75611, 0.54899, 0.5859, 0.54998, 0.46091, 0.49751, 0.43113, 0.66567, 0.41845, 0.7373, 0.39525, 0.86832, 0.09868, 1, 0.04791, 1, 0.01743, 0.94928, 0.01282, 0.80582, 0.00809, 0.65902, 0.00305, 0.50205, 0.10605, 0.39168, 0.26549, 0.39195, 0.37042, 0.44855, 0.45989, 0.4968, 0.45403, 0.34337, 0.44799, 0.18518, 0.50076, 0.09326, 0.5489, 0.00941], "triangles": [22, 23, 24, 4, 2, 3, 8, 21, 22, 22, 7, 8, 2, 0, 1, 5, 6, 2, 4, 5, 2, 0, 24, 25, 0, 22, 24, 22, 6, 7, 0, 6, 22, 2, 6, 0, 16, 17, 18, 9, 20, 21, 9, 21, 8, 9, 19, 20, 19, 15, 16, 10, 19, 9, 14, 15, 10, 18, 19, 16, 19, 10, 15, 10, 12, 14, 12, 13, 14, 10, 11, 12], "vertices": [28.77, -45.87, 25.44, -52.35, 11.28, -60.97, -1.09, -68.51, -10.2, -67.94, -18.82, -63.48, -24.68, -29.48, -15.68, -11.72, -4.23, -1.16, -17.9, 9.78, -23.72, 14.43, -34.37, 22.95, -30.46, 59.96, -27.75, 65.24, -21.52, 66.05, -8.25, 59.84, 5.32, 53.49, 19.83, 46.7, 24.34, 30.84, 15.79, 14.26, 5.05, 5.98, -4.11, -1.09, 10.12, -7.63, 24.8, -14.37, 30.32, -24.15, 35.35, -33.06], "hull": 26, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 26, 28, 34, 36, 36, 38, 32, 34, 28, 30, 30, 32, 38, 40, 40, 42, 16, 18, 18, 20, 20, 22, 42, 44, 44, 46, 46, 48, 48, 50, 2, 0, 0, 50, 2, 4, 4, 6], "width": 117, "height": 102}}, "shent3": {"shent3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [589.65, 7.95, 279.6, -398.24, -153.62, -67.56, 156.43, 338.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 214, "height": 228}}, "fashi2": {"fashi2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-177.3, -45.73, 8.21, 162.66, 174.77, 14.38, -10.74, -194.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 279, "height": 223}}, "tiankong": {"tiankong": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [749, -756, -753, -756, -753, 746, 749, 746], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 378, "height": 378}}}}], "animations": {"animation1": {"slots": {"texiao2": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "color": "ffffff74"}, {"time": 3.8667, "color": "ffffffde"}, {"time": 5.9333, "color": "ffffff6c"}, {"time": 8, "color": "ffffffff"}]}, "texiao1": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "color": "ffffffa9"}, {"time": 8, "color": "ffffffff"}]}}, "bones": {"shenti_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 2.4, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 8, "y": 20, "curve": 0.344, "c2": 0.37, "c3": 0.689, "c4": 0.74}, {"time": 3, "x": -7.42, "y": 6.44, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 4.6667, "x": -72, "y": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 31.77, "y": 24.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "shenti4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 2.4, "curve": 0.344, "c2": 0.37, "c3": 0.689, "c4": 0.74}, {"time": 3.4667, "angle": 0.77, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 4.6667, "angle": -6, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.6667, "angle": -0.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 6.2, "angle": 6.71, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 7.1333, "angle": 0.79, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 8}]}, "shent3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 2.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 3.6, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6.6667, "angle": 7.97, "curve": 0.366, "c3": 0.752}, {"time": 8}], "translate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tui2": {"translate": [{"x": -58}, {"time": 4, "x": -94.93, "y": 1.41}, {"time": 8, "x": -58}]}, "tui44": {"translate": [{"x": -196.54, "y": 167.72}, {"time": 1.9, "x": -196.54, "y": 144.92, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 2.8667, "x": -147.67, "y": 98.25, "curve": 0.324, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 4.2333, "x": -241.34, "y": 177.51, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 4.5667, "x": -242.78, "y": 163.87, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 5.4, "x": -236.58, "y": 80.89, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.2333, "x": -290.17, "y": 93.81, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.4, "x": -358.07, "y": 216.31, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.7333, "x": -264.1, "y": 290.63, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "x": -178.5, "y": 155.36, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 7.9, "x": -196.54, "y": 168.92}, {"time": 8, "x": -196.54, "y": 167.72}]}, "yifu42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -8.21, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -8.84, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.7333, "angle": 2.11, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 8}]}, "yifu49": {"rotate": [{"angle": -6.24, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.5333, "angle": 0.76, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -14.45, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 4.6667, "angle": -6.24, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 7, "angle": -13.8, "curve": 0.268, "c3": 0.618, "c4": 0.42}, {"time": 8, "angle": -6.24}]}, "yifu48": {"rotate": [{"angle": -5.8, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "angle": 0.26, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -14.02, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 4.6667, "angle": -5.8, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 6.9, "angle": -15.61, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -5.8}]}, "yifu47": {"rotate": [{"angle": -2.99, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3, "angle": 0.95, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -11.2, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 4.6667, "angle": -2.99, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 6.7667, "angle": -13.09, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 8, "angle": -2.99}]}, "yifu46": {"rotate": [{"angle": -1.05, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.2, "angle": 1.27, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.26, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 4.6667, "angle": -1.05, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 6.6333, "angle": -11.9, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 8, "angle": -1.05}]}, "yifu45": {"rotate": [{"angle": 0.12, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.0667, "angle": 1.09, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -8.09, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 4.6667, "angle": 0.12, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 6.5, "angle": -12.59, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 8, "angle": 0.12}]}, "yifu44": {"rotate": [{"angle": 1.31, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -6.9, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 4.6667, "angle": 1.31, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 6.3667, "angle": -10.96, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.9667, "angle": 1.53, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 1.31}]}, "yifu43": {"rotate": [{"angle": 0.79, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -7.42, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 4.6667, "angle": 0.79, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 6.2667, "angle": -9.75, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.8333, "angle": 1.86, "curve": 0.334, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 8, "angle": 0.79}]}, "yifu41": {"rotate": [{"angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -14.45, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 4.6667, "angle": -6.24, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 5.2667, "angle": 0.76, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -13.8, "curve": 0.268, "c3": 0.618, "c4": 0.42}, {"time": 8, "angle": -6.24}]}, "yifu40": {"rotate": [{"angle": -5.8, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "angle": 0.26, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -14.02, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 4.6667, "angle": -5.8, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 6.9, "angle": -15.61, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -5.8}]}, "yifu39": {"rotate": [{"angle": -2.99, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3, "angle": 0.95, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -11.2, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 4.6667, "angle": -2.99, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 6.7667, "angle": -13.09, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 8, "angle": -2.99}]}, "yifu38": {"rotate": [{"angle": -1.05, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.2, "angle": 1.27, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.26, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 4.6667, "angle": -1.05, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 6.6333, "angle": -11.9, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 8, "angle": -1.05}]}, "yifu37": {"rotate": [{"angle": 0.12, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.0667, "angle": 1.09, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -8.09, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 4.6667, "angle": 0.12, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 6.5, "angle": -12.59, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 8, "angle": 0.12}]}, "yifu36": {"rotate": [{"angle": 1.31, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -6.9, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 4.6667, "angle": 1.31, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 6.3667, "angle": -10.96, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.9667, "angle": 1.53, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 1.31}]}, "yifu35": {"rotate": [{"angle": 0.79, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -7.42, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 4.6667, "angle": 0.79, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 6.2667, "angle": -9.75, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.8333, "angle": 1.86, "curve": 0.334, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 8, "angle": 0.79}]}, "yifu34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -8.21, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -8.84, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.7333, "angle": 2.11, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 8}]}, "shenti_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -6, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -14.57, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shenti_3": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tou3": {"translate": [{"x": -7.23, "curve": "stepped"}, {"time": 4.6667, "x": -7.23, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "x": -10.48, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": -7.23}]}, "tou2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -9.6, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 4.6667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.6667, "angle": 6.29, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.3333, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yifu4": {"rotate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": -0.39, "curve": 0.333, "c2": 0.33, "c3": 0.674, "c4": 0.69}, {"time": 3, "angle": 1.03, "curve": 0.356, "c2": 0.42, "c3": 0.697, "c4": 0.78}, {"time": 4, "angle": 8.03, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.6667, "angle": 7.92, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.32, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": 20.51, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 4.89, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"time": 2, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 4.6667, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": 1.174, "curve": 0.27, "c3": 0.619, "c4": 0.41}, {"time": 6.8667, "x": 1.183, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 7.6, "x": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zhizhusi3": {"rotate": [{"angle": 0.02, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.7, "angle": 3.86, "curve": 0.328, "c2": 0.29, "c3": 0.661, "c4": 0.63}, {"time": 6.4333, "angle": -0.67, "curve": 0.332, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 6.7667, "angle": 0.54, "curve": 0.329, "c2": 0.31, "c3": 0.662, "c4": 0.64}, {"time": 8, "angle": 0.02}], "scale": [{"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5333, "x": 1.02, "curve": 0.329, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 6.4333, "x": 1.041, "curve": 0.343, "c2": 0.37, "c3": 0.677, "c4": 0.71}, {"time": 6.7, "x": 1.013, "curve": 0.341, "c2": 0.37, "c3": 0.675, "c4": 0.71}, {"time": 7.0333, "x": 1.025, "curve": 0.342, "c2": 0.39, "c3": 0.676, "c4": 0.73}, {"time": 7.3667, "x": 1.002, "curve": 0.354, "c2": 0.62, "c3": 0.689, "c4": 0.97}, {"time": 8}]}, "zhizhusi1": {"rotate": [{"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.84, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "angle": -26.29, "curve": "stepped"}, {"time": 6.9333, "angle": -26.29, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": -4.74, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"x": 0.88, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 0.3333, "x": 0.806, "curve": 0.308, "c2": 0.24, "c3": 0.757}, {"time": 2.1333, "x": 0.72, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "x": 0.886, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "x": 0.878, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "x": 0.679, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 0.88}]}, "zhizhusi10": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "x": 1.025, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zhizhusi13": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "x": 1.025, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuoshou2": {"translate": [{"x": -14, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "y": -24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "x": -14, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -10.86, "y": -21.73, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 19.78, "y": -19.82, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": 87.12, "y": 42.59, "curve": "stepped"}, {"time": 6.8, "x": 87.12, "y": 42.59, "curve": 0.356, "c2": 0.65, "c3": 0.691}, {"time": 8, "x": -14}]}, "youshou4": {"rotate": [{"curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "angle": -1.01, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -5.79, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.1333, "angle": -4.72, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 6.7667, "angle": 12.71, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 7.6, "angle": -0.57, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 8}], "scale": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 1.105, "y": 1.105, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "youshou5": {"rotate": [{"curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "angle": -1.01, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -5.79, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.1333, "angle": -4.72, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 6.7667, "angle": -25.69, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 7.6, "angle": -0.57, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 8}], "scale": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.14, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 1.105, "y": 1.105, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "liuhai": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -3.32, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "liuhai6": {"rotate": [{"angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -6.91, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -9.55, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -3.58}]}, "liuhai5": {"rotate": [{"angle": -2.47, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -5.8, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -2.47, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -8.24, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.47}]}, "liuhai4": {"rotate": [{"angle": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -5.11, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "angle": -7.93, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.78}]}, "liuhai2": {"rotate": [{"angle": -1.15, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -4.48, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -1.15, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": -7.67, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.15}]}, "liuhai3": {"rotate": [{"angle": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -3.89, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": -7.53, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.56}]}, "liuhai7": {"rotate": [{"angle": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "angle": -8.76, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.44}]}, "liuhai10": {"rotate": [{"angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -2.52, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": -11.22, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -3.08}]}, "liuhai9": {"rotate": [{"angle": -2.14, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -7.04, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -2.14, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -10.68, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.14}]}, "liuhai8": {"rotate": [{"angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -0.75, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": -10.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.26}]}, "toufa3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -9.09, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "angle": -8.64, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "toufa5": {"rotate": [{"angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -11.13, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -12.96, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.05}]}, "toufa4": {"rotate": [{"angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -10.08, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": -12.56, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.99}]}, "youshou3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 46, "y": 34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -32, "y": -37.28, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.2, "x": -26.1, "y": -30.41, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 6.7333, "x": -68.1, "y": -6.4, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 7.3667, "x": -5.95, "y": 29.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8}]}, "zuoshou": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -18, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 3.8667, "curve": "stepped"}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": 16.8, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuoxiaobi9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 3.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuoxiaobi14": {"rotate": [{"angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 6.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.15}]}, "zuoxiaobi13": {"rotate": [{"angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 8.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.32}]}, "zuoxiaobi12": {"rotate": [{"angle": 2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 6.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 2.93, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.93}]}, "zuoxiaobi11": {"rotate": [{"angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 5.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.7}]}, "zuoxiaobi10": {"rotate": [{"angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 4.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 4.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.87}]}, "zuoxiaobi8": {"rotate": [{"angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 6.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.15}]}, "zuoxiaobi7": {"rotate": [{"angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 8.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.32}]}, "zuoxiaobi6": {"rotate": [{"angle": 2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 6.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 2.93, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.93}]}, "zuoxiaobi5": {"rotate": [{"angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 5.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.7}]}, "zuoxiaobi4": {"rotate": [{"angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 4.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 4.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.87}]}, "zuoxiaobi3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 3.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "youxiaobi7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 5.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.86, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "youxiaobi12": {"rotate": [{"angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 6.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -1.87, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -6.89, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.69}]}, "youxiaobi11": {"rotate": [{"angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 7.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.11, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.13, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.1}]}, "youxiaobi10": {"rotate": [{"angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 6.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.35, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.37, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.4}]}, "youxiaobi9": {"rotate": [{"angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 5.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.6, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.62, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.7}]}, "youxiaobi8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 5.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.86, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "youxiaobi6": {"rotate": [{"angle": 1.69, "curve": "stepped"}, {"time": 4.6667, "angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.69}]}, "youxiaobi5": {"rotate": [{"angle": 2.1, "curve": "stepped"}, {"time": 4.6667, "angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.1}]}, "youxiaobi4": {"rotate": [{"angle": 1.4, "curve": "stepped"}, {"time": 4.6667, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.4}]}, "youxiaobi3": {"rotate": [{"angle": 0.7, "curve": "stepped"}, {"time": 4.6667, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.7}]}, "yifu15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 7.23, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 3.1667, "angle": 5.68, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 6.1333, "angle": -1.41, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": -0.78, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 5.18, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3333, "x": -10.54, "y": 0.26, "curve": 0.369, "c2": 0.48, "c3": 0.719, "c4": 0.88}, {"time": 2, "x": -12.72, "y": -0.69, "curve": 0.351, "c2": 0.65, "c3": 0.686}, {"time": 2.1333, "x": -14.91, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.3333, "x": -11.56, "y": -0.95, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 2.6667, "x": -7.9, "y": -1.06, "curve": 0.331, "c2": 0.33, "c3": 0.674, "c4": 0.69}, {"time": 3, "x": -4.18, "y": -0.95, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 3.1667, "x": -1.6, "y": -0.9, "curve": 0.354, "c2": 0.43, "c3": 0.69, "c4": 0.78}, {"time": 3.3333, "x": 1.24, "y": -0.86, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.4667, "x": 4.17, "y": -0.85, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 9.17, "y": 5.14, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4.0667, "x": 11.19, "y": 5.38, "curve": 0.306, "c2": 0.21, "c3": 0.643, "c4": 0.56}, {"time": 4.1667, "x": 13.75, "y": 6.23, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 4.3, "x": 16.34, "y": 7.75, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 4.6667, "x": 19.6, "y": 9.17, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.3333, "x": 3.53, "y": 5.12, "curve": 0.365, "c2": 0.52, "c3": 0.703, "c4": 0.88}, {"time": 5.4667, "x": 0.55, "y": -1.49, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 5.5, "x": -1.53, "y": -1.9, "curve": 0.317, "c3": 0.651, "c4": 0.35}, {"time": 5.5333, "x": -2.74, "y": -1.77, "curve": 0.291, "c2": 0.13, "c3": 0.632, "c4": 0.5}, {"time": 5.6667, "x": -5.51, "y": 0.09, "curve": 0.315, "c2": 0.28, "c3": 0.659, "c4": 0.64}, {"time": 5.8333, "x": -3.95, "y": 0.85, "curve": 0.347, "c2": 0.38, "c3": 0.691, "c4": 0.75}, {"time": 6, "x": -3.11, "y": 1.71, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 6.1333, "x": 0.78, "y": 1.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.2, "x": 10.15, "y": 1.07, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 6.3, "x": 17.05, "y": 3.81, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5667, "x": 40.11, "y": -7.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.7667, "x": 45, "y": 5.18, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 6.9333, "x": 29.61, "y": 2.53, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 7, "x": 25.27, "y": 1.43, "curve": 0.351, "c2": 0.39, "c3": 0.715, "c4": 0.83}, {"time": 7.4667, "x": 6, "y": 3.07, "curve": 0.352, "c2": 0.48, "c3": 0.687, "c4": 0.82}, {"time": 7.5667, "x": 2.07, "y": -0.19, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 7.6, "x": 0.48, "y": -2.78, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 7.6667, "x": -4.83, "y": -0.23, "curve": 0.319, "c2": 0.23, "c3": 0.653, "c4": 0.57}, {"time": 7.7333, "x": -6.05, "y": -0.13, "curve": 0.317, "c2": 0.26, "c3": 0.652, "c4": 0.6}, {"time": 7.8333, "x": -2.87, "y": -2.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8}]}, "yifu8": {"rotate": [{"angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 5.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 11.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.6333, "angle": -1.41, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -0.78, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 4.58}]}, "yifu7": {"rotate": [{"angle": 10.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 17.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "angle": -6.02, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 11.82, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 10.64}]}, "yifu5": {"rotate": [{"angle": 5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 12.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3667, "angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -5.31, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 9.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.44}]}, "yifu3": {"rotate": [{"angle": 2.45, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 9.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.2667, "angle": -6.92, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 8.17, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.45}]}, "yifu28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 8.28, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yifu33": {"rotate": [{"angle": 2.72, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 11, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 2.72, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.72}]}, "yifu32": {"rotate": [{"angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 13.33, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.04}]}, "yifu31": {"rotate": [{"angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 14.21, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.93}]}, "yifu30": {"rotate": [{"angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 12.24, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.95}]}, "yifu29": {"rotate": [{"angle": 1.98, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 10.26, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.98, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.98}]}, "yifu27": {"rotate": [{"angle": 2.72, "curve": "stepped"}, {"time": 4.6667, "angle": 2.72, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.72}]}, "yifu26": {"rotate": [{"angle": 5.04, "curve": "stepped"}, {"time": 4.6667, "angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.04}]}, "yifu25": {"rotate": [{"angle": 5.93, "curve": "stepped"}, {"time": 4.6667, "angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.93}]}, "yifu24": {"rotate": [{"angle": 3.95, "curve": "stepped"}, {"time": 4.6667, "angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.95}]}, "yifu23": {"rotate": [{"angle": 1.98, "curve": "stepped"}, {"time": 4.6667, "angle": 1.98, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.98}]}, "yifu22": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shenti1": {"translate": [{"time": 5.5}, {"time": 6.3333, "x": -20, "y": -18}, {"time": 8}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "y": 0.96, "curve": "stepped"}, {"time": 3, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.84, "curve": "stepped"}, {"time": 4.6667, "y": 0.84, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "y": 0.87, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 6.3333, "y": 0.799, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "shenti2": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shenti3": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shenti5": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "maozi": {"rotate": [{"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": -3.37, "y": 14.98, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tui23": {"rotate": [{"angle": -39.6, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "angle": -20.4}, {"time": 6.6667, "angle": -49.2, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 8, "angle": -39.6}]}, "zuoshou4": {"rotate": [{"angle": -2.18, "curve": "stepped"}, {"time": 3.8667, "angle": -2.18, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "curve": "stepped"}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "angle": 11.04, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.18}]}, "zuoshou9": {"rotate": [{"angle": -1.09, "curve": "stepped"}, {"time": 3.8667, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": "stepped"}, {"time": 5.5667, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": 11.04, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.09}]}, "zuoshou8": {"rotate": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "angle": 11.04, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuoshou5": {"rotate": [{"curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 2.1333, "angle": 8.4, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": 20.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yaodai": {"rotate": [{"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tengman3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tengman17": {"rotate": [{"angle": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.76}]}, "tengman16": {"rotate": [{"angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.7}]}, "tengman7": {"rotate": [{"angle": 0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.63}]}, "tengman4": {"rotate": [{"angle": 0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.57}]}, "tengman5": {"rotate": [{"angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.38}]}, "tengman2": {"rotate": [{"angle": 0.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.19}]}, "tengman11": {"rotate": [{"angle": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.15}]}, "tengman13": {"rotate": [{"angle": 0.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.18}]}, "tengman14": {"rotate": [{"angle": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.26}]}, "tengman15": {"rotate": [{"angle": 0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.29}]}, "tengman18": {"rotate": [{"angle": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.04}]}, "tengman19": {"rotate": [{"angle": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.07}]}, "meimao1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 10, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "tui11": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui34": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui35": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui10": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui36": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui9": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui14": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui31": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui13": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui32": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui12": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui33": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "zhizhuwang6": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.8667, "y": -38.02}, {"time": 8}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 1.32}, {"time": 8}]}, "xiaotui1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.9333, "y": 30}, {"time": 3.8667}, {"time": 5.9333, "y": 8}, {"time": 8}]}, "xiaotui4": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": 20}, {"time": 3.8667}, {"time": 5.4667, "y": 20}, {"time": 8}]}, "zhizhu2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": -12, "y": 4}, {"time": 5.5, "x": 12.46, "y": -6.15}, {"time": 8}]}, "cao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao7": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}, {"time": 3.4667, "angle": -2.57}, {"time": 6.3333, "angle": 3.56}, {"time": 8, "angle": 1.19}]}, "cao8": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}, {"time": 3.4667, "angle": -2.57}, {"time": 6.3333, "angle": 3.56}, {"time": 8, "angle": 1.19}]}, "cao9": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}, {"time": 3.4667, "angle": -2.57}, {"time": 6.3333, "angle": 3.56}, {"time": 8, "angle": 1.19}]}, "cao10": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}, {"time": 3.4667, "angle": -2.57}, {"time": 6.3333, "angle": 3.56}, {"time": 8, "angle": 1.19}]}, "cao11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "zhizhu1": {"rotate": [{}, {"time": 2.7, "angle": -12}, {"time": 5.5, "angle": 2.4}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "y": -50, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "y": 36, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tengman6": {"rotate": [{}, {"time": 2.7, "angle": 6.31}, {"time": 5.3, "angle": -4.27}, {"time": 8}]}, "tengman20": {"rotate": [{"angle": -0.2}, {"time": 0.1}, {"time": 2.8, "angle": 6.31}, {"time": 5.4333, "angle": -4.27}, {"time": 8, "angle": -0.2}]}, "tengman21": {"rotate": [{"angle": -0.39}, {"time": 0.2333}, {"time": 2.9333, "angle": 6.31}, {"time": 5.5333, "angle": -4.27}, {"time": 8, "angle": -0.39}]}, "tengman9": {"rotate": [{"angle": -0.59}, {"time": 0.3333}, {"time": 3.0333, "angle": 6.31}, {"time": 5.6667, "angle": -4.27}, {"time": 8, "angle": -0.59}]}, "tengman10": {"rotate": [{"angle": -0.79}, {"time": 0.4667}, {"time": 3.1667, "angle": 6.31}, {"time": 5.8, "angle": -4.27}, {"time": 8, "angle": -0.79}]}, "tengman12": {"rotate": [{"angle": -0.99}, {"time": 0.5667}, {"time": 3.2667, "angle": 6.31}, {"time": 5.9333, "angle": -4.27}, {"time": 8, "angle": -0.99}]}, "tengman": {"rotate": [{}, {"time": 3.8667, "angle": 4.06}, {"time": 8}]}, "tengman22": {"rotate": [{}, {"time": 3.8667, "angle": 4.06}, {"time": 8}]}, "tengman23": {"rotate": [{}, {"time": 3.8667, "angle": 4.06}, {"time": 8}]}, "zhizhuwang7": {"scale": [{}, {"time": 2.7, "x": 1.12}, {"time": 5.5, "x": 0.975, "y": 1.2}, {"time": 8}]}, "tui27": {"translate": [{"x": -53.62, "y": 85.83}]}, "tui45": {"translate": [{}, {"time": 2.1333, "x": -58, "y": 62}, {"time": 3.8667, "x": -58, "y": 6}, {"time": 6.1333, "y": 18}, {"time": 8}]}, "heshangtou1": {"rotate": [{}, {"time": 2.3, "angle": 1.2}, {"time": 4.6667, "curve": "stepped"}, {"time": 5.9333}, {"time": 6.7667, "angle": 6}, {"time": 8}]}, "zhizhusi4": {"rotate": [{"angle": 0.02}, {"time": 0.0667}, {"time": 4.7333, "angle": 3.86}, {"time": 6.4667, "angle": -0.86}, {"time": 6.8, "angle": 0.41}, {"time": 8, "angle": 0.02}], "scale": [{"x": 1.001}, {"time": 0.0667}, {"time": 5.5667, "x": 1.024}, {"time": 6.4667, "x": 1.044}, {"time": 6.7333, "x": 1.015}, {"time": 7.0667, "x": 1.026}, {"time": 7.4, "x": 1.007}, {"time": 8, "x": 1.001}]}, "zhizhusi5": {"rotate": [{"angle": 0.03}, {"time": 0.1}, {"time": 4.7667, "angle": 3.86}, {"time": 6.5, "angle": -0.86}, {"time": 6.8333, "angle": 0.41}, {"time": 8, "angle": 0.03}], "scale": [{"x": 1.001}, {"time": 0.1}, {"time": 5.6, "x": 1.024}, {"time": 6.5, "x": 1.044}, {"time": 6.7667, "x": 1.015}, {"time": 7.1, "x": 1.026}, {"time": 7.4333, "x": 1.007}, {"time": 8, "x": 1.001}]}, "zhizhusi6": {"rotate": [{"angle": 0.04}, {"time": 0.1333}, {"time": 4.8, "angle": 3.86}, {"time": 6.5333, "angle": -0.86}, {"time": 6.8667, "angle": 0.41}, {"time": 8, "angle": 0.04}], "scale": [{"x": 1.001}, {"time": 0.1333}, {"time": 5.6333, "x": 1.024}, {"time": 6.5333, "x": 1.044}, {"time": 6.8, "x": 1.015}, {"time": 7.1333, "x": 1.026}, {"time": 7.4667, "x": 1.007}, {"time": 8, "x": 1.001}]}, "zhizhusi7": {"rotate": [{"angle": 0.05}, {"time": 0.1667}, {"time": 4.8333, "angle": 3.86}, {"time": 6.5667, "angle": -0.86}, {"time": 6.9, "angle": 0.41}, {"time": 8, "angle": 0.05}], "scale": [{"x": 1.002}, {"time": 0.1667}, {"time": 5.6667, "x": 1.024}, {"time": 6.5667, "x": 1.044}, {"time": 6.8333, "x": 1.015}, {"time": 7.1667, "x": 1.026}, {"time": 7.5, "x": 1.007}, {"time": 8, "x": 1.002}]}, "zhizhusi8": {"rotate": [{"angle": 0.06}, {"time": 0.2}, {"time": 4.8667, "angle": 3.86}, {"time": 6.6, "angle": -0.86}, {"time": 6.9333, "angle": 0.41}, {"time": 8, "angle": 0.06}], "scale": [{"x": 1.002}, {"time": 0.2}, {"time": 5.7, "x": 1.024}, {"time": 6.6, "x": 1.044}, {"time": 6.8667, "x": 1.015}, {"time": 7.2, "x": 1.026}, {"time": 7.5333, "x": 1.007}, {"time": 8, "x": 1.002}]}, "tui41": {"translate": [{"time": 2.6667}, {"time": 4, "x": 8, "y": 112}, {"time": 5.3333}]}, "tui46": {"translate": [{"time": 3.8667}, {"time": 5.9333, "x": 62, "y": 56}, {"time": 8}]}, "tui17": {"rotate": [{"time": 1.3333}, {"time": 2, "angle": 2.47}, {"time": 3.3333, "angle": -6}, {"time": 8}]}, "tui19": {"rotate": [{}, {"time": 2.6667, "angle": 9.53}, {"time": 4}, {"time": 6, "angle": 25.21}, {"time": 8}]}, "fashi2": {"translate": [{}, {"time": 2, "x": 4.73, "y": 8.58}, {"time": 4}, {"time": 6, "x": 1.49, "y": -1.33}, {"time": 8}]}, "fashi1": {"rotate": [{}, {"time": 2, "angle": -7.2}, {"time": 4}, {"time": 6, "angle": 3.6}, {"time": 8}]}, "tui28": {"translate": [{"x": -108.49, "y": 189.54}]}, "zhizhusi2": {"rotate": [{}, {"time": 4.6667, "angle": 3.86}, {"time": 6.4, "angle": -0.86}, {"time": 6.7333, "angle": 0.41}, {"time": 8}], "scale": [{"curve": 0.225, "c3": 0.842, "c4": 0.67}, {"time": 5.5, "x": 1.017, "curve": 0.64, "c2": 0.41, "c4": 0.75}, {"time": 6.4, "x": 1.038, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 1.017, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.009, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zhizhusi9": {"rotate": [{"angle": 0.08}, {"time": 0.2333}, {"time": 4.9, "angle": 3.86}, {"time": 6.6333, "angle": -0.86}, {"time": 6.9667, "angle": 0.41}, {"time": 8, "angle": 0.08}], "scale": [{"x": 1.003}, {"time": 0.2333}, {"time": 5.7333, "x": 1.026}, {"time": 6.6333, "x": 1.045}, {"time": 6.9, "x": 1.017}, {"time": 7.2333, "x": 1.027}, {"time": 7.5667, "x": 1.008}, {"time": 8, "x": 1.003}]}, "shenti_4": {"rotate": [{}, {"time": 2, "angle": -5.11}, {"time": 3.1667, "angle": -1.77}, {"time": 4.6667}, {"time": 6.1667, "angle": -14.31}, {"time": 6.8333}]}, "shenti_6": {"rotate": [{}, {"time": 2, "angle": -5.11}, {"time": 3.1667, "angle": -1.77}, {"time": 4.6667}, {"time": 6.1667, "angle": -14.31}, {"time": 6.8333}]}, "shenti_5": {"rotate": [{"time": 0.1}, {"time": 2.1, "angle": -1.97}, {"time": 3.2667, "curve": "stepped"}, {"time": 4.7667}, {"time": 6.2667, "angle": -3.72}, {"time": 6.9333}]}, "shenti_7": {"rotate": [{"time": 0.2}, {"time": 2.2, "angle": -1.97}, {"time": 3.3667, "curve": "stepped"}, {"time": 4.8667}, {"time": 6.3667, "angle": -3.72}, {"time": 7.0333}]}, "shenti_8": {"rotate": [{"time": 0.1}, {"time": 2.1, "angle": -1.97}, {"time": 3.2667, "curve": "stepped"}, {"time": 4.7667}, {"time": 6.2667, "angle": -3.72}, {"time": 6.9333}]}, "shenti_9": {"rotate": [{"time": 0.2}, {"time": 2.2, "angle": -1.97}, {"time": 3.3667, "curve": "stepped"}, {"time": 4.8667}, {"time": 6.3667, "angle": -3.72}, {"time": 7.0333}]}}, "deform": {"default": {"zhengyan2": {"zhengyan2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5333, "vertices": [-6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, 0, 0, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598], "curve": 0.25, "c3": 0.75}, {"time": 2.7, "vertices": [4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 0, 0, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "vertices": [-2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, 0, 0, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042], "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "vertices": [0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0, 0, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998], "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "vertices": [9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 0, 0, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555], "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "vertices": [-4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, 0, 0, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176], "curve": 0.25, "c3": 0.75}, {"time": 7, "vertices": [6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 0, 0, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103], "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuiba2": {"zuiba2": [{"curve": 0.25, "c3": 0.75}, {"time": 2.3, "offset": 1, "vertices": [-5.43174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "offset": 1, "vertices": [-5.43174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174], "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "offset": 1, "vertices": [4.28555, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555], "curve": 0.25, "c3": 0.75}, {"time": 6.3, "offset": 1, "vertices": [-8.4553, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.00012, -8.1646, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553], "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "offset": 1, "vertices": [-0.26362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.00091, 0.01356, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362], "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "beijing": {"beijing": [{}, {"time": 2.2, "offset": 92, "vertices": [9.82678, -23.01019, -9.46704, -28.31464, -21.1499, 0.29532, 0, 0, 0, 3.14999, 4.35834, 2.72211, 4.09698, -0.44383, 2.10614, -3.40132, 2.4989, -13.00531, -13.23248, -5.39326, -21.43646, -7.44534, -29.84369, -9.5484, -33.97766, -4.49541, -39.00342, 1.37447, -44.00293, -3.28349, -49.6925, -8.58485, -49.58148, -3.3138, -44.21967, 3.32774, -39.62585, 7.25298, -32.1991, 5.55428, -25.31897, 2.66777, -22.20715, 8.11598, -3.33008, -1.72404, -3.67157, 0.31425, -4.01953, 2.3925, -2.54858, 4.96649, 0, 0, 0, 0, 0, 0, 0, 0, -24.15628, 20.76154, -2.87283, 20.63094, 17.35779, 20.5069, 29.12827, 7.7648, 39.83762, -3.8287, 54.02133, -1.75333, 55.64844, 12.89526, 57.20676, 26.92538, 64.07932, 30.95538, 72.29013, 35.7699, 84.84695, 34.8938, 96.36478, 34.09012, 101.52948, 33.06119, 90.8699, 28.49261, 79.70169, 23.70651, 72.87036, 13.88779, 78.7554, 6.79379, 83.5874, 0.96902, 76.20221, 0.32733, 71.23553, -0.10425, 72.81033, -20.10492, 73.87735, -33.65601, 74.73599, -44.56168, 59.43839, -37.45895, 39.17212, -28.04926, 18.01587, -18.22614, 7.05228, -21.46567, 0, 0, 0, 0, -9.92502, 2.64841, -9.203, 1.97772, -7.28992, 2.30765, -5.92465, 2.543, -4.10922, 2.85605, -3.14288, 2.53394, -3.49097, 1.97006, -1.97766, 1.31622, -0.44785, 0.65521, 0.9895, 0.03424, 2.83304, -0.1734, 3.11755, -1.40912, 3.92563, -2.10229, 5.4422, -0.51111, 4.97055, -1.57983, 4.15851, -2.45703, 5.30563, -2.85162, 6.75476, -3.35028, 8.60873, -3.98804, 10.07605, -4.49298]}, {"time": 3.4667, "offset": 110, "vertices": [-0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.59601, 0, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999]}, {"time": 4.6667, "vertices": [1.18182, 1.44012, 1.22052, 1.38226, 1.25336, 1.20117, 1.20441, 1.04382, 1.11789, 0.91089, 1.16599, 0.92151, 1.23593, 0.88208, 1.28192, 0.77307, 1.35336, 0.79724, 1.43973, 0.81714, 1.444, 0.8009, 1.44458, 0.71393, 1.36081, 0.68591, 1.36908, 0.62921, 1.44608, 0.62805, 1.44611, -0.57642, 1.29269, -0.75787, 1.27283, -0.86438, 0.98651, -1.03894, 0.95969, -1.06433, 0.95264, -1.12756, 0.95282, -1.14844, 1.01715, -1.26855, 1.04388, -1.26868, 1.17566, -1.19647, 1.23749, -1.16199, 1.39261, -1.05664, 1.42557, -1.03992, 1.39484, -1.06793, 1.12958, -1.23987, 1.13129, -1.28894, 1.08844, -1.30951, 1.09302, -1.33478, 1.17169, -1.38574, 1.1333, -1.46899, 1.18866, -1.50531, 1.44849, -1.54266, 1.44849, -1.5658, 1.44452, -1.56903, 1.38556, -1.56921, 1.19196, -1.50665, 1.14081, -1.52722, 1.09277, -1.5694, 0.0351, -1.56862, 0.0033, -1.46363, -0.06134, -1.44906, -2.3801, 17.55069, 8.01426, 14.99212, 6.00269, -1.41769, -0.18359, -1.42934, -0.52288, -5.30134, -7.4184, -3.72277, -6.03629, 0.64165, -3.6107, 4.15547, -1.02724, 7.81765, 1.76905, 11.80198, 9.681, 10.10013, 19.99668, 8.36045, 21.48577, 2.1216, 23.41338, -5.22162, 29.82986, -3.24608, 37.13207, -0.99768, 34.77478, -5.61016, 27.18805, -9.18402, 20.52608, -13.07011, 15.60697, -6.00117, 10.76081, -0.50077, 5.66293, 2.08065, 3.48865, 0.05511, 4.63544, -2.91574, 4.4576, -5.94733, 3.23454, -7.2295, 0.80653, -4.55015, 0.11602, -3.35597, 0.28711, -2.59113, -0.46515, -0.99345, -2.49125, -1.87234, -9.36551, -5.65552, -15.89983, -9.2514, -21.97852, -7.26529, -27.50931, -5.45827, -31.70191, -8.67528, -29.59287, -13.68296, -27.57303, -18.47908, -29.06101, -21.01143, -30.83863, -24.03675, -35.03775, -26.01148, -38.88983, -27.82283, -40.73718, -28.4198, -38.12707, -25.03354, -35.39245, -21.48578, -34.95791, -17.09759, -38.12732, -15.87183, -40.72946, -14.86524, -38.46767, -13.33138, -36.94637, -12.29987, -41.04789, -6.14507, -43.82681, -1.97484, -46.06311, 1.38117, -39.86259, 1.84427, -31.64802, 2.45764, -23.07279, 3.09783, -20.126, 6.11107, -0.59378, -0.90594, -0.66904, -0.88834, 5.16564, -2.12567, 4.6913, -1.72729, 3.5005, -1.99451, 2.6507, -2.18494, 1.52069, -2.43834, 0.90383, -2.26642, 1.10483, -1.90156, 0.13415, -1.53827, -0.84706, -1.17133, -1.76889, -0.82638, -2.93289, -0.7539, -3.1503, 0.01303, -3.67931, 0.42288, -4.58166, -0.62376, -4.31901, 0.06208, -3.83675, 0.63836, -4.56926, 0.85019, -5.49478, 1.1177, -6.67872, 1.46017, -7.61589, 1.73111, -1.562, 0.18073, -1.48486, 0.18103, -1.45663, 0.24927, -1.56219, 0.52448, -1.56366, 1.43701]}, {"time": 5.5, "offset": 92, "vertices": [-3.74524, 32.40563, 13.98761, 28.03506, 10.54736, 0.03893, 0, 0, -0.315, -6.93001, -11.98181, -4.15163, -9.61292, 3.32152, -5.43359, 9.34134, -0.98376, 15.61517, 4.02699, 21.38978, 17.51319, 18.48577, 35.07435, 15.50982, 37.60932, 4.8758, 40.89075, -7.64071, 51.82777, -4.27703, 64.27448, -0.44863, 60.25465, -8.3088, 47.32209, -14.39605, 36.17442, -19.9705, 27.58533, -8.96552, 19.3285, 0.41174, 10.65888, 4.75373, 6.72162, 2.31607, 8.66003, -2.77576, 8.35425, -7.96707, 6.23438, -10.2005, 2.06989, -5.74202, 0.94495, -3.78, 1.26007, -2.51999, 0, 0, -3.37918, -1.41428, -14.9292, -7.76579, -25.9079, -13.80298, -36.11835, -10.46338, -45.40842, -7.4248, -52.45312, -12.827, -48.91309, -21.2403, -45.52234, -29.29828, -48.02325, -33.55151, -51.01093, -38.63287, -58.06622, -41.94791, -64.53802, -44.98865, -67.64178, -45.99057, -63.25519, -40.30347, -58.65948, -34.34509, -57.92743, -26.97351, -63.25085, -24.91281, -67.62167, -23.2207, -63.82126, -20.64508, -61.26532, -18.91312, -68.15204, -8.57138, -72.81793, -1.56464, -76.573, 4.07437, -66.15659, 4.84888, -52.35675, 5.87497, -37.95102, 6.94614, -32.99921, 12.00629, 0, 0, 0, 0, 10.01126, -2.14676, 9.25943, -1.51474, 7.37112, -1.93759, 6.02359, -2.23932, 4.23172, -2.64056, 3.25381, -2.36746, 3.57257, -1.78903, 2.03375, -1.21277, 0.47815, -0.63025, -0.98337, -0.08289, -2.82892, 0.0329, -3.1731, 1.24896, -4.01178, 1.89917, -5.44302, 0.24017, -5.02621, 1.32751, -4.26117, 2.24091, -5.42258, 2.57709, -6.88983, 3.00183, -8.76694, 3.54541, -10.25269, 3.97565]}, {"time": 8}]}, "zui1": {"zui1": [{}, {"time": 2.3, "offset": 5, "vertices": [1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523]}, {"time": 3, "vertices": [1.96307, 2.37286, 1.96309, 9.49796, 1.96309, 10.19787, 1.96309, 10.19787, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 0, 0, 0, 0, 0, 0, 0, 0, 1.96307, 2.37286, 1.96307, 2.37286], "curve": "stepped"}, {"time": 4.3333, "vertices": [1.96307, 2.37286, 1.96309, 9.49796, 1.96309, 10.19787, 1.96309, 10.19787, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 0, 0, 0, 0, 0, 0, 0, 0, 1.96307, 2.37286, 1.96307, 2.37286]}, {"time": 5.6667, "offset": 5, "vertices": [3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312]}, {"time": 6.6667, "offset": 1, "vertices": [7.77221, 0.00029, 7.77221, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, 0.00029, 7.77221]}, {"time": 8}]}, "zhengyan": {"zhengyan": [{"time": 5.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "vertices": [-4.7141, 0.95439, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.59673, 0.74771, -6.38286, 1.1893, -6.81839, 2.99634, -8.93375, 2.34581, -7.53339, 3.23163, -7.31557, 2.32794, -4.4025, -0.13611, 0, 0, 0, 0, -1.68578, -0.97916, -4.76169, 0.03279, -3.37149, -1.95874, -4.18311, -3.90744, -4.96214, -1.09451, -5.52074, -2.27425, -3.3815, 0.60139, -2.81795, 0.50104], "curve": 0.25, "c3": 0.75}, {"time": 6, "vertices": [-6.38841, 2.37392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.58324, -0.08858, -9.05522, 2.15053, -15.46457, 2.285, -16.36095, 3.21571, -15.63463, 4.7657, -15.283, 2.91374, -9.60873, 1.68388, 0, 0, 0, 0, -3.9016, -1.51981, -8.42764, -1.97772, -9.6311, -4.77531, -11.63216, -7.84148, -11.46127, -3.00735, -14.04118, -3.23714, -11.17683, 0.37343, -9.80646, 1.85551], "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "vertices": [-8.83954, 2.68651, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -15.16486, -1.22681, -16.63028, 1.16595, -21.48737, 4.62735, -23.76025, 8.95308, -21.86243, 8.45015, -21.57861, 3.66949, -11.87799, 3.05421, 0, 0, 0, 0, -7.02391, -1.30966, -15.29187, -1.99545, -19.65724, -5.44737, -28.40324, -5.938, -25.54823, -0.69411, -29.6124, -1.32204, -21.53853, -0.34981, -13.50874, 2.49213], "curve": 0.25, "c3": 0.75}, {"time": 6.1, "vertices": [-8.83954, 2.68651, 0, 0, 0, 0, 7.20151, -5.27103, 9.00171, -6.58862, 12.13271, -8.8804, 12.52406, -9.16692, 9.86296, -7.21904, 7.51471, -5.50017, 2.89627, -2.1199, 0, 0, 0, 0, 0, 0, 0, 0, 1.91769, -1.40372, 3.51556, -2.57317, 6.4716, -4.73689, 9.50797, -6.95927, 10.1655, -7.44052, 9.78914, -7.16508, 7.5804, -5.54851, 1.35823, -0.99414, 0, 0, 0, 0, 0, 0, -15.16486, -1.22681, -16.63028, 1.16595, -21.48737, 4.62735, -23.76025, 8.95308, -21.86243, 8.45015, -21.57861, 3.66949, -11.87799, 3.05421, 0, 0, 0, 0, -7.02391, -1.30966, -15.29187, -1.99545, -19.65724, -5.44737, -26.05463, -7.65694, -25.54823, -0.69411, -29.6124, -1.32204, -21.53853, -0.34981, -13.50874, 2.49213], "curve": "stepped"}, {"time": 6.1667, "vertices": [-8.83954, 2.68651, 0, 0, 0, 0, 7.20151, -5.27103, 9.00171, -6.58862, 12.13271, -8.8804, 12.52406, -9.16692, 9.86296, -7.21904, 7.51471, -5.50017, 2.89627, -2.1199, 0, 0, 0, 0, 0, 0, 0, 0, 1.91769, -1.40372, 3.51556, -2.57317, 6.4716, -4.73689, 9.50797, -6.95927, 10.1655, -7.44052, 9.78914, -7.16508, 7.5804, -5.54851, 1.35823, -0.99414, 0, 0, 0, 0, 0, 0, -15.16486, -1.22681, -16.63028, 1.16595, -21.48737, 4.62735, -23.76025, 8.95308, -21.86243, 8.45015, -21.57861, 3.66949, -11.87799, 3.05421, 0, 0, 0, 0, -7.02391, -1.30966, -15.29187, -1.99545, -19.65724, -5.44737, -26.05463, -7.65694, -25.54823, -0.69411, -29.6124, -1.32204, -21.53853, -0.34981, -13.50874, 2.49213], "curve": 0.25, "c3": 0.75}, {"time": 6.2, "vertices": [-8.83954, 2.68651, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -15.16486, -1.22681, -16.63028, 1.16595, -21.48737, 4.62735, -23.76025, 8.95308, -21.86243, 8.45015, -21.57861, 3.66949, -11.87799, 3.05421, 0, 0, 0, 0, -7.02391, -1.30966, -15.29187, -1.99545, -19.65724, -5.44737, -28.40324, -5.938, -25.54823, -0.69411, -29.6124, -1.32204, -21.53853, -0.34981, -13.50874, 2.49213], "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "vertices": [-6.38841, 2.37392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.58324, -0.08858, -9.05522, 2.15053, -15.46457, 2.285, -16.36095, 3.21571, -15.63463, 4.7657, -15.283, 2.91374, -9.60873, 1.68388, 0, 0, 0, 0, -3.9016, -1.51981, -8.42764, -1.97772, -9.6311, -4.77531, -11.63216, -7.84148, -11.46127, -3.00735, -14.04118, -3.23714, -11.17683, 0.37343, -9.80646, 1.85551], "curve": 0.25, "c3": 0.75}, {"time": 6.3, "vertices": [-4.7141, 0.95439, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.59673, 0.74771, -6.38286, 1.1893, -6.81839, 2.99634, -8.93375, 2.34581, -7.53339, 3.23163, -7.31557, 2.32794, -4.4025, -0.13611, 0, 0, 0, 0, -1.68578, -0.97916, -4.76169, 0.03279, -3.37149, -1.95874, -4.18311, -3.90744, -4.96214, -1.09451, -5.52074, -2.27425, -3.3815, 0.60139, -2.81795, 0.50104], "curve": 0.25, "c3": 0.75}, {"time": 6.3333}]}, "zhizhuwang": {"zhizhuwang": [{}, {"time": 3.8667, "offset": 61, "vertices": [5.62988, -0.00397, 6.13362, 3.66437, 2.63651, 10.16467, 2.66815, 16.45587, 1.46213, 23.50427, -3.0134, 29.23077, -6.64969, 30.58453, -9.58052, 23.71869, -5.83675, 13.48981, -0.25937, 6.93219, 3.31679]}, {"time": 8}]}, "youshangbi": {"youshangbi": [{"time": 4.6667}, {"time": 5.5, "offset": 32, "vertices": [1.92483, -0.64104]}, {"time": 5.9333}, {"time": 6.5667, "offset": 32, "vertices": [-12.85551, -12.90375, -6.52467, -7.56164, -5.26447, -6.45457, -3.17657, -3.31067, -2.20013, -1.80358]}, {"time": 6.7667, "offset": 32, "vertices": [-12.9352, -12.21136, -6.0814, -5.60994, -4.36633, -5.97604, -4.23542, -4.41423, -2.93351, -2.40477]}, {"time": 8}]}}}, "drawOrder": [{"offsets": [{"slot": "tui15", "offset": 15}]}]}, "animation2": {"slots": {"tui5": {"attachment": [{"name": null}]}, "zhizhuwang6": {"attachment": [{"name": null}]}, "qianjing1": {"attachment": [{"name": null}]}, "yingzi": {"attachment": [{"name": null}]}, "beijing": {"attachment": [{"name": null}]}, "texiao2": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "color": "ffffff74"}, {"time": 3.8667, "color": "ffffffde"}, {"time": 5.9333, "color": "ffffff6c"}, {"time": 8, "color": "ffffffff"}]}, "tui6": {"attachment": [{"name": null}]}, "tui8": {"attachment": [{"name": null}]}, "tu2": {"attachment": [{"name": null}]}, "tengman": {"attachment": [{"name": null}]}, "tui7": {"attachment": [{"name": null}]}, "texiao1": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "color": "ffffffa9"}, {"time": 8, "color": "ffffffff"}]}, "zhizhu2": {"attachment": [{"name": null}]}, "zhizhuwang7": {"attachment": [{"name": null}]}, "tui3": {"attachment": [{"name": null}]}, "fazhang": {"attachment": [{"name": null}]}, "cao": {"attachment": [{"name": null}]}, "zhizhu1": {"attachment": [{"name": null}]}, "tui4": {"attachment": [{"name": null}]}, "tiankong": {"attachment": [{"name": null}]}, "temang2": {"attachment": [{"name": null}]}, "bian": {"attachment": [{"name": null}]}, "zhizhuwang": {"attachment": [{"name": null}]}, "qianjing2": {"attachment": [{"name": null}]}, "tui1": {"attachment": [{"name": null}]}, "zhizhu3": {"attachment": [{"name": null}]}}, "bones": {"shenti_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 2.4, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 8, "y": 20, "curve": 0.344, "c2": 0.37, "c3": 0.689, "c4": 0.74}, {"time": 3, "x": -7.42, "y": 6.44, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 4.6667, "x": -72, "y": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 31.77, "y": 24.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "shenti4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 2.4, "curve": 0.344, "c2": 0.37, "c3": 0.689, "c4": 0.74}, {"time": 3.4667, "angle": 0.77, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 4.6667, "angle": -6, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.6667, "angle": -0.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 6.2, "angle": 6.71, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 7.1333, "angle": 0.79, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 8}]}, "shent3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 2.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 3.6, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6.6667, "angle": 7.97, "curve": 0.366, "c3": 0.752}, {"time": 8}], "translate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tui2": {"translate": [{"x": -58}, {"time": 4, "x": -94.93, "y": 1.41}, {"time": 8, "x": -58}]}, "tui44": {"translate": [{"x": -196.54, "y": 167.72}, {"time": 1.9, "x": -196.54, "y": 144.92, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 2.8667, "x": -147.67, "y": 98.25, "curve": 0.324, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 4.2333, "x": -241.34, "y": 177.51, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 4.5667, "x": -242.78, "y": 163.87, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 5.4, "x": -236.58, "y": 80.89, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.2333, "x": -290.17, "y": 93.81, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.4, "x": -358.07, "y": 216.31, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.7333, "x": -264.1, "y": 290.63, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "x": -178.5, "y": 155.36, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 7.9, "x": -196.54, "y": 168.92}, {"time": 8, "x": -196.54, "y": 167.72}]}, "yifu42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -8.21, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -8.84, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.7333, "angle": 2.11, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 8}]}, "yifu49": {"rotate": [{"angle": -6.24, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.5333, "angle": 0.76, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -14.45, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 4.6667, "angle": -6.24, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 7, "angle": -13.8, "curve": 0.268, "c3": 0.618, "c4": 0.42}, {"time": 8, "angle": -6.24}]}, "yifu48": {"rotate": [{"angle": -5.8, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "angle": 0.26, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -14.02, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 4.6667, "angle": -5.8, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 6.9, "angle": -15.61, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -5.8}]}, "yifu47": {"rotate": [{"angle": -2.99, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3, "angle": 0.95, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -11.2, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 4.6667, "angle": -2.99, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 6.7667, "angle": -13.09, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 8, "angle": -2.99}]}, "yifu46": {"rotate": [{"angle": -1.05, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.2, "angle": 1.27, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.26, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 4.6667, "angle": -1.05, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 6.6333, "angle": -11.9, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 8, "angle": -1.05}]}, "yifu45": {"rotate": [{"angle": 0.12, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.0667, "angle": 1.09, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -8.09, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 4.6667, "angle": 0.12, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 6.5, "angle": -12.59, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 8, "angle": 0.12}]}, "yifu44": {"rotate": [{"angle": 1.31, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -6.9, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 4.6667, "angle": 1.31, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 6.3667, "angle": -10.96, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.9667, "angle": 1.53, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 1.31}]}, "yifu43": {"rotate": [{"angle": 0.79, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -7.42, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 4.6667, "angle": 0.79, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 6.2667, "angle": -9.75, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.8333, "angle": 1.86, "curve": 0.334, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 8, "angle": 0.79}]}, "yifu41": {"rotate": [{"angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -14.45, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 4.6667, "angle": -6.24, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 5.2667, "angle": 0.76, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -13.8, "curve": 0.268, "c3": 0.618, "c4": 0.42}, {"time": 8, "angle": -6.24}]}, "yifu40": {"rotate": [{"angle": -5.8, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "angle": 0.26, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -14.02, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 4.6667, "angle": -5.8, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 6.9, "angle": -15.61, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -5.8}]}, "yifu39": {"rotate": [{"angle": -2.99, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3, "angle": 0.95, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -11.2, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 4.6667, "angle": -2.99, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 6.7667, "angle": -13.09, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 8, "angle": -2.99}]}, "yifu38": {"rotate": [{"angle": -1.05, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.2, "angle": 1.27, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.26, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 4.6667, "angle": -1.05, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 6.6333, "angle": -11.9, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 8, "angle": -1.05}]}, "yifu37": {"rotate": [{"angle": 0.12, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.0667, "angle": 1.09, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -8.09, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 4.6667, "angle": 0.12, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 6.5, "angle": -12.59, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 8, "angle": 0.12}]}, "yifu36": {"rotate": [{"angle": 1.31, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -6.9, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 4.6667, "angle": 1.31, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 6.3667, "angle": -10.96, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.9667, "angle": 1.53, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 1.31}]}, "yifu35": {"rotate": [{"angle": 0.79, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -7.42, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 4.6667, "angle": 0.79, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 6.2667, "angle": -9.75, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.8333, "angle": 1.86, "curve": 0.334, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 8, "angle": 0.79}]}, "yifu34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -8.21, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -8.84, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 7.7333, "angle": 2.11, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 8}]}, "shenti_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -6, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -14.57, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shenti_3": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tou3": {"translate": [{"x": -7.23, "curve": "stepped"}, {"time": 4.6667, "x": -7.23, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "x": -10.48, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": -7.23}]}, "tou2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -9.6, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 4.6667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.6667, "angle": 6.29, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.3333, "angle": 12.34, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yifu4": {"rotate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": -0.39, "curve": 0.333, "c2": 0.33, "c3": 0.674, "c4": 0.69}, {"time": 3, "angle": 1.03, "curve": 0.356, "c2": 0.42, "c3": 0.697, "c4": 0.78}, {"time": 4, "angle": 8.03, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.6667, "angle": 7.92, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.32, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": 20.51, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 4.89, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"time": 2, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 4.6667, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": 1.174, "curve": 0.27, "c3": 0.619, "c4": 0.41}, {"time": 6.8667, "x": 1.183, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 7.6, "x": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zhizhusi3": {"rotate": [{"angle": 0.02, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.7, "angle": 3.86, "curve": 0.328, "c2": 0.29, "c3": 0.661, "c4": 0.63}, {"time": 6.4333, "angle": -0.67, "curve": 0.332, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 6.7667, "angle": 0.54, "curve": 0.329, "c2": 0.31, "c3": 0.662, "c4": 0.64}, {"time": 8, "angle": 0.02}], "scale": [{"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5333, "x": 1.02, "curve": 0.329, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 6.4333, "x": 1.041, "curve": 0.343, "c2": 0.37, "c3": 0.677, "c4": 0.71}, {"time": 6.7, "x": 1.013, "curve": 0.341, "c2": 0.37, "c3": 0.675, "c4": 0.71}, {"time": 7.0333, "x": 1.025, "curve": 0.342, "c2": 0.39, "c3": 0.676, "c4": 0.73}, {"time": 7.3667, "x": 1.002, "curve": 0.354, "c2": 0.62, "c3": 0.689, "c4": 0.97}, {"time": 8}]}, "zhizhusi1": {"rotate": [{"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.84, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "angle": -26.29, "curve": "stepped"}, {"time": 6.9333, "angle": -26.29, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": -4.74, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"x": 0.88, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 0.3333, "x": 0.806, "curve": 0.308, "c2": 0.24, "c3": 0.757}, {"time": 2.1333, "x": 0.72, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "x": 0.886, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "x": 0.878, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "x": 0.679, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 0.88}]}, "zhizhusi10": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "x": 1.025, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zhizhusi13": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "x": 1.025, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuoshou2": {"translate": [{"x": -14, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "y": -24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "x": -14, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -10.86, "y": -21.73, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 19.78, "y": -19.82, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": 87.12, "y": 42.59, "curve": "stepped"}, {"time": 6.8, "x": 87.12, "y": 42.59, "curve": 0.356, "c2": 0.65, "c3": 0.691}, {"time": 8, "x": -14}]}, "youshou4": {"rotate": [{"curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "angle": -1.01, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -5.79, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.1333, "angle": -4.72, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 6.7667, "angle": 12.71, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 7.6, "angle": -0.57, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 8}], "scale": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 1.105, "y": 1.105, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "youshou5": {"rotate": [{"curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "angle": -1.01, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -5.79, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.1333, "angle": -4.72, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 6.7667, "angle": -25.69, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 7.6, "angle": -0.57, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 8}], "scale": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.14, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "x": 1.105, "y": 1.105, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "liuhai": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -3.32, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "liuhai6": {"rotate": [{"angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -6.91, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -9.55, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -3.58}]}, "liuhai5": {"rotate": [{"angle": -2.47, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -5.8, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -2.47, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -8.24, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.47}]}, "liuhai4": {"rotate": [{"angle": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -5.11, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "angle": -7.93, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.78}]}, "liuhai2": {"rotate": [{"angle": -1.15, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -4.48, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -1.15, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": -7.67, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.15}]}, "liuhai3": {"rotate": [{"angle": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -3.89, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": -7.53, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.56}]}, "liuhai7": {"rotate": [{"angle": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "angle": -8.76, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.44}]}, "liuhai10": {"rotate": [{"angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -2.52, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": -11.22, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -3.08}]}, "liuhai9": {"rotate": [{"angle": -2.14, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -7.04, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -2.14, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -10.68, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.14}]}, "liuhai8": {"rotate": [{"angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -0.75, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": -10.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.26}]}, "toufa3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -9.09, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "angle": -8.64, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "toufa5": {"rotate": [{"angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -11.13, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -12.96, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.05}]}, "toufa4": {"rotate": [{"angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -10.08, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": -12.56, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.99}]}, "youshou3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 46, "y": 34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -32, "y": -37.28, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.2, "x": -26.1, "y": -30.41, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 6.7333, "x": -68.1, "y": -6.4, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 7.3667, "x": -5.95, "y": 29.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8}]}, "zuoshou": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -18, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 3.8667, "curve": "stepped"}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": 16.8, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuoxiaobi9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 3.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuoxiaobi14": {"rotate": [{"angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 6.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.15}]}, "zuoxiaobi13": {"rotate": [{"angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 8.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.32}]}, "zuoxiaobi12": {"rotate": [{"angle": 2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 6.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 2.93, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.93}]}, "zuoxiaobi11": {"rotate": [{"angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 5.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.7}]}, "zuoxiaobi10": {"rotate": [{"angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 4.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 4.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.87}]}, "zuoxiaobi8": {"rotate": [{"angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 6.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.15}]}, "zuoxiaobi7": {"rotate": [{"angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 8.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.32}]}, "zuoxiaobi6": {"rotate": [{"angle": 2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 6.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 2.93, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.93}]}, "zuoxiaobi5": {"rotate": [{"angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 5.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.7}]}, "zuoxiaobi4": {"rotate": [{"angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 4.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 4.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.87}]}, "zuoxiaobi3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 3.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "angle": 3.15, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "youxiaobi7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 5.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.86, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "youxiaobi12": {"rotate": [{"angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 6.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -1.87, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -6.89, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.69}]}, "youxiaobi11": {"rotate": [{"angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 7.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.11, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.13, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.1}]}, "youxiaobi10": {"rotate": [{"angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 6.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.35, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.37, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.4}]}, "youxiaobi9": {"rotate": [{"angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 5.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.6, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.62, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.7}]}, "youxiaobi8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 5.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1333, "angle": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -7.86, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "youxiaobi6": {"rotate": [{"angle": 1.69, "curve": "stepped"}, {"time": 4.6667, "angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.69}]}, "youxiaobi5": {"rotate": [{"angle": 2.1, "curve": "stepped"}, {"time": 4.6667, "angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.1}]}, "youxiaobi4": {"rotate": [{"angle": 1.4, "curve": "stepped"}, {"time": 4.6667, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.4}]}, "youxiaobi3": {"rotate": [{"angle": 0.7, "curve": "stepped"}, {"time": 4.6667, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.7}]}, "yifu15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.55, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.39, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 6.1333, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 6.7667, "angle": 10.47, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 7.3333, "angle": 1.68, "curve": 0.347, "c2": 0.4, "c3": 0.681, "c4": 0.73}, {"time": 7.6, "angle": 0.42, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8}]}, "yifu1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 7.23, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 3.1667, "angle": 5.68, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 6.1333, "angle": -1.41, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": -0.78, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 5.18, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3333, "x": -10.54, "y": 0.26, "curve": 0.369, "c2": 0.48, "c3": 0.719, "c4": 0.88}, {"time": 2, "x": -12.72, "y": -0.69, "curve": 0.351, "c2": 0.65, "c3": 0.686}, {"time": 2.1333, "x": -14.91, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.3333, "x": -11.56, "y": -0.95, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 2.6667, "x": -7.9, "y": -1.06, "curve": 0.331, "c2": 0.33, "c3": 0.674, "c4": 0.69}, {"time": 3, "x": -4.18, "y": -0.95, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 3.1667, "x": -1.6, "y": -0.9, "curve": 0.354, "c2": 0.43, "c3": 0.69, "c4": 0.78}, {"time": 3.3333, "x": 1.24, "y": -0.86, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.4667, "x": 4.17, "y": -0.85, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 9.17, "y": 5.14, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4.0667, "x": 11.19, "y": 5.38, "curve": 0.306, "c2": 0.21, "c3": 0.643, "c4": 0.56}, {"time": 4.1667, "x": 13.75, "y": 6.23, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 4.3, "x": 16.34, "y": 7.75, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 4.6667, "x": 19.6, "y": 9.17, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.3333, "x": 3.53, "y": 5.12, "curve": 0.365, "c2": 0.52, "c3": 0.703, "c4": 0.88}, {"time": 5.4667, "x": 0.55, "y": -1.49, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 5.5, "x": -1.53, "y": -1.9, "curve": 0.317, "c3": 0.651, "c4": 0.35}, {"time": 5.5333, "x": -2.74, "y": -1.77, "curve": 0.291, "c2": 0.13, "c3": 0.632, "c4": 0.5}, {"time": 5.6667, "x": -5.51, "y": 0.09, "curve": 0.315, "c2": 0.28, "c3": 0.659, "c4": 0.64}, {"time": 5.8333, "x": -3.95, "y": 0.85, "curve": 0.347, "c2": 0.38, "c3": 0.691, "c4": 0.75}, {"time": 6, "x": -3.11, "y": 1.71, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 6.1333, "x": 0.78, "y": 1.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.2, "x": 10.15, "y": 1.07, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 6.3, "x": 17.05, "y": 3.81, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5667, "x": 40.11, "y": -7.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.7667, "x": 45, "y": 5.18, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 6.9333, "x": 29.61, "y": 2.53, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 7, "x": 25.27, "y": 1.43, "curve": 0.351, "c2": 0.39, "c3": 0.715, "c4": 0.83}, {"time": 7.4667, "x": 6, "y": 3.07, "curve": 0.352, "c2": 0.48, "c3": 0.687, "c4": 0.82}, {"time": 7.5667, "x": 2.07, "y": -0.19, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 7.6, "x": 0.48, "y": -2.78, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 7.6667, "x": -4.83, "y": -0.23, "curve": 0.319, "c2": 0.23, "c3": 0.653, "c4": 0.57}, {"time": 7.7333, "x": -6.05, "y": -0.13, "curve": 0.317, "c2": 0.26, "c3": 0.652, "c4": 0.6}, {"time": 7.8333, "x": -2.87, "y": -2.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8}]}, "yifu8": {"rotate": [{"angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 5.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 11.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.6333, "angle": -1.41, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -0.78, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 4.58}]}, "yifu7": {"rotate": [{"angle": 10.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 17.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "angle": -6.02, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 11.82, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 10.64}]}, "yifu5": {"rotate": [{"angle": 5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 12.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3667, "angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -5.31, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 9.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.44}]}, "yifu3": {"rotate": [{"angle": 2.45, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 9.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.2667, "angle": -6.92, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 8.17, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.45}]}, "yifu28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 8.28, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yifu33": {"rotate": [{"angle": 2.72, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 11, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 2.72, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.72}]}, "yifu32": {"rotate": [{"angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 13.33, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.04}]}, "yifu31": {"rotate": [{"angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 14.21, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.93}]}, "yifu30": {"rotate": [{"angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 12.24, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.95}]}, "yifu29": {"rotate": [{"angle": 1.98, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 10.26, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "angle": 1.98, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.98}]}, "yifu27": {"rotate": [{"angle": 2.72, "curve": "stepped"}, {"time": 4.6667, "angle": 2.72, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 2.72}]}, "yifu26": {"rotate": [{"angle": 5.04, "curve": "stepped"}, {"time": 4.6667, "angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.04}]}, "yifu25": {"rotate": [{"angle": 5.93, "curve": "stepped"}, {"time": 4.6667, "angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.93}]}, "yifu24": {"rotate": [{"angle": 3.95, "curve": "stepped"}, {"time": 4.6667, "angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.95}]}, "yifu23": {"rotate": [{"angle": 1.98, "curve": "stepped"}, {"time": 4.6667, "angle": 1.98, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.98}]}, "yifu22": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shenti1": {"translate": [{"time": 5.5}, {"time": 6.3333, "x": -20, "y": -18}, {"time": 8}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "y": 0.96, "curve": "stepped"}, {"time": 3, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.84, "curve": "stepped"}, {"time": 4.6667, "y": 0.84, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.5, "y": 0.87, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 6.3333, "y": 0.799, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "shenti2": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shenti3": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shenti5": {"rotate": [{"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "maozi": {"rotate": [{"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": -3.37, "y": 14.98, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tui23": {"rotate": [{"angle": -39.6, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "angle": -20.4}, {"time": 6.6667, "angle": -49.2, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 8, "angle": -39.6}]}, "zuoshou4": {"rotate": [{"angle": -2.18, "curve": "stepped"}, {"time": 3.8667, "angle": -2.18, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "curve": "stepped"}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "angle": 11.04, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.18}]}, "zuoshou9": {"rotate": [{"angle": -1.09, "curve": "stepped"}, {"time": 3.8667, "angle": -1.09, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": "stepped"}, {"time": 5.5667, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": 11.04, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.09}]}, "zuoshou8": {"rotate": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.2, "angle": 11.04, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuoshou5": {"rotate": [{"curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 2.1333, "angle": 8.4, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": 20.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yaodai": {"rotate": [{"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tengman3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tengman17": {"rotate": [{"angle": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.76}]}, "tengman16": {"rotate": [{"angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.7}]}, "tengman7": {"rotate": [{"angle": 0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.63}]}, "tengman4": {"rotate": [{"angle": 0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.57}]}, "tengman5": {"rotate": [{"angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.38}]}, "tengman2": {"rotate": [{"angle": 0.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.19}]}, "tengman11": {"rotate": [{"angle": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.15}]}, "tengman13": {"rotate": [{"angle": 0.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.18}]}, "tengman14": {"rotate": [{"angle": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.6333, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.26}]}, "tengman15": {"rotate": [{"angle": 0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.29}]}, "tengman18": {"rotate": [{"angle": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.04}]}, "tengman19": {"rotate": [{"angle": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.07}]}, "meimao1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 10, "curve": 0.25, "c3": 0.75}, {"time": 4.6667}]}, "tui11": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui34": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui35": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui10": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui36": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui9": {"rotate": [{"time": 6.5667}, {"time": 6.9667, "angle": -18.27}, {"time": 7.1667, "angle": -1.02}, {"time": 7.3, "angle": -9.96}, {"time": 7.4333, "angle": 10.45}, {"time": 8}]}, "tui14": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui31": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui13": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui32": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui12": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "tui33": {"rotate": [{"time": 6.5667}, {"time": 6.7667, "angle": 7.41}, {"time": 6.9667, "angle": -2.72}, {"time": 7.1667, "angle": 8.49}, {"time": 7.6, "angle": -9.46}, {"time": 8}]}, "zhizhuwang6": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.8667, "y": -38.02}, {"time": 8}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 1.32}, {"time": 8}]}, "xiaotui1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.9333, "y": 30}, {"time": 3.8667}, {"time": 5.9333, "y": 8}, {"time": 8}]}, "xiaotui4": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": 20}, {"time": 3.8667}, {"time": 5.4667, "y": 20}, {"time": 8}]}, "zhizhu2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": -12, "y": 4}, {"time": 5.5, "x": 12.46, "y": -6.15}, {"time": 8}]}, "cao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao7": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}, {"time": 3.4667, "angle": -2.57}, {"time": 6.3333, "angle": 3.56}, {"time": 8, "angle": 1.19}]}, "cao8": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}, {"time": 3.4667, "angle": -2.57}, {"time": 6.3333, "angle": 3.56}, {"time": 8, "angle": 1.19}]}, "cao9": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}, {"time": 3.4667, "angle": -2.57}, {"time": 6.3333, "angle": 3.56}, {"time": 8, "angle": 1.19}]}, "cao10": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}, {"time": 3.4667, "angle": -2.57}, {"time": 6.3333, "angle": 3.56}, {"time": 8, "angle": 1.19}]}, "cao11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "cao17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.57}, {"time": 5.5, "angle": 3.56}, {"time": 8}]}, "zhizhu1": {"rotate": [{}, {"time": 2.7, "angle": -12}, {"time": 5.5, "angle": 2.4}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7, "y": -50, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "y": 36, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "tengman6": {"rotate": [{}, {"time": 2.7, "angle": 6.31}, {"time": 5.3, "angle": -4.27}, {"time": 8}]}, "tengman20": {"rotate": [{"angle": -0.2}, {"time": 0.1}, {"time": 2.8, "angle": 6.31}, {"time": 5.4333, "angle": -4.27}, {"time": 8, "angle": -0.2}]}, "tengman21": {"rotate": [{"angle": -0.39}, {"time": 0.2333}, {"time": 2.9333, "angle": 6.31}, {"time": 5.5333, "angle": -4.27}, {"time": 8, "angle": -0.39}]}, "tengman9": {"rotate": [{"angle": -0.59}, {"time": 0.3333}, {"time": 3.0333, "angle": 6.31}, {"time": 5.6667, "angle": -4.27}, {"time": 8, "angle": -0.59}]}, "tengman10": {"rotate": [{"angle": -0.79}, {"time": 0.4667}, {"time": 3.1667, "angle": 6.31}, {"time": 5.8, "angle": -4.27}, {"time": 8, "angle": -0.79}]}, "tengman12": {"rotate": [{"angle": -0.99}, {"time": 0.5667}, {"time": 3.2667, "angle": 6.31}, {"time": 5.9333, "angle": -4.27}, {"time": 8, "angle": -0.99}]}, "tengman": {"rotate": [{}, {"time": 3.8667, "angle": 4.06}, {"time": 8}]}, "tengman22": {"rotate": [{}, {"time": 3.8667, "angle": 4.06}, {"time": 8}]}, "tengman23": {"rotate": [{}, {"time": 3.8667, "angle": 4.06}, {"time": 8}]}, "zhizhuwang7": {"scale": [{}, {"time": 2.7, "x": 1.12}, {"time": 5.5, "x": 0.975, "y": 1.2}, {"time": 8}]}, "tui27": {"translate": [{"x": -53.62, "y": 85.83}]}, "tui45": {"translate": [{}, {"time": 2.1333, "x": -58, "y": 62}, {"time": 3.8667, "x": -58, "y": 6}, {"time": 6.1333, "y": 18}, {"time": 8}]}, "heshangtou1": {"rotate": [{}, {"time": 2.3, "angle": 1.2}, {"time": 4.6667, "curve": "stepped"}, {"time": 5.9333}, {"time": 6.7667, "angle": 6}, {"time": 8}]}, "zhizhusi4": {"rotate": [{"angle": 0.02}, {"time": 0.0667}, {"time": 4.7333, "angle": 3.86}, {"time": 6.4667, "angle": -0.86}, {"time": 6.8, "angle": 0.41}, {"time": 8, "angle": 0.02}], "scale": [{"x": 1.001}, {"time": 0.0667}, {"time": 5.5667, "x": 1.024}, {"time": 6.4667, "x": 1.044}, {"time": 6.7333, "x": 1.015}, {"time": 7.0667, "x": 1.026}, {"time": 7.4, "x": 1.007}, {"time": 8, "x": 1.001}]}, "zhizhusi5": {"rotate": [{"angle": 0.03}, {"time": 0.1}, {"time": 4.7667, "angle": 3.86}, {"time": 6.5, "angle": -0.86}, {"time": 6.8333, "angle": 0.41}, {"time": 8, "angle": 0.03}], "scale": [{"x": 1.001}, {"time": 0.1}, {"time": 5.6, "x": 1.024}, {"time": 6.5, "x": 1.044}, {"time": 6.7667, "x": 1.015}, {"time": 7.1, "x": 1.026}, {"time": 7.4333, "x": 1.007}, {"time": 8, "x": 1.001}]}, "zhizhusi6": {"rotate": [{"angle": 0.04}, {"time": 0.1333}, {"time": 4.8, "angle": 3.86}, {"time": 6.5333, "angle": -0.86}, {"time": 6.8667, "angle": 0.41}, {"time": 8, "angle": 0.04}], "scale": [{"x": 1.001}, {"time": 0.1333}, {"time": 5.6333, "x": 1.024}, {"time": 6.5333, "x": 1.044}, {"time": 6.8, "x": 1.015}, {"time": 7.1333, "x": 1.026}, {"time": 7.4667, "x": 1.007}, {"time": 8, "x": 1.001}]}, "zhizhusi7": {"rotate": [{"angle": 0.05}, {"time": 0.1667}, {"time": 4.8333, "angle": 3.86}, {"time": 6.5667, "angle": -0.86}, {"time": 6.9, "angle": 0.41}, {"time": 8, "angle": 0.05}], "scale": [{"x": 1.002}, {"time": 0.1667}, {"time": 5.6667, "x": 1.024}, {"time": 6.5667, "x": 1.044}, {"time": 6.8333, "x": 1.015}, {"time": 7.1667, "x": 1.026}, {"time": 7.5, "x": 1.007}, {"time": 8, "x": 1.002}]}, "zhizhusi8": {"rotate": [{"angle": 0.06}, {"time": 0.2}, {"time": 4.8667, "angle": 3.86}, {"time": 6.6, "angle": -0.86}, {"time": 6.9333, "angle": 0.41}, {"time": 8, "angle": 0.06}], "scale": [{"x": 1.002}, {"time": 0.2}, {"time": 5.7, "x": 1.024}, {"time": 6.6, "x": 1.044}, {"time": 6.8667, "x": 1.015}, {"time": 7.2, "x": 1.026}, {"time": 7.5333, "x": 1.007}, {"time": 8, "x": 1.002}]}, "tui41": {"translate": [{"time": 2.6667}, {"time": 4, "x": 8, "y": 112}, {"time": 5.3333}]}, "tui46": {"translate": [{"time": 3.8667}, {"time": 5.9333, "x": 62, "y": 56}, {"time": 8}]}, "tui17": {"rotate": [{"time": 1.3333}, {"time": 2, "angle": 2.47}, {"time": 3.3333, "angle": -6}, {"time": 8}]}, "tui19": {"rotate": [{}, {"time": 2.6667, "angle": 9.53}, {"time": 4}, {"time": 6, "angle": 25.21}, {"time": 8}]}, "fashi2": {"translate": [{}, {"time": 2, "x": 4.73, "y": 8.58}, {"time": 4}, {"time": 6, "x": 1.49, "y": -1.33}, {"time": 8}]}, "fashi1": {"rotate": [{}, {"time": 2, "angle": -7.2}, {"time": 4}, {"time": 6, "angle": 3.6}, {"time": 8}]}, "tui28": {"translate": [{"x": -108.49, "y": 189.54}]}, "zhizhusi2": {"rotate": [{}, {"time": 4.6667, "angle": 3.86}, {"time": 6.4, "angle": -0.86}, {"time": 6.7333, "angle": 0.41}, {"time": 8}], "scale": [{"curve": 0.225, "c3": 0.842, "c4": 0.67}, {"time": 5.5, "x": 1.017, "curve": 0.64, "c2": 0.41, "c4": 0.75}, {"time": 6.4, "x": 1.038, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 1.017, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.027, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.009, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zhizhusi9": {"rotate": [{"angle": 0.08}, {"time": 0.2333}, {"time": 4.9, "angle": 3.86}, {"time": 6.6333, "angle": -0.86}, {"time": 6.9667, "angle": 0.41}, {"time": 8, "angle": 0.08}], "scale": [{"x": 1.003}, {"time": 0.2333}, {"time": 5.7333, "x": 1.026}, {"time": 6.6333, "x": 1.045}, {"time": 6.9, "x": 1.017}, {"time": 7.2333, "x": 1.027}, {"time": 7.5667, "x": 1.008}, {"time": 8, "x": 1.003}]}, "shenti_4": {"rotate": [{}, {"time": 2, "angle": -5.11}, {"time": 3.1667, "angle": -1.77}, {"time": 4.6667}, {"time": 6.1667, "angle": -14.31}, {"time": 6.8333}]}, "shenti_6": {"rotate": [{}, {"time": 2, "angle": -5.11}, {"time": 3.1667, "angle": -1.77}, {"time": 4.6667}, {"time": 6.1667, "angle": -14.31}, {"time": 6.8333}]}, "shenti_5": {"rotate": [{"time": 0.1}, {"time": 2.1, "angle": -1.97}, {"time": 3.2667, "curve": "stepped"}, {"time": 4.7667}, {"time": 6.2667, "angle": -3.72}, {"time": 6.9333}]}, "shenti_7": {"rotate": [{"time": 0.2}, {"time": 2.2, "angle": -1.97}, {"time": 3.3667, "curve": "stepped"}, {"time": 4.8667}, {"time": 6.3667, "angle": -3.72}, {"time": 7.0333}]}, "shenti_8": {"rotate": [{"time": 0.1}, {"time": 2.1, "angle": -1.97}, {"time": 3.2667, "curve": "stepped"}, {"time": 4.7667}, {"time": 6.2667, "angle": -3.72}, {"time": 6.9333}]}, "shenti_9": {"rotate": [{"time": 0.2}, {"time": 2.2, "angle": -1.97}, {"time": 3.3667, "curve": "stepped"}, {"time": 4.8667}, {"time": 6.3667, "angle": -3.72}, {"time": 7.0333}]}}, "deform": {"default": {"zhengyan2": {"zhengyan2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5333, "vertices": [-6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, 0, 0, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598, -0.00049, -6.43598], "curve": 0.25, "c3": 0.75}, {"time": 2.7, "vertices": [4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 0, 0, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496, -0.00043, 4.05496], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "vertices": [-2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, 0, 0, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042, 0.00012, -2.15042], "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "vertices": [0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0, 0, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998, -0.00034, 0.4998], "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "vertices": [9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 0, 0, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555, -0.00018, 9.53555], "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "vertices": [-4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, 0, 0, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176, 0.0007, -4.7176], "curve": 0.25, "c3": 0.75}, {"time": 7, "vertices": [6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 0, 0, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103, 0.00067, 6.8103], "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "zuiba2": {"zuiba2": [{"curve": 0.25, "c3": 0.75}, {"time": 2.3, "offset": 1, "vertices": [-5.43174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174], "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "offset": 1, "vertices": [-5.43174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174, 0.00195, -5.43174], "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "offset": 1, "vertices": [4.28555, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555, 0.00146, 4.28555], "curve": 0.25, "c3": 0.75}, {"time": 6.3, "offset": 1, "vertices": [-8.4553, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.00012, -8.1646, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553, 0.00125, -8.4553], "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "offset": 1, "vertices": [-0.26362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.00091, 0.01356, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362, 0.0004, -0.26362], "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "beijing": {"beijing": [{}, {"time": 2.2, "offset": 92, "vertices": [9.82678, -23.01019, -9.46704, -28.31464, -21.1499, 0.29532, 0, 0, 0, 3.14999, 4.35834, 2.72211, 4.09698, -0.44383, 2.10614, -3.40132, 2.4989, -13.00531, -13.23248, -5.39326, -21.43646, -7.44534, -29.84369, -9.5484, -33.97766, -4.49541, -39.00342, 1.37447, -44.00293, -3.28349, -49.6925, -8.58485, -49.58148, -3.3138, -44.21967, 3.32774, -39.62585, 7.25298, -32.1991, 5.55428, -25.31897, 2.66777, -22.20715, 8.11598, -3.33008, -1.72404, -3.67157, 0.31425, -4.01953, 2.3925, -2.54858, 4.96649, 0, 0, 0, 0, 0, 0, 0, 0, -24.15628, 20.76154, -2.87283, 20.63094, 17.35779, 20.5069, 29.12827, 7.7648, 39.83762, -3.8287, 54.02133, -1.75333, 55.64844, 12.89526, 57.20676, 26.92538, 64.07932, 30.95538, 72.29013, 35.7699, 84.84695, 34.8938, 96.36478, 34.09012, 101.52948, 33.06119, 90.8699, 28.49261, 79.70169, 23.70651, 72.87036, 13.88779, 78.7554, 6.79379, 83.5874, 0.96902, 76.20221, 0.32733, 71.23553, -0.10425, 72.81033, -20.10492, 73.87735, -33.65601, 74.73599, -44.56168, 59.43839, -37.45895, 39.17212, -28.04926, 18.01587, -18.22614, 7.05228, -21.46567, 0, 0, 0, 0, -9.92502, 2.64841, -9.203, 1.97772, -7.28992, 2.30765, -5.92465, 2.543, -4.10922, 2.85605, -3.14288, 2.53394, -3.49097, 1.97006, -1.97766, 1.31622, -0.44785, 0.65521, 0.9895, 0.03424, 2.83304, -0.1734, 3.11755, -1.40912, 3.92563, -2.10229, 5.4422, -0.51111, 4.97055, -1.57983, 4.15851, -2.45703, 5.30563, -2.85162, 6.75476, -3.35028, 8.60873, -3.98804, 10.07605, -4.49298]}, {"time": 3.4667, "offset": 110, "vertices": [-0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999, -0.59601, 0, -0.29797, 1.48999, -0.29797, 1.48999, -0.29797, 1.48999]}, {"time": 4.6667, "vertices": [1.18182, 1.44012, 1.22052, 1.38226, 1.25336, 1.20117, 1.20441, 1.04382, 1.11789, 0.91089, 1.16599, 0.92151, 1.23593, 0.88208, 1.28192, 0.77307, 1.35336, 0.79724, 1.43973, 0.81714, 1.444, 0.8009, 1.44458, 0.71393, 1.36081, 0.68591, 1.36908, 0.62921, 1.44608, 0.62805, 1.44611, -0.57642, 1.29269, -0.75787, 1.27283, -0.86438, 0.98651, -1.03894, 0.95969, -1.06433, 0.95264, -1.12756, 0.95282, -1.14844, 1.01715, -1.26855, 1.04388, -1.26868, 1.17566, -1.19647, 1.23749, -1.16199, 1.39261, -1.05664, 1.42557, -1.03992, 1.39484, -1.06793, 1.12958, -1.23987, 1.13129, -1.28894, 1.08844, -1.30951, 1.09302, -1.33478, 1.17169, -1.38574, 1.1333, -1.46899, 1.18866, -1.50531, 1.44849, -1.54266, 1.44849, -1.5658, 1.44452, -1.56903, 1.38556, -1.56921, 1.19196, -1.50665, 1.14081, -1.52722, 1.09277, -1.5694, 0.0351, -1.56862, 0.0033, -1.46363, -0.06134, -1.44906, -2.3801, 17.55069, 8.01426, 14.99212, 6.00269, -1.41769, -0.18359, -1.42934, -0.52288, -5.30134, -7.4184, -3.72277, -6.03629, 0.64165, -3.6107, 4.15547, -1.02724, 7.81765, 1.76905, 11.80198, 9.681, 10.10013, 19.99668, 8.36045, 21.48577, 2.1216, 23.41338, -5.22162, 29.82986, -3.24608, 37.13207, -0.99768, 34.77478, -5.61016, 27.18805, -9.18402, 20.52608, -13.07011, 15.60697, -6.00117, 10.76081, -0.50077, 5.66293, 2.08065, 3.48865, 0.05511, 4.63544, -2.91574, 4.4576, -5.94733, 3.23454, -7.2295, 0.80653, -4.55015, 0.11602, -3.35597, 0.28711, -2.59113, -0.46515, -0.99345, -2.49125, -1.87234, -9.36551, -5.65552, -15.89983, -9.2514, -21.97852, -7.26529, -27.50931, -5.45827, -31.70191, -8.67528, -29.59287, -13.68296, -27.57303, -18.47908, -29.06101, -21.01143, -30.83863, -24.03675, -35.03775, -26.01148, -38.88983, -27.82283, -40.73718, -28.4198, -38.12707, -25.03354, -35.39245, -21.48578, -34.95791, -17.09759, -38.12732, -15.87183, -40.72946, -14.86524, -38.46767, -13.33138, -36.94637, -12.29987, -41.04789, -6.14507, -43.82681, -1.97484, -46.06311, 1.38117, -39.86259, 1.84427, -31.64802, 2.45764, -23.07279, 3.09783, -20.126, 6.11107, -0.59378, -0.90594, -0.66904, -0.88834, 5.16564, -2.12567, 4.6913, -1.72729, 3.5005, -1.99451, 2.6507, -2.18494, 1.52069, -2.43834, 0.90383, -2.26642, 1.10483, -1.90156, 0.13415, -1.53827, -0.84706, -1.17133, -1.76889, -0.82638, -2.93289, -0.7539, -3.1503, 0.01303, -3.67931, 0.42288, -4.58166, -0.62376, -4.31901, 0.06208, -3.83675, 0.63836, -4.56926, 0.85019, -5.49478, 1.1177, -6.67872, 1.46017, -7.61589, 1.73111, -1.562, 0.18073, -1.48486, 0.18103, -1.45663, 0.24927, -1.56219, 0.52448, -1.56366, 1.43701]}, {"time": 5.5, "offset": 92, "vertices": [-3.74524, 32.40563, 13.98761, 28.03506, 10.54736, 0.03893, 0, 0, -0.315, -6.93001, -11.98181, -4.15163, -9.61292, 3.32152, -5.43359, 9.34134, -0.98376, 15.61517, 4.02699, 21.38978, 17.51319, 18.48577, 35.07435, 15.50982, 37.60932, 4.8758, 40.89075, -7.64071, 51.82777, -4.27703, 64.27448, -0.44863, 60.25465, -8.3088, 47.32209, -14.39605, 36.17442, -19.9705, 27.58533, -8.96552, 19.3285, 0.41174, 10.65888, 4.75373, 6.72162, 2.31607, 8.66003, -2.77576, 8.35425, -7.96707, 6.23438, -10.2005, 2.06989, -5.74202, 0.94495, -3.78, 1.26007, -2.51999, 0, 0, -3.37918, -1.41428, -14.9292, -7.76579, -25.9079, -13.80298, -36.11835, -10.46338, -45.40842, -7.4248, -52.45312, -12.827, -48.91309, -21.2403, -45.52234, -29.29828, -48.02325, -33.55151, -51.01093, -38.63287, -58.06622, -41.94791, -64.53802, -44.98865, -67.64178, -45.99057, -63.25519, -40.30347, -58.65948, -34.34509, -57.92743, -26.97351, -63.25085, -24.91281, -67.62167, -23.2207, -63.82126, -20.64508, -61.26532, -18.91312, -68.15204, -8.57138, -72.81793, -1.56464, -76.573, 4.07437, -66.15659, 4.84888, -52.35675, 5.87497, -37.95102, 6.94614, -32.99921, 12.00629, 0, 0, 0, 0, 10.01126, -2.14676, 9.25943, -1.51474, 7.37112, -1.93759, 6.02359, -2.23932, 4.23172, -2.64056, 3.25381, -2.36746, 3.57257, -1.78903, 2.03375, -1.21277, 0.47815, -0.63025, -0.98337, -0.08289, -2.82892, 0.0329, -3.1731, 1.24896, -4.01178, 1.89917, -5.44302, 0.24017, -5.02621, 1.32751, -4.26117, 2.24091, -5.42258, 2.57709, -6.88983, 3.00183, -8.76694, 3.54541, -10.25269, 3.97565]}, {"time": 8}]}, "zui1": {"zui1": [{}, {"time": 2.3, "offset": 5, "vertices": [1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523, 0, 1.26523]}, {"time": 3, "vertices": [1.96307, 2.37286, 1.96309, 9.49796, 1.96309, 10.19787, 1.96309, 10.19787, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 0, 0, 0, 0, 0, 0, 0, 0, 1.96307, 2.37286, 1.96307, 2.37286], "curve": "stepped"}, {"time": 4.3333, "vertices": [1.96307, 2.37286, 1.96309, 9.49796, 1.96309, 10.19787, 1.96309, 10.19787, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 2e-05, 7.82501, 0, 0, 0, 0, 0, 0, 0, 0, 1.96307, 2.37286, 1.96307, 2.37286]}, {"time": 5.6667, "offset": 5, "vertices": [3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312, -0.00116, 3.5312]}, {"time": 6.6667, "offset": 1, "vertices": [7.77221, 0.00029, 7.77221, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, -0.00054, 10.29449, 0.00029, 7.77221]}, {"time": 8}]}, "zhengyan": {"zhengyan": [{"time": 5.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "vertices": [-4.7141, 0.95439, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.59673, 0.74771, -6.38286, 1.1893, -6.81839, 2.99634, -8.93375, 2.34581, -7.53339, 3.23163, -7.31557, 2.32794, -4.4025, -0.13611, 0, 0, 0, 0, -1.68578, -0.97916, -4.76169, 0.03279, -3.37149, -1.95874, -4.18311, -3.90744, -4.96214, -1.09451, -5.52074, -2.27425, -3.3815, 0.60139, -2.81795, 0.50104], "curve": 0.25, "c3": 0.75}, {"time": 6, "vertices": [-6.38841, 2.37392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.58324, -0.08858, -9.05522, 2.15053, -15.46457, 2.285, -16.36095, 3.21571, -15.63463, 4.7657, -15.283, 2.91374, -9.60873, 1.68388, 0, 0, 0, 0, -3.9016, -1.51981, -8.42764, -1.97772, -9.6311, -4.77531, -11.63216, -7.84148, -11.46127, -3.00735, -14.04118, -3.23714, -11.17683, 0.37343, -9.80646, 1.85551], "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "vertices": [-8.83954, 2.68651, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -15.16486, -1.22681, -16.63028, 1.16595, -21.48737, 4.62735, -23.76025, 8.95308, -21.86243, 8.45015, -21.57861, 3.66949, -11.87799, 3.05421, 0, 0, 0, 0, -7.02391, -1.30966, -15.29187, -1.99545, -19.65724, -5.44737, -28.40324, -5.938, -25.54823, -0.69411, -29.6124, -1.32204, -21.53853, -0.34981, -13.50874, 2.49213], "curve": 0.25, "c3": 0.75}, {"time": 6.1, "vertices": [-8.83954, 2.68651, 0, 0, 0, 0, 7.20151, -5.27103, 9.00171, -6.58862, 12.13271, -8.8804, 12.52406, -9.16692, 9.86296, -7.21904, 7.51471, -5.50017, 2.89627, -2.1199, 0, 0, 0, 0, 0, 0, 0, 0, 1.91769, -1.40372, 3.51556, -2.57317, 6.4716, -4.73689, 9.50797, -6.95927, 10.1655, -7.44052, 9.78914, -7.16508, 7.5804, -5.54851, 1.35823, -0.99414, 0, 0, 0, 0, 0, 0, -15.16486, -1.22681, -16.63028, 1.16595, -21.48737, 4.62735, -23.76025, 8.95308, -21.86243, 8.45015, -21.57861, 3.66949, -11.87799, 3.05421, 0, 0, 0, 0, -7.02391, -1.30966, -15.29187, -1.99545, -19.65724, -5.44737, -26.05463, -7.65694, -25.54823, -0.69411, -29.6124, -1.32204, -21.53853, -0.34981, -13.50874, 2.49213], "curve": "stepped"}, {"time": 6.1667, "vertices": [-8.83954, 2.68651, 0, 0, 0, 0, 7.20151, -5.27103, 9.00171, -6.58862, 12.13271, -8.8804, 12.52406, -9.16692, 9.86296, -7.21904, 7.51471, -5.50017, 2.89627, -2.1199, 0, 0, 0, 0, 0, 0, 0, 0, 1.91769, -1.40372, 3.51556, -2.57317, 6.4716, -4.73689, 9.50797, -6.95927, 10.1655, -7.44052, 9.78914, -7.16508, 7.5804, -5.54851, 1.35823, -0.99414, 0, 0, 0, 0, 0, 0, -15.16486, -1.22681, -16.63028, 1.16595, -21.48737, 4.62735, -23.76025, 8.95308, -21.86243, 8.45015, -21.57861, 3.66949, -11.87799, 3.05421, 0, 0, 0, 0, -7.02391, -1.30966, -15.29187, -1.99545, -19.65724, -5.44737, -26.05463, -7.65694, -25.54823, -0.69411, -29.6124, -1.32204, -21.53853, -0.34981, -13.50874, 2.49213], "curve": 0.25, "c3": 0.75}, {"time": 6.2, "vertices": [-8.83954, 2.68651, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -15.16486, -1.22681, -16.63028, 1.16595, -21.48737, 4.62735, -23.76025, 8.95308, -21.86243, 8.45015, -21.57861, 3.66949, -11.87799, 3.05421, 0, 0, 0, 0, -7.02391, -1.30966, -15.29187, -1.99545, -19.65724, -5.44737, -28.40324, -5.938, -25.54823, -0.69411, -29.6124, -1.32204, -21.53853, -0.34981, -13.50874, 2.49213], "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "vertices": [-6.38841, 2.37392, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.58324, -0.08858, -9.05522, 2.15053, -15.46457, 2.285, -16.36095, 3.21571, -15.63463, 4.7657, -15.283, 2.91374, -9.60873, 1.68388, 0, 0, 0, 0, -3.9016, -1.51981, -8.42764, -1.97772, -9.6311, -4.77531, -11.63216, -7.84148, -11.46127, -3.00735, -14.04118, -3.23714, -11.17683, 0.37343, -9.80646, 1.85551], "curve": 0.25, "c3": 0.75}, {"time": 6.3, "vertices": [-4.7141, 0.95439, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.59673, 0.74771, -6.38286, 1.1893, -6.81839, 2.99634, -8.93375, 2.34581, -7.53339, 3.23163, -7.31557, 2.32794, -4.4025, -0.13611, 0, 0, 0, 0, -1.68578, -0.97916, -4.76169, 0.03279, -3.37149, -1.95874, -4.18311, -3.90744, -4.96214, -1.09451, -5.52074, -2.27425, -3.3815, 0.60139, -2.81795, 0.50104], "curve": 0.25, "c3": 0.75}, {"time": 6.3333}]}, "zhizhuwang": {"zhizhuwang": [{}, {"time": 3.8667, "offset": 61, "vertices": [5.62988, -0.00397, 6.13362, 3.66437, 2.63651, 10.16467, 2.66815, 16.45587, 1.46213, 23.50427, -3.0134, 29.23077, -6.64969, 30.58453, -9.58052, 23.71869, -5.83675, 13.48981, -0.25937, 6.93219, 3.31679]}, {"time": 8}]}, "youshangbi": {"youshangbi": [{"time": 4.6667}, {"time": 5.5, "offset": 32, "vertices": [1.92483, -0.64104]}, {"time": 5.9333}, {"time": 6.5667, "offset": 32, "vertices": [-12.85551, -12.90375, -6.52467, -7.56164, -5.26447, -6.45457, -3.17657, -3.31067, -2.20013, -1.80358]}, {"time": 6.7667, "offset": 32, "vertices": [-12.9352, -12.21136, -6.0814, -5.60994, -4.36633, -5.97604, -4.23542, -4.41423, -2.93351, -2.40477]}, {"time": 8}]}}}, "drawOrder": [{"offsets": [{"slot": "tui15", "offset": 15}]}]}}}