import { IConfigItem } from "db://assets/GameScrpit/game/JsonDefine";
import { IConfigShop } from "../../game/bundleDefine/bundle_shop_define";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { GoodsRedeemMessage } from "../../game/net/protocol/Goods";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class GoodsData {
  private goodsRedeemMsg: GoodsRedeemMessage = {
    /** 总的兑换物料ID和兑换次数 */
    redeemMap: {},
    virtualMoneySet: [],
  };

  // 图集兑换
  private _atlasList = {
    7: { name: "师徒一行" },
    8: { name: "四大天王" },
  };

  public initGoodsRedeemMsg(res: GoodsRedeemMessage) {
    this.goodsRedeemMsg = res;
  }

  public getAtalsList() {
    return this._atlasList;
  }

  public hasBuyCount(id: number) {
    for (const key in this.goodsRedeemMsg.redeemMap) {
      if (id == Number(key)) {
        log.log("获取该购买商品已经购买的次数===id==" + id + "次数" + this.goodsRedeemMsg.redeemMap[key]);
        return this.goodsRedeemMsg.redeemMap[key];
      }
    }
    log.log("获取该购买商品已经购买的次数===id==" + id + "次数0");
    return 0;
  }

  public getGoodsRedeemMsgById(redeemId: number) {
    if (this.goodsRedeemMsg.redeemMap[redeemId]) {
      return this.goodsRedeemMsg.redeemMap[redeemId];
    }
    return 0;
  }

  public getGoodsRedeemMsg(): GoodsRedeemMessage {
    return this.goodsRedeemMsg;
  }

  /**
   * 根据type找出对应的list
   * @param type
   * @returns
   */
  public getShopItemList(type: number) {
    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let farmShopItemList: number[] = [];
    Object.values(c_shop).forEach((val) => {
      if (val["type"] == type) {
        farmShopItemList.push(val["id"]);
      }
    });

    farmShopItemList.sort((a, b) => {
      let configA = JsonMgr.instance.jsonList.c_shop[a];
      let configB = JsonMgr.instance.jsonList.c_shop[b];
      return configA["sort"] - configB["sort"];
    });

    return farmShopItemList;
  }

  /**
   * 每日已兑换物料
   * @param redeemId
   * @returns
   */
  public getDailyRedeemMapById(redeemId: number): number {
    if (this.goodsRedeemMsg.redeemMap[redeemId]) {
      return this.goodsRedeemMsg.redeemMap[redeemId];
    }
    return 0;
  }

  /**
   * 总的兑换物料
   * @param redeemId
   * @returns
   */
  public getRedeemMapById(redeemId: number): number {
    if (this.goodsRedeemMsg.redeemMap[redeemId]) {
      return this.goodsRedeemMsg.redeemMap[redeemId];
    }
    return 0;
  }

  /**
   * 每日兑换物料 的 剩余兑换次数
   * @param redeemId
   */
  public getDailyRedeemBuyCount(redeemId: number) {
    let config = JsonMgr.instance.jsonList.c_shop[redeemId];
    let hasDailyBuyCount = this.getDailyRedeemMapById(redeemId);
    let hasBuyCount = this.getRedeemMapById(redeemId);
    let max = config["max"];

    switch (config["maxtype"]) {
      case 0:
        // 0.不限购
        return [0, hasDailyBuyCount, 0];
        break;
      case 1:
        // 1.每日限购
        return [1, hasDailyBuyCount, max - hasDailyBuyCount];
        break;
      case 2:
        // 2.每周限购
        return [2, hasBuyCount, max - hasDailyBuyCount];
        break;
      case 3:
        // 3.每月限购
        break;
      case 4:
        // 4.永久限购
        return [2, hasBuyCount, max - hasDailyBuyCount];
        break;
      case 5:
        // 5.活动限购
        break;
      default:
        break;
    }
  }

  /**
   * 每日兑换物料 购买的花费
   * @param redeemId
   * @returns
   */
  public getDailyRedeemBuyCost(redeemId: number) {
    let config = JsonMgr.instance.jsonList.c_shop[redeemId];
    let hasDailyBuyCount = this.getDailyRedeemMapById(redeemId);

    return config["coinPrice"] + hasDailyBuyCount * config["priceAdd"];
  }

  public getConfigItem(id: number): IConfigItem {
    return JsonMgr.instance.getConfigItem(id);
  }

  public getConfigShopListByType(type: number): IConfigShop[] {
    let goodsMap = JsonMgr.instance.jsonList.c_shop;
    let rs: IConfigShop[] = [];
    let keys = Object.keys(goodsMap);
    for (let i = 0; i < keys.length; i++) {
      let item = goodsMap[keys[i]];
      if (item.type == type) {
        rs.push(item);
      }
    }
    rs = rs.sort((a, b) => a.sort - b.sort);
    return rs;
  }
}
