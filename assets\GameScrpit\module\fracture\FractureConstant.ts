/** 时空裂缝活动配置数据结构 **/

import { BaseActivityConfigVO } from "../activity/ActivityConfig";

// 基础物品结构（类型ID + 数量）
type ResourcePair = [number, number];

export const FRACTURE_ACTIVITYID = 11501;
// 兑换礼包配置（redeemList 中的单个礼包对象）
export interface ActivityRedeemItem {
  id: number; // 礼包唯一ID（如11501101）
  name: string | null; // 礼包显示名称（部分为null）
  maxtype: number; // 限制类型（1=次数限制，5=兑换限制）
  max: number; // 最大可兑换次数
  adNum: number; // 需要观看广告次数
  skipAd: null; // 广告跳过功能（当前均为null）
  price: number; // 定价标识（如10006代表6元档位）
  cost: number[]; // 消耗资源数组（[资源类型, 数量]）
  costAdd: number[]; // 额外消耗资源（当前为空）
  unlockList: any[]; // 解锁条件数组
  rewardList: number[]; // 奖励资源数组（[资源类型, 数量,...]）
  chosenList: any[]; // 可选奖励列表
  sort: number; // 排序序号
  close: boolean; // 是否关闭
  activityId: number; // 所属活动ID
  skipAddCostMap: Record<string, never>; // 跳过额外消耗（空对象）
  virtualCostMap: Record<string, number>; // 虚拟消耗映射表
  virtualCostAddMap: Record<string, never>; // 虚拟额外消耗（空对象）
  rewardMap: Record<string, number>; // 奖励映射表
  rewardDoubleList: number[]; // 双倍奖励列表
  chosenIndexMapList: any[]; // 可选奖励索引映射
  chosenReward2MapList: any[]; // 二级可选奖励映射
  chosenDoubleListMapList: any[]; // 双倍可选奖励映射
}
export interface RankReward {
  rank: number;
  rewardList: number[];
  itemRankId: number;
}

// 完整活动配置结构
export interface FractureActivityConfig extends BaseActivityConfigVO {
  buyType: number; // 购买类型标识（14）
  commTransformRules: any[]; // 通用转化规则
  transformAfterEnd: ResourcePair[]; // 活动结束转化规则
  orUnlockList: any[]; // 或条件解锁列表
  redeemList: ActivityRedeemItem[][]; // 兑换礼包二维数组（按组划分）
  rankRewardList: RankReward[]; // 排行榜奖励列表（根据用户提及补充）
}

export const FractureFloorMap = {
  1: [101, 102], // 难度1对应的楼层ID
  2: [201, 202], // 难度2对应的楼层ID
  3: [301, 302], // 难度3对应的楼层ID
};
