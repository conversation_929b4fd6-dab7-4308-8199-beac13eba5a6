<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>bg_biaoqian_wzry1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,61}</string>
                <key>spriteSourceSize</key>
                <string>{71,61}</string>
                <key>textureRect</key>
                <string>{{82,885},{71,61}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_biaoqian_wzry2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,61}</string>
                <key>spriteSourceSize</key>
                <string>{71,61}</string>
                <key>textureRect</key>
                <string>{{1,913},{71,61}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jindutiao_dengji1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{30,26}</string>
                <key>spriteSourceSize</key>
                <string>{30,26}</string>
                <key>textureRect</key>
                <string>{{217,804},{30,26}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jindutiao_dengji2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{30,26}</string>
                <key>spriteSourceSize</key>
                <string>{30,26}</string>
                <key>textureRect</key>
                <string>{{217,832},{30,26}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jindutiao_renshu1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{16,16}</string>
                <key>spriteSourceSize</key>
                <string>{16,16}</string>
                <key>textureRect</key>
                <string>{{217,860},{16,16}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jindutiao_renshu2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{12,16}</string>
                <key>spriteSourceSize</key>
                <string>{12,16}</string>
                <key>textureRect</key>
                <string>{{235,860},{12,16}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_moquan_lv.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{107,94}</string>
                <key>spriteSourceSize</key>
                <string>{107,94}</string>
                <key>textureRect</key>
                <string>{{136,708},{107,94}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_wz_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{705,248}</string>
                <key>spriteSourceSize</key>
                <string>{705,248}</string>
                <key>textureRect</key>
                <string>{{1,1},{705,248}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_wz_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{614,185}</string>
                <key>spriteSourceSize</key>
                <string>{614,185}</string>
                <key>textureRect</key>
                <string>{{251,1},{614,185}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_wz_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{614,185}</string>
                <key>spriteSourceSize</key>
                <string>{614,185}</string>
                <key>textureRect</key>
                <string>{{251,617},{614,185}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_wz_daojudi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{50,50}</string>
                <key>spriteSourceSize</key>
                <string>{50,50}</string>
                <key>textureRect</key>
                <string>{{82,833},{50,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_biaoq1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{79,79}</string>
                <key>spriteSourceSize</key>
                <string>{79,79}</string>
                <key>textureRect</key>
                <string>{{136,804},{79,79}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_biaoq2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{79,78}</string>
                <key>spriteSourceSize</key>
                <string>{79,78}</string>
                <key>textureRect</key>
                <string>{{1,833},{79,78}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_fenyedi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{133,123}</string>
                <key>spriteSourceSize</key>
                <string>{133,123}</string>
                <key>textureRect</key>
                <string>{{1,708},{133,123}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UICityLvReward.png</string>
            <key>size</key>
            <string>{437,1232}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:f092fced7e131f11e7413b2908150641:4926108b0d02e52ec62edaa9f466330e:a6cf14d00fb1617dadcf585123f9651c$</string>
            <key>textureFileName</key>
            <string>UICityLvReward.png</string>
        </dict>
    </dict>
</plist>
