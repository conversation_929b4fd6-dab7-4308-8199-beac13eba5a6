
ani101.png
size: 944,810
format: RGBA8888
filter: Linear,Linear
repeat: none
1
  rotate: false
  xy: 6, 6
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
10
  rotate: false
  xy: 274, 140
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
11
  rotate: false
  xy: 408, 140
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
12
  rotate: false
  xy: 542, 140
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
13
  rotate: false
  xy: 676, 140
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
14
  rotate: false
  xy: 810, 140
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
15
  rotate: false
  xy: 6, 274
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
16
  rotate: false
  xy: 140, 274
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
2
  rotate: false
  xy: 140, 6
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
3
  rotate: false
  xy: 274, 6
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
4
  rotate: false
  xy: 408, 6
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
5
  rotate: false
  xy: 542, 6
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
6
  rotate: false
  xy: 676, 6
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
7
  rotate: false
  xy: 810, 6
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
8
  rotate: false
  xy: 6, 140
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
9
  rotate: false
  xy: 140, 140
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
jljl1
  rotate: false
  xy: 274, 274
  size: 128, 88
  orig: 128, 128
  offset: 0, 0
  index: -1
jljl10
  rotate: false
  xy: 542, 408
  size: 128, 77
  orig: 128, 128
  offset: 0, 10
  index: -1
jljl11
  rotate: false
  xy: 676, 408
  size: 128, 67
  orig: 128, 128
  offset: 0, 10
  index: -1
jljl12
  rotate: false
  xy: 810, 408
  size: 128, 71
  orig: 128, 128
  offset: 0, 11
  index: -1
jljl13
  rotate: false
  xy: 6, 542
  size: 128, 75
  orig: 128, 128
  offset: 0, 12
  index: -1
jljl14
  rotate: false
  xy: 140, 542
  size: 128, 77
  orig: 128, 128
  offset: 0, 14
  index: -1
jljl15
  rotate: false
  xy: 274, 542
  size: 128, 85
  orig: 128, 128
  offset: 0, 11
  index: -1
jljl16
  rotate: false
  xy: 408, 542
  size: 128, 78
  orig: 128, 128
  offset: 0, 12
  index: -1
jljl2
  rotate: false
  xy: 408, 274
  size: 128, 78
  orig: 128, 128
  offset: 0, 9
  index: -1
jljl3
  rotate: false
  xy: 542, 274
  size: 128, 81
  orig: 128, 128
  offset: 0, 11
  index: -1
jljl4
  rotate: false
  xy: 676, 274
  size: 128, 73
  orig: 128, 128
  offset: 0, 12
  index: -1
jljl5
  rotate: false
  xy: 810, 274
  size: 128, 75
  orig: 128, 128
  offset: 0, 12
  index: -1
jljl6
  rotate: false
  xy: 6, 408
  size: 128, 79
  orig: 128, 128
  offset: 0, 12
  index: -1
jljl7
  rotate: false
  xy: 140, 408
  size: 128, 83
  orig: 128, 128
  offset: 0, 12
  index: -1
jljl8
  rotate: false
  xy: 274, 408
  size: 128, 77
  orig: 128, 128
  offset: 0, 13
  index: -1
jljl9
  rotate: false
  xy: 408, 408
  size: 128, 73
  orig: 128, 128
  offset: 0, 13
  index: -1
ly17
  rotate: false
  xy: 542, 542
  size: 52, 92
  orig: 52, 92
  offset: 0, 0
  index: -1
shuihua
  rotate: false
  xy: 676, 542
  size: 34, 26
  orig: 34, 26
  offset: 0, 0
  index: -1
yu1
  rotate: false
  xy: 810, 542
  size: 66, 17
  orig: 66, 17
  offset: 0, 0
  index: -1
yu3
  rotate: false
  xy: 6, 676
  size: 65, 13
  orig: 65, 13
  offset: 0, 0
  index: -1
