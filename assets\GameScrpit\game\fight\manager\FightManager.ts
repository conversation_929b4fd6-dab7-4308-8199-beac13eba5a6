import { _decorator, Mask, Size, Node, UITransform } from "cc";
import GameObject from "../../../lib/object/GameObject";
import PlayManager from "./PlayManager";
import { ObjectManager } from "./ObjectManager";
import { HurtLabelManager } from "./HurtLabelManager";
import { HpManager } from "./HpManager";
import { TipManager } from "./TipManager";
import { BuffLayerManager } from "./BuffLayerManager";
import SkillManager from "./SkillManager";
import { RecoverManager } from "./RecoverManager";
import { BuffSpecialManager } from "./BuffSpecialManager";
import BulletManager from "./BulletManager";
import { BoutStartUp } from "../../BoutStartUp";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ShakeSection from "../../../lib/object/ShakeSection";

const { ccclass, property } = _decorator;

export const scaleList = [0.9, 1.3, 1.6];

@ccclass("FightManager")
export default class FightManager extends GameObject {
  protected static _instance: FightManager = null;

  public static get instance(): FightManager {
    if (FightManager._instance == null) {
      let node = new FightManager();
      node.name = "FightMgr";
      FightManager._instance = node;
    }
    return FightManager._instance;
  }

  private _speed: number;
  public get speed() {
    return this._speed;
  }

  private _fightOver: boolean = true;
  public get fightOver() {
    return this._fightOver;
  }
  public set fightOver(bool) {
    this._fightOver = bool;
  }

  private _main: Node = null;
  public get main(): Node {
    return this._main;
  }

  public start(args: any) {
    this.onInit();
    this._main = args.main;
    this._speed = this.getSpeed(args.speed);
    this.setupUI(args);
    this._fightOver = false;
    this.registerEvents();
    this.createSections(args);
  }

  private getSpeed(speed: number): number {
    if ([0, 1, 2].includes(speed)) {
      return speed;
    }
    // 增加对 PlayerModule.data.fightSpeed 的有效性检查
    return typeof PlayerModule.data.fightSpeed === "number" ? PlayerModule.data.fightSpeed : 0;
  }

  private setupUI(args: any) {
    let newSize = new Size(args.contentSize.width, args.contentSize.height - 50);
    if (!FightManager.instance.getComponent(UITransform)) {
      FightManager.instance.addComponent(UITransform);
    }
    FightManager.instance.getComponent(UITransform).setContentSize(newSize);
    //FightManager.instance.addComponent(Mask);
    FightManager.instance.parent = args.parent;

    FightManager.instance.walk((val) => {
      val.layer = args.parent.layer;
    });
  }

  private registerEvents() {
    MsgMgr.on(MsgEnum.ON_FIGHT_SPEED, this.up_Speed, this);
  }

  private createSections(args: any) {
    this.createSection(ShakeSection);
    this.createSection(ObjectManager);
    this.createSection(HpManager);
    this.createSection(BuffLayerManager);
    this.createSection(BuffSpecialManager);
    this.createSection(SkillManager);
    this.createSection(BulletManager);
    this.createSection(TipManager);
    this.createSection(RecoverManager);
    this.createSection(HurtLabelManager);
    this.createSection(PlayManager, args);
  }

  private up_Speed() {
    this._speed = PlayerModule.data.fightSpeed;
  }

  public exit() {
    this.unregisterEvents();
    this.removeSections();
    FightManager.instance.destroy();
    FightManager._instance = null;
  }

  private unregisterEvents() {
    MsgMgr.off(MsgEnum.ON_FIGHT_SPEED, this.up_Speed, this);
  }

  private removeSections() {
    this.removeSection(ShakeSection);
    this.removeSection(ObjectManager);
    this.removeSection(HpManager);
    this.removeSection(BuffLayerManager);
    this.removeSection(BuffSpecialManager);
    this.removeSection(SkillManager);
    this.removeSection(BulletManager);
    this.removeSection(TipManager);
    this.removeSection(RecoverManager);
    this.removeSection(HurtLabelManager);
    this.removeSection(PlayManager);
  }

  public skip() {
    // Implement skip logic if needed
  }
}
