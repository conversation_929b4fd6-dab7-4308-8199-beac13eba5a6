import { _decorator, instantiate, Node, sp, v3, Vec3 } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { DialogBox, DialogSideEnum } from "./DialogBox";
import { BundleEnum } from "../../platform/src/ResHelper";
import { BarHp } from "./BarHp";
import { TipsMgr } from "../../platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

export interface IAttrRoleArgs {
  // 走路速度
  walkSpeed: number;
  // 跑步速度
  runSpeed: number;
  // 加速度
  acceleration: number;
  // 最大血量
  hpMax: number;
  // 攻击力
  attack: number;
  // 防御力
  def: number;
}

// 角色属性
export class AttrRole {
  // 走路速度
  private _walkSpeed: number;

  // 跑步速度
  private _runSpeed: number;

  // 加速度
  private _acceleration: number;

  // 最大血量
  private _hpMax: number;

  // 攻击力
  private _attack: number;

  // 防御力
  private _def: number;

  get walkSpeed(): number {
    return this._walkSpeed;
  }

  get runSpeed(): number {
    return this._runSpeed;
  }

  get acceleration(): number {
    return this._acceleration;
  }

  get hpMax(): number {
    return this._hpMax;
  }

  get def(): number {
    return this._def;
  }

  get attack(): number {
    return this._attack;
  }

  /**
   * 初始化方法，角色的属性只能初始化，其他情况不能修改
   * @param walkSpeed 走路速度
   * @param runSpeed 跑步速度
   * @param acceleration 加速度
   */
  constructor(args: IAttrRoleArgs) {
    this._walkSpeed = args.walkSpeed;
    this._runSpeed = args.runSpeed;
    this._acceleration = args.acceleration;
    this._hpMax = args.hpMax;
    this._attack = args.attack;
  }
}

/**
 * 当前属性
 */
export interface AttrCurrent {
  hp: number;
  hpMax: number;
  speed: number;
}

// 角色spine状态，攻击或施法可以叠加状态
export enum RoleStatusEnum {
  loading = -1,
  idle = 1,
  walk,
  run,
  die,
}

// 角色方向
export enum RoleSideEnum {
  left = -1,
  right = 1,
}

// 角色控制器
// 控制角色的移动、动画、状态切换等
@ccclass("CharacterBase")
export class CharacterBase extends BaseCtrl {
  // 角色名称
  public roleName: string;

  // 当前的状态
  _status: RoleStatusEnum = RoleStatusEnum.loading;

  public get status(): RoleStatusEnum {
    return this._status;
  }

  // 面向方向
  _side: RoleSideEnum = RoleSideEnum.right;

  // 角色原属性
  protected _attr: AttrRole;
  public get attr(): AttrRole {
    return this._attr;
  }

  // 角色当前属性
  public attrCurrent: AttrCurrent = {
    hp: 0,
    hpMax: 0,
    speed: 0,
  };

  // 血条控制器
  barHp: BarHp;

  // 角色的spine动画
  protected spineCharacter: sp.Skeleton;

  // 默认的spine动画名称
  protected _spineAniDefault: string;
  protected _spineAniIdle: string;
  protected _spineAniWalk: string;
  protected _spineAniRun: string;
  protected _spineAniAttack: string;
  protected _spineAniDie: string;

  // 说话的预制体位置
  _nodeSayPos: Node;
  public get nodeSayPos(): Node {
    if (!this._nodeSayPos) {
      this._nodeSayPos = this.getNode("node_say_pos");
      if (!this._nodeSayPos) {
        log.error("没有找到node_say_pos节点");
      }
    }
    return this._nodeSayPos;
  }

  // 说话的对话框节点
  dialogBox: DialogBox = null;

  // 当前结束回调
  idleNextAction: Function;

  // 目标位置
  targetPos: Vec3;

  public get isReady() {
    return this._status > 0;
  }

  public get side(): RoleSideEnum {
    return this._side;
  }

  public set side(side: RoleSideEnum) {
    this._side = side;
    let scale = this.spineCharacter.node.scale;
    // 转向
    this.spineCharacter.node.setScale(Math.abs(scale.x) * this._side, scale.y, scale.z);
  }

  public init(args: { aniName: any; attr: IAttrRoleArgs }) {
    this.roleName = args.aniName.roleName;

    this._spineAniDefault = args.aniName.spineAniDefault;
    this._spineAniIdle = args.aniName.spineAniIdle;
    this._spineAniWalk = args.aniName.spineAniWalk;
    this._spineAniRun = args.aniName.spineAniRun;
    this._spineAniAttack = args.aniName.spineAniAttack;
    this._spineAniDie = args.aniName.spineAniDie;

    this._attr = new AttrRole(args.attr);
  }

  protected onLoad(): void {
    super.onLoad();
    this.spineCharacter = this.node.getComponentInChildren(sp.Skeleton);

    let nodeBarHp = this.getNode("BarHp");
    if (nodeBarHp) {
      this.barHp = nodeBarHp.getComponent(BarHp);
    }
  }

  start() {
    super.start();
    this.idle();
  }

  protected onEnable(): void {
    // 监听消息
  }

  protected onDisable(): void {
    // 取消监听
  }

  /**
   * 设置spine数据
   * @param spinePlayer spine动画
   */
  public setSpine(spinePlayer: sp.SkeletonData) {
    let aniName = this._spineAniDefault;
    let track: sp.spine.TrackEntry = this.spineCharacter.getCurrent(0);
    if (track) {
      aniName = track.animation.name;
    }

    this.spineCharacter.skeletonData = spinePlayer;
    this.spineCharacter.setAnimation(0, aniName, true);
  }

  /**
   * 速度变化到指定速度
   * @param deltaTime 时间差
   * @param targetSpeed 目标速度
   */
  private speedUpdate(deltaTime: number, targetSpeed: number) {
    if (this.attrCurrent.speed < targetSpeed) {
      this.attrCurrent.speed += this._attr.acceleration * deltaTime;
      if (this.attrCurrent.speed > targetSpeed) {
        this.attrCurrent.speed = targetSpeed;
      }
    } else if (this.attrCurrent.speed > targetSpeed) {
      this.attrCurrent.speed -= this._attr.acceleration * deltaTime;
      if (this.attrCurrent.speed < targetSpeed) {
        this.attrCurrent.speed = targetSpeed;
      }
    }
  }

  private move(deltaTime: number) {
    let pos = v3(
      this.node.position.x + deltaTime * this.attrCurrent.speed * this._side,
      this.node.position.y,
      this.node.position.z
    );

    if (this.targetPos) {
      if (this._side === RoleSideEnum.right) {
        if (pos.x > this.targetPos.x) {
          pos = this.targetPos;
          this.idle();
        }
      } else {
        if (this.node.position.x < this.targetPos.x) {
          pos = this.targetPos;
          this.idle();
        }
      }
    }

    this.node.setPosition(pos);
  }

  update(deltaTime: number) {
    if (!this.isReady) {
      return;
    }

    if (this._status === RoleStatusEnum.walk) {
      // 更新速度
      this.speedUpdate(deltaTime, this._attr.walkSpeed);

      this.move(deltaTime);
    } else if (this._status === RoleStatusEnum.run) {
      // 更新速度
      this.speedUpdate(deltaTime, this._attr.runSpeed);

      // 移动
      this.move(deltaTime);
    } else if (this._status === RoleStatusEnum.idle) {
      this.attrCurrent.speed = 0;
    } else if (this._status === RoleStatusEnum.die) {
      this.attrCurrent.speed = 0;
    }
  }

  /**
   * 切换动画
   * @param aniName 动画名称
   */
  protected changeAni(aniName: string) {
    let track: sp.spine.TrackEntry = this.spineCharacter.getCurrent(0);
    if (track?.animation?.name) {
      // 混合过渡动画
      let aniNameCurr = track?.animation?.name;
      this.spineCharacter.setMix(aniNameCurr, aniName, 0.2);
      this.spineCharacter.setAnimation(0, aniName, false);
      track.trackTime = track.animationEnd - 0.2;
      this.spineCharacter.addAnimation(0, aniName, true, 0);
    } else {
      this.spineCharacter.setAnimation(0, aniName, true);
    }
  }

  protected setStatus(status: RoleStatusEnum) {
    if (status === RoleStatusEnum.idle) {
      // 切换到闲置状态
    } else if (status === RoleStatusEnum.walk) {
      // 切换到走路状态
    } else if (status === RoleStatusEnum.run) {
      // 切换到跑步状态
    }
    this._status = status;
  }

  public idle(side?: RoleSideEnum) {
    this.side = side ? side : this.side;
    this.attrCurrent.speed = 0;
    this.targetPos = null;
    this.changeAni(this._spineAniIdle);
    this.setStatus(RoleStatusEnum.idle);
    if (this.idleNextAction) {
      this.idleNextAction();
      this.idleNextAction = null;
    }
  }

  /**
   * 切换状态时改变角色走向，控制相关的显示与动画切换
   * @param side 角色方向
   */
  public walk(side?: RoleSideEnum) {
    this.side = side;
    this.changeAni(this._spineAniWalk);
    this.setStatus(RoleStatusEnum.walk);
  }

  public run(side?: RoleSideEnum) {
    this.side = side;
    this.changeAni(this._spineAniRun);
    this.setStatus(RoleStatusEnum.run);
  }

  public goTo(status: RoleStatusEnum, pos: Vec3, actionEnd?: Function) {
    this.idleNextAction = actionEnd;
    this.targetPos = pos;
    if (pos.x > this.node.position.x) {
      if (status === RoleStatusEnum.walk) {
        this.walk(RoleSideEnum.right);
      } else {
        this.run(RoleSideEnum.right);
      }
    }

    if (pos.x < this.node.position.x) {
      if (status === RoleStatusEnum.walk) {
        this.walk(RoleSideEnum.left);
      } else {
        this.run(RoleSideEnum.left);
      }
    }
  }

  public attack() {
    if (this._spineAniAttack) {
      this.spineCharacter.setAnimation(1, this._spineAniAttack, false);
    }
  }

  /**
   * 说话,会自动加
   * @param word 说话的内容
   * @param endFunc 结束回调
   * @param cameraWorldPos 摄像机世界坐标
   */
  public async talk(word: string, endFunc: Function, cameraWorldPos: Vec3 = v3(0, 0, 0)) {
    if (!this.dialogBox) {
      let pbDialogBox = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_PUB, "prefab/DialogBox");
      let nodeDialogBox = instantiate(pbDialogBox);
      // nodeDialogBox.setParent(this.node);
      TipsMgr.showTipNode(nodeDialogBox);
      this.dialogBox = nodeDialogBox.getComponent(DialogBox);
    }

    this.dialogBox.node.active = true;

    let worldPos = this.nodeSayPos.getWorldPosition();
    worldPos.subtract(cameraWorldPos);

    this.dialogBox.node.setPosition(worldPos);

    if (this.spineCharacter.node.scale.x > 0) {
      this.dialogBox.typeWord(word, DialogSideEnum.right);
    } else {
      this.dialogBox.typeWord(word, DialogSideEnum.left);
    }

    this.dialogBox.registerEndCallBack(() => {
      this.dialogBox.node.active = false;
      endFunc && endFunc();
    });
  }

  /**
   * 死亡spine动画
   */
  public die(cb: Function) {
    this._status = RoleStatusEnum.die;
    if (this._spineAniDie) {
      this.changeAni(this._spineAniDie);
    }
  }

  /**
   * 设置最大血量
   * @param hpMax 最大血量
   */
  public setHpMax(hpMax: number) {
    log.info(`CharactoerBase ${this.roleName} setHpMax hpMax:${hpMax}`);
    this.attrCurrent.hpMax = hpMax;
    this.barHp.setHpMax(hpMax);
  }

  /**
   * 设置当前血量
   *
   * @param hp 当前血量
   */
  public setHp(hp: number) {
    log.info(`CharactoerBase ${this.roleName} setHp hp:${hp}`);
    this.attrCurrent.hp = hp;
    this.barHp.setHp(hp);
  }

  // 加减血量
  public hpChange(changeValue: number) {
    changeValue = changeValue || 0;
    log.info(`CharactoerBase ${this.roleName} hpChange changeValue:${changeValue}`);
    this.attrCurrent.hp -= changeValue;
    if (this.attrCurrent.hp > this.attrCurrent.hpMax) {
      this.attrCurrent.hp = this.attrCurrent.hpMax;
    } else if (this.attrCurrent.hp < 0) {
      this.attrCurrent.hp = 0;
    }

    if (this.barHp) {
      this.barHp.setHp(this.attrCurrent.hp - changeValue);
    } else {
      log.error("血条不存在");
    }
  }

  public hideUI() {
    if (this.barHp) {
      this.barHp.node.active = false;
    }
  }

  public showUI() {
    if (this.barHp) {
      this.barHp.node.active = true;
    }
  }
}
