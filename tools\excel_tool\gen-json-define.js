const XLSX = require("xlsx");
const fs = require("fs");
const path = require("path");

/**
 * Excel行读取器类
 * 用于读取指定目录下Excel文件的特定行数据
 */
class GenJsonDefineUtil {
  constructor(inputDir = "./input") {
    this.inputDir = path.resolve(inputDir);
    this.supportedExtensions = [".xlsx", ".xls"];
  }

  /**
   * 设置输入目录
   * @param {string} inputDir 输入目录路径
   */
  setInputDir(inputDir) {
    this.inputDir = path.resolve(inputDir);
  }

  /**
   * 扫描目录获取Excel文件
   * @returns {string[]} Excel文件列表
   */
  scanExcelFiles() {
    if (!fs.existsSync(this.inputDir)) {
      throw new Error(`输入目录不存在: ${this.inputDir}`);
    }

    const files = fs.readdirSync(this.inputDir);

    return files.filter((file) => {
      // 跳过临时文件
      if (file.startsWith("~$") || file.startsWith(".")) {
        return false;
      }

      // 检查扩展名
      const ext = path.extname(file).toLowerCase();
      return this.supportedExtensions.includes(ext);
    });
  }

  /**
   * 读取Excel文件的指定行数据
   * @param {string} filePath Excel文件路径
   */
  readRowData(filePath) {
    // 第4行对应索引3
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      const workbook = XLSX.readFile(filePath);
      const sheetNames = workbook.SheetNames;

      if (sheetNames.length === 0) {
        throw new Error("Excel文件中没有工作表");
      }

      const worksheet = workbook.Sheets[sheetNames[0]];
      const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");

      // 检查行是否存在
      if (2 > range.e.r) {
        return {
          success: false,
          error: `文件只有 ${range.e.r + 1} 行，无法读取第 ${2 + 1} 行`,
        };
      }

      // 读取指定行的所有列数据
      const rowData = [];
      for (let colIndex = range.s.c; colIndex <= range.e.c; colIndex++) {
        const cellAddress1 = XLSX.utils.encode_cell({ r: 0, c: colIndex });
        const cell1 = worksheet[cellAddress1];

        const cellAddress3 = XLSX.utils.encode_cell({ r: 2, c: colIndex });
        const cell3 = worksheet[cellAddress3];

        if (cell3) {
          rowData.push({ value: cell3.v, desc: cell1?.v || "" });
        }
      }

      return rowData;
    } catch (error) {
      return {
        success: false,
        filename: path.basename(filePath),
        error: error.message,
      };
    }
  }

  /**
   * 读取目录下所有Excel文件的第4行数据
   * @param {number} rowIndex 行索引（默认为3，即第4行）
   * @returns {Array} 所有文件的第4行数据
   */
  async readAllFilesRowData(rowIndex = 2) {
    try {
      console.log(`开始读取目录: ${this.inputDir}`);
      console.log(`目标行: 第${rowIndex + 1}行`);
      console.log("=".repeat(50));

      const excelFiles = this.scanExcelFiles();

      if (excelFiles.length === 0) {
        console.log("未找到Excel文件");
        return [];
      }

      console.log(`找到 ${excelFiles.length} 个Excel文件:`);

      let JsonDefineStr = "";

      let JsonConstStrImport = "";
      let JsonConstStrExport = "";

      excelFiles.forEach((filename) => {
        const filePath = path.join(this.inputDir, filename);
        const dataList = this.readRowData(filePath, rowIndex);

        let className = filename.replace(/[()（）][^()（）]*[()（）]/g, "").replace(/[()（）]/g, "");
        className = className.split(".")[0];
        const fieldName = className;
        className = className.replace("c_", "");
        className = className[0].toUpperCase() + className.slice(1);
        className = "IConfig" + className;

        if (typeof dataList[0].value !== "string" || dataList[0].value.toLowerCase() != "id:number") {
          return;
        }

        let oneType = "";
        for (let idx in dataList) {
          let v = dataList[idx].value || "";
          if (typeof v !== "string") {
            continue;
          }

          let key = v.split(":")[0];
          let type = v.split(":")[1];

          switch (type) {
            case "string":
              oneType += `\n  /** ${dataList[idx].desc} */\n  ${key}: string;`;
              break;
            case "number":
              oneType += `\n  /** ${dataList[idx].desc} */\n  ${key}: number;`;
              break;
            case "string[]":
              oneType += `\n  /** ${dataList[idx].desc} */\n  ${key}: string[];`;
              break;
            case "string[][]":
              oneType += `\n  /** ${dataList[idx].desc} */\n  ${key}: string[][];`;
              break;
            case "number[]":
              oneType += `\n  /** ${dataList[idx].desc} */\n  ${key}: number[];`;
              break;
            case "number[][]":
              oneType += `\n  /** ${dataList[idx].desc} */\n  ${key}: number[][];`;
              break;
            case "json":
              oneType += `\n  /** ${dataList[idx].desc} */\n  ${key}: any;`;
              break;
            default:
              break;
          }
        }

        if (oneType) {
          JsonConstStrImport += `\n  ${className},`;
          JsonConstStrExport += `\n  ${fieldName}: { [key: number]: ${className} };`;
          JsonDefineStr += `export interface ${className} {${oneType}\n}\n\n`;
        }
      });

      console.log("转换完成");

      return [
        JsonDefineStr,
        `import {${JsonConstStrImport}\n} from "./JsonDefine";\n\nexport class JsonConst {${JsonConstStrExport}\n}`,
      ];
    } catch (error) {
      console.error("读取过程中发生错误:", error.message);
      throw error;
    }
  }
}

// 导出
module.exports = {
  GenJsonDefineUtil,
};
