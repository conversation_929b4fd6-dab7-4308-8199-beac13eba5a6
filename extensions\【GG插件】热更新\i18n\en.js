module.exports = {
    title: "gg-hot-update",
    description: "GG Hot Update Plugin",
    build_item_enable_label: "Generate Manifest",
    build_item_enable_desc: "Generate hot update package manifest according to the hot update build file",
    build_item_config_path_label: "Hot Update Build File Path",
    build_item_config_path_desc: "Relative path of hot update build file (relative to the path of the project root directory) e.g. settings/gg-hot-update-config.json",
};
