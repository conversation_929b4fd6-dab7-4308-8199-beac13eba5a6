import { _decorator, sp, Sprite } from "cc";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIHunting } from "../ui/UIHunting";
import { HuntingBase } from "../../HuntingBase";
import { EventActionModule } from "../../EventActionModule";
import MsgEnum from "db://assets/GameScrpit/game/event/MsgEnum";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { EventRepelResponse } from "db://assets/GameScrpit/game/net/protocol/WorldEvent";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("Event_103")
export class Event_103 extends HuntingBase {
  private _eventId: number;
  init(args: any): void {
    this._eventId = args.eventId;
  }
  protected start(): void {
    super.start();
    this.updateProgress();
    this.getNode("node_atk")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("node_atk").active = false;
      });

    this.getNode("render")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("render").getComponent(sp.Skeleton).setAnimation(0, "idle", false);
      });
  }
  private updateProgress() {
    if (!this._eventId) {
      return;
    }
    let progress = EventActionModule.data.eventTrainMessage.eventMap[this._eventId].progress;
    let totalProgress = EventActionModule.data.eventTrainMessage.eventMap[this._eventId].totalProgress;
    this.getNode("node_progress").getComponent(Sprite).fillRange = 1 - progress / totalProgress;
    if (progress >= totalProgress) {
      this.getNode("render").getComponent(sp.Skeleton).setAnimation(0, "die", false);
      setTimeout(() => {
        this.closeBack();
        MsgMgr.emit(MsgEnum.ON_THIEF_UNFOCOUS, this.node);
      }, 1000);
    }
  }

  onClickShangao() {
    RouteManager.uiRouteCtrl.showRoute(UIHunting, {
      payload: { eventId: this._eventId, eventNode: this.node },
    });
  }
  public atk(): void {
    let progress = EventActionModule.data.eventTrainMessage.eventMap[this._eventId].progress;
    let totalProgress = EventActionModule.data.eventTrainMessage.eventMap[this._eventId].totalProgress;
    if (progress >= totalProgress) {
      return;
    }
    this.getNode("node_atk").active = true;
    this.getNode("node_atk").getComponent(sp.Skeleton).setAnimation(0, "wuqi2", false);
    this.getNode("render").getComponent(sp.Skeleton).setAnimation(0, "hurt", false);
    EventActionModule.api.postExpel(this._eventId, (data: EventRepelResponse) => {
      this.updateProgress();
      if (data.rewardMessage) {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
      }
    });
  }
  public focusHunt() {
    //
    this.getNode("hp").active = true;
  }
  public unfocusHunt() {
    //
    this.getNode("hp").active = false;
  }
}
