import { _decorator, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { Sprite } from "cc";
import { Animation } from "cc";
import { Label } from "cc";
import { math } from "cc";
import { ResHelper } from "../../../../platform/src/ResHelper";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { SevenDaySignResponse } from "../../net/protocol/Activity";
import TipMgr from "../../../lib/tips/TipMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { Button } from "cc";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { HdSevenModule } from "../../../module/hd_seven/HdSevenModule";
import { ActivityID } from "../../../module/activity/ActivityConstant";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

enum SignStatusEnum {
  LOCK,
  NON_TAKE,
  TAKE,
  COST_TAKE,
  EXPIRED,
}

/**
 *
 * hopewsw
 * Tue Oct 15 2024 15:52:39 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_hd/UIHdSeven.ts
 *
 */
@ccclass("UIHdSeven")
export class UIHdSeven extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_SEVEN}?prefab/ui/UIHdSeven`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_HD_SEVEN, BundleEnum.BUNDLE_COMMON_FRIEND];
  }

  private _selectedIdx = 0;

  //=================================================
  public init(args: any): void {
    super.init(args);
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_ACTIVITY_SEVEN_UPDATE, this.refresh, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_ACTIVITY_SEVEN_UPDATE, this.refresh, this);
  }

  protected onEvtShow(): void {
    super.onEvtShow();

    if (!HdSevenModule.config.activityConfig) {
      log.error("没有找到活动配置-七日签到");
    }

    // 获取玩家签到数据
    HdSevenModule.api.sevenDaySign(ActivityID.SEVEN_DAY);

    // 修改仙友层级
    this.getNode("node_camera").walk((child) => {
      child.layer = 4;
    });

    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_qiandaolingqu"),
      BadgeType.UITerritory.btn_activity_7.btn_qiandaolingqu.id
    );
  }

  // 刷新界面
  private async refresh() {
    // 设置倒计时
    FmUtils.setCd(this.getNode("lbl_cd"), HdSevenModule.data.sevenDaySignMessage?.endTime || 0);

    // 前6天状态设置
    for (let i = 0; i < 6; i++) {
      const nodeDay = this.getNode("node_day" + (i + 1));

      const nodeItem = nodeDay.getChildByName("Item");
      let ani = nodeItem.getComponent(Animation);
      ani.playOnLoad = false;
      ani.stop();

      nodeItem.setRotation(0, 0, 0, 0);

      nodeDay.getChildByName("bg_icon_duihao").active = false;

      // 状态判断
      let status: SignStatusEnum;
      if (HdSevenModule.data.sevenDaySignMessage.count > i) {
        status = SignStatusEnum.TAKE;
      } else if (HdSevenModule.data.sevenDaySignMessage.count == i) {
        if (HdSevenModule.data.sevenDaySignMessage.sign) {
          status = SignStatusEnum.LOCK;
        } else {
          status = SignStatusEnum.NON_TAKE;
        }
      } else {
        status = SignStatusEnum.LOCK;
      }

      // 设置已签
      if (status == SignStatusEnum.TAKE) {
        nodeDay.getChildByName("bg_biaoqoan").active = true;
        nodeDay.getChildByName("bg_xuanzhong").active = false;

        FmUtils.setNodeDark(nodeItem, 100);
        nodeDay.getChildByName("bg_icon_duihao").active = true;

        nodeItem.getComponentInChildren(Label).color = math.color("cccccc");
      }
      // 设置可签
      else if (status == SignStatusEnum.NON_TAKE) {
        nodeDay.getChildByName("bg_biaoqoan").active = true;
        nodeDay.getChildByName("bg_xuanzhong").active = false;
        ani.play();
      }
      // 设置锁定
      else if (status == SignStatusEnum.LOCK) {
        nodeDay.getChildByName("bg_biaoqoan").active = true;
        nodeDay.getChildByName("bg_xuanzhong").active = false;
      }
      let rewardList = HdSevenModule.config.activityConfig.signList[i].rewardList;
      FmUtils.setItemNode(nodeItem, rewardList[0], rewardList[1], true);
      nodeItem.getComponent(Button).enabled = false;

      nodeDay.active = true;

      nodeDay.on(Node.EventType.TOUCH_END, (e) => {
        if (status == SignStatusEnum.NON_TAKE && !HdSevenModule.data.sevenDaySignMessage.sign) {
          this.severQianDao();
        }
        this.onSelect(i);
      });
    }

    let nodeDay7 = this.getNode("node_day7");
    // 第七天绑定事件
    nodeDay7.on(Node.EventType.TOUCH_END, (e) => {
      this.onSelect(6);
    });

    // 设定绑定的东西
    let rewardList = HdSevenModule.config.activityConfig.signList[6].rewardList;
    FmUtils.setItemNode(nodeDay7.getChildByName("Item"), rewardList[0], rewardList[1], true);
    nodeDay7.getChildByName("Item").getComponent(Button).enabled = false;

    let isTake7 = HdSevenModule.data.sevenDaySignMessage.count == 7;

    // 已选中
    nodeDay7.getChildByName("bg_icon_duihao").active = isTake7;

    let defaultIdx = 0;
    if (HdSevenModule.data.sevenDaySignMessage.sign) {
      defaultIdx = HdSevenModule.data.sevenDaySignMessage.count - 1;
    } else {
      defaultIdx = HdSevenModule.data.sevenDaySignMessage.count;
    }
    defaultIdx = Math.max(defaultIdx, 0);
    defaultIdx = Math.min(defaultIdx, 6);
    await this.onSelect(defaultIdx);

    // 物品状态
    FmUtils.setNodeDark(nodeDay7.getChildByName("Item"), isTake7 ? 100 : 255);

    log.log(HdSevenModule.data.sevenDaySignMessage);

    // 签到按钮显示
    this.getNode("bg_yiqiandao").active = HdSevenModule.data.sevenDaySignMessage.sign;
    this.getNode("btn_qiandaolingqu").active = !HdSevenModule.data.sevenDaySignMessage.sign;

    if (HdSevenModule.data.sevenDaySignMessage.count >= 7) {
      this.getNode("btn_qiandaolingqu").active = false;
    }
  }

  // 选中事件
  private onSelect(idx: number) {
    this._selectedIdx = idx;

    for (let i = 0; i < 7; i++) {
      let nodeItem = this.getNode("node_day" + (i + 1));

      let nodeActive = nodeItem.getChildByName("bg_biaoqoan");
      if (!nodeActive?.active) {
        nodeActive = nodeItem.getChildByName("bg_xuanzhong");
      }

      if (!nodeActive?.active) {
        nodeActive = nodeItem.getChildByName("bg_diqibiaoqian");
      }

      if (nodeActive?.active) {
        if (i == idx) {
          let sp = nodeActive.getComponent(Sprite);
          ResHelper.preLoadResSync("bundle_hd_seven", "materials/outline-glow").then((asset) => {
            if (this._selectedIdx == i) {
              sp.customMaterial = asset;
            }
          });
        } else {
          let sp = nodeActive.getComponent(Sprite);
          sp.customMaterial = null;
        }
      }

      nodeItem.getChildByName("bg_day_unactive").active = i != idx;
    }

    let isTake = HdSevenModule.data.sevenDaySignMessage.count > idx;

    let rewardList = HdSevenModule.config.activityConfig.signList[idx].rewardList;
    let nodeItemList = this.getNode("node_item_list");
    nodeItemList.children.forEach((e) => {
      e.active = false;
    });

    for (let i = 0; i < rewardList.length; i += 2) {
      let nodeItem = nodeItemList.getChildByName("node" + (i / 2 + 1));
      FmUtils.setItemNode(nodeItem.getChildByName("Item"), rewardList[i], rewardList[i + 1]);
      nodeItem.active = true;

      nodeItem.getChildByName("bg_icon_duihao").active = isTake;

      FmUtils.setNodeDark(nodeItem.getChildByName("Item"), isTake ? 100 : 255);
    }

    // 2个和4个的布局不同
    if (rewardList.length <= 4) {
      nodeItemList.getChildByName("node1").setPosition(-136, 10);
      nodeItemList.getChildByName("node2").setPosition(80, -3);
    } else {
      nodeItemList.getChildByName("node1").setPosition(-200, 10);
      nodeItemList.getChildByName("node2").setPosition(-75, 10);
    }

    // 天数显示
    this.getNode("node_lbl_day").children.forEach((e, index) => (e.active = index == idx));
  }

  // 关闭事件
  on_click_btn_close() {
    UIMgr.instance.back();
  }

  // 签到领奖事件
  on_click_btn_qiandaolingqu() {
    if (!HdSevenModule.data.sevenDaySignMessage?.activityId) {
      TipMgr.showTip("数据未准备好");
      return;
    }
    this.severQianDao();
  }

  private severQianDao() {
    if (!HdSevenModule.data.sevenDaySignMessage.sign) {
      HdSevenModule.api.takeSevenDayReward(ActivityID.SEVEN_DAY, (resp: SevenDaySignResponse) => {
        // 弹窗奖励
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, {
          itemList: resp.rewardMessage.rewardList,
          transformList: resp.rewardMessage.transformList,
        });
      });
    }
  }

  private on_click_btn_xiangxixinxi() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 21 });
  }
}
