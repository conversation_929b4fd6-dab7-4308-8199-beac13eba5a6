import org.apache.tools.ant.taskdefs.condition.Os

apply plugin: 'com.android.application'

RES_PATH = RES_PATH.replace("\\", "/")
COCOS_ENGINE_PATH = COCOS_ENGINE_PATH.replace("\\", "/")

buildDir = "${RES_PATH}/proj/build/${project.name ==~ /^[_a-zA-Z0-9-]+$/ ? project.name : 'CocosGame'}"
android {
    compileSdkVersion PROP_COMPILE_SDK_VERSION.toInteger()
    buildToolsVersion PROP_BUILD_TOOLS_VERSION
    ndkPath PROP_NDK_PATH
    namespace APPLICATION_ID

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId APPLICATION_ID
        minSdkVersion PROP_MIN_SDK_VERSION
        targetSdkVersion PROP_TARGET_SDK_VERSION
        versionCode 3
        versionName "1.1.2"

        externalNativeBuild {
            cmake {
                targets "cocos"
                arguments "-DRES_DIR=${RES_PATH}", "-DANDROID_STL=c++_static", "-DANDROID_TOOLCHAIN=clang", "-DANDROID_ARM_NEON=TRUE"
            }
            ndk { abiFilters PROP_APP_ABI.split(':') }
        }
    }

    sourceSets.main {
        java.srcDirs "../src", "src"
        res.srcDirs "../res", 'res', "${RES_PATH}/proj/res"
        jniLibs.srcDirs "../libs", 'libs'
        manifest.srcFile "AndroidManifest.xml"
        assets.srcDir "${RES_PATH}/data"
        jniLibs {
            // Vulkan validation layer
            // srcDir "${android.ndkDirectory}/sources/third_party/vulkan/src/build-android/jniLibs"
        }
    }

    externalNativeBuild {
        cmake {
            version "3.22.1"
            path "../CMakeLists.txt"
            buildStagingDirectory "${RES_PATH}/proj/build"
        }
    }

    signingConfigs {

       release {
            if (project.hasProperty("RELEASE_STORE_FILE") && !RELEASE_STORE_FILE.isEmpty()) {
                storeFile file(RELEASE_STORE_FILE)
                storePassword RELEASE_STORE_PASSWORD
                keyAlias RELEASE_KEY_ALIAS
                keyPassword RELEASE_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        release {
            debuggable true
            jniDebuggable false
            renderscriptDebuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                signingConfig signingConfigs.release
            }
                        
            externalNativeBuild {
                cmake {
                    // switch HIDE_SYMBOLS to OFF to skip compilation flag `-fvisibility=hidden`
                    arguments "-DHIDE_SYMBOLS=ON"
                }
            }

            if (!Boolean.parseBoolean(PROP_IS_DEBUG)) {
                getIsDefault().set(true)
            }

        }

        debug {
            debuggable true
            jniDebuggable true
            renderscriptDebuggable true
            // resValue  "string", "app_name", "${PROP_APP_NAME}-dbg"
            // applicationIdSuffix ".debug"
        }
    }
    ndkVersion '21.4.7075529'
}

dependencies {
    implementation fileTree(dir: '../libs', include: ['*.jar','*.aar'])
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation fileTree(dir: "${COCOS_ENGINE_PATH}/cocos/platform/android/java/libs", include: ['*.jar'])
    implementation project(':libservice')
    implementation project(':libcocos')
    if (Boolean.parseBoolean(PROP_ENABLE_INPUTSDK)) {
        implementation 'com.google.android.libraries.play.games:inputmapping:1.1.0-beta'
        implementation "org.jetbrains.kotlin:kotlin-stdlib:1.4.10"
    }
}
