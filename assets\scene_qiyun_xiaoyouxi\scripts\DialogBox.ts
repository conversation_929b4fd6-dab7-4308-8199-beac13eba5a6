import { _decorator, Component, Label, Node } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

export enum DialogSideEnum {
  left = -1,
  right = 1,
}

@ccclass("DialogBox")
export class DialogBox extends BaseCtrl {
  // 不自动设置层级
  protected autoSetLayer: boolean = false;

  // 内容label
  lblContent: Label;

  // 要显示的内容
  word: string;

  // 打字速度
  cd = 0.1;

  // 当前间隔时间
  curCd = 0;

  // 对话结束回调
  onEnd: Function;

  protected onLoad(): void {
    this.lblContent = this.getNode("lbl_content").getComponent(Label);
  }

  // 初始化对话
  public typeWord(word: string, side: DialogSideEnum) {
    this.getNode("btn_close").active = true;
    this.word = word;
    this.lblContent.string = "";
    if (side == DialogSideEnum.left) {
      this.getNode("bg_duihuakuang").setScale(-1, 1);
    } else {
      this.getNode("bg_duihuakuang").setScale(1, 1);
    }
  }

  update(deltaTime: number) {
    if (!this.word || this.word.length == 0) {
      return;
    }
    this.curCd += deltaTime;
    if (this.curCd > this.cd) {
      this.curCd = 0;
      this.lblContent.string += this.word[0];
      this.word = this.word.substring(1);
    }
  }

  registerEndCallBack(callBack: Function) {
    this.onEnd = callBack;
  }

  on_click_btn_close() {
    if (this.word) {
      this.lblContent.string += this.word;
      this.word = "";
    } else {
      this.getNode("btn_close").active = false;

      this.onEnd && this.onEnd();
    }
  }
}
