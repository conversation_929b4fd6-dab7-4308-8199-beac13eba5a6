import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ler<PERSON><PERSON>, ApiHandlerSuccess } from "../../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../../game/net/cmd/CmdData";
import { RechargeRewardTakeResponse } from "../../../game/net/protocol/Activity";
import { IntValueList, LongValue } from "../../../game/net/protocol/ExternalMessage";
import { VipModule } from "./VipModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);

export class VipApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }
  /**
   * 查看贵族好礼奖励领取情况
   * @param success
   */
  public rechargeGoods(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(IntValueList, ActivityCmd.rechargeGoods, null, (data: IntValueList) => {
      success && success(data);
    });
  }
  /**
   * 领取贵族礼包
   * @param index
   * @param success
   */
  public takeRechargeGoods(index: number, success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      RechargeRewardTakeResponse,
      ActivityCmd.takeRechargeGoods,
      LongValue.encode({ value: index }),
      (data: RechargeRewardTakeResponse) => {
        success && success(data);
      }
    );
  }
}
