import { _decorator, instantiate, Label, math, Node, Sprite, TERRAIN_NORTH_INDEX } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ClubModule } from "../../../../module/club/ClubModule";
import { ClubJoinResponse, ClubMessage } from "../../../net/protocol/Club";
import Formate from "../../../../lib/utils/Formate";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { UIClubAvatar } from "../UIClubAvatar";
import ResMgr from "db://assets/GameScrpit/lib/common/ResMgr";
const { ccclass, property } = _decorator;
@ccclass("ClubRankViewHolder")
export class ClubRankViewHolder extends ViewHolder {
  @property(Label)
  club_name: Label;
  @property(Label)
  club_level: Label;
  @property(Label)
  club_members: Label;
  @property(Label)
  club_power: Label;
  @property(Node)
  clubRank: Node;
  @property(Node)
  clubAvatar: Node;

  _data: ClubMessage;
  public init() {}
  public updateData(data: any, position: number) {
    if (position % 2 == 0) {
      this.node.getComponent(Sprite).color = math.color("#d7f1ff");
    } else {
      this.node.getComponent(Sprite).color = math.color("#bedff6");
    }
    this._data = data;
    this.club_level.string = `LV.${this._data.level}`;
    this.club_name.string = this._data.name;
    let maxMembers = ClubModule.config.getUnionMaxNumber(this._data.level);
    this.club_members.string = `${this._data.memberList.length}/${maxMembers}`;
    this.clubAvatar.getComponent(UIClubAvatar).setAvatar(this._data.avatar);
    this.club_power.string = `${Formate.format(this._data.totalPower)}`;
    if (position == 0) {
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_G_CLUB,
        `atlas_icons/YWC_icon_paiming1`,
        this.clubRank.getComponent(Sprite)
      );
      this.clubRank.getChildByName("txt").active = false;
    } else if (position == 1) {
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_G_CLUB,
        `atlas_icons/YWC_icon_paiming2`,
        this.clubRank.getComponent(Sprite)
      );
      this.clubRank.getChildByName("txt").active = false;
    } else if (position == 2) {
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_G_CLUB,
        `atlas_icons/YWC_icon_paiming3`,
        this.clubRank.getComponent(Sprite)
      );
      this.clubRank.getChildByName("txt").active = false;
    } else {
      ResMgr.setSpriteFrame(BundleEnum.BUNDLE_G_CLUB, `atlas_icons/dz_bg_mingci`, this.clubRank.getComponent(Sprite));
      this.clubRank.getChildByName("txt").active = true;
      this.clubRank.getChildByName("txt").getComponent(Label).string = `${position + 1}`;
    }
  }
}
export class ClubRankAdapter extends ListAdapter {
  private item: Node;
  private data: any[];
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }

  getViewType(position: number): number {
    return 0;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(ClubRankViewHolder).init();
    return item;
  }
  onBindData(view: Node, position: number): void {
    view.getComponent(ClubRankViewHolder).updateData(this.data[position], position);
  }
  getCount(): number {
    return this.data.length;
  }
}
