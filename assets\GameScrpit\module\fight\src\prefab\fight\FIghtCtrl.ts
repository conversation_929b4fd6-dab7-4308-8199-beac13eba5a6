import { _decorator, instantiate, Label, Node, Prefab, v3, Vec3 } from "cc";
import { CharacterCtrl } from "./CharacterCtrl";
import { Sleep } from "db://assets/GameScrpit/game/GameDefine";
import { BaseSkillType, BattleActionDTO, FaceToEnum } from "../../FightConstant";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { BundleEnum } from "db://assets/platform/src/ResHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { FightManager } from "./FightManager";
const { ccclass, property } = _decorator;

interface CharacterDataType {
  node: Node;
  ctrl: CharacterCtrl;
  id: number;
  hp: number;
  hpMax: number;
  眩晕: number;
}

@ccclass("FIghtCtrl")
export class FIghtCtrl extends BaseCtrl {
  // 站位
  pos1: Vec3 = v3(-200, 0, 0);
  pos2: Vec3 = v3(200, 0, 0);

  // 战斗管理
  fightMgr: FightManager;

  // 行动已过时间长
  actionTimePass = 0;

  // 行动所需时长
  actionTimeNeed = 0;

  characterLeftCtrl: CharacterCtrl;
  characterRightCtrl: CharacterCtrl;

  // 站位线
  node_line1: Node;

  // 回合进度
  lbl_round: Label;

  init(args: { battleData: any }) {
    // 解析录像成为可读样式，不改格式
    this.fightMgr = FightManager.getInstance();
    this.fightMgr.init(args.battleData);
  }

  async start() {
    this.node_line1 = this.getNode("node_line1");
    this.lbl_round = this.getNode("lbl_round").getComponent(Label);

    // 加载角色
    let pbCharacter = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_FIGHT, "prefab/fight/Character");

    // 左边角色加载
    const nodeCharacterLeft = instantiate(pbCharacter);
    nodeCharacterLeft.parent = this.node_line1;
    this.characterLeftCtrl = nodeCharacterLeft.getComponent(CharacterCtrl);
    this.characterLeftCtrl.init({ pos: this.pos1, characterDTO: this.fightMgr.getCharacter(0, 0) });

    // 右边角色加载
    const nodeCharacterRight = instantiate(pbCharacter);
    nodeCharacterRight.parent = this.node_line1;
    this.characterRightCtrl = nodeCharacterRight.getComponent(CharacterCtrl);
    this.characterRightCtrl.init({ pos: this.pos2, characterDTO: this.fightMgr.getCharacter(1, 0) });

    // 战斗开始动画
    TipsMgr.showTip("播放战斗开始动画");
    await Sleep(1);

    this.lbl_round.string = `战斗准备`;

    //
    // for (let idxAction = 0; idxAction < this.fightMgr; idxAction++) {
    //   const action = // this.battleData.beforeRoundActionList[idxAction];

    //   const actionData = this.characterMap[action.characterId];
    //   const targetData = this.characterMap[action.targetId];

    //   // 是否转向
    //   if (actionData.node.position.x < targetData.node.position.x) {
    //     if (actionData.ctrl.getFaceTo() == FaceToEnum.left) {
    //       actionData.ctrl.setFaceTo(FaceToEnum.right);
    //     }
    //   } else if (actionData.node.position.x > targetData.node.position.x) {
    //     if (actionData.ctrl.getFaceTo() == FaceToEnum.right) {
    //       actionData.ctrl.setFaceTo(FaceToEnum.left);
    //     }
    //   }

    //   // 行动方式
    // }

    // // 回合开始
    // for (let idx = 0; idx < this.battleData.roundList.length; idx++) {
    //   // 回合开始前的行动

    //   const round = this.battleData.roundList[idx];
    //   this.lbl_round.string = `第${idx + 1}/${this.battleData.scene.roundMax}回合`;
    // }

    // 战斗结束
  }

  public caculateActionTime(actionDTO: BattleActionDTO) {
    if (actionDTO.actionType == BaseSkillType.普攻) {
    }
  }

  protected update(dt: number): void {
    if (this.actionTimeNeed <= this.actionTimePass) {
      const actionDTO = this.fightMgr.fetchAction();
      // 战斗结束
      if (actionDTO == null) {
        return;
      }

      this.actionTimePass = 0;
      this.actionTimeNeed = 0.6;
    } else {
      this.actionTimePass += dt;
    }
  }
}
