import { IAttr } from "../../game/GameDefine";
import { ConfigSoulRecord } from "../../game/bundleDefine/bundle_soul_define";
import MsgEnum from "../../game/event/MsgEnum";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { WarriorSoulManageMessage, WarriorSoulMessage } from "../../game/net/protocol/Soul";
import MsgMgr from "../../lib/event/MsgMgr";
import TipMgr from "../../lib/tips/TipMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class SoulData {
  /**武魂 */
  private _warriorSoulManageMsg: WarriorSoulManageMessage = null;

  private _soulMap: { [key: number]: number } = {};

  public init() {
    this.warriorSoulManageMsg = null;
  }
  public set warriorSoulManageMsg(res: WarriorSoulManageMessage) {
    this._warriorSoulManageMsg = res;
    // 刷新武魂数量
    this.initSoulNumMap();
    MsgMgr.emit(MsgEnum.ON_SOUL_UPDATE, res);
  }

  public get warriorSoulManageMsg(): WarriorSoulManageMessage {
    return this._warriorSoulManageMsg;
  }

  private initSoulNumMap() {
    this._soulMap = {};
    if (!this._warriorSoulManageMsg) {
      return;
    }
    let soulList = Object.values(this._warriorSoulManageMsg?.soulMap);
    for (let i = 0; soulList && i < soulList.length; i++) {
      if (this._soulMap[soulList[i].soulTemplateId]) {
        this._soulMap[soulList[i].soulTemplateId] += 1;
      } else {
        this._soulMap[soulList[i].soulTemplateId] = 1;
      }
    }
  }

  public getSoulNumById(soulId: number) {
    return this._soulMap[soulId] ?? 0;
  }

  /**获取用户所有武魂Id 排序 */
  public getSoulIdList(): number[] {
    let list = Object.keys(this._warriorSoulManageMsg.soulMap).map(Number);
    list.sort((a, b) => {
      let a0 = Number(a);
      let b0 = Number(b);

      let a1: WarriorSoulMessage = this._warriorSoulManageMsg.soulMap[a0];
      let b1: WarriorSoulMessage = this._warriorSoulManageMsg.soulMap[b0];
      let a2: ConfigSoulRecord = JsonMgr.instance.jsonList.c_soul[a1.soulTemplateId];
      let b2: ConfigSoulRecord = JsonMgr.instance.jsonList.c_soul[b1.soulTemplateId];

      if (a1.chosen == b1.chosen) {
        if (a2.color == b2.color) {
          if (a1.stage == b1.stage) {
            if (a1.grade == b1.grade) {
              return 0;
            }
            return a1.grade - b1.grade;
          }
          return a1.stage - b1.stage;
        }
        return a2.color - b2.color;
      }
      return Number(a1.chosen) - Number(b1.chosen);
    });

    return list.reverse();
  }

  public getSpulAttrMap() {
    let attr = new IAttr();

    if (!this._warriorSoulManageMsg?.soulMap) {
      return attr;
    }

    let list = Object.keys(this._warriorSoulManageMsg.soulMap).map(Number);
    for (let i = 0; i < list.length; i++) {
      let soul = this._warriorSoulManageMsg.soulMap[list[i]];
      if (soul.chosen) {
        Object.keys(soul.attrMap)
          .map(Number)
          .forEach((attrId) => {
            attr[attrId] += soul.attrMap[attrId];
          });
        break;
      }
    }
    return attr;
  }

  public getConfigSoul(id: number = 41001): ConfigSoulRecord {
    let cfg = JsonMgr.instance.jsonList.c_soul[id];
    if (!cfg) {
      TipMgr.showTip("兽魂配置ID不存：" + id);
      log.error("兽魂配置ID不存：" + id);
      return JsonMgr.instance.jsonList.c_soul[41001];
    }
    return cfg;
  }
}
