import { _decorator, EventTouch } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { FractureModule } from "../../../module/fracture/FractureModule";
import { SimpleRankMessage } from "../../net/protocol/Activity";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { FractureRankAdapter } from "./adapter/FractureRankViewHolder";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { MessageComponent } from "../../../../platform/src/core/ui/components/MessageComponent";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";
import { ActivityModule } from "../../../module/activity/ActivityModule";
import { FractureActivityConfig } from "../../../module/fracture/FractureConstant";
import { FractureRankAwardAdapter } from "./adapter/FractureRankAwardViewHolder";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import FmUtils from "../../../lib/utils/FmUtils";
import { LangMgr } from "../../mgr/LangMgr";
import { BtnPlayerCtrl } from "db://assets/GameScrpit/game/common/BtnPlayerRankCtrl";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("UIFractureRank")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureRank",
  nextHop: [],
  exit: "",
})
export class UIFractureRank extends BaseCtrl {
  public playShowAni: boolean = true;
  private _adapter: FractureRankAdapter = null;
  start() {
    super.start();
    FractureModule.api.rankInfo((data: SimpleRankMessage) => {
      log.log("rankInfo", data);
      this.refreshRank(data);
    });
    this._adapter = new FractureRankAdapter(this.getNode("node_rank_viewholder"));
    this.getNode("node_rank_list").getComponent(AdapterView).setAdapter(this._adapter);
    this.getNode("node_award").active = false;
    this.getNode("node_rank").active = true;

    this.refreshRankAward();
  }

  update(deltaTime: number) {}

  private refreshRankAward() {
    //
    let activity = ActivityModule.data.allActivityConfig[11501] as FractureActivityConfig;
    let adapter = new FractureRankAwardAdapter(this.getNode("node_award_viewholder"));
    this.getNode("node_award_list").getComponent(AdapterView).setAdapter(adapter);
    adapter.setData(activity.rankRewardList);
    FmUtils.setCd(this.getNode("lbl_award_cd"), activity.endTime, true);
  }

  private refreshRank(data: SimpleRankMessage) {
    this.getNode("node_rank1")
      .getComponent(BtnPlayerCtrl)
      .setPlayer(data?.rankList[0]?.detailMessage ?? null);
    this.getNode("node_rank2")
      .getComponent(BtnPlayerCtrl)
      .setPlayer(data?.rankList[1]?.detailMessage ?? null);
    this.getNode("node_rank3")
      .getComponent(BtnPlayerCtrl)
      .setPlayer(data?.rankList[2]?.detailMessage ?? null);

    if (data.rankList.length > 3) {
      //取data.rankList[3]开始到结束
      const subList = data.rankList.slice(3);
      this._adapter.setData(subList);
    }

    if (data.rank < 0) {
      this.getNode("lbl_my_rank").getComponent(MessageComponent).args = [LangMgr.txMsgCode(648)];
    } else {
      this.getNode("lbl_my_rank").getComponent(MessageComponent).args = [`${data.rank}`];
    }
    this.getNode("lbl_my_points").getComponent(MessageComponent).args = [`${data.point}`];
  }

  private on_click_btn_tab1(e: EventTouch) {
    AudioMgr.instance.playEffect(1771);
    this.getNode("btn_tab1").getComponent(FmButton).selected = true;
    this.getNode("btn_tab2").getComponent(FmButton).selected = false;
    this.getNode("node_award").active = false;
    this.getNode("node_rank").active = true;
  }

  private on_click_btn_tab2(e: EventTouch) {
    AudioMgr.instance.playEffect(1771);
    this.getNode("btn_tab1").getComponent(FmButton).selected = false;
    this.getNode("btn_tab2").getComponent(FmButton).selected = true;
    this.getNode("node_award").active = true;
    this.getNode("node_rank").active = false;
  }
}
