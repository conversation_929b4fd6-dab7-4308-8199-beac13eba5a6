import { sp, instantiate, EventTouch, Vec3, Input, _decorator, Component, Node } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import ToolExt from "../ToolExt";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass } = _decorator;

@ccclass("UITouchLayer")
export class UITouchLayer extends BaseCtrl {
  private btnTouchNode: Node;
  private effectNode: Node;
  private effectLayer: Node;

  private _checkPlayEffectAudio: boolean = false;

  protected update(dt: number): void {
    if (this._checkPlayEffectAudio) {
      this._checkPlayEffectAudio = false;
      if (!AudioMgr.instance.isEffectPlaying()) {
        AudioMgr.instance.playDefaultEffect(AudioName.Effect.按钮点击);
      }
    }
  }
  start() {
    this.btnTouchNode = this.node.getChildByName("btn_touch");
    this.effectNode = this.node.getChildByName("effect");
    this.effectLayer = this.node.getChildByName("effectLayer");

    this.btnTouchNode.on(Input.EventType.TOUCH_START, ToolExt.tryFunc(this.click_btn_touch.bind(this)), this);
    this.btnTouchNode.on(Input.EventType.TOUCH_CANCEL, ToolExt.tryFunc(this.click_btn_touch.bind(this)), this);
    this.btnTouchNode.on(Input.EventType.TOUCH_MOVE, ToolExt.tryFunc(this.click_btn_touch.bind(this)), this);
    this.btnTouchNode.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.click_btn_touch.bind(this)), this);
    this.node.setPosition(new Vec3(0, 0, 0));
  }

  protected onEnable(): void {
    this.node.setPosition(new Vec3(0, 0, 0));
  }

  private click_btn_touch(event: EventTouch) {
    event.preventSwallow = true;
    if (event.type != "touch-end") {
      return;
    }
    this._checkPlayEffectAudio = true;

    MsgMgr.emit(MsgEnum.ON_USER_CLICK);

    try {
      let scPos = event.getUILocation();
      let effectNodeCopy = instantiate(this.effectNode);
      this.effectLayer.addChild(effectNodeCopy);
      effectNodeCopy.setWorldPosition(scPos.x, scPos.y, 0);
      effectNodeCopy.active = true;

      effectNodeCopy.getComponent(sp.Skeleton).setAnimation(0, "action", false);
      effectNodeCopy.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("action" == trackEntry.animation.name) {
          effectNodeCopy.destroy();
        }
      });
    } catch (error) {
      log.error(error);
    }
  }
}
