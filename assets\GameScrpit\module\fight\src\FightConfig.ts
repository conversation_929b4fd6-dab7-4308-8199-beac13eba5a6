export class FightMsgEnum {
  /**宝箱数据的更新 */
  public static ON_FIGHT_TREASURE_UPDATE: string = "ON_FIGHT_TREASURE_UPDATE";

  /**关卡进度数据的更新 */
  public static ON_FIGHT_CHAPTER_UPDATE: string = "ON_FIGHT_CHAPTER_UPDATE";

  /**气运鼓舞数据更新 */
  public static ON_FIGHT_ENERGY_UPDATE: string = "ON_FIGHT_ENERGY_UPDATE";

  /**道具鼓舞数据更新 */
  public static ON_FIGHT_ITEM_UPDATE: string = "ON_FIGHT_ITEM_UPDATE";
}
export const FightAudioName = {
  Effect: {
    跑步: 1641,
    冲锋音效: 1642,
    撞击: 1643,
  },
  Sound: {},
};
