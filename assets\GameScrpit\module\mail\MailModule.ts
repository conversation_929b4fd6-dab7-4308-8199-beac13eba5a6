import MsgEnum from "../../game/event/MsgEnum";
import data from "../../lib/data/data";
import MsgMgr from "../../lib/event/MsgMgr";
import { MailApi } from "./MailApi";
import { MailConfig } from "./MailConfig";
import { MailData } from "./MailData";
import { MailService } from "./MailService";
import { MailRoute } from "./MailRoute";
import { MailSubscriber } from "./MailSubscriber";
import { MailViewModel } from "./MailViewModel";
import { MailMessage } from "../../game/net/protocol/Mail";
import { MainTaskData } from "../mainTask/MainTaskData";
import { GameData } from "../../game/GameData";

export class MailModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): MailModule {
    if (!GameData.instance.MailModule) {
      GameData.instance.MailModule = new MailModule();
    }
    return GameData.instance.MailModule;
  }
  private _data = new MailData();
  private _api = new MailApi();
  private _service = new MailService();
  private _viewModel = new MailViewModel();
  private _route = new MailRoute();
  private _subscriber = new MailSubscriber();
  private _config = new MailConfig();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }

  public static get service() {
    return this.instance._service;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new MailData();
    this._api = new MailApi();
    this._service = new MailService();
    this._viewModel = new MailViewModel();
    this._route = new MailRoute();
    this._subscriber = new MailSubscriber();
    this._config = new MailConfig();

    // 初始化模块
    this._subscriber.register();
    this._route.init();
    MailModule.api.syncDailyMail(() => {
      MailModule.api.syncSysMail(() => {
        completedCallback && completedCallback();
      });
    });
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
