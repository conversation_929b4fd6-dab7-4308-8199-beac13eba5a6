{"skeleton": {"hash": "kvga3kQC20fvViyyFOnV2NAy4Bo=", "spine": "3.8.75", "x": -347.66, "y": -645, "width": 695.38, "height": 1290, "images": "./images/", "audio": "D:/spine导出/知己/赵飞燕"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 352.22, "rotation": -0.78, "x": -0.06, "y": -86.74, "scaleX": 0.85, "scaleY": 0.85}, {"name": "bone2", "parent": "bone", "length": 151.85, "rotation": 90.18, "x": -18.36, "y": 5.32}, {"name": "bone3", "parent": "bone2", "length": 117.04, "rotation": -2.52, "x": 151.85}, {"name": "bone4", "parent": "bone3", "length": 80.36, "rotation": 0.85, "x": 117.04}, {"name": "bone5", "parent": "bone4", "x": 94.75, "y": -1.95, "color": "abe323ff"}, {"name": "bone6", "parent": "bone4", "x": 185.76, "y": -0.92}, {"name": "bone7", "parent": "bone4", "x": 131.95, "y": -1.39}, {"name": "bone8", "parent": "bone4", "x": 298.02, "y": -3.82}, {"name": "bone9", "parent": "bone8", "length": 29.59, "rotation": 149.44, "x": -47.94, "y": 5.82}, {"name": "bone10", "parent": "bone9", "length": 29.18, "rotation": 20.13, "x": 29.59}, {"name": "bone11", "parent": "bone10", "length": 26.14, "rotation": 17.1, "x": 29.18}, {"name": "bone12", "parent": "bone8", "length": 28.75, "rotation": -154.74, "x": -46.89, "y": -10.59}, {"name": "bone13", "parent": "bone12", "length": 27.7, "rotation": -20.5, "x": 28.75}, {"name": "bone14", "parent": "bone13", "length": 30.49, "rotation": -4, "x": 27.7}, {"name": "bone15", "parent": "bone8", "length": 48.84, "rotation": 150.01, "x": -47.01, "y": 43.18}, {"name": "bone16", "parent": "bone15", "length": 45.02, "rotation": 14.63, "x": 48.84}, {"name": "bone17", "parent": "bone16", "length": 50.12, "rotation": 1.37, "x": 45.02}, {"name": "bone18", "parent": "bone8", "length": 39.28, "rotation": -150.38, "x": -55.09, "y": -46.64}, {"name": "bone19", "parent": "bone18", "length": 43.58, "rotation": -14.59, "x": 39.28}, {"name": "bone20", "parent": "bone19", "length": 38.32, "rotation": 0.55, "x": 43.58}, {"name": "bone21", "parent": "bone8", "x": -82.04, "y": 147.33}, {"name": "bone22", "parent": "bone21", "length": 27.29, "rotation": 179.74, "x": -18.11, "y": 1.29}, {"name": "bone23", "parent": "bone22", "length": 41.31, "rotation": 1.41, "x": 27.29}, {"name": "bone24", "parent": "bone4", "length": 31.62, "rotation": -179.32, "x": 98.29, "y": 77.45}, {"name": "bone25", "parent": "bone24", "length": 48.74, "rotation": 0.56, "x": 31.62}, {"name": "bone26", "parent": "bone4", "length": 31.61, "rotation": -178.53, "x": 96.36, "y": -84.75}, {"name": "bone27", "parent": "bone26", "length": 46.98, "rotation": 0.26, "x": 31.61}, {"name": "bone28", "parent": "bone3", "x": 50.02, "y": -140.09}, {"name": "bone29", "parent": "bone3", "x": 43.98, "y": 141.39}, {"name": "bone30", "parent": "bone29", "length": 236.79, "rotation": 171.73, "x": -48.72, "y": 25.05}, {"name": "bone31", "parent": "bone30", "length": 305.03, "rotation": -178.38, "x": 227.09, "y": 27.01}, {"name": "bone32", "parent": "bone31", "length": 93.82, "rotation": -59.37, "x": 305.03}, {"name": "bone33", "parent": "bone32", "length": 75.99, "rotation": -21.48, "x": 93.82}, {"name": "bone34", "parent": "bone32", "length": 46.81, "rotation": 19.8, "x": 114.37, "y": 18.98}, {"name": "bone35", "parent": "bone30", "length": 158.68, "rotation": 13.07, "x": 362.87, "y": 50.58}, {"name": "bone36", "parent": "bone35", "length": 151.16, "rotation": -15.98, "x": 158.68}, {"name": "bone37", "parent": "bone36", "length": 138.84, "rotation": -39.45, "x": 151.16}, {"name": "bone38", "parent": "bone30", "length": 192.59, "rotation": -1.77, "x": 300.54, "y": 0.91}, {"name": "bone39", "parent": "bone38", "length": 139.17, "rotation": -10.14, "x": 192.59}, {"name": "bone40", "parent": "bone39", "length": 109.94, "rotation": -36.65, "x": 139.17}, {"name": "bone41", "parent": "bone28", "length": 210.27, "rotation": -155.61, "x": -20.64, "y": -40.43}, {"name": "bone42", "parent": "bone41", "length": 306.35, "rotation": -101, "x": 208.5, "y": -17.51}, {"name": "bone43", "parent": "bone42", "length": 88.87, "rotation": 10.85, "x": 306.35}, {"name": "bone44", "parent": "bone43", "length": 43.22, "rotation": 48.47, "x": 88.87}, {"name": "bone45", "parent": "bone41", "length": 189.24, "rotation": -24.62, "x": 302.72, "y": -30.9}, {"name": "bone46", "parent": "bone45", "length": 196.43, "rotation": 24.32, "x": 189.24}, {"name": "bone47", "parent": "bone46", "length": 173.37, "rotation": 37.35, "x": 196.92, "y": 1.29}, {"name": "bone48", "parent": "bone", "x": -156.04, "y": -269.95}, {"name": "bone49", "parent": "bone48", "length": 198.21, "rotation": 37.27, "x": 31.35, "y": 26.44}, {"name": "bone50", "parent": "bone49", "length": 480.51, "rotation": -110.83, "x": 200.96, "y": -22.26}, {"name": "bone51", "parent": "bone50", "length": 132.66, "rotation": 111.83, "x": 577.4, "y": -40.39}, {"name": "bone52", "parent": "bone48", "length": 372.34, "rotation": -85.47, "x": 140.44, "y": 16.54}, {"name": "bone53", "parent": "bone4", "length": 109.45, "rotation": -169.39, "x": 217.95, "y": -85.4, "color": "cf1111ff"}, {"name": "bone54", "parent": "bone53", "length": 118.16, "rotation": -1.73, "x": 109.45, "color": "cf1111ff"}, {"name": "bone55", "parent": "bone54", "length": 124.18, "rotation": -1.11, "x": 117.53, "y": -0.5, "color": "cf1111ff"}, {"name": "bone56", "parent": "bone4", "length": 124.23, "rotation": 166.65, "x": 213.39, "y": 86.92, "color": "cf1111ff"}, {"name": "bone57", "parent": "bone56", "length": 121.22, "rotation": 3.47, "x": 124.23, "color": "cf1111ff"}, {"name": "bone58", "parent": "bone57", "length": 104.32, "rotation": 1.82, "x": 121.22, "color": "cf1111ff"}, {"name": "bone59", "parent": "bone4", "x": 65.96, "y": -6.76}], "slots": [{"name": "cut", "bone": "root", "attachment": "cut"}, {"name": "zfy01", "bone": "root", "attachment": "zfy01"}, {"name": "zfy02", "bone": "root", "attachment": "zfy02"}, {"name": "zfy03", "bone": "root", "attachment": "zfy03"}, {"name": "zfy04", "bone": "root", "attachment": "zfy04"}, {"name": "zfy05", "bone": "bone7", "attachment": "zfy05"}, {"name": "zfy06", "bone": "root", "color": "ffffffb8"}, {"name": "zfy07", "bone": "root", "attachment": "zfy07"}, {"name": "zfy08", "bone": "root", "attachment": "zfy08"}, {"name": "zfy09", "bone": "root", "attachment": "zfy09"}, {"name": "sm", "bone": "bone59", "color": "ffffff00", "attachment": "sm"}, {"name": "zfy9", "bone": "root", "attachment": "zfy09"}, {"name": "zfy031", "bone": "root", "attachment": "zfy031"}, {"name": "zfy011", "bone": "root", "attachment": "zfy011"}, {"name": "zfy012", "bone": "root", "attachment": "zfy012"}, {"name": "zfy013", "bone": "root", "attachment": "zfy013"}, {"name": "zfy014", "bone": "root", "attachment": "zfy014"}, {"name": "zfy015", "bone": "bone51", "attachment": "zfy015"}], "transform": [{"name": "eye", "order": 1, "bones": ["bone7"], "target": "bone5", "x": 37.2, "y": 0.56, "rotateMix": 0.099, "translateMix": 0.099, "scaleMix": 0.099, "shearMix": 0.099}, {"name": "face", "bones": ["bone6"], "target": "bone5", "x": 91.02, "y": 1.02, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "zui", "order": 2, "bones": ["bone59"], "target": "bone5", "x": -29.26, "y": -4.95, "rotateMix": 0.114, "translateMix": 0.114, "scaleMix": 0.114, "shearMix": 0.114}], "skins": [{"name": "default", "attachments": {"cut": {"cut": {"type": "clipping", "end": "cut", "vertexCount": 4, "vertices": [-345.77, 645.43, 346.86, 644.76, 347.73, -641.54, -347.74, -648.03], "color": "ce3a3aff"}}, "zfy9": {"zfy09": {"type": "mesh", "uvs": [1, 0.21107, 1, 0.42853, 1, 0.71704, 1, 1, 0.41745, 1, 0, 1, 0, 0.69982, 0, 0.407, 0, 0.20472, 0, 0, 0.52295, 0, 1, 0, 0.45965, 0.21107, 0.45965, 0.42638, 0.43855, 0.71919], "triangles": [14, 6, 13, 2, 14, 13, 5, 6, 14, 4, 5, 14, 14, 2, 3, 4, 14, 3, 8, 9, 10, 10, 11, 0, 12, 8, 10, 0, 12, 10, 7, 8, 12, 13, 12, 0, 7, 12, 13, 13, 0, 1, 6, 7, 13, 13, 1, 2], "vertices": [1, 26, 8.94, 11.24, 1, 2, 26, 30.25, 11.53, 0.57283, 27, -1.31, 11.54, 0.42717, 1, 27, 26.97, 11.8, 1, 1, 27, 54.7, 12.06, 1, 1, 27, 54.8, 0.41, 1, 1, 27, 54.88, -7.94, 1, 1, 27, 25.47, -8.21, 1, 2, 26, 28.42, -8.49, 0.80256, 27, -3.23, -8.48, 0.19744, 1, 26, 8.6, -8.77, 1, 1, 26, -11.46, -9.05, 1, 1, 26, -11.61, 1.41, 1, 1, 26, -11.74, 10.95, 1, 1, 26, 9.09, 0.43, 1, 2, 26, 30.19, 0.73, 0.95013, 27, -1.42, 0.73, 0.04987, 1, 27, 27.28, 0.58, 1], "hull": 12, "edges": [16, 18, 0, 22, 14, 16, 0, 2, 10, 12, 12, 14, 2, 4, 4, 6, 18, 20, 20, 22, 20, 24, 24, 26, 26, 28, 6, 8, 8, 10, 28, 8], "width": 20, "height": 98}}, "zfy01": {"zfy01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [347, -645, -346, -645, -346, 645, 347, 645], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 415, "height": 774}}, "zfy02": {"zfy02": {"type": "mesh", "uvs": [0.23104, 0.08614, 0.38035, 0.03481, 0.53916, 0, 0.68711, 0.02197, 0.81606, 0.10182, 0.88664, 0.24013, 0.91515, 0.42122, 0.95858, 0.66361, 1, 0.839, 1, 0.94736, 0.87714, 0.95306, 0.71426, 0.9659, 0.5378, 0.97873, 0.34099, 0.99014, 0.1306, 1, 0, 0.95877, 0, 0.74774, 0.06001, 0.5524, 0.1306, 0.39698, 0.16589, 0.21304, 0.15109, 0.79144, 0.18056, 0.5721, 0.22601, 0.39018, 0.26777, 0.20955, 0.78362, 0.22761, 0.81064, 0.43663, 0.83767, 0.68435, 0.86223, 0.86369, 0.34736, 0.83546, 0.538, 0.8366, 0.71084, 0.84709, 0.35721, 0.59627, 0.53831, 0.61598, 0.70441, 0.62357, 0.69893, 0.4328, 0.53858, 0.42115, 0.36513, 0.40422, 0.37309, 0.21099, 0.53886, 0.21472, 0.69291, 0.22358], "triangles": [15, 16, 20, 28, 14, 20, 13, 28, 12, 14, 15, 20, 28, 13, 14, 17, 18, 21, 21, 22, 31, 32, 31, 35, 20, 17, 21, 16, 17, 20, 31, 20, 21, 28, 31, 32, 28, 20, 31, 29, 28, 32, 37, 23, 0, 1, 37, 0, 19, 0, 23, 38, 1, 2, 37, 1, 38, 22, 19, 23, 18, 19, 22, 36, 23, 37, 22, 23, 36, 38, 36, 37, 35, 36, 38, 21, 18, 22, 31, 22, 36, 31, 36, 35, 27, 7, 8, 30, 26, 27, 27, 8, 9, 10, 27, 9, 11, 30, 27, 11, 27, 10, 12, 28, 29, 12, 29, 30, 12, 30, 11, 32, 34, 33, 7, 26, 25, 33, 25, 26, 29, 32, 33, 30, 33, 26, 29, 33, 30, 27, 26, 7, 38, 2, 3, 39, 3, 4, 38, 3, 39, 24, 39, 4, 24, 4, 5, 35, 38, 39, 34, 39, 24, 35, 39, 34, 25, 24, 5, 25, 5, 6, 34, 24, 25, 32, 35, 34, 33, 34, 25, 7, 25, 6], "vertices": [1, 56, -11.68, -18.07, 1, 2, 53, -54.15, -130.43, 0.19532, 56, -40.74, 25.43, 0.80468, 2, 53, -57.39, -76.51, 0.54208, 56, -65.61, 73.39, 0.45792, 2, 53, -43.36, -28.77, 0.82191, 56, -72.17, 122.71, 0.17809, 2, 53, -12.08, 10.04, 0.99742, 56, -59.35, 170.88, 0.00258, 1, 53, 34.71, 26.94, 1, 2, 53, 92.88, 28, 0.88184, 54, -17.41, 27.49, 0.11816, 1, 54, 60.58, 33.01, 1, 2, 54, 153.6, 40.97, 0.01569, 55, 35.26, 42.16, 0.98431, 1, 55, 152.23, 47.53, 1, 3, 55, 150.49, -3.8, 0.99467, 57, 200.46, 294.17, 0.00023, 58, 88.54, 291.51, 0.0051, 3, 55, 147.59, -69.1, 0.83392, 57, 217.51, 231.06, 0.0162, 58, 103.57, 227.89, 0.14988, 4, 54, 261.03, -131.29, 0.00291, 55, 146.01, -127.98, 0.56782, 57, 233.85, 174.48, 0.03258, 58, 118.11, 170.81, 0.39669, 4, 54, 257.07, -196.81, 0.00182, 55, 143.32, -193.57, 0.26704, 57, 251.18, 111.17, 0.01765, 58, 133.43, 106.99, 0.71349, 2, 55, 139.72, -263.61, 0.05435, 58, 149.07, 38.62, 0.94565, 1, 58, 140.42, -31.72, 1, 2, 57, 119.17, -44.7, 0.56076, 58, -3.46, -44.61, 0.43924, 1, 57, 42.91, -37.15, 1, 2, 56, 92.22, -23.75, 0.99144, 57, -33.38, -21.77, 0.00856, 1, 56, 32.9, -28.14, 1, 4, 54, 106.96, -242.37, 0.00852, 55, -5.88, -242.03, 0.01442, 57, 123.7, 19.74, 0.40745, 58, 3.11, 19.65, 0.5696, 5, 53, 135.91, -231.81, 0.00056, 54, 33.43, -230.91, 0.00431, 55, -79.62, -232, 0.00139, 56, 174.11, 9.98, 0.00021, 57, 50.4, 6.94, 0.99353, 4, 53, 49.86, -197.63, 0.0045, 54, -53.61, -199.33, 0.00235, 56, 81.59, 6.27, 0.99126, 57, -42.18, 8.84, 0.00188, 3, 53, -4.78, -175.56, 0.00525, 54, -108.89, -178.92, 0.00027, 56, 22.7, 4.24, 0.99449, 3, 53, 25.8, -6.43, 0.99067, 56, -18.04, 171.22, 0.00922, 57, -131.64, 179.52, 0.00011, 4, 53, 92.67, -7.14, 0.9892, 54, -16.57, -7.64, 0.00283, 56, 43.34, 197.72, 0.00615, 57, -68.76, 202.26, 0.00182, 5, 54, 72.23, -7.49, 0.99189, 55, -45.15, -7.87, 0.00172, 56, 125.48, 231.47, 0.00136, 57, 15.27, 230.97, 0.00435, 58, -98.57, 234.21, 0.00069, 5, 54, 154.85, -10.22, 0.00488, 55, 37.5, -9, 0.98725, 56, 202.99, 260.2, 1e-05, 57, 94.37, 254.95, 0.00367, 58, -18.75, 255.68, 0.00419, 6, 53, 230.2, -183.22, 0.0004, 54, 126.21, -179.5, 0.08937, 55, 12.15, -178.8, 0.165, 56, 240.54, 92.67, 0.00304, 57, 121.71, 85.46, 0.36319, 58, 3.2, 85.4, 0.37899, 6, 53, 239.77, -120.46, 0.00061, 54, 133.88, -116.48, 0.19866, 55, 18.6, -115.64, 0.38293, 56, 223.79, 153.91, 0.00878, 57, 108.7, 147.6, 0.21301, 58, -7.83, 147.92, 0.19601, 5, 54, 143.81, -59.69, 0.18407, 55, 27.43, -58.67, 0.67231, 56, 211.49, 210.23, 0.00268, 57, 99.84, 204.56, 0.07084, 58, -14.88, 205.14, 0.0701, 6, 53, 150.58, -170.32, 0.02876, 54, 46.23, -169.01, 0.14597, 55, -68.01, -169.86, 0.0442, 56, 162.54, 72.12, 0.1162, 57, 42.61, 69.67, 0.63696, 58, -76.36, 72.13, 0.02791, 6, 53, 157.31, -110.36, 0.06931, 54, 51.16, -108.87, 0.4113, 55, -64.25, -109.64, 0.07948, 56, 144.34, 129.65, 0.12213, 57, 27.93, 128.19, 0.28708, 58, -89.18, 131.09, 0.03071, 6, 53, 166.55, -54.42, 0.02811, 54, 58.71, -52.67, 0.7802, 55, -57.79, -53.31, 0.05311, 56, 130.06, 184.52, 0.03973, 57, 17, 183.83, 0.08721, 58, -98.33, 187.05, 0.01165, 4, 53, 86.07, -43.77, 0.70167, 54, -22.06, -44.45, 0.17114, 56, 52.19, 161.57, 0.10031, 57, -62.12, 165.63, 0.02688, 6, 53, 74.67, -96.07, 0.40134, 54, -31.87, -97.07, 0.14981, 55, -147.5, -99.45, 0.00133, 56, 63.02, 109.15, 0.37166, 57, -54.49, 112.66, 0.07585, 58, -172.04, 118.18, 0, 5, 53, 60.98, -152.43, 0.12331, 54, -43.86, -153.83, 0.05489, 55, -158.38, -156.43, 0.00062, 56, 73.4, 52.08, 0.75549, 57, -47.58, 55.06, 0.06569, 4, 53, 0.76, -140.93, 0.14396, 54, -104.4, -144.14, 0.00565, 56, 13.7, 38.14, 0.85005, 57, -108.02, 44.76, 0.00034, 4, 53, 9.94, -86.48, 0.52838, 54, -96.87, -89.44, 0.01041, 56, -0.03, 91.62, 0.45746, 57, -118.48, 98.98, 0.00375, 4, 53, 20.16, -36.13, 0.87868, 54, -88.17, -38.81, 0.00091, 56, -11.14, 141.78, 0.11884, 57, -126.53, 149.72, 0.00158], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 28, 40, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52, 52, 54, 56, 26, 58, 24, 60, 22, 62, 56, 64, 58, 66, 60, 68, 66, 70, 64, 72, 62, 2, 74, 74, 72, 4, 76, 76, 70, 6, 78, 78, 68], "width": 199, "height": 190}}, "zfy03": {"zfy03": {"type": "mesh", "uvs": [0.50998, 0, 0.46578, 0.00487, 0.43611, 0.0377, 0.43672, 0.09067, 0.43127, 0.13847, 0.40523, 0.17417, 0.35377, 0.20124, 0.28777, 0.20181, 0.23126, 0.20011, 0.19436, 0.20357, 0.18241, 0.2179, 0.17929, 0.23817, 0.15954, 0.24262, 0.15123, 0.2698, 0.1294, 0.3044, 0.1242, 0.33554, 0.05129, 0.3856, 0.0189, 0.46837, 0, 0.53767, 0.0017, 0.62044, 0.01688, 0.67434, 0.08468, 0.7215, 0.1707, 0.73209, 0.22333, 0.71284, 0.26468, 0.73384, 0.25156, 0.79347, 0.24354, 0.86211, 0.24719, 0.93837, 0.35139, 0.95924, 0.50444, 0.97779, 0.61779, 0.97657, 0.72085, 0.96432, 0.76207, 0.88101, 0.7801, 0.8124, 0.79298, 0.73522, 0.74403, 0.69602, 0.80587, 0.69479, 0.90234, 0.65464, 0.97521, 0.61652, 1, 0.55935, 0.98331, 0.51058, 0.94852, 0.47144, 0.93809, 0.40968, 0.90852, 0.35454, 0.87681, 0.29898, 0.85929, 0.25393, 0.83883, 0.22069, 0.7945, 0.19232, 0.74591, 0.18826, 0.68879, 0.19637, 0.64105, 0.18907, 0.61292, 0.16799, 0.5882, 0.13151, 0.58223, 0.08286, 0.57797, 0.03503, 0.56092, 0.00584, 0.50666, 0.04675, 0.50316, 0.08676, 0.50316, 0.13344, 0.50141, 0.18262, 0.49878, 0.22764, 0.48563, 0.33851, 0.48125, 0.44104, 0.48125, 0.53274, 0.48475, 0.62527, 0.483, 0.71946, 0.48934, 0.80944, 0.49035, 0.90105, 0.62782, 0.90391, 0.64387, 0.79703, 0.62681, 0.70064, 0.6238, 0.62239, 0.67397, 0.526, 0.72916, 0.44298, 0.76629, 0.35518, 0.77532, 0.27406, 0.63484, 0.25784, 0.61277, 0.34086, 0.5937, 0.44966, 0.5656, 0.54222, 0.34284, 0.24161, 0.37495, 0.31796, 0.38198, 0.42198, 0.39301, 0.53554, 0.22243, 0.28169, 0.2144, 0.36377, 0.22544, 0.47542, 0.26156, 0.56799, 0.34284, 0.64243, 0.35187, 0.72927, 0.33582, 0.81516, 0.34385, 0.87815, 0.82951, 0.37713, 0.86864, 0.47829, 0.85961, 0.56227, 0.78435, 0.62048, 0.7111, 0.60807, 0.13012, 0.43725, 0.10302, 0.51455, 0.11105, 0.61475, 0.1853, 0.64529, 0.25655, 0.64529], "triangles": [70, 96, 35, 65, 88, 64, 89, 88, 65, 64, 70, 65, 70, 64, 71, 69, 70, 35, 66, 65, 70, 89, 65, 66, 33, 35, 34, 90, 24, 89, 66, 70, 69, 69, 35, 33, 90, 89, 66, 25, 24, 90, 26, 25, 90, 91, 90, 66, 26, 90, 91, 32, 69, 33, 68, 67, 66, 91, 66, 67, 69, 68, 66, 68, 69, 32, 27, 26, 91, 28, 91, 67, 27, 91, 28, 31, 68, 32, 68, 29, 67, 30, 68, 31, 30, 29, 68, 28, 67, 29, 64, 79, 71, 70, 71, 96, 64, 88, 83, 24, 88, 89, 80, 7, 6, 8, 10, 9, 8, 11, 10, 84, 11, 8, 13, 12, 11, 11, 84, 13, 84, 8, 7, 84, 7, 80, 84, 80, 81, 24, 101, 88, 84, 14, 13, 85, 14, 84, 15, 14, 85, 97, 15, 85, 16, 15, 97, 17, 16, 97, 85, 81, 82, 81, 85, 84, 86, 85, 82, 97, 85, 86, 98, 17, 97, 98, 97, 86, 18, 17, 98, 87, 86, 83, 87, 99, 98, 87, 98, 86, 18, 98, 99, 19, 18, 99, 100, 99, 87, 101, 100, 87, 88, 101, 87, 20, 19, 99, 23, 100, 101, 21, 20, 99, 21, 99, 100, 22, 21, 100, 23, 22, 100, 23, 101, 24, 75, 47, 46, 75, 46, 45, 74, 76, 75, 44, 92, 75, 44, 75, 45, 92, 44, 43, 74, 75, 92, 73, 77, 74, 42, 93, 92, 42, 92, 43, 93, 42, 41, 93, 73, 92, 72, 95, 96, 73, 74, 92, 94, 73, 93, 94, 72, 73, 41, 94, 93, 39, 94, 40, 94, 95, 72, 41, 40, 94, 39, 38, 94, 37, 94, 38, 95, 94, 37, 36, 95, 37, 35, 96, 95, 35, 95, 36, 76, 51, 50, 76, 50, 49, 75, 48, 47, 75, 49, 48, 75, 76, 49, 77, 76, 74, 56, 1, 0, 56, 0, 55, 56, 55, 54, 2, 1, 56, 56, 54, 53, 57, 2, 56, 57, 56, 53, 3, 2, 57, 58, 57, 53, 3, 57, 58, 58, 53, 52, 4, 3, 58, 51, 58, 52, 59, 4, 58, 59, 5, 4, 60, 5, 59, 5, 80, 6, 60, 80, 5, 60, 81, 80, 61, 81, 60, 51, 59, 58, 76, 59, 51, 76, 77, 59, 77, 60, 59, 77, 61, 60, 82, 81, 61, 62, 82, 61, 78, 77, 73, 78, 61, 77, 62, 61, 78, 72, 78, 73, 62, 83, 82, 79, 63, 62, 63, 83, 62, 86, 82, 83, 78, 79, 62, 79, 78, 72, 71, 79, 72, 71, 72, 96, 64, 63, 79, 88, 87, 83, 64, 83, 63], "vertices": [1, 4, 89.4, -6.18, 1, 1, 4, 85.4, 19.45, 1, 1, 4, 64.61, 35.94, 1, 3, 3, 148.7, 34.77, 0.03145, 4, 32.17, 34.3, 0.96727, 29, 104.72, -106.62, 0.00128, 3, 3, 119.27, 36.35, 0.50012, 4, 2.77, 36.31, 0.47127, 29, 75.29, -105.04, 0.02861, 3, 3, 96.59, 50.32, 0.79155, 4, -19.7, 50.61, 0.06018, 29, 52.62, -91.08, 0.14827, 4, 2, 233.66, 75.86, 0.00615, 3, 78.4, 79.38, 0.5338, 30, -94.81, 74.19, 0.00056, 29, 34.42, -62.02, 0.45949, 3, 2, 232.9, 114.33, 0.00196, 3, 75.95, 117.78, 0.15652, 29, 31.98, -23.62, 0.84152, 3, 3, 75.21, 150.73, 0.01099, 30, -81.38, 4.04, 0.00361, 29, 31.23, 9.33, 0.9854, 2, 30, -75.05, -16.63, 0.0558, 29, 27.94, 30.7, 0.9442, 2, 30, -65.06, -21.72, 0.11827, 29, 18.79, 37.18, 0.88173, 2, 30, -52.53, -21.05, 0.26039, 29, 6.29, 38.32, 0.73961, 2, 30, -47.58, -31.8, 0.41255, 29, 2.94, 49.67, 0.58745, 2, 30, -30.29, -33.26, 0.62482, 29, -13.97, 53.61, 0.37518, 2, 30, -6.98, -41.55, 0.9113, 29, -35.84, 65.16, 0.0887, 2, 30, 12.33, -40.75, 0.99248, 29, -55.06, 67.15, 0.00752, 1, 30, 50.8, -76.36, 1, 1, 30, 104.27, -84.85, 1, 1, 30, 148.09, -87.27, 1, 1, 30, 197.64, -76.28, 1, 1, 30, 228.28, -61.08, 1, 1, 30, 248.81, -16.62, 1, 2, 2, -92.85, 179.17, 0.05827, 30, 245.27, 33.83, 0.94173, 2, 2, -80.73, 148.62, 0.25591, 30, 227.65, 61.57, 0.74409, 2, 2, -93.36, 124.37, 0.55898, 30, 235.51, 87.75, 0.44102, 1, 1, -149.6, -125.07, 1, 1, 1, -153.7, -167.21, 1, 1, 1, -150.95, -213.92, 1, 1, 1, -90.03, -225.89, 1, 1, 1, -0.66, -236.06, 1, 1, 1, 65.41, -234.41, 1, 1, 1, 125.39, -226.09, 1, 1, 1, 148.73, -174.7, 1, 1, 1, 158.67, -132.5, 1, 1, 1, 165.54, -85.09, 1, 1, 1, 136.68, -61.45, 1, 2, 2, -66.11, -190.87, 0.2826, 41, 225.59, -80.63, 0.7174, 2, 2, -40.91, -246.85, 0.03244, 41, 223.07, -19.29, 0.96756, 1, 41, 216.71, 28.77, 1, 1, 41, 189.3, 54.96, 1, 1, 41, 157.91, 56.74, 1, 1, 41, 128.19, 46.55, 1, 1, 41, 90.7, 54.62, 1, 1, 41, 52.95, 50.82, 1, 2, 28, -14.88, -88.27, 0.0033, 41, 14.5, 45.95, 0.9967, 2, 28, 12.14, -76.57, 0.11165, 41, -14.93, 46.45, 0.88835, 2, 28, 31.84, -63.56, 0.30899, 41, -38.25, 42.73, 0.69101, 3, 3, 97.82, -176.89, 8e-05, 28, 47.8, -36.81, 0.62608, 41, -63.83, 24.96, 0.37384, 4, 3, 98.76, -148.47, 0.01994, 4, -20.47, -148.18, 0.00014, 28, 48.75, -8.38, 0.85731, 41, -76.43, -0.53, 0.12261, 5, 2, 238.69, -119.42, 0.00065, 3, 91.99, -115.49, 0.15859, 4, -26.75, -115.11, 0.00735, 28, 41.97, 24.6, 0.83216, 41, -83.88, -33.37, 0.00126, 4, 2, 242.87, -91.54, 0.00219, 3, 94.94, -87.46, 0.38886, 4, -23.38, -87.12, 0.05079, 28, 44.93, 52.63, 0.55816, 3, 3, 106.96, -70.38, 0.50396, 4, -11.12, -70.22, 0.16905, 28, 56.94, 69.71, 0.32699, 3, 3, 128.5, -54.77, 0.3493, 4, 10.66, -54.93, 0.53338, 28, 78.49, 85.32, 0.11732, 3, 3, 158.09, -49.68, 0.0569, 4, 40.31, -50.28, 0.91972, 28, 108.07, 90.41, 0.02338, 2, 4, 69.52, -46.64, 0.99787, 28, 137.22, 94.49, 0.00213, 2, 4, 87, -35.99, 0.99996, 28, 154.54, 105.38, 4e-05, 2, 4, 60.69, -5.38, 0.99985, 28, 127.79, 135.6, 0.00015, 3, 3, 153.2, -3.78, 0.0001, 4, 36.1, -4.31, 0.99874, 28, 103.18, 136.31, 0.00116, 3, 3, 124.63, -5.33, 0.09116, 4, 7.51, -5.44, 0.90429, 28, 74.61, 134.76, 0.00455, 3, 3, 94.47, -5.95, 0.97062, 4, -22.66, -5.62, 0.01777, 28, 44.45, 134.14, 0.01161, 2, 3, 66.83, -5.92, 0.98486, 28, 16.81, 134.17, 0.01514, 4, 2, 150.32, -1.89, 0.5859, 3, -1.45, -1.96, 0.41163, 28, -51.47, 138.13, 0.00225, 41, -45.67, -175.35, 0.00022, 1, 2, 87.44, 0, 1, 3, 2, 31.24, -0.58, 0.99976, 28, -170.49, 134.21, 4e-05, 41, 64.35, -220.94, 0.0002, 1, 1, -15.06, -20.13, 1, 1, 1, -15.3, -77.88, 1, 1, 1, -10.85, -132.98, 1, 1, 1, -9.51, -189.13, 1, 1, 1, 70.65, -189.8, 1, 1, 1, 79.13, -124.16, 1, 1, 1, 68.38, -65.21, 1, 1, 1, 65.98, -17.27, 1, 4, 2, 36.54, -112.9, 0.51989, 3, -110.24, -117.85, 4e-05, 28, -160.26, 22.24, 0.0402, 41, 101.27, -114.74, 0.43986, 4, 2, 87.77, -144.54, 0.23501, 3, -57.68, -147.21, 0.00392, 28, -107.69, -7.12, 0.10089, 41, 65.52, -66.29, 0.66019, 4, 2, 141.82, -165.62, 0.04417, 3, -2.76, -165.9, 0.00247, 28, -52.78, -25.81, 0.14176, 41, 23.23, -26.59, 0.8116, 2, 28, -2.84, -28.36, 0.40336, 41, -21.2, -3.64, 0.59664, 5, 2, 200.68, -88.36, 0.04644, 3, 52.66, -86.13, 0.33934, 4, -65.65, -85.17, 0.00621, 28, 2.64, 53.95, 0.60626, 41, -60.18, -76.35, 0.00176, 4, 2, 149.65, -76.02, 0.3163, 3, 1.14, -76.05, 0.24102, 28, -48.88, 64.04, 0.35958, 41, -17.43, -106.81, 0.08311, 4, 2, 82.85, -65.61, 0.72219, 3, -66.06, -68.57, 0.01489, 28, -116.07, 71.51, 0.09577, 41, 40.68, -141.37, 0.16715, 3, 2, 25.94, -49.82, 0.86425, 28, -173.63, 84.79, 0.01496, 41, 87.61, -177.22, 0.12079, 4, 2, 208.84, 81.97, 0.02717, 3, 53.34, 84.39, 0.41841, 30, -69.29, 72.84, 0.02019, 29, 9.36, -57, 0.53422, 4, 2, 162.24, 62.76, 0.25157, 3, 7.62, 63.16, 0.38287, 30, -27.1, 100.43, 0.11919, 29, -36.35, -78.24, 0.24637, 4, 2, 98.52, 58, 0.69897, 3, -55.82, 55.6, 0.02788, 30, 34.6, 117.04, 0.22581, 29, -99.8, -85.79, 0.04734, 3, 2, 28.98, 50.83, 0.80035, 30, 101.57, 137.09, 0.19887, 29, -168.96, -96, 0.00078, 4, 2, 183.54, 151.91, 0.00483, 3, 24.99, 153.15, 0.00158, 30, -31.34, 8.87, 0.38139, 29, -18.99, 11.76, 0.6122, 4, 2, 133.18, 156.06, 0.02414, 3, -25.5, 155.09, 0.00488, 30, 18.91, 14.22, 0.94346, 29, -69.48, 13.7, 0.02752, 4, 2, 64.81, 148.91, 0.10785, 3, -93.5, 144.95, 9e-05, 30, 84.73, 34.04, 0.89053, 29, -137.47, 3.55, 0.00154, 2, 2, 8.29, 127.26, 0.28382, 30, 136.2, 65.89, 0.71618, 1, 1, -97.64, -31.77, 1, 1, 1, -91.66, -84.93, 1, 1, 1, -100.3, -137.7, 1, 1, 1, -95.1, -176.24, 1, 1, 41, 49.14, 2.87, 1, 1, 41, 115.2, 1.63, 1, 3, 2, 15.45, -221.35, 0.0362, 28, -176.58, -87.03, 0.00018, 41, 161.26, -21.96, 0.96362, 3, 2, -20.69, -177.85, 0.27167, 28, -214.59, -45.16, 0.00133, 41, 178.59, -75.79, 0.727, 3, 2, -13.54, -135.07, 0.4851, 28, -209.32, -2.11, 0.00631, 41, 156.01, -112.83, 0.50859, 1, 30, 72.77, -25.06, 1, 1, 30, 122.34, -31.19, 1, 1, 30, 181.63, -14.47, 1, 2, 2, -39.56, 171.22, 0.08305, 30, 191.43, 31.67, 0.91695, 2, 2, -39.13, 129.69, 0.3222, 30, 183.23, 72.38, 0.6778], "hull": 56, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 0, 110, 0, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 60, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 152, 154, 154, 156, 156, 158, 160, 162, 162, 164, 164, 166, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 184, 186, 186, 188, 188, 190, 190, 192, 194, 196, 196, 198, 198, 200, 200, 202], "width": 583, "height": 613}}, "zfy04": {"zfy04": {"type": "mesh", "uvs": [0, 0.24006, 0.04043, 0.15081, 0.1479, 0.08003, 0.27799, 0.0154, 0.42882, 0, 0.63055, 0, 0.66638, 0.05336, 0.66638, 0.11875, 0.66638, 0.12517, 0.76819, 0.1426, 0.85491, 0.2062, 0.93599, 0.2975, 0.94918, 0.39393, 0.92656, 0.49138, 0.93643, 0.58165, 0.9688, 0.66678, 1, 0.7617, 1, 0.86737, 0.9742, 1, 0.7218, 1, 0.44694, 1, 0.41222, 0.87704, 0.40933, 0.75425, 0.42379, 0.64563, 0.44983, 0.55433, 0.48166, 0.48034, 0.48744, 0.39219, 0.47729, 0.38658, 0.37033, 0.39283, 0.23078, 0.39151, 0.12521, 0.37765, 0.0427, 0.33473, 0, 0.29116, 0.52624, 0.17165, 0.45928, 0.25492, 0.46502, 0.3538, 0.37602, 0.3684, 0.22938, 0.26524, 0.21205, 0.15367, 0.34561, 0.07116, 0.51814, 0.04769, 0.0924, 0.23921, 0.09935, 0.30658, 0.63578, 0.24367, 0.66183, 0.37233, 0.68588, 0.4999, 0.67786, 0.64927, 0.69189, 0.78556, 0.70792, 0.89678, 0.4504, 0.35614, 0.43852, 0.25378, 0.51179, 0.16831], "triangles": [17, 18, 48, 20, 48, 19, 18, 19, 48, 48, 20, 21, 48, 21, 47, 46, 22, 23, 47, 22, 46, 48, 47, 17, 47, 21, 22, 47, 16, 17, 47, 15, 16, 47, 46, 15, 46, 14, 15, 23, 24, 46, 46, 24, 45, 46, 45, 14, 45, 24, 25, 45, 13, 14, 45, 25, 44, 25, 26, 44, 13, 45, 12, 12, 45, 44, 44, 11, 12, 44, 26, 35, 49, 35, 27, 26, 27, 35, 35, 43, 44, 11, 43, 10, 11, 44, 43, 35, 34, 43, 34, 33, 43, 43, 9, 10, 43, 8, 9, 43, 33, 8, 35, 50, 34, 31, 42, 30, 29, 30, 42, 31, 32, 42, 32, 41, 42, 42, 41, 37, 32, 0, 41, 0, 1, 41, 36, 49, 27, 35, 49, 50, 28, 36, 27, 28, 29, 36, 36, 29, 37, 37, 29, 42, 36, 37, 50, 50, 37, 38, 34, 51, 33, 37, 41, 38, 50, 38, 51, 41, 1, 38, 8, 33, 7, 38, 39, 51, 7, 33, 51, 51, 39, 40, 1, 2, 38, 38, 2, 39, 51, 40, 7, 40, 6, 7, 2, 3, 39, 39, 4, 40, 39, 3, 4, 40, 5, 6, 40, 4, 5, 49, 36, 50, 34, 50, 51], "vertices": [2, 49, -45.24, 27.91, 0.23331, 48, -21.55, 21.26, 0.76669, 2, 49, -12.33, 55.19, 0.81728, 48, -11.88, 62.9, 0.18272, 2, 49, 29.1, 65.48, 0.99118, 48, 14.86, 96.18, 0.00882, 1, 49, 73.43, 70.06, 1, 1, 49, 108.37, 53.13, 1, 1, 49, 149.4, 22.77, 1, 2, 49, 141.93, -2.56, 0.99923, 52, -93.09, 12.81, 0.00077, 2, 49, 123.85, -27.01, 0.70044, 52, -62.75, 10.82, 0.29956, 2, 49, 122.07, -29.41, 0.6711, 52, -59.78, 10.62, 0.3289, 2, 49, 137.96, -51.24, 0.26036, 52, -50, 35.79, 0.73964, 2, 49, 138.01, -88.07, 0.03981, 52, -19.05, 55.75, 0.96019, 1, 52, 24.66, 73.44, 1, 1, 52, 69.62, 73.83, 1, 1, 52, 114.46, 65.15, 1, 1, 52, 156.51, 64.9, 1, 1, 52, 196.55, 70.47, 1, 1, 52, 241.1, 75.46, 1, 1, 52, 290.14, 72.24, 1, 1, 52, 351.25, 61.69, 1, 1, 52, 347.07, -2.03, 1, 1, 52, 342.51, -71.42, 1, 1, 52, 284.88, -76.44, 1, 1, 52, 227.86, -73.43, 1, 1, 52, 177.7, -66.47, 1, 1, 52, 135.77, -57.12, 1, 3, 49, -13.72, -134.39, 0.00175, 48, 101.81, -88.81, 0.00137, 52, 101.97, -46.83, 0.99689, 3, 49, 11.83, -102.3, 0.09717, 48, 102.72, -47.8, 0.04893, 52, 61.16, -42.68, 0.8539, 2, 48, 100.11, -45.23, 0.08479, 52, 58.39, -45.08, 0.91521, 2, 49, -12.16, -84.92, 0.93339, 48, 73.1, -48.5, 0.06661, 3, 49, -40.18, -63.43, 0.69947, 48, 37.79, -48.37, 0.28453, 52, 56.59, -107.46, 0.016, 2, 49, -57.82, -42.36, 0.11248, 48, 10.99, -42.28, 0.88752, 3, 49, -62.73, -13.9, 0.0041, 48, -10.15, -22.61, 0.98543, 52, 27.13, -153.21, 0.01046, 2, 49, -59.37, 8.81, 0.00488, 48, -21.23, -2.5, 0.99512, 2, 49, 80.72, -25.7, 0.74331, 52, -40.53, -26.17, 0.25669, 3, 49, 44.07, -46.75, 0.07626, 48, 94.73, 15.93, 0.03877, 52, -3, -45.61, 0.88497, 3, 49, 17.89, -84.58, 0.008, 48, 96.81, -30.03, 0.11668, 52, 42.97, -47.17, 0.87532, 3, 49, -4.25, -76.64, 0.934, 48, 74.38, -37.12, 0.05, 52, 48.27, -70.09, 0.016, 2, 49, -5.55, -16.02, 0.79904, 48, 36.64, 10.34, 0.20096, 2, 49, 21.78, 28.3, 0.98956, 48, 31.55, 62.16, 0.01044, 1, 49, 71.77, 39.05, 1, 1, 49, 113.35, 21.86, 1, 2, 49, -26.21, 14.33, 0.42428, 48, 1.82, 21.97, 0.57572, 3, 49, -43.43, -11.9, 0.03819, 48, 4, -9.33, 0.9507, 52, 15, -138.05, 0.01111, 2, 49, 83.08, -69.1, 0.04076, 52, -5.3, -0.71, 0.95924, 1, 52, 54.83, 1.95, 1, 1, 52, 114.42, 4.13, 1, 1, 52, 183.6, -2.44, 1, 1, 52, 247.07, -3.05, 1, 1, 52, 298.94, -2.39, 1, 3, 49, 14.27, -83.25, 0.91658, 48, 93.12, -31.17, 0.01029, 52, 43.82, -50.94, 0.07313, 1, 49, 40.16, -43.2, 1, 3, 49, 78.7, -22.27, 0.928, 48, 107.47, 56.38, 9e-05, 52, -42.32, -29.72, 0.07191], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 16, 66, 66, 68, 68, 70, 70, 52, 54, 72, 72, 74, 74, 76, 76, 78, 78, 80, 82, 84, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 54, 98, 98, 100, 100, 102, 12, 14, 14, 16, 102, 14], "width": 253, "height": 465}}, "zfy05": {"zfy05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-13.18, -89.38, -19.99, 82.49, 46.96, 85.14, 53.77, -86.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 172, "height": 67}}, "zfy07": {"zfy07": {"type": "mesh", "uvs": [0.04366, 0.47775, 0.04047, 0.33583, 0.06703, 0.18123, 0.22848, 0.05754, 0.40089, 0, 0.55499, 0, 0.71342, 0.01767, 0.8721, 0.08861, 1, 0.24722, 1, 0.39419, 0.97072, 0.51995, 1, 0.52317, 1, 0.59211, 0.96665, 0.6699, 0.89465, 0.74485, 0.84728, 0.75192, 0.83596, 0.76905, 0.79824, 0.82612, 0.69575, 0.90808, 0.57276, 0.97475, 0.48784, 1, 0.39268, 0.97584, 0.24845, 0.88315, 0.17274, 0.78721, 0.13752, 0.72281, 0.08998, 0.70835, 0.02131, 0.6203, 0, 0.54801, 0, 0.48755, 0.48945, 0.80853, 0.48366, 0.91935, 0.7284, 0.60933, 0.28388, 0.59692, 0.09489, 0.59899, 0.89474, 0.63099, 0.42071, 0.63831, 0.50227, 0.65034, 0.58974, 0.64197, 0.89229, 0.57705, 0.10347, 0.54779, 0.55675, 0.78549, 0.42238, 0.77464, 0.49393, 0.75329, 0.56254, 0.76474, 0.42549, 0.75295, 0.49174, 0.78021, 0.49878, 0.69344, 0.5464, 0.72426, 0.44833, 0.71787, 0.72056, 0.68734, 0.7269, 0.54687, 0.27487, 0.66972, 0.29635, 0.5333, 0.298, 0.5249, 0.7267, 0.54223, 0.27672, 0.6548, 0.7221, 0.66698, 0.66665, 0.55812, 0.61637, 0.585, 0.63336, 0.65854, 0.685, 0.66614, 0.79302, 0.65955, 0.8433, 0.63774, 0.87524, 0.60833, 0.78025, 0.54842, 0.84117, 0.55692, 0.77832, 0.53669, 0.84468, 0.54076, 0.91215, 0.56259, 0.91059, 0.58419, 0.88786, 0.61845, 0.8415, 0.67036, 0.78968, 0.68224, 0.66604, 0.55033, 0.61528, 0.56247, 0.57233, 0.5819, 0.53968, 0.64676, 0.61463, 0.67856, 0.68361, 0.68487, 0.34752, 0.54722, 0.39653, 0.5831, 0.34186, 0.65487, 0.18351, 0.63728, 0.12224, 0.58873, 0.18822, 0.52822, 0.25797, 0.52893, 0.23064, 0.51697, 0.16748, 0.51697, 0.09062, 0.53275, 0.10344, 0.59558, 0.17463, 0.6493, 0.35279, 0.67034, 0.44833, 0.64238, 0.42277, 0.57398, 0.35551, 0.53848, 0.50775, 0.58271, 0.37419, 0.84859, 0.48668, 0.86153, 0.60297, 0.85842, 0.48486, 0.89645, 0.56594, 0.89405, 0.40628, 0.88299, 0.38324, 0.90449, 0.57993, 0.91739, 0.38983, 0.93214, 0.48364, 0.96162, 0.57828, 0.94074, 0.3442, 0.74085, 0.32111, 0.8339, 0.31834, 0.87388, 0.64986, 0.88767, 0.62493, 0.75739, 0.65448, 0.85114, 0.26732, 0.73067, 0.25832, 0.80338, 0.70525, 0.82356, 0.71333, 0.75169, 0.7893, 0.75601, 0.74959, 0.82632, 0.18998, 0.72431, 0.21676, 0.79668, 0.07151, 0.61456, 0.92591, 0.64514, 0.30952, 0.46614, 0.40541, 0.48673, 0.51438, 0.50095, 0.61918, 0.49032, 0.7247, 0.46333, 0.85817, 0.45446, 0.14748, 0.44635, 0.18025, 0.31522, 0.3366, 0.32798, 0.52668, 0.34925, 0.72162, 0.34164, 0.87246, 0.34653, 0.21565, 0.13712, 0.37577, 0.12816, 0.54463, 0.1278, 0.71683, 0.15264, 0.87377, 0.19485, 0.36968, 0.62287, 0.31077, 0.60505, 0.24793, 0.58713, 0.17306, 0.56674, 0.62693, 0.63321, 0.67553, 0.62177, 0.77314, 0.60052, 0.83288, 0.58875], "triangles": [137, 4, 5, 137, 5, 6, 136, 3, 4, 136, 4, 137, 135, 3, 136, 138, 6, 7, 137, 6, 138, 135, 2, 3, 8, 139, 7, 138, 7, 139, 130, 2, 135, 131, 135, 136, 130, 135, 131, 1, 2, 130, 133, 138, 139, 134, 133, 139, 134, 139, 8, 132, 136, 137, 132, 137, 138, 132, 138, 133, 131, 136, 132, 134, 8, 9, 129, 1, 130, 128, 133, 134, 128, 134, 9, 127, 133, 128, 126, 132, 133, 123, 130, 131, 124, 123, 131, 129, 130, 123, 0, 1, 129, 132, 124, 131, 132, 125, 124, 133, 127, 126, 126, 125, 132, 86, 129, 123, 87, 129, 86, 88, 0, 129, 10, 128, 9, 53, 86, 123, 94, 53, 123, 84, 87, 86, 85, 86, 53, 84, 86, 85, 87, 88, 129, 52, 85, 53, 66, 127, 128, 67, 66, 128, 124, 94, 123, 79, 52, 53, 10, 67, 128, 54, 127, 66, 73, 126, 127, 50, 54, 66, 94, 79, 53, 39, 88, 87, 39, 87, 84, 27, 28, 0, 33, 27, 0, 64, 66, 67, 50, 66, 64, 54, 73, 127, 73, 54, 50, 65, 64, 67, 57, 73, 50, 74, 125, 126, 74, 126, 73, 74, 73, 57, 68, 67, 10, 38, 65, 67, 143, 39, 84, 93, 124, 125, 94, 124, 93, 80, 79, 94, 68, 38, 67, 75, 125, 74, 95, 93, 125, 75, 95, 125, 93, 80, 94, 69, 38, 68, 58, 74, 57, 75, 74, 58, 142, 84, 85, 142, 85, 52, 143, 84, 142, 83, 39, 143, 147, 64, 65, 147, 65, 38, 10, 11, 12, 69, 68, 10, 10, 34, 69, 33, 0, 88, 89, 39, 83, 39, 89, 88, 32, 142, 52, 32, 52, 79, 141, 32, 79, 89, 33, 88, 146, 50, 64, 146, 64, 147, 80, 141, 79, 63, 147, 38, 63, 38, 69, 31, 50, 146, 57, 50, 31, 121, 27, 33, 70, 63, 69, 26, 27, 121, 145, 57, 31, 58, 57, 145, 140, 141, 80, 34, 70, 69, 12, 34, 10, 144, 58, 145, 82, 143, 142, 83, 143, 82, 62, 147, 63, 62, 63, 70, 71, 62, 70, 146, 147, 62, 35, 80, 93, 92, 35, 93, 140, 80, 35, 37, 75, 58, 37, 58, 144, 95, 92, 93, 122, 34, 12, 76, 95, 75, 76, 75, 37, 36, 92, 95, 90, 83, 82, 89, 83, 90, 76, 36, 95, 55, 142, 32, 55, 32, 141, 82, 142, 55, 81, 141, 140, 55, 141, 81, 81, 140, 35, 59, 144, 145, 37, 144, 59, 61, 146, 62, 31, 146, 61, 56, 60, 145, 31, 56, 145, 59, 145, 60, 61, 56, 31, 51, 82, 55, 90, 82, 51, 13, 122, 12, 91, 81, 35, 51, 55, 81, 91, 51, 81, 91, 35, 92, 71, 61, 62, 34, 71, 70, 77, 37, 59, 76, 37, 77, 72, 56, 61, 72, 61, 71, 78, 59, 60, 77, 59, 78, 56, 78, 60, 49, 56, 72, 49, 78, 56, 46, 92, 36, 25, 33, 24, 26, 121, 25, 48, 92, 46, 91, 92, 48, 90, 33, 89, 25, 121, 33, 33, 90, 24, 47, 76, 77, 46, 36, 76, 47, 46, 76, 119, 90, 51, 24, 90, 119, 113, 119, 51, 91, 113, 51, 107, 91, 48, 107, 113, 91, 34, 122, 14, 16, 71, 34, 14, 122, 13, 116, 78, 49, 14, 15, 34, 71, 117, 72, 44, 107, 48, 42, 48, 46, 42, 46, 47, 44, 48, 42, 117, 49, 72, 71, 16, 117, 116, 49, 117, 111, 77, 78, 111, 78, 116, 47, 77, 111, 43, 47, 111, 42, 47, 43, 16, 34, 15, 41, 107, 44, 45, 44, 42, 45, 42, 43, 41, 44, 45, 40, 45, 43, 23, 24, 119, 120, 119, 113, 23, 119, 120, 114, 120, 113, 114, 113, 107, 29, 41, 45, 29, 45, 40, 115, 111, 116, 17, 117, 16, 118, 116, 117, 118, 117, 17, 115, 116, 118, 108, 114, 107, 108, 107, 41, 96, 108, 41, 96, 41, 29, 112, 111, 115, 111, 40, 43, 112, 40, 111, 112, 98, 40, 97, 96, 29, 98, 97, 29, 98, 29, 40, 108, 22, 114, 109, 108, 96, 101, 96, 97, 22, 120, 114, 109, 22, 108, 23, 120, 22, 110, 98, 112, 100, 97, 98, 100, 98, 110, 99, 101, 97, 99, 97, 100, 102, 96, 101, 109, 96, 102, 18, 112, 115, 110, 112, 18, 18, 115, 118, 18, 118, 17, 103, 100, 110, 30, 101, 99, 102, 101, 30, 30, 99, 100, 30, 100, 103, 104, 102, 30, 106, 30, 103, 105, 104, 30, 105, 30, 106, 19, 105, 106, 106, 103, 110, 106, 110, 18, 19, 106, 18, 21, 104, 105, 109, 102, 104, 104, 22, 109, 21, 22, 104, 20, 105, 19, 21, 105, 20], "vertices": [2, 4, 170.72, 94.31, 0.75, 6, -15.04, 95.23, 0.25, 2, 4, 210.4, 96.55, 0.75, 6, 24.64, 97.47, 0.25, 2, 4, 253.87, 92.72, 0.75, 6, 68.11, 93.64, 0.25, 2, 4, 289.81, 60.37, 0.75, 6, 104.05, 61.29, 0.25, 2, 4, 307.34, 25, 0.75, 6, 121.58, 25.92, 0.25, 2, 4, 308.61, -7.18, 0.75, 6, 122.85, -6.26, 0.25, 2, 4, 304.98, -40.46, 0.75, 6, 119.22, -39.54, 0.25, 2, 4, 286.45, -74.39, 0.75, 6, 100.68, -73.46, 0.25, 2, 4, 243.13, -102.85, 0.75, 6, 57.36, -101.93, 0.25, 2, 4, 202.01, -104.48, 0.75, 6, 16.25, -103.56, 0.25, 2, 4, 166.58, -99.76, 0.75, 6, -19.18, -98.84, 0.25, 2, 4, 165.92, -105.91, 0.75, 6, -19.84, -104.99, 0.25, 2, 4, 146.63, -106.68, 0.75, 6, -39.13, -105.75, 0.25, 2, 4, 124.59, -100.57, 0.75, 6, -61.17, -99.65, 0.25, 2, 4, 103.03, -86.37, 0.75, 6, -82.74, -85.44, 0.25, 2, 4, 100.66, -76.55, 0.75, 6, -85.11, -75.63, 0.25, 2, 4, 95.77, -74.38, 0.75, 6, -89.99, -73.46, 0.25, 2, 4, 79.49, -67.13, 0.75, 6, -106.27, -66.21, 0.25, 2, 4, 55.71, -46.64, 0.806, 6, -130.05, -45.72, 0.194, 2, 4, 36.05, -21.69, 0.838, 6, -149.72, -20.77, 0.162, 2, 4, 28.28, -4.24, 0.838, 6, -157.49, -3.32, 0.162, 2, 4, 34.25, 15.9, 0.862, 6, -151.51, 16.83, 0.138, 2, 4, 58.99, 47.05, 0.822, 6, -126.77, 47.97, 0.178, 2, 4, 85.21, 63.93, 0.75, 6, -100.56, 64.85, 0.25, 2, 4, 102.93, 71.99, 0.75, 6, -82.83, 72.92, 0.25, 2, 4, 106.58, 82.08, 0.75, 6, -79.18, 83, 0.25, 2, 4, 130.65, 97.4, 0.75, 6, -55.11, 98.32, 0.25, 2, 4, 150.7, 102.65, 0.75, 6, -35.06, 103.57, 0.25, 2, 4, 167.62, 103.32, 0.75, 6, -18.15, 104.24, 0.25, 2, 4, 81.86, -2.45, 0.7, 5, -12.89, -0.5, 0.3, 3, 4, 50.81, -2.47, 0.6832, 5, -43.94, -0.52, 0.2928, 6, -134.96, -1.55, 0.024, 2, 4, 139.57, -50.15, 0.8, 5, 44.82, -48.2, 0.2, 2, 4, 139.37, 42.82, 0.8, 5, 44.62, 44.77, 0.2, 1, 4, 137.22, 82.27, 1, 2, 4, 134.88, -85.12, 0.856, 5, 40.14, -83.18, 0.144, 2, 4, 128.92, 13.79, 0.78, 5, 34.17, 15.74, 0.22, 2, 4, 126.23, -3.38, 0.7, 5, 31.48, -1.43, 0.3, 2, 4, 129.29, -21.55, 0.78, 5, 34.54, -19.6, 0.22, 2, 4, 149.96, -84.01, 0.85, 5, 55.21, -82.07, 0.15, 2, 4, 151.62, 81.04, 0.85, 5, 56.87, 82.99, 0.15, 3, 4, 88.86, -16.25, 0.7125, 5, -5.88, -14.3, 0.2375, 6, -96.9, -15.33, 0.05, 3, 4, 90.79, 11.93, 0.7125, 5, -3.96, 13.88, 0.2375, 6, -94.98, 12.85, 0.05, 2, 4, 97.35, -2.78, 0.7, 5, 2.61, -0.83, 0.3, 3, 4, 94.72, -17.23, 0.7125, 5, -0.03, -15.28, 0.2375, 6, -91.05, -16.31, 0.05, 3, 4, 96.88, 11.52, 0.7125, 5, 2.13, 13.47, 0.2375, 6, -88.88, 12.44, 0.05, 2, 4, 89.8, -2.62, 0.7, 5, -4.94, -0.67, 0.3, 2, 4, 114.14, -3.13, 0.7, 5, 19.39, -1.18, 0.3, 3, 4, 105.91, -13.41, 0.7125, 5, 11.16, -11.47, 0.2375, 6, -79.86, -12.49, 0.05, 3, 4, 106.89, 7.14, 0.7125, 5, 12.14, 9.09, 0.2375, 6, -78.88, 8.06, 0.05, 2, 4, 117.68, -49.37, 0.8, 5, 22.93, -47.43, 0.2, 2, 4, 157.03, -49.14, 0.8, 5, 62.29, -47.19, 0.2, 2, 4, 118.92, 43.9, 0.8, 5, 24.17, 45.85, 0.2, 2, 4, 157.27, 40.92, 0.8, 5, 62.52, 42.87, 0.2, 2, 4, 159.63, 40.67, 0.8, 5, 64.88, 42.62, 0.2, 2, 4, 158.33, -49.05, 0.8, 5, 63.58, -47.1, 0.2, 2, 4, 123.11, 43.68, 0.8, 5, 28.37, 45.62, 0.2, 2, 4, 123.39, -49.47, 0.8, 5, 28.64, -47.52, 0.2, 2, 4, 153.39, -36.68, 0.78, 5, 58.64, -34.74, 0.22, 2, 4, 145.45, -26.48, 0.78, 5, 50.7, -24.53, 0.22, 2, 4, 125.02, -30.84, 0.79, 5, 30.27, -28.9, 0.21, 2, 4, 123.32, -41.71, 0.79, 5, 28.57, -39.76, 0.21, 2, 4, 126.05, -64.2, 0.85, 5, 31.31, -62.25, 0.15, 2, 4, 132.57, -74.46, 0.88, 5, 37.82, -72.51, 0.12, 2, 4, 141.06, -80.8, 0.85, 5, 46.32, -78.85, 0.15, 2, 4, 157.04, -60.3, 0.85, 5, 62.29, -58.35, 0.15, 2, 4, 155.16, -73.12, 0.85, 5, 60.42, -71.17, 0.15, 2, 4, 160.31, -59.77, 0.85, 5, 65.56, -57.82, 0.15, 2, 4, 159.72, -73.67, 0.85, 5, 64.97, -71.72, 0.15, 2, 4, 154.17, -88, 0.85, 5, 59.42, -86.06, 0.15, 2, 4, 148.11, -87.92, 0.85, 5, 53.36, -85.97, 0.15, 2, 4, 138.34, -83.55, 0.85, 5, 43.59, -81.6, 0.15, 2, 4, 123.43, -74.44, 0.84, 5, 28.68, -72.49, 0.16, 2, 4, 119.68, -63.75, 0.85, 5, 24.93, -61.8, 0.15, 2, 4, 155.56, -36.47, 0.78, 5, 60.81, -34.52, 0.22, 2, 4, 151.74, -26, 0.78, 5, 57, -24.06, 0.22, 2, 4, 145.95, -17.25, 0.78, 5, 51.21, -15.3, 0.22, 2, 4, 127.54, -11.15, 0.78, 5, 32.79, -9.2, 0.22, 2, 4, 119.26, -27.15, 0.78, 5, 24.51, -25.21, 0.22, 2, 4, 118.06, -41.63, 0.79, 5, 23.32, -39.68, 0.21, 2, 4, 153.8, 30.08, 0.78, 5, 59.05, 32.03, 0.22, 2, 4, 144.16, 19.45, 0.78, 5, 49.42, 21.4, 0.22, 2, 4, 123.63, 30.07, 0.78, 5, 28.89, 32.02, 0.22, 2, 4, 127.24, 63.34, 0.85, 5, 32.5, 65.28, 0.15, 2, 4, 140.32, 76.67, 0.85, 5, 45.57, 78.62, 0.15, 2, 4, 157.79, 63.56, 0.85, 5, 63.05, 65.51, 0.15, 2, 4, 158.17, 48.99, 0.85, 5, 63.43, 50.93, 0.15, 2, 4, 161.29, 54.83, 0.85, 5, 66.55, 56.78, 0.15, 2, 4, 160.77, 68.02, 0.85, 5, 66.02, 69.96, 0.15, 2, 4, 155.72, 83.89, 0.85, 5, 60.97, 85.84, 0.15, 2, 4, 138.25, 80.52, 0.85, 5, 43.5, 82.47, 0.15, 2, 4, 123.81, 65.06, 0.85, 5, 29.06, 67, 0.15, 2, 4, 119.39, 27.62, 0.78, 5, 24.65, 29.57, 0.22, 2, 4, 128.01, 7.98, 0.78, 5, 33.26, 9.92, 0.22, 2, 4, 146.93, 14.07, 0.78, 5, 52.18, 16.02, 0.22, 2, 4, 156.31, 28.51, 0.78, 5, 61.56, 30.46, 0.22, 2, 4, 145.19, -3.77, 0.7, 5, 50.44, -1.83, 0.3, 3, 4, 69.7, 21.17, 0.7125, 5, -25.05, 23.12, 0.2375, 6, -116.06, 22.1, 0.05, 2, 4, 67.01, -2.46, 0.7, 5, -27.74, -0.51, 0.3, 3, 4, 68.84, -26.71, 0.7125, 5, -25.91, -24.77, 0.2375, 6, -116.92, -25.79, 0.05, 3, 4, 57.23, -2.47, 0.6832, 5, -37.52, -0.52, 0.2928, 6, -128.54, -1.55, 0.024, 3, 4, 58.57, -19.37, 0.7125, 5, -36.18, -17.43, 0.2375, 6, -127.2, -18.45, 0.05, 3, 4, 60.34, 14.09, 0.7125, 5, -34.41, 16.04, 0.2375, 6, -125.42, 15.01, 0.05, 2, 4, 54.13, 18.66, 0.75, 5, -40.61, 20.61, 0.25, 2, 4, 52.15, -22.55, 0.75, 5, -42.6, -20.61, 0.25, 2, 4, 46.45, 16.98, 0.846, 5, -48.29, 18.93, 0.154, 2, 4, 38.98, -2.94, 0.7, 5, -55.77, -0.99, 0.3, 2, 4, 45.61, -22.47, 0.75, 5, -49.14, -20.52, 0.25, 2, 4, 99.6, 28.63, 0.78, 5, 4.85, 30.58, 0.22, 2, 4, 73.37, 32.42, 0.78, 5, -21.38, 34.37, 0.22, 2, 4, 62.16, 32.56, 0.808, 5, -32.59, 34.5, 0.192, 2, 4, 61.05, -36.83, 0.78, 5, -33.7, -34.88, 0.22, 2, 4, 97.29, -30.18, 0.78, 5, 2.54, -28.23, 0.22, 2, 4, 71.31, -37.39, 0.78, 5, -23.44, -35.44, 0.22, 2, 4, 101.81, 44.8, 0.8, 5, 7.06, 46.75, 0.2, 2, 4, 81.39, 45.87, 0.8, 5, -13.36, 47.82, 0.2, 2, 4, 79.44, -47.69, 0.8, 5, -15.31, -45.74, 0.2, 2, 4, 99.62, -48.58, 0.8, 5, 4.87, -46.63, 0.2, 2, 4, 99.03, -64.49, 0.824, 5, 4.29, -62.54, 0.176, 2, 4, 79.03, -56.98, 0.85, 5, -15.71, -55.03, 0.15, 2, 4, 102.95, 61.02, 0.85, 5, 8.2, 62.97, 0.15, 2, 4, 82.92, 54.63, 0.85, 5, -11.83, 56.57, 0.15, 1, 4, 132.67, 86.98, 1, 1, 4, 131.19, -91.79, 1, 2, 4, 176.17, 38.92, 0.8, 5, 81.42, 40.87, 0.2, 2, 4, 171.2, 18.66, 0.78, 5, 76.45, 20.61, 0.22, 2, 4, 168.12, -4.25, 0.7, 5, 73.37, -2.3, 0.3, 2, 4, 171.96, -26.02, 0.78, 5, 77.22, -24.07, 0.22, 2, 4, 180.39, -47.76, 0.8, 5, 85.64, -45.81, 0.2, 2, 4, 183.97, -75.53, 0.85, 5, 89.23, -73.58, 0.15, 2, 4, 180.36, 72.98, 0.85, 5, 85.62, 74.92, 0.15, 2, 4, 217.32, 67.59, 0.85, 5, 122.58, 69.53, 0.15, 2, 4, 215.05, 34.79, 0.8, 5, 120.3, 36.74, 0.2, 2, 4, 210.67, -5.14, 0.7, 5, 115.92, -3.19, 0.3, 2, 4, 214.41, -45.76, 0.8, 5, 119.66, -43.82, 0.2, 2, 4, 214.29, -77.32, 0.85, 5, 119.54, -75.37, 0.15, 2, 4, 267.44, 62.17, 0.85, 5, 172.7, 64.11, 0.15, 2, 4, 271.27, 28.83, 0.8, 5, 176.53, 30.77, 0.2, 2, 4, 272.77, -6.43, 0.7, 5, 178.02, -4.49, 0.3, 2, 4, 267.25, -42.67, 0.8, 5, 172.5, -40.72, 0.2, 2, 4, 256.73, -75.91, 0.85, 5, 161.99, -73.97, 0.15, 2, 4, 132.81, 24.62, 0.78746, 5, 38.07, 26.56, 0.21254, 2, 4, 137.31, 37.12, 0.79607, 5, 42.57, 39.07, 0.20393, 2, 4, 141.81, 50.44, 0.80996, 5, 47.06, 52.39, 0.19004, 2, 4, 146.89, 66.3, 0.83071, 5, 52.14, 68.25, 0.16929, 2, 4, 132.05, -29.22, 0.78536, 5, 37.3, -27.27, 0.21464, 2, 4, 135.65, -39.24, 0.79237, 5, 40.9, -37.3, 0.20763, 2, 4, 142.4, -59.39, 0.81365, 5, 47.66, -57.45, 0.18635, 2, 4, 146.19, -71.74, 0.83188, 5, 51.44, -69.79, 0.16812], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 0, 66, 66, 48, 20, 68, 30, 32, 32, 34, 68, 32, 58, 80, 58, 82, 84, 86, 84, 88, 88, 82, 86, 80, 58, 90, 90, 84, 72, 92, 92, 84, 92, 94, 94, 86, 92, 96, 96, 88, 100, 62, 104, 64, 106, 104, 108, 100, 64, 110, 110, 102, 62, 112, 112, 98, 100, 114, 114, 116, 116, 74, 74, 118, 118, 120, 120, 112, 112, 122, 122, 124, 124, 126, 126, 76, 100, 128, 128, 130, 130, 76, 108, 132, 132, 134, 20, 136, 136, 76, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 108, 146, 146, 148, 148, 150, 72, 152, 152, 74, 150, 152, 152, 154, 154, 156, 104, 158, 158, 160, 160, 70, 70, 162, 110, 164, 110, 162, 164, 166, 166, 78, 78, 168, 168, 170, 170, 104, 106, 172, 172, 174, 0, 176, 176, 78, 174, 176, 176, 178, 178, 180, 180, 102, 102, 182, 70, 184, 184, 72, 182, 184, 184, 186, 188, 186, 188, 106, 190, 72, 186, 190, 58, 194, 192, 194, 194, 196, 60, 198, 198, 194, 198, 200, 200, 196, 192, 202, 202, 198, 204, 60, 60, 206, 208, 210, 210, 212, 182, 214, 214, 216, 216, 218, 218, 204, 206, 220, 154, 222, 222, 224, 224, 220, 226, 102, 44, 228, 228, 226, 36, 230, 98, 232, 232, 230, 156, 98, 98, 144, 234, 236, 238, 240, 54, 242, 24, 244, 246, 106, 246, 248, 250, 190, 248, 250, 250, 252, 254, 108, 252, 254, 254, 256, 256, 20, 246, 258, 258, 0, 2, 260, 262, 246, 260, 262, 264, 250, 262, 264, 266, 254, 264, 266, 266, 268, 268, 18, 4, 270, 8, 272, 272, 262, 270, 272, 10, 274, 274, 264, 272, 274, 12, 276, 276, 266, 274, 276, 276, 278, 280, 70, 64, 282, 282, 280, 64, 284, 78, 286, 286, 284, 288, 74, 62, 290, 290, 288, 62, 292, 76, 294, 294, 292], "width": 209, "height": 280}}, "zfy08": {"zfy08": {"type": "mesh", "uvs": [0.18646, 0.20819, 0.265, 0.11342, 0.38463, 0.03789, 0.47646, 0.01272, 0.54896, 0, 0.64563, 0.00828, 0.77855, 0.06899, 0.86434, 0.146, 0.95013, 0.27187, 0.99363, 0.42588, 1, 0.56359, 0.98155, 0.71168, 0.94167, 0.7872, 0.87521, 0.81978, 0.88125, 0.91011, 0.86917, 0.95306, 0.85105, 0.98712, 0.8323, 0.93505, 0.79667, 0.83607, 0.76163, 0.79609, 0.75075, 0.69391, 0.72666, 0.59762, 0.70677, 0.52983, 0.68129, 0.47651, 0.69932, 0.55344, 0.70491, 0.63342, 0.70429, 0.72254, 0.69248, 0.77129, 0.61727, 0.77967, 0.54704, 0.78728, 0.47308, 0.77281, 0.40285, 0.75072, 0.3985, 0.65932, 0.40658, 0.57858, 0.42709, 0.49631, 0.38855, 0.55344, 0.35623, 0.62504, 0.3245, 0.71374, 0.28982, 0.80443, 0.25975, 0.90646, 0.22738, 1, 0.21273, 1, 0.19654, 0.98014, 0.21273, 0.88567, 0.22661, 0.80254, 0.24113, 0.74309, 0.20312, 0.78766, 0.1396, 0.79109, 0.08543, 0.74988, 0.05087, 0.66403, 0.04433, 0.59191, 0.00977, 0.56215, 0, 0.49805, 0.01164, 0.36985, 0.05647, 0.26683, 0.12372, 0.2073, 0.11065, 0.40648, 0.07235, 0.51637, 0.14707, 0.30575, 0.10037, 0.29086, 0.05647, 0.38816, 0.03126, 0.50721, 0.55642, 0.37499, 0.55551, 0.4907, 0.55367, 0.57945, 0.55092, 0.68954, 0.61417, 0.48059, 0.62701, 0.57271, 0.63526, 0.68055, 0.49776, 0.48171, 0.47301, 0.59068, 0.47117, 0.70078, 0.23672, 0.89288, 0.25872, 0.79402, 0.29355, 0.65809, 0.32839, 0.51991, 0.39347, 0.40869, 0.46772, 0.35477, 0.63584, 0.35701, 0.72934, 0.44576, 0.78067, 0.59068, 0.80176, 0.69291, 0.81826, 0.77829, 0.84209, 0.87827, 0.92294, 0.6464, 0.89178, 0.49249, 0.82669, 0.34982, 0.72311, 0.21838, 0.63969, 0.18243, 0.55078, 0.17007, 0.44078, 0.19142, 0.31776, 0.24686, 0.24229, 0.36577, 0.19078, 0.4656, 0.15005, 0.59039, 0.13089, 0.69609, 0.26016, 0.62759, 0.28455, 0.50204, 0.34504, 0.3992, 0.42212, 0.33104, 0.66995, 0.32387, 0.77142, 0.41475, 0.81728, 0.5415, 0.84753, 0.66346, 0.86021, 0.74477], "triangles": [73, 44, 45, 38, 73, 37, 72, 44, 73, 72, 73, 38, 43, 44, 72, 39, 72, 38, 42, 43, 72, 41, 72, 40, 72, 41, 42, 39, 40, 72, 74, 96, 75, 36, 74, 75, 37, 74, 36, 37, 73, 74, 76, 98, 99, 76, 99, 77, 34, 76, 77, 75, 98, 76, 34, 35, 75, 34, 75, 76, 36, 75, 35, 18, 19, 82, 83, 18, 82, 13, 83, 82, 83, 13, 14, 17, 18, 83, 17, 83, 14, 15, 17, 14, 16, 17, 15, 82, 104, 13, 103, 102, 84, 80, 102, 103, 81, 80, 103, 20, 21, 80, 20, 80, 81, 104, 103, 84, 81, 103, 104, 82, 81, 104, 19, 20, 81, 19, 81, 82, 79, 100, 101, 78, 100, 79, 23, 78, 79, 22, 23, 79, 102, 101, 85, 79, 101, 102, 80, 22, 79, 102, 80, 79, 80, 21, 22, 65, 64, 68, 26, 68, 25, 27, 68, 26, 28, 65, 68, 28, 68, 27, 29, 65, 28, 67, 23, 24, 67, 24, 25, 68, 67, 25, 64, 63, 67, 64, 67, 68, 66, 62, 78, 66, 78, 23, 66, 63, 62, 67, 66, 23, 63, 66, 67, 71, 32, 70, 71, 70, 65, 31, 32, 71, 30, 71, 65, 31, 71, 30, 29, 30, 65, 70, 34, 69, 70, 69, 64, 33, 34, 70, 32, 33, 70, 65, 70, 64, 69, 77, 62, 63, 69, 62, 69, 34, 77, 64, 69, 63, 45, 95, 96, 101, 100, 86, 101, 86, 85, 12, 104, 84, 86, 87, 7, 96, 93, 97, 95, 94, 96, 97, 92, 98, 93, 92, 97, 94, 57, 93, 93, 56, 92, 50, 57, 94, 49, 50, 94, 95, 49, 94, 47, 95, 46, 56, 59, 58, 59, 55, 58, 60, 54, 59, 53, 54, 60, 0, 1, 91, 99, 91, 90, 99, 90, 77, 89, 4, 5, 3, 4, 89, 88, 89, 5, 88, 5, 6, 90, 2, 3, 90, 3, 89, 87, 88, 6, 100, 88, 87, 77, 90, 89, 78, 89, 88, 78, 88, 100, 62, 77, 89, 78, 62, 89, 91, 1, 2, 90, 91, 2, 59, 54, 55, 58, 55, 0, 92, 0, 91, 60, 59, 56, 52, 53, 60, 56, 57, 60, 57, 56, 93, 61, 52, 60, 57, 61, 60, 51, 52, 61, 50, 61, 57, 51, 61, 50, 48, 49, 95, 48, 95, 47, 58, 0, 92, 98, 91, 99, 92, 91, 98, 56, 58, 92, 75, 97, 98, 94, 93, 96, 87, 6, 7, 86, 7, 8, 100, 87, 86, 86, 8, 9, 85, 86, 9, 85, 9, 10, 102, 85, 84, 84, 85, 10, 11, 84, 10, 11, 12, 84, 46, 95, 45, 73, 45, 74, 96, 97, 75, 45, 96, 74, 13, 104, 12], "vertices": [1, 8, -4.76, 108.41, 1, 1, 8, 18.39, 86.69, 1, 1, 8, 37.48, 52.97, 1, 1, 8, 44.44, 26.77, 1, 1, 8, 48.26, 6.03, 1, 1, 8, 47.41, -21.87, 1, 1, 8, 34.67, -60.68, 1, 1, 8, 17.57, -86.09, 1, 1, 8, -11.01, -111.95, 1, 1, 8, -46.68, -125.9, 1, 1, 8, -78.95, -129.01, 1, 1, 8, -113.93, -125.08, 1, 1, 8, -132.12, -114.31, 1, 3, 8, -140.52, -95.48, 0.456, 19, 57.17, 15.13, 0.08956, 20, 13.73, 15, 0.45444, 1, 20, 34.79, 11.81, 1, 1, 20, 43.81, 6.1, 1, 1, 20, 50.4, -0.82, 1, 1, 20, 37.24, -3.26, 1, 1, 20, 12.25, -7.89, 1, 2, 19, 44.51, -15.54, 0.50132, 20, 0.78, -15.55, 0.49868, 2, 18, 55.67, -18.01, 0.00018, 19, 20.4, -13.29, 0.99982, 2, 18, 32.39, -13.77, 0.7494, 19, -3.2, -15.06, 0.2506, 3, 12, 39.23, 18.77, 0.011, 13, 3.25, 21.26, 0.00362, 18, 15.6, -11.54, 0.98539, 3, 12, 24.83, 16.91, 0.34383, 13, -9.59, 14.47, 0.13427, 18, 1.1, -12.3, 0.52189, 4, 12, 43.5, 14.63, 0.03236, 13, 8.7, 18.87, 0.92127, 14, -20.27, 17.5, 0.01508, 18, 19.55, -16, 0.03129, 3, 11, 4.84, 70.8, 2e-05, 13, 27.54, 19.66, 0.56261, 14, -1.52, 19.61, 0.43737, 3, 11, 25.71, 69.02, 0.00075, 13, 48.46, 18.58, 0.02056, 14, 19.42, 19.98, 0.97868, 2, 11, 36.87, 64.75, 0.00171, 14, 30.96, 16.88, 0.99829, 2, 11, 37.17, 43, 0.04427, 14, 33.5, -4.72, 0.95573, 2, 11, 37.4, 22.7, 0.49037, 14, 35.82, -24.89, 0.50963, 2, 11, 32.38, 1.72, 0.95945, 14, 32.98, -46.28, 0.04055, 1, 11, 25.65, -18.05, 1, 2, 10, 38.33, -15.65, 0.2321, 11, 4.14, -17.65, 0.7679, 3, 10, 19.31, -17.55, 0.93006, 11, -14.6, -13.87, 0.05652, 15, 35.79, 22.45, 0.01342, 3, 9, 34.31, -15.35, 0.1935, 10, -0.85, -16.04, 0.32283, 15, 16.29, 17.13, 0.48367, 4, 9, 51.61, -17.4, 0.00759, 10, 14.69, -23.92, 0.02103, 15, 33.56, 14.91, 0.93319, 16, -11.02, 18.28, 0.03819, 2, 15, 52.76, 16.01, 0.21174, 16, 7.84, 14.5, 0.78826, 2, 16, 30.47, 12.11, 0.96054, 17, -14.26, 12.45, 0.03946, 2, 16, 53.81, 9.04, 0.06631, 17, 9, 8.83, 0.93369, 1, 17, 34.44, 7.23, 1, 1, 17, 58.16, 4.43, 1, 1, 17, 59.34, 0.38, 1, 1, 17, 56.16, -5.4, 1, 1, 17, 33.54, -7.14, 1, 2, 16, 58.9, -8.44, 0.00354, 17, 13.67, -8.77, 0.99646, 1, 8, -129.74, 87.7, 1, 1, 8, -140.64, 98.23, 1, 1, 8, -142.17, 116.47, 1, 1, 8, -133.11, 132.45, 1, 1, 8, -113.35, 143.19, 1, 1, 8, -96.49, 145.74, 1, 1, 8, -89.89, 155.96, 1, 1, 8, -74.95, 159.37, 1, 1, 8, -44.71, 157.21, 1, 1, 8, -20.01, 145.27, 1, 1, 8, -5.27, 126.47, 1, 1, 8, -52.19, 128.38, 1, 1, 8, -78.43, 138.38, 1, 1, 8, -28.12, 118.84, 1, 1, 8, -25.16, 132.41, 1, 1, 8, -48.5, 144.14, 1, 1, 8, -76.75, 150.29, 1, 5, 8, -39.71, 0.39, 0.08379, 9, -9.84, 0.49, 0.53187, 12, -11.17, -6.88, 0.36639, 18, -36.61, -33.28, 0.00145, 15, -27.7, 33.41, 0.01649, 5, 9, 13.15, 15.01, 0.50324, 10, -10.27, 19.75, 0.06926, 11, -31.9, 30.47, 0.00052, 12, 13.75, -17.74, 0.34394, 13, -7.83, -21.87, 0.08304, 6, 9, 30.96, 25.87, 0.11302, 10, 10.2, 23.82, 0.34118, 11, -11.15, 28.35, 0.05917, 12, 32.75, -26.37, 0.06338, 13, 12.98, -23.3, 0.36648, 14, -13.05, -24.27, 0.05676, 5, 9, 53.13, 39.23, 0.00049, 10, 35.61, 28.73, 0.08804, 11, 14.59, 25.57, 0.39633, 13, 38.79, -25.22, 0.0853, 14, 12.83, -24.38, 0.42984, 4, 9, 2, 27.92, 0.0115, 10, -16.3, 35.71, 0.00221, 12, 18.17, -1.26, 0.97693, 13, -9.47, -4.88, 0.00936, 5, 9, 18.18, 42.76, 0.00621, 10, 4.01, 44.07, 0.01134, 11, -11.11, 49.53, 0.00213, 12, 39.54, -6.31, 0.00657, 13, 12.32, -2.13, 0.97375, 4, 9, 38.19, 58.5, 0, 10, 28.21, 51.96, 0.00031, 11, 14.35, 49.95, 0.00083, 14, 10.08, -0.16, 0.99886, 3, 9, 20.39, -0.11, 0.99795, 13, -10.67, -38.39, 7e-05, 15, 2.52, 32.51, 0.00198, 6, 9, 45.78, 7.78, 0.0009, 10, 17.88, 1.74, 0.9777, 11, -10.3, 4.98, 0.01043, 12, 26.11, -48.79, 0.00055, 13, 14.61, -46.63, 0.00739, 14, -9.8, -47.42, 0.00303, 4, 10, 43.23, 6.91, 0.00518, 11, 15.46, 2.47, 0.97281, 13, 40.43, -48.28, 0.00327, 14, 16.07, -47.27, 0.01874, 1, 17, 33.24, -0.03, 1, 1, 17, 9.16, -0.45, 1, 1, 16, 20.71, -0.35, 1, 3, 8, -76.34, 64.67, 3e-05, 15, 36.15, -3.95, 0.99194, 16, -13.28, -0.62, 0.00803, 2, 8, -49.48, 46.97, 0.00243, 15, 4.04, -2.05, 0.99757, 3, 8, -35.98, 26.11, 0.13315, 9, 0.02, -23.55, 0.4335, 15, -18.09, 9.27, 0.43335, 4, 8, -34.59, -22.29, 0.14973, 9, -25.79, 17.42, 0.00975, 12, -6.13, 15.83, 0.63209, 18, -29.85, -11.03, 0.20843, 2, 8, -54.36, -50.03, 0.00618, 18, 1.04, 3.31, 0.99382, 3, 8, -87.81, -66.15, 1e-05, 18, 38.08, 0.79, 0.61464, 19, -1.36, 0.47, 0.38535, 1, 19, 23.42, 1.08, 1, 2, 19, 44.03, 1.29, 0.45589, 20, 0.47, 1.28, 0.54411, 1, 20, 24.91, 2.55, 1, 1, 8, -99.27, -107.61, 1, 1, 8, -63.48, -97.21, 1, 1, 8, -30.72, -77.15, 1, 1, 8, -1.04, -46.12, 1, 1, 8, 6.45, -21.78, 1, 1, 8, 8.34, 3.92, 1, 1, 8, 2.07, 35.38, 1, 1, 8, -12.35, 70.27, 1, 1, 8, -41.13, 90.88, 1, 1, 8, -65.16, 104.77, 1, 1, 8, -94.92, 115.33, 1, 1, 8, -119.96, 119.87, 1, 3, 8, -102.4, 83.3, 0.632, 15, 68.03, -7.07, 0.00818, 16, 16.79, -11.68, 0.35983, 1, 8, -72.64, 77.45, 1, 1, 8, -47.81, 61, 1, 3, 8, -30.93, 39.45, 0.16139, 9, 2.45, -37.61, 0.13166, 15, -15.79, -4.81, 0.70695, 4, 8, -26.42, -31.8, 0.25199, 9, -37.66, 21.45, 0.00168, 12, -9.46, 27.91, 0.37892, 18, -32.26, 1.28, 0.36741, 4, 8, -46.6, -61.85, 0.05517, 12, 21.61, 46.48, 0.01231, 18, 0.14, 17.42, 0.92463, 19, -42.27, 7, 0.00789, 3, 8, -75.84, -76.22, 0.00311, 18, 32.66, 15.47, 0.75547, 19, -10.3, 13.3, 0.24141, 3, 18, 62.12, 10.04, 0.04061, 19, 19.58, 15.47, 0.93865, 20, -23.85, 15.7, 0.02074, 3, 18, 80.77, 4.5, 0.00206, 19, 39.02, 14.81, 0.72628, 20, -4.42, 14.85, 0.27166], "hull": 56, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 0, 110, 118, 120, 120, 122, 124, 126, 126, 128, 128, 130, 132, 134, 134, 136, 138, 140, 140, 142, 82, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 32, 34, 34, 36, 166, 34, 24, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 90, 192, 192, 194, 194, 196, 196, 198, 200, 202, 202, 204, 204, 206, 206, 208], "width": 288, "height": 235}}, "zfy09": {"zfy09": {"type": "mesh", "uvs": [1, 0.20233, 1, 0.4029, 1, 0.67097, 1, 1, 0.53703, 1, 0, 1, 0, 0.66518, 0, 0.40676, 0, 0.20619, 0, 0, 0.45198, 0, 1, 0, 0.46143, 0.66326, 0.45198, 0.40676, 0.50868, 0.2004], "triangles": [12, 6, 7, 12, 1, 2, 5, 6, 12, 4, 5, 12, 2, 4, 12, 4, 2, 3, 14, 10, 11, 14, 11, 0, 8, 9, 10, 8, 10, 14, 14, 0, 1, 13, 7, 8, 14, 13, 8, 1, 13, 14, 12, 13, 1, 12, 7, 13], "vertices": [1, 24, 8.63, 10.83, 1, 2, 24, 28.28, 11.38, 0.74922, 25, -3.23, 11.41, 0.25078, 1, 25, 23.04, 11.88, 1, 1, 25, 55.28, 12.46, 1, 1, 25, 55.44, 3.21, 1, 1, 25, 55.64, -7.53, 1, 1, 25, 22.83, -8.12, 1, 2, 24, 29.21, -8.6, 0.78, 25, -2.49, -8.58, 0.22, 1, 24, 9.56, -9.15, 1, 1, 24, -10.64, -9.71, 1, 1, 24, -10.89, -0.68, 1, 1, 24, -11.19, 10.28, 1, 1, 25, 22.47, 1.1, 1, 1, 24, 28.96, 0.43, 1, 1, 24, 8.71, 1, 1], "hull": 12, "edges": [16, 18, 0, 22, 0, 2, 14, 16, 10, 12, 12, 14, 2, 4, 4, 6, 6, 8, 8, 10, 8, 24, 24, 26, 26, 28, 18, 20, 20, 22, 28, 20], "width": 20, "height": 98}}, "sm": {"sm": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-18.77, -32.05, -21.5, 36.89, 15.47, 38.36, 18.2, -30.59], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 276, "height": 148}}, "zfy031": {"zfy031": {"type": "mesh", "uvs": [1, 0.06834, 0.79101, 0.23745, 0.63823, 0.40433, 0.55489, 0.56454, 0.50212, 0.81376, 0.64656, 0.96506, 0.65489, 1, 0.34656, 0.98954, 0.12156, 0.99399, 0, 0.95616, 0.12156, 0.87828, 0.24934, 0.65577, 0.32712, 0.50224, 0.32434, 0.322, 0.44378, 0.24635, 0.61045, 0.09281, 0.75212, 0.02606, 0.92989, 0, 1, 0, 0.88545, 0.06166, 0.71323, 0.17959, 0.52989, 0.31088, 0.42434, 0.52004, 0.33545, 0.72475, 0.31601, 0.89609], "triangles": [19, 16, 17, 19, 17, 18, 19, 18, 0, 20, 15, 16, 20, 16, 19, 1, 20, 19, 1, 19, 0, 14, 15, 20, 21, 14, 20, 1, 2, 21, 1, 21, 20, 13, 22, 12, 21, 13, 14, 21, 22, 13, 2, 22, 21, 3, 22, 2, 22, 11, 12, 23, 22, 3, 23, 11, 22, 4, 23, 3, 10, 11, 23, 24, 10, 23, 24, 23, 4, 7, 24, 4, 7, 4, 5, 8, 10, 24, 9, 10, 8, 8, 24, 7, 7, 5, 6], "vertices": [-93.02, -269.79, -124.99, -302.09, -148.37, -333.97, -161.12, -364.57, -169.19, -412.17, -147.09, -441.07, -145.82, -447.74, -192.99, -445.74, -227.42, -446.59, -246.02, -439.37, -273.32, -439.37, -253.77, -396.87, -253.34, -362.44, -255.04, -329.29, -241.44, -281.69, -194.27, -257.89, -144.54, -244.29, -103.74, -256.74, -93.02, -256.74, -110.54, -268.52, -136.89, -291.04, -164.94, -316.12, -181.09, -356.07, -194.69, -395.17, -197.67, -427.89], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 153, "height": 191}}, "zfy011": {"zfy011": {"type": "mesh", "uvs": [1, 0.27757, 1, 0.54871, 1, 0.75576, 1, 1, 0.4168, 1, 0, 1, 0, 0.73111, 0, 0.53638, 0, 0.27264, 0, 0, 0.48336, 0, 1, 0, 0.46857, 0.72372, 0.46117, 0.54378, 0.51294, 0.2751], "triangles": [4, 2, 3, 4, 12, 2, 12, 4, 6, 4, 5, 6, 12, 1, 2, 6, 13, 12, 6, 7, 13, 12, 13, 1, 13, 14, 1, 14, 0, 1, 14, 13, 8, 13, 7, 8, 14, 11, 0, 8, 10, 14, 14, 10, 11, 8, 9, 10], "vertices": [3, 21, -14.27, -19.83, 0.32841, 22, -3.93, 21.1, 0.66995, 23, -30.7, 21.87, 0.00163, 2, 22, 26.95, 22.47, 0.52219, 23, 0.21, 22.47, 0.47781, 2, 22, 50.53, 23.51, 0.02767, 23, 23.81, 22.93, 0.97233, 1, 23, 51.65, 23.47, 1, 1, 23, 52.08, 1.31, 1, 1, 23, 52.38, -14.53, 1, 2, 22, 49.4, -14.58, 0.00028, 23, 21.74, -15.12, 0.99972, 2, 22, 27.22, -15.56, 0.5728, 23, -0.46, -15.55, 0.4272, 2, 21, -15.22, 18.16, 0.23012, 22, -2.82, -16.88, 0.76988, 2, 21, 15.84, 19.39, 0.99856, 22, -33.87, -18.25, 0.00144, 1, 21, 16.57, 1.04, 1, 1, 21, 17.34, -18.58, 1, 2, 22, 47.77, 3.17, 0.0021, 23, 20.55, 2.67, 0.9979, 2, 22, 27.29, 1.99, 0.50816, 23, 0.04, 1.99, 0.49184, 2, 21, -14.72, -1.33, 0.16344, 22, -3.4, 2.6, 0.83656], "hull": 12, "edges": [16, 18, 0, 22, 14, 16, 0, 2, 10, 12, 12, 14, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 20, 22], "width": 38, "height": 114}}, "zfy012": {"zfy012": {"type": "mesh", "uvs": [0.5332, 0.22477, 0.55437, 0.1785, 0.57553, 0.11468, 0.58611, 0.08692, 0.61592, 0.07377, 0.71403, 0.05477, 0.79483, 0.03674, 0.83138, 0.03187, 0.87274, 0.01629, 0.90737, 0, 0.92156, 0.00045, 0.92448, 0.01152, 0.9048, 0.02518, 0.87856, 0.03755, 0.85524, 0.04788, 0.85706, 0.05083, 0.88221, 0.05157, 0.92156, 0.05213, 0.95691, 0.0512, 0.99262, 0.04696, 1, 0.05508, 1, 0.07169, 0.97804, 0.0859, 0.92994, 0.09863, 0.89204, 0.099, 0.84212, 0.09808, 0.81515, 0.09882, 0.77186, 0.10609, 0.73332, 0.10862, 0.70113, 0.11665, 0.67393, 0.12446, 0.66904, 0.13387, 0.65447, 0.19322, 0.64877, 0.25032, 0.69926, 0.28589, 0.72968, 0.33956, 0.73613, 0.40724, 0.70682, 0.48243, 0.64578, 0.56486, 0.618, 0.60506, 0.58249, 0.65645, 0.59153, 0.75605, 0.5757, 0.86138, 0.49658, 0.93923, 0.34286, 1, 0.17847, 1, 0.03542, 1, 0.0309, 0.9564, 0.0309, 0.90717, 0.0196, 0.84192, 0.12133, 0.81787, 0.20723, 0.77666, 0.26374, 0.74804, 0.30819, 0.66842, 0.33928, 0.59729, 0.37465, 0.5354, 0.33392, 0.47839, 0.29103, 0.43387, 0.26531, 0.40673, 0.32985, 0.36591, 0.38401, 0.33422, 0.43816, 0.29338, 0.47546, 0.25133, 0.50916, 0.22817, 0.51868, 0.41765, 0.57637, 0.23563, 0.5639, 0.27498, 0.5499, 0.31913, 0.5346, 0.36741, 0.59407, 0.18407, 0.61444, 0.12234, 0.62146, 0.10426, 0.65079, 0.08973, 0.7337, 0.07778, 0.76622, 0.07519, 0.81787, 0.06776, 0.87718, 0.06873, 0.91983, 0.07006, 0.96682, 0.06604, 0.91412, 0.00765, 0.87826, 0.02307, 0.84008, 0.03948, 0.81348, 0.0467, 0.78533, 0.0508, 0.74793, 0.06311, 0.71554, 0.06877, 0.61375, 0.27739, 0.63232, 0.32807, 0.63438, 0.39444, 0.59517, 0.45139, 0.49922, 0.48065, 0.48477, 0.5444, 0.58589, 0.49319, 0.50954, 0.26746, 0.47549, 0.29881, 0.4466, 0.3495, 0.39604, 0.39235, 0.38469, 0.43676, 0.41255, 0.48118, 0.43628, 0.5397, 0.492, 0.59576, 0.4889, 0.63966, 0.48271, 0.7264, 0.45176, 0.82045, 0.37658, 0.89385, 0.24657, 0.92938, 0.16772, 0.87564, 0.27296, 0.8307, 0.33797, 0.76852, 0.38955, 0.6985, 0.4267, 0.62796, 0.42876, 0.59608], "triangles": [96, 58, 59, 59, 60, 96, 79, 9, 10, 79, 10, 11, 8, 9, 79, 80, 8, 79, 12, 80, 79, 12, 79, 11, 7, 8, 80, 13, 80, 12, 81, 7, 80, 13, 81, 80, 82, 6, 7, 82, 7, 81, 14, 81, 13, 83, 6, 82, 14, 82, 81, 15, 82, 14, 83, 82, 75, 20, 78, 18, 20, 18, 19, 82, 15, 75, 76, 15, 16, 75, 15, 76, 77, 16, 17, 76, 16, 77, 77, 17, 18, 77, 18, 78, 78, 20, 21, 22, 78, 21, 25, 75, 76, 22, 23, 77, 22, 77, 78, 26, 75, 25, 24, 76, 77, 24, 77, 23, 25, 76, 24, 83, 5, 6, 84, 5, 83, 85, 5, 84, 4, 5, 85, 74, 84, 83, 74, 83, 75, 73, 85, 84, 73, 84, 74, 72, 4, 85, 72, 85, 73, 26, 74, 75, 71, 4, 72, 3, 4, 71, 27, 74, 26, 73, 74, 27, 73, 29, 72, 28, 73, 27, 28, 29, 73, 30, 71, 72, 72, 29, 30, 104, 105, 107, 45, 46, 47, 105, 45, 47, 44, 105, 104, 44, 104, 43, 45, 105, 44, 106, 50, 107, 49, 50, 106, 105, 106, 107, 48, 49, 106, 105, 47, 48, 105, 48, 106, 108, 52, 109, 108, 51, 52, 107, 108, 103, 107, 51, 108, 50, 51, 107, 103, 108, 102, 103, 102, 41, 42, 103, 41, 104, 107, 103, 104, 103, 42, 43, 104, 42, 91, 90, 92, 38, 92, 37, 91, 92, 38, 100, 91, 38, 111, 91, 100, 39, 100, 38, 100, 110, 111, 101, 100, 39, 101, 110, 100, 40, 101, 39, 109, 110, 101, 102, 109, 101, 102, 101, 40, 102, 40, 41, 108, 109, 102, 56, 57, 97, 98, 56, 97, 90, 98, 97, 55, 56, 98, 99, 98, 90, 55, 98, 99, 91, 99, 90, 111, 55, 99, 111, 99, 91, 54, 55, 111, 110, 54, 111, 53, 54, 110, 109, 53, 110, 52, 53, 109, 2, 3, 71, 70, 2, 71, 30, 70, 71, 31, 70, 30, 1, 2, 70, 69, 1, 70, 69, 70, 31, 32, 69, 31, 0, 1, 69, 65, 0, 69, 65, 69, 32, 33, 65, 32, 93, 63, 0, 93, 0, 65, 62, 63, 93, 66, 93, 65, 86, 66, 65, 33, 86, 65, 86, 33, 34, 61, 62, 93, 94, 61, 93, 66, 94, 93, 67, 94, 66, 87, 86, 34, 87, 34, 35, 95, 61, 94, 95, 94, 67, 60, 61, 95, 68, 95, 67, 96, 60, 95, 88, 87, 35, 88, 35, 36, 68, 96, 95, 64, 96, 68, 67, 66, 86, 67, 86, 87, 97, 57, 96, 97, 96, 64, 68, 87, 88, 87, 68, 67, 88, 64, 68, 89, 64, 88, 90, 97, 64, 90, 64, 89, 36, 89, 88, 37, 89, 36, 92, 90, 89, 92, 89, 37, 96, 57, 58], "vertices": [1, 31, 188.11, 22, 1, 1, 31, 232.31, 19.47, 1, 2, 31, 292.61, 19.72, 0.95603, 32, -23.3, -0.63, 0.04397, 2, 31, 318.96, 19.19, 0.35042, 32, -9.42, 21.77, 0.64958, 2, 31, 333.43, 7.41, 0.07906, 32, 8.1, 28.22, 0.92094, 2, 32, 57.68, 28.28, 0.96191, 34, -50.19, 27.95, 0.03809, 3, 32, 99.29, 30.4, 0.3147, 33, -6.04, 30.29, 0.00731, 34, -10.32, 15.85, 0.67799, 2, 32, 117.03, 28.5, 0.00982, 34, 5.73, 8.06, 0.99018, 2, 32, 140.45, 35.13, 1e-05, 34, 30.01, 6.36, 0.99999, 2, 32, 161.12, 43.49, 0, 34, 52.29, 7.23, 1, 1, 34, 57.1, 2.54, 1, 1, 34, 51.42, -6.18, 1, 2, 32, 151.64, 21.99, 1e-05, 34, 36.08, -9.79, 0.99999, 2, 32, 135.96, 15.63, 7e-05, 34, 19.18, -10.46, 0.99993, 2, 33, 22.58, 20.23, 0.28162, 34, 4.55, -10.6, 0.71838, 2, 33, 23.47, 17.48, 0.47484, 34, 3.41, -13.25, 0.52516, 2, 33, 35.35, 16.93, 0.86391, 34, 11.97, -21.5, 0.13609, 2, 33, 53.93, 16.62, 0.99, 34, 25.73, -33.99, 0.01, 1, 33, 70.6, 17.66, 1, 1, 33, 87.41, 21.8, 1, 1, 33, 90.98, 14.27, 1, 1, 33, 91.15, -1.21, 1, 1, 33, 80.93, -14.57, 1, 1, 33, 58.36, -26.68, 1, 2, 32, 121.52, -40.16, 0.00669, 33, 40.48, -27.22, 0.99331, 2, 32, 99.8, -30.96, 0.19385, 33, 16.91, -26.62, 0.80615, 2, 32, 87.66, -27.08, 0.51149, 33, 4.19, -27.45, 0.48851, 2, 32, 66.15, -26.14, 0.90873, 33, -16.17, -34.45, 0.09127, 3, 31, 310.83, -52.72, 0.01006, 32, 48.32, -21.86, 0.98736, 33, -34.33, -37.01, 0.00258, 2, 31, 300.87, -39.01, 0.09916, 32, 31.45, -23.45, 0.90084, 2, 31, 291.52, -27.59, 0.41834, 32, 16.86, -25.68, 0.58166, 2, 31, 282.49, -26.81, 0.70402, 32, 11.59, -33.05, 0.29598, 1, 31, 226.81, -29.42, 1, 1, 31, 173.9, -35.8, 1, 1, 31, 145.28, -64.91, 1, 2, 31, 98.42, -87.55, 0.99247, 35, -216.66, 113.1, 0.00753, 2, 31, 36.78, -101.26, 0.92903, 35, -153.52, 114.29, 0.07097, 3, 31, -34.64, -99.52, 0.66726, 38, -45.53, 125.2, 0.01049, 35, -83.88, 98.4, 0.32226, 3, 31, -115.24, -84.17, 0.1391, 38, 35.84, 114.64, 0.00438, 35, -7.93, 67.35, 0.85652, 2, 31, -154.38, -77.6, 0.02265, 35, 29.13, 53.14, 0.97735, 1, 35, 76.51, 34.97, 1, 2, 35, 169.42, 36.51, 0.32498, 36, 0.28, 38.06, 0.67502, 1, 36, 97.25, 55.06, 1, 2, 36, 176.78, 36.78, 0.36048, 37, -3.59, 44.68, 0.63952, 1, 37, 88.41, 47.45, 1, 2, 40, 91.46, 121.76, 0.02329, 37, 150.98, 1.56, 0.97671, 2, 40, 149.9, 87.95, 0.29729, 37, 205.43, -38.37, 0.70271, 2, 40, 131.4, 51.71, 0.47633, 37, 183.12, -72.4, 0.52367, 2, 40, 108.43, 12, 0.89594, 37, 155.99, -109.39, 0.10406, 1, 40, 82.6, -43.32, 1, 2, 39, 140, -48.83, 0.19456, 40, 29.82, -38.68, 0.80544, 2, 39, 88.68, -26.78, 0.97135, 40, -24.51, -51.62, 0.02865, 1, 39, 53.63, -12.83, 1, 1, 38, 166.09, -18.54, 1, 1, 38, 98.21, -19.35, 1, 1, 38, 38.24, -16.22, 1, 2, 31, -60.8, 74.58, 0.20317, 38, -9.12, -47.04, 0.79683, 3, 31, -23.35, 101.57, 0.40358, 38, -44.91, -76.2, 0.35642, 30, 253.3, -73.86, 0.24, 2, 31, -0.47, 117.83, 0.296, 30, 230.89, -90.76, 0.704, 2, 31, 42.19, 94.26, 0.272, 30, 187.58, -68.41, 0.728, 2, 31, 75.64, 74.08, 0.288, 30, 153.58, -49.18, 0.712, 3, 31, 117.48, 55.35, 0.93119, 38, -188.22, -38.39, 0.00481, 30, 111.22, -31.64, 0.064, 1, 31, 159.09, 44.65, 1, 1, 31, 183.06, 32.64, 1, 2, 31, 9.79, -1.76, 0.99883, 35, -146.84, 11.41, 0.00117, 1, 31, 181.6, 0.2, 1, 1, 31, 144.45, -0.22, 1, 2, 31, 102.78, -0.7, 1, 35, -238.19, 28.84, 0, 2, 31, 57.22, -1.22, 0.99986, 35, -193.43, 20.3, 0.00014, 1, 31, 230.37, 0.13, 1, 1, 31, 288.7, 0.41, 1, 2, 31, 305.87, 0.01, 0.46659, 32, 0.42, 0.73, 0.53341, 1, 32, 18.18, 8.46, 1, 2, 32, 58.72, 4.94, 0.99425, 34, -57.12, 5.64, 0.00575, 2, 32, 73.92, 1.72, 0.99255, 34, -43.91, -2.54, 0.00745, 3, 32, 99.17, -0.49, 0.22771, 33, 5.15, 1.5, 0.74441, 34, -20.9, -13.17, 0.02788, 2, 33, 33.15, 0.91, 0.99064, 34, -0.25, -32.09, 0.00936, 2, 33, 53.3, -0.1, 0.99978, 34, 14.22, -46.14, 0.00022, 1, 33, 75.43, 3.88, 1, 1, 34, 50.07, -0.25, 1, 2, 32, 140.63, 28.29, 0, 34, 27.86, -0.14, 1, 2, 33, 15.34, 27.98, 0.00075, 34, 4.23, 0, 0.99925, 3, 32, 104.21, 18.59, 0.18538, 33, 2.86, 21.11, 0.09801, 34, -9.69, 3.08, 0.71662, 3, 32, 90.44, 19.75, 0.56989, 33, -10.38, 17.14, 0.04425, 34, -22.26, 8.83, 0.38586, 2, 32, 69.86, 15.32, 0.92508, 34, -43.12, 11.63, 0.07492, 2, 32, 53.69, 15.83, 0.98589, 34, -58.16, 17.59, 0.01411, 1, 31, 146.24, -23.79, 1, 2, 31, 101.17, -40.45, 0.99881, 35, -228.71, 67.48, 0.00119, 2, 31, 40.38, -51.9, 0.96383, 35, -166.86, 66.64, 0.03617, 3, 31, -15.07, -42.68, 0.81296, 38, -61.7, 67.3, 0.03565, 35, -114.35, 46.58, 0.15139, 3, 31, -49.63, -2.67, 0.40703, 38, -24.83, 29.41, 0.45947, 35, -88.42, 0.51, 0.1335, 3, 31, -109.34, -6.04, 0.0263, 38, 34.57, 36.3, 0.43575, 35, -29.23, -8.06, 0.53795, 3, 31, -54.21, -44.97, 0.48221, 38, -22.77, 71.91, 0.14338, 35, -75.53, 41.05, 0.3744, 1, 31, 147.01, 26.25, 1, 2, 31, 115.48, 37.13, 0.99729, 38, -187.31, -20.08, 0.00271, 2, 31, 66.61, 42.55, 0.96118, 38, -138.21, -22.61, 0.03882, 3, 31, 23.21, 59.29, 0.57903, 38, -93.89, -36.75, 0.14097, 30, 205.57, -32.92, 0.28, 3, 31, -18.49, 57.54, 0.36755, 38, -52.36, -32.54, 0.31245, 30, 247.2, -29.99, 0.32, 3, 31, -57.06, 37.56, 0.14548, 38, -15.05, -10.31, 0.82252, 30, 285.19, -8.93, 0.032, 3, 31, -108.91, 17.27, 0.00373, 38, 35.52, 13.01, 0.86356, 35, -34.28, -30.81, 0.13271, 2, 38, 80.41, 50.52, 0.05624, 35, 18.72, -6.06, 0.94376, 2, 38, 120.57, 58.41, 0.07147, 35, 59.57, -8.72, 0.92853, 4, 38, 199.96, 73.97, 0.03412, 35, 140.29, -14.02, 0.83124, 36, -13.82, -18.54, 0.08294, 39, -5.77, 74.11, 0.05169, 2, 36, 74.73, -11.05, 0.94067, 39, 80.52, 95.37, 0.05933, 4, 36, 149.78, -28.55, 0.34631, 39, 157.38, 89.83, 0.06453, 40, -39.02, 82.94, 0.03074, 37, 17.08, -22.92, 0.55842, 4, 36, 197.02, -79.84, 0.00809, 39, 212.07, 46.57, 0.01289, 40, 30.68, 80.88, 0.21899, 37, 86.14, -32.51, 0.76003, 4, 36, 157.67, -128.27, 0.00228, 39, 180.78, -7.42, 0.00163, 40, 37.82, 18.89, 0.89654, 37, 86.53, -94.91, 0.09955, 4, 36, 104.82, -90.47, 0.10793, 39, 122.67, 21.64, 0.83511, 40, -26.15, 7.51, 0.02301, 37, 21.71, -99.3, 0.03395, 3, 35, 177.52, -83.46, 0.01472, 36, 41.09, -75.05, 0.18432, 39, 57.31, 26.89, 0.80096, 4, 38, 184.65, 25.24, 0.5571, 35, 113.01, -57.2, 0.24606, 36, -28.16, -67.56, 0.02015, 39, -12.26, 23.45, 0.17669, 2, 38, 116.64, 27.34, 0.59425, 35, 47.81, -37.75, 0.40575, 2, 38, 87.49, 21.52, 0.64877, 35, 18.14, -35.9, 0.35123], "hull": 64, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 80, 82, 82, 84, 84, 86, 86, 88, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 0, 126, 132, 130, 134, 132, 128, 136, 136, 134, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 20, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 172, 174, 174, 176, 176, 178, 128, 180, 180, 182, 178, 184, 184, 182, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 182, 200, 76, 78, 78, 80, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 94, 88, 90, 90, 92, 96, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 198], "width": 472, "height": 932}}, "zfy013": {"zfy013": {"type": "mesh", "uvs": [0.49579, 0.08369, 0.52121, 0.04492, 0.61302, 0.01375, 0.70237, 0.00235, 0.76959, 0.05176, 0.7991, 0.12474, 0.79336, 0.18935, 0.78025, 0.27449, 0.76057, 0.35735, 0.73024, 0.43337, 0.74321, 0.5137, 0.75879, 0.62679, 0.77621, 0.67185, 0.85874, 0.73137, 0.94951, 0.77559, 1, 0.79514, 1, 0.89208, 1, 0.98051, 0.97244, 1, 0.90367, 1, 0.78082, 0.98015, 0.65057, 0.92105, 0.55358, 0.80926, 0.48707, 0.69105, 0.42611, 0.56257, 0.37761, 0.43408, 0.30972, 0.3043, 0.23653, 0.29278, 0.17646, 0.28031, 0.13163, 0.31356, 0.10205, 0.35264, 0.05364, 0.38091, 0.00654, 0.38679, 0, 0.34197, 0.00493, 0.29938, 0.02668, 0.26801, 0.08872, 0.24186, 0.14028, 0.22243, 0.17009, 0.206, 0.24582, 0.19927, 0.29174, 0.19853, 0.37795, 0.18881, 0.42226, 0.17985, 0.44562, 0.14175, 0.67232, 0.07445, 0.66346, 0.16071, 0.55272, 0.20042, 0.43608, 0.24149, 0.33273, 0.2456, 0.23675, 0.24423, 0.15998, 0.25381, 0.07139, 0.28804, 0.03743, 0.32638, 0.033, 0.3565, 0.66641, 0.23602, 0.62655, 0.34144, 0.60145, 0.48247, 0.61621, 0.61528, 0.67084, 0.72071, 0.77124, 0.82066, 0.90708, 0.88775, 0.45675, 0.37567, 0.50547, 0.51122, 0.5601, 0.65088, 0.63245, 0.76041], "triangles": [17, 18, 60, 18, 19, 60, 19, 20, 60, 60, 16, 17, 20, 59, 60, 60, 15, 16, 60, 14, 15, 60, 13, 14, 21, 59, 20, 22, 64, 21, 21, 64, 59, 59, 13, 60, 64, 58, 59, 59, 12, 13, 59, 58, 12, 64, 23, 63, 64, 22, 23, 64, 63, 58, 63, 57, 58, 58, 11, 12, 58, 57, 11, 23, 24, 63, 63, 62, 57, 57, 10, 11, 24, 62, 63, 62, 56, 57, 57, 56, 10, 24, 25, 62, 56, 9, 10, 25, 61, 62, 62, 61, 56, 56, 55, 9, 56, 61, 55, 9, 55, 8, 7, 8, 54, 8, 55, 54, 7, 54, 6, 32, 53, 31, 32, 33, 53, 31, 53, 30, 53, 52, 30, 53, 33, 52, 52, 51, 30, 33, 34, 52, 34, 35, 52, 52, 35, 51, 30, 51, 29, 29, 50, 28, 29, 51, 50, 50, 36, 37, 50, 51, 36, 51, 35, 36, 28, 50, 49, 50, 38, 49, 50, 37, 38, 6, 45, 5, 5, 45, 44, 5, 44, 4, 44, 0, 1, 44, 1, 2, 44, 3, 4, 44, 2, 3, 25, 26, 61, 47, 26, 48, 26, 47, 61, 61, 46, 55, 61, 47, 46, 55, 46, 54, 48, 26, 49, 26, 27, 49, 27, 28, 49, 48, 49, 40, 49, 39, 40, 48, 41, 47, 48, 40, 41, 49, 38, 39, 41, 42, 47, 47, 42, 46, 46, 45, 54, 54, 45, 6, 42, 43, 46, 43, 0, 46, 46, 0, 45, 45, 0, 44], "vertices": [2, 42, 95.69, -72.73, 0.69346, 41, 118.85, -97.56, 0.30654, 2, 42, 74.98, -96.08, 0.45993, 41, 99.87, -72.77, 0.54007, 2, 42, 13.52, -106.74, 0.00956, 41, 101.15, -10.41, 0.99044, 2, 42, -43.97, -104.29, 0.00139, 41, 114.53, 45.56, 0.99861, 2, 42, -80.11, -63.19, 0.00022, 41, 161.77, 73.19, 0.99978, 2, 41, 215.38, 72.51, 0.95612, 45, -122.48, 57.63, 0.04388, 3, 42, -78.15, 32.66, 0.00164, 41, 255.48, 52.97, 0.75441, 45, -77.89, 56.57, 0.24395, 3, 42, -59.47, 88.8, 0.00307, 41, 307.03, 23.92, 0.20928, 45, -18.93, 51.63, 0.78765, 2, 41, 355.59, -8.47, 0.00383, 45, 38.72, 42.42, 0.99617, 1, 45, 92.06, 26.15, 1, 2, 45, 146.75, 37.63, 0.93251, 46, -23.23, 51.79, 0.06749, 2, 45, 223.84, 52.09, 0.01641, 46, 52.98, 33.22, 0.98359, 2, 46, 85.91, 32.5, 0.99896, 47, -69.31, 92.17, 0.00104, 2, 46, 142.99, 67, 0.74026, 47, -3, 84.95, 0.25974, 2, 46, 192.13, 110.18, 0.16543, 47, 62.26, 89.47, 0.83457, 2, 46, 216.23, 135.44, 0.06922, 47, 96.74, 94.92, 0.93078, 2, 46, 278.5, 111.56, 0.00284, 47, 131.75, 38.16, 0.99716, 2, 46, 335.3, 89.78, 0, 47, 163.69, -13.62, 1, 2, 46, 341.53, 68.56, 0, 47, 155.76, -34.26, 1, 2, 46, 325.82, 27.59, 0, 47, 118.42, -57.3, 1, 2, 46, 285.01, -40.7, 0.03832, 47, 44.54, -86.82, 0.96168, 2, 46, 217.29, -103.73, 0.62755, 47, -47.53, -95.84, 0.37245, 4, 42, 148.44, 425.04, 0.00016, 45, 356.8, -71.29, 0.01612, 46, 123.32, -133.97, 0.96735, 47, -140.57, -62.87, 0.01637, 3, 42, 175.69, 337.45, 0.0212, 45, 278.09, -118.39, 0.24206, 46, 32.2, -144.47, 0.73674, 3, 42, 198.19, 243.53, 0.15277, 45, 192.1, -162.37, 0.62193, 46, -64.27, -149.14, 0.2253, 3, 42, 212.87, 151.03, 0.48543, 45, 105.66, -198.41, 0.49462, 46, -157.89, -146.38, 0.01995, 2, 42, 239.57, 55.45, 0.93239, 45, 19.05, -246.86, 0.06761, 3, 42, 284.1, 39.32, 0.95411, 43, -14.45, 42.8, 0.04322, 45, 13.86, -293.94, 0.00266, 3, 42, 320.28, 24.04, 0.10933, 43, 18.21, 20.99, 0.88906, 44, -31.14, 66.81, 0.00161, 2, 43, 53.13, 32.02, 0.81105, 44, 0.27, 47.98, 0.18895, 2, 43, 80.43, 50.3, 0.25924, 44, 32.05, 39.67, 0.74076, 2, 43, 116.25, 57.31, 0.00664, 44, 61.05, 17.5, 0.99336, 2, 43, 145.73, 50.26, 6e-05, 44, 75.33, -9.24, 0.99994, 1, 44, 47.88, -23.91, 1, 2, 43, 125.02, -6.21, 0.00571, 44, 19.32, -31.18, 0.99429, 2, 43, 104.29, -21.35, 0.33601, 44, -5.75, -25.7, 0.66399, 1, 43, 60.89, -23.86, 1, 2, 42, 335.89, -19.26, 0.00982, 43, 25.39, -24.47, 0.99018, 2, 42, 315.16, -26.99, 0.31729, 43, 3.57, -28.17, 0.68271, 1, 42, 266.8, -22.93, 1, 1, 42, 237.88, -18.21, 1, 1, 42, 182.57, -14.97, 1, 2, 42, 153.65, -16, 0.99845, 41, 163.47, -165.28, 0.00155, 2, 42, 134.31, -39.14, 0.96354, 41, 144.45, -141.88, 0.03646, 1, 41, 153.79, 9.7, 1, 2, 42, -0.12, -1.51, 0.97435, 41, 207.04, -17.11, 0.02565, 2, 42, 74.27, 12.77, 0.96649, 45, -61.34, -96.26, 0.03351, 2, 42, 152.54, 27.3, 0.946, 45, -28.79, -168.9, 0.054, 2, 42, 217.92, 18.32, 0.98371, 45, -22.13, -234.56, 0.01629, 2, 42, 278, 6.48, 0.99974, 45, -19.5, -295.74, 0.00026, 2, 43, 21.45, 0.19, 0.99992, 44, -44.56, 50.6, 8e-05, 2, 43, 82.66, 1.79, 0.8019, 44, -2.78, 5.84, 0.1981, 1, 44, 29.5, -5.27, 1, 1, 44, 49.91, -0.69, 1, 3, 42, 7.27, 49.8, 0.49517, 41, 256, -34.15, 0.07114, 45, -41.12, -22.42, 0.43368, 2, 42, 45.23, 116.64, 0.18513, 45, 32.77, -43.58, 0.81487, 3, 42, 78.29, 209.25, 0.06687, 45, 130.57, -53.91, 0.91408, 46, -75.67, -24.96, 0.01905, 3, 42, 85.32, 300.84, 0.01088, 45, 221.24, -39.18, 0.27185, 46, 13.02, -48.88, 0.71728, 3, 42, 63.96, 378.43, 4e-05, 45, 291.62, -0.16, 0.00276, 46, 93.22, -42.31, 0.9972, 1, 46, 180.36, -7.12, 1, 2, 46, 254.49, 57.27, 0.00421, 47, 79.73, 9.58, 0.99579, 3, 42, 156.03, 120.49, 0.53649, 45, 62.6, -150.35, 0.45955, 46, -177.33, -84.85, 0.00396, 3, 42, 142.07, 217.8, 0.16095, 45, 153.89, -113.88, 0.71272, 46, -79.12, -89.21, 0.12633, 3, 42, 124.91, 318.56, 0.01869, 45, 247.78, -73.49, 0.25441, 46, 23.07, -91.07, 0.7269, 4, 42, 92.93, 400.94, 0.00011, 45, 320.32, -23.02, 0.00889, 46, 109.96, -74.96, 0.98618, 47, -115.39, -7.84, 0.00483], "hull": 44, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 0, 86, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 94, 122, 122, 124, 124, 126, 126, 128], "width": 638, "height": 688}}, "zfy014": {"zfy014": {"type": "mesh", "uvs": [0.05806, 0.07344, 0.1499, 0.03058, 0.26768, 0, 0.40598, 0, 0.51403, 0.0333, 0.57672, 0.11912, 0.61108, 0.19961, 0.68256, 0.31126, 0.77329, 0.44974, 0.84477, 0.59947, 0.90388, 0.74574, 0.94699, 0.87972, 1, 1, 0.89894, 1, 0.80404, 1, 0.72837, 0.89862, 0.62266, 0.7761, 0.49893, 0.62937, 0.39071, 0.49278, 0.33187, 0.36682, 0.30665, 0.27791, 0.28311, 0.26415, 0.25285, 0.25674, 0.1772, 0.27897, 0.0797, 0.2959, 0, 0.26732, 0, 0.15724, 0, 0.11808, 0.10325, 0.17205, 0.26883, 0.12121, 0.43578, 0.10915, 0.49189, 0.23839, 0.55894, 0.3435, 0.62462, 0.47533, 0.71631, 0.62352, 0.77652, 0.75966, 0.85178, 0.9001], "triangles": [30, 3, 4, 30, 4, 5, 31, 30, 5, 31, 5, 6, 21, 30, 31, 20, 21, 31, 31, 6, 7, 32, 31, 7, 19, 20, 31, 19, 31, 32, 33, 32, 7, 8, 33, 7, 18, 19, 32, 18, 32, 33, 34, 33, 8, 34, 8, 9, 17, 18, 33, 17, 33, 34, 35, 34, 9, 10, 35, 9, 16, 17, 34, 16, 34, 35, 15, 16, 35, 36, 35, 10, 36, 15, 35, 10, 11, 36, 14, 15, 36, 13, 36, 11, 14, 36, 13, 13, 11, 12, 29, 2, 3, 29, 3, 30, 1, 2, 29, 28, 26, 27, 29, 28, 0, 29, 0, 1, 28, 27, 0, 22, 28, 29, 21, 29, 30, 22, 29, 21, 25, 26, 28, 28, 24, 25, 22, 23, 28, 23, 24, 28], "vertices": [1, 49, 111.51, 63.65, 1, 1, 49, 148.43, 63.69, 1, 1, 49, 188.34, 53.67, 1, 2, 49, 224.26, 27.1, 0.96724, 50, -54.16, 6.76, 0.03276, 2, 49, 242.15, -7.39, 0.44319, 50, -26.96, 34.51, 0.55681, 2, 49, 232.25, -54.82, 0.00049, 50, 21.2, 39.87, 0.99951, 1, 50, 63.88, 37.42, 1, 1, 50, 125.51, 41.3, 1, 1, 50, 202.17, 46.76, 1, 1, 50, 282.34, 44.51, 1, 1, 50, 359.57, 39.01, 1, 1, 50, 429.36, 31.11, 1, 1, 50, 543.75, 21.93, 1, 1, 50, 597.12, -29.59, 1, 1, 50, 597.32, -42.66, 1, 1, 50, 439.86, -51.04, 1, 1, 50, 350.86, -58.87, 1, 1, 50, 261.75, -66.35, 1, 1, 50, 184.24, -77.48, 1, 2, 49, 93.09, -109.94, 0.04367, 50, 116.92, -75.19, 0.95633, 2, 49, 113.67, -68.43, 0.33924, 50, 71.06, -68.56, 0.66076, 2, 49, 111.76, -58.23, 0.53257, 50, 61.97, -73.56, 0.46743, 2, 49, 106.16, -49.36, 0.73284, 50, 55.29, -81.64, 0.26716, 2, 49, 79.74, -44, 0.95272, 50, 58.42, -108.42, 0.04728, 2, 49, 49.25, -32.25, 0.99832, 50, 56.76, -141.05, 0.00168, 1, 49, 37.28, -5.15, 1, 1, 49, 70.86, 40.24, 1, 1, 49, 82.81, 56.4, 1, 1, 49, 93.16, 14.3, 1, 1, 49, 151.66, 3.46, 1, 2, 49, 198.69, -23.64, 0.01065, 50, 2.01, -1.73, 0.98935, 2, 49, 173.83, -87.71, 0.00839, 50, 70.65, -5.39, 0.99161, 2, 49, 159.17, -143.94, 0.00011, 50, 128.65, -1.8, 0.99989, 1, 50, 199.52, -2.95, 1, 1, 50, 281, 1.24, 1, 1, 50, 353.4, -2.28, 1, 1, 50, 429.44, -1.88, 1], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 50, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72], "width": 323, "height": 513}}, "zfy015": {"zfy015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [34.26, -159.11, -26.38, 25.17, 253.84, 117.37, 314.48, -66.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 295}}}}], "animations": {"animation1": {"slots": {"sm": {"color": [{"time": 5.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 6.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "color": "ffffff00"}]}, "zfy015": {"attachment": [{"name": null}, {"time": 5.3333, "name": "zfy015"}]}}, "bones": {"bone2": {"translate": [{"y": 0.38, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "y": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "y": 0.38, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "y": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "y": 0.38, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.1667, "y": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.4333, "y": 0.38}]}, "bone3": {"translate": [{"x": 0.04, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 3.48, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.4, "x": 0.64, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 2.6667, "x": 0.04, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": 3.48, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.0667, "x": 0.64, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 5.3333, "x": 0.04, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "x": 3.48, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.2333, "x": 0.64, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 7.4333, "x": 0.04}]}, "bone4": {"translate": [{"x": 0.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 2.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4, "x": 1.19, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 2.6667, "x": 0.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "x": 2.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.0667, "x": 1.19, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 5.3333, "x": 0.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.6333, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": 2.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.2333, "x": 1.19, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 7.4333, "x": 0.57}]}, "bone5": {"translate": [{"x": 7.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 15.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 7.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 15.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 7.96, "curve": 0.341, "c2": 0.36, "c3": 0.682, "c4": 0.71}, {"time": 6.2667, "x": 23.76, "y": 5.2, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 7.4333, "x": 7.96}]}, "bone9": {"rotate": [{"angle": 1.39, "curve": 0.346, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.3, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 3.23, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "angle": 1.39, "curve": 0.346, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 2.9667, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": 3.23, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 5.3333, "angle": 1.39, "curve": 0.346, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 5.5667, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 5.8, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "angle": 3.23, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 7.4333, "angle": 1.39}]}, "bone10": {"rotate": [{"angle": 2.55, "curve": 0.318, "c2": 0.29, "c3": 0.659, "c4": 0.64}, {"time": 0.3, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "angle": 2.55, "curve": 0.318, "c2": 0.29, "c3": 0.659, "c4": 0.64}, {"time": 2.9667, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "angle": 2.55, "curve": 0.318, "c2": 0.29, "c3": 0.659, "c4": 0.64}, {"time": 5.5667, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 7.4333, "angle": 2.55}]}, "bone11": {"rotate": [{"angle": 3.2, "curve": 0.289, "c2": 0.12, "c3": 0.631, "c4": 0.5}, {"time": 0.3, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 3.23, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": 3.2, "curve": 0.289, "c2": 0.12, "c3": 0.631, "c4": 0.5}, {"time": 2.9667, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 3.23, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 5.3333, "angle": 3.2, "curve": 0.289, "c2": 0.12, "c3": 0.631, "c4": 0.5}, {"time": 5.5667, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 3.23, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 7.4333, "angle": 3.2}]}, "bone12": {"rotate": [{"angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 3.23, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 3.23, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.3333, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 5.5667, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 3.23, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 7.4333, "angle": 0.51}]}, "bone13": {"rotate": [{"angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": 3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4333, "angle": 1.62}]}, "bone14": {"rotate": [{"angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 7.4333, "angle": 2.55}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 7.4333}]}, "bone16": {"rotate": [{"angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "angle": 0.6}]}, "bone17": {"rotate": [{"angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 7.4333, "angle": 1.51}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 7.4333}]}, "bone19": {"rotate": [{"angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "angle": 0.6}]}, "bone20": {"rotate": [{"angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 7.4333, "angle": 1.51}]}, "bone22": {"rotate": [{"angle": -0.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -11.31, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -0.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -11.31, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 5.3333, "angle": -0.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -11.31, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 7.4333, "angle": -0.94}]}, "bone23": {"rotate": [{"angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -11.31, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -11.31, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 5.3333, "angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "angle": -11.31, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 7.4333, "angle": -3.09}]}, "bone24": {"rotate": [{"angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.8333, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 6.2667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 7.4333, "angle": -0.62}]}, "bone25": {"rotate": [{"angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 5.3333, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 7.4333, "angle": -0.82}]}, "bone26": {"rotate": [{"angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.8333, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 6.2667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 7.4333, "angle": -0.62}]}, "bone27": {"rotate": [{"angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 5.3333, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 7.4333, "angle": -0.82}]}, "bone28": {"translate": [{"x": 0.4, "y": -0.77, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 3.94, "y": -7.55, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.4, "x": 1.32, "y": -2.54, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 2.6667, "x": 0.4, "y": -0.77, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 3.94, "y": -7.55, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.0667, "x": 1.32, "y": -2.54, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 5.3333, "x": 0.4, "y": -0.77, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": 3.94, "y": -7.55, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 7.2333, "x": 1.32, "y": -2.54, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 7.4333, "x": 0.4, "y": -0.77}]}, "bone29": {"translate": [{"x": 0.67, "y": 0.98, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 6.54, "y": 9.53, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.4, "x": 2.2, "y": 3.2, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 2.6667, "x": 0.67, "y": 0.98, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 6.54, "y": 9.53, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.0667, "x": 2.2, "y": 3.2, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 5.3333, "x": 0.67, "y": 0.98, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": 6.54, "y": 9.53, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 7.2333, "x": 2.2, "y": 3.2, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 7.4333, "x": 0.67, "y": 0.98}]}, "bone30": {"rotate": [{"angle": -2.51, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -4.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.4, "angle": -3.6, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 2.6667, "angle": -2.51, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": -4.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.0667, "angle": -3.6, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 5.3333, "angle": -2.51, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 5.9, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -4.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.2333, "angle": -3.6, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 7.4333, "angle": -2.51}]}, "bone31": {"rotate": [{"angle": -3.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -4.42, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": -3.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": -4.42, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": -3.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.1667, "curve": "stepped"}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": -4.42, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.4333, "angle": -3.84}]}, "bone32": {"rotate": [{"angle": 0.14, "curve": 0.261, "c2": 0.08, "c3": 0.672, "c4": 0.69}, {"time": 0.9333, "angle": 8.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 1.2, "angle": 10.22, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.2667, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": 0.14, "curve": 0.261, "c2": 0.08, "c3": 0.672, "c4": 0.69}, {"time": 3.6, "angle": 8.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 3.8667, "angle": 10.22, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 3.9333, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 5.3333, "angle": 0.14, "curve": 0.261, "c2": 0.08, "c3": 0.672, "c4": 0.69}, {"time": 6.0667, "angle": 8.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 6.2667, "angle": 10.22, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 6.3333, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 7.4333, "angle": 0.14}]}, "bone33": {"rotate": [{"angle": 1.35, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": 5.17, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 1.2, "angle": 7.84, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.6, "angle": 10.34, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": 1.35, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6, "angle": 5.17, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 3.8667, "angle": 7.84, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.2667, "angle": 10.34, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.3333, "angle": 1.35, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.5333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.0667, "angle": -3.93, "curve": "stepped"}, {"time": 6.6, "angle": -3.93, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 7.4333, "angle": 1.35}]}, "bone34": {"rotate": [{"angle": -16.39, "curve": "stepped"}, {"time": 5.6, "angle": -16.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.2667, "angle": 13.86, "curve": "stepped"}, {"time": 6.6, "angle": 13.86, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 7.4333, "angle": -16.39}]}, "bone35": {"rotate": [{"angle": -13.95, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 0.2667, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.7667, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.4333, "angle": -15.5, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 2.6667, "angle": -13.95, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 2.9333, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.4333, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.1, "angle": -15.5, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 5.3333, "angle": -13.95, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 5.5333, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 5.9333, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.2, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.2333, "angle": -15.5, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 7.4333, "angle": -13.95}], "translate": [{"x": 0.32, "y": -1.36, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 0.2667, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.7667, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 0.36, "y": -1.52, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 2.6667, "x": 0.32, "y": -1.36, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 2.9333, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.4333, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "x": 0.36, "y": -1.52, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 5.3333, "x": 0.32, "y": -1.36, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 5.5333, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 5.9333, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "x": 0.36, "y": -1.52, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 7.4333, "x": 0.32, "y": -1.36}]}, "bone36": {"rotate": [{"angle": -15.21, "curve": 0.351, "c2": 0.65, "c3": 0.685}, {"time": 0.1, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.2667, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.7667, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4333, "angle": -0.57, "curve": 0.315, "c2": 0.27, "c3": 0.735, "c4": 0.91}, {"time": 2.6667, "angle": -15.21, "curve": 0.351, "c2": 0.65, "c3": 0.685}, {"time": 2.7667, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.9333, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 3.4333, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1, "angle": -0.57, "curve": 0.315, "c2": 0.27, "c3": 0.735, "c4": 0.91}, {"time": 5.3333, "angle": -15.21, "curve": 0.351, "c2": 0.65, "c3": 0.685}, {"time": 5.4, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.5333, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 5.9333, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.4667, "angle": -0.57, "curve": 0.315, "c2": 0.27, "c3": 0.735, "c4": 0.91}, {"time": 7.4333, "angle": -15.21}]}, "bone37": {"rotate": [{"angle": -12.18, "curve": 0.358, "c2": 0.44, "c3": 0.695, "c4": 0.79}, {"time": 0.2667, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.4333, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.7667, "angle": -0.57, "curve": 0.304, "c2": 0.24, "c3": 0.682, "c4": 0.71}, {"time": 2.6667, "angle": -12.18, "curve": 0.358, "c2": 0.44, "c3": 0.695, "c4": 0.79}, {"time": 2.9333, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.1, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.4333, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.4333, "angle": -0.57, "curve": 0.304, "c2": 0.24, "c3": 0.682, "c4": 0.71}, {"time": 5.3333, "angle": -12.18, "curve": 0.358, "c2": 0.44, "c3": 0.695, "c4": 0.79}, {"time": 5.5333, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.6667, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.9333, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.7333, "angle": -0.57, "curve": 0.304, "c2": 0.24, "c3": 0.682, "c4": 0.71}, {"time": 7.4333, "angle": -12.18}]}, "bone38": {"rotate": [{"angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.1667, "angle": -15.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.1667, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.8333, "angle": -15.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 5.7333, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.0333, "angle": -15.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 7.4333, "angle": -10.44}], "translate": [{"x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.36, "y": -1.52, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.1667, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 0.36, "y": -1.52, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 5.7333, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "x": 0.36, "y": -1.52, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 7.4333, "x": 0.24, "y": -1.01}]}, "bone39": {"rotate": [{"angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.5, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.5, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 3.1667, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.8333, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.1667, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 5.7333, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.2667, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.3, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 7.4333, "angle": -14.62}]}, "bone40": {"rotate": [{"angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5, "angle": -0.57, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 2.6667, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.8333, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.1667, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.1667, "angle": -0.57, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 5.3333, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.4667, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.7333, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": -0.57, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 7.4333, "angle": -14.75}]}, "bone42": {"rotate": [{"angle": -0.7, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.7, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -3.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.7, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -3.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "angle": -0.7}]}, "bone43": {"rotate": [{"angle": -1.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": -3.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4333, "angle": -1.9}]}, "bone44": {"rotate": [{"angle": -3.1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.81, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -3.81, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -3.81, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.4333, "angle": -3.1}]}, "bone45": {"rotate": [{"angle": 3.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 3.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 7.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 3.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": 7.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4333, "angle": 3.78}]}, "bone46": {"rotate": [{"angle": 6.16, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 7.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 6.16, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 7.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 6.16, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 7.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.4333, "angle": 6.16}]}, "bone47": {"rotate": [{"angle": 7.56, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 7.56, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 7.56, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": 7.56}]}, "bone49": {"rotate": [{"angle": 0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 2.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 2.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "angle": 0.44}]}, "bone7": {"translate": [{"x": 1.05, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 3.85, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "x": 1.05, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": 3.85, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 5.3333, "x": 1.05, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "x": 3.85, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 7.4333, "x": 1.05}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 1.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": 10.65, "curve": 0.25, "c3": 0.75}, {"time": 7.4333}], "scale": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": 0.707, "curve": 0.25, "c3": 0.75}, {"time": 7.4333}]}, "bone51": {"rotate": [{"time": 5.3333, "angle": 42.42}], "translate": [{"time": 5.3333, "x": 125.73, "y": -4.21}]}, "bone59": {"translate": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 2.27}]}}, "deform": {"default": {"zfy07": {"zfy07": [{"time": 3.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "offset": 126, "vertices": [-5.09402, 1.01118, -5.09386, 1.01118, -5.11263, 0.61815, -5.11226, 0.61816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -19.33299, 0.44725, -19.33247, 0.44727, 0, 0, 0, 0, -17.76422, 2.53096, -17.76363, 2.53097, 0, 0, 0, 0, 0, 0, 0, 0, 11.07639, 0.02835, 11.07671, 0.02836, 7.85579, 0.71544, 7.85591, 0.71544, -17.08693, -5.4224, -17.08649, -5.42239, -13.01189, -3.54771, -13.01146, -3.54771, 4.61034, 0.99124, 4.6105, 0.99124, 5.99947, 1.97598, 5.99963, 1.97599, 8.1795, 2.74984, 8.17981, 2.74984, 6.08815, 1.45405, 6.08841, 1.45405, 0, 0, 0, 0, -17.08665, -0.17232, -17.08612, -0.17231, -10.92351, 0.17381, -10.92328, 0.17382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -17.23003, 3.41379, -17.22897, 3.41381, -9.64384, 5.60972, -9.64307, 5.60973, 8.15215, 0.7532, 8.15318, 0.75321, 12.54233, -2.44556, 12.54288, -2.44555, 3.01109, -3.43252, 3.01138, -3.43251, -10.05278, 1.9397, -10.05241, 1.9397, -16.43556, 1.74896, -16.43411, 1.74897, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.56331, 0.1022, -1.5632, 0.10221, -5.0276, -1.43408, -5.02647, -1.43407, -4.5639, 3.20891, -4.56319, 3.20891, -3.46663, 2.57081, -3.46622, 2.57082, -1.43095, 0.0454, -1.43088, 0.0454, -3.12357, -0.42793, -3.12337, -0.42792, -5.4115, -1.73952, -5.41112, -1.73952, -4.37656, -1.7906, -4.37645, -1.7906], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667}]}}}}, "animation2": {"slots": {"sm": {"color": [{"time": 5.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 6.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "color": "ffffff00"}]}, "zfy01": {"attachment": [{"name": null}]}, "zfy015": {"attachment": [{"name": null}, {"time": 5.3333, "name": "zfy015"}]}}, "bones": {"bone2": {"translate": [{"y": 0.38, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "y": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "y": 0.38, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "y": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "y": 0.38, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.1667, "y": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.4333, "y": 0.38}]}, "bone3": {"translate": [{"x": 0.04, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 3.48, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.4, "x": 0.64, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 2.6667, "x": 0.04, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": 3.48, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.0667, "x": 0.64, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 5.3333, "x": 0.04, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "x": 3.48, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.2333, "x": 0.64, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 7.4333, "x": 0.04}]}, "bone4": {"translate": [{"x": 0.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 2.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4, "x": 1.19, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 2.6667, "x": 0.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "x": 2.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.0667, "x": 1.19, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 5.3333, "x": 0.57, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.6333, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": 2.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.2333, "x": 1.19, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 7.4333, "x": 0.57}]}, "bone5": {"translate": [{"x": 7.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 15.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 7.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 15.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 7.96, "curve": 0.341, "c2": 0.36, "c3": 0.682, "c4": 0.71}, {"time": 6.2667, "x": 23.76, "y": 5.2, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 7.4333, "x": 7.96}]}, "bone9": {"rotate": [{"angle": 1.39, "curve": 0.346, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.3, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 3.23, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "angle": 1.39, "curve": 0.346, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 2.9667, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": 3.23, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 5.3333, "angle": 1.39, "curve": 0.346, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 5.5667, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 5.8, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "angle": 3.23, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 7.4333, "angle": 1.39}]}, "bone10": {"rotate": [{"angle": 2.55, "curve": 0.318, "c2": 0.29, "c3": 0.659, "c4": 0.64}, {"time": 0.3, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "angle": 2.55, "curve": 0.318, "c2": 0.29, "c3": 0.659, "c4": 0.64}, {"time": 2.9667, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "angle": 2.55, "curve": 0.318, "c2": 0.29, "c3": 0.659, "c4": 0.64}, {"time": 5.5667, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 7.4333, "angle": 2.55}]}, "bone11": {"rotate": [{"angle": 3.2, "curve": 0.289, "c2": 0.12, "c3": 0.631, "c4": 0.5}, {"time": 0.3, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 3.23, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": 3.2, "curve": 0.289, "c2": 0.12, "c3": 0.631, "c4": 0.5}, {"time": 2.9667, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 3.23, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 5.3333, "angle": 3.2, "curve": 0.289, "c2": 0.12, "c3": 0.631, "c4": 0.5}, {"time": 5.5667, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 3.23, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 7.4333, "angle": 3.2}]}, "bone12": {"rotate": [{"angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 3.23, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 3.23, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.3333, "angle": 0.51, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 5.5667, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 3.23, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 7.4333, "angle": 0.51}]}, "bone13": {"rotate": [{"angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": 3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4333, "angle": 1.62}]}, "bone14": {"rotate": [{"angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "angle": 2.55, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 3.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 7.4333, "angle": 2.55}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 7.4333}]}, "bone16": {"rotate": [{"angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "angle": 0.6}]}, "bone17": {"rotate": [{"angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 7.4333, "angle": 1.51}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 7.4333}]}, "bone19": {"rotate": [{"angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 3.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "angle": 0.6}]}, "bone20": {"rotate": [{"angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "angle": 1.51, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.8667, "angle": 3.23, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 7.4333, "angle": 1.51}]}, "bone22": {"rotate": [{"angle": -0.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -11.31, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -0.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -11.31, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 5.3333, "angle": -0.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -11.31, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 7.4333, "angle": -0.94}]}, "bone23": {"rotate": [{"angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -11.31, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -11.31, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 5.3333, "angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "angle": -11.31, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 7.4333, "angle": -3.09}]}, "bone24": {"rotate": [{"angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.8333, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 6.2667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 7.4333, "angle": -0.62}]}, "bone25": {"rotate": [{"angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 5.3333, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 7.4333, "angle": -0.82}]}, "bone26": {"rotate": [{"angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.8333, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "angle": -0.62, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 6.2667, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 7.4333, "angle": -0.62}]}, "bone27": {"rotate": [{"angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 5.3333, "angle": -0.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -9.88, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 7.4333, "angle": -0.82}]}, "bone28": {"translate": [{"x": 0.4, "y": -0.77, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 3.94, "y": -7.55, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.4, "x": 1.32, "y": -2.54, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 2.6667, "x": 0.4, "y": -0.77, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 3.94, "y": -7.55, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.0667, "x": 1.32, "y": -2.54, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 5.3333, "x": 0.4, "y": -0.77, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": 3.94, "y": -7.55, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 7.2333, "x": 1.32, "y": -2.54, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 7.4333, "x": 0.4, "y": -0.77}]}, "bone29": {"translate": [{"x": 0.67, "y": 0.98, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 6.54, "y": 9.53, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.4, "x": 2.2, "y": 3.2, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 2.6667, "x": 0.67, "y": 0.98, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": 6.54, "y": 9.53, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.0667, "x": 2.2, "y": 3.2, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 5.3333, "x": 0.67, "y": 0.98, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 5.5333, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "x": 6.54, "y": 9.53, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 7.2333, "x": 2.2, "y": 3.2, "curve": 0.352, "c2": 0.4, "c3": 0.691, "c4": 0.75}, {"time": 7.4333, "x": 0.67, "y": 0.98}]}, "bone30": {"rotate": [{"angle": -2.51, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -4.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.4, "angle": -3.6, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 2.6667, "angle": -2.51, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": -4.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.0667, "angle": -3.6, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 5.3333, "angle": -2.51, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 5.9, "curve": 0.25, "c3": 0.75}, {"time": 6.9667, "angle": -4.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.2333, "angle": -3.6, "curve": 0.316, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 7.4333, "angle": -2.51}]}, "bone31": {"rotate": [{"angle": -3.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -4.42, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": -3.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": -4.42, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": -3.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.1667, "curve": "stepped"}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": -4.42, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.4333, "angle": -3.84}]}, "bone32": {"rotate": [{"angle": 0.14, "curve": 0.261, "c2": 0.08, "c3": 0.672, "c4": 0.69}, {"time": 0.9333, "angle": 8.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 1.2, "angle": 10.22, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.2667, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": 0.14, "curve": 0.261, "c2": 0.08, "c3": 0.672, "c4": 0.69}, {"time": 3.6, "angle": 8.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 3.8667, "angle": 10.22, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 3.9333, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 5.3333, "angle": 0.14, "curve": 0.261, "c2": 0.08, "c3": 0.672, "c4": 0.69}, {"time": 6.0667, "angle": 8.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 6.2667, "angle": 10.22, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 6.3333, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 7.4333, "angle": 0.14}]}, "bone33": {"rotate": [{"angle": 1.35, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": 5.17, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 1.2, "angle": 7.84, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.6, "angle": 10.34, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": 1.35, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6, "angle": 5.17, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 3.8667, "angle": 7.84, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.2667, "angle": 10.34, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.3333, "angle": 1.35, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.5333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.0667, "angle": -3.93, "curve": "stepped"}, {"time": 6.6, "angle": -3.93, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 7.4333, "angle": 1.35}]}, "bone34": {"rotate": [{"angle": -16.39, "curve": "stepped"}, {"time": 5.6, "angle": -16.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.2667, "angle": 13.86, "curve": "stepped"}, {"time": 6.6, "angle": 13.86, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 7.4333, "angle": -16.39}]}, "bone35": {"rotate": [{"angle": -13.95, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 0.2667, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.7667, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.4333, "angle": -15.5, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 2.6667, "angle": -13.95, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 2.9333, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.4333, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.1, "angle": -15.5, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 5.3333, "angle": -13.95, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 5.5333, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 5.9333, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.2, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.2333, "angle": -15.5, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 7.4333, "angle": -13.95}], "translate": [{"x": 0.32, "y": -1.36, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 0.2667, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.7667, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 0.36, "y": -1.52, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 2.6667, "x": 0.32, "y": -1.36, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 2.9333, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.4333, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "x": 0.36, "y": -1.52, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 5.3333, "x": 0.32, "y": -1.36, "curve": 0.309, "c2": 0.25, "c3": 0.648, "c4": 0.6}, {"time": 5.5333, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 5.9333, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "x": 0.36, "y": -1.52, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 7.4333, "x": 0.32, "y": -1.36}]}, "bone36": {"rotate": [{"angle": -15.21, "curve": 0.351, "c2": 0.65, "c3": 0.685}, {"time": 0.1, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.2667, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.7667, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4333, "angle": -0.57, "curve": 0.315, "c2": 0.27, "c3": 0.735, "c4": 0.91}, {"time": 2.6667, "angle": -15.21, "curve": 0.351, "c2": 0.65, "c3": 0.685}, {"time": 2.7667, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.9333, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 3.4333, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1, "angle": -0.57, "curve": 0.315, "c2": 0.27, "c3": 0.735, "c4": 0.91}, {"time": 5.3333, "angle": -15.21, "curve": 0.351, "c2": 0.65, "c3": 0.685}, {"time": 5.4, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.5333, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 5.9333, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.4667, "angle": -0.57, "curve": 0.315, "c2": 0.27, "c3": 0.735, "c4": 0.91}, {"time": 7.4333, "angle": -15.21}]}, "bone37": {"rotate": [{"angle": -12.18, "curve": 0.358, "c2": 0.44, "c3": 0.695, "c4": 0.79}, {"time": 0.2667, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.4333, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.7667, "angle": -0.57, "curve": 0.304, "c2": 0.24, "c3": 0.682, "c4": 0.71}, {"time": 2.6667, "angle": -12.18, "curve": 0.358, "c2": 0.44, "c3": 0.695, "c4": 0.79}, {"time": 2.9333, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.1, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.4333, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.4333, "angle": -0.57, "curve": 0.304, "c2": 0.24, "c3": 0.682, "c4": 0.71}, {"time": 5.3333, "angle": -12.18, "curve": 0.358, "c2": 0.44, "c3": 0.695, "c4": 0.79}, {"time": 5.5333, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.6667, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.9333, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.7333, "angle": -0.57, "curve": 0.304, "c2": 0.24, "c3": 0.682, "c4": 0.71}, {"time": 7.4333, "angle": -12.18}]}, "bone38": {"rotate": [{"angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.1667, "angle": -15.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.1667, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.8333, "angle": -15.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -10.44, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 5.7333, "angle": -3.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.0333, "angle": -15.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 7.4333, "angle": -10.44}], "translate": [{"x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.36, "y": -1.52, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 3.1667, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 0.36, "y": -1.52, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 0.24, "y": -1.01, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 5.7333, "x": 0.07, "y": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "x": 0.36, "y": -1.52, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 7.4333, "x": 0.24, "y": -1.01}]}, "bone39": {"rotate": [{"angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.5, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.5, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 3.1667, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.8333, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.1667, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "angle": -14.62, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 5.7333, "angle": -8.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.2667, "angle": -0.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.3, "angle": -15.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 7.4333, "angle": -14.62}]}, "bone40": {"rotate": [{"angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5, "angle": -0.57, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 2.6667, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.8333, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.1667, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.1667, "angle": -0.57, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 5.3333, "angle": -14.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.4667, "angle": -15.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.7333, "angle": -12.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.5, "angle": -0.57, "curve": 0.311, "c2": 0.26, "c3": 0.722, "c4": 0.85}, {"time": 7.4333, "angle": -14.75}]}, "bone42": {"rotate": [{"angle": -0.7, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.7, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -3.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.7, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": -3.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "angle": -0.7}]}, "bone43": {"rotate": [{"angle": -1.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": -3.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4333, "angle": -1.9}]}, "bone44": {"rotate": [{"angle": -3.1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.81, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -3.81, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -3.81, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.4333, "angle": -3.1}]}, "bone45": {"rotate": [{"angle": 3.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 3.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 7.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 3.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.9, "angle": 7.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.4333, "angle": 3.78}]}, "bone46": {"rotate": [{"angle": 6.16, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 7.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 6.16, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 7.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 6.16, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.1333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 7.56, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.4333, "angle": 6.16}]}, "bone47": {"rotate": [{"angle": 7.56, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 7.56, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 7.56, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": 7.56}]}, "bone49": {"rotate": [{"angle": 0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 2.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 2.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 7.4333, "angle": 0.44}]}, "bone7": {"translate": [{"x": 1.05, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 3.85, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "x": 1.05, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": 3.85, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 5.3333, "x": 1.05, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "x": 3.85, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 7.4333, "x": 1.05}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 1.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": 10.65, "curve": 0.25, "c3": 0.75}, {"time": 7.4333}], "scale": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": 0.707, "curve": 0.25, "c3": 0.75}, {"time": 7.4333}]}, "bone51": {"rotate": [{"time": 5.3333, "angle": 42.42}], "translate": [{"time": 5.3333, "x": 125.73, "y": -4.21}]}, "bone59": {"translate": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 2.27}]}}, "deform": {"default": {"zfy07": {"zfy07": [{"time": 3.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "offset": 126, "vertices": [-5.09402, 1.01118, -5.09386, 1.01118, -5.11263, 0.61815, -5.11226, 0.61816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -19.33299, 0.44725, -19.33247, 0.44727, 0, 0, 0, 0, -17.76422, 2.53096, -17.76363, 2.53097, 0, 0, 0, 0, 0, 0, 0, 0, 11.07639, 0.02835, 11.07671, 0.02836, 7.85579, 0.71544, 7.85591, 0.71544, -17.08693, -5.4224, -17.08649, -5.42239, -13.01189, -3.54771, -13.01146, -3.54771, 4.61034, 0.99124, 4.6105, 0.99124, 5.99947, 1.97598, 5.99963, 1.97599, 8.1795, 2.74984, 8.17981, 2.74984, 6.08815, 1.45405, 6.08841, 1.45405, 0, 0, 0, 0, -17.08665, -0.17232, -17.08612, -0.17231, -10.92351, 0.17381, -10.92328, 0.17382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -17.23003, 3.41379, -17.22897, 3.41381, -9.64384, 5.60972, -9.64307, 5.60973, 8.15215, 0.7532, 8.15318, 0.75321, 12.54233, -2.44556, 12.54288, -2.44555, 3.01109, -3.43252, 3.01138, -3.43251, -10.05278, 1.9397, -10.05241, 1.9397, -16.43556, 1.74896, -16.43411, 1.74897, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.56331, 0.1022, -1.5632, 0.10221, -5.0276, -1.43408, -5.02647, -1.43407, -4.5639, 3.20891, -4.56319, 3.20891, -3.46663, 2.57081, -3.46622, 2.57082, -1.43095, 0.0454, -1.43088, 0.0454, -3.12357, -0.42793, -3.12337, -0.42792, -5.4115, -1.73952, -5.41112, -1.73952, -4.37656, -1.7906, -4.37645, -1.7906], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667}]}}}}}}