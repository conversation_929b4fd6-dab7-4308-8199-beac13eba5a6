import { _decorator, Component, Label, Node, Tween, tween } from "cc";
const { ccclass, property } = _decorator;

/**显示几个。 */
const maxIndex: number = 3;

@ccclass("TopStopTouch")
export class TopStopTouch extends Component {
  @property(Node)
  mask: Node = null;

  @property(Label)
  lab: Label = null;

  private _index: number = 0;
  private _dtTime: number = 0;

  protected onEnable(): void {
    tween(this.node)
      .delay(3)
      .call(() => {
        this.mask.active = true;
      })
      .start();
  }

  protected onDisable(): void {
    this.mask.active = false;
    this._index = 0;
    Tween.stopAllByTarget(this.node);
  }

  protected onLoad(): void {
    this.mask.active = false;
  }

  protected update(dt: number): void {
    if (this.mask.active != true) return;

    this._dtTime -= dt;
    if (this._dtTime <= 0) {
      if (this._index > maxIndex) {
        this._index = 0;
      }

      for (let i = 0; i <= this._index; i++) {
        if (i == 0) {
          this.lab.string = "加载中";
        } else {
          this.lab.string += ".";
        }
      }

      this._index++;
      this._dtTime = 0.2;
    }
  }
}
