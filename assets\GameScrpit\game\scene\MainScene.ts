import { _decorator, Node } from "cc";
import Scene from "../../lib/scene/scene";
import { UIMgr } from "../../lib/ui/UIMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import MsgEnum from "../event/MsgEnum";
import * as asy from "../../lib/ext/async.js";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import ResMgr from "../../lib/common/ResMgr";
import { UIGameMap } from "../ui/ui_gameMap/UIGameMap";
import { PlayerModule } from "../../module/player/PlayerModule";
import { PlayerRouteName } from "../../module/player/PlayerConstant";
import { CityRouteName } from "../../module/city/CityConstant";
import { director } from "cc";
import { HeartBeat } from "../ctrl/HeartBeat";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { UITransform } from "cc";
import { BoutStartUp } from "../BoutStartUp";
import { GameReward } from "../../ext_reward/GameReward";
import { GuideCtrl } from "../ctrl/GuideCtrl";
import { FmConfig } from "../GameDefine";
import { GuideRouteEnum } from "../../ext_guide/GuideDefine";
import PlantMgr from "../../lib/plant/PlantMgr";
const { ccclass, property } = _decorator;

@ccclass("MainScene")
export class MainScene extends Scene {
  public type(): number {
    return 2;
  }

  private interId: number = 0;

  public async onEnter(): Promise<void> {
    PlantMgr.instance.size(`${BundleEnum.BUNDLE_COMMON_UI}?prefabs/Item`, 5);

    // 添加引导组件
    this.addGuideCtrl();

    // 默认场景
    let cfg = PlayerModule.data.getConfigLeaderData(1);
    if (PlayerModule.data.getPlayerInfo().level < cfg.fudiLv) {
      UIMgr.instance.defaultPageName = CityRouteName.UIGameMap;
    } else {
      UIMgr.instance.defaultPageName = PlayerRouteName.UITerritory;
    }

    await new Promise((resolve, reject) => {
      // 测试用，直接到主界面
      if (FmConfig.jumpXiaoYouXi) {
        UIMgr.instance.showPage(UIMgr.instance.defaultPageName, {}, null, () => {
          UIMgr.instance.addToUIRoot(BundleEnum.BUNDLE_G_COMMON_MAIN, "prefab/ui/UIMain", "UIMain", () => {
            PlayerModule.api.getOfflineEnergy();
            resolve(true);
          });
        });
      } else if (FmConfig.forReview) {
        if (!PlayerModule.data.getPlayerInfo().guideId) {
          TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopFusu, {});
        } else {
          UIMgr.instance.showPage(UIMgr.instance.defaultPageName, {}, null, () => {
            // UI界面
            UIMgr.instance.addToUIRoot(BundleEnum.BUNDLE_G_COMMON_MAIN, "prefab/ui/UIMain", "UIMain", () => {
              resolve(true);
            });
          });
        }
      } else {
        // 引导
        if (!PlayerModule.data.getPlayerInfo().guideId) {
          TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopFusu, {}, () => {
            resolve(true);
          });
        } else {
          UIMgr.instance.showPage(UIMgr.instance.defaultPageName, {}, null, () => {
            // UI界面
            UIMgr.instance.addToUIRoot(BundleEnum.BUNDLE_G_COMMON_MAIN, "prefab/ui/UIMain", "UIMain", () => {
              PlayerModule.api.getOfflineEnergy();
              resolve(true);
            });
          });
        }
      }
    });

    let tasks = [];
    for (let i in BundleEnum) {
      let callBack = (completedCallback) => {
        ResMgr.loadBundle(BundleEnum[i], completedCallback);
      };
      tasks.push(callBack);
    }
    asy.default.eachSeries(
      tasks,
      (task, completedCallback) => {
        task(completedCallback);
      },
      () => {}
    );

    // 添加心跳组件
    this.startUpdateEnergy();

    // 添加奖励组件
    this.addRewardCtrl();

    // 启动完成消息通知
    MsgMgr.emit(MsgEnum.ON_GAME_START);

    // 设置top层大小
    TipsMgr.getTipsRoot().getComponent(UITransform).setContentSize(BoutStartUp.instance.getVisibleSize());
  }

  getNodeCtrl() {
    let nodeCtrl = director.getScene().getChildByName("node_ctrl");
    if (!nodeCtrl) {
      nodeCtrl = new Node("node_ctrl");
      director.getScene().addChild(nodeCtrl);
    }
    return nodeCtrl;
  }

  // 添加心跳组件
  startUpdateEnergy() {
    let nodeCtrl = this.getNodeCtrl();

    let nodeHeartBeat = nodeCtrl.getChildByName("HeartBeat");
    if (nodeHeartBeat) {
      nodeHeartBeat.destroy();
    }

    nodeHeartBeat = new Node("HeartBeat");
    nodeHeartBeat.addComponent(HeartBeat);
    nodeCtrl.addChild(nodeHeartBeat);
  }

  // 添加奖励显示组件
  addRewardCtrl() {
    let nodeCtrl = this.getNodeCtrl();

    let nodeReward = nodeCtrl.getChildByName("GameReward");
    if (nodeReward) {
      nodeReward.destroy();
    }

    nodeReward = new Node("GameReward");
    nodeReward.addComponent(GameReward);
    nodeCtrl.addChild(nodeReward);
  }

  // 添加奖励显示组件
  addGuideCtrl() {
    let nodeCtrl = this.getNodeCtrl();

    let nodeReward = nodeCtrl.getChildByName("GuideCtrl");
    if (nodeReward) {
      nodeReward.destroy();
    }

    nodeReward = new Node("GuideCtrl");
    nodeReward.addComponent(GuideCtrl);
    nodeCtrl.addChild(nodeReward);
  }

  public update(dt: any): void {}

  public onExit(): void {
    TickerMgr.clearInterval(this.interId);
    UIMgr.instance.close(UIGameMap);
  }
}
