{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "afa02e11-1d9b-48fa-a107-451ccdf3b44c", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "afa02e11-1d9b-48fa-a107-451ccdf3b44c@6c48a", "displayName": "TY_btn_yuan_huang", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "afa02e11-1d9b-48fa-a107-451ccdf3b44c", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "afa02e11-1d9b-48fa-a107-451ccdf3b44c@f9941", "displayName": "TY_btn_yuan_huang", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 130, "height": 130, "rawWidth": 130, "rawHeight": 130, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-65, -65, 0, 65, -65, 0, -65, 65, 0, 65, 65, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 130, 130, 130, 0, 0, 130, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-65, -65, 0], "maxPos": [65, 65, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "afa02e11-1d9b-48fa-a107-451ccdf3b44c@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "afa02e11-1d9b-48fa-a107-451ccdf3b44c@6c48a"}}