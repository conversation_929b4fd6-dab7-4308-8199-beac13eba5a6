syntax = "proto3";
package sim;

// 
message FriendChatRequest {
  bool chatMore = 1;
}

// 
message FriendChatResponse {
  // 
  FriendVitalityMessage vitalityMessage = 1;
  // key:friendId value:谈心获得的friendShip
  map<int64,int64> chatGetMap = 2;
  // key:friendId value:friendShip最终值
  map<int64,int64> chatStockMap = 3;
}

// 
message FriendCitySkillMessage {
  double skillAdd = 1;
  int32 energyWashCount = 2;
  int32 itemWashCount = 3;
  double backUpSkillSkillAdd = 4;
}

// 
message FriendCitySkillRequest {
  int64 friendId = 1;
  int32 rank = 2;
  bool consumeItem = 3;
}

// 
message FriendGiftRequest {
  int64 friendId = 1;
  int64 itemId = 2;
  bool giftMore = 3;
}

// 
message FriendGiftResponse {
  // 友好度 (因果)
  int32 karma = 1;
  // 才华 (天命)
  int32 destiny = 2;
}

// 
message FriendHeroSkillRequest {
  int64 friendId = 1;
  int32 rank = 2;
}

// 
message FriendHeroSkillResponse {
  int32 level = 1;
  int64 friendShip = 2;
}

// 
message FriendKarmaUpdateMessage {
  map<int64,int32> karmaUpdateMap = 1;
}

// 
message FriendLabelMessage {
  map<int64,FriendSubLabelMessage> labelMessageMap = 1;
}

// 
message FriendMessage {
  // 挚友模版ID
  int64 friendId = 1;
  // 友好度 (因果)
  int32 karma = 2;
  // 才华 (天命)
  int32 destiny = 3;
  // 缘分点
  int64 friendShip = 4;
  // 美名等级
  int64 fameLv = 5;
  // 据点技能
  repeated FriendCitySkillMessage citySkillList = 6;
  // 门客技能 heroId : List<技能>
  repeated int32 heroSkillList = 7;
  // 获得时间
  int64 timeStamp = 8;
}

// 
message FriendStatisticsResponse {
  // 仙友偶遇次数
  map<int64,int32> encounterMap = 1;
  // VIP等级
  int32 vipLevel = 2;
  // 主角等级
  int32 level = 3;
  // 建筑总等级
  int32 cityTotalLevel = 4;
  // 繁荣度(气运赚速)
  double energySpeed = 5;
  // 累计徒弟成年数
  int32 adultPupilCnt = 6;
  // 累计获得灵兽数量
  int32 petCnt = 7;
  // 累计徒弟结伴数量
  int32 marryPupilCnt = 8;
  // 演武场累计胜利
  int32 competeWinCnt = 9;
}

// 
message FriendSubLabelMessage {
  int64 friendId = 1;
  // 完成的数量
  int64 count = 2;
  // 是否已拥有
  bool take = 3;
}

// 
message FriendUnlockCitySkillResponse {
  int64 friendId = 1;
  repeated FriendCitySkillMessage citySkillList = 2;
}

// 
message FriendVitalityMessage {
  int32 vitality = 1;
  int64 lastUpdateTime = 2;
}

// 
message TravelResUpdateMessage {
  // -1表示无实际作用;标识要访问的地点
  int64 placeId = 1;
  // 是否弹出对话
  bool isTalk = 2;
  // 体力值
  TravelVitalityMessage vitality = 3;
  // 游历获得奖励 如果是挚友，标识偶遇的次数 (注：如果要弹出仙友，索引0处为要弹出的仙友的ID)
  repeated double resAddList = 4;
}

// 
message TravelVitalityMessage {
  int32 vitality = 1;
  int32 vitalitySize = 2;
  int64 lastUpdateTime = 3;
}

