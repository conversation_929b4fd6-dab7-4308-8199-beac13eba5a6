{"skeleton": {"hash": "c2IBvCopYDtD/fn2E33wM9Qptxc=", "spine": "3.8.75", "x": -80, "y": 1, "width": 159, "height": 146, "images": "", "audio": ""}, "bones": [{"name": "root", "scaleX": -1}, {"name": "bone", "parent": "root", "y": -12.46, "color": "00ff1fff"}, {"name": "bone2", "parent": "bone", "x": -56.15, "y": 38.89, "color": "00ff1fff"}, {"name": "bone3", "parent": "bone", "x": 54.47, "y": 46.41, "color": "00ff1fff"}, {"name": "bone4", "parent": "bone", "x": -5.14, "y": 111.92, "color": "00ff1fff"}, {"name": "bone5", "parent": "bone", "x": 1.84, "y": 63.05, "color": "00ff1fff"}, {"name": "light_ball", "parent": "root", "y": -6.89, "scaleX": 1.2957, "scaleY": 1.2957}], "slots": [{"name": "xiaoguai11", "bone": "bone", "dark": "000000", "attachment": "xiaoguai11"}, {"name": "xiaoguai12", "bone": "bone", "dark": "000000"}, {"name": "xiaoguai13", "bone": "bone", "dark": "000000"}, {"name": "xiaoguai14", "bone": "bone", "dark": "000000"}, {"name": "xiaoguai15", "bone": "bone", "dark": "000000"}, {"name": "xiaoguai6", "bone": "bone", "dark": "000000"}, {"name": "xiaoguai8", "bone": "bone", "dark": "000000"}, {"name": "lightball", "bone": "light_ball", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"xiaoguai11": {"xiaoguai11": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.66667, 0.66667, 0.33333, 0.66667, 0.66667, 0.33333, 0.33333, 0.33333], "triangles": [11, 12, 10, 1, 12, 11, 0, 1, 11, 13, 4, 15, 3, 4, 13, 2, 3, 13, 12, 13, 14, 2, 13, 12, 1, 2, 12, 5, 6, 7, 15, 5, 7, 4, 5, 15, 15, 7, 8, 14, 15, 8, 13, 15, 14, 14, 8, 9, 10, 14, 9, 12, 14, 10], "vertices": [2, 5, 78.16, -49.6, 0.00077, 3, 25.53, -32.95, 0.99923, 3, 5, 25.16, -49.6, 0.43569, 2, 83.15, -25.43, 0.04392, 3, -27.47, -32.95, 0.52038, 3, 5, -27.84, -49.6, 0.42484, 2, 30.15, -25.43, 0.54493, 3, -80.47, -32.95, 0.03024, 1, 2, -22.85, -25.43, 1, 3, 4, -73.86, -49.8, 0.1689, 5, -80.84, -0.93, 0.01799, 2, -22.85, 23.24, 0.81311, 3, 4, -73.86, -1.13, 0.6785, 5, -80.84, 47.74, 0.01521, 2, -22.85, 71.9, 0.3063, 2, 4, -73.86, 47.54, 0.86688, 2, -22.85, 120.57, 0.13312, 3, 4, -20.86, 47.54, 0.95568, 2, 30.15, 120.57, 0.0428, 3, -80.47, 113.05, 0.00152, 2, 4, 32.14, 47.54, 0.87578, 3, -27.47, 113.05, 0.12422, 2, 4, 85.14, 47.54, 0.73538, 3, 25.53, 113.05, 0.26462, 3, 4, 85.14, -1.13, 0.53559, 5, 78.16, 47.74, 0.01813, 3, 25.53, 64.38, 0.44628, 3, 4, 85.14, -49.8, 0.09905, 5, 78.16, -0.93, 0.0025, 3, 25.53, 15.72, 0.89845, 3, 4, 32.14, -49.8, 0.0656, 5, 25.16, -0.93, 0.54, 3, -27.47, 15.72, 0.3944, 3, 4, -20.86, -49.8, 0.11993, 5, -27.84, -0.93, 0.5385, 2, 30.15, 23.24, 0.34157, 3, 4, 32.14, -1.13, 0.7067, 5, 25.16, 47.74, 0.10609, 3, -27.47, 64.38, 0.18721, 3, 4, -20.86, -1.13, 0.84574, 5, -27.84, 47.74, 0.05567, 2, 30.15, 71.9, 0.09859], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 159, "height": 146}}, "xiaoguai12": {"xiaoguai12": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.75, 0, 0.5, 0, 0.25, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.25, 1, 0.5, 1, 0.75, 0.75, 0.75, 0.5, 0.75, 0.25, 0.75, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5, 0.75, 0.25, 0.5, 0.25, 0.25, 0.25], "triangles": [7, 8, 9, 24, 7, 9, 6, 7, 24, 21, 6, 24, 24, 9, 10, 23, 24, 10, 21, 24, 23, 20, 21, 23, 23, 10, 11, 22, 23, 11, 20, 23, 22, 19, 20, 22, 22, 11, 12, 13, 22, 12, 19, 22, 13, 5, 6, 21, 18, 5, 21, 4, 5, 18, 3, 4, 18, 18, 21, 20, 17, 18, 20, 3, 18, 17, 2, 3, 17, 17, 20, 19, 16, 17, 19, 2, 17, 16, 1, 2, 16, 14, 19, 13, 16, 19, 14, 15, 16, 14, 1, 16, 15, 0, 1, 15], "vertices": [2, 3, 23.73, -34.86, 0.99959, 5, 76.36, -51.5, 0.00041, 3, 3, -13.77, -34.86, 0.74028, 5, 38.86, -51.5, 0.24439, 2, 96.86, -27.34, 0.01534, 3, 3, -51.27, -34.86, 0.21848, 5, 1.36, -51.5, 0.59578, 2, 59.36, -27.34, 0.18574, 3, 3, -88.77, -34.86, 0.01762, 5, -36.14, -51.5, 0.29586, 2, 21.86, -27.34, 0.68652, 2, 5, -73.64, -51.5, 0.00023, 2, -15.64, -27.34, 0.99977, 3, 5, -73.64, -14, 0.0077, 2, -15.64, 10.16, 0.93689, 4, -66.66, -62.87, 0.05541, 3, 5, -73.64, 23.5, 0.06654, 2, -15.64, 47.66, 0.47906, 4, -66.66, -25.37, 0.4544, 3, 5, -73.64, 61, 0.00971, 2, -15.64, 85.16, 0.16201, 4, -66.66, 12.13, 0.82828, 2, 2, -15.64, 122.66, 0.06972, 4, -66.66, 49.63, 0.93028, 3, 3, -88.77, 115.14, 8e-05, 2, 21.86, 122.66, 0.03355, 4, -29.16, 49.63, 0.96637, 3, 3, -51.27, 115.14, 0.03504, 2, 59.36, 122.66, 0.00048, 4, 8.34, 49.63, 0.96448, 2, 3, -13.77, 115.14, 0.15428, 4, 45.84, 49.63, 0.84572, 2, 3, 23.73, 115.14, 0.22603, 4, 83.34, 49.63, 0.77397, 3, 3, 23.73, 77.64, 0.33067, 5, 76.36, 61, 0.01059, 4, 83.34, 12.13, 0.65874, 3, 3, 23.73, 40.14, 0.65154, 5, 76.36, 23.5, 0.02871, 4, 83.34, -25.37, 0.31975, 2, 3, 23.73, 2.64, 0.97511, 4, 83.34, -62.87, 0.02489, 4, 3, -13.77, 2.64, 0.74533, 5, 38.86, -14, 0.23871, 2, 96.86, 10.16, 0.00121, 4, 45.84, -62.87, 0.01475, 3, 3, -51.27, 2.64, 0.09352, 5, 1.36, -14, 0.84006, 2, 59.36, 10.16, 0.06642, 4, 3, -88.77, 2.64, 0.00105, 5, -36.14, -14, 0.36986, 2, 21.86, 10.16, 0.585, 4, -29.16, -62.87, 0.04408, 3, 3, -13.77, 40.14, 0.42779, 5, 38.86, 23.5, 0.21998, 4, 45.84, -25.37, 0.35223, 4, 3, -51.27, 40.14, 0.03907, 5, 1.36, 23.5, 0.50979, 2, 59.36, 47.66, 0.00978, 4, 8.34, -25.37, 0.44135, 3, 5, -36.14, 23.5, 0.25375, 2, 21.86, 47.66, 0.27157, 4, -29.16, -25.37, 0.47468, 3, 3, -13.77, 77.64, 0.2189, 5, 38.86, 61, 0.033, 4, 45.84, 12.13, 0.7481, 3, 3, -51.27, 77.64, 0.02773, 5, 1.36, 61, 0.00184, 4, 8.34, 12.13, 0.97043, 3, 5, -36.14, 61, 0.01236, 2, 21.86, 85.16, 0.08142, 4, -29.16, 12.13, 0.90622], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0], "width": 150, "height": 150}}, "xiaoguai13": {"xiaoguai13": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.66667, 0.66667, 0.33333, 0.66667, 0.66667, 0.33333, 0.33333, 0.33333], "triangles": [13, 4, 15, 3, 4, 13, 2, 3, 13, 11, 12, 10, 1, 12, 11, 0, 1, 11, 12, 13, 14, 2, 13, 12, 1, 2, 12, 5, 6, 7, 15, 5, 7, 4, 5, 15, 15, 7, 8, 14, 15, 8, 13, 15, 14, 14, 8, 9, 10, 14, 9, 12, 14, 10], "vertices": [2, 5, 71.11, -47.79, 0.00181, 3, 18.49, -31.14, 0.99819, 3, 5, 18.45, -47.79, 0.50816, 3, -34.18, -31.14, 0.4296, 2, 76.44, -23.62, 0.06224, 3, 5, -34.22, -47.79, 0.33456, 3, -86.85, -31.14, 0.01774, 2, 23.78, -23.62, 0.6477, 1, 2, -28.89, -23.62, 1, 3, 4, -79.91, -46.99, 0.20023, 5, -86.89, 1.88, 0.00351, 2, -28.89, 26.04, 0.79626, 3, 4, -79.91, 2.68, 0.6824, 5, -86.89, 51.54, 0.00219, 2, -28.89, 75.71, 0.31541, 2, 4, -79.91, 52.34, 0.85497, 2, -28.89, 125.38, 0.14503, 3, 4, -27.24, 52.34, 0.94269, 3, -86.85, 117.86, 0.00017, 2, 23.78, 125.38, 0.05714, 2, 4, 25.43, 52.34, 0.93331, 3, -34.18, 117.86, 0.06669, 2, 4, 78.09, 52.34, 0.8341, 3, 18.49, 117.86, 0.1659, 3, 4, 78.09, 2.68, 0.62869, 5, 71.11, 51.54, 0.03537, 3, 18.49, 68.19, 0.33594, 3, 4, 78.09, -46.99, 0.11643, 5, 71.11, 1.88, 0.02058, 3, 18.49, 18.53, 0.86299, 3, 4, 25.43, -46.99, 0.0815, 5, 18.45, 1.88, 0.65858, 3, -34.18, 18.53, 0.25993, 3, 4, -27.24, -46.99, 0.17029, 5, -34.22, 1.88, 0.40446, 2, 23.78, 26.04, 0.42526, 3, 4, 25.43, 2.68, 0.8136, 5, 18.45, 51.54, 0.0771, 3, -34.18, 68.19, 0.1093, 3, 4, -27.24, 2.68, 0.84064, 5, -34.22, 51.54, 0.03232, 2, 23.78, 75.71, 0.12704], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 158, "height": 149}}, "xiaoguai14": {"xiaoguai14": {"type": "mesh", "uvs": [1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.8, 0.66667, 0.6, 0.66667, 0.4, 0.66667, 0.2, 0.66667, 0.8, 0.33333, 0.6, 0.33333, 0.4, 0.33333, 0.2, 0.33333], "triangles": [6, 7, 23, 19, 6, 23, 5, 6, 19, 4, 5, 19, 4, 19, 18, 3, 4, 18, 17, 18, 21, 3, 18, 17, 2, 3, 17, 16, 17, 20, 2, 17, 16, 1, 2, 16, 14, 20, 13, 16, 20, 14, 15, 16, 14, 1, 16, 15, 0, 1, 15, 7, 8, 9, 23, 7, 9, 23, 9, 10, 22, 23, 10, 19, 23, 22, 18, 19, 22, 22, 10, 11, 21, 22, 11, 18, 22, 21, 21, 11, 12, 20, 21, 12, 17, 21, 20, 20, 12, 13], "vertices": [2, 4, 104.83, -104.22, 2e-05, 3, 45.22, -38.7, 0.99998, 2, 3, 5.82, -38.7, 0.96582, 5, 58.45, -55.35, 0.03418, 3, 3, -33.58, -38.7, 0.40717, 5, 19.05, -55.35, 0.55426, 2, 77.05, -31.19, 0.03857, 3, 3, -72.98, -38.7, 0.03622, 5, -20.35, -55.35, 0.56427, 2, 37.65, -31.19, 0.39951, 2, 5, -59.75, -55.35, 0.02521, 2, -1.75, -31.19, 0.97479, 2, 4, -92.17, -104.22, 0.00194, 2, -41.15, -31.19, 0.99806, 2, 4, -92.17, -60.55, 0.18027, 2, -41.15, 12.48, 0.81973, 3, 4, -92.17, -16.89, 0.5395, 5, -99.15, 31.98, 0.00012, 2, -41.15, 56.15, 0.46039, 2, 4, -92.17, 26.78, 0.6906, 2, -41.15, 99.81, 0.3094, 2, 4, -52.77, 26.78, 0.77897, 2, -1.75, 99.81, 0.22103, 3, 4, -13.37, 26.78, 0.94788, 3, -72.98, 92.3, 0.00394, 2, 37.65, 99.81, 0.04818, 3, 4, 26.03, 26.78, 0.86336, 3, -33.58, 92.3, 0.13664, 5, 19.05, 75.65, 0, 3, 4, 65.43, 26.78, 0.6206, 3, 5.82, 92.3, 0.37927, 5, 58.45, 75.65, 0.00012, 2, 4, 104.83, 26.78, 0.51596, 3, 45.22, 92.3, 0.48404, 2, 4, 104.83, -16.89, 0.38193, 3, 45.22, 48.63, 0.61807, 2, 4, 104.83, -60.55, 0.10674, 3, 45.22, 4.96, 0.89326, 3, 4, 65.43, -60.55, 0.09454, 3, 5.82, 4.96, 0.86275, 5, 58.45, -11.68, 0.0427, 3, 4, 26.03, -60.55, 0.06987, 3, -33.58, 4.96, 0.2701, 5, 19.05, -11.68, 0.66003, 3, 4, -13.37, -60.55, 0.10486, 5, -20.35, -11.68, 0.66559, 2, 37.65, 12.48, 0.22954, 3, 4, -52.77, -60.55, 0.1721, 5, -59.75, -11.68, 0.09029, 2, -1.75, 12.48, 0.73761, 3, 4, 65.43, -16.89, 0.45837, 3, 5.82, 48.63, 0.49847, 5, 58.45, 31.98, 0.04316, 3, 4, 26.03, -16.89, 0.69985, 3, -33.58, 48.63, 0.16285, 5, 19.05, 31.98, 0.1373, 3, 4, -13.37, -16.89, 0.84356, 5, -20.35, 31.98, 0.08335, 2, 37.65, 56.15, 0.0731, 3, 4, -52.77, -16.89, 0.61494, 5, -59.75, 31.98, 0.03758, 2, -1.75, 56.15, 0.34748], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0], "width": 197, "height": 131}}, "xiaoguai15": {"xiaoguai15": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.75, 0, 0.5, 0, 0.25, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.25, 1, 0.5, 1, 0.75, 0.75, 0.75, 0.5, 0.75, 0.25, 0.75, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5, 0.75, 0.25, 0.5, 0.25, 0.25, 0.25], "triangles": [3, 4, 18, 4, 5, 18, 18, 5, 21, 5, 6, 21, 14, 19, 13, 19, 22, 13, 13, 22, 12, 22, 11, 12, 19, 20, 22, 20, 23, 22, 22, 23, 11, 23, 10, 11, 18, 21, 20, 20, 21, 23, 21, 24, 23, 23, 24, 10, 24, 9, 10, 21, 6, 24, 6, 7, 24, 24, 7, 9, 7, 8, 9, 2, 17, 16, 17, 20, 19, 2, 3, 17, 3, 18, 17, 17, 18, 20, 0, 1, 15, 1, 16, 15, 15, 16, 14, 16, 19, 14, 1, 2, 16, 16, 17, 19], "vertices": [1, 3, 29.26, -28.33, 1, 3, 3, -7.99, -28.33, 0.82991, 5, 44.64, -44.97, 0.16789, 2, 102.64, -20.81, 0.0022, 3, 3, -45.24, -28.33, 0.24541, 5, 7.39, -44.97, 0.64934, 2, 65.39, -20.81, 0.10525, 3, 3, -82.49, -28.33, 0.01445, 5, -29.86, -44.97, 0.41576, 2, 28.14, -20.81, 0.56978, 2, 5, -67.11, -44.97, 0.00188, 2, -9.11, -20.81, 0.99812, 3, 5, -67.11, -7.72, 0.05096, 4, -60.13, -56.59, 0.09926, 2, -9.11, 16.44, 0.84978, 3, 5, -67.11, 29.53, 0.09687, 4, -60.13, -19.34, 0.55068, 2, -9.11, 53.69, 0.35245, 3, 5, -67.11, 66.78, 0.00835, 4, -60.13, 17.91, 0.8975, 2, -9.11, 90.94, 0.09415, 2, 4, -60.13, 55.16, 0.97375, 2, -9.11, 128.19, 0.02625, 3, 3, -82.49, 120.67, 0.00192, 4, -22.88, 55.16, 0.98949, 2, 28.14, 128.19, 0.00859, 2, 3, -45.24, 120.67, 0.05645, 4, 14.37, 55.16, 0.94355, 2, 3, -7.99, 120.67, 0.17963, 4, 51.62, 55.16, 0.82037, 2, 3, 29.26, 120.67, 0.24525, 4, 88.87, 55.16, 0.75475, 3, 3, 29.26, 83.42, 0.33779, 5, 81.89, 66.78, 0.00337, 4, 88.87, 17.91, 0.65884, 3, 3, 29.26, 46.17, 0.62089, 5, 81.89, 29.53, 0.01656, 4, 88.87, -19.34, 0.36255, 2, 3, 29.26, 8.92, 0.94319, 4, 88.87, -56.59, 0.05681, 3, 3, -7.99, 8.92, 0.79913, 5, 44.64, -7.72, 0.15971, 4, 51.62, -56.59, 0.04115, 3, 3, -45.24, 8.92, 0.11345, 5, 7.39, -7.72, 0.87517, 2, 65.39, 16.44, 0.01138, 3, 5, -29.86, -7.72, 0.5003, 4, -22.88, -56.59, 0.0725, 2, 28.14, 16.44, 0.4272, 3, 3, -7.99, 46.17, 0.44576, 5, 44.64, 29.53, 0.14786, 4, 51.62, -19.34, 0.40638, 4, 3, -45.24, 46.17, 0.0773, 5, 7.39, 29.53, 0.37079, 4, 14.37, -19.34, 0.5496, 2, 65.39, 53.69, 0.00232, 3, 5, -29.86, 29.53, 0.23623, 4, -22.88, -19.34, 0.59024, 2, 28.14, 53.69, 0.17352, 3, 3, -7.99, 83.42, 0.24103, 5, 44.64, 66.78, 0.01978, 4, 51.62, 17.91, 0.73919, 3, 3, -45.24, 83.42, 0.05542, 5, 7.39, 66.78, 0.0059, 4, 14.37, 17.91, 0.93869, 3, 5, -29.86, 66.78, 0.00281, 4, -22.88, 17.91, 0.95948, 2, 28.14, 90.94, 0.03771], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0], "width": 149, "height": 149}}, "xiaoguai6": {"xiaoguai6": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.66667, 0.66667, 0.33333, 0.66667, 0.66667, 0.33333, 0.33333, 0.33333], "triangles": [11, 12, 10, 1, 12, 11, 0, 1, 11, 13, 4, 15, 3, 4, 13, 2, 3, 13, 12, 13, 14, 2, 13, 12, 1, 2, 12, 5, 6, 7, 15, 5, 7, 4, 5, 15, 15, 7, 8, 14, 15, 8, 13, 15, 14, 14, 8, 9, 10, 14, 9, 12, 14, 10], "vertices": [1, 3, 28.64, -34.46, 1, 3, 5, 28.26, -51.1, 0.39171, 2, 86.26, -26.94, 0.03783, 3, -24.36, -34.46, 0.57047, 3, 5, -24.74, -51.1, 0.46049, 2, 33.26, -26.94, 0.49412, 3, -77.36, -34.46, 0.04539, 2, 5, -77.74, -51.1, 1e-05, 2, -19.74, -26.94, 0.99999, 3, 4, -70.76, -49.97, 0.16364, 5, -77.74, -1.1, 0.02186, 2, -19.74, 23.06, 0.8145, 3, 4, -70.76, 0.03, 0.70231, 5, -77.74, 48.9, 0.01989, 2, -19.74, 73.06, 0.2778, 2, 4, -70.76, 50.03, 0.89771, 2, -19.74, 123.06, 0.10229, 3, 4, -17.76, 50.03, 0.96891, 2, 33.26, 123.06, 0.02798, 3, -77.36, 115.54, 0.00311, 2, 4, 35.24, 50.03, 0.86752, 3, -24.36, 115.54, 0.13248, 3, 4, 88.24, 50.03, 0.7336, 5, 81.26, 98.9, 0, 3, 28.64, 115.54, 0.2664, 3, 4, 88.24, 0.03, 0.53593, 5, 81.26, 48.9, 0.01642, 3, 28.64, 65.54, 0.44765, 3, 4, 88.24, -49.97, 0.09959, 5, 81.26, -1.1, 0.00221, 3, 28.64, 15.54, 0.8982, 3, 4, 35.24, -49.97, 0.07225, 5, 28.26, -1.1, 0.48461, 3, -24.36, 15.54, 0.44314, 3, 4, -17.76, -49.97, 0.10619, 5, -24.74, -1.1, 0.58877, 2, 33.26, 23.06, 0.30504, 3, 4, 35.24, 0.03, 0.69806, 5, 28.26, 48.9, 0.10031, 3, -24.36, 65.54, 0.20162, 3, 4, -17.76, 0.03, 0.87472, 5, -24.74, 48.9, 0.04882, 2, 33.26, 73.06, 0.07646], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 159, "height": 150}}, "xiaoguai8": {"xiaoguai8": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.75, 0.66667, 0.5, 0.66667, 0.25, 0.66667, 0.75, 0.33333, 0.5, 0.33333, 0.25, 0.33333], "triangles": [6, 7, 8, 19, 6, 8, 5, 6, 19, 19, 8, 9, 18, 19, 9, 16, 19, 18, 18, 9, 10, 17, 18, 10, 15, 18, 17, 17, 10, 11, 12, 17, 11, 1, 2, 14, 14, 17, 12, 13, 14, 12, 1, 14, 13, 0, 1, 13, 15, 16, 18, 3, 16, 15, 2, 3, 15, 14, 15, 17, 2, 15, 14, 16, 5, 19, 4, 5, 16, 3, 4, 16], "vertices": [1, 3, 26.22, -32.88, 1, 3, 2, 97.1, -25.36, 0.01291, 5, 39.1, -49.52, 0.2292, 3, -13.53, -32.88, 0.75789, 3, 2, 57.35, -25.36, 0.19617, 5, -0.65, -49.52, 0.60684, 3, -53.28, -32.88, 0.19699, 3, 2, 17.6, -25.36, 0.74897, 5, -40.4, -49.52, 0.23973, 3, -93.03, -32.88, 0.0113, 1, 2, -22.15, -25.36, 1, 3, 2, -22.15, 20.64, 0.83889, 5, -80.15, -3.52, 0.0136, 4, -73.17, -52.39, 0.14751, 3, 2, -22.15, 66.64, 0.34182, 5, -80.15, 42.48, 0.0209, 4, -73.17, -6.39, 0.63728, 2, 2, -22.15, 112.64, 0.16167, 4, -73.17, 39.61, 0.83833, 2, 2, 17.6, 112.64, 0.08835, 4, -33.42, 39.61, 0.91165, 3, 2, 57.35, 112.64, 0.00311, 3, -53.28, 105.12, 0.0325, 4, 6.33, 39.61, 0.96439, 3, 5, 39.1, 88.48, 1e-05, 3, -13.53, 105.12, 0.20838, 4, 46.08, 39.61, 0.79161, 2, 3, 26.22, 105.12, 0.31898, 4, 85.83, 39.61, 0.68102, 3, 5, 78.85, 42.48, 0.02418, 3, 26.22, 59.12, 0.50356, 4, 85.83, -6.39, 0.47226, 3, 5, 78.85, -3.52, 0.0015, 3, 26.22, 13.12, 0.9275, 4, 85.83, -52.39, 0.07101, 3, 5, 39.1, -3.52, 0.26634, 3, -13.53, 13.12, 0.67518, 4, 46.08, -52.39, 0.05848, 3, 2, 57.35, 20.64, 0.01754, 5, -0.65, -3.52, 0.96931, 3, -53.28, 13.12, 0.01315, 3, 2, 17.6, 20.64, 0.55585, 5, -40.4, -3.52, 0.31734, 4, -33.42, -52.39, 0.1268, 3, 5, 39.1, 42.48, 0.11565, 3, -13.53, 59.12, 0.32289, 4, 46.08, -6.39, 0.56146, 4, 2, 57.35, 66.64, 0.00018, 5, -0.65, 42.48, 0.11408, 3, -53.28, 59.12, 0.02367, 4, 6.33, -6.39, 0.86207, 3, 2, 17.6, 66.64, 0.19786, 5, -40.4, 42.48, 0.08134, 4, -33.42, -6.39, 0.72081], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 159, "height": 138}}, "lightball": {"lightball": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [102, -45.69, -101, -45.69, -101, 157.31, 102, 157.31], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 203}}}}], "animations": {"xiaoguai1": {"slots": {"xiaoguai11": {"attachment": [{"name": null}]}, "xiaoguai6": {"attachment": [{"name": "xiaoguai6"}]}}, "bones": {"bone": {"scale": [{"y": 0.811, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.1667, "y": 1.077, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.3333, "y": 0.811}]}, "bone4": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -9.25, "y": 0.75, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}, "bone5": {"translate": [{"x": 4.18, "y": 0.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -9.25, "y": 0.75, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.3333, "x": 4.18, "y": 0.19}]}, "bone3": {"translate": [{"x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 8.84}]}, "bone2": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 8.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}}}, "xiaoguai1_1": {"slots": {"xiaoguai11": {"attachment": [{"name": null}]}, "xiaoguai6": {"twoColor": [{"time": 0.2333, "light": "ffffffff", "dark": "ffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 0.2333, "name": "xiaoguai6"}]}, "lightball": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}], "attachment": [{"name": "lightball"}]}}, "bones": {"light_ball": {"translate": [{"y": 1329.14, "curve": 0.58, "c2": 0.55, "c3": 0.25}, {"time": 0.2}], "scale": [{"x": 0.669, "y": 1.468, "curve": "stepped"}, {"time": 0.1, "x": 0.669, "y": 1.468, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.346, "y": 0.974}]}}}, "xiaoguai2": {"slots": {"xiaoguai11": {"attachment": [{"name": null}]}, "xiaoguai8": {"attachment": [{"name": "xiaoguai8"}]}}, "bones": {"bone": {"scale": [{"y": 0.811, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.1667, "y": 1.077, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.3333, "y": 0.811}]}, "bone4": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -9.25, "y": 0.75, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}, "bone5": {"translate": [{"x": 4.18, "y": 0.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -9.25, "y": 0.75, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.3333, "x": 4.18, "y": 0.19}]}, "bone3": {"translate": [{"x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 8.84}]}, "bone2": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 8.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}}}, "xiaoguai2_1": {"slots": {"xiaoguai11": {"attachment": [{"name": null}]}, "xiaoguai8": {"twoColor": [{"time": 0.2333, "light": "ffffffff", "dark": "ffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 0.2333, "name": "xiaoguai8"}]}, "lightball": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}], "attachment": [{"name": "lightball"}]}}, "bones": {"light_ball": {"translate": [{"y": 1329.14, "curve": 0.58, "c2": 0.55, "c3": 0.25}, {"time": 0.2}], "scale": [{"x": 0.669, "y": 1.468, "curve": "stepped"}, {"time": 0.1, "x": 0.669, "y": 1.468, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.346, "y": 0.974}]}}}, "xiaoguai3": {"bones": {"bone": {"scale": [{"y": 0.811, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.1667, "y": 1.077, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.3333, "y": 0.811}]}, "bone4": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -9.25, "y": 0.75, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}, "bone5": {"translate": [{"x": 4.18, "y": 0.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -9.25, "y": 0.75, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.3333, "x": 4.18, "y": 0.19}]}, "bone3": {"translate": [{"x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 8.84}]}, "bone2": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 8.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}}}, "xiaoguai3_1": {"slots": {"xiaoguai11": {"twoColor": [{"time": 0.2333, "light": "ffffffff", "dark": "ffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "light": "ffffffff", "dark": "000000"}], "attachment": [{"name": null}, {"time": 0.2333, "name": "xiaoguai11"}]}, "lightball": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}], "attachment": [{"name": "lightball"}]}}, "bones": {"light_ball": {"translate": [{"y": 1329.14, "curve": 0.58, "c2": 0.55, "c3": 0.25}, {"time": 0.2}], "scale": [{"x": 0.669, "y": 1.468, "curve": "stepped"}, {"time": 0.1, "x": 0.669, "y": 1.468, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.346, "y": 0.974}]}}}, "xiaoguai4": {"slots": {"xiaoguai12": {"attachment": [{"name": "xiaoguai12"}]}, "xiaoguai11": {"attachment": [{"name": null}]}}, "bones": {"bone": {"scale": [{"y": 0.811, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.1667, "y": 1.077, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.3333, "y": 0.811}]}, "bone4": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -9.25, "y": 0.75, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}, "bone5": {"translate": [{"x": 4.18, "y": 0.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -9.25, "y": 0.75, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.3333, "x": 4.18, "y": 0.19}]}, "bone3": {"translate": [{"x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 8.84}]}, "bone2": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 8.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}}}, "xiaoguai5": {"slots": {"xiaoguai11": {"attachment": [{"name": null}]}, "xiaoguai13": {"attachment": [{"name": "xiaoguai13"}]}}, "bones": {"bone": {"scale": [{"y": 0.811, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.1667, "y": 1.077, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.3333, "y": 0.811}]}, "bone4": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -9.25, "y": 0.75, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}, "bone5": {"translate": [{"x": 4.18, "y": 0.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -9.25, "y": 0.75, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.3333, "x": 4.18, "y": 0.19}]}, "bone3": {"translate": [{"x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 8.84}]}, "bone2": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 8.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}}}, "xiaoguai6": {"slots": {"xiaoguai11": {"attachment": [{"name": null}]}, "xiaoguai14": {"attachment": [{"name": "xiaoguai14"}]}}, "bones": {"bone": {"scale": [{"y": 0.811, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.1667, "y": 1.077, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.3333, "y": 0.811}]}, "bone4": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -9.25, "y": 0.75, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}, "bone5": {"translate": [{"x": 4.18, "y": 0.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -9.25, "y": 0.75, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.3333, "x": 4.18, "y": 0.19}]}, "bone3": {"translate": [{"x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 8.84}]}, "bone2": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 8.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}}}, "xiaoguai7": {"slots": {"xiaoguai11": {"attachment": [{"name": null}]}, "xiaoguai15": {"attachment": [{"name": "xiaoguai15"}]}}, "bones": {"bone": {"scale": [{"y": 0.811, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.1667, "y": 1.077, "curve": 0.432, "c2": 0.03, "c3": 0.623}, {"time": 0.3333, "y": 0.811}]}, "bone4": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -9.25, "y": 0.75, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}, "bone5": {"translate": [{"x": 4.18, "y": 0.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -9.25, "y": 0.75, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.3333, "x": 4.18, "y": 0.19}]}, "bone3": {"translate": [{"x": 8.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 8.84}]}, "bone2": {"translate": [{"x": -4.46, "y": 0.55, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "x": -9.25, "y": 0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 8.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -4.46, "y": 0.55}]}}, "deform": {"default": {"xiaoguai15": {"xiaoguai15": [{"vertices": [10.09546, -7.40898, 5.47443, -7.40491, 5.47425, -7.40492, 5.47424, -7.40491, 0.51311, -7.38679, 0.51293, -7.3868, 0.51292, -7.38679, -4.62761, -7.36127, -4.6278, -7.36128, -4.6278, -7.36127, -9.60805, -7.34237, -9.60806, -7.34237, -9.55654, -2.82204, -9.55654, -2.82185, -9.55654, -2.822, -9.50836, 1.69849, -9.50837, 1.69867, -9.50837, 1.69847, -9.60126, 6.22482, -9.60127, 6.22497, -9.60127, 6.22482, -9.61005, 10.7478, -9.61005, 10.74762, -5.08425, 10.74748, -5.08444, 10.74765, -5.08444, 10.74747, -0.47366, 10.7439, -0.47386, 10.74403, 4.24781, 10.73568, 4.24761, 10.73586, 8.8763, 10.73132, 8.87613, 10.73146, 9.02932, 6.20251, 9.02915, 6.20251, 9.02914, 6.20266, 9.50047, 1.66056, 9.50029, 1.66053, 9.50028, 1.66073, 10.00368, -2.88269, 10.0035, -2.88255, 5.41614, -2.88, 5.41596, -2.88002, 5.41595, -2.87988, 0.53701, -2.86529, 0.53681, -2.86529, 0.53682, -2.86529, -4.56243, -2.84148, -4.56244, -2.84131, -4.56242, -2.84149, 4.83289, 1.66655, 4.83272, 1.66655, 4.8327, 1.66673, -0.05082, 1.68149, -0.05098, 1.68146, -0.051, 1.68159, -0.05101, 1.68149, -4.83957, 1.69242, -4.83959, 1.69261, -4.83959, 1.69241, 4.36776, 6.20826, 4.36759, 6.20825, 4.36757, 6.2084, -0.46915, 6.22115, -0.4693, 6.22112, -0.46933, 6.22134, -5.08457, 6.22504, -5.08459, 6.2252, -5.08459, 6.22504]}]}}}}}}