import { _decorator, Component, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import FmUtils from "../../../lib/utils/FmUtils";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UILotteryHint")
export class UILotteryHint extends UINode {
  protected _openAct: boolean = true; // 打开动作

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_LOTTERY}?prefab/ui/UILotteryHint`;
  }

  protected onEvtShow(): void {
    let db = JsonMgr.instance.jsonList.c_luckDraw;
    let list = Object.keys(db);
    let info = db[list[0]];

    let itemInfo = JsonMgr.instance.getConfigItem(info.reward1List[0]);

    FmUtils.setItemNode(this.getNode("Item"), itemInfo.id, 1);
  }

  on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
