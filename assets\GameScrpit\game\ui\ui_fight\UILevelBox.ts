import { _decorator, Label, Node } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import MsgMgr from "../../../lib/event/MsgMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { FightModule } from "../../../module/fight/src/FightModule";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { FightMsgEnum } from "../../../module/fight/src/FightConfig";
import MsgEnum from "../../event/MsgEnum";

const { ccclass, property } = _decorator;

@ccclass("UILevelBox")
export class UILevelBox extends UINode {
  protected _openAct: boolean = true; //打开动作

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FIGHT}?prefab/ui/UILevelBox`;
  }

  private _tickerIdList: number[] = [];

  protected onRegEvent(): void {
    MsgMgr.on(FightMsgEnum.ON_FIGHT_TREASURE_UPDATE, this.setMaxBarLab, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(FightMsgEnum.ON_FIGHT_TREASURE_UPDATE, this.setMaxBarLab, this);
  }

  protected onEvtShow(): void {
    this.setMaxBarLab();
    //this.loadBoxList();
  }

  private setMaxBarLab() {
    let db = JsonMgr.instance.jsonList.c_copyMain[FightModule.data.chapterId];
    let max = db.maxEven;

    let curNum = Object.keys(FightModule.data.treasureMap).length;
    this["maxBarLab"].getComponent(Label).string = "宝箱累计上限：" + curNum + "/" + max;
    this.upBoxList();
  }

  private upBoxList() {
    let boxNum = this.getNode("content").children.length;
    let curNum = Object.keys(FightModule.data.treasureMap).length;
    if (curNum > boxNum) {
      let needNum = curNum - boxNum;
      for (let i = 0; i < needNum; i++) {
        let node = ToolExt.clone(this["item"], this);
        this["content"].addChild(node);
        node.active = true;
      }
    } else {
      let dexNum = boxNum - curNum;
      for (let i = 0; i < dexNum; i++) {
        let node: Node = this.getNode("content").children[0];
        node.removeFromParent();
        node.destroy();
      }
    }
    let treasureMap = FightModule.data.treasureMap;
    let index = 0;
    for (let i in treasureMap) {
      let node = this.getNode("content").children[index];
      node["boxInfo"] = treasureMap[i];
      let time = node.getChildByName("time");
      time.getComponent(Label).string = ToolExt.timestampToDate(treasureMap[i].createTime);

      index++;
    }
  }

  private loadBoxList() {
    let treasureMap = FightModule.data.treasureMap;
    let loadTime = 0.016;
    for (let i in treasureMap) {
      let id = TickerMgr.setTimeout(loadTime, () => {
        let node = ToolExt.clone(this["item"], this);
        this["content"].addChild(node);
        node.active = true;

        node["boxInfo"] = treasureMap[i];
        let time = node.getChildByName("time");
        time.getComponent(Label).string = ToolExt.timestampToDate(treasureMap[i].createTime);
      });
      this._tickerIdList.push(id);
      loadTime += 0.016;
    }
  }

  private on_click_btn_get(event) {
    let item = event.node.parent;
    let boxId = item.boxInfo.id;

    FightModule.api.taskOneTreasure(boxId, (res) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.values });
    });
  }

  private on_click_btn_getAllBox() {
    if (Object.keys(FightModule.data.treasureMap).length <= 0) {
      return;
    }

    FightModule.api.takeAllTreasure((res) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.values });
    });
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    for (let i = 0; i < this._tickerIdList.length; i++) {
      let id = this._tickerIdList[i];
      TickerMgr.clearTimeout(id);
    }
  }
}
