import { _decorator, Node } from "cc";
import { AttrEnum } from "../../GameDefine";
import { Label } from "cc";
import { JsonMgr } from "../../mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

/**
 *
 * hopewsw
 * Fri Nov 29 2024 22:07:27 GMT+0800 (中国标准时间)
 *
 */
@ccclass("UIFractureTipAttr")
@routeConfig({
  url: "prefab/ui/UIFractureTipAttr",
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  nextHop: [],
  exit: "",
})
export class UIFractureTipAttr extends BaseCtrl {
  public playShowAni: boolean = true;
  @property(Node)
  private nodeBase: Node;

  @property(Node)
  private nodeAdvance: Node;

  @property(Node)
  private nodeAnt: Node;

  private _attrMap: { [key: number]: number } = {};

  // public set attrMap(attrMap: { [key: number]: number }) {
  //   this._attrMap = attrMap;
  //   // this.setAttr(attrMap);
  // }
  init(args: RouteShowArgs): void {
    if (args.payload.attrMap) {
      this._attrMap = args.payload.attrMap;
    }
    if (args.payload.attr) {
      for (let i = 0; i < args.payload.attr.length; i++) {
        let attribute = JsonMgr.instance.jsonList.c_attribute[args.payload.attr[i][0]];
        if (attribute.type1 == 2) {
          this._attrMap[args.payload.attr[i][0]] = args.payload.attr[i][1] / 10000;
        } else {
          this._attrMap[args.payload.attr[i][0]] = args.payload.attr[i][1];
        }
      }
    }
  }

  private formatAttr(nodeParent: Node, attrList: number[], attrMap: { [key: number]: number }) {
    for (let i = 0; i < attrList.length; i++) {
      let node = nodeParent.children[i];
      node.active = true;

      let configAttr = JsonMgr.instance.getConfigAttribute(attrList[i]);
      node.getChildByName("lbl_key").getComponent(Label).string = configAttr.name;
      node.getChildByName("lbl_value").getComponent(Label).string = Formate.formatAttr(
        attrList[i],
        attrMap[attrList[i]]
      );
    }
  }

  start() {
    super.start();
    let attr: { [key: number]: number } = this._attrMap;
    this.setAttr(attr);
  }

  setAttr(attr: { [key: number]: number }) {
    // 基础属性
    let baseAttr = [AttrEnum.攻击_2, AttrEnum.生命_1, AttrEnum.防御_3, AttrEnum.敏捷_4];
    this.formatAttr(this.nodeBase, baseAttr, attr);

    let advanceAttrList = [
      AttrEnum.击晕_21,
      AttrEnum.闪避_22,
      AttrEnum.连击_23,
      AttrEnum.反击_24,
      AttrEnum.暴击_25,
      AttrEnum.吸血_26,
    ];
    this.formatAttr(this.nodeAdvance, advanceAttrList, attr);

    let antAttrList = [
      AttrEnum.抗击晕_31,
      AttrEnum.抗闪避_32,
      AttrEnum.抗连击_33,
      AttrEnum.抗反击_34,
      AttrEnum.抗暴击_35,
      AttrEnum.抗吸血_36,
    ];
    this.formatAttr(this.nodeAnt, antAttrList, attr);
  }

  private onClose() {
    this.closeBack();
  }
}
