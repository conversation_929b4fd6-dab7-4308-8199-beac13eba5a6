<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>S0260.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{364,83}</string>
                <key>spriteSourceSize</key>
                <string>{364,83}</string>
                <key>textureRect</key>
                <string>{{1,184},{364,83}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0269.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{484,105}</string>
                <key>spriteSourceSize</key>
                <string>{484,105}</string>
                <key>textureRect</key>
                <string>{{1,1},{484,105}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S1261.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{473,74}</string>
                <key>spriteSourceSize</key>
                <string>{473,74}</string>
                <key>textureRect</key>
                <string>{{1,108},{473,74}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UITaskMainPage.png</string>
            <key>size</key>
            <string>{486,268}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:42ee3933cdf17d0a171f00ecbf6609ea:746af993103aa12c3e95b54c0e0d813f:7c7b2a1985145ae1e2e1b24e2a2c4676$</string>
            <key>textureFileName</key>
            <string>UITaskMainPage.png</string>
        </dict>
    </dict>
</plist>
