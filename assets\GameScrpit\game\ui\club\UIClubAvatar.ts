import { _decorator, Node, Sprite } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ResMgr from "../../../lib/common/ResMgr";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

@ccclass("UIClubAvatar")
export class UIClubAvatar extends BaseCtrl {
  @property(Sprite)
  private color: Sprite;
  @property(Sprite)
  private pic: Sprite;
  start() {
    // let avatar_color = 1;
    // let avatar_pic = 1;
    // if (ClubModule.data.clubMessage.avatar) {
    //   let ava = ClubModule.data.clubMessage.avatar.split("_");
    //   if (ava.length == 2) {
    //     avatar_color = Number(ava[0]);
    //     avatar_pic = Number(ava[1]);
    //   } else {
    //     avatar_color = 1;
    //     avatar_pic = 1;
    //   }
    // }
    // this.setAvatar(`${avatar_color}_${avatar_pic}`);
  }

  update(deltaTime: number) {}

  onLoad() {
    this.node.on(Node.EventType.TOUCH_END, this.onClick, this);
  }

  onClick() {
    // UIMgr.instance.showDialog(ClubRouteItem.UIClubEditAvatar);
  }
  public setAvatar(avatar: string) {
    let ava = avatar.split("_");
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_G_CLUB, `atlas_icons/zm_icon_qizhi_${ava[0]}`, this.color);
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_G_CLUB, `atlas_icons/zm_icon_qizhituan_${ava[1]}`, this.pic);
  }
}
