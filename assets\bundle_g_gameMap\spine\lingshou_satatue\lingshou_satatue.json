{"skeleton": {"hash": "8kWzzQtyRXXTuWPzR+mT1fHe2r8=", "spine": "3.8.75", "x": -375, "y": -549.3, "width": 750, "height": 1625, "images": "./images/", "audio": "D:/spine导出/灵兽石像动画"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 343.61, "rotation": -0.18, "x": 4.96, "y": -35.54, "scaleX": 0.1401, "scaleY": 0.1401}, {"name": "bone2", "parent": "bone", "length": 73.29, "rotation": 166.39, "x": -43.93, "y": 442.43}, {"name": "bone3", "parent": "bone2", "length": 84.95, "rotation": -25.44, "x": 73.29}, {"name": "bone4", "parent": "bone", "length": 66.28, "rotation": -6.58, "x": -32.81, "y": 441.38}, {"name": "bone5", "parent": "bone4", "length": 76.52, "rotation": 11.95, "x": 66.28}, {"name": "bone6", "parent": "bone5", "length": 70.01, "rotation": -97.84, "x": 51.13, "y": 27.8}, {"name": "bone7", "parent": "bone6", "length": 66.39, "rotation": 40.07, "x": 70.01}, {"name": "bone8", "parent": "bone7", "length": 54.55, "rotation": -74.41, "x": 65.97, "y": 0.33}, {"name": "target1", "parent": "bone", "rotation": 0.18, "x": 119.34, "y": 345, "color": "ff3f00ff"}, {"name": "target2", "parent": "target1", "x": -31.5, "y": -43.05, "color": "ff3f00ff"}, {"name": "bone9", "parent": "bone2", "length": 71.69, "rotation": 129.85, "x": 23.16, "y": -7.19}, {"name": "bone10", "parent": "bone9", "length": 63.14, "rotation": -29.88, "x": 71.69}, {"name": "bone11", "parent": "bone10", "length": 50.48, "rotation": -52.5, "x": 63.14}, {"name": "target3", "parent": "bone", "rotation": 0.18, "x": -37.37, "y": 327.42, "color": "ff3f00ff"}, {"name": "target4", "parent": "target3", "x": -42.97, "y": -27.9, "color": "ff3f00ff"}, {"name": "bone12", "parent": "bone4", "length": 88.45, "rotation": -116.41, "x": 69.45, "y": 43.06}, {"name": "bone13", "parent": "bone12", "length": 67.12, "rotation": 65.75, "x": 88.45}, {"name": "bone14", "parent": "bone13", "length": 54.83, "rotation": -87.54, "x": 67.12}, {"name": "target5", "parent": "bone", "rotation": 0.18, "x": 29.26, "y": 346.74, "color": "ff3f00ff"}, {"name": "target6", "parent": "target5", "x": -43.72, "y": -30.32, "color": "ff3f00ff"}, {"name": "bone15", "parent": "bone2", "length": 80.17, "rotation": 123.46, "x": 116.78, "y": -5.79}, {"name": "bone16", "parent": "bone15", "length": 66.7, "rotation": -32.57, "x": 80.17}, {"name": "bone17", "parent": "bone16", "length": 46.95, "rotation": -41.18, "x": 66.7}, {"name": "target7", "parent": "bone", "rotation": 0.18, "x": -142.96, "y": 336.16, "color": "ff3f00ff"}, {"name": "target8", "parent": "target7", "x": -39.12, "y": -25.35, "color": "ff3f00ff"}, {"name": "bone18", "parent": "bone3", "length": 78, "rotation": -33.95, "x": -32.78, "y": -37.81, "color": "94ff00ff"}, {"name": "bone19", "parent": "bone18", "x": 153.76, "y": 82.12, "color": "abe323ff"}, {"name": "bone20", "parent": "bone18", "x": 165.9, "y": 67.39}, {"name": "bone21", "parent": "bone5", "length": 66.88, "rotation": -17.5, "x": 74.77, "y": -5.9}, {"name": "bone22", "parent": "bone21", "length": 65.62, "rotation": 39.8, "x": 66.88}, {"name": "bone23", "parent": "bone22", "length": 77.8, "rotation": 48.36, "x": 65.62}, {"name": "bone24", "parent": "bone23", "length": 95.05, "rotation": 13.43, "x": 77.8}, {"name": "bone25", "parent": "bone18", "length": 40.42, "rotation": -56.05, "x": 228.93, "y": -128.2}, {"name": "bone26", "parent": "bone25", "length": 51.16, "rotation": 11.7, "x": 40.42}, {"name": "bone27", "parent": "bone18", "length": 52.64, "rotation": 16.29, "x": 278.09, "y": 78.57}, {"name": "bone28", "parent": "bone27", "length": 56, "rotation": -16.54, "x": 52.64}, {"name": "bone29", "parent": "bone18", "length": 44.62, "rotation": 147.04, "x": 66.4, "y": 66.01, "color": "ff0000ff"}, {"name": "bone30", "parent": "bone29", "length": 31.15, "rotation": 21.34, "x": 44.62, "color": "ff0000ff"}, {"name": "bone31", "parent": "bone30", "length": 37.79, "rotation": 32.53, "x": 31.15, "color": "ff0000ff"}, {"name": "bone32", "parent": "bone18", "length": 26.51, "rotation": -158.73, "x": 43.9, "y": -47.39, "color": "ff0000ff"}, {"name": "bone33", "parent": "bone32", "length": 21.62, "rotation": -45.58, "x": 26.51, "color": "ff0000ff"}, {"name": "bone34", "parent": "bone18", "length": 42.3, "rotation": 126.31, "x": 134.23, "y": 109.14, "color": "ff0000ff"}, {"name": "bone35", "parent": "bone34", "length": 36.95, "rotation": 24.53, "x": 42.3, "color": "ff0000ff"}, {"name": "bone36", "parent": "bone18", "length": 57.14, "rotation": 77.71, "x": 215.48, "y": 81.63, "color": "ff0000ff"}, {"name": "bone37", "parent": "bone36", "length": 55.98, "rotation": -45.44, "x": 57.14, "color": "ff0000ff"}, {"name": "bone38", "parent": "bone18", "length": 46.38, "rotation": 86.54, "x": 183.9, "y": 119.46, "color": "ff0000ff"}, {"name": "bone39", "parent": "bone38", "length": 42.95, "rotation": 0.31, "x": 46.38, "color": "ff0000ff"}, {"name": "bone40", "parent": "bone18", "length": 58.44, "rotation": -26.53, "x": 318.73, "y": -15.07, "color": "ff0000ff"}, {"name": "bone41", "parent": "bone40", "length": 66.5, "rotation": -33.45, "x": 58.44, "color": "ff0000ff"}, {"name": "bone42", "parent": "bone18", "length": 61.92, "rotation": -67.28, "x": 314.03, "y": -77.78, "color": "ff0000ff"}, {"name": "bone43", "parent": "bone42", "length": 61.37, "rotation": -13.92, "x": 61.92, "color": "ff0000ff"}, {"name": "bone44", "parent": "bone18", "length": 76.44, "rotation": -76.44, "x": 271.95, "y": -133.17, "color": "ff0000ff"}, {"name": "bone45", "parent": "bone44", "length": 58.21, "rotation": -38.61, "x": 76.06, "y": -0.65, "color": "ff0000ff"}, {"name": "bone46", "parent": "bone18", "length": 53.71, "rotation": -115.75, "x": 207.94, "y": -151.04, "color": "ff0000ff"}, {"name": "bone47", "parent": "bone46", "length": 62.18, "rotation": -0.19, "x": 53.71, "color": "ff0000ff"}, {"name": "bone48", "parent": "bone18", "length": 31.16, "rotation": -102.63, "x": 136.11, "y": -129.32, "color": "ff0000ff"}, {"name": "bone49", "parent": "bone48", "length": 30.67, "rotation": 45.83, "x": 32.03, "y": 1.46, "color": "ff0000ff"}, {"name": "bone50", "parent": "bone18", "length": 30.34, "rotation": -93.82, "x": 73.34, "y": -114.31, "color": "ff0000ff"}, {"name": "bone51", "parent": "bone50", "length": 25.82, "rotation": -53.23, "x": 30.34, "color": "ff0000ff"}, {"name": "bone52", "parent": "bone18", "length": 53.87, "rotation": -120.66, "x": 120.42, "y": -134.08, "color": "ff0000ff"}, {"name": "bone53", "parent": "bone52", "length": 56.82, "rotation": 4.63, "x": 53.87, "color": "ff0000ff"}, {"name": "bone54", "parent": "bone18", "length": 47.49, "rotation": -135.43, "x": 74.79, "y": -80.69, "color": "ff0000ff"}, {"name": "bone55", "parent": "bone54", "length": 36.02, "rotation": 20.14, "x": 47.49, "color": "ff0000ff"}], "slots": [{"name": "rootcut", "bone": "root", "attachment": "rootcut"}, {"name": "jianzhuls01", "bone": "bone", "attachment": "jianzhuls01"}, {"name": "jianzhuls02", "bone": "bone", "attachment": "jianzhuls02"}, {"name": "jianzhuls03", "bone": "bone", "attachment": "jianzhuls03"}, {"name": "jianzhuls04", "bone": "bone", "attachment": "jianzhuls04"}, {"name": "jianzhuls05", "bone": "bone", "attachment": "jianzhuls05"}, {"name": "jianzhuls06", "bone": "bone", "attachment": "jianzhuls06"}, {"name": "jianzhuls07", "bone": "bone", "attachment": "jianzhuls07"}, {"name": "jianzhuls08", "bone": "bone18", "attachment": "jianzhuls08"}, {"name": "jianzhuls09", "bone": "bone18", "attachment": "jianzhuls09"}, {"name": "jianzhuls010", "bone": "bone18", "attachment": "jianzhuls010"}, {"name": "jianzhuls011", "bone": "bone18", "attachment": "jianzhuls011"}, {"name": "jianzhuls013", "bone": "bone20", "attachment": "jianzhuls012"}, {"name": "jianzhuls014", "bone": "bone19", "attachment": "jianzhuls014"}], "ik": [{"name": "target1", "bones": ["bone6", "bone7"], "target": "target1"}, {"name": "target2", "order": 8, "bones": ["bone7", "bone8"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 1, "bones": ["bone9", "bone10"], "target": "target3", "bendPositive": false}, {"name": "target4", "order": 7, "bones": ["bone10", "bone11"], "target": "target4", "bendPositive": false}, {"name": "target5", "order": 2, "bones": ["bone12", "bone13"], "target": "target5"}, {"name": "target6", "order": 6, "bones": ["bone13", "bone14"], "target": "target6", "bendPositive": false}, {"name": "target7", "order": 3, "bones": ["bone15", "bone16"], "target": "target7", "bendPositive": false}, {"name": "target8", "order": 5, "bones": ["bone16", "bone17"], "target": "target8", "bendPositive": false}], "transform": [{"name": "bone191", "order": 4, "bones": ["bone20"], "target": "bone19", "x": 12.14, "y": -14.74, "rotateMix": -0.2, "translateMix": -0.2, "scaleMix": -0.2, "shearMix": -0.2}], "skins": [{"name": "default", "attachments": {"rootcut": {"rootcut": {"type": "clipping", "end": "rootcut", "vertexCount": 4, "vertices": [-66.05, 102.6, -65.4, -33.51, 68.77, -36.08, 69.42, 103.88], "color": "ce3a3aff"}}, "jianzhuls01": {"jianzhuls01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [335.81, 84.96, -390.19, 82.74, -391.32, 452.73, 334.67, 454.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 726, "height": 370}}, "jianzhuls02": {"jianzhuls02": {"type": "mesh", "uvs": [0.62817, 0, 0.71103, 0.01311, 0.80679, 0.07669, 0.87871, 0.17928, 0.89966, 0.30058, 0.89111, 0.33692, 0.97931, 0.46166, 1, 0.5714, 1, 0.59229, 0.9815, 0.69262, 0.92445, 0.77965, 0.82197, 0.84383, 0.65456, 0.84317, 0.46848, 0.96709, 0.29679, 1, 0.26387, 1, 0.10342, 0.9496, 0.01162, 0.87859, 0, 0.82183, 0, 0.8183, 0.02122, 0.77177, 0.12451, 0.77226, 0.2612, 0.83268, 0.43527, 0.80152, 0.23055, 0.6157, 0.2118, 0.43468, 0.28716, 0.28183, 0.37254, 0.13035, 0.44216, 0.04108, 0.56058, 0, 0.65005, 0.086, 0.6594, 0.22721, 0.66174, 0.40372, 0.65005, 0.6023, 0.59624, 0.73248, 0.51903, 0.83839, 0.45118, 0.89575, 0.27103, 0.91782, 0.09322, 0.85824], "triangles": [32, 5, 6, 33, 25, 32, 32, 25, 26, 32, 26, 27, 31, 27, 28, 31, 28, 30, 32, 27, 31, 32, 6, 33, 28, 29, 30, 29, 0, 30, 32, 31, 5, 5, 31, 4, 31, 3, 4, 31, 2, 3, 2, 30, 1, 2, 31, 30, 30, 0, 1, 33, 10, 11, 35, 34, 12, 11, 12, 34, 23, 24, 34, 11, 34, 33, 10, 33, 7, 8, 9, 10, 33, 6, 7, 34, 24, 33, 7, 8, 10, 24, 25, 33, 14, 36, 13, 13, 35, 12, 13, 36, 35, 22, 23, 36, 36, 23, 35, 35, 23, 34, 15, 37, 14, 14, 37, 36, 15, 16, 37, 17, 38, 16, 16, 38, 37, 38, 22, 37, 37, 22, 36, 17, 18, 38, 38, 19, 20, 38, 21, 22, 38, 20, 21, 38, 18, 19], "vertices": [1, 32, 149.89, 0.26, 1, 1, 32, 146.48, -21.75, 1, 1, 32, 128.94, -47.34, 1, 2, 31, 190.9, -41.63, 0.00094, 32, 100.35, -66.76, 0.99906, 2, 31, 159.21, -55.35, 0.03988, 32, 66.33, -72.74, 0.96012, 2, 31, 148.75, -55.65, 0.0841, 32, 56.09, -70.6, 0.9159, 2, 31, 120.47, -86.87, 0.37674, 32, 21.34, -94.41, 0.62326, 2, 31, 91.91, -99.73, 0.59382, 32, -9.43, -100.28, 0.40618, 2, 31, 86.22, -101.16, 0.62827, 32, -15.3, -100.35, 0.37173, 2, 31, 57.68, -103.29, 0.79254, 32, -43.55, -95.8, 0.20746, 2, 31, 30.27, -94.61, 0.90565, 32, -68.19, -80.99, 0.09435, 3, 30, 124.03, -43.69, 0, 31, 6.15, -72.68, 0.97834, 32, -86.56, -54.06, 0.02166, 2, 30, 84.76, -23.05, 0.16709, 31, -4.51, -29.62, 0.83291, 1, 30, 24.95, -31.17, 1, 2, 29, 63.52, -26.71, 0.8905, 30, -19.68, -18.37, 0.1095, 2, 29, 54.99, -28.57, 0.98297, 30, -27.42, -14.34, 0.01703, 1, 29, 10.43, -23.8, 1, 1, 29, -17.59, -9.49, 1, 1, 29, -24, 5.43, 1, 1, 29, -24.21, 6.4, 1, 1, 29, -21.5, 20.38, 1, 1, 29, 5.27, 26.08, 1, 2, 29, 44.28, 17.21, 0.91277, 30, -6.35, 27.69, 0.08723, 4, 29, 87.48, 35.6, 0.00159, 30, 38.62, 14.16, 0.85782, 31, -7.36, 29.59, 0.12528, 32, -75.96, 48.56, 0.0153, 3, 30, 14.6, 85.52, 0.15257, 31, 30.02, 94.96, 0.39133, 32, -24.42, 103.46, 0.4561, 3, 30, 33.67, 132.94, 0.06313, 31, 78.13, 112.21, 0.20091, 32, 26.38, 109.06, 0.73596, 3, 30, 71.21, 161.82, 0.01753, 31, 124.66, 103.33, 0.05078, 32, 69.57, 89.63, 0.9317, 3, 30, 110.93, 189.14, 0.00116, 31, 171.46, 91.79, 0.00071, 32, 112.42, 67.54, 0.99812, 2, 30, 138.88, 202.87, 0, 32, 137.73, 49.4, 1, 1, 32, 149.67, 18.17, 1, 1, 32, 125.8, -5.84, 1, 1, 32, 86.15, -8.82, 1, 2, 31, 115.7, -1.29, 0.00163, 32, 36.57, -10.06, 0.99837, 2, 31, 60.83, -11.92, 0.99921, 32, -19.27, -7.65, 0.00079, 1, 31, 21.88, -7.03, 1, 1, 30, 53.52, -5.27, 1, 1, 30, 30.13, -11.27, 1, 1, 29, 51.92, -5.61, 1, 1, 29, 2.32, 0.7, 1], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76], "width": 265, "height": 281}}, "jianzhuls03": {"jianzhuls03": {"type": "mesh", "uvs": [0.64753, 0, 0.87464, 0.06739, 0.97292, 0.16038, 1, 0.28015, 1, 0.28546, 0.96713, 0.40287, 0.85497, 0.52786, 0.75672, 0.63735, 0.78511, 0.67606, 0.69789, 0.83677, 0.55798, 0.8886, 0.28247, 0.89542, 0.12692, 0.88033, 0.05569, 0.83116, 0.03459, 0.78228, 0.10303, 0.61893, 0.16905, 0.56933, 0.24258, 0.40462, 0.24917, 0.33355, 0.12215, 0.24675, 0.15223, 0.15548, 0.27294, 0.0629, 0.38744, 0.0137, 0.57621, 0, 0.58174, 0.16886, 0.61039, 0.33756, 0.55309, 0.52338, 0.45642, 0.70186, 0.2201, 0.77765], "triangles": [12, 28, 11, 10, 11, 27, 11, 28, 27, 10, 27, 9, 12, 13, 28, 8, 27, 7, 8, 9, 27, 13, 14, 28, 14, 15, 28, 15, 16, 28, 28, 16, 27, 27, 26, 7, 27, 16, 26, 7, 26, 6, 16, 17, 26, 6, 26, 25, 5, 6, 25, 26, 17, 25, 25, 17, 18, 5, 25, 4, 18, 24, 25, 4, 25, 3, 2, 24, 1, 2, 25, 24, 24, 20, 21, 21, 22, 24, 19, 20, 18, 24, 18, 20, 25, 2, 3, 22, 23, 24, 24, 0, 1, 24, 23, 0], "vertices": [1, 16, -18.9, -16.45, 1, 2, 16, -25.8, 21.3, 0.98022, 17, -21.59, 114.2, 0.01978, 2, 16, -16.76, 45.71, 0.92196, 17, 4.44, 114.63, 0.07804, 2, 16, 3.48, 64.35, 0.80406, 17, 29.14, 102.53, 0.19594, 2, 16, 4.48, 65.02, 0.79893, 17, 30.13, 101.84, 0.20107, 2, 16, 29.45, 75.64, 0.62403, 17, 49.08, 82.42, 0.37597, 3, 16, 62.7, 77, 0.32564, 17, 62.42, 51.93, 0.67042, 18, -51.92, -4.84, 0.00394, 3, 16, 91.82, 78.2, 0.05571, 17, 74.1, 25.23, 0.67379, 18, -25.25, 6.92, 0.2705, 3, 16, 96.67, 86.76, 0.0188, 17, 83.83, 23.81, 0.47387, 18, -23.86, 16.65, 0.50733, 2, 17, 106.02, -8.14, 0.0018, 18, 8.03, 38.93, 0.9982, 1, 18, 32.56, 36.25, 1, 1, 18, 68.54, 13.2, 1, 2, 17, 63.53, -86.4, 0.01913, 18, 86.41, -3.35, 0.98087, 2, 17, 48.06, -89.07, 0.05725, 18, 89.13, -18.81, 0.94275, 2, 17, 37.09, -85.41, 0.09871, 18, 85.49, -29.79, 0.90129, 2, 17, 12.73, -55.51, 0.46129, 18, 55.65, -54.23, 0.53871, 2, 17, 9.35, -40.67, 0.66806, 18, 40.83, -57.65, 0.33194, 3, 16, 92.35, -17.41, 0.69817, 17, -14.81, -9.94, 0.29693, 18, 10.17, -81.9, 0.00489, 2, 16, 78.38, -25.54, 0.99579, 17, -27.46, 0.12, 0.00421, 1, 16, 72.99, -52.87, 1, 1, 16, 53.18, -60.54, 1, 1, 16, 25.31, -56.71, 1, 1, 16, 6.15, -48.2, 1, 1, 16, -12.74, -25.63, 1, 1, 16, 18.62, -3.57, 1, 2, 16, 47.94, 21.46, 0.82161, 17, 5.3, 45.53, 0.17839, 2, 16, 87.92, 37.58, 0.10194, 17, 34.83, 14.13, 0.89806, 2, 17, 59.5, -21.33, 0.15461, 18, 21.35, -7.56, 0.84539, 2, 17, 52.67, -61.22, 0.07727, 18, 61.26, -14.28, 0.92273], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 44, 48, 48, 50, 50, 52, 10, 12, 12, 14, 52, 54, 54, 56], "width": 155, "height": 227}}, "jianzhuls04": {"jianzhuls04": {"type": "mesh", "uvs": [0.84553, 0.04265, 0.96056, 0.1478, 1, 0.37468, 1, 0.43929, 0.93392, 0.54731, 0.8294, 0.71817, 0.80735, 0.76251, 0.76198, 0.86925, 0.69706, 0.93665, 0.5096, 0.97124, 0.23974, 0.93735, 0.18836, 0.89798, 0.18283, 0.8337, 0.20438, 0.77131, 0.2803, 0.72622, 0.2623, 0.62196, 0.27755, 0.56172, 0.24074, 0.44753, 0.1987, 0.31708, 0.28568, 0.13274, 0.42137, 0.03664, 0.64029, 0, 0.56489, 0.13529, 0.53611, 0.30753, 0.57208, 0.50906, 0.55769, 0.63652, 0.53851, 0.73815, 0.46898, 0.82944, 0.31552, 0.84666], "triangles": [10, 28, 9, 28, 27, 9, 9, 27, 8, 10, 11, 28, 8, 27, 7, 11, 12, 28, 27, 26, 7, 12, 13, 28, 13, 14, 28, 28, 14, 27, 27, 14, 26, 7, 26, 6, 6, 26, 5, 26, 25, 5, 26, 14, 25, 14, 15, 25, 5, 25, 4, 15, 16, 25, 25, 24, 4, 25, 16, 24, 4, 24, 3, 16, 17, 24, 17, 23, 24, 24, 23, 2, 3, 24, 2, 23, 1, 2, 22, 0, 1, 17, 18, 23, 1, 23, 22, 18, 19, 23, 22, 21, 0, 23, 19, 22, 22, 19, 20, 22, 20, 21], "vertices": [1, 21, -12.1, 68.57, 1, 1, 21, 18.97, 78.96, 1, 2, 21, 73.65, 66.77, 0.87458, 22, -41.44, 52.76, 0.12542, 2, 21, 88.55, 61.44, 0.75103, 22, -26.01, 56.29, 0.24897, 2, 21, 109.56, 41.58, 0.35064, 22, 2.38, 50.86, 0.64936, 3, 21, 142.78, 10.16, 0.00056, 22, 47.29, 42.26, 0.98118, 23, -42.43, 19.03, 0.01826, 2, 22, 58.75, 40.9, 0.89947, 23, -32.92, 25.55, 0.10053, 2, 22, 86.02, 38.95, 0.43346, 23, -11.11, 42.04, 0.56654, 2, 22, 104.67, 31.5, 0.17495, 23, 7.84, 48.71, 0.82505, 2, 22, 120.29, 1.23, 0.00169, 23, 39.53, 36.21, 0.99831, 1, 23, 73.11, 1.62, 1, 2, 22, 115.4, -57.89, 0.00038, 23, 74.77, -11.5, 0.99962, 3, 21, 131.11, -106.52, 0.00037, 22, 100.27, -62.35, 0.01168, 23, 66.32, -24.82, 0.98795, 3, 21, 117.99, -97.8, 0.00482, 22, 84.52, -62.07, 0.05211, 23, 54.28, -34.98, 0.94307, 3, 21, 112.09, -81.5, 0.03387, 22, 70.77, -51.5, 0.20962, 23, 36.98, -36.08, 0.75652, 3, 21, 86.97, -75.88, 0.18718, 22, 46.58, -60.29, 0.47704, 23, 24.56, -58.62, 0.33578, 3, 21, 73.98, -68.38, 0.35235, 22, 31.59, -60.97, 0.47143, 23, 13.72, -69, 0.17622, 3, 21, 45.45, -65.06, 0.73181, 22, 5.76, -73.52, 0.23616, 23, 2.55, -95.45, 0.03203, 3, 21, 12.87, -61.27, 0.935, 22, -23.74, -87.86, 0.06346, 23, -10.21, -125.68, 0.00154, 2, 21, -24.5, -31.64, 0.9993, 22, -71.18, -83.01, 0.0007, 1, 21, -38.63, -1.23, 1, 1, 21, -34.11, 38.08, 1, 1, 21, -7.37, 14.42, 1, 2, 21, 30.66, -4.56, 0.99739, 22, -39.26, -30.5, 0.00261, 3, 21, 79.29, -15.23, 0.36859, 22, 7.45, -13.31, 0.6254, 23, -35.82, -49.03, 0.00601, 3, 21, 107.84, -28.13, 0.01894, 22, 38.46, -8.81, 0.94364, 23, -15.45, -25.23, 0.03742, 3, 21, 130.14, -39.7, 0.00162, 22, 63.48, -6.55, 0.61402, 23, 1.89, -7.05, 0.38435, 1, 23, 24.93, 3.88, 1, 3, 21, 141.96, -85.6, 0.00045, 22, 98.15, -38.88, 0.00861, 23, 49.27, -8.55, 0.99093], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 40, 42, 18, 20, 14, 16, 18, 16, 42, 0, 42, 44, 44, 46, 46, 48, 32, 34, 34, 36, 6, 8, 8, 10, 48, 50, 50, 52, 52, 54, 54, 56], "width": 176, "height": 245}}, "jianzhuls05": {"jianzhuls05": {"type": "mesh", "uvs": [0.3044, 0, 0.44537, 0.0122, 0.61351, 0.05587, 0.69834, 0.14457, 0.75105, 0.28288, 0.75062, 0.46104, 0.69214, 0.55879, 0.58802, 0.62253, 0.44992, 0.66118, 0.25585, 0.64469, 0.12024, 0.58783, 0.03671, 0.50161, 0.00223, 0.32072, 0.0048, 0.1638, 0.11869, 0.03628, 0.25216, 0, 0.12604, 0.19783, 0.16743, 0.32132, 0.30785, 0.39657, 0.50149, 0.40622, 0.67148, 0.32711, 0.25759, 0.13609, 0.39063, 0.14766, 0.56505, 0.18625], "triangles": [23, 2, 3, 20, 23, 3, 20, 3, 4, 5, 20, 4, 6, 20, 5, 19, 20, 6, 23, 1, 2, 22, 1, 23, 19, 22, 23, 19, 23, 20, 7, 19, 6, 8, 18, 19, 8, 19, 7, 14, 15, 21, 16, 14, 21, 13, 14, 16, 12, 13, 16, 17, 16, 21, 12, 16, 17, 11, 12, 17, 10, 11, 17, 21, 15, 0, 22, 0, 1, 21, 0, 22, 18, 21, 22, 17, 21, 18, 18, 22, 19, 10, 17, 18, 9, 10, 18, 9, 18, 8], "vertices": [4, 2, 53.73, -89.14, 0.46028, 3, 20.64, -88.9, 0.34869, 4, -53.58, 94.81, 0.18925, 5, -97.63, 117.57, 0.00178, 4, 2, -0.39, -98.67, 0.32512, 3, -24.14, -120.76, 0.06568, 4, 1.3, 97.65, 0.54288, 5, -43.35, 108.98, 0.06632, 4, 2, -67.02, -101.61, 0.04069, 3, -83.04, -152.04, 0.00072, 4, 67.78, 92.42, 0.54555, 5, 20.61, 90.1, 0.41305, 3, 2, -105.36, -83.8, 0.00202, 4, 103.66, 70.05, 0.27776, 5, 51.08, 60.79, 0.72022, 2, 4, 128.87, 31.53, 0.02295, 5, 67.77, 17.89, 0.97705, 2, 4, 134.95, -21.21, 0.01509, 5, 62.79, -34.97, 0.98491, 3, 2, -132.42, 36.65, 0.00402, 4, 115.78, -52.81, 0.18762, 5, 37.5, -61.92, 0.80836, 3, 2, -97.61, 64.75, 0.07025, 4, 77.79, -76.44, 0.56151, 5, -4.56, -77.17, 0.36824, 3, 2, -48.18, 88.74, 0.35476, 4, 25.8, -94.2, 0.60182, 5, -59.1, -83.78, 0.04343, 3, 2, 26.31, 101.94, 0.79946, 3, -86.22, 71.87, 0.06382, 4, -49.75, -98.19, 0.13672, 3, 2, 81.58, 98.06, 0.65293, 3, -34.64, 92.11, 0.33718, 4, -104.12, -87.57, 0.00989, 2, 2, 119.26, 80.84, 0.39711, 3, 6.77, 92.75, 0.60289, 3, 2, 145.12, 31.68, 0.06194, 3, 51.26, 59.47, 0.93806, 4, -159.07, -13.92, 0, 2, 3, 80.05, 22.62, 1, 4, -163.58, 32.63, 0, 3, 2, 121.32, -61.43, 0.05983, 3, 69.76, -34.84, 0.93343, 4, -124.05, 75.58, 0.00674, 4, 2, 73.47, -84.3, 0.37977, 3, 36.38, -76.04, 0.51712, 4, -73.77, 92.42, 0.10311, 5, -117.87, 119.41, 0, 1, 3, 37.1, 0.65, 1, 2, 2, 82.67, 16.55, 0.32313, 3, 1.36, 18.98, 0.67687, 3, 2, 24.28, 25.32, 0.96897, 3, -55.14, 1.8, 0.0001, 4, -38.35, -22.39, 0.03093, 3, 2, -49.57, 10.16, 0.02976, 4, 36.79, -16.39, 0.96473, 5, -32.24, -9.93, 0.0055, 2, 4, 99.68, 14.8, 0.01906, 5, 35.75, 7.57, 0.98094, 3, 2, 61.75, -45.42, 0.49954, 3, 9.1, -45.97, 0.43096, 4, -66.9, 52.4, 0.0695, 4, 2, 10.67, -54.39, 0.48929, 3, -33.17, -76.02, 0.07453, 4, -15.1, 55.06, 0.41965, 5, -68.21, 70.71, 0.01653, 4, 2, -57.96, -59.39, 0.0328, 3, -93.01, -110.02, 0.00065, 4, 53.63, 51.62, 0.63227, 5, -1.68, 53.12, 0.33427], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46], "width": 389, "height": 298}}, "jianzhuls06": {"jianzhuls06": {"type": "mesh", "uvs": [0.40365, 0.47534, 0.37395, 0.27203, 0.41611, 0.08406, 0.64098, 0.11305, 0.84842, 0.30991, 0.89194, 0.46901, 0.89184, 0.5606, 0.93974, 0.65379, 0.93997, 0.7829, 0.91581, 0.8694, 0.87878, 0.92031, 0.8212, 0.94849, 0.60119, 0.94868, 0.55944, 0.9215, 0.55905, 0.92168, 0.53353, 0.85891, 0.58848, 0.75302, 0.51134, 0.71349, 0.45825, 0.61154, 0.55603, 0.33348, 0.67636, 0.45962, 0.695, 0.59354, 0.68992, 0.68852, 0.74415, 0.78507, 0.70856, 0.8567, 0.63738, 0.86916], "triangles": [22, 6, 7, 23, 7, 8, 23, 22, 7, 24, 16, 23, 25, 15, 16, 24, 25, 16, 9, 23, 8, 10, 24, 23, 9, 10, 23, 13, 15, 25, 14, 15, 13, 11, 24, 10, 12, 13, 25, 12, 25, 24, 12, 24, 11, 18, 20, 21, 21, 17, 18, 22, 17, 21, 16, 17, 22, 6, 22, 21, 16, 22, 23, 19, 2, 3, 19, 3, 4, 1, 2, 19, 20, 19, 4, 20, 4, 5, 0, 1, 19, 0, 19, 20, 6, 20, 5, 21, 20, 6, 18, 0, 20], "vertices": [3, 6, 51.41, -56.14, 0.37265, 7, -52.07, -28.04, 0.04335, 5, -12.15, -14.53, 0.584, 2, 6, -3.13, -66.89, 0.416, 5, -14.52, 41.01, 0.584, 2, 6, -54.62, -59.53, 0.112, 5, 0.55, 90.79, 0.888, 2, 6, -50.2, -3.16, 0.416, 5, 55.59, 77.9, 0.584, 3, 6, -0.11, 51.66, 0.93287, 7, -15.51, 85.7, 0.00175, 8, -102.64, -58.24, 0.06538, 3, 6, 42.26, 65.11, 0.60951, 7, 24.53, 66.39, 0.08226, 8, -74.16, -24.1, 0.30823, 3, 6, 67.04, 66.6, 0.27535, 7, 43.57, 50.46, 0.1039, 8, -54.08, -9.51, 0.62076, 3, 6, 91.52, 80.05, 0.05227, 7, 70.6, 43.44, 0.00095, 8, -40.68, 14.99, 0.94679, 3, 6, 126.44, 82.25, 0.00058, 7, 97.5, 21.05, 0.58387, 8, -12.43, 35.63, 0.41555, 2, 7, 111.63, 1.41, 0.61119, 8, 10.06, 44.56, 0.38881, 2, 7, 116.32, -14.51, 0.42558, 8, 26.64, 45.22, 0.57442, 2, 7, 112.99, -30.41, 0.24233, 8, 41.26, 38.13, 0.75767, 2, 7, 77.92, -72.49, 0.00253, 8, 73.54, -6.13, 0.99747, 2, 7, 65.6, -75.75, 0.01976, 8, 73.7, -18.87, 0.98024, 2, 7, 65.58, -75.86, 0.01966, 8, 73.8, -18.92, 0.98034, 2, 7, 48.45, -69.83, 0.08305, 8, 63.79, -34.07, 0.91695, 2, 7, 35.19, -40.94, 0.63536, 8, 32.53, -39.9, 0.36464, 3, 6, 114.19, -25.43, 0.01423, 7, 14.65, -48.82, 0.92288, 8, 35.18, -61.73, 0.06289, 3, 6, 87.42, -40.32, 0.32512, 7, -15.03, -41.26, 0.67384, 8, 20.62, -88.68, 0.00104, 1, 6, 10.72, -20.62, 1, 3, 6, 43.01, 11.38, 0.94389, 7, -11.83, 26.82, 0.0306, 8, -44.63, -69, 0.02551, 3, 6, 78.95, 18.23, 0.07896, 7, 19.01, 7.12, 0.88864, 8, -18.02, -43.89, 0.0324, 2, 7, 37.96, -10.35, 0.96676, 8, 3.54, -29.76, 0.03324, 2, 7, 66.7, -16.75, 0.0911, 8, 16.74, -3.45, 0.9089, 1, 8, 37.65, 0.81, 1, 2, 7, 67.15, -51.76, 0.0412, 8, 50.81, -11.53, 0.9588], "hull": 19, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 28, 30, 30, 32, 32, 34, 34, 36, 26, 28, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 4, 6, 2, 38, 4, 2, 2, 0, 36, 0], "width": 249, "height": 271}}, "jianzhuls07": {"jianzhuls07": {"type": "mesh", "uvs": [0.75198, 0.00905, 0.94321, 0.12038, 0.99207, 0.20611, 0.99235, 0.40431, 0.99242, 0.45141, 0.9925, 0.51029, 0.99273, 0.67068, 0.86546, 0.87862, 0.7319, 0.95643, 0.27629, 0.96524, 0.08619, 0.9284, 0.03016, 0.82007, 0.15179, 0.70181, 0.13309, 0.63374, 0.17285, 0.47719, 0.1836, 0.43486, 0.16293, 0.40101, 0.09507, 0.28209, 0.09596, 0.1234, 0.19626, 0.04668, 0.39463, 0.0091, 0.48619, 0.15894, 0.55709, 0.40625, 0.56046, 0.46317, 0.55371, 0.66338, 0.48619, 0.83807, 0.28701, 0.86162], "triangles": [9, 25, 8, 9, 26, 25, 9, 10, 26, 8, 25, 7, 10, 11, 26, 25, 24, 7, 25, 26, 12, 26, 11, 12, 25, 12, 24, 12, 13, 24, 7, 24, 6, 24, 5, 6, 13, 14, 24, 24, 14, 23, 24, 23, 5, 23, 4, 5, 23, 14, 15, 15, 22, 23, 23, 22, 4, 22, 3, 4, 15, 16, 22, 16, 21, 22, 22, 2, 3, 2, 21, 1, 1, 21, 0, 21, 2, 22, 16, 17, 21, 21, 17, 18, 18, 19, 21, 19, 20, 21, 21, 20, 0], "vertices": [1, 11, -9.32, 49.42, 1, 1, 11, 22.64, 60.51, 1, 2, 11, 41.89, 57.97, 0.99149, 12, -55.14, 34.76, 0.00851, 2, 11, 80.26, 39.43, 0.5997, 12, -12.68, 38.31, 0.4003, 2, 11, 89.38, 35.02, 0.3906, 12, -2.58, 39.15, 0.6094, 2, 11, 100.77, 29.51, 0.16791, 12, 10.03, 40.2, 0.83209, 3, 11, 131.82, 14.51, 0.00017, 12, 44.39, 43.07, 0.96148, 13, -45.4, 12.05, 0.03834, 2, 12, 90.26, 30.9, 0.1876, 13, -7.39, 40.45, 0.8124, 2, 12, 108.31, 15.65, 0.0107, 13, 15.78, 45.12, 0.9893, 1, 13, 64.24, 15.14, 1, 2, 12, 108.96, -65.29, 5e-05, 13, 79.63, -4.62, 0.99995, 3, 11, 108.29, -107.78, 0.0036, 12, 86.32, -74.19, 0.01902, 13, 72.55, -27.89, 0.97738, 3, 11, 92.03, -83.01, 0.06206, 12, 59.73, -61.13, 0.19574, 13, 45.81, -40.63, 0.74221, 3, 11, 77.84, -78.74, 0.15131, 12, 45.34, -64.67, 0.33138, 13, 39.64, -54.11, 0.51732, 3, 11, 49.71, -59.6, 0.52266, 12, 11.39, -62.49, 0.32256, 13, 16.86, -79.37, 0.15478, 3, 11, 42.11, -54.42, 0.67565, 12, 2.2, -61.9, 0.23573, 13, 10.69, -86.2, 0.08863, 3, 11, 34.43, -53.57, 0.78817, 12, -4.84, -65.08, 0.16029, 13, 8.81, -93.69, 0.05155, 3, 11, 7.72, -50.07, 0.9634, 12, -29.62, -75.64, 0.03028, 13, 1.71, -119.68, 0.00633, 2, 11, -22.94, -35.09, 0.99988, 12, -63.63, -78.34, 0.00012, 1, 11, -32.32, -16.62, 1, 1, 11, -28.78, 9.22, 1, 1, 11, 5.2, 5.48, 1, 3, 11, 56.92, -9.72, 0.87813, 12, -7.77, -15.88, 0.11576, 13, -31.58, -65.45, 0.00611, 3, 11, 68.12, -14.68, 0.50244, 12, 4.39, -14.45, 0.4828, 13, -25.15, -55.03, 0.01476, 3, 11, 106.49, -34.2, 0.01427, 12, 47.35, -11.75, 0.78574, 13, -0.59, -19.67, 0.19999, 1, 13, 27.25, 6.92, 1, 3, 11, 130.32, -82.78, 0.00036, 12, 92.58, -41.46, 0.00205, 13, 50.78, -2.66, 0.99759], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 4, 6, 6, 8, 8, 10, 10, 12, 26, 28, 28, 30, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 125, "height": 215}}, "jianzhuls08": {"jianzhuls08": {"type": "mesh", "uvs": [0.82855, 0.02523, 0.87414, 0.06614, 0.89855, 0.07128, 0.9464, 0.04168, 0.95968, 0.08727, 0.93771, 0.18714, 0.89003, 0.25075, 0.92973, 0.32443, 0.9738, 0.34063, 1, 0.34056, 1, 0.37467, 0.97593, 0.45827, 0.90822, 0.52227, 0.9236, 0.58019, 0.93958, 0.62626, 0.98719, 0.65849, 0.96466, 0.71969, 0.90336, 0.75238, 0.867, 0.75749, 0.84332, 0.86372, 0.77546, 0.89345, 0.70139, 0.89287, 0.64409, 0.85929, 0.62406, 0.85347, 0.51941, 0.92583, 0.42763, 0.93502, 0.33877, 0.9053, 0.18963, 0.99622, 0.18111, 0.94662, 0.15542, 0.90914, 0.08699, 0.83834, 0.08704, 0.71732, 0.06306, 0.69016, 0, 0.65615, 0.01392, 0.58769, 0.06212, 0.52298, 0.06936, 0.52039, 0.04028, 0.42783, 0.04036, 0.35693, 0.05379, 0.33324, 0.1031, 0.34172, 0.12526, 0.31938, 0.16698, 0.21964, 0.24098, 0.0992, 0.32117, 0.08174, 0.42306, 0.01383, 0.46339, 0.01336, 0.45728, 0.09462, 0.52818, 0.04866, 0.67571, 0.00265, 0.78114, 0.00262, 0.067, 0.38035, 0.10328, 0.43133, 0.14603, 0.44013, 0.25745, 0.44892, 0.38959, 0.45243, 0.55931, 0.43485, 0.71996, 0.41903, 0.85081, 0.40848, 0.94668, 0.39617, 0.98068, 0.36071, 0.2463, 0.33853, 0.3893, 0.33853, 0.54587, 0.31176, 0.69997, 0.27328, 0.80845, 0.2532, 0.92927, 0.08422, 0.89352, 0.12772, 0.82695, 0.12438, 0.75051, 0.12103, 0.67408, 0.13776, 0.53231, 0.18461, 0.41766, 0.21472, 0.31534, 0.22476, 0.23521, 0.22476, 0.41026, 0.08422, 0.14398, 0.54934, 0.25986, 0.57443, 0.4004, 0.57611, 0.57546, 0.55938, 0.72216, 0.54097, 0.84297, 0.51922, 0.93543, 0.67315, 0.84421, 0.66645, 0.73325, 0.68653, 0.58285, 0.72668, 0.4041, 0.72334, 0.25, 0.7133, 0.12056, 0.65474, 0.07371, 0.62463, 0.03673, 0.63634, 0.15341, 0.77623, 0.2052, 0.83164, 0.33467, 0.8411, 0.43327, 0.84246, 0.52689, 0.82759, 0.61951, 0.78974, 0.73206, 0.79515, 0.79181, 0.75865], "triangles": [36, 52, 76, 76, 52, 53, 36, 37, 52, 53, 40, 41, 37, 51, 52, 51, 40, 52, 53, 52, 40, 37, 38, 51, 38, 39, 51, 51, 39, 40, 77, 55, 78, 78, 56, 79, 78, 55, 56, 76, 54, 77, 77, 54, 55, 76, 53, 54, 54, 62, 55, 56, 62, 63, 56, 55, 62, 53, 61, 54, 54, 61, 62, 53, 41, 61, 41, 42, 61, 42, 74, 61, 61, 73, 62, 61, 74, 73, 62, 72, 63, 62, 73, 72, 73, 74, 43, 72, 44, 75, 72, 73, 44, 74, 42, 43, 73, 43, 44, 72, 75, 47, 75, 44, 45, 31, 32, 88, 33, 90, 32, 32, 89, 88, 32, 90, 89, 33, 34, 90, 88, 89, 76, 90, 34, 89, 34, 35, 89, 35, 36, 89, 89, 36, 76, 31, 88, 91, 77, 88, 76, 92, 26, 28, 28, 26, 27, 28, 29, 92, 30, 91, 29, 29, 91, 92, 30, 31, 91, 93, 26, 92, 25, 26, 94, 26, 93, 94, 93, 86, 94, 92, 87, 93, 93, 87, 86, 92, 91, 87, 91, 88, 87, 87, 77, 86, 77, 78, 86, 86, 78, 79, 87, 88, 77, 21, 97, 20, 20, 98, 19, 20, 97, 98, 19, 98, 18, 97, 84, 98, 25, 94, 24, 24, 95, 23, 24, 94, 95, 21, 22, 97, 97, 22, 96, 95, 96, 23, 22, 23, 96, 94, 86, 95, 95, 85, 96, 95, 86, 85, 97, 96, 84, 96, 85, 84, 86, 79, 85, 84, 85, 79, 98, 83, 18, 98, 84, 83, 18, 83, 17, 17, 82, 16, 17, 83, 82, 16, 82, 15, 82, 14, 15, 82, 83, 14, 83, 13, 14, 13, 81, 12, 13, 83, 81, 84, 79, 80, 84, 80, 83, 80, 81, 83, 80, 79, 57, 80, 57, 81, 81, 58, 12, 12, 59, 11, 12, 58, 59, 81, 57, 58, 10, 59, 60, 10, 11, 59, 58, 7, 59, 58, 6, 7, 58, 65, 6, 59, 8, 60, 59, 7, 8, 60, 9, 10, 60, 8, 9, 79, 56, 57, 56, 64, 57, 56, 63, 64, 57, 65, 58, 57, 64, 65, 65, 68, 6, 6, 67, 5, 6, 68, 67, 67, 66, 5, 5, 66, 4, 68, 1, 67, 67, 2, 66, 67, 1, 2, 68, 0, 1, 68, 69, 0, 66, 3, 4, 66, 2, 3, 72, 71, 63, 64, 71, 70, 64, 63, 71, 64, 69, 65, 64, 70, 69, 65, 69, 68, 72, 47, 71, 47, 48, 71, 71, 48, 70, 70, 49, 69, 70, 48, 49, 69, 50, 0, 69, 49, 50, 75, 45, 47, 47, 45, 46], "vertices": [2, 52, 106.2, 10.26, 0.07429, 53, 16.74, 27.34, 0.92571, 1, 53, 41.64, 16.9, 1, 1, 53, 53.83, 17.31, 1, 1, 53, 75.05, 32.29, 1, 1, 53, 84.59, 17.21, 1, 4, 52, 126.3, -67.05, 0.00019, 53, 80.69, -20.52, 0.91959, 54, 96.3, 78.02, 0.00969, 55, 42.33, 78.15, 0.07053, 4, 52, 94.65, -76.5, 0.02249, 53, 61.86, -47.66, 0.45543, 54, 77.8, 50.65, 0.0774, 55, 23.92, 50.73, 0.44469, 4, 52, 99.44, -109.38, 0.0004, 53, 86.13, -70.36, 0.05016, 54, 102.33, 28.25, 0.00128, 55, 48.53, 28.41, 0.94816, 2, 53, 108.61, -72.1, 0.00273, 55, 71.04, 27.02, 0.99727, 1, 55, 83.71, 29.65, 1, 1, 55, 86.21, 17.49, 1, 3, 55, 80.69, -14.72, 0.98014, 60, 105.1, 80.51, 0.00109, 61, 57.56, 76.11, 0.01876, 4, 54, 106.18, -44.44, 0.0013, 55, 52.62, -44.27, 0.53456, 60, 79.55, 48.75, 0.07349, 61, 29.54, 46.51, 0.39066, 3, 55, 64.31, -63.39, 0.14558, 60, 92.77, 30.65, 0.01905, 61, 41.25, 27.41, 0.83536, 2, 55, 75.41, -78.23, 0.0171, 61, 52.39, 12.59, 0.9829, 2, 55, 100.82, -84.98, 1e-05, 61, 77.8, 5.88, 0.99999, 2, 61, 71.42, -18.19, 0.99418, 63, 109, 56.26, 0.00582, 3, 60, 100.81, -32.31, 0.00118, 61, 44.18, -35.99, 0.87989, 63, 81.54, 38.81, 0.11892, 3, 60, 84.1, -39.15, 0.0381, 61, 26.97, -41.46, 0.59866, 63, 64.26, 33.57, 0.36324, 3, 60, 83.75, -79.54, 0.00083, 61, 23.36, -81.69, 0.03885, 63, 60.13, -6.62, 0.96032, 2, 62, 83.11, -12.09, 0.00256, 63, 29.28, -23.62, 0.99744, 3, 62, 51.67, -30.82, 0.59584, 63, -6.69, -30.37, 0.40375, 42, -94.7, 215.87, 0.00041, 3, 62, 21.12, -34.98, 0.9578, 63, -36.8, -23.76, 0.01158, 42, -86.19, 186.23, 0.03062, 3, 62, 11.55, -38.27, 0.92609, 42, -81.55, 177.24, 0.07315, 44, -177.31, 141.27, 0.00076, 4, 62, -19.11, -87.54, 0.64628, 42, -28.4, 153.98, 0.34232, 43, -0.39, 169.43, 0.00549, 44, -124.7, 165.76, 0.00591, 4, 62, -56.19, -113.82, 0.44943, 42, 2.94, 121.05, 0.50926, 43, 14.45, 126.47, 0.03368, 44, -79.28, 167.49, 0.00762, 5, 54, -141.92, -236.84, 5e-05, 62, -99.37, -127.24, 0.20477, 42, 22.42, 80.25, 0.60974, 43, 15.24, 81.26, 0.18109, 44, -35.79, 155.13, 0.00434, 3, 62, -145.35, -193.64, 0.0035, 42, 94.74, 44.29, 0.02344, 43, 66.09, 18.52, 0.97306, 3, 62, -158.28, -180.36, 0.00275, 42, 83.45, 29.58, 0.02022, 43, 49.72, 9.83, 0.97703, 3, 43, 39.78, -5.93, 0.99781, 46, 41.81, 109.29, 0.00028, 47, -3.97, 109.31, 0.00191, 4, 42, 82.47, -31.36, 0.06563, 43, 23.52, -45.21, 0.78775, 46, 70.15, 77.6, 0.05092, 47, 24.2, 77.47, 0.09571, 4, 42, 48.4, -59.29, 0.16016, 43, -19.07, -56.47, 0.15448, 46, 61.83, 34.35, 0.19866, 47, 15.64, 34.26, 0.4867, 4, 42, 48.27, -74.72, 0.05303, 43, -25.59, -70.45, 0.04499, 46, 71.6, 22.4, 0.05626, 47, 25.35, 22.27, 0.84572, 1, 47, 53.51, 4.08, 1, 2, 47, 41.93, -19.03, 0.95973, 45, 20.29, 68.92, 0.04027, 3, 46, 60.58, -37.45, 0.05029, 47, 14.01, -37.52, 0.4909, 45, 19.18, 35.45, 0.45881, 4, 46, 56.89, -37.7, 0.05838, 47, 10.31, -37.75, 0.38426, 44, 92.25, 10.39, 0.001, 45, 17.23, 32.3, 0.55636, 2, 47, 17.88, -73.59, 0.01017, 45, 50.82, 17.7, 0.98983, 1, 45, 68.52, -1.07, 1, 3, 52, -284.54, 90.15, 0.00037, 44, 97.52, -57.96, 0.00698, 45, 69.63, -11.9, 0.99265, 3, 52, -264.45, 76.03, 0.01173, 44, 73.28, -54.01, 0.20081, 45, 49.81, -26.4, 0.78746, 3, 52, -250.97, 78.1, 0.03352, 44, 62.06, -61.75, 0.44153, 45, 47.45, -39.83, 0.52495, 3, 52, -215.78, 100.55, 0.11493, 44, 40.17, -97.3, 0.70272, 45, 57.43, -80.36, 0.18236, 3, 52, -162.96, 122.2, 0.22881, 44, 2.08, -139.82, 0.71111, 45, 61, -137.33, 0.06008, 6, 52, -124.97, 109.3, 0.33169, 54, -209.84, 55.28, 0.00068, 60, -243.84, 121.05, 9e-05, 62, -261.69, 124.94, 1e-05, 44, -37.73, -144.77, 0.64266, 45, 36.59, -169.17, 0.02488, 3, 52, -68.92, 107.61, 0.48443, 44, -88.91, -167.69, 0.51266, 45, 17.01, -221.72, 0.0029, 3, 52, -51.23, 98.44, 0.49655, 44, -108.83, -167.15, 0.50144, 45, 2.66, -235.54, 0.00201, 7, 52, -67.73, 73.71, 0.55233, 54, -143.01, 64, 0.00514, 60, -178.01, 135.46, 0.00147, 62, -201.7, 155.66, 0.00141, 42, -242.84, -61.66, 2e-05, 44, -104.76, -137.7, 0.4379, 45, -15.47, -211.98, 0.00174, 5, 52, -28.95, 72.12, 0.80856, 54, -111.99, 87.34, 0.00043, 60, -149.1, 161.37, 0.0002, 62, -180.35, 188.08, 0.0003, 44, -140.36, -153.17, 0.19051, 2, 52, 43.3, 52.84, 0.99615, 44, -213.79, -167.33, 0.00385, 2, 52, 89.34, 28.49, 0.56535, 53, -7.81, 31.06, 0.43465, 3, 52, -286.79, 71.94, 0.0005, 44, 91.6, -40.6, 0.00981, 45, 53.1, -3.93, 0.9897, 3, 52, -279.63, 47.16, 0.0004, 44, 74.35, -21.41, 0.02021, 45, 27.33, -2.76, 0.97939, 3, 52, -262.45, 34.45, 0.00325, 44, 53.36, -17.47, 0.48874, 45, 9.79, -14.95, 0.50802, 7, 52, -215.3, 5.88, 0.01732, 54, -214.21, -81.96, 0.00212, 60, -236.46, -16.06, 0.00101, 62, -219.59, -5.76, 0.00185, 42, -80.53, -56.18, 0.00091, 44, -1.53, -12.32, 0.96836, 45, -32.39, -50.45, 0.00843, 7, 52, -158.2, -25.78, 0.1158, 54, -149.97, -70.28, 0.06127, 60, -173.46, 1.07, 0.03793, 62, -163.04, 26.87, 0.08557, 42, -120.94, -4.9, 0.09151, 46, -103.13, -32.18, 0.00088, 44, -66.73, -8.72, 0.60703, 6, 52, -81.1, -59.33, 0.17159, 54, -69.06, -47.4, 0.29317, 60, -94.8, 30.8, 0.17162, 62, -94.56, 75.67, 0.12246, 42, -179.07, 55.86, 0.042, 44, -150.74, -12.14, 0.19917, 6, 54, 7.58, -26.03, 0.76772, 55, -46.04, -26.18, 0.00327, 60, -20.27, 58.65, 0.21779, 62, -29.59, 121.6, 0.00402, 42, -233.86, 113.56, 0.00085, 44, -230.25, -15.09, 0.00635, 4, 54, 70.18, -9.46, 0.01584, 55, 16.5, -9.41, 0.9311, 60, 40.68, 80.52, 0.0347, 61, -6.64, 81.32, 0.01836, 2, 53, 99.21, -94.46, 0.00098, 55, 61.99, 4.52, 0.99902, 2, 53, 113.31, -78.65, 0.00087, 55, 75.84, 20.54, 0.99913, 6, 52, -201.38, 43.97, 0.08879, 54, -227.57, -43.67, 0.00331, 60, -253.05, 20.95, 0.00118, 62, -245.06, 25.8, 0.001, 44, 2.55, -52.67, 0.81721, 45, -0.78, -75.85, 0.08851, 7, 52, -138.93, 10.94, 0.23554, 54, -158.33, -29.67, 0.0513, 60, -185.25, 40.82, 0.02317, 62, -184.58, 62.3, 0.03345, 42, -152.91, -31.3, 0.01967, 44, -68.05, -50.16, 0.63377, 45, -52.1, -124.4, 0.0031, 6, 52, -66.01, -16.62, 0.44432, 54, -84.45, -4.8, 0.21227, 60, -113.77, 71.93, 0.05729, 62, -123.39, 110.6, 0.03978, 42, -209.5, 22.31, 0.01402, 44, -145.69, -57.16, 0.23233, 7, 52, 7.83, -39.84, 0.4131, 53, -28.86, -73.2, 0.00384, 54, -12.61, 24.01, 0.57994, 60, -44.66, 106.78, 0.00015, 62, -65.45, 161.92, 0.0006, 42, -268.61, 72.28, 0.00013, 44, -222.27, -68.46, 0.00226, 4, 52, 58.61, -58.44, 0.18323, 53, 22.44, -56.04, 0.2413, 54, 38.47, 41.79, 0.40102, 55, -15.37, 41.74, 0.17444, 1, 53, 69.62, 15.51, 1, 4, 52, 117.12, -37.72, 0.00076, 53, 55.22, -3.34, 0.98858, 54, 70.61, 94.89, 0.00234, 55, 16.59, 94.94, 0.00832, 4, 52, 88.62, -21.27, 0.06081, 53, 22.68, -8.27, 0.90987, 54, 38.14, 89.57, 0.01793, 55, -15.87, 89.51, 0.01139, 4, 52, 55.81, -2.53, 0.99079, 53, -14.65, -14.1, 0.00406, 54, 0.89, 83.28, 0.0047, 55, -53.1, 83.11, 0.00045, 2, 52, 19.59, 9.74, 0.99859, 44, -211.24, -118.21, 0.00141, 6, 52, -50.29, 27.42, 0.70459, 54, -100.18, 39.24, 0.04156, 60, -133.22, 114.46, 0.00918, 62, -153.04, 146.76, 0.00748, 42, -241.03, -12.22, 0.00167, 44, -140.64, -103.65, 0.23552, 7, 52, -105.48, 44.22, 0.41288, 54, -153.53, 17.27, 0.0292, 60, -184.49, 88, 0.00975, 62, -195.87, 108.12, 0.01029, 42, -196.63, -49.06, 0.00252, 44, -83.65, -94.7, 0.53101, 45, -31.3, -166.77, 0.00435, 7, 52, -151.87, 64.63, 0.24634, 54, -202.35, 3.68, 0.00817, 60, -231.97, 70.28, 0.00285, 62, -237.26, 78.87, 0.00293, 42, -161.74, -85.82, 0.00011, 44, -33.01, -92.84, 0.70546, 45, 2.91, -129.38, 0.03414, 6, 52, -186.85, 83.14, 0.16006, 54, -241.15, -4.17, 0.00104, 60, -269.95, 59.14, 0.00028, 62, -271.15, 58.42, 9e-05, 44, 6.55, -94.25, 0.73705, 45, 31.66, -102.17, 0.10148, 6, 52, -86.49, 87.92, 0.46677, 54, -166.52, 63.11, 0.00215, 60, -201.36, 132.56, 0.00059, 62, -223.54, 146.9, 0.00055, 44, -81.68, -142.3, 0.52496, 45, 4, -198.76, 0.00497, 4, 46, 22.68, -20.4, 0.53388, 47, -23.81, -20.27, 0.0209, 44, 55.78, 22.23, 0.32779, 45, -16.79, 14.63, 0.11744, 7, 52, -235.61, -35.06, 0.00059, 54, -203.99, -126.51, 0.0026, 60, -222.46, -59.57, 0.00194, 62, -194.96, -44.26, 0.01573, 42, -45.96, -26.27, 0.19884, 46, -31.82, -0.64, 0.3552, 44, -1.1, 33.39, 0.42509, 7, 52, -174.53, -68.07, 0.03888, 54, -135.82, -113.35, 0.04853, 60, -155.67, -40.63, 0.04118, 62, -135.21, -8.92, 0.19243, 42, -89.53, 27.79, 0.28844, 46, -99.89, 13.04, 0.01141, 44, -70.47, 36.46, 0.37914, 6, 52, -95.24, -103.13, 0.04432, 54, -52.26, -90.25, 0.16768, 60, -74.4, -10.46, 0.27109, 62, -64.31, 40.98, 0.31807, 42, -149.09, 90.78, 0.07281, 44, -157.1, 33.44, 0.12604, 7, 52, -28.05, -131.1, 0.00033, 54, 17.44, -69.32, 0.1762, 55, -36.04, -69.44, 0.00847, 60, -6.74, 16.36, 0.80083, 62, -5.72, 84.15, 0.00858, 42, -200.23, 142.56, 0.0009, 44, -229.77, 29.31, 0.00469, 4, 54, 74.37, -49.74, 0.06614, 55, 20.82, -49.67, 0.35689, 60, 48.31, 40.74, 0.33098, 61, -2.25, 41.06, 0.24599, 2, 61, 53.84, -4.53, 0.99679, 63, 91.6, 70.14, 0.00321, 4, 60, 63.97, -10.51, 0.07036, 61, 9.22, -11.29, 0.85632, 62, 69.5, 76.2, 0.0022, 63, 46.9, 63.96, 0.07112, 4, 60, 13.43, -32.94, 0.57834, 61, -42.96, -29.57, 0.00187, 62, 26.34, 41.62, 0.32246, 63, -5.52, 46.36, 0.09734, 6, 52, -120.5, -158.66, 0.00303, 54, -36.62, -149.22, 0.0174, 60, -53.76, -67.87, 0.04824, 62, -29.72, -9.28, 0.79326, 42, -104.32, 132.23, 0.10523, 44, -158.6, 94.43, 0.03283, 8, 52, -197.98, -116.29, 0.00468, 54, -123.41, -165.52, 0.01544, 60, -138.84, -91.54, 0.01413, 62, -105.96, -53.86, 0.31697, 42, -49.25, 63.19, 0.52716, 43, -57.05, 95.5, 0.00754, 46, -91.58, 66.02, 0.00137, 44, -70.39, 90.08, 0.11271, 7, 54, -198.75, -177.02, 0.00027, 60, -212.92, -109.45, 0.00019, 62, -173.02, -90.06, 0.0059, 42, -3.79, 2.02, 0.97776, 43, -41.09, 20.98, 0.00051, 46, -17.51, 48.08, 0.00745, 44, 5.56, 83.73, 0.00792, 4, 42, 20.29, -60.93, 0.09111, 43, -45.32, -46.3, 0.02421, 46, 41.27, 15.1, 0.57508, 47, -5.02, 15.12, 0.30959, 2, 47, 15.56, -0.11, 0.9997, 45, -10.41, 58.39, 0.0003, 3, 42, 41.38, -97.2, 0.00019, 43, -41.2, -88.04, 0.00016, 47, 34.31, 0.53, 0.99965, 4, 42, 44.18, -20.34, 0.44848, 43, -6.73, -19.29, 0.37216, 46, 33.67, 61.58, 0.10541, 47, -12.37, 61.65, 0.07395, 3, 62, -169.72, -138.38, 0.00504, 42, 43.55, 12.23, 0.36444, 43, 6.21, 10.61, 0.63052, 6, 54, -148.54, -214.33, 0.0008, 60, -159.69, -142.32, 0.00035, 62, -113.17, -108.28, 0.19144, 42, 5.64, 63.87, 0.66668, 43, -6.83, 73.32, 0.12903, 44, -34.6, 131.7, 0.01169, 7, 52, -205.52, -161.36, 0.00015, 54, -100.7, -205.16, 0.00259, 60, -112.81, -129.09, 0.00143, 62, -71.22, -83.54, 0.44589, 42, -24.87, 101.83, 0.50191, 43, -18.83, 120.53, 0.02464, 44, -83.26, 133.92, 0.02338, 7, 52, -162.11, -178.2, 0.0003, 54, -56.44, -190.7, 0.00316, 60, -69.96, -110.89, 0.00169, 62, -34.41, -55.01, 0.69169, 42, -58.39, 134.16, 0.28316, 43, -35.9, 163.85, 0.00214, 44, -129.67, 130.15, 0.01787, 5, 52, -115.22, -187.42, 6e-05, 54, -14.32, -168.13, 0.00082, 62, -2.36, -19.57, 0.94188, 42, -98.06, 160.79, 0.05395, 44, -175.88, 118.01, 0.0033, 4, 60, 23.98, -71.05, 0.03511, 61, -35.52, -68.41, 0.0017, 62, 46.26, 7.46, 0.47857, 63, 1.42, 7.43, 0.48462, 4, 60, 48.57, -50, 0.19489, 61, -9.31, -49.41, 0.12723, 62, 64.67, 34.09, 0.05709, 63, 27.87, 26.09, 0.6208], "hull": 51, "edges": [0, 100, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 66, 68, 52, 54, 78, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 18, 120, 120, 118, 82, 122, 122, 124, 124, 126, 126, 128, 128, 130, 6, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 72, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 30, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196], "width": 494, "height": 364}}, "jianzhuls09": {"jianzhuls09": {"type": "mesh", "uvs": [0.20846, 0, 0.35799, 0.01802, 0.5294, 0.12495, 0.73364, 0.2476, 0.94152, 0.38598, 1, 0.63757, 0.93058, 0.85143, 0.67893, 0.99609, 0.34705, 1, 0.09905, 0.90175, 0, 0.65959, 0, 0.35453, 0.09176, 0.10293, 0.20846, 0.09664, 0.28505, 0.29792, 0.45646, 0.5558, 0.5987, 0.81054], "triangles": [3, 15, 14, 10, 14, 15, 15, 5, 16, 4, 15, 3, 5, 15, 4, 6, 16, 5, 9, 10, 15, 8, 9, 15, 7, 16, 6, 16, 8, 15, 8, 16, 7, 13, 0, 1, 12, 0, 13, 14, 13, 1, 14, 1, 2, 12, 13, 14, 11, 12, 14, 3, 14, 2, 10, 11, 14], "vertices": [1, 36, 71.58, 1.59, 1, 2, 36, 64.84, -15.07, 0.99543, 35, 110.51, -32.9, 0.00457, 2, 36, 45.57, -31.26, 0.87193, 35, 87.42, -42.94, 0.12807, 2, 36, 23.24, -50.72, 0.42646, 35, 60.48, -55.24, 0.57354, 2, 36, -1.3, -70.07, 0.10467, 35, 31.45, -66.8, 0.89533, 2, 36, -36.67, -68.38, 0.00421, 35, -1.98, -55.11, 0.99579, 1, 35, -23.17, -32.97, 1, 1, 35, -25.1, 2.97, 1, 1, 35, -5.45, 37.23, 1, 2, 36, -45.96, 44.49, 0.00041, 35, 21.25, 55.73, 0.99959, 2, 36, -10.67, 47.79, 0.16058, 35, 56.01, 48.85, 0.83942, 2, 36, 30.16, 37.55, 0.85436, 35, 92.24, 27.41, 0.14564, 1, 36, 61.18, 18.51, 1, 1, 36, 58.64, 4.83, 1, 1, 36, 29.49, 2.75, 1, 1, 35, 40.67, -5.19, 1, 1, 35, 1.8, -1.85, 1], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 0, 26, 26, 28, 28, 30, 30, 32], "width": 119, "height": 138}}, "jianzhuls010": {"jianzhuls010": {"type": "mesh", "uvs": [0.64885, 0.00135, 0.66095, 0.04619, 0.65551, 0.0717, 0.8154, 0.07254, 0.84861, 0.06354, 0.89242, 0.03134, 0.92481, 0.04421, 0.94414, 0.10089, 0.92981, 0.17236, 0.85681, 0.22563, 0.80532, 0.24822, 0.76655, 0.26176, 0.83941, 0.37152, 0.90235, 0.46634, 0.92831, 0.47253, 0.97014, 0.45836, 0.99738, 0.4785, 0.99741, 0.55599, 0.94629, 0.60796, 0.96573, 0.62769, 0.98912, 0.66144, 0.99326, 0.70829, 0.98001, 0.7338, 0.93562, 0.7188, 0.86611, 0.7443, 0.76912, 0.76174, 0.77452, 0.78684, 0.75802, 0.82678, 0.70467, 0.86804, 0.6214, 0.86153, 0.5993, 0.88756, 0.52018, 0.94059, 0.43155, 1, 0.39549, 1, 0.31354, 0.98119, 0.21909, 0.92459, 0.14748, 0.88168, 0.13382, 0.83877, 0.14626, 0.78397, 0.18495, 0.75549, 0.06942, 0.70806, 0.00275, 0.65423, 0.00217, 0.59405, 0.03907, 0.54096, 0.04273, 0.42586, 0.07553, 0.35059, 0.15809, 0.27851, 0.14298, 0.14628, 0.22829, 0.04326, 0.28711, 0.03249, 0.28316, 0.0776, 0.27947, 0.11962, 0.29333, 0.12876, 0.38046, 0.06298, 0.62397, 0, 0.15129, 0.45729, 0.29507, 0.47651, 0.47445, 0.47555, 0.64989, 0.45729, 0.75673, 0.41501, 0.9559, 0.51783, 0.89786, 0.55435, 0.78179, 0.55435, 0.94139, 0.66486, 0.88204, 0.6735, 0.76464, 0.66582, 0.70001, 0.81284, 0.68682, 0.76479, 0.61955, 0.72828, 0.63538, 0.81284, 0.5747, 0.75422, 0.3808, 0.94449, 0.33727, 0.88395, 0.32936, 0.81861, 0.36234, 0.74942, 0.29638, 0.17286, 0.34519, 0.23339, 0.40191, 0.27856, 0.40586, 0.33045, 0.36893, 0.37561, 0.30034, 0.39964, 0.22516, 0.36408, 0.17767, 0.31219, 0.4085, 0.21033, 0.45994, 0.12769, 0.56547, 0.06331, 0.63142, 0.02679, 0.9005, 0.08061, 0.82532, 0.13154, 0.64065, 0.16613, 0.52985, 0.22475, 0.48369, 0.28048, 0.14338, 0.57068, 0.3043, 0.59951, 0.4916, 0.59567, 0.65516, 0.57164, 0.10908, 0.64179, 0.22384, 0.67735, 0.37948, 0.68119, 0.54832, 0.66197, 0.67099, 0.64948, 0.24098, 0.07965, 0.2146, 0.15364, 0.22911, 0.24397, 0.29638, 0.31988], "triangles": [34, 72, 71, 31, 32, 71, 34, 71, 33, 32, 33, 71, 71, 72, 31, 72, 35, 73, 34, 35, 72, 31, 72, 30, 30, 73, 70, 38, 73, 37, 36, 37, 73, 35, 36, 73, 30, 72, 73, 73, 38, 39, 29, 69, 28, 28, 66, 27, 69, 66, 28, 15, 16, 60, 60, 16, 17, 15, 60, 14, 61, 60, 17, 18, 61, 17, 88, 89, 2, 88, 2, 3, 8, 88, 7, 7, 88, 87, 88, 4, 87, 88, 3, 4, 87, 6, 7, 4, 5, 87, 87, 5, 6, 9, 88, 8, 11, 90, 89, 11, 89, 10, 10, 89, 88, 9, 10, 88, 89, 85, 2, 85, 86, 2, 2, 86, 1, 86, 85, 54, 86, 0, 1, 86, 54, 0, 85, 53, 54, 84, 85, 89, 90, 84, 89, 84, 53, 85, 70, 73, 74, 74, 98, 70, 98, 99, 70, 30, 70, 29, 29, 70, 69, 27, 66, 26, 73, 39, 74, 69, 67, 66, 26, 67, 25, 26, 66, 67, 69, 70, 67, 67, 70, 68, 68, 100, 67, 67, 65, 25, 67, 100, 65, 25, 65, 24, 39, 40, 97, 39, 97, 74, 97, 40, 96, 70, 99, 68, 74, 97, 98, 24, 64, 23, 24, 65, 64, 21, 22, 63, 68, 99, 100, 22, 23, 63, 23, 64, 63, 63, 20, 21, 40, 41, 96, 97, 93, 98, 98, 94, 99, 98, 93, 94, 96, 92, 97, 97, 92, 93, 65, 62, 64, 63, 64, 18, 64, 62, 61, 65, 100, 62, 63, 19, 20, 63, 18, 19, 18, 64, 61, 100, 99, 95, 41, 42, 96, 99, 94, 95, 100, 95, 62, 96, 42, 92, 92, 42, 43, 92, 56, 93, 93, 57, 94, 93, 56, 57, 94, 57, 95, 57, 58, 95, 95, 58, 62, 92, 55, 56, 92, 43, 55, 61, 62, 13, 58, 59, 62, 62, 59, 13, 43, 44, 55, 56, 79, 57, 56, 80, 79, 56, 55, 80, 91, 58, 57, 59, 12, 13, 57, 79, 78, 57, 78, 91, 58, 91, 59, 44, 45, 55, 55, 81, 80, 81, 45, 82, 81, 55, 45, 59, 91, 11, 11, 91, 90, 59, 11, 12, 81, 104, 80, 80, 104, 79, 79, 104, 78, 81, 82, 104, 45, 46, 82, 104, 77, 78, 78, 77, 91, 82, 103, 104, 104, 76, 77, 104, 103, 76, 82, 46, 103, 90, 91, 83, 91, 77, 83, 77, 76, 83, 103, 47, 102, 103, 46, 47, 103, 75, 76, 75, 102, 52, 52, 102, 51, 102, 75, 103, 76, 75, 83, 83, 84, 90, 83, 75, 84, 75, 52, 84, 51, 102, 101, 102, 47, 101, 47, 48, 101, 52, 53, 84, 51, 101, 50, 50, 101, 49, 49, 101, 48, 60, 13, 14, 60, 61, 13], "vertices": [1, 49, 106.37, -0.53, 1, 2, 26, 400.44, -132.22, 0.15897, 49, 93.55, -20.1, 0.84103, 3, 26, 388.29, -127.03, 0.3688, 49, 82.97, -28.02, 0.59329, 50, 74.1, 49.48, 0.03792, 1, 51, 54.6, 27.15, 1, 1, 51, 67.8, 26.52, 1, 1, 51, 89.3, 35.27, 1, 1, 51, 97.83, 24.47, 1, 1, 51, 93.06, -5.06, 1, 1, 51, 73.78, -36.61, 1, 2, 50, 86.56, -58.65, 0.92466, 51, 38.03, -51, 0.07534, 1, 50, 64.26, -56.32, 1, 2, 26, 283.75, -143.08, 0.27027, 50, 48.54, -53.14, 0.72973, 1, 26, 222.58, -155.51, 1, 2, 26, 169.73, -166.25, 0.824, 56, 28.68, 40.88, 0.176, 3, 26, 164.27, -174.85, 0.49244, 56, 38.26, 37.43, 0.20218, 57, 30.15, 20.6, 0.30538, 1, 57, 46.04, 13.99, 1, 1, 57, 45.3, -0.47, 1, 1, 57, 16.04, -27.27, 1, 3, 26, 95.43, -164.19, 0.53581, 56, 42.92, -32.07, 0.20019, 57, -16.46, -31.18, 0.264, 2, 26, 83.84, -168.72, 0.624, 58, 53.59, 14.11, 0.376, 3, 26, 64.94, -172.9, 0.58099, 58, 59.02, -4.47, 0.184, 59, 20.75, 20.3, 0.23501, 2, 26, 41.32, -168.46, 0.592, 59, 38.16, 3.73, 0.408, 2, 26, 29.88, -160.44, 0.592, 59, 43.4, -9.22, 0.408, 3, 26, 41.42, -146.3, 0.47872, 58, 34.05, -29.71, 0.12, 59, 26.02, -14.82, 0.40128, 3, 26, 35.19, -117.95, 0.39474, 58, 6.18, -37.83, 0.3719, 59, 15.84, -42, 0.23336, 3, 26, 35.48, -80.68, 0.80051, 58, -31.03, -40.02, 0.04749, 40, 19.92, 27.97, 0.152, 3, 26, 22.54, -79.46, 0.63283, 40, 31.54, 22.13, 0.13517, 41, -12.29, 19.08, 0.232, 2, 26, 4.24, -68.44, 0.528, 41, 8.92, 16.57, 0.472, 1, 41, 33.18, 0.64, 1, 4, 26, -0.39, -14.66, 0.5977, 40, 29.4, -46.57, 0.13478, 41, 35.29, -30.53, 0.13952, 37, 12.15, 104.02, 0.128, 3, 26, -11.26, -3.37, 0.22618, 37, 27.41, 100.47, 0.696, 38, 20.52, 99.85, 0.07782, 3, 37, 62.57, 81.19, 0.20419, 38, 46.26, 69.09, 0.2208, 39, 49.89, 50.13, 0.57501, 1, 39, 55.67, 5.58, 1, 1, 39, 47.91, -5.41, 1, 2, 38, 63.39, -8.88, 0.16, 39, 22.41, -24.83, 0.84, 3, 26, 5.45, 138.73, 0.0176, 38, 32.79, -42.71, 0.56, 39, -21.58, -36.89, 0.4224, 2, 37, 78.42, -60.18, 0.632, 38, 9.58, -68.35, 0.368, 1, 37, 59.24, -72.03, 1, 1, 37, 31.17, -76.6, 1, 2, 26, 92.49, 129.65, 0.768, 37, 12.73, -67.59, 0.232, 1, 26, 126.68, 165.39, 1, 1, 26, 159.54, 182.67, 1, 1, 26, 189.44, 175.25, 1, 1, 26, 212.38, 155.2, 1, 1, 26, 269.14, 139.3, 1, 1, 26, 303.45, 117.91, 1, 1, 26, 331.59, 78.94, 1, 1, 26, 398.58, 67.65, 1, 1, 26, 441.82, 23.77, 1, 1, 26, 441.73, 1.15, 1, 1, 26, 419.72, 8.29, 1, 1, 26, 399.21, 14.95, 1, 1, 26, 393.4, 11.1, 1, 2, 26, 417.99, -28.73, 0.52, 48, 94.91, 32.11, 0.48, 1, 49, 100.22, 6.46, 1, 1, 26, 243.53, 104.04, 1, 1, 26, 220.73, 54.52, 1, 1, 26, 204.65, -10.44, 1, 1, 26, 197.52, -76.15, 1, 1, 26, 208.64, -120.12, 1, 1, 57, 19.99, -2.66, 1, 2, 26, 126.49, -153.48, 0.664, 56, 25.68, -4.11, 0.336, 1, 26, 137.2, -111.53, 1, 3, 26, 67.65, -155.22, 0.62016, 58, 41.2, -2.95, 0.184, 59, 8.86, 6.93, 0.19584, 3, 26, 68.84, -132.67, 0.5735, 58, 18.62, -3.27, 0.2505, 59, -4.4, -11.34, 0.176, 1, 26, 83.48, -91.22, 1, 3, 26, 16.51, -49.23, 0.61306, 40, 26.2, -8.22, 0.21094, 41, 5.65, -5.98, 0.176, 1, 26, 41.57, -50.55, 1, 1, 26, 65.89, -30.87, 1, 3, 26, 22.48, -25.88, 0.69222, 40, 12.16, -27.82, 0.13978, 41, 9.83, -29.72, 0.168, 1, 26, 57.15, -11.37, 1, 2, 38, 45.8, 17.06, 0.32, 39, 21.53, 6.51, 0.68, 3, 37, 56.9, 7.27, 0.20429, 38, 14.08, 2.31, 0.70771, 39, -13.15, 11.12, 0.088, 1, 37, 26.14, -6.22, 1, 1, 26, 79.13, 64.77, 1, 1, 26, 371.24, 15.58, 1, 1, 26, 336.71, 5.61, 1, 1, 26, 309.07, -9.17, 1, 1, 26, 282.96, -4.03, 1, 1, 26, 263.96, 15.04, 1, 1, 26, 258.38, 42.87, 1, 1, 26, 282.95, 65.54, 1, 1, 26, 313.08, 76.13, 1, 1, 26, 342.31, -20.2, 1, 2, 26, 378.56, -49.25, 0.18714, 48, 68.8, -3.87, 0.81286, 2, 26, 400.76, -95.55, 0.23854, 49, 61.96, -1.47, 0.76146, 1, 49, 92.62, -5.3, 1, 1, 51, 82.17, 10.89, 1, 1, 51, 46.14, -2.08, 1, 2, 48, 63.81, -73.91, 0.13194, 50, 40.55, 14.23, 0.86806, 3, 26, 323.96, -62.23, 0.46973, 48, 25.74, -39.86, 0.45378, 50, -10.52, 15.17, 0.07649, 1, 26, 300.57, -38.48, 1, 1, 26, 188.01, 121.26, 1, 1, 26, 158.86, 66.76, 1, 1, 26, 143.48, -1.42, 1, 1, 26, 140.3, -63.58, 1, 1, 26, 155.89, 142.67, 1, 1, 26, 127.67, 105.7, 1, 1, 26, 111.4, 49.93, 1, 1, 26, 105.35, -13.52, 1, 1, 26, 100.23, -59.44, 1, 1, 26, 422.6, 23.79, 1, 1, 26, 388.32, 42.7, 1, 1, 26, 342.17, 48.9, 1, 1, 26, 298.31, 34.2, 1], "hull": 55, "edges": [0, 108, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 64, 66, 66, 68, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 102, 104, 104, 106, 106, 108, 22, 24, 24, 26, 88, 110, 110, 112, 112, 114, 114, 116, 116, 118, 30, 120, 120, 122, 122, 124, 44, 126, 126, 128, 128, 130, 56, 132, 132, 134, 134, 136, 58, 138, 138, 140, 64, 142, 60, 62, 62, 64, 142, 144, 144, 146, 146, 148, 68, 70, 70, 72, 104, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 154, 166, 166, 168, 168, 170, 170, 172, 10, 174, 174, 176, 176, 178, 178, 180, 180, 182, 86, 184, 184, 186, 186, 188, 188, 190, 192, 194, 194, 196, 196, 198, 198, 200, 98, 202, 98, 100, 100, 102, 202, 204, 204, 206, 206, 208], "width": 373, "height": 512}}, "jianzhuls011": {"jianzhuls011": {"type": "mesh", "uvs": [0.83652, 0.00699, 0.95045, 0.11635, 1, 0.41454, 0.93176, 0.74476, 0.78128, 0.93042, 0.63076, 0.99738, 0.3766, 0.86586, 0, 0.55487, 0, 0.44114, 0.05578, 0.28542, 0.29024, 0.11012, 0.5691, 0.00721, 0.78388, 0.22095, 0.65541, 0.42537, 0.50959, 0.67382], "triangles": [14, 7, 8, 14, 9, 13, 14, 8, 9, 14, 13, 3, 6, 7, 14, 4, 14, 3, 5, 6, 14, 4, 5, 14, 12, 11, 0, 12, 0, 1, 12, 1, 2, 13, 11, 12, 13, 12, 2, 10, 11, 13, 13, 9, 10, 3, 13, 2], "vertices": [1, 34, 69.72, 2.64, 1, 2, 34, 63.78, -17.24, 0.99818, 33, 106.37, -3.95, 0.00182, 2, 34, 31.25, -43.19, 0.75108, 33, 79.78, -35.96, 0.24892, 2, 34, -12.47, -58.61, 0.11699, 33, 40.09, -59.92, 0.88301, 2, 34, -44.07, -55.14, 0.00328, 33, 8.45, -62.93, 0.99672, 1, 33, -10.97, -55.04, 1, 1, 33, -18.57, -19.25, 1, 2, 34, -48.06, 55.35, 0.2078, 33, -17.87, 44.45, 0.7922, 2, 34, -34.47, 63.2, 0.29815, 33, -6.15, 54.9, 0.70185, 2, 34, -12.38, 67.92, 0.49259, 33, 14.53, 63.99, 0.50741, 2, 34, 23.23, 54.64, 0.88054, 33, 52.09, 58.22, 0.11946, 2, 34, 52.97, 31.57, 0.99997, 33, 85.89, 41.66, 3e-05, 2, 34, 40.86, -6.43, 0.99577, 33, 81.74, 1.99, 0.00423, 2, 34, 8.4, -6.64, 0.92318, 33, 50, -4.8, 0.07682, 1, 33, 12.28, -14.02, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 24, 24, 26, 26, 28, 4, 6], "width": 125, "height": 138}}, "jianzhuls013": {"jianzhuls012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-31.53, -136.45, 35.48, 81.48, 67.98, 71.49, 0.97, -146.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 225, "height": 40}, "jianzhuls013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-45.46, -130.66, 8.23, 79.59, 96.4, 57.08, 42.71, -153.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 217, "height": 91}}, "jianzhuls014": {"jianzhuls014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-76.17, -82.85, -40.3, 57.64, 63.37, 31.17, 27.5, -109.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 145, "height": 107}}}}], "animations": {"lingshou_appear": {"slots": {"jianzhuls013": {"attachment": [{"name": "jianzhuls013"}, {"time": 1.9333, "name": "jianzhuls012"}, {"time": 2.1, "name": "jianzhuls013"}]}}, "bones": {"bone2": {"rotate": [{"angle": 5.15, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.15}], "translate": [{"x": 1.63, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "x": 8.81, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.63}]}, "bone5": {"rotate": [{"angle": 0.56, "curve": 0.297, "c2": 0.22, "c3": 0.661, "c4": 0.65}, {"time": 0.6, "angle": -3.93, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1, "angle": -6.62, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 2.4333, "angle": 1.4, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 2.6667, "angle": 0.56}]}, "bone18": {"rotate": [{"angle": -1.48, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -1.95, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -1.48}]}, "bone21": {"rotate": [{"angle": 4.7, "curve": 0.313, "c2": 0.26, "c3": 0.757}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 5.24, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 2.6667, "angle": 4.7}]}, "bone22": {"rotate": [{"angle": 5.1, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.1, "angle": 5.24, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.1, "angle": 0.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.4333, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 2.6667, "angle": 5.1}]}, "bone23": {"rotate": [{"angle": 3.64, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 5.24, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 1.1, "angle": 2.79, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 1.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 3.64}]}, "bone24": {"rotate": [{"angle": 2.1, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667, "angle": 5.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.1, "angle": 4.27, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2.1, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "angle": 2.1}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.57, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone26": {"rotate": [{"angle": -2.88, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -8.57, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -2.88}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.57, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone28": {"rotate": [{"angle": -2.88, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -8.57, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -2.88}]}, "bone19": {"translate": [{"x": -0.67, "y": 1.85, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -2.01, "y": 5.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": -0.67, "y": 1.85}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone41": {"rotate": [{"angle": -1.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.08, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.49}]}, "bone42": {"rotate": [{"angle": -1.27, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -8.08, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": -1.27}]}, "bone43": {"rotate": [{"angle": -3.74, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 0.3, "angle": -1.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -8.08, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": -3.74}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone45": {"rotate": [{"angle": -1.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.08, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.49}]}, "bone46": {"rotate": [{"angle": -1.27, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -8.08, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": -1.27}]}, "bone47": {"rotate": [{"angle": -3.74, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 0.3, "angle": -1.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -8.08, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": -3.74}]}, "bone52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone53": {"rotate": [{"angle": -1.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.08, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.49}]}, "bone54": {"rotate": [{"angle": -1.27, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -8.08, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": -1.27}]}, "bone55": {"rotate": [{"angle": -3.74, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 0.3, "angle": -1.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -8.08, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": -3.74}]}, "bone36": {"rotate": [{"angle": -1.27, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -8.08, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": -1.27}]}, "bone37": {"rotate": [{"angle": -3.74, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 0.3, "angle": -1.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -8.08, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": -3.74}]}, "bone38": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone39": {"rotate": [{"angle": -1.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.08, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.49}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -12.88, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone51": {"rotate": [{"angle": -2.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -12.88, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.38}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 12, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone49": {"rotate": [{"angle": 2.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.21}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 12, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone30": {"rotate": [{"angle": 2.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.21}]}, "bone31": {"rotate": [{"angle": 1.89, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 12, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": 1.89}]}, "bone34": {"rotate": [{"angle": 1.89, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 12, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": 1.89}]}, "bone35": {"rotate": [{"angle": 5.56, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 0.3, "angle": 2.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 12, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": 5.56}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone33": {"rotate": [{"angle": -1.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.92}]}}}, "lingshou_disappear": {"slots": {"jianzhuls014": {"attachment": [{"name": null}]}, "jianzhuls03": {"attachment": [{"name": null}]}, "jianzhuls02": {"attachment": [{"name": null}]}, "jianzhuls06": {"attachment": [{"name": null}]}, "jianzhuls08": {"attachment": [{"name": null}]}, "jianzhuls010": {"attachment": [{"name": null}]}, "jianzhuls013": {"attachment": [{"name": null}]}, "jianzhuls07": {"attachment": [{"name": null}]}, "jianzhuls05": {"attachment": [{"name": null}]}, "jianzhuls09": {"attachment": [{"name": null}]}, "jianzhuls04": {"attachment": [{"name": null}]}, "jianzhuls011": {"attachment": [{"name": null}]}}}}}