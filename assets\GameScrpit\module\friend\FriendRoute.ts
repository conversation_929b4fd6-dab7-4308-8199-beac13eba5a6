import { UIFriendBelles } from "../../game/ui/friend/UIFriendBelles";
import { UIFriendFoster } from "../../game/ui/friend/UIFriendFoster";
import { UIFriendMain } from "../../game/ui/friend/UIFriendMain";
import { UIFriendPreview } from "../../game/ui/friend/UIFriendPreview";
import { UIFriendRollOfBelles } from "../../game/ui/friend/UIFriendRollOfBelles";
import { UIFriendSkillUpgrade } from "../../game/ui/friend/UIFriendSkillUpgrade";
import { UIFriendUseItem } from "../../game/ui/friend/UIFriendUseItem";
import { UIFriendXilianTips } from "../../game/ui/friend/UIFriendXilianTips";
import { UIHdFriendGoal } from "../friend_goal/src/prefab/ui/UIHdFriendGoal";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum FriendRouteItem {
  UIFriendMain = "UIFriendMain",

  UIFriendBelles = "UIFriendBelles",
  UIFriendFoster = "UIFriendFoster",
  UIFriendRollOfBelles = "UIFriendRollOfBelles",
  UIFriendSkillUpgrade = "UIFriendSkillUpgrade",
  UIFriendUseItem = "UIFriendUseItem",
  UIFriendPreview = "UIFriendPreview",
  UIFriendXilianTips = "UIFriendXilianTips",
}
export class FriendRoute {
  rotueTables: Recording[] = [
    { node: UIFriendMain, uiName: FriendRouteItem.UIFriendMain, keep: false, relevanceUIList: [] },
    { node: UIFriendBelles, uiName: FriendRouteItem.UIFriendBelles, keep: false, relevanceUIList: [] },
    { node: UIFriendFoster, uiName: FriendRouteItem.UIFriendFoster, keep: false, relevanceUIList: [] },
    { node: UIFriendRollOfBelles, uiName: FriendRouteItem.UIFriendRollOfBelles, keep: false, relevanceUIList: [] },
    { node: UIFriendSkillUpgrade, uiName: FriendRouteItem.UIFriendSkillUpgrade, keep: false, relevanceUIList: [] },
    { node: UIFriendUseItem, uiName: FriendRouteItem.UIFriendUseItem, keep: false, relevanceUIList: [] },
    { node: UIFriendPreview, uiName: FriendRouteItem.UIFriendPreview, keep: false, relevanceUIList: [] },
    { node: UIFriendXilianTips, uiName: FriendRouteItem.UIFriendXilianTips, keep: false, relevanceUIList: [] },
  ];
  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
