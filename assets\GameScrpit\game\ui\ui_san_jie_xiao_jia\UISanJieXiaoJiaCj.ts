import {
  _decorator,
  Component,
  Graphics,
  instantiate,
  isValid,
  Label,
  Mask,
  Node,
  ScrollView,
  Sprite,
  UITransform,
  v3,
} from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import { CityModule } from "../../../module/city/CityModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { RewardMessage } from "../../net/protocol/Comm";
import ToolExt from "../../common/ToolExt";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { Sleep } from "../../GameDefine";
import ResMgr from "../../../lib/common/ResMgr";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

enum TaskState {
  已完成 = 0,
  未完成 = 1,
  已领取 = 2,
}
class Task {
  index: number;
  dbId: number;
  lv: number;
  taskName: string;
  reward: Array<Array<number>>;
  state: TaskState;
  bgRes: string;
}

@ccclass("UISanJieXiaoJiaCj")
export class UISanJieXiaoJiaCj extends UINode {
  protected _openAct: boolean = true; //打开动作
  private _tickIndex: number = 0;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SAN_JIE_XIAO_JIA}?prefab/ui/UISanJieXiaoJiaCj`;
  }

  private _list: Array<Task> = [];

  protected onEvtShow(): void {
    this._list = this.getCjList();
    this.loadList();
    // this.loadReward03List();
    // this.loadReward02List();
    // this.loadReward01List();
  }

  private on_click_btn_get(event) {
    let type = event.target["task_type"];
    if (type == "id") {
      let id = event.target["sever_param"];
      CityModule.api.takeSmallHomeBuildLvReward(id, (res: RewardMessage) => {
        AudioMgr.instance.playEffect(1802);
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList, transformList: res.transformList });
        this._list = this.getCjList();
        this.loadList();
      });
    } else if (type == "lv") {
      let lv = event.target["sever_param"];
      CityModule.api.takeAllCityLevelReward(lv, (res: RewardMessage) => {
        AudioMgr.instance.playEffect(1802);
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList, transformList: res.transformList });
        this._list = this.getCjList();
        this.loadList();
      });
    }
  }

  private async loadList() {
    if (this.getNode("content_main").children.length > this._list.length) {
      let num = this.getNode("content_main").children.length - this._list.length;
      for (let i = 0; i < num; i++) {
        let node = this.getNode("content_main").children[0];
        node.removeFromParent();
        node.destroy();
      }
    }

    for (let i = 0; i < this._list.length; i++) {
      await Sleep(0.1);
      if (isValid(this.node) == false) return;

      let info = this._list[i];

      let node = this.getNode("content_main").children[i];
      if (!node) {
        node = ToolExt.clone(this.getNode("donate_1"), this);
        node.setParent(this.getNode("content_main"));
        node.setPosition(v3(0, 0, 0));
        node.active = true;
      }

      ResMgr.loadImage(`${BundleEnum.BUNDLE_COMMON_UI}?atlas_imgs/${info.bgRes}`, node.getComponent(Sprite), this);

      node.getChildByPath("head/item_title").getComponent(Label).string = info.taskName;

      let content = node.getChildByPath("ScrollView/view/content");

      if (content.children.length > info.reward.length) {
        let num = content.children.length - info.reward.length;
        for (let i = 0; i < num; i++) {
          let node = content.children[0];
          node.removeFromParent();
          node.destroy();
        }
      }

      for (let j = 0; j < info.reward.length; j++) {
        let reward01 = info.reward[j];
        let item = content.children[j];
        if (!item) {
          item = instantiate(this.getNode("Item"));
          item.setParent(content);
          item.setPosition(v3(0, 0, 0));
          item.active = true;
        }
        FmUtils.setItemNode(item, reward01[0], reward01[1]);
      }

      let bool = true;
      if (info.reward.length <= 3) bool = false;

      let view = node.getChildByPath("ScrollView/view");
      view.getComponent(Mask).enabled = bool;
      view.getComponent(Graphics).enabled = bool;

      let ScrollViewNode = node.getChildByPath("ScrollView");
      ScrollViewNode.getComponent(ScrollView).enabled = bool;

      if (info.dbId > 0) {
        node.getChildByName("btn_get")["task_type"] = "id";
        node.getChildByName("btn_get")["sever_param"] = info.dbId;
        this.LvTask(node, info.dbId);
      } else if (info.lv > 0) {
        node.getChildByName("btn_get")["task_type"] = "lv";
        node.getChildByName("btn_get")["sever_param"] = info.lv;
        this.allLvTask(node, info.lv);
      }
    }
  }

  private allLvTask(node: Node, lv: number) {
    if (CityModule.data.cityAggregateMessage.smallHomeAllLevelReward.includes(lv) == true) {
      node.getChildByPath("done").active = true;
      node.getChildByPath("btn_get").active = false;
      node.getChildByPath("btn_go").active = false;
      return;
    }

    let lvBool = this.isCityLv(lv);
    node.getChildByPath("done").active = false;
    node.getChildByPath("btn_get").active = lvBool;
    node.getChildByPath("btn_go").active = !lvBool;
  }

  private LvTask(node: Node, dbId: number) {
    if (CityModule.data.cityAggregateMessage.smallHomeRewardList.includes(dbId) == true) {
      node.getChildByPath("done").active = true;
      node.getChildByPath("btn_get").active = false;
      node.getChildByPath("btn_go").active = false;
      return;
    }

    let id = Math.floor(dbId % 100) + 100;
    let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[id] || 0;

    if (lv < Math.floor(dbId / 100)) {
      node.getChildByPath("done").active = false;
      node.getChildByPath("btn_get").active = false;
      node.getChildByPath("btn_go").active = true;
    } else {
      node.getChildByPath("done").active = false;
      node.getChildByPath("btn_get").active = true;
      node.getChildByPath("btn_go").active = false;
    }
  }

  private getCjList() {
    let list = [];

    let db = JsonMgr.instance.jsonList.c_home;
    let task1: Task = {
      index: 0,
      dbId: -1,
      lv: 2,
      taskName: "所有建筑全部2级奖励",
      reward: db[101].reward03List,
      state: TaskState.未完成,
      bgRes: "bg_9g_tanchaungnei_huang",
    };
    let lvBool = this.isCityLv(2);
    if (CityModule.data.cityAggregateMessage.smallHomeAllLevelReward.includes(2) == true) {
      task1.state = TaskState.已领取;
    } else if (lvBool == true) {
      task1.state = TaskState.已完成;
    }

    let task2: Task = {
      index: 1,
      dbId: -1,
      lv: 1,
      taskName: "所有建筑全部1级奖励",
      reward: db[101].reward02List,
      state: TaskState.未完成,
      bgRes: "bg_9g_tanchaungnei_zi",
    };
    lvBool = this.isCityLv(1);
    if (CityModule.data.cityAggregateMessage.smallHomeAllLevelReward.includes(1) == true) {
      task2.state = TaskState.已领取;
    } else if (lvBool) {
      task2.state = TaskState.已完成;
    }

    list.push(task1, task2);

    for (let i in db) {
      let task: Task = {
        index: list.length,
        dbId: db[i].id,
        lv: -1,
        taskName: db[i].name + Math.floor(db[i].id / 100) + "级奖励",
        reward: db[i].reward01List,
        state: TaskState.未完成,
        bgRes: "TY_bg_9g_tanchuangdi",
      };
      if (CityModule.data.cityAggregateMessage.smallHomeRewardList.includes(db[i].id) == true) {
        task.state = TaskState.已领取;
      } else {
        let id = Math.floor(db[i].id % 100) + 100;
        let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[id] || 0;

        if (lv < Math.floor(db[i].id / 100)) {
          task.state = TaskState.未完成;
        } else {
          task.state = TaskState.已完成;
        }
      }
      list.push(task);
    }

    list = list.sort((a, b) => {
      return a.state - b.state;
    });

    return list;
  }

  private isCityLv(lv: number) {
    let db = JsonMgr.instance.jsonList.c_home;
    let idList = [];
    for (let i in db) {
      if (db[i].id < 200) idList.push(db[i].id);
    }

    for (let i of idList) {
      if (!CityModule.data.cityAggregateMessage.smallHomeLevelMap[i]) return false;
      if (CityModule.data.cityAggregateMessage.smallHomeLevelMap[i] < lv) return false;
    }

    return true;
  }

  private on_click_btn_go() {
    UIMgr.instance.back();
  }

  public tick(dt: any): void {
    let pengz = this.getNode("pengz");

    let content_list = this.getNode("content_main");

    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();

    const index = this._tickIndex;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();

      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
          if (val.getComponent(Mask)) {
            val.getComponent(Mask).enabled = true;
          }
          if (val.getComponent(Graphics)) {
            val.getComponent(Graphics).enabled = true;
          }
        });
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
          if (val.getComponent(Mask)) {
            val.getComponent(Mask).enabled = false;
          }
          if (val.getComponent(Graphics)) {
            val.getComponent(Graphics).enabled = false;
          }
        });
      }
      this._tickIndex = i;
    }

    if (this._tickIndex >= content_list.children.length - 1) {
      this._tickIndex = 0;
    }
  }
}
