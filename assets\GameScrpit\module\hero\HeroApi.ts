import MsgEnum from "../../game/event/MsgEnum";
import { GameDirector } from "../../game/GameDirector";
import { A<PERSON><PERSON><PERSON><PERSON>, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { HeroSubCmd } from "../../game/net/cmd/CmdData";
import { HeroPictureMessage } from "../../game/net/protocol/City";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { HeroMessage, HeroSkillLvRequest } from "../../game/net/protocol/Hero";
import MsgMgr from "../../lib/event/MsgMgr";
import { HeroModule } from "./HeroModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HeroApi {
  public getAll(success?: ApiHandlerSuccess) {
    ApiHandler.instance.list(HeroMessage, HeroSubCmd.getAllHero, null, (data: HeroMessage[]) => {
      HeroModule.data.setHeroMessageList(data);
      success && success(data);
    });
  }
  public getHeroById(heroId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: heroId,
    };
    ApiHandler.instance.request(HeroMessage, HeroSubCmd.getOneHero, LongValue.encode(data), (data: HeroMessage) => {
      HeroModule.data.setHeroMessage(data);
      success && success(data);

      // 检查模块开启
      GameDirector.instance.checkAndLoadModule(true);
    });
  }

  public skillLevelUp(heroId: number, skillId: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    log.log("send skillLevelUp");
    let msg: HeroSkillLvRequest = {
      heroId,
      skillId,
    };
    ApiHandler.instance.requestSync(
      HeroMessage,
      HeroSubCmd.skillLevelUp,
      HeroSkillLvRequest.encode(msg),
      (data: HeroMessage) => {
        HeroModule.data.setHeroMessage(data);
        success && success(data);
      },
      error
    );
  }
  /**
   * 英雄升级
   * @param heroId
   * @param success
   */
  public levelUp(heroId: number, success?: ApiHandlerSuccess) {
    // log.log("send levelUp");
    let id: LongValue = {
      value: heroId,
    };
    ApiHandler.instance.requestSync(HeroMessage, HeroSubCmd.levelUp, LongValue.encode(id), (data: HeroMessage) => {
      HeroModule.data.setHeroMessage(data);
      success && success(data);
    });
  }
  /**
   * 十连升级
   * @param heroId
   */
  public levelUpTen(heroId: number, success?: ApiHandlerSuccess) {
    log.log("send levelUpTen");
    let id: LongValue = {
      value: heroId,
    };
    // SendMgr.send(HeroSubCmd.levelUp10, LongValue.encode(id));
    ApiHandler.instance.requestSync(HeroMessage, HeroSubCmd.levelUp10, LongValue.encode(id), (data: HeroMessage) => {
      HeroModule.data.setHeroMessage(data);
      success && success(data);
    });
  }
  /**
   * 英雄等级突破
   * @param heroId
   */
  public breakTop(heroId: number, success?: ApiHandlerSuccess) {
    // log.log("send breakTop");
    let id: LongValue = {
      value: heroId,
    };
    // SendMgr.send(HeroSubCmd.breakTop, LongValue.encode(id));
    ApiHandler.instance.requestSync(HeroMessage, HeroSubCmd.breakTop, LongValue.encode(id), (data: HeroMessage) => {
      HeroModule.data.setHeroMessage(data);
      log.log("breakTop", data);
      success && success(data);
    });
  }

  public getPicture(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(HeroPictureMessage, HeroSubCmd.getPicture, undefined, (data: HeroPictureMessage) => {
      HeroModule.data.pictureMessage = data;
      MsgMgr.emit(MsgEnum.ON_HERO_UPDATE);
      success?.(data);
    });
  }
  public lvPicture(pictureId: number, success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      LongValue,
      HeroSubCmd.lvPicture,
      LongValue.encode({ value: pictureId }),
      (data: LongValue) => {
        HeroModule.data.pictureMessage.pictureMap[pictureId] = data.value;
        MsgMgr.emit(MsgEnum.ON_HERO_UPDATE);
        success && success(data);
      }
    );
  }

  /**
   * 添加英雄--测试接口
   */
  public testAddHero1() {
    // log.log("send addHero1");
    // SendMgr.send(HeroSubCmd.addHero1, undefined);
    ApiHandler.instance.request(HeroMessage, HeroSubCmd.addHero1, undefined, (data: HeroMessage) => {
      HeroModule.data.setHeroMessage(data);
    });
  }
  /**
   * 英雄属性数据--测试接口
   */
  public testHeroData() {
    // log.log("send heroTest");
    // SendMgr.send(HeroSubCmd.heroTest, undefined);
    // ApiHandler.instance.request(HeroMessage, HeroSubCmd.heroTest, undefined, (data: HeroMessage) => {
    //   HeroModule.data.setHeroMessage(data);
    // });
  }
}
