import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { FriendTuJianViewHolder } from "./FriendTuJianViewHolder";
import { UIFriendMainTuJian } from "../UIFriendMainTuJian";
import { IConfigFriendPicture } from "../../../JsonDefine";

export class FriendTuJianAdapter extends ListAdapter {
  private _data: IConfigFriendPicture[];
  private viewHolder: Node;
  private _context: UIFriendMainTuJian;

  constructor(viewHolder: Node, context: UIFriendMainTuJian) {
    super();
    this.viewHolder = viewHolder;
    this._context = context;
  }
  public setData(data: IConfigFriendPicture[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.viewHolder);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(FriendTuJianViewHolder).updateData(this._data[position], this._context, position);
  }
  getCount(): number {
    return this._data.length;
  }
}
