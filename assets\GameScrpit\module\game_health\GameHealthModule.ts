import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { GameHealthApi } from "./GameHealthApi";
import { GameHealthConfig } from "./GameHealthConfig";
import { GameHealthData } from "./GameHealthData";
import { GameHealthRoute } from "./GameHealthRoute";
import { GameHealthService } from "./GameHealthService";
import { GameHealthSubscriber } from "./GameHealthSubscriber";
import { GameHealthViewModel } from "./GameHealthViewModel";

export class GameHealthModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): GameHealthModule {
    if (!GameData.instance.GameHealthModule) {
      GameData.instance.GameHealthModule = new GameHealthModule();
    }
    return GameData.instance.GameHealthModule;
  }
  private _data = new GameHealthData();
  private _api = new GameHealthApi();
  private _service = new GameHealthService();
  private _subscriber = new GameHealthSubscriber();
  private _route = new GameHealthRoute();
  private _viewModel = new GameHealthViewModel();
  private _config = new GameHealthConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new GameHealthData();
    this._api = new GameHealthApi();
    this._service = new GameHealthService();
    this._subscriber = new GameHealthSubscriber();
    this._route = new GameHealthRoute();
    this._viewModel = new GameHealthViewModel();
    this._config = new GameHealthConfig();

    // 模块初始化
    this._subscriber.register();
    this._route.init();
    completedCallback && completedCallback();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
