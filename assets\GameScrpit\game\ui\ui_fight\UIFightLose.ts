import { _decorator, Label } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { CityModule } from "../../../module/city/CityModule";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { TipsMgr } from "../../../../platform/src/TipsHelper";
import { GuideRouteEnum } from "../../../ext_guide/GuideDefine";
import { FightModule, FightRouteItem } from "../../../module/fight/src/FightModule";
import ToolExt from "../../common/ToolExt";
import { Sleep } from "../../GameDefine";
import GuideMgr from "../../../ext_guide/GuideMgr";
import { LangMgr } from "../../mgr/LangMgr";

const { ccclass, property } = _decorator;

@ccclass("UIFightLose")
export class UIFightLose extends UINode {
  protected _isAddToTop: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FIGHT}?prefab/ui/UIFightLose`;
  }
  protected dependOn(): Array<BundleEnum> {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  public init(args: any): void {
    super.init(args);
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);
    AudioMgr.instance.playEffect(AudioName.Effect.战斗失败);
    if (FightModule.data.chapterId == 1030002) {
      TipsMgr.topRouteCtrl.show(
        GuideRouteEnum.TopHeroHelp,
        null,
        () => {},
        async () => {
          await Sleep(2);
          UIMgr.instance.closeByName(FightRouteItem.UIFightLose);
          UIMgr.instance.closeByName(FightRouteItem.UIFightPage);
        }
      );
    }
  }

  protected onEvtClose(): void {
    MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "FIGHT_LOSE");
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
    ToolExt.replaceLevelMain();
  }
}
