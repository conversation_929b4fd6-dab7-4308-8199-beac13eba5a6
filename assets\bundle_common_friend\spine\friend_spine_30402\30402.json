{"skeleton": {"hash": "rMT4b2dI873p7qg0as0heDVCbnU", "spine": "3.8.75", "x": -312.42, "y": -526.44, "width": 618.28, "height": 1199.73, "images": "./images/", "audio": "D:/仙友spine/蝶恋花"}, "bones": [{"name": "root", "scaleX": 0.82, "scaleY": 0.82}, {"name": "bone", "parent": "root", "length": 299.2, "rotation": -90, "x": 559.97, "y": 207.01}, {"name": "st1", "parent": "bone", "length": 260.98, "rotation": 161.33, "x": 445.96, "y": -630.95}, {"name": "st2", "parent": "st1", "x": 375.16, "y": -69.86}, {"name": "st3", "parent": "st1", "x": 398.51, "y": 69.11}, {"name": "st4", "parent": "st1", "length": 100.79, "rotation": 157.3, "x": -22.91, "y": 14.9}, {"name": "st5", "parent": "st4", "length": 136.96, "rotation": 12.27, "x": 125.78, "y": -9.59}, {"name": "st6", "parent": "st5", "length": 134.01, "rotation": -10.09, "x": 163.51, "y": -5.9}, {"name": "t1", "parent": "st3", "x": 127.2, "y": -5.95}, {"name": "t2", "parent": "t1", "x": 79.28, "y": 11.05}, {"name": "f1", "parent": "t1", "length": 76.74, "rotation": -169.19, "x": -39.74, "y": 100.06}, {"name": "f2", "parent": "f1", "length": 44.52, "rotation": -24.03, "x": 84.62, "y": -16.72}, {"name": "f3", "parent": "f2", "length": 43.78, "rotation": 23.06, "x": 55.92, "y": -0.82}, {"name": "f4", "parent": "f3", "length": 33.2, "rotation": 6.65, "x": 53.68, "y": -3.56}, {"name": "f5", "parent": "f4", "length": 32.15, "rotation": 31.08, "x": 38.56, "y": 2.73}, {"name": "f6", "parent": "t1", "x": 2.67, "y": 188.73}, {"name": "f7", "parent": "f6", "length": 14.42, "rotation": -174.46, "x": -19.7, "y": 4.21}, {"name": "f8", "parent": "f7", "length": 20.01, "rotation": 2.35, "x": 20.1, "y": 0.36}, {"name": "f9", "parent": "f8", "length": 16.62, "rotation": 43.13, "x": 24.83, "y": 4.73}, {"name": "f10", "parent": "f8", "length": 21.61, "rotation": 5.81, "x": 36.91, "y": 1.31}, {"name": "f11", "parent": "f10", "length": 34.64, "rotation": 3.42, "x": 31.6, "y": -1.48}, {"name": "f12", "parent": "f11", "length": 30.43, "rotation": -0.21, "x": 43.01, "y": 2.1}, {"name": "s2", "parent": "st3", "length": 246.87, "rotation": -155.04, "x": 33.72, "y": -40.53}, {"name": "s3", "parent": "s2", "length": 235.42, "rotation": 142.38, "x": 246.58, "y": 19.83}, {"name": "s5", "parent": "s3", "length": 60.26, "rotation": -72.38, "x": 239.66, "y": -0.43}, {"name": "s4", "parent": "s5", "length": 23.8, "rotation": -0.5, "x": 71.22, "y": 14.52}, {"name": "s6", "parent": "s4", "length": 15.94, "rotation": 22.63, "x": 28.12, "y": -0.41}, {"name": "s8", "parent": "s5", "length": 25.91, "rotation": -36.32, "x": 73.83, "y": 1.28}, {"name": "s9", "parent": "s8", "length": 26.03, "rotation": 3.21, "x": 33.01, "y": -0.62}, {"name": "s10", "parent": "s5", "length": 20.76, "rotation": -50.45, "x": 68.81, "y": -20.08}, {"name": "s11", "parent": "s10", "length": 23.23, "rotation": -11.28, "x": 28.5, "y": -2.12}, {"name": "s12", "parent": "s5", "length": 17.28, "rotation": -45.83, "x": 51.66, "y": -25.38}, {"name": "s13", "parent": "s12", "length": 26.5, "rotation": -11.83, "x": 22.34, "y": -0.61}, {"name": "s7", "parent": "s3", "length": 88.5, "rotation": -177.22, "x": -5.31, "y": -43.82}, {"name": "s14", "parent": "s7", "length": 63.29, "rotation": -24.34, "x": 135.8, "y": 44.36}, {"name": "bone2", "parent": "root", "length": 160.59, "x": -196.43, "y": -685.51}, {"name": "bone3", "parent": "bone2", "length": 96.36, "rotation": -143.53, "x": 171.82, "y": 270.64}, {"name": "bone4", "parent": "bone3", "length": 90.91, "rotation": -9.27, "x": 132.32, "y": 60}, {"name": "s1", "parent": "st3", "length": 238.66, "rotation": -177.81, "x": -12.87, "y": 74.89}, {"name": "s15", "parent": "s1", "length": 60.71, "rotation": 54.77, "x": 242.72, "y": 8.39}, {"name": "bone5", "parent": "root", "length": 87.9, "rotation": -3.49, "x": -359.12, "y": -695.71}, {"name": "h1", "parent": "bone5", "length": 78.59, "rotation": 90.4, "x": 56.3, "y": 64.52}, {"name": "h2", "parent": "h1", "length": 69.45, "rotation": -19.03, "x": 85.52, "y": -10.25}, {"name": "h3", "parent": "h2", "length": 73.97, "rotation": -9.59, "x": 74.96, "y": 1.48}, {"name": "h4", "parent": "h3", "length": 69.34, "rotation": -7.49, "x": 75.91, "y": -0.46}, {"name": "h5", "parent": "h1", "length": 104.64, "rotation": 3.48, "x": 87.85, "y": -0.92}, {"name": "h6", "parent": "h5", "length": 111.01, "rotation": -1.12, "x": 119.47, "y": -2.22}, {"name": "h7", "parent": "h6", "length": 97.57, "rotation": 0.31, "x": 116.05, "y": 0.77}, {"name": "h8", "parent": "h7", "length": 48.91, "rotation": -3.73, "x": 103.21, "y": 1.46}, {"name": "bone6", "parent": "root", "length": 130.11, "rotation": -89.07, "x": -444.64, "y": 6.87}, {"name": "sj1", "parent": "bone6", "x": 84.28, "y": 109.14}, {"name": "h9", "parent": "sj1", "length": 41.75, "rotation": 164.6, "x": -64.98, "y": 2.18}, {"name": "h10", "parent": "h9", "length": 32.81, "rotation": -34.56, "x": 44.93, "y": -1.87}, {"name": "h11", "parent": "h10", "length": 43.76, "rotation": -16.32, "x": 40.24, "y": -0.4}, {"name": "bone7", "parent": "root", "length": 120.36, "rotation": -91.25, "x": 455.26, "y": 320.64}, {"name": "d1", "parent": "bone7", "length": 29.2, "rotation": 82.5, "x": 100.44, "y": -222.5}, {"name": "d2", "parent": "d1", "length": 24.24, "rotation": 21.09, "x": 38.2, "y": -0.11}, {"name": "d3", "parent": "d2", "length": 34.43, "rotation": 15.88, "x": 27.13, "y": -0.63}, {"name": "d4", "parent": "d3", "length": 26.65, "rotation": -29.81, "x": 41.91, "y": -4.85}, {"name": "d6", "parent": "d1", "length": 25.23, "rotation": 126.76, "x": 10.95, "y": 13.36}, {"name": "d7", "parent": "d6", "length": 35.48, "rotation": -5.21, "x": 35.88, "y": 1.85}, {"name": "d8", "parent": "d7", "length": 45.45, "rotation": -6.44, "x": 46.05, "y": 0.7}, {"name": "d9", "parent": "d1", "length": 28.07, "rotation": 103.59, "x": 23.67, "y": 21.07}, {"name": "d10", "parent": "d9", "length": 44.51, "rotation": 3.11, "x": 37.39, "y": -2.22}, {"name": "d11", "parent": "d10", "length": 40.76, "rotation": -7.96, "x": 51.21, "y": 0.02}, {"name": "s16", "parent": "s3", "length": 92.02, "rotation": -138.58, "x": -114.72, "y": -232.63}, {"name": "s17", "parent": "s16", "length": 99.35, "rotation": -7.95, "x": 108.69, "y": -5.49}, {"name": "s18", "parent": "s17", "length": 83.3, "rotation": -5.56, "x": 111.57, "y": -5.43}, {"name": "bone8", "parent": "root", "length": 154.14, "rotation": -1.59, "x": 98.77, "y": -683.56}, {"name": "d5", "parent": "bone8", "length": 126.36, "rotation": -170.62, "x": 56.21, "y": 95.76}, {"name": "d13", "parent": "d5", "length": 49.32, "rotation": -85.26, "x": 37.49, "y": -18.09}, {"name": "d14", "parent": "d13", "length": 26.75, "rotation": 40.6, "x": 61.16, "y": -5.92}, {"name": "d15", "parent": "d14", "length": 16.09, "rotation": 40.68, "x": 34.67, "y": 12.63}, {"name": "bone9", "parent": "root", "length": 159.13, "rotation": 0.96, "x": 54.89, "y": 868.75}, {"name": "h12", "parent": "bone9", "length": 61.73, "rotation": -95.93, "x": 71.18, "y": -61.37}, {"name": "h13", "parent": "h12", "length": 63.07, "rotation": 9.83, "x": 73.72, "y": 1.04}, {"name": "h14", "parent": "h13", "length": 26.87, "rotation": 0.85, "x": 72.39, "y": -0.79}, {"name": "h15", "parent": "h14", "length": 16.1, "rotation": -10.47, "x": 40.18, "y": -1.33}, {"name": "st7", "parent": "root", "length": 239.76, "x": -367.31, "y": 876.61}, {"name": "h16", "parent": "st7", "length": 120.67, "rotation": -86.09, "x": 34.99, "y": -79.23}, {"name": "h17", "parent": "h16", "length": 111.83, "rotation": -13.98, "x": 126.76, "y": -1.45}, {"name": "h18", "parent": "h17", "length": 107.02, "rotation": 9.52, "x": 133.22, "y": 9.02}, {"name": "h19", "parent": "h18", "length": 73.46, "rotation": -10.76, "x": 119.36, "y": 1.15}, {"name": "h20", "parent": "st7", "length": 58.66, "rotation": -68.39, "x": 135.83, "y": -79.23}, {"name": "h21", "parent": "h20", "length": 59.66, "rotation": -36.61, "x": 74.92, "y": -6.44}, {"name": "h22", "parent": "h21", "length": 37.17, "rotation": 10.23, "x": 74.64, "y": -0.25}, {"name": "h23", "parent": "st7", "length": 50.93, "rotation": -81.87, "x": 241.81, "y": -65.86}, {"name": "h24", "parent": "h23", "length": 27.34, "rotation": -27.93, "x": 54.43, "y": 2.62}, {"name": "h25", "parent": "st7", "length": 33.52, "rotation": -72.12, "x": 398.22, "y": -67.91}, {"name": "h26", "parent": "h25", "length": 38.57, "rotation": -8.67, "x": 35.76, "y": 3.6}, {"name": "f13", "parent": "t1", "length": 32.15, "rotation": -100.76, "x": 158.02, "y": 42}, {"name": "f14", "parent": "f13", "length": 26.91, "rotation": -51.36, "x": 36.69, "y": -0.74}, {"name": "f15", "parent": "f14", "length": 22.99, "rotation": -11, "x": 30.23, "y": -1.99}, {"name": "f16", "parent": "f15", "length": 28.04, "rotation": 41.6, "x": 31.58, "y": 0.99}, {"name": "f17", "parent": "f16", "length": 25.22, "rotation": -34.91, "x": 37.42, "y": -2.21}, {"name": "f18", "parent": "f17", "length": 17.07, "rotation": -27.15, "x": 27.18, "y": -2.33}, {"name": "f19", "parent": "t1", "length": 18.31, "rotation": -172.64, "x": -19.78, "y": -11.55}, {"name": "f20", "parent": "f19", "length": 19.95, "rotation": 71.05, "x": 28.58, "y": 3.52}, {"name": "f21", "parent": "f20", "length": 22.85, "rotation": -13.47, "x": 24.6, "y": -2.27}, {"name": "f22", "parent": "f21", "length": 21.84, "rotation": -36.81, "x": 27.41, "y": -0.61}, {"name": "t3", "parent": "t1", "length": 124.46, "rotation": 156.12, "x": 154.33, "y": 133.49}, {"name": "t4", "parent": "t3", "length": 89.63, "rotation": -1.77, "x": 49.95, "y": -84.04}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "h4", "bone": "st7", "attachment": "h4"}, {"name": "h3", "bone": "h12", "attachment": "h3"}, {"name": "sj1", "bone": "sj1", "attachment": "sj1"}, {"name": "f2", "bone": "f19", "attachment": "f2"}, {"name": "s1", "bone": "s1", "attachment": "s1"}, {"name": "st1", "bone": "st1", "attachment": "st1"}, {"name": "f1", "bone": "f1", "attachment": "f1"}, {"name": "s2", "bone": "s2", "attachment": "s2"}, {"name": "s3", "bone": "s5"}, {"name": "s4", "bone": "s4"}, {"name": "h2", "bone": "h9", "attachment": "h2"}, {"name": "h1", "bone": "h1", "attachment": "h1"}, {"name": "t1", "bone": "t1", "attachment": "t1"}, {"name": "f3", "bone": "f13", "attachment": "f3"}, {"name": "f4", "bone": "f6", "attachment": "f4"}, {"name": "d2", "bone": "d9", "attachment": "d2"}, {"name": "d1", "bone": "d1", "attachment": "d1"}, {"name": "d3", "bone": "d6", "attachment": "d3"}, {"name": "d4", "bone": "d5", "attachment": "d4"}, {"name": "d5", "bone": "d13", "attachment": "d5"}, {"name": "sss", "bone": "root", "attachment": "sss"}], "skins": [{"name": "default", "attachments": {"bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [373, -630.13, -381, -630.13, -381, 821.09, 373, 821.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 368, "height": 623}}, "d2": {"d2": {"type": "mesh", "uvs": [0.34543, 0, 0.40305, 0.0551, 0.46508, 0.1144, 0.52721, 0.1738, 0.63688, 0.27865, 0.71868, 0.35686, 0.85115, 0.48351, 1, 0.62582, 1, 0.73137, 0.89933, 0.80824, 0.79686, 0.88648, 0.65161, 0.99738, 0.20157, 0.98124, 0.09106, 0.87167, 0, 0.78139, 0, 0.63876, 0.01326, 0.55781, 0.02681, 0.47506, 0.04359, 0.37259, 0.05739, 0.28834, 0.07135, 0.20308, 0.08798, 0.10152, 0.17165, 0.05581, 0.27377, 0, 0.40404, 0.84214, 0.40728, 0.76924, 0.42996, 0.64887, 0.42348, 0.54715, 0.37163, 0.45221, 0.31654, 0.36066, 0.2809, 0.27758, 0.258, 0.18602, 0.27866, 0.10631, 0.28899, 0.05632], "triangles": [29, 19, 30, 29, 18, 19, 29, 4, 5, 29, 30, 4, 19, 20, 30, 30, 3, 4, 20, 31, 30, 30, 31, 3, 20, 21, 31, 31, 2, 3, 31, 32, 2, 31, 21, 32, 2, 32, 1, 32, 33, 1, 21, 22, 32, 32, 22, 33, 22, 23, 33, 33, 0, 1, 33, 23, 0, 8, 9, 26, 9, 25, 26, 25, 15, 26, 26, 7, 8, 15, 16, 26, 16, 27, 26, 26, 27, 7, 27, 6, 7, 16, 28, 27, 16, 17, 28, 27, 28, 6, 28, 5, 6, 17, 29, 28, 17, 18, 29, 28, 29, 5, 12, 24, 11, 11, 24, 10, 12, 13, 24, 9, 10, 25, 24, 13, 25, 10, 24, 25, 25, 13, 14, 14, 15, 25], "vertices": [1, 64, 45.81, -1.81, 1, 1, 64, 37.49, -6.37, 1, 2, 63, 77.92, -15.09, 0.00113, 64, 28.54, -11.27, 0.99887, 2, 63, 68.35, -18.7, 0.03115, 64, 19.57, -16.17, 0.96885, 2, 63, 51.47, -25.09, 0.32383, 64, 3.73, -24.84, 0.67617, 2, 63, 38.88, -29.86, 0.67122, 64, -8.08, -31.3, 0.32878, 3, 62, 57.9, -38.73, 0.02468, 63, 18.49, -37.58, 0.93274, 64, -27.2, -41.77, 0.04258, 3, 62, 35.5, -48.64, 0.17967, 63, -4.42, -46.25, 0.82028, 64, -48.69, -53.53, 4e-05, 2, 62, 19.61, -47.29, 0.3234, 63, -20.2, -44.04, 0.6766, 2, 62, 8.72, -38.38, 0.51591, 63, -30.59, -34.56, 0.48409, 2, 62, -2.37, -29.32, 0.777, 63, -41.17, -24.9, 0.223, 2, 62, -18.09, -16.47, 0.97148, 63, -56.17, -11.22, 0.02852, 1, 62, -12.65, 18.75, 1, 2, 62, 4.57, 26.05, 0.98023, 63, -31.24, 30.01, 0.01977, 2, 62, 18.76, 32.07, 0.78673, 63, -16.74, 35.24, 0.21327, 2, 62, 40.22, 30.25, 0.24907, 63, 4.59, 32.26, 0.75093, 3, 62, 52.31, 28.17, 0.0648, 63, 16.55, 29.53, 0.93173, 64, -38.42, 24.43, 0.00348, 3, 62, 64.67, 26.05, 0.00587, 63, 28.77, 26.74, 0.90147, 64, -25.92, 23.36, 0.09266, 2, 63, 43.91, 23.29, 0.39051, 64, -10.45, 22.03, 0.60949, 2, 63, 56.36, 20.45, 0.0317, 64, 2.27, 20.94, 0.9683, 1, 64, 15.14, 19.84, 1, 1, 64, 30.48, 18.52, 1, 1, 64, 37.38, 11.91, 1, 1, 64, 45.81, 3.85, 1, 2, 62, 6.92, 1.04, 0.99315, 63, -30.25, 4.9, 0.00685, 2, 62, 17.87, -0.15, 0.9385, 63, -19.38, 3.13, 0.0615, 2, 62, 35.83, -3.47, 0.0069, 63, -1.63, -1.17, 0.9931, 1, 63, 13.66, -2.78, 1, 2, 63, 28.42, -0.71, 0.98686, 64, -22.47, -3.88, 0.01314, 2, 63, 42.71, 1.68, 0.24028, 64, -8.65, 0.47, 0.75972, 2, 63, 55.53, 2.74, 0.0001, 64, 3.89, 3.28, 0.9999, 1, 64, 17.72, 5.09, 1, 1, 64, 29.76, 3.46, 1, 1, 64, 37.31, 2.64, 1], "hull": 24, "edges": [0, 46, 14, 16, 22, 24, 28, 30, 24, 26, 26, 28, 26, 48, 20, 22, 48, 20, 28, 50, 16, 18, 18, 20, 50, 18, 30, 52, 52, 16, 30, 32, 32, 54, 54, 14, 32, 34, 34, 56, 12, 14, 56, 12, 34, 36, 36, 58, 10, 12, 58, 10, 36, 38, 38, 60, 8, 10, 60, 8, 38, 40, 40, 42, 40, 62, 6, 8, 62, 6, 42, 64, 4, 6, 64, 4, 42, 44, 44, 46, 44, 66, 0, 2, 2, 4, 66, 2], "width": 79, "height": 151}}, "h1": {"h1": {"type": "mesh", "uvs": [0.19207, 0.03198, 0.27674, 0, 0.28621, 0, 0.43443, 0.01075, 0.43392, 0.0248, 0.45577, 0.0579, 0.49313, 0.11448, 0.52283, 0.15946, 0.50751, 0.21175, 0.49494, 0.25464, 0.48543, 0.28708, 0.41114, 0.33455, 0.35085, 0.37307, 0.29328, 0.40985, 0.32266, 0.46044, 0.3634, 0.53059, 0.40059, 0.59463, 0.42395, 0.63485, 0.44183, 0.60139, 0.47879, 0.53225, 0.50412, 0.48485, 0.52997, 0.4365, 0.55717, 0.38562, 0.57834, 0.346, 0.61587, 0.32903, 0.87876, 0.33472, 0.92159, 0.36228, 0.97552, 0.41604, 1, 0.4464, 1, 0.45101, 0.95329, 0.48644, 0.91503, 0.51545, 0.90246, 0.52855, 0.7878, 0.57396, 0.64777, 0.62942, 0.56066, 0.66392, 0.48958, 0.69207, 0.53379, 0.6901, 0.60963, 0.68673, 0.6976, 0.68282, 0.80671, 0.67797, 0.9236, 0.67277, 0.93831, 0.68585, 0.83605, 0.71495, 0.72357, 0.74696, 0.6583, 0.76553, 0.58836, 0.78544, 0.53719, 0.8, 0.66065, 0.97208, 0.63559, 0.97237, 0.33832, 0.80979, 0.306, 0.85634, 0.25729, 0.9265, 0.25394, 0.96158, 0.2504, 0.9986, 0.19293, 0.99865, 0.19562, 0.95508, 0.19946, 0.89309, 0.20156, 0.85908, 0.2035, 0.8278, 0.01226, 0.82497, 0.02613, 0.81894, 0.18721, 0.7563, 0.13717, 0.68607, 0.09678, 0.62939, 0.02583, 0.52984, 0.0152, 0.48914, 0.04247, 0.45393, 0.06997, 0.41841, 0.1196, 0.35431, 0.15675, 0.30633, 0.20743, 0.24087, 0.11533, 0.2182, 0, 0.18981, 0, 0.17454, 0.01469, 0.1084, 0.02783, 0.04925, 0.10291, 0.01788, 0.13302, 0.80154, 0.27021, 0.77216, 0.37016, 0.76764, 0.476, 0.73374, 0.61711, 0.71905, 0.78174, 0.69759, 0.6947, 0.70893, 0.52998, 0.72812, 0.12322, 0.55635, 0.24473, 0.63544, 0.34468, 0.70323, 0.45444, 0.66595, 0.476, 0.61171, 0.52891, 0.63883, 0.55439, 0.57556, 0.66022, 0.52923, 0.70922, 0.46822, 0.78174, 0.42189, 0.83465, 0.37331, 0.213, 0.67625, 0.2041, 0.60899, 0.17184, 0.64687, 0.16761, 0.58524, 0.12872, 0.60393, 0.26004, 0.49207, 0.27375, 0.57117, 0.32863, 0.64461, 0.15028, 0.04993, 0.15028, 0.10756, 0.36979, 0.1166, 0.18164, 0.16857, 0.39919, 0.16066, 0.26983, 0.21264, 0.42075, 0.19117, 0.34627, 0.24767, 0.33843, 0.29512, 0.29335, 0.33241, 0.25808, 0.38325, 0.23652, 0.44879], "triangles": [4, 2, 3, 105, 77, 0, 76, 77, 105, 2, 4, 5, 0, 1, 2, 5, 0, 2, 105, 0, 5, 106, 76, 105, 107, 106, 105, 75, 76, 106, 5, 107, 105, 107, 5, 6, 109, 107, 6, 109, 6, 7, 108, 106, 107, 108, 107, 109, 74, 75, 106, 74, 106, 108, 72, 73, 74, 111, 109, 7, 110, 108, 109, 110, 109, 111, 108, 72, 74, 72, 108, 110, 71, 72, 110, 8, 111, 7, 112, 110, 111, 71, 110, 112, 9, 111, 8, 112, 111, 9, 10, 112, 9, 113, 71, 112, 113, 112, 10, 70, 71, 113, 114, 70, 113, 11, 113, 10, 114, 113, 11, 69, 70, 114, 12, 114, 11, 115, 69, 114, 115, 114, 12, 13, 115, 12, 68, 69, 115, 116, 115, 13, 116, 68, 115, 67, 68, 116, 102, 116, 13, 14, 102, 13, 86, 65, 66, 102, 14, 15, 86, 67, 116, 86, 116, 102, 86, 66, 67, 103, 102, 15, 86, 102, 103, 100, 86, 103, 103, 15, 16, 101, 86, 100, 98, 100, 103, 101, 64, 65, 101, 65, 86, 87, 98, 103, 104, 87, 103, 16, 104, 103, 98, 101, 100, 99, 101, 98, 99, 98, 87, 64, 101, 99, 97, 99, 87, 63, 64, 99, 63, 99, 97, 62, 63, 97, 88, 97, 87, 79, 97, 88, 62, 97, 79, 78, 62, 79, 62, 78, 61, 59, 78, 79, 59, 79, 50, 61, 78, 59, 60, 61, 59, 96, 24, 25, 96, 25, 26, 96, 26, 27, 95, 24, 96, 22, 23, 24, 95, 22, 24, 30, 27, 28, 94, 22, 95, 21, 22, 94, 27, 95, 96, 29, 30, 28, 30, 95, 27, 31, 95, 30, 94, 95, 31, 32, 94, 31, 94, 20, 21, 33, 93, 94, 93, 20, 94, 19, 20, 93, 32, 33, 94, 92, 19, 93, 92, 18, 19, 34, 92, 93, 34, 93, 33, 91, 90, 92, 91, 92, 34, 35, 91, 34, 90, 18, 92, 17, 18, 90, 17, 104, 16, 89, 17, 90, 89, 90, 91, 35, 36, 91, 36, 89, 91, 83, 39, 40, 42, 83, 40, 42, 40, 41, 17, 89, 88, 17, 88, 104, 87, 104, 88, 84, 38, 39, 84, 39, 83, 43, 83, 42, 82, 38, 84, 85, 36, 37, 82, 85, 37, 82, 37, 38, 36, 88, 89, 81, 36, 85, 81, 88, 36, 44, 84, 83, 44, 83, 43, 45, 82, 84, 45, 84, 44, 80, 88, 81, 46, 85, 82, 46, 82, 45, 47, 85, 46, 81, 85, 47, 79, 88, 80, 80, 81, 47, 50, 79, 80, 51, 59, 50, 50, 80, 47, 49, 47, 48, 50, 47, 49, 58, 59, 51, 51, 57, 58, 52, 57, 51, 56, 57, 52, 53, 56, 52, 53, 55, 56, 54, 55, 53], "vertices": [1, 48, 108.69, 26.77, 1, 1, 48, 128.6, 0.71, 1, 2, 47, 231.61, -9.27, 0.00015, 48, 128.82, -2.35, 0.99985, 2, 47, 225.91, -57.34, 0.01996, 48, 126.26, -50.69, 0.98004, 2, 47, 218.02, -57.23, 0.02401, 48, 118.38, -51.1, 0.97599, 2, 47, 199.47, -64.45, 0.06716, 48, 100.34, -59.5, 0.93284, 2, 47, 167.76, -76.78, 0.24011, 48, 69.5, -73.87, 0.75989, 2, 47, 142.55, -86.59, 0.43139, 48, 44.98, -85.3, 0.56861, 2, 47, 113.13, -81.83, 0.68104, 48, 15.31, -82.47, 0.31896, 2, 47, 88.99, -77.94, 0.89386, 48, -9.03, -80.15, 0.10614, 2, 47, 70.74, -74.99, 0.97258, 48, -27.43, -78.39, 0.02742, 1, 47, 43.89, -51.11, 1, 1, 47, 22.1, -31.74, 1, 2, 46, 117.41, -12.46, 0.23625, 47, 1.29, -13.23, 0.76375, 2, 43, 114.18, 83.7, 0.00014, 46, 89.1, -22.34, 0.99986, 2, 43, 87.58, 51.74, 0.06, 46, 49.85, -36.04, 0.94, 3, 43, 63.3, 22.57, 0.4232, 45, 132.54, -51.03, 0.00491, 46, 14.02, -48.54, 0.57189, 3, 43, 48.05, 4.25, 0.93628, 45, 109.89, -58.44, 0.00527, 46, -8.49, -56.4, 0.05845, 1, 43, 67.09, 9.21, 1, 2, 43, 106.44, 19.45, 0.13709, 44, 27.67, 23.72, 0.86291, 2, 43, 133.42, 26.47, 0.02604, 44, 53.5, 34.2, 0.97396, 2, 43, 160.94, 33.63, 0.00371, 44, 79.85, 44.89, 0.99629, 2, 43, 189.9, 41.17, 0.00017, 44, 107.58, 56.14, 0.99983, 1, 44, 129.17, 64.89, 1, 1, 44, 144.24, 61.5, 1, 1, 44, 195.61, -6.52, 1, 1, 44, 192.38, -27.07, 1, 1, 44, 180.01, -59.7, 1, 1, 44, 171.81, -76.63, 1, 1, 44, 169.8, -78.27, 1, 1, 44, 144.81, -79.13, 1, 1, 44, 124.34, -79.83, 1, 1, 44, 116.06, -81.33, 1, 1, 44, 72.8, -68.68, 1, 2, 43, 88.77, -55.83, 0.00285, 44, 19.97, -53.22, 0.99715, 3, 42, 124.6, -49.52, 0.00173, 43, 57.44, -42.02, 0.40751, 44, -12.9, -43.61, 0.59075, 3, 42, 101.27, -34.15, 0.11061, 43, 31.88, -30.74, 0.8307, 44, -39.71, -35.76, 0.05869, 3, 42, 107.69, -47, 0.19407, 43, 40.35, -42.35, 0.79734, 44, -29.8, -46.17, 0.00859, 2, 42, 118.7, -69.05, 0.18638, 43, 54.88, -62.25, 0.81362, 2, 42, 131.47, -94.62, 0.11972, 43, 71.73, -85.34, 0.88028, 2, 42, 147.31, -126.34, 0.04064, 43, 92.63, -113.98, 0.95936, 2, 42, 164.28, -160.33, 0.00061, 43, 115.03, -144.66, 0.99939, 1, 43, 111.28, -152.58, 1, 2, 42, 131.64, -142.98, 0.03436, 43, 79.95, -132.99, 0.96564, 2, 42, 101.24, -115.99, 0.13418, 43, 45.49, -111.45, 0.86582, 2, 42, 83.61, -100.34, 0.23494, 43, 25.49, -98.95, 0.76506, 2, 42, 64.71, -83.56, 0.43246, 43, 4.06, -85.55, 0.56754, 2, 42, 50.89, -71.28, 0.70191, 43, -11.61, -75.75, 0.29809, 1, 42, -23.63, -144.76, 1, 1, 42, -26.84, -137.3, 1, 1, 42, 21.52, -13.67, 1, 2, 41, 74.72, -21.14, 0.32815, 42, -6.66, -13.82, 0.67185, 1, 41, 34.5, -7.51, 1, 1, 41, 14.75, -7.49, 1, 1, 41, -6.09, -7.47, 1, 1, 41, -7.12, 11.13, 1, 1, 41, 17.38, 11.58, 1, 1, 41, 52.23, 12.22, 1, 2, 41, 71.36, 12.57, 0.87456, 45, -15.64, 14.46, 0.12544, 2, 41, 88.94, 12.89, 0.20686, 45, 1.93, 13.71, 0.79314, 1, 45, 3.94, 75.66, 1, 1, 45, 7.3, 71.15, 1, 1, 45, 42.15, 18.72, 1, 2, 45, 81.73, 34.66, 0.96045, 46, -38.45, 36.14, 0.03955, 2, 45, 113.67, 47.54, 0.51121, 46, -6.77, 49.63, 0.48879, 3, 45, 169.77, 70.14, 0.03947, 46, 48.88, 73.33, 0.95455, 47, -66.76, 72.93, 0.00598, 3, 45, 192.67, 73.43, 0.01026, 46, 71.71, 77.07, 0.94873, 47, -43.92, 76.54, 0.04101, 3, 45, 212.4, 64.46, 0.0012, 46, 91.61, 68.48, 0.86276, 47, -24.06, 67.85, 0.13604, 2, 46, 111.68, 59.83, 0.63137, 47, -4.04, 59.08, 0.36863, 2, 46, 147.91, 44.21, 0.085, 47, 32.1, 43.26, 0.915, 1, 47, 59.15, 31.42, 1, 2, 47, 96.06, 15.27, 0.49276, 48, -8.04, 13.32, 0.50724, 1, 48, 2.51, 44, 1, 1, 48, 15.72, 82.42, 1, 1, 48, 24.28, 83.04, 1, 1, 48, 61.7, 80.98, 1, 1, 48, 95.16, 79.14, 1, 1, 48, 114.51, 56.15, 1, 2, 41, 102.45, 36.49, 0.01611, 45, 16.85, 36.45, 0.98389, 2, 42, 32.8, 14.74, 0.30186, 45, 33.06, -8.11, 0.69814, 2, 42, 47.35, -14.3, 0.98608, 43, -24.59, -20.15, 0.01392, 3, 42, 77.91, -38.89, 0.39257, 43, 9.64, -39.31, 0.6065, 44, -60.64, -47.16, 0.00093, 2, 42, 102.78, -78.13, 0.21122, 43, 40.7, -73.86, 0.78878, 2, 42, 134.05, -123, 0.05962, 43, 79, -112.89, 0.94038, 2, 42, 117.52, -99.28, 0.13142, 43, 58.75, -92.26, 0.86858, 3, 42, 87.43, -53.9, 0.29931, 43, 21.52, -52.53, 0.69968, 44, -47.14, -58.72, 0.00101, 2, 45, 154.66, 38.69, 0.09279, 46, 34.39, 41.59, 0.90721, 2, 45, 109.95, -0.38, 0.69299, 46, -9.56, 1.66, 0.30701, 2, 43, 1.86, 5.9, 0.92685, 45, 71.63, -32.5, 0.07315, 3, 42, 110.58, -18.07, 0.00864, 43, 38.38, -13.34, 0.9426, 44, -35.54, -17.66, 0.04876, 1, 43, 67.98, -3.26, 1, 2, 43, 64.03, -25.85, 0.40963, 44, -8.47, -26.73, 0.59037, 1, 44, 24.3, -10.64, 1, 1, 44, 66.15, -20.76, 1, 1, 44, 102.75, -11.38, 1, 1, 44, 137.78, -13.13, 1, 1, 44, 169.77, -9.16, 1, 1, 45, 87.08, 10.06, 1, 2, 45, 124.9, 12.69, 0.24824, 46, 5.14, 15.01, 0.75176, 2, 45, 103.68, 23.28, 0.79639, 46, -16.28, 25.19, 0.20361, 2, 45, 138.33, 24.42, 0.16174, 46, 18.33, 27, 0.83826, 2, 45, 127.91, 37.09, 0.33846, 46, 7.67, 39.47, 0.66154, 2, 43, 88.39, 91.61, 0.00033, 46, 71.07, -2.27, 0.99967, 2, 43, 52.92, 64.46, 0.0252, 46, 26.68, -7.28, 0.9748, 3, 43, 27.15, 27.64, 0.47985, 45, 104.61, -27.52, 0.26197, 46, -14.36, -25.59, 0.25818, 1, 48, 97.65, 39.54, 1, 1, 48, 65.35, 37.2, 1, 2, 47, 166.28, -36.83, 0.10764, 48, 65.42, -34.1, 0.89236, 1, 48, 31.88, 24.59, 1, 2, 47, 141.58, -46.53, 0.31861, 48, 41.41, -45.39, 0.68139, 2, 47, 112.07, -4.83, 0.08194, 48, 9.25, -5.7, 0.91806, 2, 47, 124.49, -53.64, 0.54, 48, 24.81, -53.6, 0.46, 2, 47, 92.56, -29.74, 0.98305, 48, -8.6, -31.82, 0.01695, 1, 47, 65.88, -27.4, 1, 1, 47, 44.82, -12.94, 1, 1, 47, 16.16, -1.72, 1, 1, 46, 95.3, 5.66, 1], "hull": 78, "edges": [0, 154, 0, 2, 2, 4, 4, 6, 6, 8, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 62, 64, 82, 84, 94, 96, 96, 98, 98, 100, 108, 110, 118, 120, 120, 122, 122, 124, 130, 132, 146, 148, 152, 154, 110, 112, 104, 106, 106, 108, 112, 106, 112, 114, 114, 104, 114, 116, 116, 118, 100, 102, 102, 104, 116, 102, 118, 100, 122, 156, 156, 158, 158, 160, 160, 162, 166, 84, 80, 82, 80, 166, 84, 86, 166, 86, 78, 80, 164, 168, 168, 166, 78, 168, 86, 88, 168, 88, 76, 78, 76, 164, 88, 90, 164, 90, 72, 74, 74, 76, 162, 170, 170, 164, 74, 170, 90, 92, 92, 94, 170, 92, 130, 172, 174, 176, 176, 160, 34, 176, 34, 178, 178, 72, 34, 36, 36, 180, 180, 182, 70, 72, 182, 70, 36, 38, 38, 184, 68, 70, 184, 68, 38, 40, 40, 186, 64, 66, 66, 68, 186, 66, 40, 42, 42, 188, 188, 64, 42, 44, 44, 46, 44, 190, 58, 60, 60, 62, 190, 60, 48, 192, 192, 54, 174, 194, 194, 124, 196, 174, 196, 198, 124, 126, 198, 126, 172, 200, 200, 196, 200, 202, 126, 128, 128, 130, 202, 128, 26, 204, 204, 206, 206, 208, 208, 176, 26, 28, 28, 30, 30, 32, 32, 34, 152, 210, 8, 10, 210, 10, 148, 150, 150, 152, 150, 212, 212, 214, 10, 12, 12, 14, 214, 12, 148, 216, 216, 218, 218, 14, 142, 144, 144, 146, 144, 220, 220, 222, 14, 16, 222, 16, 142, 224, 16, 18, 18, 20, 224, 18, 140, 142, 140, 226, 226, 20, 138, 140, 138, 228, 20, 22, 228, 22, 136, 138, 136, 230, 22, 24, 24, 26, 230, 24, 132, 134, 134, 136, 134, 232, 232, 26], "width": 324, "height": 562}}, "h2": {"h2": {"type": "mesh", "uvs": [0.68423, 0.06072, 0.87753, 0.1194, 1, 0.48202, 1, 0.50021, 0.87489, 0.63794, 0.72246, 0.80574, 0.54428, 0.61572, 0.472, 0.53864, 0.44286, 0.64299, 0.39891, 0.80034, 0.30343, 0.90193, 0.25645, 0.95191, 0.21125, 1, 0.16687, 1, 0, 0.94687, 0, 0.9404, 0.03704, 0.85119, 0.07377, 0.76274, 0.14596, 0.58885, 0.23179, 0.44548, 0.33546, 0.27231, 0.40807, 0.15101, 0.49628, 0.00366, 0.79792, 0.32794, 0.6473, 0.38323, 0.55247, 0.38732, 0.43904, 0.4078, 0.3721, 0.55318, 0.30888, 0.64532, 0.23636, 0.76408, 0.20103, 0.88694], "triangles": [5, 6, 24, 6, 25, 24, 5, 24, 4, 24, 23, 4, 3, 4, 2, 6, 7, 25, 27, 26, 7, 7, 26, 25, 4, 23, 2, 23, 1, 2, 19, 20, 26, 25, 26, 21, 24, 25, 21, 21, 26, 20, 0, 24, 22, 24, 0, 23, 22, 24, 21, 23, 0, 1, 29, 28, 9, 9, 28, 8, 29, 18, 28, 28, 27, 8, 27, 28, 19, 8, 27, 7, 28, 18, 19, 27, 19, 26, 11, 12, 30, 13, 15, 16, 12, 13, 30, 13, 16, 30, 11, 30, 10, 13, 14, 15, 30, 29, 10, 10, 29, 9, 16, 17, 30, 30, 17, 29, 17, 18, 29], "vertices": [1, 53, 79.28, 53.06, 1, 1, 53, 107.67, 29.19, 1, 1, 53, 102.84, -35.63, 1, 1, 53, 101.57, -38.41, 1, 1, 53, 70.88, -49.78, 1, 1, 53, 33.49, -63.64, 1, 1, 53, 16.85, -20.87, 1, 2, 52, 48.94, -6.62, 0.01218, 53, 10.1, -3.53, 0.98782, 3, 51, 63.15, -34.25, 0.00059, 52, 33.38, -16.33, 0.86862, 53, -2.11, -17.21, 0.1308, 2, 51, 35.52, -32.98, 0.25628, 52, 9.91, -30.95, 0.74372, 2, 51, 14.59, -20.15, 0.84399, 52, -14.62, -32.26, 0.15601, 2, 51, 4.28, -13.83, 0.97671, 52, -26.69, -32.9, 0.02329, 2, 51, -5.63, -7.75, 0.99967, 52, -38.3, -33.52, 0.00033, 1, 51, -7.68, 0.2, 1, 1, 51, -6.75, 32.32, 1, 1, 51, -5.7, 32.59, 1, 1, 51, 10.52, 29.7, 1, 2, 51, 26.61, 26.84, 0.99983, 52, -31.37, 13.26, 0.00017, 3, 51, 58.23, 21.21, 0.42658, 52, -2.13, 26.56, 0.57019, 53, -48.23, 13.97, 0.00323, 3, 51, 85.52, 11.85, 0.0141, 52, 25.65, 34.33, 0.70211, 53, -23.76, 29.24, 0.28379, 2, 52, 59.21, 43.72, 0.10599, 53, 5.81, 47.68, 0.89401, 2, 52, 82.71, 50.3, 0.00664, 53, 26.52, 60.59, 0.99336, 1, 53, 51.68, 76.28, 1, 1, 53, 79.67, 3.49, 1, 1, 53, 50.47, 6.67, 1, 1, 53, 34.24, 13.37, 1, 2, 52, 58.75, 13.97, 0.04164, 53, 13.73, 18.99, 0.95836, 2, 52, 33.39, 3.65, 0.8534, 53, -7.71, 1.96, 0.1466, 1, 52, 14.41, -0.37, 1, 2, 51, 33.91, -2.34, 0.94307, 52, -8.81, -6.64, 0.05693, 2, 51, 12.29, -1.17, 0.99751, 52, -27.27, -17.94, 0.00249], "hull": 23, "edges": [2, 4, 4, 6, 24, 26, 26, 28, 28, 30, 2, 0, 0, 44, 0, 46, 6, 8, 8, 10, 46, 8, 44, 48, 48, 10, 42, 44, 42, 50, 10, 12, 12, 14, 50, 12, 40, 42, 40, 52, 52, 14, 36, 38, 38, 40, 38, 54, 14, 16, 16, 18, 54, 16, 36, 56, 56, 18, 34, 36, 34, 58, 18, 20, 58, 20, 30, 32, 32, 34, 32, 60, 20, 22, 22, 24, 60, 22], "width": 185, "height": 168}}, "h3": {"h3": {"type": "mesh", "uvs": [0.8918, 0.10448, 0.92984, 0.22341, 0.96744, 0.34096, 1, 0.44276, 1, 0.52522, 0.75002, 0.52394, 0.62157, 0.60282, 0.6756, 0.72153, 0.73232, 0.84616, 0.61295, 0.95131, 0.55767, 1, 0.38085, 1, 0.32534, 0.93294, 0.26585, 0.86108, 0.18585, 0.76444, 0.09353, 0.65292, 0, 0.53993, 0, 0.48045, 0.04882, 0.34543, 0.08817, 0.23658, 0.13312, 0.11225, 0.17223, 0.00408, 0.47332, 0.00374, 0.85944, 0.00331, 0.43465, 0.12374, 0.39599, 0.23664, 0.45785, 0.34336, 0.43207, 0.44854, 0.74141, 0.4408, 0.38567, 0.53206, 0.43207, 0.61712, 0.46301, 0.73157, 0.54807, 0.8352, 0.53003, 0.91408], "triangles": [32, 12, 13, 33, 12, 32, 8, 33, 32, 9, 33, 8, 11, 12, 33, 10, 33, 9, 11, 33, 10, 31, 6, 7, 31, 14, 30, 32, 31, 7, 32, 7, 8, 13, 14, 31, 13, 31, 32, 28, 26, 2, 28, 2, 3, 27, 18, 26, 27, 26, 28, 17, 18, 27, 5, 28, 3, 5, 3, 4, 29, 17, 27, 5, 29, 27, 5, 27, 28, 16, 17, 29, 6, 29, 5, 30, 29, 6, 15, 16, 29, 15, 29, 30, 31, 30, 6, 14, 15, 30, 24, 21, 22, 20, 21, 24, 0, 24, 22, 0, 22, 23, 25, 20, 24, 19, 20, 25, 1, 25, 24, 1, 24, 0, 26, 25, 1, 26, 1, 2, 18, 19, 25, 18, 25, 26], "vertices": [2, 74, 6.56, 60.1, 0.94365, 75, -56.09, 69.66, 0.05635, 2, 74, 32.78, 67.54, 0.75808, 75, -28.99, 72.51, 0.24192, 2, 74, 58.68, 74.89, 0.48038, 75, -2.2, 75.33, 0.51962, 2, 74, 81.12, 81.25, 0.3059, 75, 20.99, 77.76, 0.6941, 2, 74, 99.61, 82.86, 0.26058, 75, 39.48, 76.19, 0.73942, 2, 74, 102.24, 49.21, 0.14932, 75, 36.33, 42.59, 0.85068, 3, 74, 121.43, 33.47, 0.01169, 75, 52.54, 23.81, 0.92214, 76, -19.48, 24.89, 0.06617, 3, 75, 79.78, 28.81, 0.21022, 76, 7.82, 29.49, 0.76438, 77, -37.42, 24.43, 0.0254, 3, 75, 108.37, 34.06, 0.00138, 76, 36.49, 34.32, 0.60254, 77, -10.11, 34.39, 0.39609, 2, 76, 58.42, 15.93, 0.05841, 77, 14.8, 20.29, 0.94159, 1, 77, 26.34, 13.76, 1, 1, 77, 28.32, -10.03, 1, 2, 76, 50.45, -22.29, 0.09225, 77, 13.91, -18.75, 0.90775, 3, 75, 106.37, -28.97, 0.00971, 76, 33.56, -28.68, 0.54243, 77, -1.54, -28.09, 0.44785, 3, 75, 83.79, -37.89, 0.2286, 76, 10.85, -37.26, 0.72383, 77, -22.31, -40.66, 0.04756, 3, 74, 138.83, -36.57, 0.00019, 75, 57.73, -48.18, 0.71442, 76, -15.36, -47.16, 0.28539, 3, 74, 114.6, -51.35, 0.03343, 75, 31.33, -58.6, 0.90038, 76, -41.91, -57.2, 0.06619, 3, 74, 101.27, -52.51, 0.08564, 75, 17.99, -57.47, 0.88288, 76, -55.23, -55.87, 0.03148, 3, 74, 70.43, -48.57, 0.45919, 75, -11.72, -48.33, 0.54039, 76, -84.8, -46.28, 0.00042, 2, 74, 45.57, -45.4, 0.85542, 75, -35.67, -40.96, 0.14458, 2, 74, 17.18, -41.78, 0.99671, 75, -63.03, -32.54, 0.00329, 1, 74, -7.53, -38.63, 1, 1, 74, -11.12, 1.86, 1, 2, 74, -15.74, 53.78, 0.99079, 75, -79.14, 67.24, 0.00921, 1, 74, 16.22, -1, 1, 1, 74, 41.99, -4, 1, 2, 74, 65.18, 6.4, 0.61281, 75, -7.5, 6.73, 0.38719, 2, 74, 89.06, 4.98, 0.00164, 75, 15.79, 1.26, 0.99836, 2, 74, 83.71, 46.43, 0.24941, 75, 17.59, 43.02, 0.75059, 2, 75, 33.98, -6.57, 0.99536, 76, -38.49, -5.21, 0.00464, 2, 75, 53.58, -1.96, 0.99083, 76, -18.83, -0.88, 0.00917, 2, 75, 79.59, 0.02, 0.0027, 76, 7.21, 0.71, 0.9973, 2, 76, 31.56, 9.81, 0.65141, 77, -10.5, 9.39, 0.34859, 2, 76, 48.97, 5.62, 0.02468, 77, 7.39, 8.44, 0.97532], "hull": 24, "edges": [6, 8, 8, 10, 10, 12, 20, 22, 32, 34, 42, 44, 44, 46, 40, 42, 40, 48, 0, 46, 48, 0, 38, 40, 38, 50, 0, 2, 50, 2, 34, 36, 36, 38, 36, 52, 2, 4, 4, 6, 52, 4, 34, 54, 54, 56, 56, 6, 32, 58, 58, 10, 30, 32, 30, 60, 60, 12, 28, 30, 28, 62, 12, 14, 14, 16, 62, 14, 26, 28, 26, 64, 64, 16, 22, 24, 24, 26, 24, 66, 16, 18, 18, 20, 66, 18], "width": 135, "height": 225}}, "h4": {"h4": {"type": "mesh", "uvs": [1, 0.05136, 1, 0.07954, 0.96027, 0.16556, 0.91074, 0.20756, 0.78654, 0.1415, 0.75962, 0.15577, 0.68036, 0.18836, 0.57623, 0.23118, 0.48986, 0.2667, 0.42225, 0.2945, 0.38552, 0.31499, 0.35953, 0.46751, 0.33354, 0.62002, 0.20683, 0.65513, 0.17464, 0.76359, 0.16004, 0.94572, 0.07206, 0.99056, 0.02702, 1, 0.00196, 1, 0.00188, 0.7744, 0.00184, 0.6616, 0.0018, 0.5488, 0.00176, 0.436, 0.00172, 0.3232, 0.00169, 0.2104, 0.00165, 0.0976, 0, 0.00182, 0.20794, 0.00179, 0.33227, 0.00177, 0.4566, 0.00175, 0.54285, 0.00174, 0.67051, 0.00172, 0.78076, 0.0017, 0.97999, 0.00167, 0.75629, 0.07084, 0.42268, 0.06731, 0.45327, 0.17209, 0.16188, 0.08639, 0.2106, 0.19715, 0.21414, 0.32397, 0.22389, 0.46299, 0.19908, 0.55689, 0.52358, 0.09321, 0.55971, 0.1725, 0.63518, 0.09103, 0.10001, 0.3239, 0.07294, 0.8747, 0.14745, 0.44331, 0.13732, 0.20581, 0.11677, 0.65976, 0.35141, 0.22833, 0.06273, 0.40985, 0.08046, 0.20738, 0.307, 0.35963, 0.30585, 0.10768, 0.26484, 0.55247, 0.12521, 0.51362, 0.07365, 0.74679, 0.07652, 0.1613, 0.89674, 0.08996, 0.29368, 0.28476], "triangles": [46, 19, 57, 46, 57, 14, 15, 46, 14, 46, 18, 19, 16, 46, 15, 46, 17, 18, 16, 17, 46, 41, 40, 55, 41, 56, 40, 13, 41, 55, 13, 55, 12, 49, 21, 56, 49, 56, 41, 49, 41, 13, 20, 21, 49, 57, 20, 49, 14, 49, 13, 57, 49, 14, 19, 20, 57, 23, 52, 45, 45, 48, 39, 51, 23, 45, 22, 23, 51, 47, 45, 39, 51, 45, 47, 47, 39, 40, 56, 51, 47, 56, 21, 22, 56, 22, 51, 40, 56, 47, 37, 26, 27, 25, 26, 37, 58, 25, 37, 38, 37, 54, 48, 58, 37, 38, 48, 37, 52, 58, 48, 24, 25, 58, 24, 58, 52, 23, 24, 52, 45, 52, 48, 48, 38, 39, 53, 60, 10, 39, 60, 53, 40, 39, 53, 11, 53, 10, 40, 53, 11, 55, 40, 11, 12, 55, 11, 50, 54, 36, 38, 54, 50, 9, 50, 36, 60, 38, 50, 8, 9, 36, 10, 60, 50, 9, 10, 50, 39, 38, 60, 35, 28, 29, 54, 27, 28, 54, 28, 35, 37, 27, 54, 54, 35, 36, 43, 42, 44, 36, 42, 43, 6, 44, 5, 43, 44, 6, 7, 43, 6, 8, 36, 43, 8, 43, 7, 44, 30, 31, 42, 29, 30, 42, 30, 44, 35, 29, 42, 36, 35, 42, 59, 33, 0, 59, 32, 33, 1, 59, 0, 2, 59, 1, 3, 59, 2, 4, 59, 3, 34, 31, 32, 34, 32, 59, 44, 31, 34, 4, 34, 59, 5, 34, 4, 44, 34, 5], "vertices": [1, 89, -8.12, 57.73, 1, 1, 89, 6.68, 55.33, 1, 1, 89, 48.79, 29.1, 1, 1, 89, 67.02, 1.96, 1, 4, 88, 50.48, -50.75, 0.68516, 89, 22.75, -51.51, 0.08042, 86, 82.51, 113.08, 0.0103, 87, -26.93, 110.75, 0.22412, 4, 88, 53.73, -65.43, 0.59896, 89, 28.17, -65.53, 0.01334, 86, 88.19, 99.16, 0.01269, 87, -15.39, 101.11, 0.37502, 3, 88, 58.5, -107.11, 0.29147, 86, 99.95, 58.89, 0.00011, 87, 13.87, 71.04, 0.70842, 3, 88, 64.77, -161.87, 0.02829, 87, 52.3, 31.53, 0.9214, 84, -0.55, 110.23, 0.05031, 3, 87, 84.18, -1.24, 0.54504, 84, 28.47, 74.91, 0.45462, 85, -32.08, 82.16, 0.00033, 3, 87, 109.13, -26.89, 0.18844, 84, 51.19, 47.26, 0.73748, 85, -14.64, 50.91, 0.07409, 3, 87, 125.39, -39.86, 0.05281, 84, 66.3, 32.97, 0.52201, 85, -2.3, 34.17, 0.42517, 3, 85, 79.6, 28.43, 0.785, 80, 77.12, 132.95, 0.0066, 81, -34.83, 131.49, 0.2084, 2, 85, 161.5, 22.68, 0.36698, 81, 46.42, 119.75, 0.63302, 3, 85, 185.18, -36.63, 0.11557, 81, 65.69, 58.85, 0.8482, 82, -63.5, 46.67, 0.03622, 3, 85, 243.97, -47.3, 0.00212, 81, 123.53, 43.89, 0.20545, 82, -3.88, 42.77, 0.79243, 1, 82, 92.51, 54.88, 1, 1, 82, 124.22, 17.97, 1, 1, 82, 133.4, -2.33, 1, 1, 82, 135.77, -14.18, 1, 2, 81, 130.09, -39.32, 0.26383, 82, 18.09, -37.75, 0.73617, 2, 81, 70.08, -39.91, 0.99896, 82, -40.75, -49.54, 0.00104, 2, 80, 149.85, -29.26, 0.22157, 81, 10.07, -40.51, 0.77843, 2, 80, 90.77, -39.77, 0.99723, 81, -49.93, -41.1, 0.00277, 2, 79, 145.36, -57.89, 0.09363, 80, 31.68, -50.28, 0.90637, 2, 79, 85.49, -53.82, 0.84021, 80, -27.4, -60.79, 0.15979, 1, 79, 25.62, -49.74, 1, 1, 79, -25.27, -47.06, 1, 2, 83, -38.63, -37.71, 0.5037, 79, -18.45, 52.94, 0.4963, 3, 86, -22.05, -93.16, 0.06051, 83, -16.56, 18.01, 0.93878, 79, -14.37, 112.73, 0.00072, 2, 86, -13.58, -33.84, 0.66033, 83, 5.51, 73.72, 0.33967, 3, 88, -56.34, -139.71, 0.01191, 86, -7.71, 7.32, 0.97879, 87, -57.1, -24.95, 0.0093, 3, 88, -37.46, -81.14, 0.40331, 86, 0.98, 68.23, 0.34365, 87, -77.95, 32.94, 0.25304, 3, 88, -21.15, -30.57, 0.81105, 86, 8.49, 120.84, 0.06371, 87, -95.96, 82.93, 0.12524, 2, 88, 8.32, 60.83, 0.03051, 89, -35.76, 52.44, 0.96949, 3, 88, 10.23, -53.08, 0.70337, 86, 43.23, 103.96, 0.06472, 87, -57.36, 84.3, 0.2319, 4, 86, 18.63, -54.95, 0.41536, 87, -4.66, -67.63, 0.01013, 83, 31.91, 45.68, 0.57286, 84, -65.61, 16.19, 0.00165, 4, 86, 75.9, -48.25, 0.14394, 87, 42.8, -34.88, 0.37941, 83, 89.16, 38.85, 0.15929, 84, -15.58, 44.85, 0.31735, 3, 83, -4.96, -74.93, 0.1806, 84, -23.28, -102.62, 0.01698, 79, 24.94, 27.72, 0.80241, 5, 83, 58.47, -74.8, 0.11258, 84, 27.56, -64.69, 0.26304, 85, -57.77, -55.05, 0.02438, 79, 85.33, 47.13, 0.59838, 80, -51.94, 37.12, 0.00163, 6, 83, 121.82, -98.07, 1e-05, 84, 92.28, -45.58, 0.06209, 85, 9.32, -47.75, 0.45, 79, 152.75, 44.23, 0.14131, 80, 14.18, 50.6, 0.34599, 81, -110.52, 60.69, 0.00059, 3, 85, 82.63, -36.92, 0.46643, 80, 86.18, 68.16, 0.26486, 81, -36.61, 66.1, 0.26871, 3, 85, 133.41, -44.69, 0.22062, 80, 137.46, 65.12, 0.04802, 81, 13.46, 54.62, 0.73136, 3, 86, 39.15, -8.76, 0.96411, 83, 62.63, 85.81, 0.03413, 84, -64.89, 66.72, 0.00175, 2, 88, 32.62, -159.86, 0.02131, 87, 25.62, 13.46, 0.97869, 3, 88, 2.53, -111.94, 0.23822, 86, 45.61, 44.65, 0.23434, 87, -27.48, 33.01, 0.52743, 1, 80, 23.77, -3.57, 1, 1, 82, 63.7, 6.3, 1, 3, 85, 75.26, -74.51, 0.16168, 80, 82.32, 30.05, 0.71604, 81, -46.72, 29.15, 0.12228, 4, 83, 49.74, -109.33, 0.013, 84, 41.14, -97.61, 0.03357, 85, -50.25, -89.87, 0.00456, 79, 87.51, 11.57, 0.94887, 3, 85, 191.24, -79.68, 0.01279, 81, 68.57, 15.47, 0.96858, 82, -52.57, 4.59, 0.01863, 3, 86, 98.58, -101.08, 0.00028, 87, 87.58, -70.94, 0.01413, 84, 26.02, 5.16, 0.9856, 1, 80, 71.93, -13.27, 1, 2, 79, 86.48, -15.83, 0.95673, 80, -35.62, -23.69, 0.04327, 4, 85, 24.51, -1.57, 0.99421, 79, 174.73, 87.59, 0.00105, 80, 25.04, 97.99, 0.00473, 81, -91.97, 105.62, 1e-05, 3, 83, 31.13, -14.59, 0.88608, 84, -30.3, -32.65, 0.04822, 79, 40.97, 96.18, 0.06569, 3, 85, 128.43, -13.3, 0.3859, 80, 129.61, 95.91, 0.02751, 81, 10.81, 86.29, 0.58659, 3, 85, 113.42, -82.08, 0.06543, 80, 121.02, 26.04, 0.26414, 81, -9.21, 18.8, 0.67043, 2, 81, 115.06, -4.86, 0.39411, 82, -3.1, -6.71, 0.60589, 2, 79, 61.89, -16.05, 0.99415, 80, -59.43, -29.84, 0.00585, 1, 89, 4.19, 5.31, 1, 5, 83, 116.55, -54.74, 0.0009, 84, 62.22, -13.95, 0.62231, 85, -14.65, -11.28, 0.29138, 79, 134.56, 83.9, 0.06219, 80, -13.06, 84.7, 0.02321], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 0], "width": 482, "height": 532}}, "f3": {"f3": {"type": "mesh", "uvs": [0.35408, 0, 0.49817, 0.02804, 0.64203, 0.05603, 0.78303, 0.08347, 0.86349, 0.09912, 0.88223, 0.19037, 0.90226, 0.28797, 0.92485, 0.39799, 0.93518, 0.4483, 0.94994, 0.52021, 0.96287, 0.58319, 0.97666, 0.65037, 0.99068, 0.71867, 1, 0.76407, 1, 0.83134, 1, 0.86516, 0.87145, 0.93262, 0.79567, 0.97239, 0.74305, 1, 0.68743, 1, 0.63551, 0.9202, 0.67318, 0.90492, 0.74109, 0.87737, 0.8371, 0.83842, 0.80752, 0.79265, 0.77624, 0.74427, 0.72911, 0.67138, 0.69325, 0.61592, 0.6435, 0.53896, 0.59908, 0.47026, 0.56712, 0.42084, 0.51878, 0.34606, 0.47553, 0.27916, 0.44284, 0.22861, 0.41063, 0.17878, 0.36634, 0.11029, 0.28632, 0.07954, 0.22588, 0.05632, 0.14423, 0.04423, 0, 0.02288, 0, 0.01396, 0.17446, 0.00584, 0.30003, 0, 0.40298, 0.06339, 0.53811, 0.09815, 0.59264, 0.14159, 0.64954, 0.18006, 0.67087, 0.25578, 0.6685, 0.31411, 0.65665, 0.40102, 0.65665, 0.45936, 0.72777, 0.52638, 0.79653, 0.59092, 0.88424, 0.65546, 0.90084, 0.73329, 0.90321, 0.78666, 0.91506, 0.83382, 0.8795, 0.88347, 0.7989, 0.93808], "triangles": [57, 23, 56, 22, 23, 57, 57, 58, 22, 16, 57, 15, 16, 58, 57, 21, 22, 58, 21, 58, 20, 17, 58, 16, 17, 19, 58, 19, 20, 58, 18, 19, 17, 12, 54, 53, 12, 53, 11, 25, 26, 53, 54, 25, 53, 54, 12, 13, 55, 54, 13, 24, 25, 54, 55, 24, 54, 55, 13, 14, 56, 55, 14, 23, 24, 55, 23, 55, 56, 56, 14, 15, 57, 56, 15, 51, 50, 8, 51, 8, 9, 28, 50, 51, 52, 51, 9, 52, 9, 10, 27, 28, 51, 27, 51, 52, 53, 52, 10, 53, 10, 11, 26, 27, 52, 26, 52, 53, 31, 32, 48, 49, 31, 48, 49, 48, 7, 30, 31, 49, 50, 30, 49, 50, 49, 7, 50, 7, 8, 29, 30, 50, 28, 29, 50, 45, 2, 3, 46, 45, 3, 46, 3, 4, 46, 4, 5, 33, 34, 45, 33, 45, 46, 47, 46, 5, 33, 46, 47, 32, 33, 47, 47, 5, 6, 48, 32, 47, 48, 47, 6, 48, 6, 7, 38, 39, 40, 41, 38, 40, 37, 41, 42, 38, 41, 37, 43, 0, 1, 36, 37, 42, 36, 42, 0, 43, 36, 0, 44, 1, 2, 43, 1, 44, 35, 36, 43, 35, 43, 44, 45, 44, 2, 34, 35, 44, 34, 44, 45], "vertices": [1, 90, 5.33, 9.36, 1, 2, 90, 18.84, 11.51, 0.99944, 91, -20.71, -6.29, 0.00056, 2, 90, 32.33, 13.66, 0.66548, 91, -13.96, 5.59, 0.33452, 2, 90, 45.56, 15.76, 0.07578, 91, -7.35, 17.23, 0.92422, 2, 90, 53.1, 16.96, 0.00733, 91, -3.57, 23.87, 0.99267, 2, 91, 12, 23.03, 0.99806, 92, -22.66, 21.09, 0.00194, 3, 91, 28.67, 22.14, 0.78242, 92, -6.14, 23.39, 0.20156, 93, -13.33, 41.79, 0.01602, 3, 91, 47.45, 21.13, 0.2008, 92, 12.5, 25.98, 0.54961, 93, 2.32, 31.36, 0.24959, 3, 91, 56.04, 20.67, 0.07216, 92, 21.02, 27.17, 0.431, 93, 9.48, 26.59, 0.49683, 3, 91, 68.32, 20.01, 0.00885, 92, 33.19, 28.86, 0.15128, 93, 19.72, 19.77, 0.83987, 4, 91, 79.07, 19.43, 0.00013, 92, 43.86, 30.35, 0.02608, 93, 28.68, 13.8, 0.91606, 94, -16.33, 8.13, 0.05772, 3, 92, 55.24, 31.93, 0.00013, 93, 38.24, 7.43, 0.34027, 94, -4.84, 8.38, 0.6596, 1, 94, 6.83, 8.63, 1, 1, 94, 14.59, 8.79, 1, 2, 94, 25.99, 7.82, 0.93466, 95, -5.7, 8.48, 0.06534, 2, 94, 31.71, 7.33, 0.64413, 95, -0.38, 10.66, 0.35587, 2, 94, 42.16, -5.05, 0.00015, 95, 14.57, 4.41, 0.99985, 1, 95, 23.38, 0.73, 1, 1, 95, 29.5, -1.83, 1, 1, 95, 31.37, -6.41, 1, 1, 95, 20.57, -15.82, 1, 1, 95, 16.89, -13.7, 1, 1, 95, 10.27, -9.88, 1, 2, 94, 25.95, -6.73, 0.08929, 95, 0.91, -4.48, 0.91071, 2, 94, 17.97, -8.69, 0.83865, 95, -5.3, -9.86, 0.16135, 3, 93, 39.08, -16.49, 0.05398, 94, 9.54, -10.76, 0.93502, 95, -11.86, -15.55, 0.011, 2, 93, 26.88, -11.78, 0.6726, 94, -3.17, -13.88, 0.3274, 2, 93, 17.59, -8.19, 0.99137, 94, -12.83, -16.26, 0.00863, 1, 93, 4.7, -3.22, 1, 2, 92, 25.68, -2.61, 0.84419, 93, -6.8, 1.22, 0.15581, 1, 92, 17.37, -5.72, 1, 3, 90, 47.01, -34.68, 0.00122, 91, 32.95, -13.13, 0.05146, 92, 4.8, -10.42, 0.94732, 3, 90, 38.06, -26.66, 0.06305, 91, 21.11, -15.11, 0.57409, 92, -6.45, -14.62, 0.36286, 3, 90, 31.31, -20.61, 0.28037, 91, 12.16, -16.61, 0.6323, 92, -14.94, -17.8, 0.08733, 3, 90, 24.65, -14.64, 0.68498, 91, 3.34, -18.08, 0.3038, 92, -23.32, -20.93, 0.01121, 2, 90, 15.5, -6.43, 0.99356, 91, -8.78, -20.11, 0.00644, 1, 90, 6.72, -5.38, 1, 1, 90, 0.1, -4.58, 1, 1, 90, -7.24, -6.37, 1, 1, 90, -20.2, -9.51, 1, 1, 90, -20.95, -8.19, 1, 1, 90, -8.1, 0.64, 1, 1, 90, 1.14, 7, 1, 1, 90, 14.42, 2.11, 1, 2, 90, 27.8, 2.88, 0.99837, 91, -8.37, -4.69, 0.00163, 2, 90, 35.65, -1.17, 0.14306, 91, -0.31, -1.08, 0.85694, 1, 91, 6.96, 2.87, 1, 1, 91, 19.97, 2.68, 1, 3, 91, 29.72, 0.89, 0.50148, 92, -1.04, 2.73, 0.49792, 93, -23.24, 22.96, 0.0006, 3, 91, 44.14, -2.52, 0.00664, 92, 13.76, 2.14, 0.98373, 93, -12.56, 12.69, 0.00963, 3, 91, 53.93, -4.1, 0.00126, 92, 23.67, 2.45, 0.82098, 93, -4.94, 6.34, 0.17776, 3, 91, 66.19, 0.32, 0.00076, 92, 34.86, 9.13, 0.05366, 93, 7.86, 3.91, 0.94558, 2, 92, 45.64, 15.59, 0.00277, 93, 20.21, 1.59, 0.99723, 2, 93, 33.64, 0.56, 0.44668, 94, -4.68, 0.11, 0.55332, 1, 94, 8.62, 0.45, 1, 2, 94, 17.68, -0.12, 0.99927, 95, -9.47, -2.37, 0.00073, 2, 94, 25.76, 0.25, 0.96704, 95, -2.44, 1.65, 0.03296, 2, 94, 33.9, -3.62, 0.01971, 95, 6.57, 1.91, 0.98029, 1, 95, 17.87, -1.21, 1], "hull": 43, "edges": [0, 84, 36, 38, 38, 40, 78, 80, 80, 82, 82, 84, 74, 76, 76, 78, 82, 76, 70, 72, 72, 74, 72, 86, 0, 2, 86, 2, 70, 88, 2, 4, 88, 4, 68, 70, 68, 90, 4, 6, 6, 8, 90, 6, 66, 68, 66, 92, 92, 8, 64, 66, 64, 94, 8, 10, 94, 10, 62, 64, 62, 96, 10, 12, 96, 12, 60, 62, 60, 98, 12, 14, 98, 14, 58, 60, 58, 100, 14, 16, 100, 16, 56, 58, 56, 102, 16, 18, 102, 18, 54, 56, 54, 104, 18, 20, 104, 20, 52, 54, 52, 106, 20, 22, 106, 22, 50, 52, 50, 108, 22, 24, 24, 26, 108, 24, 46, 48, 48, 50, 48, 110, 110, 26, 46, 112, 26, 28, 28, 30, 112, 28, 44, 46, 44, 114, 114, 30, 30, 32, 32, 116, 40, 42, 42, 44, 116, 42, 32, 34, 34, 36], "width": 89, "height": 170}}, "f4": {"f4": {"type": "mesh", "uvs": [0.68873, 0, 1, 0.09454, 1, 0.1576, 1, 0.21531, 1, 0.27549, 0.95946, 0.33564, 0.93502, 0.37191, 0.89253, 0.43495, 0.84558, 0.50461, 0.8207, 0.54153, 0.78959, 0.58769, 0.73716, 0.66548, 0.67872, 0.7522, 0.63395, 0.81862, 0.59033, 0.88334, 0.5525, 0.93948, 0.512, 0.99957, 0.41966, 0.99543, 0.32503, 0.99119, 0.33924, 0.92252, 0.35209, 0.86041, 0.36549, 0.7957, 0.38617, 0.69575, 0.40476, 0.60594, 0.42344, 0.51565, 0.18593, 0.47871, 0.16606, 0.44821, 0.11696, 0.37287, 0.06327, 0.29047, 0.02302, 0.22871, 0, 0.19339, 0, 0.17142, 0, 0.12523, 0.04721, 0.10943, 0.16304, 0.07067, 0.37424, 0, 0.4118, 0.08689, 0.70272, 0.12184, 0.37926, 0.13322, 0.68932, 0.17792, 0.36012, 0.18768, 0.63573, 0.2462, 0.35629, 0.24376, 0.49601, 0.27627, 0.78885, 0.30309, 0.27973, 0.30553, 0.53812, 0.33235, 0.81756, 0.35836, 0.207, 0.37024, 0.50367, 0.39055, 0.82139, 0.42794, 0.44816, 0.43282, 0.69124, 0.50109, 0.47496, 0.4889, 0.69889, 0.53117, 0.47496, 0.53117, 0.47113, 0.62298, 0.45007, 0.71889, 0.44175, 0.79983, 0.4341, 0.87054, 0.42836, 0.93231], "triangles": [15, 16, 60, 16, 17, 60, 17, 18, 60, 18, 19, 60, 15, 60, 14, 60, 59, 14, 60, 19, 59, 19, 20, 59, 14, 59, 13, 59, 58, 13, 59, 20, 58, 20, 21, 58, 13, 58, 12, 58, 57, 12, 58, 21, 57, 21, 22, 57, 12, 57, 11, 57, 56, 11, 57, 22, 56, 22, 23, 56, 11, 56, 10, 55, 54, 10, 10, 54, 9, 9, 54, 8, 54, 53, 52, 54, 52, 8, 52, 50, 8, 8, 50, 7, 50, 52, 49, 7, 50, 6, 50, 47, 6, 6, 47, 5, 10, 56, 55, 56, 23, 55, 23, 24, 55, 24, 53, 55, 54, 55, 53, 24, 25, 53, 25, 51, 53, 52, 53, 51, 49, 52, 51, 25, 26, 51, 26, 48, 51, 26, 27, 48, 51, 48, 49, 50, 49, 47, 49, 46, 47, 48, 45, 49, 49, 45, 46, 27, 28, 48, 48, 28, 45, 46, 44, 47, 45, 43, 46, 47, 44, 5, 5, 44, 4, 46, 43, 44, 45, 42, 43, 45, 28, 42, 43, 41, 44, 44, 41, 4, 28, 29, 42, 41, 43, 40, 4, 41, 3, 43, 42, 40, 41, 40, 39, 42, 29, 40, 29, 30, 40, 30, 31, 40, 31, 33, 40, 40, 38, 39, 40, 33, 38, 31, 32, 33, 33, 34, 38, 38, 34, 36, 41, 39, 3, 39, 2, 3, 39, 38, 37, 39, 37, 2, 37, 38, 36, 37, 1, 2, 36, 0, 37, 37, 0, 1, 34, 35, 36, 36, 35, 0], "vertices": [3, 15, 30.86, 10.93, 0.97155, 16, -50.97, -1.81, 0.02845, 18, -72.73, 62.67, 0, 3, 15, 20.51, -23.12, 0.99258, 16, -37.39, 31.09, 0.00595, 18, -39.75, 76.05, 0.00147, 4, 15, 7.43, -27.54, 0.88962, 16, -23.94, 34.22, 0.09047, 17, -42.61, 35.64, 0.00254, 18, -28.09, 68.66, 0.01737, 4, 15, -4.54, -31.59, 0.65784, 16, -11.63, 37.1, 0.25383, 17, -30.2, 38, 0.02396, 18, -17.41, 61.9, 0.06437, 4, 15, -17.03, -35.81, 0.38872, 16, 1.2, 40.09, 0.37605, 17, -17.25, 40.47, 0.07952, 18, -6.27, 54.85, 0.15571, 4, 15, -30.72, -36.45, 0.17601, 16, 14.89, 39.41, 0.33454, 17, -3.61, 39.23, 0.16382, 18, 2.84, 44.62, 0.32563, 4, 15, -38.97, -36.84, 0.09074, 16, 23.14, 39, 0.2396, 17, 4.62, 38.48, 0.18981, 18, 8.33, 38.45, 0.47985, 4, 15, -53.31, -37.52, 0.01933, 16, 37.48, 38.29, 0.08349, 17, 18.92, 37.18, 0.11792, 18, 17.88, 27.72, 0.77926, 6, 15, -69.16, -38.27, 0.00087, 16, 53.33, 37.51, 0.01069, 17, 34.72, 35.75, 0.01317, 19, 1.31, 34.48, 0.00514, 18, 28.43, 15.87, 0.95883, 20, -28.09, 37.7, 0.0113, 5, 16, 61.73, 37.09, 0.00132, 17, 43.1, 34.99, 0.00038, 19, 9.57, 32.87, 0.06044, 18, 34.03, 9.59, 0.88076, 20, -19.94, 35.61, 0.0571, 3, 19, 19.89, 30.87, 0.18128, 18, 41.02, 1.74, 0.61226, 20, -9.76, 32.99, 0.20645, 4, 19, 37.29, 27.49, 0.13669, 18, 52.8, -11.5, 0.19119, 20, 7.4, 28.58, 0.66458, 21, -35.71, 26.34, 0.00754, 4, 19, 56.68, 23.72, 0.00738, 18, 65.94, -26.25, 0.02125, 20, 26.53, 23.66, 0.78421, 21, -16.56, 21.49, 0.18716, 3, 18, 76, -37.55, 0.00064, 20, 41.19, 19.89, 0.30148, 21, -1.89, 17.78, 0.69788, 2, 20, 55.47, 16.21, 0.00332, 21, 12.4, 14.16, 0.99668, 1, 21, 24.79, 11.02, 1, 1, 21, 38.06, 7.66, 1, 1, 21, 37.42, -0.95, 1, 1, 21, 36.76, -9.77, 1, 1, 21, 21.69, -8.91, 1, 2, 20, 51.04, -6.07, 0.00247, 21, 8.06, -8.14, 0.99753, 2, 20, 36.84, -5.21, 0.80051, 21, -6.14, -7.33, 0.19949, 1, 20, 14.91, -3.88, 1, 2, 19, 26.97, -4.44, 0.42626, 20, -4.8, -2.68, 0.57374, 2, 17, 44.45, -2.36, 0.032, 19, 7.12, -4.42, 0.968, 4, 16, 61.74, -23.52, 0.01293, 17, 40.63, -25.58, 0.46736, 19, 0.98, -27.13, 0.51971, 18, -9.18, -32.92, 0, 3, 16, 55.66, -26.84, 0.0298, 17, 34.42, -28.64, 0.52507, 19, -5.52, -29.55, 0.44513, 4, 15, -63.52, 35.17, 0.00012, 16, 40.63, -35.04, 0.17849, 17, 19.06, -36.21, 0.61423, 19, -21.56, -35.53, 0.20716, 4, 15, -48.02, 45.67, 0.01977, 16, 24.19, -44, 0.47815, 17, 2.27, -44.5, 0.4364, 19, -39.1, -42.06, 0.06568, 5, 15, -36.41, 53.55, 0.07336, 16, 11.87, -50.72, 0.64226, 17, -10.32, -50.7, 0.26305, 19, -52.25, -46.97, 0.02132, 18, -63.54, -16.43, 0, 5, 15, -29.76, 58.05, 0.11242, 16, 4.82, -54.56, 0.6828, 17, -17.52, -54.25, 0.19479, 19, -59.77, -49.77, 0.00999, 18, -71.23, -14.1, 0, 5, 15, -25.21, 59.59, 0.13666, 16, 0.14, -55.66, 0.69335, 17, -22.24, -55.15, 0.16402, 19, -64.57, -50.19, 0.00596, 18, -75.29, -11.52, 0, 5, 15, -15.62, 62.83, 0.1832, 16, -9.71, -57.95, 0.69495, 17, -32.18, -57.05, 0.1203, 19, -74.64, -51.06, 0.00155, 18, -83.84, -6.11, 0, 5, 15, -10.94, 59.78, 0.20952, 16, -14.08, -54.46, 0.68504, 17, -36.4, -53.38, 0.10466, 19, -78.47, -46.99, 0.00078, 18, -84.41, -0.55, 0, 4, 15, 0.55, 52.29, 0.3526, 16, -24.79, -45.9, 0.5975, 17, -46.75, -44.39, 0.0499, 18, -85.82, 13.09, 0, 4, 15, 21.5, 38.64, 0.69662, 16, -44.33, -30.29, 0.30201, 17, -65.63, -27.99, 0.00137, 18, -88.38, 37.97, 0, 4, 15, 4.59, 29.24, 0.59981, 16, -26.59, -22.57, 0.39308, 17, -47.59, -21, 0.00711, 18, -70.44, 30.74, 0, 1, 15, 6, 1.16, 1, 5, 15, -5.99, 28.86, 0.40294, 16, -16.02, -23.21, 0.57126, 17, -37.06, -22.07, 0.02574, 19, -75.96, -15.77, 6e-05, 18, -63.48, 22.75, 0, 3, 15, -6.04, -1.6, 0.7441, 16, -13.04, 7.1, 0.25263, 18, -39.79, 41.87, 0.00327, 5, 15, -17.86, 26.73, 0.17629, 16, -4, -22.23, 0.74491, 17, -25.01, -21.59, 0.07638, 19, -63.92, -16.51, 0.00242, 18, -54.36, 14.87, 0, 4, 15, -21.8, -1.66, 0.07097, 16, 2.65, 5.64, 0.91245, 17, -17.22, 5.99, 0.0046, 18, -29.82, 29.66, 0.01198, 4, 15, -29.61, 23.13, 0.03685, 16, 8.04, -19.79, 0.74367, 17, -12.88, -19.64, 0.20729, 19, -51.65, -15.8, 0.01218, 3, 16, 12.02, -5.52, 0.89643, 17, -8.32, -5.54, 0.10159, 19, -45.69, -2.24, 0.00197, 4, 15, -29.04, -19.14, 0.15725, 16, 11.55, 22.34, 0.46454, 17, -7.64, 22.31, 0.18394, 18, -11.67, 35.03, 0.19428, 4, 15, -44.7, 25.55, 0.00391, 16, 22.83, -23.65, 0.3906, 17, 1.74, -24.1, 0.53757, 19, -37.56, -21.72, 0.06793, 3, 15, -42.58, 0.9, 0.00018, 17, 3.02, 0.6, 0.99654, 18, -18.74, 11.9, 0.00328, 4, 15, -39.65, -25.54, 0.0744, 16, 22.73, 27.69, 0.24486, 17, 3.75, 27.2, 0.24774, 18, -0.02, 30.81, 0.43299, 4, 15, -60.29, 27.42, 2e-05, 16, 38.17, -27.01, 0.15435, 17, 16.93, -28.1, 0.63713, 19, -22.86, -27.23, 0.20851, 3, 16, 36.23, 0.87, 2e-05, 17, 16.14, -0.16, 0.99933, 19, -20.81, 0.64, 0.00064, 4, 15, -53.98, -30.76, 0.01682, 16, 37.49, 31.5, 0.07632, 17, 18.65, 30.4, 0.12669, 18, 13.04, 22.95, 0.78017, 4, 16, 46.42, -2.06, 0.00087, 17, 26.2, -3.5, 0.5262, 19, -11.15, -3.7, 0.29303, 18, -4.63, -6.94, 0.1799, 6, 15, -73.03, -24.42, 4e-05, 16, 55.84, 23.35, 0.00193, 17, 36.65, 21.5, 0.00158, 19, 1.79, 20.11, 0.00729, 18, 20.1, 4.16, 0.98252, 20, -28.47, 23.33, 0.00664, 2, 17, 37.8, 1.25, 0.00125, 19, 0.87, -0.16, 0.99875, 4, 16, 62.09, 25.54, 0.00025, 19, 8.29, 21.39, 0.10581, 18, 26.05, 1.23, 0.85292, 20, -21.91, 24.22, 0.04102, 2, 19, 10.09, 0.64, 0.98879, 18, 14.9, -16.36, 0.01121, 3, 19, 30.16, 2.03, 0.19248, 18, 31.7, -27.42, 0.01683, 20, -1.23, 3.59, 0.79069, 3, 18, 48.4, -40.31, 0.00116, 20, 19.82, 2.2, 0.99508, 21, -23.2, 0.02, 0.00376, 2, 20, 37.56, 1.91, 0.61217, 21, -5.46, -0.21, 0.38783, 1, 21, 10.04, -0.45, 1, 1, 21, 23.58, -0.56, 1], "hull": 36, "edges": [0, 70, 0, 2, 48, 50, 68, 70, 68, 72, 72, 74, 2, 4, 74, 4, 64, 66, 66, 68, 66, 76, 76, 78, 4, 6, 6, 8, 78, 6, 60, 62, 62, 64, 62, 80, 80, 82, 82, 8, 58, 60, 58, 84, 84, 86, 86, 88, 8, 10, 88, 10, 56, 58, 56, 90, 90, 92, 92, 94, 10, 12, 94, 12, 54, 56, 54, 96, 96, 98, 98, 100, 12, 14, 100, 14, 50, 52, 52, 54, 52, 102, 102, 104, 14, 16, 104, 16, 50, 106, 106, 108, 16, 18, 108, 18, 48, 110, 18, 20, 110, 20, 46, 48, 46, 112, 20, 22, 112, 22, 44, 46, 44, 114, 22, 24, 114, 24, 42, 44, 42, 116, 24, 26, 116, 26, 40, 42, 40, 118, 26, 28, 118, 28, 36, 38, 38, 40, 38, 120, 28, 30, 30, 32, 120, 30, 32, 34, 34, 36], "width": 93, "height": 219}}, "d3": {"d3": {"type": "mesh", "uvs": [0.14962, 0, 0.20914, 0.04031, 0.30989, 0.10856, 0.42902, 0.18924, 0.52985, 0.25753, 0.68134, 0.36014, 0.77734, 0.42516, 1, 0.57597, 1, 0.69913, 0.97469, 0.78818, 0.91447, 1, 0.81282, 1, 0.61422, 0.97633, 0.48902, 0.86592, 0.39526, 0.78323, 0.28092, 0.68239, 0.19024, 0.60242, 0.13258, 0.55156, 0.07758, 0.39689, 0.03945, 0.28965, 0, 0.1787, 0, 0.06505, 0.06915, 0, 0.6914, 0.77366, 0.62584, 0.69476, 0.53843, 0.60183, 0.45414, 0.49312, 0.37609, 0.43876, 0.28243, 0.3318, 0.21375, 0.23186, 0.17004, 0.14594, 0.10448, 0.06703], "triangles": [10, 11, 9, 12, 23, 11, 11, 23, 9, 12, 13, 23, 13, 14, 23, 9, 23, 8, 14, 24, 23, 23, 24, 8, 24, 7, 8, 14, 15, 24, 15, 25, 24, 24, 25, 7, 15, 16, 25, 16, 26, 25, 16, 17, 26, 25, 6, 7, 25, 26, 6, 17, 27, 26, 17, 18, 27, 26, 5, 6, 26, 27, 5, 27, 4, 5, 18, 28, 27, 27, 28, 4, 18, 19, 28, 19, 29, 28, 28, 3, 4, 28, 29, 3, 19, 20, 29, 20, 30, 29, 29, 2, 3, 29, 30, 2, 20, 31, 30, 20, 21, 31, 30, 1, 2, 30, 31, 1, 21, 22, 31, 31, 0, 1, 31, 22, 0], "vertices": [1, 61, 51.41, -2.33, 1, 1, 61, 44.39, -5.36, 1, 1, 61, 32.5, -10.48, 1, 2, 61, 18.45, -16.54, 0.9795, 60, 62.53, -17.81, 0.0205, 3, 61, 6.55, -21.66, 0.83119, 60, 50.13, -21.56, 0.16861, 59, 83.85, -24.18, 0.0002, 3, 61, -11.32, -29.36, 0.31301, 60, 31.51, -27.21, 0.65818, 59, 64.79, -28.11, 0.02881, 3, 61, -22.65, -34.24, 0.09028, 60, 19.71, -30.79, 0.79986, 59, 52.71, -30.6, 0.10986, 2, 60, -7.66, -39.09, 0.48257, 59, 24.7, -36.38, 0.51743, 2, 60, -24.24, -32.12, 0.20348, 59, 8.83, -27.94, 0.79652, 2, 60, -35.42, -25.17, 0.04163, 59, -1.68, -20, 0.95837, 1, 59, -26.66, -1.12, 1, 1, 59, -22.75, 6.24, 1, 1, 59, -12.05, 19, 1, 2, 60, -30.45, 15.94, 0.06049, 59, 7, 20.49, 0.93951, 2, 60, -16.35, 18.35, 0.54364, 59, 21.27, 21.61, 0.45636, 2, 60, 0.86, 21.29, 0.9932, 59, 38.67, 22.97, 0.0068, 2, 61, -33.92, 19.24, 0.04722, 60, 14.5, 23.62, 0.95278, 2, 61, -25.47, 21.68, 0.18405, 60, 23.18, 25.1, 0.81595, 2, 61, -2.53, 19.65, 0.93693, 60, 45.75, 20.51, 0.06307, 1, 61, 13.37, 18.24, 1, 1, 61, 29.83, 16.79, 1, 1, 61, 45.75, 12.11, 1, 1, 61, 53.27, 4, 1, 1, 59, 11.1, -0.49, 1, 2, 60, -11.76, -4.09, 0.46692, 59, 23.8, -1.15, 0.53308, 2, 60, 3.52, -2.74, 0.96091, 59, 39.14, -1.19, 0.03909, 3, 61, -24.7, -6.02, 0.0098, 60, 20.83, -2.51, 0.98528, 59, 56.4, -2.55, 0.00492, 3, 61, -15.29, -2.11, 0.26331, 60, 30.63, 0.31, 0.73654, 59, 66.41, -0.62, 0.00015, 2, 61, 1.86, 0.86, 0.99578, 60, 48, 1.34, 0.00422, 2, 61, 17.45, 2.15, 1, 60, 63.63, 0.88, 0, 1, 61, 30.49, 2.06, 1, 1, 61, 43.06, 3.97, 1], "hull": 23, "edges": [0, 44, 14, 16, 20, 22, 22, 24, 40, 42, 42, 44, 24, 26, 26, 46, 16, 18, 18, 20, 46, 18, 26, 28, 28, 48, 48, 16, 28, 30, 30, 50, 50, 14, 30, 32, 32, 34, 32, 52, 12, 14, 52, 12, 34, 54, 10, 12, 54, 10, 34, 36, 36, 56, 8, 10, 56, 8, 36, 38, 38, 40, 38, 58, 6, 8, 58, 6, 40, 60, 4, 6, 60, 4, 42, 62, 0, 2, 2, 4, 62, 2], "width": 82, "height": 146}}, "d4": {"d4": {"type": "mesh", "uvs": [0.63433, 0.27864, 0.66311, 0.46025, 0.68651, 0.60787, 0.71114, 0.76332, 0.73084, 0.88765, 0.74765, 0.99372, 0.73621, 0.99421, 0.12251, 0.99424, 0.00716, 0.99456, 0.00435, 0.92129, 0, 0.80761, 0, 0.72436, 0.13001, 0.72362, 0.34753, 0.65603, 0.51089, 0.60526, 0.53008, 0.47682, 0.54591, 0.37093, 0.14209, 0.80888, 0.42696, 0.77853, 0.59539, 0.76841, 0.15113, 0.91818, 0.44392, 0.90199, 0.65644, 0.8858, 0.56487, 0.60244, 0.592, 0.46077], "triangles": [24, 16, 0, 1, 24, 0, 15, 16, 24, 23, 15, 24, 14, 15, 23, 24, 1, 2, 23, 24, 2, 19, 23, 2, 19, 2, 3, 18, 13, 14, 19, 18, 14, 19, 14, 23, 10, 11, 12, 17, 12, 13, 10, 12, 17, 17, 13, 18, 22, 19, 3, 22, 3, 4, 21, 18, 19, 21, 19, 22, 20, 17, 18, 20, 18, 21, 9, 10, 17, 9, 17, 20, 6, 4, 5, 7, 9, 20, 7, 20, 21, 6, 7, 21, 22, 6, 21, 6, 22, 4, 8, 9, 7], "vertices": [1, 69, -50.86, -61.54, 1, 1, 69, -55.27, -30.33, 1, 1, 69, -58.86, -4.96, 1, 1, 69, -62.64, 21.76, 1, 1, 69, -65.67, 43.13, 1, 1, 69, -68.25, 61.36, 1, 1, 69, -64.85, 60.98, 1, 1, 69, 116.96, 36.12, 1, 1, 69, 151.14, 31.5, 1, 1, 69, 150.31, 19.27, 1, 1, 69, 149.03, 0.28, 1, 1, 69, 147.14, -13.49, 1, 1, 69, 108.61, -8.35, 1, 1, 69, 42.65, -10.72, 1, 1, 69, -6.9, -12.5, 1, 1, 69, -15.49, -32.98, 1, 1, 69, -22.57, -49.86, 1, 1, 69, 106.96, 6.25, 1, 1, 69, 21.89, 12.77, 1, 1, 69, -28.24, 17.91, 1, 1, 69, 106.76, 24.7, 1, 1, 69, 19.66, 33.88, 1, 1, 69, -43.67, 39.81, 1, 1, 69, -22.95, -10.78, 1, 1, 69, -34.2, -33.12, 1], "hull": 17, "edges": [14, 16, 20, 22, 22, 24, 12, 14, 10, 12, 32, 0, 20, 34, 34, 36, 36, 38, 38, 6, 16, 18, 18, 20, 18, 40, 40, 42, 42, 44, 6, 8, 8, 10, 44, 8, 28, 46, 4, 6, 46, 4, 28, 30, 30, 32, 30, 48, 0, 2, 2, 4, 48, 2, 24, 26, 26, 28], "width": 299, "height": 167}}, "d5": {"d5": {"type": "mesh", "uvs": [0.61326, 0, 0.79939, 0.07343, 0.97517, 0.40485, 0.95417, 0.48739, 0.93437, 0.8667, 0.79083, 0.88981, 0.6745, 0.90855, 0.56488, 0.9262, 0.48439, 0.93916, 0.38356, 0.9554, 0.25975, 0.97534, 0.1585, 0.99165, 0.10662, 1, 0.05879, 1, 0, 0.90129, 0, 0.79417, 0.04584, 0.66968, 0.10329, 0.5137, 0.18539, 0.42197, 0.31437, 0.27785, 0.43882, 0.13879, 0.56304, 0, 0.83227, 0.46881, 0.67759, 0.36098, 0.59166, 0.36513, 0.47708, 0.39416, 0.3415, 0.42527, 0.23839, 0.60982, 0.12954, 0.72594, 0.08753, 0.86073], "triangles": [11, 12, 29, 12, 13, 29, 13, 14, 29, 10, 11, 28, 27, 9, 10, 8, 26, 25, 29, 28, 11, 27, 10, 28, 9, 27, 26, 14, 15, 29, 28, 29, 16, 29, 15, 16, 27, 28, 17, 28, 16, 17, 17, 18, 27, 27, 18, 26, 18, 19, 26, 26, 19, 25, 7, 25, 24, 23, 22, 6, 24, 23, 6, 3, 22, 2, 2, 22, 1, 22, 23, 1, 24, 25, 20, 25, 19, 20, 0, 24, 21, 24, 0, 23, 21, 24, 20, 23, 0, 1, 7, 8, 25, 8, 9, 26, 6, 7, 24, 5, 6, 22, 5, 22, 4, 4, 22, 3], "vertices": [1, 71, 43.33, -64.46, 1, 1, 71, 9.8, -74.66, 1, 2, 70, 57.7, -74.88, 0.20931, 71, -47.51, -50.11, 0.79069, 2, 70, 45.37, -68.33, 0.32481, 71, -52.61, -37.11, 0.67519, 2, 70, -14.22, -51.5, 0.90435, 71, -86.9, 14.45, 0.09565, 2, 70, -12.39, -25.88, 0.95842, 71, -68.83, 32.71, 0.04158, 3, 70, -10.9, -5.12, 0.99688, 71, -54.19, 47.51, 0.00294, 72, -44.65, 84.38, 0.00018, 2, 70, -9.5, 14.45, 0.94085, 72, -25.1, 85.96, 0.05915, 2, 70, -8.47, 28.81, 0.8263, 72, -10.74, 87.12, 0.1737, 2, 70, -7.19, 46.81, 0.66592, 72, 7.24, 88.57, 0.33408, 2, 70, -5.6, 68.91, 0.51088, 72, 29.33, 90.36, 0.48912, 2, 70, -4.31, 86.98, 0.43521, 72, 47.38, 91.82, 0.56479, 2, 70, -3.65, 96.24, 0.41429, 72, 56.64, 92.57, 0.58571, 2, 70, -1.81, 104.5, 0.40482, 72, 65.08, 92, 0.59518, 2, 70, 16.15, 111.17, 0.38277, 72, 74.4, 75.26, 0.61723, 2, 70, 33.2, 107.38, 0.35598, 72, 73.24, 57.83, 0.64402, 2, 70, 51.24, 95.06, 0.29495, 72, 63.79, 38.13, 0.70505, 2, 70, 73.86, 79.62, 0.16341, 72, 51.95, 13.44, 0.83659, 2, 70, 85.3, 62.19, 0.05171, 72, 36.46, -0.52, 0.94829, 2, 71, 58.48, 3.51, 0.27901, 72, 12.12, -22.44, 0.72099, 2, 71, 54.46, -27.84, 0.91664, 72, -11.37, -43.59, 0.08336, 2, 71, 50.45, -59.13, 0.99996, 72, -34.81, -64.7, 4e-05, 2, 70, 53.01, -47.93, 0.28508, 71, -33.53, -26.59, 0.71492, 2, 70, 76.11, -25.01, 0.01982, 71, -1.08, -24.22, 0.98018, 1, 71, 10.68, -14.56, 1, 3, 70, 78.53, 10.8, 0.00476, 71, 24.07, 1.4, 0.96511, 72, -15.36, -1.6, 0.03012, 2, 70, 78.78, 35.33, 0.00988, 72, 8.92, 1.86, 0.99012, 2, 70, 53.38, 59.67, 0.26791, 72, 29.14, 30.66, 0.73209, 2, 70, 39.08, 82.59, 0.33371, 72, 49.62, 48.26, 0.66629, 2, 70, 19.25, 94.61, 0.387, 72, 58.5, 69.69, 0.613], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 24, 26, 26, 28, 28, 30, 2, 44, 8, 10, 44, 10, 0, 46, 10, 12, 46, 12, 42, 48, 12, 14, 48, 14, 40, 42, 40, 50, 14, 16, 50, 16, 38, 40, 38, 52, 16, 18, 52, 18, 34, 36, 36, 38, 36, 54, 18, 20, 54, 20, 34, 56, 20, 22, 22, 24, 56, 22, 30, 32, 32, 34, 32, 58, 58, 26], "width": 177, "height": 163}}, "f1": {"f1": {"type": "mesh", "uvs": [0.35586, 0.14465, 0.57255, 0.15712, 1, 0.24198, 1, 0.26352, 0.96516, 0.30976, 0.92983, 0.35665, 0.89249, 0.40619, 0.86644, 0.44077, 0.79518, 0.48559, 0.70266, 0.54377, 0.56403, 0.63096, 0.459, 0.69702, 0.36778, 0.75439, 0.27992, 0.80965, 0.36597, 0.84098, 0.48735, 0.88518, 0.5645, 0.92744, 0.42049, 0.96235, 0.34054, 0.98173, 0.26519, 1, 0.14652, 1, 0.00669, 0.97727, 0.01991, 0.9203, 0.03016, 0.87612, 0.03918, 0.83719, 0.04846, 0.79719, 0.06067, 0.74454, 0.07534, 0.68131, 0.08853, 0.62445, 0.10005, 0.57477, 0.10563, 0.52693, 0.1127, 0.46637, 0.09562, 0.39237, 0.07873, 0.31918, 0.0646, 0.258, 0.04298, 0.16433, 0.02533, 0.08784, 0.006, 0.00411, 0.07048, 0.00058, 0.18778, 0.10979, 0.24458, 0.19128, 0.53594, 0.22538, 0.30137, 0.25615, 0.59027, 0.27777, 0.35075, 0.3127, 0.60755, 0.33182, 0.81497, 0.35012, 0.42236, 0.36721, 0.60261, 0.37968, 0.75571, 0.38883, 0.46187, 0.42542, 0.58039, 0.42542, 0.71373, 0.42625, 0.4594, 0.4828, 0.60508, 0.47781, 0.39767, 0.53852, 0.56311, 0.53603, 0.33347, 0.60573, 0.44211, 0.60573, 0.32606, 0.67475, 0.24704, 0.7362, 0.18038, 0.79109, 0.20507, 0.83849, 0.26433, 0.87508, 0.31124, 0.91998, 0.28161, 0.95408, 0.06926, 0.95741, 0.15075, 0.97653, 0.22482, 0.97404], "triangles": [19, 20, 68, 20, 67, 68, 19, 68, 18, 20, 21, 67, 67, 21, 66, 68, 65, 18, 18, 65, 17, 21, 22, 66, 68, 67, 65, 65, 67, 66, 65, 64, 17, 17, 64, 16, 65, 66, 64, 64, 66, 22, 64, 15, 16, 22, 63, 64, 22, 23, 63, 64, 63, 15, 63, 14, 15, 23, 62, 63, 23, 24, 62, 63, 62, 14, 62, 13, 14, 24, 25, 62, 25, 61, 62, 62, 61, 13, 13, 61, 12, 25, 26, 61, 61, 60, 12, 61, 26, 60, 26, 27, 60, 12, 60, 59, 11, 12, 59, 60, 27, 59, 10, 11, 59, 59, 57, 58, 10, 59, 58, 27, 28, 59, 59, 28, 57, 28, 29, 57, 57, 29, 55, 58, 56, 10, 10, 56, 9, 58, 57, 55, 58, 55, 56, 29, 30, 55, 56, 54, 9, 9, 54, 8, 55, 53, 56, 55, 30, 53, 56, 53, 54, 30, 31, 53, 53, 51, 54, 53, 50, 51, 53, 31, 50, 31, 32, 50, 32, 47, 50, 32, 44, 47, 32, 33, 44, 54, 52, 8, 8, 52, 7, 54, 51, 52, 7, 52, 6, 6, 52, 49, 49, 52, 48, 52, 51, 48, 51, 50, 48, 50, 47, 48, 49, 46, 6, 6, 46, 5, 46, 49, 45, 49, 48, 45, 48, 47, 45, 47, 44, 45, 5, 46, 4, 4, 46, 43, 44, 43, 45, 46, 45, 43, 33, 42, 44, 33, 34, 42, 44, 42, 43, 4, 43, 3, 42, 41, 43, 43, 41, 3, 41, 2, 3, 34, 40, 42, 34, 35, 40, 42, 40, 41, 41, 1, 2, 40, 0, 41, 41, 0, 1, 35, 39, 40, 40, 39, 0, 35, 36, 39, 36, 38, 39, 0, 39, 38, 36, 37, 38], "vertices": [2, 10, -39.95, -56.61, 0.8966, 11, -97.53, -87.16, 0.1034, 2, 10, -39.03, -28.05, 0.94619, 11, -108.33, -60.7, 0.05381, 1, 10, -14.19, 31.48, 1, 1, 10, -5.95, 32.62, 1, 1, 10, 12.35, 30.57, 1, 1, 10, 30.9, 28.5, 1, 1, 10, 50.51, 26.31, 1, 1, 10, 64.2, 24.78, 1, 2, 10, 82.6, 17.97, 0.92061, 11, -15.97, 30.86, 0.07939, 3, 10, 106.5, 9.13, 0.2632, 11, 9.46, 32.51, 0.73446, 12, -29.69, 48.87, 0.00234, 3, 10, 142.3, -4.12, 0.00246, 11, 47.55, 34.99, 0.49735, 12, 6.34, 36.23, 0.50019, 3, 11, 76.41, 36.87, 0.03757, 12, 33.63, 26.65, 0.95102, 13, -16.42, 32.34, 0.01141, 2, 12, 57.33, 18.33, 0.32144, 13, 6.16, 21.33, 0.67856, 3, 12, 80.16, 10.32, 0.00018, 13, 27.91, 10.72, 0.73146, 14, -5, 12.35, 0.26836, 1, 14, 11, 16.3, 1, 1, 14, 33.56, 21.86, 1, 1, 14, 52.69, 22.76, 1, 1, 14, 55.43, -0.15, 1, 1, 14, 56.96, -12.86, 1, 1, 14, 58.4, -24.85, 1, 1, 14, 50.94, -38.35, 1, 1, 14, 34.47, -50.02, 1, 2, 13, 71.87, -21.44, 0.01739, 14, 16.05, -37.89, 0.98261, 2, 13, 54.78, -20.75, 0.15913, 14, 1.76, -28.48, 0.84087, 2, 13, 39.72, -20.15, 0.5444, 14, -10.82, -20.19, 0.4556, 2, 13, 24.25, -19.53, 0.94542, 14, -23.75, -11.67, 0.05458, 2, 12, 59.7, -21.7, 0.0217, 13, 3.87, -18.71, 0.9783, 2, 12, 35.29, -23.56, 0.64395, 13, -20.59, -17.74, 0.35605, 3, 11, 78.08, -18.82, 0.00927, 12, 13.34, -25.24, 0.95479, 13, -42.58, -16.85, 0.03594, 3, 11, 61, -27.68, 0.24716, 12, -5.84, -26.7, 0.75264, 13, -61.8, -16.09, 0.0002, 3, 10, 110.68, -68.65, 0.00047, 11, 44.94, -36.82, 0.66825, 12, -24.2, -28.82, 0.33128, 3, 10, 87.39, -70.93, 0.04781, 11, 24.61, -48.39, 0.89135, 12, -47.44, -31.5, 0.06084, 3, 10, 59.4, -77.04, 0.27112, 11, 1.53, -65.36, 0.72766, 12, -75.32, -38.08, 0.00122, 2, 10, 31.72, -83.08, 0.54609, 11, -21.3, -82.16, 0.45391, 2, 10, 8.57, -88.13, 0.72246, 11, -40.38, -96.19, 0.27754, 2, 10, -26.86, -95.86, 0.86842, 11, -69.59, -117.68, 0.13158, 2, 10, -55.79, -102.18, 0.91376, 11, -93.45, -135.23, 0.08624, 2, 10, -87.46, -109.09, 0.92882, 11, -119.56, -154.43, 0.07118, 2, 10, -89.96, -100.97, 0.9287, 11, -125.15, -148.04, 0.0713, 2, 10, -50.29, -80.1, 0.90298, 11, -97.41, -112.82, 0.09702, 2, 10, -20.14, -68.48, 0.8544, 11, -74.61, -89.93, 0.1456, 2, 10, -12.28, -29.16, 0.92905, 11, -83.44, -50.82, 0.07095, 2, 10, 3.66, -57.74, 0.77498, 11, -57.25, -70.43, 0.22502, 2, 10, 6.78, -19.4, 0.93207, 11, -70, -34.14, 0.06793, 2, 10, 24.4, -48.4, 0.66607, 11, -42.11, -53.45, 0.33393, 2, 10, 27.15, -14.32, 0.90816, 11, -53.47, -21.21, 0.09184, 1, 10, 30.45, 13.36, 1, 2, 10, 43.97, -36.29, 0.54683, 11, -29.16, -34.43, 0.45317, 2, 10, 45.53, -12.42, 0.84076, 11, -37.45, -11.99, 0.15924, 1, 10, 46.31, 7.77, 1, 2, 10, 65.53, -28.13, 0.27676, 11, -12.79, -18.2, 0.72324, 2, 10, 63.42, -12.87, 0.61161, 11, -20.93, -5.12, 0.38839, 1, 10, 61.36, 4.35, 1, 1, 11, 6.18, -6.77, 1, 2, 10, 83.01, -6.92, 0.30468, 11, -5.46, 8.29, 0.69532, 2, 11, 28.68, -2.22, 0.99663, 12, -25.6, 9.38, 0.00337, 3, 10, 106.02, -9.25, 0.07615, 11, 16.5, 15.53, 0.92168, 12, -29.86, 30.49, 0.00217, 2, 11, 55.12, 4.4, 0.04485, 12, 1.31, 5.12, 0.95515, 3, 10, 134.82, -21.15, 0.00063, 11, 47.66, 16.39, 0.50965, 12, -0.86, 19.07, 0.48972, 2, 11, 78.25, 17.66, 0.00736, 12, 27.79, 8.25, 0.99264, 2, 12, 52.8, 1.75, 0.06004, 13, -0.26, 5.37, 0.93996, 1, 13, 21.24, -2.48, 1, 2, 13, 39.4, 1.42, 0.1983, 14, 0.04, -1.55, 0.8017, 2, 13, 53.22, 9.65, 0.00027, 14, 16.13, -1.63, 0.99973, 1, 14, 34.25, -4.67, 1, 1, 14, 43.91, -14.41, 1, 2, 13, 85.94, -14.48, 8e-05, 14, 31.69, -39.2, 0.99992, 1, 14, 43.27, -33.49, 1, 1, 14, 47.09, -24.6, 1], "hull": 39, "edges": [0, 76, 0, 2, 2, 4, 4, 6, 30, 32, 38, 40, 40, 42, 74, 76, 72, 74, 72, 78, 78, 0, 70, 72, 70, 80, 80, 82, 82, 6, 68, 70, 68, 84, 84, 86, 6, 8, 86, 8, 66, 68, 66, 88, 88, 90, 90, 92, 8, 10, 92, 10, 62, 64, 64, 66, 64, 94, 94, 96, 96, 98, 10, 12, 12, 14, 98, 12, 62, 100, 100, 102, 102, 104, 104, 14, 58, 60, 60, 62, 60, 106, 106, 108, 14, 16, 108, 16, 58, 110, 110, 112, 16, 18, 112, 18, 56, 58, 56, 114, 114, 116, 18, 20, 116, 20, 54, 56, 54, 118, 20, 22, 118, 22, 52, 54, 52, 120, 22, 24, 24, 26, 120, 24, 50, 52, 50, 122, 122, 26, 48, 50, 48, 124, 26, 28, 28, 30, 124, 28, 46, 48, 46, 126, 126, 30, 42, 44, 44, 46, 44, 128, 128, 32, 32, 34, 34, 130, 130, 132, 132, 44, 132, 134, 134, 136, 136, 130, 130, 128, 128, 126, 126, 124, 124, 50, 34, 36, 36, 38], "width": 130, "height": 386}}, "sss": {"sss": {"type": "mesh", "uvs": [0.03844, 0, 0.13221, 0, 0.24302, 0.01598, 0.34055, 0.04362, 0.44608, 0.07352, 0.49787, 0.07762, 0.53344, 0.08378, 0.55588, 0.09721, 0.59921, 0.09117, 0.65623, 0.11858, 0.74256, 0.14969, 0.85065, 0.18865, 0.96264, 0.22901, 0.92998, 0.2994, 0.86087, 0.29966, 0.76907, 0.28665, 0.68726, 0.26947, 0.62215, 0.25581, 0.67335, 0.3487, 0.72724, 0.44648, 0.73571, 0.46185, 0.78345, 0.54846, 0.83218, 0.60553, 0.89324, 0.67703, 0.82885, 0.71373, 0.76678, 0.64974, 0.71242, 0.59372, 0.67033, 0.55034, 0.61102, 0.4892, 0.57099, 0.44794, 0.57846, 0.52295, 0.58306, 0.56919, 0.59155, 0.65449, 0.61114, 0.7382, 0.62504, 0.7976, 0.62573, 0.88565, 0.59275, 0.88653, 0.55529, 0.82209, 0.50974, 0.87441, 0.42766, 0.74042, 0.3874, 0.66311, 0.33169, 0.5558, 0.235, 0.47287, 0.12522, 0.39772, 0.0264, 0.24487, 0, 0.16957, 0, 0.08217, 0.01107, 0.03302], "triangles": [4, 29, 42, 29, 41, 42, 38, 39, 37, 39, 40, 32, 30, 40, 41, 35, 36, 34, 36, 37, 34, 37, 33, 34, 37, 39, 33, 39, 32, 33, 40, 31, 32, 40, 30, 31, 30, 41, 29, 19, 28, 18, 28, 29, 18, 29, 17, 18, 24, 22, 23, 24, 25, 22, 25, 21, 22, 25, 26, 21, 21, 26, 20, 26, 27, 20, 27, 19, 20, 27, 28, 19, 13, 14, 11, 14, 15, 11, 13, 11, 12, 16, 10, 15, 15, 10, 11, 17, 29, 5, 7, 5, 6, 4, 5, 29, 17, 7, 8, 7, 17, 5, 10, 16, 9, 16, 17, 9, 17, 8, 9, 42, 3, 4, 3, 43, 2, 3, 42, 43, 2, 43, 44, 2, 44, 1, 0, 1, 45, 1, 44, 45, 47, 0, 46, 0, 45, 46], "vertices": [1, 24, -11.12, 16.78, 1, 1, 24, 2.72, 20.16, 1, 2, 24, 19.58, 22.12, 0.98619, 25, -51.7, 7.15, 0.01381, 2, 24, 34.84, 22.11, 0.89668, 25, -36.44, 7.27, 0.10332, 2, 24, 51.35, 22.11, 0.5502, 25, -19.93, 7.41, 0.4498, 2, 24, 59.13, 23.45, 0.26901, 25, -12.16, 8.82, 0.73099, 2, 24, 64.57, 23.95, 0.1147, 25, -6.72, 9.37, 0.8853, 2, 24, 68.31, 23.05, 0.03761, 25, -2.99, 8.5, 0.96239, 2, 24, 74.52, 25.37, 1e-05, 25, 3.21, 10.88, 0.99999, 2, 25, 12.49, 9.53, 0.99751, 26, -10.6, 15.19, 0.00249, 2, 25, 26.21, 8.8, 0.38512, 26, 1.78, 9.24, 0.61488, 1, 26, 17.29, 1.78, 1, 1, 26, 33.35, -5.94, 1, 1, 26, 27.09, -14.34, 1, 1, 26, 16.7, -12.83, 1, 1, 26, 3.14, -9.1, 1, 3, 25, 21.91, -8.48, 0.57016, 26, -8.83, -5.05, 0.42049, 27, 12.78, 15.07, 0.00935, 3, 25, 11.88, -9.17, 0.58905, 26, -18.36, -1.83, 0.00113, 27, 5.05, 8.64, 0.40982, 3, 25, 22.41, -19.05, 0.01131, 28, -13.2, 8.16, 0.01675, 27, 19.38, 6.78, 0.97194, 2, 28, 1.75, 5.36, 0.8625, 27, 34.46, 4.83, 0.1375, 2, 28, 4.1, 4.92, 0.94413, 27, 36.83, 4.53, 0.05587, 1, 28, 17.34, 2.45, 1, 1, 28, 27.86, 2.74, 1, 1, 28, 41.04, 3.09, 1, 1, 28, 37.84, -7.33, 1, 1, 28, 25.28, -8.48, 1, 1, 28, 14.27, -9.48, 1, 1, 28, 5.75, -10.25, 1, 3, 28, -6.26, -11.35, 0.46128, 27, 27.4, -12.3, 0.47873, 29, 16.29, 12.23, 0.05999, 3, 28, -14.37, -12.09, 0.02512, 27, 19.35, -13.49, 0.49115, 29, 8.78, 9.11, 0.48373, 3, 27, 27.61, -18.93, 0.04762, 29, 18.11, 5.85, 0.92712, 30, -11.74, 5.79, 0.02526, 3, 27, 32.7, -22.29, 0.00473, 29, 23.87, 3.84, 0.71164, 30, -5.7, 4.94, 0.28363, 2, 29, 34.49, 0.13, 0.02096, 30, 5.44, 3.38, 0.97904, 1, 30, 16.8, 3.51, 1, 1, 30, 24.86, 3.59, 1, 1, 30, 36.05, 0.8, 1, 1, 30, 34.9, -4.09, 1, 2, 30, 25.3, -7.47, 0.79778, 32, 35.95, 5.56, 0.20222, 2, 30, 30.19, -15.9, 0.29924, 32, 40.23, -3.19, 0.70076, 1, 32, 19.62, -9.4, 1, 2, 32, 8.06, -11.97, 0.96085, 31, 27.78, -13.98, 0.03915, 2, 32, -7.96, -15.5, 0.12656, 31, 11.37, -14.15, 0.87344, 2, 24, 32.58, -36.32, 0.22039, 31, -5.45, -21.31, 0.77961, 2, 24, 14.04, -30.71, 0.68242, 31, -22.39, -30.7, 0.31758, 2, 24, -5.3, -14.82, 0.97942, 31, -47.27, -33.49, 0.02058, 2, 24, -11.54, -6.18, 0.9993, 31, -57.8, -31.95, 0.0007, 1, 24, -14.25, 4.94, 1, 1, 24, -14.14, 11.59, 1], "hull": 48, "edges": [0, 94, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 24, 26, 28, 30, 46, 48, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 86, 88, 88, 90, 90, 92, 92, 94, 16, 18, 86, 84, 84, 82, 82, 80, 80, 78, 64, 66, 66, 68, 62, 64, 58, 60, 60, 62, 56, 58, 54, 56, 52, 54, 48, 50, 50, 52, 42, 44, 44, 46, 40, 42, 38, 40, 34, 36, 36, 38, 30, 32, 32, 34, 26, 28, 18, 20, 20, 22, 22, 24, 4, 6, 6, 8], "width": 152, "height": 131}}, "f2": {"f2": {"type": "mesh", "uvs": [0.54794, 0.0913, 0.66173, 0.20962, 0.76341, 0.31535, 0.87058, 0.42678, 0.9551, 0.51467, 1, 0.56135, 1, 0.65912, 0.94031, 0.82352, 0.81575, 0.88947, 0.73549, 0.93197, 0.61631, 0.99508, 0.51451, 0.9087, 0.43021, 0.83717, 0.30985, 0.73504, 0.15805, 0.60624, 0.01166, 0.48203, 0.01078, 0.3556, 0.00999, 0.24074, 0.00926, 0.13522, 0.00856, 0.03455, 0.46323, 0.00322, 0.24229, 0.11449, 0.24229, 0.2162, 0.45354, 0.21243, 0.20565, 0.35934, 0.53545, 0.33862, 0.20134, 0.48365, 0.45138, 0.49495, 0.72298, 0.41773, 0.28109, 0.6155, 0.57425, 0.59666, 0.86525, 0.49872, 0.47725, 0.68895, 0.6346, 0.67012, 0.93853, 0.60796, 0.5764, 0.75111, 0.66909, 0.75864, 0.62598, 0.84905, 0.69496, 0.84152], "triangles": [31, 3, 4, 34, 31, 4, 34, 4, 5, 34, 5, 6, 33, 31, 34, 36, 33, 34, 35, 33, 36, 36, 34, 6, 7, 36, 6, 38, 36, 7, 37, 35, 36, 37, 36, 38, 11, 12, 35, 8, 38, 7, 37, 11, 35, 9, 38, 8, 10, 11, 37, 37, 38, 9, 10, 37, 9, 28, 25, 2, 28, 2, 3, 27, 25, 28, 31, 28, 3, 30, 27, 28, 30, 28, 31, 29, 26, 27, 29, 27, 30, 33, 30, 31, 32, 29, 30, 32, 30, 33, 13, 29, 32, 14, 29, 13, 35, 32, 33, 12, 13, 32, 12, 32, 35, 25, 23, 1, 25, 1, 2, 24, 16, 17, 24, 23, 25, 15, 16, 24, 26, 15, 24, 27, 24, 25, 26, 24, 27, 14, 15, 26, 14, 26, 29, 21, 19, 20, 21, 20, 0, 18, 19, 21, 23, 21, 0, 23, 0, 1, 22, 18, 21, 22, 21, 23, 17, 18, 22, 22, 24, 17, 24, 22, 23], "vertices": [3, 96, -5.84, 28.77, 0.65738, 97, 12.7, 40.76, 0.28782, 98, -21.6, 39.08, 0.0548, 4, 96, 4.1, 41.2, 0.32613, 97, 27.68, 35.39, 0.41759, 98, -5.78, 37.35, 0.24977, 99, -49.31, 10.51, 0.0065, 4, 96, 12.98, 52.31, 0.12292, 97, 41.08, 30.6, 0.30523, 98, 8.36, 35.81, 0.49515, 99, -37.07, 17.74, 0.07671, 4, 96, 22.34, 64.02, 0.02783, 97, 55.19, 25.55, 0.12192, 98, 23.27, 34.18, 0.52783, 99, -24.16, 25.37, 0.32242, 4, 96, 29.73, 73.25, 0.00426, 97, 66.32, 21.56, 0.0405, 98, 35.02, 32.9, 0.35936, 99, -13.98, 31.39, 0.59588, 4, 96, 33.65, 78.16, 0.001, 97, 72.24, 19.44, 0.02277, 98, 41.27, 32.21, 0.28204, 99, -8.57, 34.58, 0.69419, 3, 97, 77.31, 10.74, 0.00768, 98, 48.23, 24.94, 0.16904, 99, 1.36, 32.93, 0.82328, 2, 98, 56.05, 8.99, 0.01353, 99, 17.18, 24.84, 0.98647, 1, 99, 22.04, 12.67, 1, 1, 99, 25.17, 4.82, 1, 2, 98, 47.19, -23.94, 0.00315, 99, 29.82, -6.82, 0.99685, 2, 98, 34.42, -23.84, 0.13364, 99, 19.53, -14.4, 0.86636, 3, 97, 42.26, -30.94, 0.00027, 98, 23.85, -23.76, 0.47261, 99, 11.02, -20.67, 0.52712, 3, 97, 27.6, -27.31, 0.08743, 98, 8.75, -23.65, 0.80934, 99, -1.14, -29.63, 0.10322, 3, 97, 9.11, -22.73, 0.65803, 98, -10.3, -23.5, 0.34161, 99, -16.47, -40.92, 0.00037, 3, 96, 43.08, -10.67, 0.03594, 97, -8.71, -18.32, 0.92791, 98, -28.66, -23.37, 0.03615, 3, 96, 30.33, -13.3, 0.3881, 97, -15.34, -7.11, 0.61083, 98, -37.72, -14.01, 0.00107, 2, 96, 18.74, -15.69, 0.89893, 97, -21.37, 3.07, 0.10107, 2, 96, 8.1, -17.89, 0.99976, 97, -26.9, 12.43, 0.00024, 1, 96, -2.06, -19.98, 1, 3, 96, -13.25, 19.51, 0.82599, 97, 1.54, 44.76, 0.16291, 98, -33.38, 40.36, 0.0111, 2, 96, 1.89, 2.26, 0.98972, 97, -9.86, 24.84, 0.01028, 2, 96, 12.16, 4.31, 0.93363, 97, -4.58, 15.79, 0.06637, 3, 96, 8.06, 22.88, 0.54931, 97, 11.65, 25.7, 0.39445, 98, -19.12, 24.19, 0.05624, 2, 96, 27.27, 3.97, 0.0791, 97, 0, 1.39, 0.9209, 4, 96, 19.35, 32.66, 0.19852, 97, 24.56, 18.19, 0.52752, 98, -4.8, 19.89, 0.27128, 99, -38.07, -2.89, 0.00267, 2, 97, 6.12, -9.86, 0.88987, 98, -16.21, -11.69, 0.11013, 3, 96, 36.63, 28.4, 0.0054, 97, 26.14, 0.47, 0.18151, 98, 0.86, 3.03, 0.81309, 4, 96, 24.03, 50.81, 0.06322, 97, 43.25, 19.66, 0.21808, 98, 13.02, 25.67, 0.59296, 99, -27.26, 12.42, 0.12575, 3, 97, 19.16, -17.98, 0.35009, 98, -1.63, -16.54, 0.64057, 99, -13.71, -30.16, 0.00934, 4, 96, 44.73, 41.3, 0.00099, 97, 40.97, -3.01, 0.00797, 98, 16.09, 3.1, 0.98953, 99, -11.28, -3.81, 0.00151, 4, 96, 29.7, 65, 0.01244, 97, 58.51, 18.9, 0.07295, 98, 28.04, 28.49, 0.46442, 99, -16.93, 23.68, 0.45019, 3, 97, 38.22, -15.62, 0.00067, 98, 16.35, -9.8, 0.84604, 99, -3.34, -13.99, 0.15329, 4, 96, 51.09, 48.11, 1e-05, 97, 49.48, -6.81, 0.00079, 98, 25.25, 1.39, 0.53787, 99, -2.93, 0.3, 0.46132, 4, 96, 39.44, 73.67, 0.00053, 97, 69.88, 12.51, 0.01677, 98, 40.59, 24.92, 0.24313, 99, -4.75, 28.34, 0.73957, 2, 98, 27.23, -8.26, 0.28502, 99, 4.44, -6.24, 0.71498, 1, 99, 6.58, 1.86, 1, 2, 98, 37.42, -12.47, 0.02745, 99, 15.12, -3.49, 0.97255, 1, 99, 15.38, 2.76, 1], "hull": 21, "edges": [10, 12, 12, 14, 38, 40, 36, 38, 36, 42, 0, 40, 42, 0, 34, 36, 34, 44, 44, 46, 0, 2, 46, 2, 30, 32, 32, 34, 32, 48, 48, 50, 2, 4, 50, 4, 30, 52, 52, 54, 54, 56, 4, 6, 56, 6, 28, 30, 28, 58, 58, 60, 60, 62, 6, 8, 8, 10, 62, 8, 26, 28, 26, 64, 64, 66, 66, 68, 68, 10, 24, 26, 24, 70, 70, 72, 72, 12, 20, 22, 22, 24, 22, 74, 74, 76, 76, 14, 14, 16, 16, 18, 18, 20], "width": 90, "height": 103}}, "d1": {"d1": {"type": "mesh", "uvs": [0.82031, 0, 0.92115, 0.16651, 0.99851, 0.29427, 0.98712, 0.58605, 0.98054, 0.75431, 0.88618, 0.79757, 0.7875, 0.8428, 0.68522, 0.88968, 0.52766, 0.9619, 0.44453, 1, 0.39217, 1, 0.33447, 0.88564, 0.25954, 0.73717, 0.17521, 0.57007, 0.0957, 0.41251, 0.00738, 0.2375, 0, 0.1524, 0.11207, 0.26295, 0.19043, 0.34025, 0.28686, 0.43536, 0.36672, 0.39302, 0.44444, 0.35181, 0.5369, 0.30279, 0.59291, 0.22027, 0.66272, 0.11742, 0.74242, 0, 0.12856, 0.35476, 0.20874, 0.44987, 0.30149, 0.58486, 0.38482, 0.56645, 0.48543, 0.57259, 0.56876, 0.56031, 0.64736, 0.47748, 0.76999, 0.39158, 0.85803, 0.33329, 0.93506, 0.38544], "triangles": [34, 0, 1, 35, 34, 1, 25, 0, 34, 2, 35, 1, 33, 25, 34, 3, 35, 2, 3, 5, 35, 5, 34, 35, 4, 5, 3, 33, 34, 5, 24, 25, 33, 32, 23, 24, 32, 24, 33, 22, 23, 32, 6, 33, 5, 32, 33, 6, 7, 32, 6, 31, 32, 7, 31, 22, 32, 30, 21, 22, 22, 31, 30, 8, 30, 31, 8, 31, 7, 11, 30, 9, 8, 9, 30, 17, 15, 16, 26, 17, 18, 14, 15, 17, 14, 17, 26, 27, 18, 19, 29, 20, 21, 30, 29, 21, 28, 19, 20, 26, 27, 14, 27, 26, 18, 13, 14, 27, 29, 28, 20, 27, 28, 13, 28, 27, 19, 12, 13, 28, 11, 28, 29, 12, 28, 11, 11, 29, 30, 9, 10, 11], "vertices": [2, 57, 60.86, 24.86, 0.51882, 58, 1.68, 35.2, 0.48118, 2, 57, 70.61, 0.16, 0.05159, 58, 22.42, 18.62, 0.94841, 1, 58, 38.33, 5.9, 1, 2, 57, 61.86, -44.19, 0.04611, 58, 36.87, -24.21, 0.95389, 2, 57, 52.51, -58.83, 0.10599, 58, 36.03, -41.57, 0.89401, 2, 57, 33.69, -53.79, 0.26084, 58, 17.2, -46.55, 0.73916, 3, 56, 53.88, -43.47, 0.01456, 57, 14.01, -48.52, 0.56573, 58, -2.5, -51.76, 0.4197, 4, 55, 84.53, -29.19, 0.00161, 56, 32.76, -43.79, 0.16061, 57, -6.39, -43.05, 0.6875, 58, -22.92, -57.16, 0.15028, 4, 55, 54.35, -41.35, 0.18229, 56, 0.24, -44.29, 0.52588, 57, -37.81, -34.63, 0.28336, 58, -54.37, -65.47, 0.00847, 4, 55, 38.44, -47.77, 0.40889, 56, -16.93, -44.55, 0.46564, 57, -54.39, -30.19, 0.12531, 58, -70.96, -69.86, 0.00016, 3, 55, 28.03, -49.37, 0.51173, 56, -27.21, -42.3, 0.40477, 57, -63.67, -25.22, 0.0835, 3, 55, 14.78, -39.5, 0.71513, 56, -36.02, -28.32, 0.25071, 57, -68.32, -9.35, 0.03416, 3, 55, -2.43, -26.67, 0.96013, 56, -47.47, -10.16, 0.03857, 57, -74.36, 11.24, 0.00129, 1, 55, -21.8, -12.24, 1, 1, 55, -40.06, 1.37, 1, 1, 55, -60.35, 16.49, 1, 1, 55, -63.15, 24.93, 1, 1, 55, -39.16, 17.1, 1, 1, 55, -22.38, 11.63, 1, 1, 55, -1.73, 4.89, 1, 2, 55, 13.47, 11.64, 0.9936, 56, -18.85, 19.86, 0.0064, 3, 55, 28.27, 18.21, 0.61918, 56, -2.68, 20.67, 0.3806, 57, -22.84, 28.65, 0.00023, 3, 55, 45.87, 26.03, 0.05924, 56, 16.56, 21.63, 0.71929, 57, -4.08, 24.31, 0.22148, 3, 55, 55.7, 36.14, 0.0013, 56, 29.37, 27.53, 0.31698, 57, 9.86, 26.48, 0.68172, 3, 56, 45.34, 34.88, 0.05758, 57, 27.23, 29.17, 0.91629, 58, -29.65, 22.23, 0.02613, 3, 56, 63.58, 43.27, 0.00172, 57, 47.07, 32.26, 0.74651, 58, -13.97, 34.76, 0.25177, 1, 55, -34.44, 8.26, 1, 1, 55, -17.02, 1.03, 1, 2, 55, 3.52, -9.88, 0.99334, 56, -35.88, 3.36, 0.00666, 3, 55, 19.78, -5.46, 0.98306, 56, -19.11, 1.64, 0.01579, 57, -43.86, 14.83, 0.00114, 3, 55, 39.87, -3.01, 0.03552, 56, 0.51, -3.3, 0.95267, 57, -26.33, 4.71, 0.01181, 3, 56, 17.14, -5.65, 0.83736, 57, -10.98, -2.09, 0.15998, 58, -47.26, -23.9, 0.00266, 2, 57, 6.98, -2.04, 0.99525, 58, -31.7, -14.93, 0.00475, 2, 57, 32.88, -5.9, 0.68729, 58, -7.31, -5.4, 0.31271, 2, 57, 51.31, -8.98, 0.00113, 58, 10.21, 1.09, 0.99887, 2, 57, 62.41, -21.03, 0.00026, 58, 25.84, -3.85, 0.99974], "hull": 26, "edges": [0, 50, 18, 20, 30, 32, 32, 34, 34, 52, 28, 30, 52, 28, 34, 36, 36, 38, 36, 54, 26, 28, 54, 26, 38, 56, 24, 26, 56, 24, 38, 40, 40, 58, 20, 22, 22, 24, 58, 22, 40, 42, 42, 44, 42, 60, 60, 18, 44, 62, 16, 18, 62, 16, 44, 46, 46, 64, 14, 16, 64, 14, 46, 48, 48, 50, 48, 66, 12, 14, 66, 12, 50, 68, 8, 10, 10, 12, 68, 10, 0, 2, 2, 4, 2, 70, 4, 6, 6, 8, 70, 6], "width": 201, "height": 103}}, "s1": {"s1": {"type": "mesh", "uvs": [0.91844, 0, 0.93071, 0.01913, 0.94793, 0.14126, 0.95845, 0.21592, 0.96481, 0.26103, 0.972, 0.31201, 0.97758, 0.35159, 0.98524, 0.40587, 0.99142, 0.44971, 0.99781, 0.49509, 1, 0.51059, 1, 0.53789, 1, 0.5963, 0.9749, 0.63128, 0.93379, 0.6886, 0.90067, 0.73476, 0.87054, 0.77676, 0.74627, 0.83138, 0.64208, 0.87718, 0.54929, 0.91796, 0.47176, 0.95204, 0.39733, 0.98476, 0.32175, 0.97641, 0.20445, 0.96346, 0.14694, 0.90772, 0.07396, 0.83697, 0.00521, 0.77034, 0.0529, 0.7377, 0.1045, 0.70238, 0.18913, 0.64446, 0.25927, 0.59645, 0.34368, 0.53868, 0.26934, 0.48683, 0.24114, 0.46717, 0.22189, 0.45375, 0.22316, 0.43257, 0.22512, 0.39984, 0.27568, 0.40188, 0.32311, 0.39185, 0.41971, 0.3714, 0.53626, 0.34674, 0.64079, 0.32462, 0.65666, 0.29347, 0.68074, 0.24622, 0.70005, 0.20832, 0.74331, 0.12342, 0.78881, 0.03414, 0.85825, 0, 0.83588, 0.14205, 0.79218, 0.21108, 0.83466, 0.21927, 0.91114, 0.2181, 0.73877, 0.25262, 0.85044, 0.25964, 0.73391, 0.30411, 0.86016, 0.31113, 0.71813, 0.33687, 0.87108, 0.34857, 0.63558, 0.37225, 0.86501, 0.39272, 0.56032, 0.40092, 0.74726, 0.42373, 0.88322, 0.44011, 0.51419, 0.42315, 0.72541, 0.45825, 0.89414, 0.48173, 0.2969, 0.44545, 0.35517, 0.4279, 0.52755, 0.43843, 0.43078, 0.43252, 0.39108, 0.40298, 0.2868, 0.46062, 0.45957, 0.46769, 0.40494, 0.50337, 0.65501, 0.48582, 0.83102, 0.5139, 0.56761, 0.52268, 0.74605, 0.54608, 0.48627, 0.56194, 0.68293, 0.59119, 0.4365, 0.61693, 0.64044, 0.64852, 0.34061, 0.67265, 0.55547, 0.70599, 0.25927, 0.73232, 0.4887, 0.75806, 0.18158, 0.78265, 0.40737, 0.8119, 0.11725, 0.81073, 0.36853, 0.85403, 0.30055, 0.8944, 0.2532, 0.92423], "triangles": [13, 77, 12, 14, 79, 13, 81, 79, 14, 83, 80, 81, 15, 81, 14, 83, 81, 15, 85, 82, 83, 84, 82, 85, 16, 83, 15, 85, 83, 16, 88, 26, 27, 86, 88, 27, 87, 84, 85, 86, 84, 87, 17, 85, 16, 87, 85, 17, 25, 26, 88, 89, 86, 87, 88, 86, 89, 18, 87, 17, 89, 87, 18, 90, 88, 89, 25, 88, 90, 24, 25, 90, 19, 89, 18, 90, 89, 19, 91, 24, 90, 20, 90, 19, 91, 90, 20, 23, 24, 91, 22, 91, 20, 23, 91, 22, 21, 22, 20, 47, 0, 1, 46, 47, 1, 48, 46, 1, 48, 45, 46, 1, 2, 48, 49, 44, 45, 48, 49, 45, 51, 48, 2, 51, 2, 3, 50, 49, 48, 51, 50, 48, 52, 44, 49, 43, 44, 52, 53, 50, 51, 52, 49, 50, 53, 52, 50, 51, 3, 4, 53, 51, 4, 52, 42, 43, 54, 52, 53, 54, 42, 52, 55, 53, 4, 54, 53, 55, 55, 4, 5, 41, 42, 54, 56, 41, 54, 57, 55, 5, 56, 54, 55, 57, 56, 55, 57, 5, 6, 58, 40, 41, 58, 41, 56, 59, 56, 57, 58, 56, 59, 60, 40, 58, 39, 40, 60, 70, 38, 39, 59, 57, 6, 7, 59, 6, 63, 39, 60, 70, 39, 63, 61, 58, 59, 60, 58, 61, 67, 38, 70, 37, 38, 67, 69, 70, 63, 67, 70, 69, 35, 36, 37, 64, 68, 63, 69, 63, 68, 62, 59, 7, 61, 59, 62, 66, 37, 67, 35, 37, 66, 62, 7, 8, 34, 35, 66, 64, 60, 61, 64, 63, 60, 71, 34, 66, 33, 34, 71, 72, 69, 68, 65, 62, 8, 64, 61, 62, 65, 64, 62, 74, 68, 64, 72, 68, 74, 32, 33, 71, 65, 8, 9, 72, 73, 67, 72, 67, 69, 71, 66, 67, 73, 71, 67, 32, 71, 73, 75, 64, 65, 74, 64, 75, 76, 72, 74, 73, 72, 76, 65, 9, 10, 10, 75, 65, 11, 75, 10, 31, 32, 73, 77, 74, 75, 76, 74, 77, 78, 73, 76, 31, 73, 78, 79, 76, 77, 78, 76, 79, 75, 11, 12, 77, 75, 12, 80, 31, 78, 30, 31, 80, 79, 77, 13, 81, 78, 79, 80, 78, 81, 82, 30, 80, 29, 30, 82, 82, 80, 83, 84, 29, 82, 28, 29, 84, 86, 28, 84, 27, 28, 86], "vertices": [1, 38, -15.33, 4.24, 1, 1, 38, -8.86, 8.55, 1, 1, 38, 35.66, 25.08, 1, 1, 38, 62.88, 35.19, 1, 2, 38, 79.33, 41.29, 0.99998, 39, -67.37, 152.45, 2e-05, 2, 38, 97.91, 48.19, 0.9972, 39, -51.01, 141.25, 0.0028, 2, 38, 112.34, 53.55, 0.98726, 39, -38.32, 132.55, 0.01274, 2, 38, 132.13, 60.9, 0.94927, 39, -20.9, 120.62, 0.05073, 2, 38, 148.11, 66.83, 0.88497, 39, -6.83, 110.99, 0.11503, 2, 38, 164.66, 72.98, 0.77734, 39, 7.73, 101.02, 0.22266, 2, 38, 170.31, 75.08, 0.73176, 39, 12.7, 97.61, 0.26824, 2, 38, 180.47, 78.08, 0.64164, 39, 21.02, 91.05, 0.35836, 2, 38, 202.2, 84.51, 0.43428, 39, 38.81, 77.01, 0.56572, 2, 38, 216.54, 83.86, 0.30598, 39, 46.55, 64.91, 0.69402, 2, 38, 240.05, 82.8, 0.10723, 39, 59.24, 45.1, 0.89277, 2, 38, 258.98, 81.94, 0.02263, 39, 69.46, 29.14, 0.97737, 2, 38, 276.21, 81.16, 0.00112, 39, 78.76, 14.62, 0.99888, 2, 38, 303.12, 64.89, 0.00593, 39, 81, -16.75, 0.99407, 2, 38, 325.69, 51.25, 0.04771, 39, 82.87, -43.06, 0.95229, 2, 38, 345.79, 39.1, 0.10068, 39, 84.54, -66.48, 0.89932, 2, 38, 362.58, 28.95, 0.13739, 39, 85.93, -86.05, 0.86261, 2, 38, 378.7, 19.21, 0.15801, 39, 87.27, -104.84, 0.84199, 2, 38, 379.61, 4.74, 0.17499, 39, 75.97, -113.93, 0.82501, 2, 38, 381.01, -17.72, 0.20955, 39, 58.44, -128.03, 0.79045, 2, 38, 363.32, -34.17, 0.26726, 39, 34.8, -123.07, 0.73274, 2, 38, 340.87, -55.05, 0.4017, 39, 4.8, -116.77, 0.5983, 2, 38, 319.73, -74.71, 0.5019, 39, -23.46, -110.84, 0.4981, 2, 38, 305.06, -69.75, 0.53879, 39, -27.88, -96, 0.46121, 2, 38, 289.18, -64.39, 0.61594, 39, -32.65, -79.93, 0.38406, 2, 38, 263.14, -55.59, 0.80225, 39, -40.49, -53.58, 0.19775, 2, 38, 241.55, -48.3, 0.95331, 39, -46.98, -31.75, 0.04669, 1, 38, 215.58, -39.52, 1, 1, 38, 200.24, -58.56, 1, 1, 38, 194.42, -65.78, 1, 1, 38, 190.44, -70.71, 1, 1, 38, 182.5, -72.81, 1, 1, 38, 170.22, -76.06, 1, 1, 38, 168.29, -66.77, 1, 1, 38, 162.04, -59.37, 1, 1, 38, 149.31, -44.3, 1, 1, 38, 133.95, -26.12, 1, 1, 38, 120.18, -9.81, 1, 1, 38, 107.75, -10.39, 1, 1, 38, 88.89, -11.28, 1, 1, 38, 73.76, -11.99, 1, 1, 38, 39.88, -13.58, 1, 1, 38, 4.25, -15.25, 1, 1, 38, -12.14, -6.55, 1, 1, 38, 41.9, 5.07, 1, 1, 38, 69.9, 4.84, 1, 1, 38, 70.7, 13.36, 1, 1, 38, 66.2, 26.94, 1, 1, 38, 88.19, -0.17, 1, 1, 38, 84.88, 20.63, 1, 2, 38, 107.6, 4.63, 1, 39, -81.01, 108.2, 0, 2, 38, 103.52, 28.04, 0.9987, 39, -64.24, 125.04, 0.0013, 2, 38, 120.63, 5.41, 0.99986, 39, -72.86, 98.01, 0.00014, 2, 38, 116.87, 34.12, 0.99246, 39, -51.57, 117.64, 0.00754, 1, 38, 138.17, -5.5, 1, 2, 38, 133.62, 37.89, 0.97371, 39, -38.83, 106.14, 0.02629, 1, 38, 152.83, -15.84, 1, 2, 38, 151.4, 20.19, 0.98089, 39, -43.03, 81.4, 0.01911, 2, 38, 150.28, 46.38, 0.92129, 39, -22.29, 97.42, 0.07871, 1, 38, 163.55, -21.66, 1, 2, 38, 165.4, 20.08, 0.96578, 39, -35.05, 69.9, 0.03422, 2, 38, 165.19, 52.92, 0.83693, 39, -8.35, 89.02, 0.16307, 1, 38, 183.38, -58.17, 1, 1, 38, 173.76, -49.66, 1, 1, 38, 168.53, -17.59, 1, 1, 38, 171.46, -35.59, 1, 1, 38, 162.58, -45.96, 1, 1, 38, 189.56, -58.31, 1, 1, 38, 183.02, -26.56, 1, 1, 38, 199.2, -32.42, 1, 2, 38, 179.4, 10.49, 0.98221, 39, -34.81, 52.93, 0.01779, 2, 38, 180.51, 45.14, 0.78145, 39, -5.87, 72.02, 0.21855, 1, 38, 197.75, -1.13, 1, 2, 38, 196.99, 33.45, 0.74032, 39, -5.91, 51.81, 0.25968, 1, 38, 216.67, -11.39, 1, 2, 38, 217.12, 27.09, 0.55339, 39, 0.51, 31.7, 0.44661, 2, 38, 239.77, -14.26, 0.98042, 39, -20.21, -10.66, 0.01958, 2, 38, 240.7, 25.79, 0.0576, 39, 13.05, 11.68, 0.9424, 2, 38, 265.59, -25.32, 0.72438, 39, -14.35, -38.13, 0.27562, 2, 38, 266.59, 16.87, 0.06843, 39, 20.7, -14.61, 0.93157, 2, 38, 292.1, -33.34, 0.54207, 39, -5.6, -64.41, 0.45793, 2, 38, 289.51, 10.64, 0.16146, 39, 28.82, -36.93, 0.83854, 2, 38, 314.95, -41.73, 0.45447, 39, 0.72, -87.92, 0.54553, 2, 38, 313.86, 1.98, 0.21145, 39, 35.79, -61.81, 0.78855, 2, 38, 328.81, -50.17, 0.42823, 39, 1.82, -104.11, 0.57177, 2, 38, 331.59, -0.35, 0.20603, 39, 44.12, -77.64, 0.79397, 2, 38, 350.22, -8.1, 0.21689, 39, 48.54, -97.32, 0.78311, 2, 38, 363.83, -13.3, 0.2162, 39, 52.14, -111.44, 0.7838], "hull": 48, "edges": [0, 94, 0, 2, 92, 94, 72, 74, 90, 92, 90, 96, 2, 4, 96, 4, 88, 90, 88, 98, 98, 100, 100, 102, 4, 6, 102, 6, 86, 88, 86, 104, 104, 106, 6, 8, 106, 8, 82, 84, 84, 86, 84, 108, 108, 110, 8, 10, 110, 10, 82, 112, 112, 114, 10, 12, 114, 12, 80, 82, 80, 116, 116, 118, 12, 14, 118, 14, 78, 80, 78, 120, 120, 122, 122, 124, 14, 16, 124, 16, 74, 76, 76, 78, 126, 128, 128, 130, 16, 18, 18, 20, 130, 18, 62, 64, 132, 134, 134, 138, 138, 136, 76, 140, 140, 126, 68, 70, 70, 72, 64, 66, 66, 68, 64, 142, 142, 132, 136, 144, 144, 146, 146, 62, 144, 148, 148, 150, 20, 22, 22, 24, 150, 22, 146, 152, 152, 154, 154, 24, 62, 156, 156, 158, 24, 26, 158, 26, 60, 62, 60, 160, 160, 162, 26, 28, 162, 28, 58, 60, 58, 164, 164, 166, 28, 30, 30, 32, 166, 30, 56, 58, 56, 168, 168, 170, 170, 32, 52, 54, 54, 56, 54, 172, 172, 174, 32, 34, 174, 34, 52, 176, 176, 178, 34, 36, 178, 36, 50, 52, 50, 180, 36, 38, 180, 38, 46, 48, 48, 50, 48, 182, 38, 40, 40, 42, 182, 40, 42, 44, 44, 46], "width": 187, "height": 388}}, "s2": {"s2": {"type": "mesh", "uvs": [0.5074, 0, 0.5583, 0.00951, 0.75233, 0.04576, 0.78855, 0.09109, 0.77019, 0.10548, 0.75689, 0.12182, 0.78216, 0.14602, 0.81805, 0.19717, 0.85611, 0.25421, 0.84654, 0.35028, 0.84098, 0.40616, 0.84195, 0.44528, 0.84382, 0.52035, 0.84589, 0.60363, 0.8576, 0.64963, 0.86848, 0.69239, 0.8792, 0.73448, 0.89074, 0.77981, 0.9178, 0.88613, 0.88021, 0.94372, 0.85781, 0.97803, 0.84347, 1, 0.77944, 1, 0.75949, 0.93799, 0.74415, 0.89033, 0.72263, 0.82343, 0.65291, 0.81012, 0.65996, 0.91378, 0.66575, 0.99893, 0.53506, 0.99914, 0.27588, 0.99956, 0.16639, 0.99973, 0.06218, 0.9999, 0, 1, 0, 0.99563, 0.08656, 0.88333, 0.18331, 0.75782, 0.24988, 0.67144, 0.33267, 0.68101, 0.4014, 0.68894, 0.47067, 0.69694, 0.54604, 0.70565, 0.53738, 0.68131, 0.45996, 0.62648, 0.39345, 0.57938, 0.38642, 0.54442, 0.37345, 0.47999, 0.36307, 0.42842, 0.37915, 0.34454, 0.38614, 0.30811, 0.38955, 0.29032, 0.40093, 0.23092, 0.41406, 0.16243, 0.42172, 0.12247, 0.42799, 0.08977, 0.44255, 0, 0.57411, 0.08063, 0.59073, 0.10805, 0.57611, 0.15024, 0.57744, 0.19031, 0.62995, 0.15973, 0.67315, 0.12123, 0.70572, 0.10278, 0.74361, 0.1091, 0.5894, 0.23302, 0.6572, 0.16025, 0.6911, 0.13336, 0.72898, 0.14549, 0.77352, 0.21879, 0.80409, 0.29314, 0.46577, 0.07694, 0.53224, 0.07377, 0.47242, 0.10647, 0.5349, 0.10383, 0.48571, 0.14918, 0.54553, 0.14813, 0.50167, 0.20297, 0.50366, 0.23566, 0.67647, 0.17555, 0.69907, 0.22358, 0.723, 0.07384, 0.42789, 0.33381, 0.50166, 0.31957, 0.5442, 0.35912, 0.6233, 0.37231, 0.73031, 0.32116, 0.40662, 0.39393, 0.40462, 0.44455, 0.46777, 0.43822, 0.48449, 0.37968, 0.53248, 0.42691, 0.62531, 0.43664, 0.75492, 0.36994, 0.52897, 0.48944, 0.62881, 0.49639, 0.75492, 0.43942, 0.43177, 0.51678, 0.53948, 0.53901, 0.65508, 0.53137, 0.75141, 0.50149, 0.80133, 0.4605, 0.43877, 0.58016, 0.54824, 0.58781, 0.67697, 0.56766, 0.77156, 0.54542, 0.53598, 0.63713, 0.66209, 0.62532, 0.76542, 0.59684, 0.6437, 0.66899, 0.7409, 0.65093, 0.61304, 0.70929, 0.71813, 0.69331, 0.7952, 0.68983, 0.23822, 0.77529, 0.14189, 0.88785, 0.31441, 0.78849, 0.23297, 0.90382, 0.39411, 0.80795, 0.31879, 0.91147, 0.49132, 0.82184, 0.44578, 0.91772, 0.40812, 0.99345, 0.57539, 0.8135, 0.564, 0.91494, 0.71966, 0.73759, 0.79729, 0.73748, 0.72127, 0.78407, 0.81861, 0.78061, 0.80795, 0.83726, 0.79303, 0.89561, 0.80049, 0.94719, 0.69481, 0.73507, 0.66904, 0.78123, 0.54162, 0.28016, 0.61177, 0.30098, 0.71576, 0.2757], "triangles": [18, 128, 17, 24, 25, 128, 129, 24, 128, 129, 128, 18, 23, 24, 129, 19, 129, 18, 130, 23, 129, 19, 130, 129, 20, 130, 19, 22, 23, 130, 21, 22, 130, 20, 21, 130, 110, 108, 111, 131, 110, 111, 125, 112, 15, 125, 15, 16, 124, 111, 112, 124, 131, 111, 112, 125, 124, 127, 125, 16, 127, 16, 17, 132, 110, 131, 126, 132, 131, 126, 124, 125, 126, 125, 127, 126, 131, 124, 132, 122, 110, 26, 122, 132, 25, 126, 127, 132, 126, 25, 26, 132, 25, 128, 25, 127, 128, 127, 17, 99, 94, 95, 99, 95, 100, 100, 11, 12, 99, 100, 12, 104, 99, 12, 103, 98, 99, 104, 103, 99, 107, 103, 104, 104, 12, 13, 107, 104, 13, 106, 103, 107, 109, 106, 107, 14, 109, 107, 14, 107, 13, 108, 106, 109, 112, 109, 14, 112, 14, 15, 111, 108, 109, 111, 109, 112, 114, 35, 36, 116, 114, 113, 116, 115, 118, 122, 26, 27, 123, 119, 122, 123, 122, 27, 120, 119, 123, 118, 117, 120, 121, 118, 120, 29, 120, 123, 121, 120, 29, 28, 29, 123, 28, 123, 27, 30, 116, 118, 30, 118, 121, 30, 121, 29, 31, 114, 116, 31, 116, 30, 32, 34, 35, 32, 35, 114, 32, 114, 31, 33, 34, 32, 113, 36, 37, 113, 37, 38, 115, 113, 38, 115, 38, 39, 117, 115, 39, 117, 39, 40, 122, 41, 110, 119, 40, 41, 119, 41, 122, 117, 40, 119, 114, 36, 113, 116, 113, 115, 118, 115, 117, 120, 117, 119, 93, 91, 94, 96, 88, 93, 46, 87, 96, 98, 94, 99, 97, 93, 94, 97, 94, 98, 96, 93, 97, 45, 46, 96, 101, 44, 45, 101, 96, 97, 101, 45, 96, 102, 97, 98, 102, 98, 103, 101, 97, 102, 106, 102, 103, 43, 101, 102, 44, 101, 43, 105, 43, 102, 105, 102, 106, 108, 105, 106, 42, 105, 108, 43, 105, 42, 110, 41, 42, 108, 110, 42, 85, 135, 69, 9, 69, 8, 92, 85, 69, 92, 69, 9, 84, 134, 85, 84, 85, 92, 83, 134, 84, 10, 92, 9, 90, 89, 83, 90, 83, 84, 91, 84, 92, 90, 84, 91, 88, 86, 89, 88, 89, 90, 95, 92, 10, 91, 92, 95, 87, 47, 86, 87, 86, 88, 100, 95, 10, 11, 100, 10, 46, 47, 87, 93, 88, 90, 93, 90, 91, 94, 91, 95, 96, 87, 88, 80, 1, 2, 80, 2, 3, 62, 56, 80, 4, 80, 3, 63, 62, 80, 4, 63, 80, 62, 61, 56, 5, 63, 4, 66, 61, 62, 67, 62, 63, 67, 63, 5, 66, 62, 67, 67, 5, 6, 60, 57, 61, 65, 60, 61, 65, 61, 66, 78, 65, 66, 78, 66, 67, 68, 67, 6, 68, 6, 7, 78, 67, 68, 79, 78, 68, 64, 59, 60, 64, 60, 65, 64, 65, 78, 64, 78, 79, 68, 7, 8, 69, 135, 79, 69, 68, 8, 69, 79, 68, 134, 64, 79, 134, 79, 135, 133, 64, 134, 134, 135, 85, 71, 0, 1, 56, 71, 1, 70, 55, 0, 70, 0, 71, 80, 56, 1, 54, 55, 70, 73, 71, 56, 72, 70, 71, 72, 71, 73, 54, 70, 72, 61, 57, 56, 73, 56, 57, 53, 54, 72, 75, 73, 57, 74, 72, 73, 74, 73, 75, 53, 72, 74, 58, 75, 57, 58, 57, 60, 52, 53, 74, 59, 58, 60, 76, 74, 75, 59, 76, 75, 59, 75, 58, 52, 74, 76, 51, 52, 76, 77, 76, 59, 77, 59, 64, 51, 76, 77, 133, 77, 64, 50, 51, 77, 82, 50, 77, 82, 77, 133, 81, 49, 50, 82, 81, 50, 48, 49, 81, 83, 133, 134, 82, 133, 83, 89, 81, 82, 89, 82, 83, 86, 48, 81, 86, 81, 89, 47, 48, 86], "vertices": [2, 22, -19.79, 5.84, 0.99303, 23, 202.47, 173.67, 0.00697, 2, 22, -8.11, 38.76, 0.89172, 23, 213.3, 140.46, 0.10828, 2, 22, 36.44, 164.25, 0.02117, 23, 254.61, 13.87, 0.97883, 3, 23, 234.58, -26.64, 0.99313, 33, -240.44, -5.51, 0.00674, 65, -398.21, 76.62, 0.00013, 3, 23, 217.85, -22.46, 0.99131, 33, -223.94, -10.5, 0.00847, 65, -388.43, 62.42, 0.00022, 3, 23, 201.48, -22.02, 0.98774, 33, -207.61, -11.73, 0.01186, 65, -376.44, 51.26, 0.0004, 3, 23, 192.85, -47.03, 0.95101, 33, -197.78, 12.83, 0.04666, 65, -353.43, 64.31, 0.00233, 3, 23, 168.53, -89.9, 0.82427, 33, -171.4, 54.47, 0.16298, 65, -306.82, 80.36, 0.01274, 3, 23, 140.71, -136.59, 0.62769, 33, -141.35, 99.75, 0.33299, 65, -255.08, 96.97, 0.03933, 4, 23, 68.29, -173.18, 0.2227, 33, -67.24, 132.79, 0.60982, 34, -221.44, -3.13, 0.0045, 65, -176.57, 76.5, 0.16298, 4, 23, 26.16, -194.47, 0.06745, 33, -24.13, 152, 0.59058, 34, -190.08, 32.15, 0.02224, 65, -130.89, 64.59, 0.31973, 4, 23, -1.63, -212.15, 0.0182, 33, 4.5, 168.31, 0.47106, 34, -170.72, 58.81, 0.03515, 65, -98.35, 59.45, 0.4756, 3, 33, 59.43, 199.6, 0.15772, 34, -133.57, 109.96, 0.0155, 65, -35.9, 49.6, 0.82678, 2, 33, 120.37, 234.32, 5e-05, 65, 33.38, 38.67, 0.99995, 1, 65, 72.88, 39.58, 1, 2, 65, 109.6, 40.42, 0.55311, 66, -5.45, 45.6, 0.44689, 2, 65, 145.74, 41.26, 0.07474, 66, 30.24, 51.42, 0.92526, 3, 65, 184.68, 42.15, 8e-05, 66, 68.67, 57.7, 0.97103, 67, -48.81, 58.66, 0.02889, 2, 66, 158.8, 72.41, 0.17029, 67, 39.47, 82.05, 0.82971, 2, 66, 206.32, 45.5, 0.01557, 67, 89.37, 59.87, 0.98443, 2, 66, 234.63, 29.46, 0.00014, 67, 119.1, 46.65, 0.99986, 1, 67, 138.14, 38.2, 1, 1, 67, 140.69, -4.5, 1, 1, 67, 89.36, -20.92, 1, 4, 34, 107.5, 318.2, 0.00013, 36, -102.2, 243.14, 0.00071, 37, -260.96, 142.97, 0.00036, 67, 49.91, -33.53, 0.9988, 5, 34, 84.97, 264.61, 0.01945, 36, -124.12, 189.3, 0.04747, 37, -273.92, 86.3, 0.02539, 66, 101.16, -55.9, 0.40935, 67, -5.46, -51.24, 0.49833, 6, 34, 115.35, 227.56, 0.06576, 36, -93.33, 152.6, 0.22723, 37, -237.62, 55.04, 0.17057, 65, 181.95, -118.73, 0.00391, 66, 88.22, -102.02, 0.41761, 67, -13.87, -98.4, 0.11492, 5, 34, 164.26, 300, 0.01087, 36, -45.24, 225.59, 0.22251, 37, -201.92, 134.83, 0.51307, 66, 175.61, -100.58, 0.20686, 67, 72.97, -88.5, 0.04669, 5, 34, 204.44, 359.51, 0.00023, 36, -5.73, 285.55, 0.1827, 37, -172.59, 200.37, 0.60008, 66, 247.4, -99.4, 0.17714, 67, 144.31, -80.36, 0.03985, 5, 34, 274.16, 306.97, 5e-05, 36, 64.58, 233.8, 0.15046, 37, -94.86, 160.62, 0.68324, 66, 244.31, -186.65, 0.13601, 67, 149.69, -167.5, 0.03024, 3, 37, 59.29, 81.8, 0.99377, 66, 238.17, -359.67, 0.00505, 67, 160.36, -340.3, 0.00118, 1, 37, 124.41, 48.5, 1, 1, 37, 186.4, 16.81, 1, 2, 36, 352.44, 21.94, 0, 37, 223.38, -2.1, 1, 2, 36, 350.25, 18.98, 0, 37, 221.69, -5.37, 1, 2, 36, 247.55, -22.69, 0.07578, 37, 127.05, -63.05, 0.92422, 2, 36, 132.76, -69.27, 0.89037, 37, 21.26, -127.5, 0.10963, 2, 36, 53.77, -101.32, 1, 37, -51.54, -171.86, 0, 1, 36, 14.08, -61.97, 1, 5, 34, 187.75, 44.83, 0.00201, 36, -18.87, -29.31, 0.99635, 65, 52.04, -266.24, 0.00017, 66, -20.04, -266.08, 0.00141, 67, -105.71, -272.19, 6e-05, 6, 34, 154.92, 78.12, 0.06207, 36, -52.07, 3.61, 0.8249, 37, -172.9, -85.35, 0.02369, 65, 66.79, -221.87, 0.00781, 66, -11.57, -220.1, 0.07393, 67, -101.74, -225.6, 0.00761, 6, 34, 119.19, 114.34, 0.27904, 36, -88.2, 39.43, 0.38146, 37, -214.33, -55.82, 0.04643, 65, 82.83, -173.59, 0.05539, 66, -2.36, -170.06, 0.21825, 67, -97.43, -174.91, 0.01944, 6, 34, 111.44, 94.51, 0.52417, 36, -95.73, 19.51, 0.19022, 37, -218.55, -76.7, 0.01935, 65, 61.64, -175.69, 0.08068, 66, -23.06, -175.07, 0.17514, 67, -117.54, -181.9, 0.01044, 8, 22, 501.05, -83.47, 0.00092, 33, 260.44, 17.04, 0.00294, 34, 124.82, 26.49, 0.92142, 36, -81.58, -48.35, 0.02759, 37, -193.66, -141.4, 0.00046, 65, 7.12, -218.51, 0.01531, 66, -71.13, -225.02, 0.03076, 67, -160.54, -236.28, 0.00059, 5, 22, 456.76, -123.29, 0.0266, 33, 246.83, -40.94, 0.07175, 34, 136.31, -31.94, 0.90011, 36, -69.43, -106.65, 0.00094, 66, -112.43, -267.93, 0.00061, 3, 22, 426.99, -124.73, 0.06198, 33, 223.22, -59.12, 0.15123, 34, 122.3, -58.25, 0.78679, 3, 22, 372.11, -127.39, 0.21521, 33, 179.7, -92.65, 0.36106, 34, 96.47, -106.74, 0.42373, 3, 22, 328.19, -129.52, 0.38786, 33, 144.86, -119.49, 0.41349, 34, 75.79, -145.55, 0.19866, 3, 22, 259.17, -111.11, 0.72992, 33, 77.68, -143.8, 0.24342, 34, 24.61, -195.38, 0.02666, 3, 22, 229.2, -103.11, 0.86229, 33, 48.51, -154.35, 0.13275, 34, 2.38, -217.02, 0.00497, 3, 22, 214.55, -99.2, 0.91207, 33, 34.26, -159.51, 0.08661, 34, -8.48, -227.6, 0.00131, 2, 22, 165.67, -86.16, 0.99288, 33, -13.31, -176.73, 0.00712, 1, 22, 109.31, -71.12, 1, 1, 22, 76.43, -62.35, 1, 1, 22, 49.52, -55.17, 1, 1, 22, -24.54, -37.21, 1, 2, 22, 52.57, 42.7, 0.77511, 23, 167.64, 100.3, 0.22489, 2, 22, 76.74, 51.2, 0.64913, 23, 153.69, 78.82, 0.35087, 2, 22, 110.97, 37.6, 0.70011, 23, 118.27, 68.7, 0.29989, 2, 22, 144.61, 34.78, 0.62477, 23, 89.9, 50.4, 0.37523, 2, 22, 122.86, 72.47, 0.25907, 23, 130.14, 33.82, 0.74093, 2, 22, 93.81, 104.71, 0.11883, 23, 172.83, 26.02, 0.88117, 2, 22, 80.74, 128.04, 0.04546, 23, 197.41, 15.51, 0.95454, 3, 23, 206.02, -8.88, 0.99689, 33, -212.78, -24.64, 0.00303, 65, -388.54, 44.4, 8e-05, 2, 22, 181.23, 38.78, 0.35043, 23, 63.33, 24.87, 0.64957, 2, 22, 125.3, 90.52, 0.10061, 23, 139.22, 18.04, 0.89939, 2, 22, 105.27, 115.51, 0.03416, 23, 170.33, 10.47, 0.96584, 3, 23, 174.77, -16.46, 0.98829, 33, -181.2, -18.58, 0.01123, 65, -360.09, 29.42, 0.00048, 3, 23, 137.51, -73.96, 0.79516, 33, -141.19, 37.03, 0.18945, 65, -294.12, 47.88, 0.01539, 4, 23, 94.65, -123.95, 0.45688, 33, -95.95, 84.89, 0.47336, 34, -227.86, -58.6, 2e-05, 65, -228.9, 57.01, 0.06974, 1, 22, 41.55, -28.9, 1, 2, 22, 43.77, 15.53, 0.95525, 23, 158.03, 127.2, 0.04475, 1, 22, 66.75, -27.21, 1, 2, 22, 69.12, 14.52, 0.9491, 23, 137.33, 112.53, 0.0509, 1, 22, 103.47, -22.32, 1, 2, 22, 106.97, 17.49, 0.90182, 23, 109.17, 87.07, 0.09818, 1, 22, 149.65, -16.7, 1, 1, 22, 177.16, -18.39, 1, 2, 22, 139.5, 101.9, 0.00132, 23, 134.91, 0.35, 0.99868, 3, 23, 108.21, -33.57, 0.89375, 33, -113.89, -4.72, 0.10043, 65, -298.87, -1.78, 0.00582, 2, 22, 57.79, 142.18, 0.04077, 23, 224.22, 18.31, 0.95923, 3, 22, 253.76, -77.76, 0.79412, 33, 54.2, -119.52, 0.19534, 34, -6.8, -182.94, 0.01054, 2, 22, 247.25, -27.46, 0.91194, 33, 20.12, -81.95, 0.08806, 3, 22, 283.46, -2.86, 0.42469, 23, -43.06, -4.53, 0.10098, 33, 35.8, -41.08, 0.47433, 3, 33, 20.3, 10.64, 0.9828, 34, -91.33, -78.32, 0.00638, 65, -184.45, -73.57, 0.01082, 4, 23, 48.88, -94.11, 0.3072, 33, -51.68, 52.86, 0.62408, 34, -174.32, -69.53, 0.0021, 65, -214.32, 4.36, 0.06662, 3, 22, 302.51, -97.43, 0.52191, 33, 105.45, -107.82, 0.38701, 34, 35.07, -151.15, 0.09108, 3, 22, 344.73, -103.43, 0.31354, 33, 143.53, -88.62, 0.42977, 34, 61.86, -117.97, 0.25669, 3, 22, 344.06, -60.92, 0.25357, 33, 118.7, -54.11, 0.55521, 34, 25.01, -96.76, 0.19122, 4, 22, 296.29, -44.41, 0.52633, 23, -78.58, 20.55, 0.00064, 33, 70.06, -67.85, 0.45183, 34, -13.65, -129.33, 0.0212, 4, 22, 339.34, -16.91, 0.10191, 23, -95.9, -27.51, 0.00039, 33, 89.68, -20.69, 0.86196, 34, -15.21, -78.27, 0.03575, 3, 33, 67.25, 37.7, 0.77452, 34, -59.71, -34.32, 0.1636, 65, -130.88, -81.75, 0.06188, 4, 23, 22.33, -129.51, 0.11168, 33, -23.45, 86.93, 0.70345, 34, -162.64, -26.86, 0.01874, 65, -170.99, 13.34, 0.16614, 3, 22, 391.41, -25, 0.02492, 33, 137.05, 2.41, 0.36765, 34, 18.43, -37.7, 0.60742, 5, 33, 110.33, 63.79, 0.21948, 34, -31.22, 7.21, 0.65596, 36, -237.39, -69.39, 0.00038, 65, -80.94, -88.26, 0.12319, 66, -176.36, -108.21, 0.00099, 4, 23, -27.64, -159.92, 0.0099, 33, 27.94, 114.88, 0.53924, 34, -127.34, 19.79, 0.08774, 65, -113.4, 3.08, 0.36312, 3, 22, 407.17, -92.07, 0.08736, 33, 188.3, -43.64, 0.22491, 34, 84.1, -58.53, 0.68773, 6, 22, 433.67, -22.6, 0.00051, 33, 170.36, 28.52, 0.0081, 34, 38.02, -0.18, 0.98938, 36, -168.08, -76, 0.0003, 65, -56.08, -153.3, 0.00117, 66, -142.74, -169.18, 0.00054, 5, 33, 127.82, 93.28, 0.06845, 34, -27.44, 41.28, 0.62223, 36, -234, -35.28, 0.00357, 65, -48.87, -76.15, 0.29649, 66, -146.27, -91.77, 0.00927, 4, 33, 74.97, 137.79, 0.24363, 34, -93.93, 60.06, 0.1497, 36, -300.7, -17.26, 1e-05, 65, -62.36, -8.39, 0.60667, 4, 23, -26.69, -195.63, 0.00731, 33, 28.72, 150.6, 0.41949, 34, -141.35, 52.66, 0.05376, 65, -90.49, 30.49, 0.51945, 7, 22, 460.73, -93.27, 0.01383, 33, 232.95, -14.03, 0.04005, 34, 112.57, -13.15, 0.93794, 36, -93.38, -88.13, 0.00391, 65, -33.76, -225.61, 0.00044, 66, -110.63, -237.71, 0.00383, 67, -198.63, -252.73, 1e-05, 6, 34, 58.14, 36.11, 0.87953, 36, -148.36, -39.49, 0.02503, 37, -260.99, -143.4, 0.0005, 65, -14.6, -154.75, 0.05715, 66, -101.46, -164.87, 0.03726, 67, -196.56, -179.35, 0.00054, 7, 33, 147.67, 120.73, 0.01535, 34, -20.67, 74.47, 0.46635, 36, -227.6, -2.01, 0.01129, 37, -345.23, -119.19, 4e-05, 65, -16.23, -67.11, 0.47437, 66, -115.19, -78.3, 0.03261, 67, -218.62, -94.52, 1e-05, 4, 33, 101.04, 167.29, 0.06729, 34, -82.34, 97.67, 0.05956, 36, -289.54, 20.49, 5e-05, 65, -23.58, -1.63, 0.8731, 6, 34, 89.74, 64.29, 0.74221, 36, -117.09, -10.95, 0.0811, 37, -234.72, -110.2, 0.00469, 65, 24.86, -170.09, 0.07154, 66, -60.26, -174.61, 0.09715, 67, -154.61, -185.05, 0.00331, 6, 34, 16.56, 107.19, 0.40405, 36, -190.74, 31.12, 0.04806, 37, -314.19, -80.55, 0.00317, 65, 29.83, -85.41, 0.39451, 66, -67.04, -90.06, 0.14792, 67, -169.56, -101.55, 0.00228, 5, 34, -52.95, 129.72, 0.03799, 36, -260.51, 52.86, 0.00226, 37, -386.55, -70.33, 2e-05, 65, 18.32, -13.25, 0.94904, 66, -88.42, -20.18, 0.0107, 6, 34, 48.55, 129.1, 0.34227, 36, -159.01, 53.39, 0.10851, 37, -286.46, -53.46, 0.01401, 65, 63.88, -103.95, 0.23959, 66, -30.76, -103.71, 0.28449, 67, -132.12, -111.63, 0.01112, 6, 34, -12.41, 156.15, 0.09705, 36, -220.27, 79.75, 0.0207, 37, -351.17, -37.31, 0.00199, 65, 60.29, -37.36, 0.68877, 66, -43.53, -38.26, 0.19024, 67, -151.18, -47.71, 0.00125, 6, 34, 85.35, 143.8, 0.27068, 36, -122.37, 68.5, 0.21627, 37, -252.74, -32.64, 0.04187, 65, 93.69, -130.06, 0.10482, 66, 2.38, -125.45, 0.3386, 67, -97.03, -130.04, 0.02776, 6, 34, 21.25, 175.43, 0.11574, 36, -186.83, 99.41, 0.05459, 37, -321.33, -12.52, 0.01049, 65, 92.75, -58.59, 0.2746, 66, -8.44, -54.79, 0.53149, 67, -114.65, -60.77, 0.01308, 6, 34, -21.56, 204.16, 0.00372, 36, -229.96, 127.66, 0.00166, 37, -368.45, 8.41, 0.00027, 65, 98.9, -7.39, 0.49188, 66, -9.43, -3.24, 0.50223, 67, -120.64, -9.56, 0.00023, 2, 36, 112.01, -35.63, 0.94419, 37, -4.64, -97.65, 0.05581, 2, 36, 220.09, 2.33, 0.10033, 37, 95.91, -42.77, 0.89968, 4, 36, 77.69, 3.56, 0.98546, 37, -44.83, -64.5, 0.01421, 66, 61.55, -327.29, 0.00028, 67, -18.57, -325.2, 5e-05, 2, 36, 179.16, 49.32, 0.00785, 37, 47.95, -2.99, 0.99215, 5, 34, 252.1, 121.78, 0.00378, 36, 44.61, 48.38, 0.72343, 37, -84.69, -25.6, 0.24067, 66, 79.91, -274.71, 0.0272, 67, -5.4, -271.08, 0.00492, 4, 36, 136.88, 88.57, 0.00307, 37, -0.1, 28.93, 0.98714, 66, 165.13, -328.25, 0.00802, 67, 84.61, -316.11, 0.00177, 6, 34, 207.38, 170.3, 0.0315, 36, -0.65, 96.38, 0.49498, 37, -137.1, 14.49, 0.31297, 65, 172.74, -226.73, 0.00062, 66, 94.03, -210.26, 0.13543, 67, 2.41, -205.57, 0.0245, 5, 34, 280.35, 216.32, 0.00214, 36, 71.8, 143.22, 0.16887, 37, -73.14, 72.39, 0.73156, 66, 173.57, -243.68, 0.08029, 67, 84.81, -231.12, 0.01713, 4, 36, 129.93, 179.55, 0.04606, 37, -21.63, 117.6, 0.88725, 66, 236.35, -271.21, 0.05461, 67, 149.96, -252.43, 0.01208, 6, 34, 158.36, 198.59, 0.06357, 36, -49.99, 124.12, 0.35901, 37, -190.26, 33.91, 0.25299, 65, 175.67, -170.21, 0.0034, 66, 89.12, -153.87, 0.26816, 67, -7.94, -149.92, 0.05287, 5, 34, 215.97, 262.11, 0.00903, 36, 6.89, 188.28, 0.2252, 37, -144.46, 106.4, 0.55476, 66, 174.19, -164.67, 0.173, 67, 77.77, -152.42, 0.03801, 6, 34, 42.94, 205.78, 0.07061, 36, -165.48, 130, 0.07045, 37, -305.19, 21.11, 0.02254, 65, 129.65, -64.12, 0.05061, 66, 28.86, -55.17, 0.73424, 67, -77.49, -57.53, 0.05155, 5, 34, 1.53, 237, 0.00169, 36, -207.24, 160.75, 0.00165, 37, -351.36, 44.73, 0.0005, 66, 30.71, -3.35, 0.99473, 67, -80.67, -5.77, 0.00143, 6, 34, 65.7, 237.63, 0.03834, 36, -143.09, 162.11, 0.07333, 37, -288.26, 56.41, 0.03532, 65, 168.36, -69.92, 0.00262, 66, 68.01, -55.56, 0.65587, 67, -38.49, -54.12, 0.19451, 1, 66, 67.53, 9.52, 1, 2, 66, 114.94, 0.62, 0.07531, 67, 2.77, 6.34, 0.92469, 1, 67, 52.4, -0.68, 1, 1, 67, 95.46, 6.89, 1, 6, 34, 54.9, 194.07, 0.10484, 36, -153.4, 118.43, 0.10555, 37, -291.4, 11.64, 0.03338, 65, 124.64, -80.09, 0.07141, 66, 26.12, -71.68, 0.63025, 67, -78.62, -74.22, 0.05457, 6, 34, 92.08, 214.67, 0.07944, 36, -116.45, 139.45, 0.17078, 37, -258.32, 38.34, 0.08947, 65, 159.89, -103.86, 0.01375, 66, 64.32, -90.34, 0.5179, 67, -38.79, -89.1, 0.12864, 2, 22, 217.18, 2.71, 0.9624, 23, 12.84, 31.51, 0.0376, 3, 23, 22.22, -17.64, 0.78666, 33, -28.77, -24.81, 0.21301, 65, -244.92, -70.62, 0.00033, 3, 23, 76.52, -65.91, 0.61131, 33, -80.66, 26.04, 0.36372, 65, -253.7, 1.5, 0.02497], "hull": 56, "edges": [0, 110, 42, 44, 50, 52, 66, 68, 82, 84, 108, 110, 0, 2, 2, 4, 2, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 12, 14, 14, 16, 118, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 16, 18, 18, 20, 138, 18, 108, 140, 140, 142, 142, 112, 106, 108, 106, 144, 144, 146, 146, 114, 104, 106, 104, 148, 148, 150, 150, 116, 102, 104, 102, 152, 152, 118, 100, 102, 100, 154, 154, 128, 128, 156, 156, 136, 128, 158, 158, 138, 4, 160, 160, 124, 4, 6, 6, 8, 126, 10, 10, 12, 8, 10, 98, 100, 98, 162, 162, 164, 164, 166, 166, 168, 168, 170, 94, 96, 96, 98, 96, 172, 94, 174, 174, 176, 178, 180, 180, 182, 182, 184, 176, 186, 186, 188, 188, 190, 190, 20, 184, 18, 92, 94, 92, 192, 192, 194, 194, 196, 196, 198, 198, 200, 20, 22, 200, 22, 88, 90, 90, 92, 90, 202, 202, 204, 204, 206, 206, 208, 22, 24, 24, 26, 208, 24, 84, 86, 86, 88, 86, 210, 210, 212, 212, 214, 214, 26, 84, 216, 216, 218, 26, 28, 218, 28, 82, 220, 220, 222, 222, 224, 28, 30, 224, 30, 74, 76, 76, 226, 226, 228, 64, 66, 228, 64, 72, 74, 68, 70, 70, 72, 76, 78, 78, 230, 230, 232, 62, 64, 232, 62, 78, 80, 80, 82, 80, 234, 234, 236, 60, 62, 236, 60, 82, 238, 238, 240, 240, 242, 238, 244, 244, 52, 240, 246, 52, 54, 54, 56, 246, 54, 56, 58, 58, 60, 246, 58, 248, 222, 248, 250, 30, 32, 250, 32, 50, 252, 252, 248, 252, 254, 32, 34, 34, 36, 254, 34, 50, 256, 256, 36, 48, 50, 48, 258, 36, 38, 258, 38, 44, 46, 46, 48, 46, 260, 38, 40, 40, 42, 260, 40, 262, 222, 52, 264, 264, 262, 154, 266, 266, 268, 158, 270, 270, 170, 268, 270, 172, 178], "width": 668, "height": 842}}, "s3": {"s3": {"type": "mesh", "uvs": [0.64704, 0.12341, 0.54214, 0.36533, 0.48439, 0.49853, 0.43465, 0.61325, 0.32861, 0.55969, 0.16512, 0.47712, 0.085, 0.33882, 0.00546, 0.20154, 0.00731, 0, 0.13172, 0, 0.23427, 0.02134, 0.33132, 0.04153, 0.44213, 0.06459, 0.54861, 0.08675, 0.10245, 0.0931, 0.21348, 0.14226, 0.33461, 0.2059, 0.42798, 0.26085], "triangles": [14, 8, 9, 10, 14, 9, 15, 14, 10, 7, 8, 14, 16, 11, 12, 17, 16, 12, 17, 12, 13, 6, 7, 14, 6, 14, 15, 1, 17, 13, 1, 13, 0, 16, 5, 6, 15, 10, 11, 15, 11, 16, 16, 6, 15, 2, 17, 1, 4, 5, 16, 4, 16, 17, 4, 17, 2, 3, 4, 2], "vertices": [1, 24, 81.86, 24.79, 1, 1, 24, 74.13, -9.46, 1, 1, 24, 69.87, -28.33, 1, 1, 24, 66.21, -44.57, 1, 1, 24, 49.21, -41.55, 1, 1, 24, 23, -36.89, 1, 1, 24, 7.14, -22.26, 1, 1, 24, -8.6, -7.73, 1, 1, 24, -14.54, 17.79, 1, 1, 24, 3.46, 22.19, 1, 1, 24, 18.97, 23.11, 1, 1, 24, 33.64, 23.99, 1, 1, 24, 50.39, 24.99, 1, 1, 24, 66.48, 25.95, 1, 1, 24, 2.1, 9.39, 1, 1, 24, 19.68, 7.11, 1, 1, 24, 39.18, 3.35, 1, 1, 24, 54.39, -0.3, 1], "hull": 14, "edges": [14, 16, 16, 18, 26, 0, 18, 28, 28, 14, 18, 20, 20, 30, 10, 12, 12, 14, 30, 12, 20, 22, 22, 32, 32, 10, 22, 24, 24, 26, 24, 34, 6, 8, 8, 10, 34, 8, 0, 2, 26, 2, 2, 4, 4, 6], "width": 149, "height": 130}}, "s4": {"s4": {"type": "mesh", "uvs": [0.03599, 0, 0.13577, 0.01162, 0.26889, 0.0688, 0.47702, 0.15819, 0.54168, 0.14668, 0.65036, 0.12732, 0.72995, 0.11314, 0.83955, 0.09361, 0.85974, 0.35185, 0.80363, 0.47704, 0.71509, 0.67461, 0.6488, 0.67394, 0.47088, 0.67215, 0.20454, 0.66946, 0.12554, 0.54015, 0.00966, 0.3505, 0.01199, 0, 0.10636, 0.1934, 0.25101, 0.34011, 0.45894, 0.39293, 0.55236, 0.41053, 0.67893, 0.36945, 0.75125, 0.35185], "triangles": [22, 6, 7, 22, 7, 8, 21, 5, 6, 21, 6, 22, 20, 4, 5, 20, 5, 21, 3, 4, 20, 19, 3, 20, 9, 22, 8, 12, 19, 20, 11, 20, 21, 12, 20, 11, 10, 21, 22, 10, 11, 21, 22, 9, 10, 17, 0, 1, 2, 17, 1, 18, 2, 3, 18, 17, 2, 17, 15, 16, 17, 16, 0, 19, 18, 3, 14, 17, 18, 15, 17, 14, 13, 14, 18, 13, 18, 19, 13, 19, 12], "vertices": [1, 25, -8.9, 6.74, 1, 1, 25, -1.63, 8.12, 1, 1, 25, 8.45, 8.43, 1, 2, 25, 24.22, 8.92, 0.65912, 26, -0.01, 10.12, 0.34088, 2, 25, 28.75, 10.52, 0.25031, 26, 4.79, 9.85, 0.74969, 2, 25, 36.36, 13.21, 0.01393, 26, 12.85, 9.4, 0.98607, 2, 25, 41.94, 15.17, 0, 26, 18.76, 9.07, 1, 1, 26, 26.89, 8.61, 1, 1, 26, 26.93, -1.31, 1, 1, 26, 22.12, -5.41, 1, 1, 26, 14.54, -11.88, 1, 1, 26, 9.69, -11.13, 1, 2, 25, 28.57, -10.12, 0.29342, 26, -3.32, -9.14, 0.70658, 2, 25, 9.44, -14.86, 0.99976, 26, -22.8, -6.15, 0.00024, 1, 25, 2.56, -11.53, 1, 1, 25, -7.52, -6.65, 1, 1, 25, -10.62, 6.3, 1, 1, 25, -2.05, 0.89, 1, 1, 25, 9.7, -1.89, 1, 2, 25, 25.11, -0.05, 0.75783, 26, -2.64, 1.49, 0.24217, 1, 26, 4.1, -0.19, 1, 1, 26, 13.59, -0.02, 1, 1, 26, 18.99, -0.14, 1], "hull": 17, "edges": [0, 32, 30, 32, 0, 2, 16, 14, 2, 34, 34, 30, 2, 4, 4, 6, 4, 36, 26, 28, 28, 30, 36, 28, 6, 38, 38, 26, 6, 8, 8, 40, 24, 26, 40, 24, 8, 10, 10, 42, 20, 22, 22, 24, 42, 22, 10, 12, 12, 14, 12, 44, 44, 20, 16, 18, 18, 20], "width": 74, "height": 38}}, "st1": {"st1": {"type": "mesh", "uvs": [0.40371, 0.46766, 0.39422, 0.44603, 0.38251, 0.41933, 0.37731, 0.39934, 0.37019, 0.37199, 0.36297, 0.34422, 0.3889, 0.32035, 0.42346, 0.28854, 0.45885, 0.25596, 0.58016, 0.26736, 0.58355, 0.2979, 0.58545, 0.31504, 0.58717, 0.33057, 0.64337, 0.36226, 0.67913, 0.38242, 0.71097, 0.40037, 0.71429, 0.41672, 0.71811, 0.43551, 0.69782, 0.45538, 0.6784, 0.47439, 0.66057, 0.49185, 0.64592, 0.50619, 0.60521, 0.5196, 0.61267, 0.55768, 0.61953, 0.59272, 0.62262, 0.60848, 0.62861, 0.6391, 0.63451, 0.66921, 0.61174, 0.69917, 0.5866, 0.73227, 0.5619, 0.76478, 0.59631, 0.78996, 0.63645, 0.81934, 0.59938, 0.81379, 0.55362, 0.80693, 0.51618, 0.80132, 0.4648, 0.86318, 0.42522, 0.91082, 0.39353, 0.94898, 0.37294, 0.97376, 0.35115, 1, 0.25996, 0.99983, 0.1025, 0.99954, 0.0009, 0.99935, 0.00093, 0.9718, 0.00097, 0.93138, 0.00103, 0.88023, 0.0011, 0.82337, 0.00117, 0.7641, 0.00124, 0.70386, 0.04742, 0.70324, 0.10785, 0.70243, 0.15057, 0.68543, 0.19825, 0.66647, 0.24792, 0.64671, 0.2774, 0.63498, 0.30616, 0.62354, 0.3526, 0.60506, 0.36907, 0.59121, 0.38969, 0.57387, 0.39785, 0.54609, 0.40315, 0.52805, 0.40892, 0.50838, 0.414, 0.49112, 0.4893, 0.29953, 0.48854, 0.31947, 0.4694, 0.34214, 0.44492, 0.36842, 0.56124, 0.36706, 0.4564, 0.39471, 0.59108, 0.38384, 0.46328, 0.41783, 0.62705, 0.40106, 0.47588, 0.4466, 0.65366, 0.41561, 0.48146, 0.46465, 0.65722, 0.42915, 0.48705, 0.48301, 0.66585, 0.44961, 0.48553, 0.50136, 0.65874, 0.46646, 0.48553, 0.52061, 0.61099, 0.49715, 0.48451, 0.54215, 0.47334, 0.56789, 0.57239, 0.55916, 0.41492, 0.59304, 0.53734, 0.59304, 0.40984, 0.6111, 0.5287, 0.60959, 0.35498, 0.63035, 0.40781, 0.63697, 0.5414, 0.64209, 0.35022, 0.64838, 0.41473, 0.65439, 0.54375, 0.66342, 0.36038, 0.67064, 0.45181, 0.68568, 0.55036, 0.69742, 0.29079, 0.6873, 0.34819, 0.70204, 0.4828, 0.72761, 0.25066, 0.7214, 0.33549, 0.74036, 0.53054, 0.75691, 0.17447, 0.74517, 0.26184, 0.78459, 0.39492, 0.79361, 0.45994, 0.79692, 0.11148, 0.77165, 0.18209, 0.83603, 0.29485, 0.84235, 0.03899, 0.76905, 0.08216, 0.82802, 0.12178, 0.87375, 0.23252, 0.88759, 0.06948, 0.90146, 0.18783, 0.92914, 0.03748, 0.92736, 0.15634, 0.95775, 0.01614, 0.95044, 0.13348, 0.98023, 0.05221, 0.98745], "triangles": [40, 41, 39, 42, 121, 41, 121, 119, 41, 41, 119, 39, 43, 122, 42, 42, 122, 121, 43, 44, 122, 121, 122, 120, 122, 44, 120, 120, 118, 121, 121, 118, 119, 119, 117, 39, 39, 117, 38, 44, 45, 120, 118, 116, 119, 119, 116, 117, 120, 45, 118, 117, 115, 38, 45, 46, 118, 116, 114, 117, 117, 114, 115, 118, 46, 116, 114, 116, 113, 113, 116, 47, 116, 46, 47, 38, 115, 37, 115, 111, 37, 37, 111, 36, 114, 110, 115, 115, 110, 111, 114, 113, 110, 110, 106, 111, 111, 106, 107, 113, 109, 110, 109, 105, 110, 110, 105, 106, 47, 112, 113, 113, 112, 109, 112, 50, 109, 47, 48, 112, 105, 102, 106, 50, 51, 109, 109, 51, 105, 50, 112, 49, 49, 112, 48, 51, 52, 105, 105, 52, 102, 52, 53, 102, 111, 107, 36, 107, 108, 36, 36, 108, 35, 34, 31, 33, 32, 33, 31, 34, 35, 30, 35, 108, 104, 108, 107, 104, 106, 103, 107, 107, 103, 104, 106, 102, 103, 103, 101, 104, 102, 99, 103, 103, 100, 101, 103, 99, 100, 102, 53, 99, 100, 96, 97, 100, 99, 96, 53, 54, 99, 99, 54, 96, 54, 55, 96, 55, 93, 96, 55, 56, 93, 31, 34, 30, 35, 104, 30, 30, 104, 29, 104, 101, 29, 101, 98, 29, 29, 98, 28, 100, 97, 101, 101, 97, 98, 27, 28, 95, 97, 95, 98, 28, 98, 95, 96, 94, 97, 97, 94, 95, 96, 93, 94, 95, 26, 27, 94, 92, 95, 95, 92, 26, 93, 91, 94, 94, 91, 92, 93, 90, 91, 93, 56, 90, 92, 91, 89, 91, 88, 89, 92, 25, 26, 92, 89, 25, 91, 90, 88, 56, 57, 90, 90, 57, 88, 57, 58, 88, 88, 86, 89, 88, 58, 86, 25, 87, 24, 25, 89, 87, 89, 86, 87, 24, 85, 23, 24, 87, 85, 86, 84, 87, 87, 84, 85, 58, 59, 86, 86, 59, 84, 59, 60, 84, 84, 83, 85, 84, 60, 83, 23, 85, 22, 85, 83, 22, 60, 61, 83, 83, 81, 22, 83, 61, 81, 61, 62, 81, 62, 79, 81, 22, 81, 82, 22, 82, 21, 82, 81, 79, 62, 63, 79, 21, 82, 20, 80, 82, 79, 79, 63, 77, 63, 75, 77, 63, 0, 75, 80, 79, 77, 0, 73, 75, 0, 1, 73, 75, 78, 77, 1, 71, 73, 71, 69, 70, 69, 68, 70, 1, 2, 71, 2, 69, 71, 2, 3, 69, 3, 67, 69, 3, 4, 67, 69, 67, 68, 4, 5, 67, 67, 66, 68, 67, 5, 66, 68, 12, 13, 68, 66, 12, 5, 6, 66, 66, 65, 12, 66, 6, 65, 65, 11, 12, 6, 7, 65, 11, 64, 10, 11, 65, 64, 65, 7, 64, 7, 8, 64, 64, 9, 10, 64, 8, 9, 76, 75, 73, 70, 68, 13, 20, 82, 80, 77, 78, 80, 20, 80, 19, 19, 80, 18, 80, 78, 18, 75, 76, 78, 76, 73, 74, 18, 78, 17, 78, 76, 17, 73, 72, 74, 76, 16, 17, 76, 74, 16, 71, 70, 72, 74, 15, 16, 74, 72, 15, 72, 14, 15, 72, 70, 14, 70, 13, 14, 73, 71, 72], "vertices": [2, 4, -140.14, 24.13, 0.29017, 2, 258.37, 93.24, 0.70983, 2, 4, -116.35, 39.71, 0.47088, 2, 282.16, 108.83, 0.52912, 2, 4, -86.97, 58.96, 0.68447, 2, 311.54, 128.08, 0.31553, 2, 4, -64.12, 70.82, 0.81301, 2, 334.39, 139.94, 0.18699, 2, 4, -32.85, 87.05, 0.93378, 2, 365.66, 156.17, 0.06622, 2, 4, -1.1, 103.53, 0.98466, 2, 397.41, 172.65, 0.01534, 2, 4, 33.94, 94.74, 0.99869, 2, 432.45, 163.85, 0.00131, 1, 4, 80.64, 83.01, 1, 1, 4, 128.47, 71.01, 1, 2, 3, 167.37, 118.68, 0.00927, 4, 144.02, -20.29, 0.99073, 2, 3, 131.35, 103.82, 0.03695, 4, 108, -35.16, 0.96305, 2, 3, 111.13, 95.48, 0.0854, 4, 87.78, -43.5, 0.9146, 2, 3, 92.82, 87.92, 0.1765, 4, 69.47, -51.05, 0.8235, 2, 3, 68.17, 34.86, 0.63891, 4, 44.82, -104.12, 0.36109, 1, 3, 52.48, 1.1, 1, 1, 3, 38.52, -28.95, 1, 1, 3, 19.61, -37.99, 1, 1, 3, -2.13, -48.38, 1, 1, 3, -30.99, -41.98, 1, 2, 3, -58.61, -35.85, 0.9543, 2, 316.55, -105.72, 0.0457, 2, 3, -83.97, -30.23, 0.65116, 2, 291.2, -100.1, 0.34884, 2, 3, -104.8, -25.61, 0.42017, 2, 270.36, -95.48, 0.57983, 2, 3, -130.8, -2, 0.15547, 2, 244.36, -71.86, 0.84453, 2, 3, -174.92, -22.84, 0.01494, 2, 200.24, -92.71, 0.98506, 2, 3, -215.52, -42.02, 0.00029, 2, 159.64, -111.89, 0.99971, 1, 2, 141.37, -120.52, 1, 2, 2, 105.89, -137.28, 0.99999, 5, -177.55, 90.69, 1e-05, 2, 2, 71, -153.76, 0.99233, 5, -151.72, 119.36, 0.00767, 2, 2, 29.37, -149.71, 0.948, 5, -111.75, 131.69, 0.052, 3, 2, -16.61, -145.23, 0.80535, 5, -67.61, 145.3, 0.19458, 6, -156.06, 192.44, 7e-05, 3, 2, -61.77, -140.84, 0.57334, 5, -24.24, 158.67, 0.41945, 6, -110.85, 196.29, 0.0072, 3, 2, -83.84, -175.68, 0.49866, 5, -17.33, 199.33, 0.49682, 6, -95.45, 234.55, 0.00451, 2, 2, -109.59, -216.33, 0.49678, 5, -9.26, 246.77, 0.50322, 3, 2, -111.83, -187.58, 0.49579, 5, 3.91, 221.11, 0.50255, 6, -70.08, 251.33, 0.00166, 3, 2, -114.61, -152.1, 0.47538, 5, 20.16, 189.45, 0.50929, 6, -60.92, 216.93, 0.01533, 4, 2, -116.88, -123.07, 0.37932, 5, 33.46, 163.54, 0.55928, 6, -53.43, 188.79, 0.0611, 7, -247.68, 153.69, 0.0003, 4, 2, -203.88, -111.57, 0.07961, 5, 118.16, 186.51, 0.515, 6, 34.21, 193.24, 0.36798, 7, -162.17, 173.42, 0.03741, 4, 2, -270.9, -102.72, 0.01609, 5, 183.4, 204.2, 0.30788, 6, 101.72, 196.66, 0.51339, 7, -96.31, 188.61, 0.16264, 4, 2, -324.56, -95.63, 0.00203, 5, 235.64, 218.37, 0.17819, 6, 155.78, 199.41, 0.49078, 7, -43.56, 200.78, 0.32901, 4, 2, -359.42, -91.02, 0.00013, 5, 269.58, 227.57, 0.1257, 6, 190.9, 201.19, 0.43944, 7, -9.3, 208.69, 0.43473, 3, 5, 305.51, 237.31, 0.10035, 6, 228.08, 203.07, 0.40088, 7, 26.98, 217.05, 0.49877, 3, 5, 350.78, 185.57, 0.0568, 6, 261.33, 142.89, 0.28266, 7, 70.25, 163.63, 0.66054, 3, 5, 428.96, 96.22, 0.00207, 6, 318.74, 38.97, 0.01656, 7, 144.98, 71.37, 0.98137, 1, 7, 193.2, 11.84, 1, 1, 7, 166.01, -10.3, 1, 1, 7, 126.1, -42.79, 1, 2, 6, 223.25, -101.74, 0.07242, 7, 75.61, -83.89, 0.92758, 2, 6, 159.97, -136.9, 0.45157, 7, 19.47, -129.59, 0.54843, 2, 6, 94.02, -173.55, 0.78289, 7, -39.05, -177.23, 0.21711, 3, 5, 196.95, -209.84, 0.00023, 6, 26.99, -210.8, 0.88079, 7, -98.51, -225.64, 0.11899, 3, 5, 173.34, -184.23, 0.00925, 6, 9.37, -180.76, 0.88464, 7, -121.13, -199.15, 0.10612, 3, 5, 142.45, -150.71, 0.08045, 6, -13.69, -141.45, 0.86115, 7, -150.72, -164.49, 0.0584, 4, 2, -65.37, 185.32, 0.00016, 5, 104.93, -140.84, 0.25295, 6, -48.26, -123.82, 0.72727, 7, -187.84, -153.19, 0.01963, 4, 2, -30.99, 158.99, 0.01747, 5, 63.05, -129.81, 0.49576, 6, -86.84, -104.16, 0.48376, 7, -229.27, -140.58, 0.00302, 3, 2, 4.83, 131.56, 0.11826, 5, 19.42, -118.33, 0.6504, 6, -127.04, -83.66, 0.23134, 3, 2, 26.09, 115.28, 0.25632, 5, -6.47, -111.51, 0.61643, 6, -150.89, -71.5, 0.12725, 3, 2, 46.83, 99.4, 0.45242, 5, -31.74, -104.86, 0.48552, 6, -174.17, -59.64, 0.06206, 3, 2, 80.32, 73.75, 0.80278, 5, -72.53, -94.13, 0.18637, 6, -211.75, -40.48, 0.01084, 3, 2, 101.01, 67.63, 0.9179, 5, -93.98, -96.46, 0.07948, 6, -233.2, -38.21, 0.00262, 3, 2, 126.9, 59.98, 0.98302, 5, -120.81, -99.39, 0.01694, 6, -260.04, -35.36, 4e-05, 3, 4, -236.14, -3.65, 0.0018, 2, 162.37, 65.47, 0.99767, 5, -151.42, -118.14, 0.00052, 2, 4, -213.11, -0.08, 0.01234, 2, 185.4, 69.03, 0.98766, 2, 4, -187.99, 3.81, 0.04721, 2, 210.52, 72.92, 0.95279, 2, 4, -165.95, 7.22, 0.12021, 2, 232.56, 76.33, 0.87979, 2, 3, 106.63, 170.48, 0.00318, 4, 83.28, 31.5, 0.99682, 2, 3, 82.4, 162.9, 0.00623, 4, 59.05, 23.92, 0.99377, 1, 4, 27.1, 28.35, 1, 2, 4, -10.52, 35.13, 0.96229, 2, 387.99, 104.24, 0.03771, 3, 3, 42.55, 91.57, 0.2893, 4, 19.2, -47.4, 0.69816, 2, 417.71, 21.71, 0.01253, 3, 3, -16.1, 155.2, 0.1116, 4, -39.45, 16.22, 0.74887, 2, 359.06, 85.33, 0.13953, 3, 3, 29.52, 63.42, 0.64292, 4, 6.17, -75.56, 0.33322, 2, 404.69, -6.44, 0.02385, 3, 3, -42.32, 140.86, 0.24045, 4, -65.67, 1.88, 0.49517, 2, 332.84, 70.99, 0.26437, 1, 3, 17.43, 30.71, 1, 3, 3, -73.98, 120.14, 0.27083, 4, -97.33, -18.84, 0.26552, 2, 301.18, 50.27, 0.46365, 1, 3, 6.31, 5.77, 1, 3, 3, -94.4, 108.79, 0.17421, 4, -117.75, -30.19, 0.16533, 2, 280.76, 38.92, 0.66046, 1, 3, -9.16, -2.29, 1, 3, 3, -115.18, 97.32, 0.05208, 4, -138.53, -41.66, 0.07134, 2, 259.98, 27.45, 0.87658, 1, 3, -31.75, -16.79, 1, 2, 4, -161.04, -48.05, 0.02209, 2, 237.48, 21.06, 0.97791, 3, 3, -53.79, -18.58, 0.95587, 4, -77.14, -157.56, 0.00015, 2, 321.37, -88.45, 0.04398, 2, 4, -184.26, -55.9, 0.00349, 2, 214.25, 13.22, 0.99651, 3, 3, -102.32, 3.02, 0.36983, 4, -125.67, -135.96, 0.0007, 2, 272.84, -66.84, 0.62948, 2, 4, -210.48, -63.95, 0.00022, 2, 188.03, 5.16, 0.99978, 1, 2, 154.3, 2.66, 1, 2, 3, -186.43, 5.33, 0.00965, 2, 188.73, -64.54, 0.99035, 3, 2, 109.86, 34.13, 0.97982, 5, -115.07, -68.97, 0.02003, 6, -247.97, -6.86, 0.00015, 1, 2, 139.41, -53.31, 1, 3, 2, 86.86, 30.41, 0.95737, 5, -95.29, -56.66, 0.04166, 6, -226.02, 0.97, 0.00097, 1, 2, 117.37, -53.89, 1, 3, 2, 50.4, 61.75, 0.67693, 5, -49.56, -71.5, 0.30249, 6, -184.49, -23.25, 0.02058, 3, 2, 55.17, 21.31, 0.93012, 5, -69.56, -36.04, 0.06794, 6, -196.5, 15.65, 0.00194, 1, 2, 81.24, -76.2, 1, 3, 2, 27.51, 57.8, 0.5258, 5, -29.96, -59.04, 0.44395, 6, -162.69, -15.23, 0.03026, 3, 2, 35.82, 9.27, 0.95895, 5, -56.36, -17.47, 0.04025, 6, -179.66, 30.99, 0.00079, 2, 2, 56.08, -86.57, 0.99406, 5, -112.03, 63.13, 0.00594, 3, 2, 3.11, 41.47, 0.36031, 5, -13.75, -34.56, 0.61946, 6, -141.65, 5.25, 0.02022, 2, 2, 7.04, -29.97, 0.98737, 5, -44.94, 29.84, 0.01263, 2, 2, 16.67, -105.14, 0.92423, 5, -82.84, 95.47, 0.07577, 3, 2, -33.77, 84.4, 0.04475, 5, 36.84, -59.92, 0.78627, 6, -97.61, -30.29, 0.16898, 2, 5, 22.32, -15.04, 0.98527, 6, -102.26, 16.65, 0.01473, 3, 2, -36.05, -69.19, 0.66065, 5, -20.32, 82.65, 0.33813, 6, -123.17, 121.17, 0.00122, 4, 2, -84.59, 99.16, 1e-05, 5, 89.42, -53.94, 0.5143, 6, -44.96, -35.61, 0.48468, 7, -200.03, -65.76, 0.00101, 2, 2, -86.98, 30.84, 0.00601, 5, 65.26, 10.01, 0.99399, 3, 2, -59.85, -115.23, 0.58437, 5, -16.13, 134.31, 0.40738, 6, -108.1, 170.76, 0.00826, 3, 5, 150.09, -77.06, 0.05674, 6, 9.42, -71.09, 0.9135, 7, -140.28, -91.17, 0.02976, 3, 2, -158.09, 65.43, 0.00026, 5, 144.21, 5.54, 0.06851, 6, 21.22, 10.87, 0.93124, 4, 2, -136.86, -33.31, 0.10663, 5, 86.52, 88.44, 0.72576, 6, -17.54, 104.13, 0.16688, 7, -197.52, 76.63, 0.00073, 4, 2, -125.15, -81.1, 0.24568, 5, 57.28, 128.02, 0.64029, 6, -37.7, 149.02, 0.11317, 7, -225.23, 117.28, 0.00086, 3, 5, 206.77, -90.42, 0.00168, 6, 61.97, -96.2, 0.86251, 7, -84.15, -106.69, 0.13581, 2, 6, 107.69, -9.82, 0.97375, 7, -54.26, -13.64, 0.02625, 4, 2, -219.79, 18.31, 0.00542, 5, 182.95, 72.82, 0.21819, 6, 73.37, 68.38, 0.75347, 7, -101.75, 57.35, 0.02292, 2, 6, 85.66, -145.57, 0.77992, 7, -52.18, -151.14, 0.22008, 2, 6, 135.42, -80.61, 0.51038, 7, -14.56, -78.48, 0.48962, 2, 6, 171.76, -26.2, 0.05542, 7, 11.68, -18.54, 0.94458, 3, 5, 257.23, 75.61, 0.05288, 6, 146.55, 55.33, 0.6301, 7, -27.42, 57.31, 0.31702, 2, 6, 221.76, -43.5, 0.00361, 7, 63.94, -26.82, 0.99639, 3, 5, 319.19, 85.28, 0.02025, 6, 209.15, 51.61, 0.17168, 7, 34.87, 64.62, 0.80808, 1, 7, 104.74, -24.68, 1, 3, 5, 362.22, 91.53, 0.01021, 6, 252.52, 48.57, 0.07267, 7, 78.1, 69.22, 0.91712, 1, 7, 137.68, -18.59, 1, 3, 5, 395.08, 97.5, 0.0058, 6, 285.91, 47.43, 0.0417, 7, 111.17, 73.94, 0.95251, 3, 5, 442.48, 57.59, 2e-05, 6, 323.74, -1.64, 0.00069, 7, 157.01, 32.26, 0.99929], "hull": 64, "edges": [42, 44, 16, 18, 14, 16, 14, 128, 18, 20, 128, 20, 10, 12, 12, 14, 12, 130, 20, 22, 22, 24, 130, 22, 10, 132, 132, 24, 8, 10, 8, 134, 134, 136, 24, 26, 136, 26, 4, 6, 6, 8, 6, 138, 138, 140, 26, 28, 28, 30, 140, 28, 4, 142, 142, 144, 144, 30, 4, 2, 2, 146, 146, 148, 30, 32, 32, 34, 148, 32, 2, 0, 0, 126, 0, 150, 150, 152, 152, 34, 126, 154, 154, 156, 34, 36, 156, 36, 124, 126, 124, 158, 158, 160, 36, 38, 160, 38, 122, 124, 122, 162, 162, 164, 38, 40, 40, 42, 164, 40, 118, 120, 120, 122, 120, 166, 166, 44, 118, 168, 168, 170, 44, 46, 170, 46, 114, 116, 116, 118, 116, 172, 172, 174, 46, 48, 174, 48, 114, 176, 176, 178, 48, 50, 178, 50, 112, 114, 112, 180, 180, 182, 182, 184, 50, 52, 52, 54, 184, 52, 110, 112, 110, 186, 186, 188, 188, 190, 190, 54, 108, 110, 108, 192, 192, 194, 194, 196, 54, 56, 196, 56, 106, 108, 106, 198, 198, 200, 200, 202, 56, 58, 58, 60, 202, 58, 102, 104, 104, 106, 104, 204, 204, 206, 206, 208, 208, 60, 102, 210, 210, 212, 212, 214, 214, 216, 216, 70, 98, 100, 100, 102, 100, 218, 218, 220, 220, 222, 70, 72, 222, 72, 98, 224, 224, 226, 226, 228, 228, 230, 72, 74, 230, 74, 96, 98, 94, 96, 92, 94, 90, 92, 94, 232, 232, 234, 74, 76, 234, 76, 92, 236, 236, 238, 76, 78, 78, 80, 238, 78, 90, 240, 240, 242, 80, 82, 242, 82, 86, 88, 88, 90, 88, 244, 82, 84, 84, 86, 244, 84, 60, 62, 62, 64, 68, 70, 64, 66, 66, 68], "width": 754, "height": 1273}}, "sj1": {"sj1": {"type": "mesh", "uvs": [0.03697, 0, 0.2907, 0.06403, 0.56153, 0.13237, 0.7929, 0.19076, 0.82667, 0.27399, 0.86866, 0.37749, 0.91236, 0.48519, 0.99406, 0.68657, 0.7966, 0.78181, 0.63798, 0.85833, 0.42925, 0.95901, 0.10114, 1, 0.00383, 1, 0.00392, 0.84142, 0.004, 0.69194, 0.00408, 0.56049, 0.00417, 0.41101, 0.00424, 0.27698, 0.00433, 0.12492, 0.0044, 0, 0.32517, 0.19452, 0.39541, 0.34142, 0.46565, 0.47286, 0.49892, 0.58884, 0.51001, 0.70224, 0.50262, 0.83111], "triangles": [20, 1, 2, 18, 0, 1, 18, 19, 0, 1, 20, 18, 2, 3, 4, 20, 2, 4, 17, 18, 20, 21, 20, 4, 17, 20, 21, 21, 4, 5, 16, 17, 21, 22, 21, 5, 16, 21, 22, 22, 5, 6, 15, 16, 22, 23, 22, 6, 15, 22, 23, 23, 6, 7, 14, 15, 23, 8, 24, 23, 14, 23, 24, 7, 8, 23, 25, 14, 24, 9, 25, 24, 13, 14, 25, 8, 9, 24, 10, 13, 25, 10, 25, 9, 11, 12, 13, 10, 11, 13], "vertices": [1, 50, -97.23, -37.11, 1, 1, 50, -76.03, 18.62, 1, 1, 50, -53.39, 78.12, 1, 1, 50, -34.05, 128.94, 1, 1, 50, -7.55, 135.97, 1, 1, 50, 25.41, 144.72, 1, 1, 50, 59.71, 153.82, 1, 1, 50, 123.83, 170.83, 1, 1, 50, 153.3, 126.7, 1, 1, 50, 176.98, 91.26, 1, 1, 50, 208.15, 44.61, 1, 1, 50, 219.95, -28.1, 1, 1, 50, 219.6, -49.6, 1, 1, 50, 169.34, -48.76, 1, 1, 50, 121.96, -47.97, 1, 1, 50, 80.3, -47.28, 1, 1, 50, 32.92, -46.49, 1, 1, 50, -9.56, -45.78, 1, 1, 50, -57.76, -44.97, 1, 1, 50, -97.35, -44.31, 1, 1, 50, -34.54, 25.57, 1, 1, 50, 12.27, 40.33, 1, 1, 50, 54.19, 55.17, 1, 1, 50, 91.07, 61.92, 1, 1, 50, 127.05, 63.79, 1, 1, 50, 167.87, 61.49, 1], "hull": 20, "edges": [0, 38, 20, 22, 22, 24, 36, 38, 36, 40, 6, 8, 40, 8, 34, 36, 34, 42, 8, 10, 42, 10, 32, 34, 32, 44, 10, 12, 12, 14, 44, 12, 30, 32, 30, 46, 46, 14, 28, 30, 28, 48, 14, 16, 48, 16, 24, 26, 26, 28, 26, 50, 16, 18, 18, 20, 50, 18, 0, 2, 2, 4, 4, 6], "width": 221, "height": 317}}, "t1": {"t1": {"type": "mesh", "uvs": [0.44856, 0, 0.49035, 0.01441, 0.53959, 0.03138, 0.58976, 0.04867, 0.63122, 0.06296, 0.63881, 0.08378, 0.6364, 0.11617, 0.63414, 0.14662, 0.63161, 0.18071, 0.63017, 0.20013, 0.62893, 0.21686, 0.60939, 0.2321, 0.57659, 0.23334, 0.54153, 0.23466, 0.49427, 0.22712, 0.46932, 0.22314, 0.45199, 0.22038, 0.43143, 0.2232, 0.36958, 0.19958, 0.33366, 0.18585, 0.31088, 0.17715, 0.27484, 0.16339, 0.23911, 0.14974, 0.18836, 0.13035, 0.19432, 0.0976, 0.20083, 0.06181, 0.20602, 0.03322, 0.26774, 0.02193, 0.29238, 0.01743, 0.34663, 0.00752, 0.38775, 0, 0.24554, 0.05995, 0.24954, 0.09658, 0.28718, 0.05006, 0.29117, 0.08596, 0.3442, 0.03358, 0.31968, 0.0757, 0.30257, 0.12881, 0.38298, 0.03651, 0.34249, 0.10097, 0.33223, 0.15665, 0.41606, 0.03761, 0.39211, 0.09108, 0.37329, 0.16141, 0.44971, 0.06105, 0.43203, 0.1028, 0.42348, 0.14346, 0.41321, 0.18228, 0.50788, 0.05336, 0.47366, 0.10171, 0.44914, 0.14712, 0.43227, 0.16363, 0.448, 0.18778, 0.48279, 0.19913, 0.45142, 0.20096, 0.48279, 0.21085, 0.50218, 0.21378, 0.47406, 0.22179, 0.47649, 0.0977, 0.51472, 0.07387, 0.56833, 0.07314, 0.60882, 0.10647, 0.55521, 0.05519, 0.61929, 0.14058, 0.52149, 0.09234, 0.46612, 0.11568, 0.52035, 0.10872, 0.56032, 0.10481, 0.58874, 0.08994, 0.45539, 0.13555, 0.50018, 0.12999, 0.55841, 0.11483, 0.44841, 0.1735, 0.47544, 0.15395, 0.50094, 0.14295, 0.52378, 0.1449, 0.53177, 0.15444, 0.53063, 0.16397, 0.50398, 0.17497, 0.47239, 0.17766, 0.56831, 0.14295, 0.56679, 0.1273, 0.57707, 0.11581, 0.59838, 0.11777, 0.61433, 0.12442, 0.57973, 0.14661, 0.60105, 0.1383, 0.572, 0.12796, 0.58259, 0.12136, 0.5962, 0.12136, 0.60088, 0.12786, 0.59937, 0.13592, 0.58637, 0.14204, 0.46073, 0.17146, 0.4825, 0.1569, 0.50155, 0.14864, 0.51878, 0.14932, 0.52755, 0.16292, 0.51364, 0.16913, 0.49323, 0.1737, 0.46829, 0.17438, 0.53232, 0.18335, 0.59021, 0.16488, 0.62409, 0.15623, 0.54228, 0.1978, 0.59869, 0.18004, 0.6068, 0.18596, 0.588, 0.19211, 0.56993, 0.20277, 0.59169, 0.20253, 0.6068, 0.19661, 0.522, 0.22006, 0.60229, 0.21557], "triangles": [35, 28, 29, 38, 29, 30, 35, 29, 38, 33, 27, 28, 33, 28, 35, 31, 26, 27, 31, 27, 33, 25, 26, 31, 36, 33, 35, 34, 33, 36, 31, 33, 34, 32, 31, 34, 25, 31, 32, 24, 25, 32, 38, 36, 35, 36, 37, 34, 21, 32, 34, 32, 23, 24, 21, 22, 32, 22, 23, 32, 60, 62, 3, 41, 30, 0, 38, 30, 41, 48, 1, 2, 48, 2, 62, 1, 41, 0, 44, 1, 48, 44, 41, 1, 59, 48, 62, 59, 62, 60, 42, 38, 41, 42, 41, 44, 48, 59, 58, 48, 58, 44, 45, 42, 44, 39, 38, 42, 38, 39, 36, 44, 58, 45, 49, 45, 58, 65, 45, 49, 36, 39, 37, 46, 42, 45, 46, 45, 69, 40, 37, 39, 42, 46, 43, 42, 43, 39, 40, 39, 43, 34, 37, 21, 20, 21, 37, 51, 46, 50, 47, 43, 46, 40, 20, 37, 51, 47, 46, 52, 47, 51, 19, 40, 43, 20, 40, 19, 18, 19, 43, 18, 43, 47, 103, 63, 7, 105, 103, 8, 14, 56, 111, 13, 111, 108, 84, 61, 6, 86, 91, 84, 112, 109, 110, 105, 102, 103, 103, 102, 86, 50, 46, 69, 51, 50, 73, 50, 69, 73, 73, 72, 51, 93, 73, 94, 79, 72, 100, 52, 79, 53, 101, 56, 53, 111, 56, 104, 107, 106, 110, 105, 107, 104, 108, 104, 107, 108, 107, 109, 101, 102, 105, 107, 105, 106, 91, 92, 90, 64, 59, 60, 58, 59, 64, 4, 68, 60, 67, 64, 60, 67, 60, 68, 61, 68, 5, 67, 68, 61, 66, 64, 67, 71, 66, 67, 71, 67, 61, 82, 71, 61, 83, 82, 61, 83, 88, 82, 89, 88, 83, 83, 61, 84, 89, 83, 84, 81, 71, 82, 87, 81, 82, 88, 87, 82, 70, 66, 71, 89, 90, 88, 88, 90, 87, 92, 87, 90, 80, 81, 87, 80, 87, 92, 70, 75, 74, 71, 75, 70, 71, 81, 75, 80, 75, 81, 85, 80, 92, 85, 92, 86, 96, 95, 74, 75, 96, 74, 73, 74, 95, 76, 75, 80, 96, 75, 76, 97, 96, 76, 77, 97, 76, 77, 76, 80, 77, 80, 85, 77, 85, 102, 98, 95, 96, 98, 96, 97, 94, 95, 98, 99, 94, 98, 77, 78, 98, 77, 98, 97, 78, 77, 101, 66, 58, 64, 66, 65, 49, 66, 49, 58, 70, 65, 66, 69, 65, 70, 69, 70, 74, 73, 69, 74, 94, 73, 95, 92, 91, 86, 102, 85, 86, 101, 77, 102, 90, 89, 84, 101, 105, 104, 109, 107, 110, 53, 78, 101, 112, 108, 109, 111, 104, 108, 101, 104, 56, 78, 99, 98, 79, 99, 78, 53, 79, 78, 99, 100, 94, 79, 100, 99, 53, 56, 55, 100, 93, 94, 72, 93, 100, 52, 72, 79, 54, 52, 53, 93, 72, 73, 54, 53, 55, 54, 47, 52, 72, 52, 51, 106, 105, 8, 84, 91, 90, 86, 84, 63, 103, 86, 63, 63, 84, 6, 110, 106, 8, 69, 45, 65, 57, 55, 56, 108, 112, 13, 112, 110, 9, 18, 47, 17, 54, 17, 47, 7, 63, 6, 6, 61, 5, 68, 4, 5, 9, 110, 8, 10, 112, 9, 16, 54, 55, 57, 16, 55, 15, 16, 57, 17, 54, 16, 14, 57, 56, 15, 57, 14, 11, 112, 10, 12, 112, 11, 112, 12, 13, 14, 111, 13, 62, 2, 3, 4, 60, 3, 8, 103, 7], "vertices": [3, 9, 133.18, 145.45, 0.00094, 100, -43.83, -44.58, 0.8354, 101, -94.95, 36.55, 0.16366, 3, 9, 127.24, 110.18, 0.04757, 100, -52.68, -9.93, 0.9211, 101, -104.87, 70.92, 0.03132, 1, 8, 199.53, 79.68, 1, 1, 8, 192.4, 37.35, 1, 1, 8, 186.51, 2.36, 1, 1, 8, 165.2, -10.88, 1, 1, 8, 128.59, -21.34, 1, 2, 8, 94.17, -31.17, 0.99419, 9, 14.89, -42.22, 0.00581, 1, 8, 55.64, -42.17, 1, 1, 8, 33.7, -48.44, 1, 1, 8, 14.79, -53.84, 1, 1, 8, -6.87, -45.61, 1, 1, 8, -16.16, -22.65, 1, 1, 8, -26.1, 1.9, 1, 1, 8, -29.12, 38.49, 1, 1, 8, -30.72, 57.81, 1, 1, 8, -31.83, 71.23, 1, 1, 8, -39.93, 84.85, 1, 3, 8, -28.58, 137.91, 0.19797, 9, -107.86, 126.86, 0.02582, 100, 169.04, 70.01, 0.77622, 4, 8, -21.99, 168.73, 0.08214, 9, -101.27, 157.68, 0.00692, 100, 175.49, 39.16, 0.90207, 101, 121.68, 127.02, 0.00887, 4, 8, -17.81, 188.27, 0.036, 9, -97.09, 177.21, 0.00116, 100, 179.58, 19.61, 0.90868, 101, 126.37, 107.6, 0.05416, 3, 8, -11.2, 219.18, 0.00423, 100, 186.05, -11.34, 0.72533, 101, 133.8, 76.87, 0.27044, 2, 100, 192.47, -42.02, 0.39169, 101, 141.16, 46.4, 0.60831, 2, 100, 201.58, -85.6, 0.0939, 101, 151.61, 3.12, 0.9061, 2, 100, 170.22, -108.3, 0.01018, 101, 120.96, -20.54, 0.98982, 1, 101, 87.47, -46.39, 1, 1, 101, 60.72, -67.03, 1, 1, 101, 18.73, -42.99, 1, 1, 101, 1.97, -33.4, 1, 2, 100, 14.65, -95.23, 0.16303, 101, -34.94, -12.27, 0.83697, 2, 100, -12.82, -78.35, 0.4839, 101, -62.91, 3.75, 0.5161, 1, 101, 62.35, -23.78, 1, 2, 100, 141.18, -78.44, 0.03904, 101, 91.01, 8.41, 0.96096, 1, 101, 32.11, -9.44, 1, 2, 100, 110.77, -63.75, 0.16336, 101, 60.16, 22.16, 0.83664, 2, 100, 38.42, -75.88, 0.11601, 101, -11.77, 7.81, 0.88399, 2, 100, 87.36, -56.06, 0.26692, 101, 36.52, 29.13, 0.73308, 3, 8, 33.95, 212.37, 8e-05, 100, 142.01, -23.39, 0.72253, 101, 90.15, 63.46, 0.2774, 2, 100, 21.18, -52.01, 0.49902, 101, -29.74, 31.13, 0.50098, 2, 100, 97.58, -23.32, 0.78709, 101, 45.73, 62.16, 0.21291, 4, 8, 10.15, 180.73, 0.02885, 9, -69.12, 169.67, 0.00212, 100, 150.96, 15.18, 0.94062, 101, 97.9, 102.29, 0.0284, 2, 100, 5.27, -32.77, 0.78315, 101, -46.24, 49.87, 0.21685, 2, 100, 63.73, -3.61, 0.98582, 101, 11.29, 80.82, 0.01418, 3, 8, 14.77, 149.61, 0.09338, 9, -64.51, 138.55, 0.02411, 100, 134.14, 41.77, 0.88251, 2, 9, 65.56, 121.68, 0.00535, 100, 8.38, 4.53, 0.99465, 3, 8, 94.12, 129.67, 0.01262, 9, 14.85, 118.62, 0.07267, 100, 53.51, 27.86, 0.91471, 3, 8, 46.84, 120.5, 0.108, 9, -32.44, 109.45, 0.11534, 100, 93.03, 55.39, 0.77666, 3, 8, 1.18, 113.25, 0.26195, 9, -78.09, 102.19, 0.06735, 100, 131.84, 80.51, 0.6707, 2, 9, 88.15, 83.02, 0.19844, 100, -27.93, 30.74, 0.80156, 3, 8, 105.39, 100.35, 0.00594, 9, 26.12, 89.29, 0.69191, 100, 31.33, 50.12, 0.30215, 3, 8, 48.96, 100.8, 0.05627, 9, -30.31, 89.74, 0.70478, 100, 83.11, 72.55, 0.23895, 3, 8, 26.53, 106.64, 0.21495, 9, -52.74, 95.59, 0.13094, 100, 105.99, 76.29, 0.65411, 1, 9, -75.81, 75.28, 1, 1, 9, -80.04, 46.16, 1, 2, 8, -10.37, 78.93, 0.41297, 9, -89.65, 67.88, 0.58703, 2, 8, -13.8, 52.81, 0.24904, 9, -93.08, 41.76, 0.75096, 2, 8, -12.38, 37.86, 0.1, 9, -91.65, 26.8, 0.9, 1, 8, -28.07, 54.93, 1, 3, 8, 110.53, 99.83, 0.00547, 9, 31.25, 88.78, 0.59484, 100, 26.43, 48.51, 0.39968, 3, 8, 146.27, 81.48, 1e-05, 9, 66.99, 70.43, 0.32091, 100, -13.68, 50.82, 0.67909, 2, 9, 80.74, 32.41, 0.56559, 100, -41.65, 80.02, 0.43441, 2, 9, 53.45, -9.04, 0.98739, 100, -33.47, 128.97, 0.01261, 2, 9, 97.54, 48.52, 0.42583, 100, -50.48, 58.48, 0.57417, 2, 8, 97.31, -18.29, 0.16296, 9, 18.04, -29.34, 0.83704, 3, 8, 127.35, 69.7, 0.00089, 9, 48.08, 58.65, 0.64391, 100, -1.16, 69.25, 0.35519, 3, 8, 88.04, 100.49, 0.01515, 9, 8.76, 89.43, 0.7046, 100, 47.26, 57.02, 0.28024, 1, 9, 29.58, 53.31, 1, 1, 9, 43.58, 26.23, 1, 2, 9, 66.98, 11.51, 0.72, 100, -37.53, 104.69, 0.28, 3, 8, 63.35, 100.68, 0.02221, 9, -15.93, 89.63, 0.83174, 100, 69.91, 66.83, 0.14605, 1, 9, 1.06, 59.72, 1, 1, 9, 31.97, 23.82, 1, 1, 9, -59.83, 80.35, 1, 1, 9, -31.56, 68.39, 1, 1, 9, -13.17, 54.31, 1, 1, 9, -9.83, 37.26, 1, 1, 9, -18.5, 27.97, 1, 1, 9, -29.38, 25.2, 1, 1, 9, -48.05, 40.1, 1, 1, 9, -58.67, 61.66, 1, 1, 9, 3.1, 6.18, 1, 1, 9, 20.13, 13.15, 1, 1, 9, 35.39, 10.13, 1, 1, 9, 38.36, -5.83, 1, 2, 8, 114.08, -8.67, 0.1, 9, 34.8, -19.73, 0.9, 1, 9, 1.77, -3.35, 1, 2, 8, 95.44, -4.4, 0.1, 9, 16.16, -15.45, 0.9, 1, 9, 20.65, 9.18, 1, 1, 9, 30.55, 4.1, 1, 1, 9, 33.84, -5.62, 1, 3, 8, 107.01, -0.36, 0.00049, 9, 27.73, -11.41, 0.99951, 100, -10.92, 141.55, 0, 2, 8, 97.68, -2.31, 0.1, 9, 18.4, -13.36, 0.9, 1, 9, 8.46, -6.37, 1, 1, 9, -54.59, 72.32, 1, 1, 9, -33.13, 62.24, 1, 1, 9, -19.36, 51.73, 1, 1, 9, -15.95, 39.17, 1, 1, 9, -28.95, 27.79, 1, 1, 9, -39.22, 35.39, 1, 1, 9, -49.23, 48.26, 1, 1, 9, -56, 65.82, 1, 1, 9, -50.53, 16.71, 1, 1, 9, -16.02, -17.7, 1, 2, 8, 81.07, -27.6, 0.27656, 9, 1.79, -38.65, 0.72344, 1, 9, -64.19, 4.17, 1, 2, 8, 48.45, -18.4, 0.1, 9, -30.82, -29.46, 0.9, 2, 8, 43.82, -26.42, 0.1, 9, -35.45, -37.47, 0.9, 1, 9, -46.84, -26.36, 1, 1, 9, -63.05, -17.46, 1, 2, 8, 21.74, -21.85, 0.002, 9, -57.54, -32.91, 0.998, 2, 8, 31.97, -30.43, 0.1, 9, -47.3, -41.48, 0.9, 2, 8, -14.57, 21.34, 0.20025, 9, -93.85, 10.29, 0.79975, 2, 8, 9.79, -34.33, 0.16152, 9, -69.48, -45.38, 0.83848], "hull": 31, "edges": [0, 60, 8, 10, 34, 32, 22, 20, 52, 54, 54, 62, 62, 64, 44, 46, 64, 44, 54, 56, 56, 66, 66, 68, 42, 44, 68, 42, 50, 52, 46, 48, 48, 50, 56, 58, 58, 60, 58, 70, 70, 72, 72, 74, 40, 42, 74, 40, 60, 76, 76, 78, 78, 80, 38, 40, 80, 38, 0, 82, 82, 84, 84, 86, 34, 36, 36, 38, 86, 36, 0, 2, 2, 88, 88, 90, 90, 92, 92, 94, 94, 34, 2, 4, 4, 96, 94, 102, 102, 100, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 30, 32, 30, 114, 114, 112, 96, 116, 116, 98, 116, 118, 118, 120, 96, 124, 4, 6, 6, 8, 124, 6, 10, 12, 122, 12, 12, 14, 14, 16, 126, 14, 116, 128, 128, 120, 98, 130, 130, 132, 132, 134, 120, 136, 136, 122, 134, 136, 100, 138, 138, 130, 138, 140, 140, 142, 142, 122, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 144, 160, 162, 162, 164, 164, 166, 122, 168, 168, 126, 166, 168, 160, 170, 170, 172, 172, 168, 160, 174, 174, 176, 176, 178, 178, 168, 168, 180, 180, 182, 182, 184, 184, 160, 144, 186, 186, 188, 188, 190, 190, 192, 192, 152, 152, 194, 194, 196, 196, 198, 198, 200, 200, 186, 150, 162, 152, 160, 154, 170, 102, 146, 104, 158, 106, 202, 202, 204, 16, 206, 206, 126, 204, 206, 112, 208, 208, 210, 210, 16, 212, 214, 214, 216, 216, 218, 218, 220, 220, 16, 212, 16, 216, 222, 26, 28, 28, 30, 222, 28, 16, 18, 18, 20, 18, 224, 224, 26, 22, 24, 24, 26], "width": 754, "height": 1174}}}}], "animations": {"animation1": {"slots": {"d4": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "d5": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{"curve": 0.165, "c2": 0.15, "c3": 0.75}, {"time": 1.3333, "y": -10, "curve": 0.223, "c2": 0.01, "c3": 0.765, "c4": 0.82}, {"time": 2.6667}]}, "s2": {"rotate": [{"angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -3.6, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.21}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.76, "y": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s3": {"rotate": [{"angle": -2.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.94}]}, "s5": {"rotate": [{"angle": 4.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 8.4, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 4.84}]}, "st2": {"translate": [{"x": -7.71, "y": -5.24, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "x": 9.02, "y": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -8.83, "y": -5.1, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": -7.71, "y": -5.24}]}, "t1": {"rotate": [{"angle": 1.42, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.42}], "translate": [{"x": -1.26, "y": -0.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -3.76, "y": -1.36, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": -1.26, "y": -0.46}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.85, "y": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st1": {"rotate": [{"angle": -0.08, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -1.2, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.08}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -17.08, "y": 7.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"rotate": [{"angle": 0.38, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.38}]}, "f19": {"rotate": [{"angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.04}], "scale": [{"x": 1.24}]}, "f20": {"rotate": [{"angle": 0.71, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": -0.64, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 12.46, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": 0.71}]}, "f21": {"rotate": [{"angle": 3.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": -0.64, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 12.46, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 3.35}]}, "f22": {"rotate": [{"angle": 6.35, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "angle": -0.64, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 12.46, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": 6.35}]}, "f13": {"rotate": [{"angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.91}]}, "f14": {"rotate": [{"angle": -1.31, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 5.3, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -1.31}]}, "f15": {"rotate": [{"angle": 0.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 8.9, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 0.71}]}, "f16": {"rotate": [{"angle": 3.83, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 11.3, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "angle": 3.83}]}, "f17": {"rotate": [{"angle": 4.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 7.7, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 4.17}]}, "f18": {"rotate": [{"angle": 8.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 11.3, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 8.87}]}, "f2": {"rotate": [{"angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.98}]}, "f3": {"rotate": [{"angle": 0.86, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -15.88, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": 0.86}]}, "f4": {"rotate": [{"angle": -3.6, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -13.48, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "angle": -3.6}]}, "f5": {"rotate": [{"angle": -15.68, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -23.08, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": -15.68}]}, "f6": {"rotate": [{"angle": 2.06, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": 2.69}, {"time": 1.8333, "angle": 0.82, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.06}], "translate": [{"x": 0.64, "y": 0.2, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.9, "y": 0.6, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.64, "y": 0.2}]}, "f7": {"rotate": [{"angle": -1.2, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 0.5, "angle": 2.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.2}]}, "f8": {"rotate": [{"angle": -2.51, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "angle": 1.24, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -5.18, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -2.51}]}, "f9": {"rotate": [{"angle": -3.73, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 0.05, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.73}]}, "f10": {"rotate": [{"angle": -2.51, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "angle": 1.24, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -5.18, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -2.51}]}, "f11": {"rotate": [{"angle": -3.73, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 0.05, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.73}]}, "f12": {"rotate": [{"angle": -4.72, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.5, "angle": -1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -5.18, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -4.72}]}, "h1": {"rotate": [{"angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.65}]}, "h2": {"rotate": [{"angle": -1.5, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 0.77, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -1.5}]}, "h3": {"rotate": [{"angle": -1.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.77, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.21}]}, "h4": {"rotate": [{"angle": -0.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 0.77, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.84}]}, "h5": {"rotate": [{"angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 0.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -1.34}]}, "h6": {"rotate": [{"angle": -0.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 0.77, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.76}]}, "h7": {"rotate": [{"angle": -0.12, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 0.77, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -0.12}]}, "h8": {"rotate": [{"angle": 0.45, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 0.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.45}]}, "h9": {"rotate": [{"angle": 2.35, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 7.01, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.35}]}, "h10": {"rotate": [{"angle": 5.72, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 2.35, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 7.01, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 5.72}]}, "h11": {"rotate": [{"angle": 6.57, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": 7.01, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": 5.72, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 6.57}]}, "h13": {"rotate": [{"angle": -9.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -9.05}]}, "h14": {"rotate": [{"angle": -7.45, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -9.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -7.45}]}, "h15": {"rotate": [{"angle": -4.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -9.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -4.71}]}, "st4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.18, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st5": {"rotate": [{"angle": -1.07, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.18, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.07}]}, "st6": {"rotate": [{"angle": -2.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.6}]}, "bone3": {"rotate": [{"angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.92, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.4}], "translate": [{"y": 8, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -22, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 8}], "scale": [{"x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.02, "y": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.08}]}, "bone4": {"rotate": [{"angle": -1.05, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -1.28, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.05}]}, "s16": {"rotate": [{"angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.96}]}, "s17": {"rotate": [{"angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.71}]}, "s18": {"rotate": [{"angle": 3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.88, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 3.25}]}, "t3": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t4": {"shear": [{"x": 1.12, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2, "x": 4.89, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 2.6667, "x": 1.12}]}, "d6": {"translate": [{"x": 0.88, "y": -1.18, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.79, "y": -6.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.88, "y": -1.18}], "shear": [{"x": 18.82, "y": -2.43, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 102, "y": -13.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 18.82, "y": -2.43}]}, "d7": {"shear": [{"x": 0.75, "y": -0.53, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 12, "y": -8.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 0.75, "y": -0.53}]}, "d8": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 12, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "d9": {"translate": [{"x": 0.09, "y": -0.37, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.41, "y": -5.83, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 0.09, "y": -0.37}], "shear": [{"x": -5.05, "y": 1.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -80.4, "y": 21.6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": -5.05, "y": 1.36}]}, "d10": {"shear": [{"x": -0.98, "y": -0.68, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -15.6, "y": -10.8, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": -0.98, "y": -0.68}]}, "d11": {"shear": [{"x": -2.88, "y": -1.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -15.6, "y": -10.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -2.88, "y": -1.99}]}, "d1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.59, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "d2": {"rotate": [{"angle": 0.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 0.78, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": 0.1}]}, "d3": {"rotate": [{"angle": 1.48, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.02, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 1.48}]}, "d4": {"rotate": [{"angle": 18.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 28.96, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 18.31}]}, "bone7": {"translate": [{"x": 6.64, "y": -5.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "y": -14, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 36, "y": 32, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 40, "y": 24, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 6.64, "y": -5.52}]}, "s6": {"rotate": [{"angle": -8.75, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -26.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -8.75}]}, "s9": {"rotate": [{"angle": -8.75, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -26.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -8.75}]}, "s11": {"rotate": [{"angle": -8.75, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -26.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -8.75}]}, "s13": {"rotate": [{"angle": -8.75, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -26.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -8.75}]}, "s4": {"rotate": [{"angle": -0.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "angle": -12.33, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.77}]}, "s8": {"rotate": [{"angle": -0.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "angle": -12.33, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.77}]}, "s10": {"rotate": [{"angle": -0.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "angle": -12.33, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.77}]}, "s12": {"rotate": [{"angle": -0.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -12.33, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.77}]}, "f1": {"scale": [{"x": 1.011, "y": 1.011, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": 1.06, "y": 1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.011, "y": 1.011}], "shear": [{"x": 0.66, "y": -1.11, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": 3.6, "y": -6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.66, "y": -1.11}]}, "h16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.11, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "h17": {"rotate": [{"angle": -0.27, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -2.11, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -0.27}]}, "h18": {"rotate": [{"angle": -0.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -2.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.78}]}, "h19": {"rotate": [{"angle": -1.33, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.11, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -1.33}]}, "h20": {"rotate": [{"angle": -0.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -3.52, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.65}]}, "h21": {"rotate": [{"angle": -1.16, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.3333, "angle": -0.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.52, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.16}]}, "h22": {"rotate": [{"angle": -1.75, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -0.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.75}]}, "h23": {"rotate": [{"angle": -1.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -3.52, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.18}]}, "h24": {"rotate": [{"angle": -2.21, "curve": 0.338, "c2": 0.35, "c3": 0.692, "c4": 0.75}, {"time": 0.5, "angle": -0.55, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -3.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -2.21}]}, "h26": {"rotate": [{"angle": -1.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.52, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.18}]}, "bone6": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s15": {"rotate": [{"angle": 6.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 13.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 6.6}]}, "s14": {"rotate": [{"angle": -2.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -7.14, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -2.4}], "scale": [{"x": 0.985, "y": 0.952, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.92, "y": 0.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.985, "y": 0.952}]}, "s7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "scale": [{"x": 0.996, "y": 0.982, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.98, "y": 0.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.996, "y": 0.982}]}, "bone8": {"translate": [{"x": 42.2, "y": -189.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": 367.14, "y": 93.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 42.2, "y": -189.9}]}, "d13": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 37.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "d5": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.2, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}}, "deform": {"default": {"f1": {"f1": [{"offset": 110, "vertices": [-0.31888, 2.16459, -1.47206, 1.61873, -1.15895, 1.85575, -0.23616, 1.6924, -1.14, 1.27301, -0.89405, 1.45623, -0.11817, 1.23868, -0.78896, 0.96227, 0.00428, 0.854, -0.47271, 0.71134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37018, 0.79014, -0.74783, 0.44946, 0, 0, 0, 0, -0.14857, 1.20613, -0.79599, 0.91831, 0, 0, 0, 0, 0, 0, -0.06079, 1.00386, -0.61026, 0.79944, 0, 0, 0, 0, 0, 0, -0.08954, 1.06616, -0.6689, 0.83512], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 54, "vertices": [7.32979, -9.91063, 6.79585, -15.37915, 0.17842, -15.21631, -3.49515, -15.12575, -6.95743, -15.04065, -10.48529, -12.36671, -13.12208, -7.20966, -7.60777, -5.26379, -8.90185, -2.52278, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.72865, 11.73436, -7.98011, 8.77524, -6.28273, 10.06012, -1.28024, 9.1746, -6.17998, 6.90103, -4.84669, 7.89429, -0.64062, 6.71493, -4.27698, 5.21649, 0.02319, 4.62959, -2.56257, 3.85622, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.00676, 4.28339, -4.05403, 2.43652, 0, 0, 0, 0, -0.80539, 6.53849, -4.31512, 4.97821, 0, 0, 0, 0, 0, 0, -0.32954, 5.44199, -3.30827, 4.3338, 0, 0, 0, 0, 0, 0, -0.4854, 5.77973, -3.62616, 4.52724, 0, 0, 0, 0, 0, 0, 6.15784, 1.02318, 0, 0, 0, 0, 6.15784, 1.02318, 6.23805, 0.23116, 0, 0, 0, 0, 0, 0, 6.15784, 1.02318, 6.23805, 0.23116, 0, 0, 0, 0, 0, 0, 6.15784, 1.02318, 6.23805, 0.23116, 6.23805, 0.23116, 6.20043, 0.72633, 6.20043, 0.72633, 0, 0, 0, 0, 2.593, -3.33534, 1.37493, -3.99367, -0.23491, -9.01474, -3.39704, -11.35722, -7.17998, -9.70724, -9.9327, -6.86601, -8.79017, -10.39029, -6.42066, -11.83916], "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "offset": 110, "vertices": [-0.31888, 2.16459, -1.47206, 1.61873, -1.15895, 1.85575, -0.23616, 1.6924, -1.14, 1.27301, -0.89405, 1.45623, -0.11817, 1.23868, -0.78896, 0.96227, 0.00428, 0.854, -0.47271, 0.71134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37018, 0.79014, -0.74783, 0.44946, 0, 0, 0, 0, -0.14857, 1.20613, -0.79599, 0.91831, 0, 0, 0, 0, 0, 0, -0.06079, 1.00386, -0.61026, 0.79944, 0, 0, 0, 0, 0, 0, -0.08954, 1.06616, -0.6689, 0.83512]}]}, "h2": {"h2": [{"vertices": [0.47419, -4.21732, -1.57779, -6.67874, -2.02585, 1.75097, -2.02585, 1.75097, -2.7647, 2.38961, 1.11451, 2.26723, 1.83182, 2.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.56192, -2.38091, 2.15426, -1.86209, -0.68573, -3.86859, 0.39997, -3.90857, 0.39997, -3.90857, -0.65572, -0.75867, 0, 0, 0, 0, 1.07832, -1.64375, 1.48727, -1.28556], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "vertices": [1.41222, -12.55977, -4.69888, -19.89024, -6.03328, 5.21465, -6.03328, 5.21465, -8.23367, 7.11659, 3.31917, 6.75214, 5.45541, 6.31145, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.65161, -7.0907, 6.41568, -5.54558, -2.04221, -11.52121, 1.19116, -11.64029, 1.19116, -11.64029, -1.95282, -2.25941, 0, 0, 0, 0, 3.2114, -4.89532, 4.42931, -3.82857], "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "vertices": [0.47419, -4.21732, -1.57779, -6.67874, -2.02585, 1.75097, -2.02585, 1.75097, -2.7647, 2.38961, 1.11451, 2.26723, 1.83182, 2.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.56192, -2.38091, 2.15426, -1.86209, -0.68573, -3.86859, 0.39997, -3.90857, 0.39997, -3.90857, -0.65572, -0.75867, 0, 0, 0, 0, 1.07832, -1.64375, 1.48727, -1.28556]}]}, "s2": {"s2": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "offset": 28, "vertices": [4.81635, 4.03278, 4.08973, 6.98863, 4.08368, 4.7729, 4.81635, 4.03278, 4.08973, 6.98863, 4.08368, 4.7729, 1.81685, -0.79536, -3.07484, 2.30527, 3.03448, -2.6353, 0.45804, -3.81575, -4.04952, 4.86343, 3.92578, -5.45894, 6.50895, -3.6322, -0.71785, -6.28789, -4.64182, 6.33769, 4.47052, -7.08744, 7.86512, -5.10226, -1.37448, -7.73468, -2.17976, 2.66707, 2.11126, -2.99196, 3.52969, -2.01573, -0.42204, -3.4186, 4.94699, -7.01064, 8.27071, -4.72319, -0.98878, -8.01021, 4.94699, -7.01064, -0.98878, -8.01021, -0.57759, -4.67857, -2.13222, -2.28486, -1.86935, -2.50583, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.69684, -5.6767, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.06584, 13.24059, 6.7005, 8.75557, 5.16058, 9.74233, 11.73183, -2.09544, 12.40903, -0.49939, 12.3596, 1.20897, 6.65498, -0.70428, 5.5851, -0.88565, 5.65411, 0.04904, 2.27509, -6.13362, 3.45557, -5.55484, 4.18454, -5.02902, 2.55717, -3.28638, 3.9951, -0.98312, 3.85215, 0.89639, 3.43106, 0.64948, 3.27477, 1.20772, 2.61032, -3.41956, 3.48868, -2.82054, 3.8434, -2.31548, 2.55717, -2.98328, 3.8093, -0.71993, 3.54171, 1.07364, 3.19147, 0.8196, 3.49313, -2.51715, -1e-05, 3.03801, -1.8621, 2.63892, -3.11327, 1.77792, 1e-05, 8.52419, -5.15532, 7.11829, -8.05435, 4.59921, 1e-05, 8.52419, -5.15532, 7.11829, -8.05435, 4.59921, 2e-05, 5.48618, -3.29322, 4.47937, -4.94108, 2.82129, 0, 0, 0, 0, 0, 0, 7.76924, 2.90652, 4.51934, 7.62509, 0.25939, 11.24495, 7.15267, -0.76954, 6.27256, 4.02733, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.89591, -4e-05, 3.01654, 2.4655, 8.43547, -5.87927, -10.25203, -0.78563, 6.35841, -1.4049, -5.81231, -2.93584, 6.35841, -1.4049, -5.81231, -2.93584, -5.81231, -2.93584, 6.01783, 2.95381, 6.13852, -2.17347, 1.08638, 7.49517, 3.90196, -6.49087, 6.35841, -1.4049, -5.81231, -2.93584, 6.35841, -1.4049, -5.81231, -2.93584, -5.81231, -2.93584, 6.01783, 2.95381, 6.13852, -2.17347, -4.31355, -0.02538, 4.38288, -0.15074, 2.99887, -3.10093, -2.55455, 3.88049, 2.4451, -4.32762, 4.53635, -3.291, -1.04021, -4.52799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.14066, -2.32638, 2.11561, -0.2947, 7.14581, -4e-05, -5.53294, -4.52192, -2.78527, -2.27633, 2.91735, 2.36144, 3.57007, -0.44078, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.09685, 5.31426, -9.01294, -0.0427, -1.82174, 2.06747, 2.71887, -0.44777, -2.74473, 0.60008, 1.70335, 1.66878, 0.72431, 2.90189, 2.27049, -0.06677, -5.99724, -1.96718, 6.16827, 1.8923, 4.78238, 5.90421, 5.56625, -2.97662, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.52304, 1.32527, -7.72461, -4.44395, -4.91067, -9.69399, -7.82308, 3.29645, 8.14339, 2.39827, -8.36513, -2.27219, -6.63888, -7.68056, -3.59152, -2.5848, 1.14529, 4.27441, -1.32862, -4.60283, 1.15136, -5.92432, 3.67158, -3.1146, 5.05197, -1.18884, 0.60205, -4.5685, -8.43606, 0.68639, 8.5432, -1.0957, 8.55657, 4.05551, 5.3329, -6.57316, -6.646, 5.69935, -8.88342, 0.5878, -8.61103, -4.8284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.53461, 1.51636, 8.61123, -2.00275, 9.09187, 3.09151, 4.80106, -7.21786, -9.81831, -1.0502, -7.31906, -7.3576, -3.01627, -12.67534, -7.25219, 2.54306, -7.44041, -2.55165, -5.62921, -7.42014, -6.23215, -4.07158, -6.88533, 3.41213, -7.22116, 2.63265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.9958, -3.26273, 10.10668, 1.93138, 9.15303, -0.35371, 4.26691, -8.31189, -0.89807, 1.09884, 0.86984, -1.23265, 1.45419, -0.83049, -0.17383, -1.40849, -10.21162, -2.80513, -6.56238, -9.14021, -1.38182, -14.18543, -3.74521, -9.31754, -10.478, -1.53306, -10.24617, -2.67806, -9.78032, -4.06005, -2.64264, -10.26779, -4.14023, -6.47577, -3.01602, -7.07056, -8.05409, 0.30061, -8.04021, -0.58846, -7.88098, -1.68673, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.21014, -4.24695, 11.75378, 1.58899, 10.52678, -0.85338, 4.49532, -9.79241, 0.05685, -14.13245, -2.48084, -9.51723, -0.87706, -9.79602, -10.02811, -2.75919, -9.66521, -3.84732, -9.04245, -5.13789, -2.14568, -6.32358, -3.00241, -3.90334, -2.31676, -4.34558, -5.10886, 0.63068, -5.14757, 0.06394, -5.10727, -0.64372, 13.2562, 0.35559, 11.61478, -1.92894, 11.77284, 0.01598, 4.04892, -11.31943, 5.2702, -10.80446, -3.89662, -6.46184, -3.48344, -7.07857, -2.26738, -7.55539, -8.05557, -0.44466, -7.68845, -2.44051, -7.27998, -3.47218, 4.43131, -8.0834, 2.68076, -8.71986, 4.08305, -8.15656, -6.72409, -6.72648, -6.22699, -8.3848, -5.01395, -9.15881, 4.56229, -6.50266, 4.55513, -6.75392, 5.60759, -5.90633, -3.40079, -7.68343, -1.30336, -8.29061, -0.15306, -8.39183, 10.434, -0.20853, 9.0549, -1.84692, 9.23447, -0.3259, 2.83975, -9.00331, 3.81332, -8.63574, 4.96472, -8.02953, 4.65155, -2.65636, 3.57745, -2.54779, 3.94846, -1.92217, -0.55589, -4.50529, -0.05673, -4.53915, 0.56821, -4.50361, -4.14026, 2.56606, -4.50938, 1.84738, -2.24753, -3.53534, -1.6348, -3.85764, -1.68175, 4.33546, -2.37685, 3.99835, 2.72136, 4.08054, 2.13696, 4.41663, 0.70674, -1.43419, 0.9314, -1.29764, -0.00833, -3.84947, 0.68259, -5.48054, 1.57635, -5.29098, -3.31622, -4.71883, -2.6351, -5.12761, 2.78713, -10.68973, 4.51276, -10.08102, -6.53932, -9.68983, -5.14558, -10.49428, 0.52978, -12.57822, -0.41519, -11.4404, 1.48047, -11.35052, -10.40997, -5.53702, -9.09268, -7.50719, -7.97665, -8.6839, 0.52978, -12.57822, -0.41519, -11.4404, 1.48047, -11.35052, -9.09268, -7.50719, -7.97665, -8.6839, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.62892, -2.07234, 2.79068, -1.9877, 3.08044, -1.49954, -0.43381, -3.51467, -0.04414, -3.54109, 0.44304, -3.51348, 3.83134, -0.72688, 3.20949, -1.1158, 3.34818, -0.57034, 0.95381, -3.34623, 1.40642, -3.18288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.06152, -0.95859, 0.32663, 2.23039, 0.01874, 2.25456, 0.26544, 5.56868, -0.42374, 8.38258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.80869, 6.52078, 8.62412, -1.37307, 1.19547, -6.02005, -0.98188, 6.5997, 3.52696, 5.02322, -5.09712, -3.03447, 5.29512, 3.09076, 5.71594, -1.5885], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "offset": 24, "vertices": [-9.05077, -1.88645, 9.26711, 1.67767, 17.24933, 2.82524, -1.32071, 10.26908, 17.96138, 13.18712, 18.87695, 3.75922, 8.17945, 13.97726, 8.16736, 9.54581, 3.6337, -1.59073, -6.14967, 4.61054, 6.06896, -5.2706, 0.91608, -7.6315, -8.09904, 9.72687, 7.85155, -10.91788, 13.01789, -7.2644, -1.4357, -12.57578, -9.28365, 12.67537, 8.94103, -14.17488, 15.73024, -10.20453, -2.74896, -15.46936, -4.35953, 5.33414, 4.22251, -5.98392, 7.05938, -4.03146, -0.84409, -6.8372, 9.89399, -14.02129, 16.54141, -9.44638, -1.97757, -16.02042, 9.89399, -14.02129, -1.97757, -16.02042, -1.15518, -9.35715, -4.26443, -4.56972, -3.73871, -5.01166, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.39368, -11.35339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.17829, 27.88974, 2.2308, 19.28244, -0.98706, 19.38657, 18.91348, 8.07637, 17.90692, 10.1109, 16.35013, 12.47598, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2e-05, 5.46981, -3.35261, 4.75148, -5.60567, 3.20135, -4.30807, 3.07025, -4.7587, 2.31735, 0.67047, 5.42812, 0.06622, 5.4686, -0.68228, 5.42708, -2e-05, 6.07602, -3.7242, 5.27785, -6.22654, 3.55585, -4.78726, 3.41049, 0.07513, 6.07538, -2e-05, 6.07602, -3.7242, 5.27785, -6.22654, 3.55585, -0.00012, 14.4544, -8.85958, 12.55573, -14.81259, 8.45926, -0.00012, 14.4544, -8.85958, 12.55573, -14.81259, 8.45926, -0.00011, 8.37838, -5.13538, 7.27788, -8.58605, 4.90341, 0, 0, 0, 0, 0, 0, 15.53848, 5.81305, 9.03867, 15.25018, 0.51878, 22.4899, 14.30534, -1.53907, 12.54511, 8.05466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.79182, -8e-05, 6.03309, 4.931, 16.87094, -11.75854, -20.50406, -1.57126, 12.71683, -2.80979, -11.62461, -5.87168, 12.71683, -2.80979, -11.62461, -5.87168, -11.62461, -5.87168, 12.03565, 5.90762, 12.27704, -4.34694, 2.17276, 14.99033, 7.80391, -12.98174, 12.71683, -2.80979, -11.62461, -5.87168, 12.71683, -2.80979, -11.62461, -5.87168, -11.62461, -5.87168, 12.03565, 5.90762, 12.27704, -4.34694, -8.6271, -0.05077, 8.76576, -0.30148, 5.99774, -6.20186, -5.1091, 7.76098, 4.8902, -8.65524, 9.0727, -6.582, -2.08041, -9.05598, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.28133, -4.65276, 4.23122, -0.5894, 14.29163, -7e-05, -11.06589, -9.04385, -5.57053, -4.55266, 5.83469, 4.72287, 7.14014, -0.88156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.1937, 10.62851, -18.02587, -0.0854, -3.64348, 4.13493, 5.43775, -0.89554, -5.48946, 1.20016, 3.40669, 3.33755, 1.44862, 5.80377, 4.54099, -0.13354, -11.99448, -3.93436, 12.33653, 3.78461, 9.56477, 11.80841, 11.13251, -5.95325, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -17.04608, 2.65054, -15.44922, -8.88791, -9.82135, -19.38797, -15.64615, 6.5929, 16.28677, 4.79654, -16.73026, -4.54439, -13.27777, -15.36111, -7.18304, -5.1696, 2.29058, 8.54882, -2.65723, -9.20566, 2.30272, -11.84863, 7.34316, -6.2292, 10.10393, -2.37769, 1.2041, -9.13699, -16.87211, 1.37278, 17.0864, -2.19141, 17.11313, 8.11102, 10.6658, -13.14632, -13.29201, 11.39869, -17.76685, 1.1756, -17.22205, -9.6568, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -17.06921, 3.03271, 17.22245, -4.00549, 18.18375, 6.18301, 9.60211, -14.43571, -19.63661, -2.1004, -14.63812, -14.71519, -6.03254, -25.35068, -14.50438, 5.08612, -14.88082, -5.1033, -11.25841, -14.84027, -12.46429, -8.14316, -13.77066, 6.82425, -14.44232, 5.2653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17.99159, -6.52547, 20.21335, 3.86276, 18.30606, -0.70743, 8.53381, -16.62378, -1.79614, 2.19767, 1.73967, -2.4653, 2.90837, -1.66098, -0.34766, -2.81697, -20.42323, -5.61026, -13.12475, -18.28043, -2.76363, -28.37085, -7.49042, -18.63507, -20.95599, -3.06612, -20.49234, -5.35612, -19.56064, -8.1201, -5.28528, -20.53558, -8.28046, -12.95154, -6.03204, -14.14111, -16.10818, 0.60121, -16.08041, -1.17693, -15.76196, -3.37346, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20.42028, -8.4939, 23.50756, 3.17798, 21.05356, -1.70676, 8.99063, -19.58481, 0.1137, -28.26489, -4.96167, -19.03445, -1.75412, -19.59204, -20.05621, -5.51837, -19.33041, -7.69464, -18.0849, -10.27577, -0.52183, 0.38211, 0.0502, 0.0517, 0.03696, 0.06345, 0.12527, 0.51717, 0.0563, 0.52861, -0.00864, 0.53416, 26.5124, 0.71118, 23.22955, -3.85788, 23.54568, 0.03195, 8.09784, -22.63885, 10.54041, -21.60892, -5.12971, -3.99405, -5.69397, -1.41351, -5.38486, -2.33264, -4.30753, 3.63892, -4.68542, 3.14232, -5.06863, 2.47021, 11.59064, -12.92429, 7.52628, -9.84689, 9.04575, -8.46695, -5.86584, -12.43678, -4.46603, -13.00716, -2.63141, -13.4957, -3.694, 17.3992, 0.1402, 11.9455, -1.83768, 11.80942, 11.31055, 6.50639, 10.52008, 7.7126, 9.37036, 9.08778, 20.86799, -0.41705, 18.1098, -3.69385, 18.46893, -0.65179, 5.6795, -18.00662, 7.62665, -17.27147, 9.92944, -16.05905, 9.3031, -5.31271, 7.15491, -5.09558, 7.89691, -3.84433, -1.11179, -9.01057, -0.11346, -9.07829, 1.13641, -9.00722, -8.28052, 5.13211, -9.01877, 3.69476, -4.49506, -7.07068, -3.26959, -7.71527, -3.36349, 8.67093, -4.75369, 7.9967, 5.44272, 8.16107, 4.27393, 8.83325, 1.41348, -2.86838, 1.86279, -2.59528, 10.37351, -14.14951, 5.26019, -11.40097, 7.06607, -10.37357, -5.76678, -12.33601, -4.00958, -13.0092, 9.46927, -21.81934, 12.93887, -19.95364, -12.21298, -22.27802, -9.03055, -23.74254, 11.44971, -31.60699, 3.06464, -23.32068, 6.8743, -22.49265, -19.18631, -15.8956, -17.3197, -17.91272, -14.69269, -20.12178, 11.44971, -31.60699, 3.06464, -23.32068, 6.8743, -22.49265, -17.3197, -17.91272, -14.69269, -20.12178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.25784, -4.14468, 5.58136, -3.9754, 6.16089, -2.99908, -0.86761, -7.02934, -0.08829, -7.08218, 0.88608, -7.02696, 7.66267, -1.45377, 6.41898, -2.2316, 6.69635, -1.14069, 1.90762, -6.69246, 2.81284, -6.36575, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12305, -1.91718, 0.65326, 4.46077, 0.03748, 4.50912, 0.53088, 11.13736, -0.84747, 16.76517, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -11.61739, 13.04155, 17.24823, -2.74613, 2.39094, -12.0401, -1.96376, 13.1994, 7.05392, 10.04645, -10.19425, -6.06895, 10.59024, 6.18152, 11.43188, -3.177], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "offset": 28, "vertices": [4.81635, 4.03278, 4.08973, 6.98863, 4.08368, 4.7729, 4.81635, 4.03278, 4.08973, 6.98863, 4.08368, 4.7729, 1.81685, -0.79536, -3.07484, 2.30527, 3.03448, -2.6353, 0.45804, -3.81575, -4.04952, 4.86343, 3.92578, -5.45894, 6.50895, -3.6322, -0.71785, -6.28789, -4.64182, 6.33769, 4.47052, -7.08744, 7.86512, -5.10226, -1.37448, -7.73468, -2.17976, 2.66707, 2.11126, -2.99196, 3.52969, -2.01573, -0.42204, -3.4186, 9.40305, -12.75404, 15.65324, -7.70724, -0.28726, -14.92404, 9.40305, -12.75404, -0.28726, -14.92404, -0.14514, -7.53931, -0.0654, -3.3988, 0.13809, -3.39665, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.31763, -8.00391, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, 8.45557, -5.32651, 7.22429, -8.86716, 4.36639, 3e-05, 8.45557, -5.32651, 7.22429, -8.86716, 4.36639, 3e-05, 8.45557, -5.32651, 7.22429, -8.86716, 4.36639, 0, 0, 0, 0, 0, 0, 7.76924, 2.90652, 4.51934, 7.62509, 0.25939, 11.24495, 7.15267, -0.76954, 6.27256, 4.02733, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.89591, -4e-05, 3.01654, 2.4655, 8.43547, -5.87927, -10.25203, -0.78563, 6.35841, -1.4049, -5.81231, -2.93584, 6.35841, -1.4049, -5.81231, -2.93584, -5.81231, -2.93584, 6.01783, 2.95381, 6.13852, -2.17347, 1.08638, 7.49517, 3.90196, -6.49087, 6.35841, -1.4049, -5.81231, -2.93584, 6.35841, -1.4049, -5.81231, -2.93584, -5.81231, -2.93584, 6.01783, 2.95381, 6.13852, -2.17347, -4.31355, -0.02538, 4.38288, -0.15074, 2.99887, -3.10093, -2.55455, 3.88049, 2.4451, -4.32762, 4.53635, -3.291, -1.04021, -4.52799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.14066, -2.32638, 2.11561, -0.2947, 7.14581, -4e-05, -5.53294, -4.52192, -2.78527, -2.27633, 2.91735, 2.36144, 3.57007, -0.44078, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.09685, 5.31426, -9.01294, -0.0427, -1.82174, 2.06747, 2.71887, -0.44777, -2.74473, 0.60008, 1.70335, 1.66878, 0.72431, 2.90189, 2.27049, -0.06677, -5.99724, -1.96718, 6.16827, 1.8923, 4.78238, 5.90421, 5.56625, -2.97662, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.52304, 1.32527, -7.72461, -4.44395, -4.91067, -9.69399, -7.82308, 3.29645, 8.14339, 2.39827, -8.36513, -2.27219, -6.63888, -7.68056, -3.59152, -2.5848, 1.14529, 4.27441, -1.32862, -4.60283, 1.15136, -5.92432, 3.67158, -3.1146, 5.05197, -1.18884, 0.60205, -4.5685, -8.43606, 0.68639, 8.5432, -1.0957, 8.55657, 4.05551, 5.3329, -6.57316, -6.646, 5.69935, -8.88342, 0.5878, -8.61103, -4.8284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.53461, 1.51636, 8.61123, -2.00275, 9.09187, 3.09151, 4.80106, -7.21786, -9.81831, -1.0502, -7.31906, -7.3576, -3.01627, -12.67534, -7.25219, 2.54306, -7.44041, -2.55165, -5.62921, -7.42014, -6.23215, -4.07158, -6.88533, 3.41213, -7.22116, 2.63265, 4.64951, -6.30669, 7.73987, -3.81079, 4.85452, -4.6311, -0.14175, -7.37947, 0.29977, -7.37495, 16.81321, -13.86639, 23.12009, -4.47566, 17.31526, -8.14037, 4.0286, -20.71921, -9.00274, 10.49684, 8.68724, -11.8363, 14.4676, -7.23753, -0.41214, -13.8158, -10.21162, -2.80513, -6.56238, -9.14021, -1.38182, -14.18543, -3.74521, -9.31754, -10.478, -1.53306, -10.24617, -2.67806, -9.78032, -4.06005, -2.64264, -10.26779, -4.14023, -6.47577, -3.01602, -7.07056, -8.05409, 0.30061, -8.04021, -0.58846, -7.88098, -1.68673, 7.81741, -10.60365, 13.01341, -6.40704, 8.16223, -7.78665, 9.42642, -6.19843, -0.23831, -12.40732, 0.50415, -12.39992, 1.005, -12.36928, 14.40258, -9.93356, 18.73286, -1.84723, 14.90416, -5.02953, 4.36739, -16.44646, 0.05685, -14.13245, -2.48084, -9.51723, -0.87706, -9.79602, -10.02811, -2.75919, -9.66521, -3.84732, -9.04245, -5.13789, -1.38513, -6.69736, -2.52597, -4.3578, -1.76472, -4.70627, -5.1218, -0.09465, -5.11763, -0.66151, -5.04843, -1.36722, 13.2562, 0.35559, 11.61478, -1.92894, 11.77284, 0.01598, 4.04892, -11.31943, 5.2702, -10.80446, 4.3283, -2.41124, 3.22002, -2.40865, 3.58057, -1.83488, -0.43947, -4.18073, -0.01343, -4.20681, 0.49435, -4.17774, 11.53159, -2.75104, 9.66052, -3.55525, 10.13127, -1.93106, 1.29581, -10.52703, 2.48431, -10.2206, 3.926, -9.58517, 8.43581, 8.18039, 8.75485, 3.83929, 7.92639, 5.3461, 8.5145, -5.26112, 8.8143, -4.74297, 8.99762, -4.38242, 15.34742, -3.01447, 12.83295, -4.53819, 13.40524, -2.3562, 2.25224, -13.76187, 3.75371, -13.43004, 5.56427, -12.78659, 9.56497, -5.4623, 7.3555, -5.23906, 8.11923, -3.95247, -1.1434, -9.26384, -0.11635, -9.33345, 1.16776, -9.26067, -4.26117, 3.44891, -4.80853, 2.63034, -2.27484, -4.99417, -1.34238, -5.32056, -1.77213, 6.02289, -2.82077, 5.60873, 3.17606, 5.71115, 2.94171, 5.83478, 0.74426, -1.97778, 1.0878, -1.81204, 2.72652, -6.15567, 1.49506, -4.84335, 2.33939, -4.49689, -3.20618, -3.88464, -3.047, -4.01149, 3.76419, -12.11829, 5.87466, -11.24796, -6.94077, -10.89406, -6.49524, -11.16615, 5.0225, -17.85153, 0.48553, -13.26862, 2.85364, -12.96732, -10.90131, -8.25748, -10.38766, -8.89523, -10.01941, -9.30823, 5.0225, -17.85153, 0.48553, -13.26862, 2.85364, -12.96732, -10.38766, -8.89523, -10.01941, -9.30823, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21.26301, -10.75523, 13.85117, -12.54071, 15.853, -9.89993, -0.75781, -20.32752, 0.63899, -20.34335, 1.80424, -20.27422, 10.81917, -4.51324, 6.974, -5.93115, 7.9259, -4.58536, 0.97922, -10.092, 1.38538, -10.04443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.91074, -5.57372, -0.18823, 0.36569, -0.20312, 0.35696, 0.28259, 6.59775, -0.99683, 11.90828, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.80869, 6.52078, 8.62412, -1.37307, 1.19547, -6.02005, -0.98188, 6.5997, 3.52696, 5.02322, -5.09712, -3.03447, 5.29512, 3.09076, 5.71594, -1.5885], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667}]}, "h1": {"h1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [-16.11966, -0.79266, -23.36046, -1.14941, -23.37073, 0.91766, -23.36046, -1.14941, -23.71884, 0.93106, -23.70848, -1.16705, -24.39105, 0.95764, -24.38039, -1.1998, -23.19241, 8.25659, -23.83138, 6.17642, -11.01917, 12.789, -12.10606, 11.76636, -11.01666, 10.98895, -11.94456, 9.97357, 0, 0, 0, 0, 9.98517, -2.95142, 10.20621, -2.05685, 9.98517, -2.95142, 10.20621, -2.05685, 9.98517, -2.95142, 9.98517, -2.95142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.28638, -11.91313, 3.82999, -11.51117, 2.28638, -11.91313, 3.82999, -11.51117, 3.7677, -7.18226, 4.67758, -6.62625, -10.99716, -7.7637, -10.99716, -7.7637, -6.71808, -4.74315, -6.71808, -4.74315, -9.3837, 1.26175, -8.14651, -5.75146, -8.14651, -5.75146, -8.14651, -5.75146, -10.55981, 2.85812, -10.55981, 2.85812, 2.94153, 8.89732, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.71246, -5.59494, -2.76636, -6.11772, -9.63062, -7.81157, -8.25006, -9.25894, -16.96765, -10.56662, -15.04797, -13.15927, -24.83118, -13.51617, -22.33362, -17.33629, -23.98145, -16.31861, -20.26105, -6.73463, -18.91565, -9.90773, -13.62335, -0.66541, -13.34247, -2.84902, -9.76807, 2.85818, -10.10474, 1.24974, -5.6319, 6.63853, -6.63068, 5.64685, 2.47571, 9.9424, 0.83789, 10.21362, -15.96948, 35.42601, -13.60858, 36.04301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -15.33124, -9.95435, -14.47314, -9.16495, -5.52106, -1.01477, 0, 0, 0, 0, 1.6416, 4.53958, 1.54251, 4.57401, -8.78168, 5.25635, -8.89365, 5.06506, -8.93323, 4.99588, 0, 0, 0, 0, 0, 0, 0.17639, 2.55249, 0.12085, 2.55533, 0.10049, 2.55685, 0.12085, 2.55533, 0.10049, 2.55685, 0.12085, 2.55533, 0.10049, 2.55685, 0, 0, 0, 0, 0, 0, -13.06625, -11.47794, 0, 0, -13.06625, -11.47794, -14.19987, -8.96036, -22.61026, -0.48325, -17.1916, 3.32089, -7.07312, -6.95526, -7.52808, -6.45306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.33746, -1.88068, -4.96796, -2.71367, -15.92596, -7.7256, -14.47729, -10.18747, -10.32928, -4.63435, -9.45129, -6.23479, 0.34814, 0.9326, 0.19092, 0.97838, 0.06226, 0.9924, 0, 0, 0, 0, -2.59204, 0.17828, -2.59604, 0.12234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.31268, 3.75026, 0, 0, -4.08139, -2.88168, -10.34198, 0.71112, 0, 0, 0, 0, -6.9621, 0.47873, -6.97089, 0.32813, 0, 0, 0, 0, -8.78168, 5.25635, -8.89365, 5.06506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.04056, 0.63873, -3.85953, -0.18954, -1.49084, 2.80493, -1.73323, 2.6629, -4.47504, -0.22025, -4.02203, 0.15799, -4.02029, -0.19772, -6.04309, 0.23734, -6.04048, -0.29712, -6.04309, 0.23734, -6.04048, -0.29712], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "f2": {"f2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 60, "vertices": [-3.81442, -9.80705, 1.84084, -10.35954, -5.96814, 2.07706, -6.17841, -1.32858, -5.9767, 8.01033, -6.65154, 7.45956, -9.56098, 2.91331, -5.1897, 9.22191, -5.97303, 8.73488, -9.64442, 4.35567, -0.89543, 6.50714, -1.45864, 6.40475, -4.5763, 4.71181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.91644, 6.65935, -1.49276, 6.5544, -4.68328, 4.82193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.06535, 1.28401, -2.1692, 1.0993, -2.42494, -0.18826, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.54076, -3.23952, 0.36802, -3.5674, 0, 0, -2.78518, -3.15773, -0.73741, -4.14471], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"s1": [{"vertices": [-0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 6, "vertices": [4.60098, -2.9898, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.25162, 0.78291, 6.07818, -9.50053, 13.8576, -1.39973, 5.41093, -12.83345, 13.52489, -3.13816, 3.72567, -13.37442, 13.17516, -5.04919, 1.88062, -13.98309, 12.55273, -8.2169, -1.19812, -14.9543, 11.93788, -10.81832, -3.77603, -15.66096, 0, 0, 1.64775, 21.81484, 9.09932, 19.90666, 14.18759, 18.60388, 18.50876, 13.32966, 25.18823, 5.17757, 18.83716, 0.5301, 12.93283, -4.26934, 8.36287, -9.85889, 9.99199, -4e-05, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 0, 0, 0, 0, 0, 0, 0, 0, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 3.52248, -0.24394, 1.99838, 0, 0.95744, -1.75406, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.15442, 8.95717, 11.07893, -1.23492, 0, 0, 3.74574, -2.47261, 8.07109, -4.97454, 5.13541, 13.6712, 0, 0, 0, 0, 5.12987, -1.2299, 1.37848, -5.09126, 0, 0, 0, 0, 6.3732, 1.04915, 0, 0, 0, 0, 7.46599, 3.38836, 0, 0, 0, 0, 7.64313, 6.18169, 9.08814, -3.74643, 2.91034, -0.84226, 0.65528, -2.9575, 8.67813, 9.46908, 12.46967, -3.07991, 3.32173, 1.38828, 2.81021, -2.24998, 9.20575, 12.81395, 15.6583, -1.94051, 3.59322, 3.34555, 4.6583, -1.55054, 9.80138, 15.69759, 18.47486, -1.0818, 4.05983, 5.43895, 6.71944, -0.9572, 10.56796, 17.48695, 20.41274, -0.89725, 4.05266, 6.9186, 8.01473, -0.24191, 4.49475, 8.53014, 9.64108, 0.14198, 4.77304, 9.70306, 10.80394, 0.45966], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "vertices": [-0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472, -0.7867, 2.65916, 1.95714, 1.96472]}]}, "st1": {"st1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 5, "vertices": [-0.70214, -0.0004, -0.70207, -2.6205, 5.9937, -2.62161, 5.99403, -0.6505, 3.15085, -0.65054, 3.15087, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.73656, -6.97916, -4.60796, -4e-05, -4.2684, -0.99168, -7.70205, -2.34566, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.1322, -4.12411, 2.81378, 5.95129, 3.81982, 5.36023, -7.28677, -2.52001, 5.45233, 5.44852, 6.32852, 4.40048, -9.81573, -0.69661, 8.52362, 4.91193, -7.94626, -0.66193, 6.85693, 4.06457, 7.46707, 2.79065, -5.63208, -0.60207, 4.80074, 3.00053, 5.25604, 2.10574, -3.70901, -0.50468, 3.11331, 2.07365, 3.43121, 1.49036, 3.09436, 2.10123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.60895, 4.77101, 14.65332, 7.27562, 15.23492, 2.73363, 39.38852, 6.59856, 37.48425, 13.78067, 23.8074, 6.70531, 22.15314, 10.99919, 14.42923, 4.06383, 13.42657, 6.66652, 15.89737, 9.86792, 17.39044, 6.90834, 15.80762, 10.01158, 10.91478, 4.64793, 11.56567, 2.64821, 10.87268, 4.74677, 4.39694, -2.11562, 3.95786, -2.8609, 4.41699, -2.0762, 1.66248, 10.12729, 2.91905, -9.83612, 1.14206, -10.20016, 3.00903, -9.81026, -11.99805, 10.26871, 15.2751, -4.00134, 14.33124, -6.63206, 15.31097, -3.8624, -11.99805, 10.26871, 15.2751, -4.00134, 14.33124, -6.63206, -11.99805, 10.26871, 15.2751, -4.00134, 14.33124, -6.63206, -11.99805, 10.26871, 15.2751, -4.00134, 14.33124, -6.63206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.64743, -10.8296, -2.64948, -10.82888, 0, 0, 0, 0, 0, 0, -4.11298, -8.55957, -4.11249, -8.55935, -4.11441, -8.55856, 0, 0, 0, 0, 0, 0, -5.23628, -6.52802, -5.23587, -6.52783, -5.23785, -6.52705, -4.73656, -6.97916, -6.78756, -3.98904, -6.78694, -3.98887, -6.78888, -3.98811, -7.71373, -2.86317, -7.56087, -2.28796, -7.56039, -2.2877, -7.56256, -2.28698, -6.30481, -3.31882, -8.32767, -0.54245, -8.32726, -0.54221, -8.32938, -0.54147, -0.00013, -2.49543, -8.72998, 1.28062, -8.73184, 1.28136, 0, 0, 0, 0, 0, 0, -9.2758, 3.13352, -9.27768, 3.13428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.68243, 0.45691, 1.7052, 0.32457, 1.73883, 0.01465, 0, 0, -2.00085, 0.00378, 1.79446, 0.87125, 1.92255, 0.53708, 0, 0, -3.01041, -4.84602, 0.58022, 5.67628, 1.57578, 5.47725, -3.10013, 3.91013, 4.48885, -2.16319, 4.03964, -2.92462, 0, 0, 0.46927, -2.44454, -1.50299, 1.99622, -1.12177, 2.22321, -4.85175, 4.29923, 6.23363, -1.74865, 5.83038, -2.82506, 0, 0, 0, 0, 2.22183, -4.50084, -3.97858, 3.08195, -3.36749, 3.7261, -10.89435, 3.71319, 11.41193, 1.4166, 0, 0, 0, 0, -12.24771, -15.54162, 4.2318, 19.32983, 7.57355, 18.27919, -8.49271, 2.97371, -7.83481, 4.4221, -0.59369, -6.07892, -2.12276, 5.72887, -1.07843, 6.01138, -11.12393, -12.61645, 4.49763, 16.20744, 7.28482, 15.15887, 4.34979, 16.24736, 18.06326, -8.72038, -20.06845, -0.0375, -1.4032, -3.07584, -0.08614, 3.38065, 0.51291, 3.3396, 2.92145, 12.7138, 5.11789, 11.99785, 2.80548, 12.73929, 10.63525, -12.78865, -15.15225, 6.86411, -13.70514, 9.42613, 2.30219, 0.45194, -1.87752, -1.41112, -2.09549, -1.05988, -1.86383, -1.4288, -0.90585, -0.09522, 0.77005, 0.48158, 0.84409, 0.33633, 0.76599, 0.48792, 1.89772, 9.39421, 3.52475, 8.91034, 1.81189, 9.41008, -13.70514, 9.42613, -15.21448, 6.72505, 0, 0, 0, 0, 0, 0, 0, 0, 0.52753, 7.59157, -0.88885, 7.55914, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.70514, 9.42613, -15.21448, 6.72505, 0, 0, 0, 0, 0, 0, -15.21448, 6.72505, 0, 0, 0, 0, 0, 0, -15.21448, 6.72505], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t1": {"t1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 136, "vertices": [-2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, 3.4231, -2.42249, 0, 0, 0, 0, 3.83218, -4.42615, 3.9682, -4.38232, -3.90903, 9.26004, -3.90884, 9.26009, 7.17783, -6.88699, 0, 0, 0, 0, -1.65955, 5.20176, -1.65933, 5.20176, 3.53769, -4.0856, -3.771, 8.82652, -3.77072, 8.82654, 6.88333, -6.54645, 0, 0, 0, 0, 0, 0, 0.06335, -6.18837, -2.44611, 5.63391, -1.10614, 3.46787, -1.10608, 3.46789, 2.35822, -2.72382, -2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, -2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.96808, 3.03439, -0.96793, 3.03442, 2.06335, -2.38339, 0, 0, 0, 0, 0, 0, 1.24426, -3.90136, -2.65338, 3.06412, -0.59052, -1.14347, 0.10358, 1.28482, 1.24426, -3.90136, -2.65338, 3.06412, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, 0, 0, 0, 0, -0.59052, -1.14347, 0.10358, 1.28482, -2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.33221, -0.74385, -2.33221, -0.74385, -2.33221, -0.74385, 0.11789, 1.64362, 2.33218, 0.74387, 2.33221, 0.74387, 2.33218, 0.74387, 2.33221, 0.74387], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "offset": 136, "vertices": [-1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, 2.79165, -1.97562, 0, 0, 0, 0, 3.12528, -3.60967, 3.2362, -3.57393, -3.18794, 7.55188, -3.1878, 7.55192, 5.85376, -5.61658, 0, 0, 0, 0, -1.35342, 4.24222, -1.35324, 4.24222, 2.88511, -3.33195, -3.07538, 7.19833, -3.07515, 7.19834, 5.61359, -5.33885, 0, 0, 0, 0, 0, 0, 0.05167, -5.04683, -1.99488, 4.59465, -0.90209, 2.82817, -0.90205, 2.82818, 1.9232, -2.22136, -1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, -1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7895, 2.47465, -0.78938, 2.47468, 1.68274, -1.94374, 0, 0, 0, 0, 0, 0, 1.01474, -3.18169, -2.16392, 2.49889, -0.48159, -0.93254, 0.08447, 1.04781, 1.01474, -3.18169, -2.16392, 2.49889, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, 0, 0, 0, 0, -0.48159, -0.93254, 0.08447, 1.04781, -1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.902, -0.60664, -1.902, -0.60664, -1.902, -0.60664, 0.09614, 1.34043, 1.90198, 0.60665, 1.902, 0.60665, 1.90198, 0.60665, 1.902, 0.60665], "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 1.9, "offset": 136, "vertices": [-1.27025, 2.18065, -1.27021, 2.18066, 2.01274, -1.48044, 2.05933, -1.45737, 0, 0, 0, 0, 2.30544, -2.66277, 2.38727, -2.6364, -2.35167, 5.57083, -2.35156, 5.57086, 4.31817, -4.14321, 0, 0, 0, 0, -0.99838, 3.12938, -0.99825, 3.12938, 2.12827, -2.45789, -2.26863, 5.31003, -2.26846, 5.31004, 4.14101, -3.93834, 0, 0, 0, 0, 0, 0, 0.03811, -3.72292, -1.47158, 3.38936, -0.66545, 2.08627, -0.66542, 2.08628, 1.4187, -1.63865, -5.41212, 0.90513, -5.41154, 0.90507, 5.31883, 1.36373, -1.27025, 2.18065, -1.27021, 2.18066, 2.01274, -1.48044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.5824, 1.82549, -0.5823, 1.82551, 1.24131, -1.43385, 0, 0, 0, 0, 0, 0, 0.74855, -2.34705, -1.59627, 1.84337, -0.35525, -0.68791, 0.06231, 0.77295, 0.74855, -2.34705, -1.59627, 1.84337, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.27025, 2.18065, -1.27021, 2.18066, 2.01274, -1.48044, 0, 0, -4.51794, -1.39154, -2.23666, -1.26764, 1.56439, 2.06592, -5.60051, 0.84714, -5.60017, 0.84711, 5.4694, 1.4927, -3.8183, -1.17599, -6.57617, -2.02535, 0, 0, -3.01178, -0.92766, -3.76511, -1.15962, -2.63562, -0.81172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.20032, -0.98567, -7.53842, -2.32174, -2.25867, -0.69576, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.77658, -7.73413, -9.35632, -5.52579, -4.24133, -1.30658, 0, 0, 0, 0, 0, 0, 2.91809, 6.59249, 2.91895, 6.59265, 3.09933, 5.25436, 0, 0, -8.27438, -5.65116, -13.61789, -6.1837, -7.52054, -4.69357, 2.1752, 5.9373, 6.20456, 6.49424, 6.77966, 3.55725, 3.56979, 0.96658, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.40306, -0.4475, -1.40306, -0.4475, -1.40306, -0.4475, 0.07092, 0.9888, 1.40304, 0.44751, 1.40306, 0.44751, 1.40304, 0.44751, 1.40306, 0.44751], "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 2.1667, "offset": 136, "vertices": [-0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, 1.16064, -0.82137, 0, 0, 0, 0, 1.29935, -1.50074, 1.34546, -1.48588, -1.3254, 3.13972, -1.32534, 3.13974, 2.43372, -2.33511, 0, 0, 0, 0, -0.56269, 1.76372, -0.56262, 1.76372, 1.19949, -1.38527, -1.2786, 2.99273, -1.27851, 2.99274, 2.33387, -2.21965, 0, 0, 0, 0, 0, 0, 0.02148, -2.09824, -0.82938, 1.91024, -0.37505, 1.17582, -0.37503, 1.17583, 0.79958, -0.92354, -0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, -0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.32824, 1.02884, -0.32819, 1.02886, 0.6996, -0.80812, 0, 0, 0, 0, 0, 0, 0.42188, -1.3228, -0.89966, 1.03892, -0.20022, -0.38771, 0.03512, 0.43563, 0.42188, -1.3228, -0.89966, 1.03892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, 0, 0, 0, 0, -0.20022, -0.38771, 0.03512, 0.43563, -0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.79076, -0.25221, -0.79076, -0.25221, -0.79076, -0.25221, 0.03997, 0.55729, 0.79075, 0.25222, 0.79076, 0.25222, 0.79075, 0.25222, 0.79076, 0.25222], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.6667}]}}}}, "animation2": {"slots": {"h2": {"attachment": [{"name": null}]}, "bg": {"attachment": [{"name": null}]}, "d4": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"name": null}]}, "sj1": {"attachment": [{"name": null}]}, "h4": {"attachment": [{"name": null}]}, "h3": {"attachment": [{"name": null}]}, "d5": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"name": null}]}}, "bones": {"bone": {"translate": [{"curve": 0.165, "c2": 0.15, "c3": 0.75}, {"time": 1.3333, "y": -10, "curve": 0.223, "c2": 0.01, "c3": 0.765, "c4": 0.82}, {"time": 2.6667}]}, "s2": {"rotate": [{"angle": -1.21, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -3.6, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.21}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.76, "y": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s3": {"rotate": [{"angle": -2.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.94}]}, "s5": {"rotate": [{"angle": 4.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 8.4, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 4.84}]}, "st2": {"translate": [{"x": -7.71, "y": -5.24, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "x": 9.02, "y": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -8.83, "y": -5.1, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": -7.71, "y": -5.24}]}, "t1": {"rotate": [{"angle": 1.42, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.42}], "translate": [{"x": -1.26, "y": -0.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -3.76, "y": -1.36, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": -1.26, "y": -0.46}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.85, "y": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st1": {"rotate": [{"angle": -0.08, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -1.2, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.08}]}, "s1": {"rotate": [{"angle": 0.38, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.38}]}, "f19": {"rotate": [{"angle": -3.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.04}]}, "f20": {"rotate": [{"angle": 0.71, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": -0.64, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 12.46, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": 0.71}]}, "f21": {"rotate": [{"angle": 3.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": -0.64, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 12.46, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 3.35}]}, "f22": {"rotate": [{"angle": 6.35, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "angle": -0.64, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 12.46, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": 6.35}]}, "f13": {"rotate": [{"angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.91}]}, "f14": {"rotate": [{"angle": -1.31, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 5.3, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -1.31}]}, "f15": {"rotate": [{"angle": 0.71, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 8.9, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 0.71}]}, "f16": {"rotate": [{"angle": 3.83, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 11.3, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "angle": 3.83}]}, "f17": {"rotate": [{"angle": 4.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 7.7, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 4.17}]}, "f18": {"rotate": [{"angle": 8.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 11.3, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 8.87}]}, "f2": {"rotate": [{"angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.98}]}, "f3": {"rotate": [{"angle": 0.86, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -15.88, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": 0.86}]}, "f4": {"rotate": [{"angle": -3.6, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -13.48, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "angle": -3.6}]}, "f5": {"rotate": [{"angle": -15.68, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -23.08, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": -15.68}]}, "f6": {"rotate": [{"angle": 2.06, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": 2.69}, {"time": 1.8333, "angle": 0.82, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.06}], "translate": [{"x": 0.64, "y": 0.2, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.9, "y": 0.6, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.64, "y": 0.2}]}, "f7": {"rotate": [{"angle": -1.2, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 0.5, "angle": 2.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6667, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.2}]}, "f8": {"rotate": [{"angle": -2.51, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "angle": 1.24, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -5.18, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -2.51}]}, "f9": {"rotate": [{"angle": -3.73, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 0.05, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.73}]}, "f10": {"rotate": [{"angle": -2.51, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "angle": 1.24, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -5.18, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -2.51}]}, "f11": {"rotate": [{"angle": -3.73, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 0.05, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.73}]}, "f12": {"rotate": [{"angle": -4.72, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.5, "angle": -1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -5.18, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -4.72}]}, "h1": {"rotate": [{"angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.65}]}, "h2": {"rotate": [{"angle": -1.5, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 0.77, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -1.5}]}, "h3": {"rotate": [{"angle": -1.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.77, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.21}]}, "h4": {"rotate": [{"angle": -0.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 0.77, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.84}]}, "h5": {"rotate": [{"angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 0.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -1.34}]}, "h6": {"rotate": [{"angle": -0.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 0.77, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.76}]}, "h7": {"rotate": [{"angle": -0.12, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 0.77, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -0.12}]}, "h8": {"rotate": [{"angle": 0.45, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 0.77, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.45}]}, "h9": {"rotate": [{"angle": 2.35, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 7.01, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.35}]}, "h10": {"rotate": [{"angle": 5.72, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 2.35, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 7.01, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 5.72}]}, "h11": {"rotate": [{"angle": 6.57, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": 7.01, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": 5.72, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 6.57}]}, "h13": {"rotate": [{"angle": -9.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -9.05}]}, "h14": {"rotate": [{"angle": -7.45, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -9.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.37, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -7.45}]}, "h15": {"rotate": [{"angle": -4.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -9.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -4.71}]}, "st4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.18, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st5": {"rotate": [{"angle": -1.07, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.18, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.07}]}, "st6": {"rotate": [{"angle": -2.6, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.6}]}, "bone3": {"rotate": [{"angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.92, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.4}], "translate": [{"y": 8, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -22, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 8}], "scale": [{"x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.02, "y": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.08}]}, "bone4": {"rotate": [{"angle": -1.05, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -0.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -1.28, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.05}]}, "s16": {"rotate": [{"angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.96}]}, "s17": {"rotate": [{"angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.71}]}, "s18": {"rotate": [{"angle": 3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.88, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 3.25}]}, "t3": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t4": {"shear": [{"x": 1.12, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2, "x": 4.89, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 2.6667, "x": 1.12}]}, "d6": {"translate": [{"x": 0.88, "y": -1.18, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.79, "y": -6.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.88, "y": -1.18}], "shear": [{"x": 18.82, "y": -2.43, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 102, "y": -13.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 18.82, "y": -2.43}]}, "d7": {"shear": [{"x": 0.75, "y": -0.53, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 12, "y": -8.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 0.75, "y": -0.53}]}, "d8": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 12, "y": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "d9": {"translate": [{"x": 0.09, "y": -0.37, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.41, "y": -5.83, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 0.09, "y": -0.37}], "shear": [{"x": -5.05, "y": 1.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -80.4, "y": 21.6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": -5.05, "y": 1.36}]}, "d10": {"shear": [{"x": -0.98, "y": -0.68, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -15.6, "y": -10.8, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": -0.98, "y": -0.68}]}, "d11": {"shear": [{"x": -2.88, "y": -1.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -15.6, "y": -10.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -2.88, "y": -1.99}]}, "d1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.59, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "d2": {"rotate": [{"angle": 0.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 0.78, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": 0.1}]}, "d3": {"rotate": [{"angle": 1.48, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.02, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 1.48}]}, "d4": {"rotate": [{"angle": 18.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 28.96, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 18.31}]}, "bone7": {"translate": [{"x": 6.64, "y": -5.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "y": -14, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 36, "y": 32, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 40, "y": 24, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 6.64, "y": -5.52}]}, "s6": {"rotate": [{"angle": -8.75, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -26.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -8.75}]}, "s9": {"rotate": [{"angle": -8.75, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -26.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -8.75}]}, "s11": {"rotate": [{"angle": -8.75, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -26.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -8.75}]}, "s13": {"rotate": [{"angle": -8.75, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -26.05, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -8.75}]}, "s4": {"rotate": [{"angle": -0.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "angle": -12.33, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.77}]}, "s8": {"rotate": [{"angle": -0.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "angle": -12.33, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.77}]}, "s10": {"rotate": [{"angle": -0.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "angle": -12.33, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.77}]}, "s12": {"rotate": [{"angle": -0.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -12.33, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.77}]}, "f1": {"scale": [{"x": 1.011, "y": 1.011, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": 1.06, "y": 1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.011, "y": 1.011}], "shear": [{"x": 0.66, "y": -1.11, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": 3.6, "y": -6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.66, "y": -1.11}]}, "h16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.11, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "h17": {"rotate": [{"angle": -0.27, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -2.11, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -0.27}]}, "h18": {"rotate": [{"angle": -0.78, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -2.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.78}]}, "h19": {"rotate": [{"angle": -1.33, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.11, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -1.33}]}, "h20": {"rotate": [{"angle": -0.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -3.52, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.65}]}, "h21": {"rotate": [{"angle": -1.16, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.3333, "angle": -0.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.52, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.16}]}, "h22": {"rotate": [{"angle": -1.75, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -0.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.75}]}, "h23": {"rotate": [{"angle": -1.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -3.52, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.18}]}, "h24": {"rotate": [{"angle": -2.21, "curve": 0.338, "c2": 0.35, "c3": 0.692, "c4": 0.75}, {"time": 0.5, "angle": -0.55, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -3.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -2.21}]}, "h26": {"rotate": [{"angle": -1.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.52, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.18}]}, "bone6": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s15": {"rotate": [{"angle": 6.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 13.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 6.6}]}, "s14": {"rotate": [{"angle": -2.4, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -7.14, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -2.4}], "scale": [{"x": 0.985, "y": 0.952, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.92, "y": 0.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.985, "y": 0.952}]}, "s7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "scale": [{"x": 0.996, "y": 0.982, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.98, "y": 0.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.996, "y": 0.982}]}, "bone8": {"translate": [{"x": 42.2, "y": -189.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": 367.14, "y": 93.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 42.2, "y": -189.9}]}, "d13": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 37.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "d5": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.2, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}}, "deform": {"default": {"f1": {"f1": [{"offset": 110, "vertices": [-0.31888, 2.16459, -1.47206, 1.61873, -1.15895, 1.85575, -0.23616, 1.6924, -1.14, 1.27301, -0.89405, 1.45623, -0.11817, 1.23868, -0.78896, 0.96227, 0.00428, 0.854, -0.47271, 0.71134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37018, 0.79014, -0.74783, 0.44946, 0, 0, 0, 0, -0.14857, 1.20613, -0.79599, 0.91831, 0, 0, 0, 0, 0, 0, -0.06079, 1.00386, -0.61026, 0.79944, 0, 0, 0, 0, 0, 0, -0.08954, 1.06616, -0.6689, 0.83512], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 54, "vertices": [7.32979, -9.91063, 6.79585, -15.37915, 0.17842, -15.21631, -3.49515, -15.12575, -6.95743, -15.04065, -10.48529, -12.36671, -13.12208, -7.20966, -7.60777, -5.26379, -8.90185, -2.52278, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.72865, 11.73436, -7.98011, 8.77524, -6.28273, 10.06012, -1.28024, 9.1746, -6.17998, 6.90103, -4.84669, 7.89429, -0.64062, 6.71493, -4.27698, 5.21649, 0.02319, 4.62959, -2.56257, 3.85622, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.00676, 4.28339, -4.05403, 2.43652, 0, 0, 0, 0, -0.80539, 6.53849, -4.31512, 4.97821, 0, 0, 0, 0, 0, 0, -0.32954, 5.44199, -3.30827, 4.3338, 0, 0, 0, 0, 0, 0, -0.4854, 5.77973, -3.62616, 4.52724, 0, 0, 0, 0, 0, 0, 6.15784, 1.02318, 0, 0, 0, 0, 6.15784, 1.02318, 6.23805, 0.23116, 0, 0, 0, 0, 0, 0, 6.15784, 1.02318, 6.23805, 0.23116, 0, 0, 0, 0, 0, 0, 6.15784, 1.02318, 6.23805, 0.23116, 6.23805, 0.23116, 6.20043, 0.72633, 6.20043, 0.72633, 0, 0, 0, 0, 2.593, -3.33534, 1.37493, -3.99367, -0.23491, -9.01474, -3.39704, -11.35722, -7.17998, -9.70724, -9.9327, -6.86601, -8.79017, -10.39029, -6.42066, -11.83916], "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "offset": 110, "vertices": [-0.31888, 2.16459, -1.47206, 1.61873, -1.15895, 1.85575, -0.23616, 1.6924, -1.14, 1.27301, -0.89405, 1.45623, -0.11817, 1.23868, -0.78896, 0.96227, 0.00428, 0.854, -0.47271, 0.71134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37018, 0.79014, -0.74783, 0.44946, 0, 0, 0, 0, -0.14857, 1.20613, -0.79599, 0.91831, 0, 0, 0, 0, 0, 0, -0.06079, 1.00386, -0.61026, 0.79944, 0, 0, 0, 0, 0, 0, -0.08954, 1.06616, -0.6689, 0.83512]}]}, "h2": {"h2": [{"vertices": [0.47419, -4.21732, -1.57779, -6.67874, -2.02585, 1.75097, -2.02585, 1.75097, -2.7647, 2.38961, 1.11451, 2.26723, 1.83182, 2.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.56192, -2.38091, 2.15426, -1.86209, -0.68573, -3.86859, 0.39997, -3.90857, 0.39997, -3.90857, -0.65572, -0.75867, 0, 0, 0, 0, 1.07832, -1.64375, 1.48727, -1.28556], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "vertices": [1.41222, -12.55977, -4.69888, -19.89024, -6.03328, 5.21465, -6.03328, 5.21465, -8.23367, 7.11659, 3.31917, 6.75214, 5.45541, 6.31145, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.65161, -7.0907, 6.41568, -5.54558, -2.04221, -11.52121, 1.19116, -11.64029, 1.19116, -11.64029, -1.95282, -2.25941, 0, 0, 0, 0, 3.2114, -4.89532, 4.42931, -3.82857], "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "vertices": [0.47419, -4.21732, -1.57779, -6.67874, -2.02585, 1.75097, -2.02585, 1.75097, -2.7647, 2.38961, 1.11451, 2.26723, 1.83182, 2.11926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.56192, -2.38091, 2.15426, -1.86209, -0.68573, -3.86859, 0.39997, -3.90857, 0.39997, -3.90857, -0.65572, -0.75867, 0, 0, 0, 0, 1.07832, -1.64375, 1.48727, -1.28556]}]}, "s2": {"s2": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "offset": 28, "vertices": [4.81635, 4.03278, 4.08973, 6.98863, 4.08368, 4.7729, 4.81635, 4.03278, 4.08973, 6.98863, 4.08368, 4.7729, 1.81685, -0.79536, -3.07484, 2.30527, 3.03448, -2.6353, 0.45804, -3.81575, -4.04952, 4.86343, 3.92578, -5.45894, 6.50895, -3.6322, -0.71785, -6.28789, -4.64182, 6.33769, 4.47052, -7.08744, 7.86512, -5.10226, -1.37448, -7.73468, -2.17976, 2.66707, 2.11126, -2.99196, 3.52969, -2.01573, -0.42204, -3.4186, 4.94699, -7.01064, 8.27071, -4.72319, -0.98878, -8.01021, 4.94699, -7.01064, -0.98878, -8.01021, -0.57759, -4.67857, -2.13222, -2.28486, -1.86935, -2.50583, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.69684, -5.6767, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.06584, 13.24059, 6.7005, 8.75557, 5.16058, 9.74233, 11.73183, -2.09544, 12.40903, -0.49939, 12.3596, 1.20897, 6.65498, -0.70428, 5.5851, -0.88565, 5.65411, 0.04904, 2.27509, -6.13362, 3.45557, -5.55484, 4.18454, -5.02902, 2.55717, -3.28638, 3.9951, -0.98312, 3.85215, 0.89639, 3.43106, 0.64948, 3.27477, 1.20772, 2.61032, -3.41956, 3.48868, -2.82054, 3.8434, -2.31548, 2.55717, -2.98328, 3.8093, -0.71993, 3.54171, 1.07364, 3.19147, 0.8196, 3.49313, -2.51715, -1e-05, 3.03801, -1.8621, 2.63892, -3.11327, 1.77792, 1e-05, 8.52419, -5.15532, 7.11829, -8.05435, 4.59921, 1e-05, 8.52419, -5.15532, 7.11829, -8.05435, 4.59921, 2e-05, 5.48618, -3.29322, 4.47937, -4.94108, 2.82129, 0, 0, 0, 0, 0, 0, 7.76924, 2.90652, 4.51934, 7.62509, 0.25939, 11.24495, 7.15267, -0.76954, 6.27256, 4.02733, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.89591, -4e-05, 3.01654, 2.4655, 8.43547, -5.87927, -10.25203, -0.78563, 6.35841, -1.4049, -5.81231, -2.93584, 6.35841, -1.4049, -5.81231, -2.93584, -5.81231, -2.93584, 6.01783, 2.95381, 6.13852, -2.17347, 1.08638, 7.49517, 3.90196, -6.49087, 6.35841, -1.4049, -5.81231, -2.93584, 6.35841, -1.4049, -5.81231, -2.93584, -5.81231, -2.93584, 6.01783, 2.95381, 6.13852, -2.17347, -4.31355, -0.02538, 4.38288, -0.15074, 2.99887, -3.10093, -2.55455, 3.88049, 2.4451, -4.32762, 4.53635, -3.291, -1.04021, -4.52799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.14066, -2.32638, 2.11561, -0.2947, 7.14581, -4e-05, -5.53294, -4.52192, -2.78527, -2.27633, 2.91735, 2.36144, 3.57007, -0.44078, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.09685, 5.31426, -9.01294, -0.0427, -1.82174, 2.06747, 2.71887, -0.44777, -2.74473, 0.60008, 1.70335, 1.66878, 0.72431, 2.90189, 2.27049, -0.06677, -5.99724, -1.96718, 6.16827, 1.8923, 4.78238, 5.90421, 5.56625, -2.97662, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.52304, 1.32527, -7.72461, -4.44395, -4.91067, -9.69399, -7.82308, 3.29645, 8.14339, 2.39827, -8.36513, -2.27219, -6.63888, -7.68056, -3.59152, -2.5848, 1.14529, 4.27441, -1.32862, -4.60283, 1.15136, -5.92432, 3.67158, -3.1146, 5.05197, -1.18884, 0.60205, -4.5685, -8.43606, 0.68639, 8.5432, -1.0957, 8.55657, 4.05551, 5.3329, -6.57316, -6.646, 5.69935, -8.88342, 0.5878, -8.61103, -4.8284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.53461, 1.51636, 8.61123, -2.00275, 9.09187, 3.09151, 4.80106, -7.21786, -9.81831, -1.0502, -7.31906, -7.3576, -3.01627, -12.67534, -7.25219, 2.54306, -7.44041, -2.55165, -5.62921, -7.42014, -6.23215, -4.07158, -6.88533, 3.41213, -7.22116, 2.63265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.9958, -3.26273, 10.10668, 1.93138, 9.15303, -0.35371, 4.26691, -8.31189, -0.89807, 1.09884, 0.86984, -1.23265, 1.45419, -0.83049, -0.17383, -1.40849, -10.21162, -2.80513, -6.56238, -9.14021, -1.38182, -14.18543, -3.74521, -9.31754, -10.478, -1.53306, -10.24617, -2.67806, -9.78032, -4.06005, -2.64264, -10.26779, -4.14023, -6.47577, -3.01602, -7.07056, -8.05409, 0.30061, -8.04021, -0.58846, -7.88098, -1.68673, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.21014, -4.24695, 11.75378, 1.58899, 10.52678, -0.85338, 4.49532, -9.79241, 0.05685, -14.13245, -2.48084, -9.51723, -0.87706, -9.79602, -10.02811, -2.75919, -9.66521, -3.84732, -9.04245, -5.13789, -2.14568, -6.32358, -3.00241, -3.90334, -2.31676, -4.34558, -5.10886, 0.63068, -5.14757, 0.06394, -5.10727, -0.64372, 13.2562, 0.35559, 11.61478, -1.92894, 11.77284, 0.01598, 4.04892, -11.31943, 5.2702, -10.80446, -3.89662, -6.46184, -3.48344, -7.07857, -2.26738, -7.55539, -8.05557, -0.44466, -7.68845, -2.44051, -7.27998, -3.47218, 4.43131, -8.0834, 2.68076, -8.71986, 4.08305, -8.15656, -6.72409, -6.72648, -6.22699, -8.3848, -5.01395, -9.15881, 4.56229, -6.50266, 4.55513, -6.75392, 5.60759, -5.90633, -3.40079, -7.68343, -1.30336, -8.29061, -0.15306, -8.39183, 10.434, -0.20853, 9.0549, -1.84692, 9.23447, -0.3259, 2.83975, -9.00331, 3.81332, -8.63574, 4.96472, -8.02953, 4.65155, -2.65636, 3.57745, -2.54779, 3.94846, -1.92217, -0.55589, -4.50529, -0.05673, -4.53915, 0.56821, -4.50361, -4.14026, 2.56606, -4.50938, 1.84738, -2.24753, -3.53534, -1.6348, -3.85764, -1.68175, 4.33546, -2.37685, 3.99835, 2.72136, 4.08054, 2.13696, 4.41663, 0.70674, -1.43419, 0.9314, -1.29764, -0.00833, -3.84947, 0.68259, -5.48054, 1.57635, -5.29098, -3.31622, -4.71883, -2.6351, -5.12761, 2.78713, -10.68973, 4.51276, -10.08102, -6.53932, -9.68983, -5.14558, -10.49428, 0.52978, -12.57822, -0.41519, -11.4404, 1.48047, -11.35052, -10.40997, -5.53702, -9.09268, -7.50719, -7.97665, -8.6839, 0.52978, -12.57822, -0.41519, -11.4404, 1.48047, -11.35052, -9.09268, -7.50719, -7.97665, -8.6839, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.62892, -2.07234, 2.79068, -1.9877, 3.08044, -1.49954, -0.43381, -3.51467, -0.04414, -3.54109, 0.44304, -3.51348, 3.83134, -0.72688, 3.20949, -1.1158, 3.34818, -0.57034, 0.95381, -3.34623, 1.40642, -3.18288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.06152, -0.95859, 0.32663, 2.23039, 0.01874, 2.25456, 0.26544, 5.56868, -0.42374, 8.38258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.80869, 6.52078, 8.62412, -1.37307, 1.19547, -6.02005, -0.98188, 6.5997, 3.52696, 5.02322, -5.09712, -3.03447, 5.29512, 3.09076, 5.71594, -1.5885], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "offset": 28, "vertices": [9.63269, 8.06555, 8.17945, 13.97726, 8.16736, 9.54581, 9.63269, 8.06555, 8.17945, 13.97726, 8.16736, 9.54581, 3.6337, -1.59073, -6.14967, 4.61054, 6.06896, -5.2706, 0.91608, -7.6315, -8.09904, 9.72687, 7.85155, -10.91788, 13.01789, -7.2644, -1.4357, -12.57578, -9.28365, 12.67537, 8.94103, -14.17488, 15.73024, -10.20453, -2.74896, -15.46936, -4.35953, 5.33414, 4.22251, -5.98392, 7.05938, -4.03146, -0.84409, -6.8372, 9.89399, -14.02129, 16.54141, -9.44638, -1.97757, -16.02042, 9.89399, -14.02129, -1.97757, -16.02042, -1.15518, -9.35715, -4.26443, -4.56972, -3.73871, -5.01166, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.39368, -11.35339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.17829, 27.88974, 2.2308, 19.28244, -0.98706, 19.38657, 18.91348, 8.07637, 17.90692, 10.1109, 16.35013, 12.47598, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2e-05, 5.46981, -3.35261, 4.75148, -5.60567, 3.20135, -4.30807, 3.07025, -4.7587, 2.31735, 0.67047, 5.42812, 0.06622, 5.4686, -0.68228, 5.42708, -2e-05, 6.07602, -3.7242, 5.27785, -6.22654, 3.55585, -4.78726, 3.41049, 0.07513, 6.07538, -2e-05, 6.07602, -3.7242, 5.27785, -6.22654, 3.55585, -0.00012, 14.4544, -8.85958, 12.55573, -14.81259, 8.45926, -0.00012, 14.4544, -8.85958, 12.55573, -14.81259, 8.45926, -0.00011, 8.37838, -5.13538, 7.27788, -8.58605, 4.90341, 0, 0, 0, 0, 0, 0, 15.53848, 5.81305, 9.03867, 15.25018, 0.51878, 22.4899, 14.30534, -1.53907, 12.54511, 8.05466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.79182, -8e-05, 6.03309, 4.931, 16.87094, -11.75854, -20.50406, -1.57126, 12.71683, -2.80979, -11.62461, -5.87168, 12.71683, -2.80979, -11.62461, -5.87168, -11.62461, -5.87168, 12.03565, 5.90762, 12.27704, -4.34694, 2.17276, 14.99033, 7.80391, -12.98174, 12.71683, -2.80979, -11.62461, -5.87168, 12.71683, -2.80979, -11.62461, -5.87168, -11.62461, -5.87168, 12.03565, 5.90762, 12.27704, -4.34694, -8.6271, -0.05077, 8.76576, -0.30148, 5.99774, -6.20186, -5.1091, 7.76098, 4.8902, -8.65524, 9.0727, -6.582, -2.08041, -9.05598, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.28133, -4.65276, 4.23122, -0.5894, 14.29163, -7e-05, -11.06589, -9.04385, -5.57053, -4.55266, 5.83469, 4.72287, 7.14014, -0.88156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.1937, 10.62851, -18.02587, -0.0854, -3.64348, 4.13493, 5.43775, -0.89554, -5.48946, 1.20016, 3.40669, 3.33755, 1.44862, 5.80377, 4.54099, -0.13354, -11.99448, -3.93436, 12.33653, 3.78461, 9.56477, 11.80841, 11.13251, -5.95325, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -17.04608, 2.65054, -15.44922, -8.88791, -9.82135, -19.38797, -15.64615, 6.5929, 16.28677, 4.79654, -16.73026, -4.54439, -13.27777, -15.36111, -7.18304, -5.1696, 2.29058, 8.54882, -2.65723, -9.20566, 2.30272, -11.84863, 7.34316, -6.2292, 10.10393, -2.37769, 1.2041, -9.13699, -16.87211, 1.37278, 17.0864, -2.19141, 17.11313, 8.11102, 10.6658, -13.14632, -13.29201, 11.39869, -17.76685, 1.1756, -17.22205, -9.6568, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -17.06921, 3.03271, 17.22245, -4.00549, 18.18375, 6.18301, 9.60211, -14.43571, -19.63661, -2.1004, -14.63812, -14.71519, -6.03254, -25.35068, -14.50438, 5.08612, -14.88082, -5.1033, -11.25841, -14.84027, -12.46429, -8.14316, -13.77066, 6.82425, -14.44232, 5.2653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17.99159, -6.52547, 20.21335, 3.86276, 18.30606, -0.70743, 8.53381, -16.62378, -1.79614, 2.19767, 1.73967, -2.4653, 2.90837, -1.66098, -0.34766, -2.81697, -20.42323, -5.61026, -13.12475, -18.28043, -2.76363, -28.37085, -7.49042, -18.63507, -20.95599, -3.06612, -20.49234, -5.35612, -19.56064, -8.1201, -5.28528, -20.53558, -8.28046, -12.95154, -6.03204, -14.14111, -16.10818, 0.60121, -16.08041, -1.17693, -15.76196, -3.37346, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20.42028, -8.4939, 23.50756, 3.17798, 21.05356, -1.70676, 8.99063, -19.58481, 0.1137, -28.26489, -4.96167, -19.03445, -1.75412, -19.59204, -20.05621, -5.51837, -19.33041, -7.69464, -18.0849, -10.27577, -0.52183, 0.38211, 0.0502, 0.0517, 0.03696, 0.06345, 0.12527, 0.51717, 0.0563, 0.52861, -0.00864, 0.53416, 26.5124, 0.71118, 23.22955, -3.85788, 23.54568, 0.03195, 8.09784, -22.63885, 10.54041, -21.60892, -5.12971, -3.99405, -5.69397, -1.41351, -5.38486, -2.33264, -4.30753, 3.63892, -4.68542, 3.14232, -5.06863, 2.47021, 11.59064, -12.92429, 7.52628, -9.84689, 9.04575, -8.46695, -5.86584, -12.43678, -4.46603, -13.00716, -2.63141, -13.4957, -3.694, 17.3992, 0.1402, 11.9455, -1.83768, 11.80942, 11.31055, 6.50639, 10.52008, 7.7126, 9.37036, 9.08778, 20.86799, -0.41705, 18.1098, -3.69385, 18.46893, -0.65179, 5.6795, -18.00662, 7.62665, -17.27147, 9.92944, -16.05905, 9.3031, -5.31271, 7.15491, -5.09558, 7.89691, -3.84433, -1.11179, -9.01057, -0.11346, -9.07829, 1.13641, -9.00722, -8.28052, 5.13211, -9.01877, 3.69476, -4.49506, -7.07068, -3.26959, -7.71527, -3.36349, 8.67093, -4.75369, 7.9967, 5.44272, 8.16107, 4.27393, 8.83325, 1.41348, -2.86838, 1.86279, -2.59528, 10.37351, -14.14951, 5.26019, -11.40097, 7.06607, -10.37357, -5.76678, -12.33601, -4.00958, -13.0092, 9.46927, -21.81934, 12.93887, -19.95364, -12.21298, -22.27802, -9.03055, -23.74254, 11.44971, -31.60699, 3.06464, -23.32068, 6.8743, -22.49265, -19.18631, -15.8956, -17.3197, -17.91272, -14.69269, -20.12178, 11.44971, -31.60699, 3.06464, -23.32068, 6.8743, -22.49265, -17.3197, -17.91272, -14.69269, -20.12178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.25784, -4.14468, 5.58136, -3.9754, 6.16089, -2.99908, -0.86761, -7.02934, -0.08829, -7.08218, 0.88608, -7.02696, 7.66267, -1.45377, 6.41898, -2.2316, 6.69635, -1.14069, 1.90762, -6.69246, 2.81284, -6.36575, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12305, -1.91718, 0.65326, 4.46077, 0.03748, 4.50912, 0.53088, 11.13736, -0.84747, 16.76517, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -11.61739, 13.04155, 17.24823, -2.74613, 2.39094, -12.0401, -1.96376, 13.1994, 7.05392, 10.04645, -10.19425, -6.06895, 10.59024, 6.18152, 11.43188, -3.177], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "offset": 28, "vertices": [4.81635, 4.03278, 4.08973, 6.98863, 4.08368, 4.7729, 4.81635, 4.03278, 4.08973, 6.98863, 4.08368, 4.7729, 1.81685, -0.79536, -3.07484, 2.30527, 3.03448, -2.6353, 0.45804, -3.81575, -4.04952, 4.86343, 3.92578, -5.45894, 6.50895, -3.6322, -0.71785, -6.28789, -4.64182, 6.33769, 4.47052, -7.08744, 7.86512, -5.10226, -1.37448, -7.73468, -2.17976, 2.66707, 2.11126, -2.99196, 3.52969, -2.01573, -0.42204, -3.4186, 9.40305, -12.75404, 15.65324, -7.70724, -0.28726, -14.92404, 9.40305, -12.75404, -0.28726, -14.92404, -0.14514, -7.53931, -0.0654, -3.3988, 0.13809, -3.39665, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.31763, -8.00391, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, 8.45557, -5.32651, 7.22429, -8.86716, 4.36639, 3e-05, 8.45557, -5.32651, 7.22429, -8.86716, 4.36639, 3e-05, 8.45557, -5.32651, 7.22429, -8.86716, 4.36639, 0, 0, 0, 0, 0, 0, 7.76924, 2.90652, 4.51934, 7.62509, 0.25939, 11.24495, 7.15267, -0.76954, 6.27256, 4.02733, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.89591, -4e-05, 3.01654, 2.4655, 8.43547, -5.87927, -10.25203, -0.78563, 6.35841, -1.4049, -5.81231, -2.93584, 6.35841, -1.4049, -5.81231, -2.93584, -5.81231, -2.93584, 6.01783, 2.95381, 6.13852, -2.17347, 1.08638, 7.49517, 3.90196, -6.49087, 6.35841, -1.4049, -5.81231, -2.93584, 6.35841, -1.4049, -5.81231, -2.93584, -5.81231, -2.93584, 6.01783, 2.95381, 6.13852, -2.17347, -4.31355, -0.02538, 4.38288, -0.15074, 2.99887, -3.10093, -2.55455, 3.88049, 2.4451, -4.32762, 4.53635, -3.291, -1.04021, -4.52799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.14066, -2.32638, 2.11561, -0.2947, 7.14581, -4e-05, -5.53294, -4.52192, -2.78527, -2.27633, 2.91735, 2.36144, 3.57007, -0.44078, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.09685, 5.31426, -9.01294, -0.0427, -1.82174, 2.06747, 2.71887, -0.44777, -2.74473, 0.60008, 1.70335, 1.66878, 0.72431, 2.90189, 2.27049, -0.06677, -5.99724, -1.96718, 6.16827, 1.8923, 4.78238, 5.90421, 5.56625, -2.97662, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.52304, 1.32527, -7.72461, -4.44395, -4.91067, -9.69399, -7.82308, 3.29645, 8.14339, 2.39827, -8.36513, -2.27219, -6.63888, -7.68056, -3.59152, -2.5848, 1.14529, 4.27441, -1.32862, -4.60283, 1.15136, -5.92432, 3.67158, -3.1146, 5.05197, -1.18884, 0.60205, -4.5685, -8.43606, 0.68639, 8.5432, -1.0957, 8.55657, 4.05551, 5.3329, -6.57316, -6.646, 5.69935, -8.88342, 0.5878, -8.61103, -4.8284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.53461, 1.51636, 8.61123, -2.00275, 9.09187, 3.09151, 4.80106, -7.21786, -9.81831, -1.0502, -7.31906, -7.3576, -3.01627, -12.67534, -7.25219, 2.54306, -7.44041, -2.55165, -5.62921, -7.42014, -6.23215, -4.07158, -6.88533, 3.41213, -7.22116, 2.63265, 4.64951, -6.30669, 7.73987, -3.81079, 4.85452, -4.6311, -0.14175, -7.37947, 0.29977, -7.37495, 16.81321, -13.86639, 23.12009, -4.47566, 17.31526, -8.14037, 4.0286, -20.71921, -9.00274, 10.49684, 8.68724, -11.8363, 14.4676, -7.23753, -0.41214, -13.8158, -10.21162, -2.80513, -6.56238, -9.14021, -1.38182, -14.18543, -3.74521, -9.31754, -10.478, -1.53306, -10.24617, -2.67806, -9.78032, -4.06005, -2.64264, -10.26779, -4.14023, -6.47577, -3.01602, -7.07056, -8.05409, 0.30061, -8.04021, -0.58846, -7.88098, -1.68673, 7.81741, -10.60365, 13.01341, -6.40704, 8.16223, -7.78665, 9.42642, -6.19843, -0.23831, -12.40732, 0.50415, -12.39992, 1.005, -12.36928, 14.40258, -9.93356, 18.73286, -1.84723, 14.90416, -5.02953, 4.36739, -16.44646, 0.05685, -14.13245, -2.48084, -9.51723, -0.87706, -9.79602, -10.02811, -2.75919, -9.66521, -3.84732, -9.04245, -5.13789, -1.38513, -6.69736, -2.52597, -4.3578, -1.76472, -4.70627, -5.1218, -0.09465, -5.11763, -0.66151, -5.04843, -1.36722, 13.2562, 0.35559, 11.61478, -1.92894, 11.77284, 0.01598, 4.04892, -11.31943, 5.2702, -10.80446, 4.3283, -2.41124, 3.22002, -2.40865, 3.58057, -1.83488, -0.43947, -4.18073, -0.01343, -4.20681, 0.49435, -4.17774, 11.53159, -2.75104, 9.66052, -3.55525, 10.13127, -1.93106, 1.29581, -10.52703, 2.48431, -10.2206, 3.926, -9.58517, 8.43581, 8.18039, 8.75485, 3.83929, 7.92639, 5.3461, 8.5145, -5.26112, 8.8143, -4.74297, 8.99762, -4.38242, 15.34742, -3.01447, 12.83295, -4.53819, 13.40524, -2.3562, 2.25224, -13.76187, 3.75371, -13.43004, 5.56427, -12.78659, 9.56497, -5.4623, 7.3555, -5.23906, 8.11923, -3.95247, -1.1434, -9.26384, -0.11635, -9.33345, 1.16776, -9.26067, -4.26117, 3.44891, -4.80853, 2.63034, -2.27484, -4.99417, -1.34238, -5.32056, -1.77213, 6.02289, -2.82077, 5.60873, 3.17606, 5.71115, 2.94171, 5.83478, 0.74426, -1.97778, 1.0878, -1.81204, 2.72652, -6.15567, 1.49506, -4.84335, 2.33939, -4.49689, -3.20618, -3.88464, -3.047, -4.01149, 3.76419, -12.11829, 5.87466, -11.24796, -6.94077, -10.89406, -6.49524, -11.16615, 5.0225, -17.85153, 0.48553, -13.26862, 2.85364, -12.96732, -10.90131, -8.25748, -10.38766, -8.89523, -10.01941, -9.30823, 5.0225, -17.85153, 0.48553, -13.26862, 2.85364, -12.96732, -10.38766, -8.89523, -10.01941, -9.30823, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21.26301, -10.75523, 13.85117, -12.54071, 15.853, -9.89993, -0.75781, -20.32752, 0.63899, -20.34335, 1.80424, -20.27422, 10.81917, -4.51324, 6.974, -5.93115, 7.9259, -4.58536, 0.97922, -10.092, 1.38538, -10.04443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.91074, -5.57372, -0.18823, 0.36569, -0.20312, 0.35696, 0.28259, 6.59775, -0.99683, 11.90828, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.80869, 6.52078, 8.62412, -1.37307, 1.19547, -6.02005, -0.98188, 6.5997, 3.52696, 5.02322, -5.09712, -3.03447, 5.29512, 3.09076, 5.71594, -1.5885], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667}]}, "h1": {"h1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [-16.11966, -0.79266, -23.36046, -1.14941, -23.37073, 0.91766, -23.36046, -1.14941, -23.71884, 0.93106, -23.70848, -1.16705, -24.39105, 0.95764, -24.38039, -1.1998, -23.19241, 8.25659, -23.83138, 6.17642, -11.01917, 12.789, -12.10606, 11.76636, -11.01666, 10.98895, -11.94456, 9.97357, 0, 0, 0, 0, 9.98517, -2.95142, 10.20621, -2.05685, 9.98517, -2.95142, 10.20621, -2.05685, 9.98517, -2.95142, 9.98517, -2.95142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.28638, -11.91313, 3.82999, -11.51117, 2.28638, -11.91313, 3.82999, -11.51117, 3.7677, -7.18226, 4.67758, -6.62625, -10.99716, -7.7637, -10.99716, -7.7637, -6.71808, -4.74315, -6.71808, -4.74315, -9.3837, 1.26175, -8.14651, -5.75146, -8.14651, -5.75146, -8.14651, -5.75146, -10.55981, 2.85812, -10.55981, 2.85812, 2.94153, 8.89732, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.71246, -5.59494, -2.76636, -6.11772, -9.63062, -7.81157, -8.25006, -9.25894, -16.96765, -10.56662, -15.04797, -13.15927, -24.83118, -13.51617, -22.33362, -17.33629, -23.98145, -16.31861, -20.26105, -6.73463, -18.91565, -9.90773, -13.62335, -0.66541, -13.34247, -2.84902, -9.76807, 2.85818, -10.10474, 1.24974, -5.6319, 6.63853, -6.63068, 5.64685, 2.47571, 9.9424, 0.83789, 10.21362, -15.96948, 35.42601, -13.60858, 36.04301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -15.33124, -9.95435, -14.47314, -9.16495, -5.52106, -1.01477, 0, 0, 0, 0, 1.6416, 4.53958, 1.54251, 4.57401, -8.78168, 5.25635, -8.89365, 5.06506, -8.93323, 4.99588, 0, 0, 0, 0, 0, 0, 0.17639, 2.55249, 0.12085, 2.55533, 0.10049, 2.55685, 0.12085, 2.55533, 0.10049, 2.55685, 0.12085, 2.55533, 0.10049, 2.55685, 0, 0, 0, 0, 0, 0, -13.06625, -11.47794, 0, 0, -13.06625, -11.47794, -14.19987, -8.96036, -22.61026, -0.48325, -17.1916, 3.32089, -7.07312, -6.95526, -7.52808, -6.45306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.33746, -1.88068, -4.96796, -2.71367, -15.92596, -7.7256, -14.47729, -10.18747, -10.32928, -4.63435, -9.45129, -6.23479, 0.34814, 0.9326, 0.19092, 0.97838, 0.06226, 0.9924, 0, 0, 0, 0, -2.59204, 0.17828, -2.59604, 0.12234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.31268, 3.75026, 0, 0, -4.08139, -2.88168, -10.34198, 0.71112, 0, 0, 0, 0, -6.9621, 0.47873, -6.97089, 0.32813, 0, 0, 0, 0, -8.78168, 5.25635, -8.89365, 5.06506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.04056, 0.63873, -3.85953, -0.18954, -1.49084, 2.80493, -1.73323, 2.6629, -4.47504, -0.22025, -4.02203, 0.15799, -4.02029, -0.19772, -6.04309, 0.23734, -6.04048, -0.29712, -6.04309, 0.23734, -6.04048, -0.29712], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "f2": {"f2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 60, "vertices": [-3.81442, -9.80705, 1.84084, -10.35954, -5.96814, 2.07706, -6.17841, -1.32858, -5.9767, 8.01033, -6.65154, 7.45956, -9.56098, 2.91331, -5.1897, 9.22191, -5.97303, 8.73488, -9.64442, 4.35567, -0.89543, 6.50714, -1.45864, 6.40475, -4.5763, 4.71181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.91644, 6.65935, -1.49276, 6.5544, -4.68328, 4.82193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.06535, 1.28401, -2.1692, 1.0993, -2.42494, -0.18826, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.54076, -3.23952, 0.36802, -3.5674, 0, 0, -2.78518, -3.15773, -0.73741, -4.14471], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"s1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 6, "vertices": [4.60098, -2.9898, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.25162, 0.78291, 6.07818, -9.50053, 13.8576, -1.39973, 5.41093, -12.83345, 13.52489, -3.13816, 3.72567, -13.37442, 13.17516, -5.04919, 1.88062, -13.98309, 12.55273, -8.2169, -1.19812, -14.9543, 11.93788, -10.81832, -3.77603, -15.66096, 0, 0, 1.64775, 21.81484, 9.09932, 19.90666, 14.18759, 18.60388, 18.50876, 13.32966, 25.18823, 5.17757, 18.83716, 0.5301, 12.93283, -4.26934, 8.36287, -9.85889, 9.99199, -4e-05, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 0, 0, 0, 0, 0, 0, 0, 0, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 4.60098, -2.9898, -0.41982, -5.47055, 3.52248, -0.24394, 1.99838, 0, 0.95744, -1.75406, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.15442, 8.95717, 11.07893, -1.23492, 0, 0, 3.74574, -2.47261, 8.07109, -4.97454, 5.13541, 13.6712, 0, 0, 0, 0, 5.12987, -1.2299, 1.37848, -5.09126, 0, 0, 0, 0, 6.3732, 1.04915, 0, 0, 0, 0, 7.46599, 3.38836, 0, 0, 0, 0, 7.64313, 6.18169, 9.08814, -3.74643, 2.91034, -0.84226, 0.65528, -2.9575, 8.67813, 9.46908, 12.46967, -3.07991, 3.32173, 1.38828, 2.81021, -2.24998, 9.20575, 12.81395, 15.6583, -1.94051, 3.59322, 3.34555, 4.6583, -1.55054, 9.80138, 15.69759, 18.47486, -1.0818, 4.05983, 5.43895, 6.71944, -0.9572, 10.56796, 17.48695, 20.41274, -0.89725, 4.05266, 6.9186, 8.01473, -0.24191, 4.49475, 8.53014, 9.64108, 0.14198, 4.77304, 9.70306, 10.80394, 0.45966], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st1": {"st1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 5, "vertices": [-0.70214, -0.0004, -0.70207, -2.6205, 5.9937, -2.62161, 5.99403, -0.6505, 3.15085, -0.65054, 3.15087, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.73656, -6.97916, -4.60796, -4e-05, -4.2684, -0.99168, -7.70205, -2.34566, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.1322, -4.12411, 2.81378, 5.95129, 3.81982, 5.36023, -7.28677, -2.52001, 5.45233, 5.44852, 6.32852, 4.40048, -9.81573, -0.69661, 8.52362, 4.91193, -7.94626, -0.66193, 6.85693, 4.06457, 7.46707, 2.79065, -5.63208, -0.60207, 4.80074, 3.00053, 5.25604, 2.10574, -3.70901, -0.50468, 3.11331, 2.07365, 3.43121, 1.49036, 3.09436, 2.10123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.60895, 4.77101, 14.65332, 7.27562, 15.23492, 2.73363, 39.38852, 6.59856, 37.48425, 13.78067, 23.8074, 6.70531, 22.15314, 10.99919, 14.42923, 4.06383, 13.42657, 6.66652, 15.89737, 9.86792, 17.39044, 6.90834, 15.80762, 10.01158, 10.91478, 4.64793, 11.56567, 2.64821, 10.87268, 4.74677, 4.39694, -2.11562, 3.95786, -2.8609, 4.41699, -2.0762, 1.66248, 10.12729, 2.91905, -9.83612, 1.14206, -10.20016, 3.00903, -9.81026, -11.99805, 10.26871, 15.2751, -4.00134, 14.33124, -6.63206, 15.31097, -3.8624, -11.99805, 10.26871, 15.2751, -4.00134, 14.33124, -6.63206, -11.99805, 10.26871, 15.2751, -4.00134, 14.33124, -6.63206, -11.99805, 10.26871, 15.2751, -4.00134, 14.33124, -6.63206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.64743, -10.8296, -2.64948, -10.82888, 0, 0, 0, 0, 0, 0, -4.11298, -8.55957, -4.11249, -8.55935, -4.11441, -8.55856, 0, 0, 0, 0, 0, 0, -5.23628, -6.52802, -5.23587, -6.52783, -5.23785, -6.52705, -4.73656, -6.97916, -6.78756, -3.98904, -6.78694, -3.98887, -6.78888, -3.98811, -7.71373, -2.86317, -7.56087, -2.28796, -7.56039, -2.2877, -7.56256, -2.28698, -6.30481, -3.31882, -8.32767, -0.54245, -8.32726, -0.54221, -8.32938, -0.54147, -0.00013, -2.49543, -8.72998, 1.28062, -8.73184, 1.28136, 0, 0, 0, 0, 0, 0, -9.2758, 3.13352, -9.27768, 3.13428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.68243, 0.45691, 1.7052, 0.32457, 1.73883, 0.01465, 0, 0, -2.00085, 0.00378, 1.79446, 0.87125, 1.92255, 0.53708, 0, 0, -3.01041, -4.84602, 0.58022, 5.67628, 1.57578, 5.47725, -3.10013, 3.91013, 4.48885, -2.16319, 4.03964, -2.92462, 0, 0, 0.46927, -2.44454, -1.50299, 1.99622, -1.12177, 2.22321, -4.85175, 4.29923, 6.23363, -1.74865, 5.83038, -2.82506, 0, 0, 0, 0, 2.22183, -4.50084, -3.97858, 3.08195, -3.36749, 3.7261, -10.89435, 3.71319, 11.41193, 1.4166, 0, 0, 0, 0, -12.24771, -15.54162, 4.2318, 19.32983, 7.57355, 18.27919, -8.49271, 2.97371, -7.83481, 4.4221, -0.59369, -6.07892, -2.12276, 5.72887, -1.07843, 6.01138, -11.12393, -12.61645, 4.49763, 16.20744, 7.28482, 15.15887, 4.34979, 16.24736, 18.06326, -8.72038, -20.06845, -0.0375, -1.4032, -3.07584, -0.08614, 3.38065, 0.51291, 3.3396, 2.92145, 12.7138, 5.11789, 11.99785, 2.80548, 12.73929, 10.63525, -12.78865, -15.15225, 6.86411, -13.70514, 9.42613, 2.30219, 0.45194, -1.87752, -1.41112, -2.09549, -1.05988, -1.86383, -1.4288, -0.90585, -0.09522, 0.77005, 0.48158, 0.84409, 0.33633, 0.76599, 0.48792, 1.89772, 9.39421, 3.52475, 8.91034, 1.81189, 9.41008, -13.70514, 9.42613, -15.21448, 6.72505, 0, 0, 0, 0, 0, 0, 0, 0, 0.52753, 7.59157, -0.88885, 7.55914, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.70514, 9.42613, -15.21448, 6.72505, 0, 0, 0, 0, 0, 0, -15.21448, 6.72505, 0, 0, 0, 0, 0, 0, -15.21448, 6.72505], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t1": {"t1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 136, "vertices": [-2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, 3.4231, -2.42249, 0, 0, 0, 0, 3.83218, -4.42615, 3.9682, -4.38232, -3.90903, 9.26004, -3.90884, 9.26009, 7.17783, -6.88699, 0, 0, 0, 0, -1.65955, 5.20176, -1.65933, 5.20176, 3.53769, -4.0856, -3.771, 8.82652, -3.77072, 8.82654, 6.88333, -6.54645, 0, 0, 0, 0, 0, 0, 0.06335, -6.18837, -2.44611, 5.63391, -1.10614, 3.46787, -1.10608, 3.46789, 2.35822, -2.72382, -2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, -2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.96808, 3.03439, -0.96793, 3.03442, 2.06335, -2.38339, 0, 0, 0, 0, 0, 0, 1.24426, -3.90136, -2.65338, 3.06412, -0.59052, -1.14347, 0.10358, 1.28482, 1.24426, -3.90136, -2.65338, 3.06412, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, 0, 0, 0, 0, -0.59052, -1.14347, 0.10358, 1.28482, -2.11145, 3.62476, -2.11139, 3.62477, 3.34564, -2.46085, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.33221, -0.74385, -2.33221, -0.74385, -2.33221, -0.74385, 0.11789, 1.64362, 2.33218, 0.74387, 2.33221, 0.74387, 2.33218, 0.74387, 2.33221, 0.74387], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "offset": 136, "vertices": [-1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, 2.79165, -1.97562, 0, 0, 0, 0, 3.12528, -3.60967, 3.2362, -3.57393, -3.18794, 7.55188, -3.1878, 7.55192, 5.85376, -5.61658, 0, 0, 0, 0, -1.35342, 4.24222, -1.35324, 4.24222, 2.88511, -3.33195, -3.07538, 7.19833, -3.07515, 7.19834, 5.61359, -5.33885, 0, 0, 0, 0, 0, 0, 0.05167, -5.04683, -1.99488, 4.59465, -0.90209, 2.82817, -0.90205, 2.82818, 1.9232, -2.22136, -1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, -1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7895, 2.47465, -0.78938, 2.47468, 1.68274, -1.94374, 0, 0, 0, 0, 0, 0, 1.01474, -3.18169, -2.16392, 2.49889, -0.48159, -0.93254, 0.08447, 1.04781, 1.01474, -3.18169, -2.16392, 2.49889, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, 0, 0, 0, 0, -0.48159, -0.93254, 0.08447, 1.04781, -1.72196, 2.95611, -1.72191, 2.95613, 2.72849, -2.0069, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.902, -0.60664, -1.902, -0.60664, -1.902, -0.60664, 0.09614, 1.34043, 1.90198, 0.60665, 1.902, 0.60665, 1.90198, 0.60665, 1.902, 0.60665], "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 1.9, "offset": 136, "vertices": [-1.27025, 2.18065, -1.27021, 2.18066, 2.01274, -1.48044, 2.05933, -1.45737, 0, 0, 0, 0, 2.30544, -2.66277, 2.38727, -2.6364, -2.35167, 5.57083, -2.35156, 5.57086, 4.31817, -4.14321, 0, 0, 0, 0, -0.99838, 3.12938, -0.99825, 3.12938, 2.12827, -2.45789, -2.26863, 5.31003, -2.26846, 5.31004, 4.14101, -3.93834, 0, 0, 0, 0, 0, 0, 0.03811, -3.72292, -1.47158, 3.38936, -0.66545, 2.08627, -0.66542, 2.08628, 1.4187, -1.63865, -5.41212, 0.90513, -5.41154, 0.90507, 5.31883, 1.36373, -1.27025, 2.18065, -1.27021, 2.18066, 2.01274, -1.48044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.5824, 1.82549, -0.5823, 1.82551, 1.24131, -1.43385, 0, 0, 0, 0, 0, 0, 0.74855, -2.34705, -1.59627, 1.84337, -0.35525, -0.68791, 0.06231, 0.77295, 0.74855, -2.34705, -1.59627, 1.84337, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.27025, 2.18065, -1.27021, 2.18066, 2.01274, -1.48044, 0, 0, -4.51794, -1.39154, -2.23666, -1.26764, 1.56439, 2.06592, -5.60051, 0.84714, -5.60017, 0.84711, 5.4694, 1.4927, -3.8183, -1.17599, -6.57617, -2.02535, 0, 0, -3.01178, -0.92766, -3.76511, -1.15962, -2.63562, -0.81172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.20032, -0.98567, -7.53842, -2.32174, -2.25867, -0.69576, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.35611, -3.2, -7.14716, -2.43357, -4.24133, -1.30658, 0, 0, 0, 0, 0, 0, 1.12582, 3.13406, 1.12631, 3.13409, 1.3067, 1.7958, 0, 0, -5.51315, -4.71809, -9.23499, -4.70267, -5.70972, -4.08164, 1.19171, 2.92194, 3.49298, 2.2369, 3.90234, 1.66587, 2.33398, 0.71869, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.40306, -0.4475, -1.40306, -0.4475, -1.40306, -0.4475, 0.07092, 0.9888, 1.40304, 0.44751, 1.40306, 0.44751, 1.40304, 0.44751, 1.40306, 0.44751], "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 2.1667, "offset": 136, "vertices": [-0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, 1.16064, -0.82137, 0, 0, 0, 0, 1.29935, -1.50074, 1.34546, -1.48588, -1.3254, 3.13972, -1.32534, 3.13974, 2.43372, -2.33511, 0, 0, 0, 0, -0.56269, 1.76372, -0.56262, 1.76372, 1.19949, -1.38527, -1.2786, 2.99273, -1.27851, 2.99274, 2.33387, -2.21965, 0, 0, 0, 0, 0, 0, 0.02148, -2.09824, -0.82938, 1.91024, -0.37505, 1.17582, -0.37503, 1.17583, 0.79958, -0.92354, -0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, -0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.32824, 1.02884, -0.32819, 1.02886, 0.6996, -0.80812, 0, 0, 0, 0, 0, 0, 0.42188, -1.3228, -0.89966, 1.03892, -0.20022, -0.38771, 0.03512, 0.43563, 0.42188, -1.3228, -0.89966, 1.03892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, 0, 0, 0, 0, -0.20022, -0.38771, 0.03512, 0.43563, -0.71591, 1.22902, -0.71589, 1.22902, 1.13438, -0.83438, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.79076, -0.25221, -0.79076, -0.25221, -0.79076, -0.25221, 0.03997, 0.55729, 0.79075, 0.25222, 0.79076, 0.25222, 0.79075, 0.25222, 0.79076, 0.25222], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.6667}]}}}}}}