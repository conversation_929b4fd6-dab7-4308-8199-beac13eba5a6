import { Label } from "cc";
import { _decorator, Component, Node } from "cc";
import { TimeUtils } from "../../GameScrpit/lib/utils/TimeUtils";
const { ccclass, property } = _decorator;

@ccclass("LblCd")
export class LblCd extends Component {
  private _ts: number;
  private _endHide: boolean;
  private _callback: Function;

  public setCd(ts: number, endHide: boolean = false, callback: Function = null) {
    this._ts = ts;
    this._endHide = endHide;
    this.node.active = true;
    this._callback = callback;
  }

  update(deltaTime: number) {
    const endTime = this._ts;
    if (endTime && endTime - TimeUtils.serverTime > 0) {
      const s = `${TimeUtils.timeformatHMS(endTime - TimeUtils.serverTime)}`;
      if (this.node.getComponent(Label).string != s) {
        this.node.getComponent(Label).string = s;
      }
    } else {
      if (this._endHide) {
        this.node.active = false;
      }
      if (this.node.getComponent(Label).string != "00:00") {
        this.node.getComponent(Label).string = "00:00";
      }
      this._callback && this._callback();
    }
  }
}
