<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>S0254.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-2,0}</string>
                <key>spriteSize</key>
                <string>{89,81}</string>
                <key>spriteSourceSize</key>
                <string>{93,81}</string>
                <key>textureRect</key>
                <string>{{1455,210},{89,81}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0255.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{750,207}</string>
                <key>spriteSourceSize</key>
                <string>{750,207}</string>
                <key>textureRect</key>
                <string>{{753,1},{750,207}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S1266.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{700,47}</string>
                <key>spriteSourceSize</key>
                <string>{700,47}</string>
                <key>textureRect</key>
                <string>{{753,210},{700,47}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S1268.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{135,43}</string>
                <key>spriteSourceSize</key>
                <string>{135,43}</string>
                <key>textureRect</key>
                <string>{{753,259},{135,43}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>wujiang_back_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{750,318}</string>
                <key>spriteSourceSize</key>
                <string>{750,318}</string>
                <key>textureRect</key>
                <string>{{1,1},{750,318}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>wujiang_btn_moren.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{180,63}</string>
                <key>spriteSourceSize</key>
                <string>{180,63}</string>
                <key>textureRect</key>
                <string>{{1505,1},{180,63}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>wujiang_icon_moren.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,24}</string>
                <key>spriteSourceSize</key>
                <string>{32,24}</string>
                <key>textureRect</key>
                <string>{{1505,183},{32,24}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UIHeroMain.png</string>
            <key>size</key>
            <string>{1569,320}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:c20ba245bd8c3fbe1a7ba6e65f9ecb32:f698a65a251014f2dfd0f71cebbcc125:2d3dc0b31cd5e30d224df4f1d5b5a679$</string>
            <key>textureFileName</key>
            <string>UIHeroMain.png</string>
        </dict>
    </dict>
</plist>
