import { instantiate, Node } from "cc";
import { ListAdapter, ViewHolder } from "../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { MineViewHolder } from "./MineViewHolder";
import { OtherViewHolder } from "./OtherViewHolder";
import { ChatPackMessage } from "../../net/protocol/Chat";
import { PlayerModule } from "../../../module/player/PlayerModule";

const MINE = 0;
const OTHER = 1;
export class ChatAdapter extends ListAdapter {
  private _mineViewHolder: Node;
  private _otherViewHolder: Node;
  private _data: ChatPackMessage[];
  private _marketType;

  public constructor(mine: Node, other: Node) {
    super();
    this._mineViewHolder = mine;
    this._otherViewHolder = other;
  }
  public setData(data: ChatPackMessage[], marketType: number) {
    this._data = data;
    this._marketType = marketType;
    this.notifyDataSetChanged();
  }
  public insertNewData(data: ChatPackMessage) {
    this._data.unshift(data);
  }
  getViewType(position: number): number {
    return this._data[position].sender.userId == PlayerModule.data.playerId ? MINE : OTHER;
  }
  onCreateView(viewType: number): Node {
    switch (viewType) {
      case MINE:
        return instantiate(this._mineViewHolder);
      case OTHER:
        let item = instantiate(this._otherViewHolder);
        item.getComponent(OtherViewHolder).init();
        return item;
      default:
        return null;
    }
  }
  onBindData(node: Node, position: number): void {
    switch (node.getComponent(ViewHolder).viewType) {
      case MINE:
        node
          .getComponent(MineViewHolder)
          .updateData(this._data[position - 1], this._data[position], this._data[position + 1], this._marketType);
        break;
      case OTHER:
        node
          .getComponent(OtherViewHolder)
          .updateData(this._data[position - 1], this._data[position], this._data[position + 1], this._marketType);
        break;
      default:
        break;
    }
  }
  getCount(): number {
    return this._data.length;
  }

  public getItem(index: number): any {
    return index;
  }
}
