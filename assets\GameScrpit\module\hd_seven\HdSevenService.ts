import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import { SevenDaySignMessage } from "../../game/net/protocol/Activity";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { HdSevenModule } from "./HdSevenModule";

/**
 * 模块逻辑处理
 */
export class HdSevenService {
  public canShowSeven(callback: Function) {
    HdSevenModule.api.sevenDaySign(10501, (resp: SevenDaySignMessage) => {
      let bool = TimeUtils.serverTime < (HdSevenModule.data.sevenDaySignMessage?.endTime ?? 0);

      if (HdSevenModule.data.sevenDaySignMessage?.sign == false && HdSevenModule.data.sevenDaySignMessage?.count >= 7) {
        bool = false;
      }

      callback(bool);
    });
  }

  public updatePopover() {
    let isShow = false;
    if (!HdSevenModule.data.sevenDaySignMessage.sign) {
      if (HdSevenModule.data.sevenDaySignMessage.count < 7) {
        isShow = true;
      }
    }

    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_activity_7.btn_qiandaolingqu.id, isShow);
  }
}
