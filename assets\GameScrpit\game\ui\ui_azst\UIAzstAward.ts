import { _decorator, Label, Sprite } from "cc";
import ResMgr from "../../../lib/common/ResMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { AzstModule } from "../../../module/azst/AzstModule";
import { DialogZero } from "../../GameDefine";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import FmUtils from "../../../lib/utils/FmUtils";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";

const { ccclass, property } = _decorator;

enum AzstAward_Enum {
  WEEK,
  DAY,
}

const pithColor = "#6F2D13";
const noPitchColor = "#134B84";

const rankIconList = ["", "S1175", "S1176", "S1177"];

@ccclass("UIAzstAward")
export class UIAzstAward extends UINode {
  protected _openAct: boolean = true; //打开动作
  public zOrder(): number {
    return DialogZero.UIAzstAward;
  }
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_AZST}?prefab/ui/UIAzstAward`;
  }

  protected dependOn(): Array<BundleEnum> {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _curIndexMain: AzstAward_Enum = AzstAward_Enum.DAY;

  private _dayList: Array<any> = [];

  private _weekList: Array<any> = [];

  private _timeTickId: number = 0;

  protected onEvtShow(): void {
    this.changeMain();
    FmUtils.setCd(this.getNode("lbl_cd"), AzstModule.data.dailyRewardDeadline + TimeUtils.serverTime);
    let c_compete = JsonMgr.instance.jsonList.c_compete;
    for (let i in c_compete) {
      if (Number(i) > 100 && Number(i) < 200) {
        this._weekList.push(c_compete[i]);
      } else if (Number(i) > 200 && Number(i) < 300) {
        this._dayList.push(c_compete[i]);
      }
    }
    this.loadDayList();
    this.loadWeekList();
  }

  private loadDayList() {
    for (let i = 0; i < this._dayList.length; i++) {
      let node = ToolExt.clone(this.getNode("rankAwardItem"), this);
      this.getNode("dayContent").addChild(node);
      node.active = true;
      node["index"] = i;
      let info = this._dayList[i];

      // for (let j = 0; j < this.getNode("dayContent").children.length; j++) {
      //   let ch = this.getNode("dayContent").children[j];
      //   ch.setSiblingIndex(ch["index"]);
      // }

      let lastInfo = this._weekList[i - 1];
      let orderCha = 1;
      if (lastInfo) {
        orderCha = info.order - lastInfo.order;
      }

      if (info.order <= 3) {
        node["spr_rank"].active = true;
        node["lab_rank"].active = false;

        ResMgr.loadImage(
          `${BundleEnum.BUNDLE_COMMON_UI}?atlas_imgs/YWC_icon_paiming${info.order}`,
          node["spr_rank"].getComponent(Sprite),
          this
        );

        ResMgr.loadImage(
          `${BundleEnum.BUNDLE_G_AZST}?images/YWC_jl_henglan${info.order}`,
          node.getComponent(Sprite),
          this
        );
      } else {
        node["spr_rank"].active = false;
        node["lab_rank"].active = true;

        if (orderCha == 1) {
          node["lab_rank"].getComponent(Label).string = info.order;
        } else {
          node["lab_rank"].getComponent(Label).string = lastInfo.order + 1 + "-" + info.order;
        }

        ResMgr.loadImage(`${BundleEnum.BUNDLE_G_AZST}?images/YWC_jl_henglan`, node.getComponent(Sprite), this);
      }

      for (let j = 0; j < info.rewardList.length; j++) {
        let itemN = ToolExt.clone(this["btn_item"], this);
        node["awardLay"].addChild(itemN);
        itemN.active = true;

        let itemInfo = info.rewardList[j];
        itemN["itemId"] = itemInfo[0];

        FmUtils.setItemNode(itemN, itemInfo[0], itemInfo[1]);
      }
    }
  }

  private loadWeekList() {
    for (let i = 0; i < this._weekList.length; i++) {
      let info = this._weekList[i];

      let node = ToolExt.clone(this["rankAwardItem"], this);
      this["weekContent"].addChild(node);
      node.active = true;
      node["index"] = i;

      // for (let j = 0; j < this.getNode("weekContent").children.length; j++) {
      //   let ch = this.getNode("weekContent").children[j];
      //   ch.setSiblingIndex(ch["index"]);
      // }

      let lastInfo = this._weekList[i - 1];
      let orderCha = 1;
      if (lastInfo) {
        orderCha = info.order - lastInfo.order;
      }

      if (info.order <= 3) {
        node["spr_rank"].active = true;
        node["lab_rank"].active = false;

        ResMgr.loadImage(
          `${BundleEnum.BUNDLE_COMMON_UI}?atlas_imgs/YWC_icon_paiming${info.order}`,
          node["spr_rank"].getComponent(Sprite),
          this
        );

        ResMgr.loadImage(
          `${BundleEnum.BUNDLE_G_AZST}?images/YWC_jl_henglan${info.order}`,
          node.getComponent(Sprite),
          this
        );
      } else {
        node["spr_rank"].active = false;
        node["lab_rank"].active = true;
        let lastInfo = this._weekList[i - 1];

        if (orderCha == 1) {
          node["lab_rank"].getComponent(Label).string = info.order;
        } else {
          node["lab_rank"].getComponent(Label).string = lastInfo.order + 1 + "-" + info.order;
        }

        ResMgr.loadImage(`${BundleEnum.BUNDLE_G_AZST}?images/YWC_jl_henglan`, node.getComponent(Sprite), this);
      }

      for (let j = 0; j < info.rewardList.length; j++) {
        let itemN = ToolExt.clone(this["btn_item"], this);
        node["awardLay"].addChild(itemN);
        itemN.active = true;

        let itemInfo = info.rewardList[j];
        itemN["itemId"] = itemInfo[0];

        FmUtils.setItemNode(itemN, itemInfo[0], itemInfo[1]);
      }
    }
  }

  private on_click_btn_day() {
    AudioMgr.instance.playEffect(1090);
    if (this._curIndexMain == AzstAward_Enum.DAY) {
      return;
    }
    this._curIndexMain = AzstAward_Enum.DAY;
    FmUtils.setCd(this.getNode("lbl_cd"), AzstModule.data.dailyRewardDeadline + TimeUtils.serverTime);
    this.changeMain();
  }

  private on_click_btn_week() {
    AudioMgr.instance.playEffect(1090);
    if (this._curIndexMain == AzstAward_Enum.WEEK) {
      return;
    }
    FmUtils.setCd(this.getNode("lbl_cd"), AzstModule.data.weekRewardDeadline + TimeUtils.serverTime);
    this._curIndexMain = AzstAward_Enum.WEEK;
    this.changeMain();
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private changeMain() {
    if (this._curIndexMain == AzstAward_Enum.WEEK) {
      this["dayScroll"].active = false;
      this["weekScroll"].active = true;
      this["zi_meizhoujiangli"].active = true;
      this["zi_meirijiangli"].active = false;

      this["btn_week"].getChildByName("pich").active = true;
      this["btn_week"].getChildByName("no_pich").active = false;

      this["btn_day"].getChildByName("pich").active = false;
      this["btn_day"].getChildByName("no_pich").active = true;
    } else if (this._curIndexMain == AzstAward_Enum.DAY) {
      this["weekScroll"].active = false;
      this["dayScroll"].active = true;
      this["zi_meizhoujiangli"].active = false;
      this["zi_meirijiangli"].active = true;

      this["btn_day"].getChildByName("pich").active = true;
      this["btn_day"].getChildByName("no_pich").active = false;

      this["btn_week"].getChildByName("pich").active = false;
      this["btn_week"].getChildByName("no_pich").active = true;
    }
  }

  protected onEvtClose(): void {
    TickerMgr.clearTicker(this._timeTickId);
  }
}
