{"skeleton": {"hash": "17HAsf3OKFZZYts+OeYiTwloU1Q=", "spine": "3.8.75", "x": -135.24, "y": -40.86, "width": 205.56, "height": 226.18, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": -40.93, "scaleX": 0.43, "scaleY": 0.43}, {"name": "bone2", "parent": "bone", "x": 5.63, "y": 190.37, "color": "ff0000ff"}, {"name": "bone3", "parent": "bone2", "length": 52.57, "rotation": -0.58, "y": 0.53, "color": "0044ffff"}, {"name": "bone4", "parent": "bone3", "length": 40.58, "rotation": 79, "x": -0.35, "y": 0.44}, {"name": "bone5", "parent": "bone4", "length": 98.17, "rotation": 0.32, "x": 40.58}, {"name": "bone6", "parent": "bone5", "length": 27.72, "rotation": 1.3, "x": 98.17}, {"name": "bone7", "parent": "bone6", "length": 19.62, "rotation": -156.29, "x": -14.93, "y": -11.95}, {"name": "bone8", "parent": "bone7", "length": 13.41, "rotation": 46.7, "x": 19.25, "y": 0.09}, {"name": "bone9", "parent": "bone6", "length": 17.12, "rotation": -81.35, "x": 30.46, "y": -47.82}, {"name": "bone10", "parent": "bone6", "length": 16.21, "rotation": -20.31, "x": 86.42, "y": -47.09}, {"name": "bone11", "parent": "bone10", "length": 13.64, "rotation": 33.53, "x": 16.21}, {"name": "bone12", "parent": "bone6", "length": 21.36, "rotation": -23.16, "x": 121.3, "y": -3.06}, {"name": "bone13", "parent": "bone12", "length": 12.83, "rotation": 47.15, "x": 21.36}, {"name": "bone14", "parent": "bone6", "length": 22.56, "rotation": 67.63, "x": 114.25, "y": 50.6}, {"name": "bone15", "parent": "bone14", "length": 20.22, "rotation": -68.77, "x": 22.56}, {"name": "bone16", "parent": "bone6", "length": 22.28, "rotation": 124.73, "x": 55.33, "y": 78.07}, {"name": "bone17", "parent": "bone16", "length": 20.29, "rotation": -110.38, "x": 22.28}, {"name": "bone18", "parent": "bone6", "length": 14.14, "rotation": 107.86, "x": 5.36, "y": 43.24}, {"name": "bone19", "parent": "bone18", "length": 13.79, "rotation": -24.3, "x": 14.19, "y": 0.39}, {"name": "bone20", "parent": "bone5", "length": 60.06, "rotation": -139.99, "x": 75.28, "y": -66.25}, {"name": "bone21", "parent": "bone20", "length": 64.98, "rotation": -33.28, "x": 60.06}, {"name": "bone22", "parent": "bone21", "length": 35.03, "rotation": -28.53, "x": 64.98}, {"name": "bone23", "parent": "bone5", "length": 69.18, "rotation": 136.66, "x": 80.04, "y": 97.4}, {"name": "bone24", "parent": "bone23", "length": 65.35, "rotation": 31.51, "x": 69.18}, {"name": "bone25", "parent": "bone24", "length": 23.06, "rotation": -104.04, "x": 65.35, "transform": "noRotationOrReflection"}, {"name": "bone26", "parent": "bone25", "length": 64.78, "rotation": -93.71, "x": -73.56, "y": 14.43}, {"name": "a22", "parent": "bone26", "length": 69.55, "rotation": -93.58, "x": 0.41, "y": 0.12}, {"name": "a19", "parent": "a22", "length": 46.8, "rotation": 145.23, "x": 163.68, "y": 19.2}, {"name": "a20", "parent": "a22", "length": 29.74, "rotation": -158.67, "x": 165.53, "y": -17.23}, {"name": "a1", "parent": "a22", "length": 43.78, "rotation": 4.4, "x": -192.28, "y": 52.74}, {"name": "a0", "parent": "a22", "length": 40.99, "rotation": -41.35, "x": -189.46, "y": -53.58}, {"name": "bone27", "parent": "bone6", "x": 10.84, "y": -12.1, "color": "abe323ff"}, {"name": "bone28", "parent": "bone6", "x": 60.23, "y": 0.17}, {"name": "bone29", "parent": "bone5", "x": 19.18, "y": -10.6, "color": "abe323ff"}, {"name": "bone30", "parent": "bone5", "x": 67.57, "y": -10.02, "color": "abe323ff"}, {"name": "bone31", "parent": "bone5", "length": 79.57, "rotation": -85.79, "x": 133.02, "y": 18.9, "transform": "noRotationOrReflection", "color": "ff9118ff"}, {"name": "bone32", "parent": "bone31", "length": 53.18, "rotation": 1.67, "x": 79.57, "color": "ff9118ff"}, {"name": "bone33", "parent": "bone32", "length": 56.63, "rotation": -0.75, "x": 53.18, "color": "ff9118ff"}, {"name": "bone34", "parent": "bone3", "length": 32.31, "rotation": -91.49, "x": -1.04, "y": -0.29, "color": "fffc00ff"}, {"name": "bone35", "parent": "bone34", "x": 11.61, "y": -34.72}, {"name": "bone36", "parent": "bone35", "length": 38.77, "rotation": -16.5, "x": 0.05, "y": -1.41}, {"name": "bone37", "parent": "bone36", "length": 39.86, "rotation": -1.11, "x": 38.56, "y": -0.45}, {"name": "bone47", "parent": "bone", "length": 33.79, "rotation": 0.6, "x": -56.45, "y": 84.51}, {"name": "bone48", "parent": "bone47", "length": 17.6, "rotation": 90.55, "x": -0.11, "y": -0.5}, {"name": "yj1", "parent": "bone48", "rotation": 159.18, "x": 22.36, "y": 1.06, "color": "ff3f00ff"}, {"name": "bone38", "parent": "yj1", "length": 15.12, "rotation": 29.57}, {"name": "bone39", "parent": "bone35", "length": 41.89, "rotation": 2.55, "x": -0.64, "y": -1.78}, {"name": "bone40", "parent": "bone39", "length": 41.29, "rotation": -39.6, "x": 41.89, "color": "abe323ff"}, {"name": "bone41", "parent": "bone34", "x": 10.82, "y": 21.82}, {"name": "bone42", "parent": "bone41", "length": 40.55, "rotation": 15.62}, {"name": "bone43", "parent": "bone42", "length": 33.34, "rotation": -2.6, "x": 40.55}, {"name": "bone49", "parent": "bone", "length": 24.64, "rotation": -0.82, "x": 48.17, "y": 90.68}, {"name": "bone50", "parent": "bone49", "length": 12.85, "rotation": 100.28, "x": -0.35, "y": -0.01}, {"name": "zj1", "parent": "bone50", "rotation": -178.51, "x": 16.94, "y": 3.24, "color": "ff3f00ff"}, {"name": "bone44", "parent": "zj1", "length": 16.55, "rotation": 40.96}, {"name": "bone45", "parent": "bone41", "length": 41.5, "rotation": 34.95, "x": 0.7, "y": 0.03}, {"name": "bone46", "parent": "bone45", "length": 36.93, "rotation": -43.87, "x": 41.5, "color": "abe323ff"}, {"name": "yj", "parent": "bone48", "rotation": 160.29, "x": 59.27, "y": -13.47, "color": "ff3f00ff"}, {"name": "zj", "parent": "bone50", "rotation": -175.91, "x": 50.27, "y": 4.1, "color": "ff3f00ff"}, {"name": "bone51", "parent": "bone4", "length": 15.66, "rotation": 130.74, "x": -16.07, "y": 8.78}, {"name": "bone52", "parent": "bone51", "length": 11.66, "rotation": -18.16, "x": 15.66}, {"name": "bone53", "parent": "bone4", "length": 16.61, "rotation": -173.92, "x": -10.65, "y": -16.08}, {"name": "bone54", "parent": "bone53", "length": 11.29, "rotation": -4.24, "x": 16.61}, {"name": "bone55", "parent": "bone34", "length": 24.93, "rotation": -43.5, "x": 3.96, "y": -57.39}, {"name": "bone56", "parent": "bone55", "length": 14.17, "rotation": -6.43, "x": 24.68, "y": -0.25}, {"name": "bone57", "parent": "bone34", "length": 17.73, "rotation": 38.26, "x": 7.02, "y": 41.55}, {"name": "bone58", "parent": "bone57", "length": 12.19, "rotation": 40.57, "x": 17.73}, {"name": "bone59", "parent": "bone34", "x": 13.05, "y": -39.88, "color": "ff9717ff"}, {"name": "bone60", "parent": "bone59", "length": 40.55, "rotation": 15.62, "color": "ff9717ff"}, {"name": "bone61", "parent": "bone60", "length": 33.34, "rotation": -2.6, "x": 40.55, "color": "ff9717ff"}, {"name": "bone62", "parent": "bone59", "length": 41.5, "rotation": 34.95, "x": 0.7, "y": 0.03, "color": "ff9717ff"}, {"name": "bone63", "parent": "bone62", "length": 36.93, "rotation": -43.87, "x": 41.5, "color": "ff9717ff"}, {"name": "bone64", "parent": "bone", "length": 24.64, "rotation": -0.82, "x": -13.43, "y": 90.68, "color": "1eff00ff"}, {"name": "bone65", "parent": "bone64", "length": 12.85, "rotation": 100.28, "x": -0.35, "y": -0.01, "color": "1eff00ff"}, {"name": "zj2", "parent": "bone65", "rotation": -178.51, "x": 16.94, "y": 3.24, "color": "ff3f00ff"}, {"name": "bone66", "parent": "zj2", "length": 16.55, "rotation": 40.96}, {"name": "zj3", "parent": "bone65", "rotation": -175.91, "x": 50.27, "y": 4.1, "color": "ff3f00ff"}, {"name": "tx", "parent": "root", "y": -83.58}, {"name": "<PERSON><PERSON><PERSON>", "parent": "tx", "rotation": 0.83, "x": 19.64, "y": -60.43, "scaleX": 0.5711, "scaleY": 0.5711}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "tx", "y": 82.98}, {"name": "langhua", "parent": "tx", "x": -50.75, "y": -15.54}, {"name": "langhua2", "parent": "langhua", "x": 6.08, "y": -8.01}, {"name": "langhua3", "parent": "tx", "x": -163.03, "y": -13.94, "scaleX": -1}, {"name": "langhua4", "parent": "langhua3", "x": 6.08, "y": -8.01}, {"name": "runall", "parent": "root", "length": 100, "x": -100.62, "y": -52.43, "scaleX": 1.1403, "scaleY": 1.1403}, {"name": "runshui1", "parent": "runall", "rotation": 179.8, "x": 119.76, "y": 120.24, "scaleX": 2.5781, "scaleY": 2.5781}, {"name": "runshui2", "parent": "runall", "x": 119.76, "y": 120.24, "scaleX": 1.8261, "scaleY": 1.8261}, {"name": "runall2", "parent": "runall", "x": 112.75, "y": 121.13}, {"name": "runall3", "parent": "runall", "rotation": 91.03, "x": 84.18, "y": 110.33, "scaleX": 4.8248, "scaleY": 4.1404}, {"name": "tx1", "parent": "root", "x": -37.03, "y": 131.19}, {"name": "jineng2", "parent": "tx1", "x": 48.62, "y": -33.51, "scaleX": -2.4104, "scaleY": 2.4104}, {"name": "jineng4", "parent": "tx1", "rotation": -175.6, "x": 44.82, "y": 49.63, "scaleX": -2.6303, "scaleY": 2.071}, {"name": "jineng3", "parent": "tx1", "rotation": 49.98, "x": 17.44, "y": -303.46, "scaleX": -2.1841, "scaleY": 1.9576}, {"name": "shuiliuytx", "parent": "root", "x": 11.96, "y": 40.58, "scaleY": 1.3876}, {"name": "shuiliuytx2", "parent": "root", "x": -6.77, "y": 117.06}], "slots": [{"name": "tx/shuiboquan_00000", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "a13", "bone": "bone42", "attachment": "a13"}, {"name": "a12", "bone": "bone43", "attachment": "a12"}, {"name": "a26", "bone": "bone60", "color": "ffffff00", "attachment": "a13"}, {"name": "a27", "bone": "bone61", "color": "ffffff00", "attachment": "a12"}, {"name": "a11", "bone": "bone36", "attachment": "a11"}, {"name": "a10", "bone": "bone37", "attachment": "a10"}, {"name": "a9", "bone": "bone34", "attachment": "a9"}, {"name": "a18", "bone": "bone20", "attachment": "a18"}, {"name": "a7", "bone": "bone21", "attachment": "a7"}, {"name": "a6", "bone": "bone22", "attachment": "a6"}, {"name": "a25", "bone": "a19", "attachment": "a25"}, {"name": "a24", "bone": "a0", "attachment": "a24"}, {"name": "a23", "bone": "a1", "attachment": "a23"}, {"name": "a22", "bone": "a22", "attachment": "a22"}, {"name": "a21", "bone": "a22", "attachment": "a21"}, {"name": "a20", "bone": "a20", "attachment": "a20"}, {"name": "a19", "bone": "a19", "attachment": "a19"}, {"name": "a17", "bone": "bone24", "attachment": "a17"}, {"name": "a16", "bone": "bone25", "attachment": "a16"}, {"name": "a15", "bone": "bone23", "attachment": "a15"}, {"name": "a14", "bone": "bone5", "attachment": "a14"}, {"name": "shuilang_add/xhs_01", "bone": "shuiliuytx", "blend": "additive"}, {"name": "a28", "bone": "bone5", "color": "ffffff00", "attachment": "a14"}, {"name": "a8", "bone": "bone4", "attachment": "a8"}, {"name": "a5", "bone": "bone31", "attachment": "a5"}, {"name": "a4", "bone": "bone6", "attachment": "a4"}, {"name": "a2", "bone": "bone6", "attachment": "a2"}, {"name": "a3", "bone": "bone6", "color": "ffffff00", "attachment": "a3"}, {"name": "a1", "bone": "a1", "attachment": "a1"}, {"name": "a0", "bone": "a0", "attachment": "a0"}, {"name": "tx/dg_0001", "bone": "<PERSON><PERSON><PERSON>", "blend": "additive"}, {"name": "tx/1_00006", "bone": "langhua"}, {"name": "tx/1_6", "bone": "langhua3"}, {"name": "tx/1_00007", "bone": "langhua2"}, {"name": "tx/1_7", "bone": "langhua4"}, {"name": "run1/quan20", "bone": "runshui1"}, {"name": "run1/huoq_02", "bone": "runshui2"}, {"name": "run1/dd_2", "bone": "runall2", "color": "ffffff53", "blend": "additive"}, {"name": "runwl/A_wenli_wave_00000", "bone": "runall3", "color": "0084ffff", "blend": "additive"}, {"name": "runwl/A_wenli_wave_0", "bone": "runall3", "color": "0058ffff", "blend": "additive"}, {"name": "lshuo01_add/lshuo01_add_001", "bone": "jineng2", "dark": "002cff", "blend": "additive"}, {"name": "lshuo01_add/lshuo01_add_2", "bone": "jineng3", "dark": "002cff", "blend": "additive"}, {"name": "lshuo01_add/lshuo01_add_1", "bone": "jineng4", "dark": "002cff", "blend": "additive"}, {"name": "nuqixunhuan04-add/uvxh_01", "bone": "shuiliuytx2", "blend": "additive"}], "ik": [{"name": "j1", "order": 10, "bones": ["bone60"], "target": "zj3", "compress": true, "stretch": true}, {"name": "j2", "order": 11, "bones": ["bone61"], "target": "zj2", "compress": true, "stretch": true}, {"name": "j3", "order": 8, "bones": ["bone62", "bone63"], "target": "zj2", "bendPositive": false}, {"name": "yj", "order": 2, "bones": ["bone36"], "target": "yj", "compress": true, "stretch": true}, {"name": "yj1", "order": 3, "bones": ["bone37"], "target": "yj1", "compress": true, "stretch": true}, {"name": "yj2", "bones": ["bone39", "bone40"], "target": "yj1", "bendPositive": false}, {"name": "zj", "order": 6, "bones": ["bone42"], "target": "zj", "compress": true, "stretch": true}, {"name": "zj1", "order": 7, "bones": ["bone43"], "target": "zj1", "compress": true, "stretch": true}, {"name": "zj2", "order": 4, "bones": ["bone45", "bone46"], "target": "zj1", "bendPositive": false}], "transform": [{"name": "j", "order": 9, "bones": ["zj3"], "target": "bone63", "rotation": 23.27, "x": 5.74, "y": -11.77, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "q1", "order": 14, "bones": ["bone23"], "target": "bone30", "rotation": 136.66, "x": 12.47, "y": 107.42, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "q2", "order": 15, "bones": ["bone20"], "target": "bone30", "rotation": -139.99, "x": 7.72, "y": -56.24, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "s01", "order": 12, "bones": ["bone30"], "target": "bone29", "x": 48.39, "y": 0.58, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "t01", "order": 13, "bones": ["bone28"], "target": "bone27", "x": 49.39, "y": 12.27, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "w1", "order": 16, "bones": ["bone31"], "target": "bone29", "rotation": -164.54, "x": 113.84, "y": 29.5, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "yj", "order": 1, "bones": ["yj"], "target": "bone40", "rotation": 18.92, "x": 3.39, "y": -11.7, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "zj", "order": 5, "bones": ["zj"], "target": "bone46", "rotation": 23.44, "x": 5.77, "y": -11.86, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}], "skins": [{"name": "default", "attachments": {"a10": {"a10": {"type": "mesh", "uvs": [0.41491, 0.02123, 0.68164, 0.02041, 0.8323, 0.15362, 0.91959, 0.25954, 0.81699, 0.53632, 0.97649, 0.73522, 0.97598, 0.89262, 0.90296, 0.97907, 0.6798, 0.97889, 0.39319, 0.97865, 0.11386, 0.95644, 0.02412, 0.8394, 0.02371, 0.71647, 0.14915, 0.49489, 0.17275, 0.2832, 0.19462, 0.08695, 0.5285, 0.27569, 0.50099, 0.61286, 0.76923, 0.8121, 0.22586, 0.79064, 0.49067, 0.82129], "triangles": [17, 16, 4, 17, 13, 16, 19, 13, 17, 12, 13, 19, 18, 17, 4, 18, 4, 5, 20, 19, 17, 20, 17, 18, 11, 12, 19, 6, 18, 5, 10, 11, 19, 9, 19, 20, 10, 19, 9, 8, 20, 18, 9, 20, 8, 7, 18, 6, 8, 18, 7, 16, 0, 1, 16, 1, 2, 3, 4, 16, 3, 16, 2, 16, 15, 0, 16, 14, 15, 16, 13, 14], "vertices": [1, 42, 19.83, -11.87, 1, 1, 42, 16.11, -1.59, 1, 1, 42, 19.8, 6.29, 1, 2, 42, 23.19, 11.3, 0.99262, 46, -8.92, 18.06, 0.00738, 2, 42, 36.59, 11.63, 0.45802, 46, 2.9, 11.73, 0.54198, 2, 42, 43, 20.87, 0.02907, 46, 13.04, 16.6, 0.97093, 2, 42, 49.83, 23.29, 0.0012, 46, 20.17, 15.33, 0.9988, 1, 46, 23.57, 11.7, 1, 1, 46, 21.99, 2.69, 1, 1, 46, 19.96, -8.88, 1, 1, 46, 16.99, -19.99, 1, 2, 42, 60.66, -14.28, 0.00618, 46, 11.05, -22.69, 0.99382, 2, 42, 55.35, -16.2, 0.03751, 46, 5.48, -21.74, 0.96249, 2, 42, 44.02, -14.79, 0.41773, 46, -3.68, -14.92, 0.58227, 2, 42, 34.52, -17.16, 0.90404, 46, -13.11, -12.29, 0.09596, 2, 42, 25.72, -19.36, 0.9983, 46, -21.85, -9.86, 0.0017, 1, 42, 29.28, -3.54, 1, 1, 46, 4.14, -1.64, 1, 2, 42, 49.19, 14.06, 0.00887, 46, 15.06, 7.62, 0.99113, 2, 42, 55.77, -7.25, 0.01049, 46, 10.26, -14.16, 0.98951, 1, 46, 13.52, -3.7, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 2, 32, 32, 34, 12, 36, 22, 38, 34, 40, 14, 16, 16, 18], "width": 41, "height": 46}}, "a11": {"a11": {"type": "mesh", "uvs": [0.75589, 0.08773, 0.86325, 0.17085, 0.95243, 0.37843, 0.95284, 0.51776, 0.90543, 0.64077, 0.90494, 0.81504, 0.85778, 0.88851, 0.62097, 0.98172, 0.37813, 0.98173, 0.31084, 0.96381, 0.21569, 0.89132, 0.13134, 0.76081, 0.04735, 0.49988, 0.04724, 0.40772, 0.16453, 0.18337, 0.2389, 0.07498, 0.37364, 0.01813, 0.61971, 0.01829, 0.4731, 0.71941, 0.60311, 0.27871, 0.53768, 0.5158, 0.18203, 0.52925, 0.29458, 0.25154, 0.7273, 0.76469, 0.7991, 0.45378], "triangles": [19, 16, 17, 19, 17, 0, 22, 16, 19, 24, 0, 1, 24, 1, 2, 19, 0, 24, 20, 22, 19, 20, 19, 24, 24, 2, 3, 4, 24, 3, 23, 20, 24, 23, 24, 4, 18, 20, 23, 5, 23, 4, 6, 23, 5, 7, 23, 6, 22, 15, 16, 14, 15, 22, 21, 12, 13, 21, 14, 22, 18, 21, 22, 21, 13, 14, 20, 18, 22, 11, 12, 21, 10, 11, 21, 18, 10, 21, 9, 10, 18, 7, 18, 23, 8, 9, 18, 7, 8, 18], "vertices": [1, 41, 12.18, 13.5, 1, 1, 41, 13.57, 23.48, 1, 2, 42, -17.42, 34.27, 0.00012, 41, 21.81, 34.15, 0.99988, 2, 42, -10.35, 36.84, 0.00638, 41, 28.93, 36.58, 0.99362, 2, 42, -2.76, 35.32, 0.0432, 41, 36.49, 34.92, 0.9568, 2, 42, 6.12, 38.45, 0.10967, 41, 45.43, 37.88, 0.89033, 2, 42, 11.19, 36.06, 0.14605, 41, 50.45, 35.38, 0.85395, 2, 42, 22.63, 19.03, 0.60784, 41, 61.55, 18.13, 0.39216, 1, 42, 29.5, -0.18, 1, 2, 42, 30.49, -5.83, 0.99989, 41, 68.93, -6.87, 0.00011, 2, 42, 29.49, -14.67, 0.99995, 41, 67.77, -15.7, 5e-05, 2, 42, 25.24, -23.72, 0.99999, 41, 63.34, -24.66, 1e-05, 2, 42, 14.35, -35.11, 0.9984, 41, 52.23, -35.83, 0.0016, 2, 42, 9.67, -36.79, 0.99249, 41, 47.52, -37.42, 0.00751, 2, 42, -5.05, -31.59, 0.87023, 41, 32.9, -31.94, 0.12977, 2, 42, -12.67, -27.68, 0.72548, 41, 25.36, -27.89, 0.27452, 2, 42, -19.37, -18.06, 0.41816, 41, 18.85, -18.13, 0.58184, 1, 41, 12.27, 1.46, 1, 2, 42, 13.47, 2.56, 0.95674, 41, 52.08, 1.85, 0.04326, 1, 41, 26.05, 4.62, 1, 2, 42, 1.29, 3.97, 0.17647, 41, 39.93, 3.49, 0.82353, 2, 42, 12.04, -23.92, 0.99659, 41, 50.13, -24.6, 0.00341, 2, 42, -5.27, -20.07, 0.78105, 41, 32.91, -20.42, 0.21895, 2, 42, 8.58, 23.49, 0.23514, 41, 47.6, 22.87, 0.76486, 2, 42, -9.26, 23.51, 0.00631, 41, 29.77, 23.24, 0.99369], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 16, 36, 36, 40, 40, 38, 26, 42, 42, 44, 10, 46, 46, 48], "width": 84, "height": 54}}, "a12": {"a12": {"type": "mesh", "uvs": [0.28942, 0.01471, 0.55598, 0, 0.5772, 0.29, 0.55801, 0.55174, 0.63585, 0.59523, 0.8119, 0.56209, 0.92184, 0.67411, 0.99194, 0.90896, 0.86572, 0.97953, 0.60497, 0.9791, 0.34276, 0.8962, 0.05789, 0.89264, 0.05643, 0.71148, 0.11485, 0.57413, 0.01877, 0.27677, 0.0188, 0.15284, 0.31726, 0.29299, 0.34688, 0.65043], "triangles": [3, 17, 16, 12, 10, 11, 17, 12, 13, 17, 10, 12, 17, 3, 4, 4, 10, 17, 9, 10, 4, 6, 4, 5, 8, 6, 7, 8, 4, 6, 9, 4, 8, 16, 0, 1, 16, 1, 2, 15, 0, 16, 14, 15, 16, 3, 16, 2, 13, 14, 16, 17, 13, 16], "vertices": [1, 51, 7.76, 2.61, 1, 2, 51, 9.71, 16.35, 0.99967, 55, -7.13, 27.83, 0.00033, 2, 51, 23.31, 14.84, 0.88012, 55, 2.15, 17.79, 0.11988, 2, 51, 35.19, 11.52, 0.17454, 55, 8.95, 7.49, 0.82546, 2, 51, 37.97, 15.11, 0.01832, 55, 13.4, 8.38, 0.98168, 1, 55, 19.64, 15.25, 1, 1, 55, 27.39, 14.63, 1, 1, 55, 37.07, 8.19, 1, 1, 55, 33.95, 1.54, 1, 1, 55, 23.26, -6.81, 1, 2, 51, 48.96, -2.54, 0.02227, 55, 10.13, -12.16, 0.97773, 2, 51, 45.98, -17.05, 0.25628, 55, -1.63, -21.16, 0.74372, 2, 51, 37.61, -15.51, 0.41133, 55, -6.95, -14.51, 0.58867, 2, 51, 31.85, -11.3, 0.7923, 55, -8.54, -7.55, 0.2077, 1, 51, 17.18, -13.55, 1, 1, 51, 11.46, -12.44, 1, 2, 51, 20.88, 1.54, 0.99646, 55, -8.4, 9.34, 0.00354, 1, 55, 3.17, -2.93, 1], "hull": 16, "edges": [0, 30, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 32, 32, 34, 2, 4, 4, 6, 0, 2], "width": 52, "height": 47}}, "a13": {"a13": {"type": "mesh", "uvs": [0.56442, 0.02455, 0.84692, 0, 0.93633, 0.1871, 0.98569, 0.40103, 0.9861, 0.54953, 0.78789, 0.89132, 0.60317, 0.97544, 0.35213, 0.97543, 0.11587, 0.97543, 0.04199, 0.91208, 0.00754, 0.83532, 0.03056, 0.44368, 0.06716, 0.31745, 0.22258, 0.18086, 0.37508, 0.09022, 0.74187, 0.40775, 0.49065, 0.55355, 0.20891, 0.6224], "triangles": [15, 0, 1, 16, 14, 0, 16, 0, 15, 17, 12, 13, 11, 12, 17, 13, 14, 16, 17, 13, 16, 10, 11, 17, 9, 10, 17, 8, 9, 17, 7, 17, 16, 8, 17, 7, 15, 1, 2, 15, 2, 3, 15, 3, 4, 5, 15, 4, 6, 16, 15, 6, 7, 16, 15, 5, 6], "vertices": [2, 51, -20.45, 7.96, 0.06406, 50, 20.49, 8.88, 0.93594, 2, 51, -17.71, 27.28, 0.40268, 50, 24.1, 28.06, 0.59732, 2, 51, -9.19, 31.92, 0.53706, 50, 32.82, 32.3, 0.46294, 2, 51, -0.14, 33.64, 0.67723, 50, 41.94, 33.61, 0.32277, 2, 51, 5.7, 32.53, 0.73788, 50, 47.72, 32.24, 0.26212, 2, 51, 16.52, 16.51, 0.97172, 50, 57.81, 15.74, 0.02828, 1, 51, 17.4, 3.36, 1, 2, 51, 14.11, -13.65, 0.61209, 50, 54.03, -14.28, 0.38791, 2, 51, 11.01, -29.65, 0.20706, 50, 50.21, -30.12, 0.79294, 2, 51, 7.56, -34.18, 0.16189, 50, 46.55, -34.48, 0.83811, 2, 51, 4.09, -35.93, 0.14309, 50, 43.01, -36.08, 0.85691, 2, 51, -10.99, -31.39, 0.03709, 50, 28.15, -30.86, 0.96291, 2, 51, -15.46, -27.95, 0.01638, 50, 23.84, -27.22, 0.98362, 1, 50, 21.04, -15.52, 1, 1, 50, 19.98, -4.44, 1, 2, 51, -3.07, 17.07, 0.57239, 50, 38.26, 17.19, 0.42761, 2, 51, -0.64, -1.06, 0.00716, 50, 39.87, -1.03, 0.99284, 2, 51, -1.63, -20.67, 0.13199, 50, 37.99, -20.57, 0.86801], "hull": 15, "edges": [0, 28, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 12, 14, 14, 16, 2, 30, 30, 32, 32, 34, 0, 2], "width": 69, "height": 40}}, "nuqixunhuan04-add/uvxh_01": {"nuqixunhuan04-add/uvxh_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114, -147, -114, -147, -114, 147, 114, 147], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 228, "height": 294}, "nuqixunhuan04-add/uvxh_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114, -147, -114, -147, -114, 147, 114, 147], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 228, "height": 294}, "nuqixunhuan04-add/uvxh_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114, -147, -114, -147, -114, 147, 114, 147], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 228, "height": 294}, "nuqixunhuan04-add/uvxh_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114, -147, -114, -147, -114, 147, 114, 147], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 228, "height": 294}, "nuqixunhuan04-add/uvxh_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114, -147, -114, -147, -114, 147, 114, 147], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 228, "height": 294}, "nuqixunhuan04-add/uvxh_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114, -147, -114, -147, -114, 147, 114, 147], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 228, "height": 294}, "nuqixunhuan04-add/uvxh_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [114, -147, -114, -147, -114, 147, 114, 147], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 228, "height": 294}}, "a15": {"a15": {"type": "mesh", "uvs": [0.7853, 0.00981, 0.84639, 0.03185, 0.93933, 0.09616, 0.9891, 0.24603, 0.98959, 0.48913, 0.93229, 0.6454, 0.90011, 0.70101, 0.7475, 0.84018, 0.73381, 0.8916, 0.66609, 0.92218, 0.58257, 0.95007, 0.57236, 0.99026, 0.42756, 0.99023, 0.36986, 0.97214, 0.19931, 0.86729, 0.04269, 0.65363, 0.0106, 0.51066, 0.01078, 0.33438, 0.05671, 0.23084, 0.1294, 0.21986, 0.21961, 0.12264, 0.30287, 0.06523, 0.41004, 0.07844, 0.63788, 0.00979, 0.73751, 0.3069, 0.51123, 0.46914, 0.28494, 0.60954, 0.22665, 0.32562, 0.5078, 0.80298], "triangles": [14, 15, 26, 13, 14, 28, 12, 13, 28, 11, 12, 10, 24, 23, 0, 24, 0, 1, 24, 1, 2, 24, 2, 3, 22, 23, 24, 27, 20, 21, 19, 20, 27, 25, 22, 24, 24, 3, 4, 17, 18, 19, 27, 17, 19, 27, 25, 26, 22, 27, 21, 25, 27, 22, 5, 24, 4, 25, 24, 5, 27, 16, 17, 26, 16, 27, 26, 15, 16, 6, 25, 5, 28, 26, 25, 7, 28, 25, 6, 7, 25, 14, 26, 28, 7, 9, 28, 8, 9, 7, 10, 28, 9, 10, 12, 28], "vertices": [1, 23, -10.04, -25.47, 1, 1, 23, -13.3, -20.45, 1, 1, 23, -16.46, -10.31, 1, 1, 23, -11.47, 4.53, 1, 2, 23, 2.57, 24.37, 0.99792, 24, -44.05, 55.59, 0.00208, 2, 23, 15.88, 34.09, 0.97487, 24, -27.63, 56.92, 0.02513, 2, 23, 21.49, 36.93, 0.9569, 24, -21.36, 56.41, 0.0431, 2, 23, 40.87, 40.22, 0.80595, 24, -3.12, 49.09, 0.19405, 2, 23, 44.86, 43.69, 0.75766, 24, 2.1, 49.96, 0.24234, 2, 23, 51.66, 42.62, 0.69595, 24, 7.33, 45.49, 0.30405, 2, 23, 59.47, 40.49, 0.57051, 24, 12.88, 39.59, 0.42949, 2, 23, 62.55, 43.22, 0.52954, 24, 16.94, 40.31, 0.47046, 2, 23, 73.29, 35.59, 0.39217, 24, 22.1, 28.19, 0.60783, 2, 23, 76.52, 31.07, 0.3263, 24, 22.5, 22.65, 0.6737, 2, 23, 83.1, 13.53, 0.0363, 24, 18.94, 4.26, 0.9637, 2, 23, 82.34, -12.14, 0.24786, 24, 4.88, -17.23, 0.75214, 2, 23, 76.44, -25.48, 0.70121, 24, -7.13, -25.52, 0.29879, 2, 23, 66.21, -39.84, 0.93585, 24, -23.35, -32.42, 0.06415, 2, 23, 56.81, -45.86, 0.97467, 24, -34.51, -32.64, 0.02533, 2, 23, 50.78, -42.92, 0.98699, 24, -38.12, -26.98, 0.01301, 2, 23, 38.45, -46.09, 0.99986, 24, -50.28, -23.24, 0.00014, 1, 23, 28.95, -46.38, 1, 1, 23, 21.77, -39.66, 1, 1, 23, 0.89, -33.24, 1, 1, 23, 10.71, -3.77, 1, 1, 23, 36.9, -2.48, 1, 1, 23, 61.82, -2.96, 1, 2, 23, 49.69, -29.18, 0.98838, 24, -31.86, -14.69, 0.01162, 2, 23, 56.49, 24.55, 0.6574, 24, 2.02, 27.56, 0.3426], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 4, 48, 48, 50, 50, 52, 42, 54, 14, 56], "width": 91, "height": 100}}, "a16": {"a16": {"x": 22.08, "y": -0.68, "rotation": 104.04, "width": 52, "height": 48}}, "a17": {"a17": {"type": "mesh", "uvs": [0.45737, 0.00945, 0.60282, 0.00927, 0.73996, 0.05711, 0.83446, 0.12191, 0.93675, 0.22928, 0.9865, 0.33297, 0.98672, 0.52626, 0.93919, 0.60443, 0.78048, 0.715, 0.78168, 0.80063, 0.7197, 0.84216, 0.70945, 0.8918, 0.50683, 0.96214, 0.28757, 0.96202, 0.20856, 0.94412, 0.12296, 0.91442, 0.02718, 0.84761, 0.02718, 0.80297, 0.00855, 0.68516, 0.04408, 0.54918, 0.15732, 0.47201, 0.19534, 0.30339, 0.25963, 0.1746, 0.24626, 0.14332, 0.24638, 0.07618, 0.26702, 0.04736, 0.36447, 0.04739, 0.62874, 0.28644, 0.50731, 0.51331, 0.42383, 0.67688, 0.35933, 0.82725, 0.35553, 0.14662], "triangles": [13, 30, 12, 13, 14, 30, 14, 15, 30, 30, 15, 17, 15, 16, 17, 5, 27, 4, 27, 3, 4, 27, 2, 3, 27, 1, 2, 11, 12, 10, 10, 12, 30, 30, 29, 10, 10, 8, 9, 10, 29, 8, 29, 30, 18, 30, 17, 18, 29, 28, 8, 8, 28, 7, 18, 19, 29, 19, 20, 29, 29, 20, 28, 7, 28, 6, 27, 6, 28, 6, 27, 5, 20, 21, 28, 28, 21, 27, 31, 27, 21, 31, 21, 22, 1, 27, 31, 31, 26, 0, 31, 0, 1, 22, 23, 31, 23, 24, 31, 26, 31, 25, 31, 24, 25], "vertices": [2, 24, -17.35, -27.24, 0.51993, 23, 68.62, -32.29, 0.48007, 2, 24, -21.53, -17.48, 0.32605, 23, 59.96, -26.16, 0.67395, 2, 24, -20.83, -6.3, 0.11384, 23, 54.71, -16.26, 0.88616, 2, 24, -17.28, 2.71, 0.00563, 23, 53.03, -6.72, 0.99437, 2, 24, -9.84, 14, 0.06762, 23, 53.47, 6.8, 0.93238, 2, 24, -1.25, 21.61, 0.3927, 23, 56.82, 17.78, 0.6073, 2, 24, 17.42, 29.59, 0.84964, 23, 68.56, 34.33, 0.15036, 3, 24, 26.33, 29.61, 0.92225, 23, 76.15, 39.01, 0.07743, 25, -33.88, 35.39, 0.00032, 3, 24, 41.55, 23.51, 0.94367, 23, 92.32, 41.76, 0.00702, 25, -19.8, 26.96, 0.04931, 3, 24, 49.79, 27.12, 0.85355, 23, 97.45, 49.14, 0.00025, 25, -11.1, 29.23, 0.1462, 2, 24, 55.57, 24.67, 0.74917, 25, -5.77, 25.9, 0.25083, 2, 24, 60.66, 26.02, 0.64505, 25, -0.53, 26.43, 0.35495, 2, 24, 73.26, 15.31, 0.18055, 25, 10.22, 13.88, 0.81945, 1, 25, 14.09, -1.65, 1, 2, 24, 80.06, -5.46, 0.00481, 25, 13.66, -7.71, 0.99519, 2, 24, 79.64, -12.43, 0.07196, 25, 12.15, -14.52, 0.92804, 2, 24, 75.93, -21.61, 0.24454, 25, 7.04, -23.01, 0.75546, 2, 24, 71.62, -23.45, 0.34736, 25, 2.5, -24.15, 0.65264, 2, 24, 60.77, -29.55, 0.68403, 25, -9.17, -28.47, 0.31597, 2, 24, 46.62, -32.77, 0.89388, 25, -23.66, -29.41, 0.10612, 2, 24, 35.92, -28.34, 0.97418, 25, -33.52, -23.36, 0.02582, 2, 24, 18.55, -32.73, 0.98395, 23, 102.1, -18.22, 0.01605, 2, 24, 4.27, -33.72, 0.84892, 23, 90.44, -26.52, 0.15108, 2, 24, 1.63, -35.91, 0.80424, 23, 89.33, -29.76, 0.19576, 2, 24, -4.86, -38.66, 0.75073, 23, 85.24, -35.5, 0.24927, 2, 24, -8.23, -38.46, 0.7379, 23, 82.26, -37.1, 0.2621, 2, 24, -11.02, -31.92, 0.66548, 23, 76.46, -32.97, 0.33452, 1, 24, 4.5, -4.33, 1, 1, 24, 29.89, -3.14, 1, 2, 24, 48.08, -2.01, 0.99978, 25, -17.37, 0.73, 0.00022, 2, 24, 64.45, -0.15, 0.86264, 25, -0.91, 0, 0.13736, 2, 24, -1.18, -28.43, 0.77902, 23, 83.03, -24.86, 0.22098], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 2, 54, 54, 56, 56, 58, 58, 60, 50, 62], "width": 73, "height": 105}}, "a18": {"a18": {"type": "mesh", "uvs": [0.61031, 0, 0.73091, 0.24555, 0.83874, 0.23276, 0.85162, 0.25889, 0.85094, 0.35244, 0.88836, 0.45391, 0.94133, 0.54532, 0.99044, 0.58492, 0.9895, 0.62416, 0.93567, 0.75278, 0.81229, 0.88704, 0.69258, 0.96296, 0.52607, 1, 0.2921, 0.98961, 0.21527, 0.89965, 0.09515, 0.83833, 0.01034, 0.7525, 0.01048, 0.54514, 0.05298, 0.42286, 0.13786, 0.27097, 0.21801, 0.17615, 0.33753, 0.08176, 0.47847, 0.01084, 0.37843, 0.42109, 0.52178, 0.63975, 0.61822, 0.78728, 0.2012, 0.61867, 0.57391, 0.31045], "triangles": [11, 25, 10, 12, 13, 25, 11, 12, 25, 27, 22, 0, 27, 0, 1, 21, 22, 27, 3, 1, 2, 4, 1, 3, 23, 21, 27, 20, 21, 23, 19, 20, 23, 18, 19, 23, 26, 18, 23, 17, 18, 26, 8, 6, 7, 24, 23, 27, 26, 23, 24, 16, 17, 26, 6, 25, 5, 9, 6, 8, 24, 27, 5, 4, 27, 1, 5, 27, 4, 9, 25, 6, 25, 24, 5, 15, 16, 26, 10, 25, 9, 14, 26, 24, 13, 14, 24, 15, 26, 14, 25, 13, 24], "vertices": [1, 20, -4.24, 36.27, 1, 1, 20, 21.23, 35.22, 1, 1, 20, 25.07, 44.68, 1, 1, 20, 27.78, 44.57, 1, 2, 20, 35.38, 40.33, 0.99885, 21, -42.77, 20.17, 0.00115, 2, 20, 45.34, 38.87, 0.98008, 21, -33.64, 24.42, 0.01992, 2, 20, 55.19, 39.15, 0.92533, 21, -25.56, 30.06, 0.07467, 2, 20, 60.64, 41.43, 0.89779, 21, -22.25, 34.95, 0.10221, 2, 20, 63.8, 39.59, 0.88443, 21, -18.6, 35.15, 0.11557, 2, 20, 71.85, 29.4, 0.74897, 21, -6.28, 31.05, 0.25103, 2, 20, 77.22, 13.23, 0.33007, 21, 7.08, 20.47, 0.66993, 2, 20, 77.99, -0.03, 0.01914, 21, 15.01, 9.81, 0.98086, 2, 20, 73.48, -15.41, 0.05946, 21, 19.68, -5.52, 0.94054, 2, 20, 62.06, -34.23, 0.49379, 21, 20.45, -27.52, 0.50621, 2, 20, 51.25, -36.54, 0.68157, 21, 12.68, -35.38, 0.31843, 2, 20, 40.82, -43.69, 0.84308, 21, 7.88, -47.09, 0.15692, 2, 20, 29.99, -46.84, 0.90155, 21, 0.56, -55.66, 0.09845, 2, 20, 13.09, -37.55, 0.96865, 21, -18.67, -57.17, 0.03135, 2, 20, 5.04, -28.58, 0.99118, 21, -30.32, -54.09, 0.00882, 2, 20, -3.51, -14.79, 0.99996, 21, -45.03, -47.25, 4e-05, 1, 20, -7.62, -3.94, 1, 1, 20, -9.91, 10.13, 1, 1, 20, -9.32, 24.92, 1, 2, 20, 19.61, -1.68, 0.99992, 21, -32.9, -23.6, 8e-05, 1, 20, 43.92, 0.35, 1, 2, 20, 60.31, 1.7, 0.59293, 21, -0.73, 1.55, 0.40707, 2, 20, 27.7, -25.13, 0.94424, 21, -13.27, -38.76, 0.05576, 1, 20, 19.43, 19.38, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 40, 46, 46, 48, 48, 50, 46, 52, 46, 54], "width": 94, "height": 93}}, "a19": {"a19": {"x": 20.36, "y": -0.34, "rotation": 146.1, "width": 44, "height": 37}}, "tx/1_7": {"tx/1_00007": {"x": -5.58, "y": 8.01, "width": 113, "height": 60}, "tx/1_00008": {"x": -5.58, "y": 8.01, "width": 113, "height": 60}}, "a22": {"a22": {"x": -41.56, "y": 15.37, "rotation": -68.67, "width": 336, "height": 526}}, "lshuo01_add/lshuo01_add_2": {"lshuo01_add/lshuo01_add_001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}}, "shuilang_add/xhs_01": {"shuilang_add/xhs_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [129, -51, -128, -51, -128, 52, 129, 52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 257, "height": 103}, "shuilang_add/xhs_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [129, -51, -128, -51, -128, 52, 129, 52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 257, "height": 103}, "shuilang_add/xhs_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [129, -51, -128, -51, -128, 52, 129, 52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 257, "height": 103}, "shuilang_add/xhs_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [129, -51, -128, -51, -128, 52, 129, 52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 257, "height": 103}, "shuilang_add/xhs_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [129, -51, -128, -51, -128, 52, 129, 52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 257, "height": 103}}, "runwl/A_wenli_wave_0": {"runwl/A_wenli_wave_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}}, "a9": {"a9": {"type": "mesh", "uvs": [0.46902, 0.01319, 0.54747, 0.01319, 0.66562, 0.05734, 0.74204, 0.10746, 0.79102, 0.16112, 0.8264, 0.23889, 0.85873, 0.36505, 0.88978, 0.48621, 0.91481, 0.54065, 0.94657, 0.55452, 0.97666, 0.56766, 0.97558, 0.627, 0.92469, 0.77507, 0.82995, 0.85766, 0.72839, 0.94621, 0.62651, 0.94701, 0.52444, 0.92087, 0.40547, 0.98693, 0.30081, 0.98672, 0.21392, 0.94876, 0.12925, 0.87903, 0.08098, 0.77388, 0.0423, 0.64814, 0.04265, 0.58395, 0.0725, 0.5314, 0.10582, 0.47273, 0.13662, 0.4146, 0.18099, 0.33088, 0.20274, 0.24589, 0.22844, 0.14552, 0.3041, 0.05167, 0.37393, 0.01319, 0.28259, 0.24869, 0.30611, 0.46937, 0.34532, 0.66893, 0.38713, 0.79558, 0.45595, 0.86083, 0.37486, 0.35039, 0.46198, 0.50583, 0.55432, 0.61137, 0.66322, 0.65934, 0.7512, 0.66702, 0.84006, 0.65358, 0.91585, 0.62288, 0.25157, 0.40412, 0.21846, 0.5135, 0.14703, 0.71499, 0.17965, 0.6285, 0.24286, 0.79558], "triangles": [13, 42, 12, 42, 43, 12, 12, 43, 11, 42, 8, 43, 43, 9, 11, 11, 9, 10, 43, 8, 9, 14, 41, 13, 13, 41, 42, 42, 7, 8, 42, 6, 7, 21, 22, 46, 46, 22, 23, 23, 24, 46, 47, 24, 25, 17, 36, 16, 18, 35, 17, 17, 35, 36, 19, 48, 18, 35, 48, 34, 35, 18, 48, 19, 20, 48, 20, 46, 48, 20, 21, 46, 46, 47, 48, 47, 45, 48, 34, 45, 33, 33, 45, 44, 45, 34, 48, 46, 24, 47, 34, 33, 37, 25, 26, 47, 47, 26, 45, 26, 27, 45, 45, 27, 44, 33, 44, 32, 27, 28, 44, 14, 40, 41, 15, 40, 14, 35, 34, 38, 36, 35, 38, 34, 37, 38, 5, 6, 41, 37, 33, 32, 40, 3, 4, 30, 31, 37, 16, 39, 15, 15, 39, 40, 16, 36, 39, 36, 38, 39, 40, 2, 3, 40, 39, 2, 39, 38, 1, 38, 0, 1, 39, 1, 2, 38, 37, 0, 37, 31, 0, 44, 28, 32, 32, 30, 37, 28, 29, 32, 32, 29, 30, 41, 40, 4, 4, 5, 41, 42, 41, 6], "vertices": [1, 4, 13.8, 21.55, 1, 1, 4, 16.37, 9.03, 1, 1, 4, 17.03, -10.5, 1, 1, 4, 15.9, -23.44, 1, 1, 4, 13.61, -32.06, 1, 1, 4, 9.13, -38.87, 1, 2, 4, 1.04, -45.9, 0.816, 66, 1.88, 4.34, 0.184, 3, 4, -6.73, -52.66, 0.312, 66, 12.1, 3.13, 0.67453, 67, -2.24, 6.03, 0.01347, 2, 66, 17.76, 4.04, 0.30148, 67, 2.65, 3.05, 0.69852, 2, 66, 21.65, 7.61, 0.00245, 67, 7.93, 3.23, 0.99755, 1, 67, 12.92, 3.41, 1, 1, 67, 13.76, -0.9, 1, 4, 4, -26.53, -62.52, 0.00988, 64, -56.34, 105.55, 0.00019, 66, 32.71, -4.9, 0.0939, 67, 8.19, -13.47, 0.89603, 4, 4, -35.61, -48.62, 0.10622, 64, -41.04, 99.11, 0.00933, 66, 28.53, -20.98, 0.56719, 67, -5.44, -22.96, 0.31725, 4, 4, -45.35, -33.72, 0.27878, 64, -24.63, 92.2, 0.047, 66, 24.04, -38.21, 0.60701, 67, -20.05, -33.13, 0.0672, 4, 4, -48.74, -17.46, 0.41842, 64, -12.73, 80.61, 0.12986, 66, 14.28, -51.64, 0.44107, 67, -36.2, -36.99, 0.01065, 4, 4, -50.19, -0.77, 0.4629, 64, -2.2, 67.58, 0.31698, 66, 2.9, -63.93, 0.22001, 67, -52.84, -38.92, 0.00011, 3, 4, -58.87, 17.24, 0.33283, 64, 15.07, 57.5, 0.59519, 66, -4.61, -82.46, 0.07198, 3, 4, -62.27, 33.96, 0.2001, 64, 27.24, 45.54, 0.77391, 66, -14.7, -96.22, 0.026, 4, 4, -62.36, 48.4, 0.10489, 64, 35.39, 33.62, 0.88086, 65, 6.84, 34.86, 0.00577, 66, -25.33, -105.99, 0.00849, 4, 4, -60.08, 62.95, 0.0386, 64, 41.63, 20.28, 0.84784, 65, 14.54, 22.3, 0.11213, 66, -37.64, -114.08, 0.00143, 4, 4, -54.03, 72.22, 0.01122, 64, 41.8, 9.21, 0.56557, 65, 15.95, 11.32, 0.42313, 66, -48.57, -115.84, 8e-05, 3, 4, -46.18, 80.27, 1e-05, 64, 39.79, -1.85, 0.00105, 65, 15.19, 0.11, 0.99894, 3, 4, -41.52, 81.16, 0, 64, 36.42, -5.2, 2e-05, 65, 12.22, -3.6, 0.99998, 1, 65, 5.99, -3.67, 1, 1, 65, -0.96, -3.75, 1, 2, 64, 16.71, -3.42, 0.96721, 65, -7.57, -4.05, 0.03279, 2, 4, -18.65, 62.83, 0.472, 64, 7.21, -2.78, 0.528, 1, 4, -11.78, 60.62, 1, 1, 4, -3.66, 58.01, 1, 1, 4, 5.62, 47.32, 1, 1, 4, 10.69, 36.74, 1, 1, 4, -9.37, 47.83, 1, 3, 4, -24.6, 40.79, 0.18635, 64, -0.18, 18.81, 0.80828, 66, -45.08, -72.92, 0.00537, 3, 4, -37.78, 31.57, 0.27036, 64, 5.6, 33.83, 0.70229, 66, -29.39, -76.48, 0.02735, 3, 4, -45.59, 23.01, 0.32873, 64, 7.29, 45.3, 0.6141, 66, -17.8, -76.52, 0.05717, 3, 4, -48.07, 11.05, 0.41259, 64, 2.66, 56.6, 0.46968, 66, -7.28, -70.31, 0.11774, 3, 4, -13.72, 31.58, 0.74738, 64, -14.35, 20.37, 0.25096, 66, -45.57, -58.68, 0.00166, 3, 4, -22.14, 15.36, 0.87692, 64, -16.43, 38.52, 0.10799, 66, -27.9, -54.01, 0.01509, 3, 4, -26.77, -0.95, 0.83568, 64, -21.71, 54.64, 0.07887, 66, -12.71, -46.47, 0.08545, 4, 4, -26.69, -19.05, 0.78424, 64, -31.9, 69.6, 0.02397, 66, 0.64, -34.24, 0.18927, 67, -35.26, -14.9, 0.00253, 4, 4, -24.37, -33.21, 0.54416, 64, -41.75, 80.05, 0.01061, 66, 9.56, -23.01, 0.41744, 67, -21.17, -12.16, 0.02778, 4, 4, -20.49, -47.2, 0.25598, 64, -52.79, 89.48, 0.00188, 66, 17.31, -10.73, 0.56258, 67, -7.29, -7.88, 0.17957, 2, 4, -15.78, -58.85, 0.00102, 67, 4.21, -2.84, 0.99898, 3, 4, -21.65, 50.47, 0.25203, 64, 2.79, 9.14, 0.74736, 66, -54.23, -77.24, 0.00062, 3, 4, -30.66, 54.14, 0.0432, 64, 12.31, 11.14, 0.95548, 66, -50.88, -86.38, 0.00131, 4, 4, -47.61, 62.55, 0.02637, 64, 31.06, 13.64, 0.87744, 65, 4.78, 14.51, 0.09538, 66, -45.73, -104.58, 0.00082, 4, 4, -40.27, 58.63, 0.03299, 64, 22.78, 12.79, 0.96385, 65, -3.35, 12.74, 0.00193, 66, -47.75, -96.51, 0.00123, 4, 4, -50.31, 46.05, 0.11701, 64, 24.08, 28.83, 0.87275, 65, -3.85, 28.83, 0.00067, 66, -31.69, -95.49, 0.00956], "hull": 32, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 58, 60, 60, 62, 24, 26, 26, 28, 10, 12, 12, 14, 16, 18, 18, 20, 50, 52, 52, 54, 46, 48, 48, 50, 54, 56, 56, 58, 2, 0, 0, 62, 60, 64, 64, 66, 66, 68, 68, 70, 70, 72, 64, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 64, 88, 88, 90, 90, 94, 94, 92, 92, 46, 38, 96], "width": 163, "height": 74}}, "runwl/A_wenli_wave_00000": {"runwl/A_wenli_wave_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}, "runwl/A_wenli_wave_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 64}}, "a1": {"a1": {"x": 21.75, "y": -3.45, "rotation": -73.07, "width": 42, "height": 50}}, "run1/dd_2": {"run1/dd_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [199.54, -182.55, -165.57, -182.55, -165.57, 182.55, 199.54, 182.55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 50, "height": 50}}, "tx/1_00006": {"tx/1_00006": {"y": 0.5, "width": 116, "height": 77}, "tx/1_00009": {"y": 0.5, "width": 116, "height": 77}, "tx/1_00010": {"y": 0.5, "width": 116, "height": 77}, "tx/1_00012": {"y": 0.5, "width": 116, "height": 77}, "tx/1_00014": {"y": 0.5, "width": 116, "height": 77}}, "tx/1_00007": {"tx/1_00007": {"x": -5.58, "y": 8.01, "width": 113, "height": 60}, "tx/1_00008": {"x": -5.58, "y": 8.01, "width": 113, "height": 60}}, "a20": {"a20": {"x": 12.55, "y": 0.31, "rotation": 90, "width": 14, "height": 37}}, "a21": {"a21": {"x": -102.97, "y": -0.56, "rotation": -68.67, "width": 33, "height": 28}}, "lshuo01_add/lshuo01_add_001": {"lshuo01_add/lshuo01_add_001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}}, "a23": {"a23": {"x": 24.43, "y": -15.7, "rotation": -73.07, "width": 29, "height": 46}}, "tx/shuiboquan_00000": {"tx/shuiboquan_00000": {"x": 0.5, "width": 239, "height": 100}, "tx/shuiboquan_00002": {"x": 0.5, "width": 239, "height": 100}, "tx/shuiboquan_00004": {"x": 0.5, "width": 239, "height": 100}, "tx/shuiboquan_00006": {"x": 0.5, "width": 239, "height": 100}, "tx/shuiboquan_00008": {"x": 0.5, "width": 239, "height": 100}, "tx/shuiboquan_00010": {"x": 0.5, "width": 239, "height": 100}, "tx/shuiboquan_00012": {"x": 0.5, "width": 239, "height": 100}, "tx/shuiboquan_00014": {"x": 0.5, "width": 239, "height": 100}, "tx/shuiboquan_00016": {"x": 0.5, "width": 239, "height": 100}, "tx/shuiboquan_00018": {"x": 0.5, "width": 239, "height": 100}}, "a25": {"a25": {"x": 19.55, "y": 3.83, "rotation": 146.1, "width": 44, "height": 33}}, "a26": {"a13": {"type": "mesh", "uvs": [0.56442, 0.02455, 0.84692, 0, 0.93633, 0.1871, 0.98569, 0.40103, 0.9861, 0.54953, 0.78789, 0.89132, 0.60317, 0.97544, 0.35213, 0.97543, 0.11587, 0.97543, 0.04199, 0.91208, 0.00754, 0.83532, 0.03056, 0.44368, 0.06716, 0.31745, 0.22258, 0.18086, 0.37508, 0.09022, 0.74187, 0.40775, 0.49065, 0.55355, 0.20891, 0.6224], "triangles": [15, 5, 6, 6, 7, 16, 6, 16, 15, 5, 15, 4, 8, 17, 7, 15, 3, 4, 15, 2, 3, 15, 1, 2, 7, 17, 16, 8, 9, 17, 9, 10, 17, 10, 11, 17, 17, 13, 16, 13, 14, 16, 11, 12, 17, 17, 12, 13, 16, 0, 15, 16, 14, 0, 15, 0, 1], "vertices": [2, 69, 20.49, 8.88, 0.97473, 70, -20.49, 7.89, 0.02527, 2, 69, 24.1, 28.06, 0.74657, 70, -17.81, 27.22, 0.25343, 2, 69, 32.82, 32.3, 0.63026, 70, -9.3, 31.89, 0.36974, 2, 69, 41.94, 33.61, 0.49621, 70, -0.25, 33.64, 0.50379, 2, 69, 47.72, 32.24, 0.43295, 70, 5.59, 32.55, 0.56705, 2, 69, 57.81, 15.74, 0.11146, 70, 16.48, 16.57, 0.88854, 2, 69, 58.09, 2.56, 0.0006, 70, 17.41, 3.42, 0.9994, 2, 69, 54.03, -14.28, 0.22708, 70, 14.17, -13.6, 0.77292, 2, 69, 50.21, -30.12, 0.55083, 70, 11.12, -29.62, 0.44917, 2, 69, 46.55, -34.48, 0.59567, 70, 7.68, -34.15, 0.40433, 2, 69, 43.01, -36.08, 0.61798, 70, 4.22, -35.91, 0.38202, 2, 69, 28.15, -30.86, 0.78959, 70, -10.89, -31.43, 0.21041, 2, 69, 23.84, -27.22, 0.83803, 70, -15.38, -28, 0.16197, 2, 69, 21.04, -15.52, 0.94637, 70, -18.75, -16.45, 0.05363, 2, 69, 19.98, -4.44, 0.99971, 70, -20.35, -5.43, 0.00029, 2, 69, 38.26, 17.19, 0.57132, 70, -3.13, 17.06, 0.42868, 2, 69, 39.87, -1.03, 0.78021, 70, -0.64, -1.06, 0.21979, 2, 69, 37.99, -20.57, 0.67307, 70, -1.56, -20.67, 0.32693], "hull": 15, "edges": [0, 28, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 12, 14, 14, 16, 2, 30, 30, 32, 32, 34, 0, 2], "width": 69, "height": 40}}, "a27": {"a12": {"type": "mesh", "uvs": [0.28942, 0.01471, 0.55598, 0, 0.5772, 0.29, 0.55801, 0.55174, 0.63585, 0.59523, 0.8119, 0.56209, 0.92184, 0.67411, 0.99194, 0.90896, 0.86572, 0.97953, 0.60497, 0.9791, 0.34276, 0.8962, 0.05789, 0.89264, 0.05643, 0.71148, 0.11485, 0.57413, 0.01877, 0.27677, 0.0188, 0.15284, 0.31726, 0.29299, 0.34688, 0.65043], "triangles": [9, 4, 8, 8, 4, 6, 8, 6, 7, 6, 4, 5, 9, 10, 4, 4, 10, 17, 17, 3, 4, 17, 10, 12, 17, 12, 13, 12, 10, 11, 3, 17, 16, 17, 13, 16, 13, 14, 16, 3, 16, 2, 14, 15, 16, 15, 0, 16, 16, 1, 2, 16, 0, 1], "vertices": [1, 70, 7.76, 2.61, 1, 1, 70, 9.71, 16.35, 1, 2, 70, 23.31, 14.84, 0.93008, 76, 2.15, 17.79, 0.06992, 2, 70, 35.19, 11.52, 0.21962, 76, 8.95, 7.49, 0.78038, 2, 70, 37.97, 15.11, 0.02716, 76, 13.4, 8.38, 0.97284, 1, 76, 19.64, 15.25, 1, 1, 76, 27.39, 14.63, 1, 1, 76, 37.07, 8.19, 1, 1, 76, 33.95, 1.53, 1, 1, 76, 23.26, -6.81, 1, 2, 70, 48.96, -2.54, 0.01998, 76, 10.13, -12.16, 0.98002, 2, 70, 45.98, -17.05, 0.24852, 76, -1.63, -21.16, 0.75148, 2, 70, 37.61, -15.51, 0.40774, 76, -6.95, -14.51, 0.59226, 2, 70, 31.85, -11.3, 0.79118, 76, -8.54, -7.55, 0.20882, 1, 70, 17.18, -13.55, 1, 1, 70, 11.46, -12.44, 1, 2, 70, 20.88, 1.54, 0.99918, 76, -8.4, 9.34, 0.00082, 1, 76, 3.17, -2.93, 1], "hull": 16, "edges": [0, 30, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 32, 32, 34, 2, 4, 4, 6, 0, 2], "width": 52, "height": 47}}, "a28": {"a14": {"type": "mesh", "uvs": [0.20814, 0.32607, 0.09375, 0.55933, 0.04367, 0.5018, 0.00458, 0.41208, 0.00436, 0.25325, 0.02837, 0.1573, 0.06744, 0.11432, 0.11357, 0.12236, 0.12795, 0.12231, 0.24938, 0.08517, 0.09, 0.20273, 0.07556, 0.31256, 0.07845, 0.40966, 0.09434, 0.50358, 0.2197, 0.18758], "triangles": [1, 13, 0, 1, 2, 13, 2, 12, 13, 13, 12, 0, 2, 3, 12, 3, 11, 12, 3, 4, 11, 12, 11, 0, 11, 10, 0, 0, 10, 14, 0, 14, 9, 14, 10, 8, 10, 7, 8, 11, 4, 10, 4, 5, 10, 5, 6, 10, 10, 6, 7, 14, 8, 9], "vertices": [4, 5, 80.44, 78.19, 0.56057, 6, -15.94, 78.57, 0.0075, 34, 61.26, 88.78, 0.10394, 23, -13.49, 13.7, 0.328, 3, 5, 30.78, 93.49, 0.58987, 35, -36.79, 103.51, 0.04213, 23, 33.14, 36.65, 0.368, 3, 5, 39.73, 106.31, 0.58987, 35, -27.84, 116.32, 0.04213, 23, 35.43, 21.19, 0.368, 3, 5, 55.33, 118.02, 0.5688, 35, -12.24, 128.04, 0.0632, 23, 32.12, 1.97, 0.368, 3, 5, 85.85, 124.14, 0.5688, 35, 18.28, 134.16, 0.0632, 23, 14.12, -23.43, 0.368, 3, 5, 105.31, 122.73, 0.5688, 35, 37.74, 132.74, 0.0632, 23, -1, -35.76, 0.368, 3, 5, 115.22, 116.09, 0.5688, 35, 47.65, 126.11, 0.0632, 23, -12.76, -37.74, 0.368, 4, 5, 115.62, 106.01, 0.57924, 6, 19.86, 105.59, 0.00536, 35, 48.05, 116.03, 0.0474, 23, -19.97, -30.68, 0.368, 4, 5, 116.23, 102.97, 0.54017, 6, 20.4, 102.53, 0.02863, 35, 48.66, 112.99, 0.0632, 23, -22.5, -28.89, 0.368, 4, 5, 128.49, 78.67, 0.7765, 6, 32.11, 77.95, 0.1205, 35, 60.92, 88.68, 0.075, 34, 109.31, 89.26, 0.028, 3, 5, 99.17, 107.93, 0.61093, 35, 31.61, 117.95, 0.02107, 23, -6.69, -20.79, 0.368, 2, 5, 77.45, 106.79, 0.632, 23, 8.32, -5.05, 0.368, 2, 5, 58.91, 102.46, 0.632, 23, 18.84, 10.82, 0.368, 2, 5, 41.52, 95.5, 0.632, 23, 26.71, 27.82, 0.368, 5, 5, 107.55, 81.04, 0.61418, 6, 11.23, 80.8, 0.03359, 35, 39.98, 91.05, 0.0248, 34, 88.37, 91.63, 0.07142, 23, -31.24, -6.98, 0.256], "hull": 10, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 14, 20, 20, 22, 22, 24, 24, 26, 18, 28, 28, 0, 18, 0, 0, 2], "width": 216, "height": 196}}, "tx/dg_0001": {"tx/xuanzhuandaoguang_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-94.07, -77.83, 136.89, -72.51, 133.21, 87, -97.74, 81.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 346, "height": 180}}, "a2": {"a2": {"type": "mesh", "uvs": [0.39373, 0.35517, 0.68783, 0.6616, 0.80173, 0.29829, 0.91201, 0, 1, 0.28495, 1, 0.54533, 0.92365, 1, 0.81353, 1, 0.64175, 0.7883, 0.38413, 0.65772, 0.27363, 0.82765, 0.17791, 0.98362, 0.01542, 0.66529, 0.01372, 0.04704, 0.12833, 0.04625, 0.24709, 0.04543], "triangles": [2, 3, 4, 5, 2, 4, 0, 10, 15, 8, 9, 0, 12, 13, 14, 1, 8, 0, 9, 10, 0, 10, 11, 14, 10, 14, 15, 12, 14, 11, 6, 7, 2, 1, 2, 7, 8, 1, 7, 5, 6, 2], "vertices": [2, 6, 39.48, 2.58, 0.75, 32, 28.64, 14.68, 0.25, 2, 6, 36.54, -17.94, 0.742, 32, 25.7, -5.84, 0.258, 2, 6, 45.38, -24.14, 0.766, 32, 34.54, -12.04, 0.234, 2, 6, 52.82, -30.33, 0.806, 32, 41.98, -18.23, 0.194, 2, 6, 47.95, -37.17, 0.806, 32, 37.11, -25.07, 0.194, 2, 6, 42.56, -38.12, 0.806, 32, 31.72, -26.02, 0.194, 2, 6, 32.27, -34.73, 0.806, 32, 21.43, -22.63, 0.194, 2, 6, 31, -27.46, 0.774, 32, 20.16, -15.36, 0.226, 2, 6, 33.39, -15.36, 0.71, 32, 22.55, -3.26, 0.29, 2, 6, 33.11, 2.12, 0.75, 32, 22.27, 14.22, 0.25, 2, 6, 28.31, 8.79, 0.766, 32, 17.47, 20.89, 0.234, 2, 6, 23.98, 14.54, 0.742, 32, 13.14, 26.64, 0.258, 2, 6, 28.68, 26.42, 0.806, 32, 17.84, 38.52, 0.194, 2, 6, 41.45, 28.78, 0.774, 32, 30.61, 40.88, 0.226, 2, 6, 42.79, 21.22, 0.758, 32, 31.95, 33.32, 0.242, 2, 6, 44.19, 13.38, 0.75, 32, 33.34, 25.48, 0.25], "hull": 16, "edges": [0, 30, 0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 18, 20, 20, 22, 2, 4, 4, 6], "width": 67, "height": 21}}, "a0": {"a0": {"x": 21.23, "y": -0.45, "rotation": -27.32, "width": 51, "height": 33}}, "lshuo01_add/lshuo01_add_1": {"lshuo01_add/lshuo01_add_001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}, "lshuo01_add/lshuo01_add_017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -67, -100, -67, -100, 67, 100, 67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 134}}, "a24": {"a24": {"x": 26.74, "y": -6.11, "rotation": -27.32, "width": 36, "height": 32}}, "a3": {"a3": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 7, 2, 3, 7, 2, 7, 8, 1, 2, 8, 1, 8, 9, 0, 1, 9], "vertices": [2, 6, 33.16, -39.77, 0.75, 32, 22.32, -27.67, 0.25, 2, 6, 30.26, -23.27, 0.75, 32, 19.42, -11.17, 0.25, 2, 6, 27.37, -6.77, 0.75, 32, 16.53, 5.33, 0.25, 2, 6, 24.48, 9.73, 0.75, 32, 13.63, 21.83, 0.25, 2, 6, 21.58, 26.22, 0.75, 32, 10.74, 38.33, 0.25, 2, 6, 42.26, 29.85, 0.75, 32, 31.42, 41.95, 0.25, 2, 6, 45.16, 13.35, 0.75, 32, 34.32, 25.46, 0.25, 2, 6, 48.05, -3.14, 0.75, 32, 37.21, 8.96, 0.25, 2, 6, 50.95, -19.64, 0.75, 32, 40.11, -7.54, 0.25, 2, 6, 53.84, -36.14, 0.75, 32, 43, -24.04, 0.25], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 67, "height": 21}}, "a4": {"a4": {"type": "mesh", "uvs": [0.24424, 0.00489, 0.2947, 0.00452, 0.34104, 0.04463, 0.4104, 0.05416, 0.4185, 0.04471, 0.45403, 0.0244, 0.50893, 0.02478, 0.56619, 0.07393, 0.65832, 0.10053, 0.74838, 0.06878, 0.73223, 0.024, 0.77612, 0.05112, 0.79254, 0.09576, 0.75958, 0.14742, 0.85144, 0.23088, 0.91903, 0.34104, 0.92685, 0.26667, 0.97004, 0.24725, 0.96105, 0.27735, 1, 0.37462, 1, 0.37587, 0.9413, 0.46794, 0.99469, 0.4639, 0.99441, 0.52727, 0.89335, 0.6026, 0.96551, 0.61596, 0.96579, 0.68798, 0.82776, 0.74726, 0.8729, 0.7606, 0.87301, 0.80496, 0.8088, 0.8519, 0.75874, 0.8885, 0.81698, 0.90453, 0.78716, 0.92991, 0.75248, 0.94089, 0.69719, 0.99556, 0.5754, 0.99504, 0.52128, 0.95942, 0.46922, 0.97339, 0.3709, 0.85165, 0.28712, 0.83531, 0.26055, 0.81251, 0.28684, 0.77515, 0.1674, 0.72926, 0.14837, 0.67771, 0.19289, 0.66029, 0.12149, 0.59932, 0.09586, 0.55631, 0.03455, 0.54363, 0, 0.49672, 0, 0.47546, 0.01823, 0.36321, 0.06933, 0.36749, 0.0692, 0.28054, 0.05576, 0.25158, 0.0201, 0.23324, 0.06385, 0.21514, 0.14965, 0.26213, 0.1599, 0.22858, 0.12191, 0.19172, 0.21356, 0.16122, 0.21373, 0.09845, 0.25961, 0.05967, 0.2692, 0.02972, 0.57368, 0.19902, 0.624, 0.37138, 0.64619, 0.51586, 0.64915, 0.56655, 0.63879, 0.69962, 0.6314, 0.7275, 0.624, 0.80481, 0.69502, 0.77946, 0.79269, 0.68441, 0.84152, 0.56148, 0.85188, 0.43094, 0.78529, 0.30041, 0.68171, 0.22437, 0.44198, 0.22691, 0.31473, 0.30675, 0.28957, 0.38405, 0.32064, 0.49685, 0.37983, 0.545, 0.36652, 0.62611, 0.39315, 0.69962, 0.4553, 0.76679, 0.55593, 0.80607, 0.06586, 0.40481, 0.04662, 0.44157, 0.07178, 0.47198, 0.11025, 0.43523, 0.13393, 0.37186, 0.22716, 0.2132, 0.29671, 0.20686, 0.34998, 0.23347, 0.32334, 0.0852, 0.32778, 0.13209, 0.38993, 0.11181, 0.47576, 0.11434, 0.54924, 0.15078, 0.14176, 0.55037, 0.19651, 0.52629, 0.26014, 0.57065, 0.20247, 0.44377, 0.21578, 0.34492, 0.63456, 0.94309, 0.62716, 0.88099, 0.50878, 0.91141, 0.55761, 0.86959, 0.77218, 0.81002, 0.44663, 0.35886, 0.45255, 0.43996, 0.63795, 0.46223, 0.48954, 0.53501, 0.49398, 0.61992, 0.51322, 0.70737, 0.71447, 0.36519, 0.74406, 0.46278, 0.74406, 0.55529, 0.74406, 0.63893, 0.71151, 0.70484, 0.74358, 0.7322, 0.43943, 0.17088, 0.69514, 0.17988, 0.81424, 0.25938, 0.87904, 0.36138, 0.8948, 0.44388, 0.87028, 0.56688, 0.84943, 0.65366, 0.81243, 0.71129, 0.75636, 0.77948, 0.70029, 0.82942, 0.62179, 0.84094, 0.56236, 0.83998, 0.46704, 0.82365, 0.39415, 0.77275, 0.3392, 0.71417, 0.31229, 0.63253, 0.25687, 0.28107], "triangles": [43, 45, 42, 43, 44, 45, 41, 42, 40, 39, 40, 42, 49, 50, 88, 50, 87, 88, 50, 51, 87, 87, 86, 89, 87, 51, 86, 90, 86, 52, 86, 51, 52, 90, 52, 53, 53, 54, 57, 54, 55, 56, 46, 47, 99, 99, 47, 88, 89, 100, 99, 88, 47, 48, 48, 49, 88, 99, 88, 89, 88, 87, 89, 89, 90, 102, 89, 86, 90, 57, 90, 53, 61, 62, 94, 94, 62, 2, 2, 62, 63, 63, 1, 2, 63, 0, 1, 91, 57, 58, 121, 95, 96, 91, 58, 60, 58, 59, 60, 91, 60, 92, 92, 60, 95, 95, 61, 94, 61, 95, 60, 121, 96, 97, 98, 97, 7, 95, 94, 96, 96, 3, 97, 3, 4, 97, 4, 5, 97, 97, 6, 7, 97, 5, 6, 94, 2, 96, 96, 2, 3, 54, 56, 57, 9, 11, 12, 9, 10, 11, 123, 13, 14, 122, 8, 13, 98, 7, 8, 8, 9, 13, 13, 9, 12, 15, 16, 18, 18, 16, 17, 23, 126, 21, 23, 21, 22, 20, 21, 125, 125, 15, 20, 15, 19, 20, 15, 18, 19, 108, 129, 27, 28, 29, 27, 27, 29, 108, 27, 127, 26, 26, 24, 25, 26, 127, 24, 24, 126, 23, 31, 104, 105, 36, 104, 35, 35, 104, 34, 34, 104, 31, 34, 31, 33, 33, 31, 32, 108, 29, 30, 36, 37, 104, 38, 39, 106, 38, 106, 37, 104, 37, 106, 31, 108, 30, 122, 13, 123, 21, 126, 125, 126, 24, 127, 27, 128, 127, 128, 27, 129, 42, 135, 134, 104, 107, 105, 107, 131, 105, 107, 132, 131, 31, 105, 130, 106, 133, 107, 105, 131, 130, 107, 133, 132, 39, 134, 133, 131, 70, 130, 131, 132, 70, 133, 85, 132, 132, 85, 70, 70, 71, 130, 130, 129, 108, 130, 71, 129, 134, 84, 133, 133, 84, 85, 69, 70, 85, 85, 84, 114, 70, 69, 71, 69, 85, 114, 71, 120, 129, 128, 129, 120, 71, 69, 119, 71, 119, 120, 119, 69, 68, 135, 45, 136, 135, 83, 134, 134, 83, 84, 84, 83, 114, 120, 72, 128, 120, 119, 72, 69, 114, 68, 135, 82, 83, 135, 136, 82, 128, 72, 127, 83, 113, 114, 114, 113, 68, 119, 118, 72, 119, 68, 118, 83, 82, 113, 68, 67, 118, 68, 113, 67, 127, 72, 73, 45, 101, 136, 101, 99, 100, 72, 118, 73, 126, 127, 73, 67, 117, 118, 118, 117, 73, 82, 136, 81, 82, 81, 113, 81, 136, 101, 81, 112, 113, 113, 112, 67, 101, 80, 81, 101, 100, 80, 112, 66, 67, 67, 66, 117, 117, 116, 73, 126, 73, 125, 73, 74, 125, 73, 116, 74, 117, 66, 116, 112, 81, 110, 81, 80, 110, 112, 111, 66, 112, 110, 111, 100, 102, 80, 100, 89, 102, 66, 111, 116, 102, 79, 80, 80, 79, 110, 111, 115, 116, 116, 115, 74, 110, 65, 111, 111, 65, 115, 74, 124, 125, 102, 103, 79, 102, 90, 103, 79, 109, 110, 110, 109, 65, 124, 74, 75, 75, 74, 115, 79, 103, 78, 79, 78, 109, 78, 103, 137, 103, 90, 57, 109, 64, 65, 65, 76, 115, 65, 64, 76, 115, 76, 75, 78, 77, 109, 109, 77, 64, 103, 57, 137, 124, 75, 123, 78, 93, 77, 78, 137, 93, 75, 76, 123, 137, 92, 93, 137, 91, 92, 76, 122, 123, 92, 95, 93, 93, 121, 77, 121, 93, 95, 64, 121, 98, 64, 77, 121, 76, 64, 122, 64, 98, 122, 98, 8, 122, 121, 97, 98, 45, 46, 101, 106, 39, 133, 31, 130, 108, 125, 124, 15, 15, 124, 123, 106, 107, 104, 14, 15, 123, 57, 91, 137, 99, 101, 46, 135, 42, 45, 134, 39, 42], "vertices": [4, 12, -12.48, 84.77, 0, 14, 37.99, -15.63, 3e-05, 15, 20.16, 8.73, 0.89997, 33, 82.94, 79.62, 0.1, 4, 12, -7.64, 77.5, 0.00409, 14, 30.65, -20.36, 0.07673, 15, 21.91, 0.18, 0.81918, 33, 84.52, 71.03, 0.1, 4, 12, -10.05, 66.36, 0.03182, 14, 19.55, -17.8, 0.3097, 15, 15.51, -9.25, 0.58348, 33, 77.92, 61.74, 0.075, 4, 12, -5.11, 55.25, 0.08523, 14, 8.38, -22.59, 0.48102, 15, 15.93, -21.4, 0.33375, 33, 78.1, 49.59, 0.1, 4, 12, -2.75, 55.12, 0.14561, 14, 8.21, -24.95, 0.60744, 15, 18.07, -22.4, 0.14696, 33, 80.22, 48.54, 0.1, 4, 12, 4.05, 52.21, 0.19427, 14, 5.21, -31.71, 0.60077, 15, 23.28, -27.65, 0.10496, 33, 85.33, 43.19, 0.1, 5, 6, 147.12, 33.99, 1e-05, 12, 9.17, 44.22, 0.29489, 14, -2.86, -36.72, 0.54381, 15, 25.03, -36.98, 0.06129, 33, 86.89, 33.82, 0.1, 6, 6, 139.06, 22.52, 1e-05, 12, 6.27, 30.5, 0.48916, 13, 12.09, 31.81, 0.00459, 14, -16.53, -33.63, 0.37693, 15, 17.19, -48.61, 0.02931, 33, 78.83, 22.35, 0.1, 6, 6, 136.52, 5.89, 1e-05, 12, 10.47, 14.21, 0.41054, 13, 3.01, 17.65, 0.29842, 14, -32.88, -37.61, 0.18308, 15, 14.99, -65.29, 0.00795, 33, 76.29, 5.72, 0.1, 5, 12, 24.36, 4.66, 0.26484, 13, 5.46, 0.98, 0.59842, 14, -42.61, -51.36, 0.03631, 15, 24.28, -79.34, 0.00043, 33, 85.3, -8.52, 0.1, 4, 12, 30.41, 11.95, 0.00611, 13, 14.91, 1.49, 0.89383, 14, -35.42, -57.51, 6e-05, 33, 93.72, -4.2, 0.1, 3, 12, 29.97, 2.59, 0.15044, 13, 7.75, -4.54, 0.74956, 33, 89.64, -12.63, 0.1, 4, 10, 41.13, 47.5, 0.00788, 12, 23.97, -4.71, 0.44256, 13, -1.68, -5.11, 0.44956, 33, 81.25, -16.98, 0.1, 5, 6, 130.21, -13, 0.00128, 10, 29.25, 47.17, 0.14007, 12, 12.11, -5.64, 0.6091, 13, -10.42, 2.95, 0.14956, 33, 69.98, -13.17, 0.1, 6, 6, 116.36, -31.57, 0.00166, 10, 22.69, 24.95, 0.42092, 11, 19.18, 17.22, 0.00868, 12, 6.67, -28.16, 0.46873, 16, -124.88, 12.3, 1e-05, 33, 56.13, -31.73, 0.1, 6, 6, 96.46, -46.93, 0.00166, 10, 9.36, 3.64, 0.41304, 11, -3.7, 6.81, 0.30868, 12, -5.58, -50.11, 0.17661, 16, -126.17, 37.4, 1e-05, 33, 36.23, -47.1, 0.1, 6, 6, 111.49, -45.67, 0.00038, 10, 23.02, 10.04, 0.28085, 11, 11.22, 4.61, 0.60868, 12, 7.74, -43.03, 0.01008, 16, -133.69, 24.33, 1e-05, 33, 51.26, -45.83, 0.1, 3, 10, 30.17, 5.56, 0.00015, 11, 14.71, -3.08, 0.89985, 33, 56.41, -52.52, 0.1, 3, 10, 24.14, 3.84, 0.30014, 11, 8.73, -1.18, 0.59986, 33, 50.16, -52.03, 0.1, 4, 9, 23.2, 58.92, 1e-05, 10, 10.56, -11.88, 0.60013, 11, -11.27, -6.78, 0.29986, 33, 31.97, -62.07, 0.1, 7, 6, 91.95, -61.94, 0.0028, 9, 23.21, 58.66, 0.06557, 10, 10.34, -12.01, 0.83162, 11, -11.53, -6.77, 1e-05, 14, -112.57, -22.21, 0, 16, -135.94, 49.66, 0, 33, 31.72, -62.11, 0.1, 7, 6, 71.88, -55.16, 0.0028, 9, 13.48, 39.84, 0.15199, 10, -10.84, -12.61, 0.7452, 11, -29.51, 4.43, 0, 14, -113.93, -1.06, 0, 16, -118.92, 62.29, 0, 33, 11.65, -55.32, 0.1, 6, 6, 74.27, -64.11, 0.00326, 9, 22.69, 40.86, 0.2556, 10, -5.48, -20.18, 0.64114, 14, -121.3, -6.69, 0, 16, -127.65, 65.42, 0, 33, 14.05, -64.28, 0.1, 5, 6, 61.66, -66.28, 0.00888, 9, 22.93, 28.07, 0.42657, 10, -16.56, -26.58, 0.46442, 12, -29.97, -81.58, 0.00012, 33, 1.43, -66.44, 0.1, 5, 6, 43.65, -51.68, 0.00888, 9, 5.8, 12.46, 0.63555, 10, -38.51, -19.15, 0.25545, 12, -52.26, -75.25, 0.00012, 33, -16.58, -51.85, 0.1, 5, 6, 43.15, -64.45, 0.00843, 9, 18.34, 10.04, 0.83192, 10, -34.56, -31.29, 0.05953, 12, -47.7, -87.18, 0.00012, 33, -17.08, -64.61, 0.1, 6, 6, 28.83, -67.01, 0.02081, 7, -17.92, 68.01, 0.06797, 8, 23.94, 73.63, 0.01519, 9, 18.72, -4.5, 0.79143, 10, -47.1, -38.67, 0.0046, 33, -31.4, -67.18, 0.1, 5, 6, 12.91, -45.56, 0.03246, 7, -11.97, 41.96, 0.1628, 8, 9.06, 51.44, 0.03946, 9, -4.88, -17.02, 0.66528, 33, -47.32, -45.72, 0.1, 5, 6, 11.6, -53.72, 0.04234, 7, -7.5, 48.91, 0.26792, 8, 17.19, 52.94, 0.06858, 9, 2.99, -19.53, 0.52116, 33, -48.63, -53.88, 0.1, 5, 6, 2.78, -55.28, 0.03145, 7, 1.21, 46.8, 0.33217, 8, 21.62, 45.16, 0.10844, 9, 3.21, -28.49, 0.42794, 33, -57.45, -55.45, 0.1, 5, 6, -8.48, -45.98, 0.03621, 7, 7.78, 33.75, 0.37889, 8, 16.63, 31.43, 0.19886, 9, -7.68, -38.22, 0.31104, 33, -68.71, -46.15, 0.075, 5, 6, -17.26, -38.73, 0.01222, 7, 12.9, 23.58, 0.34369, 8, 12.74, 20.73, 0.38594, 9, -16.17, -45.81, 0.15815, 33, -77.49, -38.89, 0.1, 5, 6, -18.7, -49.21, 0.0023, 7, 18.44, 32.6, 0.29809, 8, 23.1, 22.88, 0.52534, 9, -6.02, -48.82, 0.07427, 33, -78.93, -49.38, 0.1, 4, 7, 22.19, 26.37, 0.24652, 8, 21.14, 15.88, 0.60182, 9, -11.06, -54.06, 0.05166, 33, -84.87, -45.18, 0.1, 4, 7, 22.92, 20.01, 0.15898, 8, 17.02, 10.99, 0.70916, 9, -17.01, -56.42, 0.03186, 33, -88.1, -39.66, 0.1, 6, 6, -40.4, -31.98, 0.00175, 7, 31.37, 8.1, 0.35234, 8, 14.14, -3.34, 0.55898, 9, -26.32, -67.67, 0.0097, 18, -57.56, 66.61, 0.00222, 33, -100.63, -32.14, 0.075, 6, 6, -43.94, -11.2, 0.0252, 7, 26.26, -12.35, 0.44265, 8, -4.24, -13.63, 0.41198, 9, -47.39, -68.05, 0, 18, -36.7, 63.61, 0.02017, 33, -104.16, -11.37, 0.1, 6, 6, -38.47, -0.74, 0.07815, 7, 17.04, -19.73, 0.68087, 8, -15.94, -11.99, 0.08458, 9, -56.91, -61.07, 0, 18, -28.42, 55.2, 0.05641, 33, -98.7, -0.91, 0.1, 5, 6, -42.8, 7.64, 0.13812, 7, 17.64, -29.15, 0.55093, 8, -22.38, -18.88, 0.01049, 18, -19.11, 56.75, 0.20046, 33, -103.03, 7.48, 0.1, 5, 6, -21.52, 28.65, 0.14923, 7, -10.29, -39.82, 0.32216, 8, -49.3, -5.88, 0.00035, 18, -5.65, 30.06, 0.42826, 33, -81.75, 28.48, 0.1, 4, 6, -20.77, 43.49, 0.14048, 7, -16.94, -53.11, 0.10609, 18, 8.25, 24.79, 0.65343, 33, -81, 43.33, 0.1, 4, 6, -17.03, 48.82, 0.08056, 7, -22.51, -56.48, 0.04835, 18, 12.17, 19.6, 0.77109, 33, -77.26, 48.65, 0.1, 5, 6, -8.81, 45.64, 0.04657, 7, -28.76, -50.27, 0.02733, 18, 6.63, 12.75, 0.52609, 19, -11.98, 8.16, 0.3, 33, -69.04, 45.47, 0.1, 6, 6, -3.25, 67.59, 0.01819, 7, -42.68, -68.13, 0.0099, 16, 24.76, 54.11, 0.00265, 18, 25.82, 0.72, 0.27271, 19, 10.46, 5.09, 0.59655, 33, -63.48, 67.43, 0.1, 5, 12, -135.38, 24.42, 0, 16, 23.38, 43.28, 0.05572, 18, 27.65, -10.04, 0.04933, 19, 16.56, -3.97, 0.79495, 33, -53.79, 72.47, 0.1, 5, 12, -128.22, 19.89, 0, 16, 14.92, 43.31, 0.25836, 18, 19.54, -12.47, 0.07628, 19, 10.16, -9.52, 0.56536, 33, -49, 65.49, 0.1, 7, 6, 21.23, 79.95, 0.0005, 12, -124.65, 36.96, 1e-05, 16, 20.97, 26.95, 0.56009, 17, -24.81, -10.61, 0.0021, 18, 30.08, -26.37, 0.07991, 19, 25.49, -17.85, 0.25739, 33, -39, 79.79, 0.1, 5, 16, 21.35, 17.21, 0.69473, 17, -15.81, -6.86, 0.05722, 18, 33.27, -35.58, 0.03958, 19, 32.19, -24.93, 0.10847, 33, -31.21, 85.65, 0.1, 5, 16, 29.91, 10.44, 0.59936, 17, -12.44, 3.52, 0.24964, 18, 43.43, -39.58, 0.01263, 19, 43.09, -24.39, 0.03837, 33, -30.52, 96.54, 0.1, 5, 16, 31.37, -0.67, 0.39301, 17, -2.53, 8.76, 0.49836, 18, 48.04, -49.79, 0.00142, 19, 51.5, -31.8, 0.00722, 33, -22.22, 104.07, 0.1, 4, 16, 29.57, -4.57, 0.15575, 17, 1.75, 8.43, 0.74394, 19, 52.71, -35.92, 0.00031, 33, -17.99, 104.81, 0.1, 4, 14, 32.33, 66.44, 0.01689, 16, 17.2, -23.84, 0.11096, 17, 24.11, 3.54, 0.77215, 33, 4.89, 105.62, 0.1, 4, 14, 24.4, 62.44, 0.07879, 16, 9.54, -19.35, 0.15003, 17, 22.57, -5.21, 0.67119, 33, 5.56, 96.77, 0.1, 4, 14, 33.81, 47.61, 0.19112, 16, 2.2, -35.31, 0.24021, 17, 40.09, -6.53, 0.46867, 33, 22.86, 99.82, 0.1, 4, 14, 38.9, 43.91, 0.26298, 16, 1.86, -41.59, 0.26151, 17, 46.1, -4.66, 0.37551, 33, 28.22, 103.12, 0.1, 4, 14, 46.1, 44.08, 0.2673, 16, 5.91, -47.54, 0.25429, 17, 50.27, 1.21, 0.37841, 33, 30.8, 109.84, 0.1, 5, 6, 95.94, 103.18, 0.0004, 14, 41.66, 36.94, 0.30969, 16, -2.5, -47.69, 0.25978, 17, 53.33, -6.62, 0.33012, 33, 35.71, 103.02, 0.1, 6, 6, 89.16, 86.92, 0.00065, 14, 24.04, 37.03, 0.41585, 15, -33.98, 14.79, 0.00087, 16, -12, -32.85, 0.23912, 17, 42.73, -20.69, 0.24352, 33, 28.93, 86.76, 0.1, 7, 6, 96.14, 86.35, 0.00105, 14, 26.16, 30.35, 0.57762, 15, -26.99, 14.35, 0.00421, 16, -16.45, -38.26, 0.18869, 17, 49.35, -22.98, 0.1284, 18, 13.19, -99.63, 4e-05, 33, 35.91, 86.18, 0.1, 6, 6, 102.34, 94.11, 0.00026, 14, 35.7, 27.57, 0.63297, 15, -20.95, 22.23, 0.04464, 16, -13.6, -47.77, 0.13689, 17, 57.28, -17, 0.08524, 33, 42.11, 93.94, 0.1, 6, 6, 111.15, 79.56, 1e-05, 14, 25.59, 13.89, 0.48104, 15, -11.85, 7.86, 0.29877, 16, -30.58, -46.72, 0.07502, 17, 62.2, -33.28, 0.04516, 33, 50.92, 79.39, 0.1, 6, 6, 123.64, 81.72, 1e-05, 14, 32.35, 3.16, 0.26325, 15, 0.6, 10.27, 0.59718, 16, -35.92, -58.22, 0.02557, 17, 74.84, -34.28, 0.01399, 33, 63.41, 81.55, 0.1, 5, 14, 29.83, -7.7, 0.04297, 15, 9.81, 3.99, 0.855, 16, -46.41, -62.01, 0.00125, 17, 82.04, -42.8, 0.00079, 33, 72.5, 75.09, 0.1, 4, 12, -14.32, 78.41, 0, 14, 31.66, -13.7, 3e-05, 15, 16.07, 3.53, 0.89997, 33, 78.74, 74.5, 0.1, 2, 6, 114.39, 16.87, 0.814, 32, 103.55, 28.97, 0.186, 2, 6, 81.6, 2.28, 0.76, 32, 70.76, 14.39, 0.24, 2, 6, 53.52, -6.54, 0.744, 32, 42.68, 5.56, 0.256, 2, 6, 43.52, -8.82, 0.744, 32, 32.68, 3.29, 0.256, 2, 6, 16.74, -11.7, 0.744, 32, 5.9, 0.41, 0.256, 2, 6, 10.97, -11.41, 0.768, 32, 0.13, 0.69, 0.232, 2, 6, -4.63, -12.85, 0.798, 32, -15.47, -0.74, 0.202, 2, 6, 2.53, -24.06, 0.792, 32, -8.31, -11.96, 0.208, 2, 6, 24.36, -37.39, 0.816, 32, 13.52, -25.29, 0.184, 2, 6, 50.28, -41.42, 0.832, 32, 39.44, -29.32, 0.168, 2, 6, 76.56, -38.63, 0.832, 32, 65.72, -26.52, 0.168, 2, 6, 100.54, -22.72, 0.832, 32, 89.7, -10.62, 0.168, 2, 6, 112.58, -2.42, 0.832, 32, 101.73, 9.68, 0.168, 2, 6, 104.91, 38.34, 0.808, 32, 94.06, 50.44, 0.192, 2, 6, 85.22, 57.24, 0.808, 32, 74.37, 69.34, 0.192, 2, 6, 69.08, 58.83, 0.808, 32, 58.24, 70.93, 0.192, 2, 6, 47.57, 49.59, 0.808, 32, 36.73, 61.7, 0.192, 2, 6, 39.76, 37.83, 0.808, 32, 28.92, 49.93, 0.192, 2, 6, 23.22, 37.27, 0.808, 32, 12.38, 49.37, 0.192, 2, 6, 9.39, 30.16, 0.808, 32, -1.45, 42.26, 0.192, 2, 6, -2.11, 17.23, 0.824, 32, -12.95, 29.33, 0.176, 2, 6, -6.92, -1.29, 0.8, 32, -17.76, 10.81, 0.2, 4, 14, 20.88, 69.14, 0.01985, 16, 13.24, -12.75, 0.10848, 17, 15.1, -4.03, 0.77167, 33, -1.96, 96.05, 0.1, 6, 14, 19.72, 77.19, 0.00296, 16, 19.38, -7.41, 0.34592, 17, 7.96, -0.14, 0.55104, 18, 38.52, -59.71, 2e-05, 19, 46.9, -44.76, 6e-05, 33, -9.85, 98.05, 0.1, 6, 14, 12.76, 80.05, 0.00135, 16, 18, -0.01, 0.54128, 17, 1.5, -4.01, 0.35729, 18, 35.06, -53.03, 2e-05, 19, 40.99, -40.1, 6e-05, 33, -15.15, 92.7, 0.1, 7, 6, 53.54, 87.59, 0.00023, 14, 11.1, 70.22, 0.03644, 16, 8.84, -3.96, 0.70537, 17, 8.39, -11.22, 0.15789, 18, 27.44, -59.47, 2e-05, 19, 36.7, -49.1, 6e-05, 33, -6.69, 87.43, 0.1, 5, 6, 66.86, 85.77, 0.00034, 14, 14.48, 57.21, 0.05465, 16, -0.24, -13.86, 0.60818, 17, 20.84, -16.28, 0.23683, 33, 6.63, 85.61, 0.1, 8, 6, 101.21, 75.43, 0.30499, 14, 17.99, 21.51, 0.42096, 15, -21.7, 3.53, 0.00403, 16, -28.31, -36.21, 0.11069, 17, 51.56, -34.82, 0.06328, 18, 1.24, -101.11, 5e-05, 32, 90.37, 87.53, 0.02933, 33, 40.98, 75.26, 0.06667, 8, 6, 104.55, 63.8, 0.56608, 14, 8.51, 13.99, 0.22606, 15, -18.13, -8.03, 0.00316, 16, -39.77, -32.33, 0.04757, 17, 51.91, -46.91, 0.02242, 18, -10.85, -100.72, 5e-05, 32, 93.71, 75.9, 0.10133, 33, 44.32, 63.63, 0.03333, 7, 6, 100.85, 53.79, 0.87613, 14, -2.15, 13.61, 0.00141, 15, -21.63, -18.11, 2e-05, 16, -45.89, -23.58, 0.0003, 17, 45.84, -55.69, 0.00014, 18, -19.24, -94.13, 0, 32, 90.01, 65.89, 0.122, 4, 12, -18.59, 64.44, 0.00672, 14, 17.75, -9.24, 0.53254, 15, 6.88, -7.82, 0.42741, 33, 69.32, 63.34, 0.03333, 3, 12, -26.1, 58.63, 0.01958, 14, 12.04, -1.65, 0.72494, 15, -2.27, -10.4, 0.25549, 6, 6, 126.25, 51.23, 0.00052, 12, -16.8, 51.86, 0.10122, 14, 5.14, -10.86, 0.82224, 15, 3.82, -20.16, 0.07592, 16, -62.46, -43, 9e-05, 18, -29.47, -117.52, 0, 7, 6, 128.31, 36.51, 0.29652, 12, -9.12, 39.14, 0.10085, 14, -7.68, -18.36, 0.50206, 15, 6.17, -34.83, 0.06314, 16, -75.73, -36.31, 9e-05, 18, -44.1, -114.97, 0, 32, 117.47, 48.62, 0.03733, 7, 6, 123.26, 22.72, 0.61119, 12, -8.34, 24.47, 0.0839, 14, -22.36, -18.94, 0.23296, 15, 1.39, -48.72, 0.01585, 16, -84.19, -24.3, 9e-05, 18, -55.68, -105.93, 0, 32, 112.42, 34.82, 0.056, 9, 6, 31.57, 78.21, 0.27395, 10, -94.92, 98.48, 0, 12, -114.45, 39.43, 2e-05, 14, -5.94, 86.96, 2e-05, 16, 13.64, 19.44, 0.44408, 18, 25.24, -35.68, 0.04751, 19, 24.91, -28.32, 0.11441, 32, 20.73, 90.31, 0.05333, 33, -28.66, 78.04, 0.06667, 9, 6, 38, 69.72, 0.61146, 10, -85.94, 92.75, 0, 12, -105.21, 34.15, 2e-05, 14, -11.34, 77.79, 2e-05, 16, 3, 19, 0.18108, 18, 15.19, -39.19, 0.01542, 19, 17.2, -35.66, 0.033, 32, 27.16, 81.82, 0.134, 33, -22.23, 69.55, 0.025, 8, 6, 31.08, 57.33, 0.84661, 10, -88.14, 78.73, 0, 12, -106.7, 20.03, 1e-05, 14, -25.44, 79.47, 2e-05, 16, -3.24, 31.75, 0.00519, 18, 5.52, -28.8, 0.00071, 19, 4.11, -30.17, 0.00079, 32, 20.24, 69.43, 0.14667, 8, 6, 54.6, 71.58, 0.81461, 10, -71.03, 100.26, 0, 12, -90.68, 42.39, 1e-05, 14, -3.3, 63.15, 2e-05, 16, -4.92, 4.29, 0.00519, 18, 11.88, -55.56, 0.00071, 19, 20.91, -51.94, 0.00079, 32, 43.76, 83.69, 0.17867, 7, 6, 74.66, 72.77, 0.85084, 14, 5.43, 45.04, 0.00189, 15, -48.19, 0.34, 3e-05, 16, -15.38, -12.87, 0.0004, 17, 25.18, -30.82, 0.00019, 18, 6.85, -75.02, 0, 32, 63.82, 84.87, 0.14667, 6, 6, -31.83, -19.47, 0.28534, 7, 18.5, 0.09, 0.20456, 8, -0.51, 0.54, 0.42875, 9, -37.4, -57.32, 2e-05, 32, -42.67, -7.37, 0.048, 33, -92.06, -19.64, 0.03333, 5, 6, -19.7, -16.04, 0.428, 7, 6.01, 1.83, 0.30685, 8, -7.81, 10.82, 0.19312, 9, -38.96, -44.81, 2e-05, 32, -30.54, -3.94, 0.072, 6, 6, -29.29, 3.07, 0.35006, 7, 7.11, -19.52, 0.48146, 8, -22.6, -4.62, 0.01057, 18, -27.61, 45.29, 0.04324, 32, -40.13, 15.17, 0.048, 33, -89.52, 2.9, 0.06667, 6, 6, -19.51, -3.79, 0.49079, 7, 0.91, -9.31, 0.3475, 8, -19.41, 6.9, 0.00065, 18, -37.14, 38.09, 0.03906, 32, -30.35, 8.31, 0.072, 33, -79.74, -3.96, 0.05, 5, 6, -1.24, -38.28, 0.05416, 7, -1.94, 29.61, 0.4142, 8, 6.95, 35.67, 0.14667, 9, -14.21, -29.91, 0.33497, 33, -61.47, -38.44, 0.05, 2, 6, 78.79, 32.94, 0.792, 32, 67.95, 45.05, 0.208, 2, 6, 62.83, 29.1, 0.784, 32, 51.99, 41.21, 0.216, 2, 6, 63.94, -3.27, 0.744, 32, 53.1, 8.84, 0.256, 2, 6, 45.03, 19.48, 0.784, 32, 34.18, 31.58, 0.216, 2, 6, 28.26, 15.76, 0.784, 32, 17.42, 27.86, 0.216, 2, 6, 11.44, 9.43, 0.784, 32, 0.6, 21.53, 0.216, 2, 6, 85.54, -12.92, 0.784, 32, 74.7, -0.81, 0.216, 2, 6, 67.01, -21.37, 0.784, 32, 56.17, -9.26, 0.216, 2, 6, 48.6, -24.59, 0.784, 32, 37.76, -12.49, 0.216, 2, 6, 31.96, -27.51, 0.784, 32, 21.12, -15.41, 0.216, 2, 6, 17.87, -24.27, 0.744, 32, 7.03, -12.17, 0.256, 2, 6, 13.39, -30.69, 0.824, 32, 2.55, -18.59, 0.176, 2, 6, 115.98, 40.73, 0.832, 32, 105.14, 52.83, 0.168, 2, 6, 121.83, -3.15, 0.92, 32, 110.99, 8.95, 0.08, 2, 6, 109.57, -26.22, 0.93067, 32, 98.73, -14.12, 0.06933, 2, 6, 91.22, -40.83, 0.91467, 32, 80.38, -28.72, 0.08533, 2, 6, 75.27, -46.39, 0.89867, 32, 64.43, -34.29, 0.10133, 2, 6, 50.07, -46.51, 0.88533, 32, 39.23, -34.4, 0.11467, 2, 6, 32.18, -45.98, 0.87467, 32, 21.34, -33.88, 0.12533, 2, 6, 19.61, -41.69, 0.85867, 32, 8.77, -29.59, 0.14133, 2, 6, 4.36, -34.52, 0.85067, 32, -6.48, -22.41, 0.14933, 2, 6, -7.25, -26.71, 0.808, 32, -18.09, -14.6, 0.192, 2, 6, -11.89, -13.73, 0.816, 32, -22.73, -1.63, 0.184, 2, 6, -13.47, -3.57, 0.8, 32, -24.32, 8.53, 0.2, 2, 6, -13.08, 13.24, 0.824, 32, -23.92, 25.34, 0.176, 7, 6, -5.13, 27.44, 0.81348, 7, -24.81, -32.12, 0.00061, 12, -128.23, -21.69, 0, 16, -7.18, 78.53, 0.00163, 18, -11.82, 14.82, 0.01817, 19, -29.65, 2.45, 0.00611, 32, -15.97, 39.54, 0.16, 7, 6, 4.89, 38.85, 0.83215, 7, -38.57, -38.54, 0.00061, 12, -123.51, -7.26, 0, 16, -3.51, 63.8, 0.00163, 18, -4.04, 1.79, 0.01817, 19, -17.19, -6.22, 0.00611, 32, -5.95, 50.95, 0.14133, 7, 6, 20.32, 46.28, 0.84815, 7, -55.69, -39.14, 0.00061, 12, -112.24, 5.65, 0, 16, -6.19, 46.88, 0.00163, 18, -1.69, -15.18, 0.01817, 19, -8.07, -20.72, 0.00611, 32, 9.48, 58.38, 0.12533, 7, 6, 88.59, 67.99, 0.85617, 14, 6.32, 30.34, 0.00189, 15, -34.16, -4.15, 3e-05, 16, -27.23, -21.6, 0.0004, 17, 37.49, -38.89, 0.00019, 18, -1.96, -86.82, 0, 32, 77.75, 80.1, 0.14133], "hull": 64, "edges": [0, 126, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 128, 130, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 144, 146, 146, 148, 148, 150, 150, 152, 152, 128, 128, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 140, 104, 172, 172, 174, 174, 176, 176, 178, 178, 180, 116, 182, 182, 184, 184, 186, 4, 188, 188, 190, 190, 192, 192, 194, 194, 196, 92, 198, 198, 200, 200, 202, 200, 204, 204, 206, 70, 208, 208, 210, 74, 212, 212, 214, 58, 60, 60, 62, 60, 216, 154, 218, 218, 220, 130, 222, 222, 132, 220, 222, 220, 224, 224, 226, 226, 228, 230, 232, 232, 234, 234, 236, 236, 238, 142, 240, 240, 144, 186, 242, 196, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 202, 206, 274, 274, 186], "width": 173, "height": 202}}, "a5": {"a5": {"type": "mesh", "uvs": [0.26664, 0.03029, 0.26644, 0.08605, 0.23761, 0.11656, 0.26548, 0.18497, 0.20548, 0.29277, 0.28776, 0.34905, 0.30101, 0.42112, 0.27224, 0.47393, 0.25158, 0.49419, 0.31907, 0.54052, 0.34481, 0.65637, 0.42275, 0.65775, 0.47775, 0.70792, 0.47764, 0.77067, 0.48159, 0.7758, 0.58187, 0.76896, 0.6294, 0.7904, 0.67302, 0.73055, 0.73804, 0.71018, 0.71651, 0.60516, 0.7499, 0.56013, 0.7684, 0.51031, 0.73074, 0.40847, 0.7526, 0.34946, 0.79891, 0.31187, 0.79892, 0.27952, 0.78864, 0.1995, 0.78881, 0.12964, 0.85666, 0.09479, 0.93001, 0.09623, 0.98106, 0.14213, 1, 0.26516, 1, 0.26578, 0.94539, 0.28911, 0.92091, 0.29138, 0.9954, 0.40325, 0.9956, 0.46052, 0.89833, 0.49247, 0.90075, 0.52859, 0.94691, 0.64495, 0.91894, 0.74767, 0.84665, 0.73114, 0.8428, 0.74022, 0.84526, 0.84367, 0.825, 0.90883, 0.75164, 0.90896, 0.74868, 0.8953, 0.65343, 0.91849, 0.63376, 0.93692, 0.63349, 0.96837, 0.5675, 0.99563, 0.50843, 0.99589, 0.48392, 0.97624, 0.45686, 0.91478, 0.45631, 0.88979, 0.38161, 0.84499, 0.37123, 0.85399, 0.30361, 0.85412, 0.25472, 0.81712, 0.26122, 0.75686, 0.2611, 0.73578, 0.21827, 0.69592, 0.16702, 0.68417, 0.13638, 0.70293, 0.08827, 0.66226, 0.07215, 0.62484, 0.12757, 0.51746, 0.10336, 0.51207, 0.0368, 0.4781, 0.04629, 0.38997, 0.08356, 0.32303, 0.0667, 0.26964, 0.00555, 0.26965, 0.00394, 0.20088, 0.05775, 0.10655, 0.13154, 0.08994, 0.12733, 0.05548, 0.1557, 0.00489, 0.22683, 0.00395], "triangles": [51, 52, 50, 49, 50, 48, 52, 53, 48, 48, 50, 52, 14, 15, 54, 48, 54, 15, 16, 48, 15, 48, 16, 47, 47, 16, 46, 48, 53, 54, 45, 46, 44, 44, 46, 43, 16, 17, 46, 43, 46, 18, 54, 55, 14, 56, 57, 55, 59, 55, 57, 57, 58, 59, 59, 10, 55, 55, 13, 14, 13, 55, 10, 62, 65, 66, 17, 18, 46, 42, 18, 41, 60, 61, 10, 41, 19, 20, 39, 20, 38, 39, 41, 20, 20, 21, 38, 41, 18, 19, 63, 64, 62, 10, 61, 9, 9, 61, 8, 61, 62, 8, 62, 64, 65, 43, 18, 42, 13, 10, 11, 13, 11, 12, 59, 60, 10, 40, 41, 39, 21, 37, 38, 21, 22, 37, 67, 68, 69, 22, 23, 37, 23, 24, 37, 37, 35, 36, 37, 34, 35, 37, 24, 34, 70, 71, 4, 24, 25, 34, 4, 71, 3, 75, 3, 71, 3, 75, 2, 78, 76, 77, 2, 76, 78, 30, 33, 34, 32, 33, 31, 26, 30, 34, 29, 30, 28, 28, 30, 26, 34, 25, 26, 28, 26, 27, 72, 73, 71, 73, 74, 71, 71, 74, 75, 33, 30, 31, 2, 78, 1, 2, 75, 76, 1, 78, 0, 8, 66, 69, 8, 69, 7, 69, 66, 67, 7, 69, 70, 7, 70, 4, 7, 5, 6, 7, 4, 5, 62, 66, 8], "vertices": [1, 36, -17.86, -48.12, 1, 1, 36, -5.69, -49.06, 1, 1, 36, 0.56, -55.27, 1, 1, 36, 15.9, -50.83, 1, 1, 36, 38.57, -64.47, 1, 1, 36, 52.07, -49.05, 1, 2, 36, 68, -47.57, 0.96296, 37, -12.95, -47.22, 0.03704, 2, 36, 79.11, -54.13, 0.93185, 37, -2.04, -54.1, 0.06815, 2, 36, 83.24, -58.56, 0.74163, 37, 1.96, -58.64, 0.25837, 2, 36, 94.34, -45.91, 0.31437, 37, 13.43, -46.32, 0.68563, 3, 36, 120.02, -42.66, 0.09695, 37, 39.19, -43.82, 0.87881, 38, -13.42, -44, 0.02424, 3, 36, 121.46, -27.21, 0.03704, 37, 41.08, -28.42, 0.81481, 38, -11.73, -28.58, 0.14815, 2, 37, 53.13, -18.66, 0.62963, 38, 0.19, -18.66, 0.37037, 2, 37, 66.8, -20.09, 0.37037, 38, 13.88, -19.91, 0.62963, 2, 37, 67.99, -19.42, 0.18519, 38, 15.07, -19.23, 0.81481, 2, 37, 68.55, 0.58, 0.18519, 38, 15.36, 0.78, 0.81481, 2, 37, 74.19, 9.51, 0.37037, 38, 20.87, 9.78, 0.62963, 2, 37, 62.04, 19.48, 0.62963, 38, 8.6, 19.6, 0.37037, 2, 37, 58.92, 32.81, 0.928, 38, 5.31, 32.88, 0.072, 1, 37, 35.61, 30.91, 1, 1, 37, 26.48, 38.52, 1, 2, 36, 94.3, 43.75, 0.45837, 37, 16, 43.3, 0.54163, 2, 36, 71.51, 37.91, 0.91763, 37, -6.95, 38.13, 0.08237, 2, 36, 58.94, 43.2, 0.89985, 37, -19.36, 43.78, 0.10015, 2, 36, 51.4, 52.99, 0.96296, 37, -26.61, 53.79, 0.03704, 1, 36, 44.34, 53.52, 1, 1, 36, 26.71, 52.76, 1, 1, 36, 11.46, 53.92, 1, 1, 36, 4.84, 67.94, 1, 1, 36, 6.22, 82.48, 1, 1, 36, 16.99, 91.87, 1, 1, 36, 44.14, 93.65, 1, 1, 36, 44.27, 93.64, 1, 1, 36, 48.57, 82.43, 1, 1, 36, 48.71, 77.54, 1, 2, 36, 74.23, 90.52, 0.96296, 37, -2.7, 90.64, 0.03704, 2, 36, 86.74, 89.64, 0.89985, 37, 9.78, 89.4, 0.10015, 3, 36, 92.3, 69.83, 0.74287, 37, 14.76, 69.42, 0.23733, 38, -39.33, 68.91, 0.01979, 3, 36, 100.23, 69.72, 0.44494, 37, 22.68, 69.09, 0.47852, 38, -31.41, 68.68, 0.07654, 1, 37, 48.97, 75.62, 1, 3, 36, 148.34, 69.82, 0.00089, 37, 70.78, 67.78, 0.992, 38, 16.7, 68.01, 0.00711, 2, 37, 65.7, 53.84, 0.93363, 38, 11.81, 54, 0.06637, 2, 37, 67.6, 52.88, 0.83852, 38, 13.72, 53.06, 0.16148, 2, 37, 90.19, 51.04, 0.33333, 38, 36.33, 51.52, 0.66667, 2, 37, 103.97, 45.57, 0.14815, 38, 50.18, 46.24, 0.85185, 2, 37, 102.51, 31.05, 0.03704, 38, 48.91, 31.69, 0.96296, 1, 38, 45.88, 31.37, 1, 1, 38, 49.24, 12.04, 1, 1, 38, 52.91, 7.78, 1, 1, 38, 59.77, 7.11, 1, 1, 38, 64.54, -6.5, 1, 1, 38, 63.55, -18.21, 1, 1, 38, 58.83, -22.69, 1, 1, 38, 44.94, -26.85, 1, 1, 38, 39.48, -26.47, 1, 1, 38, 28.38, -40.4, 1, 1, 38, 30.16, -42.63, 1, 2, 37, 81.43, -56.41, 0.03704, 38, 28.99, -56.04, 0.96296, 2, 37, 72.37, -65.26, 0.14815, 38, 20.05, -65, 0.85185, 2, 37, 59.38, -62.62, 0.37037, 38, 7.02, -62.54, 0.62963, 2, 37, 54.78, -62.17, 0.77363, 38, 2.42, -62.15, 0.22637, 2, 37, 45.23, -69.76, 0.89185, 38, -7.04, -69.86, 0.10815, 3, 36, 123.5, -78.39, 0.096, 37, 41.62, -79.64, 0.87052, 38, -10.51, -79.79, 0.03348, 2, 36, 127.15, -84.77, 0.15704, 37, 45.09, -86.13, 0.84296, 2, 36, 117.56, -93.67, 0.24415, 37, 35.24, -94.74, 0.75585, 2, 36, 109.16, -96.27, 0.34637, 37, 26.76, -97.09, 0.65363, 2, 36, 86.51, -83.54, 0.80563, 37, 4.5, -83.71, 0.19437, 2, 36, 84.98, -88.26, 0.776, 37, 2.83, -88.38, 0.224, 2, 36, 76.59, -100.93, 0.976, 37, -5.92, -100.8, 0.024, 1, 36, 57.48, -97.63, 1, 1, 36, 43.4, -89.16, 1, 1, 36, 31.5, -91.64, 1, 1, 36, 30.61, -103.78, 1, 1, 36, 15.56, -103, 1, 1, 36, -4.25, -90.8, 1, 1, 36, -6.81, -75.89, 1, 1, 36, -14.39, -76.17, 1, 1, 36, -25.03, -69.73, 1, 1, 36, -24.2, -55.6, 1], "hull": 79, "edges": [0, 156, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156], "width": 199, "height": 219}}, "a6": {"a6": {"x": 23.04, "y": 2.01, "rotation": 123.06, "width": 51, "height": 46}}, "a7": {"a7": {"type": "mesh", "uvs": [0.69001, 0.0102, 0.85303, 0.06538, 0.98693, 0.15278, 0.98726, 0.20754, 0.89401, 0.35369, 0.89403, 0.4345, 0.78727, 0.61822, 0.78686, 0.71884, 0.73579, 0.85223, 0.64133, 0.94741, 0.56061, 0.98979, 0.33188, 0.98983, 0.22624, 0.96844, 0.09333, 0.86522, 0.07972, 0.83309, 0.07984, 0.68685, 0.10641, 0.57273, 0.10647, 0.51103, 0.01252, 0.40067, 0.01302, 0.34201, 0.0539, 0.2494, 0.24027, 0.10372, 0.48162, 0.00961, 0.47902, 0.23956, 0.45033, 0.51136, 0.41446, 0.71311, 0.38936, 0.83921, 0.13708, 0.34428, 0.86007, 0.21123], "triangles": [10, 11, 26, 11, 12, 26, 23, 22, 0, 23, 21, 22, 10, 26, 9, 12, 13, 26, 9, 26, 8, 13, 14, 26, 26, 25, 8, 8, 25, 7, 25, 26, 15, 26, 14, 15, 7, 25, 6, 15, 16, 25, 25, 24, 6, 25, 16, 24, 6, 24, 5, 4, 24, 23, 4, 23, 28, 5, 24, 4, 16, 17, 24, 28, 0, 1, 28, 23, 0, 17, 27, 24, 24, 27, 23, 17, 18, 27, 18, 19, 27, 4, 28, 3, 19, 20, 27, 27, 21, 23, 27, 20, 21, 28, 2, 3, 28, 1, 2], "vertices": [2, 21, -18.31, 17.86, 0.33213, 20, 54.56, 24.97, 0.66787, 2, 21, -13.99, 30.46, 0.56648, 20, 65.08, 33.14, 0.43352, 2, 21, -6.42, 41.14, 0.69527, 20, 77.27, 37.91, 0.30473, 2, 21, -1.18, 41.58, 0.72328, 20, 81.89, 35.41, 0.27672, 2, 21, 13.36, 35.71, 0.89136, 20, 90.83, 22.52, 0.10864, 2, 21, 21.09, 36.32, 0.95699, 20, 97.63, 18.79, 0.04301, 3, 21, 39.31, 29.73, 0.98109, 20, 109.24, 3.29, 0.00027, 22, -36.76, 13.86, 0.01863, 2, 21, 48.94, 30.46, 0.91671, 22, -28.64, 19.1, 0.08329, 2, 21, 62.01, 27.66, 0.71314, 22, -15.82, 22.88, 0.28686, 2, 21, 71.67, 21.31, 0.4115, 22, -4.3, 21.92, 0.5885, 2, 21, 76.21, 15.6, 0.22446, 22, 2.42, 19.07, 0.77554, 1, 22, 11.78, 4.69, 1, 2, 21, 76.14, -9.56, 0.04286, 22, 14.38, -3.07, 0.95714, 2, 21, 67.05, -20.28, 0.51257, 22, 11.51, -16.83, 0.48743, 2, 21, 64.06, -21.54, 0.58976, 22, 9.48, -19.36, 0.41024, 2, 21, 50.06, -22.64, 0.9118, 22, -2.29, -27.01, 0.0882, 3, 21, 38.98, -21.52, 0.99218, 20, 80.85, -39.38, 4e-05, 22, -12.56, -31.32, 0.00779, 3, 21, 33.08, -21.98, 0.99668, 20, 75.66, -36.52, 0.00295, 22, -17.53, -34.55, 0.00037, 2, 21, 23.07, -29.84, 0.96421, 20, 62.98, -37.6, 0.03579, 2, 21, 17.45, -30.25, 0.93934, 20, 58.06, -34.86, 0.06066, 2, 21, 8.35, -27.89, 0.85311, 20, 51.74, -27.9, 0.14689, 2, 21, -6.7, -15.06, 0.25418, 20, 46.2, -8.92, 0.74582, 2, 21, -17.13, 2.27, 0.03426, 20, 46.99, 11.3, 0.96574, 1, 21, 4.89, 3.82, 1, 1, 21, 31.07, 3.73, 1, 1, 21, 50.59, 2.58, 1, 1, 21, 62.81, 1.66, 1, 2, 21, 16.94, -20.95, 0.93697, 20, 62.73, -26.81, 0.06303, 2, 21, -0.07, 32.09, 0.7295, 20, 77.61, 26.87, 0.2705], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 36, 54, 4, 56], "width": 75, "height": 96}}, "a8": {"a8": {"type": "mesh", "uvs": [0.10086, 0.00192, 0.19502, 0.06504, 0.44272, 0.17367, 0.73752, 0.18533, 0.96798, 0.11338, 0.99914, 0.15908, 0.95758, 0.23556, 0.94134, 0.35388, 0.94094, 0.39429, 0.9788, 0.4508, 0.93255, 0.51837, 0.76312, 0.60102, 0.76408, 0.71491, 0.764, 0.79528, 0.76536, 0.86972, 0.76393, 0.93966, 0.76284, 1, 0.73942, 1, 0.68452, 0.97573, 0.55124, 0.9232, 0.5776, 0.8493, 0.6039, 0.78761, 0.62747, 0.69782, 0.63043, 0.62198, 0.55834, 0.62275, 0.51692, 0.6979, 0.4705, 0.75485, 0.41029, 0.81031, 0.35023, 0.84697, 0.29466, 0.73285, 0.20811, 0.64906, 0.25368, 0.63981, 0.30023, 0.63657, 0.35311, 0.62894, 0.39837, 0.59763, 0.2429, 0.55646, 0.09592, 0.43953, 0.04945, 0.36862, 0.0686, 0.02039, 0.46087, 0.54213, 0.5183, 0.47192, 0.58187, 0.48836, 0.64032, 0.50927, 0.69775, 0.46595, 0.73261, 0.52271], "triangles": [21, 18, 20, 21, 14, 18, 15, 18, 14, 19, 20, 18, 17, 18, 15, 16, 17, 15, 12, 23, 11, 12, 22, 23, 22, 13, 14, 12, 13, 22, 14, 21, 22, 29, 31, 32, 30, 31, 29, 33, 29, 32, 27, 29, 33, 27, 28, 29, 25, 39, 24, 26, 34, 25, 27, 34, 26, 33, 34, 27, 23, 44, 11, 11, 44, 10, 6, 3, 4, 7, 3, 6, 7, 43, 3, 2, 41, 40, 2, 3, 41, 43, 41, 3, 42, 41, 43, 39, 2, 40, 23, 41, 42, 24, 40, 41, 24, 41, 23, 38, 1, 37, 1, 38, 0, 36, 37, 1, 35, 1, 2, 35, 2, 39, 36, 1, 35, 39, 40, 24, 6, 4, 5, 7, 44, 43, 7, 8, 44, 10, 8, 9, 10, 44, 8, 42, 43, 44, 23, 42, 44, 34, 35, 39, 34, 39, 25], "vertices": [1, 4, 23.53, 62.89, 1, 1, 4, 20.75, 50.98, 1, 1, 4, 17.99, 20.58, 1, 1, 4, 24.05, -13.68, 1, 1, 4, 35.21, -39.16, 1, 1, 4, 32.32, -43.5, 1, 1, 4, 25.27, -39.94, 1, 1, 4, 15.5, -39.99, 1, 1, 4, 12.28, -40.6, 1, 1, 4, 8.69, -45.89, 1, 1, 4, 2.24, -41.64, 1, 1, 4, -8.33, -23.4, 1, 2, 62, 7.64, 8.52, 0.9888, 63, -9.57, 7.84, 0.0112, 2, 62, 14.12, 9.14, 0.71385, 63, -3.16, 8.93, 0.28615, 2, 62, 20.11, 9.87, 0.22888, 63, 2.76, 10.11, 0.77112, 2, 62, 25.77, 10.25, 0.02789, 63, 8.37, 10.9, 0.97211, 1, 63, 13.21, 11.59, 1, 1, 63, 13.68, 8.87, 1, 1, 63, 12.83, 2.15, 1, 1, 63, 11.3, -14.07, 1, 2, 62, 20.59, -12.34, 0.11071, 63, 4.87, -12.01, 0.88929, 2, 62, 15.31, -9.73, 0.50222, 63, -0.58, -9.8, 0.49778, 4, 4, -19.23, -9.29, 0.01155, 60, -11.63, 14.19, 0.00673, 62, 7.81, -7.66, 0.95426, 63, -8.21, -8.29, 0.02746, 3, 4, -13.14, -8.4, 0.19048, 60, -14.93, 9, 0.08465, 62, 1.66, -7.9, 0.72487, 3, 4, -14.91, -0.08, 0.24955, 60, -7.47, 4.9, 0.61285, 62, 2.54, -16.36, 0.1376, 3, 4, -21.85, 3.49, 0.00518, 60, -0.24, 7.84, 0.98792, 62, 9.06, -20.64, 0.00691, 2, 60, 6.79, 9.2, 0.99725, 61, -11.29, 5.97, 0.00275, 3, 60, 15.19, 9.66, 0.69, 61, -3.46, 9.03, 0.31, 62, 19.33, -32.29, 0, 3, 60, 22.82, 8.8, 0.23479, 61, 4.06, 10.59, 0.76521, 62, 22.96, -39.06, 0, 1, 61, 8.74, 0.26, 1, 1, 61, 17.47, -8.35, 1, 1, 61, 12.04, -8.06, 1, 2, 60, 19.67, -8.96, 0.05049, 61, 6.6, -7.27, 0.94951, 3, 4, -20.26, 23.54, 0.00292, 60, 13.92, -6.46, 0.60051, 61, 0.36, -6.68, 0.39657, 4, 4, -16.7, 18.82, 0.93762, 60, 8.02, -6.07, 0.0621, 61, -5.37, -8.15, 0.00027, 62, 2.32, -35.34, 0, 1, 4, -17.12, 37.46, 1, 1, 4, -11.32, 56.35, 1, 1, 4, -6.79, 62.88, 1, 1, 4, 21.3, 66.32, 1, 1, 4, -10.82, 12.5, 1, 1, 4, -3.89, 7, 1, 1, 4, -3.68, -0.62, 1, 1, 4, -3.96, -7.71, 1, 1, 4, 0.84, -13.65, 1, 1, 4, -2.84, -18.6, 1], "hull": 39, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 32, 34, 34, 36, 36, 38, 54, 56, 56, 58, 58, 60, 68, 70, 70, 72, 72, 74, 74, 76, 30, 32, 22, 24, 24, 26, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 60, 62, 66, 68, 26, 28, 28, 30, 62, 64, 64, 66, 76, 0, 68, 78, 78, 80, 80, 82, 82, 84, 86, 88, 88, 22], "width": 118, "height": 81}}, "run1/huoq_02": {"run1/huoq_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.22, 63.78, 63.78, 64.22, 64.22, -63.78, -63.78, -64.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}, "run1/huoq_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.22, 63.78, 63.78, 64.22, 64.22, -63.78, -63.78, -64.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}, "run1/huoq_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.22, 63.78, 63.78, 64.22, 64.22, -63.78, -63.78, -64.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}, "run1/huoq_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.22, 63.78, 63.78, 64.22, 64.22, -63.78, -63.78, -64.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}, "run1/huoq_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.22, 63.78, 63.78, 64.22, 64.22, -63.78, -63.78, -64.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}, "run1/huoq_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.22, 63.78, 63.78, 64.22, 64.22, -63.78, -63.78, -64.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}, "run1/huoq_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.22, 63.78, 63.78, 64.22, 64.22, -63.78, -63.78, -64.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}, "run1/huoq_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.22, 63.78, 63.78, 64.22, 64.22, -63.78, -63.78, -64.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}}, "a14": {"a14": {"type": "mesh", "uvs": [0.36092, 0.03851, 0.44057, 0.00488, 0.56876, 0.00485, 0.68376, 0.04685, 0.7603, 0.1199, 0.80417, 0.22811, 0.95376, 0.34216, 0.99552, 0.36133, 0.9955, 0.43661, 0.94675, 0.53407, 0.86159, 0.65131, 0.7817, 0.76129, 0.74129, 0.80577, 0.72402, 0.83351, 0.72167, 0.87404, 0.67304, 0.91691, 0.59882, 0.98234, 0.54152, 0.99512, 0.3954, 0.99499, 0.31246, 0.97922, 0.2301, 0.94198, 0.13929, 0.7916, 0.15579, 0.70253, 0.14828, 0.65408, 0.12456, 0.60168, 0.09375, 0.55933, 0.04367, 0.5018, 0.00458, 0.41208, 0.00436, 0.25325, 0.02837, 0.1573, 0.06744, 0.11432, 0.11357, 0.12236, 0.12795, 0.12231, 0.24938, 0.08517, 0.60482, 0.19476, 0.6106, 0.35235, 0.60337, 0.50517, 0.57593, 0.62456, 0.54415, 0.73121, 0.09, 0.20273, 0.07556, 0.31256, 0.07845, 0.40966, 0.09434, 0.50358, 0.33412, 0.17248, 0.34134, 0.32688, 0.36445, 0.46537, 0.40345, 0.59113, 0.45401, 0.70733, 0.51772, 0.80508, 0.50601, 0.89358, 0.79462, 0.33981, 0.76718, 0.48466, 0.71662, 0.59928, 0.63862, 0.68364, 0.93618, 0.43372, 0.65963, 0.84183, 0.2003, 0.79726, 0.2197, 0.18758, 0.20814, 0.32607, 0.2197, 0.4757, 0.25436, 0.61419, 0.30925, 0.76064, 0.3627, 0.8673], "triangles": [34, 2, 3, 1, 2, 34, 43, 0, 1, 43, 33, 0, 34, 44, 43, 15, 55, 14, 57, 32, 33, 39, 30, 31, 29, 30, 39, 28, 29, 39, 40, 28, 39, 39, 31, 32, 57, 39, 32, 58, 39, 57, 40, 39, 58, 57, 43, 44, 58, 57, 44, 50, 5, 6, 50, 35, 5, 41, 40, 58, 27, 28, 40, 27, 40, 41, 54, 50, 6, 54, 6, 7, 8, 54, 7, 35, 44, 34, 35, 45, 44, 59, 58, 44, 59, 44, 45, 41, 58, 59, 51, 35, 50, 51, 50, 54, 26, 27, 41, 42, 26, 41, 59, 42, 41, 36, 45, 35, 36, 35, 51, 9, 54, 8, 51, 54, 9, 25, 26, 42, 46, 45, 36, 52, 36, 51, 24, 42, 59, 25, 42, 24, 60, 59, 45, 60, 45, 46, 24, 59, 60, 37, 46, 36, 37, 36, 52, 10, 51, 9, 52, 51, 10, 23, 24, 60, 53, 37, 52, 22, 23, 60, 47, 46, 37, 38, 47, 37, 38, 37, 53, 61, 60, 46, 61, 46, 47, 22, 60, 61, 11, 52, 10, 53, 52, 11, 48, 47, 38, 12, 53, 11, 12, 55, 53, 13, 55, 12, 38, 53, 55, 48, 38, 55, 62, 61, 47, 62, 47, 48, 14, 55, 13, 57, 33, 43, 5, 35, 34, 49, 48, 55, 5, 34, 4, 43, 1, 34, 34, 3, 4, 49, 62, 48, 16, 49, 55, 62, 56, 61, 61, 56, 22, 62, 20, 56, 21, 56, 20, 19, 20, 62, 15, 16, 55, 18, 62, 49, 19, 62, 18, 17, 49, 16, 18, 49, 17, 56, 21, 22], "vertices": [4, 5, 142.16, 56.82, 0.61311, 6, 45.28, 55.8, 0.27789, 35, 74.59, 66.84, 0.075, 34, 122.98, 67.42, 0.034, 3, 5, 151.99, 41.23, 0.41652, 6, 54.74, 40, 0.48348, 35, 84.42, 51.25, 0.1, 4, 5, 157.39, 14.08, 0.38789, 6, 59.53, 12.73, 0.50111, 35, 89.83, 24.1, 0.075, 34, 138.21, 24.68, 0.036, 3, 5, 154.17, -11.89, 0.39612, 6, 55.72, -13.16, 0.50388, 35, 86.6, -1.87, 0.1, 3, 5, 143.35, -30.9, 0.55485, 6, 44.47, -31.92, 0.34515, 35, 75.79, -20.88, 0.1, 4, 5, 124.4, -44.33, 0.74698, 6, 25.22, -44.92, 0.12602, 35, 56.83, -34.32, 0.075, 34, 105.22, -33.74, 0.052, 3, 5, 108.79, -80.39, 0.85873, 6, 8.79, -80.61, 0.04127, 35, 41.22, -70.37, 0.1, 3, 5, 106.86, -89.97, 0.89556, 6, 6.64, -90.14, 0.00444, 35, 39.29, -79.95, 0.1, 2, 5, 92.39, -92.84, 0.9, 35, 24.82, -82.82, 0.1, 2, 5, 71.6, -86.24, 0.9, 35, 4.03, -76.22, 0.1, 2, 5, 45.47, -72.69, 0.9, 35, -22.1, -62.67, 0.1, 2, 5, 20.96, -59.97, 0.9, 35, -46.61, -49.95, 0.1, 3, 4, 51.59, -53.05, 0.025, 5, 10.71, -53.11, 0.875, 35, -56.86, -43.09, 0.1, 3, 4, 45.51, -50.49, 0.09375, 5, 4.65, -50.51, 0.80625, 35, -62.92, -40.49, 0.1, 3, 4, 37.63, -51.58, 0.24219, 5, -3.24, -51.57, 0.65781, 35, -70.81, -41.55, 0.1, 3, 4, 27.29, -42.98, 0.52708, 5, -13.53, -42.9, 0.37292, 35, -81.1, -32.89, 0.1, 3, 4, 11.51, -29.85, 0.75833, 5, -29.24, -29.68, 0.14167, 35, -96.81, -19.66, 0.1, 3, 4, 6.57, -18.22, 0.86667, 5, -34.11, -18.03, 0.03333, 35, -101.68, -8.02, 0.1, 2, 4, 0.26, 12.7, 0.9, 35, -107.82, 22.95, 0.1, 3, 4, -0.3, 30.87, 0.875, 5, -40.71, 31.1, 0.025, 35, -108.28, 41.12, 0.1, 3, 4, 3.28, 49.76, 0.80625, 5, -37.03, 49.97, 0.09375, 35, -104.6, 59.99, 0.1, 3, 4, 28.22, 74.89, 0.65781, 5, -11.95, 74.96, 0.24219, 35, -79.52, 84.98, 0.1, 3, 4, 46.03, 74.91, 0.37292, 5, 5.87, 74.87, 0.52708, 35, -61.7, 84.89, 0.1, 3, 4, 55.01, 78.4, 0.14167, 5, 14.87, 78.32, 0.75833, 35, -52.7, 88.34, 0.1, 4, 4, 64.04, 85.48, 0.02667, 5, 23.94, 85.35, 0.72, 35, -43.63, 95.37, 0.05333, 23, 32.52, 47.27, 0.2, 3, 5, 30.78, 93.49, 0.58987, 35, -36.79, 103.51, 0.04213, 23, 33.14, 36.65, 0.368, 3, 5, 39.73, 106.31, 0.58987, 35, -27.84, 116.32, 0.04213, 23, 35.43, 21.19, 0.368, 3, 5, 55.33, 118.02, 0.5688, 35, -12.24, 128.04, 0.0632, 23, 32.12, 1.97, 0.368, 3, 5, 85.85, 124.14, 0.5688, 35, 18.28, 134.16, 0.0632, 23, 14.12, -23.43, 0.368, 3, 5, 105.31, 122.73, 0.5688, 35, 37.74, 132.74, 0.0632, 23, -1, -35.76, 0.368, 3, 5, 115.22, 116.09, 0.5688, 35, 47.65, 126.11, 0.0632, 23, -12.76, -37.74, 0.368, 4, 5, 115.62, 106.01, 0.57924, 6, 19.86, 105.59, 0.00536, 35, 48.05, 116.03, 0.0474, 23, -19.97, -30.68, 0.368, 4, 5, 116.23, 102.97, 0.54017, 6, 20.4, 102.53, 0.02863, 35, 48.66, 112.99, 0.0632, 23, -22.5, -28.89, 0.368, 4, 5, 128.49, 78.67, 0.7765, 6, 32.11, 77.95, 0.1205, 35, 60.92, 88.68, 0.075, 34, 109.31, 89.26, 0.028, 4, 5, 122.41, -0.83, 0.51592, 6, 24.22, -1.38, 0.32275, 35, 54.84, 9.19, 0.03333, 34, 103.23, 9.77, 0.128, 3, 5, 92.36, -8.08, 0.66333, 6, -5.99, -7.94, 0.12867, 34, 73.18, 2.52, 0.208, 3, 5, 62.68, -12.39, 0.73197, 6, -35.76, -11.58, 0.02803, 34, 43.5, -1.8, 0.24, 3, 4, 79.21, -10.93, 0.02111, 5, 38.57, -11.15, 0.73889, 34, 19.39, -0.55, 0.24, 3, 4, 57.36, -8.4, 0.06861, 5, 16.73, -8.5, 0.69139, 34, -2.45, 2.1, 0.24, 3, 5, 99.17, 107.93, 0.61093, 35, 31.61, 117.95, 0.02107, 23, -6.69, -20.79, 0.368, 2, 5, 77.45, 106.79, 0.632, 23, 8.32, -5.05, 0.368, 2, 5, 58.91, 102.46, 0.632, 23, 18.84, 10.82, 0.368, 2, 5, 41.52, 95.5, 0.632, 23, 26.71, 27.82, 0.368, 4, 5, 115.28, 57.37, 0.67726, 6, 18.41, 56.97, 0.16408, 35, 47.71, 67.39, 0.03333, 34, 96.1, 67.97, 0.12533, 3, 5, 85.9, 49.94, 0.73481, 6, -11.12, 50.2, 0.05986, 34, 66.72, 60.54, 0.20533, 3, 5, 60.26, 39.74, 0.74727, 6, -37, 40.6, 0.01273, 34, 41.07, 50.34, 0.24, 3, 4, 78.16, 26.88, 0.02111, 5, 37.73, 26.67, 0.73889, 34, 18.54, 37.27, 0.24, 3, 4, 58.04, 11.61, 0.06861, 5, 17.52, 11.51, 0.69139, 34, -1.66, 22.11, 0.24, 3, 4, 42.03, -5.71, 0.13854, 5, 1.41, -5.72, 0.62146, 34, -17.77, 4.88, 0.24, 3, 4, 24.53, -6.72, 0.21375, 5, -16.09, -6.63, 0.54625, 34, -35.27, 3.97, 0.24, 4, 5, 102.53, -46.58, 0.78744, 6, 3.3, -46.67, 0.04056, 35, 34.96, -36.57, 0.03333, 34, 83.35, -35.98, 0.13867, 3, 5, 73.53, -46.31, 0.78679, 6, -25.69, -45.74, 0.00521, 34, 54.34, -35.71, 0.208, 2, 5, 49.36, -39.99, 0.78133, 34, 30.18, -29.39, 0.21867, 2, 5, 29.86, -26.69, 0.776, 34, 10.68, -16.09, 0.224, 2, 5, 90.44, -80.16, 0.9, 35, 22.87, -70.15, 0.1, 3, 4, 41.12, -37.19, 0.15937, 5, 0.33, -37.19, 0.74062, 35, -67.23, -27.17, 0.1, 3, 4, 29.77, 61.76, 0.74062, 5, -10.46, 61.82, 0.15937, 35, -78.03, 71.84, 0.1, 5, 5, 107.55, 81.04, 0.61418, 6, 11.23, 80.8, 0.03359, 35, 39.98, 91.05, 0.0248, 34, 88.37, 91.63, 0.07142, 23, -31.24, -6.98, 0.256, 4, 5, 80.44, 78.19, 0.56057, 6, -15.94, 78.57, 0.0075, 34, 61.26, 88.78, 0.10394, 23, -13.49, 13.7, 0.328, 3, 4, 92.36, 70.3, 0.03052, 5, 52.17, 70.01, 0.79348, 34, 32.99, 80.61, 0.176, 3, 4, 67.27, 57.52, 0.10681, 5, 27.01, 57.37, 0.71718, 34, 7.83, 67.97, 0.176, 3, 4, 41.53, 40.15, 0.22126, 5, 1.17, 40.14, 0.60274, 34, -18.01, 50.74, 0.176, 3, 4, 23.36, 24.64, 0.28611, 5, -17.08, 24.74, 0.53789, 34, -36.26, 35.34, 0.176], "hull": 34, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 4, 68, 68, 70, 70, 72, 72, 74, 74, 76, 62, 78, 78, 80, 80, 82, 82, 84, 2, 0, 0, 66, 0, 86, 86, 88, 88, 90, 90, 92, 92, 94, 76, 96, 94, 96, 96, 98, 10, 100, 100, 102, 102, 104, 104, 106, 14, 108, 28, 30, 30, 32, 18, 20, 20, 22, 28, 110, 42, 112, 66, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124], "width": 216, "height": 196}}, "tx/1_6": {"tx/1_00006": {"y": 0.5, "width": 116, "height": 77}, "tx/1_00009": {"y": 0.5, "width": 116, "height": 77}, "tx/1_00010": {"y": 0.5, "width": 116, "height": 77}, "tx/1_00012": {"y": 0.5, "width": 116, "height": 77}, "tx/1_00014": {"y": 0.5, "width": 116, "height": 77}}, "run1/quan20": {"run1/quan20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"a28": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "tx/dg_0001": {"color": [{"time": 0.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffff00"}], "attachment": [{"time": 0.2, "name": "tx/xuanzhuandaoguang_02"}, {"time": 0.3667, "name": null}]}}, "bones": {"bone2": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": -18.82, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 0.6}], "translate": [{"y": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -49.24, "y": 1.53, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": 57.25, "y": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 39.75, "y": 1.53, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "y": -4.19}]}, "bone4": {"translate": [{"y": 0.49}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.03, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 8.98, "curve": "stepped"}, {"time": 0.3333, "angle": 8.98, "curve": 0.854, "c3": 0.75}, {"time": 0.6}], "translate": [{"x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": 0.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.8, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 3.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.73, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": 0.93}]}, "bone7": {"rotate": [{"angle": -1.85}]}, "bone8": {"rotate": [{"angle": -2.43}]}, "bone9": {"rotate": [{"angle": -8.03}]}, "bone10": {"rotate": [{"angle": -0.68}]}, "bone11": {"rotate": [{"angle": -13.48}]}, "bone12": {"rotate": [{"angle": -5.39}]}, "bone13": {"rotate": [{"angle": -26.5}]}, "bone14": {"rotate": [{"angle": -2.21}]}, "bone15": {"rotate": [{"angle": -9.79}]}, "bone16": {"rotate": [{"angle": -9.08}]}, "bone17": {"rotate": [{"angle": -12.9}]}, "bone18": {"rotate": [{"angle": -1.31}]}, "bone19": {"rotate": [{"angle": -14.61}]}, "bone20": {"rotate": [{"angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -60.59, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 21.43, "curve": "stepped"}, {"time": 0.3333, "angle": 21.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 1.22}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 18.38, "y": -25.78, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": 28.67, "y": 40.32, "curve": "stepped"}, {"time": 0.3333, "x": 28.67, "y": 40.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone21": {"rotate": [{"angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.31, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 42.96, "curve": "stepped"}, {"time": 0.3333, "angle": 42.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 4.66}], "translate": [{"time": 0.1667, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": -17.11, "y": 2.44, "curve": "stepped"}, {"time": 0.3333, "x": -17.11, "y": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone22": {"rotate": [{"angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.22, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 12.42}]}, "bone23": {"rotate": [{"angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.12, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 88.02, "curve": "stepped"}, {"time": 0.3333, "angle": 88.02, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": 6.78}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 9.39, "y": -1.78, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": -35.63, "y": 4.17, "curve": "stepped"}, {"time": 0.3333, "x": -35.63, "y": 4.17, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "bone24": {"rotate": [{"angle": -1.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.87, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 2.71, "curve": "stepped"}, {"time": 0.3333, "angle": 2.71, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": -1.93}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 90.17, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 110.95, "curve": "stepped"}, {"time": 0.3333, "angle": 110.95, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "a22": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 11.46, "y": 182.88, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": 9.67, "y": 159.05, "curve": "stepped"}, {"time": 0.3333, "x": 9.67, "y": 159.05, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "a19": {"rotate": [{"angle": -19.2}]}, "a20": {"rotate": [{"angle": -12.58}]}, "a1": {"rotate": [{"angle": 10.73}]}, "a0": {"rotate": [{"angle": 18.43}]}, "bone27": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": 4.99, "y": -16.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 2.99, "y": -10.14, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "bone29": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": 11.84, "y": -30.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.41, "y": -4.48, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "bone31": {"rotate": [{"angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.47, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 18.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 3.75}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -1.23, "y": 3.72, "curve": 0.25, "c3": 0}, {"time": 0.2333}]}, "bone32": {"rotate": [{"angle": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.75, "curve": 0.25, "c3": 0}, {"time": 0.3667, "angle": 23.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.32}]}, "bone33": {"rotate": [{"angle": -5.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 13.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": -20.35, "curve": 0.25, "c3": 0}, {"time": 0.4333, "angle": 23.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.24}]}, "bone35": {"translate": [{"x": -0.92, "y": -0.03, "curve": "stepped"}, {"time": 0.1667, "x": -0.92, "y": -0.03, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": -1.67, "y": 20.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.92, "y": -0.03}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.23, "curve": "stepped"}, {"time": 0.3333, "angle": 4.23, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.86, "curve": "stepped"}, {"time": 0.3333, "angle": -8.86, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "bone47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.22, "curve": 0.25, "c3": 0}, {"time": 0.2333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -48.35, "curve": 0.25, "c3": 0}, {"time": 0.2333}]}, "bone39": {"rotate": [{"angle": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.64, "curve": "stepped"}, {"time": 0.3333, "angle": 3.64, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": -1.37}]}, "bone40": {"rotate": [{"angle": 3, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.73, "curve": "stepped"}, {"time": 0.3333, "angle": -8.73, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": 3}]}, "bone41": {"translate": [{"x": -1.37, "y": -0.05}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.39, "curve": "stepped"}, {"time": 0.3333, "angle": 4.39, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.66, "curve": "stepped"}, {"time": 0.3333, "angle": -7.66, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "bone49": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": 54.55, "y": -0.78, "curve": "stepped"}, {"time": 0.3333, "x": 54.55, "y": -0.78, "curve": 0.854, "c3": 0.75}, {"time": 0.6}]}, "bone45": {"rotate": [{"angle": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.6, "curve": "stepped"}, {"time": 0.3333, "angle": 4.6, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": -0.57}]}, "bone46": {"rotate": [{"angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.37, "curve": "stepped"}, {"time": 0.3333, "angle": -8.37, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": 1.68}]}, "bone51": {"rotate": [{"angle": 1.37}]}, "bone52": {"rotate": [{"angle": -15.1}]}, "bone53": {"rotate": [{"angle": -4.62}]}, "bone54": {"rotate": [{"angle": -14.51}]}, "bone55": {"rotate": [{"angle": -5.79, "curve": "stepped"}, {"time": 0.1667, "angle": -5.79, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": -26.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.79}]}, "bone56": {"rotate": [{"angle": 6.42}]}, "bone57": {"rotate": [{"angle": 0.49, "curve": "stepped"}, {"time": 0.1667, "angle": 0.49, "curve": 0.25, "c3": 0}, {"time": 0.2333, "angle": 18.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 28.12, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": 0.49}], "translate": [{"time": 0.1667, "curve": 0.25, "c3": 0}, {"time": 0.2333, "x": -13.18, "y": -7.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone58": {"rotate": [{"angle": 9.27}]}, "bone60": {"rotate": [{"angle": 0.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.69, "curve": "stepped"}, {"time": 0.3333, "angle": 6.69, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": 0.19}]}, "bone61": {"rotate": [{"angle": -0.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.03, "curve": "stepped"}, {"time": 0.3333, "angle": -11.03, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": -0.19}]}, "bone62": {"rotate": [{"angle": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.91, "curve": "stepped"}, {"time": 0.3333, "angle": 6.91, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": -0.52}]}, "bone63": {"rotate": [{"angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.57, "curve": "stepped"}, {"time": 0.3333, "angle": -12.57, "curve": 0.854, "c3": 0.75}, {"time": 0.6, "angle": 1.8}]}, "daoguang": {"translate": [{"time": 0.2, "x": 136.14, "y": 191.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 340.82, "y": 202.76}], "scale": [{"time": 0.2, "y": 1.534, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.845, "y": 2.02}]}}, "deform": {"default": {"a28": {"a14": [{"time": 0.2333, "vertices": [-24.6866, -30.54393, -27.08206, -28.44167, -24.68678, -30.54412, 39.02995, 4.36197, -0.77435, -10.8084, -0.77408, -10.80832, 8.15015, 7.14094, -2.27505, -2.11425, -2.27502, -2.11423, 3.10428, -0.09621]}]}, "a15": {"a15": [{"time": 0.1667}, {"time": 0.2333, "vertices": [-6.82707, 10.96209, -12.84547, 12.13728, -14.56326, 16.5154, -12.31352, 13.35647, -9.75357, 4.25661, -5.67131, 9.00481, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.92366, 0.98664, -7.62515, 9.13335, -9.37656, 8.44614, -9.52101, -0.19911], "curve": "stepped"}, {"time": 0.3333, "vertices": [-6.82707, 10.96209, -12.84547, 12.13728, -14.56326, 16.5154, -12.31352, 13.35647, -9.75357, 4.25661, -5.67131, 9.00481, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.92366, 0.98664, -7.62515, 9.13335, -9.37656, 8.44614, -9.52101, -0.19911]}, {"time": 0.6}]}}}, "drawOrder": [{"time": 0.1667, "offsets": [{"slot": "a18", "offset": 14}, {"slot": "a7", "offset": 14}, {"slot": "a6", "offset": 14}, {"slot": "a22", "offset": 2}, {"slot": "a14", "offset": -7}, {"slot": "a8", "offset": -9}]}, {"time": 0.2333, "offsets": [{"slot": "a18", "offset": 4}, {"slot": "a7", "offset": 4}, {"slot": "a6", "offset": 4}, {"slot": "a22", "offset": 5}, {"slot": "a14", "offset": -4}, {"slot": "a8", "offset": -6}]}, {"time": 0.5667, "offsets": [{"slot": "a18", "offset": 4}, {"slot": "a7", "offset": 4}, {"slot": "a6", "offset": 4}, {"slot": "a22", "offset": 3}, {"slot": "a28", "offset": 1}]}], "events": [{"time": 0.2333, "name": "atk"}]}, "boss_attack3": {"slots": {"a28": {"color": [{"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00"}]}, "lshuo01_add/lshuo01_add_001": {"attachment": [{"time": 0.1667, "name": "lshuo01_add/lshuo01_add_001"}, {"time": 0.2, "name": "lshuo01_add/lshuo01_add_003"}, {"time": 0.2333, "name": "lshuo01_add/lshuo01_add_005"}, {"time": 0.2667, "name": "lshuo01_add/lshuo01_add_007"}, {"time": 0.3, "name": "lshuo01_add/lshuo01_add_009"}, {"time": 0.3333, "name": "lshuo01_add/lshuo01_add_011"}, {"time": 0.3667, "name": "lshuo01_add/lshuo01_add_013"}, {"time": 0.4, "name": "lshuo01_add/lshuo01_add_015"}, {"time": 0.4333, "name": "lshuo01_add/lshuo01_add_017"}, {"time": 0.4667, "name": null}]}, "tx/dg_0001": {"color": [{"time": 0.6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.5, "name": "tx/xuanzhuandaoguang_02"}, {"time": 0.6667, "name": null}]}, "nuqixunhuan04-add/uvxh_01": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffff00"}], "attachment": [{"name": "nuqixunhuan04-add/uvxh_01"}, {"time": 0.0667, "name": "nuqixunhuan04-add/uvxh_02"}, {"time": 0.1333, "name": "nuqixunhuan04-add/uvxh_03"}, {"time": 0.1667, "name": "nuqixunhuan04-add/uvxh_04"}, {"time": 0.2333, "name": "nuqixunhuan04-add/uvxh_05"}, {"time": 0.3, "name": "nuqixunhuan04-add/uvxh_06"}, {"time": 0.3667, "name": "nuqixunhuan04-add/uvxh_07"}, {"time": 0.4333, "name": "nuqixunhuan04-add/uvxh_01"}, {"time": 0.5333, "name": "nuqixunhuan04-add/uvxh_02"}, {"time": 0.6, "name": "nuqixunhuan04-add/uvxh_03"}, {"time": 0.6667, "name": "nuqixunhuan04-add/uvxh_04"}, {"time": 0.7333, "name": "nuqixunhuan04-add/uvxh_05"}, {"time": 0.8, "name": "nuqixunhuan04-add/uvxh_06"}, {"time": 0.8333, "name": "nuqixunhuan04-add/uvxh_07"}, {"time": 0.9, "name": "nuqixunhuan04-add/uvxh_01"}]}, "lshuo01_add/lshuo01_add_2": {"attachment": [{"time": 0.5, "name": "lshuo01_add/lshuo01_add_001"}, {"time": 0.5333, "name": "lshuo01_add/lshuo01_add_003"}, {"time": 0.5667, "name": "lshuo01_add/lshuo01_add_005"}, {"time": 0.6, "name": "lshuo01_add/lshuo01_add_007"}, {"time": 0.6333, "name": "lshuo01_add/lshuo01_add_009"}, {"time": 0.6667, "name": "lshuo01_add/lshuo01_add_011"}, {"time": 0.7, "name": "lshuo01_add/lshuo01_add_013"}, {"time": 0.7333, "name": "lshuo01_add/lshuo01_add_015"}, {"time": 0.7667, "name": "lshuo01_add/lshuo01_add_017"}, {"time": 0.8, "name": null}]}, "shuilang_add/xhs_01": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffff00"}], "attachment": [{"name": "shuilang_add/xhs_01"}, {"time": 0.1, "name": "shuilang_add/xhs_03"}, {"time": 0.1667, "name": "shuilang_add/xhs_05"}, {"time": 0.2333, "name": "shuilang_add/xhs_07"}, {"time": 0.3333, "name": "shuilang_add/xhs_09"}, {"time": 0.4333, "name": "shuilang_add/xhs_01"}, {"time": 0.5667, "name": "shuilang_add/xhs_03"}, {"time": 0.6667, "name": "shuilang_add/xhs_05"}, {"time": 0.7333, "name": "shuilang_add/xhs_07"}, {"time": 0.8333, "name": "shuilang_add/xhs_09"}, {"time": 0.9, "name": "shuilang_add/xhs_01"}]}, "lshuo01_add/lshuo01_add_1": {"attachment": [{"time": 0.1667, "name": "lshuo01_add/lshuo01_add_001"}, {"time": 0.2, "name": "lshuo01_add/lshuo01_add_003"}, {"time": 0.2333, "name": "lshuo01_add/lshuo01_add_005"}, {"time": 0.2667, "name": "lshuo01_add/lshuo01_add_007"}, {"time": 0.3, "name": "lshuo01_add/lshuo01_add_009"}, {"time": 0.3333, "name": "lshuo01_add/lshuo01_add_011"}, {"time": 0.3667, "name": "lshuo01_add/lshuo01_add_013"}, {"time": 0.4, "name": "lshuo01_add/lshuo01_add_015"}, {"time": 0.4333, "name": "lshuo01_add/lshuo01_add_017"}, {"time": 0.4667, "name": null}]}}, "bones": {"bone2": {"rotate": [{"time": 0.4667, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": -18.82, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 0.9}], "translate": [{"y": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -13.97, "y": -2.56, "curve": "stepped"}, {"time": 0.4, "x": -13.97, "y": -2.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -49.24, "y": 1.53, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": 57.25, "y": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 39.75, "y": 1.53, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "y": -4.19}]}, "bone4": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.23, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "translate": [{"y": 0.49}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.03, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 8.98, "curve": "stepped"}, {"time": 0.6333, "angle": 8.98, "curve": 0.854, "c3": 0.75}, {"time": 0.9}], "translate": [{"x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": 0.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 5.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -1.8, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 3.35, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -3.73, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": 0.93}]}, "bone7": {"rotate": [{"angle": -1.85}]}, "bone8": {"rotate": [{"angle": -2.43}]}, "bone9": {"rotate": [{"angle": -8.03}]}, "bone10": {"rotate": [{"angle": -0.68}]}, "bone11": {"rotate": [{"angle": -13.48}]}, "bone12": {"rotate": [{"angle": -5.39}]}, "bone13": {"rotate": [{"angle": -26.5}]}, "bone14": {"rotate": [{"angle": -2.21}]}, "bone15": {"rotate": [{"angle": -9.79}]}, "bone16": {"rotate": [{"angle": -9.08}]}, "bone17": {"rotate": [{"angle": -12.9}]}, "bone18": {"rotate": [{"angle": -1.31}]}, "bone19": {"rotate": [{"angle": -14.61}]}, "bone20": {"rotate": [{"angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -167.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -161.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": -167.88, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4333, "angle": -84.18, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4667, "angle": -60.59, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 21.43, "curve": "stepped"}, {"time": 0.6333, "angle": 21.43, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 1.22}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.56, "y": -18.08, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 1.84, "y": -19.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "x": 1.56, "y": -18.08, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4667, "x": 18.38, "y": -25.78, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": 28.67, "y": 40.32, "curve": "stepped"}, {"time": 0.6333, "x": 28.67, "y": 40.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9}]}, "bone21": {"rotate": [{"angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 7.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 14.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": 7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 14.31, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 42.96, "curve": "stepped"}, {"time": 0.6333, "angle": 42.96, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 4.66}], "translate": [{"time": 0.4667, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": -17.11, "y": 2.44, "curve": "stepped"}, {"time": 0.6333, "x": -17.11, "y": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.9}]}, "bone22": {"rotate": [{"angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 4.86, "curve": "stepped"}, {"time": 0.4, "angle": 4.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -14.22, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 12.42}]}, "bone23": {"rotate": [{"angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 174.96, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 174.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": 174.96, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -37.08, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": -5.12, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 88.02, "curve": "stepped"}, {"time": 0.6333, "angle": 88.02, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": 6.78}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -14.7, "y": 5.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": -25.25, "y": 2.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "x": -14.7, "y": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 9.39, "y": -1.78, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": -35.63, "y": 4.17, "curve": "stepped"}, {"time": 0.6333, "x": -35.63, "y": 4.17, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "bone24": {"rotate": [{"angle": -1.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -0.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -13.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": -0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.87, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 2.71, "curve": "stepped"}, {"time": 0.6333, "angle": 2.71, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": -1.93}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 112.68, "curve": "stepped"}, {"time": 0.4, "angle": 112.68, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 90.17, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 110.95, "curve": "stepped"}, {"time": 0.6333, "angle": 110.95, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "bone26": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 49.75, "y": 3.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 56.96, "y": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.533, "curve": "stepped"}, {"time": 0.4, "x": 0.533, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "a22": {"rotate": [{"time": 0.1333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "angle": 91.91, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2667, "angle": 133.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -114.4, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 3.25, "y": 51.9, "curve": "stepped"}, {"time": 0.4, "x": 3.25, "y": 51.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 11.46, "y": 182.88, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": 9.67, "y": 159.05, "curve": "stepped"}, {"time": 0.6333, "x": 9.67, "y": 159.05, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "a19": {"rotate": [{"angle": -19.2}]}, "a20": {"rotate": [{"angle": -12.58}]}, "a1": {"rotate": [{"angle": 10.73}]}, "a0": {"rotate": [{"angle": 18.43}]}, "bone27": {"translate": [{"time": 0.4667, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": 4.99, "y": -16.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 2.99, "y": -10.14, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "bone29": {"translate": [{"time": 0.4667, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": 11.84, "y": -30.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 1.41, "y": -4.48, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "bone31": {"rotate": [{"angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -1.7, "curve": "stepped"}, {"time": 0.4, "angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -15.47, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 18.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 3.75}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -0.35, "y": 1.05, "curve": "stepped"}, {"time": 0.4, "x": -0.35, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -1.23, "y": 3.72, "curve": 0.25, "c3": 0}, {"time": 0.5333}]}, "bone32": {"rotate": [{"angle": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 2.43, "curve": "stepped"}, {"time": 0.4, "angle": 2.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.75, "curve": 0.25, "c3": 0}, {"time": 0.6667, "angle": 23.21, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.32}]}, "bone33": {"rotate": [{"angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -0.36, "curve": "stepped"}, {"time": 0.4, "angle": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -20.35, "curve": 0.25, "c3": 0}, {"time": 0.7333, "angle": 23.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -5.24}]}, "bone35": {"translate": [{"x": -0.92, "y": -0.03, "curve": "stepped"}, {"time": 0.4667, "x": -0.92, "y": -0.03, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": -1.67, "y": 20.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -0.92, "y": -0.03}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 2.64, "curve": "stepped"}, {"time": 0.4, "angle": 2.64, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.23, "curve": "stepped"}, {"time": 0.6333, "angle": 4.23, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.22, "curve": "stepped"}, {"time": 0.4, "angle": -5.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.86, "curve": "stepped"}, {"time": 0.6333, "angle": -8.86, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "bone47": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.22, "curve": 0.25, "c3": 0}, {"time": 0.5333}], "translate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -48.35, "curve": 0.25, "c3": 0}, {"time": 0.5333}]}, "bone39": {"rotate": [{"angle": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 1.72, "curve": "stepped"}, {"time": 0.4, "angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.64, "curve": "stepped"}, {"time": 0.6333, "angle": 3.64, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": -1.37}]}, "bone40": {"rotate": [{"angle": 3, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -3.66, "curve": "stepped"}, {"time": 0.4, "angle": -3.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.73, "curve": "stepped"}, {"time": 0.6333, "angle": -8.73, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": 3}]}, "bone41": {"translate": [{"x": -1.37, "y": -0.05}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.81, "curve": "stepped"}, {"time": 0.4, "angle": 6.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.39, "curve": "stepped"}, {"time": 0.6333, "angle": 4.39, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 8.72, "curve": "stepped"}, {"time": 0.4, "angle": 8.72, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.66, "curve": "stepped"}, {"time": 0.6333, "angle": -7.66, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "bone49": {"translate": [{"time": 0.4667, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": 54.55, "y": -0.78, "curve": "stepped"}, {"time": 0.6333, "x": 54.55, "y": -0.78, "curve": 0.854, "c3": 0.75}, {"time": 0.9}]}, "bone45": {"rotate": [{"angle": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 2.63, "curve": "stepped"}, {"time": 0.4, "angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.6, "curve": "stepped"}, {"time": 0.6333, "angle": 4.6, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": -0.57}]}, "bone46": {"rotate": [{"angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 17.25, "curve": "stepped"}, {"time": 0.4, "angle": 17.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.37, "curve": "stepped"}, {"time": 0.6333, "angle": -8.37, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": 1.68}]}, "bone51": {"rotate": [{"angle": 1.37}]}, "bone52": {"rotate": [{"angle": -15.1}]}, "bone53": {"rotate": [{"angle": -4.62}]}, "bone54": {"rotate": [{"angle": -14.51}]}, "bone55": {"rotate": [{"angle": -5.79, "curve": "stepped"}, {"time": 0.4667, "angle": -5.79, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": -26.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -5.79}]}, "bone56": {"rotate": [{"angle": 6.42}]}, "bone57": {"rotate": [{"angle": 0.49, "curve": "stepped"}, {"time": 0.4667, "angle": 0.49, "curve": 0.25, "c3": 0}, {"time": 0.5333, "angle": 18.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 28.12, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": 0.49}], "translate": [{"time": 0.4667, "curve": 0.25, "c3": 0}, {"time": 0.5333, "x": -13.18, "y": -7.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone58": {"rotate": [{"angle": 9.27}]}, "bone60": {"rotate": [{"angle": 0.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 9.68, "curve": "stepped"}, {"time": 0.4, "angle": 9.68, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.69, "curve": "stepped"}, {"time": 0.6333, "angle": 6.69, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": 0.19}]}, "bone61": {"rotate": [{"angle": -0.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 2.73, "curve": "stepped"}, {"time": 0.4, "angle": 2.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.03, "curve": "stepped"}, {"time": 0.6333, "angle": -11.03, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": -0.19}]}, "bone62": {"rotate": [{"angle": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.34, "curve": "stepped"}, {"time": 0.4, "angle": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.91, "curve": "stepped"}, {"time": 0.6333, "angle": 6.91, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": -0.52}]}, "bone63": {"rotate": [{"angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 10.42, "curve": "stepped"}, {"time": 0.4, "angle": 10.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.57, "curve": "stepped"}, {"time": 0.6333, "angle": -12.57, "curve": 0.854, "c3": 0.75}, {"time": 0.9, "angle": 1.8}]}, "daoguang": {"translate": [{"time": 0.5, "x": 132.54, "y": 185.41, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 340.82, "y": 202.76}], "scale": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.845, "y": 1.845}]}, "jineng3": {"rotate": [{"time": 0.5, "angle": 18.65}], "translate": [{"time": 0.5, "x": 55.81, "y": 214.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 188.35, "y": 251.14}], "scale": [{"time": 0.5, "x": 0.825, "y": 0.778, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 1.176, "y": 1.109}], "shear": [{"time": 0.5, "x": -45.07, "y": -22.82}]}}, "deform": {"default": {"a28": {"a14": [{"time": 0.4, "vertices": [-24.6866, -30.54393, -27.08206, -28.44167, -24.68678, -30.54412, 39.02995, 4.36197, -0.77435, -10.8084, -0.77408, -10.80832, 8.15015, 7.14094, -2.27505, -2.11425, -2.27502, -2.11423, 3.10428, -0.09621]}]}, "a15": {"a15": [{"time": 0.4}, {"time": 0.5333, "vertices": [-6.82707, 10.96209, -12.84547, 12.13728, -14.56326, 16.5154, -12.31352, 13.35647, -9.75357, 4.25661, -5.67131, 9.00481, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.92366, 0.98664, -7.62515, 9.13335, -9.37656, 8.44614, -9.52101, -0.19911], "curve": "stepped"}, {"time": 0.6333, "vertices": [-6.82707, 10.96209, -12.84547, 12.13728, -14.56326, 16.5154, -12.31352, 13.35647, -9.75357, 4.25661, -5.67131, 9.00481, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.92366, 0.98664, -7.62515, 9.13335, -9.37656, 8.44614, -9.52101, -0.19911]}, {"time": 0.9}]}}}, "drawOrder": [{"time": 0.0333, "offsets": [{"slot": "a17", "offset": 5}, {"slot": "a16", "offset": 5}, {"slot": "a15", "offset": 5}, {"slot": "a14", "offset": -13}, {"slot": "a28", "offset": -13}, {"slot": "a8", "offset": -13}, {"slot": "a5", "offset": -13}, {"slot": "a4", "offset": -13}, {"slot": "a2", "offset": -13}, {"slot": "a3", "offset": -13}, {"slot": "a1", "offset": -13}, {"slot": "a0", "offset": -13}]}, {"time": 0.7333, "offsets": [{"slot": "a18", "offset": 4}, {"slot": "a7", "offset": 4}, {"slot": "a6", "offset": 4}, {"slot": "a22", "offset": 5}, {"slot": "a14", "offset": -4}, {"slot": "a8", "offset": -4}]}, {"time": 0.8667, "offsets": [{"slot": "a18", "offset": 4}, {"slot": "a7", "offset": 4}, {"slot": "a6", "offset": 4}, {"slot": "a22", "offset": 3}, {"slot": "a28", "offset": 1}]}], "events": [{"time": 0.5333, "name": "atk"}]}, "boss_idle": {"bones": {"bone2": {"translate": [{"y": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -4.19}]}, "bone4": {"translate": [{"y": 0.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.03, "y": 2.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "y": 0.49}]}, "bone5": {"translate": [{"x": 3.47, "y": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 8.52, "y": 1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": 0.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 0.93}]}, "bone7": {"rotate": [{"angle": -1.85, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": -14.19, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.9667, "angle": -4.32, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -1.85}]}, "bone8": {"rotate": [{"angle": -2.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -29.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.9667, "angle": -22.21, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 1.3333, "angle": -2.43}]}, "bone9": {"rotate": [{"angle": -8.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -16.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -8.03}]}, "bone10": {"rotate": [{"angle": -0.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -16.97, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.1667, "angle": -5.17, "curve": 0.362, "c2": 0.45, "c3": 0.706, "c4": 0.82}, {"time": 1.3333, "angle": -0.68}]}, "bone11": {"rotate": [{"angle": -13.48, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -30.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.1667, "angle": -23.41, "curve": 0.32, "c2": 0.29, "c3": 0.663, "c4": 0.66}, {"time": 1.3333, "angle": -13.48}]}, "bone12": {"rotate": [{"angle": -5.39, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -17.68, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -5.39}]}, "bone13": {"rotate": [{"angle": -26.5, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -34.97, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -26.5}]}, "bone14": {"rotate": [{"angle": -2.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -17.78, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -2.21}]}, "bone15": {"rotate": [{"angle": -9.79, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -14.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -9.79}]}, "bone16": {"rotate": [{"angle": -9.08, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": -1.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -9.08}]}, "bone17": {"rotate": [{"angle": -12.9, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": -7.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": 10.31, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": -12.9}]}, "bone18": {"rotate": [{"angle": -1.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -12.84, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -1.31}]}, "bone19": {"rotate": [{"angle": -14.61, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": 6.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -21.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -14.61}]}, "bone20": {"rotate": [{"angle": 1.22, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 5.04, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 1.22}]}, "bone21": {"rotate": [{"angle": 4.66, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 7.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 4.66}]}, "bone22": {"rotate": [{"angle": 12.42, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 12.97, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": 12.42}]}, "bone23": {"rotate": [{"angle": 6.78, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 7.52, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 4.47, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 6.78}]}, "bone24": {"rotate": [{"angle": -1.93, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -3.05, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -1.93}]}, "a19": {"rotate": [{"angle": -19.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.4667, "angle": 1.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667, "angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -19.2}]}, "a20": {"rotate": [{"angle": -12.58, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -19.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4667, "angle": -9.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "angle": 8.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -12.58}]}, "a1": {"rotate": [{"angle": 10.73, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": -1.97, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5333, "angle": -8.81, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 13.66, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 10.73}]}, "a0": {"rotate": [{"angle": 18.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": 20.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3, "angle": 13.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7667, "angle": -8.81, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": 18.43}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.93, "y": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone29": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 5.45, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone31": {"rotate": [{"angle": 3.75, "curve": 0.291, "c2": 0.19, "c3": 0.663, "c4": 0.65}, {"time": 0.3333, "angle": -0.74, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5667, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 4.35, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "angle": 3.75}]}, "bone32": {"rotate": [{"angle": 2.32, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "angle": 8.21, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.3333, "angle": 8.05, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.5, "angle": 4.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9667, "angle": -5.35, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "angle": 2.32}]}, "bone33": {"rotate": [{"angle": -5.24, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.3333, "angle": 5.44, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.5, "angle": 9.24, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.5333, "angle": 9.53, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -7.47, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -5.24}]}, "bone35": {"translate": [{"x": -0.92, "y": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.79, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.92, "y": -0.03}]}, "bone39": {"rotate": [{"angle": -1.37}]}, "bone40": {"rotate": [{"angle": 3}]}, "bone41": {"translate": [{"x": -1.37, "y": -0.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -0.5, "y": -0.02, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.37, "y": -0.05}]}, "bone45": {"rotate": [{"angle": -0.57}]}, "bone46": {"rotate": [{"angle": 1.68}]}, "bone51": {"rotate": [{"angle": 1.37, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 12.32, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.8333, "angle": 0.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.0667, "angle": -5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 1.37}]}, "bone52": {"rotate": [{"angle": -15.1, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 23.65, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.8333, "angle": 14.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.3, "angle": -15.77, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -15.1}]}, "bone53": {"rotate": [{"angle": -4.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": 5.71, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.9333, "angle": -3.1, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.1667, "angle": -6.96, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -4.62}]}, "bone54": {"rotate": [{"angle": -14.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": -15.67, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 11.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.9333, "angle": 5.19, "curve": 0.333, "c2": 0.33, "c3": 0.723, "c4": 0.86}, {"time": 1.3333, "angle": -14.51}]}, "bone55": {"rotate": [{"angle": -5.79, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -10.31, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 4.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -5.79}]}, "bone56": {"rotate": [{"angle": 6.42, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -23.62, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 16.01, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 6.42}]}, "bone57": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 7.27, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 0.49}]}, "bone58": {"rotate": [{"angle": 9.27, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 17.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 9.27}]}, "bone60": {"rotate": [{"angle": 0.19}]}, "bone61": {"rotate": [{"angle": -0.19}]}, "bone62": {"rotate": [{"angle": -0.52}]}, "bone63": {"rotate": [{"angle": 1.8}]}}}, "die": {"slots": {"a3": {"color": [{"time": 0.0667, "color": "ffffffff"}]}}, "bones": {"bone2": {"rotate": [{"time": 0.0667, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 97.83}], "translate": [{"y": -4.19, "curve": "stepped"}, {"time": 0.0667, "y": -4.19, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "x": 54.21, "y": -55.21}]}, "bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "y": 49.23, "curve": 0.908, "c3": 0.75}, {"time": 0.1667, "x": 1.79, "y": 36.4, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.3333, "curve": 0.25, "c3": 0.218}, {"time": 0.4333, "x": 20.72, "y": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -3.89, "curve": 0.784, "c3": 0.75}, {"time": 0.3333}], "translate": [{"y": 0.49}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -14.66, "curve": 0.533, "c3": 0.816, "c4": 0.48}, {"time": 0.2, "angle": -17.36, "curve": 0.435, "c2": 0.35, "c3": 0.716, "c4": 0.73}, {"time": 0.2667, "angle": -24.99, "curve": 0.292, "c2": 0.6, "c3": 0.587}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 6.33}], "translate": [{"x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": 0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0333, "angle": 5.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -15.9, "curve": 0.292, "c2": 0.6, "c3": 0.587}, {"time": 0.3333, "angle": 0.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.33}]}, "bone7": {"rotate": [{"angle": -1.85}]}, "bone8": {"rotate": [{"angle": -2.43}]}, "bone9": {"rotate": [{"angle": -8.03}]}, "bone10": {"rotate": [{"angle": -0.68}]}, "bone11": {"rotate": [{"angle": -13.48}]}, "bone12": {"rotate": [{"angle": -5.39}]}, "bone13": {"rotate": [{"angle": -26.5}]}, "bone14": {"rotate": [{"angle": -2.21}]}, "bone15": {"rotate": [{"angle": -9.79}]}, "bone16": {"rotate": [{"angle": -9.08}]}, "bone17": {"rotate": [{"angle": -12.9}]}, "bone18": {"rotate": [{"angle": -1.31}]}, "bone19": {"rotate": [{"angle": -14.61}]}, "bone20": {"rotate": [{"angle": 1.22, "curve": "stepped"}, {"time": 0.3333, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -52.03}]}, "bone21": {"rotate": [{"angle": 4.66, "curve": "stepped"}, {"time": 0.3333, "angle": 4.66, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 27.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6, "angle": 4.66}]}, "bone22": {"rotate": [{"angle": 12.42, "curve": "stepped"}, {"time": 0.3333, "angle": 12.42, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 31.24, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6, "angle": 12.42}]}, "bone23": {"rotate": [{"angle": 6.78, "curve": "stepped"}, {"time": 0.0667, "angle": 6.78, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 23.48, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "angle": 17.37, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.6, "angle": 20.27}], "translate": [{"time": 0.3333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "x": 13.22, "y": -21.57, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.6, "x": 24.6, "y": -1.25}]}, "bone24": {"rotate": [{"angle": -1.93, "curve": "stepped"}, {"time": 0.0667, "angle": -1.93, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 6.3, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "angle": 0.19, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.6, "angle": 3.1}]}, "bone25": {"rotate": [{"time": 0.0667, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 114.48, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "angle": 108.37, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.6, "angle": 111.27}]}, "a19": {"rotate": [{"angle": -19.2}]}, "a20": {"rotate": [{"angle": -12.58}]}, "a1": {"rotate": [{"angle": 10.73}]}, "a0": {"rotate": [{"angle": 18.43}]}, "bone27": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4667, "x": 6.71, "y": -25.46}]}, "bone29": {"translate": [{"time": 0.3333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "x": 4.51, "y": -32.6, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.6, "x": -1.13, "y": -22.35}]}, "bone31": {"rotate": [{"angle": 3.75, "curve": "stepped"}, {"time": 0.0667, "angle": 3.75, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 94.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 84.4}]}, "bone32": {"rotate": [{"angle": 2.32, "curve": "stepped"}, {"time": 0.3333, "angle": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.32}]}, "bone33": {"rotate": [{"angle": -5.24, "curve": "stepped"}, {"time": 0.3333, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.24}]}, "bone35": {"translate": [{"x": -0.92, "y": -0.03}]}, "bone36": {"rotate": [{"angle": 4.23, "curve": "stepped"}, {"time": 0.0667, "angle": 4.23, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": -156.41}]}, "bone37": {"rotate": [{"angle": -8.86, "curve": "stepped"}, {"time": 0.0667, "angle": -8.86, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 45.06}]}, "bone48": {"rotate": [{"time": 0.0667, "curve": 0.628, "c3": 0.846, "c4": 0.57}, {"time": 0.2333, "angle": 38.49, "curve": 0.328, "c2": 0.56, "c3": 0.573}, {"time": 0.3333, "angle": 103.61}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 0.5, "y": 47.81, "curve": 0.628, "c3": 0.846, "c4": 0.57}, {"time": 0.2333, "x": 105.9, "y": 8.12, "curve": 0.328, "c2": 0.56, "c3": 0.573}, {"time": 0.3333, "x": 212.19, "y": 71.6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4333, "x": 212.88, "y": 56.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 212.95, "y": 63.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 212.88, "y": 56.28}]}, "bone39": {"rotate": [{"angle": 3.64, "curve": "stepped"}, {"time": 0.0667, "angle": 3.64, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": -158.96}]}, "bone40": {"rotate": [{"angle": -8.73, "curve": "stepped"}, {"time": 0.0667, "angle": -8.73, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 39.6}]}, "bone41": {"translate": [{"x": -1.37, "y": -0.05}]}, "bone42": {"rotate": [{"angle": 4.39, "curve": "stepped"}, {"time": 0.0667, "angle": 4.39, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": -51.84}]}, "bone43": {"rotate": [{"angle": -7.66, "curve": "stepped"}, {"time": 0.0667, "angle": -7.66, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 19.85}]}, "bone49": {"rotate": [{"time": 0.0667, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 80.94}], "translate": [{"time": 0.0667, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "x": 51.59, "y": 90.03}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -24.72, "curve": 0.628, "c3": 0.846, "c4": 0.57}, {"time": 0.2333, "angle": 36.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 17.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -7.48, "y": 51.09, "curve": 0.628, "c3": 0.846, "c4": 0.57}, {"time": 0.2333, "x": 34.86, "y": 1.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 8.47, "y": -53.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -7.45, "y": -56.33}]}, "bone45": {"rotate": [{"angle": 4.6, "curve": "stepped"}, {"time": 0.0667, "angle": 4.6, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": -50.89}]}, "bone46": {"rotate": [{"angle": -8.37, "curve": "stepped"}, {"time": 0.0667, "angle": -8.37, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": 14.58}]}, "bone51": {"rotate": [{"angle": 1.37}]}, "bone52": {"rotate": [{"angle": -15.1}]}, "bone53": {"rotate": [{"angle": -4.62}]}, "bone54": {"rotate": [{"angle": -14.51}]}, "bone55": {"rotate": [{"angle": -5.79}]}, "bone56": {"rotate": [{"angle": 6.42}]}, "bone57": {"rotate": [{"angle": 0.49}]}, "bone58": {"rotate": [{"angle": 9.27}]}, "bone60": {"rotate": [{"angle": 6.69, "curve": "stepped"}, {"time": 0.0667, "angle": 6.69, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": -147.99}]}, "bone61": {"rotate": [{"angle": -11.03, "curve": "stepped"}, {"time": 0.0667, "angle": -11.03, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": -13.56}]}, "bone62": {"rotate": [{"angle": 6.91, "curve": "stepped"}, {"time": 0.0667, "angle": 6.91, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": -139.09}]}, "bone63": {"rotate": [{"angle": -12.57, "curve": "stepped"}, {"time": 0.0667, "angle": -12.57, "curve": 0.784, "c3": 0.75}, {"time": 0.3333, "angle": -39.41}]}}}, "hurt": {"slots": {"a3": {"color": [{"time": 0.0667, "color": "ffffffff"}]}, "a2": {"attachment": [{"time": 0.0667, "name": null}]}}, "bones": {"bone2": {"translate": [{"x": 14.59, "y": -0.55}]}, "bone4": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 16.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"y": 0.49}]}, "bone5": {"rotate": [{"angle": 6.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 16.43, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 6.04}], "translate": [{"x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": 15.23, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 0.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.36, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 15.23}]}, "bone7": {"rotate": [{"angle": -1.85}]}, "bone8": {"rotate": [{"angle": -2.43}]}, "bone9": {"rotate": [{"angle": -8.03}]}, "bone10": {"rotate": [{"angle": -0.68}]}, "bone11": {"rotate": [{"angle": -13.48}]}, "bone12": {"rotate": [{"angle": -5.39}]}, "bone13": {"rotate": [{"angle": -26.5}]}, "bone14": {"rotate": [{"angle": -2.21}]}, "bone15": {"rotate": [{"angle": -9.79}]}, "bone16": {"rotate": [{"angle": -9.08}]}, "bone17": {"rotate": [{"angle": -12.9}]}, "bone18": {"rotate": [{"angle": -1.31}]}, "bone19": {"rotate": [{"angle": -14.61}]}, "bone20": {"rotate": [{"angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 25.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.22}]}, "bone21": {"rotate": [{"angle": 13.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 28.71, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 13.5}]}, "bone22": {"rotate": [{"angle": 36.47, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 36.47}]}, "bone23": {"rotate": [{"angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 37.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.78}]}, "bone24": {"rotate": [{"angle": 9.48, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -1.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 29.07, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 9.48}]}, "a19": {"rotate": [{"angle": -35.92, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -64.64, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -35.92}]}, "a20": {"rotate": [{"angle": -12.58}]}, "a1": {"rotate": [{"angle": 10.73}]}, "a0": {"rotate": [{"angle": 18.43}]}, "bone31": {"rotate": [{"angle": 3.75}]}, "bone32": {"rotate": [{"angle": 2.32}], "translate": [{"x": -8.35, "y": -5.7}]}, "bone33": {"rotate": [{"angle": -28.19, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -31.62, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -28.19}]}, "bone35": {"translate": [{"x": -0.92, "y": -0.03}]}, "bone36": {"rotate": [{"angle": -23.59}]}, "bone37": {"rotate": [{"angle": 34.59}]}, "bone39": {"rotate": [{"angle": -28.77}]}, "bone40": {"rotate": [{"angle": 39.6}]}, "bone41": {"translate": [{"x": -1.37, "y": -0.05}]}, "bone42": {"rotate": [{"angle": -11.95}]}, "bone43": {"rotate": [{"angle": 1.25}]}, "bone45": {"rotate": [{"angle": -10.44}]}, "bone46": {"rotate": [{"angle": -1.84}]}, "bone51": {"rotate": [{"angle": 1.37}]}, "bone52": {"rotate": [{"angle": -15.1}]}, "bone53": {"rotate": [{"angle": -4.62}]}, "bone54": {"rotate": [{"angle": -14.51}]}, "bone55": {"rotate": [{"angle": -5.79}]}, "bone56": {"rotate": [{"angle": 6.42}]}, "bone57": {"rotate": [{"angle": 0.49}]}, "bone58": {"rotate": [{"angle": 9.27}]}, "bone60": {"rotate": [{"angle": -9.84}]}, "bone61": {"rotate": [{"angle": -2.76}]}, "bone62": {"rotate": [{"angle": -8.02}]}, "bone63": {"rotate": [{"angle": -6.77}]}, "bone3": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 13.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone25": {"rotate": [{"angle": 14.81, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 113.84, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 14.81}]}}}, "run1": {"slots": {"a26": {"color": [{"color": "ffffffff"}]}, "a10": {"color": [{"color": "ffffff00"}]}, "a11": {"color": [{"color": "ffffff00"}]}, "a27": {"color": [{"color": "ffffffff"}]}}, "bones": {"bone60": {"rotate": [{"angle": -28}]}, "bone61": {"rotate": [{"angle": 7.94}]}, "bone64": {"translate": [{"x": -68.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 78.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -68.92}]}, "bone": {"translate": [{"x": -4.6}]}, "bone2": {"translate": [{"x": 14.59, "y": 1.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 14.59, "y": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 14.59, "y": 1.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 14.59, "y": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 14.59, "y": 1.63}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -1.66, "y": -19.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone29": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 9.7, "y": -27.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone39": {"rotate": [{"angle": -1.37}]}, "bone40": {"rotate": [{"angle": 3}]}, "bone49": {"translate": [{"x": 34.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -113, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 34.64}]}, "bone50": {"rotate": [{"angle": 59.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": 10.74, "curve": "stepped"}, {"time": 0.1333, "angle": 10.74, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": -96.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -101.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "angle": 59.24}], "translate": [{"x": -0.41, "y": 28.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": -0.02, "y": 1.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "x": -25.09, "y": 1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "x": -0.68, "y": 47.73, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": -11.26, "y": 37.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": -0.41, "y": 28.98}]}, "bone44": {"rotate": [{"angle": 0.77}]}, "bone45": {"rotate": [{"angle": -0.57}]}, "bone46": {"rotate": [{"angle": 1.68}]}, "bone59": {"translate": [{"curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.2333, "x": -0.82, "y": 42.43, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2667, "x": -1.58, "y": 43.73, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone62": {"rotate": [{"angle": -25.14}]}, "bone63": {"rotate": [{"angle": 1.36}]}, "bone65": {"rotate": [{"angle": -75.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -97.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 36.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 5.28, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4, "angle": 6.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "angle": -75.9}], "translate": [{"x": -0.64, "y": 44.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": -14.12, "y": 29.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": -0.4, "y": 27.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": -0.06, "y": 3.91, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4, "x": -0.02, "y": 1.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": -0.64, "y": 44.54}]}, "bone4": {"translate": [{"y": 0.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -0.03, "y": 2.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "y": 0.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.03, "y": 2.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "y": 0.49}]}, "bone23": {"rotate": [{"angle": 15.54, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": 16.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 13.23, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": 15.54, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 16.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 13.23, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": 15.54}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 4.91, "y": -13.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone24": {"rotate": [{"angle": -9.56, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.0667, "angle": -7.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.68, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": -9.56, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": -7.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -10.68, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": -9.56}]}, "bone5": {"rotate": [{"angle": -8.48}], "translate": [{"x": 3.47, "y": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 8.52, "y": 1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 3.47, "y": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 8.52, "y": 1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": 0.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 0.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 1.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 0.93}]}, "bone25": {"rotate": [{"angle": -9.97}]}, "bone20": {"rotate": [{"angle": 14.32, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 19.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.39, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 14.32}], "translate": [{"x": 37.04, "y": 21.54, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "x": 35.87, "y": 21.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 42.2, "y": 23.39, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "x": 37.04, "y": 21.54}]}, "bone21": {"rotate": [{"angle": 15.61, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.0667, "angle": 19.75, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": 24, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.2667, "angle": 18.73, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.4333, "angle": 11.35, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5333, "angle": 15.61}], "translate": [{"x": -7.82, "y": 3.05}]}, "bone22": {"rotate": [{"angle": 3.01, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": 9.26, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 31.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.01}]}, "bone7": {"rotate": [{"angle": -1.85, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "angle": -14.19, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4, "angle": -4.32, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -1.85}], "translate": [{"curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1333, "x": -0.31, "y": 0.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone14": {"rotate": [{"angle": -2.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -17.78, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": -2.21}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "a0": {"rotate": [{"angle": -8.81, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.2333, "angle": 18.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2667, "angle": 20.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3667, "angle": 13.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5333, "angle": -8.81}], "translate": [{"x": -0.18, "y": 0.3, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667, "x": -0.54, "y": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "x": -0.18, "y": 0.3}]}, "bone15": {"rotate": [{"angle": -9.79, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -14.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": -9.79}]}, "bone55": {"rotate": [{"angle": -5.79, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -10.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 4.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": -5.79}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 0.04, "y": -1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone31": {"rotate": [{"angle": 3.75, "curve": 0.291, "c2": 0.19, "c3": 0.663, "c4": 0.65}, {"time": 0.1333, "angle": -0.74, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.35, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.5333, "angle": 3.75}], "translate": [{"curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": -0.35, "y": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "a19": {"rotate": [{"angle": -19.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2, "angle": 1.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -19.2}], "translate": [{"curve": 0.324, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.1333, "x": -0.54, "y": 0.88, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2}]}, "bone56": {"rotate": [{"angle": 6.42, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": -23.62, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 16.01, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": 6.42}]}, "bone33": {"rotate": [{"angle": -5.24, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.1333, "angle": 5.44, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.2, "angle": 9.53, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.47, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -5.24}]}, "bone51": {"rotate": [{"angle": 1.37, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": 12.32, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": 0.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4333, "angle": -5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": 1.37}], "translate": [{"time": 0.0667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "x": -0.21, "y": 1.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3333}]}, "a1": {"rotate": [{"angle": -1.27, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.1667, "angle": 13.66, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2333, "angle": 10.73, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.3667, "angle": -1.97, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4333, "angle": -8.81, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5333, "angle": -1.27}], "translate": [{"x": -0.18, "y": 0.3, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667, "x": -0.54, "y": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "x": -0.18, "y": 0.3}]}, "bone13": {"rotate": [{"angle": -26.5, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -34.97, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": -26.5}]}, "bone19": {"rotate": [{"angle": -14.61, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": 6.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -21.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": -14.61}]}, "bone52": {"rotate": [{"angle": -15.1, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.2667, "angle": 23.65, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 14.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5333, "angle": -15.1}]}, "bone10": {"rotate": [{"angle": -0.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -16.97, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4667, "angle": -5.17, "curve": 0.362, "c2": 0.45, "c3": 0.706, "c4": 0.82}, {"time": 0.5333, "angle": -0.68}], "translate": [{"curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.1333, "x": -0.31, "y": 0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2}]}, "bone58": {"rotate": [{"angle": 9.27, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 17.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": 9.27}]}, "bone57": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 7.27, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": 0.49}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 0.04, "y": -1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone16": {"rotate": [{"angle": -9.08, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": -1.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": -9.08}], "translate": [{"time": 0.1, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.1333, "x": -0.31, "y": 0.99, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.3667}]}, "bone53": {"rotate": [{"angle": -4.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": 5.71, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3667, "angle": -3.1, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": -6.96, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": -4.62}], "translate": [{"time": 0.1, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.1333, "x": -0.21, "y": 1.01, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.3667}]}, "bone17": {"rotate": [{"angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": -7.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": 10.31, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5333, "angle": -12.9}]}, "bone9": {"rotate": [{"angle": -8.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -8.03}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone54": {"rotate": [{"angle": -14.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": -15.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3667, "angle": 5.19, "curve": 0.333, "c2": 0.33, "c3": 0.723, "c4": 0.86}, {"time": 0.5333, "angle": -14.51}]}, "bone12": {"rotate": [{"angle": -5.39, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -17.68, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": -5.39}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone8": {"rotate": [{"angle": -2.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -29.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4, "angle": -22.21, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.5333, "angle": -2.43}]}, "bone32": {"rotate": [{"angle": 2.32, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1333, "angle": 8.05, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.2, "angle": 4.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": -5.35, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.5333, "angle": 2.32}]}, "a20": {"rotate": [{"angle": -12.58, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -19.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2, "angle": -9.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": 8.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": -12.58}], "translate": [{"curve": 0.324, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.1333, "x": -0.54, "y": 0.88, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2}]}, "bone18": {"rotate": [{"angle": -1.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -12.84, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": -1.31}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone11": {"rotate": [{"angle": -13.48, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -30.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4667, "angle": -23.41, "curve": 0.32, "c2": 0.29, "c3": 0.663, "c4": 0.66}, {"time": 0.5333, "angle": -13.48}]}}}, "run1_1": {"bones": {"bone2": {"translate": [{"x": 14.59, "y": -0.55}]}, "bone4": {"translate": [{"y": 0.49}]}, "bone5": {"translate": [{"x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": 0.93}]}, "bone7": {"rotate": [{"angle": -1.85}]}, "bone8": {"rotate": [{"angle": -2.43}]}, "bone9": {"rotate": [{"angle": -8.03}]}, "bone10": {"rotate": [{"angle": -0.68}]}, "bone11": {"rotate": [{"angle": -13.48}]}, "bone12": {"rotate": [{"angle": -5.39}]}, "bone13": {"rotate": [{"angle": -26.5}]}, "bone14": {"rotate": [{"angle": -2.21}]}, "bone15": {"rotate": [{"angle": -9.79}]}, "bone16": {"rotate": [{"angle": -9.08}]}, "bone17": {"rotate": [{"angle": -12.9}]}, "bone18": {"rotate": [{"angle": -1.31}]}, "bone19": {"rotate": [{"angle": -14.61}]}, "bone20": {"rotate": [{"angle": 1.22}]}, "bone21": {"rotate": [{"angle": 4.66}]}, "bone22": {"rotate": [{"angle": 12.42}]}, "bone23": {"rotate": [{"angle": 6.78}]}, "bone24": {"rotate": [{"angle": -1.93}]}, "a19": {"rotate": [{"angle": -19.2}]}, "a20": {"rotate": [{"angle": -12.58}]}, "a1": {"rotate": [{"angle": 10.73}]}, "a0": {"rotate": [{"angle": 18.43}]}, "bone31": {"rotate": [{"angle": 3.75}]}, "bone32": {"rotate": [{"angle": 2.32}]}, "bone33": {"rotate": [{"angle": -5.24}]}, "bone35": {"translate": [{"x": -0.92, "y": -0.03}]}, "bone36": {"rotate": [{"angle": 4.23}]}, "bone37": {"rotate": [{"angle": -8.86}]}, "bone39": {"rotate": [{"angle": 3.64}]}, "bone40": {"rotate": [{"angle": -8.73}]}, "bone41": {"translate": [{"x": -1.37, "y": -0.05}]}, "bone42": {"rotate": [{"angle": 4.39}]}, "bone43": {"rotate": [{"angle": -7.66}]}, "bone45": {"rotate": [{"angle": 4.6}]}, "bone46": {"rotate": [{"angle": -8.37}]}, "bone51": {"rotate": [{"angle": 1.37}]}, "bone52": {"rotate": [{"angle": -15.1}]}, "bone53": {"rotate": [{"angle": -4.62}]}, "bone54": {"rotate": [{"angle": -14.51}]}, "bone55": {"rotate": [{"angle": -5.79}]}, "bone56": {"rotate": [{"angle": 6.42}]}, "bone57": {"rotate": [{"angle": 0.49}]}, "bone58": {"rotate": [{"angle": 9.27}]}, "bone60": {"rotate": [{"angle": 6.69}]}, "bone61": {"rotate": [{"angle": -11.03}]}, "bone62": {"rotate": [{"angle": 6.91}]}, "bone63": {"rotate": [{"angle": -12.57}]}}}, "run2": {"slots": {"tx/1_00006": {"attachment": [{"time": 0.0333, "name": "tx/1_00006"}, {"time": 0.0667, "name": "tx/1_00009"}, {"time": 0.1, "name": "tx/1_00010"}, {"time": 0.1333, "name": "tx/1_00012"}, {"time": 0.1667, "name": "tx/1_00014"}, {"time": 0.2, "name": null}]}, "a28": {"color": [{"color": "ffffffff"}]}, "tx/1_6": {"attachment": [{"time": 0.1333, "name": "tx/1_00006"}, {"time": 0.1667, "name": "tx/1_00009"}, {"time": 0.2, "name": "tx/1_00010"}, {"time": 0.2333, "name": "tx/1_00012"}, {"time": 0.2667, "name": "tx/1_00014"}, {"time": 0.3, "name": null}]}, "tx/1_00007": {"attachment": [{"name": "tx/1_00007"}, {"time": 0.0333, "name": "tx/1_00008"}, {"time": 0.0667, "name": null}]}, "tx/shuiboquan_00000": {"attachment": [{"name": "tx/shuiboquan_00000"}, {"time": 0.0333, "name": "tx/shuiboquan_00002"}, {"time": 0.0667, "name": "tx/shuiboquan_00004"}, {"time": 0.1, "name": "tx/shuiboquan_00006"}, {"time": 0.1333, "name": "tx/shuiboquan_00008"}, {"time": 0.1667, "name": "tx/shuiboquan_00010"}, {"time": 0.2, "name": "tx/shuiboquan_00012"}, {"time": 0.2333, "name": "tx/shuiboquan_00014"}, {"time": 0.2667, "name": "tx/shuiboquan_00016"}, {"time": 0.3, "name": "tx/shuiboquan_00018"}]}, "tx/1_7": {"attachment": [{"time": 0.1, "name": "tx/1_00007"}, {"time": 0.1333, "name": "tx/1_00008"}, {"time": 0.1667, "name": null}]}, "a26": {"color": [{"color": "ffffffff"}]}, "a10": {"color": [{"color": "ffffff00"}]}, "a11": {"color": [{"color": "ffffff00"}]}, "a27": {"color": [{"color": "ffffffff"}]}}, "bones": {"bone60": {"rotate": [{"angle": -28}]}, "bone61": {"rotate": [{"angle": 7.94}]}, "bone64": {"translate": [{"x": -68.92, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 78.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -68.92}]}, "bone": {"translate": [{"x": -4.6}]}, "bone2": {"translate": [{"x": 14.59, "y": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 14.59, "y": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 14.59, "y": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 14.59, "y": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 14.59, "y": -0.55}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -1.66, "y": -19.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone29": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 9.7, "y": -27.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone39": {"rotate": [{"angle": -1.37}]}, "bone40": {"rotate": [{"angle": 3}]}, "bone49": {"translate": [{"x": 34.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -113, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 34.64}]}, "bone50": {"rotate": [{"angle": 59.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": 10.74, "curve": "stepped"}, {"time": 0.0667, "angle": 10.74, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": -96.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -101.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 59.24}], "translate": [{"x": -0.41, "y": 28.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "x": -0.02, "y": 1.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "x": -25.09, "y": 1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "x": -0.68, "y": 47.73, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -11.26, "y": 37.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -0.41, "y": 28.98}]}, "bone44": {"rotate": [{"angle": 0.77}]}, "bone45": {"rotate": [{"angle": -0.57}]}, "bone46": {"rotate": [{"angle": 1.68}]}, "bone59": {"translate": [{"curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.1333, "x": -0.82, "y": 42.43, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": -1.58, "y": 43.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone62": {"rotate": [{"angle": -25.14}]}, "bone63": {"rotate": [{"angle": 1.36}]}, "bone65": {"rotate": [{"angle": -75.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": -97.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 36.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "angle": 5.28, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2333, "angle": 6.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -75.9}], "translate": [{"x": -0.64, "y": 44.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -14.12, "y": 29.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "x": -0.4, "y": 27.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "x": -0.06, "y": 3.91, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2333, "x": -0.02, "y": 1.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -0.64, "y": 44.54}]}, "bone4": {"rotate": [{"angle": -4.85}], "translate": [{"y": 0.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.03, "y": 2.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "y": 0.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.03, "y": 2.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "y": 0.49}]}, "bone23": {"rotate": [{"angle": -301.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -300.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -303.82, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": -301.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -300.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -303.82, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -301.51}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 4.91, "y": -13.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone24": {"rotate": [{"angle": 12.44, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.0333, "angle": 14.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 11.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1667, "angle": 12.44, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 14.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 12.44}]}, "bone5": {"rotate": [{"angle": -8.57}], "translate": [{"x": 3.47, "y": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 8.52, "y": 1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": 3.47, "y": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 8.52, "y": 1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": -3.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -2.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -3.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -2.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -3.53}]}, "bone25": {"rotate": [{"angle": 83.24}]}, "bone7": {"rotate": [{"angle": -1.85, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -14.19, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2333, "angle": -4.32, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -1.85}], "translate": [{"curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone14": {"rotate": [{"angle": -2.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.78, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -2.21}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "a0": {"rotate": [{"angle": -8.81, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.1333, "angle": 18.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1667, "angle": 20.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": 13.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -8.81}], "translate": [{"x": -0.18, "y": 0.3, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2333, "x": -0.54, "y": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3333, "x": -0.18, "y": 0.3}]}, "bone15": {"rotate": [{"angle": -9.79, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -9.79}]}, "bone55": {"rotate": [{"angle": -5.79, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -10.31, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -5.79}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 0.04, "y": -1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone31": {"rotate": [{"angle": 17.02, "curve": 0.291, "c2": 0.19, "c3": 0.663, "c4": 0.65}, {"time": 0.0667, "angle": 12.53, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 10.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.62, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.3333, "angle": 17.02}], "translate": [{"curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "x": -0.35, "y": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "a19": {"rotate": [{"angle": -19.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1333, "angle": 1.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1667, "angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.2}], "translate": [{"curve": 0.324, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.0667, "x": -0.54, "y": 0.88, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.1333}]}, "bone56": {"rotate": [{"angle": 6.42, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -23.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 16.01, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 6.42}]}, "bone33": {"rotate": [{"angle": -5.24, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.0667, "angle": 5.44, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1333, "angle": 9.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.47, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -5.24}]}, "bone51": {"rotate": [{"angle": 1.37, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 12.32, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2, "angle": 0.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": -5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 1.37}], "translate": [{"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": -0.21, "y": 1.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2}]}, "a1": {"rotate": [{"angle": -1.27, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.1, "angle": 13.66, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 10.73, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": -1.97, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": -8.81, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.3333, "angle": -1.27}], "translate": [{"x": -0.18, "y": 0.3, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2333, "x": -0.54, "y": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3333, "x": -0.18, "y": 0.3}]}, "bone13": {"rotate": [{"angle": -26.5, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -34.97, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -26.5}]}, "bone19": {"rotate": [{"angle": -14.61, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 6.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -21.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -14.61}]}, "bone52": {"rotate": [{"angle": -15.1, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.1667, "angle": 23.65, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2, "angle": 14.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -15.1}]}, "bone10": {"rotate": [{"angle": -0.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -16.97, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3, "angle": -5.17, "curve": 0.362, "c2": 0.45, "c3": 0.706, "c4": 0.82}, {"time": 0.3333, "angle": -0.68}], "translate": [{"curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333}]}, "bone58": {"rotate": [{"angle": 9.27, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 9.27}]}, "bone57": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.27, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": 0.49}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 0.04, "y": -1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone16": {"rotate": [{"angle": -9.08, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0667, "angle": -1.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -9.08}], "translate": [{"curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.2333}]}, "bone53": {"rotate": [{"angle": -4.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 5.71, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2333, "angle": -3.1, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": -6.96, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -4.62}], "translate": [{"curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "x": -0.21, "y": 1.01, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.2333}]}, "bone17": {"rotate": [{"angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": -7.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 10.31, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "angle": -12.9}]}, "bone9": {"rotate": [{"angle": -8.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -8.03}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone54": {"rotate": [{"angle": -14.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": -15.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": 5.19, "curve": 0.333, "c2": 0.33, "c3": 0.723, "c4": 0.86}, {"time": 0.3333, "angle": -14.51}]}, "bone12": {"rotate": [{"angle": -5.39, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.68, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -5.39}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone8": {"rotate": [{"angle": -2.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -29.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": -22.21, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.3333, "angle": -2.43}]}, "bone32": {"rotate": [{"angle": 2.32, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.0667, "angle": 8.05, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1333, "angle": 4.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -5.35, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.3333, "angle": 2.32}]}, "a20": {"rotate": [{"angle": -12.58, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -19.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "angle": -9.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 8.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -12.58}], "translate": [{"curve": 0.324, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.0667, "x": -0.54, "y": 0.88, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.1333}]}, "bone18": {"rotate": [{"angle": -1.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.84, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -1.31}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone11": {"rotate": [{"angle": -13.48, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -30.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3, "angle": -23.41, "curve": 0.32, "c2": 0.29, "c3": 0.663, "c4": 0.66}, {"time": 0.3333, "angle": -13.48}]}, "a22": {"rotate": [{"angle": 28.55}], "translate": [{"x": -29.06, "y": 181.52}], "shear": [{"y": -33.32}]}, "bone20": {"rotate": [{"angle": 27.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": 25.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 27.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": 27.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 25.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 27.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": 27.08}], "translate": [{"x": 42.36, "y": 62.87}]}, "bone21": {"rotate": [{"angle": 22.7}], "translate": [{"x": 6.93, "y": 10.27}]}, "bone22": {"rotate": [{"angle": 40.81, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "angle": 28.38, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.81, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2667, "angle": 28.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 40.81}], "translate": [{"x": -4.45, "y": 4.52}]}, "langhua": {"translate": [{"x": 108.61, "y": 113.35}]}, "langhua2": {"translate": [{"x": -24.56, "y": -10.85}]}, "langhua3": {"translate": [{"time": 0.1, "x": 108.61, "y": 113.35}]}, "langhua4": {"translate": [{"time": 0.1, "x": -24.56, "y": -10.85}]}}, "deform": {"default": {"a28": {"a14": [{"vertices": [-10.87822, -12.938, -10.36755, -13.35064, -10.87827, -12.93802, 10.88344, 12.93349, -8.64232, -19.48536, -8.64195, -19.48529, 8.65005, 19.4817, -5.06323, -7.24054, -5.06287, -7.24047, 5.06598, 7.23837, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.76846, -8.03152, 2.77161, 8.03035]}]}}}, "drawOrder": [{"offsets": [{"slot": "a18", "offset": 6}, {"slot": "a7", "offset": 7}, {"slot": "a6", "offset": 8}, {"slot": "a22", "offset": 5}, {"slot": "a16", "offset": 4}, {"slot": "a14", "offset": -6}, {"slot": "a8", "offset": -7}]}]}, "run3": {"slots": {"tx/1_00006": {"attachment": [{"time": 0.0333, "name": "tx/1_00006"}, {"time": 0.0667, "name": "tx/1_00009"}, {"time": 0.1, "name": "tx/1_00010"}, {"time": 0.1333, "name": "tx/1_00012"}, {"time": 0.1667, "name": "tx/1_00014"}, {"time": 0.2, "name": null}]}, "a28": {"color": [{"color": "ffffffff"}]}, "runwl/A_wenli_wave_0": {"attachment": [{"name": "runwl/A_wenli_wave_00000"}, {"time": 0.0333, "name": "runwl/A_wenli_wave_00002"}, {"time": 0.0667, "name": "runwl/A_wenli_wave_00004"}, {"time": 0.1, "name": "runwl/A_wenli_wave_00006"}, {"time": 0.1333, "name": "runwl/A_wenli_wave_00009"}, {"time": 0.1667, "name": "runwl/A_wenli_wave_00011"}, {"time": 0.2, "name": "runwl/A_wenli_wave_00013"}, {"time": 0.2333, "name": "runwl/A_wenli_wave_00015"}, {"time": 0.2667, "name": "runwl/A_wenli_wave_00017"}, {"time": 0.3333, "name": "runwl/A_wenli_wave_00019"}]}, "tx/1_6": {"attachment": [{"time": 0.1333, "name": "tx/1_00006"}, {"time": 0.1667, "name": "tx/1_00009"}, {"time": 0.2, "name": "tx/1_00010"}, {"time": 0.2333, "name": "tx/1_00012"}, {"time": 0.2667, "name": "tx/1_00014"}, {"time": 0.3, "name": null}]}, "tx/1_00007": {"attachment": [{"name": "tx/1_00007"}, {"time": 0.0333, "name": "tx/1_00008"}, {"time": 0.0667, "name": null}]}, "tx/shuiboquan_00000": {"attachment": [{"name": "tx/shuiboquan_00000"}, {"time": 0.0333, "name": "tx/shuiboquan_00002"}, {"time": 0.0667, "name": "tx/shuiboquan_00004"}, {"time": 0.1, "name": "tx/shuiboquan_00006"}, {"time": 0.1333, "name": "tx/shuiboquan_00008"}, {"time": 0.1667, "name": "tx/shuiboquan_00010"}, {"time": 0.2, "name": "tx/shuiboquan_00012"}, {"time": 0.2333, "name": "tx/shuiboquan_00014"}, {"time": 0.2667, "name": "tx/shuiboquan_00016"}, {"time": 0.3, "name": "tx/shuiboquan_00018"}]}, "tx/1_7": {"attachment": [{"time": 0.1, "name": "tx/1_00007"}, {"time": 0.1333, "name": "tx/1_00008"}, {"time": 0.1667, "name": null}]}, "a26": {"color": [{"color": "ffffffff"}]}, "runwl/A_wenli_wave_00000": {"attachment": [{"name": "runwl/A_wenli_wave_00000"}, {"time": 0.0333, "name": "runwl/A_wenli_wave_00002"}, {"time": 0.0667, "name": "runwl/A_wenli_wave_00004"}, {"time": 0.1, "name": "runwl/A_wenli_wave_00006"}, {"time": 0.1333, "name": "runwl/A_wenli_wave_00009"}, {"time": 0.1667, "name": "runwl/A_wenli_wave_00011"}, {"time": 0.2, "name": "runwl/A_wenli_wave_00013"}, {"time": 0.2333, "name": "runwl/A_wenli_wave_00015"}, {"time": 0.2667, "name": "runwl/A_wenli_wave_00017"}, {"time": 0.3333, "name": "runwl/A_wenli_wave_00019"}]}, "run1/quan20": {"attachment": [{"name": "run1/quan20"}]}, "a10": {"color": [{"color": "ffffff00"}]}, "run1/dd_2": {"color": [{"color": "ffffff53", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff97", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffff53"}], "attachment": [{"name": "run1/dd_2"}]}, "a11": {"color": [{"color": "ffffff00"}]}, "run1/huoq_02": {"attachment": [{"name": "run1/huoq_02"}, {"time": 0.0333, "name": "run1/huoq_04"}, {"time": 0.1, "name": "run1/huoq_06"}, {"time": 0.1333, "name": "run1/huoq_08"}, {"time": 0.1667, "name": "run1/huoq_10"}, {"time": 0.2, "name": "run1/huoq_12"}, {"time": 0.2667, "name": "run1/huoq_14"}, {"time": 0.3, "name": "run1/huoq_16"}]}, "a27": {"color": [{"color": "ffffffff"}]}}, "bones": {"bone60": {"rotate": [{"angle": -28}]}, "bone61": {"rotate": [{"angle": 7.94}]}, "bone64": {"translate": [{"x": -68.92, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 78.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -68.92}]}, "bone": {"translate": [{"x": -4.6}]}, "bone2": {"translate": [{"x": 14.59, "y": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 14.59, "y": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 14.59, "y": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 14.59, "y": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 14.59, "y": -0.55}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -1.66, "y": -19.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone29": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 9.7, "y": -27.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone39": {"rotate": [{"angle": -1.37}]}, "bone40": {"rotate": [{"angle": 3}]}, "bone49": {"translate": [{"x": 34.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -113, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 34.64}]}, "bone50": {"rotate": [{"angle": 59.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": 10.74, "curve": "stepped"}, {"time": 0.0667, "angle": 10.74, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": -96.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -101.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 59.24}], "translate": [{"x": -0.41, "y": 28.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "x": -0.02, "y": 1.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "x": -25.09, "y": 1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "x": -0.68, "y": 47.73, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -11.26, "y": 37.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -0.41, "y": 28.98}]}, "bone44": {"rotate": [{"angle": 0.77}]}, "bone45": {"rotate": [{"angle": -0.57}]}, "bone46": {"rotate": [{"angle": 1.68}]}, "bone59": {"translate": [{"curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.1333, "x": -0.82, "y": 42.43, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": -1.58, "y": 43.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone62": {"rotate": [{"angle": -25.14}]}, "bone63": {"rotate": [{"angle": 1.36}]}, "bone65": {"rotate": [{"angle": -75.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": -97.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 36.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "angle": 5.28, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2333, "angle": 6.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -75.9}], "translate": [{"x": -0.64, "y": 44.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -14.12, "y": 29.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "x": -0.4, "y": 27.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "x": -0.06, "y": 3.91, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2333, "x": -0.02, "y": 1.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -0.64, "y": 44.54}]}, "bone4": {"rotate": [{"angle": -4.85}], "translate": [{"y": 0.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.03, "y": 2.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "y": 0.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.03, "y": 2.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "y": 0.49}]}, "bone23": {"rotate": [{"angle": -301.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -300.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -303.82, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": -301.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -300.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -303.82, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -301.51}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 4.91, "y": -13.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone24": {"rotate": [{"angle": 12.44, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.0333, "angle": 14.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 11.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1667, "angle": 12.44, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 14.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 12.44}]}, "bone5": {"rotate": [{"angle": -8.57}], "translate": [{"x": 3.47, "y": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 8.52, "y": 1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": 3.47, "y": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 8.52, "y": 1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": -3.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -2.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -3.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -2.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -3.53}]}, "bone25": {"rotate": [{"angle": 83.24}]}, "bone7": {"rotate": [{"angle": -1.85, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -14.19, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2333, "angle": -4.32, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -1.85}], "translate": [{"curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone14": {"rotate": [{"angle": -2.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.78, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -2.21}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "a0": {"rotate": [{"angle": -8.81, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.1333, "angle": 18.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1667, "angle": 20.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": 13.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -8.81}], "translate": [{"x": -0.18, "y": 0.3, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2333, "x": -0.54, "y": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3333, "x": -0.18, "y": 0.3}]}, "bone15": {"rotate": [{"angle": -9.79, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -9.79}]}, "bone55": {"rotate": [{"angle": -5.79, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -10.31, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -5.79}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 0.04, "y": -1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone31": {"rotate": [{"angle": 17.02, "curve": 0.291, "c2": 0.19, "c3": 0.663, "c4": 0.65}, {"time": 0.0667, "angle": 12.53, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 10.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.62, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.3333, "angle": 17.02}], "translate": [{"curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "x": -0.35, "y": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "a19": {"rotate": [{"angle": -19.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1333, "angle": 1.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1667, "angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.2}], "translate": [{"curve": 0.324, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.0667, "x": -0.54, "y": 0.88, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.1333}]}, "bone56": {"rotate": [{"angle": 6.42, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -23.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 16.01, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 6.42}]}, "bone33": {"rotate": [{"angle": -5.24, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.0667, "angle": 5.44, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1333, "angle": 9.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.47, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -5.24}]}, "bone51": {"rotate": [{"angle": 1.37, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 12.32, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2, "angle": 0.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": -5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 1.37}], "translate": [{"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": -0.21, "y": 1.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2}]}, "a1": {"rotate": [{"angle": -1.27, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.1, "angle": 13.66, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 10.73, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": -1.97, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": -8.81, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.3333, "angle": -1.27}], "translate": [{"x": -0.18, "y": 0.3, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2333, "x": -0.54, "y": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3333, "x": -0.18, "y": 0.3}]}, "bone13": {"rotate": [{"angle": -26.5, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -34.97, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -26.5}]}, "bone19": {"rotate": [{"angle": -14.61, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 6.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -21.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -14.61}]}, "bone52": {"rotate": [{"angle": -15.1, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.1667, "angle": 23.65, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2, "angle": 14.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -15.1}]}, "bone10": {"rotate": [{"angle": -0.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -16.97, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3, "angle": -5.17, "curve": 0.362, "c2": 0.45, "c3": 0.706, "c4": 0.82}, {"time": 0.3333, "angle": -0.68}], "translate": [{"curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333}]}, "bone58": {"rotate": [{"angle": 9.27, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 9.27}]}, "bone57": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.27, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": 0.49}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 0.04, "y": -1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone16": {"rotate": [{"angle": -9.08, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0667, "angle": -1.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -9.08}], "translate": [{"curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.2333}]}, "bone53": {"rotate": [{"angle": -4.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 5.71, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2333, "angle": -3.1, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": -6.96, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -4.62}], "translate": [{"curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "x": -0.21, "y": 1.01, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.2333}]}, "bone17": {"rotate": [{"angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": -7.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 10.31, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "angle": -12.9}]}, "bone9": {"rotate": [{"angle": -8.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -8.03}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone54": {"rotate": [{"angle": -14.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": -15.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": 5.19, "curve": 0.333, "c2": 0.33, "c3": 0.723, "c4": 0.86}, {"time": 0.3333, "angle": -14.51}]}, "bone12": {"rotate": [{"angle": -5.39, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.68, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -5.39}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone8": {"rotate": [{"angle": -2.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -29.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": -22.21, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.3333, "angle": -2.43}]}, "bone32": {"rotate": [{"angle": 2.32, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.0667, "angle": 8.05, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1333, "angle": 4.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -5.35, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.3333, "angle": 2.32}]}, "a20": {"rotate": [{"angle": -12.58, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -19.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "angle": -9.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 8.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -12.58}], "translate": [{"curve": 0.324, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.0667, "x": -0.54, "y": 0.88, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.1333}]}, "bone18": {"rotate": [{"angle": -1.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.84, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -1.31}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone11": {"rotate": [{"angle": -13.48, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -30.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3, "angle": -23.41, "curve": 0.32, "c2": 0.29, "c3": 0.663, "c4": 0.66}, {"time": 0.3333, "angle": -13.48}]}, "a22": {"rotate": [{"angle": 28.55}], "translate": [{"x": -29.06, "y": 181.52}], "shear": [{"y": -33.32}]}, "bone20": {"rotate": [{"angle": 27.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": 25.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 27.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": 27.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 25.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 27.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": 27.08}], "translate": [{"x": 42.36, "y": 62.87}]}, "bone21": {"rotate": [{"angle": 22.7}], "translate": [{"x": 6.93, "y": 10.27}]}, "bone22": {"rotate": [{"angle": 40.81, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "angle": 28.38, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.81, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2667, "angle": 28.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 40.81}], "translate": [{"x": -4.45, "y": 4.52}]}, "langhua": {"translate": [{"x": 108.61, "y": 113.35}]}, "langhua2": {"translate": [{"x": -24.56, "y": -10.85}]}, "langhua3": {"translate": [{"time": 0.1, "x": 108.61, "y": 113.35}]}, "langhua4": {"translate": [{"time": 0.1, "x": -24.56, "y": -10.85}]}, "runall": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -5.56, "y": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "runall2": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.18, "y": 1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}}, "deform": {"default": {"a28": {"a14": [{"vertices": [-10.87822, -12.938, -10.36755, -13.35064, -10.87827, -12.93802, 10.88344, 12.93349, -8.64232, -19.48536, -8.64195, -19.48529, 8.65005, 19.4817, -5.06323, -7.24054, -5.06287, -7.24047, 5.06598, 7.23837, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.76846, -8.03152, 2.77161, 8.03035]}]}}}, "drawOrder": [{"offsets": [{"slot": "a18", "offset": 6}, {"slot": "a7", "offset": 7}, {"slot": "a6", "offset": 8}, {"slot": "a22", "offset": 5}, {"slot": "a16", "offset": 4}, {"slot": "a14", "offset": -6}, {"slot": "a8", "offset": -7}]}]}, "show_time": {"slots": {"tx/1_00006": {"attachment": [{"time": 0.0333, "name": "tx/1_00006"}, {"time": 0.0667, "name": "tx/1_00009"}, {"time": 0.1, "name": "tx/1_00010"}, {"time": 0.1333, "name": "tx/1_00012"}, {"time": 0.1667, "name": "tx/1_00014"}, {"time": 0.2, "name": null}]}, "a28": {"color": [{"color": "ffffffff"}]}, "runwl/A_wenli_wave_0": {"attachment": [{"name": "runwl/A_wenli_wave_00000"}, {"time": 0.0333, "name": "runwl/A_wenli_wave_00002"}, {"time": 0.0667, "name": "runwl/A_wenli_wave_00004"}, {"time": 0.1, "name": "runwl/A_wenli_wave_00006"}, {"time": 0.1333, "name": "runwl/A_wenli_wave_00009"}, {"time": 0.1667, "name": "runwl/A_wenli_wave_00011"}, {"time": 0.2, "name": "runwl/A_wenli_wave_00013"}, {"time": 0.2333, "name": "runwl/A_wenli_wave_00015"}, {"time": 0.2667, "name": "runwl/A_wenli_wave_00017"}, {"time": 0.3333, "name": "runwl/A_wenli_wave_00019"}]}, "tx/1_6": {"attachment": [{"time": 0.1333, "name": "tx/1_00006"}, {"time": 0.1667, "name": "tx/1_00009"}, {"time": 0.2, "name": "tx/1_00010"}, {"time": 0.2333, "name": "tx/1_00012"}, {"time": 0.2667, "name": "tx/1_00014"}, {"time": 0.3, "name": null}]}, "tx/1_00007": {"attachment": [{"name": "tx/1_00007"}, {"time": 0.0333, "name": "tx/1_00008"}, {"time": 0.0667, "name": null}]}, "tx/shuiboquan_00000": {"attachment": [{"name": "tx/shuiboquan_00000"}, {"time": 0.0333, "name": "tx/shuiboquan_00002"}, {"time": 0.0667, "name": "tx/shuiboquan_00004"}, {"time": 0.1, "name": "tx/shuiboquan_00006"}, {"time": 0.1333, "name": "tx/shuiboquan_00008"}, {"time": 0.1667, "name": "tx/shuiboquan_00010"}, {"time": 0.2, "name": "tx/shuiboquan_00012"}, {"time": 0.2333, "name": "tx/shuiboquan_00014"}, {"time": 0.2667, "name": "tx/shuiboquan_00016"}, {"time": 0.3, "name": "tx/shuiboquan_00018"}]}, "tx/1_7": {"attachment": [{"time": 0.1, "name": "tx/1_00007"}, {"time": 0.1333, "name": "tx/1_00008"}, {"time": 0.1667, "name": null}]}, "a26": {"color": [{"color": "ffffffff"}]}, "runwl/A_wenli_wave_00000": {"attachment": [{"name": "runwl/A_wenli_wave_00000"}, {"time": 0.0333, "name": "runwl/A_wenli_wave_00002"}, {"time": 0.0667, "name": "runwl/A_wenli_wave_00004"}, {"time": 0.1, "name": "runwl/A_wenli_wave_00006"}, {"time": 0.1333, "name": "runwl/A_wenli_wave_00009"}, {"time": 0.1667, "name": "runwl/A_wenli_wave_00011"}, {"time": 0.2, "name": "runwl/A_wenli_wave_00013"}, {"time": 0.2333, "name": "runwl/A_wenli_wave_00015"}, {"time": 0.2667, "name": "runwl/A_wenli_wave_00017"}, {"time": 0.3333, "name": "runwl/A_wenli_wave_00019"}]}, "run1/quan20": {"attachment": [{"name": "run1/quan20"}]}, "a10": {"color": [{"color": "ffffff00"}]}, "run1/dd_2": {"color": [{"color": "ffffff53", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff97", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffff53"}], "attachment": [{"name": "run1/dd_2"}]}, "a11": {"color": [{"color": "ffffff00"}]}, "run1/huoq_02": {"attachment": [{"name": "run1/huoq_02"}, {"time": 0.0333, "name": "run1/huoq_04"}, {"time": 0.1, "name": "run1/huoq_06"}, {"time": 0.1333, "name": "run1/huoq_08"}, {"time": 0.1667, "name": "run1/huoq_10"}, {"time": 0.2, "name": "run1/huoq_12"}, {"time": 0.2667, "name": "run1/huoq_14"}, {"time": 0.3, "name": "run1/huoq_16"}]}, "a27": {"color": [{"color": "ffffffff"}]}}, "bones": {"bone60": {"rotate": [{"angle": -28}]}, "bone61": {"rotate": [{"angle": 7.94}]}, "bone64": {"translate": [{"x": -68.92, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 78.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -68.92}]}, "bone": {"translate": [{"x": -4.6}]}, "bone2": {"translate": [{"x": 14.59, "y": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 14.59, "y": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 14.59, "y": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 14.59, "y": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 14.59, "y": -0.55}]}, "bone27": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -1.66, "y": -19.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone29": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 9.7, "y": -27.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone39": {"rotate": [{"angle": -1.37}]}, "bone40": {"rotate": [{"angle": 3}]}, "bone49": {"translate": [{"x": 34.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -113, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 34.64}]}, "bone50": {"rotate": [{"angle": 59.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": 10.74, "curve": "stepped"}, {"time": 0.0667, "angle": 10.74, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": -96.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -101.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 59.24}], "translate": [{"x": -0.41, "y": 28.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "x": -0.02, "y": 1.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "x": -25.09, "y": 1, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "x": -0.68, "y": 47.73, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -11.26, "y": 37.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -0.41, "y": 28.98}]}, "bone44": {"rotate": [{"angle": 0.77}]}, "bone45": {"rotate": [{"angle": -0.57}]}, "bone46": {"rotate": [{"angle": 1.68}]}, "bone59": {"translate": [{"curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.1333, "x": -0.82, "y": 42.43, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": -1.58, "y": 43.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone62": {"rotate": [{"angle": -25.14}]}, "bone63": {"rotate": [{"angle": 1.36}]}, "bone65": {"rotate": [{"angle": -75.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": -97.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 36.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "angle": 5.28, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2333, "angle": 6.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -75.9}], "translate": [{"x": -0.64, "y": 44.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -14.12, "y": 29.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "x": -0.4, "y": 27.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "x": -0.06, "y": 3.91, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2333, "x": -0.02, "y": 1.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -0.64, "y": 44.54}]}, "bone4": {"rotate": [{"angle": -4.85}], "translate": [{"y": 0.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.03, "y": 2.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "y": 0.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.03, "y": 2.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "y": 0.49}]}, "bone23": {"rotate": [{"angle": -301.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -300.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -303.82, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": -301.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -300.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -303.82, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -301.51}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 4.91, "y": -13.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone24": {"rotate": [{"angle": 12.44, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.0333, "angle": 14.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 11.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1667, "angle": 12.44, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 14.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.32, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 12.44}]}, "bone5": {"rotate": [{"angle": -8.57}], "translate": [{"x": 3.47, "y": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 8.52, "y": 1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": 3.47, "y": 0.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 8.52, "y": 1.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 3.47, "y": 0.71}]}, "bone6": {"rotate": [{"angle": -3.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -2.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -3.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -2.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -3.53}]}, "bone25": {"rotate": [{"angle": 83.24}]}, "bone7": {"rotate": [{"angle": -1.85, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -14.19, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2333, "angle": -4.32, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -1.85}], "translate": [{"curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone14": {"rotate": [{"angle": -2.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.78, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -2.21}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "a0": {"rotate": [{"angle": -8.81, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.1333, "angle": 18.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1667, "angle": 20.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": 13.7, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -8.81}], "translate": [{"x": -0.18, "y": 0.3, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2333, "x": -0.54, "y": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3333, "x": -0.18, "y": 0.3}]}, "bone15": {"rotate": [{"angle": -9.79, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -9.79}]}, "bone55": {"rotate": [{"angle": -5.79, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -10.31, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -5.79}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 0.04, "y": -1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone31": {"rotate": [{"angle": 17.02, "curve": 0.291, "c2": 0.19, "c3": 0.663, "c4": 0.65}, {"time": 0.0667, "angle": 12.53, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 10.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.62, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.3333, "angle": 17.02}], "translate": [{"curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "x": -0.35, "y": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "a19": {"rotate": [{"angle": -19.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1333, "angle": 1.51, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1667, "angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.2}], "translate": [{"curve": 0.324, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.0667, "x": -0.54, "y": 0.88, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.1333}]}, "bone56": {"rotate": [{"angle": 6.42, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -23.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 16.01, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 6.42}]}, "bone33": {"rotate": [{"angle": -5.24, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.0667, "angle": 5.44, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.1333, "angle": 9.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.47, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -5.24}]}, "bone51": {"rotate": [{"angle": 1.37, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 12.32, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2, "angle": 0.27, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": -5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 1.37}], "translate": [{"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": -0.21, "y": 1.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2}]}, "a1": {"rotate": [{"angle": -1.27, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.1, "angle": 13.66, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 10.73, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": -1.97, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2667, "angle": -8.81, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.3333, "angle": -1.27}], "translate": [{"x": -0.18, "y": 0.3, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2333, "x": -0.54, "y": 0.88, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3333, "x": -0.18, "y": 0.3}]}, "bone13": {"rotate": [{"angle": -26.5, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -34.97, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -26.5}]}, "bone19": {"rotate": [{"angle": -14.61, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 6.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -21.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -14.61}]}, "bone52": {"rotate": [{"angle": -15.1, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.1667, "angle": 23.65, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2, "angle": 14.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -15.1}]}, "bone10": {"rotate": [{"angle": -0.68, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -16.97, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3, "angle": -5.17, "curve": 0.362, "c2": 0.45, "c3": 0.706, "c4": 0.82}, {"time": 0.3333, "angle": -0.68}], "translate": [{"curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333}]}, "bone58": {"rotate": [{"angle": 9.27, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 9.27}]}, "bone57": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.27, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": 0.49}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 0.04, "y": -1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone16": {"rotate": [{"angle": -9.08, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.0667, "angle": -1.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -9.08}], "translate": [{"curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.2333}]}, "bone53": {"rotate": [{"angle": -4.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 5.71, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2333, "angle": -3.1, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": -6.96, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -4.62}], "translate": [{"curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "x": -0.21, "y": 1.01, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.2333}]}, "bone17": {"rotate": [{"angle": -13.3, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": -7.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 10.31, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "angle": -12.9}]}, "bone9": {"rotate": [{"angle": -8.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -8.03}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone54": {"rotate": [{"angle": -14.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": -15.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": 5.19, "curve": 0.333, "c2": 0.33, "c3": 0.723, "c4": 0.86}, {"time": 0.3333, "angle": -14.51}]}, "bone12": {"rotate": [{"angle": -5.39, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.68, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -5.39}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone8": {"rotate": [{"angle": -2.43, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -29.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": -22.21, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.3333, "angle": -2.43}]}, "bone32": {"rotate": [{"angle": 2.32, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.0667, "angle": 8.05, "curve": 0.286, "c2": 0.11, "c3": 0.63, "c4": 0.5}, {"time": 0.1333, "angle": 4.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -5.35, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.3333, "angle": 2.32}]}, "a20": {"rotate": [{"angle": -12.58, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -19.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "angle": -9.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 8.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -12.58}], "translate": [{"curve": 0.324, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.0667, "x": -0.54, "y": 0.88, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.1333}]}, "bone18": {"rotate": [{"angle": -1.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.84, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -1.31}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -0.31, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}]}, "bone11": {"rotate": [{"angle": -13.48, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -30.89, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3, "angle": -23.41, "curve": 0.32, "c2": 0.29, "c3": 0.663, "c4": 0.66}, {"time": 0.3333, "angle": -13.48}]}, "a22": {"rotate": [{"angle": 28.55}], "translate": [{"x": -29.06, "y": 181.52}], "shear": [{"y": -33.32}]}, "bone20": {"rotate": [{"angle": 27.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": 25.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 27.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": 27.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 25.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 27.07, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": 27.08}], "translate": [{"x": 42.36, "y": 62.87}]}, "bone21": {"rotate": [{"angle": 22.7}], "translate": [{"x": 6.93, "y": 10.27}]}, "bone22": {"rotate": [{"angle": 40.81, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "angle": 28.38, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.81, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2667, "angle": 28.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 40.81}], "translate": [{"x": -4.45, "y": 4.52}]}, "langhua": {"translate": [{"x": 108.61, "y": 113.35}]}, "langhua2": {"translate": [{"x": -24.56, "y": -10.85}]}, "langhua3": {"translate": [{"time": 0.1, "x": 108.61, "y": 113.35}]}, "langhua4": {"translate": [{"time": 0.1, "x": -24.56, "y": -10.85}]}, "runall": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -5.56, "y": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "runall2": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.18, "y": 1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}}, "deform": {"default": {"a28": {"a14": [{"vertices": [-10.87822, -12.938, -10.36755, -13.35064, -10.87827, -12.93802, 10.88344, 12.93349, -8.64232, -19.48536, -8.64195, -19.48529, 8.65005, 19.4817, -5.06323, -7.24054, -5.06287, -7.24047, 5.06598, 7.23837, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.76846, -8.03152, 2.77161, 8.03035]}]}}}, "drawOrder": [{"offsets": [{"slot": "a18", "offset": 6}, {"slot": "a7", "offset": 7}, {"slot": "a6", "offset": 8}, {"slot": "a22", "offset": 5}, {"slot": "a16", "offset": 4}, {"slot": "a14", "offset": -6}, {"slot": "a8", "offset": -7}]}]}}}