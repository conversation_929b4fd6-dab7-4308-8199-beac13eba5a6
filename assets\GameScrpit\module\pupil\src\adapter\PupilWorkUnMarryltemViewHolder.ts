import { _decorator, Node, UITransform } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PupilModule } from "../PupilModule";
import { Label } from "cc";
import { Sprite } from "cc";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { Layout } from "cc";
import TipMgr from "../../../../lib/tips/TipMgr";
import Formate from "../../../../lib/utils/Formate";
import { PupilHeader } from "../prefab/ui/PupilHeader";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("PupilWorkUnMarryltemViewHolder")
export class PupilWorkUnMarryltemViewHolder extends ViewHolder {
  @property(Label)
  private lblName: Label;

  @property(Sprite)
  private spNewAttr: Sprite;

  @property(Label)
  private lblNewAttr: Label;

  @property(Layout)
  private layoutAttr: Layout;

  @property(Node)
  private nodeStart: Node;

  @property(Node)
  private nodeStop: Node;

  @property(Node)
  private nodeJob: Node;

  getViewType() {
    return 2;
  }

  private _pupilId: number;
  private _slotIndex: number;

  public init() {}

  public updateData(position: number, args: any) {
    this._pupilId = args.pupilId;
    this._slotIndex = args.slotIndex;

    const pupilMsg = PupilModule.data.allPupilMap[this._pupilId];
    const pupilInfo = pupilMsg.ownInfo;

    // 头像
    this.node
      .getChildByPath("node_1/btn_head1/PupilHeader")
      .getComponent(PupilHeader)
      .setHeaderByNameId(pupilInfo.nameId);

    // 徒弟名称
    this.lblName.string = PupilModule.service.getPupilName(pupilInfo.nameId);

    // 徒弟出师标签背景
    PupilModule.service.setPupilAdultAttrBg(this.spNewAttr, pupilInfo.talentId);

    // 出师属性配置
    this.lblNewAttr.string = Formate.formatAttribute(pupilInfo.adultAttrList[0], pupilInfo.adultAttrList[1]);

    // 上下阵控制按钮状态
    const isWorking = PupilModule.data.pupilTrainMsg.workSlotList.indexOf(pupilMsg.id) != -1;
    this.nodeStart.active = !isWorking;
    this.nodeStop.active = isWorking;

    // 设置初始属性
    PupilModule.service.setLayoutAttr(this.layoutAttr, pupilInfo.initAttrList);

    // 任职显示
    this.nodeJob.getChildByName("bg_job3").active = PupilModule.data.pupilTrainMsg.workSlotList[0] == this._pupilId;
    this.nodeJob.getChildByName("bg_job2").active =
      PupilModule.data.pupilTrainMsg.workSlotList[1] == this._pupilId ||
      PupilModule.data.pupilTrainMsg.workSlotList[2] == this._pupilId;
    this.nodeJob.getChildByName("bg_job1").active =
      PupilModule.data.pupilTrainMsg.workSlotList[3] == this._pupilId ||
      PupilModule.data.pupilTrainMsg.workSlotList[4] == this._pupilId;

    // 主动适配一下界面
    this.layoutAttr.updateLayout();
    this.node.getComponent(Layout).updateLayout();
    // log.log("height", this.node.getComponent(UITransform).height);
  }

  public onStartWork() {
    AudioMgr.instance.playEffect(1557);
    PupilModule.api.work(this._pupilId, this._slotIndex, (data) => {
      UIMgr.instance.back();
      TipMgr.showTip("上阵成功");
    });
  }

  public onStopWork() {
    AudioMgr.instance.playEffect(1557);
    PupilModule.api.leaveWork(this._slotIndex, (data) => {
      UIMgr.instance.back();
      TipMgr.showTip("下阵成功");
    });
  }
}
