import { _decorator, Node } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { NodeTool } from "../lib/utils/NodeTool";
import { v3 } from "cc";
import { sp } from "cc";
import { tween } from "cc";
import { PlayerModule } from "../module/player/PlayerModule";
import { Label } from "cc";
import { math } from "cc";
import { AudioMgr, AudioName } from "../../platform/src/AudioHelper";
import { TipsMgr } from "../../platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("TopPlayerLevelUpRes")
export class TopPlayerLevelUpRes extends BaseCtrl {
  @property(Node)
  private nodeBg: Node;

  @property(Label)
  private lblLevel: Label;

  @property(Label)
  private lblLevelName: Label;

  @property(Node)
  private nodeLayout: Node;

  private isAniFinish = false;

  start() {
    super.start();
    TipsMgr.setEnableTouch(false, 0.2);
    this.playeAction();
    this.initPopup();

    AudioMgr.instance.playEffect(AudioName.Effect.武将升级);

    tween(this.node)
      .delay(0.5)
      .call(() => {
        this.isAniFinish = true;
      })
      .start();
  }

  private playeAction() {
    this.nodeBg.scale = v3(0, 0, 0);
    NodeTool.findByName(this.node, "tytanchuang")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        NodeTool.findByName(this.node, "tytanchuang")
          .getComponent(sp.Skeleton)
          .setAnimation(0, "zi_gongxitisheng_1", true);
      });
    NodeTool.findByName(this.node, "tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_gongxitisheng", false);
    tween(this.nodeBg)
      .to(0.1, { scale: v3(1, 1, 1) })
      .start();
  }

  private initPopup() {
    let lv = PlayerModule.data.getPlayerInfo().level;
    let configLeader = PlayerModule.data.getConfigLeaderData(lv);
    let configLeaderPre = PlayerModule.data.getConfigLeaderData(lv - 1);

    this.lblLevel.string = `${lv}`;
    this.lblLevelName.string = `${configLeader.name}`;

    this.setNodeValue(
      this.nodeLayout.getChildByName("node_day_reward"),
      configLeaderPre.dayReward,
      configLeader.dayReward
    );
    this.setNodeValue(this.nodeLayout.getChildByName("node_chat_max"), configLeaderPre.chatMax, configLeader.chatMax);
    this.setNodeValue(
      this.nodeLayout.getChildByName("node_chat_time"),
      configLeaderPre.chatTime,
      configLeader.chatTime
    );
    this.setNodeValue(
      this.nodeLayout.getChildByName("node_train_max"),
      configLeaderPre.trainMax,
      configLeader.trainMax
    );
    this.setNodeValue(
      this.nodeLayout.getChildByName("node_train_reward"),
      configLeaderPre.trainReward,
      configLeader.trainReward
    );
  }

  private setNodeValue(node: Node, oldValue: number, newValue: number) {
    node.active = newValue > 0;

    if (node.active) {
      node.getChildByPath("layout_item/lbl_old_value").getComponent(Label).string = `${oldValue}`;
      node.getChildByPath("layout_item/lbl_new_value").getComponent(Label).string = `${newValue}`;

      node.getChildByPath("layout_item/node_up").active = newValue != oldValue;
      let color = math.color("FFFBE5");
      if (newValue != oldValue) {
        color = math.color("74FF77");
      }
      node.getChildByPath("layout_item/lbl_new_value").getComponent(Label).color = color;
    }
  }

  onBtnClose() {
    if (!this.isAniFinish) {
      return;
    }

    this.closeBack();
  }
}
