{"skeleton": {"hash": "b8CJxf3xAISYZx3nMFvuvMz1+cQ=", "spine": "3.8.75", "x": -60.7, "y": -18.74, "width": 116.07, "height": 139.02, "images": "./images/", "audio": "D:/spine导出/弟子spine/鸡弟子"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 165.65, "rotation": 0.28, "scaleX": 0.365, "scaleY": 0.365}, {"name": "bone2", "parent": "bone", "length": 113.83, "rotation": -1.74, "x": -18.1, "y": 93.67}, {"name": "bone3", "parent": "bone2", "length": 47.58, "rotation": 73.76, "x": -24.74, "y": 5.64}, {"name": "bone4", "parent": "bone3", "length": 40.76, "rotation": 6.79, "x": 47.58}, {"name": "bone5", "parent": "bone4", "length": 27.64, "rotation": 16.92, "x": 40.76}, {"name": "bone6", "parent": "bone4", "x": 39.35, "y": -66.56}, {"name": "bone7", "parent": "bone4", "x": 23.15, "y": 37.85}, {"name": "bone8", "parent": "bone6", "length": 54.66, "rotation": -127.67, "x": -0.12, "y": -17.21}, {"name": "bone9", "parent": "bone8", "length": 50.67, "rotation": -43.61, "x": 54.66}, {"name": "bone10", "parent": "bone9", "length": 37.14, "rotation": -27.38, "x": 50.67}, {"name": "bone11", "parent": "bone7", "length": 44.89, "rotation": 166.82, "x": -6.58, "y": 6.1}, {"name": "bone12", "parent": "bone11", "length": 41.37, "rotation": -16.66, "x": 44.89}, {"name": "bone13", "parent": "bone12", "length": 41.94, "rotation": -29.78, "x": 41.37}, {"name": "bone15", "parent": "bone2", "x": 58.46, "y": -36.27}, {"name": "bone16", "parent": "bone2", "length": 35.19, "rotation": -90.3, "x": -30.2, "y": -37.09}, {"name": "bone17", "parent": "bone16", "length": 17.26, "rotation": -1.82, "x": 35.19}, {"name": "bone18", "parent": "bone17", "length": 18.06, "rotation": -65.47, "x": 17.24, "y": 0.36}, {"name": "bone20", "parent": "bone2", "length": 46.63, "rotation": -69.83, "x": 62.17, "y": -32.22}, {"name": "bone21", "parent": "bone20", "length": 21.87, "rotation": -2.64, "x": 46.63}, {"name": "bone22", "parent": "bone21", "length": 17.41, "rotation": 43.16, "x": 23.79, "y": 5.38}, {"name": "bone24", "parent": "bone5", "length": 23.12, "rotation": -21.26, "x": 115.21, "y": 11.86}, {"name": "bone25", "parent": "bone5", "length": 26.4, "rotation": -53.5, "x": 115.02, "y": -17.06}, {"name": "bone26", "parent": "bone5", "length": 25.21, "rotation": -70.27, "x": 95.17, "y": -46.78}, {"name": "bone27", "parent": "bone5", "length": 16.86, "rotation": 21.17, "x": 89.12, "y": 31.33}, {"name": "bone28", "parent": "bone5", "x": 13.82, "y": 28.5, "color": "abe323ff"}, {"name": "bone29", "parent": "bone5", "x": 79.61, "y": 11.48}, {"name": "bone30", "parent": "bone5", "length": 11.66, "rotation": -126.54, "x": 14.73, "y": -56.27}, {"name": "bone31", "parent": "bone30", "length": 13.69, "rotation": 34.85, "x": 11.66}, {"name": "bone32", "parent": "bone5", "length": 12.5, "rotation": -153.63, "x": -3.2, "y": -15.02}, {"name": "bone33", "parent": "bone32", "length": 15.3, "rotation": 11.26, "x": 12.5}, {"name": "bone34", "parent": "bone5", "length": 17.53, "rotation": 163.84, "x": -1.82, "y": 32.48}, {"name": "bone35", "parent": "bone34", "length": 20.87, "rotation": 8.03, "x": 17.44, "y": 0.51}, {"name": "bone36", "parent": "bone5", "length": 14.52, "rotation": 148.79, "x": 1.95, "y": 48.66}, {"name": "bone37", "parent": "bone36", "length": 14.81, "rotation": -14.86, "x": 14.41, "y": 0.23}, {"name": "bone14", "parent": "bone2", "length": 21.8, "rotation": 1.98, "x": -11.49, "y": -22.02}, {"name": "bone38", "parent": "bone14", "length": 24.81, "rotation": -29.98, "x": 21.8}, {"name": "bone39", "parent": "bone38", "length": 24.74, "rotation": -9.64, "x": 24.71, "y": 0.17}, {"name": "bone40", "parent": "bone14", "length": 17.6, "rotation": -36.74, "x": 3.95, "y": -5.04}, {"name": "bone41", "parent": "bone40", "length": 20.48, "rotation": -21.95, "x": 17.76, "y": 0.12}, {"name": "bone42", "parent": "bone41", "length": 15.66, "rotation": -6.9, "x": 20.58, "y": 0.17}, {"name": "bone43", "parent": "bone2", "length": 13.09, "rotation": -139.75, "x": -43.28, "y": -15.02}, {"name": "bone44", "parent": "bone43", "length": 17.71, "rotation": 25.8, "x": 13.09}, {"name": "bone45", "parent": "bone44", "length": 15.12, "rotation": -2.17, "x": 17.71}, {"name": "bone46", "parent": "bone", "length": 84.49, "rotation": 179.49, "x": -52.68, "y": 3.13}, {"name": "target1", "parent": "bone46", "rotation": -179.78, "x": -0.68, "y": -2.04, "color": "ff3f00ff"}, {"name": "target2", "parent": "bone46", "rotation": -179.78, "x": 16.39, "y": 5.81, "color": "ff3f00ff"}, {"name": "bone47", "parent": "bone", "length": 70.59, "rotation": 179.21, "x": 62.79, "y": -4.79}, {"name": "target3", "parent": "bone47", "rotation": -179.49, "x": -0.61, "y": 0.92, "color": "ff3f00ff"}, {"name": "target4", "parent": "bone47", "rotation": -179.49, "x": -22.98, "y": 8.33, "color": "ff3f00ff"}], "slots": [{"name": "sd", "bone": "root", "color": "ffffff87", "attachment": "sd"}, {"name": "c1", "bone": "bone", "attachment": "c1"}, {"name": "j3", "bone": "bone16", "attachment": "j3"}, {"name": "j4", "bone": "bone20", "attachment": "j4"}, {"name": "j1", "bone": "bone", "attachment": "j1"}, {"name": "j2", "bone": "bone", "attachment": "j2"}, {"name": "b2", "bone": "bone", "attachment": "b2"}, {"name": "m2", "bone": "bone", "attachment": "m2"}, {"name": "m1", "bone": "bone", "attachment": "m1"}, {"name": "tou2", "bone": "bone", "attachment": "tou2"}, {"name": "tou1", "bone": "bone5"}, {"name": "c2", "bone": "bone", "attachment": "c2"}], "ik": [{"name": "target1", "bones": ["bone16", "bone17"], "target": "target1"}, {"name": "target2", "order": 3, "bones": ["bone17", "bone18"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 1, "bones": ["bone20", "bone21"], "target": "target3", "bendPositive": false}, {"name": "target4", "order": 2, "bones": ["bone21", "bone22"], "target": "target4", "stretch": true}], "transform": [{"name": "face", "order": 4, "bones": ["bone29"], "target": "bone28", "x": 65.79, "y": -17.02, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "s1", "order": 5, "bones": ["bone6"], "target": "bone28", "rotation": -16.92, "x": -34.54, "y": -91.78, "shearY": 360, "rotateMix": -0.199, "translateMix": -0.199, "scaleMix": -0.199, "shearMix": -0.199}, {"name": "s2", "order": 6, "bones": ["bone7"], "target": "bone28", "rotation": -16.92, "x": -19.65, "y": 12.84, "shearY": 360, "rotateMix": -0.199, "translateMix": -0.199, "scaleMix": -0.199, "shearMix": -0.199}, {"name": "ss1", "order": 7, "bones": ["bone14"], "target": "bone28", "rotation": -95.48, "x": -125, "y": -11, "shearY": -13.2, "rotateMix": 0.328, "translateMix": 0.328, "scaleMix": 0.328, "shearMix": 0.328}, {"name": "ss2", "order": 8, "bones": ["bone43"], "target": "bone28", "rotation": 122.79, "x": -114.45, "y": 23.55, "rotateMix": 0.247, "translateMix": 0.247, "scaleMix": 0.247, "shearMix": 0.247}], "skins": [{"name": "default", "attachments": {"j1": {"j1": {"type": "mesh", "uvs": [0.53931, 0.13502, 0.54213, 0.02593, 0.65076, 0.05038, 0.77631, 0.08988, 0.74245, 0.17829, 0.72553, 0.28174, 0.71847, 0.36262, 0.77772, 0.40776, 0.86095, 0.41717, 0.93854, 0.46043, 0.97663, 0.54695, 0.97804, 0.65793, 0.93431, 0.74069, 0.88776, 0.65416, 0.79606, 0.63535, 0.69731, 0.65416, 0.62254, 0.67862, 0.55342, 0.74821, 0.46031, 0.79335, 0.35028, 0.80276, 0.25999, 0.87047, 0.19651, 0.93254, 0.15137, 0.98145, 0.06531, 0.94571, 0.00889, 0.89681, 0.03146, 0.74445, 0.10905, 0.61466, 0.20215, 0.52626, 0.32065, 0.49993, 0.40812, 0.49993, 0.48429, 0.42281, 0.52238, 0.34757, 0.53649, 0.23283, 0.63963, 0.16267, 0.62299, 0.26574, 0.61614, 0.36489, 0.61125, 0.51231, 0.61372, 0.43792, 0.69051, 0.51231, 0.77857, 0.51492, 0.86174, 0.53449, 0.92436, 0.58015, 0.93414, 0.65321, 0.52319, 0.5658, 0.41849, 0.62059, 0.32065, 0.63233, 0.2277, 0.68974, 0.14355, 0.76279, 0.09267, 0.86716], "triangles": [39, 7, 8, 39, 8, 40, 38, 6, 7, 36, 37, 38, 36, 30, 37, 40, 8, 9, 41, 40, 9, 43, 30, 36, 29, 30, 43, 10, 41, 9, 44, 29, 43, 45, 27, 28, 45, 28, 29, 44, 45, 29, 14, 39, 40, 11, 42, 41, 15, 39, 14, 13, 40, 41, 13, 41, 42, 14, 40, 13, 11, 41, 10, 15, 16, 36, 15, 36, 38, 43, 36, 16, 46, 27, 45, 26, 27, 46, 12, 42, 11, 13, 42, 12, 17, 43, 16, 44, 43, 17, 47, 26, 46, 25, 26, 47, 18, 44, 17, 19, 45, 44, 19, 44, 18, 46, 45, 19, 48, 25, 47, 20, 46, 19, 47, 46, 20, 21, 48, 47, 24, 25, 48, 20, 21, 47, 23, 24, 48, 22, 23, 48, 21, 22, 48, 15, 38, 39, 38, 37, 6, 38, 7, 39, 0, 1, 2, 33, 0, 2, 4, 33, 2, 3, 4, 2, 32, 0, 33, 34, 32, 33, 5, 33, 4, 34, 33, 5, 31, 32, 34, 6, 34, 5, 35, 31, 34, 35, 34, 6, 37, 31, 35, 30, 31, 37, 6, 37, 35], "vertices": [1, 16, 2.54, -3.77, 1, 1, 16, -2.71, -1.83, 1, 1, 16, -5.98, 3.19, 1, 1, 16, -1.62, 8.51, 1, 1, 16, 3.69, 7.7, 1, 1, 16, 8.08, 7.01, 1, 1, 17, -8.43, -1.89, 1, 1, 17, -10.6, 1.26, 1, 1, 17, -14.63, 3.63, 1, 1, 17, -17.76, 7.15, 1, 1, 17, -18.11, 11.35, 1, 1, 17, -16.16, 15.59, 1, 1, 17, -12.45, 17.66, 1, 1, 17, -11.67, 13.25, 1, 1, 17, -7.38, 10.32, 1, 1, 17, -2.06, 8.64, 1, 1, 17, 2.16, 7.75, 1, 1, 17, 6.92, 8.71, 1, 1, 17, 12.44, 8.16, 1, 1, 17, 18.16, 5.85, 1, 1, 17, 23.95, 6.22, 1, 1, 17, 28.28, 7.03, 1, 1, 17, 31.45, 7.79, 1, 1, 17, 35.15, 4.35, 1, 1, 17, 37.1, 1.13, 1, 1, 17, 33.19, -4.09, 1, 1, 17, 26.92, -7.13, 1, 1, 17, 20.61, -8.21, 1, 1, 17, 14.15, -6.34, 1, 2, 16, 18.28, -10.2, 0.01701, 17, 9.73, -4.22, 0.98299, 2, 16, 14.79, -6.13, 0.46951, 17, 4.49, -5.29, 0.53049, 2, 16, 11.51, -4.19, 0.9484, 17, 1.2, -7.21, 0.0516, 1, 16, 6.65, -3.68, 1, 1, 16, 1, 2.1, 1, 1, 16, 7.75, 1.23, 1, 2, 16, 11.93, 1.1, 0.98919, 17, -3.22, -4.28, 0.01081, 1, 17, -0.29, 1.18, 1, 2, 16, 15, 1.14, 0.79049, 17, -1.77, -1.58, 0.20951, 1, 17, -4.29, 3.1, 1, 1, 17, -8.69, 5.33, 1, 1, 17, -12.53, 8.09, 1, 1, 17, -14.86, 11.34, 1, 1, 17, -14.03, 14.34, 1, 1, 17, 5.12, 1.07, 1, 1, 17, 11.41, 0.6, 1, 1, 17, 16.56, -1.32, 1, 1, 17, 22.29, -1.41, 1, 1, 17, 27.87, -0.68, 1, 1, 17, 32.34, 2.04, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 4, 66, 66, 68, 68, 70, 70, 74, 74, 72, 72, 76, 76, 78, 78, 80, 80, 82, 82, 84, 72, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96], "width": 56, "height": 42}}, "j2": {"j2": {"type": "mesh", "uvs": [0.26055, 0.18373, 0.20478, 0.13861, 0.14106, 0.09725, 0.17451, 0.05213, 0.33065, 0.02205, 0.47085, 0.01453, 0.5059, 0.07281, 0.48041, 0.12357, 0.47722, 0.18937, 0.53777, 0.30029, 0.61743, 0.42813, 0.74807, 0.43753, 0.91377, 0.47325, 0.9998, 0.62365, 0.94563, 0.78533, 0.89465, 0.91505, 0.79428, 1, 0.68753, 0.83797, 0.54733, 0.71577, 0.38163, 0.73645, 0.26533, 0.64433, 0.2478, 0.56349, 0.13468, 0.59921, 0.00085, 0.65561, 0.00245, 0.48641, 0.06139, 0.35105, 0.18567, 0.31721, 0.23824, 0.32661, 0.28604, 0.24013, 0.34685, 0.09147, 0.37657, 0.22078, 0.41279, 0.32817, 0.42579, 0.49475, 0.58166, 0.56464, 0.73025, 0.61505, 0.85098, 0.70272, 0.24919, 0.43313, 0.11175, 0.47916, 0.04859, 0.55587], "triangles": [36, 37, 26, 19, 20, 32, 18, 19, 32, 37, 25, 26, 24, 25, 37, 38, 24, 37, 33, 32, 10, 22, 37, 21, 38, 37, 22, 34, 10, 11, 34, 11, 12, 33, 10, 34, 23, 24, 38, 23, 38, 22, 35, 34, 12, 35, 12, 13, 18, 32, 33, 18, 33, 34, 14, 35, 13, 17, 18, 34, 17, 34, 35, 15, 35, 14, 17, 35, 15, 16, 17, 15, 21, 37, 36, 20, 21, 32, 36, 26, 27, 31, 36, 27, 21, 36, 32, 29, 4, 5, 7, 5, 6, 29, 5, 7, 1, 3, 4, 1, 4, 29, 2, 3, 1, 0, 1, 29, 7, 30, 29, 8, 30, 7, 0, 29, 30, 28, 0, 30, 31, 30, 8, 31, 8, 9, 28, 30, 31, 32, 31, 9, 32, 9, 10, 31, 27, 28, 32, 36, 31], "vertices": [1, 19, 4.2, -5.71, 1, 1, 19, 1.15, -8.29, 1, 1, 19, -7.91, -10.33, 1, 1, 19, -13.88, -5.51, 1, 1, 19, -13.02, 1.02, 1, 1, 19, -9.99, 7.29, 1, 1, 19, 2.67, 9.72, 1, 2, 19, 4.72, 7.6, 0.9975, 20, -10.68, 15.95, 0.0025, 2, 19, 7.85, 6.55, 0.97264, 20, -9.45, 12.89, 0.02736, 2, 19, 14.14, 8.53, 0.69228, 20, -3.86, 9.39, 0.30772, 2, 19, 21.55, 11.37, 0.03998, 20, 3.11, 5.59, 0.96002, 1, 20, 10.28, 8.44, 1, 1, 20, 19.89, 10.98, 1, 1, 20, 27.68, 6.33, 1, 1, 20, 28.22, -2.35, 1, 1, 20, 28.25, -9.5, 1, 1, 20, 24.7, -15.86, 1, 2, 19, 42.4, 9.95, 0.00018, 20, 15.55, -11.2, 0.99982, 2, 19, 34.32, 3.58, 0.15128, 20, 5.47, -9.18, 0.84872, 1, 20, -2.94, -14.27, 1, 2, 19, 26.48, -11.52, 0.008, 20, -11.11, -13.01, 0.992, 1, 20, -13.76, -9.79, 1, 1, 20, -19.05, -14.25, 1, 1, 20, -25, -20.15, 1, 1, 20, -28.5, -12.45, 1, 1, 20, -28.23, -4.85, 1, 1, 20, -22.31, -0.2, 1, 1, 20, -19.3, 0.69, 1, 1, 19, 7.31, -5, 1, 1, 19, -4.36, 0.84, 1, 2, 19, 7.79, 0.4, 0.9995, 20, -14.16, 8.95, 0.0005, 2, 19, 13.54, 1.05, 0.9833, 20, -9.95, 4.99, 0.0167, 1, 19, 21.77, -0.41, 1, 2, 19, 27.57, 7.53, 0.01417, 20, 4.1, -1.48, 0.98583, 1, 20, 13.1, -0.04, 1, 1, 20, 21.41, -0.98, 1, 2, 19, 16.05, -9.65, 0.008, 20, -16.46, -3.86, 0.992, 1, 20, -22.82, -9.39, 1, 1, 20, -24.56, -14.44, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 8, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 64, 72, 72, 74, 74, 76], "width": 59, "height": 50}}, "j3": {"j3": {"x": 1.64, "y": 1.53, "rotation": 94.05, "width": 76, "height": 76}}, "j4": {"j4": {"x": 6.69, "y": -1.39, "rotation": 70.3, "width": 78, "height": 79}}, "b2": {"b2": {"type": "mesh", "uvs": [0.46882, 0, 0.59785, 0.01445, 0.71094, 0.07157, 0.80807, 0.18759, 0.88056, 0.29825, 0.91826, 0.30718, 0.9806, 0.27683, 1, 0.34288, 0.99075, 0.43926, 0.9806, 0.53922, 0.9574, 0.6963, 0.90376, 0.84802, 0.80662, 0.94083, 0.69644, 0.93905, 0.6515, 0.98189, 0.58626, 1, 0.45433, 0.98546, 0.32675, 0.96939, 0.24991, 0.9212, 0.13827, 0.90513, 0.04549, 0.8266, 0.01794, 0.78197, 0, 0.63204, 0.00054, 0.47675, 0.01504, 0.33574, 0.08173, 0.18045, 0.19337, 0.08228, 0.3253, 0.01445, 0.37335, 0.17437, 0.29565, 0.36568, 0.26823, 0.57949, 0.28194, 0.77079, 0.62462, 0.19722, 0.63495, 0.3669, 0.64012, 0.55355, 0.61773, 0.75293, 0.59189, 0.8611, 0.14741, 0.2057, 0.11468, 0.34993, 0.09918, 0.55779, 0.11124, 0.70414, 0.13708, 0.81019, 0.77359, 0.33005, 0.80058, 0.53565, 0.77528, 0.75788, 0.90348, 0.49204, 0.88998, 0.73088], "triangles": [44, 43, 46, 22, 23, 39, 44, 46, 11, 18, 19, 41, 20, 41, 19, 31, 18, 41, 16, 17, 31, 18, 31, 17, 14, 36, 13, 36, 16, 31, 15, 16, 36, 14, 15, 36, 12, 13, 44, 11, 12, 44, 41, 40, 31, 20, 21, 40, 41, 20, 40, 36, 31, 35, 36, 35, 13, 13, 35, 44, 11, 46, 10, 21, 22, 40, 31, 30, 35, 40, 30, 31, 35, 34, 44, 22, 39, 40, 37, 25, 26, 38, 25, 37, 24, 25, 38, 28, 37, 26, 32, 1, 2, 32, 2, 3, 42, 32, 3, 42, 3, 4, 5, 6, 7, 33, 32, 42, 8, 5, 7, 45, 4, 5, 45, 5, 8, 42, 4, 45, 43, 42, 45, 33, 42, 43, 9, 45, 8, 34, 33, 43, 32, 28, 0, 32, 0, 1, 10, 45, 9, 46, 43, 45, 10, 46, 45, 33, 28, 32, 44, 34, 43, 28, 27, 0, 26, 27, 28, 38, 23, 24, 39, 23, 38, 30, 38, 29, 39, 38, 30, 40, 39, 30, 34, 29, 33, 33, 29, 28, 34, 30, 29, 35, 30, 34, 38, 37, 29, 29, 37, 28], "vertices": [6, 4, 63.5, -21.13, 0.06336, 5, 15.6, -26.83, 0.31529, 6, 24.15, 45.44, 0.14537, 7, 40.35, -58.98, 0.00014, 25, 1.79, -55.33, 0.20384, 26, -64.01, -38.31, 0.272, 6, 4, 66.45, -50.65, 0.03323, 5, 9.84, -55.93, 0.11191, 6, 27.1, 15.92, 0.44884, 7, 43.3, -88.5, 7e-05, 25, -3.98, -84.44, 0.13395, 26, -69.77, -67.41, 0.272, 6, 3, 117.3, -70.34, 0.00015, 5, -3.44, -80.58, 0.00983, 6, 21.57, -11.52, 0.62483, 7, 37.77, -115.94, 1e-05, 25, -17.25, -109.08, 0.09318, 26, -83.05, -92.05, 0.272, 4, 3, 103.51, -98.09, 0.0338, 6, 4.59, -37.45, 0.63014, 25, -41.04, -128.94, 0.06406, 26, -106.84, -111.92, 0.272, 4, 3, 88.94, -120.16, 0.08966, 6, -12.48, -57.65, 0.57428, 25, -63.25, -143.3, 0.06406, 26, -129.05, -126.27, 0.272, 4, 3, 89.99, -128.89, 0.10066, 6, -12.47, -66.44, 0.56328, 25, -65.81, -151.71, 0.06406, 26, -131.6, -134.68, 0.272, 4, 3, 99.7, -140.77, 0.10315, 6, -4.23, -79.39, 0.56079, 25, -61.69, -166.49, 0.06406, 26, -127.48, -149.47, 0.272, 4, 3, 89.35, -148.74, 0.10697, 6, -15.45, -86.07, 0.55696, 25, -74.37, -169.63, 0.06406, 26, -140.16, -152.6, 0.272, 4, 3, 71.63, -152.17, 0.1261, 6, -33.46, -87.39, 0.53783, 25, -91.98, -165.64, 0.06406, 26, -157.77, -148.62, 0.272, 5, 3, 53.21, -155.61, 0.0719, 6, -52.15, -88.62, 0.22555, 2, 139.54, 13.2, 0.36649, 25, -110.22, -161.39, 0.06406, 26, -176.02, -144.36, 0.272, 5, 3, 23.76, -159.44, 0.09904, 6, -81.85, -88.94, 0.19841, 2, 134.98, -16.14, 0.36649, 25, -138.72, -153.04, 0.06406, 26, -204.52, -136.02, 0.272, 3, 2, 123.41, -44.67, 0.66394, 25, -165.5, -137.87, 0.06406, 26, -231.3, -120.85, 0.272, 3, 2, 101.61, -62.49, 0.66394, 25, -180.34, -113.94, 0.06406, 26, -246.14, -96.92, 0.272, 3, 2, 76.38, -62.8, 0.63482, 25, -177.37, -88.89, 0.09318, 26, -243.17, -71.86, 0.272, 3, 2, 66.3, -71.03, 0.63482, 25, -184.22, -77.82, 0.09318, 26, -250.01, -60.79, 0.272, 3, 2, 51.45, -74.77, 0.59405, 25, -186, -62.61, 0.13395, 26, -251.8, -45.58, 0.272, 3, 2, 21.18, -72.84, 0.59405, 25, -180.15, -32.84, 0.13395, 26, -245.95, -15.82, 0.272, 3, 2, -8.11, -70.59, 0.52416, 25, -174.12, -4.1, 0.20384, 26, -239.92, 12.92, 0.272, 3, 2, -25.93, -62.08, 0.52416, 25, -163.36, 12.46, 0.20384, 26, -229.16, 29.49, 0.272, 3, 2, -51.56, -59.74, 0.59405, 25, -157.72, 37.57, 0.13395, 26, -223.51, 54.6, 0.272, 3, 2, -73.17, -45.68, 0.63482, 25, -140.96, 57.17, 0.09318, 26, -206.76, 74.2, 0.272, 3, 2, -79.69, -37.54, 0.63482, 25, -132.05, 62.58, 0.09318, 26, -197.84, 79.6, 0.272, 5, 3, -31.51, 53.06, 0.25191, 7, -95.41, 24.19, 0.03249, 2, -84.5, -9.77, 0.35042, 25, -103.89, 63.74, 0.09318, 26, -169.68, 80.77, 0.272, 5, 3, -3.96, 61.73, 0.20257, 7, -67.02, 29.53, 0.08182, 2, -85.11, 19.11, 0.35042, 25, -75.17, 60.6, 0.09318, 26, -140.97, 77.62, 0.272, 5, 3, 22.04, 66.54, 0.24745, 7, -40.64, 31.24, 0.26548, 2, -82.46, 45.41, 0.12188, 25, -49.44, 54.55, 0.09318, 26, -115.23, 71.57, 0.272, 5, 3, 54.2, 60.77, 0.09311, 7, -9.39, 21.71, 0.53663, 2, -67.93, 74.68, 0.00508, 25, -22.31, 36.34, 0.09318, 26, -88.1, 53.36, 0.272, 6, 4, 36.53, 37.92, 0.03533, 5, 6.99, 37.51, 0.05271, 6, -2.82, 104.48, 2e-05, 7, 13.38, 0.06, 0.50599, 25, -6.83, 9, 0.13395, 26, -72.62, 26.03, 0.272, 6, 4, 54.64, 10.64, 0.01105, 5, 16.37, 6.14, 0.49251, 6, 15.29, 77.2, 0.00018, 7, 31.49, -27.22, 0.02042, 25, 2.56, -22.36, 0.20384, 26, -63.24, -5.34, 0.272, 3, 4, 27.51, -5.8, 0.69958, 6, -11.84, 60.77, 0.02042, 25, -28.18, -30.19, 0.28, 4, 3, 36.27, 3.63, 0.69131, 4, -10.8, 4.94, 0.014, 7, -33.95, -32.92, 0.01468, 25, -61.7, -8.77, 0.28, 3, 3, -3.52, -2.48, 0.71666, 6, -90.38, 70.14, 0.00334, 25, -100.59, 1.63, 0.28, 2, 2, -19.3, -33.93, 0.72, 25, -136.31, 2.24, 0.28, 6, 3, 89.03, -58.61, 0.01344, 4, 34.23, -63.1, 0.03676, 5, -24.61, -58.47, 0.00592, 6, -5.12, 3.46, 0.75987, 7, 11.08, -100.96, 0, 25, -38.43, -86.97, 0.184, 4, 3, 59.68, -70.46, 0.17661, 4, 3.69, -71.4, 0.0672, 6, -35.66, -4.84, 0.5722, 25, -70.06, -86.02, 0.184, 5, 3, 26.97, -82.15, 0.29716, 4, -30.18, -79.13, 0.02004, 6, -69.53, -12.57, 0.36171, 2, 61.67, 8.55, 0.13709, 25, -104.71, -83.57, 0.184, 5, 3, -9.92, -88.54, 0.21679, 4, -67.56, -81.12, 0.00103, 6, -106.91, -14.55, 0.14774, 2, 57.48, -28.65, 0.45043, 25, -141.06, -74.58, 0.184, 2, 2, 52.08, -48.91, 0.816, 25, -160.45, -66.59, 0.184, 4, 3, 54.3, 45.01, 0.09601, 4, 12, 43.9, 0.01001, 7, -11.15, 6.05, 0.70997, 25, -28.56, 21.87, 0.184, 5, 3, 26.46, 44, 0.38194, 4, -15.77, 46.19, 0.0049, 7, -38.91, 8.33, 0.37041, 2, -59.58, 43.35, 0.05875, 25, -54.45, 32.13, 0.184, 4, 3, -11.45, 35.63, 0.37304, 7, -77.55, 4.5, 0.06434, 2, -62.15, 4.62, 0.37862, 25, -92.53, 39.71, 0.184, 5, 3, -36.54, 24.72, 0.34991, 6, -119.96, 101.06, 0.00018, 7, -103.76, -3.36, 0.01548, 2, -58.7, -22.53, 0.45043, 25, -119.89, 39.82, 0.184, 2, 2, -52.28, -42.1, 0.816, 25, -140.13, 36, 0.184, 4, 3, 75.86, -98.62, 0.11343, 4, 16.43, -101.28, 0.00395, 6, -22.92, -34.71, 0.75462, 25, -66.57, -118.31, 0.128, 5, 3, 41.31, -116.14, 0.2306, 4, -19.95, -114.58, 0.00283, 6, -59.3, -48.02, 0.56183, 2, 98.32, 12.81, 0.07674, 25, -105.25, -120.46, 0.128, 5, 3, 0.17, -123.19, 0.17388, 4, -61.64, -116.72, 0.00015, 6, -100.99, -50.15, 0.21663, 2, 93.57, -28.65, 0.48134, 25, -145.75, -110.37, 0.128, 5, 3, 56.2, -136.12, 0.18327, 4, -7.53, -136.19, 4e-05, 6, -46.88, -69.62, 0.62654, 2, 121.67, 21.52, 0.10214, 25, -99.65, -144.74, 0.088, 4, 3, 12.94, -146.68, 0.15098, 6, -91.08, -75, 0.25759, 2, 119.71, -22.97, 0.50342, 25, -143.5, -137.02, 0.088], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 0, 56, 56, 58, 58, 60, 60, 62, 2, 64, 64, 66, 66, 68, 68, 70, 70, 72, 52, 74, 74, 76, 76, 78, 78, 80, 80, 82, 54, 56, 84, 86, 86, 88, 90, 92], "width": 229, "height": 186}}, "sd": {"sd": {"x": -1.3, "y": -2.3, "scaleX": 0.6577, "scaleY": 0.6577, "width": 125, "height": 50}}, "tou1": {"tou1": {"x": 53.64, "y": 9.75, "rotation": -96.01, "width": 80, "height": 52}}, "tou2": {"tou2": {"type": "mesh", "uvs": [0.21074, 0.22739, 0.21323, 0.15597, 0.25811, 0.07986, 0.3192, 0.01502, 0.36657, 0, 0.40148, 0.0216, 0.40771, 0.05542, 0.41021, 0.09019, 0.47877, 0.04791, 0.57352, 0.02817, 0.68323, 0.01784, 0.68572, 0.08174, 0.66453, 0.13154, 0.6371, 0.15879, 0.73185, 0.13436, 0.81503, 0.13661, 0.90463, 0.14189, 0.85563, 0.24636, 0.80383, 0.30546, 0.75623, 0.34028, 0.82491, 0.39833, 0.86547, 0.47829, 0.88887, 0.55472, 0.93255, 0.58294, 1, 0.59588, 0.98715, 0.64879, 0.95127, 0.70053, 0.86547, 0.73463, 0.75003, 0.7311, 0.74847, 0.80754, 0.72195, 0.88279, 0.61431, 0.8875, 0.48639, 0.86045, 0.45675, 0.83576, 0.41775, 0.89573, 0.32883, 0.95334, 0.22431, 1, 0.13539, 0.95687, 0.03399, 0.90513, 0, 0.87691, 0.03711, 0.80989, 0.07455, 0.71347, 0.10731, 0.63468, 0.14007, 0.56178, 0.11355, 0.5606, 0.06207, 0.57824, 0.03867, 0.51592, 0.06987, 0.44301, 0.11667, 0.40421, 0.12291, 0.35953, 0.15255, 0.33483, 0.12603, 0.29133, 0.12603, 0.22548, 0.13851, 0.19255, 0.18063, 0.20431, 0.07863, 0.52818, 0.1285, 0.49252, 0.22822, 0.46843, 0.28895, 0.45783, 0.36758, 0.4612, 0.38995, 0.51131, 0.381, 0.55131, 0.32794, 0.56046, 0.25571, 0.54986, 0.18986, 0.55323, 0.16685, 0.38266, 0.23142, 0.37254, 0.29598, 0.39374, 0.33945, 0.42362, 0.46617, 0.35901, 0.49904, 0.4189, 0.51959, 0.49943, 0.52781, 0.62747, 0.54425, 0.74724, 0.60315, 0.81126, 0.66068, 0.8505, 0.62233, 0.35591, 0.65794, 0.39412, 0.69356, 0.47052, 0.73739, 0.59236, 0.80589, 0.65225, 0.89355, 0.65638, 0.53877, 0.28966, 0.64698, 0.24216, 0.74424, 0.19983, 0.82643, 0.17505, 0.32233, 0.34748, 0.37439, 0.25972, 0.44562, 0.18124, 0.53603, 0.11413, 0.62233, 0.05837, 0.30316, 0.25249, 0.30727, 0.16782, 0.33055, 0.08315, 0.34973, 0.02843, 0.23055, 0.33303, 0.18261, 0.2845, 0.15247, 0.22977, 0.25635, 0.33219, 0.40044, 0.45119, 0.58327, 0.40803, 0.69277, 0.69804, 0.6345, 0.61172, 0.23612, 0.61004, 0.21402, 0.70242, 0.19996, 0.79101, 0.19594, 0.8743, 0.20599, 0.9485, 0.10855, 0.87127, 0.11658, 0.78116, 0.13667, 0.69409, 0.16279, 0.61534, 0.32352, 0.61837, 0.32452, 0.71832, 0.33557, 0.80918, 0.33156, 0.89247, 0.45511, 0.72589, 0.43502, 0.61307, 0.42799, 0.55476], "triangles": [79, 21, 22, 104, 109, 110, 104, 111, 103, 76, 82, 83, 105, 104, 113, 66, 65, 95, 83, 82, 88, 95, 96, 98, 67, 57, 66, 69, 87, 82, 67, 66, 86, 98, 96, 91, 87, 88, 82, 86, 87, 69, 66, 95, 98, 86, 91, 87, 98, 91, 86, 108, 109, 105, 106, 108, 105, 108, 40, 109, 66, 98, 86, 91, 92, 87, 87, 92, 88, 91, 0, 92, 105, 113, 114, 76, 83, 19, 43, 111, 42, 110, 42, 111, 110, 111, 104, 105, 109, 104, 104, 103, 112, 51, 52, 97, 51, 97, 96, 96, 97, 0, 97, 52, 53, 54, 97, 53, 97, 54, 0, 50, 51, 96, 91, 96, 0, 92, 93, 7, 92, 2, 93, 93, 94, 6, 94, 4, 5, 1, 2, 92, 2, 3, 93, 93, 3, 94, 94, 5, 6, 94, 3, 4, 0, 1, 92, 93, 6, 7, 19, 83, 18, 17, 85, 16, 84, 15, 85, 84, 14, 15, 84, 85, 17, 85, 15, 16, 18, 84, 17, 83, 84, 18, 13, 14, 84, 83, 13, 84, 88, 7, 89, 13, 88, 89, 90, 10, 11, 89, 8, 9, 89, 9, 90, 89, 90, 12, 12, 90, 11, 90, 9, 10, 7, 8, 89, 13, 89, 12, 38, 39, 108, 39, 40, 108, 38, 108, 37, 41, 42, 110, 109, 41, 110, 40, 41, 109, 35, 107, 115, 107, 106, 115, 37, 107, 36, 37, 106, 107, 35, 115, 34, 36, 107, 35, 34, 115, 114, 115, 106, 114, 37, 108, 106, 33, 34, 114, 106, 105, 114, 26, 81, 25, 27, 81, 26, 25, 81, 23, 25, 23, 24, 23, 81, 22, 101, 102, 79, 81, 80, 22, 101, 79, 80, 28, 80, 27, 28, 101, 80, 27, 80, 81, 80, 79, 22, 32, 74, 31, 28, 29, 74, 31, 75, 30, 31, 74, 75, 30, 75, 29, 75, 74, 29, 32, 33, 74, 28, 74, 101, 33, 73, 74, 101, 73, 72, 74, 73, 101, 57, 65, 66, 111, 43, 64, 55, 46, 56, 44, 55, 56, 46, 47, 56, 64, 43, 56, 64, 56, 57, 78, 20, 21, 20, 77, 19, 78, 100, 77, 77, 76, 19, 100, 76, 77, 102, 71, 78, 101, 72, 102, 56, 48, 57, 92, 7, 88, 83, 88, 13, 56, 43, 44, 61, 112, 62, 48, 49, 65, 58, 67, 68, 102, 72, 71, 68, 86, 69, 71, 70, 100, 49, 50, 65, 70, 99, 69, 99, 68, 69, 59, 68, 99, 117, 61, 118, 72, 117, 118, 116, 117, 72, 114, 113, 116, 116, 72, 73, 113, 117, 116, 33, 116, 73, 114, 116, 33, 61, 60, 118, 118, 71, 72, 118, 60, 71, 60, 99, 71, 99, 70, 71, 60, 59, 99, 58, 68, 59, 65, 50, 95, 61, 117, 112, 60, 62, 58, 70, 69, 100, 61, 62, 60, 59, 60, 58, 63, 58, 62, 58, 57, 67, 67, 86, 68, 69, 82, 76, 50, 96, 95, 63, 57, 58, 48, 65, 57, 112, 103, 62, 117, 113, 112, 63, 64, 57, 62, 103, 63, 104, 112, 113, 100, 69, 76, 100, 78, 71, 20, 78, 77, 79, 78, 21, 102, 78, 79, 103, 64, 63, 45, 46, 55, 45, 55, 44, 47, 48, 56, 111, 64, 103], "vertices": [2, 5, 102.48, 27.12, 0.848, 24, 10.94, -8.74, 0.152, 3, 21, -3.58, 12.98, 0.72345, 25, 102.76, -3.24, 0.09514, 26, 36.96, 13.78, 0.18141, 4, 30, -138.03, 58.98, 0, 21, 12.8, 10.47, 0.80256, 25, 117.12, -11.52, 0.088, 26, 51.32, 5.5, 0.10944, 4, 30, -141.05, 74.52, 0, 21, 27.66, 5.03, 0.672, 25, 128.99, -21.98, 0.2, 26, 63.2, -4.96, 0.128, 3, 21, 32.41, -1.04, 0.77875, 25, 131.22, -29.36, 0.168, 26, 65.42, -12.34, 0.05325, 3, 21, 29.64, -7.22, 0.78596, 25, 126.4, -34.12, 0.09706, 26, 60.6, -17.1, 0.11699, 3, 21, 23.4, -9.9, 0.76447, 25, 119.6, -34.35, 0.12069, 26, 53.81, -17.32, 0.11483, 1, 5, 126.5, -5.49, 1, 3, 22, 10.8, 15.37, 0.77598, 21, 27.64, -19.79, 0.05602, 26, 54.18, -28.08, 0.168, 2, 22, 23.93, 8.66, 0.928, 26, 56.6, -42.62, 0.072, 4, 30, -102.96, 113.65, 0, 22, 37.45, -0.94, 0.50112, 25, 122.72, -76.23, 0.21888, 26, 56.92, -59.21, 0.28, 4, 30, -93.5, 105.14, 0, 22, 29.13, -10.57, 0.70012, 25, 110.03, -75.27, 0.07326, 26, 44.24, -58.25, 0.22662, 4, 30, -88.52, 96, 0, 22, 20.09, -15.73, 0.70503, 25, 100.51, -71.07, 0.13572, 26, 34.71, -54.05, 0.15925, 1, 5, 109.36, -37.91, 1, 3, 30, -81.15, 102.93, 0, 23, 11.36, 14.56, 0.7101, 26, 33.1, -64.03, 0.2899, 3, 30, -72.21, 111.65, 0, 23, 22.4, 8.73, 0.71992, 26, 31.35, -76.39, 0.28008, 4, 30, -62.18, 120.65, 0, 23, 34.05, 1.95, 0.50064, 25, 94.69, -106.67, 0.256, 26, 28.89, -89.65, 0.24336, 4, 5, 88.6, -68.68, 0.00158, 23, 18.4, -13.58, 0.53219, 25, 74.78, -97.19, 0.23325, 26, 8.99, -80.16, 0.23297, 4, 5, 77.72, -59.73, 0.02367, 23, 6.3, -20.8, 0.58395, 25, 63.9, -88.23, 0.13496, 26, -1.89, -71.2, 0.25743, 3, 5, 71.57, -51.9, 0.13984, 25, 57.76, -80.4, 0.33572, 26, -8.04, -63.38, 0.52444, 3, 5, 59.01, -60.94, 0.20352, 25, 45.19, -89.44, 0.20413, 26, -20.61, -72.41, 0.59235, 3, 5, 42.54, -65.32, 0.1993, 28, -1.34, 34.73, 0.0089, 26, -37.07, -76.8, 0.7918, 4, 5, 27.05, -67.22, 0.04777, 30, -4.35, 62.25, 0, 28, 1.01, 19.3, 0.27608, 26, -52.56, -78.7, 0.67615, 5, 5, 20.78, -73.15, 0.00138, 30, 4.24, 63.11, 0, 27, 9.96, 14.91, 0.26885, 28, 7.12, 13.2, 0.4482, 26, -58.83, -84.63, 0.28157, 4, 30, 13.08, 68.66, 0, 27, 19.98, 17.83, 0.03166, 28, 17.02, 9.88, 0.68834, 26, -62.45, -94.42, 0.28, 3, 30, 19.37, 60, 0, 28, 14.3, -0.48, 0.72, 26, -72.72, -91.4, 0.28, 5, 29, 25.6, 52.56, 0.00176, 30, 23.11, 49, 0.00206, 27, 24.27, -3.82, 0.06379, 28, 8.16, -10.34, 0.65239, 26, -82.4, -84.97, 0.28, 5, 29, 24.44, 38.06, 0.03555, 30, 19.14, 35, 0.02579, 27, 16.63, -16.21, 0.46335, 28, -5.18, -16.14, 0.1953, 26, -87.8, -71.46, 0.28, 6, 5, -5.68, -42.83, 0.00062, 29, 14.58, 23.81, 0.1894, 30, 6.68, 22.95, 0.19199, 27, 1.36, -24.4, 0.33554, 28, -22.4, -14.14, 0.00244, 26, -85.29, -54.31, 0.28, 4, 29, 27.3, 15.47, 0.04757, 30, 17.53, 12.29, 0.62008, 27, 8.88, -37.62, 0.05235, 26, -100.39, -52.49, 0.28, 3, 30, 25.62, -0.93, 0.648, 25, -49.07, -63.99, 0.072, 26, -114.87, -46.96, 0.28, 6, 5, -34.5, -19.33, 0.01266, 29, 29.96, -10.05, 0.00675, 30, 15.16, -13.26, 0.58351, 32, 7.68, 57.84, 0.00764, 25, -48.31, -47.83, 0.152, 26, -114.11, -30.81, 0.23744, 7, 5, -27.14, -0.81, 0.12841, 29, 15.14, -23.37, 0.1313, 30, -1.98, -23.43, 0.17656, 31, 15.05, 39.02, 0.00034, 32, 3.01, 38.47, 0.08755, 25, -40.95, -29.31, 0.272, 26, -106.75, -12.29, 0.20384, 7, 5, -21.78, 3.1, 0.1921, 29, 8.6, -24.49, 0.10221, 30, -8.6, -23.26, 0.07022, 31, 10.99, 33.78, 0.00639, 32, -1.73, 33.85, 0.19356, 25, -35.6, -25.41, 0.216, 26, -101.39, -8.38, 0.21952, 6, 5, -33.04, 10.16, 0.11216, 29, 15.55, -35.82, 0.0237, 30, -4, -35.73, 0.00816, 32, 10.41, 28.44, 0.4435, 25, -46.86, -18.34, 0.184, 26, -112.65, -1.31, 0.22848, 6, 5, -43.05, 24.63, 0.02139, 29, 18.09, -53.23, 0.00244, 30, -4.91, -53.29, 5e-05, 32, 22.36, 15.54, 0.48753, 25, -56.86, -3.87, 0.24, 26, -122.66, 13.15, 0.2486, 5, 30, -9.01, -71.05, 0, 32, 32.22, 0.21, 0.40066, 34, 17.25, 39.13, 0.00173, 25, -64.45, 12.69, 0.41504, 26, -130.25, 29.71, 0.18257, 6, 30, -24.42, -74.78, 0, 31, 43.22, -9.42, 0.05219, 32, 24.14, -13.43, 0.54095, 34, 19.26, 23.4, 0.20046, 25, -54.52, 25.06, 0.1984, 26, -120.32, 42.08, 0.008, 6, 30, -42.37, -78.68, 0, 31, 35.76, -26.21, 0.0192, 32, 14.41, -29.01, 0.04967, 34, 21.17, 5.13, 0.57914, 25, -42.69, 39.1, 0.072, 26, -108.49, 56.13, 0.28, 5, 30, -49.95, -78.5, 0, 32, 8.99, -34.32, 0.00101, 34, 20.16, -2.38, 0.54085, 25, -36.57, 43.59, 0.224, 26, -102.37, 60.61, 0.23414, 4, 33, 18.84, -7.88, 0.06476, 34, 6.37, -6.71, 0.46035, 25, -23.89, 36.65, 0.248, 26, -89.68, 53.68, 0.2269, 5, 5, 8.42, 57.56, 0.01075, 30, -65.78, -47.96, 0, 33, -0.91, -10.97, 0.31808, 25, -5.4, 29.06, 0.33578, 26, -71.19, 46.09, 0.33538, 4, 5, 23.5, 51.04, 0.16544, 30, -73.73, -33.58, 0, 25, 9.68, 22.53, 0.39833, 26, -56.11, 39.56, 0.43623, 4, 5, 37.41, 44.63, 0.45508, 30, -80.84, -20.01, 0, 25, 23.6, 16.13, 0.30685, 26, -42.2, 33.15, 0.23807, 4, 5, 38.06, 48.56, 0.3872, 30, -83.76, -22.73, 0, 25, 24.25, 20.06, 0.37103, 26, -41.55, 37.08, 0.24177, 4, 5, 35.38, 56.61, 0.39001, 30, -86.55, -30.74, 0, 25, 21.56, 28.11, 0.35896, 26, -44.23, 45.13, 0.25103, 4, 5, 48.08, 58.8, 0.41481, 30, -97.94, -24.72, 0, 25, 34.26, 30.3, 0.30078, 26, -31.53, 47.32, 0.28441, 4, 5, 62.02, 52.63, 0.25313, 30, -105.21, -11.32, 0, 25, 48.2, 24.13, 0.27548, 26, -17.59, 41.15, 0.47138, 4, 5, 68.96, 44.84, 0.46866, 30, -105.96, -0.92, 0, 25, 55.15, 16.34, 0.25592, 26, -10.65, 33.36, 0.27542, 2, 5, 77.71, 42.98, 1, 30, -111.75, 5.9, 0, 3, 5, 82.13, 38.04, 0.96307, 30, -112.24, 12.51, 0, 26, 2.52, 26.56, 0.03693, 3, 5, 91.16, 41.09, 0.36087, 24, 5.42, 8.37, 0.5623, 26, 11.55, 29.61, 0.07683, 3, 5, 104.19, 39.72, 0.488, 24, 17.08, 2.38, 0.36864, 26, 24.58, 28.24, 0.14336, 5, 5, 110.51, 37.17, 0.488, 30, -134.18, 30.52, 0, 24, 22.05, -2.27, 0.29491, 25, 96.69, 8.67, 0.07373, 26, 30.9, 25.69, 0.14336, 4, 5, 107.52, 31.13, 0.49447, 30, -128.13, 33.48, 0, 24, 17.09, -6.82, 0.36441, 26, 27.91, 19.65, 0.14112, 3, 5, 45.03, 53.1, 0.8241, 30, -92.04, -22.07, 0, 25, 31.21, 24.59, 0.1759, 3, 5, 51.3, 44.91, 0.58339, 30, -92.02, -11.76, 0, 25, 37.48, 16.41, 0.41661, 3, 5, 54.5, 29.54, 0.473, 30, -85.16, 2.38, 0, 25, 40.69, 1.03, 0.527, 3, 5, 55.65, 20.26, 0.68558, 30, -80.4, 10.42, 0, 25, 41.83, -8.25, 0.31442, 3, 5, 53.74, 8.6, 0.73576, 30, -71.78, 18.5, 0, 25, 39.93, -19.91, 0.26424, 4, 5, 43.48, 6.3, 0.78698, 29, -51.29, 1.62, 0.00025, 30, -62.25, 14.04, 0, 25, 29.66, -22.2, 0.21277, 3, 5, 35.7, 8.47, 0.80657, 30, -57.41, 7.58, 0, 25, 21.88, -20.03, 0.19343, 3, 5, 34.72, 16.58, 0.72649, 30, -61.59, 0.56, 0, 25, 20.91, -11.93, 0.27351, 3, 5, 37.95, 27.13, 0.67716, 30, -70.59, -5.82, 0, 25, 24.14, -1.37, 0.32284, 3, 5, 38.32, 37.02, 0.61053, 30, -76.92, -13.43, 0, 25, 24.5, 8.52, 0.38947, 3, 5, 72.44, 36.9, 0.69638, 30, -103.87, 7.49, 0, 25, 58.62, 8.4, 0.30362, 4, 5, 73.43, 27.06, 0.34403, 30, -98.64, 15.89, 0, 24, -16.17, 1.69, 0.0264, 25, 59.61, -1.44, 0.62956, 3, 5, 68.22, 17.87, 0.53633, 30, -88.91, 19.99, 0, 25, 54.4, -10.63, 0.46367, 3, 5, 61.62, 12.01, 0.67575, 30, -80.1, 20.6, 0, 25, 47.81, -16.49, 0.32425, 2, 5, 72.42, -8.24, 0.71822, 25, 58.6, -36.74, 0.28178, 3, 5, 60.05, -11.9, 0.78256, 21, -42.78, -42.15, 0.01214, 25, 46.24, -40.4, 0.2053, 3, 5, 43.79, -13.28, 0.84525, 30, -50.54, 29.75, 0, 25, 29.97, -41.79, 0.15475, 3, 5, 18.32, -11.84, 0.68564, 29, -20.69, 6.71, 0.204, 25, 4.51, -40.35, 0.11036, 4, 5, -5.64, -11.8, 0.12369, 29, 0.76, -3.97, 0.76868, 32, -19.82, 46.31, 0.00762, 25, -19.46, -40.3, 0.1, 5, 5, -19.23, -19.25, 0.01835, 29, 16.25, -3.33, 0.10127, 30, 3.02, -4, 0.7721, 32, -7.41, 55.61, 0.00828, 25, -33.05, -47.76, 0.1, 4, 5, -27.9, -27.02, 0.00136, 30, 14.63, -3.14, 0.89771, 32, 0.07, 64.52, 0.00094, 25, -41.72, -55.52, 0.1, 2, 5, 70.58, -31.6, 0.6375, 25, 56.77, -60.1, 0.3625, 1, 5, 62.46, -36.12, 1, 1, 5, 46.78, -39.84, 1, 3, 5, 21.98, -43.84, 0.13, 30, -14.61, 40.63, 0.00636, 27, -14.3, -1.58, 0.86364, 4, 5, 9.05, -52.81, 0.0039, 29, 5.81, 39.29, 0.06608, 30, 1.11, 39.84, 0.02447, 27, 0.6, -6.63, 0.90554, 4, 29, 13.55, 49.96, 0.00119, 30, 10.78, 48.79, 0.00088, 27, 12.35, -0.65, 0.40511, 28, 0.19, -0.93, 0.59282, 2, 5, 85, -20.51, 0.34133, 25, 71.19, -49.02, 0.65867, 5, 5, 92.71, -37.65, 0.02529, 22, 3.28, -30.18, 0.13594, 23, -9.43, 0.76, 0.63193, 21, -3.01, -54.3, 0.00041, 25, 78.89, -66.15, 0.20643, 3, 30, -70.43, 95.28, 0, 23, 7.37, 2.01, 0.79882, 25, 85.74, -81.54, 0.20118, 3, 30, -65.49, 107.61, 0, 23, 20.62, 1.1, 0.79999, 25, 89.35, -94.32, 0.20001, 3, 5, 76.96, 12.98, 0.30342, 30, -92.84, 29.2, 0, 25, 63.14, -15.53, 0.69658, 4, 5, 93.51, 3.38, 0.13943, 30, -100.09, 46.9, 0, 22, -29.23, -5.12, 0.0175, 25, 79.7, -25.12, 0.84307, 4, 5, 107.92, -8.88, 0.01427, 30, -104.02, 65.41, 0, 22, -10.8, -0.83, 0.59296, 25, 94.11, -37.38, 0.39277, 5, 5, 119.79, -23.76, 1e-05, 30, -104.33, 84.44, 0, 22, 8.22, -0.15, 0.69484, 23, -13.35, 30.94, 0.00114, 25, 105.97, -52.27, 0.304, 5, 30, -103.43, 101.47, 0, 22, 25.26, -0.72, 0.5339, 23, 3.13, 35.31, 0.00063, 25, 115.65, -66.3, 0.23347, 26, 49.86, -49.28, 0.232, 3, 5, 96.06, 13.86, 0.11648, 30, -108.51, 40.16, 0, 25, 82.25, -14.64, 0.88352, 4, 5, 112.75, 11.48, 0.00115, 30, -120.28, 52.24, 0, 21, -2.15, -1.25, 0.78919, 25, 98.94, -17.02, 0.20965, 2, 21, 15.03, -0.18, 0.79963, 25, 115.33, -22.26, 0.20037, 3, 30, -135.96, 75.99, 0, 21, 26.29, -0.09, 0.79992, 25, 125.86, -26.26, 0.20008, 3, 5, 81.26, 26.37, 0.41746, 30, -104.42, 21.22, 0, 25, 67.45, -2.14, 0.58254, 2, 24, 2.75, 0.2, 0.9, 25, 77.8, 4.01, 0.1, 3, 30, -127.38, 26.93, 0, 24, 14.51, -0.75, 0.89982, 25, 89.11, 7.36, 0.10018, 3, 5, 81.02, 22.5, 0.28305, 30, -101.87, 24.14, 0, 25, 67.21, -6, 0.71695, 3, 5, 55.21, 3.49, 0.99937, 29, -60.55, 9.36, 0.00063, 30, -69.82, 23.44, 0, 1, 5, 60.88, -24.69, 1, 4, 5, 1.77, -34.98, 0.03561, 29, 4.42, 20.08, 0.61061, 30, -4.01, 21.28, 0.19848, 25, -12.05, -63.48, 0.1553, 4, 5, 19.76, -28.09, 0.68946, 30, -22.47, 26.8, 0.01063, 27, -25.64, -12.74, 0.032, 25, 5.95, -56.59, 0.26791, 3, 5, 26.35, 31.31, 0.47127, 30, -63.95, -16.21, 0, 25, 12.54, 2.8, 0.52873, 3, 5, 8.42, 36.53, 0.19282, 30, -52.93, -31.3, 0, 25, -5.4, 8.03, 0.80718, 3, 31, 9.02, -5.71, 0.63378, 34, -12.23, 9.57, 0.00627, 25, -22.71, 11.97, 0.35995, 6, 30, -30.05, -56.87, 0, 31, 25.44, -3.38, 0.20377, 32, 7.38, -4.97, 0.41173, 33, 20.29, 19.13, 0.00458, 34, 0.84, 19.78, 0.07593, 25, -39.13, 14.3, 0.304, 5, 30, -18.32, -65.96, 0, 31, 39.71, 0.71, 0.00803, 32, 22.08, -2.91, 0.72765, 34, 11.17, 30.43, 0.02432, 25, -53.97, 14.35, 0.24, 6, 30, -39.53, -65.94, 0, 31, 27.16, -16.39, 0.11215, 32, 7.26, -18.09, 0.08623, 34, 8.82, 9.36, 0.42276, 25, -37.16, 27.28, 0.3328, 26, -102.95, 44.3, 0.04606, 3, 31, 9.3, -18.36, 0.00692, 33, 8.59, 0.47, 0.63308, 25, -19.45, 24.2, 0.36, 4, 5, 11.28, 47.89, 0.05005, 30, -62.14, -38.55, 0, 33, -8.37, -4.18, 0.7285, 25, -2.54, 19.39, 0.22145, 3, 5, 26.45, 42.36, 0.50113, 30, -70.78, -24.9, 0, 25, 12.64, 13.85, 0.49887, 3, 5, 23.33, 18.44, 0.72009, 30, -53.7, -7.87, 0, 25, 9.52, -10.06, 0.27991, 4, 5, 3.54, 20.38, 0.56872, 32, -24.35, 13.16, 0.08004, 33, -16.01, 23.37, 0.01732, 25, -10.28, -8.13, 0.33393, 6, 5, -14.62, 20.62, 0.18013, 29, -5.6, -37.01, 0.01333, 30, -24.97, -32.76, 0.00303, 31, 8.99, 14.95, 0.13737, 32, -6.34, 15.49, 0.46615, 25, -28.44, -7.88, 0.2, 5, 5, -31.04, 22.95, 0.07494, 29, 8.08, -46.39, 0.01038, 30, -13.39, -44.64, 0.00166, 32, 10.24, 15.5, 0.71302, 25, -44.86, -5.55, 0.2, 5, 5, -0.01, 1.05, 0.83219, 30, -24.6, -8.35, 0.00021, 31, -10.48, 29.68, 0.00869, 32, -23.57, 32.79, 0.00691, 25, -13.83, -27.45, 0.152, 3, 5, 22.63, 1.7, 0.91093, 30, -42.92, 4.96, 0, 25, 8.81, -26.8, 0.08907, 2, 5, 34.28, 1.53, 1, 30, -52.05, 12.21, 0], "hull": 55, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 0, 108, 90, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 96, 130, 130, 132, 132, 134, 134, 136, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 164, 166, 166, 168, 168, 170, 172, 174, 174, 176, 176, 178, 178, 180, 182, 184, 184, 186, 186, 188, 190, 192, 192, 194, 172, 132, 132, 196, 182, 196, 56, 202, 202, 204, 126, 206, 206, 208, 208, 210, 210, 212, 212, 214, 74, 216, 76, 216, 216, 218, 218, 220, 220, 222, 224, 226, 226, 228, 228, 230, 66, 232, 232, 234, 120, 236], "width": 150, "height": 199}}, "m1": {"m1": {"type": "mesh", "uvs": [0, 0.08324, 0.03787, 0.00507, 0.1761, 0, 0.35921, 0.0124, 0.55129, 0.08568, 0.6985, 0.24201, 0.84391, 0.30551, 0.99829, 0.35681, 0.99111, 0.54489, 0.94264, 0.69633, 0.83672, 0.81358, 0.68054, 0.9064, 0.51539, 0.97723, 0.36998, 1, 0.29997, 0.78671, 0.29279, 0.5571, 0.20662, 0.37391, 0.07377, 0.22247, 0, 0.16384, 0.12619, 0.10845, 0.27829, 0.19469, 0.43884, 0.30391, 0.55714, 0.44763, 0.65854, 0.64884, 0.12787, 0.1837, 0.26928, 0.2997, 0.38365, 0.42986, 0.45019, 0.58831, 0.49386, 0.79204, 0.15074, 0.0592, 0.32958, 0.1073, 0.5001, 0.19784, 0.64775, 0.35064, 0.76628, 0.46947], "triangles": [33, 5, 6, 32, 5, 33, 8, 6, 7, 33, 6, 8, 33, 22, 32, 23, 22, 33, 9, 33, 8, 10, 23, 33, 9, 10, 33, 11, 23, 10, 32, 31, 5, 22, 31, 32, 31, 3, 4, 30, 3, 31, 31, 4, 5, 21, 30, 31, 21, 31, 22, 26, 21, 22, 20, 30, 21, 19, 0, 29, 24, 0, 19, 20, 19, 29, 0, 17, 18, 20, 24, 19, 30, 2, 3, 29, 1, 2, 0, 1, 29, 29, 2, 30, 20, 29, 30, 27, 22, 23, 28, 27, 23, 14, 27, 28, 28, 23, 11, 12, 28, 11, 13, 14, 28, 13, 28, 12, 27, 26, 22, 14, 15, 27, 25, 20, 21, 26, 25, 21, 15, 16, 26, 15, 26, 27, 24, 17, 0, 25, 24, 20, 16, 24, 25, 17, 24, 16, 26, 16, 25], "vertices": [2, 38, -11.7, -0.41, 0.01887, 35, -5.66, 1.64, 0.98113, 1, 35, -2.48, 6.38, 1, 1, 35, 9, 6.58, 1, 2, 35, 24.19, 5.68, 0.44914, 36, -0.77, 6.12, 0.55085, 2, 36, 15.31, 10.07, 0.90941, 37, -10.92, 8.18, 0.09059, 1, 37, 4.57, 8.48, 1, 1, 37, 16.38, 13.09, 1, 1, 37, 28.3, 18.74, 1, 1, 37, 35.07, 9.46, 1, 2, 40, 20.93, 37.87, 0.02132, 37, 37.78, -0.25, 0.97868, 2, 40, 23.71, 26.88, 0.19964, 37, 35.46, -11.34, 0.80036, 3, 40, 23.38, 12.74, 0.65649, 36, 49.27, -28.25, 2e-05, 37, 28.97, -23.91, 0.34349, 3, 39, 41.77, -3.91, 0.00029, 40, 21.52, -1.51, 0.99197, 37, 21.06, -35.91, 0.00774, 2, 39, 36.58, -14.9, 0.06114, 40, 17.69, -13.04, 0.93886, 2, 39, 22.46, -12.98, 0.5417, 40, 3.44, -12.82, 0.4583, 2, 38, 24.99, -9.37, 0.04467, 39, 10.25, -6.1, 0.95533, 1, 38, 12.61, -4.58, 1, 2, 38, -1.74, -3.64, 0.83846, 35, 0.38, -6.91, 0.16154, 2, 38, -8.79, -4.37, 0.13976, 35, -5.71, -3.28, 0.86024, 2, 38, -2.34, 4.54, 0.00292, 35, 4.8, 0, 0.99708, 4, 38, 10.95, 7.76, 0.16663, 39, -9.17, 4.54, 0.22955, 35, 17.37, -5.37, 0.45475, 36, -1.15, -6.87, 0.14907, 3, 39, 3.52, 12.35, 0.29848, 40, -18.41, 10.04, 0.0088, 36, 13.73, -6.11, 0.69272, 4, 39, 16.14, 16.07, 0.11072, 40, -6.32, 15.25, 0.17419, 36, 26.59, -8.92, 0.53132, 37, 3.38, -8.65, 0.18378, 3, 40, 8.36, 17.71, 0.41608, 36, 39.95, -15.47, 0.04977, 37, 17.65, -12.87, 0.53415, 2, 38, 0.49, 0.92, 0.89189, 35, 4.89, -4.59, 0.10811, 4, 38, 14.14, 2.15, 0.4816, 39, -4.12, 0.53, 0.4322, 35, 16.57, -11.77, 0.03894, 36, 1.35, -12.81, 0.04726, 3, 39, 7.63, 4.41, 0.82345, 40, -13.37, 2.65, 0.0167, 36, 13.52, -15.06, 0.15985, 4, 39, 18.75, 4, 0.41798, 40, -2.28, 3.58, 0.48753, 36, 23.08, -20.76, 0.07539, 37, 1.9, -20.91, 0.0191, 3, 40, 10.52, 1.63, 0.98376, 36, 32.35, -29.79, 0.00228, 37, 12.55, -28.27, 0.01395, 1, 35, 6.86, 2.99, 1, 4, 38, 11.24, 14.57, 8e-05, 39, -11.45, 10.97, 0.00117, 35, 21.68, -0.08, 0.55067, 36, -0.06, -0.13, 0.44808, 2, 36, 14.97, 2.02, 0.99181, 37, -9.91, 0.19, 0.00819, 1, 37, 5.48, 0.68, 1, 1, 37, 17.69, 1.26, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 83, "height": 61}}, "m2": {"m2": {"type": "mesh", "uvs": [1, 0.04165, 1, 0.14959, 0.83912, 0.2525, 0.70733, 0.46838, 0.71905, 0.69179, 0.71612, 0.99301, 0.47891, 1, 0.20948, 0.96791, 0.01619, 0.85997, 0, 0.70183, 0.19191, 0.51608, 0.36762, 0.38806, 0.48769, 0.22238, 0.69269, 0.04416, 0.86841, 0, 0.76225, 0.14676, 0.61783, 0.29347, 0.49213, 0.45852, 0.36643, 0.69234, 0.29155, 0.85052, 0.54027, 0.87573], "triangles": [7, 19, 6, 6, 20, 5, 6, 19, 20, 5, 20, 4, 7, 8, 19, 19, 18, 20, 20, 18, 4, 8, 9, 19, 4, 18, 17, 19, 9, 18, 9, 10, 18, 17, 18, 10, 4, 17, 3, 17, 10, 11, 17, 16, 3, 3, 16, 2, 17, 11, 16, 11, 12, 16, 16, 12, 15, 16, 15, 2, 2, 15, 1, 12, 13, 15, 15, 0, 1, 15, 14, 0, 15, 13, 14], "vertices": [1, 41, -4.98, 1.66, 1, 1, 41, -1.66, 5.79, 1, 2, 41, 6.76, 5.48, 0.916, 42, -3.31, 7.69, 0.084, 3, 41, 17.7, 10.26, 0.00765, 42, 8.62, 7.23, 0.97741, 43, -9.36, 6.88, 0.01494, 2, 42, 18.3, 12.37, 0.36115, 43, 0.12, 12.38, 0.63885, 2, 42, 31.68, 18.59, 0.00214, 43, 13.25, 19.11, 0.99786, 1, 43, 18.17, 10.44, 1, 1, 43, 22.02, -0.32, 1, 1, 43, 21.09, -9.97, 1, 2, 42, 31.7, -14.7, 0.00168, 43, 14.53, -14.16, 0.99832, 2, 42, 20.02, -11.32, 0.2579, 43, 2.73, -11.23, 0.7421, 2, 42, 11.19, -7.35, 0.9465, 43, -6.24, -7.59, 0.0535, 2, 41, 17.34, -4.92, 0.11534, 42, 1.69, -6.28, 0.88466, 1, 41, 5.16, -6.33, 1, 1, 41, -1.95, -3.39, 1, 1, 41, 6.03, -0.58, 1, 1, 42, 2.49, 0.16, 1, 1, 42, 12.06, -1.14, 1, 2, 42, 24.68, -1, 0.00052, 43, 7, -0.73, 0.99948, 1, 43, 15.32, 0.07, 1, 2, 42, 29.66, 9.45, 0.01002, 43, 11.58, 9.9, 0.98998], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 42, "height": 49}}, "c1": {"c1": {"type": "mesh", "uvs": [0.88233, 0, 0.92326, 0.03526, 0.94373, 0.18315, 0.96978, 0.35168, 1, 0.58556, 0.99211, 0.74033, 0.84139, 0.81084, 0.65905, 0.89682, 0.43205, 0.96733, 0.24784, 0.97077, 0.08783, 0.90714, 0, 0.80912, 0.04131, 0.6767, 0.17342, 0.55116, 0.33157, 0.43079, 0.45996, 0.36716, 0.54927, 0.2227, 0.67021, 0.08169, 0.76511, 0, 0.85418, 0.04305, 0.80971, 0.15513, 0.75514, 0.3083, 0.6743, 0.44092, 0.61569, 0.58289, 0.49038, 0.68376, 0.28828, 0.74727, 0.10234, 0.79023], "triangles": [25, 13, 14, 12, 13, 25, 26, 12, 25, 11, 12, 26, 10, 11, 26, 8, 25, 24, 9, 26, 25, 9, 25, 8, 10, 26, 9, 23, 15, 22, 23, 14, 15, 24, 14, 23, 24, 25, 14, 5, 6, 4, 4, 23, 22, 6, 23, 4, 7, 23, 6, 24, 23, 7, 8, 24, 7, 19, 18, 0, 19, 0, 1, 20, 18, 19, 17, 18, 20, 21, 17, 20, 16, 17, 21, 19, 1, 2, 20, 19, 2, 22, 16, 21, 15, 16, 22, 21, 20, 2, 21, 2, 3, 21, 3, 22, 22, 3, 4], "vertices": [1, 11, -20.32, 1.94, 1, 1, 11, -18.11, 8.4, 1, 1, 11, -1.31, 18.65, 1, 2, 11, 17.7, 30.63, 0.94265, 12, -34.83, 21.55, 0.05735, 2, 11, 44.38, 46.6, 0.48245, 12, -13.85, 44.49, 0.51755, 3, 11, 63.43, 54.05, 0.28588, 12, 2.26, 57.1, 0.71403, 13, -62.3, 30.14, 9e-05, 3, 11, 79.43, 41.07, 0.13965, 12, 21.31, 49.24, 0.83358, 13, -41.86, 32.78, 0.02677, 3, 11, 98.87, 25.39, 0.01153, 12, 44.43, 39.8, 0.70772, 13, -17.11, 36.06, 0.28075, 2, 12, 69.56, 24.89, 0.13228, 13, 12.11, 35.6, 0.86772, 2, 12, 84.57, 8.16, 0.00099, 13, 33.44, 28.54, 0.99901, 1, 13, 49.05, 14.11, 1, 1, 13, 54.84, -1.66, 1, 2, 12, 71.61, -36.26, 0.00712, 13, 44.26, -16.46, 0.99288, 2, 12, 48.53, -34.87, 0.20505, 13, 23.54, -26.71, 0.79495, 3, 11, 59.01, -36.19, 0.04157, 12, 23.9, -30.62, 0.71345, 13, 0.05, -35.25, 0.24498, 3, 11, 44.95, -25.32, 0.37555, 12, 7.32, -24.24, 0.5946, 13, -17.52, -37.95, 0.02985, 2, 11, 23.1, -23.15, 0.95318, 12, -14.24, -28.43, 0.04682, 1, 11, 0.09, -17.28, 1, 1, 11, -14.48, -11.11, 1, 1, 11, -13.73, 1.13, 1, 1, 11, 1.99, 2.21, 1, 1, 11, 23.16, 4.39, 1, 2, 11, 43.17, 2.53, 0.88927, 12, -2.38, 1.93, 0.11073, 2, 11, 63.2, 3.65, 0.00755, 12, 16.49, 8.74, 0.99245, 2, 12, 36.55, 5.85, 0.96258, 13, -7.09, 2.69, 0.03742, 1, 13, 18.96, 2.37, 1, 1, 13, 42.23, 0.15, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 122, "height": 132}}, "c2": {"c2": {"type": "mesh", "uvs": [0.03441, 0.01103, 0.13341, 0, 0.29635, 0.04999, 0.49435, 0.12653, 0.71091, 0.22672, 0.85735, 0.31021, 1, 0.36448, 0.97904, 0.49112, 0.91304, 0.62053, 0.79135, 0.74299, 0.62841, 0.85014, 0.44073, 0.94059, 0.25716, 1, 0.07773, 1, 0.11691, 0.87241, 0.17466, 0.7249, 0.22829, 0.57044, 0.2881, 0.4285, 0.17054, 0.345, 0.04266, 0.21837, 0, 0.13348, 0, 0.06112, 0.11065, 0.06321, 0.22036, 0.13154, 0.34694, 0.22454, 0.50448, 0.31564, 0.67326, 0.43522, 0.617, 0.57567, 0.47916, 0.7256, 0.34132, 0.84707, 0.1866, 0.94767], "triangles": [13, 30, 12, 11, 12, 29, 13, 14, 30, 12, 30, 29, 30, 14, 29, 11, 29, 10, 14, 15, 29, 29, 28, 10, 10, 28, 9, 29, 15, 28, 15, 16, 28, 28, 27, 9, 9, 27, 8, 28, 16, 27, 7, 8, 26, 16, 17, 27, 27, 17, 26, 8, 27, 26, 26, 5, 7, 7, 5, 6, 26, 4, 5, 26, 17, 25, 26, 25, 4, 17, 24, 25, 17, 18, 24, 24, 19, 23, 24, 18, 19, 24, 3, 25, 25, 3, 4, 23, 2, 24, 24, 2, 3, 23, 20, 22, 23, 19, 20, 20, 21, 22, 23, 22, 2, 21, 0, 22, 22, 1, 2, 22, 0, 1], "vertices": [2, 8, -28.67, 2.05, 0.72, 6, 19.02, 4.23, 0.28, 2, 8, -22.71, 11.58, 0.72, 6, 22.92, -6.31, 0.28, 2, 8, -4.41, 19.77, 0.848, 6, 18.23, -25.8, 0.152, 1, 8, 19.79, 27.99, 1, 2, 8, 48.31, 35.18, 0.79673, 9, -28.86, 21.09, 0.20327, 2, 8, 69.55, 38.31, 0.33571, 9, -15.63, 38.01, 0.66429, 2, 8, 86.88, 44.32, 0.14934, 9, -7.24, 54.32, 0.85066, 2, 8, 101.09, 28.66, 0.0518, 9, 13.85, 52.77, 0.9482, 3, 8, 112.3, 8.9, 0.00101, 9, 35.6, 46.2, 0.97089, 10, -34.62, 34.1, 0.0281, 2, 9, 56.44, 33.36, 0.70281, 10, -10.22, 32.27, 0.29719, 2, 9, 74.9, 15.8, 0.09189, 10, 14.26, 25.18, 0.90811, 1, 10, 37.69, 14.3, 1, 1, 10, 56.41, 1.28, 1, 1, 10, 66.32, -16.2, 1, 2, 9, 80.78, -41.31, 0.00723, 10, 45.74, -22.83, 0.99277, 2, 9, 56.06, -35.78, 0.23566, 10, 21.25, -29.29, 0.76434, 3, 8, 55.33, -43.1, 0.0377, 9, 30.21, -30.75, 0.79307, 10, -4.02, -36.71, 0.16923, 3, 8, 42.09, -22.49, 0.56941, 9, 6.41, -24.95, 0.42839, 10, -27.82, -42.51, 0.0022, 2, 8, 22.99, -23.2, 0.97107, 9, -6.94, -38.64, 0.02893, 2, 8, -2.25, -20.03, 0.72, 6, -14.6, -3.19, 0.28, 2, 8, -15.98, -14.29, 0.72, 6, -1.67, 4.17, 0.28, 2, 8, -24.98, -6.34, 0.72, 6, 10.13, 6.44, 0.28, 2, 8, -16.52, 2.72, 0.72, 6, 12.13, -5.79, 0.28, 1, 8, 0.11, 4.43, 1, 1, 8, 21.07, 4.85, 1, 2, 8, 44.08, 8.07, 0.99398, 9, -13.23, -1.45, 0.00602, 2, 8, 71.47, 9.12, 0.09807, 9, 5.89, 18.2, 0.90193, 2, 9, 29.42, 12.79, 0.99897, 10, -24.74, 1.58, 0.00103, 1, 10, 4.52, 0.44, 1, 2, 9, 75.62, -16.35, 0.00348, 10, 29.68, -3.05, 0.99652, 1, 10, 52.75, -9.88, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 112, "height": 166}}}}], "animations": {"idle": {"bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.44}], "translate": [{"x": 0.86, "y": 0.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.04, "y": 2.24, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.86, "y": 0.64}]}, "bone5": {"rotate": [{"angle": -1.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.11}], "translate": [{"x": 1.23, "y": 0.31, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.72, "y": 0.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.23, "y": 0.31}]}, "bone6": {"translate": [{"x": 2.11, "y": -0.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 4.22, "y": -1.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 2.11, "y": -0.89}]}, "bone7": {"translate": [{"x": -0.67, "y": 2.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -1.34, "y": 4.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -0.67, "y": 2.11}]}, "bone8": {"rotate": [{"angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 8.04}]}, "bone9": {"rotate": [{"angle": 5.76, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 5.76}]}, "bone10": {"rotate": [{"angle": 2.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.28}]}, "bone11": {"rotate": [{"angle": -9.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -9.99, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -9.41}]}, "bone12": {"rotate": [{"angle": -1.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.11}]}, "bone13": {"rotate": [{"angle": -0.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -0.44}]}, "bone16": {"rotate": [{"angle": -2.29}]}, "bone17": {"rotate": [{"angle": 2.49}]}, "bone18": {"rotate": [{"angle": 4.5}]}, "bone20": {"rotate": [{"angle": 0.98}]}, "bone21": {"rotate": [{"angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.74}]}, "bone22": {"rotate": [{"angle": 6.39, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 6.39}]}, "bone24": {"rotate": [{"angle": -5.2, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -12.61, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -5.2}]}, "bone25": {"rotate": [{"angle": -3.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -12.61, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -3.05}]}, "bone26": {"rotate": [{"angle": -0.54, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -12.61, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -0.54}]}, "bone27": {"rotate": [{"angle": -1.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": -12.61, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -1.64}]}, "bone28": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.47, "y": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone30": {"rotate": [{"angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 9.77, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.77}]}, "bone31": {"rotate": [{"angle": 7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.77, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7}]}, "bone32": {"rotate": [{"angle": 3.27, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1333, "angle": 2.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 7.18, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 3.27}]}, "bone33": {"rotate": [{"angle": 6.26, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.1333, "angle": 5.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 7.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 6.26}]}, "bone34": {"rotate": [{"angle": -1.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.73}]}, "bone35": {"rotate": [{"angle": -4.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.36}]}, "bone36": {"rotate": [{"angle": -5.96, "curve": 0.267, "c2": 0.1, "c3": 0.657, "c4": 0.63}, {"time": 0.6, "angle": -1.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -6.09, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -5.96}]}, "bone37": {"rotate": [{"angle": -4.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -6.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "angle": -4.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2667, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -4.85}]}, "bone14": {"rotate": [{"angle": 5.77, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 8.55, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 5.77}]}, "bone38": {"rotate": [{"angle": 8.48, "curve": 0.256, "c2": 0.06, "c3": 0.653, "c4": 0.62}, {"time": 0.6333, "angle": 2.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 8.55, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": 8.48}]}, "bone39": {"rotate": [{"angle": 6.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 8.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "angle": 6.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 6.48}]}, "bone40": {"rotate": [{"angle": 8.48, "curve": 0.256, "c2": 0.06, "c3": 0.653, "c4": 0.62}, {"time": 0.6333, "angle": 2.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 8.55, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": 8.48}]}, "bone41": {"rotate": [{"angle": 6.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 8.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "angle": 6.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 6.48}]}, "bone42": {"rotate": [{"angle": 2.78, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "angle": 8.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 2.78}]}, "bone43": {"rotate": [{"angle": -7.08, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -10.49, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": -7.08}]}, "bone44": {"rotate": [{"angle": -10.4, "curve": 0.256, "c2": 0.06, "c3": 0.653, "c4": 0.62}, {"time": 0.6333, "angle": -2.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -10.49, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": -10.4}]}, "bone45": {"rotate": [{"angle": -7.95, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": -10.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "angle": -7.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -7.95}]}}}, "train": {"slots": {"tou1": {"attachment": [{"time": 0.4333, "name": "tou1"}]}}, "bones": {"bone2": {"rotate": [{"time": 0.1667, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": -30.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.4333, "angle": 13.33, "curve": "stepped"}, {"time": 0.5, "angle": 13.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": 23.64, "y": -8.24, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": 89.93, "y": 50.57, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4333, "x": -39.41, "y": -9.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -20.13, "y": -9.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone3": {"rotate": [{"time": 0.1667, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -5.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 11.86, "curve": "stepped"}, {"time": 0.5, "angle": 11.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"time": 0.1667, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "x": 27.12, "y": -3.38, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.4333, "y": -10.9, "curve": "stepped"}, {"time": 0.5, "y": -10.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone4": {"rotate": [{"angle": 7.14, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4, "angle": -17.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 19.42, "curve": "stepped"}, {"time": 0.5667, "angle": 19.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 7.14}], "translate": [{"x": -5.75, "y": 1.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "x": -15.63, "y": 3.61, "curve": "stepped"}, {"time": 0.5667, "x": -15.63, "y": 3.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": -5.75, "y": 1.33}]}, "bone5": {"rotate": [{"angle": 4.07, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "angle": -9.2, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5667, "angle": 6.85, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "angle": 4.07}]}, "bone8": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 17.75, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 51.74, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.4, "angle": -79.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "angle": -149.47, "curve": "stepped"}, {"time": 0.5, "angle": -149.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"time": 0.4, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.4333, "x": -30.57, "y": 11.72, "curve": "stepped"}, {"time": 0.5, "x": -30.57, "y": 11.72, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "scale": [{"time": 0.4, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.4333, "x": 1.237, "y": 1.237, "curve": "stepped"}, {"time": 0.5, "x": 1.237, "y": 1.237, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone9": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -64.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -66.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 23.32, "curve": "stepped"}, {"time": 0.5, "angle": 23.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone10": {"rotate": [{"time": 0.1667, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4333, "angle": 16.21, "curve": "stepped"}, {"time": 0.5, "angle": 16.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone11": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 76.79, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 18.26, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4333, "angle": 85.65, "curve": "stepped"}, {"time": 0.5, "angle": 85.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone12": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -94.39, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -119.22, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4333, "angle": -77.18, "curve": "stepped"}, {"time": 0.5, "angle": -77.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone16": {"rotate": [{"angle": -2.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -36.99, "curve": "stepped"}, {"time": 0.3333, "angle": -36.99, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4333, "angle": -2.29}]}, "bone17": {"rotate": [{"angle": 2.49, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 22.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4333, "angle": 2.49}]}, "bone18": {"rotate": [{"angle": 4.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 19.74, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4333, "angle": 4.5}]}, "bone20": {"rotate": [{"angle": 0.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 19.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4333, "angle": 0.98}]}, "bone21": {"rotate": [{"angle": -1.74, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -3.86, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4333, "angle": -1.74}]}, "bone22": {"rotate": [{"angle": 6.39, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -19.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4333, "angle": 6.39}]}, "bone24": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone25": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone26": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone27": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone28": {"translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": -0.49, "y": -4.61, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "x": 6.33, "y": -4.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.4333, "x": 0.67, "y": 10.55, "curve": "stepped"}, {"time": 0.5, "x": 0.67, "y": 10.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone14": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone38": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone39": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone40": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone41": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone42": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone43": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone44": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone45": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone46": {"translate": [{"time": 0.1667, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": 23.35, "y": 38.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333}]}}}}}