import { _decorator, Animation, Component, find, instantiate, Label, Node, ProgressBar, Sprite, tween, v3 } from "cc";
import { DisciplinesModule } from "db://assets/GameScrpit/module/disciplines/DisciplinesModule";
import ToolExt from "../../../common/ToolExt";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { RedeemChosenRequest, RedeemRequest, RedeemResponse } from "../../../net/protocol/Activity";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { DisciplinesRouteItem } from "db://assets/GameScrpit/module/disciplines/DisciplinesRoute";
import TipMgr from "db://assets/GameScrpit/lib/tips/TipMgr";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import GameHttpApi from "../../../httpNet/GameHttpApi";
import { GoodsRouteName } from "db://assets/GameScrpit/module/goods/GoodsRoute";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { DisciplinesAudioName } from "db://assets/GameScrpit/module/disciplines/DisciplinesConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIOptionalItem } from "../UIOptionalItem";
import { CommIntegerListMessage } from "../../../net/protocol/Comm";
import { RedeemPackVO, LeaderFundVO } from "db://assets/GameScrpit/module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("DisMain4")
export class DisMain4 extends Component {
  private _redeemList: Array<RedeemPackVO> = null;
  private _redeemMap: { [key: number]: number } = null;
  private _disciplinesdb: LeaderFundVO;

  @property(Node)
  btn_fuli_award: Node = null;

  @property(Node)
  btn_fuli_award_get: Node = null;

  @property(Node)
  progressBar_rech: Node = null;

  @property(Node)
  lbl_rech_bar: Node = null;

  @property(Node)
  main4_day_award: Node = null;

  @property(Node)
  btn_main4_day_awardget: Node = null;

  @property(Node)
  lbl_gift1_goumai: Node = null;

  @property(Node)
  btn_gift1_goumai: Node = null;
  @property(Node)
  gift1_yigoumai: Node = null;

  @property(Node)
  lbl_gift2_goumai: Node = null;

  @property(Node)
  btn_gift2_goumai: Node = null;

  @property(Node)
  gift2_yigoumai: Node = null;

  @property(Node)
  award_item: Node = null;

  @property(Node)
  chosen_item: Node = null;

  protected onEnable(): void {}

  protected onDisable(): void {}

  onLoad() {
    MsgMgr.on(MsgEnum.ON_ACTIVITY_FUND_GIFT_UPDATE, this.initMain4, this);
  }

  start() {
    this.initMain4();
  }

  private async initMain4() {
    this._disciplinesdb = await DisciplinesModule.data.getdb();
    this._redeemList = await DisciplinesModule.data.getRedeemList();
    this._redeemMap = await DisciplinesModule.data.redeemMap;
    this.setRechargeAward();
    this.setProgress();
    this.setMain4DayFree();
    this.setDisGift1();
    this.setDisGift2();

    this.node.getChildByName("gift1").active = true;
    this.node.getChildByName("gift2").active = true;
  }

  private async setRechargeAward() {
    let rechargeVO = await DisciplinesModule.data.getRechargeVO();
    let rewardList = rechargeVO.rewardList;
    if (rewardList.length >= 2) {
      FmUtils.setItemNode(this.btn_fuli_award, rewardList[0], rewardList[1]);
    } else {
      this.btn_fuli_award.active = false;
    }
  }

  private async setProgress() {
    let leaderRecharge = await DisciplinesModule.data.getLeaderRecharge();
    this.progressBar_rech.getComponent(ProgressBar).progress = leaderRecharge.numerator / leaderRecharge.denominator;
    this.lbl_rech_bar.getComponent(Label).string = leaderRecharge.numerator + "/" + leaderRecharge.denominator;
    if (leaderRecharge.numerator >= leaderRecharge.denominator && leaderRecharge.take == false) {
      this.award_snake(this.btn_fuli_award, true);
      this.btn_fuli_award_get.active = true;
    } else {
      this.award_snake(this.btn_fuli_award, false);
      this.btn_fuli_award_get.active = false;
    }
  }

  private setMain4DayFree() {
    let info = this._redeemList[0];
    let rewardList = info.rewardList;
    if (rewardList.length >= 2) {
      FmUtils.setItemNode(this.main4_day_award, rewardList[0], rewardList[1]);
    } else {
      this.main4_day_award.active = false;
    }
    let num = this._redeemMap[info.id] || 0;
    if (num >= info.max) {
      this.award_snake(this.main4_day_award, false);
      this.btn_main4_day_awardget.active = false;
      this.main4_day_award.getChildByName("spr_gou").active = true;
    } else {
      this.award_snake(this.main4_day_award, true);
      this.btn_main4_day_awardget.active = true;
      this.main4_day_award.getChildByName("spr_gou").active = false;
    }
  }

  private award_snake(targetNode: Node, is: boolean) {
    // 获取目标节点的初始位置
    const nodeStartPos = targetNode.getPosition();
    if (!targetNode["nodeStartPos"]) {
      targetNode["nodeStartPos"] = nodeStartPos;
    }
    let ani = targetNode.getComponent(Animation);
    if (is == false) {
      ani.stop();
      targetNode.setRotation(0, 0, 0, 0);
      return;
    }
    targetNode.setRotation(0, 0, 0, 0);
    ani.play("ani_dou");
  }

  private setDisGift1() {
    let info: RedeemPackVO = this._redeemList[1];
    let price = info.price % 10000;
    this.lbl_gift1_goumai.getComponent(Label).string = "￥ " + price;
    let lbl_gift_name = find(`lay1/lbl_gift_name`, this.node.getChildByName("gift1"));
    lbl_gift_name.getComponent(Label).string = "修行" + price + "元礼包";
    let lay2 = find(`lay2`, this.node.getChildByName("gift1"));
    lay2.destroyAllChildren();
    let rewardList = ToolExt.traAwardItemMapList(info.rewardList);
    for (let i = 0; i < rewardList.length; i++) {
      let node = instantiate(this.award_item);
      node.setPosition(v3(0, 0, 0));
      lay2.addChild(node);
      node.active = true;
      let obj = rewardList[i];
      FmUtils.setItemNode(node, obj.id, obj.num);
    }
    let chosen_itemList = [];
    for (let i = 0; i < info.chosenList.length; i++) {
      let node = instantiate(this.chosen_item);

      node.setPosition(v3(0, 0, 0));
      lay2.addChild(node);
      node.active = true;
      let chosenList = info.chosenList[i];
      node["infodb"] = chosenList;
      node.getChildByName("btn_chosen_type2")["infodb"] = chosenList;
      node.getChildByName("btn_chosen_type2")["redeemId"] = info.id;
      let chosen = DisciplinesModule.data.getChosenMap(info.id);
      if (chosen.intList.length > 0) {
        node.getChildByName("type1").active = true;
        node.getChildByName("btn_chosen_type2").getComponent(Sprite).enabled = false;
        let list = ToolExt.traAwardItemMapList(info.chosenList[i]);
        FmUtils.setItemNode(node.getChildByName("type1"), list[chosen.intList[0]].id, list[chosen.intList[0]].num);
      } else {
        node.getChildByName("type1").active = false;
        node.getChildByName("btn_chosen_type2").getComponent(Sprite).enabled = true;
      }
      chosen_itemList.push(node);
    }
    let num = this._redeemMap[info.id] || 0;
    if (num >= info.max) {
      this.btn_gift1_goumai.active = false;
      this.gift1_yigoumai.active = true;
      chosen_itemList.forEach((val) => {
        val.getChildByName("btn_chosen_type2").active = false;
        let chosen = DisciplinesModule.data.getChosenMap(info.id);
        if (chosen.intList.length > 0) {
          val.getChildByName("type1").active = true;
        }
      });
    } else {
      this.btn_gift1_goumai.active = true;
      this.gift1_yigoumai.active = false;
      chosen_itemList.forEach((val) => {
        val.getChildByName("btn_chosen_type2").active = true;
      });
    }
  }

  private setDisGift2() {
    let info = this._redeemList[2];
    let price = info.price % 10000;
    this.lbl_gift2_goumai.getComponent(Label).string = "￥ " + price;
    let lbl_gift_name = find(`lay1/lbl_gift_name`, this.node.getChildByName("gift2"));
    lbl_gift_name.getComponent(Label).string = "修行" + price + "元礼包";
    let lay2 = find(`lay2`, this.node.getChildByName("gift2"));
    lay2.destroyAllChildren();
    let rewardList = ToolExt.traAwardItemMapList(info.rewardList);
    for (let i = 0; i < rewardList.length; i++) {
      let node = instantiate(this.award_item);
      node.setPosition(v3(0, 0, 0));
      lay2.addChild(node);
      node.active = true;
      let obj = rewardList[i];
      FmUtils.setItemNode(node, obj.id, obj.num);
    }
    let chosen_itemList = [];
    for (let i = 0; i < info.chosenList.length; i++) {
      let node = instantiate(this.chosen_item);
      node.setPosition(v3(0, 0, 0));
      lay2.addChild(node);
      node.active = true;
      let chosenList = info.chosenList[i];
      node["infodb"] = chosenList;
      node.getChildByName("btn_chosen_type2")["infodb"] = chosenList;
      node.getChildByName("btn_chosen_type2")["redeemId"] = info.id;
      let chosen = DisciplinesModule.data.getChosenMap(info.id);
      if (chosen.intList.length > 0) {
        node.getChildByName("type1").active = true;
        node.getChildByName("btn_chosen_type2").getComponent(Sprite).enabled = false;
        let list = ToolExt.traAwardItemMapList(info.chosenList[i]);
        FmUtils.setItemNode(node.getChildByName("type1"), list[chosen.intList[0]].id, list[chosen.intList[0]].num);
      } else {
        node.getChildByName("type1").active = false;
        node.getChildByName("btn_chosen_type2").getComponent(Sprite).enabled = true;
      }
      chosen_itemList.push(node);
    }
    let num = this._redeemMap[info.id] || 0;
    if (num >= info.max) {
      this.btn_gift2_goumai.active = false;
      this.gift2_yigoumai.active = true;
      chosen_itemList.forEach((val) => {
        val.getChildByName("btn_chosen_type2").active = false;
      });
    } else {
      this.btn_gift2_goumai.active = true;
      this.gift2_yigoumai.active = false;
      chosen_itemList.forEach((val) => {
        val.getChildByName("btn_chosen_type2").active = true;
      });
    }
  }

  private on_click_btn_main4_day_awardget() {
    AudioMgr.instance.playEffect(DisciplinesAudioName.Effect.点击道具图标);
    let param: RedeemRequest = {
      activityId: this._disciplinesdb.id,
      redeemId: this._redeemList[0].id,
      count: 1,
    };
    DisciplinesModule.api.buyFixedPack(param, async (res: RedeemResponse) => {
      let rewardList = res.rewardList;
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
      this._redeemMap = await DisciplinesModule.data.redeemMap;
      this.initMain4();
    });
  }
  private async on_click_btn_fuli_award_get() {
    AudioMgr.instance.playEffect(DisciplinesAudioName.Effect.点击道具图标);
    DisciplinesModule.api.takeLeaderRechargeReward(this._disciplinesdb.id, async (res: RedeemResponse) => {
      let rewardList = res.rewardList;
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
      this._redeemMap = await DisciplinesModule.data.redeemMap;
      this.initMain4();
    });
  }

  private on_click_btn_chosen_type2(event) {
    AudioMgr.instance.playEffect(1346);
    let chosenList = event.target["infodb"];
    RouteManager.uiRouteCtrl.showRoute(UIOptionalItem, {
      payload: {
        chosenList: chosenList,
        activityId: this._disciplinesdb.id,
        redeemId: event.target["redeemId"],
      },
      onCloseBack: (args) => {
        if (!args || !args.activityId) {
          return;
        }
        let param: RedeemChosenRequest = args;
        DisciplinesModule.api.recordRedeemChosen(param, async (res: CommIntegerListMessage) => {
          log.log("确认预选道具", res);
        });
      },
    });
  }
  private on_click_btn_gift1_goumai(event) {
    AudioMgr.instance.playEffect(1348);
    let chosen = DisciplinesModule.data.getChosenMap(this._redeemList[1].id);
    if (chosen.intList.length <= 0) {
      TipMgr.showTip("请先预选道具");
      return;
    }
    let data = {
      goodsId: this._redeemList[1].id,
      goodsType: this._disciplinesdb.buyType,
      playerId: PlayerModule.data.playerId,
      orderAmount: (this._redeemList[1].price % 10000) % 10000,
      goodsName: this._disciplinesdb.name,
      platformType: "TEST",
      param: chosen.intList,
    };

    GameHttpApi.pay(data).then((resp: any) => {
      if (resp.code != 200) {
        log.log(resp.msg);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }
  private on_click_btn_gift2_goumai(event) {
    AudioMgr.instance.playEffect(1348);
    let chosen = DisciplinesModule.data.getChosenMap(this._redeemList[2].id);
    if (chosen.intList.length <= 0) {
      TipMgr.showTip("请先预选道具");
      return;
    }
    let data = {
      goodsId: this._redeemList[2].id,
      goodsType: this._disciplinesdb.buyType,
      playerId: PlayerModule.data.playerId,
      orderAmount: (this._redeemList[1].price % 10000) % 10000,
      goodsName: this._disciplinesdb.name,
      platformType: "TEST",
      param: chosen.intList,
    };
    GameHttpApi.pay(data).then((resp: any) => {
      if (resp.code != 200) {
        log.log(resp.msg);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }

  update(deltaTime: number) {}

  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_ACTIVITY_FUND_GIFT_UPDATE, this.initMain4, this);
  }
}
