import { AttrEnum, BaseAttrList } from "../../game/GameDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { divide, times } from "./NumbersUtils";

export default class Formate {
  static units = ["", "万", "亿", "万亿", "亿亿", "万亿亿", "亿亿亿"];

  /**
   * 格式化一个数字，小于等于100000显示整数部份，大于100000显示万、亿、万亿等单位
   * 小数位数最多2位
   * 数字最多4个，当超过4个时，小数位数舍去
   * 去除小数最后为0的部份
   *
   * 举例：
   * [0-10_0000) 直接显示数字 例：1 - 99999
   * [10_0000 - 1_0000_0000) 单位万 例：10万 - 77.77万 - 555.5万 - 9999万
   * [1_0000_0000 - 1_0000_0000_0000) 单位亿 例：1亿 - 77.77亿 - 555.5亿 - 9999亿
   * [1_0000_0000_0000 - 1_0000_0000_0000_0000) 单位万亿 例：1万亿 - 77.77万亿 - 555.5万亿 - 9999万亿
   * [1_0000_0000_0000_0000 - 1_0000_0000_0000_0000_0000) 单位亿亿 例：1亿亿 - 77.77亿亿 - 555.5亿亿 - 9999亿亿
   * [1_0000_0000_0000_0000_0000 - 1_0000_0000_0000_0000_0000_0000) 单位万亿亿 例：1万亿亿 - 77.77万亿亿 - 555.5万亿亿 - 9999万亿亿

   * @param number 一个数字
   * @param decimal 最大精度，小数位数最多几位
   * @param numCount 最大显示数字数量
   * @returns
   */
  public static format(number: number, decimal: number = 2, numCount: number = 4): string {
    if (!number) return "0";

    if (number < 100000) {
      return Math.floor(number) + "";
    }

    // 单位换算
    let unitIndex = 0;
    for (let i = 0; i < Formate.units.length; i++) {
      if (i == 0) {
        if (number < 100000) {
          break;
        } else {
          number /= 10000;
          unitIndex++;
        }
      } else if (number >= 10000) {
        number /= 10000;
        unitIndex++;
      }
    }

    if (decimal == 0) {
      return Math.floor(number) + this.units[unitIndex];
    }

    // 数字转为字符串循环用
    let numStr = Formate.formatDecimal(number, decimal); // (Math.floor(number * 100) / 100).toFixed(2);
    // 最终的数字串
    let rs = "";
    // 已有的有效位数
    let rsCount = 0;
    for (let i = 0; i < numStr.length; i++) {
      // 小数点直接加上，不算有效位
      if (numStr[i] == "." && rs.length < numCount) {
        rs += numStr[i];
        continue;
      }

      // 有效位数+1
      rsCount++;
      if (rsCount <= numCount) {
        rs += numStr[i];
      } else {
        // 超过了有效位就退出
        break;
      }
    }

    return rs + this.units[unitIndex];
  }

  /**
   * 格式化小数
   * 会舍去剩余部份
   *
   * @param num 要格式化的数字
   * @param decimal 精度
   * @returns
   */
  public static formatDecimal(num: number, decimal: number = 1): string {
    // 将数字格式化为两位小数
    const factor = Math.pow(10, decimal);
    const truncated = Math.trunc(num * factor) / factor;
    // 转换为字符串，同时防止浮点数精度问题
    let formatted = truncated.toFixed(decimal);

    // 使用正则表达式去除多余的0
    formatted = formatted.replace(/(?:\.0*|(\.\d+?)0+)$/, "$1");

    // 如果小数部分为空，则去掉小数点
    formatted = formatted.replace(/\.$/, "");

    return formatted;
  }

  // 将权重转为比例，isShow为true时，保留两位小数，并且保证合并比例=1
  public static weightListToPercent(weightList: number[], isShow: boolean = true): number[] {
    // 计算所有权重的总和
    const totalWeight = weightList.reduce((sum, weight) => sum + weight, 0);

    // 计算每个权重的百分比

    const percentageList = weightList.map((weight) => times(divide(weight, totalWeight), 100));

    if (isShow) {
      const percentageFormatList = percentageList.map((percentage) => {
        // 大于0，最小为万分之1
        if (percentage > 0) {
          percentage = Math.max(percentage, 0.01);
        }

        // 否则保留两位小数
        return Number(Formate.formatDecimal(percentage));
      });

      let maxIdx = -1;
      let maxPercentage = 0;
      let totalPercentage = 0;
      for (let i = 0; i < percentageFormatList.length; i++) {
        let percentage = Number(percentageFormatList[i]);
        if (percentage > maxPercentage) {
          maxIdx = i;
          maxPercentage = percentage;
        }
        totalPercentage += percentage;
      }

      let dt = 100 - totalPercentage;
      let num = percentageFormatList[maxIdx] + dt;
      percentageFormatList[maxIdx] = num;
      return percentageFormatList;
    }
    return percentageList;
  }

  // 格式化：属性名+属性
  public static formatAttribute(attrId: number | AttrEnum, value: number): string {
    attrId = attrId as number;

    let configAttr = JsonMgr.instance.getConfigAttribute(attrId);

    let rs = `${configAttr.name}：`;
    value = value || 0;

    // 基础属性
    if (BaseAttrList.includes(attrId)) {
      return `${rs}${Formate.format(value)}`;
    } else {
      return `${rs}${Formate.formatDecimal(value * 100, 1)}%`;
    }
  }

  // 格式化：属性
  public static formatAttr(attrId: number, attrValue: number): string {
    attrValue = attrValue || 0;
    let rs = "";
    if (BaseAttrList.includes(attrId)) {
      return `${Formate.format(attrValue)}`;
    } else {
      return `${rs}${Formate.formatDecimal(attrValue * 100, 1)}%`;
    }
  }
}
