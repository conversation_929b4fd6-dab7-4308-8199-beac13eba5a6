{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "855a84b3-6578-4cc4-933f-535fed15568e", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "855a84b3-6578-4cc4-933f-535fed15568e@6c48a", "displayName": "bg_shanghai<PERSON>xian_jingdu", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "855a84b3-6578-4cc4-933f-535fed15568e", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "855a84b3-6578-4cc4-933f-535fed15568e@f9941", "displayName": "bg_shanghai<PERSON>xian_jingdu", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 118, "height": 20, "rawWidth": 118, "rawHeight": 20, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-59, -10, 0, 59, -10, 0, -59, 10, 0, 59, 10, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 20, 118, 20, 0, 0, 118, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-59, -10, 0], "maxPos": [59, 10, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "855a84b3-6578-4cc4-933f-535fed15568e@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "855a84b3-6578-4cc4-933f-535fed15568e@6c48a"}}