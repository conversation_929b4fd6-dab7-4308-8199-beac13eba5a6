import MsgEnum from "../../game/event/MsgEnum";
import { <PERSON>piHandler } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { RedeemBuyMessage, RedeemLimitUpdateMessage, TopUpMessage } from "../../game/net/protocol/Activity";
import { CommLongListMessage } from "../../game/net/protocol/Comm";
import MsgMgr from "../../lib/event/MsgMgr";
import { AfterModule } from "./AfterModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);

export class AfterSubscriber {
  private rechargeCallabck(res: TopUpMessage) {
    log.log("累充回馈充钱后推送", res);
    AfterModule.data.topUpMessage = res;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_RECHARGE_UP, { res: res });
  }

  private redeemBuyCallback(rs: RedeemBuyMessage) {
    let list = [10901];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }
    AfterModule.data.topUpMessage.redeemMap = rs.redeemMap;
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList });
  }

  private upActivityDb(rs: CommLongListMessage) {
    for (let i = 0; i < rs.longList.length; i++) {
      if (rs.longList.includes(10901)) {
        AfterModule.data.upVO();
        return;
      }
    }
  }
  private RedeemLimitUpdate(rs: RedeemLimitUpdateMessage) {
    let list = [10901];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }

    /** achieveId*/
    AfterModule.data.limitMap = rs.limitMap;
  }

  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(TopUpMessage, ActivityCmd.RechargeCash, this.rechargeCallabck);
    ApiHandler.instance.subscribe(RedeemBuyMessage, ActivityCmd.RedeemBuyMessage, this.redeemBuyCallback);
    ApiHandler.instance.subscribe(CommLongListMessage, ActivityCmd.ActivityUp, this.upActivityDb);
    ApiHandler.instance.subscribe(RedeemLimitUpdateMessage, ActivityCmd.RedeemLimitUpdate, this.RedeemLimitUpdate);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ActivityCmd.RechargeCash, this.rechargeCallabck);
    ApiHandler.instance.unSubscribe(ActivityCmd.RedeemBuyMessage, this.redeemBuyCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.ActivityUp, this.upActivityDb);
    ApiHandler.instance.unSubscribe(ActivityCmd.RedeemLimitUpdate, this.RedeemLimitUpdate);
  }
}
