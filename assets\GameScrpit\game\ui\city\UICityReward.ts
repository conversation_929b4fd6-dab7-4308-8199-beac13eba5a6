import { _decorator } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UINode } from "../../../lib/ui/UINode";
import { UIMgr } from "../../../lib/ui/UIMgr";

import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { CityRewardAdapter } from "./adapter/CityRewardAdapter";
import { JsonMgr } from "../../mgr/JsonMgr";
import { CityWorkerAdapter } from "./adapter/CityWorkerAdapter";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";

const { ccclass, property } = _decorator;

@ccclass("UICityReward")
export class UICityReward extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UICityReward`;
  }

  private _adapter: CityRewardAdapter;
  private _adapter2: CityWorkerAdapter;

  public init(param) {
    super.init(param);
  }

  protected onEvtShow(): void {
    this._adapter = new CityRewardAdapter(
      this.getNode("city_level_viewholder"),
      this.getNode("city_worker_viewholder")
    );

    this._adapter2 = new CityWorkerAdapter(this.getNode("city_worker_viewholder"));

    this.getNode("list_view_level").getComponent(AdapterView).setAdapter(this._adapter);

    this.getNode("list_view_worker").getComponent(AdapterView).setAdapter(this._adapter2);

    this.onSwitchTab(1);
    this._adapter.setDatas(1);

    BadgeMgr.instance.setBadgeId(this.getNode("btn_tab1"), BadgeType.UIMajorCity.btn_wuzurongyao.btn_tab1.id);
  }

  private onSwitchTab(tabIndex: number) {
    this.getNode("btn_tab1").getChildByName("bg_active").active = tabIndex == 1;
    this.getNode("btn_tab1").getChildByName("bg_unactive").active = tabIndex != 1;
    this.getNode("btn_tab2").getChildByName("bg_active").active = tabIndex == 2;
    this.getNode("btn_tab2").getChildByName("bg_unactive").active = tabIndex != 2;
    this.getNode("list_view_level").active = tabIndex == 1;
    this.getNode("list_view_worker").active = tabIndex == 2;
  }

  private on_click_btn_tab1() {
    AudioMgr.instance.playEffect(1881);
    this.onSwitchTab(1);
    this._adapter.setDatas(1);
  }

  private on_click_btn_tab2() {
    AudioMgr.instance.playEffect(1881);
    this.onSwitchTab(2);
    let datas = Object.keys(JsonMgr.instance.jsonList.c_buildWorkerReward).map(Number);
    this._adapter2.setDatas(datas);
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop);
  }
}
