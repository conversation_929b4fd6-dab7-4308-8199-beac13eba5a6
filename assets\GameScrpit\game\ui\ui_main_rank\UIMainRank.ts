import { _decorator } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { PlayerRankAdapter } from "./adapter/PlayerViewholder";
import { MainRankModule } from "../../../module/main_rank/MainRankModule";
import { PlayerRankMessage } from "../../net/protocol/Player";
import { ClubRankMessage } from "../../net/protocol/Club";
import { ClubMainRankAdapter } from "./adapter/ClubMainRankViewHolder";
import { MessageComponent } from "../../../../platform/src/core/ui/components/MessageComponent";
import Formate from "../../../lib/utils/Formate";
import { RewardMessage } from "../../net/protocol/Comm";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { ShengDianModule } from "../../../module/sheng_dian/ShengDianModule";
import { LangMgr } from "../../mgr/LangMgr";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
const { ccclass, property } = _decorator;

@ccclass("UIMainRank")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_MAIN_RANK,
  url: "prefab/ui/UIMainRank",
  nextHop: [],
  exit: "dialog_close",
})
export class UIMainRank extends BaseCtrl {
  public playShowAni: boolean = true;
  private _playerRankAdapter: PlayerRankAdapter;
  private _playerFanrongAdapter: PlayerRankAdapter;
  private _clubRankAdapter: ClubMainRankAdapter;
  start() {
    super.start();
    this._playerRankAdapter = new PlayerRankAdapter(1, this.getNode("viewholder_player"));
    this.getNode("list_zhanli").getComponent(AdapterView).setAdapter(this._playerRankAdapter);
    this._playerFanrongAdapter = new PlayerRankAdapter(2, this.getNode("viewholder_player"));
    this.getNode("list_fanrong").getComponent(AdapterView).setAdapter(this._playerFanrongAdapter);
    this._clubRankAdapter = new ClubMainRankAdapter(1, this.getNode("viewholder_club_single"));
    this.getNode("list_club").getComponent(AdapterView).setAdapter(this._clubRankAdapter);
    let isCanTake = MainRankModule.data?.playerRankBoardMessage?.takeCurServerRankReward ?? true;
    this.getNode("btn_zan").getComponent(FmButton).btnEnable = !isCanTake;
    //
    MainRankModule.api.powerRank((res: PlayerRankMessage) => {
      this._playerRankAdapter.setData(res.rankList);
      this.getNode("lbl_my_rank")
        .getComponent(MessageComponent)
        .setArgs([res.rank + ""], (arg) => Number(arg) > 0, [LangMgr.txMsgCode(648)]);

      this.getNode("lbl_my_zhanli").getComponent(MessageComponent).args = [Formate.format(res.point)];
    });

    MainRankModule.api.energyRank((res: PlayerRankMessage) => {
      this._playerFanrongAdapter.setData(res.rankList);
      this.getNode("lbl_my_fanrong").getComponent(MessageComponent).args = [Formate.format(res.point)];
      this.getNode("lbl_my_fanrong_rank")
        .getComponent(MessageComponent)
        .setArgs([res.rank + ""], (arg) => Number(arg) > 0, [LangMgr.txMsgCode(648)]);
    });
    MainRankModule.api.clubRank((res: ClubRankMessage) => {
      this._clubRankAdapter.setData(res.rankList);
      this.getNode("lbl_club_rank")
        .getComponent(MessageComponent)
        .setArgs([res.rank + ""], (arg) => Number(arg) > 0, [LangMgr.txMsgCode(648)]);
      this.getNode("lbl_club_zhanli").getComponent(MessageComponent).args = [Formate.format(res.point)];
    });
    this.on_click_btn_top_tab1();

    BadgeMgr.instance.setBadgeId(this.getNode("btn_zan"), BadgeType.UIMajorCity.btn_pai_hang.btn_zan.id);
  }

  private refreshTab(index: number) {
    this.getNode("btn_top_tab1").getComponent(FmButton).selected = index == 1;
    this.getNode("btn_top_tab2").getComponent(FmButton).selected = index == 2;
    this.getNode("btn_top_tab3").getComponent(FmButton).selected = index == 3;
    this.getNode("node_rank_zhanli").active = index == 1;
    this.getNode("node_rank_fanrong").active = index == 2;
    this.getNode("node_rank_club").active = index == 3;
  }
  private on_click_btn_top_tab1() {
    this.refreshTab(1);
    this._playerRankAdapter.notifyDataSetChanged();
  }
  private on_click_btn_top_tab2() {
    //
    this.refreshTab(2);
    this._playerFanrongAdapter.notifyDataSetChanged();
  }
  private on_click_btn_top_tab3() {
    this.refreshTab(3);
    this._clubRankAdapter.notifyDataSetChanged();
  }

  private on_click_btn_zan() {
    //点赞
    if (!this.getNode("btn_zan").getComponent(FmButton).btnEnable) {
      return;
    }
    MainRankModule.api.takeMainRankReward((data: RewardMessage) => {
      //
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
      this.getNode("btn_zan").getComponent(FmButton).btnEnable = false;
    });
  }

  update(deltaTime: number) {}
}
