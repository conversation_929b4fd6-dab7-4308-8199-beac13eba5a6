import { CompeteExtraMessage, CompeteLogMessage, CompeteOwnRankMessage } from "../../game/net/protocol/Compete";
import MsgMgr from "../../lib/event/MsgMgr";
import { AzstMsgEnum } from "./AzstConfig";

export class AzstData {
  private _azstMessage: CompeteExtraMessage = null;

  private _logMessage: CompeteLogMessage[] = null;

  private _rankMessage: CompeteOwnRankMessage = null;

  public get azstMessage(): CompeteExtraMessage {
    if (!this._azstMessage) {
      this._azstMessage = {
        /** 挑战券槽位大小 */
        ticketSize: -1,
        /** 今日剩余的免费刷新次数 */
        freeFreshCount: -1,
        /** 上一次触发修正剩余的免费刷新次数的时间  这个时间计算要满足以下规范：1.如果剩余的免费刷新次数的时间已经达到最大值，lastUpdateTime会更新为当前最新的时间; 2.如果使用免费刷新，刷新后的剩余免费剩余次数 ==  配置文件中配置的最大值 - 1，则lastUpdateTime更新为当前最新的时间；否则不变; 3.在1、2的基础上，如果剩余免费刷新次数 < 配置文件中配置的最大值 && 如果系统当前时间 - lastUpdateTime > 恢复一次免费刷新次数的时间的话，则lastUpdateTime =  lastUpdateTime + 新增加的次数 * 谈心精力值恢复一次的时间； */
        lastUpdateTime: -1,
        /** 距离日榜结束还有的时间 */
        dailyRewardDeadline: -1,
        /** 距离日周结束还有的时间 */
        weekRewardDeadline: -1,
        /** 对手的名单 */
        battlerList: [],
      };
    }

    return this._azstMessage;
  }

  public set azstMessage(value: CompeteExtraMessage) {
    this._azstMessage = value;
  }

  public get battlerList() {
    return this.azstMessage.battlerList;
  }

  public set battlerList(val) {
    this.azstMessage.battlerList = val;
    MsgMgr.emit(AzstMsgEnum.OPPONENT_REFRESH);
  }

  public get dailyRewardDeadline() {
    return this.azstMessage.dailyRewardDeadline;
  }
  public set dailyRewardDeadline(value: number) {
    this.azstMessage.dailyRewardDeadline = value;
  }

  public get weekRewardDeadline() {
    return this.azstMessage.weekRewardDeadline;
  }
  public set weekRewardDeadline(value: number) {
    this.azstMessage.weekRewardDeadline = value;
  }

  public get logMessage(): CompeteLogMessage[] {
    return this._logMessage;
  }

  public set logMessage(value: CompeteLogMessage[]) {
    this._logMessage = value;
    MsgMgr.emit(AzstMsgEnum.LOG_REFRESH);
  }

  public get freeFreshCount() {
    return this.azstMessage.freeFreshCount;
  }

  public set freeFreshCount(value: number) {
    this.azstMessage.freeFreshCount = value;
    MsgMgr.emit(AzstMsgEnum.FREE_COUNT_UPDATE);
  }

  public get lastUpdateTime() {
    return this.azstMessage.lastUpdateTime;
  }

  public set lastUpdateTime(value: number) {
    this.azstMessage.lastUpdateTime = value;
    MsgMgr.emit(AzstMsgEnum.FREE_TIME_UPDATE);
  }

  public get rankMessage() {
    return this._rankMessage;
  }

  public set rankMessage(value: CompeteOwnRankMessage) {
    this._rankMessage = value;
  }

  public get rankList() {
    return this._rankMessage.rankList;
  }

  public get rank() {
    return this._rankMessage.rank;
  }

  public set rank(value: number) {
    this._rankMessage.rank = value;
  }

  public get point() {
    return this._rankMessage.point;
  }

  public setPointRank(point: number, rank: number) {
    this._rankMessage.rank = rank;
    this._rankMessage.point = point;
    MsgMgr.emit(AzstMsgEnum.POINT_CHANGE);
  }

  /**设置日志的切磋属性
   * @param  logId 日志id
   */
  public setLogRevenge(logId: number) {
    for (let i = 0; i < this._logMessage.length; i++) {
      let info = this._logMessage[i];
      if (info.id == logId) {
        info.revenge = true;
        break;
      }
    }
    MsgMgr.emit(AzstMsgEnum.LOG_REFRESH);
  }
}
