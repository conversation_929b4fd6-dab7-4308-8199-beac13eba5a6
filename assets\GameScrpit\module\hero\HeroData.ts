import MsgEnum from "../../game/event/MsgEnum";
import { AttrEnum, HeroAttrEnum, IAttr } from "../../game/GameDefine";
import { IConfigHero, IConfigHeroPicture } from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { HeroPictureMessage } from "../../game/net/protocol/City";
import { HeroMessage } from "../../game/net/protocol/Hero";
import MsgMgr from "../../lib/event/MsgMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { times } from "../../lib/utils/NumbersUtils";
import { FriendModule } from "../friend/FriendModule";
import { HeroSort, HeroType } from "./HeroConfig";
import { HeroModule } from "./HeroModule";
const log = Logger.getLoger(LOG_LEVEL.STOP);
export class HeroData {
  private _data: { [key: number]: HeroMessage } = {};
  // 已有hero 的 id List
  private _ownHeroList: IConfigHero[] = [];
  // 未拥有 的 id List
  private unOwnedHeroList: IConfigHero[] = [];

  private _personHaloList: number[] = [];
  private _godHaloList: number[] = [];
  private _allHeroHaloList: number[] = [];

  private _pictureMessage: HeroPictureMessage;

  public init() {
    this._data = {};
    this._ownHeroList = [];
    this.unOwnedHeroList = [];
    this._personHaloList = [];
    this._godHaloList = [];
    this._allHeroHaloList = [];
    this._pictureMessage = null;
  }
  public get personHaloList(): number[] {
    return this._personHaloList;
  }
  public get godHaloList(): number[] {
    return this._godHaloList;
  }
  public get allHeroHaloList(): number[] {
    return this._allHeroHaloList;
  }
  private async initHeros() {
    log.log(this._data);
    this._ownHeroList = [];
    this.unOwnedHeroList = [];
    this._personHaloList = [];
    this._godHaloList = [];
    this._allHeroHaloList = [];

    // let strlog = "[";
    // let friends = {};

    // log.log(friends);
    Object.values(JsonMgr.instance.jsonList.c_hero).forEach((val: IConfigHero) => {
      // strlog = strlog.concat(`${val.petId},`);
      if (this._data[val.id]) {
        // val.level = this._data[val.id].level;
        // val.remoteData = this._data[val.id];
        this._ownHeroList.push(val);
      } else {
        this.unOwnedHeroList.push(val);
      }
    });
    this._ownHeroList.forEach((val) => {
      let heroMessage = this.getHeroMessage(val.id);
      if (heroMessage.skillMap[2103]) {
        this.personHaloList.push(heroMessage.skillMap[2103]);
      }
      if (heroMessage.skillMap[2203]) {
        this.godHaloList.push(heroMessage.skillMap[2203]);
      }
      if (heroMessage.skillMap[2302]) {
        this.allHeroHaloList.push(heroMessage.skillMap[2302]);
      }
    });
    // strlog = strlog.concat(`]`);
    // log.log(strlog);
  }
  //未拥有排序
  private sortUnOwned(a: IConfigHero, b: IConfigHero) {
    if (a.quality == b.quality) {
      return b.id - a.id;
    }
    return b.quality - a.quality;
  }
  //默认排序
  private sortDefault(a: IConfigHero, b: IConfigHero) {
    if (b.color == a.color) {
      let aHeroPower = this.getHeroPower(a.id);
      let bHeroPower = this.getHeroPower(b.id);
      if (aHeroPower == bHeroPower) {
        return b.id - a.id;
      }
      return bHeroPower - aHeroPower;
    }
    return b.color - a.color;
  }
  //战力排序
  private sortPower(a: IConfigHero, b: IConfigHero) {
    let aHeroPower = this.getHeroPower(a.id);
    let bHeroPower = this.getHeroPower(b.id);
    if (aHeroPower == bHeroPower) {
      if (a.color == b.color) {
        return b.id - a.id;
      }
      return b.color - a.color;
    }
    return bHeroPower - aHeroPower;
  }
  //资质排序
  private sortAbility(a: IConfigHero, b: IConfigHero) {
    let aHeroQuality = HeroModule.service.getHeroQuality(a.id);
    let bHeroQuality = HeroModule.service.getHeroQuality(b.id);
    if (aHeroQuality == bHeroQuality) {
      if (b.color == a.color) {
        return a.id - b.id;
      }
      return b.color - a.color;
    }
    return bHeroQuality - aHeroQuality;
  }
  //精进排序
  private sortLevel(a: IConfigHero, b: IConfigHero) {
    if (this.getHeroImprintsLv(a.id) == this.getHeroImprintsLv(b.id)) {
      return this.sortDefault(a, b);
    }
    return this.getHeroImprintsLv(b.id) - this.getHeroImprintsLv(a.id);
  }

  // 获取英雄列表
  public get allHeroList(): IConfigHero[] {
    return this._ownHeroList.concat(this.unOwnedHeroList);
  }

  public get ownHeroList(): IConfigHero[] {
    return this._ownHeroList;
  }

  public set pictureMessage(pictureMessage: HeroPictureMessage) {
    this._pictureMessage = pictureMessage;
  }
  public get pictureMessage(): HeroPictureMessage {
    return this._pictureMessage;
  }

  public getOwnedHeros(sort?: HeroSort, type: HeroType = HeroType.ALL): IConfigHero[] {
    // this.hasHeroList.sort()
    if (!sort) {
      return this._ownHeroList.filter((vaule) => {
        return type == HeroType.ALL || vaule.type == type;
      });
    }

    switch (sort) {
      case HeroSort.DEFAULT:
        return this._ownHeroList
          .filter((vaule) => {
            return type == HeroType.ALL || vaule.type == type;
          })
          .sort((a, b) => this.sortDefault(a, b));
      case HeroSort.ABILITY:
        return this._ownHeroList
          .filter((vaule) => {
            return type == HeroType.ALL || vaule.type == type;
          })
          .sort((a, b) => this.sortAbility(a, b));
      case HeroSort.LEVEL:
        return this._ownHeroList
          .filter((vaule) => {
            return type == HeroType.ALL || vaule.type == type;
          })
          .sort((a, b) => this.sortLevel(a, b));
      case HeroSort.POWER:
        return this._ownHeroList
          .filter((vaule) => {
            return type == HeroType.ALL || vaule.type == type;
          })
          .sort((a, b) => this.sortPower(a, b));
    }
  }
  public getUnOwnedHeros(type: HeroType = HeroType.ALL): IConfigHero[] {
    return this.unOwnedHeroList
      .filter((vaule) => {
        return type == HeroType.ALL || vaule.type == type;
      })
      .sort(this.sortUnOwned);
  }
  public setHeroMessageList(res: HeroMessage[]) {
    log.warn("战将信息加载完成===", res);
    res.forEach((val) => {
      this._data[val.heroId] = val;
    });
    this.initHeros();
    log.log(res);
  }
  /**
   * 获取英英雄信息
   * @param heroId
   * @returns
   */
  public getHeroMessage(heroId: number) {
    return this._data[heroId];
  }
  /**
   * 更新英雄信息
   * @param hero
   */
  public setHeroMessage(hero: HeroMessage) {
    this._data[hero.heroId] = hero;
    this.initHeros();
    // HeroModule.instance.notifyDataChange();
    MsgMgr.emit(MsgEnum.ON_HERO_UPDATE, hero);
    // EventMgr.emit(MsgEnum.ON_HERO_UPDATE, hero);
  }
  /**
   * 获取精进（印记）等级
   * @param heroId a
   * @returns
   */
  public getHeroImprintsLv(heroId: number) {
    let hero = this.getHeroMessage(heroId);
    return hero?.skillMap[3100] ?? 0;
  }
  public getHeroHaloLv(heroId: number, haloId: number) {
    let hero = this.getHeroMessage(heroId);
    return hero?.skillMap[haloId] ?? 0;
  }

  /**
   * 获得已拥有英雄数量
   * @returns
   */
  public getOwnedHerosNums(): number {
    return this._ownHeroList.length;
  }

  //通过id获得英雄战力
  public getHeroPower(heroId: number) {
    if (!this.getHeroMessage(heroId)) {
      //如果英雄信息不存在
      return 0;
    }
    return this.HeroPower(heroId);
  }
  // 战将战力
  public HeroPower(heroId: number) {
    // int(a*(1+b)+c)  a:基础战力;b:各种加成百分比;c:各种战力加成固定值
    // 基础战力 = 资质 * level对应战力值
    let heroMessage = this.getHeroMessage(heroId);
    let a = HeroModule.service.getHeroQuality(heroId) * HeroModule.config.getHeroLvData(heroMessage.level).powerAdd;

    let b = 0;

    let c = 0;

    return a * (1 + b) + c;
  }
  //获取英雄总战力
  public getTotalPower() {
    let heroSkillTotal = 0;
    Object.values(this._ownHeroList).forEach((val) => {
      heroSkillTotal += this.HeroPower(val.id);
    });
    return heroSkillTotal;
  }
  // TODO:

  public getHeroTotalLevel(): number {
    let totalLevel = 0;
    this._ownHeroList.forEach((val) => {
      let heroMessage = this.getHeroMessage(val.id);
      totalLevel += heroMessage.level;
    });
    return totalLevel;
  }

  public canLevelTen(): boolean {
    // HeroModule.config.getHeroLvData(heroId);
    if (this.getHeroTotalLevel() >= 100) {
      return true;
    }
    if (this.getOwnedHerosNums() >= 10) {
      return true;
    }
    return false;
  }

  /**
   * 获取单个挚友对战将的战力加成
   * @param heroId
   * @param friendId
   * @returns
   */
  public getHeroPowerAddByFriend(heroId: number, friendId: number): number {
    // let hero = HeroModule.config.getHeroInfo(heroId);
    let heroBaseAttr = HeroModule.service.getHeroBaseAttr(heroId); //英雄基础属性
    let friendAttrAdd = FriendModule.service.getHeroAttrAddByFriendId(friendId);
    // 挚友属性增加部分
    let attack =
      times(heroBaseAttr[AttrEnum.攻击_2], friendAttrAdd[HeroAttrEnum.战将攻击百分比_12] / 10000) +
      friendAttrAdd[AttrEnum.攻击_2];
    let defense =
      times(heroBaseAttr[AttrEnum.防御_3], friendAttrAdd[HeroAttrEnum.战将防御百分比_13] / 10000) +
      friendAttrAdd[AttrEnum.防御_3];
    let blood =
      times(heroBaseAttr[AttrEnum.生命_1], friendAttrAdd[HeroAttrEnum.战将生命百分比_11] / 10000) +
      friendAttrAdd[AttrEnum.生命_1];
    let attackPowerRate = JsonMgr.instance.jsonList.c_attribute[2].powerRate1List[1];
    let defensePowerRate = JsonMgr.instance.jsonList.c_attribute[3].powerRate1List[1];
    let bloodPowerRate = JsonMgr.instance.jsonList.c_attribute[1].powerRate1List[1];
    let powerAdd = attack * attackPowerRate + defense * defensePowerRate + blood * bloodPowerRate;
    return powerAdd;
  }

  /**
   * 获取图鉴属性加成
   * @param pictureId
   * @returns
   */
  public getHeroPictureAttrMap(pictureId: number): IAttr {
    let attr = new IAttr();
    let data: IConfigHeroPicture = JsonMgr.instance.jsonList.c_heroPicture[pictureId];
    let pictureLv = this.pictureMessage?.pictureMap[data.id] ?? 0;
    for (let i = 0; i < pictureLv; i++) {
      for (let i1 = 0; i1 < data.attrAdd[i].length; i1++) {
        let attrKey = data.attrAdd[i][i1++];
        attr[attrKey] ??= 0;
        attr[attrKey] += data.attrAdd[i][i1];
      }
    }
    return attr;
  }
  /**
   * 获取最新的图鉴属性加成
   * @param pictureId
   * @returns
   */
  public getHeroPictureLastestAttrMap(pictureId: number): IAttr {
    let attr = new IAttr();
    let data: IConfigHeroPicture = JsonMgr.instance.jsonList.c_heroPicture[pictureId];
    let pictureLv = this.pictureMessage?.pictureMap[data.id] ?? 0;
    let i = pictureLv - 1;
    for (let i1 = 0; i1 < data.attrAdd[i]?.length; i1++) {
      let attrKey = data.attrAdd[i][i1++];
      attr[attrKey] ??= 0;
      attr[attrKey] += data.attrAdd[i][i1];
    }
    return attr;
  }
  public getAllHeroPictureAttrMap() {
    let keys = Object.keys(this.pictureMessage?.pictureMap);
    let attrTotal = new IAttr();
    for (let i = 0; i < keys.length; i++) {
      let attr = this.getHeroPictureAttrMap(Number(keys[i]));
      Object.keys(attr).forEach((key) => {
        attrTotal[key] ??= 0;
        attrTotal[key] += attr[key];
      });
    }
    return attrTotal;
  }
}
