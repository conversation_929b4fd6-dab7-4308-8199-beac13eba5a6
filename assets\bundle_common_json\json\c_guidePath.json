{"1": {"id": 1, "name": "弟子系统", "typeId": 10, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_pupil", "checkType": 1}, "2": {"id": 2, "name": "演武场", "typeId": 11, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_tiao_zhan", "checkType": 1}, "3": {"id": 3, "typeId": 11, "parentName": "btn_tiao_zhan_close", "nodeClickName": "btn_yan_wu_chang", "checkType": 1}, "4": {"id": 4, "name": "至宝系统", "typeId": 12, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "5": {"id": 5, "typeId": 12, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_horse", "checkType": 1}, "6": {"id": 6, "name": "兽魂系统", "typeId": 13, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_pet", "checkType": 1}, "7": {"id": 7, "name": "驿站系统", "typeId": 14, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_tiao_zhan", "checkType": 1}, "8": {"id": 8, "parentName": "btn_tiao_zhan_close", "nodeClickName": "btn_post", "checkType": 1}, "9": {"id": 9, "name": "福地洞天", "typeId": 15, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_farm", "checkType": 1}, "10": {"id": 10, "name": "战盟系统", "typeId": 16, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_xian_meng", "checkType": 1}, "11": {"id": 11, "name": "游历系统", "typeId": 17, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_tiao_zhan", "checkType": 1}, "12": {"id": 12, "typeId": 17, "parentName": "btn_tiao_zhan_close", "nodeClickName": "btn_you_li", "checkType": 1}, "13": {"id": 13, "name": "点击女娲", "typeId": 18, "routeNameList": ["UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_home", "checkType": 1, "hideShadow": 1}, "14": {"id": 14, "typeId": 18, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_collect", "redo": 1, "checkType": 2, "msg": "CLICK_NVWA", "hideShadow": 1}, "15": {"id": 15, "name": "关卡通关", "typeId": 19, "routeNameList": ["UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_home", "checkType": 1}, "16": {"id": 16, "typeId": 19, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_fight", "checkType": 1}, "17": {"id": 17, "name": "战将", "typeId": 20, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_hero", "checkType": 1}, "18": {"id": 18, "name": "游历引导", "typeId": 21, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_tiao_zhan", "checkType": 1}, "19": {"id": 19, "typeId": 21, "parentName": "btn_tiao_zhan_close", "nodeClickName": "btn_you_li", "checkType": 1}, "20": {"id": 20, "typeId": 21, "routeNameList": ["UITravel"], "parentName": "UITravel", "nodeClickName": "btn_travel", "checkType": 1, "noFly": 1}, "21": {"id": 21, "name": "据点建造", "typeId": 22, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "city_${buildId}", "checkType": 1}, "22": {"id": 22, "name": "据点升级", "typeId": 23, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "city_${buildId}", "checkType": 1}, "23": {"id": 23, "typeId": 23, "routeNameList": ["UICityDetail"], "parentName": "UICityDetail", "nodeClickName": "btn_shengji", "checkType": 1, "hideShadow": 1}, "24": {"id": 24, "name": "据点招募", "typeId": 24, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "city_${buildId}", "checkType": 1}, "25": {"id": 25, "typeId": 24, "routeNameList": ["UICityDetail"], "parentName": "UICityDetail", "nodeClickName": "btn_haozhao", "checkType": 2, "hideShadow": 1}, "26": {"id": 26, "name": "身份提升", "typeId": 25, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "27": {"id": 27, "typeId": 25, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_shengji", "checkType": 1}, "28": {"id": 28, "typeId": 25, "routeNameList": ["UIPlayerLevelUp"], "parentName": "UIPlayerLevelUp", "nodeClickName": "btn_post_lv", "checkType": 1}, "29": {"id": 29, "name": "升级女娲", "typeId": 26, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_level_up", "checkType": 1}, "30": {"id": 30, "typeId": 26, "routeNameList": ["UIEnergyUpgrade"], "parentName": "UIEnergyUpgrade", "nodeClickName": "btn_upgrade", "checkType": 1}, "31": {"id": 31, "name": "仙友赠送", "typeId": 27, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "32": {"id": 32, "typeId": 27, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_friend", "checkType": 1}, "33": {"id": 33, "typeId": 27, "routeNameList": ["UIFriendMain"], "parentName": "UIFriendMain", "nodeClickName": "friend_card_${friendId}", "checkType": 1}, "34": {"id": 34, "typeId": 27, "routeNameList": ["UIFriendFoster"], "parentName": "UIFriendFoster", "nodeClickName": "give_gift", "checkType": 1}, "35": {"id": 35, "name": "仙友互动", "typeId": 28, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "36": {"id": 36, "typeId": 28, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_friend", "checkType": 1}, "37": {"id": 37, "typeId": 28, "routeNameList": ["UIFriendMain"], "parentName": "UIFriendMain", "nodeClickName": "btn_heart_talk", "checkType": 1}, "38": {"id": 38, "name": "仙友据点技能提升", "typeId": 29, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "39": {"id": 39, "typeId": 29, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_friend", "checkType": 1}, "40": {"id": 40, "typeId": 29, "routeNameList": ["UIFriendMain"], "parentName": "UIFriendMain", "nodeClickName": "friend_card_${friendId}", "checkType": 1}, "41": {"id": 41, "typeId": 29, "routeNameList": ["UIFriendFoster"], "parentName": "UIFriendFoster", "nodeClickName": "btn_skill", "checkType": 1}, "42": {"id": 42, "name": "仙友技能提升", "typeId": 30, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "43": {"id": 43, "typeId": 30, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_friend", "checkType": 1}, "44": {"id": 44, "typeId": 30, "routeNameList": ["UIFriendMain"], "parentName": "friends_list", "nodeClickName": "UIFriendCard", "checkType": 1}, "45": {"id": 45, "typeId": 30, "routeNameList": ["UIFriendFoster"], "parentName": "UIFriendFoster", "nodeClickName": "btn_skill", "checkType": 1}, "46": {"id": 46, "name": "弟子获得", "typeId": 31, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_pupil", "checkType": 1}, "47": {"id": 47, "typeId": 31, "routeNameList": ["UIPupilPage"], "parentName": "UIPupilPage", "nodeClickName": "btn_add", "checkType": 1}, "48": {"id": 48, "name": "弟子培养", "typeId": 32, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_pupil", "checkType": 1}, "49": {"id": 49, "typeId": 32, "routeNameList": ["UIPupilPage"], "parentName": "UIPupilPage", "nodeClickName": "btn_train", "checkType": 1}, "50": {"id": 50, "name": "弟子结拜", "typeId": 33, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_pupil", "checkType": 1}, "51": {"id": 51, "typeId": 33, "routeNameList": ["UIPupilPage"], "parentName": "UIPupilPage", "nodeClickName": "btn_tab_marry", "checkType": 1}, "52": {"id": 52, "name": "演武场挑战", "typeId": 34, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_tiao_zhan", "checkType": 1}, "53": {"id": 53, "typeId": 34, "parentName": "btn_tiao_zhan_close", "nodeClickName": "btn_yan_wu_chang", "checkType": 1}, "54": {"id": 54, "typeId": 34, "routeNameList": ["UIAzst"], "parentName": "UIAzst", "nodeClickName": "btn_showPk", "checkType": 1}, "55": {"id": 55, "name": "至宝升级", "typeId": 35, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "56": {"id": 56, "typeId": 35, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_horse", "checkType": 1}, "57": {"id": 57, "typeId": 35, "routeNameList": ["UIHorseList"], "parentName": "UIHorseList", "nodeClickName": "btn_upgrade", "checkType": 1}, "58": {"id": 58, "name": "玲珑宝鼎", "typeId": 36, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "59": {"id": 59, "typeId": 36, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_lottery", "checkType": 1}, "60": {"id": 60, "name": "古境", "typeId": 37, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_tiao_zhan", "checkType": 1}, "61": {"id": 61, "typeId": 37, "parentName": "btn_tiao_zhan_close", "nodeClickName": "btn_tian_huang_gu_jing", "checkType": 1}, "62": {"id": 62, "name": "灵兽升级", "typeId": 38, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_pet", "checkType": 1}, "63": {"id": 63, "typeId": 38, "routeNameList": ["UIPetList"], "parentName": "UIPetList", "nodeClickName": "UIPetCard_${petId}", "checkType": 1}, "64": {"id": 64, "name": "仙友战将技能升级", "typeId": 39, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "65": {"id": 65, "typeId": 39, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_friend", "checkType": 1}, "66": {"id": 66, "typeId": 39, "routeNameList": ["UIFriendMain"], "parentName": "UIFriendMain", "nodeClickName": "friend_card_${friendId}", "checkType": 1}, "67": {"id": 67, "typeId": 39, "routeNameList": ["UIFriendFoster"], "parentName": "UIFriendFoster", "nodeClickName": "btn_skill", "checkType": 1}, "68": {"id": 68, "typeId": 39, "routeNameList": ["UIFriendSkillUpgrade"], "parentName": "UIFriendSkillUpgrade", "nodeClickName": "tab_hero", "checkType": 1}, "69": {"id": 69, "name": "挑战战盟BOSS", "typeId": 40, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_club", "checkType": 1}, "70": {"id": 70, "name": "参与战盟捐献", "typeId": 41, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_club", "checkType": 1}, "71": {"id": 71, "name": "洞天采集", "typeId": 42, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_farm", "checkType": 1}, "72": {"id": 72, "name": "洞天抢夺", "typeId": 43, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_farm", "checkType": 1}, "73": {"id": 73, "typeId": 43, "routeNameList": ["UIFarmMain"], "parentName": "UIFarmMain", "nodeClickName": "btn_find", "checkType": 1}, "74": {"id": 74, "name": "洞天探寻", "typeId": 44, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_farm", "checkType": 1}, "75": {"id": 75, "typeId": 44, "routeNameList": ["UIFarmMain"], "parentName": "UIFarmMain", "nodeClickName": "btn_find", "checkType": 1}, "76": {"id": 76, "name": "挑战关卡", "typeId": 45, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_fight", "checkType": 1}, "77": {"id": 77, "name": "兽魂刷新", "typeId": 46, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "78": {"id": 78, "typeId": 46, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_soul", "checkType": 1}, "79": {"id": 79, "typeId": 46, "routeNameList": ["UISoulList"], "parentName": "UISoulList", "nodeClickName": "btn_free_refresh", "checkType": 1, "msg": "BUILD"}, "80": {"id": 80, "name": "福地洞天商店引导", "typeId": 47, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_farm", "checkType": 1}, "81": {"id": 81, "typeId": 47, "routeNameList": ["UIFarmMain"], "parentName": "UIFarmMain", "nodeClickName": "btn_shop", "checkType": 1}, "82": {"id": 82, "name": "据点建造", "typeId": 48, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "city_${buildId}", "checkType": 1}, "83": {"id": 83, "typeId": 48, "checkType": 2, "msg": "REWARD_END", "hideShadow": 1}, "84": {"id": 84, "name": "主线任务", "typeId": 49, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_task", "checkType": 1, "hideShadow": 1}, "85": {"id": 85, "typeId": 49, "routeNameList": ["UITaskMainPopup"], "parentName": "UITaskMainPopup", "nodeClickName": "btn_huangse", "checkType": 1, "hideShadow": 1}, "86": {"id": 86, "name": "主线任务-领取奖励", "typeId": 50, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_task", "checkType": 1, "hideShadow": 1}, "87": {"id": 87, "typeId": 50, "checkType": 2, "msg": "REWARD_END", "hideShadow": 1}, "88": {"id": 88, "name": "建造-引导-关闭", "typeId": 51, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "city_${buildId}", "checkType": 1}, "89": {"id": 89, "typeId": 51, "routeNameList": ["UICityBuild"], "parentName": "UICityBuild", "nodeClickName": "dialog_close", "checkType": 1}, "90": {"id": 90, "name": "据点招募-关闭", "typeId": 52, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "city_${buildId}", "checkType": 1}, "91": {"id": 91, "typeId": 52, "routeNameList": ["UICityDetail"], "parentName": "UICityDetail", "nodeClickName": "btn_haozhao", "redo": 1, "checkType": 2, "msg": "BUILD_HAOZHAO", "hideShadow": 1}, "92": {"id": 92, "typeId": 52, "routeNameList": ["UICityDetail"], "parentName": "UICityDetail", "nodeClickName": "btn_close", "checkType": 1}, "93": {"id": 93, "name": "升级女娲", "typeId": 53, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_level_up", "checkType": 1}, "94": {"id": 94, "typeId": 53, "routeNameList": ["UIEnergyUpgrade"], "parentName": "UIEnergyUpgrade", "nodeClickName": "btn_upgrade", "checkType": 1}, "95": {"id": 95, "typeId": 53, "checkType": 2, "msg": "REWARD_END", "hideShadow": 1}, "96": {"id": 96, "typeId": 53, "routeNameList": ["UIEnergyUpgrade"], "parentName": "UIEnergyUpgrade", "nodeClickName": "dialog_close", "checkType": 1}, "97": {"id": 97, "name": "身份提升", "typeId": 54, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "98": {"id": 98, "typeId": 54, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_shengji", "checkType": 1, "hideShadow": 1}, "99": {"id": 99, "typeId": 54, "routeNameList": ["UIPlayerLevelUp"], "parentName": "UIPlayerLevelUp", "nodeClickName": "btn_post_lv", "checkType": 1, "hideShadow": 1}, "100": {"id": 100, "typeId": 54, "checkType": 2, "msg": "REWARD_END", "hideShadow": 1}, "101": {"id": 101, "typeId": 54, "routeNameList": ["UIPlayerLevelUp"], "parentName": "UIPlayerLevelUp", "nodeClickName": "btn_close", "checkType": 1}, "102": {"id": 102, "name": "战将升级", "typeId": 55, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_hero", "checkType": 1}, "103": {"id": 103, "typeId": 55, "routeNameList": ["UIHeroMain"], "parentName": "UIHeroMain", "nodeClickName": "hero_card_10102", "checkType": 1}, "104": {"id": 104, "typeId": 55, "routeNameList": ["UIHeroDetail"], "parentName": "UIHeroDetail", "nodeClickName": "btn_level_up", "redo": 1, "checkType": 2, "msg": "HERO_LEVEL_UP", "hideShadow": 1}, "105": {"id": 105, "name": "冲关引导", "typeId": 56, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_fight", "checkType": 1}, "106": {"id": 106, "typeId": 56, "routeNameList": ["UILevelGame"], "parentName": "UILevelGame", "nodeClickName": "btn_fight", "checkType": 2, "msg": "SEE_BOSS", "hideShadow": 1}, "107": {"id": 107, "typeId": 56, "routeNameList": ["UILevelBoss"], "parentName": "UILevelBoss", "nodeClickName": "btn_tiaozhan", "checkType": 1, "hideShadow": 1}, "108": {"id": 108, "typeId": 56, "checkType": 2, "msg": "FIGHT_FINISH", "hideShadow": 1}, "109": {"id": 109, "typeId": 56, "routeNameList": ["UILevelGame"], "parentName": "UILevelGame", "nodeClickName": "btn_close", "checkType": 1}, "110": {"id": 110, "name": "冲关按钮", "typeId": 57, "routeNameList": ["UILevelGame"], "parentName": "UILevelGame", "nodeClickName": "btn_fight", "checkType": 1}, "111": {"id": 111, "name": "仙友引导", "typeId": 58, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "112": {"id": 112, "typeId": 58, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_friend", "checkType": 1}, "113": {"id": 113, "name": "使用道具", "typeId": 59, "routeNameList": ["UIGameMap", "UITerritory"], "parentName": "UIMain", "nodeClickName": "btn_knapsack", "checkType": 1}, "114": {"id": 114, "name": "仙友目标", "typeId": 60, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "115": {"id": 115, "typeId": 60, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_friendmubiao", "checkType": 1}, "116": {"id": 116, "name": "VIP豪礼", "typeId": 61, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "117": {"id": 117, "typeId": 61, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_vip_lv", "checkType": 1}, "118": {"id": 118, "name": "七日登录", "typeId": 62, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "119": {"id": 119, "typeId": 62, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_activity_7", "checkType": 1}, "120": {"id": 120, "name": "图鉴兑换", "typeId": 63, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "121": {"id": 121, "typeId": 63, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_tuJian", "checkType": 1}, "122": {"id": 122, "name": "灵兽洗练", "typeId": 64, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_pet", "checkType": 1}, "123": {"id": 123, "typeId": 64, "routeNameList": ["UIHeroDetail"], "parentName": "UIHeroPet", "nodeClickName": "btn", "checkType": 1}, "124": {"id": 124, "name": "玲珑夺宝1", "typeId": 65, "routeNameList": ["UILottery"], "parentName": "UILottery", "nodeClickName": "btn_one", "checkType": 1}, "125": {"id": 125, "typeId": 65, "checkType": 2, "msg": "REWARD_END", "hideShadow": 1}, "126": {"id": 126, "name": "玲珑夺宝2", "typeId": 66, "routeNameList": ["UILottery"], "parentName": "UILottery", "nodeClickName": "btn_luckyShop", "checkType": 1}, "127": {"id": 127, "name": "仙玉充值", "typeId": 67, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "128": {"id": 128, "typeId": 67, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_shopCenter", "checkType": 1}, "129": {"id": 129, "typeId": 67, "routeNameList": ["UIShopCenter"], "parentName": "UIShopCenter", "nodeClickName": "btn_rmb", "checkType": 1}, "130": {"id": 130, "name": "弟子建造", "typeId": 68, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_pupil", "checkType": 1}, "131": {"id": 131, "name": "福地建造", "typeId": 69, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_farm", "checkType": 1}, "132": {"id": 132, "name": "进阶皮肤换装", "typeId": 70, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_shengji", "checkType": 1}, "133": {"id": 133, "typeId": 70, "routeNameList": ["UIPlayerLevelUp"], "parentName": "UIPlayerLevelUp", "nodeClickName": "btn_go_skin_main", "checkType": 1}, "134": {"id": 134, "typeId": 70, "routeNameList": ["UIPlayerLevelUp"], "parentName": "UIPlayerLevelUp", "nodeClickName": "btn_skin_identity", "checkType": 1}, "135": {"id": 135, "name": "据点大装饰", "typeId": 71, "routeNameList": ["UIGameMap"], "parentName": "trim_${buildId}", "nodeClickName": "btn_build", "checkType": 1}, "136": {"id": 136, "name": "小偷入口", "typeId": 72, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_xiao_tou_icon", "checkType": 1}, "137": {"id": 137, "typeId": 72, "routeNameList": ["UIThief"], "parentName": "UIThief", "nodeClickName": "btn_lvse", "checkType": 1}, "138": {"id": 138, "name": "小偷-初始引导", "typeId": 73, "routeNameList": ["UIGameMap"], "parentName": "path_thief_101", "nodeClickName": "render", "checkType": 1}, "139": {"id": 139, "name": "小偷-日常引导", "typeId": 74, "routeNameList": ["UIGameMap"], "parentName": "path_thief_201", "nodeClickName": "render", "checkType": 1}, "140": {"id": 140, "name": "弟子内部引导招徒", "typeId": 75, "routeNameList": ["UIPupilPage"], "parentName": "UIPupilPage", "nodeClickName": "btn_add", "checkType": 1}, "141": {"id": 141, "name": "弟子内部引导修练", "typeId": 76, "routeNameList": ["UIPupilPage"], "parentName": "UIPupilPage", "nodeClickName": "btn_train", "checkType": 1}, "142": {"id": 142, "typeId": 76, "routeNameList": ["UIPupilPage"], "parentName": "UIPupilPage", "nodeClickName": "btn_train", "checkType": 1}, "143": {"id": 143, "name": "号招宝箱", "typeId": 77, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "city_${buildId}", "checkType": 1}, "144": {"id": 144, "typeId": 77, "routeNameList": ["UICityDetail"], "parentName": "UICityDetail", "nodeClickName": "btn_hao_zhao_box", "checkType": 1}, "145": {"id": 145, "typeId": 77, "routeNameList": ["UIHaoZhaoBox"], "parentName": "UIHaoZhaoBox", "nodeClickName": "btn_open", "checkType": 1}, "146": {"id": 146, "name": "三界小家s%达到s%级", "typeId": 78, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_sanjiexiaojia", "checkType": 1}, "147": {"id": 147, "typeId": 78, "routeNameList": ["UISanJieXiaoJia"], "parentName": "home_${homeId}", "nodeClickName": "spr_message_bg", "checkType": 1}, "148": {"id": 148, "name": "首充奖励领取引导", "typeId": 79, "routeNameList": ["UIShouChong"], "parentName": "UIShouChong", "nodeClickName": "btn_lingqu_6_0", "checkType": 1, "hideShadow": 1}, "149": {"id": 149, "typeId": 79, "checkType": 2, "msg": "REWARD_END", "hideShadow": 1}, "150": {"id": 150, "typeId": 79, "routeNameList": ["UIShouChong"], "parentName": "UIShouChong", "nodeClickName": "btn_close", "checkType": 1}, "151": {"id": 151, "name": "换熊猫皮肤", "typeId": 80, "routeNameList": ["UIGameMap"], "parentName": "UIMain", "nodeClickName": "btn_territory", "checkType": 1}, "152": {"id": 152, "typeId": 80, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_shengji", "checkType": 1, "hideShadow": 1}, "153": {"id": 153, "typeId": 80, "routeNameList": ["UIPlayerLevelUp"], "parentName": "UIPlayerLevelUp", "nodeClickName": "btn_go_skin_main", "checkType": 1}, "154": {"id": 154, "typeId": 80, "routeNameList": ["UIPlayerLevelUp"], "parentName": "btn_skin_card_1810", "nodeClickName": "btn_skin_active", "checkType": 1, "hideShadow": 1}, "155": {"id": 155, "typeId": 80, "routeNameList": ["UICostConfirm"], "parentName": "UICostConfirm", "nodeClickName": "btn_yes", "checkType": 1}, "156": {"id": 156, "typeId": 80, "routeNameList": ["UISkinOpen"], "parentName": "UISkinOpen", "nodeClickName": "btn_close", "checkType": 1}, "157": {"id": 157, "typeId": 80, "routeNameList": ["UIPlayerLevelUp"], "parentName": "UIPlayerLevelUp", "nodeClickName": "btn_use_skin", "checkType": 1}, "158": {"id": 158, "typeId": 80, "routeNameList": ["UIPlayerLevelUp"], "parentName": "UIPlayerLevelUp", "nodeClickName": "btn_close", "checkType": 1}, "159": {"id": 159, "name": "据点大装饰", "typeId": 81, "routeNameList": ["UIGameMap"], "parentName": "trim_${buildId}", "nodeClickName": "btn_build", "checkType": 1}, "160": {"id": 160, "typeId": 81, "checkType": 2, "msg": "REWARD_END", "hideShadow": 1}, "161": {"id": 161, "name": "七日签到", "typeId": 82, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_activity_7", "checkType": 1, "hideShadow": 1}, "162": {"id": 162, "typeId": 82, "routeNameList": ["UIHdSeven"], "parentName": "UIHdSeven", "nodeClickName": "btn_qiandaolingqu", "checkType": 1, "hideShadow": 1}, "163": {"id": 163, "name": "充值好礼，每日礼包", "typeId": 83, "routeNameList": ["UITerritory"], "parentName": "UITerritory", "nodeClickName": "btn_chongzhihaoli", "checkType": 1}, "164": {"id": 164, "typeId": 83, "routeNameList": ["UIFundMain"], "parentName": "FundBanner001", "nodeClickName": "btn_go", "checkType": 1, "hideShadow": 1}, "165": {"id": 165, "name": "灵兽建筑", "typeId": 84, "routeNameList": ["UIGameMap"], "parentName": "btn_pet", "nodeClickName": "btn_build", "checkType": 1, "hideShadow": 1}, "166": {"id": 166, "typeId": 85, "routeNameList": ["UIGameMap"], "parentName": "UIGameMap", "nodeClickName": "btn_fracture", "checkType": 1, "hideShadow": 1}, "167": {"id": 167, "typeId": 86, "routeNameList": ["UISanJieXiaoJia"], "parentName": "UISanJieXiaoJia", "nodeClickName": "btn_zhanjiang", "checkType": 1, "hideShadow": 1}, "-1": {"id": -1, "name": "", "typeId": 0, "routeNameList": [], "parentName": "", "nodeClickName": "", "redo": 0, "checkType": 0, "msg": "", "hideShadow": 0, "noFly": 0}}