import { _decorator, instantiate, Node, tween, v3 } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIFriendCard } from "./UIFriendCard";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Fri Jul 26 2024 14:25:15 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/friend/UIFriendHeartTalkOneKey.ts
 *
 */

@ccclass("UIFriendHeartTalkOneKey")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRIEND,
  url: "prefab/ui/UIFriendHeartTalkOneKey",
  nextHop: [],
  exit: "dialog_close",
})
export class UIFriendHeartTalkOneKey extends BaseCtrl {
  public playShowAni: boolean = true;
  private UIFriendCard: Node;
  private content: Node;
  private _chat_list: any[] = [];
  private _index: number = 0;

  protected update(dt: number): void {
    if (this.UIFriendCard && this.content && this._index < this._chat_list.length) {
      log.log(this._index);
      let card = instantiate(this.UIFriendCard);
      let chat = this._chat_list[this._index];
      card.getComponent(UIFriendCard).init(chat.friendId, chat.chatAdd);
      card.active = true;
      this.content.addChild(card);

      tween(card)
        .set({ scale: v3(0.1, 0.1, 1) })
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
        .start();
      this._index++;
    }
  }

  //=================================================
  public init(args: RouteShowArgs): void {
    super.init(args);
    log.log(args);
    let keys = Object.keys(args.payload.chatList);
    for (let i = 0; i < keys.length; i++) {
      let key = keys[i];
      this._chat_list.push({
        friendId: Number(key),
        chatAdd: args.payload.chatList[key],
      });
    }
    log.log(this._chat_list);
    log.log(this._index, this._chat_list.length);
  }
  protected start(): void {
    super.start();
    this.UIFriendCard = this.getNode("UIFriendCard");
    this.content = this.getNode("content");
    this.node.on(
      Node.EventType.TOUCH_END,
      () => {
        AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
        log.log("UIFriendHeartTalkOneKey---");
        this.closeBack();
      },
      this
    );
    this.content.on(
      Node.EventType.TOUCH_END,
      () => {
        AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
        log.log("UIFriendHeartTalkOneKey===");
      },
      this
    );
  }
}
