import { persistent } from "../../lib/decorators/persistent";
import { TimeUtils } from "../../lib/utils/TimeUtils";

export class PetViewModel {
  @persistent
  private _pet_xilian_tips_setting: string;

  @persistent
  private _pet_xilian_tips_setting_forever: string;
  public isShowXilianTipsSetting(): boolean {
    if (this._pet_xilian_tips_setting === TimeUtils.formatTimestamp(new Date().getTime(), "YYYY/MM/DD")) {
      return false;
    }
    return true;
  }
  public setXilianTipsSetting(hide: boolean) {
    if (hide) {
      this._pet_xilian_tips_setting = TimeUtils.formatTimestamp(new Date().getTime(), "YYYY/MM/DD");
    } else {
      this._pet_xilian_tips_setting = "";
    }
  }
  public isShowXilianTipsSettingForever(): boolean {
    if (this._pet_xilian_tips_setting_forever === "Forever") {
      return false;
    }
    return true;
  }
  public setXilianTipsSettingForever(hide: boolean) {
    if (hide) {
      this._pet_xilian_tips_setting_forever = "Forever";
    } else {
      this._pet_xilian_tips_setting_forever = "";
    }
  }
}
