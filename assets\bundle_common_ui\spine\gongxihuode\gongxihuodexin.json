{"skeleton": {"hash": "HNiR3dsKkITW97FXWBj+HgvJggY", "spine": "3.8.75", "x": -534.74, "y": -710.02, "width": 1081.04, "height": 1432.05, "images": "./images/", "audio": "E:/1/文件/PSD/动画拆分/G_恭喜获得"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 241.22, "rotation": -90.76, "x": 643.67, "y": 118.76}, {"name": "bone22", "parent": "bone", "x": -546.95, "y": -855.32}, {"name": "bone2", "parent": "bone22", "length": 99.58, "rotation": 77.3, "x": 119.8, "y": -124.45}, {"name": "bone3", "parent": "bone2", "length": 72.72, "rotation": 36.66, "x": 99.58}, {"name": "bone4", "parent": "bone3", "length": 54.51, "rotation": 8.5, "x": 72.72}, {"name": "bone5", "parent": "bone4", "length": 49.27, "rotation": -26.94, "x": 54.51}, {"name": "bone24", "parent": "bone22", "length": 53.93, "rotation": 100.61, "x": 161.85, "y": 45.4}, {"name": "bone6", "parent": "bone24", "length": 32.86, "rotation": 26.68, "x": 57.61, "y": -1.53}, {"name": "bone7", "parent": "bone6", "length": 42.9, "rotation": 10.32, "x": 32.86}, {"name": "bone8", "parent": "bone7", "length": 32.8, "rotation": -20.28, "x": 42.9}, {"name": "bone9", "parent": "bone22", "length": 33.98, "rotation": 122.09, "x": 136, "y": 158.57}, {"name": "bone10", "parent": "bone9", "length": 25.74, "rotation": -20.02, "x": 37.21, "y": -1.97}, {"name": "bone11", "parent": "bone10", "length": 48.22, "rotation": 31.57, "x": 26.98, "y": -0.25}, {"name": "bone12", "parent": "bone", "length": 112.89, "rotation": 88.08, "x": -394.8, "y": -416.98}, {"name": "bone15", "parent": "bone", "length": 176.24, "rotation": -0.38, "x": 111.9, "y": -383.11}, {"name": "bone16", "parent": "bone", "length": 114.54, "rotation": 91.65, "x": 643.33, "y": -433.09}, {"name": "bone21", "parent": "bone", "x": 732.27, "y": -638.26}, {"name": "bone17", "parent": "bone21", "length": 66.13, "rotation": 0.76, "x": -86.62, "y": -334.16}, {"name": "bone18", "parent": "bone17", "length": 39.54, "rotation": 13.5, "x": 66.13}, {"name": "bone23", "parent": "bone", "x": -488.55, "y": -539.95}, {"name": "bone25", "parent": "bone", "x": -461.18, "y": -573.79}, {"name": "bone26", "parent": "bone", "x": 732.27, "y": -634.72, "scaleY": -1}, {"name": "bone19", "parent": "bone26", "length": 66.13, "rotation": 0.76, "x": -86.62, "y": -334.16}, {"name": "bone20", "parent": "bone19", "length": 39.54, "rotation": 13.5, "x": 66.13}, {"name": "bone27", "parent": "bone", "length": 202.98, "rotation": 179.46, "x": 196.34, "y": 186.32}, {"name": "guadian_zi", "parent": "root", "length": 82.16, "x": -92.05, "y": 518.61, "color": "00ff11ff"}, {"name": "bone29", "parent": "bone", "length": 139.39, "rotation": 1.75, "x": 58.08, "y": -153.59}, {"name": "bone31", "parent": "root", "x": -160.92, "y": -449.83}, {"name": "bone30", "parent": "bone31", "rotation": -90.76, "x": 1.11, "y": 41.97}, {"name": "bone32", "parent": "root", "x": -5.11, "y": -449.83}, {"name": "bone33", "parent": "bone32", "rotation": -90.76, "x": 1.11, "y": 41.97}, {"name": "bone34", "parent": "root", "x": 152.57, "y": -449.83}, {"name": "bone35", "parent": "bone34", "rotation": -90.76, "x": 1.11, "y": 41.97}, {"name": "guadian", "parent": "root", "y": 433.82, "color": "54ff00ff"}, {"name": "guadian2", "parent": "root", "y": -337.88, "color": "54ff00ff"}], "slots": [{"name": "guang", "bone": "bone27", "attachment": "guang"}, {"name": "liusu", "bone": "bone21", "attachment": "liusu"}, {"name": "liusu2", "bone": "bone26", "attachment": "liusu"}, {"name": "zhong", "bone": "bone15", "attachment": "zhong"}, {"name": "di", "bone": "bone16", "attachment": "di"}, {"name": "shang", "bone": "bone12", "attachment": "shang"}, {"name": "hong<PERSON>o", "bone": "bone22", "attachment": "hong<PERSON>o"}, {"name": "guangdian", "bone": "bone23", "attachment": "guangdian"}, {"name": "guangdian2", "bone": "bone25", "attachment": "guangdian"}, {"name": "bone30", "bone": "bone", "attachment": "bone30"}], "skins": [{"name": "default", "attachments": {"honghuo": {"honghuo": {"type": "mesh", "uvs": [1, 1, 0.91667, 1, 0.83333, 1, 0.75, 1, 0.66667, 1, 0.58333, 1, 0.5, 1, 0.41667, 1, 0.33333, 1, 0.25, 1, 0.16667, 1, 0.08333, 1, 0, 1, 0, 0.8, 0, 0.6, 0, 0.4, 0, 0.2, 0, 0, 0.08333, 0, 0.16667, 0, 0.25, 0, 0.33333, 0, 0.41667, 0, 0.5, 0, 0.58333, 0, 0.66667, 0, 0.75, 0, 0.83333, 0, 0.91667, 0, 1, 0, 1, 0.2, 1, 0.4, 1, 0.6, 1, 0.8, 0.91667, 0.8, 0.83333, 0.8, 0.75, 0.8, 0.66667, 0.8, 0.58333, 0.8, 0.5, 0.8, 0.41667, 0.8, 0.33333, 0.8, 0.25, 0.8, 0.16667, 0.8, 0.08333, 0.8, 0.91667, 0.6, 0.83333, 0.6, 0.75, 0.6, 0.66667, 0.6, 0.58333, 0.6, 0.5, 0.6, 0.41667, 0.6, 0.33333, 0.6, 0.25, 0.6, 0.16667, 0.6, 0.08333, 0.6, 0.91667, 0.4, 0.83333, 0.4, 0.75, 0.4, 0.66667, 0.4, 0.58333, 0.4, 0.5, 0.4, 0.41667, 0.4, 0.33333, 0.4, 0.25, 0.4, 0.16667, 0.4, 0.08333, 0.4, 0.91667, 0.2, 0.83333, 0.2, 0.75, 0.2, 0.66667, 0.2, 0.58333, 0.2, 0.5, 0.2, 0.41667, 0.2, 0.33333, 0.2, 0.25, 0.2, 0.16667, 0.2, 0.08333, 0.2], "triangles": [0, 1, 33, 1, 34, 33, 33, 34, 32, 34, 45, 32, 32, 45, 31, 45, 56, 31, 31, 56, 30, 56, 67, 30, 45, 46, 56, 1, 2, 34, 2, 35, 34, 34, 35, 45, 35, 46, 45, 46, 57, 56, 2, 3, 35, 3, 36, 35, 35, 36, 46, 36, 47, 46, 46, 47, 57, 30, 67, 29, 67, 28, 29, 56, 57, 67, 57, 68, 67, 67, 68, 28, 68, 27, 28, 57, 58, 68, 68, 69, 27, 47, 58, 57, 48, 59, 58, 3, 4, 36, 4, 37, 36, 36, 37, 47, 37, 48, 47, 47, 48, 58, 4, 5, 37, 37, 38, 48, 48, 49, 59, 5, 38, 37, 38, 49, 48, 49, 60, 59, 5, 6, 38, 6, 39, 38, 38, 39, 49, 39, 50, 49, 49, 50, 60, 6, 7, 39, 7, 40, 39, 39, 40, 50, 40, 51, 50, 7, 8, 40, 8, 41, 40, 58, 69, 68, 69, 26, 27, 58, 59, 69, 59, 70, 69, 69, 70, 26, 70, 25, 26, 59, 60, 70, 60, 71, 70, 70, 71, 25, 71, 24, 25, 50, 61, 60, 60, 61, 71, 61, 72, 71, 71, 72, 24, 72, 23, 24, 50, 51, 61, 61, 62, 72, 72, 73, 23, 73, 22, 23, 51, 62, 61, 62, 73, 72, 40, 41, 51, 41, 52, 51, 51, 52, 62, 52, 63, 62, 62, 63, 73, 63, 74, 73, 73, 74, 22, 74, 21, 22, 8, 9, 41, 9, 42, 41, 41, 42, 52, 42, 53, 52, 52, 53, 63, 53, 64, 63, 63, 64, 74, 64, 75, 74, 74, 75, 21, 75, 20, 21, 9, 10, 42, 10, 43, 42, 42, 43, 53, 43, 54, 53, 53, 54, 64, 54, 65, 64, 64, 65, 75, 65, 76, 75, 75, 76, 20, 76, 19, 20, 10, 11, 43, 11, 44, 43, 43, 44, 54, 44, 55, 54, 54, 55, 65, 55, 66, 65, 65, 66, 76, 66, 77, 76, 76, 77, 19, 77, 18, 19, 11, 12, 44, 12, 13, 44, 44, 13, 55, 13, 14, 55, 55, 14, 66, 14, 15, 66, 66, 15, 77, 15, 16, 77, 77, 16, 18, 16, 17, 18], "vertices": [5, 8, 70.48, -141.78, 0.12958, 10, 21.34, -148.01, 0, 11, 26.48, -119.09, 0.38536, 12, 30.01, -113.72, 0.03406, 13, -56.83, -98.27, 0.45101, 5, 8, 44.03, -122.19, 0.17051, 10, -8.1, -133.29, 0, 11, -1.64, -101.98, 0.41739, 12, -2.27, -107.27, 0.03696, 13, -80.95, -75.87, 0.37514, 6, 7, 119.38, -85.31, 0.00477, 8, 17.58, -102.6, 0.29326, 10, -37.54, -118.56, 0, 11, -29.76, -84.86, 0.44935, 12, -34.55, -100.81, 0.02878, 13, -105.07, -53.47, 0.22384, 6, 7, 86.95, -79.68, 0.06213, 8, -8.87, -83, 0.45354, 10, -66.98, -103.84, 0, 11, -57.87, -67.75, 0.3728, 12, -66.82, -94.36, 0.01068, 13, -129.19, -31.08, 0.10086, 5, 7, 54.52, -74.05, 0.2793, 8, -35.32, -63.41, 0.48102, 11, -85.99, -50.63, 0.20548, 12, -99.1, -87.9, 0.00112, 13, -153.31, -8.68, 0.03307, 5, 3, 222.3, -57.77, 0.00205, 7, 22.08, -68.42, 0.64429, 8, -61.77, -43.82, 0.28016, 11, -114.11, -33.52, 0.06771, 13, -177.43, 13.72, 0.00579, 6, 3, 190.29, -65.43, 0.03588, 4, 33.69, -106.65, 0.02581, 7, -10.35, -62.79, 0.84316, 8, -88.22, -24.23, 0.08468, 11, -142.23, -16.4, 0.01038, 13, -201.55, 36.12, 9e-05, 5, 3, 158.27, -73.1, 0.1685, 4, 3.43, -93.68, 0.12218, 7, -42.78, -57.16, 0.69827, 8, -114.67, -4.63, 0.01095, 11, -170.34, 0.71, 9e-05, 4, 3, 126.26, -80.76, 0.44115, 4, -26.82, -80.71, 0.15103, 7, -75.21, -51.53, 0.4078, 8, -141.12, 14.96, 2e-05, 3, 3, 94.25, -88.42, 0.74729, 4, -57.08, -67.75, 0.06989, 7, -107.64, -45.89, 0.18281, 3, 3, 62.24, -96.09, 0.92643, 4, -87.33, -54.78, 0.01107, 7, -140.07, -40.26, 0.0625, 3, 3, 30.23, -103.75, 0.98724, 4, -117.59, -41.81, 4e-05, 7, -172.5, -34.63, 0.01272, 2, 3, -1.79, -111.42, 0.99965, 7, -204.94, -29, 0.00035, 1, 3, -10.91, -73.3, 1, 1, 3, -20.04, -35.17, 1, 1, 3, -29.17, 2.95, 1, 3, 3, -38.3, 41.07, 0.99482, 4, -86.08, 115.27, 0.00462, 5, -140, 137.49, 0.00056, 3, 3, -47.43, 79.19, 0.9871, 4, -70.63, 151.31, 0.01093, 5, -119.4, 170.84, 0.00197, 3, 3, -15.41, 86.86, 0.95021, 4, -40.38, 138.34, 0.03716, 5, -91.4, 153.54, 0.01263, 3, 3, 16.6, 94.52, 0.82869, 4, -10.12, 125.37, 0.12067, 5, -63.39, 136.24, 0.05063, 3, 3, 48.61, 102.19, 0.614, 4, 20.13, 112.41, 0.25245, 5, -35.39, 118.95, 0.13355, 3, 3, 80.62, 109.85, 0.35795, 4, 50.39, 99.44, 0.35595, 5, -7.38, 101.65, 0.2861, 4, 3, 112.63, 117.52, 0.1536, 4, 80.64, 86.47, 0.31718, 5, 20.63, 84.35, 0.51376, 6, -68.42, 59.85, 0.01545, 4, 3, 144.65, 125.18, 0.04349, 4, 110.9, 73.51, 0.14806, 5, 48.63, 67.05, 0.66217, 6, -35.62, 57.11, 0.14628, 4, 3, 176.66, 132.85, 0.00529, 4, 141.15, 60.54, 0.02623, 5, 76.64, 49.76, 0.41424, 6, -2.81, 54.38, 0.55424, 4, 4, 171.41, 47.57, 0.0002, 5, 104.64, 32.46, 0.06248, 6, 29.99, 51.65, 0.89754, 10, -8.77, 86.18, 0.03978, 4, 6, 62.79, 48.91, 0.71016, 8, 107.79, 74.49, 0, 10, 20.67, 71.46, 0.2828, 13, 4.18, 112.55, 0.00705, 4, 6, 95.6, 46.18, 0.3331, 8, 134.24, 54.9, 0, 10, 50.11, 56.74, 0.59475, 13, 28.3, 90.15, 0.07215, 5, 6, 128.4, 43.45, 0.09183, 8, 160.69, 35.31, 0, 10, 79.56, 42.02, 0.63953, 12, 36.17, 84.93, 0.00015, 13, 52.42, 67.76, 0.2685, 4, 6, 161.2, 40.71, 0.02074, 8, 187.14, 15.71, 0, 10, 109, 27.3, 0.55519, 13, 76.54, 45.36, 0.42407, 5, 6, 157.95, 1.65, 0.00333, 8, 163.81, -15.78, 0, 10, 91.47, -7.76, 0.32902, 12, 60.76, 40.03, 0.03269, 13, 49.87, 16.63, 0.63495, 4, 8, 140.48, -47.28, 0.0001, 10, 73.94, -42.82, 5e-05, 12, 53.07, 1.59, 1e-05, 13, 23.19, -12.09, 0.99984, 5, 8, 117.14, -78.78, 0.02861, 10, 56.41, -77.88, 0, 11, 67.24, -52.13, 0.1253, 12, 45.38, -36.85, 0.02567, 13, -3.48, -40.82, 0.82041, 5, 8, 93.81, -110.28, 0.09605, 10, 38.87, -112.95, 0, 11, 46.86, -85.61, 0.32758, 12, 37.7, -75.29, 0.04595, 13, -30.15, -69.54, 0.53042, 5, 8, 67.36, -90.69, 0.12807, 10, 9.43, -98.22, 0, 11, 18.74, -68.49, 0.39473, 12, 5.42, -68.83, 0.0689, 13, -54.27, -47.15, 0.4083, 6, 7, 126.08, -46.69, 0.00068, 8, 40.91, -71.1, 0.24163, 10, -20.01, -83.5, 0, 11, -9.37, -51.38, 0.50537, 12, -26.86, -62.37, 0.05131, 13, -78.39, -24.75, 0.20101, 6, 7, 93.65, -41.06, 0.02633, 8, 14.46, -51.51, 0.47816, 10, -49.45, -68.78, 0, 11, -37.49, -34.27, 0.41011, 12, -59.14, -55.92, 0.01168, 13, -102.52, -2.35, 0.07372, 5, 7, 61.22, -35.42, 0.22922, 8, -11.99, -31.91, 0.58442, 11, -65.61, -17.15, 0.16583, 12, -91.41, -49.46, 0.00049, 13, -126.64, 20.05, 0.02005, 5, 3, 213.17, -19.64, 0.00019, 7, 28.79, -29.79, 0.75194, 8, -38.44, -12.32, 0.21023, 11, -93.73, -0.04, 0.03525, 13, -150.76, 42.45, 0.0024, 5, 3, 181.16, -27.31, 0.01606, 4, 49.13, -70.62, 0.02292, 7, -3.64, -24.16, 0.92195, 8, -64.89, 7.27, 0.03589, 11, -121.84, 17.08, 0.00317, 4, 3, 149.15, -34.97, 0.11228, 4, 18.88, -57.65, 0.24285, 7, -36.07, -18.53, 0.64243, 8, -91.34, 26.87, 0.00244, 3, 3, 117.13, -42.64, 0.42553, 4, -11.38, -44.68, 0.26709, 7, -68.51, -12.9, 0.30738, 3, 3, 85.12, -50.3, 0.8146, 4, -41.64, -31.72, 0.06642, 7, -100.94, -7.27, 0.11898, 3, 3, 53.11, -57.97, 0.95869, 4, -71.89, -18.75, 0.00388, 7, -133.37, -1.64, 0.03743, 2, 3, 21.1, -65.63, 0.9932, 7, -165.8, 3.99, 0.0068, 5, 8, 90.69, -59.19, 0.03275, 10, 26.96, -63.16, 0, 11, 39.12, -35.01, 0.21995, 12, 13.11, -30.39, 0.27521, 13, -27.6, -18.42, 0.47209, 5, 8, 64.24, -39.6, 0.04883, 10, -2.48, -48.44, 0, 11, 11.01, -17.9, 0.75379, 12, -19.17, -23.94, 0.11102, 13, -51.72, 3.98, 0.08636, 5, 8, 37.79, -20.01, 0.31259, 9, 1.27, -20.57, 0.15186, 11, -17.11, -0.78, 0.52481, 12, -51.45, -17.48, 0.00145, 13, -75.84, 26.38, 0.00928, 3, 8, 11.34, -0.41, 0.99897, 11, -45.23, 16.33, 0.00099, 13, -99.96, 48.77, 5e-05, 4, 4, 94.83, -47.55, 0.00112, 5, 14.84, -50.3, 0.07214, 6, -12.58, -62.81, 0.00421, 7, 35.49, 8.83, 0.92253, 3, 4, 64.57, -34.59, 0.19818, 5, -13.17, -33, 0.06624, 7, 3.06, 14.46, 0.73558, 3, 3, 140.02, 3.15, 0.0017, 4, 34.32, -21.62, 0.72918, 7, -29.37, 20.09, 0.26911, 3, 3, 108.01, -4.51, 0.17721, 4, 4.06, -8.65, 0.78594, 7, -61.8, 25.72, 0.03685, 2, 3, 75.99, -12.18, 0.98978, 7, -94.23, 31.35, 0.01022, 2, 3, 43.98, -19.84, 0.99645, 7, -126.66, 36.98, 0.00355, 2, 3, 11.97, -27.51, 0.99974, 7, -159.09, 42.61, 0.00026, 2, 10, 44.49, -28.1, 0.09123, 12, 20.79, 8.05, 0.90877, 4, 9, 52.38, -17.77, 0.02729, 10, 15.05, -13.38, 0.51236, 11, 31.39, 15.59, 0.40132, 12, -11.48, 14.5, 0.05903, 3, 6, 56.28, -29.22, 0.09078, 7, 107.06, 36.19, 0.00022, 9, 29.87, 6.24, 0.909, 5, 5, 63.44, -34.24, 0.07509, 6, 23.48, -26.48, 0.4269, 7, 74.63, 41.82, 0.0717, 8, 34.68, 31.09, 0.13597, 9, 7.35, 30.26, 0.29034, 5, 5, 35.44, -16.95, 0.70351, 6, -9.32, -23.75, 0.08712, 7, 42.2, 47.45, 0.16755, 8, 8.23, 50.68, 0.0263, 9, -15.16, 54.27, 0.01552, 2, 3, 162.9, 48.94, 0.00013, 5, 7.43, 0.35, 0.99987, 3, 3, 130.89, 41.27, 0.03148, 4, 49.76, 14.41, 0.90853, 5, -20.57, 17.65, 0.05999, 3, 3, 98.88, 33.61, 0.31945, 4, 19.5, 27.38, 0.64217, 5, -48.58, 34.95, 0.03838, 3, 3, 66.87, 25.94, 0.83757, 4, -10.75, 40.35, 0.14327, 5, -76.58, 52.24, 0.01916, 3, 3, 34.85, 18.28, 0.97346, 4, -41.01, 53.31, 0.021, 5, -104.59, 69.54, 0.00554, 3, 3, 2.84, 10.61, 0.99753, 4, -71.26, 66.28, 0.00189, 5, -132.59, 86.84, 0.00058, 5, 6, 125.14, 4.38, 0.03233, 8, 137.36, 3.81, 0, 10, 62.03, 6.96, 0.59967, 12, 28.48, 46.49, 0.09335, 13, 25.75, 39.03, 0.27465, 5, 6, 92.34, 7.12, 0.20182, 8, 110.91, 23.4, 0, 10, 32.58, 21.68, 0.76954, 12, -3.8, 52.94, 0.00019, 13, 1.63, 61.43, 0.02844, 5, 6, 59.54, 9.85, 0.76585, 8, 84.46, 42.99, 0, 9, 58.46, 33.06, 0.00181, 10, 3.14, 36.4, 0.23167, 13, -22.49, 83.83, 0.00067, 3, 5, 84.04, -0.89, 0.00333, 6, 26.73, 12.58, 0.99569, 10, -26.3, 51.12, 0.00098, 4, 3, 185.79, 94.72, 0.00178, 4, 125.71, 24.51, 0.00945, 5, 56.04, 16.4, 0.57019, 6, -6.07, 15.32, 0.41858, 4, 3, 153.77, 87.06, 0.02669, 4, 95.46, 37.48, 0.1474, 5, 28.03, 33.7, 0.78677, 6, -38.87, 18.05, 0.03914, 4, 3, 121.76, 79.39, 0.12401, 4, 65.2, 50.44, 0.43365, 5, 0.03, 51, 0.44048, 6, -71.68, 20.78, 0.00186, 3, 3, 89.75, 71.73, 0.3557, 4, 34.95, 63.41, 0.43814, 5, -27.98, 68.3, 0.20615, 3, 3, 57.74, 64.07, 0.65977, 4, 4.69, 76.38, 0.24826, 5, -55.98, 85.59, 0.09196, 3, 3, 25.73, 56.4, 0.87196, 4, -25.57, 89.34, 0.09437, 5, -83.99, 102.89, 0.03367, 3, 3, -6.29, 48.74, 0.9692, 4, -55.82, 102.31, 0.02333, 5, -112, 120.19, 0.00747], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 0], "width": 395, "height": 196}}, "di": {"di": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.66, -21.08, -588.25, -9.22, -587.42, 44.77, 183.49, 32.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 771, "height": 54}}, "zhong": {"zhong": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [496.4, 133.85, 511.72, -632, -450.09, -651.23, -465.41, 114.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 766, "height": 962}}, "guangdian2": {"guangdian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [109.86, 121.19, 117.1, -421.76, -26.89, -423.68, -34.12, 119.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 543, "height": 144}}, "guangdian": {"guangdian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [109.86, 121.19, 117.1, -421.76, -26.89, -423.68, -34.12, 119.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 543, "height": 144}}, "bone30": {"bone30": {"type": "clipping", "end": "bone30", "vertexCount": 4, "vertices": [683.06, -1024.84, -351.52, -1014.91, -358.92, -239.61, 663.1, -259.8], "color": "ce3a3aff"}}, "shang": {"shang": {"x": -231.87, "y": -7.95, "rotation": 2.68, "width": 764, "height": 106}}, "guang": {"guang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-636.2, 266.67, -660.72, 1345.39, 770.91, 1379.98, 795.43, 301.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1079, "height": 1432}}, "liusu2": {"liusu": {"type": "mesh", "uvs": [0.13043, 0.33333, 0.13043, 0.66667, 0.13043, 1, 0.08696, 1, 0.04348, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.04348, 0, 0.08696, 0, 0.13043, 0, 0.08696, 0.66667, 0.04348, 0.66667, 0.08696, 0.33333, 0.04348, 0.33333], "triangles": [12, 13, 15, 6, 15, 13, 12, 14, 0, 12, 0, 1, 4, 5, 6, 4, 13, 12, 4, 6, 13, 2, 3, 12, 4, 12, 3, 2, 12, 1, 7, 8, 9, 15, 9, 10, 7, 9, 15, 14, 10, 11, 15, 10, 14, 14, 11, 0, 6, 7, 15, 12, 15, 14], "vertices": [3, 23, 38.08, 65.38, 0.51474, 24, -12.02, 70.13, 0.38383, 22, -49.42, -268.28, 0.10142, 3, 23, 81.08, 65.38, 0.18186, 24, 29.79, 60.09, 0.71902, 22, -6.42, -267.71, 0.09912, 3, 23, 124.08, 65.38, 0.0665, 24, 71.6, 50.05, 0.83494, 22, 36.58, -267.13, 0.09856, 3, 23, 124.08, 33.38, 0.0095, 24, 64.13, 18.94, 0.9627, 22, 37, -299.13, 0.02781, 2, 24, 56.66, -12.18, 0.99913, 22, 37.43, -331.13, 0.00087, 1, 24, 49.2, -43.29, 1, 2, 23, 81.08, -30.62, 0.05777, 24, 7.38, -33.26, 0.94223, 3, 23, 38.08, -30.62, 0.91006, 24, -34.43, -23.22, 0.08994, 22, -48.14, -364.27, 0, 3, 23, -4.92, -30.62, 0.99998, 24, -76.24, -13.19, 0, 22, -91.13, -364.84, 2e-05, 3, 23, -4.92, 1.38, 0.99954, 24, -68.77, 17.93, 0.00029, 22, -91.56, -332.85, 0.00017, 3, 23, -4.92, 33.38, 0.89327, 24, -61.3, 49.04, 0.07522, 22, -91.99, -300.85, 0.03151, 3, 23, -4.92, 65.38, 0.68195, 24, -53.84, 80.16, 0.21533, 22, -92.41, -268.85, 0.10272, 3, 23, 81.08, 33.38, 0.0915, 24, 22.32, 28.97, 0.88136, 22, -5.99, -299.7, 0.02714, 1, 24, 14.85, -2.14, 1, 3, 23, 38.08, 33.38, 0.69452, 24, -19.49, 39.01, 0.27506, 22, -48.99, -300.28, 0.03042, 3, 23, 38.08, 1.38, 0.99825, 24, -26.96, 7.89, 0.00156, 22, -48.56, -332.27, 0.00019], "hull": 12, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0, 0, 2, 2, 4], "width": 736, "height": 129}}, "liusu": {"liusu": {"type": "mesh", "uvs": [0.13043, 0.33333, 0.13043, 0.66667, 0.13043, 1, 0.08696, 1, 0.04348, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.04348, 0, 0.08696, 0, 0.13043, 0, 0.08696, 0.66667, 0.04348, 0.66667, 0.08696, 0.33333, 0.04348, 0.33333], "triangles": [12, 13, 15, 6, 15, 13, 12, 14, 0, 12, 0, 1, 4, 5, 6, 4, 13, 12, 4, 6, 13, 2, 3, 12, 4, 12, 3, 2, 12, 1, 7, 8, 9, 15, 9, 10, 7, 9, 15, 14, 10, 11, 15, 10, 14, 14, 11, 0, 6, 7, 15, 12, 15, 14], "vertices": [3, 18, 48.7, 65.24, 0.51474, 19, -1.73, 67.51, 0.38383, 17, -38.79, -268.28, 0.10142, 3, 18, 91.7, 65.24, 0.18186, 19, 40.09, 57.47, 0.71902, 17, 4.2, -267.71, 0.09912, 3, 18, 134.7, 65.24, 0.0665, 19, 81.9, 47.44, 0.83494, 17, 47.2, -267.13, 0.09856, 3, 18, 134.7, 33.24, 0.0095, 19, 74.43, 16.32, 0.9627, 17, 47.63, -299.13, 0.02781, 2, 19, 66.96, -14.79, 0.99913, 17, 48.05, -331.13, 0.00087, 1, 19, 59.49, -45.91, 1, 2, 18, 91.7, -30.76, 0.05777, 19, 17.68, -35.88, 0.94223, 3, 18, 48.7, -30.76, 0.91006, 19, -24.13, -25.84, 0.08994, 17, -37.51, -364.27, 0, 3, 18, 5.7, -30.76, 0.99998, 19, -65.94, -15.81, 0, 17, -80.51, -364.84, 2e-05, 3, 18, 5.7, 1.24, 0.99954, 19, -58.48, 15.31, 0.00029, 17, -80.94, -332.85, 0.00017, 3, 18, 5.7, 33.24, 0.89327, 19, -51.01, 46.43, 0.07522, 17, -81.36, -300.85, 0.03151, 3, 18, 5.7, 65.24, 0.68195, 19, -43.54, 77.54, 0.21533, 17, -81.79, -268.85, 0.10272, 3, 18, 91.7, 33.24, 0.0915, 19, 32.62, 26.36, 0.88136, 17, 4.63, -299.7, 0.02714, 1, 19, 25.15, -4.76, 1, 3, 18, 48.7, 33.24, 0.69452, 19, -9.2, 36.39, 0.27506, 17, -38.37, -300.28, 0.03042, 3, 18, 48.7, 1.24, 0.99825, 19, -16.66, 5.28, 0.00156, 17, -37.94, -332.27, 0.00019], "hull": 12, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0, 0, 2, 2, 4], "width": 736, "height": 129}}}}], "events": {"close": {}, "jinengchuxian": {}, "pinzhichuxian": {}, "zichuxian": {}}, "animations": {"animation": {"slots": {"honghuo": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff4f", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffffff9d", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff"}]}, "zhong": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffffff"}]}, "liusu2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "guang": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 0.7333, "color": "ffffff4f", "curve": 0.264, "c2": 0.09, "c3": 0.694, "c4": 0.76}, {"time": 1.5, "color": "ffffff50", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.6667, "color": "ffffff4f"}]}, "guangdian2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}]}, "guangdian": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0667, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00"}]}, "liusu": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}}, "bones": {"bone2": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.11}]}, "bone3": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": -4.55, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 2.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.0667, "angle": -4.55, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 2.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.6667, "angle": -4.55}]}, "bone4": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": -1.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": -1.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 2.34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -1.88}]}, "bone5": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": 0.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.0667, "angle": 0.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.3, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 2.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "angle": 0.79}]}, "bone24": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": -4.55, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 2.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.0667, "angle": -4.55, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 2.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.6667, "angle": -4.55}]}, "bone6": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": -4.55, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 2.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.0667, "angle": -4.55, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 2.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.6667, "angle": -4.55}]}, "bone7": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": -1.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": -1.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 2.34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -1.88}]}, "bone8": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": 0.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.0667, "angle": 0.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.3, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 2.34, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "angle": 0.79}]}, "bone9": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": -1.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": -1.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 2.34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -1.88}]}, "bone10": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": -4.55, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 2.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.0667, "angle": -4.55, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.1333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 2.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.6667, "angle": -4.55}]}, "bone11": {"rotate": [{"time": 0.1}, {"time": 0.5, "angle": 1.26, "curve": 0.306, "c2": 0.24, "c3": 0.653, "c4": 0.62}, {"time": 0.5667, "angle": -1.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.0667, "angle": 1.26, "curve": 0.306, "c2": 0.24, "c3": 0.653, "c4": 0.62}, {"time": 1.1667, "angle": -1.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 2.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 1.26}]}, "bone12": {"translate": [{"x": 471.63, "curve": "stepped"}, {"time": 0.1, "x": 471.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.95}]}, "bone16": {"translate": [{"x": -484.39, "curve": "stepped"}, {"time": 0.1, "x": -484.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 29.97, "y": 10.51, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6, "x": -16.91, "y": 10.51, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.7, "x": 29.97, "y": 10.51, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.8333, "x": 22.56, "y": 10.51, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.9333, "x": 29.97, "y": 10.51}]}, "bone21": {"translate": [{"x": -31.19, "curve": "stepped"}, {"time": 0.1, "x": -31.19}, {"time": 0.5, "x": -99.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -35.01}, {"time": 0.6333, "x": -22.88}, {"time": 0.7, "x": -2.29}]}, "bone17": {"rotate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.81}, {"time": 1, "angle": 3.48}, {"time": 1.0667}, {"time": 1.3, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.48}], "translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 8.52, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone18": {"rotate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.81}, {"time": 1, "angle": 3.48}, {"time": 1.0667}, {"time": 1.3, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.48}]}, "bone23": {"translate": [{"time": 0.1}, {"time": 0.5, "x": 52.4, "y": -11.45}, {"time": 1, "x": -28.77, "y": 10.56}, {"time": 1.0667, "x": 52.4, "y": -11.45}, {"time": 1.6, "x": -28.77, "y": 10.56}, {"time": 1.6667, "x": 52.4, "y": -11.45}]}, "bone25": {"translate": [{"time": 0.1}, {"time": 0.5, "x": -28.77, "y": 10.56}, {"time": 0.6, "x": 52.4, "y": -11.45}, {"time": 1.0667, "x": -23.11, "y": 9.03}, {"time": 1.1, "x": -28.77, "y": 10.56}, {"time": 1.2, "x": 52.4, "y": -11.45}, {"time": 1.6667, "x": -23.11, "y": 9.03}]}, "bone26": {"translate": [{"x": -31.19, "curve": "stepped"}, {"time": 0.1, "x": -31.19}, {"time": 0.5, "x": -99.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -35.01}, {"time": 0.6333, "x": -22.88}, {"time": 0.7, "x": -2.29}]}, "bone20": {"rotate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.81}, {"time": 1, "angle": 3.48}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.48}]}, "bone19": {"rotate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 7.63, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.81}, {"time": 1, "angle": 3.48}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.48}], "translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 8.52, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone27": {"scale": [{"time": 0.7}, {"time": 0.8333, "x": 0.99}, {"time": 0.9333}]}, "guadian_zi": {"scale": [{"x": 1.69, "y": 1.69, "curve": "stepped"}, {"time": 0.3333, "x": 1.69, "y": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone29": {"translate": [{"x": 487.39, "curve": "stepped"}, {"time": 0.0667, "x": 487.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone30": {"rotate": [{}, {"time": 0.6333, "angle": 56.85, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bone33": {"rotate": [{}, {"time": 0.6333, "angle": 56.85, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bone35": {"rotate": [{}, {"time": 0.6333, "angle": 56.85, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "guadian2": {"translate": [{"y": 330.73, "curve": "stepped"}, {"time": 0.1, "y": 330.73, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "guadian": {"translate": [{"y": -367.85, "curve": "stepped"}, {"time": 0.1, "y": -367.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": -10.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}}, "deform": {"default": {"bone30": {"bone30": [{"vertices": [-511.63098, -8.64197, 335.13135, -2.81488, 335.5926, -37.41217, -493.42273, -8.39911]}, {"time": 0.1, "vertices": [-569.99475, -3.47028, 335.13135, -2.81488, 335.5926, -37.41217, -566.426, 2.04102], "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5, "vertices": [-192.12512, 12.25926, 26.2319, -27.10024, 34.42224, -11.17719, -185.85187, 89.44391], "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.5333, "vertices": [-74.84833, 13.57233, 0.38857, -29.13202, 9.22554, -8.9823, -68.3488, 96.75336]}]}, "zhong": {"zhong": [{"time": 0.1, "vertices": [-475.16812, -12.93681, -475.16812, -12.93681, 408.3949, 4.73465, 408.3949, 4.73465], "curve": 0.25, "c3": 0.75}, {"time": 0.5, "vertices": [0.04147, -2.07364], "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6, "vertices": [-40.41156, -3.92117, -40.45303, -1.84753], "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.7, "vertices": [0.04147, -2.07364], "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.8333, "vertices": [-6.206, -1.16058, -6.24747, 0.91306], "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.9333, "vertices": [0.04147, -2.07364]}]}, "di": {"di": [{"vertices": [-9.43358, 28.30158, -9.43358, 28.30158, -9.43358, 28.30158, -9.43358, 28.30158]}]}, "guang": {"guang": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "vertices": [44.3727, 0.00391, 44.3727], "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}}}, "events": [{"time": 0.2667, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.3333, "name": "pinzhichuxian"}, {"time": 0.7, "name": "jineng<PERSON><PERSON>n"}, {"time": 0.7667, "name": "jineng<PERSON><PERSON>n"}, {"time": 0.8, "name": "jineng<PERSON><PERSON>n"}, {"time": 1, "name": "close"}]}, "animation2": {"slots": {"honghuo": {"color": [{"color": "ffffff9d", "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "color": "ffffffff"}]}, "guang": {"color": [{"color": "ffffff50"}]}, "guangdian2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "ffffff00"}]}, "guangdian": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{"angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.11}]}, "bone3": {"rotate": [{"angle": -4.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.55}]}, "bone4": {"rotate": [{"angle": -1.88, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.88}]}, "bone5": {"rotate": [{"angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.79}]}, "bone24": {"rotate": [{"angle": -4.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.55}]}, "bone6": {"rotate": [{"angle": -4.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.55}]}, "bone7": {"rotate": [{"angle": -1.88, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.88}]}, "bone8": {"rotate": [{"angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.79}]}, "bone9": {"rotate": [{"angle": -1.88, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.88}]}, "bone10": {"rotate": [{"angle": -4.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.55}]}, "bone11": {"rotate": [{"angle": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -1.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 1.26}]}, "bone12": {"translate": [{"time": 0.6, "x": -2.95}]}, "bone21": {"translate": [{"x": -2.29}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone23": {"translate": [{"x": 52.4, "y": -11.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -28.77, "y": 10.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 52.4, "y": -11.45}]}, "bone25": {"translate": [{"x": -23.11, "y": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "x": -28.77, "y": 10.56, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 52.4, "y": -11.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -23.11, "y": 9.03}]}, "bone26": {"translate": [{"x": -2.29}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}}, "deform": {"default": {"zhong": {"zhong": [{"vertices": [0.04147, -2.07364]}]}}}}}}