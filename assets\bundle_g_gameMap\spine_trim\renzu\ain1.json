{"skeleton": {"hash": "lcX+ry1XZlMbQS2VgNzrV+HXPvc", "spine": "3.8.75", "x": -56.46, "y": -85.98, "width": 157.52, "height": 415.55, "images": "", "audio": ""}, "bones": [{"name": "jtroot", "scaleX": 1.22, "scaleY": 1.22}, {"name": "jtbe", "parent": "jtroot", "length": 4.39, "rotation": 178.43, "x": -26.04, "y": 8.83}, {"name": "jtbe2", "parent": "jtbe", "x": -0.35, "y": -3.87}, {"name": "jtbe5", "parent": "jtroot", "length": 4.44, "rotation": -2.06, "x": 3.37, "y": -23.4, "scaleX": 3.236, "scaleY": 3.236}, {"name": "jtbe6", "parent": "jtbe5", "x": -0.66, "y": 7.24}, {"name": "jtbe11", "parent": "jtroot", "x": 1.03, "y": -72.11}, {"name": "jtbe9", "parent": "jtbe11", "length": 4.65, "rotation": -0.69, "x": 0.6, "y": 3.11}, {"name": "jtbe10", "parent": "jtbe9", "x": -2.28, "y": 3.22}, {"name": "jtbe12", "parent": "jtroot", "x": 1.03, "y": -72.11}, {"name": "jtbe13", "parent": "jtbe12", "length": 4.65, "rotation": -0.69, "x": 0.6, "y": 3.11}, {"name": "jtbe14", "parent": "jtbe13", "x": -2.28, "y": 3.22}, {"name": "jtbe16", "parent": "jtroot", "length": 8.76, "rotation": -0.9, "x": 20.49, "y": -1.14, "scaleX": 1.5, "scaleY": 1.5}, {"name": "jtbe17", "parent": "jtbe16", "length": 8.19, "rotation": 96.37, "x": -0.57, "y": 2.95}, {"name": "jtbe18", "parent": "jtbe17", "length": 10.19, "rotation": -2.4, "x": 8.23}, {"name": "jtbe19", "parent": "jtbe18", "length": 5.64, "rotation": 57.53, "x": -0.16, "y": 3.56}, {"name": "jtbe20", "parent": "jtbe19", "length": 5.88, "rotation": 3.01, "x": 5.64}, {"name": "jtbe21", "parent": "jtbe20", "length": 5.27, "rotation": 5.14, "x": 5.88}, {"name": "jtbe22", "parent": "jtbe21", "length": 4.36, "rotation": 4.6, "x": 5.27}, {"name": "jtbe23", "parent": "jtbe22", "rotation": 163.35, "x": 3.97, "y": -0.28, "transform": "noRotationOrReflection"}, {"name": "jtbe24", "parent": "jtbe23", "rotation": 163.35, "x": -5.13, "y": 17.03, "transform": "onlyTranslation"}, {"name": "jtbe35", "parent": "jtroot", "x": -2.98, "y": 150.34}, {"name": "jtbe15", "parent": "jtbe35", "length": 9.22, "rotation": 109.52, "x": -0.27, "y": 2.68}, {"name": "jtbe25", "parent": "jtbe15", "length": 12.57, "rotation": -4.96, "x": 10.64}, {"name": "jtbe26", "parent": "jtbe35", "length": 9.18, "rotation": -146.79, "x": -2.93, "y": 3.6}, {"name": "jtbe27", "parent": "jtbe35", "length": 10.66, "rotation": -80.19, "x": 0.04, "y": -2.46}, {"name": "jtbe34", "parent": "jtroot", "x": -1.6, "y": 240.5}, {"name": "jtbe28", "parent": "jtbe34", "length": 10.41, "rotation": 105.56, "x": 0.07, "y": 1.19}, {"name": "jtbe29", "parent": "jtbe28", "length": 10.15, "rotation": -0.58, "x": 11.66, "y": 0.24}, {"name": "jtbe31", "parent": "jtbe34", "length": 12.09, "rotation": -81.87, "x": -0.16, "y": -1.47}, {"name": "r8s3", "parent": "jtbe28", "length": 4.62, "rotation": 132.83, "x": 9.22, "y": 6.41}, {"name": "r8s4", "parent": "r8s3", "length": 4.15, "rotation": 26.04, "x": 5.24, "y": 0.15}, {"name": "jtbe32", "parent": "r8s4", "length": 15.45, "rotation": -115.73, "x": 4.36, "y": -0.68}, {"name": "jts1", "parent": "jtbe35", "length": 10.72, "rotation": -124.05, "x": -4.87, "y": 7.73}, {"name": "jtbe33", "parent": "jts1", "length": 13.44, "rotation": -112.7, "x": 10.94, "y": 0.45}, {"name": "jtbe7", "parent": "jtroot", "x": -8.78, "y": -32.8, "scaleX": 1.22, "scaleY": 1.22}, {"name": "jtr2", "parent": "jtbe7", "length": 8.58, "rotation": 90, "x": 22.52, "y": 14.6}, {"name": "jtr3", "parent": "jtr2", "length": 8.29, "rotation": 178.98, "x": -3.33, "y": 2}, {"name": "jtr4", "parent": "jtr2", "length": 6.58, "rotation": -160.95, "x": -3.85, "y": -2.44}, {"name": "jtr5", "parent": "jtr2", "length": 4.85, "rotation": 7.02, "x": 11.4, "y": 0.15}, {"name": "jtr2s1", "parent": "jtr2", "length": 7.85, "rotation": 171.87, "x": 6.84, "y": 5.5}, {"name": "jtr2s2", "parent": "jtr2", "length": 8.29, "rotation": 179.49, "x": 6.16, "y": -7.31}, {"name": "jtr6", "parent": "jtbe5", "length": 2.93, "rotation": 93.84, "x": -10.35, "y": 2.99, "scaleX": 1.14, "scaleY": 1.14}, {"name": "jtr7", "parent": "jtr6", "length": 2.11, "rotation": 6.88, "x": 3.4, "y": 0.03}, {"name": "jtr8", "parent": "jtr6", "length": 2.59, "rotation": 175.71, "x": -0.75, "y": 0.57}, {"name": "jtr9", "parent": "jtr6", "length": 2.98, "rotation": -173.46, "x": -0.64, "y": -0.91}, {"name": "jtr4s1", "parent": "jtr6", "length": 3.33, "rotation": 164.85, "x": 2.52, "y": 1.49}, {"name": "jtr4s2", "parent": "jtr6", "length": 3, "rotation": -168.68, "x": 2.48, "y": -1.87}, {"name": "jtr4t1", "parent": "jtr8", "length": 1.17, "rotation": -8.8, "x": 2.64, "y": -0.07}, {"name": "jtr4t2", "parent": "jtr9", "length": 1.43, "rotation": -40.93, "x": 3.06, "y": 0.13}, {"name": "jtbe3", "parent": "jtroot", "x": 66.27, "y": -47.32}, {"name": "jtr1", "parent": "jtbe3", "length": 9.92, "rotation": 96.48, "x": 3.46, "y": 20.46}, {"name": "jtr10", "parent": "jtr1", "length": 8.29, "rotation": 173.52, "x": -5.56, "y": 0.63}, {"name": "jtr11", "parent": "jtr1", "length": 10.91, "rotation": -11.78, "x": 12.54, "y": 0.27}, {"name": "jtbe36", "parent": "jtbe34", "length": 11.19, "rotation": -98.65, "x": -6.29, "y": -1.94}, {"name": "jtr7s2", "parent": "jtbe15", "length": 13.56, "rotation": 122.74, "x": 5.11, "y": -3.65}, {"name": "jtbe4", "parent": "jtroot", "x": -71.81, "y": 144.66}, {"name": "jtr7t2", "parent": "jtbe35", "length": 7.51, "rotation": -102.15, "x": -5.16, "y": -6.83}, {"name": "jtr7t1", "parent": "jtbe35", "length": 10.21, "rotation": -92.58, "x": -0.26, "y": -5.65}, {"name": "jtshit", "parent": "jtroot", "x": -38.58, "y": 1.66}, {"name": "jtshit2", "parent": "jtroot", "x": -58.43, "y": -13.24}, {"name": "jtxin", "parent": "jtroot", "x": -0.02, "y": -46.29}, {"name": "jtbone2", "parent": "jtxin", "x": -70.11, "y": 75.6}, {"name": "jtbone3", "parent": "jtxin", "x": -0.31, "y": 50.37, "scaleX": 0.82, "scaleY": 0.86}, {"name": "jtbone", "parent": "jtbone3", "x": -0.58, "y": -3.02}, {"name": "jtst", "parent": "jtbone", "length": 19.79, "rotation": -21.96, "x": -0.72, "y": 11.5}, {"name": "jtst2", "parent": "jtst", "length": 15.58, "rotation": 163.13, "x": -1.17, "y": 0.09}, {"name": "jtj2", "parent": "jtst", "length": 6.37, "rotation": -120.09, "x": 3.6, "y": -8.15}, {"name": "jtj3", "parent": "jtj2", "length": 4.29, "rotation": -62.05, "x": 3.53, "y": -0.23}, {"name": "jtj1", "parent": "jtst", "length": 6.61, "rotation": -78.82, "x": 9.71, "y": -5.14}, {"name": "jtj4", "parent": "jtj1", "length": 3.88, "rotation": -62.22, "x": 3.67, "y": 0.06}, {"name": "jtbone4", "parent": "jtxin", "x": -0.31, "y": 90.98, "scaleX": 0.88, "scaleY": 0.88}, {"name": "jtbone5", "parent": "jtbone4", "x": -0.58, "y": -3.02}, {"name": "jtst3", "parent": "jtbone5", "length": 19.79, "rotation": -21.96, "x": -0.72, "y": 11.5}, {"name": "jtst4", "parent": "jtst3", "length": 15.58, "rotation": 163.13, "x": -1.17, "y": 0.09}, {"name": "jtj5", "parent": "jtst3", "length": 6.37, "rotation": -120.09, "x": 3.6, "y": -8.15}, {"name": "jtj6", "parent": "jtj5", "length": 4.29, "rotation": -62.05, "x": 3.53, "y": -0.23}, {"name": "jtj7", "parent": "jtst3", "length": 6.61, "rotation": -78.82, "x": 9.71, "y": -5.14}, {"name": "jtj8", "parent": "jtj7", "length": 3.88, "rotation": -62.22, "x": 3.67, "y": 0.06}, {"name": "jtbone6", "parent": "jtxin", "rotation": -1.2, "x": -42.56, "y": 59.51, "scaleX": 0.8, "scaleY": 0.8}, {"name": "jtbone7", "parent": "jtbone6", "x": -0.58, "y": -3.02}, {"name": "jtst5", "parent": "jtbone7", "length": 19.79, "rotation": -21.96, "x": -0.72, "y": 11.5}, {"name": "jtst6", "parent": "jtst5", "length": 15.58, "rotation": 163.13, "x": -1.17, "y": 0.09}, {"name": "jtj9", "parent": "jtst5", "length": 6.37, "rotation": -120.09, "x": 3.6, "y": -8.15}, {"name": "jtj10", "parent": "jtj9", "length": 4.29, "rotation": -62.05, "x": 3.53, "y": -0.23}, {"name": "jtj11", "parent": "jtst5", "length": 6.61, "rotation": -78.82, "x": 9.71, "y": -5.14}, {"name": "jtj12", "parent": "jtj11", "length": 3.88, "rotation": -62.22, "x": 3.67, "y": 0.06}, {"name": "vv2", "parent": "jtroot", "x": -0.64, "y": -53.04}, {"name": "root", "parent": "vv2", "x": 0.93, "y": 43.29}, {"name": "nst", "parent": "root", "length": 27.64, "rotation": -169.17, "x": 11.74, "y": 24.59}, {"name": "nt", "parent": "nst", "length": 11.51, "rotation": 122.1, "x": 0.42, "y": -3.14}, {"name": "nj4", "parent": "nst", "length": 6.94, "rotation": 156.11, "x": 7.98, "y": 7.91}, {"name": "nj5", "parent": "nj4", "length": 3.82, "rotation": -28.82, "x": 6.54, "y": -0.46}, {"name": "nj1", "parent": "nst", "length": 6.58, "rotation": 123.57, "x": 10.81, "y": 4.91}, {"name": "nj2", "parent": "nj1", "length": 4.31, "rotation": -23.08, "x": 6.03, "y": -0.29}, {"name": "nj3", "parent": "nst", "length": 8.85, "rotation": 106.87, "x": 22.98, "y": -1.25}, {"name": "nj6", "parent": "nj3", "length": 4.37, "rotation": -18.66, "x": 8.37, "y": -0.05}, {"name": "nj7", "parent": "nst", "length": 8.79, "rotation": 56.89, "x": 19.43, "y": -1.17}, {"name": "nj8", "parent": "nj7", "length": 5.27, "rotation": -22.72, "x": 8.3, "y": 0.24}, {"name": "rst", "parent": "nst", "length": 11.67, "rotation": 57.99, "x": 9.57, "y": -12.64}, {"name": "rst2", "parent": "rst", "length": 5.13, "rotation": 14.59, "x": 10.69, "y": 0.38}, {"name": "rst3", "parent": "rst2", "length": 3.51, "rotation": -19.98, "x": 4.79, "y": 0.05}, {"name": "rt", "parent": "rst", "length": 8.34, "rotation": -145.93, "x": -3.48, "y": -1.62}, {"name": "jtroot2", "parent": "jtroot", "x": 2.69, "y": -2.68}, {"name": "r8s1", "parent": "jtbe28", "length": 6.49, "rotation": 131.49, "x": 4.86, "y": -1.37}, {"name": "r8s2", "parent": "r8s1", "length": 3.94, "rotation": -24.58, "x": 7.24, "y": -0.23}, {"name": "target2", "parent": "jtbe32", "rotation": -141.24, "x": 0.44, "y": -2.82, "color": "ff3f00ff"}, {"name": "jtbe30", "parent": "jtbe28", "length": 5.83, "rotation": 114.33, "x": 10.05, "y": 6.15}, {"name": "jtbe37", "parent": "jtbe30", "length": 5.66, "rotation": 60.85, "x": 6, "y": 0.15, "color": "abe323ff"}, {"name": "target1", "parent": "jtbe32", "rotation": -141.24, "x": -7.36, "y": -4.22, "color": "ff3f00ff"}, {"name": "jtbe38", "parent": "jtbe28", "length": 5.94, "rotation": 139.95, "x": 4.43, "y": -1.64}, {"name": "jtbe39", "parent": "jtbe38", "length": 5.63, "rotation": -39.96, "x": 5.94}], "slots": [{"name": "j<PERSON><PERSON><PERSON>", "bone": "jtbe7", "attachment": "j<PERSON><PERSON><PERSON>"}, {"name": "jtyingzi3", "bone": "jtroot2", "attachment": "j<PERSON><PERSON><PERSON>"}, {"name": "jtyingzi4", "bone": "jtbe3", "attachment": "j<PERSON><PERSON><PERSON>"}, {"name": "jtyingzi5", "bone": "jtbe5", "attachment": "j<PERSON><PERSON><PERSON>"}, {"name": "jtt2", "bone": "jtroot", "attachment": "jtt2"}, {"name": "jtt1", "bone": "jtroot", "attachment": "jtt1"}, {"name": "jtr1", "bone": "jtr1", "attachment": "jtr1"}, {"name": "jttuzi", "bone": "jtbe11", "dark": "5e5d47", "attachment": "jttuzi"}, {"name": "jttuzi2", "bone": "jtbe12", "dark": "5e5d47", "attachment": "jttuzi"}, {"name": "jtr2s1", "bone": "jtr2s1", "attachment": "jtr2s1"}, {"name": "jtr2", "bone": "jtr2", "attachment": "jtr2"}, {"name": "jtr3", "bone": "jtbe", "dark": "312b2b", "attachment": "jtr3"}, {"name": "jtr4", "bone": "jtr6", "dark": "362d2d", "attachment": "jtr4"}, {"name": "jtdyl", "bone": "jtbe16", "attachment": "jtdyl"}, {"name": "j<PERSON>an", "bone": "jtroot"}, {"name": "jttian2", "bone": "jtroot"}, {"name": "jtyingzi2", "bone": "jtbe34", "attachment": "j<PERSON><PERSON><PERSON>"}, {"name": "j<PERSON><PERSON><PERSON>", "bone": "jtbe23", "attachment": "j<PERSON><PERSON><PERSON>"}, {"name": "jtjian", "bone": "jtroot", "attachment": "jtjian"}, {"name": "jts1", "bone": "jts1", "attachment": "jts1"}, {"name": "jtgaozi", "bone": "jtroot", "attachment": "jtgaozi"}, {"name": "jtr9", "bone": "jtroot", "attachment": "jtr8"}, {"name": "r8s2", "bone": "r8s3"}, {"name": "j<PERSON><PERSON><PERSON>", "bone": "jtroot", "attachment": "j<PERSON><PERSON><PERSON>"}, {"name": "jtr8", "bone": "jtroot", "attachment": "jtr7"}, {"name": "jtr2s2", "bone": "jtr2s2", "attachment": "jtr2s2"}, {"name": "jtr4s1", "bone": "jtr4s1", "attachment": "jtr4s1"}, {"name": "jtr4s2", "bone": "jtr4s2", "attachment": "jtr4s2"}, {"name": "jtr4t1", "bone": "jtr4t1", "attachment": "jtr4t1"}, {"name": "jtr4t2", "bone": "jtr4t2", "attachment": "jtr4t2"}, {"name": "jtr7s2", "bone": "jtr7s2"}, {"name": "jtr7t2", "bone": "jtr7t2"}, {"name": "jtr7t1", "bone": "jtr7t1"}, {"name": "jtshit2", "bone": "jtroot"}, {"name": "jtshit", "bone": "jtshit"}, {"name": "jtyanwu0001", "bone": "jtbe4"}, {"name": "jtyanwu0002", "bone": "jtbe4"}, {"name": "jtyanwu0003", "bone": "jtbe4"}, {"name": "jtyanwu0004", "bone": "jtbe4"}, {"name": "jtfangz", "bone": "jtbone2"}, {"name": "jtyingzi6", "bone": "jtbone"}, {"name": "jtyingzi7", "bone": "jtbone5"}, {"name": "jtyingzi8", "bone": "jtbone7"}, {"name": "jtweil", "bone": "jtbone2"}, {"name": "jtst3", "bone": "jtst5"}, {"name": "jtst2", "bone": "jtst3"}, {"name": "jtj6", "bone": "jtj11"}, {"name": "jtst", "bone": "jtst"}, {"name": "jtj5", "bone": "jtj9"}, {"name": "jtj4", "bone": "jtj7"}, {"name": "jtj1", "bone": "jtj1"}, {"name": "jtj3", "bone": "jtj5"}, {"name": "jtj2", "bone": "jtj2"}, {"name": "jtqiao", "bone": "jtroot"}, {"name": "jtjin", "bone": "jtroot"}, {"name": "t5", "bone": "jtroot"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root"}, {"name": "nj4", "bone": "nj4"}, {"name": "nj3", "bone": "nj7"}, {"name": "nst", "bone": "nst"}, {"name": "nj2", "bone": "nj3"}, {"name": "nj1", "bone": "nj1"}, {"name": "nt", "bone": "nt"}, {"name": "rst", "bone": "rst"}, {"name": "rt", "bone": "rt"}, {"name": "r8s1", "bone": "r8s1"}], "ik": [{"name": "target1", "bones": ["r8s1", "r8s2"], "target": "target1", "bendPositive": false, "stretch": true}, {"name": "target2", "order": 2, "bones": ["jtbe30", "jtbe37"], "target": "target2", "stretch": true}, {"name": "target14", "order": 1, "bones": ["jtbe38", "jtbe39"], "target": "target1", "bendPositive": false, "stretch": true}], "transform": [{"name": "jtbe37", "order": 3, "bones": ["r8s4"], "target": "jtbe37", "rotation": -24.99, "x": 1.9, "y": 1.75, "rotateMix": 0.801, "translateMix": 0.801, "scaleMix": 0.801, "shearMix": 0.801}], "skins": [{"name": "default", "attachments": {"jtyingzi": {"jtyingzi": {"x": 22.97, "y": 0.07, "width": 24, "height": 11}}, "jtyanwu0001": {"jtyanwu0001": {"y": 1.6, "rotation": -57.82, "width": 32, "height": 21}}, "jtjin": {"jtjin": {"x": -2.68, "y": -19.36, "scaleX": 0.86, "scaleY": 0.86, "width": 51, "height": 54}}, "jttian2": {"jttian": {"type": "mesh", "uvs": [1, 0.05541, 1, 0.14724, 0.76693, 0.92267, 0.61151, 1, 0.58068, 1, 0, 0.93446, 0, 0.70722, 0.28614, 0, 0.39318, 0, 0.80873, 0.06413], "triangles": [9, 0, 1, 9, 4, 8, 2, 9, 1, 4, 5, 6, 2, 4, 9, 6, 7, 8, 8, 4, 6, 3, 4, 2], "vertices": [45.65, -61.45, 45.7, -70.24, 18.06, -144.65, -0.6, -152.16, -4.31, -152.18, -74.23, -146.3, -74.36, -124.54, -40.3, -56.63, -27.42, -56.55, 22.63, -62.41], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 118, "height": 84}}, "jtt1": {"jtt1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.5, -6.8, -31.9, -6.8, -31.9, 49.6, 54.5, 49.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 98}}, "jtt2": {"jtt2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [82.83, -52.35, -33.97, -52.35, -33.97, 10.05, 82.83, 10.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 107}}, "jtr4t1": {"jtr4t1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 47, 1.46, 1.46, 1, 1, 47, 1.95, -1.05, 1, 1, 47, 0.07, -1.42, 1, 1, 47, -0.43, 1.09, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 8, "height": 6}}, "jtr4t2": {"jtr4t2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 48, 0.69, 1.67, 1, 1, 48, 2.07, -0.49, 1, 1, 48, 0.46, -1.53, 1, 1, 48, -0.92, 0.63, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 8, "height": 6}}, "nj2": {"nj2": {"type": "mesh", "uvs": [0.55318, 0.05037, 0.7148, 0.09268, 0.88337, 0.13682, 0.88329, 0.27261, 0.8832, 0.43351, 0.88314, 0.53714, 0.88308, 0.63259, 0.883, 0.76895, 0.88294, 0.87098, 0.71393, 0.99142, 0.55152, 0.94486, 0.43559, 0.82975, 0.32533, 0.72025, 0.19282, 0.58867, 0.05717, 0.45397, 0.05616, 0.34367, 0.05553, 0.27443, 0.21086, 0.15401, 0.39722, 0.00953], "triangles": [6, 12, 5, 6, 11, 12, 7, 11, 6, 7, 10, 11, 8, 10, 7, 9, 10, 8, 3, 1, 2, 15, 16, 17, 3, 0, 1, 13, 14, 15, 0, 3, 4, 17, 13, 15, 17, 18, 0, 4, 17, 0, 17, 4, 5, 17, 5, 13, 12, 13, 5], "vertices": [1, 94, -1.14, 3.53, 1, 1, 94, 0.65, 5.24, 1, 1, 94, 2.52, 7.02, 1, 2, 94, 4.77, 5.83, 0.98758, 95, -5.29, 4.42, 0.01242, 2, 94, 7.44, 4.43, 0.78387, 95, -2.31, 3.95, 0.21613, 2, 94, 9.16, 3.52, 0.40545, 95, -0.39, 3.64, 0.59455, 2, 94, 10.75, 2.69, 0.08679, 95, 1.38, 3.36, 0.91321, 1, 95, 3.9, 2.96, 1, 1, 95, 5.79, 2.66, 1, 1, 95, 7.64, -0.12, 1, 2, 94, 13.7, -4.29, 0.00345, 95, 6.4, -2.3, 0.99655, 2, 94, 11.01, -4.77, 0.10796, 95, 4.01, -3.62, 0.89204, 2, 94, 8.45, -5.23, 0.45694, 95, 1.73, -4.88, 0.54306, 2, 94, 5.37, -5.78, 0.86811, 95, -1.01, -6.39, 0.13189, 2, 94, 2.22, -6.35, 0.99011, 95, -3.81, -7.93, 0.00989, 2, 94, 0.38, -5.4, 0.99983, 95, -5.86, -7.62, 0.00017, 1, 94, -0.77, -4.81, 1, 1, 94, -1.73, -1.76, 1, 1, 94, -2.87, 1.89, 1], "hull": 19, "edges": [16, 18, 18, 20, 4, 6, 6, 8, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 2, 2, 4, 12, 14, 14, 16, 8, 10, 10, 12], "width": 17, "height": 22}}, "jtqiao": {"jtqiao": {"x": 3.14, "y": 0.27, "width": 90, "height": 62}}, "jtr2s1": {"jtr2s1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 39, 10.45, 2.55, 1, 1, 39, 10.96, -1.01, 1, 1, 39, -2.11, -2.88, 1, 1, 39, -2.62, 0.68, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 6, "height": 22}}, "jtyanwu0003": {"jtyanwu0003": {"x": -1.6, "y": -8.8, "width": 32, "height": 21}}, "nj1": {"nj1": {"type": "mesh", "uvs": [0.54556, 0.1399, 0.71095, 0.25055, 0.88573, 0.36749, 0.91488, 0.5023, 0.95376, 0.68215, 0.98948, 0.84734, 0.91363, 0.91246, 0.81167, 1, 0.71795, 1, 0.62052, 0.94247, 0.46629, 0.8514, 0.33809, 0.77569, 0.24827, 0.72266, 0.19338, 0.62455, 0.1177, 0.48929, 0.06494, 0.395, 0.01468, 0.30517, 0.08202, 0.18532, 0.14403, 0.07498, 0.38134, 0.03002], "triangles": [11, 12, 13, 10, 11, 3, 4, 10, 3, 4, 9, 10, 5, 6, 4, 6, 9, 4, 8, 9, 6, 7, 8, 6, 15, 16, 17, 17, 0, 15, 14, 15, 0, 0, 17, 19, 19, 17, 18, 1, 14, 0, 1, 13, 14, 1, 11, 13, 3, 11, 1, 3, 1, 2], "vertices": [1, 92, 1.09, 3.69, 1, 2, 92, 3.88, 4.11, 0.98861, 93, -3.7, 3.21, 0.01139, 2, 92, 6.83, 4.56, 0.77368, 93, -1.16, 4.78, 0.22632, 2, 92, 8.59, 3.4, 0.45625, 93, 0.91, 4.4, 0.54375, 2, 92, 10.93, 1.84, 0.07275, 93, 3.67, 3.89, 0.92725, 2, 92, 13.08, 0.42, 0.00142, 93, 6.21, 3.42, 0.99858, 1, 93, 6.77, 2.09, 1, 1, 93, 7.51, 0.31, 1, 1, 93, 7.05, -0.88, 1, 1, 93, 5.74, -1.8, 1, 2, 92, 8.13, -4.72, 0.01699, 93, 3.67, -3.25, 0.98301, 2, 92, 6.08, -5.16, 0.17035, 93, 1.96, -4.46, 0.82965, 2, 92, 4.64, -5.47, 0.33788, 93, 0.75, -5.3, 0.66212, 2, 92, 3.04, -4.95, 0.56881, 93, -0.92, -5.45, 0.43119, 2, 92, 0.84, -4.23, 0.86492, 93, -3.23, -5.66, 0.13508, 2, 92, -0.7, -3.73, 0.96189, 93, -4.84, -5.81, 0.03811, 2, 92, -2.16, -3.26, 0.99212, 93, -6.37, -5.94, 0.00788, 2, 92, -2.83, -1.32, 0.99995, 93, -7.75, -4.42, 5e-05, 1, 92, -3.45, 0.47, 1, 1, 92, -1.68, 3.27, 1], "hull": 20, "edges": [14, 16, 36, 38, 0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 10, 12, 12, 14], "width": 16, "height": 18}}, "jtshit2": {"jtshit2": {"type": "mesh", "uvs": [0.58644, 0.27956, 1, 0.74413, 1, 0.82504, 0.81568, 0.92783, 0.04812, 1, 0, 1, 0, 0, 0.12869, 0.00525], "triangles": [0, 4, 5, 2, 3, 1, 0, 5, 6, 0, 6, 7, 0, 1, 4, 3, 4, 1], "vertices": [-92.99, 86.39, -55.06, 2.34, -54.86, -12.39, -71.02, -31.31, -139.15, -45.35, -143.43, -45.41, -145.85, 136.57, -134.39, 135.77], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 89, "height": 182}}, "r8s2": {"r8s2": {"type": "mesh", "uvs": [1, 0, 1, 0.1607, 0.902, 0.36378, 0.77404, 0.62895, 0.66814, 0.84841, 0.44605, 1, 0.22025, 1, 0.10908, 0.97692, 0.10915, 0.74959, 0.10923, 0.50658, 0.1093, 0.28426, 0.10936, 0.10422, 0.50877, 0], "triangles": [3, 9, 2, 8, 9, 3, 4, 8, 3, 6, 7, 8, 8, 5, 6, 4, 5, 8, 12, 0, 1, 2, 12, 1, 12, 10, 11, 12, 2, 10, 2, 9, 10], "vertices": [1, 29, -2.54, 0.76, 1, 1, 29, -0.76, 1.86, 1, 2, 29, 1.95, 2.49, 0.94302, 30, -1.92, 3.55, 0.05698, 2, 29, 5.49, 3.32, 0.19449, 30, 1.62, 2.74, 0.80551, 2, 29, 8.42, 4, 0.00024, 30, 4.55, 2.07, 0.99976, 1, 30, 6.71, 0.27, 1, 1, 30, 6.9, -1.75, 1, 1, 30, 6.7, -2.78, 1, 1, 30, 3.76, -3.07, 1, 2, 29, 7.27, -2.61, 0.20104, 30, 0.62, -3.37, 0.79896, 2, 29, 4.81, -4.13, 0.8163, 30, -2.26, -3.65, 0.1837, 2, 29, 2.82, -5.35, 0.9801, 30, -4.59, -3.88, 0.0199, 1, 29, -0.22, -3, 1], "hull": 13, "edges": [0, 24, 0, 2, 8, 10, 10, 12, 12, 14, 22, 24, 2, 4, 4, 6, 6, 8, 20, 22, 18, 20, 14, 16, 16, 18], "width": 9, "height": 13}}, "yingzi": {"nyingzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [19.17, 3.52, -20.01, 2.05, -20.46, 13.98, 18.73, 15.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 46, "height": 14}}, "jtyanwu0002": {"jtyanwu0002": {"x": 0.4, "y": -8.4, "width": 32, "height": 21}}, "jtst": {"jtst": {"type": "mesh", "uvs": [0.50109, 0.11675, 0.54714, 0.21552, 0.60191, 0.33302, 0.68041, 0.25009, 0.80289, 0.1207, 0.93898, 0.1207, 0.98127, 0.28895, 0.9812, 0.41826, 0.98109, 0.5937, 0.92214, 0.69916, 0.8408, 0.8447, 0.68837, 0.83691, 0.49096, 0.82684, 0.32677, 0.81845, 0.24229, 0.7858, 0.09279, 0.75294, 0.01892, 0.70002, 0.01883, 0.5521, 0.01873, 0.39445, 0.01867, 0.28635, 0.16505, 0.11438, 0.25009, 0.01448, 0.34223, 0.01455, 0.45349, 0.01463, 0.68196, 0.36143, 0.60052, 0.43477, 0.54468, 0.55027, 0.37714, 0.61077, 0.34456, 0.6621, 0.22123, 0.69877, 0.37481, 0.36693, 0.13979, 0.48793, 0.305, 0.1836], "triangles": [10, 11, 9, 9, 11, 26, 13, 28, 12, 11, 12, 26, 12, 27, 26, 9, 26, 8, 26, 25, 8, 8, 25, 7, 7, 25, 24, 26, 30, 25, 25, 2, 24, 7, 24, 6, 6, 3, 4, 4, 5, 6, 2, 3, 24, 6, 24, 3, 28, 27, 12, 13, 14, 28, 15, 29, 14, 14, 29, 28, 15, 16, 29, 16, 17, 29, 17, 31, 29, 28, 29, 27, 27, 29, 31, 31, 30, 27, 27, 30, 26, 17, 18, 31, 31, 18, 30, 25, 30, 2, 30, 19, 32, 32, 19, 20, 19, 30, 18, 1, 30, 32, 30, 1, 2, 1, 32, 0, 0, 22, 23, 22, 0, 32, 20, 21, 32, 32, 21, 22], "vertices": [2, 65, 8.17, -12.06, 0.97512, 64, -5.49, 14, 0.02488, 2, 65, 5.14, -10.24, 0.88603, 64, -3.12, 11.38, 0.11397, 2, 65, 1.54, -8.08, 0.45272, 64, -0.3, 8.27, 0.54728, 2, 65, 1.67, -11.54, 0.10882, 64, 0.58, 11.63, 0.89118, 2, 65, 1.87, -16.95, 0.00846, 64, 1.96, 16.86, 0.99154, 1, 64, 5.29, 18.21, 1, 2, 65, -5.34, -15.51, 0.0017, 64, 8.44, 13.39, 0.9983, 2, 65, -8.06, -12.13, 0.00074, 64, 10.06, 9.37, 0.99926, 1, 64, 12.26, 3.91, 1, 1, 64, 12.14, 0.05, 1, 1, 64, 11.97, -5.29, 1, 2, 65, -10.84, 3.66, 0.00509, 64, 8.13, -6.55, 0.99491, 2, 65, -6.56, 6.67, 0.18405, 64, 3.17, -8.19, 0.81595, 2, 65, -3, 9.17, 0.53075, 64, -0.96, -9.55, 0.46925, 2, 65, -0.58, 9.71, 0.72492, 64, -3.44, -9.37, 0.27508, 2, 65, 3.19, 11.33, 0.92811, 64, -7.52, -9.82, 0.07189, 2, 65, 5.82, 11.17, 0.96958, 64, -9.99, -8.91, 0.03042, 2, 65, 8.94, 7.31, 0.99955, 64, -11.85, -4.31, 0.00045, 1, 65, 12.25, 3.19, 1, 1, 65, 14.53, 0.37, 1, 1, 65, 15.13, -6.55, 1, 1, 65, 15.48, -10.57, 1, 2, 65, 13.58, -12.09, 0.99979, 64, -10.66, 15.61, 0.00021, 2, 65, 11.29, -13.94, 0.99743, 64, -7.94, 16.71, 0.00257, 2, 65, -0.7, -8.66, 0.20216, 64, 2.01, 8.18, 0.79784, 2, 65, -0.57, -5.39, 0.32112, 64, 0.94, 5.09, 0.67888, 2, 65, -1.85, -1.45, 0.0707, 64, 1.02, 0.95, 0.9293, 2, 65, 0.33, 2.91, 0.78788, 64, -2.33, -2.59, 0.21212, 2, 65, -0.08, 4.79, 0.70716, 64, -2.48, -4.51, 0.29284, 2, 65, 1.69, 7.79, 0.84938, 64, -5.05, -6.87, 0.15062, 2, 65, 5.5, -3.43, 0.94336, 64, -5.45, 4.97, 0.05664, 1, 65, 7.8, 3.63, 1, 2, 65, 10.8, -7.06, 0.992, 64, -9.46, 9.98, 0.008], "hull": 24, "edges": [8, 10, 10, 12, 26, 28, 28, 30, 30, 32, 4, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 30, 42, 44, 44, 46, 38, 40, 40, 42, 36, 38, 32, 34, 34, 36, 0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26], "width": 52, "height": 66}}, "jttian": {"jttian": {"type": "mesh", "uvs": [1, 0.05541, 1, 0.14724, 0.76693, 0.92267, 0.61151, 1, 0.58068, 1, 0, 0.93446, 0, 0.70722, 0.28614, 0, 0.39318, 0, 0.80873, 0.06413], "triangles": [9, 0, 1, 9, 4, 8, 2, 9, 1, 4, 5, 6, 2, 4, 9, 6, 7, 8, 8, 4, 6, 3, 4, 2], "vertices": [29.86, 9.94, 29.91, 1.15, 2.27, -73.26, -16.39, -80.77, -20.1, -80.79, -90.03, -74.91, -90.15, -53.15, -56.09, 14.76, -43.21, 14.84, 6.84, 8.98], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 118, "height": 84}}, "jtr7s2": {"jtr7s2": {"x": 6.04, "y": -0.58, "rotation": 127.74, "width": 12, "height": 16}}, "jtjian": {"jtjian": {"x": 2.39, "y": -9.99, "scaleX": 0.7, "scaleY": 0.8, "rotation": -75.87, "width": 10, "height": 50}}, "jtchutou": {"jtchutou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 31, -18.92, 9.32, 1, 1, 31, -18.46, -10.68, 1, 1, 31, 31.52, -9.55, 1, 1, 31, 31.07, 10.45, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 50}}, "jtshit": {"jtshit": {"type": "mesh", "uvs": [0.91728, 0.41029, 0.92986, 0.63167, 0.74144, 0.72893, 0.563, 0.82104, 0.32448, 0.94416, 0.16993, 0.89606, 0, 0.84317, 0, 0.6472, 0, 0.38631, 0, 0.05178, 0.29573, 0.0722, 0.58114, 0.09191, 0.90045, 0.11397, 0.08292, 0.40611, 0.35956, 0.40611, 0.64023, 0.40884, 0.16975, 0.67338, 0.60792, 0.65702, 0.38177, 0.69793], "triangles": [4, 18, 3, 18, 4, 16, 6, 16, 5, 4, 5, 16, 6, 7, 16, 3, 17, 2, 3, 18, 17, 17, 15, 2, 2, 0, 1, 2, 15, 0, 16, 14, 18, 18, 14, 17, 7, 13, 16, 16, 13, 14, 17, 14, 15, 7, 8, 13, 13, 8, 9, 13, 10, 14, 15, 12, 0, 14, 11, 15, 15, 11, 12, 14, 10, 11, 10, 13, 9], "vertices": [2, 58, 25.76, -9.05, 0.54589, 59, 45.61, 5.86, 0.45411, 2, 58, 26.81, -22.68, 0.168, 59, 46.66, -7.78, 0.832, 1, 59, 30.98, -13.77, 1, 1, 59, 16.14, -19.45, 1, 1, 59, -3.71, -27.03, 1, 2, 58, -36.42, -38.97, 0.00026, 59, -16.57, -24.07, 0.99974, 1, 59, -30.71, -20.81, 1, 1, 59, -30.71, -8.74, 1, 2, 58, -50.55, -7.57, 0.22593, 59, -30.71, 7.33, 0.77407, 2, 58, -50.55, 13.04, 0.616, 59, -30.71, 27.94, 0.384, 2, 58, -25.95, 11.78, 0.808, 59, -6.1, 26.68, 0.192, 2, 58, -2.2, 10.56, 0.99283, 59, 17.64, 25.47, 0.00717, 1, 58, 24.36, 9.21, 1, 2, 58, -43.65, -8.79, 0.18797, 59, -23.81, 6.11, 0.81203, 2, 58, -20.64, -8.79, 0.29774, 59, -0.79, 6.11, 0.70226, 2, 58, 2.71, -8.96, 0.3136, 59, 22.56, 5.95, 0.6864, 1, 59, -16.58, -10.35, 1, 2, 58, 0.03, -24.25, 0.19426, 59, 19.87, -9.34, 0.80574, 1, 59, 1.06, -11.86, 1], "hull": 13, "edges": [18, 20, 20, 22, 22, 24, 2, 4, 4, 6, 6, 8, 2, 0, 0, 24, 16, 18, 12, 14, 14, 16, 8, 10, 10, 12], "width": 129, "height": 77}}, "jttuzi2": {"jttuzi": {"type": "mesh", "uvs": [1, 0.52886, 1, 1, 0.5, 1, 0, 1, 0, 0.52886, 0, 0, 0.5, 0, 1, 0, 0.48247, 0.52186], "triangles": [8, 5, 6, 4, 5, 8, 6, 7, 0, 8, 6, 0, 3, 4, 8, 2, 8, 0, 3, 8, 2, 2, 0, 1], "vertices": [2, 9, 3.96, 2.34, 0.81577, 10, 6.24, -0.88, 0.18423, 1, 9, 4, -1.43, 1, 2, 9, -0.5, -1.48, 0.96977, 10, 1.78, -4.7, 0.03023, 2, 9, -5, -1.53, 0.48865, 10, -2.72, -4.75, 0.51135, 2, 9, -5.04, 2.24, 0.10735, 10, -2.76, -0.99, 0.89265, 1, 10, -2.81, 3.25, 1, 2, 9, -0.59, 6.52, 0.13413, 10, 1.69, 3.3, 0.86587, 2, 9, 3.91, 6.57, 0.47171, 10, 6.19, 3.35, 0.52829, 2, 9, -0.7, 2.34, 0.43115, 10, 1.58, -0.88, 0.56885], "hull": 8, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 6, 8, 8, 10, 2, 0, 0, 14], "width": 9, "height": 8}}, "jtgaozi": {"jtgaozi": {"type": "mesh", "uvs": [1, 0.26533, 0.55395, 1, 0.39706, 1, 0, 0.34225, 0, 0.12794, 1, 0.15327], "triangles": [4, 5, 0, 3, 4, 0, 1, 2, 3, 0, 1, 3], "vertices": [1, 33, 28.96, -9.99, 1, 1, 33, -7.83, -1.29, 1, 1, 33, -7.85, 1.85, 1, 1, 33, 24.99, 9.99, 1, 1, 33, 35.71, 10.06, 1, 1, 33, 34.56, -9.95, 1], "hull": 6, "edges": [0, 10, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10], "width": 20, "height": 50}}, "jtweil": {"jtweil": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [200.4, -95.36, -46.08, -95.36, -46.08, 63.45, 200.4, 63.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 125}}, "jtfangz": {"jtfangz": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [19.52, 4.52, -47.21, 4.52, -47.21, 74.55, 19.52, 74.55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 101, "height": 106}}, "r8s1": {"r8s1": {"type": "mesh", "uvs": [0.59978, 0.23604, 0.83496, 0.05986, 1, 0.06004, 1, 0.50179, 0.77779, 0.66833, 0.60346, 0.79899, 0.33526, 1, 0, 1, 0, 0.76412, 0.19952, 0.57723, 0.42004, 0.37068], "triangles": [8, 6, 7, 9, 6, 8, 9, 5, 6, 1, 2, 3, 0, 1, 3, 4, 0, 3, 10, 0, 4, 9, 10, 4, 5, 9, 4], "vertices": [2, 103, 0.15, -2.8, 0.99982, 104, -5.38, -5.29, 0.00018, 1, 103, -3.75, -1.97, 1, 1, 103, -4.83, -0.3, 1, 1, 103, 1.1, 3.54, 1, 2, 103, 4.79, 2.75, 0.99969, 104, -3.47, 1.7, 0.00031, 2, 103, 7.68, 2.13, 0.4376, 104, -0.58, 2.34, 0.5624, 1, 104, 3.86, 3.32, 1, 1, 104, 7.25, 1.16, 1, 1, 104, 5.23, -2.02, 1, 2, 103, 7.34, -3.86, 0.22129, 104, 1.6, -3.26, 0.77871, 2, 103, 3.13, -3.44, 0.9273, 104, -2.4, -4.63, 0.0727], "hull": 11, "edges": [2, 4, 4, 6, 12, 14, 14, 16, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 2, 0, 0, 20], "width": 12, "height": 16}}, "jtyanwu0004": {"jtyanwu0004": {"x": 5.6, "y": -8.4, "width": 32, "height": 21}}, "t5": {"t5": {"x": -10.38, "y": -5.52, "scaleX": 0.76, "scaleY": 0.76, "width": 250, "height": 200}}, "rt": {"rt": {"type": "mesh", "uvs": [1, 0.24049, 1, 0.30615, 0.88015, 0.67624, 0.61041, 0.94628, 0.10425, 0.94478, 0.01458, 0.74163, 0.05643, 0.48269, 0.30339, 0.05492, 0.86224, 0.05494], "triangles": [8, 0, 1, 2, 8, 1, 4, 6, 7, 5, 6, 4, 3, 7, 8, 3, 8, 2, 4, 7, 3], "vertices": [5.34, -16.09, 4.36, -15.86, -0.36, -11.01, -2.55, -2.02, 0.94, 13.12, 4.59, 15.1, 8.18, 12.97, 12.88, 4.11, 9.06, -12.6], "hull": 9, "edges": [0, 16, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 36, "height": 18}}, "jtyuxian": {"jtyuxian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 19, -0.15, 0.01, 1, 1, 19, 0.08, 0.08, 1, 1, 18, 0.11, -0.13, 1, 1, 18, -0.12, -0.2, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1, "height": 18}}, "nt": {"nt": {"type": "mesh", "uvs": [0.77788, 0.06696, 0.94607, 0.21213, 0.96503, 0.43272, 0.91701, 0.85269, 0.78758, 0.94074, 0.62418, 0.9399, 0.26111, 0.80765, 0.08032, 0.51117, 0.08263, 0.41893, 0.12813, 0.1614, 0.25639, 0.09631, 0.51133, 0.27216], "triangles": [11, 0, 1, 9, 10, 11, 11, 1, 2, 11, 8, 9, 6, 11, 2, 11, 7, 8, 6, 7, 11, 2, 5, 6, 3, 5, 2, 4, 5, 3], "vertices": [-4.2, 14.08, 1.13, 13.82, 5.94, 9.87, 13.92, 1.11, 13.93, -2.52, 11.64, -4.95, 3.85, -7.86, -4.77, -4.88, -6.64, -3.08, -11.31, 2.54, -10.86, 5.7, -3.69, 6.15], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 24, "height": 33}}, "jtr7t1": {"jtr7t1": {"type": "mesh", "uvs": [0.41515, 0, 0.87885, 0, 0.90879, 0.15851, 0.95534, 0.40505, 1, 0.64153, 1, 0.8417, 1, 0.98005, 0.67958, 1, 0.33195, 1, 0.27316, 0.84467, 0.18208, 0.60401, 0.09225, 0.24329, 0.15129, 0], "triangles": [0, 11, 12, 2, 10, 11, 4, 10, 3, 9, 10, 4, 0, 1, 2, 0, 2, 11, 3, 10, 2, 5, 9, 4, 9, 7, 8, 9, 5, 7, 6, 7, 5], "vertices": [-2.08, 0.15, -1.39, 4.26, 0.84, 4.16, 4.32, 4, 7.65, 3.84, 10.41, 3.38, 12.32, 3.05, 12.12, 0.16, 11.6, -2.92, 9.36, -3.08, 5.9, -3.33, 0.79, -3.29, -2.48, -2.2], "hull": 13, "edges": [12, 14, 14, 16, 20, 22, 22, 24, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 2, 0, 0, 24], "width": 9, "height": 14}}, "jtr1": {"jtr1": {"type": "mesh", "uvs": [0.49253, 0.0639, 0.6991, 0.13204, 0.76854, 0.18374, 0.76732, 0.34302, 0.7662, 0.48824, 0.87179, 0.57488, 0.94914, 0.63833, 0.98969, 0.70107, 0.92405, 0.84354, 0.86744, 0.96641, 0.83793, 1, 0.26245, 1, 0.19974, 0.96726, 0.12607, 0.8319, 0.04942, 0.69102, 0.17544, 0.54548, 0.26217, 0.44531, 0.16627, 0.2992, 0.16632, 0.11671, 0.29986, 0.0826], "triangles": [10, 11, 9, 9, 11, 8, 12, 13, 8, 8, 11, 12, 13, 15, 8, 4, 15, 16, 5, 15, 4, 8, 15, 5, 8, 6, 7, 8, 5, 6, 13, 14, 15, 4, 16, 3, 2, 3, 17, 1, 17, 19, 19, 0, 1, 17, 3, 16, 19, 17, 18, 2, 17, 1], "vertices": [1, 52, 16.74, 1.44, 1, 1, 52, 13.94, -3.79, 1, 1, 52, 11.62, -5.68, 1, 2, 52, 4.01, -6.36, 0.83272, 50, 15.17, -6.78, 0.16728, 3, 52, -2.94, -6.98, 0.08788, 50, 8.24, -5.96, 0.91193, 51, -14.46, 5, 0.00018, 3, 52, -6.84, -9.88, 2e-05, 50, 3.82, -8.01, 0.94114, 51, -10.31, 7.53, 0.05884, 2, 50, 0.59, -9.51, 0.79585, 51, -7.26, 9.39, 0.20415, 2, 50, -2.51, -10.14, 0.63152, 51, -4.25, 10.36, 0.36848, 2, 50, -9.13, -7.8, 0.15751, 51, 2.59, 8.78, 0.84249, 2, 50, -14.84, -5.79, 0.00398, 51, 8.49, 7.43, 0.99602, 2, 50, -16.36, -4.9, 8e-05, 51, 10.1, 6.72, 0.99992, 2, 50, -14.8, 8.82, 0.0031, 51, 10.1, -7.09, 0.9969, 2, 50, -13.07, 10.14, 0.01396, 51, 8.53, -8.6, 0.98604, 2, 50, -6.41, 11.16, 0.22705, 51, 2.03, -10.37, 0.77295, 2, 50, 0.51, 12.23, 0.62481, 51, -4.73, -12.21, 0.37519, 3, 52, -6.98, 6.89, 0.06817, 50, 7.11, 8.43, 0.86272, 51, -11.72, -9.18, 0.06912, 3, 52, -2, 5.26, 0.56783, 50, 11.66, 5.82, 0.43022, 51, -16.52, -7.1, 0.00195, 1, 52, 4.77, 8.2, 1, 1, 52, 13.49, 9.01, 1, 1, 52, 15.42, 5.97, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 12, 14, 18, 20, 20, 22, 22, 24, 32, 34, 34, 36, 36, 38, 24, 26, 26, 28, 14, 16, 16, 18, 8, 10, 10, 12, 28, 30, 30, 32, 4, 6, 6, 8], "width": 30, "height": 60}}, "jtr2": {"jtr2": {"type": "mesh", "uvs": [0.61624, 0, 0.76946, 0.02973, 0.76955, 0.02976, 0.88497, 0.11934, 0.88498, 0.11935, 0.92345, 0.16414, 0.92345, 0.16415, 0.96192, 0.23878, 0.96192, 0.2388, 0.96192, 0.28359, 0.96191, 0.28362, 0.88506, 0.37309, 1, 0.4177, 1, 0.44791, 0.96192, 0.44791, 0.96192, 0.55332, 0.96192, 0.65183, 0.96192, 0.7772, 0.96192, 0.89362, 0.96192, 0.97019, 0.96181, 0.97026, 0.80855, 1, 0.6533, 1, 0.61506, 0.98516, 0.615, 0.9851, 0.615, 0.97014, 0.615, 0.97013, 0.63566, 0.91402, 0.65343, 0.86574, 0.50038, 0.82119, 0.50038, 0.90407, 0.50038, 0.9851, 0.50033, 0.98516, 0.46208, 1, 0.15346, 1, 0.15346, 0.94028, 0.15349, 0.94023, 0.23033, 0.89551, 0.14122, 0.82635, 0.07657, 0.77618, 0.07655, 0.77615, 0.115, 0.74447, 0.115, 0.66228, 0.115, 0.58019, 0.115, 0.49362, 0.115, 0.41788, 0.11506, 0.41783, 0.19188, 0.38801, 0.15347, 0.32839, 0.15346, 0.32837, 0.15346, 0.23238, 0.15346, 0.14924, 0.15348, 0.1492, 0.19197, 0.11933, 0.192, 0.11931, 0.38433, 0.02975, 0.38434, 0.02975, 0.42283, 0.01481, 0.42288, 0.0148, 0.49914, 0], "triangles": [17, 29, 16, 28, 29, 17, 28, 17, 18, 27, 28, 18, 21, 27, 18, 27, 21, 26, 21, 18, 19, 25, 23, 24, 26, 22, 25, 25, 22, 23, 22, 26, 21, 21, 19, 20, 15, 41, 42, 39, 40, 41, 16, 29, 41, 38, 39, 41, 29, 38, 41, 37, 38, 29, 37, 29, 30, 33, 37, 30, 33, 36, 37, 36, 33, 35, 31, 33, 30, 33, 34, 35, 33, 31, 32, 53, 50, 51, 8, 11, 7, 6, 7, 47, 9, 10, 8, 48, 49, 50, 56, 58, 59, 58, 56, 57, 50, 53, 48, 4, 5, 48, 6, 47, 5, 8, 10, 11, 51, 52, 53, 47, 48, 5, 56, 59, 0, 56, 0, 55, 2, 0, 1, 2, 55, 0, 3, 54, 55, 3, 55, 2, 4, 53, 54, 53, 4, 48, 54, 3, 4, 47, 7, 11, 14, 11, 12, 14, 12, 13, 47, 44, 45, 47, 11, 14, 47, 43, 44, 15, 47, 14, 15, 42, 43, 45, 46, 47, 15, 43, 47, 16, 41, 15], "vertices": [1, 38, 13.57, -3.44, 1, 1, 38, 12.09, -5.66, 1, 1, 38, 12.09, -5.67, 1, 1, 38, 8.3, -7.01, 1, 1, 38, 8.3, -7.01, 1, 2, 35, 18.69, -6.4, 0.00063, 38, 6.44, -7.39, 0.99937, 2, 35, 18.69, -6.4, 0.00063, 38, 6.44, -7.39, 0.99937, 2, 35, 15.69, -7, 0.03209, 38, 3.38, -7.62, 0.96791, 2, 35, 15.68, -7, 0.03209, 38, 3.38, -7.62, 0.96791, 2, 35, 13.88, -7, 0.0886, 38, 1.6, -7.4, 0.9114, 3, 35, 13.88, -7, 0.08863, 38, 1.6, -7.4, 0.91137, 37, -15.27, 10.09, 0, 3, 35, 10.29, -5.8, 0.52828, 38, -1.83, -5.77, 0.46431, 37, -12.26, 7.79, 0.00741, 3, 35, 8.49, -7.59, 0.78607, 38, -3.83, -7.33, 0.18698, 37, -9.98, 8.9, 0.02696, 3, 35, 7.28, -7.59, 0.80883, 38, -5.03, -7.18, 0.15808, 37, -8.84, 8.5, 0.03308, 3, 35, 7.28, -7, 0.81943, 38, -4.96, -6.59, 0.14177, 37, -9.03, 7.94, 0.0388, 3, 35, 3.04, -7, 0.76876, 38, -9.17, -6.07, 0.00454, 37, -5.02, 6.56, 0.2267, 2, 35, -0.92, -7, 0.3901, 37, -1.28, 5.26, 0.6099, 2, 35, -5.96, -7, 0.01219, 37, 3.48, 3.62, 0.98781, 1, 37, 7.91, 2.09, 1, 1, 37, 10.82, 1.09, 1, 1, 37, 10.82, 1.08, 1, 1, 37, 11.17, -1.57, 1, 1, 37, 10.38, -3.86, 1, 1, 37, 9.62, -4.22, 1, 1, 37, 9.62, -4.22, 1, 1, 37, 9.05, -4.03, 1, 1, 37, 9.05, -4.03, 1, 2, 36, 8.06, 4.05, 0.00125, 37, 7.02, -2.99, 0.99875, 2, 36, 6.11, 4.29, 0.04457, 37, 5.28, -2.09, 0.95543, 2, 36, 4.36, 1.88, 0.71873, 37, 2.8, -3.76, 0.28127, 1, 36, 7.7, 1.93, 1, 1, 36, 10.95, 1.99, 1, 1, 36, 10.95, 1.99, 1, 1, 36, 11.56, 1.41, 1, 1, 36, 11.65, -3.41, 1, 1, 36, 9.25, -3.45, 1, 1, 36, 9.25, -3.45, 1, 1, 36, 7.43, -2.28, 1, 1, 36, 4.67, -3.72, 1, 2, 35, -5.92, 6.81, 0.0005, 36, 2.67, -4.77, 0.9995, 2, 35, -5.92, 6.81, 0.0005, 36, 2.67, -4.77, 0.9995, 2, 35, -4.64, 6.21, 0.01883, 36, 1.39, -4.19, 0.98117, 2, 35, -1.34, 6.21, 0.30409, 36, -1.92, -4.25, 0.69591, 3, 35, 1.96, 6.21, 0.71352, 38, -8.62, 7.17, 5e-05, 36, -5.22, -4.31, 0.28644, 3, 35, 5.44, 6.21, 0.89547, 38, -5.17, 6.75, 0.03821, 36, -8.69, -4.37, 0.06632, 3, 35, 8.49, 6.21, 0.78856, 38, -2.15, 6.38, 0.19944, 36, -11.74, -4.43, 0.01199, 3, 35, 8.49, 6.21, 0.78854, 38, -2.15, 6.37, 0.19947, 36, -11.74, -4.42, 0.01199, 3, 35, 9.69, 5.01, 0.57722, 38, -1.1, 5.04, 0.41973, 36, -12.96, -3.25, 0.00305, 2, 35, 12.08, 5.61, 0.17235, 38, 1.35, 5.34, 0.82765, 2, 35, 12.08, 5.61, 0.17235, 38, 1.35, 5.34, 0.82765, 2, 35, 15.94, 5.61, 0.0042, 38, 5.18, 4.87, 0.9958, 1, 38, 8.5, 4.46, 1, 1, 38, 8.5, 4.46, 1, 1, 38, 9.62, 3.72, 1, 1, 38, 9.62, 3.72, 1, 1, 38, 12.83, 0.3, 1, 1, 38, 12.83, 0.3, 1, 1, 38, 13.35, -0.37, 1, 1, 38, 13.35, -0.37, 1, 1, 38, 13.79, -1.62, 1], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 56, 58, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 78, 80, 90, 92, 92, 94, 94, 96, 96, 98, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 80, 82, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 58, 60, 60, 62, 52, 54, 54, 56, 74, 76, 76, 78, 88, 90, 86, 88, 82, 84, 84, 86, 98, 100, 100, 102], "width": 26, "height": 67}}, "jtr3": {"jtr3": {"type": "mesh", "uvs": [0.27127, 0.03753, 0.54412, 0.08428, 0.75051, 0.2886, 0.8508, 0.44308, 0.85022, 0.75865, 0.54835, 0.92427, 0.39153, 0.92319, 0.27099, 0.95863, 0.16934, 0.88118, 0.19231, 0.72429, 0.07269, 0.56593, 0.03465, 0.45781, 0.22391, 0.38322, 0.1749, 0.29047, 0.1405, 0.10397, 0.16267, 0.03865, 0.61519, 0.61401], "triangles": [0, 14, 15, 13, 14, 0, 1, 12, 13, 1, 13, 0, 12, 1, 2, 10, 11, 12, 16, 12, 2, 16, 2, 3, 9, 10, 12, 16, 9, 12, 4, 16, 3, 6, 9, 16, 5, 6, 16, 8, 9, 6, 4, 5, 16, 7, 8, 6], "vertices": [-6.18, -5.1, -16.04, -3.45, -25.16, 5.37, -29.84, 12.06, -30.28, 29.01, -18.87, 41.22, -12.24, 40.8, -7.47, 42.78, -3.3, 38.86, -4, 30.68, 1.01, 22.58, 2.69, 17, -4.78, 12.92, -2.68, 8.15, -1.04, -1.51, -1.84, -4.93, -21.17, 19.91], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30], "width": 40, "height": 52}}, "jtr4": {"jtr4": {"type": "mesh", "uvs": [0.64366, 0, 0.71447, 0.02023, 0.71452, 0.02025, 0.7503, 0.0407, 0.75036, 0.04078, 0.75036, 0.16776, 0.75036, 0.24812, 0.75036, 0.3459, 0.75036, 0.46108, 0.75036, 0.57225, 0.75036, 0.67309, 0.85762, 0.69397, 0.92891, 0.77544, 0.92893, 0.77549, 0.92893, 0.83678, 0.92885, 0.83687, 0.86933, 0.86238, 0.78603, 0.89808, 0.75033, 0.97968, 0.75023, 0.97975, 0.67937, 1, 0.57092, 1, 0.53544, 0.97972, 0.53541, 0.9797, 0.46464, 0.89882, 0.46464, 0.97966, 0.46451, 0.97976, 0.35827, 1, 0.32063, 1, 0.24977, 0.97976, 0.24968, 0.97969, 0.21279, 0.90591, 0.17822, 0.83678, 0.17821, 0.83675, 0.17821, 0.70619, 0.17821, 0.62716, 0.17821, 0.51019, 0.17822, 0.51017, 0.21327, 0.43005, 0.2496, 0.347, 0.214, 0.32665, 0.21395, 0.3266, 0.17822, 0.22452, 0.17821, 0.2245, 0.17821, 0.14283, 0.17823, 0.14279, 0.21398, 0.10193, 0.21401, 0.10191, 0.28548, 0.06107, 0.28553, 0.06105, 0.35699, 0.04063, 0.35702, 0.04063, 0.5703, 0], "triangles": [52, 3, 4, 46, 43, 44, 46, 42, 43, 5, 49, 4, 44, 45, 46, 47, 41, 42, 52, 0, 1, 51, 52, 4, 46, 47, 42, 39, 40, 41, 6, 49, 5, 39, 47, 48, 39, 41, 47, 1, 3, 52, 2, 3, 1, 4, 50, 51, 4, 49, 50, 6, 48, 49, 7, 48, 6, 7, 39, 48, 35, 9, 10, 13, 16, 12, 11, 12, 10, 13, 14, 16, 16, 14, 15, 16, 17, 10, 12, 16, 10, 10, 17, 24, 24, 17, 23, 23, 17, 20, 23, 20, 22, 20, 17, 18, 20, 21, 22, 20, 18, 19, 34, 35, 10, 24, 33, 34, 33, 24, 32, 34, 10, 24, 31, 32, 24, 30, 31, 24, 30, 28, 29, 28, 30, 24, 28, 24, 27, 27, 24, 25, 26, 27, 25, 8, 39, 7, 38, 39, 8, 37, 38, 8, 9, 37, 8, 36, 37, 9, 35, 36, 9], "vertices": [2, 44, -8.46, 1.47, 0, 42, 4.32, -1.97, 1, 2, 44, -8.13, 1.95, 0, 42, 3.99, -2.44, 1, 2, 44, -8.13, 1.95, 0, 42, 3.99, -2.44, 1, 2, 44, -7.83, 2.17, 0, 42, 3.69, -2.66, 1, 2, 44, -7.83, 2.17, 0, 42, 3.69, -2.66, 1, 3, 41, 5.77, -2.12, 0.00231, 44, -6.23, 1.93, 0, 42, 2.09, -2.42, 0.99769, 2, 41, 4.75, -2.09, 0.05922, 42, 1.08, -2.27, 0.94078, 3, 41, 3.5, -2.05, 0.38899, 44, -3.99, 1.61, 0.00769, 42, -0.15, -2.08, 0.60332, 3, 41, 2.04, -2.01, 0.80822, 44, -2.53, 1.39, 0.10266, 42, -1.6, -1.86, 0.08912, 3, 41, 0.62, -1.96, 0.53447, 44, -1.13, 1.19, 0.46541, 42, -3, -1.64, 0.00012, 2, 41, -0.66, -1.92, 0.03178, 44, 0.14, 1, 0.96822, 1, 44, 0.52, 1.74, 1, 1, 44, 1.62, 2.1, 1, 1, 44, 1.62, 2.1, 1, 1, 44, 2.39, 1.99, 1, 1, 44, 2.39, 1.99, 1, 1, 44, 2.65, 1.51, 1, 1, 44, 3.01, 0.84, 1, 1, 44, 4, 0.44, 1, 2, 43, 3.63, 2.65, 0, 44, 4, 0.44, 1, 2, 43, 3.91, 2.14, 0.00833, 44, 4.19, -0.11, 0.99167, 2, 43, 3.94, 1.35, 0.05973, 44, 4.07, -0.89, 0.94027, 2, 43, 3.7, 1.08, 0.09958, 44, 3.78, -1.11, 0.90042, 2, 43, 3.7, 1.08, 0.09963, 44, 3.78, -1.11, 0.90037, 2, 43, 2.69, 0.52, 0.75818, 44, 2.68, -1.47, 0.24182, 2, 43, 3.72, 0.57, 1, 44, 3.7, -1.62, 0, 1, 43, 3.72, 0.57, 1, 1, 43, 4.01, -0.19, 1, 1, 43, 4.02, -0.47, 1, 1, 43, 3.79, -0.99, 1, 1, 43, 3.79, -0.99, 1, 1, 43, 2.86, -1.3, 1, 1, 43, 1.99, -1.59, 1, 1, 43, 1.99, -1.59, 1, 2, 41, -0.96, 2.25, 0.06827, 43, 0.33, -1.67, 0.93173, 2, 41, 0.05, 2.22, 0.37381, 43, -0.68, -1.71, 0.62619, 3, 41, 1.54, 2.18, 0.79482, 43, -2.17, -1.78, 0.18562, 42, -1.59, 2.35, 0.01956, 3, 41, 1.54, 2.18, 0.79485, 43, -2.17, -1.78, 0.18557, 42, -1.59, 2.35, 0.01958, 3, 41, 2.55, 1.89, 0.76454, 43, -3.2, -1.57, 0.04934, 42, -0.62, 1.95, 0.18612, 3, 41, 3.6, 1.59, 0.25587, 43, -4.27, -1.35, 0.00311, 42, 0.38, 1.53, 0.74102, 3, 41, 3.87, 1.84, 0.12031, 43, -4.51, -1.62, 0.00043, 42, 0.68, 1.75, 0.87926, 3, 41, 3.87, 1.84, 0.12021, 43, -4.51, -1.62, 0.00043, 42, 0.68, 1.75, 0.87936, 2, 41, 5.18, 2.06, 0.00127, 42, 2, 1.81, 0.99873, 2, 41, 5.18, 2.06, 0.00126, 42, 2.01, 1.81, 0.99874, 1, 42, 3.03, 1.65, 1, 2, 44, -7.15, -2.14, 0, 42, 3.03, 1.65, 1, 2, 44, -7.63, -1.81, 0, 42, 3.51, 1.31, 1, 2, 44, -7.63, -1.81, 0, 42, 3.51, 1.31, 1, 2, 44, -8.07, -1.22, 0, 42, 3.95, 0.72, 1, 2, 44, -8.07, -1.22, 0, 42, 3.95, 0.72, 1, 2, 44, -8.25, -0.67, 0, 42, 4.13, 0.17, 1, 2, 44, -8.25, -0.67, 0, 42, 4.13, 0.17, 1, 2, 44, -8.54, 0.95, 0, 42, 4.4, -1.45, 1], "hull": 53, "edges": [0, 104, 0, 2, 2, 4, 4, 6, 6, 8, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 64, 66, 72, 74, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 20, 22, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 74, 76, 76, 78, 70, 72, 66, 68, 68, 70, 60, 62, 62, 64, 30, 32, 32, 34], "width": 28, "height": 49}}, "jtr7t2": {"jtr7t2": {"type": "mesh", "uvs": [0.67275, 0, 0.93935, 0, 0.988, 0.22502, 0.988, 0.53137, 0.86592, 0.77079, 0.751, 1, 0.56075, 1, 0.29753, 1, 0, 0.70144, 0, 0.5944, 0.18478, 0.28858, 0.35915, 0], "triangles": [1, 2, 0, 7, 8, 9, 2, 4, 0, 3, 4, 2, 10, 6, 7, 7, 9, 10, 10, 11, 0, 0, 4, 10, 4, 6, 10, 5, 6, 4], "vertices": [-0.75, -0.21, -1.31, 2.4, 0.78, 3.35, 3.78, 3.99, 6.38, 3.3, 8.86, 2.66, 9.26, 0.8, 9.81, -1.77, 7.52, -5.31, 6.48, -5.53, 3.1, -4.37, -0.09, -3.27], "hull": 12, "edges": [2, 4, 4, 6, 14, 16, 16, 18, 6, 8, 8, 10, 18, 20, 20, 22, 2, 0, 0, 22, 10, 12, 12, 14], "width": 10, "height": 10}}, "nst": {"nst": {"type": "mesh", "uvs": [0.63366, 0.062, 0.82894, 0.06207, 0.89534, 0.1597, 0.97905, 0.28276, 0.97956, 0.51925, 0.97994, 0.69703, 0.83077, 0.84022, 0.6643, 1, 0.60553, 1, 0.45383, 0.92712, 0.29943, 0.85295, 0.13586, 0.77438, 0, 0.70911, 0, 0.66509, 0.08871, 0.49203, 0.15342, 0.30701, 0.21407, 0.13358, 0.30048, 0.1021, 0.41074, 0.06193], "triangles": [11, 12, 13, 10, 11, 14, 11, 13, 14, 2, 0, 1, 2, 4, 0, 4, 2, 3, 6, 4, 5, 17, 15, 16, 10, 15, 17, 10, 14, 15, 17, 18, 10, 18, 0, 9, 0, 4, 6, 0, 6, 9, 9, 10, 18, 8, 9, 6, 7, 8, 6], "vertices": [1, 88, 6.12, -8.65, 1, 1, 88, -1.4, -7.2, 1, 1, 88, -3.45, -4.1, 1, 1, 88, -6.05, -0.19, 1, 1, 88, -4.85, 6.15, 1, 1, 88, -3.96, 10.92, 1, 1, 88, 2.52, 13.66, 1, 1, 88, 9.75, 16.71, 1, 1, 88, 12.02, 16.28, 1, 1, 88, 17.49, 13.21, 1, 1, 88, 23.05, 10.08, 1, 1, 88, 28.95, 6.77, 1, 1, 88, 33.85, 4.02, 1, 1, 88, 33.62, 2.84, 1, 1, 88, 29.32, -1.14, 1, 1, 88, 25.88, -5.62, 1, 1, 88, 22.65, -9.82, 1, 1, 88, 19.16, -10.03, 1, 1, 88, 14.71, -10.29, 1], "hull": 19, "edges": [14, 16, 24, 26, 26, 28, 10, 12, 12, 14, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 36, 32, 34, 34, 36, 28, 30, 30, 32, 20, 22, 22, 24, 16, 18, 18, 20], "width": 46, "height": 32}}, "jtr8": {"jtr7": {"type": "mesh", "uvs": [0.52232, 0.05535, 0.55043, 0.0769, 0.55033, 0.15395, 0.66229, 0.19433, 0.7231, 0.2207, 0.72339, 0.28817, 0.68794, 0.34614, 0.67292, 0.38744, 0.47528, 0.44151, 0.47542, 0.54938, 0.47549, 0.6041, 0.50031, 0.63416, 0.50034, 0.71043, 0.50036, 0.78372, 0.5063, 0.8014, 0.52532, 0.84586, 0.52536, 0.88479, 0.5077, 0.89823, 0.44936, 0.94312, 0.44322, 0.94262, 0.37468, 0.94259, 0.36151, 0.89289, 0.34945, 0.84737, 0.29488, 0.88909, 0.25018, 0.92327, 0.14112, 0.92327, 0.1215, 0.91295, 0.097, 0.89735, 0.09639, 0.82671, 0.12415, 0.82671, 0.07026, 0.72215, 0.04962, 0.67282, 0.04967, 0.58888, 0.04971, 0.52963, 0.04976, 0.44747, 0.15119, 0.42584, 0.09737, 0.34258, 0.07462, 0.29728, 0.07455, 0.25061, 0.09978, 0.15338, 0.12723, 0.12844, 0.19667, 0.08962, 0.23151, 0.06909, 0.39475, 0.06909, 0.42444, 0.05742, 0.52413, 0.05738, 0.3398, 0.55021, 0.295, 0.61354, 0.22497, 0.65216, 0.21924, 0.68522, 0.3064, 0.66361, 0.39541, 0.62677, 0.42186, 0.57328], "triangles": [18, 19, 17, 17, 19, 21, 19, 20, 21, 24, 25, 29, 23, 24, 29, 26, 29, 25, 16, 17, 15, 26, 27, 29, 27, 28, 29, 15, 17, 22, 15, 22, 14, 14, 22, 13, 21, 22, 17, 13, 50, 12, 23, 29, 22, 12, 51, 11, 51, 12, 50, 29, 49, 22, 49, 50, 22, 50, 13, 22, 29, 30, 49, 49, 48, 50, 48, 47, 50, 30, 31, 49, 49, 31, 48, 31, 32, 48, 32, 33, 48, 47, 48, 33, 33, 34, 35, 47, 33, 35, 51, 10, 11, 50, 47, 51, 47, 46, 51, 51, 52, 10, 51, 46, 52, 47, 35, 46, 52, 9, 10, 52, 46, 9, 46, 8, 9, 46, 35, 8, 7, 8, 6, 2, 8, 36, 36, 43, 2, 41, 42, 43, 43, 36, 41, 6, 8, 2, 2, 44, 45, 44, 2, 43, 36, 8, 35, 40, 38, 39, 41, 36, 40, 3, 6, 2, 5, 3, 4, 5, 6, 3, 38, 40, 36, 36, 37, 38, 2, 45, 1, 1, 45, 0], "vertices": [1, 22, 14.75, -13.04, 1, 1, 22, 13.38, -13.84, 1, 2, 22, 9.5, -12.83, 0.99857, 21, 19, -13.61, 0.00143, 2, 22, 6.35, -16.64, 0.99426, 21, 15.53, -17.13, 0.00574, 2, 22, 4.41, -18.65, 0.97138, 21, 13.42, -18.96, 0.02862, 2, 22, 1.01, -17.78, 0.9022, 21, 10.11, -17.8, 0.0978, 2, 22, -1.55, -15.65, 0.70935, 21, 7.74, -15.46, 0.29065, 2, 22, -3.48, -14.53, 0.62321, 21, 5.92, -14.17, 0.37679, 2, 22, -4.21, -6.17, 0.14517, 21, 5.91, -5.78, 0.85483, 2, 21, 0.62, -3.91, 0.98416, 24, -6.39, 4.32, 0.01584, 2, 21, -2.06, -2.97, 0.70125, 24, -3.59, 3.83, 0.29875, 2, 21, -3.86, -3.38, 0.41675, 24, -1.88, 4.55, 0.58325, 2, 21, -7.6, -2.05, 0.04654, 24, 2.03, 3.87, 0.95346, 1, 24, 5.79, 3.22, 1, 1, 24, 6.73, 3.3, 1, 1, 24, 9.14, 3.66, 1, 1, 24, 11.14, 3.31, 1, 1, 24, 11.71, 2.5, 1, 1, 24, 13.61, -0.2, 1, 1, 24, 13.54, -0.44, 1, 2, 23, 15.18, 15.74, 0.00016, 24, 13.07, -3.14, 0.99984, 1, 24, 10.42, -3.16, 1, 1, 24, 7.87, -2.76, 1, 1, 24, 9.61, -4.1, 1, 1, 24, 11.33, -5.65, 1, 1, 24, 11.33, -9.75, 1, 1, 24, 10.79, -10.52, 1, 1, 24, 9.97, -11.49, 1, 2, 23, 15.84, 5.03, 0.11997, 24, 6.23, -11.41, 0.88003, 2, 23, 14.98, 5.84, 0.17987, 24, 6.22, -10.19, 0.82013, 2, 23, 12.83, 1.09, 0.57723, 24, 1.08, -12.2, 0.42277, 3, 22, -13.13, 13.43, 4e-05, 23, 12.09, -1.41, 0.71117, 24, -1.41, -13.52, 0.28879, 2, 22, -8.44, 13.88, 0.01615, 23, 9.67, -5.07, 0.98385, 2, 22, -5.09, 14.07, 0.06642, 23, 7.82, -7.63, 0.93358, 3, 22, -0.47, 14.03, 0.15901, 21, 11.39, 14.02, 0.00032, 23, 5, -11.03, 0.84068, 3, 22, 0.54, 8.21, 0.46694, 21, 11.89, 8.13, 0.04661, 23, -0.14, -8.54, 0.48645, 2, 22, 4.65, 7.49, 0.94486, 23, -3.68, -11.53, 0.05514, 2, 22, 7.1, 7.56, 0.98482, 23, -5.25, -13.48, 0.01518, 2, 22, 9.43, 6.87, 0.9975, 23, -7.29, -14.83, 0.0025, 1, 22, 14.06, 4.6, 1, 1, 22, 15.04, 3.21, 1, 1, 22, 16.3, 0.02, 1, 1, 22, 16.98, -1.6, 1, 1, 22, 15.34, -7.92, 1, 1, 22, 15.63, -9.22, 1, 1, 22, 14.63, -13.08, 1, 4, 22, -8.51, 0.39, 0.07666, 21, 2.2, 1.12, 0.58661, 23, -1.15, -0.17, 0.19311, 24, -7.09, -0.91, 0.14361, 4, 22, -11.28, 2.86, 0.05482, 21, -0.34, 3.82, 0.4195, 23, 2.08, 1.66, 0.23641, 24, -4.13, -3.15, 0.28927, 4, 22, -12.63, 6, 0.03726, 21, -1.42, 7.07, 0.28511, 23, 5.48, 1.94, 0.33105, 24, -2.52, -6.17, 0.34658, 4, 22, -14.25, 6.53, 0.03045, 21, -2.99, 7.73, 0.23301, 23, 6.5, 3.31, 0.2933, 24, -0.86, -6.55, 0.44324, 4, 22, -13.92, 3.03, 0.03323, 21, -2.96, 4.22, 0.26395, 23, 3.08, 4.11, 0.21066, 24, -1.48, -3.1, 0.49216, 4, 22, -12.84, -0.76, 0.01771, 21, -2.21, 0.35, 0.513, 23, -0.86, 4.3, 0.10094, 24, -2.87, 0.59, 0.36836, 4, 22, -10.38, -2.43, 0.02184, 21, 0.09, -1.53, 0.75463, 23, -3.23, 2.5, 0.07228, 24, -5.46, 2.06, 0.15126], "hull": 46, "edges": [0, 90, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 48, 50, 50, 52, 58, 60, 60, 62, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 56, 58, 52, 54, 54, 56, 66, 68, 62, 64, 64, 66, 14, 16, 16, 18, 18, 20, 22, 24, 24, 26, 44, 46, 46, 48, 40, 42, 42, 44], "width": 40, "height": 52}}, "jtr9": {"jtr8": {"type": "mesh", "uvs": [0.52531, 0.0575, 0.55046, 0.07698, 0.55037, 0.08042, 0.55029, 0.15414, 0.52559, 0.17536, 0.52526, 0.21024, 0.52529, 0.28736, 0.5264, 0.28697, 0.47524, 0.3561, 0.47534, 0.447, 0.47543, 0.52699, 0.47553, 0.61553, 0.50061, 0.6348, 0.50038, 0.7539, 0.50048, 0.80892, 0.52538, 0.84622, 0.52532, 0.88475, 0.45049, 0.94272, 0.37446, 0.94254, 0.37447, 0.86586, 0.31614, 0.89303, 0.25047, 0.92362, 0.15, 0.92333, 0.08897, 0.88819, 0.12028, 0.82661, 0.15764, 0.82657, 0.10294, 0.76383, 0.05381, 0.70749, 0.04972, 0.67849, 0.04972, 0.60775, 0.04971, 0.54545, 0.0497, 0.44883, 0.16715, 0.41738, 0.1124, 0.37593, 0.07443, 0.28813, 0.07451, 0.25049, 0.0998, 0.15341, 0.12429, 0.11549, 0.19915, 0.07669, 0.38845, 0.07668, 0.42396, 0.05747, 0.51382, 0.0575, 0.14225, 0.66852, 0.21325, 0.62545, 0.23225, 0.54391], "triangles": [25, 43, 19, 20, 25, 19, 25, 23, 24, 21, 22, 25, 22, 23, 25, 20, 21, 25, 26, 42, 25, 42, 29, 43, 28, 29, 42, 27, 28, 42, 26, 27, 42, 25, 42, 43, 13, 11, 12, 13, 19, 43, 13, 43, 11, 19, 13, 14, 19, 14, 15, 16, 19, 15, 17, 18, 19, 16, 17, 19, 43, 44, 11, 44, 9, 10, 44, 10, 11, 44, 32, 9, 30, 31, 32, 30, 32, 44, 43, 29, 30, 44, 43, 30, 32, 8, 9, 2, 0, 1, 0, 2, 41, 2, 3, 41, 40, 41, 4, 41, 3, 4, 40, 4, 39, 5, 8, 4, 8, 5, 6, 8, 39, 4, 8, 6, 7, 36, 8, 35, 8, 34, 35, 33, 34, 8, 36, 38, 39, 36, 37, 38, 8, 36, 39, 32, 33, 8], "vertices": [1, 27, 14.64, -12.17, 1, 1, 27, 13.4, -12.88, 1, 1, 27, 13.23, -12.83, 1, 2, 27, 9.52, -11.83, 0.99948, 26, 21.07, -11.69, 0.00052, 2, 27, 8.71, -10.59, 0.99633, 26, 20.27, -10.44, 0.00367, 2, 27, 6.96, -10.11, 0.98293, 26, 18.53, -9.94, 0.01707, 2, 27, 3.09, -9.08, 0.90469, 26, 14.66, -8.87, 0.09531, 2, 27, 3.1, -9.13, 0.90583, 26, 14.67, -8.91, 0.09417, 2, 27, 0.15, -6.22, 0.60298, 26, 11.76, -5.98, 0.39702, 2, 27, -4.41, -5, 0.03074, 26, 7.2, -4.71, 0.96926, 1, 26, 3.19, -3.6, 1, 2, 26, -1.24, -2.37, 0.62121, 28, -1.68, 3.11, 0.37879, 2, 26, -2.48, -3.07, 0.33965, 28, -0.54, 3.97, 0.66035, 1, 28, 5.59, 3.08, 1, 1, 28, 8.42, 2.68, 1, 1, 28, 10.48, 3.39, 1, 1, 28, 12.46, 3.11, 1, 1, 28, 15.02, -0.28, 1, 2, 28, 14.58, -3.29, 0.99928, 53, 13.54, 7.05, 0.00072, 2, 28, 10.64, -2.73, 0.76654, 53, 9.59, 6.45, 0.23346, 2, 28, 11.71, -5.24, 0.21218, 53, 11.34, 4.36, 0.78782, 2, 28, 12.91, -8.06, 0.01174, 53, 13.31, 2, 0.98826, 1, 53, 13.9, -1.98, 1, 1, 53, 12.46, -4.66, 1, 1, 53, 9.11, -3.91, 1, 1, 53, 8.88, -2.43, 1, 1, 53, 5.98, -5.08, 1, 1, 53, 3.38, -7.47, 1, 2, 27, -12.51, 14.5, 0.00107, 53, 1.92, -7.85, 0.99893, 2, 27, -8.88, 14.61, 0.92503, 26, 2.93, 14.95, 0.07497, 2, 27, -5.41, 14.8, 0.84667, 26, 6.4, 15.1, 0.15333, 2, 27, -0.06, 14.75, 0.86139, 26, 11.76, 14.99, 0.13861, 2, 27, 1.12, 8.23, 0.89253, 26, 12.87, 8.46, 0.10747, 2, 27, 3.14, 8.74, 0.99784, 26, 14.89, 8.95, 0.00216, 1, 27, 7.76, 8.52, 1, 1, 27, 9.62, 7.92, 1, 1, 27, 14.22, 5.56, 1, 1, 27, 15.87, 4.11, 1, 1, 27, 17.04, 0.69, 1, 1, 27, 15.09, -6.62, 1, 1, 27, 15.69, -8.25, 1, 1, 27, 14.76, -11.72, 1, 1, 53, 0.85, -4.27, 1, 3, 26, 2.31, 7.94, 0.02423, 28, -3.87, -7.57, 0.06932, 53, -1.8, -1.8, 0.90645, 2, 27, -4.77, 7.13, 0.17302, 26, 6.97, 7.42, 0.82698], "hull": 42, "edges": [0, 82, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 54, 56, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 44, 46, 48, 50, 46, 48, 60, 62, 16, 18, 18, 20, 20, 22, 50, 52, 52, 54, 38, 40, 40, 42, 56, 58, 58, 60], "width": 40, "height": 52}}, "jtj2": {"jtj2": {"type": "mesh", "uvs": [0.96847, 0.14108, 0.81725, 0.34766, 0.68695, 0.52565, 0.52567, 0.74597, 0.35418, 0.98024, 0.24606, 0.8298, 0.15964, 0.70956, 0.08576, 0.60676, 0.08533, 0.37119, 0.08491, 0.14206, 0.22043, 0.14202, 0.40478, 0.14197, 0.56489, 0.14192, 0.70923, 0.14188, 0.87618, 0.14183], "triangles": [1, 13, 14, 1, 14, 0, 2, 12, 13, 8, 9, 10, 6, 7, 8, 10, 6, 8, 11, 6, 10, 5, 11, 3, 5, 6, 11, 4, 5, 3, 2, 13, 1, 3, 11, 12, 3, 12, 2], "vertices": [1, 64, 6.71, -6.43, 1, 1, 66, -0.74, 1.91, 1, 1, 66, 1.24, 1.97, 1, 2, 66, 3.69, 2.05, 0.6964, 67, -1.93, 1.21, 0.3036, 2, 66, 6.29, 2.13, 0.08974, 67, -0.78, 3.55, 0.91026, 2, 66, 6.63, 0.51, 0.01495, 67, 0.81, 3.08, 0.98505, 1, 67, 2.08, 2.72, 1, 1, 67, 3.17, 2.4, 1, 1, 67, 3.86, 0.87, 1, 1, 67, 4.53, -0.61, 1, 2, 66, 3.86, -3.54, 0.00991, 67, 3.08, -1.26, 0.99009, 2, 66, 2.16, -2.21, 0.42991, 67, 1.11, -2.14, 0.57009, 1, 64, 2.33, -8.2, 1, 1, 64, 3.9, -7.57, 1, 1, 64, 5.71, -6.84, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 23, "height": 14}}, "jtj3": {"jtj2": {"type": "mesh", "uvs": [0.96847, 0.14108, 0.81725, 0.34766, 0.68695, 0.52565, 0.52567, 0.74597, 0.35418, 0.98024, 0.24606, 0.8298, 0.15964, 0.70956, 0.08576, 0.60676, 0.08533, 0.37119, 0.08491, 0.14206, 0.22043, 0.14202, 0.40478, 0.14197, 0.56489, 0.14192, 0.70923, 0.14188, 0.87618, 0.14183], "triangles": [1, 13, 14, 1, 14, 0, 2, 12, 13, 8, 9, 10, 6, 7, 8, 10, 6, 8, 11, 6, 10, 5, 11, 3, 5, 6, 11, 4, 5, 3, 2, 13, 1, 3, 11, 12, 3, 12, 2], "vertices": [1, 72, 6.71, -6.43, 1, 1, 74, -0.74, 1.91, 1, 1, 74, 1.24, 1.97, 1, 2, 74, 3.69, 2.05, 0.6964, 75, -1.93, 1.21, 0.3036, 2, 74, 6.29, 2.13, 0.08974, 75, -0.78, 3.55, 0.91026, 2, 74, 6.63, 0.51, 0.01495, 75, 0.81, 3.08, 0.98505, 1, 75, 2.08, 2.72, 1, 1, 75, 3.17, 2.4, 1, 1, 75, 3.86, 0.87, 1, 1, 75, 4.53, -0.61, 1, 2, 74, 3.86, -3.54, 0.00991, 75, 3.08, -1.26, 0.99009, 2, 74, 2.16, -2.21, 0.42991, 75, 1.11, -2.14, 0.57009, 1, 72, 2.33, -8.2, 1, 1, 72, 3.9, -7.57, 1, 1, 72, 5.71, -6.84, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 23, "height": 14}}, "jtj4": {"jtj1": {"type": "mesh", "uvs": [0.50631, 0.07544, 0.67385, 0.0682, 0.84491, 0.0608, 0.89934, 0.07768, 0.86946, 0.25058, 0.84085, 0.41611, 0.81352, 0.57429, 0.81398, 0.71908, 0.81442, 0.85335, 0.8148, 0.97324, 0.73539, 0.92009, 0.64292, 0.85819, 0.44441, 0.85816, 0.26191, 0.85813, 0.08905, 0.85811, 0.23754, 0.72427, 0.37458, 0.60075, 0.49619, 0.49113, 0.46676, 0.35497, 0.4352, 0.20903, 0.40725, 0.07973], "triangles": [4, 1, 2, 0, 1, 19, 19, 20, 0, 4, 2, 3, 12, 13, 15, 14, 15, 13, 12, 16, 17, 17, 11, 12, 6, 11, 17, 6, 7, 11, 12, 15, 16, 8, 11, 7, 10, 11, 8, 10, 8, 9, 1, 18, 19, 4, 5, 1, 1, 17, 18, 5, 17, 1, 6, 17, 5], "vertices": [1, 72, 7.86, -5.46, 1, 1, 72, 9.11, -4.91, 1, 1, 72, 10.38, -4.34, 1, 1, 72, 10.83, -4.28, 1, 1, 76, 0.63, 1.26, 1, 2, 76, 1.84, 1.25, 0.99865, 77, -1.91, -1.06, 0.00135, 2, 76, 2.98, 1.24, 0.76183, 77, -1.36, -0.05, 0.23817, 2, 76, 3.99, 1.44, 0.15619, 77, -1.07, 0.93, 0.84381, 2, 76, 4.93, 1.62, 0.00334, 77, -0.79, 1.85, 0.99666, 1, 77, -0.55, 2.66, 1, 1, 77, -0.04, 2.11, 1, 1, 77, 0.55, 1.47, 1, 1, 77, 2.1, 1, 1, 1, 77, 3.51, 0.57, 1, 1, 77, 4.86, 0.16, 1, 1, 77, 3.43, -0.4, 1, 2, 76, 3.84, -2.23, 0.04212, 77, 2.1, -0.92, 0.95788, 2, 76, 2.88, -1.4, 0.64431, 77, 0.93, -1.37, 0.35569, 2, 76, 1.98, -1.82, 0.97418, 77, 0.88, -2.37, 0.02582, 1, 76, 1.01, -2.27, 1, 1, 72, 7.13, -5.79, 1], "hull": 21, "edges": [4, 6, 32, 34, 28, 30, 30, 32, 26, 28, 22, 24, 24, 26, 18, 20, 20, 22, 16, 18, 12, 14, 14, 16, 10, 12, 6, 8, 8, 10, 34, 36, 36, 38, 38, 40, 0, 40, 0, 2, 2, 4], "width": 16, "height": 14}}, "jtj5": {"jtj2": {"type": "mesh", "uvs": [0.96847, 0.14108, 0.81725, 0.34766, 0.68695, 0.52565, 0.52567, 0.74597, 0.35418, 0.98024, 0.24606, 0.8298, 0.15964, 0.70956, 0.08576, 0.60676, 0.08533, 0.37119, 0.08491, 0.14206, 0.22043, 0.14202, 0.40478, 0.14197, 0.56489, 0.14192, 0.70923, 0.14188, 0.87618, 0.14183], "triangles": [1, 13, 14, 1, 14, 0, 2, 12, 13, 8, 9, 10, 6, 7, 8, 10, 6, 8, 11, 6, 10, 5, 11, 3, 5, 6, 11, 4, 5, 3, 2, 13, 1, 3, 11, 12, 3, 12, 2], "vertices": [1, 80, 6.71, -6.43, 1, 1, 82, -0.74, 1.91, 1, 1, 82, 1.24, 1.97, 1, 2, 82, 3.69, 2.05, 0.6964, 83, -1.93, 1.21, 0.3036, 2, 82, 6.29, 2.13, 0.08974, 83, -0.78, 3.55, 0.91026, 2, 82, 6.63, 0.51, 0.01495, 83, 0.81, 3.08, 0.98505, 1, 83, 2.08, 2.72, 1, 1, 83, 3.17, 2.4, 1, 1, 83, 3.86, 0.87, 1, 1, 83, 4.53, -0.61, 1, 2, 82, 3.86, -3.54, 0.00991, 83, 3.08, -1.26, 0.99009, 2, 82, 2.16, -2.21, 0.42991, 83, 1.11, -2.14, 0.57009, 1, 80, 2.33, -8.2, 1, 1, 80, 3.9, -7.57, 1, 1, 80, 5.71, -6.84, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 23, "height": 14}}, "jtj6": {"jtj1": {"type": "mesh", "uvs": [0.50631, 0.07544, 0.67385, 0.0682, 0.84491, 0.0608, 0.89934, 0.07768, 0.86946, 0.25058, 0.84085, 0.41611, 0.81352, 0.57429, 0.81398, 0.71908, 0.81442, 0.85335, 0.8148, 0.97324, 0.73539, 0.92009, 0.64292, 0.85819, 0.44441, 0.85816, 0.26191, 0.85813, 0.08905, 0.85811, 0.23754, 0.72427, 0.37458, 0.60075, 0.49619, 0.49113, 0.46676, 0.35497, 0.4352, 0.20903, 0.40725, 0.07973], "triangles": [4, 1, 2, 0, 1, 19, 19, 20, 0, 4, 2, 3, 12, 13, 15, 14, 15, 13, 12, 16, 17, 17, 11, 12, 6, 11, 17, 6, 7, 11, 12, 15, 16, 8, 11, 7, 10, 11, 8, 10, 8, 9, 1, 18, 19, 4, 5, 1, 1, 17, 18, 5, 17, 1, 6, 17, 5], "vertices": [1, 80, 7.86, -5.46, 1, 1, 80, 9.11, -4.91, 1, 1, 80, 10.38, -4.34, 1, 1, 80, 10.83, -4.28, 1, 1, 84, 0.63, 1.26, 1, 2, 84, 1.84, 1.25, 0.99865, 85, -1.91, -1.06, 0.00135, 2, 84, 2.98, 1.24, 0.76183, 85, -1.36, -0.05, 0.23817, 2, 84, 3.99, 1.44, 0.15619, 85, -1.07, 0.93, 0.84381, 2, 84, 4.93, 1.62, 0.00334, 85, -0.79, 1.85, 0.99666, 1, 85, -0.55, 2.66, 1, 1, 85, -0.04, 2.11, 1, 1, 85, 0.55, 1.47, 1, 1, 85, 2.1, 1, 1, 1, 85, 3.51, 0.57, 1, 1, 85, 4.86, 0.16, 1, 1, 85, 3.43, -0.4, 1, 2, 84, 3.84, -2.23, 0.04212, 85, 2.1, -0.92, 0.95788, 2, 84, 2.88, -1.4, 0.64431, 85, 0.93, -1.37, 0.35569, 2, 84, 1.98, -1.82, 0.97418, 85, 0.88, -2.37, 0.02582, 1, 84, 1.01, -2.27, 1, 1, 80, 7.13, -5.79, 1], "hull": 21, "edges": [4, 6, 32, 34, 28, 30, 30, 32, 26, 28, 22, 24, 24, 26, 18, 20, 20, 22, 16, 18, 12, 14, 14, 16, 10, 12, 6, 8, 8, 10, 34, 36, 36, 38, 38, 40, 0, 40, 0, 2, 2, 4], "width": 16, "height": 14}}, "jtj1": {"jtj1": {"type": "mesh", "uvs": [0.50631, 0.07544, 0.67385, 0.0682, 0.84491, 0.0608, 0.89934, 0.07768, 0.86946, 0.25058, 0.84085, 0.41611, 0.81352, 0.57429, 0.81398, 0.71908, 0.81442, 0.85335, 0.8148, 0.97324, 0.73539, 0.92009, 0.64292, 0.85819, 0.44441, 0.85816, 0.26191, 0.85813, 0.08905, 0.85811, 0.23754, 0.72427, 0.37458, 0.60075, 0.49619, 0.49113, 0.46676, 0.35497, 0.4352, 0.20903, 0.40725, 0.07973], "triangles": [4, 1, 2, 0, 1, 19, 19, 20, 0, 4, 2, 3, 12, 13, 15, 14, 15, 13, 12, 16, 17, 17, 11, 12, 6, 11, 17, 6, 7, 11, 12, 15, 16, 8, 11, 7, 10, 11, 8, 10, 8, 9, 1, 18, 19, 4, 5, 1, 1, 17, 18, 5, 17, 1, 6, 17, 5], "vertices": [1, 64, 7.86, -5.46, 1, 1, 64, 9.11, -4.91, 1, 1, 64, 10.38, -4.34, 1, 1, 64, 10.83, -4.28, 1, 1, 68, 0.63, 1.26, 1, 2, 68, 1.84, 1.25, 0.99865, 69, -1.91, -1.06, 0.00135, 2, 68, 2.98, 1.24, 0.76183, 69, -1.36, -0.05, 0.23817, 2, 68, 3.99, 1.44, 0.15619, 69, -1.07, 0.93, 0.84381, 2, 68, 4.93, 1.62, 0.00334, 69, -0.79, 1.85, 0.99666, 1, 69, -0.55, 2.66, 1, 1, 69, -0.04, 2.11, 1, 1, 69, 0.55, 1.47, 1, 1, 69, 2.1, 1, 1, 1, 69, 3.51, 0.57, 1, 1, 69, 4.86, 0.16, 1, 1, 69, 3.43, -0.4, 1, 2, 68, 3.84, -2.23, 0.04212, 69, 2.1, -0.92, 0.95788, 2, 68, 2.88, -1.4, 0.64431, 69, 0.93, -1.37, 0.35569, 2, 68, 1.98, -1.82, 0.97418, 69, 0.88, -2.37, 0.02582, 1, 68, 1.01, -2.27, 1, 1, 64, 7.13, -5.79, 1], "hull": 21, "edges": [4, 6, 32, 34, 28, 30, 30, 32, 26, 28, 22, 24, 24, 26, 18, 20, 20, 22, 16, 18, 12, 14, 14, 16, 10, 12, 6, 8, 8, 10, 34, 36, 36, 38, 38, 40, 0, 40, 0, 2, 2, 4], "width": 16, "height": 14}}, "jts1": {"jts1": {"x": 4.63, "y": -1.7, "rotation": 124.05, "width": 13, "height": 14}}, "jtst2": {"jtst": {"type": "mesh", "uvs": [0.50109, 0.11675, 0.54714, 0.21552, 0.60191, 0.33302, 0.68041, 0.25009, 0.80289, 0.1207, 0.93898, 0.1207, 0.98127, 0.28895, 0.9812, 0.41826, 0.98109, 0.5937, 0.92214, 0.69916, 0.8408, 0.8447, 0.68837, 0.83691, 0.49096, 0.82684, 0.32677, 0.81845, 0.24229, 0.7858, 0.09279, 0.75294, 0.01892, 0.70002, 0.01883, 0.5521, 0.01873, 0.39445, 0.01867, 0.28635, 0.16505, 0.11438, 0.25009, 0.01448, 0.34223, 0.01455, 0.45349, 0.01463, 0.68196, 0.36143, 0.60052, 0.43477, 0.54468, 0.55027, 0.37714, 0.61077, 0.34456, 0.6621, 0.22123, 0.69877, 0.37481, 0.36693, 0.13979, 0.48793, 0.305, 0.1836], "triangles": [10, 11, 9, 9, 11, 26, 13, 28, 12, 11, 12, 26, 12, 27, 26, 9, 26, 8, 26, 25, 8, 8, 25, 7, 7, 25, 24, 26, 30, 25, 25, 2, 24, 7, 24, 6, 6, 3, 4, 4, 5, 6, 2, 3, 24, 6, 24, 3, 28, 27, 12, 13, 14, 28, 15, 29, 14, 14, 29, 28, 15, 16, 29, 16, 17, 29, 17, 31, 29, 28, 29, 27, 27, 29, 31, 31, 30, 27, 27, 30, 26, 17, 18, 31, 31, 18, 30, 25, 30, 2, 30, 19, 32, 32, 19, 20, 19, 30, 18, 1, 30, 32, 30, 1, 2, 1, 32, 0, 0, 22, 23, 22, 0, 32, 20, 21, 32, 32, 21, 22], "vertices": [2, 73, 8.17, -12.06, 0.97512, 72, -5.49, 14, 0.02488, 2, 73, 5.14, -10.24, 0.88603, 72, -3.12, 11.38, 0.11397, 2, 73, 1.54, -8.08, 0.45272, 72, -0.3, 8.27, 0.54728, 2, 73, 1.67, -11.54, 0.10882, 72, 0.58, 11.63, 0.89118, 2, 73, 1.87, -16.95, 0.00846, 72, 1.96, 16.86, 0.99154, 1, 72, 5.29, 18.21, 1, 2, 73, -5.34, -15.51, 0.0017, 72, 8.44, 13.39, 0.9983, 2, 73, -8.06, -12.13, 0.00074, 72, 10.06, 9.37, 0.99926, 1, 72, 12.26, 3.91, 1, 1, 72, 12.14, 0.05, 1, 1, 72, 11.97, -5.29, 1, 2, 73, -10.84, 3.66, 0.00509, 72, 8.13, -6.55, 0.99491, 2, 73, -6.56, 6.67, 0.18405, 72, 3.17, -8.19, 0.81595, 2, 73, -3, 9.17, 0.53075, 72, -0.96, -9.55, 0.46925, 2, 73, -0.58, 9.71, 0.72492, 72, -3.44, -9.37, 0.27508, 2, 73, 3.19, 11.33, 0.92811, 72, -7.52, -9.82, 0.07189, 2, 73, 5.82, 11.17, 0.96958, 72, -9.99, -8.91, 0.03042, 2, 73, 8.94, 7.31, 0.99955, 72, -11.85, -4.31, 0.00045, 1, 73, 12.25, 3.19, 1, 1, 73, 14.53, 0.37, 1, 1, 73, 15.13, -6.55, 1, 1, 73, 15.48, -10.57, 1, 2, 73, 13.58, -12.09, 0.99979, 72, -10.66, 15.61, 0.00021, 2, 73, 11.29, -13.94, 0.99743, 72, -7.94, 16.71, 0.00257, 2, 73, -0.7, -8.66, 0.20216, 72, 2.01, 8.18, 0.79784, 2, 73, -0.57, -5.39, 0.32112, 72, 0.94, 5.09, 0.67888, 2, 73, -1.85, -1.45, 0.0707, 72, 1.02, 0.95, 0.9293, 2, 73, 0.33, 2.91, 0.78788, 72, -2.33, -2.59, 0.21212, 2, 73, -0.08, 4.79, 0.70716, 72, -2.48, -4.51, 0.29284, 2, 73, 1.69, 7.79, 0.84938, 72, -5.05, -6.87, 0.15062, 2, 73, 5.5, -3.43, 0.94336, 72, -5.45, 4.97, 0.05664, 1, 73, 7.8, 3.63, 1, 2, 73, 10.8, -7.06, 0.992, 72, -9.46, 9.98, 0.008], "hull": 24, "edges": [8, 10, 10, 12, 26, 28, 28, 30, 30, 32, 4, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 30, 42, 44, 44, 46, 38, 40, 40, 42, 36, 38, 32, 34, 34, 36, 0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26], "width": 52, "height": 66}}, "jtst3": {"jtst": {"type": "mesh", "uvs": [0.50109, 0.11675, 0.54714, 0.21552, 0.60191, 0.33302, 0.68041, 0.25009, 0.80289, 0.1207, 0.93898, 0.1207, 0.98127, 0.28895, 0.9812, 0.41826, 0.98109, 0.5937, 0.92214, 0.69916, 0.8408, 0.8447, 0.68837, 0.83691, 0.49096, 0.82684, 0.32677, 0.81845, 0.24229, 0.7858, 0.09279, 0.75294, 0.01892, 0.70002, 0.01883, 0.5521, 0.01873, 0.39445, 0.01867, 0.28635, 0.16505, 0.11438, 0.25009, 0.01448, 0.34223, 0.01455, 0.45349, 0.01463, 0.68196, 0.36143, 0.60052, 0.43477, 0.54468, 0.55027, 0.37714, 0.61077, 0.34456, 0.6621, 0.22123, 0.69877, 0.37481, 0.36693, 0.13979, 0.48793, 0.305, 0.1836], "triangles": [10, 11, 9, 9, 11, 26, 13, 28, 12, 11, 12, 26, 12, 27, 26, 9, 26, 8, 26, 25, 8, 8, 25, 7, 7, 25, 24, 26, 30, 25, 25, 2, 24, 7, 24, 6, 6, 3, 4, 4, 5, 6, 2, 3, 24, 6, 24, 3, 28, 27, 12, 13, 14, 28, 15, 29, 14, 14, 29, 28, 15, 16, 29, 16, 17, 29, 17, 31, 29, 28, 29, 27, 27, 29, 31, 31, 30, 27, 27, 30, 26, 17, 18, 31, 31, 18, 30, 25, 30, 2, 30, 19, 32, 32, 19, 20, 19, 30, 18, 1, 30, 32, 30, 1, 2, 1, 32, 0, 0, 22, 23, 22, 0, 32, 20, 21, 32, 32, 21, 22], "vertices": [2, 81, 8.17, -12.06, 0.97512, 80, -5.49, 14, 0.02488, 2, 81, 5.14, -10.24, 0.88603, 80, -3.12, 11.38, 0.11397, 2, 81, 1.54, -8.08, 0.45272, 80, -0.3, 8.27, 0.54728, 2, 81, 1.67, -11.54, 0.10882, 80, 0.58, 11.63, 0.89118, 2, 81, 1.87, -16.95, 0.00846, 80, 1.96, 16.86, 0.99154, 1, 80, 5.29, 18.21, 1, 2, 81, -5.34, -15.51, 0.0017, 80, 8.44, 13.39, 0.9983, 2, 81, -8.06, -12.13, 0.00074, 80, 10.06, 9.37, 0.99926, 1, 80, 12.26, 3.91, 1, 1, 80, 12.14, 0.05, 1, 1, 80, 11.97, -5.29, 1, 2, 81, -10.84, 3.66, 0.00509, 80, 8.13, -6.55, 0.99491, 2, 81, -6.56, 6.67, 0.18405, 80, 3.17, -8.19, 0.81595, 2, 81, -3, 9.17, 0.53075, 80, -0.96, -9.55, 0.46925, 2, 81, -0.58, 9.71, 0.72492, 80, -3.44, -9.37, 0.27508, 2, 81, 3.19, 11.33, 0.92811, 80, -7.52, -9.82, 0.07189, 2, 81, 5.82, 11.17, 0.96958, 80, -9.99, -8.91, 0.03042, 2, 81, 8.94, 7.31, 0.99955, 80, -11.85, -4.31, 0.00045, 1, 81, 12.25, 3.19, 1, 1, 81, 14.53, 0.37, 1, 1, 81, 15.13, -6.55, 1, 1, 81, 15.48, -10.57, 1, 2, 81, 13.58, -12.09, 0.99979, 80, -10.66, 15.61, 0.00021, 2, 81, 11.29, -13.94, 0.99743, 80, -7.94, 16.71, 0.00257, 2, 81, -0.7, -8.66, 0.20216, 80, 2.01, 8.18, 0.79784, 2, 81, -0.57, -5.39, 0.32112, 80, 0.94, 5.09, 0.67888, 2, 81, -1.85, -1.45, 0.0707, 80, 1.02, 0.95, 0.9293, 2, 81, 0.33, 2.91, 0.78788, 80, -2.33, -2.59, 0.21212, 2, 81, -0.08, 4.79, 0.70716, 80, -2.48, -4.51, 0.29284, 2, 81, 1.69, 7.79, 0.84938, 80, -5.05, -6.87, 0.15062, 2, 81, 5.5, -3.43, 0.94336, 80, -5.45, 4.97, 0.05664, 1, 81, 7.8, 3.63, 1, 2, 81, 10.8, -7.06, 0.992, 80, -9.46, 9.98, 0.008], "hull": 24, "edges": [8, 10, 10, 12, 26, 28, 28, 30, 30, 32, 4, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 30, 42, 44, 44, 46, 38, 40, 40, 42, 36, 38, 32, 34, 34, 36, 0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26], "width": 52, "height": 66}}, "jtr2s2": {"jtr2s2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 40, 11.08, 2.43, 1, 1, 40, 11.13, -2.97, 1, 1, 40, -2.67, -3.09, 1, 1, 40, -2.72, 2.31, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 9, "height": 23}}, "jtr4s1": {"jtr4s1": {"type": "mesh", "uvs": [0.89046, 0, 0.85111, 0.04978, 0.80667, 0.15, 0.77889, 0.21105, 0.77889, 0.4739, 0.77824, 0.47421, 0.66778, 0.47421, 0.66778, 1, 0.55378, 1, 0, 0.79015, 0, 0.57777, 0.5546, 0.05236, 0.55471, 0.05229, 0.6651, 0], "triangles": [1, 13, 0, 1, 12, 13, 2, 12, 1, 11, 12, 2, 3, 11, 2, 3, 10, 11, 4, 6, 3, 5, 6, 4, 3, 6, 10, 9, 10, 6, 8, 9, 6, 8, 6, 7], "vertices": [1, 45, -0.44, 0.19, 1, 1, 45, -0.2, 0.16, 1, 1, 45, 0.26, 0.17, 1, 1, 45, 0.54, 0.18, 1, 1, 45, 1.69, 0.45, 1, 1, 45, 1.7, 0.45, 1, 1, 45, 1.75, 0.22, 1, 1, 45, 4.05, 0.77, 1, 1, 45, 4.11, 0.53, 1, 1, 45, 3.46, -0.83, 1, 1, 45, 2.53, -1.05, 1, 1, 45, -0.04, -0.45, 1, 1, 45, -0.04, -0.45, 1, 1, 45, -0.32, -0.28, 1], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26], "width": 9, "height": 19}}, "jtr4s2": {"jtr4s2": {"type": "mesh", "uvs": [0.36473, 0, 0.54623, 0.15762, 0.54625, 0.15764, 1, 0.68303, 1, 0.73759, 0.90966, 0.78989, 0.90948, 0.78995, 0.63685, 0.84257, 0.36492, 1, 0.18053, 1, 0.09014, 0.94767, 0.09, 0.94747, 0.09, 0.84209, 0.09, 0.84206, 0.18079, 0.42158, 0.14287, 0.42138, 0.10182, 0.26482, 0.08182, 0.10452, 0.09032, 0.05223, 0.09044, 0.05218, 0.27069, 0], "triangles": [20, 17, 19, 17, 18, 19, 17, 1, 16, 20, 0, 1, 1, 17, 20, 16, 14, 15, 2, 16, 1, 2, 14, 16, 14, 2, 3, 14, 3, 7, 4, 5, 3, 5, 6, 3, 6, 7, 3, 13, 14, 7, 11, 9, 10, 13, 8, 12, 11, 12, 9, 12, 8, 9, 13, 7, 8], "vertices": [1, 46, -0.45, 0.34, 1, 1, 46, 0.35, 0.64, 1, 1, 46, 0.35, 0.64, 1, 1, 46, 2.92, 1.25, 1, 1, 46, 3.16, 1.2, 1, 1, 46, 3.33, 0.91, 1, 1, 46, 3.33, 0.91, 1, 1, 46, 3.4, 0.17, 1, 1, 46, 3.93, -0.68, 1, 1, 46, 3.82, -1.15, 1, 1, 46, 3.54, -1.33, 1, 1, 46, 3.54, -1.33, 1, 1, 46, 3.08, -1.22, 1, 1, 46, 3.08, -1.22, 1, 1, 46, 1.29, -0.56, 1, 1, 46, 1.27, -0.66, 1, 1, 46, 0.55, -0.6, 1, 1, 46, -0.16, -0.49, 1, 1, 46, -0.38, -0.41, 1, 1, 46, -0.38, -0.41, 1, 1, 46, -0.51, 0.1, 1], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 28, 30], "width": 11, "height": 19}}, "rst": {"rst": {"type": "mesh", "uvs": [0.2154, 0.0899, 0.39763, 0.04088, 0.54959, 0, 0.55962, 0, 0.68032, 0.07281, 0.83986, 0.16903, 0.95796, 0.24027, 0.95848, 0.34416, 0.85337, 0.34417, 0.74189, 0.34418, 0.65183, 0.40958, 0.62108, 0.5317, 0.58466, 0.67631, 0.55847, 0.78033, 0.46222, 0.86759, 0.39334, 0.93004, 0.31618, 1, 0.26027, 1, 0.19544, 0.90747, 0.12837, 0.81174, 0.0874, 0.75327, 0.08645, 0.53455, 0.08555, 0.32623, 0.08468, 0.12506], "triangles": [15, 16, 18, 16, 17, 18, 15, 18, 14, 18, 19, 14, 14, 19, 13, 19, 20, 13, 13, 20, 12, 20, 21, 12, 12, 21, 11, 0, 11, 22, 10, 11, 0, 10, 0, 1, 10, 1, 2, 11, 21, 22, 4, 10, 2, 10, 4, 9, 4, 2, 3, 9, 5, 8, 9, 4, 5, 8, 6, 7, 8, 5, 6, 22, 23, 0], "vertices": [1, 98, -0.82, -7.94, 1, 1, 98, -3.36, -5.09, 1, 1, 98, -5.47, -2.72, 1, 1, 98, -5.54, -2.53, 1, 1, 98, -4.55, 0.39, 1, 1, 98, -3.23, 4.26, 1, 1, 98, -2.25, 7.12, 1, 1, 98, 0.39, 8.15, 1, 1, 98, 1.13, 6.23, 1, 1, 98, 1.92, 4.19, 1, 2, 98, 4.22, 3.19, 0.98198, 99, -5.55, 4.35, 0.01802, 3, 98, 7.55, 3.83, 0.67193, 99, -2.18, 4.13, 0.32326, 100, -7.94, 1.46, 0.00481, 3, 98, 11.48, 4.59, 0.03706, 99, 1.82, 3.87, 0.75908, 100, -4.09, 2.58, 0.20386, 2, 99, 4.7, 3.69, 0.29485, 100, -1.33, 3.39, 0.70515, 1, 100, 1.65, 2.77, 1, 1, 100, 3.78, 2.32, 1, 1, 100, 6.16, 1.82, 1, 1, 100, 6.65, 0.84, 1, 3, 98, 20.12, -0.25, 6e-05, 99, 8.96, -2.98, 0.03801, 100, 4.96, -1.42, 0.96193, 3, 98, 18.16, -2.42, 0.02393, 99, 6.52, -4.59, 0.37361, 100, 3.21, -3.77, 0.60246, 3, 98, 16.96, -3.74, 0.07444, 99, 5.03, -5.57, 0.57354, 100, 2.14, -5.2, 0.35203, 3, 98, 11.4, -5.92, 0.66758, 99, -0.9, -6.27, 0.33038, 100, -3.18, -7.88, 0.00203, 2, 98, 6.11, -7.99, 0.99089, 99, -6.54, -6.94, 0.00911, 1, 98, 1, -9.98, 1], "hull": 24, "edges": [4, 6, 12, 14, 18, 20, 32, 34, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18], "width": 23, "height": 32}}, "jtdyl": {"jtdyl": {"type": "mesh", "uvs": [0, 0.0407, 0.07966, 0.06615, 0.19321, 0.12433, 0.31449, 0.19342, 0.43707, 0.28251, 0.53772, 0.36251, 0.60739, 0.42797, 0.52739, 0.30433, 0.46417, 0.15888, 0.4874, 0, 0.60481, 0, 0.77771, 0, 0.92739, 0, 1, 0.06797, 1, 0.23342, 1, 0.46615, 0.86287, 0.46251, 0.91191, 0.54069, 0.89513, 0.68796, 0.96223, 0.76433, 0.96094, 0.8916, 0.88997, 0.90978, 0.83965, 0.87705, 0.84868, 1, 0.63965, 1, 0.6332, 0.86251, 0.58933, 0.67524, 0.53901, 0.64069, 0.52739, 0.53342, 0.46288, 0.47706, 0.38933, 0.42433, 0.28611, 0.35524, 0.17901, 0.28433, 0.08224, 0.23706, 0, 0.19161, 0, 0.11706, 0.08224, 0.15342, 0.19191, 0.20979, 0.30933, 0.27888, 0.40998, 0.34615, 0.50159, 0.42615, 0.57901, 0.48251], "triangles": [34, 36, 33, 33, 36, 37, 34, 35, 36, 35, 1, 36, 36, 1, 2, 35, 0, 1, 32, 37, 38, 32, 33, 37, 37, 2, 3, 37, 36, 2, 29, 30, 39, 39, 4, 40, 30, 31, 39, 31, 38, 39, 31, 32, 38, 39, 38, 4, 38, 3, 4, 38, 37, 3, 16, 26, 6, 27, 41, 26, 26, 41, 6, 27, 28, 41, 29, 40, 28, 28, 40, 41, 40, 5, 41, 41, 5, 6, 40, 29, 39, 40, 4, 5, 16, 14, 15, 6, 11, 16, 14, 11, 12, 14, 16, 11, 6, 7, 10, 6, 10, 11, 7, 8, 10, 10, 8, 9, 12, 13, 14, 24, 22, 23, 24, 25, 22, 20, 21, 19, 19, 21, 22, 22, 18, 19, 22, 25, 18, 25, 26, 18, 26, 16, 18, 18, 16, 17], "vertices": [1, 17, 4.45, -1.68, 1, 2, 16, 7.34, -1.69, 0.00967, 17, 1.93, -1.85, 0.99033, 1, 16, 3.6, -1.77, 1, 2, 15, 5.58, -1.75, 0.69368, 16, -0.46, -1.72, 0.30632, 2, 14, 7.03, -1.61, 0.05299, 15, 1.3, -1.69, 0.94701, 2, 13, 3.05, 5.6, 0.00012, 14, 3.45, -1.61, 0.99988, 2, 13, 1.5, 3.52, 0.30231, 14, 0.86, -1.42, 0.69769, 2, 13, 4.35, 5.85, 0.86296, 14, 4.35, -2.57, 0.13704, 2, 13, 7.65, 7.64, 0.95545, 14, 7.63, -4.4, 0.04455, 2, 13, 11.1, 6.73, 0.98047, 14, 8.72, -7.8, 0.01953, 2, 13, 10.9, 3.1, 0.99222, 14, 5.55, -9.58, 0.00778, 1, 13, 10.62, -2.25, 1, 1, 13, 10.37, -6.89, 1, 1, 13, 8.75, -9.05, 1, 2, 12, 12.97, -9.07, 0.00683, 13, 5.12, -8.86, 0.99317, 2, 12, 7.87, -8.58, 0.06, 13, 0.01, -8.58, 0.94, 2, 12, 8.36, -4.36, 0.37174, 13, 0.31, -4.34, 0.62826, 2, 12, 6.5, -5.71, 0.77561, 13, -1.49, -5.77, 0.22439, 2, 12, 3.33, -4.88, 0.97404, 13, -4.69, -5.08, 0.02596, 2, 12, 1.46, -6.79, 1, 13, -6.48, -7.06, 0, 1, 12, -1.33, -6.48, 1, 1, 12, -1.52, -4.26, 1, 1, 12, -0.65, -2.77, 1, 1, 12, -3.37, -2.79, 1, 1, 12, -2.75, 3.66, 1, 2, 12, 0.28, 3.57, 0.96344, 14, -4.53, 6.52, 0.03656, 2, 12, 4.51, 4.53, 0.45304, 14, -1.33, 3.6, 0.54696, 2, 12, 5.41, 6.01, 0.18546, 14, 0.41, 3.7, 0.81454, 2, 12, 7.8, 6.14, 0.0148, 14, 1.88, 1.82, 0.9852, 2, 14, 4.23, 1.72, 0.93523, 15, -1.32, 1.79, 0.06477, 2, 14, 6.79, 1.83, 0.08403, 15, 1.24, 1.77, 0.91597, 2, 15, 4.78, 1.83, 0.89732, 16, -0.93, 1.92, 0.10268, 2, 15, 8.45, 1.91, 0.00051, 16, 2.73, 1.67, 0.99949, 2, 16, 5.9, 1.79, 0.24204, 17, 0.77, 1.73, 0.75796, 2, 16, 8.64, 1.78, 0, 17, 3.5, 1.5, 1, 1, 17, 3.97, -0.07, 1, 1, 17, 1.3, -0.03, 1, 1, 16, 2.95, 0, 1, 1, 15, 4.88, 0, 1, 1, 15, 1.43, -0.06, 1, 1, 14, 3.73, 0.16, 1, 2, 12, 8.76, 4.44, 0.00019, 14, 1.03, 0.06, 0.99981], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70], "width": 31, "height": 22}}, "jtyingzi2": {"jtyingzi": {"x": -2.36, "y": -16.01, "width": 24, "height": 11}}, "jtyingzi3": {"jtyingzi": {"x": 2.12, "y": -10.21, "width": 24, "height": 11}}, "jtyingzi4": {"jtyingzi": {"x": 0.05, "y": 3.56, "width": 24, "height": 11}}, "jtyingzi5": {"jtyingzi": {"x": -10.22, "y": -3.25, "scaleX": 0.36, "scaleY": 0.38, "rotation": 2.06, "width": 24, "height": 11}}, "jtyingzi6": {"jtyingzi1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.69, -5.59, -16.13, -5.59, -16.13, 4.13, 17.69, 4.13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 9}}, "jtyingzi7": {"jtyingzi1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.69, -5.25, -16.13, -5.25, -16.13, 4.47, 17.69, 4.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 9}}, "jtyingzi8": {"jtyingzi1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [18.09, -5.78, -15.73, -5.78, -15.73, 3.94, 18.09, 3.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 9}}, "nj3": {"nj3": {"type": "mesh", "uvs": [0.65949, 0.07289, 0.78411, 0.10727, 0.87289, 0.20823, 0.97212, 0.32108, 0.92638, 0.47426, 0.90221, 0.55518, 0.88364, 0.61738, 0.7965, 0.67713, 0.73917, 0.71643, 0.6502, 0.77743, 0.55763, 0.84091, 0.391, 0.95515, 0.16626, 0.95573, 0.0635, 0.87113, 0.09506, 0.71335, 0.10886, 0.64434, 0.12717, 0.55277, 0.14241, 0.47656, 0.15983, 0.38949, 0.1869, 0.25414, 0.34805, 0.12328, 0.47334, 0.02153], "triangles": [9, 16, 17, 16, 10, 15, 17, 8, 9, 10, 14, 15, 16, 9, 10, 14, 12, 13, 10, 11, 14, 11, 12, 14, 4, 2, 3, 2, 4, 20, 4, 5, 7, 1, 2, 0, 6, 7, 5, 20, 21, 0, 2, 20, 0, 20, 4, 19, 4, 7, 19, 7, 18, 19, 8, 18, 7, 17, 18, 8], "vertices": [1, 96, -2.44, -0.63, 1, 1, 96, -2.57, 1.38, 1, 2, 96, -1.33, 3.36, 0.99654, 97, -10.09, -0.84, 0.00346, 2, 96, 0.05, 5.57, 0.97297, 97, -9.67, 1.73, 0.02703, 2, 96, 2.97, 6.01, 0.87349, 97, -7.14, 3.27, 0.12651, 2, 96, 4.52, 6.24, 0.7622, 97, -5.81, 4.08, 0.2378, 2, 96, 5.7, 6.42, 0.67547, 97, -4.78, 4.7, 0.32453, 2, 96, 7.25, 5.61, 0.50646, 97, -3.04, 4.55, 0.49354, 2, 96, 8.26, 5.07, 0.34604, 97, -1.9, 4.45, 0.65396, 2, 96, 9.84, 4.24, 0.10837, 97, -0.12, 4.29, 0.89163, 2, 96, 11.48, 3.38, 0.00601, 97, 1.72, 4.13, 0.99399, 1, 97, 5.05, 3.83, 1, 1, 97, 7.49, 1.4, 1, 1, 97, 7.48, -0.83, 1, 2, 96, 11.96, -4.09, 0.0649, 97, 5.05, -2.58, 0.9351, 2, 96, 10.68, -4.39, 0.19904, 97, 3.98, -3.35, 0.80096, 2, 96, 8.98, -4.78, 0.4935, 97, 2.57, -4.37, 0.5065, 2, 96, 7.57, -5.11, 0.74384, 97, 1.4, -5.21, 0.25616, 2, 96, 5.96, -5.48, 0.92173, 97, 0.05, -6.18, 0.07827, 2, 96, 3.45, -6.06, 0.99746, 97, -2.04, -7.68, 0.00254, 1, 96, 0.25, -4.7, 1, 1, 96, -2.25, -3.64, 1], "hull": 22, "edges": [22, 24, 24, 26, 20, 22, 36, 38, 26, 28, 6, 8, 2, 4, 4, 6, 38, 40, 40, 42, 2, 0, 0, 42, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 32, 34, 34, 36, 28, 30, 30, 32], "width": 18, "height": 22}}, "nj4": {"nj4": {"type": "mesh", "uvs": [0.58999, 0.16955, 0.7143, 0.22038, 0.81069, 0.33587, 0.87831, 0.41689, 0.94883, 0.50138, 0.94832, 0.64868, 0.94801, 0.73874, 0.94754, 0.87581, 0.81247, 0.87597, 0.67982, 0.87613, 0.56827, 0.87626, 0.43722, 0.76641, 0.32249, 0.72146, 0.18152, 0.66623, 0.04957, 0.56123, 0.05057, 0.41272, 0.05166, 0.25118, 0.05251, 0.12404, 0.17773, 0.12397, 0.31035, 0.12391, 0.47815, 0.12382], "triangles": [5, 3, 4, 3, 5, 9, 11, 1, 2, 5, 6, 8, 2, 9, 11, 7, 8, 6, 2, 3, 9, 5, 8, 9, 10, 11, 9, 18, 15, 16, 16, 17, 18, 15, 18, 19, 13, 15, 19, 14, 15, 13, 12, 19, 20, 12, 20, 0, 13, 19, 12, 11, 12, 0, 11, 0, 1], "vertices": [2, 90, 4.35, 3.93, 0.93036, 91, -4.03, 2.78, 0.06964, 2, 90, 6.44, 3.53, 0.63523, 91, -2.01, 3.44, 0.36477, 2, 90, 8.21, 2.19, 0.24185, 91, 0.19, 3.13, 0.75815, 2, 90, 9.45, 1.26, 0.06883, 91, 1.73, 2.91, 0.93117, 2, 90, 10.75, 0.28, 0.0111, 91, 3.33, 2.68, 0.9889, 1, 91, 4.54, 1.07, 1, 1, 91, 5.27, 0.09, 1, 1, 91, 6.4, -1.41, 1, 1, 91, 4.65, -2.73, 1, 2, 90, 7.17, -5.4, 0.0002, 91, 2.94, -4.03, 0.9998, 2, 90, 5.38, -5.66, 0.02333, 91, 1.5, -5.12, 0.97667, 2, 90, 3.07, -4.49, 0.29153, 91, -1.1, -5.2, 0.70847, 2, 90, 1.14, -4.15, 0.66473, 91, -2.95, -5.83, 0.33527, 2, 90, -1.23, -3.73, 0.91473, 91, -5.23, -6.61, 0.08527, 2, 90, -3.55, -2.62, 0.99041, 91, -7.8, -6.76, 0.00959, 2, 90, -3.82, -0.61, 0.9998, 91, -9, -5.13, 0.0002, 1, 90, -4.12, 1.57, 1, 1, 90, -4.36, 3.29, 1, 1, 90, -2.35, 3.58, 1, 1, 90, -0.23, 3.89, 1, 2, 90, 2.46, 4.28, 0.99976, 91, -5.85, 2.19, 0.00024], "hull": 21, "edges": [20, 22, 26, 28, 18, 20, 14, 16, 16, 18, 12, 14, 8, 10, 10, 12, 6, 8, 2, 4, 4, 6, 2, 0, 0, 40, 22, 24, 24, 26, 38, 40, 34, 36, 36, 38, 32, 34, 28, 30, 30, 32], "width": 19, "height": 16}}, "jttuzi": {"jttuzi": {"type": "mesh", "uvs": [1, 0.52886, 1, 1, 0.5, 1, 0, 1, 0, 0.52886, 0, 0, 0.5, 0, 1, 0, 0.48247, 0.52186], "triangles": [8, 5, 6, 4, 5, 8, 6, 7, 0, 8, 6, 0, 3, 4, 8, 2, 8, 0, 3, 8, 2, 2, 0, 1], "vertices": [2, 6, 3.96, 2.34, 0.81577, 7, 6.24, -0.88, 0.18423, 1, 6, 4, -1.43, 1, 2, 6, -0.5, -1.48, 0.96977, 7, 1.78, -4.7, 0.03023, 2, 6, -5, -1.53, 0.48865, 7, -2.72, -4.75, 0.51135, 2, 6, -5.04, 2.24, 0.10735, 7, -2.76, -0.99, 0.89265, 1, 7, -2.81, 3.25, 1, 2, 6, -0.59, 6.52, 0.13413, 7, 1.69, 3.3, 0.86587, 2, 6, 3.91, 6.57, 0.47171, 7, 6.19, 3.35, 0.52829, 2, 6, -0.7, 2.34, 0.43115, 7, 1.58, -0.88, 0.56885], "hull": 8, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 6, 8, 8, 10, 2, 0, 0, 14], "width": 9, "height": 8}}}}], "animations": {"ain1": {"slots": {"jtr4t2": {"attachment": [{"name": null}]}, "jtr4s2": {"attachment": [{"name": null}]}, "jttuzi2": {"attachment": [{"name": null}]}, "jtr2s1": {"attachment": [{"name": null}]}, "jtr1": {"attachment": [{"name": null}]}, "jtr4t1": {"attachment": [{"name": null}]}, "jtyingzi4": {"attachment": [{"name": null}]}, "jtt2": {"attachment": [{"name": null}]}, "jttian2": {"attachment": [{"name": "j<PERSON>an"}]}, "jtr4": {"attachment": [{"name": null}]}, "jtr2": {"attachment": [{"name": null}]}, "jts1": {"attachment": [{"name": null}]}, "jtyingzi": {"attachment": [{"name": null}]}, "jtr3": {"attachment": [{"name": null}]}, "jtdyl": {"attachment": [{"name": null}]}, "r8s2": {"attachment": [{"name": "r8s2"}]}, "jtgaozi": {"attachment": [{"name": null}]}, "jtyingzi3": {"attachment": [{"name": null}]}, "jtt1": {"attachment": [{"name": null}]}, "jtyuxian": {"attachment": [{"name": null}]}, "r8s1": {"attachment": [{"name": "r8s1"}]}, "jtr2s2": {"attachment": [{"name": null}]}, "jttuzi": {"attachment": [{"name": null}]}, "jtjian": {"attachment": [{"name": null}]}, "jtr4s1": {"attachment": [{"name": null}]}, "jtr8": {"attachment": [{"name": null}]}, "jttian": {"attachment": [{"name": "j<PERSON>an"}]}}, "bones": {"jtbe28": {"rotate": [{"angle": -13.25, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": -16.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 7.97, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 7.97, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": 7.97, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 7.97, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -3.6, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 6.1667, "angle": -6, "curve": 0.316, "c2": 0.28, "c3": 0.659, "c4": 0.64}, {"time": 6.8667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 7.97, "curve": 0.25, "c3": 0.75}, {"time": 8.2333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": 7.97, "curve": 0.25, "c3": 0.75}, {"time": 9.5667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "angle": -6, "curve": 0.32, "c2": 0.29, "c3": 0.657, "c4": 0.64}, {"time": 10.8667, "angle": -4.44, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 11.0667, "angle": -13.25}]}, "jtbe29": {"rotate": [{"angle": 1.19, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 14.4, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3667, "angle": 1.19, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 14.4, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.7333, "angle": 1.19, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 14.4, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 4.0667, "angle": 1.19, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 4.2, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 14.4, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 5.4, "angle": 1.19}, {"time": 6.1, "angle": 9.59}, {"time": 6.8, "angle": 1.19, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 6.9, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "angle": 14.4, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 8.1333, "angle": 1.19, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 8.2667, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "angle": 14.4, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 9.5333, "angle": 1.19}]}, "jtbe34": {"translate": [{"x": 5.68, "y": -240.69, "curve": "stepped"}, {"time": 5.4, "x": 5.68, "y": -240.69}, {"time": 6.7667, "x": 20.08, "y": -291.27, "curve": "stepped"}, {"time": 9.5333, "x": 20.08, "y": -291.27}, {"time": 11.0333, "x": 5.68, "y": -240.69}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 5.4}, {"time": 6.1333, "y": 0.98}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 9.5333}]}, "jtbe31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.6333, "angle": -9.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": 9.43, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 6.4, "angle": -11.29, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 6.8, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "curve": "stepped"}, {"time": 9.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.8333, "angle": -12.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.2, "angle": 9.43, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 10.6, "angle": -12.49, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 10.9667}]}, "jtbe36": {"rotate": [{"time": 5.3333}, {"time": 5.6333, "angle": 21.6}, {"time": 6, "angle": -4.8}, {"time": 6.4333, "angle": 16.8}, {"time": 6.8667, "angle": -6}, {"time": 9.5333, "curve": "stepped"}, {"time": 9.5667}, {"time": 9.8333, "angle": 20.4}, {"time": 10.2, "angle": -3.6}, {"time": 10.6333, "angle": 15.6}, {"time": 11.0667}]}, "r8s1": {"rotate": [{"angle": 21.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 15.6, "curve": "stepped"}, {"time": 1.2, "angle": 15.6, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 4.8, "curve": "stepped"}, {"time": 5.3333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": 18, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "angle": 16.8, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 11.0667, "angle": 21.6}]}, "r8s3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -7.2, "curve": "stepped"}, {"time": 1.2, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -7.2, "curve": "stepped"}, {"time": 3, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -18, "curve": "stepped"}, {"time": 6.7, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -7.2, "curve": "stepped"}, {"time": 8.1667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "angle": -7.2, "curve": "stepped"}, {"time": 9.7667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 11.0667}]}, "jtbe32": {"rotate": [{"angle": -33.6, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 0.4333, "angle": 24, "curve": "stepped"}, {"time": 1, "angle": 24}, {"time": 1.5667, "angle": -33.6, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 1.8333, "angle": 24, "curve": "stepped"}, {"time": 2.3, "angle": 24}, {"time": 2.9, "angle": -33.6, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 3.2333, "angle": 24, "curve": "stepped"}, {"time": 3.6667, "angle": 24}, {"time": 4.2, "angle": -33.6, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 4.4333, "angle": 24, "curve": "stepped"}, {"time": 4.8667, "angle": 24}, {"time": 5.2667, "angle": -33.6, "curve": 0.234, "c3": 0.609, "c4": 0.42}, {"time": 6.1333, "angle": -30.12, "curve": 0.435, "c2": 0.32, "c3": 0.9, "c4": 0.8}, {"time": 7.6, "angle": 24}, {"time": 8.4667, "angle": -33.6, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 8.7333, "angle": 24, "curve": "stepped"}, {"time": 9.1667, "angle": 24}, {"time": 9.5333, "angle": -33.6}], "translate": [{"x": -0.88, "y": -1.22, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 0.4333, "x": 1.66, "y": 2.7}, {"time": 1, "x": 1.69, "y": 3.75}, {"time": 1.5667, "x": -0.88, "y": -1.22, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 1.8333, "x": 1.66, "y": 2.7}, {"time": 2.3, "x": 1.69, "y": 3.75}, {"time": 2.9, "x": -0.88, "y": -1.22, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 3.2333, "x": 1.66, "y": 2.7}, {"time": 3.6667, "x": 1.69, "y": 3.75}, {"time": 4.2, "x": -0.88, "y": -1.22, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 4.4333, "x": 1.66, "y": 2.7}, {"time": 4.8667, "x": 1.69, "y": 3.75}, {"time": 5.2667, "x": -0.88, "y": -1.22, "curve": 0.244, "c3": 0.605, "c4": 0.4}, {"time": 5.6333, "x": -1.56, "y": -0.08, "curve": 0.33, "c2": 0.27, "c3": 0.69, "c4": 0.64}, {"time": 6.1333, "x": -1.7, "y": 0.16, "curve": 0.485, "c2": 0.4, "c3": 0.877, "c4": 0.78}, {"time": 6.6667, "x": -0.88, "y": -1.22, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 7.3, "x": 1.66, "y": 2.7}, {"time": 7.6, "x": 1.69, "y": 3.75}, {"time": 8.4667, "x": -0.88, "y": -1.22, "curve": 0.25, "c3": 0.904, "c4": 0.76}, {"time": 8.7333, "x": 1.66, "y": 2.7}, {"time": 9.1667, "x": 1.69, "y": 3.75}, {"time": 9.5333, "x": -0.88, "y": -1.22, "curve": 0.242, "c3": 0.605, "c4": 0.4}, {"time": 10, "x": -1.44, "y": -0.28, "curve": 0.413, "c2": 0.29, "c3": 0.902, "c4": 0.8}, {"time": 11.0667, "x": -0.88, "y": -1.22}]}}, "deform": {"default": {"jttian2": {"jttian": [{"vertices": [-7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911]}]}, "jttian": {"jttian": [{"vertices": [-7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911]}]}}}}, "ain2": {"slots": {"jtr4t2": {"attachment": [{"name": null}]}, "jtr4s1": {"attachment": [{"name": null}]}, "jtjin": {"attachment": [{"name": "jtjin"}]}, "jtr4s2": {"attachment": [{"name": null}]}, "jttuzi2": {"attachment": [{"name": null}]}, "jtr2s1": {"attachment": [{"name": null}]}, "jtr1": {"attachment": [{"name": null}]}, "jtr4t1": {"attachment": [{"name": null}]}, "jtyingzi4": {"attachment": [{"name": null}]}, "jtt2": {"attachment": [{"name": null}]}, "jtchutou": {"attachment": [{"name": null}]}, "jtr4": {"attachment": [{"name": null}]}, "jtr2": {"attachment": [{"name": null}]}, "jts1": {"attachment": [{"name": null}]}, "jtyingzi": {"attachment": [{"name": null}]}, "jtr3": {"attachment": [{"name": null}]}, "jtdyl": {"attachment": [{"name": null}]}, "jtr9": {"attachment": [{"name": null}]}, "jtgaozi": {"attachment": [{"name": null}]}, "jtyingzi3": {"attachment": [{"name": null}]}, "jtt1": {"attachment": [{"name": null}]}, "jtyuxian": {"attachment": [{"name": null}]}, "jtyingzi5": {"attachment": [{"name": null}]}, "jtr2s2": {"attachment": [{"name": null}]}, "jttuzi": {"attachment": [{"name": null}]}, "jtjian": {"attachment": [{"name": null}]}, "jtyingzi2": {"attachment": [{"name": null}]}, "jtr8": {"attachment": [{"name": null}]}}, "bones": {"jtroot": {"scale": [{"x": 1.098, "y": 1.098}]}, "jtbe28": {"rotate": [{"angle": -13.25}]}, "jtbe32": {"rotate": [{"angle": -13.77}], "translate": [{"x": -0.68, "y": 0.59}]}, "jtbe29": {"rotate": [{"angle": 1.19}]}, "jtbe34": {"translate": [{"x": 5.68, "y": -240.69}]}}, "deform": {"default": {"jttian2": {"jttian": [{"vertices": [-7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911]}]}, "jttian": {"jttian": [{"vertices": [-7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911]}]}}}}, "ain3": {"slots": {"jtyingzi2": {"attachment": [{"name": null}]}, "jttuzi2": {"attachment": [{"name": null}]}, "jtr1": {"attachment": [{"name": null}]}, "jtyingzi4": {"attachment": [{"name": null}]}, "jtchutou": {"attachment": [{"name": null}]}, "jts1": {"attachment": [{"name": null}]}, "jtr3": {"attachment": [{"name": null}]}, "jtdyl": {"attachment": [{"name": null}]}, "jtr9": {"attachment": [{"name": null}]}, "jtgaozi": {"attachment": [{"name": null}]}, "jtyingzi3": {"attachment": [{"name": null}]}, "jtt1": {"attachment": [{"name": null}]}, "jtyuxian": {"attachment": [{"name": null}]}, "jttuzi": {"attachment": [{"name": null}]}, "jtjian": {"attachment": [{"name": null}]}, "jtr8": {"attachment": [{"name": null}]}}, "bones": {"jtr4": {"rotate": [{"angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -25.59, "curve": "stepped"}, {"time": 3.3333, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -14.79, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 13.5667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 15.1667, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 17.9333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 18.7333, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 19.5333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 21.1, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 21.9, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 22.7, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 23.5, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 25.0667, "angle": -6.43, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": -25.59}]}, "jtr3": {"rotate": [{"angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 3.42, "curve": "stepped"}, {"time": 3.3333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "angle": 3.42, "curve": "stepped"}, {"time": 12, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 13.5667, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 15.1667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 17.9333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 18.7333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 19.5333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 21.1, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 21.9, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 22.7, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 23.5, "angle": 3.42}]}, "jtr2s1": {"rotate": [{"angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 12.91, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -7.94, "curve": "stepped"}, {"time": 3.3333, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 5.71, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -7.94, "curve": "stepped"}, {"time": 12, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 13.5667, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 15.1667, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 18.7333, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 21.9, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 23.5, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 25.0667, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": -7.94}]}, "jtr2s2": {"rotate": [{"angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -11.54, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 6.91, "curve": "stepped"}, {"time": 3.3333, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -33.14, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 18.91, "curve": 0.25, "c3": 0.75}, {"time": 11.2, "angle": -41.09, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 13.5667, "angle": -33.14, "curve": 0.25, "c3": 0.75}, {"time": 15.1667, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 18.7333, "angle": -25.94, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "angle": 12.91, "curve": 0.25, "c3": 0.75}, {"time": 21.9, "angle": -17.54, "curve": 0.25, "c3": 0.75}, {"time": 23.5, "angle": 16.51, "curve": 0.25, "c3": 0.75}, {"time": 25.0667, "angle": -58.34, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": 6.91}]}, "jtr2": {"rotate": [{"angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 1.2, "curve": "stepped"}, {"time": 3.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 1.2, "curve": "stepped"}, {"time": 12, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 13.5667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 15.1667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 18.7333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 21.9, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 23.5, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 25.0667, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": 1.2}], "scale": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5667, "y": 0.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "y": -1, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.9, "y": -0.94, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 6.4667, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 9.6667, "y": 0.96, "curve": 0.372, "c2": 0.49, "c3": 0.752}, {"time": 12, "curve": 0.25, "c3": 0.75}, {"time": 12.0333, "y": -1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 13.5667, "y": -0.963, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 15.1333, "y": -1.005, "curve": 0.25, "c3": 0.75}, {"time": 15.1667}, {"time": 18.7333, "y": 0.98}, {"time": 20.3333}, {"time": 21.9, "y": 0.96}, {"time": 23.5}, {"time": 25.0667, "y": 0.98}, {"time": 26.6667}], "shear": [{"curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 1.5667, "x": 2.4, "curve": 0.314, "c2": 0.27, "c3": 0.656, "c4": 0.63}, {"time": 3.1667, "curve": 0.341, "c2": 0.36, "c3": 0.683, "c4": 0.72}, {"time": 4.7333, "x": -2.4, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 6.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.2, "x": 0.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.6667, "x": -3.6, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 11.2333, "x": 0.72, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 12, "curve": 0.25, "c3": 0.75}, {"time": 13.5667, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 15.1667, "curve": 0.25, "c3": 0.75}, {"time": 18.7333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "curve": 0.25, "c3": 0.75}, {"time": 21.9, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 23.5, "curve": 0.25, "c3": 0.75}, {"time": 24.2667, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 25.0667, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 25.8667, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 26.6667}]}, "jtr8": {"rotate": [{"angle": -4.63, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 0.4667, "angle": 2.03, "curve": "stepped"}, {"time": 6.9667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "angle": 2.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.9667, "angle": -3.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.3333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 11.7333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": 2.03, "curve": "stepped"}, {"time": 13.4667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 15.0333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 16.6333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": -2.38, "curve": 0.25, "c3": 0.75}, {"time": 19.2, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 20, "angle": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 20.4, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 21.3667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 22.3333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 23.1667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 23.9667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 24.7667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 25.5333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 26.3333, "angle": -8.89, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 26.6667, "angle": -4.63}]}, "jtr9": {"rotate": [{"angle": -2.22, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 0.4667, "angle": -8.89, "curve": "stepped"}, {"time": 6.9667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "angle": -8.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.9667, "angle": -3.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.3333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "angle": -8.89}, {"time": 10.9333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 11.7333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": -8.89, "curve": "stepped"}, {"time": 13.4667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 15.0333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 16.6333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": -13.3, "curve": 0.25, "c3": 0.75}, {"time": 19.2, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 20, "angle": -12.29, "curve": 0.25, "c3": 0.75}, {"time": 20.4, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 21.3667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 22.3333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 23.1667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 23.9667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 24.7667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 25.5333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 26.3333, "angle": 2.03, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 26.6667, "angle": -2.22}]}, "jtr4s1": {"rotate": [{"angle": -5.06, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.4667, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -123.53, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -105.53, "curve": 0.25, "c3": 0.75}, {"time": 5.5667, "angle": -123.53, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 6.9333, "angle": -11.48, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 6.9667, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "angle": 9.77, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.9667, "angle": 6.21, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 10.1333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 11.7333, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": -9.53, "curve": "stepped"}, {"time": 13.4667, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 15.0333, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 16.6333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 19.2, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 20, "angle": -25.27, "curve": 0.25, "c3": 0.75}, {"time": 20.8, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 22.3667, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 23.9667, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 25.5333, "angle": 9.77, "curve": 0.243, "c3": 0.657, "c4": 0.63}, {"time": 26.6667, "angle": -5.06}]}, "jtr4s2": {"rotate": [{"angle": 5.3, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.4667, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 26.57, "curve": 0.488, "c2": 0.37, "c3": 0.976, "c4": 0.75}, {"time": 6.9333, "angle": 9.92, "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 6.9667, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "angle": -9.53, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.9667, "angle": -5.97, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 10.1333, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 11.7333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": 9.77, "curve": "stepped"}, {"time": 13.4667, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 15.0333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 16.6333, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 18.5333, "angle": -2.52, "curve": 0.25, "c3": 0.75}, {"time": 19.2, "angle": -49.03, "curve": 0.25, "c3": 0.75}, {"time": 20, "angle": 1.18, "curve": 0.25, "c3": 0.75}, {"time": 20.8, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 22.3667, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 23.9667, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 25.5333, "angle": -9.53, "curve": 0.243, "c3": 0.657, "c4": 0.63}, {"time": 26.6667, "angle": 5.3}]}, "jtr6": {"rotate": [{"angle": 0.09, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.4667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": -3.6, "curve": 0, "c2": 0.25, "c3": 0.952, "c4": 0.74}, {"time": 6.9333, "angle": 1.16, "curve": 0.342, "c2": 0.66, "c3": 0.675}, {"time": 6.9667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "angle": -3.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8.9667, "angle": -2.71, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 10.1333, "angle": 1.2}, {"time": 11.7333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": 1.2, "curve": "stepped"}, {"time": 12.6667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 15.0333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 16.6333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 17.6667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 19.2, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 20, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 20.8, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 22.3667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 23.9667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 25.5333, "angle": -3.6, "curve": 0.243, "c3": 0.657, "c4": 0.63}, {"time": 26.6667, "angle": 0.09}], "scale": [{"y": 0.99, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.4667, "y": 0.98, "curve": 0.344, "c2": 0.37, "c3": 0.688, "c4": 0.73}, {"time": 3.2333, "y": 1.013, "curve": 0, "c2": 0.25, "c3": 0.95, "c4": 0.74}, {"time": 6.9333, "curve": "stepped"}, {"time": 6.9667, "curve": 0.25, "c3": 0.75}, {"time": 7, "y": -1, "curve": 0.25, "c3": 0.624, "c4": 0.5}, {"time": 8.5667, "y": -0.98, "curve": 0.336, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 8.9667, "y": -0.987, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 10.1, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "y": 0.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12.5, "curve": 0.25, "c3": 0.75}, {"time": 12.5333, "y": -1, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 14.4333, "y": -0.96, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 16.6333, "y": -1, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 19.2, "y": -0.96, "curve": 0.372, "c2": 0.49, "c3": 0.752}, {"time": 20.8, "y": -1, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 22.3667, "y": -0.96, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 23.9333, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 23.9667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 25.5333, "y": 1.013, "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 26.6667, "y": 0.99}]}, "jtr7": {"rotate": [{"angle": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.736}, {"time": 0.4667, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": -4.8, "curve": 0.248, "c3": 0.733, "c4": 0.92}, {"time": 6.9333, "angle": 1.71, "curve": 0.346, "c2": 0.66, "c3": 0.68}, {"time": 6.9667, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "angle": -4.8, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 8.9667, "angle": -4.43, "curve": 0.295, "c2": 0.19, "c3": 0.755}, {"time": 10.1333, "angle": 1.8, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 10.3667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 13.3, "angle": 1.8, "curve": "stepped"}, {"time": 13.4667, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 15.2667, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 16.6333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 17.6, "angle": 4.21, "curve": 0.25, "c3": 0.75}, {"time": 19.2, "angle": -7.8, "curve": 0.25, "c3": 0.75}, {"time": 20, "angle": 5.16, "curve": 0.25, "c3": 0.75}, {"time": 20.8, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 21.0333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 22.6333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 23.9667, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 24.2, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 25.7667, "angle": -4.8, "curve": 0.243, "c3": 0.647, "c4": 0.59}, {"time": 26.6667, "angle": -0.14}]}, "jtr5": {"rotate": [{"angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 2.19, "curve": "stepped"}, {"time": 3.3333, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "angle": 8.19, "curve": "stepped"}, {"time": 10.0667, "angle": 8.19, "curve": 0.25, "c3": 0.75}, {"time": 11.2, "angle": -2.21, "curve": 0.25, "c3": 0.75}, {"time": 11.6, "angle": 7.19, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 12.1, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 15.1667, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 15.2667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 18.8667, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 22.0333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 23.5, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 23.6, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 25.2, "angle": 14.4, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": 2.19}]}, "jtr4t1": {"rotate": [{"angle": 3.6, "curve": "stepped"}, {"time": 0.0333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 3.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.0333, "angle": -1.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.4, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": 3.6}, {"time": 4, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.5667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": 3.6, "curve": "stepped"}, {"time": 6.5333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 3.6, "curve": "stepped"}, {"time": 11.9, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 12.7667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 14.5667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 15.4333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 16.3, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 17.1667, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 18.0667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 18.9333, "angle": 3.6}], "translate": [{"x": 0.05, "y": 0.54, "curve": "stepped"}, {"time": 0.0333, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.0333, "x": 0.02, "y": 0.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.4, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": 0.05, "y": 0.54}, {"time": 4, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 5.5667, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "x": 0.05, "y": 0.54, "curve": "stepped"}, {"time": 6.5333, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "x": 0.05, "y": 0.54, "curve": "stepped"}, {"time": 11.9, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 12.7667, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 14.5667, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 15.4333, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 16.3, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 17.1667, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 18.0667, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 18.9333, "x": 0.05, "y": 0.54}]}, "jtr4t2": {"rotate": [{"angle": -3.6, "curve": "stepped"}, {"time": 0.0333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -3.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.0333, "angle": 0.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.4, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -3.6}, {"time": 4, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.5667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -3.6, "curve": "stepped"}, {"time": 6.5333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 10.8, "curve": "stepped"}, {"time": 11.9, "angle": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 12.7667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 14.5667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 15.4333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 16.3, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 17.1667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 18.0667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 18.9333, "angle": -3.6}], "translate": [{"x": 0.05, "y": -0.26, "curve": "stepped"}, {"time": 0.0333, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.0333, "x": 0.02, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.4, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": 0.05, "y": -0.26}, {"time": 4, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 5.5667, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "x": 0.05, "y": -0.26, "curve": "stepped"}, {"time": 6.5333, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "x": 0.07, "y": 0.32, "curve": "stepped"}, {"time": 11.9, "x": 0.07, "y": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 12.7667, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 13.7, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 14.5667, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 15.4333, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 16.3, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 17.1667, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 18.0667, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 18.9333, "x": 0.05, "y": -0.26}]}, "jtr10": {"rotate": [{"time": 6.5}, {"time": 6.5667, "angle": 10.8}, {"time": 8.3, "angle": 4.8}, {"time": 9.5, "angle": 12, "curve": "stepped"}, {"time": 15.7333, "angle": 12}, {"time": 15.8667, "angle": 2.4, "curve": "stepped"}, {"time": 19.0333, "angle": 2.4}, {"time": 19.1333, "angle": 10.8}, {"time": 22.0667, "angle": 11.08}, {"time": 22.1667}], "scale": [{"y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 0.912, "curve": "stepped"}, {"time": 3.3333, "y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "y": 0.912, "curve": "stepped"}, {"time": 9.5, "y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 11.2, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 12, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "y": 0.912, "curve": "stepped"}, {"time": 12.7, "y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 12.8, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 13.6, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 14.4, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 15.2, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 15.8667, "y": 0.912, "curve": "stepped"}, {"time": 19.0333, "y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 19.1333, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 19.9333, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 20.7333, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 21.5333, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 22.1667, "y": 0.912}]}, "jtr1": {"rotate": [{"angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -1.2, "curve": "stepped"}, {"time": 3.3333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "curve": 0.25, "c3": 0.75}, {"time": 11.0667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 11.8667, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 13.4333, "curve": 0.25, "c3": 0.75}, {"time": 14.2333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 15.0333, "curve": 0.25, "c3": 0.75}, {"time": 15.8333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 16.8667, "curve": 0.25, "c3": 0.75}, {"time": 17.6333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 18.4333, "curve": 0.25, "c3": 0.75}, {"time": 19, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 19.7667, "curve": 0.25, "c3": 0.75}, {"time": 20.5667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 21.3667, "curve": 0.25, "c3": 0.75}, {"time": 22.1667, "angle": -1.2}], "scale": [{"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "y": -1, "curve": "stepped"}, {"time": 15.7333, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 15.8333, "curve": "stepped"}, {"time": 19, "curve": 0.25, "c3": 0.75}, {"time": 19.1333, "y": -1, "curve": "stepped"}, {"time": 22.0667, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 22.1667}]}, "jtr11": {"rotate": [{"angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -1.74, "curve": "stepped"}, {"time": 3.3333, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": -8.94, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "angle": -10.14, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 12.0667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 12.8667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 13.6333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 14.4333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 15.2333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 15.8333, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 16.8, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 17.6, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 18.4, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 19, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 19.2, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 19.9667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 20.7667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 21.5667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 22.1667, "angle": -1.74}]}, "jtbe5": {"translate": [{"x": -15.22, "y": -16.81, "curve": 0.368, "c2": 0.63, "c3": 0.705}, {"time": 0.4667, "x": -18.58, "y": -15.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2667, "x": -18.54, "y": -14, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 6.9333, "x": -18.58, "y": -15.03, "curve": 0.337, "c2": 0.66, "c3": 0.671}, {"time": 6.9667, "x": -18.58, "y": -15.04, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.9667, "x": 10.92, "y": -16.1, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 10.1333, "x": 25.84, "y": -16.64}, {"time": 11.4333, "x": 4.73, "y": -8.36}, {"time": 12.5, "x": -12.42, "y": 1.48, "curve": "stepped"}, {"time": 12.6667, "x": -12.42, "y": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 13.5, "x": -11.59, "y": 6.95, "curve": 0.313, "c2": 0.27, "c3": 0.682, "c4": 0.72}, {"time": 15.1667, "x": 6.03, "y": 19.83, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 16.0667, "x": 11.56, "y": 14.67, "curve": 0.251, "c3": 0.624, "c4": 0.5}, {"time": 17.8333, "x": 11.53, "y": 13.64, "curve": 0.374, "c2": 0.5, "c3": 0.751}, {"time": 19.6667, "x": 11.56, "y": 14.67, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 20.8, "x": 14.35, "y": 5.56, "curve": 0.318, "c2": 0.28, "c3": 0.69, "c4": 0.74}, {"time": 22.9667, "x": 23.63, "y": -8.28, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 23.9667, "x": 25.84, "y": -16.64, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 24.7667, "x": 17.72, "y": -14.15, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 26.3333, "x": -10.46, "y": -19.32, "curve": 0.348, "c2": 0.4, "c3": 0.684, "c4": 0.74}, {"time": 26.6667, "x": -15.22, "y": -16.81}]}, "jtbe7": {"translate": [{"x": 11.98}, {"time": 1.0667, "x": 12.81, "y": 4.23}, {"time": 2.3, "x": 2.83, "y": 4.02}, {"time": 3.1667, "x": -1.47, "y": 7.71, "curve": "stepped"}, {"time": 3.3333, "x": -1.47, "y": 7.71}, {"time": 4.4, "x": 7.47, "y": 7.7}, {"time": 5.4333, "x": 20.41, "y": 13.79}, {"time": 6.4333, "x": 28.9, "y": 14.24, "curve": "stepped"}, {"time": 12, "x": 28.9, "y": 14.24}, {"time": 12.9667, "x": 38.32, "y": 25.05}, {"time": 14.3, "x": 48.76, "y": 29.63, "curve": "stepped"}, {"time": 16.8333, "x": 48.76, "y": 29.63}, {"time": 18.1333, "x": 50.87, "y": 23.36}, {"time": 19.4, "x": 41.39, "y": 20.33}, {"time": 20.3333, "x": 34.52, "y": 13.23}, {"time": 21.1, "x": 29.08, "y": 13.16}, {"time": 22.3, "x": 25.5, "y": 11.23}, {"time": 23.5, "x": 16.34, "y": 9.74}, {"time": 25.0667, "x": 11.78, "y": 1.4}, {"time": 26.6667, "x": 11.98}]}, "jtbe3": {"translate": [{"x": -102.17, "y": 24.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5667, "x": -84.43, "y": 20.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "x": -66.69, "y": 14.19, "curve": "stepped"}, {"time": 3.3333, "x": -66.69, "y": 14.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.1333, "x": -39.36, "y": 20.53, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 6.5333, "x": -20.34, "y": 27.43, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -20.34, "y": 23.43, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "x": -20.34, "y": 27.43, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 9.7, "x": -29.8, "y": 24.05, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 10.7667, "x": -40.28, "y": 18.02, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "x": -64.67, "y": 17.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 14.2333, "x": -71.97, "y": 22.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 15.8333, "x": -79.28, "y": 30.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 17.4, "x": -78.74, "y": 30.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 19, "x": -79.28, "y": 30.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 20.5667, "x": -90.72, "y": 28.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 22.1667, "x": -102.17, "y": 24.6}]}}, "deform": {"default": {"jtt2": {"jtt2": [{"vertices": [60.11194, 12.061, 1.71193, 12.061, 1.71193, 43.26099, 60.11194, 43.26099]}]}, "jtt1": {"jtt1": [{"vertices": [-6.068, -9.4, -34.86804, -9.4, -34.86804, 9.39999, -6.068, 9.39999]}]}}}, "drawOrder": [{"time": 11.4667, "offsets": [{"slot": "jtr4s1", "offset": -14}]}]}, "ain4": {"slots": {"jtr4t2": {"attachment": [{"name": null}]}, "jtyanwu0002": {"attachment": [{"name": "jtyanwu0002"}, {"time": 0.0667, "name": null}]}, "jtshit": {"attachment": [{"name": "jtshit"}]}, "jtr7s2": {"attachment": [{"name": "jtr7s2"}]}, "jtyingzi2": {"attachment": [{"name": null}]}, "jtr4s2": {"attachment": [{"name": null}]}, "jttuzi2": {"attachment": [{"name": null}]}, "jtr2s1": {"attachment": [{"name": null}]}, "jtr1": {"attachment": [{"name": null}]}, "jtr4t1": {"attachment": [{"name": null}]}, "jtshit2": {"attachment": [{"name": "jtshit2"}]}, "jtyingzi4": {"attachment": [{"name": null}]}, "jtt2": {"attachment": [{"name": null}]}, "jtchutou": {"attachment": [{"name": null}]}, "jtr4": {"attachment": [{"name": null}]}, "jtr2": {"attachment": [{"name": null}]}, "jtyingzi": {"attachment": [{"name": null}]}, "jtr3": {"attachment": [{"name": null}]}, "jtdyl": {"attachment": [{"name": null}]}, "jtr9": {"attachment": [{"name": null}]}, "jtyanwu0001": {"attachment": [{"time": 1.1333, "name": "jtyanwu0001"}]}, "jtt1": {"attachment": [{"name": null}]}, "jtyanwu0003": {"attachment": [{"time": 0.0667, "name": "jtyanwu0003"}, {"time": 0.1333, "name": null}]}, "jtyuxian": {"attachment": [{"name": null}]}, "jtyingzi5": {"attachment": [{"name": null}]}, "jtr2s2": {"attachment": [{"name": null}]}, "jttuzi": {"attachment": [{"name": null}]}, "jtjian": {"attachment": [{"name": null}]}, "jtyanwu0004": {"attachment": [{"time": 0.1333, "name": "jtyanwu0004"}, {"time": 0.2, "name": null}]}, "jtr7t2": {"attachment": [{"name": "jtr7t2"}]}, "jtr4s1": {"attachment": [{"name": null}]}, "jtr7t1": {"attachment": [{"name": "jtr7t1"}]}}, "bones": {"jtbe26": {"rotate": [{"angle": 7.89, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": 13.45, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.1667, "angle": 7.89}], "translate": [{"x": 0.61, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 2, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.1667, "x": 0.61}]}, "jtbe15": {"rotate": [{"angle": 13.45, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5333, "angle": -3.68, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.9, "angle": -28.68, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.0667, "angle": 11.13, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 1.1667, "angle": 13.45}], "translate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.13, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}], "scale": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "jtbe33": {"rotate": [{"angle": 19.96, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 0.2, "angle": 8.81, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8667, "angle": -11.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.9667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 1.1667, "angle": 19.96}], "translate": [{"x": -0.28, "y": 1.44, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 0.2, "x": -0.13, "y": 1.84, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3, "x": -0.07, "y": 2, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.78, "y": 0.13, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.1667, "x": -0.28, "y": 1.44}]}, "jts1": {"rotate": [{"curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 0.3333, "angle": -24.81, "curve": 0.26, "c2": 0.42, "c3": 0.746}, {"time": 0.9333, "angle": -115.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "jtbe25": {"rotate": [{"angle": -0.5, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1667, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -25.2, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.1667, "angle": -0.5}]}, "jtbe27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 19.33, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -12, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0333, "angle": -10.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667}], "translate": [{}, {"time": 0.3667, "x": 1.39, "y": 0.35}, {"time": 0.7}], "scale": [{"curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.3667, "y": 1.15, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.7, "y": 1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "jtbe35": {"rotate": [{"angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 15.6, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.4}], "translate": [{"x": 8.69, "y": -144.42, "curve": "stepped"}, {"time": 0.2667, "x": 8.69, "y": -144.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 13.59, "y": -144.42, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 8.69, "y": -144.42}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -2.4, "y": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "jtr7s2": {"rotate": [{"angle": -15.6, "curve": 0.294, "c3": 0.632, "c4": 0.37}, {"time": 0.4667, "angle": -37.99, "curve": 0.314, "c2": 0.25, "c3": 0.65, "c4": 0.6}, {"time": 0.9333, "angle": -76.8, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -15.6}]}, "jtbe4": {"rotate": [{"angle": -24, "curve": "stepped"}, {"time": 0.2, "angle": -24}, {"time": 1.1333, "angle": 7.2}], "translate": [{"x": 37.87, "y": -127.82, "curve": "stepped"}, {"time": 0.2, "x": 37.87, "y": -127.82}, {"time": 0.3667, "x": 37.06, "y": -130.73}, {"time": 1.1333, "x": 33.37, "y": -136.37}], "scale": [{}, {"time": 1.1333, "x": 1.24, "y": 1.2}]}, "jtr7t1": {"rotate": [{"angle": 14.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 44.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 14.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 15.6, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 14.4}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 3.31, "y": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.15, "y": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "jtr7t2": {"rotate": [{"curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": 7.53, "curve": 0.319, "c2": 0.29, "c3": 0.666, "c4": 0.66}, {"time": 0.5333, "angle": 29.27, "curve": 0.347, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 0.8667, "angle": -3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667}], "translate": [{"curve": 0.243, "c3": 0.653, "c4": 0.62}, {"time": 0.3667, "x": 1.39, "y": -0.28, "curve": 0.348, "c2": 0.39, "c3": 0.684, "c4": 0.73}, {"time": 0.7, "x": -1.85, "y": 1.05, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 1.1667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.08, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 1.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": 1.1, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.8, "x": 0.996, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.1667}]}, "jtshit": {"scale": [{"y": 0.914, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "x": 1.32, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.88, "y": 1.064, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 1.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "y": 0.914}]}, "jtroot2": {"translate": [{"x": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -2.7}], "scale": [{"x": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.24}]}}, "deform": {"default": {"jtshit2": {"jtshit2": [{"vertices": [29.46572, 14.72533, 31.74143, 9.68207, 31.7532, 8.79864, 30.78397, 7.66308, 26.69604, 6.82055, 26.43911, 6.81715, 26.29378, 17.73615, 26.9817, 17.68795]}]}, "jtr7t1": {"jtr7t1": [{"vertices": [0.24491, -0.07268, 0.50194, -0.12442, 0.49103, -0.26443, 0.47405, -0.48221, 0.6153, -0.71219, 1.14104, -0.95984, 0.2688, -1.95553, -0.2911, -0.8963, -0.8747, -0.80517, -0.94998, -0.65534, 0.01089, -0.5675, 0.02369, -0.24644, 0.09865, -0.04325]}]}}}, "drawOrder": [{"offsets": [{"slot": "jtshit2", "offset": -33}, {"slot": "jtshit", "offset": -33}]}]}, "ain5": {"slots": {"jtr4t2": {"attachment": [{"name": null}]}, "jtj6": {"attachment": [{"name": "jtj1"}]}, "jtyingzi8": {"attachment": [{"name": "jtyingzi1"}]}, "jtt1": {"attachment": [{"name": null}]}, "jtr4s2": {"attachment": [{"name": null}]}, "jttuzi2": {"attachment": [{"name": null}]}, "jtr2s1": {"attachment": [{"name": null}]}, "jtr1": {"attachment": [{"name": null}]}, "jtj1": {"attachment": [{"name": "jtj1"}]}, "jtyingzi4": {"attachment": [{"name": null}]}, "jtj4": {"attachment": [{"name": "jtj1"}]}, "jtst": {"attachment": [{"name": "jtst"}]}, "jtyingzi6": {"attachment": [{"name": "jtyingzi1"}]}, "jtr4": {"attachment": [{"name": null}]}, "jtr2": {"attachment": [{"name": null}]}, "jts1": {"attachment": [{"name": null}]}, "jtyingzi": {"attachment": [{"name": null}]}, "jtr3": {"attachment": [{"name": null}]}, "jtj5": {"attachment": [{"name": "jtj2"}]}, "jtr4t1": {"attachment": [{"name": null}]}, "jtj3": {"attachment": [{"name": "jtj2"}]}, "jtdyl": {"attachment": [{"name": null}]}, "jtr9": {"attachment": [{"name": null}]}, "jtst2": {"attachment": [{"name": "jtst"}]}, "jtgaozi": {"attachment": [{"name": null}]}, "jtj2": {"attachment": [{"name": "jtj2"}]}, "jtyingzi3": {"attachment": [{"name": null}]}, "jtfangz": {"attachment": [{"name": "jtfangz"}]}, "jtyingzi7": {"attachment": [{"name": "jtyingzi1"}]}, "jtst3": {"attachment": [{"name": "jtst"}]}, "jtyuxian": {"attachment": [{"name": null}]}, "jtyingzi2": {"attachment": [{"name": null}]}, "jtchutou": {"attachment": [{"name": null}]}, "jtyingzi5": {"attachment": [{"name": null}]}, "jtr2s2": {"attachment": [{"name": null}]}, "jttuzi": {"attachment": [{"name": null}]}, "jtjian": {"attachment": [{"name": null}]}, "jtr4s1": {"attachment": [{"name": null}]}, "jtr8": {"attachment": [{"name": null}]}, "jtweil": {"attachment": [{"name": "jtweil"}]}, "jtt2": {"attachment": [{"name": null}]}}, "bones": {"jtj2": {"rotate": [{"angle": 22.58, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 22.58, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 22.58, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 22.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 18.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 11.3333, "angle": 22.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "angle": 18.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12.6667, "angle": 22.58, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 16, "angle": 22.58}]}, "jtj1": {"rotate": [{"angle": 22.28, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 22.28, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 22.28, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 22.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 18.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 11.3333, "angle": 22.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "angle": 18.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12.6667, "angle": 22.28, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 16, "angle": 22.28}]}, "jtj4": {"rotate": [{"angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -29.79}, {"time": 2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "angle": -29.79}, {"time": 5.3333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7, "angle": -29.79}, {"time": 8.6667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10, "angle": -25.91, "curve": "stepped"}, {"time": 12.6667, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 13, "angle": -29.79}, {"time": 14.6667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 16, "angle": -25.91}]}, "jtj3": {"rotate": [{"angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.3333, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.3333, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.6667, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10, "angle": -3.88, "curve": "stepped"}, {"time": 12.6667, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 13, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 14.3333, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 14.6667, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 16, "angle": -3.88}]}, "jtst": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 11.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "angle": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 16}], "shear": [{"x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "x": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 11.3333, "x": -2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12.6667, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 16, "x": -2.4}]}, "jtst2": {"rotate": [{"curve": 0.274, "c3": 0.62, "c4": 0.4}, {"time": 0.8333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.1667, "curve": 0.274, "c3": 0.62, "c4": 0.4}, {"time": 5, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 49.2, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": 38.4, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 49.2, "curve": 0.25, "c3": 0.75}, {"time": 12.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 16}]}, "jtbone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -24.36, "y": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -49.56, "y": 3.36, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -24.36, "y": -6.16, "curve": "stepped"}, {"time": 12.6667, "x": -24.36, "y": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 16}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 6.6333, "x": -1, "y": 0.999, "curve": 0.274, "c2": 0.1, "c3": 0.753}, {"time": 8.3333, "x": -1, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -1, "curve": "stepped"}, {"time": 12.6667, "x": -1, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "x": -1, "y": 0.96, "curve": "stepped"}, {"time": 15.9667, "x": -1, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 16}], "shear": [{"y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 10, "y": -4.8, "curve": "stepped"}, {"time": 12.6667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 13.5, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 15.1667, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 16, "y": -4.8}]}, "jtst3": {"rotate": [{"angle": 3.64, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1667, "angle": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.5, "angle": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": 4.8, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10, "angle": 4.6, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 11.5, "curve": 0.25, "c3": 0.75}, {"time": 13.1667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 14.8333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 16, "angle": 3.64}], "shear": [{"x": 1.24, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1667, "x": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.8333, "x": -2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.5, "x": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.1667, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "x": 2.4, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10, "x": 2.2, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 11.5, "x": -2.4}, {"time": 13.1667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 14.8333, "x": -2.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 16, "x": 1.24}]}, "jtst4": {"rotate": [{"angle": 1.83, "curve": 0.306, "c2": 0.21, "c3": 0.643, "c4": 0.56}, {"time": 0.5, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 49.2, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 38.4, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 49.2, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": 8.4, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10, "angle": 8.04, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 11.5}, {"time": 12.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 13.1667, "curve": 0.25, "c3": 0.75}, {"time": 14, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 14.8333, "curve": "stepped"}, {"time": 15.6667, "curve": 0.304, "c3": 0.639, "c4": 0.36}, {"time": 16, "angle": 1.83}]}, "jtj6": {"rotate": [{"angle": -15.03, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.5, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.8333, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.1667, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -3.88, "curve": "stepped"}, {"time": 8.1667, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.5, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.8333, "angle": -25.91, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 10, "angle": -28.6, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 10.1667, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.5, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.8333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 13.1667, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 13.5, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 14.8333, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 15.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 16, "angle": -15.03}]}, "jtj5": {"rotate": [{"angle": 56, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 22.58, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 22.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1667, "angle": 18.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.8333, "angle": 22.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.5, "angle": 18.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.1667, "angle": 22.58, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": 66.68, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10, "angle": 64.8, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 11.5, "angle": 22.58, "curve": 0.25, "c3": 0.75}, {"time": 13.1667, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 14.8333, "angle": 22.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 16, "angle": 56}]}, "jtj8": {"rotate": [{"angle": -14.89}, {"time": 0.8333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.1667, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5, "angle": -29.79}, {"time": 4.1667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.5, "angle": -25.91, "curve": "stepped"}, {"time": 8.1667, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.5, "angle": -29.79}, {"time": 10, "angle": -2.98}, {"time": 10.1667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.5, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.8333, "angle": -29.79}, {"time": 13.5, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 14.8333, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 15.1667, "angle": -29.79}, {"time": 16, "angle": -14.89}]}, "jtj7": {"rotate": [{"angle": -11.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 22.28, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 22.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1667, "angle": 18.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.8333, "angle": 22.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.5, "angle": 18.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.1667, "angle": 22.28, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": -21.82, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10, "angle": -19.94, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 11.5, "angle": 22.28}, {"time": 13.1667, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 14.8333, "angle": 22.28, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 16, "angle": -11.14}]}, "jtbone5": {"translate": [{"x": -32.04, "y": -3.26, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 2.1667, "x": -49.56, "y": 3.36, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -24.36, "y": -6.16, "curve": "stepped"}, {"time": 8.1667, "x": -24.36, "y": -6.16, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 10, "x": -10.57, "y": -2.67, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 11.5, "curve": 0.25, "c3": 0.75}, {"time": 14.8333, "x": -24.36, "y": -6.16, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 16, "x": -32.04, "y": -3.26}], "scale": [{"y": 0.97, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 2.2, "x": -1, "y": 0.999, "curve": 0.274, "c2": 0.1, "c3": 0.753}, {"time": 3.8333, "x": -1, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -1, "curve": "stepped"}, {"time": 8.1667, "x": -1, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "x": -1, "y": 0.96, "curve": "stepped"}, {"time": 11.4667, "x": -1, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 11.5}, {"time": 13.1667, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 14.8333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 16, "y": 0.97}], "shear": [{"y": 1.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "y": -4.8, "curve": "stepped"}, {"time": 8.1667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 9, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "y": -4.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10, "y": -3.55, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 10.6667, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 11.5, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 13.1667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 14, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 14.8333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 15.6667, "y": 4.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 16, "y": 1.27}]}, "jtst5": {"rotate": [{"angle": 3.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3, "angle": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.3333, "angle": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 3.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.6667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 15, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 16, "angle": 3.03}], "shear": [{"x": 0.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3, "x": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "x": -2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.3333, "x": 3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "x": -2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "x": 0.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.6667, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": -2.4}, {"time": 10, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 15, "x": -2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 16, "x": 0.63}]}, "jtst6": {"rotate": [{"angle": 1.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 49.2, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 38.4, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 49.2, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 5.31, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.6667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 8.3333}, {"time": 9.1667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": "stepped"}, {"time": 12.5, "curve": 0.274, "c3": 0.62, "c4": 0.4}, {"time": 13.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 14.1667, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 15.8333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 16, "angle": 1.09}]}, "jtj10": {"rotate": [{"angle": -11.09, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.6667, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.3333, "angle": -3.88, "curve": "stepped"}, {"time": 5, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": -11.09, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 6.6667, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.3333, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.6667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.3333, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.6667, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 13.3333, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 13.6667, "angle": -29.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 15, "angle": -3.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 15.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 16, "angle": -11.09}]}, "jtj9": {"rotate": [{"angle": 50.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 22.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3, "angle": 18.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "angle": 22.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.3333, "angle": 18.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "angle": 22.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 50.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.6667, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 22.58, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 22.58, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": 22.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 16, "angle": 50.46}]}, "jtj12": {"rotate": [{"angle": -17.87}, {"time": 1, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.3333, "angle": -25.91, "curve": "stepped"}, {"time": 5, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.3333, "angle": -29.79}, {"time": 6, "angle": -17.87}, {"time": 7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.3333, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.6667, "angle": -29.79}, {"time": 10.3333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.6667, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12, "angle": -29.79}, {"time": 13.6667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 15, "angle": -25.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 15.3333, "angle": -29.79}, {"time": 16, "angle": -17.87}]}, "jtj11": {"rotate": [{"angle": -5.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 22.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3, "angle": 18.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "angle": 22.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.3333, "angle": 18.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "angle": 22.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -5.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.6667, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 22.28}, {"time": 10, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 22.28, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": 22.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 16, "angle": -5.6}]}, "jtbone7": {"translate": [{"x": -43.46, "y": 1.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.3333, "x": -24.36, "y": -6.16, "curve": "stepped"}, {"time": 5, "x": -24.36, "y": -6.16, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "x": -18.46, "y": -4.67, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": -24.36, "y": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 15, "x": -49.56, "y": 3.36, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 16, "x": -43.46, "y": 1.05}], "scale": [{"x": -1, "y": 0.974, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.6667, "x": -1, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -1, "curve": "stepped"}, {"time": 5, "x": -1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "x": -1, "y": 0.975, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.6667, "x": -1, "y": 0.96, "curve": "stepped"}, {"time": 8.3, "x": -1, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 8.3333}, {"time": 10, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 15, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 15.0333, "x": -1, "y": 0.999, "curve": 0.265, "c2": 0.09, "c3": 0.648, "c4": 0.6}, {"time": 16, "x": -1, "y": 0.974}], "shear": [{"y": 3.55, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": -4.8, "curve": "stepped"}, {"time": 5, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "y": 4.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6, "y": 3.55, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.6667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 10, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 14.1667, "y": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 15, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 15.8333, "y": 4.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 16, "y": 3.55}]}}, "deform": {"default": {"jtst": {"jtst": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 5, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": "stepped"}, {"time": 12.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.3333, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 16}]}, "jtst2": {"jtst": [{"offset": 80, "vertices": [-1.90267, 1.28115, 1.69325, -1.54738, -1.22663, 1.10199, 1.05097, -1.27061, -0.33287, 1.23685, 0.14709, -1.27237, 0.50074, 0.65133, -0.59119, -0.57047, 0.9086, 0.68496, -0.99956, -0.54365, 1.5156, 0.26906, -1.53868, -0.0429], "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": "stepped"}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10, "offset": 80, "vertices": [-2.40394, 1.61868, 2.13935, -1.95505, -1.54979, 1.39232, 1.32786, -1.60537, -0.42056, 1.56271, 0.18585, -1.60759, 0.63266, 0.82293, -0.74694, -0.72076, 1.14798, 0.86541, -1.26291, -0.68688, 1.91489, 0.33994, -1.94406, -0.0542], "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 11.5}, {"time": 13.1667, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 14.8333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 16, "offset": 80, "vertices": [-1.90267, 1.28115, 1.69325, -1.54738, -1.22663, 1.10199, 1.05097, -1.27061, -0.33287, 1.23685, 0.14709, -1.27237, 0.50074, 0.65133, -0.59119, -0.57047, 0.9086, 0.68496, -0.99956, -0.54365, 1.5156, 0.26906, -1.53868, -0.0429]}]}, "jtst3": {"jtst": [{"offset": 80, "vertices": [-1.58717, 1.06872, 1.41248, -1.2908, -1.02323, 0.91926, 0.8767, -1.05992, -0.27767, 1.03176, 0.1227, -1.06139, 0.41771, 0.54333, -0.49316, -0.47588, 0.75794, 0.57138, -0.83382, -0.4535, 1.26429, 0.22444, -1.28355, -0.03578], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": "stepped"}, {"time": 5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "offset": 80, "vertices": [-1.58717, 1.06872, 1.41248, -1.2908, -1.02323, 0.91926, 0.8767, -1.05992, -0.27767, 1.03176, 0.1227, -1.06139, 0.41771, 0.54333, -0.49316, -0.47588, 0.75794, 0.57138, -0.83382, -0.4535, 1.26429, 0.22444, -1.28355, -0.03578], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.6667, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 8.3333}, {"time": 10, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "offset": 80, "vertices": [-2.51078, 1.69062, 2.23444, -2.04195, -1.61867, 1.4542, 1.38687, -1.67672, -0.43925, 1.63216, 0.19411, -1.67904, 0.66078, 0.85951, -0.78014, -0.7528, 1.19901, 0.90388, -1.31904, -0.71741, 2, 0.35505, -2.03047, -0.05661], "curve": 0.25, "c3": 0.75}, {"time": 15, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 16, "offset": 80, "vertices": [-1.58717, 1.06872, 1.41248, -1.2908, -1.02323, 0.91926, 0.8767, -1.05992, -0.27767, 1.03176, 0.1227, -1.06139, 0.41771, 0.54333, -0.49316, -0.47588, 0.75794, 0.57138, -0.83382, -0.4535, 1.26429, 0.22444, -1.28355, -0.03578]}]}}}}, "ain6": {"slots": {"jtr4t2": {"attachment": [{"name": null}]}, "jtyingzi2": {"attachment": [{"name": null}]}, "jtr4s2": {"attachment": [{"name": null}]}, "jttuzi2": {"attachment": [{"name": null}]}, "jtr2s1": {"attachment": [{"name": null}]}, "jtr4t1": {"attachment": [{"name": null}]}, "jtt2": {"attachment": [{"name": null}]}, "jtchutou": {"attachment": [{"name": null}]}, "jtr4": {"attachment": [{"name": null}]}, "jtr2": {"attachment": [{"name": null}]}, "jts1": {"attachment": [{"name": null}]}, "jtyingzi": {"attachment": [{"name": null}]}, "jtr3": {"attachment": [{"name": null}]}, "jtdyl": {"attachment": [{"name": null}]}, "jtr9": {"attachment": [{"name": null}]}, "jtgaozi": {"attachment": [{"name": null}]}, "jtyingzi3": {"attachment": [{"name": null}]}, "jtyuxian": {"attachment": [{"name": null}]}, "jtyingzi5": {"attachment": [{"name": null}]}, "jtr2s2": {"attachment": [{"name": null}]}, "jttuzi": {"attachment": [{"name": null}]}, "jtjian": {"attachment": [{"name": null}]}, "jtr4s1": {"attachment": [{"name": null}]}, "jtr8": {"attachment": [{"name": null}]}}, "bones": {"jtr4": {"rotate": [{"angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -25.59, "curve": "stepped"}, {"time": 3.7667, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": -14.79, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 15.1333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 16.9333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 18.7, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 19.6, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 20.5, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 23.1, "angle": -25.59, "curve": 0.25, "c3": 0.75}, {"time": 24.8667, "angle": -6.43, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": -25.59}]}, "jtr3": {"rotate": [{"angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 3.42, "curve": "stepped"}, {"time": 3.7667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 3.42, "curve": "stepped"}, {"time": 10.7, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 15.1333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 16.9333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 18.7, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 19.6, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 20.5, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 23.1, "angle": 3.42}]}, "jtr2s1": {"rotate": [{"angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 12.91, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -7.94, "curve": "stepped"}, {"time": 3.7667, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": 5.71, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -7.94, "curve": "stepped"}, {"time": 10.7, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 19.6, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 23.1, "angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 24.8667, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": -7.94}]}, "jtr2s2": {"rotate": [{"angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -11.54, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 6.91, "curve": "stepped"}, {"time": 3.7667, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": -33.14, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": 18.91, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": -41.09, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": -33.14, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "angle": -25.94, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": 12.91, "curve": 0.25, "c3": 0.75}, {"time": 19.6, "angle": -17.54, "curve": 0.25, "c3": 0.75}, {"time": 23.1, "angle": 16.51, "curve": 0.25, "c3": 0.75}, {"time": 24.8667, "angle": -58.34, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": 6.91}]}, "jtr2": {"rotate": [{"angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 1.2, "curve": "stepped"}, {"time": 3.7667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 1.2, "curve": "stepped"}, {"time": 10.7, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 19.6, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 23.1, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 24.8667, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": 1.2}], "scale": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.7667, "y": 0.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "y": -1, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 5.5333, "y": -0.94, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 7.1667, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 8.9, "y": 0.96, "curve": 0.372, "c2": 0.49, "c3": 0.752}, {"time": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 10.7667, "y": -1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12.4667, "y": -0.963, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 14.1667, "y": -1.005, "curve": 0.25, "c3": 0.75}, {"time": 14.2667}, {"time": 16.0333, "y": 0.98}, {"time": 17.8333}, {"time": 19.6, "y": 0.96}, {"time": 23.1}, {"time": 24.8667, "y": 0.98}, {"time": 26.6667}], "shear": [{"curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 1.7667, "x": 2.4, "curve": 0.314, "c2": 0.27, "c3": 0.656, "c4": 0.63}, {"time": 3.5667, "curve": 0.341, "c2": 0.36, "c3": 0.683, "c4": 0.72}, {"time": 5.3333, "x": -2.4, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 7.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.1, "x": 0.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.9, "x": -3.6, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 9.8333, "x": 0.72, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "curve": 0.25, "c3": 0.75}, {"time": 19.6, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 23.1, "curve": 0.25, "c3": 0.75}, {"time": 23.9667, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 24.8667, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 25.7667, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 26.6667}]}, "jtr8": {"rotate": [{"angle": 2.03, "curve": "stepped"}, {"time": 0.0333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3, "angle": -3.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 2.03, "curve": "stepped"}, {"time": 7.3667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": -2.38, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 13.4, "angle": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 13.8667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 14.9333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 16.9667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 17.8667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 18.7667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 19.6333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 20.5333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 23.1333, "angle": 2.03}]}, "jtr9": {"rotate": [{"angle": -8.89, "curve": "stepped"}, {"time": 0.0333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -8.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3, "angle": -3.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -8.89}, {"time": 4.5, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -8.89, "curve": "stepped"}, {"time": 7.3667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": -13.3, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 13.4, "angle": -12.29, "curve": 0.25, "c3": 0.75}, {"time": 13.8667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 14.9333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 16.9667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 17.8667, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 18.7667, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 19.6333, "angle": -8.89, "curve": 0.25, "c3": 0.75}, {"time": 20.5333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 23.1333, "angle": -8.89}]}, "jtr4s1": {"rotate": [{"angle": -11.48, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 9.77, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.3, "angle": 6.21, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -9.53, "curve": "stepped"}, {"time": 7.3667, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 13.4, "angle": -25.27, "curve": 0.25, "c3": 0.75}, {"time": 14.3, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 16.0667, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 17.8667, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 19.6333, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 23.1333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 24.0333, "angle": -123.53, "curve": 0.25, "c3": 0.75}, {"time": 24.8333, "angle": -105.53, "curve": 0.25, "c3": 0.75}, {"time": 25.8, "angle": -123.53, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 26.6667, "angle": -11.48}]}, "jtr4s2": {"rotate": [{"angle": 9.92, "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 0.0333, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -9.53, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.3, "angle": -5.97, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 9.77, "curve": "stepped"}, {"time": 7.3667, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 11.7333, "angle": -2.52, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": -49.03, "curve": 0.25, "c3": 0.75}, {"time": 13.4, "angle": 1.18, "curve": 0.25, "c3": 0.75}, {"time": 14.3, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 16.0667, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 17.8667, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 19.6333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 23.1333, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 24.9, "angle": 26.57, "curve": 0.249, "c3": 0.739, "c4": 0.95}, {"time": 26.6667, "angle": 9.92}]}, "jtr6": {"rotate": [{"angle": 1.16, "curve": 0.342, "c2": 0.66, "c3": 0.675}, {"time": 0.0333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.3, "angle": -2.71, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6, "angle": 1.2}, {"time": 5.4, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": 1.2, "curve": "stepped"}, {"time": 6.4667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 11.7, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 13.4, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 14.3, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 16.0667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 17.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 19.6333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 23.1333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 25, "angle": -3.6, "curve": 0.248, "c3": 0.739, "c4": 0.95}, {"time": 26.6667, "angle": 1.16}], "scale": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": -1, "curve": 0.25, "c3": 0.624, "c4": 0.5}, {"time": 1.8333, "y": -0.98, "curve": 0.336, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 2.3, "y": -0.987, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.5333, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "y": 0.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.2667, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "y": -1, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 8.4333, "y": -0.96, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 10.9333, "y": -1, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 12.5, "y": -0.96, "curve": 0.372, "c2": 0.49, "c3": 0.752}, {"time": 14.3, "y": -1, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 16.0667, "y": -0.96, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 17.7333, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 17.8667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 19.6333, "y": 1.013, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 23.1333, "y": 0.98, "curve": 0.344, "c2": 0.37, "c3": 0.688, "c4": 0.73}, {"time": 25, "y": 1.013, "curve": 0.377, "c2": 0.59, "c3": 0.72, "c4": 0.98}, {"time": 26.6667}]}, "jtr7": {"rotate": [{"angle": 1.71, "curve": 0.346, "c2": 0.66, "c3": 0.68}, {"time": 0.0333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -4.8, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 2.3, "angle": -4.43, "curve": 0.295, "c2": 0.19, "c3": 0.755}, {"time": 3.6, "angle": 1.8, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.8667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 1.8, "curve": "stepped"}, {"time": 7.3667, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 9.4, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 11.6333, "angle": 4.21, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": -7.8, "curve": 0.25, "c3": 0.75}, {"time": 13.4, "angle": 5.16, "curve": 0.25, "c3": 0.75}, {"time": 14.3, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 14.5667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 16.3667, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 17.8667, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 18.1333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 19.9, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 23.1333, "angle": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 23.8333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 25.6, "angle": -4.8, "curve": 0.248, "c3": 0.733, "c4": 0.92}, {"time": 26.6667, "angle": 1.71}]}, "jtr5": {"rotate": [{"angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 2.19, "curve": "stepped"}, {"time": 3.7667, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 8.19, "curve": "stepped"}, {"time": 9.3333, "angle": 8.19, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": -2.21, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": 7.19, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 12.6, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 14.3667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 16.1667, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 17.9333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 19.7333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 23.1, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 23.2, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 25, "angle": 14.4, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": 2.19}]}, "jtr4t1": {"rotate": [{"angle": 3.6, "curve": "stepped"}, {"time": 0.0333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3, "angle": -1.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 3.6}, {"time": 4.5, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 3.6, "curve": "stepped"}, {"time": 7.3667, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": 3.6, "curve": "stepped"}, {"time": 13.4, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 14.3667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 15.4333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 16.4, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 17.3667, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 18.3667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 19.3333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 23.0333, "angle": 3.6}], "translate": [{"x": 0.05, "y": 0.54, "curve": "stepped"}, {"time": 0.0333, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3, "x": 0.02, "y": 0.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 0.05, "y": 0.54}, {"time": 4.5, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 0.05, "y": 0.54, "curve": "stepped"}, {"time": 7.3667, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "x": 0.05, "y": 0.54, "curve": "stepped"}, {"time": 13.4, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 14.3667, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 15.4333, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 16.4, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 17.3667, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 18.3667, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 19.3333, "x": 0.05, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "x": -0.01, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 23.0333, "x": 0.05, "y": 0.54}]}, "jtr4t2": {"rotate": [{"angle": -3.6, "curve": "stepped"}, {"time": 0.0333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3, "angle": 0.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -3.6}, {"time": 4.5, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -3.6, "curve": "stepped"}, {"time": 7.3667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": 10.8, "curve": "stepped"}, {"time": 13.4, "angle": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 14.3667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 15.4333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 16.4, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 17.3667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 18.3667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 19.3333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 23.0333, "angle": -3.6}], "translate": [{"x": 0.05, "y": -0.26, "curve": "stepped"}, {"time": 0.0333, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3, "x": 0.02, "y": -0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 0.05, "y": -0.26}, {"time": 4.5, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 0.05, "y": -0.26, "curve": "stepped"}, {"time": 7.3667, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "x": 0.07, "y": 0.32, "curve": "stepped"}, {"time": 13.4, "x": 0.07, "y": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 14.3667, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 15.4333, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 16.4, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 17.3667, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 18.3667, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 19.3333, "x": 0.05, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 20.3333, "x": -0.01, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 23.0333, "x": 0.05, "y": -0.26}]}, "jtr10": {"rotate": [{"time": 7.3333}, {"time": 7.4, "angle": 10.8}, {"time": 9.3333, "angle": 4.8}, {"time": 10.7, "angle": 12, "curve": "stepped"}, {"time": 17.7333, "angle": 12}, {"time": 17.8667, "angle": 2.4, "curve": "stepped"}, {"time": 23.1333, "angle": 2.4}, {"time": 23.2333, "angle": 10.8}, {"time": 26.5333, "angle": 11.08}, {"time": 26.6667}], "scale": [{"y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "y": 0.912, "curve": "stepped"}, {"time": 3.7667, "y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "y": 0.912, "curve": "stepped"}, {"time": 10.7, "y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 10.8, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 11.7, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 12.6, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 13.5, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "y": 0.912, "curve": "stepped"}, {"time": 14.3, "y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 14.4, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 15.3, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 16.2, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 17.1333, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 17.8667, "y": 0.912, "curve": "stepped"}, {"time": 23.1333, "y": 0.912, "curve": 0.25, "c3": 0.75}, {"time": 23.2333, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 24.1333, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 25.0333, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 25.9333, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "y": 0.912}]}, "jtr1": {"rotate": [{"angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -1.2, "curve": "stepped"}, {"time": 3.7667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.4333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 11.6, "curve": 0.25, "c3": 0.75}, {"time": 12.4667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 13.3667, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 15.1333, "curve": 0.25, "c3": 0.75}, {"time": 16.0333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 16.9333, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 19, "curve": 0.25, "c3": 0.75}, {"time": 19.8667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 20.7667, "curve": 0.25, "c3": 0.75}, {"time": 23.1, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 23.9667, "curve": 0.25, "c3": 0.75}, {"time": 24.8667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 25.7667, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": -1.2}], "scale": [{"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "y": -1, "curve": "stepped"}, {"time": 17.8, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "curve": "stepped"}, {"time": 23.1, "curve": 0.25, "c3": 0.75}, {"time": 23.1333, "y": -1, "curve": "stepped"}, {"time": 26.6333, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 26.6667}]}, "jtr11": {"rotate": [{"angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -1.74, "curve": "stepped"}, {"time": 3.7667, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "angle": -8.94, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": -10.14, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 10.9333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 11.8, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 12.7, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 13.6, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 14.5, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 15.3667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 16.2667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 17.1667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 17.8333, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 18.0667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 18.9333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 19.8333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 20.7333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 23.1, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 23.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 24.2, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 25.1, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 26, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 26.6667, "angle": -1.74}]}, "jtbe5": {"translate": [{"x": -18.58, "y": -15.03, "curve": 0.337, "c2": 0.66, "c3": 0.671}, {"time": 0.0333, "x": -18.58, "y": -15.04, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.3, "x": 10.92, "y": -16.1, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.6, "x": 25.84, "y": -16.64}, {"time": 5.0667, "x": 4.73, "y": -8.36}, {"time": 6.2667, "x": -12.42, "y": 1.48, "curve": "stepped"}, {"time": 6.4667, "x": -12.42, "y": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": -11.59, "y": 6.95, "curve": 0.313, "c2": 0.27, "c3": 0.682, "c4": 0.72}, {"time": 9.2667, "x": 6.03, "y": 19.83, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 10.3, "x": 11.56, "y": 14.67, "curve": 0.251, "c3": 0.624, "c4": 0.5}, {"time": 11.6333, "x": 11.53, "y": 13.64, "curve": 0.374, "c2": 0.5, "c3": 0.751}, {"time": 13.0333, "x": 11.56, "y": 14.67, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 14.3, "x": 14.35, "y": 5.56, "curve": 0.318, "c2": 0.28, "c3": 0.69, "c4": 0.74}, {"time": 16.7333, "x": 23.63, "y": -8.28, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 17.8667, "x": 25.84, "y": -16.64, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 18.7667, "x": 17.72, "y": -14.15, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 20.5333, "x": -10.46, "y": -19.32, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 23.1333, "x": -18.58, "y": -15.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 24.9, "x": -18.54, "y": -14, "curve": 0.373, "c2": 0.49, "c3": 0.745, "c4": 0.98}, {"time": 26.6667, "x": -18.58, "y": -15.03}]}, "jtbe7": {"translate": [{"x": 18.62, "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 1.2, "x": 12.81, "y": 4.23, "curve": 0.327, "c2": 0.32, "c3": 0.683, "c4": 0.72}, {"time": 2.6, "x": 2.83, "y": 4.02, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 3.5667, "x": -1.47, "y": 7.71, "curve": "stepped"}, {"time": 3.7667, "x": -1.47, "y": 7.71, "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 4.9667, "x": 7.47, "y": 7.7, "curve": 0.326, "c2": 0.31, "c3": 0.676, "c4": 0.69}, {"time": 6.1333, "x": 20.41, "y": 13.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 7.2333, "x": 28.9, "y": 14.24, "curve": "stepped"}, {"time": 10.7, "x": 28.9, "y": 14.24, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 11.8, "x": 35.41, "y": 16.14, "curve": 0.324, "c2": 0.3, "c3": 0.683, "c4": 0.72}, {"time": 13.3, "x": 48.76, "y": 29.63, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 14.2667, "x": 54.18, "y": 31.14, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 15.3667, "x": 50.87, "y": 23.36, "curve": 0.324, "c2": 0.3, "c3": 0.681, "c4": 0.71}, {"time": 16.7667, "x": 44.3, "y": 18.47, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 17.8333, "x": 41.36, "y": 13.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 18.7, "x": 37.17, "y": 7.97, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 20.0333, "x": 26.33, "y": 5.42, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 23.1, "x": 18.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 24.8667, "x": 18.62, "y": 1.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 26.6667, "x": 18.62}]}, "jtbe3": {"translate": [{"x": -102.17, "y": 24.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.7667, "x": -84.43, "y": 20.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5667, "x": -66.69, "y": 14.19, "curve": "stepped"}, {"time": 3.7667, "x": -66.69, "y": 14.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.7667, "x": -39.36, "y": 20.53, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 7.3667, "x": -20.34, "y": 27.43, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "x": -20.34, "y": 23.43, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "x": -20.34, "y": 27.43, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 10.9333, "x": -29.8, "y": 24.05, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 12.1333, "x": -40.28, "y": 18.02, "curve": 0.25, "c3": 0.75}, {"time": 14.2667, "x": -64.67, "y": 17.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 16.0333, "x": -71.97, "y": 22.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 17.8333, "x": -79.28, "y": 30.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 19.6, "x": -78.74, "y": 30.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 23.1, "x": -79.28, "y": 30.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 24.8667, "x": -90.72, "y": 28.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 26.6667, "x": -102.17, "y": 24.6}]}}, "deform": {"default": {"jtt2": {"jtt2": [{"vertices": [60.11194, 12.061, 1.71193, 12.061, 1.71193, 43.26099, 60.11194, 43.26099]}]}, "jtt1": {"jtt1": [{"vertices": [7.372, -18.17333, -48.30804, -18.17333, -48.30804, 18.17332, 7.37199, 18.17332]}]}}}, "drawOrder": [{"time": 2.3, "offsets": [{"slot": "jtr4s1", "offset": -14}]}]}, "ain7": {"slots": {"jtr4t2": {"attachment": [{"name": null}]}, "nj4": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nj4"}]}, "jtt1": {"attachment": [{"name": null}]}, "jtr4s2": {"attachment": [{"name": null}]}, "jttuzi2": {"attachment": [{"name": null}]}, "jtr2s1": {"attachment": [{"name": null}]}, "jtr1": {"attachment": [{"name": null}]}, "jtr4t1": {"attachment": [{"name": null}]}, "jtyingzi4": {"attachment": [{"name": null}]}, "jtt2": {"attachment": [{"name": null}]}, "jtchutou": {"attachment": [{"name": null}]}, "nj1": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nj1"}]}, "jtr3": {"attachment": [{"name": null}]}, "jtr2": {"attachment": [{"name": null}]}, "jts1": {"attachment": [{"name": null}]}, "jtyingzi": {"attachment": [{"name": null}]}, "rt": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "rt"}]}, "jtdyl": {"attachment": [{"name": null}]}, "jtr9": {"attachment": [{"name": null}]}, "nt": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nt"}]}, "yingzi": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 18.3333, "color": "ffffff00"}], "attachment": [{"name": "n<PERSON><PERSON>"}]}, "nst": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nst"}]}, "jtyingzi3": {"attachment": [{"name": null}]}, "rst": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "rst"}]}, "jtgaozi": {"attachment": [{"name": null}]}, "jtyuxian": {"attachment": [{"name": null}]}, "jtyingzi2": {"attachment": [{"name": null}]}, "jtyingzi5": {"attachment": [{"name": null}]}, "jtr2s2": {"attachment": [{"name": null}]}, "jttuzi": {"attachment": [{"name": null}]}, "jtjian": {"attachment": [{"name": null}]}, "nj3": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nj3"}]}, "jtr4s1": {"attachment": [{"name": null}]}, "nj2": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nj2"}]}, "jtr8": {"attachment": [{"name": null}]}, "jtr4": {"attachment": [{"name": null}]}}, "bones": {"nst": {"rotate": [{"angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 1.2, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.8333, "angle": -8.4, "curve": "stepped"}, {"time": 7.3333, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": 1.2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.8333, "y": -4, "curve": "stepped"}, {"time": 7.3333, "y": -4, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "y": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "y": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 15}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": -2.4, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -1.8, "y": -1.5, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.4, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "y": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 12.7333, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 14, "y": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 15}]}, "rst": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 2.31, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.41, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 15}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.56, "y": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -0.56, "y": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "x": -0.56, "y": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 15}]}, "rst2": {"rotate": [{"angle": -5.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -7.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "angle": -1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -5.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.1, "angle": -7.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.8333, "angle": -1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.3333, "angle": -5.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333, "angle": -7.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10, "angle": -1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.5, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": -5.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667, "angle": -7.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 13.3333, "angle": -1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 13.8333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": -5.46}]}, "rst3": {"rotate": [{"angle": -10.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -27.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": -10.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -27.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.3333, "angle": -10.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -27.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.6667, "angle": -10.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.3333, "curve": 0.25, "c3": 0.75}, {"time": 14, "angle": -27.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 15, "angle": -10.15}]}, "rt": {"rotate": [{"angle": 0.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": 0.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.3333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -9.59, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.3333, "angle": 0.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -3.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.6667, "angle": 0.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.3333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 14, "angle": -3.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 15, "angle": 0.19}]}, "nt": {"rotate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.6667, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.6, "angle": -13.2, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.1, "angle": -16.8, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.6, "angle": -13.2, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 6.1, "angle": -16.8, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6.6, "angle": -13.2, "curve": 0.313, "c2": 0.27, "c3": 0.677, "c4": 0.7}, {"time": 7.0333, "angle": -21.36, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3333, "angle": -18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 8.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9, "angle": 3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12.3333, "angle": 3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 13.3333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 15}], "translate": [{"x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.08, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 0.11, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "x": 0.19, "y": 1.91, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 0.17, "y": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 0.08, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "x": 0.11, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "x": 0.08, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 14.1667, "x": 0.11, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 15, "x": -0.09, "y": -0.44}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.2, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "y": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "y": -1.84, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": 1.2, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "x": 1.2, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 15}]}, "nj1": {"rotate": [{"angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -22.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.1, "angle": -2.16, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.8333, "angle": 22.58, "curve": "stepped"}, {"time": 7.3333, "angle": 22.58}, {"time": 8.3333, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": -22.39}]}, "nj2": {"rotate": [{"angle": -29.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2, "angle": 1.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -29.44, "curve": "stepped"}, {"time": 8.3333, "angle": -29.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.6667, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.3333, "angle": 1.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.6667, "angle": -29.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 13.3333, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 13.6667, "angle": 1.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 15, "angle": -29.44}]}, "nj4": {"rotate": [{"angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -11.53, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 20.8, "curve": "stepped"}, {"time": 7.3333, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": 1.67}]}, "nj5": {"rotate": [{"angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": 1.67}, {"time": 2, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -2.99, "curve": 0.343, "c2": 0.38, "c3": 0.677, "c4": 0.71}, {"time": 4.8333, "angle": -24.59, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 8.3333, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.6667, "angle": 1.67}, {"time": 10.3333, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.6667, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12, "angle": 1.67}, {"time": 13.6667, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 15, "angle": -2.99}]}, "nj3": {"rotate": [{"angle": -3.47, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -23.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -3.47, "curve": 0.341, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 4.6, "angle": -16.67, "curve": "stepped"}, {"time": 7.3333, "angle": -16.67, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 8.3333, "angle": -3.47, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": -23.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": -3.47, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 13.8333, "angle": -23.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": -3.47}]}, "nj6": {"rotate": [{"angle": -6.65, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -36.71, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -6.65, "curve": "stepped"}, {"time": 8.3333, "angle": -6.65, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": -36.71, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": -6.65, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 13.8333, "angle": -36.71, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": -6.65}]}, "nj7": {"rotate": [{"angle": 6.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5}, {"time": 2.1667, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 6.43, "curve": 0.341, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 4.6, "angle": 26.83, "curve": "stepped"}, {"time": 7.3333, "angle": 26.83, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 8.3333, "angle": 6.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333}, {"time": 10.5, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": 6.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667}, {"time": 13.8333, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": 6.43}]}, "nj8": {"rotate": [{"angle": -3.5, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -13.1}, {"time": 2.1667, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -3.5, "curve": "stepped"}, {"time": 8.3333, "angle": -3.5, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333, "angle": -13.1}, {"time": 10.5, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": -3.5, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667, "angle": -13.1}, {"time": 13.8333, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": -3.5}]}, "root": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 15}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 88.05, "y": -7.63, "curve": "stepped"}, {"time": 7.5, "x": 88.05, "y": -7.63, "curve": 0.25, "c3": 0.75}, {"time": 15, "x": 231.28, "y": -20.55}]}}, "deform": {"default": {"nst": {"nst": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 22, "vertices": [0.84927, -3.34685, -0.47458, -2.69335, -0.6053, -3.12714, -0.1123, -0.73801], "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "offset": 22, "vertices": [0.84927, -3.34685, -0.47458, -2.69335, -0.6053, -3.12714, -0.1123, -0.73801], "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "offset": 22, "vertices": [0.84927, -3.34685, -0.47458, -2.69335, -0.6053, -3.12714, -0.1123, -0.73801], "curve": 0.25, "c3": 0.75}, {"time": 15}]}}}}, "ain8": {"slots": {"jtr4t2": {"attachment": [{"name": null}]}, "jtyingzi2": {"attachment": [{"name": null}]}, "jtr4s2": {"attachment": [{"name": null}]}, "jttuzi2": {"attachment": [{"name": null}]}, "jtr2s1": {"attachment": [{"name": null}]}, "jtr1": {"attachment": [{"name": null}]}, "jtr4t1": {"attachment": [{"name": null}]}, "jtyingzi4": {"attachment": [{"name": null}]}, "jtt2": {"attachment": [{"name": null}]}, "jtchutou": {"attachment": [{"name": null}]}, "jtr4": {"attachment": [{"name": null}]}, "jtr2": {"attachment": [{"name": null}]}, "jts1": {"attachment": [{"name": null}]}, "jtyingzi": {"attachment": [{"name": null}]}, "jtr3": {"attachment": [{"name": null}]}, "t5": {"attachment": [{"name": "t5"}]}, "jtdyl": {"attachment": [{"name": null}]}, "jtr9": {"attachment": [{"name": null}]}, "jtgaozi": {"attachment": [{"name": null}]}, "jtyingzi3": {"attachment": [{"name": null}]}, "jtt1": {"attachment": [{"name": null}]}, "jtyuxian": {"attachment": [{"name": null}]}, "jtyingzi5": {"attachment": [{"name": null}]}, "jtr2s2": {"attachment": [{"name": null}]}, "jttuzi": {"attachment": [{"name": null}]}, "jtjian": {"attachment": [{"name": null}]}, "jtr4s1": {"attachment": [{"name": null}]}, "jtr8": {"attachment": [{"name": null}]}}, "bones": {"jtroot": {"scale": [{"x": 1.098, "y": 1.098}]}, "jtbe28": {"rotate": [{"angle": -13.25}]}, "jtbe32": {"rotate": [{"angle": -13.77}], "translate": [{"x": -0.68, "y": 0.59}]}, "jtbe29": {"rotate": [{"angle": 1.19}]}, "jtbe34": {"translate": [{"x": 5.68, "y": -240.69}]}}, "deform": {"default": {"jttian2": {"jttian": [{"vertices": [-7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911]}]}, "jttian": {"jttian": [{"vertices": [-7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911]}]}}}}, "ain9": {"slots": {"jtr4t2": {"attachment": [{"name": null}]}, "jtyingzi2": {"attachment": [{"name": null}]}, "jtr4s2": {"attachment": [{"name": null}]}, "jttuzi2": {"attachment": [{"name": null}]}, "jtr2s1": {"attachment": [{"name": null}]}, "jtr1": {"attachment": [{"name": null}]}, "jtr4t1": {"attachment": [{"name": null}]}, "jtyingzi4": {"attachment": [{"name": null}]}, "jtt2": {"attachment": [{"name": null}]}, "jtchutou": {"attachment": [{"name": null}]}, "jtr4": {"attachment": [{"name": null}]}, "jtr2": {"attachment": [{"name": null}]}, "jts1": {"attachment": [{"name": null}]}, "jtyingzi": {"attachment": [{"name": null}]}, "jtr3": {"attachment": [{"name": null}]}, "jtdyl": {"attachment": [{"name": null}]}, "jtr9": {"attachment": [{"name": null}]}, "jtgaozi": {"attachment": [{"name": null}]}, "jtqiao": {"attachment": [{"name": "jtqiao"}]}, "jtyingzi3": {"attachment": [{"name": null}]}, "jtt1": {"attachment": [{"name": null}]}, "jtyuxian": {"attachment": [{"name": null}]}, "jtyingzi5": {"attachment": [{"name": null}]}, "jtr2s2": {"attachment": [{"name": null}]}, "jttuzi": {"attachment": [{"name": null}]}, "jtjian": {"attachment": [{"name": null}]}, "jtr4s1": {"attachment": [{"name": null}]}, "jtr8": {"attachment": [{"name": null}]}}, "bones": {"jtroot": {"scale": [{"x": 1.098, "y": 1.098}]}, "jtbe28": {"rotate": [{"angle": -13.25}]}, "jtbe32": {"rotate": [{"angle": -13.77}], "translate": [{"x": -0.68, "y": 0.59}]}, "jtbe29": {"rotate": [{"angle": 1.19}]}, "jtbe34": {"translate": [{"x": 5.68, "y": -240.69}]}}, "deform": {"default": {"jttian2": {"jttian": [{"vertices": [-7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911]}]}, "jttian": {"jttian": [{"vertices": [-7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911, -7.80542, 35.28911]}]}}}}}}