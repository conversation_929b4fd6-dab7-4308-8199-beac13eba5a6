import { IConfigGuide } from "../game/JsonDefine";
import { JsonMgr } from "../game/mgr/JsonMgr";

/**
 * 提示内部使用的路由名称枚举
 */
export enum GuideRouteEnum {
  TopTalk = "TopTalk",
  TopFinger = "TopFinger",
  TopOpen = "TopOpen",
  TopEventRoute = "TopEventRoute",
  TopFusu = "TopFusu",
  TopLoading = "TopLoading",
  TopLoading2 = "TopLoading2",
  TopQiYunGame = "TopQiYunGame",
  TopConfirm = "TopConfirm",
  TopHeroHelp = "TopHeroHelp",
  TopEventHeroCome = "TopEventHeroCome",
}

// 返回脚本列表
export function getConfigGuideList(type: number): IConfigGuide[] {
  let values = Object.values(JsonMgr.instance.jsonList.c_guide) as IConfigGuide[];

  return values.filter((item) => item.type == type);
}
