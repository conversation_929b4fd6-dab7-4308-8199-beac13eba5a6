import { _decorator, Label, sp, tween, UIOpacity, v3, Vec3 } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ClubModule } from "../../../module/club/ClubModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import { ItemCtrl } from "../../common/ItemCtrl";
import { ClubBargainAdapter } from "./adapter/ClubBargainViewHolder";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { RewardMessage } from "../../net/protocol/Comm";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import TipMgr from "../../../lib/tips/TipMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PublicRouteName } from "../../../module/player/PlayerConstant";
import { BargainRecordMessage } from "../../net/protocol/Club";
import { ConfirmMsg } from "../UICostConfirm";
import { LangMgr } from "../../mgr/LangMgr";
import { ClubUnBargainAdapter } from "./adapter/ClubUnbargainViewHolder";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Fri Mar 07 2025 15:57:00 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubBargain.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIClubBargain")
export class UIClubBargain extends UINode {
  protected _openAct: boolean = true; //打开动作
  private _curPrice = 0;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubBargain`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    // do something
    this.refreshUI();
  }
  private refreshUI() {
    let index = ClubModule.data.clubMessage.bargain.bargainId;
    let bargainConfig = JsonMgr.instance.jsonList.c_unionBuy[index];
    let bargainList = Object.values(ClubModule.data.clubMessage.bargain.recordMap);
    let deductPrice = 0;
    bargainList.forEach((item) => {
      deductPrice += item.deductPrice;
    });
    this._curPrice = bargainConfig.price[1] - deductPrice;
    this.getNode("lbl_cur_price").getComponent(Label).string = `${this._curPrice}`;
    this.getNode("lbl_orignal_price").getComponent(Label).string = `${bargainConfig.price[1]}`;
    if (deductPrice == 0) {
      this.getNode("lbl_orignal_price").active = false;
    } else {
      this.getNode("lbl_orignal_price").active = true;
    }
    for (let i = 0; i < bargainConfig.rewardList.length && i < this.getNode("node_goods").children.length; i++) {
      let itemNode = this.getNode("node_goods").children[i];
      itemNode.getComponent(ItemCtrl).setItemId(bargainConfig.rewardList[i][0], bargainConfig.rewardList[i][1], false);
    }
    this.getNode("lbl_title").getComponent(
      Label
    ).string = `当前${bargainList.length}/${ClubModule.data.clubMessage.memberList.length}人累计砍价`;
    this.getNode("lbl_total_bargain").getComponent(Label).string = `${deductPrice}`;

    let isBargain = false;
    let isBuy = false;
    if (ClubModule.data.clubMessage.bargain.recordMap[PlayerModule.data.playerId]) {
      isBargain = true;
      isBuy = ClubModule.data.clubMessage.bargain.recordMap[PlayerModule.data.playerId].paid;
    }
    this.getNode("btn_bargain").active = !isBargain; // 未砍价则显示砍价按钮
    this.getNode("btn_buy").active = isBargain && !isBuy; // 已砍价则显示购买按钮
    this.getNode("btn_yigoumai").active = isBuy;

    if (this._curPrice < 1000) {
      this.getNode("node_shangren").getComponent(sp.Skeleton).setAnimation(0, "animation3", true);
      let msg = LangMgr.txMsgCode(240, [], "");
      this.getNode("lbl_daji_talk").getComponent(Label).string = msg;
    } else if (this._curPrice < 0) {
      this.getNode("node_shangren").getComponent(sp.Skeleton).setAnimation(0, "animation2", true);
      let msg = LangMgr.txMsgCode(241, [], "");
      this.getNode("lbl_daji_talk").getComponent(Label).string = msg;
    } else {
      this.getNode("node_shangren").getComponent(sp.Skeleton).setAnimation(0, "animation", true);
      let msg = LangMgr.txMsgCode(239, [], "");
      this.getNode("lbl_daji_talk").getComponent(Label).string = msg;
    }

    let adapter = new ClubBargainAdapter(this.getNode("history_view_holder"));
    this.getNode("node_history_list").getComponent(AdapterView).setAdapter(adapter);
    adapter.setData(bargainList);
  }

  private on_click_btn_bargain() {
    if (ClubModule.data.clubMessage.bargain.endTime < TimeUtils.serverTime) {
      TipMgr.showTip("活动已结束");
      return;
    }

    //
    ClubModule.api.addBargain((data: BargainRecordMessage) => {
      //
      this.refreshUI();
      this.getNode("lbl_deduct_price").getComponent(Label).string = `-${data.deductPrice}`;
      this.getNode("lbl_deduct_price").setPosition(v3(0, 0, 0));
      this.getNode("bargain_finish").active = true;
      this.getNode("bargain_finish")
        .getComponent(sp.Skeleton)
        .setCompleteListener(() => {
          this.getNode("bargain_finish").active = false;
        });
      this.getNode("bargain_finish").getComponent(sp.Skeleton).setAnimation(0, "animation", false);
      tween(this.getNode("lbl_deduct_price"))
        .hide()
        .delay(0.3)
        .show()
        .by(
          2,
          { position: new Vec3(0, 100, 0) },
          {
            easing: "sineOut",
            onUpdate: (target, ratio) => {
              this.getNode("lbl_deduct_price").getComponent(UIOpacity).opacity = 255 * this.transform(ratio);
            },
          }
        )
        .start();
    });
  }

  private transform(ratio) {
    if (ratio < 0.8) {
      return 1;
    }
    return 1 - (ratio - 0.8) / 0.2;
  }

  private on_click_btn_buy() {
    if (ClubModule.data.clubMessage.bargain.endTime < TimeUtils.serverTime) {
      TipMgr.showTip("活动已结束");
      return;
    }
    let index = ClubModule.data.clubMessage.bargain.bargainId;
    let bargainConfig = JsonMgr.instance.jsonList.c_unionBuy[index];
    let coinName = JsonMgr.instance.jsonList.c_item[bargainConfig.price[0]].name;
    let msg: ConfirmMsg = {
      msg: `您确定花费'${this._curPrice}${coinName}'进行购买`,
      itemList: [bargainConfig.price[0], this._curPrice],
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        //
        ClubModule.api.buyBargain((data: RewardMessage) => {
          //
          this.refreshUI();
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
        });
      }
    });
  }

  private on_click_btn_unbargain() {
    //
    let unBargainList = [];
    for (let i = 0; i < ClubModule.data.clubMessage.memberList.length; i++) {
      let member = ClubModule.data.clubMessage.memberList[i].simpleMessage;

      if (!ClubModule.data.clubMessage.bargain.recordMap[member.userId]) {
        unBargainList.push(member);
      }
    }
    this.getNode("btn_unbargain_blank").active = true;
    let adapter = new ClubUnBargainAdapter(this.getNode("unbargain_view_holder"));
    this.getNode("node_unbargain_list").getComponent(AdapterView).setAdapter(adapter);
    adapter.setData(unBargainList);
  }

  private on_click_btn_unbargain_blank() {
    //
    this.getNode("btn_unbargain_blank").active = false;
  }
}
