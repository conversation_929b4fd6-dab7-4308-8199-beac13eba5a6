import { JsonMgr } from "../../game/mgr/JsonMgr";
import { FractureMessage } from "../../game/net/protocol/Activity";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { times } from "../../lib/utils/NumbersUtils";

const log = Logger.getLoger(LOG_LEVEL.STOP);
export class FractureData {
  private _fractureData: FractureMessage;
  public get fractureData(): FractureMessage {
    return this._fractureData;
  }
  public set fractureData(value: FractureMessage) {
    this._fractureData = value;
    log.log("fractureData", this._fractureData);
  }
}
