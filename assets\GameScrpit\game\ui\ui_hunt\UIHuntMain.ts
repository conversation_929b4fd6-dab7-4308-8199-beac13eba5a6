import { _decorator, Label, sp } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { HuntModule } from "../../../module/hunt/HuntModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { HuntRouteName } from "../../../module/hunt/HuntRoute";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import MsgEnum from "../../event/MsgEnum";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { GameDirector } from "../../GameDirector";
import { FmConfig, SystemOpenEnum } from "../../GameDefine";
import TipMgr from "../../../lib/tips/TipMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { HuntAudioName } from "../../../module/hunt/HuntConfig";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("UIHuntMain")
export class UIHuntMain extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntMain`;
  }
  protected _openAct: boolean = true; //打开动作

  private _basicsBossData: any = null;

  private _basicsHuntData: any = null;

  private _spititState: boolean = null;

  private _primitiveState: boolean = null;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_CLOSE_CALL_ENTRY, this.isShowBuyBtn, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_CLOSE_CALL_ENTRY, this.isShowBuyBtn, this);
  }

  private isShowBuyBtn() {
    this.getNode("btn_summon").active = false;
    GoodsModule.api.buyInfo((data) => {
      let list = GoodsModule.data.getShopItemList(9);
      let shopId = list[0];

      let c_shop = JsonMgr.instance.jsonList.c_shop;
      let c_info_shop = c_shop[shopId];

      if (c_info_shop.maxtype == 4 && PlayerModule.service.isShopBuy(c_info_shop.itemsList[0][0])) {
        this.getNode("btn_summon").active = true;
      } else {
        this.getNode("btn_summon").active = false;
      }
    });
  }
  protected onEvtShow(): void {
    this.getNode("btn_test1").active = FmConfig.isDebug;
    this.getNode("btn_test2").active = FmConfig.isDebug;
    this.getNode("btn_test3").active = FmConfig.isDebug;

    AudioMgr.instance.playMusic(AudioName.Sound.天荒古境);

    this.getBasicsBossData();
    this.getBasicsHuntData();
    this.isShowBuyBtn();
    HuntModule.api.getTrain((data) => {
      this.initMain();
    });
  }

  private initMain() {
    this.initSpirit();
    this.initPrimitive();

    //this.primitiveTime();
  }

  private getBasicsBossData() {
    let c_huntBoss = JsonMgr.instance.jsonList.c_huntBoss;
    let list = Object.keys(c_huntBoss);
    this._basicsBossData = c_huntBoss[list[0]];
  }

  private getBasicsHuntData() {
    let c_hunt = JsonMgr.instance.jsonList.c_hunt;
    let list = Object.keys(c_hunt);
    this._basicsHuntData = c_hunt[list[0]];
  }

  /**灵兽入侵开始时间结束时间 */
  private initSpirit() {
    this.getNode("lab_spirit_noonTime").getComponent(Label).string =
      this._basicsHuntData.timeStart + ":00 --" + this._basicsHuntData.timeEnd + ":00";
  }

  /**洪荒开启关闭时间 */
  private initPrimitive() {
    this.getNode("lab_primitive_noonTime").getComponent(Label).string =
      this._basicsBossData.start1 + ":00 --" + this._basicsBossData.over1 + ":00";

    this.getNode("lab_primitive_nightTime").getComponent(Label).string =
      this._basicsBossData.start2 + ":00 --" + this._basicsBossData.over2 + ":00";
  }

  /**获取灵兽入侵是否是开启状态 */
  private getSpiritIsOpen() {
    let obj = {
      time: 0,
      state: false,
    };
    const timestamp = TimeUtils.serverTime;
    const date = new Date(timestamp);
    const hour = date.getHours();
    if (this._basicsHuntData.timeStart < hour && hour < this._basicsHuntData.timeEnd) {
      obj.state = true;
    }
    return obj;
  }

  /**设置灵兽入侵的状态 */
  private setSpiritIsOpen() {
    let obj = this.getSpiritIsOpen();
    if (this._spititState == obj.state) {
      return;
    }
    if (obj.state == true && this._spititState != obj.state) {
      this.getNode("btn_lab_spirit_open").active = true;
      this.getNode("btn_spr_spirit_open").active = true;
      this.getNode("lab_spirit_prepare").active = false;
    } else if (obj.state == false && this._spititState != obj.state) {
      this.getNode("btn_lab_spirit_open").active = false;
      this.getNode("btn_spr_spirit_open").active = false;
      this.getNode("lab_spirit_prepare").active = true;
    }
    this._spititState = obj.state;
  }

  /**获取洪荒开启状态 */
  private getPrimitiveIsOpen() {
    let obj = {
      time: 0,
      state: false,
    };

    // 如果为０表示未开启
    if (HuntModule.data.bossEndStamp == 0) {
      return obj;
    }

    if (HuntModule.data.bossStartStamp > TimeUtils.serverTime) {
      obj.state = false;
    } else {
      obj.state = true;
    }
    obj.time = HuntModule.data.bossEndStamp - TimeUtils.serverTime;

    return obj;
  }

  /**设置洪荒开启状态 */
  private setPrimitiveIsOpen() {
    let obj = this.getPrimitiveIsOpen();
    if (obj.state == true && this._primitiveState != obj.state) {
      this.getNode("lab_primitive_prepare").active = false;
      this.getNode("btn_lab_primitive_open").active = true;
      this.getNode("lab_primitive_endTime").active = true;
      this.getNode("noOpen").active = false;
      this.getNode("openAni").active = true;
      this.getNode("openAni").getComponent(sp.Skeleton).setAnimation(0, "animation1", true);
    } else if (obj.state == false && this._spititState != obj.state) {
      this.getNode("lab_primitive_prepare").active = true;
      this.getNode("btn_lab_primitive_open").active = false;
      this.getNode("lab_primitive_endTime").active = false;
      this.getNode("noOpen").active = true;
      this.getNode("openAni").active = false;
    }
    let time = TimeUtils.serverTime;
    this._primitiveState = obj.state;
    this.getNode("lab_primitive_endTime").getComponent(Label).string = TimeUtils.timeformatHMS(obj.time);
    const date = new Date(time);
    const hour = date.getHours();
    if (hour >= this._basicsBossData.over2) {
      this.getNode("lab_primitive_prepare").active = false;
      this.getNode("btn_lab_primitive_open").active = true;
      this.getNode("lab_primitive_endTime").active = true;
      this.getNode("lab_primitive_endTime").active = false;
    }
  }

  public tick(dt: any): void {
    this.setSpiritIsOpen();
    this.setPrimitiveIsOpen();
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 5 });
  }

  private on_click_btn_award() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击商店);
    UIMgr.instance.showDialog(HuntRouteName.UIHuntShop);
  }

  private on_click_btn_lab_primitive_open() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HUNT_洪荒出现)) {
      let obj = this.getPrimitiveIsOpen();
      if (obj.state == false) {
        TipsMgr.showTipX(182, [], "狩猎BOSS战场还未开启");
        return;
      }

      UIMgr.instance.showDialog(HuntRouteName.UIHuntPreparePrimitive);
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.HUNT_洪荒出现));
    }
  }

  private on_click_btn_rank() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击排行榜);
    UIMgr.instance.showDialog(HuntRouteName.UIHuntRank);
  }

  private on_click_btn_rankAward() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击奖励);
    UIMgr.instance.showDialog(HuntRouteName.UIHuntBossRank);
  }

  private on_click_btn_spr_spirit_open() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击灵兽入侵);
    this.openSpirit();
  }

  private on_click_btn_lab_spirit_open() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击灵兽入侵);
    this.openSpirit();
  }

  private openSpirit() {
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HUNT_灵兽入侵)) {
      UIMgr.instance.showDialog(HuntRouteName.UIHuntPrepareSpirit);
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.HUNT_灵兽入侵));
    }
  }

  private on_click_btn_summon() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HUNT_洪荒出现)) {
      UIMgr.instance.showDialog(HuntRouteName.UIHuntSummon);
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.HUNT_洪荒出现));
    }
  }

  private on_click_btn_primitive_open() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击洪荒出现);
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HUNT_洪荒出现)) {
      let obj = this.getPrimitiveIsOpen();
      if (obj.state == false) {
        TipsMgr.showTipX(182, [], "狩猎BOSS战场还未开启");
        return;
      }
      UIMgr.instance.showDialog(HuntRouteName.UIHuntPreparePrimitive);
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.HUNT_洪荒出现));
    }
  }

  private on_click_btn_spirit_open() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击灵兽入侵);
    this.openSpirit();
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_test1() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    HuntModule.api.testResetHunt();
  }

  private on_click_btn_test2() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    HuntModule.api.testSetStart();
  }

  private on_click_btn_test3() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    HuntModule.api.reviveBoss();
  }
}
