# cocos-creator-gg-hot-update

> -   查阅 [插件论坛讨论地址](https://forum.cocos.org/t/topic/161655)
> -   查阅 [插件官方文档](https://www.yuque.com/dhunterstudio/mydoid/qshphh)
> -   查阅 [插件官方 demo](https://github.com/zhitaocai/cocos-creator-gg-hot-update-demo)

## 一、介绍

**gg-hot-update/热更新插件是一款基于 Cocos Creator 官方的热更新原理，重新采用 DownloadTask 进行编写的热更新插件。**

相比起原来的 CocosCreator 官方的热更新方案，我们的热更新方案具有以下优势：

1. **支持主包/子包的热更新模式**
2. **支持跨版本热更新**
    - 比如存在版本 1.0.0、1.1.0、1.2.0、1.3.0，我们可以在 1.0.0 版本上，直接升级到 1.3.0
3. **支持每个包（主包/子包）的断线重连**
4. **提供一整套 API，实现上述所有功能**

同时，我们还提供了适配 **Cocos Creator 3.6+** 的插件，更方便生成热更新配置文件信息（project.manifest 和 version.manifest）：

1. 支持 **原生平台构建** 自动生成热更新配置文件
2. 支持 **构建后自动生成** 热更新配置文件
3. 支持 **命令行/CLI 生成** 热更新配置文件

## 二、插件安装预览

插件安装后，将会提供两个部分：

-   插件资产库：提供一整套运行时 API
-   插件构建插件：提供构建后自动生成热更包配置的能力

![](static/assets-preview.png)

## 三、插件构建预览

以 Android 平台为例，构建任务时，插件会自动在构建完毕时，根据插件的热更新构建配置文件，对原始输出目录进行处理，最终生成两份资源

-   本地资源目录（构建 apk 时会打包进 apk）
-   远程资源目录（差不多等于整包资源）

![](static/output-preview.png)

## 接入插件

1. 查阅 [插件官方文档](https://www.yuque.com/dhunterstudio/mydoid/qshphh)
2. 查阅 [插件官方 demo](https://github.com/zhitaocai/cocos-creator-gg-hot-update-demo)

## 联系作者

-   QQ: 554939014
-   邮箱: <EMAIL>
-   更多关于我的其他作品，可以[点击这里查看](https://store.cocos.com/app/search?name=saisam)

## 版权声明

-   该游戏的源代码可商业使用
-   商业授权范围仅限于在您自行开发的游戏作品中使用
-   不得进行任何形式的转售、租赁、传播等

## 购买须知

-   本产品为付费虚拟商品，一经购买成功概不退款，请在购买谨慎确认购买内容。
