import { _decorator, instantiate, Label, Node, Sprite } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import ResMgr from "../../../../../lib/common/ResMgr";
import { PupilMessage } from "../../../../../game/net/protocol/Pupil";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import ToolExt from "../../../../../game/common/ToolExt";
import Formate from "../../../../../lib/utils/Formate";
import { UITransform } from "cc";
import { PupilAni } from "./PupilAni";
import { PupilModule } from "../../PupilModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PupilAudioName } from "../../PupilConstant";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIPupilMarryResPop")
export class UIPupilMarryResPop extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilMarryResPop`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private pupilMsg: PupilMessage = null;

  public init(param) {
    super.init(param);
    this.pupilMsg = param["pupilMsg"].pupilMessage;
    log.log("init", this.pupilMsg);
  }

  protected onEvtShow(): void {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.结伴成功音乐);
    let config_0 = PupilModule.data.getConfigPupil(this.pupilMsg.ownInfo.talentId);
    let config_1 = PupilModule.data.getConfigPupil(this.pupilMsg.partnerInfo.talentId);

    this.getNode("player_name_lab").getComponent(Label).string = `归属：${this.pupilMsg.partnerInfo.userName}`;
    // 弟子姓名
    PupilModule.service.setPupilNameNode(this.getNode("pupil_name_lab_0"), this.pupilMsg.ownInfo.nameId);
    PupilModule.service.setPupilNameNode(this.getNode("pupil_name_lab_1"), this.pupilMsg.partnerInfo.nameId);

    // 形象
    this.getNode("PupilAni1")
      .getComponent(PupilAni)
      .setAniByNameId(this.pupilMsg.ownInfo.nameId, this.pupilMsg.ownInfo.adultAttrList.length);
    this.getNode("PupilAni2")
      .getComponent(PupilAni)
      .setAniByNameId(this.pupilMsg.partnerInfo.nameId, this.pupilMsg.ownInfo.adultAttrList.length);

    // 奖励图标&数量
    FmUtils.setItemIcon(this.getNode("bg_item_reward0"), config_0.rewardList[0]);
    FmUtils.setItemIcon(this.getNode("bg_item_reward1"), config_1.rewardList[0]);
    this.getNode("bg_item_reward0").getComponentInChildren(Label).string = `+${config_0.rewardList[1]}`;
    this.getNode("bg_item_reward1").getComponentInChildren(Label).string = `+${config_1.rewardList[1]}`;

    // 攻血防
    let left_base_attr = ToolExt.traAwardItemMapList(this.pupilMsg.ownInfo.basicAttrList);
    let right_base_attr = ToolExt.traAwardItemMapList(this.pupilMsg.partnerInfo.basicAttrList);
    this.getNode("base_attr_lay_0").removeAllChildren();
    this.getNode("base_attr_lay_1").removeAllChildren();
    for (let i = 0; i < 3; i++) {
      let base_attr_lab_0 = ToolExt.clone(this.getNode("base_attr_lab"), this);
      let base_attr_lab_1 = ToolExt.clone(this.getNode("base_attr_lab"), this);
      base_attr_lab_0.active = true;
      base_attr_lab_1.active = true;
      this.getNode("base_attr_lay_0").addChild(base_attr_lab_0);
      PupilModule.service.setPupilBaseAttrNode(base_attr_lab_0, left_base_attr[i]);
      this.getNode("base_attr_lay_1").addChild(base_attr_lab_1);
      PupilModule.service.setPupilBaseAttrNode(base_attr_lab_1, right_base_attr[i]);
    }

    // 出师属性
    let left_adult_attr = ToolExt.traAwardItemMapList(this.pupilMsg.ownInfo.adultAttrList);
    let right_adult_attr = ToolExt.traAwardItemMapList(this.pupilMsg.partnerInfo.adultAttrList);
    PupilModule.service.setPupilAttrNode(this.getNode("attr_bg_0").getChildByName("Label"), left_adult_attr[0]);
    PupilModule.service.setPupilAttrNode(this.getNode("attr_bg_1").getChildByName("Label"), right_adult_attr[0]);
    ResMgr.loadSpriteFrame(
      `${BundleEnum.BUNDLE_G_PUPIL}?patterns/pupilIcon`,
      `dz_shuxing_1_${config_0.color}`,
      this.getNode("attr_bg_0").getComponent(Sprite)
    );
    ResMgr.loadSpriteFrame(
      `${BundleEnum.BUNDLE_G_PUPIL}?patterns/pupilIcon`,
      `dz_shuxing_1_${config_1.color}`,
      this.getNode("attr_bg_1").getComponent(Sprite)
    );

    // 天生属性
    this.setInitAttr(this.getNode("init_attr_lay_0"), this.pupilMsg.ownInfo.initAttrList);
    this.setInitAttr(this.getNode("init_attr_lay_1"), this.pupilMsg.partnerInfo.initAttrList);
  }

  private setInitAttr(node: Node, attrList: number[]) {
    node.children.forEach((child) => {
      child.active = false;
    });

    for (let i = 0; i < attrList.length; i += 2) {
      let nodeItem = node.children[i / 2];
      if (!nodeItem) {
        nodeItem = instantiate(node.children[0]);
        node.addChild(nodeItem);
      }
      nodeItem.active = true;
      nodeItem.getComponentInChildren(Label).string = Formate.formatAttribute(attrList[i], attrList[i + 1]);
    }

    if (attrList.length == 2) {
      node.getComponent(UITransform).setContentSize(152, 0);
    } else {
      node.getComponent(UITransform).setContentSize(304, 0);
    }
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
