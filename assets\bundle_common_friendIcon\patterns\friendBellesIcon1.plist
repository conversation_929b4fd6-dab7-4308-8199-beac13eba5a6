<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>bg_meimingchenghao_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{241,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{1555,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{1701,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{1847,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{387,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{533,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{679,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{825,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{971,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{1117,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{1263,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingchenghao_9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,226}</string>
                <key>spriteSourceSize</key>
                <string>{144,226}</string>
                <key>textureRect</key>
                <string>{{1409,1},{144,226}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_meimingdi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{238,233}</string>
                <key>spriteSourceSize</key>
                <string>{238,233}</string>
                <key>textureRect</key>
                <string>{{1,1},{238,233}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>friendBellesIcon1.png</string>
            <key>size</key>
            <string>{1992,235}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:dcc6af694e8f75f53b6e0085893cdcdc:afcc898e89bb9103c3f324fe27106f8b:6ea9c85392033735fb809500e4d5e3ed$</string>
            <key>textureFileName</key>
            <string>friendBellesIcon1.png</string>
        </dict>
    </dict>
</plist>
