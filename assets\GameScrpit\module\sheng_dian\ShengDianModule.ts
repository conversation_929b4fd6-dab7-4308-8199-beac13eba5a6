import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { ShengDianApi } from "./ShengDianApi";
import { ShengDianConfig } from "./ShengDianConfig";
import { ShengDianData } from "./ShengDianData";
import { ShengDianRoute } from "./ShengDianRoute";
import { ShengDianService } from "./ShengDianService";
import { ShengDianSubscriber } from "./ShengDianSubscriber";
import { ShengDianViewModel } from "./ShengDianViewModel";

export class ShengDianModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): ShengDianModule {
    if (!GameData.instance.ShengDianModule) {
      GameData.instance.ShengDianModule = new ShengDianModule();
    }
    return GameData.instance.ShengDianModule;
  }
  private _data = new ShengDianData();
  private _api = new ShengDianApi();
  private _service = new ShengDianService();
  private _subscriber = new ShengDianSubscriber();
  private _route = new ShengDianRoute();
  private _viewModel = new ShengDianViewModel();
  private _config = new ShengDianConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new ShengDianData();
    this._api = new ShengDianApi();
    this._service = new ShengDianService();
    this._subscriber = new ShengDianSubscriber();
    this._route = new ShengDianRoute();
    this._viewModel = new ShengDianViewModel();
    this._config = new ShengDianConfig();
    // 初始化模块回调
    this._api.templeInfo(() => {
      completedCallback && completedCallback();
    });
    // 模块初始化
    this._subscriber.register();
    this._route.init();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
