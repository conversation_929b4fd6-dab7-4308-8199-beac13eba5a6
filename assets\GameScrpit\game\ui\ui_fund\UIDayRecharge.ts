import { _decorator, instantiate, is<PERSON>alid, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { DayActivityModule } from "../../../module/day/DayActivityModule";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { dtTime } from "../../BoutStartUp";
import ResMgr from "../../../lib/common/ResMgr";
import { DayMain } from "./day_recharge/DayMain";
import { Sleep } from "../../GameDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { ActivityAudioName } from "../../../module/activity/ActivityConfig";
const { ccclass, property } = _decorator;

const avId1: number = 10701;

const db_info: string = "db_info";

enum Main_Type {
  type1 = 1,
  type2 = 2,
}

@ccclass("UIDayRecharge")
export class UIDayRecharge extends UINode {
  protected _openAct: boolean = true; //打开动作

  private _main_map: Map<Main_Type, Node> = new Map<Main_Type, Node>();
  private _main_load: Map<Main_Type, boolean> = new Map<Main_Type, boolean>();
  private _main_get_Ticker: Map<Main_Type, number> = new Map<Main_Type, number>();
  private _curMain_type: Main_Type = Main_Type.type1;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FUND}?prefab/ui/UIDayRecharge`;
  }

  protected onEvtShow() {
    this.startMain();
    this.set_lbl_cd();
  }

  private async startMain() {
    await Sleep(0.3);
    if (isValid(this.node) == false) return;

    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_type1"),
      BadgeType.UITerritory.btn_chongzhihaoli.FundBanner001.btn_type1.id
    );

    this.initMain();
  }

  private async initMain() {
    switch (this._curMain_type) {
      case Main_Type.type1:
        this.init_type1_main();
        break;
      case Main_Type.type2:
        this.init_type2_main();
        break;
      default:
        break;
    }

    this.change_btn();
  }

  private change_btn() {
    this.getNode("lay_bottom_btn").children.forEach((val) => {
      val.getChildByName("sprNo_pich").active = true;
      val.getChildByName("lblNo_pich").active = true;

      val.getChildByName("sprPich").active = false;
      val.getChildByName("lblPich").active = false;
    });

    let node = null;
    switch (this._curMain_type) {
      case Main_Type.type1:
        node = this.getNode("btn_type1");
        break;

      case Main_Type.type2:
        node = this.getNode("btn_type2");
        break;
    }
    if (node == null) return;

    node.getChildByName("sprNo_pich").active = false;
    node.getChildByName("lblNo_pich").active = false;

    node.getChildByName("sprPich").active = true;
    node.getChildByName("lblPich").active = true;
  }

  private init_type1_main() {
    this._main_map.forEach((val, key) => {
      val.active = false;
    });
    let main: Node = null;

    if (this._main_map.has(Main_Type.type1)) {
      main = this._main_map.get(Main_Type.type1);
      main.active = true;
    } else if (this._main_load.has(Main_Type.type1)) {
      this._main_get_Ticker.forEach((val) => {
        TickerMgr.clearInterval(val);
      });

      let id = TickerMgr.setInterval(
        dtTime,
        () => {
          if (this._main_map.has(Main_Type.type1)) {
            main = this._main_map.get(Main_Type.type1);
            if (this._curMain_type == Main_Type.type1) {
              main.active = true;
              TickerMgr.clearInterval(id);
            }
          }
        },
        true
      );
      this._main_get_Ticker.set(Main_Type.type1, id);
    } else {
      this._main_load.set(Main_Type.type1, true);
      ResMgr.loadPrefab(
        `${BundleEnum.BUNDLE_HD_FUND}?prefab/day_recharge/main_type1`,
        (prefab) => {
          if (isValid(this.node) == false) return;

          let main: Node = instantiate(prefab);
          this.getNode("main_list").addChild(main);
          main.walk((child) => (child.layer = this.node.layer));
          this._main_map.set(Main_Type.type1, main);
          if (this._curMain_type == Main_Type.type1) {
            main.active = true;
          }
        },
        this
      );
    }
  }

  private init_type2_main() {
    this._main_map.forEach((val, key) => {
      val.active = false;
    });
    let main: Node = null;

    if (this._main_map.has(Main_Type.type2)) {
      main = this._main_map.get(Main_Type.type2);
      main.active = true;
    } else if (this._main_load.has(Main_Type.type2)) {
      this._main_get_Ticker.forEach((val) => {
        TickerMgr.clearInterval(val);
      });

      let id = TickerMgr.setInterval(
        dtTime,
        () => {
          if (this._main_map.has(Main_Type.type2)) {
            main = this._main_map.get(Main_Type.type2);
            if (this._curMain_type == Main_Type.type2) {
              main.active = true;
              TickerMgr.clearInterval(id);
            }
          }
        },
        true
      );
      this._main_get_Ticker.set(Main_Type.type2, id);
    } else {
      this._main_load.set(Main_Type.type2, true);
      ResMgr.loadPrefab(
        `${BundleEnum.BUNDLE_HD_FUND}?prefab/day_recharge/main_type2`,
        (prefab) => {
          if (isValid(this.node) == false) return;

          let main: Node = instantiate(prefab);
          this.getNode("main_list").addChild(main);
          main.walk((child) => (child.layer = this.node.layer));
          this._main_map.set(Main_Type.type2, main);
          if (this._curMain_type == Main_Type.type2) {
            main.active = true;
          }
        },
        this
      );
    }
  }

  private on_click_btn_type1() {
    AudioMgr.instance.playEffect(ActivityAudioName.Effect.点击每日礼包下方页签);

    if (this._curMain_type == Main_Type.type1) return;
    this._curMain_type = Main_Type.type1;
    this.initMain();
  }

  private on_click_btn_type2() {
    AudioMgr.instance.playEffect(ActivityAudioName.Effect.点击每日礼包下方页签);

    if (this._curMain_type == Main_Type.type2) return;
    this._curMain_type = Main_Type.type2;
    this.initMain();
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    this._main_map.forEach((val) => {
      val.getComponent(DayMain).onRemove();
    });
    this._main_get_Ticker.forEach((val) => {
      TickerMgr.clearInterval(val);
    });
  }

  private set_lbl_cd() {
    FmUtils.setCd(this.getNode("lbl_time"), DayActivityModule.data.dayMessage.resetTime);
  }
}
