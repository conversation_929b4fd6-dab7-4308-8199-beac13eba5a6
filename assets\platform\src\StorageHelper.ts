import { sys } from "cc";

/**
 * 类型
 */
export enum StorageKeyEnum {
  Version = "Version",
  SelectServer = "SelectServer",
  LoginName = "LoginName",
  Psd = "Psd",
  SystemOpenCondition = "SystemOpenCondition",
  WaitSystemOpenPlayList = "WaitSystemOpenPlayList",
  ShouChong = "ShouChong",
  GameSpeed = "GameSpeed",
  TiaoJianLiBao_DAYMARK = "TiaoJianLiBao_DAYMARK",
  ActivityKey = "ActivityKey",

  BargainStartAni = "BargainStartAni",

  游历10次 = "youli_10ci",

  弟子自动培养 = "dizhi_auto_train",

  山海探险10次 = "sonhai10",

  十连招募 = "shi_li_zhu_mai",
}

/**
 * 本地localStorage缓存统一读写封装
 */
export default class StorageMgr {
  /**用户唯一标识 */
  public static userId: string = "";

  public static remove(key: string) {
    sys.localStorage.removeItem(`${StorageMgr.userId}${key}`);
  }

  public static saveItem(key: string, val: string) {
    if (val === null || val === undefined) val = "";
    sys.localStorage.setItem(`${StorageMgr.userId}${key}`, val);
  }

  // 读取字符串
  public static loadStr(key: string) {
    let val = sys.localStorage.getItem(`${StorageMgr.userId}${key}`);
    if (val === null || val === undefined) val = "";
    return val;
  }

  // 读取数字
  public static loadNum(key: string, defaultVal: number = 0) {
    return parseInt(StorageMgr.loadStr(key)) || defaultVal;
  }
}
