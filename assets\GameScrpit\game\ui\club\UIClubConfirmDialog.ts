import { _decorator, RichText } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu Aug 15 2024 20:17:05 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubConfirmDialog.ts
 *
 */

export class UIClubConfirmDialogArgs {
  title: string;
  content: string;
  callback: Function;
  isBtnCancel: boolean = true;
  isClose: boolean = true;
}
@ccclass("UIClubConfirmDialog")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_CLUB,
  url: "prefab/ui/UIClubConfirmDialog",
  nextHop: [],
  exit: "",
})
export class UIClubConfirmDialog extends BaseCtrl {
  public playShowAni: boolean = true;
  dialog_args: UIClubConfirmDialogArgs = new UIClubConfirmDialogArgs();

  //=================================================
  public init(args: RouteShowArgs): void {
    super.init(args);
    this.dialog_args.title = args.title;
    this.dialog_args.content = args.payload.content;
    this.dialog_args.callback = args.payload.callback;
    this.dialog_args.isBtnCancel = args.payload.isBtnCancel ?? this.dialog_args.isBtnCancel;
    this.dialog_args.isClose = args.payload.isClose ?? this.dialog_args.isClose;
  }
  protected start(): void {
    super.start();
    AudioMgr.instance.playEffect(AudioName.Effect.二级弹窗提示);
    this.getNode("confirm_content").getComponent(RichText).string = `${this.dialog_args.content}`;
    this.getNode("btn_cancel").active = this.dialog_args.isBtnCancel;
    this.getNode("dialog_close").active = this.dialog_args.isClose;
  }
  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    this.closeBack();
  }
  private on_click_btn_confirm() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.closeBack();
    if (this.dialog_args.callback) {
      this.dialog_args.callback(true);
    }
  }
  private on_click_btn_cancel() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.closeBack();
    if (this.dialog_args.callback) {
      this.dialog_args.callback(false);
    }
  }
}
