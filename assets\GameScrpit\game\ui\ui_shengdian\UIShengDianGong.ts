import { _decorator, Label, Node, RichText } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { ShengDianModule } from "../../../module/sheng_dian/ShengDianModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ToolExt from "../../common/ToolExt";
import { PetModule } from "../../../module/pet/PetModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Wed Jan 15 2025 15:29:32 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_shengdian/UIShengDianGong.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIShengDianGong")
export class UIShengDianGong extends UINode {
  private _templeId: number = 0;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SHENGDIAN}?prefab/ui/UIShengDianGong`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
    this._templeId = args.templeId;
  }
  protected onEvtShow(): void {
    // do something
    let temple = JsonMgr.instance.jsonList.c_temple[this._templeId];
    this.getNode("lbl_title").getComponent(Label).string = `${temple.name}`;
    this.getNode("lbl_back").getComponent(Label).string = `${temple.name}`;
    let templeMesage = ShengDianModule.data.templeMessage;
    log.log(templeMesage);

    if (templeMesage.placeMap[this._templeId]) {
      this.getNode("lbl_role_name").getComponent(Label).string =
        templeMesage.placeMap[this._templeId]?.simpleMessage.nickname;
      let leaderConfig = PlayerModule.data.getConfigLeaderData(
        templeMesage.placeMap[this._templeId]?.simpleMessage.level
      );
      this.getNode("rich_role_level").getComponent(RichText).string = `${leaderConfig.jingjie2}`;
      this.getNode("lbl_role_server").getComponent(Label).string = `${
        templeMesage.placeMap[this._templeId]?.simpleMessage.serverName
      }`;

      let roleSkinId = templeMesage.placeMap[this._templeId].simpleMessage.avatarList[0];
      let horseId = templeMesage.placeMap[this._templeId].horseMessage.horseId;
      ToolExt.loadUIRole(this.getNode("node_role"), roleSkinId, horseId, "renderScale16", this);
      let heroList = templeMesage.placeMap[this._templeId].heroList;
      for (let i = 0; i < heroList.length; i++) {
        let heroSkinId = JsonMgr.instance.jsonList.c_hero[heroList[i].heroId].skinId;
        ToolExt.loadUIRole(this.getNode("node_hero" + (i + 1)), heroSkinId, 0, "renderScale17", this);
      }
      let petList = templeMesage.placeMap[this._templeId].petList;
      for (let i = 0; i < petList.length; i++) {
        let skinId = petList[i].chosenSkinId;
        PetModule.service.setPetSkin(skinId, this.getNode("node_pet" + (i + 1)), "renderScale2");
      }

      this.getNode("node_tips").active = false;
      this.getNode("node_player_info").active = true;
    } else {
      this.getNode("node_tips").active = true;
      this.getNode("node_player_info").active = false;
    }
    if (temple.titleId) {
      PlayerModule.service.createTitle(this.getNode("node_role_title"), temple.titleId, (node: Node) => {
        node.walk((child) => {
          child.layer = this.getNode("node_role_title").layer;
        });
      });
    }
  }

  private on_click_btn_back() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
