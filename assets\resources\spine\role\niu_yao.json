{"skeleton": {"hash": "CCcLuFo20r+3wN0k4+zir1jKQTc=", "spine": "3.8.75", "x": -194.39, "y": -8.04, "width": 358.08, "height": 345.45, "images": "./images/", "audio": "D:/spine导出/S_时空裂隙_怪/牛妖"}, "bones": [{"name": "root"}, {"name": "bone40", "parent": "root", "length": 110.33, "rotation": -0.19, "x": -2.23, "y": -11.07}, {"name": "bone", "parent": "bone40", "length": 66.53, "rotation": -0.39, "x": 8.52, "y": 110.99}, {"name": "bone2", "parent": "bone", "length": 41.59, "rotation": 100.35, "x": -1.67, "y": -1.36}, {"name": "bone3", "parent": "bone2", "length": 38.49, "rotation": 2.33, "x": 41.59, "color": "13ff00ff"}, {"name": "bone4", "parent": "bone3", "length": 20.14, "rotation": -74.25, "x": 38.49}, {"name": "bone5", "parent": "bone4", "x": 32.86, "y": -22.68, "color": "ffd400ff"}, {"name": "bone6", "parent": "bone4", "x": 40.18, "y": -4.51, "color": "ffd400ff"}, {"name": "bone7", "parent": "bone4", "x": 62.98, "y": 53.75, "color": "ffd400ff"}, {"name": "bone8", "parent": "bone4", "x": -17.57, "y": 74.64, "color": "ffd400ff"}, {"name": "bone9", "parent": "bone4", "x": -22.9, "y": 44.4, "color": "ffd400ff"}, {"name": "bone10", "parent": "bone4", "x": -45.76, "y": 37.1, "color": "ffd400ff"}, {"name": "bone11", "parent": "bone4", "x": -18.56, "y": 10.19, "color": "ffd400ff"}, {"name": "bone12", "parent": "bone4", "x": -14.46, "y": -7.94, "color": "ffd400ff"}, {"name": "bone13", "parent": "bone4", "x": 3.95, "y": -45.03, "color": "ffd400ff"}, {"name": "bone14", "parent": "bone4", "x": 49.62, "y": -41.29, "color": "ffeb00ff"}, {"name": "niuyao_06", "parent": "bone3", "x": 57.78, "y": 57.75, "color": "13ff00ff"}, {"name": "bone15", "parent": "bone3", "x": 24.96, "y": -41.79, "color": "13ff00ff"}, {"name": "niuyao_6", "parent": "niuyao_06", "length": 61.81, "rotation": 121.91, "x": -7.11, "y": 6.6, "color": "13ff00ff"}, {"name": "niuyao_7", "parent": "niuyao_6", "length": 52.89, "rotation": 43.11, "x": 61.81, "color": "13ff00ff"}, {"name": "niuyao_8", "parent": "niuyao_7", "length": 26.61, "rotation": 12.75, "x": 52.89, "color": "13ff00ff"}, {"name": "bone16", "parent": "bone15", "length": 56.3, "rotation": -166.04, "x": -4.48, "y": -1.03, "color": "13ff00ff"}, {"name": "bone17", "parent": "bone16", "length": 55.38, "rotation": 102.56, "x": 56.05, "y": 0.12, "color": "13ff00ff"}, {"name": "bone18", "parent": "bone17", "length": 28.27, "rotation": 14.06, "x": 55.38, "color": "13ff00ff"}, {"name": "bone19", "parent": "bone18", "length": 47.38, "rotation": 152.83, "x": 23.06, "y": 4.51, "transform": "noRotationOrReflection", "color": "13ff00ff"}, {"name": "bone20", "parent": "bone", "length": 13.6, "rotation": -124.56, "x": -33.97, "y": -7.05}, {"name": "bone21", "parent": "bone20", "length": 17.49, "rotation": -7.96, "x": 13.6}, {"name": "bone22", "parent": "bone", "length": 13.7, "rotation": -46.86, "x": 20.81, "y": -4.65}, {"name": "bone23", "parent": "bone22", "length": 18.71, "rotation": 10.95, "x": 13.86, "y": -0.14}, {"name": "bone24", "parent": "bone", "length": 19.42, "rotation": -72.14, "x": 6.66, "y": -10.97}, {"name": "bone25", "parent": "bone24", "length": 19.38, "rotation": -19.72, "x": 19.42}, {"name": "bone26", "parent": "bone", "length": 19.5, "rotation": -96.1, "x": -12.87, "y": -15.9}, {"name": "bone27", "parent": "bone26", "length": 15.35, "rotation": 18.29, "x": 19.5}, {"name": "bone28", "parent": "bone27", "length": 20.54, "rotation": 9.55, "x": 15.35}, {"name": "bone29", "parent": "bone", "length": 62.24, "rotation": -152.72, "x": -23.04, "y": -12.49}, {"name": "bone30", "parent": "bone29", "length": 68.28, "rotation": -47.82, "x": 61.34, "y": -0.3}, {"name": "bone31", "parent": "bone30", "length": 50.48, "rotation": -29.03, "x": 68.59, "y": 0.12}, {"name": "bone32", "parent": "bone31", "length": 21.7, "rotation": 103.99, "x": 50.48}, {"name": "bone33", "parent": "bone32", "length": 19.09, "rotation": 56.83, "x": 21.7}, {"name": "bone34", "parent": "bone", "length": 42.85, "rotation": -62.45, "x": 19.81, "y": -15.79, "color": "6fff00ff"}, {"name": "bone35", "parent": "bone34", "length": 38.49, "rotation": -67.21, "x": 42.65, "y": -0.4, "color": "6fff00ff"}, {"name": "bone36", "parent": "bone35", "length": 27.75, "rotation": 99.91, "x": 38.31, "y": -0.15, "color": "6fff00ff"}, {"name": "bone37", "parent": "bone", "length": 38.06, "rotation": -126.7, "x": -31.22, "y": -20.82, "color": "6fff00ff"}, {"name": "bone38", "parent": "bone37", "length": 46.1, "rotation": 62.84, "x": 38.06, "color": "6fff00ff"}, {"name": "bone39", "parent": "bone38", "length": 22.42, "rotation": -100.96, "x": 46.1, "color": "6fff00ff"}, {"name": "target1", "parent": "bone40", "rotation": 0.19, "x": 22.59, "y": 27.59, "color": "ff3f00ff"}, {"name": "target3", "parent": "bone40", "rotation": 0.19, "x": -25.74, "y": 19.26, "color": "ff3f00ff"}, {"name": "bone41", "parent": "bone3", "x": 90.03, "y": 55.75, "color": "13ff00ff"}, {"name": "bone42", "parent": "bone3", "x": 78.04, "y": 82.29, "color": "13ff00ff"}, {"name": "bone43", "parent": "bone3", "x": 78.36, "y": 106.19, "color": "13ff00ff"}, {"name": "bone44", "parent": "root", "length": 47.38, "rotation": -154.5, "x": -337.19, "y": 461.58, "color": "13ff00ff"}, {"name": "dg1all", "parent": "root", "length": 151.37, "rotation": -2.16, "x": -55.92, "y": -21.53}, {"name": "dg1_1", "parent": "dg1all", "x": 121.92, "y": 175.39, "scaleX": 1.7625, "scaleY": 1.7625}, {"name": "dg2all", "parent": "root", "length": 235.58, "x": -59.78, "y": -78.83}, {"name": "dg2_1", "parent": "dg2all", "x": 4.97, "y": 337.96, "scaleX": 2.3799, "scaleY": 3.3972}, {"name": "ci1", "parent": "dg2all", "x": 410.35, "y": 160.42, "scaleX": 2.5759, "scaleY": 2.5759}, {"name": "dg2all2", "parent": "dg2all", "rotation": 41.72, "x": 295.4, "y": 85.7, "scaleX": 2.4539, "scaleY": 2.4539}, {"name": "dg2all3", "parent": "dg2all", "rotation": 41.72, "x": 332.48, "y": 93.75, "scaleX": 2.5105, "scaleY": 2.5105}, {"name": "dg2all4", "parent": "dg2all", "rotation": 41.72, "x": 365.2, "y": 94.55, "scaleX": 2.7036, "scaleY": 2.7036}, {"name": "dg2all5", "parent": "dg2all", "rotation": 41.72, "x": 400.38, "y": 107.37, "scaleX": 3.0681, "scaleY": 3.0681}, {"name": "dg2all6", "parent": "dg2all", "x": 313.71, "y": 73.59, "scaleX": 0.6493, "scaleY": 0.8985}, {"name": "dg2all7", "parent": "dg2all", "rotation": 21.64, "x": 314.61, "y": 74.6}, {"name": "dg2all8", "parent": "dg2all7", "scaleX": 1.7067, "scaleY": 1.7067}, {"name": "dg2all9", "parent": "dg2all", "rotation": 21.64, "x": 352.41, "y": 58.28}, {"name": "dg2all10", "parent": "dg2all9", "scaleX": 1.7067, "scaleY": 1.7067}, {"name": "dg2all11", "parent": "dg2all", "rotation": -37.7, "x": 402.47, "y": 94.84, "scaleX": -1}, {"name": "dg2all12", "parent": "dg2all11", "scaleX": 1.7067, "scaleY": 1.7067}, {"name": "target2", "parent": "target3", "x": -21.92, "y": -5.87, "color": "ff3f00ff"}, {"name": "target4", "parent": "target1", "x": 24.07, "y": -13.77, "color": "ff3f00ff"}], "slots": [{"name": "niuyao_01", "bone": "bone19", "attachment": "niuyao_01"}, {"name": "niuyao_1", "bone": "bone44"}, {"name": "niuyao_02", "bone": "root", "attachment": "niuyao_02"}, {"name": "niuyao_03", "bone": "root", "attachment": "niuyao_03"}, {"name": "niuyao_04", "bone": "root", "attachment": "niuyao_04"}, {"name": "niuyao_05", "bone": "root", "attachment": "niuyao_05"}, {"name": "niuyao_06", "bone": "niuyao_06", "attachment": "niuyao_06"}, {"name": "niuyao_07", "bone": "root", "attachment": "niuyao_07"}, {"name": "niuyao_08", "bone": "root", "attachment": "niuyao_08"}, {"name": "niuyao_09", "bone": "root", "attachment": "niuyao_09"}, {"name": "niuyao_010", "bone": "root", "attachment": "niuyao_010"}, {"name": "niuyao_011", "bone": "root", "attachment": "niuyao_011"}, {"name": "niuyao_012", "bone": "root", "attachment": "niuyao_012"}, {"name": "niuyao_013", "bone": "root", "attachment": "niuyao_013"}, {"name": "dg1/dg_0001", "bone": "dg1_1"}, {"name": "dg2/qxdg02_000", "bone": "dg2_1"}, {"name": "dg2effect/xin_skill1/ci1/ci_0001", "bone": "ci1"}, {"name": "dg2effect/huo/huoyan-1", "bone": "dg2all2"}, {"name": "dg2effect/huo/huoyan-2", "bone": "dg2all3"}, {"name": "dg2effect/huo/huoyan-3", "bone": "dg2all4"}, {"name": "dg2effect/huo/huoyan-4", "bone": "dg2all5"}, {"name": "dg2effect/xin_skill1/dilie", "bone": "dg2all6", "color": "ffffffb1"}, {"name": "dg2effect/xin_skill1/2", "bone": "dg2all8"}, {"name": "dg2effect/xin_skill1/4", "bone": "dg2all12"}, {"name": "dg2effect/xin_skill1/3", "bone": "dg2all10"}], "ik": [{"name": "target1", "bones": ["bone34", "bone35"], "target": "target1", "bendPositive": false}, {"name": "target2", "order": 2, "bones": ["bone39"], "target": "target2"}, {"name": "target3", "order": 1, "bones": ["bone37", "bone38"], "target": "target3"}, {"name": "target4", "order": 3, "bones": ["bone36"], "target": "target4"}], "transform": [{"name": "face", "order": 4, "bones": ["bone6"], "target": "bone5", "x": 7.32, "y": 18.17, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}], "skins": [{"name": "default", "attachments": {"dg2effect/huo/huoyan-1": {"dg2effect/huo/huoyan-1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}}, "dg2effect/huo/huoyan-2": {"dg2effect/huo/huoyan-1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}}, "dg2effect/huo/huoyan-3": {"dg2effect/huo/huoyan-1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}}, "dg2effect/huo/huoyan-4": {"dg2effect/huo/huoyan-1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}, "dg2effect/huo/huoyan-9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46, -40, -45, -40, -45, 41, 46, 41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 81}}, "dg2effect/xin_skill1/dilie": {"dg2effect/xin_skill1/dilie": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [217.96, -36.27, -157.04, -36.27, -157.04, 15.73, 217.96, 15.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 375, "height": 52}}, "dg2effect/xin_skill1/2": {"dg2effect/xin_skill1/02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [6.65, -10.08, -10.35, -10.08, -10.35, 11.92, 6.65, 11.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 22}}, "dg2effect/xin_skill1/3": {"dg2effect/xin_skill1/02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [6.65, -10.08, -10.35, -10.08, -10.35, 11.92, 6.65, 11.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 22}}, "dg2effect/xin_skill1/4": {"dg2effect/xin_skill1/02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [6.65, -10.08, -10.35, -10.08, -10.35, 11.92, 6.65, 11.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 22}}, "dg2effect/xin_skill1/ci1/ci_0001": {"dg2effect/xin_skill1/ci1/ci_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -70, -119, -70, -119, 70, 119, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 140}, "dg2effect/xin_skill1/ci1/ci_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -70, -119, -70, -119, 70, 119, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 140}, "dg2effect/xin_skill1/ci1/ci_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -70, -119, -70, -119, 70, 119, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 140}, "dg2effect/xin_skill1/ci1/ci_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -70, -119, -70, -119, 70, 119, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 140}, "dg2effect/xin_skill1/ci1/ci_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -70, -119, -70, -119, 70, 119, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 140}, "dg2effect/xin_skill1/ci1/ci_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -70, -119, -70, -119, 70, 119, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 140}, "dg2effect/xin_skill1/ci1/ci_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -70, -119, -70, -119, 70, 119, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 140}, "dg2effect/xin_skill1/ci1/ci_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -70, -119, -70, -119, 70, 119, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 140}}, "dg2/qxdg02_000": {"dg2/qxdg02_000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [185, -107, -185, -107, -185, 107, 185, 107], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 370, "height": 214}, "dg2/qxdg02_001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [185, -107, -185, -107, -185, 107, 185, 107], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 370, "height": 214}, "dg2/qxdg02_002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [185, -107, -185, -107, -185, 107, 185, 107], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 370, "height": 214}, "dg2/qxdg02_003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [185, -107, -185, -107, -185, 107, 185, 107], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 370, "height": 214}, "dg2/qxdg02_007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [185, -107, -185, -107, -185, 107, 185, 107], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 370, "height": 214}, "dg2/qxdg02_009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [185, -107, -185, -107, -185, 107, 185, 107], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 370, "height": 214}, "dg2/qxdg02_011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [185, -107, -185, -107, -185, 107, 185, 107], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 370, "height": 214}}, "niuyao_010": {"niuyao_010": {"type": "mesh", "uvs": [0.09588, 0.20203, 0.15746, 0.26407, 0.18754, 0.37468, 0.27776, 0.40435, 0.37944, 0.40975, 0.43958, 0.41245, 0.48112, 0.27487, 0.56131, 0.16156, 0.66013, 0.1211, 0.75751, 0.15077, 0.82911, 0.24249, 0.82482, 0.13459, 0.83484, 0.02398, 0.89929, 0.01589, 0.96946, 0.09412, 1, 0.2344, 0.99524, 0.38547, 0.9666, 0.53384, 0.91074, 0.5824, 0.89356, 0.79012, 0.83055, 0.925, 0.73316, 1, 0.61, 1, 0.51692, 0.91152, 0.4582, 0.77663, 0.39233, 0.78203, 0.28921, 0.77933, 0.1904, 0.74426, 0.11163, 0.70379, 0.05722, 0.62286, 0.01568, 0.4664, 0.00852, 0.30994, 0.04862, 0.2371, 0.0701, 0.38817, 0.14028, 0.53654, 0.24196, 0.57161, 0.35366, 0.5851, 0.44818, 0.59049, 0.89786, 0.12649, 0.93366, 0.24519, 0.92507, 0.3504, 0.8864, 0.41245, 0.55558, 0.5878, 0.68591, 0.5851, 0.80907, 0.51496, 0.66299, 0.3477, 0.70882, 0.83059, 0.59282, 0.76854, 0.79618, 0.72268, 0.76181, 0.37738, 0.58566, 0.42324], "triangles": [10, 11, 38, 29, 33, 34, 39, 14, 15, 29, 30, 33, 30, 31, 33, 38, 13, 14, 11, 12, 38, 38, 12, 13, 39, 38, 14, 33, 0, 1, 33, 32, 0, 31, 32, 33, 33, 1, 2, 34, 33, 2, 28, 29, 34, 34, 2, 35, 22, 46, 21, 21, 46, 20, 23, 47, 22, 22, 47, 46, 46, 48, 20, 20, 48, 19, 23, 24, 47, 47, 43, 46, 46, 43, 48, 19, 48, 18, 26, 36, 25, 25, 37, 24, 25, 36, 37, 27, 35, 26, 26, 35, 36, 24, 42, 47, 24, 37, 42, 47, 42, 43, 28, 34, 27, 27, 34, 35, 48, 44, 18, 48, 43, 44, 36, 4, 37, 4, 5, 37, 37, 5, 42, 42, 50, 43, 42, 5, 50, 50, 45, 43, 43, 49, 44, 43, 45, 49, 35, 3, 36, 36, 3, 4, 44, 41, 18, 18, 41, 17, 35, 2, 3, 41, 40, 17, 17, 40, 16, 44, 49, 41, 5, 6, 50, 6, 7, 50, 50, 7, 45, 49, 10, 41, 41, 10, 40, 40, 39, 16, 16, 39, 15, 45, 9, 49, 49, 9, 10, 40, 10, 39, 7, 8, 45, 45, 8, 9, 10, 38, 39], "vertices": [1, 3, 16.9, 38.22, 1, 1, 3, 13.43, 33.75, 1, 2, 2, -34.8, 1.06, 0.35467, 3, 8.33, 32.16, 0.64533, 2, 2, -27.48, -0.15, 0.71289, 3, 5.83, 25.17, 0.28711, 2, 2, -19.24, -0.29, 0.89156, 3, 4.2, 17.1, 0.10844, 2, 2, -14.37, -0.36, 0.95911, 3, 3.26, 12.31, 0.04089, 2, 2, -11.06, 5.59, 0.98844, 3, 8.52, 7.99, 0.01156, 1, 2, -4.62, 10.53, 1, 1, 2, 3.37, 12.35, 1, 2, 2, 11.27, 11.15, 0.93067, 3, 9.98, -14.97, 0.06933, 2, 2, 17.11, 7.26, 0.75022, 3, 5.11, -20.02, 0.24978, 2, 2, 16.72, 11.9, 0.35956, 3, 9.75, -20.46, 0.64044, 1, 3, 14.29, -22.07, 1, 2, 2, 22.7, 17.07, 0.016, 3, 13.75, -27.28, 0.984, 2, 2, 28.41, 13.76, 0.23911, 3, 9.47, -32.31, 0.76089, 2, 2, 30.95, 7.75, 0.39555, 3, 3.11, -33.72, 0.60445, 2, 2, 30.63, 1.25, 0.632, 3, -3.23, -32.24, 0.368, 2, 2, 28.37, -5.15, 0.74933, 3, -9.12, -28.87, 0.25067, 2, 2, 23.87, -7.28, 0.88533, 3, -10.41, -24.06, 0.11467, 2, 2, 22.57, -16.23, 0.96178, 3, -18.98, -21.17, 0.03822, 1, 2, 17.52, -22.08, 1, 1, 2, 9.67, -25.38, 1, 1, 2, -0.31, -25.49, 1, 2, 2, -7.89, -21.76, 0.98844, 3, -18.95, 9.78, 0.01156, 2, 2, -12.7, -16.01, 0.95911, 3, -12.42, 13.48, 0.04089, 2, 2, -18.03, -16.29, 0.89156, 3, -11.75, 18.78, 0.10844, 2, 2, -26.39, -16.26, 0.80889, 3, -10.22, 26.99, 0.19111, 2, 2, -34.4, -14.83, 0.72356, 3, -7.37, 34.63, 0.27644, 2, 2, -40.8, -13.16, 0.60356, 3, -4.58, 40.62, 0.39644, 2, 2, -45.24, -9.72, 0.43733, 3, -0.4, 44.37, 0.56267, 1, 3, 6.8, 46.55, 1, 1, 3, 13.53, 45.98, 1, 1, 3, 16.07, 42.24, 1, 2, 2, -44.3, 0.38, 0.28756, 3, 9.37, 41.63, 0.71244, 2, 2, -38.55, -5.94, 0.41867, 3, 2.12, 37.11, 0.58133, 2, 2, -30.3, -7.37, 0.68889, 3, -0.77, 29.25, 0.31111, 2, 2, -21.25, -7.86, 0.89156, 3, -2.87, 20.43, 0.10844, 2, 2, -13.59, -8.01, 0.95911, 3, -4.4, 12.93, 0.04089, 2, 2, 22.63, 12.31, 0.28844, 3, 9.08, -26.35, 0.71156, 2, 2, 25.58, 7.23, 0.55467, 3, 3.56, -28.35, 0.44533, 2, 2, 24.93, 2.7, 0.73422, 3, -0.78, -26.89, 0.26578, 2, 2, 21.82, 0, 0.81733, 3, -2.88, -23.35, 0.18267, 2, 2, -4.89, -7.81, 0.98844, 3, -5.76, 4.33, 0.01156, 1, 2, 5.66, -7.58, 1, 1, 2, 15.61, -4.47, 1, 1, 2, 3.7, 2.61, 1, 1, 2, 7.62, -18.12, 1, 1, 2, -1.8, -15.55, 1, 1, 2, 14.65, -13.41, 1, 1, 2, 11.72, 1.41, 1, 1, 2, -2.53, -0.71, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 24, 76, 76, 78, 78, 80, 80, 82, 74, 84, 84, 86, 86, 88], "width": 81, "height": 43}}, "niuyao_011": {"niuyao_011": {"type": "mesh", "uvs": [0.77776, 0.00626, 0.91387, 0.07483, 0.99057, 0.11347, 0.99117, 0.19856, 0.86245, 0.28518, 0.77301, 0.41606, 0.60399, 0.5036, 0.47035, 0.52509, 0.45147, 0.57271, 0.43204, 0.62174, 0.40282, 0.68035, 0.35864, 0.76014, 0.34336, 0.78773, 0.43579, 0.84315, 0.4073, 0.95801, 0.31944, 0.99364, 0.1799, 0.99398, 0.05502, 0.96479, 0.05728, 0.92384, 0.07527, 0.82299, 0.0548, 0.80339, 0.00874, 0.62904, 0.00975, 0.55479, 0.04319, 0.54651, 0.0492, 0.4464, 0.05552, 0.34117, 0.14416, 0.24413, 0.21936, 0.34529, 0.30556, 0.19288, 0.49704, 0.02946, 0.60076, 0.00612, 0.65513, 0.14362, 0.44117, 0.27078, 0.30688, 0.42399, 0.17941, 0.42092, 0.25225, 0.50978, 0.22038, 0.77789, 0.23457, 0.65854, 0.23859, 0.91424, 0.15437, 0.50672, 0.54522, 0.43145, 0.67783, 0.34824], "triangles": [17, 18, 16, 16, 38, 15, 16, 18, 38, 15, 38, 14, 14, 38, 13, 18, 19, 38, 19, 36, 38, 38, 12, 13, 38, 36, 12, 19, 20, 36, 20, 21, 37, 23, 37, 21, 36, 20, 37, 12, 36, 11, 36, 37, 11, 22, 23, 21, 11, 37, 10, 10, 37, 9, 23, 39, 37, 37, 35, 9, 37, 39, 35, 9, 35, 8, 8, 35, 7, 23, 24, 39, 39, 34, 35, 39, 24, 34, 35, 33, 7, 7, 40, 6, 7, 33, 40, 35, 34, 33, 5, 40, 41, 5, 6, 40, 24, 25, 34, 33, 32, 40, 40, 32, 41, 34, 27, 33, 33, 27, 32, 34, 25, 27, 5, 41, 4, 32, 31, 41, 41, 31, 4, 25, 26, 27, 27, 28, 32, 3, 1, 2, 3, 4, 1, 4, 31, 1, 32, 28, 31, 28, 29, 31, 29, 30, 31, 31, 0, 1, 31, 30, 0], "vertices": [1, 18, -28.02, -7.46, 1, 1, 18, -30.87, 10.16, 1, 1, 18, -32.47, 20.09, 1, 1, 18, -23.3, 29.68, 1, 1, 18, -4.19, 30.01, 1, 2, 18, 16.75, 38.17, 0.95088, 19, -6.81, 58.66, 0.04912, 2, 18, 39, 35.67, 0.84964, 19, 7.73, 41.62, 0.15036, 2, 18, 51.43, 28.33, 0.56025, 19, 11.78, 27.78, 0.43975, 2, 18, 58.01, 32.3, 0.27062, 19, 19.3, 26.17, 0.72938, 3, 18, 64.79, 36.38, 0.11337, 19, 27.04, 24.52, 0.88626, 20, -19.8, 29.62, 0.00037, 3, 18, 73.35, 40.82, 0.02649, 19, 36.33, 21.91, 0.94638, 20, -11.32, 25.03, 0.02714, 3, 18, 85.34, 46.56, 0.00028, 19, 48.99, 17.91, 0.67319, 20, 0.15, 18.33, 0.32653, 2, 19, 53.37, 16.52, 0.37549, 20, 4.12, 16.01, 0.62451, 2, 19, 61.52, 26.65, 0.03642, 20, 14.3, 24.09, 0.96358, 2, 19, 79.56, 24.56, 0, 20, 31.44, 18.07, 1, 1, 20, 35.34, 8.03, 1, 1, 20, 32.88, -6.41, 1, 1, 20, 26.14, -18.55, 1, 1, 20, 19.89, -17.22, 1, 2, 19, 60.28, -11.31, 0.22674, 20, 4.71, -12.67, 0.77326, 2, 19, 57.34, -13.62, 0.44372, 20, 1.33, -14.26, 0.55628, 1, 19, 30.41, -19.81, 1, 1, 19, 18.84, -20.29, 1, 1, 19, 17.37, -16.85, 1, 2, 18, 74.7, -11.22, 0.2342, 19, 1.74, -17, 0.7658, 2, 18, 62.82, -22.57, 0.86238, 19, -14.68, -17.17, 0.13762, 1, 18, 45.61, -26.99, 1, 2, 18, 50.9, -10.15, 0.99704, 19, -14.91, 0.05, 0.00296, 1, 18, 27.87, -20.97, 1, 1, 18, -4.3, -25.34, 1, 1, 18, -14.67, -20.39, 1, 1, 18, -3.87, -1, 1, 1, 18, 26.07, -2.33, 1, 2, 18, 52.81, 5.06, 0.89252, 19, -3.11, 9.84, 0.10748, 2, 18, 62.11, -4.58, 0.66402, 19, -2.91, -3.55, 0.33598, 2, 18, 66.24, 10.7, 0.08783, 19, 10.55, 4.79, 0.91217, 2, 19, 52.49, 3.55, 0.50384, 20, 0.39, 3.55, 0.49616, 2, 18, 83.7, 26.11, 0.00174, 19, 33.82, 4.1, 0.99826, 1, 20, 21.68, 1.79, 1, 1, 19, 10.59, -5.5, 1, 2, 18, 35.62, 23.28, 0.8773, 19, -3.2, 34.89, 0.1227, 2, 18, 16.59, 23.62, 0.90727, 19, -16.87, 48.15, 0.09273], "hull": 31, "edges": [0, 60, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 2, 2, 4, 2, 62, 62, 64, 64, 66, 66, 70, 70, 74, 74, 72, 72, 76, 20, 22, 22, 24, 72, 22, 14, 16, 16, 18, 70, 16, 46, 48, 48, 50], "width": 105, "height": 156}}, "niuyao_012": {"niuyao_012": {"type": "mesh", "uvs": [0.83543, 0.25487, 0.86094, 0.25377, 0.88228, 0.25487, 0.87603, 0.16786, 0.84064, 0.05551, 0.91143, 0.07313, 0.94085, 0.12131, 1, 0.21816, 1, 0.32753, 0.93769, 0.40639, 0.88746, 0.46996, 0.92509, 0.47824, 0.97015, 0.50041, 0.9838, 0.5446, 0.97069, 0.58495, 0.93315, 0.61332, 0.8813, 0.6398, 0.89978, 0.66375, 0.90812, 0.70789, 0.88044, 0.70034, 0.85236, 0.69918, 0.8731, 0.74355, 0.8783, 0.78706, 0.86998, 0.82974, 0.8541, 0.84241, 0.84577, 0.82533, 0.83276, 0.79394, 0.80959, 0.77302, 0.78227, 0.79947, 0.74948, 0.83747, 0.74557, 0.87795, 0.7323, 0.93661, 0.71668, 0.949, 0.6917, 0.94652, 0.67218, 0.91347, 0.65968, 0.8953, 0.64641, 0.93082, 0.62533, 0.96634, 0.60971, 1, 0.5652, 0.96221, 0.541, 0.89282, 0.541, 0.84491, 0.52538, 0.82426, 0.49181, 0.85565, 0.47775, 0.90604, 0.46448, 0.95808, 0.43247, 0.90274, 0.43949, 0.835, 0.47541, 0.76643, 0.42696, 0.7831, 0.37602, 0.79999, 0.32128, 0.86354, 0.319, 0.8016, 0.32737, 0.74852, 0.28327, 0.77908, 0.23689, 0.81608, 0.23005, 0.7646, 0.24146, 0.72599, 0.26122, 0.70347, 0.22929, 0.70025, 0.18596, 0.71956, 0.14338, 0.74288, 0.16959, 0.67736, 0.21102, 0.64467, 0.26979, 0.61678, 0.31876, 0.59286, 0.28863, 0.5562, 0.24794, 0.53228, 0.2163, 0.51634, 0.16959, 0.54823, 0.11831, 0.57678, 0.07015, 0.59678, 0.05187, 0.62515, 0.03663, 0.67997, 0.0098, 0.64579, 0.00127, 0.59162, 0.01651, 0.55034, 0.02382, 0.49294, 0.03641, 0.43099, 0.0563, 0.38954, 0.07317, 0.35766, 0.03038, 0.33534, 0.00868, 0.27858, 0.00567, 0.21099, 0.02013, 0.15806, 0.04665, 0.12618, 0.07498, 0.17592, 0.11597, 0.18485, 0.15816, 0.16954, 0.16599, 0.14212, 0.16057, 0.10003, 0.20156, 0.12682, 0.24013, 0.12873, 0.29097, 0.10506, 0.34534, 0.10004, 0.39582, 0.111, 0.41222, 0.11693, 0.41351, 0.07995, 0.39496, 0.03978, 0.3544, 0.01787, 0.39237, 0.00371, 0.45205, 0.01212, 0.50575, 0.03812, 0.54378, 0.07014, 0.56722, 0.09704, 0.58549, 0.08149, 0.61608, 0.07686, 0.6268, 0.05039, 0.63475, 0.01466, 0.66931, 0.0487, 0.70488, 0.09735, 0.73096, 0.13915, 0.74822, 0.16958, 0.7628, 0.14443, 0.75781, 0.10993, 0.80422, 0.15132, 0.82263, 0.18176, 0.83299, 0.22234, 0.81617, 0.53997, 0.72606, 0.5793, 0.64729, 0.57797, 0.5156, 0.5173, 0.55901, 0.78197, 0.70216, 0.79871, 0.78278, 0.7756, 0.82271, 0.71263, 0.78202, 0.67437, 0.53415, 0.72538, 0.59065, 0.67277, 0.70969, 0.74212, 0.70292, 0.67371, 0.68863, 0.57867, 0.58764, 0.75009, 0.78127, 0.74212, 0.62382, 0.63067, 0.74964, 0.62827, 0.69666, 0.63202, 0.67592, 0.53416, 0.71311, 0.53288, 0.79113, 0.48064, 0.83685, 0.48709, 0.83502, 0.51353, 0.58265, 0.46774, 0.50707, 0.45355, 0.50036, 0.48709, 0.79256, 0.60716, 0.48273, 0.59452, 0.50065, 0.66087, 0.56569, 0.50595, 0.56476, 0.53995, 0.66435, 0.55935, 0.71942, 0.56226, 0.80799, 0.51274, 0.54991, 0.59212, 0.54449, 0.63947, 0.77208, 0.6253, 0.75183, 0.50695, 0.77006, 0.53395, 0.76587, 0.56192, 0.76972, 0.59913, 0.80322, 0.57683, 0.69441, 0.4766, 0.62192, 0.39534, 0.5779, 0.41086, 0.52267, 0.37708, 0.4847, 0.41451, 0.77122, 0.40356, 0.79538, 0.41908, 0.81005, 0.3853, 0.823, 0.43095, 0.4728, 0.10109, 0.52542, 0.10956, 0.52771, 0.12892, 0.46594, 0.16523, 0.41561, 0.24873, 0.41104, 0.3407, 0.46365, 0.48592, 0.39731, 0.52343, 0.29322, 0.44961, 0.25662, 0.31892, 0.30466, 0.19185, 0.85144, 0.31496, 0.82075, 0.35345, 0.65049, 0.05747, 0.66375, 0.11797, 0.67701, 0.19776, 0.69441, 0.28631, 0.70353, 0.3889, 0.78641, 0.15655, 0.78972, 0.22055, 0.77895, 0.2907, 0.76403, 0.35383, 0.74331, 0.21266, 0.73751, 0.28105, 0.73585, 0.36961, 0.73863, 0.43455, 0.66126, 0.37575, 0.6422, 0.29245, 0.60491, 0.20565, 0.59496, 0.12849, 0.54689, 0.17233, 0.51291, 0.24335, 0.50877, 0.32226, 0.58253, 0.30297, 0.619, 0.3512, 0.85354, 0.55014, 0.89606, 0.52483, 0.94788, 0.51921, 0.83826, 0.60004, 0.82962, 0.64151, 0.86882, 0.6626, 0.89141, 0.6879, 0.85287, 0.74835, 0.85752, 0.79053, 0.85752, 0.81935, 0.71573, 0.90646, 0.71108, 0.85515, 0.7051, 0.83969, 0.73566, 0.82282, 0.61806, 0.83407, 0.57089, 0.82915, 0.18344, 0.69282, 0.24722, 0.66159, 0.31292, 0.64245, 0.41098, 0.62936, 0.26722, 0.75325, 0.31768, 0.70793, 0.38718, 0.66864, 0.34433, 0.7885, 0.40907, 0.72706, 0.45668, 0.66764, 0.43954, 0.56792, 0.38051, 0.56893, 0.31863, 0.5377, 0.02803, 0.60157, 0.06421, 0.52704, 0.12038, 0.45955, 0.1994, 0.40013, 0.04136, 0.19364, 0.07754, 0.25609, 0.15942, 0.2853, 0.20892, 0.29739, 0.18798, 0.15234, 0.22701, 0.2158, 0.28102, 0.25437, 0.27747, 0.14831, 0.33745, 0.14026, 0.41705, 0.02767, 0.45914, 0.06037, 0.90529, 0.12841, 0.94067, 0.20693, 0.93636, 0.29459, 0.88717, 0.36215, 0.4384, 0.14393, 0.359, 0.21972, 0.32793, 0.30554, 0.35124, 0.39959, 0.4151, 0.44524], "triangles": [24, 214, 23, 24, 25, 214, 23, 214, 22, 25, 26, 214, 26, 213, 214, 214, 213, 22, 213, 26, 212, 26, 27, 212, 213, 212, 22, 212, 21, 22, 125, 20, 212, 212, 20, 21, 19, 211, 18, 211, 17, 18, 211, 19, 210, 19, 20, 210, 20, 209, 210, 211, 210, 17, 210, 16, 17, 210, 209, 16, 209, 208, 16, 16, 208, 205, 219, 40, 220, 35, 219, 217, 216, 217, 218, 219, 123, 217, 217, 123, 218, 123, 219, 122, 42, 122, 220, 219, 220, 122, 42, 48, 122, 28, 218, 124, 124, 218, 123, 124, 129, 133, 124, 123, 129, 122, 132, 123, 123, 132, 129, 48, 127, 122, 122, 127, 132, 27, 124, 125, 27, 125, 212, 125, 124, 133, 127, 230, 147, 132, 130, 129, 132, 128, 130, 132, 127, 128, 133, 126, 125, 133, 129, 126, 129, 130, 126, 127, 154, 128, 127, 147, 154, 20, 125, 209, 125, 126, 209, 130, 135, 126, 135, 155, 126, 145, 126, 155, 145, 209, 126, 128, 134, 130, 130, 134, 136, 136, 134, 120, 120, 131, 136, 130, 136, 135, 128, 154, 134, 147, 230, 146, 230, 224, 146, 147, 146, 154, 209, 145, 208, 15, 16, 205, 154, 153, 134, 154, 146, 153, 136, 119, 135, 136, 131, 119, 134, 153, 120, 224, 231, 146, 135, 159, 155, 135, 119, 159, 155, 159, 145, 205, 206, 15, 15, 207, 14, 15, 206, 207, 145, 160, 208, 145, 159, 160, 208, 160, 205, 205, 160, 118, 119, 158, 159, 159, 158, 160, 146, 231, 121, 146, 121, 153, 121, 176, 144, 176, 121, 231, 153, 149, 120, 153, 121, 149, 14, 207, 13, 131, 151, 119, 119, 151, 158, 120, 150, 131, 150, 137, 131, 131, 138, 151, 131, 137, 138, 120, 149, 150, 160, 158, 118, 232, 177, 231, 232, 233, 177, 231, 177, 176, 158, 151, 157, 158, 157, 118, 157, 151, 138, 157, 138, 156, 137, 150, 148, 118, 141, 205, 205, 141, 206, 207, 12, 13, 157, 152, 118, 118, 152, 141, 150, 149, 148, 149, 121, 148, 177, 233, 178, 148, 142, 137, 137, 161, 138, 137, 142, 161, 152, 157, 139, 138, 161, 156, 141, 140, 206, 140, 10, 206, 206, 11, 207, 206, 10, 11, 177, 178, 257, 177, 257, 176, 257, 178, 256, 207, 11, 12, 121, 144, 148, 141, 152, 140, 157, 156, 139, 152, 139, 140, 161, 195, 156, 156, 195, 139, 144, 143, 148, 148, 143, 142, 144, 176, 143, 139, 169, 140, 140, 169, 10, 176, 165, 143, 176, 257, 165, 139, 195, 167, 139, 167, 169, 167, 195, 166, 142, 162, 161, 162, 196, 161, 161, 187, 195, 161, 196, 187, 169, 252, 10, 10, 252, 9, 143, 163, 142, 142, 163, 162, 143, 164, 163, 143, 165, 164, 237, 179, 178, 256, 179, 255, 256, 178, 179, 256, 175, 257, 257, 175, 165, 187, 194, 195, 195, 194, 166, 167, 168, 169, 169, 168, 252, 252, 168, 182, 167, 166, 168, 165, 202, 164, 165, 175, 202, 162, 163, 204, 252, 251, 9, 9, 251, 8, 194, 191, 166, 166, 191, 168, 237, 241, 179, 256, 255, 175, 163, 164, 204, 162, 204, 196, 194, 187, 186, 168, 191, 182, 164, 203, 204, 164, 202, 203, 204, 197, 196, 187, 196, 186, 194, 193, 191, 182, 181, 252, 252, 181, 251, 251, 181, 2, 191, 190, 182, 182, 190, 181, 175, 174, 202, 175, 255, 174, 251, 7, 8, 202, 174, 201, 179, 244, 255, 179, 241, 244, 181, 1, 2, 190, 0, 181, 255, 254, 174, 255, 244, 254, 241, 243, 244, 251, 250, 7, 251, 2, 250, 250, 2, 3, 244, 180, 254, 244, 243, 180, 174, 173, 201, 174, 253, 173, 174, 254, 253, 201, 173, 200, 254, 96, 253, 254, 180, 96, 250, 6, 7, 243, 245, 180, 3, 249, 250, 250, 249, 6, 180, 246, 96, 246, 95, 96, 180, 245, 246, 173, 172, 200, 249, 3, 4, 173, 253, 172, 172, 170, 171, 172, 253, 170, 253, 96, 170, 172, 171, 104, 249, 5, 6, 249, 4, 5, 96, 97, 170, 171, 170, 102, 196, 197, 186, 194, 186, 193, 191, 193, 190, 204, 203, 197, 202, 201, 203, 181, 0, 1, 203, 198, 197, 203, 201, 198, 197, 185, 186, 197, 198, 185, 0, 189, 117, 0, 190, 189, 190, 193, 189, 193, 186, 192, 193, 192, 189, 192, 186, 185, 201, 200, 198, 189, 116, 117, 192, 112, 189, 112, 188, 189, 116, 188, 115, 116, 189, 188, 112, 113, 188, 192, 185, 112, 200, 199, 198, 198, 184, 185, 184, 199, 106, 184, 198, 199, 185, 111, 112, 111, 184, 110, 111, 185, 184, 246, 94, 95, 199, 172, 104, 199, 200, 172, 115, 188, 114, 114, 188, 113, 106, 183, 184, 106, 107, 183, 104, 105, 199, 199, 105, 106, 184, 109, 110, 184, 183, 109, 171, 103, 104, 103, 171, 102, 97, 248, 170, 170, 248, 102, 97, 247, 248, 97, 98, 247, 247, 101, 248, 248, 101, 102, 109, 183, 108, 183, 107, 108, 98, 99, 247, 99, 100, 247, 247, 100, 101, 80, 239, 240, 80, 81, 239, 81, 82, 239, 241, 240, 243, 239, 87, 240, 87, 88, 240, 243, 88, 242, 243, 240, 88, 239, 83, 238, 239, 82, 83, 238, 86, 239, 239, 86, 87, 243, 242, 92, 243, 92, 245, 92, 242, 91, 83, 84, 238, 86, 238, 85, 238, 84, 85, 88, 89, 242, 91, 242, 90, 245, 93, 246, 245, 92, 93, 242, 89, 90, 246, 93, 94, 69, 70, 236, 66, 67, 233, 70, 235, 236, 69, 236, 68, 233, 67, 178, 67, 68, 178, 236, 237, 68, 68, 237, 178, 77, 78, 236, 78, 79, 236, 79, 80, 236, 236, 80, 237, 80, 240, 237, 237, 240, 241, 72, 73, 234, 73, 74, 234, 74, 75, 234, 72, 234, 71, 235, 71, 234, 234, 75, 76, 234, 76, 235, 71, 235, 70, 76, 77, 235, 235, 77, 236, 225, 55, 56, 225, 54, 55, 54, 225, 53, 56, 57, 225, 57, 58, 225, 225, 226, 53, 225, 58, 226, 61, 221, 60, 61, 62, 221, 60, 221, 59, 58, 223, 226, 226, 223, 227, 59, 222, 58, 58, 222, 223, 59, 221, 222, 221, 63, 222, 221, 62, 63, 227, 223, 224, 222, 64, 223, 222, 63, 64, 224, 65, 232, 224, 223, 65, 223, 64, 65, 224, 232, 231, 66, 233, 65, 65, 233, 232, 45, 46, 44, 44, 46, 43, 46, 47, 43, 228, 51, 52, 228, 50, 51, 42, 43, 48, 43, 47, 48, 228, 52, 53, 49, 50, 229, 50, 228, 229, 228, 53, 229, 49, 229, 48, 229, 230, 48, 127, 48, 230, 53, 226, 229, 226, 227, 229, 229, 227, 230, 227, 224, 230, 38, 39, 37, 37, 39, 36, 36, 40, 219, 40, 36, 39, 31, 32, 215, 32, 33, 215, 33, 34, 215, 31, 215, 30, 36, 219, 35, 34, 35, 215, 35, 216, 215, 215, 216, 30, 35, 217, 216, 40, 41, 220, 30, 216, 29, 216, 218, 29, 41, 42, 220, 29, 218, 28, 28, 124, 27], "vertices": [6, 14, 73.76, 66.25, 0.11448, 13, 92.17, 29.17, 0.04494, 12, 96.27, 11.04, 0.0047, 10, 100.62, -23.18, 0.00365, 8, 14.73, -32.53, 0.63223, 7, 37.53, 25.73, 0.2, 6, 14, 77.12, 64.65, 0.12842, 13, 95.53, 27.56, 0.04658, 12, 99.63, 9.43, 0.00323, 10, 103.98, -24.78, 0.00227, 8, 18.1, -34.13, 0.6195, 7, 40.9, 24.13, 0.2, 1, 5, 83.76, 18.03, 1, 1, 5, 88.56, 29.07, 1, 1, 5, 91.23, 45.19, 1, 1, 5, 99.24, 38.22, 1, 1, 5, 99.93, 30.33, 1, 1, 5, 101.32, 14.48, 1, 1, 5, 94.27, 1.13, 1, 1, 5, 81.14, -4.24, 1, 1, 5, 70.56, -8.57, 1, 1, 5, 74.89, -12.15, 1, 1, 5, 79.27, -17.92, 1, 5, 14, 74.24, 20.78, 0.39609, 13, 92.64, -16.3, 0.12587, 12, 96.75, -34.43, 0.00169, 10, 101.09, -68.65, 0.00068, 8, 15.21, -78, 0.47567, 2, 5, 73.89, -28.28, 0.856, 15, 24.27, 13.01, 0.144, 3, 5, 67.22, -29.18, 0.6144, 15, 17.6, 12.11, 0.1856, 7, 27.04, -24.67, 0.2, 2, 15, 9.2, 12.42, 0.8, 7, 18.64, -24.37, 0.2, 2, 15, 10.04, 8.24, 0.8, 7, 19.48, -28.55, 0.2, 2, 15, 8.27, 2.28, 0.8, 7, 17.71, -34.5, 0.2, 2, 15, 5.18, 5.09, 0.8, 7, 14.63, -31.69, 0.2, 2, 15, 1.63, 7.15, 0.8, 7, 11.08, -29.64, 0.2, 2, 15, 1.45, 0.32, 0.8, 7, 10.89, -36.47, 0.2, 2, 15, -0.68, -5.35, 0.8, 7, 8.76, -42.13, 0.2, 2, 15, -4.51, -9.99, 0.8, 7, 4.93, -46.77, 0.2, 2, 15, -7.38, -10.45, 0.8, 7, 2.07, -47.23, 0.2, 2, 15, -7.35, -7.8, 0.8, 7, 2.09, -44.58, 0.2, 2, 15, -7.01, -3.08, 0.8, 7, 2.44, -39.86, 0.2, 5, 14, 37.02, 4.79, 0.63122, 13, 55.43, -32.29, 0.13884, 12, 59.53, -50.42, 0.00083, 10, 63.88, -84.64, 0.00222, 8, -22, -93.99, 0.22689, 5, 14, 31.79, 3.43, 0.6884, 13, 50.2, -33.66, 0.13298, 12, 54.3, -51.79, 0.00079, 10, 58.65, -86, 0.00279, 8, -27.23, -95.35, 0.17504, 5, 14, 25.11, 1.03, 0.79013, 13, 43.52, -36.06, 0.10133, 12, 47.62, -54.19, 0.00015, 10, 51.96, -88.4, 0.00162, 8, -33.92, -97.75, 0.10677, 4, 14, 21.99, -3.65, 0.86873, 13, 40.4, -40.73, 0.06314, 10, 48.85, -93.08, 0.00043, 8, -37.03, -102.43, 0.0677, 3, 14, 16.5, -9.9, 0.92118, 13, 34.91, -46.98, 0.0335, 8, -42.53, -108.68, 0.04532, 3, 14, 13.69, -10.35, 0.92427, 13, 32.09, -47.43, 0.03183, 8, -45.34, -109.12, 0.0439, 3, 14, 10.62, -8.34, 0.92498, 13, 29.03, -45.42, 0.03224, 8, -48.41, -107.12, 0.04277, 4, 14, 10.23, -2.98, 0.91848, 13, 28.64, -40.06, 0.04113, 10, 37.09, -92.4, 8e-05, 8, -48.8, -101.75, 0.04031, 4, 14, 9.79, 0.09, 0.9173, 13, 28.2, -36.99, 0.04937, 10, 36.64, -89.34, 0.0003, 8, -49.24, -98.69, 0.03302, 4, 14, 5.78, -3.34, 0.98079, 13, 24.19, -40.42, 0.01046, 10, 32.64, -92.76, 1e-05, 8, -53.24, -102.11, 0.00874, 2, 14, 0.77, -6.23, 0.99976, 8, -58.25, -105.01, 0.00024, 1, 14, -3.41, -9.27, 1, 1, 14, -6.72, -1.63, 1, 4, 14, -5.37, 8.49, 0.89233, 13, 13.03, -28.6, 0.09929, 10, 21.48, -80.94, 0.0005, 8, -64.4, -90.29, 0.00787, 4, 14, -2.28, 14.34, 0.69851, 13, 16.12, -22.75, 0.27512, 10, 24.57, -75.09, 0.0028, 8, -61.31, -84.44, 0.02357, 6, 14, -2.97, 17.92, 0.42021, 13, 15.44, -19.16, 0.35546, 12, 19.54, -37.29, 0.00018, 10, 23.89, -71.51, 0.00349, 8, -61.99, -80.86, 0.02066, 7, -39.2, -22.6, 0.2, 5, 14, -9.33, 16.38, 0.33318, 13, 9.08, -20.7, 0.45918, 10, 17.53, -73.05, 0.00078, 8, -68.35, -82.4, 0.00686, 7, -45.56, -24.14, 0.2, 4, 14, -14.39, 11.19, 0.31514, 13, 4.02, -25.89, 0.48419, 8, -73.42, -87.59, 0.00067, 7, -50.62, -29.33, 0.2, 3, 14, -19.46, 5.75, 0.31159, 13, -1.05, -31.34, 0.48841, 7, -55.69, -34.78, 0.2, 4, 14, -20.02, 14.68, 0.3146, 13, -1.61, -22.4, 0.48489, 8, -79.05, -84.1, 0.0005, 7, -56.25, -25.84, 0.2, 5, 14, -14.75, 22.47, 0.32078, 13, 3.66, -14.62, 0.47305, 10, 12.11, -66.96, 0.00058, 8, -73.77, -76.31, 0.00558, 7, -50.98, -18.05, 0.2, 6, 14, -5.69, 28.39, 0.24256, 13, 12.72, -8.7, 0.51846, 12, 16.82, -26.83, 0.00561, 10, 21.16, -61.04, 0.00923, 8, -64.72, -70.39, 0.02414, 7, -41.92, -12.14, 0.2, 5, 14, -13.02, 29.66, 0.07075, 13, 5.39, -7.43, 0.71665, 10, 13.83, -59.77, 0.00391, 8, -72.05, -69.12, 0.00869, 7, -49.25, -10.87, 0.2, 4, 14, -20.69, 31.07, 0.00505, 13, -2.28, -6.02, 0.79489, 8, -79.71, -67.71, 6e-05, 7, -56.91, -9.45, 0.2, 6, 14, -31.85, 27.05, 0, 13, -13.44, -10.04, 0.79999, 12, -9.34, -28.17, 1e-05, 10, -4.99, -62.38, 0, 8, -90.87, -71.73, 0, 7, -68.08, -13.47, 0.2, 5, 13, -9.74, -2.32, 0.79548, 12, -5.64, -20.45, 0.00452, 10, -1.3, -54.67, 0, 8, -87.18, -64.02, 0, 7, -64.38, -5.76, 0.2, 5, 13, -5.24, 3.58, 0.53902, 12, -1.14, -14.55, 0.26055, 10, 3.21, -48.76, 0.0004, 8, -82.67, -58.11, 3e-05, 7, -59.88, 0.15, 0.2, 3, 13, -12.9, 2.86, 0.24195, 12, -8.8, -15.27, 0.55805, 7, -67.54, -0.58, 0.2, 3, 13, -21.28, 1.51, 0.19906, 12, -17.17, -16.62, 0.60094, 7, -75.91, -1.93, 0.2, 3, 13, -18.84, 8.26, 0.19732, 12, -14.74, -9.87, 0.60268, 7, -73.48, 4.82, 0.2, 3, 13, -14.88, 12.19, 0.17385, 12, -10.78, -5.94, 0.62615, 7, -69.52, 8.75, 0.2, 3, 13, -10.88, 13.59, 0.11012, 12, -6.77, -4.54, 0.68988, 7, -65.51, 10.15, 0.2, 3, 13, -14.79, 16.16, 0.01492, 12, -10.69, -1.97, 0.78508, 7, -69.43, 12.72, 0.2, 2, 12, -17.53, -1.37, 0.8, 7, -76.27, 13.32, 0.2, 2, 12, -24.53, -1.31, 0.8, 7, -83.27, 13.38, 0.2, 2, 12, -16.92, 4.9, 0.8, 7, -75.66, 19.59, 0.2, 2, 12, -9.46, 6.06, 0.8, 7, -68.2, 20.75, 0.2, 5, 14, -22.59, 60.67, 0.00046, 12, -0.08, 5.46, 0.78738, 10, 4.27, -28.76, 0.01026, 8, -81.61, -38.11, 0.00189, 7, -58.82, 20.15, 0.2, 7, 14, -14.73, 60.25, 0.0102, 13, 3.68, 23.16, 0.06747, 12, 7.79, 5.03, 0.50879, 10, 12.13, -29.18, 0.17709, 9, 6.79, -59.42, 0.00133, 8, -73.75, -38.53, 0.03512, 7, -50.95, 19.73, 0.2, 6, 14, -16.25, 66.78, 0.00896, 13, 2.16, 29.69, 0.03872, 12, 6.26, 11.56, 0.37471, 10, 10.6, -22.65, 0.50906, 9, 5.27, -52.89, 0.00582, 8, -75.28, -32, 0.06273, 7, 14, -19.96, 72.47, 0.0032, 13, -1.56, 35.38, 0.01113, 12, 2.55, 17.26, 0.21791, 11, 29.75, -9.66, 0.00373, 10, 6.89, -16.96, 0.71168, 9, 1.56, -47.2, 0.00547, 8, -78.99, -26.31, 0.04688, 8, 14, -23.02, 76.57, 0.00057, 13, -4.61, 39.49, 0.00153, 12, -0.51, 21.36, 0.08572, 11, 26.69, -5.55, 0.0276, 10, 3.83, -12.86, 0.66261, 9, -1.5, -43.1, 0.00214, 8, -82.05, -22.21, 0.01983, 7, -59.25, 36.05, 0.2, 5, 12, -8.59, 20.65, 0.01727, 11, 18.61, -6.26, 0.19084, 10, -4.25, -13.56, 0.58979, 8, -90.13, -22.91, 0.0021, 7, -67.33, 35.35, 0.2, 4, 12, -17.05, 20.67, 0.00103, 11, 10.15, -6.24, 0.47242, 10, -12.71, -13.55, 0.32655, 7, -75.79, 35.36, 0.2, 3, 11, 2.64, -5.4, 0.74188, 10, -20.22, -12.7, 0.05812, 7, -83.3, 36.2, 0.2, 3, 11, -1.55, -7.62, 0.79806, 10, -24.41, -14.92, 0.00194, 7, -87.49, 33.99, 0.2, 3, 11, -7.05, -13.26, 0.79995, 10, -29.91, -20.57, 5e-05, 7, -92.99, 28.34, 0.2, 2, 11, -8.31, -7.26, 0.8, 7, -94.25, 34.34, 0.2, 2, 11, -5.92, -0.07, 0.8, 7, -91.86, 41.53, 0.2, 3, 11, -1.29, 3.92, 0.76058, 10, -24.15, -3.38, 0.03942, 7, -87.23, 45.53, 0.2, 4, 11, 3.36, 10.43, 0.49571, 10, -19.5, 3.13, 0.29664, 9, -24.84, -27.12, 0.00765, 7, -82.59, 52.03, 0.2, 4, 11, 8.97, 17.13, 0.2321, 10, -13.89, 9.83, 0.50265, 9, -19.22, -20.41, 0.06524, 7, -76.97, 58.73, 0.2, 4, 11, 14.21, 20.83, 0.09772, 10, -8.65, 13.53, 0.523, 9, -13.98, -16.71, 0.17928, 7, -71.73, 62.44, 0.2, 6, 12, -8.76, 50.48, 4e-05, 11, 18.45, 23.57, 0.02384, 10, -4.41, 16.27, 0.38395, 9, -9.75, -13.97, 0.3915, 8, -90.29, 6.92, 0.00067, 7, -67.5, 65.17, 0.2, 4, 11, 14.36, 29.21, 0.00316, 10, -8.5, 21.91, 0.171, 9, -13.83, -8.33, 0.62583, 7, -71.58, 70.82, 0.2, 3, 10, -7.64, 30.31, 0.04547, 9, -12.98, 0.07, 0.75453, 7, -70.72, 79.22, 0.2, 3, 10, -3.67, 38.77, 0.00057, 9, -9.01, 8.53, 0.79943, 7, -66.76, 87.67, 0.2, 2, 9, -3.73, 14, 0.8, 7, -61.48, 93.15, 0.2, 3, 12, 2.74, 80.54, 0, 9, 1.75, 16.08, 0.8, 7, -56, 95.23, 0.2, 4, 12, 3.19, 72.53, 0.00011, 9, 2.2, 8.08, 0.79875, 8, -78.34, 28.97, 0.00114, 7, -55.55, 87.23, 0.2, 5, 12, 7.91, 68.65, 0.00285, 10, 12.25, 34.43, 0.02196, 9, 6.92, 4.19, 0.75545, 8, -73.63, 25.08, 0.01973, 7, -50.83, 83.34, 0.2, 5, 12, 14.34, 67.64, 0.00961, 10, 18.69, 33.42, 0.08356, 9, 13.35, 3.18, 0.64054, 8, -67.2, 24.07, 0.06629, 7, -44.4, 82.33, 0.2, 5, 12, 17.12, 70.45, 0.01224, 10, 21.46, 36.24, 0.09712, 9, 16.13, 5.99, 0.60061, 8, -64.42, 26.89, 0.09003, 7, -41.62, 85.14, 0.2, 5, 12, 19.13, 75.96, 0.01208, 10, 23.48, 41.74, 0.09292, 9, 18.14, 11.5, 0.6022, 8, -62.4, 32.39, 0.09281, 7, -39.61, 90.65, 0.2, 5, 12, 22.7, 69.89, 0.0145, 10, 27.04, 35.68, 0.11014, 9, 21.71, 5.44, 0.569, 8, -58.84, 26.33, 0.10636, 7, -36.04, 84.59, 0.2, 5, 12, 27.55, 67.03, 0.02074, 10, 31.9, 32.81, 0.14385, 9, 26.56, 2.57, 0.48402, 8, -53.98, 23.46, 0.15139, 7, -31.19, 81.72, 0.2, 5, 12, 35.64, 66.45, 0.02714, 10, 39.99, 32.23, 0.16278, 9, 34.65, 1.99, 0.38491, 8, -45.89, 22.89, 0.22517, 7, -23.1, 81.14, 0.2, 7, 14, 20.47, 118.57, 1e-05, 13, 38.88, 81.48, 0.00035, 12, 42.98, 63.36, 0.03042, 10, 47.33, 29.14, 0.16321, 9, 41.99, -1.1, 0.31148, 8, -38.55, 19.79, 0.29453, 7, -15.75, 78.05, 0.2, 7, 14, 26.28, 113.79, 0.00035, 13, 44.69, 76.7, 0.0012, 12, 48.8, 58.58, 0.03019, 10, 53.14, 24.36, 0.14455, 9, 47.8, -5.88, 0.22838, 8, -32.74, 15.01, 0.39532, 7, -9.94, 73.27, 0.2, 1, 5, 31.97, 66.92, 1, 7, 14, 30.57, 116.37, 7e-05, 13, 48.98, 79.28, 0.00041, 12, 53.08, 61.16, 0.02062, 10, 57.42, 26.94, 0.09842, 9, 52.09, -3.3, 0.14746, 8, -28.46, 17.59, 0.53302, 7, -5.66, 75.85, 0.2, 6, 13, 49.17, 85.45, 1e-05, 12, 53.28, 67.32, 0.01614, 10, 57.62, 33.11, 0.08395, 9, 52.28, 2.87, 0.13427, 8, -28.26, 23.76, 0.56563, 7, -5.46, 82.02, 0.2, 6, 13, 45.35, 90.89, 0, 12, 49.45, 72.76, 0.01511, 10, 53.8, 38.55, 0.08127, 9, 48.46, 8.31, 0.13311, 8, -32.09, 29.2, 0.57051, 7, -9.29, 87.46, 0.2, 5, 12, 55.27, 71.9, 0.01551, 10, 59.61, 37.69, 0.0822, 9, 54.27, 7.44, 0.13325, 8, -26.27, 28.34, 0.56904, 7, -3.47, 86.59, 0.2, 5, 12, 62.43, 66.8, 0.01654, 10, 66.77, 32.59, 0.08312, 9, 61.44, 2.35, 0.12922, 8, -19.11, 23.24, 0.57112, 7, 3.69, 81.5, 0.2, 5, 12, 67.68, 59.97, 0.01673, 10, 72.03, 25.75, 0.07783, 9, 66.69, -4.49, 0.11293, 8, -13.85, 16.41, 0.59251, 7, 8.94, 74.66, 0.2, 7, 14, 48.02, 108.68, 7e-05, 13, 66.43, 71.6, 0.0006, 12, 70.53, 53.47, 0.01587, 10, 74.87, 19.25, 0.06676, 9, 69.54, -10.99, 0.08792, 8, -11.01, 9.9, 0.62877, 7, 11.79, 68.16, 0.2, 6, 14, 49.31, 103.8, 0.00089, 13, 67.72, 66.72, 0.00172, 12, 71.82, 48.59, 0.01471, 10, 76.17, 14.37, 0.05549, 9, 70.83, -15.87, 0.06531, 8, -9.72, 5.02, 0.86188, 7, 14, 52.67, 104.45, 0.00023, 13, 71.08, 67.37, 0.0007, 12, 75.18, 49.24, 0.00676, 10, 79.53, 15.03, 0.02481, 9, 74.19, -15.22, 0.02826, 8, -6.35, 5.68, 0.73923, 7, 16.44, 63.93, 0.2, 5, 12, 79.43, 47.72, 0.00166, 10, 83.77, 13.5, 0.00621, 9, 78.44, -16.74, 0.0071, 8, -2.11, 4.15, 0.78503, 7, 20.69, 62.41, 0.2, 5, 12, 82.52, 50.22, 0.00015, 10, 86.87, 16, 0.00064, 9, 81.53, -14.24, 0.00079, 8, 0.98, 6.65, 0.79842, 7, 23.78, 64.91, 0.2, 2, 8, 4.31, 10.47, 0.8, 7, 27.11, 68.73, 0.2, 4, 14, 65.61, 102.74, 0.00024, 13, 84.01, 65.65, 6e-05, 8, 6.58, 3.96, 0.7997, 7, 29.38, 62.22, 0.2, 5, 14, 67.06, 94.38, 0.01002, 13, 85.47, 57.29, 0.0045, 12, 89.57, 39.16, 0.00034, 8, 8.03, -4.4, 0.78514, 7, 30.83, 53.85, 0.2, 7, 14, 67.73, 87.5, 0.03108, 13, 86.14, 50.41, 0.01436, 12, 90.24, 32.28, 0.00249, 10, 94.59, -1.93, 0.00196, 9, 89.25, -32.17, 1e-05, 8, 8.71, -11.28, 0.7501, 7, 31.51, 46.98, 0.2, 7, 14, 68, 82.61, 0.05481, 13, 86.41, 45.52, 0.02442, 12, 90.51, 27.39, 0.00374, 10, 94.86, -6.82, 0.00284, 9, 89.52, -37.06, 5e-05, 8, 8.97, -16.17, 0.71413, 7, 31.77, 42.08, 0.2, 6, 14, 71.5, 84.68, 0.06154, 13, 89.91, 47.6, 0.0262, 12, 94.01, 29.47, 0.00239, 10, 98.36, -4.75, 0.00046, 8, 12.48, -14.1, 0.70941, 7, 35.27, 44.16, 0.2, 5, 14, 73.08, 89.23, 0.06157, 13, 91.49, 52.15, 0.02594, 12, 95.59, 34.02, 0.00185, 8, 14.06, -9.55, 0.71064, 7, 36.85, 48.71, 0.2, 6, 14, 76.41, 81.02, 0.06737, 13, 94.81, 43.93, 0.02839, 12, 98.92, 25.8, 0.00251, 10, 103.26, -8.41, 0.00026, 8, 17.38, -17.76, 0.70145, 7, 40.18, 40.49, 0.2, 6, 14, 76.82, 76.05, 0.07611, 13, 95.23, 38.96, 0.03185, 12, 99.33, 20.83, 0.00319, 10, 103.68, -13.38, 0.00103, 8, 17.79, -22.73, 0.68782, 7, 40.59, 35.52, 0.2, 6, 14, 75.54, 70.39, 0.09235, 13, 93.95, 33.3, 0.0379, 12, 98.05, 15.17, 0.00424, 10, 102.4, -19.04, 0.00259, 8, 16.52, -28.39, 0.66293, 7, 39.31, 29.87, 0.2, 2, 5, 56.85, -12.25, 0.85, 7, 16.67, -7.74, 0.15, 2, 5, 42.68, -10.9, 0.792, 6, 9.82, 11.78, 0.208, 2, 5, 32.6, -5.37, 0.784, 6, -0.26, 17.31, 0.216, 2, 5, 19.51, 11.02, 0.9, 7, -20.67, 15.52, 0.1, 2, 5, 8.05, -24.24, 0.9, 7, -32.13, -19.73, 0.1, 2, 5, 25.45, -36.05, 0.7, 6, -7.41, -13.37, 0.3, 2, 5, 37.35, -38.72, 0.85, 7, -2.83, -34.22, 0.15, 2, 5, 46.56, -33.76, 0.85, 7, 6.38, -29.26, 0.15, 2, 5, 43.77, -26.32, 0.85, 7, 3.6, -21.81, 0.15, 2, 5, 8.49, -15.64, 0.85, 7, -31.69, -11.13, 0.15, 2, 5, 19.17, -13.07, 0.84, 6, -13.69, 9.61, 0.16, 2, 5, 30.07, -29.65, 0.7, 6, -2.79, -6.97, 0.3, 2, 5, 33.61, -20.84, 0.7, 6, 0.75, 1.83, 0.3, 2, 5, 37.89, -8.27, 0.7, 6, 5.03, 14.41, 0.3, 2, 5, 13.8, -22.3, 0.84, 6, -19.06, 0.38, 0.16, 2, 5, 39.31, -34.54, 0.736, 6, 6.45, -11.86, 0.264, 2, 5, 26.17, -10.2, 0.832, 6, -6.69, 12.48, 0.168, 2, 5, 42.57, -18.49, 0.736, 6, 9.71, 4.19, 0.264, 2, 5, 35.48, -15.33, 0.7, 6, 2.63, 7.35, 0.3, 2, 5, 39.12, -1.98, 0.7, 6, 6.26, 20.7, 0.3, 2, 5, 44, -4.36, 0.7, 6, 11.14, 18.32, 0.3, 2, 5, 57.44, -3.3, 0.88, 6, 24.58, 19.38, 0.12, 2, 5, 62.93, -7.21, 0.85, 7, 22.75, -2.7, 0.15, 2, 5, 60.99, -10.31, 0.85, 7, 20.81, -5.8, 0.15, 2, 5, 31.36, 12.49, 0.84, 6, -1.5, 35.17, 0.16, 2, 5, 22.51, 19.38, 0.9, 7, -17.67, 23.88, 0.1, 2, 5, 19.49, 15.74, 0.9, 7, -20.69, 20.25, 0.1, 2, 5, 49.47, -18.84, 0.85, 7, 9.29, -14.33, 0.15, 2, 5, 10.29, 3.83, 0.9, 7, -29.89, 8.34, 0.1, 2, 5, 8.32, -5.48, 0.9, 7, -31.86, -0.98, 0.1, 2, 5, 26.71, 8.98, 0.84, 6, -6.15, 31.66, 0.16, 2, 5, 24.39, 4.9, 0.84, 6, -8.47, 27.58, 0.16, 2, 5, 36, -4.26, 0.784, 6, 3.14, 18.42, 0.216, 2, 5, 42.92, -8.37, 0.8, 6, 10.06, 14.31, 0.2, 2, 5, 57.55, -8.37, 0.856, 6, 24.69, 14.31, 0.144, 2, 5, 19.11, -0.46, 0.84, 6, -13.75, 22.22, 0.16, 2, 5, 15.36, -5.86, 0.784, 6, -17.5, 16.82, 0.216, 2, 5, 45.65, -19.66, 0.736, 6, 12.79, 3.02, 0.264, 2, 5, 50.67, -3.83, 0.736, 6, 17.81, 18.85, 0.264, 2, 5, 51.28, -8.37, 0.736, 6, 18.42, 14.31, 0.264, 2, 5, 48.94, -11.5, 0.736, 6, 16.08, 11.18, 0.264, 2, 5, 47.04, -16.3, 0.736, 6, 14.18, 6.38, 0.264, 2, 5, 52.8, -15.86, 0.85, 7, 12.62, -11.36, 0.15, 3, 5, 45.21, 3.79, 0.595, 7, 5.03, 8.29, 0.15, 6, 12.35, 26.47, 0.255, 3, 5, 41.09, 18.65, 0.5916, 7, 0.91, 23.15, 0.15, 6, 8.23, 41.33, 0.2584, 2, 5, 34.41, 19.75, 0.9, 7, -5.77, 24.26, 0.1, 2, 5, 29.46, 27.64, 0.9, 7, -10.72, 32.15, 0.1, 2, 5, 22.14, 25.66, 0.9, 7, -18.04, 30.17, 0.1, 3, 5, 59.84, 7.46, 0.6256, 7, 19.66, 11.97, 0.15, 6, 26.98, 30.14, 0.2244, 3, 5, 61.96, 3.92, 0.7548, 7, 21.78, 8.43, 0.15, 6, 29.1, 26.6, 0.0952, 1, 5, 66.03, 7.04, 1, 3, 5, 64.76, 0.59, 0.7412, 7, 24.58, 5.09, 0.1308, 6, 31.9, 23.27, 0.128, 1, 5, 40.81, 64.72, 1, 1, 5, 47.06, 60.1, 1, 2, 5, 46.11, 57.58, 0.76, 6, 13.25, 80.26, 0.24, 1, 5, 35.79, 57.36, 1, 1, 5, 23.91, 50.61, 1, 1, 5, 17.39, 39.7, 1, 2, 5, 14.82, 18.39, 0.76, 6, -18.04, 41.07, 0.24, 1, 5, 3.84, 18.33, 1, 1, 5, -4.84, 34.44, 1, 1, 5, -1.14, 52.88, 1, 1, 5, 13.26, 65.11, 1, 1, 5, 75.91, 12.8, 1, 1, 5, 69.46, 10.2, 1, 5, 14, 62.61, 102.95, 3e-05, 13, 81.02, 65.87, 0, 8, 3.58, 4.17, 0.55997, 7, 26.38, 62.43, 0.2, 6, 33.7, 80.6, 0.24, 7, 14, 60.42, 94.67, 0.00611, 13, 78.83, 57.58, 0.0031, 12, 82.93, 39.45, 0.00105, 10, 87.28, 5.24, 0.00183, 9, 81.94, -25, 0.00077, 8, 1.4, -4.11, 0.68713, 6, 31.51, 72.32, 0.3, 8, 14, 56.99, 84.03, 0.02926, 13, 75.4, 46.94, 0.01636, 12, 79.5, 28.81, 0.008, 10, 83.85, -5.4, 0.01363, 9, 78.51, -35.65, 0.00435, 8, -2.04, -14.75, 0.54439, 5, 60.94, 39, 0.084, 6, 28.08, 61.68, 0.3, 8, 14, 53.53, 72.03, 0.05509, 13, 71.94, 34.95, 0.03171, 12, 76.04, 16.82, 0.0141, 10, 80.38, -17.4, 0.02142, 9, 75.05, -47.64, 0.00391, 8, -5.5, -26.75, 0.40016, 5, 57.48, 27, 0.1736, 6, 24.62, 49.68, 0.3, 8, 14, 48.09, 58.89, 0.05737, 13, 66.5, 21.81, 0.03383, 12, 70.6, 3.68, 0.01244, 10, 74.95, -30.54, 0.0168, 9, 69.61, -60.78, 0.00158, 8, -10.93, -39.89, 0.19158, 5, 52.04, 13.87, 0.3864, 6, 19.18, 36.54, 0.3, 6, 14, 73.77, 81.59, 0.06734, 13, 92.18, 44.51, 0.02849, 12, 96.28, 26.38, 0.00258, 10, 100.62, -7.84, 0.00038, 8, 14.74, -17.19, 0.72121, 6, 44.86, 59.24, 0.18, 8, 14, 70.07, 73.56, 0.06803, 13, 88.48, 36.47, 0.02912, 12, 92.58, 18.34, 0.00408, 10, 96.93, -15.87, 0.00315, 9, 91.59, -46.11, 3e-05, 8, 11.05, -25.22, 0.55159, 5, 74.02, 28.53, 0.164, 6, 41.16, 51.21, 0.18, 8, 14, 64.16, 65.73, 0.08675, 13, 82.57, 28.65, 0.03875, 12, 86.67, 10.52, 0.00765, 10, 91.01, -23.7, 0.00867, 9, 85.68, -53.94, 0.00032, 8, 5.13, -33.05, 0.4745, 5, 68.11, 20.7, 0.20336, 6, 35.25, 43.38, 0.18, 8, 14, 58.16, 59.05, 0.06592, 13, 76.57, 21.96, 0.0315, 12, 80.67, 3.83, 0.00748, 10, 85.02, -30.38, 0.00926, 9, 79.68, -60.62, 0.00044, 8, -0.86, -39.73, 0.25276, 5, 62.11, 14.02, 0.45264, 6, 29.26, 36.7, 0.18, 8, 14, 64.59, 77.69, 0.05887, 13, 83, 40.6, 0.02733, 12, 87.1, 22.47, 0.00598, 10, 91.44, -11.74, 0.00661, 9, 86.11, -41.99, 0.00051, 8, 5.56, -21.09, 0.61526, 5, 68.54, 32.66, 0.05544, 6, 35.68, 55.34, 0.23, 8, 14, 59.43, 69.74, 0.06871, 13, 77.84, 32.65, 0.03426, 12, 81.94, 14.52, 0.01014, 10, 86.29, -19.69, 0.0134, 9, 80.95, -49.93, 0.00137, 8, 0.41, -29.04, 0.45116, 5, 63.38, 24.71, 0.19096, 6, 30.52, 47.39, 0.23, 8, 14, 53.51, 59.04, 0.06254, 13, 71.92, 21.96, 0.03274, 12, 76.02, 3.83, 0.00972, 10, 80.36, -30.39, 0.01269, 9, 75.03, -60.63, 0.00091, 8, -5.52, -39.74, 0.22636, 5, 57.46, 14.01, 0.42504, 6, 24.6, 36.69, 0.23, 3, 5, 53.63, 5.9, 0.6392, 7, 13.45, 10.41, 0.15, 6, 20.77, 28.58, 0.2108, 8, 14, 43.48, 63.38, 0.05239, 13, 61.89, 26.3, 0.03616, 12, 66, 8.17, 0.01834, 10, 70.34, -26.05, 0.02668, 9, 65, -56.29, 0.0039, 8, -15.54, -35.4, 0.20749, 5, 47.44, 18.35, 0.42504, 6, 14.58, 41.03, 0.23, 8, 14, 46.39, 74.85, 0.05046, 13, 64.8, 37.76, 0.0355, 12, 68.9, 19.63, 0.02433, 10, 73.25, -14.58, 0.04207, 9, 67.91, -44.83, 0.01159, 8, -12.63, -23.93, 0.41509, 5, 50.34, 29.82, 0.19096, 6, 17.49, 52.5, 0.23, 8, 14, 47.17, 87.98, 0.02076, 13, 65.58, 50.89, 0.01616, 12, 69.69, 32.77, 0.01876, 10, 74.03, -1.45, 0.04316, 9, 68.69, -31.69, 0.02449, 8, -11.85, -10.8, 0.55427, 5, 51.13, 42.95, 0.0924, 6, 18.27, 65.63, 0.23, 7, 14, 50.86, 98.07, 0.00357, 13, 69.27, 60.99, 0.00333, 12, 73.38, 42.86, 0.00915, 10, 77.72, 8.64, 0.02894, 9, 72.38, -21.6, 0.02807, 8, -8.16, -0.71, 0.69694, 6, 21.96, 75.72, 0.23, 7, 14, 41.83, 96, 0.00977, 13, 60.24, 58.92, 0.01035, 12, 64.34, 40.79, 0.02703, 10, 68.69, 6.57, 0.08236, 9, 63.35, -23.67, 0.07231, 8, -17.19, -2.78, 0.67017, 6, 12.93, 73.65, 0.128, 8, 14, 32.87, 89.65, 0.01764, 13, 51.28, 52.57, 0.02014, 12, 55.38, 34.44, 0.04268, 10, 59.72, 0.22, 0.11436, 9, 54.39, -30.02, 0.07351, 8, -26.16, -9.13, 0.42927, 5, 36.82, 44.63, 0.1744, 6, 3.96, 67.3, 0.128, 8, 14, 27.25, 80.31, 0.01945, 13, 45.65, 43.22, 0.02348, 12, 49.76, 25.09, 0.03814, 10, 54.1, -9.12, 0.08256, 9, 48.77, -39.36, 0.03189, 8, -31.78, -18.47, 0.19514, 5, 31.2, 35.28, 0.48134, 6, -1.66, 57.96, 0.128, 8, 14, 38.01, 77.63, 0.04453, 13, 56.42, 40.55, 0.04008, 12, 60.52, 22.42, 0.04159, 10, 64.87, -11.8, 0.08028, 9, 59.53, -42.04, 0.02806, 8, -21.01, -21.15, 0.40918, 5, 41.96, 32.6, 0.21229, 6, 9.1, 55.28, 0.144, 8, 14, 39.61, 69.26, 0.04157, 13, 58.02, 32.17, 0.03353, 12, 62.12, 14.05, 0.02398, 10, 66.47, -20.17, 0.03881, 9, 61.13, -50.41, 0.0085, 8, -19.42, -29.52, 0.22096, 5, 43.56, 24.23, 0.45264, 6, 10.7, 46.91, 0.18, 3, 5, 61.02, -16.04, 0.608, 15, 11.39, 25.25, 0.192, 6, 28.16, 6.64, 0.2, 2, 5, 68.14, -15.85, 0.8, 6, 35.28, 6.83, 0.2, 2, 5, 75.19, -18.7, 0.8, 6, 42.33, 3.98, 0.2, 3, 5, 55.83, -21.09, 0.3328, 15, 6.2, 20.21, 0.4672, 6, 22.97, 1.59, 0.2, 3, 5, 52.04, -25.56, 0.5056, 15, 2.41, 15.73, 0.2944, 6, 19.18, -2.88, 0.2, 3, 15, 6.11, 10.49, 0.64, 7, 15.56, -26.3, 0.16, 6, 22.88, -8.12, 0.2, 3, 15, 7.4, 5.86, 0.64, 7, 16.84, -30.93, 0.16, 6, 24.16, -12.75, 0.2, 3, 15, -1.47, 1.11, 0.64, 7, 7.97, -35.67, 0.16, 6, 15.29, -17.5, 0.2, 3, 15, -3.59, -4.35, 0.64, 7, 5.85, -41.14, 0.16, 6, 13.17, -22.96, 0.2, 2, 15, -5.45, -7.87, 0.8, 7, 4, -44.65, 0.2, 5, 14, 16.3, -5.09, 0.72618, 13, 34.71, -42.18, 0.03386, 10, 43.16, -94.52, 6e-05, 8, -42.72, -103.87, 0.0399, 6, -12.6, -27.44, 0.2, 6, 14, 19.01, 1.49, 0.67505, 13, 37.42, -35.6, 0.06484, 12, 41.52, -53.73, 1e-05, 10, 45.87, -87.94, 0.00078, 8, -40.01, -97.29, 0.05932, 6, -9.9, -20.86, 0.2, 3, 5, 23.19, -41.25, 0.5916, 7, -16.99, -36.74, 0.15, 6, -9.67, -18.57, 0.2584, 3, 5, 28.22, -41.27, 0.4872, 7, -11.96, -36.77, 0.3, 6, -4.64, -18.59, 0.2128, 3, 5, 12.31, -34.62, 0.756, 7, -27.87, -30.12, 0.1, 6, -20.54, -11.94, 0.144, 2, 5, 6.54, -30.81, 0.85, 7, -33.64, -26.3, 0.15, 2, 12, -16.13, 2.07, 0.8, 7, -74.87, 16.76, 0.2, 4, 13, -9.98, 19.65, 0.00747, 12, -5.88, 1.53, 0.63253, 7, -64.62, 16.22, 0.16, 6, -57.3, 34.39, 0.2, 7, 14, -18.68, 54.6, 0.003, 13, -0.27, 17.51, 0.05615, 12, 3.83, -0.62, 0.54628, 10, 8.18, -34.83, 0.02767, 8, -77.7, -44.18, 0.00691, 7, -54.91, 14.07, 0.16, 6, -47.59, 32.25, 0.2, 7, 14, -5.17, 49.51, 0.05776, 13, 13.23, 12.42, 0.32738, 12, 17.34, -5.71, 0.26372, 10, 21.68, -39.92, 0.09557, 9, 16.34, -70.17, 0.00077, 8, -64.2, -49.27, 0.05479, 7, -41.4, 8.98, 0.2, 4, 13, -13.31, 7.11, 0.17497, 12, -9.21, -11.02, 0.46503, 7, -67.95, 3.67, 0.16, 6, -60.63, 21.84, 0.2, 6, 13, -3.88, 9.2, 0.25418, 12, 0.23, -8.93, 0.38249, 10, 4.57, -43.15, 0.00252, 8, -81.31, -52.5, 0.00081, 7, -58.51, 5.76, 0.16, 6, -51.19, 23.93, 0.2, 7, 14, -10.78, 46.34, 0.02695, 13, 7.63, 9.25, 0.3414, 12, 11.73, -8.88, 0.21169, 10, 16.08, -43.09, 0.0393, 8, -69.81, -52.44, 0.02065, 7, -47.01, 5.81, 0.16, 6, -39.69, 23.99, 0.2, 5, 14, -24.04, 34.63, 7e-05, 13, -5.63, -2.45, 0.62844, 12, -1.53, -20.58, 0.01149, 7, -60.26, -5.89, 0.16, 6, -52.94, 12.28, 0.2, 7, 14, -11.72, 37.71, 0.041, 13, 6.69, 0.63, 0.53956, 12, 10.79, -17.5, 0.03814, 10, 15.14, -51.72, 0.01063, 8, -70.74, -61.07, 0.01066, 7, -47.95, -2.81, 0.16, 6, -40.63, 15.36, 0.2, 8, 14, -1.74, 41.72, 0.09769, 13, 16.67, 4.63, 0.36127, 12, 20.77, -13.5, 0.09331, 10, 25.11, -47.71, 0.04261, 9, 19.78, -77.95, 0.00013, 8, -60.77, -57.06, 0.04499, 7, -37.97, 1.2, 0.16, 6, -30.65, 19.37, 0.2, 8, 14, 2.47, 55.05, 0.05875, 13, 20.88, 17.97, 0.17786, 12, 24.98, -0.16, 0.1813, 10, 29.33, -34.37, 0.12969, 9, 23.99, -64.62, 0.00466, 8, -56.55, -43.72, 0.08773, 7, -33.75, 14.53, 0.16, 6, -26.43, 32.71, 0.2, 8, 14, -5.21, 58.96, 0.02606, 13, 13.2, 21.87, 0.11882, 12, 17.3, 3.74, 0.26637, 10, 21.64, -30.47, 0.16637, 9, 16.31, -60.71, 0.00389, 8, -64.24, -39.82, 0.05849, 7, -41.44, 18.44, 0.16, 6, -34.12, 36.61, 0.2, 8, 14, -11.19, 66.99, 0.00858, 13, 7.22, 29.9, 0.03533, 12, 11.32, 11.77, 0.24673, 10, 15.67, -22.44, 0.29499, 9, 10.33, -52.68, 0.00612, 8, -70.21, -31.79, 0.04826, 7, -47.42, 26.47, 0.16, 6, -40.09, 44.64, 0.2, 4, 11, -3.1, -3.11, 0.63985, 10, -25.96, -10.42, 0.00015, 7, -89.05, 38.49, 0.16, 6, -81.73, 56.66, 0.2, 5, 11, 6.37, 3.51, 0.44856, 10, -16.49, -3.79, 0.19116, 9, -21.82, -34.03, 0.00028, 7, -79.57, 45.12, 0.16, 6, -72.25, 63.29, 0.2, 5, 11, 17.97, 7.92, 0.08936, 10, -4.89, 0.61, 0.54122, 9, -10.22, -29.63, 0.00942, 7, -67.97, 49.52, 0.16, 6, -60.65, 67.69, 0.2, 7, 14, -17.71, 91.91, 6e-05, 12, 4.8, 36.69, 0.02602, 10, 9.15, 2.48, 0.50666, 9, 3.81, -27.76, 0.08525, 8, -76.74, -6.87, 0.022, 7, -53.94, 51.38, 0.16, 6, -46.62, 69.56, 0.2, 3, 9, -3.28, 8.21, 0.64, 7, -61.03, 87.36, 0.16, 6, -53.71, 105.53, 0.2, 4, 10, 2.7, 28.36, 0.02623, 9, -2.64, -1.88, 0.61377, 7, -60.38, 77.27, 0.16, 6, -53.06, 95.44, 0.2, 6, 12, 7.04, 53.43, 0.00768, 10, 11.39, 19.21, 0.2053, 9, 6.05, -11.03, 0.40172, 8, -74.5, 9.86, 0.02531, 7, -51.7, 68.12, 0.16, 6, -44.38, 86.29, 0.2, 8, 14, -9.86, 103.79, 4e-05, 13, 8.55, 66.71, 0.0001, 12, 12.65, 48.58, 0.0199, 10, 17, 14.36, 0.27302, 9, 11.66, -15.88, 0.29582, 8, -68.88, 5.01, 0.05111, 7, -46.09, 63.27, 0.16, 6, -38.77, 81.44, 0.2, 6, 12, 19.3, 67.71, 0.01061, 10, 23.64, 33.49, 0.0846, 9, 18.31, 3.25, 0.46904, 8, -62.24, 24.14, 0.07574, 7, -39.44, 82.4, 0.16, 6, -32.12, 100.57, 0.2, 7, 13, 16.14, 75.43, 3e-05, 12, 20.25, 57.3, 0.01728, 10, 24.59, 23.08, 0.15183, 9, 19.26, -7.16, 0.38318, 8, -61.29, 13.73, 0.08768, 7, -38.49, 71.99, 0.16, 6, -31.17, 90.17, 0.2, 1, 5, 6.17, 59.1, 1, 8, 14, 8.6, 117.31, 0, 13, 27.01, 80.22, 5e-05, 12, 31.11, 62.09, 0.02087, 10, 35.46, 27.88, 0.13256, 9, 30.12, -2.36, 0.33182, 8, -50.43, 18.53, 0.15469, 7, -27.63, 76.79, 0.16, 6, -20.31, 94.96, 0.2, 8, 14, 16.86, 114.2, 0.00011, 13, 35.27, 77.11, 0.00047, 12, 39.37, 58.99, 0.02523, 10, 43.72, 24.77, 0.13636, 9, 38.38, -5.47, 0.25465, 8, -42.16, 15.42, 0.22318, 7, -19.37, 73.68, 0.16, 6, -12.04, 91.85, 0.2, 6, 13, 52.8, 85.42, 0, 12, 56.91, 67.29, 0.0163, 10, 61.25, 33.08, 0.08397, 9, 55.92, 2.84, 0.1333, 8, -24.63, 23.73, 0.56643, 7, -1.83, 81.99, 0.2, 7, 14, 37.72, 115.65, 2e-05, 13, 56.13, 78.56, 0.00017, 12, 60.23, 60.44, 0.01823, 10, 64.58, 26.22, 0.08686, 9, 59.24, -4.02, 0.12912, 8, -21.3, 16.87, 0.56561, 7, 1.49, 75.13, 0.2, 2, 5, 94.88, 31.89, 0.784, 6, 62.02, 54.57, 0.216, 2, 5, 94.39, 19.9, 0.784, 6, 61.53, 42.58, 0.216, 2, 5, 88.18, 9.49, 0.784, 6, 55.32, 32.17, 0.216, 2, 5, 77.47, 4.61, 0.784, 6, 44.62, 27.28, 0.216, 2, 5, 33.61, 61.84, 0.76, 6, 0.75, 84.52, 0.24, 2, 5, 18.47, 58.01, 0.76, 6, -14.38, 80.69, 0.24, 2, 5, 8.93, 49.65, 0.76, 6, -23.93, 72.33, 0.24, 2, 5, 5.88, 36.59, 0.76, 6, -26.98, 59.27, 0.24, 3, 5, 11.18, 26.66, 0.75706, 7, -29, 31.17, 0.00294, 6, -21.68, 49.34, 0.24], "hull": 118, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 0, 234, 244, 246, 246, 248, 248, 250, 250, 252, 244, 254, 254, 256, 246, 258, 252, 260, 260, 256, 258, 260, 238, 262, 262, 240, 254, 264, 264, 258, 258, 266, 266, 250, 240, 268, 238, 270, 270, 252, 268, 256, 260, 272, 272, 262, 262, 274, 262, 276, 278, 280, 280, 282, 282, 236, 274, 284, 284, 286, 286, 288, 288, 242, 290, 252, 242, 292, 292, 294, 294, 254, 284, 296, 240, 298, 298, 242, 296, 298, 296, 300, 298, 306, 306, 308, 308, 268, 292, 308, 270, 310, 310, 252, 308, 254, 276, 312, 312, 278, 302, 314, 314, 304, 312, 314, 236, 316, 316, 238, 314, 316, 316, 318, 236, 320, 320, 290, 322, 324, 324, 326, 326, 328, 328, 330, 330, 286, 332, 334, 334, 336, 336, 338, 338, 280, 192, 340, 340, 342, 342, 344, 344, 346, 346, 348, 348, 350, 350, 330, 330, 352, 352, 354, 354, 356, 356, 358, 360, 192, 4, 362, 362, 364, 364, 336, 216, 366, 366, 368, 368, 370, 370, 372, 372, 374, 228, 376, 376, 378, 378, 380, 380, 382, 224, 384, 384, 386, 386, 388, 322, 390, 390, 332, 392, 394, 394, 396, 396, 398, 400, 402, 402, 404, 404, 406, 410, 412, 412, 414, 410, 416, 416, 418, 418, 420, 420, 422, 422, 36, 250, 424, 424, 426, 426, 428, 64, 430, 430, 432, 432, 434, 434, 436, 436, 248, 434, 438, 438, 440, 440, 244, 122, 442, 442, 444, 444, 446, 446, 448, 110, 450, 450, 452, 452, 454, 102, 456, 456, 458, 458, 460, 462, 464, 464, 466, 146, 468, 468, 470, 470, 472, 472, 474, 170, 476, 476, 478, 478, 480, 480, 482, 180, 484, 484, 486, 358, 488, 488, 360, 486, 488, 490, 492, 198, 494, 494, 496, 8, 498, 10, 12, 12, 14, 498, 500, 500, 502, 502, 504, 16, 18, 18, 20, 504, 338, 344, 506, 506, 508, 508, 510, 510, 512, 512, 514], "width": 146, "height": 138}}, "niuyao_013": {"niuyao_013": {"type": "mesh", "uvs": [0.38958, 0.63324, 0.75572, 0.63523, 0.85082, 0.36175, 0.99917, 0.36272, 0.97351, 0.9102, 0.78335, 0.91284, 0.75557, 0.63865, 0.39265, 0.63737, 0.15895, 0.9376, 0.02409, 0.58654, 0, 1e-05, 0.20094, 0.08958], "triangles": [9, 10, 11, 7, 0, 1, 6, 7, 1, 4, 2, 3, 5, 6, 1, 1, 2, 5, 4, 5, 2, 8, 9, 11, 8, 11, 0, 8, 0, 7], "vertices": [3, 6, -1.74, 18.99, 0.26074, 7, -9.06, 0.82, 0.224, 5, 31.12, -3.69, 0.51526, 3, 6, 10.23, 12.64, 0.26074, 7, 2.91, -5.53, 0.224, 5, 43.09, -10.04, 0.51526, 3, 6, 14.74, 13.66, 0.26074, 7, 7.42, -4.51, 0.224, 5, 47.6, -9.02, 0.51526, 3, 6, 19.59, 11.08, 0.26074, 7, 12.27, -7.09, 0.224, 5, 52.45, -11.6, 0.51526, 3, 6, 15.94, 6.2, 0.26074, 7, 8.62, -11.97, 0.224, 5, 48.8, -16.48, 0.51526, 3, 6, 9.71, 9.46, 0.26074, 7, 2.39, -8.71, 0.224, 5, 42.56, -13.22, 0.51526, 3, 6, 10.21, 12.61, 0.26074, 7, 2.89, -5.56, 0.224, 5, 43.06, -10.07, 0.51526, 3, 6, -1.66, 18.9, 0.26074, 7, -8.98, 0.72, 0.224, 5, 31.2, -3.78, 0.51526, 3, 6, -10.85, 20.01, 0.26074, 7, -18.17, 1.84, 0.224, 5, 22.01, -2.66, 0.51526, 3, 6, -13.46, 25.76, 0.26074, 7, -20.78, 7.59, 0.224, 5, 19.4, 3.08, 0.51526, 3, 6, -11.23, 31.88, 0.26074, 7, -18.55, 13.71, 0.224, 5, 21.63, 9.2, 0.51526, 3, 6, -5.12, 27.54, 0.26074, 7, -12.44, 9.36, 0.224, 5, 27.74, 4.86, 0.51526], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 37, "height": 11}}, "niuyao_01": {"niuyao_01": {"type": "mesh", "uvs": [0.03387, 0.22975, 0.22506, 0, 0.47302, 0.18996, 0.41354, 0.45855, 1, 0.87706, 1, 1, 0.91202, 1, 0.37937, 0.54547, 0.28071, 0.77789, 0, 0.5338], "triangles": [3, 1, 2, 0, 1, 3, 7, 9, 0, 3, 7, 0, 8, 9, 7, 6, 3, 4, 7, 3, 6, 6, 4, 5], "vertices": [250.27, -4.22, 223.42, -69.13, 144.48, -67.38, 135.01, -12.46, -48.92, -13.73, -60.04, 7.92, -38.12, 19.17, 135.66, 7.22, 139.22, 60.77, 231.22, 53.67], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 280, "height": 198}}, "niuyao_02": {"niuyao_02": {"type": "mesh", "uvs": [0.89398, 0.01387, 0.98951, 0.04519, 0.99072, 0.10934, 0.96194, 0.18182, 0.9266, 0.27085, 0.84941, 0.46527, 0.80647, 0.52269, 0.73484, 0.58261, 0.57104, 0.66473, 0.41215, 0.74437, 0.33392, 0.85856, 0.31723, 0.98542, 0.26598, 1, 0.25277, 0.81845, 0.2792, 0.66566, 0.27665, 0.52379, 0.22579, 0.50841, 0.12932, 0.54121, 0.0408, 0.67841, 0.00025, 0.67467, 0.05337, 0.53525, 0.15082, 0.40769, 0.27967, 0.35168, 0.49542, 0.33651, 0.44378, 0.25557, 0.39033, 0.17179, 0.32574, 0.07054, 0.36526, 0.06993, 0.47533, 0.12404, 0.52941, 0.12597, 0.68059, 0.01389, 0.87523, 0.10092, 0.74039, 0.20208, 0.58962, 0.30805, 0.41678, 0.14588, 0.49155, 0.20368, 0.56878, 0.1796, 0.70729, 0.10414, 0.79678, 0.0656, 0.88626, 0.05436, 0.29052, 0.8347, 0.342, 0.70464, 0.47071, 0.56335, 0.60433, 0.48949, 0.73549, 0.38673, 0.85071, 0.26952, 0.92303, 0.12661, 0.09562, 0.53123, 0.17529, 0.45898, 0.30033, 0.42205, 0.47071, 0.44292, 0.61045, 0.39636, 0.73916, 0.28718, 0.82129, 0.21492, 0.8057, 0.15308], "triangles": [46, 31, 1, 45, 53, 46, 40, 13, 41, 12, 13, 40, 2, 46, 1, 3, 46, 2, 4, 45, 46, 3, 4, 46, 10, 40, 41, 11, 40, 10, 12, 40, 11, 6, 44, 5, 9, 42, 8, 8, 42, 43, 8, 43, 7, 7, 43, 44, 7, 44, 6, 9, 41, 42, 5, 44, 45, 10, 41, 9, 5, 45, 4, 42, 50, 43, 43, 51, 44, 42, 41, 15, 45, 44, 52, 47, 21, 48, 47, 20, 21, 17, 47, 48, 17, 48, 16, 19, 20, 47, 18, 19, 47, 18, 47, 17, 49, 22, 23, 50, 49, 23, 48, 21, 22, 48, 22, 49, 16, 48, 49, 15, 16, 49, 49, 50, 42, 15, 49, 42, 41, 14, 15, 13, 14, 41, 0, 38, 30, 39, 0, 1, 39, 38, 0, 31, 38, 39, 31, 39, 1, 37, 30, 38, 30, 36, 29, 34, 27, 28, 26, 27, 34, 54, 38, 31, 37, 38, 54, 25, 26, 34, 37, 36, 30, 32, 37, 54, 35, 28, 29, 35, 29, 36, 34, 28, 35, 53, 54, 31, 32, 54, 53, 53, 31, 46, 24, 34, 35, 25, 34, 24, 32, 36, 37, 52, 32, 53, 32, 33, 36, 52, 33, 32, 35, 36, 33, 23, 35, 33, 24, 35, 23, 52, 51, 33, 45, 52, 53, 44, 51, 52, 51, 50, 23, 51, 23, 33, 43, 50, 51], "vertices": [1, 47, 7.1, -31.46, 1, 2, 47, 3.07, -39.68, 0.232, 4, 93.09, 16.07, 0.768, 3, 47, -1.41, -38.84, 0.23199, 48, 10.58, -65.38, 1e-05, 4, 88.62, 16.91, 0.768, 3, 47, -5.88, -35.14, 0.23161, 48, 6.11, -61.68, 0.0004, 4, 84.15, 20.6, 0.768, 3, 47, -11.37, -30.6, 0.22904, 48, 0.62, -57.15, 0.00296, 4, 78.65, 25.14, 0.768, 3, 47, -23.37, -20.69, 0.2086, 48, -11.38, -47.23, 0.0234, 4, 66.66, 35.06, 0.768, 3, 47, -26.52, -15.93, 0.19654, 48, -14.52, -42.48, 0.03546, 4, 63.51, 39.81, 0.768, 3, 47, -29.28, -8.53, 0.17206, 48, -17.29, -35.07, 0.05994, 4, 60.75, 47.22, 0.768, 3, 47, -31.79, 7.59, 0.08787, 48, -19.8, -18.95, 0.14413, 4, 58.24, 63.34, 0.768, 3, 47, -34.22, 23.22, 0.01342, 48, -22.23, -3.32, 0.21858, 4, 55.81, 78.97, 0.768, 3, 47, -40.63, 32.03, 0.00025, 48, -28.63, 5.49, 0.23175, 4, 49.4, 87.78, 0.768, 2, 48, -37.12, 8.9, 0.232, 4, 40.92, 91.19, 0.768, 2, 48, -37.13, 13.77, 0.232, 4, 40.91, 96.06, 0.768, 1, 48, -24.27, 12.27, 1, 2, 47, -26.17, 34.14, 0.0022, 48, -14.18, 7.6, 0.9978, 2, 48, -4.28, 5.72, 0.92496, 49, -4.6, -18.18, 0.07504, 2, 48, -2.22, 10.12, 0.64642, 49, -2.55, -13.79, 0.35358, 2, 48, -2.62, 19.38, 0.08643, 49, -2.94, -4.53, 0.91357, 1, 49, -10.74, 5.57, 1, 1, 49, -9.69, 9.2, 1, 2, 48, -0.72, 26.19, 0.00032, 49, -1.05, 2.29, 0.99968, 2, 48, 6.24, 15.44, 0.38452, 49, 5.91, -8.47, 0.61548, 3, 47, -4.38, 29.43, 0.00019, 48, 7.61, 2.88, 0.91291, 49, 7.29, -21.02, 0.0869, 2, 47, -7.53, 9.58, 0.64167, 48, 4.46, -16.96, 0.35833, 2, 47, -0.9, 13.07, 0.9489, 48, 11.09, -13.47, 0.0511, 2, 47, 5.95, 16.69, 0.99839, 48, 17.95, -9.85, 0.00161, 1, 47, 14.24, 21.06, 1, 1, 47, 13.51, 17.45, 1, 2, 47, 7.61, 8.25, 0.99904, 48, 19.6, -18.29, 0.00096, 1, 47, 6.42, 3.36, 1, 1, 47, 11.26, -12.06, 1, 2, 47, 1.42, -28.46, 0.99957, 48, 13.42, -55, 0.00043, 2, 47, -2.97, -14.69, 0.98661, 48, 9.02, -41.24, 0.01339, 2, 47, -7.39, 0.59, 0.86191, 48, 4.6, -25.95, 0.13809, 2, 47, 7.24, 13.9, 0.99894, 48, 19.23, -12.64, 0.00106, 2, 47, 1.77, 7.96, 0.97372, 48, 13.76, -18.58, 0.02628, 1, 47, 1.93, 0.58, 1, 2, 47, 4.47, -13.14, 0.99972, 48, 16.47, -39.68, 0.00028, 2, 47, 5.41, -21.85, 0.99993, 48, 17.4, -48.39, 7e-05, 2, 47, 4.44, -30.16, 1, 48, 16.43, -56.7, 0, 3, 47, -38.12, 35.63, 6e-05, 48, -26.13, 9.08, 0.23194, 4, 51.9, 91.37, 0.768, 3, 47, -30.1, 29.01, 0.004, 48, -18.11, 2.47, 0.228, 4, 59.93, 84.76, 0.768, 3, 47, -22.8, 15.2, 0.05285, 48, -10.8, -11.34, 0.17915, 4, 67.23, 70.95, 0.768, 3, 47, -20.27, 1.95, 0.13938, 48, -8.28, -24.59, 0.09262, 4, 69.76, 57.7, 0.768, 3, 47, -15.69, -11.5, 0.20407, 48, -3.7, -38.04, 0.02793, 4, 74.33, 44.25, 0.768, 3, 47, -9.8, -23.72, 0.2269, 48, 2.19, -50.27, 0.0051, 4, 80.23, 32.02, 0.768, 3, 47, -1.29, -32.43, 0.23186, 48, 10.7, -58.97, 0.00014, 4, 88.74, 23.32, 0.768, 2, 48, -1.27, 22.29, 0.01396, 49, -1.59, -1.61, 0.98604, 2, 48, 2.2, 13.97, 0.40908, 49, 1.87, -9.93, 0.59092, 3, 47, -9.67, 28.6, 0.00025, 48, 2.33, 2.05, 0.9514, 49, 2, -21.85, 0.04835, 2, 47, -14.44, 13.41, 0.32, 48, -2.44, -13.13, 0.68, 2, 47, -13.93, 0.01, 0.73882, 48, -1.93, -26.53, 0.26118, 2, 47, -8.85, -13.32, 0.95085, 48, 3.14, -39.86, 0.04915, 2, 47, -5.44, -21.86, 0.98764, 48, 6.55, -48.4, 0.01236, 2, 47, -0.84, -21.36, 0.99616, 48, 11.15, -47.9, 0.00384], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 52, 54, 54, 56, 56, 58, 58, 60, 32, 34, 34, 36, 28, 30, 30, 32, 14, 16, 16, 18, 8, 10, 2, 62, 64, 66, 52, 68, 50, 52, 68, 70, 46, 48, 48, 50, 70, 72, 72, 74, 74, 76, 76, 78, 24, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 4, 6, 6, 8, 38, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 62, 108, 108, 64, 106, 92], "width": 93, "height": 71}}, "niuyao_03": {"niuyao_03": {"type": "mesh", "uvs": [0.18617, 0.01341, 0.22275, 0.0638, 0.26237, 0.22076, 0.3175, 0.43913, 0.34225, 0.50049, 0.37512, 0.5626, 0.45063, 0.70527, 0.57143, 0.8353, 0.65516, 0.87655, 0.73442, 0.83891, 0.7585, 0.80901, 0.8617, 0.64272, 0.91762, 0.57394, 0.97239, 0.57543, 0.99513, 0.63136, 0.99474, 0.66667, 0.94883, 0.72688, 0.9127, 0.77425, 0.82871, 0.88439, 0.72514, 0.98648, 0.63305, 0.98922, 0.52169, 0.9242, 0.42898, 0.81, 0.33656, 0.69616, 0.27618, 0.49747, 0.22987, 0.34508, 0.16125, 0.11928, 0.18449, 0.38386, 0.20186, 0.58164, 0.18302, 0.70473, 0.07511, 0.65693, 0.04703, 0.57647, 0.00512, 0.36684, 0.00539, 0.27261, 0.02776, 0.164, 0.06155, 0.08049, 0.1499, 0.01341, 0.93372, 0.66538, 0.88239, 0.71717, 0.79372, 0.84662, 0.72256, 0.91853, 0.64556, 0.92429, 0.54172, 0.87251, 0.43322, 0.74881, 0.35156, 0.62511, 0.29803, 0.46785, 0.2432, 0.26361, 0.18253, 0.0795, 0.1557, 0.06224, 0.1032, 0.13416, 0.0927, 0.31539, 0.13353, 0.5484], "triangles": [33, 34, 50, 32, 33, 50, 50, 26, 27, 26, 50, 49, 51, 50, 27, 31, 32, 50, 31, 50, 51, 51, 27, 28, 30, 31, 51, 29, 51, 28, 30, 51, 29, 49, 35, 36, 49, 34, 35, 50, 34, 49, 48, 36, 0, 47, 48, 0, 47, 0, 1, 26, 48, 47, 48, 49, 36, 49, 48, 26, 46, 1, 2, 47, 1, 46, 46, 26, 47, 25, 26, 46, 46, 2, 3, 45, 25, 46, 3, 45, 46, 24, 25, 45, 45, 3, 4, 44, 45, 4, 44, 24, 45, 23, 24, 44, 44, 4, 5, 43, 5, 6, 44, 5, 43, 23, 44, 43, 22, 23, 43, 42, 6, 7, 43, 6, 42, 21, 43, 42, 22, 43, 21, 41, 7, 8, 41, 8, 40, 42, 7, 41, 20, 42, 41, 21, 42, 20, 37, 12, 13, 38, 11, 12, 37, 13, 14, 15, 37, 14, 37, 38, 12, 16, 37, 15, 17, 38, 37, 17, 37, 16, 39, 10, 11, 39, 11, 38, 18, 39, 38, 18, 38, 17, 40, 8, 9, 39, 40, 9, 39, 9, 10, 19, 40, 39, 19, 39, 18, 19, 20, 41, 19, 41, 40], "vertices": [3, 36, 48.73, -6.37, 0.76337, 37, -5.76, 3.25, 0.23622, 38, -12.31, 24.76, 0.00041, 2, 36, 41.68, -9.07, 0.93707, 37, -6.67, 10.73, 0.06293, 3, 35, 89.86, -19.91, 0.06037, 36, 28.31, -7.2, 0.93928, 37, -1.63, 23.25, 0.00034, 2, 35, 74.86, -8.62, 0.23186, 36, 9.72, -4.6, 0.76814, 2, 35, 69.09, -6.05, 0.51446, 36, 3.43, -5.15, 0.48554, 2, 35, 61.94, -3.95, 0.78741, 36, -3.85, -6.79, 0.21259, 2, 35, 45.5, 0.87, 0.94926, 36, -20.55, -10.55, 0.05074, 2, 34, 77.38, -15.19, 0.11109, 35, 21.8, 1.89, 0.88891, 2, 34, 65.27, -5.72, 0.33328, 35, 6.66, -0.73, 0.66672, 2, 34, 51.29, -1.77, 0.66659, 35, -5.66, -8.43, 0.33341, 2, 34, 46.43, -1.77, 0.88884, 35, -8.92, -12.03, 0.11116, 2, 34, 24.38, -4.26, 0.99998, 35, -21.87, -30.05, 2e-05, 1, 34, 13.13, -4.23, 1, 1, 34, 4.38, 0.3, 1, 1, 34, 2.55, 5.79, 1, 1, 34, 3.78, 8.06, 1, 1, 34, 13.13, 8.27, 1, 1, 34, 20.5, 8.44, 1, 2, 34, 37.62, 8.83, 0.92074, 35, -22.69, -11.45, 0.07926, 2, 34, 57.62, 7.11, 0.73036, 35, -7.99, 2.22, 0.26964, 3, 34, 72.52, -0.16, 0.42887, 35, 7.4, 8.38, 0.57096, 36, -57.51, -22.47, 0.00017, 3, 34, 88.29, -13.41, 0.17481, 35, 27.81, 11.17, 0.74653, 36, -41.02, -10.13, 0.07866, 3, 34, 99.45, -28.36, 0.03185, 35, 46.38, 9.41, 0.69989, 36, -23.92, -2.66, 0.26827, 2, 35, 64.9, 7.65, 0.43135, 36, -6.88, 4.79, 0.56865, 4, 35, 80.26, -1.97, 0.17651, 36, 11.21, 3.84, 0.79426, 37, 13.21, 37.18, 0.02835, 38, 26.48, 27.45, 0.00088, 4, 35, 92.05, -9.34, 0.03278, 36, 25.1, 3.11, 0.79874, 37, 9.15, 23.88, 0.07693, 38, 13.12, 23.57, 0.09155, 3, 36, 45.67, 2.03, 0.58116, 37, 3.13, 4.19, 0.12579, 38, -6.66, 17.84, 0.29305, 3, 36, 28.16, 11.19, 0.27814, 37, 16.26, 18.96, 0.11796, 38, 12.89, 14.93, 0.6039, 3, 36, 15.07, 18.04, 0.08406, 37, 26.07, 30, 0.06967, 38, 27.5, 12.76, 0.84627, 3, 36, 10.35, 26.41, 0.00109, 37, 35.32, 32.56, 0.0208, 38, 34.71, 6.42, 0.97811, 2, 37, 43.97, 14.82, 0.01504, 38, 24.59, -10.52, 0.98496, 2, 37, 42.21, 7.28, 0.07315, 38, 17.31, -13.18, 0.92685, 2, 37, 34.3, -7.84, 0.22338, 38, 0.33, -14.84, 0.77662, 2, 37, 28.72, -11.86, 0.45483, 38, -6.09, -12.36, 0.54517, 3, 36, 58.56, 22.56, 0.04886, 37, 19.94, -13.29, 0.66122, 38, -12.09, -5.8, 0.28992, 3, 36, 59.34, 13.99, 0.2085, 37, 11.43, -11.97, 0.68428, 38, -15.64, 2.05, 0.10723, 3, 36, 52.91, -1.36, 0.47924, 37, -1.91, -2.02, 0.50067, 38, -14.61, 18.66, 0.02009, 1, 34, 13.55, 3.04, 1, 1, 34, 23.5, 2.26, 1, 2, 34, 42, 3.53, 0.90913, 35, -15.82, -11.76, 0.09087, 2, 34, 55.81, 2.47, 0.70714, 35, -5.77, -2.24, 0.29286, 3, 34, 68.38, -3.39, 0.39404, 35, 7.01, 3.14, 0.6058, 36, -55.31, -27.24, 0.00016, 3, 34, 83.37, -15.16, 0.15158, 35, 25.81, 6.35, 0.81784, 36, -40.43, -15.31, 0.03058, 3, 34, 96.76, -32, 0.02024, 35, 47.28, 4.96, 0.80765, 36, -20.99, -6.11, 0.17212, 2, 35, 64.25, 1.84, 0.57555, 36, -4.63, -0.61, 0.42445, 2, 35, 77.37, -5.4, 0.27264, 36, 10.35, -0.57, 0.72736, 3, 35, 91.95, -15.75, 0.08084, 36, 28.13, -2.55, 0.8823, 37, 2.93, 22.31, 0.03686, 2, 36, 45.44, -2.78, 0.81518, 37, -1.48, 5.56, 0.18482, 3, 36, 49.5, 0.12, 0.55616, 37, 0.36, 0.92, 0.34879, 38, -10.91, 18.37, 0.09505, 3, 36, 51.53, 10.74, 0.25973, 37, 10.17, -3.61, 0.43923, 38, -9.33, 7.68, 0.30104, 3, 36, 42.58, 20.67, 0.07445, 37, 21.97, 2.67, 0.31553, 38, 2.38, 1.24, 0.61002, 3, 36, 24.82, 25.93, 0.00021, 37, 31.37, 18.64, 0.22734, 38, 20.89, 2.11, 0.77245], "hull": 37, "edges": [0, 72, 0, 2, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 36, 38, 38, 40, 40, 42, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 34, 36, 28, 74, 30, 32, 32, 34, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 42, 44, 44, 46, 84, 86, 86, 88, 8, 10, 10, 12, 46, 48, 48, 50, 50, 52, 2, 4, 4, 6, 52, 54, 54, 56, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102], "width": 180, "height": 73}}, "niuyao_04": {"niuyao_04": {"type": "mesh", "uvs": [0.63936, 0.30688, 0.7422, 0.50743, 0.84096, 0.69999, 0.83984, 0.79722, 0.83978, 0.8603, 0.74155, 0.95317, 0.66909, 0.97692, 0.4553, 0.97686, 0.40194, 0.95045, 0.36353, 0.84126, 0.34055, 0.81262, 0.2293, 0.75458, 0.05053, 0.66133, 0.005, 0.51176, 0.01485, 0.41327, 0.11135, 0.23134, 0.19522, 0.17921, 0.24442, 0.16248, 0.43837, 0.16275, 0.60432, 0.26679, 0.35137, 0.44175, 0.61359, 0.78889], "triangles": [20, 17, 18, 20, 18, 19, 16, 17, 20, 15, 16, 20, 14, 15, 20, 20, 13, 14, 12, 13, 20, 11, 12, 20, 0, 20, 19, 1, 20, 0, 21, 1, 2, 1, 21, 20, 10, 11, 20, 3, 21, 2, 21, 10, 20, 9, 10, 21, 4, 21, 3, 7, 8, 9, 5, 21, 4, 21, 7, 9, 21, 6, 7, 5, 6, 21], "vertices": [1, 21, 15.85, 28.96, 1, 1, 21, 35, 28.87, 1, 1, 21, 53.39, 28.78, 1, 1, 21, 60.87, 25.03, 1, 1, 21, 65.74, 22.64, 1, 1, 21, 69.42, 11.98, 1, 1, 21, 68.67, 5.81, 1, 1, 21, 61.06, -9.74, 1, 1, 21, 57.13, -12.63, 1, 1, 21, 47.32, -11.3, 1, 1, 21, 44.29, -11.89, 1, 1, 21, 35.85, -17.79, 1, 1, 21, 22.29, -27.28, 1, 1, 21, 9.11, -24.94, 1, 1, 21, 1.85, -20.5, 1, 1, 21, -8.77, -6.61, 1, 1, 21, -9.82, 1.46, 1, 1, 21, -9.36, 5.68, 1, 1, 21, -2.44, 19.78, 1, 1, 21, 11.51, 27.92, 1, 1, 21, 16.02, 2.91, 1, 1, 21, 52.17, 8.88, 1], "hull": 20, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4, 20, 22, 22, 24, 30, 40, 40, 42], "width": 81, "height": 86}}, "niuyao_05": {"niuyao_05": {"type": "mesh", "uvs": [0.71654, 0.01047, 0.89193, 0.10364, 0.99074, 0.18199, 0.98976, 0.24509, 0.96817, 0.30414, 0.79897, 0.45339, 0.74649, 0.46605, 0.55798, 0.79232, 0.44231, 0.85189, 0.41688, 0.93133, 0.36901, 0.90971, 0.2306, 0.93116, 0.1083, 0.93182, 0.06517, 0.97795, 0.02888, 0.97772, 0.01008, 0.79751, 0.01343, 0.7187, 0.0743, 0.58771, 0.21862, 0.43526, 0.34082, 0.40574, 0.467, 0.2872, 0.43759, 0.21033, 0.53034, 0.07928, 0.5975, 0.08052, 0.66288, 0.0115, 0.07517, 0.84883, 0.28993, 0.68311, 0.55887, 0.40077, 0.72913, 0.2289], "triangles": [5, 6, 28, 6, 27, 28, 5, 28, 4, 28, 23, 0, 0, 23, 24, 20, 23, 28, 28, 27, 20, 28, 1, 4, 4, 1, 3, 20, 22, 23, 20, 21, 22, 3, 1, 2, 28, 0, 1, 13, 14, 12, 14, 25, 12, 14, 15, 25, 12, 25, 11, 9, 10, 8, 10, 11, 26, 11, 25, 26, 10, 26, 8, 8, 26, 7, 15, 16, 25, 25, 16, 26, 26, 16, 17, 7, 27, 6, 7, 26, 27, 26, 19, 27, 17, 18, 26, 26, 18, 19, 19, 20, 27], "vertices": [1, 23, 35.73, 12.02, 1, 1, 23, 39.07, -5.73, 1, 1, 23, 39.16, -17.09, 1, 1, 23, 34.74, -20.35, 1, 1, 23, 29.45, -21.89, 1, 2, 22, 69, -14.51, 0.03333, 23, 9.69, -17.38, 0.96667, 2, 22, 64.54, -12.35, 0.14793, 23, 5.88, -14.21, 0.85207, 1, 22, 33.27, -23.71, 1, 1, 22, 21.73, -21.12, 1, 1, 22, 15.58, -25.06, 1, 1, 22, 13.32, -20.84, 1, 1, 22, 2.2, -14.35, 1, 1, 22, -6.62, -7.37, 1, 1, 22, -12.23, -8.03, 1, 1, 22, -14.83, -5.93, 1, 1, 22, -6.39, 7.39, 1, 1, 22, -1.87, 12.56, 1, 1, 22, 9.61, 17.97, 1, 1, 22, 28.27, 20.05, 1, 2, 22, 38.65, 15.04, 0.97934, 23, -12.57, 18.65, 0.02066, 2, 22, 54.16, 15.85, 0.37748, 23, 2.67, 15.67, 0.62252, 2, 22, 56.22, 22.76, 0.14182, 23, 6.34, 21.88, 0.85818, 2, 22, 70, 26.35, 0.01679, 23, 20.58, 22.01, 0.98321, 2, 22, 74.76, 22.41, 0.00345, 23, 24.24, 17.03, 0.99655, 1, 23, 32.67, 15.89, 1, 1, 22, -4.5, 0.17, 1, 1, 22, 19.94, -0.9, 1, 2, 22, 54.6, 2.86, 0.63238, 23, -0.07, 2.96, 0.36762, 1, 23, 21.32, -0.43, 1], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56], "width": 92, "height": 87}}, "niuyao_06": {"niuyao_06": {"type": "mesh", "uvs": [0.75616, 0.08057, 0.80743, 0.18466, 0.85333, 0.27782, 0.89409, 0.37345, 0.86892, 0.44153, 0.85068, 0.58865, 0.78492, 0.75913, 0.75678, 0.91566, 0.73248, 0.95118, 0.66492, 0.97219, 0.61428, 0.98794, 0.54343, 0.98376, 0.48701, 0.98044, 0.45107, 0.97832, 0.41092, 0.96648, 0.3049, 0.82178, 0.10158, 0.642, 0.03428, 0.51202, 0.01678, 0.32175, 0.06051, 0.23161, 0.26042, 0.13038, 0.44667, 0.04096, 0.6167, 0.04059, 0.62022, 0.24659, 0.5812, 0.43237, 0.61904, 0.62655, 0.61786, 0.75361, 0.61549, 0.86508, 0.48424, 0.89265, 0.42749, 0.77159, 0.3932, 0.6685, 0.25485, 0.57981, 0.2111, 0.38203, 0.76448, 0.34607, 0.44877, 0.37604, 0.51853, 0.65652, 0.53509, 0.77039, 0.55401, 0.88306, 0.31634, 0.36046, 0.23589, 0.49407, 0.35544, 0.51717, 0.48825, 0.53475, 0.59967, 0.52716, 0.6959, 0.51628, 0.67107, 0.38203, 0.68999, 0.64813, 0.67462, 0.7632, 0.66634, 0.86987, 0.78222, 0.4983, 0.75029, 0.64933, 0.72664, 0.762, 0.716, 0.8483, 0.43695, 0.90463, 0.36127, 0.79316, 0.26904, 0.70446, 0.17445, 0.62415, 0.14016, 0.50309, 0.12242, 0.34008, 0.23357, 0.25378, 0.37428, 0.20703, 0.61904, 0.13751, 0.7633, 0.22861], "triangles": [11, 37, 10, 10, 27, 9, 13, 28, 12, 11, 12, 28, 14, 52, 13, 9, 47, 8, 7, 8, 51, 49, 48, 5, 49, 43, 48, 5, 48, 4, 43, 44, 48, 44, 33, 48, 48, 33, 4, 4, 33, 3, 33, 2, 3, 33, 61, 2, 16, 55, 54, 16, 56, 55, 16, 17, 56, 31, 56, 39, 31, 55, 56, 17, 57, 56, 17, 18, 57, 56, 32, 39, 56, 57, 32, 39, 32, 38, 32, 58, 38, 32, 57, 58, 38, 58, 59, 18, 19, 57, 57, 19, 58, 58, 20, 59, 58, 19, 20, 44, 24, 23, 24, 34, 23, 44, 23, 33, 34, 59, 23, 23, 61, 33, 61, 1, 2, 59, 60, 23, 23, 60, 61, 60, 0, 61, 61, 0, 1, 60, 21, 22, 60, 59, 21, 59, 20, 21, 60, 22, 0, 55, 31, 54, 54, 31, 30, 31, 40, 30, 30, 41, 35, 30, 40, 41, 35, 42, 25, 35, 41, 42, 25, 42, 43, 31, 39, 40, 40, 34, 41, 41, 24, 42, 41, 34, 24, 42, 24, 43, 39, 38, 40, 40, 38, 34, 24, 44, 43, 38, 59, 34, 10, 37, 27, 37, 11, 28, 13, 52, 28, 9, 27, 47, 14, 15, 52, 52, 15, 53, 51, 8, 47, 7, 51, 6, 6, 51, 50, 53, 29, 52, 52, 29, 28, 28, 36, 37, 28, 29, 36, 37, 36, 27, 51, 47, 46, 47, 27, 46, 27, 26, 46, 27, 36, 26, 51, 46, 50, 15, 16, 54, 15, 54, 53, 53, 30, 29, 53, 54, 30, 29, 35, 36, 29, 30, 35, 36, 35, 26, 50, 46, 45, 46, 26, 45, 50, 49, 6, 50, 45, 49, 6, 49, 5, 26, 25, 45, 26, 35, 25, 45, 43, 49, 45, 25, 43], "vertices": [3, 5, 52.32, 20.4, 0.88095, 16, 14.55, -102.57, 0.00105, 17, 47.36, -3.04, 0.11799, 2, 5, 51.93, 3.41, 0.6729, 17, 30.91, -7.27, 0.3271, 2, 5, 51.59, -11.78, 0.29291, 17, 16.19, -11.06, 0.70709, 3, 3, 70.07, -54.71, 0.00021, 5, 50.4, -26.95, 0.02005, 17, 1.27, -14.04, 0.97974, 3, 3, 60.91, -49.35, 0.02741, 4, 17.29, -50.1, 0.01467, 17, -7.67, -8.31, 0.95792, 3, 3, 40.2, -43.05, 0.29851, 4, -3.14, -42.96, 0.12314, 17, -28.1, -1.17, 0.57834, 3, 3, 17.32, -29.24, 0.83296, 4, -25.44, -28.23, 0.02867, 17, -50.4, 13.56, 0.13837, 1, 2, 20.05, -1.96, 1, 1, 2, 16.51, -7.19, 1, 1, 2, 6.54, -10.36, 1, 1, 2, -0.93, -12.73, 1, 1, 2, -11.42, -12.23, 1, 1, 2, -19.78, -11.82, 1, 1, 2, -25.1, -11.57, 1, 1, 2, -31.06, -9.9, 1, 3, 3, 20.36, 42.33, 0.6497, 4, -19.5, 43.16, 0.22308, 16, -77.28, -14.59, 0.12721, 3, 3, 51.33, 67.53, 0.1398, 4, 12.47, 67.08, 0.3265, 16, -45.3, 9.33, 0.5337, 3, 3, 71.72, 74.13, 0.04788, 4, 33.11, 72.84, 0.17892, 16, -24.66, 15.09, 0.7732, 3, 3, 99.54, 71.97, 0.00044, 4, 60.82, 69.56, 0.00029, 16, 3.04, 11.8, 0.99927, 3, 4, 72.33, 60.47, 0.00036, 5, -49.01, 48.99, 0.02154, 16, 14.56, 2.72, 0.9781, 3, 4, 80.58, 28.44, 0.05852, 5, -15.95, 48.24, 0.43837, 16, 22.81, -29.31, 0.5031, 3, 4, 87.57, -1.24, 0.00222, 5, 14.52, 46.91, 0.859, 16, 29.8, -59, 0.13879, 3, 5, 36.8, 35.2, 0.95715, 16, 24.58, -83.61, 0.02689, 17, 57.39, 15.92, 0.01597, 3, 5, 23.21, 8.36, 0.97709, 16, -4.94, -77.82, 0.00545, 17, 27.88, 21.71, 0.01747, 3, 4, 27.52, -8.74, 0.67168, 5, 5.44, -12.92, 0.27199, 17, 2.56, 33.05, 0.05633, 4, 3, 40.56, -8.32, 0.56256, 4, -1.37, -8.28, 0.36982, 5, -2.86, -40.61, 0.00068, 17, -26.33, 33.51, 0.06694, 3, 3, 22.31, -5.01, 0.98693, 4, -19.47, -4.22, 0.00025, 17, -44.43, 37.57, 0.01283, 2, 3, 6.33, -1.9, 0.99924, 17, -60.27, 41.32, 0.00076, 3, 3, 5.66, 17.93, 0.96989, 4, -35.18, 19.37, 0.01628, 16, -92.95, -38.38, 0.01383, 3, 3, 24.5, 23.21, 0.783, 4, -16.13, 23.88, 0.15664, 16, -73.91, -33.87, 0.06037, 3, 3, 40.2, 25.66, 0.43802, 4, -0.36, 25.69, 0.44067, 16, -58.13, -32.06, 0.12131, 3, 3, 56.43, 43.64, 0.15647, 4, 16.6, 43, 0.45925, 16, -41.18, -14.75, 0.38428, 4, 3, 85.99, 45.12, 0.01228, 4, 46.19, 43.28, 0.24224, 5, -39.56, 19.16, 0.0497, 16, -11.59, -14.47, 0.69579, 3, 4, 34.16, -37.9, 0.01055, 5, 35.31, -14.45, 0.29242, 17, 9.2, 3.88, 0.69704, 3, 4, 39.67, 8.7, 0.70356, 5, -8.05, 3.5, 0.21792, 16, -18.1, -49.05, 0.07852, 3, 3, 38.77, 7.08, 0.67831, 4, -2.53, 7.19, 0.30813, 16, -60.31, -50.57, 0.01356, 3, 3, 21.97, 7.48, 0.97518, 4, -19.3, 8.27, 0.01705, 16, -77.08, -49.48, 0.00776, 3, 3, 5.29, 7.51, 0.99604, 4, -35.97, 8.98, 0.00125, 16, -93.75, -48.77, 0.00272, 4, 3, 86.45, 29.24, 0.00478, 4, 46, 27.39, 0.42391, 5, -24.32, 14.67, 0.16312, 16, -11.77, -30.36, 0.40819, 4, 3, 69.24, 44.28, 0.06661, 4, 29.42, 43.12, 0.41845, 5, -43.96, 2.98, 0.00562, 16, -28.35, -14.63, 0.50932, 4, 3, 62.92, 27.41, 0.07733, 4, 22.42, 26.53, 0.68986, 5, -29.89, -8.26, 0.00162, 16, -35.36, -31.23, 0.23119, 3, 3, 57.06, 8.48, 0.02053, 4, 15.79, 7.84, 0.95115, 16, -41.98, -49.91, 0.02832, 4, 3, 55.35, -7.96, 0.02263, 4, 13.42, -8.51, 0.85766, 5, 1.39, -26.44, 0.03381, 17, -11.54, 33.27, 0.08591, 4, 3, 54.5, -22.27, 0.12258, 4, 11.99, -22.77, 0.40829, 5, 14.72, -31.68, 0.06034, 17, -12.97, 19.01, 0.4088, 4, 3, 74.44, -21.97, 0.001, 4, 31.92, -23.28, 0.15236, 5, 20.63, -12.64, 0.55265, 17, 6.96, 18.5, 0.29399, 4, 3, 35.68, -18.14, 0.62943, 4, -6.65, -17.88, 0.18228, 5, 4.96, -48.3, 0.00022, 17, -31.61, 23.9, 0.18807, 3, 3, 19.51, -13.05, 0.94259, 4, -22.6, -12.14, 0.00671, 17, -47.56, 29.65, 0.0507, 2, 3, 4.36, -9.2, 0.99289, 17, -62.53, 34.11, 0.00711, 4, 3, 54.92, -35.3, 0.11573, 4, 11.88, -35.81, 0.14227, 5, 27.24, -35.33, 0.01218, 17, -13.08, 5.97, 0.72983, 3, 3, 33.99, -26.9, 0.56198, 4, -8.69, -26.57, 0.13657, 17, -33.65, 15.21, 0.30144, 3, 3, 18.37, -20.66, 0.88772, 4, -24.04, -19.7, 0.01713, 17, -49, 22.08, 0.09515, 3, 3, 6.22, -16.97, 0.97503, 4, -36.03, -15.52, 6e-05, 17, -60.99, 26.26, 0.02492, 3, 3, 5.12, 25.12, 0.94233, 4, -35.42, 26.58, 0.03356, 16, -93.2, -31.17, 0.02411, 3, 3, 23.06, 33.4, 0.69663, 4, -17.16, 34.12, 0.20161, 16, -74.94, -23.63, 0.10176, 3, 3, 38.14, 44.65, 0.3666, 4, -1.64, 44.76, 0.3751, 16, -59.42, -12.99, 0.2583, 3, 3, 52.07, 56.46, 0.16794, 4, 12.76, 55.99, 0.36429, 16, -45.02, -1.76, 0.46777, 4, 3, 70.35, 58.47, 0.06079, 4, 31.11, 57.25, 0.25783, 5, -57.1, 8.44, 0.00025, 16, -26.67, -0.5, 0.68112, 4, 3, 94.25, 57.02, 0.00174, 4, 54.93, 54.83, 0.03319, 5, -48.31, 30.71, 0.00958, 16, -2.85, -2.92, 0.95549, 4, 3, 103.88, 38.67, 1e-05, 4, 63.8, 36.1, 0.11265, 5, -27.88, 34.17, 0.22228, 16, 6.02, -21.65, 0.66507, 3, 4, 66.11, 14.31, 0.09815, 5, -6.27, 30.47, 0.61476, 16, 8.33, -43.44, 0.28709, 3, 5, 30.5, 22.53, 0.95668, 16, 10.67, -80.99, 0.01942, 17, 43.48, 18.55, 0.0239, 2, 5, 43.16, 0.79, 0.6501, 17, 26.01, 0.46, 0.3499], "hull": 23, "edges": [0, 44, 6, 8, 8, 10, 10, 12, 14, 16, 26, 28, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 4, 6, 40, 42, 38, 40, 28, 30, 12, 14, 46, 48, 0, 2, 2, 4, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62, 70, 72, 72, 74, 20, 22, 22, 24, 24, 26, 62, 78, 78, 64, 60, 80, 80, 76, 78, 80, 68, 82, 82, 70, 80, 82, 48, 84, 84, 50, 82, 84, 84, 86, 88, 86, 86, 90, 90, 92, 92, 94, 16, 18, 18, 20, 66, 96, 96, 98, 98, 100, 100, 102, 102, 16, 94, 18, 54, 20, 74, 22, 56, 24, 106, 108, 108, 110, 114, 116, 116, 118, 118, 120, 120, 122, 68, 46, 46, 88, 46, 122, 46, 118], "width": 148, "height": 146}}, "niuyao_07": {"niuyao_07": {"type": "mesh", "uvs": [0.55555, 0.01086, 0.79006, 0.12813, 0.81598, 0.15684, 0.91027, 0.29826, 0.92588, 0.40314, 0.93687, 0.47705, 0.78336, 0.51678, 0.57765, 0.57003, 0.46874, 0.7191, 0.84999, 0.7702, 0.97802, 0.87869, 0.9783, 0.97184, 0.91535, 0.98912, 0.59128, 0.98923, 0.44313, 0.97578, 0.12758, 0.91021, 0.04383, 0.88152, 0.0435, 0.73371, 0.17682, 0.65365, 0.17531, 0.63178, 0.08511, 0.49397, 0.14611, 0.45911, 0.19722, 0.42989, 0.15353, 0.38685, 0.04423, 0.27914, 0.04384, 0.15399, 0.12952, 0.07124, 0.31159, 0.01093, 0.44348, 0.18096, 0.62135, 0.35269, 0.64588, 0.43702, 0.39748, 0.52596, 0.32695, 0.67622, 0.26561, 0.80349, 0.51095, 0.84642, 0.76241, 0.88016], "triangles": [33, 32, 8, 17, 18, 33, 34, 8, 9, 33, 8, 34, 35, 34, 9, 35, 9, 10, 16, 17, 33, 15, 16, 33, 12, 35, 10, 14, 33, 34, 13, 14, 34, 15, 33, 14, 11, 12, 10, 35, 13, 34, 13, 35, 12, 30, 22, 23, 21, 22, 30, 31, 21, 30, 7, 31, 30, 7, 30, 6, 31, 20, 21, 19, 20, 31, 32, 18, 19, 31, 32, 19, 32, 31, 7, 8, 32, 7, 33, 18, 32, 28, 27, 0, 28, 0, 1, 26, 27, 28, 25, 26, 28, 24, 25, 28, 2, 29, 28, 2, 28, 1, 29, 2, 3, 23, 24, 28, 23, 28, 29, 29, 3, 4, 30, 29, 4, 30, 23, 29, 30, 4, 5, 6, 30, 5], "vertices": [1, 39, 6.52, 11.69, 1, 1, 39, 20.72, 16.27, 1, 1, 39, 23.55, 16.13, 1, 1, 39, 36.81, 14.1, 1, 1, 39, 45.54, 10.43, 1, 1, 39, 51.68, 7.83, 1, 2, 39, 51.72, 0.05, 0.79437, 40, 2.86, 8.62, 0.20563, 3, 39, 51.76, -10.37, 0.02036, 40, 12.59, 4.88, 0.97908, 41, 10.05, 24.21, 0.00056, 2, 40, 25.87, 10.13, 0.41612, 41, 12.55, 10.14, 0.58388, 1, 41, 29.7, 14.78, 1, 1, 41, 39.58, 9.23, 1, 1, 41, 43.8, 1.99, 1, 1, 41, 42.14, -0.78, 1, 1, 41, 29.53, -8.11, 1, 2, 40, 43.9, 24.62, 0.01006, 41, 23.16, -10.41, 0.98994, 2, 40, 48.93, 10.09, 0.53188, 41, 7.91, -12.44, 0.46812, 2, 40, 49.5, 5.55, 0.75324, 41, 3.36, -12.1, 0.24676, 1, 41, -3.34, -0.6, 1, 3, 39, 50.24, -29.85, 6e-05, 40, 30.2, -3.6, 0.99856, 41, -1.77, 8.64, 0.00138, 2, 39, 48.45, -29.02, 0.00141, 40, 28.77, -4.96, 0.99859, 2, 39, 35.56, -26.98, 0.08723, 40, 22.2, -16.23, 0.91277, 2, 39, 34.02, -23.1, 0.15779, 40, 18.03, -16.26, 0.84221, 2, 39, 32.73, -19.86, 0.33031, 40, 14.54, -16.29, 0.66969, 2, 39, 28.39, -19.84, 0.59556, 40, 12.95, -20.34, 0.40444, 2, 39, 17.52, -19.8, 0.89195, 40, 8.97, -30.45, 0.10805, 2, 39, 7.48, -14.68, 0.98467, 40, 0.57, -37.95, 0.01533, 2, 39, 2.61, -7.85, 0.99949, 40, -7.56, -40.02, 0.00051, 1, 39, 1.52, 1.91, 1, 1, 39, 17.84, 0.22, 1, 1, 39, 35.25, 0.3, 1, 1, 40, 1.6, -0.78, 1, 2, 39, 44.53, -15.78, 0.02099, 40, 15.01, -3.81, 0.97901, 2, 40, 27.23, 2.8, 0.88736, 41, 5.1, 10.28, 0.11264, 2, 40, 37.62, 8.35, 0.01737, 41, 8.46, -1.02, 0.98263, 2, 40, 33.18, 19.17, 0.00197, 41, 19.95, 1.19, 0.99803, 1, 41, 31.26, 4.24, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 60, 10, 6, 8, 8, 10, 8, 60, 10, 12, 12, 14, 60, 12, 60, 44, 44, 46, 46, 48, 46, 60, 40, 42, 42, 44, 60, 42], "width": 45, "height": 90}}, "niuyao_08": {"niuyao_08": {"type": "mesh", "uvs": [0.80008, 0.02092, 0.92785, 0.09487, 0.98299, 0.1588, 0.98212, 0.26826, 0.9307, 0.34287, 0.82627, 0.41633, 0.78713, 0.44386, 0.78561, 0.48173, 0.78213, 0.56861, 0.70942, 0.66676, 0.70941, 0.71573, 0.78381, 0.8232, 0.78077, 0.89883, 0.62106, 0.91418, 0.5392, 0.96198, 0.35532, 0.988, 0.24164, 0.99022, 0.10791, 0.97901, 0.0361, 0.948, 0.03602, 0.8734, 0.11852, 0.77886, 0.45427, 0.72236, 0.40734, 0.56679, 0.27531, 0.48595, 0.12541, 0.42047, 0.12617, 0.39958, 0.17, 0.39974, 0.22782, 0.35393, 0.31054, 0.28839, 0.35963, 0.18187, 0.3999, 0.09451, 0.44827, 0.03791, 0.52702, 0.02094, 0.69388, 0.13879, 0.51186, 0.35845, 0.47597, 0.45641, 0.51291, 0.45943, 0.58621, 0.58108, 0.59646, 0.7295, 0.61954, 0.8141, 0.43239, 0.8423, 0.17603, 0.88683], "triangles": [40, 20, 21, 39, 40, 21, 41, 20, 40, 19, 20, 41, 12, 39, 11, 13, 39, 12, 40, 39, 13, 18, 19, 41, 14, 40, 13, 17, 18, 41, 15, 41, 40, 15, 40, 14, 16, 17, 41, 15, 16, 41, 6, 35, 5, 26, 27, 35, 36, 35, 6, 7, 36, 6, 23, 26, 35, 24, 25, 26, 23, 24, 26, 22, 23, 35, 7, 37, 36, 8, 37, 7, 9, 37, 8, 37, 38, 21, 22, 37, 21, 36, 22, 35, 37, 22, 36, 37, 9, 38, 10, 38, 9, 39, 38, 10, 39, 10, 11, 39, 21, 38, 33, 32, 0, 33, 0, 1, 2, 33, 1, 3, 33, 2, 4, 33, 3, 30, 32, 33, 32, 30, 31, 34, 33, 4, 33, 29, 30, 34, 29, 33, 28, 29, 34, 5, 34, 4, 5, 35, 34, 34, 27, 28, 35, 27, 34], "vertices": [1, 42, -6.09, -0.42, 1, 2, 42, -4.96, 9.45, 0.99852, 43, -9.36, 43.03, 0.00148, 2, 42, -2.08, 15.6, 0.98826, 43, -2.58, 42.99, 0.01174, 2, 42, 6.1, 22.02, 0.93455, 43, 6.67, 38.24, 0.06545, 2, 42, 13.41, 24.21, 0.8495, 43, 11.71, 32.5, 0.1505, 2, 42, 22.44, 24.04, 0.62522, 43, 15.33, 24.22, 0.37478, 2, 42, 25.83, 23.98, 0.46862, 43, 16.69, 21.12, 0.53138, 2, 42, 28.7, 26.15, 0.29736, 43, 19.86, 19.42, 0.70264, 2, 42, 35.29, 31.13, 0.09344, 43, 27.13, 15.51, 0.90656, 2, 42, 45.08, 33.79, 0.00688, 43, 33.64, 7.72, 0.99312, 2, 42, 48.73, 36.68, 0.00025, 43, 37.78, 5.61, 0.99975, 2, 43, 48.74, 4.64, 0.71814, 44, -5.1, 1.59, 0.28186, 2, 43, 55.07, 1.24, 0.17384, 44, -3.13, 8.5, 0.82616, 1, 44, 5.74, 7.71, 1, 1, 44, 11.24, 10.97, 1, 1, 44, 21.65, 10.82, 1, 1, 44, 27.75, 9.46, 1, 1, 44, 34.6, 6.58, 1, 1, 44, 37.69, 2.74, 1, 1, 44, 35.91, -4.12, 1, 2, 43, 28.41, -26.08, 0.00256, 44, 29.26, -11.68, 0.99744, 2, 43, 31.99, -7.18, 0.67471, 44, 10.04, -12.24, 0.32529, 1, 43, 17.64, -2.79, 1, 1, 43, 7.51, -5.79, 1, 2, 42, 46.7, -5.92, 0.01869, 43, -1.77, -10.32, 0.98131, 2, 42, 45.12, -7.12, 0.02359, 43, -3.52, -9.39, 0.97641, 2, 42, 43.64, -5.22, 0.08606, 43, -2.42, -7.25, 0.91394, 2, 42, 38.25, -5.43, 0.7012, 43, -4.86, -2.44, 0.2988, 1, 42, 30.54, -5.73, 1, 1, 42, 20.94, -9.91, 1, 1, 42, 13.06, -13.33, 1, 1, 42, 7.19, -14.58, 1, 1, 42, 3.24, -12.19, 1, 2, 42, 6.31, 1.96, 0.99918, 43, -11.47, 29.67, 0.00082, 2, 42, 28.88, 7.08, 0.7448, 43, 2.6, 11.3, 0.2552, 2, 42, 37.4, 11.31, 0.15098, 43, 10, 5.32, 0.84902, 2, 42, 36.36, 13.08, 0.18735, 43, 11.18, 7, 0.81265, 2, 42, 42.91, 23.42, 0.02486, 43, 23.31, 5.36, 0.97514, 2, 43, 36.14, -0.52, 0.98615, 44, 2.64, -9.62, 0.01385, 2, 43, 43.88, -3.03, 0.25505, 44, 3.43, -1.52, 0.74495, 2, 43, 41.61, -13.42, 0.02091, 44, 14.07, -1.51, 0.97909, 1, 44, 28.78, -0.96, 1], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 60, 62, 62, 64, 56, 58, 58, 60, 0, 66, 66, 68, 68, 70, 70, 12, 8, 10, 10, 12, 10, 70, 12, 14, 14, 16, 70, 52, 52, 54, 54, 56, 54, 70, 70, 46, 14, 72, 72, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82], "width": 55, "height": 95}}, "niuyao_09": {"niuyao_09": {"type": "mesh", "uvs": [0.74254, 0.01273, 0.8083, 0.07485, 0.99816, 0.39442, 0.85992, 0.50072, 0.69663, 0.47091, 0.67563, 0.64512, 0.65722, 0.79788, 0.56612, 0.77294, 0.52614, 0.88003, 0.48136, 1, 0.43429, 0.87693, 0.39271, 0.76317, 0.25632, 0.78203, 0.30322, 0.60799, 0.1023, 0.48208, 0.01588, 0.50001, 0.02776, 0.32006, 0.13128, 0.07991, 0.29309, 0.07961, 0.38994, 0.12881, 0.50944, 0.08314, 0.60726, 0.10634, 0.66303, 0.0132, 0.71729, 0.08972, 0.79265, 0.28124, 0.6749, 0.30476, 0.52299, 0.31316, 0.50768, 0.461, 0.50533, 0.64076, 0.4912, 0.77516, 0.48531, 0.8894, 0.346, 0.2895, 0.32951, 0.42054, 0.36131, 0.65238, 0.41901, 0.44406, 0.43196, 0.27942, 0.6086, 0.30966, 0.6086, 0.47262, 0.60977, 0.66078, 0.23232, 0.20046, 0.1546, 0.3651], "triangles": [39, 40, 17, 21, 22, 23, 23, 22, 0, 31, 18, 19, 39, 17, 18, 35, 19, 20, 26, 20, 21, 14, 40, 13, 15, 16, 14, 14, 16, 40, 40, 16, 17, 9, 30, 8, 9, 10, 30, 8, 30, 29, 30, 10, 29, 8, 29, 7, 10, 11, 29, 12, 33, 11, 7, 29, 28, 29, 11, 28, 11, 33, 28, 12, 13, 33, 33, 13, 34, 33, 34, 28, 34, 13, 32, 13, 40, 32, 27, 34, 26, 32, 31, 34, 34, 35, 26, 34, 31, 35, 40, 39, 32, 32, 39, 31, 39, 18, 31, 31, 19, 35, 35, 20, 26, 7, 38, 6, 6, 38, 5, 7, 28, 38, 5, 38, 37, 38, 28, 37, 5, 37, 4, 28, 27, 37, 28, 34, 27, 27, 26, 37, 37, 25, 4, 26, 36, 37, 37, 36, 25, 26, 21, 36, 36, 21, 25, 4, 24, 3, 3, 24, 2, 4, 25, 24, 24, 1, 2, 25, 23, 24, 24, 23, 1, 23, 0, 1, 25, 21, 23], "vertices": [1, 2, 24.48, -0.19, 1, 2, 27, 7.45, 7.76, 0.9108, 28, -4.79, 8.97, 0.0892, 1, 28, 25.8, 1.78, 1, 3, 28, 18.65, -13.42, 0.8871, 29, 33.88, 21.39, 0.01225, 30, 6.4, 25.02, 0.10065, 4, 27, 21.25, -21.13, 0.00653, 28, 3.27, -22.02, 0.17246, 29, 26.56, 5.37, 0.06928, 30, 4.91, 7.47, 0.75173, 1, 30, 18.06, 5.78, 1, 2, 30, 29.59, 4.3, 0.96857, 33, 18.47, 22.39, 0.03143, 3, 30, 28.14, -5.52, 0.58424, 32, 26.06, 15.97, 0.00968, 33, 13.21, 13.97, 0.40608, 2, 30, 36.34, -9.45, 0.0789, 33, 19.16, 7.09, 0.9211, 1, 33, 25.82, -0.63, 1, 1, 33, 15.39, -2, 1, 2, 32, 21.61, -2.06, 0.02489, 33, 5.83, -3.06, 0.97511, 2, 32, 20.06, -16.64, 0.47445, 33, 1.88, -17.19, 0.52555, 5, 31, 30.21, -6.03, 0.04102, 32, 8.28, -9.09, 0.84032, 33, -8.48, -7.79, 0.07025, 25, 24.76, 31.49, 0.00073, 26, 6.69, 32.73, 0.04767, 4, 31, 23.33, -28.48, 0.07487, 32, -5.3, -28.25, 0.06587, 25, 29.41, 8.48, 0.00179, 26, 14.48, 10.58, 0.85747, 3, 31, 25.74, -37.51, 0.00068, 32, -5.84, -37.58, 0.00401, 26, 21.78, 4.75, 0.99531, 1, 26, 11.05, -3.54, 1, 1, 2, -40.87, -5.89, 1, 1, 2, -23.56, -5.69, 1, 1, 2, -13.16, -9.28, 1, 1, 2, -0.41, -5.72, 1, 1, 2, 10.08, -7.36, 1, 1, 2, 15.97, -0.31, 1, 2, 27, 1.69, -0.17, 0.99665, 29, -0.08, 15.98, 0.00335, 4, 27, 17.72, -3.94, 0.07066, 28, 3.07, -4.47, 0.8291, 29, 16.03, 19.41, 0.06497, 30, -9.74, 17.13, 0.03527, 4, 27, 10.5, -14.42, 0.16425, 28, -6.01, -13.38, 0.09821, 29, 13.97, 6.85, 0.69541, 30, -7.44, 4.61, 0.04213, 4, 29, 9.74, -8.85, 0.63158, 30, -6.12, -11.6, 0.05786, 31, 5.52, 14.75, 0.28375, 32, -8.64, 18.39, 0.02681, 5, 29, 19.84, -13.71, 0.16566, 30, 5.03, -12.76, 0.37233, 31, 16.72, 14.41, 0.21183, 32, 1.89, 14.56, 0.24536, 33, -10.86, 16.59, 0.00482, 5, 29, 32.64, -17.96, 0.00164, 30, 18.51, -12.44, 0.42769, 31, 30.14, 15.73, 0.00456, 32, 15.04, 11.6, 0.2652, 33, 1.62, 11.49, 0.30091, 3, 30, 28.64, -13.52, 0.16842, 32, 24.61, 8.09, 0.01072, 33, 10.47, 6.44, 0.82086, 2, 30, 37.23, -13.79, 0.02019, 33, 18.24, 2.76, 0.97982, 3, 31, 5.96, -4.26, 0.91214, 25, 2.59, 21.49, 0.07685, 26, -13.88, 19.76, 0.01101, 4, 31, 15.92, -4.87, 0.84342, 32, -4.92, -3.51, 0.079, 25, 11.64, 25.7, 0.02915, 26, -5.5, 25.18, 0.04843, 3, 32, 12.79, -3.67, 0.86448, 33, -3.13, -3.2, 0.13233, 26, 4.87, 39.55, 0.00319, 4, 29, 15.81, -22.39, 0.04026, 30, 4.16, -22.3, 0.0564, 31, 16.56, 4.84, 0.61688, 32, -1.27, 5.52, 0.28645, 4, 29, 4.43, -17.4, 0.15262, 30, -8.23, -21.44, 0.00822, 31, 4.14, 4.78, 0.83422, 32, -13.08, 9.36, 0.00494, 5, 27, 5.97, -19.89, 0.00023, 29, 12.21, -0.03, 0.99881, 30, -6.77, -2.46, 0.00034, 31, 4.19, 23.82, 0.00052, 32, -7.06, 27.42, 0.0001, 5, 29, 23.88, -3.66, 0.0277, 30, 5.44, -1.94, 0.94761, 31, 16.33, 25.24, 0.00947, 32, 4.91, 24.96, 0.01499, 33, -6.15, 26.34, 0.00023, 3, 30, 19.53, -1.21, 0.97279, 32, 18.76, 22.24, 0.00624, 33, 7.05, 21.37, 0.02097, 4, 31, 0.74, -17.12, 0.26856, 32, -23.18, -10.37, 0.00094, 25, 4.13, 7.7, 0.71464, 26, -10.45, 6.31, 0.01586, 4, 31, 13.97, -23.95, 0.14124, 32, -12.76, -21, 0.0561, 25, 19.01, 8, 0.11512, 26, 4.25, 8.68, 0.68754], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 18, 20, 44, 46, 46, 48, 42, 50, 40, 52, 52, 54, 54, 56, 8, 10, 10, 12, 56, 58, 14, 16, 16, 18, 58, 60, 60, 18, 38, 62, 62, 64, 64, 26, 24, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 12, 36, 78, 78, 80, 80, 28, 48, 6, 50, 8, 72, 42, 38, 70], "width": 107, "height": 75}}, "dg1/dg_0001": {"dg2effect/xin_skill1/zadidaoguang/dg_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [148.03, -77.47, -141.77, -88.41, -148.06, 78.47, 141.73, 89.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 167}, "dg2effect/xin_skill1/zadidaoguang/dg_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [148.03, -77.47, -141.77, -88.41, -148.06, 78.47, 141.73, 89.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 167}, "dg2effect/xin_skill1/zadidaoguang/dg_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [148.03, -77.47, -141.77, -88.41, -148.06, 78.47, 141.73, 89.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 167}, "dg2effect/xin_skill1/zadidaoguang/dg_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [148.03, -77.47, -141.77, -88.41, -148.06, 78.47, 141.73, 89.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 167}, "dg2effect/xin_skill1/zadidaoguang/dg_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [148.03, -77.47, -141.77, -88.41, -148.06, 78.47, 141.73, 89.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 167}, "dg2effect/xin_skill1/zadidaoguang/dg_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [148.03, -77.47, -141.77, -88.41, -148.06, 78.47, 141.73, 89.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 167}}, "niuyao_1": {"niuyao_01": {"type": "mesh", "uvs": [0.03387, 0.22975, 0.22506, 0, 0.47302, 0.18996, 0.41354, 0.45855, 1, 0.87706, 1, 1, 0.91202, 1, 0.37937, 0.54547, 0.28071, 0.77789, 0, 0.5338], "triangles": [3, 1, 2, 0, 1, 3, 7, 9, 0, 3, 7, 0, 8, 9, 7, 6, 3, 4, 7, 3, 6, 6, 4, 5], "vertices": [172.69, -2.13, 145.84, -67.05, 66.9, -65.29, 57.43, -10.38, -126.5, -11.65, -137.62, 10.01, -115.7, 21.26, 58.08, 9.3, 61.64, 62.86, 153.63, 55.76], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 280, "height": 198}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"dg1/dg_0001": {"attachment": [{"time": 0.2333, "name": "dg2effect/xin_skill1/z<PERSON><PERSON><PERSON><PERSON>/dg_0001"}, {"time": 0.2667, "name": "dg2effect/xin_skill1/z<PERSON><PERSON><PERSON><PERSON>/dg_0002"}, {"time": 0.3, "name": "dg2effect/xin_skill1/z<PERSON><PERSON><PERSON><PERSON>/dg_0004"}, {"time": 0.3333, "name": "dg2effect/xin_skill1/z<PERSON><PERSON>oguang/dg_0007"}, {"time": 0.3667, "name": "dg2effect/xin_skill1/z<PERSON><PERSON><PERSON><PERSON>/dg_0009"}, {"time": 0.4, "name": "dg2effect/xin_skill1/z<PERSON><PERSON>oguang/dg_0011"}, {"time": 0.4333, "name": null}]}, "dg2effect/xin_skill1/ci1/ci_0001": {"attachment": [{"time": 0.2333, "name": "dg2effect/xin_skill1/ci1/ci_0001"}, {"time": 0.2667, "name": "dg2effect/xin_skill1/ci1/ci_0003"}, {"time": 0.3, "name": "dg2effect/xin_skill1/ci1/ci_0005"}, {"time": 0.3333, "name": "dg2effect/xin_skill1/ci1/ci_0007"}, {"time": 0.3667, "name": "dg2effect/xin_skill1/ci1/ci_0009"}, {"time": 0.4, "name": "dg2effect/xin_skill1/ci1/ci_0011"}, {"time": 0.4333, "name": "dg2effect/xin_skill1/ci1/ci_0013"}, {"time": 0.4667, "name": "dg2effect/xin_skill1/ci1/ci_0015"}, {"time": 0.5, "name": null}]}}, "bones": {"bone": {"rotate": [{"curve": 0.278, "c3": 0.622, "c4": 0.4}, {"time": 0.1333, "angle": 9.91, "curve": 0.33, "c2": 0.32, "c3": 0.758}, {"time": 0.4333}], "translate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.1333, "x": -11.47, "curve": 0.326, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.2333, "x": 0.73, "curve": 0.358, "c2": 0.43, "c3": 0.756}, {"time": 0.4333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 5.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -15.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone3": {"rotate": [{"angle": -2.96, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.97, "curve": 0.326, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.3, "angle": -14.15, "curve": 0.335, "c2": 0.34, "c3": 0.696, "c4": 0.76}, {"time": 0.4333, "angle": -2.96}]}, "bone5": {"translate": [{"x": 0.41, "y": -0.71, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.15, "y": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 3.14, "y": -5.44, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4333, "x": 0.41, "y": -0.71}]}, "bone7": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -4.33, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.99, "y": -4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -4.33, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.99, "y": -4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone9": {"translate": [{"x": 0.73, "y": -1.79, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -4.33, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.99, "y": -4.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "x": 0.73, "y": -1.79}]}, "bone10": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -4.33, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.99, "y": -4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -4.33, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.99, "y": -4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone12": {"translate": [{"x": 0.73, "y": -1.79, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -4.33, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.99, "y": -4.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "x": 0.73, "y": -1.79}]}, "bone13": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -4.33, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.99, "y": -4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone14": {"translate": [{"x": 0.73, "y": -1.79, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -4.33, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.99, "y": -4.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "x": 0.73, "y": -1.79}]}, "niuyao_6": {"rotate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.1333, "angle": 56.13, "curve": 0.321, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.2333, "angle": -15.24, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.4333}]}, "niuyao_7": {"rotate": [{"angle": -0.36, "curve": 0.358, "c2": 0.64, "c3": 0.693}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 42.24, "curve": 0.321, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.2667, "angle": -5.91, "curve": 0.356, "c2": 0.41, "c3": 0.722, "c4": 0.86}, {"time": 0.4333, "angle": -0.36}]}, "bone16": {"rotate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.1333, "angle": 115.21, "curve": 0.326, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.2333, "angle": -23, "curve": 0.358, "c2": 0.43, "c3": 0.756}, {"time": 0.4333}]}, "bone17": {"rotate": [{"angle": -2.14, "curve": 0.361, "c2": 0.64, "c3": 0.696}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -27.24, "curve": 0.326, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": -32.59, "curve": 0.345, "c2": 0.37, "c3": 0.721, "c4": 0.85}, {"time": 0.4333, "angle": -2.14}]}, "bone18": {"rotate": [{"angle": -2.92, "curve": 0.361, "c2": 0.64, "c3": 0.696}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -44.51, "curve": 0.345, "c2": 0.37, "c3": 0.721, "c4": 0.85}, {"time": 0.4333, "angle": -2.92}]}, "bone19": {"rotate": [{"angle": 10.76, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": 71.79, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 50.42, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 0.2333, "angle": -114.97, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.2667, "angle": -161.69, "curve": 0.345, "c2": 0.37, "c3": 0.721, "c4": 0.85}, {"time": 0.4333, "angle": 10.76}]}, "bone20": {"rotate": [{"angle": 1.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.25, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4333, "angle": 1.46}]}, "bone21": {"rotate": [{"angle": 4.1, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0333, "angle": 1.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": 4.1}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone23": {"rotate": [{"angle": 1.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.25, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4333, "angle": 1.46}]}, "bone24": {"rotate": [{"angle": 1.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.25, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4333, "angle": 1.46}]}, "bone25": {"rotate": [{"angle": 1.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.25, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4333, "angle": 1.46}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone27": {"rotate": [{"angle": 1.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.25, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4333, "angle": 1.46}]}, "bone28": {"rotate": [{"angle": 4.14, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": 4.14}]}, "bone34": {"rotate": [{"angle": 0.15}]}, "bone35": {"rotate": [{"angle": -1.55}]}, "bone36": {"rotate": [{"angle": 1.58}]}, "bone37": {"rotate": [{"angle": -1.13}]}, "bone38": {"rotate": [{"angle": 2.5}]}, "bone39": {"rotate": [{"angle": -1.42}]}, "target3": {"translate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.1333, "x": -17.22, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.2333}]}, "bone41": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 6.95, "y": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -1.52, "y": -2.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone42": {"translate": [{"x": -0.56, "y": -0.94, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 6.95, "y": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -1.52, "y": -2.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "x": -0.56, "y": -0.94}]}, "bone43": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 6.95, "y": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -1.52, "y": -2.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone29": {"rotate": [{"angle": -7.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.53}]}, "bone30": {"rotate": [{"angle": -8.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -7.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -11.18, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4333, "angle": -8.34}]}, "bone31": {"rotate": [{"angle": -25.14, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -23.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -27.19, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": -25.14}]}, "bone32": {"rotate": [{"angle": -26.36, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -24.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -27.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4333, "angle": -26.36}]}, "bone33": {"rotate": [{"angle": 40.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 51.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 37.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 40.8}]}, "ci1": {"translate": [{"time": 0.2333, "x": -60.72, "y": -15.09}], "scale": [{"time": 0.2333, "x": 0.889, "y": 0.719}]}}, "events": [{"time": 0.2333, "name": "atk"}]}, "boss_attack3": {"slots": {"dg2effect/huo/huoyan-2": {"attachment": [{"time": 0.5667, "name": "dg2effect/huo/huoyan-1"}, {"time": 0.6, "name": "dg2effect/huo/huoyan-3"}, {"time": 0.6333, "name": "dg2effect/huo/huoyan-4"}, {"time": 0.6667, "name": "dg2effect/huo/huoyan-5"}, {"time": 0.7, "name": "dg2effect/huo/huoyan-7"}, {"time": 0.7333, "name": "dg2effect/huo/huoyan-8"}, {"time": 0.7667, "name": "dg2effect/huo/huoyan-9"}, {"time": 0.8, "name": null}]}, "dg2effect/huo/huoyan-3": {"attachment": [{"time": 0.6, "name": "dg2effect/huo/huoyan-1"}, {"time": 0.6333, "name": "dg2effect/huo/huoyan-3"}, {"time": 0.6667, "name": "dg2effect/huo/huoyan-4"}, {"time": 0.7, "name": "dg2effect/huo/huoyan-5"}, {"time": 0.7333, "name": "dg2effect/huo/huoyan-7"}, {"time": 0.7667, "name": "dg2effect/huo/huoyan-8"}, {"time": 0.8, "name": "dg2effect/huo/huoyan-9"}, {"time": 0.8333, "name": null}]}, "dg2/qxdg02_000": {"attachment": [{"time": 0.1667, "name": "dg2/qxdg02_000"}, {"time": 0.2, "name": "dg2/qxdg02_001"}, {"time": 0.2667, "name": "dg2/qxdg02_002"}, {"time": 0.3, "name": "dg2/qxdg02_003"}, {"time": 0.3667, "name": "dg2/qxdg02_007"}, {"time": 0.4, "name": "dg2/qxdg02_009"}, {"time": 0.4333, "name": "dg2/qxdg02_011"}, {"time": 0.5, "name": null}]}, "dg2effect/xin_skill1/4": {"color": [{"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffff00"}], "attachment": [{"time": 0.5333, "name": "dg2effect/xin_skill1/02"}]}, "dg2effect/huo/huoyan-4": {"attachment": [{"time": 0.6667, "name": "dg2effect/huo/huoyan-1"}, {"time": 0.7, "name": "dg2effect/huo/huoyan-3"}, {"time": 0.7333, "name": "dg2effect/huo/huoyan-4"}, {"time": 0.7667, "name": "dg2effect/huo/huoyan-5"}, {"time": 0.8, "name": "dg2effect/huo/huoyan-7"}, {"time": 0.8333, "name": "dg2effect/huo/huoyan-8"}, {"time": 0.8667, "name": "dg2effect/huo/huoyan-9"}, {"time": 0.9, "name": null}]}, "dg2effect/xin_skill1/dilie": {"color": [{"time": 0.7333, "color": "ffffffb1", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffff00"}], "attachment": [{"time": 0.5333, "name": "dg2effect/xin_skill1/dilie"}]}, "dg1/dg_0001": {"attachment": [{"time": 0.5333, "name": "dg2effect/xin_skill1/z<PERSON><PERSON><PERSON><PERSON>/dg_0001"}, {"time": 0.5667, "name": "dg2effect/xin_skill1/z<PERSON><PERSON><PERSON><PERSON>/dg_0002"}, {"time": 0.6, "name": "dg2effect/xin_skill1/z<PERSON><PERSON><PERSON><PERSON>/dg_0004"}, {"time": 0.6333, "name": "dg2effect/xin_skill1/z<PERSON><PERSON>oguang/dg_0007"}, {"time": 0.6667, "name": "dg2effect/xin_skill1/z<PERSON><PERSON><PERSON><PERSON>/dg_0009"}, {"time": 0.7, "name": "dg2effect/xin_skill1/z<PERSON><PERSON>oguang/dg_0011"}, {"time": 0.7333, "name": null}]}, "dg2effect/huo/huoyan-1": {"attachment": [{"time": 0.5333, "name": "dg2effect/huo/huoyan-1"}, {"time": 0.5667, "name": "dg2effect/huo/huoyan-3"}, {"time": 0.6, "name": "dg2effect/huo/huoyan-4"}, {"time": 0.6333, "name": "dg2effect/huo/huoyan-5"}, {"time": 0.6667, "name": "dg2effect/huo/huoyan-7"}, {"time": 0.7, "name": "dg2effect/huo/huoyan-8"}, {"time": 0.7333, "name": "dg2effect/huo/huoyan-9"}, {"time": 0.7667, "name": null}]}, "dg2effect/xin_skill1/3": {"color": [{"time": 0.6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"time": 0.6, "name": "dg2effect/xin_skill1/02"}]}, "dg2effect/xin_skill1/2": {"color": [{"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffff00"}], "attachment": [{"time": 0.5333, "name": "dg2effect/xin_skill1/02"}]}, "dg2effect/xin_skill1/ci1/ci_0001": {"attachment": [{"time": 0.5333, "name": "dg2effect/xin_skill1/ci1/ci_0001"}, {"time": 0.5667, "name": "dg2effect/xin_skill1/ci1/ci_0003"}, {"time": 0.6, "name": "dg2effect/xin_skill1/ci1/ci_0005"}, {"time": 0.6333, "name": "dg2effect/xin_skill1/ci1/ci_0007"}, {"time": 0.6667, "name": "dg2effect/xin_skill1/ci1/ci_0009"}, {"time": 0.7, "name": "dg2effect/xin_skill1/ci1/ci_0011"}, {"time": 0.7333, "name": "dg2effect/xin_skill1/ci1/ci_0013"}, {"time": 0.7667, "name": "dg2effect/xin_skill1/ci1/ci_0015"}, {"time": 0.8, "name": null}]}}, "bones": {"bone": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 6.63, "curve": "stepped"}, {"time": 0.2667, "angle": 6.63, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.4333, "angle": 3.51, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8}], "translate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "x": -10.17, "curve": "stepped"}, {"time": 0.2667, "x": -10.17, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.4333, "x": -5.38, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8}]}, "bone2": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": 5.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 24.3, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -26.97, "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 0.8}]}, "bone3": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 5.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4333, "angle": 14.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8}]}, "bone4": {"rotate": [{"time": 0.1333, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4333, "angle": 3.03, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8}]}, "bone12": {"rotate": [{"time": 0.4333, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.5333, "angle": -31.59, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.8}]}, "niuyao_06": {"translate": [{"time": 0.4333, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.5333, "x": -16.84, "y": -11.98, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.8}]}, "niuyao_6": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 159.85, "curve": "stepped"}, {"time": 0.4333, "angle": 159.85, "curve": 0.316, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.5, "angle": 95.89, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.66}, {"time": 0.5333, "angle": 86.23, "curve": 0.331, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.6, "angle": 96.06, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8}], "translate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "x": -9.93, "y": -19.04, "curve": "stepped"}, {"time": 0.4333, "x": -9.93, "y": -19.04, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8}]}, "niuyao_8": {"rotate": [{"time": 0.4333, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.5333, "angle": -48.63, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.8}]}, "bone16": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 102.66, "curve": "stepped"}, {"time": 0.4333, "angle": 102.66, "curve": 0.309, "c2": 0.25, "c3": 0.651, "c4": 0.61}, {"time": 0.5, "angle": -46.25, "curve": 0.332, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -69.99, "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 0.8}]}, "bone17": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": -23.83, "curve": "stepped"}, {"time": 0.4333, "angle": -23.83, "curve": 0.309, "c2": 0.25, "c3": 0.651, "c4": 0.61}, {"time": 0.5, "angle": 9.16, "curve": 0.332, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -0.19, "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 0.8}]}, "bone19": {"rotate": [{"angle": 10.76, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 55.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -152.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -18.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 55.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -161.64, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -43.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 51.32, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.4667, "angle": 46.17, "curve": 0.311, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 0.5, "angle": -80.07, "curve": 0.322, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.5333, "angle": -155.03, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.8, "angle": 10.76}], "scale": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.367, "y": 1.731, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "bone21": {"rotate": [{"angle": -0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -10.59, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.8, "angle": -0.66}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "bone23": {"rotate": [{"angle": -0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -10.59, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.8, "angle": -0.66}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "bone25": {"rotate": [{"angle": -0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -10.59, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.8, "angle": -0.66}]}, "bone26": {"rotate": [{"angle": -0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -10.59, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.8, "angle": -0.66}]}, "bone27": {"rotate": [{"angle": -1.93, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.0333, "angle": -0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -10.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8, "angle": -1.93}]}, "bone28": {"rotate": [{"angle": -3.54, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.0333, "angle": -1.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -10.59, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.8, "angle": -3.54}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.55, "curve": "stepped"}, {"time": 0.4333, "angle": 2.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -26.87, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "bone30": {"rotate": [{"angle": -1.69, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 2.55, "curve": "stepped"}, {"time": 0.4667, "angle": 2.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -26.87, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.8, "angle": -1.69}]}, "bone31": {"rotate": [{"angle": -4.96, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.55, "curve": "stepped"}, {"time": 0.5, "angle": 2.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -26.87, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8, "angle": -4.96}]}, "bone32": {"rotate": [{"angle": -9.02, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.55, "curve": "stepped"}, {"time": 0.5333, "angle": 2.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -26.87, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.8, "angle": -9.02}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.55, "curve": "stepped"}, {"time": 0.4333, "angle": 2.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -26.87, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "bone34": {"rotate": [{"angle": 0.15, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": -2.12, "curve": "stepped"}, {"time": 0.2667, "angle": -2.12, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.4333, "angle": -0.8, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8, "angle": 0.15}]}, "bone35": {"rotate": [{"angle": -1.55, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 4.41, "curve": "stepped"}, {"time": 0.2667, "angle": 4.41, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.4333, "angle": 1.13, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8, "angle": -1.55}]}, "bone36": {"rotate": [{"angle": 1.58, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": -8.38, "curve": "stepped"}, {"time": 0.2667, "angle": -8.38, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.4333, "angle": -3.47, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8, "angle": 1.58}]}, "bone37": {"rotate": [{"angle": -1.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": -18.12, "curve": "stepped"}, {"time": 0.2667, "angle": -18.12, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.4333, "angle": -14.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8, "angle": -1.13}]}, "bone38": {"rotate": [{"angle": 2.5, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 9.17, "curve": "stepped"}, {"time": 0.2667, "angle": 9.17, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.4333, "angle": 3.81, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8, "angle": 2.5}]}, "bone39": {"rotate": [{"angle": -1.42, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "angle": 2.2, "curve": "stepped"}, {"time": 0.2667, "angle": 2.2, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.4333, "angle": 7.04, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8, "angle": -1.42}]}, "target3": {"translate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1333, "x": -15.25, "curve": "stepped"}, {"time": 0.4333, "x": -15.25, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333}]}, "bone41": {"rotate": [{"curve": 0.32, "c3": 0.654, "c4": 0.34}, {"time": 0.1667, "angle": 14.14, "curve": 0.298, "c2": 0.13, "c3": 0.636, "c4": 0.49}, {"time": 0.8}], "translate": [{"curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 0.5333, "x": -2.69, "y": -7.69, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.61}, {"time": 0.8}]}, "bone42": {"rotate": [{"angle": 1.02, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 14.14, "curve": 0.299, "c2": 0.13, "c3": 0.637, "c4": 0.49}, {"time": 0.8, "angle": 1.02}], "translate": [{"x": 0.99, "y": -2.41, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -7.28, "y": -8.39, "curve": 0.311, "c2": 0.17, "c3": 0.646, "c4": 0.51}, {"time": 0.5667, "x": 7.17, "y": -17.5, "curve": 0.323, "c2": 0.28, "c3": 0.657, "c4": 0.62}, {"time": 0.8, "x": 0.99, "y": -2.41}]}, "bone43": {"rotate": [{"angle": 1.09, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 14.14, "curve": "stepped"}, {"time": 0.4, "angle": 14.14, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.8, "angle": 1.09}], "translate": [{"x": 0.42, "y": -4.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -15.78, "y": -14.95, "curve": "stepped"}, {"time": 0.4, "x": -15.78, "y": -14.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 2.27, "y": -21.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8, "x": 0.42, "y": -4.01}]}, "dg1_1": {"translate": [{"time": 0.5333, "x": 76.8, "y": 35.41}], "scale": [{"time": 0.5333, "y": 1.302}]}, "ci1": {"translate": [{"x": -10.15, "y": -12.5}]}, "dg2all8": {"translate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -24.89, "y": 118.84}], "scale": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 1.23, "y": 1.23}]}, "dg2all10": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -24.89, "y": 118.84}], "scale": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.23, "y": 1.23}]}, "dg2all12": {"translate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -24.89, "y": 118.84}], "scale": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 1.23, "y": 1.23}]}}, "drawOrder": [{"offsets": [{"slot": "niuyao_011", "offset": 2}]}], "events": [{"time": 0.5333, "name": "atk"}]}, "boss_idle": {"bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone3": {"translate": [{"x": 0.63, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 1.94, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "x": 0.63}]}, "bone4": {"rotate": [{"angle": 2.91, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.91}], "translate": [{"x": 1.86, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.86}]}, "bone5": {"translate": [{"x": 0.63, "y": -0.35, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 3.81, "y": -2.11, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "x": 0.63, "y": -0.35}]}, "bone7": {"rotate": [{"angle": -6.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.01, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.45}], "translate": [{"x": -3.04, "y": -1.2, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -4.24, "y": -1.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -3.04, "y": -1.2}]}, "bone8": {"rotate": [{"angle": -9.01, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -9.01}], "translate": [{"x": -3.09, "y": 1.25, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -3.09, "y": 1.25}]}, "bone9": {"rotate": [{"angle": -6.83, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -9.01, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -6.83}], "translate": [{"x": -2.21, "y": 0.89, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -3.09, "y": 1.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -2.21, "y": 0.89}]}, "bone10": {"rotate": [{"angle": -2.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.56}], "translate": [{"x": -2.21, "y": 0.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -3.09, "y": 1.25, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.21, "y": 0.89}]}, "bone11": {"rotate": [{"angle": -2.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.56}], "translate": [{"x": -1.2, "y": -0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.24, "y": -1.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.2, "y": -0.48}]}, "bone12": {"rotate": [{"angle": -0.38, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -9.01, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -0.38}], "translate": [{"x": -0.18, "y": -0.07, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -4.24, "y": -1.68, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "x": -0.18, "y": -0.07}]}, "bone13": {"rotate": [{"angle": -2.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.56}], "translate": [{"x": -1.2, "y": -0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.24, "y": -1.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.2, "y": -0.48}]}, "bone14": {"rotate": [{"angle": -2.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.56}]}, "niuyao_06": {"translate": [{"x": 1.85, "y": 0.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.71, "y": 0.41, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.85, "y": 0.21}]}, "bone15": {"translate": [{"x": 0.75, "y": -1.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.51, "y": -2.33, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.75, "y": -1.17}]}, "niuyao_6": {"rotate": [{"angle": -2.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.65, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.46}]}, "niuyao_7": {"rotate": [{"angle": -6.2, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.2}]}, "niuyao_8": {"rotate": [{"angle": -8.65, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -8.65}]}, "bone16": {"rotate": [{"angle": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.04, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.13}]}, "bone17": {"rotate": [{"angle": 7.91, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.91}]}, "bone18": {"rotate": [{"angle": 11.04, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 11.04}]}, "bone19": {"rotate": [{"angle": 10.76, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.03, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10.76}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone20": {"rotate": [{"angle": -2.32, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.17, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.32}]}, "bone21": {"rotate": [{"angle": -5.84, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": -2.32, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5.84}]}, "bone22": {"rotate": [{"angle": 13.41, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 14, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": 13.41}]}, "bone23": {"rotate": [{"angle": 11.68, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": 14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.9, "angle": 3.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 11.68}]}, "bone24": {"rotate": [{"angle": -6.65, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -8.78, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -6.65}]}, "bone25": {"rotate": [{"angle": -8.68, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "angle": -8.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7, "angle": -2.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.0333, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 2, "angle": -8.68}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.17, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone27": {"rotate": [{"angle": -2.32, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.17, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.32}]}, "bone28": {"rotate": [{"angle": -5.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5.85}]}, "bone34": {"rotate": [{"angle": 0.15}]}, "bone35": {"rotate": [{"angle": -1.55}]}, "bone36": {"rotate": [{"angle": 1.58}]}, "bone37": {"rotate": [{"angle": -1.13}]}, "bone38": {"rotate": [{"angle": 2.5}]}, "bone39": {"rotate": [{"angle": -1.42}]}, "bone41": {"rotate": [{"angle": -2.91, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.25, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.91}], "translate": [{"x": 0.82, "y": 0.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.88, "y": 1.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.82, "y": 0.53}]}, "bone42": {"rotate": [{"angle": -7.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -7.34}], "translate": [{"x": 2.07, "y": 1.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.88, "y": 1.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.07, "y": 1.34}]}, "bone43": {"rotate": [{"angle": -10.25, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -10.25}], "translate": [{"x": 2.88, "y": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 2.88, "y": 1.87}]}, "bone29": {"rotate": [{"angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.81, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 8.13}]}, "bone30": {"rotate": [{"angle": 3.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.89}]}, "bone31": {"rotate": [{"angle": -2.57, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.57}]}, "bone32": {"rotate": [{"angle": -6.81, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.81}]}, "bone33": {"rotate": [{"angle": -2.57, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -6.81, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.57}]}}}, "die": {"slots": {"niuyao_013": {"attachment": [{"time": 0.1333, "name": null}]}, "niuyao_1": {"attachment": [{"time": 0.1667, "name": "niuyao_01"}]}, "niuyao_01": {"attachment": [{"time": 0.1333, "name": null}]}}, "bones": {"bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 60.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 79.74, "curve": "stepped"}, {"time": 0.6667, "angle": 79.74}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 115.51, "y": 117.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 115.51, "y": 30.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 115.51, "y": 44.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 115.51, "y": 30.95}]}, "bone3": {"rotate": [{"time": 0.3333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 2.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333}]}, "niuyao_6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 54.59, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.4, "angle": 66.59, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.4333, "angle": 54.59}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -37.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -25.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "angle": -37.41}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -68.18}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -52.33}]}, "bone34": {"rotate": [{"angle": 0.15}]}, "bone35": {"rotate": [{"angle": -1.55}]}, "bone36": {"rotate": [{"angle": 1.58}]}, "bone37": {"rotate": [{"angle": -1.13}]}, "bone38": {"rotate": [{"angle": 2.5}]}, "bone39": {"rotate": [{"angle": -1.42}]}, "target1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 21.86, "y": 13.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -19.04, "y": -9.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": -4.45, "y": -9.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "x": -19.04, "y": -9.1}]}, "target3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 21.32, "y": -7.17}]}, "bone44": {"rotate": [{"angle": -47.73, "curve": "stepped"}, {"time": 0.1667, "angle": -47.73, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 65.91}], "translate": [{"x": 360, "y": -186.75, "curve": "stepped"}, {"time": 0.1667, "x": 360, "y": -186.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "x": 360, "y": -179.71, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 360, "y": -284.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 360, "y": -273.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "x": 360, "y": -284.62}]}}}, "hurt": {"slots": {"niuyao_013": {"attachment": [{"time": 0.1, "name": null}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone3": {"rotate": [{"angle": 1.51, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 5.31, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 1.51}]}, "bone4": {"rotate": [{"angle": 13.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 13.64}]}, "niuyao_6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -5.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 27.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "niuyao_7": {"rotate": [{"angle": 7.91, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 27.86, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 7.91}]}, "niuyao_8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 30.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 18.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone30": {"rotate": [{"angle": 3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.57, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3}]}, "bone31": {"rotate": [{"angle": 7.57, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 7.57}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone34": {"rotate": [{"angle": 0.15}]}, "bone35": {"rotate": [{"angle": -1.55}]}, "bone36": {"rotate": [{"angle": 1.58}]}, "bone37": {"rotate": [{"angle": -1.13}]}, "bone38": {"rotate": [{"angle": 2.5}]}, "bone39": {"rotate": [{"angle": -1.42}]}, "bone40": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 9.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -24.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}}}}}