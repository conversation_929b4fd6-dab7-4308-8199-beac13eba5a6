import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiHandlerFail } from "../../game/mgr/ApiHandler";
import { FriendSubCmd } from "../../game/net/cmd/CmdData";
import { BoolValue, IntValue } from "../../game/net/protocol/ExternalMessage";
import {
  FriendStatisticsResponse,
  TravelResUpdateMessage,
  TravelVitalityMessage,
} from "../../game/net/protocol/Friend";
import { TravelModule } from "./TravelModule";

export class TravelApi {


  /**获取游历模块基础信息 */
  public getTravelInfo(success) {
    ApiHandler.instance.request(
      TravelVitalityMessage,
      FriendSubCmd.travelVitality,
      null, //入参
      (res: TravelVitalityMessage) => {
        TravelModule.data.message = res;
        success && success(res);
      }
    );
  }

  /**请求进行游历 */
  public postTravel(bool: boolean, success, fail?) {
    ApiHandler.instance.request(
      TravelResUpdateMessage,
      FriendSubCmd.travel,
      BoolValue.encode({ value: bool }), //入参
      (res: TravelResUpdateMessage) => {
        TravelModule.data.vitality = res.vitality.vitality;
        TravelModule.data.vitalitySize = res.vitality.vitalitySize;
        TravelModule.data.lastUpdateTime = res.vitality.lastUpdateTime;
        success && success(res);
      },
      (error) => {
        fail && fail();
        return false;
      }
    );
  }

  /**游历查看统计信息 */
  public friendUnlockStatistics(success) {
    ApiHandler.instance.request(
      FriendStatisticsResponse,
      FriendSubCmd.friendUnlockStatistics,
      null,
      (res: FriendStatisticsResponse) => {
        success && success(res);
      }
    );
  }

  /**使用体力丹恢复精力 */
  public addTravelVitality(count, success) {
    ApiHandler.instance.request(
      TravelVitalityMessage,
      FriendSubCmd.addTravelVitality,
      IntValue.encode({ value: count.num }), //入参
      (res: TravelVitalityMessage) => {
        TravelModule.data.message = res;
        success && success(res);
      }
    );
  }

  /**恢复体力 --- 测试接口 */
  public testReseTravelVitality() {
    ApiHandler.instance.request(
      TravelVitalityMessage,
      FriendSubCmd.testReseTravelVitality,
      null,
      (res: TravelVitalityMessage) => {
        TravelModule.data.message = res;
      },
      (code, msg) => {
        //失败回调
        return false;
      }
    );
  }
}
