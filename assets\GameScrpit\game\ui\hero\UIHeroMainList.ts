import {
  _decorator,
  AssetManager,
  Component,
  EventTouch,
  Input,
  instantiate,
  Label,
  Node,
  Sprite,
  tween,
  v3,
} from "cc";
import { HeroAudioName, HeroSort, HeroType } from "../../../module/hero/HeroConfig";
import { HeroModule } from "../../../module/hero/HeroModule";
import { UIHeroCard } from "./UIHeroCard";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HeroRouteItem } from "../../../module/hero/HeroRoute";
import Formate from "../../../lib/utils/Formate";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { IConfigHero } from "../../JsonDefine";
import { AttrEnum } from "../../GameDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIHeroMainList")
export class UIHeroMainList extends Component {
  //下拉列表

  @property(Node)
  private heroViewHolder: Node;
  @property(Node)
  private nodeDivider: Node;
  @property(Node)
  private listContent: Node;
  @property(Node)
  private lblHeroPower: Node;
  @property(Node)
  private lbHeroDefense: Node;
  @property(Node)
  private lbHeroblood: Node;
  @property(Node)
  private lbHeroTotalNum: Node;
  @property(Node)
  private lblHeroNum: Node;
  @property(Node)
  private btnBlank: Node;
  @property(Node)
  private bgSort: Node;
  @property(Node)
  private nodeType: Node;

  // 种族枚举
  // typeEnum: TypeEnum = null;
  sort = HeroSort.DEFAULT;
  heroType = HeroType.ALL;
  private indexOwn: number = 0;
  private indexUnOwned: number = 0;

  currentOwnedHero: IConfigHero[];
  currentUnOwnedHero: IConfigHero[];
  heroNodeMap: Map<number, Node> = new Map();
  heroBundle: AssetManager.Bundle = null;
  protected onLoad(): void {
    this.currentOwnedHero = HeroModule.data.getOwnedHeros(HeroSort.DEFAULT, HeroType.ALL);
    this.currentUnOwnedHero = HeroModule.data.getUnOwnedHeros();
  }

  start() {
    this.refreshBaseUIinfo();
    MsgMgr.on(MsgEnum.ON_HERO_UPDATE, this.heroUpdate, this);
  }

  protected onDestroy(): void {
    // super.onDestroy();
    MsgMgr.off(MsgEnum.ON_HERO_UPDATE, this.heroUpdate, this);
  }

  update(deltaTime: number) {
    // 刷新战将列表
    if (this.currentOwnedHero && this.indexOwn < this.currentOwnedHero.length) {
      let hero = this.currentOwnedHero[this.indexOwn];
      let heroMessage = HeroModule.data.getHeroMessage(hero.id);
      let card = instantiate(this.heroViewHolder);
      card.getComponent(UIHeroCard).initInfo(hero, heroMessage.level, this.indexOwn++);
      card.on(Input.EventType.TOUCH_END, this.showDetail, this);
      let index = this.nodeDivider.getSiblingIndex();
      card.active = true;
      this.listContent.insertChild(card, index);

      tween(card)
        .set({ scale: v3(0.1, 0.1, 1) })
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
        .start();
      this.heroNodeMap.set(hero.id, card);
    }
    if (this.currentUnOwnedHero && this.indexUnOwned < this.currentUnOwnedHero.length) {
      let hero = this.currentUnOwnedHero[this.indexUnOwned];
      let card = instantiate(this.heroViewHolder);
      card.getComponent(UIHeroCard).initInfo(hero, 0, this.indexUnOwned++);
      card.on(Input.EventType.TOUCH_END, this.showPreview, this);
      card.active = true;
      this.listContent.addChild(card);

      tween(card)
        .set({ scale: v3(0.1, 0.1, 1) })
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
        .start();
      this.heroNodeMap.set(hero.id, card);
    }
  }

  private showPreview(e: EventTouch) {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击战将卡牌);
    const node: Node = e.target;
    let position = node.getComponent(UIHeroCard).position;
    UIMgr.instance.showDialog(HeroRouteItem.UIHeroDetail, [this.currentUnOwnedHero, position]);
  }

  private showDetail(e: EventTouch) {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击战将卡牌);
    const node: Node = e.target;
    let position = node.getComponent(UIHeroCard).position;
    UIMgr.instance.showDialog(HeroRouteItem.UIHeroDetail, [this.currentOwnedHero, position]);
  }
  private refreshBaseUIinfo() {
    let attrs = HeroModule.service.getAllHeroAttrMap();
    this.lblHeroPower.getComponent(Label).string = `${Formate.format(attrs[AttrEnum.攻击_2])}`;
    this.lbHeroDefense.getComponent(Label).string = `${Formate.format(attrs[AttrEnum.防御_3])}`;
    this.lbHeroblood.getComponent(Label).string = `${Formate.format(attrs[AttrEnum.生命_1])}`;
    this.lbHeroTotalNum.getComponent(Label).string = `/${
      this.currentOwnedHero?.length + this.currentUnOwnedHero?.length
    }`;
    this.lblHeroNum.getComponent(Label).string = `${this.currentOwnedHero?.length}`;
  }

  //刷新影响队列信息 分针加载不可调用此方法 因为会加载对应的hero实例
  private refreshUIHeroListInfo() {
    // this.syncHeroListAndMap(this.hasHeroIdxList, true);
    // this.syncHeroListAndMap(this.haveNotHeroIdxList, false);
    this.currentOwnedHero = HeroModule.data.getOwnedHeros(this.sort, this.heroType);
    this.currentUnOwnedHero = HeroModule.data.getUnOwnedHeros(this.heroType);
    this.listContent.removeAllChildren();
    //插入分割条
    this.listContent.addChild(this.nodeDivider);
    this.currentOwnedHero.forEach((hero, i) => {
      let heroId = hero.id;
      let heroNode = this.heroNodeMap.get(heroId);
      heroNode.getComponent(UIHeroCard).updatePosition(i);
      let index = this.nodeDivider.getSiblingIndex();
      this.listContent.insertChild(heroNode, index);
    });
    this.currentUnOwnedHero.forEach((hero, i) => {
      let heroId = hero.id;
      let heroNode = this.heroNodeMap.get(heroId);
      heroNode.getComponent(UIHeroCard).updatePosition(i);
      this.listContent.addChild(heroNode);
    });
  }
  private heroUpdate() {
    if (this?.node) {
      this.refreshBaseUIinfo();
    }
  }
  // 种族选择
  private onSelectType(e: EventTouch) {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击种族图标);

    if (this.indexOwn < this.currentOwnedHero.length || this.indexUnOwned < this.currentUnOwnedHero.length) {
      // 等待页面刷新完成
      return;
    }
    this.nodeType.children.forEach((val) => {
      if (val.name == e.target.name) {
        val.getChildByName("btn_select").active = true;
        val.getComponent(Sprite).grayscale = false;
      } else {
        val.getChildByName("btn_select").active = false;
        val.getComponent(Sprite).grayscale = false;
        // val.getComponent(Sprite).grayscale = true;
      }
    });
    switch (e.target.name) {
      case "btn0":
        this.heroType = HeroType.ALL;
        break;
      case "btn1":
        this.heroType = HeroType.PERSON;
        break;
      case "btn2":
        this.heroType = HeroType.GOD;
        break;
      case "btn3":
        this.heroType = HeroType.YAO;
        break;
      case "btn4":
        this.heroType = HeroType.MING;
        break;
      case "btn5":
        this.heroType = HeroType.WU;
        break;
    }
    this.refreshUIHeroListInfo();
  }
  // 点击选择
  private onSelectSort(e: EventTouch) {
    AudioMgr.instance.playEffect(524);
    switch (e.target.name) {
      case "lbl_sort1":
        this.bgSort.getChildByName("lblSort").getComponent(Label).string = "默认排序";
        this.sort = HeroSort.DEFAULT;
        break;
      case "lbl_sort2":
        this.bgSort.getChildByName("lblSort").getComponent(Label).string = "战力排序";
        this.sort = HeroSort.POWER;
        break;
      case "lbl_sort3":
        this.bgSort.getChildByName("lblSort").getComponent(Label).string = "资质排序";
        this.sort = HeroSort.ABILITY;
        break;
      case "lbl_sort4":
        this.bgSort.getChildByName("lblSort").getComponent(Label).string = "精进排序";
        this.sort = HeroSort.LEVEL;
        break;
    }
    // e.currentTarget.parent.active = false;
    this.btnBlank.active = false;
    this.bgSort.getChildByName("iconSelect").setScale(1, -1);

    this.refreshUIHeroListInfo();
  }
  // 选择排序方式 点击展开/收起
  private onClickSort(e: EventTouch) {
    AudioMgr.instance.playEffect(524);
    let active = !this.btnBlank.active;
    this.btnBlank.active = active;
    this.bgSort.getChildByName("iconSelect").setScale(1, active ? -1 : 1);
  }
  /**
   * 点击下拉窗外部，空白部分
   */
  private onClickBlank() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.btnBlank.active = false;
    this.bgSort.getChildByName("iconSelect").setScale(1, -1);
  }
}
