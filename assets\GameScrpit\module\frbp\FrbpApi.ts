import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { FrbpCmd } from "../../game/net/cmd/CmdData";
import {
  AchieveRewardRequest,
  AchieveRewardResponse,
  ProsperityMessage,
  RedeemRequest,
  RedeemResponse,
  SimpleRankMessage,
} from "../../game/net/protocol/Activity";
import { RewardMessage } from "../../game/net/protocol/Comm";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import TipMgr from "../../lib/tips/TipMgr";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { ActivityModule } from "../activity/ActivityModule";
import { activityId, FrbpModule } from "./FrbpModule";

export class FrbpApi {
  public getFrbpInfo(id: number, success?: ApiHandlerSuccess) {
    // if (ActivityModule.service.checkActivityUnlock(activityId) == false) {
    //   return;
    // }
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.request(
      ProsperityMessage,
      FrbpCmd.getAchieveInfo,
      LongValue.encode(data),
      (res: ProsperityMessage) => {
        FrbpModule.data.ProsperityMessage = res;
        success && success(res);
      }
    );
  }

  public getRankList(id: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    // if (ActivityModule.service.checkActivityUnlock(activityId) == false) {
    //   return;
    // }
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.request(
      SimpleRankMessage,
      FrbpCmd.getRankList,
      LongValue.encode(data),
      (res: SimpleRankMessage) => {
        FrbpModule.data.SimpleRankMessage = res;
        success && success(res);
      }
    );
  }

  public async getRankAward(id: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    // if (ActivityModule.service.checkActivityUnlock(activityId) == false) {
    //   return;
    // }

    let vo = await FrbpModule.data.getVO(activityId);
    vo.publicityTime;
    vo.endTime;

    if (TimeUtils.serverTime < vo.publicityTime || TimeUtils.serverTime > vo.endTime) {
      TipMgr.showTip("公示期可以领取");
      return;
    }
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.request(RewardMessage, FrbpCmd.takeRankReward, LongValue.encode(data), (res: RewardMessage) => {
      success && success(res);
    });
  }

  /**消耗道具兑换或免费领取固定礼包道具 -----  每日礼包，累计回馈都有使用 */
  public buyFixedPack(param: RedeemRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      RedeemResponse,
      FrbpCmd.buyFixedPack,
      RedeemRequest.encode(param),
      (data: RedeemResponse) => {
        FrbpModule.data.ProsperityMessage.redeemMap = data.redeemMap;
        FrbpModule.data.ProsperityMessage.adMap = data.adMap;
        FrbpModule.data.ProsperityMessage.chosenMap = data.chosenMap;
        success && success(data);
      }
    );
  }

  public takeAchieveReward(param: AchieveRewardRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      AchieveRewardResponse,
      FrbpCmd.takeAchieveReward,
      AchieveRewardRequest.encode(param),
      (data: AchieveRewardResponse) => {
        FrbpModule.data.ProsperityMessage.achieveMap[data.achieve.id] = data.achieve;
        success && success(data);
      }
    );
  }
}
