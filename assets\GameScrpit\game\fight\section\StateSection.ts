import GameObject, { GO_STATE } from "../../../lib/object/GameObject";
import { Section } from "../../../lib/object/Section";
import ToolExt from "../../common/ToolExt";
import Atk from "../../state/Atk";
import Atk2_1 from "../../state/Atk2_1";
import Atk2_2 from "../../state/Atk2_2";
import Atk3 from "../../state/Atk3";
import Die from "../../state/Die";
import Hurt from "../../state/Hurt";
import Idle from "../../state/Idle";
import { ActionEffect, ActionType } from "../FightDefine";
import FSMMachine from "../FSM/FSMMachine";
import { GORole } from "../role/GORole";

export enum STATE {
  IDLE = 0,
  ATK1 = 1,
  ATK2_1 = 2,
  ATK2_2 = 3,
  ATK3 = 4,
  DIE = 5,
  HURT = 6,
}

export class FSMBoard {
  private _sub: GORole;
  public set sub(role: GORole) {
    this._sub = role;
  }
  public get sub(): GORole {
    return this._sub;
  }

  private _roundMovementInfo: MovementInfo;

  public set roundMovementInfo(info: MovementInfo) {
    this._roundMovementInfo = info;
  }
  public get roundMovementInfo(): MovementInfo {
    return this._roundMovementInfo;
  }
}

export class MovementInfo {
  /**技能id */
  skillId: number;
  /**受伤对象 */
  hurtRole: GORole;
  /**动作类型 */
  actionType: ActionType;
  /**动作具体信息 */
  movementInfo: { [key: number]: ActionEffect };
  /**await 回调 */
  resolveBack: Function;
}

export default class StateSection extends Section {
  protected _target: GORole;
  protected _machine: FSMMachine;
  protected _board: FSMBoard;

  public get board() {
    return this._board;
  }

  public static sectionName(): string {
    return "StateSection";
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.ready();
  }

  public onStart() {
    this.onMsg("OnSwitchState", this.onSwitchState.bind(this));
    this.onMsg("OnRoleDead", this.onRoleDead.bind(this));
    this.onMsg("OnMovementInfo", this.OnMovementInfo.bind(this));
    this.createRoleState();
  }

  public onRemove(): void {
    super.onRemove();
    this.offMsg("OnSwitchState", this.onSwitchState.bind(this));
    this.offMsg("OnRoleDead", this.onRoleDead.bind(this));
    this.offMsg("OnMovementInfo", this.OnMovementInfo.bind(this));
  }

  private OnMovementInfo(info: MovementInfo) {
    this._board.roundMovementInfo = info;
  }

  private createRoleState() {
    this._board = new FSMBoard();
    this._board.sub = this.getSub() as GORole;
    this._machine = new FSMMachine(this._board);
    let idle = new Idle(STATE.IDLE);
    idle.addTranslate(STATE.IDLE);
    idle.addTranslate(STATE.ATK1);
    idle.addTranslate(STATE.ATK2_1);
    idle.addTranslate(STATE.ATK3);
    idle.addTranslate(STATE.HURT);
    idle.addTranslate(STATE.DIE);

    let atk1 = new Atk(STATE.ATK1);
    atk1.addTranslate(STATE.IDLE);
    atk1.addTranslate(STATE.ATK1);
    atk1.addTranslate(STATE.ATK2_1);
    atk1.addTranslate(STATE.ATK3);

    let akt2_1 = new Atk2_1(STATE.ATK2_1);
    akt2_1.addTranslate(STATE.IDLE);
    akt2_1.addTranslate(STATE.ATK2_2);
    akt2_1.addTranslate(STATE.ATK1);
    akt2_1.addTranslate(STATE.ATK2_1);
    akt2_1.addTranslate(STATE.ATK3);

    let atk2_2 = new Atk2_2(STATE.ATK2_2);
    atk2_2.addTranslate(STATE.IDLE);
    atk2_2.addTranslate(STATE.ATK1);
    atk2_2.addTranslate(STATE.ATK2_1);
    atk2_2.addTranslate(STATE.ATK3);

    let atk3 = new Atk3(STATE.ATK3);
    atk3.addTranslate(STATE.IDLE);
    atk3.addTranslate(STATE.ATK1);
    atk3.addTranslate(STATE.ATK2_1);
    atk3.addTranslate(STATE.ATK3);

    let hurt = new Hurt(STATE.HURT);
    hurt.addTranslate(STATE.IDLE);
    hurt.addTranslate(STATE.DIE);
    hurt.addTranslate(STATE.ATK1);
    hurt.addTranslate(STATE.ATK2_1);
    hurt.addTranslate(STATE.ATK3);

    let die = new Die(STATE.DIE);

    this._machine.addState(idle);
    this._machine.addState(atk1);
    this._machine.addState(akt2_1);
    this._machine.addState(atk2_2);
    this._machine.addState(atk3);
    this._machine.addState(hurt);
    this._machine.addState(die);
    this._machine.setBeginState(STATE.IDLE);
  }

  protected _pointarget: GORole = null;
  public setPointTarget(role: GORole) {
    this._pointarget = role;
  }

  protected onSwitchState(id: number) {
    this._machine.translateState(id);
  }

  public getFSMState() {
    return this._machine.getCurrentStateId();
  }

  public getFSMStateById(id: STATE) {
    return this._machine.getState(id);
  }

  public updateSelf(dt) {
    if (!this._machine) {
      return;
    }
    this._machine.update(dt);
  }

  protected onRoleDead() {
    this._machine.translateState(STATE.DIE);
  }

  public setTarget(role: GORole) {
    this._target = role;
  }

  public getTarget(): GORole {
    return this._target;
  }
}
