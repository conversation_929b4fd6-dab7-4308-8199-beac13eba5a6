CCEffect %{
  techniques:
  - passes:
    - vert: sprite-vs:vert
      frag: sprite-fs:frag
      depthStencilState:
        depthTest: false
        depthWrite: false
      blendState:
        targets:
        - blend: true
          blendSrc: src_alpha
          blendDst: one_minus_src_alpha
          blendDstAlpha: one_minus_src_alpha
      rasterizerState:
        cullMode: none
      properties:
        alphaThreshold: { value: 0.5 }

        ## 自定义参数
        wh_ratio: { value: 1, editor: { tooltip: "宽高比"}}
        blur: { value: 0.01, editor: { tooltip: "光圈模糊程度"}}
        radius: { value: 0.5, editor: { tooltip: "光圈半径"}}
        center: { value: [0.5, 0.5], editor: { tooltip: "光圈中心"}}
}%


CCProgram sprite-vs %{
  precision highp float;
  #include <builtin/uniforms/cc-global>
  #if USE_LOCAL
    #include <builtin/uniforms/cc-local>
  #endif

  in vec3 a_position;
  in vec2 a_texCoord;
  in vec4 a_color;

  out vec4 v_color;
  out vec2 v_uv0;

  vec4 vert () {
    vec4 pos = vec4(a_position, 1);

    #if USE_LOCAL
      pos = cc_matWorld * pos;
    #endif

    #if USE_PIXEL_ALIGNMENT
      pos = cc_matView * pos;
      pos.xyz = floor(pos.xyz);
      pos = cc_matProj * pos;
    #else
      pos = cc_matViewProj * pos;
    #endif

    v_color = a_color;
    v_uv0 = a_texCoord;

    return pos;
  }
}%

CCProgram sprite-fs %{
  precision highp float;
  #include <builtin/internal/embedded-alpha>
  #include <builtin/internal/alpha-test>

  in vec4 v_color;

  #if USE_TEXTURE
    in vec2 v_uv0;
    #pragma builtin(local)
    layout(set = 2, binding = 11) uniform sampler2D cc_spriteTexture;
  #endif

  uniform ARGS{
    float radius;
    float blur;
    vec2 center;
    float wh_ratio;
  };

  vec4 frag () {
    vec4 o = vec4(1, 1, 1, 1);
    o *= CCSampleWithAlphaSeparated(cc_spriteTexture, v_uv0);
    o *= v_color;

    ALPHA_TEST(o);

    float circle = radius * radius;
    float rx = (v_uv0.x - center.x) * wh_ratio;
    float ry = v_uv0.y - center.y;
    float dis = rx * rx + ry * ry;

    o.a = smoothstep(circle, circle - blur, dis) * o.a;

    return o;
  }
}%