<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>bg_9g_jianglidi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{28,28}</string>
                <key>spriteSourceSize</key>
                <string>{28,28}</string>
                <key>textureRect</key>
                <string>{{105,912},{28,28}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jiangliyulan.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{216,51}</string>
                <key>spriteSourceSize</key>
                <string>{216,51}</string>
                <key>textureRect</key>
                <string>{{685,1},{216,51}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_judianbiaotidi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{644,98}</string>
                <key>spriteSourceSize</key>
                <string>{644,98}</string>
                <key>textureRect</key>
                <string>{{105,812},{644,98}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_judianjindutiao.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{359,15}</string>
                <key>spriteSourceSize</key>
                <string>{367,23}</string>
                <key>textureRect</key>
                <string>{{724,219},{359,15}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_judianjindutiaodi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{367,23}</string>
                <key>spriteSourceSize</key>
                <string>{367,23}</string>
                <key>textureRect</key>
                <string>{{135,912},{367,23}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_tanchuangdi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{682,809}</string>
                <key>spriteSourceSize</key>
                <string>{682,809}</string>
                <key>textureRect</key>
                <string>{{1,1},{682,809}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_baoxiang.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{30,22}</string>
                <key>spriteSourceSize</key>
                <string>{30,22}</string>
                <key>textureRect</key>
                <string>{{504,912},{30,22}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_duizhan.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{37,36}</string>
                <key>spriteSourceSize</key>
                <string>{37,36}</string>
                <key>textureRect</key>
                <string>{{685,219},{37,36}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>zi_yitongguan.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{129,102}</string>
                <key>spriteSourceSize</key>
                <string>{129,102}</string>
                <key>textureRect</key>
                <string>{{1,812},{129,102}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UICityFight.png</string>
            <key>size</key>
            <string>{750,942}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:b08fc8112bdd4355756a9d7aeb5b6c00:f0b031737424a2311e8baa5a3ac67f9e:c7a4f81ef59207e0c56c4e81caae3bda$</string>
            <key>textureFileName</key>
            <string>UICityFight.png</string>
        </dict>
    </dict>
</plist>
