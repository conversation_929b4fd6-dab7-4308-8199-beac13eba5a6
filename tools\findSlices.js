const readline = require("readline");
const fs = require("fs");
const path = require("path");

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function copyFileto(source, destination, names, logArea) {
  const sourDir = path.resolve(source);
  if (!fs.existsSync(sourDir)) {
    console.log("源目录不存在。");
    return;
  }
  const disDir = path.resolve(destination);
  if (!fs.existsSync(disDir)) {
    fs.mkdirSync(disDir, { recursive: true });
  }
  const resultFiles = [];
  findFiles(sourDir, names, resultFiles);
  let success = 0,
    faild = 0;
  resultFiles.forEach((file) => {
    try {
      const destFilePath = path.join(disDir, path.basename(file));
      fs.copyFileSync(file, destFilePath);
      console.log(`${file}\t:SUCCESS`);
      success++;
    } catch (err) {
      console.error(err);
      console.log(`${file}\t:FAILED`);
      faild++;
    }
  });
  names.forEach((name) => {
    console.log(`${name}\t:未找到`);
  });
  console.log(
    `\n文件总数:${names.length + resultFiles.length}\t成功:${success}\t失败:${faild}\t未找到:${names.length}\n`
  );
}

function findFiles(directory, names, results) {
  const files = fs.readdirSync(directory);
  files.forEach((file) => {
    if (names.length < 1) {
      return;
    }
    const filePath = path.join(directory, file);
    if (fs.statSync(filePath).isDirectory()) {
      findFiles(filePath, names, results);
    }
    names.forEach((name) => {
      if (file == `${name}.png`) {
        results.push(filePath);
        names.splice(names.indexOf(name), 1);
      }
    });
  });
}

rl.question("请输入源目录路径：", (fromDir) => {
  rl.question("请输入目标目录路径：", (toDir) => {
    rl.question("请输入要查找的文件名：", (fileNames) => {
      // const namesToFind = fileNames.split(' ');
      const fileNamesRegex = /S\d+|item_\d+/g;
      const namesToFind = fileNames.match(fileNamesRegex);
      console.log(namesToFind);
      copyFileto(fromDir, toDir, namesToFind);
      rl.close();
    });
  });
});
