syntax = "proto3";
package sim;
import "Player.proto";

// 
message ChatPackAddRequest {
  // CLUB(1,"妖盟"),LOCAL(2,"本地"),CROSS(3,"跨服")
  int32 marketType = 1;
  // 消息的类型  1-文本
  int32 messageType = 2;
  // 消息体
  string content = 3;
  // 列出提到的用户ID
  repeated int64 mentionedUsers = 4;
}

// 
message ChatPackFindRequest {
  // 开始的序列  start和end可以为<=0，表示序列以后和序列之前 （start,end） 左开右开
  int64 start = 1;
  // 结束序列
  int64 end = 2;
  // 频道 CLUB(1,"妖盟"),LOCAL(2,"本地"),CROSS(3,"跨服")
  int32 marketType = 3;
}

// 
message ChatPackMessage {
  // 频道  1-妖盟 2-本地 3-跨服
  int32 marketType = 1;
  // 仙盟频道才有效
  int64 groupId = 2;
  // 玩家信息
  sim.PlayerBaseMessage sender = 3;
  // 记录已经看过的信息ID
  int64 sequence = 4;
  // 消息的类型  1-文本
  int32 messageType = 5;
  // 消息体
  string content = 6;
  // 列出提到的用户ID
  repeated int64 mentionedUsers = 7;
  // 创建时间
  int64 createTime = 8;
}

