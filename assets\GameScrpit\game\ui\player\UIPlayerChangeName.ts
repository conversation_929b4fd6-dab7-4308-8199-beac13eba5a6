import { _decorator, Component, EditBox, Input, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { DialogZero } from "../../GameDefine";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { IConfigLeaderRecord } from "../../../module/player/PlayerConfig";
import { Label } from "cc";
import { PlayerRenameResponse } from "../../net/protocol/Player";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("UIPlayerChangeName")
export class UIPlayerChangeName extends UINode {
  protected _openAct: boolean = true; //打开动作
  public zOrder(): number {
    return DialogZero.UIPlayerChangeName;
  }
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PLAYER}?prefab/ui/UIPlayerChangeName`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  /** 初始化显示事件 */
  protected onEvtShow() {
    this.initPopup();
  }

  private _isFirst: boolean;
  private _playerName: string;

  private initPopup() {
    this._isFirst = PlayerModule.data.getPlayerInfo().renameCount == 0;
    this.getNode("curName").getComponent(Label).string = PlayerModule.data.getPlayerInfo().nickname;

    this.getNode("btn_firstUse").active = this._isFirst;
    this.getNode("btn_use").active = !this._isFirst;
  }

  private on_click_btn_firstUse() {
    this.changeName();
  }

  private on_click_btn_use() {
    this.changeName();
  }

  private changeName() {
    let configLeader = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level);
    if (PlayerModule.data.getItemNum(configLeader.nameCostList[0]) < configLeader.nameCostList[1]) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: configLeader.nameCostList[0],
        needNum: configLeader.nameCostList[1],
      });
      return;
    }
    this._playerName = this.getNode("editBox").getComponent(EditBox).string;
    if (!this._playerName || this._playerName == "") {
      TipsMgr.showErrX(1000000000, ["请输入名字"]);
      return;
    }
    PlayerModule.api.rename(
      this._playerName,
      (data: PlayerRenameResponse) => {
        UIMgr.instance.back();
      },
      (errorCode: number, msg: string[], data: any): boolean => {
        TipsMgr.showErrX(errorCode, msg);
        this._playerName = "";
        this.getNode("editBox").getComponent(EditBox).string = "";
        return true;
      }
    );
  }
}
