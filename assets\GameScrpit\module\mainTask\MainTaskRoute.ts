import { UITaskAutoClickPop } from "../../game/ui/mainTask/UITaskAutoClickPop";
import { UITaskMainPopup } from "../../game/ui/mainTask/UITaskMainPopup";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum MainTaskRouteName {
  UITaskMainPopup = "UITaskMainPopup",
  UITaskAutoClickPop = "UITaskAutoClickPop",
}
export class MainTaskRoute {
  rotueTables: Recording[] = [
    {
      node: UITaskMainPopup,
      uiName: MainTaskRouteName.UITaskMainPopup,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UITaskAutoClickPop,
      uiName: MainTaskRouteName.UITaskAutoClickPop,
      keep: false,
      relevanceUIList: [],
    },
  ];
  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
