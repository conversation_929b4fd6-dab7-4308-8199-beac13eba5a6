import { _decorator, EditBox } from "cc";
import CenterHttpApi from "../../GameScrpit/game/httpNet/CenterHttpApi";
import StorageMgr, { StorageKeyEnum } from "../../platform/src/StorageHelper";
import { SceneLogin } from "../../script_game/scene_login/SceneLogin";
import { RouteSceneLogin } from "../../script_game/scene_login/RouteSceneLogin";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { FmConfig } from "../../GameScrpit/game/GameDefine";
import { TimeUtils } from "../../GameScrpit/lib/utils/TimeUtils";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";

const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;
@ccclass("PageLogin")
export class PageLogin extends BaseCtrl {
  playShowAni: boolean = true;

  @property(EditBox)
  private editAccount: EditBox;

  @property(EditBox)
  private editPsd: EditBox;

  // 冷却时间
  cd: number = 0;

  start() {
    super.start();
    // 初始化
    StorageMgr.userId = "";
    this.editAccount.string = StorageMgr.loadStr(StorageKeyEnum.LoginName);
    this.editPsd.string = StorageMgr.loadStr(StorageKeyEnum.Psd);
  }

  onGoRegedit() {
    SceneLogin.routeMgr.replaceDailog(RouteSceneLogin.PageRegister);
    this.editAccount.string = "";
    this.editPsd.string = "";
  }

  onLogin() {
    if (this.cd > new Date().getTime()) {
      // todo  显示加载界面
      TipsMgr.showTip("正在登录中");
      return;
    }
    this.cd = new Date().getTime() + 3000;

    let account = this.editAccount.string;
    let psd = this.editPsd.string;

    if (account) {
      TipsMgr.setEnableTouch(false, 3);
      log.log("登录数据===", account, psd);
      CenterHttpApi.login(account, psd).then(async (res: any) => {
        TipsMgr.setEnableTouch(true, 3);
        if (res.code === 200) {
          log.log("登录成功===", res);

          StorageMgr.saveItem(StorageKeyEnum.LoginName, account);
          StorageMgr.saveItem(StorageKeyEnum.Psd, psd);

          StorageMgr.userId = res.data.accountId;

          TipsMgr.setEnableTouch(false, 0.5);
          SceneLogin.routeMgr.back();
          // CenterHttpApi.announcement(0)
          //   .then((resp: any) => {
          //     if (resp.code != 200) {
          //       log.error(resp);
          //       return;
          //     }
          //     if (isValid(SceneLogin.instance?.node) && SceneLogin.routeMgr) {
          //       const routeInfo = RouteSceneLogin.PageNotice;
          //       routeInfo.args = resp.data;
          //       SceneLogin.routeMgr.showDialog(routeInfo);
          //     }
          //   })
          //   .catch(() => {
          //     log.error("公告获取失败");
          //   });
          // 游戏健康检测
          // this.checkHealthLogin(res.data);
        } else {
          TipsMgr.showTip("登录失败");
        }
      });
    } else {
      TipsMgr.showTipX(252, []);
    }
  }

  on_close() {
    SceneLogin.routeMgr.back();
  }

  // 游戏健康检测
  private checkHealthLogin(loginInfo: any): boolean {
    // 检查是否开启功能
    if (!FmConfig.gameHealthCheck) {
      // 暂时只在审核版本开启此功能
      return;
    }
    // 检查是否实名认证
    if (!loginInfo.age || loginInfo.age <= 0) {
      // 需要进行实名认证 需弹出实名认证窗口
      // UIMgr.instance.showDialog(GameHealthRouteItem.UIGameHealthAuth);
      SceneLogin.routeMgr.showDialog(RouteSceneLogin.UIGameHealthAuth);
      return;
    }
    // 检查是否未成年
    if (loginInfo.age < 16) {
      // 年龄限制范围外，不可进行游戏 弹窗提示
      SceneLogin.routeMgr.showDialog(RouteSceneLogin.UIGameHealthAuthFailTips);
      return;
    }
    if (loginInfo.age < 18 && loginInfo.force_logout_time > TimeUtils.serverTime) {
      // 表示可以在指定的时间内进行游戏，需弹窗提示
      SceneLogin.routeMgr.showDialog(RouteSceneLogin.UIGameHealthAuthOKTips);
      return;
    } else if (loginInfo.force_logout_time < 0) {
      SceneLogin.routeMgr.showDialog(RouteSceneLogin.UIGameHealthAuthFailTips);
      return;
    }
  }
}
