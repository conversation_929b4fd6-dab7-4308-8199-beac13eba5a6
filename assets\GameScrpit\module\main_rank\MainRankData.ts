import MsgEnum from "../../game/event/MsgEnum";
import { PlayerRankBoardMessage } from "../../game/net/protocol/Player";
import MsgMgr from "../../lib/event/MsgMgr";

export class MainRankData {
  private _playerRankBoardMessage: PlayerRankBoardMessage;
  public get playerRankBoardMessage() {
    return this._playerRankBoardMessage;
  }
  public set playerRankBoardMessage(val: PlayerRankBoardMessage) {
    this._playerRankBoardMessage = val;
    MsgMgr.emit(MsgEnum.ON_MAIN_RANK_UPDATE);
  }
}
