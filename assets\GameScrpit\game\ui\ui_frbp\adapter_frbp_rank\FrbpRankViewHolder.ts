import { _decorator, Component, Label, Node, RichText } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import ToolExt from "../../../common/ToolExt";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
const { ccclass, property } = _decorator;

@ccclass("FrbpRankViewHolder")
export class FrbpRankViewHolder extends ViewHolder {
  @property(Node)
  public BtnHeader: Node = null;

  @property(Node)
  public lbl_rank_item: Node = null;

  @property(Node)
  public lbl_nickname_item: Node = null;

  @property(Node)
  public lbl_point_item: Node = null;

  public init() {}

  updateData(data: any, position: number) {
    if (position % 2 == 1) {
      this.node.getChildByName("bg").active = true;
    } else {
      this.node.getChildByName("bg").active = false;
    }

    this.lbl_rank_item.getComponent(Label).string = position + 4 + "";

    let detailMessage = data.detailMessage;
    let simpleMessage = detailMessage.simpleMessage;
    this.lbl_nickname_item.getComponent(Label).string = simpleMessage.nickname;
    this.lbl_point_item.getComponent(Label).string = Formate.format(data.point);

    let avatarList = simpleMessage.avatarList;
    if (avatarList[3] != -1) {
      this.node.getChildByName("lv_root").active = false;
      this.node.getChildByName("title_root").active = true;
      this.node.getChildByName("title_root").destroyAllChildren();
      PlayerModule.service.createTitle(this.node.getChildByName("title_root"), avatarList[3], (node: Node, db) => {});
    } else {
      this.node.getChildByName("lv_root").active = true;
      this.node.getChildByName("title_root").active = false;
      let level = simpleMessage.level;
      let info = PlayerModule.data.getConfigLeaderData(level);
      this.node.getChildByPath("lv_root/lab_lv_jingjie").getComponent(RichText).string = info.jingjie2;
    }

    FmUtils.setHeaderNode(this.BtnHeader, simpleMessage);
  }
}
