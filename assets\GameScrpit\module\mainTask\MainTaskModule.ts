import MsgEnum from "../../game/event/MsgEnum";
import data from "../../lib/data/data";
import MsgMgr from "../../lib/event/MsgMgr";
import MainTaskApi from "./MainTaskApi";
import { MainTaskData } from "./MainTaskData";
import { MainTaskRoute } from "./MainTaskRoute";
import { MainTaskService } from "./MainTaskService";
import { MainTaskSubscriber } from "./MainTaskSubscriber";
import { GameData } from "../../game/GameData";

export class MainTaskModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): MainTaskModule {
    if (!GameData.instance.MainTaskModule) {
      GameData.instance.MainTaskModule = new MainTaskModule();
    }
    return GameData.instance.MainTaskModule;
  }
  private _data = new MainTaskData();
  private _api = new MainTaskApi();
  private _service = new MainTaskService();
  private _route = new MainTaskRoute();
  private _subscriber = new MainTaskSubscriber();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new MainTaskData();
    this._api = new MainTaskApi();
    this._service = new MainTaskService();
    this._route = new MainTaskRoute();
    this._subscriber = new MainTaskSubscriber();

    this._route.init();
    this._subscriber.register();

    MainTaskModule.api.getMainTaskInfo((data) => {
      completedCallback && completedCallback();
    });

    MsgMgr.once(
      MsgEnum.ON_GAME_START,
      () => {
        // 订阅任务消息
        MsgMgr.on(MsgEnum.ON_MAINTASK_UPDATE, this._service.showTipsComplete, this._service);
      },
      this
    );
  }
}
