import { _decorator, Layout, Node, sp, tween, UITransform, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { DialogZero, tweenTagEnum } from "../../GameDefine";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { TravelAudioName } from "../../../module/travel/TravelConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UITravelGet")
export class UITravelGet extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_TRAVEL}?prefab/ui/UITravelGet`;
  }
  protected _isAddToBottom: boolean = true;
  public zOrder(): number {
    return DialogZero.UITravelGet;
  }

  private _resAddList: any = null;

  public init(args: any): void {
    super.init(args);
    this._resAddList = args.resAddList;
  }

  protected onEvtShow(): void {
    log.log("游历发现的奖励展示====", this._resAddList);
    AudioMgr.instance.playEffect(TravelAudioName.Effect.获得奖励音乐);
    this.playeAction();
  }

  private playeAction() {
    this.getNode("bg").scale = v3(0, 0, 0);
    this.getNode("tytanchuang")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_youlifaxian_1", true);
      });
    this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_youlifaxian", false);
    tween(this.getNode("bg"))
      .tag(tweenTagEnum.UITravelGet_Tag)
      .to(0.1, { scale: v3(1, 1, 1) })
      .call(() => {
        let data = ToolExt.traAwardItemMapList(this._resAddList);
        let layerList = ToolExt.minuteItemLayer(data, 5);
        this.loadItem(layerList);
      })
      .start();
  }

  private loadItem(layerList: Array<Array<{ id: number; num: number }>>) {
    let index = 0;

    for (let i = 0; i < layerList.length; i++) {
      let rowList = layerList[i];
      let horLayer = ToolExt.clone(this.getNode("horLayer"), this);

      this.getNode("itemContent").addChild(horLayer);
      if (i == 0) {
        horLayer.getComponent(Layout).resizeMode = Layout.ResizeMode.CONTAINER;
      }
      if (i >= 2) {
        this.getNode("bg").getComponent(UITransform).height +=
          horLayer.getComponent(UITransform).height + this.getNode("itemContent").getComponent(Layout).spacingY;

        this.getNode("bg").getComponent(UITransform);
      }

      for (let j = 0; j < rowList.length; j++) {
        let item = ToolExt.clone(this.getNode("award"), this);

        horLayer.addChild(item);
        item.active = true;
        this.itemAct(item, rowList[j], index);
        index++;
      }
      horLayer.active = true;
    }
  }

  private itemAct(node: Node, info: any, index) {
    FmUtils.setItemNode(node["btn_item"], info.id, info.num);
    node.scale = v3(0, 0, 0);
    tween(node)
      .tag(tweenTagEnum.TopItemAwardPop_Tag)
      .delay(0.08 * index)
      .to(0.16, { scale: v3(1, 1, 1) })
      .start();
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.back();
  }

  public tick(dt: any): void {
    this.getNode("item_scroll").getComponent(UITransform).height =
      this.getNode("bg").getComponent(UITransform).height - 130;
  }
}
