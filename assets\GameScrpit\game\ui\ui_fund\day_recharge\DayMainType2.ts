import {
  _decorator,
  Component,
  Graphics,
  Input,
  instantiate,
  isValid,
  Label,
  Mask,
  Node,
  Prefab,
  ScrollView,
  Sprite,
  UIOpacity,
  UITransform,
} from "cc";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { avId1, DayActivityModule } from "db://assets/GameScrpit/module/day/DayActivityModule";
import MsgEnum from "../../../event/MsgEnum";
import { RedeemMessage, RedeemRequest, RedeemResponse } from "../../../net/protocol/Activity";
import { Sleep } from "../../../GameDefine";
import { DayMain } from "./DayMain";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import ToolExt from "../../../common/ToolExt";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { ConfirmMsg } from "../../UICostConfirm";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { PublicRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { RedeemPackVO } from "db://assets/GameScrpit/module/activity/ActivityConfig";
const { ccclass, property } = _decorator;
const db_info: string = "db_info";
@ccclass("DayMainType2")
export class DayMainType2 extends DayMain {
  @property(Node)
  private award_content2: Node = null;

  @property(Node)
  private pengz: Node = null;

  private _node_day_item_type2: Prefab = null;
  private _ad_bar_prefab: Prefab = null;

  private _redeemMessage: RedeemMessage = null;
  private _activityDb: Array<RedeemPackVO> = null;

  private _tickIndex: number = 0;

  protected onLoad() {
    super.onLoad();
    this._redeemMessage = DayActivityModule.data.dayMessage;
  }

  async start() {
    let db = await DayActivityModule.data.getDayVO();
    this._activityDb = db[0];

    this._node_day_item_type2 = await this._assetMgr.loadPrefabSync(
      BundleEnum.BUNDLE_HD_FUND,
      "prefab/day_recharge/node_day_item_type2"
    );
    this._ad_bar_prefab = await this._assetMgr.loadPrefabSync(BundleEnum.BUNDLE_HD_FUND, "prefab/day_recharge/ad_bar");
    if (this.isValid == false) {
      return;
    }
    this.initMain();
  }

  private initMain() {
    this.load_day_item_type2();
  }

  private async load_day_item_type2() {
    this.sortDb();

    for (let i = 0; i < this._activityDb.length; i++) {
      await Sleep(0.01);
      if (isValid(this.node) == false) return;
      let node = instantiate(this._node_day_item_type2);
      node.walk((child) => (child.layer = this.node.layer));
      this.award_content2.addChild(node);

      let btn_ad: Node = node.getChildByPath("btn_ad");
      btn_ad.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.on_click_btn_ad.bind(this)), this);

      let btn_item6_buy: Node = node.getChildByPath("btn_item6_buy");
      btn_item6_buy.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.on_click_btn_item6_buy.bind(this)), this);

      let info = this._activityDb[i];

      this.set_day_item_type2(node, info);
      this.load_item_type2(node, info.rewardList);

      node[db_info] = info;
      node.getChildByName("btn_ad")[db_info] = info;
      node.getChildByName("btn_item6_buy")[db_info] = info;
    }
  }

  private sortDb() {
    let list1 = [];
    let list2 = [];
    for (let i = 0; i < this._activityDb.length; i++) {
      let info = this._activityDb[i];
      let redem_num = this._redeemMessage.redeemMap[info.id] || 0;
      let show_bool = redem_num >= info.max ? true : false; //true 是代表已经售空
      if (show_bool == true) {
        list2.push(info);
      } else {
        list1.push(info);
      }
    }
    this._activityDb = list1.concat(list2);
  }

  private set_day_item_type2(day_item: Node, info: RedeemPackVO) {
    let lbl_day_item_name: Node = day_item.getChildByName("lbl_day_item_name");
    let lbl_ad_hit_count: Node = day_item.getChildByPath("lbl_ad_hit_count");
    let btn_item6_buy: Node = day_item.getChildByPath("btn_item6_buy");
    let lbl_item6_num: Node = day_item.getChildByPath("btn_item6_buy/lbl_item6_num");
    let btn_ad: Node = day_item.getChildByPath("btn_ad");
    let lay_ad_count: Node = btn_ad.getChildByName("lay_ad_count");
    let lbl_buy_max: Node = day_item.getChildByPath("lbl_buy_max");
    let lab_ad_free: Node = btn_ad.getChildByName("lab_ad_free");
    let yishouqin: Node = day_item.getChildByName("yishouqin");

    lbl_day_item_name.getComponent(Label).string = info.name;

    let redeem = this._redeemMessage.redeemMap[info.id] || 0;
    if (redeem >= info.max) {
      yishouqin.active = true;
      lbl_ad_hit_count.active = false;
      btn_item6_buy.active = false;
      lbl_buy_max.active = false;
      btn_ad.active = false;
      return;
    }

    let count = info.max - redeem;
    lbl_buy_max.getComponent(Label).string = ToolExt.getMaxtypeLab(info.maxtype) + `(${count}/${info.max})`;
    yishouqin.active = false;

    if (info.adNum == 0) {
      btn_ad.active = false;
      lbl_ad_hit_count.active = false;
      btn_item6_buy.active = true;
      lbl_item6_num.getComponent(Label).string = "x" + info.cost[1];
    } else if (info.adNum == 1) {
      btn_ad.active = true;
      lbl_ad_hit_count.active = false;
      lab_ad_free.active = true;
      lay_ad_count.active = false;
      btn_item6_buy.active = false;
    } else {
      btn_ad.active = true;
      lbl_ad_hit_count.active = true;
      lbl_ad_hit_count.getComponent(Label).string = "观看" + info.adNum + "次视频";
      btn_item6_buy.active = false;
      lab_ad_free.active = false;
      lay_ad_count.active = true;
      let ad_num = this._redeemMessage.adMap[info.id] || 0;
      let childrenNum = lay_ad_count.children.length;
      for (let i = 0; i < info.adNum && childrenNum < info.adNum; i++) {
        let ad_bar_clone = instantiate(this._ad_bar_prefab);
        ad_bar_clone.walk((child) => (child.layer = this.node.layer));
        lay_ad_count.addChild(ad_bar_clone);
        ad_bar_clone.active = true;
      }

      lay_ad_count.children.forEach((val) => {
        val.getChildByName("has").active = false;
      });

      for (let i = 0; i < ad_num; i++) {
        lay_ad_count.children[i].getChildByName("has").active = true;
      }
    }
  }

  private async load_item_type2(day_item: Node, list: number[]) {
    let rewardList = ToolExt.traAwardItemMapList(list);
    let item_content: Node = day_item.getChildByPath("ScrollView/view/item_content");
    let path = `${BundleEnum.BUNDLE_COMMON_UI}?prefabs/Item`;
    for (let i = 0; i < rewardList.length; i++) {
      let prefab = await this._assetMgr.loadPrefabSync(BundleEnum.BUNDLE_COMMON_UI, "prefabs/Item");
      if (this.isValid == false) {
        return;
      }
      let node = instantiate(prefab); //await this._plantUI.getNode(path, this.node.layer);
      node.getChildByName("Mask").getComponent(Mask).enabled = false;
      node.getChildByName("Mask").getComponent(Graphics).enabled = false;
      item_content.addChild(node);
      FmUtils.setItemNode(node, rewardList[i].id, rewardList[i].num);
    }

    if (rewardList.length <= 3) {
      day_item.getChildByName("ScrollView").getComponent(ScrollView).enabled = false;
    }
  }

  private on_click_btn_item6_buy(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let node = event.target;
    let info: RedeemPackVO = node[db_info];
    let msg: ConfirmMsg = {
      msg: "是否花费" + info.cost[1] + JsonMgr.instance.getConfigItem(info.cost[0]).name + "购买该商品？",
      itemList: [],
      stopHintOption: false,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        let param: RedeemRequest = {
          activityId: avId1,
          redeemId: info.id,
          count: 1,
        };

        DayActivityModule.api.buyFixedPack(param, (res: RedeemResponse) => {
          let rewardList = res.rewardList;
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
          this.upRedeeMap();
        });
      }
    });
  }

  private on_click_btn_ad(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let node = event.target;
    let info: RedeemPackVO = node[db_info];

    let param: RedeemRequest = {
      activityId: avId1,
      redeemId: info.id,
      count: 1,
    };

    DayActivityModule.api.watchAdFixedPack(param, (res: RedeemResponse) => {
      let rewardList = res.rewardList;
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
      this.upRedeeMap();
    });
  }

  private upRedeeMap() {
    this._redeemMessage = DayActivityModule.data.dayMessage;
    this.award_content2.children.forEach((val) => {
      let info = val[db_info];
      let redem_num = this._redeemMessage.redeemMap[info.id] || 0;
      let show_bool = redem_num >= info.max ? true : false; //true 是代表已经售空
      if (show_bool == true) {
        val.setSiblingIndex(this.award_content2.children.length);
      }
      this.set_day_item_type2(val, info);
    });
  }

  update(deltaTime: number) {
    let pengz = this.pengz;
    let content_list = this.award_content2;
    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();
    const index = this._tickIndex;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();
      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });

        if (content_list.children[i].getChildByName("ScrollView").getComponent(ScrollView).enabled == true) {
          let view = content_list.children[i].getChildByPath("ScrollView/view");
          view.getComponent(Mask).enabled = true;
          view.getComponent(Graphics).enabled = true;
        }
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
        let view = content_list.children[i].getChildByPath("ScrollView/view");
        view.getComponent(Mask).enabled = false;
        view.getComponent(Graphics).enabled = false;
      }
      this._tickIndex = i;
    }
    if (this._tickIndex >= content_list.children.length - 1) {
      this._tickIndex = 0;
    }
  }

  onRemove() {
    super.onRemove();
  }
}
