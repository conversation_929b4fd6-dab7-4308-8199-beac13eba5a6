import { _decorator, Node, ProgressBar, sp } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import MsgMgr from "../lib/event/MsgMgr";
import MsgEnum from "../game/event/MsgEnum";
const { ccclass, property } = _decorator;

@ccclass("TopEventFixNvWa")
export class TopEventFixNvWa extends BaseCtrl {
  @property(sp.Skeleton)
  spineWuCaiShiHc: sp.Skeleton;

  @property(ProgressBar)
  progressBar: ProgressBar;

  @property(Node)
  btnNext: Node;

  idx: number = 0;

  // 点击时间
  touchTime: number = -1;

  // 是否长按
  isTouch: boolean = false;

  start() {
    super.start();
    this.spineWuCaiShiHc.setAnimation(0, "state0", true);
    this.idx = 0;
    this.progressBar.progress = 0;

    // 长按事件
    this.btnNext.on(Node.EventType.TOUCH_START, this.onBtnNextStart, this);
    this.btnNext.on(Node.EventType.TOUCH_END, this.onBtnNextEnd, this);
    this.btnNext.on(Node.EventType.TOUCH_CANCEL, this.onBtnNextCancel, this);

    this.touchTime = 0.01;
  }

  onBtnNextStart() {
    if (this.touchTime <= 0) {
      this.touchTime = 0.01;
    }
    this.isTouch = true;
  }

  onBtnNextEnd() {
    this.isTouch = false;
  }

  onBtnNextCancel() {
    this.isTouch = false;
  }

  playFix() {
    this.getNode("spine_xinshouxindao").active = false;
    this.progressBar.node.active = false;
    this.spineWuCaiShiHc.setAnimation(0, "state1", false);
    this.spineWuCaiShiHc.setEventListener((animation, event) => {
      if (event["data"].name === "apear") {
        this.spineWuCaiShiHc.setEventListener(null);

        MsgMgr.emit(MsgEnum.ON_GAME_MAP_SHOW100);

        this.getNode("bg_mask").active = false;
      }
    });

    this.spineWuCaiShiHc.setCompleteListener(() => {
      this.spineWuCaiShiHc.setCompleteListener(null);
      this.closeBack();
    });
  }

  update(deltaTime: number) {
    let max = Number(1);

    if (this.touchTime > max) {
      return;
    } else if (this.touchTime > 0) {
      // 更新时间
      if (this.isTouch) {
        this.touchTime += deltaTime;
        if (this.touchTime >= max) {
          this.playFix();
        }
      } else {
        this.touchTime -= deltaTime / 2;
      }

      // 更新进度条
      if (this.touchTime > max) {
        this.progressBar.progress = 1;
      } else if (this.touchTime < 0) {
        this.progressBar.progress = 0;
      } else {
        this.progressBar.progress = this.touchTime / max;
      }
    }
  }
}
