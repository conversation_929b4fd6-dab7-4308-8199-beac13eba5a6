import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { ActivityModule } from "../activity/ActivityModule";
import { DisciplinesApi } from "./DisciplinesApi";
import { DisciplinesConfig } from "./DisciplinesConfig";
import { DisciplinesData } from "./DisciplinesData";
import { DisciplinesRoute } from "./DisciplinesRoute";
import { DisciplinesService } from "./DisciplinesService";
import { DisciplinesSubscriber } from "./DisciplinesSubscriber";
import { DisciplinesViewModel } from "./DisciplinesViewModel";

export class DisciplinesModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): DisciplinesModule {
    if (!GameData.instance.DisciplinesModule) {
      GameData.instance.DisciplinesModule = new DisciplinesModule();
    }
    return GameData.instance.DisciplinesModule;
  }
  private _data = new DisciplinesData();
  private _api = new DisciplinesApi();
  private _config = new DisciplinesConfig();
  private _service = new DisciplinesService();
  private _route = new DisciplinesRoute();
  private _viewModel = new DisciplinesViewModel();
  private _subscriber = new DisciplinesSubscriber();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public static get route() {
    return this.instance._route;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new DisciplinesData();
    this._api = new DisciplinesApi();
    this._config = new DisciplinesConfig();
    this._service = new DisciplinesService();
    this._route = new DisciplinesRoute();
    this._viewModel = new DisciplinesViewModel();
    this._subscriber = new DisciplinesSubscriber();

    // 模块数据初始化
    this._subscriber.register();
    this._route.init();
    this._service.init();
    // if (ActivityModule.service.checkActivityUnlock(11001)) {
    // }
    DisciplinesModule.api.leaderFundInfo(11001);
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
