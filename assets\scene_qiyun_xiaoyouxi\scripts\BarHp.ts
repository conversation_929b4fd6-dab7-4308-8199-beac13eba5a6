import { _decorator, Component, Node, ProgressBar } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

@ccclass("BarHp")
export class BarHp extends BaseCtrl {
  bar: ProgressBar = null;

  private _hpMax: number = 100;
  private _hp: number = 100;

  protected onLoad(): void {
    super.onLoad();
    this.bar = this.node.getComponent(ProgressBar);
  }

  setHpMax(hpMax: number) {
    this._hpMax = hpMax;
  }

  setHp(hp: number) {
    hp = Math.min(hp, this._hpMax);
    this._hp = hp;
    this.bar.progress = this._hp / this._hpMax;
  }
}
