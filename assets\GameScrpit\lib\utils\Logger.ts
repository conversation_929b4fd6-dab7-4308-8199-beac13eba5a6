export enum LOG_LEVEL {
  DEBUG,
  INFO,
  WARN,
  ERROR,
  STOP,
}

export default class Logger {
  /**
   * 日志工厂
   *
   * @param name 名称
   * @param level 等级
   * @returns
   */
  public static getLoger(level: LOG_LEVEL) {
    const rs = {
      debug: (message?: any, ...optionalParams: any[]) => {},
      log: (message?: any, ...optionalParams: any[]) => {},
      info: (message?: any, ...optionalParams: any[]) => {},
      warn: (message?: any, ...optionalParams: any[]) => {},
      error: (message?: any, ...optionalParams: any[]) => {},
    };

    if (level <= LOG_LEVEL.DEBUG && level != LOG_LEVEL.STOP) {
      rs.debug = console.debug;
    }

    if (level <= LOG_LEVEL.INFO && level != LOG_LEVEL.STOP) {
      rs.log = console.info;
    }

    if (level <= LOG_LEVEL.INFO && level != LOG_LEVEL.STOP) {
      rs.info = console.info;
    }

    if (level <= LOG_LEVEL.WARN && level != LOG_LEVEL.STOP) {
      rs.warn = console.warn;
    }

    if (level <= LOG_LEVEL.ERROR && level != LOG_LEVEL.STOP) {
      rs.error = console.error;
    }

    return rs;
  }
}
