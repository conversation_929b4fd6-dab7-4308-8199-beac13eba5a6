[{"__type__": "cc.Prefab", "_name": "ui_thief", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "ui_thief", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 34}, {"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "block_layer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 15}], "_active": true, "_components": [{"__id__": 27}, {"__id__": 29}, {"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_dianji_nvwa", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_active": true, "_components": [{"__id__": 10}, {"__id__": 12}], "_prefab": {"__id__": 14}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -331.58, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spine", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 6}, "_contentSize": {"__type__": "cc.Size", "width": 158, "height": 156}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c96x2U/EJOv5/WJ45yteVC"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 8}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "a13175b2-dcf6-4811-85b2-a54f95b85125", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "quan_ani", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aM+B273BAqKTaeQmEpn4B"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cfUQKd8IpJRJRpywszalAS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 11}, "_contentSize": {"__type__": "cc.Size", "width": 118, "height": 118}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5GWqB3gpLi4MLm6OzJGQq"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 13}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.1, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9vMOz/JpFv45XEfOXZGG/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96xSopW1BGh4ZUCmdPNZiv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 16}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "1667122e-f34c-4585-bfe8-9cbc8be12e6d", "__expectedType__": "cc.Prefab"}, "fileId": "ff/4ZLPudBML7gJNH0kc+p", "instance": {"__id__": 17}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "7c8v3wuZxD6r3LzohNZ2sc", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 18}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_name"], "value": "hint"}, {"__type__": "cc.TargetInfo", "localID": ["ff/4ZLPudBML7gJNH0kc+p"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -431.369, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 26}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 140, "g": 71, "b": 4, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["0bGc/xDc1BNrAZDBL3Bra/"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 28}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3Z63/ohVPR4HanByU2ZvJ"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 30}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0t2DvduBDLKQeYRtMPwKV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 32}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1500, "_originalHeight": 2000, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dLhaizTpGrqzsXECYK5N6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7d0HkxYq9G8YXXIeOQY4qX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 35}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fgH73sVZCuJmjUSaCWiZJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 37}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51pxXibuNDG5OBxst/Vh8Y"}, {"__type__": "6c6f1+spftMv7b9FYaBKEf6", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 39}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38VcUgEaxH3a3XD641U2Cu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 15}]}]