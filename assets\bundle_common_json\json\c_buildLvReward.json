{"1": {"id": 1, "buildId": 10, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "2": {"id": 2, "buildId": 15, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "3": {"id": 3, "buildId": 20, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "4": {"id": 4, "buildId": 25, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "5": {"id": 5, "buildId": 30, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "6": {"id": 6, "buildId": 35, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "7": {"id": 7, "buildId": 40, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "8": {"id": 8, "buildId": 45, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "9": {"id": 9, "buildId": 50, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "10": {"id": 10, "buildId": 55, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "11": {"id": 11, "buildId": 60, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "12": {"id": 12, "buildId": 65, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "13": {"id": 13, "buildId": 70, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "14": {"id": 14, "buildId": 75, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "15": {"id": 15, "buildId": 80, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "16": {"id": 16, "buildId": 85, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "17": {"id": 17, "buildId": 90, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "18": {"id": 18, "buildId": 95, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "19": {"id": 19, "buildId": 100, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "20": {"id": 20, "buildId": 105, "rewardList": [[1035, 1], [1001, 2], [1017, 1], [1034, 1]]}, "21": {"id": 21, "buildId": 110, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "22": {"id": 22, "buildId": 115, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "23": {"id": 23, "buildId": 120, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "24": {"id": 24, "buildId": 125, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "25": {"id": 25, "buildId": 130, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "26": {"id": 26, "buildId": 135, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "27": {"id": 27, "buildId": 140, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "28": {"id": 28, "buildId": 145, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "29": {"id": 29, "buildId": 150, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "30": {"id": 30, "buildId": 155, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "31": {"id": 31, "buildId": 160, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "32": {"id": 32, "buildId": 165, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "33": {"id": 33, "buildId": 170, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "34": {"id": 34, "buildId": 175, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "35": {"id": 35, "buildId": 180, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "36": {"id": 36, "buildId": 185, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "37": {"id": 37, "buildId": 190, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "38": {"id": 38, "buildId": 195, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "39": {"id": 39, "buildId": 200, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "40": {"id": 40, "buildId": 205, "rewardList": [[1035, 1], [1001, 2], [1017, 2], [1034, 2]]}, "41": {"id": 41, "buildId": 210, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "42": {"id": 42, "buildId": 215, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "43": {"id": 43, "buildId": 220, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "44": {"id": 44, "buildId": 225, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "45": {"id": 45, "buildId": 230, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "46": {"id": 46, "buildId": 235, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "47": {"id": 47, "buildId": 240, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "48": {"id": 48, "buildId": 245, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "49": {"id": 49, "buildId": 250, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "50": {"id": 50, "buildId": 255, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "51": {"id": 51, "buildId": 260, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "52": {"id": 52, "buildId": 265, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "53": {"id": 53, "buildId": 270, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "54": {"id": 54, "buildId": 275, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "55": {"id": 55, "buildId": 280, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "56": {"id": 56, "buildId": 285, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "57": {"id": 57, "buildId": 290, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "58": {"id": 58, "buildId": 295, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "59": {"id": 59, "buildId": 300, "rewardList": [[1035, 1], [1001, 2], [1017, 3], [1034, 3]]}, "-1": {"id": -1, "buildId": 0, "rewardList": [], "des": "建筑总等级达到%s级"}}