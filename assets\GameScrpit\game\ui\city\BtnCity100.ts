import { _decorator, Button, EventTouch, instantiate, Node, sp, v3 } from "cc";
import { CityRouteName } from "../../../module/city/CityConstant";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { CityModule } from "../../../module/city/CityModule";
import Formate from "../../../lib/utils/Formate";
import { AddNumCtrl } from "../ui_gameMap/AddNumCtrl";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PoolMgr } from "db://assets/platform/src/PoolHelper";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { NvWaAudioName } from "../../../module/city/CityConstant";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("BtnCity100")
export class BtnCity100 extends BaseCtrl {
  @property(Node)
  btnCollect: Node;

  @property(Node)
  btnLevelUp: Node;

  @property(sp.Skeleton)
  skt: sp.Skeleton;

  // 女娲点击特效池
  poolSpineNwDianJiTxA2: PoolMgr<Node>;

  // 自动设置层级
  protected autoSetLayer: boolean = false;

  // 点击发光效果
  nodeSpineNwDianJiTxA1: Node;

  // 点击掉落效果，放入池子管理
  nodeSpineNwDianJiTxA2: Node;

  // 持续时间
  private _duration: number = 0;

  protected onLoad(): void {
    super.onLoad();

    this.nodeSpineNwDianJiTxA1 = this.getNode("spine_nw_dianjitx_a1");
    this.nodeSpineNwDianJiTxA2 = this.getNode("spine_nw_dianjitx_a2");

    this.poolSpineNwDianJiTxA2 = new PoolMgr(async () => {
      return instantiate(this.nodeSpineNwDianJiTxA2);
    }, 20);
  }

  start() {
    super.start();

    this.setIdleAni();
    //this.load100();
    // 升级监听-形象变化
    MsgMgr.on(MsgEnum.ON_ENERGYFACTORY_CHANGE_ANI, this.setChangeAni, this);

    BadgeMgr.instance.setBadgeId(this.btnLevelUp, BadgeType.UIMajorCity.btn_level_up.id);
  }

  protected update(dt: number): void {
    this._duration += dt;
    if (this._duration > 1) {
      this._duration = 0;
      let configBuildCrystal = CityModule.data.getConfigBuildCrystal(CityModule.data.energyFactoryMsg.level);
      let rewardNum = configBuildCrystal.time * configBuildCrystal.reward;
      if (rewardNum > 0) {
        AddNumCtrl.showByPos(this.node.getWorldPosition(), `+${Formate.format(rewardNum)}`, 50, true, 1.2);
      }
    }
  }

  protected onDestroy(): void {
    // 升级监听-形象变化
    MsgMgr.off(MsgEnum.ON_ENERGYFACTORY_CHANGE_ANI, this.setChangeAni, this);

    this.poolSpineNwDianJiTxA2.release((obj: Node) => {
      obj.destroy();
    });
  }

  private setIdleAni() {
    if (this.isShow == true) {
      return;
    }
    let level = CityModule.data.energyFactoryMsg.level;
    let key = "a0" + CityModule.service.getNvWaImageKey(level) + "-1";
    this.skt.setAnimation(0, key, true);
  }

  private isShow = false;

  public showAnimation(level: number) {
    this.isShow = true;
    let key = "a0" + CityModule.service.getNvWaImageKey(level) + "-1";
    this.skt.setAnimation(0, key, true);
    this.node.getChildByName("bg_fazheng").active = false;
    this.node.getChildByName("btn_level_up").active = false;
    this.node.getChildByName("particle_nvwa").active = false;
    this.node.getChildByName("btn_collect").setPosition(v3(0, 0, 0));
    this.node.getChildByName("btn_collect").getComponent(Button).enabled = false;
  }

  private setChangeAni(args: any) {
    if (this.isShow == true) {
      return;
    }
    let level = args.level;
    let key = "a0" + CityModule.service.getNvWaImageKey(level);
    this.skt.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
      //清空监听
      if (key == trackEntry.animation.name) {
        args.callback && args.callback();
        this.skt.setAnimation(0, key + "-1", true);
        this.skt.setCompleteListener(null);
      }
    });
    this.skt.setAnimation(0, key, false);
  }

  private async load100() {
    const nodeImage = await CityModule.service.loadNvWaImage();
    this.btnCollect.destroyAllChildren();
    this.btnCollect.addChild(nodeImage);
  }

  onLevelUp() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(CityRouteName.UIEnergyUpgrade);
  }

  private async onCollect(event: EventTouch) {
    AudioMgr.instance.playEffect(NvWaAudioName.Effect.点击女娲);
    // 手动点击次数增加
    CityModule.data.clickHomeCount++;

    // 当前等级对应的配置id
    let config = CityModule.data.getConfigBuildCrystal(CityModule.data.energyFactoryMsg.level);

    // 飘字
    AddNumCtrl.showByPos(this.node.getWorldPosition(), `+${Formate.format(config.reward)}`, 50, true, 1.2);

    // 气运漂出
    MsgMgr.emit(MsgEnum.ON_ENERGYFACTORY_FLY, event.getUILocation());

    this.nodeSpineNwDianJiTxA1.active = true;
    this.nodeSpineNwDianJiTxA1.getComponent(sp.Skeleton).setAnimation(0, "animation1", false);

    let level = CityModule.data.energyFactoryMsg.level;
    let aniPre = "a0" + CityModule.service.getNvWaImageKey(level);
    this.skt.setAnimation(0, aniPre + "-2");
    this.skt.setCompleteListener(() => {
      this.skt.setCompleteListener(null);
      this.skt.setAnimation(0, aniPre + "-1");
    });

    let nodeA2: Node;
    try {
      nodeA2 = await this.poolSpineNwDianJiTxA2.getOne();
    } catch (e) {
      log.warn(e);
      return;
    }
    nodeA2.active = true;
    this.skt.node.addChild(nodeA2);

    const spineA2 = nodeA2.getComponent(sp.Skeleton);
    spineA2.setAnimation(0, "animation2", false);
    spineA2.setCompleteListener(() => {
      spineA2.setCompleteListener(null);

      this.poolSpineNwDianJiTxA2.recycle(nodeA2);
      nodeA2.active = false;
    });

    // TipsMgr.topRouteCtrl.show(CityRouteName.TopCityBuild, { cityId: 110 });
    // const pupilInfoMap = PupilModule.data.allPupilMap;
    // const pupilInfo = pupilInfoMap[Object.keys(pupilInfoMap)[10]];
    // TipsMgr.topRouteCtrl.showPrefab(BundleEnum.BUNDLE_EXT_REWARD, "prefab/top/TopPupilMarry", pupilInfo);
  }
}
