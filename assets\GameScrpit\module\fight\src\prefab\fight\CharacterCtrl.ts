import { _decorator, Node, Vec3 } from "cc";
import { JsonMgr } from "db://assets/GameScrpit/game/mgr/JsonMgr";
import { CharacterDTO, FaceToEnum } from "../../FightConstant";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { IConfigFightCharacter, IConfigSpineShow } from "db://assets/GameScrpit/game/JsonDefine";
import { BundleEnum } from "db://assets/platform/src/ResHelper";

const { ccclass, property } = _decorator;

export interface actionAniInfo {}

@ccclass("CharacterCtrl")
export class CharacterCtrl extends BaseCtrl {
  // 角色
  node_character: Node;

  // 角色配置
  cfgFightCharacter: IConfigFightCharacter;

  // spine形象
  cfgSpineShow: IConfigSpineShow;

  // 初始位置
  pos: Vec3;

  // 受击点
  nodeHunt: Node;

  // 打击点1
  nodeAttack1Hit: Node;

  // 打击点2
  nodeAttack2Hit: Node;

  // 打击点3
  nodeAttack3Hit: Node;

  // 战斗移动速度
  runBattleSpeed: number;

  /**
   * 战斗角色所有对象所在结点，不包含UI
   */
  node_show: Node;

  // 初始化位置
  public init(args: { characterDTO: CharacterDTO; pos: Vec3 }) {
    // 主角形象
    this.cfgFightCharacter = JsonMgr.instance.jsonList.c_fightCharacter[args.characterDTO.artId];

    // 形象id
    this.cfgSpineShow = JsonMgr.instance.jsonList.c_spineShow[this.cfgFightCharacter.spineShowId];

    this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_FIGHT, "");

    // 初始位置
    this.pos = args.pos;

    // TODO 宠物 配置 读取宠物配置

    // TODO 根据配置决定放在前后
  }

  start() {
    this.node_character = this.getNode("node_character");

    this.node_show = this.getNode("node_world");
  }

  public setFaceTo(to: FaceToEnum) {
    if (to == FaceToEnum.left) {
      this.node_show.setScale(-1, 1, 1);
    } else {
      this.node_show.setScale(1, 1, 1);
    }
  }

  public getFaceTo(): FaceToEnum {
    return this.node_show.scale.x > 0 ? FaceToEnum.right : FaceToEnum.left;
  }

  // 跑到指定结点
  public runTo(pos: Vec3, speed: number = 0) {
    // 播放跑步动画
    // 按速度移动
  }

  public attack(idx = 0, hitCallBack: Function) {}

  // 使用技能
  public skill(idx = 0) {}

  // 播放受伤
  public hurt() {}

  // 播放死亡
  public die() {}
}
