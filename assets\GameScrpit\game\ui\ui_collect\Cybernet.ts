import { _decorator, Component, Node, Sprite<PERSON><PERSON>e } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CCInteger } from "cc";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { CollectAudioName } from "../../../module/player/PlayerConfig";
const { ccclass, property } = _decorator;

@ccclass("Cybernet")
export class Cybernet extends Component {
  @property(CCInteger)
  shopType: number = 0;

  @property(Node)
  antiqueList: Node[] = [];

  private on_touch_openPortraitGroup() {
    AudioMgr.instance.playEffect(CollectAudioName.Effect.点击羁绊);
    UIMgr.instance.showDialog("UIPortraitGroup", { type: this.shopType });
  }
}
