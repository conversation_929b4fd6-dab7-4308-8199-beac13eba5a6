import { <PERSON><PERSON><PERSON>and<PERSON> } from "../../game/mgr/ApiHandler";
import { HeroSubCmd } from "../../game/net/cmd/CmdData";
import { HeroMessage } from "../../game/net/protocol/Hero";
import { HeroModule } from "./HeroModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HeroSubScriber {
  private onHeroBattleAttrAdd(heroMessage: HeroMessage) {
    // log.log("战力提升++++", heroMessage);
    HeroModule.data.setHeroMessage(heroMessage);
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(HeroMessage, HeroSubCmd.notifyHeroBattleAttr, this.onHeroBattleAttrAdd);
  }

  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(HeroSubCmd.notifyHeroBattleAttr, this.onHeroBattleAttrAdd);
  }
}
