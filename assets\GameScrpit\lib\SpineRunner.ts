import { sp } from "cc";
import { EDITOR } from "cc/env";
// 编辑器模式下运行spine
if (EDITOR) {
  // 重写uupdateAnimation方法 达到在编辑模式下 自动播放动画的功能
  sp.Skeleton.prototype["updateAnimation"] = function (dt) {
    this.markForUpdateRenderData();
    // if (this.paused) {
    //   return;
    // }

    dt *= this._timeScale * sp["timeScale"];

    if (this.isAnimationCached()) {
      if (this._isAniComplete) {
        if (this._animationQueue.length === 0 && !this._headAniInfo) {
          const frameCache = this._animCache;
          if (frameCache && frameCache.isInvalid()) {
            frameCache.updateToFrame(0);
            const frames = frameCache.frames;
            this._curFrame = frames[frames.length - 1];
          }
          return;
        }
        if (!this._headAniInfo) {
          this._headAniInfo = this._animationQueue.shift()!;
        }
        this._accTime += dt;
        if (this._accTime > this._headAniInfo?.delay) {
          const aniInfo = this._headAniInfo;
          this._headAniInfo = null;
          this.setAnimation(0, aniInfo?.animationName, aniInfo?.loop);
        }
        return;
      }
      this._updateCache(dt);
    } else {
      this._instance?.updateAnimation(dt);
    }
  };
}
