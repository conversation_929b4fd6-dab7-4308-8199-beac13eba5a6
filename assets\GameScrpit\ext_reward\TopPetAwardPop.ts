import { _decorator, Node, sp, UIOpacity, UITransform } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { PetModule } from "../module/pet/PetModule";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { instantiate, Sprite, Label } from "cc";
import { AudioMgr, AudioName } from "../../platform/src/AudioHelper";
import { BundleEnum } from "../game/bundleEnum/BundleEnum";
import { tween } from "cc";
import { SpriteFrame } from "cc";
import { IConfigPet } from "../game/JsonDefine";
import ResMgr from "../lib/common/ResMgr";
import { LayerEnum } from "../game/GameDefine";
import { TipsMgr } from "../../platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("TopPetAwardPop")
export class TopPetAwardPop extends BaseCtrl {
  private _petInfo: IConfigPet;

  @property(Node)
  nodePetImg: Node;
  @property(Node)
  bgColor: Node;
  @property(Node)
  lblName: Node;

  @property(Node)
  nodePetGuadian: Node;
  @property(Node)
  nodePetGuadian2: Node;
  @property(Node)
  nodeTitle: Node;
  @property(Node)
  nodeSkillLayout: Node;

  private isAniFinish = false;

  public init(args: any): void {
    super.init(args);
    TipsMgr.setEnableTouch(false, 1);
    this._petInfo = PetModule.config.getHeroPet(args);
  }

  start() {
    TipsMgr.setEnableTouch(false, 1);
    super.start();
    this.refreshUI(this._petInfo);
    AudioMgr.instance.playEffect(AudioName.Effect.获得奖励);

    tween(this.node)
      .delay(0.5)
      .call(() => {
        this.isAniFinish = true;
      })
      .start();

    // 动画效果
    this.nodePetGuadian2.on(Node.EventType.TRANSFORM_CHANGED, this.nodePetGuadian2Transform, this);
    this.node
      .getChildByName("node_dialog")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == "animation") {
          this.nodePetGuadian2.off(Node.EventType.TRANSFORM_CHANGED, this.nodePetGuadian2Transform, this);
          tween(this.node.getChildByName("btn_close_huang").getComponent(UIOpacity)).to(0.5, { opacity: 255 }).start();
          this.node.getChildByName("node_dialog").getComponent(sp.Skeleton).setAnimation(0, "animation2", true);
        }
      });
    this.nodeTitle.on(Node.EventType.TRANSFORM_CHANGED, () => {
      let opacity = ((0.69 - (this.nodeTitle.scale.x - 1)) / 0.69) * 255;
      this.nodeTitle.getComponent(UIOpacity).opacity = opacity;
    });
    this.node
      .getChildByName("node_dialog")
      .getComponent(sp.Skeleton)
      .setEventListener((animation, event) => {
        if (event["data"].name == "pinzhichuxian") {
          tween(this.bgColor.getComponent(UIOpacity)).to(0.5, { opacity: 255 }).start();
        }
        if (event["data"].name == "jinengchuxian") {
          for (let i = 0; i < this.nodeSkillLayout.children.length; i++) {
            let child = this.nodeSkillLayout.children[i];
            if (child.active) {
              tween(child.getComponent(UIOpacity))
                .delay(0.1 * i)
                .to(0.5, { opacity: 255 })
                .start();
            }
          }
        }
      });
  }

  private nodePetGuadian2Transform() {
    let height = this.nodePetGuadian.position.y - this.nodePetGuadian2.position.y;
    let contentSize = this.nodePetGuadian.getComponent(UITransform).contentSize;
    this.nodePetGuadian.getComponent(UITransform).setContentSize(contentSize.width, height);
  }

  private refreshUI(petInfo: IConfigPet) {
    let petSkin = JsonMgr.instance.jsonList.c_petSkin[petInfo.firstSkin];
    let spinedb = JsonMgr.instance.getConfigSpineShow(Number(petSkin.spineId));

    let assetInfo = spinedb.prefabPath.split("?");
    //预览选中皮肤
    this.assetMgr.loadPrefab(assetInfo[0], assetInfo[1], (prefab) => {
      this.nodePetImg.removeAllChildren();
      let node: Node = instantiate(prefab);

      node.walk((child) => (child.layer = LayerEnum.TOP));

      this.nodePetImg.addChild(node);
      node.setScale(petSkin.showFirst[0] * petSkin.renderScale3[0], petSkin.showFirst[1] * petSkin.renderScale3[1]);
    });

    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_COMMON_UI,
      `atlas_imgs/pingzhi_${petInfo.color}`,
      (sp: SpriteFrame) => {
        this.bgColor.getComponent(Sprite).spriteFrame = sp;
      }
    );

    this.lblName.getComponent(Label).string = `${petSkin.name}`;

    // 加载技能信息
    let skillFirst = JsonMgr.instance.jsonList.c_pet[petInfo.id].skillFirst;
    let i = 0;
    for (; i < skillFirst; i++) {
      let petSkillList = PetModule.data.getHeroPetSkillList();
      let petSkill = petSkillList[i % petSkillList.length];
      if (!this.nodeSkillLayout.children[i]) {
        this.nodeSkillLayout.addChild(instantiate(this.nodeSkillLayout.children[0]));
      }
      let skillNode = this.nodeSkillLayout.children[i];
      skillNode.active = true;
      skillNode.getComponent(UIOpacity).opacity = 0;
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_G_PET,
        `atlas_pet_skill/petskill_${petSkill.iconId}`,
        skillNode.getChildByPath("mask/bg_pet_skill").getComponent(Sprite)
      );
    }
    for (; i < this.nodeSkillLayout.children.length; i++) {
      this.nodeSkillLayout.children[i].active = false;
    }
  }

  onBtnClose() {
    if (!this.isAniFinish) {
      return;
    }

    this.closeBack();
  }
}
