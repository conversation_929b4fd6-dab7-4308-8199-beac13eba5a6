import { _decorator, sp, Tween, tween, UIOpacity } from "cc";
import { CharacterBase, AttrRole, RoleSideEnum } from "./CharacterBase";
import MsgMgr from "../../GameScrpit/lib/event/MsgMgr";
import { QiyunXiaoyouxiEvent } from "../QiyunXiaoyouxiEvent";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
const { ccclass, property } = _decorator;

// 角色控制器
// 控制角色的移动、动画、状态切换等

const log = Logger.getLoger(LOG_LEVEL.STOP);

@ccclass("CharacterPlayer")
export class CharacterPlayer extends CharacterBase {
  // 名字
  public roleName: string = "player";

  // 立场冲击波
  spineChongjibo: sp.Skeleton;
  // 攻击爆炸
  spineChongjibo_shouji: sp.Skeleton;
  // 跑动特效
  spineRunEffect: sp.Skeleton;
  // 等级提升动画
  spineUpgrade: sp.Skeleton;

  protected _spineAniDefault: string = "boss_idle";
  protected _spineAniIdle: string = "boss_idle";
  protected _spineAniWalk: string = "run1";
  protected _spineAniRun: string = "run2";
  protected _spineAniAttack: string = null;

  protected _attr: AttrRole = new AttrRole({
    walkSpeed: 400,
    runSpeed: 800,
    acceleration: 1600,
    hpMax: 100,
    attack: 1,
    def: 1,
  });

  start() {
    this.spineChongjibo = this.getNode("spineChongjibo").getComponent(sp.Skeleton);
    this.spineChongjibo_shouji = this.getNode("spineChongjibo_shouji").getComponent(sp.Skeleton);
    this.spineRunEffect = this.getNode("spineRunEffect").getComponent(sp.Skeleton);
    this.spineUpgrade = this.getNode("spine_juselvup_animation").getComponent(sp.Skeleton);

    super.start();
  }

  protected onEnable(): void {
    MsgMgr.on(QiyunXiaoyouxiEvent.QIYUN_CONSOME, this.onSubQiYun, this);
    MsgMgr.on(QiyunXiaoyouxiEvent.QIYUN_ADD, this.onAddQiYun, this);
  }

  protected onDisable(): void {
    MsgMgr.off(QiyunXiaoyouxiEvent.QIYUN_CONSOME, this.onSubQiYun, this);
    MsgMgr.off(QiyunXiaoyouxiEvent.QIYUN_ADD, this.onAddQiYun, this);
  }

  public idle(side?: RoleSideEnum) {
    super.idle(side);

    this.spineChongjibo.node.active = false;
    this.spineChongjibo_shouji.node.active = false;
    this.spineRunEffect.node.active = false;
    this.spineUpgrade.node.active = false;
  }

  /**
   * 切换状态时改变角色走向，控制相关的显示与动画切换
   * @param side 角色方向
   */
  public walk(side?: RoleSideEnum) {
    super.walk(side);

    this.spineChongjibo.node.active = false;
    this.spineChongjibo_shouji.node.active = false;
    this.spineRunEffect.node.active = false;
  }

  /**
   * 切换状态时改变角色走向，控制相关的显示与动画切换
   * @param side 角色方向
   */
  public run(side?: RoleSideEnum) {
    super.run(side);

    if (!this.spineChongjibo.node.active) {
      this.spineChongjibo.node.getComponent(UIOpacity).opacity = 64;
      this.spineChongjibo.node.active = true;
      this.spineChongjibo_shouji.node.active = true;
      this.spineRunEffect.node.active = true;
    }
  }

  /**
   * 攻击
   */
  public attack() {
    super.attack();

    // 冲击动画
    const uiOpacity = this.spineChongjibo.node.getComponent(UIOpacity);
    Tween.stopAllByTarget(uiOpacity);
    uiOpacity.opacity = 255;
    tween(uiOpacity).to(0.3, { opacity: 64 }).start();

    // 爆炸动画
    this.spineChongjibo_shouji.setAnimation(0, "animation", false);
  }

  /**
   * 升级,换装
   */
  public async setLevelSpine(sex: number, level: number, cbEnd?: Function) {
    let spineData = null;
    if (sex == 2) {
      spineData = await this.assetMgr.loadSpineSync("resources", `spine/role/nvzhujue${level}`);
    } else {
      spineData = await this.assetMgr.loadSpineSync("resources", `spine/role/zhujue${level}`);
    }

    this.spineUpgrade.node.active = true;
    this.spineUpgrade.setAnimation(0, "animation", false);
    this.spineUpgrade.setCompleteListener(() => {
      this.spineUpgrade.setCompleteListener(null);
      cbEnd && cbEnd();
    });

    setTimeout(() => {
      // 切换spine
      this.setSpine(spineData);
    }, 200);
  }

  private onSubQiYun(subValue: number, max: number) {
    log.info(`characterPlayer onSubQiYun subValue: ${subValue} max: ${max}`);
    this.attrCurrent.hpMax = max;
    this.setHpMax(max);

    this.attrCurrent.hp -= subValue;
    this.setHp(this.attrCurrent.hp);
  }

  private onAddQiYun(addValue: number, max: number) {
    log.info(`characterPlayer onAddQiYun addValue: ${addValue} max: ${max}`);
    this.attrCurrent.hpMax = max;
    this.setHpMax(max);

    this.attrCurrent.hp += addValue;
    this.setHp(this.attrCurrent.hp);
  }
}
