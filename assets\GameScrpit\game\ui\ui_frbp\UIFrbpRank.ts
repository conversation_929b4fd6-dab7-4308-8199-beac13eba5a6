import { _decorator, Component, Label, Node, RichText } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ListView } from "../../common/ListView";
import { FrbpRankAdapter } from "./adapter_frbp_rank/FrbpRankAdapter";
import { FrbpModule } from "../../../module/frbp/FrbpModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ToolExt from "../../common/ToolExt";
import Formate from "../../../lib/utils/Formate";
const { ccclass, property } = _decorator;

@ccclass("UIFrbpRank")
export class UIFrbpRank extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FRBP}?prefab/ui/UIFrbpRank`;
  }

  protected onEvtShow(): void {
    let rankList = FrbpModule.data.SimpleRankMessage.rankList;
    let list = [];

    for (let i = 3; i < rankList.length; i++) {
      list.push(rankList[i]);
    }

    let adapter = new FrbpRankAdapter(this.getNode("item"));
    this.getNode("list_view_level").getComponent(ListView).setAdapter(adapter);
    adapter.setData(list);

    this.setMyInfo();

    this.setRankList(0);
    this.setRankList(1);
    this.setRankList(2);
  }
  private setMyInfo() {
    let rank = FrbpModule.data.SimpleRankMessage.rank;
    let diff = FrbpModule.data.SimpleRankMessage.point;

    this.getNode("lbl_rank_my").getComponent(Label).string = "我的排名：" + (rank <= -1 ? "未上榜" : rank);
    this.getNode("lbl_diff_my").getComponent(Label).string = Formate.format(diff);
  }

  private setRankList(index: number) {
    if (index >= FrbpModule.data.SimpleRankMessage.rankList.length) {
      return;
    }

    let rankInfo = FrbpModule.data.SimpleRankMessage.rankList[index];
    let node = this.getNode("role_point_" + index);
    node.getChildByPath("ming_ci/Label").getComponent(Label).string = rankInfo.detailMessage.simpleMessage.nickname;

    let avatarList = rankInfo.detailMessage.simpleMessage.avatarList;
    if (avatarList[3] != -1) {
      node.getChildByName("lv_root").active = false;
      node.getChildByName("title_root").active = true;
      PlayerModule.service.createTitle(node.getChildByName("title_root"), avatarList[3], (node: Node, db) => {});
    } else {
      node.getChildByName("lv_root").active = true;
      node.getChildByName("title_root").active = false;
      let level = rankInfo.detailMessage.simpleMessage.level;
      let info = PlayerModule.data.getConfigLeaderData(level);
      node.getChildByPath("lv_root/lab_lv_jingjie").getComponent(RichText).string = info.jingjie2;
    }

    ToolExt.loadUIRole(
      node.getChildByName("role_root"),
      avatarList[0] == -1 ? 1701 : avatarList[0],
      rankInfo.detailMessage.horseMessage.horseId,
      "renderScale22",
      this
    );
  }

  on_click_btn_close_huang_2() {
    UIMgr.instance.back();
  }
}
