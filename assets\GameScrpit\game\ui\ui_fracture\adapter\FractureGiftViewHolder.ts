import { _decorator, instantiate, Label, Node } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ActivityRedeemItem } from "db://assets/GameScrpit/module/fracture/FractureConstant";
import { ItemCtrl } from "../../../common/ItemCtrl";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { MessageComponent } from "../../../../../platform/src/core/ui/components/MessageComponent";
import { ItemCost } from "../../../common/ItemCost";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { GoodsRouteName } from "db://assets/GameScrpit/module/goods/GoodsRoute";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import GameHttpApi from "../../../httpNet/GameHttpApi";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { FractureModule } from "db://assets/GameScrpit/module/fracture/FractureModule";
import { PublicRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { ConfirmMsg } from "../../UICostConfirm";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);

@ccclass("FractureGiftViewHolder")
export class FractureGiftViewHolder extends ViewHolder {
  @property(Node)
  private lblTitle: Node = null;
  @property(Node)
  private itemLayout: Node = null;
  @property(Node)
  private nodeBuy: Node = null;
  @property(Node)
  private lblLimit: Node = null;
  @property(Node)
  private lblPrice: Node = null;
  @property(Node)
  private lblFree: Node = null;
  @property(Node)
  private itemBuyCost: Node = null;
  @property(Node)
  private nodeSellOut: Node = null;

  private _data: ActivityRedeemItem = null;
  start() {}

  update(deltaTime: number) {}

  updateData(data: ActivityRedeemItem) {
    //
    this._data = data;
    this.lblTitle.getComponent(Label).string = data.name;
    let i = 0;
    let childrenIndex = 0;
    for (; i < data.rewardList.length; i += 2) {
      let child = this.itemLayout.children[childrenIndex++];
      if (!child) {
        child = instantiate(this.itemLayout.children[0]);
        child.parent = this.itemLayout;
      }
      child.active = true;
      child.getComponent(ItemCtrl).setItemId(data.rewardList[i], data.rewardList[i + 1]);
    }
    for (; childrenIndex < this.itemLayout.children.length; childrenIndex++) {
      this.itemLayout.children[childrenIndex].active = false;
    }
    if (data.price == 0 && data.cost.length == 0) {
      this.lblPrice.active = false;
      this.lblFree.active = true;
      this.itemBuyCost.active = false;
    } else if (data.price == 0 && data.cost.length > 0) {
      this.lblPrice.active = false;
      this.lblFree.active = false;
      this.itemBuyCost.active = true;
      this.itemBuyCost.getComponent(ItemCost).setItemId(data.cost[0], data.cost[1]);
    } else if (data.price > 0 && data.cost.length == 0) {
      this.lblPrice.active = true;
      this.lblFree.active = false;
      this.itemBuyCost.active = false;
      this.lblPrice.getComponent(MessageComponent).args = [Formate.format(data.price % 10000)];
    }
    let buyNums = FractureModule.data.fractureData.redeemMap[data.id] || 0;
    this.lblLimit.getComponent(MessageComponent).args = [`${data.max - buyNums}`];
    if (data.max - buyNums <= 0) {
      this.nodeSellOut.active = true;
      this.nodeBuy.active = false;
    } else {
      this.nodeBuy.active = true;
      this.nodeSellOut.active = false;
    }
  }

  // {"goodsId":11501103,"goodsType":14,"playerId":10800003,"orderAmount":0,"goodsName":"探险礼包1","platformType":"TEST"}
  // {"goodsId":1020110,"goodsType":4,"playerId":10800003,"orderAmount":30,"goodsName":"月卡","platformType":"TEST"}
  private onClickBuy() {
    AudioMgr.instance.playEffect(1769);
    if (this._data.price == 0) {
      let buyFunc = () => {
        FractureModule.api.buyRedeem(this._data.id, 1, (res: any) => {
          log.log("buyRedeem", res);
          this.updateData(this._data);
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
        });
      };
      if (this._data.cost.length > 0) {
        let attrName = JsonMgr.instance.jsonList.c_item[this._data.cost[0]].name;
        let msg: ConfirmMsg = {
          msg: `您确定花费'${this._data.cost[1]}${attrName}'进行购买`,
          itemList: [this._data.cost[0], this._data.cost[1]],
        };
        UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
          if (resp?.ok) {
            buyFunc();
          }
        });
      } else {
        buyFunc();
      }
    } else {
      GameHttpApi.pay({
        goodsId: this._data.id,
        goodsType: 14,
        playerId: PlayerModule.data.playerId,
        orderAmount: this._data.price % 10000,
        goodsName: this._data.name,
        platformType: "TEST",
      }).then((resp: any) => {
        log.log("pay resp");
        if (resp.code != 200) {
          log.error(resp);
          return;
        }
        UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
      });
    }
  }
}

export class FractureGiftAdapter extends ListAdapter {
  private item: Node;
  private data: any[];
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(FractureGiftViewHolder).updateData(this.data[position]);
  }
  getCount(): number {
    return this.data.length;
  }
}
