const fs = require("fs").promises;
const path = require("path");

/**
 * 删除指定目录下所有的 .json 文件
 * @param {string} directoryPath - 目标目录的路径
 */
async function deleteJsonFiles(directoryPath) {
  try {
    // 检查目录是否存在
    try {
      await fs.access(directoryPath);
    } catch (error) {
      console.error(`错误：目录不存在 -> ${directoryPath}`);
      return;
    }

    // 读取目录内容
    const files = await fs.readdir(directoryPath);
    let deletedCount = 0;

    // 遍历目录下的所有文件
    for (const file of files) {
      // 构造文件的完整路径
      const filePath = path.join(directoryPath, file);

      // 检查是否是文件以及文件扩展名是否为 .json
      const stat = await fs.stat(filePath);
      if (stat.isFile() && path.extname(file).toLowerCase() === ".json") {
        try {
          // 删除文件
          await fs.unlink(filePath);
          // console.log(`已删除文件: ${filePath}`);
          deletedCount++;
        } catch (err) {
          console.error(`删除文件时出错: ${filePath}`, err);
        }
      }
    }

    if (deletedCount === 0) {
      console.log(`在目录 ${directoryPath} 中没有找到要删除的 .json 文件。`);
    } else {
      console.log(`\n总共删除了 ${deletedCount} 个 .json 文件。`);
    }
  } catch (err) {
    console.error(`处理目录时发生错误: ${directoryPath}`, err);
  }
}

// 导出函数，以便在其他模块中使用（可选）
module.exports = deleteJsonFiles;
