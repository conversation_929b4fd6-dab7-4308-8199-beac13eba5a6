import { _decorator, instantiate, Layers, Node, sp, tween, UIOpacity, UITransform, v3, Vec3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import TickerMgr from "../../../lib/ticker/TickerMgr";
const { ccclass, property } = _decorator;

@ccclass("UIfigureTop_son")
export class UIfigureTop_son extends UINode {
  protected _isSwallowTouch: boolean = false; //是否可点击穿透，false为可以
  protected _isSetParent: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_GAME_MAP}?prefab/ui/UIfigureTop_son`;
  }

  private _mapLayer: Node;
  private _city1Ticker: number;
  private _vulture1Ticker: number;
  private _vulture2Ticker: number;
  private _loadCraneTime: number = 0;
  public init(args: any): void {
    super.init(args);
    this._mapLayer = args.mapLayer;
  }

  protected onEvtShow(): void {
    this.initVulture1();
    this.vultureFun1();
    this.initVulture2();
    this.vultureFun2();

    this.initCity2();
    this.city2Fun("animation2");

    this.move();
  }

  private move() {
    for (let i = 1; i <= 3; i++) {
      let callback = () => {
        let endPos = ToolExt.getRandomPoint(
          this.getNode("figureLayer").getComponent(UITransform).getBoundingBox(),
          this.getNode("xian" + i).getComponent(UITransform).contentSize
        );
        let pointA = this.getNode("xian" + i).getPosition();
        let distance = this.calculateDistance(pointA, endPos);
        let time = distance / 200;
        tween(this.getNode("xian" + i))
          .to(time, { position: endPos })
          .call(() => {
            callback();
          })
          .start();
      };

      callback();
    }
  }

  private calculateDistance(pointA, pointB) {
    // 使用欧几里得距离公式
    var dx = pointB.x - pointA.x;
    var dy = pointB.y - pointA.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  private initCity2() {
    this.getNode("city2")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听

        if ("animation2" == trackEntry.animation.name) {
          this.city2Fun("animation3");
        }
        if ("animation3" == trackEntry.animation.name) {
          this.city2Fun("animation4");
        }
        if ("animation4" == trackEntry.animation.name) {
          this.city2Fun("animation5");
        }
        if ("animation5" == trackEntry.animation.name) {
          this.city2Fun("animation2");
        }
      });
  }

  private city2Fun(aniName: string) {
    if (aniName == "animation2") {
      let time = Math.random() * 10;
      this._city1Ticker = TickerMgr.setTimeout(time, () => {
        this.getNode("city2").getComponent(sp.Skeleton).setAnimation(0, aniName, false);
      });
      return;
    }
    this.getNode("city2").getComponent(sp.Skeleton).setAnimation(0, aniName, false);
  }

  private initVulture1() {
    this.getNode("vulture1")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("animation" == trackEntry.animation.name) {
          this.vultureFun1();
        }
      });
  }
  private vultureFun1() {
    let time = Math.random() * 20;
    this._vulture1Ticker = TickerMgr.setTimeout(time, () => {
      this.getNode("vulture1").getComponent(sp.Skeleton).setAnimation(0, "animation", false);
    });
  }

  private initVulture2() {
    this.getNode("vulture2")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("animation" == trackEntry.animation.name) {
          this.vultureFun1();
        }
      });
  }
  private vultureFun2() {
    let time = Math.random() * 20;
    this._vulture2Ticker = TickerMgr.setTimeout(time, () => {
      this.getNode("vulture2").getComponent(sp.Skeleton).setAnimation(0, "animation", false);
    });
  }

  private loadCrane() {
    let pos1 = this.getPos1();
    let pos2 = this.getPos2();

    let scale = v3(0.4, 0.4, 1);
    let endScale = v3(0.1, 0.1, 1);
    let angle = 0;

    let distance = Vec3.distance(pos1, pos2);
    let time = distance / (Math.floor(Math.random() * 150) + 150);

    let arrAct = ["crane1", "crane2"];
    let index = Math.floor(Math.random() * arrAct.length);
    let key = arrAct[index];
    let node = ToolExt.clone(this[key], this);
    node.active = true;

    this.getNode("figureLayer").addChild(node);
    node.setPosition(pos1);

    node.scale = scale;
    node.angle = angle;
    node.getComponent(UIOpacity).opacity = 0;

    tween(node)
      .parallel(
        tween(node).to(time, { position: pos2 }).start(),
        tween(node)
          .delay(time * 0.45)
          .to(time * 0.55, { scale: endScale })
          .start()
      )
      .delay(time)
      .call(() => {
        node.destroy();
      })
      .start();
    tween(node.getComponent(UIOpacity))
      .to(time * 0.15, { opacity: 255 })
      .delay(time * 0.7)
      .to(time * 0.15, { opacity: 0 })
      .start();
  }

  private getPos1() {
    let map = this._mapLayer.getChildByName("map_1");
    let startX = map.getPosition().x - map.getComponent(UITransform).width / 2 - 100;
    let startY1 = map.getPosition().y - map.getComponent(UITransform).height / 2;
    let startY2 = map.getPosition().y;
    let startY3 = Math.random() * this.getRandomNumber(startY1, startY2);
    let pos = v3(startX, startY3, 0);
    return pos;
  }

  private getPos2() {
    let map = this._mapLayer.getChildByName("map_1");
    let startX1 = map.getPosition().x - map.getComponent(UITransform).width / 2;
    let startX2 = map.getPosition().x;
    let startX3 = Math.random() * this.getRandomNumber(startX1, startX2);
    let starttY = map.getPosition().y + map.getComponent(UITransform).height / 2 + 100;
    let pos = v3(startX3, starttY, 0);
    return pos;
  }

  private getRandomNumber(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  public tick(dt: any): void {
    this._loadCraneTime -= dt;
    if (this._loadCraneTime <= 0) {
      this.loadCrane();
      this._loadCraneTime = 15;
    }
  }

  protected onEvtClose(): void {
    if (this._vulture1Ticker) {
      TickerMgr.clearTimeout(this._vulture1Ticker);
    }
    if (this._vulture2Ticker) {
      TickerMgr.clearTimeout(this._vulture2Ticker);
    }
  }
}
