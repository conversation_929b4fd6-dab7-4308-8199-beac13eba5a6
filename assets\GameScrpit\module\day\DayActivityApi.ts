import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { RedeemMessage, RedeemRequest, RedeemResponse } from "../../game/net/protocol/Activity";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import MsgMgr from "../../lib/event/MsgMgr";
import { DayActivityModule } from "./DayActivityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class DayActivityApi {
  // 获取每日礼包的情况
  public fixedPack(avId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: avId,
    };
    ApiHandler.instance.request(RedeemMessage, ActivityCmd.fixedPack, LongValue.encode(data), (res: RedeemMessage) => {
      //log.log("获取固定礼包领取情况==========", res);
      DayActivityModule.data.dayMessage = res;
      success && success(res);
    });
  }

  /**消耗道具兑换或免费领取固定礼包道具 -----  每日礼包，累计回馈都有使用 */
  public buyFixedPack(param: RedeemRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      RedeemResponse,
      ActivityCmd.buyFixedPack,
      RedeemRequest.encode(param),
      (data: RedeemResponse) => {
        DayActivityModule.data.dayMessage.redeemMap = data.redeemMap;
        DayActivityModule.data.dayMessage.adMap = data.adMap;
        DayActivityModule.data.dayMessage.chosenMap = data.chosenMap;
        MsgMgr.emit(MsgEnum.ON_ACTIVITY_DAILY_GIFT_BUT_UP);
        success && success(data);
      }
    );
  }

  /**看广告兑换固定礼包；VIP有跳过广告的权限，也是调用此接口 */
  public watchAdFixedPack(param: any, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      RedeemResponse,
      ActivityCmd.watchAdFixedPack,
      RedeemRequest.encode(param),
      (data: RedeemResponse) => {
        DayActivityModule.data.dayMessage.redeemMap = data.redeemMap;
        DayActivityModule.data.dayMessage.adMap = data.adMap;
        DayActivityModule.data.dayMessage.chosenMap = data.chosenMap;
        MsgMgr.emit(MsgEnum.ON_ACTIVITY_DAILY_GIFT_BUT_UP);
        //log.log("看广告兑换固定礼包；VIP有跳过广告的权限，也是调用此接口=======", data);
        success && success(data);
      }
    );
  }
}
