import MsgEnum from "../../game/event/MsgEnum";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import { MainRankModule } from "./MainRankModule";

/**
 * 模块逻辑处理
 */
export class MainRankService {
  public init() {
    this.updatePopover();
    MsgMgr.off(MsgEnum.ON_MAIN_RANK_UPDATE, this.updatePopover);
    MsgMgr.on(MsgEnum.ON_MAIN_RANK_UPDATE, this.updatePopover, this);
  }
  private updatePopover() {
    let showBadge = !MainRankModule.data.playerRankBoardMessage.takeCurServerRankReward;
    BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_pai_hang.btn_zan.id, showBadge);
  }
}
