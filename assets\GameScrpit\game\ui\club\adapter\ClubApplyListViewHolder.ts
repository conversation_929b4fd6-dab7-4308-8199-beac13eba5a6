import { _decorator, instantiate, Label, Node, RichText } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ClubModule } from "../../../../module/club/ClubModule";
import { ClubApplyMessage, ClubExamineMessage } from "../../../net/protocol/Club";
import TipMgr from "../../../../lib/tips/TipMgr";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
const { ccclass, property } = _decorator;
@ccclass("ClubApplyListViewHolder")
export class ClubApplyListViewHolder extends ViewHolder {
  @property(Label)
  membersName: Label;
  @property(Label)
  membersPower: Label;
  @property(Node)
  richTitleLv: Node;

  @property(Node)
  BtnHeader: Node;

  applyMessage: ClubApplyMessage;
  private _adapter: ClubApplyListAdapter;

  public init(adapter: ClubApplyListAdapter) {
    this._adapter = adapter;
  }
  public updateData(data: ClubApplyMessage) {
    this.applyMessage = data;
    this.membersName.string = `${data.simpleMessage.nickname}`;
    this.membersPower.string = `${Formate.format(data.simpleMessage.power)}`;

    //设置头像
    FmUtils.setHeaderNode(this.BtnHeader, data.simpleMessage);
    // if(ClubModule.data.clubFormMessage.applyMap[data.])
    let configLeader = PlayerModule.data.getConfigLeaderData(data.simpleMessage.level);
    this.getNode("rich_title_lv").getComponent(RichText).string = configLeader.jingjie2;
  }

  private onClickAppoint() {
    ClubModule.api.examineApply(this.applyMessage.simpleMessage.userId, true, (data: ClubExamineMessage) => {
      if (data.code == 1) {
        // TipMgr.showTip("批准成功");
        TipsMgr.showTipX(189);
      } else if (data.code == 2) {
        // TipMgr.showTip("该角色已经加入别的战盟");
        TipsMgr.showTipX(190);
      }

      this._adapter.removeData(this.applyMessage);
    });
  }
  private onClickRefuse() {
    ClubModule.api.examineApply(this.applyMessage.simpleMessage.userId, false, (data: ClubExamineMessage) => {
      if (data.code == 0) {
        // TipMgr.showTip("您已拒绝");
        TipsMgr.showTipX(191);
      } else if (data.code == 2) {
        // TipMgr.showTip("该角色已经加入别的战盟");
        TipsMgr.showTipX(190);
      }

      this._adapter.removeData(this.applyMessage);
    });
  }
}
export class ClubApplyListAdapter extends ListAdapter {
  private item: Node;
  private data: ClubApplyMessage[];
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }
  public removeData(data: ClubApplyMessage) {
    this.data.splice(this.data.indexOf(data), 1);
    this.notifyDataSetChanged();
  }

  getViewType(position: number): number {
    return 0;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(ClubApplyListViewHolder).init(this);
    return item;
  }
  onBindData(view: Node, position: number): void {
    view.getComponent(ClubApplyListViewHolder).updateData(this.data[position]);
  }
  getCount(): number {
    return this.data.length;
  }
}
