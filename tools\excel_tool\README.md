# Excel 转 JSON 工具 - JavaScript 简化版本

这是一个简化的 JavaScript 版本的 Excel 转 JSON 转换工具，无需 TypeScript 编译，可以直接使用。

## 功能特性

- ✅ 支持 `.xlsx` 和 `.xls` 格式
- ✅ 支持字段格式定义：`字段名:类型`
- ✅ 支持多种数据类型：`string`, `number`, `string[]`, `number[]`, `string[][]`, `number[][]`, `json`
- ✅ 自动忽略临时文件（以`~$`开头的文件）
- ✅ 批量转换和单文件转换
- ✅ 详细的转换结果统计
- ✅ 简单易用的 API
- ✅ **自动删除文件名和字段名中的括号及其内容**（支持中英文括号：`()` 和 `（）`）
- ✅ **Excel行数据读取器**（可读取指定行的数据并输出到控制台）

## Excel 文件格式要求

Excel 文件必须按以下格式组织：

```
第1行：描述信息（可选）
第2行：输出条件（1表示输出该列，0或空表示不输出）
第3行：字段定义（格式：字段名:类型）
第4行及以后：数据行
```

### 示例 Excel 格式

| A 列       | B 列      | C 列        | D 列         |
| ---------- | --------- | ----------- | ------------ |
| 角色数据表 |           |             |              |
| 0          | 1         | 1           | 1            |
| desc       | id:number | name:string | value:number |
| 角色 1     | 1001      | 战士        | 100          |
| 角色 2     | 1002      | 法师        | 80           |

## 安装

```bash
npm install xlsx
```

## 快速开始

### 方法 1：快速转换（推荐）

```javascript
const { convertExcelToJson } = require("./excel-converter");

async function convert() {
  try {
    const results = await convertExcelToJson(
      "./input", // 输入目录
      "./output" // 输出目录（可选）
    );
    console.log("转换完成:", results);
  } catch (error) {
    console.error("转换失败:", error.message);
  }
}

convert();
```

### 方法 2：详细控制

```javascript
const { ExcelToJsonConverter } = require("./excel-converter");

async function convert() {
  try {
    // 创建转换器
    const converter = new ExcelToJsonConverter("./input", "./output");

    // 可选配置
    converter.setSupportedExtensions([".xlsx", ".xls"]);
    converter.setOutputDir("./custom-output");

    // 获取统计信息
    const stats = converter.getDirectoryStats();
    console.log("目录统计:", stats);

    // 转换所有文件
    const results = await converter.convertAll();
    console.log("转换结果:", results);
  } catch (error) {
    console.error("转换失败:", error.message);
  }
}

convert();
```

### 方法 3：转换单个文件

```javascript
const { ExcelToJsonConverter } = require("./excel-converter");

const converter = new ExcelToJsonConverter("./input", "./output");
const result = converter.convertSingle("example.xlsx");
console.log("转换结果:", result);
```

### 方法 4：读取Excel文件指定行数据

```javascript
const ExcelRowReader = require("./ExcelRowReader");

async function readRowData() {
  try {
    // 创建行读取器
    const reader = new ExcelRowReader("./input");

    // 读取所有文件的第4行数据
    const results = reader.readAllFilesRowData(3); // 第4行，索引为3

    // 读取单个文件的第4行数据
    reader.readSingleFileRowData("example.xlsx", 3);

    // 读取第1行数据（标题行）
    reader.readSingleFileRowData("example.xlsx", 0);

    // 获取目录统计
    const stats = reader.getDirectoryStats();
    console.log("目录统计:", stats);
  } catch (error) {
    console.error("读取失败:", error.message);
  }
}

readRowData();
```

## 支持的数据类型

| 类型         | 说明           | 示例                                      |
| ------------ | -------------- | ----------------------------------------- |
| `string`     | 字符串         | `"hello"`                                 |
| `number`     | 数字           | `123`                                     |
| `string[]`   | 字符串数组     | `"a,b,c"` → `["a", "b", "c"]`             |
| `number[]`   | 数字数组       | `"1,2,3"` → `[1, 2, 3]`                   |
| `string[][]` | 二维字符串数组 | `"a,b;c,d"` → `[["a", "b"], ["c", "d"]]`  |
| `number[][]` | 二维数字数组   | `"1,2;3,4"` → `[[1, 2], [3, 4]]`          |
| `json`       | JSON 对象      | `'{"key": "value"}'` → `{"key": "value"}` |

## 文件结构

```
excel_tool/
├── excel-converter.js    # 主转换器文件
├── example.js           # 使用示例
├── package-js.json      # 简化的依赖配置
└── README-JS.md         # 说明文档
```

## 运行示例

```bash
# 运行使用示例
node example.js

# 直接运行转换器（查看帮助信息）
node excel-converter.js

# 快速启动转换
node start.js

# 快速开始示例
node quick-start.js

# 读取Excel文件第4行数据
node read-row4.js

# 行读取器详细示例
node row-reader-example.js

# 使用npm脚本
npm run start          # 快速启动转换
npm run read-row4      # 读取第4行数据
npm run row-example    # 行读取器示例
npm test              # 运行转换示例
```

## 转换结果

转换成功后，每个 Excel 文件会生成对应的 JSON 文件，包含所有有效的数据行。

### 示例输出

```json
[
  {
    "id": 1001,
    "name": "战士",
    "value": 100
  },
  {
    "id": 1002,
    "name": "法师",
    "value": 80
  }
]
```

## 错误处理

工具会自动处理以下情况：

- 跳过临时文件（以`~$`开头）
- 跳过格式不正确的文件
- 跳过没有有效数据的行
- 跳过缺少`id`字段的数据行
- 提供详细的错误信息和统计

## 注意事项

1. Excel 文件必须包含`id`字段，且数据行必须有有效的`id`值
2. 字段定义格式必须为`字段名:类型`
3. 输出条件行中，1 表示输出该列，0 或空表示跳过
4. 工具会自动创建输出目录
5. 相同名称的 JSON 文件会被覆盖
6. **文件名和字段名中的括号及其内容会被自动删除**
   - 例如：`c_attribute(加成方式).xlsx` → `c_attribute.json`
   - 例如：`name(名称):string` → `name:string`

## 许可证

MIT License
