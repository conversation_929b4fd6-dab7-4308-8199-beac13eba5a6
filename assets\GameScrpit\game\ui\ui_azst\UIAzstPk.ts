import { _decorator, Color, Label, Node, Sprite } from "cc";
import TipMgr from "../../../lib/tips/TipMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import Formate from "../../../lib/utils/Formate";
import { DialogZero } from "../../GameDefine";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerSimpleMessage } from "../../net/protocol/Player";
import { AzstModule } from "../../../module/azst/AzstModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import { AzstAudioName, AzstMsgEnum } from "../../../module/azst/AzstConfig";
import MsgEnum from "../../event/MsgEnum";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { FightData } from "../../fight/FightDefine";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";

const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const pkNeedItemId: number = 1082;

@ccclass("UIAzstPk")
export class UIAzstPk extends UINode {
  protected _openAct: boolean = true; //打开动作

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_AZST}?prefab/ui/UIAzstPk`;
  }

  protected dependOn(): Array<BundleEnum> {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _winReward: number[][] = null;

  private _costList: number[] = null;

  private _time: number = 0;

  private _info = null;

  private _upTime: number = 0;
  private _maxUpTime: number = 1;

  protected onRegEvent(): void {
    MsgMgr.on(AzstMsgEnum.OPPONENT_REFRESH, this.upBattlerList, this);
    MsgMgr.on(AzstMsgEnum.POINT_CHANGE, this.myPointShow, this);
    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.setXianYuUp, this);
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setPkNeedItemNum, this);
    MsgMgr.on(AzstMsgEnum.FREE_COUNT_UPDATE, this.setFreeUp, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(AzstMsgEnum.OPPONENT_REFRESH, this.upBattlerList, this);
    MsgMgr.off(AzstMsgEnum.POINT_CHANGE, this.myPointShow, this);
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.setXianYuUp, this);
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setPkNeedItemNum, this);
    MsgMgr.off(AzstMsgEnum.FREE_COUNT_UPDATE, this.setFreeUp, this);
  }

  protected onEvtShow(): void {
    let c_compete = JsonMgr.instance.jsonList.c_compete;
    let list = Object.keys(c_compete);
    this._info = c_compete[list[0]];

    this.getWinRewardCostList();
    this.setMyPower();
    this.loadBattlerList();
    this.setPkNeedItemNum();
    this.setFreeUp();
    this.setXianYuUp();
    this.myPointShow();
  }

  private myPointShow() {
    this.getNode("myPoint").getComponent(Label).string = "积分：" + AzstModule.data.point;
  }

  private setFreeUp() {
    if (AzstModule.data.freeFreshCount > 0) {
      this.getNode("btn_upList").getComponent(Sprite).grayscale = false;
      this.getNode("btn_upList").getChildByName("lab").getComponent(Label).color = new Color(159, 69, 41, 255);
      let str = "免费刷新：" + AzstModule.data.freeFreshCount + "/" + this._info.freeMax;
      this["counLab"].getComponent(Label).string = str;
      this.getNode("counLab").active = true;
      this.getNode("LblCd").active = false;
      this.getNode("hit").active = false;
    } else {
      this.getNode("btn_upList").getComponent(Sprite).grayscale = true;
      this.getNode("btn_upList").getChildByName("lab").getComponent(Label).color = new Color(100, 100, 100, 255);
      this.getNode("counLab").active = false;
      this.getNode("LblCd").active = true;
      this.getNode("hit").active = true;
      let num = this._time * 1000 + AzstModule.data.lastUpdateTime;
      FmUtils.setCd(this.getNode("LblCd"), num);

      // let time = TimeUtils.serverTime - AzstModule.data.lastUpdateTime;
      // if (time > this._time * 1000) {
      //   return;
      // }
      // this["counLab"].getComponent(Label).string =
      //   TimeUtils.formatTimeLeft(Math.floor(this._time - time / 1000)) + "后恢复1次";
    }
  }

  private setXianYuUp(id?) {
    if (id && id != 6) {
      return;
    }
    this["needItem"].active = true;
    this.getNode("needItemNum").getComponent(Label).string =
      Formate.format(PlayerModule.data.getItemNum(ItemEnum.仙玉_6)) + "/" + this._costList[1];
  }

  /**获取单次胜利的奖励信息 */
  private getWinRewardCostList() {
    let c_compete = JsonMgr.instance.jsonList.c_compete;
    let list = Object.keys(c_compete);
    let oneIndex = list[0];
    this._winReward = c_compete[oneIndex].winReward;
    this._costList = c_compete[oneIndex].costList;
    this._time = c_compete[oneIndex].time;
    ToolExt.setItemIcon(this["needItem"], this._costList[0], this);
  }

  /**设置自己的战力 */
  private setMyPower() {
    this.getNode("power_lab").getComponent(Label).string = Formate.format(
      PlayerModule.data.playerBattleAttrResponse.power
    );
  }

  /**设置当前的挑战券数量 */
  private setPkNeedItemNum() {
    let c_pupilDrop = JsonMgr.instance.jsonList.c_pupilDrop;
    let list = Object.keys(c_pupilDrop);
    let oneIndex = list[0];
    let info = c_pupilDrop[oneIndex];

    let myItem = PlayerModule.data.getItemNum(pkNeedItemId);
    this["needItemLab"].getComponent(Label).string =
      Formate.format(myItem) + "/" + AzstModule.data.azstMessage.ticketSize;
  }

  /**创建可挑战者列表 */
  private loadBattlerList() {
    let battlerList = AzstModule.data.battlerList;
    for (let i = 0; i < battlerList.length; i++) {
      let node = ToolExt.clone(this.getNode("playerMessage"), this);
      this.getNode("content").addChild(node);
      node.active = true;

      let info: PlayerSimpleMessage = battlerList[i].simpleMessage;
      let point: number = battlerList[i].point;
      this.setBattlerInfo(node, info, point, battlerList[i].isRobot);
      this.setWinReward(node);
    }
  }

  /**设置对手数据 */
  private setBattlerInfo(node: Node, info: PlayerSimpleMessage, point: number, isRobot: boolean) {
    node["userInfo"] = info;
    node["btn_header"]["isRobot"] = isRobot;
    node["playerName"].getComponent(Label).string = info.nickname == "" ? "守卫者" : info.nickname;
    node["playerScore"].getComponent(Label).string = "积分:" + point;
    node["playerPower"].getComponent(Label).string = Formate.format(info.power);

    let data = ToolExt.newPlayerBaseMessage();
    data.userId = info.userId;
    data.nickname = info.nickname;
    data.avatarList = info.avatarList;
    data.sex = info.sex;
    data.level = info.level;
    data.vipLevel = info.vipLevel;

    FmUtils.setHeaderNode(node["btn_header"], data, isRobot);
  }

  /**奖励道具变更，刷新或者创建 */
  private setWinReward(node: Node) {
    if (node["awardLay"].children.length > this._winReward.length) {
      let num = node["awardLay"].children.length - this._winReward.length;
      for (let i = 0; i < num; i++) {
        let item = node["awardLay"].children[i];
        item.removeFromParent();
        item.destroy();
      }
    } else {
      let num = this._winReward.length - node["awardLay"].children.length;
      for (let i = 0; i < num; i++) {
        let item = ToolExt.clone(this["btn_item"], this);
        node["awardLay"].addChild(item);
        item.active = true;
      }
    }

    for (let j = 0; j < this._winReward.length; j++) {
      let itemId = this._winReward[j][0];
      let num = this._winReward[j][1];
      let item = node["awardLay"].children[j];
      FmUtils.setItemNode(item, itemId, num, false, 40, 40);
    }
  }

  /**刷新后处理变更，不重新创建 */
  private upBattlerList() {
    let battlerList = AzstModule.data.battlerList;
    if (this.getNode("content").children.length > battlerList.length) {
      let num = this.getNode("content").children.length - battlerList.length;
      for (let i = 0; i < num; i++) {
        let node = this.getNode("content").children[i];
        node.removeFromParent();
        node.destroy();
      }
    } else {
      let num = battlerList.length - this.getNode("content").children.length;
      for (let i = 0; i < num; i++) {
        let node = ToolExt.clone(this.getNode("playerMessage"), this);
        this.getNode("content").addChild(node);
        node.active = true;
      }
    }

    for (let i = 0; i < battlerList.length; i++) {
      let node = this.getNode("content").children[i];
      let info: PlayerSimpleMessage = battlerList[i].simpleMessage;
      let point: number = battlerList[i].point;
      this.setBattlerInfo(node, info, point, battlerList[i].isRobot);
      this.setWinReward(node);
    }
  }

  private on_click_btn_pkUser(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    TipsMgr.setEnableTouch(false, 3);
    let myItem = PlayerModule.data.getItemNum(pkNeedItemId);
    if (myItem < 1) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: pkNeedItemId,
        needNum: 1,
      });
      return;
    }
    let node: Node = event.node;
    let userInfo = node.parent["userInfo"];
    AzstModule.api.azstFight(userInfo.userId, (res) => {
      let data = JSON.parse(res.replay);
      log.log("战斗录像====", data);

      let args: FightData = {
        fightData: data,
        win: res.win,
        userInfo: userInfo,
      };
      UIMgr.instance.showDialog("UIAzstFight", { data: args }, null, () => {
        TipsMgr.setEnableTouch(true);
      });
    });
  }

  private on_click_btn_upList() {
    AudioMgr.instance.playEffect(AzstAudioName.Effect.点击刷新按钮);
    if (this._upTime > 0) {
      TipMgr.showTip("操作太快");
      return;
    }
    if (AzstModule.data.freeFreshCount <= 0) {
      TipMgr.showTip("次数不足");
      return;
    }
    AzstModule.api.freeUp(true, () => {
      this._upTime = this._maxUpTime;
    });
  }

  private on_click_btn_xianyuUp() {
    AudioMgr.instance.playEffect(AzstAudioName.Effect.点击刷新按钮);
    if (this._upTime > 0) {
      TipMgr.showTip("操作太快");
      return;
    }

    if (PlayerModule.data.getItemNum(ItemEnum.仙玉_6) < this._costList[1]) {
      TipMgr.showTip("道具不足");
      return;
    }

    AzstModule.api.freeUp(false, () => {
      this._upTime = this._maxUpTime;
    });
  }

  private on_click_btn_add() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
      itemId: pkNeedItemId,
    });
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_header(event) {
    AudioMgr.instance.playEffect(518);
    if (event.node.isRobot == true) {
      TipMgr.showTip("演武守卫不可查看");
      return;
    }
    let userInfo = event.node.parent.userInfo;
    let userId = userInfo.userId;
    UIMgr.instance.showDialog(PlayerRouteName.UIPlayerOtherMsg, { userId: userId });
  }

  public tick(dt: any): void {
    //this.setFreeUp();

    this._upTime -= dt;
  }
}
