import { _decorator, instantiate, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { FarmModule } from "../../../module/farm/FarmModule";
import { FarmCollectorMessage, FarmCollectRequest, FarmSlotMessage } from "../../net/protocol/Farm";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { ProgressBar } from "cc";
import { color } from "cc";
import TipMgr from "../../../lib/tips/TipMgr";
import { FontColor } from "../../common/FmConstant";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { FarmAudioName } from "../../../module/farm/FarmConfig";
import { ItemCtrl } from "../../common/ItemCtrl";
import Formate from "../../../lib/utils/Formate";
import { FarmRouteName } from "../../../module/farm/FarmRoute";
import { LangMgr } from "../../mgr/LangMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIFarmCollect")
export class UIFarmCollect extends UINode {
  protected _openAct: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FARM}?prefab/ui/UIFarmCollect`;
  }

  private _slotMsg: FarmSlotMessage = null;

  /**采集的蜜蜂的数量  */
  private _beeCnf: number = 0;
  // 本葫芦最多可派遣多少人
  private _beeMax: number = 0;
  // 玩家在这个葫芦最多可派遣
  private _beeMine: number = 0;

  public init(args): void {
    super.init(args);
    this._slotMsg = args.slotMsg;
  }

  protected onEvtShow(): void {
    this.getNode("lbl_rate_hint").getComponent(Label).string = LangMgr.txMsgCode(235, []);

    this.updatePage();
  }

  protected onEvtClose(): void {}

  /**
   *
   * @param pNode 要设置的节点
   * @param msg 服务端消息
   * @param exclusive 专属标识
   */
  private setPlayer(pNode: Node, msg: FarmCollectorMessage, exclusive: boolean) {
    const isPull = (msg?.playerBaseMessage.userId || 0) > 0;

    pNode.getChildByPath("empty").active = !isPull;
    const houzi = pNode.getChildByName("houzi");
    houzi.active = isPull;
    const empty = pNode.getChildByName("empty");
    empty.active = !isPull;
    if (isPull) {
      if (msg.playerBaseMessage.userId == PlayerModule.data.playerId) {
        houzi.getComponentInChildren(Label).string = `我(${msg.beeNum})`;
      } else {
        houzi.getComponentInChildren(Label).string = `${msg.playerBaseMessage.nickname}(${msg.beeNum})`;
      }
    } else {
      if (exclusive) {
        houzi.getComponentInChildren(Label).string = `仅限主人采集`;
      } else {
        houzi.getComponentInChildren(Label).string = `无人采集`;
      }
    }
  }

  /** 设置进度条 */
  private setProgressBar() {
    this.getNode("lbl_count").getComponent(Label).string = `${this._beeCnf}/${this._beeMax}`;
    this.getNode("progress_bar_count").getComponent(ProgressBar).progress = this._beeCnf / this._beeMax;

    // 双方数量
    let myCollectorMsg: FarmCollectorMessage = null;
    let enemyCollectorMsg: FarmCollectorMessage = null;
    if (this._slotMsg.userId == PlayerModule.data.playerId) {
      myCollectorMsg = this._slotMsg.ownCollectorMessage;
      enemyCollectorMsg = this._slotMsg.otherCollectorMessage;
    } else {
      myCollectorMsg = this._slotMsg.otherCollectorMessage;
      enemyCollectorMsg = this._slotMsg.ownCollectorMessage;
    }

    // 数据修复
    myCollectorMsg.playerBaseMessage.userId = myCollectorMsg?.playerBaseMessage.userId || -1;
    myCollectorMsg.beeNum = myCollectorMsg?.beeNum || 0;
    enemyCollectorMsg.playerBaseMessage.userId = enemyCollectorMsg?.playerBaseMessage.userId || -1;
    enemyCollectorMsg.beeNum = enemyCollectorMsg?.beeNum || 0;

    // 采集时间
    let endTime = this._slotMsg.ownCollectorMessage?.endTime || -1;
    if (endTime == -1) {
      endTime = this._slotMsg.otherCollectorMessage?.endTime || -1;
    }

    // 时间Label
    const lblNeedTs = this.getNode("lbl_need_ts").getComponent(Label);

    // 无人采集
    if (endTime == -1) {
      lblNeedTs.string = TimeUtils.timeformatHMS(this._slotMsg.dragTime);
    }

    /**
     * 判断输赢的内部方法
     * @param mCount 我的数量
     * @param eCount 对方数量
     * @param farmUserId 场地主ID
     * @returns 输 false, 赢 true
     */
    function selfWinLose(mCount: number, eCount: number, farmUserId: number) {
      if (mCount <= 0) return false;

      if (mCount > eCount) return true;

      if (mCount < eCount) return false;

      if (farmUserId == PlayerModule.data.playerId) {
        return true;
      }

      return false;
    }

    // 工作数量
    const beeCount = Math.abs(this._beeCnf - enemyCollectorMsg.beeNum) || 1;

    if (selfWinLose(myCollectorMsg.beeNum, enemyCollectorMsg.beeNum, this._slotMsg.userId)) {
      if (selfWinLose(this._beeCnf, enemyCollectorMsg?.beeNum, this._slotMsg.userId)) {
        const remainTime =
          myCollectorMsg.remainTime -
          (TimeUtils.serverTime - myCollectorMsg.startTime) * (myCollectorMsg.beeNum - enemyCollectorMsg.beeNum);
        lblNeedTs.string = TimeUtils.timeformatHMS(remainTime / beeCount);
        lblNeedTs.color = FontColor.GREEN_2;
      } else {
        lblNeedTs.string = TimeUtils.timeformatHMS(this._slotMsg.dragTime / beeCount);
        lblNeedTs.color = FontColor.RED_2;
      }
    } else {
      if (selfWinLose(this._beeCnf, enemyCollectorMsg.beeNum, this._slotMsg.userId)) {
        lblNeedTs.string = TimeUtils.timeformatHMS(this._slotMsg.dragTime / beeCount);
        lblNeedTs.color = FontColor.GREEN_2;
      } else {
        const remainTime =
          enemyCollectorMsg.remainTime -
          (TimeUtils.serverTime - enemyCollectorMsg.startTime) * (enemyCollectorMsg.beeNum - myCollectorMsg.beeNum);
        lblNeedTs.string = TimeUtils.timeformatHMS(remainTime / beeCount);
        lblNeedTs.color = FontColor.RED_2;
      }
    }
  }

  private isSelfWin() {
    let selfWin = false;
    if (
      (this._slotMsg.ownCollectorMessage?.endTime || -1) > 0 &&
      this._slotMsg.ownCollectorMessage.playerBaseMessage.userId == PlayerModule.data.playerId
    ) {
      selfWin = true;
    } else if (
      (this._slotMsg.otherCollectorMessage?.endTime || -1) > 0 &&
      this._slotMsg.otherCollectorMessage.playerBaseMessage.userId == PlayerModule.data.playerId
    ) {
      selfWin = true;
    }

    return selfWin;
  }

  private updatePage() {
    if (this._slotMsg.gourdId < 0 || this._slotMsg.gourdId > 7) {
      log.error("葫芦ID错误");
      UIMgr.instance.back();
      return;
    }

    // 葫芦名称
    const cfgHulu = FarmModule.data.getConfigBlessLand(this._slotMsg.gourdId);
    this.getNode("lbl_hulu_name").getComponent(Label).string = cfgHulu.name;

    // 设置葫芦形态
    this.getNode("bg_jianhzudi").children.forEach((child) => {
      if (child.name.startsWith("img_hulu_")) {
        child.active = "img_hulu_" + this._slotMsg.gourdId == child.name;
      }
    });

    // 葫芦等级
    this.getNode("lbl_level").getComponent(Label).string = LangMgr.txMsgCode(466, [this._slotMsg.gourdId]); // `${}级`;

    // 是不是自己赢，设置倒计时颜色
    let selfWin = this.isSelfWin();
    this.getNode("lbl_need_ts").getComponent(Label).color = color().fromHEX(selfWin ? "00af04" : "e10000");

    // 两方采集状态
    this.setPlayer(this.getNode("houzi_1"), this._slotMsg.ownCollectorMessage, this._slotMsg.deadlineStamp == -1);
    this.setPlayer(this.getNode("houzi_2"), this._slotMsg.otherCollectorMessage, this._slotMsg.deadlineStamp == -1);

    // 派遣上限
    this.getNode("lbl_max").getComponent(Label).string = LangMgr.txMsgCode(467, [cfgHulu.send]); // `上限${}`;

    // 设置计数(x/y)
    this.updateCount();
    // 进度条
    this.setProgressBar();

    // 设置按钮文字
    if (
      this._slotMsg.ownCollectorMessage.playerBaseMessage.userId != PlayerModule.data.playerId &&
      this._slotMsg.ownCollectorMessage.beeNum > 0
    ) {
      this.getNode("btn_collect").getComponentInChildren(Label).string = LangMgr.txMsgCode(468);
    } else {
      this.getNode("btn_collect").getComponentInChildren(Label).string = LangMgr.txMsgCode(469);
    }

    // 召回按钮权限
    if (
      (this._slotMsg.otherCollectorMessage.playerBaseMessage.userId == PlayerModule.data.playerId &&
        this._slotMsg.otherCollectorMessage.beeNum > 0) ||
      (this._slotMsg.ownCollectorMessage.playerBaseMessage.userId == PlayerModule.data.playerId &&
        this._slotMsg.ownCollectorMessage.beeNum > 0)
    ) {
      this.getNode("btn_callback").active = true;
    } else {
      this.getNode("btn_callback").active = false;
    }

    // 设置奖励列表
    const nodeContentReward = this.getNode("content_reward");
    const nodeItemRewardFix = this.getNode("item_reward_0");
    const nodeItemRewardRate = this.getNode("item_reward_1");
    nodeContentReward.children.forEach((child) => {
      child.active = false;
    });
    // 必得
    for (let idx in cfgHulu.reward1List) {
      let itemInfo = cfgHulu.reward1List[idx];
      const nodeNew = instantiate(nodeItemRewardFix);
      nodeNew.active = true;
      nodeNew.setParent(nodeContentReward);
      nodeNew.getChildByName("Item").getComponent(ItemCtrl).setItemId(itemInfo[0], itemInfo[1]);
    }

    // 几率获得
    let powerAll = 0; // 总权重
    for (let idx in cfgHulu.reward2List) {
      let itemInfo = cfgHulu.reward2List[idx];
      powerAll += itemInfo[2];
    }
    for (let idx in cfgHulu.reward2List) {
      let itemInfo = cfgHulu.reward2List[idx];
      const nodeNew = instantiate(nodeItemRewardRate);
      nodeNew.active = true;
      nodeNew.setParent(nodeContentReward);
      nodeNew.getChildByName("Item").getComponent(ItemCtrl).setItemId(itemInfo[0], itemInfo[1]);
      nodeNew.getChildByName("lbl_rate").getComponent(Label).string = `${Formate.formatDecimal(
        (itemInfo[2] * 100) / powerAll,
        2
      )}%`;
    }
  }

  // 更新数量信息
  private updateCount() {
    const cfgHulu = FarmModule.data.getConfigBlessLand(this._slotMsg.gourdId);
    this._beeMine = FarmModule.data.farmTrainMessage.beeMetric.relaxBeeCnt; // 闲置工人
    if (this._slotMsg.otherCollectorMessage.playerBaseMessage.userId == PlayerModule.data.playerId) {
      this._beeMine += this._slotMsg.otherCollectorMessage.beeNum;
      this._beeCnf = this._slotMsg.otherCollectorMessage.beeNum;
    } else if (this._slotMsg.ownCollectorMessage.playerBaseMessage.userId == PlayerModule.data.playerId) {
      this._beeMine += this._slotMsg.ownCollectorMessage.beeNum;
      this._beeCnf = this._slotMsg.ownCollectorMessage.beeNum;
    } else {
      this._beeCnf = 1;
    }
    this._beeMax = cfgHulu.send;
  }

  private on_click_btn_subtract() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.资源详情界面点击加减号);
    if (this._beeCnf <= 1) {
      return;
    }
    this._beeCnf--;
    this.setProgressBar();
  }

  private on_click_btn_add() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.资源详情界面点击加减号);
    if (this._beeCnf >= this._beeMax) {
      return;
    }
    if (this._beeCnf >= this._beeMine) {
      UIMgr.instance.showDialog(FarmRouteName.UIFarmBeeManage, { guideHire: true }, (resp) => {
        if (resp.ok) {
          this.updateCount();
          this._beeCnf++;
          this.setProgressBar();
        }
      });

      return;
    }

    this._beeCnf++;
    this.setProgressBar();
  }

  private on_click_btn_callback() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    FarmModule.api.recall(this._slotMsg.identifyId, () => {
      UIMgr.instance.back();
    });
  }

  private on_click_btn_collect() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.资源详情界面点击采集);
    if (this._slotMsg.deadlineStamp != -1 && this._slotMsg.userId != PlayerModule.data.playerId) {
      TipMgr.showTip(LangMgr.txMsgCode(470));
      return;
    }

    let relaxBeeCnt = FarmModule.data.farmTrainMessage.beeMetric.relaxBeeCnt;
    if (relaxBeeCnt <= 0) {
      TipMgr.showTip(LangMgr.txMsgCode(471));
      return;
    }

    const otherUserId = this._slotMsg.otherCollectorMessage?.playerBaseMessage.userId || 0;
    if (
      this._slotMsg.userId != PlayerModule.data.playerId &&
      otherUserId > 0 &&
      otherUserId != PlayerModule.data.playerId
    ) {
      TipMgr.showTip(LangMgr.txMsgCode(472));
      return;
    }

    this._beeCnf = this._beeCnf || 1;
    let data: FarmCollectRequest = {
      farmUserId: this._slotMsg.userId,
      rank: this._slotMsg.rank,
      identifyId: this._slotMsg.identifyId,
      beeCnf: this._beeCnf,
    };

    FarmModule.api.collectFarm(data, () => {
      AudioMgr.instance.playEffect(FarmAudioName.Effect.猴子采集音乐);
      UIMgr.instance.back();
    });
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
