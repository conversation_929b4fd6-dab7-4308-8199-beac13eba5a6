import { Animation, Label, Layers, Node, Prefab, UIOpacity, _decorator, instantiate, isValid, tween } from "cc";
import ManagerSection from "./ManagerSection";
import GameObject from "../../../lib/object/GameObject";
import FightManager from "./FightManager";
import ResMgr from "../../../lib/common/ResMgr";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

@ccclass("HpManager")
export class HpManager extends ManagerSection {
  public static sectionName(): string {
    return "HpManager";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.createRoot("HpRoot", FightManager.instance);
    this.ready();
  }

  public onStart(): void {
    super.onStart();
  }

  public callHpRender(callback) {
    if (FightManager.instance.fightOver == true) {
      return;
    }

    this.assetMgr.loadPrefab(BundleEnum.RESOURCES, "prefab/effect/hp", (prefab) => {
      let node = instantiate(prefab);
      node.getComponent(UIOpacity).opacity = 0;
      node.walk((child) => (child.layer = this.root.layer));
      this.root.addChild(node);
      callback && callback(node);
    });
  }
}
