import { UIChat } from "../../game/ui/ui_chat/UIChat";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum ChatRouteItem {
  UIChat = "UIChat",
}
export class ChatRoute {


  rotueTables: Recording[] = [
    //
    { node: UIChat, uiName: ChatRouteItem.UIChat, relevanceUIList: [], keep: false },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
