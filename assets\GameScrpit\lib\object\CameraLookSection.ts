
import { Node, Rect, Vec2, size, v3 } from "cc";
import GameObject from "./GameObject";
import { Section } from "./Section";

export default class CameraLookSection extends Section {
    public camera:Node = null;
    public rate:number = 0.15;
    public boundary:Rect;
    public isLimitBoundary:boolean;
    public static sectionName():string {
        return "CameraLookSection";
    }
    public onInit(go:GameObject, args) {
        super.onInit(go, args);
        this.camera = args;
        this.ready();
    }

    public setBoundary(rect:Rect) {
        this.boundary = rect;
        this.isLimitBoundary = true;
    }

    public updateSelf(dt) {
        // if (this.camera == null) { return; }
        // let targetPos = this.getSub().getPosVec2();
        // let cameraPos = this.camera.getPosition();
        // let srcPos = new Vec2(cameraPos.x, cameraPos.y);
        // let distance = Vec2.distance(targetPos, srcPos);
        // let length = distance*this.rate;
        // let unitVec = targetPos.divide(srcPos).normalize();
        // let addVec = unitVec.mul(length);
        // let nextVec = srcPos.add(addVec);
        // let winSize = size();

        // //限定范围
        // let viewLeft = nextVec.x-winSize.width/2;
        // let viewDown = nextVec.y-winSize.height/2;
        // let viewRight = nextVec.x+winSize.width/2;
        // let viewUp = nextVec.y+winSize.height/2;
        // if (this.isLimitBoundary) {
        //     if (viewLeft < this.boundary.x) {
        //         nextVec.x = this.boundary.x+winSize.width/2;
        //     } else if (viewRight > (this.boundary.x+this.boundary.width)) {
        //         nextVec.x = (this.boundary.x+this.boundary.width)-winSize.width/2;
        //     }
        //     if (viewDown < this.boundary.y) {
        //         nextVec.y = this.boundary.y+winSize.height/2;
        //     } else if (viewUp > (this.boundary.y+this.boundary.height)) {
        //         nextVec.y = (this.boundary.y+this.boundary.height)-winSize.height/2;
        //     }
        // }

        // this.camera.setPosition(v3(nextVec.x,nextVec.y,cameraPos.z))
    }


    public getCameraEdge(){
        let winSize = size();
        let cameraPos = this.camera.getPosition();
        return [cameraPos.x - winSize.width/2,cameraPos.x + winSize.width/2];
    }

    public onRemove(): void {
        // this.camera.x = cc.winSize.width/2
    }
    
}
