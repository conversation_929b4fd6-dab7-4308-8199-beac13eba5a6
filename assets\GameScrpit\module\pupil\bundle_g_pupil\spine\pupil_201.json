{"skeleton": {"hash": "x5LpMyaHVZ0BD24Hcn3qgzc2Cz4=", "spine": "3.8.75", "x": -276.71, "y": -524.35, "width": 752, "height": 1502, "images": "./images/", "audio": "D:/spine导出/弟子spine/女弟子"}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "length": 101.84, "x": 3.16, "y": 0.49, "scaleX": 0.4331, "scaleY": 0.4331}, {"name": "all2", "parent": "all", "length": 75.1, "rotation": -2.04, "x": -14.96, "y": 94.28}, {"name": "all3", "parent": "all2", "length": 29.16, "rotation": 86.64, "x": 0.31, "y": 0.44}, {"name": "all4", "parent": "all3", "length": 22.4, "rotation": -8.78, "x": 29.16}, {"name": "all5", "parent": "all4", "length": 13.28, "rotation": 11.22, "x": 22.4}, {"name": "all6", "parent": "all5", "rotation": 7.99, "x": 24.87, "y": 11.12, "color": "abe323ff"}, {"name": "all7", "parent": "all5", "rotation": 7.99, "x": 60.96, "y": 19.54}, {"name": "all8", "parent": "all5", "length": 18.18, "rotation": 0.32, "x": 157.84, "y": 30}, {"name": "all9", "parent": "all8", "length": 23.08, "rotation": -49.24, "x": 18.18}, {"name": "all10", "parent": "all9", "length": 22.63, "rotation": -38.83, "x": 22.34, "y": -0.49}, {"name": "all11", "parent": "all5", "length": 15.27, "rotation": 148.4, "x": 86.42, "y": 80.3}, {"name": "all12", "parent": "all11", "length": 18.16, "rotation": -56.32, "x": 15.27}, {"name": "all13", "parent": "all5", "length": 10.88, "rotation": 153.76, "x": 49.41, "y": 76.43}, {"name": "all14", "parent": "all13", "length": 14.85, "rotation": -41, "x": 10.88}, {"name": "all15", "parent": "all4", "x": 11.1, "y": 20.92}, {"name": "all16", "parent": "all4", "x": 23.2, "y": -33.13}, {"name": "all17", "parent": "all5", "length": 20.19, "rotation": 160.74, "x": 25.99, "y": 38.47}, {"name": "all18", "parent": "all17", "length": 30.32, "rotation": -31.61, "x": 20.19}, {"name": "all19", "parent": "all18", "length": 32.2, "rotation": 19.49, "x": 30.32}, {"name": "all20", "parent": "all19", "length": 19.77, "rotation": 31.29, "x": 32.2}, {"name": "all21", "parent": "all5", "length": 26.87, "rotation": -143.85, "x": 41.03, "y": -39.26}, {"name": "all22", "parent": "all21", "length": 26.78, "rotation": 8.63, "x": 26.87}, {"name": "all23", "parent": "all22", "length": 31.86, "rotation": -0.56, "x": 26.78}, {"name": "all24", "parent": "all23", "length": 30.16, "rotation": -32.84, "x": 31.86}, {"name": "all27", "parent": "all16", "length": 30.24, "rotation": -146.4, "x": -10.07, "y": -7.89}, {"name": "all28", "parent": "all27", "length": 21.91, "rotation": -5.96, "x": 30.24}, {"name": "all29", "parent": "all28", "length": 13.77, "rotation": -2.16, "x": 21.91}, {"name": "all30", "parent": "all15", "length": 22.73, "rotation": -171.63, "x": -7.83, "y": -0.26}, {"name": "all31", "parent": "all30", "length": 23.28, "rotation": -11.43, "x": 22.75, "y": -0.19}, {"name": "all32", "parent": "all31", "length": 18.22, "rotation": -5.01, "x": 23.28}, {"name": "all33", "parent": "all2", "length": 20.09, "rotation": -113.86, "x": -20.25, "y": -9.24}, {"name": "all34", "parent": "all33", "length": 20.72, "rotation": -0.24, "x": 20.09}, {"name": "all35", "parent": "all2", "length": 20.29, "rotation": -82.01, "x": -6.66, "y": -11.04}, {"name": "all36", "parent": "all35", "length": 19.84, "rotation": 9.43, "x": 20.29}, {"name": "all37", "parent": "all36", "length": 16.55, "rotation": 5.74, "x": 19.84}, {"name": "all38", "parent": "all2", "length": 18.23, "rotation": -64.11, "x": 22.34, "y": -6.85}, {"name": "all39", "parent": "all38", "length": 20.53, "rotation": 6.44, "x": 18.23}, {"name": "all40", "parent": "all39", "length": 16.53, "rotation": 6.94, "x": 20.53}, {"name": "all41", "parent": "all2", "length": 27.86, "rotation": -90.59, "x": -13.01, "y": -23.86}, {"name": "all42", "parent": "all41", "length": 34.06, "rotation": 15.97, "x": 27.86}, {"name": "all43", "parent": "all42", "length": 14.97, "rotation": -71.68, "x": 34.06}, {"name": "all44", "parent": "all2", "length": 31.48, "rotation": -66.59, "x": 18.63, "y": -23.17}, {"name": "all45", "parent": "all44", "length": 27.57, "rotation": -0.14, "x": 31.48}, {"name": "all46", "parent": "all45", "length": 13.42, "rotation": 2.09, "x": 27.57}, {"name": "all47", "parent": "all", "length": 36.98, "rotation": -178.8, "x": -23.8, "y": 9.27}, {"name": "target1", "parent": "all47", "rotation": 178.8, "x": -1.25, "y": -0.61, "color": "ff3f00ff"}, {"name": "target2", "parent": "all47", "rotation": 178.8, "x": 12.53, "y": 7.15, "color": "ff3f00ff"}, {"name": "all48", "parent": "all", "length": 31.61, "rotation": -176.63, "x": 24, "y": 13.13}, {"name": "target3", "parent": "all48", "rotation": 176.63, "x": -0.43, "y": -2.01, "color": "ff3f00ff"}, {"name": "target4", "parent": "all48", "rotation": 176.63, "x": -4.95, "y": 10.66, "color": "ff3f00ff"}], "slots": [{"name": "sd", "bone": "all", "color": "ffffff87", "attachment": "sd"}, {"name": "f2", "bone": "all", "attachment": "f2"}, {"name": "f1", "bone": "all", "attachment": "f1"}, {"name": "s3", "bone": "all", "attachment": "s3"}, {"name": "j2", "bone": "all", "attachment": "j2"}, {"name": "j1", "bone": "all", "attachment": "j1"}, {"name": "b2", "bone": "all", "attachment": "b2"}, {"name": "b1", "bone": "all", "attachment": "b1"}, {"name": "s1", "bone": "all", "attachment": "s1"}, {"name": "tou1", "bone": "all5", "attachment": "tou1"}, {"name": "tou", "bone": "all5"}], "ik": [{"name": "target1", "bones": ["all41", "all42"], "target": "target1"}, {"name": "target2", "order": 3, "bones": ["all42", "all43"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 1, "bones": ["all44", "all45"], "target": "target3", "bendPositive": false}, {"name": "target4", "order": 2, "bones": ["all45", "all46"], "target": "target4"}], "transform": [{"name": "b1", "order": 5, "bones": ["all17"], "target": "all6", "rotation": 152.75, "x": 4.91, "y": 26.93, "shearY": 360, "rotateMix": -0.17, "translateMix": -0.17, "scaleMix": -0.17, "shearMix": -0.17}, {"name": "b2", "order": 6, "bones": ["all21"], "target": "all6", "rotation": -151.84, "x": 8.99, "y": -52.14, "shearY": 360, "rotateMix": -0.173, "translateMix": -0.173, "scaleMix": -0.173, "shearMix": -0.173}, {"name": "face", "order": 4, "bones": ["all7"], "target": "all6", "x": 36.9, "y": 3.32, "rotateMix": -0.852, "translateMix": -0.852, "scaleMix": -0.852, "shearMix": -0.852}, {"name": "s1", "order": 7, "bones": ["all15"], "target": "all6", "rotation": -19.21, "x": -29.96, "y": 15.93, "shearY": 360, "rotateMix": -0.162, "translateMix": -0.162, "scaleMix": -0.162, "shearMix": -0.162}, {"name": "s2", "order": 8, "bones": ["all16"], "target": "all6", "rotation": -19.21, "x": -36.32, "y": -39.09, "shearY": 360, "rotateMix": -0.177, "translateMix": -0.177, "scaleMix": -0.177, "shearMix": -0.177}], "skins": [{"name": "default", "attachments": {"j1": {"j1": {"type": "mesh", "uvs": [0.61328, 0, 0.79026, 0.01974, 0.91921, 0.13938, 1, 0.31166, 1, 0.49512, 1, 0.7009, 0.89898, 0.76631, 0.81049, 0.79821, 0.79532, 0.91945, 0.66385, 0.97528, 0.4894, 0.99602, 0.33011, 0.98645, 0.32506, 0.90988, 0.34276, 0.82214, 0.14302, 0.73759, 0, 0.60678, 0.03177, 0.4345, 0.08234, 0.26859, 0.21887, 0.10269, 0.37815, 0.01814, 0.49782, 0.12741, 0.46514, 0.28409, 0.48039, 0.46138, 0.51306, 0.63592, 0.5893, 0.8187, 0.51524, 0.90254, 0.40415, 0.94927], "triangles": [11, 26, 10, 10, 25, 9, 10, 26, 25, 11, 12, 26, 8, 9, 24, 26, 12, 25, 9, 25, 24, 12, 13, 25, 25, 13, 24, 8, 24, 7, 24, 13, 23, 13, 14, 23, 24, 23, 7, 7, 23, 6, 6, 23, 5, 23, 14, 15, 22, 15, 16, 23, 15, 22, 23, 4, 5, 23, 22, 4, 4, 22, 3, 21, 16, 17, 16, 21, 22, 3, 21, 2, 2, 21, 20, 20, 1, 2, 20, 0, 1, 21, 3, 22, 17, 18, 21, 21, 18, 20, 18, 19, 20, 20, 19, 0], "vertices": [1, 39, -10.14, 4.78, 1, 2, 39, -8.98, 14.24, 0.9956, 40, -32.03, 23.1, 0.0044, 2, 39, 0.69, 21.6, 0.93988, 40, -20.81, 27.77, 0.06012, 2, 39, 14.92, 26.64, 0.68489, 40, -5.78, 29.05, 0.31511, 2, 39, 30.31, 27.46, 0.24291, 40, 9.32, 25.94, 0.75709, 2, 39, 47.57, 28.37, 0.01096, 40, 26.25, 22.46, 0.98904, 3, 39, 53.34, 23.32, 0.00048, 40, 30.55, 16.11, 0.99509, 41, -16.34, 2.23, 0.00443, 2, 40, 32.23, 10.98, 0.91303, 41, -10.94, 2.05, 0.08697, 2, 40, 42.05, 8.14, 0.3144, 41, -4.91, 10.3, 0.6856, 2, 40, 45.24, 0.37, 0.0255, 41, 3.48, 10.63, 0.9745, 1, 41, 12.27, 7.27, 1, 1, 41, 19.03, 2.15, 1, 2, 40, 36.24, -16.11, 0.03139, 41, 15.89, -3.46, 0.96861, 2, 40, 29.21, -13.71, 0.4457, 41, 11.22, -9.25, 0.5543, 3, 39, 53.05, -16.82, 0.01384, 40, 20.12, -22.65, 0.87601, 41, 16.51, -20.85, 0.11015, 3, 39, 42.48, -24.97, 0.12687, 40, 7.83, -27.86, 0.85043, 41, 17.2, -34.18, 0.0227, 3, 39, 27.94, -24.06, 0.49537, 40, -6, -23.3, 0.50381, 41, 8.17, -45.61, 0.00082, 2, 39, 13.89, -22.12, 0.89687, 40, -19.11, -17.86, 0.10313, 2, 39, -0.42, -15.63, 0.99984, 40, -31.31, -7.97, 0.00016, 1, 39, -7.96, -7.58, 1, 1, 39, 0.87, -0.76, 1, 1, 39, 14.11, -1.79, 1, 2, 39, 28.94, -0.19, 0.01971, 40, 1, -0.46, 0.98029, 2, 40, 15.71, -1.72, 0.99574, 41, -4.67, -17.83, 0.00426, 2, 40, 31.56, -0.85, 0.85649, 41, -0.06, -2.64, 0.14351, 1, 41, 6.98, 1.3, 1, 1, 41, 14.05, 1.55, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 53, "height": 84}}, "j2": {"j2": {"type": "mesh", "uvs": [0.17824, 0.01589, 0.35824, 0.00839, 0.49398, 0.01589, 0.65479, 0.14017, 0.76102, 0.27946, 0.89381, 0.42839, 1, 0.56017, 0.96168, 0.65446, 0.85988, 0.73803, 0.89086, 0.86553, 0.89086, 0.95446, 0.7625, 1, 0.58397, 0.9866, 0.4807, 0.90624, 0.39512, 0.82053, 0.2225, 0.80124, 0.10299, 0.68874, 0.03512, 0.53767, 0.00119, 0.35675, 0, 0.18961, 0.07791, 0.07711, 0.26447, 0.15497, 0.35164, 0.33006, 0.44067, 0.47282, 0.52969, 0.62771, 0.62057, 0.78394, 0.70403, 0.90516], "triangles": [12, 26, 11, 11, 26, 10, 12, 13, 26, 26, 9, 10, 13, 25, 26, 13, 14, 25, 9, 26, 8, 26, 25, 8, 25, 14, 24, 14, 15, 24, 15, 16, 24, 25, 24, 8, 8, 24, 7, 16, 23, 24, 6, 7, 5, 7, 24, 5, 24, 23, 5, 16, 17, 23, 17, 22, 23, 17, 18, 22, 23, 4, 5, 23, 22, 4, 18, 21, 22, 18, 19, 21, 22, 3, 4, 3, 21, 2, 2, 21, 1, 21, 3, 22, 19, 20, 21, 20, 0, 21, 21, 0, 1], "vertices": [1, 42, -10.52, -2.29, 1, 1, 42, -7.1, 8.16, 1, 1, 42, -3.5, 15.64, 1, 2, 42, 9.8, 20.98, 0.98011, 43, -21.73, 20.92, 0.01989, 2, 42, 23.05, 22.75, 0.75726, 43, -8.48, 22.72, 0.24274, 3, 42, 37.65, 25.73, 0.25679, 43, 6.11, 25.75, 0.72069, 44, -20.51, 26.51, 0.02252, 3, 42, 50.32, 27.73, 0.05664, 43, 18.78, 27.78, 0.83742, 44, -7.78, 28.08, 0.10594, 3, 42, 56.85, 22.67, 0.01878, 43, 25.31, 22.73, 0.78382, 44, -1.43, 22.8, 0.19739, 3, 42, 61.12, 14.32, 0.00059, 43, 29.61, 14.4, 0.45668, 44, 2.56, 14.31, 0.54273, 2, 43, 40.28, 12.28, 0.01969, 44, 13.14, 11.81, 0.98031, 1, 44, 20, 8.85, 1, 1, 44, 20.42, 0.15, 1, 2, 43, 42.98, -8.85, 0.00071, 44, 15.07, -9.41, 0.99929, 3, 42, 65.85, -12.36, 0, 43, 34.4, -12.28, 0.15218, 44, 6.38, -12.52, 0.84782, 3, 42, 57.24, -14.6, 0.01291, 43, 25.8, -14.54, 0.70963, 44, -2.3, -14.46, 0.27746, 3, 42, 51.9, -23.82, 0.08079, 43, 20.48, -23.77, 0.8894, 44, -7.96, -23.49, 0.02981, 3, 42, 40.44, -27.16, 0.2434, 43, 9.03, -27.14, 0.75639, 44, -19.52, -26.45, 0.00021, 2, 42, 27.12, -26.39, 0.58619, 43, -4.3, -26.4, 0.41381, 2, 42, 12.21, -22.78, 0.92333, 43, -19.21, -22.83, 0.07667, 2, 42, -0.89, -17.73, 0.99853, 43, -32.33, -17.81, 0.00147, 1, 42, -7.96, -9.86, 1, 1, 42, 2.28, -1.65, 1, 2, 42, 17.91, -2.06, 0.9984, 43, -13.56, -2.09, 0.0016, 2, 42, 31.06, -1.37, 0.69278, 43, -0.42, -1.37, 0.30722, 2, 42, 45.15, -1.05, 0.00095, 43, 13.68, -1.02, 0.99905, 2, 43, 27.92, -0.6, 0.33267, 44, 0.32, -0.62, 0.66733, 1, 44, 11.69, 0.03, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 61, "height": 84}}, "f1": {"f1": {"type": "mesh", "uvs": [0.06721, 0.02244, 0.19655, 0, 0.36101, 0.00673, 0.46079, 0.12673, 0.41275, 0.24673, 0.54764, 0.23816, 0.54025, 0.38101, 0.68808, 0.46673, 0.88579, 0.59815, 1, 0.80244, 0.95231, 1, 0.73612, 1, 0.51068, 0.92958, 0.52731, 0.79673, 0.44786, 0.63958, 0.26308, 0.51673, 0.1596, 0.38958, 0.00992, 0.31244, 0, 0.11673, 0.18206, 0.15742, 0.29128, 0.31626, 0.42093, 0.43972, 0.57993, 0.56018, 0.71781, 0.70815, 0.7425, 0.85611], "triangles": [13, 14, 23, 23, 8, 9, 24, 23, 9, 13, 23, 24, 12, 13, 24, 11, 12, 24, 10, 24, 9, 11, 24, 10, 22, 6, 7, 14, 21, 22, 8, 22, 7, 23, 22, 8, 14, 22, 23, 6, 4, 5, 6, 21, 4, 15, 20, 21, 21, 6, 22, 15, 21, 14, 19, 0, 1, 18, 0, 19, 3, 19, 2, 17, 18, 19, 19, 1, 2, 4, 19, 3, 20, 19, 4, 17, 19, 20, 21, 20, 4, 16, 17, 20, 15, 16, 20], "vertices": [1, 21, -15.7, 1.01, 1, 1, 21, -11.42, 12.43, 1, 1, 21, -2.47, 24.66, 1, 2, 21, 14.51, 24.52, 0.99335, 22, -8.54, 26.1, 0.00665, 2, 21, 24.04, 13.01, 0.59402, 22, -0.84, 13.28, 0.40598, 2, 21, 29.98, 23.95, 0.15018, 22, 6.67, 23.21, 0.84982, 3, 21, 43.83, 14.07, 0.0082, 22, 18.89, 11.37, 0.91502, 23, -8.01, 11.29, 0.07677, 2, 22, 35.56, 14.7, 0.07759, 23, 8.63, 14.79, 0.92241, 2, 23, 32.38, 18.15, 0.61429, 24, -9.41, 15.53, 0.38571, 1, 24, 16.18, 22.37, 1, 1, 24, 38.79, 14.59, 1, 1, 24, 35.88, -5.09, 1, 2, 23, 39.27, -33.8, 0.01704, 24, 24.56, -24.38, 0.98296, 2, 23, 28.4, -22.23, 0.26406, 24, 9.14, -20.55, 0.73594, 3, 22, 36.15, -15.48, 0.11558, 23, 9.52, -15.39, 0.82155, 24, -10.42, -25.05, 0.06287, 3, 21, 43.39, -16.11, 0.01605, 22, 13.92, -18.4, 0.94066, 23, -12.68, -18.53, 0.04329, 2, 21, 25.51, -15.79, 0.65776, 22, -3.7, -15.41, 0.34224, 2, 21, 10.29, -22.29, 0.99737, 22, -19.73, -19.55, 0.00263, 1, 21, -9.7, -10.3, 1, 1, 21, 3.53, 1.06, 1, 1, 21, 24.85, -0.88, 1, 1, 22, 16.77, -1.47, 1, 2, 23, 10.43, -0.03, 0.9999, 24, -17.99, -11.65, 0.0001, 2, 23, 32.03, -2.1, 0.25885, 24, 1.28, -1.67, 0.74115, 2, 23, 46.76, -12, 0.00072, 24, 19.03, -2, 0.99928], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 92, "height": 119}}, "f2": {"f2": {"type": "mesh", "uvs": [0.54621, 0.20745, 0.54621, 0.10307, 0.62731, 0.01728, 0.78769, 0, 0.9241, 0.00727, 0.99783, 0.13024, 0.99415, 0.25035, 0.83562, 0.31899, 0.77478, 0.38762, 0.61721, 0.50584, 0.47481, 0.6384, 0.35376, 0.7949, 0.39648, 1, 0.17812, 0.98269, 0, 0.94219, 0, 0.7415, 0.02147, 0.57764, 0.16625, 0.44877, 0.3585, 0.39721, 0.41547, 0.24624, 0.796, 0.13713, 0.6857, 0.26617, 0.60433, 0.35313, 0.45968, 0.46674, 0.26621, 0.57895, 0.17037, 0.76409], "triangles": [15, 16, 25, 25, 24, 11, 14, 15, 25, 13, 25, 11, 14, 25, 13, 13, 11, 12, 24, 17, 18, 16, 17, 24, 24, 23, 10, 25, 16, 24, 11, 24, 10, 19, 0, 22, 22, 21, 8, 18, 19, 22, 23, 18, 22, 9, 22, 8, 23, 22, 9, 24, 18, 23, 10, 23, 9, 20, 3, 4, 20, 4, 5, 2, 3, 20, 1, 2, 20, 0, 1, 20, 6, 20, 5, 21, 0, 20, 7, 21, 20, 6, 7, 20, 22, 0, 21, 8, 21, 7], "vertices": [2, 17, 13.57, -15.18, 0.60107, 18, 2.32, -16.39, 0.39893, 2, 17, 3.23, -19.4, 0.95223, 18, -4.27, -25.41, 0.04777, 2, 17, -7.81, -16.64, 0.99901, 18, -15.12, -28.85, 0.00099, 1, 17, -14.55, -5.02, 1, 1, 17, -18.12, 5.76, 1, 1, 17, -8.25, 16.4, 1, 1, 17, 3.76, 20.97, 1, 2, 17, 15.54, 11.57, 0.95584, 18, -10.02, 7.42, 0.04416, 2, 17, 24.25, 9.67, 0.43344, 18, -1.61, 10.36, 0.56656, 2, 18, 16.41, 12.86, 0.9282, 19, -8.83, 16.76, 0.0718, 3, 18, 34.33, 17.33, 0.1433, 19, 9.55, 15, 0.83536, 20, -11.56, 24.58, 0.02133, 2, 19, 29.05, 16.15, 0.32256, 20, 5.7, 15.44, 0.67744, 1, 20, 27.42, 20.15, 1, 1, 20, 26.54, 1.95, 1, 1, 20, 23, -13.04, 1, 2, 19, 40.89, -11.31, 0.29711, 20, 1.56, -14.19, 0.70289, 2, 19, 25.41, -19.73, 0.97893, 20, -16.05, -13.34, 0.02107, 2, 18, 43.02, -14.17, 0.01206, 19, 7.24, -17.59, 0.98794, 3, 17, 38.26, -21.93, 0.00087, 18, 26.89, -9.2, 0.84507, 19, -6.31, -7.53, 0.15406, 2, 17, 21.52, -23.66, 0.13513, 18, 13.53, -19.45, 0.86487, 1, 17, -1.23, 1.17, 1, 2, 17, 15.01, -2.09, 0.92931, 18, -3.31, -4.49, 0.07069, 2, 17, 26.18, -4.82, 0.00984, 18, 7.63, -0.96, 0.99016, 2, 18, 24.5, 1.76, 0.97728, 19, -4.91, 3.6, 0.02272, 1, 19, 14.07, -2.88, 1, 2, 19, 34.91, 1.73, 0.00034, 20, 3.22, 0.06, 0.99966], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 83, "height": 107}}, "b1": {"b1": {"type": "mesh", "uvs": [0.21443, 0, 0.27754, 0.03005, 0.3365, 0, 0.36547, 0, 0.39754, 0, 0.42133, 0.04355, 0.49788, 0.04805, 0.59305, 0.03905, 0.70892, 0.02855, 0.72531, 0.1021, 0.74077, 0.16934, 0.80119, 0.3303, 0.87426, 0.48719, 0.97122, 0.62166, 1, 0.70316, 0.91641, 0.82949, 0.81103, 0.93136, 0.72391, 0.99249, 0.65365, 0.89876, 0.53702, 0.94766, 0.42039, 1, 0.35294, 1, 0.23912, 0.94563, 0.10985, 0.8682, 0.02835, 0.78874, 0, 0.69705, 0.05645, 0.56461, 0.10142, 0.41384, 0.14638, 0.26103, 0.18011, 0.13878, 0.18854, 0.03283, 0.33735, 0.11402, 0.32525, 0.2544, 0.31617, 0.44961, 0.31466, 0.69309, 0.33886, 0.86856, 0.56426, 0.26317, 0.65048, 0.48032, 0.78663, 0.76766, 0.44173, 0.25659, 0.45837, 0.41452, 0.49618, 0.67115, 0.23751, 0.11621, 0.21331, 0.26537, 0.19364, 0.44084, 0.14675, 0.64702, 0.52341, 0.83566, 0.62779, 0.81153, 0.57787, 0.63167], "triangles": [42, 0, 1, 30, 0, 42, 9, 36, 7, 36, 6, 7, 39, 5, 6, 9, 7, 8, 3, 4, 5, 31, 2, 3, 1, 2, 31, 31, 3, 5, 10, 36, 9, 23, 45, 22, 22, 45, 34, 23, 24, 45, 24, 25, 45, 25, 26, 45, 45, 44, 34, 45, 26, 44, 29, 30, 42, 44, 26, 27, 34, 44, 33, 44, 43, 33, 33, 43, 32, 27, 28, 44, 44, 28, 43, 28, 29, 43, 43, 42, 32, 43, 29, 42, 32, 42, 31, 42, 1, 31, 19, 20, 46, 22, 35, 21, 46, 20, 35, 20, 21, 35, 19, 47, 18, 19, 46, 47, 35, 41, 46, 46, 48, 47, 46, 41, 48, 35, 34, 41, 41, 34, 33, 41, 40, 48, 22, 34, 35, 33, 40, 41, 40, 32, 39, 40, 33, 32, 40, 39, 36, 32, 31, 39, 31, 5, 39, 17, 18, 16, 18, 38, 16, 16, 38, 15, 18, 47, 38, 14, 15, 13, 47, 48, 38, 48, 37, 38, 15, 38, 13, 38, 12, 13, 38, 37, 12, 48, 40, 37, 37, 11, 12, 40, 36, 37, 37, 36, 10, 37, 10, 11, 39, 6, 36], "vertices": [6, 36, -36.6, -35.48, 5e-05, 33, -24.04, -15.71, 0.01039, 31, -15.98, -12.88, 0.14265, 2, -25.56, 10.58, 0.55565, 6, -62.76, 29.53, 0.07526, 7, -99.66, 26.21, 0.216, 6, 36, -31.44, -29.76, 0.00216, 33, -20.89, -8.68, 0.03492, 31, -17.01, -5.24, 0.11059, 2, -18.16, 8.44, 0.53598, 6, -65.79, 22.45, 0.10035, 7, -102.7, 19.12, 0.216, 6, 36, -30.87, -22.53, 0.00809, 33, -22.57, -1.62, 0.06254, 31, -22.16, -0.14, 0.06078, 2, -11.41, 11.08, 0.47697, 6, -64, 15.42, 0.17562, 7, -100.9, 12.1, 0.216, 6, 36, -29.51, -19.46, 0.01129, 33, -22.22, 1.72, 0.06723, 31, -23.63, 2.88, 0.04612, 2, -8.05, 11.2, 0.45239, 6, -64.29, 12.07, 0.20698, 7, -101.2, 8.75, 0.216, 6, 36, -28.01, -16.05, 0.01641, 33, -21.84, 5.42, 0.07636, 31, -25.26, 6.23, 0.03864, 2, -4.33, 11.34, 0.47697, 6, -64.62, 8.37, 0.17562, 7, -101.52, 5.05, 0.216, 5, 36, -23.7, -14.94, 0.03833, 33, -18.09, 7.8, 0.11477, 31, -23.33, 10.23, 0.03525, 2, -1.45, 7.95, 0.68365, 6, -68.33, 5.92, 0.128, 6, 36, -19.78, -6.96, 0.07806, 33, -16.81, 16.6, 0.06824, 31, -26.88, 18.38, 0.00407, 2, 7.44, 7.91, 0.54581, 6, -69.47, -2.89, 0.08781, 7, -106.37, -6.21, 0.216, 6, 36, -15.98, 3.43, 0.1265, 33, -16.38, 27.65, 0.01981, 31, -32.35, 28, 1e-05, 2, 18.44, 9.02, 0.53106, 6, -69.72, -13.95, 0.10662, 7, -106.62, -17.27, 0.216, 5, 36, -11.31, 16.06, 0.14612, 33, -15.82, 41.1, 0.00019, 2, 31.85, 10.34, 0.53106, 6, -70.06, -27.41, 0.10662, 7, -106.97, -30.73, 0.216, 5, 36, -5.16, 15.42, 0.1461, 37, -21.51, 17.94, 0.00021, 2, 33.96, 4.52, 0.53106, 6, -76.09, -28.79, 0.10662, 7, -112.99, -32.11, 0.216, 5, 36, 0.48, 14.88, 0.14315, 37, -15.96, 16.78, 0.00317, 2, 35.94, -0.79, 0.53106, 6, -81.61, -30.1, 0.10662, 7, -118.51, -33.42, 0.216, 5, 36, 15.09, 16.09, 0.393, 37, -1.31, 16.34, 0.27952, 38, -19.7, 18.86, 0.00486, 6, -95.05, -35.96, 0.10662, 7, -131.95, -39.28, 0.216, 5, 36, 30, 18.76, 0.03373, 37, 13.8, 17.32, 0.43682, 38, -4.58, 18.01, 0.20683, 6, -108.3, -43.3, 0.10662, 7, -145.2, -46.62, 0.216, 4, 37, 28.77, 21.61, 0.08488, 38, 10.79, 20.45, 0.59249, 6, -120, -53.56, 0.10662, 7, -156.9, -56.88, 0.216, 4, 37, 36.08, 21.2, 0.03499, 38, 18, 19.17, 0.64238, 6, -126.79, -56.31, 0.10662, 7, -163.69, -59.63, 0.216, 4, 37, 39.92, 7.73, 0.00037, 38, 20.18, 5.33, 0.67701, 6, -136, -45.77, 0.10662, 7, -172.91, -49.09, 0.216, 5, 37, 40.79, -6.93, 0.0003, 38, 19.28, -9.33, 0.66118, 35, 27.81, 36.12, 0.03471, 6, -143.05, -32.87, 0.08781, 7, -179.95, -36.2, 0.216, 6, 37, 39.91, -18.13, 0.01716, 38, 17.05, -20.34, 0.58141, 34, 45.93, 27.68, 0, 35, 28.73, 24.93, 0.09762, 6, -147.03, -22.38, 0.08781, 7, -183.94, -25.7, 0.216, 6, 37, 29.33, -21.38, 0.09487, 38, 6.15, -22.29, 0.3504, 34, 36.54, 21.81, 0.00986, 35, 18.8, 20.03, 0.24106, 6, -138.85, -14.92, 0.08781, 7, -175.75, -18.24, 0.216, 6, 37, 25.88, -35.04, 0.02922, 38, 1.08, -35.43, 0.04195, 34, 36.72, 7.73, 0.00051, 35, 17.57, 6, 0.61197, 6, -141.56, -1.1, 0.10035, 7, -178.46, -4.42, 0.216, 4, 34, 37.17, -6.43, 0.00423, 35, 16.6, -8.13, 0.7486, 32, 25.28, 43.65, 0.03117, 7, -181.45, 9.43, 0.216, 5, 34, 35.09, -13.97, 0.03903, 35, 13.78, -15.42, 0.46275, 32, 28.73, 36.63, 0.07525, 6, -143.86, 20.54, 0.20698, 7, -180.76, 17.22, 0.216, 4, 34, 27.39, -25.54, 0.14812, 35, 4.96, -26.17, 0.29388, 32, 30.64, 22.86, 0.34201, 7, -175.27, 29.99, 0.216, 5, 34, 17.44, -38.36, 0.04322, 35, -6.22, -37.93, 0.04634, 32, 31.68, 6.67, 0.59409, 6, -130.88, 47.71, 0.10035, 7, -167.78, 44.38, 0.216, 5, 34, 8.8, -45.79, 0.00066, 35, -15.56, -44.46, 0.00184, 32, 30.14, -4.62, 0.70623, 6, -123.72, 56.57, 0.07526, 7, -160.62, 53.24, 0.216, 3, 32, 25, -10.8, 0.70874, 6, -116.12, 59.2, 0.07526, 7, -153.03, 55.88, 0.216, 4, 31, 32.66, -9.64, 0.0054, 32, 12.61, -9.59, 0.70333, 6, -106.14, 51.74, 0.07526, 7, -143.05, 48.42, 0.216, 3, 31, 19.53, -10.21, 0.44693, 32, -0.52, -10.22, 0.33707, 7, -131.49, 42.17, 0.216, 4, 31, 6.25, -10.86, 0.70771, 32, -13.79, -10.92, 0.00103, 6, -82.87, 39.22, 0.07526, 7, -119.77, 35.9, 0.216, 5, 33, -13.41, -20.82, 0.0019, 31, -4.25, -11.61, 0.19655, 2, -29.14, -0.66, 0.51029, 6, -73.47, 34.47, 0.07526, 7, -110.37, 31.15, 0.216, 5, 33, -21.74, -18.97, 0.00753, 31, -12.31, -14.43, 0.14556, 2, -28.47, 7.85, 0.55565, 6, -65.11, 32.75, 0.07526, 7, -102.01, 29.43, 0.216, 4, 36, -22.49, -26.13, 0.03483, 33, -13.49, -2.47, 0.36708, 31, -14, 3.93, 0.33409, 6, -73.09, 16.12, 0.264, 4, 36, -12.78, -31.95, 0.00052, 33, -2.47, -5.04, 0.44665, 31, -3.29, 7.57, 0.28883, 6, -84.16, 18.51, 0.264, 5, 33, 12.96, -7.7, 0.49378, 34, -8.49, -6.4, 0.0194, 31, 11.22, 13.45, 0.19509, 32, -8.92, 13.41, 0.02772, 6, -99.62, 20.93, 0.264, 6, 33, 32.31, -9.9, 0.00862, 34, 10.24, -11.74, 0.47848, 35, -10.72, -10.72, 0.04903, 31, 28.82, 21.79, 0.00454, 32, 8.64, 21.83, 0.19533, 6, -119.01, 22.81, 0.264, 4, 34, 24.52, -12.75, 0.16865, 35, 3.38, -13.16, 0.43183, 32, 20.01, 30.53, 0.13552, 6, -133.24, 21.24, 0.264, 4, 36, -0.93, -6.88, 0.69398, 33, 1.11, 22.47, 0.1901, 34, -15.24, 25.31, 0.00391, 6, -87.29, -9.05, 0.112, 6, 36, 19, -4.76, 0.32311, 37, 0.24, -4.81, 0.49743, 33, 19.43, 30.61, 0.02447, 34, 4.16, 30.34, 0.03686, 35, -12.56, 31.75, 0.00612, 6, -105.47, -17.49, 0.112, 5, 37, 28.05, -2.77, 0.01056, 38, 7.14, -3.66, 0.85822, 34, 30.52, 39.47, 0.00082, 35, 14.57, 38.2, 0.01839, 6, -129.76, -31.2, 0.112, 5, 36, -7.16, -19.67, 0.18843, 33, -0.89, 8.38, 0.68277, 34, -19.52, 11.74, 8e-05, 31, -9.03, 19.8, 0.00073, 6, -85.52, 5.06, 0.128, 5, 36, 5.18, -23.01, 0.15349, 37, -15.55, -21.4, 0.01933, 33, 11.88, 8.99, 0.63469, 34, -6.82, 10.25, 0.06449, 6, -98.27, 4.25, 0.128, 7, 36, 25.73, -27.3, 0.02316, 37, 4.39, -27.97, 0.11406, 38, -19.4, -25.81, 0.00998, 33, 32.75, 11.23, 0.02542, 34, 14.13, 9.03, 0.56971, 35, -4.77, 9.56, 0.12967, 6, -119.11, 1.68, 0.128, 4, 36, -27.01, -36.79, 0.00097, 33, -14.52, -14.01, 0.06899, 31, -8.79, -6.41, 0.80204, 6, -72.25, 27.68, 0.128, 2, 31, 3.17, -3.73, 0.872, 6, -83.89, 31.52, 0.128, 4, 33, 10.78, -21.77, 0.00295, 34, -12.94, -19.92, 0.00078, 31, 16.8, 0.35, 0.86827, 6, -97.68, 35.02, 0.128, 5, 33, 26.63, -28.89, 0.00118, 34, 1.52, -29.54, 0.02454, 35, -21.18, -27.56, 0.00463, 32, 13.91, 2.72, 0.84165, 6, -113.63, 41.89, 0.128, 6, 36, 39.04, -29.73, 5e-05, 37, 17.35, -31.88, 0.06769, 38, -7.01, -31.26, 0.05149, 34, 27.66, 8.58, 0.04491, 35, 8.64, 7.76, 0.70786, 6, -132.5, -0.31, 0.128, 6, 37, 21.79, -20.45, 0.21309, 38, -1.22, -20.45, 0.27408, 33, 45.51, 25.24, 9e-05, 34, 29.01, 20.77, 0.04844, 35, 11.2, 19.75, 0.3523, 6, -131.64, -12.54, 0.112, 7, 36, 26.67, -17.35, 0.05707, 37, 6.44, -18.19, 0.37713, 38, -16.18, -16.36, 0.02737, 33, 30.59, 20.98, 0.0408, 34, 13.6, 19.01, 0.26105, 35, -4.3, 19.53, 0.12458, 6, -116.79, -8.04, 0.112], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 6, 62, 62, 64, 64, 66, 66, 68, 68, 70, 12, 72, 72, 74, 74, 76, 78, 80, 80, 82, 84, 86, 86, 88, 88, 90, 36, 94, 94, 96], "width": 116, "height": 80}}, "b2": {"b2": {"type": "mesh", "uvs": [0.3389, 0.20267, 0.34048, 0.09829, 0.39273, 0.0158, 0.46557, 0, 0.58115, 0.01748, 0.6144, 0.09324, 0.61598, 0.14206, 0.70307, 0.19594, 0.85665, 0.27338, 0.96748, 0.35587, 1, 0.50739, 0.90732, 0.62524, 0.76957, 0.71279, 0.7569, 0.8441, 0.61915, 0.96195, 0.44973, 1, 0.27557, 1, 0.11723, 0.97205, 0.02698, 0.89124, 0, 0.77339, 0.06023, 0.68753, 0.04915, 0.57474, 0.0349, 0.42995, 0.0539, 0.31379, 0.17582, 0.26496, 0.25815, 0.23803, 0.42178, 0.10538, 0.40831, 0.21565, 0.33827, 0.38464, 0.27497, 0.58227, 0.24265, 0.79852, 0.59148, 0.32593, 0.51471, 0.49062, 0.48373, 0.68824, 0.477, 0.86582, 0.19955, 0.34454, 0.14433, 0.47343, 0.12951, 0.6639, 0.12008, 0.82286, 0.25073, 0.92024, 0.77331, 0.4362, 0.68307, 0.58943, 0.63593, 0.77417, 0.0514, 0.79708], "triangles": [14, 42, 13, 17, 18, 38, 15, 16, 39, 17, 39, 16, 15, 34, 14, 35, 24, 25, 23, 24, 35, 35, 25, 28, 36, 22, 23, 35, 36, 23, 21, 22, 36, 28, 36, 35, 37, 21, 36, 40, 7, 8, 40, 8, 9, 31, 7, 40, 32, 31, 40, 40, 9, 10, 41, 32, 40, 11, 40, 10, 41, 40, 11, 12, 41, 11, 26, 2, 3, 1, 2, 26, 0, 1, 26, 27, 0, 26, 26, 5, 6, 5, 26, 4, 31, 6, 7, 28, 0, 27, 26, 3, 4, 6, 27, 26, 31, 27, 6, 31, 28, 27, 32, 28, 31, 29, 28, 32, 29, 36, 28, 28, 25, 0, 37, 36, 29, 20, 21, 37, 33, 29, 32, 33, 32, 41, 42, 33, 41, 42, 41, 12, 43, 19, 20, 30, 37, 29, 30, 29, 33, 38, 20, 37, 38, 37, 30, 43, 20, 38, 13, 42, 12, 34, 30, 33, 34, 33, 42, 18, 43, 38, 39, 30, 34, 38, 30, 39, 14, 34, 42, 17, 38, 39, 34, 15, 39, 18, 19, 43], "vertices": [5, 4, 27.73, 8.7, 0.06638, 5, 6.92, 7.5, 0.49922, 15, 16.63, -12.22, 0.0552, 6, -18.28, -1.09, 0.1552, 7, -55.18, -4.41, 0.224, 3, 5, 15.16, 7.79, 0.77472, 15, 24.65, -10.33, 0.00128, 7, -46.98, -5.26, 0.224, 3, 5, 21.9, 3.74, 0.6208, 6, -3.97, -6.89, 0.1552, 7, -40.87, -10.21, 0.224, 4, 5, 23.46, -2.3, 0.56181, 16, 22.66, 35.43, 0.00312, 6, -3.27, -13.09, 0.21107, 7, -40.17, -16.41, 0.224, 4, 5, 22.58, -12.07, 0.59523, 16, 23.7, 25.68, 0.02557, 6, -5.49, -22.64, 0.1552, 7, -42.4, -25.96, 0.224, 5, 4, 41.78, -11.62, 0.00185, 5, 16.75, -15.17, 0.55921, 16, 18.58, 21.51, 0.05974, 6, -11.7, -24.9, 0.1552, 7, -48.6, -28.22, 0.224, 5, 4, 38.07, -12.69, 0.01315, 5, 12.9, -15.5, 0.49022, 16, 14.87, 20.43, 0.11743, 6, -15.55, -24.69, 0.1552, 7, -52.46, -28.01, 0.224, 5, 4, 35.74, -20.83, 0.03427, 5, 9.03, -23.02, 0.25372, 16, 12.54, 12.3, 0.33281, 6, -20.44, -31.61, 0.1552, 7, -57.34, -34.93, 0.224, 5, 4, 32.96, -34.83, 0.00054, 5, 3.59, -36.22, 0.02133, 16, 9.77, -1.71, 0.62376, 6, -27.66, -43.92, 0.13037, 7, -64.56, -47.24, 0.224, 4, 3, 50.81, -49.34, 0.00016, 16, 5.73, -12.33, 0.7448, 6, -34.97, -52.62, 0.03104, 7, -71.87, -55.94, 0.224, 5, 3, 39.15, -53.18, 0.01559, 4, 17.99, -51.04, 0.00062, 16, -5.21, -17.91, 0.72875, 6, -47.13, -54.29, 0.03104, 7, -84.04, -57.61, 0.224, 5, 3, 29.15, -46.31, 0.07715, 4, 7.06, -45.77, 0.02522, 16, -16.14, -12.64, 0.64259, 6, -55.72, -45.72, 0.03104, 7, -92.63, -49.04, 0.224, 5, 3, 21.17, -35.44, 0.29416, 4, -2.48, -36.24, 0.07635, 16, -25.68, -3.12, 0.37445, 6, -61.6, -33.59, 0.03104, 7, -98.5, -36.91, 0.224, 6, 3, 10.74, -35.36, 0.26468, 4, -12.8, -37.75, 0.02891, 16, -36, -4.63, 0.12359, 2, 36.24, 9.09, 0.32778, 6, -71.84, -31.62, 0.03104, 7, -108.74, -34.94, 0.224, 3, 2, 25, -0.62, 0.70771, 6, -80.1, -19.27, 0.06829, 7, -117, -22.6, 0.224, 3, 2, 10.89, -4.13, 0.6208, 6, -81.84, -4.83, 0.1552, 7, -118.75, -8.16, 0.224, 3, 2, -3.73, -4.65, 0.56493, 6, -80.56, 9.74, 0.21107, 7, -117.46, 6.42, 0.224, 3, 2, -17.1, -2.92, 0.6208, 6, -77.19, 22.79, 0.1552, 7, -114.1, 19.47, 0.224, 3, 2, -24.9, 3.19, 0.74496, 6, -70.17, 29.79, 0.03104, 7, -107.07, 26.46, 0.224, 5, 3, 10.32, 28.47, 0.36604, 15, -34.06, 4.33, 0.05114, 2, -27.5, 12.42, 0.32778, 6, -60.7, 31.23, 0.03104, 7, -97.6, 27.91, 0.224, 5, 3, 17.55, 24.07, 0.56752, 4, -15.14, 22.01, 0.00049, 15, -26.24, 1.09, 0.17695, 6, -54.38, 25.59, 0.03104, 7, -91.29, 22.27, 0.224, 5, 3, 26.34, 25.83, 0.33397, 4, -6.73, 25.1, 0.00847, 15, -17.83, 4.18, 0.40253, 6, -45.43, 25.74, 0.03104, 7, -82.33, 22.42, 0.224, 4, 3, 37.61, 28.1, 0.07411, 15, -7.04, 8.14, 0.67085, 6, -33.93, 25.93, 0.03104, 7, -70.83, 22.61, 0.224, 4, 3, 46.9, 27.37, 0.00262, 15, 2.25, 8.84, 0.74234, 6, -24.93, 23.53, 0.03104, 7, -61.83, 20.21, 0.224, 4, 4, 19.6, 20.78, 0.07996, 5, 1.3, 20.92, 0.0541, 15, 8.5, -0.15, 0.64194, 7, -58.88, 9.67, 0.224, 5, 4, 23.36, 14.59, 0.16457, 5, 3.78, 14.13, 0.17961, 15, 12.26, -6.33, 0.27663, 6, -20.47, 5.92, 0.1552, 7, -57.37, 2.59, 0.224, 2, 5, 14.96, 0.94, 0.728, 6, -11.24, -8.7, 0.272, 4, 4, 28.16, 2.8, 0.00797, 5, 6.2, 1.62, 0.71356, 15, 17.06, -18.13, 0.00647, 6, -19.81, -6.8, 0.272, 4, 4, 13.78, 5.23, 0.6052, 5, -7.44, 6.81, 0.01889, 15, 2.68, -15.69, 0.10391, 6, -32.6, 0.23, 0.272, 4, 3, 27.53, 6.89, 0.46245, 4, -2.66, 6.56, 0.16263, 15, -13.76, -14.36, 0.10292, 6, -47.68, 6.89, 0.272, 3, 3, 10.27, 7.99, 0.7025, 15, -30.99, -15.91, 0.0255, 6, -64.46, 11.1, 0.272, 5, 3, 50.19, -17.67, 0.0059, 4, 23.48, -14.25, 0.24785, 5, -1.71, -14.19, 0.27803, 16, 0.29, 18.87, 0.26822, 6, -29.84, -21.37, 0.2, 5, 3, 36.63, -12.48, 0.09944, 4, 9.29, -11.19, 0.55203, 5, -15.03, -8.43, 0.01184, 16, -13.91, 21.94, 0.1367, 6, -42.24, -13.8, 0.2, 4, 3, 20.84, -11.35, 0.60493, 4, -6.49, -12.49, 0.11556, 16, -29.68, 20.64, 0.07951, 6, -57.56, -9.84, 0.2, 4, 3, 6.82, -12.11, 0.74164, 4, -20.23, -15.38, 0.00917, 16, -43.42, 17.75, 0.04919, 6, -71.49, -8.05, 0.2, 5, 3, 45.63, 14.97, 0.00016, 4, 13.99, 17.3, 0.1253, 5, -4.88, 18.61, 0.03235, 15, 2.89, -3.62, 0.64219, 6, -28.42, 11.56, 0.2, 4, 3, 35.06, 18.63, 0.14639, 4, 2.99, 19.31, 0.06876, 15, -8.12, -1.62, 0.58485, 6, -38.16, 17.07, 0.2, 4, 3, 19.96, 18.45, 0.57901, 4, -11.91, 16.83, 0.0075, 15, -23.01, -4.09, 0.21349, 6, -53.03, 19.63, 0.2, 3, 3, 7.38, 18.06, 0.73408, 15, -35.38, -6.4, 0.06592, 6, -65.48, 21.52, 0.2, 3, 3, 0.76, 6.41, 0.72151, 15, -40.15, -18.93, 0.0065, 6, -74.1, 11.26, 0.272, 5, 3, 42.95, -33.7, 0.02785, 4, 18.78, -31.2, 0.05114, 5, -9.62, -29.9, 0.00964, 16, -4.42, 1.93, 0.74337, 6, -39.86, -35.82, 0.168, 5, 3, 30.19, -27.29, 0.22673, 4, 5.18, -26.81, 0.17408, 5, -22.1, -22.95, 0.0015, 16, -18.01, 6.31, 0.42969, 6, -51.25, -27.2, 0.168, 4, 3, 15.29, -24.72, 0.54229, 4, -9.94, -26.55, 0.07499, 16, -33.13, 6.58, 0.21472, 6, -65.44, -21.98, 0.168, 2, 3, 8.87, 23.99, 0.888, 15, -34.82, -0.31, 0.112], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 6, 52, 52, 54, 54, 56, 56, 58, 58, 60, 14, 62, 62, 64, 64, 66, 66, 68, 50, 70, 70, 72, 72, 74, 74, 76, 80, 82, 82, 84], "width": 84, "height": 79}}, "sd": {"sd": {"x": 0.5, "y": -2.24, "width": 125, "height": 50}}, "tou1": {"tou1": {"type": "mesh", "uvs": [0.44237, 0.1879, 0.47309, 0.12538, 0.52625, 0.06676, 0.59714, 0.08239, 0.63022, 0.12538, 0.64203, 0.05634, 0.60304, 0, 0.50735, 0, 0.40339, 0.01075, 0.32542, 0.06936, 0.30415, 0.15273, 0.33014, 0.22437, 0.24645, 0.19098, 0.14081, 0.22401, 0.08405, 0.3457, 0.12662, 0.46566, 0.09193, 0.54563, 0.05251, 0.58735, 0, 0.58387, 0, 0.6256, 0.05409, 0.67601, 0.1077, 0.67949, 0.1077, 0.7525, 0.05093, 0.80813, 0.09824, 0.86029, 0.15027, 0.90027, 0.18969, 0.87246, 0.23226, 0.93504, 0.27641, 1, 0.27798, 0.94026, 0.35209, 0.97503, 0.47035, 1, 0.58545, 0.97503, 0.65798, 0.92983, 0.69898, 0.98546, 0.71001, 0.92113, 0.792, 0.89332, 0.8535, 0.80466, 0.96544, 0.81161, 0.92445, 0.73686, 0.91026, 0.6708, 0.97017, 0.64124, 1, 0.603, 0.93864, 0.56997, 0.89134, 0.50043, 0.95283, 0.4361, 0.96387, 0.3005, 0.88188, 0.18229, 0.76047, 0.17534, 0.69424, 0.21532, 0.58703, 0.17186, 0.49715, 0.18055, 0.38902, 0.26413, 0.45182, 0.28755, 0.42966, 0.2295, 0.40657, 0.56334, 0.40958, 0.68741, 0.41685, 0.82127, 0.43707, 0.92666, 0.22934, 0.75435, 0.26381, 0.83796, 0.29484, 0.91905, 0.75447, 0.6872, 0.72, 0.79109, 0.67633, 0.89371, 0.22819, 0.691, 0.28909, 0.57571, 0.57751, 0.55164, 0.70736, 0.60865, 0.5867, 0.68213, 0.5959, 0.79869, 0.59015, 0.91145, 0.31093, 0.71127, 0.3362, 0.82023, 0.36608, 0.92158, 0.21877, 0.50772, 0.15796, 0.57476, 0.13236, 0.64709, 0.14836, 0.83057, 0.18037, 0.78117, 0.17557, 0.69825, 0.05677, 0.61983, 0.10586, 0.61532, 0.94736, 0.61231, 0.88735, 0.60931, 0.7687, 0.56119, 0.89963, 0.7687, 0.83689, 0.71006, 0.79188, 0.63036, 0.22725, 0.33262, 0.36363, 0.39277, 0.56548, 0.36119, 0.67596, 0.37623, 0.80825, 0.34014, 0.18224, 0.29803, 0.38, 0.21533, 0.37727, 0.15367, 0.40319, 0.09052, 0.47683, 0.0424, 0.54503, 0.03187, 0.60504, 0.05743], "triangles": [99, 7, 6, 98, 8, 7, 98, 7, 99, 100, 6, 5, 99, 6, 100, 2, 98, 99, 3, 99, 100, 2, 99, 3, 1, 98, 2, 5, 3, 100, 4, 3, 5, 97, 9, 8, 97, 8, 98, 1, 97, 98, 0, 97, 1, 96, 9, 97, 10, 9, 96, 96, 97, 0, 95, 96, 0, 11, 10, 96, 11, 96, 95, 54, 95, 0, 51, 54, 0, 81, 17, 82, 19, 18, 17, 19, 17, 81, 20, 19, 81, 81, 82, 21, 20, 81, 21, 82, 16, 76, 17, 16, 82, 77, 82, 76, 21, 82, 77, 24, 23, 22, 78, 24, 22, 24, 78, 25, 22, 21, 80, 78, 22, 79, 25, 78, 26, 52, 95, 54, 11, 95, 52, 53, 54, 51, 52, 54, 53, 94, 13, 12, 89, 94, 12, 89, 12, 11, 93, 48, 47, 93, 47, 46, 49, 48, 93, 14, 13, 94, 91, 51, 50, 91, 50, 49, 92, 91, 49, 53, 51, 91, 93, 92, 49, 90, 11, 52, 90, 52, 53, 89, 11, 90, 90, 53, 91, 45, 93, 46, 15, 14, 94, 15, 94, 89, 44, 93, 45, 75, 15, 89, 75, 89, 90, 67, 91, 92, 55, 90, 91, 85, 92, 93, 85, 93, 44, 68, 67, 92, 67, 55, 91, 66, 75, 90, 76, 15, 75, 16, 15, 76, 55, 66, 90, 85, 68, 92, 84, 85, 44, 84, 44, 43, 83, 43, 42, 84, 43, 83, 88, 85, 84, 68, 85, 88, 41, 83, 42, 40, 84, 83, 40, 83, 41, 69, 67, 68, 62, 68, 88, 56, 55, 67, 56, 67, 69, 66, 55, 56, 66, 76, 75, 72, 65, 66, 65, 76, 66, 80, 77, 76, 65, 80, 76, 21, 77, 80, 87, 88, 84, 87, 84, 40, 62, 88, 87, 56, 72, 66, 87, 40, 39, 59, 65, 72, 80, 65, 59, 86, 87, 39, 79, 80, 59, 22, 80, 79, 62, 69, 68, 63, 69, 62, 70, 69, 63, 37, 87, 86, 86, 39, 38, 37, 86, 38, 73, 72, 56, 60, 59, 72, 57, 56, 69, 57, 69, 70, 73, 56, 57, 73, 60, 72, 79, 59, 60, 26, 79, 60, 78, 79, 26, 63, 87, 37, 87, 63, 62, 36, 63, 37, 64, 70, 63, 35, 64, 63, 71, 57, 70, 71, 70, 64, 61, 60, 73, 27, 26, 60, 36, 35, 63, 74, 73, 57, 61, 73, 74, 58, 57, 71, 74, 57, 58, 33, 71, 64, 33, 64, 35, 61, 27, 60, 29, 27, 61, 30, 61, 74, 29, 61, 30, 71, 31, 58, 32, 71, 33, 34, 33, 35, 28, 27, 29, 32, 31, 71, 30, 74, 58, 31, 30, 58], "vertices": [5, 5, 160.86, 19.82, 0.02209, 13, -124.99, 1.5, 0.0014, 8, 2.96, -10.19, 0.91145, 9, -2.21, -18.18, 0.0595, 10, -8.04, -29.18, 0.00556, 4, 5, 173.38, 13.86, 6e-05, 8, 15.45, -16.23, 0.21208, 9, 10.51, -12.66, 0.57924, 10, -1.59, -16.91, 0.20862, 1, 10, 9.7, -5.34, 1, 1, 10, 24.98, -8.2, 1, 1, 10, 32.19, -16.49, 1, 1, 10, 34.57, -3, 1, 1, 10, 26.05, 7.88, 1, 2, 9, 31.39, 2.02, 0.28591, 10, 5.48, 7.63, 0.71409, 2, 8, 37.08, -0.23, 0.00237, 9, 12.52, 14.17, 0.99763, 2, 8, 24.89, 15.99, 0.58295, 9, -7.73, 15.53, 0.41705, 3, 11, -83.88, -15.86, 0.00693, 8, 8.44, 19.81, 0.9742, 9, -21.36, 5.56, 0.01887, 3, 5, 152.51, 43.55, 0.7383, 6, 130.91, 14.37, 0.1577, 7, 94.01, 11.05, 0.104, 3, 5, 158.08, 61.86, 0.7383, 6, 138.97, 31.72, 0.1577, 7, 102.07, 28.4, 0.104, 3, 5, 150.48, 84.21, 0.73114, 6, 134.55, 54.91, 0.16486, 7, 97.65, 51.59, 0.104, 3, 5, 126.15, 95.17, 0.73114, 6, 111.98, 69.15, 0.16486, 7, 75.08, 65.83, 0.104, 3, 5, 103.26, 84.82, 0.68544, 6, 87.88, 62.09, 0.15456, 7, 50.98, 58.77, 0.16, 3, 11, 5.1, -9.97, 0.83704, 12, 2.66, -13.99, 0.16203, 8, -70.19, 61.86, 0.00093, 2, 11, 16.61, -12.34, 0.07304, 12, 11.01, -5.73, 0.92696, 1, 12, 22.31, -6.23, 1, 1, 12, 22.18, 1.9, 1, 2, 13, -1.17, -24.94, 0.02715, 12, 10.41, 11.56, 0.97285, 4, 13, -6.2, -14.55, 0.35485, 14, -3.35, -22.19, 0.00059, 11, 24.67, 7.63, 0.16997, 12, -1.13, 12.06, 0.47459, 4, 13, 6.22, -7.6, 0.60881, 14, 1.47, -8.79, 0.36154, 11, 36.4, 15.7, 0.0052, 12, -1.35, 26.29, 0.02446, 1, 14, 16.63, -2.72, 1, 3, 5, 26.1, 86.94, 0.00856, 13, 25.56, 0.87, 0.07924, 14, 10.51, 10.29, 0.9122, 3, 5, 18.89, 75.37, 0.185, 13, 26.91, 14.44, 0.3678, 14, 2.62, 21.42, 0.4472, 5, 5, 24.74, 67.18, 0.62445, 13, 18.04, 19.2, 0.29258, 14, -7.19, 19.19, 0.0826, 11, 45.66, 43.49, 0.00033, 8, -132.89, 37.92, 3e-05, 2, 5, 13.03, 57.41, 0.792, 6, -5.29, 47.49, 0.208, 2, 5, 0.87, 47.28, 0.872, 6, -18.74, 39.15, 0.128, 2, 5, 12.52, 47.54, 0.792, 6, -7.17, 37.79, 0.208, 3, 5, 6.57, 31.28, 0.67898, 6, -15.32, 22.51, 0.14502, 7, -52.22, 19.19, 0.176, 3, 5, 3.02, 5.64, 0.61965, 6, -22.4, -2.39, 0.20435, 7, -59.3, -5.71, 0.176, 3, 5, 9.16, -18.83, 0.67898, 6, -19.72, -27.47, 0.14502, 7, -56.62, -30.79, 0.176, 3, 5, 18.77, -33.94, 0.67238, 6, -12.31, -43.77, 0.15162, 7, -49.21, -47.09, 0.176, 3, 5, 8.39, -43.31, 0.84634, 6, -23.89, -51.6, 0.06566, 7, -60.79, -54.92, 0.088, 3, 5, 21.04, -45.03, 0.84634, 6, -11.6, -55.07, 0.06566, 7, -48.5, -58.39, 0.088, 2, 5, 27.37, -62.35, 0.912, 7, -44.65, -76.42, 0.088, 2, 5, 45.32, -74.66, 0.912, 7, -28.59, -91.11, 0.088, 2, 5, 45.21, -98.77, 0.912, 7, -32.05, -114.97, 0.088, 2, 5, 59.31, -89.21, 0.912, 7, -16.75, -107.47, 0.088, 2, 5, 72.02, -85.5, 0.912, 7, -3.65, -105.56, 0.088, 2, 5, 78.44, -98.07, 0.912, 7, 0.96, -118.89, 0.088, 2, 5, 86.22, -104.09, 0.912, 7, 7.82, -125.94, 0.088, 2, 5, 91.97, -90.58, 0.912, 7, 15.4, -113.36, 0.088, 3, 5, 104.98, -79.72, 0.89741, 6, 66.7, -101.1, 0.01459, 7, 29.8, -104.42, 0.088, 3, 5, 118.19, -92.28, 0.89741, 6, 78.03, -115.37, 0.01459, 7, 41.13, -118.69, 0.088, 3, 5, 144.72, -93.28, 0.89741, 6, 104.17, -120.05, 0.01459, 7, 67.26, -123.37, 0.088, 3, 5, 166.83, -74.49, 0.89741, 6, 128.68, -104.51, 0.01459, 7, 91.77, -107.84, 0.088, 3, 5, 166.84, -48.35, 0.89741, 6, 132.32, -78.63, 0.01459, 7, 95.41, -81.95, 0.088, 2, 5, 158.32, -34.53, 0.816, 6, 125.8, -63.76, 0.184, 2, 5, 165.59, -11.07, 0.824, 6, 136.26, -41.54, 0.176, 2, 5, 162.9, 8.14, 0.824, 6, 136.27, -22.15, 0.176, 2, 5, 145.42, 30.51, 0.848, 6, 122.08, 2.44, 0.152, 2, 5, 141.56, 16.79, 0.752, 6, 116.34, -10.61, 0.248, 5, 5, 152.62, 22.13, 0.50631, 13, -116.57, 3.07, 0.00222, 11, -86.86, 14.85, 0.00904, 8, -5.27, -7.84, 0.48131, 9, -9.37, -22.88, 0.00112, 2, 5, 87.35, 23.73, 0.752, 6, 63.62, 3.8, 0.248, 2, 5, 63.22, 21.83, 0.752, 6, 39.47, 5.28, 0.248, 2, 5, 37.23, 18.92, 0.752, 6, 13.33, 6.01, 0.248, 2, 5, 16.94, 13.52, 0.752, 6, -7.53, 3.48, 0.248, 3, 5, 48.18, 59.86, 0.67238, 6, 29.86, 45.03, 0.15162, 7, -7.04, 41.71, 0.176, 3, 5, 32.28, 51.62, 0.67238, 6, 12.97, 39.07, 0.15162, 7, -23.93, 35.75, 0.176, 3, 5, 16.84, 44.14, 0.67238, 6, -3.36, 33.82, 0.15162, 7, -40.27, 30.5, 0.176, 3, 5, 67.09, -52.22, 0.67238, 6, 33, -68.59, 0.15162, 7, -3.9, -71.91, 0.176, 3, 5, 46.48, -45.86, 0.67238, 6, 13.47, -59.43, 0.15162, 7, -23.43, -62.75, 0.176, 3, 5, 26.01, -37.52, 0.67238, 6, -5.64, -48.32, 0.15162, 7, -42.54, -51.64, 0.176, 3, 5, 60.51, 60.74, 0.67238, 6, 42.19, 44.19, 0.15162, 7, 5.29, 40.87, 0.176, 2, 5, 83.64, 48.83, 0.824, 6, 63.44, 29.17, 0.176, 2, 5, 91.53, -12.86, 0.824, 6, 62.67, -33.01, 0.176, 2, 5, 81.87, -41.31, 0.816, 6, 49.15, -59.84, 0.184, 2, 5, 66.22, -16.14, 0.824, 6, 37.15, -32.75, 0.176, 2, 5, 43.62, -19.29, 0.824, 6, 14.34, -32.72, 0.176, 2, 5, 21.6, -19.19, 0.824, 6, -7.46, -29.56, 0.176, 2, 5, 57.48, 42.78, 0.824, 6, 36.69, 26.82, 0.176, 2, 5, 36.54, 36.25, 0.824, 6, 15.05, 23.27, 0.176, 2, 5, 17.14, 28.81, 0.84, 6, -5.2, 18.6, 0.16, 3, 5, 96.1, 64.61, 0.67238, 6, 77.97, 43.07, 0.15162, 7, 41.07, 39.75, 0.176, 5, 5, 82.36, 76.99, 0.74332, 13, -29.3, -15.08, 0.00422, 11, 1.72, 4.94, 0.07601, 8, -75.21, 47.41, 0.00045, 7, 29.19, 53.92, 0.176, 5, 5, 67.99, 81.76, 0.00249, 13, -14.31, -13, 0.24837, 11, 16.46, 8.41, 0.50962, 12, -6.33, 5.66, 0.2394, 8, -89.56, 52.26, 0.00013, 3, 5, 32.44, 76.48, 0.02933, 13, 15.25, 7.46, 0.54739, 14, -1.6, 8.49, 0.42328, 5, 5, 42.42, 70.1, 0.65964, 13, 3.48, 8.76, 0.33202, 14, -11.33, 1.76, 0.00234, 11, 32.14, 31.74, 0.00547, 8, -115.2, 40.75, 0.00052, 5, 5, 58.51, 71.97, 0.87021, 13, -10.13, -0.03, 0.09199, 11, 19.41, 21.72, 0.03309, 12, -15.78, 15.49, 0.00414, 8, -99.1, 42.52, 0.00057, 2, 13, -11.02, -29.78, 0.00056, 12, 10, 0.59, 0.99944, 3, 5, 73.89, 87.77, 2e-05, 11, 14.59, 0.2, 0.59691, 12, -0.54, -0.45, 0.40307, 1, 5, 83.82, -92.88, 1, 1, 5, 83.74, -79.96, 1, 3, 5, 91.79, -54, 0.67238, 6, 57.21, -73.79, 0.15162, 7, 20.31, -77.11, 0.176, 1, 5, 52.83, -84.21, 1, 1, 5, 63.56, -70.14, 1, 2, 5, 78.58, -59.68, 0.912, 6, 43.34, -77.58, 0.088, 3, 5, 130.29, 64.56, 0.67898, 6, 111.82, 38.26, 0.14502, 7, 74.92, 34.94, 0.176, 3, 5, 120.09, 34.67, 0.61965, 6, 97.57, 10.08, 0.20435, 7, 60.66, 6.76, 0.176, 3, 5, 128.48, -8.36, 0.67898, 6, 99.89, -33.69, 0.14502, 7, 62.99, -37.01, 0.176, 3, 5, 126.78, -32.23, 0.67238, 6, 94.89, -57.09, 0.15162, 7, 57.99, -60.42, 0.176, 3, 5, 135.28, -60.27, 0.89741, 6, 99.4, -86.05, 0.01459, 7, 62.5, -89.37, 0.088, 3, 5, 136.52, 74.57, 0.73114, 6, 119.39, 47.31, 0.16486, 7, 82.49, 43.99, 0.104, 4, 5, 154.83, 32.94, 0.15172, 13, -113.78, -7.6, 0.00086, 11, -83.08, 4.49, 0.02726, 8, -3, 2.96, 0.82016, 2, 11, -92.65, -2.81, 0.00088, 8, 8.98, 4.1, 0.99912, 2, 8, 21.54, -0.9, 0.06239, 9, 2.88, 1.96, 0.93761, 1, 10, -0.98, -0.72, 1, 2, 9, 33.93, -7.87, 0.0034, 10, 13.65, 1.51, 0.9966, 1, 10, 26.62, -3.31, 1], "hull": 52, "edges": [0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 0, 102, 22, 104, 104, 106, 106, 108, 4, 6, 110, 112, 112, 114, 114, 116, 118, 120, 120, 122, 124, 126, 126, 128, 130, 132, 134, 136, 138, 140, 140, 142, 144, 146, 146, 148, 150, 152, 152, 154, 156, 158, 158, 160, 162, 164, 166, 168, 168, 170, 172, 174, 174, 176, 178, 180, 180, 182, 182, 184, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200], "width": 215, "height": 195}}, "tou": {"tou": {"x": 40.77, "y": 21.23, "rotation": -87.04, "width": 81, "height": 73}}, "s1": {"s1": {"type": "mesh", "uvs": [0.14924, 0.11872, 0.23311, 0.04385, 0.34164, 0, 0.47731, 0.01077, 0.49951, 0.11872, 0.49951, 0.23538, 0.60557, 0.32418, 0.74371, 0.42691, 0.90897, 0.50526, 1, 0.58536, 0.91391, 0.72813, 0.74124, 0.85524, 0.69684, 0.96319, 0.50691, 0.97886, 0.36877, 1, 0.22818, 0.951, 0.13938, 0.87091, 0.06538, 0.72465, 0.01358, 0.55053, 0, 0.37642, 0.04564, 0.20926, 0.25586, 0.20675, 0.3496, 0.39157, 0.42072, 0.56498, 0.49183, 0.7247, 0.55648, 0.87073, 0.3849, 0.47764], "triangles": [13, 14, 25, 13, 25, 12, 25, 14, 15, 12, 25, 11, 25, 15, 24, 25, 24, 11, 15, 16, 24, 16, 17, 24, 11, 24, 10, 24, 8, 10, 10, 8, 9, 17, 23, 24, 24, 7, 8, 24, 23, 7, 23, 18, 26, 18, 23, 17, 23, 26, 7, 26, 18, 22, 21, 22, 19, 19, 20, 21, 22, 18, 19, 26, 6, 7, 26, 22, 6, 22, 5, 6, 22, 21, 5, 21, 4, 5, 20, 0, 21, 4, 2, 3, 2, 4, 1, 0, 1, 21, 4, 21, 1], "vertices": [1, 25, -5.11, -4.71, 1, 1, 25, -9.44, 2.15, 1, 1, 25, -10.79, 9.54, 1, 1, 25, -7.22, 16.91, 1, 1, 25, 1.88, 15.11, 1, 2, 25, 11.23, 11.81, 0.99679, 26, -20.13, 9.78, 0.00321, 3, 25, 20.47, 15.31, 0.84761, 26, -11.31, 14.21, 0.15135, 27, -33.73, 12.95, 0.00104, 3, 25, 31.46, 20.22, 0.32308, 26, -0.88, 20.24, 0.62418, 27, -23.54, 19.36, 0.05274, 3, 25, 41.04, 27.35, 0.06152, 26, 7.9, 28.33, 0.74419, 27, -15.07, 27.78, 0.19429, 3, 25, 49.27, 30.24, 0.01788, 26, 15.79, 32.05, 0.70778, 27, -7.32, 31.8, 0.27434, 3, 25, 59, 21.33, 0.00036, 26, 26.39, 24.2, 0.52806, 27, 3.57, 24.36, 0.47158, 2, 26, 34.49, 11.61, 0.08267, 27, 12.13, 12.08, 0.91733, 2, 26, 42.79, 6.89, 1e-05, 27, 20.61, 7.67, 0.99999, 2, 26, 41.43, -4.51, 0.00322, 27, 19.68, -3.77, 0.99678, 2, 26, 41.25, -12.98, 0.05158, 27, 19.81, -12.25, 0.94842, 2, 26, 35.24, -20.22, 0.19051, 27, 14.07, -19.7, 0.80949, 3, 25, 54.99, -26.53, 0.00046, 26, 27.38, -23.82, 0.395, 27, 6.35, -23.59, 0.60454, 3, 25, 41.79, -26.58, 0.0562, 26, 14.25, -25.24, 0.71157, 27, -6.71, -25.51, 0.23223, 3, 25, 26.8, -24.59, 0.3965, 26, -0.87, -24.81, 0.57859, 27, -21.83, -25.65, 0.02491, 2, 25, 12.57, -20.44, 0.83084, 26, -15.45, -22.16, 0.16916, 2, 25, 0.08, -13.13, 0.98864, 26, -28.63, -16.19, 0.01136, 2, 25, 4.07, -1.16, 0.99984, 26, -25.9, -3.87, 0.00016, 2, 25, 20.76, -1.08, 0.99126, 26, -9.31, -2.06, 0.00874, 3, 25, 36.08, -1.96, 0.00372, 26, 6.02, -1.34, 0.99592, 27, -15.83, -1.94, 0.00036, 1, 26, 20.21, -0.36, 1, 1, 27, 11.25, 0.95, 1, 2, 25, 28.36, -1.52, 0.81795, 26, -1.7, -1.7, 0.18205], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 2, 42, 42, 44, 46, 48, 48, 50, 44, 52, 52, 46], "width": 60, "height": 85}}, "s3": {"s3": {"type": "mesh", "uvs": [0.62827, 0.00965, 0.75607, 0.04479, 0.88005, 0.13325, 0.96207, 0.23988, 0.95444, 0.33319, 0.99068, 0.3962, 0.98114, 0.55373, 0.93918, 0.71368, 0.86479, 0.87242, 0.737, 0.97906, 0.58631, 1, 0.3784, 0.98512, 0.21437, 0.9112, 0.18576, 0.8082, 0.05796, 0.68097, 0, 0.52828, 0.15333, 0.44346, 0.28303, 0.35621, 0.41655, 0.24231, 0.50239, 0.20474, 0.49285, 0.10901, 0.52337, 0.01207, 0.66344, 0.11078, 0.7032, 0.20173, 0.68995, 0.28425, 0.64488, 0.4274, 0.58391, 0.56382, 0.49113, 0.69181, 0.41955, 0.82317, 0.37714, 0.92085], "triangles": [28, 9, 10, 12, 29, 11, 10, 11, 29, 10, 29, 28, 9, 28, 8, 29, 12, 28, 12, 13, 28, 28, 27, 8, 8, 27, 7, 28, 13, 27, 13, 14, 27, 27, 26, 7, 27, 14, 16, 7, 26, 6, 26, 27, 16, 14, 15, 16, 16, 17, 26, 26, 17, 25, 26, 25, 6, 6, 25, 5, 17, 18, 25, 25, 18, 24, 25, 4, 5, 25, 24, 4, 24, 18, 19, 4, 24, 3, 24, 19, 23, 24, 23, 3, 23, 19, 22, 23, 2, 3, 0, 22, 20, 20, 21, 0, 22, 19, 20, 23, 22, 2, 22, 1, 2, 22, 0, 1], "vertices": [1, 28, -11.61, -4.14, 1, 1, 28, -9.34, 3.02, 1, 1, 28, -2.54, 10.45, 1, 1, 28, 6.03, 15.77, 1, 1, 28, 13.97, 16.16, 1, 3, 28, 19.1, 18.65, 0.99235, 29, -7.32, 17.74, 0.00607, 30, -32.02, 15, 0.00158, 3, 28, 32.47, 19.49, 0.70028, 29, 5.63, 21.22, 0.21821, 30, -19.44, 19.6, 0.08151, 3, 28, 46.23, 18.61, 0.24768, 29, 19.28, 23.09, 0.30482, 30, -5.99, 22.65, 0.4475, 3, 28, 60.06, 15.98, 0.04952, 29, 33.36, 23.25, 0.07332, 30, 8.02, 24.04, 0.87716, 3, 28, 69.77, 10.03, 0.00778, 29, 44.06, 19.34, 0.00343, 30, 19.02, 21.08, 0.98879, 2, 28, 72.36, 2.12, 0.00062, 30, 23.75, 14.23, 0.99938, 1, 30, 26.83, 3.36, 1, 2, 29, 46.92, -9.32, 0.00573, 30, 24.36, -7.22, 0.99427, 2, 29, 39.01, -13.39, 0.1211, 30, 16.85, -11.97, 0.8789, 2, 29, 30.73, -23.19, 0.54799, 30, 9.45, -22.45, 0.45201, 2, 29, 19.26, -30.02, 0.80559, 30, -1.38, -30.26, 0.19441, 2, 29, 9.92, -24.25, 0.92491, 30, -11.18, -25.32, 0.07509, 3, 28, 19.58, -19.71, 0.04638, 29, 0.76, -19.76, 0.94854, 30, -20.7, -21.65, 0.00508, 2, 28, 9.22, -13.52, 0.51947, 29, -10.62, -15.74, 0.48053, 2, 28, 5.57, -9.23, 0.8403, 29, -15.04, -12.26, 0.1597, 2, 28, -2.47, -10.56, 0.99368, 29, -22.66, -15.17, 0.00632, 1, 28, -10.83, -9.76, 1, 1, 28, -3.25, -1.38, 1, 1, 28, 4.22, 1.53, 1, 1, 28, 11.27, 1.53, 1, 1, 28, 23.62, 0.34, 1, 3, 28, 35.49, -1.76, 0.01223, 29, 12.8, 0.99, 0.98633, 30, -10.52, 0.07, 0.00143, 1, 30, 1.45, -0.45, 1, 2, 28, 58.32, -8.36, 1e-05, 30, 13.24, 0.2, 0.99999, 1, 30, 21.8, 1.23, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 54, "height": 85}}}}], "animations": {"idle": {"bones": {"all3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "all4": {"rotate": [{"angle": -0.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.39}], "translate": [{"x": 0.21, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.73, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.21}]}, "all5": {"rotate": [{"angle": -0.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.37, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -0.98}], "translate": [{"x": 0.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.93, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.67}]}, "all6": {"translate": [{"x": 1.05, "y": 2.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.71, "y": 8.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.05, "y": 2.38}]}, "all8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.94, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "all9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.94, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "all10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.94, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "all11": {"rotate": [{"angle": 12.92, "curve": 0.33, "c2": 0.32, "c3": 0.714, "c4": 0.82}, {"time": 0.5667, "angle": 1.19, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 17.13, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": 12.92}]}, "all12": {"rotate": [{"angle": 16.74, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": 17.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "angle": 8.55, "curve": 0.336, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.7, "angle": 5.57, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 1.0667, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "angle": 16.74}]}, "all13": {"rotate": [{"angle": 10.07, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 17.13, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 10.07}]}, "all14": {"rotate": [{"angle": 16.76, "curve": 0.267, "c2": 0.1, "c3": 0.652, "c4": 0.61}, {"time": 0.5667, "angle": 5.57, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 17.13, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": 16.76}]}, "all15": {"translate": [{"x": 0.45, "y": 0.59, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.91, "y": 1.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.45, "y": 0.59}]}, "all16": {"translate": [{"x": 0.92, "y": -0.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.84, "y": -0.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.92, "y": -0.35}]}, "all17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.28, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "all18": {"rotate": [{"angle": 2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.28, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.92}]}, "all19": {"rotate": [{"angle": 7.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.36}]}, "all20": {"rotate": [{"angle": 10.16, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 10.28, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": 10.16}]}, "all21": {"rotate": [{"angle": 0.71, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 10.28, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 0.71}]}, "all22": {"rotate": [{"angle": 4.67, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1333, "angle": 2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 10.28, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 4.67}]}, "all23": {"rotate": [{"angle": 8.96, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.1333, "angle": 7.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 10.28, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 8.96}]}, "all24": {"rotate": [{"angle": 9.84, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": 10.28, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 0.1333, "angle": 10.16, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 1.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": 9.84}]}, "all27": {"rotate": [{"angle": 0.84, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 5.03, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 0.84}]}, "all28": {"rotate": [{"angle": 2.95, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.2333, "angle": 1.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 5.03, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 2.95}]}, "all29": {"rotate": [{"angle": 4.83, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.2333, "angle": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 5.03, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": 4.83}]}, "all30": {"rotate": [{"angle": -0.87, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -5.25, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -0.87}]}, "all31": {"rotate": [{"angle": -3.08, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.2333, "angle": -1.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -5.25, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": -3.08}]}, "all32": {"rotate": [{"angle": -5.04, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.2333, "angle": -3.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -5.25, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": -5.04}]}, "all33": {"rotate": [{"angle": 0.92, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9, "angle": 21.51, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": 0.92}]}, "all34": {"rotate": [{"angle": 3.53, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 21.51, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.9, "angle": 6.11, "curve": 0.343, "c2": 0.37, "c3": 0.678, "c4": 0.71}, {"time": 2, "angle": 3.53}]}, "all35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 21.51, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "all36": {"rotate": [{"angle": 6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 21.51, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 6.11}]}, "all37": {"rotate": [{"angle": 15.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 21.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 15.41}]}, "all38": {"rotate": [{"angle": 2.8, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 21.51, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": 2.8}]}, "all39": {"rotate": [{"angle": 11.68, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.2, "angle": 6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 21.51, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": 11.68}]}, "all40": {"rotate": [{"angle": 20.11, "curve": 0.304, "c2": 0.22, "c3": 0.644, "c4": 0.58}, {"time": 0.2, "angle": 15.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 21.51, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 20.11}]}, "all41": {"rotate": [{"angle": -0.42}]}, "all42": {"rotate": [{"angle": -1.31}]}, "all43": {"rotate": [{"angle": 1.7}]}, "all44": {"rotate": [{"angle": -0.15}]}, "all45": {"rotate": [{"angle": 0.73}]}, "all46": {"rotate": [{"angle": -2.09}]}}}, "train": {"slots": {"tou": {"attachment": [{"time": 0.3333, "name": "tou"}]}}, "bones": {"all2": {"translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": 12.25, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": -23.36, "y": -18.95, "curve": "stepped"}, {"time": 0.5, "x": -23.36, "y": -18.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.12, "curve": "stepped"}, {"time": 0.5, "angle": 6.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "all4": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "all5": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.56, "curve": "stepped"}, {"time": 0.5333, "angle": -3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "all8": {"rotate": [{"angle": 2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 15.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 2}]}, "all9": {"rotate": [{"angle": 5.67, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 15.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 5.67}]}, "all10": {"rotate": [{"angle": 9.74, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 15.4, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 9.74}]}, "all11": {"rotate": [{"angle": -3.45, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.71}, {"time": 0.0333, "angle": -1.87, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -14.39, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -3.45}]}, "all12": {"rotate": [{"angle": -9.1, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "angle": -7.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 8, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -14.39, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -9.1}]}, "all13": {"rotate": [{"angle": -1.87, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -14.39, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.87}]}, "all14": {"rotate": [{"angle": -7.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -14.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -7.19}]}, "all17": {"rotate": [{"angle": -6.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -5.07, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2333, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -15.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -6.48}]}, "all18": {"rotate": [{"angle": -9.03, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0667, "angle": -6.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -5.07, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -15.95, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -9.03}]}, "all19": {"rotate": [{"angle": -11.93, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": -9.07, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": -5.07, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3667, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -15.95, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -11.93}]}, "all20": {"rotate": [{"angle": -14.55, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.0667, "angle": -11.94, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": -5.07, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4333, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -15.95, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -14.55}]}, "all21": {"rotate": [{"angle": -5.53, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": -5.07, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -15.95, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.6667, "angle": -5.53}]}, "all22": {"rotate": [{"angle": -7.68, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.71}, {"time": 0.0333, "angle": -6.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -5.07, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2667, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -15.95, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -7.68}]}, "all23": {"rotate": [{"angle": -10.5, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.0333, "angle": -9.07, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1667, "angle": -5.07, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -15.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -10.5}]}, "all24": {"rotate": [{"angle": -13.32, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.0333, "angle": -11.94, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2333, "angle": -5.07, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -15.95, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -13.32}]}, "all27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 6.21, "curve": "stepped"}, {"time": 0.5, "angle": 6.21, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667}]}, "all28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -96.25, "curve": "stepped"}, {"time": 0.5, "angle": -96.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667}]}, "all30": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 97.98, "curve": "stepped"}, {"time": 0.2667, "angle": 97.98, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -76.43, "curve": "stepped"}, {"time": 0.5, "angle": -76.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all31": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -113.02, "curve": "stepped"}, {"time": 0.2667, "angle": -113.02, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -0.03, "curve": "stepped"}, {"time": 0.5, "angle": -0.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all32": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -37.81, "curve": "stepped"}, {"time": 0.2667, "angle": -37.81, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -1.23, "curve": "stepped"}, {"time": 0.5, "angle": -1.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all33": {"rotate": [{"angle": -3.09, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -23.76, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -3.09}]}, "all34": {"rotate": [{"angle": -9.75, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0667, "angle": -3.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -26.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -9.75}]}, "all35": {"rotate": [{"angle": -6.83, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0667, "angle": -2.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -18.76, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -6.83}]}, "all36": {"rotate": [{"angle": -11.79, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": -6.83, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1333, "angle": -2.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -18.76, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -11.79}]}, "all37": {"rotate": [{"angle": -16.35, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.0667, "angle": -11.84, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -6.9, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -18.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -16.35}]}, "all38": {"rotate": [{"angle": -5.16, "curve": 0.36, "c2": 0.45, "c3": 0.701, "c4": 0.81}, {"time": 0.0667, "angle": -0.93, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -21.75, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -5.16}]}, "all39": {"rotate": [{"angle": -10.78, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.0667, "angle": -5.21, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "angle": -2.83, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -21.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -10.78}]}, "all40": {"rotate": [{"angle": -16.49, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": -10.86, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1, "angle": -8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -21.75, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -16.49}]}, "all41": {"rotate": [{"angle": -0.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -12.06, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -64.17, "curve": "stepped"}, {"time": 0.5, "angle": -64.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -0.42}]}, "all42": {"rotate": [{"angle": -1.31, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -1.14, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 73.63, "curve": "stepped"}, {"time": 0.5, "angle": 73.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -1.31}]}, "all43": {"rotate": [{"angle": 1.7, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 13.97, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -10.15, "curve": "stepped"}, {"time": 0.5, "angle": -10.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 1.7}]}, "all44": {"rotate": [{"angle": -0.15, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 6.69, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 32.22, "curve": "stepped"}, {"time": 0.5, "angle": 32.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -0.15}]}, "all45": {"rotate": [{"angle": 0.73, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -24.33, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -9.25, "curve": "stepped"}, {"time": 0.5, "angle": -9.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 0.73}]}, "all46": {"rotate": [{"angle": -2.09, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 16.96, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -2.09}]}, "all47": {"translate": [{"time": 0.1667, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": -41.23, "curve": "stepped"}, {"time": 0.5, "x": -41.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all48": {"translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": 7.23, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": 4.46, "curve": "stepped"}, {"time": 0.5, "x": 4.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}}}}}