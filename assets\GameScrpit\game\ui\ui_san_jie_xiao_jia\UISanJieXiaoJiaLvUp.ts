import { _decorator, isValid, Label, Node, sp, Sprite, tween, Tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import ResMgr from "../../../lib/common/ResMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
const { ccclass, property } = _decorator;

@ccclass("UISanJieXiaoJiaLvUp")
export class UISanJieXiaoJiaLvUp extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SAN_JIE_XIAO_JIA}?prefab/ui/UISanJieXiaoJiaLvUp`;
  }

  private _homeId: number;
  private _lv: number;

  public init(args: any): void {
    super.init(args);
    this._homeId = args.id;
    this._lv = args.lv;
  }

  protected onEvtShow(): void {
    this.playeAction();
  }

  private playeAction() {
    this.getNode("bg").scale = v3(0, 0, 0);
    this.getNode("tytanchuang")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_shengjichenggong_1", true);
      });
    this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_shengjichenggong", false);
    tween(this.getNode("bg"))
      .to(0.1, { scale: v3(1, 1, 1) })
      .call(() => {
        if (isValid(this.node) == false) return;

        let newdbId = this._lv * 100 + (this._homeId % 100);

        let db = JsonMgr.instance.jsonList.c_home[newdbId];
        const urlFar = `${BundleEnum.BUNDLE_G_SAN_JIE_XIAO_JIA}?images/icon/${db.iconName}`;
        ResMgr.loadImage(urlFar, this.getNode("icon").getComponent(Sprite), this);

        this.getNode("lbl_1").getComponent(Label).string = db.name + this._lv + "级";
      })
      .start();
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    Tween.stopAllByTarget(this.getNode("bg"));
  }
}
