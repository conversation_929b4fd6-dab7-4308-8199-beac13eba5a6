{"skeleton": {"hash": "lWv/kTfHdP5WkiJfCRjq8zV4PC8=", "spine": "3.8.75", "images": "./11/", "audio": ""}, "bones": [{"name": "root"}, {"name": "alljn", "parent": "root", "y": 18.58, "scaleX": 0.2093, "scaleY": 0.2093}, {"name": "hit_back", "parent": "alljn"}, {"name": "mo", "parent": "hit_back"}, {"name": "mo2", "parent": "hit_back"}, {"name": "mo3", "parent": "hit_back"}, {"name": "kuosan", "parent": "hit_back"}, {"name": "k", "parent": "hit_back"}, {"name": "rao", "parent": "hit_back"}, {"name": "fire", "parent": "hit_back", "x": -124.53, "y": -116.98}, {"name": "hit_fore", "parent": "alljn"}, {"name": "s", "parent": "hit_fore", "rotation": 0.16}, {"name": "zhu", "parent": "hit_fore"}, {"name": "zhu2", "parent": "hit_fore"}, {"name": "biu", "parent": "hit_fore"}, {"name": "tuowei", "parent": "biu"}, {"name": "fire2", "parent": "biu"}, {"name": "jian", "parent": "biu"}, {"name": "bao", "parent": "hit_fore"}, {"name": "qilang", "parent": "biu"}, {"name": "biu2", "parent": "hit_fore", "x": -98.12, "y": -33.96}, {"name": "tuowei2", "parent": "biu2"}, {"name": "fire3", "parent": "biu2"}, {"name": "jian2", "parent": "biu2"}, {"name": "qilang2", "parent": "biu2"}, {"name": "zhu3", "parent": "hit_fore", "x": -124.53, "y": -116.98}, {"name": "zhu4", "parent": "hit_fore", "x": -124.53, "y": -116.98}, {"name": "rao2", "parent": "hit_back", "x": -124.53, "y": -116.98}, {"name": "kuosan2", "parent": "hit_back", "x": -124.53, "y": -116.98}, {"name": "s2", "parent": "hit_fore", "rotation": 0.16, "x": -124.53, "y": -116.98}], "slots": [{"name": "mo(d1)", "bone": "mo"}, {"name": "mo2(d1)", "bone": "mo2"}, {"name": "mo3(d1)", "bone": "mo3"}, {"name": "k<PERSON><PERSON>(d1)", "bone": "kuosan"}, {"name": "kuo<PERSON>(d1)2", "bone": "kuosan2"}, {"name": "k(d1)", "bone": "k", "blend": "additive"}, {"name": "rao(d1)", "bone": "rao", "blend": "additive"}, {"name": "rao(d1)2", "bone": "rao2", "blend": "additive"}, {"name": "fire(d1)", "bone": "fire", "blend": "additive"}, {"name": "fire(d1)2", "bone": "fire", "blend": "additive"}, {"name": "s(d2)", "bone": "s", "blend": "additive"}, {"name": "s(d2)2", "bone": "s2", "blend": "additive"}, {"name": "zhu(d2)", "bone": "zhu", "blend": "additive"}, {"name": "zhu(d2)2", "bone": "zhu4", "blend": "additive"}, {"name": "zhu2(d2)", "bone": "zhu2", "blend": "additive"}, {"name": "zhu2(d2)2", "bone": "zhu3", "blend": "additive"}, {"name": "tuo<PERSON>(d2)", "bone": "tuowei"}, {"name": "tuowei(d2)2", "bone": "tuowei2"}, {"name": "fire2(d2)", "bone": "fire2", "blend": "additive"}, {"name": "fire2(d2)2", "bone": "fire3", "blend": "additive"}, {"name": "jian(d2)", "bone": "jian"}, {"name": "jian(d2)2", "bone": "jian2"}, {"name": "bao(d2)", "bone": "bao", "blend": "additive"}, {"name": "qilang(d2)", "bone": "qilang", "blend": "additive"}, {"name": "qilang(d2)2", "bone": "qilang2", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"tuowei(d2)": {"tuo/1_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -125, -24, -125, -24, 126, 24, 126], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 100}, "tuo/1_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -125, -24, -125, -24, 126, 24, 126], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 100}, "tuo/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -125, -24, -125, -24, 126, 24, 126], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 100}}, "kuosan(d1)2": {"kuo/1_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}}, "fire(d1)": {"dimian/1_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00027": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00029": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}}, "s(d2)2": {"s/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00027": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00029": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00033": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00035": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00037": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00039": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00041": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}}, "kuosan(d1)": {"kuo/1_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}, "kuo/1_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88, -38.5, -88, -38.5, -88, 38.5, 88, 38.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 31}}, "zhu2(d2)": {"zhu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [92, -114, -91, -114, -91, 114, 92, 114], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 73, "height": 91}}, "qilang(d2)2": {"liu2/1_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27, -119, -26, -119, -26, 120, 27, 120], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 96}, "liu2/1_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27, -119, -26, -119, -26, 120, 27, 120], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 96}, "liu2/1_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27, -119, -26, -119, -26, 120, 27, 120], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 96}}, "bao(d2)": {"bao/1_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -128, -105, -128, -105, 129, 106, 129], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 84, "height": 103}, "bao/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -128, -105, -128, -105, 129, 106, 129], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 84, "height": 103}, "bao/1_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -128, -105, -128, -105, 129, 106, 129], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 84, "height": 103}, "bao/1_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -128, -105, -128, -105, 129, 106, 129], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 84, "height": 103}, "bao/1_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -128, -105, -128, -105, 129, 106, 129], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 84, "height": 103}}, "jian(d2)": {"jian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66, -136, -65, -136, -65, 137, 66, 137], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 52, "height": 109}}, "fire2(d2)2": {"liu/1_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}, "liu/1_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}, "liu/1_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}, "liu/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}, "liu/1_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}}, "mo2(d1)": {"mo_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [21.44, -34.09, -100.56, -34.09, -100.56, 26.91, 21.44, 26.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 49, "height": 24}}, "mo3(d1)": {"mo_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [21.44, -34.09, -100.56, -34.09, -100.56, 26.91, 21.44, 26.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 49, "height": 24}}, "s(d2)": {"s/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00027": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00029": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00033": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00035": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00037": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00039": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}, "s/1_00041": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -100.5, -49, -100.5, -49, 101.5, 50, 101.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 81}}, "fire(d1)2": {"dimian/1_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00027": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00029": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}, "dimian/1_00031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -70, -94, -70, -94, 70, 95, 70], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 76, "height": 56}}, "zhu2(d2)2": {"zhu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [92, -114, -91, -114, -91, 114, 92, 114], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 73, "height": 91}}, "fire2(d2)": {"liu/1_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}, "liu/1_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}, "liu/1_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}, "liu/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}, "liu/1_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -179, -48, -179, -48, 179, 48, 179], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 143}}, "tuowei(d2)2": {"tuo/1_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -125, -24, -125, -24, 126, 24, 126], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 100}, "tuo/1_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -125, -24, -125, -24, 126, 24, 126], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 100}, "tuo/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -125, -24, -125, -24, 126, 24, 126], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 100}}, "jian(d2)2": {"jian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66, -136, -65, -136, -65, 137, 66, 137], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 52, "height": 109}}, "rao(d1)": {"rao/1_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -85, -127, -85, -127, 86, 128, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 68}, "rao/1_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -85, -127, -85, -127, 86, 128, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 68}, "rao/1_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -85, -127, -85, -127, 86, 128, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 68}, "rao/1_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -85, -127, -85, -127, 86, 128, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 68}}, "mo(d1)": {"mo_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [21.44, -34.09, -100.56, -34.09, -100.56, 26.91, 21.44, 26.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 49, "height": 24}}, "zhu(d2)2": {"zhu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [92, -114, -91, -114, -91, 114, 92, 114], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 73, "height": 91}}, "qilang(d2)": {"liu2/1_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27, -119, -26, -119, -26, 120, 27, 120], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 96}, "liu2/1_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27, -119, -26, -119, -26, 120, 27, 120], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 96}, "liu2/1_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27, -119, -26, -119, -26, 120, 27, 120], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 96}}, "zhu(d2)": {"zhu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [92, -114, -91, -114, -91, 114, 92, 114], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 73, "height": 91}}, "rao(d1)2": {"rao/1_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -85, -127, -85, -127, 86, 128, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 68}, "rao/1_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -85, -127, -85, -127, 86, 128, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 68}, "rao/1_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -85, -127, -85, -127, 86, 128, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 68}, "rao/1_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -85, -127, -85, -127, 86, 128, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 68}}, "k(d1)": {"k_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [189, -88, -188, -88, -188, 88, 189, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 151, "height": 70}}}}], "events": {"atk": {}}, "animations": {"animation": {"slots": {"rao(d1)": {"attachment": [{"time": 0.2333, "name": "rao/1_00024"}, {"time": 0.3, "name": "rao/1_00026"}, {"time": 0.3333, "name": "rao/1_00028"}, {"time": 0.4, "name": null}]}, "s(d2)2": {"attachment": [{"time": 0.2, "name": "s/1_00021"}, {"time": 0.2333, "name": "s/1_00023"}, {"time": 0.2667, "name": "s/1_00025"}, {"time": 0.3, "name": "s/1_00029"}, {"time": 0.3333, "name": "s/1_00033"}, {"time": 0.3667, "name": "s/1_00035"}, {"time": 0.4, "name": "s/1_00039"}, {"time": 0.4333, "name": null}]}, "bao(d2)": {"attachment": [{"time": 0.1333, "name": "bao/1_00021"}, {"time": 0.1667, "name": null}, {"time": 0.2, "name": "bao/1_00026"}, {"time": 0.2333, "name": null}]}, "mo3(d1)": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "mo_00023"}, {"time": 0.5333, "name": null}]}, "s(d2)": {"attachment": [{"time": 0.1333, "name": "s/1_00021"}, {"time": 0.1667, "name": "s/1_00023"}, {"time": 0.2, "name": "s/1_00025"}, {"time": 0.2333, "name": "s/1_00029"}, {"time": 0.2667, "name": "s/1_00033"}, {"time": 0.3, "name": "s/1_00035"}, {"time": 0.3333, "name": "s/1_00039"}, {"time": 0.3667, "name": null}]}, "mo2(d1)": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}], "attachment": [{"time": 0.2, "name": "mo_00023"}, {"time": 0.4333, "name": null}]}, "tuowei(d2)2": {"attachment": [{"time": 0.1333, "name": "tuo/1_00017"}, {"time": 0.2, "name": "tuo/1_00019"}, {"time": 0.2333, "name": "tuo/1_00021"}, {"time": 0.3, "name": null}]}, "tuowei(d2)": {"attachment": [{"time": 0.0667, "name": "tuo/1_00017"}, {"time": 0.1333, "name": "tuo/1_00019"}, {"time": 0.1667, "name": "tuo/1_00021"}, {"time": 0.2333, "name": null}]}, "kuosan(d1)2": {"attachment": [{"time": 0.2, "name": "kuo/1_00020"}, {"time": 0.2333, "name": "kuo/1_00022"}, {"time": 0.2667, "name": "kuo/1_00026"}, {"time": 0.3, "name": "kuo/1_00030"}, {"time": 0.3333, "name": "kuo/1_00032"}, {"time": 0.3667, "name": null}]}, "fire2(d2)2": {"attachment": [{"time": 0.1, "name": "liu/1_00015"}, {"time": 0.1333, "name": "liu/1_00017"}, {"time": 0.2, "name": "liu/1_00019"}, {"time": 0.2333, "name": "liu/1_00021"}, {"time": 0.3, "name": "liu/1_00023"}, {"time": 0.3333, "name": null}]}, "fire(d1)": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0.2, "name": "dimian/1_00021"}, {"time": 0.2333, "name": "dimian/1_00023"}, {"time": 0.2667, "name": "dimian/1_00025"}, {"time": 0.3, "name": "dimian/1_00029"}, {"time": 0.3333, "name": "dimian/1_00031"}, {"time": 0.3667, "name": null}]}, "zhu2(d2)": {"color": [{"time": 0.2, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"time": 0.1333, "name": "zhu"}]}, "zhu(d2)": {"color": [{"time": 0.2333, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}], "attachment": [{"time": 0.1333, "name": "zhu2"}]}, "k(d1)": {"color": [{"time": 0.1333, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"time": 0.1333, "name": "k_00021"}]}, "rao(d1)2": {"attachment": [{"time": 0.3, "name": "rao/1_00024"}, {"time": 0.3667, "name": "rao/1_00026"}, {"time": 0.4, "name": "rao/1_00028"}, {"time": 0.4667, "name": null}]}, "mo(d1)": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}], "attachment": [{"time": 0.1333, "name": "mo_00023"}, {"time": 0.2667, "name": null}]}, "zhu(d2)2": {"color": [{"time": 0.3, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0.2, "name": "zhu2"}]}, "fire(d1)2": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}]}, "fire2(d2)": {"attachment": [{"time": 0.0333, "name": "liu/1_00015"}, {"time": 0.0667, "name": "liu/1_00017"}, {"time": 0.1333, "name": "liu/1_00019"}, {"time": 0.1667, "name": "liu/1_00021"}, {"time": 0.2333, "name": "liu/1_00023"}, {"time": 0.2667, "name": null}]}, "zhu2(d2)2": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0.2, "name": "zhu"}]}, "kuosan(d1)": {"attachment": [{"time": 0.1333, "name": "kuo/1_00020"}, {"time": 0.1667, "name": "kuo/1_00022"}, {"time": 0.2, "name": "kuo/1_00026"}, {"time": 0.2333, "name": "kuo/1_00030"}, {"time": 0.2667, "name": "kuo/1_00032"}, {"time": 0.3, "name": null}]}, "jian(d2)": {"attachment": [{"name": "jian"}, {"time": 0.1333, "name": null}]}, "qilang(d2)2": {"attachment": [{"time": 0.1, "name": "liu2/1_00016"}, {"time": 0.1667, "name": "liu2/1_00018"}, {"time": 0.2, "name": "liu2/1_00020"}, {"time": 0.2667, "name": null}]}, "jian(d2)2": {"attachment": [{"time": 0.0667, "name": "jian"}, {"time": 0.2, "name": null}]}, "qilang(d2)": {"attachment": [{"time": 0.0333, "name": "liu2/1_00016"}, {"time": 0.1, "name": "liu2/1_00018"}, {"time": 0.1333, "name": "liu2/1_00020"}, {"time": 0.2, "name": null}]}}, "bones": {"bao": {"translate": [{"time": 0.1333, "x": 20.99, "y": 226.21}], "scale": [{"time": 0.1333, "x": 3, "y": 3}]}, "fire": {"translate": [{"time": 0.2, "x": 214.85, "y": -257.35}, {"time": 0.3333, "x": 214.85, "y": -236.1}], "scale": [{"time": 0.2, "x": 3.224, "y": 3.224}, {"time": 0.3333, "x": 4.146, "y": 4.146}]}, "fire2": {"translate": [{"time": 0.0333, "y": 1654.02}, {"time": 0.0667, "y": 1100.29}, {"time": 0.1, "y": 627.32}], "scale": [{"time": 0.0333, "x": 3, "y": 3}]}, "jian": {"translate": [{"y": 2803.37}, {"time": 0.1333, "y": 300.33}], "scale": [{"x": 3, "y": 3}]}, "k": {"translate": [{"time": 0.1333, "x": 233.35, "y": -311.62}], "scale": [{"time": 0.1333, "x": 2.788, "y": 2.788}, {"time": 0.2333, "x": 2.966, "y": 2.966}]}, "kuosan": {"translate": [{"time": 0.1333, "x": 235.01, "y": -313.52}], "scale": [{"time": 0.1333, "x": 5.6, "y": 5.6}]}, "mo": {"translate": [{"time": 0.1333, "x": 82.51, "y": -294.32}], "scale": [{"time": 0.1333, "x": 3.014, "y": 3.014}, {"time": 0.2667, "x": 4.146, "y": 4.146}]}, "mo2": {"rotate": [{"time": 0.2, "angle": -170.12}], "translate": [{"time": 0.2, "x": 348.43, "y": -323.27}], "scale": [{"time": 0.2, "x": 2.938, "y": -2.093}, {"time": 0.4333, "x": 3.955, "y": -2.816}]}, "mo3": {"rotate": [{"time": 0.2333, "angle": 17.42}], "translate": [{"time": 0.2333, "x": 258.73, "y": -369.55}], "scale": [{"time": 0.2333, "x": 2.856, "y": -2.64}, {"time": 0.5333, "x": 4.006, "y": -3.702}]}, "qilang": {"translate": [{"time": 0.0333, "y": 1563.6}, {"time": 0.0667, "y": 922.52}, {"time": 0.1, "y": 547.26}, {"time": 0.1333, "y": 370.05}], "scale": [{"time": 0.0333, "x": 3, "y": 3}]}, "rao": {"translate": [{"time": 0.2333, "x": 238.46, "y": -269.15}], "scale": [{"time": 0.2333, "x": 3, "y": 3}]}, "s": {"translate": [{"time": 0.1333, "x": 15.21, "y": 486.66}], "scale": [{"time": 0.1333, "x": 6, "y": 6}]}, "tuowei": {"translate": [{"time": 0.0667, "x": 4.66, "y": 1067.38}, {"time": 0.1, "y": 680.93}], "scale": [{"time": 0.0667, "x": 3, "y": 3}]}, "zhu": {"translate": [{"time": 0.1333, "x": 16.61, "y": 481.15}], "scale": [{"time": 0.1333, "x": 4.678, "y": 6.53}, {"time": 0.2667, "x": 6.93, "y": 6.53}]}, "zhu2": {"translate": [{"time": 0.1333, "x": 16.33, "y": 127.37}], "scale": [{"time": 0.1333, "x": 2.683, "y": 2.683}, {"time": 0.2, "x": 3.472, "y": 2.683}, {"time": 0.2333, "x": 4.039, "y": 3.529}]}, "hit_back": {"translate": [{"x": -216.54, "y": 298.34}]}, "biu": {"rotate": [{"angle": 56.46}]}, "tuowei2": {"translate": [{"time": 0.1333, "x": 4.66, "y": 1067.38}, {"time": 0.1667, "y": 680.93}], "scale": [{"time": 0.1333, "x": 3, "y": 3}]}, "fire3": {"translate": [{"time": 0.1, "y": 1654.02}, {"time": 0.1333, "y": 1100.29}, {"time": 0.1667, "y": 627.32}], "scale": [{"time": 0.1, "x": 3, "y": 3}]}, "jian2": {"translate": [{"time": 0.0667, "y": 2803.37}, {"time": 0.2, "y": 300.33}], "scale": [{"time": 0.0667, "x": 3, "y": 3}]}, "qilang2": {"translate": [{"time": 0.1, "y": 1563.6}, {"time": 0.1333, "y": 922.52}, {"time": 0.1667, "y": 547.26}, {"time": 0.2, "y": 370.05}], "scale": [{"time": 0.1, "x": 3, "y": 3}]}, "biu2": {"rotate": [{"angle": 56.46}]}, "zhu3": {"translate": [{"time": 0.2, "x": 16.33, "y": 127.37}], "scale": [{"time": 0.2, "x": 2.683, "y": 2.683}, {"time": 0.2667, "x": 3.472, "y": 2.683}, {"time": 0.3, "x": 4.039, "y": 3.529}]}, "zhu4": {"translate": [{"time": 0.2, "x": 16.61, "y": 481.15}], "scale": [{"time": 0.2, "x": 4.678, "y": 6.53}, {"time": 0.3333, "x": 6.93, "y": 6.53}]}, "rao2": {"translate": [{"time": 0.3, "x": 238.46, "y": -269.15}], "scale": [{"time": 0.3, "x": 3, "y": 3}]}, "kuosan2": {"translate": [{"time": 0.2, "x": 235.01, "y": -313.52}], "scale": [{"time": 0.2, "x": 5.6, "y": 5.6}]}, "s2": {"translate": [{"time": 0.2, "x": 15.21, "y": 486.66}], "scale": [{"time": 0.2, "x": 6, "y": 6}]}}, "events": [{"time": 0.1333, "name": "atk"}]}}}