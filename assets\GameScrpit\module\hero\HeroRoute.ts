import { UIHeroBreak } from "../../game/ui/hero/UIHeroBreak";
import { UIHeroBreakSuccess } from "../../game/ui/hero/UIHeroBreakSuccess";
import { UIHeroDetail } from "../../game/ui/hero/UIHeroDetail";
import { UIHeroImprint } from "../../game/ui/hero/UIHeroImprint";
import { UIHeroImprintFullLevel } from "../../game/ui/hero/UIHeroImprintFullLevel";
import { UIHeroImprintPromotion } from "../../game/ui/hero/UIHeroImprintPromotion";
import { UIHeroMain } from "../../game/ui/hero/UIHeroMain";
import { UIHeroTujianDetail } from "../../game/ui/hero/UIHeroTujianDetail";

import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum HeroRouteItem {
  //GameScript/game/ui/hero 目录下继承UINode的类
  UIHeroBreak = "UIHeroBreak",
  UIHeroBreakSuccess = "UIHeroBreakSuccess",
  UIHeroDetail = "UIHeroDetail",
  UIHeroImprint = "UIHeroImprint",
  UIHeroImprintFullLevel = "UIHeroImprintFullLevel",
  UIHeroImprintPromotion = "UIHeroImprintPromotion",
  UIHeroMain = "UIHeroMain",
  UIHeroTujianDetail = "UIHeroTujianDetail",
}
export class HeroRoute {
  rotueTables: Recording[] = [
    {
      node: UIHeroMain,
      uiName: HeroRouteItem.UIHeroMain,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHeroDetail,
      uiName: HeroRouteItem.UIHeroDetail,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHeroBreak,
      uiName: HeroRouteItem.UIHeroBreak,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHeroBreakSuccess,
      uiName: HeroRouteItem.UIHeroBreakSuccess,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHeroImprint,
      uiName: HeroRouteItem.UIHeroImprint,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHeroImprintFullLevel,
      uiName: HeroRouteItem.UIHeroImprintFullLevel,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHeroImprintPromotion,
      uiName: HeroRouteItem.UIHeroImprintPromotion,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHeroTujianDetail,
      uiName: HeroRouteItem.UIHeroTujianDetail,
      keep: false,
      relevanceUIList: [],
    },
  ];
  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
