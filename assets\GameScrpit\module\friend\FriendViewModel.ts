import { persistent, persistentWith } from "../../lib/decorators/persistent";
import { TimeUtils } from "../../lib/utils/TimeUtils";

export class FriendViewModel {
  private _index: number;
  @persistentWith(false)
  private _setting_ten_heart_talk: boolean = false;
  @persistentWith(false)
  private _setting_xilianshi: boolean = false;
  @persistentWith(false)
  private _setting_ten_give_gift: boolean = false;
  @persistent
  private _friend_xilian_tips_setting: string;
  @persistent
  private _friend_xilian_tips_forever_setting: string;

  public isShowXilianTipsSettingForever(): boolean {
    if (this._friend_xilian_tips_setting === "Forever") {
      return false;
    }
    return true;
  }
  public setXilianTipsSettingForever(hide: boolean) {
    if (hide) {
      this._friend_xilian_tips_setting = "Forever";
    } else {
      this._friend_xilian_tips_setting = "";
    }
  }
  public isShowXilianTipsSetting(): boolean {
    if (this._friend_xilian_tips_setting === TimeUtils.formatTimestamp(new Date().getTime(), "YYYY/MM/DD")) {
      return false;
    }
    return true;
  }
  public setXilianTipsSetting(hide: boolean) {
    if (hide) {
      this._friend_xilian_tips_setting = TimeUtils.formatTimestamp(new Date().getTime(), "YYYY/MM/DD");
    } else {
      this._friend_xilian_tips_setting = "";
    }
  }
  public get setting_ten_heart_talk(): boolean {
    return this._setting_ten_heart_talk;
  }
  public set setting_ten_heart_talk(value: boolean) {
    this._setting_ten_heart_talk = value;
  }
  public get setting_xilianshi(): boolean {
    return this._setting_xilianshi;
  }
  public set setting_xilianshi(value: boolean) {
    this._setting_xilianshi = value;
  }
  public get setting_ten_give_gift(): boolean {
    return this._setting_ten_give_gift;
  }
  public set setting_ten_give_gift(value: boolean) {
    this._setting_ten_give_gift = value;
  }

  private _onViewModelChange: Function;
  public set index(value: number) {
    this._index = value;
    if (this._onViewModelChange) {
      this._onViewModelChange(this);
    }
  }
  public get index(): number {
    return this._index;
  }
  public set onViewModelChange(value: Function) {
    this._onViewModelChange = value;
  }
}
