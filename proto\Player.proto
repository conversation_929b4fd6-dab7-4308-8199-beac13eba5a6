syntax = "proto3";
package sim;
import "Comm.proto";

// 
message BubbleMessage {
  // 气泡ID
  int64 bubbleId = 1;
  // 有限期截止时间 -1表示没有失效时间
  int64 expireTime = 2;
  // 是否选中
  bool chosen = 3;
}

// 
message ClubSimpleMessage {
  // 仙盟ID
  int64 id = 1;
  // 仙盟名称
  string name = 2;
  // 旗帜
  string avatar = 3;
  // 等级
  int32 level = 4;
  // 已有人数
  int32 cnt = 5;
  // 最大人数
  int32 maxCnt = 6;
}

// 
message DecorationMessage {
  // 装饰物ID
  int64 decorationId = 1;
  // 有限期截止时间 -1表示没有失效时间
  int64 expireTime = 2;
}

// 
message HeadFrameMessage {
  // 头像框ID
  int64 headFrameId = 1;
  // 有限期截止时间 -1表示没有失效时间
  int64 expireTime = 2;
  // 是否选中
  bool chosen = 3;
}

// 
message HeadShowMessage {
  // 头像ID
  int64 headShowId = 1;
  // 有限期截止时间 -1表示没有失效时间
  int64 expireTime = 2;
  // 是否选中
  bool chosen = 3;
}

// 
message HeroSimpleMessage {
  // 英雄编号
  int64 heroId = 1;
  // 英雄等级
  int32 level = 2;
  // 英雄突破几次
  int32 breakTopLevel = 3;
}

// 
message HorseSimpleMessage {
  // 当前使用的坐骑编号
  int64 horseId = 1;
  // 升阶的阶数
  int32 stage = 2;
  // 当阶的已升级的次数
  int32 grade = 3;
}

// 
message OfflineEnergyMessage {
  // 离线期间获得气运
  double energy = 1;
  // 间隔时间
  int64 spanTime = 2;
  // 气运库存
  double totalEnergy = 3;
}

// 
message PetSimpleMessage {
  // 宠物模版ID
  int64 petId = 1;
  // 宠物等级
  int32 level = 2;
  // 觉醒次数
  int32 awakeCount = 3;
  // 生效的皮肤ID
  int64 chosenSkinId = 4;
}

// 
message PlayVipExpResponse {
  int32 vipExp = 1;
  int32 vipLevel = 2;
}

// 
message PlayerBaseMessage {
  int64 userId = 1;
  // 角色名称
  string nickname = 2;
  // 头像
  repeated int64 avatarList = 3;
  // 性别
  int32 sex = 4;
  // 当前等级
  int32 level = 5;
  // 会员等级
  int32 vipLevel = 6;
  // 是否对外隐藏贵族
  bool hiddenVip = 7;
}

// 
message PlayerBattleAttrResponse {
  map<int64,double> battleAttrMap = 1;
  double power = 2;
  // 总繁荣度
  double speed = 3;
  // 三界繁荣度
  double citySpeed = 4;
  // 弟子繁荣度
  double pupilSpeed = 5;
}

// 创建用户的提交信息
message PlayerCreateMessage {
  // 角色名称
  string name = 1;
  // 性别
  int32 sex = 2;
}

// 
message PlayerDailyTreasureResponse {
  // 弹窗展示新增资源 偶数Index为资源ID，奇数Index为数量
  repeated double resAddList = 1;
  // 最近一次玩家领取每日宝箱的时间 前端需要更新到玩家信息的对应字段中
  int64 lastDailyTreasureTs = 2;
  map<int64,double> battleAttrMap = 3;
  double power = 4;
}

// 
message PlayerDataMessage {
  int64 id = 1;
  // 角色名称
  string nickname = 2;
  // 当前等级
  int32 level = 3;
  // VIP会员等级
  int32 vipLevel = 4;
  // 充值获得的积分
  int32 vipExp = 5;
  // 所在服务器ID
  int64 serverId = 6;
  // 性别
  int32 sex = 7;
  // 创建时间戳
  int64 createTs = 8;
  // 注册区域IP归属
  string regArea = 9;
  // 上一次领取每日宝箱的时间
  int64 lastDailyTreasureTs = 10;
  // 修改名称的次数
  int32 renameCount = 11;
  // 更新时间戳
  int64 lastAddEnergyTs = 12;
  // key: 模板ID，可叠加
  map<int64,double> bigNumItemMap = 13;
  // key: 模板ID，可叠加
  map<int64,int32> itemMap = 14;
  // 记录有播放过的动画的模块
  repeated int64 animationList = 15;
  // 引导ID
  int64 guideId = 16;
  // 全局权限配置
  repeated int32 configurationIdList = 17;
}

// 
message PlayerDetailMessage {
  // 玩家基础信息
  PlayerSimpleMessage simpleMessage = 1;
  // 武魂 武魂ID为-1表示不存在
  SoulSimpleMessage soulMessage = 2;
  // 坐骑 坐骑ID为-1表示不存在
  HorseSimpleMessage horseMessage = 3;
  // 所在仙盟 ID为-1表示未加入仙盟
  ClubSimpleMessage clubSimpleMessage = 4;
  // 战力排行
  int32 powerRank = 5;
  // 繁荣度排行
  int32 energySpeedRank = 6;
  // 演武场排行
  int32 competeRank = 7;
  int32 heroCount = 8;
  int32 friendCount = 9;
}

// 
message PlayerEnergyUpdateMessage {
  // 总气运
  double totalEnergy = 1;
  // 服务器时间戳
  int64 nowStamp = 2;
}

// 
message PlayerLevelUpResponse {
  // 当前等级
  int32 level = 1;
  // 奖励列表
  sim.RewardMessage rewardMessage = 2;
}

// 
message PlayerRankBoardMessage {
  // 是否领取本服一键点赞排行榜奖励
  bool takeCurServerRankReward = 1;
}

// 
message PlayerRankMessage {
  double point = 1;
  int32 rank = 2;
  repeated PlayerSimpleMessage rankList = 3;
}

// 
message PlayerRenameResponse {
  // 新的角色名称
  string nickname = 1;
  // 修改名称的次数
  int32 renameCount = 2;
}

// 
message PlayerSimpleMessage {
  int64 userId = 1;
  // 角色名称
  string nickname = 2;
  // 头像
  repeated int64 avatarList = 3;
  // 性别
  int32 sex = 4;
  // 当前等级
  int32 level = 5;
  // vip等级
  int32 vipLevel = 6;
  // 所在服务器ID
  int64 serverId = 7;
  // 所在服务器的名称
  string serverName = 8;
  // 注册区域IP归属
  string regArea = 9;
  // 战斗属性
  map<int64,double> battleAttrMap = 10;
  // 赚速
  double energySpeed = 11;
  // 总战力
  double power = 12;
  // 最近一次登录的时间
  int64 lastLoginTime = 13;
  // 坐骑的模板ID
  int64 horseId = 14;
  // 是否对外隐藏贵族
  bool hiddenVip = 15;
}

// 
message PlayerTempleMessage {
  // 玩家基础信息
  PlayerSimpleMessage simpleMessage = 1;
  // 最强的武将列表
  repeated HeroSimpleMessage heroList = 2;
  // 坐骑 坐骑ID为-1表示不存在
  HorseSimpleMessage horseMessage = 3;
  // 最强的灵兽列表
  repeated PetSimpleMessage petList = 4;
}

// 
message PlayerUpdateEnergyRequestMessage {
  int32 autoClick = 1;
  int32 manualClick = 2;
}

// 
message SkinMessage {
  // 皮肤ID
  int64 skinId = 1;
  // 有限期截止时间 -1表示没有失效时间
  int64 expireTime = 2;
  // 叠了几层
  int32 level = 3;
  // 数量(一级)
  int32 stock = 4;
  // 是否选中
  bool chosen = 5;
}

// 
message SoulSimpleMessage {
  int64 id = 1;
  // 武魂的模板ID
  int64 soulTemplateId = 2;
  // 阶
  int32 stage = 3;
  // 等级
  int32 grade = 4;
  // 属性集合
  map<int64,double> attrMap = 5;
}

// 
message TitleMessage {
  // 称号ID
  int64 titleId = 1;
  // 有限期截止时间 -1表示没有失效时间
  int64 expireTime = 2;
  // 是否选中
  bool chosen = 3;
}

