import { UIHorseAttrDetail } from "../../game/ui/ui_horse/UIHorseAttrDetail";
import { UIHorseHelp } from "../../game/ui/ui_horse/UIHorseHelp";
import { UIHorseList } from "../../game/ui/ui_horse/UIHorseList";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum HorseRouteName {
  UIHorseList = "UIHorseList",
  UIHorseAttrDetail = "UIHorseAttrDetail",
  UIHorseHelp = "UIHorseHelp",
}
export class HorseRoute {

  rotueTables: Recording[] = [
    {
      node: UIHorseList,
      uiName: HorseRouteName.UIHorseList,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHorseAttrDetail,
      uiName: HorseRouteName.UIHorseAttrDetail,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIHorseHelp,
      uiName: HorseRouteName.UIHorseHelp,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
