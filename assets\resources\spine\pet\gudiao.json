{"skeleton": {"hash": "hmFNwRketiJW3t92vuINtyIm16w", "spine": "3.8.99", "x": -156.61, "y": 67.3, "width": 285.16, "height": 211.9, "images": "./images/", "audio": "D:/spine导出/灵兽动画/蛊雕"}, "bones": [{"name": "root", "y": 0.88, "scaleX": -1}, {"name": "bone", "parent": "root", "x": 6.71, "y": 49.32, "scaleX": 0.4942, "scaleY": 0.4942}, {"name": "bone67", "parent": "bone", "length": 161.31, "rotation": 1.18, "x": 30.74, "y": 46.48}, {"name": "bone2", "parent": "bone67", "length": 44.39, "rotation": 0.87, "x": 41.82, "y": 158.44}, {"name": "bone3", "parent": "bone2", "length": 39.24, "rotation": 116.93, "x": -37.49, "y": -13.99, "color": "98ff00ff"}, {"name": "bone4", "parent": "bone3", "length": 46.94, "rotation": -11.97, "x": 39.24, "color": "98ff00ff"}, {"name": "bone5", "parent": "bone2", "x": 19.02, "y": -44.54, "color": "98ff00ff"}, {"name": "bone6", "parent": "bone5", "length": 50.05, "rotation": -174.77, "x": -33.49, "y": -20.19, "color": "98ff00ff"}, {"name": "bone7", "parent": "bone6", "length": 40.76, "rotation": -38.49, "x": 60.27, "y": 0.82, "color": "98ff00ff"}, {"name": "bone8", "parent": "bone7", "length": 51.36, "rotation": 0.96, "x": 40.76, "color": "98ff00ff"}, {"name": "bone9", "parent": "bone8", "length": 58.94, "rotation": -6, "x": 51.36, "color": "98ff00ff"}, {"name": "bone10", "parent": "bone6", "length": 36.23, "rotation": 46.02, "x": 48.79, "y": 19.33, "color": "98ff00ff"}, {"name": "bone11", "parent": "bone10", "length": 45.26, "rotation": -4.99, "x": 36.23, "color": "98ff00ff"}, {"name": "bone12", "parent": "bone4", "length": 32.01, "rotation": -8.37, "x": 47.04, "y": 0.28, "color": "98ff00ff"}, {"name": "bone51", "parent": "bone12", "x": 36.28, "y": 16.57, "color": "98ff00ff"}, {"name": "bone13", "parent": "bone51", "rotation": -4.18, "x": 26.53, "y": -4.75, "color": "68ff00ff"}, {"name": "bone69", "parent": "bone67", "x": 39.47, "y": 18.59}, {"name": "bone14", "parent": "bone69", "rotation": -1.18, "x": -29.18, "y": 12.54, "color": "ff0067ff"}, {"name": "bone15", "parent": "bone69", "rotation": -1.18, "x": 43.86, "y": 24.12, "color": "ff0067ff"}, {"name": "bone16", "parent": "bone69", "rotation": -1.18, "x": 94.91, "y": 45.24, "color": "ff0067ff"}, {"name": "bone17", "parent": "bone69", "rotation": -1.18, "x": 141.1, "y": 78.99, "color": "ff0067ff"}, {"name": "bone18", "parent": "bone69", "rotation": -1.18, "x": 176.71, "y": 123.75, "color": "ff0067ff"}, {"name": "bone19", "parent": "bone69", "rotation": -1.18, "x": 196.87, "y": 191.58, "color": "ff0067ff"}, {"name": "bone20", "parent": "bone67", "rotation": -1.18, "x": -89.11, "y": 9.29, "color": "ff0067ff"}, {"name": "bone21", "parent": "bone67", "rotation": -1.18, "x": -183.11, "y": 56.72, "color": "ff0067ff"}, {"name": "bone22", "parent": "bone67", "rotation": -1.18, "x": -167.68, "y": 198.58, "color": "ff0067ff"}, {"name": "bone70", "parent": "bone67", "x": -238.35, "y": 94.65}, {"name": "bone23", "parent": "bone70", "rotation": -1.18, "x": 8.39, "y": 34.12, "color": "ff0067ff"}, {"name": "bone24", "parent": "bone70", "rotation": -1.18, "x": -6.03, "y": 79.91, "color": "ff0067ff"}, {"name": "bone25", "parent": "bone70", "rotation": -1.18, "x": -7.16, "y": 135.1, "color": "ff0067ff"}, {"name": "bone26", "parent": "bone70", "rotation": -1.18, "x": -43.32, "y": 174.52, "color": "ff0067ff"}, {"name": "bone27", "parent": "bone4", "length": 70.49, "rotation": -64.22, "x": 18.2, "y": -65, "color": "98ff00ff"}, {"name": "bone28", "parent": "bone27", "length": 78.15, "rotation": 29.16, "x": 70.49, "color": "98ff00ff"}, {"name": "bone29", "parent": "bone28", "length": 97.81, "rotation": -50.15, "x": 78.15, "color": "98ff00ff"}, {"name": "bone30", "parent": "bone27", "x": 62.65, "y": -48.99, "color": "98ff00ff"}, {"name": "bone31", "parent": "bone28", "rotation": -29.16, "x": 20.16, "y": -83.16, "color": "98ff00ff"}, {"name": "bone32", "parent": "bone29", "rotation": 20.98, "x": 79.92, "y": -52.12, "color": "98ff00ff"}, {"name": "bone33", "parent": "bone4", "length": 68.71, "rotation": 23.44, "x": 63.18, "y": 65.12, "color": "98ff00ff"}, {"name": "bone34", "parent": "bone33", "length": 71.01, "rotation": -21.46, "x": 68.71, "color": "98ff00ff"}, {"name": "bone35", "parent": "bone34", "length": 79.5, "rotation": 44.26, "x": 71.01, "color": "98ff00ff"}, {"name": "bone36", "parent": "bone33", "x": 44.35, "y": 46.08, "color": "98ff00ff"}, {"name": "bone37", "parent": "bone34", "rotation": 21.46, "x": 32.8, "y": 61.47, "color": "98ff00ff"}, {"name": "bone38", "parent": "bone35", "rotation": -22.81, "x": 78.44, "y": 26.58, "color": "98ff00ff"}, {"name": "bone39", "parent": "bone67", "rotation": -1.18, "x": -130.88, "y": 358.41, "color": "e20088ff"}, {"name": "bone40", "parent": "bone2", "x": -150.95, "y": 95.79, "color": "06cdffff"}, {"name": "bone41", "parent": "bone2", "x": 66.54, "y": 3.15, "color": "06cdffff"}, {"name": "bone42", "parent": "bone2", "x": -148.23, "y": 48.69, "color": "0a9dffff"}, {"name": "bone43", "parent": "bone2", "x": -34.38, "y": 17.57, "color": "06cdffff"}, {"name": "bone44", "parent": "bone2", "x": 37.25, "y": 110.71, "color": "06cdffff"}, {"name": "bone45", "parent": "bone2", "x": -36.02, "y": 123.13, "color": "06cdffff"}, {"name": "bone46", "parent": "bone42", "length": 15.68, "rotation": -95.57, "x": -2.11, "y": -11.46, "color": "0a9dffff"}, {"name": "bone47", "parent": "bone46", "length": 17.86, "rotation": 7.39, "x": 15.68, "color": "0a9dffff"}, {"name": "bone48", "parent": "bone42", "length": 18.37, "rotation": -12.62, "x": 15.07, "y": -2.68, "color": "0a9dffff"}, {"name": "bone49", "parent": "bone48", "length": 17.06, "rotation": -19.97, "x": 18.37, "color": "0a9dffff"}, {"name": "bone50", "parent": "bone12", "x": 100.53, "y": -5.74, "color": "98ff00ff"}, {"name": "bone52", "parent": "bone42", "length": 23.16, "rotation": -54.3, "x": 6.16, "y": -5.01, "color": "0a9dffff"}, {"name": "bone53", "parent": "bone52", "length": 27.13, "rotation": -32.75, "x": 23.05, "y": 0.09, "color": "0a9dffff"}, {"name": "bone54", "parent": "bone53", "length": 33.49, "rotation": -32.01, "x": 21.11, "color": "0a9dffff"}, {"name": "bone55", "parent": "bone54", "length": 36.64, "rotation": -39.21, "x": 33.36, "y": -0.07, "color": "0a9dffff"}, {"name": "bone56", "parent": "bone55", "length": 36.15, "rotation": -2.45, "x": 29.67, "y": -0.27, "color": "0a9dffff"}, {"name": "bone57", "parent": "bone56", "length": 33.32, "rotation": 31.17, "x": 37.3, "y": 0.81, "color": "0a9dffff"}, {"name": "bone58", "parent": "bone42", "length": 35.84, "rotation": -46.26, "x": 8.62, "y": -4.82, "color": "0a9dffff"}, {"name": "bone59", "parent": "bone58", "length": 31.99, "rotation": -15.08, "x": 35.84, "color": "0a9dffff"}, {"name": "bone60", "parent": "bone59", "length": 29.18, "rotation": -30.43, "x": 31.99, "color": "0a9dffff"}, {"name": "bone61", "parent": "bone60", "length": 27.5, "rotation": -48.98, "x": 29.18, "color": "0a9dffff"}, {"name": "bone62", "parent": "bone61", "length": 35.02, "rotation": -30.03, "x": 33.08, "y": -0.38, "color": "0a9dffff"}, {"name": "bone63", "parent": "bone62", "length": 40.66, "rotation": -29.26, "x": 41.11, "y": -2.81, "color": "0a9dffff"}, {"name": "bone64", "parent": "bone63", "length": 33.87, "rotation": -25.5, "x": 40.66, "color": "0a9dffff"}, {"name": "bone65", "parent": "bone64", "length": 30.54, "rotation": -18.42, "x": 33.87, "color": "0a9dffff"}, {"name": "bone66", "parent": "bone65", "length": 25.65, "rotation": -12.2, "x": 30.87, "y": 0.47, "color": "0a9dffff"}, {"name": "bone68", "parent": "bone41", "x": 35.85, "y": 7.15, "color": "06cdffff"}], "slots": [{"name": "x6", "bone": "bone", "attachment": "x6"}, {"name": "cb2", "bone": "bone", "attachment": "cb2"}, {"name": "cb1", "bone": "bone", "attachment": "cb1"}, {"name": "x5", "bone": "bone", "attachment": "x5"}, {"name": "m5", "bone": "bone", "attachment": "m5"}, {"name": "m4", "bone": "bone5", "attachment": "m4"}, {"name": "m3", "bone": "bone", "attachment": "m3"}, {"name": "m2", "bone": "bone4", "attachment": "m2"}, {"name": "m1", "bone": "bone", "attachment": "m1"}, {"name": "x4", "bone": "bone", "attachment": "x4"}, {"name": "x3", "bone": "bone", "attachment": "x3"}, {"name": "x44", "bone": "bone", "attachment": "x44"}, {"name": "x2", "bone": "bone", "attachment": "x2"}, {"name": "x1", "bone": "bone", "attachment": "x1"}, {"name": "t1", "bone": "bone", "attachment": "t1"}, {"name": "biyan", "bone": "bone13"}, {"name": "yanj", "bone": "bone13", "attachment": "yanj"}, {"name": "w6", "bone": "bone39", "attachment": "w6"}, {"name": "w5", "bone": "bone23", "attachment": "w5"}, {"name": "w4", "bone": "bone22", "attachment": "w4"}, {"name": "w3", "bone": "bone21", "attachment": "w3"}, {"name": "w2", "bone": "bone20", "attachment": "w2"}, {"name": "w1", "bone": "bone14", "attachment": "w1"}, {"name": "l1", "bone": "bone68", "attachment": "l1", "blend": "additive"}], "transform": [{"name": "111", "bones": ["bone13"], "target": "bone51", "rotation": -4.18, "x": 24.66, "y": -5.97, "rotateMix": 0, "translateMix": 0.237, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"biyan": {"biyan": {"x": 4.72, "y": -7.5, "rotation": -91.16, "width": 121, "height": 28}}, "cb1": {"cb1": {"type": "mesh", "uvs": [1, 0.87075, 0.89399, 0.81151, 0.77899, 0.71734, 0.69356, 0.56242, 0.65249, 0.41205, 0.61635, 0.25713, 0.51778, 0.11587, 0.36171, 0.06575, 0.20235, 0.05968, 0.09064, 0, 0.00028, 0, 0, 0.10676, 0.00521, 0.2389, 0.05614, 0.38168, 0.11857, 0.52749, 0.18428, 0.66722, 0.27299, 0.78569, 0.39128, 0.87986, 0.51121, 0.96796, 0.62785, 1, 0.78556, 0.98922, 0.92192, 0.95581, 0.11484, 0.13648, 0.29547, 0.18132, 0.45268, 0.2509, 0.53128, 0.38542, 0.53964, 0.53541, 0.58313, 0.64983, 0.65337, 0.77198, 0.77044, 0.86475, 0.63497, 0.88485, 0.51957, 0.83847, 0.42257, 0.72868, 0.36738, 0.59416, 0.36905, 0.44882, 0.27874, 0.30038, 0.15164, 0.21998, 0.14327, 0.35141, 0.22188, 0.51221, 0.26369, 0.62663], "triangles": [37, 12, 36, 13, 12, 37, 14, 13, 37, 38, 37, 35, 38, 35, 34, 14, 37, 38, 33, 38, 34, 39, 38, 33, 15, 14, 38, 15, 38, 39, 32, 33, 27, 32, 16, 39, 32, 39, 33, 15, 39, 16, 31, 32, 27, 31, 27, 28, 17, 16, 32, 17, 32, 31, 30, 31, 28, 18, 17, 31, 18, 31, 30, 19, 18, 30, 22, 9, 8, 11, 10, 9, 22, 11, 9, 23, 8, 7, 22, 8, 23, 36, 22, 23, 12, 11, 22, 12, 22, 36, 24, 7, 6, 23, 7, 24, 24, 6, 5, 35, 36, 23, 35, 23, 24, 37, 36, 35, 34, 35, 24, 25, 24, 5, 25, 5, 4, 34, 24, 25, 26, 25, 4, 34, 25, 26, 26, 4, 3, 33, 34, 26, 27, 26, 3, 33, 26, 27, 27, 3, 2, 28, 27, 2, 29, 28, 2, 29, 2, 1, 30, 28, 29, 21, 1, 0, 29, 1, 21, 20, 29, 21, 30, 29, 20, 19, 30, 20], "vertices": [1, 37, -19.57, -22.42, 1, 1, 37, 3.47, -14.75, 1, 1, 37, 33.29, -10.56, 1, 2, 37, 69.14, -19.12, 0.29671, 38, 7.4, -17.64, 0.70329, 1, 38, 40.16, -20.41, 1, 2, 38, 73.52, -24.4, 0.69053, 39, -15.23, -19.22, 0.30947, 2, 38, 108.12, -15.88, 0.0186, 39, 15.5, -37.27, 0.9814, 1, 39, 47.6, -33, 1, 1, 39, 76.07, -20.09, 1, 2, 39, 101.31, -21.54, 0.99945, 42, 39.74, -35.49, 0.00055, 2, 39, 117.13, -13.57, 0.98565, 42, 51.23, -22.01, 0.01435, 2, 39, 106.99, 6.67, 0.79707, 42, 34.04, -7.28, 0.20293, 3, 39, 93.48, 31.23, 0.07407, 41, 87.15, -6.17, 0.00065, 42, 12.06, 10.11, 0.92528, 3, 39, 70.94, 53.77, 0.01067, 41, 57.64, 5.88, 0.25124, 42, -17.44, 22.16, 0.73809, 3, 39, 46.11, 75.87, 0.01082, 41, 26.18, 16.62, 0.76484, 42, -48.91, 32.9, 0.22434, 4, 38, 18.88, 83.98, 0.00061, 40, 72.65, 25.17, 0.08952, 41, -4.72, 26.04, 0.90537, 42, -79.8, 42.32, 0.00451, 3, 38, -10.53, 75.71, 0.0289, 40, 42.25, 28.23, 0.42521, 41, -35.12, 29.1, 0.54589, 3, 38, -36.95, 60.29, 0.01279, 40, 12.02, 23.54, 0.83755, 41, -65.35, 24.42, 0.14966, 3, 37, 26.91, 63.86, 0.0515, 40, -17.44, 17.77, 0.94807, 41, -94.81, 18.65, 0.00043, 2, 37, 6.91, 50.87, 0.34378, 40, -37.45, 4.78, 0.65622, 2, 37, -11.41, 25.86, 0.82537, 40, -55.76, -20.22, 0.17463, 2, 37, -23.36, 0.93, 0.99579, 40, -67.71, -45.15, 0.00421, 2, 39, 84.06, 2.17, 0.91728, 42, 14.65, -20.32, 0.08272, 1, 39, 48.16, -5.27, 1, 1, 39, 14.01, -5.97, 1, 4, 38, 53.23, 0.22, 0.99844, 39, -12.58, 12.57, 0.001, 41, -3.39, -64.47, 0.00053, 42, -78.47, -48.19, 3e-05, 4, 38, 22.63, 9.02, 0.92971, 39, -28.35, 40.23, 0.00115, 40, 48.72, -45.96, 0.01161, 41, -28.65, -45.09, 0.05753, 4, 37, 69.09, 9.37, 0.47252, 38, -3.08, 8.86, 0.411, 40, 24.73, -36.71, 0.0816, 41, -52.64, -35.84, 0.03488, 4, 37, 40.45, 15.7, 0.76281, 38, -32.05, 4.27, 0.00039, 40, -3.91, -30.39, 0.2356, 41, -81.27, -29.51, 0.0012, 2, 37, 10.6, 11, 0.91159, 40, -33.76, -35.09, 0.08841, 2, 37, 24.58, 33.97, 0.40916, 40, -19.77, -12.12, 0.59084, 4, 37, 46.74, 44.8, 0.0195, 38, -36.84, 33.65, 0.00525, 40, 2.38, -1.29, 0.96523, 41, -74.99, -0.41, 0.01002, 4, 37, 76.78, 44.16, 0.04728, 38, -8.64, 44.06, 0.14841, 40, 32.43, -1.92, 0.48252, 41, -44.94, -1.05, 0.32179, 5, 37, 105.5, 33.89, 0.00215, 38, 21.84, 45, 0.25777, 39, -3.81, 66.54, 0.00653, 40, 61.15, -12.19, 0.09071, 41, -16.22, -11.32, 0.64284, 5, 38, 50.87, 34.66, 0.36343, 39, 9.77, 38.88, 0.20011, 40, 84.38, -32.43, 0.00076, 41, 7.01, -31.56, 0.39209, 42, -68.07, -15.28, 0.04362, 4, 38, 86.39, 41.15, 0.02497, 39, 39.73, 18.74, 0.69941, 41, 42.44, -38.51, 0.11307, 42, -32.64, -22.23, 0.16255, 3, 39, 69.65, 14.73, 0.55394, 41, 71.58, -30.61, 0.0086, 42, -3.5, -14.33, 0.43745, 4, 38, 84.8, 69.78, 0.00217, 39, 58.58, 40.35, 0.12199, 41, 51.44, -11.28, 0.23568, 42, -23.64, 5, 0.64016, 4, 38, 47.55, 66.31, 0.01135, 39, 29.48, 63.86, 0.04002, 41, 15.5, -0.89, 0.81942, 42, -59.58, 15.39, 0.12921, 4, 38, 21.95, 66.46, 0.0154, 40, 69.1, 7.74, 0.07692, 41, -8.27, 8.61, 0.90757, 42, -83.36, 24.89, 0.00011], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 22, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 74, 76, 76, 78], "width": 196, "height": 212}}, "cb2": {"cb2": {"type": "mesh", "uvs": [0.00448, 0.97258, 0, 0.91217, 0.01237, 0.83543, 0.10856, 0.77012, 0.2016, 0.67706, 0.2426, 0.55787, 0.22367, 0.42235, 0.28044, 0.30969, 0.38767, 0.1856, 0.49963, 0.13662, 0.68256, 0.11702, 0.83552, 0.06314, 0.9191, 0, 1, 0, 1, 0.14968, 1, 0.31459, 1, 0.4697, 0.92383, 0.6395, 0.81187, 0.77176, 0.67783, 0.87462, 0.52487, 0.96442, 0.34194, 1, 0.17321, 1, 0.05337, 1, 0.12947, 0.88604, 0.25766, 0.77479, 0.35757, 0.65963, 0.40469, 0.5152, 0.45182, 0.36296, 0.59697, 0.28683, 0.76663, 0.2478, 0.89105, 0.20486, 0.88916, 0.32587, 0.75155, 0.36491, 0.62148, 0.43712, 0.54985, 0.55228, 0.50649, 0.72599, 0.416, 0.83139, 0.30667, 0.90166, 0.5932, 0.77674, 0.70065, 0.69867, 0.79302, 0.58351, 0.87974, 0.45079], "triangles": [37, 26, 36, 25, 26, 37, 38, 25, 37, 39, 37, 36, 20, 37, 39, 21, 38, 37, 21, 37, 20, 22, 38, 21, 40, 35, 41, 36, 35, 40, 40, 41, 18, 39, 36, 40, 19, 39, 40, 19, 40, 18, 20, 39, 19, 42, 33, 32, 32, 15, 16, 42, 32, 16, 41, 33, 42, 34, 33, 41, 35, 34, 41, 17, 42, 16, 41, 42, 17, 18, 41, 17, 12, 13, 14, 11, 12, 14, 31, 11, 14, 30, 10, 11, 30, 11, 31, 29, 9, 10, 29, 10, 30, 31, 14, 15, 32, 30, 31, 32, 31, 15, 28, 8, 9, 28, 9, 29, 7, 8, 28, 33, 29, 30, 33, 30, 32, 34, 29, 33, 28, 29, 34, 34, 35, 28, 27, 7, 28, 35, 27, 28, 6, 7, 27, 5, 6, 27, 26, 5, 27, 4, 5, 26, 35, 26, 27, 36, 26, 35, 25, 4, 26, 3, 4, 25, 24, 3, 25, 2, 3, 24, 24, 25, 38, 1, 2, 24, 23, 0, 1, 24, 23, 1, 22, 24, 38, 23, 24, 22], "vertices": [2, 31, -21.34, -4.22, 0.99734, 34, -83.99, 44.76, 0.00266, 1, 31, -12.84, 6.51, 1, 1, 31, 1.07, 17.27, 1, 1, 31, 27.61, 12.82, 1, 2, 31, 57.88, 13.46, 0.80054, 32, -4.46, 17.9, 0.19946, 2, 31, 83.21, 26.72, 0.00191, 32, 24.13, 17.13, 0.99809, 1, 32, 51.87, 30.83, 1, 2, 32, 80.2, 26.09, 0.79908, 33, -18.72, 18.29, 0.20092, 2, 32, 114.64, 10.92, 0.00886, 33, 15, 35.01, 0.99114, 1, 33, 43.43, 35.56, 1, 1, 33, 84.82, 23.78, 1, 2, 33, 122.58, 21.79, 0.99762, 36, 66.29, 53.74, 0.00238, 1, 33, 146.03, 27.78, 1, 2, 33, 163.61, 20.74, 0.99818, 36, 104.23, 38.07, 0.00182, 2, 33, 151.05, -10.66, 0.86869, 36, 81.26, 13.24, 0.13131, 3, 33, 137.21, -45.27, 0.40617, 36, 55.94, -14.11, 0.59365, 35, 122.02, 9.47, 0.00018, 3, 33, 124.19, -77.82, 0.05897, 36, 32.13, -39.84, 0.88977, 35, 98.21, -16.25, 0.05127, 2, 36, -7.02, -55.9, 0.65821, 35, 59.06, -32.31, 0.34179, 3, 36, -46.55, -60.04, 0.22711, 35, 19.53, -36.45, 0.77035, 34, 85.49, -50.26, 0.00254, 3, 36, -85.36, -55.79, 0.00275, 35, -19.28, -32.21, 0.78146, 34, 46.68, -46.01, 0.21579, 2, 35, -59.34, -22.79, 0.24813, 34, 6.63, -36.59, 0.75187, 2, 31, 32.4, -62.41, 0.19542, 34, -30.25, -13.42, 0.80458, 2, 31, 3.43, -35.59, 0.7819, 34, -59.22, 13.4, 0.2181, 2, 31, -17.15, -16.54, 0.97938, 34, -79.81, 32.45, 0.02062, 2, 31, 13.41, -9.74, 0.95366, 34, -49.24, 39.25, 0.04634, 2, 31, 52.5, -11.66, 0.86372, 34, -10.15, 37.33, 0.13628, 6, 31, 87.34, -8.44, 0.03125, 32, 10.6, -15.58, 0.81137, 33, -31.33, -61.84, 0.0015, 36, -107.35, 30.77, 0.0006, 35, -41.28, 54.36, 0.05822, 34, 24.69, 40.55, 0.09706, 5, 32, 45.05, -15.95, 0.85652, 33, -8.97, -35.63, 0.05926, 36, -77.09, 47.23, 0.01403, 35, -11.01, 70.82, 0.06156, 34, 54.95, 57.01, 0.00863, 4, 32, 81.18, -15.77, 0.13973, 33, 14.05, -7.78, 0.84191, 36, -45.63, 65, 0.00873, 35, 20.45, 88.58, 0.00964, 4, 32, 108.06, -42.73, 0.00243, 33, 51.97, -4.42, 0.97446, 36, -9.01, 54.55, 0.02066, 35, 57.06, 78.14, 0.00244, 2, 33, 92.11, -10.97, 0.86904, 36, 26.12, 34.06, 0.13096, 2, 33, 122.75, -12.77, 0.80279, 36, 54.07, 21.4, 0.19721, 2, 33, 112.18, -38, 0.38836, 36, 35.17, 1.63, 0.61164, 4, 32, 102.49, -82.59, 0.00235, 33, 79, -34.23, 0.36558, 36, 5.55, 17.03, 0.63005, 35, 71.62, 40.62, 0.00201, 4, 32, 77.54, -58.71, 0.1014, 33, 44.68, -38.08, 0.4188, 36, -27.87, 25.73, 0.34423, 35, 38.2, 49.31, 0.13557, 5, 32, 47.61, -50.84, 0.33358, 33, 19.45, -56.02, 0.15244, 36, -57.85, 18.01, 0.13973, 35, 8.22, 41.6, 0.35633, 34, 74.19, 27.79, 0.01792, 6, 31, 102.72, -43.11, 0.0027, 32, 7.14, -53.36, 0.23268, 33, -4.55, -88.71, 0.0055, 36, -91.97, -3.91, 0.00229, 35, -25.89, 19.68, 0.45628, 34, 40.07, 5.87, 0.30056, 4, 31, 71.01, -46.21, 0.02705, 32, -22.07, -40.61, 0.0563, 35, -57.61, 16.58, 0.06798, 34, 8.35, 2.77, 0.84867, 2, 31, 41.44, -40.49, 0.35145, 34, -21.21, 8.5, 0.64855, 4, 32, 2.52, -76.2, 0.03542, 33, 10.03, -106.89, 6e-05, 35, -18.79, -2.52, 0.7386, 34, 47.17, -16.33, 0.22592, 5, 32, 27.09, -94.64, 0.00057, 33, 39.93, -99.85, 0.0009, 36, -54.43, -30.24, 0.12748, 35, 11.65, -6.65, 0.87061, 34, 77.61, -20.46, 0.00044, 4, 32, 58.53, -107.13, 0.00247, 33, 69.67, -83.71, 0.00022, 36, -20.89, -25.82, 0.61481, 35, 45.19, -2.23, 0.38249, 3, 33, 99.65, -63.4, 0.03997, 36, 14.38, -17.59, 0.91523, 35, 80.46, 6, 0.04479], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 2, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 78, 80, 80, 82, 82, 84], "width": 234, "height": 226}}, "l1": {"l1": {"x": -0.44, "y": 0.21, "rotation": -2.05, "width": 31, "height": 47}}, "m1": {"m1": {"type": "mesh", "uvs": [0, 0.72563, 0.00086, 0.5537, 0.04533, 0.31656, 0.15494, 0.18909, 0.26773, 0.15945, 0.42181, 0.18909, 0.5346, 0.18316, 0.65056, 0.08534, 0.71886, 0, 0.82847, 0, 0.95714, 0.04088, 1, 0.19206, 1, 0.42327, 0.96191, 0.52406, 0.88248, 0.72266, 0.71886, 0.83531, 0.56637, 0.93313, 0.3964, 1, 0.26455, 0.93016, 0.13111, 0.85013, 0.04375, 0.78491, 0.15061, 0.40645, 0.30489, 0.44544, 0.44311, 0.43045, 0.60543, 0.38246, 0.73079, 0.28949, 0.82721, 0.17553, 0.92364, 0.36147, 0.84168, 0.49343, 0.71471, 0.61938, 0.55721, 0.70036, 0.40132, 0.72435, 0.25507, 0.67637, 0.13132, 0.61939, 0.05626, 0.68961], "triangles": [33, 21, 32, 32, 21, 22, 29, 24, 25, 25, 26, 28, 22, 5, 23, 26, 9, 10, 7, 8, 25, 33, 1, 21, 1, 2, 21, 22, 21, 4, 21, 3, 4, 22, 4, 5, 23, 5, 6, 21, 2, 3, 24, 6, 7, 25, 8, 26, 26, 8, 9, 24, 7, 25, 23, 6, 24, 17, 18, 31, 19, 32, 18, 18, 32, 31, 20, 34, 19, 19, 33, 32, 19, 34, 33, 20, 0, 34, 0, 1, 34, 32, 22, 31, 31, 23, 30, 31, 22, 23, 30, 23, 24, 34, 1, 33, 17, 31, 16, 31, 30, 16, 16, 30, 15, 30, 29, 15, 15, 29, 14, 29, 28, 14, 14, 28, 13, 30, 24, 29, 29, 25, 28, 28, 27, 13, 13, 27, 12, 28, 26, 27, 27, 11, 12, 27, 26, 11, 26, 10, 11], "vertices": [3, 4, 91.11, 86.73, 0.03415, 5, 32.75, 95.6, 0.5285, 13, -28.01, 92.23, 0.43735, 3, 4, 107.87, 77.25, 0.01995, 5, 51.11, 89.8, 0.48612, 13, -9, 89.16, 0.49393, 3, 4, 126.6, 56.25, 0.00136, 5, 73.79, 73.14, 0.15316, 13, 15.86, 75.98, 0.84549, 1, 13, 26.54, 51.19, 1, 1, 13, 26.28, 27.38, 1, 1, 13, 18.16, -3.96, 1, 1, 13, 15.28, -27.36, 1, 1, 13, 22.47, -52.97, 1, 1, 13, 29.77, -68.52, 1, 1, 13, 26.33, -91.16, 1, 3, 4, 61.28, -125.42, 0.5304, 5, 47.58, -118.12, 0.2177, 13, 17.77, -117.06, 0.2519, 3, 4, 42.13, -125.05, 0.55449, 5, 28.77, -121.73, 0.20758, 13, -0.32, -123.38, 0.23793, 3, 4, 19.48, -112.51, 0.59527, 5, 4, -114.16, 0.19462, 13, -25.92, -119.49, 0.21011, 3, 4, 13.46, -100.07, 0.61847, 5, -4.46, -103.24, 0.18942, 13, -35.88, -109.92, 0.1921, 3, 4, 2.04, -74.77, 0.68632, 5, -20.88, -80.86, 0.17185, 13, -55.38, -90.17, 0.14182, 4, 4, 7.57, -38.75, 0.62666, 5, -22.94, -44.47, 0.09483, 13, -62.72, -54.47, 0.04651, 14, -99, -71.04, 0.232, 3, 4, 13.43, -5.56, 0.76638, 13, -68.76, -21.31, 0.00162, 14, -105.04, -37.88, 0.232, 4, 4, 24.09, 29.15, 0.59076, 5, -20.87, 25.37, 0.17034, 13, -70.83, 14.94, 0.00691, 14, -107.11, -1.64, 0.232, 4, 4, 44.28, 49.46, 0.24614, 5, -5.33, 49.43, 0.43622, 13, -58.96, 41, 0.08564, 14, -95.24, 24.43, 0.232, 4, 4, 65.64, 69.52, 0.074, 5, 11.4, 73.48, 0.44381, 13, -45.91, 67.23, 0.23419, 14, -82.19, 50.66, 0.248, 3, 4, 80.87, 81.95, 0.04525, 5, 23.73, 88.8, 0.54521, 13, -35.95, 84.19, 0.40954, 4, 4, 107.13, 41.88, 0.00454, 5, 57.73, 55.05, 0.214, 13, 2.6, 55.74, 0.678, 14, -33.67, 39.17, 0.10345, 4, 4, 87.69, 15.78, 0.00702, 5, 44.12, 25.49, 0.43188, 13, -6.56, 24.51, 0.3291, 14, -42.83, 7.94, 0.232, 4, 4, 75.16, -10.3, 0.00258, 5, 37.28, -2.62, 0.75624, 13, -9.23, -4.3, 0.00918, 14, -45.51, -20.87, 0.232, 4, 4, 63.43, -42.58, 0.20204, 5, 32.49, -36.64, 0.37667, 13, -9.02, -38.64, 0.18929, 14, -45.29, -55.22, 0.232, 3, 4, 59.84, -70.55, 0.17069, 5, 34.79, -64.74, 0.12155, 13, -2.66, -66.11, 0.70776, 3, 4, 61.25, -94.36, 0.20339, 5, 41.1, -87.74, 0.1026, 13, 6.94, -87.95, 0.69401, 3, 4, 33.26, -101.9, 0.58089, 5, 15.29, -100.92, 0.20352, 13, -16.68, -104.75, 0.21559, 3, 4, 28.63, -79.75, 0.61312, 5, 6.17, -80.22, 0.20822, 13, -28.72, -85.59, 0.17866, 4, 4, 29.15, -49.71, 0.50574, 5, 0.44, -50.72, 0.18101, 13, -38.68, -57.24, 0.08125, 14, -74.96, -73.81, 0.232, 4, 4, 37.16, -16.52, 0.48, 5, 1.39, -16.59, 0.27654, 13, -42.7, -23.33, 0.01146, 14, -78.98, -39.91, 0.232, 4, 4, 50.6, 13.29, 0.16934, 5, 8.36, 15.36, 0.58846, 13, -40.47, 9.28, 0.0102, 14, -76.74, -7.29, 0.232, 4, 4, 70.11, 37.42, 0.08942, 5, 22.44, 43.01, 0.50896, 13, -30.56, 38.69, 0.16962, 14, -66.84, 22.12, 0.232, 4, 4, 88.22, 56.96, 0.03807, 5, 36.1, 65.88, 0.45846, 13, -20.37, 63.31, 0.35947, 14, -56.65, 46.73, 0.144, 3, 4, 88.95, 74.49, 0.03832, 5, 33.17, 83.18, 0.53212, 13, -25.79, 80, 0.42956], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 4, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 22, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 209, "height": 112}}, "m2": {"m2": {"x": -13.68, "y": 33.47, "rotation": -107.01, "width": 50, "height": 69}}, "m3": {"m3": {"type": "mesh", "uvs": [0, 0.38995, 0.06696, 0.34016, 0.23234, 0.28063, 0.43205, 0.24275, 0.62633, 0.22326, 0.75466, 0.21785, 0.83779, 0.22651, 0.87304, 0.35748, 0.86942, 0.5101, 0.83779, 0.60319, 0.90286, 0.63566, 0.94533, 0.74499, 0.93991, 0.89761, 0.8649, 0.97121, 0.75104, 1, 0.57754, 1, 0.40765, 0.94956, 0.26306, 0.84998, 0.13022, 0.68437, 0.0263, 0.54474, 0, 0.46031, 0.09919, 0.46569, 0.26559, 0.41093, 0.47038, 0.37589, 0.69346, 0.38465, 0.70809, 0.53139, 0.49598, 0.52044, 0.28936, 0.55767, 0.18879, 0.62995, 0.28387, 0.73727, 0.41004, 0.68909, 0.54352, 0.66718, 0.70809, 0.63433, 0.81232, 0.75479, 0.77757, 0.8424, 0.61301, 0.86211, 0.40821, 0.78984, 0.60386, 0.76574, 0.71906, 0.73508], "triangles": [15, 35, 14, 14, 34, 13, 14, 35, 34, 13, 34, 12, 34, 33, 12, 12, 33, 11, 35, 38, 34, 34, 38, 33, 33, 10, 11, 33, 9, 10, 9, 33, 32, 33, 38, 32, 32, 25, 9, 9, 25, 8, 18, 19, 28, 19, 21, 28, 28, 21, 27, 21, 22, 27, 27, 23, 26, 27, 22, 23, 19, 20, 21, 21, 0, 1, 21, 2, 22, 21, 1, 2, 21, 20, 0, 22, 3, 23, 22, 2, 3, 23, 4, 24, 24, 4, 5, 23, 3, 4, 15, 16, 35, 16, 36, 35, 16, 17, 36, 35, 36, 37, 31, 37, 36, 36, 30, 31, 35, 37, 38, 17, 18, 29, 17, 29, 36, 29, 18, 28, 36, 29, 30, 38, 37, 32, 29, 27, 30, 29, 28, 27, 37, 31, 32, 30, 26, 31, 30, 27, 26, 32, 31, 25, 31, 26, 25, 8, 25, 7, 5, 7, 24, 26, 24, 25, 24, 7, 25, 26, 23, 24, 5, 6, 7], "vertices": [3, 4, 101.2, 75.43, 0.06608, 5, 44.96, 86.64, 0.93392, 6, -169.6, 86.61, 0, 3, 4, 102.05, 58.27, 0.05033, 5, 49.36, 70.03, 0.94967, 6, -154.69, 95.14, 0, 3, 4, 94.07, 21.48, 0.0046, 5, 49.18, 32.39, 0.99539, 6, -118.27, 104.69, 0, 3, 4, 79, -19.94, 0.01713, 5, 43.03, -11.26, 0.97982, 6, -74.52, 110.02, 0.00304, 3, 4, 61.58, -58.71, 0.31355, 5, 34.04, -52.8, 0.60234, 6, -32.06, 112.05, 0.08411, 3, 4, 48.89, -83.66, 0.41389, 5, 26.8, -79.84, 0.42061, 6, -4.07, 112.04, 0.1655, 3, 4, 38.73, -98.75, 0.43034, 5, 19.99, -96.71, 0.37477, 6, 13.99, 109.82, 0.19489, 3, 4, 14.16, -93.93, 0.44287, 5, -5.05, -97.09, 0.30494, 6, 20.81, 85.72, 0.25219, 3, 4, -9.76, -79.78, 0.41616, 5, -31.39, -88.21, 0.18171, 6, 19.03, 57.99, 0.40214, 3, 4, -21.24, -65.54, 0.2756, 5, -45.57, -76.66, 0.07431, 6, 11.54, 41.31, 0.6501, 3, 4, -33.28, -75.08, 0.10606, 5, -55.37, -88.49, 0.02363, 6, 25.5, 34.89, 0.87031, 3, 4, -55.17, -73.54, 0.01686, 5, -77.11, -91.53, 0.00473, 6, 34.05, 14.68, 0.97841, 1, 6, 31.87, -13.04, 1, 2, 4, -82.7, -38.26, 0.00048, 6, 15.05, -25.84, 0.99953, 2, 4, -75.25, -14, 0.09975, 6, -9.94, -30.19, 0.90025, 2, 4, -56.93, 19.08, 0.57323, 6, -47.74, -28.84, 0.42677, 3, 4, -30.95, 47.04, 0.90613, 5, -78.42, 31.45, 0.01047, 6, -84.42, -18.35, 0.0834, 3, 4, 0.17, 65.83, 0.84893, 5, -51.87, 56.29, 0.14928, 6, -115.28, 0.89, 0.00179, 3, 4, 40.57, 76.56, 0.42764, 5, -14.58, 75.17, 0.57236, 6, -143.14, 32.05, 0, 3, 4, 73.78, 84.06, 0.14452, 5, 16.35, 89.4, 0.85548, 6, -164.87, 58.25, 0, 3, 4, 90, 81.63, 0.08704, 5, 32.72, 90.39, 0.91296, 6, -170.05, 73.81, 0, 3, 4, 78.66, 63.19, 0.11124, 5, 25.46, 69.99, 0.88876, 6, -148.48, 72.06, 0, 3, 4, 69.81, 26.63, 0.06026, 5, 24.38, 32.39, 0.93974, 6, -111.87, 80.73, 0, 3, 4, 53.76, -15.51, 0.09193, 5, 17.42, -12.17, 0.90158, 6, -67.03, 85.51, 0.00649, 3, 4, 28.8, -57.28, 0.46602, 5, 1.67, -58.2, 0.36784, 6, -18.48, 82.18, 0.16615, 3, 4, 3.89, -47.13, 0.54944, 5, -24.8, -53.44, 0.14884, 6, -16.25, 55.37, 0.30172, 3, 4, 28.04, -7.65, 0.92127, 5, -9.37, -9.81, 0.07198, 6, -62.39, 59.02, 0.00675, 3, 4, 43.93, 35.04, 0.42178, 5, -2.68, 35.25, 0.57822, 6, -107.65, 53.85, 0, 3, 4, 43.05, 60.59, 0.43051, 5, -8.84, 60.06, 0.56949, 6, -130.03, 41.49, 0, 3, 4, 15.92, 51.92, 0.77213, 5, -33.58, 45.95, 0.22786, 6, -110.01, 21.23, 1e-05, 3, 4, 10.26, 23.61, 0.94631, 5, -33.24, 17.09, 0.05334, 6, -82.21, 29.01, 0.00035, 3, 4, -0.35, -3.78, 0.97827, 5, -37.94, -11.91, 0.00116, 6, -52.99, 31.96, 0.02057, 3, 4, -12.5, -38.06, 0.50178, 5, -42.72, -47.96, 0.05084, 6, -16.92, 36.65, 0.44738, 3, 4, -42.69, -47.31, 0.07359, 5, -70.33, -63.28, 0.00837, 6, 5, 13.93, 0.91804, 2, 4, -52.96, -32.96, 0.02248, 6, -3.13, -1.73, 0.97752, 2, 4, -38.72, 0.16, 0.55221, 6, -39.11, -4.04, 0.44779, 3, 4, -5.58, 32.84, 0.94701, 5, -50.66, 22.83, 0.03268, 6, -83.26, 10.7, 0.02031, 3, 4, -22.41, -6.59, 0.70314, 5, -58.94, -19.24, 0.00035, 6, -40.48, 13.56, 0.29651, 3, 4, -29.7, -31.27, 0.33241, 5, -60.95, -44.89, 0.01067, 6, -15.18, 18.24, 0.65692], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 70, 72, 74, 76], "width": 218, "height": 182}}, "m4": {"m4": {"x": 6.24, "y": -5.25, "rotation": -2.05, "width": 82, "height": 61}}, "m5": {"m5": {"type": "mesh", "uvs": [0, 0, 0.10477, 0.00942, 0.23203, 0.0408, 0.36554, 0.10656, 0.51027, 0.16335, 0.6575, 0.2052, 0.81471, 0.25452, 0.94572, 0.32625, 1, 0.39948, 1, 0.4488, 0.94572, 0.55192, 0.86961, 0.63113, 0.76231, 0.73575, 0.655, 0.82691, 0.54521, 0.91509, 0.43541, 0.98084, 0.30863, 1, 0.33092, 0.92901, 0.40771, 0.85038, 0.43496, 0.76285, 0.43867, 0.63082, 0.4845, 0.54329, 0.36312, 0.54181, 0.23432, 0.47802, 0.14143, 0.3712, 0.07331, 0.22582, 0.04234, 0.11752, 0, 0.04928, 0.56738, 0.47922, 0.68519, 0.43466, 0.8092, 0.41052, 0.92855, 0.39753, 0.89135, 0.47551, 0.79059, 0.50707, 0.68364, 0.56834, 0.58599, 0.66489, 0.52243, 0.78186, 0.45888, 0.87655, 0.38603, 0.94154, 0.77509, 0.3474, 0.65574, 0.32883, 0.52708, 0.36596, 0.38758, 0.32326, 0.26202, 0.24714, 0.14577, 0.13759, 0.06051, 0.05219], "triangles": [45, 27, 0, 1, 45, 0, 26, 27, 45, 44, 1, 2, 45, 1, 44, 26, 45, 44, 25, 26, 44, 44, 2, 43, 43, 25, 44, 43, 2, 3, 42, 3, 4, 43, 3, 42, 41, 42, 4, 43, 24, 25, 23, 24, 43, 23, 43, 42, 22, 23, 42, 21, 22, 42, 5, 41, 4, 5, 40, 41, 28, 41, 40, 41, 21, 42, 28, 21, 41, 21, 28, 35, 36, 19, 20, 35, 36, 20, 36, 35, 13, 37, 19, 36, 18, 19, 37, 14, 36, 13, 37, 36, 14, 38, 17, 18, 38, 18, 37, 15, 38, 37, 15, 37, 14, 16, 17, 38, 16, 38, 15, 34, 28, 29, 35, 28, 34, 20, 21, 35, 12, 34, 33, 12, 33, 11, 35, 34, 12, 13, 35, 12, 39, 40, 5, 6, 39, 5, 7, 30, 6, 30, 39, 6, 31, 7, 8, 7, 31, 30, 29, 40, 39, 29, 39, 30, 31, 8, 9, 32, 30, 31, 32, 31, 9, 28, 40, 29, 33, 29, 30, 33, 30, 32, 10, 32, 9, 34, 29, 33, 11, 33, 32, 11, 32, 10], "vertices": [1, 10, 65.68, 1.65, 1, 1, 10, 46.25, -10.48, 1, 2, 9, 69.42, -24.3, 0.06035, 10, 20.5, -22.28, 0.93965, 4, 7, 105.06, -70.55, 0.00089, 8, 79.48, -27.98, 0.00355, 9, 38.25, -28.63, 0.83772, 10, -10.04, -29.84, 0.15784, 3, 7, 75.07, -56.3, 0.1044, 8, 47.14, -35.49, 0.31371, 9, 5.79, -35.6, 0.58189, 3, 7, 44.2, -44.68, 0.60099, 8, 15.75, -45.62, 0.34456, 9, -25.77, -45.19, 0.05445, 2, 7, 11.34, -31.44, 0.98161, 8, -18.21, -55.7, 0.01839, 1, 7, -15.34, -14.87, 1, 2, 7, -25.39, -0.15, 0.99955, 11, -65.52, 39.85, 0.00045, 2, 7, -24.25, 8.75, 0.993, 11, -58.32, 45.22, 0.007, 2, 7, -10.13, 25.87, 0.88344, 11, -36.2, 46.94, 0.11656, 2, 7, 8.15, 38.07, 0.55504, 11, -14.73, 42.26, 0.44496, 3, 7, 33.76, 54, 0.06592, 11, 14.52, 34.88, 0.91493, 12, -24.66, 32.86, 0.01914, 2, 11, 41.8, 26.05, 0.48531, 12, 3.29, 26.43, 0.51469, 2, 11, 68.97, 16.45, 0.00343, 12, 31.19, 19.23, 0.99657, 1, 12, 56.05, 9.32, 1, 1, 12, 77.03, -9.01, 1, 1, 12, 64.15, -13.97, 1, 2, 12, 42.33, -10.99, 0.99987, 8, 1.46, 83.03, 0.00013, 3, 11, 61.12, -19.38, 0.01529, 12, 26.48, -17.15, 0.95951, 8, 4.63, 66.33, 0.0252, 4, 11, 41.37, -33.09, 0.25026, 12, 8, -32.52, 0.50943, 8, 16.4, 45.36, 0.23988, 9, -23.6, 45.76, 0.00042, 4, 11, 22.63, -34.61, 0.213, 12, -10.54, -35.66, 0.09201, 8, 16.11, 26.55, 0.63782, 9, -24.2, 26.96, 0.05717, 4, 11, 38.23, -55.98, 0.01166, 12, 6.86, -55.6, 0.00158, 8, 38.88, 40.04, 0.41686, 9, -1.21, 40.06, 0.5699, 3, 8, 68.91, 44.66, 0.0682, 9, 28.9, 44.18, 0.866, 10, -26.96, 41.6, 0.06581, 3, 8, 96.3, 38.53, 0.00092, 9, 56.18, 37.6, 0.53607, 10, 0.87, 37.89, 0.46301, 2, 9, 82.34, 22.22, 0.02525, 10, 28.49, 25.34, 0.97475, 1, 10, 45.59, 13.44, 1, 1, 10, 60.38, 8.88, 1, 3, 11, 2.48, -27.09, 0.09927, 12, -31.27, -29.92, 0.00551, 8, 6.7, 7.22, 0.89522, 2, 7, 43.5, -2.49, 0.99514, 8, -11.06, -13.03, 0.00486, 1, 7, 16.13, -3.42, 1, 2, 7, -9.98, -2.47, 0.99955, 11, -56.5, 27.15, 0.00045, 2, 7, -0.14, 10.58, 0.94406, 11, -40.27, 29.13, 0.05594, 2, 7, 22.38, 13.49, 0.75982, 11, -22.54, 14.95, 0.24018, 2, 7, 46.92, 21.6, 0.00828, 11, 0.34, 2.92, 0.99172, 3, 11, 27.15, -3.64, 0.95593, 12, -8.73, -4.42, 0.01348, 8, -14.28, 34.01, 0.03059, 2, 12, 16.38, -0.6, 0.99938, 8, -13.47, 59.4, 0.00062, 1, 12, 38.47, 0.51, 1, 1, 12, 57.86, -3.48, 1, 2, 7, 22.05, -15.76, 0.98546, 8, -19.59, -36.77, 0.01454, 3, 7, 47.43, -22.41, 0.69981, 8, 4.41, -26.17, 0.29071, 9, -36.78, -25.56, 0.00947, 3, 7, 76.11, -19.26, 0.03695, 8, 24.9, -5.86, 0.95589, 9, -15.96, -5.59, 0.00716, 2, 8, 54.93, 3.26, 0.00053, 9, 14.23, 3.02, 0.99947, 2, 9, 44.85, 4.85, 0.98103, 10, -6.98, 4.14, 0.01897, 1, 10, 25.25, 3.05, 1, 1, 10, 49.43, 1.5, 1], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 42, 56, 56, 58, 58, 60, 60, 62, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90], "width": 218, "height": 182}}, "t1": {"t1": {"type": "mesh", "uvs": [0.11018, 0.41671, 0.0817, 0.49018, 0.06436, 0.59416, 0.06683, 0.71477, 0.07055, 0.81459, 0.05569, 0.86173, 0, 0.90609, 0, 0.95046, 0.07922, 0.9768, 0.17583, 0.97125, 0.20803, 0.94907, 0.24395, 0.97818, 0.32322, 0.99898, 0.36905, 0.96155, 0.43097, 0.98096, 0.509, 1, 0.57217, 1, 0.59323, 0.96155, 0.65144, 0.98512, 0.72947, 0.98789, 0.77654, 0.93798, 0.80255, 0.87421, 0.86943, 0.89362, 0.93631, 0.8853, 0.94127, 0.84509, 0.9326, 0.79657, 0.96728, 0.78548, 1, 0.7425, 1, 0.7023, 0.97471, 0.70091, 0.94994, 0.63714, 0.93012, 0.52207, 0.8942, 0.39314, 0.85333, 0.32382, 0.88182, 0.18518, 0.88553, 0.06734, 0.82236, 0, 0.74929, 0.09091, 0.68736, 0.14914, 0.6502, 0.17548, 0.6019, 0.17132, 0.58084, 0.08814, 0.49786, 0.04655, 0.40125, 0.03546, 0.35542, 0.07705, 0.36036, 0.13578, 0.3574, 0.16896, 0.30997, 0.19136, 0.26105, 0.23283, 0.21288, 0.18638, 0.07429, 0.03789, 0.02241, 0.08683, 0.01574, 0.24445, 0.05873, 0.37635, 0.15457, 0.4287, 0.20005, 0.39759, 0.25564, 0.35234, 0.29101, 0.30143, 0.27417, 0.26278, 0.61358, 0.21504, 0.58318, 0.25459, 0.5955, 0.33001, 0.66206, 0.38059, 0.73273, 0.40635, 0.79764, 0.38703, 0.82969, 0.3576, 0.30097, 0.69116, 0.33016, 0.66049, 0.35697, 0.63782, 0.37901, 0.62515, 0.41058, 0.63648, 0.45765, 0.66715, 0.4785, 0.71183, 0.45943, 0.74117, 0.42965, 0.75451, 0.39867, 0.78251, 0.37126, 0.81585, 0.34863, 0.83853, 0.31884, 0.81119, 0.30037, 0.76518, 0.2962, 0.72584, 0.36843, 0.5426, 0.37743, 0.43769, 0.41192, 0.30341, 0.45316, 0.22284, 0.4869, 0.17416, 0.55213, 0.17333, 0.39992, 0.175, 0.54464, 0.1332, 0.50265, 0.10802, 0.43741, 0.09879, 0.38493, 0.10802, 0.4306, 0.54348, 0.48887, 0.58197, 0.56146, 0.66109, 0.56242, 0.73593, 0.52612, 0.77443, 0.48218, 0.80436, 0.43155, 0.83537, 0.38188, 0.88349, 0.31693, 0.88349, 0.26152, 0.82575, 0.23286, 0.74449, 0.24051, 0.64612, 0.29782, 0.567, 0.4774, 0.43335, 0.58726, 0.4975, 0.67896, 0.6365, 0.67418, 0.76373, 0.60923, 0.83858, 0.50797, 0.89097, 0.28636, 0.45473, 0.21089, 0.51247, 0.16218, 0.60442, 0.14116, 0.73059, 0.16313, 0.84179, 0.55955, 0.37027, 0.65794, 0.41517, 0.75347, 0.48681, 0.81269, 0.61618, 0.7955, 0.74984, 0.89484, 0.63757, 0.88051, 0.77657, 0.86427, 0.49857, 0.29018, 0.38417, 0.21854, 0.43549, 0.13257, 0.50285, 0.08958, 0.60336, 0.08862, 0.72311, 0.10677, 0.83323, 0.04659, 0.93053, 0.11633, 0.91129, 0.14307, 0.88563, 0.65508, 0.90487, 0.69711, 0.88669, 0.7506, 0.83751, 0.84803, 0.82575, 0.8872, 0.83751, 0.26534, 0.9177, 0.30546, 0.94657, 0.51149, 0.23609, 0.34896, 0.24244], "triangles": [88, 41, 40, 88, 89, 41, 45, 44, 91, 85, 90, 89, 87, 91, 90, 81, 82, 92, 93, 92, 105, 104, 111, 81, 112, 111, 104, 106, 93, 105, 94, 93, 106, 103, 112, 104, 113, 112, 103, 107, 94, 106, 114, 113, 103, 95, 94, 107, 102, 114, 103, 108, 95, 107, 109, 95, 108, 96, 95, 109, 110, 97, 96, 110, 96, 109, 98, 97, 110, 110, 99, 98, 69, 81, 92, 68, 104, 81, 70, 92, 93, 67, 103, 104, 71, 93, 94, 66, 102, 103, 72, 94, 95, 96, 72, 95, 97, 73, 96, 101, 102, 79, 97, 98, 74, 100, 101, 78, 99, 76, 98, 100, 77, 99, 70, 69, 92, 69, 68, 81, 68, 67, 104, 71, 70, 93, 66, 103, 67, 72, 71, 94, 73, 71, 72, 80, 102, 66, 75, 79, 80, 102, 80, 79, 73, 72, 96, 70, 68, 69, 80, 66, 67, 67, 75, 80, 76, 78, 79, 75, 76, 79, 101, 79, 78, 97, 74, 73, 75, 74, 98, 76, 75, 98, 77, 78, 76, 100, 78, 77, 77, 76, 99, 74, 70, 71, 74, 71, 73, 68, 70, 74, 74, 67, 68, 75, 67, 74, 115, 114, 102, 115, 102, 101, 138, 101, 100, 81, 111, 82, 92, 82, 105, 90, 43, 42, 91, 44, 43, 89, 42, 41, 90, 42, 89, 90, 91, 43, 85, 89, 88, 85, 88, 86, 87, 90, 85, 45, 91, 87, 58, 48, 47, 125, 55, 56, 57, 58, 141, 124, 56, 57, 54, 55, 125, 126, 0, 54, 59, 39, 62, 61, 60, 59, 61, 59, 62, 62, 39, 38, 117, 62, 63, 118, 63, 64, 65, 64, 38, 33, 38, 34, 37, 36, 35, 34, 37, 35, 34, 38, 37, 65, 38, 33, 86, 88, 40, 46, 45, 87, 84, 87, 85, 140, 85, 86, 84, 85, 140, 141, 47, 46, 141, 46, 87, 141, 87, 84, 59, 86, 40, 140, 86, 60, 58, 47, 141, 83, 141, 84, 116, 140, 60, 57, 141, 83, 124, 57, 83, 117, 116, 62, 140, 83, 84, 116, 83, 140, 116, 105, 83, 82, 124, 83, 125, 56, 124, 105, 82, 83, 111, 125, 124, 111, 124, 82, 117, 63, 118, 106, 116, 117, 105, 116, 106, 123, 64, 32, 118, 64, 123, 1, 0, 126, 126, 54, 125, 112, 126, 125, 112, 125, 111, 123, 32, 31, 127, 2, 1, 126, 127, 1, 113, 126, 112, 127, 126, 113, 119, 118, 123, 118, 106, 117, 107, 118, 119, 107, 106, 118, 121, 123, 31, 121, 31, 30, 119, 123, 121, 3, 2, 127, 128, 3, 127, 114, 128, 127, 113, 114, 127, 29, 28, 27, 120, 107, 119, 120, 119, 121, 108, 107, 120, 122, 120, 121, 121, 30, 29, 122, 121, 29, 29, 25, 122, 26, 29, 27, 26, 25, 29, 4, 3, 128, 136, 120, 122, 129, 128, 114, 4, 128, 129, 135, 108, 120, 135, 120, 136, 137, 122, 25, 136, 122, 137, 129, 114, 115, 137, 25, 24, 5, 4, 129, 21, 135, 136, 23, 137, 24, 132, 129, 115, 134, 108, 135, 109, 108, 134, 22, 136, 137, 22, 137, 23, 21, 136, 22, 133, 109, 134, 131, 129, 132, 5, 129, 131, 115, 101, 138, 10, 132, 115, 130, 6, 5, 130, 5, 131, 20, 135, 21, 134, 135, 20, 139, 138, 100, 138, 10, 115, 7, 6, 130, 13, 100, 99, 139, 100, 13, 17, 110, 109, 17, 109, 133, 9, 132, 10, 131, 132, 9, 8, 130, 131, 8, 131, 9, 7, 130, 8, 11, 10, 138, 11, 138, 139, 110, 14, 99, 13, 99, 14, 18, 17, 133, 19, 134, 20, 133, 134, 19, 18, 133, 19, 12, 139, 13, 11, 139, 12, 15, 110, 17, 14, 110, 15, 16, 15, 17, 59, 40, 39, 59, 60, 86, 116, 60, 61, 62, 116, 61, 32, 64, 65, 65, 33, 32, 38, 63, 62, 64, 63, 38, 56, 58, 57, 56, 49, 48, 56, 48, 58, 51, 49, 52, 55, 53, 49, 53, 52, 49, 56, 55, 49, 0, 53, 55, 49, 51, 50, 54, 0, 55], "vertices": [1, 13, 96.95, 56.35, 1, 2, 13, 85, 63.84, 0.9, 14, 48.73, 47.27, 0.1, 2, 13, 67.43, 69.96, 0.9, 14, 31.15, 53.39, 0.1, 2, 13, 46.36, 72.67, 0.9, 14, 10.09, 56.1, 0.1, 2, 13, 28.89, 74.58, 0.9, 14, -7.39, 58.01, 0.1, 1, 13, 21.12, 78.73, 1, 1, 13, 15.05, 90.74, 1, 1, 13, 7.33, 91.92, 1, 1, 13, 0.41, 77.18, 1, 1, 13, -1.49, 58.22, 1, 2, 13, 1.42, 51.36, 0.9, 14, -34.86, 34.79, 0.1, 2, 13, -4.71, 45.14, 0.9, 14, -40.99, 28.56, 0.1, 2, 13, -10.67, 30.25, 0.9, 14, -46.95, 13.67, 0.1, 2, 13, -5.52, 20.33, 0.9, 14, -41.79, 3.76, 0.1, 2, 13, -10.73, 8.78, 0.9, 14, -47, -7.79, 0.1, 2, 13, -16.35, -5.91, 0.9, 14, -52.62, -22.48, 0.1, 2, 13, -18.22, -18.21, 0.9, 14, -54.49, -34.79, 0.1, 2, 13, -12.15, -23.33, 0.9, 14, -48.42, -39.9, 0.1, 2, 13, -17.97, -34.05, 0.872, 14, -54.25, -50.62, 0.128, 2, 13, -20.76, -49.17, 0.9, 14, -57.04, -65.74, 0.1, 2, 13, -13.47, -59.66, 0.792, 14, -49.75, -76.23, 0.208, 2, 13, -3.14, -66.41, 0.848, 14, -39.42, -82.98, 0.152, 1, 13, -8.5, -78.92, 1, 1, 13, -9.03, -92.17, 1, 1, 13, -2.18, -94.2, 1, 1, 13, 6.52, -93.79, 1, 1, 13, 7.42, -100.84, 1, 1, 13, 13.93, -108.35, 1, 1, 13, 20.93, -109.41, 1, 1, 13, 21.92, -104.52, 1, 1, 13, 33.75, -101.38, 1, 1, 13, 54.36, -100.56, 1, 1, 13, 77.85, -96.97, 1, 1, 13, 91.12, -90.85, 1, 1, 13, 114.4, -100.06, 1, 1, 13, 134.8, -103.9, 1, 1, 13, 148.39, -93.37, 1, 1, 13, 134.73, -76.74, 1, 1, 13, 126.43, -63.14, 1, 1, 13, 122.95, -55.2, 1, 2, 13, 125.1, -45.91, 0.776, 14, 88.82, -62.48, 0.224, 2, 13, 140.2, -44, 0.416, 14, 103.92, -60.58, 0.584, 1, 14, 113.61, -45.51, 1, 1, 14, 118.4, -26.99, 1, 2, 13, 148.79, -0.39, 0.336, 14, 112.52, -16.96, 0.664, 2, 13, 138.43, 0.2, 0.592, 14, 102.15, -16.37, 0.408, 2, 13, 132.74, 1.65, 0.68, 14, 96.47, -14.92, 0.32, 2, 13, 130.25, 11.48, 0.792, 14, 93.97, -5.09, 0.208, 1, 13, 124.48, 22.1, 1, 1, 13, 133.99, 30.26, 1, 1, 13, 163.92, 53.33, 1, 1, 13, 156.94, 64.72, 1, 1, 13, 129.71, 70.19, 1, 1, 13, 105.49, 65.3, 1, 1, 13, 93.55, 48.02, 1, 1, 13, 97.61, 38.34, 1, 1, 13, 103.84, 26.32, 1, 1, 13, 111.66, 18.08, 1, 1, 13, 118.88, 20.34, 1, 1, 13, 117.14, -47.03, 1, 1, 13, 111.16, -40.06, 1, 1, 13, 97.68, -40.47, 1, 1, 13, 86.9, -52.09, 1, 1, 13, 80.33, -65.18, 1, 1, 13, 81.77, -78.33, 1, 1, 13, 85.95, -85.35, 1, 1, 14, 7.27, 9.87, 1, 1, 14, 11.74, 3.38, 1, 1, 14, 14.9, -2.44, 1, 1, 14, 16.45, -7.07, 1, 1, 14, 13.54, -12.92, 1, 1, 14, 6.81, -21.28, 1, 1, 14, -1.58, -24.16, 1, 1, 14, -6.12, -19.67, 1, 1, 14, -7.56, -13.52, 1, 1, 14, -11.52, -6.74, 1, 1, 14, -16.51, -0.52, 1, 1, 14, -19.78, 4.49, 1, 1, 14, -14.14, 9.56, 1, 1, 14, -5.59, 11.94, 1, 1, 14, 1.38, 11.72, 1, 2, 13, 67.4, 9.38, 0.3, 14, 31.12, -7.19, 0.7, 2, 13, 85.39, 4.85, 0.56, 14, 49.11, -11.72, 0.44, 2, 13, 107.73, -5.41, 0.8, 14, 71.46, -21.99, 0.2, 2, 13, 120.53, -15.58, 0.712, 14, 84.26, -32.15, 0.288, 2, 13, 128.01, -23.43, 0.632, 14, 91.73, -40.01, 0.368, 2, 13, 126.22, -36.16, 0.72, 14, 89.95, -52.73, 0.28, 2, 13, 130.43, -6.47, 0.656, 14, 94.16, -23.04, 0.344, 2, 13, 133.42, -35.76, 0.088, 14, 97.15, -52.33, 0.912, 1, 14, 102.77, -44.82, 1, 1, 14, 106.31, -32.36, 1, 1, 14, 106.25, -21.89, 1, 2, 13, 65.41, -2.71, 0.3, 14, 29.13, -19.28, 0.7, 2, 13, 56.99, -13.04, 0.3, 14, 20.71, -29.61, 0.7, 2, 13, 41.07, -25.09, 0.3, 14, 4.8, -41.66, 0.7, 2, 13, 28.02, -23.29, 0.3, 14, -8.26, -39.87, 0.7, 2, 13, 22.4, -15.21, 0.3, 14, -13.88, -31.78, 0.7, 2, 13, 18.49, -5.86, 0.3, 14, -17.79, -22.43, 0.7, 2, 13, 14.59, 4.82, 0.3, 14, -21.69, -11.75, 0.7, 2, 13, 7.69, 15.77, 0.3, 14, -28.59, -0.8, 0.7, 2, 13, 9.61, 28.42, 0.3, 14, -26.67, 11.85, 0.7, 2, 13, 21.29, 37.68, 0.3, 14, -14.98, 21.11, 0.7, 2, 13, 36.28, 41.12, 0.3, 14, 0.01, 24.55, 0.7, 2, 13, 53.17, 37.03, 0.3, 14, 16.9, 20.46, 0.7, 2, 13, 65.24, 23.78, 0.3, 14, 28.97, 7.2, 0.7, 2, 13, 83.19, -14.73, 0.5, 14, 46.91, -31.31, 0.5, 2, 13, 68.77, -34.43, 0.5, 14, 32.5, -51.01, 0.5, 2, 13, 41.87, -48.62, 0.5, 14, 5.6, -65.19, 0.5, 2, 13, 19.88, -44.33, 0.5, 14, -16.4, -60.9, 0.5, 2, 13, 8.78, -29.7, 0.5, 14, -27.5, -46.27, 0.5, 2, 13, 2.65, -8.59, 0.5, 14, -33.62, -25.16, 0.5, 2, 13, 85.12, 23.04, 0.5, 14, 48.84, 6.47, 0.5, 2, 13, 77.3, 39.26, 0.5, 14, 41.03, 22.69, 0.5, 2, 13, 62.74, 51.18, 0.5, 14, 26.47, 34.61, 0.5, 2, 13, 41.41, 58.61, 0.5, 14, 5.14, 42.04, 0.5, 2, 13, 21.41, 57.27, 0.6, 14, -14.86, 40.7, 0.4, 2, 13, 91.73, -32.4, 0.8, 14, 55.46, -48.97, 0.2, 2, 13, 81.01, -50.38, 0.8, 14, 44.73, -66.95, 0.2, 2, 13, 65.72, -67.09, 0.8, 14, 29.44, -83.66, 0.2, 2, 13, 41.45, -75.2, 0.8, 14, 5.18, -91.78, 0.2, 2, 13, 18.71, -68.32, 0.8, 14, -17.57, -84.89, 0.2, 1, 13, 35.3, -90.64, 1, 1, 13, 11.54, -84.17, 1, 1, 13, 60.39, -88.36, 1, 2, 13, 97.28, 20.43, 0.8, 14, 61.01, 3.86, 0.2, 2, 13, 90.47, 35.74, 0.8, 14, 54.2, 19.17, 0.2, 2, 13, 81.3, 54.27, 0.8, 14, 45.02, 37.69, 0.2, 2, 13, 65.08, 65.29, 0.8, 14, 28.8, 48.72, 0.2, 2, 13, 44.27, 68.65, 0.8, 14, 7.99, 52.07, 0.2, 2, 13, 24.57, 68.02, 0.9, 14, -11.71, 51.45, 0.1, 1, 13, 9.42, 82.31, 1, 1, 13, 10.71, 68.22, 1, 2, 13, 14.38, 62.34, 0.9, 14, -21.9, 45.76, 0.1, 2, 13, -4.12, -36.88, 0.6, 14, -40.39, -53.45, 0.4, 2, 13, -2.2, -45.54, 0.6, 14, -38.47, -62.11, 0.4, 2, 13, 4.78, -57.26, 0.68, 14, -31.5, -73.83, 0.32, 1, 13, 3.94, -76.55, 1, 1, 13, 0.74, -83.87, 1, 2, 13, 5.18, 39.37, 0.6, 14, -31.09, 22.8, 0.4, 2, 13, -1.03, 32.32, 0.6, 14, -37.3, 15.75, 0.4, 2, 13, 116.5, -26.59, 0.816, 14, 80.23, -43.16, 0.184, 2, 13, 120.21, 5.24, 0.872, 14, 83.93, -11.34, 0.128], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 0, 106, 0, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 96, 78, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 66, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 132, 138, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 170, 174, 176, 178, 178, 180, 180, 182, 162, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 164, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 164, 222, 222, 224, 224, 226, 226, 228, 228, 230, 232, 234, 234, 236, 236, 238, 238, 240, 242, 244, 166, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 260, 262, 262, 264, 266, 268, 268, 270, 272, 274, 276, 278, 168, 280, 168, 282], "width": 197, "height": 176}}, "w1": {"w1": {"type": "mesh", "uvs": [0, 0.98364, 0, 0.95844, 0.06197, 0.90992, 0.16161, 0.88476, 0.28544, 0.84522, 0.39931, 0.79669, 0.49752, 0.723, 0.56869, 0.63135, 0.65978, 0.53609, 0.71814, 0.43904, 0.64982, 0.36715, 0.60285, 0.24314, 0.63416, 0.10116, 0.72668, 0.00411, 0.82632, 0, 0.96296, 0.03287, 1, 0.14609, 1, 0.27909, 0.95442, 0.41029, 0.84482, 0.43365, 0.82632, 0.55586, 0.77365, 0.67448, 0.7092, 0.71474, 0.63843, 0.75895, 0.54307, 0.85959, 0.44486, 0.93867, 0.33383, 0.96743, 0.19435, 0.98001, 0.07905, 0.99259, 0, 1, 0.06584, 0.95822, 0.15192, 0.93718, 0.25327, 0.91439, 0.35809, 0.88722, 0.43653, 0.8583, 0.53072, 0.78069, 0.60099, 0.69923, 0.67472, 0.63232, 0.745, 0.54359, 0.78417, 0.42576, 0.8026, 0.32975, 0.8867, 0.27448, 0.9132, 0.13629, 0.80836, 0.11447, 0.70583, 0.17265, 0.71735, 0.27302, 0.80375, 0.20902], "triangles": [43, 13, 14, 42, 14, 15, 43, 14, 42, 42, 15, 16, 44, 12, 13, 44, 13, 43, 46, 44, 43, 46, 43, 42, 11, 12, 44, 45, 44, 46, 11, 44, 45, 41, 46, 42, 42, 16, 17, 41, 42, 17, 40, 46, 41, 18, 41, 17, 40, 45, 46, 10, 11, 45, 40, 9, 45, 9, 10, 45, 19, 40, 41, 19, 41, 18, 39, 40, 19, 40, 39, 9, 38, 9, 39, 8, 9, 38, 20, 39, 19, 38, 39, 20, 21, 38, 20, 37, 8, 38, 7, 8, 37, 37, 38, 21, 36, 7, 37, 22, 37, 21, 23, 36, 37, 6, 7, 36, 22, 23, 37, 35, 36, 23, 35, 6, 36, 34, 5, 6, 34, 6, 35, 24, 35, 23, 34, 35, 24, 33, 5, 34, 25, 34, 24, 33, 34, 25, 33, 4, 5, 32, 3, 4, 32, 4, 33, 31, 3, 32, 26, 32, 33, 26, 33, 25, 27, 31, 32, 27, 32, 26, 31, 2, 3, 30, 2, 31, 1, 2, 30, 0, 1, 30, 28, 30, 31, 28, 31, 27, 29, 0, 30, 29, 30, 28], "vertices": [1, 17, -11.09, -2.88, 1, 1, 17, -11.09, 2.59, 1, 2, 17, 5.9, 13.12, 0.92518, 18, -66.88, 0.04, 0.07482, 2, 17, 33.2, 18.58, 0.47028, 18, -39.58, 5.5, 0.52972, 3, 17, 67.13, 27.16, 0.02246, 18, -5.65, 14.08, 0.95591, 19, -56.26, -8.1, 0.02163, 2, 18, 25.55, 24.61, 0.35487, 19, -25.06, 2.43, 0.64513, 3, 18, 52.46, 40.6, 0.00031, 19, 1.85, 18.42, 0.80993, 20, -43.64, -16.26, 0.18976, 2, 19, 21.35, 38.31, 0.23224, 20, -24.14, 3.63, 0.76776, 2, 20, 0.82, 24.3, 0.71342, 21, -33.86, -21.19, 0.28658, 3, 20, 16.81, 45.36, 0.09847, 21, -17.87, -0.13, 0.86382, 22, -36.63, -68.36, 0.03771, 2, 21, -36.59, 15.47, 0.74429, 22, -55.35, -52.76, 0.25571, 2, 21, -49.46, 42.38, 0.5224, 22, -68.22, -25.85, 0.4776, 2, 21, -40.88, 73.19, 0.29076, 22, -59.64, 4.96, 0.70924, 2, 21, -15.53, 94.25, 0.08273, 22, -34.29, 26.02, 0.91727, 2, 21, 11.77, 95.15, 0.0029, 22, -6.99, 26.92, 0.9971, 2, 21, 49.21, 88.01, 0.00165, 22, 30.45, 19.78, 0.99835, 2, 21, 59.36, 63.44, 0.08087, 22, 40.6, -4.79, 0.91913, 2, 21, 59.36, 34.58, 0.30928, 22, 40.6, -33.65, 0.69072, 2, 21, 46.87, 6.11, 0.56597, 22, 28.11, -62.12, 0.43403, 3, 20, 51.52, 46.53, 0.00243, 21, 16.84, 1.04, 0.87909, 22, -1.92, -67.19, 0.11849, 2, 20, 46.45, 20.01, 0.27396, 21, 11.77, -25.48, 0.72604, 2, 20, 32.02, -5.73, 0.73227, 21, -2.66, -51.22, 0.26773, 3, 19, 59.85, 20.22, 0.01234, 20, 14.36, -14.47, 0.9317, 21, -20.32, -59.95, 0.05596, 2, 19, 40.46, 10.62, 0.27745, 20, -5.03, -24.06, 0.72255, 3, 18, 64.94, 10.96, 0.00469, 19, 14.33, -11.22, 0.92395, 20, -31.16, -45.9, 0.07136, 2, 18, 38.03, -6.2, 0.36202, 19, -12.58, -28.38, 0.63798, 3, 17, 80.39, 0.64, 0.00056, 18, 7.61, -12.44, 0.93118, 19, -43, -34.62, 0.06827, 2, 17, 42.17, -2.09, 0.36878, 18, -30.61, -15.17, 0.63122, 2, 17, 10.58, -4.82, 0.91777, 18, -62.2, -17.9, 0.08223, 2, 17, -11.09, -6.43, 0.99999, 18, -83.86, -19.51, 1e-05, 2, 17, 6.95, 2.64, 0.94705, 18, -65.82, -10.44, 0.05295, 2, 17, 30.54, 7.2, 0.55804, 18, -42.24, -5.88, 0.44196, 2, 17, 58.31, 12.15, 0.11348, 18, -14.47, -0.93, 0.88652, 2, 18, 14.25, 4.96, 0.75866, 19, -36.35, -17.21, 0.24134, 2, 18, 35.74, 11.24, 0.26991, 19, -14.86, -10.93, 0.73009, 2, 19, 10.95, 5.91, 0.82937, 20, -34.54, -28.78, 0.17063, 2, 19, 30.2, 23.58, 0.28412, 20, -15.28, -11.1, 0.71588, 2, 20, 4.92, 3.42, 0.93632, 21, -29.77, -42.07, 0.06368, 2, 20, 24.17, 22.67, 0.4247, 21, -10.51, -22.81, 0.5753, 2, 21, 0.22, 2.76, 0.98075, 22, -18.54, -65.47, 0.01925, 2, 21, 5.27, 23.59, 0.69832, 22, -13.49, -44.64, 0.30168, 2, 21, 28.32, 35.58, 0.39424, 22, 9.55, -32.65, 0.60576, 2, 21, 35.58, 65.57, 0.04103, 22, 16.81, -2.66, 0.95897, 2, 21, 6.85, 70.31, 0.04868, 22, -11.91, 2.08, 0.95132, 2, 21, -21.24, 57.68, 0.33354, 22, -40.01, -10.55, 0.66646, 2, 21, -18.09, 35.9, 0.5825, 22, -36.85, -32.33, 0.4175, 2, 21, 5.59, 49.79, 0.28492, 22, -13.18, -18.44, 0.71508], "hull": 30, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 2, 0, 0, 58, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90], "width": 274, "height": 217}}, "w2": {"w2": {"x": -21.15, "y": 32.45, "width": 78, "height": 80}}, "w3": {"w3": {"x": -1.7, "y": 3.46, "width": 63, "height": 31}}, "w4": {"w4": {"x": -2.71, "y": 1.32, "width": 56, "height": 49}}, "w5": {"w5": {"type": "mesh", "uvs": [0.04634, 0.05726, 0.19875, 0, 0.44575, 0, 0.60078, 0.02241, 0.8399, 0.10424, 0.95026, 0.25123, 0.92661, 0.39367, 0.89508, 0.55732, 0.90822, 0.69522, 0.98179, 0.8316, 1, 0.9498, 0.88457, 1, 0.68749, 1, 0.54823, 0.91949, 0.40633, 0.78008, 0.43524, 0.63764, 0.59027, 0.52853, 0.66384, 0.4467, 0.451, 0.43004, 0.19087, 0.39215, 0.00956, 0.28305, 0, 0.14818, 0.23316, 0.12741, 0.47959, 0.13925, 0.73515, 0.22742, 0.79219, 0.41295, 0.5777, 0.31689, 0.3404, 0.28268, 0.17155, 0.21689, 0.75414, 0.5522, 0.67656, 0.66932, 0.63777, 0.79432, 0.7473, 0.92722, 0.7838, 0.79959, 0.78609, 0.67721], "triangles": [22, 1, 2, 0, 1, 22, 22, 2, 23, 21, 0, 22, 28, 21, 22, 28, 22, 27, 20, 21, 28, 20, 28, 19, 23, 2, 3, 4, 23, 3, 24, 23, 4, 24, 4, 5, 27, 22, 23, 26, 27, 23, 24, 26, 23, 19, 28, 27, 5, 25, 24, 25, 26, 24, 5, 6, 25, 18, 27, 26, 17, 18, 26, 19, 27, 18, 25, 17, 26, 7, 25, 6, 29, 17, 25, 16, 17, 29, 29, 25, 7, 30, 16, 29, 15, 16, 30, 34, 29, 7, 30, 29, 34, 34, 7, 8, 14, 15, 30, 31, 14, 30, 33, 30, 34, 33, 34, 8, 31, 30, 33, 33, 8, 9, 13, 14, 31, 32, 31, 33, 32, 33, 9, 13, 31, 32, 32, 9, 10, 12, 13, 32, 11, 32, 10, 12, 32, 11], "vertices": [2, 29, -49.88, 34.91, 0.02334, 30, -12.93, -3.75, 0.97666, 2, 29, -35.56, 44.25, 0.00862, 30, 1.4, 5.58, 0.99138, 2, 29, -12.34, 44.25, 0.37727, 30, 24.62, 5.58, 0.62273, 2, 29, 2.23, 40.6, 0.63018, 30, 39.19, 1.93, 0.36982, 3, 28, 22.44, 82.41, 0.00548, 29, 24.71, 27.26, 0.91668, 30, 61.67, -11.41, 0.07784, 3, 28, 32.81, 58.45, 0.0914, 29, 35.08, 3.3, 0.90754, 30, 72.04, -35.37, 0.00105, 2, 28, 30.59, 35.23, 0.33259, 29, 32.86, -19.92, 0.66741, 3, 27, 12.27, 54.04, 0.03831, 28, 27.62, 8.56, 0.7662, 29, 29.9, -46.6, 0.19549, 3, 27, 13.51, 31.57, 0.40211, 28, 28.86, -13.92, 0.58798, 29, 31.13, -69.07, 0.00991, 2, 27, 20.42, 9.34, 0.89615, 28, 35.77, -36.15, 0.10385, 2, 27, 22.13, -9.93, 0.99962, 28, 37.49, -55.42, 0.00038, 1, 27, 11.28, -18.11, 1, 2, 27, -7.24, -18.11, 0.99978, 28, 8.11, -63.6, 0.00022, 2, 27, -20.33, -4.99, 0.89244, 28, -4.98, -50.48, 0.10756, 2, 27, -33.67, 17.74, 0.4291, 28, -18.32, -27.75, 0.5709, 2, 27, -30.95, 40.95, 0.07326, 28, -15.6, -4.53, 0.92674, 2, 28, -1.03, 13.25, 0.87527, 29, 1.25, -41.9, 0.12473, 3, 28, 5.89, 26.59, 0.42979, 29, 8.16, -28.56, 0.57007, 30, 45.12, -67.23, 0.00014, 3, 28, -14.12, 29.31, 0.0626, 29, -11.85, -25.85, 0.87977, 30, 25.11, -64.51, 0.05763, 3, 28, -38.57, 35.48, 0.00051, 29, -36.3, -19.67, 0.6892, 30, 0.66, -58.34, 0.3103, 2, 29, -53.34, -1.89, 0.40605, 30, -16.38, -40.55, 0.59395, 2, 29, -54.24, 20.09, 0.15403, 30, -17.28, -18.57, 0.84597, 2, 29, -32.32, 23.48, 0.22089, 30, 4.64, -15.18, 0.77911, 2, 29, -9.16, 21.55, 0.6356, 30, 27.8, -17.11, 0.3644, 3, 28, 12.59, 62.33, 0.03118, 29, 14.86, 7.18, 0.95098, 30, 51.82, -31.48, 0.01784, 2, 28, 17.95, 32.09, 0.37864, 29, 20.23, -23.06, 0.62136, 3, 28, -2.21, 47.75, 0.04407, 29, 0.06, -7.41, 0.95127, 30, 37.02, -46.07, 0.00466, 3, 28, -24.52, 53.32, 0.00049, 29, -22.24, -1.83, 0.7226, 30, 14.72, -40.49, 0.27691, 2, 29, -38.11, 8.9, 0.37831, 30, -1.16, -29.77, 0.62169, 3, 27, -0.98, 54.88, 0.01626, 28, 14.37, 9.39, 0.81031, 29, 16.65, -45.76, 0.17343, 3, 27, -8.27, 35.79, 0.1744, 28, 7.08, -9.7, 0.82307, 29, 9.36, -64.85, 0.00253, 2, 27, -11.92, 15.41, 0.62066, 28, 3.44, -30.07, 0.37934, 1, 27, -1.62, -6.25, 1, 2, 27, 1.81, 14.56, 0.74287, 28, 17.16, -30.93, 0.25713, 3, 27, 2.03, 34.5, 0.27302, 28, 17.38, -10.98, 0.71429, 29, 19.65, -66.14, 0.0127], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 50, 58, 58, 60, 60, 62, 62, 64], "width": 94, "height": 163}}, "w6": {"w6": {"x": 4.3, "y": 5.77, "width": 45, "height": 41}}, "x1": {"x1": {"type": "mesh", "uvs": [0.16632, 0, 0.28107, 0, 0.39218, 0.05485, 0.58707, 0.05035, 0.77832, 0.08185, 0.9896, 0.16285, 1, 0.3046, 0.94225, 0.4711, 0.84025, 0.59035, 0.72186, 0.6556, 0.61621, 0.63985, 0.52332, 0.5116, 0.39946, 0.38335, 0.4705, 0.55435, 0.5215, 0.69835, 0.50693, 0.8491, 0.38853, 0.9481, 0.24464, 0.98635, 0.08253, 1, 0, 0.9121, 0, 0.7276, 0.02789, 0.52735, 0.08071, 0.3046, 0.15357, 0.2191, 0.13171, 0.0931, 0.25866, 0.37294, 0.43458, 0.24136, 0.27964, 0.18354, 0.20701, 0.05794, 0.23122, 0.53243, 0.23122, 0.7657, 0.58629, 0.26927, 0.75575, 0.37493], "triangles": [8, 9, 32, 9, 10, 32, 10, 11, 32, 8, 32, 7, 7, 32, 6, 32, 5, 6, 32, 4, 5, 32, 31, 4, 11, 12, 31, 11, 31, 32, 31, 12, 26, 26, 12, 27, 31, 3, 4, 31, 26, 3, 26, 2, 3, 17, 18, 30, 18, 19, 30, 17, 30, 16, 16, 30, 14, 15, 16, 14, 30, 13, 14, 19, 20, 30, 20, 21, 30, 29, 13, 30, 29, 12, 13, 29, 25, 12, 30, 21, 29, 25, 29, 22, 29, 21, 22, 22, 23, 25, 12, 25, 27, 25, 23, 27, 27, 2, 26, 23, 28, 27, 23, 24, 28, 27, 1, 2, 27, 28, 1, 24, 0, 28, 28, 0, 1], "vertices": [2, 46, -8.59, 15.59, 0.99644, 52, -27.07, 12.66, 0.00356, 2, 46, 1.05, 15.24, 0.9033, 52, -17.6, 14.43, 0.0967, 2, 46, 10.24, 11.18, 0.51154, 52, -7.74, 12.48, 0.48846, 3, 46, 26.61, 10.9, 0.01616, 52, 8.3, 15.78, 0.94547, 53, -14.86, 11.39, 0.03837, 2, 52, 24.48, 16.62, 0.33548, 53, 0.07, 17.71, 0.66452, 2, 52, 42.94, 14.46, 0.00192, 53, 18.15, 21.99, 0.99808, 1, 53, 23.8, 14.13, 1, 1, 53, 25.38, 1.91, 1, 2, 52, 35.94, -16.41, 0.02675, 53, 22.12, -9.42, 0.97325, 2, 52, 26.98, -22.6, 0.16315, 53, 15.81, -18.3, 0.83685, 2, 52, 18.06, -23.18, 0.32863, 53, 7.63, -21.89, 0.67137, 3, 50, 6.58, 23, 0.00981, 52, 8.79, -16.03, 0.70021, 53, -3.53, -18.34, 0.28998, 5, 46, 10.05, -11.16, 0.05448, 50, -1.48, 12.08, 0.43315, 51, -15.47, 14.19, 0.00457, 52, -3.04, -9.37, 0.50327, 53, -16.92, -16.12, 0.00452, 3, 50, 9.76, 18.75, 0.68044, 51, -3.46, 19.36, 0.27767, 52, 4.96, -19.71, 0.04189, 3, 50, 19.27, 23.63, 0.38667, 51, 6.6, 22.97, 0.60926, 52, 10.97, -28.55, 0.00407, 2, 50, 29.58, 23.04, 0.19989, 51, 16.74, 21.06, 0.80011, 2, 50, 36.91, 13.52, 0.04868, 51, 22.79, 10.68, 0.95132, 1, 51, 24.57, -1.55, 1, 1, 51, 24.58, -15.2, 1, 2, 50, 36.47, -19.2, 0.01198, 51, 18.14, -21.71, 0.98802, 2, 50, 23.94, -19.97, 0.18395, 51, 5.63, -20.87, 0.81605, 3, 46, -21.49, -19.83, 0.00864, 50, 10.21, -18.47, 0.72022, 51, -7.8, -17.61, 0.27114, 3, 46, -16.51, -4.86, 0.28062, 50, -5.18, -14.97, 0.7172, 51, -22.61, -12.16, 0.00218, 2, 46, -10.19, 0.74, 0.73046, 50, -11.36, -9.22, 0.26954, 2, 46, -11.72, 9.36, 0.98932, 50, -19.8, -11.58, 0.01068, 3, 46, -1.74, -10.03, 0.08135, 50, -1.46, 0.23, 0.90805, 52, -14.79, -10.85, 0.0106, 3, 46, 13.35, -1.62, 0.08183, 50, -11.3, 14.43, 0.01031, 52, -1.91, 0.66, 0.90786, 2, 46, 0.48, 2.78, 0.95357, 52, -15.42, 2.14, 0.04643, 2, 46, -5.31, 11.53, 0.98601, 52, -22.99, 9.41, 0.01399, 1, 50, 9.51, -1.4, 1, 1, 51, 9.52, -1.66, 1, 1, 52, 10.97, 1.13, 1, 2, 52, 26.28, -3.32, 0.00266, 53, 8.56, -0.42, 0.99734], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 46, 50, 50, 24, 24, 52, 52, 4, 0, 56, 50, 58, 58, 60, 52, 62, 62, 64], "width": 84, "height": 68}}, "x2": {"x2": {"type": "mesh", "uvs": [0.47484, 0, 0.60335, 0.0793, 0.73371, 0.16018, 0.86036, 0.28706, 0.94416, 0.42503, 1, 0.56142, 0.98327, 0.69305, 0.87339, 0.82469, 0.7095, 0.91509, 0.53444, 0.97852, 0.3184, 0.98011, 0.26626, 0.97376, 0.21411, 0.89605, 0.23832, 0.76759, 0.32213, 0.7898, 0.50464, 0.81041, 0.64805, 0.76284, 0.77096, 0.67719, 0.8287, 0.56459, 0.79704, 0.44248, 0.69274, 0.31084, 0.61266, 0.203, 0.54003, 0.11736, 0.43573, 0.03806, 0.42456, 0.00476, 0.57411, 0.10477, 0.68729, 0.19252, 0.78696, 0.30185, 0.87649, 0.43563, 0.92041, 0.56798, 0.87987, 0.68738, 0.73633, 0.80511, 0.59665, 0.8698, 0.37857, 0.89066], "triangles": [10, 33, 9, 10, 11, 33, 11, 12, 33, 12, 14, 33, 12, 13, 14, 33, 14, 15, 9, 32, 8, 9, 33, 32, 8, 32, 31, 33, 15, 32, 32, 16, 31, 32, 15, 16, 8, 31, 7, 7, 30, 6, 7, 31, 30, 31, 17, 30, 31, 16, 17, 30, 29, 6, 6, 29, 5, 17, 18, 30, 30, 18, 29, 18, 28, 29, 29, 4, 5, 29, 28, 4, 18, 19, 28, 20, 27, 19, 19, 27, 28, 27, 3, 28, 28, 3, 4, 20, 26, 27, 27, 2, 3, 21, 26, 20, 26, 2, 27, 22, 25, 21, 21, 25, 26, 25, 1, 26, 26, 1, 2, 22, 23, 25, 25, 24, 0, 25, 0, 1, 25, 23, 24], "vertices": [1, 61, -6.98, 2.68, 1, 1, 61, 10.14, 5.17, 1, 1, 61, 27.55, 7.66, 1, 1, 62, 11.01, 8.81, 1, 2, 62, 30.86, 7.65, 0.76979, 63, -4.85, 6.02, 0.23021, 1, 63, 12.64, 12.02, 1, 2, 63, 29.48, 10.12, 0.8255, 64, -7.44, 6.87, 0.1745, 1, 64, 12.68, 11.62, 1, 2, 64, 33.74, 8.53, 0.19079, 65, 1.13, 10.5, 0.80921, 1, 65, 21.43, 14.74, 1, 2, 65, 44.56, 10.34, 0.11812, 66, 3.27, 13.68, 0.88188, 2, 65, 49.98, 8.43, 0.01766, 66, 8.93, 14.67, 0.98234, 1, 66, 17.41, 6.96, 1, 1, 66, 19.98, -9.49, 1, 2, 65, 39.41, -13.47, 0.06, 66, 10.41, -9.61, 0.94, 2, 65, 20.41, -7, 0.99278, 66, -9.32, -13.25, 0.00722, 2, 64, 25.91, -10.54, 0.42307, 65, 3.89, -9.92, 0.57693, 2, 63, 27.34, -13.01, 0.22355, 64, 8.61, -9.93, 0.77645, 3, 62, 39.79, -12.3, 0.00378, 63, 12.96, -6.65, 0.98762, 64, -5.63, -16.61, 0.00861, 2, 62, 24.59, -7.28, 0.8993, 63, -2.69, -10.03, 0.1007, 2, 61, 37.79, -9.28, 0.2276, 62, 4.3, -8.45, 0.7724, 1, 61, 21.91, -5.47, 1, 1, 61, 8.59, -3.14, 1, 1, 61, -6.63, -3.79, 1, 1, 61, -10.48, -1.58, 1, 1, 61, 10.13, 0.61, 1, 1, 61, 26.8, 1.16, 1, 1, 62, 8.55, 0.97, 1, 1, 62, 28.26, 0.61, 1, 1, 63, 13.44, 3.34, 1, 2, 63, 28.7, -1.15, 0.47076, 64, 0.55, -1.12, 0.52924, 2, 64, 22.25, -0.12, 0.99852, 65, -4.49, -2.73, 0.00148, 1, 65, 12.06, 2.41, 1, 2, 65, 35.9, 0.39, 0.30699, 66, 0.57, 0.77, 0.69301], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 109, "height": 128}}, "x3": {"x3": {"type": "mesh", "uvs": [0.45936, 0, 0.62536, 0.05966, 0.77936, 0.19028, 0.93336, 0.38794, 0.98136, 0.61481, 0.97936, 0.82965, 0.98136, 1, 0.80136, 0.94137, 0.62736, 0.85715, 0.69536, 0.75231, 0.71936, 0.56497, 0.65736, 0.37934, 0.58536, 0.22122, 0.48336, 0.12325, 0.39936, 0.02356, 0.39136, 0, 0.44751, 0.02315, 0.57333, 0.09966, 0.68812, 0.20652, 0.79407, 0.37725, 0.84048, 0.58896, 0.83397, 0.80161], "triangles": [21, 8, 9, 7, 21, 5, 7, 8, 21, 7, 5, 6, 3, 19, 2, 10, 11, 19, 20, 19, 3, 10, 19, 20, 20, 3, 4, 9, 10, 20, 21, 9, 20, 21, 20, 4, 5, 21, 4, 16, 15, 0, 14, 15, 16, 17, 0, 1, 16, 0, 17, 13, 16, 17, 14, 16, 13, 18, 1, 2, 17, 1, 18, 12, 17, 18, 13, 17, 12, 18, 2, 19, 11, 12, 18, 11, 18, 19], "vertices": [1, 55, -7.35, 0.41, 1, 1, 55, 1.26, 5.29, 1, 2, 55, 13.05, 6.87, 0.95272, 56, -12.07, 0.29, 0.04728, 1, 56, 1.27, 7.63, 1, 1, 56, 15.96, 9, 1, 2, 56, 29.65, 7.69, 0.42284, 57, -1.94, 7.85, 0.57716, 1, 57, 7.72, 12.9, 1, 1, 57, 8.87, 2.38, 1, 2, 56, 29.72, -11.75, 0.1186, 57, 8.42, -8.6, 0.8814, 2, 56, 23.36, -7.44, 0.63768, 57, 0.74, -8.31, 0.36232, 2, 55, 29.99, -10.42, 0.00336, 56, 11.53, -5.08, 0.99664, 2, 55, 18.51, -5.84, 0.91903, 56, -0.6, -7.44, 0.08097, 1, 55, 8.09, -2.78, 1, 1, 55, -0.31, -3.38, 1, 1, 55, -8.18, -3.13, 1, 1, 55, -9.64, -2.55, 1, 1, 55, -6.58, -1.02, 1, 1, 55, 1.53, 1.46, 1, 2, 55, 10.8, 2.26, 0.99712, 56, -11.47, -4.8, 0.00288, 1, 56, -0.08, 0.06, 1, 1, 56, 13.64, 1.42, 1, 2, 56, 27.17, -0.12, 0.45106, 57, 0.09, -0.09, 0.54894], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 55, "height": 64}}, "x4": {"x4": {"type": "mesh", "uvs": [0.95075, 0.0196, 1, 0.05732, 0.9524, 0.17329, 0.87568, 0.28926, 0.77915, 0.38732, 0.66118, 0.46086, 0.53908, 0.51272, 0.41945, 0.56834, 0.36913, 0.59851, 0.32788, 0.71543, 0.28936, 0.73588, 0.22065, 0.77375, 0.1665, 0.83842, 0.12204, 0.9151, 0.07516, 1, 0.04848, 0.9927, 0.02504, 0.9345, 0.01857, 0.83288, 0.02746, 0.73865, 0.05656, 0.64257, 0.0994, 0.55666, 0.1568, 0.47444, 0.22712, 0.40885, 0.30553, 0.37467, 0.36939, 0.3488, 0.45669, 0.33033, 0.53671, 0.32294, 0.61827, 0.30716, 0.70382, 0.25209, 0.7756, 0.18916, 0.82968, 0.11162, 0.87295, 0.01048, 0.88542, 0.13754, 0.82391, 0.24299, 0.7284, 0.32254, 0.63128, 0.38359, 0.53415, 0.42984, 0.40789, 0.45204, 0.3221, 0.48719, 0.22659, 0.57599, 0.15698, 0.66109, 0.10356, 0.74249, 0.07605, 0.83684, 0.06148, 0.93859], "triangles": [15, 43, 14, 14, 43, 13, 15, 16, 43, 13, 43, 42, 43, 16, 42, 16, 17, 42, 13, 42, 12, 42, 41, 12, 12, 41, 11, 17, 18, 42, 42, 18, 41, 41, 40, 11, 11, 40, 10, 18, 19, 41, 41, 19, 40, 9, 10, 39, 10, 40, 39, 9, 39, 8, 19, 20, 40, 40, 20, 39, 20, 21, 39, 39, 38, 8, 8, 38, 7, 39, 22, 38, 39, 21, 22, 38, 37, 7, 7, 37, 6, 22, 23, 38, 37, 23, 24, 37, 38, 23, 37, 25, 36, 37, 24, 25, 36, 25, 26, 37, 36, 6, 5, 36, 35, 5, 6, 36, 5, 34, 4, 5, 35, 34, 36, 26, 35, 26, 27, 35, 4, 34, 33, 35, 27, 34, 27, 28, 34, 33, 34, 28, 4, 33, 3, 33, 28, 29, 3, 32, 2, 3, 33, 32, 33, 30, 32, 33, 29, 30, 2, 32, 1, 30, 31, 32, 32, 0, 1, 32, 31, 0], "vertices": [1, 57, 1.99, 2.25, 1, 1, 57, 2.83, 9.32, 1, 1, 57, 16.28, 9.76, 1, 2, 57, 31.31, 7.09, 0.95433, 58, -6.12, 4.25, 0.04567, 1, 58, 8.64, 9, 1, 1, 58, 24.7, 10.36, 1, 2, 58, 40.31, 9.43, 0.70635, 59, 3.37, 9.86, 0.29365, 3, 58, 55.8, 8.99, 0.01065, 59, 18.86, 10.08, 0.95125, 60, -10.97, 17.48, 0.0381, 2, 59, 25.64, 10.83, 0.69923, 60, -4.78, 14.61, 0.30077, 2, 59, 34.72, 20.47, 0.06906, 60, 7.97, 18.16, 0.93094, 2, 59, 39.8, 20.79, 0.01903, 60, 12.49, 15.8, 0.98097, 1, 60, 20.66, 11.68, 1, 1, 60, 30, 10.66, 1, 1, 60, 39.64, 11.33, 1, 1, 60, 50.14, 12.3, 1, 1, 60, 51.48, 9.29, 1, 1, 60, 48.34, 3.34, 1, 1, 60, 40.35, -3.77, 1, 1, 60, 31.85, -8.95, 1, 2, 59, 62.26, 1.51, 0.0001, 60, 21.72, -12.32, 0.9999, 2, 59, 54.19, -5.03, 0.06159, 60, 11.44, -13.74, 0.93841, 2, 59, 44.64, -10.57, 0.51188, 60, 0.4, -13.53, 0.48812, 2, 59, 34.27, -13.91, 0.98254, 60, -10.2, -11.03, 0.01746, 1, 59, 24.2, -13.84, 1, 1, 59, 16.08, -13.58, 1, 1, 59, 5.61, -11.58, 1, 2, 58, 32.53, -8.92, 0.3184, 59, -3.61, -8.81, 0.6816, 2, 58, 22.91, -6.49, 0.96599, 59, -13.33, -6.79, 0.03401, 2, 57, 37.2, -13.06, 0.02883, 58, 11.18, -7.64, 0.97117, 2, 57, 27.4, -8.38, 0.69702, 58, 0.64, -10.21, 0.30298, 2, 57, 17.2, -6.3, 0.99939, 58, -8.58, -15.05, 0.00061, 1, 57, 5.38, -6.5, 1, 1, 57, 16.58, 0.89, 1, 2, 57, 29.8, -0.65, 0.96637, 58, -2.39, -2.7, 0.03363, 1, 58, 11.47, 0.32, 1, 1, 58, 24.72, 1.49, 1, 1, 58, 37.34, 1.23, 1, 1, 59, 15.72, -1.8, 1, 1, 59, 26.65, -2.11, 1, 2, 59, 40.72, 2.41, 0.00649, 60, 3.76, -0.39, 0.99351, 1, 60, 15.93, -1.58, 1, 1, 60, 26.61, -1.46, 1, 1, 60, 36.48, 1.95, 1, 1, 60, 46.02, 7.07, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 0, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 120, "height": 105}}, "x5": {"x5": {"type": "mesh", "uvs": [0.04399, 0.06573, 0.1543, 0, 0.21522, 0.0196, 0.22346, 0.13646, 0.2498, 0.2764, 0.31566, 0.41172, 0.40621, 0.54089, 0.5297, 0.65161, 0.67129, 0.71773, 0.80301, 0.7654, 0.91661, 0.84382, 1, 0.92071, 1, 0.95608, 0.95119, 1, 0.83265, 1, 0.6894, 0.97453, 0.53958, 0.9161, 0.40292, 0.82229, 0.29096, 0.72542, 0.17406, 0.59471, 0.09009, 0.464, 0.02094, 0.33176, 0, 0.19029, 0.01435, 0.11801, 0.08939, 0.17079, 0.12512, 0.31151, 0.19813, 0.44208, 0.28356, 0.57265, 0.39385, 0.69161, 0.51656, 0.77576, 0.66258, 0.84539, 0.79461, 0.89762, 0.91422, 0.93244], "triangles": [20, 21, 25, 25, 22, 24, 25, 21, 22, 25, 24, 4, 24, 3, 4, 22, 23, 24, 3, 1, 2, 1, 3, 0, 23, 0, 24, 3, 24, 0, 19, 26, 27, 19, 20, 26, 27, 26, 5, 20, 25, 26, 26, 4, 5, 26, 25, 4, 17, 29, 16, 18, 28, 17, 17, 28, 29, 29, 7, 8, 29, 28, 7, 19, 27, 18, 18, 27, 28, 28, 6, 7, 28, 27, 6, 27, 5, 6, 14, 32, 13, 13, 32, 12, 15, 31, 14, 14, 31, 32, 16, 30, 15, 15, 30, 31, 32, 11, 12, 32, 10, 11, 32, 31, 10, 16, 29, 30, 31, 9, 10, 31, 30, 9, 30, 8, 9, 30, 29, 8], "vertices": [1, 69, 25.98, 1.44, 1, 2, 68, 57.57, -16.53, 0.00162, 69, 29.69, -10.97, 0.99838, 2, 68, 52.9, -20.87, 0.01214, 69, 26.04, -16.2, 0.98786, 2, 68, 41.58, -15.76, 0.20614, 69, 13.9, -13.6, 0.79386, 2, 68, 27.27, -11.07, 0.97646, 69, -1.08, -12.04, 0.02354, 2, 67, 41.65, -13.2, 0.02528, 68, 11.55, -10.07, 0.97472, 2, 67, 25.72, -9.44, 0.89879, 68, -4.76, -11.54, 0.10121, 2, 66, 44.56, -12.21, 0.10972, 67, 8.77, -9.34, 0.89028, 2, 66, 29.06, -9.88, 0.97316, 67, -6.22, -13.91, 0.02684, 1, 66, 15.1, -9.1, 1, 1, 66, 1.83, -4.67, 1, 1, 66, -8.54, 0.53, 1, 1, 66, -9.69, 4.1, 1, 1, 66, -6.54, 10.02, 1, 1, 66, 4.62, 13.64, 1, 1, 66, 18.95, 15.46, 1, 2, 66, 34.97, 14.15, 0.89715, 67, -11.23, 10.32, 0.10285, 2, 66, 50.91, 8.88, 0.03564, 67, 5.42, 12.42, 0.96436, 1, 67, 20.53, 12.6, 1, 3, 67, 38.46, 10.52, 0.31452, 68, 1.03, 11.43, 0.68507, 69, -31.48, 4.41, 0.00041, 2, 68, 17.17, 12.24, 0.81273, 69, -15.87, 8.61, 0.18727, 2, 68, 32.76, 11.68, 0.01329, 69, -0.52, 11.35, 0.98671, 1, 69, 14.47, 9.24, 1, 1, 69, 21.45, 5.78, 1, 1, 69, 14.04, 0.16, 1, 1, 69, -1.28, 0.85, 1, 2, 68, 14.18, 1.71, 0.99129, 69, -16.57, -2.32, 0.00871, 2, 67, 32.21, 1.36, 0.97158, 68, -2.01, 0.76, 0.02842, 1, 67, 15.61, 2.99, 1, 2, 66, 41.73, 0.71, 0.14131, 67, 0.66, 1.1, 0.85869, 1, 66, 25.7, 3.26, 1, 1, 66, 11.56, 4.49, 1, 1, 66, -0.84, 4.34, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 0, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 99, "height": 106}}, "x6": {"x6": {"type": "mesh", "uvs": [0, 0.52938, 0.06299, 0.40558, 0.15284, 0.31608, 0.26637, 0.23105, 0.39313, 0.17288, 0.52129, 0.14752, 0.64526, 0.16393, 0.72327, 0.19525, 0.79362, 0.31757, 0.85491, 0.50552, 0.8984, 0.71523, 0.91098, 0.83138, 0.89683, 0.96268, 0.92355, 1, 0.96364, 1, 0.99036, 0.85831, 0.98329, 0.65968, 0.93692, 0.42401, 0.87718, 0.23548, 0.79387, 0.06547, 0.67597, 0, 0.56357, 0, 0.41738, 0, 0.29869, 0.0419, 0.18944, 0.10418, 0.09669, 0.17993, 0.02045, 0.28935, 0, 0.41391, 0.06523, 0.3097, 0.14825, 0.22173, 0.24963, 0.15435, 0.34926, 0.10756, 0.44888, 0.08136, 0.52579, 0.07387, 0.62455, 0.08136, 0.7233, 0.11692, 0.80195, 0.1974, 0.86837, 0.3696, 0.9166, 0.56063, 0.94463, 0.76473, 0.93762, 0.9103], "triangles": [11, 10, 39, 39, 16, 15, 13, 12, 40, 13, 40, 14, 12, 11, 40, 14, 40, 15, 40, 39, 15, 40, 11, 39, 30, 24, 23, 29, 25, 24, 29, 24, 30, 28, 26, 25, 28, 25, 29, 3, 2, 29, 3, 29, 30, 28, 29, 2, 28, 27, 26, 1, 28, 2, 1, 27, 28, 0, 27, 1, 33, 22, 21, 34, 21, 20, 33, 21, 34, 32, 22, 33, 31, 23, 22, 31, 22, 32, 5, 32, 33, 30, 23, 31, 5, 33, 34, 6, 5, 34, 4, 31, 32, 4, 32, 5, 3, 30, 31, 3, 31, 4, 35, 20, 19, 34, 20, 35, 6, 34, 35, 7, 6, 35, 18, 36, 19, 35, 19, 36, 7, 35, 36, 8, 7, 36, 37, 8, 36, 18, 37, 36, 37, 18, 17, 9, 8, 37, 38, 9, 37, 17, 38, 37, 38, 17, 16, 10, 9, 38, 39, 10, 38, 16, 39, 38], "vertices": [2, 49, -127.46, -50.13, 0.01809, 44, -12.54, -22.79, 0.98191, 2, 49, -110.75, -35.86, 0.08874, 44, 4.17, -8.52, 0.91126, 2, 49, -87.29, -25.96, 0.25607, 44, 27.63, 1.39, 0.74393, 3, 48, -131.04, -4.38, 0.00622, 49, -57.77, -16.8, 0.49571, 44, 57.15, 10.54, 0.49807, 3, 48, -98.23, 1.44, 0.07709, 49, -24.97, -10.99, 0.67124, 44, 89.96, 16.35, 0.25167, 3, 48, -65.21, 3.3, 0.25036, 49, 8.06, -9.12, 0.66368, 44, 122.98, 18.22, 0.08596, 3, 48, -33.44, 0.2, 0.5223, 49, 39.83, -12.23, 0.46263, 44, 154.76, 15.12, 0.01506, 2, 48, -13.54, -4.28, 0.77604, 49, 59.73, -16.7, 0.22396, 3, 48, 4.01, -19.59, 0.89166, 49, 77.27, -32.01, 0.0639, 45, -25.29, 87.97, 0.04444, 3, 48, 18.94, -42.69, 0.79128, 49, 92.21, -55.11, 0.00872, 45, -10.35, 64.87, 0.2, 2, 48, 29.21, -68.24, 0.53333, 45, -0.08, 39.32, 0.46667, 2, 48, 31.95, -82.28, 0.24444, 45, 2.65, 25.27, 0.75556, 2, 48, 27.75, -97.9, 0.06667, 45, -1.54, 9.66, 0.93333, 2, 48, 34.45, -102.62, 0.05333, 45, 5.16, 4.94, 0.94667, 2, 48, 44.75, -102.99, 0.07556, 45, 15.45, 4.57, 0.92444, 2, 48, 52.22, -86.24, 0.25156, 45, 22.92, 21.31, 0.74844, 2, 48, 51.25, -62.36, 0.53867, 45, 21.96, 45.2, 0.46133, 3, 48, 40.35, -33.67, 0.78526, 49, 113.62, -46.09, 0.00051, 45, 11.06, 73.89, 0.21422, 3, 48, 25.82, -10.51, 0.9063, 49, 99.09, -22.93, 0.03681, 45, -3.48, 97.05, 0.05689, 3, 48, 5.15, 10.64, 0.82634, 49, 78.42, -1.78, 0.16299, 45, -24.15, 118.2, 0.01067, 3, 48, -24.85, 19.57, 0.60075, 49, 48.42, 7.15, 0.3915, 44, 163.34, 34.49, 0.00775, 3, 48, -53.72, 20.6, 0.32494, 49, 19.55, 8.18, 0.62128, 44, 134.48, 35.52, 0.05377, 3, 48, -91.27, 21.95, 0.11779, 49, -18, 9.52, 0.70758, 44, 96.93, 36.86, 0.17464, 3, 48, -121.93, 18.01, 0.02123, 49, -48.66, 5.59, 0.5979, 44, 66.27, 32.93, 0.38087, 2, 49, -76.99, -0.88, 0.37832, 44, 37.94, 26.46, 0.62168, 2, 49, -101.13, -9.11, 0.17608, 44, 13.79, 18.23, 0.82392, 2, 49, -121.18, -21.54, 0.05795, 44, -6.26, 5.81, 0.94205, 2, 49, -126.97, -36.29, 0.02462, 44, -12.04, -8.95, 0.97538, 2, 49, -109.77, -24.39, 0.09643, 44, 5.16, 2.95, 0.90357, 2, 49, -88.07, -14.6, 0.25358, 44, 26.86, 12.74, 0.74642, 2, 49, -61.74, -7.45, 0.47903, 44, 53.18, 19.89, 0.52097, 3, 48, -109.22, 9.67, 0.00739, 49, -35.96, -2.75, 0.70211, 44, 78.97, 24.59, 0.2905, 3, 48, -83.52, 11.9, 0.06645, 49, -10.25, -0.52, 0.81455, 44, 104.67, 26.82, 0.11899, 3, 48, -63.74, 12.09, 0.22411, 49, 9.53, -0.33, 0.74369, 44, 124.46, 27.01, 0.03219, 3, 48, -38.41, 10.29, 0.4781, 49, 34.86, -2.13, 0.51843, 44, 149.79, 25.21, 0.00347, 2, 48, -13.2, 5.12, 0.73986, 49, 60.07, -7.31, 0.26014, 3, 48, 6.66, -5.26, 0.89864, 49, 79.93, -17.68, 0.08447, 45, -22.63, 102.3, 0.01689, 3, 48, 22.98, -26.52, 0.91371, 49, 96.25, -38.94, 0.01251, 45, -6.31, 81.04, 0.07378, 2, 48, 34.55, -49.87, 0.75822, 45, 5.26, 57.69, 0.24178, 2, 48, 40.88, -74.6, 0.51289, 45, 11.58, 32.96, 0.48711, 2, 48, 38.45, -91.99, 0.23644, 45, 9.16, 15.56, 0.76356], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 26], "width": 257, "height": 120}}, "x44": {"x44": {"type": "mesh", "uvs": [0.09967, 0.07709, 0.09711, 0.00941, 0.0629, 0.03302, 0.01842, 0.10227, 0, 0.22347, 0.0227, 0.33207, 0.07316, 0.40605, 0.14501, 0.49419, 0.2331, 0.56502, 0.33745, 0.60594, 0.44778, 0.60437, 0.54358, 0.60437, 0.63852, 0.62798, 0.68727, 0.63742, 0.68898, 0.71769, 0.7739, 1, 0.81635, 1, 0.92196, 0.96602, 1, 0.77168, 1, 0.66498, 0.90228, 0.62497, 0.88675, 0.49159, 0.78322, 0.44968, 0.71025, 0.53241, 0.64372, 0.50273, 0.55501, 0.478, 0.45824, 0.47553, 0.36886, 0.478, 0.26873, 0.45203, 0.18674, 0.39885, 0.1229, 0.3296, 0.07317, 0.23685, 0.06511, 0.16512, 0.10072, 0.11318, 0.0698, 0.09052, 0.03618, 0.15076, 0.03795, 0.24032, 0.07069, 0.32011, 0.11316, 0.39013, 0.17156, 0.44549, 0.23439, 0.50086, 0.30252, 0.53668, 0.38747, 0.54319, 0.47949, 0.53831, 0.57328, 0.53994, 0.64761, 0.55785, 0.70955, 0.57902, 0.79398, 0.60604, 0.81865, 0.74603, 0.85977, 0.8671], "triangles": [2, 1, 0, 34, 2, 0, 3, 2, 34, 34, 0, 33, 35, 3, 34, 32, 35, 34, 32, 34, 33, 4, 3, 35, 36, 35, 32, 36, 32, 31, 4, 35, 36, 37, 36, 31, 37, 31, 30, 5, 4, 36, 5, 36, 37, 38, 37, 30, 38, 30, 29, 6, 37, 38, 5, 37, 6, 39, 38, 29, 7, 38, 39, 6, 38, 7, 40, 29, 28, 39, 29, 40, 40, 7, 39, 8, 7, 40, 41, 28, 27, 40, 28, 41, 43, 26, 25, 44, 25, 24, 43, 25, 44, 42, 27, 26, 41, 27, 42, 42, 26, 43, 8, 40, 41, 10, 42, 43, 11, 43, 44, 10, 43, 11, 9, 41, 42, 8, 41, 9, 9, 42, 10, 11, 44, 12, 45, 24, 23, 44, 24, 45, 46, 45, 23, 47, 22, 21, 23, 22, 47, 46, 23, 47, 47, 21, 20, 12, 44, 45, 13, 45, 46, 12, 45, 13, 47, 14, 13, 48, 47, 20, 20, 19, 18, 49, 48, 20, 49, 20, 18, 17, 49, 18, 14, 47, 48, 46, 47, 13, 15, 48, 49, 15, 14, 48, 16, 15, 49, 16, 49, 17], "vertices": [1, 44, 12.39, 2.83, 1, 1, 44, 12, 14.29, 1, 1, 44, 1.23, 10.68, 1, 2, 46, -15.73, 46.58, 0.07768, 44, -13.01, -0.52, 0.92232, 2, 46, -22.18, 26.31, 0.4368, 44, -19.47, -20.78, 0.5632, 2, 46, -15.78, 7.72, 0.79012, 44, -13.07, -39.38, 0.20988, 2, 47, -114.4, 25.78, 0.00567, 46, -0.55, -5.33, 0.99433, 2, 47, -92.6, 10.1, 0.16677, 46, 21.25, -21.02, 0.83323, 2, 47, -65.65, -2.84, 0.45451, 46, 48.21, -33.96, 0.54549, 2, 47, -33.46, -10.91, 0.79186, 46, 80.39, -42.03, 0.20814, 3, 45, -100.09, 2.55, 0.00689, 47, 0.84, -11.87, 0.99002, 46, 114.69, -42.99, 0.00309, 2, 45, -70.31, 1.49, 0.21854, 47, 30.61, -12.93, 0.78146, 2, 45, -40.95, -3.55, 0.61551, 47, 59.98, -17.98, 0.38449, 2, 45, -25.85, -5.69, 0.86533, 47, 75.07, -20.11, 0.13467, 2, 45, -25.81, -19.26, 0.96677, 47, 75.12, -33.69, 0.03323, 1, 45, -1.12, -67.89, 1, 1, 45, 12.08, -68.36, 1, 1, 45, 45.11, -63.79, 1, 1, 45, 70.53, -31.83, 1, 1, 45, 71.18, -13.81, 1, 1, 45, 41.05, -5.97, 1, 1, 45, 37.03, 16.73, 1, 2, 45, 5.1, 24.96, 0.99931, 47, 106.02, 10.53, 0.00069, 2, 45, -18.08, 11.79, 0.88707, 47, 82.85, -2.63, 0.11293, 2, 45, -38.58, 17.54, 0.58903, 47, 62.35, 3.12, 0.41097, 2, 45, -66, 22.71, 0.23868, 47, 34.93, 8.28, 0.76132, 2, 45, -96.06, 24.2, 0.01285, 47, 4.87, 9.77, 0.98715, 2, 47, -22.93, 10.35, 0.86303, 46, 90.92, -20.77, 0.13697, 2, 47, -53.89, 15.85, 0.5165, 46, 59.96, -15.27, 0.4835, 2, 47, -79.05, 25.74, 0.2086, 46, 34.8, -5.38, 0.7914, 3, 47, -98.48, 38.14, 0.03081, 46, 15.37, 7.03, 0.95978, 44, 18.09, -40.07, 0.00941, 2, 46, 0.48, 23.24, 0.62262, 44, 3.19, -23.85, 0.37738, 2, 46, -1.6, 35.45, 0.18095, 44, 1.12, -11.65, 0.81905, 2, 46, 9.79, 43.82, 0.00211, 44, 12.5, -3.27, 0.99789, 1, 44, 3.03, 0.9, 1, 2, 46, -10.5, 38.19, 0.16816, 44, -7.79, -8.91, 0.83184, 2, 46, -10.49, 23.05, 0.5326, 44, -7.78, -24.05, 0.4674, 2, 46, -0.8, 9.21, 0.89875, 44, 1.92, -37.89, 0.10125, 2, 47, -101.87, 28.03, 0.04416, 46, 11.98, -3.09, 0.95584, 2, 47, -84.05, 18.03, 0.19896, 46, 29.8, -13.09, 0.80104, 2, 47, -64.86, 7.98, 0.42331, 46, 48.99, -23.14, 0.57669, 2, 47, -43.9, 1.17, 0.66861, 46, 69.95, -29.94, 0.33139, 2, 47, -17.54, -0.87, 0.91545, 46, 96.31, -31.99, 0.08455, 2, 45, -89.83, 13.36, 0.04165, 47, 11.09, -1.07, 0.95835, 2, 45, -60.69, 12.04, 0.31593, 47, 40.23, -2.38, 0.68407, 2, 45, -37.7, 8.19, 0.62949, 47, 63.23, -6.23, 0.37051, 2, 45, -18.58, 3.93, 0.88847, 47, 82.35, -10.5, 0.11153, 1, 45, 7.5, -1.57, 1, 1, 45, 14.32, -25.49, 1, 1, 45, 26.37, -46.39, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98], "width": 311, "height": 169}}, "yanj": {"yanj": {"x": 6.31, "y": -5.67, "rotation": -94.46, "width": 133, "height": 57}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"l1": {"attachment": [{"name": null}]}}, "bones": {"bone67": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -9.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.17, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 37.06, "y": -5.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 54.94, "y": 62.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -14.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -17.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 13.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -66.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone28": {"rotate": [{"angle": -5.27, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -17.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 13.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -23.47, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "angle": -5.27}]}, "bone29": {"rotate": [{"angle": -13.95, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 13.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -23.47, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "angle": -13.95}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -12.16, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 37.41, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone34": {"rotate": [{"angle": 8.39, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.16, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 37.41, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "angle": 8.39}]}, "bone35": {"rotate": [{"angle": 22.24, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 37.41, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "angle": 22.24}]}, "bone30": {"translate": [{"x": -3.35, "y": -3.63, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 25, "y": -7.35, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": -8.26, "y": -8.94, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7333, "x": -3.35, "y": -3.63}]}, "bone31": {"translate": [{"x": -7.63, "y": -8.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 25, "y": -7.35, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.7, "x": -8.26, "y": -8.94, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7333, "x": -7.63, "y": -8.25}]}, "bone32": {"translate": [{"x": 10.24, "y": -8.06, "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 0.0667, "x": -8.26, "y": -8.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 25, "y": -7.35, "curve": 0.34, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 0.7333, "x": 10.24, "y": -8.06}]}, "bone36": {"translate": [{"x": -3.82, "y": 12.29, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -15.25, "y": 8.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 4.86, "y": 15.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -9.43, "y": 30.31, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7333, "x": -3.82, "y": 12.29}]}, "bone37": {"translate": [{"x": -8.7, "y": 27.97, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -15.25, "y": 8.67, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 4.86, "y": 15.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -9.43, "y": 30.31, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7333, "x": -8.7, "y": 27.97}]}, "bone38": {"translate": [{"x": 0.81, "y": 19.91, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "x": -9.43, "y": 30.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -15.25, "y": 8.67, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 4.86, "y": 15.79, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7333, "x": 0.81, "y": 19.91}]}, "bone51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -1.23, "y": 5.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 13.81, "y": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -8.91, "y": 12.21, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 3.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone4": {"rotate": [{"angle": 0.45, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.99, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "angle": 0.45}]}, "bone12": {"rotate": [{"angle": 1.18, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 1.99, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "angle": 1.18}]}, "bone41": {"translate": [{"x": -1.31, "y": -0.8, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 15.45, "y": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -5.84, "y": -3.53, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "x": -3.47, "y": -2.1, "curve": 0.336, "c2": 0.34, "c3": 0.682, "c4": 0.71}, {"time": 0.7333, "x": -1.31, "y": -0.8}]}, "bone43": {"translate": [{"x": -3.47, "y": -2.1, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 15.45, "y": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -5.84, "y": -3.53, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "x": -3.47, "y": -2.1}]}, "bone42": {"translate": [{"x": -1.31, "y": -0.79, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 15.45, "y": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -5.84, "y": -3.53, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "x": -1.31, "y": -0.79}]}, "bone40": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 15.45, "y": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -5.84, "y": -3.53, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone45": {"translate": [{"x": -1.31, "y": -0.79, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 15.45, "y": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -5.84, "y": -3.53, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "x": -1.31, "y": -0.79}]}, "bone44": {"translate": [{"x": -3.47, "y": -2.1, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 15.45, "y": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -5.84, "y": -3.53, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "x": -3.47, "y": -2.1}]}, "bone14": {"translate": [{"x": 2.22, "y": 2.05, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3, "x": 12.02, "y": 11.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -12.23, "y": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7333, "x": 2.22, "y": 2.05}]}, "bone15": {"translate": [{"x": -0.28, "y": 1.03, "curve": 0.378, "c2": 0.6, "c3": 0.722}, {"time": 0.3333, "x": 8.77, "y": 9.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -16.28, "y": -9.75, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.6333, "x": -4.26, "y": -2.34, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.6667, "x": -3.26, "y": -1.73, "curve": 0.339, "c2": 0.35, "c3": 0.672, "c4": 0.69}, {"time": 0.7333, "x": -0.28, "y": 1.03}]}, "bone16": {"translate": [{"x": 0.35, "y": 0.32, "curve": 0.283, "c2": 0.14, "c3": 0.754}, {"time": 0.3667, "x": 12.02, "y": 11.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -12.23, "y": -7.3, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.6333, "x": -2.74, "y": -1.64, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.7, "curve": 0.309, "c3": 0.644, "c4": 0.35}, {"time": 0.7333, "x": 0.35, "y": 0.32}]}, "bone17": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 12.02, "y": 11.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -12.23, "y": -7.3, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.6333, "x": -4.96, "y": -2.96, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.7333}]}, "bone18": {"translate": [{"x": -0.91, "y": -0.54, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 12.02, "y": 11.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -12.23, "y": -7.3, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6333, "x": -7.27, "y": -4.34, "curve": 0.348, "c2": 0.38, "c3": 0.71, "c4": 0.81}, {"time": 0.7333, "x": -0.91, "y": -0.54}]}, "bone19": {"translate": [{"x": -5.01, "y": -2.99, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 12.02, "y": 11.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -12.23, "y": -7.3, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.6333, "x": -11.28, "y": -6.73, "curve": 0.29, "c2": 0.19, "c3": 0.652, "c4": 0.62}, {"time": 0.7333, "x": -5.01, "y": -2.99}]}, "bone23": {"translate": [{"x": 0.82, "y": 1.16, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.3333, "x": 8.47, "y": 11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -7.52, "y": -7.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.7333, "x": 0.82, "y": 1.16}]}, "bone24": {"translate": [{"x": -3.26, "y": -1.73, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4, "x": 5.22, "y": 10.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -11.33, "y": -10.38, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.6667, "x": -5.07, "y": -3.67, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.7333, "x": -3.26, "y": -1.73}]}, "bone25": {"translate": [{"x": -1.69, "y": -1.77, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 8.47, "y": 11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -7.52, "y": -7.87, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "x": -4.47, "y": -4.68, "curve": 0.336, "c2": 0.34, "c3": 0.682, "c4": 0.71}, {"time": 0.7333, "x": -1.69, "y": -1.77}]}, "bone26": {"translate": [{"x": -4.51, "y": -4.72, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 8.47, "y": 11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -7.52, "y": -7.87, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.6667, "x": -6.94, "y": -7.26, "curve": 0.298, "c2": 0.21, "c3": 0.644, "c4": 0.59}, {"time": 0.7333, "x": -4.51, "y": -4.72}]}, "bone22": {"translate": [{"x": 1.79, "y": 1.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3, "x": 9.68, "y": 9.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -3.69, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7333, "x": 1.79, "y": 1.69}]}, "bone39": {"translate": [{"x": -2.19, "y": 1.41, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 9.68, "y": 9.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -3.69, "y": 2.37, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "x": -2.19, "y": 1.41}]}, "bone21": {"translate": [{"x": -0.83, "y": 0.53, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 9.68, "y": 9.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -3.69, "y": 2.37, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "x": -0.83, "y": 0.53}]}, "bone20": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 9.68, "y": 9.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -3.69, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "translate": [{"x": 0.02, "y": 0.53}]}, "bone7": {"rotate": [{"angle": 0.76, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.39, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "angle": 0.76}]}, "bone8": {"rotate": [{"angle": 2.02, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.39, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "angle": 2.02}]}, "bone9": {"rotate": [{"angle": 3.13, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 3.39, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7333, "angle": 3.13}]}, "bone10": {"rotate": [{"angle": 0.76, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.39, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "angle": 0.76}]}, "bone11": {"rotate": [{"angle": 2.02, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.39, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "angle": 2.02}]}, "bone5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 12.12, "y": 10.65, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -9.53, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone53": {"rotate": [{"angle": 0.13, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 1.71, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.7333, "angle": 0.13}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone59": {"rotate": [{"angle": 0.13, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 1.71, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.7333, "angle": 0.13}]}, "bone60": {"rotate": [{"angle": 0.38, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.71, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "angle": 0.38}]}, "bone54": {"rotate": [{"angle": 0.38, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.71, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7333, "angle": 0.38}]}, "bone61": {"rotate": [{"angle": 0.69, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 1.71, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7333, "angle": 0.69}]}, "bone62": {"rotate": [{"angle": 1.02, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 1.71, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "angle": 1.02}]}, "bone56": {"rotate": [{"angle": 1.02, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 1.71, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "angle": 1.02}]}, "bone63": {"rotate": [{"angle": 1.33, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.71, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.7333, "angle": 1.33}]}, "bone55": {"rotate": [{"angle": 0.69, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 1.71, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7333, "angle": 0.69}]}, "bone57": {"rotate": [{"angle": 1.58, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 1.71, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7333, "angle": 1.58}]}, "bone64": {"rotate": [{"angle": 1.33, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.71, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.7333, "angle": 1.33}]}, "bone65": {"rotate": [{"angle": 1.58, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 1.71, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7333, "angle": 1.58}]}, "bone66": {"rotate": [{"angle": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 1.71}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 24.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -15.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone47": {"rotate": [{"angle": -13.91, "curve": 0.29, "c2": 0.19, "c3": 0.652, "c4": 0.62}, {"time": 0.1, "angle": -6.09, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 24.39, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -15.02, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7333, "angle": -13.91}]}, "bone49": {"rotate": [{"angle": -6.09, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 24.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -15.02, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7333, "angle": -6.09}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 24.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -15.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}}, "events": [{"time": 0.5, "name": "atk"}]}, "boss_idle": {"slots": {"biyan": {"attachment": [{"time": 1.4, "name": "biyan"}, {"time": 1.4667, "name": null}]}, "yanj": {"attachment": [{"time": 1.4, "name": null}, {"time": 1.4667, "name": "yanj"}]}}, "bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.25, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": -0.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.25, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.92}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.32, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.09, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone8": {"rotate": [{"angle": -2.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.3}]}, "bone9": {"rotate": [{"angle": -5.79, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5.79}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.09, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone11": {"rotate": [{"angle": -2.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.3}]}, "bone12": {"rotate": [{"angle": -2.33, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.33}]}, "bone51": {"translate": [{"x": -1.06, "y": 4.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -2.12, "y": 8.84, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -1.06, "y": 4.42}]}, "bone13": {"scale": [{"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 0.345, "curve": "stepped"}, {"time": 1.4, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 1.4667, "x": 0.345, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333}]}, "bone14": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 24.58, "y": 14.39, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone15": {"translate": [{"x": 5.95, "y": 3.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 24.58, "y": 14.39, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "x": 5.95, "y": 3.48}]}, "bone16": {"translate": [{"x": 12.29, "y": 7.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 24.58, "y": 14.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 12.29, "y": 7.19}]}, "bone17": {"translate": [{"x": 23.53, "y": 13.77, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": 24.58, "y": 14.39, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "x": 23.53, "y": 13.77}]}, "bone18": {"translate": [{"x": 21.38, "y": 12.52, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "x": 24.58, "y": 14.39, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "x": 21.38, "y": 12.52}]}, "bone19": {"translate": [{"x": 12.29, "y": 7.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 24.58, "y": 14.39, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 12.29, "y": 7.19}]}, "bone20": {"translate": [{"x": 2.32, "y": 5.18, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.3333, "x": 1.61, "y": 4.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.3333, "x": 6.38, "y": 10.37, "curve": 0.358, "c2": 0.47, "c3": 0.695, "c4": 0.82}, {"time": 2, "x": 2.32, "y": 5.18}]}, "bone21": {"translate": [{"x": 3.12, "y": 16.51, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "x": 2.94, "y": 7.53, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.8, "x": 3.15, "y": 17.85, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "x": 3.12, "y": 16.51}]}, "bone22": {"translate": [{"x": 2.58, "y": 1.43, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "x": 6.26, "y": 3.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "x": 2.58, "y": 1.43}]}, "bone23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -8.03, "y": 11, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone24": {"translate": [{"x": -1.63, "y": 2.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -8.03, "y": 11, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "x": -1.63, "y": 2.23}]}, "bone25": {"translate": [{"x": -4.37, "y": 5.99, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": -8.03, "y": 11, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": -4.37, "y": 5.99}]}, "bone26": {"translate": [{"x": -6.99, "y": 9.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": -8.03, "y": 11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "x": -6.99, "y": 9.57}]}, "bone27": {"rotate": [{"angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -16.61}]}, "bone28": {"rotate": [{"angle": -11.45, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "angle": -16.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3667, "angle": -0.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "angle": -11.45}]}, "bone29": {"rotate": [{"angle": -3.97, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": -16.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.7333, "angle": -0.76, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": -3.97}]}, "bone30": {"translate": [{"x": 4.83, "y": -1.47, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 10.59, "y": -3.22, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": 4.83, "y": -1.47}]}, "bone31": {"translate": [{"x": 10.47, "y": -3.18, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 10.59, "y": -3.22, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "x": 10.47, "y": -3.18}]}, "bone32": {"translate": [{"x": 5.29, "y": -1.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 10.59, "y": -3.22, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 5.29, "y": -1.61}]}, "bone33": {"rotate": [{"angle": 19.34, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 19.34}]}, "bone34": {"rotate": [{"angle": 12.82, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "angle": 19.34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3667, "angle": -0.69, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "angle": 12.82}]}, "bone35": {"rotate": [{"angle": 3.37, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": 19.34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.7333, "angle": -0.69, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 3.37}]}, "bone36": {"translate": [{"x": 7.65, "y": 4.2, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 16.78, "y": 9.22, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": 7.65, "y": 4.2}]}, "bone37": {"translate": [{"x": 16.59, "y": 9.11, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 16.78, "y": 9.22, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "x": 16.59, "y": 9.11}]}, "bone38": {"translate": [{"x": 9.13, "y": 5.02, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "x": 16.78, "y": 9.22, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": 9.13, "y": 5.02}]}, "bone39": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 10.82, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone40": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.77, "y": -14.44, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone41": {"rotate": [{"angle": 5.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 15.38, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2, "angle": 5.63}], "translate": [{"x": 4.77, "y": -14.44, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 26.41, "y": -4.93, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.77, "y": -14.44}]}, "bone42": {"translate": [{"x": 1.35, "y": -4.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.77, "y": -14.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.35, "y": -4.1}]}, "bone43": {"translate": [{"x": 3.42, "y": -10.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.77, "y": -14.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 3.42, "y": -10.34}]}, "bone44": {"translate": [{"x": 3.42, "y": -10.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.77, "y": -14.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 3.42, "y": -10.34}]}, "bone45": {"translate": [{"x": 1.35, "y": -4.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.77, "y": -14.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.35, "y": -4.1}]}, "bone46": {"rotate": [{"angle": 11.58, "curve": 0.338, "c2": 0.35, "c3": 0.685, "c4": 0.72}, {"time": 0.3, "angle": 4.01, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 19.8, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 11.58}]}, "bone47": {"rotate": [{"angle": 18.5, "curve": 0.295, "c2": 0.2, "c3": 0.643, "c4": 0.58}, {"time": 0.3, "angle": 11.65, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 19.8, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 18.5}]}, "bone48": {"rotate": [{"angle": 4.01, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 19.8, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 4.01}]}, "bone49": {"rotate": [{"angle": 11.65, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 19.8, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 11.65}]}, "bone52": {"rotate": [{"angle": -4.12, "curve": 0.32, "c2": 0.29, "c3": 0.674, "c4": 0.69}, {"time": 0.3667, "angle": -1.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -5.45, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -4.12}]}, "bone53": {"rotate": [{"angle": -5.33, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": -5.45, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3667, "angle": -4.13, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.0667, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "angle": -5.33}]}, "bone54": {"rotate": [{"angle": -3.68, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "angle": -5.45, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "angle": -3.68}]}, "bone55": {"rotate": [{"angle": -1.33, "curve": 0.32, "c2": 0.29, "c3": 0.674, "c4": 0.69}, {"time": 0.3667, "angle": -3.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7, "angle": -5.45, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -1.33}]}, "bone56": {"rotate": [{"angle": -0.06, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -1.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.0333, "angle": -5.45, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 2, "angle": -0.06}]}, "bone57": {"rotate": [{"angle": -4.12, "curve": 0.32, "c2": 0.29, "c3": 0.674, "c4": 0.69}, {"time": 0.3667, "angle": -1.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -5.45, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -4.12}]}, "bone58": {"rotate": [{"angle": -1.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.55}]}, "bone59": {"rotate": [{"angle": -4.13, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -5.45, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -4.13}]}, "bone60": {"rotate": [{"angle": -5.45, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.45}]}, "bone61": {"rotate": [{"angle": -3.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -5.45, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.9}]}, "bone62": {"rotate": [{"angle": -1.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -5.45, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.55}]}, "bone63": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.45, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone64": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.45, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone65": {"rotate": [{"angle": -1.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.55}]}, "bone66": {"rotate": [{"angle": -3.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.45, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -3.9}]}, "bone67": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 18.94, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone68": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 48.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2}], "scale": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "x": 0.513, "y": 0.513, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2}]}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.1, "name": "biyan"}]}, "w1": {"attachment": [{"time": 0.1333, "name": null}]}, "w2": {"attachment": [{"time": 0.1333, "name": null}]}, "w3": {"attachment": [{"time": 0.1333, "name": null}]}, "w4": {"attachment": [{"time": 0.1333, "name": null}]}, "w5": {"attachment": [{"time": 0.1333, "name": null}]}, "w6": {"attachment": [{"time": 0.1333, "name": null}]}, "yanj": {"attachment": [{"time": 0.1, "name": null}]}}, "bones": {"bone67": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -83.01}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -128.42, "y": 189.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -128.42, "y": -25.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4667, "x": -128.42, "y": 23.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": -128.42, "y": -25.39}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 46.59}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -60.53}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -21.72}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -21.72}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -21.72}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -21.72}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -21.72}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -21.72}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 99.69}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -37.76, "y": 88.82}]}, "bone53": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 23.33}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 45.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 23.33}]}, "bone55": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 23.33}]}, "bone56": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 23.33}]}, "bone57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 23.33}]}, "bone59": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 23.33}]}, "bone60": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 23.33}]}, "bone61": {"rotate": [{"curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 31.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5333, "angle": 23.33}]}, "bone62": {"rotate": [{"curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": 59.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": 23.33}]}, "bone63": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 55.63}]}, "bone64": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.97}]}, "bone65": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.82}]}, "bone66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 0.33}]}, "bone42": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 18.41, "y": -1.31}]}, "bone5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -25.78, "y": 4.1}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.0333, "name": "biyan"}]}, "yanj": {"attachment": [{"time": 0.0333, "name": null}]}}, "bones": {"bone67": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 24.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -4.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone4": {"rotate": [{"angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": -0.87}]}, "bone12": {"rotate": [{"angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -2.35}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone28": {"rotate": [{"angle": -3.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -6.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -3.28}]}, "bone29": {"rotate": [{"angle": -6.55, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -6.55}]}, "bone34": {"rotate": [{"angle": -3.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -6.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -3.28}]}, "bone35": {"rotate": [{"angle": -6.55, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -6.55}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -9.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone10": {"rotate": [{"angle": -2.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -9.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2, "angle": -2.58}]}, "bone11": {"rotate": [{"angle": -6.52, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "angle": -6.52}]}, "bone7": {"rotate": [{"angle": -2.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -9.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2, "angle": -2.58}]}, "bone8": {"rotate": [{"angle": -6.52, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "angle": -6.52}]}, "bone9": {"rotate": [{"angle": -9.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -9.1}]}, "bone14": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 12.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone15": {"translate": [{"x": 8.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 12.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "x": 8.82}]}, "bone16": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 12.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone17": {"translate": [{"x": 8.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 12.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "x": 8.82}]}, "bone18": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 12.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone19": {"translate": [{"x": 8.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 12.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "x": 8.82}]}, "bone23": {"translate": [{"x": 8.79, "y": 1.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 12.27, "y": 2.38, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "x": 8.79, "y": 1.7}]}, "bone24": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 12.27, "y": 2.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone25": {"translate": [{"x": 8.79, "y": 1.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 12.27, "y": 2.38, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "x": 8.79, "y": 1.7}]}, "bone26": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 12.27, "y": 2.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone22": {"translate": [{"x": 3.48, "y": 0.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 12.27, "y": 2.38, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2, "x": 3.48, "y": 0.67}]}, "bone21": {"translate": [{"x": 12.27, "y": 2.38, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 12.27, "y": 2.38}]}, "bone20": {"translate": [{"x": 3.48, "y": 0.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 12.27, "y": 2.38, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2, "x": 3.48, "y": 0.67}]}, "bone39": {"translate": [{"x": 12.27, "y": 2.38, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 12.27, "y": 2.38}]}, "bone41": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 20.41, "y": 10.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone59": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone53": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone60": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone55": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone56": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone63": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone64": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone65": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "bone66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}}}}}