import { _decorator, Label, math, Node, Sprite } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { FriendModule } from "../../../../friend/FriendModule";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import ResMgr from "../../../../../lib/common/ResMgr";
import { FriendLabelMessage } from "../../../../../game/net/protocol/Friend";
import TipMgr from "../../../../../lib/tips/TipMgr";
import { CityModule } from "../../../../city/CityModule";
import { CityMessage } from "../../../../../game/net/protocol/City";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HdFriendMubiaoAudioName } from "../../../../friend/FriendConfig";
import MsgMgr from "../../../../../lib/event/MsgMgr";
import MsgEnum from "../../../../../game/event/MsgEnum";
import GuideMgr from "../../../../../ext_guide/GuideMgr";
import { ShopRouteName } from "../../../../player/PlayerConstant";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
import { CardAdapter } from "../../adapter/CardViewHolder";
import { FoucsLinearLayoutManager } from "db://assets/platform/src/core/ui/adapter_view/layout_manager/FoucsLinearLayoutManager";
import { FriendGoalModule } from "../../FriendGoalModule";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Fri Nov 01 2024 17:37:57 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_hd_friendmubiao/UIHdFriendMubiao.ts
 *
 */
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("UIHdFriendGoal")
@routeConfig({
  bundle: BundleEnum.BUNDLE_HD_FRIENDMUBIAO,
  url: "prefab/ui/UIHdFriendGoal",
  nextHop: [],
  exit: "",
})
export class UIHdFriendGoal extends BaseCtrl {
  private _index: number = 0;
  private _friendLabel: FriendLabelMessage;
  public playShowAni: boolean = true;

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected start(): void {
    super.start();
    log.log("onEvtShow");
    this.refreshUI();
    log.log("onRefreshUI FINISH");
    FriendGoalModule.api.getLabel((data: FriendLabelMessage) => {
      this._friendLabel = data;
      this.refreshUI();
    });
    MsgMgr.on(MsgEnum.ON_FRIEND_UPDATE, this.refreshUI, this);
    let targetFriend = FriendModule.config.obtainFriendTarget();
    let adapter = new CardAdapter(this.getNode("viewholder_card"));
    this.getNode("list_card").getComponent(AdapterView).setAdapter(adapter);
    this.getNode("list_card").getComponent(FoucsLinearLayoutManager).onFoucsScrollComplete = (position: number) => {
      this._index = position;
      this.refreshMain();
    };
    this.getNode("list_card").getComponent(FoucsLinearLayoutManager).onFoucsChange = (position: number) => {
      //
      this.getNode("node_indicators").children.forEach((child: Node) => {
        child.getChildByName("bg_select").active = false;
      });
      if (this.getNode("node_indicators").children[position]) {
        this.getNode("node_indicators").children[position].getChildByName("bg_select").active = true;
      }
    };
    adapter.setDatas(targetFriend);
  }

  protected onDestroy(): void {
    super.onDestroy();
    MsgMgr.off(MsgEnum.ON_FRIEND_UPDATE, this.refreshUI, this);
  }

  private refreshMain() {
    this.getNode("bg_yilingqu").active = false;
    this.getNode("btn_obtain").active = false;
    this.getNode("btn_go").active = false;

    let targetFriend = FriendModule.config.obtainFriendTarget();
    let friend = targetFriend[this._index];
    this.getNode("node_friend_img").destroyAllChildren();
    ResMgr.setNodePrefab(
      BundleEnum.BUNDLE_COMMON_FRIEND,
      `prefab/friend_${friend.id}`,
      this.getNode("node_friend_img"),
      (child: Node) => {
        this.getNode("node_friend_img").addChild(child);
      }
    );

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_UI,
      `atlas_imgs/pingzhi_${friend.color}`,
      this.getNode("bg_pinzhi").getComponent(Sprite)
    );
    this.getNode("friend_name").getComponent(Label).string = `${friend.name}`;
    this.getNode("lbl_tiaojian").getComponent(Label).string = FriendModule.config.getFriendTiaojianString(
      friend.unlock,
      this._friendLabel?.labelMessageMap[friend.id]?.count ?? 0,
      friend.unlockNum
    );
    this.getNode("bg_tiaojian_weidacheng").active = false;
    this.getNode("bg_tiaojian_yidacheng").active = false;
    this.getNode("lbl_tiaojian").getComponent(Label).color = math.color("fff99d");
    if (FriendModule.data.getFriendMessage(friend.id)) {
      this.getNode("bg_yilingqu").active = true;
      this.getNode("bg_tiaojian_yidacheng").active = true;
    } else if (
      this._friendLabel &&
      this._friendLabel.labelMessageMap[friend.id] &&
      this._friendLabel.labelMessageMap[friend.id].count >= friend.unlockNum
    ) {
      this.getNode("btn_obtain").active = true;
      this.getNode("bg_tiaojian_yidacheng").active = true;
    } else {
      this.getNode("btn_go").active = true;
      this.getNode("bg_tiaojian_weidacheng").active = true;
      this.getNode("lbl_tiaojian").getComponent(Label).color = math.color("ffffff");
    }
  }

  private refreshUI() {
    // this.refreshMain();
    this.getNode("list_card").getComponent(FoucsLinearLayoutManager).foucsTo(this._index);
  }
  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_btn_go() {
    AudioMgr.instance.playEffect(HdFriendMubiaoAudioName.Effect.点击前往按钮);
    // TipMgr.showTip("前往领取");
    let targetFriend = FriendModule.config.obtainFriendTarget();
    let friend = targetFriend[this._index];
    switch (friend.unlock) {
      case 1: //游历累计偶遇次数
        GuideMgr.startGuide({ stepId: 21 });
        break;
      case 2: //VIP等级
        GuideMgr.startGuide({ stepId: 61 });
        break;
      case 3: //主角等级
        GuideMgr.startGuide({ stepId: 25 });
        break;
      case 4: {
        //建筑总等级
        let cityList = CityModule.data.cityMessageList;

        // 获取最低升级花费的建筑
        let levelUpCostMin = -1;
        let cityId = -1;
        for (let idx in cityList) {
          let cityMsg: CityMessage = cityList[idx];
          if (cityMsg && cityMsg.level > 0) {
            let cfgBuildLv = CityModule.data.getConfigBuildLv(cityMsg.cityId, cityMsg.level);
            if (levelUpCostMin < 0 || levelUpCostMin > cfgBuildLv.cost) {
              levelUpCostMin = cfgBuildLv.cost;
              cityId = cityMsg.cityId;
            }
          }
        }

        if (cityId == -1) {
          cityId = 101;
        }
        GuideMgr.startGuide({ stepId: 23, args: { buildId: cityId } });
        break;
      }
      case 5: //繁荣度
        TipMgr.showTip(`繁荣度达到${friend.unlockNum}解锁`);
        break;
      case 6: //累计徒弟结伴数量
        GuideMgr.startGuide({ stepId: 33 });
        break;
      case 7: //累计获得灵兽数量
        GuideMgr.startGuide({ stepId: 38 });
        break;
      case 8: //首充次数
        UIMgr.instance.showDialog(ShopRouteName.UIShopCenter, { tabIdx: 6 });
        break;
      case 9: //累计徒弟成年数
        GuideMgr.startGuide({ stepId: 33 });
        break;
      case 10: //演武场累计胜利
        GuideMgr.startGuide({ stepId: 34 });
        break;
      case 11: //幸运商店
        GuideMgr.startGuide({ stepId: 40 });
        break;
      case 12: //七日登录
        GuideMgr.startGuide({ stepId: 41 });
        break;
      case 13: //福地采集次数47
        GuideMgr.startGuide({ stepId: 42 });
        break;
      case 14: //活动
        GuideMgr.startGuide({ stepId: 43 });
        break;
    }
  }
  private on_click_btn_obtain() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let targetFriend = FriendModule.config.obtainFriendTarget();
    let friend = targetFriend[this._index];
    // 获得仙友后 通过监听MsgEnum.ON_FRIEND_UPDATE 刷新UI
    FriendModule.api.unLockLabelFriend(friend.id);
  }
  private on_click_btn_right() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._index++;
    let targetFriend = FriendModule.config.obtainFriendTarget();
    this._index >= targetFriend.length && (this._index = 0);
    this.refreshUI();
  }
  private on_click_btn_left() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._index--;
    let targetFriend = FriendModule.config.obtainFriendTarget();
    this._index < 0 && (this._index = targetFriend.length - 1);
    this.refreshUI();
  }
  private on_click_btn_test() {
    let friends = FriendModule.config.obtainFriendTarget();
    for (let i = 0; i < friends.length; i++) {
      let friend = friends[i];
      if (FriendModule.data.getFriendMessage(friend.id)) {
        FriendModule.api.testDelFriend(friend.id, () => {
          this.refreshUI();
        });
      }
    }
    FriendGoalModule.api.getLabel((data: FriendLabelMessage) => {
      this._friendLabel = data;
      this.refreshUI();
    });
  }
}
