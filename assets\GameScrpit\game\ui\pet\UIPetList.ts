import { _decorator, EventTouch, isValid, Label, Node, NodeEventType, tween, v3 } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { PetModule } from "../../../module/pet/PetModule";
import { PetSort } from "../../../module/pet/PetConstant";
import { PetMessage } from "../../net/protocol/Pet";
import { UIPetCard } from "./UIPetCard";
import { instantiate } from "cc";
import MsgEnum from "../../../game/event/MsgEnum"; // 新增：导入事件枚举
import MsgMgr from "../../../lib/event/MsgMgr"; // 新增：导入事件管理器
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Fri May 09 2025 11:14:02 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/pet/UIPetList.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIPetList")
export class UIPetList extends UINode {
  // 新增属性（参考UIHeroMainList）
  protected _openAct: boolean = true;
  private _indexOwned: number = 0;
  private _indexUnowned: number = 0;
  private currentOwnedPets: number[] = []; // 已拥有宠物ID列表
  private currentUnownedPets: number[] = []; // 未拥有宠物ID列表
  private petNodeMap: Map<number, Node> = new Map(); // 宠物ID到节点的映射

  // 新增节点引用（需要在编辑器中绑定）

  private petViewHolder: Node = null; // 宠物卡牌预制体

  private nodeDivider: Node = null; // 分割线节点

  private petListContent: Node = null; // 宠物列表容器

  private _sortType: PetSort = PetSort.QUALITY; // 当前排序类型

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PET}?prefab/ui/UIPetList`;
  }

  public tick(dt: number): void {
    // 加载已拥有的宠物卡牌（分帧）
    if (this.currentOwnedPets && this._indexOwned < this.currentOwnedPets.length) {
      const petId = this.currentOwnedPets[this._indexOwned];
      const petMessage = PetModule.data.getPet(petId);
      this.addPetCard(petId, petMessage, true, this._indexOwned);
      this._indexOwned++;
    } // 先加载已拥有的再加载未拥有的
    // 加载未拥有的宠物卡牌（分帧）
    else if (this.currentUnownedPets && this._indexUnowned < this.currentUnownedPets.length) {
      const petId = this.currentUnownedPets[this._indexUnowned];
      const petMessage = PetModule.data.getPet(petId); // 未拥有可能只有配置信息，需根据实际协议调整
      this.addPetCard(petId, petMessage, false, this._indexUnowned);
      this._indexUnowned++;
    }
  }

  // 新增初始化数据方法（参考UIHeroMainList的onLoad）
  protected onEvtShow(): void {
    super.onEvtShow();
    this.getNode("btn_blank").on(NodeEventType.ACTIVE_CHANGED, this.onSortShowChanged, this);
    this.petListContent = this.getNode("pet_list_content");
    this.nodeDivider = this.getNode("node_divider");
    this.petViewHolder = this.getNode("UIPetCard");
    // 初始化数据从PetData获取已拥有和未拥有的宠物ID列表（默认按品质排序）
    this.refreshList(PetSort.QUALITY); // 默认按品质排序

    this.getNode("lbl_pet_num").getComponent(Label).string = `${this.currentOwnedPets.length}`;
    this.getNode("lbl_pet_total_num").getComponent(Label).string = `/${
      this.currentUnownedPets.length + this.currentOwnedPets.length
    }`;
    // 新增：监听宠物数据更新事件
    MsgMgr.on(MsgEnum.ON_PET_UPDATE, this.onPetUpdate, this);
  }
  /**
   * 刷新宠物列表（公共方法）
   * @param sort 排序类型
   */
  private refreshList(sort: PetSort) {
    this._sortType = sort;
    this.currentOwnedPets = PetModule.data.getPetIds(true, sort);
    this.currentUnownedPets = PetModule.data.getPetIds(false, sort);
    this.petListContent.removeAllChildren();
    this.petListContent.addChild(this.nodeDivider);
    this._indexOwned = 0;
    this._indexUnowned = 0;
    this.petNodeMap.clear();
  }
  // 新增：宠物数据更新时的回调
  private onPetUpdate() {
    // 重新加载数据并刷新列表
    if (isValid(this.node)) {
      this.refreshList(this._sortType);
    }
  }

  protected onEvtHide(): void {
    super.onEvtHide();
    // 新增：移除事件监听防止内存泄漏
    MsgMgr.off(MsgEnum.ON_PET_UPDATE, this.onPetUpdate, this);
  }

  // 新增添加卡牌方法
  private addPetCard(petId: number, petMessage: PetMessage, isOwned: boolean, position: number) {
    // 优先从map中获取已存在的节点，不存在则实例化新节点
    let card = this.petNodeMap.get(petId);
    if (!card) {
      card = instantiate(this.petViewHolder);
      this.petNodeMap.set(petId, card); // 存储新节点到map中
    }

    // 无论是否新节点，都更新卡牌信息（确保排序/数据变化时刷新）
    card.getComponent(UIPetCard).initInfo(petId, position);
    card.name += "_" + petId;
    card.active = true;

    // 已拥有的宠物插入到分割线前，未拥有的添加到分割线后
    if (isOwned) {
      const dividerIndex = this.nodeDivider.getSiblingIndex();
      this.petListContent.insertChild(card, dividerIndex);
    } else {
      this.petListContent.addChild(card);
    }
    tween(card)
      .set({ scale: v3(0.1, 0.1, 1) })
      .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
      .start();
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }

  private onSortShowChanged() {
    let scaleY = this.getNode("btn_blank").active ? -1 : 1;
    this.getNode("bg_icon_select").setScale(1, scaleY);
  }

  private on_click_btn_blank(e: EventTouch) {
    e.target.active = false;
  }

  private on_click_btn_sort(e: EventTouch) {
    AudioMgr.instance.playEffect(524);
    this.getNode("btn_blank").active = !this.getNode("btn_blank").active;
  }

  private on_click_lbl_sort1(e: EventTouch) {
    AudioMgr.instance.playEffect(524);
    this.getNode("btn_blank").active = false;
    this.getNode("lbl_sort").getComponent(Label).string = e.target.getChildByName("label").getComponent(Label).string;
    // 排序变化后需要重新加载数据（类似UIHeroMainList的refreshUIHeroListInfo）
    this.refreshList(PetSort.QUALITY);
  }
  private on_click_lbl_sort2(e: EventTouch) {
    AudioMgr.instance.playEffect(524);
    this.getNode("btn_blank").active = false;
    this.getNode("lbl_sort").getComponent(Label).string = e.target.getChildByName("label").getComponent(Label).string;
    this.refreshList(PetSort.LEVEL);
  }
}
