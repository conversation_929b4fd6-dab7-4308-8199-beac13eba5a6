import MsgEnum from "../../game/event/MsgEnum";
import { <PERSON>piHandler } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { RedeemBuyMessage, RedeemLimitUpdateMessage } from "../../game/net/protocol/Activity";
import { CommLongListMessage } from "../../game/net/protocol/Comm";
import MsgMgr from "../../lib/event/MsgMgr";
import { DayActivityModule } from "./DayActivityModule";

export class DayActivitySubscriber {
  private redeemBuyCallback(rs: RedeemBuyMessage) {
    let list = [10701];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }

    DayActivityModule.data.dayMessage.redeemMap = rs.redeemMap;
    DayActivityModule.data.dayMessage.chosenMap = rs.chosenMap;

    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList });
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_DAILY_GIFT_BUT_UP);
  }

  private upActivityDb(rs: CommLongListMessage) {
    for (let i = 0; i < rs.longList.length; i++) {
      if (rs.longList.includes(10701)) {
        DayActivityModule.data.upDayVO();
        return;
      }
    }
  }

  private RedeemLimitUpdate(rs: RedeemLimitUpdateMessage) {
    let list = [10701];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }

    /** achieveId*/
    DayActivityModule.data.limitMap = rs.limitMap;
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(RedeemBuyMessage, ActivityCmd.RedeemBuyMessage, this.redeemBuyCallback);
    ApiHandler.instance.subscribe(CommLongListMessage, ActivityCmd.ActivityUp, this.upActivityDb);
    ApiHandler.instance.subscribe(RedeemLimitUpdateMessage, ActivityCmd.RedeemLimitUpdate, this.RedeemLimitUpdate);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ActivityCmd.RedeemBuyMessage, this.redeemBuyCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.ActivityUp, this.upActivityDb);
    ApiHandler.instance.unSubscribe(ActivityCmd.RedeemLimitUpdate, this.RedeemLimitUpdate);
  }
}
