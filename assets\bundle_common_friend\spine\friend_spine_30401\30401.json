{"skeleton": {"hash": "Sf1+o+I11iAVu7U/IybsoMyWmQY", "spine": "3.8.75", "x": -320.62, "y": -524.4, "width": 624.02, "height": 1179.13, "images": "./images/", "audio": "D:/仙友spine/李清照"}, "bones": [{"name": "root", "scaleX": 0.82, "scaleY": 0.82}, {"name": "bone", "parent": "root", "length": 324.64, "rotation": -89.19, "x": 495.89, "y": 31.12}, {"name": "st", "parent": "bone", "length": 119.67, "rotation": -29.5, "x": 243.84, "y": -465.17}, {"name": "bone2", "parent": "bone", "length": 87.36, "rotation": 177.02, "x": 238.61, "y": -444.1}, {"name": "bone3", "parent": "bone2", "x": 202.33, "y": 29.8}, {"name": "bone4", "parent": "bone2", "length": 87.49, "rotation": 30.03, "x": 328.07, "y": -77.11}, {"name": "st2", "parent": "st", "length": 88.9, "rotation": -29, "x": 132.71, "y": -4.87}, {"name": "st3", "parent": "st2", "length": 67.85, "rotation": 41.62, "x": 98.05, "y": 2.06}, {"name": "st4", "parent": "st3", "length": 100.55, "rotation": 16.07, "x": 85.75, "y": 1.71}, {"name": "st5", "parent": "st4", "length": 58.01, "rotation": 49.64, "x": 117.13, "y": 1.1}, {"name": "t1", "parent": "bone4", "length": 62.4, "rotation": -57.6, "x": 90.47, "y": 28.85}, {"name": "t2", "parent": "t1", "x": 6.27, "y": 72.96}, {"name": "t4", "parent": "t1", "length": 41.21, "rotation": 169.82, "x": 104.5, "y": 182.58}, {"name": "t3", "parent": "t1", "length": 137.69, "rotation": -66.98, "x": 121.94, "y": 165.06}, {"name": "t7", "parent": "t1", "length": 18.35, "rotation": -140.13, "x": -27.85, "y": 64.49}, {"name": "t8", "parent": "t7", "length": 16.02, "rotation": 39.77, "x": 24.81, "y": -0.5}, {"name": "t6", "parent": "t1", "length": 14.97, "rotation": -140.6, "x": 81.46, "y": -9.82}, {"name": "t9", "parent": "t6", "length": 21.72, "rotation": -5.52, "x": 20.64, "y": -2.24}, {"name": "t10", "parent": "t9", "length": 11.69, "rotation": 26.56, "x": 25.17, "y": -0.25}, {"name": "t11", "parent": "t3", "length": 42.85, "rotation": 20.17, "x": 52.54, "y": 78.74}, {"name": "t12", "parent": "t11", "length": 32.77, "rotation": -7.9, "x": 51.77, "y": -1.67}, {"name": "t13", "parent": "t12", "length": 28.99, "rotation": -5.55, "x": 38.84, "y": 6.24}, {"name": "t14", "parent": "t1", "length": 34.01, "rotation": 178.78, "x": 117.25, "y": -15.98}, {"name": "t15", "parent": "t14", "length": 51.9, "rotation": 41.48, "x": 31.38, "y": 12.88}, {"name": "t16", "parent": "t15", "length": 53.99, "rotation": -12.84, "x": 58.8, "y": -2.76}, {"name": "t17", "parent": "t16", "length": 54.68, "rotation": -13.94, "x": 68.04, "y": -4.54}, {"name": "t18", "parent": "t17", "length": 51.94, "rotation": 11.43, "x": 59.69, "y": -1.57}, {"name": "t19", "parent": "t18", "length": 47.61, "rotation": 16.31, "x": 61.76, "y": -3.56}, {"name": "t20", "parent": "t19", "length": 21.19, "rotation": 15.08, "x": 54.03, "y": -5.02}, {"name": "t21", "parent": "t1", "length": 20.41, "rotation": -60.26, "x": 134.02, "y": -19.83}, {"name": "t22", "parent": "t21", "length": 65.5, "rotation": -73.18, "x": 4.37, "y": -7.29}, {"name": "t23", "parent": "t22", "length": 56.73, "rotation": 9.09, "x": 75.27, "y": -2.95}, {"name": "t24", "parent": "t23", "length": 49.56, "rotation": 0.28, "x": 63.93, "y": -1.87}, {"name": "t25", "parent": "t24", "length": 49.76, "rotation": 5.64, "x": 56.74, "y": -1.91}, {"name": "t26", "parent": "t25", "length": 53.93, "rotation": -18.54, "x": 57.28, "y": -0.38}, {"name": "t27", "parent": "t26", "length": 43.11, "rotation": -17.16, "x": 60.86, "y": -2.38}, {"name": "t28", "parent": "t27", "length": 30.32, "rotation": -23.3, "x": 47.09, "y": -4.84}, {"name": "s2", "parent": "bone2", "length": 278.13, "rotation": 179.71, "x": 316.93, "y": -153.05}, {"name": "s3", "parent": "s2", "length": 254.83, "rotation": -88.49, "x": 284.37, "y": -5.72}, {"name": "s4", "parent": "s3", "length": 85.99, "rotation": 108.32, "x": 92.72, "y": 27.01}, {"name": "s5", "parent": "s4", "length": 80.3, "rotation": 1.27, "x": 103.13, "y": -5.36}, {"name": "s6", "parent": "s5", "length": 69.44, "rotation": -8.7, "x": 90.31, "y": 0.24}, {"name": "s7", "parent": "s6", "length": 79.42, "rotation": 4.41, "x": 80.83, "y": 0.61}, {"name": "s8", "parent": "s7", "length": 65.95, "rotation": -0.84, "x": 89.77, "y": -4.41}, {"name": "s10", "parent": "s3", "length": 34.23, "rotation": 23.21, "x": 268.91, "y": -6.25}, {"name": "s11", "parent": "s10", "length": 29.2, "rotation": 24.08, "x": 41.98, "y": -0.06}, {"name": "s9", "parent": "s3", "length": 75.56, "rotation": 27.36, "x": 281.25, "y": -26.21}, {"name": "s1", "parent": "bone2", "length": 278.78, "rotation": 171.88, "x": 321.64, "y": 37.68}, {"name": "s12", "parent": "s1", "length": 230.65, "rotation": -69.42, "x": 281.25, "y": -3.3}, {"name": "s14", "parent": "s12", "length": 20.58, "rotation": -97.22, "x": 282.39, "y": 10.97}, {"name": "s15", "parent": "s14", "length": 24.56, "rotation": -12.53, "x": 23.53, "y": 0.21}, {"name": "s16", "parent": "s12", "length": 25.56, "rotation": -84.46, "x": 293.39, "y": 3.75}, {"name": "s17", "parent": "s16", "length": 23.49, "rotation": -16.73, "x": 28.74, "y": -0.9}, {"name": "t29", "parent": "t1", "length": 17.54, "rotation": -150.26, "x": -31.31, "y": 26.79}, {"name": "t30", "parent": "t29", "length": 14.69, "x": 20.86}, {"name": "t31", "parent": "t30", "length": 13.28, "rotation": 2.05, "x": 16.59, "y": 0.47}, {"name": "t32", "parent": "t1", "length": 22.91, "rotation": 124.01, "x": 52.7, "y": 163.56}, {"name": "t33", "parent": "t32", "length": 24.74, "rotation": 78.61, "x": 23.47, "y": 7.48}, {"name": "t34", "parent": "t33", "length": 18.46, "rotation": 18.85, "x": 29.35, "y": 1.61}, {"name": "t35", "parent": "t34", "length": 15.28, "rotation": 26.93, "x": 22.88, "y": 1.17}, {"name": "t36", "parent": "t35", "length": 13.24, "rotation": 39.45, "x": 21.3, "y": 0.43}, {"name": "t37", "parent": "t36", "length": 9.23, "rotation": 7.65, "x": 15.98, "y": 0.23}, {"name": "s18", "parent": "s9", "length": 117.18, "rotation": 12.77, "x": -9.82, "y": -13.22}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "t8", "bone": "t29", "attachment": "t8"}, {"name": "t7", "bone": "t7", "attachment": "t7"}, {"name": "t6", "bone": "t21", "attachment": "t6"}, {"name": "s1", "bone": "s1", "attachment": "s1"}, {"name": "s4", "bone": "s16", "attachment": "s4"}, {"name": "s5", "bone": "s14", "attachment": "s5"}, {"name": "st", "bone": "st", "attachment": "st"}, {"name": "t5", "bone": "t14", "attachment": "t5"}, {"name": "t2", "bone": "t11", "attachment": "t2"}, {"name": "t1", "bone": "t4", "attachment": "t1"}, {"name": "t4", "bone": "t6", "attachment": "t4"}, {"name": "t3", "bone": "t32", "attachment": "t3"}, {"name": "s2", "bone": "s2", "attachment": "s2"}, {"name": "s6", "bone": "s18", "attachment": "s6"}, {"name": "s3", "bone": "s10", "attachment": "s3"}], "skins": [{"name": "default", "attachments": {"t4": {"t4": {"type": "mesh", "uvs": [0.40143, 0, 0.48953, 0.10375, 0.63376, 0.27361, 0.74366, 0.40302, 0.83382, 0.50919, 0.92567, 0.61736, 0.98498, 0.6872, 0.77141, 0.85615, 0.62883, 0.96896, 0.35037, 1, 0.21435, 1, 0.1917, 0.90822, 0.17088, 0.82382, 0.14098, 0.70266, 0.10861, 0.57152, 0.07946, 0.4534, 0.05263, 0.34467, 0.02532, 0.234, 0, 0.13141, 0, 0.09922, 0.15038, 0, 0.28959, 0.19662, 0.30907, 0.32032, 0.33342, 0.4457, 0.35289, 0.56605, 0.31881, 0.69811, 0.32368, 0.81178, 0.4795, 0.90205], "triangles": [8, 9, 27, 27, 9, 11, 9, 10, 11, 8, 27, 7, 11, 26, 27, 11, 12, 26, 27, 26, 7, 7, 26, 6, 12, 13, 26, 13, 25, 26, 26, 25, 6, 13, 14, 25, 25, 5, 6, 25, 24, 5, 25, 14, 24, 24, 4, 5, 14, 15, 24, 15, 23, 24, 24, 23, 4, 23, 3, 4, 15, 16, 23, 23, 22, 3, 16, 22, 23, 22, 2, 3, 16, 17, 22, 17, 21, 22, 22, 21, 2, 21, 1, 2, 17, 18, 21, 21, 18, 1, 1, 20, 0, 19, 20, 1, 1, 18, 19], "vertices": [1, 16, -5.46, 6.38, 1, 1, 16, 1.73, 7.21, 1, 2, 16, 13.5, 8.57, 0.93061, 17, -8.14, 10.07, 0.06939, 2, 16, 22.48, 9.61, 0.33666, 17, 0.69, 11.97, 0.66334, 3, 16, 29.84, 10.46, 0.05794, 17, 7.93, 13.52, 0.93814, 18, -9.26, 20.02, 0.00392, 3, 16, 37.34, 11.33, 0.00205, 17, 15.31, 15.11, 0.91176, 18, -1.95, 18.14, 0.08619, 2, 17, 20.08, 16.13, 0.80181, 18, 2.77, 16.93, 0.19819, 2, 17, 31.02, 10.41, 0.15732, 18, 10, 6.92, 0.84268, 1, 18, 14.82, 0.24, 1, 1, 18, 13.34, -6.32, 1, 1, 18, 11.74, -9.01, 1, 1, 18, 6.19, -6.32, 1, 2, 17, 27.86, -3.21, 0.00018, 18, 1.08, -3.85, 0.99982, 2, 17, 19.71, -3.31, 0.9846, 18, -6.25, -0.3, 0.0154, 1, 17, 10.89, -3.41, 1, 1, 17, 2.95, -3.51, 1, 2, 16, 15.96, -5.4, 0.60495, 17, -4.36, -3.6, 0.39505, 2, 16, 8.54, -4.78, 0.99977, 17, -11.8, -3.69, 0.00023, 1, 16, 1.67, -4.2, 1, 1, 16, -0.46, -3.84, 1, 1, 16, -6.43, 0.69, 1, 1, 16, 7.09, 1.63, 1, 2, 16, 15.34, 0.68, 0.97998, 17, -5.56, 2.4, 0.02002, 2, 16, 23.71, -0.17, 0.04085, 17, 2.86, 2.35, 0.95915, 2, 16, 31.74, -1.08, 0.00064, 17, 10.93, 2.22, 0.99936, 2, 17, 19.7, 0.79, 0.99681, 18, -4.42, 3.38, 0.00319, 1, 18, 2.18, -0.41, 1, 1, 18, 9.21, -0.42, 1], "hull": 21, "edges": [0, 40, 16, 18, 18, 20, 36, 38, 38, 40, 34, 36, 34, 42, 0, 2, 42, 2, 32, 34, 32, 44, 2, 4, 44, 4, 30, 32, 30, 46, 4, 6, 46, 6, 28, 30, 28, 48, 6, 8, 48, 8, 26, 28, 26, 50, 8, 10, 10, 12, 50, 10, 24, 26, 24, 52, 52, 12, 20, 22, 22, 24, 22, 54, 12, 14, 14, 16, 54, 14], "width": 23, "height": 67}}, "t5": {"t5": {"type": "mesh", "uvs": [0.71569, 0, 0.76767, 0.0993, 0.8161, 0.19184, 0.8569, 0.26979, 0.89405, 0.34075, 0.93159, 0.41247, 0.95604, 0.45919, 0.78698, 0.52863, 0.5709, 0.61739, 0.53067, 0.6707, 0.50613, 0.7032, 0.55812, 0.75962, 0.60378, 0.80916, 0.63225, 0.84005, 0.60912, 0.90285, 0.58943, 0.95629, 0.57457, 0.99663, 0.35579, 0.98565, 0.12041, 0.97384, 0.07159, 0.93383, 0, 0.87517, 0, 0.81923, 0.03101, 0.78468, 0.07523, 0.73542, 0.12833, 0.67627, 0.19142, 0.60599, 0.24956, 0.54122, 0.31232, 0.47131, 0.36579, 0.41174, 0.42947, 0.3408, 0.49129, 0.27194, 0.51737, 0.241, 0.40466, 0.18018, 0.31296, 0.13069, 0.6194, 0.00977, 0.66451, 0, 0.62752, 0.1437, 0.6937, 0.2124, 0.69021, 0.27048, 0.67976, 0.34246, 0.63797, 0.41444, 0.58224, 0.47006, 0.51606, 0.5361, 0.4255, 0.6099, 0.30707, 0.67288, 0.33493, 0.72687, 0.3628, 0.77349, 0.36977, 0.82011, 0.3628, 0.87164, 0.4255, 0.92972], "triangles": [16, 17, 15, 17, 49, 15, 17, 18, 49, 18, 19, 49, 15, 49, 14, 19, 48, 49, 19, 20, 48, 49, 48, 14, 14, 48, 13, 20, 21, 48, 13, 47, 12, 13, 48, 47, 48, 21, 47, 21, 22, 47, 22, 46, 47, 47, 46, 12, 46, 11, 12, 46, 22, 23, 46, 23, 45, 46, 45, 11, 45, 10, 11, 23, 24, 45, 24, 44, 45, 45, 44, 10, 10, 44, 9, 24, 25, 44, 44, 43, 9, 44, 25, 43, 9, 43, 8, 25, 26, 43, 33, 34, 1, 1, 34, 0, 34, 35, 0, 43, 42, 8, 8, 42, 7, 43, 26, 42, 26, 27, 42, 42, 41, 7, 42, 27, 41, 7, 41, 6, 27, 28, 41, 6, 40, 5, 6, 41, 40, 41, 28, 40, 40, 39, 5, 39, 4, 5, 28, 29, 40, 40, 29, 39, 4, 39, 3, 29, 30, 39, 39, 30, 38, 39, 38, 3, 30, 31, 38, 3, 38, 37, 38, 31, 37, 37, 2, 3, 31, 32, 37, 32, 36, 37, 37, 36, 2, 36, 1, 2, 32, 33, 36, 36, 33, 1], "vertices": [1, 22, -16.53, -4.4, 1, 2, 23, -9.49, 16.47, 0.51824, 22, 13.36, 18.93, 0.48176, 1, 23, 25.78, 14.3, 1, 2, 23, 55.49, 12.48, 0.74613, 24, -6.62, 14.12, 0.25387, 2, 23, 82.53, 10.82, 0.00082, 24, 20.12, 18.52, 0.99918, 2, 24, 47.14, 22.96, 0.95643, 25, -26.9, 21.65, 0.04357, 2, 24, 64.74, 25.85, 0.63052, 25, -10.51, 28.7, 0.36948, 2, 24, 91.65, 11.88, 0.0228, 25, 18.97, 21.62, 0.9772, 2, 25, 56.64, 12.58, 0.7665, 26, -0.18, 14.48, 0.2335, 2, 25, 77.04, 14.8, 0.01346, 26, 20.25, 12.61, 0.98654, 1, 26, 32.71, 11.47, 1, 2, 26, 53.63, 17.88, 0.72573, 27, -1.78, 22.86, 0.27427, 2, 26, 72, 23.51, 0.15152, 27, 17.43, 23.11, 0.84848, 2, 26, 83.45, 27.02, 0.03414, 27, 29.41, 23.26, 0.96586, 2, 27, 52.32, 16.51, 0.80886, 28, 3.96, 21.23, 0.19114, 2, 27, 71.82, 10.76, 0.06919, 28, 21.29, 10.6, 0.93081, 1, 28, 34.37, 2.58, 1, 1, 28, 21.94, -12.97, 1, 2, 27, 70.03, -31.47, 0.00099, 28, 8.57, -29.71, 0.99901, 2, 27, 54.31, -32.71, 0.12737, 28, -6.94, -26.81, 0.87263, 2, 27, 31.25, -34.53, 0.64758, 28, -29.67, -22.57, 0.35242, 3, 26, 80.32, -29.71, 0.00016, 27, 10.48, -30.31, 0.92217, 28, -48.63, -13.09, 0.07767, 3, 26, 67.04, -28.06, 0.05556, 27, -1.81, -25, 0.92741, 28, -59.11, -4.76, 0.01703, 2, 26, 48.11, -25.71, 0.54864, 27, -19.32, -17.43, 0.45136, 2, 26, 25.37, -22.89, 0.96854, 27, -40.35, -8.34, 0.03146, 2, 25, 61.95, -21.05, 0.14259, 26, -1.64, -19.54, 0.85741, 2, 25, 36.94, -22.96, 0.91231, 26, -26.54, -16.45, 0.08769, 2, 24, 71.65, -31.21, 0.06578, 25, 9.94, -25.02, 0.93422, 2, 24, 48.9, -27.37, 0.63811, 25, -13.07, -26.77, 0.36189, 2, 24, 21.81, -22.8, 0.99312, 25, -40.47, -28.86, 0.00688, 2, 23, 50.35, -19.66, 0.39649, 24, -4.49, -18.36, 0.60351, 3, 23, 39.24, -15.24, 0.86718, 24, -16.3, -16.51, 0.13008, 22, 70.87, 27.45, 0.00273, 2, 23, 14.75, -20.89, 0.70838, 22, 56.27, 6.99, 0.29162, 2, 23, -5.18, -25.49, 0.03556, 22, 44.38, -9.65, 0.96444, 1, 22, -8.95, -9.85, 1, 1, 22, -14.19, -8.31, 1, 1, 23, 4.77, 1.13, 1, 1, 23, 31.45, 2.17, 1, 2, 23, 53.04, -2.16, 0.8342, 24, -5.76, -0.69, 0.1658, 1, 24, 21.54, -0.51, 1, 2, 24, 48.94, -3.13, 0.97345, 25, -18.87, -3.23, 0.02655, 1, 25, 2.75, -2.09, 1, 2, 25, 28.43, -0.73, 0.99981, 26, -30.47, 7.02, 0.00019, 2, 25, 57.54, -0.64, 0.41796, 26, -1.92, 1.34, 0.58204, 2, 26, 22.75, -7.15, 0.99742, 27, -38.44, 7.51, 0.00258, 2, 26, 42.93, -2.96, 0.97719, 27, -17.9, 5.87, 0.02281, 2, 26, 60.33, 1, 0.28562, 27, -0.09, 4.78, 0.71438, 2, 26, 77.89, 3.11, 0.0071, 27, 17.35, 1.87, 0.9929, 2, 27, 36.37, -2.62, 0.96997, 28, -16.42, 6.91, 0.03003, 2, 27, 59.05, -1.54, 0.06857, 28, 5.76, 2.06, 0.93143], "hull": 36, "edges": [0, 70, 40, 42, 60, 62, 66, 68, 68, 70, 62, 64, 64, 66, 64, 72, 0, 2, 72, 2, 62, 74, 2, 4, 74, 4, 60, 76, 4, 6, 76, 6, 58, 60, 58, 78, 6, 8, 78, 8, 56, 58, 56, 80, 8, 10, 10, 12, 80, 10, 54, 56, 54, 82, 82, 12, 52, 54, 52, 84, 12, 14, 14, 16, 84, 14, 50, 52, 50, 86, 86, 16, 48, 50, 48, 88, 16, 18, 18, 20, 88, 18, 46, 48, 46, 90, 90, 20, 42, 44, 44, 46, 44, 92, 20, 22, 92, 22, 42, 94, 22, 24, 24, 26, 94, 24, 40, 96, 96, 26, 36, 38, 38, 40, 38, 98, 26, 28, 98, 28, 28, 30, 30, 32, 32, 34, 34, 36], "width": 89, "height": 379}}, "t6": {"t6": {"type": "mesh", "uvs": [0.23278, 0, 0.31745, 0.06297, 0.38026, 0.10968, 0.45306, 0.16382, 0.52699, 0.2188, 0.59641, 0.27043, 0.66764, 0.3234, 0.74666, 0.38217, 0.8372, 0.44951, 0.92437, 0.51433, 1, 0.57058, 1, 0.59531, 0.98072, 0.69528, 0.96711, 0.76586, 0.95597, 0.82365, 0.94435, 0.88391, 0.93164, 0.94981, 0.92196, 1, 0.87576, 1, 0.79386, 0.93877, 0.71132, 0.87707, 0.63333, 0.81876, 0.61736, 0.75622, 0.60161, 0.69455, 0.58141, 0.61547, 0.56785, 0.56238, 0.5485, 0.48661, 0.41496, 0.40861, 0.28511, 0.36082, 0.13773, 0.30658, 0.11468, 0.25702, 0.08893, 0.20167, 0.05672, 0.13242, 0.03311, 0.08168, 0.00156, 0.01384, 0.17152, 0.0749, 0.21365, 0.12967, 0.25858, 0.18612, 0.33161, 0.24257, 0.37935, 0.2906, 0.47765, 0.34368, 0.57033, 0.3976, 0.70233, 0.46332, 0.79501, 0.5388, 0.83714, 0.59862, 0.83995, 0.69547, 0.80905, 0.75782, 0.78378, 0.81596], "triangles": [46, 22, 23, 47, 22, 46, 47, 46, 13, 21, 22, 47, 14, 47, 13, 20, 21, 47, 15, 47, 14, 20, 47, 15, 19, 20, 15, 16, 19, 15, 18, 19, 16, 17, 18, 16, 45, 23, 24, 12, 45, 44, 45, 46, 23, 13, 45, 12, 46, 45, 13, 9, 43, 42, 25, 26, 43, 43, 9, 10, 44, 43, 10, 11, 44, 10, 25, 43, 44, 24, 25, 44, 12, 44, 11, 44, 45, 24, 42, 7, 8, 26, 41, 42, 9, 42, 8, 26, 42, 43, 40, 5, 6, 28, 39, 40, 41, 40, 6, 41, 6, 7, 27, 28, 40, 27, 40, 41, 42, 41, 7, 26, 27, 41, 38, 37, 3, 38, 3, 4, 38, 30, 31, 38, 31, 37, 38, 4, 5, 39, 38, 5, 39, 29, 30, 39, 30, 38, 40, 39, 5, 28, 29, 39, 35, 0, 1, 33, 34, 35, 2, 36, 35, 2, 35, 1, 32, 33, 35, 32, 35, 36, 3, 37, 36, 3, 36, 2, 31, 32, 36, 31, 36, 37, 35, 34, 0], "vertices": [1, 29, 22.19, 10.79, 1, 2, 29, 34.13, -18.81, 0.27214, 30, 19.63, 25.15, 0.72786, 3, 29, 42.98, -40.76, 0.02363, 30, 43.21, 27.27, 0.97551, 31, -26.88, 34.91, 0.00086, 2, 30, 70.54, 29.73, 0.66871, 31, 0.49, 33.02, 0.33129, 2, 30, 98.29, 32.23, 0.1068, 31, 28.29, 31.11, 0.8932, 3, 30, 124.35, 34.58, 0.00238, 31, 54.39, 29.31, 0.8346, 32, -9.38, 31.23, 0.16302, 2, 31, 81.18, 27.47, 0.1604, 32, 17.39, 29.26, 0.8396, 3, 31, 110.89, 25.43, 0.00033, 32, 47.1, 27.07, 0.81598, 33, -6.75, 29.78, 0.18369, 2, 32, 81.13, 24.56, 0.05021, 33, 26.87, 23.94, 0.94979, 2, 33, 59.24, 18.32, 0.30601, 34, -4.09, 18.35, 0.69399, 1, 34, 24.09, 22.65, 1, 1, 34, 35.41, 19.98, 1, 1, 35, 16.14, 14.32, 1, 2, 35, 49.37, 14.65, 0.70158, 36, -5.62, 18.81, 0.29842, 2, 35, 76.58, 14.92, 0.06432, 36, 19.26, 29.82, 0.93568, 2, 35, 104.94, 15.2, 9e-05, 36, 45.2, 41.3, 0.99991, 1, 36, 73.57, 53.85, 1, 1, 36, 95.18, 63.41, 1, 1, 36, 98.16, 57.62, 1, 1, 36, 77.83, 34.2, 1, 1, 36, 57.35, 10.6, 1, 1, 36, 38, -11.7, 1, 2, 35, 48.18, -34.86, 0.12567, 36, 12.88, -27.13, 0.87433, 3, 34, 67.89, -45.41, 0.08393, 35, 19.42, -39.03, 0.62568, 36, -11.89, -42.35, 0.29039, 4, 33, 74.13, -47.84, 0.06434, 34, 31.07, -39.64, 0.65772, 35, -17.47, -44.39, 0.25765, 36, -43.65, -61.86, 0.02029, 4, 33, 51.92, -36.3, 0.40531, 34, 6.34, -35.77, 0.55455, 35, -42.24, -47.98, 0.03982, 36, -64.98, -74.96, 0.00032, 2, 33, 20.23, -19.84, 0.99561, 34, -28.94, -30.24, 0.00439, 2, 32, 37.61, -20.38, 0.84817, 33, -20.86, -16.51, 0.15183, 2, 31, 73.43, -28.73, 0.08229, 32, 9.37, -26.9, 0.91771, 2, 31, 41.42, -36.28, 0.78945, 32, -22.67, -34.3, 0.21055, 2, 31, 19.05, -29.03, 0.9803, 32, -45.01, -26.94, 0.0197, 2, 30, 72.71, -24.56, 0.19533, 31, -5.94, -20.93, 0.80467, 2, 30, 40.24, -19.49, 0.98185, 31, -37.2, -10.8, 0.01815, 1, 30, 16.45, -15.77, 1, 2, 29, -10.41, 4.29, 0.97716, 30, -15.36, -10.8, 0.02284, 2, 29, 13.55, -24.41, 0.03881, 30, 19.05, 3.83, 0.96119, 2, 29, 19.49, -50.15, 0.00061, 30, 45.4, 2.07, 0.99939, 2, 30, 72.63, 0.45, 0.35154, 31, -2.06, 3.78, 0.64846, 2, 30, 101.01, 2.63, 0.00236, 31, 26.3, 1.45, 0.99764, 2, 31, 49.55, -2.36, 0.98408, 32, -14.38, -0.41, 0.01592, 2, 31, 78.05, -0.79, 0.00269, 32, 14.13, 1.01, 0.99731, 1, 32, 42.63, 1.56, 1, 2, 32, 78.57, 4.63, 0.0095, 33, 22.36, 4.36, 0.9905, 3, 33, 59.39, -3.25, 0.02165, 34, 2.91, -2.05, 0.97796, 35, -55.46, -16.78, 0.00039, 4, 33, 86.41, -13.03, 0.0017, 34, 31.64, -2.73, 0.98938, 35, -27.81, -8.95, 0.00866, 36, -67.17, -33.4, 0.00025, 2, 35, 17.58, -5.48, 0.9644, 36, -26.86, -12.26, 0.0356, 1, 36, 1.2, -2.75, 1, 2, 35, 74.61, -9.55, 0.00521, 36, 27.13, 6.57, 0.99479], "hull": 35, "edges": [0, 68, 20, 22, 34, 36, 52, 54, 66, 68, 66, 70, 0, 2, 70, 2, 64, 66, 64, 72, 2, 4, 72, 4, 62, 64, 62, 74, 4, 6, 74, 6, 58, 60, 60, 62, 60, 76, 6, 8, 76, 8, 58, 78, 8, 10, 78, 10, 54, 56, 56, 58, 56, 80, 10, 12, 80, 12, 54, 82, 12, 14, 82, 14, 52, 84, 14, 16, 84, 16, 50, 52, 50, 86, 16, 18, 18, 20, 86, 18, 48, 50, 48, 88, 88, 22, 46, 48, 46, 90, 22, 24, 90, 24, 42, 44, 44, 46, 44, 92, 24, 26, 92, 26, 42, 94, 26, 28, 94, 28, 40, 42, 28, 30, 40, 30, 36, 38, 38, 40, 30, 32, 32, 34, 38, 32], "width": 141, "height": 470}}, "t7": {"t7": {"type": "mesh", "uvs": [0.08935, 0, 1, 0.44698, 1, 0.53603, 0.92403, 0.68172, 0.84386, 0.83545, 0.7674, 0.98206, 0.4048, 0.98032, 0.11869, 0.97895, 0.10432, 0.81529, 0.08548, 0.60069, 0.05932, 0.30278, 0.03804, 0.0605, 0.05784, 0, 0.37415, 0.23479, 0.70428, 0.42995, 0.38951, 0.48707, 0.73883, 0.61559, 0.48164, 0.73459, 0.46244, 0.90119], "triangles": [16, 14, 2, 3, 16, 2, 17, 15, 16, 9, 15, 17, 8, 9, 17, 4, 16, 3, 17, 16, 4, 18, 8, 17, 18, 17, 4, 7, 8, 18, 6, 7, 18, 5, 18, 4, 6, 18, 5, 0, 1, 13, 11, 12, 0, 13, 11, 0, 10, 11, 13, 1, 14, 13, 15, 13, 14, 10, 13, 15, 14, 1, 2, 9, 10, 15, 15, 14, 16], "vertices": [1, 14, -8.43, 1.16, 1, 2, 14, 18.53, 25.03, 0.53269, 15, 11.51, 23.63, 0.46731, 2, 14, 22.92, 24.24, 0.45958, 15, 14.37, 20.23, 0.54042, 2, 14, 29.67, 20.64, 0.24338, 15, 17.26, 13.14, 0.75662, 2, 14, 36.8, 16.85, 0.04106, 15, 20.31, 5.66, 0.95894, 1, 15, 23.22, -1.48, 1, 1, 15, 14.57, -8.65, 1, 1, 15, 7.74, -14.31, 1, 1, 15, 2.13, -8.34, 1, 2, 14, 21.11, -4.23, 0.51245, 15, -5.23, -0.51, 0.48755, 1, 14, 6.31, -2.41, 1, 1, 14, -5.73, -0.93, 1, 1, 14, -8.6, 0.2, 1, 2, 14, 4.68, 7.79, 0.98959, 15, -10.17, 19.25, 0.01041, 2, 14, 16.08, 16.15, 0.67862, 15, 3.95, 18.38, 0.32138, 2, 14, 17.18, 6.04, 0.83488, 15, -1.68, 9.91, 0.16512, 2, 14, 25.41, 15.57, 0.32443, 15, 10.74, 11.97, 0.67557, 2, 14, 29.86, 6.68, 0.03705, 15, 8.48, 2.28, 0.96295, 1, 15, 13.39, -4.47, 1], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 22, 24, 22, 26, 26, 28, 28, 4, 20, 22, 20, 30, 30, 32, 4, 6, 32, 6, 18, 20, 18, 34, 6, 8, 8, 10, 34, 8, 14, 16, 16, 18, 16, 36, 36, 10, 10, 12, 12, 14], "width": 31, "height": 50}}, "t8": {"t8": {"type": "mesh", "uvs": [0.27936, 0.15241, 0.46474, 0.18145, 0.65025, 0.21051, 0.81062, 0.23564, 0.91523, 0.25203, 1, 0.56591, 1, 0.6422, 0.81753, 0.73862, 0.5838, 0.86212, 0.32288, 1, 0.21132, 1, 0.06059, 0.95027, 0.07522, 0.6591, 0.08859, 0.393, 0.10207, 0.12463, 0.24397, 0.39021, 0.25178, 0.66147, 0.41833, 0.42286, 0.36629, 0.65896, 0.61872, 0.44296, 0.57708, 0.73933, 0.79048, 0.51831], "triangles": [10, 16, 9, 8, 18, 20, 8, 9, 18, 10, 11, 16, 8, 20, 7, 20, 21, 7, 20, 19, 21, 7, 21, 6, 9, 16, 18, 11, 12, 16, 12, 13, 16, 20, 18, 19, 16, 15, 18, 13, 15, 16, 19, 18, 17, 18, 15, 17, 21, 5, 6, 21, 4, 5, 21, 3, 4, 21, 19, 3, 17, 1, 19, 19, 2, 3, 19, 1, 2, 17, 15, 1, 13, 14, 15, 15, 0, 1, 15, 14, 0], "vertices": [1, 53, -13.1, -5.94, 1, 2, 53, -10.6, 9.45, 0.97806, 55, -47.7, 10.69, 0.02194, 3, 53, -8.1, 24.85, 0.87095, 54, -28.96, 24.85, 0.00077, 55, -44.65, 25.98, 0.12828, 3, 53, -5.94, 38.16, 0.76365, 54, -26.8, 38.16, 0.00167, 55, -42.02, 39.21, 0.23468, 3, 53, -4.53, 46.84, 0.72681, 54, -25.39, 46.84, 0.00182, 55, -40.3, 47.84, 0.27137, 3, 53, 22.46, 53.88, 0.51705, 54, 1.61, 53.88, 0.02807, 55, -13.07, 53.9, 0.45488, 3, 53, 29.02, 53.88, 0.48928, 54, 8.17, 53.88, 0.03151, 55, -6.51, 53.67, 0.47921, 3, 53, 37.31, 38.73, 0.34921, 54, 16.46, 38.73, 0.06123, 55, 1.23, 38.24, 0.58956, 3, 53, 47.94, 19.33, 0.05683, 54, 27.08, 19.33, 0.02043, 55, 11.16, 18.47, 0.92274, 2, 54, 38.94, -2.32, 0.02347, 55, 22.23, -3.59, 0.97653, 2, 54, 38.94, -11.58, 0.12687, 55, 21.9, -12.85, 0.87313, 2, 54, 34.66, -24.09, 0.29343, 55, 17.18, -25.2, 0.70657, 3, 53, 30.48, -22.88, 0.11807, 54, 9.62, -22.88, 0.77848, 55, -7.8, -23.09, 0.10345, 2, 53, 7.59, -21.77, 0.81041, 54, -13.26, -21.77, 0.18959, 1, 53, -15.49, -20.65, 1, 2, 53, 7.35, -8.87, 0.92698, 54, -13.5, -8.87, 0.07302, 3, 53, 30.68, -8.22, 0.0382, 54, 9.82, -8.22, 0.93566, 55, -7.07, -8.45, 0.02613, 3, 53, 10.16, 5.6, 0.96265, 54, -10.7, 5.6, 0.0142, 55, -27.09, 6.1, 0.02315, 3, 53, 30.46, 1.28, 0.0018, 54, 9.61, 1.28, 0.96822, 55, -6.95, 1.05, 0.02998, 3, 53, 11.89, 22.23, 0.69772, 54, -8.97, 22.23, 0.08825, 55, -24.77, 22.66, 0.21403, 3, 53, 37.38, 18.78, 0.11803, 54, 16.52, 18.78, 0.11562, 55, 0.58, 18.29, 0.76635, 3, 53, 18.37, 36.49, 0.54609, 54, -2.49, 36.49, 0.05503, 55, -17.78, 36.67, 0.39887], "hull": 15, "edges": [8, 10, 10, 12, 18, 20, 20, 22, 0, 28, 0, 30, 30, 32, 32, 20, 0, 2, 2, 34, 34, 36, 36, 18, 2, 4, 4, 38, 38, 40, 16, 18, 40, 16, 4, 6, 6, 8, 6, 42, 12, 14, 14, 16, 42, 14, 26, 28, 22, 24, 24, 26], "width": 83, "height": 86}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [370, -637.96, -391, -637.96, -391, 798.44, 370, 798.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 342, "height": 567}}, "s2": {"s2": {"type": "mesh", "uvs": [0.75431, 0, 0.84359, 0.00611, 0.85701, 0.04575, 0.8774, 0.10596, 0.89641, 0.1621, 0.91338, 0.21222, 0.93137, 0.26534, 0.95246, 0.32765, 0.97311, 0.38864, 1, 0.46805, 0.9675, 0.54884, 0.94765, 0.59817, 0.93557, 0.62819, 0.94154, 0.64969, 0.93577, 0.73459, 0.92835, 0.84392, 0.91779, 0.99936, 0.84296, 0.99932, 0.6788, 0.99922, 0.28333, 0.99899, 0.24515, 0.91985, 0.19851, 0.77345, 0.16421, 0.66578, 0.1311, 0.56184, 0.13343, 0.45046, 0.06863, 0.43623, 0.00478, 0.42222, 0.00013, 0.34941, 0.0665, 0.35698, 0.15614, 0.36719, 0.1847, 0.37045, 0.31207, 0.38497, 0.34309, 0.37849, 0.4614, 0.32031, 0.48969, 0.3064, 0.57136, 0.26561, 0.58532, 0.20819, 0.59694, 0.16043, 0.61045, 0.1049, 0.62246, 0.05554, 0.65405, 0.03599, 0.71221, 0, 0.76224, 0.04478, 0.70549, 0.10594, 0.82655, 0.10929, 0.67996, 0.16185, 0.82182, 0.16185, 0.66104, 0.21154, 0.81425, 0.21345, 0.63362, 0.26174, 0.8029, 0.26174, 0.58822, 0.31048, 0.77264, 0.32815, 0.55418, 0.32029, 0.7159, 0.35039, 0.88518, 0.4149, 0.92585, 0.37476, 0.46528, 0.37005, 0.66577, 0.40923, 0.83128, 0.48711, 0.42272, 0.40139, 0.60714, 0.44296, 0.83033, 0.54856, 0.06429, 0.40044, 0.139, 0.42241, 0.26667, 0.44726, 0.20499, 0.39236, 0.43176, 0.44518, 0.34306, 0.54478, 0.39886, 0.65276, 0.47925, 0.76218, 0.58801, 0.86038, 0.50249, 0.53304, 0.57531, 0.64246, 0.66516, 0.74806, 0.73892, 0.85413, 0.6765, 0.52989, 0.75784, 0.63931, 0.80906, 0.74388, 0.87148, 0.84995], "triangles": [17, 79, 16, 18, 75, 17, 19, 71, 18, 19, 20, 71, 16, 79, 15, 17, 75, 79, 18, 71, 75, 71, 74, 75, 75, 78, 79, 79, 78, 15, 15, 78, 14, 20, 70, 71, 71, 70, 74, 75, 74, 78, 70, 73, 74, 74, 77, 78, 14, 78, 13, 13, 78, 77, 13, 77, 12, 77, 62, 12, 12, 62, 11, 20, 21, 70, 21, 69, 70, 21, 22, 69, 70, 69, 73, 74, 73, 77, 22, 68, 69, 22, 23, 68, 69, 72, 73, 73, 76, 77, 68, 65, 67, 72, 67, 61, 69, 68, 72, 73, 72, 76, 68, 67, 72, 72, 61, 76, 77, 76, 62, 11, 62, 10, 62, 59, 10, 59, 62, 61, 62, 76, 61, 61, 58, 59, 59, 58, 55, 65, 60, 67, 65, 31, 60, 67, 60, 61, 60, 57, 61, 61, 57, 58, 57, 53, 58, 58, 53, 54, 31, 32, 60, 60, 32, 57, 57, 32, 33, 57, 33, 53, 33, 34, 53, 53, 34, 51, 65, 66, 31, 68, 23, 65, 23, 24, 65, 24, 64, 65, 24, 25, 64, 64, 66, 65, 26, 63, 25, 25, 63, 64, 66, 29, 30, 66, 64, 29, 64, 63, 29, 26, 27, 63, 63, 28, 29, 63, 27, 28, 66, 30, 31, 10, 59, 9, 59, 55, 9, 55, 8, 9, 58, 54, 55, 54, 52, 55, 55, 56, 8, 55, 52, 56, 56, 7, 8, 56, 52, 7, 53, 51, 54, 54, 51, 52, 7, 50, 6, 7, 52, 50, 51, 49, 52, 52, 49, 50, 34, 35, 51, 51, 35, 49, 35, 36, 49, 6, 50, 5, 50, 48, 5, 49, 47, 50, 49, 36, 47, 50, 47, 48, 48, 4, 5, 48, 46, 4, 47, 45, 48, 48, 45, 46, 36, 37, 47, 47, 37, 45, 4, 46, 3, 46, 44, 3, 45, 43, 46, 46, 43, 44, 37, 38, 45, 45, 38, 43, 43, 42, 44, 44, 2, 3, 44, 42, 2, 38, 39, 43, 39, 40, 43, 43, 40, 42, 42, 1, 2, 40, 41, 42, 41, 0, 42, 42, 0, 1], "vertices": [1, 37, -28.53, -7.48, 1, 1, 37, -25.34, 27.24, 1, 1, 37, 4.77, 33.74, 1, 1, 37, 50.51, 43.61, 1, 1, 37, 93.15, 52.81, 1, 1, 37, 131.23, 61.02, 1, 1, 37, 171.58, 69.73, 1, 4, 37, 218.92, 79.93, 0.99863, 39, -29.04, 199.29, 0.00016, 40, -127.59, 207.53, 0.00073, 41, -246.75, 171.94, 0.00049, 5, 37, 265.25, 89.82, 0.92575, 38, -96.16, -16.6, 0.03335, 39, 17.91, 193.02, 0.01811, 40, -80.79, 200.22, 0.01423, 41, -199.38, 171.8, 0.00855, 5, 37, 325.59, 102.3, 0.58056, 38, -107.81, 44.05, 0.20588, 39, 79.14, 185.02, 0.0955, 40, -19.75, 190.87, 0.07259, 41, -137.63, 171.78, 0.04547, 6, 37, 387.75, 91.62, 0.24396, 38, -96.11, 105.9, 0.25712, 39, 134.7, 154.47, 0.16009, 40, 35.12, 159.09, 0.18665, 41, -78.59, 148.67, 0.14871, 42, -147.56, 159.88, 0.00346, 6, 37, 425.33, 85.16, 0.11728, 38, -88.8, 143.3, 0.19127, 39, 168.56, 135.79, 0.13712, 40, 68.55, 139.65, 0.24454, 41, -42.6, 134.52, 0.28625, 42, -112.77, 143, 0.02354, 6, 37, 448.03, 81.27, 0.0659, 38, -84.32, 165.88, 0.13512, 39, 189.09, 124.43, 0.0991, 40, 88.82, 127.84, 0.23434, 41, -20.78, 125.91, 0.40808, 42, -91.67, 132.74, 0.05746, 6, 37, 464.01, 84.11, 0.04217, 38, -86.77, 181.94, 0.10016, 39, 205.44, 121.71, 0.0713, 40, 105.11, 124.76, 0.19866, 41, -4.21, 125.33, 0.49178, 42, -75.2, 130.89, 0.09593, 6, 37, 527.73, 84.37, 0.0051, 38, -85.14, 245.64, 0.02302, 39, 266.65, 100.15, 0.01072, 40, 165.82, 101.85, 0.03611, 41, 59.27, 111.87, 0.53156, 42, -12.94, 112.58, 0.3935, 4, 38, -83.22, 328.07, 0.00059, 41, 141.19, 94.58, 0.10111, 42, 67.4, 89.05, 0.84054, 43, -23.73, 93.12, 0.05776, 2, 42, 181.75, 55.61, 0.09979, 43, 91.09, 61.36, 0.90021, 2, 42, 174.54, 27.56, 0.06428, 43, 84.3, 33.21, 0.93572, 2, 40, 326.05, -57.14, 0.00043, 43, 69.4, -28.55, 0.99957, 4, 40, 277, -202.12, 0.05163, 41, 215.16, -171.78, 0.06063, 42, 120.68, -182.21, 0.15661, 43, 33.51, -177.33, 0.73114, 4, 40, 214.84, -196.76, 0.10307, 41, 152.9, -175.88, 0.11692, 42, 58.29, -181.51, 0.20364, 43, -28.88, -177.55, 0.57637, 6, 38, 199.85, 279.55, 0.0049, 39, 209.89, -181.06, 0.00524, 40, 102.82, -178.04, 0.43927, 41, 39.34, -174.33, 0.26161, 42, -54.82, -171.23, 0.1313, 43, -142.12, -168.92, 0.15768, 6, 38, 214.05, 198.7, 0.06527, 39, 127.35, -169.13, 0.08796, 40, 20.57, -164.28, 0.66323, 41, -44.04, -173.17, 0.12915, 42, -137.86, -163.66, 0.0232, 43, -225.27, -162.57, 0.03119, 6, 38, 227.76, 120.63, 0.29716, 39, 47.76, -157.62, 0.23715, 40, -58.74, -150.99, 0.44399, 41, -124.45, -172.04, 0.01932, 42, -217.95, -156.35, 0.00031, 43, -305.46, -156.44, 0.00207, 4, 38, 228.03, 36.08, 0.88752, 39, -32.9, -131.3, 0.06235, 40, -138.8, -122.89, 0.05012, 41, -207.84, -156.37, 1e-05, 3, 38, 253.26, 25.71, 0.99124, 39, -50.45, -151.99, 0.00468, 40, -156.8, -143.19, 0.00407, 1, 38, 278.14, 15.4, 1, 1, 38, 280.87, -40.33, 1, 1, 38, 255.1, -34.97, 1, 1, 38, 220.28, -27.73, 1, 1, 38, 209.19, -25.42, 1, 2, 37, 273.38, -163.27, 0.00036, 38, 159.71, -15.13, 0.99964, 2, 37, 267.91, -151.69, 0.00367, 38, 147.8, -20.29, 0.99633, 2, 37, 221.45, -109.2, 0.22362, 38, 103.04, -65.62, 0.77638, 2, 37, 210.34, -99.06, 0.34279, 38, 92.32, -76.46, 0.65721, 2, 37, 177.78, -69.41, 0.77106, 38, 61.09, -108.23, 0.22894, 2, 37, 133.61, -65.95, 0.97167, 38, 56.28, -152.29, 0.02833, 2, 37, 96.87, -63.03, 0.9984, 38, 52.37, -188.94, 0.0016, 1, 37, 54.14, -59.64, 1, 1, 37, 16.17, -56.63, 1, 1, 37, 0.68, -45.05, 1, 1, 37, -27.83, -23.75, 1, 1, 37, 5.61, -2.94, 1, 1, 37, 53.36, -22.86, 1, 1, 37, 53.9, 24.06, 1, 2, 37, 96.56, -30.89, 0.99975, 38, 20.23, -188.4, 0.00025, 1, 37, 94.2, 23.96, 1, 2, 37, 134.91, -36.57, 0.98403, 38, 26.92, -150.22, 0.01597, 1, 37, 133.82, 22.74, 1, 2, 37, 173.78, -45.51, 0.88466, 38, 36.94, -111.59, 0.11534, 1, 37, 170.96, 19.94, 1, 2, 37, 211.83, -61.31, 0.57704, 38, 54.02, -73.97, 0.42296, 1, 37, 222.3, 10.43, 1, 2, 37, 219.91, -73.96, 0.42917, 38, 67.12, -66.23, 0.57083, 2, 37, 240.26, -10.78, 0.91185, 38, 3.9, -44.23, 0.08815, 5, 37, 286.81, 56.61, 0.83914, 38, -62.48, 4.08, 0.09096, 39, 26.95, 154.54, 0.03608, 40, -72.61, 161.55, 0.02141, 41, -185.45, 134.81, 0.01241, 5, 37, 255.41, 71.14, 0.96935, 38, -77.66, -26.93, 0.01061, 39, 2.31, 178.7, 0.0082, 40, -96.7, 186.25, 0.00737, 41, -213, 155.58, 0.00447, 2, 37, 259.44, -105.64, 0.06304, 38, 100.72, -27.55, 0.93696, 4, 38, 22.54, 1.16, 0.99628, 39, -2.38, 74.75, 0.00369, 40, -103.71, 82.43, 1e-05, 41, -204.22, 51.9, 1e-05, 5, 37, 342.96, 37.57, 0.30557, 38, -42.52, 59.71, 0.37674, 39, 73.62, 118.12, 0.18219, 40, -26.76, 124.1, 0.09007, 41, -134.46, 104.73, 0.04543, 2, 37, 284.12, -120.63, 0.0023, 38, 116.69, -3.27, 0.9977, 3, 38, 44.81, 27.33, 0.65987, 39, 15.5, 45.39, 0.33998, 41, -182.69, 25.09, 0.00015, 6, 37, 389.68, 38.81, 0.14646, 38, -42.82, 106.45, 0.27261, 39, 118.64, 103.71, 0.22066, 40, 17.93, 108.69, 0.22992, 41, -87.95, 96.26, 0.12791, 42, -160.93, 108.34, 0.00244, 1, 38, 255.39, -1.67, 1, 3, 38, 226.21, 14.65, 0.97691, 39, -52.6, -122.84, 0.01301, 40, -158.3, -114, 0.01008, 3, 38, 176.52, 32.74, 0.79878, 39, -20.18, -81.36, 0.15056, 40, -124.98, -73.24, 0.05066, 1, 38, 201.05, -8.77, 1, 3, 38, 112.67, 30.08, 0.25574, 39, -3.18, -19.9, 0.73684, 40, -106.61, -12.18, 0.00742, 5, 38, 145.96, 106.17, 0.15844, 39, 59.54, -75.42, 0.44501, 40, -45.14, -69.08, 0.39063, 41, -123.4, -89.01, 0.00543, 43, -299.24, -73.64, 0.00049, 6, 38, 123.42, 187.19, 0.01893, 39, 144.8, -79.48, 0.04276, 40, 40.01, -75.03, 0.79327, 41, -38.33, -82.01, 0.12139, 42, -125.16, -73.22, 0.00893, 43, -213.9, -71.95, 0.01472, 5, 38, 91.33, 269.21, 0.0001, 40, 129.32, -72.33, 0.238, 41, 49.55, -65.82, 0.52023, 42, -36.3, -63.83, 0.16679, 43, -125.18, -61.26, 0.07489, 4, 40, 214.05, -56.47, 0.0197, 41, 130.9, -37.33, 0.03727, 42, 47.01, -41.68, 0.67684, 43, -42.21, -37.89, 0.26618, 2, 39, 69.19, -13.85, 0.89811, 40, -34.13, -7.74, 0.10189, 3, 40, 54.3, -7.81, 0.99348, 41, -34.38, -13.4, 0.00623, 43, -205.68, -3.72, 0.0003, 5, 37, 542.16, -18.43, 3e-05, 38, 19.52, 257.37, 0.0004, 39, 245.17, -2.89, 4e-05, 41, 51.29, 6.91, 0.97513, 42, -28.96, 8.55, 0.0244, 2, 41, 136.25, 21.02, 0.01019, 42, 56.82, 16.09, 0.98981, 5, 37, 377.79, -20.78, 0.02305, 38, 17.03, 92.99, 0.2501, 39, 87.16, 51.13, 0.544, 40, -14.71, 56.82, 0.16082, 41, -112.37, 40.05, 0.02203, 6, 37, 458.96, 13.56, 0.02223, 38, -15.46, 175.04, 0.07216, 39, 176.54, 56.18, 0.07045, 40, 74.76, 59.89, 0.41779, 41, -24.39, 56.62, 0.39018, 42, -100.61, 63.93, 0.0272, 6, 37, 536.73, 36.33, 0.00212, 38, -36.16, 253.38, 0.01226, 39, 258.77, 51.22, 0.00468, 40, 156.86, 53.1, 0.01483, 41, 57.79, 62.32, 0.58636, 42, -18.23, 63.3, 0.37976, 4, 38, -61.27, 332.98, 0.00031, 41, 141.94, 72.11, 0.07607, 42, 66.43, 66.58, 0.85589, 43, -24.38, 70.64, 0.06773], "hull": 42, "edges": [0, 82, 0, 2, 24, 26, 38, 40, 46, 48, 52, 54, 62, 64, 68, 70, 78, 80, 80, 82, 80, 84, 2, 4, 84, 4, 76, 78, 76, 86, 86, 88, 4, 6, 88, 6, 74, 76, 74, 90, 90, 92, 6, 8, 92, 8, 70, 72, 72, 74, 72, 94, 94, 96, 8, 10, 96, 10, 70, 98, 98, 100, 10, 12, 100, 12, 68, 102, 102, 104, 12, 14, 104, 14, 64, 66, 66, 68, 66, 106, 106, 108, 108, 110, 110, 18, 104, 112, 14, 16, 16, 18, 112, 16, 64, 114, 114, 116, 116, 118, 18, 20, 118, 20, 62, 120, 120, 122, 122, 124, 20, 22, 22, 24, 124, 22, 54, 56, 56, 126, 48, 50, 50, 52, 126, 50, 56, 58, 58, 128, 128, 48, 48, 130, 58, 60, 60, 62, 60, 132, 132, 130, 122, 134, 134, 130, 130, 136, 136, 138, 138, 140, 140, 142, 36, 38, 142, 36, 134, 144, 144, 146, 146, 148, 148, 150, 32, 34, 34, 36, 150, 34, 122, 152, 152, 154, 154, 156, 156, 158, 158, 32, 26, 28, 156, 28, 28, 30, 30, 32, 158, 30, 44, 46, 138, 44, 40, 42, 42, 44, 140, 42, 142, 40], "width": 387, "height": 766}}, "s1": {"s1": {"type": "mesh", "uvs": [0.98539, 0.04304, 0.92829, 0.12685, 0.86818, 0.21509, 0.80495, 0.30791, 0.81864, 0.34996, 0.82915, 0.39934, 0.84953, 0.49505, 0.87815, 0.62949, 0.90157, 0.73951, 0.86527, 0.77568, 0.81116, 0.8296, 0.76194, 0.87864, 0.71983, 0.9206, 0.54119, 0.90165, 0.419, 0.88869, 0.23379, 0.86904, 0, 0.60593, 0, 0.5336, 0.06132, 0.53288, 0.17179, 0.53836, 0.31806, 0.67295, 0.37698, 0.67383, 0.41358, 0.67439, 0.47839, 0.67536, 0.51571, 0.63796, 0.56003, 0.59356, 0.65085, 0.50255, 0.70691, 0.44639, 0.75434, 0.40544, 0.76304, 0.36559, 0.77518, 0.30995, 0.79208, 0.23251, 0.80684, 0.16487, 0.82037, 0.10288, 0.95594, 0.05059, 0.76993, 0.47867, 0.73127, 0.54952, 0.83832, 0.66441, 0.65078, 0.6017, 0.74692, 0.71372, 0.59596, 0.66669, 0.68814, 0.76531, 0.53451, 0.72701, 0.65147, 0.81796, 0.51072, 0.76435, 0.54343, 0.84573, 0.4007, 0.71169, 0.41854, 0.78254], "triangles": [38, 25, 26, 38, 26, 36, 16, 17, 18, 40, 25, 38, 24, 25, 40, 46, 21, 22, 38, 36, 39, 40, 38, 39, 42, 24, 40, 23, 24, 42, 44, 23, 42, 22, 23, 44, 41, 40, 39, 42, 40, 41, 39, 37, 9, 44, 46, 22, 47, 46, 44, 43, 42, 41, 44, 42, 43, 10, 39, 9, 41, 39, 10, 45, 44, 43, 20, 16, 19, 20, 47, 15, 46, 20, 21, 16, 18, 19, 20, 15, 16, 11, 41, 10, 43, 41, 11, 45, 14, 47, 45, 47, 44, 47, 20, 46, 14, 15, 47, 13, 14, 45, 12, 43, 11, 13, 45, 43, 12, 13, 43, 1, 33, 34, 1, 34, 0, 32, 33, 1, 2, 32, 1, 31, 32, 2, 3, 31, 2, 30, 31, 3, 30, 3, 4, 29, 30, 4, 29, 4, 5, 28, 29, 5, 35, 28, 5, 27, 28, 35, 35, 5, 6, 36, 27, 35, 26, 27, 36, 6, 36, 35, 7, 36, 6, 7, 37, 36, 39, 36, 37, 37, 7, 8, 9, 37, 8], "vertices": [1, 47, -21.32, 24.2, 1, 1, 47, 22.07, 5.73, 1, 1, 47, 67.75, -13.71, 1, 1, 47, 115.8, -34.16, 1, 1, 47, 134.13, -24.51, 1, 2, 47, 156.12, -15.67, 0.99802, 48, -32.42, -121.49, 0.00198, 1, 47, 198.73, 1.47, 1, 1, 47, 258.57, 25.55, 1, 2, 47, 307.55, 45.25, 0.72152, 48, -36.21, 41.69, 0.27848, 2, 47, 327.22, 32.07, 0.45409, 48, -16.95, 55.47, 0.54591, 2, 47, 356.54, 12.43, 0.10758, 48, 11.75, 76.01, 0.89242, 2, 47, 383.22, -5.44, 0.01425, 48, 37.85, 94.7, 0.98575, 2, 47, 406.04, -20.72, 0.00038, 48, 60.18, 110.69, 0.99962, 1, 48, 138.39, 87.44, 1, 1, 48, 191.88, 71.53, 1, 1, 48, 272.97, 47.43, 1, 1, 48, 355.31, -93.21, 1, 1, 48, 349.23, -126.66, 1, 1, 48, 321.78, -122.02, 1, 1, 48, 272.9, -110.52, 1, 1, 48, 218.86, -36.42, 1, 1, 48, 192.61, -31.23, 1, 1, 48, 176.31, -28.01, 1, 1, 48, 147.44, -22.3, 1, 2, 47, 291.89, -135.64, 0.0056, 48, 127.63, -36.57, 0.9944, 2, 47, 267.76, -119.57, 0.05247, 48, 104.11, -53.51, 0.94753, 2, 47, 218.31, -86.64, 0.46224, 48, 55.89, -88.23, 0.53776, 2, 47, 187.79, -66.32, 0.7982, 48, 26.14, -109.65, 0.2018, 2, 47, 165, -48.57, 0.96119, 48, 1.51, -124.74, 0.03881, 2, 47, 145.87, -48.04, 0.9952, 48, -5.72, -142.47, 0.0048, 1, 47, 119.16, -47.28, 1, 1, 47, 81.98, -46.24, 1, 1, 47, 49.5, -45.33, 1, 1, 47, 19.74, -44.49, 1, 1, 47, -15.44, 11.68, 1, 2, 47, 197.61, -35.46, 0.89472, 48, 0.7, -89.61, 0.10528, 2, 47, 233.51, -46.78, 0.57542, 48, 23.92, -59.98, 0.42458, 1, 47, 277.95, 10.69, 1, 2, 47, 264.16, -78.35, 0.13944, 48, 64.25, -42.38, 0.86056, 2, 47, 308.17, -26, 0.00217, 48, 30.71, 17.22, 0.99783, 2, 47, 298.67, -97.38, 0.00913, 48, 94.19, -16.77, 0.99087, 2, 47, 336.79, -47.92, 0.0001, 48, 61.3, 36.31, 0.9999, 1, 48, 126.71, 6.13, 1, 1, 48, 82.1, 57.68, 1, 1, 48, 140.47, 21.47, 1, 1, 48, 132.69, 61.76, 1, 1, 48, 185.2, -11.8, 1, 1, 48, 183.18, 22.41, 1], "hull": 35, "edges": [0, 68, 6, 8, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 54, 56, 66, 68, 64, 66, 0, 2, 64, 2, 62, 64, 2, 4, 4, 6, 62, 4, 60, 62, 60, 6, 8, 10, 56, 10, 56, 58, 58, 60, 54, 70, 10, 12, 70, 12, 52, 54, 52, 72, 72, 74, 16, 18, 74, 18, 12, 14, 14, 16, 50, 52, 50, 76, 76, 78, 18, 20, 78, 20, 46, 48, 48, 50, 48, 80, 80, 82, 20, 22, 22, 24, 82, 22, 46, 84, 84, 86, 86, 24, 44, 46, 44, 88, 88, 90, 24, 26, 90, 26, 40, 42, 42, 44, 42, 92, 92, 94, 26, 28, 28, 30, 94, 28], "width": 454, "height": 470}}, "st": {"st": {"type": "mesh", "uvs": [0.80427, 0.14455, 0.89221, 0.21614, 0.94775, 0.26136, 1, 0.30389, 1, 0.36326, 0.97932, 0.44057, 0.9705, 0.47352, 0.96241, 0.50375, 0.86463, 0.55247, 0.78749, 0.59091, 0.68611, 0.64142, 0.67078, 0.69502, 0.69586, 0.81142, 0.7093, 0.87384, 0.71843, 0.91624, 0.72804, 0.96081, 0.74518, 0.99798, 0.91532, 0.99777, 1, 0.99907, 0.45763, 0.99915, 0.27657, 0.99917, 0.14139, 0.99919, 0.06915, 0.9992, 0.03747, 0.94449, 0, 0.87979, 0, 0.82119, 0.02138, 0.72309, 0.03385, 0.66587, 0.08117, 0.65408, 0.17228, 0.63138, 0.24235, 0.63442, 0.33391, 0.6384, 0.49087, 0.64521, 0.5361, 0.62418, 0.6113, 0.58923, 0.61053, 0.5501, 0.60965, 0.50576, 0.59081, 0.48336, 0.57094, 0.45195, 0.54637, 0.41311, 0.6057, 0.3511, 0.64853, 0.30634, 0.70099, 0.25151, 0.77047, 0.17889, 0.75998, 0.24603, 0.84927, 0.22672, 0.6881, 0.30126, 0.83315, 0.28465, 0.63409, 0.35149, 0.77868, 0.34845, 0.92368, 0.32772, 0.59905, 0.41199, 0.72607, 0.40766, 0.91376, 0.39159, 0.62477, 0.4554, 0.71096, 0.4556, 0.90756, 0.44729, 0.66325, 0.48517, 0.71223, 0.48422, 0.90136, 0.47997, 0.68682, 0.52012, 0.76495, 0.52234, 0.71309, 0.56196, 0.69325, 0.59241, 0.62753, 0.62286, 0.58164, 0.66508, 0.48119, 0.69205, 0.6238, 0.75592, 0.40554, 0.71953, 0.59404, 0.80048, 0.36834, 0.74404, 0.57792, 0.83539, 0.31129, 0.78536, 0.5742, 0.89007, 0.24681, 0.83066, 0.52459, 0.94206, 0.17736, 0.88933, 0.39934, 0.9636, 0.10419, 0.92943, 0.24309, 0.97994, 0.86548, 0.33604, 0.84384, 0.39758, 0.84699, 0.44985, 0.82712, 0.48164], "triangles": [20, 77, 19, 20, 79, 77, 18, 16, 17, 18, 19, 16, 19, 75, 16, 19, 77, 75, 16, 75, 15, 75, 73, 15, 79, 76, 77, 77, 74, 75, 15, 73, 14, 73, 71, 14, 75, 72, 73, 71, 13, 14, 22, 23, 21, 21, 79, 20, 23, 78, 21, 21, 78, 79, 78, 76, 79, 76, 74, 77, 23, 24, 78, 78, 24, 76, 74, 76, 25, 25, 76, 24, 74, 72, 75, 73, 72, 71, 71, 72, 70, 74, 25, 26, 72, 74, 26, 26, 27, 28, 72, 26, 28, 28, 29, 72, 72, 29, 70, 29, 30, 70, 70, 30, 68, 71, 69, 13, 69, 12, 13, 71, 70, 69, 69, 70, 68, 69, 67, 12, 67, 11, 12, 68, 66, 69, 69, 66, 67, 66, 65, 67, 67, 65, 11, 30, 31, 68, 68, 31, 66, 11, 65, 10, 66, 32, 65, 66, 31, 32, 32, 33, 65, 65, 33, 64, 5, 53, 4, 50, 3, 4, 50, 2, 3, 50, 47, 2, 46, 42, 44, 2, 45, 1, 2, 47, 45, 47, 44, 45, 44, 42, 43, 44, 43, 45, 45, 43, 1, 43, 0, 1, 37, 54, 57, 57, 55, 58, 57, 54, 55, 37, 38, 54, 83, 55, 82, 55, 52, 82, 55, 54, 52, 38, 51, 54, 54, 51, 52, 38, 39, 51, 52, 81, 82, 82, 81, 56, 56, 53, 5, 56, 81, 53, 51, 39, 40, 51, 48, 52, 51, 40, 48, 52, 49, 81, 52, 48, 49, 81, 80, 53, 81, 49, 80, 53, 50, 4, 53, 80, 50, 48, 46, 49, 48, 41, 46, 48, 40, 41, 49, 47, 80, 49, 46, 47, 80, 47, 50, 46, 41, 42, 59, 82, 56, 46, 44, 47, 10, 65, 64, 10, 63, 9, 10, 64, 63, 33, 34, 64, 64, 34, 63, 63, 62, 9, 62, 63, 35, 9, 62, 8, 63, 34, 35, 62, 61, 8, 35, 60, 62, 62, 60, 61, 8, 61, 7, 35, 36, 60, 61, 59, 7, 61, 83, 59, 60, 58, 61, 61, 58, 83, 36, 57, 60, 60, 57, 58, 36, 37, 57, 7, 59, 6, 58, 55, 83, 83, 82, 59, 59, 56, 6, 6, 56, 5], "vertices": [1, 5, 146.79, -49.76, 1, 1, 5, 53.88, -63.61, 1, 1, 5, -4.81, -72.36, 1, 3, 3, 316.45, -176.92, 0.04946, 4, 114.12, -206.72, 0.0111, 5, -60.02, -80.59, 0.93944, 3, 3, 253.75, -179.3, 0.15891, 4, 51.41, -209.1, 0.06935, 5, -115.49, -51.27, 0.77174, 3, 3, 171.6, -169.32, 0.45469, 4, -30.74, -199.12, 0.12322, 5, -181.62, -1.51, 0.42209, 3, 3, 136.57, -165.06, 0.59863, 4, -65.76, -194.86, 0.10411, 5, -209.81, 19.71, 0.29726, 3, 3, 104.45, -161.16, 0.69209, 4, -97.89, -190.96, 0.08115, 5, -235.66, 39.16, 0.22676, 3, 3, 50.64, -101.27, 0.91029, 4, -151.69, -131.07, 0.01809, 5, -252.27, 117.95, 0.07162, 2, 3, 8.19, -54.01, 0.99227, 5, -265.37, 180.11, 0.00773, 2, 3, -47.6, 8.09, 0.68471, 6, -105.52, -18.87, 0.31529, 4, 3, -104.58, 15.64, 0.1708, 6, -67.04, 23.82, 0.82046, 8, -152.71, 173.26, 0.00353, 9, -43.59, 317.1, 0.00521, 4, 6, -14.69, 136.3, 0.64273, 7, 4.87, 175.23, 0.04541, 8, -29.68, 189.13, 0.13057, 9, 48.19, 233.63, 0.18128, 4, 6, 13.38, 196.61, 0.35815, 7, 65.92, 201.68, 0.0359, 8, 36.31, 197.64, 0.18148, 9, 97.4, 188.87, 0.42446, 4, 6, 32.44, 237.57, 0.22039, 7, 107.38, 219.64, 0.0159, 8, 81.11, 203.42, 0.14797, 9, 130.83, 158.47, 0.61575, 4, 6, 52.49, 280.65, 0.1361, 7, 150.97, 238.53, 0.0026, 8, 128.23, 209.5, 0.09151, 9, 165.98, 126.51, 0.76978, 3, 6, 64.31, 319.65, 0.11225, 8, 167.52, 220.36, 0.06852, 9, 199.69, 103.6, 0.81923, 3, 6, -26.83, 377.03, 0.00253, 8, 167.3, 328.05, 0.00338, 9, 281.61, 173.52, 0.99409, 1, 9, 323.34, 207.19, 1, 1, 9, 61.8, -15.22, 1, 3, 7, 269.05, -24.85, 0.00926, 8, 168.78, -76.28, 0.40273, 9, -25.51, -89.47, 0.58801, 3, 7, 292.76, -107.07, 0.06667, 8, 168.8, -161.84, 0.63925, 9, -90.69, -144.9, 0.29408, 3, 7, 305.44, -151.01, 0.08842, 8, 168.81, -207.57, 0.66222, 9, -125.53, -174.53, 0.24935, 3, 7, 255.42, -186.29, 0.15096, 8, 110.98, -227.63, 0.66593, 9, -178.26, -143.45, 0.18312, 4, 6, 396.22, -38.05, 0.0002, 7, 196.27, -228.02, 0.29512, 8, 42.59, -251.34, 0.61397, 9, -240.63, -106.7, 0.0907, 4, 6, 363.11, -90.4, 0.00799, 7, 136.75, -245.17, 0.43661, 8, -19.35, -251.34, 0.5143, 9, -280.74, -59.51, 0.04109, 4, 6, 296.26, -170.8, 0.07196, 7, 33.37, -260.87, 0.64801, 8, -123.04, -237.81, 0.27691, 9, -337.59, 28.26, 0.00311, 4, 6, 257.26, -217.7, 0.11406, 7, -26.93, -270.03, 0.68665, 8, -183.52, -229.92, 0.19927, 9, -370.74, 79.45, 2e-05, 3, 6, 225.29, -212.22, 0.13651, 7, -47.2, -244.7, 0.68736, 8, -195.98, -199.97, 0.17612, 3, 6, 163.72, -201.68, 0.22872, 7, -86.23, -195.93, 0.66037, 8, -219.98, -142.29, 0.11091, 3, 6, 127.95, -175.26, 0.33215, 7, -95.42, -152.42, 0.59851, 8, -216.76, -97.94, 0.06934, 4, 3, -52.86, 231, 3e-05, 6, 81.21, -140.73, 0.58001, 7, -107.43, -95.56, 0.40172, 8, -212.56, -39.98, 0.01823, 3, 3, -56.29, 131.44, 0.10352, 6, 1.08, -81.54, 0.88274, 7, -128.02, 1.9, 0.01374, 2, 3, -32.99, 103.67, 0.31569, 6, -35, -85.02, 0.68431, 2, 3, 5.73, 57.51, 0.81616, 6, -94.97, -90.81, 0.18384, 3, 3, 47.05, 59.56, 0.96969, 4, -155.29, 29.76, 0.01496, 6, -116.67, -126.03, 0.01535, 2, 3, 93.85, 61.89, 0.78292, 4, -108.48, 32.09, 0.21708, 2, 3, 117.06, 74.71, 0.39229, 4, -85.27, 44.91, 0.60771, 1, 4, -52.58, 58.74, 1, 1, 4, -12.14, 75.84, 1, 1, 4, 54.78, 40.8, 1, 1, 4, 103.09, 15.5, 1, 2, 4, 162.26, -15.48, 0.04669, 5, 77.38, 60.87, 0.95331, 1, 5, 124.7, -13.89, 1, 1, 5, 65.05, 25.15, 1, 1, 5, 56.69, -34.36, 1, 1, 4, 109.4, -9.32, 1, 2, 4, 130.43, -100.41, 0.58179, 5, 7.32, 3.28, 0.41821, 1, 4, 55.05, 22.83, 1, 1, 4, 61.74, -68.51, 1, 3, 3, 289.45, -129.6, 0.01458, 4, 87.11, -159.4, 0.796, 5, -59.71, -26.11, 0.18942, 1, 4, -9.69, 42.56, 1, 1, 4, -2.06, -37.61, 1, 3, 3, 221.75, -125.89, 0.01964, 4, 19.41, -155.69, 0.9345, 5, -116.46, 10.99, 0.04587, 1, 4, -54.92, 24.55, 1, 1, 4, -53.07, -29.97, 1, 3, 3, 162.77, -124.2, 0.20827, 4, -39.57, -154, 0.65251, 5, -166.68, 41.97, 0.13922, 3, 3, 116.89, 28.82, 0.60395, 4, -85.44, -0.98, 0.39541, 5, -129.81, 197.4, 0.00064, 3, 3, 119.07, -2.12, 0.72463, 4, -83.26, -31.92, 0.23917, 5, -143.4, 169.53, 0.0362, 3, 3, 128.1, -121.59, 0.6615, 4, -74.23, -151.39, 0.10756, 5, -195.38, 61.58, 0.23094, 2, 3, 80.55, 12.51, 0.99092, 4, -121.79, -17.29, 0.00908, 3, 3, 80.07, -37, 0.95792, 4, -122.26, -66.8, 0.01699, 5, -194.62, 158.85, 0.02509, 2, 3, 36.98, -5.79, 0.99938, 5, -216.3, 207.44, 0.00062, 2, 3, 4.34, 5.54, 0.99487, 6, -137.03, -60.25, 0.00513, 2, 3, -29.4, 45.89, 0.64543, 6, -84.66, -55.28, 0.35457, 2, 3, -75.09, 73.22, 0.22913, 6, -36.26, -33.09, 0.77087, 3, 3, -106, 135.68, 0.01913, 6, 32.72, -42.97, 0.97045, 7, -78.74, 9.73, 0.01042, 5, 3, -170.04, 42.91, 0.00217, 6, -7.5, 62.34, 0.89363, 7, -38.87, 115.16, 0.01917, 8, -88.34, 143.52, 0.04062, 9, -24.56, 248.79, 0.0444, 3, 6, 88.72, -44.01, 0.73941, 7, -37.58, -28.24, 0.25991, 8, -126.8, 5.36, 0.00068, 4, 6, 33.6, 92.08, 0.67787, 7, 11.61, 110.1, 0.0962, 8, -41.24, 124.68, 0.12173, 9, -8.41, 200.7, 0.1042, 3, 6, 122.47, -34.7, 0.26602, 7, -6.16, -43.7, 0.71784, 8, -100.9, -18.19, 0.01614, 4, 6, 61.94, 117.81, 0.45647, 7, 49.88, 110.51, 0.13198, 8, -4.34, 114.48, 0.23323, 9, 7.71, 165.98, 0.17832, 3, 6, 176.33, -17.09, 0.01808, 7, 45.8, -66.31, 0.77768, 8, -57.23, -54.3, 0.20424, 4, 6, 94.83, 165.4, 0.22207, 7, 106.08, 124.25, 0.05749, 8, 53.46, 112.12, 0.32596, 9, 43.35, 120.41, 0.39449, 4, 6, 236.42, 1.56, 0.00172, 7, 103.11, -92.27, 0.40708, 8, -9.34, -95.12, 0.57596, 9, -155.22, 34.05, 0.01524, 4, 6, 150.74, 195.06, 0.05954, 7, 167.58, 109.29, 0.00825, 8, 108.41, 80.72, 0.24947, 9, 55.02, 58.21, 0.68274, 3, 7, 174.88, -117.35, 0.19563, 8, 52.67, -139.08, 0.68991, 9, -148.55, -41.68, 0.11446, 1, 9, 9.35, -10.49, 1, 3, 7, 228.43, -150.11, 0.14857, 8, 95.06, -185.39, 0.66901, 9, -156.39, -103.97, 0.18243, 3, 7, 255.38, -50.85, 0.02296, 8, 148.44, -97.47, 0.52146, 9, -54.83, -87.7, 0.45558, 3, 3, 279.26, -93.12, 0.00299, 4, 76.93, -122.92, 0.95817, 5, -50.27, 10.57, 0.03884, 1, 4, 11.41, -111.7, 1, 3, 3, 158.61, -85.99, 0.11471, 4, -43.73, -115.79, 0.80861, 5, -151.15, 77.14, 0.07668, 3, 3, 124.56, -74.69, 0.68628, 4, -77.78, -104.49, 0.15922, 5, -174.98, 103.96, 0.1545], "hull": 44, "edges": [0, 86, 6, 8, 20, 22, 30, 32, 32, 34, 34, 36, 48, 50, 72, 74, 84, 86, 84, 88, 88, 90, 0, 2, 90, 2, 82, 84, 82, 92, 92, 94, 2, 4, 4, 6, 94, 4, 78, 80, 80, 82, 80, 96, 96, 98, 100, 6, 78, 102, 102, 104, 106, 8, 74, 76, 76, 78, 76, 108, 108, 110, 8, 10, 112, 10, 74, 114, 114, 116, 10, 12, 12, 14, 118, 12, 72, 120, 120, 122, 122, 14, 68, 70, 70, 72, 70, 124, 14, 16, 124, 16, 68, 126, 16, 18, 18, 20, 126, 18, 64, 66, 66, 68, 66, 128, 128, 20, 64, 130, 130, 22, 62, 64, 62, 132, 132, 134, 22, 24, 134, 24, 58, 60, 60, 62, 60, 136, 136, 138, 24, 26, 138, 26, 58, 140, 140, 142, 26, 28, 28, 30, 142, 28, 54, 56, 56, 58, 56, 144, 144, 146, 146, 30, 50, 52, 52, 54, 52, 148, 148, 150, 150, 32, 50, 152, 152, 154, 36, 38, 154, 38, 48, 156, 156, 158, 38, 40, 158, 40, 44, 46, 46, 48, 40, 42, 42, 44, 46, 42, 98, 160, 160, 100, 104, 162, 162, 106, 110, 164, 164, 112, 116, 166, 166, 118], "width": 633, "height": 1057}}, "s3": {"s3": {"type": "mesh", "uvs": [0.13377, 0.54448, 0.25692, 0.46066, 0.46869, 0.31653, 0.71152, 0.15126, 0.91682, 0.01153, 1, 0.5058, 1, 0.72941, 0.81413, 0.80658, 0.61773, 0.88812, 0.40577, 0.97611, 0.20756, 0.95691, 0.05463, 0.9421, 0.04601, 0.78018, 0.03697, 0.61036, 0.75883, 0.44713, 0.57357, 0.6536, 0.36052, 0.81518, 0.19687, 0.85408], "triangles": [2, 3, 14, 5, 3, 4, 1, 2, 15, 12, 13, 0, 0, 1, 16, 17, 12, 0, 16, 1, 15, 16, 17, 0, 16, 15, 8, 11, 12, 17, 10, 17, 16, 11, 17, 10, 9, 16, 8, 10, 16, 9, 5, 14, 3, 15, 2, 14, 14, 5, 6, 7, 15, 14, 6, 7, 14, 8, 15, 7], "vertices": [1, 46, 68.08, -2.14, 1, 1, 46, 54.1, -4.28, 1, 1, 46, 30.05, -7.95, 1, 1, 46, 2.47, -12.15, 1, 1, 46, -20.84, -15.71, 1, 1, 44, -5.26, 6.95, 1, 2, 44, 2.96, 27.03, 0.99642, 45, -24.57, 40.65, 0.00358, 2, 44, 21.96, 27.34, 0.86828, 45, -7.1, 33.18, 0.13172, 2, 44, 42.04, 27.67, 0.37297, 45, 11.37, 25.29, 0.62703, 2, 44, 63.72, 28.03, 0.02545, 45, 31.3, 16.77, 0.97455, 2, 45, 42.82, 2.01, 0.94253, 46, 79.66, 36.78, 0.05747, 2, 45, 51.71, -9.38, 0.80975, 46, 91.89, 29.1, 0.19025, 2, 45, 40.91, -20.81, 0.5128, 46, 85.63, 14.67, 0.4872, 2, 45, 29.58, -32.8, 0.01454, 46, 79.07, -0.46, 0.98546, 2, 44, 13.57, -6.9, 0.75944, 46, 11.25, 15.53, 0.24056, 2, 44, 37.27, 5.04, 0.58263, 45, -2.22, 6.58, 0.41737, 2, 44, 61.74, 11.97, 0.00483, 45, 22.95, 2.92, 0.99517, 2, 45, 36.3, -5.61, 0.86676, 46, 76.12, 27.4, 0.13324], "hull": 14, "edges": [8, 10, 10, 12, 8, 6, 6, 28, 12, 14, 28, 14, 6, 4, 4, 30, 14, 16, 16, 18, 30, 16, 4, 2, 2, 32, 32, 18, 2, 0, 0, 26, 0, 34, 18, 20, 20, 22, 34, 20, 22, 24, 24, 26], "width": 94, "height": 97}}, "s4": {"s4": {"type": "mesh", "uvs": [0.66619, 0.0293, 0.74084, 0.19311, 0.81268, 0.35074, 0.88383, 0.50686, 0.93619, 0.62176, 1, 0.76177, 1, 0.8549, 0.8528, 1, 0.78481, 1, 0.60903, 0.90909, 0.46457, 0.83437, 0.23416, 0.71521, 0, 0.59411, 0, 0.4232, 0.20035, 0.21867, 0.38905, 0.02603], "triangles": [3, 12, 13, 11, 12, 3, 4, 11, 3, 10, 11, 4, 5, 10, 4, 9, 10, 5, 6, 9, 5, 7, 8, 9, 6, 7, 9, 1, 14, 15, 1, 15, 0, 2, 13, 14, 2, 14, 1, 3, 13, 2], "vertices": [1, 52, 31.27, -3.9, 1, 1, 52, 20.01, -6.39, 1, 2, 52, 9.17, -8.78, 0.98599, 51, 34.99, -11.95, 0.01401, 2, 52, -1.57, -11.16, 0.4627, 51, 24.02, -11.13, 0.5373, 2, 52, -9.47, -12.9, 0.06654, 51, 15.95, -10.53, 0.93346, 1, 51, 6.12, -9.8, 1, 1, 51, -0.06, -8.05, 1, 1, 51, -8.45, -0.93, 1, 1, 51, -7.87, 1.1, 1, 1, 51, -0.35, 4.63, 1, 1, 51, 5.83, 7.54, 1, 2, 52, -16.26, 8.76, 0.01291, 51, 15.69, 12.16, 0.98709, 2, 52, -8.01, 16.15, 0.25135, 51, 25.71, 16.87, 0.74865, 2, 52, 3.78, 16.33, 0.73025, 51, 37.05, 13.65, 0.26975, 2, 52, 17.98, 10.34, 0.99932, 51, 48.94, 3.83, 0.00068, 1, 52, 31.37, 4.7, 1], "hull": 16, "edges": [0, 30, 10, 12, 12, 14, 14, 16, 24, 26, 26, 28, 28, 30, 0, 2, 28, 2, 2, 4, 26, 4, 4, 6, 24, 6, 22, 24, 6, 8, 8, 10, 22, 8, 20, 22, 20, 10, 16, 18, 18, 20, 18, 12], "width": 31, "height": 69}}, "s5": {"s5": {"type": "mesh", "uvs": [0.96675, 0.04345, 0.95428, 0.2201, 0.94141, 0.40226, 0.9264, 0.61466, 0.91343, 0.7984, 0.89919, 1, 0.85009, 1, 0, 0.9287, 0, 0.79255, 0, 0.61975, 0.15859, 0.39746, 0.28379, 0.22198, 0.4334, 0.01229], "triangles": [1, 12, 0, 11, 12, 1, 2, 11, 1, 10, 11, 2, 3, 10, 2, 9, 10, 3, 4, 8, 9, 3, 4, 9, 7, 8, 4, 6, 7, 4, 5, 6, 4], "vertices": [1, 50, 29.34, -5.74, 1, 1, 50, 18.15, -7.36, 1, 2, 49, 28.02, -10.05, 0.13469, 50, 6.6, -9.04, 0.86531, 2, 49, 14.46, -9.04, 0.97263, 50, -6.85, -10.99, 0.02737, 1, 49, 2.73, -8.16, 1, 1, 49, -10.14, -7.2, 1, 1, 49, -10.09, -6.27, 1, 1, 49, -4.67, 9.62, 1, 1, 49, 4.03, 9.15, 1, 2, 49, 15.08, 8.56, 0.97886, 50, -10.07, 6.32, 0.02114, 2, 49, 29.12, 4.79, 0.00135, 50, 4.46, 5.68, 0.99865, 1, 50, 15.93, 5.18, 1, 1, 50, 29.64, 4.58, 1], "hull": 13, "edges": [0, 24, 10, 12, 12, 14, 22, 24, 0, 2, 22, 2, 18, 20, 20, 22, 2, 4, 20, 4, 4, 6, 18, 6, 14, 16, 16, 18, 6, 8, 8, 10], "width": 19, "height": 64}}, "s6": {"s6": {"type": "mesh", "uvs": [0.97923, 0, 1, 0.04804, 1, 0.05089, 0.24607, 0.80559, 0.08395, 1, 0.02445, 1, 0.00432, 0.93821, 0.69864, 0.24507, 0.96517, 0], "triangles": [8, 0, 1, 7, 2, 3, 8, 2, 7, 8, 1, 2, 6, 7, 3, 4, 5, 6, 3, 4, 6], "vertices": [-60.1, 0.55, -58.76, 7.82, -58.51, 8.12, 105.79, 5.68, 143.86, 8.52, 151.75, 2.09, 149.15, -6.55, -2, -4.1, -58.24, -0.97], "hull": 9, "edges": [0, 16, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 171, "height": 135}}, "t1": {"t1": {"type": "mesh", "uvs": [0.35788, 0.0034, 0.63281, 0, 0.70273, 0.05514, 0.76821, 0.10679, 0.84922, 0.17067, 1, 0.28959, 1, 0.37527, 0.90004, 0.48656, 0.76819, 0.63336, 0.72139, 0.68546, 0.6673, 0.74568, 0.55844, 0.86688, 0.40885, 0.82583, 0.32708, 0.8034, 0.29796, 0.78835, 0.24581, 0.74035, 0.195, 0.6936, 0.10325, 0.60916, 0.03563, 0.54693, 0.0031, 0.517, 0.00348, 0.38371, 0.00385, 0.2548, 0.00408, 0.17347, 0.14075, 0.07874, 0.24748, 0.00476, 0.19201, 0.14613, 0.15962, 0.29314, 0.17986, 0.37943, 0.3877, 0.34321, 0.66977, 0.44334, 0.75615, 0.40393, 0.88301, 0.4231, 0.06919, 0.41458, 0.05839, 0.29846, 0.11778, 0.20685, 0.17725, 0.21311, 0.13802, 0.12695, 0.2192, 0.07683, 0.30807, 0.2452, 0.55101, 0.29846, 0.76965, 0.31764, 0.34451, 0.15785, 0.5591, 0.21963, 0.7629, 0.21324, 0.36881, 0.07582, 0.62119, 0.15252, 0.59015, 0.05345, 0.15786, 0.56371, 0.23659, 0.551, 0.31533, 0.57855, 0.28043, 0.6188, 0.20886, 0.63787, 0.17038, 0.57713, 0.25091, 0.57501, 0.28849, 0.58773, 0.24733, 0.61456, 0.1847, 0.61456, 0.1847, 0.51287, 0.34128, 0.48038, 0.48891, 0.44154, 0.37888, 0.56559, 0.40678, 0.51527, 0.50481, 0.49073, 0.614, 0.46431, 0.56538, 0.53225, 0.51119, 0.57188, 0.4267, 0.58069, 0.42309, 0.5346, 0.48829, 0.51702, 0.56462, 0.49025, 0.53123, 0.53502, 0.48193, 0.55928, 0.69633, 0.50377, 0.81521, 0.46442, 0.3033, 0.6708, 0.43819, 0.63623, 0.69397, 0.5664, 0.78944, 0.53114, 0.38639, 0.72909, 0.49326, 0.70973, 0.67282, 0.66203, 0.43106, 0.74914, 0.64217, 0.70628, 0.47924, 0.80169, 0.57559, 0.75675], "triangles": [7, 31, 6, 73, 30, 31, 40, 30, 29, 39, 29, 28, 5, 6, 31, 31, 30, 40, 40, 29, 39, 27, 38, 28, 31, 40, 5, 28, 38, 39, 40, 39, 43, 39, 42, 43, 40, 4, 5, 40, 43, 4, 39, 38, 42, 35, 25, 38, 38, 41, 42, 38, 25, 41, 42, 45, 43, 45, 42, 44, 43, 3, 4, 43, 45, 3, 25, 37, 41, 42, 41, 44, 41, 37, 44, 44, 46, 45, 45, 2, 3, 45, 46, 2, 25, 36, 37, 37, 36, 23, 37, 23, 24, 44, 24, 0, 44, 37, 24, 44, 0, 46, 46, 1, 2, 46, 0, 1, 19, 20, 32, 20, 33, 32, 27, 33, 26, 27, 32, 33, 20, 21, 33, 27, 26, 38, 26, 33, 34, 33, 21, 34, 26, 35, 38, 26, 34, 35, 21, 22, 34, 35, 34, 25, 34, 36, 25, 34, 22, 36, 36, 22, 23, 82, 80, 9, 11, 83, 84, 80, 75, 76, 12, 81, 83, 82, 79, 80, 64, 63, 72, 84, 81, 82, 12, 13, 81, 76, 65, 64, 81, 78, 79, 78, 75, 79, 65, 70, 64, 70, 68, 69, 68, 62, 69, 65, 71, 70, 71, 68, 70, 58, 28, 59, 58, 27, 28, 32, 27, 57, 18, 19, 32, 13, 14, 81, 14, 78, 81, 14, 15, 78, 15, 74, 78, 15, 16, 74, 78, 74, 75, 74, 51, 50, 74, 16, 51, 16, 17, 51, 74, 50, 75, 17, 56, 51, 51, 55, 50, 51, 56, 55, 50, 66, 75, 50, 60, 66, 50, 49, 60, 50, 54, 49, 50, 55, 54, 55, 53, 54, 55, 56, 53, 17, 52, 56, 56, 52, 53, 17, 47, 52, 17, 18, 47, 54, 53, 49, 66, 60, 71, 61, 60, 49, 52, 48, 53, 52, 47, 48, 49, 53, 48, 60, 67, 71, 60, 61, 67, 61, 49, 48, 47, 57, 48, 47, 18, 57, 71, 67, 68, 48, 58, 61, 48, 57, 58, 57, 18, 32, 67, 61, 68, 68, 61, 62, 62, 61, 59, 57, 27, 58, 61, 58, 59, 75, 66, 65, 66, 71, 65, 63, 62, 59, 64, 70, 69, 76, 75, 65, 80, 79, 75, 69, 62, 63, 64, 69, 63, 81, 79, 82, 83, 81, 84, 63, 59, 29, 29, 59, 28, 84, 82, 10, 11, 84, 10, 12, 83, 11, 10, 82, 9, 9, 80, 8, 80, 76, 8, 8, 77, 7, 8, 76, 77, 76, 72, 77, 76, 64, 72, 77, 73, 7, 77, 72, 73, 63, 29, 72, 72, 30, 73, 72, 29, 30, 73, 31, 7], "vertices": [2, 12, -89.36, -0.5, 0.00058, 13, 25.55, 65.85, 0.99942, 1, 13, 98.05, 75.55, 1, 1, 13, 118.69, 59.27, 1, 1, 13, 138.03, 44.03, 1, 2, 10, 208.44, 25.85, 0.00708, 13, 161.95, 25.17, 0.99292, 2, 10, 193.54, -28.86, 0.1553, 13, 206.48, -9.93, 0.8447, 2, 10, 168.47, -43.18, 0.24493, 13, 209.86, -38.6, 0.75507, 2, 10, 122.72, -38.71, 0.54476, 13, 187.84, -78.96, 0.45524, 2, 10, 62.37, -32.8, 0.98253, 13, 158.81, -132.2, 0.01747, 1, 10, 40.94, -30.7, 1, 1, 10, 16.19, -28.28, 1, 1, 10, -33.64, -23.4, 1, 2, 10, -41.38, 18.01, 0.80595, 11, -47.64, -54.95, 0.19405, 2, 10, -45.6, 40.65, 0.63051, 11, -51.87, -32.31, 0.36949, 2, 10, -45.04, 49.89, 0.67429, 11, -51.31, -23.07, 0.32571, 3, 10, -37.88, 69.96, 0.37869, 11, -44.15, -3, 0.61875, 12, 120.25, 136, 0.00256, 2, 10, -30.91, 89.51, 0.11894, 11, -37.17, 16.55, 0.88106, 1, 11, -24.58, 51.86, 1, 3, 11, -15.29, 77.88, 0.85239, 12, 106.13, 51.3, 0.14747, 13, -38.13, -126.1, 0.00014, 2, 11, -10.83, 90.4, 0.68216, 12, 103.95, 38.19, 0.31784, 3, 11, 28.22, 112.6, 0.17986, 12, 69.43, 9.44, 0.81822, 13, -53.06, -72.47, 0.00192, 1, 12, 36.06, -18.36, 1, 2, 12, 15, -35.9, 0.99823, 13, -61.2, -2.09, 0.00177, 2, 12, -32.82, -28.51, 0.39241, 13, -28.83, 33.87, 0.60759, 2, 12, -70.16, -22.73, 0.03804, 13, -3.56, 61.95, 0.96196, 2, 12, -24.15, -3.48, 0.3156, 13, -12.63, 12.91, 0.6844, 4, 10, 81.6, 164.64, 0.00025, 11, 75.33, 91.68, 0.05254, 12, 19.37, 21.71, 0.76787, 13, -15.39, -37.3, 0.17934, 4, 10, 59.02, 145.53, 0.0026, 11, 52.75, 72.58, 0.25328, 12, 38.22, 44.5, 0.56977, 13, -6.63, -65.55, 0.17435, 4, 10, 97.05, 103.59, 0.06287, 11, 90.78, 30.63, 0.17563, 12, -6.62, 79.07, 0.09391, 13, 46.84, -46.95, 0.66759, 4, 10, 104.97, 21.7, 0.54537, 11, 98.71, -51.26, 0.04039, 12, -28.89, 158.26, 0.00013, 13, 125.31, -71.68, 0.41411, 3, 10, 127.91, 8.34, 0.41091, 11, 121.64, -64.62, 0.00784, 13, 146.57, -55.79, 0.58124, 3, 10, 139.04, -24.16, 0.40527, 11, 132.77, -97.12, 0, 13, 180.84, -58.26, 0.59473, 3, 11, 27.86, 92.26, 0.2882, 12, 66.2, 29.52, 0.69146, 13, -34.48, -80.76, 0.02034, 3, 11, 60.41, 114.17, 0.00402, 12, 38.03, 2.21, 0.99379, 13, -41.92, -42.23, 0.0022, 1, 12, 4.21, -5.49, 1, 3, 11, 101.07, 100.99, 0.00248, 12, -4.32, 8, 0.66349, 13, -13.89, -9.97, 0.33403, 2, 12, -19.89, -18.64, 0.59446, 13, -27.65, 17.65, 0.40554, 2, 12, -46.7, -12.92, 0.13392, 13, -8.18, 36.95, 0.86608, 4, 10, 115.22, 138.37, 0.00311, 11, 108.95, 65.41, 0.02875, 12, -18.36, 41.63, 0.07183, 13, 21.94, -16.63, 0.89631, 4, 10, 131.69, 73.35, 0.07672, 11, 125.43, 0.4, 0.03797, 12, -46.06, 102.7, 0.00282, 13, 88.22, -26.89, 0.88249, 3, 10, 154.94, 19.65, 0.1515, 11, 148.67, -53.31, 0.0022, 13, 146.73, -26.49, 0.8463, 1, 13, 28.12, 13.74, 1, 4, 10, 155.83, 84.66, 8e-05, 11, 149.56, 11.71, 5e-05, 12, -67.82, 87.31, 0, 13, 87.24, -0.25, 0.99987, 2, 10, 184.59, 38.67, 0.00087, 13, 140.83, 8.24, 0.99913, 2, 12, -72.5, 17.39, 1e-05, 13, 31.3, 41.95, 0.99999, 1, 13, 101, 24.14, 1, 1, 13, 88.89, 56.33, 1, 1, 11, -4.07, 46.85, 1, 1, 11, 10.04, 30.79, 1, 1, 11, 12.37, 8, 1, 1, 11, -4.01, 9.33, 1, 1, 11, -19.04, 22.67, 1, 1, 11, -6.35, 41.71, 1, 1, 11, 4.9, 23.47, 1, 1, 11, 6.14, 12.66, 1, 1, 11, -7.14, 17.68, 1, 1, 11, -15.41, 32.14, 1, 1, 11, 14.35, 49.15, 1, 1, 11, 44.52, 18.42, 1, 1, 11, 75.37, -9.18, 1, 1, 11, 24.55, -4.52, 1, 1, 11, 42.95, -2.54, 1, 1, 11, 63.07, -21.08, 1, 4, 10, 91.48, 31.08, 0.31589, 11, 85.21, -41.88, 0.50245, 12, -13.95, 151.42, 0.00091, 13, 111.4, -80.43, 0.18074, 1, 11, 58.92, -42.01, 1, 1, 11, 40.17, -36.12, 1, 1, 11, 26.44, -18.08, 1, 1, 11, 39.45, -9.54, 1, 1, 11, 53.2, -21.66, 1, 1, 11, 71.11, -34.81, 1, 1, 11, 53.6, -34.59, 1, 1, 11, 39.99, -27.26, 1, 3, 10, 90.8, 5.46, 0.77063, 11, 84.53, -67.49, 0.01813, 13, 134.71, -91.07, 0.21125, 3, 10, 118, -15.41, 0.57763, 11, 111.73, -88.37, 0.0009, 13, 164.56, -74.2, 0.42147, 1, 11, -16.21, -4.65, 1, 1, 11, 11.71, -30.02, 1, 3, 10, 72.16, -4.46, 0.80636, 11, 65.89, -77.42, 0.15829, 13, 136.56, -112.11, 0.03535, 3, 10, 95.08, -20.62, 0.79495, 11, 88.81, -93.58, 0.00022, 13, 160.39, -97.33, 0.20483, 1, 11, -22.3, -33.59, 1, 1, 11, -2.53, -55.03, 1, 2, 10, 41.39, -15.57, 0.3441, 11, 35.12, -88.53, 0.6559, 1, 11, -22.27, -47.26, 1, 2, 10, 24.4, -15.89, 0.07249, 11, 18.13, -88.84, 0.92751, 2, 10, -25.02, 5.79, 0.17435, 11, -31.29, -67.17, 0.82565, 1, 11, -5.42, -81.91, 1], "hull": 25, "edges": [10, 12, 26, 28, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 12, 54, 64, 64, 38, 38, 40, 40, 66, 66, 52, 40, 42, 42, 44, 42, 68, 50, 70, 70, 52, 68, 70, 44, 72, 72, 50, 44, 46, 46, 48, 48, 74, 74, 50, 46, 74, 52, 76, 76, 78, 78, 80, 80, 10, 50, 82, 82, 84, 84, 86, 8, 10, 86, 8, 74, 88, 88, 90, 6, 8, 90, 6, 2, 0, 0, 48, 0, 92, 2, 4, 4, 6, 92, 4, 34, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 34, 34, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 34, 34, 36, 36, 38, 36, 114, 114, 116, 116, 118, 118, 58, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 120, 126, 58, 120, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 120, 96, 122, 100, 132, 128, 144, 144, 146, 146, 62, 32, 34, 32, 148, 148, 150, 150, 152, 152, 154, 12, 14, 154, 14, 28, 30, 30, 32, 30, 156, 156, 158, 158, 160, 14, 16, 160, 16, 28, 162, 162, 164, 16, 18, 164, 18, 22, 24, 24, 26, 24, 166, 166, 168, 18, 20, 20, 22, 168, 20], "width": 266, "height": 337}}, "t2": {"t2": {"type": "mesh", "uvs": [0.8557, 0.12554, 0.97013, 0.23489, 0.98645, 0.60749, 1, 0.91687, 1, 1, 0.86123, 1, 0.69567, 0.74374, 0.54105, 0.50439, 0.34956, 0.41152, 0, 0.45502, 0, 0.33197, 0.04701, 0.14077, 0.08162, 0, 0.20658, 0, 0.30468, 0.0041, 0.52354, 0.01324, 0.748, 0.02261, 0.15873, 0.24815, 0.39886, 0.23712, 0.65338, 0.34474, 0.80941, 0.60137, 0.19524, 0.09362, 0.48075, 0.13364, 0.77178, 0.30886, 0.88908, 0.51307, 0.60026, 0.07982, 0.87027, 0.29093, 0.94109, 0.43856], "triangles": [27, 1, 2, 23, 26, 24, 24, 26, 27, 27, 26, 1, 23, 25, 26, 26, 25, 0, 25, 16, 0, 26, 0, 1, 25, 15, 16, 5, 3, 4, 6, 20, 5, 3, 20, 24, 3, 5, 20, 24, 2, 3, 6, 7, 20, 24, 27, 2, 7, 19, 20, 19, 23, 20, 20, 23, 24, 7, 18, 19, 19, 22, 23, 22, 25, 23, 8, 18, 7, 9, 17, 8, 9, 10, 17, 8, 17, 18, 18, 22, 19, 10, 11, 17, 17, 21, 18, 17, 11, 21, 18, 21, 22, 11, 12, 21, 22, 21, 14, 21, 13, 14, 22, 14, 25, 21, 12, 13, 14, 15, 25], "vertices": [1, 21, 27.27, 12.28, 1, 2, 20, 87.94, -3.56, 0.06218, 21, 49.82, -5, 0.93782, 2, 20, 85.44, -62.46, 0.70384, 21, 53.03, -63.87, 0.29616, 2, 20, 83.37, -111.37, 0.87121, 21, 55.7, -112.75, 0.12879, 2, 20, 82.1, -124.44, 0.87671, 21, 55.7, -125.88, 0.12329, 2, 20, 54.89, -121.8, 0.87891, 21, 28.36, -125.88, 0.12109, 2, 20, 26.34, -78.34, 0.89617, 21, -4.25, -85.39, 0.10383, 3, 19, 46.27, -39.02, 0.17987, 20, -0.32, -37.76, 0.81801, 21, -34.71, -47.58, 0.00212, 2, 19, 12.99, -15.98, 0.97569, 20, -36.44, -19.5, 0.02431, 1, 19, -55.58, -6.65, 1, 1, 19, -51.06, 12.26, 1, 1, 19, -35.03, 39.49, 1, 1, 19, -23.22, 59.54, 1, 1, 19, 0.72, 53.81, 1, 3, 19, 19.36, 48.69, 0.992, 20, -39.01, 45.42, 0.00297, 21, -81.28, 31.47, 0.00504, 3, 19, 60.96, 37.25, 0.46219, 20, 3.76, 39.81, 0.31352, 21, -38.16, 30.03, 0.22429, 3, 19, 103.62, 25.53, 0.00404, 20, 47.63, 34.06, 0.00981, 21, 6.06, 28.54, 0.98615, 1, 19, -17.57, 17.87, 1, 2, 19, 28.85, 8.56, 0.99754, 21, -62.72, -5.35, 0.00246, 3, 19, 73.66, -19.64, 0.00241, 20, 24.15, -14.79, 0.98717, 21, -12.58, -22.35, 0.01042, 2, 20, 50.82, -58.12, 0.79907, 21, 18.15, -62.9, 0.20093, 1, 19, -4.89, 39.94, 1, 3, 19, 48.34, 20.71, 0.66503, 20, -6.47, 21.7, 0.25902, 21, -46.59, 11, 0.07595, 2, 20, 47.91, -11.41, 0.48057, 21, 10.74, -16.68, 0.51943, 2, 20, 67.79, -45.76, 0.61611, 21, 33.85, -48.95, 0.38389, 3, 19, 73.21, 23.51, 0.15312, 20, 17.78, 27.88, 0.39661, 21, -23.05, 19.5, 0.45027, 2, 20, 67.5, -10.46, 0.15523, 21, 30.14, -13.85, 0.84477, 2, 20, 79.13, -35.03, 0.44123, 21, 44.1, -37.18, 0.55877], "hull": 17, "edges": [6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 24, 26, 20, 34, 34, 36, 36, 38, 38, 40, 40, 10, 10, 12, 12, 14, 20, 22, 22, 24, 22, 42, 42, 44, 44, 46, 46, 48, 48, 6, 26, 28, 28, 50, 50, 52, 52, 54, 2, 4, 4, 6, 54, 4, 28, 30, 30, 32, 2, 0, 0, 32], "width": 197, "height": 158}}, "t3": {"t3": {"type": "mesh", "uvs": [0.63128, 0, 1, 0.07418, 1, 0.17093, 0.72346, 0.2342, 0.38555, 0.31151, 0.33795, 0.43938, 0.27471, 0.60927, 0.52674, 0.72646, 0.7536, 0.83194, 1, 0.94652, 1, 1, 0.88833, 1, 0.52816, 0.95624, 0.2335, 0.92044, 0.16328, 0.77745, 0.05318, 0.55329, 0, 0.44499, 0, 0.35103, 0, 0.21996, 0.09721, 0.10127, 0.18016, 0], "triangles": [11, 9, 10, 9, 11, 8, 11, 12, 8, 8, 12, 7, 12, 13, 7, 13, 14, 7, 14, 6, 7, 14, 15, 6, 6, 15, 5, 15, 16, 5, 16, 17, 5, 5, 17, 4, 17, 18, 4, 18, 19, 4, 3, 4, 19, 3, 19, 0, 0, 19, 20, 2, 3, 1, 3, 0, 1], "vertices": [1, 56, 5.34, -12.84, 1, 1, 56, -10.3, -4.46, 1, 1, 56, -9.6, 4.9, 1, 2, 56, 2.99, 10.11, 0.91565, 57, -1.47, 20.6, 0.08435, 2, 56, 18.37, 16.48, 0.08312, 57, 7.82, 6.77, 0.91688, 2, 57, 20.39, 6.23, 0.9397, 58, -6.99, 7.28, 0.0603, 1, 58, 8.58, 1.2, 1, 2, 58, 21.96, 9.75, 0.11746, 59, 3.07, 8.06, 0.88254, 2, 59, 17.29, 9.47, 0.51638, 60, 2.65, 9.53, 0.48362, 1, 60, 15.55, 0.89, 1, 1, 60, 16.62, -4.19, 1, 1, 60, 11.81, -5.2, 1, 2, 59, 20.51, -5.81, 0.45283, 60, -4.57, -4.31, 0.54717, 2, 59, 9.7, -13.77, 0.99973, 60, -17.98, -3.59, 0.00027, 2, 58, 23.56, -6.91, 0.33038, 59, -3.06, -7.51, 0.66962, 2, 57, 32.9, -4.83, 0.16204, 58, 1.28, -7.24, 0.83796, 2, 57, 22.77, -8.45, 0.98383, 58, -9.48, -7.4, 0.01617, 1, 57, 13.73, -9.58, 1, 2, 56, 34.63, 6.37, 0.15867, 57, 1.11, -11.16, 0.84133, 2, 56, 29.51, -4.8, 0.93715, 57, -10.84, -8.35, 0.06285, 1, 56, 25.14, -14.32, 1], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 18, 20, 20, 22, 4, 6, 6, 8, 36, 38, 38, 40, 8, 10, 10, 12, 32, 34, 34, 36, 30, 32, 26, 28, 28, 30, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26], "width": 44, "height": 97}}}}], "animations": {"animation1": {"bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone4": {"rotate": [{"angle": -0.38, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.38}]}, "t1": {"rotate": [{"angle": -0.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.22}]}, "s2": {"rotate": [{"angle": 2.02, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 1.33, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.4, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.5, "angle": 2.27, "curve": 0.312, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 2.6667, "angle": 2.02}]}, "s3": {"rotate": [{"angle": -4.89, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -1.99, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.8333, "angle": -0.38, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.89}]}, "s1": {"rotate": [{"angle": 1.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.86}]}, "s12": {"rotate": [{"angle": -0.3, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.8, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.3}]}, "s10": {"rotate": [{"angle": 2.33, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.94, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.33}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.67, "y": 11.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.54, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.67, "y": 11.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s11": {"rotate": [{"angle": 0.91, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 20.4, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.3333, "angle": 5.24, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.91}]}, "t3": {"scale": [{"y": 0.996, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "y": 0.94, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "y": 0.996}], "shear": [{"y": 0.23, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 3.6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "y": 0.23}]}, "t11": {"shear": [{"x": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -0.44}]}, "t12": {"shear": [{"x": -1.19, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "x": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -1.19}]}, "t13": {"shear": [{"x": -1.96, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": -1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -2.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -1.96}]}, "t4": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t2": {"rotate": [{"angle": 0.08, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.2, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.08}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.54, "y": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t32": {"rotate": [{"angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.42, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -5.01}]}, "t33": {"rotate": [{"angle": -4.35, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 1.42, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": -4.35}]}, "t34": {"rotate": [{"angle": -3.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 1.42, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -3.05}]}, "t35": {"rotate": [{"angle": -1.58, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 1.42, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": -1.58}]}, "t36": {"rotate": [{"angle": -0.14, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 1.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -0.14}]}, "t37": {"rotate": [{"angle": 1.02, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.42, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": 1.02}]}, "t15": {"rotate": [{"angle": -1.5, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -3.47, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.41, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.5}]}, "t16": {"rotate": [{"angle": 0.03, "curve": 0.342, "c2": 0.36, "c3": 0.697, "c4": 0.76}, {"time": 0.5, "angle": -2.71, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7667, "angle": -3.47, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 2.41, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "angle": 0.03}]}, "t17": {"rotate": [{"angle": 1.5, "curve": 0.308, "c2": 0.25, "c3": 0.662, "c4": 0.65}, {"time": 0.5, "angle": -1.31, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.0333, "angle": -3.47, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 2.41, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "angle": 1.5}]}, "t18": {"rotate": [{"angle": 2.39, "curve": 0.267, "c2": 0.05, "c3": 0.624, "c4": 0.49}, {"time": 0.5, "angle": 0.25, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.3, "angle": -3.47, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 2.41, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.6667, "angle": 2.39}]}, "t19": {"rotate": [{"angle": 1.81, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": 1.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.5667, "angle": -3.47, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": 1.81}]}, "t20": {"rotate": [{"angle": 0.44, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.47, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 0.44}]}, "t29": {"rotate": [{"angle": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.74, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.27}], "scale": [{"x": 1.16}]}, "t30": {"rotate": [{"angle": 1.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.21}]}, "t31": {"rotate": [{"angle": 3.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 3.74}]}, "s4": {"rotate": [{"angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.55}], "scale": [{"x": 1.08}]}, "s5": {"rotate": [{"angle": 0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -2.45, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": 0.16}]}, "s6": {"rotate": [{"angle": -0.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -2.45, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.56}]}, "s7": {"rotate": [{"angle": -1.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.45, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -1.35}]}, "s8": {"rotate": [{"angle": -2.06, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -2.45, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": -2.06}]}, "bone3": {"translate": [{"x": 0.73, "y": 0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": 5.96, "y": 4.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.73, "y": 0.74}], "scale": [{"y": 1.004, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "y": 1.004}]}, "s16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.49, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -16.32, "curve": "stepped"}, {"time": 1.3333, "angle": -16.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s17": {"rotate": [{"angle": -5.48, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -16.32, "curve": "stepped"}, {"time": 1.8333, "angle": -16.32, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -5.48}]}, "t22": {"rotate": [{"angle": 1.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.75}]}, "t23": {"rotate": [{"angle": 2.31, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.6667, "angle": 0.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 3.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 2.31}]}, "t24": {"rotate": [{"angle": 2.85, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 0.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.85}]}, "t25": {"rotate": [{"angle": 3.29, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.6667, "angle": 1.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 3.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": 3.29}]}, "t26": {"rotate": [{"angle": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 1.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.5}]}, "t27": {"rotate": [{"angle": 3.28, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": 3.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.6667, "angle": 2.33, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 3.28}]}, "t28": {"rotate": [{"angle": 2.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 3.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": 2.85, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.85}]}, "t6": {"rotate": [{"angle": -3.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -5.6}, {"time": 1.6667, "angle": 5.46, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -3.56}]}, "t9": {"rotate": [{"angle": -0.11, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -3.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "angle": -5.6, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.11}]}, "t10": {"rotate": [{"angle": 3.43, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -0.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": -5.6, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 3.43}]}, "t7": {"rotate": [{"angle": -2.31, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 8.76, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "angle": -2.31}]}, "t8": {"rotate": [{"angle": 5.06, "curve": 0.32, "c2": 0.29, "c3": 0.669, "c4": 0.67}, {"time": 0.4333, "angle": -1.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.9333, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 5.06}]}, "st": {"scale": [{"x": 1.02}]}, "st2": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st3": {"shear": [{"x": 0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.44}]}, "st4": {"shear": [{"x": 1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.2}]}, "st5": {"shear": [{"x": 1.96, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 2.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.96}]}, "s18": {"rotate": [{"angle": 1.61, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.6, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 1.61}]}}, "deform": {"default": {"t6": {"t6": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 154, "vertices": [-1.68906, -3.48419, -1.74522, -3.45645, -1.74522, -3.45645, -2.83593, -5.61667, -3.39424, -5.29784, -3.39424, -5.29784, -1.53346, -6.10248, 0.31222, -6.28381, -3.39424, -5.29784, -1.53346, -6.10248, 0.31222, -6.28381, 2.70853, -5.67886, 0.31222, -6.28381, 2.70853, -5.67886, 2.70853, -5.67886, 0.31222, -6.28381, 2.70853, -5.67886], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t5": {"t5": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 12, "vertices": [-0.39844, -2.92494, 0.31891, -2.93472, 0.51808, -4.76895, 1.84996, -4.42595, 0.67754, -6.23634, 2.41924, -5.78782, 0.71744, -6.60324, 2.56161, -6.12828, 1.84999, -4.42597, 1.21522, -4.64044, 1.42314, -3.4046, 0.93485, -3.56959, 0.74788, -2.85567, 1.21533, -4.64035, 0.10096, -4.79594, 1.86962, -7.1392, 0.15515, -7.37844, 1.68271, -6.42529, 0.13968, -6.64058, 0.08553, -4.05814, -0.87004, -3.96458, 0.0854, -4.05811, -0.87025, -3.96456, 0, 0, 1.26708, 5.76638, -0.24743, 11.8054, 2.53326, 11.53323, -0.15462, 7.37834, 1.58326, 7.20821, -0.10046, 4.79588, 1.02914, 4.6853, -0.65422, 2.49883, -0.05412, 2.58243, 0.55414, 2.52289, -0.46736, 1.78489, -0.03872, 1.84461, 0.39577, 1.80211, -0.37376, 1.42788, -0.03087, 1.47563, -0.8411, 3.21281, -0.06956, 3.32026, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.14948, -1.09685, 0.11955, -1.10054, 0.19933, -1.8342, 0.19928, -1.83424, 0.71153, -1.70231, 0.56921, -1.36183, 0.99619, -2.38318, 0.65439, -2.49867, 1.1386, -2.72363, 0.74796, -2.85557, -0.21477, 0.09064, -0.18768, 0.13731, 0.22144, -1.47213, -0.12746, -1.48389, 0.48415, -2.44234, -0.09808, -2.4886, 0.40609, -1.61877, 0.01781, -1.66946, 0.19859, -0.40934, 0.09773, -0.44444, 0.13726, 0.28158, 0.20056, 0.24155], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t1": {"t1": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "vertices": [-0.05408, -0.05383, -0.0086, 0.07748, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.95939, -1.25694, 0.25535, -2.4268, -1.95939, -1.25694, 0.25535, -2.4268, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.29656, -0.46178, 0.29649, -0.46183, 1.13687, -1.77036, 1.03826, -1.6166, -1.30853, 1.35274, 1.91292, 0.3419, 0.78992, -1.23094, -0.99506, 1.02905, -1.30352, -1.16315, 1.07875, 1.4198, 0.46322, -1.74936, 1.26564, 1.22659, 1.26564, 1.22659, 0.18997, -1.79817, 0.88039, 0.85335, 0.13221, -1.25089, -0.65954, -0.64044, -0.09944, 0.93762, 1.40881, 1.36498, 0.21123, -2.0013, -1.61583, -1.03729, -1.6159, -1.03739, 1.40881, 1.36498, 0.21123, -2.0013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.46379, -0.29575, -0.46413, -0.29628, 0.40642, 0.38823, 0.05882, -0.57469, 0.48321, -0.75279, 0.48322, -0.75293, 0.89068, 0.15883, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.45181, -0.93221, 1.26564, 1.22659, 0.18997, -1.79817, 1.26564, 1.22659, -1.6159, -1.03739, 1.40881, 1.36498, 0.21123, -2.0013, 1.26564, 1.22659, 0.18997, -1.79817, 1.40881, 1.36498, 0.21123, -2.0013, 0.47095, -3.06585, 0.47084, -3.06604, -1.00527, 2.89136, 2.96074, -0.80971, 0.47095, -3.06585, 0.47084, -3.06604, -1.00527, 2.89136, 2.96074, -0.80971, 2.3849, 1.5323, 2.3849, 1.53223, -0.31296, 2.95401, 2.96074, -0.80971, 0.47095, -3.06585, 0.47084, -3.06604, -1.00527, 2.89136, 2.96074, -0.80971, 2.3849, 1.5323, -0.31296, 2.95401, -1.00527, 2.89136, 2.96074, -0.80971, 2.96074, -0.80971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.63769, -0.68778, 0, 0, 0, 0, 0, 0, -0.21668, -2.20391, -0.21667, -2.20431, -0.17519, 2.19885, 1.88112, -1.1227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.42033, -1.65334, 1.49794, 0.88929, 0, 0, 0, 0, 1.11296, 2.06289], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "vertices": [-0.10815, -0.10767, -0.0172, 0.15497, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.91878, -2.51389, 0.51069, -4.85361, -3.91878, -2.51389, 0.51069, -4.85361, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.59312, -0.92357, 0.59299, -0.92366, 2.27374, -3.54072, 2.07652, -3.23321, -2.61705, 2.70547, 3.82584, 0.68381, 1.57985, -2.46188, -1.99011, 2.05811, -2.60704, -2.3263, 2.1575, 2.8396, 0.92644, -3.49872, 2.53128, 2.45319, 2.53128, 2.45319, 0.37993, -3.59634, 1.76079, 1.7067, 0.26442, -2.50177, -1.31908, -1.28088, -0.19888, 1.87524, 2.81763, 2.72995, 0.42246, -4.00259, -3.23166, -2.07458, -3.2318, -2.07478, 2.81763, 2.72995, 0.42246, -4.00259, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.92758, -0.59149, -0.92825, -0.59255, 0.81284, 0.77646, 0.11764, -1.14938, 0.96642, -1.50558, 0.96645, -1.50586, 1.78136, 0.31766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.90363, -1.86443, 2.53128, 2.45319, 0.37993, -3.59634, 2.53128, 2.45319, -3.2318, -2.07478, 2.81763, 2.72995, 0.42246, -4.00259, 2.53128, 2.45319, 0.37993, -3.59634, 2.81763, 2.72995, 0.42246, -4.00259, 0.94191, -6.13169, 0.94168, -6.13209, -2.01054, 5.78271, 5.92148, -1.61942, 0.94191, -6.13169, 0.94168, -6.13209, -2.01054, 5.78271, 5.92148, -1.61942, 4.76981, 3.06459, 4.76981, 3.06447, -0.62592, 5.90802, 5.92148, -1.61942, 0.94191, -6.13169, 0.94168, -6.13209, -2.01054, 5.78271, 5.92148, -1.61942, 4.76981, 3.06459, -0.62592, 5.90802, -2.01054, 5.78271, 5.92148, -1.61942, 5.92148, -1.61942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.27538, -1.37556, 0, 0, 0, 0, 0, 0, -0.43336, -4.40783, -0.43335, -4.40862, -0.35037, 4.39771, 3.76224, -2.24539, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.02861, 1.30376], "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 1.8333, "vertices": [-0.07184, -0.07151, -0.01143, 0.10293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.60293, -1.66978, 0.33921, -3.22386, -2.60293, -1.66978, 0.33921, -3.22386, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39397, -0.61345, 0.39387, -0.61351, 1.51027, -2.35182, 1.37927, -2.14756, -1.7383, 1.79703, 2.5412, 0.4542, 1.04937, -1.63523, -1.32187, 1.36704, -1.73165, -1.54518, 1.43306, 1.88612, 0.61536, -2.32392, 1.68133, 1.62946, 1.68133, 1.62946, 0.25236, -2.38876, 1.16955, 1.13362, 0.17564, -1.66173, -0.87616, -0.85079, -0.1321, 1.24557, 1.87152, 1.81329, 0.28061, -2.6586, -2.14653, -1.37798, -2.14662, -1.37811, 1.87152, 1.81329, 0.28061, -2.6586, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61612, -0.39288, -0.61656, -0.39359, 0.5399, 0.51574, 0.07814, -0.76344, 0.64191, -1.00004, 0.64193, -1.00022, 1.18322, 0.21099, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.92865, -1.23839, 1.68133, 1.62946, 0.25236, -2.38876, 1.68133, 1.62946, -2.14662, -1.37811, 1.87152, 1.81329, 0.28061, -2.6586, 1.68133, 1.62946, 0.25236, -2.38876, 1.87152, 1.81329, 0.28061, -2.6586, 0.62564, -4.07279, 0.62548, -4.07306, -1.33544, 3.841, 3.93317, -1.07565, 0.62564, -4.07279, 0.62548, -4.07306, -1.33544, 3.841, 3.93317, -1.07565, 3.1682, 2.03557, 3.1682, 2.03548, -0.41575, 3.92423, 3.93317, -1.07565, 0.62564, -4.07279, 0.62548, -4.07306, -1.33544, 3.841, 3.93317, -1.07565, 3.1682, 2.03557, -0.41575, 3.92423, -1.33544, 3.841, 3.93317, -1.07565, 3.93317, -1.07565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.17557, -0.91368, 0, 0, 0, 0, 0, 0, -0.28785, -2.92777, -0.28784, -2.9283, -0.23272, 2.92104, 2.49896, -1.49143], "curve": 0.328, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 2.0667, "vertices": [-0.04683, -0.04662, -0.00745, 0.0671, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.69677, -1.08848, 0.22112, -2.10154, -1.69677, -1.08848, 0.22112, -2.10154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.25681, -0.39989, 0.25675, -0.39993, 0.9845, -1.53308, 0.8991, -1.39993, -1.13315, 1.17143, 1.65653, 0.29608, 0.68405, -1.06596, -0.86169, 0.89113, -1.12881, -1.00726, 0.93417, 1.22951, 0.40113, -1.5149, 1.09601, 1.06219, 1.09601, 1.06219, 0.16451, -1.55717, 0.7624, 0.73897, 0.11449, -1.08323, -0.57114, -0.5546, -0.08611, 0.81195, 1.21999, 1.18203, 0.18292, -1.73307, -1.39926, -0.89827, -1.39932, -0.89835, 1.21999, 1.18203, 0.18292, -1.73307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.40163, -0.25611, -0.40192, -0.25657, 0.35195, 0.3362, 0.05094, -0.49767, 0.41844, -0.6519, 0.41846, -0.65202, 0.7713, 0.13754, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.25723, -0.80727, 1.09601, 1.06219, 0.16451, -1.55717, 1.09601, 1.06219, -1.39932, -0.89835, 1.21999, 1.18203, 0.18292, -1.73307, 1.09601, 1.06219, 0.16451, -1.55717, 1.21999, 1.18203, 0.18292, -1.73307, 0.40783, -2.65494, 0.40773, -2.65511, -0.87054, 2.50383, 2.56392, -0.70118, 0.40783, -2.65494, 0.40773, -2.65511, -0.87054, 2.50383, 2.56392, -0.70118, 2.06526, 1.32693, 2.06526, 1.32687, -0.27101, 2.55809, 2.56392, -0.70118, 0.40783, -2.65494, 0.40773, -2.65511, -0.87054, 2.50383, 2.56392, -0.70118, 2.06526, 1.32693, -0.27101, 2.55809, -0.87054, 2.50383, 2.56392, -0.70118, 2.56392, -0.70118, 0, 0, -1.32385, -3.54198, -1.32385, -3.54198, 0, 0, 0, 0, 0, 0, -3.30719, -3.79321, -2.38951, -1.48221, 0, 0, 1.76585, 2.05582, 0.59846, 2.13174, -1.53139, -5.27107, -1.51512, -4.78097, -0.09693, -5.37657, 0, 0, -1.19209, -2.98004, -1.89719, -2.61728, -1.45925, -2.69685, -1.45887, -2.69723, 0.9613, 2.92487, 1.80198, -2.5064, 0, 0, 0, 0, 0, 0, -0.4035, -4.25129, -2.86569, -3.37824, -1.88153, -2.127, 0.04842, 1.4704, 2.19588, 1.36243], "curve": 0.344, "c2": 0.37, "c3": 0.684, "c4": 0.72}, {"time": 2.3333, "vertices": [-0.01996, -0.01987, -0.00318, 0.0286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.72327, -0.46398, 0.09426, -0.89581, -0.72327, -0.46398, 0.09426, -0.89581, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10947, -0.17046, 0.10945, -0.17048, 0.41966, -0.6535, 0.38326, -0.59674, -0.48302, 0.49934, 0.70612, 0.12621, 0.29159, -0.45438, -0.36731, 0.37986, -0.48117, -0.42936, 0.3982, 0.52409, 0.17099, -0.64575, 0.46719, 0.45278, 0.46719, 0.45278, 0.07012, -0.66376, 0.32498, 0.315, 0.0488, -0.46174, -0.24346, -0.23641, -0.03671, 0.34611, 0.52004, 0.50386, 0.07797, -0.73874, -0.59646, -0.3829, -0.59648, -0.38294, 0.52004, 0.50386, 0.07797, -0.73874, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.1712, -0.10917, -0.17132, -0.10937, 0.15002, 0.14331, 0.02171, -0.21214, 0.17837, -0.27788, 0.17837, -0.27793, 0.32878, 0.05863, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.53591, -0.34411, 0.46719, 0.45278, 0.07012, -0.66376, 0.46719, 0.45278, -0.59648, -0.38294, 0.52004, 0.50386, 0.07797, -0.73874, 0.46719, 0.45278, 0.07012, -0.66376, 0.52004, 0.50386, 0.07797, -0.73874, 0.17384, -1.1317, 0.1738, -1.13178, -0.37108, 1.06729, 1.09291, -0.29889, 0.17384, -1.1317, 0.1738, -1.13178, -0.37108, 1.06729, 1.09291, -0.29889, 0.88035, 0.56562, 0.88035, 0.5656, -0.11552, 1.09042, 1.09291, -0.29889, 0.17384, -1.1317, 0.1738, -1.13178, -0.37108, 1.06729, 1.09291, -0.29889, 0.88035, 0.56562, -0.11552, 1.09042, -0.37108, 1.06729, 1.09291, -0.29889, 1.09291, -0.29889, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60452, -0.25388, 0, 0, 0, 0, 0, 0, -0.07998, -0.81354, -0.07998, -0.81368, -0.06467, 0.81167, 0.69438, -0.41442], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.6667}]}, "s2": {"s2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 32, "vertices": [22.07477, -3.54094, 3.69453, 22.04933, 18.70436, -9.58028, 18.83405, -9.32139, 20.17175, -5.89142, 16.73083, -11.10138, 11.21765, 16.65291, 11.99408, -15.34206, 12.2041, -15.17514, 14.66483, -12.81245, 13.77158, -13.7691, 0.03809, -6.24991, 6.25006, -0.00549, -1.58636, -6.01065, -1.5033, -6.03193, -0.42786, -6.2018, -0.84317, -6.15905, 0.02647, -4.37454, 4.3748, -0.00398, -1.11037, -4.20718, -1.05197, -4.2221, -0.29968, -4.34105, -0.5899, -4.31121, 0.03423, -5.62488, 5.62502, -0.00491, -1.42778, -5.40953, -1.35297, -5.42867, -0.38501, -5.58155, -0.75888, -5.54308, 0.04949, -8.12482, 8.12502, -0.00711, -2.06233, -7.81376, -1.95428, -7.84142, -0.55609, -8.06225, -1.09616, -8.00668, 6.25003, -0.00549, -0.42786, -6.20176, -0.8432, -6.15901, -0.7702, -6.16862, 0, 0, 0, 0, 0, 0, 0, 0, 1.96228, 8.0368, 0.98547, 8.21402, 1.52618, 6.25093, 0.41183, 6.42131, 0.84241, 6.37918, 0.76648, 6.38878, 1.63522, 6.69738, 0.44125, 6.87995, 0.90262, 6.83478, 0.82123, 6.84506, -4.61996, -0.0197, 1.15207, 4.44936, 1.08994, 4.46485, 0.29395, 4.58656, 0.6015, 4.55641, 0.54712, 4.56337, -6.00598, -0.02567, 1.49799, 5.78424, 1.41675, 5.80436, 0.38187, 5.96255, 0.78177, 5.92339, 0.71069, 5.93254, -12.55516, -0.02464, 3.15555, 12.08385, 2.98859, 12.12624, 0.82599, 12.46162, 1.66193, 12.37785, 1.5141, 12.3968, -17.45715, -3.71411, 1.11806, 17.80681, 0.86215, 17.82034, -2.25931, 17.69774, -7.44731, 7.37964, 8.46443, 5.14635, 8.38376, 5.26205, -1.05708, 15.17361, 7.73257, 11.3766, 5.40977, 4.24232, 6.46448, 0.97621, 6.24728, -4.29456, 0, 0, 0, 0, 0, 0, 0, 0, 5.38968, 0.0605, -0.02296, 5.38988, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.01688, 2.75993, 5.37152, -0.73502, -0.04221, 6.89995, -6.9001, 0.00592, 5.22846, 2.75516, -0.04221, 6.89995, -6.9001, 0.00592, 5.18546, 6.19096, -0.04221, 6.89995, -6.9001, 0.00592, 5.28809, 9.41872, -0.03799, 6.20981, -6.20995, 0.00533, 5.91902, 13.90044, 5.38966, 0.06068, -0.02293, 5.38972, 5.92859, 0.0665, -0.02515, 5.9288, 15.60718, -3.61351, 3.72209, 15.5815, 12.93646, -7.83903, 13.04291, -7.65984, 14.17957, -5.26604, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.34081, 3.78532, -3.76178, 3.36694, -0.03506, 20.24077, 18.03722, -5.49836, 18.11069, -5.24908, 18.74884, -2.00779, 26.68494, -3.54053, 3.7262, 26.65956, 22.80225, -10.87072, 22.94955, -10.55521, 24.43942, -6.3879, 0, 0, 0, 0, -0.04138, 9.70171, 8.65198, -2.61183, 8.98828, -0.93806, 17.44407, 4.90692, -4.78535, 17.47758, 16.77859, -0.17366, 16.77838, 0.05779, 16.51044, 2.98499, 16.67361, 1.86895, -0.90199, 9.07278, -3.91229, -1.36441, -0.21724, 4.13544, -0.28342, 4.13184, -2.31004, -0.00995, 0.57626, 2.22473, 0.54477, 2.23248, -7.39201, -0.03139, -2.1951, 9.15355, 8.70872, -0.3903, 8.71286, -0.27022, 12.17154, 0.05124, -3.03349, -11.72251, -2.87326, -11.76324, -0.77597, -12.08437, -1.44397, -12.02258, 20.04768, 0.08467, -4.99721, -19.3078, -4.73157, -19.37486, -1.2774, -19.90339, -2.61209, -19.77246, -2.37689, -19.802, 26.49159, 0.11195, -6.25244, -25.60249, -1.68784, -26.30091, -3.45157, -26.1279, -3.14087, -26.16696, -3.04172, -12.45525, -0.82101, -12.79504, -1.67905, -12.71088, -1.52789, -12.7299, 14.11174, -5.17985, 14.18106, -4.98475, 13.98761, 0.20106, 13.73758, 2.63915, 13.86261, 1.87518, 12.67681, 9.07967, -8.9912, 12.73941, 13.62257, 5.16695, 12.40665, 7.63677, 12.89124, 6.78601, -0.535, 1.76839, -0.41507, 1.80032, 12.36838, -0.91586, 1.00198, 12.36151, 10.75705, -4.34247, 10.81525, -4.19369, 11.38086, -2.24187, 10.57291, 4.21297, -4.13918, 10.60176, 10.49074, 1.08393, 10.47415, 1.2285, 10.09863, 3.03755, 10.27945, 2.35227, 9.59852, 9.11757, -9.05049, 9.6615, 10.89612, 6.06524, 10.81085, 6.21494, 9.55991, 8.00621, 10.07578, 7.34589, -1.78767, -3.52597, -3.14484, 2.12962, -2.99478, 2.3361, -3.02222, 2.30032], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"s1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 40, "vertices": [5.04284, -1.03522, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.75821, -1.59258, 0.52796, -0.10837, 3.16793, -0.65034, 3.76144, 0.29492, 0.75888, 3.69595, 8.68507, -7.14297, 9.26988, 6.36656, 10.67122, -9.98442, 12.5504, 7.4877, 6.61569, 0.51871, 1.33473, 6.50053, -0.29639, 3.78037, -3.71446, 0.7628, -0.18524, 2.36278, -2.32157, 0.47687, -0.11116, 1.41768, 0, 0, 0, 0, 0, 0, 0, 0, -0.33342, 4.25294, -4.17879, 0.85817, 8.2162, 3.13575, -0.73625, 8.76457, 6.1928, -2.22276, 6.89054, -7.28368, 8.90791, 4.60356, 12.49684, -2.02938, 5.41278, 11.44604, 13.76577, -11.76396, 15.11771, 9.96776, 7.82312, -18.71778, 20.15266, 2.33113, 7.48096, 4.93256, 15.44306, 11.55214, 15.46062, -0.17554, 13.86261, -2.59824, -0.45712, 1.71165, 2.71535, -0.55743], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st": {"st": [{"offset": 235, "vertices": [-0.03704, 0.00266, -0.29658, -0.12264, -0.27658, -0.00723, 0.8156, 0.01129, -1.26044, 0, 0, 0, 0, 0, 0, -0.01119, 1.26048, 0.00564, -0.63022, 0, 0, 0, 0, 0, 0, -0.01383, 1.55705, 0.00431, -0.48193, 0, 0, 0, 0, 0, 0, -0.01399, 1.62601, -0.01413, 1.59414, 0.65904, 1.48645, 0.00366, -0.41595, 0.00364, -0.40779, -0.16861, -0.38027, 0, 0, 0, 0, 0, 0, 0.00103, -0.0725, 0.00069, -0.07127, 0.65245, 0.0057, 0.65243, 0.00561, 0.59645, -0.26444, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.72118, -1.3641, 1.87716, -3.55474, 0.82008, -3.95286, 3.07402, -1.61345, 1.2274, -3.28909, 0.26917, -3.50614, -2.49648, -2.45586, 3.18629, -0.75553, 1.88148, -2.74482, 1.04839, -3.1805, 3.34792, -0.69723, 2.04116, -2.81405, 1.18266, -3.29407, -1.7434, -3.0206, 2.52361, -0.75007, 1.38942, -2.28389, 0.70303, -2.59412, 4.12239, -0.12513, 3.00063, -2.93359, 2.0717, -3.6932, -1.47183, -3.95859, 2.70986, -1.2758, 1.17936, -2.79344, 0.36005, -3.0184, -2.06626, -2.21278, 4.41281, -0.6074, 2.89736, -3.48349, 1.82015, -4.18771, -2.01159, -4.08304, 1.81341, -2.94365, 0.9279, -3.35021, -1.9512, -2.86118, -1.92863, -3.03233, 1.12988, 0.28223, 1.16438, -0.06613, 0.70418, -0.93496, 0.65508, 0.42852, 0.7486, 0.21458, 0.64886, -0.43599, 0.00928, -1.05954, 0.00925, -1.04253, -0.42947, -0.96865, 0.00866, -0.97736, 0.01102, -1.2582, 0.01098, -1.238, -0.51001, -1.15025, 0.00927, -1.05952, 0.00924, -1.04251, -0.42948, -0.96863], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 235, "vertices": [-0.20078, 0.05789, -4.62511, -1.89711, -4.31993, -0.03918, 4.42143, 0.15535, -13.37049, 0, 0, 0, 0, 0, 0, -0.06067, 6.83311, 0.13198, -10.45692, 0, 0, 0, 0, 0, 0, -0.07498, 8.44086, 0.09574, -7.64148, 0, 0, 0, 0, 0, 0, -0.07587, 8.81467, -0.07659, 8.6419, 3.57269, 8.05814, 0.01987, -2.25489, 0.01974, -2.21066, -0.91402, -2.06148, 0, 0, 0, 0, 0, 0, 0.00558, -0.39301, 0.00375, -0.38636, 3.53696, 0.03089, 3.53684, 0.03041, 3.23338, -1.43356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20.17273, -7.39487, 10.17621, -19.27045, 4.44571, -21.42868, 16.66443, -8.74658, 6.65381, -17.83032, 1.4592, -19.00699, -13.53355, -13.31335, 17.27303, -4.09579, 10.19958, -14.8798, 5.68338, -17.24167, 18.14928, -3.77974, 11.06522, -15.25513, 6.41125, -17.85735, -9.45108, -16.37482, 13.68065, -4.06619, 7.53214, -12.38108, 3.81116, -14.06288, 22.34767, -0.67833, 16.2666, -15.90318, 11.23077, -20.02101, -7.97885, -21.45972, 14.69031, -6.91617, 6.39337, -15.14337, 1.95187, -16.3629, -11.20129, -11.99561, 23.92206, -3.29272, 15.70673, -18.88419, 9.86713, -22.70181, -10.90492, -22.1344, 9.83057, -15.95769, 5.03021, -18.16167, -10.57756, -15.51062, -10.45518, -16.43842, 6.12512, 1.52996, 6.31219, -0.35849, 3.81738, -5.06848, 3.55121, 2.32304, 4.0582, 1.16325, 3.5175, -2.36353, 0.05032, -5.74381, 0.05016, -5.6516, -2.3282, -5.25108, 0.04696, -5.29835, 0.05974, -6.82079, 0.05953, -6.71129, -2.76478, -6.23558, 0.05028, -5.74373, 0.0501, -5.65153, -2.32826, -5.25102], "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "offset": 235, "vertices": [-0.03704, 0.00266, -0.29658, -0.12264, -0.27658, -0.00723, 0.8156, 0.01129, -1.26044, 0, 0, 0, 0, 0, 0, -0.01119, 1.26048, 0.00564, -0.63022, 0, 0, 0, 0, 0, 0, -0.01383, 1.55705, 0.00431, -0.48193, 0, 0, 0, 0, 0, 0, -0.01399, 1.62601, -0.01413, 1.59414, 0.65904, 1.48645, 0.00366, -0.41595, 0.00364, -0.40779, -0.16861, -0.38027, 0, 0, 0, 0, 0, 0, 0.00103, -0.0725, 0.00069, -0.07127, 0.65245, 0.0057, 0.65243, 0.00561, 0.59645, -0.26444, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.72118, -1.3641, 1.87716, -3.55474, 0.82008, -3.95286, 3.07402, -1.61345, 1.2274, -3.28909, 0.26917, -3.50614, -2.49648, -2.45586, 3.18629, -0.75553, 1.88148, -2.74482, 1.04839, -3.1805, 3.34792, -0.69723, 2.04116, -2.81405, 1.18266, -3.29407, -1.7434, -3.0206, 2.52361, -0.75007, 1.38942, -2.28389, 0.70303, -2.59412, 4.12239, -0.12513, 3.00063, -2.93359, 2.0717, -3.6932, -1.47183, -3.95859, 2.70986, -1.2758, 1.17936, -2.79344, 0.36005, -3.0184, -2.06626, -2.21278, 4.41281, -0.6074, 2.89736, -3.48349, 1.82015, -4.18771, -2.01159, -4.08304, 1.81341, -2.94365, 0.9279, -3.35021, -1.9512, -2.86118, -1.92863, -3.03233, 1.12988, 0.28223, 1.16438, -0.06613, 0.70418, -0.93496, 0.65508, 0.42852, 0.7486, 0.21458, 0.64886, -0.43599, 0.00928, -1.05954, 0.00925, -1.04253, -0.42947, -0.96865, 0.00866, -0.97736, 0.01102, -1.2582, 0.01098, -1.238, -0.51001, -1.15025, 0.00927, -1.05952, 0.00924, -1.04251, -0.42948, -0.96863]}]}}}}, "animation2": {"slots": {"bg": {"attachment": [{"name": null}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone4": {"rotate": [{"angle": -0.38, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.38}]}, "t1": {"rotate": [{"angle": -0.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.22}]}, "s2": {"rotate": [{"angle": 2.02, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 1.33, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 2.4, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.5, "angle": 2.27, "curve": 0.312, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 2.6667, "angle": 2.02}]}, "s3": {"rotate": [{"angle": -4.89, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -1.99, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.8333, "angle": -0.38, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.89}]}, "s1": {"rotate": [{"angle": 1.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.86}]}, "s12": {"rotate": [{"angle": -0.3, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.8, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.3}]}, "s10": {"rotate": [{"angle": 2.33, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.94, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.33}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.67, "y": 11.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.54, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.67, "y": 11.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s11": {"rotate": [{"angle": 0.91, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 20.4, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.3333, "angle": 5.24, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.91}]}, "t3": {"scale": [{"y": 0.996, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "y": 0.94, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "y": 0.996}], "shear": [{"y": 0.23, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 3.6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "y": 0.23}]}, "t11": {"shear": [{"x": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -0.44}]}, "t12": {"shear": [{"x": -1.19, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "x": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -1.19}]}, "t13": {"shear": [{"x": -1.96, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": -1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -2.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -1.96}]}, "t4": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t2": {"rotate": [{"angle": 0.08, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.2, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.08}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.54, "y": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t32": {"rotate": [{"angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.42, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -5.01}]}, "t33": {"rotate": [{"angle": -4.35, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 1.42, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": -4.35}]}, "t34": {"rotate": [{"angle": -3.05, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 1.42, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -3.05}]}, "t35": {"rotate": [{"angle": -1.58, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 1.42, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": -1.58}]}, "t36": {"rotate": [{"angle": -0.14, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 1.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -0.14}]}, "t37": {"rotate": [{"angle": 1.02, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.42, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": 1.02}]}, "t15": {"rotate": [{"angle": -1.5, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -3.47, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.41, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.5}]}, "t16": {"rotate": [{"angle": 0.03, "curve": 0.342, "c2": 0.36, "c3": 0.697, "c4": 0.76}, {"time": 0.5, "angle": -2.71, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7667, "angle": -3.47, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 2.41, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "angle": 0.03}]}, "t17": {"rotate": [{"angle": 1.5, "curve": 0.308, "c2": 0.25, "c3": 0.662, "c4": 0.65}, {"time": 0.5, "angle": -1.31, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.0333, "angle": -3.47, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 2.41, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "angle": 1.5}]}, "t18": {"rotate": [{"angle": 2.39, "curve": 0.267, "c2": 0.05, "c3": 0.624, "c4": 0.49}, {"time": 0.5, "angle": 0.25, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.3, "angle": -3.47, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 2.41, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.6667, "angle": 2.39}]}, "t19": {"rotate": [{"angle": 1.81, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": 2.41, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": 1.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.5667, "angle": -3.47, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": 1.81}]}, "t20": {"rotate": [{"angle": 0.44, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.47, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 0.44}]}, "t29": {"rotate": [{"angle": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.74, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.27}], "scale": [{"x": 1.16}]}, "t30": {"rotate": [{"angle": 1.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.21}]}, "t31": {"rotate": [{"angle": 3.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 3.74}]}, "s4": {"rotate": [{"angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.55}], "scale": [{"x": 1.08}]}, "s5": {"rotate": [{"angle": 0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -2.45, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": 0.16}]}, "s6": {"rotate": [{"angle": -0.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -2.45, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.56}]}, "s7": {"rotate": [{"angle": -1.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -2.45, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -1.35}]}, "s8": {"rotate": [{"angle": -2.06, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -2.45, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": -2.06}]}, "bone3": {"translate": [{"x": 0.73, "y": 0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "x": 5.96, "y": 4.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.73, "y": 0.74}], "scale": [{"y": 1.004, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "y": 1.004}]}, "s16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.49, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -16.32, "curve": "stepped"}, {"time": 1.3333, "angle": -16.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s17": {"rotate": [{"angle": -5.48, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -16.32, "curve": "stepped"}, {"time": 1.8333, "angle": -16.32, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -5.48}]}, "t22": {"rotate": [{"angle": 1.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.75}]}, "t23": {"rotate": [{"angle": 2.31, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.6667, "angle": 0.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 3.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 2.31}]}, "t24": {"rotate": [{"angle": 2.85, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 0.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.85}]}, "t25": {"rotate": [{"angle": 3.29, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.6667, "angle": 1.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 3.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": 3.29}]}, "t26": {"rotate": [{"angle": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 1.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.5}]}, "t27": {"rotate": [{"angle": 3.28, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": 3.5, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.6667, "angle": 2.33, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 3.28}]}, "t28": {"rotate": [{"angle": 2.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 3.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": 2.85, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.85}]}, "t6": {"rotate": [{"angle": -3.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -5.6}, {"time": 1.6667, "angle": 5.46, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -3.56}]}, "t9": {"rotate": [{"angle": -0.11, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -3.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "angle": -5.6, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.11}]}, "t10": {"rotate": [{"angle": 3.43, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -0.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": -5.6, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 3.43}]}, "t7": {"rotate": [{"angle": -2.31, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 8.76, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "angle": -2.31}]}, "t8": {"rotate": [{"angle": 5.06, "curve": 0.32, "c2": 0.29, "c3": 0.669, "c4": 0.67}, {"time": 0.4333, "angle": -1.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.9333, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 8.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 5.06}]}, "st": {"scale": [{"x": 1.02}]}, "st2": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st3": {"shear": [{"x": 0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.44}]}, "st4": {"shear": [{"x": 1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 2.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.2}]}, "st5": {"shear": [{"x": 1.96, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 2.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.96}]}, "s18": {"rotate": [{"angle": 1.61, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.6, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 1.61}]}}, "deform": {"default": {"t6": {"t6": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 154, "vertices": [-1.68906, -3.48419, -1.74522, -3.45645, -1.74522, -3.45645, -2.83593, -5.61667, -3.39424, -5.29784, -3.39424, -5.29784, -1.53346, -6.10248, 0.31222, -6.28381, -3.39424, -5.29784, -1.53346, -6.10248, 0.31222, -6.28381, 2.70853, -5.67886, 0.31222, -6.28381, 2.70853, -5.67886, 2.70853, -5.67886, 0.31222, -6.28381, 2.70853, -5.67886], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t5": {"t5": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 12, "vertices": [-0.39844, -2.92494, 0.31891, -2.93472, 0.51808, -4.76895, 1.84996, -4.42595, 0.67754, -6.23634, 2.41924, -5.78782, 0.71744, -6.60324, 2.56161, -6.12828, 1.84999, -4.42597, 1.21522, -4.64044, 1.42314, -3.4046, 0.93485, -3.56959, 0.74788, -2.85567, 1.21533, -4.64035, 0.10096, -4.79594, 1.86962, -7.1392, 0.15515, -7.37844, 1.68271, -6.42529, 0.13968, -6.64058, 0.08553, -4.05814, -0.87004, -3.96458, 0.0854, -4.05811, -0.87025, -3.96456, 0, 0, 1.26708, 5.76638, -0.24743, 11.8054, 2.53326, 11.53323, -0.15462, 7.37834, 1.58326, 7.20821, -0.10046, 4.79588, 1.02914, 4.6853, -0.65422, 2.49883, -0.05412, 2.58243, 0.55414, 2.52289, -0.46736, 1.78489, -0.03872, 1.84461, 0.39577, 1.80211, -0.37376, 1.42788, -0.03087, 1.47563, -0.8411, 3.21281, -0.06956, 3.32026, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.14948, -1.09685, 0.11955, -1.10054, 0.19933, -1.8342, 0.19928, -1.83424, 0.71153, -1.70231, 0.56921, -1.36183, 0.99619, -2.38318, 0.65439, -2.49867, 1.1386, -2.72363, 0.74796, -2.85557, -0.21477, 0.09064, -0.18768, 0.13731, 0.22144, -1.47213, -0.12746, -1.48389, 0.48415, -2.44234, -0.09808, -2.4886, 0.40609, -1.61877, 0.01781, -1.66946, 0.19859, -0.40934, 0.09773, -0.44444, 0.13726, 0.28158, 0.20056, 0.24155], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t1": {"t1": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "vertices": [-0.05408, -0.05383, -0.0086, 0.07748, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.95939, -1.25694, 0.25535, -2.4268, -1.95939, -1.25694, 0.25535, -2.4268, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.29656, -0.46178, 0.29649, -0.46183, 1.13687, -1.77036, 1.03826, -1.6166, -1.30853, 1.35274, 1.91292, 0.3419, 0.78992, -1.23094, -0.99506, 1.02905, -1.30352, -1.16315, 1.07875, 1.4198, 0.46322, -1.74936, 1.26564, 1.22659, 1.26564, 1.22659, 0.18997, -1.79817, 0.88039, 0.85335, 0.13221, -1.25089, -0.65954, -0.64044, -0.09944, 0.93762, 1.40881, 1.36498, 0.21123, -2.0013, -1.61583, -1.03729, -1.6159, -1.03739, 1.40881, 1.36498, 0.21123, -2.0013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.46379, -0.29575, -0.46413, -0.29628, 0.40642, 0.38823, 0.05882, -0.57469, 0.48321, -0.75279, 0.48322, -0.75293, 0.89068, 0.15883, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.45181, -0.93221, 1.26564, 1.22659, 0.18997, -1.79817, 1.26564, 1.22659, -1.6159, -1.03739, 1.40881, 1.36498, 0.21123, -2.0013, 1.26564, 1.22659, 0.18997, -1.79817, 1.40881, 1.36498, 0.21123, -2.0013, 0.47095, -3.06585, 0.47084, -3.06604, -1.00527, 2.89136, 2.96074, -0.80971, 0.47095, -3.06585, 0.47084, -3.06604, -1.00527, 2.89136, 2.96074, -0.80971, 2.3849, 1.5323, 2.3849, 1.53223, -0.31296, 2.95401, 2.96074, -0.80971, 0.47095, -3.06585, 0.47084, -3.06604, -1.00527, 2.89136, 2.96074, -0.80971, 2.3849, 1.5323, -0.31296, 2.95401, -1.00527, 2.89136, 2.96074, -0.80971, 2.96074, -0.80971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.63769, -0.68778, 0, 0, 0, 0, 0, 0, -0.21668, -2.20391, -0.21667, -2.20431, -0.17519, 2.19885, 1.88112, -1.1227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.42033, -1.65334, 1.49794, 0.88929, 0, 0, 0, 0, 1.11296, 2.06289], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "vertices": [-0.10815, -0.10767, -0.0172, 0.15497, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.91878, -2.51389, 0.51069, -4.85361, -3.91878, -2.51389, 0.51069, -4.85361, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.59312, -0.92357, 0.59299, -0.92366, 2.27374, -3.54072, 2.07652, -3.23321, -2.61705, 2.70547, 3.82584, 0.68381, 1.57985, -2.46188, -1.99011, 2.05811, -2.60704, -2.3263, 2.1575, 2.8396, 0.92644, -3.49872, 2.53128, 2.45319, 2.53128, 2.45319, 0.37993, -3.59634, 1.76079, 1.7067, 0.26442, -2.50177, -1.31908, -1.28088, -0.19888, 1.87524, 2.81763, 2.72995, 0.42246, -4.00259, -3.23166, -2.07458, -3.2318, -2.07478, 2.81763, 2.72995, 0.42246, -4.00259, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.92758, -0.59149, -0.92825, -0.59255, 0.81284, 0.77646, 0.11764, -1.14938, 0.96642, -1.50558, 0.96645, -1.50586, 1.78136, 0.31766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.90363, -1.86443, 2.53128, 2.45319, 0.37993, -3.59634, 2.53128, 2.45319, -3.2318, -2.07478, 2.81763, 2.72995, 0.42246, -4.00259, 2.53128, 2.45319, 0.37993, -3.59634, 2.81763, 2.72995, 0.42246, -4.00259, 0.94191, -6.13169, 0.94168, -6.13209, -2.01054, 5.78271, 5.92148, -1.61942, 0.94191, -6.13169, 0.94168, -6.13209, -2.01054, 5.78271, 5.92148, -1.61942, 4.76981, 3.06459, 4.76981, 3.06447, -0.62592, 5.90802, 5.92148, -1.61942, 0.94191, -6.13169, 0.94168, -6.13209, -2.01054, 5.78271, 5.92148, -1.61942, 4.76981, 3.06459, -0.62592, 5.90802, -2.01054, 5.78271, 5.92148, -1.61942, 5.92148, -1.61942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.27538, -1.37556, 0, 0, 0, 0, 0, 0, -0.43336, -4.40783, -0.43335, -4.40862, -0.35037, 4.39771, 3.76224, -2.24539, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.02861, 1.30376], "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 1.8333, "vertices": [-0.07184, -0.07151, -0.01143, 0.10293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.60293, -1.66978, 0.33921, -3.22386, -2.60293, -1.66978, 0.33921, -3.22386, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39397, -0.61345, 0.39387, -0.61351, 1.51027, -2.35182, 1.37927, -2.14756, -1.7383, 1.79703, 2.5412, 0.4542, 1.04937, -1.63523, -1.32187, 1.36704, -1.73165, -1.54518, 1.43306, 1.88612, 0.61536, -2.32392, 1.68133, 1.62946, 1.68133, 1.62946, 0.25236, -2.38876, 1.16955, 1.13362, 0.17564, -1.66173, -0.87616, -0.85079, -0.1321, 1.24557, 1.87152, 1.81329, 0.28061, -2.6586, -2.14653, -1.37798, -2.14662, -1.37811, 1.87152, 1.81329, 0.28061, -2.6586, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61612, -0.39288, -0.61656, -0.39359, 0.5399, 0.51574, 0.07814, -0.76344, 0.64191, -1.00004, 0.64193, -1.00022, 1.18322, 0.21099, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.92865, -1.23839, 1.68133, 1.62946, 0.25236, -2.38876, 1.68133, 1.62946, -2.14662, -1.37811, 1.87152, 1.81329, 0.28061, -2.6586, 1.68133, 1.62946, 0.25236, -2.38876, 1.87152, 1.81329, 0.28061, -2.6586, 0.62564, -4.07279, 0.62548, -4.07306, -1.33544, 3.841, 3.93317, -1.07565, 0.62564, -4.07279, 0.62548, -4.07306, -1.33544, 3.841, 3.93317, -1.07565, 3.1682, 2.03557, 3.1682, 2.03548, -0.41575, 3.92423, 3.93317, -1.07565, 0.62564, -4.07279, 0.62548, -4.07306, -1.33544, 3.841, 3.93317, -1.07565, 3.1682, 2.03557, -0.41575, 3.92423, -1.33544, 3.841, 3.93317, -1.07565, 3.93317, -1.07565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.17557, -0.91368, 0, 0, 0, 0, 0, 0, -0.28785, -2.92777, -0.28784, -2.9283, -0.23272, 2.92104, 2.49896, -1.49143], "curve": 0.328, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 2.0667, "vertices": [-0.04683, -0.04662, -0.00745, 0.0671, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.69677, -1.08848, 0.22112, -2.10154, -1.69677, -1.08848, 0.22112, -2.10154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.25681, -0.39989, 0.25675, -0.39993, 0.9845, -1.53308, 0.8991, -1.39993, -1.13315, 1.17143, 1.65653, 0.29608, 0.68405, -1.06596, -0.86169, 0.89113, -1.12881, -1.00726, 0.93417, 1.22951, 0.40113, -1.5149, 1.09601, 1.06219, 1.09601, 1.06219, 0.16451, -1.55717, 0.7624, 0.73897, 0.11449, -1.08323, -0.57114, -0.5546, -0.08611, 0.81195, 1.21999, 1.18203, 0.18292, -1.73307, -1.39926, -0.89827, -1.39932, -0.89835, 1.21999, 1.18203, 0.18292, -1.73307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.40163, -0.25611, -0.40192, -0.25657, 0.35195, 0.3362, 0.05094, -0.49767, 0.41844, -0.6519, 0.41846, -0.65202, 0.7713, 0.13754, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.25723, -0.80727, 1.09601, 1.06219, 0.16451, -1.55717, 1.09601, 1.06219, -1.39932, -0.89835, 1.21999, 1.18203, 0.18292, -1.73307, 1.09601, 1.06219, 0.16451, -1.55717, 1.21999, 1.18203, 0.18292, -1.73307, 0.40783, -2.65494, 0.40773, -2.65511, -0.87054, 2.50383, 2.56392, -0.70118, 0.40783, -2.65494, 0.40773, -2.65511, -0.87054, 2.50383, 2.56392, -0.70118, 2.06526, 1.32693, 2.06526, 1.32687, -0.27101, 2.55809, 2.56392, -0.70118, 0.40783, -2.65494, 0.40773, -2.65511, -0.87054, 2.50383, 2.56392, -0.70118, 2.06526, 1.32693, -0.27101, 2.55809, -0.87054, 2.50383, 2.56392, -0.70118, 2.56392, -0.70118, 0, 0, -1.32385, -3.54198, -1.32385, -3.54198, 0, 0, 0, 0, 0, 0, -3.30719, -3.79321, -2.38951, -1.48221, 0, 0, 1.76585, 2.05582, 0.59846, 2.13174, -1.53139, -5.27107, -1.51512, -4.78097, -0.09693, -5.37657, 0, 0, -1.19209, -2.98004, -1.89719, -2.61728, -1.45925, -2.69685, -1.45887, -2.69723, 0.9613, 2.92487, 1.80198, -2.5064, 0, 0, 0, 0, 0, 0, -0.4035, -4.25129, -2.86569, -3.37824, -1.88153, -2.127, 0.04842, 1.4704, 2.19588, 1.36243], "curve": 0.344, "c2": 0.37, "c3": 0.684, "c4": 0.72}, {"time": 2.3333, "vertices": [-0.01996, -0.01987, -0.00318, 0.0286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.72327, -0.46398, 0.09426, -0.89581, -0.72327, -0.46398, 0.09426, -0.89581, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10947, -0.17046, 0.10945, -0.17048, 0.41966, -0.6535, 0.38326, -0.59674, -0.48302, 0.49934, 0.70612, 0.12621, 0.29159, -0.45438, -0.36731, 0.37986, -0.48117, -0.42936, 0.3982, 0.52409, 0.17099, -0.64575, 0.46719, 0.45278, 0.46719, 0.45278, 0.07012, -0.66376, 0.32498, 0.315, 0.0488, -0.46174, -0.24346, -0.23641, -0.03671, 0.34611, 0.52004, 0.50386, 0.07797, -0.73874, -0.59646, -0.3829, -0.59648, -0.38294, 0.52004, 0.50386, 0.07797, -0.73874, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.1712, -0.10917, -0.17132, -0.10937, 0.15002, 0.14331, 0.02171, -0.21214, 0.17837, -0.27788, 0.17837, -0.27793, 0.32878, 0.05863, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.53591, -0.34411, 0.46719, 0.45278, 0.07012, -0.66376, 0.46719, 0.45278, -0.59648, -0.38294, 0.52004, 0.50386, 0.07797, -0.73874, 0.46719, 0.45278, 0.07012, -0.66376, 0.52004, 0.50386, 0.07797, -0.73874, 0.17384, -1.1317, 0.1738, -1.13178, -0.37108, 1.06729, 1.09291, -0.29889, 0.17384, -1.1317, 0.1738, -1.13178, -0.37108, 1.06729, 1.09291, -0.29889, 0.88035, 0.56562, 0.88035, 0.5656, -0.11552, 1.09042, 1.09291, -0.29889, 0.17384, -1.1317, 0.1738, -1.13178, -0.37108, 1.06729, 1.09291, -0.29889, 0.88035, 0.56562, -0.11552, 1.09042, -0.37108, 1.06729, 1.09291, -0.29889, 1.09291, -0.29889, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60452, -0.25388, 0, 0, 0, 0, 0, 0, -0.07998, -0.81354, -0.07998, -0.81368, -0.06467, 0.81167, 0.69438, -0.41442], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.6667}]}, "s2": {"s2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 32, "vertices": [22.07477, -3.54094, 3.69453, 22.04933, 18.70436, -9.58028, 18.83405, -9.32139, 20.17175, -5.89142, 16.73083, -11.10138, 11.21765, 16.65291, 11.99408, -15.34206, 12.2041, -15.17514, 14.66483, -12.81245, 13.77158, -13.7691, 0.03809, -6.24991, 6.25006, -0.00549, -1.58636, -6.01065, -1.5033, -6.03193, -0.42786, -6.2018, -0.84317, -6.15905, 0.02647, -4.37454, 4.3748, -0.00398, -1.11037, -4.20718, -1.05197, -4.2221, -0.29968, -4.34105, -0.5899, -4.31121, 0.03423, -5.62488, 5.62502, -0.00491, -1.42778, -5.40953, -1.35297, -5.42867, -0.38501, -5.58155, -0.75888, -5.54308, 0.04949, -8.12482, 8.12502, -0.00711, -2.06233, -7.81376, -1.95428, -7.84142, -0.55609, -8.06225, -1.09616, -8.00668, 6.25003, -0.00549, -0.42786, -6.20176, -0.8432, -6.15901, -0.7702, -6.16862, 0, 0, 0, 0, 0, 0, 0, 0, 1.96228, 8.0368, 0.98547, 8.21402, 1.52618, 6.25093, 0.41183, 6.42131, 0.84241, 6.37918, 0.76648, 6.38878, 1.63522, 6.69738, 0.44125, 6.87995, 0.90262, 6.83478, 0.82123, 6.84506, -4.61996, -0.0197, 1.15207, 4.44936, 1.08994, 4.46485, 0.29395, 4.58656, 0.6015, 4.55641, 0.54712, 4.56337, -6.00598, -0.02567, 1.49799, 5.78424, 1.41675, 5.80436, 0.38187, 5.96255, 0.78177, 5.92339, 0.71069, 5.93254, -12.55516, -0.02464, 3.15555, 12.08385, 2.98859, 12.12624, 0.82599, 12.46162, 1.66193, 12.37785, 1.5141, 12.3968, -17.45715, -3.71411, 1.11806, 17.80681, 0.86215, 17.82034, -2.25931, 17.69774, -7.44731, 7.37964, 8.46443, 5.14635, 8.38376, 5.26205, -1.05708, 15.17361, 7.73257, 11.3766, 5.40977, 4.24232, 6.46448, 0.97621, 6.24728, -4.29456, 0, 0, 0, 0, 0, 0, 0, 0, 5.38968, 0.0605, -0.02296, 5.38988, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.01688, 2.75993, 5.37152, -0.73502, -0.04221, 6.89995, -6.9001, 0.00592, 5.22846, 2.75516, -0.04221, 6.89995, -6.9001, 0.00592, 5.18546, 6.19096, -0.04221, 6.89995, -6.9001, 0.00592, 5.28809, 9.41872, -0.03799, 6.20981, -6.20995, 0.00533, 5.91902, 13.90044, 5.38966, 0.06068, -0.02293, 5.38972, 5.92859, 0.0665, -0.02515, 5.9288, 15.60718, -3.61351, 3.72209, 15.5815, 12.93646, -7.83903, 13.04291, -7.65984, 14.17957, -5.26604, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.34081, 3.78532, -3.76178, 3.36694, -0.03506, 20.24077, 18.03722, -5.49836, 18.11069, -5.24908, 18.74884, -2.00779, 26.68494, -3.54053, 3.7262, 26.65956, 22.80225, -10.87072, 22.94955, -10.55521, 24.43942, -6.3879, 0, 0, 0, 0, -0.04138, 9.70171, 8.65198, -2.61183, 8.98828, -0.93806, 17.44407, 4.90692, -4.78535, 17.47758, 16.77859, -0.17366, 16.77838, 0.05779, 16.51044, 2.98499, 16.67361, 1.86895, -0.90199, 9.07278, -3.91229, -1.36441, -0.21724, 4.13544, -0.28342, 4.13184, -2.31004, -0.00995, 0.57626, 2.22473, 0.54477, 2.23248, -7.39201, -0.03139, -2.1951, 9.15355, 8.70872, -0.3903, 8.71286, -0.27022, 12.17154, 0.05124, -3.03349, -11.72251, -2.87326, -11.76324, -0.77597, -12.08437, -1.44397, -12.02258, 20.04768, 0.08467, -4.99721, -19.3078, -4.73157, -19.37486, -1.2774, -19.90339, -2.61209, -19.77246, -2.37689, -19.802, 26.49159, 0.11195, -6.25244, -25.60249, -1.68784, -26.30091, -3.45157, -26.1279, -3.14087, -26.16696, -3.04172, -12.45525, -0.82101, -12.79504, -1.67905, -12.71088, -1.52789, -12.7299, 14.11174, -5.17985, 14.18106, -4.98475, 13.98761, 0.20106, 13.73758, 2.63915, 13.86261, 1.87518, 12.67681, 9.07967, -8.9912, 12.73941, 13.62257, 5.16695, 12.40665, 7.63677, 12.89124, 6.78601, -0.535, 1.76839, -0.41507, 1.80032, 12.36838, -0.91586, 1.00198, 12.36151, 10.75705, -4.34247, 10.81525, -4.19369, 11.38086, -2.24187, 10.57291, 4.21297, -4.13918, 10.60176, 10.49074, 1.08393, 10.47415, 1.2285, 10.09863, 3.03755, 10.27945, 2.35227, 9.59852, 9.11757, -9.05049, 9.6615, 10.89612, 6.06524, 10.81085, 6.21494, 9.55991, 8.00621, 10.07578, 7.34589, -1.78767, -3.52597, -3.14484, 2.12962, -2.99478, 2.3361, -3.02222, 2.30032], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"s1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 40, "vertices": [5.04284, -1.03522, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.75821, -1.59258, 0.52796, -0.10837, 3.16793, -0.65034, 3.76144, 0.29492, 0.75888, 3.69595, 8.68507, -7.14297, 9.26988, 6.36656, 10.67122, -9.98442, 12.5504, 7.4877, 6.61569, 0.51871, 1.33473, 6.50053, -0.29639, 3.78037, -3.71446, 0.7628, -0.18524, 2.36278, -2.32157, 0.47687, -0.11116, 1.41768, 0, 0, 0, 0, 0, 0, 0, 0, -0.33342, 4.25294, -4.17879, 0.85817, 8.2162, 3.13575, -0.73625, 8.76457, 6.1928, -2.22276, 6.89054, -7.28368, 8.90791, 4.60356, 12.49684, -2.02938, 5.41278, 11.44604, 13.76577, -11.76396, 15.11771, 9.96776, 7.82312, -18.71778, 20.15266, 2.33113, 7.48096, 4.93256, 15.44306, 11.55214, 15.46062, -0.17554, 13.86261, -2.59824, -0.45712, 1.71165, 2.71535, -0.55743], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st": {"st": [{"offset": 235, "vertices": [-0.03704, 0.00266, -0.29658, -0.12264, -0.27658, -0.00723, 0.8156, 0.01129, -1.26044, 0, 0, 0, 0, 0, 0, -0.01119, 1.26048, 0.00564, -0.63022, 0, 0, 0, 0, 0, 0, -0.01383, 1.55705, 0.00431, -0.48193, 0, 0, 0, 0, 0, 0, -0.01399, 1.62601, -0.01413, 1.59414, 0.65904, 1.48645, 0.00366, -0.41595, 0.00364, -0.40779, -0.16861, -0.38027, 0, 0, 0, 0, 0, 0, 0.00103, -0.0725, 0.00069, -0.07127, 0.65245, 0.0057, 0.65243, 0.00561, 0.59645, -0.26444, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.72118, -1.3641, 1.87716, -3.55474, 0.82008, -3.95286, 3.07402, -1.61345, 1.2274, -3.28909, 0.26917, -3.50614, -2.49648, -2.45586, 3.18629, -0.75553, 1.88148, -2.74482, 1.04839, -3.1805, 3.34792, -0.69723, 2.04116, -2.81405, 1.18266, -3.29407, -1.7434, -3.0206, 2.52361, -0.75007, 1.38942, -2.28389, 0.70303, -2.59412, 4.12239, -0.12513, 3.00063, -2.93359, 2.0717, -3.6932, -1.47183, -3.95859, 2.70986, -1.2758, 1.17936, -2.79344, 0.36005, -3.0184, -2.06626, -2.21278, 4.41281, -0.6074, 2.89736, -3.48349, 1.82015, -4.18771, -2.01159, -4.08304, 1.81341, -2.94365, 0.9279, -3.35021, -1.9512, -2.86118, -1.92863, -3.03233, 1.12988, 0.28223, 1.16438, -0.06613, 0.70418, -0.93496, 0.65508, 0.42852, 0.7486, 0.21458, 0.64886, -0.43599, 0.00928, -1.05954, 0.00925, -1.04253, -0.42947, -0.96865, 0.00866, -0.97736, 0.01102, -1.2582, 0.01098, -1.238, -0.51001, -1.15025, 0.00927, -1.05952, 0.00924, -1.04251, -0.42948, -0.96863], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 235, "vertices": [-0.20078, 0.05789, -4.62511, -1.89711, -4.31993, -0.03918, 4.42143, 0.15535, -13.37049, 0, 0, 0, 0, 0, 0, -0.06067, 6.83311, 0.13198, -10.45692, 0, 0, 0, 0, 0, 0, -0.07498, 8.44086, 0.09574, -7.64148, 0, 0, 0, 0, 0, 0, -0.07587, 8.81467, -0.07659, 8.6419, 3.57269, 8.05814, 0.01987, -2.25489, 0.01974, -2.21066, -0.91402, -2.06148, 0, 0, 0, 0, 0, 0, 0.00558, -0.39301, 0.00375, -0.38636, 3.53696, 0.03089, 3.53684, 0.03041, 3.23338, -1.43356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20.17273, -7.39487, 10.17621, -19.27045, 4.44571, -21.42868, 16.66443, -8.74658, 6.65381, -17.83032, 1.4592, -19.00699, -13.53355, -13.31335, 17.27303, -4.09579, 10.19958, -14.8798, 5.68338, -17.24167, 18.14928, -3.77974, 11.06522, -15.25513, 6.41125, -17.85735, -9.45108, -16.37482, 13.68065, -4.06619, 7.53214, -12.38108, 3.81116, -14.06288, 22.34767, -0.67833, 16.2666, -15.90318, 11.23077, -20.02101, -7.97885, -21.45972, 14.69031, -6.91617, 6.39337, -15.14337, 1.95187, -16.3629, -11.20129, -11.99561, 23.92206, -3.29272, 15.70673, -18.88419, 9.86713, -22.70181, -10.90492, -22.1344, 9.83057, -15.95769, 5.03021, -18.16167, -10.57756, -15.51062, -10.45518, -16.43842, 6.12512, 1.52996, 6.31219, -0.35849, 3.81738, -5.06848, 3.55121, 2.32304, 4.0582, 1.16325, 3.5175, -2.36353, 0.05032, -5.74381, 0.05016, -5.6516, -2.3282, -5.25108, 0.04696, -5.29835, 0.05974, -6.82079, 0.05953, -6.71129, -2.76478, -6.23558, 0.05028, -5.74373, 0.0501, -5.65153, -2.32826, -5.25102], "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "offset": 235, "vertices": [-0.03704, 0.00266, -0.29658, -0.12264, -0.27658, -0.00723, 0.8156, 0.01129, -1.26044, 0, 0, 0, 0, 0, 0, -0.01119, 1.26048, 0.00564, -0.63022, 0, 0, 0, 0, 0, 0, -0.01383, 1.55705, 0.00431, -0.48193, 0, 0, 0, 0, 0, 0, -0.01399, 1.62601, -0.01413, 1.59414, 0.65904, 1.48645, 0.00366, -0.41595, 0.00364, -0.40779, -0.16861, -0.38027, 0, 0, 0, 0, 0, 0, 0.00103, -0.0725, 0.00069, -0.07127, 0.65245, 0.0057, 0.65243, 0.00561, 0.59645, -0.26444, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.72118, -1.3641, 1.87716, -3.55474, 0.82008, -3.95286, 3.07402, -1.61345, 1.2274, -3.28909, 0.26917, -3.50614, -2.49648, -2.45586, 3.18629, -0.75553, 1.88148, -2.74482, 1.04839, -3.1805, 3.34792, -0.69723, 2.04116, -2.81405, 1.18266, -3.29407, -1.7434, -3.0206, 2.52361, -0.75007, 1.38942, -2.28389, 0.70303, -2.59412, 4.12239, -0.12513, 3.00063, -2.93359, 2.0717, -3.6932, -1.47183, -3.95859, 2.70986, -1.2758, 1.17936, -2.79344, 0.36005, -3.0184, -2.06626, -2.21278, 4.41281, -0.6074, 2.89736, -3.48349, 1.82015, -4.18771, -2.01159, -4.08304, 1.81341, -2.94365, 0.9279, -3.35021, -1.9512, -2.86118, -1.92863, -3.03233, 1.12988, 0.28223, 1.16438, -0.06613, 0.70418, -0.93496, 0.65508, 0.42852, 0.7486, 0.21458, 0.64886, -0.43599, 0.00928, -1.05954, 0.00925, -1.04253, -0.42947, -0.96865, 0.00866, -0.97736, 0.01102, -1.2582, 0.01098, -1.238, -0.51001, -1.15025, 0.00927, -1.05952, 0.00924, -1.04251, -0.42948, -0.96863]}]}}}}}}