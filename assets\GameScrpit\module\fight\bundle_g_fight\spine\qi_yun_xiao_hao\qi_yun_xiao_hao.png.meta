{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "acc07885-cbd3-4c0e-8482-1f3cc58af351", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "acc07885-cbd3-4c0e-8482-1f3cc58af351@6c48a", "displayName": "qi_yun_xiao_hao", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "acc07885-cbd3-4c0e-8482-1f3cc58af351", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "acc07885-cbd3-4c0e-8482-1f3cc58af351@f9941", "displayName": "qi_yun_xiao_hao", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -3, "trimX": 3, "trimY": 8, "width": 311, "height": 992, "rawWidth": 317, "rawHeight": 1002, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-155.5, -496, 0, 155.5, -496, 0, -155.5, 496, 0, 155.5, 496, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [3, 994, 314, 994, 3, 2, 314, 2], "nuv": [0.00946372239747634, 0.001996007984031936, 0.9905362776025236, 0.001996007984031936, 0.00946372239747634, 0.9920159680638723, 0.9905362776025236, 0.9920159680638723], "minPos": [-155.5, -496, 0], "maxPos": [155.5, 496, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "acc07885-cbd3-4c0e-8482-1f3cc58af351@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "acc07885-cbd3-4c0e-8482-1f3cc58af351@6c48a"}}