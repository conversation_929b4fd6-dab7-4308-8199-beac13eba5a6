import { _decorator, Component, Label, Node, ScrollView, Sprite } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { JsonMgr } from "../../mgr/JsonMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import ToolExt from "../../common/ToolExt";
import { CityModule } from "../../../module/city/CityModule";
import ResMgr from "../../../lib/common/ResMgr";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import Formate from "../../../lib/utils/Formate";
const { ccclass, property } = _decorator;

@ccclass("UICityRewardWorker")
export class UICityRewardWorker extends UINode {
  protected _openAct: boolean = true;
  protected _isSetParent: boolean = true;
  public zOrder(): number {
    return 100;
  }
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UICityRewardWorker`;
  }

  private _itemMainIndex = 0;

  private _intervalId: number = 0;

  protected onEvtShow(): void {
    this.initItemMain();
  }

  protected onEvtHide(): void {
    this.getNode("workerRewardScrollView").getComponent(ScrollView).scrollToTop(0, false);
  }

  protected onEvtClose(): void {
    TickerMgr.clearInterval(this._intervalId);
  }

  private initItemMain() {
    let workerRewardList = Object.keys(JsonMgr.instance.jsonList.c_buildWorkerReward).map(Number);
    let max = 5;
    this._intervalId = TickerMgr.setInterval(
      0.016,
      () => {
        let index = this._itemMainIndex;
        for (let i = index; i < index + max && i < workerRewardList.length; i++) {
          let node = ToolExt.clone(this["worker_reward_item"], this);
          this.getNode("workerContent").addChild(node);
          node.active = true;

          let config = CityModule.data.getConfigBuildWorkerRewardData(workerRewardList[i]);
          let isFinish: boolean = config.workerNum <= CityModule.data.cityTotalNum;

          node.getChildByPath("bg_wz_1/bg_moquan_lv").active = isFinish;
          ResMgr.loadSpriteFrame(
            `${BundleEnum.BUNDLE_G_MAJORCITY}?patterns/UICityLvReward`,
            isFinish ? "bg_wz_2" : "bg_wz_3",
            node.getChildByPath("bg_wz_1").getComponent(Sprite)
          );
          ResMgr.loadSpriteFrame(
            `${BundleEnum.BUNDLE_G_MAJORCITY}?patterns/UICityLvReward`,
            isFinish ? "bg_biaoqian_wzry2" : "bg_biaoqian_wzry1",
            node.getChildByPath("bg_wz_1/bg_9g_biaoqian_wzry1").getComponent(Sprite)
          );

          ResMgr.loadSpriteFrame(
            `${BundleEnum.BUNDLE_G_MAJORCITY}?patterns/UICityLvReward`,
            isFinish ? "img_biaoq1" : "img_biaoq2",
            node.getChildByPath("bar/img_biaoq1").getComponent(Sprite)
          );
          ResMgr.loadSpriteFrame(
            `${BundleEnum.BUNDLE_G_MAJORCITY}?patterns/UICityLvReward`,
            isFinish ? "bg_jindutiao_renshu2" : "bg_jindutiao_renshu1",
            node.getChildByPath("bar/bg_jindutiao_renshu1").getComponent(Sprite)
          );

          node.getChildByPath("bar/Label").getComponent(Label).string = `${config.id}`;
          ToolExt.setLabColor(node.getChildByPath("bar/Label").getComponent(Label), isFinish ? "975C2E" : "2A5287");
          node.getChildByPath("bg_wz_1/bg_9g_biaoqian_wzry1/task_lab").getComponent(Label).string = config.des.replace(
            "s%",
            `${Formate.format(config.workerNum)}`
          );

          for (let i = 0; i < config.attrAdd.length; i++) {
            let attributeConfig = JsonMgr.instance.jsonList.c_attribute[config.attrAdd[i][0]];
            node.getChildByPath(`bg_wz_1/award_lab_${i}`).getComponent(Label).string = `${i + 1}.${
              attributeConfig.name
            }+${config.attrAdd[i][1] / 100}%`;

            ToolExt.setLabColor(
              node.getChildByPath(`bg_wz_1/award_lab_${i}`).getComponent(Label),
              isFinish ? "583303" : "2A4A66"
            );
          }

          if (i == workerRewardList.length - 1) {
            node.getChildByPath("bar/bg_jindutiao_renshu1").active = false;
          }

          this._itemMainIndex++;
        }
        if (index >= workerRewardList.length) {
          TickerMgr.clearInterval(this._intervalId);
        }
      },
      true
    );
  }
}
