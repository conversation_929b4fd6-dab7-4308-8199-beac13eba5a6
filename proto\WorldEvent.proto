syntax = "proto3";
package sim;
import "Comm.proto";

// 
message EventRepelResponse {
  // 进度 当大于等于总进度时就是完成
  int32 progress = 1;
  // 奖励
  sim.RewardMessage rewardMessage = 2;
}

// 
message EventTrainMessage {
  // 事件集合
  map<int64,ThiefEventMessage> eventMap = 1;
  // 最近一次触发循环任务的时间戳
  int64 lastUpdateTime = 2;
}

// 
message ThiefEventMessage {
  // 当前进度
  int32 progress = 1;
  // 总进度
  int32 totalProgress = 2;
  // 完成时的奖励
  repeated double rewardList = 3;
}

