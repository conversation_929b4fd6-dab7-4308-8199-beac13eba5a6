import { _decorator, Node } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { AudioMgr, AudioName } from "../../platform/src/AudioHelper";
import { v3 } from "cc";
import { sp } from "cc";
import { tween } from "cc";
import { Label } from "cc";
import { CityModule } from "../module/city/CityModule";
import Formate from "../lib/utils/Formate";
import MsgEnum from "../game/event/MsgEnum";
import MsgMgr from "../lib/event/MsgMgr";
import { TipsMgr } from "../../platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("TopEnergyUpgradeRes")
export class TopEnergyUpgradeRes extends BaseCtrl {
  @property(Node)
  nodeBg: Node;

  @property(Node)
  nodeTytanchuang: Node;

  @property(Node)
  nodeLayout: Node;

  @property(Label)
  lblEnergyLevel: Label;

  private isAniFinish = false;

  private config_buildCrystal: any = null;
  private config_buildCrystal_next: any = null;

  effectMap = [
    {
      key: "reward",
      name: "点击收益",
    },
    {
      key: "rateAdd",
      name: "据点加成",
    },
    {
      key: "time",
      name: "自动点击次数",
    },
  ];

  start() {
    super.start();
    TipsMgr.setEnableTouch(false, 0.2);
    this.playeAction();
    this.setEnergyEffect();
    AudioMgr.instance.playEffect(AudioName.Effect.女娲升级成功);
  }

  private playeAction() {
    this.nodeBg.scale = v3(0, 0, 0);
    this.nodeTytanchuang.getComponent(sp.Skeleton).setCompleteListener(() => {
      this.nodeTytanchuang.getComponent(sp.Skeleton).setAnimation(0, "zi_shengjichenggong_1", true);
    });

    this.nodeTytanchuang.getComponent(sp.Skeleton).setAnimation(0, "zi_shengjichenggong", false);

    tween(this.nodeBg)
      .to(0.1, { scale: v3(1, 1, 1) })
      .call(() => {
        this.lblEnergyLevel.string = `${CityModule.data.energyFactoryMsg.level}`;
      })
      .start();

    tween(this.node)
      .delay(0.5)
      .call(() => {
        this.isAniFinish = true;
      })
      .start();
  }

  private setEnergyEffect() {
    this.config_buildCrystal = CityModule.data.getConfigBuildCrystal(CityModule.data.energyFactoryMsg.level - 1);
    this.config_buildCrystal_next = CityModule.data.getConfigBuildCrystal(CityModule.data.energyFactoryMsg.level);

    this.nodeLayout.children.forEach((nodeChild, index) => {
      let num1 = this.config_buildCrystal[this.effectMap[index].key];
      let num2 = this.config_buildCrystal_next[this.effectMap[index].key];
      let str1 = "";
      let str2 = "";

      switch (this.effectMap[index].key) {
        case "reward":
          str1 = Formate.format(num1);
          str2 = Formate.format(num2);
          break;
        case "rateAdd":
          str1 = `${num1 / 100}%`;
          str2 = `${num2 / 100}%`;
          break;
        case "time":
          str1 = `${Formate.format(num1)} 次/ 秒`;
          str2 = `${Formate.format(num2)} 次/ 秒`;
          break;
        default:
          break;
      }

      let lblTitle = nodeChild.getChildByPath("title_node/title_lab");
      lblTitle.getComponent(Label).string = `${this.effectMap[index].name}`;

      let lblNum1 = nodeChild.getChildByPath("num1_node/num1_lab");
      lblNum1.getComponent(Label).string = str1;

      let lblNum2 = nodeChild.getChildByPath("num2_node/num2_lab");
      lblNum2.getComponent(Label).string = str2;
    });
  }

  onBtnClose() {
    if (!this.isAniFinish) {
      return;
    }

    // 消息用于监听引导
    MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "REWARD_END");
    this.closeBack();
  }
}
