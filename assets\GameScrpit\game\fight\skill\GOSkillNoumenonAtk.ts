import { _decorator, sp } from "cc";
import GOSkill from "./GOSkill";
import { CallBulletDetail, CallSkillDetail } from "../FightDefine";
import RenderSection from "../../../lib/object/RenderSection";
import { JsonMgr } from "../../mgr/JsonMgr";
import FightManager from "../manager/FightManager";
import BulletManager from "../manager/BulletManager";
const { ccclass, property } = _decorator;

@ccclass("GOSkillNoumenonAtk")
export class GOSkillNoumenonAtk extends GOSkill {
  protected async onEnter(detail: CallSkillDetail) {
    super.onEnter(detail);
  }

  protected doBullet() {
    super.doBullet();
    let skilldb = JsonMgr.instance.jsonList.c_skillShow[this._detail.skillId];
    let bulletDetail: CallBulletDetail = {
      goSkill: this,
      actionType: this._detail.actionType,
      resolveBack: this._detail.resolveBack,
      movementInfo: this._detail.movementInfo,
      bulletId: skilldb.bulletId,
      skillId: this._detail.skillId,
      target: this._detail.target,
      src: this._detail.src,
    };

    FightManager.instance.getSection(BulletManager).doCallObject(bulletDetail);
    this.remove();
  }
}

//noumenon
