import { _decorator, Component, instantiate, Node, sp } from "cc";
import { BundleEnum } from "../GameScrpit/game/bundleEnum/BundleEnum";
import ResMgr from "../GameScrpit/lib/common/ResMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("test_role")
export class test_role extends Component {
  start() {
    this.checkRole();
    this.checkSkill();
  }

  update(deltaTime: number) {}

  async checkSkill() {
    let res = await ResMgr.getBundleSync(BundleEnum.RESOURCES);

    res.loadDir("prefab/bullet", async (error: any, items: any) => {
      log.log(items);
      for (let i = 0; i < items.length; i++) {
        let node = instantiate(items[i]);
        this.node.getChildByName("bullet").addChild(node);
        node.walk((val) => {
          val.layer = this.node.layer;
        });
        let skt = node.getChildByName("render").getComponent(sp.Skeleton);
        skt.timeScale = 2;

        let animations = skt.skeletonData.skeletonJson.animations;
        for (let j in animations) {
          await new Promise((resolve, reject) => {
            let name = j;
            skt.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
              //清空监听
              if (name == trackEntry.animation.name) {
                skt.getComponent(sp.Skeleton).setCompleteListener(null);
                resolve(true);
              }
            });

            skt.setEventListener((animation, event) => {
              if ("animation" == animation.animation.name) {
                if (event["data"].name === "atk") {
                  skt.setEventListener(null);
                } else {
                  log.error(node.name, "技能表现动画中没有hurt事件");
                }
              }
            });

            skt.getComponent(sp.Skeleton).setAnimation(0, name, false);
          });
        }

        node.destroy();
      }
    });
  }

  /**角色资源检查 */
  async checkRole() {
    let res = await ResMgr.getBundleSync(BundleEnum.RESOURCES);

    res.loadDir("prefab/role", async (error: any, items: any) => {
      log.log(items);
      for (let i = 0; i < items.length; i++) {
        let node = instantiate(items[i]);
        this.node.getChildByName("role").addChild(node);
        node.walk((val) => {
          val.layer = this.node.layer;
        });
        let skt = node.getChildByName("render").getComponent(sp.Skeleton);
        skt.timeScale = 3;
        let animations = skt.skeletonData.skeletonJson.animations;
        for (let j in animations) {
          await new Promise((resolve, reject) => {
            let name = j;
            skt.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
              //清空监听
              if (name == trackEntry.animation.name) {
                skt.getComponent(sp.Skeleton).setCompleteListener(null);
                resolve(true);
              }
            });

            skt.setEventListener((animation, event) => {
              if (
                "boss_attack1" == animation.animation.name ||
                "boss_attack2" == animation.animation.name ||
                "boss_attack3" == animation.animation.name
              ) {
                if (event["data"].name === "atk") {
                  skt.setEventListener(null);
                } else {
                  if (node.name == "a_nezha" && "boss_attack3" == animation.animation.name) {
                    log.warn(node.name + "的" + animation.animation.name + "是特殊的动画，不需要事件" + "属于正常的");
                  } else {
                    log.error(node.name, "角色攻击动画中没有atk事件");
                  }
                }
              }
            });

            skt.getComponent(sp.Skeleton).setAnimation(0, name, false);
          });
        }

        node.destroy();
      }
    });
  }
}
