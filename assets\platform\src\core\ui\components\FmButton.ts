import { _decorator, CCBoolean, CCInteger, CCString, Component, Label, Node, UITransform } from "cc";
import { MessageComponent } from "./MessageComponent";
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass("FmButton")
@executeInEditMode
export class FmButton extends Component {
  @property({ serializable: true })
  private enableNode: Node = null;
  @property({ serializable: true })
  private disableNode: Node = null;
  @property({ serializable: true })
  private selectedNode: Node = null;

  @property({ type: CCInteger, serializable: true })
  private _messageKey: number = 0; // 消息ID
  @property({ serializable: true })
  private _args: string[] = [];
  @property({ serializable: true })
  private _selected: boolean = false; // 新增选中状态属性
  @property({ serializable: true })
  private _btnEnable: boolean = true; // 新增选中状态属性

  @property({ type: CCString, displayName: "参数", tooltip: "参数" })
  public set args(val: string[]) {
    this._args = val;
    this.updateLabelMessage();
  }
  public get args() {
    return this._args;
  }

  @property({ type: CCInteger, displayName: "消息ID", tooltip: "消息ID", step: 1 })
  set messageKey(value: number) {
    this._messageKey = value;
    this.updateLabelMessage();
  }
  get messageKey() {
    return this._messageKey;
  }

  @property({ type: CCBoolean, displayName: "Enable", tooltip: "是否允许使用" })
  set btnEnable(value: boolean) {
    if (!this.enableNode || !this.disableNode) {
      console.error("未设置 enableNode或disableNode");
      return;
    }
    this._btnEnable = value;
    this.enableNode.active = value;
    this.disableNode.active = !value;
    // 当按钮禁用时，强制取消选中状态
    if (!value) {
      this.selected = false;
    }
    this.selectedNode.active = false;
    this.enableNode.active = value;
    // 当按钮启用时，若未选中则显示普通启用节点
    // if (this.selectedNode && value && this.selected) {
    //   this.selectedNode.active = true;
    //   this.enableNode.active = false;
    // } else {

    // }
  }
  get btnEnable() {
    return this._btnEnable;
  }

  @property({ type: CCBoolean, displayName: "选中", tooltip: "选中" })
  set selected(value: boolean) {
    // 仅在按钮启用时允许修改选中状态
    if (value && (!this.enableNode || !this._btnEnable)) {
      this.btnEnable = true;
    }
    if (!this.selectedNode) {
      console.error("未设置 selectedNode");
      this._selected = false;
      return;
    }
    this._selected = value; // 需补充 _selected 属性声明
    this.selectedNode.active = value;
    // 选中时隐藏普通启用节点，未选中时显示
    this.enableNode.active = !value;
  }
  get selected() {
    return this._selected;
  }
  private updateLabelMessage() {
    if (this._messageKey == 0) {
      return;
    }
    this.node.getComponentsInChildren(MessageComponent).forEach((component) => {
      component.args = this._args;
    });
    this.node.getComponentsInChildren(MessageComponent).forEach((component) => {
      component.messageKey = this._messageKey;
    });
  }
  set string(val: string) {
    this.node.getComponentsInChildren(Label).forEach((component) => {
      component.string = val;
    });
  }

  onFocusInEditor(): void {
    this.updateLabelMessage();
  }
  start() {
    if (!this.enableNode) {
      this.enableNode = new Node("node_enable"); // 初始化 enableNode
      this.enableNode.parent = this.node;
      this.enableNode.layer = this.node.layer;
      this.enableNode.addComponent(UITransform);
      let label = new Node("label");
      label.parent = this.enableNode;
      label.layer = this.node.layer;
      label.addComponent(MessageComponent);
      // label.getComponent(MessageComponent).string = this._string;
    }
    if (!this.disableNode) {
      this.disableNode = new Node("node_disable"); // 初始化 disableNode
      this.disableNode.parent = this.node;
      this.disableNode.layer = this.node.layer;
      this.disableNode.addComponent(UITransform);
      let label = new Node("label");
      label.parent = this.disableNode;
      label.layer = this.node.layer;
      label.addComponent(MessageComponent);
    }
    if (!this.selectedNode) {
      this.selectedNode = new Node("node_selected"); // 初始化 selectedNode
      this.selectedNode.parent = this.node;
      this.selectedNode.layer = this.node.layer;
      this.selectedNode.addComponent(UITransform);
      let label = new Node("label");
      label.parent = this.selectedNode;
      label.layer = this.node.layer;
      label.addComponent(MessageComponent);
    }
    // this.btnEnable = true;
  }

  update(deltaTime: number) {}
}
