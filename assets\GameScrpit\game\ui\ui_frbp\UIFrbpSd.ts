import { _decorator, is<PERSON>alid, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import ToolExt from "../../common/ToolExt";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { Sleep } from "../../GameDefine";
import { dtTime } from "../../BoutStartUp";
import FmUtils from "../../../lib/utils/FmUtils";
import { activityId, FrbpModule } from "../../../module/frbp/FrbpModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PublicRouteName } from "../../../module/player/PlayerConstant";
import TipMgr from "../../../lib/tips/TipMgr";
import { RedeemRequest, RedeemResponse } from "../../net/protocol/Activity";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { BuyConfirm } from "../UIBuyConfirm";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { RedeemPackVO } from "../../../module/activity/ActivityConfig";
const { ccclass, property } = _decorator;
const db_info: string = "db_info";
@ccclass("UIFrbpSd")
export class UIFrbpSd extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FRBP}?prefab/ui/UIFrbpSd`;
  }

  private _vo: any;
  private _needItemId: number = null;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setMyItemNum, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setMyItemNum, this);
  }

  public init(args: any): void {
    super.init(args);
    this._vo = args.vo;
  }

  protected async onEvtShow() {
    this._vo = await FrbpModule.data.getVO(
      activityId,
      () => {
        TipsMgr.setEnableTouch(true);
      },
      () => {
        UIMgr.instance.back();
        TipsMgr.setEnableTouch(true);
      }
    );
    TipsMgr.setEnableTouch(true);

    let list = this._vo.redeemList[1];
    this._needItemId = list[0].cost[0];
    this.setTime();
    this.setMyItemIcon();
    this.setMyItemNum();

    this.loadLb();
  }

  private setTime() {
    let startTime = TimeUtils.formatTimestamp(this._vo.startTime, "YYYY/MM/DD");
    let publicityTime = TimeUtils.formatTimestamp(this._vo.publicityTime, "YYYY/MM/DD");
    this.getNode("title_time").getComponent(Label).string = "活动时间:" + startTime + "--" + publicityTime;
  }

  private setMyItemIcon() {
    ToolExt.setItemIcon(this.getNode("item_title"), this._needItemId);
  }
  private setMyItemNum() {
    this.getNode("myItenm").getComponent(Label).string = PlayerModule.data.getItemNum(this._needItemId) + "";
  }

  private async loadLb() {
    let list = this._vo.redeemList[1];
    for (let i = 0; i < list.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) {
        return;
      }
      let node = this.getNode("sd_content").children[i];
      if (!node) {
        node = ToolExt.clone(this.getNode("sd_item"), this);
        this.getNode("sd_content").addChild(node);
        node.active = true;
      }

      if (i % 3 == 0) {
        node["bg_sdtc_neidi_xing"].active = false;
      } else {
        node["bg_sdtc_neidi_xing"].active = true;
      }

      let info = list[i];
      let rewardList = info.rewardList;
      FmUtils.setItemNode(node["Item"], rewardList[0], rewardList[1]);
      let redem_num = FrbpModule.data.ProsperityMessage.redeemMap[info.id] || 0;
      node["lbl_buy_max"].getComponent(Label).string =
        ToolExt.getMaxtypeLab(info.maxtype) + `(${info.max - redem_num}/${info.max})`;

      if (redem_num >= info.max) {
        node["btn_goumai"].active = false;
        node["sd_bg_yishouqing"].active = true;
      } else {
        node["btn_goumai"].active = true;
        node["sd_bg_yishouqing"].active = false;
      }

      if (info.maxtype == 4 && PlayerModule.service.isShopBuy(info.rewardList[0]) == false) {
        node["btn_goumai"].active = false;
        node["sd_bg_yishouqing"].active = true;
      }

      node["btn_goumai"][db_info] = info;

      let unlockList = info.unlockList;
      if (unlockList.length < 2) {
        unlockList = [0, 0];
      }
      let limitMap = FrbpModule.data.ProsperityMessage.limitMap;
      let unNum = limitMap[unlockList[0]] || 0;
      if (unNum >= unlockList[1]) {
        node["unlock_mask"].active = false;
      } else {
        node["unlock_mask"].active = true;
        let un_itemdb = JsonMgr.instance.getConfigItem(unlockList[0]);
        node["lbl_unlock"].getComponent(Label).string = "累计获得\n" + unlockList[1] + `${un_itemdb.name}\n` + "解锁";
      }

      let cost = info.cost;
      ToolExt.setItemIcon(node["but_item_icon"], cost[0]);
      node["buy_need_num"].getComponent(Label).string = cost[1];
    }
  }

  private on_click_btn_goumai(event) {
    AudioMgr.instance.playEffect(2010);
    let node: Node = event.node;
    let info: RedeemPackVO = node[db_info];

    let id = info.cost[0];
    let num = PlayerModule.data.getItemNum(id);
    if (num < info.cost[1]) {
      let itemdb = JsonMgr.instance.getConfigItem(id);
      TipsMgr.showTipX(112, [itemdb.name], "");
      //UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: id });
      return;
    }

    if (info.maxtype == 4 && PlayerModule.service.isShopBuy(info.rewardList[0]) == false) {
      let item = JsonMgr.instance.getConfigItem(id);
      TipMgr.showTip(`已拥有${item.name}，无法重复获得`);
      return;
    }

    let redem_num = FrbpModule.data.ProsperityMessage.redeemMap[info.id] || 0;
    let numLimit = info.max - redem_num;

    const buyConfirm: BuyConfirm = {
      itemInfo: info.rewardList,
      moneyInfo: info.cost,
      maxNum: numLimit,
    };

    UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
      if (resp.ok) {
        if (info.price == 0) {
          AudioMgr.instance.playEffect(2011);
          let param: RedeemRequest = {
            activityId: this._vo.id,
            redeemId: info.id,
            count: resp.num,
          };

          FrbpModule.api.buyFixedPack(param, (res: RedeemResponse) => {
            let rewardList = res.rewardList;
            MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
            this.loadLb();
          });
          return;
        }
      }
    });
  }
}
