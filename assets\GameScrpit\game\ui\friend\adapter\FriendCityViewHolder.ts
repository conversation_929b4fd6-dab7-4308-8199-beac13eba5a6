import { _decorator, Label, Node, sp, Sprite } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { FriendModule } from "../../../../module/friend/FriendModule";
import { times } from "../../../../lib/utils/NumbersUtils";
import { FriendCityExpandViewHolder } from "./FriendCityExpandViewHolder";
import ResMgr from "../../../../lib/common/ResMgr";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { HeroTypeIcon } from "db://assets/GameScrpit/module/hero/HeroConstant";
import { FriendCitySkillMessage } from "../../../net/protocol/Friend";
import TipMgr from "db://assets/GameScrpit/lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import FlashLight from "../../../common/effect_ctrl/FlashLight";
const { ccclass, property } = _decorator;
@ccclass("FriendCityViewHolder")
export class FriendCityViewHolder extends ViewHolder {
  @property(Sprite)
  private background: Sprite;
  @property(Sprite)
  private skillImg: Sprite;
  @property(Label)
  private addValue: Label;
  @property(Node)
  private select: Node;
  @property(Node)
  private xilianTx: Node;

  private _friendId: number;
  // private _skillIndex: number;
  private _lastSkillAdd: number = 0;

  public setSelect(select: boolean, expand: Node): boolean {
    if (!this.select) {
      TipMgr.showTip("因果值不足");

      return;
    }
    if (select) {
      this.select.active = true;
      expand.getComponent(FriendCityExpandViewHolder).updateData(this._friendId, this.position);
    } else {
      this.select.active = false;
    }
    return true;
  }
  public init(): void {
    this.node.on(Node.EventType.TOUCH_END, this.onClick, this);
  }
  private onClick() {
    AudioMgr.instance.playEffect(1067);
  }
  public updateData(friendId: number, position: number) {
    this.position = position;
    this._friendId = friendId;
    // this._friendId = args[0];
    // this._skillIndex = args[1];

    let skillId = position + 1;
    let friendMessage = FriendModule.data.getFriendMessage(this._friendId);
    if (skillId > friendMessage.citySkillList.length) {
      let friendlyNeed = JsonMgr.instance.jsonList.c_friendSkill[skillId].friendlyNeed;
      this.node.getChildByName("lbl_next_value").getComponent(Label).string = `${friendlyNeed}`;
      return;
    }
    FriendModule.service.registerFriendSkillCitySkillBadge(this.node, friendId, position);

    let skill: FriendCitySkillMessage = friendMessage.citySkillList[position];
    let friendSkill = FriendModule.config.getFriendCitySkillById(skillId);
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_UI,
      `atlas_imgs/${HeroTypeIcon[`type_${friendSkill.type}`]}`,
      this.skillImg
    );
    this.addValue.string = `+${times(skill.skillAdd, 100)}%`;
    if (skill.skillAdd == 0.3) {
      this.skillImg.getComponent(FlashLight).startFlash(true);
    } else {
      this.skillImg.getComponent(FlashLight).stopFlash();
    }
    if (this.select.active && skill.skillAdd > this._lastSkillAdd) {
      this.xilianTx.active = true;
      this.xilianTx.getComponent(sp.Skeleton).setAnimation(0, "action", false);
    } else {
      this.xilianTx.active = false;
    }
    this._lastSkillAdd = skill.skillAdd;
  }
}
