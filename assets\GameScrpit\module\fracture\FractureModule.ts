import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { FractureApi } from "./FractureApi";
import { FractureConfig } from "./FractureConfig";
import { FractureData } from "./FractureData";
import { FractureRoute } from "./FractureRoute";
import { FractureService } from "./FractureService";
import { FractureSubscriber } from "./FractureSubscriber";
import { FractureViewModel } from "./FractureViewModel";

export class FractureModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): FractureModule {
    if (!GameData.instance.FractureModule) {
      GameData.instance.FractureModule = new FractureModule();
    }
    return GameData.instance.FractureModule;
  }
  private _data = new FractureData();
  private _api = new FractureApi();
  private _service = new FractureService();
  private _subscriber = new FractureSubscriber();
  private _route = new FractureRoute();
  private _viewModel = new FractureViewModel();
  private _config = new FractureConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new FractureData();
    this._api = new FractureApi();
    this._service = new FractureService();
    this._subscriber = new FractureSubscriber();
    this._route = new FractureRoute();
    this._viewModel = new FractureViewModel();
    this._config = new FractureConfig();

    // 模块初始化
    this._subscriber.register();
    this._route.init();
    this._api.fractureInfo();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
