{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "0d8d1ec6-6d7c-4cdf-a07b-726e7f841efa", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "0d8d1ec6-6d7c-4cdf-a07b-726e7f841efa@6c48a", "displayName": "bg_9g_sklx_huang", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "0d8d1ec6-6d7c-4cdf-a07b-726e7f841efa", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "0d8d1ec6-6d7c-4cdf-a07b-726e7f841efa@f9941", "displayName": "bg_9g_sklx_huang", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 80, "height": 80, "rawWidth": 80, "rawHeight": 80, "borderTop": 29, "borderBottom": 30, "borderLeft": 30, "borderRight": 29, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-40, -40, 0, 40, -40, 0, -40, 40, 0, 40, 40, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 80, 80, 80, 0, 0, 80, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-40, -40, 0], "maxPos": [40, 40, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "0d8d1ec6-6d7c-4cdf-a07b-726e7f841efa@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "0d8d1ec6-6d7c-4cdf-a07b-726e7f841efa@6c48a"}}