import { _decorator, is<PERSON>alid, Label, Node, Sprite, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ToolExt, { Lab_Color_Enum } from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import Formate from "../../../lib/utils/Formate";
import MsgEnum from "../../event/MsgEnum";
import MsgMgr from "../../../lib/event/MsgMgr";
import ResMgr from "../../../lib/common/ResMgr";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import FmUtils from "../../../lib/utils/FmUtils";
import { SHOP_UNLOCK_TYPE, Sleep } from "../../GameDefine";
import { ClubModule } from "../../../module/club/ClubModule";
import { HeroModule } from "../../../module/hero/HeroModule";
import { PetModule } from "../../../module/pet/PetModule";
import { BuyConfirm } from "../UIBuyConfirm";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { dtTime } from "../../BoutStartUp";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HuntAudioName } from "../../../module/hunt/HuntConfig";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";
const { ccclass, property } = _decorator;

enum ShopItemType {
  DAYSHOP = 1,
  PETSHOP = 2,
}

const ItemId: number = 1085;

const item1073: number = 1073;
const item1071: number = 1071;

@ccclass("UIHuntShop")
export class UIHuntShop extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntShop`;
  }

  private _curMainIndex: ShopItemType = ShopItemType.DAYSHOP;

  private _dayShop_dataList = null;

  private _petShop_dataList = null;

  private _petShop_srotList = null;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setHuntMoneryLab, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setHuntMoneryLab, this);
  }

  protected onEvtShow(): void {
    GoodsModule.api.buyInfo((data) => {
      this._dayShop_dataList = GoodsModule.data.getShopItemList(3);
      this._petShop_dataList = GoodsModule.data.getShopItemList(4);

      this.sortPetShop();
      this.changeMain();

      this.loadDayShop();
      this.loadPetShop();
    });
  }

  private sortPetShop() {
    let c_shop = JsonMgr.instance.jsonList.c_shop;

    let arr1 = [];
    let arr2 = [];
    let arr3 = []; //已解锁，可兑换
    let arr4 = []; //未解锁
    let arr5 = []; //已兑换

    for (let i = 0; i < this._petShop_dataList.length; i++) {
      let info1 = c_shop[this._petShop_dataList[i]];
      let info2 = JsonMgr.instance.getConfigItem(info1.itemsList[0][0]);

      if (info2.id == item1073) {
        arr1.push(info1);
      } else if (info2.id == item1071) {
        arr2.push(info1);
      } else {
        let bool = this.unlockIs(info1);
        if (bool == false) {
          arr4.push(info1);
        } else {
          if (info1.max - GoodsModule.data.hasBuyCount(info1.id) <= 0 && info1.maxtype != 0) {
            arr5.push(info1);
          } else {
            arr3.push(info1);
          }
        }
      }
    }

    this._petShop_srotList = arr1.concat(arr2, arr3, arr4, arr5);
  }

  private setHuntMoneryLab() {
    let c_shop = JsonMgr.instance.jsonList.c_shop;
    if (this._curMainIndex == ShopItemType.DAYSHOP) {
      let info1 = c_shop[this._dayShop_dataList[0]];
      this.getNode("myItenm").getComponent(Label).string = Formate.format(PlayerModule.data.getItemNum(info1.cointype));
    } else {
      let info1 = c_shop[this._petShop_dataList[0]];
      this.getNode("myItenm").getComponent(Label).string = Formate.format(PlayerModule.data.getItemNum(info1.cointype));
    }
  }

  private async loadDayShop() {
    let c_shop = JsonMgr.instance.jsonList.c_shop;

    for (let i = 0; i < this._dayShop_dataList.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) {
        return;
      }
      let node = ToolExt.clone(this.getNode("day_item"), this);
      node.setPosition(v3(0, 0, 0));
      this.getNode("dayContent").addChild(node);
      node.active = true;
      // if (i % 3 == 0) {
      //   //node["bg_sdtc_neidi_xing"].active = false;
      // }

      let info1 = c_shop[this._dayShop_dataList[i]];
      let info2 = JsonMgr.instance.getConfigItem(info1.itemsList[0][0]);

      // node["itemName"].getComponent(Label).string = info2.name;
      // ToolExt.setLabQualityColor(node["itemName"].getComponent(Label), info2.color, Lab_Color_Enum.BRIGHT_CLOR);
      node["btn_item"]["itemId"] = info2.id;
      FmUtils.setItemNode(node["btn_item"], info2.id, info1.itemsList[0][1]);

      let itemInfo = JsonMgr.instance.getConfigItem(info1.cointype);
      ToolExt.setItemIcon(node["needIcon"], itemInfo.id, this);
      node["needNum"].getComponent(Label).string = Formate.format(info1.coinPrice);

      this.changeStateDayItemState(node, this._dayShop_dataList[i]);
      node["btn_day_buy"]["shopId"] = this._dayShop_dataList[i];
    }
  }

  private async loadPetShop() {
    for (let i = 0; i < this._petShop_srotList.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) {
        return;
      }
      let node = ToolExt.clone(this.getNode("day_item"), this);
      this.getNode("petContent").addChild(node);
      node.active = true;

      let info1 = this._petShop_srotList[i];
      let info2 = JsonMgr.instance.getConfigItem(info1.itemsList[0][0]);
      if (info2.goodsType == 2) {
        FmUtils.setItemNode(node["btn_item"], info2.id, info1.itemsList[0][1]);
        this.petShopItem(info2, node);
      } else if (info2.goodsType == 5) {
        FmUtils.setItemNode(node["btn_item"], info2.id, info1.itemsList[0][1]);
        this.petShopPet(info2, node);
      }

      node["btn_item"]["itemId"] = info2.id;
      this.changeStateDayItemState(node, info1.id);

      let itemInfo = JsonMgr.instance.getConfigItem(info1.cointype);
      ToolExt.setItemIcon(node["needIcon"], itemInfo.id, this);
      node["needNum"].getComponent(Label).string = Formate.format(info1.coinPrice);
      node["btn_day_buy"]["shopId"] = info1.id;
    }
  }

  /**灵兽商店是道具 */
  private petShopItem(db: any, node: Node) {
    //node["itemName"].getComponent(Label).string = db.name;
  }

  /**灵兽商店是灵兽 */
  private petShopPet(db: any, node: Node) {
    let c_pet = JsonMgr.instance.jsonList.c_pet;
    let petdb = c_pet[db.id];
    node["lbl_name"].getComponent(Label).string = petdb.name;
  }

  private unlockIs(shopdb: any) {
    if (shopdb.unlockType == SHOP_UNLOCK_TYPE.无条件) {
      return true;
    }

    switch (shopdb.unlockType) {
      case SHOP_UNLOCK_TYPE.仙盟等级:
        if (shopdb.unlockNum <= ClubModule.data.level) {
          return true;
        }
        break;

      case SHOP_UNLOCK_TYPE.对应战将:
        if (HeroModule.data.getHeroMessage(shopdb.unlockNum)) {
          return true;
        }
        break;
      case SHOP_UNLOCK_TYPE.拥有灵兽数量:
        if (PetModule.data.getPetNum() >= shopdb.unlockNum) {
          return true;
        }
    }

    return false;
  }

  private changeStateDayItemState(node: Node, id: number) {
    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let info1 = c_shop[id];

    let bool = this.unlockIs(info1);

    if (bool == false) {
      node["spr_noon"].active = false;
      node["btn_day_buy"].active = false;
      node["lab_purchase"].active = false;
      node["unLockLab"].active = true;
      node["unMask"].active = true;
      node["unLockLab"].getComponent(Label).string = ToolExt.getShopUnlockTypeLab(info1.unlockType, info1.unlockNum);
      return;
    } else {
      node["unLockLab"].active = false;
      node["unMask"].active = false;
    }

    node["lab_purchase"].getComponent(Label).string = ToolExt.getMaxtypeLab(info1.maxtype);

    if (info1.maxtype != 0) {
      let count = info1.max - GoodsModule.data.hasBuyCount(id);
      node["lab_purchase"].getComponent(Label).string += `(${count}/${info1.max})`;
    }

    if (info1.max - GoodsModule.data.hasBuyCount(id) <= 0 && info1.maxtype != 0) {
      node["spr_noon"].active = true;
      node["btn_day_buy"].active = false;
      node["lab_purchase"].active = false;
    } else {
      node["spr_noon"].active = false;
      node["btn_day_buy"].active = true;
      node["lab_purchase"].active = true;
    }

    if (info1.maxtype == 4 && !PlayerModule.service.isShopBuy(info1.itemsList[0][0])) {
      node["spr_noon"].active = true;
      node["btn_day_buy"].active = false;
      node["lab_purchase"].active = false;
    }
  }

  private on_click_btn_day_buy(event) {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.商店点击购买道具);
    let shopId = event.node["shopId"];
    let info = JsonMgr.instance.jsonList.c_shop[shopId];

    if (info.maxtype == 4 && !PlayerModule.service.isShopBuy(info.itemsList[0][0])) {
      TipMgr.showTip("已拥有该物品，无法重复获得");
      return;
    }

    let my = PlayerModule.data.getItemNum(info.cointype);
    let need = info.coinPrice;
    if (my < need) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: info.cointype,
      });
      return;
    }

    let numLimit = info.max - GoodsModule.data.hasBuyCount(info.id);
    const buyConfirm: BuyConfirm = {
      itemInfo: info.itemsList[0],
      moneyInfo: [info.cointype, info.coinPrice],
      maxNum: numLimit,
    };

    UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
      if (resp.ok) {
        GoodsModule.api.redeemGoods(info.id, resp.num, (resp) => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: resp });
          this.changeStateDayItemState(event.node.parent, shopId);
        });
      }
    });
  }
  private on_click_btn_day_shop() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击商店里的页签);
    if (this._curMainIndex == ShopItemType.DAYSHOP) {
      return;
    }
    this._curMainIndex = ShopItemType.DAYSHOP;
    this.changeMain();
  }

  private on_click_btn_pet_shop() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击商店里的页签);
    if (this._curMainIndex == ShopItemType.PETSHOP) {
      return;
    }
    this._curMainIndex = ShopItemType.PETSHOP;
    this.changeMain();
  }

  private getNeedInfo() {
    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let info1 = c_shop[this._dayShop_dataList[0]];
    if (this._curMainIndex == ShopItemType.PETSHOP) {
      info1 = c_shop[this._petShop_dataList[0]];
    }

    let itemInfo = JsonMgr.instance.getConfigItem(info1.cointype);
    return itemInfo;
  }

  private changeMain() {
    let itemInfo = this.getNeedInfo();
    ToolExt.setItemIcon(this.getNode("my_item_spr"), itemInfo.id, this);
    this.setHuntMoneryLab();
    if (this._curMainIndex == ShopItemType.DAYSHOP) {
      this.getNode("dayScroll").active = true;
      this.getNode("petScroll").active = false;
      this.getNode("btn_pet_shop").getComponent(FmButton).selected = false;
      this.getNode("btn_day_shop").getComponent(FmButton).selected = true;
      return;
    }
    if (this._curMainIndex == ShopItemType.PETSHOP) {
      this.getNode("dayScroll").active = false;
      this.getNode("petScroll").active = true;
      this.getNode("btn_pet_shop").getComponent(FmButton).selected = true;
      this.getNode("btn_day_shop").getComponent(FmButton).selected = false;
      return;
    }
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {}
}
