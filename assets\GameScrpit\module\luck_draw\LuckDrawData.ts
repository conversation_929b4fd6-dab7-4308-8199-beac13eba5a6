import { LuckTrainMessage, LuckTrainResponse } from "../../game/net/protocol/LuckDraw";
import MsgMgr from "../../lib/event/MsgMgr";
import { LuckDrawMsgEnum } from "./LuckDrawConfig";

export class LuckDrawData {
  private _message: LuckTrainResponse = null;

  public get message() {
    return this._message;
  }

  public set message(val: LuckTrainResponse) {
    this._message = val;
    MsgMgr.emit(LuckDrawMsgEnum.UPDATE_LUCK_DRAW_DATA);
  }

  public get train() {
    return this._message.train;
  }

  public set train(val: LuckTrainMessage) {
    this._message.train = val;
    MsgMgr.emit(LuckDrawMsgEnum.UPDATE_LUCK_DRAW_DATA);
  }

  public get trainCount() {
    if (this._message) {
      return this._message?.train.count;
    }
    return 0;
    //return this._message?.train.count;
  }

  public set trainCount(val) {
    this._message.train.count = val;
  }

  public get recordList() {
    return this._message.recordList;
  }

  public set recordList(val) {
    this._message.recordList = val;
    MsgMgr.emit(LuckDrawMsgEnum.UPDATE_LUCK_DRAW_RECORD);
  }
}
