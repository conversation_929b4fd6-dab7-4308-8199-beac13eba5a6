import { GameData } from "../../../game/GameData";
import data from "../../../lib/data/data";
import { EventActionApi } from "./EventActionApi";
import { EventActionConfig } from "./EventActionConfig";
import { EventActionData } from "./EventActionData";
import { EventActionService } from "./EventActionService";
import { EventActionSubscriber } from "./EventActionSubscriber";
import { EventActionViewModel } from "./EventActionViewModel";

export class EventActionModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): EventActionModule {
    if (!GameData.instance.EventActionModule) {
      GameData.instance.EventActionModule = new EventActionModule();
    }
    return GameData.instance.EventActionModule;
  }
  private _data = new EventActionData();
  private _api = new EventActionApi();
  private _service = new EventActionService();
  private _subscriber = new EventActionSubscriber();
  private _viewModel = new EventActionViewModel();
  private _config = new EventActionConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new EventActionData();
    this._api = new EventActionApi();
    this._service = new EventActionService();
    this._subscriber = new EventActionSubscriber();
    this._viewModel = new EventActionViewModel();
    this._config = new EventActionConfig();

    // 模块初始化
    this._subscriber.register();

    EventActionModule.api.getEventInfo();

    EventActionModule.service.destroy();
    EventActionModule.service.init();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
