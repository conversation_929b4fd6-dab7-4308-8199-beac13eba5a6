import { PostTrainMessage } from "../../game/net/protocol/Post";
import MsgMgr from "../../lib/event/MsgMgr";
import { PostMsgEnum } from "./postConfig";

export class PostData {


  private _message: PostTrainMessage = null;

  public get message(): PostTrainMessage {
    return this._message;
  }

  public set message(value: PostTrainMessage) {
    this._message = value;
    MsgMgr.emit(PostMsgEnum.POST_REFRESH);
  }

  public get lastDeadline() {
    return this._message.lastDeadline;
  }

  public get level() {
    return this._message.level;
  }

  public get rewardList() {
    return this._message.rewardList;
  }

  public get dailyCount() {
    return this._message.dailyCount;
  }

  public get dailyMaxCount() {
    return this._message.dailyMaxCount;
  }
}
