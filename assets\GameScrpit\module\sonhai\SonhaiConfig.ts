import { JsonMgr } from "../../game/mgr/JsonMgr";
/**
 * 本地配置文件数据结构
 * 数据基础处理，过滤等，按id取
 */
export class SonhaiConfig {}

export class Check {
  index: number;
  require: number;
}

export enum SONHAI_LJAWARD_State {
  未达成 = 0,
  领取过 = 1,
  可以领取 = 2,
  异常状态 = 99,
}

export const SonhaiAudioName = {
  Effect: {
    点击道具图标: 1401,
    选中奖励道具: 1402,
    点击确认选择: 1403,
    点击左侧获奖概率活动任务: 1404,
    点击左下方限时礼包山海商店: 1405,
    点击图标进行探险: 1406,
    点击兑换按钮: 1407,
    点击充值按钮: 1408,
    点击右上方累计大奖: 1409,
  },
};

export class SonhaiMsgEnum {
  /**红点更新 */
  public static readonly SONHAIMSGENUM_RED_DOT_UPDATE = "SONHAIMSGENUM_RED_DOT_UPDATE";

  /**大奖ID变化 */
  public static readonly BIG_AWARD_ID_CHANGE = "BIG_AWARD_ID_CHANGE";

  /**累计抽奖次数发生变化 */
  public static readonly TOTAL_DRAW_COUNT_CHANGE = "TOTAL_DRAW_COUNT_CHANGE";

  /**每日活动界面任务数据发生变化 */
  public static readonly DAILY_ACTIVITY_TASK_CHANGE = "DAILY_ACTIVITY_TASK_CHANGE";

  /**指引点击去抽奖 */
  public static readonly GUIDE_CLICK_DRAW = "GUIDE_CLICK_DRAW";

  /**山海探险礼包刷新 */
  public static SONHAI_GIFTPACKAGE_REFRESH = "SONHAI_GIFTPACKAGE_REFRESH";

  /**累计大奖按钮变化刷新 */
  public static SONHAI_LEIJI_REFRESH = "SONHAI_LEIJI_REFRESH";
}
