import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../game/mgr/ApiHandler";
import { MainTaskSubCmd } from "../../game/net/cmd/CmdData";
import { MainTaskMessage } from "../../game/net/protocol/MainTask";
import { MainTaskModule } from "./MainTaskModule";

export class MainTaskSubscriber {
  private mainTaskUpdateCallback(data: MainTaskMessage) {
    MainTaskModule.data.mainTaskMsg = data;
  }
  public register() {
    //订阅服务器消息
    // 	路由: 11 - 3  --- 广播推送: com.feamon.proto.maintask.MainTaskMessage (主动推送主线任务变更情况)
    ApiHandler.instance.subscribe(MainTaskMessage, MainTaskSubCmd.mainTaskUpdate, this.mainTaskUpdateCallback);
  }

  // 取消订阅
  public unRegister() {
    ApiHandler.instance.unSubscribe(MainTaskSubCmd.mainTaskUpdate, this.mainTaskUpdateCallback);
  }
}
