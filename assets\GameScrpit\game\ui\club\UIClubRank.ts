import { _decorator } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ClubRankAdapter } from "./adapter/ClubRankViewHolder";
import { ClubModule } from "../../../module/club/ClubModule";
import { ClubMessage } from "../../net/protocol/Club";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu Aug 22 2024 16:38:08 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubRank.ts
 *
 */

@ccclass("UIClubRank")
export class UIClubRank extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubRank`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }

  private _adapter: ClubRankAdapter;
  protected onEvtShow(): void {
    super.onEvtShow();
    this._adapter = new ClubRankAdapter(this["club_rank_viewholder"]);
    this["club_list"].getComponent(AdapterView).setAdapter(this._adapter);
    ClubModule.api.listClub((data: ClubMessage[]) => {
      //
      log.log(data);
      this._adapter.setData(data);
    });
  }
}
