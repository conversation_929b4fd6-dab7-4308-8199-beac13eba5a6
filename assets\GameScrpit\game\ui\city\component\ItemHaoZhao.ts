import { _decorator, color, Label, sp, tween, v3 } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { ItemCtrl } from "../../../common/ItemCtrl";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
const { ccclass, property } = _decorator;

export interface IListData {
  id: number;
  itemId: number;
  itemNum: number;
  rate: number;
  nextRate: number;
}

@ccclass("ItemHaoZhao")
export class ItemHaoZhao extends BaseCtrl {
  start() {
    super.start();

    tween(this.node)
      .set({ scale: v3(0.5, 0.5, 1) })
      .to(0.3, { scale: v3(1, 1, 1) })
      .start();
  }

  // 初始化配置
  initData(data: IListData, isMax: boolean, isLevelUp: boolean) {
    // 箭头
    const bg_icon_jiantou_shang = this.getNode("bg_icon_jiantou_shang");

    // 下级背景
    const lbl_next_rate = this.getNode("lbl_next_rate").getComponent(Label);

    // 设置道具
    this.getNode("Item").getComponent(ItemCtrl).setItemId(data.itemId, data.itemNum);

    // 设置概率
    this.getNode("lbl_rate").getComponent(Label).string = Formate.formatDecimal(data.rate, 2) + "%";

    // 设置下一级概率
    lbl_next_rate.string = Formate.formatDecimal(data.nextRate, 2) + "%";

    // 设置是否是最大等级显示状态
    this.getNode("lbl_next_rate").active = !isMax;
    bg_icon_jiantou_shang.active = !isMax;
    this.getNode("bg_zi_yimanji").active = isMax;

    // 箭头方向
    if (data.rate < data.nextRate) {
      bg_icon_jiantou_shang.setScale(1, 1, 1);
      lbl_next_rate.color = color().fromHEX("#11CF2C");
    } else if (data.rate > data.nextRate) {
      bg_icon_jiantou_shang.setScale(1, -1, 1);
      lbl_next_rate.color = color().fromHEX("#11CF2C");
    } else {
      bg_icon_jiantou_shang.active = false;
      lbl_next_rate.color = color().fromHEX("#5480BF");
    }

    const spine = this.getNode("spine_zhao_huan_ sheng_ji_sao_guang").getComponent(sp.Skeleton);
    if (isLevelUp) {
      spine.node.active = true;
      spine.setAnimation(0, "animation", false);
      spine.setCompleteListener(() => {
        spine.setCompleteListener(null);
        spine.node.active = false;
      });
    } else {
      spine.node.active = false;
    }
  }
}
