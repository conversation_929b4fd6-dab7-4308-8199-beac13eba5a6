import { _decorator, ccenum, CCInteger, Component, instantiate, Node, UITransform } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

/**
 * @en
 * layout direction.
 *
 * @zh
 * 布局方向。
 */
enum Orientation {
  /**
   * @en
   * The horizontal direction .
   *
   * @zh
   * 水平方向。
   */
  HORIZONTAL = 0,
  /**
   * @en
   * The vertical direction.
   *
   * @zh
   * 垂直方向。
   */
  VERTICAL = 1,
}
ccenum(Orientation);

@ccclass("Star")
export class Star extends Component {
  @property({ type: Orientation })
  _orientation: Orientation = Orientation.HORIZONTAL;

  @property({ type: Orientation })
  get orientation(): Orientation {
    return this._orientation;
  }
  set orientation(value: Orientation) {
    this._orientation = value;
    // this.layout();
  }

  @property({ type: Node })
  starNor: Node;

  @property({ type: Node })
  starAdv: Node;
  @property({ type: CCInteger })
  starGap: number = 0;

  starNum: number = 7;
  starNorMax: number = 5;

  start() {
    this.layout();
    //log.log("***********start**********");
  }

  update(deltaTime: number) {}

  protected onLoad(): void {
    // this.node.getComponent(UITransform).anchorY = 0;
    //log.log("***********onLoad**********");
  }

  private layout() {
    this.node.removeAllChildren();
    if (this.orientation == Orientation.HORIZONTAL) {
      let starIndex: number = 0;
      this.node.getComponent(UITransform).width = 0;
      for (let i = 0; i < this.starNum - this.starNorMax; i++) {
        let star = instantiate(this.starAdv);
        star.name = "star";
        star.parent = this.node;
        star.setPosition(i * (this.starAdv.getComponent(UITransform).width + this.starGap), 0);
        this.node.getComponent(UITransform).width += this.starAdv.getComponent(UITransform).width + this.starGap;
        starIndex++;
      }
      for (let i = starIndex; i < this.starNum - starIndex; i++) {
        let star = instantiate(this.starNor);
        star.name = "star";
        star.parent = this.node;
        star.setPosition(i * (this.starNor.getComponent(UITransform).width + this.starGap), 0);
        this.node.getComponent(UITransform).width += this.starNor.getComponent(UITransform).width + this.starGap;
      }
      //根把锚点重新设置子节点的位置
      let width = this.node.getComponent(UITransform).width;
      let anchorX = this.node.getComponent(UITransform).anchorX;
      let left = this.node.position.x - width * anchorX;
      for (let i = 0; i < this.node.children.length; i++) {
        let element = this.node.children[i];
        let newX =
          i * (this.starNor.getComponent(UITransform).width + this.starGap) +
          this.starNor.getComponent(UITransform).width / 2 +
          left;
        element.setPosition(newX, 0);
      }
    } else {
      let startIndex: number = 0;
      this.node.getComponent(UITransform).height = 0;
      for (let i = 0; i < this.starNum - this.starNorMax; i++) {
        let star = instantiate(this.starAdv);
        star.name = "star";
        star.parent = this.node;
        star.setPosition(0, -i * (this.starAdv.getComponent(UITransform).height + this.starGap));
        this.node.getComponent(UITransform).height += this.starNor.getComponent(UITransform).height + this.starGap;
        startIndex++;
      }
      for (let i = startIndex; i < this.starNum - startIndex; i++) {
        let star = instantiate(this.starNor);
        star.name = "star";
        star.parent = this.node;
        star.setPosition(0, -i * (this.starNor.getComponent(UITransform).height + this.starGap));
        this.node.getComponent(UITransform).height += this.starNor.getComponent(UITransform).height + this.starGap;
      }
      //根把锚点重新设置子节点的位置
      let height = this.node.getComponent(UITransform).height;
      let anchorY = this.node.getComponent(UITransform).anchorY;
      let top = this.node.position.y + height * (1 - anchorY);
      for (let i = 0; i < this.node.children.length; i++) {
        let element = this.node.children[i];
        let newY =
          top -
          i * (this.starNor.getComponent(UITransform).height + this.starGap) -
          this.starNor.getComponent(UITransform).height / 2;
        element.setPosition(0, newY);
      }
    }
  }
  public setStar(star: number) {
    this.starNum = star > 10 ? 10 : star;
    // log.log(`***********${this.starNum}************`);
    this.layout();
  }
  resetInEditor(didResetToDefault?: boolean): void {
    this.setStar(this.starNum);
  }
}
