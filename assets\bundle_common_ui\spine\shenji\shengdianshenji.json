{"skeleton": {"hash": "7XjUfiNrXHpM2/HoGntReZ1tuEI", "spine": "3.8.75", "x": -277.6, "y": -97, "width": 533.29, "height": 194, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 113.63, "x": 314.83, "y": 2.42}, {"name": "zhong", "parent": "bone", "length": 100, "x": -314.54, "y": 21.33, "color": "939393ff"}, {"name": "zuo", "parent": "bone", "length": 121.35, "rotation": 90, "x": -525.2, "y": -62.87}, {"name": "you", "parent": "bone", "length": 121.35, "rotation": 90, "x": -101.3, "y": -71.1}, {"name": "dian", "parent": "bone", "rotation": -1.11, "x": -309.34, "y": 27.49, "color": "fdff00ff"}, {"name": "guang", "parent": "bone", "length": 146.7, "x": -384.85, "y": -102.01}, {"name": "dian2", "parent": "bone", "rotation": -1.11, "x": -341.4, "y": 27.49, "color": "fdff00ff"}, {"name": "dian3", "parent": "bone", "rotation": -1.11, "x": -343.63, "y": 63.8, "color": "fdff00ff"}, {"name": "gd_juanzhou", "parent": "root", "x": -194.33, "y": 8.4, "color": "46ff00ff"}], "slots": [{"name": "guang", "bone": "guang", "attachment": "guang"}, {"name": "guangdain", "bone": "dian", "attachment": "guangdain"}, {"name": "guangdain2", "bone": "dian2", "attachment": "guangdain"}, {"name": "guangdain3", "bone": "dian3", "attachment": "guangdain"}, {"name": "jz_you", "bone": "you", "attachment": "jz_you"}, {"name": "jz_zuo", "bone": "zuo", "attachment": "jz_zuo"}, {"name": "root", "bone": "root", "attachment": "root"}, {"name": "jz_zhong", "bone": "zhong", "attachment": "jz_zhong"}], "skins": [{"name": "default", "attachments": {"jz_zhong": {"jz_zhong": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [194.71, -89.75, -194.29, -89.75, -194.29, 44.25, 194.71, 44.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 389, "height": 134}}, "guangdain": {"guangdain": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [252.29, -105, -246.62, -114.69, -249.22, 19.29, 249.69, 28.98], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 499, "height": 134}}, "jz_zuo": {"jz_zuo": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-24.55, -21.38, -24.55, 20.62, 146.45, 20.62, 146.45, -21.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 171}}, "root": {"root": {"type": "clipping", "end": "root", "vertexCount": 4, "vertices": [-191.6, 86.15, -192.92, -88.22, 200.24, -86.23, 196.26, 89.47], "color": "ce3a3aff"}}, "guangdain2": {"guangdain": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [252.29, -105, -246.62, -114.69, -249.22, 19.29, 249.69, 28.98], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 499, "height": 134}}, "guangdain3": {"guangdain": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [252.29, -105, -246.62, -114.69, -249.22, 19.29, 249.69, 28.98], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 499, "height": 134}}, "guang": {"guang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [313.03, 2.6, -171.97, 2.6, -171.97, 196.6, 313.03, 196.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 485, "height": 194}}, "jz_you": {"jz_you": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-16.31, -18.47, -16.31, 23.53, 154.69, 23.53, 154.69, -18.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 171}}}}], "animations": {"animation": {"slots": {"guangdain3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00"}]}, "root": {"color": [{"time": 2.1667, "color": "ffffff00"}]}, "guangdain": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "guang": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}]}, "jz_zhong": {"color": [{"time": 1.8333, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}]}, "jz_you": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}]}, "jz_zuo": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}]}, "guangdain2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}]}}, "bones": {"you": {"translate": [{"x": -385.39, "curve": "stepped"}, {"time": 0.1333, "x": -385.39}, {"time": 0.4333}]}, "dian": {"translate": [{"time": 1.1}, {"time": 1.8333, "x": 35.24, "y": 103.66}]}, "dian2": {"translate": [{"time": 0.7667}, {"time": 1.3, "x": 35.24, "y": 103.66}]}, "dian3": {"translate": [{"time": 0.5333}, {"time": 1.0667, "x": 35.24, "y": 103.66}]}, "gd_juanzhou": {"translate": [{"time": 0.1333}, {"time": 0.4333, "x": 390.92}]}}, "deform": {"default": {"root": {"root": [{"vertices": [-13.92299, 0, -13.92299, 0, -395.81085, -1.86601, -391.88092, -3.43807], "curve": "stepped"}, {"time": 0.1333, "vertices": [-13.92299, 0, -13.92299, 0, -395.81085, -1.86601, -391.88092, -3.43807]}, {"time": 0.4333, "vertices": [-13.92299, 0, -13.92299, 0, -7.76875, -1.86601, -3.83882, -3.43807]}]}}}}}}