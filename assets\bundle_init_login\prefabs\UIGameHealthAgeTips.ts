import { _decorator, Component, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

/**
 * i<PERSON>_huang
 * Wed Mar 05 2025 16:55:13 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_game_health/UIGameHealthAgeTips.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIGameHealthAgeTips")
export class UIGameHealthAgeTips extends Component {
  protected start(): void {
    
  }
}
