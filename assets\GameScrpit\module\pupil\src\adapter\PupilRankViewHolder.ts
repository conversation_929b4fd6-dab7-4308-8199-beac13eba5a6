import { _decorator, instantiate, Label, Layout, Sprite, SpriteFrame } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { NodeTool } from "db://assets/GameScrpit/lib/utils/NodeTool";
import { PupilModule } from "db://assets/GameScrpit/module/pupil/src/PupilModule";
import { AssetMgr } from "db://assets/platform/src/ResHelper";
import { BundleEnum } from "../../../../game/bundleEnum/BundleEnum";
import ToolExt from "../../../../game/common/ToolExt";
import { PupilMessage } from "../../../../game/net/protocol/Pupil";
import { PupilAni } from "../prefab/ui/PupilAni";
import { PupilService } from "db://assets/GameScrpit/module/pupil/src/PupilService";
const { ccclass, property } = _decorator;

@ccclass("PupilRankViewHolder")
export class PupilRankViewHolder extends ViewHolder {
  // 资源管理
  private _assetMgr: AssetMgr;

  protected onLoad(): void {
    this._assetMgr = AssetMgr.create();
  }

  protected onDestroy(): void {
    this._assetMgr.release();
  }

  /**滚动列表数据变更*/
  onItemRender(pupilMsg: PupilMessage, position: number) {
    // 形象
    let pupilAni = this.node.getChildByPath("dz_bg_touxiangkuang/PupilAni");
    pupilAni.getComponent(PupilAni).setAniByNameId(pupilMsg.ownInfo.nameId);

    //配置
    let config = PupilModule.data.getConfigPupil(pupilMsg.ownInfo.talentId);

    // 所属人
    this.node.getChildByName("user_name_lab").getComponent(Label).string = `${pupilMsg.ownInfo.userName}`;

    // 弟子名字
    PupilModule.service.setPupilNameNode(this.node.getChildByName("pupil_name_lab"), pupilMsg.ownInfo.nameId);

    // 天资背景
    this._assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_G_PUPIL, `images/${config.talentBg}`, (spf: SpriteFrame) => {
      this.node.getChildByName("bg_talent").getComponent(Sprite).spriteFrame = spf;
    });

    // 弟子领悟属性
    let adult_attr_own = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.adultAttrList);

    let adult_attr_bg = this.node.getChildByName("adult_attr_bg");
    PupilModule.service.setPupilAttrNode(adult_attr_bg.getChildByName("Label"), adult_attr_own[0]);
    PupilModule.service.setPupilAdultAttrBgNode(adult_attr_bg, config.color);

    // 天生属性
    let init_attr_lay = this.node.getChildByName("init_attr_lay");

    PupilModule.service.setLayoutAttr(init_attr_lay.getComponent(Layout), pupilMsg.ownInfo.initAttrList);

    // 名次
    const node_mingci = NodeTool.findByName(this.node, "node_mingci");
    node_mingci.getChildByName("bg_1").active = position + 1 == 1;
    node_mingci.getChildByName("bg_2").active = position + 1 == 2;
    node_mingci.getChildByName("bg_3").active = position + 1 == 3;
    node_mingci.getChildByName("bg_deault").active = position + 1 > 3;
    node_mingci.getChildByName("lbl_mingci").active = position + 1 > 3;
    node_mingci.getChildByName("lbl_mingci").getComponent(Label).string = `${position + 1}`;

    this.node.active = true;
  }
}
