{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ac40665e-d4d0-44ed-94a9-************", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "ac40665e-d4d0-44ed-94a9-************@6c48a", "displayName": "jian<PERSON>_chengse", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ac40665e-d4d0-44ed-94a9-************", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ac40665e-d4d0-44ed-94a9-************@f9941", "displayName": "jian<PERSON>_chengse", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -8.5, "offsetY": 9, "trimX": 0, "trimY": 0, "width": 111, "height": 110, "rawWidth": 128, "rawHeight": 128, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-55.5, -55, 0, 55.5, -55, 0, -55.5, 55, 0, 55.5, 55, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 128, 111, 128, 0, 18, 111, 18], "nuv": [0, 0.140625, 0.8671875, 0.140625, 0, 1, 0.8671875, 1], "minPos": [-55.5, -55, 0], "maxPos": [55.5, 55, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ac40665e-d4d0-44ed-94a9-************@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ac40665e-d4d0-44ed-94a9-************@6c48a"}}