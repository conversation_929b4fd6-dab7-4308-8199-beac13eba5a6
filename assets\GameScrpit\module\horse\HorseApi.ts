import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>andlerSuccess } from "../../game/mgr/ApiHandler";
import { HorseSubCmd } from "../../game/net/cmd/CmdData";
import { <PERSON><PERSON>Val<PERSON>, LongValue, <PERSON>ValueList, StringValue } from "../../game/net/protocol/ExternalMessage";
import { HorseMessage, HorseUpResponse } from "../../game/net/protocol/Horse";
import { HorseModule } from "./HorseModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HorseApi {
  /** 获取用户的坐骑 */
  public getHorse(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(HorseMessage, HorseSubCmd.getHorse, null, (data: HorseMessage) => {
      HorseModule.data.initHorseData(data);
      success && success(data);
    });
  }

  /** 升级或升阶 */
  public upgradeOrStage(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(HorseUpResponse, HorseSubCmd.upgradeOrStage, null, (data: HorseUpResponse) => {
      HorseModule.data.setHorseUpResponse(data);
      success && success(data);
    });
  }

  /** 切换坐骑 */
  public toggleHorse(horseId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: horseId,
    };
    ApiHandler.instance.request(LongValue, HorseSubCmd.toggleHorse, LongValue.encode(data), (data: LongValue) => {
      HorseModule.data.setToggleHorseRes(data.value);
      success && success(data);
    });
  }

  /** 解锁坐骑 */
  public unLockHorse(horseId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: horseId,
    };
    ApiHandler.instance.request(
      LongValueList,
      HorseSubCmd.unLockHorse,
      LongValue.encode(data),
      (data: LongValueList) => {
        HorseModule.data.setUnLockHorseRes(data.values);
        success && success(data);
      }
    );
  }
  /** 重置坐骑 */
  public testRest(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(HorseMessage, HorseSubCmd.testRest, null, (data: HorseMessage) => {
      HorseModule.data.setHorseMessage(data);
      success && success(data);
    });
  }

  /** 后端调试：展示计算各模块的战力及赚速信息 */
  public horseInfo(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(StringValue, HorseSubCmd.horseInfo, null, (data: StringValue) => {
      log.log("StringValue ", data);
      success && success(data);
    });
  }
}
