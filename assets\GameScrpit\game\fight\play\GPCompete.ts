import { StartUp } from "../../../lib/StartUp";
import { ObjectManager } from "../manager/ObjectManager";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { _decorator } from "cc";
import { PlayerBackInfo } from "../FightDefine";
import FightManager from "../manager/FightManager";
import GamePlay from "./GamePlay";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { FightModule } from "../../../module/fight/src/FightModule";

const { ccclass, property } = _decorator;

@ccclass("GPCompete")
export class GPCompete extends GamePlay {
  public play() {
    MsgMgr.on(MsgEnum.ON_FIGHT_SKIP, this.fightSkip, this);
    MsgMgr.on(MsgEnum.ON_FIGHT_SPEED, this.upFightSpeed, this);
    MsgMgr.emit(MsgEnum.ON_FIGHT_SPEED);
    this._fightData = FightModule.instance.fightData;
    this._posMap = FightModule.instance.posMap;
    this._roundIndex = 0;
    this.initPlay();
  }

  public exit() {
    MsgMgr.off(MsgEnum.ON_FIGHT_SPEED, this.upFightSpeed, this);
    MsgMgr.off(MsgEnum.ON_FIGHT_SKIP, this.fightSkip, this);
  }

  private async initPlay() {
    let queueList = this.newQueueList();
    await this.loadRolObject(queueList);
    await new Promise((res) => TickerMgr.setTimeout(0.2, res));
    await this.startGame();
    await this.roundFight(this._fightData.e);
    await new Promise((res) => TickerMgr.setTimeout(0.5, res));
    if (FightManager.instance.fightOver == false) {
      MsgMgr.emit(MsgEnum.ON_FIGHT_END);
    }
  }

  protected newQueueList() {
    /**挑战者 */
    let challenger = this._fightData.c;
    /**被挑战者 */
    let underChallenger = this._fightData.d;
    let queueList: Array<PlayerBackInfo> = [
      {
        battlePlayerBackInfo: challenger,
        pos: this._posMap.get(challenger.b),
        dir: challenger.b,
        dbId: challenger.d,
        isPlayer: true,
        sceneId: this._fightData.a,
      },
      {
        battlePlayerBackInfo: underChallenger,
        pos: this._posMap.get(underChallenger.b),
        dir: underChallenger.b,
        dbId: underChallenger.d,
        isPlayer: true,
        sceneId: this._fightData.a,
      },
    ];
    return queueList;
  }

  protected async loadRolObject(queueList: Array<PlayerBackInfo>) {
    for (let i = 0; i < queueList.length; i++) {
      let role = await FightManager.instance.getSection(ObjectManager).callObject(queueList[i]);
      this._allRole.set(queueList[i].dir, role);
    }
  }
}
