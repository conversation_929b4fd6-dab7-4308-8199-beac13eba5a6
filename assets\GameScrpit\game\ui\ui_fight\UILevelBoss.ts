import {
  _decorator,
  Animation,
  instantiate,
  isValid,
  Label,
  Prefab,
  sp,
  Tween,
  tween,
  UIOpacity,
  UITransform,
  v3,
  Vec3,
} from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { FightModule, FightRouteItem } from "../../../module/fight/src/FightModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import ResMgr from "../../../lib/common/ResMgr";
import Formate from "../../../lib/utils/Formate";
import ToolExt from "../../common/ToolExt";
import { HeroModule } from "../../../module/hero/HeroModule";
import { Star } from "../../common/Star";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import TipMgr from "../../../lib/tips/TipMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import { FightMsgEnum } from "../../../module/fight/src/FightConfig";
import MsgEnum from "../../event/MsgEnum";
import { BossChapterPassResponse } from "../../net/protocol/Chapter";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { FightData } from "../../fight/FightDefine";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { TipAttr } from "../ui_territory/TipAttr";
import { LangMgr } from "../../mgr/LangMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HeroSort } from "../../../module/hero/HeroConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { SpGuide103002 } from "../../../module/fight/src/FightConstant";
import { BattleReplayMessage } from "../../net/protocol/Comm";
import GuideMgr from "../../../ext_guide/GuideMgr";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
import { ggLogger } from "db://gg-hot-update/scripts/hotupdate/GGLogger";
const { ccclass, property } = _decorator;

@ccclass("UILevelBoss")
export class UILevelBoss extends UINode {
  protected _openAct: boolean = true; //打开动作

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FIGHT}?prefab/ui/UILevelBoss`;
  }

  private _consumeList: number[] = [];

  private _powerHit: boolean = false;

  private _tickId: number = null;

  protected onRegEvent(): void {
    MsgMgr.on(FightMsgEnum.ON_FIGHT_ENERGY_UPDATE, this.guWuOver, this);
    MsgMgr.on(FightMsgEnum.ON_FIGHT_ITEM_UPDATE, this.guWuOver, this);

    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.energyUpdate, this);
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.energyUpdate, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(FightMsgEnum.ON_FIGHT_ENERGY_UPDATE, this.guWuOver, this);
    MsgMgr.off(FightMsgEnum.ON_FIGHT_ITEM_UPDATE, this.guWuOver, this);

    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.energyUpdate, this);
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.energyUpdate, this);
  }
  protected onEvtShow(): void {
    let cfgCopyMain = JsonMgr.instance.jsonList.c_copyMain[FightModule.data.chapterMsg.chapterId];
    let cityName = FightModule.service.getLevelName(cfgCopyMain.buildId);
    this.getNode("title").getComponent(Label).string = cityName;

    this.getConsumeList();
    this.setBossShowInfo();
    this.loadAwardList();
    this.setPlayerImg();
    this.loadHero();
    this.setPlayerPower();

    this.energyUpdate();

    this._tickId = TickerMgr.setInterval(6, this.hitDuiHua.bind(this), true);

    MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "SEE_BOSS");

    // 出场动画0.3秒在设置可点击
    TipsMgr.setEnableTouch(false, 0.3);
  }

  private isPowerHit() {
    if (this._powerHit == true) {
      return;
    }

    this._powerHit = true;

    let chapterId = FightModule.data.chapterId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let bossPower = ToolExt.levelBossPower(levelCopy.powerList);

    let playerAttrMap = PlayerModule.data.getPlayerAttrMap();
    let newPlayerAttrMap = this.addInspireAdd(playerAttrMap);
    let power = ToolExt.computePowerByAttr(newPlayerAttrMap);

    let sub = bossPower * 1.1;
    if (power > sub) {
      UIMgr.instance.showDialog(FightRouteItem.UILevelBossPower);
    }
  }

  private hitDuiHua() {
    if (isValid(this.node) == false) return;

    let chapterId = FightModule.data.chapterId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let bossPower = ToolExt.levelBossPower(levelCopy.powerList);

    let playerAttrMap = PlayerModule.data.playerBattleAttrResponse.battleAttrMap;
    let newPlayerAttrMap = this.addInspireAdd(playerAttrMap);
    let power = ToolExt.computePowerByAttr(newPlayerAttrMap);

    if (power >= bossPower) {
      return;
    }

    let info = this.getItemNeed();
    let myItem = PlayerModule.data.getItemNum(info[0]);
    let str = LangMgr.txMsgCode(236, []) + " \n";
    if (myItem > 0) {
      this.getNode("item_duihua").active = true;
      str = LangMgr.txMsgCode(237, []) + " \n";
      str += LangMgr.txMsgCode(122, [levelCopy.inspireAdd2 / 100]);
      this.getNode("item_duihua").getChildByPath("bg/Label").getComponent(Label).string = str;
      this.getNode("item_duihua").getComponent(Animation).play("duihua");

      let animation2 = this.getNode("item_duihua").getComponent(Animation);
      animation2.on(
        Animation.EventType.FINISHED,
        function () {
          this.getNode("item_duihua").active = false;
        },
        this
      );
      animation2.play("duihua");
    } else {
      this.getNode("qiyun_duihua").active = true;
      str += LangMgr.txMsgCode(122, [levelCopy.inspireAdd1 / 100]);
      this.getNode("qiyun_duihua").getChildByPath("bg/Label").getComponent(Label).string = str;
      let animation1 = this.getNode("qiyun_duihua").getComponent(Animation);
      animation1.on(
        Animation.EventType.FINISHED,
        function () {
          this.getNode("qiyun_duihua").active = false;
        },
        this
      );
      animation1.play("duihua");
    }
  }

  private energyUpdate(id?: number) {
    this["energyNum"].getComponent(Label).string = Formate.format(PlayerModule.data.getItemNum(ItemEnum.气运_1));
    this["experienceNum"].getComponent(Label).string = Formate.format(PlayerModule.data.getItemNum(ItemEnum.阅历_3));

    ToolExt.setItemIcon(this.getNode("energyIcon"), ItemEnum.气运_1);
    ToolExt.setItemIcon(this.getNode("experienceIcon"), ItemEnum.阅历_3);

    this.setMianFateNeed();
    this.setMainItemNeed();
  }

  /**计算出总的气运消耗 */
  private getConsumeList() {
    let chapterId = FightModule.data.chapterId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let inspireRateList = levelCopy.inspireRateList;
    if (levelCopy.inspireCostNum > 0) {
      let map = [];
      let param1 = levelCopy.inspireCostNum;

      for (let i = 0; i < inspireRateList.length; i++) {
        param1 = (param1 * inspireRateList[i]) / 10000;
        map.push(param1);
      }
      this._consumeList = map;
    } else {
      let param1 = FightModule.data.chapterMsg.speedParam;

      param1 = (param1 * levelCopy.inspireCost1) / 10000;

      let map = [];
      for (let i = 0; i < inspireRateList.length; i++) {
        param1 = (param1 * inspireRateList[i]) / 10000;
        map.push(param1);
      }
      this._consumeList = map;
    }
  }

  private setBossShowInfo() {
    let chapterId = FightModule.data.chapterId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let db1 = JsonMgr.instance.jsonList.c_monsterShow[levelCopy.monsterId];

    ToolExt.loadUIRole(this.getNode("bossPoint"), levelCopy.monsterId, -1, "renderScale1", this);

    this.getNode("lab_bossName").getComponent(Label).string = db1.name;
    this.getNode("levelName").getComponent(Label).string = levelCopy.des;
    this.getNode("lab_bossPower").getComponent(Label).string = Formate.format(
      ToolExt.levelBossPower(levelCopy.powerList)
    );
  }

  private loadAwardList() {
    let chapterId = FightModule.data.chapterId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let reward1List = levelCopy.reward1List;
    for (let i = 0; i < reward1List.length; i++) {
      let item = ToolExt.clone(this.getNode(`btn_item`), this);
      item.active = true;
      this.getNode("awardLay").addChild(item);

      FmUtils.setItemNode(item, reward1List[i][0], reward1List[i][1], false, 40, 40);
      item["itemId"] = reward1List[i][0];
    }
  }

  private loadHero() {
    let list = HeroModule.data.getOwnedHeros(HeroSort.POWER);

    for (let i = 0; i < list.length; i++) {
      let node = ToolExt.clone(this.getNode("btn_hero"), this);
      this.getNode("heroContent").addChild(node);
      node.active = true;

      ToolExt.setHeroImageIcon(node["heroHead"], list[i].id, this);
      let imprintLevel = HeroModule.data.getHeroImprintsLv(list[i].id);
      if (imprintLevel <= 0) {
        node["startBg"].active = false;
      } else {
        node["startBg"].active = true;
      }
      node["star"].getComponent(Star).setStar(imprintLevel);

      ToolExt.setItemBg(node, list[i].color, this);
    }
  }

  /**玩家头像设置 */
  private setPlayerImg() {
    let playerId = PlayerModule.data.head;
    let simpleMessage = ToolExt.newPlayerBaseMessage();
    (simpleMessage.avatarList = PlayerModule.data.getMyAvatarList()),
      (simpleMessage.nickname = PlayerModule.data.getPlayerInfo().nickname);
    simpleMessage.userId = PlayerModule.data.getPlayerInfo().id;
    simpleMessage.sex = PlayerModule.data.sex;
    simpleMessage.level = PlayerModule.data.getPlayerInfo().level;
    simpleMessage.vipLevel = PlayerModule.data.getPlayerInfo().vipLevel;

    FmUtils.setHeaderNode(this.getNode("BtnHeader"), simpleMessage);
  }

  private guWuOver() {
    this.setPlayerPower();
    this.setMianFateNeed();
    this.setMainItemNeed();
  }

  /**玩家战力设置 */
  private setPlayerPower() {
    /**角色属性设置 */
    let playerAttrMap = PlayerModule.data.playerBattleAttrResponse.battleAttrMap;
    let newPlayerAttrMap = this.addInspireAdd(playerAttrMap);
    let power = ToolExt.computePowerByAttr(newPlayerAttrMap);
    this["lab_playerPower"].getComponent(Label).string = Formate.format(power);
  }

  /**角色属性提升百分比 */
  private addInspireAdd(playerAttrMap) {
    let chapterId = FightModule.data.chapterId;
    let energyEncourage = FightModule.data.energyEncourage;
    let itemEncourage = FightModule.data.itemEncourage;
    let att = { ...playerAttrMap };
    let obj = { 1: 0, 2: 0, 3: 0, 4: 0 };
    let info = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let bar1 = (energyEncourage * info.inspireAdd1) / 10000;
    let bar2 = (itemEncourage * info.inspireAdd2) / 10000;
    let allBar = bar1 + bar2;
    for (let i in obj) {
      att[i] = att[i] + att[i] * allBar;
    }
    return att;
  }

  /**界面消耗道具显示 */
  private setMainItemNeed() {
    let info = this.getItemNeed();
    let myItem = PlayerModule.data.getItemNum(info[0]);

    let color = info[1] > myItem ? "#FF0000" : "#00af04";
    ToolExt.setLabColor(this["lab_item"].getComponent(Label), color);
    this["lab_item"].getComponent(Label).string = Formate.format(myItem) + "/" + Formate.format(info[1]);
  }

  /**获取本次消耗需要的道具 */
  private getItemNeed() {
    let chapterId = FightModule.data.chapterId;
    let info = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    return info.inspireCost2List;
  }

  /**获取本次消耗需要的气运 */
  private getFateNeed() {
    let energyEncourage = FightModule.data.energyEncourage;
    if (energyEncourage >= this._consumeList.length) {
      return this._consumeList[this._consumeList.length - 1];
    }
    return this._consumeList[energyEncourage];
  }

  /**界面消耗气运显示 */
  private setMianFateNeed() {
    let num = this.getFateNeed();
    let myEnergy = PlayerModule.data.getItemNum(ItemEnum.气运_1);
    let color = num > myEnergy ? "#FF0000" : "#00af04";
    ToolExt.setLabColor(this["lab_qiyun"].getComponent(Label), color);
    this["lab_qiyun"].getComponent(Label).string = Formate.format(myEnergy) + "/" + Formate.format(num);

    this.getNode("lab_add_count").getComponent(Label).string =
      FightModule.data.energyEncourage + "/" + this._consumeList.length;

    if (FightModule.data.energyEncourage >= this._consumeList.length) {
      ToolExt.setLabColor(this.getNode("lab_add_count").getComponent(Label), "#FF0000");
    } else {
      ToolExt.setLabColor(this.getNode("lab_add_count").getComponent(Label), "#00af04");
    }
  }

  private on_click_btn_player(event) {
    if (this.getNode("btn_detailLayer").children.length > 0) {
      this.getNode("btn_detailLayer").destroyAllChildren();
    }
    this.showDetail(event.node, "playerGoMessage");
  }

  private on_click_btn_hero(event) {
    if (this.getNode("btn_detailLayer").children.length > 0) {
      this.getNode("btn_detailLayer").destroyAllChildren();
    }
    this.showDetail(event.node, "heroGoMessage");
  }

  private showDetail(touchNode, nodeName) {
    let worldPosition = touchNode.getWorldPosition();
    let left = touchNode.getComponent(UITransform).width * touchNode.getComponent(UITransform).anchorX;
    let top = touchNode.getComponent(UITransform).height * (1 - touchNode.getComponent(UITransform).anchorY) + 15;

    this.setTips(worldPosition.x - left, worldPosition.y + top, nodeName);
  }

  private setTips(toWorldX: number, toWorldY: number, nodeName): void {
    let finalWorldx = toWorldX;
    let finalWorldy = toWorldY;
    let sceneWidth = this.getNode("btn_detailLayer").getComponent(UITransform).width;

    let node = ToolExt.clone(this.getNode(nodeName), this);
    node.active = true;

    this.getNode("btn_detailLayer").addChild(node);
    this.getNode("btn_detailLayer").active = true;

    let right = node.getComponent(UITransform).width * (1 - node.getComponent(UITransform).anchorX);
    if (finalWorldx + right > sceneWidth) {
      let cur = finalWorldx;
      finalWorldx = sceneWidth - right;
      let min = cur - finalWorldx;
      node.getChildByName("sanjiao").setPosition(v3(min, node.getChildByName("sanjiao").getPosition().y, 1));
    }
    let left = node.getComponent(UITransform).width * node.getComponent(UITransform).anchorX;
    if (finalWorldx - left < 0) {
      finalWorldx = left;
    }

    node.setWorldPosition(new Vec3(finalWorldx, finalWorldy));
    tween(node)
      .set({ scale: new Vec3(0.5, 0.5, 0.5) })
      .to(0.1, { scale: new Vec3(1, 1, 1) })
      .start();
    tween(node)
      .set({ worldPosition: new Vec3(toWorldX, toWorldY) })
      .to(0.1, { worldPosition: new Vec3(finalWorldx, finalWorldy) })
      .start();
  }

  private on_click_btn_detailLayer() {
    if (this.getNode("btn_detailLayer").children.length > 0) {
      this.getNode("btn_detailLayer").destroyAllChildren();
    }
    this.getNode("btn_detailLayer").active = false;
  }

  private on_click_btn_qiyun() {
    let energyEncourage = FightModule.data.energyEncourage;
    let chapterId = FightModule.data.chapterId;
    let inspireMax1 = JsonMgr.instance.jsonList.c_copyMain[chapterId].inspireMax1;
    if (energyEncourage >= inspireMax1) {
      TipMgr.showTip("达到气运鼓舞最大次数");
      return;
    }
    let needFate = this.getFateNeed();
    let energy = PlayerModule.data.getItemNum(ItemEnum.气运_1);
    if (needFate > energy) {
      TipMgr.showTip("气运不足");
      return;
    }

    FightModule.api.energyConsumeEncourage(() => {
      AudioMgr.instance.playEffect(1644);
      TipMgr.showTip("气运鼓舞成功");
      PlayerModule.data.setItemNum(ItemEnum.气运_1, energy - needFate);
      this.guWu_zhanli_skt();
      this.guWu_toux_skt();
      this.isPowerHit();
    });
  }

  private on_click_btn_itemGu() {
    let needFate = this.getItemNeed();
    let myItem = PlayerModule.data.getItemNum(needFate[0]);
    if (needFate[1] > myItem) {
      TipMgr.showTip("道具不足");
      return;
    }

    FightModule.api.itemConsumeEncourage(() => {
      TipMgr.showTip("道具鼓舞成功");
      AudioMgr.instance.playEffect(1644);
      this.guWu_zhanli_skt();
      this.guWu_toux_skt();
      this.isPowerHit();
    });
  }

  private guWu_zhanli_skt() {
    if (this.getNode("zhan_li_skt").activeInHierarchy == true) {
      return;
    }
    this.getNode("zhan_li_skt").active = true;
    this.getNode("zhan_li_skt").getComponent(sp.Skeleton).setAnimation(0, "zhan_li", false);
    this.getNode("zhan_li_skt")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == "zhan_li") {
          this.getNode("zhan_li_skt").active = false;
        }
      });
  }

  private guWu_toux_skt() {
    if (this.getNode("tou_xiang_skt").activeInHierarchy == true) {
      return;
    }
    this.getNode("tou_xiang_skt").active = true;
    this.getNode("tou_xiang_skt").getComponent(sp.Skeleton).setAnimation(0, "tou_xiang", false);
    this.getNode("tou_xiang_skt")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == "tou_xiang") {
          this.getNode("tou_xiang_skt").active = false;
        }
      });
  }

  private on_click_btn_tiaozhan() {
    TipsMgr.setEnableTouch(false, 3, false);

    let uiOpaticy = this.node.getComponent(UIOpacity);
    if (uiOpaticy == null) {
      uiOpaticy = this.node.addComponent(UIOpacity);
    }

    const t1 = tween(uiOpaticy).to(0.2, { opacity: 0 });

    const t2 = tween(this.node)
      .to(0.2, { scale: v3(1.2, 1.2, 1) })
      .call(() => {
        if (FightModule.data.chapterId == 1030002) {
          if (FightModule.data.stepPass1030002 == SpGuide103002.SECOND_FIGHT) {
            FightModule.api.passDiffBossChapter((res: BattleReplayMessage) => {
              let data: FightData = {
                fightData: JSON.parse(res.replay),
                win: res.win,
              };

              UIMgr.instance.replaceDialog(FightRouteItem.UIFightPage, { data }, null, () => {
                TipsMgr.setEnableTouch(true);
              });
            });
            return;
          }
        }

        FightModule.api.passBossChapter((res: BossChapterPassResponse) => {
          let data: FightData = {
            fightData: JSON.parse(res.replay),
            win: res.win,
          };
          // data.fightData.d.d = "50004";
          UIMgr.instance.replaceDialog(FightRouteItem.UIFightPage, { data }, null, () => {
            TipsMgr.setEnableTouch(true);
          });
        });
      });

    tween(this.node).parallel(t1, t2).start();
  }

  private on_click_btn_goHero() {
    GuideMgr.startGuide({ stepId: 20 });
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_look_boss_attr() {
    let obj: { [key: number]: number } = Object.create(null);
    let chapterId = FightModule.data.chapterId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let attrAdd = levelCopy.powerList;
    for (let j = 0; j < attrAdd.length; j++) {
      if (!obj[attrAdd[j][0]]) {
        obj[attrAdd[j][0]] = 0;
      }

      let attr = JsonMgr.instance.getConfigAttribute(attrAdd[j][0]);
      if (attr.type1 == 1) {
        obj[attrAdd[j][0]] += attrAdd[j][1];
      } else {
        obj[attrAdd[j][0]] += attrAdd[j][1] / 10000;
      }
    }

    ResMgr.loadPrefab(
      `${BundleEnum.BUNDLE_G_TERRITORY}?prefab/TipAttr`,
      (prefab: Prefab) => {
        if (isValid(this.node) == false) return;

        let node = instantiate(prefab);
        node.getComponent(TipAttr).attrMap = obj;
        TipsMgr.showTipNode(node, -1);
      },
      this
    );
  }

  public onEvtClose(): void {
    Tween.stopAllByTarget(this.node);
    TickerMgr.clearInterval(this._tickId);
  }

  public tick(dt: any): void {}
}
