[{"__type__": "cc.Prefab", "_name": "zjm_shachen", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "zjm_shachen", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 6}, "_lpos": {"__type__": "cc.Vec3", "x": 242.497, "y": 192.852, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 3147, "height": 1650}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6842961488620114, "y": 0.667909120501894}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5d88HyGw5MAa9X0X+l2Vzq"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "80a8a4ce-f20e-4dcc-a289-2d4e3d24ed0f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80iEK+MbxF+6z7suO7k/KA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0SHupsUBDSYuy4XSTdTdR", "targetOverrides": null}]