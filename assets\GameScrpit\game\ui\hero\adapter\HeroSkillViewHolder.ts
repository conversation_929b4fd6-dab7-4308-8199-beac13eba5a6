import { _decorator, Label, Node, Sprite } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import TipMgr from "../../../../lib/tips/TipMgr";
import { HeroModule } from "../../../../module/hero/HeroModule";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { HeroExpandViewHolder } from "./HeroExpandViewHolder";
import { Star } from "../../../common/Star";
import ResMgr from "../../../../lib/common/ResMgr";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { HeroAudioName } from "db://assets/GameScrpit/module/hero/HeroConfig";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("HeroSkillViewHolder")
export class HeroSkillViewHolder extends ViewHolder {
  @property(Node)
  select: Node;
  @property(Sprite)
  bgSkillIcon: Sprite;
  @property(Node)
  nodeLock: Node;
  @property(Node)
  starSkill: Node;
  @property(Label)
  lblSkillName: Label;
  @property(Label)
  lblSKillLevel: Label;
  @property(Label)
  lblSkillZizhi: Label;

  private heroId: number;
  private skillId: number;

  public setSelect(select: boolean, expand: Node): boolean {
    if (this.nodeLock.active) {
      if (HeroModule.data.getHeroMessage(this.heroId)) {
        let skillInfo = JsonMgr.instance.jsonList.c_heroSkill[this.skillId];
        let lvupCondition = skillInfo.unlockList[1];
        TipMgr.showTip(`战将种族印记 ${lvupCondition} 级时解锁`);
      }
      return false;
    }
    if (select) {
      this.select.active = true;
      expand.getComponent(HeroExpandViewHolder).updateData(this.heroId, this.skillId);
    } else {
      this.select.active = false;
    }
    return true;
  }
  public init() {
    this.node.on(Node.EventType.TOUCH_END, this.onClick, this);
  }
  private onClick() {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击技能图标);
  }
  public updateData(heroId: number, data: number) {
    // log.log(data);
    this.heroId = heroId;
    this.skillId = data;
    let skillInfo = JsonMgr.instance.jsonList.c_heroSkill[data];
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    let level = heroMessage?.skillMap[skillInfo.id] ?? 0;
    let quality = HeroModule.service.getSkillQuality(skillInfo.id, level);
    this.lblSkillName.string = `${skillInfo.name}`;
    this.lblSKillLevel.string = `等级 ${level}`;
    this.lblSkillZizhi.string = `资质 ${quality}`;
    this.starSkill.getComponent(Star).setStar(skillInfo.star);
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_G_HERO, `atlas_hero_skill/heroskill_${skillInfo.iconId}`, this.bgSkillIcon);
    if (heroMessage && level > 0) {
      this.nodeLock.active = false;
    } else {
      this.nodeLock.active = true;
    }
  }
}
