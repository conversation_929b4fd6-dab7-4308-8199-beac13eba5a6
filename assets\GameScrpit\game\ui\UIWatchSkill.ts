import { _decorator, Component, Node, UITransform, v2 } from "cc";
import { UINode } from "../../lib/ui/UINode";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import FightManager from "../fight/manager/FightManager";
import { UIMgr } from "../../lib/ui/UIMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { PlayerAudioName } from "../../module/player/PlayerConfig";
import { RoleSourceEnum } from "../fight/FightDefine";
const { ccclass, property } = _decorator;

@ccclass("UIWatchSkill")
export class UIWatchSkill extends UINode {
  protected prefab(): string {
    return `${BundleEnum.RESOURCES}?prefab/ui/UIWatchSkill`;
  }

  private _roleId: number = 0;

  public init(args: any): void {
    super.init(args);
    this._roleId = args.roleId;
  }

  protected onEvtShow(): void {
    AudioMgr.instance.playMusic(PlayerAudioName.Sound.战斗预览背景音乐);
    let posMap = new Map([
      [1, v2(-200, -200)],
      [2, v2(200, -200)],
    ]);
    let contentSize = this.getNode("main").getComponent(UITransform);
    this.getNode("main").setScale(0, 0, 0);
    FightManager.instance.start({
      main: this.node,
      parent: this.getNode("fightPoint"),
      fight: this.newGameData(),
      posMap: posMap,
      playId: 101,
      speed: 0,
      contentSize: contentSize,
    });
  }

  public tick(dt: any): void {
    FightManager.instance.tick(dt);
  }

  protected onEvtClose(): void {
    FightManager.instance.fightOver = true;
    FightManager.instance.exit();
  }

  private on_click_btn_close() {
    FightManager.instance.fightOver = true;
    UIMgr.instance.back();
  }

  private newGameData() {
    let data = {
      a: 101,
      b: 1,
      f: 30,
      c: { a: 11111, b: 1, c: 100000, f: 100000, d: this._roleId, g: RoleSourceEnum.C_LEADERSKIN },
      d: { a: 22222, b: 2, c: 100000, f: 100000, d: 10003, g: RoleSourceEnum.C_MONSTERSHOW },
      e: [
        {
          a: 1,
          b: { "1": { a: 100000, d: 100000, b: 0 }, "2": { a: 100000, d: 100000, b: 0 } },
          c: [
            { a: 1, b: 2, c: 1, d: { "2": { a: 5000 } } },
            { a: 1, b: 2, c: 4, d: { "2": { a: 20000 } } },
          ],
        },
      ],
    };

    return data;
  }
}
