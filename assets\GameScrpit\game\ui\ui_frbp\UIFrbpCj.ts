import { _decorator, Node, Sprite<PERSON>rame, instantiate, isValid } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { main_tag } from "./frlb_cj_main/main_tag";
import { activityId, FrbpModule } from "../../../module/frbp/FrbpModule";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIFrbpCj")
export class UIFrbpCj extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FRBP}?prefab/ui/UIFrbpCj`;
  }

  private _vo: any;
  private isLoading: boolean = false; // 标记是否正在加载
  private mainListNodes: (Node | null)[] = []; // 存储 main_list 里的节点

  // 假设在编辑器中赋值选中和未选中的 SpriteFrame
  @property(SpriteFrame)
  selectedSpriteFrame: SpriteFrame | null = null;

  @property(SpriteFrame)
  unselectedSpriteFrame: SpriteFrame | null = null;

  public init(args: any): void {
    super.init(args);
  }

  protected async onEvtShow() {
    TipsMgr.setEnableTouch(false, 5);
    this._vo = await FrbpModule.data.getVO(
      activityId,
      () => {
        TipsMgr.setEnableTouch(true);
      },
      () => {
        UIMgr.instance.back();
        TipsMgr.setEnableTouch(true);
      }
    );
    TipsMgr.setEnableTouch(true);

    this.handleButtonSelection(3);
  }

  private on_click_btn_type_1() {
    AudioMgr.instance.playEffect(2008);
    this.handleButtonSelection(1);
  }

  private on_click_btn_type_2() {
    AudioMgr.instance.playEffect(2008);
    this.handleButtonSelection(2);
  }

  private on_click_btn_type_3() {
    AudioMgr.instance.playEffect(2008);
    this.handleButtonSelection(3);
  }

  private on_click_btn_type_4() {
    AudioMgr.instance.playEffect(2008);
    this.handleButtonSelection(4);
  }

  /**
   * 处理按钮的选中状态切换
   * @param type - 被选中按钮的类型编号
   */
  private async handleButtonSelection(type: number) {
    if (this.isLoading) return; // 如果正在加载，直接返回
    this.isLoading = true; // 开始加载，标记为正在加载

    // 处理按钮选中状态
    const buttonNames = ["btn_type_1", "btn_type_2", "btn_type_3", "btn_type_4"];
    const bottomNode = this.getNode("bottom"); // 假设可以通过 getNode 方法获取 bottom 节点
    if (!bottomNode) {
      this.isLoading = false;
      return;
    }

    buttonNames.forEach((name, index) => {
      const button = bottomNode.getChildByName(name);
      if (button) {
        const pichNode = button.getChildByName("pich");
        const noPichNode = button.getChildByName("no_pich");

        const pitchName = "btn_type_" + type;
        const isSelected = name === pitchName;

        if (pichNode) {
          pichNode.active = isSelected;
        }
        if (noPichNode) {
          noPichNode.active = !isSelected;
        }
      }
    });

    // 处理 main_list 节点
    const mainListNode = this.getNode("main_list");
    if (!mainListNode) {
      this.isLoading = false;
      return;
    }

    const index = type - 1;
    if (!this.mainListNodes[index]) {
      let prefab = await this.assetMgr.loadPrefabSync(
        BundleEnum.BUNDLE_HD_FRBP,
        `prefab/cheng_jiu_main_list/main_tag${type}`
      );

      if (isValid(this.node) == false) {
        return;
      }

      const newNode = instantiate(prefab);
      newNode.walk((val) => {
        val.layer = this.node.layer;
      });
      newNode.getComponent(main_tag).init(this._vo.achieveVOList[type - 1]);
      newNode.parent = mainListNode;
      this.mainListNodes[index] = newNode;
    }

    // 显示选中的节点，隐藏其他节点
    this.mainListNodes.forEach((node, i) => {
      if (node) {
        if (node.getComponent(main_tag)._startInit == false) {
          node.getComponent(main_tag).init(this._vo.achieveVOList[type - 1]);
        }
        node.active = i === index;
      }
    });

    this.isLoading = false; // 加载完成，标记为加载完成
  }

  on_click_btn_close_huang_2() {
    UIMgr.instance.back();
  }
}
