import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { RedeemPackVO } from "../activity/ActivityConfig";
import { AfterMsgEnum, TopUpCostRewardVO, TopUpVO } from "./AfterConfig";
import { AfterModule } from "./AfterModule";

export class AfterService {
  private _tickId: number;
  private _topUpVO: TopUpVO;
  public async init() {
    this._topUpVO = await AfterModule.data.getafterVO();
    MsgMgr.off(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE, this.updatePopover, this);
    MsgMgr.on(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE, this.updatePopover, this);
    if (this._tickId) {
      TickerMgr.clearInterval(this._tickId);
    }
    this.updatePopover();
    this._tickId = TickerMgr.setInterval(3, this.updatePopover.bind(this), false);
  }

  private updatePopover() {
    this.update_total_recharge_popover();
    this.update_daily_recharge_popover();
    this.update_feedback_reward_popover();
  }

  /**累计充值的红点判断 */
  private async update_total_recharge_popover() {
    if (!this._topUpVO) {
      return;
    }
    let redBool = false;
    for (let i = 0; i < this._topUpVO.costRewardVOList.length; i++) {
      let info: TopUpCostRewardVO = this._topUpVO.costRewardVOList[i];
      if (AfterModule.data.takeList.indexOf(i) == -1) {
        if (AfterModule.data.totalRecharge > info.money) {
          redBool = true;
        }
      }
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_lchk.btn_type1.id, redBool);
  }

  /**累天充值的红点判断 */
  private async update_daily_recharge_popover() {
    if (!this._topUpVO) {
      return;
    }
    let signlist: Array<string> = Object.keys(AfterModule.data.signMap);
    let map = Object.create(null);
    for (let i = 0; i < this._topUpVO.signVOList.length; i++) {
      let info = this._topUpVO.signVOList[i];
      map[info.id] = info;
    }
    let index = 0;
    for (let i = 0; i < signlist.length; i++) {
      let info = AfterModule.data.signMap[signlist[i]];
      if (info >= 0) {
        index++;
      }
    }

    let redBool = false;
    for (let i = 0; i < signlist.length; i++) {
      let info = map[signlist[i]];
      if (AfterModule.data.signMap[info.id] == 0) {
        redBool = true;
        break;
      }
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_lchk.btn_type2.id, redBool);
  }

  /**回馈礼包的红点判断 */
  private async update_feedback_reward_popover() {
    if (!this._topUpVO) {
      return;
    }
    let redBool = false;
    for (let i = 0; i < this._topUpVO.redeemList[0].length; i++) {
      let info: RedeemPackVO = this._topUpVO.redeemList[0][i];
      if (!info.cost && info.adNum == 0 && info.price == 0) {
        redBool = true;
      }
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_lchk.btn_type3.id, redBool);
  }
}
