// Copyright (c) 2017-2020 Xiamen Yaji Software Co., Ltd.

CCEffect %{
techniques:
  - passes:
      - vert: vs
        frag: fs
        pass: post-process
        rasterizerState:
          cullMode: none
        depthStencilState:
          depthTest: false
          depthWrite: false
        blendState:
          targets:
            - blend: true
              blendSrc: one
              blendDst: one_minus_src_alpha
              blendSrcAlpha: one
              blendDstAlpha: zero
        properties:
          mask: { value: white }
          # 强度
          strength: { value: 1.0 }

}%


CCProgram vs %{
precision highp float;

#include <legacy/decode-standard>
#include <builtin/uniforms/cc-global>
#include <common/common-define>

out vec2 v_uv;

void main() {
  StandardVertInput In;
  CCDecode(In);
  CC_HANDLE_GET_CLIP_FLIP(In.position.xy);
  gl_Position = In.position;
  
  v_uv = a_texCoord;
}
}%


CCProgram fs %{
precision highp float;

// #include <builtin/uniforms/cc-global>

in vec2 v_uv;

uniform UBO {
  float strength;
};
#pragma rate inputTexture pass
uniform sampler2D inputTexture;
uniform sampler2D mask;
layout(location = 0)out vec4 fragColor;

void main() {
  vec4 maskColor = texture(mask, v_uv);
  fragColor = texture(inputTexture, v_uv);
  fragColor.a *= maskColor.r;
}

}%

