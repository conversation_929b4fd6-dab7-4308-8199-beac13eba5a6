import { Label } from "cc";
import { _decorator } from "cc";
import { CityModule } from "../../../module/city/CityModule";
import Formate from "../../../lib/utils/Formate";
import { PupilModule } from "../../../module/pupil/src/PupilModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

@ccclass("TipBloom")
export class TipBloom extends BaseCtrl {
  @property(Label)
  private lblValue1: Label;

  @property(Label)
  private lblValue2: Label;

  @property(Label)
  private lblTotal: Label;

  start() {
    // 显示具体信息
    // this.lblValue1.string = Formate.format(CityModule.data.getTotalBloom());
    // this.lblValue2.string = Formate.format(PupilModule.data.getTotalBloom());
    this.lblValue1.string = Formate.format(PlayerModule.data.playerBattleAttrResponse.citySpeed);
    this.lblValue2.string = Formate.format(PlayerModule.data.playerBattleAttrResponse.pupilSpeed);
    this.lblTotal.string = `当前总繁荣度\n${Formate.format(PlayerModule.data.playerBattleAttrResponse.speed)}`;
  }

  onClickDestory() {
    this.node.destroy();
  }
}
