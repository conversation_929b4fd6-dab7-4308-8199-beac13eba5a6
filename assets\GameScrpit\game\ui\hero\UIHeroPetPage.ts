import { _decorator, EventTouch, instantiate, Label, Layout, Node, sp, Sprite, UITransform, Widget } from "cc";
import { FontColor } from "../../common/FmConstant";
import ResMgr from "../../../lib/common/ResMgr";
import { PetModule } from "../../../module/pet/PetModule";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { PetMessage, PetSkillMessage } from "../../net/protocol/Pet";
import { HeroModule } from "../../../module/hero/HeroModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import { HeroEvent } from "../../../module/hero/HeroConstant";
import { JsonMgr } from "../../mgr/JsonMgr";
import { ExpandScrollView } from "../../../../platform/src/core/ui/components/ExpandScrollView";
import { HeroPetAdapter } from "./adapter/HeroPetAdapter";
import { times } from "../../../lib/utils/NumbersUtils";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import MsgEnum from "../../event/MsgEnum";
import { ItemCost } from "../../common/ItemCost";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PetAudioName } from "../../../module/pet/PetConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import GuideMgr from "../../../ext_guide/GuideMgr";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIHeroPetPage")
export class UIHeroPetPage extends BaseCtrl {
  @property(Node)
  private nodePetImg: Node;

  @property(Node)
  private nodePetInfo: Node;
  @property(Node)
  private nodeUnowned: Node;
  @property(Node)
  private nodeOp: Node;

  @property(Node)
  private itemCost: Node;

  @property(Sprite)
  private bgColor: Sprite;
  @property(Label)
  private lblName: Label;
  @property(Node)
  private nodeTab: Node;
  @property(Label)
  private lblAttack: Label;
  @property(Label)
  private lblDefense: Label;
  @property(Label)
  private lblBlood: Label;

  //皮肤相关
  @property(Node)
  private nodeSkinAdd: Node;
  @property(Node)
  private skinNode: Node;
  @property(Node)
  private nodeSkinContent: Node;

  //技能相关
  @property(Node)
  private skillNode: Node;
  @property(Node)
  private skillList: Node;
  @property(Label)
  private lblSkillNums: Label;

  @property(Node)
  private nodeWakeCount: Node;
  @property(Label)
  private lblWakeCount: Label;
  @property(Sprite)
  private bgHeroAvatar: Sprite;

  @property(Node)
  private nodeSkillViewHolder: Node;
  @property(Node)
  private nodeSkillExpandViewHolder: Node;
  @property(Node)
  private nodeSkinViewHolder: Node;

  // private petInfo: PetMessage;
  private skillNumMax: number;
  private petSkillList: any;
  private skinSelectIndex: number = 0;
  private _adapter: HeroPetAdapter;
  private _skinList: number[] = [];
  start() {
    this.refreshUI();
    this.refreshData();
    this.refreshAttrs();
    this.refreshSkin();
    MsgMgr.on(HeroEvent.HERO_VIEWMODEL, this.onViewModelChange, this);
    MsgMgr.on(MsgEnum.ON_PET_UPDATE, this.onPetMessageUpdate, this);
    this.skillList
      .getComponent(ExpandScrollView)
      .setColumnWidth(this.nodeSkillViewHolder.getComponent(UITransform).width);
    this.nodePetImg.parent
      .getChildByName("levelupSpine")
      .getComponent(sp.Skeleton)
      .setCompleteListener((e) => {
        this.nodePetImg.parent.getChildByName("levelupSpine").active = false;
      });
  }

  update(deltaTime: number) {}

  private onPetMessageUpdate() {
    this.refreshUI();
    this.refreshData(true); //只刷新数据
    this.refreshAttrs();
    this.refreshSkin();
  }
  private onViewModelChange() {
    //刷新皮肤
    this.skinSelectIndex = 0;
    this.refreshUI();
    this.refreshData();
    this.refreshAttrs();
    this.refreshSkin();
  }
  //刷新皮肤Item状态
  private refreshSkinItem(item: Node, index: number, skinList: any[]) {
    item.active = true;
    // log.log(this.petInfo);
    let heroInfo = HeroModule.viewModel.currentHero;
    let petMessage = PetModule.data.getPet(heroInfo.petId);
    let petSkin = JsonMgr.instance.jsonList.c_petSkin[skinList[index]];

    if (index == this.skinSelectIndex) {
      item.getChildByName("lbl_name").active = true;
      item.getChildByName("bg_select").active = true;
      let spineInfo = JsonMgr.instance.jsonList.c_spineShow[petSkin.spineId];
      let spinePath = spineInfo.prefabPath.split("?");
      if (spinePath.length < 2) {
        TipMgr.showTip("配置错误:灵兽资源未配置");
      } else {
        ResMgr.setNodePrefab(spinePath[0], spinePath[1], this.nodePetImg, (node: Node) => {
          log.log(node);
          log.log(this.nodePetImg.children);
          if (node.name === this.nodePetImg.children[0]?.name) {
            log.log("刷新皮肤", node.uuid);
            node.destroy();
            return true;
          }
          this.nodePetImg.destroyAllChildren();
          this.nodePetImg.addChild(node);
          this.nodePetImg.setScale(petSkin.renderScale1[0], petSkin.renderScale1[1]);
          node.getComponentInChildren(sp.Skeleton).setAnimation(0, "boss_idle", true);
          return true;
        });
      }
      ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_UI, `atlas_imgs/pingzhi_${petSkin.color}`, this.bgColor);
      this.lblName.string = petSkin.name;
    } else {
      item.getChildByName("bg_select").active = false;
      item.getChildByName("lbl_name").active = false;
    }
    item.getChildByName("lbl_name").getComponent(Label).string = `${petSkin.name}`;
    // item.getChildByName("node_selected").active = false;
    let isUnLock = false;
    log.log("宠物信息", petMessage);
    for (let i = 0; petMessage && i < petMessage.skinList.length; i++) {
      if (petMessage.skinList[i] == skinList[index]) {
        isUnLock = true;
        break;
      }
    }
    if (isUnLock && petMessage && petMessage.chosenSkinId == skinList[index]) {
      item.getChildByName("node_selected").active = true;
    } else {
      item.getChildByName("node_selected").active = false;
    }
    // if(petMessage.)
    // item.setPosition(0, 0);
    //设置皮肤图标
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_ITEM,
      `autoItem/${petSkin.iconId}`,
      item.getChildByName("bg_skin_icon").getComponent(Sprite)
    );
  }
  private refreshSkin() {
    let heroInfo = HeroModule.viewModel.currentHero;
    let pet = PetModule.config.getHeroPet(heroInfo.petId);
    let petMessage = PetModule.data.getPet(heroInfo.petId);
    // let skinList = []; //皮肤列表

    this.skinNode.getChildByName("lbl_name").active = false;

    let cacheLength = this.nodeSkinContent.children.length;
    if (cacheLength - this._skinList.length > 0) {
      for (let i = 0; i < cacheLength; i++) {
        if (i < this._skinList.length) {
          this.refreshSkinItem(this.nodeSkinContent.children[i], i, this._skinList);
        } else {
          this.nodeSkinContent.children[i].active = false;
        }
      }
    } else {
      for (let i = 0; i < this._skinList.length; i++) {
        if (i < cacheLength) {
          this.refreshSkinItem(this.nodeSkinContent.children[i], i, this._skinList);
        } else {
          let item = instantiate(this.nodeSkinViewHolder);
          item.on(
            Node.EventType.TOUCH_END,
            (e: EventTouch) => {
              AudioMgr.instance.playEffect(1923);
              this.skinSelectIndex = e.target.getSiblingIndex();
              this.refreshSkin();
            },
            this
          );
          this.nodeSkinContent.addChild(item);
          this.nodeSkinContent.getComponent(Layout).updateLayout();
          this.refreshSkinItem(item, i, this._skinList);
        }
      }
    }
    // JsonMgr.instance.jsonList.c_petSkin[]
    let petSkin = JsonMgr.instance.jsonList.c_petSkin[this._skinList[this.skinSelectIndex]];
    this.nodeSkinAdd.getChildByName("lbl_skin_add1").getComponent(Label).string = `+${petSkin.add1 ?? 0}`;
    this.nodeSkinAdd.getChildByName("lbl_skin_add2").getComponent(Label).string = `+${petSkin.add2 ?? 0}`;
    this.nodeSkinAdd.getChildByName("lbl_skin_add3").getComponent(Label).string = `+${petSkin.add3 ?? 0}`;
    let isUnLock = false;
    log.log("宠物信息", petMessage);
    for (let i = 0; petMessage && i < petMessage.skinList.length; i++) {
      if (petMessage.skinList[i] == this._skinList[this.skinSelectIndex]) {
        isUnLock = true;
        break;
      }
    }

    if (isUnLock) {
      this.skinNode.getChildByName("lbl_jiesuotiaojian").active = false;
      this.skinNode.getChildByName("btn_chuandai").active = true;
    } else {
      this.skinNode.getChildByName("btn_chuandai").active = false;
      this.skinNode.getChildByName("lbl_jiesuotiaojian").active = true;
      let jiesuotiaojian = `-`;
      if (petSkin.unlockType == 1) {
        jiesuotiaojian = `灵兽等级 ${petSkin.unlockNum}级解锁`;
      } else if (petSkin.unlockType == 2) {
        jiesuotiaojian = `觉醒次数 ${petSkin.unlockNum}次解锁`;
      } else if (petSkin.unlockType == 3) {
        jiesuotiaojian = `通过洗练技能概率获得`;
      }
      this.skinNode.getChildByName("lbl_jiesuotiaojian").getComponent(Label).string = jiesuotiaojian;
    }
    if (isUnLock && petMessage && petMessage.chosenSkinId == this._skinList[this.skinSelectIndex]) {
      this.skinNode.getChildByName("bg_chuandai").active = true;
      this.skinNode.getChildByName("btn_chuandai").active = false;
    } else {
      this.skinNode.getChildByName("bg_chuandai").active = false;
    }
  }

  private refreshData(updateOnly: boolean = false) {
    let heroInfo = HeroModule.viewModel.currentHero;
    let petMessage = PetModule.data.getPet(heroInfo.petId);
    let pet = PetModule.config.getHeroPet(heroInfo.petId);
    let skillList: PetSkillMessage[] = [];
    this._skinList = [];
    this._skinList.push(pet.firstSkin);
    this._skinList = this._skinList.concat(pet.skin);

    if (petMessage) {
      for (let i = 0; i < this._skinList.length; i++) {
        if (petMessage.chosenSkinId == this._skinList[i]) {
          this.skinSelectIndex = i;
        }
      }
      skillList = skillList.concat(petMessage.petSkillList);
      skillList.push(null);
    } else {
      let skillFirstNum = PetModule.config.getHeroPet(heroInfo.petId).skillFirst;
      for (let i = 0; i < skillFirstNum; i++) {
        skillList.push({
          skillAdd: 0,
          backUpSkillAdd: 0,
          energyWashCount: 0,
          itemWashCount: 0,
        });
      }
    }
    if (updateOnly) {
      this._adapter.updateDataOnly(skillList);
    } else {
      this._adapter = new HeroPetAdapter(this.nodeSkillViewHolder, this.nodeSkillExpandViewHolder);

      this.skillList.getComponent(ExpandScrollView).setAdapter(this._adapter);
      log.log(skillList);
      this._adapter.setData(skillList);

      // 跳到最小的那个并且展开 --START
      let minIndex = 0;
      let minValue = 1000;
      for (let i = 0; i < skillList.length; i++) {
        if (skillList[i] && skillList[i].skillAdd < minValue) {
          minValue = skillList[i].skillAdd;
          minIndex = i;
        }
      }
      this.skillList.getComponent(ExpandScrollView).expandIndex(minIndex);
      // 跳到最小的那个并且展开 --END
    }
  }

  private refreshAttrs() {
    let heroInfo = HeroModule.viewModel.currentHero;
    let pet = PetModule.data.getPet(heroInfo.petId);
    let skillNum = PetModule.config.getHeroPet(heroInfo.petId).skillFirst;
    if (pet) {
      let numberAttack = 0;
      let numberDefense = 0;
      let numberBlood = 0;
      let petSkills = PetModule.data.getHeroPetSkillList();
      for (let i = 0; i < pet.petSkillList.length; i++) {
        switch (petSkills[i % petSkills.length].id) {
          case 101: //武将生命
            numberBlood += times(pet.petSkillList[i].skillAdd, 100);
            break;
          case 102: //武将攻击
            numberAttack += times(pet.petSkillList[i].skillAdd, 100);
            break;
          case 103: //武将防御
            numberDefense += times(pet.petSkillList[i].skillAdd, 100);
            break;
        }
      }
      skillNum = pet.petSkillList.length;
      this.lblAttack.string = `攻击+${numberAttack}%`;
      this.lblDefense.string = `防御+${numberDefense}%`;
      this.lblBlood.string = `血量+${numberBlood}%`;
    }
    this.lblSkillNums.string = "技能数量:" + skillNum;
  }
  protected onDestroy(): void {
    MsgMgr.off(HeroEvent.HERO_VIEWMODEL, this.onViewModelChange, this);
    MsgMgr.off(MsgEnum.ON_PET_UPDATE, this.onPetMessageUpdate, this);
  }
  private refreshUI() {
    log.log("refreshUI", this);
    let heroInfo = HeroModule.viewModel.currentHero;
    let pet: PetMessage = PetModule.data.getPet(heroInfo.petId);

    if (pet) {
      let petLocal = PetModule.config.getHeroPet(heroInfo.petId);
      this.nodeOp.active = true;
      this.nodePetInfo.active = true;
      this.nodeUnowned.active = false;
      let quality = PetModule.data.getHeroPetQuality(heroInfo.petId);
      this.nodePetInfo.getChildByName("lbl_level").getComponent(Label).string = `等级:${pet.level}`;
      this.nodePetInfo.getChildByName("lbl_quality").getComponent(Label).string = `资质：${quality}`;
      if (pet.level < petLocal.levelMax) {
        this.itemCost.getComponent(ItemCost).setItemId(petLocal.trainAddList[0], petLocal.trainAddList[1]);
      } else {
        this.itemCost.getComponent(ItemCost).setItemId(petLocal.wakeLvCostList[0], petLocal.wakeLvCostList[1]);
      }
      //   this.numsSkillup.string = `技能数量：${spirit.skillFirst + pet.awakeCount}`;
      if (pet.awakeCount > 0) {
        this.nodeWakeCount.active = true;
        this.lblWakeCount.string = `觉醒${pet.awakeCount}阶`;
      } else {
        this.nodeWakeCount.active = false;
      }
      // this.skillList.getComponent(UITransform).height = 400;
      this.skillList.getComponent(Widget).updateAlignment();
      this.skillList.children.forEach((item) => {
        item.getComponent(Widget)?.updateAlignment();
      });
    } else {
      this.nodeWakeCount.active = false;

      // this.skillList.getComponent(UITransform).height = 460;
      this.skillList.getComponent(Widget).updateAlignment();
      this.skillList.children.forEach((item) => {
        item.getComponent(Widget)?.updateAlignment();
      });
      this.nodeOp.active = false;
      this.nodePetInfo.active = false;
      this.nodeUnowned.active = true;
      //   this.numsSkillup.string = `技能数量：${spirit.skillFirst}`;
    }
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/item_${heroInfo.id}`, this.bgHeroAvatar);
  }
  private onSwitch() {
    AudioMgr.instance.playEffect(1922);
    let nodeSwitch = this.nodeTab.getChildByName("node_switch");
    nodeSwitch.setScale(-nodeSwitch.scale.x, 1);
    if (nodeSwitch.scale.x < 0) {
      this.nodeTab.getChildByName("lbl_skin").getComponent(Label).outlineColor = FontColor.YELLOW_STROKE;
      this.nodeTab.getChildByName("lbl_skill").getComponent(Label).outlineColor = FontColor.BLUE_STROKE;
      this.skillNode.active = false;
      this.skinNode.active = true;
    } else {
      this.nodeTab.getChildByName("lbl_skin").getComponent(Label).outlineColor = FontColor.BLUE_STROKE;
      this.nodeTab.getChildByName("lbl_skill").getComponent(Label).outlineColor = FontColor.YELLOW_STROKE;
      this.skillNode.active = true;
      this.skinNode.active = false;
    }
  }
  //点击升级
  private onClickUpgrade() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let heroInfo = HeroModule.viewModel.currentHero;
    let petLocal = PetModule.config.getHeroPet(heroInfo.petId);
    if (!this.itemCost.getComponent(ItemCost).isEnough()) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: petLocal.trainAddList[0] });
      return;
    }
    let petMessage = PetModule.data.getPet(petLocal.id);
    let MaxLv = PetModule.service.getPetMaxLevel(petLocal.id);
    if (petMessage.level >= MaxLv) {
      TipMgr.showTip("灵兽觉醒后可以提升等级上限");
      return;
    }
    PetModule.api.levelUpOne(heroInfo.petId, (data: PetMessage) => {
      AudioMgr.instance.playEffect(PetAudioName.Effect.灵兽升级成功);
      // this.onLevelUpOne(data);
      this.refreshAttrs();
      let levelupSpine = this.nodePetImg.parent.getChildByName("levelupSpine");
      levelupSpine.active = true;
      levelupSpine.getComponent(sp.Skeleton).setAnimation(0, "action", false);
    });
  }
  private onClickGetPath() {
    AudioMgr.instance.playEffect(PetAudioName.Effect.点击前往获得灵兽);
    let heroInfo = HeroModule.viewModel.currentHero;
    let pet = PetModule.config.getHeroPet(heroInfo.petId);
    if (pet.jumplist.length == 0) return;
    GuideMgr.startGuide({ stepId: pet.jumplist[0] });
  }
  //穿戴皮肤
  private onClickChooseSkin() {
    AudioMgr.instance.playEffect(1924);
    let heroInfo = HeroModule.viewModel.currentHero;
    let skinId = this._skinList[this.skinSelectIndex];
    PetModule.api.chooseSkin(heroInfo.petId, skinId, () => {
      this.refreshSkin();
    });
  }
}
