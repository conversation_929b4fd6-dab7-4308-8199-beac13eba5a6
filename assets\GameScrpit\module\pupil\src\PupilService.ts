import { Sprite, Node, Label } from "cc";
import { BundleEnum } from "../../../game/bundleEnum/BundleEnum";
import ResMgr from "../../../lib/common/ResMgr";
import { PupilModule } from "./PupilModule";
import { JsonMgr } from "../../../game/mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import { workSlotIcon } from "./PupilConstant";
import { PupilMessage } from "../../../game/net/protocol/Pupil";
import { IAttr, SystemOpenEnum } from "../../../game/GameDefine";
import { PlayerModule } from "../../player/PlayerModule";
import { Layout } from "cc";
import { instantiate } from "cc";
import { formatNumber } from "../../../lib/utils/NumbersUtils";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { BadgeMgr, BadgeType } from "../../../game/mgr/BadgeMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../../game/event/MsgEnum";
import { GameDirector } from "../../../game/GameDirector";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class PupilService {
  // 定时任务
  public schedule() {
    const slotList = PupilModule.data.pupilTrainMsg.trainSlotList;

    // 体力上限
    const trainMax = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level).trainMax;

    for (let i = 0; i < slotList.length; i++) {
      const slot = PupilModule.data.pupilTrainMsg.trainSlotList[i];
      const config = PupilModule.data.getConfigPupil(1);

      if (trainMax > slot.vitality && slot.lastUpdateTime + config.time * 1000 < TimeUtils.serverTime) {
        PupilModule.api.getTrain();
        return;
      }
    }
  }

  public init() {
    MsgMgr.off(MsgEnum.ON_PUPIL_UPDATE, this.updatePopover, this);

    MsgMgr.on(MsgEnum.ON_PUPIL_UPDATE, this.updatePopover, this);
    this.updatePopover();
  }

  /** ================================ data 数据加工 ===================================== */
  /** 获取弟子可上阵列表 */
  public getPupilCanWorkList(slotIndex: number): number[] {
    // id
    let workList = [];
    let list = Object.keys(PupilModule.data.allPupilMap).map(Number);

    log.log("list", list);

    for (let i = 0; i < list.length; i++) {
      let pupilId = list[i];
      let pupilMsg: PupilMessage = PupilModule.data.allPupilMap[pupilId];
      if (pupilMsg.ownInfo.adultAttrList.length == 0) {
        continue;
      }
      if (PupilModule.data.pupilTrainMsg.workSlotList.indexOf(pupilId) != -1) {
        continue;
      }
      workList.push(pupilId);
    }
    // 按照天资进行排序，天资一样的情况下，按照获得时间的先后顺序排序
    workList.sort((a, b) => {
      let a1 = PupilModule.data.allPupilMap[a];
      let b1 = PupilModule.data.allPupilMap[b];
      if (a1.ownInfo.talentId == b1.ownInfo.talentId) {
        return a1.addStamp - b1.addStamp;
      }
      return b1.ownInfo.talentId - a1.ownInfo.talentId;
    });

    // 已上阵的放前 和 放后
    for (let j = 0; j < PupilModule.data.pupilTrainMsg.workSlotList.length; j++) {
      if (PupilModule.data.pupilTrainMsg.workSlotList[j] == -1) {
        continue;
      }
      if (j == slotIndex) {
        workList.unshift(PupilModule.data.pupilTrainMsg.workSlotList[j]);
      } else {
        workList.push(PupilModule.data.pupilTrainMsg.workSlotList[j]);
      }
    }

    return workList;
  }

  /** 获取结伴弟子id list */
  public get marriedPupilIdList() {
    let marriedPupilIdList: number[] = [];
    let allPupilMap = PupilModule.data.allPupilMap;

    let list = Object.keys(allPupilMap).map(Number);
    for (let i = 0; i < list.length; i++) {
      let pupilId = list[i];
      let pupilMsg = allPupilMap[pupilId];
      if (pupilMsg.ownInfo.adultAttrList.length == 0) {
        continue;
      }
      if (pupilMsg.partnerInfo) {
        marriedPupilIdList.push(pupilId);
      }
    }

    marriedPupilIdList.sort((a, b) => {
      let a1 = allPupilMap[a];
      let b1 = allPupilMap[b];
      return b1.marryStamp - a1.marryStamp;
    });

    // 任命的排前端
    for (let i = 0; i < PupilModule.data.pupilTrainMsg.workSlotList.length; i++) {
      let jobPupilId = PupilModule.data.pupilTrainMsg.workSlotList[i];
      if (jobPupilId > 0) {
        let index = marriedPupilIdList.indexOf(jobPupilId);
        if (index != -1) {
          marriedPupilIdList.splice(index, 1);
          marriedPupilIdList.unshift(jobPupilId);
        }
      }
    }

    return marriedPupilIdList;
  }

  /** 获取请求中的弟子 id list  */
  public get onRequestPupilIdList() {
    let list = [
      ...Object.keys(PupilModule.data.pupilTrainMsg.clubMarryApplyMap).map(Number),
      ...Object.keys(PupilModule.data.pupilTrainMsg.crossMarryApplyMap).map(Number),
      ...Object.keys(PupilModule.data.pupilTrainMsg.localMarryApplyMap).map(Number),
    ];

    return list;
  }

  /**任命弟子的加成属性 展示 */
  public get pupilWorkAttrMapShow() {
    let workSlotList = PupilModule.data.pupilTrainMsg.workSlotList;
    let attr = new IAttr();

    function setAttr(attr1: IAttr, attr2: IAttr) {
      let list = Object.keys(attr1).map(Number);
      for (let i = 0; i < list.length; i++) {
        let id = list[i];
        attr1[id] += attr2[id];
      }
    }

    for (let i = 0; i < workSlotList.length; i++) {
      if (workSlotList[i] != -1) {
        let attrMap = PupilModule.data.getPupilWorkAttrMap(workSlotList[i]);
        setAttr(attr, attrMap);
      }
    }

    return attr;
  }

  /**设置 排行榜 */
  public get bestAttr() {
    let bestAttr: number[] = [];
    let bestNum = 0;
    let list = Object.keys(PupilModule.data.allPupilMap).map(Number);
    for (let i = 0; i < list.length; i++) {
      let pupilId = list[i];
      let pupilMsg = PupilModule.data.allPupilMap[pupilId];
      if (pupilMsg.ownInfo.adultAttrList.length == 0) {
        continue;
      }
      if (pupilMsg.ownInfo.adultAttrList[1] > bestNum) {
        bestAttr = pupilMsg.ownInfo.adultAttrList;
      }
    }
    return bestAttr;
  }

  /** =============================== UI =================================== */
  /**设置弟子名字 */
  public setPupilNameNode(val: Node, nameId: number) {
    val.getComponent(Label).string = this.getPupilName(nameId);
  }

  /**设置弟子出师属性背景 */
  public setPupilAdultAttrBgNode(val: Node, color: number) {
    ResMgr.loadSpriteFrame(
      `${BundleEnum.BUNDLE_G_PUPIL}?patterns/pupilIcon`,
      `dz_shuxing_2_${color}`,
      val.getComponent(Sprite)
    );
  }

  /**设置弟子属性Label {id, num} */
  public setPupilAttrNode(val: Node, attrMap) {
    val.getComponent(Label).string = `${JsonMgr.instance.jsonList.c_attribute[attrMap["id"]]["name"]}:${formatNumber(
      attrMap["num"] * 100,
      1
    )}%`;
  }

  /**设置弟子属性Label {id, num} */
  public setPupilBaseAttrNode(val: Node, attrMap) {
    val.getComponent(Label).string = `${JsonMgr.instance.jsonList.c_attribute[attrMap["id"]]["name"]}：${formatNumber(
      attrMap["num"],
      1
    )}`;
  }

  /**设置弟子任命 职位icon */
  public setPupilWorkSlotIconNode(val: Node, workSlotIndex: number) {
    val.active = workSlotIndex != -1;
    if (workSlotIndex != -1) {
      ResMgr.loadSpriteFrame(
        `${BundleEnum.BUNDLE_G_PUPIL}?patterns/UIPopil`,
        workSlotIcon[workSlotIndex],
        val.getComponent(Sprite)
      );
    }
  }

  public getMarryChannelId(pupilId: number) {
    if (PupilModule.data.pupilTrainMsg.clubMarryApplyMap[pupilId]) {
      if (PupilModule.data.pupilTrainMsg.clubMarryApplyMap[pupilId] > TimeUtils.serverTime) {
        return 1;
      }
    } else if (PupilModule.data.pupilTrainMsg.localMarryApplyMap[pupilId]) {
      if (PupilModule.data.pupilTrainMsg.localMarryApplyMap[pupilId] > TimeUtils.serverTime) {
        return 2;
      }
    } else if (PupilModule.data.pupilTrainMsg.crossMarryApplyMap[pupilId]) {
      if (PupilModule.data.pupilTrainMsg.crossMarryApplyMap[pupilId] > TimeUtils.serverTime) {
        return 3;
      }
    }
    return -1;
  }

  public getMarryTimeEnd(pupilId: number) {
    if (PupilModule.data.pupilTrainMsg.clubMarryApplyMap[pupilId]) {
      return PupilModule.data.pupilTrainMsg.clubMarryApplyMap[pupilId];
    } else if (PupilModule.data.pupilTrainMsg.localMarryApplyMap[pupilId]) {
      return PupilModule.data.pupilTrainMsg.localMarryApplyMap[pupilId];
    } else if (PupilModule.data.pupilTrainMsg.crossMarryApplyMap[pupilId]) {
      return PupilModule.data.pupilTrainMsg.crossMarryApplyMap[pupilId];
    }
    return -1;
  }

  // ================== 马丁 =====================
  /** 弟子姓名 */
  public getPupilName(nameId: number): string {
    return JsonMgr.instance.jsonList.c_pupilName[nameId]["des"] || "";
  }

  /**设置天赋图标 */
  public setPupilTalentBg(sp: Sprite, talentId: number) {
    let config = PupilModule.data.getConfigPupil(talentId);
    ResMgr.loadSpriteFrame(`${BundleEnum.BUNDLE_G_PUPIL}?patterns/pupilIcon`, `DZ_dizichenghao${config.color}`, sp);
  }

  /**设置弟子出师属性背景 */
  public setPupilAdultAttrBg(sp: Sprite, talentId: number) {
    let config = PupilModule.data.getConfigPupil(talentId);
    ResMgr.loadSpriteFrame(
      `${BundleEnum.BUNDLE_G_PUPIL}?patterns/pupilIcon`,
      `dz_shuxing_2_${config.color}`,
      sp.getComponent(Sprite)
    );
  }

  public setLayoutAttr(layout: Layout, attrList: number[]) {
    const attrMap: { [key: number]: number } = {};

    for (let i = 0; i < attrList.length; i += 2) {
      const id = attrList[i];
      const value = attrList[i + 1];

      if (attrMap[id]) {
        attrMap[id] += value;
      } else {
        attrMap[id] = value;
      }
    }

    const newAttrList: number[] = [];
    for (const [id, value] of Object.entries(attrMap)) {
      newAttrList.push(Number(id), value);
    }

    layout.node.children.forEach((child) => {
      child.active = false;
    });
    for (let i = 0; i < newAttrList.length; i += 2) {
      let child: Node;
      if (layout.node.children.length > i / 2) {
        child = layout.node.children[i / 2];
      } else {
        child = instantiate(layout.node.children[0]);
        child.parent = layout.node;
      }
      child.active = true;

      child.getComponent(Label).string = Formate.formatAttribute(newAttrList[i], newAttrList[i + 1]);
    }
  }

  public updatePopover() {
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.PUPIL_弟子系统)) {
      return;
    }

    // 招人，训练红点
    const slotList = PupilModule.data.pupilTrainMsg.trainSlotList;
    const trainMax = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level).trainMax;
    let isAddRed = false;
    let isTrainRed = false;
    for (let idx in slotList) {
      let slot = slotList[idx];
      if (!isAddRed && slot.pupilId <= 0) {
        isAddRed = true;
      }

      if (!isTrainRed && slot.vitality >= trainMax) {
        isTrainRed = true;
      }
    }

    BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_pupil.btn_add.id, isAddRed);
    BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_pupil.btn_train.id, isTrainRed);

    // 任命红点
    let isWorkRed = false;

    const workSlotList = PupilModule.data.pupilTrainMsg.workSlotList;

    // 有成年弟子
    let keys = Object.keys(PupilModule.data.allPupilMap).map(Number);

    // 是否有闲置弟子
    let hasIdlePupile = false;
    for (let i in keys) {
      let key = keys[i];
      let pupilMsg = PupilModule.data.allPupilMap[key];

      if (pupilMsg.ownInfo.adultAttrList.length == 0) {
        continue;
      }

      if (workSlotList.indexOf(pupilMsg.id) < 0) {
        hasIdlePupile = true;
        break;
      }
    }

    // 是否有空位
    if (hasIdlePupile) {
      for (let idx in workSlotList) {
        let workSlot = workSlotList[idx];
        //如果当前位置没有解锁是-2，如果当前位置没有委任，则为-1，委任了弟子则是弟子的唯一id
        if (!isWorkRed && workSlot == -1) {
          //需要已解锁，并且没有委任的空位
          isWorkRed = true;
        }
      }
    }
    BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_pupil.btn_renming.id, isWorkRed);
  }
}
