import { _decorator } from "cc";
import GameObject from "./GameObject";
import { AssetMgr } from "db://assets/platform/src/ResHelper";
const { ccclass, property } = _decorator;

@ccclass("Section")
export abstract class Section {
  private _assetMgr: AssetMgr = null;

  protected get assetMgr() {
    if (!this._assetMgr) {
      this._assetMgr = AssetMgr.create();
    }
    return this._assetMgr;
  }

  public static sectionName(): string {
    return this.name;
  }
  private subject: GameObject = null;
  public setSub(subject) {
    this.subject = subject;
  }
  public getSub(): GameObject {
    return this.subject;
  }
  private _isReady: boolean = false;
  protected ready() {
    this._isReady = true;
  }
  public isReady(): boolean {
    return this._isReady;
  }
  private _isPause: boolean = false;
  public setPause(isPause: boolean) {
    if (this._isPause && !isPause) {
      this.onResume();
      this._isPause = false;
    }
    if (!this._isPause && isPause) {
      this.onPause();
      this._isPause = true;
    }
  }
  public isPause(): boolean {
    return this._isPause;
  }
  public getSection<T extends Section>(type: { new (): T }): T {
    return this.getSub().getSection(type);
  }
  public getSectionByName(name) {
    return this.getSub().getSectionByName(name);
  }
  ////////////////////////////////
  public onInit(go: GameObject, args) {
    this.setSub(go);
  }
  public onStart() {}
  public onPause() {}
  public onResume() {}
  public onRemove() {}
  public updateSelf(dt) {}
  public onMsg(name: string, func) {
    if (this.getSectionByName("NotifySection")) {
      this.getSectionByName("NotifySection")["onNotify"](name, func);
    }
  }
  public offMsg(name: string, func) {
    if (this.getSectionByName("NotifySection")) {
      this.getSectionByName("NotifySection")["offNotify"](name, func);
    }
  }

  public emitMsg(name: string, args = null) {
    if (this.getSectionByName("NotifySection")) {
      this.getSectionByName("NotifySection")["notify"](name, args);
    }
  }
}
