import { _decorator, CCInteger, CCString, Component } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ActivityModule } from "../../../module/activity/ActivityModule";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { ActivityAudioName } from "../../../module/activity/ActivityConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("FundBanner")
export class FundBanner extends Component {
  @property(CCString)
  uiName: string = "";

  @property(CCInteger)
  activityId: number = 0;

  private on_touch_btn_go() {
    AudioMgr.instance.playEffect(ActivityAudioName.Effect.点击前往按钮);
    if (this.uiName == "") {
      log.error("banner没有填写前往的脚本名");
      return;
    }

    if (this.activityId == 0 || ActivityModule.service.checkActivityUnlock(this.activityId)) {
      UIMgr.instance.showDialog(this.uiName);
    } else {
      TipMgr.showTip("活动未解锁");
    }
  }
}
