import { IConfigGuideGameTast } from "../GameScrpit/game/JsonDefine";
import { JsonMgr } from "../GameScrpit/game/mgr/JsonMgr";
import MsgMgr from "../GameScrpit/lib/event/MsgMgr";
import { FightModule } from "../GameScrpit/module/fight/src/FightModule";
import { QiyunXiaoyouxiEvent } from "./QiyunXiaoyouxiEvent";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export const add_num: number = 500;

export const needNum: number = 5000;

export enum QiyunXiaoyouxiTalkType {
  TALK_DAJI_开场 = 1001,
  TALK_MONSTER_开场 = 2001,
  TALK_BOSS_开场 = 2002,
  TALK_DAJI_进攻 = 1002,
  TALK_DAJI_气运不足 = 1003,
  TALK_DAJI_气运充足 = 1004,
  TALK_BOSS_终极挑战 = 2003,
}

export class QiyunXiaoyouxiData {
  // 生成单例模式
  private constructor() {
    this._taskList = Object.values(JsonMgr.instance.jsonList.c_guideGameTast);
    this._currentQiyun = this.getCurrentTask().doneNum;
  }
  private static _instance: QiyunXiaoyouxiData = null;
  public static get instance(): QiyunXiaoyouxiData {
    if (this._instance == null) {
      this._instance = new QiyunXiaoyouxiData();
    }
    return this._instance;
  }
  private _taskList: IConfigGuideGameTast[] = [];
  //
  private _currentTaskIndex = 0;
  //
  private _currentQiyun: number = 0;

  //
  private _consumeList = [];
  public get currentQiyun(): number {
    return this._currentQiyun;
  }

  public init() {
    this._currentTaskIndex = 0;
  }

  /** 消耗气运 */
  public consumeQiyun(consume?: number): boolean {
    // const configMonsterMatrix = FightModule.data.getConfigMonsterMatrix(40);
    // let value = maxValue / configMonsterMatrix.positionList.length;
    let maxValue = this.getCurrentTask().doneNum;
    let value = this._consumeList.shift();
    if (!value) {
      value = this._currentQiyun;
    }
    if (this._currentQiyun < value) {
      MsgMgr.emit(QiyunXiaoyouxiEvent.QIYUN_NOT_ENOUGH, this._currentQiyun);
      return false;
    }
    this._currentQiyun -= value;
    MsgMgr.emit(QiyunXiaoyouxiEvent.QIYUN_CONSOME, value, maxValue);
    return true;
  }
  generateRedPackets(totalAmount: number, totalPackets: number): number[] {
    if (totalPackets <= 0 || totalAmount <= 0) {
      throw new Error("总金额和红包数量必须大于0");
    }
    const packets: number[] = [];
    let remainingAmount = totalAmount; // 剩余金额
    let remainingPackets = totalPackets; // 剩余红包数量

    for (let i = 0; i < totalPackets; i++) {
      // 最后一个红包直接分配剩余金额
      if (i === totalPackets - 1) {
        packets.push(remainingAmount);
      } else {
        // 计算当前红包的最大值和最小值
        const max = (remainingAmount / remainingPackets) * 2; // 二倍均值
        const min = remainingAmount / remainingPackets / 2; // 最小值为0.01元（1分钱）

        // 随机生成当前红包的金额
        let amount = Math.random() * (max - min) + min;
        amount = Math.floor(amount); // 保留两位小数

        // 确保剩余金额足够分配
        if (remainingAmount - amount < (remainingPackets - 1) * min) {
          amount = Math.floor(remainingAmount - (remainingPackets - 1) * min);
        }

        packets.push(amount);
        remainingAmount -= amount;
        remainingPackets--;
      }
    }

    return packets;
  }
  /**
   * 气运增加
   * @param value
   */
  public addQiyun() {
    let config = this.getCurrentTask();
    if (!config) {
      log.error("当前任务不存在");
      return;
    }
    if (this._currentQiyun > config.doneNum) {
      return;
    }
    this._currentQiyun += config.rewardNum;
    if (this._currentQiyun >= config.doneNum) {
      this._currentQiyun = config.doneNum;
      MsgMgr.emit(QiyunXiaoyouxiEvent.QIYUN_ENOUGH, this._currentQiyun);
    }
    MsgMgr.emit(QiyunXiaoyouxiEvent.QIYUN_ADD, config.rewardNum, config.doneNum);
    return;
  }

  /** 获取任务总数 */
  public getTaskNum(): number {
    return this._taskList.length;
  }

  /** 设置当前任务索引 */
  public setCurrentTaskIndex(index: number) {
    this._currentTaskIndex = index;
    MsgMgr.emit(QiyunXiaoyouxiEvent.LEVEL_CHANGE);
    const configMonsterMatrix = FightModule.data.getConfigMonsterMatrix(40);
    let maxValue = this.getCurrentTask().doneNum;
    let packages = configMonsterMatrix.positionList.length;
    this._consumeList = this.generateRedPackets(maxValue, packages);
    this._consumeList = this._consumeList.sort((a, b) => a - b);
    let total = 0;
    for (let i = 0; i < this._consumeList.length; i++) {
      total += this._consumeList[i];
    }
  }

  /** 获取当前任务索引 */
  public getCurrentTaskIndex(): number {
    return this._currentTaskIndex;
  }

  /**  获取当前任务 */
  public getCurrentTask(): IConfigGuideGameTast {
    return this._taskList[this._currentTaskIndex];
  }

  /**  获取任务配置 */
  public getTask(): IConfigGuideGameTast[] {
    return this._taskList;
  }

  /**
   * 获取对话内容
   */
  public getTalkContent(type: QiyunXiaoyouxiTalkType): string {
    return JsonMgr.instance.jsonList.c_guideGameTalk[type]?.msg ?? "...";
  }
}
