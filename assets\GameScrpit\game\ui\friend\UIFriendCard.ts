import { _decorator, Label, math, Node, Sprite, UIOpacity } from "cc";
import { FriendMessage } from "../../net/protocol/Friend";
import Formate from "../../../lib/utils/Formate";
import { FriendModule } from "../../../module/friend/FriendModule";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { FriendRaceColorBG, FriendRaceColorFG } from "../../../module/friend/FriendConstant";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import ResMgr from "../../../lib/common/ResMgr";
import { IConfigFriend } from "../../JsonDefine";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

@ccclass("UIFriendCard")
export class UIFriendCard extends BaseCtrl {
  private _friendId: number;
  private _chat_add: number;
  @property(Node)
  private friendImg: Node;
  @property(Label)
  private friendship: Label;
  @property(Label)
  private talent: Label;
  @property(Label)
  private friend_name;
  @property(Sprite)
  private background;
  @property(Sprite)
  private front;
  @property(Node)
  private labels;
  @property(Node)
  private chat: Node;
  start() {}

  update(deltaTime: number) {}
  public init(...args: any) {
    this._friendId = args[0];
    this._chat_add = args[1];
    this.refreshUI();
    MsgMgr.on(MsgEnum.ON_FRIEND_UPDATE, this.onrefreshData, this);
  }
  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_FRIEND_UPDATE, this.onrefreshData, this);
  }
  private onrefreshData() {
    let friendMessage: FriendMessage = FriendModule.data.getFriendMessage(this._friendId);
    if (this._chat_add) {
      this.chat.active = true;
      this.chat.getChildByName("chat_add").getComponent(Label).string = `+${Formate.format(this._chat_add)}`;
    } else {
      this.chat.active = false;
      if (friendMessage) {
        this.friendship.string = `${Formate.format(friendMessage.karma)}`;
        this.talent.string = `${Formate.format(friendMessage.destiny)}`;
        this.node.getComponent(UIOpacity).opacity = 255;
      } else {
        this.labels.active = false;
        this.node.getComponentsInChildren(Sprite).forEach((item) => {
          item.color = math.color("#6E6E6E");
        });
      }
    }
  }
  public refreshUI() {
    this.onrefreshData();
    let friend: IConfigFriend = FriendModule.config.getFriendById(this._friendId);
    if (friend) {
      this.friend_name.string = friend.name;
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_COMMON_FRIEND,
        `half/friend_${friend.id}`,
        this.friendImg.getComponent(Sprite)
      );

      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_COMMON_FRIENDICON,
        `atlas_colors/${FriendRaceColorBG[friend.color]}`,
        this.background
      );
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_COMMON_FRIENDICON,
        `atlas_colors/${FriendRaceColorFG[friend.color]}`,
        this.front
      );
    }
  }
}
