import { _decorator, Component, director, Scene, EditBox, Node } from "cc";
import { PlayerModule } from "../GameScrpit/module/player/PlayerModule";
import { PlayerCreateMessage, PlayerDataMessage } from "../GameScrpit/game/net/protocol/Player";
import { JsonMgr } from "../GameScrpit/game/mgr/JsonMgr";
import { AssetMgr } from "../platform/src/ResHelper";
import { instantiate } from "cc";
import { sp } from "cc";
import { AudioMgr, AudioName } from "../platform/src/AudioHelper";
import { TipsMgr } from "../platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

enum SexEnum {
  Boy = 1,
  Girl = 2,
}

@ccclass("SceneCreatePlayer")
export class SceneCreatePlayer extends Component {
  @property(EditBox)
  editName: EditBox;

  @property(Node)
  btnBoy: Node;

  @property(Node)
  btnGirl: Node;

  @property(Node)
  nodeRole: Node;

  // 当前选中类型
  private _sex: SexEnum = SexEnum.Boy;

  // 资源管理器
  private _resMgr: AssetMgr = null;

  private _nameList: string[] = [];

  protected onLoad(): void {
    this._resMgr = AssetMgr.create();
  }

  protected onDestroy(): void {
    this._resMgr.release();
  }

  start() {
    this.onBtnTouzi();
    this.onBtnBoy();
  }

  private changeSex(sex: SexEnum) {
    this._sex = sex;
    this.btnBoy.getChildByName("enable").active = sex == SexEnum.Boy;
    this.btnBoy.getChildByName("unenable").active = sex != SexEnum.Boy;
    this.btnGirl.getChildByName("enable").active = sex == SexEnum.Girl;
    this.btnGirl.getChildByName("unenable").active = sex != SexEnum.Girl;

    this.chageRole();
  }

  onBtnBoy() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.changeSex(SexEnum.Boy);
  }

  onBtnGirl() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.changeSex(SexEnum.Girl);
  }

  onBtnTouzi() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._nameList.length > 0) {
      this.editName.string = this._nameList.shift();
    } else {
      PlayerModule.api.getRandomNickName((name: string[]) => {
        this._nameList = name;
        this.editName.string = this._nameList?.shift() ?? "";
      });
    }
  }

  onBtnStart() {
    if (this.editName.string == "") {
      return;
    }

    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let data: PlayerCreateMessage = {
      name: this.editName.string,
      sex: this._sex,
    };
    TipsMgr.setEnableTouch(false, 3);
    PlayerModule.api.postCreatrPlayer(
      data,
      (res: PlayerDataMessage) => {
        director.loadScene("SceneLoading", (error: null | Error, scene?: Scene) => {
          TipsMgr.setEnableTouch(true);
          if (error) {
            log.error(error);
            return;
          }
          log.log("Scene Loaded");
        });
      },
      (errorCode: number, msg: string[], data: any): boolean => {
        TipsMgr.showErrX(errorCode, msg);
        this.editName.string = "";
        return true;
      }
    );
  }

  chageRole() {
    try {
      const _skinIdList: number[] = [1701, 1711];
      let id = _skinIdList[this._sex - 1];

      let skindb = JsonMgr.instance.jsonList.c_leaderSkin[id];
      let roledb = JsonMgr.instance.jsonList.c_spineShow[skindb.spineId];

      let sList = roledb.prefabPath.split("?");

      this._resMgr.loadPrefab(sList[0], sList[1], (prefab) => {
        this.nodeRole.destroyAllChildren();
        let roleSpine = instantiate(prefab);
        this.nodeRole.addChild(roleSpine);
        roleSpine.getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);
      });
    } catch (error) {
      log.error("角色加载失败", error);
    }
  }
}
