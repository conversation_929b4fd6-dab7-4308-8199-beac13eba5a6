import { GameData } from "../../game/GameData";
import { UIPost } from "../../game/ui/ui_post/UIPost";
import { UIPostBet } from "../../game/ui/ui_post/UIPostBet";
import { UIPostLevelUp } from "../../game/ui/ui_post/UIPostLevelUp";
import { UIPostSpeed } from "../../game/ui/ui_post/UIPostSpeed";
import data from "../../lib/data/data";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { PostApi } from "./postApi";
import { PostData } from "./postData";
import { PostService } from "./PostService";

export class PostModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): PostModule {
    if (!GameData.instance.PostModule) {
      GameData.instance.PostModule = new PostModule();
      GameData.instance.PostModule.onViewLoad();
    }
    return GameData.instance.PostModule;
  }

  private _data: PostData = new PostData();
  private _api: PostApi = new PostApi();
  private _service: PostService = new PostService();

  private _tickId: number = null;

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }

  public init(data?: any, completedCallback?: Function) {
    this._data = new PostData();
    this._api = new PostApi();
    this._service = new PostService();

    PostModule.api.getPostInfo((data) => {
      PostModule.service.init();

      this._tickId = TickerMgr.setInterval(1, this.upPostMessage.bind(this), false);

      completedCallback && completedCallback();
    });
  }

  /**更新驿站数据 */
  private upPostMessage() {
    if (PostModule.data?.lastDeadline == 0) {
      return;
    }
    if (TimeUtils.serverTime < PostModule.data?.lastDeadline) {
      return;
    }
    if (PostModule.data?.rewardList.length != 0) {
      return;
    }
    PostModule.api.getPostInfo((data) => {});
  }

  protected onViewLoad(): void {
    let data = new Recording();
    data = {
      node: UIPost,
      uiName: "UIPost",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UIPostLevelUp,
      uiName: "UIPostLevelUp",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UIPostSpeed,
      uiName: "UIPostSpeed",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UIPostBet,
      uiName: "UIPostBet",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);
  }
}
