import { <PERSON><PERSON><PERSON>and<PERSON> } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { CommLongListMessage } from "../../game/net/protocol/Comm";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { ActivityModule } from "./ActivityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ActivitySubscriber {
  private pushBuyActivityCallback(rs: LongValue) {
    log.log("GoodsRedeemResponse GoodsRedeemResponse", rs);
  }

  private onActivityUp(rs: CommLongListMessage) {
    log.log("GoodsRedeemResponse GoodsRedeemResponse", rs);
    ActivityModule.service.httpGetAllActivity();
  }
  public register() {
    // 资源更新
    ApiHandler.instance.subscribe(LongValue, ActivityCmd.monthCardBuyNotice, this.pushBuyActivityCallback);
    ApiHandler.instance.subscribe(CommLongListMessage, ActivityCmd.ActivityUp, this.onActivityUp);
  }

  public unRegister() {
    ApiHandler.instance.unSubscribe(ActivityCmd.monthCardBuyNotice, this.pushBuyActivityCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.ActivityUp, this.onActivityUp);
  }
}
