[{"__type__": "cc.Prefab", "_name": "path_thief_101", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "path_thief_101", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 10}, {"__id__": 14}, {"__id__": 18}, {"__id__": 22}, {"__id__": 26}, {"__id__": 30}, {"__id__": 34}, {"__id__": 38}, {"__id__": 42}, {"__id__": 46}, {"__id__": 50}, {"__id__": 54}, {"__id__": 58}, {"__id__": 62}, {"__id__": 66}], "_active": true, "_components": [{"__id__": 126}, {"__id__": 128}], "_prefab": {"__id__": 130}, "_lpos": {"__type__": "cc.Vec3", "x": -1375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "A1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": {"__id__": 5}, "_lpos": {"__type__": "cc.Vec3", "x": -202.276, "y": 225.004, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1heJ8LOtEv59PRStg6n5k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5l9Flm0JIHr/nJ4rf+nNQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "A2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": -158.762, "y": 237.169, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": {"__id__": 8}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1vLQHByhJ8YZAlu+/Q57+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67k6Ca76RGQ6DLAwIJZGHq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Arun1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 11}], "_prefab": {"__id__": 13}, "_lpos": {"__type__": "cc.Vec3", "x": -220.132, "y": 76.812, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 12}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4ayq4zaVJLBaI4AExuX4vU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33XWWsTmJNh5iZqXfS5z38", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": -168.459, "y": -91.239, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 16}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13hpvNa0BNprKbgY9v1Yha"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dbkBoYHJ9OqbDb03wltN+p", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 19}], "_prefab": {"__id__": 21}, "_lpos": {"__type__": "cc.Vec3", "x": -97.709, "y": -184.185, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5YcvwZk9MaZxd5PVo4wlS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e3Hn96vMZOX7VCOsnCIpv+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 23}], "_prefab": {"__id__": 25}, "_lpos": {"__type__": "cc.Vec3", "x": -75.277, "y": -293.879, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 24}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "068AEIwgFHu6nvSenaa38L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5Ce1uQ9tOA7RwPepu+1Pu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 27}], "_prefab": {"__id__": 29}, "_lpos": {"__type__": "cc.Vec3", "x": 63.416, "y": -314.75, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 28}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "804wZEnnND8rYfdmJKfU1M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0cBg4bfNdIQorKDsaYh+oh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": 156.957, "y": -260.303, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 32}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2WleSgcJDT5IXqDpWa0Lk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "57mu6nujFPC5lv8xpiADJO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 35}], "_prefab": {"__id__": 37}, "_lpos": {"__type__": "cc.Vec3", "x": 121.471, "y": -315.66, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 36}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83cYQrB+ZM7IAnQrkm+JEh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a2cZmFq1tMQrls4nGCRbpw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 39}], "_prefab": {"__id__": 41}, "_lpos": {"__type__": "cc.Vec3", "x": 183.215, "y": -390.888, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 40}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a53mYJuAZKjKDVIriH9wY1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4+1L8O6ZL4YgMyV6D8vm+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run8", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 43}], "_prefab": {"__id__": 45}, "_lpos": {"__type__": "cc.Vec3", "x": 365.609, "y": -457.6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 44}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90dx603/5OPqPD7zD2bWqM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "21GIKS8Y1Cg7uECbAuoSrV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run9", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 47}], "_prefab": {"__id__": 49}, "_lpos": {"__type__": "cc.Vec3", "x": 361.351, "y": -600.96, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 48}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecI967wWJFf5qmRAvCUYeo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76m9BQMT5H2K7QZdkxw/0d", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run10", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 51}], "_prefab": {"__id__": 53}, "_lpos": {"__type__": "cc.Vec3", "x": 50.501, "y": -576.12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 52}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08BQoUN8tLeryM1b07YFgk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8EqtQ47dDvqjwCGbe1+rx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run11", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": 324.446, "y": -612.315, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 56}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2X4d6W+lAtry+/EjIeWZx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eDXsiWNZC2rt6My1+L7Sl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run12", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 59}], "_prefab": {"__id__": 61}, "_lpos": {"__type__": "cc.Vec3", "x": 262.702, "y": -669.091, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 60}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efIAfo6r9OgK0pNFwieYao"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "18nyz95KtMArEjP+p5vYxV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "run13", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 63}], "_prefab": {"__id__": 65}, "_lpos": {"__type__": "cc.Vec3", "x": 196.7, "y": -781.224, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ef43elQZBbY7+o72nqjr1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11Kg2lA9dBSr/pvdE4kLFq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "render", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 67}, {"__id__": 73}, {"__id__": 91}, {"__id__": 97}], "_active": true, "_components": [{"__id__": 123}], "_prefab": {"__id__": 125}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "skt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 70}], "_prefab": {"__id__": 72}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 2, "y": 2, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 69}, "_contentSize": {"__type__": "cc.Size", "width": 64.75, "height": 54.810001373291016}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.4375289563506727, "y": 0.0540047430027868}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daaAGGI9tJnLzEJ4Fy17Tn"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 71}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "103aebbe-e359-4759-bf29-de89d9e724a5", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "atk_1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5m0P3ZeVF46BBLEIeDTy0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46weJmRJBHJ4Pf3PL1TlAD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "hp_layer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [{"__id__": 74}, {"__id__": 82}], "_active": true, "_components": [{"__id__": 88}], "_prefab": {"__id__": 90}, "_lpos": {"__type__": "cc.Vec3", "x": 9, "y": 111, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 73}, "_children": [], "_active": true, "_components": [{"__id__": 75}, {"__id__": 77}, {"__id__": 79}], "_prefab": {"__id__": 81}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 76}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52brzY0vhKYaLax19X6vBD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 78}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9ccb7f6b-f66f-4cca-ad99-49845ccf6f78@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0196vWquxNyq496fZmTskF"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 80}, "_barSprite": null, "_mode": 0, "_totalLength": 130, "_progress": 1, "_reverse": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d16cro2aRFtLc/YND5EOJz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "47ceJt9vBLQIJ3oTdGLJml", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 73}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 85}], "_prefab": {"__id__": 87}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 84}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0x4jbqN5G1YVNboGmFHHU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 86}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4a850d89-c729-4b74-9684-c46cba1b31de@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68GPJSSDlE8pxRCd1WiDcm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8PLlQLztBnaoM4czyEiWS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 89}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87ToaEV19OsbeVChHM6MPv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7fZ37MwDVNcI7LYTVUtoOv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_language", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [], "_active": false, "_components": [{"__id__": 92}, {"__id__": 94}], "_prefab": {"__id__": 96}, "_lpos": {"__type__": "cc.Vec3", "x": 67.229, "y": 58.954, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 93}, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 29.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4A4b38gtCq7qJ74Pb9BGu"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 95}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "别打我了，别打我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92OQxDgz1Aiq/jKsOT4fUC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5erIjf5RxGqaELBFrpDhvE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "debuff_num", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [{"__id__": 98}, {"__id__": 104}, {"__id__": 110}], "_active": false, "_components": [{"__id__": 116}, {"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 21.384, "y": 141.544, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_message", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 99}, {"__id__": 101}], "_prefab": {"__id__": 103}, "_lpos": {"__type__": "cc.Vec3", "x": -182.5, "y": 2.2737367544323206e-13, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999996, "y": 0.9999999999999996, "z": 0.9999999999999997}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 100}, "_contentSize": {"__type__": "cc.Size", "width": 152, "height": 39.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ejZT3555L9o8C4qL97yUg"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 102}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 140, "g": 71, "b": 4, "a": 255}, "_string": "贼人偷走了", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 194, "g": 96, "b": 49, "a": 255}, "_outlineWidth": 1, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2atiBsajtKubs5ToX9Eh3K"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f7TZtdt+dL26hZCJEkLZux", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "item_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 105}, {"__id__": 107}], "_prefab": {"__id__": 109}, "_lpos": {"__type__": "cc.Vec3", "x": -84, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": {"__id__": 106}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27cF+4mnhB1YSw8c4mE9NE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": {"__id__": 108}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5ddc7c6-0144-4433-8eb2-53f32cd574f2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0b0CZ69VhOlrtfMpfD6ciJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d7eFTJRw1JaaqAELR0t+n8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "num", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 111}, {"__id__": 113}], "_prefab": {"__id__": 115}, "_lpos": {"__type__": "cc.Vec3", "x": 98.5, "y": 2.2737367544323206e-13, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999996, "y": 0.9999999999999996, "z": 0.9999999999999997}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 112}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 39.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecIKS49T9ARpMJtD64d9Ln"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 114}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 140, "g": 71, "b": 4, "a": 255}, "_string": "+1234567890亿万千百", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 194, "g": 96, "b": 49, "a": 255}, "_outlineWidth": 1, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58mEOgVCZMPLYZj98e8Cso"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6RP6rWbBBCq159GIc3CO0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 117}, "_contentSize": {"__type__": "cc.Size", "width": 557, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09dq63cgtOYIjHh0J/Lw4M"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 119}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 20, "_paddingRight": 20, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdBvkLL3hLdrlspGi5vnn3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 121}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54JPHdU1BHlbpIepn+arYg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5cJI6v6ihF86WCF8+myOYj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": {"__id__": 124}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfn1eUmItKdIqDdo2gdBKj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "784a/ws8lKxpxSQa8OD3IL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 127}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14Ql3t8qNEf6IqN7PKv694"}, {"__type__": "d53dc1zHNBC9Zt0oWEnClhi", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 129}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bSb1U0v5DgpGzO64YQjrL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9fXa+xNCxKjYfh7/ezNR9P", "instance": null, "targetOverrides": null}]