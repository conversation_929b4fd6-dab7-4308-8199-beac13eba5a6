{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "2a3f4fab-8ede-4d8c-9dbe-30b79ba3ccde", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "2a3f4fab-8ede-4d8c-9dbe-30b79ba3ccde@6c48a", "displayName": "btn_nv_weixuanzhong", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "2a3f4fab-8ede-4d8c-9dbe-30b79ba3ccde", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "2a3f4fab-8ede-4d8c-9dbe-30b79ba3ccde@f9941", "displayName": "btn_nv_weixuanzhong", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 209, "height": 210, "rawWidth": 209, "rawHeight": 210, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-104.5, -105, 0, 104.5, -105, 0, -104.5, 105, 0, 104.5, 105, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 210, 209, 210, 0, 0, 209, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-104.5, -105, 0], "maxPos": [104.5, 105, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "2a3f4fab-8ede-4d8c-9dbe-30b79ba3ccde@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "2a3f4fab-8ede-4d8c-9dbe-30b79ba3ccde@6c48a"}}