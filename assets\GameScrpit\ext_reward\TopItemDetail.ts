import { _decorator, Color, Component, Label, math, Node, Sprite, tween, UITransform, Vec3 } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { BundleEnum } from "../game/bundleEnum/BundleEnum";
import { JsonMgr } from "../game/mgr/JsonMgr";
import ResMgr from "../lib/common/ResMgr";
import { StartUp } from "../lib/StartUp";
import { UIMgr } from "../lib/ui/UIMgr";
import Formate from "../lib/utils/Formate";
import { FriendModule } from "../module/friend/FriendModule";
import { HeroModule } from "../module/hero/HeroModule";
import { PetModule } from "../module/pet/PetModule";
import { PlayerModule } from "../module/player/PlayerModule";
import { SoulModule } from "../module/soul/SoulModule";
import { NodeTool } from "../lib/utils/NodeTool";
const { ccclass, property } = _decorator;
const qualityList = ["", "S0325", "S0328", "S0326", "S0324", "S0327"];
@ccclass("TopItemDetail")
export class TopItemDetail extends BaseCtrl {
  @property(Node)
  private nodeTips: Node;
  @property(Node)
  private bgTipsContent: Node;
  @property(Sprite)
  private bgItemColor: Sprite;
  @property(Sprite)
  private bgItemIcon: Sprite;
  @property(Label)
  private lblItemName: Label;
  @property(Label)
  private lblItemNum: Label;
  @property(Label)
  private lblItemInfo: Label;

  @property({ type: Color, tooltip: "品质对应字体颜色" }) public itemColors: Color[] = [
    math.color("#92d564"),
    math.color("#70bff7"),
    math.color("#d0b0f7"),
    math.color("#f4b500"),
    math.color("#ffa9a9"),
  ];
  //"#115b21", "#165fa8", "#7529bd", "#965509", "#c53737"
  @property({ type: Color, tooltip: "品质对应字体描边颜色" }) public itemOutlineColors: Color[] = [
    math.color("#115b21"),
    math.color("#165fa8"),
    math.color("#7529bd"),
    math.color("#965509"),
    math.color("#c53737"),
  ];

  private _itemId: number;
  private _tipsPaddingRight: number = 0;
  private _tipsPaddingLeft: number = 0;
  private _toWorldX: number;
  private _toWorldY: number;
  start() {
    super.start();
    this.setTips(this._toWorldX, this._toWorldY);
  }

  init(args: any): void {
    this._itemId = args.itemId;
    this._toWorldX = args.toWorldX;
    this._toWorldY = args.toWorldY;
  }

  update(deltaTime: number) {}
  private setTips(toWorldX: number, toWorldY: number): void {
    // this.indicator.setWorldPosition(new Vec3(this.toWorldX, this.toWorldY - 2));
    // let finalWorldx = toWorldX;
    // let finalWorldy = toWorldY;
    // const colors: any[] = [, "42a200", "70bff7", "ae6cfd", "f4b500", "e93515"];
    let inputPosition = new Vec3(toWorldX, toWorldY);
    if (this.nodeTips.parent) {
      this.nodeTips.parent.removeChild(this.nodeTips);
    }

    TipsMgr.getTipsRoot().addChild(this.nodeTips);
    this.nodeTips.active = true;

    let info = JsonMgr.instance.getConfigItem(this._itemId);
    ResMgr.loadImage(`${BundleEnum.BUNDLE_COMMON_ITEM}?autoItem/${this.getItemQuality(info.color)}`, this.bgItemColor);
    // 设置图标
    ResMgr.loadImage(`${BundleEnum.BUNDLE_COMMON_ITEM}?autoItem/${info.iconId}`, this.bgItemIcon);
    this.lblItemName.string = info.name;
    this.lblItemName.color = math.color(this.itemColors[info.color - 1]);
    this.lblItemName.outlineColor = math.color(this.itemOutlineColors[info.color - 1]);

    let itemNum = 0;

    // 1.资源
    // 2.道具
    // 3.门客
    // 4.徒弟
    // 5.珍兽
    // 6.挚友
    // 7.领地装扮
    // 8.坐骑道具
    // 9.活动道具
    // 10.武魂道具
    // 11.主角皮肤
    // 12.头像、气泡、称号、皮肤
    switch (info.goodsType) {
      case 1:
      case 2:
        itemNum = PlayerModule.data.getItemNum(this._itemId);
        break;
      case 3:
        itemNum = HeroModule.data.getHeroMessage(this._itemId) ? 1 : 0;
        break;
      case 4:
        itemNum = PetModule.data.getPet(this._itemId) ? 1 : 0;
        break;
      case 6:
        itemNum = FriendModule.data.getFriendMessage(this._itemId) ? 1 : 0;
        break;
      case 10:
        itemNum = SoulModule.data.getSoulNumById(this._itemId);
      default:
        break;
    }
    this.lblItemNum.string = `拥有数量：${Formate.format(itemNum)}`;
    this.lblItemInfo.string = info.des;
    this.bgItemColor.color = math.color("ffffff");
    this.bgItemIcon.color = math.color("ffffff");
    this.lblItemNum.color = math.color("e9e9e9");
    this.lblItemInfo.color = math.color("DFEAFF");

    let currentNode = UIMgr.instance.getLastPageInfo().uiNode?.node;
    let borderRight = StartUp.instance.getVisibleSize().width;
    let borderLeft = 0;
    let borderTop = 0;
    if (currentNode) {
      borderTop = NodeTool.getNodeTop(currentNode);
      borderRight = NodeTool.getNodeRight(currentNode);
      inputPosition = currentNode.getComponent(UITransform).convertToNodeSpaceAR(inputPosition);
      borderLeft = NodeTool.getNodeLeft(currentNode);
    }
    let right =
      this.bgTipsContent.getComponent(UITransform).width * (1 - this.bgTipsContent.getComponent(UITransform).anchorX);
    if (inputPosition.x + right > borderRight - this._tipsPaddingRight) {
      inputPosition.x = borderRight - this._tipsPaddingRight - right;
    }
    let left =
      this.bgTipsContent.getComponent(UITransform).width * this.bgTipsContent.getComponent(UITransform).anchorX;
    if (inputPosition.x - left < borderLeft + this._tipsPaddingLeft) {
      inputPosition.x = borderLeft + this._tipsPaddingLeft;
    }
    let top =
      this.bgTipsContent.getComponent(UITransform).height * (1 - this.bgTipsContent.getComponent(UITransform).anchorY);
    if (inputPosition.y + top > borderTop) {
      inputPosition.y = borderTop - top;
    }

    let worldPosition = new Vec3(inputPosition.x, inputPosition.y);
    if (currentNode) {
      worldPosition = currentNode.getComponent(UITransform).convertToWorldSpaceAR(inputPosition);
    }

    this.bgTipsContent.setWorldPosition(worldPosition);
    tween(this.bgTipsContent)
      .set({ scale: new Vec3(0.5, 0.5, 0.5) })
      .to(0.1, { scale: new Vec3(1, 1, 1) })
      .start();
    tween(this.bgTipsContent)
      .set({ worldPosition: new Vec3(toWorldX, toWorldY) })
      .to(0.1, { worldPosition: worldPosition })
      .start();
  }
  /**获取道具品质框 */
  public getItemQuality(quality: number) {
    let key = qualityList[quality];
    return key;
  }
  private onClickBlank() {
    if (this.nodeTips.parent) {
      this.nodeTips.parent.removeChild(this.nodeTips);
    }
    // this.nodeTips.active = false;
    this.closeBack();
  }
}
