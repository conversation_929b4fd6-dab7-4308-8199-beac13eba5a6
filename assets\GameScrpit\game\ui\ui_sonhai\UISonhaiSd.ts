import { _decorator, Component, isValid, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { sonhai_activityId, SonhaiModule } from "../../../module/sonhai/SonhaiModule";
import { Sleep } from "../../GameDefine";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { RedeemRequest, RedeemResponse } from "../../net/protocol/Activity";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import TipMgr from "../../../lib/tips/TipMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { dtTime } from "../../BoutStartUp";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { SonhaiAudioName } from "../../../module/sonhai/SonhaiConfig";
import { BuyConfirm } from "../UIBuyConfirm";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { RedeemPackVO } from "../../../module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const LbId: number = 11101301;
const db_info: string = "db_info";
@ccclass("UISonhaiSd")
export class UISonhaiSd extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_SONHAI}?prefab/ui/UISonhaiSd`;
  }

  private _list: Array<RedeemPackVO> = null;

  private _needItemId: number = null;

  private _tickMgrIdList: number[] = [];

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setMyItemNum, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setMyItemNum, this);
  }

  protected async onEvtShow() {
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    log.log("db=====", db);
    for (let i = 0; i < db.redeemList.length; i++) {
      let list = db.redeemList[i];
      let id = list[0].id;
      if (LbId == id) {
        this._list = list;
        this._needItemId = list[0].cost[0];
      }
    }

    let startTime = TimeUtils.formatTimestamp(db.startTime, "YYYY/MM/DD");
    let publicityTime = TimeUtils.formatTimestamp(db.publicityTime, "YYYY/MM/DD");
    this.getNode("title_time").getComponent(Label).string = "活动时间:" + startTime + "--" + publicityTime;

    this.loadLb();
    this.setMyItemNum();
    this.setMyItemIcon();
  }

  private setMyItemIcon() {
    ToolExt.setItemIcon(this.getNode("item_title"), this._needItemId);
  }

  private setMyItemNum() {
    this.getNode("myItenm").getComponent(Label).string = PlayerModule.data.getItemNum(this._needItemId) + "";
  }

  private async loadLb() {
    for (let i = 0; i < this._list.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) {
        return;
      }

      let node = this.getNode("sd_content").children[i];
      if (!node) {
        node = ToolExt.clone(this.getNode("sd_item"), this);
        this.getNode("sd_content").addChild(node);
        node.active = true;
      }

      if (i % 3 == 0) {
        node["bg_sdtc_neidi_xing"].active = false;
      } else {
        node["bg_sdtc_neidi_xing"].active = true;
      }

      let info = this._list[i];
      let rewardList = info.rewardList;
      FmUtils.setItemNode(node["Item"], rewardList[0], rewardList[1]);
      let redem_num = SonhaiModule.data.redeemMap[info.id] || 0;
      node["lbl_buy_max"].getComponent(Label).string =
        ToolExt.getMaxtypeLab(info.maxtype) + `(${info.max - redem_num}/${info.max})`;

      if (redem_num >= info.max) {
        node["btn_goumai"].active = false;
        node["sd_bg_yishouqing"].active = true;
      } else {
        node["btn_goumai"].active = true;
        node["sd_bg_yishouqing"].active = false;
      }

      if (info.maxtype == 4 && PlayerModule.service.isShopBuy(info.rewardList[0]) == false) {
        node["btn_goumai"].active = false;
        node["sd_bg_yishouqing"].active = true;
      }

      node["btn_goumai"][db_info] = info;

      let unlockList = info.unlockList;
      if (unlockList.length < 2) {
        unlockList = [0, 0];
      }
      let limitMap = SonhaiModule.data.limitMap;
      let unNum = limitMap[unlockList[0]] || 0;
      if (unNum >= unlockList[1]) {
        node["unlock_mask"].active = false;
      } else {
        node["unlock_mask"].active = true;
        let un_itemdb = JsonMgr.instance.getConfigItem(unlockList[0]);
        node["lbl_unlock"].getComponent(Label).string = "累计获得\n" + unlockList[1] + `${un_itemdb.name}\n` + "解锁";
      }

      let cost = info.cost;
      ToolExt.setItemIcon(node["but_item_icon"], cost[0]);
      node["buy_need_num"].getComponent(Label).string = cost[1];
    }
  }

  private on_click_btn_goumai(event) {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击兑换按钮);
    let node: Node = event.node;
    let info: RedeemPackVO = node[db_info];

    let id = info.cost[0];
    let num = PlayerModule.data.getItemNum(id);
    if (num < info.cost[1]) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: id });
      return;
    }

    if (info.maxtype == 4 && PlayerModule.service.isShopBuy(info.rewardList[0]) == false) {
      let item = JsonMgr.instance.getConfigItem(id);
      TipMgr.showTip(`已拥有${item.name}，无法重复获得`);
      return;
    }

    if (info.price == 0) {
      let redem_num = SonhaiModule.data.redeemMap[info.id] || 0;
      let numLimit = info.max - redem_num;

      const buyConfirm: BuyConfirm = {
        itemInfo: info.rewardList,
        moneyInfo: info.cost,
        maxNum: numLimit,
      };

      UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
        if (resp.ok) {
          if (info.price == 0) {
            let param: RedeemRequest = {
              activityId: sonhai_activityId,
              redeemId: info.id,
              count: resp.num,
            };

            SonhaiModule.api.buyFixedPack(param, (res: RedeemResponse) => {
              let rewardList = res.rewardList;
              MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
              this.loadLb();
            });
            return;
          }
        }
      });

      return;
    }
  }

  protected onEvtClose(): void {
    for (let i = 0; i < this._tickMgrIdList.length; i++) {
      TickerMgr.clearTimeout(this._tickMgrIdList[i]);
    }
  }
}
