import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { BundleEnum } from "../../../game/bundleEnum/BundleEnum";
import { ApiHandler } from "../../../game/mgr/ApiHandler";
import { PupilSubCmd } from "../../../game/net/cmd/CmdData";
import { CommLongListMessage } from "../../../game/net/protocol/Comm";
import { PupilModule } from "./PupilModule";
import { Sleep } from "../../../game/GameDefine";

export class PupilSubScriber {
  private async beMarriedCallback(data: CommLongListMessage) {
    // 只更新变动的弟子
    for (let i = 0; i < data.longList.length; i++) {
      PupilModule.api.getPupil(data.longList[i], (pupilInfo) => {
        // MsgMgr.emit(MsgEnum.ON_PUPIL_MARRY_SEND, pupilInfo);
        TipsMgr.topRouteCtrl.showPrefab(BundleEnum.BUNDLE_EXT_REWARD, "prefab/top/TopPupilMarry", pupilInfo);
      });

      // 防止多个叠加
      await Sleep(1.333);
    }
  }

  public register() {
    /**弟子被结伴 */
    ApiHandler.instance.subscribe(CommLongListMessage, PupilSubCmd.beMarried, this.beMarriedCallback);
  }

  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(PupilSubCmd.beMarried, this.beMarriedCallback);
  }
}
