import MsgEnum from "../../../game/event/MsgEnum";
import { JsonMgr } from "../../../game/mgr/JsonMgr";
import { EventTrainMessage, ThiefEventMessage } from "../../../game/net/protocol/WorldEvent";
import MsgMgr from "../../../lib/event/MsgMgr";
import { times } from "../../../lib/utils/NumbersUtils";
import { EventActionModule } from "./EventActionModule";

export class EventActionData {
  private _eventTrainMessage: EventTrainMessage;

  public get eventTrainMessage() {
    if (!this._eventTrainMessage) {
      this._eventTrainMessage = {
        /** 事件集合 */
        eventMap: {},
        /** 最近一次触发循环任务的时间戳 */
        lastUpdateTime: 0,
      };
    }

    return this._eventTrainMessage;
  }

  public set eventTrainMessage(value) {
    this._eventTrainMessage = value;
    MsgMgr.emit(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this._eventTrainMessage.eventMap);
  }

  public get eventProgressMap() {
    return this.eventTrainMessage.eventMap;
  }

  public setEventProgressMap(obj) {
    this._eventTrainMessage.eventMap[obj.id].progress = obj.progress;
    MsgMgr.emit(MsgEnum.ON_SANJIE_THIEF_HP_UPDATE);
  }
}
