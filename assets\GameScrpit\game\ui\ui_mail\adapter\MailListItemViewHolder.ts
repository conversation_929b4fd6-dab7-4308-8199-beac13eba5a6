import { _decorator, Label, Node } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { MailMessage } from "../../../net/protocol/Mail";
import { TimeUtils } from "../../../../lib/utils/TimeUtils";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { MailRouteItem } from "../../../../module/mail/MailRoute";
import { MailAudioName } from "db://assets/GameScrpit/module/mail/MailConfig";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";

const { ccclass, property } = _decorator;
@ccclass("MailListItemViewHolder")
export class MailListItemViewHolder extends ViewHolder {
  @property(Node)
  private nodeIcon: Node;

  @property(Label)
  private lblTitle: Label;

  @property(Label)
  private lblDate: Label;

  @property(Label)
  private lblDeadLine: Label;

  @property(Node)
  private bgReadTag: Node;

  @property(Node)
  private nodeMask: Node;

  private mailMsg: MailMessage;

  @property(Node)
  private nodeBadge: Node;

  public init() {}

  public updateData(position: number, mailMsg: MailMessage) {
    this.mailMsg = mailMsg;

    this.lblTitle.string = mailMsg.title;
    this.lblDate.string = TimeUtils.formatTimestamp(mailMsg.sendTs, "YYYY/MM/DD");

    this.bgReadTag.active = mailMsg.read;
    this.nodeIcon.getChildByName("bg_unread").active = !mailMsg.read;
    this.nodeIcon.getChildByName("bg_read").active = mailMsg.read;
    let day = Math.floor((mailMsg.overDateTs - TimeUtils.serverTime) / 86400000);
    day = Math.max(day, 1);
    this.lblDeadLine.string = `剩余${day}天`;
    this.nodeMask.active = mailMsg.read;
    this.nodeBadge.active = !mailMsg.read;
  }

  public onClick() {
    AudioMgr.instance.playEffect(MailAudioName.Effect.点开单个邮件);
    UIMgr.instance.showDialog(MailRouteItem.UIMailDetail, { mail: this.mailMsg });
  }
}
