import { _decorator, Label } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { IConfigHeroPicture } from "../../JsonDefine";
import { HeroModule } from "../../../module/hero/HeroModule";
import { LangMgr } from "../../mgr/LangMgr";
import { TuJianLevelAdapter } from "./adapter/TuJianLevelAdapter";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Wed Mar 12 2025 21:21:50 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/hero/UIHeroTujianDetail.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIHeroTujianDetail")
export class UIHeroTujianDetail extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HERO}?prefab/ui/UIHeroTujianDetail`;
  }

  private _data: IConfigHeroPicture;
  private _levelAdapter: TuJianLevelAdapter;
  //=================================================
  public init(args: any): void {
    super.init(args);
    this._data = args.data;
  }
  protected onEvtShow(): void {
    // do something
    this.refresh();
  }
  private refresh() {
    this.getNode("lbl_tip_title").getComponent(Label).string = this._data.name;

    let pictureLv = HeroModule.data.pictureMessage?.pictureMap[this._data.id] ?? 0;
    if (pictureLv == this._data.level.length) {
      this.getNode("lbl_upgrade_tips").active = false;
    } else {
      this.getNode("lbl_upgrade_tips").active = true;
      let strContent = LangMgr.txMsgCode(227, [], ".获得所有战将可激活和升级羁绊.");
      this.getNode("lbl_upgrade_tips").getComponent(Label).string = strContent;
    }
    //
    this._levelAdapter = new TuJianLevelAdapter(this.getNode("level_tip_viewholder"));
    this.getNode("node_level_list").getComponent(AdapterView).setAdapter(this._levelAdapter);
    this._levelAdapter.setData(this._data);
  }
}
