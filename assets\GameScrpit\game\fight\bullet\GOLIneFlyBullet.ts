import { _decorator, Node, UITransform, v2, Vec3 } from "cc";
import { CallBulletDetail } from "../FightDefine";
import RenderSection from "../../../lib/object/RenderSection";
import { JsonMgr } from "../../mgr/JsonMgr";
import { AnimationSection } from "../section/AnimationSection";
import DirectSection from "../../../lib/object/DirectSection";
import { GOBullet } from "./GOBullet";
import FightManager, { scaleList } from "../manager/FightManager";
import ToolExt from "../../common/ToolExt";
import BulletRenderSection from "../../../lib/object/BulletRenderSection";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass } = _decorator;

const speed: number = 1500;

@ccclass("GOLIneFlyBullet")
export class GOLIneFlyBullet extends GOBullet {
  private _distance: number;

  /**通用受击表现脚本 */
  public async onInitDetail(detail: CallBulletDetail) {
    super.onInitDetail(detail);
    //log.log("初始化飞行子弹");

    this.createAllSection(detail.bulletId);
  }

  private setBulletStartPos(detail: CallBulletDetail) {
    let render = detail.src.getSection(RenderSection).getRender();
    let newPos = ToolExt.transferOfAxes(render, this.parent);
    return v2(
      newPos.x + this.getSection(BulletRenderSection).getRender().getComponent(UITransform).width / 2,
      newPos.y +
        detail.src.getSection(RenderSection).getNode().getChildByName("crash").getComponent(UITransform).height / 2
    );
  }

  public async createAllSection(bulletId: number) {
    await new Promise(async (res) => {
      let param = {
        bulletId: bulletId,
        callBack: res,
      };
      this.createSection(BulletRenderSection, param);
    });
    this.setPosVec3(this.setBulletStartPos(this.detail));
    this.createSection(AnimationSection);
    this.createSection(DirectSection, this.getDir());
    this.getSection(AnimationSection).playAction(101, true);
  }

  public updateSelf(dt: any): void {
    let Render1 = this.getSection(BulletRenderSection).getRender();
    let Render2 = this.target.getSection(RenderSection).getRender();
    if (!Render1 || !Render2) {
      return;
    }
    this.updateFly(dt);
  }
  async updateFly(dt) {
    let oldVec2 = this.getPosVec2();
    let mul = this.src.getSection(DirectSection).getForward().x * speed * dt * scaleList[FightManager.instance.speed];

    if (!this._distance) {
      let pos1 = this.getPosVec3();
      let pos2 = this.target.getPosVec3();
      this._distance = Vec3.distance(pos1, pos2);
    } else {
      this._distance -= Math.abs(mul);
    }

    let newVec2 = v2(
      oldVec2.x + mul,
      oldVec2.y +
        this.src.getSection(DirectSection).getForward().y * speed * dt * scaleList[FightManager.instance.speed]
    );
    this.setPosVec3(newVec2);

    let bool = this.intersectsFight(
      this.getSection(BulletRenderSection).getRender(),
      this.target.getSection(RenderSection).getRender()
    );
    if (bool == true) {
      let bulletDB = JsonMgr.instance.jsonList.c_bulletShow[this.bulletId];
      if (bulletDB.addBullet == true) {
        this.loadHeBullet();
        this.remove();
      } else {
        await this.bulletHurt();
        this.remove();
      }
    }
  }
  private intersectsFight(bullet: Node, role: Node) {
    if (this._distance <= 0) {
      return true;
    } else {
      return false;
    }
  }

  private async bulletHurt() {
    this.hurtRoleMsg();
    this.atkRoleMsg();
    this.resolveBack(true);
  }
}
