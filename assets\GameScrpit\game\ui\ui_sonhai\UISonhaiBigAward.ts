import { _decorator, is<PERSON>alid, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { sonhai_activityId, SonhaiModule } from "../../../module/sonhai/SonhaiModule";
import { Sleep } from "../../GameDefine";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { SonhaiAudioName } from "../../../module/sonhai/SonhaiConfig";
import { dtTime } from "../../BoutStartUp";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { AdventureBigPrizeVO } from "../../../module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UISonhaiBigAward")
export class UISonhaiBigAward extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_SONHAI}?prefab/ui/UISonhaiBigAward`;
  }

  private _bigPrizeId: number = null;
  private _bigPrizeinfo = null;

  private _itemMap: Map<number, Node> = new Map();

  private _bigPrizeVO: AdventureBigPrizeVO = null;

  protected async onEvtShow() {
    /**先不显示确定按钮，等列表加载完了再显示 */
    this.getNode("btn_huangse").active = false;
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    this._bigPrizeVO = db.bigPrizeVO;
    this.load_item_big(db.bigPrizeVO);
  }

  private async load_item_big(db: AdventureBigPrizeVO) {
    log.log("大奖数据类====", db);
    let bigWinCount = SonhaiModule.data.adventureMessage.bigWinCount;
    let curBigPrizeId = SonhaiModule.data.adventureMessage.curBigPrizeId;

    let bigChosenMap = SonhaiModule.data.adventureMessage.bigChosenMap;

    let subList = db.subList;
    for (let i = 0; i < subList.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) return;

      let node = ToolExt.clone(this.getNode("btn_award_item"), this);
      this.getNode("content_big").addChild(node);
      node.active = true;

      let info = subList[i];
      let id = info.rewardList[0];
      let num = info.rewardList[1];

      if (SonhaiModule.data.curBigPrizeId == info.id) {
        this._bigPrizeinfo = info;
      }

      node["info"] = info;
      this._itemMap.set(info.id, node);

      let itemNode = node["btn_touch_Item"];
      FmUtils.setItemNode(itemNode, id, num);

      let count = bigChosenMap[info.id] || 0;
      node["lbl_chou_num"].getComponent(Label).string = `可抽取次数${count}/${info.max}`;

      /**是否有选中的奖 */
      if (curBigPrizeId == info.id) {
        this._bigPrizeId = info.id;
        node["icon_duigou"].active = true;
        node["huodong_di_7"].active = false;
        node["huodong_di_8"].active = true;
      } else {
        node["icon_duigou"].active = false;
        node["huodong_di_7"].active = true;
        node["huodong_di_8"].active = false;
      }

      /**是否解锁了 */
      if (bigWinCount >= info.unlock) {
        node["unlock_mask"].active = false;
      } else {
        node["unlock_mask"].active = true;
        node["lbl_unlock"].getComponent(Label).string = "累计获得\n" + info.unlock + "次\n" + "大奖解锁";
      }
    }

    if (this._bigPrizeinfo == null) {
      this._bigPrizeinfo = subList[0];
      this._bigPrizeId = subList[0].id;
      this._itemMap.forEach((val, key) => {
        if (key == this._bigPrizeId) {
          val["icon_duigou"].active = true;
          val["huodong_di_7"].active = false;
          val["huodong_di_8"].active = true;
        } else {
          val["icon_duigou"].active = false;
          val["huodong_di_7"].active = true;
          val["huodong_di_8"].active = false;
        }
      });
    }

    await Sleep(dtTime);
    this.getNode("btn_huangse").active = true;
  }

  on_click_btn_award_item(event) {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.选中奖励道具);
    let id = event.node["info"].id;
    if (this._bigPrizeId == id) {
      return;
    }
    this._bigPrizeinfo = event.node["info"];
    this._bigPrizeId = id;

    this._itemMap.forEach((val, key) => {
      if (key == this._bigPrizeId) {
        val["icon_duigou"].active = true;
        val["huodong_di_7"].active = false;
        val["huodong_di_8"].active = true;
      } else {
        val["icon_duigou"].active = false;
        val["huodong_di_7"].active = true;
        val["huodong_di_8"].active = false;
      }
    });
  }

  on_click_btn_touch_Item(event) {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.选中奖励道具);
    let id = event.node.parent["info"].id;
    if (this._bigPrizeId == id) {
      return;
    }
    this._bigPrizeinfo = event.node.parent["info"];
    this._bigPrizeId = id;

    this._itemMap.forEach((val, key) => {
      if (key == this._bigPrizeId) {
        val["icon_duigou"].active = true;
        val["huodong_di_7"].active = false;
        val["huodong_di_8"].active = true;
      } else {
        val["icon_duigou"].active = false;
        val["huodong_di_7"].active = true;
        val["huodong_di_8"].active = false;
      }
    });
  }

  private on_click_btn_huangse() {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击确认选择);
    let bigWinCount = SonhaiModule.data.adventureMessage.bigWinCount;
    if (bigWinCount < this._bigPrizeinfo.unlock) {
      return;
    }
    let bigChosenMap = SonhaiModule.data.adventureMessage.bigChosenMap;
    let count = bigChosenMap[this._bigPrizeinfo.id] || 0;
    let num = this._bigPrizeinfo.max - count;
    if (num <= 0) {
      return;
    }

    SonhaiModule.api.chooseAdventureBigPrizeId(
      {
        activityId: sonhai_activityId,
        bigPrizeId: this._bigPrizeId,
      },
      (data) => {
        log.log("选择大奖回调====", data);
        TipsMgr.showTip("选择成功");
        UIMgr.instance.back();
      }
    );
  }

  on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
