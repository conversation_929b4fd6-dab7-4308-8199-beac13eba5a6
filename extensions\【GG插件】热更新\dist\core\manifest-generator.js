"use strict";
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
Object.defineProperty(exports, "__esModule", { value: true });
exports.manifestGenerator = void 0;
const crypto_1 = __importDefault(require("crypto"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("../logger");
const gg_hot_update_type_1 = require("./gg-hot-update-type");
/**
 * 生成 Manifest
 */
class ManifestGenerator {
  constructor() {
    this._doNotDeleteFileName = ["main.js", "application.js", "project.manifest", "version.manifest", "gg.config.json"];
  }
  /**
   * 清空热更包输出目录
   *
   * @param outputDirPath 热更包输出目录路径 e.g. /Users/<USER>/game/build/android/hotupdate
   */
  clear(outputDirPath) {
    if (fs_extra_1.default.existsSync(outputDirPath)) {
      fs_extra_1.default.removeSync(outputDirPath);
    }
  }
  /**
   * 生成需要热更新的 bundle 的 manifest 资源
   *
   * @param assetsRootDir 构建目录绝对路径 e.g. /Users/<USER>/game/build/android/data
   * @param configFilePath 热更包配置文件路径 e.g. /Users/<USER>/game/gg-hot-update-config.json
   * @param outputDirPath 热更包输出目录路径 e.g. /Users/<USER>/game/build/android/data-hotupdate
   */
  generate(assetsRootDir, configFilePath, outputDirPath) {
    this.clear(outputDirPath);
    // 复制原始构建内容到热更包输出目录（部分文件不用参与生成热更新条目）
    const excludeFileNames = ["application.js", "main.js"];
    fs_extra_1.default.copySync(assetsRootDir, outputDirPath, {
      overwrite: true,
      preserveTimestamps: true,
      filter: (src, dst) => {
        return !excludeFileNames.includes(path_1.default.basename(src));
      },
    });
    // 遍历各个需要热更新bundle，生成其 manifests 文件
    const hotUpdateFileConfig = JSON.parse(fs_extra_1.default.readFileSync(configFilePath, "utf-8"));
    // 1. 生成远程包的热更新配置
    this._generateManifest(outputDirPath, hotUpdateFileConfig.remote_bundles);
    // 2. 生成构建包的热更新配置
    this._generateManifest(assetsRootDir, hotUpdateFileConfig.local_bundles);
    // 3. 剔除构建包的非内置文件，以减少构建包的包体体积
    const fileRegs = [];
    Object.values(hotUpdateFileConfig.local_bundles).forEach((bundleConfig) => {
      bundleConfig.files.forEach((fileRegText) => {
        fileRegs.push(new RegExp(fileRegText));
      });
    });
    if (fileRegs.length > 0) {
      this._deleteUnexpectedFiles(assetsRootDir, assetsRootDir, fileRegs);
    }
  }
  /**
   * 根据配置，对指定目录生成热更新配置
   *
   * @param assetsRootDir 热更新检索根目录绝对路径 e.g. /Users/<USER>/game/build/android/data
   * @param bundles 热更包配置
   */
  _generateManifest(assetsRootDir, bundles) {
    Object.keys(bundles).forEach((bundleName) => {
      const bundleConfig = bundles[bundleName];
      // 收集文件匹配规则
      const fileRegs = [];
      bundleConfig.files.forEach((fileRegText) => {
        fileRegs.push(new RegExp(fileRegText));
      });
      // 如果没有文件匹配规则，则不用生成
      if (fileRegs.length == 0) {
        return;
      }
      // 定义 project.manifest 的文件 json 结构
      const projectManifest = {
        version: bundleConfig.version,
        assets: {},
      };
      // 根据 bundle 类型，初始化 project.manifest 和 version.manifest 的输出路径
      let projectManifestPath = "";
      let versionManifestPath = "";
      if (bundleName == gg_hot_update_type_1.GGHotUpdateInstanceEnum.BuildIn) {
        // 初始化主包的 project.manifest 和 version.manifest 的输出路径
        // e.g. /Users/<USER>/game/build/andorid/data/project.manifest
        // e.g. /Users/<USER>/game/build/andorid/data/version.manifest
        projectManifestPath = path_1.default.join(assetsRootDir, "project.manifest");
        versionManifestPath = path_1.default.join(assetsRootDir, "version.manifest");
      } else {
        // 初始化子包的 project.manifest 和 version.manifest 的输出路径
        // e.g. /Users/<USER>/game/build/andorid/data/assets/${bundleName}/project.manifest
        // e.g. /Users/<USER>/game/build/andorid/data/assets/${bundleName}/version.manifest
        projectManifestPath = path_1.default.join(assetsRootDir, "assets", bundleName, "project.manifest");
        versionManifestPath = path_1.default.join(assetsRootDir, "assets", bundleName, "version.manifest");
      }
      // 根据文件匹配规则，遍历输出目录，，找到匹配的文件并生成热更条目
      this._generateDirAssetEntry(assetsRootDir, assetsRootDir, fileRegs, projectManifest.assets);
      // 如果是主包，还需要生成热更包信息文件confis.json 以及将其纳入到热更新条目
      if (bundleName == gg_hot_update_type_1.GGHotUpdateInstanceEnum.BuildIn) {
        const hotUpdateConfigFilePath = path_1.default.join(assetsRootDir, "gg.config.json");
        const hotUpdateConfig = { bundles: {} };
        Object.keys(bundles).forEach((key) => {
          hotUpdateConfig.bundles[key] = {
            version: bundles[key].version,
          };
        });
        fs_extra_1.default.writeFileSync(hotUpdateConfigFilePath, JSON.stringify(hotUpdateConfig));
        this._generateFileAssetEntry(assetsRootDir, hotUpdateConfigFilePath, projectManifest.assets);
        logger_1.logger.log(`${hotUpdateConfigFilePath} successfully generated.`);
      }
      // 写入 json 内容到 project.manifest
      fs_extra_1.default.writeFileSync(projectManifestPath, JSON.stringify(projectManifest));
      logger_1.logger.log(`${projectManifestPath} successfully generated.`);
      // 写入 json 内容到 version.manifest
      delete projectManifest.assets;
      fs_extra_1.default.writeFileSync(versionManifestPath, JSON.stringify(projectManifest));
      logger_1.logger.log(`${versionManifestPath} successfully generated.`);
    });
  }
  /**
   * 深度遍历指定目录，为目录下所有文件生成热更条目，并写入到对象中
   *
   * @param rootDir 扫描的根目录 e.g. /Users/<USER>/game/build/android/data
   * @param scanDir 当前扫描的目录 e.g. /Users/<USER>/game/build/android/data/assets/main
   * @param expectRegs 文件匹配规则（正则表达式）数组
   * @param assetsObj 热更包的热更新条目
   */
  _generateDirAssetEntry(rootDir, scanDir, expectRegs, assetsObj) {
    let stat = fs_extra_1.default.statSync(scanDir);
    let absolutePath = "";
    let relativePath = "";
    if (!stat.isDirectory()) {
      return;
    }
    let dirFileNames = fs_extra_1.default.readdirSync(scanDir);
    for (const fileName of dirFileNames) {
      if (fileName.startsWith(".")) {
        continue;
      }
      absolutePath = path_1.default.join(scanDir, fileName);
      stat = fs_extra_1.default.statSync(absolutePath);
      if (stat.isDirectory()) {
        this._generateDirAssetEntry(rootDir, absolutePath, expectRegs, assetsObj);
        continue;
      }
      if (stat.isFile()) {
        // 计算相对路径
        relativePath = path_1.default.relative(rootDir, absolutePath);
        relativePath = relativePath.replace(/\\/g, "/");
        relativePath = encodeURI(relativePath);
        // 如果是热更新文件，那么就加入到热更新条目中
        if (
          expectRegs.findIndex((reg) => {
            return reg.test(relativePath);
          }) != -1
        ) {
          this._addFileToAssetEntry(relativePath, absolutePath, stat.size, assetsObj);
        }
        continue;
      }
    }
  }
  /**
   * 将某个文件加入到热更新条目中
   *
   * @param rootDir 热更包根目录 e.g. /Users/<USER>/game/build/android/data
   * @param filePath 目标文件路径 e.g. /Users/<USER>/game/build/android/data/jsb-adapter/engine-adapter.js
   * @param assetsObj 热更包的热更新条目
   */
  _generateFileAssetEntry(rootDir, filePath, assetsObj) {
    const stat = fs_extra_1.default.statSync(filePath);
    if (stat.isFile()) {
      // 计算相对路径
      let relativePath = path_1.default.relative(rootDir, filePath);
      relativePath = relativePath.replace(/\\/g, "/");
      relativePath = encodeURI(relativePath);
      // 加入到热更新条目中
      this._addFileToAssetEntry(relativePath, filePath, stat.size, assetsObj);
    }
  }
  _addFileToAssetEntry(relativePath, filePath, fileSize, assetsObj) {
    assetsObj[relativePath] = {
      size: fileSize,
      md5: crypto_1.default.createHash("md5").update(fs_extra_1.default.readFileSync(filePath)).digest("hex"),
    };
  }
  /**
   * 深度遍历指定目录，删除非期望文件
   *
   * @param rootDir 扫描的根目录 e.g. /Users/<USER>/game/build/android/data
   * @param scanDir 当前扫描的目录 e.g. /Users/<USER>/game/build/android/data/assets/main
   * @param expectRegs 文件匹配规则（正则表达式）数组
   */
  _deleteUnexpectedFiles(rootDir, scanDir, expectRegs) {
    let stat = fs_extra_1.default.statSync(scanDir);
    let absolutePath = "";
    let relativePath = "";
    if (!stat.isDirectory()) {
      return;
    }
    let dirFileNames = fs_extra_1.default.readdirSync(scanDir);
    for (const fileName of dirFileNames) {
      if (fileName.startsWith(".")) {
        continue;
      }
      absolutePath = path_1.default.join(scanDir, fileName);
      stat = fs_extra_1.default.statSync(absolutePath);
      if (stat.isDirectory()) {
        this._deleteUnexpectedFiles(rootDir, absolutePath, expectRegs);
        continue;
      }
      if (stat.isFile()) {
        // 计算相对路径
        relativePath = path_1.default.relative(rootDir, absolutePath);
        relativePath = relativePath.replace(/\\/g, "/");
        relativePath = encodeURI(relativePath);
        // main.js 等不要删除
        if (this._doNotDeleteFileName.includes(fileName)) {
          continue;
        }
        // 如果不是热更新文件，那么删除
        if (
          expectRegs.findIndex((reg) => {
            return reg.test(relativePath);
          }) == -1
        ) {
          fs_extra_1.default.removeSync(absolutePath);
          continue;
        }
      }
    }
    // 判断当前目录是否为空目录，空目录则删除
    if (fs_extra_1.default.readdirSync(scanDir).length == 0) {
      fs_extra_1.default.removeSync(scanDir);
    }
  }
}
exports.manifestGenerator = new ManifestGenerator();
