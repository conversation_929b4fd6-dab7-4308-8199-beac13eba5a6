import { _decorator, Component, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { Scene } from "XrFrame/xrFrameSystem";
import { SceneLogin } from "../../script_game/scene_login/SceneLogin";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Wed Mar 05 2025 16:56:09 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_game_health/UIGameHealthAuthResult.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIGameHealthAuthResult")
export class UIGameHealthAuthResult extends BaseCtrl {
  protected start(): void {}
  private onClickOk() {
    SceneLogin.routeMgr.back();
  }
}
