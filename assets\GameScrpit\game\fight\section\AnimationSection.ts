import { _decorator, sp } from "cc";
import { Section } from "../../../lib/object/Section";
import GameObject from "../../../lib/object/GameObject";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

export class PlayAnimationDetail {
  public name: string;
  public isLoop: boolean;
  public anicall: any;
}

interface aniData {
  actId: number;
  isLoop: boolean;
}

export const actionDB = {
  1: { id: 1, actName: "boss_idle" },
  2: { id: 2, actName: "boss_attack1" },
  3: { id: 3, actName: "boss_attack2_1" },
  4: { id: 4, actName: "boss_attack2_2" },
  5: { id: 5, actName: "boss_attack3" },
  6: { id: 6, actName: "die" },
  7: { id: 7, actName: "hurt" },
  101: { id: 101, actName: "animation" },
  102: { id: 102, actName: "critical_hit" },
  103: { id: 103, actName: "hit" },
  201: { id: 201, actName: "show_time" },
};

@ccclass("AnimationSection")
export class AnimationSection extends Section {
  private curretAni: aniData;
  protected allMotionTime: Map<string, number> = new Map<string, number>(); //spine所有动作的时间
  public static sectionName(): string {
    return "AnimationSection";
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.ready();
    this.onMsg("OnRenderLoaded", this.onRenderLoaded.bind(this));
  }

  public onRemove(): void {
    super.onRemove();
    this.offMsg("OnRenderLoaded", this.onRenderLoaded.bind(this));
  }

  public onStart() {
    super.onStart();
  }

  private onRenderLoaded(spineNode: sp.Skeleton) {
    let spine = spineNode.getComponent(sp.Skeleton);
    if (spine == null) return; //不是spine动画
    let animations = spine.skeletonData.getRuntimeData(true);
    for (let i = 0; i < animations.animations.length; i++) {
      let animationData = animations.animations[i];
      this.allMotionTime.set(animationData["name"], animationData["duration"] < 0.1 ? 0.2 : animationData["duration"]);
    }
  }

  public playAction(actId, isLoop, call?) {
    let actDB = actionDB[actId];
    if (actDB == null) {
      log.error("没找到对应的action配置 actId:" + actId);
      return;
    }

    let actName = actDB["actName"];
    this.playAnimation(actName, isLoop, call);
    this.curretAni = {
      actId: actId,
      isLoop: isLoop,
    };
    return this.allMotionTime.get(actName);
  }

  private playAnimation(actName, isLoop, call) {
    let detail = new PlayAnimationDetail();
    detail.name = actName;
    detail.isLoop = isLoop;
    detail.anicall = call;
    this.emitMsg("OnPlayAnimation", detail);
  }

  public getActionTime(actId) {
    let actDB = actionDB[actId];
    if (actDB == null) {
      log.error("没找到对应的action配置 actId:" + actId);
      return 0;
    }
    let actName = actDB["name"];
    return this.getAnimationTime(actName);
  }

  public getAnimationTime(name: string): number {
    return this.allMotionTime.get(name) || 0;
  }
}
