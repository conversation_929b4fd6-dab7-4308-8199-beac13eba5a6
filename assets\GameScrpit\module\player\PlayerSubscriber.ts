import { GameDirector } from "../../game/GameDirector";
import { <PERSON>pi<PERSON>andler } from "../../game/mgr/ApiHandler";
import { CommandSubCmd, PlayerSubCmd } from "../../game/net/cmd/CmdData";
import { CommIntegerMapMessage, ErrorMessage, SystemOpenMessage } from "../../game/net/protocol/Comm";
import {
  BubbleMessage,
  HeadFrameMessage,
  HeadShowMessage,
  PlayerBattleAttrResponse,
  PlayVipExpResponse,
  SkinMessage,
  TitleMessage,
} from "../../game/net/protocol/Player";
import MsgMgr from "../../lib/event/MsgMgr";
import { AzstModule } from "../azst/AzstModule";
import { TravelModule } from "../travel/TravelModule";
import { PlayerMsgEnum } from "./PlayerConfig";
import { PlayerModule } from "./PlayerModule";
import { LangMgr } from "../../game/mgr/LangMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class PlayerSubscriber {
  public register() {
    // 订阅服务器消息
    // 当模块解锁时，推送解锁的模块标识
    ApiHandler.instance.subscribe(SystemOpenMessage, CommandSubCmd.intList, (data: SystemOpenMessage) => {
      GameDirector.instance.checkAndLoadModule(true, false);
    });

    /**权益变化推送 */
    ApiHandler.instance.subscribe(CommIntegerMapMessage, CommandSubCmd.intList2, (data: CommIntegerMapMessage) => {
      log.log("权益变化推送", data);
      PlayerModule.data.rightMap = data.intMap;
      if (data.intMap[106] > 0) {
        TravelModule.api.getTravelInfo((data) => {});
      }

      if (data.intMap[107] > 0) {
        AzstModule.api.getAzstMessage((data) => {});
      }
    });

    ApiHandler.instance.subscribe(ErrorMessage, CommandSubCmd.errorNotice, (data: ErrorMessage) => {
      log.error(LangMgr.txErrorCode(data.code, data.errorMgr.split("#"), ""));
    });

    ApiHandler.instance.subscribe(SkinMessage, PlayerSubCmd.SkinAddResponse, (data: SkinMessage) => {
      log.log("更新皮肤", data);
      PlayerModule.data.skinMap[data.skinId] = data;
      MsgMgr.emit(PlayerMsgEnum.SKIN_UPDATA, data);
    });

    ApiHandler.instance.subscribe(HeadShowMessage, PlayerSubCmd.headAddResponse, (data: HeadShowMessage) => {
      log.log("更新头像", data);
      PlayerModule.data.headShowMap[data.headShowId] = data;
    });

    ApiHandler.instance.subscribe(HeadFrameMessage, PlayerSubCmd.headFarmeAddResponse, (data: HeadFrameMessage) => {
      log.log("更新头像框", data);
      PlayerModule.data.headFrameMap[data.headFrameId] = data;
    });

    ApiHandler.instance.subscribe(TitleMessage, PlayerSubCmd.TitleAddResponse, (data: TitleMessage) => {
      log.log("更新头衔", data);
      PlayerModule.data.titleMap[data.titleId] = data;
    });

    ApiHandler.instance.subscribe(BubbleMessage, PlayerSubCmd.BubbleAddResponse, (data: BubbleMessage) => {
      log.log("更新气泡", data);
      PlayerModule.data.bubbleMap[data.bubbleId] = data;
    });

    ApiHandler.instance.subscribe(PlayVipExpResponse, PlayerSubCmd.vipExpNotice, (data: PlayVipExpResponse) => {
      PlayerModule.data.getPlayerInfo().vipExp = data.vipExp;
      PlayerModule.data.getPlayerInfo().vipLevel = data.vipLevel;
    });

    ApiHandler.instance.subscribe(
      PlayerBattleAttrResponse,
      PlayerSubCmd.notifyPlayerBattleAttr,
      (data: PlayerBattleAttrResponse) => {
        PlayerModule.data.playerBattleAttrResponse = data;
      }
    );

    ApiHandler.instance.subscribe(LongValue, PlayerSubCmd.upDecorateMessage, (data: LongValue) => {
      log.log("当装饰过期时，前端收到消息后，要重新拉取所有的装饰", data);
      PlayerModule.api.getAllSkin();
      PlayerModule.api.getAllTitle();
      PlayerModule.api.getAllHeadFrame();
      PlayerModule.api.getGetAllHeadShow();
      PlayerModule.api.getAllBubble();
    });
  }
  public unRegister() {
    //取消订阅服务器消息
  }
}

// 路由: 0 - 1  --- 广播推送: com.feamon.proto.comm.CommIntegerListMessage (当模块解锁时，推送解锁的模块标识)
