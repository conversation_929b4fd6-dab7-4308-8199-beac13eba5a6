import { _decorator, asset<PERSON><PERSON>ger, <PERSON>set<PERSON>anager, is<PERSON><PERSON><PERSON>, <PERSON><PERSON>, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ToolExt from "../../common/ToolExt";
import { ItemType1Enum, ItemType2Enum } from "../../../module/player/PlayerData";
import { UIMgr } from "../../../lib/ui/UIMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { dtTime } from "../../BoutStartUp";
import { Sleep } from "../../GameDefine";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { KnappsackAudioName } from "../../../module/player/PlayerConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIKnappsack_itemMain_son")
export class UIKnappsack_itemMain_son extends UINode {
  protected _isAddToBottom: boolean = true;
  protected _isSetParent: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_KNAPSACK}?prefab/ui/UIKnappsack_itemMain_son`;
  }

  private _itemMainIndex = 0;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upList, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upList, this);
  }

  protected onEvtShow(): void {
    this.initItemMain();
  }
  private async initItemMain() {
    const comItem = PlayerModule.data.comItem;
    const itemIds = Object.keys(comItem);
    this["itemScrollView"].active = itemIds.length > 0;
    this["itemWithout"].active = itemIds.length === 0;

    if (itemIds.length === 0) return;

    for (let i = 0; i < itemIds.length; i++) {
      const itemId = itemIds[i];
      const myItemNum = comItem[itemId];
      if (myItemNum <= 0) {
        continue;
      }

      if (i > 2 && i % 4 == 0) {
        await Sleep(0.01);
        if (isValid(this.node) == false) {
          return;
        }
      }

      const node = ToolExt.clone(this["btn_item"], this);
      this.getNode("ItemContent").addChild(node);
      node.active = true;
      node.setScale(0, 0, 1);
      tween(node)
        .to(0.2, { scale: v3(1, 1, 1) })
        .start();

      const c_info = JsonMgr.instance.getConfigItem(Number(itemId));

      this.setItem(c_info, node, c_info.id, myItemNum);
      node["itemType"] = c_info.type1;
      node["itemId"] = c_info.id;
    }
  }

  private upList(id: number) {
    const info = JsonMgr.instance.getConfigItem(id);
    if (info?.type2List.includes(ItemType2Enum.Com_Item)) {
      this.upItemMain(id);
    }
  }

  private upItemMain(id: number) {
    let node = this.getItemNodeById(id);
    if (!node) {
      node = ToolExt.clone(this["btn_item"], this);
      this.getNode("ItemContent").addChild(node);
      node.active = true;
      const c_info = JsonMgr.instance.getConfigItem(Number(id));
      node["itemType"] = c_info.type1;
      node["itemId"] = c_info.id;
    }

    const num = PlayerModule.data.getItemNum(id);
    if (num <= 0) {
      node.removeFromParent();
      node.destroy();
      return;
    }

    const c_item_info = JsonMgr.instance.getConfigItem(id);
    this.setItem(c_item_info, node, id, num);
  }

  private getItemNodeById(id: number): Node | null {
    return this["ItemContent"].children.find((child) => child["itemId"] === id) || null;
  }

  public addNewItem(itemId: number, parent: Node): Node {
    const node = ToolExt.clone(this["btn_item"], this);
    parent.addChild(node);
    node.active = true;

    const c_item_info = JsonMgr.instance.getConfigItem(itemId);
    node["itemType"] = c_item_info.type1;
    node["itemId"] = c_item_info.id;

    const myItemNum = PlayerModule.data.getItemNum(c_item_info.id);
    this.setItem(c_item_info, node, itemId, myItemNum);

    return node;
  }

  private setItem(c_item_info: any, node: Node, id: number, num: number) {
    FmUtils.setItemNode(node, id, num, ![ItemType1Enum.Look_Item, ItemType1Enum.Join_Item].includes(c_item_info.type1));
  }

  private on_click_btn_item(event) {
    AudioMgr.instance.playEffect(KnappsackAudioName.Effect.点击道具图标);
    let ItemType = event.node["itemType"];
    let itemId = event.node["itemId"];
    this.btnItemSwitch(ItemType, itemId);
  }

  private btnItemSwitch(ItemType: ItemType1Enum, itemId: number) {
    switch (ItemType) {
      case ItemType1Enum.Look_Item:
      case ItemType1Enum.Join_Item:
        log.log("点击了查看", itemId);
        break;
      case ItemType1Enum.Add_Item:
      case ItemType1Enum.Get_GD_Item:
      case ItemType1Enum.Get_SJ_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId });
        break;
      case ItemType1Enum.Get_ZX_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUseZx, { itemId });
        break;
    }
  }
}
