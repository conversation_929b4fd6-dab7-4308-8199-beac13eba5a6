import { _decorator, Label, sp, Sprite, v3 } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { IConfigBuildTrimReward } from "../../../JsonDefine";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("GameMapTopTrimPop")
export class GameMapTopTrimPop extends BaseCtrl {
  playShowAni: boolean = true;

  @property(Sprite)
  bg: Sprite;

  @property(Label)
  lblName: Label;

  @property(Label)
  lblBloomAdd: Label;

  @property(Label)
  lblUnlock: Label;

  // 配置信息
  cfgBuildTrimReward: IConfigBuildTrimReward;

  init(args: IConfigBuildTrimReward): void {
    this.cfgBuildTrimReward = args;
  }

  protected async start(): Promise<void> {
    super.start();

    if (!this.cfgBuildTrimReward) {
      log.error("配置未加载");
      this.closeBack();
      return;
    }

    this.lblName.string = this.cfgBuildTrimReward.name;
    this.lblBloomAdd.string = `繁荣度+${this.cfgBuildTrimReward.prosperityAdd}`;
    this.lblUnlock.string = `建筑总等级${this.cfgBuildTrimReward.buildLv}解锁`;

    if (this.cfgBuildTrimReward.scale.length > 1) {
      this.node.setScale(this.cfgBuildTrimReward.scale[0], this.cfgBuildTrimReward.scale[1], 1);
    }

    // 加载设置spine
    const nodeSpine = this.node.getChildByPath("node_trim/spine");
    const spine = nodeSpine.getComponent(sp.Skeleton);
    spine.premultipliedAlpha = false;

    spine.skeletonData = await this.assetMgr.loadSpineSync(
      BundleEnum.BUNDLE_G_GAME_MAP,
      this.cfgBuildTrimReward.spineName
    );

    if (this.cfgBuildTrimReward.windowBg != "") {
      this.bg.spriteFrame = await this.assetMgr.loadSpriteFrameSync(
        BundleEnum.BUNDLE_G_GAME_MAP,
        `images/${this.cfgBuildTrimReward.windowBg}`
      );
    }

    if (this.cfgBuildTrimReward.windowBgPlace.length >= 2) {
      this.bg.node.setPosition(this.cfgBuildTrimReward.windowBgPlace[0], this.cfgBuildTrimReward.windowBgPlace[1], 1);
    }

    // 设置动画位置
    if (this.cfgBuildTrimReward.windowPlace.length > 1) {
      nodeSpine.setPosition(v3(this.cfgBuildTrimReward.windowPlace[0], this.cfgBuildTrimReward.windowPlace[1], 0));
    }

    // 设置动画缩放
    if (this.cfgBuildTrimReward.windowBig.length > 1) {
      nodeSpine.setScale(v3(this.cfgBuildTrimReward.windowBig[0], this.cfgBuildTrimReward.windowBig[1], 1));
    }

    let animations = spine.skeletonData.getRuntimeData().animations;
    if (animations.find((ani) => ani.name == this.cfgBuildTrimReward.aniName)) {
      spine.setAnimation(0, this.cfgBuildTrimReward.aniName, true);
    } else {
      log.error("动画不存在-" + this.cfgBuildTrimReward.id, this.cfgBuildTrimReward.aniName);
    }
  }

  on_click_btn_close() {
    this.closeBack();
  }
}
