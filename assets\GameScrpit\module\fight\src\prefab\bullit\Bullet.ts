import { _decorator, Component, Node, Vec3 } from "cc";
import { JsonMgr } from "db://assets/GameScrpit/game/mgr/JsonMgr";
import { FaceToEnum } from "../../FightConstant";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

/**
 * 子弹类
 */
@ccclass("Bullet")
export class Bullet extends BaseCtrl {
  init(args: { spineId: number }) {}

  start() {}

  public setFaceTo(to: FaceToEnum) {
    if (to == FaceToEnum.left) {
      this.node.setScale(-1, 1, 1);
    } else {
      this.node.setScale(1, 1, 1);
    }
  }

  public getFaceTo(): FaceToEnum {
    return this.node.scale.x > 0 ? FaceToEnum.right : FaceToEnum.left;
  }

  public playStart() {}

  public playFlay() {}

  public playBoom() {}
}
