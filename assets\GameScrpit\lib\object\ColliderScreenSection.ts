import { _decorator } from "cc";
import ColliderSection from "./ColliderSection";
import GameObject from "./GameObject";
import { Section } from "./Section";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("ColliderScreenSection")
export default class ColliderScreenSection extends Section {
  private _interval: number = 0.5;
  private _count: number = 0;
  public static sectionName(): string {
    return "ColliderScreenSection";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.ready();
  }

  public onStart(): void {
    super.onStart();
    let sec = this.getSection(ColliderSection);
    if (sec == null) {
      log.warn("需要对应的ColliderSection支持");
    }
  }

  public updateSelf(dt: any): void {
    super.updateSelf(dt);
    this._count += dt;
    if (this._count >= this._interval) {
      this._count = 0;
      this.doCollider(this.getSection(ColliderSection).dict);
    }
  }

  public onRemove(): void {}

  public setInterval(val) {
    this._interval = val;
  }

  protected doCollider(objs) {}
}
