import { _decorator, Label, Node } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { FarmLogMessage } from "../../../net/protocol/Farm";
import FmUtils from "../../../../lib/utils/FmUtils";
import ToolExt from "../../../common/ToolExt";
import { PlayerModule } from "../../../../module/player/PlayerModule";
import { FmColor } from "../../../common/FmConstant";
import { LangMgr } from "../../../mgr/LangMgr";

const { ccclass, property } = _decorator;
@ccclass("FarmLogItemViewHolder")
export class FarmLogItemViewHolder extends ViewHolder {
  @property(Label)
  private lblName: Label;

  @property(Label)
  private lblDetail: Label;

  @property(Label)
  private lblTime: Label;

  @property(Node)
  private btnHeader: Node;

  private farmLog: FarmLogMessage;

  public init() {}

  public updateData(position: number, args: FarmLogMessage) {
    this.farmLog = args;

    // 头像
    FmUtils.setHeaderNode(this.btnHeader, args.collectorMessage);

    // 采集时间
    this.lblTime.string = `${ToolExt.formatTimeAgo(args.endTime)}`;

    // 福地
    if (args.farmUserMessage.userId == PlayerModule.data.playerId) {
      this.lblName.string = LangMgr.txMsgCode(462, [""]); // "我";
      this.lblName.color = FmColor.COLOR_RED_LIGHT;
    } else {
      this.lblName.string = `${args.farmUserMessage.nickname}`;
      this.lblName.color = FmColor.COLOR_BLUE_LIGHT;
    }

    // 采集结果
    this.lblDetail.string = LangMgr.txMsgCode(458, [args.gourdId + 1]); //`成功采集了${args.gourdId + 1}级葫芦`;
    if (args.collectorMessage.userId == PlayerModule.data.playerId) {
      this.lblDetail.color = FmColor.COLOR_GREEN_LIGHT;
    } else {
      this.lblDetail.color = FmColor.COLOR_RED_LIGHT;
    }
  }
}
