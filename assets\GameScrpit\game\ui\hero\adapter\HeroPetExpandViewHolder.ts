import { _decorator, EventTouch, Label, Node, sp, Sprite } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { HeroModule } from "../../../../module/hero/HeroModule";
import { PetModule } from "../../../../module/pet/PetModule";
import ResMgr from "../../../../lib/common/ResMgr";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { ItemEnum } from "../../../../lib/common/ItemEnum";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../../../module/player/PlayerConstant";
import { times } from "../../../../lib/utils/NumbersUtils";
import { ItemCost } from "../../../common/ItemCost";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PetSkillMessage } from "../../../net/protocol/Pet";
import { PetRouteItem } from "db://assets/GameScrpit/module/pet/PetRoute";
import { PetAudioName } from "db://assets/GameScrpit/module/pet/PetConfig";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;
@ccclass("HeroPetExpandViewHolder")
export class HeroPetExpandViewHolder extends ViewHolder {
  @property(Node)
  private itemCost1: Node;
  @property(Node)
  private itemCost2: Node;

  @property(Label)
  private lblTxt1: Label;
  @property(Label)
  private lblTxt2: Label;

  @property(Node)
  private bgWeiManji: Node;
  @property(Node)
  private bgManji: Node;

  @property(Node)
  private upEffect: Node;

  private _oldValue = 0;
  private click_confirm(e: EventTouch, index: any) {
    // let position = this._position;
    console.log(index);
    let petInfo = PetModule.data.getPet(HeroModule.viewModel.currentHero.petId);
    let usedItem = Number(index) === 0;
    if (usedItem) {
      //道具消耗
      if (!this.itemCost1.getComponent(ItemCost).isEnough()) {
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: 1072 });
        return;
      }
    } else {
      //气运消耗
      if (!this.itemCost2.getComponent(ItemCost).isEnough()) {
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: 1 });
        return;
      }
    }
    this.upEffect.active = true;
    this.upEffect.getComponent(sp.Skeleton).setAnimation(0, "animation", false);
    AudioMgr.instance.playEffect(AudioName.Effect.通用升级);
    PetModule.api.reFreshSkill(petInfo.petId, this.position, usedItem, (data: PetSkillMessage) => {
      AudioMgr.instance.playEffect(PetAudioName.Effect.洗练成功);
      if (
        PetModule.viewModel.isShowXilianTipsSettingForever() &&
        PetModule.viewModel.isShowXilianTipsSetting() &&
        data.backUpSkillAdd > this._oldValue
      ) {
        TipsMgr.setEnableTouch(false, 1);
        UIMgr.instance.showDialog(PetRouteItem.UIPetXilianTips, {
          old: times(this._oldValue, 100),
          new: times(data.backUpSkillAdd, 100),
        });
      }
      this.updateData(this.position);
    });
  }

  /**
   * 更新数据
   * @param position
   * @param isopen
   */
  updateData(position, isopen: boolean = false) {
    this.position = position;
    let petSkillList = PetModule.data.getHeroPetSkillList();
    let petSkill = petSkillList[position % petSkillList.length];
    let petInfo = PetModule.data.getPet(HeroModule.viewModel.currentHero.petId);
    let pet = petInfo.petSkillList[position];
    this._oldValue = pet.skillAdd;

    let energySost = PetModule.data.getHeroPetSkillCost(pet.energyWashCount + 1);
    this.itemCost1.getComponent(ItemCost).setItemId(1072, 1);
    this.itemCost2.getComponent(ItemCost).setItemId(ItemEnum.气运_1, energySost);

    this.itemCost1.getComponent(ItemCost).setCheck(HeroModule.viewModel.setting_hero_pet_xilianshi);
    this.itemCost1.getComponent(ItemCost).setOnCheckChange((check: boolean) => {
      HeroModule.viewModel.setting_hero_pet_xilianshi = check;
    });

    if (times(pet.skillAdd, 100) >= 25) {
      this.bgWeiManji.active = false;
      this.bgManji.active = true;
      pet.backUpSkillAdd = 0;
    } else {
      this.bgWeiManji.active = true;
      this.bgManji.active = false;
    }
    // this.lblTxt1.string = ``
    //技能描述
    this.lblTxt1.getComponent(Label).string = `${petSkill.des.replace("+%s", "")}`;
    if (pet.backUpSkillAdd != 0) {
      let formattedString = `+${times(pet.backUpSkillAdd, 100)}%`;
      if (isopen) {
        formattedString = `+${times(pet.skillAdd, 100)}%`;
      }
      this.lblTxt2.getComponent(Label).string = formattedString;
    } else {
      this.lblTxt2.getComponent(Label).string = `+${times(pet.skillAdd, 100)}%`;
    }
  }
}
