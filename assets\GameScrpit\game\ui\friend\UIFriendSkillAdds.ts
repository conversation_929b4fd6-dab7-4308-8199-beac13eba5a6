import { _decorator, Component, Label, Node } from "cc";
import { FriendModule } from "../../../module/friend/FriendModule";
const { ccclass, property } = _decorator;

@ccclass("UIFriendSkillAdds")
export class UIFriendSkillAdds extends Component {
  @property(Node)
  private skillAddsNums: Node;
  @property(Label)
  private type1Add: Label;
  @property(Label)
  private type2Add: Label;
  @property(Label)
  private type3Add: Label;
  @property(Label)
  private type4Add: Label;
  @property(Label)
  private type5Add: Label;
  start() {}
  protected onEnable(): void {
    this.skillAddsNums.getComponent(Label).string = `${FriendModule.data.getAllFriendCitySkillNums()}`;
    let attrs = FriendModule.data.getAllFriendCityAttrAdds();
    this.type1Add.string = `+${attrs[1]}%`;
    this.type2Add.string = `+${attrs[2]}%`;
    this.type3Add.string = `+${attrs[3]}%`;
    this.type4Add.string = `+${attrs[4]}%`;
    this.type5Add.string = `+${attrs[5]}%`;
  }

  update(deltaTime: number) {}
}
