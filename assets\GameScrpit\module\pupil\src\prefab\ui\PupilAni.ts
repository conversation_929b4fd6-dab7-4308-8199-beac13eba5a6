import { _decorator, Component, isValid, Node } from "cc";
import { instantiate } from "cc";
import { Prefab } from "cc";
import { sp, tween } from "cc";
import { AssetMgr } from "../../../../../../platform/src/ResHelper";
import { PupilModule } from "../../PupilModule";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { NodeStatus } from "../../../../../game/GameDefine";

const { ccclass, property } = _decorator;

// 可用动画列表
export enum PupilAniEnum {
  idle = "idle",
  train = "train",
}

@ccclass("PupilAni")
export class PupilAni extends Component {
  _assetMgr: AssetMgr;

  // 当前播放动画名称
  _currentAniName: string = PupilAniEnum.idle;

  // 是否循环播放
  _isLoop: boolean = true;

  // 当前名字ID
  _nameId: number;

  // 当前加载状态
  _status = NodeStatus.INIT;

  private _aniNode: Node;

  get assetMgr() {
    if (this._assetMgr == null) {
      this._assetMgr = AssetMgr.create();
    }
    return this._assetMgr;
  }

  onLoad() {}

  start() {
    this._status = NodeStatus.START;
    this.playAni(this._currentAniName, this._isLoop);
  }

  onDestroy() {
    this.assetMgr && this.assetMgr.release();
  }

  setAniByNameId(nameId: number, aduitIs: number = 0) {
    if (this._nameId == nameId) {
      return;
    }

    this._nameId = nameId;

    this.node.removeAllChildren();

    let configPupilName = PupilModule.data.getConfigPupilName(nameId);
    this.assetMgr.loadPrefab(
      BundleEnum.BUNDLE_G_PUPIL,
      `prefab/pupil_prefab/Pupil_${configPupilName.skinId}`,
      (pb: Prefab) => {
        if (!isValid(this.node)) {
          return;
        }
        let node = instantiate(pb);
        node.setPosition(0, 0);
        node.children.forEach((val) => {
          val.active = false;
        });

        if (aduitIs > 0) {
          node.getChildByName("adult").active = true;
          this._aniNode = node.getChildByName("adult");
        } else {
          node.getChildByName("ani").active = true;
          this._aniNode = node.getChildByName("ani");
        }
        this.node.addChild(node);
        // 播放当前动画
        this.playAni(PupilAniEnum.idle, true);
      }
    );
  }

  playAni(aniName: string, loop: boolean = false, force: boolean = false) {
    if (this._currentAniName == aniName && !force) {
      return;
    }

    this._currentAniName = aniName;
    this._isLoop = loop;

    if (this._status != NodeStatus.START) {
      return;
    }

    let nodeAni = this.node.children[0];
    if (nodeAni) {
      let aniSpine = this._aniNode.getComponent(sp.Skeleton); //nodeAni.getChildByName("ani").getComponent(sp.Skeleton);
      aniSpine.setAnimation(0, aniName, loop);

      // 非循环状态的，默认回到idle状态
      if (!loop) {
        tween(nodeAni)
          .delay(1)
          .call(() => {
            this.playAni(PupilAniEnum.idle, true);
          })
          .start();
      }
    }
  }
}
