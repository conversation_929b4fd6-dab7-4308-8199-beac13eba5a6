import { GameData } from "../../../game/GameData";
import data from "../../../lib/data/data";
import { FriendGoalApi } from "./FriendGoalApi";
import { FriendGoalConfig } from "./FriendGoalConfig";
import { FriendGoalData } from "./FriendGoalData";
import { FriendGoalRoute } from "./FriendGoalRoute";
import { FriendGoalService } from "./FriendGoalService";
import { FriendGoalSubscriber } from "./FriendGoalSubscriber";
import { FriendGoalViewModel } from "./FriendGoalViewModel";

export class FriendGoalModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): FriendGoalModule {
    if (!GameData.instance.FriendGoalModule) {
      GameData.instance.FriendGoalModule = new FriendGoalModule();
    }
    return GameData.instance.FriendGoalModule;
  }
  private _data = new FriendGoalData();
  private _api = new FriendGoalApi();
  private _service = new FriendGoalService();
  private _subscriber = new FriendGoalSubscriber();
  private _route = new FriendGoalRoute();
  private _viewModel = new FriendGoalViewModel();
  private _config = new FriendGoalConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new FriendGoalData();
    this._api = new FriendGoalApi();
    this._service = new FriendGoalService();
    this._subscriber = new FriendGoalSubscriber();
    this._route = new FriendGoalRoute();
    this._viewModel = new FriendGoalViewModel();
    this._config = new FriendGoalConfig();

    // 模块初始化
    this._subscriber.register();
    this._route.init();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
