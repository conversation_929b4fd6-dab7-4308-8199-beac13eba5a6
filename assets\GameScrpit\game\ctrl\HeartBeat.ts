import { _decorator, Component } from "cc";
import { CityModule } from "../../module/city/CityModule";
import { ConfigBuildCrystalRecord } from "../../module/city/CityConstant";
import MsgMgr from "../../lib/event/MsgMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
import MsgEnum from "../event/MsgEnum";
import { PlayerUpdateEnergyRequestMessage } from "../net/protocol/Player";
import { HeartBeatSec } from "../GameDefine";
import { MainTaskModule } from "../../module/mainTask/MainTaskModule";
import { JsonMgr } from "../mgr/JsonMgr";
import { TaskType } from "../../module/mainTask/MainTaskData";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

@ccclass("HeartBeat")
export class HeartBeat extends Component {
  private heartBeatCd = 0;

  start() {
    MsgMgr.on(MsgEnum.ON_ENERGYFACTORY_FLY, this.checkBeat, this);
  }

  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_ENERGYFACTORY_FLY, this.checkBeat, this);
  }

  private checkBeat() {
    const cfgTaskMain = JsonMgr.instance.jsonList.c_taskMain[MainTaskModule.data.mainTaskMsg.taskTimelineId];

    if (cfgTaskMain.taskId == TaskType.点击女娲_64 || cfgTaskMain.taskId == TaskType.点击女娲_1) {
      this.heartBeatCd = 0;
      this.heartBeat(false);
    }
  }

  private heartBeat(heartBool: boolean = true) {
    try {
      let energyLevel = CityModule.data.energyFactoryMsg.level;
      let config_buildCrystal: ConfigBuildCrystalRecord = CityModule.data.getConfigBuildCrystal(energyLevel);
      let data: PlayerUpdateEnergyRequestMessage = {
        autoClick: 3 * config_buildCrystal.time,
        manualClick: CityModule.data.clickHomeCount,
      };

      // 未解锁自动点击不加自动点击
      if (!CityModule.data.energyFactoryMsg.autoTake) {
        data.autoClick = 0;
      }

      // 同步后，重置点击次数
      CityModule.data.clickHomeCount = 0;

      PlayerModule.api.updateEnergy(data, () => {
        if (heartBool == true) {
          MsgMgr.emit(MsgEnum.ON_HEART_BEAT);
        } else {
          // 引导消息回调
          MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "CLICK_NVWA");
        }
      });
    } catch (e) {
      log.error("3秒同步报错", e);
    }
  }

  update(deltaTime: number) {
    this.heartBeatCd += deltaTime;
    if (this.heartBeatCd < HeartBeatSec) {
      return;
    }

    this.heartBeatCd = 0;

    this.heartBeat();
  }
}
