export class ActivityConfig {}

export const ActivityAudioName = {
  Effect: {
    点击充值豪礼图标: 1321,
    点击前往按钮: 1322,
    点击道具图标: 1324,
    点击每日礼包下方页签: 1325,
    点击一键领取: 1326,
  },
  Sound: {},
};
export enum ActivityUnlockEnum {
  /**等级解锁 */
  PLAYER_LEVEL = 1,
}

/**活动类 */
export enum ActivityTypeEnum {
  /**首充 */
  FIRST_RECHARGE = 101,
  /**月卡 */
  MONTH_VIP_CARD = 102,
  /**终身卡 */
  LIFE_VIP_CARD = 103,
  /**基金 */
  ACTIVITY_FUND = 104,
  /**七天签到 */
  SEVEN_DAY = 105,

  /**弹窗礼包  */
  WINDOWS_PACK = 106,
  /**每日礼包 */
  TIME_PACK = 107,
  /**繁荣度冲榜 */
  Prosperity = 108,
  /**累天回馈 */
  TOP_UP_RECHARGE = 109,
  /**修行基金 */
  LEADER_FUND = 110,
  /**山海探险 */
  ADVENTURE = 111,
}

/**每日活动 */

class RedeemBaseConfigVO {
  /**
   * 兑换的物料ID
   */
  id: number;

  /**
   * 名称
   */
  name: string;

  /**
   * 限购类型
   */
  maxtype: number;

  /**
   * 限购次数
   */
  max: number;
}

export class RedeemPackVO extends RedeemBaseConfigVO {
  /**
   * 领取一次奖励/看广告次数 为0就需要支付虚拟货币/真实货币
   */
  adNum: number;

  /**
   * 跳过单次看广告的消耗
   */
  skipAd: Array<number>;

  /**
   * 价格(真实货币)
   */
  price: number;

  /**
   * 用虚拟货币兑换当前道具的消耗
   */
  cost: Array<number>;

  /**
   * 每多次购买额外增加的道具消耗
   */
  costAdd: Array<number>;

  /**
   * 解锁条件
   */
  unlockList: Array<number>;

  /**
   * 对应奖励
   */
  rewardList: Array<number>;

  /**
   * 可选奖励
   */
  chosenList: Array<Array<number>>;

  /**
   * 排序
   */
  sort: Array<number>;

  /**
   * 是否关闭
   */
  close: boolean;
}

/**成就配置 */
export class AchieveVO {
  /**
   * 充值ID
   */
  id: number;

  /**
   * 名称
   */
  name: string;

  /**
   * 价格
   */
  price: number;

  otherParam: number[];

  /**
   * 追踪的指标
   */
  targetId: number;

  /**
   * 档位成就值
   */
  requireList: Array<number>;

  /**付费奖励 */
  paidRewardList: Array<number>;

  /**基础奖励 */
  basicRewardList: Array<Array<number>>;
}

/**基础活动 */
export class BaseActivityConfigVO {
  id: number;

  /**
   * 活动名称
   */
  name: string;

  /**
   * 活动类型 @See ActivityTypeEnum
   */
  activityType: ActivityTypeEnum;

  /**
   * 是否关闭活动
   */
  close: boolean;

  /**
   * 整个首充对外的解锁条件
   * ActivityUnlockEnum
   *
   * @see GlobalConstant#OR_CONDITION_SEPARATOR 条件或分隔符
   */
  unlockList: Array<number>;
  /**
   * 预热时间  warmUpTime <= startTime < publicityTime <= publicityAfterTime <= endTime
   */
  warmUpTime: number;

  /**
   * 开始时间
   */
  startTime: number;

  /**
   * 结算时间前
   */
  publicityTime: number;

  /**
   * 结算时间后-公示时间前
   */
  publicityAfterTime: number;

  /**
   * 结束时间
   */
  endTime: number;

  /**
   * 期号
   */
  issue: number;
  /**
   * 版本号
   */
  version: number;
}

export class FundVO extends BaseActivityConfigVO {
  /**
   * 购买类型 @See GoodsBuyTypeEnum
   */
  buyType: number;

  /**
   * 成就任务列表
   */
  achieveVOList: Array<AchieveVO>;
}
//、兑换类
export class RedeemVO extends BaseActivityConfigVO {
  /**
   * 购买类型 @See GoodsBuyTypeEnum
   */
  buyType: number;

  /**
   * 兑换道具列表 第一层List的长度代表有几个兑换活动页签
   */
  redeemList: Array<Array<RedeemPackVO>>;
}

/**签到配置信息 */
export class DaySignVO {
  /**
   * 充值ID
   */
  id: number;

  /**
   * 价格
   */
  price: number;

  name: string;

  maxDay: number;

  /**
   * 解锁后未充值可以领取的奖励
   */
  basicRewardList: Array<Array<number>>;

  /**
   * 解锁后充值后可以额外领取的奖励
   */
  paidRewardList: Array<Array<number>>;
}

/**2.每日活动 */
export class DayTaskVO {
  id: number;

  name: string;

  takeList: Array<Array<number>>;

  rewardList: Array<Array<number>>;
}

/**3.充值任务 */
export class LeaderRechargeVO {
  id: number;

  name: string;

  requireList: Array<number>;

  rewardList: Array<Array<number>>;
}

/**修行基金配置 */
export class LeaderFundVO extends RedeemVO {
  /**
   * 成就任务列表
   */
  achieveVOList: Array<AchieveVO>;

  /**
   * 签到列表
   */
  signVO: DaySignVO;

  /**
   * 任务及奖励
   */
  taskVO: DayTaskVO;

  /**
   * 累计付费次数奖励列表
   */
  rechargeVO: { id: number; rewardList: Array<number>; round: number };

  /**
   * 期号
   */
  issue: number;
}

/**山海探险成就任务 */
export class AdventureAchieveVO extends AchieveVO {
  showList: Array<number>;
}

/**山海探险成就任务 */
export class AdventureTaskVO {
  /**
   * 每日任务数量
   */
  taskNum: number;

  /**
   * 每日任务池子
   */
  taskList: Array<Array<number>>;

  /**
   * 道具奖励
   */
  rewardList: Array<Array<number>>;
}

export class AdventureBigPrizeSubVO {
  id: number;

  rewardList: Array<number>;

  /**
   * 解锁数值(中大奖几次解锁)
   */
  unlock: number;

  /**
   * 最多中几次
   */
  max: number;
}

/**山海探险大奖奖品配置 */
export class AdventureBigPrizeVO {
  /**
   * 中大奖的最大次数（也是一轮最大次数）
   */
  round: number;

  /**
   * 中奖概率（已经是小数形式）
   */
  rateList: Array<number>;

  /**
   * 大奖的概率和解锁条件
   */
  subList: Array<AdventureBigPrizeSubVO>;
}
/**山海探险小奖奖品配置 */
export class AdventureSmallPrizeSubVO {
  id: number;

  rewardList: Array<number>;

  /**
   * 自取最大次数
   */
  max: number;

  /**
   * 概率
   */
  rate: number;
}

/**6.小奖总配置 */
export class AdventureSmallPrizeVO {
  /**
   * 必中道具
   */
  mustRewardList: Array<number>;
  subList: Array<AdventureSmallPrizeSubVO>;
}

/**山海探险总配置 */
export class AdventureVO extends RedeemVO {
  /**
   * 成就任务列表
   */
  achieveVOList: Array<AdventureAchieveVO>;

  /**
   * 日常任务
   */
  taskVO: AdventureTaskVO;

  /**
   * 大奖配置
   */
  bigPrizeVO: AdventureBigPrizeVO;

  /**
   * 小奖配置
   */
  smallPrizeVO: AdventureSmallPrizeVO;

  /**
   * 每次抽奖消耗的道具
   */
  costList: Array<number>;

  /**
   * 转化参数
   */
  transformList: Array<number>;

  /**
   * 期号
   */
  issue: number;
}
