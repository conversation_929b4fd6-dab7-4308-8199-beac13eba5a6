import { _decorator, Label, RichText } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";

const { ccclass, property } = _decorator;

@ccclass("UILotteryProb")
export class UILotteryProb extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_LOTTERY}?prefab/ui/UILotteryProb`;
  }

  protected onEvtShow(): void {
    this.getNode("titleLab").getComponent(Label).string = JsonMgr.instance.jsonList.c_help[1].helpText;
    this.getNode("des_lab").getComponent(RichText).string = JsonMgr.instance.jsonList.c_help[2].helpText;
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);

    UIMgr.instance.back();
  }

  private on_click_btn_closeMain() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
