import { _decorator, instantiate, isValid, Label, Sprite } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import { TravelMsgEnum } from "../../../module/travel/TravelConfig";
import { DialogZero } from "../../GameDefine";
import ResMgr from "../../../lib/common/ResMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UITravelDialogue")
export class UITravelDialogue extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_TRAVEL}?prefab/ui/UITravelDialogue`;
  }
  protected _isAddToBottom: boolean = true;
  public zOrder(): number {
    return DialogZero.UITravelDialogue;
  }
  //=================================================

  private _tickIdList: number[] = [];

  private _resAddList: number = null;

  public init(args: any): void {
    super.init(args);
    log.log("对话参数====", args);
    this._resAddList = args.resAddList;
  }

  protected onEvtShow(): void {
    this.getNode("btn_next").active = false;
    this.getNode("btn_skip").active = false;

    let c_friend = JsonMgr.instance.jsonList.c_friend;
    let friendInfo = c_friend[this._resAddList[0] % 1000000];

    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_COMMON_FRIEND}?half/friend_` + friendInfo.id,
      this.getNode("friendImage").getComponent(Sprite),
      this
    );
    this.assetMgr.loadPrefab(BundleEnum.BUNDLE_COMMON_FRIEND, `prefab/friend_${friendInfo.id}`, (prefab) => {
      if (isValid(this.node) == false) {
        return;
      }

      let item = instantiate(prefab);
      this.getNode("friendImage").addChild(item);
      item.walk((child) => {
        child.layer = this.node.layer;
      });
    });

    this.getNode("lab_dial").getComponent(Label).string = "";
    this.getNode("friendName").getComponent(Label).string = friendInfo.name;

    let talkInfo = this.getRandomTalk();
    this.typewriterTalk(talkInfo);
  }

  private getRandomTalk() {
    let c_talk = JsonMgr.instance.jsonList.c_talk;
    let list = Object.keys(c_talk);
    let index = Math.floor(Math.random() * list.length);
    log.log("getRandomTalk====", index);
    return c_talk[list[index]];
  }

  private typewriterTalk(talkInfo) {
    for (let i = 0; i < talkInfo.talk.length; i++) {
      let id = TickerMgr.setTimeout(0.1 * i, () => {
        this.getNode("lab_dial").getComponent(Label).string += talkInfo.talk[i];
        if (i == talkInfo.talk.length - 1) {
          this.getNode("btn_next").active = true;
          this.getNode("btn_skip").active = false;
        }
      });
      this._tickIdList.push(id);
    }

    this.getNode("btn_skip").active = true;
  }

  private on_click_btn_next() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_skip() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    for (let i = 0; i < this._tickIdList.length; i++) {
      TickerMgr.clearTicker(this._tickIdList[i]);
    }
    let talkInfo = this.getRandomTalk();
    this.getNode("lab_dial").getComponent(Label).string = talkInfo.talk;
    this.getNode("btn_next").active = true;
    this.getNode("btn_skip").active = false;
  }

  protected onEvtClose(): void {
    MsgMgr.emit(TravelMsgEnum.TRAVEL_DIALOGUE_CLOSE);
    for (let i = 0; i < this._tickIdList.length; i++) {
      TickerMgr.clearTimeout(this._tickIdList[i]);
    }
  }
}
