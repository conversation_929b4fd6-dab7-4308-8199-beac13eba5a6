import { _decorator, instantiate, Label, Node, Sprite } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ClubModule } from "../../../../module/club/ClubModule";
import { ClubRewardMessage } from "../../../net/protocol/Club";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { ClubAudioName, UnionTaskConfig } from "../../../../module/club/ClubConfig";
import { divide } from "../../../../lib/utils/NumbersUtils";
import MsgMgr from "../../../../lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { ClubRouteItem } from "../../../../module/club/ClubRoute";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIClubDonate } from "../UIClubDonate";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("ClubTaskViewHolder")
export class ClubTaskViewHolder extends ViewHolder {
  @property(Label)
  private item_title: Label;
  @property(Label)
  private task_des: Label;
  @property(Sprite)
  private process: Sprite;
  @property(Node)
  private item_1_bkg: Node;
  @property(Node)
  private item_2_bkg: Node;
  @property(Node)
  private item_3_bkg: Node;
  @property(Node)
  private btn_get: Node;
  @property(Node)
  private btn_go: Node;
  @property(Node)
  private tag_taked: Node;
  _data: UnionTaskConfig;
  init() {
    this.btn_get.on(Node.EventType.TOUCH_END, () => {
      AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟任务点击领取奖励);
      if (ClubModule.service.isInterceptOperation()) {
        return;
      }
      ClubModule.api.takeTaskReward(this._data.id, (data: ClubRewardMessage) => {
        log.log(data);

        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
      });
    });
    this.btn_go.on(Node.EventType.TOUCH_END, (event) => {
      AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟任务点击前往按钮);
      log.log("btn go touch end trigger", event);
      if (ClubModule.service.isInterceptOperation()) {
        return;
      }
      if (this._data.taskId == 40) {
        UIMgr.instance.back();

        RouteManager.uiRouteCtrl.showRoute(UIClubDonate);
      } else if (this._data.taskId == 39) {
        //boss
        UIMgr.instance.back();
        UIMgr.instance.showDialog(ClubRouteItem.UIClubBossMain);
      }
    });
  }
  updateData(data: UnionTaskConfig) {
    this._data = data;
    this.item_title.string = `${ClubModule.config.getTaskConfig(data.taskId).title} ${data.finish} 次`;
    this.task_des.string = `${ClubModule.config.getTaskConfig(data.taskId).title} ${data.finish} 次`;
    let finishTimes = ClubModule.data.clubFormMessage.dailyTask.taskCntMap[this._data.taskId];
    let pro = divide(finishTimes, this._data.finish);
    this.process.fillRange = pro;
    if (finishTimes >= this._data.finish) {
      this.btn_get.active = true;
      this.btn_go.active = false;
    } else {
      this.btn_get.active = false;
      this.btn_go.active = true;
    }
    let isTaked = false;
    for (let i = 0; i < ClubModule.data.clubFormMessage.dailyTask.completeTaskList.length; i++) {
      if (ClubModule.data.clubFormMessage.dailyTask.completeTaskList[i] == this._data.id) {
        isTaked = true;
        break;
      }
    }
    let taskReward = ClubModule.config.getTaskRewardListByLevel(data.id);
    this.item_1_bkg.active = false;
    this.item_2_bkg.active = false;
    this.item_3_bkg.active = false;
    // log.log(taskReward);
    for (let i = 0; i < taskReward.length; i++) {
      // log.log(i);
      if (i == 0) {
        this.item_1_bkg.active = true;
        FmUtils.setItemNode(this.item_1_bkg, taskReward[i][0], taskReward[i][1]);
        continue;
      } else if (i == 1) {
        this.item_2_bkg.active = true;
        FmUtils.setItemNode(this.item_2_bkg, taskReward[i][0], taskReward[i][1]);
        continue;
      } else if (i == 2) {
        this.item_3_bkg.active = true;
        FmUtils.setItemNode(this.item_3_bkg, taskReward[i][0], taskReward[i][1]);
        continue;
      }
    }

    if (isTaked) {
      this.btn_get.active = false;
      this.btn_go.active = false;
      this.tag_taked.active = true;
    } else {
      this.tag_taked.active = false;
    }
  }
}
export class ClubTaskAdapter extends ListAdapter {
  item: Node;
  constructor(item: Node) {
    super();
    this.item = item;
  }
  datas: UnionTaskConfig[] = [];
  setData(data: UnionTaskConfig[]) {
    if (!data) return;
    // this.datas = data;
    this.datas = data.sort(this.sortData);
    // log.log(this.datas);
    this.notifyDataSetChanged();
  }
  private sortData(a: UnionTaskConfig, b: UnionTaskConfig) {
    let isATaked = false;
    let aFinishTimes = ClubModule.data.clubFormMessage.dailyTask.taskCntMap[a.taskId];
    let isAfinish = aFinishTimes >= a.finish;
    for (let i = 0; i < ClubModule.data.clubFormMessage.dailyTask.completeTaskList.length; i++) {
      if (ClubModule.data.clubFormMessage.dailyTask.completeTaskList[i] == a.id) {
        isATaked = true;
        break;
      }
    }
    let isBTaked = false;
    let bFinishTimes = ClubModule.data.clubFormMessage.dailyTask.taskCntMap[b.taskId];
    let isBfinish = bFinishTimes >= b.finish;
    for (let i = 0; i < ClubModule.data.clubFormMessage.dailyTask.completeTaskList.length; i++) {
      if (ClubModule.data.clubFormMessage.dailyTask.completeTaskList[i] == b.id) {
        isBTaked = true;
        break;
      }
    }
    if (isATaked && isBTaked) {
      return a.id - b.id;
    } else if (isATaked) {
      return 1;
    } else if (isBTaked) {
      return -1;
    } else if (isAfinish && isBfinish) {
      return a.id - b.id;
    } else if (isAfinish) {
      return -1;
    } else if (isBfinish) {
      return 1;
    } else {
      return a.id - b.id;
    }
  }

  getCount(): number {
    return this.datas.length;
  }
  getViewType(position: number): number {
    return 0;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.getComponent(ClubTaskViewHolder).init();
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    // update view here;
    node.getComponent(ClubTaskViewHolder).updateData(this.datas[position]);
  }
}
