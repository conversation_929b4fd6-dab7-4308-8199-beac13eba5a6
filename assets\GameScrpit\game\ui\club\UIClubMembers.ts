import { _decorator, Label, log, RichText } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ClubMembersAdapter } from "./adapter/ClubMemberViewHolder";
import { ClubModule } from "../../../module/club/ClubModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { CLUB_POSITION, ClubEvent } from "../../../module/club/ClubConstant";
import { ClubApplyMessage } from "../../net/protocol/Club";
import { ClubApplyListAdapter } from "./adapter/ClubApplyListViewHolder";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";
import { PlayerModule } from "../../../module/player/PlayerModule";
import Formate from "../../../lib/utils/Formate";
import FmUtils from "../../../lib/utils/FmUtils";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ClubAudioName } from "../../../module/club/ClubConfig";
import { ClubRouteItem } from "../../../module/club/ClubRoute";
import TipMgr from "../../../lib/tips/TipMgr";
import { CityRouteName } from "../../../module/city/CityConstant";
import MsgMgr from "../../../lib/event/MsgMgr";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIClubConfirmDialog } from "./UIClubConfirmDialog";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu Aug 15 2024 19:22:47 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubMembers.ts
 *
 */

@ccclass("UIClubMembers")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_CLUB,
  url: "prefab/ui/UIClubMembers",
  nextHop: [],
  exit: "dialog_close",
})
export class UIClubMembers extends BaseCtrl {
  public playShowAni: boolean = true;

  private _adapter: ClubMembersAdapter;
  //=================================================

  private _applyAdapter: ClubApplyListAdapter;
  public init(args: any): void {
    super.init(args);
  }

  protected start(): void {
    super.start();
    MsgMgr.on(ClubEvent.CLUB_DATA_CHANGE, this.onClubDataChange, this);

    BadgeMgr.instance.setBadgeId(this.getNode("btn_tab2"), BadgeType.UIClubMain.btn_apply.id);

    this._adapter = new ClubMembersAdapter(this.getNode("member_viewholder"));
    this.getNode("members_list").getComponent(AdapterView).setAdapter(this._adapter);
    let members = ClubModule.data.clubMessage.memberList;
    this._adapter.setDatas(members);

    this._applyAdapter = new ClubApplyListAdapter(this.getNode("member_apply_viewholder"));
    this.getNode("list_apply").getComponent(AdapterView).setAdapter(this._applyAdapter);

    this.getNode("node_tab").active = false;
    this.getNode("node_my_mengzhu").active = false;
    this.getNode("node_my_fumengzhu").active = false;
    if (ClubModule.data.position == CLUB_POSITION.盟主) {
      this.getNode("node_tab").active = true;
      this.getNode("node_my_mengzhu").active = true;
    } else if (ClubModule.data.position == CLUB_POSITION.副盟主) {
      this.getNode("node_my_fumengzhu").active = true;
      this.getNode("node_tab").active = true;
    }
    this.getNode("lbl_my_power").getComponent(Label).string = `${Formate.format(
      PlayerModule.data._playerBattleAttrResponse.power
    )}`;
    this.getNode("lbl_my_name").getComponent(Label).string = PlayerModule.data.playerDataMsg.nickname;
    let configLeader = PlayerModule.data.getConfigLeaderData(PlayerModule.data.playerDataMsg.level);
    this.getNode("rich_my_title_lv").getComponent(RichText).string = configLeader.jingjie2;

    let data = ToolExt.newPlayerBaseMessage();
    data.avatarList = PlayerModule.data.getMyAvatarList();
    data.vipLevel = PlayerModule.data.getPlayerInfo().vipLevel;
    FmUtils.setHeaderNode(this.getNode("btn_my_header"), data);
  }
  protected onClubDataChange() {
    if (ClubModule.data.clubMessage) {
      let members = ClubModule.data.clubMessage.memberList;
      this._adapter.setDatas(members);
    }
  }
  private on_click_btn_tab1() {
    this.getNode("node_members").active = true;
    this.getNode("node_apply").active = false;
    this.getNode("btn_tab1").getComponent(FmButton).selected = true;
    this.getNode("btn_tab2").getComponent(FmButton).selected = false;
  }
  private on_click_btn_tab2() {
    this.getNode("node_members").active = false;
    this.getNode("node_apply").active = true;
    this.getNode("btn_tab1").getComponent(FmButton).selected = false;
    this.getNode("btn_tab2").getComponent(FmButton).selected = true;
    ClubModule.api.listApply((data: ClubApplyMessage[]) => {
      this._applyAdapter.setData(data);
    });
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    this.closeBack();
  }

  private on_click_btn_exit() {
    let dialogCallBack = (isOK: boolean) => {
      if (isOK) {
        ClubModule.api.leaveBySelf(() => {
          TipMgr.showTip("退出成功");
          this.closeBack();
          UIMgr.instance.showPage(CityRouteName.UIGameMap);
          UIMgr.instance.showDialog(ClubRouteItem.UIClubList);
        });
      }
    };
    AudioMgr.instance.playEffect(ClubAudioName.Effect.管理点击盟主让位成员任免退盟按钮);
    let dialogArgs: RouteShowArgs = {
      payload: { title: "退出联盟", content: "确定要退出联盟吗？", callback: dialogCallBack },
    };
    RouteManager.uiRouteCtrl.showRoute(UIClubConfirmDialog, dialogArgs);
  }
}
