{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "fb3017a9-8f52-4a15-be90-892f302ac6d2", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "fb3017a9-8f52-4a15-be90-892f302ac6d2@6c48a", "displayName": "zhandou_shanghai", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "fb3017a9-8f52-4a15-be90-892f302ac6d2", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "fb3017a9-8f52-4a15-be90-892f302ac6d2@f9941", "displayName": "zhandou_shanghai", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -31, "offsetY": 1.5, "trimX": 0, "trimY": 0, "width": 66, "height": 253, "rawWidth": 128, "rawHeight": 256, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-33, -126.5, 0, 33, -126.5, 0, -33, 126.5, 0, 33, 126.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 256, 66, 256, 0, 3, 66, 3], "nuv": [0, 0.01171875, 0.515625, 0.01171875, 0, 1, 0.515625, 1], "minPos": [-33, -126.5, 0], "maxPos": [33, 126.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "fb3017a9-8f52-4a15-be90-892f302ac6d2@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "fb3017a9-8f52-4a15-be90-892f302ac6d2@6c48a"}}