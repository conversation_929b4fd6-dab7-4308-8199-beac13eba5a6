import { _decorator, Component, Node, PageView, Event, UITransform, v3, Label } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

const titleList = ["收集/提升战将", "提升据点等级", "收集/升级兽魂", "收集/升级至宝", "收集/升级仙友"];

@ccclass("UIStrategy")
export class UIStrategy extends UINode {
  protected _openAct: boolean = true;
  private _pageView: PageView = null;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_STRATEGY}?prefab/ui/UIStrategy`;
  }

  protected onRegEvent(): void {
    this._pageView = this.getNode("page_layer").getComponent(PageView);
    // 使用系统事件监听（修正后的方式）
    this._pageView.node.on(PageView.EventType.PAGE_TURNING, this.onPageChanged, this);
  }

  protected onDelEvent(): void {
    if (this._pageView) {
      // 使用系统事件移除监听
      this._pageView.node.off(PageView.EventType.PAGE_TURNING, this.onPageChanged, this);
    }
  }

  protected onEvtShow(): void {
    this.setTileLab();
  }

  private onPageChanged(event: PageView) {
    const pageView = event as PageView;
    this.setTileLab();
  }

  private setTileLab() {
    let index = this._pageView.getCurrentPageIndex();

    let lab = titleList[index];
    this.getNode("lbl_title").getComponent(Label).string = lab;

    this.getNode("lbl_hit_layer").children.forEach((val) => {
      val.active = false;
    });

    this.getNode("lbl_hit_" + index).active = true;

    this.getNode("des_layer").children.forEach((val) => {
      val.active = false;
    });

    this.getNode("lbl_des_" + index).active = true;
    this.getNode("indicator_layer").children.forEach((val) => {
      if (val.name.split("_")[0] == "dot") {
        val.getChildByName("selected").active = false;
        val.getChildByName("normal").active = true;
      }
    });

    let dot = this.getNode("dot_" + index);
    dot.getChildByName("selected").active = true;
    dot.getChildByName("normal").active = false;
  }

  on_click_btn_close_lan() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_left_go() {
    AudioMgr.instance.playEffect(1861);
    let index = this._pageView.getCurrentPageIndex();
    let newIndex = index - 1;
    if (newIndex < 0) {
      newIndex = 0;
    }
    this._pageView.setCurrentPageIndex(newIndex);
  }

  private on_click_btn_right_go() {
    AudioMgr.instance.playEffect(1861);
    let index = this._pageView.getCurrentPageIndex();
    let list = this._pageView.getPages();

    let newIndex = index + 1;
    if (newIndex >= list.length) {
      newIndex = index;
    }
    this._pageView.setCurrentPageIndex(newIndex);
  }

  // public tick(dt: any): void {
  //   let width = this.getNode("indicator").getComponent(UITransform).width;
  //   let pos = v3(width * 0.5 + 80, 0, 0);
  //   this.getNode("icon_jiantou_6").setPosition(pos);
  //   pos = v3(-width * 0.5 - 80, 0, 0);
  //   this.getNode("icon_jiantou_5").setPosition(pos);
  // }
}
