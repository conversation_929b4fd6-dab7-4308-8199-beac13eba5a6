{"skeleton": {"hash": "ZhSaawZwnV9iqjrL18VLyI7kqK8=", "spine": "3.8.75", "x": -84.14, "y": -11.6, "width": 195.72, "height": 342.38, "images": "./images/", "audio": "D:/spine导出/主角换皮动画/主角/主角3"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": -13.02}, {"name": "bone2", "parent": "bone", "length": 75.15, "rotation": 0.7, "x": 0.97, "y": 166.52}, {"name": "bone3", "parent": "bone2", "length": 42.77, "rotation": 97.43, "x": 2.26, "y": 4.99}, {"name": "bone4", "parent": "bone3", "length": 34.8, "rotation": 0.62, "x": 42.77}, {"name": "bone5", "parent": "bone4", "length": 10.59, "rotation": -6.7, "x": 34.8}, {"name": "bone6", "parent": "bone5", "x": 27.33, "y": -15.43}, {"name": "bone13", "parent": "bone6", "x": 27.12, "y": -3.13}, {"name": "bone14", "parent": "bone13", "length": 11.27, "rotation": 59.14, "x": 2.29, "y": 3.62}, {"name": "bone15", "parent": "bone14", "length": 14.53, "rotation": 60.65, "x": 11.27}, {"name": "bone16", "parent": "bone15", "length": 15, "rotation": 14.51, "x": 14.53}, {"name": "bone17", "parent": "bone16", "length": 19.08, "rotation": -18.1, "x": 14.23, "y": -0.35}, {"name": "bone18", "parent": "bone13", "length": 10.35, "rotation": -70.79, "x": 1.72, "y": -4.35}, {"name": "bone19", "parent": "bone18", "length": 17.62, "rotation": -89.12, "x": 10.35}, {"name": "bone20", "parent": "bone19", "length": 19.37, "rotation": 17.98, "x": 18.81, "y": -0.14}, {"name": "bone22", "parent": "bone6", "length": 17.79, "rotation": 145.16, "x": -9.65, "y": 40.06}, {"name": "bone23", "parent": "bone22", "length": 18.21, "rotation": 15.09, "x": 17.79}, {"name": "bone24", "parent": "bone23", "length": 21.2, "rotation": -19.04, "x": 18.21}, {"name": "bone31", "parent": "bone4", "x": 21.73, "y": 31.05}, {"name": "bone36", "parent": "bone31", "length": 37.62, "rotation": 153.83, "x": -1.83, "y": 1.87}, {"name": "bone37", "parent": "bone36", "length": 44.81, "rotation": 18.1, "x": 37.62}, {"name": "bone38", "parent": "bone37", "length": 16.25, "rotation": 1.18, "x": 44.81}, {"name": "bone62", "parent": "bone38", "length": 42.77, "rotation": 69.27, "x": 8.88, "y": 0.27}, {"name": "bone32", "parent": "bone4", "x": 14.46, "y": -24.82}, {"name": "bone33", "parent": "bone32", "length": 35.85, "rotation": -173.49, "x": -8.17, "y": -1.39}, {"name": "bone34", "parent": "bone33", "length": 35.66, "rotation": -2.95, "x": 35.85}, {"name": "bone35", "parent": "bone34", "length": 15.47, "rotation": -1.56, "x": 35.66}, {"name": "bone44", "parent": "bone2", "length": 17.63, "rotation": -79.05, "x": 23.32, "y": -16.68, "scaleX": 1.5545, "color": "52f94bff"}, {"name": "bone45", "parent": "bone44", "length": 14.89, "rotation": 14.61, "x": 17.67, "y": 0.17, "scaleX": 1.3044, "color": "52f94bff"}, {"name": "bone46", "parent": "bone45", "length": 13.32, "rotation": 11.68, "x": 14.89, "scaleX": 1.3458, "color": "52f94bff"}, {"name": "bone49", "parent": "bone2", "length": 15.88, "rotation": -103, "x": -22.32, "y": -14.94, "scaleX": 1.495, "scaleY": 1.2559, "color": "52f94bff"}, {"name": "bone50", "parent": "bone49", "length": 15.85, "rotation": -4.68, "x": 15.88, "scaleX": 1.495, "scaleY": 1.2559, "color": "52f94bff"}, {"name": "bone51", "parent": "bone50", "length": 11.93, "rotation": -14.97, "x": 15.85, "scaleX": 1.495, "scaleY": 1.2559, "color": "52f94bff"}, {"name": "bone52", "parent": "bone2", "length": 73.79, "rotation": -93.46, "x": -17.36, "y": -3.86}, {"name": "bone53", "parent": "bone52", "length": 56.02, "rotation": -0.27, "x": 73.79}, {"name": "bone54", "parent": "bone53", "length": 23, "rotation": 30.32, "x": 56.63, "y": -0.26}, {"name": "bone56", "parent": "bone2", "length": 76.08, "rotation": -85.57, "x": 10.47, "y": -3.6}, {"name": "bone57", "parent": "bone56", "length": 55.47, "rotation": -8.81, "x": 76.08}, {"name": "bone7", "parent": "bone2", "length": 32.5, "rotation": -89.97, "x": 4.98, "y": -7.99}, {"name": "b1one8", "parent": "bone7", "length": 30.64, "rotation": -0.34, "x": 32.5}, {"name": "b1one9", "parent": "b1one8", "length": 25.47, "rotation": -1.78, "x": 30.64}, {"name": "b1one10", "parent": "b1one9", "length": 27.78, "rotation": -1.59, "x": 25.47}, {"name": "f<PERSON><PERSON>an", "parent": "root", "x": 49.46, "y": 384.69, "scaleX": 1.7354, "scaleY": 1.7354}, {"name": "bone58", "parent": "bone57", "length": 26, "rotation": 63.6, "x": 55.4, "y": -1.04}, {"name": "bone11", "parent": "bone2", "length": 80.82, "rotation": -77.14, "x": -15.78, "y": 0.13}, {"name": "bone12", "parent": "bone11", "length": 61.66, "rotation": -39.59, "x": 80.82, "color": "abe323ff"}, {"name": "bone8", "parent": "bone2", "length": 78.02, "rotation": -72.31, "x": 13.69, "y": -3.79}, {"name": "bone9", "parent": "bone8", "length": 61.69, "rotation": -41.6, "x": 78.02, "color": "abe323ff"}, {"name": "bone55", "parent": "bone", "length": 35.96, "x": -34.86, "y": 13.01}, {"name": "bone59", "parent": "bone55", "length": 29.87, "rotation": 89.72, "x": -0.87, "y": -0.29}, {"name": "bone60", "parent": "bone", "length": 39.3, "rotation": -0.63, "x": 9.66, "y": 13.59}, {"name": "bone61", "parent": "bone60", "length": 28.86, "rotation": 90.92, "x": -1.31, "y": 0.42}, {"name": "ljio1", "parent": "bone61", "rotation": -90.29, "x": 73.37, "y": -10.25, "color": "ff3f00ff"}, {"name": "ljio2", "parent": "bone61", "rotation": -90.29, "x": 17.78, "y": -5.74, "color": "ff3f00ff"}, {"name": "ljio3", "parent": "ljio2", "x": 21.4, "y": -12.87, "color": "ff3f00ff"}, {"name": "rjio1", "parent": "bone59", "rotation": -89.72, "x": 76.36, "y": -15.55, "color": "ff3f00ff"}, {"name": "rjio2", "parent": "bone59", "rotation": -89.72, "x": 19.28, "y": -12.13, "color": "ff3f00ff"}, {"name": "rjio3", "parent": "rjio2", "x": 10.25, "y": -20.46, "color": "ff3f00ff"}], "slots": [{"name": "qunbian", "bone": "bone2", "attachment": "qunbian"}, {"name": "m5", "bone": "bone22", "attachment": "m5"}, {"name": "s2", "bone": "bone33", "attachment": "s2"}, {"name": "j2", "bone": "bone56", "attachment": "j2"}, {"name": "j1", "bone": "bone52", "attachment": "j1"}, {"name": "paidai3", "bone": "bone44", "attachment": "paidai3"}, {"name": "pd1", "bone": "bone49", "attachment": "pd1"}, {"name": "bd", "bone": "bone3", "attachment": "bd"}, {"name": "yaodai2", "bone": "bone2", "attachment": "yaodai2"}, {"name": "yd", "bone": "bone2", "attachment": "yd"}, {"name": "tou", "bone": "bone5", "attachment": "tou"}, {"name": "wuqi", "bone": "bone62", "attachment": "wuqi"}, {"name": "biyan", "bone": "bone6"}, {"name": "m4", "bone": "bone5", "attachment": "m4"}, {"name": "m3", "bone": "bone18", "attachment": "m3"}, {"name": "m2", "bone": "bone14", "attachment": "m2"}, {"name": "s1", "bone": "bone36", "attachment": "s1"}, {"name": "mutou2", "bone": "f<PERSON><PERSON>an"}, {"name": "xuanz", "bone": "f<PERSON><PERSON>an", "color": "ffffff6f", "blend": "additive"}], "ik": [{"name": "ljio1", "order": 2, "bones": ["bone56"], "target": "ljio1", "compress": true, "stretch": true}, {"name": "ljio2", "order": 3, "bones": ["bone57"], "target": "ljio2", "compress": true, "stretch": true}, {"name": "ljio3", "order": 4, "bones": ["bone58"], "target": "ljio3"}, {"name": "ljio4", "bones": ["bone8", "bone9"], "target": "ljio2", "bendPositive": false}, {"name": "rjio1", "order": 7, "bones": ["bone52"], "target": "rjio1", "compress": true, "stretch": true}, {"name": "rjio2", "order": 8, "bones": ["bone53"], "target": "rjio2", "compress": true, "stretch": true}, {"name": "rjio3", "order": 9, "bones": ["bone54"], "target": "rjio3"}, {"name": "rjio4", "order": 5, "bones": ["bone11", "bone12"], "target": "rjio2", "bendPositive": false}], "transform": [{"name": "ljio5", "order": 1, "bones": ["ljio1"], "target": "bone9", "rotation": 113.21, "x": 8.9, "y": -18.03, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "rjio5", "order": 6, "bones": ["rjio1"], "target": "bone12", "rotation": 115.45, "x": 8.55, "y": -21.18, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}], "skins": [{"name": "default", "attachments": {"mutou2": {"mutou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.4, -51.34, -68.62, 12.5, -48.9, 51.12, 76.12, -12.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 120}}, "bd": {"bd": {"type": "mesh", "uvs": [0.39245, 0.0879, 0.41031, 0.03327, 0.51259, 0, 0.60026, 0.02321, 0.61974, 0.07496, 0.70904, 0.1109, 0.7821, 0.17415, 0.86327, 0.23452, 0.86652, 0.27908, 0.93146, 0.31502, 1, 0.37108, 0.98828, 0.45302, 0.94445, 0.50477, 0.89899, 0.5824, 0.9136, 0.69021, 0.95906, 0.82102, 0.90386, 0.91158, 0.75287, 0.99783, 0.65546, 1, 0.53207, 1, 0.36647, 0.96046, 0.23822, 0.91014, 0.17165, 0.83396, 0.19925, 0.73908, 0.19925, 0.61115, 0.11158, 0.5134, 0.03203, 0.42715, 0, 0.36102, 0.02391, 0.32365, 0.09535, 0.29921, 0.18302, 0.26327, 0.17977, 0.20721, 0.27069, 0.14396, 0.34862, 0.09077, 0.53531, 0.09221, 0.55804, 0.22733, 0.58889, 0.35815, 0.6311, 0.54646, 0.65221, 0.74914, 0.65546, 0.89289, 0.30153, 0.39121, 0.38757, 0.58096, 0.43628, 0.79227, 0.83729, 0.43433, 0.82105, 0.56083, 0.79183, 0.74483, 0.76586, 0.87852, 0.46388, 0.89146, 0.31614, 0.85408, 0.15594, 0.41089, 0.91831, 0.40661, 0.40093, 0.22965, 0.71845, 0.22395], "triangles": [21, 48, 20, 17, 46, 16, 17, 18, 39, 18, 19, 39, 20, 47, 19, 43, 50, 12, 43, 8, 50, 50, 8, 9, 50, 9, 10, 11, 50, 10, 12, 50, 11, 25, 49, 24, 26, 28, 29, 26, 29, 49, 27, 28, 26, 49, 29, 30, 25, 26, 49, 24, 49, 40, 49, 30, 40, 34, 2, 3, 34, 3, 4, 1, 2, 34, 0, 1, 34, 35, 34, 4, 35, 4, 5, 51, 0, 34, 51, 34, 35, 33, 0, 51, 32, 33, 51, 52, 5, 6, 35, 5, 52, 30, 31, 32, 30, 32, 51, 36, 35, 52, 40, 30, 51, 52, 7, 8, 7, 52, 6, 43, 52, 8, 36, 52, 43, 37, 36, 43, 44, 37, 43, 44, 43, 12, 36, 41, 40, 13, 44, 12, 24, 40, 41, 37, 41, 36, 36, 51, 35, 51, 36, 40, 23, 24, 41, 44, 38, 37, 14, 45, 44, 14, 44, 13, 45, 38, 44, 38, 41, 37, 38, 42, 41, 23, 41, 42, 45, 14, 15, 48, 23, 42, 22, 23, 48, 46, 38, 45, 46, 45, 15, 47, 42, 38, 39, 47, 38, 48, 42, 47, 46, 39, 38, 21, 22, 48, 16, 46, 15, 20, 48, 47, 17, 39, 46, 19, 47, 39], "vertices": [2, 4, 46.96, 6.02, 0.04315, 5, 11.37, 7.4, 0.95685, 1, 5, 16.56, 5.69, 1, 1, 5, 19.44, -3.11, 1, 2, 4, 50.41, -12.38, 0.03061, 5, 16.94, -10.48, 0.96939, 2, 4, 45.25, -13.27, 0.14669, 5, 11.92, -11.96, 0.85331, 3, 3, 83.66, -19.8, 2e-05, 4, 40.68, -20.24, 0.46953, 5, 8.2, -19.42, 0.53045, 3, 3, 76.77, -25.09, 0.00691, 4, 33.74, -25.46, 0.71716, 5, 1.91, -25.41, 0.27593, 4, 3, 70.06, -31.1, 0.02784, 4, 26.96, -31.4, 0.69612, 5, -4.13, -32.1, 0.10804, 23, 12.5, -6.57, 0.168, 4, 3, 65.79, -30.77, 0.04962, 4, 22.69, -31.02, 0.69205, 5, -8.41, -32.22, 0.06634, 23, 8.23, -6.19, 0.192, 1, 23, 3.98, -11.13, 1, 1, 23, -2.23, -16.06, 1, 1, 23, -9.85, -13.88, 1, 4, 3, 43.4, -34.27, 0.21993, 4, 0.27, -34.27, 0.49177, 5, -30.3, -38.07, 0.00029, 23, -14.2, -9.45, 0.288, 2, 3, 36.57, -29.39, 0.5638, 4, -6.51, -29.32, 0.4362, 2, 3, 26.15, -29.15, 0.86404, 4, -16.93, -28.97, 0.13596, 2, 3, 13.17, -31.2, 0.98198, 4, -29.93, -30.88, 0.01802, 1, 2, 26.7, 13.45, 1, 1, 2, 13.76, 5.33, 1, 1, 2, 5.48, 5.22, 1, 1, 2, -5.01, 5.35, 1, 1, 2, -19.04, 9.32, 1, 1, 2, -29.88, 14.28, 1, 2, 3, 21.41, 35.23, 0.92943, 4, -20.98, 35.46, 0.07057, 2, 3, 30.09, 31.62, 0.82268, 4, -12.33, 31.76, 0.17732, 3, 3, 42.25, 29.88, 0.44134, 4, -0.19, 29.89, 0.55864, 5, -38.25, 25.6, 1e-05, 4, 3, 52.59, 35.93, 0.08473, 4, 10.21, 35.83, 0.55789, 5, -28.6, 32.71, 0.00538, 18, -11.52, 4.78, 0.352, 1, 18, -2.31, 10.2, 1, 1, 18, 4.38, 11.93, 1, 1, 18, 7.62, 9.37, 1, 1, 18, 9.01, 3.01, 1, 4, 3, 75.51, 26.53, 0.00248, 4, 33.02, 26.17, 0.55276, 5, -4.82, 25.79, 0.13276, 18, 11.29, -4.88, 0.312, 3, 3, 80.87, 26.04, 0.0001, 4, 38.38, 25.63, 0.68558, 5, 0.57, 25.87, 0.31432, 2, 4, 43.21, 17.07, 0.43268, 5, 6.36, 17.93, 0.56732, 2, 4, 47.25, 9.74, 0.139, 5, 11.23, 11.13, 0.861, 2, 4, 44.7, -5.92, 0.02988, 5, 10.52, -4.72, 0.97012, 2, 4, 31.59, -5.86, 0.78075, 5, -2.51, -6.19, 0.21925, 3, 3, 61.61, -6.34, 0.00186, 4, 18.78, -6.54, 0.99074, 5, -15.15, -8.36, 0.0074, 2, 3, 43.21, -7.33, 0.45296, 4, 0.36, -7.34, 0.54704, 2, 3, 23.69, -6.36, 0.99531, 4, -19.14, -6.15, 0.00469, 1, 3, 9.99, -4.68, 1, 4, 3, 61.92, 18.29, 0.02767, 4, 19.35, 18.09, 0.68307, 5, -17.45, 16.16, 0.02526, 18, -2.38, -12.97, 0.264, 2, 3, 42.86, 13.63, 0.46942, 4, 0.24, 13.63, 0.53058, 2, 3, 22.19, 12.4, 0.97765, 4, -20.44, 12.62, 0.02235, 4, 3, 51.38, -26.2, 0.16147, 4, 8.34, -26.3, 0.60033, 5, -23.22, -29.2, 0.0062, 23, -6.13, -1.47, 0.232, 2, 3, 39.56, -23.12, 0.53002, 4, -3.46, -23.09, 0.46998, 2, 3, 22.42, -18.16, 0.9469, 4, -20.54, -17.94, 0.0531, 2, 3, 10.03, -14.16, 0.99902, 4, -32.89, -13.81, 0.00098, 2, 3, 12.43, 11.42, 0.99918, 4, -30.21, 11.75, 0.00082, 2, 3, 17.76, 23.35, 0.96627, 4, -24.76, 23.61, 0.03373, 1, 18, -2.37, -0.45, 1, 1, 23, -4.54, -8.68, 1, 2, 4, 33.4, 7.38, 0.6705, 5, -2.26, 7.16, 0.3295, 3, 3, 72.81, -19.06, 0.00579, 4, 29.83, -19.38, 0.74686, 5, -2.67, -19.83, 0.24735], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 4, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 60, 80, 80, 82, 82, 84, 16, 86, 86, 88, 88, 90, 90, 92, 84, 94, 84, 96], "width": 85, "height": 96}}, "j1": {"j1": {"type": "mesh", "uvs": [0.63451, 0, 0.92674, 0.03453, 1, 0.12862, 1, 0.29799, 1, 0.42658, 1, 0.52694, 0.92674, 0.59908, 0.69945, 0.6273, 0.64533, 0.72139, 0.60204, 0.81548, 0.65615, 0.84842, 0.79145, 0.86723, 0.8618, 0.92996, 0.75356, 1, 0.55874, 1, 0.34768, 0.99112, 0.19615, 0.93937, 0.09874, 0.87978, 0.15286, 0.81862, 0.18533, 0.76373, 0.14745, 0.67435, 0.12039, 0.60221, 0, 0.56458, 0, 0.45637, 0.08251, 0.34346, 0.15827, 0.23055, 0.22321, 0.13176, 0.30439, 0.01728, 0.60745, 0.1051, 0.59121, 0.21017, 0.5858, 0.3121, 0.51545, 0.43285, 0.45592, 0.51753, 0.43427, 0.6226, 0.39639, 0.71512, 0.35851, 0.78412, 0.38015, 0.86567, 0.53168, 0.92526, 0.65615, 0.95976], "triangles": [37, 10, 11, 38, 37, 11, 12, 38, 11, 13, 38, 12, 37, 36, 10, 16, 36, 37, 15, 16, 37, 14, 15, 37, 38, 14, 37, 14, 38, 13, 36, 9, 10, 18, 35, 36, 17, 18, 36, 16, 17, 36, 32, 31, 5, 22, 23, 32, 6, 32, 5, 21, 22, 32, 33, 21, 32, 7, 33, 32, 6, 7, 32, 20, 21, 33, 34, 20, 33, 8, 33, 7, 34, 33, 8, 19, 20, 34, 35, 19, 34, 8, 35, 34, 9, 35, 8, 18, 19, 35, 36, 35, 9, 28, 27, 0, 28, 0, 1, 28, 1, 2, 26, 27, 28, 29, 26, 28, 29, 28, 2, 25, 26, 29, 29, 2, 3, 30, 25, 29, 30, 29, 3, 24, 25, 30, 30, 3, 4, 31, 24, 30, 31, 30, 4, 23, 24, 31, 32, 23, 31, 31, 4, 5], "vertices": [1, 33, -14.55, 3.67, 1, 1, 33, -9.19, 18.85, 1, 1, 33, 7.17, 23.38, 1, 1, 33, 36.94, 24.82, 1, 2, 33, 59.51, 25.88, 0.89425, 34, -14.38, 25.63, 0.10575, 2, 33, 76.97, 26.78, 0.43948, 34, 2.65, 26.83, 0.56052, 2, 33, 89.76, 23.83, 0.15989, 34, 15.18, 24.11, 0.84011, 2, 33, 95.39, 12.61, 0.01747, 34, 20.87, 12.98, 0.98253, 1, 34, 37.2, 11.56, 1, 2, 34, 53.49, 10.69, 0.84316, 35, 3.19, 10.94, 0.15684, 2, 34, 58.94, 13.9, 0.30274, 35, 9.51, 10.75, 0.69726, 2, 34, 61.64, 21.03, 0.07803, 35, 15.6, 15.34, 0.92197, 2, 34, 72.12, 25.47, 0.00593, 35, 26.82, 13.51, 0.99407, 1, 35, 34.91, 3.04, 1, 1, 35, 30.26, -5.74, 1, 1, 35, 23.88, -14.54, 1, 1, 35, 12.39, -17.25, 1, 2, 34, 66.47, -14.02, 0.01998, 35, 1, -16.88, 0.98002, 2, 34, 55.8, -12.1, 0.41219, 35, -7.01, -9.57, 0.58781, 2, 34, 46.28, -11.22, 0.93429, 35, -14.59, -3.75, 0.06571, 1, 34, 31.13, -14.41, 1, 2, 33, 92.82, -17.15, 0.04758, 34, 18.87, -16.82, 0.95242, 2, 33, 86.54, -23.72, 0.18626, 34, 12.85, -23.49, 0.81374, 2, 33, 67.34, -24.81, 0.7383, 34, -5.88, -24.92, 0.2617, 2, 33, 47.19, -21.54, 0.99687, 34, -25.6, -22, 0.00313, 1, 33, 27.15, -18.63, 1, 1, 33, 9.62, -16.16, 1, 1, 33, -10.7, -13, 1, 1, 33, 4, 3.19, 1, 1, 33, 22.51, 3.25, 1, 1, 33, 40.44, 3.84, 1, 1, 33, 61.84, 1.28, 1, 1, 34, 3.04, -0.94, 1, 1, 34, 21.12, -0.56, 1, 1, 34, 37.12, -1.18, 1, 2, 34, 49.09, -2.13, 0.98148, 35, -7.38, 2.44, 0.01852, 1, 35, 5.56, -3.07, 1, 1, 35, 18.24, -1, 1, 1, 35, 26.46, 1.86, 1], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 0, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76], "width": 51, "height": 176}}, "j2": {"j2": {"type": "mesh", "uvs": [0.28704, 0, 0.49392, 0.00552, 0.63345, 0.09103, 0.69119, 0.21166, 0.75373, 0.33381, 0.82109, 0.47276, 0.86439, 0.57354, 0.70081, 0.63004, 0.65751, 0.72166, 0.59496, 0.8148, 0.57571, 0.86061, 0.70081, 0.90184, 0.98949, 0.87435, 1, 0.93543, 0.94619, 0.99345, 0.74892, 1, 0.49392, 0.98124, 0.24855, 0.98429, 0.08496, 0.95528, 0.11383, 0.88351, 0.11383, 0.81175, 0.10902, 0.72166, 0.06572, 0.6163, 0, 0.59187, 0, 0.47429, 0, 0.35977, 0, 0.23303, 0, 0.13531, 0, 0.04522, 0.15232, 0.00857, 0.3159, 0.08034, 0.33996, 0.18723, 0.34958, 0.30938, 0.37364, 0.44223, 0.37845, 0.54606, 0.36882, 0.64073, 0.3592, 0.7354, 0.33515, 0.82854, 0.33515, 0.91558, 0.53241, 0.93238, 0.75854, 0.94612], "triangles": [17, 38, 16, 17, 18, 38, 18, 19, 38, 39, 38, 10, 19, 37, 38, 38, 37, 10, 15, 16, 40, 15, 40, 14, 40, 16, 39, 14, 40, 13, 16, 38, 39, 39, 11, 40, 13, 40, 12, 40, 11, 12, 39, 10, 11, 19, 20, 37, 10, 37, 9, 9, 37, 36, 37, 20, 36, 9, 36, 8, 20, 21, 36, 36, 35, 8, 36, 21, 35, 8, 35, 7, 21, 22, 35, 35, 34, 7, 35, 22, 34, 7, 34, 6, 22, 23, 34, 23, 24, 34, 34, 5, 6, 24, 33, 34, 34, 33, 5, 24, 25, 33, 33, 4, 5, 25, 32, 33, 33, 32, 4, 25, 26, 32, 32, 3, 4, 26, 31, 32, 32, 31, 3, 26, 27, 31, 31, 2, 3, 27, 30, 31, 31, 30, 2, 27, 28, 30, 30, 1, 2, 28, 29, 30, 29, 0, 30, 30, 0, 1], "vertices": [1, 36, -13.58, 2.62, 1, 1, 36, -11.68, 13.46, 1, 1, 36, 3.2, 19.55, 1, 1, 36, 23.54, 20.8, 1, 1, 36, 44.16, 22.27, 1, 2, 36, 67.59, 23.75, 0.79851, 37, -12.29, 22.03, 0.20149, 2, 36, 84.55, 24.53, 0.32239, 37, 4.31, 25.59, 0.67761, 2, 36, 93.18, 15.05, 0.06102, 37, 14.38, 17.66, 0.93898, 1, 37, 29.81, 16.54, 1, 2, 37, 45.56, 14.41, 0.88633, 43, 9.57, 15.61, 0.11367, 2, 37, 53.22, 13.97, 0.36136, 43, 12.53, 8.54, 0.63864, 2, 37, 59.5, 21.13, 0.00522, 43, 21.73, 6.03, 0.99478, 1, 43, 32.54, 17.79, 1, 1, 43, 38.23, 9.3, 1, 1, 43, 40.72, -0.49, 1, 1, 43, 32.29, -6.76, 1, 1, 43, 19.07, -10.97, 1, 1, 43, 8.15, -18.05, 1, 1, 43, -1.78, -18.31, 1, 1, 43, -6.58, -7.22, 1, 2, 37, 47.02, -11.07, 0.57633, 43, -12.69, 3.14, 0.42367, 2, 37, 32.02, -12.45, 0.98876, 43, -20.5, 16.02, 0.01124, 2, 36, 87.88, -18.27, 0.09355, 37, 14.65, -16.07, 0.90645, 2, 36, 83.5, -21.37, 0.2095, 37, 10.84, -19.86, 0.7905, 2, 36, 63.95, -19.61, 0.86292, 37, -8.74, -21.35, 0.13708, 1, 36, 44.9, -17.9, 1, 1, 36, 23.82, -16.01, 1, 1, 36, 7.56, -14.55, 1, 1, 36, -7.42, -13.2, 1, 1, 36, -12.79, -4.61, 1, 1, 36, -0.08, 2.95, 1, 1, 36, 17.81, 2.62, 1, 1, 36, 38.17, 1.3, 1, 1, 36, 60.38, 0.59, 1, 2, 36, 77.68, -0.71, 0.06835, 37, 1.69, -0.44, 0.93165, 1, 37, 17.5, 0.25, 1, 1, 37, 33.3, 0.95, 1, 1, 37, 48.91, 0.85, 1, 1, 43, 6.24, -5.84, 1, 1, 43, 16.66, -2.91, 1, 1, 43, 28.13, 1.23, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80], "width": 53, "height": 167}}, "m4": {"m4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.92, 10.59, 16.46, 25.58, 48.44, 24.44, 47.9, 9.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 32}}, "yd": {"yd": {"type": "mesh", "uvs": [0.05869, 0.01298, 0.19932, 0, 0.37808, 0.00211, 0.49726, 0.05648, 0.5783, 0.14348, 0.64742, 0.11448, 0.6951, 0.17973, 0.738, 0.05286, 0.83811, 0.00573, 0.96205, 0.00573, 0.94299, 0.22323, 0.96444, 0.44798, 0.97636, 0.64736, 1, 0.83223, 0.88578, 0.82861, 0.79997, 0.78873, 0.738, 0.9156, 0.64742, 1, 0.5354, 0.9011, 0.46151, 0.77061, 0.35663, 0.86123, 0.15641, 0.86848, 0, 0.74161, 0.07537, 0.56398, 0.11351, 0.43348, 0.11112, 0.21598, 0.28036, 0.47336, 0.49726, 0.49148, 0.65696, 0.49511, 0.77137, 0.46248, 0.86671, 0.45886, 0.84764, 0.22686, 0.86671, 0.63648, 0.65934, 0.29573, 0.63312, 0.75248, 0.30896, 0.24498, 0.26367, 0.63648, 0.48534, 0.28486, 0.47343, 0.65098, 0.77614, 0.63286], "triangles": [22, 23, 21, 21, 36, 20, 21, 23, 36, 23, 24, 36, 36, 24, 26, 26, 24, 35, 24, 25, 35, 14, 12, 13, 15, 32, 14, 14, 32, 12, 26, 35, 37, 20, 36, 19, 36, 26, 38, 15, 39, 32, 12, 32, 11, 16, 17, 34, 17, 18, 34, 18, 19, 34, 32, 30, 11, 19, 38, 34, 34, 38, 28, 34, 28, 39, 28, 38, 27, 38, 26, 27, 32, 39, 30, 28, 29, 39, 39, 29, 30, 28, 33, 29, 28, 27, 33, 27, 26, 37, 27, 37, 33, 29, 33, 31, 29, 31, 30, 30, 10, 11, 30, 31, 10, 16, 34, 15, 34, 39, 15, 19, 36, 38, 4, 5, 33, 35, 2, 37, 37, 3, 4, 37, 2, 3, 25, 1, 35, 35, 1, 2, 25, 0, 1, 37, 4, 33, 33, 5, 6, 31, 33, 6, 6, 7, 31, 7, 8, 31, 31, 8, 10, 10, 8, 9], "vertices": [2, 3, 18.91, 35.9, 0.528, 2, -35.79, 19.1, 0.472, 2, 3, 18.08, 25.65, 0.528, 2, -25.51, 19.6, 0.472, 2, 3, 16.13, 12.74, 0.528, 2, -12.46, 19.34, 0.472, 2, 3, 12.32, 4.5, 0.528, 2, -3.8, 16.62, 0.472, 2, 3, 7.35, -0.77, 0.528, 2, 2.07, 12.37, 0.472, 2, 3, 8.01, -5.96, 0.528, 2, 7.13, 13.7, 0.472, 2, 3, 4.42, -8.96, 0.528, 2, 10.57, 10.53, 0.472, 2, 3, 10.01, -12.92, 0.528, 2, 13.78, 16.58, 0.472, 2, 3, 11.21, -20.48, 0.528, 2, 21.11, 18.75, 0.472, 2, 3, 9.93, -29.43, 0.528, 2, 30.16, 18.64, 0.472, 2, 3, -0.21, -26.58, 0.528, 2, 28.64, 8.22, 0.472, 1, 2, 30.07, -2.59, 1, 2, 27, -1.93, 8.23, 0.16, 2, 30.83, -12.17, 0.84, 2, 27, 3.88, 8.13, 0.16, 2, 32.44, -21.06, 0.84, 2, 27, 2.69, 0, 0.16, 2, 24.11, -20.78, 0.84, 2, 27, 0.67, -5.75, 0.16, 2, 17.87, -18.79, 0.84, 2, 27, 3.92, -11.41, 0.16, 2, 13.27, -24.83, 0.84, 3, 30, 4.68, 24.92, 0.14112, 27, 5.61, -18.7, 0.16, 2, 6.61, -28.8, 0.69888, 2, 30, 2.75, 17.76, 0.168, 2, -1.51, -23.95, 0.832, 2, 30, -0.58, 12.5, 0.168, 2, -6.83, -17.62, 0.832, 2, 30, 3.35, 7.28, 0.168, 2, -14.54, -21.88, 0.832, 2, 30, 5.66, -4.03, 0.168, 2, -29.15, -22.05, 0.832, 2, 30, 3.31, -13.95, 0.168, 2, -40.5, -15.82, 0.832, 1, 2, -34.89, -7.36, 1, 1, 2, -32.03, -1.13, 1, 2, 3, 8.73, 33.49, 0.528, 2, -32.08, 9.31, 0.472, 1, 2, -19.87, -3.19, 1, 1, 2, -4.05, -4.26, 1, 1, 2, 7.6, -4.57, 1, 1, 2, 15.97, -3.11, 1, 1, 2, 22.93, -3.02, 1, 2, 3, 0.61, -19.67, 0.528, 2, 21.68, 8.13, 0.472, 2, 27, -3.3, 0.5, 0.16, 2, 22.83, -11.55, 0.84, 2, 3, -0.72, -5.59, 0.528, 2, 7.89, 4.99, 0.472, 3, 30, -2.93, 22.1, 0.14112, 27, -2.01, -17.33, 0.16, 2, 5.71, -16.9, 0.69888, 2, 3, 5.31, 19.39, 0.528, 2, -17.65, 7.74, 0.472, 2, 30, -2.73, 0.17, 0.168, 2, -21.19, -11.01, 0.832, 2, 3, 1.59, 6.91, 0.528, 2, -4.8, 5.67, 0.472, 2, 30, -4.46, 12.2, 0.168, 2, -5.89, -11.89, 0.832, 2, 27, -4.27, -5.94, 0.16, 2, 16.22, -11.29, 0.84], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 48, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 73, "height": 48}}, "yaodai2": {"yaodai2": {"type": "mesh", "uvs": [0.06792, 0.04761, 0.4036, 0, 0.67214, 0.01129, 0.76165, 0.05487, 0.76725, 0.17835, 0.75046, 0.33995, 0.74487, 0.52516, 0.83438, 0.72308, 0.90711, 0.86108, 1, 1, 0.68892, 0.99545, 0.46514, 0.97366, 0.191, 0.99364, 0, 0.99545, 0.06233, 0.87379, 0.0959, 0.71037, 0.12946, 0.52335, 0.08471, 0.35448, 0.10149, 0.17835, 0.4036, 0.1729, 0.38681, 0.33269, 0.42598, 0.5179, 0.41479, 0.69585, 0.42038, 0.84837], "triangles": [10, 8, 9, 10, 11, 8, 13, 14, 12, 11, 12, 23, 12, 14, 23, 11, 23, 8, 14, 15, 23, 23, 7, 8, 15, 22, 23, 23, 22, 7, 22, 6, 7, 15, 16, 22, 22, 21, 6, 22, 16, 21, 6, 21, 5, 16, 17, 21, 17, 20, 21, 21, 20, 5, 17, 18, 20, 5, 20, 4, 20, 19, 4, 20, 18, 19, 19, 3, 4, 18, 0, 19, 0, 1, 19, 3, 1, 2, 3, 19, 1], "vertices": [1, 38, 6.03, -13.95, 1, 1, 38, 0.76, -1.46, 1, 1, 38, 2.18, 8.46, 1, 1, 38, 7.19, 11.71, 1, 2, 38, 21.27, 11.74, 0.98468, 39, -11.31, 11.67, 0.01532, 2, 38, 39.68, 10.88, 0.1166, 39, 7.11, 10.92, 0.8834, 2, 39, 28.22, 10.57, 0.61839, 40, -2.74, 10.49, 0.38161, 2, 40, 19.73, 14.35, 0.73119, 41, -6.13, 14.19, 0.26881, 2, 40, 35.4, 17.43, 0.04503, 41, 9.44, 17.7, 0.95497, 1, 41, 25.08, 21.96, 1, 1, 41, 25.16, 10.43, 1, 1, 41, 23.11, 2.04, 1, 1, 41, 25.91, -7.97, 1, 1, 41, 26.49, -15.02, 1, 2, 40, 37.61, -13.78, 0.00892, 41, 12.52, -13.44, 0.99108, 2, 40, 18.95, -13, 0.78773, 41, -6.15, -13.17, 0.21227, 2, 39, 27.86, -12.19, 0.63055, 40, -2.39, -12.27, 0.36945, 2, 38, 41.02, -13.77, 0.08309, 39, 8.6, -13.72, 0.91691, 2, 38, 20.95, -12.89, 0.97958, 39, -11.47, -12.96, 0.02042, 1, 38, 20.47, -1.71, 1, 2, 38, 38.68, -2.56, 0.01009, 39, 6.19, -2.53, 0.98991, 2, 39, 27.32, -1.22, 0.97817, 40, -3.28, -1.32, 0.02183, 2, 40, 17.01, -1.24, 0.9993, 41, -8.42, -1.48, 0.0007, 2, 40, 34.39, -0.61, 6e-05, 41, 8.93, -0.36, 0.99994], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 2, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 37, "height": 114}}, "xuanz": {"xuanz": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-104.92, 12.46, -21.22, -104.34, 105.49, -13.54, 21.79, 103.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 236, "height": 256}}, "tou": {"tou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-2.31, -51.79, 1.61, 58.14, 97.55, 54.71, 93.62, -55.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 110, "height": 96}}, "s1": {"s1": {"type": "mesh", "uvs": [0.14844, 0.10045, 0.34559, 0.05216, 0.55235, 0.11591, 0.71103, 0.18738, 0.83606, 0.29168, 0.78797, 0.36702, 0.70623, 0.44428, 0.72546, 0.51768, 0.6341, 0.58336, 0.61006, 0.70891, 0.59082, 0.7939, 0.71584, 0.87117, 0.72065, 0.989, 0.43695, 1, 0.23018, 0.99479, 0.0715, 0.95616, 0.08593, 0.85958, 0.13401, 0.7997, 0.10035, 0.68573, 0.08593, 0.55825, 0, 0.48098, 0, 0.41338, 0.09074, 0.34577, 0.16286, 0.25305, 0.16286, 0.1642, 0.43695, 0.1951, 0.36002, 0.32645, 0.31674, 0.44235, 0.29751, 0.56404, 0.31193, 0.67222, 0.34559, 0.81902, 0.36002, 0.90594], "triangles": [25, 2, 3, 4, 25, 3, 2, 25, 1, 31, 30, 10, 31, 10, 11, 16, 17, 30, 16, 30, 31, 31, 11, 12, 31, 15, 16, 14, 15, 31, 13, 31, 12, 14, 31, 13, 20, 21, 27, 19, 20, 27, 28, 19, 27, 7, 8, 27, 7, 27, 6, 28, 27, 8, 29, 28, 8, 18, 19, 28, 18, 28, 29, 9, 29, 8, 9, 30, 29, 17, 18, 29, 30, 17, 29, 9, 10, 30, 24, 0, 1, 25, 24, 1, 23, 24, 25, 26, 23, 25, 22, 23, 26, 27, 22, 26, 21, 22, 27, 4, 26, 25, 5, 26, 4, 6, 26, 5, 27, 26, 6], "vertices": [2, 19, 3.32, -15.63, 0.536, 18, 2.09, 17.36, 0.464, 2, 19, -4.85, -8.48, 0.416, 18, 6.26, 7.34, 0.584, 1, 18, -2.59, -1.13, 1, 2, 19, 5.1, 12.64, 0.432, 18, -11.99, -7.23, 0.568, 1, 18, -24.94, -11.18, 1, 2, 19, 24.07, 22.39, 0.87456, 20, -5.93, 25.49, 0.12544, 2, 19, 33.85, 21.43, 0.54311, 20, 3.07, 21.54, 0.45689, 2, 19, 41.77, 24.86, 0.24539, 20, 11.67, 22.35, 0.75461, 2, 19, 50.39, 23.07, 0.07444, 20, 19.3, 17.96, 0.92556, 3, 19, 64.74, 26.39, 0.00011, 20, 33.98, 16.66, 0.93947, 21, -10.48, 16.88, 0.06043, 2, 20, 43.91, 15.64, 0.50725, 21, -0.57, 15.66, 0.49275, 2, 20, 53.02, 21.41, 0.10039, 21, 8.65, 21.24, 0.89961, 2, 20, 66.8, 21.48, 0.0007, 21, 22.44, 21.02, 0.9993, 1, 21, 23.29, 7.65, 1, 1, 21, 22.37, -2.04, 1, 1, 21, 17.61, -9.35, 1, 2, 20, 51.32, -8.18, 0.01168, 21, 6.34, -8.31, 0.98832, 2, 20, 44.34, -5.84, 0.56165, 21, -0.59, -5.83, 0.43835, 1, 20, 30.98, -7.26, 1, 1, 20, 16.06, -7.77, 1, 2, 19, 47.88, -8.95, 0.10427, 20, 6.97, -11.7, 0.89573, 2, 19, 40.34, -11.32, 0.49504, 20, -0.93, -11.61, 0.50496, 2, 19, 31.51, -9.62, 0.96346, 20, -8.79, -7.25, 0.03654, 1, 19, 20.15, -9.64, 1, 1, 19, 10.23, -12.75, 1, 1, 19, 9.82, 0.62, 1, 2, 19, 25.57, 1.78, 0.9972, 20, -10.91, 5.43, 0.0028, 2, 19, 39.11, 3.9, 0.21705, 20, 2.63, 3.24, 0.78295, 2, 19, 52.97, 7.3, 0.00273, 20, 16.86, 2.17, 0.99727, 1, 20, 29.52, 2.7, 1, 2, 20, 46.71, 4.08, 0.24302, 21, 1.99, 4.04, 0.75698, 2, 20, 56.89, 4.64, 0.00644, 21, 12.18, 4.39, 0.99356], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 4, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 47, "height": 117}}, "s2": {"s2": {"type": "mesh", "uvs": [0.12676, 0.00982, 0.30876, 0, 0.51502, 0.04425, 0.57164, 0.13771, 0.58378, 0.21149, 0.66062, 0.30167, 0.81027, 0.42957, 0.77791, 0.48695, 0.81431, 0.60173, 0.81836, 0.70011, 0.84667, 0.78537, 0.97204, 0.83948, 1, 0.93457, 0.91947, 1, 0.71724, 1, 0.47862, 0.9936, 0.37347, 0.93621, 0.44222, 0.83128, 0.34516, 0.71158, 0.29258, 0.61649, 0.24405, 0.54434, 0.0944, 0.48695, 0.08227, 0.38038, 0.00138, 0.26068, 0, 0.14427, 0.03778, 0.05081, 0.18743, 0.07376, 0.25618, 0.16558, 0.30472, 0.25412, 0.37752, 0.34267, 0.44223, 0.44432, 0.50694, 0.54106, 0.57569, 0.6296, 0.62423, 0.71814, 0.64849, 0.8116, 0.7132, 0.90998], "triangles": [12, 13, 35, 15, 35, 14, 13, 14, 35, 15, 16, 35, 35, 17, 34, 17, 35, 16, 35, 11, 12, 35, 10, 11, 35, 34, 10, 17, 33, 34, 34, 33, 10, 17, 18, 33, 33, 9, 10, 18, 32, 33, 33, 32, 9, 18, 19, 32, 32, 8, 9, 19, 31, 32, 32, 31, 8, 19, 20, 31, 31, 7, 8, 31, 20, 30, 20, 21, 30, 31, 30, 7, 7, 30, 6, 21, 22, 30, 22, 29, 30, 30, 5, 6, 30, 29, 5, 22, 28, 29, 22, 23, 28, 29, 28, 5, 28, 4, 5, 23, 27, 28, 23, 24, 27, 28, 27, 4, 27, 3, 4, 24, 26, 27, 27, 2, 3, 2, 27, 26, 2, 26, 1, 24, 25, 26, 25, 0, 26, 26, 0, 1], "vertices": [1, 23, 6.48, 4.14, 1, 2, 23, 6.31, -4.12, 0.96931, 24, -14.08, 4.35, 0.03069, 2, 23, 0.04, -12.54, 0.52032, 24, -6.89, 12.01, 0.47968, 2, 23, -10.6, -13.49, 0.0704, 24, 3.78, 11.74, 0.9296, 2, 23, -18.77, -12.78, 2e-05, 24, 11.83, 10.11, 0.99998, 2, 24, 22.4, 10.81, 0.99709, 25, -13.99, 10.1, 0.00291, 2, 24, 37.86, 13.57, 0.50398, 25, 1.31, 13.66, 0.49602, 2, 24, 43.63, 10.49, 0.17571, 25, 7.23, 10.88, 0.82429, 1, 25, 20.02, 9.77, 1, 2, 25, 30.73, 7.62, 0.93191, 26, -5.14, 7.48, 0.06809, 2, 25, 40.25, 6.85, 0.07905, 26, 4.4, 6.97, 0.92095, 1, 26, 11.35, 11.39, 1, 1, 26, 21.96, 10.66, 1, 1, 26, 28.42, 5.75, 1, 1, 26, 26.72, -3.19, 1, 1, 26, 24.02, -13.61, 1, 1, 26, 16.88, -17.07, 1, 2, 25, 41.35, -12.02, 0.14203, 26, 6.01, -11.86, 0.85797, 2, 25, 27.44, -13.46, 0.92659, 26, -7.85, -13.68, 0.07341, 2, 24, 51.75, -14.36, 0.00662, 25, 16.62, -13.52, 0.99338, 2, 24, 43.45, -14.36, 0.16175, 25, 8.33, -13.95, 0.83825, 2, 24, 35.53, -19.18, 0.52813, 25, 0.68, -19.17, 0.47187, 2, 24, 23.98, -16.6, 0.902, 25, -11, -17.18, 0.098, 3, 23, -20.19, 13.95, 0.00806, 24, 10.2, -16.61, 0.9919, 25, -24.76, -17.91, 4e-05, 2, 23, -7.4, 12.05, 0.30409, 24, -2.28, -13.27, 0.69591, 2, 23, 2.59, 8.79, 0.89342, 24, -11.84, -8.9, 0.10658, 2, 23, -0.95, 2.52, 0.86997, 24, -7.61, -3.07, 0.13003, 2, 23, -11.5, 1.02, 0.01385, 24, 3.03, -2.77, 0.98615, 1, 24, 13.09, -3.25, 1, 2, 24, 23.43, -2.68, 0.99783, 25, -12.26, -3.31, 0.00217, 2, 24, 35.09, -2.84, 0.59704, 25, -0.61, -2.87, 0.40296, 2, 24, 46.21, -2.85, 0.00039, 25, 10.5, -2.31, 0.99961, 1, 25, 20.76, -1.38, 1, 2, 25, 30.83, -1.34, 0.99381, 26, -4.8, -1.48, 0.00619, 2, 25, 41.2, -2.49, 0.01315, 26, 5.6, -2.34, 0.98685, 1, 26, 16.87, -1.51, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 0, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 45, "height": 111}}, "m2": {"m2": {"type": "mesh", "uvs": [1, 0.3808, 0.89361, 0.18423, 0.75924, 0.06137, 0.60605, 0.06137, 0.46361, 0, 0.41524, 0.13508, 0.35074, 0.34803, 0.27011, 0.50365, 0.17605, 0.60603, 0.09274, 0.74527, 0, 0.85584, 0.11155, 1, 0.31311, 1, 0.52543, 0.95413, 0.67055, 0.8927, 0.75924, 0.66746, 0.80761, 0.51184, 0.9178, 0.54461, 1, 0.48727, 0.91512, 0.41766, 0.77537, 0.30299, 0.61143, 0.35213, 0.49587, 0.51594, 0.38837, 0.68794, 0.26474, 0.79851, 0.13843, 0.87223], "triangles": [11, 25, 12, 25, 24, 12, 12, 24, 23, 11, 10, 25, 10, 9, 25, 25, 9, 24, 9, 8, 24, 24, 7, 23, 24, 8, 7, 12, 23, 13, 14, 13, 22, 13, 23, 22, 14, 22, 15, 23, 7, 22, 22, 7, 6, 22, 6, 21, 22, 21, 15, 16, 15, 21, 21, 6, 5, 3, 5, 4, 5, 3, 21, 20, 3, 2, 20, 21, 3, 16, 21, 20, 16, 19, 17, 17, 19, 18, 16, 20, 19, 19, 0, 18, 20, 1, 19, 19, 1, 0, 20, 2, 1], "vertices": [1, 8, -6.41, -2.73, 1, 1, 8, 3.53, -6.69, 1, 2, 8, 13.56, -7.06, 0.563, 9, -5.04, -5.45, 0.437, 2, 8, 22.15, -2.34, 0.0017, 9, 3.29, -10.63, 0.9983, 3, 9, 9.68, -17.62, 0.96335, 10, -9.11, -15.85, 0.03498, 11, -17.37, -21.98, 0.00166, 3, 9, 15.3, -14.44, 0.821, 10, -2.87, -14.17, 0.15359, 11, -11.95, -18.45, 0.02541, 3, 9, 23.53, -9.02, 0.18187, 10, 6.45, -10.98, 0.51875, 11, -4.08, -12.52, 0.29938, 3, 9, 31.36, -6.19, 0.00158, 10, 14.74, -10.21, 0.09598, 11, 3.55, -9.21, 0.90244, 1, 11, 10.89, -8.27, 1, 1, 11, 18.36, -5.64, 1, 1, 11, 25.78, -4.36, 1, 2, 10, 36.83, -3.16, 0.00023, 11, 22.36, 4.35, 0.99977, 3, 9, 40.02, 12.98, 0.00067, 10, 27.93, 6.17, 0.2648, 11, 11, 10.46, 0.73453, 4, 8, 8.6, 33, 0.00084, 9, 27.46, 18.51, 0.08898, 10, 17.16, 14.68, 0.87456, 11, -1.88, 15.19, 0.03562, 3, 8, 1.7, 26.27, 0.01955, 9, 18.21, 21.21, 0.23736, 10, 8.88, 19.62, 0.74308, 3, 8, 1.29, 15.24, 0.1617, 9, 8.39, 16.17, 0.4606, 10, -1.88, 17.19, 0.37771, 3, 8, 1.73, 8.02, 0.62181, 9, 2.32, 12.25, 0.30194, 10, -8.75, 14.92, 0.07625, 3, 8, -5.12, 5.83, 0.98101, 9, -2.95, 17.14, 0.01737, 10, -12.62, 20.98, 0.00162, 1, 8, -8.57, 1.19, 1, 3, 8, -2.4, 1.24, 0.99484, 9, -5.62, 12.52, 0.00477, 10, -16.36, 17.17, 0.00039, 3, 8, 7.76, 1.33, 0.87554, 9, -0.56, 3.71, 0.12278, 10, -13.67, 7.37, 0.00168, 1, 9, 9.44, -0.07, 1, 3, 8, 19.12, 17.79, 0.00023, 9, 19.36, 1.87, 0.0101, 10, 5.14, 0.6, 0.98967, 2, 9, 29.01, 4.38, 0.00073, 10, 15.12, 0.61, 0.99927, 2, 10, 23.94, -1.91, 0.02659, 11, 9.72, 1.54, 0.97341, 2, 10, 31.76, -5.62, 0.00031, 11, 18.31, 0.44, 0.99969], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 64, "height": 42}}, "m3": {"m3": {"type": "mesh", "uvs": [0, 0.25746, 0.10691, 0.13897, 0.23696, 0.02048, 0.41316, 0, 0.65228, 0.11221, 0.75296, 0.34919, 0.79911, 0.54413, 0.86623, 0.75817, 1, 0.93017, 1, 1, 0.74457, 0.96457, 0.52642, 0.86519, 0.3754, 0.80786, 0.31667, 0.61675, 0.28311, 0.46768, 0.21179, 0.37595, 0.06496, 0.46768, 0.0314, 0.38359, 0.14048, 0.27658, 0.32506, 0.19249, 0.47609, 0.29186, 0.53482, 0.5212, 0.61033, 0.71613, 0.74038, 0.83462, 0.8956, 0.91871], "triangles": [22, 21, 6, 22, 6, 7, 12, 13, 22, 23, 22, 7, 11, 12, 22, 11, 22, 23, 8, 24, 7, 23, 7, 24, 10, 23, 24, 11, 23, 10, 24, 8, 9, 10, 24, 9, 4, 20, 3, 20, 4, 5, 14, 15, 20, 21, 20, 5, 14, 20, 21, 21, 5, 6, 13, 14, 21, 13, 21, 22, 19, 2, 3, 20, 19, 3, 1, 2, 19, 18, 1, 19, 0, 1, 18, 15, 18, 19, 15, 19, 20, 17, 0, 18, 17, 18, 15, 16, 17, 15], "vertices": [1, 12, -4.92, 4.06, 1, 1, 12, 1.1, 7.44, 1, 2, 12, 8, 10.47, 0.98377, 13, -10.51, -2.19, 0.01623, 2, 12, 15.07, 8.71, 0.67674, 13, -8.64, 4.85, 0.32326, 2, 12, 22.37, 0.45, 0.02538, 13, -0.27, 12.03, 0.97462, 2, 13, 11.16, 11.83, 0.96665, 14, -3.57, 13.75, 0.03335, 2, 13, 20, 10.28, 0.47365, 14, 4.35, 9.54, 0.52635, 2, 13, 29.96, 9.2, 0.00904, 14, 13.49, 5.44, 0.99096, 1, 14, 22.95, 4.65, 1, 1, 14, 25.35, 2.62, 1, 1, 14, 17.38, -4.36, 1, 1, 14, 8.2, -8.32, 1, 2, 13, 24.45, -10.28, 0.03468, 14, 2.24, -11.39, 0.96532, 3, 12, 1.32, -15.72, 0.01378, 13, 15.58, -9.27, 0.54822, 14, -5.89, -7.69, 0.43799, 3, 12, 2.47, -8.97, 0.23718, 13, 8.85, -8.02, 0.71819, 14, -11.91, -4.42, 0.04463, 3, 12, 1.24, -4.06, 0.83298, 13, 3.92, -9.17, 0.16661, 14, -16.95, -4, 0.00041, 1, 12, -5.87, -5.73, 1, 1, 12, -5.78, -1.7, 1, 1, 12, 0.14, 1.17, 1, 1, 12, 8.56, 1.95, 1, 1, 13, 4.5, 2.29, 1, 1, 13, 14.96, 0.63, 1, 1, 14, 5.29, -1.36, 1, 1, 14, 12.8, -0.72, 1, 1, 14, 19.79, 1.71, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 41, "height": 45}}, "biyan": {"biyan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-11.05, -20.01, -9.19, 31.95, 11.79, 31.2, 9.94, -20.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 52, "height": 21}}, "m5": {"m5": {"type": "mesh", "uvs": [0.45201, 0.37376, 0.40516, 0.31744, 0.26101, 0.29295, 0.1601, 0.23908, 0.16731, 0.08726, 0.35471, 0, 0.64661, 0, 0.87365, 0.14113, 1, 0.35417, 0.98176, 0.50109, 0.8268, 0.50844, 0.80518, 0.56965, 0.74391, 0.59904, 0.74391, 0.73862, 0.67544, 0.89288, 0.50606, 0.95655, 0.26101, 1, 0, 0.96635, 0.03757, 0.90758, 0.17091, 0.84881, 0.22857, 0.72882, 0.31867, 0.60883, 0.41957, 0.54762, 0.39795, 0.46926, 0.41957, 0.41049, 0.56012, 0.60149, 0.47363, 0.71168, 0.40876, 0.83657, 0.29344, 0.90513, 0.14569, 0.94676, 0.59976, 0.49619, 0.65021, 0.43008, 0.68625, 0.26602, 0.53129, 0.14113, 0.34029, 0.15582], "triangles": [1, 34, 33, 3, 4, 34, 34, 4, 5, 1, 33, 32, 32, 7, 8, 33, 6, 7, 33, 5, 6, 31, 32, 8, 32, 33, 7, 34, 5, 33, 2, 34, 1, 2, 3, 34, 17, 29, 16, 16, 28, 15, 16, 29, 28, 17, 18, 29, 28, 27, 15, 15, 27, 14, 29, 19, 28, 29, 18, 19, 28, 19, 27, 19, 20, 27, 27, 26, 14, 14, 26, 13, 27, 20, 26, 26, 25, 13, 25, 12, 13, 20, 21, 26, 21, 22, 26, 26, 22, 25, 25, 30, 12, 25, 22, 30, 12, 30, 11, 11, 30, 10, 22, 23, 30, 30, 31, 10, 9, 10, 8, 8, 10, 31, 23, 24, 30, 24, 0, 30, 30, 0, 31, 31, 0, 32, 0, 1, 32], "vertices": [1, 15, 4.05, -9.23, 1, 2, 15, 1.71, -13.7, 0.616, 5, 24.11, 36.85, 0.384, 2, 15, 4.24, -21.15, 0.752, 5, 26.29, 44.41, 0.248, 2, 15, 3.6, -27.93, 0.368, 5, 30.68, 49.61, 0.632, 2, 15, -6.56, -34.02, 0.224, 5, 42.5, 48.8, 0.776, 1, 5, 48.95, 38.63, 1, 1, 5, 48.39, 23.17, 1, 1, 5, 36.96, 11.54, 1, 1, 5, 20.12, 5.44, 1, 2, 15, -2.8, 19.75, 0.99979, 16, -14.74, 24.43, 0.00021, 2, 15, 2.13, 13.16, 0.96079, 16, -11.7, 16.78, 0.03921, 2, 15, 6.76, 14.78, 0.85845, 16, -6.8, 17.14, 0.14155, 2, 15, 10.45, 13.29, 0.69053, 16, -3.63, 14.74, 0.30947, 3, 15, 19.6, 19.19, 0.14215, 16, 6.74, 18.06, 0.82766, 17, -16.73, 13.33, 0.03019, 3, 15, 31.68, 22.66, 0.00278, 16, 19.31, 18.26, 0.70961, 17, -4.92, 17.62, 0.28761, 2, 16, 26.77, 11.22, 0.29415, 17, 4.43, 13.4, 0.70585, 1, 17, 14.92, 5.02, 1, 1, 17, 21.09, -7.64, 1, 1, 17, 16.23, -8.78, 1, 2, 16, 24.17, -8.26, 0.01323, 17, 8.33, -5.86, 0.98677, 2, 16, 14.32, -8.19, 0.73352, 17, -1, -9.01, 0.26648, 2, 15, 23.3, -5.24, 0.00071, 16, 3.95, -6.49, 0.99929, 2, 15, 16.38, -3.33, 0.79068, 16, -2.22, -2.85, 0.20932, 1, 15, 11.87, -7.6, 1, 1, 15, 7.39, -9.12, 1, 2, 15, 15.88, 5.21, 0.53911, 16, -0.48, 5.52, 0.46089, 3, 15, 25.59, 6.01, 0.00689, 16, 9.1, 3.77, 0.9929, 17, -9.84, 0.59, 0.0002, 2, 16, 19.42, 3.46, 0.40752, 17, 0.02, 3.67, 0.59248, 1, 17, 7.96, 1.97, 1, 1, 17, 15.25, -2.36, 1, 2, 15, 7.84, 2.52, 0.98229, 16, -8.95, 5.03, 0.01771, 2, 15, 2.06, 1.98, 0.99842, 16, -14.67, 6, 0.00158, 1, 5, 27.58, 21.81, 1, 1, 5, 37.61, 29.67, 1, 1, 5, 36.83, 39.83, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 50, 52, 52, 54, 54, 56, 56, 58, 50, 60, 60, 62, 62, 64, 64, 66, 66, 68], "width": 53, "height": 78}}, "wuqi": {"wuqi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100.9, -93.82, -36.62, -2.65, 29.69, 97.37, 167.21, 6.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 120}}, "pd1": {"pd1": {"type": "mesh", "uvs": [0.48307, 0.17309, 0.55805, 0.08903, 0.69396, 0, 0.88143, 0, 1, 0.04453, 1, 0.16073, 0.95641, 0.28435, 0.91423, 0.43764, 0.86268, 0.60824, 0.78301, 0.78131, 0.70334, 0.91482, 0.52525, 1, 0.31435, 1, 0.20187, 0.91977, 0.1597, 0.83571, 0, 0.79368, 0, 0.70467, 0.10346, 0.61319, 0.23468, 0.48215, 0.33778, 0.37583, 0.41746, 0.27446, 0.77415, 0.138, 0.66977, 0.32606, 0.58338, 0.4578, 0.5028, 0.56798, 0.42096, 0.66149, 0.30433, 0.78436], "triangles": [6, 21, 5, 1, 2, 21, 21, 3, 4, 21, 4, 5, 21, 2, 3, 21, 0, 1, 24, 18, 23, 24, 23, 8, 25, 17, 18, 24, 25, 18, 8, 25, 24, 9, 25, 8, 26, 17, 25, 26, 25, 9, 16, 17, 26, 14, 15, 16, 26, 14, 16, 10, 26, 9, 13, 14, 26, 11, 12, 13, 13, 26, 11, 10, 11, 26, 23, 22, 7, 23, 19, 22, 18, 19, 23, 8, 23, 7, 22, 21, 6, 22, 0, 21, 20, 0, 22, 7, 22, 6, 22, 19, 20], "vertices": [2, 30, -3.05, -11.47, 0.96659, 31, -11.99, -10.33, 0.03341, 2, 30, -11.92, -8.26, 0.048, 2, -28.41, 4.76, 0.952, 1, 2, -16.3, 17.6, 1, 1, 2, -0.38, 18.03, 1, 1, 2, 9.4, 12.03, 1, 1, 2, 8.65, -4.32, 1, 2, 30, 0.51, 21.77, 0.96259, 31, -11.43, 16.28, 0.03741, 3, 30, 15.33, 22.1, 0.17058, 31, -1.57, 17.51, 0.81453, 32, -14.28, 9.88, 0.01489, 2, 31, 9.46, 18.64, 0.25853, 32, -7.35, 13.02, 0.74147, 1, 32, 0.16, 15.21, 1, 1, 32, 6.26, 16.23, 1, 1, 32, 12.35, 12.23, 1, 1, 32, 16, 4.52, 1, 1, 32, 15.1, -1.96, 1, 1, 32, 12.85, -5.98, 1, 1, 32, 14.12, -13.05, 1, 1, 32, 10.97, -15.67, 1, 2, 31, 19.69, -19.99, 0.00446, 32, 5.94, -14.59, 0.99554, 2, 31, 10.02, -16.19, 0.34079, 32, -0.97, -13.65, 0.65921, 3, 30, 17.82, -16.88, 0.03892, 31, 2.21, -13.27, 0.85623, 32, -6.51, -13.01, 0.10485, 3, 30, 7.28, -13.71, 0.56048, 31, -4.98, -11.44, 0.43897, 32, -11.48, -13.08, 0.00055, 2, 30, -10.46, 6.88, 0.08, 2, -10.38, -1.64, 0.92, 1, 30, 8.47, 3.85, 1, 2, 31, 3.98, 1.07, 0.98804, 32, -7.85, -1.62, 0.01196, 2, 31, 11.72, -0.61, 0.00328, 32, -2.56, -1.32, 0.99672, 1, 32, 2.17, -1.56, 1, 1, 32, 8.53, -2.2, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 6, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 67, "height": 127}}, "qunbian": {"qunbian": {"type": "mesh", "uvs": [0, 0.87881, 0.07749, 0.67152, 0.12909, 0.50359, 0.1781, 0.30942, 0.22196, 0.14411, 0.25808, 0.06277, 0.38449, 0, 0.54444, 0, 0.67343, 0.04965, 0.75598, 0.13624, 0.78178, 0.31992, 0.82048, 0.49047, 0.87208, 0.64791, 0.94173, 0.8001, 1, 0.9208, 0.96495, 1, 0.82048, 1, 0.63989, 0.98639, 0.45156, 0.99951, 0.28645, 1, 0.11877, 0.96278, 0.5109, 0.14149, 0.49284, 0.30942, 0.4851, 0.50097, 0.50058, 0.75024, 0.5238, 0.91293, 0.65537, 0.18085, 0.65795, 0.33041, 0.68117, 0.52196, 0.70955, 0.68464, 0.76114, 0.90768, 0.37159, 0.14674, 0.32773, 0.34878, 0.30451, 0.51146, 0.26582, 0.70039, 0.2297, 0.86569], "triangles": [15, 16, 14, 17, 30, 16, 14, 16, 13, 18, 25, 17, 17, 25, 30, 16, 30, 13, 30, 25, 29, 25, 24, 29, 13, 29, 12, 13, 30, 29, 24, 28, 29, 24, 23, 28, 29, 11, 12, 29, 28, 11, 23, 27, 28, 28, 10, 11, 28, 27, 10, 23, 22, 27, 22, 26, 27, 27, 26, 10, 26, 9, 10, 22, 21, 26, 26, 8, 9, 26, 21, 8, 21, 7, 8, 20, 35, 19, 19, 35, 18, 25, 18, 35, 25, 35, 24, 24, 35, 34, 20, 0, 35, 0, 1, 35, 35, 1, 34, 1, 2, 34, 24, 34, 23, 34, 33, 23, 34, 2, 33, 33, 32, 23, 32, 33, 3, 33, 2, 3, 23, 32, 22, 3, 4, 32, 32, 31, 22, 32, 4, 31, 22, 31, 21, 4, 5, 31, 31, 6, 21, 31, 5, 6, 21, 6, 7], "vertices": [1, 32, 11.4, -3.9, 1, 2, 31, 19.44, -7.82, 0.08335, 32, 3.67, -5.28, 0.91665, 2, 31, 10.21, -7.57, 0.9842, 32, -2.33, -6.98, 0.0158, 2, 30, 14.64, -10.03, 0.55931, 31, -0.27, -8.04, 0.44069, 1, 30, 1.26, -9.25, 1, 2, 30, -5.57, -7.52, 0.99343, 27, -14.04, -49.73, 0.00657, 2, 30, -12.51, 2.94, 0.83553, 27, -16.71, -33.51, 0.16447, 2, 30, -15.22, 17.74, 0.45942, 27, -14.24, -14.87, 0.54058, 2, 30, -13.61, 30.67, 0.13259, 27, -8.58, -1.01, 0.86741, 2, 30, -8.39, 40.03, 0.00118, 27, -0.92, 6.57, 0.99882, 1, 27, 13.01, 5.24, 1, 1, 28, 7.39, 3.21, 1, 1, 29, 1.91, 1.82, 1, 1, 29, 9.38, 1.05, 1, 2, 32, -10.47, 49.5, 0.00021, 29, 15.42, 0.73, 0.99979, 3, 31, 21.31, 63.2, 0.00043, 32, -7.39, 49.74, 0.0045, 29, 16.37, -6.78, 0.99507, 4, 31, 23.68, 52.75, 0.01512, 32, -4.06, 42.18, 0.05351, 28, 32.93, -17.91, 0.01277, 29, 10.43, -21.19, 0.91861, 4, 31, 25.95, 39.4, 0.06806, 32, -0.28, 32.38, 0.2356, 28, 26.1, -37.01, 0.09039, 29, 2.59, -38.51, 0.60594, 4, 31, 29.68, 26.04, 0.07816, 32, 4.44, 22.88, 0.54445, 28, 20.36, -58.06, 0.09404, 29, -4.75, -57.96, 0.28335, 4, 31, 32.41, 14.1, 0.02305, 32, 8.26, 14.25, 0.82476, 28, 14.77, -76.05, 0.04289, 29, -11.53, -74.45, 0.1093, 3, 32, 11.07, 4.51, 0.98155, 28, 7.2, -92.77, 0.00474, 29, -19.55, -89.29, 0.01372, 4, 30, -3.84, 17.45, 0.51029, 31, -14.09, 12.56, 0.00504, 27, -4.33, -22.12, 0.48229, 28, -20.62, -16.03, 0.00237, 5, 30, 9.31, 19.1, 0.40536, 31, -5.42, 14.73, 0.15747, 27, 7.77, -28.19, 0.35146, 28, -12.82, -24.95, 0.08569, 29, -23.92, -18.83, 2e-05, 6, 30, 24.09, 22.19, 0.06331, 31, 4.26, 18.14, 0.48897, 32, -10.62, 11.57, 0.02647, 27, 21.77, -33.62, 0.09666, 28, -3.49, -33.74, 0.2958, 29, -18.44, -29.32, 0.02879, 5, 31, 16.45, 24.42, 0.24808, 32, -3.83, 18.91, 0.28897, 27, 40.39, -37.7, 0.00046, 28, 9.53, -42.39, 0.22552, 29, -10.27, -40.42, 0.23697, 4, 31, 24.18, 29.47, 0.10391, 32, 0.29, 24.38, 0.39464, 28, 18.47, -46.6, 0.12249, 29, -4.4, -46.36, 0.37896, 4, 30, -3.28, 31.6, 0.07534, 31, -14.49, 23.83, 0.00382, 27, 0.81, -6.21, 0.92038, 28, -13.74, -1.93, 0.00047, 4, 30, 8.12, 34.81, 0.05876, 31, -7.07, 27.12, 0.04437, 27, 11.87, -9.44, 0.78881, 28, -6.16, -7.85, 0.10806, 6, 30, 22.37, 40.76, 0.00962, 31, 2.1, 32.76, 0.10379, 32, -14.54, 22.37, 0.01479, 27, 26.35, -11.26, 0.02933, 28, 4.23, -13.26, 0.80934, 29, -9.75, -10.83, 0.03313, 4, 31, 9.76, 38.19, 0.08298, 32, -10.53, 28.12, 0.05828, 28, 13.35, -16.91, 0.43501, 29, -3.66, -16.25, 0.42372, 4, 31, 20.04, 46.54, 0.03291, 32, -5.33, 36.66, 0.09264, 28, 26.28, -20.54, 0.04604, 29, 5.2, -22.42, 0.8284, 3, 30, -1.07, 4.65, 0.91807, 27, -6.09, -38.48, 0.08128, 28, -25.1, -31.41, 0.00065, 5, 30, 15.12, 4.6, 0.54737, 31, -0.76, 3.6, 0.40763, 27, 8.12, -48.36, 0.02766, 28, -16.46, -44.56, 0.01731, 29, -29.52, -37.29, 3e-05, 5, 31, 7.74, 5.29, 0.94235, 32, -6.15, 2.4, 0.00095, 27, 19.76, -54.91, 0.00925, 28, -9.1, -53.83, 0.04151, 29, -25.55, -47.86, 0.00593, 5, 31, 17.8, 6.4, 0.28103, 32, 0.15, 5.33, 0.65455, 27, 33.08, -63.89, 0, 28, -0.95, -65.88, 0.03573, 29, -21.43, -61.31, 0.02868, 4, 31, 26.64, 7.21, 0.01481, 32, 5.73, 7.77, 0.91328, 28, 6.11, -76.67, 0.02358, 29, -17.92, -73.3, 0.04833], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 14, 42, 42, 44, 44, 46, 46, 48, 48, 50, 16, 52, 52, 54, 54, 56, 56, 58, 58, 60, 12, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 119, "height": 117}}, "paidai3": {"paidai3": {"type": "mesh", "uvs": [0.02551, 0.0618, 0.07398, 0, 0.25172, 0, 0.33251, 0.15443, 0.46716, 0.3238, 0.61797, 0.44819, 0.81186, 0.58846, 1, 0.69697, 0.96805, 0.83723, 0.90342, 0.91663, 0.84956, 1, 0.65567, 0.98544, 0.45639, 0.90075, 0.28942, 0.70226, 0.23556, 0.55141, 0.12784, 0.32116, 0.06321, 0.16766, 0.14241, 0.08486, 0.21515, 0.18596, 0.26398, 0.32418, 0.35415, 0.44194, 0.45798, 0.5454, 0.5618, 0.64886, 0.73422, 0.80001], "triangles": [16, 0, 17, 17, 2, 3, 0, 1, 17, 17, 1, 2, 10, 11, 9, 9, 11, 23, 11, 12, 23, 9, 23, 8, 23, 13, 22, 23, 12, 13, 8, 23, 7, 23, 6, 7, 23, 22, 6, 13, 21, 22, 13, 14, 21, 22, 21, 6, 21, 5, 6, 14, 20, 21, 20, 14, 15, 21, 20, 5, 20, 4, 5, 20, 15, 19, 20, 19, 4, 3, 4, 19, 19, 15, 18, 19, 18, 3, 15, 16, 18, 18, 16, 17, 3, 18, 17], "vertices": [1, 2, 10.64, 2.02, 1, 1, 2, 13.01, 9.33, 1, 1, 2, 23.36, 8.09, 1, 2, 27, -2.5, 6.74, 0.912, 2, 29.19, -11.58, 0.088, 2, 27, 12.44, 11.49, 0.81104, 28, -1.69, 12.27, 0.18896, 2, 28, 8.13, 15.54, 0.89763, 29, -2.58, 16.58, 0.10237, 2, 28, 19.66, 20.44, 6e-05, 29, 6.55, 19.05, 0.99994, 1, 29, 14.44, 22.72, 1, 1, 29, 18.66, 14.76, 1, 1, 29, 20.03, 8.11, 1, 1, 29, 21.78, 1.77, 1, 1, 29, 17.05, -6.5, 1, 1, 29, 9.75, -11.79, 1, 1, 29, -0.83, -10.32, 1, 2, 28, 6.46, -7.7, 0.6323, 29, -7.29, -5.83, 0.3677, 1, 27, 8.32, -7.41, 1, 1, 27, -4.55, -8.51, 1, 1, 2, 17.62, -1.65, 1, 1, 27, -1.36, -0.33, 1, 2, 27, 10.13, 0.14, 0.9999, 28, -5.6, 1.87, 0.0001, 1, 28, 2.68, 2.27, 1, 1, 29, -2.66, 4.71, 1, 1, 29, 3.22, 4.72, 1, 1, 29, 12.27, 5.69, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 2, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 57, "height": 116}}}}], "events": {"atk": {}, "hurt": {}}, "animations": {"boss_attack1": {"bones": {"bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -20.44, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -36.08, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -9.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -80.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -113.18, "curve": "stepped"}, {"time": 0.3667, "angle": -113.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.27}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -92.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8.81, "curve": "stepped"}, {"time": 0.3667, "angle": 8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.44}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -30.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 74.78, "curve": "stepped"}, {"time": 0.3667, "angle": 74.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.62}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": "stepped"}, {"time": 0.1, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -30.18, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -3.87}]}, "bone45": {"rotate": [{"angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -7.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": 3.04}]}, "bone44": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -38.41, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.5}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -34.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.6}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.51, "curve": "stepped"}, {"time": 0.3667, "angle": -46.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 68.61, "curve": "stepped"}, {"time": 0.3667, "angle": 68.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.77}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": "stepped"}, {"time": 0.1, "angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 58.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 44.1, "curve": "stepped"}, {"time": 0.4667, "angle": 44.1, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -4.1}]}, "bone49": {"rotate": [{"angle": 7.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2667, "angle": -30.76, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 7.66}]}, "bone5": {"rotate": [{"angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24}]}, "bone19": {"rotate": [{"angle": -2.76}]}, "bone4": {"rotate": [{"angle": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.71, "curve": "stepped"}, {"time": 0.3667, "angle": 17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 117.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 31.39, "curve": "stepped"}, {"time": 0.3667, "angle": 31.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.22}]}, "bone50": {"rotate": [{"angle": 10.7, "curve": "stepped"}, {"time": 0.1, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -27.71, "curve": 0.349, "c2": 0.38, "c3": 0.693, "c4": 0.75}, {"time": 0.5, "angle": 10.7}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -18.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -70.9, "curve": "stepped"}, {"time": 0.3667, "angle": -70.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.48}]}, "bone51": {"rotate": [{"angle": 7.67, "curve": "stepped"}, {"time": 0.2, "angle": 7.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": -30.75, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.5, "angle": 7.67}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 76.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 38.17, "curve": "stepped"}, {"time": 0.3667, "angle": 38.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.08}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 30.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.55, "curve": "stepped"}, {"time": 0.3667, "angle": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.52}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 54.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -19.21, "curve": "stepped"}, {"time": 0.3667, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -0.69}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -24.9, "y": -51.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 36.43, "y": -2.04, "curve": "stepped"}, {"time": 0.3667, "x": 36.43, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone46": {"rotate": [{"angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -30.75, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.1333, "angle": -23.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 7.67}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 28.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -57.6, "curve": "stepped"}, {"time": 0.3667, "angle": -57.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.235, "y": 1.832, "curve": "stepped"}, {"time": 0.3667, "x": 1.235, "y": 1.832, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone55": {"translate": [{"time": 0.1667, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.3, "x": 78.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5}]}, "bone59": {"translate": [{"time": 0.1667, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.3, "y": 65.75, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.5}]}}, "events": [{"time": 0.2667, "name": "atk"}]}, "boss_attack3": {"slots": {"wuqi": {"attachment": [{"time": 0.2667, "name": null}]}, "xuanz": {"attachment": [{"time": 0.2667, "name": "xuanz"}, {"time": 0.5333, "name": null}]}, "mutou2": {"attachment": [{"time": 0.2667, "name": "mutou"}, {"time": 0.5333, "name": null}]}}, "bones": {"feijian": {"rotate": [{"time": 0.2667}, {"time": 0.3333, "angle": -120}, {"time": 0.4, "angle": 120}, {"time": 0.4333}, {"time": 0.5333, "angle": -120}, {"time": 0.5667, "angle": 120}, {"time": 0.6667}], "translate": [{"x": -6.78, "y": -184.11, "curve": "stepped"}, {"time": 0.2667, "x": -6.78, "y": -184.11, "curve": 0.321, "c2": 0.51, "c3": 0.75}, {"time": 0.6667, "x": 777.72, "y": -196.09}]}, "bone2": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 5.42, "curve": "stepped"}, {"time": 0.2, "angle": 5.42, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": -28, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": -11.2, "y": -18.17, "curve": "stepped"}, {"time": 0.2, "x": -11.2, "y": -18.17, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "x": 8.84, "y": -18.08, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone3": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 9.89, "curve": "stepped"}, {"time": 0.2, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone4": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone5": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 9.26, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.4, "angle": 3.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -18.73, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -25.46, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone17": {"rotate": [{"angle": -11.2, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -25.46, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.7, "angle": -11.2}]}, "bone20": {"rotate": [{"angle": -8.19, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -25.46, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.7, "angle": -8.19}]}, "bone33": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 15.99, "curve": "stepped"}, {"time": 0.2, "angle": 15.99, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3667, "angle": -39.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone34": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 62.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4333, "angle": -10.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone36": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 37.6, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.1667, "angle": -177.28, "curve": "stepped"}, {"time": 0.2, "angle": -177.28, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 71.15, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.7}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.74, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3, "angle": 18.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -26.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone45": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -26.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone46": {"rotate": [{"angle": -25.88, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -26.82, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 0.7, "angle": -25.88}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -26.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone50": {"rotate": [{"angle": -11.8, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -26.82, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.7, "angle": -11.8}]}, "bone51": {"rotate": [{"angle": -25.88, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -26.82, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 0.7, "angle": -25.88}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone62": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -37.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "b1one8": {"rotate": [{"angle": -2.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -18.73, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.7, "angle": -2.44}]}, "b1one9": {"rotate": [{"angle": -9.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -18.73, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7, "angle": -9.37}]}, "b1one10": {"rotate": [{"angle": -16.29, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -18.73, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.7, "angle": -16.29}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.88, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone23": {"rotate": [{"angle": -2.08, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -18.88, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.7, "angle": -2.08}]}, "bone24": {"rotate": [{"angle": -10.58, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -18.88, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.7, "angle": -10.58}]}}, "deform": {"default": {"mutou2": {"mutou": [{"time": 0.2667, "vertices": [-35.04689, -20.0926, 17.61178, 36.35671, 35.04676, 20.09225, -17.61174, -36.35686]}]}}}, "events": [{"time": 0.4667, "name": "atk"}]}, "boss_idle": {"slots": {"biyan": {"attachment": [{"time": 1.0667, "name": "biyan"}, {"time": 1.1667, "name": null}]}}, "bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.8, "y": -0.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.51, "y": -0.08}]}, "bone5": {"rotate": [{"angle": 1.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.52, "y": -0.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.09, "y": -0.24}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.64, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.64, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.87}]}, "bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.64, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -9.77}]}, "bone19": {"rotate": [{"angle": -2.76, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -13.64, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -2.76}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -13.64, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -8.6}]}, "bone22": {"rotate": [{"angle": -4.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -16.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -4.56}]}, "bone23": {"rotate": [{"angle": -11.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -16.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -11.51}]}, "bone24": {"rotate": [{"angle": -16.08, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -16.08}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.63, "y": 2.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.82, "y": 1.09}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.22, "y": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.11, "y": -1.69}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.48}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.22}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.62}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.72, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.1}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone45": {"rotate": [{"angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.04}]}, "bone46": {"rotate": [{"angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.67}]}, "bone49": {"rotate": [{"angle": -17.23, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -27.61, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -17.23}]}, "bone50": {"rotate": [{"angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10.7}]}, "bone51": {"rotate": [{"angle": 7.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 7.67}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.2, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -23.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 52.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 103.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 84.48}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 11.86, "y": -31.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.74, "y": 42.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.74, "y": 57.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.74, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 3.7, "y": -124.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 3.7, "y": -99.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 3.7, "y": -124.49}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone33": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 45.03, "curve": "stepped"}, {"time": 0.3333, "angle": 45.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -24.05}]}, "bone34": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 30.81, "curve": "stepped"}, {"time": 0.3667, "angle": 30.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.76}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 73.21, "curve": "stepped"}, {"time": 0.3, "angle": 73.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 22.07}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -15.22, "curve": "stepped"}, {"time": 0.3, "angle": -15.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.67}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 20.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone45": {"rotate": [{"angle": 5.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 20.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6333, "angle": 5.77}]}, "bone46": {"rotate": [{"angle": 17.21, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 20.34, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.6333, "angle": 17.21}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 20.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone50": {"rotate": [{"angle": 5.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 20.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6333, "angle": 5.77}]}, "bone51": {"rotate": [{"angle": 17.21, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 20.34, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.6333, "angle": 17.21}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.73}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 16.4}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.98}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.81}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 49.39}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -45.22, "curve": "stepped"}, {"time": 0.3, "angle": -45.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -72.27}]}, "bone55": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 177.46}]}, "bone60": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 139.15}]}, "bone59": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 82.57}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 7.16, "y": 100.94, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 7.16, "y": 132.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 7.16, "y": 55.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 7.16, "y": 3.52}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 82.57}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2413, "x": 7.16, "y": 100.94, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "x": 7.16, "y": 124.74, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "x": 7.16, "y": 51.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 7.16, "y": 5.73}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.1667, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -1.12, "y": -8.93, "curve": "stepped"}, {"time": 0.2, "x": -1.12, "y": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone4": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone33": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone34": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 41.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone36": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 62, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone37": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -20.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -52.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 29.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 35.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -40.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.44}]}, "bone62": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -39.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}}}, "run1": {"bones": {"bone60": {"translate": [{"x": 33.29, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -41.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -90.06, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -50.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 33.29}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -67.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -50.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"x": 0.48, "y": 17.65, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.79, "y": 55.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.48, "y": 17.65}]}, "bone55": {"translate": [{"x": -58.4, "y": -0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -34.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 85.88, "y": 0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -5.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -58.4, "y": -0.4}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 60.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -58.5}], "translate": [{"x": -1.86, "y": 44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 16.3, "y": 58.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.7, "y": 37.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.86, "y": 44.51}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone2": {"rotate": [{"angle": -22.85}]}, "bone": {"rotate": [{"angle": -4.76}], "translate": [{"y": 7.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 7.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 7.43}]}, "bone44": {"rotate": [{"angle": 35.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 20.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 35.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 20.46, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 35.4}], "translate": [{"x": -2.28, "y": 20.48}]}, "bone45": {"rotate": [{"angle": -19.39, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -9.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -24.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -24.88, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -19.39}]}, "bone16": {"rotate": [{"angle": -16.17, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -21.34, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -5.17, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -16.17}]}, "bone17": {"rotate": [{"angle": -5.17, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -21.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -16.17, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -5.17}]}, "bone20": {"rotate": [{"angle": -16.17, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -21.34, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -5.17, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -16.17}]}, "bone19": {"rotate": [{"angle": -21.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -21.34}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone3": {"rotate": [{"angle": 5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.55}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.82}]}, "bone36": {"rotate": [{"angle": -15.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 17.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -15.28}]}, "bone37": {"rotate": [{"angle": 33.81, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 29.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 61.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 33.81}]}, "bone38": {"rotate": [{"angle": 16.21}]}, "bone35": {"rotate": [{"angle": 35.12}]}, "bone33": {"rotate": [{"angle": -5.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -69.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.06}]}, "bone34": {"rotate": [{"angle": 74.36, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 79.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 65.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 74.36}]}, "ljio3": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 13.97, "y": -7.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "bone15": {"rotate": [{"angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.85}]}, "bone49": {"rotate": [{"angle": 4.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 11.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 4.22}]}, "bone50": {"rotate": [{"angle": 7.71, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.0667, "angle": 3.49, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": -3.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 7.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 7.71}]}, "bone51": {"rotate": [{"angle": -5.51, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.0667, "angle": 0.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.78, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4333, "angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -9.78, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -5.51}]}, "bone46": {"rotate": [{"angle": -27.24, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -29.18, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.25, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3667, "angle": -29.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -14.25, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -27.24}]}, "bone7": {"rotate": [{"angle": 29.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 17.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 29.97}]}, "b1one8": {"rotate": [{"angle": -3, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -12.37, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -3}]}, "b1one9": {"rotate": [{"angle": -7.82, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.37, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -7.82}]}, "b1one10": {"rotate": [{"angle": -11.85, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -12.37, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -11.85}]}}, "deform": {"default": {"yd": {"yd": [{"vertices": [-1.61502, -1.31358, 1.6426, -1.27968, -0.62833, -1.39151, 1.49704, -0.30067, 0.61382, -1.57557, 1.39751, 0.95103, 1.40791, -1.94057, 1.57495, 1.80679, 1.91568, -2.41754, 1.92576, 2.40877, 2.41512, -2.35237, 1.75013, 2.88084, 2.70488, -2.69733, 2.02128, 3.24059, 3.0847, -2.15894, 1.4113, 3.48985, 3.81125, -2.04109, 1.13338, 4.17136, 4.67332, -2.16206, 1.05772, 5.03867, 4.40112, -3.13821, 2.07008, 4.99252, 3.23083, 5.20891, -0.4289, 6.63595, 4.09801, 5.26157, -0.174, 7.38673, 4.90292, 5.5313, 0.21172, 6.88301, 4.98908, 4.75293, 0.43398, 6.37418, 4.88784, 4.14615, 0.87756, 6.55325, 5.51033, 3.79976, -3.00627, 4.0512, 1.34216, 6.46022, 5.96868, 3.23398, -2.29687, 3.8084, 5.43299, 2.26915, -1.85645, 3.45187, 4.84861, 1.72826, -1.45284, 3.92658, 5.29388, 1.00626, -0.5493, 4.24353, 5.37794, -0.39963, 0.26307, 4.01993, 4.83116, -1.51981, 3.99139, -1.0197, 3.37872, -0.77333, -1.38039, -2.29322, 2.5445, -0.8311, 3.52086, 0.40579, 3.54983, 1.93315, 3.52627, 3.05603, 3.34662, 3.85458, 3.30579, 4.52399, 3.7356, -3.06177, 2.14496, 4.32686, -0.07209, 6.1265, 4.14763, 4.51001, 2.38165, -3.193, 2.57679, 3.03693, -2.68338, 3.24654, 0.94121, 5.5297, 4.8754, 2.99089, -0.0228, -2.61892, 2.55717, 0.56493, -0.85012, 3.26277, 4.27884, 0.31564, 1.17834, -2.97344, 2.63296, 1.81512, -1.81445, 3.0093, 4.29288, 1.79211, 0.23248, 5.7244, 4.21265, 3.89232]}]}}}}, "run2": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 75.4}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone46": {"rotate": [{"angle": -27.04, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -12.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -40.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -12.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -40.99, "curve": 0.345, "c2": 0.39, "c3": 0.679, "c4": 0.72}, {"time": 0.3667, "angle": -27.04}]}, "bone45": {"rotate": [{"angle": -11.14, "curve": 0.347, "c2": 0.66, "c3": 0.68}, {"time": 0.0333, "angle": -7.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -35.47, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.2, "angle": -7.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3, "angle": -35.47, "curve": 0.354, "c2": 0.48, "c3": 0.688, "c4": 0.82}, {"time": 0.3667, "angle": -11.14}]}, "bone44": {"rotate": [{"angle": 67.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": 39.28, "curve": 0.324, "c2": 0.3, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "angle": 67.5, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2667, "angle": 39.28, "curve": 0.324, "c2": 0.3, "c3": 0.664, "c4": 0.66}, {"time": 0.3667, "angle": 67.5}], "translate": [{"x": 1.8, "y": 34.1}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "bone7": {"rotate": [{"angle": 28.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 28.76}], "translate": [{"x": 9.54, "y": 5.79}], "scale": [{"y": 1.54}]}, "b1one8": {"rotate": [{"angle": 8.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2333, "angle": -3.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": 8.59}]}, "b1one9": {"rotate": [{"angle": 5.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2667, "angle": -3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 5.21}]}, "b1one10": {"rotate": [{"angle": 1.44, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.3, "angle": -3.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": 1.44}]}, "bone49": {"rotate": [{"angle": -0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 23.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 23.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.43}], "translate": [{"x": 9.31, "y": 10.06}]}, "bone50": {"rotate": [{"angle": 2.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -3.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": 20.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 20.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 2.94}]}, "bone51": {"rotate": [{"angle": 7.43, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -9.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 14.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 14.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 7.43}]}}, "deform": {"default": {"yd": {"yd": [{"vertices": [0.73848, 1.0347, -1.12174, 0.59805, 3.0484, 0.04119, -0.4351, 3.01708, 5.97656, -1.45652, 0.67159, 6.11427, 7.90678, -3.12434, 2.07584, 8.24389, 9.19885, -4.89062, 3.66013, 9.7534, 10.34328, -5.09297, 3.71299, 10.91453, 11.09749, -6.3097, 4.82207, 11.81969, 11.85266, -5.05971, 3.4849, 12.40687, 13.51222, -5.28823, 3.49689, 14.082, 15.54303, -6.30814, 4.24591, 16.22755, 15.14124, -8.89938, 6.86724, 16.16412, 9.82471, 16.81102, 3.28456, 19.14185, 10.29461, 16.92645, 4.54272, 20.62708, 12.58995, 17.80027, 4.6422, 18.81121, 12.22652, 16.0145, 4.45887, 17.22324, 11.50553, 14.57131, 5.40771, 17.01453, 12.86184, 13.95515, -10.37732, 8.28231, 6.09277, 16.10046, 13.62601, 12.77797, -8.42311, 8.25345, 12.93364, 9.93945, -7.1711, 6.89082, 10.84518, 8.50042, -6.13651, 7.58901, 11.35173, 6.79605, -3.71516, 7.34023, 10.23306, 3.339, -1.46681, 5.8812, 7.69166, 0.47587, 5.91205, 1.5629, 4.50056, 2.06306, 1.51469, -1.96195, 1.74941, 1.75517, 6.01045, 5.00023, 7.54912, 8.77723, 8.55981, 11.54607, 8.84057, 13.48671, 9.3711, 15.13268, 13.57765, -8.16055, 6.33669, 14.51812, 3.32867, 17.35433, 9.85662, 15.19207, 10.46402, -7.48119, 6.06551, 11.34293, -9.33587, 6.09702, 4.38923, 14.38454, 10.60147, 11.87801, 4.74416, -3.95648, 3.30988, 5.21529, -4.39833, 5.29686, 7.96218, 4.91136, 7.61763, -5.91188, 4.87726, 8.3175, -6.99443, 5.76837, 9.41203, 8.56011, 3.40209, 15.90989, 9.5593, 13.77396]}]}}}}, "show_time": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 75.4}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone46": {"rotate": [{"angle": -27.04, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -12.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -40.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -12.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -40.99, "curve": 0.345, "c2": 0.39, "c3": 0.679, "c4": 0.72}, {"time": 0.3667, "angle": -27.04}]}, "bone45": {"rotate": [{"angle": -11.14, "curve": 0.347, "c2": 0.66, "c3": 0.68}, {"time": 0.0333, "angle": -7.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -35.47, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.2, "angle": -7.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3, "angle": -35.47, "curve": 0.354, "c2": 0.48, "c3": 0.688, "c4": 0.82}, {"time": 0.3667, "angle": -11.14}]}, "bone44": {"rotate": [{"angle": 67.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": 39.28, "curve": 0.324, "c2": 0.3, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "angle": 67.5, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2667, "angle": 39.28, "curve": 0.324, "c2": 0.3, "c3": 0.664, "c4": 0.66}, {"time": 0.3667, "angle": 67.5}], "translate": [{"x": 1.8, "y": 34.1}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "bone7": {"rotate": [{"angle": 28.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 28.76}], "translate": [{"x": 9.54, "y": 5.79}], "scale": [{"y": 1.54}]}, "b1one8": {"rotate": [{"angle": 8.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2333, "angle": -3.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": 8.59}]}, "b1one9": {"rotate": [{"angle": 5.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2667, "angle": -3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 5.21}]}, "b1one10": {"rotate": [{"angle": 1.44, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.3, "angle": -3.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": 1.44}]}, "bone49": {"rotate": [{"angle": -0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 23.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 23.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.43}], "translate": [{"x": 9.31, "y": 10.06}]}, "bone50": {"rotate": [{"angle": 2.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -3.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": 20.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 20.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 2.94}]}, "bone51": {"rotate": [{"angle": 7.43, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -9.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 14.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 14.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 7.43}]}}, "deform": {"default": {"yd": {"yd": [{"vertices": [0.73848, 1.0347, -1.12174, 0.59805, 3.0484, 0.04119, -0.4351, 3.01708, 5.97656, -1.45652, 0.67159, 6.11427, 7.90678, -3.12434, 2.07584, 8.24389, 9.19885, -4.89062, 3.66013, 9.7534, 10.34328, -5.09297, 3.71299, 10.91453, 11.09749, -6.3097, 4.82207, 11.81969, 11.85266, -5.05971, 3.4849, 12.40687, 13.51222, -5.28823, 3.49689, 14.082, 15.54303, -6.30814, 4.24591, 16.22755, 15.14124, -8.89938, 6.86724, 16.16412, 9.82471, 16.81102, 3.28456, 19.14185, 10.29461, 16.92645, 4.54272, 20.62708, 12.58995, 17.80027, 4.6422, 18.81121, 12.22652, 16.0145, 4.45887, 17.22324, 11.50553, 14.57131, 5.40771, 17.01453, 12.86184, 13.95515, -10.37732, 8.28231, 6.09277, 16.10046, 13.62601, 12.77797, -8.42311, 8.25345, 12.93364, 9.93945, -7.1711, 6.89082, 10.84518, 8.50042, -6.13651, 7.58901, 11.35173, 6.79605, -3.71516, 7.34023, 10.23306, 3.339, -1.46681, 5.8812, 7.69166, 0.47587, 5.91205, 1.5629, 4.50056, 2.06306, 1.51469, -1.96195, 1.74941, 1.75517, 6.01045, 5.00023, 7.54912, 8.77723, 8.55981, 11.54607, 8.84057, 13.48671, 9.3711, 15.13268, 13.57765, -8.16055, 6.33669, 14.51812, 3.32867, 17.35433, 9.85662, 15.19207, 10.46402, -7.48119, 6.06551, 11.34293, -9.33587, 6.09702, 4.38923, 14.38454, 10.60147, 11.87801, 4.74416, -3.95648, 3.30988, 5.21529, -4.39833, 5.29686, 7.96218, 4.91136, 7.61763, -5.91188, 4.87726, 8.3175, -6.99443, 5.76837, 9.41203, 8.56011, 3.40209, 15.90989, 9.5593, 13.77396]}]}}}}}}