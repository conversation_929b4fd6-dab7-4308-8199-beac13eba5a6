[{"__type__": "cc.Prefab", "_name": "UILevelBoss", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UILevelBoss", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 52}, {"__id__": 76}, {"__id__": 138}, {"__id__": 198}], "_active": true, "_components": [{"__id__": 614}, {"__id__": 616}], "_prefab": {"__id__": 618}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_hero", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 19}, {"__id__": 27}], "_active": false, "_components": [{"__id__": 45}, {"__id__": 47}, {"__id__": 49}], "_prefab": {"__id__": 51}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "heroHead", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 6}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53EDtvJl5GNIUu3Whn8CFW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 8}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "11c22232-446a-4c8f-8823-b6829b640c5b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cJbnoN8BHfrXrEczXXXSN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 10}, "_alignFlags": 0, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dpLWua8dEQaMqD3QpKXSk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "87ivMvY2FMBYfecqSWEW14", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 13}, "_contentSize": {"__type__": "cc.Size", "width": 95, "height": 95}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7ELPrw1hMAIw+1HnPEltj"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 15}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80RL3HKvBPqaCYqRMTaR3l"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 17}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5a8P4OsfZPNL/PQVqS0zQr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26BZGHnX5CoaEPrP7ZD0EQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "startBg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 95, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7C1IhAC9PVJ3n8lmprHSK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 23}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d3506cf1-af1d-4e83-9bac-56187961fa47@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94pJk0DIZPsI6PYomlgLAh"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 25}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5f1PmuV1ZCT5R0KLLHkMtI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58f6Dlj1pD87bcCfCMbC64", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "star", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 28}, {"__id__": 34}], "_active": true, "_components": [{"__id__": 40}, {"__id__": 42}], "_prefab": {"__id__": 44}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -38.796, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "starNor", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": 50, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 30}, "_contentSize": {"__type__": "cc.Size", "width": 17, "height": 17}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2fo2Bk2pGc7fK6tKevhkg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 32}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "21a8d75f-bb0d-4069-9672-e172e5d54d0e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfrgrMOF1Hko9DDP3vRJi9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceetBb+11O+LSTFVBdW4EO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "starAdv", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 37}], "_prefab": {"__id__": 39}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 36}, "_contentSize": {"__type__": "cc.Size", "width": 17, "height": 17}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fVMl2/HZBL60MeVuthgKH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 38}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7f4fe7bd-fdfc-4ec6-8c85-6ad041496186@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93OGtgPx1OzK0P2runMZ5C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5xl3A4yVDx446hdbaXj3c", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 41}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4R+H5IZRH7rDBzom1hUgV"}, {"__type__": "6ab2fOTDmBEC5MimPTmIqZN", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 43}, "_orientation": 0, "starNor": {"__id__": 28}, "starAdv": {"__id__": 34}, "starGap": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37Q59YBYhFAbP4YPn7OAJe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7ima5ssJNz6JKoZItu6kl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 46}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53uIjuaLpIuLrLPhjwOmww"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 48}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ebf21b3e-8aa9-4356-8500-2e0668cbad02@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65lE2oPZtNrZHFjIOvuq2K"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 50}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "608HPGDA9KOpEtUKcc2WyG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4cr6UAj5BM+6wFxvBBK+Ca", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 53}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 52}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 54}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "98eozHVDZF1KCxWOaD52DB", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 55}, {"__id__": 57}, {"__id__": 58}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 64}, {"__id__": 66}, {"__id__": 68}, {"__id__": 70}, {"__id__": 72}, {"__id__": 74}, {"__id__": 75}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_name"], "value": "btn_item"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.9, "y": 0.9, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 102, "height": 41.8}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "ebf21b3e-8aa9-4356-8500-2e0668cbad02@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["d1PcUCEbVCQ7t7NK/Q0EQM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 69}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 53, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.Node", "_name": "player<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 77}, {"__id__": 129}], "_active": false, "_components": [{"__id__": 135}], "_prefab": {"__id__": 137}, "_lpos": {"__type__": "cc.Vec3", "x": 526.95, "y": -319.965, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bszs_bg_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 76}, "_children": [{"__id__": 78}, {"__id__": 84}, {"__id__": 90}, {"__id__": 112}, {"__id__": 118}], "_active": true, "_components": [{"__id__": 124}, {"__id__": 126}], "_prefab": {"__id__": 128}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 77}, "_children": [], "_active": true, "_components": [{"__id__": 79}, {"__id__": 81}], "_prefab": {"__id__": 83}, "_lpos": {"__type__": "cc.Vec3", "x": 177.455, "y": 149.719, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 80}, "_contentSize": {"__type__": "cc.Size", "width": 106.75, "height": 44.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18mERikShE0IKZs/1DcRUD"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 82}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": " 主 角 ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78vQDQTpFLN72P83vCTIp8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "de9oj4gFBJMau32o69pYNd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 77}, "_children": [], "_active": true, "_components": [{"__id__": 85}, {"__id__": 87}], "_prefab": {"__id__": 89}, "_lpos": {"__type__": "cc.Vec3", "x": 184.45, "y": 99.003, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 86}, "_contentSize": {"__type__": "cc.Size", "width": 324, "height": 34.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fCazUDBdOEZzLY7sIRTEr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 88}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "选择皮肤，更换出战形象！", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23uRf9j8VOBKjUJJNJebxH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32iZEaWVZGRo7SmCO4vAbP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_goPlayer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 77}, "_children": [{"__id__": 91}, {"__id__": 99}], "_active": true, "_components": [{"__id__": 105}, {"__id__": 107}, {"__id__": 109}], "_prefab": {"__id__": 111}, "_lpos": {"__type__": "cc.Vec3", "x": 176.706, "y": 38.348, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 90}, "_children": [], "_active": true, "_components": [{"__id__": 92}, {"__id__": 94}, {"__id__": 96}], "_prefab": {"__id__": 98}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 93}, "_contentSize": {"__type__": "cc.Size", "width": 75.19999694824219, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98lzLDSRVDDowH9mwGaNyk"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 95}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "前 往", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": true, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fr5HvM95PZ4I/aH1MS2/X"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 97}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7CoEaiN9NRY4YR5IA8XGL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a66vuAHfNJ+YuHuM30mbKy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "dj_btn_qianwang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 90}, "_children": [], "_active": true, "_components": [{"__id__": 100}, {"__id__": 102}], "_prefab": {"__id__": 104}, "_lpos": {"__type__": "cc.Vec3", "x": 133.136, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": {"__id__": 101}, "_contentSize": {"__type__": "cc.Size", "width": 37, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaDVBbCp1NXqzXJSW08OII"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": {"__id__": 103}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b85a172b-f11b-4733-9c88-83577d1d5ffb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52y2fZj4ZFYqStjqatjI0L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fomryTntH3L/gPTD+T9Fz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 106}, "_contentSize": {"__type__": "cc.Size", "width": 326, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34XT+ZI7BA8JcfFHAVAM1r"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 108}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7afc81c9-cffa-4f5f-810f-9b0af2cf4c55@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03P1BAf81BOa6iMFDFuRKM"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 110}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14DYZLBiZC06O7CqvlQ+Nu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11Xjn+UONC4Y/wDze5NxED", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 77}, "_children": [], "_active": true, "_components": [{"__id__": 113}, {"__id__": 115}], "_prefab": {"__id__": 117}, "_lpos": {"__type__": "cc.Vec3", "x": 69.701, "y": 147.343, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": {"__id__": 114}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1g8dREsZATb3ojuc9c3S8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": {"__id__": 116}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "897fa295-fcb3-4615-9572-5d2cd1056943@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3f6XYFh1tAv703S4HuuKua"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78plQpREdDUbd0j/SPu+27", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_bai-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 77}, "_children": [], "_active": true, "_components": [{"__id__": 119}, {"__id__": 121}], "_prefab": {"__id__": 123}, "_lpos": {"__type__": "cc.Vec3", "x": 289.701, "y": 147.343, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": {"__id__": 120}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acejHQNVtGe6BSzs3VBuw3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": {"__id__": 122}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "897fa295-fcb3-4615-9572-5d2cd1056943@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42+SsvuLRCWq0s/5HQN8LS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3Yv7tg89JpaXL2CAJvj2t", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 125}, "_contentSize": {"__type__": "cc.Size", "width": 355, "height": 186}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34DgA5b/9PwZOb/eTZIARm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 127}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e2efc2c1-2d71-491c-8c2e-b7e8309ea73c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b13zf8tBVLgZpMN4mEPMSf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddXPryNnZLFIAFU9B7W40F", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "sanji<PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 76}, "_children": [], "_active": true, "_components": [{"__id__": 130}, {"__id__": 132}], "_prefab": {"__id__": 134}, "_lpos": {"__type__": "cc.Vec3", "x": 56.43, "y": 1.364, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 0}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": -180, "y": -180, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 131}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f73qNS2wxMsIS9rKAvlgMu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 133}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "89a68030-2a86-4151-ac7e-ef40a01151cc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaUcV5rS9C8oVBoNVCwD+7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5+jl0WnZOV5mHK5bLQFPA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 76}, "_enabled": true, "__prefab": {"__id__": 136}, "_contentSize": {"__type__": "cc.Size", "width": 355, "height": 186}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8320vrh95IjKkURfohSBPf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26poLw3gtDrqlwW3quYMOg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heroGoMessage", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 139}, {"__id__": 189}], "_active": false, "_components": [{"__id__": 195}], "_prefab": {"__id__": 197}, "_lpos": {"__type__": "cc.Vec3", "x": 526.95, "y": -533.006, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bszs_bg_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [{"__id__": 140}, {"__id__": 146}, {"__id__": 152}, {"__id__": 172}, {"__id__": 178}], "_active": true, "_components": [{"__id__": 184}, {"__id__": 186}], "_prefab": {"__id__": 188}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 139}, "_children": [], "_active": true, "_components": [{"__id__": 141}, {"__id__": 143}], "_prefab": {"__id__": 145}, "_lpos": {"__type__": "cc.Vec3", "x": 177.45499999999993, "y": 149.719, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 142}, "_contentSize": {"__type__": "cc.Size", "width": 106.75, "height": 44.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aDVpQpplEZ7ZgKzXWXiBr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 144}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": " 战 将 ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0FhwSuZtEjYtTJsnX/nA2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89036NE45ALpQKkIB/HMzf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 139}, "_children": [], "_active": true, "_components": [{"__id__": 147}, {"__id__": 149}], "_prefab": {"__id__": 151}, "_lpos": {"__type__": "cc.Vec3", "x": 184.45000000000005, "y": 99.00299999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 148}, "_contentSize": {"__type__": "cc.Size", "width": 297, "height": 34.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6gWrfAqtGQ59PZkw+kvKM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 150}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "培养战将，可提升战力！", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cICNGfPBOwYoz8N9h4eZW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6S6Zwds1Clb+SpvbyFPxo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_goHero", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 139}, "_children": [{"__id__": 153}, {"__id__": 159}], "_active": true, "_components": [{"__id__": 165}, {"__id__": 167}, {"__id__": 169}], "_prefab": {"__id__": 171}, "_lpos": {"__type__": "cc.Vec3", "x": 176.7059999999999, "y": 38.34800000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [{"__id__": 154}, {"__id__": 156}], "_prefab": {"__id__": 158}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 155}, "_contentSize": {"__type__": "cc.Size", "width": 75.19999694824219, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68fMowpGlFNLcqXHl8urHk"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 157}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "前 往", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": true, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52Cy2N5KRLsJDczBOXDzWY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "be46OrsrVH7qw8ot4JrxhX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "dj_btn_qianwang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [{"__id__": 160}, {"__id__": 162}], "_prefab": {"__id__": 164}, "_lpos": {"__type__": "cc.Vec3", "x": 133.136, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 161}, "_contentSize": {"__type__": "cc.Size", "width": 37, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ce4a+0V1VCg5Ua51nDA++j"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 163}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b85a172b-f11b-4733-9c88-83577d1d5ffb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "be5spe4yZM0LHiLN9vpf0F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1d9RUlbUxPGpmekk+NDlym", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 152}, "_enabled": true, "__prefab": {"__id__": 166}, "_contentSize": {"__type__": "cc.Size", "width": 326, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71pA5bsTdFabKSfmah/c7l"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 152}, "_enabled": true, "__prefab": {"__id__": 168}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7afc81c9-cffa-4f5f-810f-9b0af2cf4c55@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0THvp00BCXo5g2s/D0st8"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 152}, "_enabled": true, "__prefab": {"__id__": 170}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feBh8zMclC8YR8CzN/RSNQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1ErOnYvJOarGXRlLnqkNH", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 139}, "_children": [], "_active": true, "_components": [{"__id__": 173}, {"__id__": 175}], "_prefab": {"__id__": 177}, "_lpos": {"__type__": "cc.Vec3", "x": 69.70100000000002, "y": 147.34299999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 174}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dAFB0ka5Kwqk4+pfD421O"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 176}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "897fa295-fcb3-4615-9572-5d2cd1056943@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0c7T9WTrRJvIUuV+W9AFr8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7eqaXAvLFDO7bGz94TuFpX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_bai-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 139}, "_children": [], "_active": true, "_components": [{"__id__": 179}, {"__id__": 181}], "_prefab": {"__id__": 183}, "_lpos": {"__type__": "cc.Vec3", "x": 289.701, "y": 147.34299999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 180}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afjdqdCVZEiqtAmZaI1m9w"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 182}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "897fa295-fcb3-4615-9572-5d2cd1056943@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eSofyF5VL7KNOZhNK0FUl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56NsyVEaNHp4i54SjfUZ0E", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 185}, "_contentSize": {"__type__": "cc.Size", "width": 355, "height": 186}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dJelaS6xNSYVSB5dCtkrq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 187}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e2efc2c1-2d71-491c-8c2e-b7e8309ea73c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5a7CrU6ahI8IH4diHTNN6d"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ef1AehbkBCAoh6ri5nlPx7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "sanji<PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 190}, {"__id__": 192}], "_prefab": {"__id__": 194}, "_lpos": {"__type__": "cc.Vec3", "x": 56.43, "y": 1.364, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 0}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": -180, "y": -180, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 191}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bc72JAYoBNdppaaCMdxrjw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 193}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "89a68030-2a86-4151-ac7e-ef40a01151cc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef6TMWUSJN+ppV8NRJHtOl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2e0DkOEbNCw7nzY3/MQgEb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": {"__id__": 196}, "_contentSize": {"__type__": "cc.Size", "width": 355, "height": 186}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7mME2P8hE5apLuqz+QaXW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eJ0Z2yD5DT6n+RSA6dbgq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tcNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 199}, {"__id__": 261}, {"__id__": 283}, {"__id__": 301}, {"__id__": 307}, {"__id__": 383}, {"__id__": 397}, {"__id__": 455}, {"__id__": 461}, {"__id__": 499}, {"__id__": 525}, {"__id__": 535}, {"__id__": 599}], "_active": true, "_components": [{"__id__": 609}, {"__id__": 611}], "_prefab": {"__id__": 613}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -11.642000000000053, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bossBgMask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 200}, {"__id__": 206}, {"__id__": 210}, {"__id__": 222}, {"__id__": 234}], "_active": true, "_components": [{"__id__": 254}, {"__id__": 256}, {"__id__": 258}], "_prefab": {"__id__": 260}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -76.423, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 199}, "_children": [], "_active": true, "_components": [{"__id__": 201}, {"__id__": 203}], "_prefab": {"__id__": 205}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 202}, "_contentSize": {"__type__": "cc.Size", "width": 682, "height": 665}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3LSWQ6dJPvKCw5IkV0n6Z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 204}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0a996f9e-a1cf-4b08-9ab2-2c235c5ee1c5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0efeVzTmJL9ZUBJLqChN/z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7csqMcUFxLq4BRZqjMhQpN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bossPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 199}, "_children": [], "_active": true, "_components": [{"__id__": 207}], "_prefab": {"__id__": 209}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 160.221, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 208}, "_contentSize": {"__type__": "cc.Size", "width": 261, "height": 276.8900146484375}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c07yvEKDBNNa0H9jfY3Ib1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "68B5iROadDWrMfGIqXtz+y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_guanqiaming", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 199}, "_children": [{"__id__": 211}], "_active": true, "_components": [{"__id__": 217}, {"__id__": 219}], "_prefab": {"__id__": 221}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 599.864, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "levelName", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 210}, "_children": [], "_active": true, "_components": [{"__id__": 212}, {"__id__": 214}], "_prefab": {"__id__": 216}, "_lpos": {"__type__": "cc.Vec3", "x": -6, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 211}, "_enabled": true, "__prefab": {"__id__": 213}, "_contentSize": {"__type__": "cc.Size", "width": 167.3499755859375, "height": 45.58}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94YsJwP+9PUode7wMoYeSC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 211}, "_enabled": true, "__prefab": {"__id__": 215}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "小试牛刀-1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 33, "_fontSize": 33, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 33, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19HEXFn1xFSoVmOtU3+wIv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "95zbYrXWBDg44J4dSWzzEX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 218}, "_contentSize": {"__type__": "cc.Size", "width": 328, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2NIl/UZRN2obQOhTaErQo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 220}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8ae11497-8b28-432e-8222-eb5408e711e6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90R7P5tmxPEoehA0FkMptF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "68p7jZ+iFIuIQ/7SRL/8Vi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spr_boss<PERSON>ame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 199}, "_children": [{"__id__": 223}], "_active": true, "_components": [{"__id__": 229}, {"__id__": 231}], "_prefab": {"__id__": 233}, "_lpos": {"__type__": "cc.Vec3", "x": 153.641, "y": 387.025, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lab_boss<PERSON>ame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [], "_active": true, "_components": [{"__id__": 224}, {"__id__": 226}], "_prefab": {"__id__": 228}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 225}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 140.57999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75sR3MJaZL+LzC0wHzRC4O"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 227}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 31, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 33, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97eXWuwxxEjo7bI9lz7jZm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80ozLnhXVFGaPNBXFcW0pX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 230}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 208}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2gMQSI/5FprUuPPCTm5A0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 232}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5acdc2ec-7dc0-44d3-b0d3-46474fcbad4b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2ADjeFGdJALZDJABUl9Eg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7djj+RtGVDKaB1XQXuJtbD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spr_boss<PERSON><PERSON>er", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 199}, "_children": [{"__id__": 235}, {"__id__": 241}], "_active": true, "_components": [{"__id__": 249}, {"__id__": 251}], "_prefab": {"__id__": 253}, "_lpos": {"__type__": "cc.Vec3", "x": 2.213, "y": 105.252, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.1, "y": 1.1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lab_boss<PERSON><PERSON>er", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": true, "_components": [{"__id__": 236}, {"__id__": 238}], "_prefab": {"__id__": 240}, "_lpos": {"__type__": "cc.Vec3", "x": -42.952, "y": -8.175, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": {"__id__": 237}, "_contentSize": {"__type__": "cc.Size", "width": 96.75, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11Nva4+y1FR4/Zuv2CDGRk"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": {"__id__": 239}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1235", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "970543e4-4be7-42fb-9538-1678b5544d24", "__expectedType__": "cc.BitmapFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98VMfU32RJZY/WBchJKo6t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53TVbFKlJG3bn7ebe3FY3V", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_look_boss_attr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": true, "_components": [{"__id__": 242}, {"__id__": 244}, {"__id__": 246}], "_prefab": {"__id__": 248}, "_lpos": {"__type__": "cc.Vec3", "x": 154.097, "y": -12.025, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": true, "__prefab": {"__id__": 243}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9quaC1upFPYjF4qrD3HEh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": true, "__prefab": {"__id__": 245}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "15970a7b-7b45-475c-9675-180c125cec2f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15DunmilRJxq4lPvRl8awU"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": true, "__prefab": {"__id__": 247}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dHsYaaX9P2ogJ21meb1GT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6H2ZoRcVA0q6gvNKe9yT+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 234}, "_enabled": true, "__prefab": {"__id__": 250}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feYgNRRAtPW5WVvATm726c"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 234}, "_enabled": true, "__prefab": {"__id__": 252}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "159ff75d-a27b-4752-94c8-b5d87bcd1a35@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5oIeMQM1Fu4De78Zpr9WG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e7CEIJ3nRM5669HtsULfYj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 199}, "_enabled": true, "__prefab": {"__id__": 255}, "_contentSize": {"__type__": "cc.Size", "width": 682, "height": 675}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11A9j2yCZO2Y0gBU1EkIdC"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 199}, "_enabled": true, "__prefab": {"__id__": 257}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26F+hRAJFCfa6ATp5lvrBy"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 199}, "_enabled": true, "__prefab": {"__id__": 259}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87yMwVYvFC4am6IT/YUS2W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67b04lmkRABb37isMDTZPR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "hint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 262}, {"__id__": 268}, {"__id__": 274}], "_active": true, "_components": [{"__id__": 280}], "_prefab": {"__id__": 282}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -310.011, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "line_fengexian_lan1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 261}, "_children": [], "_active": true, "_components": [{"__id__": 263}, {"__id__": 265}], "_prefab": {"__id__": 267}, "_lpos": {"__type__": "cc.Vec3", "x": -135.12, "y": -1.3139999999999645, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 264}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bubkofcZP1LgFd1FSHr2Q"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 266}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3aac0b27-a1e8-4574-8b35-ab81390e6b16@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebGGYCjWVNH7OaVqQS465V"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0dtf6H47lN86A0k2FzreKp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_lan2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 261}, "_children": [], "_active": true, "_components": [{"__id__": 269}, {"__id__": 271}], "_prefab": {"__id__": 273}, "_lpos": {"__type__": "cc.Vec3", "x": 143.462, "y": -1.3139999999999645, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 270}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44qugNx3ZFS53ArxWfn3AM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 272}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3aac0b27-a1e8-4574-8b35-ab81390e6b16@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96t30DLVFPb70YnlNx1ufi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4t/9yoL9AW4MWgOZ3oen0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 261}, "_children": [], "_active": true, "_components": [{"__id__": 275}, {"__id__": 277}], "_prefab": {"__id__": 279}, "_lpos": {"__type__": "cc.Vec3", "x": 5, "y": -0.6430000000000291, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 274}, "_enabled": true, "__prefab": {"__id__": 276}, "_contentSize": {"__type__": "cc.Size", "width": 84.59999084472656, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9FLMMLu5BDK7aYlvt6KQI"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 274}, "_enabled": true, "__prefab": {"__id__": 278}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "奖 励", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "603n7bbpVAEbyKcAViVUKo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "022ZkkjB9PjLGe7lIpy0R6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 261}, "_enabled": true, "__prefab": {"__id__": 281}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dd7HHfIlEuqz6GsSQwmAf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "348GXANRNMfYTlVPSoJmjQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_z<PERSON><PERSON>_left", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 284}, {"__id__": 290}], "_active": true, "_components": [{"__id__": 296}, {"__id__": 298}], "_prefab": {"__id__": 300}, "_lpos": {"__type__": "cc.Vec3", "x": -169.448, "y": -94.788, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lab_player<PERSON>ower", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 283}, "_children": [], "_active": true, "_components": [{"__id__": 285}, {"__id__": 287}], "_prefab": {"__id__": 289}, "_lpos": {"__type__": "cc.Vec3", "x": -43.979, "y": -6.974, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": {"__id__": 286}, "_contentSize": {"__type__": "cc.Size", "width": 76.31, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d45TaQ22dJWo0y0gdeWk14"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": {"__id__": 288}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "666", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 33, "_fontSize": 33, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 33, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "e01d7349-fb8f-45ba-825c-b03a50fe22f1", "__expectedType__": "cc.BitmapFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f83uWtLpRNSqZyY1LkI3VB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ad4MbJ8zNNj76uzxZwDyBb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_wo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 283}, "_children": [], "_active": true, "_components": [{"__id__": 291}, {"__id__": 293}], "_prefab": {"__id__": 295}, "_lpos": {"__type__": "cc.Vec3", "x": -155.085, "y": 3.678, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 290}, "_enabled": true, "__prefab": {"__id__": 292}, "_contentSize": {"__type__": "cc.Size", "width": 29, "height": 29}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bagpHpndlIlq8poMwLYHua"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 290}, "_enabled": true, "__prefab": {"__id__": 294}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2626c6e4-ef57-4012-b6ef-43ed08263e24@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "745kZs78tM94Q1ClQvXYg7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fbS6+SFD5J369tPnW6pN0B", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": {"__id__": 297}, "_contentSize": {"__type__": "cc.Size", "width": 302, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bMD1avUFNGZ873h0GtQ21"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": {"__id__": 299}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "da85f0d4-ec05-436a-8076-6e68610dd0c3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06JHkyGoRJ0bGjBEQO3Qdt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "604EVjDs1EyKStiIhV0roP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "awardLay", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [], "_active": true, "_components": [{"__id__": 302}, {"__id__": 304}], "_prefab": {"__id__": 306}, "_lpos": {"__type__": "cc.Vec3", "x": 6, "y": -389.133, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 301}, "_enabled": true, "__prefab": {"__id__": 303}, "_contentSize": {"__type__": "cc.Size", "width": -9, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9ixehXBBAg4k9ibKPrtVe"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 301}, "_enabled": true, "__prefab": {"__id__": 305}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 9, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0dU8bmgVFxqkdvURr9SnM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bxPaAT9BHObM9LgmoxaSV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_qiyun", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 308}, {"__id__": 314}, {"__id__": 320}, {"__id__": 338}, {"__id__": 356}], "_active": true, "_components": [{"__id__": 376}, {"__id__": 378}, {"__id__": 380}], "_prefab": {"__id__": 382}, "_lpos": {"__type__": "cc.Vec3", "x": -233.313, "y": -520.674, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 307}, "_children": [], "_active": true, "_components": [{"__id__": 309}, {"__id__": 311}], "_prefab": {"__id__": 313}, "_lpos": {"__type__": "cc.Vec3", "x": -15.712, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": {"__id__": 310}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21HtzJuWdKELP3WtBbjoZT"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": {"__id__": 312}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "气运鼓舞", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abF+u2k1NBd6BUQzoN9vBk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dez+cSu2BEhLAcaNRicjl2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_jiant<PERSON>_guwu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 307}, "_children": [], "_active": true, "_components": [{"__id__": 315}, {"__id__": 317}], "_prefab": {"__id__": 319}, "_lpos": {"__type__": "cc.Vec3", "x": 59.65, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 314}, "_enabled": true, "__prefab": {"__id__": 316}, "_contentSize": {"__type__": "cc.Size", "width": 31, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c41gi+Es1LnLE4QVs/ScMb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 314}, "_enabled": true, "__prefab": {"__id__": 318}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "12c9ed37-3e27-4fe5-b26e-f22e0fbff13c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8sUA8BIhHa7P4V1hCu9f3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76g5nJUA5N7o/TfiqfvSmb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 307}, "_children": [{"__id__": 321}, {"__id__": 327}], "_active": true, "_components": [{"__id__": 333}, {"__id__": 335}], "_prefab": {"__id__": 337}, "_lpos": {"__type__": "cc.Vec3", "x": -2.125, "y": 49.646, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "item_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 320}, "_children": [], "_active": true, "_components": [{"__id__": 322}, {"__id__": 324}], "_prefab": {"__id__": 326}, "_lpos": {"__type__": "cc.Vec3", "x": -30.87497711181641, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.35, "y": 0.35, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 321}, "_enabled": true, "__prefab": {"__id__": 323}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "728Br/GgBKja3v40uYHMwe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 321}, "_enabled": true, "__prefab": {"__id__": 325}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5ddc7c6-0144-4433-8eb2-53f32cd574f2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26mINc0x1E6Yl0NVuZ5D1W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fhJiVLtVNSLb3nlIug3Un", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_qiyun", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 320}, "_children": [], "_active": true, "_components": [{"__id__": 328}, {"__id__": 330}], "_prefab": {"__id__": 332}, "_lpos": {"__type__": "cc.Vec3", "x": 21.949999999999996, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 329}, "_contentSize": {"__type__": "cc.Size", "width": 57.74995422363281, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1R1sZRnpOFb7n+N7wlYob"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 331}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}, "_string": "16/62", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42bYrOah9KprSq5ZFNczqr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92x5brPy9Cb6SjtViAyuVB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 320}, "_enabled": true, "__prefab": {"__id__": 334}, "_contentSize": {"__type__": "cc.Size", "width": 101.64995422363282, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfprN9prlHEbItHiosI8mz"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 320}, "_enabled": true, "__prefab": {"__id__": 336}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 4, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6XgqTU1lI9r0aig6FKNrC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "21cCLIxTpNsYOEBbAazFJw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 307}, "_children": [{"__id__": 339}, {"__id__": 345}], "_active": true, "_components": [{"__id__": 351}, {"__id__": 353}], "_prefab": {"__id__": 355}, "_lpos": {"__type__": "cc.Vec3", "x": -4.183, "y": -44.163, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lab_cishu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 338}, "_children": [], "_active": true, "_components": [{"__id__": 340}, {"__id__": 342}], "_prefab": {"__id__": 344}, "_lpos": {"__type__": "cc.Vec3", "x": -25.57497711181641, "y": 3.926, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 341}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 28.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29uZjyqq5OmrAvrSm77PsB"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 343}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "次数：", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 23, "_fontSize": 23, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9xW7QIGBPcb+ckTN0dNXr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9cdv8VXeNKd4YLRExdH+hB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_add_count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 338}, "_children": [], "_active": true, "_components": [{"__id__": 346}, {"__id__": 348}], "_prefab": {"__id__": 350}, "_lpos": {"__type__": "cc.Vec3", "x": 31.199999999999996, "y": 5.452, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 345}, "_enabled": true, "__prefab": {"__id__": 347}, "_contentSize": {"__type__": "cc.Size", "width": 57.74995422363281, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88JHUDnmBI4ppsax4gT2xP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 345}, "_enabled": true, "__prefab": {"__id__": 349}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}, "_string": "14/20", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9c8SrzhN1PG4ffooP/5F/t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecdAalHYRJzLBgEywLdhmw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 352}, "_contentSize": {"__type__": "cc.Size", "width": 120.14995422363282, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45/NpQW0VEEa9bWgz90EA4"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 354}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": -6.6, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eNSMGg/JI17Xpph4YLeBW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c29ir0B19J15ySQDq7qyqI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "qiyun_duihua", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 307}, "_children": [{"__id__": 357}], "_active": false, "_components": [{"__id__": 371}, {"__id__": 373}], "_prefab": {"__id__": 375}, "_lpos": {"__type__": "cc.Vec3", "x": 52.152, "y": 106.869, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [{"__id__": 358}], "_active": true, "_components": [{"__id__": 364}, {"__id__": 366}, {"__id__": 368}], "_prefab": {"__id__": 370}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [], "_active": true, "_components": [{"__id__": 359}, {"__id__": 361}], "_prefab": {"__id__": 363}, "_lpos": {"__type__": "cc.Vec3", "x": -1.319, "y": 9.669, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": {"__id__": 360}, "_contentSize": {"__type__": "cc.Size", "width": 104.39997863769531, "height": 65.53999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "721bYuN4NA2op7HAupwldA"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": {"__id__": 362}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 146, "g": 97, "b": 92, "a": 255}, "_string": "气运鼓舞\n战力:+2%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 29, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0IV0FIjtHTLvbzr1Q+FRu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aAIxHt0pD+ayesu7TGkHK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 365}, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acPR+2z8JKdJmILw4XK/xg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 367}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0cba2783-2cdd-4fde-a4aa-79e955fca471@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56gBblzj9B9rtNhCuEqOgk"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 369}, "playOnLoad": false, "_clips": [{"__uuid__": "e6c78ec8-567c-4467-b01f-f269d98aa664", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "e6c78ec8-567c-4467-b01f-f269d98aa664", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "600w1329pK0Lf3aUt+/Pfa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2aEibIWkxA2r2NW7dnvClM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 356}, "_enabled": true, "__prefab": {"__id__": 372}, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fi2qDpB1JjaKaXWc36BDx"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 356}, "_enabled": true, "__prefab": {"__id__": 374}, "playOnLoad": false, "_clips": [{"__uuid__": "e6c78ec8-567c-4467-b01f-f269d98aa664", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "e6c78ec8-567c-4467-b01f-f269d98aa664", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ba4P1nZ5FAqdxbsQ+oitf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1Yae1IblCIpRzSWpezHaN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 377}, "_contentSize": {"__type__": "cc.Size", "width": 177, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6czz7O1qtBEoA2YBCR1xaN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 379}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d305d5d2-e829-43ba-8678-c09c43c159a4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99UKgzagFNlrGoF5m5OEj9"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 381}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.1, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bQvBGaKlME7HA52SUl7oh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afD62vWKFDKqfnWYmLf218", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_tiaozhan", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 384}], "_active": true, "_components": [{"__id__": 390}, {"__id__": 392}, {"__id__": 394}], "_prefab": {"__id__": 396}, "_lpos": {"__type__": "cc.Vec3", "x": 4.543, "y": -520.319, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 383}, "_children": [], "_active": true, "_components": [{"__id__": 385}, {"__id__": 387}], "_prefab": {"__id__": 389}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 386}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2CQKHZPpHQLr5ssfzl67q"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 388}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "挑 战", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "df3a/FVqpHr6wamCJriFVY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9drXc9Tx5IKIqO11vFLmlu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 383}, "_enabled": true, "__prefab": {"__id__": 391}, "_contentSize": {"__type__": "cc.Size", "width": 244, "height": 83}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dc9pcXR5RKZJUePDzHHPQH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 383}, "_enabled": true, "__prefab": {"__id__": 393}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8aac8aa5-631a-436e-92ba-84e56018ec61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7f9HDsHvNG/IqBgii8cwHD"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 383}, "_enabled": true, "__prefab": {"__id__": 395}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.1, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32lhvCmVVNBId7+H1I/tb8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6f8QqT7bNM54FLWtREwrLR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_itemGu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 398}, {"__id__": 404}, {"__id__": 410}, {"__id__": 428}], "_active": true, "_components": [{"__id__": 448}, {"__id__": 450}, {"__id__": 452}], "_prefab": {"__id__": 454}, "_lpos": {"__type__": "cc.Vec3", "x": 233.749, "y": -520.357, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 399}, {"__id__": 401}], "_prefab": {"__id__": 403}, "_lpos": {"__type__": "cc.Vec3", "x": -15.712, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": {"__id__": 400}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5a6Ykxo41K1psLDmI9r3rs"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": {"__id__": 402}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "道具鼓舞", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 56, "g": 92, "b": 142, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17embsNelHsIfygdmxbZ75"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4dm1QkJflABKj0I09NQYWd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_jiant<PERSON>_guwu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 405}, {"__id__": 407}], "_prefab": {"__id__": 409}, "_lpos": {"__type__": "cc.Vec3", "x": 59.65, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 404}, "_enabled": true, "__prefab": {"__id__": 406}, "_contentSize": {"__type__": "cc.Size", "width": 31, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46jsfXTQFMl7zD4w8Jr7V1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 404}, "_enabled": true, "__prefab": {"__id__": 408}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "12c9ed37-3e27-4fe5-b26e-f22e0fbff13c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aPKg/DOpDyJXaBEZobvtD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39adxWRN5P0KY06mO4EPrN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [{"__id__": 411}, {"__id__": 417}], "_active": true, "_components": [{"__id__": 423}, {"__id__": 425}], "_prefab": {"__id__": 427}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 48.62, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "item_1013", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 410}, "_children": [], "_active": true, "_components": [{"__id__": 412}, {"__id__": 414}], "_prefab": {"__id__": 416}, "_lpos": {"__type__": "cc.Vec3", "x": -28.87497711181641, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.35, "y": 0.35, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 411}, "_enabled": true, "__prefab": {"__id__": 413}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aDbjOEAxHvLhF19COsnW3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 411}, "_enabled": true, "__prefab": {"__id__": 415}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "579848b0-1163-40a3-8ae2-d760d89f85d9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dUbQiZI9BR7mcwvyTrIfe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4+3xsNcZMs5iZ4d+Saets", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_item", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 410}, "_children": [], "_active": true, "_components": [{"__id__": 418}, {"__id__": 420}], "_prefab": {"__id__": 422}, "_lpos": {"__type__": "cc.Vec3", "x": 19.949999999999996, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 417}, "_enabled": true, "__prefab": {"__id__": 419}, "_contentSize": {"__type__": "cc.Size", "width": 57.74995422363281, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9zPBF8iJByr4Db7oGuLxa"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 417}, "_enabled": true, "__prefab": {"__id__": 421}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}, "_string": "16/62", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bOroZUX1ED6zQOjpw0chp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbYBQkmHZH55DF76fP8Fmx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 424}, "_contentSize": {"__type__": "cc.Size", "width": 97.64995422363282, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfQsmB1TlGzaL8jcPpDxRQ"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 426}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80vPUVmTxCAKKXrlWr9iOD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "adyVceuYNKXaSJ7bwL4GJg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "item_duihua", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [{"__id__": 429}], "_active": false, "_components": [{"__id__": 443}, {"__id__": 445}], "_prefab": {"__id__": 447}, "_lpos": {"__type__": "cc.Vec3", "x": -44.436, "y": 106.869, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 428}, "_children": [{"__id__": 430}], "_active": true, "_components": [{"__id__": 436}, {"__id__": 438}, {"__id__": 440}], "_prefab": {"__id__": 442}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 429}, "_children": [], "_active": true, "_components": [{"__id__": 431}, {"__id__": 433}], "_prefab": {"__id__": 435}, "_lpos": {"__type__": "cc.Vec3", "x": -1.319, "y": 9.669, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 432}, "_contentSize": {"__type__": "cc.Size", "width": 104.39997863769531, "height": 65.53999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04tp1OK2ZA0oGFmr/x2eIw"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 434}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 146, "g": 97, "b": 92, "a": 255}, "_string": "气运鼓舞\n战力:+2%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 29, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8Z2FZGnJA/KpktfoHudsR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50R011EgdPeKT/NvaUOhK8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 437}, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36qQ0TgOJIwITOzPAN8q9z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 439}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0cba2783-2cdd-4fde-a4aa-79e955fca471@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54TmIALR1NWJyvwiO41697"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 441}, "playOnLoad": false, "_clips": [{"__uuid__": "e6c78ec8-567c-4467-b01f-f269d98aa664", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "e6c78ec8-567c-4467-b01f-f269d98aa664", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "413Cc5LgVPhqx9HfWlKnIq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8yDMzkBVNxrYKfxM76VrU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 444}, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbbOR7xHJARI/zQ4JAu0x+"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 446}, "playOnLoad": false, "_clips": [{"__uuid__": "e6c78ec8-567c-4467-b01f-f269d98aa664", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "e6c78ec8-567c-4467-b01f-f269d98aa664", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c15UuogAFFpqSEAKXEbgP/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ef1L82bIpNsLJ0Sp4ydC2Y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 397}, "_enabled": true, "__prefab": {"__id__": 449}, "_contentSize": {"__type__": "cc.Size", "width": 177, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aeYhnzShDeJRV4WTs9biF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 397}, "_enabled": true, "__prefab": {"__id__": 451}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d305d5d2-e829-43ba-8678-c09c43c159a4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edGxX2rzhEFL66FdphT9O8"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 397}, "_enabled": true, "__prefab": {"__id__": 453}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.1, "_target": {"__id__": 397}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6sskDmphPebVGoumegsmU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43DvG9G2ZHVplrkymznEv4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TY_bg_9g_tanchuangdi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [], "_active": true, "_components": [{"__id__": 456}, {"__id__": 458}], "_prefab": {"__id__": 460}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -206.12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 455}, "_enabled": true, "__prefab": {"__id__": 457}, "_contentSize": {"__type__": "cc.Size", "width": 677, "height": 161}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "628HZwgIdHT5MdlNrAwYyo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 455}, "_enabled": true, "__prefab": {"__id__": 459}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "23736cc0-0cbc-49b1-b866-809dda29d34d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9wMd5he5McJNiQ3TU6XU0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "03nv+ZiGFDx6cGkPzBTggq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "playerBg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 462}, {"__id__": 482}, {"__id__": 488}], "_active": true, "_components": [{"__id__": 494}, {"__id__": 496}], "_prefab": {"__id__": 498}, "_lpos": {"__type__": "cc.Vec3", "x": -262, "y": -204.186, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_player", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 461}, "_children": [{"__id__": 463}], "_active": true, "_components": [{"__id__": 475}, {"__id__": 477}, {"__id__": 479}], "_prefab": {"__id__": 481}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 462}, "_prefab": {"__id__": 464}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 463}, "asset": {"__uuid__": "9f3bd2e8-623f-4363-815e-8dbdf1ced8ba", "__expectedType__": "cc.Prefab"}, "fileId": "e3t6Sl+oNAFp8fYboS8QNH", "instance": {"__id__": 465}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "aaJJ/sQQFA3pTYI5cGGoQM", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 466}, {"__id__": 468}, {"__id__": 469}, {"__id__": 470}, {"__id__": 471}, {"__id__": 473}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 467}, "propertyPath": ["_name"], "value": "BtnHeader"}, {"__type__": "cc.TargetInfo", "localID": ["e3t6Sl+oNAFp8fYboS8QNH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 467}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 467}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 467}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 472}, "propertyPath": ["_enabled"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["40DFblmAxJl539aNhCMcCT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 474}, "propertyPath": ["_enabled"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d3rVy7KgZGv7F5Uwmi8pZD"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 462}, "_enabled": true, "__prefab": {"__id__": 476}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "40Qmk8N21HIIycUixDU2xr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 462}, "_enabled": true, "__prefab": {"__id__": 478}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dce83f7c-f6be-4f28-9e03-bf1ae067524d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dlp//qfJDir0eLCKNDeIj"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 462}, "_enabled": true, "__prefab": {"__id__": 480}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ekowuC0FAbKYrAVVuO2zQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0f/ywk8fVGhpbQaX4QugcV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zhan_li_skt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 461}, "_children": [], "_active": false, "_components": [{"__id__": 483}, {"__id__": 485}], "_prefab": {"__id__": 487}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -65.991, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 482}, "_enabled": true, "__prefab": {"__id__": 484}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.1559733378092448, "y": 0.3064000040690104}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4s0j8VRhLnIGiCv6dtaMp"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 482}, "_enabled": true, "__prefab": {"__id__": 486}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "9d7abb25-d8af-4eed-9912-41b38469911a", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<None>", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efk7J4NjhNk5hq371QKzuu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54pZS12o1CJqU1Oi6f/oH8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tou_xiang_skt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 461}, "_children": [], "_active": false, "_components": [{"__id__": 489}, {"__id__": 491}], "_prefab": {"__id__": 493}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -65.991, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 488}, "_enabled": true, "__prefab": {"__id__": 490}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.1559733378092448, "y": 0.3064000040690104}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8CHDDyM5MGrJaTb0ZnZe1"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 488}, "_enabled": true, "__prefab": {"__id__": 492}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "9d7abb25-d8af-4eed-9912-41b38469911a", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<None>", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6FKe88vxKwpeMPzes8iYa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2cUHAXSNP94OFaNJaa3di", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 461}, "_enabled": true, "__prefab": {"__id__": 495}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 132}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01rmkZBaBC6LOUH+CiO4Kn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 461}, "_enabled": true, "__prefab": {"__id__": 497}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2f256584-d6a2-44d0-8d3d-d805e825c218@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6DzlfWPFAUL5hzIdq+EHt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "473YvXIJJHNLNEQ88LS0lI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heroScroll", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 500}], "_active": true, "_components": [{"__id__": 518}, {"__id__": 520}, {"__id__": 522}], "_prefab": {"__id__": 524}, "_lpos": {"__type__": "cc.Vec3", "x": 72.687, "y": -204.186, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 499}, "_children": [{"__id__": 501}], "_active": true, "_components": [{"__id__": 509}, {"__id__": 511}, {"__id__": 513}, {"__id__": 515}], "_prefab": {"__id__": 517}, "_lpos": {"__type__": "cc.Vec3", "x": -5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "hero<PERSON><PERSON>nt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 500}, "_children": [], "_active": true, "_components": [{"__id__": 502}, {"__id__": 504}, {"__id__": 506}], "_prefab": {"__id__": 508}, "_lpos": {"__type__": "cc.Vec3", "x": -225, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 501}, "_enabled": true, "__prefab": {"__id__": 503}, "_contentSize": {"__type__": "cc.Size", "width": -22, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eKeI32ghEC5S0Ipv4o+hh"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 501}, "_enabled": true, "__prefab": {"__id__": 505}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 1, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 23, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3TO3mRvJKLrvRVGe8L+KN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 501}, "_enabled": true, "__prefab": {"__id__": 507}, "_alignFlags": 8, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eajHP1f45Kmbw11S0hUVbR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16bLayqOhALbnIFfuvmHBf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 510}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 600}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "faAakbJBNAHYTc3LkHIa89"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 512}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79TJf8on1CS6dtxZhWxywn"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 514}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfJTxBSu5PWL3ZzElvh3YP"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 516}, "_alignFlags": 45, "_target": null, "_left": 20, "_right": 30, "_top": -235, "_bottom": -235, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aG313CoRCppQ+GLwJEPOX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9fkE5i71BCoJgZIA6ODVS9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 499}, "_enabled": true, "__prefab": {"__id__": 519}, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19Xnn3lNZCjp/6uB2cUncR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 499}, "_enabled": true, "__prefab": {"__id__": 521}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2f256584-d6a2-44d0-8d3d-d805e825c218@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46uTMIu1RCSqio9a/v8Cxt"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 499}, "_enabled": true, "__prefab": {"__id__": 523}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": true, "vertical": false, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 501}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4xocTXkpOu5Ug6EPleJcz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23v1GqMvZKdoGEC/7yaZQW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_HidePlayerGo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [], "_active": false, "_components": [{"__id__": 526}, {"__id__": 528}, {"__id__": 530}, {"__id__": 532}], "_prefab": {"__id__": 534}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 525}, "_enabled": true, "__prefab": {"__id__": 527}, "_contentSize": {"__type__": "cc.Size", "width": 712, "height": 1214}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a65NI7gGpDtoVTwOrDd4Nu"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 525}, "_enabled": true, "__prefab": {"__id__": 529}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3FT6+IWxLvaEBicBI2c/T"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 525}, "_enabled": true, "__prefab": {"__id__": 531}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8chIjoHrlI/ZOQ21rvhG6b"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 525}, "_enabled": true, "__prefab": {"__id__": 533}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28L7AaPjRAWpqNJ/EYdFqU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9oRtu8VZD05vWLapiCu44", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "topIcon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 536}, {"__id__": 544}, {"__id__": 550}, {"__id__": 558}, {"__id__": 576}], "_active": true, "_components": [{"__id__": 594}, {"__id__": 596}], "_prefab": {"__id__": 598}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 655.813, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 535}, "_children": [], "_active": true, "_components": [{"__id__": 537}, {"__id__": 539}, {"__id__": 541}], "_prefab": {"__id__": 543}, "_lpos": {"__type__": "cc.Vec3", "x": 332.458, "y": -55.558, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 536}, "_enabled": true, "__prefab": {"__id__": 538}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43rLrs3zpFjow8Y/ytVbU+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 536}, "_enabled": true, "__prefab": {"__id__": 540}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8e27d803-97dc-403c-bae5-609f4c45539b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61unCYBh1KcoVezi/d4FBn"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 536}, "_enabled": true, "__prefab": {"__id__": 542}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 536}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afGBweWNBBHp8je6ZZ96er"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40FFDdU4FN1rofKZAijeC5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 535}, "_children": [], "_active": true, "_components": [{"__id__": 545}, {"__id__": 547}], "_prefab": {"__id__": 549}, "_lpos": {"__type__": "cc.Vec3", "x": -260.863, "y": -57.401, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 546}, "_contentSize": {"__type__": "cc.Size", "width": 137, "height": 64.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8OoJdVO5Or6mW48rSm3xZ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 548}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "盘丝洞", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 43, "_fontSize": 45, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 45, "_overflow": 2, "_enableWrapText": false, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 47, "g": 102, "b": 178, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1bcG43vJP1q+gi8Sl/7PG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "efDaVqoN9B1ocRHy9yynji", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_wenhao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 535}, "_children": [], "_active": true, "_components": [{"__id__": 551}, {"__id__": 553}, {"__id__": 555}], "_prefab": {"__id__": 557}, "_lpos": {"__type__": "cc.Vec3", "x": -165.917, "y": -54.693, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 552}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dxGEqSfZLWZqhCwONjUN9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 554}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "de1ad3e4-6d05-46d2-a61c-5c7b4dcbe4bc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baRWk/XnxCEYGdH25/zBX+"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 556}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2370dqZhBGSLlaE8PhNUeP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6dDn0qEkNCeqoroaf/QtVt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_daojudi1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 535}, "_children": [{"__id__": 559}, {"__id__": 565}], "_active": true, "_components": [{"__id__": 571}, {"__id__": 573}], "_prefab": {"__id__": 575}, "_lpos": {"__type__": "cc.Vec3", "x": -18.687, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "energyIcon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 558}, "_children": [], "_active": true, "_components": [{"__id__": 560}, {"__id__": 562}], "_prefab": {"__id__": 564}, "_lpos": {"__type__": "cc.Vec3", "x": -58, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.3, "y": 0.3, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 559}, "_enabled": true, "__prefab": {"__id__": 561}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77HnASJGNLH5YdUlUOxqOu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 559}, "_enabled": true, "__prefab": {"__id__": 563}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5ddc7c6-0144-4433-8eb2-53f32cd574f2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9lSxrnLRLO5QYUzGXdIOS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "179hSRH0xEN7G2WcNd/AUt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "energyNum", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 558}, "_children": [], "_active": true, "_components": [{"__id__": 566}, {"__id__": 568}], "_prefab": {"__id__": 570}, "_lpos": {"__type__": "cc.Vec3", "x": -32, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 565}, "_enabled": true, "__prefab": {"__id__": 567}, "_contentSize": {"__type__": "cc.Size", "width": 75.63671875, "height": 21.42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2c7k4aud9P8rZYxD9H7vbD"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 565}, "_enabled": true, "__prefab": {"__id__": 569}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "123456", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": false, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4a4y7uVXBMmId3ZM3pnRiE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70JdjIUjBAdKhSgZgIxXyq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 558}, "_enabled": true, "__prefab": {"__id__": 572}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cYhGqtzhJ9p8y+r4swto8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 558}, "_enabled": true, "__prefab": {"__id__": 574}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8a1cd6ad-6301-4d51-a76e-1da37e624092@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4QqaJJYRDrIELd4o4zvOs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8ciLms1FNOtKCYwtSYNnsU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_daojudi2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 535}, "_children": [{"__id__": 577}, {"__id__": 583}], "_active": true, "_components": [{"__id__": 589}, {"__id__": 591}], "_prefab": {"__id__": 593}, "_lpos": {"__type__": "cc.Vec3", "x": 120.776, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "experienceIcon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 576}, "_children": [], "_active": true, "_components": [{"__id__": 578}, {"__id__": 580}], "_prefab": {"__id__": 582}, "_lpos": {"__type__": "cc.Vec3", "x": -57, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.3, "y": 0.3, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 577}, "_enabled": true, "__prefab": {"__id__": 579}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5iDyRyihPU4sPCJorJxor"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 577}, "_enabled": true, "__prefab": {"__id__": 581}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0e411d26-ab61-4193-8f52-99dde03b6673@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9e2+J+eHFFroq2RIMlA3I6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3ffKYhZv9F/Yxd/2+srTQW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "experienceNum", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 576}, "_children": [], "_active": true, "_components": [{"__id__": 584}, {"__id__": 586}], "_prefab": {"__id__": 588}, "_lpos": {"__type__": "cc.Vec3", "x": -35.542, "y": 0.948, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 583}, "_enabled": true, "__prefab": {"__id__": 585}, "_contentSize": {"__type__": "cc.Size", "width": 75.63671875, "height": 21.42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdIJj0mj9HE6V/AZSOM2tE"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 583}, "_enabled": true, "__prefab": {"__id__": 587}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "123456", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": false, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8yMxCZQNMRYadUWXN/vXi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1a27bhIiZM/qUQ/1kN5hhz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 590}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6YvzDo/BGY4H4YS4NY6FW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 592}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8a1cd6ad-6301-4d51-a76e-1da37e624092@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5Lc5wBgVITZwlqWa/9Ide"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "15lODZ5QZEYq7/nphVsws4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 535}, "_enabled": true, "__prefab": {"__id__": 595}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 213}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7Ub+1lipKdIE/rkvl6lYJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 535}, "_enabled": true, "__prefab": {"__id__": 597}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1876394a-9418-43ce-a671-ebfb941873a7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4PCJcEOJABYnfVBBW2XkA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "17mBvzjQtCIajWl13B2rUB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_detailLayer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [], "_active": false, "_components": [{"__id__": 600}, {"__id__": 602}, {"__id__": 604}, {"__id__": 606}], "_prefab": {"__id__": 608}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 599}, "_enabled": true, "__prefab": {"__id__": 601}, "_contentSize": {"__type__": "cc.Size", "width": 712, "height": 1214}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74zDOfuSxCQZzxbE3M0jXc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 599}, "_enabled": true, "__prefab": {"__id__": 603}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7/57s4KdMVL+Cz5yKeH4N"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 599}, "_enabled": true, "__prefab": {"__id__": 605}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01nNDxW4lLFoZkUmfnGz8j"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 599}, "_enabled": true, "__prefab": {"__id__": 607}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6KQVzmXRBjZeZEJSfdkyr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6Se2Ck0VDIYgo6VUmZZMy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 610}, "_contentSize": {"__type__": "cc.Size", "width": 712, "height": 1230}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fVEnckIdB45iVsJLiFZFI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 612}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cb229e36-71b5-4d11-86e3-9408ddb638ff@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12VwuOZaNKDpnx+4MfJyVP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cfsP69zdxODZZtmRVonpPy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 615}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eVXLXhrVKCp43qiZvQvp3"}, {"__type__": "cc.UIStaticBatch", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 617}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fB9g58EJGcJ4e1U1MEW3O"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 463}, {"__id__": 52}]}]