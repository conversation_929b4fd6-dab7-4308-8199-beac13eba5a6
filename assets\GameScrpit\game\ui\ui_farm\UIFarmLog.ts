import { _decorator } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { FarmModule } from "../../../module/farm/FarmModule";
import { FarmLogMessage } from "../../net/protocol/Farm";
import { FarmLogAdapter } from "./adapter/FarmLogAdapter";
import { ListView } from "../../common/ListView";
import { UIMgr } from "../../../lib/ui/UIMgr";
const { ccclass, property } = _decorator;

@ccclass("UIFarmLog")
export class UIFarmLog extends UINode {
  protected _openAct: boolean = true;

  private _logAdapter: FarmLogAdapter;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FARM}?prefab/ui/UIFarmLog`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  protected onRegEvent() {
    this._logAdapter = new FarmLogAdapter(this.getNode("node_item"));
    this.getNode("list").getComponent(ListView).setAdapter(this._logAdapter);
  }

  protected onEvtShow(): void {
    FarmModule.api.getLog((data: FarmLogMessage[]) => {
      this._logAdapter.setDatas(data);
    });
  }

  on_click_btn_close() {
    UIMgr.instance.back();
  }
}
