{"skeleton": {"hash": "jR3U+m6IQ0nvdW1Fg+SuJXUrhts", "spine": "3.8.75", "images": "./images/", "audio": "D:/spine/骆驼女"}, "bones": [{"name": "txclltroot", "scaleX": 1.04, "scaleY": 1.04}, {"name": "txbone5", "parent": "txclltroot", "x": -1.26, "y": -25.02, "scaleX": 0.6, "scaleY": 0.6}, {"name": "txbone", "parent": "txbone5", "x": 1.42, "y": 25.24}, {"name": "txst", "parent": "txbone", "length": 23.28, "rotation": -74.23, "x": 127.3, "y": 26.02}, {"name": "txj1", "parent": "txst", "length": 18.29, "rotation": -36.58, "x": 23.81, "y": -2.8}, {"name": "txj2", "parent": "txj1", "length": 15.1, "rotation": 36.57, "x": 19.07, "y": 0.74}, {"name": "txj3", "parent": "txst", "length": 18.31, "rotation": 39.5, "x": 24.28, "y": 6.13}, {"name": "txj4", "parent": "txj3", "length": 16.77, "rotation": -57.02, "x": 17.62, "y": -0.59}, {"name": "txs2", "parent": "txst", "length": 17.8, "rotation": -31.94, "x": -2.99, "y": -13}, {"name": "txs3", "parent": "txs2", "length": 14.3, "rotation": 23.04, "x": 18.72, "y": 0.98}, {"name": "txs1", "parent": "txst", "length": 13.24, "rotation": 80.16, "x": -0.15, "y": 10.98}, {"name": "txs4", "parent": "txs1", "length": 10.06, "rotation": 25.87, "x": 15.81, "y": 1}, {"name": "txt", "parent": "txst", "length": 38.13, "rotation": 163.71, "x": -2.61, "y": -2}, {"name": "txps2", "parent": "txst", "length": 16.34, "rotation": -29.08, "x": 4.74, "y": -20.13}, {"name": "txps3", "parent": "txps2", "length": 13.71, "rotation": 16.89, "x": 17.75, "y": -0.03}, {"name": "txps4", "parent": "txps3", "length": 10.22, "rotation": 13.95, "x": 14.99, "y": 0.05}, {"name": "txps1", "parent": "txst", "length": 15.9, "rotation": 24.43, "x": 5.31, "y": 14.56}, {"name": "txps5", "parent": "txps1", "length": 12.92, "rotation": -33.36, "x": 17.22, "y": -0.77}, {"name": "txps6", "parent": "txps5", "length": 9.29, "rotation": -13.18, "x": 16.06, "y": 1.38}, {"name": "txhuo", "parent": "txbone5", "x": 1.18, "y": -34.26, "scaleX": 0.2, "scaleY": 0.2}, {"name": "txbone2", "parent": "txbone5", "x": -2.46, "y": 93.28, "scaleX": 0.38, "scaleY": 0.72}, {"name": "txbone3", "parent": "txbone5", "x": 1.42, "y": 25.24}, {"name": "txst2", "parent": "txbone3", "length": 23.28, "rotation": -74.23, "x": 127.3, "y": 26.02}, {"name": "txj5", "parent": "txst2", "length": 18.29, "rotation": -36.58, "x": 23.81, "y": -2.8}, {"name": "txj6", "parent": "txj5", "length": 15.1, "rotation": 36.57, "x": 19.07, "y": 0.74}, {"name": "txj7", "parent": "txst2", "length": 18.31, "rotation": 39.5, "x": 24.28, "y": 6.13}, {"name": "txj8", "parent": "txj7", "length": 16.77, "rotation": -57.02, "x": 17.62, "y": -0.59}, {"name": "txs5", "parent": "txst2", "length": 17.8, "rotation": -31.94, "x": -2.99, "y": -13}, {"name": "txs6", "parent": "txs5", "length": 14.3, "rotation": 23.04, "x": 18.72, "y": 0.98}, {"name": "txs7", "parent": "txst2", "length": 13.24, "rotation": 80.16, "x": -0.15, "y": 10.98}, {"name": "txs8", "parent": "txs7", "length": 10.06, "rotation": 25.87, "x": 15.81, "y": 1}, {"name": "txt2", "parent": "txst2", "length": 38.13, "rotation": 163.71, "x": -2.61, "y": -2}, {"name": "txps7", "parent": "txst2", "length": 16.34, "rotation": -29.08, "x": 4.74, "y": -20.13}, {"name": "txps8", "parent": "txps7", "length": 13.71, "rotation": 16.89, "x": 17.75, "y": -0.03}, {"name": "txps9", "parent": "txps8", "length": 10.22, "rotation": 13.95, "x": 14.99, "y": 0.05}, {"name": "txps10", "parent": "txst2", "length": 15.9, "rotation": 24.43, "x": 5.31, "y": 14.56}, {"name": "txps11", "parent": "txps10", "length": 12.92, "rotation": -33.36, "x": 17.22, "y": -0.77}, {"name": "txps12", "parent": "txps11", "length": 9.29, "rotation": -13.18, "x": 16.06, "y": 1.38}, {"name": "txbone4", "parent": "txbone5", "x": 1.42, "y": 25.24}, {"name": "txst3", "parent": "txbone4", "length": 23.28, "rotation": -74.23, "x": 127.3, "y": 26.02}, {"name": "txj9", "parent": "txst3", "length": 18.29, "rotation": -36.58, "x": 23.81, "y": -2.8}, {"name": "txj10", "parent": "txj9", "length": 15.1, "rotation": 36.57, "x": 19.07, "y": 0.74}, {"name": "txj11", "parent": "txst3", "length": 18.31, "rotation": 39.5, "x": 24.28, "y": 6.13}, {"name": "txj12", "parent": "txj11", "length": 16.77, "rotation": -57.02, "x": 17.62, "y": -0.59}, {"name": "txs9", "parent": "txst3", "length": 17.8, "rotation": -31.94, "x": -2.99, "y": -13}, {"name": "txs10", "parent": "txs9", "length": 14.3, "rotation": 23.04, "x": 18.72, "y": 0.98}, {"name": "txs11", "parent": "txst3", "length": 13.24, "rotation": 80.16, "x": -0.15, "y": 10.98}, {"name": "txs12", "parent": "txs11", "length": 10.06, "rotation": 25.87, "x": 15.81, "y": 1}, {"name": "txt3", "parent": "txst3", "length": 38.13, "rotation": 163.71, "x": -2.61, "y": -2}, {"name": "txps13", "parent": "txst3", "length": 16.34, "rotation": -29.08, "x": 4.74, "y": -20.13}, {"name": "txps14", "parent": "txps13", "length": 13.71, "rotation": 16.89, "x": 17.75, "y": -0.03}, {"name": "txps15", "parent": "txps14", "length": 10.22, "rotation": 13.95, "x": 14.99, "y": 0.05}, {"name": "txps16", "parent": "txst3", "length": 15.9, "rotation": 24.43, "x": 5.31, "y": 14.56}, {"name": "txps17", "parent": "txps16", "length": 12.92, "rotation": -33.36, "x": 17.22, "y": -0.77}, {"name": "txps18", "parent": "txps17", "length": 9.29, "rotation": -13.18, "x": 16.06, "y": 1.38}, {"name": "txtarget1", "parent": "txclltroot", "x": 167.18, "y": -214.97, "color": "ff3f00ff"}, {"name": "troot", "parent": "txclltroot", "x": -5.49, "y": -15.25, "scaleX": 0.1, "scaleY": 0.1}, {"name": "tst", "parent": "troot", "length": 298.8, "rotation": 37.04, "x": -74.38, "y": 134.41}, {"name": "tst2", "parent": "tst", "length": 59.19, "rotation": -118.3, "x": 68.51, "y": -143.86}, {"name": "tst3", "parent": "tst2", "length": 105.32, "rotation": -72.55, "x": 56.91}, {"name": "tst4", "parent": "tst", "length": 51.72, "rotation": -143.9, "x": -112.15, "y": 21.4}, {"name": "tw1", "parent": "tst", "length": 106.54, "rotation": 17.7, "x": 311.33, "y": -44.79}, {"name": "tw2", "parent": "tw1", "length": 77.49, "rotation": 20.68, "x": 116.23, "y": 7.98}, {"name": "tw3", "parent": "tw2", "length": 85.29, "rotation": 48.82, "x": 83.5, "y": 11.28}, {"name": "tw4", "parent": "tw3", "length": 74.65, "rotation": 33.31, "x": 100.68, "y": 27.42}, {"name": "tw5", "parent": "tw4", "length": 84.48, "rotation": 38.95, "x": 99.78, "y": 11.56}, {"name": "tj2", "parent": "tst", "length": 138.86, "rotation": -100.75, "x": 148.81, "y": -178.73}, {"name": "tj3", "parent": "tj2", "length": 151.51, "rotation": -25.72, "x": 167.02, "y": 9.19}, {"name": "tj4", "parent": "tj2", "length": 125.9, "rotation": -41.48, "x": 118.28, "y": -50.39}, {"name": "tj1", "parent": "tst", "length": 193.65, "rotation": 145.18, "x": -87.83, "y": 77.57}, {"name": "tj5", "parent": "tj1", "length": 121.58, "rotation": 36.77, "x": 213.41, "y": -5.39}, {"name": "tj6", "parent": "tj1", "length": 126.79, "rotation": 61.82, "x": 155.61, "y": 30.05}, {"name": "ttt1", "parent": "tst", "length": 75.94, "rotation": -34.5, "x": 213.7, "y": -168.09}, {"name": "ttt2", "parent": "ttt1", "length": 31.36, "rotation": -56.29, "x": 82.53, "y": -5.29}, {"name": "ttt3", "parent": "ttt2", "length": 73.59, "rotation": -46.15, "x": 37.3, "y": -1.22}, {"name": "ttt4", "parent": "tst", "length": 91.02, "rotation": -21.46, "x": 254.11, "y": -154.92}, {"name": "ttt5", "parent": "ttt4", "length": 37.78, "rotation": -54.24, "x": 98.48, "y": -8.95}, {"name": "ttt6", "parent": "ttt5", "length": 82.63, "rotation": -52.51, "x": 47.72, "y": -1.27}, {"name": "ttt7", "parent": "tst", "length": 105.1, "rotation": -6.16, "x": 283.31, "y": -148.52}, {"name": "ttt8", "parent": "ttt7", "length": 51.12, "rotation": -44.23, "x": 110.39, "y": -11.25}, {"name": "ttt9", "parent": "ttt8", "length": 81.25, "rotation": -54.08, "x": 56.09, "y": -11.68}, {"name": "tt1", "parent": "tst", "length": 81.92, "rotation": 104.1, "x": 22.77, "y": 106.27}, {"name": "tt2", "parent": "tt1", "length": 39.11, "rotation": 51.95, "x": 81.42, "y": 2.14}, {"name": "tt3", "parent": "tt2", "length": 71.79, "rotation": 52.87, "x": 49.3, "y": 5.15}, {"name": "tt4", "parent": "tst", "length": 97.13, "rotation": 96.85, "x": 84.94, "y": 100.97}, {"name": "tt5", "parent": "tt4", "length": 46.92, "rotation": 56.99, "x": 103.29, "y": 17.66}, {"name": "tt6", "parent": "tt5", "length": 78.81, "rotation": 58.03, "x": 55.6, "y": 4.92}, {"name": "tt7", "parent": "tst", "length": 114.73, "rotation": 100.15, "x": 154.76, "y": 88.83}, {"name": "tt8", "parent": "tt7", "length": 43.64, "rotation": 48.64, "x": 118.58, "y": 11.14}, {"name": "tt9", "parent": "tt8", "length": 99.06, "rotation": 57.61, "x": 55.52, "y": -1.38}, {"name": "root", "parent": "txclltroot", "x": -0.33, "y": -2.28, "scaleX": 0.62, "scaleY": 0.62}, {"name": "st", "parent": "root", "length": 16.32, "rotation": 162.59, "x": 1.27, "y": 21.89}, {"name": "st2", "parent": "st", "length": 24.3, "rotation": -78.83, "x": 0.99, "y": -2.75}, {"name": "c1", "parent": "st2", "length": 16.37, "rotation": 69.4, "x": 23.13, "y": 6.33}, {"name": "c2", "parent": "c1", "length": 13.73, "rotation": 4.22, "x": 18.61, "y": 1.45}, {"name": "c3", "parent": "c2", "length": 12.51, "rotation": 20.2, "x": 15.61, "y": 0.82}, {"name": "c4", "parent": "c3", "length": 10.81, "rotation": 18.49, "x": 13.38, "y": 0.06}, {"name": "c11", "parent": "c1", "length": 12.56, "rotation": 60.25, "x": 24.52, "y": 10.85}, {"name": "c12", "parent": "c11", "length": 8.33, "rotation": 36.22, "x": 14.98, "y": 0.91}, {"name": "c5", "parent": "st2", "length": 18.17, "rotation": -45.45, "x": 24.11, "y": -5.74}, {"name": "c6", "parent": "c5", "length": 14.73, "rotation": -13.59, "x": 20.67, "y": -0.59}, {"name": "c7", "parent": "c6", "length": 13.25, "rotation": -14.01, "x": 16.46, "y": 0.8}, {"name": "c8", "parent": "c7", "length": 10.67, "rotation": -24.06, "x": 15.41, "y": -0.5}, {"name": "c9", "parent": "c5", "length": 8.99, "rotation": -68.06, "x": 23.78, "y": -14.39}, {"name": "c10", "parent": "c9", "length": 12.46, "rotation": -14.53, "x": 12.07, "y": -1.25}, {"name": "t1", "parent": "st2", "length": 5.98, "rotation": -57.2, "x": 1.39, "y": -2.97}, {"name": "t2", "parent": "t1", "length": 19.67, "rotation": -55.75, "x": 8.63}, {"name": "st3", "parent": "st", "length": 15.67, "rotation": 51.77, "x": 1.9, "y": 3.26}, {"name": "z1", "parent": "st3", "length": 9.64, "rotation": 144.86, "x": 18.51, "y": 0.85}, {"name": "z2", "parent": "z1", "length": 8.42, "rotation": -121.41, "x": 9.04, "y": -1.78}, {"name": "z3", "parent": "st3", "length": 7.42, "rotation": 150.74, "x": 2.98, "y": 9.62}, {"name": "z4", "parent": "z3", "length": 7.54, "rotation": -127.96, "x": 7.02, "y": -1.26}, {"name": "wb", "parent": "st3", "length": 3.24, "rotation": -11.98, "x": 21.1, "y": -1.7}, {"name": "wb2", "parent": "wb", "length": 4.05, "rotation": 12, "x": 5.28, "y": 0.39}, {"name": "wb3", "parent": "wb2", "length": 3.49, "rotation": 14.71, "x": 5.06, "y": -0.39}, {"name": "bone", "parent": "txclltroot", "length": 101.7, "rotation": 90}, {"name": "bone2", "parent": "txclltroot", "x": -0.54, "y": -0.26}, {"name": "honghuo2", "parent": "bone2", "length": 18.65, "rotation": 91.47, "x": -2.32, "y": 41.41, "scaleX": 1.4}], "slots": [{"name": "txy<PERSON><PERSON>", "bone": "txbone"}, {"name": "txyingzi2", "bone": "txbone3"}, {"name": "txyingzi3", "bone": "txbone4"}, {"name": "txhuo", "bone": "txhuo"}, {"name": "txps2", "bone": "txps2"}, {"name": "txps5", "bone": "txps13"}, {"name": "txps3", "bone": "txps7"}, {"name": "txps1", "bone": "txps1"}, {"name": "txps6", "bone": "txps16"}, {"name": "txps4", "bone": "txps10"}, {"name": "txs2", "bone": "txs2"}, {"name": "txs5", "bone": "txs9"}, {"name": "txs3", "bone": "txs5"}, {"name": "txs1", "bone": "txs1"}, {"name": "txs6", "bone": "txs11"}, {"name": "txs4", "bone": "txs7"}, {"name": "txst", "bone": "txst"}, {"name": "txst3", "bone": "txst3"}, {"name": "txst2", "bone": "txst2"}, {"name": "txj2", "bone": "txj3"}, {"name": "txj6", "bone": "txj11"}, {"name": "txj4", "bone": "txj7"}, {"name": "txj1", "bone": "txj1"}, {"name": "txj5", "bone": "txj9"}, {"name": "txj3", "bone": "txj5"}, {"name": "txt", "bone": "txt"}, {"name": "txt3", "bone": "txt3"}, {"name": "txt2", "bone": "txt2"}, {"name": "<PERSON><PERSON>", "bone": "tst"}, {"name": "ttt3", "bone": "ttt7", "color": "f4ed9fff"}, {"name": "ttt2", "bone": "ttt4", "color": "f4ed9fff"}, {"name": "ttt1", "bone": "ttt1", "color": "f4ed9fff"}, {"name": "tt3", "bone": "tt7", "color": "f4ed9fff"}, {"name": "tt2", "bone": "tt4", "color": "f4ed9fff"}, {"name": "tt1", "bone": "tt1", "color": "f4ed9fff"}, {"name": "tw1", "bone": "tw1", "color": "f4ed9fff"}, {"name": "tj1", "bone": "tj1", "color": "f4ed9fff"}, {"name": "tst", "bone": "tst", "color": "f4ed9fff"}, {"name": "tj2", "bone": "tj2", "color": "f4ed9fff"}, {"name": "wb", "bone": "wb"}, {"name": "c2", "bone": "c5"}, {"name": "c1", "bone": "c1"}, {"name": "st", "bone": "st"}, {"name": "z2", "bone": "z3"}, {"name": "z1", "bone": "z1"}, {"name": "t1", "bone": "t1"}, {"name": "root", "bone": "root"}, {"name": "honghuo2", "bone": "honghuo2", "color": "fffffff1"}], "skins": [{"name": "default", "attachments": {"txj2": {"j2": {"type": "mesh", "uvs": [0.44312, 0.01315, 0.79232, 0.17297, 0.88609, 0.2759, 0.96793, 0.36572, 0.96752, 0.46751, 0.96732, 0.51851, 0.96697, 0.60643, 0.9084, 0.65631, 0.84744, 0.70824, 0.90718, 0.78046, 0.96853, 0.85462, 0.96663, 0.97617, 0.79904, 0.97597, 0.64879, 0.93896, 0.50028, 0.8313, 0.47183, 0.76191, 0.4429, 0.69133, 0.40029, 0.58738, 0.36632, 0.50451, 0.36424, 0.43939, 0.23373, 0.40333, 0.09841, 0.36593, 0, 0.21913, 0, 0.20154, 0.33143, 0.09779], "triangles": [22, 23, 24, 1, 24, 0, 21, 22, 24, 4, 17, 18, 5, 17, 4, 5, 7, 17, 6, 7, 5, 8, 16, 17, 7, 8, 17, 15, 16, 8, 14, 15, 8, 13, 14, 8, 13, 8, 9, 12, 13, 9, 10, 12, 9, 11, 12, 10, 1, 19, 24, 19, 1, 2, 20, 24, 19, 4, 2, 3, 19, 2, 4, 18, 19, 4, 20, 21, 24], "vertices": [1, 3, 19.46, 14.01, 1, 1, 6, 13.16, 9.71, 1, 1, 6, 17.69, 7.92, 1, 2, 6, 21.65, 6.35, 0.96142, 7, -3.63, 7.16, 0.03858, 2, 6, 23.92, 3.05, 0.67985, 7, 0.38, 7.27, 0.32015, 2, 6, 25.07, 1.39, 0.45832, 7, 2.39, 7.33, 0.54168, 2, 6, 27.03, -1.46, 0.20181, 7, 5.86, 7.42, 0.79819, 2, 6, 26.76, -4.04, 0.0934, 7, 7.87, 5.8, 0.9066, 2, 6, 26.48, -6.73, 0.01525, 7, 9.97, 4.1, 0.98475, 2, 6, 29.52, -8.09, 2e-05, 7, 12.77, 5.91, 0.99998, 1, 7, 15.63, 7.77, 1, 1, 7, 20.43, 7.86, 1, 1, 7, 20.57, 3.03, 1, 1, 7, 19.24, -1.35, 1, 1, 7, 15.13, -5.76, 1, 2, 6, 18.78, -14.64, 0.00185, 7, 12.42, -6.66, 0.99815, 2, 6, 16.51, -12.83, 0.02826, 7, 9.66, -7.58, 0.97174, 2, 6, 13.17, -10.16, 0.20535, 7, 5.61, -8.94, 0.79465, 2, 6, 10.5, -8.03, 0.49256, 7, 2.37, -10.02, 0.50744, 2, 6, 8.99, -5.96, 0.78513, 7, -0.19, -10.16, 0.21487, 2, 6, 5.09, -6.93, 0.97898, 7, -1.5, -13.96, 0.02102, 3, 6, 1.04, -7.95, 0.95187, 7, -2.85, -17.91, 0.00013, 3, 30.14, 0.66, 0.048, 2, 6, -4.59, -4.81, 0.248, 3, 23.8, -0.5, 0.752, 1, 3, 23.13, -0.31, 1, 1, 3, 21.79, 10, 1], "hull": 25, "edges": [0, 48, 0, 2, 20, 22, 22, 24, 24, 26, 26, 28, 36, 38, 42, 44, 44, 46, 46, 48, 34, 36, 32, 34, 28, 30, 30, 32, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 12, 14, 14, 16, 2, 4, 4, 6, 38, 40, 40, 42], "width": 30, "height": 41}}, "txt3": {"t": {"type": "mesh", "uvs": [0.45189, 0.01329, 0.53961, 0.01244, 0.59395, 0.04517, 0.59403, 0.15484, 0.82644, 0.17281, 0.88036, 0.18649, 0.98347, 0.18649, 0.99318, 0.42748, 0.96841, 0.84728, 0.9007, 0.87579, 0.79898, 0.95712, 0.71051, 0.9868, 0.2606, 0.98745, 0.06942, 0.86339, 0.01158, 0.83869, 0.04931, 0.66028, 0.01528, 0.57291, 0.01545, 0.50545, 0.0465, 0.45331, 0.0773, 0.42627, 0.00219, 0.29764, 0.02307, 0.18579, 0.20755, 0.18665, 0.40474, 0.13626, 0.40623, 0.05307], "triangles": [22, 20, 21, 19, 20, 22, 18, 16, 17, 18, 19, 16, 15, 16, 19, 7, 4, 5, 7, 5, 6, 12, 13, 15, 14, 15, 13, 24, 0, 1, 24, 1, 23, 3, 1, 2, 3, 23, 1, 8, 9, 7, 15, 3, 7, 4, 7, 3, 9, 10, 7, 10, 11, 7, 23, 19, 22, 19, 3, 15, 12, 15, 11, 23, 3, 19, 7, 11, 15], "vertices": [1, 48, 52.84, 2.51, 1, 1, 48, 52.95, -2.89, 1, 1, 48, 50.62, -6.25, 1, 1, 48, 42.71, -6.33, 1, 1, 48, 41.54, -20.64, 1, 1, 48, 40.59, -23.97, 1, 1, 48, 40.64, -30.31, 1, 1, 48, 23.27, -31.06, 1, 1, 48, -7.02, -29.81, 1, 1, 48, -9.11, -25.66, 1, 1, 48, -15.03, -19.46, 1, 1, 48, -17.22, -14.03, 1, 1, 48, -17.51, 13.65, 1, 1, 48, -8.67, 25.5, 1, 1, 48, -6.92, 29.07, 1, 1, 48, 5.96, 26.87, 1, 1, 48, 12.24, 29.02, 1, 1, 48, 17.11, 29.05, 1, 1, 48, 20.89, 27.17, 1, 1, 48, 22.85, 25.3, 1, 1, 48, 32.09, 30, 1, 1, 48, 40.16, 28.79, 1, 1, 48, 40.2, 17.44, 1, 1, 48, 43.95, 5.33, 1, 1, 48, 49.95, 5.3, 1], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 8, 10], "width": 64, "height": 75}}, "t1": {"tut1": {"type": "mesh", "uvs": [0.19821, 0.02525, 0.33588, 0.03971, 0.47604, 0.05444, 0.61877, 0.06943, 0.7116, 0.17121, 0.818, 0.28786, 0.92594, 0.40619, 0.92504, 0.56036, 0.92432, 0.68373, 0.92323, 0.87001, 0.92248, 1, 0.87957, 1, 0.79082, 0.90426, 0.718, 0.82568, 0.62531, 0.72569, 0.55616, 0.65108, 0.4049, 0.64939, 0.25416, 0.6477, 0.12853, 0.6463, 0, 0.64486, 0, 0.57882, 0.02607, 0.3984, 0.05469, 0.20032, 0.08176, 0.01302], "triangles": [18, 19, 20, 0, 22, 23, 1, 22, 0, 18, 20, 21, 22, 17, 21, 17, 18, 21, 22, 1, 17, 17, 1, 16, 7, 5, 6, 16, 1, 2, 4, 15, 2, 4, 2, 3, 15, 4, 5, 14, 15, 5, 16, 2, 15, 5, 7, 14, 8, 14, 7, 13, 14, 8, 8, 12, 13, 9, 12, 8, 11, 12, 9, 10, 11, 9], "vertices": [3, 106, -11.56, 4.19, 0.07978, 105, 5.59, 11.91, 0.32022, 92, 14.43, -1.21, 0.6, 2, 106, -6.84, 6.33, 0.56205, 105, 10.01, 9.22, 0.43795, 2, 106, -2.04, 8.51, 0.92985, 105, 14.52, 6.47, 0.07015, 1, 106, 2.86, 10.73, 1, 1, 106, 7.37, 9.78, 1, 1, 106, 12.55, 8.69, 1, 1, 106, 17.81, 7.58, 1, 1, 106, 20.02, 3.55, 1, 1, 106, 21.79, 0.33, 1, 1, 106, 24.46, -4.54, 1, 1, 106, 26.32, -7.94, 1, 1, 106, 24.92, -8.72, 1, 1, 106, 20.62, -7.85, 1, 1, 106, 17.09, -7.14, 1, 2, 106, 12.61, -6.23, 0.99346, 105, 10.58, -13.93, 0.00654, 2, 106, 9.26, -5.55, 0.92259, 105, 9.25, -10.78, 0.07741, 2, 106, 4.28, -8.27, 0.43275, 105, 4.2, -8.2, 0.56725, 2, 106, -0.68, -10.99, 0.03669, 105, -0.83, -5.62, 0.96331, 2, 105, -5.03, -3.48, 0.72, 92, -4.26, -0.63, 0.28, 2, 105, -9.32, -1.29, 0.4, 92, -4.74, 4.17, 0.6, 2, 105, -8.44, 0.48, 0.304, 92, -2.78, 4.38, 0.696, 2, 105, -5.16, 4.85, 0.288, 92, 2.67, 3.99, 0.712, 3, 106, -13.71, -2.99, 0.01031, 105, -1.56, 9.65, 0.27769, 92, 8.66, 3.57, 0.712, 3, 106, -15.55, 2.38, 0.02417, 105, 1.85, 14.19, 0.22383, 92, 14.32, 3.17, 0.752], "hull": 24, "edges": [20, 22, 38, 40, 30, 32, 32, 34, 34, 36, 36, 38, 28, 30, 26, 28, 22, 24, 24, 26, 16, 18, 18, 20, 12, 14, 14, 16, 10, 12, 6, 8, 8, 10, 6, 4, 4, 2, 2, 0, 0, 46, 44, 46, 40, 42, 42, 44], "width": 39, "height": 31}}, "ttt2": {"tt2": {"type": "mesh", "uvs": [0.60144, 0.00664, 0.71953, 0.08357, 0.8483, 0.16746, 0.96164, 0.24129, 0.99743, 0.38756, 0.99341, 0.53219, 0.99071, 0.62941, 0.98968, 0.80155, 0.92461, 0.89773, 0.85541, 1, 0.83917, 1, 0.83856, 0.87829, 0.83175, 0.74899, 0.82646, 0.64859, 0.81224, 0.57324, 0.80099, 0.48041, 0.78628, 0.35906, 0.713, 0.32441, 0.65624, 0.29757, 0.54423, 0.35845, 0.41765, 0.42726, 0.15147, 0.42718, 0.01306, 0.35903, 0.00326, 0.2527, 0.21497, 0.12025, 0.36086, 0.06166, 0.48518, 0.01172], "triangles": [8, 9, 11, 9, 10, 11, 8, 11, 7, 11, 12, 7, 7, 12, 6, 12, 13, 6, 13, 14, 6, 6, 14, 5, 14, 15, 5, 5, 15, 4, 15, 16, 4, 16, 3, 4, 16, 2, 3, 16, 17, 2, 17, 18, 2, 18, 1, 2, 21, 24, 20, 24, 25, 20, 20, 25, 19, 24, 21, 23, 21, 22, 23, 25, 26, 19, 18, 26, 0, 18, 19, 26, 18, 0, 1], "vertices": [2, 75, 86.78, 32.86, 0.99945, 76, -40.77, 14.93, 0.00055, 2, 75, 104.59, 12.61, 0.60993, 76, -13.93, 17.55, 0.39007, 2, 75, 124.01, -9.47, 0.0007, 76, 15.34, 20.41, 0.9993, 2, 76, 41.1, 22.93, 0.74681, 77, -23.23, 9.47, 0.25319, 2, 76, 63.93, 5.35, 0.00482, 77, 4.62, 16.89, 0.99518, 1, 77, 32.3, 16.68, 1, 1, 77, 50.91, 16.55, 1, 1, 77, 83.85, 17.02, 1, 1, 77, 102.5, 4.95, 1, 1, 77, 122.34, -7.89, 1, 1, 77, 122.4, -11, 1, 1, 77, 99.12, -11.59, 1, 1, 77, 74.41, -13.4, 1, 1, 77, 55.22, -14.8, 1, 2, 76, 58.46, -44.53, 0.00224, 77, 40.86, -17.82, 0.99776, 2, 76, 45.68, -32.01, 0.09635, 77, 23.15, -20.33, 0.90365, 3, 75, 102.73, -41.6, 0.00092, 76, 28.98, -15.63, 0.82917, 77, -0.01, -23.62, 0.16991, 3, 75, 91.01, -31.45, 0.09954, 76, 13.89, -19.21, 0.90006, 77, -6.35, -37.77, 0.00039, 2, 75, 81.93, -23.58, 0.49572, 76, 2.2, -21.99, 0.50428, 2, 75, 58.15, -29.05, 0.96937, 76, -7.26, -44.48, 0.03063, 1, 75, 31.28, -35.22, 1, 1, 75, -17.77, -21.53, 1, 1, 75, -39.78, -1.85, 1, 1, 75, -36.12, 18.25, 1, 1, 75, 9.71, 31.78, 1, 1, 75, 39.61, 35.08, 1, 1, 75, 65.09, 37.89, 1], "hull": 27, "edges": [0, 52, 6, 8, 12, 14, 18, 20, 20, 22, 26, 28, 40, 42, 42, 44, 44, 46, 46, 48, 14, 16, 16, 18, 8, 10, 10, 12, 22, 24, 24, 26, 28, 30, 30, 32, 4, 6, 0, 2, 2, 4, 36, 38, 38, 40, 48, 50, 50, 52, 32, 34, 34, 36], "width": 129, "height": 129}}, "tyingzi": {"xyingzi": {"x": 51.62, "y": -27.08, "scaleX": 17.78, "scaleY": 16.8, "rotation": -35.65, "width": 54, "height": 38}}, "z1": {"z1": {"type": "mesh", "uvs": [0.41154, 0.07591, 0.62696, 0.03605, 0.74777, 0.06241, 0.88227, 0.09175, 0.9588, 0.18922, 0.95885, 0.32487, 0.95892, 0.50039, 0.87494, 0.68413, 0.75012, 0.81884, 0.55504, 0.87192, 0.42561, 0.90714, 0.26956, 0.9496, 0.25582, 0.79699, 0.24075, 0.62948, 0.22921, 0.50125, 0.04072, 0.50091, 0.04111, 0.31761, 0.11838, 0.23272, 0.23066, 0.10937, 0.38299, 0.44122, 0.60965, 0.3685, 0.78632, 0.36486], "triangles": [11, 12, 10, 9, 10, 13, 9, 13, 19, 9, 20, 8, 9, 19, 20, 7, 20, 21, 7, 8, 20, 13, 10, 12, 7, 21, 6, 13, 14, 19, 21, 5, 6, 20, 2, 21, 21, 4, 5, 15, 16, 14, 16, 17, 14, 14, 17, 19, 17, 18, 19, 19, 0, 20, 19, 18, 0, 20, 1, 2, 20, 0, 1, 21, 3, 4, 21, 2, 3], "vertices": [1, 108, 3.98, 4.06, 1, 1, 108, 8.94, 4.97, 1, 2, 108, 11.74, 4.45, 0.96046, 109, -6.72, -0.94, 0.03954, 2, 108, 14.85, 3.87, 0.80685, 109, -7.85, 2.02, 0.19315, 2, 108, 16.64, 1.83, 0.6525, 109, -7.05, 4.61, 0.3475, 2, 108, 16.68, -1.04, 0.40486, 109, -4.62, 6.14, 0.59514, 2, 108, 16.74, -4.75, 0.11062, 109, -1.48, 8.12, 0.88938, 2, 108, 14.85, -8.66, 0.0038, 109, 2.84, 8.55, 0.9962, 1, 109, 6.79, 7.63, 1, 1, 109, 10.14, 4.42, 1, 1, 109, 12.36, 2.29, 1, 1, 109, 15.04, -0.28, 1, 2, 108, 0.6, -11.25, 0.01441, 109, 12.48, -2.27, 0.98559, 2, 108, 0.2, -7.71, 0.1901, 109, 9.66, -4.45, 0.8099, 2, 108, -0.1, -5, 0.64812, 109, 7.51, -6.12, 0.35188, 2, 108, -4.45, -5.05, 0.96613, 109, 9.82, -9.81, 0.03387, 2, 108, -4.49, -1.17, 0.99669, 109, 6.53, -11.86, 0.00331, 1, 108, -2.74, 0.65, 1, 1, 108, -0.18, 3.29, 1, 2, 108, 3.43, -3.68, 0.50866, 109, 4.54, -3.8, 0.49134, 2, 108, 8.64, -2.07, 0.01011, 109, 0.45, -0.19, 0.98989, 2, 108, 12.71, -1.94, 0.26578, 109, -1.78, 3.22, 0.73422], "hull": 19, "edges": [6, 8, 12, 14, 14, 16, 28, 30, 30, 32, 16, 18, 18, 20, 20, 22, 26, 28, 22, 24, 24, 26, 32, 34, 34, 36, 2, 0, 0, 36, 2, 4, 4, 6, 8, 10, 10, 12], "width": 24, "height": 22}}, "txst": {"st": {"type": "mesh", "uvs": [0.82101, 0.30365, 0.86062, 0.37587, 0.94396, 0.43337, 0.98785, 0.54802, 0.84671, 0.61126, 0.46063, 0.64999, 0.41284, 0.65108, 0.19993, 0.46235, 0.19758, 0.41855, 0.34113, 0.36593, 0.56637, 0.37587], "triangles": [10, 0, 1, 7, 8, 9, 2, 4, 10, 2, 10, 1, 4, 2, 3, 10, 7, 9, 5, 10, 4, 5, 7, 10, 6, 7, 5], "vertices": [1, 3, -6.9, 14.21, 1, 1, 3, 0.9, 13.99, 1, 1, 3, 7.79, 16.21, 1, 1, 3, 19.93, 14.97, 1, 1, 3, 24.46, 6.64, 1, 1, 3, 23.32, -12.33, 1, 1, 3, 22.81, -14.57, 1, 1, 3, 0.99, -19.04, 1, 1, 3, -3.46, -17.9, 1, 1, 3, -6.89, -9.76, 1, 1, 3, -2.95, 0.37, 1], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 16, 18, 18, 20, 10, 12], "width": 50, "height": 109}}, "txps1": {"ps1": {"type": "mesh", "uvs": [0.79524, 0.03528, 0.87329, 0.14478, 0.90309, 0.22841, 0.93092, 0.30652, 0.965, 0.40216, 0.98026, 0.52965, 0.99449, 0.64854, 0.97242, 0.74131, 0.92987, 0.73117, 0.89763, 0.72349, 0.89762, 0.81796, 0.89762, 0.8713, 0.89761, 0.92808, 0.85278, 0.99282, 0.82021, 0.94647, 0.82007, 0.90666, 0.81077, 0.81715, 0.80355, 0.74765, 0.79341, 0.6501, 0.78135, 0.53397, 0.76698, 0.65912, 0.75285, 0.78222, 0.73984, 0.89555, 0.71435, 0.89462, 0.67938, 0.89334, 0.67892, 0.81422, 0.67843, 0.7306, 0.66369, 0.62238, 0.64926, 0.5164, 0.58904, 0.52155, 0.58902, 0.39309, 0.589, 0.26434, 0.58899, 0.17354, 0.72398, 0.03427, 0.85282, 0.31584, 0.89508, 0.51448, 0.74058, 0.32136, 0.77491, 0.43907, 0.65475, 0.38205, 0.71021, 0.58254, 0.85282, 0.64691, 0.85532, 0.82477, 0.7186, 0.71848], "triangles": [13, 14, 12, 14, 15, 12, 12, 15, 11, 15, 41, 11, 15, 16, 41, 11, 41, 10, 9, 10, 41, 17, 9, 41, 17, 41, 16, 17, 40, 9, 7, 8, 6, 8, 9, 6, 9, 40, 6, 22, 23, 21, 21, 23, 25, 23, 24, 25, 25, 42, 21, 25, 26, 42, 21, 42, 20, 17, 18, 40, 26, 27, 42, 6, 35, 5, 35, 6, 40, 27, 39, 42, 42, 39, 20, 20, 39, 19, 18, 19, 40, 40, 19, 35, 27, 28, 39, 19, 39, 37, 39, 28, 37, 19, 37, 35, 35, 4, 5, 28, 38, 37, 37, 34, 35, 4, 35, 34, 29, 30, 28, 28, 30, 38, 4, 34, 3, 38, 36, 37, 37, 36, 34, 30, 31, 38, 38, 31, 36, 36, 32, 33, 34, 36, 1, 36, 33, 0, 34, 2, 3, 34, 1, 2, 1, 36, 0, 36, 31, 32], "vertices": [1, 16, 1.11, 10.62, 1, 2, 16, 9.39, 11.28, 0.99581, 17, -13.16, 5.77, 0.00419, 2, 16, 14.27, 10.08, 0.89319, 17, -8.43, 7.45, 0.10681, 3, 16, 18.83, 8.96, 0.55375, 17, -4, 9.02, 0.44176, 18, -21.28, 2.86, 0.0045, 3, 16, 24.42, 7.59, 0.13811, 17, 1.42, 10.94, 0.80824, 18, -16.44, 5.97, 0.05365, 3, 16, 30.4, 4.03, 0.00252, 17, 8.37, 11.26, 0.69409, 18, -9.74, 7.86, 0.30338, 2, 17, 14.85, 11.56, 0.26442, 18, -3.5, 9.63, 0.73558, 2, 17, 19.61, 9.32, 0.07451, 18, 1.65, 8.54, 0.92549, 2, 17, 18.69, 6.22, 0.05472, 18, 1.46, 5.3, 0.94528, 2, 17, 17.99, 3.87, 0.01219, 18, 1.31, 2.86, 0.98781, 1, 18, 6.37, 3.42, 1, 1, 18, 9.22, 3.73, 1, 1, 18, 12.26, 4.07, 1, 1, 18, 16.1, 1.11, 1, 1, 18, 13.89, -1.59, 1, 1, 18, 11.76, -1.84, 1, 2, 17, 22.22, -3.2, 0.00161, 18, 7.04, -3.06, 0.99839, 2, 17, 18.44, -3.3, 0.0989, 18, 3.39, -4.01, 0.9011, 2, 17, 13.14, -3.42, 0.86126, 18, -1.75, -5.35, 0.13874, 2, 16, 20.95, -7.51, 0.05057, 17, 6.82, -3.58, 0.94943, 2, 16, 25.4, -12.68, 0.0284, 17, 13.38, -5.45, 0.9716, 2, 16, 29.78, -17.77, 0.00023, 17, 19.84, -7.29, 0.99977, 1, 17, 25.78, -8.99, 1, 1, 17, 25.5, -10.88, 1, 1, 17, 25.12, -13.48, 1, 1, 17, 20.89, -13, 1, 2, 16, 24.05, -20.24, 0.00902, 17, 16.41, -12.5, 0.99098, 2, 16, 18.89, -17.32, 0.09978, 17, 10.5, -12.91, 0.90022, 2, 16, 13.83, -14.47, 0.42223, 17, 4.7, -13.3, 0.57777, 2, 16, 11.13, -18.1, 0.57341, 17, 4.44, -17.82, 0.42659, 2, 16, 5.84, -13.63, 0.74657, 17, -2.43, -17, 0.25343, 2, 16, 0.55, -9.16, 0.95062, 17, -9.31, -16.17, 0.04938, 2, 16, -3.19, -6, 0.99453, 17, -14.17, -15.59, 0.00547, 1, 16, -2.38, 6.57, 1, 3, 16, 15.44, 4.16, 0.82184, 17, -4.2, 3.14, 0.1781, 18, -20.13, -2.91, 6e-05, 3, 16, 25.65, -0.32, 0.00159, 17, 6.8, 5.02, 0.89218, 18, -9.85, 1.42, 0.10623, 2, 16, 10.23, -2.46, 0.94804, 17, -4.91, -5.25, 0.05196, 2, 16, 16.73, -4.58, 0.24089, 17, 1.69, -3.45, 0.75911, 2, 16, 8.57, -9.48, 0.75293, 17, -2.43, -12.03, 0.24707, 2, 16, 19.5, -13.27, 0.12241, 17, 8.78, -9.19, 0.87759, 2, 17, 13.5, 1.02, 0.65294, 18, -2.41, -0.94, 0.34706, 1, 18, 7.08, 0.3, 1, 2, 16, 25.5, -17.52, 0.00917, 17, 16.13, -9.43, 0.99083], "hull": 34, "edges": [0, 66, 0, 2, 12, 14, 24, 26, 26, 28, 28, 30, 56, 58, 64, 66, 62, 64, 58, 60, 60, 62, 52, 54, 54, 56, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 36, 38, 34, 36, 30, 32, 32, 34, 18, 20, 14, 16, 16, 18, 8, 10, 10, 12, 6, 8, 2, 4, 4, 6, 44, 46, 46, 48, 20, 22, 22, 24], "width": 78, "height": 56}}, "txj1": {"j1": {"type": "mesh", "uvs": [0.86979, 0.02349, 0.95704, 0.12164, 0.95705, 0.21134, 0.87053, 0.22846, 0.8704, 0.28128, 0.87022, 0.36101, 0.89588, 0.43613, 0.92469, 0.52049, 0.90173, 0.60426, 0.88108, 0.67962, 0.85061, 0.79078, 0.82587, 0.88107, 0.65229, 0.97596, 0.2604, 0.97622, 0.26042, 0.88666, 0.38969, 0.78756, 0.26527, 0.70247, 0.1559, 0.62767, 0.08719, 0.58068, 0.01559, 0.53171, 0.04864, 0.44847, 0.09296, 0.3368, 0.13145, 0.23985, 0.17769, 0.12337, 0.23547, 0.04851, 0.43955, 0.04843, 0.66564, 0.02249], "triangles": [1, 26, 0, 23, 24, 25, 4, 25, 26, 17, 18, 20, 19, 20, 18, 7, 17, 6, 7, 16, 17, 6, 17, 20, 7, 8, 16, 9, 16, 8, 15, 16, 9, 10, 15, 9, 11, 15, 10, 12, 15, 11, 14, 15, 12, 13, 14, 12, 3, 1, 2, 1, 3, 26, 26, 3, 4, 25, 22, 23, 22, 25, 4, 22, 5, 21, 4, 5, 22, 6, 21, 5, 21, 6, 20], "vertices": [1, 3, 24.04, 0.43, 1, 1, 4, 1.19, 5.91, 1, 2, 4, 4.5, 7.16, 0.99889, 5, -7.87, 13.84, 0.00111, 2, 4, 5.81, 5.61, 0.99018, 5, -7.74, 11.82, 0.00982, 2, 4, 7.75, 6.35, 0.94922, 5, -5.74, 11.25, 0.05078, 2, 4, 10.69, 7.46, 0.80058, 5, -2.72, 10.39, 0.19942, 2, 4, 13.26, 9.05, 0.55182, 5, 0.29, 10.13, 0.44818, 2, 4, 16.14, 10.82, 0.29468, 5, 3.66, 9.84, 0.70532, 2, 4, 19.41, 11.52, 0.11688, 5, 6.7, 8.46, 0.88312, 2, 4, 22.35, 12.15, 0.02888, 5, 9.44, 7.21, 0.97112, 2, 4, 26.69, 13.08, 0.00013, 5, 13.47, 5.37, 0.99987, 1, 5, 16.75, 3.88, 1, 1, 5, 19.3, -0.83, 1, 1, 5, 16.96, -9.18, 1, 1, 5, 13.56, -8.22, 1, 1, 5, 10.58, -4.41, 1, 2, 4, 28.03, -0.26, 0.01266, 5, 6.6, -6.14, 0.98734, 2, 4, 26.13, -3.57, 0.15467, 5, 3.11, -7.67, 0.84533, 2, 4, 24.94, -5.65, 0.32922, 5, 0.91, -8.63, 0.67078, 2, 4, 23.7, -7.81, 0.47418, 5, -1.38, -9.63, 0.52582, 2, 4, 20.37, -8.3, 0.69297, 5, -4.34, -8.03, 0.30703, 2, 4, 15.91, -8.94, 0.95844, 5, -8.31, -5.89, 0.04156, 2, 4, 12.03, -9.51, 0.99983, 5, -11.75, -4.03, 0.00017, 1, 4, 7.38, -10.18, 1, 2, 4, 4.16, -10.03, 0.04, 3, 21.18, -13.34, 0.96, 1, 3, 22.4, -8.99, 1, 1, 3, 22.78, -3.9, 1], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 22, 24, 24, 26, 26, 28, 28, 30, 46, 48, 48, 50, 50, 52, 20, 22, 18, 20, 14, 16, 16, 18, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 10, 12, 12, 14, 6, 8, 8, 10, 34, 36, 36, 38], "width": 23, "height": 41}}, "txps3": {"ps2": {"type": "mesh", "uvs": [0.35982, 0.33484, 0.37843, 0.40216, 0.39719, 0.47002, 0.41459, 0.53294, 0.37717, 0.53573, 0.34812, 0.48161, 0.32042, 0.42997, 0.33555, 0.52701, 0.35172, 0.63067, 0.36817, 0.73614, 0.38255, 0.82838, 0.39753, 0.92438, 0.35586, 0.89257, 0.29202, 0.8438, 0.23352, 0.79913, 0.243, 0.8673, 0.25131, 0.92714, 0.25938, 0.98515, 0.2211, 0.98522, 0.18732, 0.98527, 0.1631, 0.93035, 0.13886, 0.87539, 0.11119, 0.81266, 0.05279, 0.81349, 0.0137, 0.76447, 0.0137, 0.68517, 0.01369, 0.59939, 0.01369, 0.50217, 0.01368, 0.4106, 0.03622, 0.33051, 0.04789, 0.28901, 0.06692, 0.2214, 0.12508, 0.15548, 0.18056, 0.15607, 0.18165, 0.20312, 0.28099, 0.20263, 0.33563, 0.24733], "triangles": [19, 20, 18, 18, 16, 17, 18, 20, 16, 20, 15, 16, 20, 21, 15, 21, 14, 15, 21, 22, 14, 12, 10, 11, 12, 13, 10, 10, 13, 9, 13, 14, 9, 23, 24, 22, 14, 22, 25, 8, 9, 14, 8, 14, 25, 8, 25, 7, 24, 25, 22, 6, 25, 26, 6, 26, 27, 7, 25, 6, 6, 28, 29, 6, 29, 34, 29, 30, 34, 34, 35, 6, 4, 2, 3, 4, 5, 2, 6, 27, 28, 2, 5, 1, 5, 6, 1, 6, 0, 1, 0, 35, 36, 0, 6, 35, 30, 31, 34, 34, 32, 33, 34, 31, 32], "vertices": [2, 32, 2.23, 11.34, 0.99986, 33, -11.54, 15.38, 0.00014, 2, 32, 5.97, 13.55, 0.99637, 33, -7.33, 16.41, 0.00363, 2, 32, 9.73, 15.77, 0.99971, 33, -3.08, 17.44, 0.00029, 1, 32, 13.22, 17.83, 1, 1, 32, 13.99, 15.35, 1, 2, 32, 11.21, 12.63, 0.99721, 33, -2.58, 14.01, 0.00279, 2, 32, 8.56, 10.03, 0.94999, 33, -5.87, 12.29, 0.05001, 3, 32, 14.13, 12.43, 0.57005, 33, 0.15, 12.97, 0.42596, 34, -11.29, 16.11, 0.00398, 3, 32, 20.08, 14.98, 0.2027, 33, 6.59, 13.68, 0.73159, 34, -4.87, 15.26, 0.06571, 3, 32, 26.13, 17.59, 0.03753, 33, 13.14, 14.42, 0.73494, 34, 1.66, 14.39, 0.22753, 3, 32, 31.43, 19.86, 0.00216, 33, 18.87, 15.06, 0.67498, 34, 7.37, 13.63, 0.32286, 3, 32, 36.94, 22.23, 0, 33, 24.83, 15.72, 0.66246, 34, 13.32, 12.84, 0.33754, 2, 33, 22.69, 12.97, 0.66386, 34, 10.58, 10.68, 0.33614, 3, 32, 33.79, 13.98, 0.00121, 33, 19.42, 8.74, 0.64242, 34, 6.39, 7.37, 0.35637, 3, 32, 32.05, 9.41, 0.00193, 33, 16.43, 4.87, 0.37576, 34, 2.55, 4.33, 0.62231, 2, 33, 20.65, 5.26, 0.01007, 34, 6.75, 3.7, 0.98993, 1, 34, 10.43, 3.14, 1, 1, 34, 14.01, 2.59, 1, 1, 34, 13.21, 0.06, 1, 1, 34, 12.51, -2.17, 1, 1, 34, 8.78, -2.75, 1, 1, 34, 5.05, -3.33, 1, 2, 33, 16.73, -3.63, 0.26969, 34, 0.79, -3.99, 0.73031, 2, 33, 16.53, -7.67, 0.65625, 34, -0.37, -7.86, 0.34375, 2, 33, 13.35, -10.18, 0.80587, 34, -4.07, -9.54, 0.19413, 3, 32, 28.73, -7.02, 0, 33, 8.48, -9.88, 0.94917, 34, -8.72, -8.07, 0.05083, 3, 32, 23.59, -8.23, 0.05305, 33, 3.21, -9.55, 0.94605, 34, -13.75, -6.48, 0.0009, 2, 32, 17.77, -9.61, 0.43348, 33, -2.76, -9.18, 0.56652, 2, 32, 12.29, -10.91, 0.82698, 33, -8.39, -8.83, 0.17302, 2, 32, 7.13, -10.52, 0.97287, 33, -13.21, -6.96, 0.02713, 2, 32, 4.46, -10.33, 0.99505, 33, -15.71, -6, 0.00495, 1, 32, 0.11, -10, 1, 1, 32, -4.77, -7.02, 1, 1, 32, -5.62, -3.27, 1, 1, 32, -2.82, -2.53, 1, 1, 32, -4.43, 4.15, 1, 1, 32, -2.62, 8.47, 1], "hull": 37, "edges": [6, 8, 44, 46, 46, 48, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 34, 36, 36, 38, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 72, "height": 64}}, "tw1": {"w1": {"type": "mesh", "uvs": [0.43521, 0.03906, 0.49721, 0.06631, 0.52795, 0.06132, 0.65134, 0.03075, 0.74599, 0.08332, 0.8611, 0.14725, 0.90499, 0.22934, 0.94291, 0.30027, 0.99517, 0.39803, 0.99075, 0.51664, 0.98524, 0.66454, 0.97273, 0.78297, 0.95952, 0.9079, 0.89528, 0.98574, 0.79223, 1, 0.72504, 1, 0.58115, 0.93623, 0.56269, 0.80859, 0.60501, 0.70275, 0.71388, 0.63452, 0.76486, 0.5631, 0.75009, 0.45978, 0.74009, 0.38982, 0.7151, 0.32746, 0.62472, 0.33855, 0.56237, 0.3462, 0.47247, 0.27013, 0.46283, 0.22829, 0.34779, 0.19322, 0.26326, 0.16745, 0.17438, 0.19952, 0.11256, 0.25116, 0.09004, 0.32659, 0.11315, 0.4789, 0.09111, 0.49815, 0.0313, 0.41387, 0, 0.30676, 0, 0.25524, 0.00866, 0.1237, 0.08028, 0.07045, 0.16866, 0.00474, 0.26713, 0.00228, 0.34702, 0.00029], "triangles": [34, 35, 33, 35, 32, 33, 35, 36, 32, 31, 32, 37, 32, 36, 37, 37, 38, 31, 31, 38, 30, 38, 39, 30, 30, 39, 29, 28, 29, 42, 29, 41, 42, 39, 40, 29, 29, 40, 41, 24, 25, 26, 2, 27, 1, 24, 26, 2, 4, 24, 2, 4, 2, 3, 2, 26, 27, 27, 28, 1, 28, 0, 1, 28, 42, 0, 22, 7, 8, 22, 23, 7, 23, 24, 4, 23, 6, 7, 23, 5, 6, 23, 4, 5, 10, 20, 9, 20, 21, 9, 9, 21, 8, 21, 22, 8, 13, 14, 12, 12, 14, 15, 12, 15, 16, 18, 12, 17, 11, 12, 18, 12, 16, 17, 11, 18, 19, 11, 19, 10, 10, 19, 20], "vertices": [2, 64, 77.57, -31.44, 0.8259, 65, -44.3, -19.48, 0.1741, 3, 63, 163.14, 33.02, 0.01147, 64, 55.27, -29.62, 0.984, 65, -60.5, -4.05, 0.00453, 2, 63, 159.12, 23.81, 0.04034, 64, 46.86, -35.11, 0.95966, 2, 63, 146.27, -15.38, 0.30316, 64, 14.6, -60.81, 0.69684, 2, 63, 112.91, -29.44, 0.72709, 64, -21, -54.24, 0.27291, 1, 63, 72.34, -46.54, 1, 1, 63, 39.03, -40.91, 1, 2, 62, 117.38, -4.74, 0.11678, 63, 10.24, -36.05, 0.88322, 2, 62, 86.21, -30.19, 0.89161, 63, -29.43, -29.34, 0.10839, 2, 61, 170.51, -14.27, 0.02366, 62, 42.92, -39.98, 0.97634, 2, 61, 124.31, -44.75, 0.67394, 62, -11.06, -52.19, 0.32606, 2, 61, 85.82, -67.04, 0.98991, 62, -54.95, -59.44, 0.01009, 1, 61, 45.22, -90.55, 1, 1, 61, 9.53, -90.51, 1, 1, 61, -13.93, -66.56, 1, 1, 61, -26.39, -48.94, 1, 1, 61, -33.58, 2.56, 1, 1, 61, 1.98, 34.96, 1, 2, 61, 42.15, 46.71, 0.99918, 62, -55.63, 62.4, 0.00082, 2, 61, 83.18, 32.89, 0.7984, 62, -22.13, 34.98, 0.2016, 3, 61, 114.44, 34.95, 0.08673, 62, 7.85, 25.86, 0.89905, 63, -38.84, 66.55, 0.01422, 2, 62, 44.05, 40.17, 0.65294, 63, -4.22, 48.72, 0.34706, 3, 62, 68.57, 49.87, 0.17858, 63, 19.22, 36.64, 0.80861, 64, -63.02, 52.44, 0.01282, 3, 62, 89.13, 63.5, 0.00625, 63, 43.01, 30.15, 0.80676, 64, -46.7, 33.95, 0.18699, 2, 63, 55.92, 56.48, 0.26333, 64, -21.45, 48.86, 0.73667, 2, 63, 64.82, 74.64, 0.10091, 64, -4.04, 59.15, 0.89909, 2, 64, 33.51, 43.87, 0.99994, 65, -31.23, 66.78, 6e-05, 2, 64, 42.35, 30.59, 0.99041, 65, -32.7, 50.9, 0.00959, 2, 64, 81.5, 32.57, 0.45214, 65, -1.01, 27.83, 0.54786, 2, 64, 110.27, 34.03, 0.00771, 65, 22.28, 10.87, 0.99229, 1, 65, 53.06, 14.27, 1, 1, 65, 77.58, 27.15, 1, 1, 65, 92.53, 52.14, 1, 1, 65, 101.6, 108.88, 1, 1, 65, 110.43, 113.77, 1, 1, 65, 119.89, 78.08, 1, 1, 65, 118.15, 36.82, 1, 1, 65, 112.68, 18.34, 1, 1, 65, 96.03, -28.04, 1, 1, 65, 68.32, -40.61, 1, 1, 65, 34.12, -56.11, 1, 2, 64, 132.71, -23.55, 0.0664, 65, 3.54, -48.01, 0.9336, 2, 64, 109.28, -34.03, 0.31097, 65, -21.27, -41.43, 0.68903], "hull": 43, "edges": [2, 4, 4, 6, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 50, 52, 52, 54, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 20, 22, 22, 24, 16, 18, 18, 20, 40, 42, 42, 44, 14, 16, 10, 12, 12, 14, 6, 8, 8, 10, 46, 48, 48, 50, 2, 0, 0, 84, 54, 56, 56, 58, 80, 82, 82, 84, 76, 78, 78, 80], "width": 217, "height": 252}}, "txps5": {"ps2": {"type": "mesh", "uvs": [0.35982, 0.33484, 0.37843, 0.40216, 0.39719, 0.47002, 0.41459, 0.53294, 0.37717, 0.53573, 0.34812, 0.48161, 0.32042, 0.42997, 0.33555, 0.52701, 0.35172, 0.63067, 0.36817, 0.73614, 0.38255, 0.82838, 0.39753, 0.92438, 0.35586, 0.89257, 0.29202, 0.8438, 0.23352, 0.79913, 0.243, 0.8673, 0.25131, 0.92714, 0.25938, 0.98515, 0.2211, 0.98522, 0.18732, 0.98527, 0.1631, 0.93035, 0.13886, 0.87539, 0.11119, 0.81266, 0.05279, 0.81349, 0.0137, 0.76447, 0.0137, 0.68517, 0.01369, 0.59939, 0.01369, 0.50217, 0.01368, 0.4106, 0.03622, 0.33051, 0.04789, 0.28901, 0.06692, 0.2214, 0.12508, 0.15548, 0.18056, 0.15607, 0.18165, 0.20312, 0.28099, 0.20263, 0.33563, 0.24733], "triangles": [19, 20, 18, 18, 16, 17, 18, 20, 16, 20, 15, 16, 20, 21, 15, 21, 14, 15, 21, 22, 14, 12, 10, 11, 12, 13, 10, 10, 13, 9, 13, 14, 9, 23, 24, 22, 14, 22, 25, 8, 9, 14, 8, 14, 25, 8, 25, 7, 24, 25, 22, 6, 25, 26, 6, 26, 27, 7, 25, 6, 6, 28, 29, 6, 29, 34, 29, 30, 34, 34, 35, 6, 4, 2, 3, 4, 5, 2, 6, 27, 28, 2, 5, 1, 5, 6, 1, 6, 0, 1, 0, 35, 36, 0, 6, 35, 30, 31, 34, 34, 32, 33, 34, 31, 32], "vertices": [2, 49, 2.23, 11.34, 0.99986, 50, -11.54, 15.38, 0.00014, 2, 49, 5.97, 13.55, 0.99637, 50, -7.33, 16.41, 0.00363, 2, 49, 9.73, 15.77, 0.99971, 50, -3.08, 17.44, 0.00029, 1, 49, 13.22, 17.83, 1, 1, 49, 13.99, 15.35, 1, 2, 49, 11.21, 12.63, 0.99721, 50, -2.58, 14.01, 0.00279, 2, 49, 8.56, 10.03, 0.94999, 50, -5.87, 12.29, 0.05001, 3, 49, 14.13, 12.43, 0.57005, 50, 0.15, 12.97, 0.42596, 51, -11.29, 16.11, 0.00398, 3, 49, 20.08, 14.98, 0.2027, 50, 6.59, 13.68, 0.73159, 51, -4.87, 15.26, 0.06571, 3, 49, 26.13, 17.59, 0.03753, 50, 13.14, 14.42, 0.73494, 51, 1.66, 14.39, 0.22753, 3, 49, 31.43, 19.86, 0.00216, 50, 18.87, 15.06, 0.67498, 51, 7.37, 13.63, 0.32286, 3, 49, 36.94, 22.23, 0, 50, 24.83, 15.72, 0.66246, 51, 13.32, 12.84, 0.33754, 2, 50, 22.69, 12.97, 0.66386, 51, 10.58, 10.68, 0.33614, 3, 49, 33.79, 13.98, 0.00121, 50, 19.42, 8.74, 0.64242, 51, 6.39, 7.37, 0.35637, 3, 49, 32.05, 9.41, 0.00193, 50, 16.43, 4.87, 0.37576, 51, 2.55, 4.33, 0.62231, 2, 50, 20.65, 5.26, 0.01007, 51, 6.75, 3.7, 0.98993, 1, 51, 10.43, 3.14, 1, 1, 51, 14.01, 2.59, 1, 1, 51, 13.21, 0.06, 1, 1, 51, 12.51, -2.17, 1, 1, 51, 8.78, -2.75, 1, 1, 51, 5.05, -3.33, 1, 2, 50, 16.73, -3.63, 0.26969, 51, 0.79, -3.99, 0.73031, 2, 50, 16.53, -7.67, 0.65625, 51, -0.37, -7.86, 0.34375, 2, 50, 13.35, -10.18, 0.80587, 51, -4.07, -9.54, 0.19413, 3, 49, 28.73, -7.02, 0, 50, 8.48, -9.88, 0.94917, 51, -8.72, -8.07, 0.05083, 3, 49, 23.59, -8.23, 0.05305, 50, 3.21, -9.55, 0.94605, 51, -13.75, -6.48, 0.0009, 2, 49, 17.77, -9.61, 0.43348, 50, -2.76, -9.18, 0.56652, 2, 49, 12.29, -10.91, 0.82698, 50, -8.39, -8.83, 0.17302, 2, 49, 7.13, -10.52, 0.97287, 50, -13.21, -6.96, 0.02713, 2, 49, 4.46, -10.33, 0.99505, 50, -15.71, -6, 0.00495, 1, 49, 0.11, -10, 1, 1, 49, -4.77, -7.02, 1, 1, 49, -5.62, -3.27, 1, 1, 49, -2.82, -2.53, 1, 1, 49, -4.43, 4.15, 1, 1, 49, -2.62, 8.47, 1], "hull": 37, "edges": [6, 8, 44, 46, 46, 48, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 34, 36, 36, 38, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 72, "height": 64}}, "txps6": {"ps1": {"type": "mesh", "uvs": [0.79524, 0.03528, 0.87329, 0.14478, 0.90309, 0.22841, 0.93092, 0.30652, 0.965, 0.40216, 0.98026, 0.52965, 0.99449, 0.64854, 0.97242, 0.74131, 0.92987, 0.73117, 0.89763, 0.72349, 0.89762, 0.81796, 0.89762, 0.8713, 0.89761, 0.92808, 0.85278, 0.99282, 0.82021, 0.94647, 0.82007, 0.90666, 0.81077, 0.81715, 0.80355, 0.74765, 0.79341, 0.6501, 0.78135, 0.53397, 0.76698, 0.65912, 0.75285, 0.78222, 0.73984, 0.89555, 0.71435, 0.89462, 0.67938, 0.89334, 0.67892, 0.81422, 0.67843, 0.7306, 0.66369, 0.62238, 0.64926, 0.5164, 0.58904, 0.52155, 0.58902, 0.39309, 0.589, 0.26434, 0.58899, 0.17354, 0.72398, 0.03427, 0.85282, 0.31584, 0.89508, 0.51448, 0.74058, 0.32136, 0.77491, 0.43907, 0.65475, 0.38205, 0.71021, 0.58254, 0.85282, 0.64691, 0.85532, 0.82477, 0.7186, 0.71848], "triangles": [13, 14, 12, 14, 15, 12, 12, 15, 11, 15, 41, 11, 15, 16, 41, 11, 41, 10, 9, 10, 41, 17, 9, 41, 17, 41, 16, 17, 40, 9, 7, 8, 6, 8, 9, 6, 9, 40, 6, 22, 23, 21, 21, 23, 25, 23, 24, 25, 25, 42, 21, 25, 26, 42, 21, 42, 20, 17, 18, 40, 26, 27, 42, 6, 35, 5, 35, 6, 40, 27, 39, 42, 42, 39, 20, 20, 39, 19, 18, 19, 40, 40, 19, 35, 27, 28, 39, 19, 39, 37, 39, 28, 37, 19, 37, 35, 35, 4, 5, 28, 38, 37, 37, 34, 35, 4, 35, 34, 29, 30, 28, 28, 30, 38, 4, 34, 3, 38, 36, 37, 37, 36, 34, 30, 31, 38, 38, 31, 36, 36, 32, 33, 34, 36, 1, 36, 33, 0, 34, 2, 3, 34, 1, 2, 1, 36, 0, 36, 31, 32], "vertices": [1, 52, 1.11, 10.62, 1, 2, 52, 9.39, 11.28, 0.99581, 53, -13.16, 5.77, 0.00419, 2, 52, 14.27, 10.08, 0.89319, 53, -8.43, 7.45, 0.10681, 3, 52, 18.83, 8.96, 0.55375, 53, -4, 9.02, 0.44176, 54, -21.28, 2.86, 0.0045, 3, 52, 24.42, 7.59, 0.13811, 53, 1.42, 10.94, 0.80824, 54, -16.44, 5.97, 0.05365, 3, 52, 30.4, 4.03, 0.00252, 53, 8.37, 11.26, 0.69409, 54, -9.74, 7.86, 0.30338, 2, 53, 14.85, 11.56, 0.26442, 54, -3.5, 9.63, 0.73558, 2, 53, 19.61, 9.32, 0.07451, 54, 1.65, 8.54, 0.92549, 2, 53, 18.69, 6.22, 0.05472, 54, 1.46, 5.3, 0.94528, 2, 53, 17.99, 3.87, 0.01219, 54, 1.31, 2.86, 0.98781, 1, 54, 6.37, 3.42, 1, 1, 54, 9.22, 3.73, 1, 1, 54, 12.26, 4.07, 1, 1, 54, 16.1, 1.11, 1, 1, 54, 13.89, -1.59, 1, 1, 54, 11.76, -1.84, 1, 2, 53, 22.22, -3.2, 0.00161, 54, 7.04, -3.06, 0.99839, 2, 53, 18.44, -3.3, 0.0989, 54, 3.39, -4.01, 0.9011, 2, 53, 13.14, -3.42, 0.86126, 54, -1.75, -5.35, 0.13874, 2, 52, 20.95, -7.51, 0.05057, 53, 6.82, -3.58, 0.94943, 2, 52, 25.4, -12.68, 0.0284, 53, 13.38, -5.45, 0.9716, 2, 52, 29.78, -17.77, 0.00023, 53, 19.84, -7.29, 0.99977, 1, 53, 25.78, -8.99, 1, 1, 53, 25.5, -10.88, 1, 1, 53, 25.12, -13.48, 1, 1, 53, 20.89, -13, 1, 2, 52, 24.05, -20.24, 0.00902, 53, 16.41, -12.5, 0.99098, 2, 52, 18.89, -17.32, 0.09978, 53, 10.5, -12.91, 0.90022, 2, 52, 13.83, -14.47, 0.42223, 53, 4.7, -13.3, 0.57777, 2, 52, 11.13, -18.1, 0.57341, 53, 4.44, -17.82, 0.42659, 2, 52, 5.84, -13.63, 0.74657, 53, -2.43, -17, 0.25343, 2, 52, 0.55, -9.16, 0.95062, 53, -9.31, -16.17, 0.04938, 2, 52, -3.19, -6, 0.99453, 53, -14.17, -15.59, 0.00547, 1, 52, -2.38, 6.57, 1, 3, 52, 15.44, 4.16, 0.82184, 53, -4.2, 3.14, 0.1781, 54, -20.13, -2.91, 6e-05, 3, 52, 25.65, -0.32, 0.00159, 53, 6.8, 5.02, 0.89218, 54, -9.85, 1.42, 0.10623, 2, 52, 10.23, -2.46, 0.94804, 53, -4.91, -5.25, 0.05196, 2, 52, 16.73, -4.58, 0.24089, 53, 1.69, -3.45, 0.75911, 2, 52, 8.57, -9.48, 0.75293, 53, -2.43, -12.03, 0.24707, 2, 52, 19.5, -13.27, 0.12241, 53, 8.78, -9.19, 0.87759, 2, 53, 13.5, 1.02, 0.65294, 54, -2.41, -0.94, 0.34706, 1, 54, 7.08, 0.3, 1, 2, 52, 25.5, -17.52, 0.00917, 53, 16.13, -9.43, 0.99083], "hull": 34, "edges": [0, 66, 0, 2, 12, 14, 24, 26, 26, 28, 28, 30, 56, 58, 64, 66, 62, 64, 58, 60, 60, 62, 52, 54, 54, 56, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 36, 38, 34, 36, 30, 32, 32, 34, 18, 20, 14, 16, 16, 18, 8, 10, 10, 12, 6, 8, 2, 4, 4, 6, 44, 46, 46, 48, 20, 22, 22, 24], "width": 78, "height": 56}}, "txj6": {"j2": {"type": "mesh", "uvs": [0.44312, 0.01315, 0.79232, 0.17297, 0.88609, 0.2759, 0.96793, 0.36572, 0.96752, 0.46751, 0.96732, 0.51851, 0.96697, 0.60643, 0.9084, 0.65631, 0.84744, 0.70824, 0.90718, 0.78046, 0.96853, 0.85462, 0.96663, 0.97617, 0.79904, 0.97597, 0.64879, 0.93896, 0.50028, 0.8313, 0.47183, 0.76191, 0.4429, 0.69133, 0.40029, 0.58738, 0.36632, 0.50451, 0.36424, 0.43939, 0.23373, 0.40333, 0.09841, 0.36593, 0, 0.21913, 0, 0.20154, 0.33143, 0.09779], "triangles": [22, 23, 24, 1, 24, 0, 21, 22, 24, 4, 17, 18, 5, 17, 4, 5, 7, 17, 6, 7, 5, 8, 16, 17, 7, 8, 17, 15, 16, 8, 14, 15, 8, 13, 14, 8, 13, 8, 9, 12, 13, 9, 10, 12, 9, 11, 12, 10, 1, 19, 24, 19, 1, 2, 20, 24, 19, 4, 2, 3, 19, 2, 4, 18, 19, 4, 20, 21, 24], "vertices": [1, 39, 19.46, 14.01, 1, 1, 42, 13.16, 9.71, 1, 1, 42, 17.69, 7.92, 1, 2, 42, 21.65, 6.35, 0.96142, 43, -3.63, 7.16, 0.03858, 2, 42, 23.92, 3.05, 0.67985, 43, 0.38, 7.27, 0.32015, 2, 42, 25.07, 1.39, 0.45832, 43, 2.39, 7.33, 0.54168, 2, 42, 27.03, -1.46, 0.20181, 43, 5.86, 7.42, 0.79819, 2, 42, 26.76, -4.04, 0.0934, 43, 7.87, 5.8, 0.9066, 2, 42, 26.48, -6.73, 0.01525, 43, 9.97, 4.1, 0.98475, 2, 42, 29.52, -8.09, 2e-05, 43, 12.77, 5.91, 0.99998, 1, 43, 15.63, 7.77, 1, 1, 43, 20.43, 7.86, 1, 1, 43, 20.57, 3.03, 1, 1, 43, 19.24, -1.35, 1, 1, 43, 15.13, -5.76, 1, 2, 42, 18.78, -14.64, 0.00185, 43, 12.42, -6.66, 0.99815, 2, 42, 16.51, -12.83, 0.02826, 43, 9.66, -7.58, 0.97174, 2, 42, 13.17, -10.16, 0.20535, 43, 5.61, -8.94, 0.79465, 2, 42, 10.5, -8.03, 0.49256, 43, 2.37, -10.02, 0.50744, 2, 42, 8.99, -5.96, 0.78513, 43, -0.19, -10.16, 0.21487, 2, 42, 5.09, -6.93, 0.97898, 43, -1.5, -13.96, 0.02102, 3, 42, 1.04, -7.95, 0.95187, 43, -2.85, -17.91, 0.00013, 39, 30.14, 0.66, 0.048, 2, 42, -4.59, -4.81, 0.248, 39, 23.8, -0.5, 0.752, 1, 39, 23.13, -0.31, 1, 1, 39, 21.79, 10, 1], "hull": 25, "edges": [0, 48, 0, 2, 20, 22, 22, 24, 24, 26, 26, 28, 36, 38, 42, 44, 44, 46, 46, 48, 34, 36, 32, 34, 28, 30, 30, 32, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 12, 14, 14, 16, 2, 4, 4, 6, 38, 40, 40, 42], "width": 30, "height": 41}}, "c1": {"c1": {"type": "mesh", "uvs": [0.6162, 0.01197, 0.72393, 0.11323, 0.83988, 0.2222, 0.93766, 0.31409, 0.99348, 0.40106, 0.97169, 0.51065, 0.87699, 0.58277, 0.77288, 0.66207, 0.69309, 0.69506, 0.58581, 0.73943, 0.49913, 0.70576, 0.43392, 0.8235, 0.36811, 0.94232, 0.22392, 0.92535, 0.10068, 0.91084, 0.0193, 1, 0.00393, 1, 0.01433, 0.8984, 0.05757, 0.74982, 0.09334, 0.62693, 0.14467, 0.45055, 0.18229, 0.32129, 0.31106, 0.17861, 0.41062, 0.0683, 0.476, 0.01455, 0.81657, 0.42741, 0.61532, 0.32083, 0.38663, 0.27775, 0.47079, 0.53399, 0.23681, 0.61335], "triangles": [5, 25, 4, 6, 25, 5, 29, 13, 18, 13, 14, 18, 16, 17, 15, 15, 17, 14, 29, 18, 19, 20, 21, 29, 27, 21, 22, 27, 29, 21, 27, 23, 26, 27, 22, 23, 19, 20, 29, 17, 18, 14, 12, 13, 11, 11, 13, 29, 11, 29, 10, 8, 9, 28, 29, 28, 10, 9, 10, 28, 8, 28, 26, 28, 27, 26, 29, 27, 28, 23, 24, 26, 26, 1, 2, 26, 0, 1, 26, 24, 0, 25, 7, 8, 7, 25, 6, 25, 8, 26, 4, 25, 3, 25, 2, 3, 25, 26, 2], "vertices": [4, 93, 37.67, -13.36, 0.01355, 94, 17.92, -16.17, 0.5769, 95, -3.7, -16.75, 0.40901, 96, -21.53, -10.52, 0.00054, 3, 93, 26.42, -11.31, 0.17226, 94, 6.85, -13.3, 0.77464, 95, -13.1, -10.22, 0.0531, 2, 93, 14.3, -9.1, 0.8713, 94, -5.07, -10.2, 0.1287, 1, 93, 4.09, -7.24, 1, 2, 93, -2.81, -4.07, 0.2, 92, 25.95, 2.27, 0.8, 3, 93, -4.54, 3.44, 0.26714, 97, -20.85, 21.56, 0.00486, 92, 18.32, 3.29, 0.728, 5, 93, 0.39, 11.45, 0.17977, 94, -17.44, 11.31, 0.0002, 97, -11.46, 21.25, 0.05031, 98, -9.31, 32.04, 0.00171, 92, 12.55, 10.72, 0.768, 4, 93, 5.8, 20.26, 0.37277, 94, -11.39, 19.7, 0.00114, 97, -1.12, 20.92, 0.55882, 98, -1.17, 25.66, 0.06726, 3, 93, 10.81, 25.31, 0.17269, 97, 5.76, 19.09, 0.64367, 98, 3.3, 20.12, 0.18364, 3, 93, 17.54, 32.12, 0.04434, 97, 15, 16.62, 0.4653, 98, 9.3, 12.66, 0.49036, 3, 93, 25.13, 33.38, 0.00565, 97, 19.86, 10.66, 0.11112, 98, 9.7, 4.99, 0.88323, 2, 98, 19.15, 2.61, 0.98012, 96, 8.63, 38.45, 0.01988, 2, 98, 28.7, 0.22, 0.95408, 96, 16.23, 44.7, 0.04592, 2, 98, 31.86, -11.62, 0.51917, 96, 27.63, 40.21, 0.48083, 2, 98, 34.56, -21.74, 0.33769, 96, 37.38, 36.37, 0.66231, 2, 98, 42.66, -26.08, 0.21082, 96, 45.68, 40.31, 0.78918, 2, 98, 43.12, -27.3, 0.17885, 96, 46.93, 39.95, 0.82115, 2, 98, 36.31, -28.89, 0.26337, 96, 44.17, 33.53, 0.73663, 2, 98, 25.52, -28.99, 0.2224, 96, 37.84, 24.8, 0.7776, 2, 98, 16.61, -29.07, 0.2011, 96, 32.61, 17.57, 0.7989, 2, 98, 3.81, -29.2, 0.01736, 96, 25.1, 7.21, 0.98264, 1, 96, 19.6, -0.39, 1, 3, 94, 37.38, 4.26, 4e-05, 95, 21.62, -4.29, 0.01339, 96, 6.43, -6.74, 0.98657, 3, 94, 32.5, -5.93, 0.07103, 95, 13.52, -12.17, 0.61696, 96, -3.75, -11.64, 0.31201, 3, 94, 28.8, -11.45, 0.2005, 95, 8.15, -16.07, 0.70927, 96, -10.08, -13.63, 0.09023, 4, 93, 9.74, 4.29, 0.85338, 94, -8.64, 3.49, 0.04069, 97, -13.03, 9.58, 0.10497, 98, -17.47, 23.55, 0.00096, 3, 94, 9.88, 3.32, 0.67522, 97, -2.82, -5.87, 0.24752, 95, -4.51, 4.32, 0.07726, 4, 97, 11.72, -18.98, 0.01716, 95, 14.94, 2.2, 0.16225, 98, -14.38, -14.11, 0.00794, 96, 2.16, 1.54, 0.81265, 4, 97, 15.4, -0.45, 0.10026, 95, 7.09, 19.38, 0.0131, 98, -0.46, -1.34, 0.87083, 96, 0.16, 20.32, 0.01581, 3, 95, 26.64, 25.63, 0.00083, 98, 11.51, -18.02, 0.52083, 96, 20.69, 20.04, 0.47833], "hull": 25, "edges": [0, 48, 6, 8, 8, 10, 18, 20, 28, 30, 30, 32, 32, 34, 46, 48, 4, 6, 0, 2, 2, 4, 42, 44, 44, 46, 40, 42, 38, 40, 34, 36, 36, 38, 24, 26, 26, 28, 20, 22, 22, 24, 14, 16, 16, 18, 10, 12, 12, 14], "width": 88, "height": 71}}, "txps4": {"ps1": {"type": "mesh", "uvs": [0.79524, 0.03528, 0.87329, 0.14478, 0.90309, 0.22841, 0.93092, 0.30652, 0.965, 0.40216, 0.98026, 0.52965, 0.99449, 0.64854, 0.97242, 0.74131, 0.92987, 0.73117, 0.89763, 0.72349, 0.89762, 0.81796, 0.89762, 0.8713, 0.89761, 0.92808, 0.85278, 0.99282, 0.82021, 0.94647, 0.82007, 0.90666, 0.81077, 0.81715, 0.80355, 0.74765, 0.79341, 0.6501, 0.78135, 0.53397, 0.76698, 0.65912, 0.75285, 0.78222, 0.73984, 0.89555, 0.71435, 0.89462, 0.67938, 0.89334, 0.67892, 0.81422, 0.67843, 0.7306, 0.66369, 0.62238, 0.64926, 0.5164, 0.58904, 0.52155, 0.58902, 0.39309, 0.589, 0.26434, 0.58899, 0.17354, 0.72398, 0.03427, 0.85282, 0.31584, 0.89508, 0.51448, 0.74058, 0.32136, 0.77491, 0.43907, 0.65475, 0.38205, 0.71021, 0.58254, 0.85282, 0.64691, 0.85532, 0.82477, 0.7186, 0.71848], "triangles": [13, 14, 12, 14, 15, 12, 12, 15, 11, 15, 41, 11, 15, 16, 41, 11, 41, 10, 9, 10, 41, 17, 9, 41, 17, 41, 16, 17, 40, 9, 7, 8, 6, 8, 9, 6, 9, 40, 6, 22, 23, 21, 21, 23, 25, 23, 24, 25, 25, 42, 21, 25, 26, 42, 21, 42, 20, 17, 18, 40, 26, 27, 42, 6, 35, 5, 35, 6, 40, 27, 39, 42, 42, 39, 20, 20, 39, 19, 18, 19, 40, 40, 19, 35, 27, 28, 39, 19, 39, 37, 39, 28, 37, 19, 37, 35, 35, 4, 5, 28, 38, 37, 37, 34, 35, 4, 35, 34, 29, 30, 28, 28, 30, 38, 4, 34, 3, 38, 36, 37, 37, 36, 34, 30, 31, 38, 38, 31, 36, 36, 32, 33, 34, 36, 1, 36, 33, 0, 34, 2, 3, 34, 1, 2, 1, 36, 0, 36, 31, 32], "vertices": [1, 35, 1.11, 10.62, 1, 2, 35, 9.39, 11.28, 0.99581, 36, -13.16, 5.77, 0.00419, 2, 35, 14.27, 10.08, 0.89319, 36, -8.43, 7.45, 0.10681, 3, 35, 18.83, 8.96, 0.55375, 36, -4, 9.02, 0.44176, 37, -21.28, 2.86, 0.0045, 3, 35, 24.42, 7.59, 0.13811, 36, 1.42, 10.94, 0.80824, 37, -16.44, 5.97, 0.05365, 3, 35, 30.4, 4.03, 0.00252, 36, 8.37, 11.26, 0.69409, 37, -9.74, 7.86, 0.30338, 2, 36, 14.85, 11.56, 0.26442, 37, -3.5, 9.63, 0.73558, 2, 36, 19.61, 9.32, 0.07451, 37, 1.65, 8.54, 0.92549, 2, 36, 18.69, 6.22, 0.05472, 37, 1.46, 5.3, 0.94528, 2, 36, 17.99, 3.87, 0.01219, 37, 1.31, 2.86, 0.98781, 1, 37, 6.37, 3.42, 1, 1, 37, 9.22, 3.73, 1, 1, 37, 12.26, 4.07, 1, 1, 37, 16.1, 1.11, 1, 1, 37, 13.89, -1.59, 1, 1, 37, 11.76, -1.84, 1, 2, 36, 22.22, -3.2, 0.00161, 37, 7.04, -3.06, 0.99839, 2, 36, 18.44, -3.3, 0.0989, 37, 3.39, -4.01, 0.9011, 2, 36, 13.14, -3.42, 0.86126, 37, -1.75, -5.35, 0.13874, 2, 35, 20.95, -7.51, 0.05057, 36, 6.82, -3.58, 0.94943, 2, 35, 25.4, -12.68, 0.0284, 36, 13.38, -5.45, 0.9716, 2, 35, 29.78, -17.77, 0.00023, 36, 19.84, -7.29, 0.99977, 1, 36, 25.78, -8.99, 1, 1, 36, 25.5, -10.88, 1, 1, 36, 25.12, -13.48, 1, 1, 36, 20.89, -13, 1, 2, 35, 24.05, -20.24, 0.00902, 36, 16.41, -12.5, 0.99098, 2, 35, 18.89, -17.32, 0.09978, 36, 10.5, -12.91, 0.90022, 2, 35, 13.83, -14.47, 0.42223, 36, 4.7, -13.3, 0.57777, 2, 35, 11.13, -18.1, 0.57341, 36, 4.44, -17.82, 0.42659, 2, 35, 5.84, -13.63, 0.74657, 36, -2.43, -17, 0.25343, 2, 35, 0.55, -9.16, 0.95062, 36, -9.31, -16.17, 0.04938, 2, 35, -3.19, -6, 0.99453, 36, -14.17, -15.59, 0.00547, 1, 35, -2.38, 6.57, 1, 3, 35, 15.44, 4.16, 0.82184, 36, -4.2, 3.14, 0.1781, 37, -20.13, -2.91, 6e-05, 3, 35, 25.65, -0.32, 0.00159, 36, 6.8, 5.02, 0.89218, 37, -9.85, 1.42, 0.10623, 2, 35, 10.23, -2.46, 0.94804, 36, -4.91, -5.25, 0.05196, 2, 35, 16.73, -4.58, 0.24089, 36, 1.69, -3.45, 0.75911, 2, 35, 8.57, -9.48, 0.75293, 36, -2.43, -12.03, 0.24707, 2, 35, 19.5, -13.27, 0.12241, 36, 8.78, -9.19, 0.87759, 2, 36, 13.5, 1.02, 0.65294, 37, -2.41, -0.94, 0.34706, 1, 37, 7.08, 0.3, 1, 2, 35, 25.5, -17.52, 0.00917, 36, 16.13, -9.43, 0.99083], "hull": 34, "edges": [0, 66, 0, 2, 12, 14, 24, 26, 26, 28, 28, 30, 56, 58, 64, 66, 62, 64, 58, 60, 60, 62, 52, 54, 54, 56, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 36, 38, 34, 36, 30, 32, 32, 34, 18, 20, 14, 16, 16, 18, 8, 10, 10, 12, 6, 8, 2, 4, 4, 6, 44, 46, 46, 48, 20, 22, 22, 24], "width": 78, "height": 56}}, "tst": {"xst": {"type": "mesh", "uvs": [0.60021, 0, 0.79126, 0.01357, 0.85643, 0.05698, 0.93342, 0.15125, 0.9984, 0.23082, 0.9985, 0.34215, 0.90313, 0.49846, 0.81048, 0.59472, 0.79498, 0.62494, 0.75488, 0.69682, 0.69804, 0.74452, 0.63476, 0.79763, 0.6453, 0.82131, 0.64806, 0.88288, 0.56042, 0.96751, 0.50529, 0.98062, 0.34493, 0.99842, 0.3292, 0.97684, 0.35823, 0.93846, 0.45591, 0.89731, 0.52126, 0.86978, 0.57097, 0.84883, 0.44931, 0.82347, 0.38576, 0.83702, 0.31989, 0.85107, 0.24673, 0.86667, 0.24106, 0.83514, 0.2265, 0.79679, 0.16913, 0.80853, 0.12003, 0.81858, 0.09201, 0.82222, 0.095, 0.85647, 0.09902, 0.90254, 0.06236, 0.90099, 0.02478, 0.83363, 0.00352, 0.79551, 0.00392, 0.73232, 0.0316, 0.68643, 0.05847, 0.64189, 0.05504, 0.62375, 0.03785, 0.52831, 0.05707, 0.37866, 0.11927, 0.25525, 0.19579, 0.17162, 0.29417, 0.0859, 0.41744, 0.03985, 0.52412, 0], "triangles": [14, 20, 21, 14, 21, 13, 15, 19, 20, 15, 20, 14, 16, 17, 18, 15, 16, 18, 15, 18, 19, 21, 22, 11, 21, 11, 12, 21, 12, 13, 29, 36, 37, 36, 30, 35, 29, 30, 36, 34, 35, 30, 34, 30, 31, 33, 34, 31, 33, 31, 32, 38, 29, 37, 29, 38, 28, 40, 41, 27, 10, 11, 22, 9, 10, 8, 6, 2, 3, 5, 6, 3, 5, 3, 4, 1, 6, 0, 6, 1, 2, 6, 7, 0, 45, 46, 0, 7, 8, 0, 0, 8, 45, 22, 42, 43, 27, 42, 22, 42, 27, 41, 22, 44, 45, 44, 22, 43, 27, 24, 26, 23, 24, 27, 25, 26, 24, 10, 45, 8, 10, 22, 45, 22, 23, 27, 27, 28, 38, 27, 39, 40, 27, 38, 39], "vertices": [1, 57, 258.9, 99.84, 1, 1, 57, 328.26, 40.18, 1, 2, 57, 341.88, 6.53, 0.99914, 58, -262, 169.42, 0.00086, 1, 57, 346.84, -47.97, 1, 1, 57, 351.02, -93.98, 1, 1, 57, 322.23, -132.2, 1, 1, 57, 245.38, -158.37, 1, 1, 57, 185.12, -164.72, 1, 1, 57, 171.38, -170.63, 1, 1, 57, 137.48, -183.75, 1, 1, 57, 103.44, -183.75, 1, 1, 58, 36.53, 16.31, 1, 2, 58, 47.35, 19.74, 0.97483, 59, -21.7, -3.2, 0.02517, 2, 58, 73.71, 17.02, 0.3897, 59, -11.2, 21.13, 0.6103, 1, 59, 42.44, 35.27, 1, 1, 59, 68.57, 28.69, 1, 1, 59, 140.71, 1.72, 1, 1, 59, 143.35, -9.91, 1, 1, 59, 123.62, -18.59, 1, 1, 59, 73.93, -13.85, 1, 1, 59, 40.68, -10.68, 1, 4, 57, 27.97, -182.95, 0.00527, 60, 7.19, 247.67, 0.00079, 58, 53.64, -17.17, 0.30179, 59, 15.4, -8.27, 0.69215, 4, 57, -11.87, -139.23, 0.89982, 60, 13.62, 188.87, 0.00723, 58, 34.03, -72.97, 0.09118, 59, 62.75, -43.72, 0.00177, 4, 57, -39.62, -125.58, 1, 60, 28, 161.49, 0, 58, 35.17, -103.88, 0, 59, 92.57, -51.9, 0, 1, 57, -68.39, -111.44, 1, 1, 57, -100.33, -95.73, 1, 1, 57, -94.33, -83.28, 1, 1, 57, -89.95, -65.93, 1, 3, 57, -114.88, -53.45, 0.91917, 60, 46.3, 58.86, 0.07501, 58, 7.32, -204.33, 0.00582, 3, 57, -136.21, -42.76, 0.76204, 60, 57.24, 37.66, 0.23285, 58, 8.02, -228.18, 0.0051, 3, 57, -147.84, -35.94, 0.01571, 60, 62.62, 25.3, 0.97853, 58, 7.53, -241.65, 0.00576, 3, 57, -155.57, -48.55, 0.00237, 60, 76.29, 30.93, 0.99665, 58, 22.3, -242.48, 0.00098, 1, 60, 94.69, 38.52, 1, 1, 60, 99.13, 21.56, 1, 1, 60, 76.63, -4.03, 1, 1, 60, 63.9, -18.5, 1, 1, 60, 37.85, -26.2, 1, 1, 60, 15.14, -19.26, 1, 1, 57, -113.94, 35.58, 1, 1, 57, -110.56, 42.79, 1, 1, 57, -92.4, 80.48, 1, 1, 57, -46.32, 126.29, 1, 1, 57, 9.36, 150.72, 1, 1, 57, 60.2, 157.39, 1, 1, 57, 119.92, 158.47, 1, 2, 57, 178.86, 138.78, 0.99983, 60, -304.3, 76.61, 0.00017, 1, 57, 229.88, 121.74, 1], "hull": 47, "edges": [0, 92, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 42, 44, 50, 52, 52, 54, 58, 60, 64, 66, 70, 72, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 40, 42, 36, 38, 38, 40, 60, 62, 62, 64, 66, 68, 68, 70, 72, 74, 74, 76, 54, 56, 56, 58, 44, 46, 46, 48, 48, 50, 18, 20, 20, 22, 88, 90, 90, 92, 4, 6, 6, 8], "width": 323, "height": 290}}, "tt1": {"t1": {"type": "mesh", "uvs": [0.67528, 0.05848, 0.78901, 0.20763, 0.90659, 0.36183, 0.9912, 0.5252, 0.98813, 0.64712, 0.8413, 0.68204, 0.73713, 0.63598, 0.61124, 0.58032, 0.51464, 0.42925, 0.43761, 0.30879, 0.36029, 0.31174, 0.30687, 0.31378, 0.25286, 0.45376, 0.16969, 0.66933, 0.08861, 0.87945, 0.04998, 1, 0.03636, 1, 0.02424, 0.96717, 0.00188, 0.79098, 0.00991, 0.61836, 0.06938, 0.42268, 0.11768, 0.26375, 0.1837, 0.10962, 0.30767, 0.0772, 0.44372, 0.04162, 0.58787, 0.00392], "triangles": [14, 15, 17, 15, 16, 17, 17, 18, 14, 14, 18, 13, 18, 19, 13, 19, 20, 13, 13, 20, 12, 20, 21, 12, 12, 21, 11, 21, 22, 11, 11, 23, 10, 11, 22, 23, 9, 10, 24, 24, 10, 23, 4, 5, 3, 2, 3, 5, 5, 6, 2, 2, 6, 1, 6, 7, 1, 7, 8, 1, 0, 8, 9, 8, 0, 1, 0, 9, 25, 25, 9, 24], "vertices": [1, 81, 61.38, -28.7, 1, 1, 81, 32.67, -25.67, 1, 1, 81, 3, -22.54, 1, 1, 81, -22.84, -14.71, 1, 1, 81, -32.85, -1.4, 1, 1, 81, -15.28, 18.87, 1, 1, 81, 3.25, 25.72, 1, 2, 81, 25.64, 34.01, 0.99987, 82, -9.29, 63.56, 0.00013, 2, 81, 52.11, 28.84, 0.9242, 82, 2.96, 39.54, 0.0758, 3, 81, 73.21, 24.72, 0.39916, 82, 12.72, 20.38, 0.6001, 83, -9.94, 38.36, 0.00074, 3, 81, 83.79, 33.75, 0.05069, 82, 26.36, 17.62, 0.87282, 83, -3.91, 25.82, 0.0765, 3, 81, 91.1, 40, 0.00353, 82, 35.78, 15.72, 0.58618, 83, 0.26, 17.16, 0.41029, 2, 82, 49.56, 32.13, 0.0108, 83, 21.67, 16.08, 0.9892, 1, 83, 54.64, 14.41, 1, 1, 83, 86.78, 12.78, 1, 1, 83, 104.64, 13.14, 1, 1, 83, 105.64, 10.9, 1, 1, 83, 102.43, 7.08, 1, 1, 83, 82.1, -6.39, 1, 1, 83, 59.99, -14.67, 1, 1, 83, 31.23, -15.78, 1, 2, 82, 67.36, 1.36, 0.00268, 83, 7.87, -16.69, 0.99732, 2, 82, 51.03, -16.45, 0.5336, 83, -16.18, -14.42, 0.4664, 2, 82, 28.32, -15.71, 0.99707, 83, -29.3, 4.14, 0.00293, 2, 81, 95.24, -4.38, 0.11815, 82, 3.39, -14.9, 0.88185, 2, 81, 78.29, -24.65, 0.9988, 82, -23.02, -14.05, 0.0012], "hull": 26, "edges": [0, 50, 4, 6, 6, 8, 8, 10, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 22, 24, 24, 26, 26, 28, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 18, 20, 20, 22, 14, 16, 16, 18, 10, 12, 12, 14, 0, 2, 2, 4], "width": 121, "height": 92}}, "tj2": {"xj2": {"type": "mesh", "uvs": [0.46155, 0.05087, 0.46185, 0.1185, 0.44275, 0.14179, 0.64567, 0.19774, 0.82957, 0.29899, 0.95669, 0.40992, 1, 0.55765, 1, 0.59155, 0.9444, 0.71482, 0.82546, 0.83854, 0.62314, 0.94294, 0.40114, 1, 0.35056, 1, 0.33672, 0.98203, 0.40283, 0.89636, 0.45601, 0.82744, 0.51335, 0.75314, 0.38302, 0.68755, 0.29137, 0.76194, 0.14225, 0.88296, 0.09083, 0.86756, 0.04672, 0.74468, 0, 0.61452, 0, 0.60807, 0.0291, 0.50021, 0.10128, 0.43725, 0.17196, 0.37559, 0.16088, 0.36778, 0.11565, 0.2804, 0.07679, 0.20534, 0.2515, 0.00162, 0.43214, 0.00201, 0.30052, 0.37197, 0.42596, 0.42326, 0.45104, 0.54049], "triangles": [10, 11, 14, 14, 11, 13, 11, 12, 13, 14, 15, 10, 10, 15, 9, 15, 16, 9, 9, 16, 8, 8, 16, 34, 16, 17, 34, 8, 34, 7, 34, 6, 7, 34, 5, 6, 5, 34, 33, 19, 20, 18, 20, 21, 18, 18, 21, 17, 25, 17, 22, 34, 17, 26, 26, 17, 25, 17, 21, 22, 24, 25, 23, 26, 33, 34, 25, 22, 23, 26, 27, 32, 33, 26, 32, 5, 33, 4, 33, 3, 4, 33, 32, 3, 32, 27, 2, 32, 2, 3, 30, 1, 2, 2, 29, 30, 2, 27, 28, 2, 28, 29, 1, 30, 0, 0, 30, 31], "vertices": [1, 66, -13.76, 54.75, 1, 1, 66, 10.53, 42.84, 1, 2, 66, 16.9, 34.71, 0.99983, 67, -146.32, -42.16, 0.00017, 2, 66, 57.97, 67.31, 0.95184, 67, -123.47, 5.03, 0.04816, 2, 66, 113.31, 87.9, 0.6582, 67, -82.54, 47.6, 0.3418, 2, 66, 166.25, 94.87, 0.25102, 67, -37.88, 76.86, 0.74898, 2, 66, 223.71, 77.78, 0.01233, 67, 21.31, 86.39, 0.98767, 2, 66, 235.87, 71.77, 0.00264, 67, 34.87, 86.26, 0.99736, 1, 67, 84.05, 72.78, 1, 1, 67, 133.26, 44.5, 1, 1, 67, 174.55, -3.18, 1, 1, 67, 196.86, -55.28, 1, 1, 67, 196.74, -67.1, 1, 1, 67, 189.52, -70.26, 1, 1, 67, 155.41, -54.47, 1, 2, 68, 131.54, 71.47, 0.00029, 67, 127.96, -41.77, 0.99971, 2, 68, 99.35, 76.61, 0.05376, 67, 98.37, -28.08, 0.94624, 2, 68, 82.01, 40.35, 0.6489, 67, 71.84, -58.28, 0.3511, 2, 68, 116.34, 27.48, 0.98138, 67, 101.38, -79.98, 0.01862, 1, 68, 172.19, 6.54, 1, 1, 68, 169.4, -6.66, 1, 1, 68, 124.66, -29.49, 1, 1, 68, 77.28, -53.68, 1, 1, 68, 74.79, -54.35, 1, 1, 68, 31.37, -59.1, 1, 2, 66, 87.53, -89.17, 0.03031, 68, 2.65, -49.42, 0.96969, 2, 66, 72.73, -63.44, 0.3733, 68, -25.48, -39.95, 0.6267, 2, 66, 68.78, -64.37, 0.45695, 68, -27.82, -43.27, 0.54305, 2, 66, 32.76, -58.37, 0.85053, 68, -58.78, -62.63, 0.14947, 2, 66, 1.83, -53.21, 0.9599, 68, -85.37, -79.26, 0.0401, 1, 66, -53.16, 19.48, 1, 1, 66, -34.32, 57.25, 1, 2, 66, 84.73, -35.86, 0.54902, 68, -34.75, -11.34, 0.45098, 2, 66, 116.11, -18.67, 0.71026, 68, -22.64, 22.32, 0.28974, 3, 66, 160.75, -34.18, 0.1599, 68, 21.08, 40.27, 0.46027, 67, 13.18, -41.8, 0.37982], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 32, 34, 38, 40, 44, 46, 46, 48, 52, 54, 58, 60, 60, 62, 34, 36, 36, 38, 30, 32, 26, 28, 28, 30, 54, 56, 56, 58, 48, 50, 50, 52, 40, 42, 42, 44], "width": 157, "height": 270}}, "txyingzi3": {"yingzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [177.83, -49.69, 117.33, -49.69, 117.33, -25.46, 177.83, -25.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 26, "height": 12}}, "ttt1": {"tt1": {"type": "mesh", "uvs": [0.59183, 0, 0.63476, 0, 0.66175, 0.01609, 0.76747, 0.11299, 0.89487, 0.22977, 0.98029, 0.27419, 0.99721, 0.42453, 0.96869, 0.59355, 0.93473, 0.79482, 0.78822, 0.98839, 0.74309, 1, 0.7548, 0.84543, 0.76525, 0.70754, 0.78078, 0.50252, 0.79077, 0.37066, 0.70535, 0.33579, 0.63849, 0.3085, 0.5051, 0.35296, 0.41392, 0.38334, 0.18845, 0.36553, 0.01381, 0.27976, 0.00949, 0.17309, 0.10241, 0.10313, 0.43832, 0.02245], "triangles": [10, 11, 9, 9, 11, 8, 11, 12, 8, 8, 12, 7, 12, 13, 7, 7, 13, 6, 6, 13, 14, 5, 14, 4, 14, 5, 6, 14, 15, 4, 4, 15, 3, 17, 18, 23, 19, 23, 18, 16, 23, 0, 16, 0, 2, 19, 20, 22, 19, 22, 23, 16, 17, 23, 15, 16, 3, 16, 2, 3, 2, 0, 1, 20, 21, 22], "vertices": [1, 72, 57.99, 35.66, 1, 1, 72, 64.83, 35.35, 1, 2, 72, 69.02, 32.46, 0.99955, 73, -38.9, 9.71, 0.00045, 2, 72, 85.15, 15.42, 0.69982, 73, -15.77, 13.68, 0.30018, 2, 73, 12.1, 18.46, 0.99177, 74, -31.65, -4.54, 0.00823, 2, 73, 26.19, 25.03, 0.86302, 74, -26.63, 10.17, 0.13698, 2, 73, 48.19, 12.25, 0.10144, 74, -2.17, 17.18, 0.89856, 1, 74, 26.63, 17.58, 1, 1, 74, 60.92, 18.06, 1, 1, 74, 97.03, 0.62, 1, 1, 74, 100.19, -6.14, 1, 1, 74, 74.25, -8.77, 1, 1, 74, 51.1, -11.11, 1, 2, 73, 38.34, -23.37, 0.0777, 74, 16.69, -14.6, 0.9223, 3, 72, 86.94, -28.06, 0.00846, 73, 21.39, -8.96, 0.92097, 74, -5.44, -16.84, 0.07057, 2, 72, 73.58, -21.59, 0.29717, 73, 8.6, -16.49, 0.70283, 2, 72, 63.12, -16.53, 0.79762, 73, -1.42, -22.38, 0.20238, 2, 72, 41.52, -23.06, 0.99988, 73, -7.98, -43.97, 0.00012, 1, 72, 26.75, -27.52, 1, 1, 72, -9.07, -22.93, 1, 1, 72, -36.27, -7.27, 1, 1, 72, -36.16, 10.69, 1, 1, 72, -20.83, 21.79, 1, 1, 72, 33.34, 32.97, 1], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 8, 10, 10, 12, 16, 18, 18, 20, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 26, 28, 24, 26, 20, 22, 22, 24, 12, 14, 14, 16, 4, 6, 6, 8, 28, 30, 30, 32, 32, 34, 34, 36], "width": 107, "height": 113}}, "st": {"tst": {"type": "mesh", "uvs": [0.71324, 0, 0.86612, 0.08354, 1, 0.1567, 1, 0.33843, 0.9631, 0.47704, 0.92064, 0.63654, 0.87789, 0.79712, 0.84164, 0.93333, 0.62066, 0.9626, 0.48805, 0.98017, 0.40014, 0.90161, 0.29259, 0.80551, 0.23269, 0.75198, 0.31319, 0.60254, 0.41092, 0.42111, 0.48156, 0.29, 0.57259, 0.12102, 0.63778, 0, 0.61897, 0.26564, 0.86771, 0.33738, 0.56776, 0.44275, 0.84332, 0.5257, 0.61165, 0.62659, 0.41657, 0.73869, 0.6775, 0.83509], "triangles": [8, 9, 24, 8, 24, 7, 24, 9, 10, 7, 24, 6, 10, 23, 24, 10, 11, 23, 23, 22, 24, 24, 22, 6, 11, 12, 23, 6, 22, 5, 12, 13, 23, 23, 13, 22, 14, 22, 13, 5, 22, 21, 22, 14, 20, 5, 21, 4, 19, 21, 20, 19, 20, 18, 18, 1, 19, 21, 22, 20, 21, 19, 4, 1, 18, 0, 16, 17, 0, 4, 19, 3, 14, 15, 20, 20, 15, 18, 19, 2, 3, 19, 1, 2, 15, 16, 18, 0, 18, 16], "vertices": [1, 92, 35.78, 5.06, 1, 1, 92, 31.74, -3.81, 1, 1, 92, 28.2, -11.58, 1, 1, 92, 17.43, -12.75, 1, 1, 92, 9, -11.64, 1, 2, 92, -0.71, -10.36, 0.83619, 107, -12.69, 4.28, 0.16381, 2, 92, -10.48, -9.07, 0.18282, 107, -5.35, 10.86, 0.81718, 2, 92, -18.77, -7.98, 0.00684, 107, 0.87, 16.44, 0.99316, 1, 107, 11.85, 11.05, 1, 1, 107, 18.45, 7.81, 1, 1, 107, 19.78, 1.22, 1, 2, 92, -14.46, 22.76, 0.01853, 107, 21.41, -6.83, 0.98147, 2, 92, -11.64, 26.37, 0.03449, 107, 22.32, -11.32, 0.96551, 2, 92, -2.31, 22.95, 0.20418, 107, 13.65, -16.19, 0.79582, 2, 92, 9.03, 18.8, 0.69102, 107, 3.13, -22.09, 0.30898, 2, 92, 17.22, 15.81, 0.92488, 107, -4.48, -26.36, 0.07512, 2, 92, 27.77, 11.94, 0.99855, 107, -14.29, -31.86, 0.00145, 1, 92, 35.33, 9.17, 1, 2, 92, 19.48, 8.48, 0.98382, 107, -11.52, -23.31, 0.01618, 1, 92, 16.71, -5.54, 1, 2, 92, 8.68, 10.12, 0.83241, 107, -3.24, -16.17, 0.16759, 2, 92, 5.4, -5.43, 0.99801, 107, -12.92, -3.57, 0.00199, 2, 92, -1.96, 6.54, 0.39573, 107, 0.96, -5.77, 0.60427, 2, 92, -9.76, 16.44, 0.04871, 107, 13.55, -6.29, 0.95129, 1, 107, 4.99, 6.53, 1], "hull": 18, "edges": [0, 34, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 32, 34, 30, 32, 28, 30, 24, 26, 26, 28, 22, 24, 18, 20, 20, 22, 14, 16, 16, 18, 0, 2, 2, 4], "width": 57, "height": 62}}, "txs1": {"s1": {"type": "mesh", "uvs": [0.3364, 0.2361, 0.40643, 0.11906, 0.45731, 0.03405, 0.48755, 0.02733, 0.53179, 0.01749, 0.51479, 0.10703, 0.49773, 0.19681, 0.48096, 0.28512, 0.60472, 0.28499, 0.73107, 0.28486, 0.84987, 0.28475, 0.97311, 0.28462, 0.97291, 0.36151, 0.97272, 0.43696, 0.97244, 0.54438, 0.89538, 0.61652, 0.82832, 0.6793, 0.73377, 0.73838, 0.62666, 0.80531, 0.53464, 0.86282, 0.41621, 0.93682, 0.33067, 0.99027, 0.22509, 0.93099, 0.15609, 0.89225, 0.07923, 0.76402, 0.0147, 0.65636, 0.05694, 0.49949, 0.17033, 0.35558, 0.28565, 0.32091, 0.56666, 0.36154, 0.50443, 0.48411], "triangles": [12, 10, 11, 29, 7, 8, 13, 10, 12, 13, 15, 10, 14, 15, 13, 15, 16, 9, 15, 9, 10, 16, 29, 9, 17, 29, 16, 9, 29, 8, 17, 30, 29, 18, 30, 17, 5, 3, 4, 5, 2, 3, 5, 1, 2, 6, 1, 5, 7, 1, 6, 0, 1, 7, 30, 7, 29, 24, 26, 27, 25, 26, 24, 19, 30, 18, 28, 23, 24, 28, 0, 7, 28, 7, 30, 28, 24, 27, 30, 23, 28, 22, 23, 30, 20, 22, 30, 19, 20, 30, 21, 22, 20], "vertices": [2, 10, 7.1, 11.99, 0.8098, 11, -3.04, 13.69, 0.1902, 2, 10, 9.77, 14.88, 0.7733, 11, 0.62, 15.12, 0.2267, 2, 10, 11.71, 16.98, 0.77548, 11, 3.28, 16.17, 0.22452, 2, 10, 12.74, 17.05, 0.77596, 11, 4.24, 15.78, 0.22404, 2, 10, 14.25, 17.16, 0.77643, 11, 5.65, 15.22, 0.22357, 2, 10, 13.43, 14.83, 0.77259, 11, 3.89, 13.48, 0.22741, 2, 10, 12.61, 12.48, 0.75282, 11, 2.13, 11.72, 0.24718, 2, 10, 11.8, 10.17, 0.65733, 11, 0.39, 10, 0.34267, 2, 10, 15.94, 9.75, 0.23691, 11, 3.94, 7.81, 0.76309, 2, 10, 20.17, 9.31, 0.02976, 11, 7.55, 5.57, 0.97024, 2, 10, 24.15, 8.9, 1e-05, 11, 10.95, 3.47, 0.99999, 1, 11, 14.48, 1.28, 1, 1, 11, 13.38, -0.47, 1, 1, 11, 12.31, -2.19, 1, 1, 11, 10.77, -4.65, 1, 1, 11, 7.55, -4.93, 1, 1, 11, 4.74, -5.18, 1, 2, 10, 19, -2.84, 0.05951, 11, 1.2, -4.85, 0.94049, 2, 10, 15.23, -4.26, 0.60104, 11, -2.82, -4.49, 0.39896, 2, 10, 11.99, -5.48, 0.96329, 11, -6.27, -4.17, 0.03671, 1, 10, 7.82, -7.05, 1, 1, 10, 4.81, -8.19, 1, 1, 10, 1.44, -6.23, 1, 1, 10, -0.76, -4.95, 1, 1, 10, -2.98, -1.25, 1, 1, 10, -4.84, 1.85, 1, 2, 10, -2.99, 5.91, 0.99982, 11, -14.77, 12.61, 0.00018, 2, 10, 1.21, 9.37, 0.9786, 11, -9.49, 13.9, 0.0214, 2, 10, 5.16, 9.89, 0.89684, 11, -5.7, 12.65, 0.10316, 2, 10, 14.46, 7.83, 0.34532, 11, 1.76, 6.73, 0.65468, 2, 10, 12.03, 4.76, 0.63541, 11, -1.76, 5.03, 0.36459], "hull": 29, "edges": [50, 52, 52, 54, 54, 56, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 56, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 4, 6, 6, 8], "width": 35, "height": 28}}, "txs2": {"s2": {"type": "mesh", "uvs": [0.63031, 0.10342, 0.85573, 0.10378, 0.89366, 0.12719, 0.92018, 0.20933, 0.93614, 0.25878, 0.96347, 0.34344, 0.96348, 0.40846, 0.96349, 0.48096, 0.96349, 0.54229, 0.87416, 0.59229, 0.81542, 0.62517, 0.81544, 0.70933, 0.81544, 0.77188, 0.83981, 0.85348, 0.8631, 0.93152, 0.77723, 0.98001, 0.62983, 0.97975, 0.55531, 0.93783, 0.5247, 0.87763, 0.481, 0.79167, 0.48093, 0.70933, 0.48085, 0.61871, 0.48082, 0.57884, 0.48079, 0.54323, 0.38987, 0.56831, 0.28145, 0.59821, 0.21043, 0.55169, 0.118, 0.49113, 0.03507, 0.4368, 0.03586, 0.39644, 0.14675, 0.29154, 0.36878, 0.24998], "triangles": [20, 21, 10, 20, 10, 11, 12, 19, 20, 12, 20, 11, 18, 19, 12, 18, 12, 13, 17, 18, 13, 16, 17, 13, 13, 15, 16, 14, 15, 13, 27, 29, 30, 28, 29, 27, 31, 5, 23, 27, 30, 31, 23, 27, 31, 0, 2, 31, 1, 2, 0, 3, 31, 2, 26, 27, 23, 24, 26, 23, 4, 31, 3, 22, 23, 10, 4, 5, 31, 6, 23, 5, 7, 23, 6, 7, 9, 23, 7, 8, 9, 25, 26, 24, 10, 21, 22, 23, 9, 10], "vertices": [1, 8, -3.99, -6.07, 1, 1, 8, -5.6, -0.44, 1, 1, 8, -4.84, 0.8, 1, 1, 8, -1.39, 2.52, 1, 1, 8, 0.68, 3.55, 1, 2, 8, 4.24, 5.32, 0.99958, 9, -11.62, 9.67, 0.00042, 2, 8, 7.12, 6.16, 0.98742, 9, -8.64, 9.31, 0.01258, 2, 8, 10.34, 7.09, 0.92368, 9, -5.32, 8.91, 0.07632, 2, 8, 13.05, 7.88, 0.82938, 9, -2.51, 8.57, 0.17062, 2, 8, 15.92, 6.29, 0.614, 9, -0.5, 5.99, 0.386, 2, 8, 17.8, 5.25, 0.2748, 9, 0.83, 4.29, 0.7252, 2, 8, 21.53, 6.33, 0.00052, 9, 4.68, 3.83, 0.99948, 1, 9, 7.55, 3.48, 1, 1, 9, 11.36, 3.66, 1, 1, 9, 15.01, 3.83, 1, 1, 9, 16.97, 1.35, 1, 1, 9, 16.5, -2.45, 1, 1, 9, 14.35, -4.14, 1, 1, 9, 11.49, -4.59, 1, 1, 9, 7.42, -5.24, 1, 2, 8, 23.95, -2.01, 0.02876, 9, 3.64, -4.79, 0.97124, 2, 8, 19.93, -3.17, 0.47734, 9, -0.51, -4.29, 0.52266, 2, 8, 18.16, -3.69, 0.81526, 9, -2.34, -4.07, 0.18474, 2, 8, 16.59, -4.14, 0.98223, 9, -3.97, -3.88, 0.01777, 1, 8, 18.36, -6.09, 1, 1, 8, 20.47, -8.41, 1, 1, 8, 18.92, -10.78, 1, 1, 8, 16.9, -13.86, 1, 1, 8, 15.09, -16.63, 1, 1, 8, 13.3, -17.13, 1, 1, 8, 7.85, -15.71, 1, 1, 8, 4.4, -10.71, 1], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 28, 30, 30, 32, 32, 34, 56, 58, 58, 60, 60, 62, 38, 40, 40, 42, 20, 22, 22, 24, 16, 18, 18, 20, 10, 12, 12, 14, 14, 16, 46, 48, 48, 50, 54, 56, 50, 52, 52, 54, 4, 6, 6, 8, 8, 10, 34, 36, 36, 38, 24, 26, 26, 28, 42, 44, 44, 46], "width": 27, "height": 48}}, "txs3": {"s2": {"type": "mesh", "uvs": [0.63031, 0.10342, 0.85573, 0.10378, 0.89366, 0.12719, 0.92018, 0.20933, 0.93614, 0.25878, 0.96347, 0.34344, 0.96348, 0.40846, 0.96349, 0.48096, 0.96349, 0.54229, 0.87416, 0.59229, 0.81542, 0.62517, 0.81544, 0.70933, 0.81544, 0.77188, 0.83981, 0.85348, 0.8631, 0.93152, 0.77723, 0.98001, 0.62983, 0.97975, 0.55531, 0.93783, 0.5247, 0.87763, 0.481, 0.79167, 0.48093, 0.70933, 0.48085, 0.61871, 0.48082, 0.57884, 0.48079, 0.54323, 0.38987, 0.56831, 0.28145, 0.59821, 0.21043, 0.55169, 0.118, 0.49113, 0.03507, 0.4368, 0.03586, 0.39644, 0.14675, 0.29154, 0.36878, 0.24998], "triangles": [20, 21, 10, 20, 10, 11, 12, 19, 20, 12, 20, 11, 18, 19, 12, 18, 12, 13, 17, 18, 13, 16, 17, 13, 13, 15, 16, 14, 15, 13, 27, 29, 30, 28, 29, 27, 31, 5, 23, 27, 30, 31, 23, 27, 31, 0, 2, 31, 1, 2, 0, 3, 31, 2, 26, 27, 23, 24, 26, 23, 4, 31, 3, 22, 23, 10, 4, 5, 31, 6, 23, 5, 7, 23, 6, 7, 9, 23, 7, 8, 9, 25, 26, 24, 10, 21, 22, 23, 9, 10], "vertices": [1, 27, -3.99, -6.07, 1, 1, 27, -5.6, -0.44, 1, 1, 27, -4.84, 0.8, 1, 1, 27, -1.39, 2.52, 1, 1, 27, 0.68, 3.55, 1, 2, 27, 4.24, 5.32, 0.99958, 28, -11.62, 9.67, 0.00042, 2, 27, 7.12, 6.16, 0.98742, 28, -8.64, 9.31, 0.01258, 2, 27, 10.34, 7.09, 0.92368, 28, -5.32, 8.91, 0.07632, 2, 27, 13.05, 7.88, 0.82938, 28, -2.51, 8.57, 0.17062, 2, 27, 15.92, 6.29, 0.614, 28, -0.5, 5.99, 0.386, 2, 27, 17.8, 5.25, 0.2748, 28, 0.83, 4.29, 0.7252, 2, 27, 21.53, 6.33, 0.00052, 28, 4.68, 3.83, 0.99948, 1, 28, 7.55, 3.48, 1, 1, 28, 11.36, 3.66, 1, 1, 28, 15.01, 3.83, 1, 1, 28, 16.97, 1.35, 1, 1, 28, 16.5, -2.45, 1, 1, 28, 14.35, -4.14, 1, 1, 28, 11.49, -4.59, 1, 1, 28, 7.42, -5.24, 1, 2, 27, 23.95, -2.01, 0.02876, 28, 3.64, -4.79, 0.97124, 2, 27, 19.93, -3.17, 0.47734, 28, -0.51, -4.29, 0.52266, 2, 27, 18.16, -3.69, 0.81526, 28, -2.34, -4.07, 0.18474, 2, 27, 16.59, -4.14, 0.98223, 28, -3.97, -3.88, 0.01777, 1, 27, 18.36, -6.09, 1, 1, 27, 20.47, -8.41, 1, 1, 27, 18.92, -10.78, 1, 1, 27, 16.9, -13.86, 1, 1, 27, 15.09, -16.63, 1, 1, 27, 13.3, -17.13, 1, 1, 27, 7.85, -15.71, 1, 1, 27, 4.4, -10.71, 1], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 28, 30, 30, 32, 32, 34, 56, 58, 58, 60, 60, 62, 38, 40, 40, 42, 20, 22, 22, 24, 16, 18, 18, 20, 10, 12, 12, 14, 14, 16, 46, 48, 48, 50, 54, 56, 50, 52, 52, 54, 4, 6, 6, 8, 8, 10, 34, 36, 36, 38, 24, 26, 26, 28, 42, 44, 44, 46], "width": 27, "height": 48}}, "txs4": {"s1": {"type": "mesh", "uvs": [0.3364, 0.2361, 0.40643, 0.11906, 0.45731, 0.03405, 0.48755, 0.02733, 0.53179, 0.01749, 0.51479, 0.10703, 0.49773, 0.19681, 0.48096, 0.28512, 0.60472, 0.28499, 0.73107, 0.28486, 0.84987, 0.28475, 0.97311, 0.28462, 0.97291, 0.36151, 0.97272, 0.43696, 0.97244, 0.54438, 0.89538, 0.61652, 0.82832, 0.6793, 0.73377, 0.73838, 0.62666, 0.80531, 0.53464, 0.86282, 0.41621, 0.93682, 0.33067, 0.99027, 0.22509, 0.93099, 0.15609, 0.89225, 0.07923, 0.76402, 0.0147, 0.65636, 0.05694, 0.49949, 0.17033, 0.35558, 0.28565, 0.32091, 0.56666, 0.36154, 0.50443, 0.48411], "triangles": [12, 10, 11, 29, 7, 8, 13, 10, 12, 13, 15, 10, 14, 15, 13, 15, 16, 9, 15, 9, 10, 16, 29, 9, 17, 29, 16, 9, 29, 8, 17, 30, 29, 18, 30, 17, 5, 3, 4, 5, 2, 3, 5, 1, 2, 6, 1, 5, 7, 1, 6, 0, 1, 7, 30, 7, 29, 24, 26, 27, 25, 26, 24, 19, 30, 18, 28, 23, 24, 28, 0, 7, 28, 7, 30, 28, 24, 27, 30, 23, 28, 22, 23, 30, 20, 22, 30, 19, 20, 30, 21, 22, 20], "vertices": [2, 29, 7.1, 11.99, 0.8098, 30, -3.04, 13.69, 0.1902, 2, 29, 9.77, 14.88, 0.7733, 30, 0.62, 15.12, 0.2267, 2, 29, 11.71, 16.98, 0.77548, 30, 3.28, 16.17, 0.22452, 2, 29, 12.74, 17.05, 0.77596, 30, 4.24, 15.78, 0.22404, 2, 29, 14.25, 17.16, 0.77643, 30, 5.65, 15.22, 0.22357, 2, 29, 13.43, 14.83, 0.77259, 30, 3.89, 13.48, 0.22741, 2, 29, 12.61, 12.48, 0.75282, 30, 2.13, 11.72, 0.24718, 2, 29, 11.8, 10.17, 0.65733, 30, 0.39, 10, 0.34267, 2, 29, 15.94, 9.75, 0.23691, 30, 3.94, 7.81, 0.76309, 2, 29, 20.17, 9.31, 0.02976, 30, 7.55, 5.57, 0.97024, 2, 29, 24.15, 8.9, 1e-05, 30, 10.95, 3.47, 0.99999, 1, 30, 14.48, 1.28, 1, 1, 30, 13.38, -0.47, 1, 1, 30, 12.31, -2.19, 1, 1, 30, 10.77, -4.65, 1, 1, 30, 7.55, -4.93, 1, 1, 30, 4.74, -5.18, 1, 2, 29, 19, -2.84, 0.05951, 30, 1.2, -4.85, 0.94049, 2, 29, 15.23, -4.26, 0.60104, 30, -2.82, -4.49, 0.39896, 2, 29, 11.99, -5.48, 0.96329, 30, -6.27, -4.17, 0.03671, 1, 29, 7.82, -7.05, 1, 1, 29, 4.81, -8.19, 1, 1, 29, 1.44, -6.23, 1, 1, 29, -0.76, -4.95, 1, 1, 29, -2.98, -1.25, 1, 1, 29, -4.84, 1.85, 1, 2, 29, -2.99, 5.91, 0.99982, 30, -14.77, 12.61, 0.00018, 2, 29, 1.21, 9.37, 0.9786, 30, -9.49, 13.9, 0.0214, 2, 29, 5.16, 9.89, 0.89684, 30, -5.7, 12.65, 0.10316, 2, 29, 14.46, 7.83, 0.34532, 30, 1.76, 6.73, 0.65468, 2, 29, 12.03, 4.76, 0.63541, 30, -1.76, 5.03, 0.36459], "hull": 29, "edges": [50, 52, 52, 54, 54, 56, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 56, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 4, 6, 6, 8], "width": 35, "height": 28}}, "txs5": {"s2": {"type": "mesh", "uvs": [0.63031, 0.10342, 0.85573, 0.10378, 0.89366, 0.12719, 0.92018, 0.20933, 0.93614, 0.25878, 0.96347, 0.34344, 0.96348, 0.40846, 0.96349, 0.48096, 0.96349, 0.54229, 0.87416, 0.59229, 0.81542, 0.62517, 0.81544, 0.70933, 0.81544, 0.77188, 0.83981, 0.85348, 0.8631, 0.93152, 0.77723, 0.98001, 0.62983, 0.97975, 0.55531, 0.93783, 0.5247, 0.87763, 0.481, 0.79167, 0.48093, 0.70933, 0.48085, 0.61871, 0.48082, 0.57884, 0.48079, 0.54323, 0.38987, 0.56831, 0.28145, 0.59821, 0.21043, 0.55169, 0.118, 0.49113, 0.03507, 0.4368, 0.03586, 0.39644, 0.14675, 0.29154, 0.36878, 0.24998], "triangles": [20, 21, 10, 20, 10, 11, 12, 19, 20, 12, 20, 11, 18, 19, 12, 18, 12, 13, 17, 18, 13, 16, 17, 13, 13, 15, 16, 14, 15, 13, 27, 29, 30, 28, 29, 27, 31, 5, 23, 27, 30, 31, 23, 27, 31, 0, 2, 31, 1, 2, 0, 3, 31, 2, 26, 27, 23, 24, 26, 23, 4, 31, 3, 22, 23, 10, 4, 5, 31, 6, 23, 5, 7, 23, 6, 7, 9, 23, 7, 8, 9, 25, 26, 24, 10, 21, 22, 23, 9, 10], "vertices": [1, 44, -3.99, -6.07, 1, 1, 44, -5.6, -0.44, 1, 1, 44, -4.84, 0.8, 1, 1, 44, -1.39, 2.52, 1, 1, 44, 0.68, 3.55, 1, 2, 44, 4.24, 5.32, 0.99958, 45, -11.62, 9.67, 0.00042, 2, 44, 7.12, 6.16, 0.98742, 45, -8.64, 9.31, 0.01258, 2, 44, 10.34, 7.09, 0.92368, 45, -5.32, 8.91, 0.07632, 2, 44, 13.05, 7.88, 0.82938, 45, -2.51, 8.57, 0.17062, 2, 44, 15.92, 6.29, 0.614, 45, -0.5, 5.99, 0.386, 2, 44, 17.8, 5.25, 0.2748, 45, 0.83, 4.29, 0.7252, 2, 44, 21.53, 6.33, 0.00052, 45, 4.68, 3.83, 0.99948, 1, 45, 7.55, 3.48, 1, 1, 45, 11.36, 3.66, 1, 1, 45, 15.01, 3.83, 1, 1, 45, 16.97, 1.35, 1, 1, 45, 16.5, -2.45, 1, 1, 45, 14.35, -4.14, 1, 1, 45, 11.49, -4.59, 1, 1, 45, 7.42, -5.24, 1, 2, 44, 23.95, -2.01, 0.02876, 45, 3.64, -4.79, 0.97124, 2, 44, 19.93, -3.17, 0.47734, 45, -0.51, -4.29, 0.52266, 2, 44, 18.16, -3.69, 0.81526, 45, -2.34, -4.07, 0.18474, 2, 44, 16.59, -4.14, 0.98223, 45, -3.97, -3.88, 0.01777, 1, 44, 18.36, -6.09, 1, 1, 44, 20.47, -8.41, 1, 1, 44, 18.92, -10.78, 1, 1, 44, 16.9, -13.86, 1, 1, 44, 15.09, -16.63, 1, 1, 44, 13.3, -17.13, 1, 1, 44, 7.85, -15.71, 1, 1, 44, 4.4, -10.71, 1], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 28, 30, 30, 32, 32, 34, 56, 58, 58, 60, 60, 62, 38, 40, 40, 42, 20, 22, 22, 24, 16, 18, 18, 20, 10, 12, 12, 14, 14, 16, 46, 48, 48, 50, 54, 56, 50, 52, 52, 54, 4, 6, 6, 8, 8, 10, 34, 36, 36, 38, 24, 26, 26, 28, 42, 44, 44, 46], "width": 27, "height": 48}}, "honghuo2": {"honghuo0001": {"x": 8.09, "y": 0.37, "rotation": -91.47, "width": 58, "height": 88}, "honghuo0002": {"x": 8.09, "y": 0.37, "rotation": -91.47, "width": 58, "height": 88}, "honghuo0003": {"x": 8.09, "y": 0.37, "rotation": -91.47, "width": 58, "height": 88}, "honghuo0004": {"x": 8.09, "y": 0.37, "rotation": -91.47, "width": 58, "height": 88}, "honghuo0005": {"x": 8.09, "y": 0.37, "rotation": -91.47, "width": 58, "height": 88}, "honghuo0006": {"x": 8.09, "y": 0.37, "rotation": -91.47, "width": 58, "height": 88}, "honghuo0007": {"x": 8.09, "y": 0.37, "rotation": -91.47, "width": 58, "height": 88}, "honghuo0008": {"x": 8.09, "y": 0.37, "rotation": -91.47, "width": 58, "height": 88}, "honghuo0009": {"x": 8.09, "y": 0.37, "rotation": -91.47, "width": 58, "height": 88}}, "txt2": {"t": {"type": "mesh", "uvs": [0.45189, 0.01329, 0.53961, 0.01244, 0.59395, 0.04517, 0.59403, 0.15484, 0.82644, 0.17281, 0.88036, 0.18649, 0.98347, 0.18649, 0.99318, 0.42748, 0.96841, 0.84728, 0.9007, 0.87579, 0.79898, 0.95712, 0.71051, 0.9868, 0.2606, 0.98745, 0.06942, 0.86339, 0.01158, 0.83869, 0.04931, 0.66028, 0.01528, 0.57291, 0.01545, 0.50545, 0.0465, 0.45331, 0.0773, 0.42627, 0.00219, 0.29764, 0.02307, 0.18579, 0.20755, 0.18665, 0.40474, 0.13626, 0.40623, 0.05307], "triangles": [22, 20, 21, 19, 20, 22, 18, 16, 17, 18, 19, 16, 15, 16, 19, 7, 4, 5, 7, 5, 6, 12, 13, 15, 14, 15, 13, 24, 0, 1, 24, 1, 23, 3, 1, 2, 3, 23, 1, 8, 9, 7, 15, 3, 7, 4, 7, 3, 9, 10, 7, 10, 11, 7, 23, 19, 22, 19, 3, 15, 12, 15, 11, 23, 3, 19, 7, 11, 15], "vertices": [1, 31, 52.84, 2.51, 1, 1, 31, 52.95, -2.89, 1, 1, 31, 50.62, -6.25, 1, 1, 31, 42.71, -6.33, 1, 1, 31, 41.54, -20.64, 1, 1, 31, 40.59, -23.97, 1, 1, 31, 40.64, -30.31, 1, 1, 31, 23.27, -31.06, 1, 1, 31, -7.02, -29.81, 1, 1, 31, -9.11, -25.66, 1, 1, 31, -15.03, -19.46, 1, 1, 31, -17.22, -14.03, 1, 1, 31, -17.51, 13.65, 1, 1, 31, -8.67, 25.5, 1, 1, 31, -6.92, 29.07, 1, 1, 31, 5.96, 26.87, 1, 1, 31, 12.24, 29.02, 1, 1, 31, 17.11, 29.05, 1, 1, 31, 20.89, 27.17, 1, 1, 31, 22.85, 25.3, 1, 1, 31, 32.09, 30, 1, 1, 31, 40.16, 28.79, 1, 1, 31, 40.2, 17.44, 1, 1, 31, 43.95, 5.33, 1, 1, 31, 49.95, 5.3, 1], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 8, 10], "width": 64, "height": 75}}, "txj5": {"j1": {"type": "mesh", "uvs": [0.86979, 0.02349, 0.95704, 0.12164, 0.95705, 0.21134, 0.87053, 0.22846, 0.8704, 0.28128, 0.87022, 0.36101, 0.89588, 0.43613, 0.92469, 0.52049, 0.90173, 0.60426, 0.88108, 0.67962, 0.85061, 0.79078, 0.82587, 0.88107, 0.65229, 0.97596, 0.2604, 0.97622, 0.26042, 0.88666, 0.38969, 0.78756, 0.26527, 0.70247, 0.1559, 0.62767, 0.08719, 0.58068, 0.01559, 0.53171, 0.04864, 0.44847, 0.09296, 0.3368, 0.13145, 0.23985, 0.17769, 0.12337, 0.23547, 0.04851, 0.43955, 0.04843, 0.66564, 0.02249], "triangles": [1, 26, 0, 23, 24, 25, 4, 25, 26, 17, 18, 20, 19, 20, 18, 7, 17, 6, 7, 16, 17, 6, 17, 20, 7, 8, 16, 9, 16, 8, 15, 16, 9, 10, 15, 9, 11, 15, 10, 12, 15, 11, 14, 15, 12, 13, 14, 12, 3, 1, 2, 1, 3, 26, 26, 3, 4, 25, 22, 23, 22, 25, 4, 22, 5, 21, 4, 5, 22, 6, 21, 5, 21, 6, 20], "vertices": [1, 39, 24.04, 0.43, 1, 1, 40, 1.19, 5.91, 1, 2, 40, 4.5, 7.16, 0.99889, 41, -7.87, 13.84, 0.00111, 2, 40, 5.81, 5.61, 0.99018, 41, -7.74, 11.82, 0.00982, 2, 40, 7.75, 6.35, 0.94922, 41, -5.74, 11.25, 0.05078, 2, 40, 10.69, 7.46, 0.80058, 41, -2.72, 10.39, 0.19942, 2, 40, 13.26, 9.05, 0.55182, 41, 0.29, 10.13, 0.44818, 2, 40, 16.14, 10.82, 0.29468, 41, 3.66, 9.84, 0.70532, 2, 40, 19.41, 11.52, 0.11688, 41, 6.7, 8.46, 0.88312, 2, 40, 22.35, 12.15, 0.02888, 41, 9.44, 7.21, 0.97112, 2, 40, 26.69, 13.08, 0.00013, 41, 13.47, 5.37, 0.99987, 1, 41, 16.75, 3.88, 1, 1, 41, 19.3, -0.83, 1, 1, 41, 16.96, -9.18, 1, 1, 41, 13.56, -8.22, 1, 1, 41, 10.58, -4.41, 1, 2, 40, 28.03, -0.26, 0.01266, 41, 6.6, -6.14, 0.98734, 2, 40, 26.13, -3.57, 0.15467, 41, 3.11, -7.67, 0.84533, 2, 40, 24.94, -5.65, 0.32922, 41, 0.91, -8.63, 0.67078, 2, 40, 23.7, -7.81, 0.47418, 41, -1.38, -9.63, 0.52582, 2, 40, 20.37, -8.3, 0.69297, 41, -4.34, -8.03, 0.30703, 2, 40, 15.91, -8.94, 0.95844, 41, -8.31, -5.89, 0.04156, 2, 40, 12.03, -9.51, 0.99983, 41, -11.75, -4.03, 0.00017, 1, 40, 7.38, -10.18, 1, 2, 40, 4.16, -10.03, 0.04, 39, 21.18, -13.34, 0.96, 1, 39, 22.4, -8.99, 1, 1, 39, 22.78, -3.9, 1], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 22, 24, 24, 26, 26, 28, 28, 30, 46, 48, 48, 50, 50, 52, 20, 22, 18, 20, 14, 16, 16, 18, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 10, 12, 12, 14, 6, 8, 8, 10, 34, 36, 36, 38], "width": 23, "height": 41}}, "txj4": {"j2": {"type": "mesh", "uvs": [0.44312, 0.01315, 0.79232, 0.17297, 0.88609, 0.2759, 0.96793, 0.36572, 0.96752, 0.46751, 0.96732, 0.51851, 0.96697, 0.60643, 0.9084, 0.65631, 0.84744, 0.70824, 0.90718, 0.78046, 0.96853, 0.85462, 0.96663, 0.97617, 0.79904, 0.97597, 0.64879, 0.93896, 0.50028, 0.8313, 0.47183, 0.76191, 0.4429, 0.69133, 0.40029, 0.58738, 0.36632, 0.50451, 0.36424, 0.43939, 0.23373, 0.40333, 0.09841, 0.36593, 0, 0.21913, 0, 0.20154, 0.33143, 0.09779], "triangles": [22, 23, 24, 1, 24, 0, 21, 22, 24, 4, 17, 18, 5, 17, 4, 5, 7, 17, 6, 7, 5, 8, 16, 17, 7, 8, 17, 15, 16, 8, 14, 15, 8, 13, 14, 8, 13, 8, 9, 12, 13, 9, 10, 12, 9, 11, 12, 10, 1, 19, 24, 19, 1, 2, 20, 24, 19, 4, 2, 3, 19, 2, 4, 18, 19, 4, 20, 21, 24], "vertices": [1, 22, 19.46, 14.01, 1, 1, 25, 13.16, 9.71, 1, 1, 25, 17.69, 7.92, 1, 2, 25, 21.65, 6.35, 0.96142, 26, -3.63, 7.16, 0.03858, 2, 25, 23.92, 3.05, 0.67985, 26, 0.38, 7.27, 0.32015, 2, 25, 25.07, 1.39, 0.45832, 26, 2.39, 7.33, 0.54168, 2, 25, 27.03, -1.46, 0.20181, 26, 5.86, 7.42, 0.79819, 2, 25, 26.76, -4.04, 0.0934, 26, 7.87, 5.8, 0.9066, 2, 25, 26.48, -6.73, 0.01525, 26, 9.97, 4.1, 0.98475, 2, 25, 29.52, -8.09, 2e-05, 26, 12.77, 5.91, 0.99998, 1, 26, 15.63, 7.77, 1, 1, 26, 20.43, 7.86, 1, 1, 26, 20.57, 3.03, 1, 1, 26, 19.24, -1.35, 1, 1, 26, 15.13, -5.76, 1, 2, 25, 18.78, -14.64, 0.00185, 26, 12.42, -6.66, 0.99815, 2, 25, 16.51, -12.83, 0.02826, 26, 9.66, -7.58, 0.97174, 2, 25, 13.17, -10.16, 0.20535, 26, 5.61, -8.94, 0.79465, 2, 25, 10.5, -8.03, 0.49256, 26, 2.37, -10.02, 0.50744, 2, 25, 8.99, -5.96, 0.78513, 26, -0.19, -10.16, 0.21487, 2, 25, 5.09, -6.93, 0.97898, 26, -1.5, -13.96, 0.02102, 3, 25, 1.04, -7.95, 0.95187, 26, -2.85, -17.91, 0.00013, 22, 30.14, 0.66, 0.048, 2, 25, -4.59, -4.81, 0.248, 22, 23.8, -0.5, 0.752, 1, 22, 23.13, -0.31, 1, 1, 22, 21.79, 10, 1], "hull": 25, "edges": [0, 48, 0, 2, 20, 22, 22, 24, 24, 26, 26, 28, 36, 38, 42, 44, 44, 46, 46, 48, 34, 36, 32, 34, 28, 30, 30, 32, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 12, 14, 14, 16, 2, 4, 4, 6, 38, 40, 40, 42], "width": 30, "height": 41}}, "txst2": {"st": {"type": "mesh", "uvs": [0.82101, 0.30365, 0.86062, 0.37587, 0.94396, 0.43337, 0.98785, 0.54802, 0.84671, 0.61126, 0.46063, 0.64999, 0.41284, 0.65108, 0.19993, 0.46235, 0.19758, 0.41855, 0.34113, 0.36593, 0.56637, 0.37587], "triangles": [10, 0, 1, 7, 8, 9, 2, 4, 10, 2, 10, 1, 4, 2, 3, 10, 7, 9, 5, 10, 4, 5, 7, 10, 6, 7, 5], "vertices": [1, 22, -6.9, 14.21, 1, 1, 22, 0.9, 13.99, 1, 1, 22, 7.79, 16.21, 1, 1, 22, 19.93, 14.97, 1, 1, 22, 24.46, 6.64, 1, 1, 22, 23.32, -12.33, 1, 1, 22, 22.81, -14.57, 1, 1, 22, 0.99, -19.04, 1, 1, 22, -3.46, -17.9, 1, 1, 22, -6.89, -9.76, 1, 1, 22, -2.95, 0.37, 1], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 16, 18, 18, 20, 10, 12], "width": 50, "height": 109}}, "txst3": {"st": {"type": "mesh", "uvs": [0.82101, 0.30365, 0.86062, 0.37587, 0.94396, 0.43337, 0.98785, 0.54802, 0.84671, 0.61126, 0.46063, 0.64999, 0.41284, 0.65108, 0.19993, 0.46235, 0.19758, 0.41855, 0.34113, 0.36593, 0.56637, 0.37587], "triangles": [10, 0, 1, 7, 8, 9, 2, 4, 10, 2, 10, 1, 4, 2, 3, 10, 7, 9, 5, 10, 4, 5, 7, 10, 6, 7, 5], "vertices": [1, 39, -6.9, 14.21, 1, 1, 39, 0.9, 13.99, 1, 1, 39, 7.79, 16.21, 1, 1, 39, 19.93, 14.97, 1, 1, 39, 24.46, 6.64, 1, 1, 39, 23.32, -12.33, 1, 1, 39, 22.81, -14.57, 1, 1, 39, 0.99, -19.04, 1, 1, 39, -3.46, -17.9, 1, 1, 39, -6.89, -9.76, 1, 1, 39, -2.95, 0.37, 1], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 16, 18, 18, 20, 10, 12], "width": 50, "height": 109}}, "c2": {"c2": {"type": "mesh", "uvs": [0.61313, 0.03092, 0.69441, 0.05439, 0.77288, 0.14603, 0.84581, 0.23119, 0.91282, 0.30944, 0.98401, 0.2825, 0.97741, 0.39151, 0.97111, 0.49558, 0.96372, 0.61764, 0.92532, 0.74049, 0.89097, 0.85039, 0.81934, 0.9728, 0.7879, 0.97574, 0.72549, 0.90556, 0.66846, 0.84145, 0.60598, 0.7712, 0.52599, 0.76521, 0.40782, 0.75637, 0.29703, 0.74808, 0.23322, 0.65447, 0.16555, 0.55521, 0.14182, 0.40978, 0.21843, 0.32223, 0.28623, 0.24475, 0.39562, 0.11973, 0.48226, 0.05394, 0.54026, 0.00988, 0.29841, 0.44968, 0.46221, 0.32752, 0.73521, 0.31505, 0.51351, 0.57932, 0.7683, 0.60675], "triangles": [30, 29, 31, 15, 16, 30, 17, 30, 16, 31, 15, 30, 14, 15, 31, 10, 31, 9, 13, 14, 31, 10, 13, 31, 11, 13, 10, 12, 13, 11, 28, 29, 30, 8, 31, 7, 29, 1, 2, 29, 2, 3, 6, 4, 5, 7, 4, 6, 0, 29, 28, 1, 29, 0, 4, 31, 29, 4, 29, 3, 31, 4, 7, 9, 31, 8, 28, 24, 25, 23, 24, 28, 25, 0, 28, 0, 25, 26, 27, 20, 21, 27, 23, 28, 22, 23, 27, 27, 21, 22, 27, 28, 30, 19, 20, 27, 18, 19, 27, 17, 18, 27, 30, 17, 27], "vertices": [2, 100, 30.55, 7.29, 0.2935, 102, -7.18, 7.98, 0.7065, 2, 100, 37.67, 2.2, 0.01815, 102, 1.56, 8.36, 0.98185, 1, 102, 11.12, 4.02, 1, 3, 102, 20.01, -0.02, 0.94257, 103, 26.2, 31.39, 2e-05, 104, 5.48, 35.14, 0.05741, 2, 102, 28.17, -3.72, 0.85842, 104, 14.39, 36.15, 0.14158, 2, 102, 35.06, -0.15, 0.8195, 104, 18.46, 42.76, 0.1805, 2, 102, 36.15, -7.75, 0.81913, 104, 23.3, 36.79, 0.18087, 2, 102, 37.19, -15.01, 0.74125, 104, 27.93, 31.1, 0.25875, 2, 102, 38.4, -23.53, 0.75782, 104, 33.35, 24.42, 0.24218, 2, 102, 36.44, -32.86, 0.67758, 104, 36.46, 15.41, 0.32242, 2, 102, 34.69, -41.2, 0.25688, 104, 39.24, 7.35, 0.74312, 2, 102, 29.3, -51.31, 0.16151, 104, 39.82, -4.09, 0.83849, 2, 102, 26.11, -52.28, 0.06105, 104, 37.58, -6.56, 0.93895, 2, 102, 18.55, -49.01, 0.01068, 104, 29.41, -7.64, 0.98932, 3, 99, 21.41, -48.94, 9e-05, 102, 11.65, -46.03, 0.00118, 104, 21.95, -8.63, 0.99873, 3, 99, 19.28, -40.98, 0.01239, 103, 22.98, -14.11, 0.01825, 104, 13.78, -9.71, 0.96936, 3, 99, 12.9, -35.4, 0.08726, 103, 15.42, -17.94, 0.17515, 104, 7.43, -15.32, 0.73759, 3, 99, 3.48, -27.17, 0.35062, 103, 4.26, -23.6, 0.35464, 104, -1.95, -23.6, 0.29473, 3, 99, -5.36, -19.45, 0.62764, 103, -6.2, -28.91, 0.25854, 104, -10.75, -31.36, 0.11382, 4, 99, -6.58, -10.11, 0.31852, 103, -15.32, -26.56, 0.04896, 104, -20.17, -31.37, 0.01651, 92, 12.29, -8.15, 0.616, 4, 99, -7.88, -0.2, 0.32408, 103, -24.99, -24.06, 0.00306, 104, -30.16, -31.38, 0.00086, 92, 18.44, -0.27, 0.672, 2, 99, -3.52, 9.36, 0.288, 92, 28.32, 3.33, 0.712, 1, 99, 6.65, 9.16, 1, 2, 99, 15.65, 8.98, 0.85994, 100, -7.13, 8.12, 0.14006, 3, 99, 30.17, 8.7, 0.02155, 100, 7.05, 11.26, 0.97605, 102, -28.13, -3.4, 0.0024, 2, 100, 17.31, 11.62, 0.84023, 102, -20.28, 3.21, 0.15977, 2, 100, 24.17, 11.86, 0.61912, 102, -15.02, 7.64, 0.38088, 3, 99, 7.74, -3.1, 0.94281, 103, -16.46, -10.65, 0.05119, 104, -25.27, -16.26, 0.00599, 2, 100, 7.35, -4.94, 0.68806, 103, -5.68, 5.39, 0.31194, 4, 100, 33.94, -16.22, 0.00309, 102, 9.98, -8.44, 0.77393, 103, 18.96, 20.47, 0.05728, 104, 1.22, 22.75, 0.16569, 3, 99, 19.95, -24.35, 0.06103, 103, 7.8, -7.27, 0.49104, 104, -2.62, -6.9, 0.44794, 3, 102, 18.12, -27.56, 0.40362, 103, 32.16, 4.43, 0.00183, 104, 18.02, 10.54, 0.59456], "hull": 27, "edges": [8, 10, 20, 22, 22, 24, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 2, 0, 0, 52, 2, 4, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 110, "height": 73}}, "txt": {"t": {"type": "mesh", "uvs": [0.45189, 0.01329, 0.53961, 0.01244, 0.59395, 0.04517, 0.59403, 0.15484, 0.82644, 0.17281, 0.88036, 0.18649, 0.98347, 0.18649, 0.99318, 0.42748, 0.96841, 0.84728, 0.9007, 0.87579, 0.79898, 0.95712, 0.71051, 0.9868, 0.2606, 0.98745, 0.06942, 0.86339, 0.01158, 0.83869, 0.04931, 0.66028, 0.01528, 0.57291, 0.01545, 0.50545, 0.0465, 0.45331, 0.0773, 0.42627, 0.00219, 0.29764, 0.02307, 0.18579, 0.20755, 0.18665, 0.40474, 0.13626, 0.40623, 0.05307], "triangles": [22, 20, 21, 19, 20, 22, 18, 16, 17, 18, 19, 16, 15, 16, 19, 7, 4, 5, 7, 5, 6, 12, 13, 15, 14, 15, 13, 24, 0, 1, 24, 1, 23, 3, 1, 2, 3, 23, 1, 8, 9, 7, 15, 3, 7, 4, 7, 3, 9, 10, 7, 10, 11, 7, 23, 19, 22, 19, 3, 15, 12, 15, 11, 23, 3, 19, 7, 11, 15], "vertices": [1, 12, 52.84, 2.51, 1, 1, 12, 52.95, -2.89, 1, 1, 12, 50.62, -6.25, 1, 1, 12, 42.71, -6.33, 1, 1, 12, 41.54, -20.64, 1, 1, 12, 40.59, -23.97, 1, 1, 12, 40.64, -30.31, 1, 1, 12, 23.27, -31.06, 1, 1, 12, -7.02, -29.81, 1, 1, 12, -9.11, -25.66, 1, 1, 12, -15.03, -19.46, 1, 1, 12, -17.22, -14.03, 1, 1, 12, -17.51, 13.65, 1, 1, 12, -8.67, 25.5, 1, 1, 12, -6.92, 29.07, 1, 1, 12, 5.96, 26.87, 1, 1, 12, 12.24, 29.02, 1, 1, 12, 17.11, 29.05, 1, 1, 12, 20.89, 27.17, 1, 1, 12, 22.85, 25.3, 1, 1, 12, 32.09, 30, 1, 1, 12, 40.16, 28.79, 1, 1, 12, 40.2, 17.44, 1, 1, 12, 43.95, 5.33, 1, 1, 12, 49.95, 5.3, 1], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 8, 10], "width": 64, "height": 75}}, "txps2": {"ps2": {"type": "mesh", "uvs": [0.35982, 0.33484, 0.37843, 0.40216, 0.39719, 0.47002, 0.41459, 0.53294, 0.37717, 0.53573, 0.34812, 0.48161, 0.32042, 0.42997, 0.33555, 0.52701, 0.35172, 0.63067, 0.36817, 0.73614, 0.38255, 0.82838, 0.39753, 0.92438, 0.35586, 0.89257, 0.29202, 0.8438, 0.23352, 0.79913, 0.243, 0.8673, 0.25131, 0.92714, 0.25938, 0.98515, 0.2211, 0.98522, 0.18732, 0.98527, 0.1631, 0.93035, 0.13886, 0.87539, 0.11119, 0.81266, 0.05279, 0.81349, 0.0137, 0.76447, 0.0137, 0.68517, 0.01369, 0.59939, 0.01369, 0.50217, 0.01368, 0.4106, 0.03622, 0.33051, 0.04789, 0.28901, 0.06692, 0.2214, 0.12508, 0.15548, 0.18056, 0.15607, 0.18165, 0.20312, 0.28099, 0.20263, 0.33563, 0.24733], "triangles": [19, 20, 18, 18, 16, 17, 18, 20, 16, 20, 15, 16, 20, 21, 15, 21, 14, 15, 21, 22, 14, 12, 10, 11, 12, 13, 10, 10, 13, 9, 13, 14, 9, 23, 24, 22, 14, 22, 25, 8, 9, 14, 8, 14, 25, 8, 25, 7, 24, 25, 22, 6, 25, 26, 6, 26, 27, 7, 25, 6, 6, 28, 29, 6, 29, 34, 29, 30, 34, 34, 35, 6, 4, 2, 3, 4, 5, 2, 6, 27, 28, 2, 5, 1, 5, 6, 1, 6, 0, 1, 0, 35, 36, 0, 6, 35, 30, 31, 34, 34, 32, 33, 34, 31, 32], "vertices": [2, 13, 2.23, 11.34, 0.99986, 14, -11.54, 15.38, 0.00014, 2, 13, 5.97, 13.55, 0.99637, 14, -7.33, 16.41, 0.00363, 2, 13, 9.73, 15.77, 0.99971, 14, -3.08, 17.44, 0.00029, 1, 13, 13.22, 17.83, 1, 1, 13, 13.99, 15.35, 1, 2, 13, 11.21, 12.63, 0.99721, 14, -2.58, 14.01, 0.00279, 2, 13, 8.56, 10.03, 0.94999, 14, -5.87, 12.29, 0.05001, 3, 13, 14.13, 12.43, 0.57005, 14, 0.15, 12.97, 0.42596, 15, -11.29, 16.11, 0.00398, 3, 13, 20.08, 14.98, 0.2027, 14, 6.59, 13.68, 0.73159, 15, -4.87, 15.26, 0.06571, 3, 13, 26.13, 17.59, 0.03753, 14, 13.14, 14.42, 0.73494, 15, 1.66, 14.39, 0.22753, 3, 13, 31.43, 19.86, 0.00216, 14, 18.87, 15.06, 0.67498, 15, 7.37, 13.63, 0.32286, 3, 13, 36.94, 22.23, 0, 14, 24.83, 15.72, 0.66246, 15, 13.32, 12.84, 0.33754, 2, 14, 22.69, 12.97, 0.66386, 15, 10.58, 10.68, 0.33614, 3, 13, 33.79, 13.98, 0.00121, 14, 19.42, 8.74, 0.64242, 15, 6.39, 7.37, 0.35637, 3, 13, 32.05, 9.41, 0.00193, 14, 16.43, 4.87, 0.37576, 15, 2.55, 4.33, 0.62231, 2, 14, 20.65, 5.26, 0.01007, 15, 6.75, 3.7, 0.98993, 1, 15, 10.43, 3.14, 1, 1, 15, 14.01, 2.59, 1, 1, 15, 13.21, 0.06, 1, 1, 15, 12.51, -2.17, 1, 1, 15, 8.78, -2.75, 1, 1, 15, 5.05, -3.33, 1, 2, 14, 16.73, -3.63, 0.26969, 15, 0.79, -3.99, 0.73031, 2, 14, 16.53, -7.67, 0.65625, 15, -0.37, -7.86, 0.34375, 2, 14, 13.35, -10.18, 0.80587, 15, -4.07, -9.54, 0.19413, 3, 13, 28.73, -7.02, 0, 14, 8.48, -9.88, 0.94917, 15, -8.72, -8.07, 0.05083, 3, 13, 23.59, -8.23, 0.05305, 14, 3.21, -9.55, 0.94605, 15, -13.75, -6.48, 0.0009, 2, 13, 17.77, -9.61, 0.43348, 14, -2.76, -9.18, 0.56652, 2, 13, 12.29, -10.91, 0.82698, 14, -8.39, -8.83, 0.17302, 2, 13, 7.13, -10.52, 0.97287, 14, -13.21, -6.96, 0.02713, 2, 13, 4.46, -10.33, 0.99505, 14, -15.71, -6, 0.00495, 1, 13, 0.11, -10, 1, 1, 13, -4.77, -7.02, 1, 1, 13, -5.62, -3.27, 1, 1, 13, -2.82, -2.53, 1, 1, 13, -4.43, 4.15, 1, 1, 13, -2.62, 8.47, 1], "hull": 37, "edges": [6, 8, 44, 46, 46, 48, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 34, 36, 36, 38, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 72, "height": 64}}, "tt2": {"t2": {"type": "mesh", "uvs": [0.64146, 0, 0.75544, 0.13774, 0.82424, 0.24518, 0.90648, 0.3736, 1, 0.58148, 1, 0.62132, 0.96557, 0.71541, 0.86938, 0.71543, 0.77796, 0.71545, 0.67191, 0.62373, 0.5809, 0.54502, 0.52148, 0.41894, 0.46439, 0.29778, 0.33375, 0.29779, 0.28219, 0.40498, 0.23253, 0.50826, 0.17551, 0.62681, 0.16829, 0.64693, 0.12359, 0.7665, 0.08229, 0.87696, 0.04086, 0.98776, 0.01625, 1, 0.01848, 0.98184, 0, 0.77165, 0, 0.65101, 0.0471, 0.51145, 0.09665, 0.36462, 0.13767, 0.24305, 0.21335, 0.07701, 0.36026, 0.04866, 0.50569, 0.02059, 0.61236, 0], "triangles": [21, 22, 20, 20, 22, 19, 22, 23, 19, 19, 23, 18, 23, 24, 18, 18, 24, 17, 24, 25, 17, 17, 25, 16, 16, 25, 15, 25, 26, 15, 15, 26, 14, 26, 27, 14, 14, 27, 13, 27, 28, 13, 13, 29, 12, 13, 28, 29, 12, 29, 30, 7, 8, 3, 4, 6, 7, 5, 6, 4, 2, 9, 10, 12, 31, 1, 7, 3, 4, 9, 2, 3, 2, 10, 1, 1, 31, 0, 9, 3, 8, 1, 10, 11, 1, 11, 12, 31, 12, 30], "vertices": [1, 84, 93.72, -24.69, 1, 1, 84, 61.92, -27.56, 1, 1, 84, 40.07, -26.73, 1, 1, 84, 13.95, -25.74, 1, 1, 84, -22.53, -18.1, 1, 1, 84, -26.89, -13.91, 1, 1, 84, -32.14, 1.25, 1, 1, 84, -18.04, 15.92, 1, 1, 84, -4.63, 29.86, 1, 2, 84, 20.96, 36.36, 0.99619, 85, -29.16, 79.23, 0.00381, 2, 84, 42.93, 41.95, 0.95924, 85, -12.52, 63.85, 0.04076, 2, 84, 65.45, 37.73, 0.80464, 85, -3.79, 42.66, 0.19536, 2, 84, 87.09, 33.67, 0.28365, 85, 4.59, 22.31, 0.71635, 2, 85, 31.73, 17.09, 0.82531, 86, -2.32, 26.69, 0.17469, 2, 85, 45.52, 31.02, 0.17992, 86, 16.8, 22.37, 0.82008, 2, 85, 58.8, 44.45, 0.00766, 86, 35.22, 18.21, 0.99234, 1, 86, 56.37, 13.44, 1, 1, 86, 59.77, 13.12, 1, 1, 86, 80.12, 10.83, 1, 1, 86, 98.92, 8.71, 1, 1, 86, 117.78, 6.59, 1, 1, 86, 121.39, 2.4, 1, 1, 86, 118.64, 1.85, 1, 1, 86, 90.25, -13.29, 1, 1, 86, 73.15, -19.88, 1, 1, 86, 49.78, -18.21, 1, 1, 86, 25.2, -16.45, 1, 2, 85, 70.89, 1.09, 0.00615, 86, 4.85, -15, 0.99385, 2, 85, 50.41, -20.66, 0.89455, 86, -24.45, -9.14, 0.10545, 1, 85, 19.08, -19.02, 1, 2, 84, 111.38, -1.82, 0.55747, 85, -11.94, -17.4, 0.44253, 2, 84, 97.99, -20.25, 0.99885, 85, -34.69, -16.21, 0.00115], "hull": 32, "edges": [0, 62, 0, 2, 6, 8, 8, 10, 10, 12, 24, 26, 32, 34, 40, 42, 42, 44, 44, 46, 46, 48, 54, 56, 52, 54, 48, 50, 50, 52, 26, 28, 28, 30, 30, 32, 34, 36, 36, 38, 38, 40, 56, 58, 58, 60, 60, 62, 2, 4, 4, 6, 20, 22, 22, 24, 16, 18, 18, 20, 12, 14, 14, 16], "width": 143, "height": 102}}, "tt3": {"t3": {"type": "mesh", "uvs": [0.63008, 0, 0.74657, 0.12485, 0.82028, 0.24073, 0.89256, 0.35435, 0.94067, 0.42997, 1, 0.58378, 1, 0.63995, 0.94556, 0.72027, 0.81276, 0.71621, 0.71359, 0.65437, 0.61311, 0.5917, 0.56077, 0.49591, 0.50895, 0.40107, 0.45654, 0.30518, 0.40082, 0.30425, 0.32939, 0.30306, 0.27401, 0.42235, 0.20993, 0.56037, 0.18264, 0.60786, 0.13458, 0.7417, 0.07984, 0.89413, 0.04247, 0.99817, 0.01599, 0.98921, 0.00894, 0.96146, 0.00757, 0.81961, 0.00586, 0.64092, 0.04424, 0.52471, 0.0824, 0.40913, 0.13218, 0.25841, 0.17275, 0.13556, 0.19435, 0.07016, 0.31616, 0.07169, 0.4022, 0.05119, 0.50794, 0.02599, 0.61701, 0], "triangles": [20, 21, 23, 21, 22, 23, 23, 24, 20, 20, 24, 19, 24, 25, 19, 19, 25, 18, 18, 25, 26, 18, 26, 17, 26, 27, 17, 17, 27, 16, 27, 28, 16, 16, 28, 15, 15, 28, 29, 31, 29, 30, 29, 31, 15, 14, 32, 13, 13, 32, 33, 32, 14, 31, 14, 15, 31, 6, 7, 5, 5, 7, 8, 4, 5, 8, 9, 3, 4, 9, 2, 3, 1, 2, 11, 2, 9, 10, 8, 9, 4, 13, 34, 1, 34, 0, 1, 11, 2, 10, 1, 11, 12, 1, 12, 13, 34, 13, 33], "vertices": [1, 87, 111.92, -33.13, 1, 1, 87, 76.44, -36.37, 1, 1, 87, 49.63, -33.71, 1, 1, 87, 23.35, -31.11, 1, 1, 87, 5.85, -29.38, 1, 1, 87, -22.88, -19.51, 1, 1, 87, -29.53, -12.34, 1, 1, 87, -29.35, 6.88, 1, 1, 87, -5.26, 28.23, 1, 1, 87, 19.68, 36.67, 1, 2, 87, 44.95, 45.21, 0.98895, 88, -23.09, 77.78, 0.01105, 2, 87, 65.59, 41.6, 0.93204, 88, -12.16, 59.91, 0.06796, 2, 87, 86.01, 38.02, 0.73011, 88, -1.34, 42.21, 0.26989, 2, 87, 106.67, 34.41, 0.22049, 88, 9.59, 24.32, 0.77951, 3, 87, 116.69, 43.47, 0.01723, 88, 23.01, 22.79, 0.97495, 89, 2.99, 40.4, 0.00782, 2, 88, 40.21, 20.82, 0.65655, 89, 10.55, 24.83, 0.34345, 2, 88, 55.66, 40.12, 0.04743, 89, 35.12, 22.11, 0.95257, 1, 89, 63.55, 18.97, 1, 1, 89, 73.9, 16.75, 1, 1, 89, 99.94, 16.75, 1, 1, 89, 129.6, 16.75, 1, 1, 89, 149.84, 16.75, 1, 1, 89, 151.32, 10.31, 1, 1, 89, 147.76, 6.62, 1, 1, 89, 125.83, -4.71, 1, 1, 89, 98.2, -18.99, 1, 1, 89, 75.95, -19.72, 1, 1, 89, 53.82, -20.44, 1, 1, 89, 24.97, -21.39, 1, 1, 89, 1.45, -22.16, 1, 2, 88, 68.65, -22.82, 0.01034, 89, -11.07, -22.57, 0.98966, 2, 88, 39.31, -19.56, 0.80363, 89, -24.03, 3.95, 0.19637, 2, 87, 146.37, 10.93, 0.00743, 88, 18.21, -20.99, 0.99257, 2, 87, 130.56, -9.7, 0.56651, 88, -7.73, -22.76, 0.43349, 2, 87, 114.24, -30.98, 0.99872, 88, -34.48, -24.57, 0.00128], "hull": 35, "edges": [0, 68, 0, 2, 8, 10, 10, 12, 12, 14, 14, 16, 34, 36, 42, 44, 44, 46, 60, 62, 54, 56, 50, 52, 52, 54, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 46, 48, 48, 50, 56, 58, 58, 60, 62, 64, 64, 66, 66, 68, 26, 28, 28, 30, 24, 26, 20, 22, 22, 24, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20], "width": 163, "height": 117}}, "txyingzi2": {"yingzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [177.83, -49.69, 117.33, -49.69, 117.33, -25.46, 177.83, -25.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 26, "height": 12}}, "txj3": {"j1": {"type": "mesh", "uvs": [0.86979, 0.02349, 0.95704, 0.12164, 0.95705, 0.21134, 0.87053, 0.22846, 0.8704, 0.28128, 0.87022, 0.36101, 0.89588, 0.43613, 0.92469, 0.52049, 0.90173, 0.60426, 0.88108, 0.67962, 0.85061, 0.79078, 0.82587, 0.88107, 0.65229, 0.97596, 0.2604, 0.97622, 0.26042, 0.88666, 0.38969, 0.78756, 0.26527, 0.70247, 0.1559, 0.62767, 0.08719, 0.58068, 0.01559, 0.53171, 0.04864, 0.44847, 0.09296, 0.3368, 0.13145, 0.23985, 0.17769, 0.12337, 0.23547, 0.04851, 0.43955, 0.04843, 0.66564, 0.02249], "triangles": [1, 26, 0, 23, 24, 25, 4, 25, 26, 17, 18, 20, 19, 20, 18, 7, 17, 6, 7, 16, 17, 6, 17, 20, 7, 8, 16, 9, 16, 8, 15, 16, 9, 10, 15, 9, 11, 15, 10, 12, 15, 11, 14, 15, 12, 13, 14, 12, 3, 1, 2, 1, 3, 26, 26, 3, 4, 25, 22, 23, 22, 25, 4, 22, 5, 21, 4, 5, 22, 6, 21, 5, 21, 6, 20], "vertices": [1, 22, 24.04, 0.43, 1, 1, 23, 1.19, 5.91, 1, 2, 23, 4.5, 7.16, 0.99889, 24, -7.87, 13.84, 0.00111, 2, 23, 5.81, 5.61, 0.99018, 24, -7.74, 11.82, 0.00982, 2, 23, 7.75, 6.35, 0.94922, 24, -5.74, 11.25, 0.05078, 2, 23, 10.69, 7.46, 0.80058, 24, -2.72, 10.39, 0.19942, 2, 23, 13.26, 9.05, 0.55182, 24, 0.29, 10.13, 0.44818, 2, 23, 16.14, 10.82, 0.29468, 24, 3.66, 9.84, 0.70532, 2, 23, 19.41, 11.52, 0.11688, 24, 6.7, 8.46, 0.88312, 2, 23, 22.35, 12.15, 0.02888, 24, 9.44, 7.21, 0.97112, 2, 23, 26.69, 13.08, 0.00013, 24, 13.47, 5.37, 0.99987, 1, 24, 16.75, 3.88, 1, 1, 24, 19.3, -0.83, 1, 1, 24, 16.96, -9.18, 1, 1, 24, 13.56, -8.22, 1, 1, 24, 10.58, -4.41, 1, 2, 23, 28.03, -0.26, 0.01266, 24, 6.6, -6.14, 0.98734, 2, 23, 26.13, -3.57, 0.15467, 24, 3.11, -7.67, 0.84533, 2, 23, 24.94, -5.65, 0.32922, 24, 0.91, -8.63, 0.67078, 2, 23, 23.7, -7.81, 0.47418, 24, -1.38, -9.63, 0.52582, 2, 23, 20.37, -8.3, 0.69297, 24, -4.34, -8.03, 0.30703, 2, 23, 15.91, -8.94, 0.95844, 24, -8.31, -5.89, 0.04156, 2, 23, 12.03, -9.51, 0.99983, 24, -11.75, -4.03, 0.00017, 1, 23, 7.38, -10.18, 1, 2, 23, 4.16, -10.03, 0.04, 22, 21.18, -13.34, 0.96, 1, 22, 22.4, -8.99, 1, 1, 22, 22.78, -3.9, 1], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 22, 24, 24, 26, 26, 28, 28, 30, 46, 48, 48, 50, 50, 52, 20, 22, 18, 20, 14, 16, 16, 18, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 10, 12, 12, 14, 6, 8, 8, 10, 34, 36, 36, 38], "width": 23, "height": 41}}, "z2": {"z2": {"type": "mesh", "uvs": [0.41256, 0.09456, 0.62392, 0.02375, 0.85109, 0.097, 0.85085, 0.29592, 0.8506, 0.50187, 0.78233, 0.64056, 0.72865, 0.74962, 0.53228, 0.80597, 0.32661, 0.86499, 0.10631, 0.92821, 0.04925, 0.6993, 0.04932, 0.52011, 0.04942, 0.2837, 0.24518, 0.15065, 0.21418, 0.46111, 0.48853, 0.38441, 0.67732, 0.37261], "triangles": [9, 10, 8, 8, 10, 14, 10, 11, 14, 8, 14, 7, 14, 15, 7, 6, 7, 5, 5, 7, 15, 16, 5, 15, 5, 16, 4, 4, 16, 3, 16, 15, 1, 11, 12, 14, 14, 13, 15, 14, 12, 13, 13, 0, 15, 15, 0, 1, 3, 16, 2, 2, 16, 1], "vertices": [1, 110, 5.25, 3.74, 1, 2, 110, 9.42, 4.73, 0.99227, 111, -6.2, -1.8, 0.00773, 2, 110, 13.65, 2.94, 0.832, 111, -7.39, 2.64, 0.168, 2, 110, 13.3, -0.87, 0.48147, 111, -4.17, 4.71, 0.51853, 2, 110, 12.94, -4.81, 0.08232, 111, -0.84, 6.85, 0.91768, 2, 110, 11.4, -7.35, 0.00442, 111, 2.11, 7.2, 0.99558, 1, 111, 4.43, 7.47, 1, 1, 111, 7.39, 4.88, 1, 1, 111, 10.49, 2.18, 1, 2, 110, -2.04, -11.71, 0.00097, 111, 13.81, -0.72, 0.99903, 2, 110, -2.74, -7.23, 0.1326, 111, 10.71, -4.03, 0.8674, 2, 110, -2.43, -3.79, 0.50771, 111, 7.81, -5.9, 0.49229, 2, 110, -2.03, 0.73, 0.99157, 111, 3.99, -8.37, 0.00843, 1, 110, 1.95, 2.95, 1, 2, 110, 0.82, -2.95, 0.57122, 111, 5.14, -3.85, 0.42878, 2, 110, 6.21, -1.95, 0.0431, 111, 1.04, -0.22, 0.9569, 2, 110, 9.85, -2.04, 0.21643, 111, -1.12, 2.7, 0.78357], "hull": 14, "edges": [2, 4, 18, 20, 24, 26, 2, 0, 0, 26, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24], "width": 20, "height": 20}}, "tj1": {"xj1": {"type": "mesh", "uvs": [0.58194, 0.014, 0.76725, 0.08387, 0.97237, 0.27141, 1, 0.35724, 1, 0.42638, 0.89652, 0.48425, 0.80741, 0.53408, 0.67641, 0.60734, 0.65993, 0.6812, 0.57728, 0.82296, 0.50548, 0.86004, 0.41834, 0.90503, 0.29308, 1, 0.25994, 1, 0.23961, 0.98571, 0.29246, 0.86853, 0.34665, 0.74836, 0.3034, 0.6119, 0.17681, 0.67537, 0.10491, 0.81803, 0.07746, 0.97828, 0.03871, 0.94711, 0.00523, 0.80081, 0.00519, 0.55424, 0.06713, 0.31568, 0.19021, 0.1096, 0.29527, 0.02204, 0.42655, 0.00229, 0.33293, 0.51987, 0.39658, 0.41587, 0.52785, 0.35187, 0.64322, 0.39721], "triangles": [13, 15, 12, 12, 15, 11, 13, 14, 15, 15, 16, 11, 11, 16, 10, 10, 16, 9, 9, 16, 8, 29, 8, 28, 7, 8, 30, 17, 28, 16, 8, 16, 28, 20, 21, 19, 21, 22, 19, 19, 22, 18, 22, 23, 18, 28, 17, 18, 24, 28, 18, 24, 18, 23, 29, 28, 25, 28, 24, 25, 25, 26, 29, 29, 26, 27, 30, 8, 29, 7, 30, 31, 7, 31, 6, 6, 31, 5, 3, 4, 5, 2, 5, 1, 5, 2, 3, 29, 27, 30, 5, 31, 1, 1, 31, 0, 31, 30, 0, 30, 27, 0], "vertices": [2, 69, 127.86, -88.57, 0.90123, 70, -118.32, -15.42, 0.09877, 2, 69, 63.41, -67.71, 0.99903, 70, -157.47, 39.87, 0.00097, 1, 69, -6.82, -15.72, 1, 1, 69, -15.66, 7.17, 1, 1, 69, -14.96, 25.3, 1, 2, 69, 22.02, 39.07, 0.99241, 71, -55.14, 122.01, 0.00759, 2, 69, 53.86, 50.93, 0.90716, 71, -29.65, 99.54, 0.09284, 2, 69, 100.68, 68.36, 0.34559, 71, 7.82, 66.51, 0.65441, 2, 69, 107.22, 87.51, 0.11861, 71, 27.79, 69.78, 0.88139, 2, 69, 137.73, 123.56, 0.00083, 71, 73.98, 59.92, 0.99917, 1, 71, 93.8, 41.46, 1, 1, 71, 117.84, 19.06, 1, 1, 71, 159.55, -9.67, 1, 1, 71, 164.65, -20.15, 1, 1, 71, 164.41, -28.23, 1, 1, 71, 128.61, -24.97, 1, 2, 70, 67.33, 82.31, 0.03266, 71, 91.91, -21.63, 0.96734, 3, 69, 231.89, 64.47, 0.0089, 70, 56.62, 44.89, 0.57371, 71, 66.36, -50.99, 0.41739, 2, 70, 101.73, 29.81, 0.9913, 71, 100.84, -83.75, 0.0087, 1, 70, 144.96, 43, 1, 1, 70, 178.94, 69.61, 1, 1, 70, 184.39, 54.67, 1, 1, 70, 169.38, 17.41, 1, 1, 70, 128.67, -32.9, 1, 1, 70, 72.32, -67.86, 1, 2, 69, 266.59, -68.83, 0.04453, 70, 4.62, -82.66, 0.95547, 2, 69, 228.76, -90.37, 0.23854, 70, -38.58, -77.26, 0.76146, 2, 69, 182.39, -93.75, 0.58295, 70, -77.75, -52.21, 0.41705, 3, 69, 220.58, 40.73, 0.05594, 70, 33.34, 32.65, 0.6206, 71, 40.09, -52.23, 0.32346, 3, 69, 197.14, 14.32, 0.50438, 70, -1.24, 25.53, 0.32419, 71, 5.74, -44.04, 0.17142, 2, 69, 150.32, -0.68, 0.99963, 70, -47.72, 41.54, 0.00037, 2, 69, 110.21, 12.79, 0.88861, 71, -36.66, 31.86, 0.11139], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 18, 20, 20, 22, 12, 14, 8, 10, 10, 12, 28, 30, 30, 32], "width": 237, "height": 177}}, "txyingzi": {"yingzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [168.16, -49.69, 107.66, -49.69, 107.66, -25.46, 168.16, -25.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 26, "height": 12}}, "txs6": {"s1": {"type": "mesh", "uvs": [0.3364, 0.2361, 0.40643, 0.11906, 0.45731, 0.03405, 0.48755, 0.02733, 0.53179, 0.01749, 0.51479, 0.10703, 0.49773, 0.19681, 0.48096, 0.28512, 0.60472, 0.28499, 0.73107, 0.28486, 0.84987, 0.28475, 0.97311, 0.28462, 0.97291, 0.36151, 0.97272, 0.43696, 0.97244, 0.54438, 0.89538, 0.61652, 0.82832, 0.6793, 0.73377, 0.73838, 0.62666, 0.80531, 0.53464, 0.86282, 0.41621, 0.93682, 0.33067, 0.99027, 0.22509, 0.93099, 0.15609, 0.89225, 0.07923, 0.76402, 0.0147, 0.65636, 0.05694, 0.49949, 0.17033, 0.35558, 0.28565, 0.32091, 0.56666, 0.36154, 0.50443, 0.48411], "triangles": [12, 10, 11, 29, 7, 8, 13, 10, 12, 13, 15, 10, 14, 15, 13, 15, 16, 9, 15, 9, 10, 16, 29, 9, 17, 29, 16, 9, 29, 8, 17, 30, 29, 18, 30, 17, 5, 3, 4, 5, 2, 3, 5, 1, 2, 6, 1, 5, 7, 1, 6, 0, 1, 7, 30, 7, 29, 24, 26, 27, 25, 26, 24, 19, 30, 18, 28, 23, 24, 28, 0, 7, 28, 7, 30, 28, 24, 27, 30, 23, 28, 22, 23, 30, 20, 22, 30, 19, 20, 30, 21, 22, 20], "vertices": [2, 46, 7.1, 11.99, 0.8098, 47, -3.04, 13.69, 0.1902, 2, 46, 9.77, 14.88, 0.7733, 47, 0.62, 15.12, 0.2267, 2, 46, 11.71, 16.98, 0.77548, 47, 3.28, 16.17, 0.22452, 2, 46, 12.74, 17.05, 0.77596, 47, 4.24, 15.78, 0.22404, 2, 46, 14.25, 17.16, 0.77643, 47, 5.65, 15.22, 0.22357, 2, 46, 13.43, 14.83, 0.77259, 47, 3.89, 13.48, 0.22741, 2, 46, 12.61, 12.48, 0.75282, 47, 2.13, 11.72, 0.24718, 2, 46, 11.8, 10.17, 0.65733, 47, 0.39, 10, 0.34267, 2, 46, 15.94, 9.75, 0.23691, 47, 3.94, 7.81, 0.76309, 2, 46, 20.17, 9.31, 0.02976, 47, 7.55, 5.57, 0.97024, 2, 46, 24.15, 8.9, 1e-05, 47, 10.95, 3.47, 0.99999, 1, 47, 14.48, 1.28, 1, 1, 47, 13.38, -0.47, 1, 1, 47, 12.31, -2.19, 1, 1, 47, 10.77, -4.65, 1, 1, 47, 7.55, -4.93, 1, 1, 47, 4.74, -5.18, 1, 2, 46, 19, -2.84, 0.05951, 47, 1.2, -4.85, 0.94049, 2, 46, 15.23, -4.26, 0.60104, 47, -2.82, -4.49, 0.39896, 2, 46, 11.99, -5.48, 0.96329, 47, -6.27, -4.17, 0.03671, 1, 46, 7.82, -7.05, 1, 1, 46, 4.81, -8.19, 1, 1, 46, 1.44, -6.23, 1, 1, 46, -0.76, -4.95, 1, 1, 46, -2.98, -1.25, 1, 1, 46, -4.84, 1.85, 1, 2, 46, -2.99, 5.91, 0.99982, 47, -14.77, 12.61, 0.00018, 2, 46, 1.21, 9.37, 0.9786, 47, -9.49, 13.9, 0.0214, 2, 46, 5.16, 9.89, 0.89684, 47, -5.7, 12.65, 0.10316, 2, 46, 14.46, 7.83, 0.34532, 47, 1.76, 6.73, 0.65468, 2, 46, 12.03, 4.76, 0.63541, 47, -1.76, 5.03, 0.36459], "hull": 29, "edges": [50, 52, 52, 54, 54, 56, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 56, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 4, 6, 6, 8], "width": 35, "height": 28}}, "txhuo": {"huo": {"type": "mesh", "uvs": [0.56224, 0.01844, 0.57131, 0.0399, 0.56988, 0.0611, 0.56474, 0.08177, 0.54808, 0.10472, 0.52864, 0.12241, 0.51186, 0.13769, 0.51001, 0.14472, 0.51167, 0.15815, 0.52789, 0.18177, 0.53481, 0.18555, 0.5519, 0.18431, 0.56511, 0.17772, 0.58266, 0.15904, 0.59839, 0.1423, 0.59169, 0.11865, 0.60154, 0.09661, 0.61687, 0.07149, 0.62929, 0.06133, 0.62971, 0.06286, 0.61607, 0.12837, 0.63094, 0.13043, 0.64716, 0.11637, 0.64598, 0.08141, 0.64396, 0.07931, 0.65752, 0.06805, 0.67994, 0.06243, 0.67974, 0.06584, 0.66698, 0.08508, 0.67157, 0.10352, 0.67539, 0.11887, 0.67355, 0.14446, 0.67255, 0.15832, 0.66019, 0.17339, 0.6458, 0.19092, 0.64435, 0.22399, 0.65079, 0.25958, 0.6568, 0.29276, 0.64948, 0.31271, 0.64314, 0.32999, 0.64413, 0.33993, 0.65206, 0.34475, 0.67807, 0.3343, 0.68647, 0.32902, 0.69356, 0.31313, 0.68457, 0.28943, 0.70135, 0.26667, 0.71809, 0.24397, 0.70073, 0.22494, 0.7068, 0.20451, 0.71321, 0.18292, 0.71554, 0.17868, 0.72057, 0.17791, 0.72977, 0.1929, 0.72752, 0.21239, 0.72528, 0.23186, 0.73788, 0.24917, 0.72943, 0.27757, 0.72397, 0.29592, 0.73533, 0.31803, 0.74733, 0.34138, 0.74487, 0.35854, 0.73471, 0.38043, 0.71193, 0.40155, 0.71022, 0.40164, 0.71008, 0.38666, 0.68929, 0.386, 0.67267, 0.39178, 0.66335, 0.40056, 0.65768, 0.41348, 0.65811, 0.44255, 0.64721, 0.47525, 0.63484, 0.49159, 0.63982, 0.50405, 0.67151, 0.54929, 0.68414, 0.53813, 0.68787, 0.51898, 0.69486, 0.48307, 0.707, 0.4452, 0.72894, 0.42814, 0.75421, 0.41938, 0.75225, 0.39525, 0.76085, 0.37124, 0.77215, 0.36692, 0.77353, 0.37779, 0.77329, 0.40531, 0.74192, 0.43755, 0.73949, 0.44221, 0.73019, 0.47009, 0.73049, 0.50035, 0.73105, 0.50334, 0.7403, 0.53937, 0.74805, 0.56956, 0.75613, 0.59862, 0.75999, 0.61314, 0.76729, 0.61484, 0.77336, 0.63235, 0.7789, 0.64421, 0.81815, 0.63473, 0.84138, 0.62912, 0.90327, 0.64065, 0.94981, 0.69896, 0.95832, 0.71052, 0.95657, 0.72271, 0.99506, 0.73104, 0.99919, 0.7358, 0.99882, 0.79506, 0.98766, 0.80968, 0.99912, 0.82396, 0.99728, 0.83087, 0.96598, 0.87813, 0.92792, 0.87603, 0.9325, 0.88559, 0.9516, 0.90767, 0.94514, 0.91114, 0.82547, 0.96233, 0.81197, 0.9612, 0.76917, 0.94979, 0.75603, 0.94878, 0.75435, 0.97232, 0.73215, 0.97634, 0.62017, 0.99968, 0.60555, 1, 0.5905, 0.9901, 0.55776, 0.98996, 0.46565, 0.99313, 0.39368, 0.99405, 0.38121, 0.98273, 0.35889, 0.98315, 0.26551, 0.96729, 0.25887, 0.9687, 0.23428, 0.97409, 0.14809, 0.94433, 0.149, 0.93183, 0.08153, 0.9007, 0.0666, 0.8594, 0.06124, 0.85521, 0.00583, 0.83125, 0, 0.73347, 0.01048, 0.71372, 0.0405, 0.62517, 0.1165, 0.61368, 0.17344, 0.62157, 0.19195, 0.62378, 0.2144, 0.59542, 0.23781, 0.58774, 0.25744, 0.58777, 0.27128, 0.58987, 0.26871, 0.55789, 0.26554, 0.51841, 0.25537, 0.48589, 0.24795, 0.46211, 0.24655, 0.44222, 0.26159, 0.42842, 0.26362, 0.40559, 0.27395, 0.38569, 0.27874, 0.38305, 0.28233, 0.41751, 0.27094, 0.43409, 0.27707, 0.45193, 0.30867, 0.47411, 0.32219, 0.49813, 0.32655, 0.50583, 0.33571, 0.51165, 0.34936, 0.51317, 0.36455, 0.5075, 0.37228, 0.50107, 0.38285, 0.48447, 0.38258, 0.47127, 0.37608, 0.46172, 0.34194, 0.43296, 0.32994, 0.4133, 0.3279, 0.38308, 0.32599, 0.35493, 0.32344, 0.35086, 0.32186, 0.32582, 0.32005, 0.297, 0.31976, 0.27488, 0.31946, 0.25206, 0.3273, 0.20841, 0.3283, 0.19827, 0.3145, 0.18723, 0.32808, 0.17638, 0.3343, 0.16894, 0.32639, 0.15193, 0.34563, 0.15261, 0.34526, 0.17422, 0.33868, 0.20034, 0.32914, 0.1985, 0.33573, 0.23579, 0.34222, 0.29279, 0.34818, 0.319, 0.36752, 0.35052, 0.3811, 0.35113, 0.37489, 0.30762, 0.36155, 0.2781, 0.35642, 0.25379, 0.36035, 0.21465, 0.37878, 0.17681, 0.3831, 0.16389, 0.38265, 0.10709, 0.38432, 0.09793, 0.40809, 0.06976, 0.4125, 0.07112, 0.405, 0.09495, 0.40437, 0.10361, 0.41654, 0.1334, 0.42816, 0.12869, 0.44941, 0.10171, 0.48016, 0.08971, 0.50229, 0.08107, 0.51272, 0.07366, 0.53227, 0.05207, 0.5351, 0.04597, 0.54066, 0.02951, 0.54126, 0.02768, 0.54126, 0, 0.54511, 0.00069, 0.48364, 0.11596, 0.46115, 0.17285, 0.41105, 0.213, 0.44785, 0.24646, 0.62883, 0.17703, 0.6002, 0.24312, 0.58793, 0.31673, 0.63701, 0.38533, 0.37321, 0.41127, 0.34254, 0.35103, 0.30361, 0.51947], "triangles": [215, 216, 217, 215, 217, 0, 215, 0, 1, 214, 215, 1, 213, 214, 1, 2, 213, 1, 212, 213, 2, 27, 25, 26, 17, 18, 19, 20, 17, 19, 23, 24, 25, 3, 212, 2, 211, 212, 3, 28, 25, 27, 23, 25, 28, 204, 202, 203, 204, 201, 202, 205, 201, 204, 4, 211, 3, 200, 201, 205, 218, 209, 210, 208, 209, 218, 29, 22, 23, 29, 23, 28, 20, 16, 17, 22, 29, 30, 4, 210, 211, 5, 210, 4, 218, 210, 5, 15, 16, 20, 6, 218, 5, 14, 15, 20, 31, 22, 30, 21, 22, 31, 7, 218, 6, 32, 21, 31, 206, 199, 200, 206, 200, 205, 183, 184, 185, 218, 207, 208, 219, 218, 7, 219, 7, 8, 219, 207, 218, 33, 21, 32, 186, 183, 185, 21, 14, 20, 222, 21, 33, 222, 14, 21, 13, 14, 222, 34, 222, 33, 182, 188, 180, 186, 182, 183, 181, 182, 180, 182, 187, 188, 186, 187, 182, 50, 51, 52, 53, 50, 52, 49, 50, 53, 179, 180, 188, 189, 179, 188, 54, 49, 53, 199, 219, 198, 220, 198, 219, 206, 207, 219, 219, 199, 206, 197, 198, 220, 35, 222, 34, 48, 49, 54, 55, 48, 54, 222, 12, 13, 223, 222, 35, 223, 12, 222, 11, 12, 223, 47, 48, 55, 221, 220, 219, 219, 8, 9, 221, 219, 9, 47, 55, 56, 178, 179, 189, 190, 178, 189, 223, 35, 36, 190, 177, 178, 57, 47, 56, 46, 47, 57, 220, 196, 197, 221, 196, 220, 221, 195, 196, 58, 46, 57, 45, 46, 58, 176, 177, 190, 194, 195, 221, 223, 37, 224, 37, 223, 36, 44, 45, 58, 223, 10, 11, 38, 224, 37, 44, 58, 59, 176, 190, 191, 175, 176, 191, 39, 224, 38, 221, 9, 10, 227, 174, 175, 191, 227, 175, 192, 227, 191, 193, 194, 221, 173, 174, 227, 60, 44, 59, 82, 83, 84, 44, 61, 43, 60, 61, 44, 43, 65, 42, 42, 65, 66, 172, 173, 227, 40, 225, 224, 40, 224, 39, 225, 40, 41, 65, 43, 61, 41, 42, 66, 67, 225, 41, 62, 65, 61, 66, 67, 41, 81, 82, 84, 68, 225, 67, 63, 65, 62, 64, 65, 63, 85, 81, 84, 226, 192, 193, 226, 171, 172, 172, 227, 192, 192, 226, 172, 69, 225, 68, 155, 156, 157, 154, 155, 157, 80, 81, 85, 153, 154, 157, 170, 171, 226, 158, 153, 157, 86, 79, 80, 86, 80, 85, 87, 79, 86, 225, 169, 226, 170, 226, 169, 223, 221, 10, 224, 221, 223, 152, 159, 151, 158, 152, 153, 159, 152, 158, 87, 78, 79, 88, 78, 87, 224, 225, 226, 224, 193, 221, 224, 226, 193, 168, 169, 225, 70, 71, 225, 70, 225, 69, 72, 168, 225, 77, 78, 88, 72, 167, 168, 150, 151, 159, 150, 159, 160, 225, 71, 72, 77, 88, 89, 228, 149, 150, 160, 228, 150, 76, 77, 89, 76, 89, 90, 161, 228, 160, 228, 161, 162, 76, 90, 91, 75, 76, 91, 148, 149, 228, 228, 162, 163, 228, 163, 164, 164, 147, 148, 103, 101, 102, 105, 103, 104, 106, 107, 103, 105, 106, 103, 109, 107, 108, 136, 137, 138, 139, 140, 141, 142, 139, 141, 143, 139, 142, 136, 139, 143, 136, 138, 139, 143, 144, 145, 146, 143, 145, 147, 143, 146, 135, 136, 143, 135, 133, 134, 103, 98, 101, 101, 98, 100, 111, 103, 107, 111, 107, 109, 110, 111, 109, 135, 143, 133, 114, 112, 113, 143, 129, 133, 73, 166, 167, 73, 167, 72, 74, 165, 166, 74, 166, 73, 147, 164, 165, 93, 75, 92, 94, 75, 93, 74, 75, 94, 96, 94, 95, 97, 94, 96, 98, 99, 100, 103, 97, 98, 111, 97, 103, 118, 97, 111, 117, 118, 111, 116, 117, 111, 112, 116, 111, 112, 115, 116, 114, 115, 112, 147, 129, 143, 133, 131, 132, 164, 148, 228, 165, 74, 147, 118, 129, 147, 129, 130, 133, 74, 94, 97, 130, 131, 133, 118, 74, 97, 92, 75, 91, 119, 120, 118, 74, 118, 147, 118, 127, 129, 118, 124, 127, 128, 129, 127, 118, 123, 124, 118, 121, 123, 125, 127, 124, 126, 127, 125, 120, 121, 118, 122, 123, 121], "vertices": [25.7, 896.93, 33.08, 875.59, 31.92, 854.51, 27.74, 833.96, 14.18, 811.14, -1.62, 793.55, -15.28, 778.36, -16.78, 771.37, -15.43, 758.02, -2.24, 734.54, 3.39, 730.77, 17.3, 732.01, 28.04, 738.57, 42.31, 757.14, 55.11, 773.78, 49.66, 797.29, 57.67, 819.21, 70.15, 844.18, 80.25, 854.29, 80.59, 852.76, 69.49, 787.63, 81.59, 785.58, 94.78, 799.56, 93.82, 834.32, 92.18, 836.4, 103.21, 847.6, 121.45, 853.19, 121.28, 849.8, 110.9, 830.67, 114.64, 812.34, 117.75, 797.07, 116.25, 771.63, 115.44, 757.85, 105.38, 742.87, 93.68, 725.44, 92.5, 692.56, 97.74, 657.18, 102.62, 624.19, 96.67, 604.36, 91.51, 587.17, 92.31, 577.29, 98.77, 572.5, 119.93, 582.89, 126.76, 588.14, 132.53, 603.93, 125.21, 627.5, 138.87, 650.13, 152.48, 672.69, 138.36, 691.61, 143.3, 711.93, 148.52, 733.39, 150.41, 737.6, 154.5, 738.37, 161.98, 723.47, 160.16, 704.09, 158.33, 684.73, 168.58, 667.52, 161.71, 639.29, 157.27, 621.05, 166.51, 599.06, 176.27, 575.85, 174.27, 558.78, 166, 537.02, 147.47, 516.02, 146.08, 515.93, 145.97, 530.83, 129.05, 531.48, 115.54, 525.74, 107.96, 517.01, 103.34, 504.16, 103.69, 475.26, 94.83, 442.75, 84.76, 426.5, 88.81, 414.12, 114.59, 369.13, 124.86, 380.23, 127.9, 399.27, 133.59, 434.98, 143.46, 472.63, 161.31, 489.59, 181.87, 498.3, 180.27, 522.29, 187.27, 546.16, 196.46, 550.45, 197.58, 539.65, 197.39, 512.29, 171.86, 480.24, 169.89, 475.6, 162.32, 447.88, 162.57, 417.79, 163.02, 414.82, 170.55, 379, 176.85, 348.98, 183.43, 320.09, 186.56, 305.66, 192.5, 303.97, 197.44, 286.56, 201.95, 274.76, 233.88, 284.19, 252.77, 289.76, 303.12, 278.3, 340.98, 220.33, 347.9, 208.84, 346.48, 196.71, 377.79, 188.43, 381.15, 183.7, 380.84, 124.78, 371.77, 110.25, 381.09, 96.06, 379.59, 89.18, 354.13, 42.2, 323.17, 44.29, 326.89, 34.77, 342.43, 12.82, 337.18, 9.37, 239.83, -41.52, 228.85, -40.4, 194.03, -29.05, 183.35, -28.05, 181.98, -51.45, 163.92, -55.45, 72.83, -78.66, 60.93, -78.97, 48.69, -69.13, 22.06, -68.99, -52.87, -72.14, -111.41, -73.05, -121.56, -61.81, -139.71, -62.22, -215.68, -46.45, -221.08, -47.86, -241.08, -53.21, -311.19, -23.62, -310.45, -11.2, -365.34, 19.75, -377.48, 60.81, -381.84, 64.98, -426.91, 88.81, -431.66, 186.02, -423.13, 205.66, -398.71, 293.7, -336.89, 305.12, -290.57, 297.28, -275.51, 295.08, -257.25, 323.27, -238.2, 330.9, -222.24, 330.88, -210.98, 328.79, -213.07, 360.59, -215.65, 399.83, -223.92, 432.18, -229.96, 455.81, -231.09, 475.59, -218.86, 489.31, -217.21, 512, -208.81, 531.8, -204.91, 534.42, -201.99, 500.15, -211.25, 483.67, -206.27, 465.93, -180.56, 443.88, -169.56, 420, -166.02, 412.34, -158.57, 406.56, -147.47, 405.04, -135.11, 410.68, -128.82, 417.07, -120.22, 433.58, -120.44, 446.71, -125.73, 456.2, -153.5, 484.8, -163.26, 504.34, -164.93, 534.39, -166.47, 562.37, -168.55, 566.42, -169.83, 591.32, -171.31, 619.97, -171.54, 641.96, -171.79, 664.65, -165.41, 708.05, -164.6, 718.13, -175.82, 729.11, -164.78, 739.9, -159.72, 747.29, -166.15, 764.2, -150.5, 763.53, -150.8, 742.04, -156.16, 716.08, -163.92, 717.9, -158.55, 680.83, -153.27, 624.16, -148.42, 598.1, -132.7, 566.76, -121.64, 566.16, -126.7, 609.42, -137.55, 638.76, -141.73, 662.93, -138.53, 701.85, -123.54, 739.47, -120.02, 752.31, -120.39, 808.78, -119.03, 817.89, -99.69, 845.9, -96.11, 844.55, -102.2, 820.86, -102.72, 812.24, -92.81, 782.63, -83.37, 787.31, -66.08, 814.14, -41.06, 826.07, -23.07, 834.66, -14.57, 842.03, 1.33, 863.48, 3.63, 869.55, 8.15, 885.92, 8.64, 887.74, 8.63, 915.26, 11.77, 914.57, -38.23, 799.97, -56.53, 743.41, -93.8, 703.48, -61.06, 670.22, 79.87, 739.25, 52.39, 673.54, 46.6, 600.35, 82.34, 532.15, -119.68, 506.36, -148.13, 566.25, -184.68, 398.78], "hull": 218, "edges": [0, 434, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 68, 70, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 94, 96, 100, 102, 102, 104, 104, 106, 110, 112, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 316, 316, 318, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 346, 348, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 376, 378, 378, 380, 380, 382, 382, 384, 384, 386, 386, 388, 388, 390, 390, 392, 392, 394, 394, 396, 396, 398, 398, 400, 400, 402, 402, 404, 404, 406, 406, 408, 408, 410, 410, 412, 412, 414, 414, 416, 420, 422, 422, 424, 424, 426, 426, 428, 428, 430, 430, 432, 432, 434, 298, 300, 300, 302, 294, 296, 296, 298, 342, 344, 344, 346, 416, 418, 418, 420, 8, 10, 10, 12, 24, 26, 26, 28, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 78, 116, 118, 118, 120, 90, 92, 92, 94, 112, 114, 114, 116, 106, 108, 108, 110, 96, 98, 98, 100, 180, 182, 182, 184, 150, 152, 152, 154, 194, 196, 196, 198, 348, 350, 350, 352, 352, 354, 354, 356], "width": 548, "height": 579}}, "ttt3": {"tt3": {"type": "mesh", "uvs": [0.45287, 0, 0.55908, 0.03506, 0.67406, 0.073, 0.8031, 0.11559, 0.87261, 0.1843, 0.91457, 0.33352, 0.93893, 0.42786, 0.97338, 0.56135, 1, 0.66445, 0.99214, 0.77026, 0.98322, 0.89044, 0.95248, 1, 0.93831, 1, 0.88474, 0.83937, 0.84038, 0.70635, 0.79416, 0.56778, 0.74865, 0.43132, 0.71135, 0.31948, 0.63115, 0.3037, 0.56955, 0.29158, 0.5079, 0.38316, 0.44244, 0.48042, 0.37208, 0.53312, 0.30408, 0.58405, 0.10483, 0.63222, 0, 0.56656, 0, 0.52518, 0.00848, 0.43772, 0.11776, 0.30093, 0.24301, 0.14416, 0.32315, 0.07513, 0.41036, 0], "triangles": [11, 12, 10, 12, 13, 10, 10, 13, 9, 13, 14, 9, 9, 14, 8, 14, 7, 8, 14, 15, 7, 15, 6, 7, 15, 16, 6, 16, 5, 6, 16, 17, 5, 17, 4, 5, 17, 3, 4, 3, 17, 2, 17, 18, 2, 18, 19, 2, 19, 1, 2, 24, 28, 23, 27, 24, 26, 24, 27, 28, 23, 28, 22, 26, 24, 25, 21, 22, 29, 22, 28, 29, 21, 29, 20, 20, 29, 30, 19, 20, 31, 31, 20, 30, 19, 0, 1, 0, 19, 31], "vertices": [1, 78, 99.85, 35.37, 1, 2, 78, 118.02, 16.8, 0.76522, 79, -14.1, 25.42, 0.23478, 2, 78, 137.69, -3.3, 0.05348, 79, 14.01, 24.74, 0.94652, 1, 79, 45.56, 23.98, 1, 2, 79, 64.55, 15.18, 0.82287, 80, -16.79, 22.61, 0.17713, 2, 79, 80.7, -9.89, 0.04232, 80, 12.99, 20.98, 0.95768, 1, 80, 31.62, 19.47, 1, 1, 80, 57.98, 17.34, 1, 1, 80, 78.34, 15.7, 1, 1, 80, 96.04, 6.33, 1, 1, 80, 116.14, -4.32, 1, 1, 80, 132.42, -18.96, 1, 1, 80, 131.13, -22.05, 1, 1, 80, 98.31, -22.13, 1, 1, 80, 71.14, -22.2, 1, 1, 80, 42.83, -22.27, 1, 2, 79, 46.77, -36.89, 0.01953, 80, 14.95, -22.33, 0.98047, 2, 79, 33.32, -18.42, 0.66507, 80, -7.9, -22.39, 0.33493, 3, 78, 106.66, -35.4, 0.02292, 79, 14.18, -19.9, 0.96128, 80, -17.93, -38.77, 0.0158, 2, 78, 95.33, -25.96, 0.38555, 79, -0.53, -21.05, 0.61445, 2, 78, 73.96, -33.29, 0.89783, 79, -10.73, -41.21, 0.10217, 2, 78, 51.26, -41.07, 0.99206, 79, -21.57, -62.62, 0.00794, 2, 78, 31.88, -41.05, 0.99998, 79, -35.46, -76.12, 2e-05, 1, 78, 13.15, -41.03, 1, 1, 78, -31.96, -24.63, 1, 1, 78, -46.89, -1.28, 1, 1, 78, -42.88, 5.41, 1, 1, 78, -32.7, 18.53, 1, 1, 78, 2.72, 27.38, 1, 1, 78, 43.31, 37.53, 1, 1, 78, 66.25, 38.97, 1, 1, 78, 91.22, 40.53, 1], "hull": 32, "edges": [0, 62, 6, 8, 8, 10, 20, 22, 22, 24, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 2, 2, 4, 4, 6, 34, 36, 36, 38, 32, 34, 30, 32, 28, 30, 24, 26, 26, 28, 16, 18, 18, 20, 14, 16, 10, 12, 12, 14, 38, 40, 40, 42, 42, 44, 44, 46], "width": 159, "height": 127}}, "wb": {"wb": {"type": "mesh", "uvs": [0.55113, 0.03547, 0.73086, 0.16882, 0.8813, 0.28044, 0.95373, 0.48904, 0.88612, 0.70742, 0.82791, 0.89545, 0.60219, 0.89548, 0.42401, 0.89551, 0.16293, 0.78368, 0.00894, 0.61762, 0.24478, 0.26966, 0.42369, 0.42951, 0.56369, 0.58425, 0.69669, 0.6874], "triangles": [13, 1, 2, 11, 0, 1, 8, 9, 10, 8, 10, 11, 8, 11, 7, 11, 10, 0, 12, 11, 1, 6, 12, 13, 7, 11, 12, 7, 12, 6, 13, 12, 1, 13, 2, 3, 4, 13, 3, 5, 13, 4, 6, 13, 5], "vertices": [4, 112, 1.13, -6.17, 0.29323, 113, -5.43, -5.55, 0.02296, 114, -11.46, -2.33, 0.00381, 107, 20.92, -7.97, 0.68, 2, 112, -2.42, -2.08, 0.344, 107, 18.3, -3.23, 0.656, 3, 112, -5.39, 1.35, 0.22924, 113, -10.24, 3.16, 0.00276, 107, 16.11, 0.74, 0.768, 2, 112, -5.74, 5.62, 0.92366, 113, -9.7, 7.41, 0.07634, 2, 112, -2.54, 8.61, 0.76249, 113, -5.94, 9.67, 0.23751, 2, 112, 0.22, 11.19, 0.62561, 113, -2.71, 11.62, 0.37439, 3, 112, 5.84, 8.88, 0.30109, 113, 2.31, 8.19, 0.65009, 114, -0.49, 9, 0.04882, 3, 112, 10.27, 7.05, 0.03781, 113, 6.27, 5.48, 0.56518, 114, 2.65, 5.38, 0.39701, 1, 114, 5.71, -1.27, 1, 1, 114, 6.14, -6.39, 1, 3, 112, 10.38, -5.36, 0.03765, 113, 3.79, -6.68, 0.27806, 114, -2.83, -5.76, 0.68429, 3, 112, 7.04, -0.82, 0.02866, 113, 1.47, -1.55, 0.86858, 114, -3.77, -0.21, 0.10276, 3, 112, 4.63, 3.23, 0.29845, 113, -0.05, 2.91, 0.69887, 114, -4.11, 4.49, 0.00267, 3, 112, 2.04, 6.33, 0.579, 113, -1.94, 6.49, 0.41976, 114, -5.03, 8.43, 0.00124], "hull": 11, "edges": [0, 20, 4, 6, 14, 16, 16, 18, 18, 20, 0, 2, 2, 4, 10, 12, 12, 14, 6, 8, 8, 10], "width": 28, "height": 19}}}}], "animations": {"gouhuoxiao_ren": {"slots": {"txj5": {"attachment": [{"name": "j1"}]}, "txst": {"attachment": [{"name": "st"}]}, "txs6": {"attachment": [{"name": "s1"}]}, "txst2": {"attachment": [{"name": "st"}]}, "txs1": {"attachment": [{"name": "s1"}]}, "honghuo2": {"attachment": [{"name": "honghuo0001"}, {"time": 0.1, "name": "honghuo0002"}, {"time": 0.2, "name": "honghuo0003"}, {"time": 0.3, "name": "honghuo0004"}, {"time": 0.4333, "name": "honghuo0005"}, {"time": 0.5333, "name": "honghuo0006"}, {"time": 0.6333, "name": "honghuo0007"}, {"time": 0.7333, "name": "honghuo0008"}, {"time": 0.8333, "name": "honghuo0009"}, {"time": 0.9333, "name": "honghuo0001"}, {"time": 1.0333, "name": "honghuo0002"}, {"time": 1.1333, "name": "honghuo0003"}, {"time": 1.2667, "name": "honghuo0004"}, {"time": 1.3667, "name": "honghuo0005"}, {"time": 1.4667, "name": "honghuo0006"}, {"time": 1.5667, "name": "honghuo0007"}, {"time": 1.6667, "name": "honghuo0008"}, {"time": 1.7667, "name": "honghuo0009"}, {"time": 1.8667, "name": "honghuo0001"}, {"time": 2, "name": "honghuo0002"}, {"time": 2.1, "name": "honghuo0003"}, {"time": 2.2, "name": "honghuo0004"}, {"time": 2.3, "name": "honghuo0005"}, {"time": 2.4, "name": "honghuo0006"}, {"time": 2.5, "name": "honghuo0007"}, {"time": 2.6, "name": "honghuo0008"}, {"time": 2.7333, "name": "honghuo0009"}, {"time": 2.8333, "name": "honghuo0001"}, {"time": 2.9333, "name": "honghuo0002"}, {"time": 3.0333, "name": "honghuo0003"}, {"time": 3.1333, "name": "honghuo0004"}, {"time": 3.2333, "name": "honghuo0005"}, {"time": 3.3333, "name": "honghuo0006"}, {"time": 3.4333, "name": "honghuo0007"}, {"time": 3.5667, "name": "honghuo0008"}, {"time": 3.6667, "name": "honghuo0009"}]}, "txyingzi3": {"attachment": [{"name": "<PERSON><PERSON><PERSON>"}]}, "txps5": {"attachment": [{"name": "ps2"}]}, "txhuo": {"attachment": [{"name": "huo"}]}, "txps4": {"attachment": [{"name": "ps1"}]}, "txt3": {"attachment": [{"name": "t"}]}, "txj2": {"attachment": [{"name": "j2"}]}, "txyingzi": {"attachment": [{"name": "<PERSON><PERSON><PERSON>"}]}, "txt2": {"attachment": [{"name": "t"}]}, "txs5": {"attachment": [{"name": "s2"}]}, "txps6": {"attachment": [{"name": "ps1"}]}, "txs4": {"attachment": [{"name": "s1"}]}, "txps3": {"attachment": [{"name": "ps2"}]}, "txt": {"attachment": [{"name": "t"}]}, "txj4": {"attachment": [{"name": "j2"}]}, "txps1": {"attachment": [{"name": "ps1"}]}, "txs2": {"attachment": [{"name": "s2"}]}, "txps2": {"attachment": [{"name": "ps2"}]}, "txs3": {"attachment": [{"name": "s2"}]}, "txyingzi2": {"attachment": [{"name": "<PERSON><PERSON><PERSON>"}]}, "txj1": {"attachment": [{"name": "j1"}]}, "txst3": {"attachment": [{"name": "st"}]}, "txj3": {"attachment": [{"name": "j1"}]}, "txj6": {"attachment": [{"name": "j2"}]}}, "bones": {"txst": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -8, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -8, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "y": 8, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "y": -10.81, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "y": -10.81, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "y": 4, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "txj1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -26.81, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -26.81, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "txj2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 54, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 27.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333, "angle": 21.28, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 47.69, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.5, "angle": 27.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.6, "angle": 21.28, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 47.69, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.1667, "angle": 27.68, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "txt": {"rotate": [{"angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 30.33, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 14.73, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 30.33, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 14.73, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 30.33, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 7.2}]}, "txj3": {"rotate": [{"angle": 1.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 12, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 1.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 12, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": 1.56, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -22.1}, {"time": 2.1667, "angle": 11.5}, {"time": 2.5, "angle": -22.1}, {"time": 2.8333, "angle": 11.5}, {"time": 3.1667, "angle": -22.1, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 1.56}]}, "txj4": {"rotate": [{"angle": -0.82, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -19.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.6667, "angle": -0.82, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -19.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "angle": -0.82, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 37.64}, {"time": 2.1667, "angle": -37.96}, {"time": 2.5, "angle": 37.64}, {"time": 2.8333, "angle": -37.96}, {"time": 3.1667, "angle": 37.64, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -0.82}]}, "txs1": {"rotate": [{"angle": 2.35, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": 1.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333}, {"time": 0.5667, "angle": 3.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 2.35, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.7333, "angle": 1.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9}, {"time": 1.2333, "angle": 3.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 2.35, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -25.41, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -39.58, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -33.59, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -39.58, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -33.59, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -39.58, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 2.35}]}, "txs4": {"rotate": [{"angle": 6.07, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0667, "angle": 2.14, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333}, {"time": 0.4667, "angle": 16.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6333, "angle": 8.29, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.6667, "angle": 6.07, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.7333, "angle": 2.14, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8}, {"time": 1.1333, "angle": 16.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3, "angle": 8.29, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 1.3333, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -21.69, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -32.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -35.86, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5, "angle": -32.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "angle": -35.86, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1667, "angle": -32.87, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 6.07}]}, "txs2": {"rotate": [{"angle": -2.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667}, {"time": 0.5, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -2.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333}, {"time": 1.1667, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -2.83, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -24.91, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -39.88, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -48.31, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -39.88, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -48.31, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -39.88, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -2.83}]}, "txs3": {"rotate": [{"angle": -0.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "angle": -2.83, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.6667, "angle": -0.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2333, "angle": -2.83, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 1.3333, "angle": -0.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -22.81, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -25.42, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333, "angle": -37.78, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -46.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.5, "angle": -39.82, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.6, "angle": -37.78, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -30.61, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.1667, "angle": -18.73, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": -22.81, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -0.73}]}, "txps2": {"rotate": [{"angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1}, {"time": 0.4333, "angle": -12.72, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.7667}, {"time": 1.1, "angle": -12.72, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -13.51, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -14.02, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -14.02, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": -7.88, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.08}]}, "txps3": {"rotate": [{"angle": -8.01, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.72, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -8.01, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.7667, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -12.72, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -18.43, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -10.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.9333, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -18.94, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.5, "angle": -10.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.6, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -18.94, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.1667, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -18.43, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -8.01}]}, "txps4": {"rotate": [{"angle": -12.22, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 0.1, "angle": -8.04, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -12.72, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -12.22, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 0.7667, "angle": -8.04, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -12.72, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.44, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -8.33, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.0333, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -23.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.5, "angle": -19.13, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.7, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -23.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.1667, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": -22.64, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -12.22}]}, "txps1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.21, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -2.89, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 16.99, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 16.99, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -2.89, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "txps5": {"rotate": [{"angle": 2.96, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 12.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 2.96, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 12.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 5.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.9, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 19.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "angle": 5.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.5667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": 19.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.1667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 2.96}]}, "txps6": {"rotate": [{"angle": 7.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 12.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 7.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 12.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 16.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 24.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5, "angle": 16.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 24.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1667, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 7.72}]}, "txbone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}], "translate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 49.94, "curve": "stepped"}, {"time": 3.1667, "x": 49.94, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "txj6": {"rotate": [{"angle": 24.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": 47.69, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": 27.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": 21.28, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 47.69, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.1667, "angle": 27.68, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 54, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 54, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 27.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.6, "angle": 21.28, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.6667, "angle": 24.72}]}, "txj5": {"rotate": [{"angle": -22.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -26.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -26.81, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -17.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": -22.01}]}, "txj8": {"rotate": [{"angle": -0.16}, {"time": 0.1667, "angle": -37.96}, {"time": 0.5, "angle": 37.64}, {"time": 0.8333, "angle": -37.96}, {"time": 1.1667, "angle": 37.64, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.82, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -19.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.3333, "angle": -0.82, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -19.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3, "angle": -0.82, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 37.64}, {"time": 3.6667, "angle": -0.16}]}, "txj7": {"rotate": [{"angle": -5.3}, {"time": 0.1667, "angle": 11.5}, {"time": 0.5, "angle": -22.1}, {"time": 0.8333, "angle": 11.5}, {"time": 1.1667, "angle": -22.1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 12, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.3333, "angle": 1.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 12, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3, "angle": 1.56, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -22.1}, {"time": 3.6667, "angle": -5.3}]}, "txs6": {"rotate": [{"angle": -38.87, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": -46.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -39.82, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": -37.78, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -30.61, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.1667, "angle": -18.73, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -22.81, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2333, "angle": -2.83, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 2.3333, "angle": -0.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9, "angle": -2.83, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 3, "angle": -0.73, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -22.81, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -25.42, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.6, "angle": -37.78, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.6667, "angle": -38.87}]}, "txs5": {"rotate": [{"angle": -44.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -48.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -39.88, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -48.31, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -39.88, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.8333}, {"time": 2.1667, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3333, "angle": -2.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5}, {"time": 2.8333, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3, "angle": -2.83, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -24.91, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -39.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": -44.09}]}, "txs8": {"rotate": [{"angle": -35.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -32.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -35.86, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": -32.87, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6.07, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 1.7333, "angle": 2.14, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.8}, {"time": 2.1333, "angle": 16.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3, "angle": 8.29, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2.3333, "angle": 6.07, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 2.4, "angle": 2.14, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.4667}, {"time": 2.8, "angle": 16.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9667, "angle": 8.29, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 3, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -21.69, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -32.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "angle": -35.86}]}, "txs7": {"rotate": [{"angle": -36.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -33.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -39.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -33.59, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -39.58, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.35, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 1.7333, "angle": 1.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9}, {"time": 2.2333, "angle": 3.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.3333, "angle": 2.35, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 2.4, "angle": 1.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667}, {"time": 2.9, "angle": 3.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3, "angle": 2.35, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -25.41, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -39.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": -36.58}]}, "txt2": {"rotate": [{"angle": 22.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 14.73, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 30.33, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 14.73, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 30.33, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 30.33, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": 22.53}]}, "txps9": {"rotate": [{"angle": -11.97, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -23.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "angle": -19.13, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -23.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.1667, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -22.64, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -12.22, "curve": 0.333, "c2": 0.33, "c3": 0.708, "c4": 0.68}, {"time": 1.7667, "angle": -8.04, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -12.72, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.3333, "angle": -12.22, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 2.4333, "angle": -8.04, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -12.72, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -9.44, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -8.33, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 3.6667, "angle": -11.97}]}, "txps8": {"rotate": [{"angle": -9.43, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": -18.94, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -10.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -18.94, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.1667, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -18.43, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.01, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 1.7667, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -12.72, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3333, "angle": -8.01, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 2.4333, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -12.72, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -18.43, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -10.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.6, "angle": -8.01, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.6667, "angle": -9.43}]}, "txps7": {"rotate": [{"angle": -8.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -14.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -14.02, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -7.88, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.7667}, {"time": 2.1, "angle": -12.72, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.3333, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.4333}, {"time": 2.7667, "angle": -12.72, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -13.51, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -3.08, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": -8.55}]}, "txps12": {"rotate": [{"angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 24.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 16.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 24.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.72, "curve": 0.583, "c2": 0.38, "c4": 0.73}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 12.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.3333, "angle": 7.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 12.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 16.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "angle": 7.72}]}, "txps11": {"rotate": [{"angle": 7.07, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 19.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": 5.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 19.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.1667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.96, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 12.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.3333, "angle": 2.96, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 12.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 5.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.5667, "angle": 2.96, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6667, "angle": 7.07}]}, "txps10": {"rotate": [{"angle": 8.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 16.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 16.99, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -2.89, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.167, "c2": 0.29, "c3": 0.556, "c4": 0.64}, {"time": 2, "angle": 12.21, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 12.21, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -2.89, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": 8.5}]}, "txst2": {"rotate": [{"angle": -20.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -19.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": -20.4}], "translate": [{"y": -5.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "y": -10.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -10.81, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "y": 4, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -8, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": -8, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "y": 8, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "y": -5.4}]}, "txbone3": {"rotate": [{"angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 2.4}], "translate": [{"x": -22.87, "y": -145.48, "curve": "stepped"}, {"time": 1.1667, "x": -22.87, "y": -145.48, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -77.77, "y": -145.48, "curve": "stepped"}, {"time": 3, "x": -77.77, "y": -145.48, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -22.87, "y": -145.48}]}, "txj10": {"rotate": [{"angle": 47.69, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2333, "angle": 27.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 54, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 54, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 27.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.6667, "angle": 21.28, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 47.69, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.2333, "angle": 27.68, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.3333, "angle": 21.28, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 47.69}]}, "txj9": {"rotate": [{"angle": -24.49, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -26.81, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -26.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6667, "angle": -24.49}]}, "txj12": {"rotate": [{"angle": -15.28}, {"time": 0.2333, "angle": 37.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -0.82, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -19.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.4, "angle": -0.82, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -19.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.0667, "angle": -0.82, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 37.64}, {"time": 2.9, "angle": -37.96}, {"time": 3.2333, "angle": 37.64}, {"time": 3.5667, "angle": -37.96}, {"time": 3.6667, "angle": -15.28}]}, "txj11": {"rotate": [{"angle": 1.42}, {"time": 0.2333, "angle": -22.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 1.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 12, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.4, "angle": 1.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 12, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.0667, "angle": 1.56, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -22.1}, {"time": 2.9, "angle": 11.5}, {"time": 3.2333, "angle": -22.1}, {"time": 3.5667, "angle": 11.5}, {"time": 3.6667, "angle": 1.42}]}, "txs10": {"rotate": [{"angle": -30.61, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2333, "angle": -18.73, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -22.81, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -0.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3, "angle": -2.83, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 1.4, "angle": -0.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.9667, "angle": -2.83, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 2.0667, "angle": -0.73, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -22.81, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -25.42, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.6667, "angle": -37.78, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -46.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.2333, "angle": -39.82, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.3333, "angle": -37.78, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -30.61}]}, "txs9": {"rotate": [{"angle": -46.27, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -39.88, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -2.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9}, {"time": 1.2333, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4, "angle": -2.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5667}, {"time": 1.9, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.0667, "angle": -2.83, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -24.91, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -39.88, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -48.31, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": -39.88, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -48.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6667, "angle": -46.27}]}, "txs12": {"rotate": [{"angle": -30.65, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -32.87, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 6.07, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.8, "angle": 2.14, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8667}, {"time": 1.2, "angle": 16.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3667, "angle": 8.29, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 1.4, "angle": 6.07, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 1.4667, "angle": 2.14, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.5333}, {"time": 1.8667, "angle": 16.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.0333, "angle": 8.29, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2.0667, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -21.69, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -32.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7333, "angle": -35.86, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": -29.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2333, "angle": -32.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4, "angle": -35.86, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.6667, "angle": -30.65}]}, "txs11": {"rotate": [{"angle": -35.04, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -39.58, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 2.35, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.8, "angle": 1.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667}, {"time": 1.3, "angle": 3.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.4, "angle": 2.35, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 1.4667, "angle": 1.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6333}, {"time": 1.9667, "angle": 3.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.0667, "angle": 2.35, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -25.41, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -39.58, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -33.59, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": -39.58, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -33.59, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6667, "angle": -35.04}]}, "txt3": {"rotate": [{"angle": 18.51, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 30.33, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 30.33, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": 14.73, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": 30.33, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 14.73, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6667, "angle": 18.51}]}, "txps15": {"rotate": [{"angle": -20.5, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -23.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -22.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -12.22, "curve": 0.333, "c2": 0.33, "c3": 0.708, "c4": 0.68}, {"time": 0.8333, "angle": -8.04, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -12.72, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.4, "angle": -12.22, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 1.5, "angle": -8.04, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -12.72, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.0667, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -9.44, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -8.33, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.7667, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -23.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.2333, "angle": -19.13, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4333, "angle": -12.22, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.6667, "angle": -20.5}]}, "txps14": {"rotate": [{"angle": -18.94, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.2333, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -18.43, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -8.01, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.8333, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -12.72, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.4, "angle": -8.01, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 1.5, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -12.72, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.0667, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -18.43, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -10.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.6667, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -18.94, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.2333, "angle": -10.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.3333, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -18.94}]}, "txps13": {"rotate": [{"angle": -11.37, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.88, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8333}, {"time": 1.1667, "angle": -12.72, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.4, "angle": -3.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5}, {"time": 1.8333, "angle": -12.72, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.0667, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -13.51, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -14.02, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": -3.08, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -14.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6667, "angle": -11.37}]}, "txps18": {"rotate": [{"angle": 22.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 24.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 7.72, "curve": 0.583, "c2": 0.38, "c4": 0.73}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 12.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.4, "angle": 7.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 12.21, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.0667, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 16.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7333, "angle": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": 24.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2333, "angle": 16.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4, "angle": 7.72, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.6667, "angle": 22.5}]}, "txps17": {"rotate": [{"angle": 19.1, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 0.2333, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 2.96, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 12.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.4, "angle": 2.96, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 12.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.0667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 5.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.6333, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 19.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.2333, "angle": 5.17, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.3, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": 19.95, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.6667, "angle": 19.1}]}, "txps16": {"rotate": [{"angle": 12.88, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.89, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.167, "c2": 0.29, "c3": 0.556, "c4": 0.64}, {"time": 1.0667, "angle": 12.21, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 12.21, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -2.89, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": 16.99, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 16.99, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6667, "angle": 12.88}]}, "txst3": {"rotate": [{"angle": -21.02, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": -19.2, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -21.6, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6667, "angle": -21.02}], "translate": [{"y": -8.19, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 4, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": -8, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "y": -8, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 8, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "y": -10.81, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "y": -10.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.6667, "y": -8.19}]}, "txbone4": {"rotate": [{"angle": -1.08, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 2.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.6667, "angle": -1.08}], "translate": [{"x": -242.74, "y": -73.2, "curve": "stepped"}, {"time": 0.2333, "x": -242.74, "y": -73.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -287.31, "y": -73.2, "curve": "stepped"}, {"time": 2.0667, "x": -287.31, "y": -73.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": -242.74, "y": -73.2}]}, "bone": {"translate": [{}, {"time": 0.9, "y": 10}, {"time": 1.8333}, {"time": 2.7333, "y": 10}, {"time": 3.6333}]}}, "deform": {"default": {"txyingzi": {"yingzi": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "vertices": [2.33997, 0, -2.33997, 0, -2.33997, 0, 2.33995], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "vertices": [5.45996, 0, -5.45995, 0, -5.45997, 0, 5.45993], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "vertices": [3.63994, 0, -3.63993, 0, -3.63993, 0, 3.63992], "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "vertices": [-12.45987, 0, -19.73974, 0, -19.73974, 0, -12.45989], "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "vertices": [-10.53489, 0, -21.66489, 0, -21.6649, 0, -10.53491], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "vertices": [-14.78049, 0, -17.41902, 0, -17.41903, 0, -14.7805], "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "vertices": [-10.53489, 0, -21.66489, 0, -21.6649, 0, -10.53491], "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "vertices": [-12.45987, 0, -19.73974, 0, -19.73974, 0, -12.45989], "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "vertices": [-3.1313, 0, -6.52857, 0, -6.52857, 0, -3.13131], "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "txyingzi3": {"yingzi": [{"vertices": [-27.94525, 0.00014, -27.94525, 0.00014, -27.94525, 0.00014, -27.94525], "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "vertices": [-21.64568, -0.00011, -26.32561, -0.00011, -26.32562, -0.00011, -21.6457], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "vertices": [-13.26489, 0.00031, -13.26489, 0.00031, -13.26489, 0.00031, -13.26489], "curve": 0.25, "c3": 0.75}, {"time": 1, "vertices": [-13.91509, -0.00011, -18.59502, -0.00011, -18.59503, -0.00011, -13.91511], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [-13.26489, 0.00031, -13.26489, 0.00031, -13.26489, 0.00031, -13.26489], "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "vertices": [-12.45987, 0, -19.73974, 0, -19.73974, 0, -12.45989], "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "vertices": [-10.53489, 0, -21.66489, 0, -21.6649, 0, -10.53491], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "vertices": [-20.96666, 5e-05, -23.60519, 5e-05, -23.60521, 5e-05, -20.96667], "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "vertices": [-19.03881, -6.95778, -30.16882, -6.95778, -30.16882, -6.95778, -19.03883, -6.95778], "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "vertices": [-12.45987, 0, -19.73974, 0, -19.73974, 0, -12.45989], "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "vertices": [-27.94525, 0.00014, -27.94525, 0.00014, -27.94525, 0.00014, -27.94525]}]}, "txyingzi2": {"yingzi": [{"vertices": [-27.94525, 0.00014, -27.94525, 0.00014, -27.94525, 0.00014, -27.94525], "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "vertices": [-21.64568, -0.00011, -26.32561, -0.00011, -26.32562, -0.00011, -21.6457], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "vertices": [-27.94525, 0.00014, -27.94525, 0.00014, -27.94525, 0.00014, -27.94525], "curve": 0.25, "c3": 0.75}, {"time": 1, "vertices": [-21.64568, -0.00011, -26.32561, -0.00011, -26.32562, -0.00011, -21.6457], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [-27.94525, 0.00014, -27.94525, 0.00014, -27.94525, 0.00014, -27.94525], "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "vertices": [-12.45987, 0, -19.73974, 0, -19.73974, 0, -12.45989], "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "vertices": [-10.53489, 0, -21.66489, 0, -21.6649, 0, -10.53491], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "vertices": [-14.78049, 0, -17.41902, 0, -17.41903, 0, -14.7805], "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "vertices": [-10.53489, 0, -21.66489, 0, -21.6649, 0, -10.53491], "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "vertices": [-12.45987, 0, -19.73974, 0, -19.73974, 0, -12.45989], "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "vertices": [-27.94525, 0.00014, -27.94525, 0.00014, -27.94525, 0.00014, -27.94525]}]}}}}, "tu_jiu": {"slots": {"z1": {"attachment": [{"name": "z1"}]}, "c2": {"attachment": [{"name": "c2"}]}, "st": {"attachment": [{"name": "tst"}]}, "z2": {"attachment": [{"name": "z2"}]}, "wb": {"attachment": [{"name": "wb"}]}, "c1": {"attachment": [{"name": "c1"}]}, "t1": {"attachment": [{"name": "tut1"}]}}, "bones": {"st2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -0.32, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": -0.32, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -0.32, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -0.32, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "x": -0.32, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "x": -0.32, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "x": -0.32, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "x": -0.32, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}]}, "c1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}], "scale": [{"x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.74, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 0.74, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.2333, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 0.74, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4.8333, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "x": 0.74, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.4667, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "x": 0.74, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 8.0667, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 0.74, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 9.7, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 9.8, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "x": 0.74, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 11.3, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 11.4, "curve": 0.25, "c3": 0.75}, {"time": 12.2, "x": 0.74, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 12.9333, "x": 0.989, "y": 0.998}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": -15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "x": -15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "x": -15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "x": -15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "x": -15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}]}, "c2": {"rotate": [{"angle": 1.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 14.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6, "angle": 1.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 14.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.2333, "angle": 1.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": 14.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "angle": 1.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 14.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.4667, "angle": 1.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6333, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": 14.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.0667, "angle": 1.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "angle": 14.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.7, "angle": 1.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.8667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 14.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "angle": 1.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "angle": 14.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12.9333, "angle": 1.88}]}, "c3": {"rotate": [{"angle": 5.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6, "angle": 5.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.2333, "angle": 5.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8333, "angle": 5.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.4667, "angle": 5.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.0667, "angle": 5.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.7, "angle": 5.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.0333, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "angle": 5.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 11.6333, "curve": 0.25, "c3": 0.75}, {"time": 12.4333, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.9333, "angle": 5.3}]}, "c4": {"rotate": [{"angle": 9.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6, "angle": 9.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.2333, "angle": 9.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.8333, "angle": 9.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.4667, "angle": 9.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.0667, "angle": 9.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.5667, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.7, "angle": 9.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3, "angle": 9.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 11.8, "curve": 0.25, "c3": 0.75}, {"time": 12.6, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12.9333, "angle": 9.11}]}, "c5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -19.47, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -19.47, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -19.47, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -19.47, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -19.47, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": -19.47, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": -19.47, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": -19.47, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}], "scale": [{"x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.68, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 0.68, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.2333, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 0.68, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4.8333, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "x": 0.68, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.4667, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "x": 0.68, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 8.0667, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 0.68, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 9.7, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 9.8, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "x": 0.68, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 11.3, "x": 0.989, "y": 0.998, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 11.4, "curve": 0.25, "c3": 0.75}, {"time": 12.2, "x": 0.68, "y": 0.96, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 12.9333, "x": 0.989, "y": 0.998}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": 15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "x": 15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "x": 15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "x": 15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "x": 15.6, "y": -6, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}]}, "c6": {"rotate": [{"angle": -1.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -14.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6, "angle": -1.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -14.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.2333, "angle": -1.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -14.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "angle": -1.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": -14.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.4667, "angle": -1.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6333, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": -14.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.0667, "angle": -1.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "angle": -14.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.7, "angle": -1.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.8667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -14.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "angle": -1.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "angle": -14.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12.9333, "angle": -1.91}]}, "c7": {"rotate": [{"angle": -5.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -14.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6, "angle": -5.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -14.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.2333, "angle": -5.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -14.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8333, "angle": -5.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": -14.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.4667, "angle": -5.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -14.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.0667, "angle": -5.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "angle": -14.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.7, "angle": -5.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.0333, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "angle": -14.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "angle": -5.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 11.6333, "curve": 0.25, "c3": 0.75}, {"time": 12.4333, "angle": -14.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.9333, "angle": -5.4}]}, "c8": {"rotate": [{"angle": -9.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -14.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6, "angle": -9.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -14.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.2333, "angle": -9.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -14.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.8333, "angle": -9.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -14.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.4667, "angle": -9.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -14.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.0667, "angle": -9.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.5667, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "angle": -14.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.7, "angle": -9.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": -14.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3, "angle": -9.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 11.8, "curve": 0.25, "c3": 0.75}, {"time": 12.6, "angle": -14.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12.9333, "angle": -9.27}]}, "c9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -43.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -43.73, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -43.73, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -43.73, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -43.73, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": -43.73, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": -43.73, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": -43.73, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}]}, "c10": {"rotate": [{"angle": -6.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -24.87, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6, "angle": -6.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -24.87, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.2333, "angle": -6.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -24.87, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.8333, "angle": -6.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.1, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": -24.87, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.4667, "angle": -6.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.7333, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "angle": -24.87, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.0667, "angle": -6.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": -24.87, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.7, "angle": -6.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.7667, "angle": -24.87, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.3, "angle": -6.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 11.5667, "curve": 0.25, "c3": 0.75}, {"time": 12.3667, "angle": -24.87, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12.9333, "angle": -6.02}]}, "c11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 45.15, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 45.15, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": 45.15, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 45.15, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 45.15, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": 45.15, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": 45.15, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": 45.15, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "y": 0.843, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "y": 0.843, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "y": 0.843, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 0.843, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "y": 0.843, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "y": 0.843, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "y": 0.843, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "y": 0.843, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}]}, "c12": {"rotate": [{"angle": 4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 16.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6, "angle": 4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 16.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.2333, "angle": 4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 16.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.8333, "angle": 4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.1, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 16.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.4667, "angle": 4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.7333, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "angle": 16.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.0667, "angle": 4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "angle": 16.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.7, "angle": 4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.7667, "angle": 16.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.3, "angle": 4, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 11.5667, "curve": 0.25, "c3": 0.75}, {"time": 12.3667, "angle": 16.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12.9333, "angle": 4}]}, "st3": {"rotate": [{"angle": -2.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6, "angle": -2.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.2333, "angle": -2.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8333, "angle": -2.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": -6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.4667, "angle": -2.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": -6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.0667, "angle": -2.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "angle": -6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.7, "angle": -2.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.0333, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "angle": -6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "angle": -2.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 11.6333, "curve": 0.25, "c3": 0.75}, {"time": 12.4333, "angle": -6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.9333, "angle": -2.21}]}, "t1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}], "translate": [{"x": -0.27, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": -0.74, "y": -0.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6, "x": -0.27, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": -0.74, "y": -0.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.2333, "x": -0.27, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "x": -0.74, "y": -0.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8333, "x": -0.27, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "x": -0.74, "y": -0.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.4667, "x": -0.27, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "x": -0.74, "y": -0.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.0667, "x": -0.27, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "x": -0.74, "y": -0.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.7, "x": -0.27, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.0333, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "x": -0.74, "y": -0.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "x": -0.27, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 11.6333, "curve": 0.25, "c3": 0.75}, {"time": 12.4333, "x": -0.74, "y": -0.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.9333, "x": -0.27, "y": -0.01}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}]}, "t2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 19.19, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 19.19, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": 19.19, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 19.19, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 19.19, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": 19.19, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": 19.19, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": 19.19, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}]}, "st": {"rotate": [{"angle": -0.89}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "y": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 12.9333}]}, "z1": {"rotate": [{"angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667}, {"time": 0.9667, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667}, {"time": 2.6, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.2333, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4}, {"time": 4.2, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5}, {"time": 5.8333, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.4667, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6333}, {"time": 7.4333, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.0667, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333}, {"time": 9.0333, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.7, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.8667}, {"time": 10.6667, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.4667}, {"time": 12.2667, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12.9333, "angle": -1.79}], "translate": [{"x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667}, {"time": 1.0667, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8667}, {"time": 2.6667, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.2333, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.5}, {"time": 4.3, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.8333, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.1}, {"time": 5.9, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.4667, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.7333}, {"time": 7.5333, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.0667, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3333}, {"time": 9.1333, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.7, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.9667}, {"time": 10.7667, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.3, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 11.5667}, {"time": 12.3667, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12.9333, "x": 0.44, "y": 0.11}]}, "z2": {"rotate": [{"angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 1.5667, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 1.6, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 3.1667, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 3.2333, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6333, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 4.7667, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.2333, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 6.4, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 6.4667, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6333, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.8667, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 8, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 8.0667, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.4667, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 9.6333, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 9.7, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.8667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 11.1, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 11.2333, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12.7, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 12.8667, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 12.9333, "angle": 1.69}]}, "z3": {"rotate": [{"angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.2333, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.4667, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6333, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.0667, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.7, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.8667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "angle": -1.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "angle": -13.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12.9333, "angle": -1.79}], "translate": [{"x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.2333, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.8333, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.1, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.4667, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.7333, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.0667, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 9.7, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 9.9667, "curve": 0.25, "c3": 0.75}, {"time": 10.7667, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.3, "x": 0.44, "y": 0.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 11.5667, "curve": 0.25, "c3": 0.75}, {"time": 12.3667, "x": 1.81, "y": 0.44, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12.9333, "x": 0.44, "y": 0.11}]}, "z4": {"rotate": [{"angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 1.5667, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 1.6, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 3.1667, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 3.2333, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6333, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 4.7667, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.2333, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 6.4, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 6.4667, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6333, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.8667, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 8, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 8.0667, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.4667, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 9.6333, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 9.7, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.8667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 11.1, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 11.2333, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "angle": 13.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12.7, "angle": 6.58, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 12.8667, "angle": 3.21, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 12.9333, "angle": 1.69}]}, "wb": {"rotate": [{"angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 12.1333, "angle": 10.57, "curve": 0.25, "c3": 0.75}, {"time": 12.9333, "angle": -13.35}]}, "wb2": {"rotate": [{"angle": -9.19, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.57, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6, "angle": -9.19, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.8, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 10.57, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 3.2333, "angle": -9.19, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 3.4333, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 10.57, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 4.8333, "angle": -9.19, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 5.0333, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 10.57, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 6.4667, "angle": -9.19, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 6.6667, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": 10.57, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 8.0667, "angle": -9.19, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 8.2667, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "angle": 10.57, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 9.7, "angle": -9.19, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 9.9, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 10.7, "angle": 10.57, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 11.3, "angle": -9.19, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 11.5, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 12.3, "angle": 10.57, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 12.9333, "angle": -9.19}]}, "wb3": {"rotate": [{"angle": -2.02, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 10.57, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6, "angle": -2.02, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 2, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 10.57, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 3.2333, "angle": -2.02, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.6333, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": 10.57, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 4.8333, "angle": -2.02, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 5.2333, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": 10.57, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 6.4667, "angle": -2.02, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 6.8667, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 10.57, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 8.0667, "angle": -2.02, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 8.4667, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "angle": 10.57, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 9.7, "angle": -2.02, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 10.0667, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 10.9, "angle": 10.57, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 11.3, "angle": -2.02, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 11.7, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 12.5, "angle": 10.57, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 12.9333, "angle": -2.02}]}, "root": {"translate": [{}, {"time": 1.3333, "x": 34.61, "y": 28.17}, {"time": 2.2667, "x": 59.08, "y": 34.44}, {"time": 3.5, "x": 91.16, "y": 48.64}, {"time": 4.6667, "x": 121.54, "y": 34.83}, {"time": 5.7, "x": 148.55, "y": 24.93}, {"time": 6.4667, "x": 168.81}, {"time": 8.0667, "x": 126.6, "y": -28}, {"time": 8.9, "x": 105.5, "y": -30}, {"time": 9.7, "x": 84.4, "y": -44}, {"time": 11.3, "x": 42.2, "y": -30}, {"time": 11.9667, "x": 25.32, "y": -12}, {"time": 12.9333}], "scale": [{"time": 6.4333}, {"time": 6.4667, "x": -1, "curve": "stepped"}, {"time": 12.9, "x": -1}, {"time": 12.9333}]}}, "deform": {"default": {"c2": {"c2": [{"offset": 126, "vertices": [-0.19694, 0.12496, -0.11475, -0.38313, 0.11494, -0.38308, -0.43064, -0.06428, -0.11222, -0.79379, -0.52342, -0.05445, -0.2735, -0.44961, -0.15812, -0.94704, 0.38895, -0.87789, -0.41444, 0.13354, -0.11222, -0.79379, 0.34297, -0.72466, -0.17752, -0.34776, -0.08927, -0.71717, 0.31998, -0.64804], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "offset": 126, "vertices": [-1.51377, 0.96053, -0.88202, -2.94493, 0.8835, -2.9446, -3.31017, -0.49407, -0.86255, -6.10152, -4.0233, -0.41851, -2.10225, -3.45598, -1.21537, -7.2795, 2.98966, -6.74793, -3.18562, 1.02646, -0.86256, -6.10155, 2.63628, -5.57011, -1.36455, -2.6731, -0.68615, -5.51256, 2.45958, -4.98119], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6, "offset": 126, "vertices": [-0.19694, 0.12496, -0.11475, -0.38313, 0.11494, -0.38308, -0.43064, -0.06428, -0.11222, -0.79379, -0.52342, -0.05445, -0.2735, -0.44961, -0.15812, -0.94704, 0.38895, -0.87789, -0.41444, 0.13354, -0.11222, -0.79379, 0.34297, -0.72466, -0.17752, -0.34776, -0.08927, -0.71717, 0.31998, -0.64804], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "offset": 126, "vertices": [-1.51377, 0.96053, -0.88202, -2.94493, 0.8835, -2.9446, -3.31017, -0.49407, -0.86255, -6.10152, -4.0233, -0.41851, -2.10225, -3.45598, -1.21537, -7.2795, 2.98966, -6.74793, -3.18562, 1.02646, -0.86256, -6.10155, 2.63628, -5.57011, -1.36455, -2.6731, -0.68615, -5.51256, 2.45958, -4.98119], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.2333, "offset": 126, "vertices": [-0.19694, 0.12496, -0.11475, -0.38313, 0.11494, -0.38308, -0.43064, -0.06428, -0.11222, -0.79379, -0.52342, -0.05445, -0.2735, -0.44961, -0.15812, -0.94704, 0.38895, -0.87789, -0.41444, 0.13354, -0.11222, -0.79379, 0.34297, -0.72466, -0.17752, -0.34776, -0.08927, -0.71717, 0.31998, -0.64804], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "offset": 126, "vertices": [-1.51377, 0.96053, -0.88202, -2.94493, 0.8835, -2.9446, -3.31017, -0.49407, -0.86255, -6.10152, -4.0233, -0.41851, -2.10225, -3.45598, -1.21537, -7.2795, 2.98966, -6.74793, -3.18562, 1.02646, -0.86256, -6.10155, 2.63628, -5.57011, -1.36455, -2.6731, -0.68615, -5.51256, 2.45958, -4.98119], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "offset": 126, "vertices": [-0.19694, 0.12496, -0.11475, -0.38313, 0.11494, -0.38308, -0.43064, -0.06428, -0.11222, -0.79379, -0.52342, -0.05445, -0.2735, -0.44961, -0.15812, -0.94704, 0.38895, -0.87789, -0.41444, 0.13354, -0.11222, -0.79379, 0.34297, -0.72466, -0.17752, -0.34776, -0.08927, -0.71717, 0.31998, -0.64804], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "offset": 126, "vertices": [-1.51377, 0.96053, -0.88202, -2.94493, 0.8835, -2.9446, -3.31017, -0.49407, -0.86255, -6.10152, -4.0233, -0.41851, -2.10225, -3.45598, -1.21537, -7.2795, 2.98966, -6.74793, -3.18562, 1.02646, -0.86256, -6.10155, 2.63628, -5.57011, -1.36455, -2.6731, -0.68615, -5.51256, 2.45958, -4.98119], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.4667, "offset": 126, "vertices": [-0.19694, 0.12496, -0.11475, -0.38313, 0.11494, -0.38308, -0.43064, -0.06428, -0.11222, -0.79379, -0.52342, -0.05445, -0.2735, -0.44961, -0.15812, -0.94704, 0.38895, -0.87789, -0.41444, 0.13354, -0.11222, -0.79379, 0.34297, -0.72466, -0.17752, -0.34776, -0.08927, -0.71717, 0.31998, -0.64804], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6333, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "offset": 126, "vertices": [-1.51377, 0.96053, -0.88202, -2.94493, 0.8835, -2.9446, -3.31017, -0.49407, -0.86255, -6.10152, -4.0233, -0.41851, -2.10225, -3.45598, -1.21537, -7.2795, 2.98966, -6.74793, -3.18562, 1.02646, -0.86256, -6.10155, 2.63628, -5.57011, -1.36455, -2.6731, -0.68615, -5.51256, 2.45958, -4.98119], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.0667, "offset": 126, "vertices": [-0.19694, 0.12496, -0.11475, -0.38313, 0.11494, -0.38308, -0.43064, -0.06428, -0.11222, -0.79379, -0.52342, -0.05445, -0.2735, -0.44961, -0.15812, -0.94704, 0.38895, -0.87789, -0.41444, 0.13354, -0.11222, -0.79379, 0.34297, -0.72466, -0.17752, -0.34776, -0.08927, -0.71717, 0.31998, -0.64804], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "offset": 126, "vertices": [-1.51377, 0.96053, -0.88202, -2.94493, 0.8835, -2.9446, -3.31017, -0.49407, -0.86255, -6.10152, -4.0233, -0.41851, -2.10225, -3.45598, -1.21537, -7.2795, 2.98966, -6.74793, -3.18562, 1.02646, -0.86256, -6.10155, 2.63628, -5.57011, -1.36455, -2.6731, -0.68615, -5.51256, 2.45958, -4.98119], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.7, "offset": 126, "vertices": [-0.19694, 0.12496, -0.11475, -0.38313, 0.11494, -0.38308, -0.43064, -0.06428, -0.11222, -0.79379, -0.52342, -0.05445, -0.2735, -0.44961, -0.15812, -0.94704, 0.38895, -0.87789, -0.41444, 0.13354, -0.11222, -0.79379, 0.34297, -0.72466, -0.17752, -0.34776, -0.08927, -0.71717, 0.31998, -0.64804], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.8667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "offset": 126, "vertices": [-1.51377, 0.96053, -0.88202, -2.94493, 0.8835, -2.9446, -3.31017, -0.49407, -0.86255, -6.10152, -4.0233, -0.41851, -2.10225, -3.45598, -1.21537, -7.2795, 2.98966, -6.74793, -3.18562, 1.02646, -0.86256, -6.10155, 2.63628, -5.57011, -1.36455, -2.6731, -0.68615, -5.51256, 2.45958, -4.98119], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "offset": 126, "vertices": [-0.19694, 0.12496, -0.11475, -0.38313, 0.11494, -0.38308, -0.43064, -0.06428, -0.11222, -0.79379, -0.52342, -0.05445, -0.2735, -0.44961, -0.15812, -0.94704, 0.38895, -0.87789, -0.41444, 0.13354, -0.11222, -0.79379, 0.34297, -0.72466, -0.17752, -0.34776, -0.08927, -0.71717, 0.31998, -0.64804], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "offset": 126, "vertices": [-1.51377, 0.96053, -0.88202, -2.94493, 0.8835, -2.9446, -3.31017, -0.49407, -0.86255, -6.10152, -4.0233, -0.41851, -2.10225, -3.45598, -1.21537, -7.2795, 2.98966, -6.74793, -3.18562, 1.02646, -0.86256, -6.10155, 2.63628, -5.57011, -1.36455, -2.6731, -0.68615, -5.51256, 2.45958, -4.98119], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12.9333, "offset": 126, "vertices": [-0.19694, 0.12496, -0.11475, -0.38313, 0.11494, -0.38308, -0.43064, -0.06428, -0.11222, -0.79379, -0.52342, -0.05445, -0.2735, -0.44961, -0.15812, -0.94704, 0.38895, -0.87789, -0.41444, 0.13354, -0.11222, -0.79379, 0.34297, -0.72466, -0.17752, -0.34776, -0.08927, -0.71717, 0.31998, -0.64804]}]}, "st": {"tst": [{"offset": 54, "vertices": [-0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.18175, -0.00647, 0.08076, 0.16295, 0.08076, 0.16295], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "offset": 54, "vertices": [-1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -0.49408, -0.01758, 0.21955, 0.44296, 0.21955, 0.44296], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6, "offset": 54, "vertices": [-0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.18175, -0.00647, 0.08076, 0.16295, 0.08076, 0.16295], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "offset": 54, "vertices": [-1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -0.49408, -0.01758, 0.21955, 0.44296, 0.21955, 0.44296], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.2333, "offset": 54, "vertices": [-0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.18175, -0.00647, 0.08076, 0.16295, 0.08076, 0.16295], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "offset": 54, "vertices": [-1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -0.49408, -0.01758, 0.21955, 0.44296, 0.21955, 0.44296], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.8333, "offset": 54, "vertices": [-0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.18175, -0.00647, 0.08076, 0.16295, 0.08076, 0.16295], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "offset": 54, "vertices": [-1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -0.49408, -0.01758, 0.21955, 0.44296, 0.21955, 0.44296], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.4667, "offset": 54, "vertices": [-0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.18175, -0.00647, 0.08076, 0.16295, 0.08076, 0.16295], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "offset": 54, "vertices": [-1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -0.49408, -0.01758, 0.21955, 0.44296, 0.21955, 0.44296], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.0667, "offset": 54, "vertices": [-0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.18175, -0.00647, 0.08076, 0.16295, 0.08076, 0.16295], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 9.2, "offset": 54, "vertices": [-1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -0.49408, -0.01758, 0.21955, 0.44296, 0.21955, 0.44296], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.7, "offset": 54, "vertices": [-0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.18175, -0.00647, 0.08076, 0.16295, 0.08076, 0.16295], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.0333, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "offset": 54, "vertices": [-1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -0.49408, -0.01758, 0.21955, 0.44296, 0.21955, 0.44296], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "offset": 54, "vertices": [-0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.18175, -0.00647, 0.08076, 0.16295, 0.08076, 0.16295], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 11.6333, "curve": 0.25, "c3": 0.75}, {"time": 12.4333, "offset": 54, "vertices": [-1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -1.48226, -0.05274, 0.65865, 1.3289, -0.49408, -0.01758, 0.21955, 0.44296, 0.21955, 0.44296], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 12.9333, "offset": 54, "vertices": [-0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.54526, -0.0194, 0.24229, 0.48884, -0.18175, -0.00647, 0.08076, 0.16295, 0.08076, 0.16295]}]}, "c1": {"c1": [{"offset": 126, "vertices": [-0.08348, -0.02282, -0.08651, 0.00223, -0.04132, 0.10227, 0.04961, 0.09851, -0.40371, 0.01038, -0.19283, 0.47724, -0.34695, 0.20669, -0.15151, 0.37498, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859, -0.12396, 0.3068, -0.22304, 0.13287, 0.14884, 0.29553, -0.15047, 0.21157, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "offset": 126, "vertices": [-0.64165, -0.17539, -0.66497, 0.01711, -0.31761, 0.78607, 0.38135, 0.75719, -3.10318, 0.07978, -1.4822, 3.66833, -2.66686, 1.58875, -1.16458, 2.88227, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763, -0.95283, 2.35823, -1.71442, 1.02133, 1.14408, 2.27161, -1.1566, 1.62625, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6, "offset": 126, "vertices": [-0.08348, -0.02282, -0.08651, 0.00223, -0.04132, 0.10227, 0.04961, 0.09851, -0.40371, 0.01038, -0.19283, 0.47724, -0.34695, 0.20669, -0.15151, 0.37498, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859, -0.12396, 0.3068, -0.22304, 0.13287, 0.14884, 0.29553, -0.15047, 0.21157, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "offset": 126, "vertices": [-0.64165, -0.17539, -0.66497, 0.01711, -0.31761, 0.78607, 0.38135, 0.75719, -3.10318, 0.07978, -1.4822, 3.66833, -2.66686, 1.58875, -1.16458, 2.88227, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763, -0.95283, 2.35823, -1.71442, 1.02133, 1.14408, 2.27161, -1.1566, 1.62625, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.2333, "offset": 126, "vertices": [-0.08348, -0.02282, -0.08651, 0.00223, -0.04132, 0.10227, 0.04961, 0.09851, -0.40371, 0.01038, -0.19283, 0.47724, -0.34695, 0.20669, -0.15151, 0.37498, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859, -0.12396, 0.3068, -0.22304, 0.13287, 0.14884, 0.29553, -0.15047, 0.21157, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "offset": 126, "vertices": [-0.64165, -0.17539, -0.66497, 0.01711, -0.31761, 0.78607, 0.38135, 0.75719, -3.10318, 0.07978, -1.4822, 3.66833, -2.66686, 1.58875, -1.16458, 2.88227, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763, -0.95283, 2.35823, -1.71442, 1.02133, 1.14408, 2.27161, -1.1566, 1.62625, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.8333, "offset": 126, "vertices": [-0.08348, -0.02282, -0.08651, 0.00223, -0.04132, 0.10227, 0.04961, 0.09851, -0.40371, 0.01038, -0.19283, 0.47724, -0.34695, 0.20669, -0.15151, 0.37498, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859, -0.12396, 0.3068, -0.22304, 0.13287, 0.14884, 0.29553, -0.15047, 0.21157, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "offset": 126, "vertices": [-0.64165, -0.17539, -0.66497, 0.01711, -0.31761, 0.78607, 0.38135, 0.75719, -3.10318, 0.07978, -1.4822, 3.66833, -2.66686, 1.58875, -1.16458, 2.88227, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763, -0.95283, 2.35823, -1.71442, 1.02133, 1.14408, 2.27161, -1.1566, 1.62625, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.4667, "offset": 126, "vertices": [-0.08348, -0.02282, -0.08651, 0.00223, -0.04132, 0.10227, 0.04961, 0.09851, -0.40371, 0.01038, -0.19283, 0.47724, -0.34695, 0.20669, -0.15151, 0.37498, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859, -0.12396, 0.3068, -0.22304, 0.13287, 0.14884, 0.29553, -0.15047, 0.21157, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.6333, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "offset": 126, "vertices": [-0.64165, -0.17539, -0.66497, 0.01711, -0.31761, 0.78607, 0.38135, 0.75719, -3.10318, 0.07978, -1.4822, 3.66833, -2.66686, 1.58875, -1.16458, 2.88227, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763, -0.95283, 2.35823, -1.71442, 1.02133, 1.14408, 2.27161, -1.1566, 1.62625, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.0667, "offset": 126, "vertices": [-0.08348, -0.02282, -0.08651, 0.00223, -0.04132, 0.10227, 0.04961, 0.09851, -0.40371, 0.01038, -0.19283, 0.47724, -0.34695, 0.20669, -0.15151, 0.37498, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859, -0.12396, 0.3068, -0.22304, 0.13287, 0.14884, 0.29553, -0.15047, 0.21157, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "offset": 126, "vertices": [-0.64165, -0.17539, -0.66497, 0.01711, -0.31761, 0.78607, 0.38135, 0.75719, -3.10318, 0.07978, -1.4822, 3.66833, -2.66686, 1.58875, -1.16458, 2.88227, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763, -0.95283, 2.35823, -1.71442, 1.02133, 1.14408, 2.27161, -1.1566, 1.62625, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 9.7, "offset": 126, "vertices": [-0.08348, -0.02282, -0.08651, 0.00223, -0.04132, 0.10227, 0.04961, 0.09851, -0.40371, 0.01038, -0.19283, 0.47724, -0.34695, 0.20669, -0.15151, 0.37498, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859, -0.12396, 0.3068, -0.22304, 0.13287, 0.14884, 0.29553, -0.15047, 0.21157, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.8667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "offset": 126, "vertices": [-0.64165, -0.17539, -0.66497, 0.01711, -0.31761, 0.78607, 0.38135, 0.75719, -3.10318, 0.07978, -1.4822, 3.66833, -2.66686, 1.58875, -1.16458, 2.88227, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763, -0.95283, 2.35823, -1.71442, 1.02133, 1.14408, 2.27161, -1.1566, 1.62625, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "offset": 126, "vertices": [-0.08348, -0.02282, -0.08651, 0.00223, -0.04132, 0.10227, 0.04961, 0.09851, -0.40371, 0.01038, -0.19283, 0.47724, -0.34695, 0.20669, -0.15151, 0.37498, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859, -0.12396, 0.3068, -0.22304, 0.13287, 0.14884, 0.29553, -0.15047, 0.21157, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 11.4667, "curve": 0.25, "c3": 0.75}, {"time": 12.2667, "offset": 126, "vertices": [-0.64165, -0.17539, -0.66497, 0.01711, -0.31761, 0.78607, 0.38135, 0.75719, -3.10318, 0.07978, -1.4822, 3.66833, -2.66686, 1.58875, -1.16458, 2.88227, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763, -0.95283, 2.35823, -1.71442, 1.02133, 1.14408, 2.27161, -1.1566, 1.62625, -2.09539, 1.2483, 1.39832, 2.77641, -1.41363, 1.98763], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 12.9333, "offset": 126, "vertices": [-0.08348, -0.02282, -0.08651, 0.00223, -0.04132, 0.10227, 0.04961, 0.09851, -0.40371, 0.01038, -0.19283, 0.47724, -0.34695, 0.20669, -0.15151, 0.37498, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859, -0.12396, 0.3068, -0.22304, 0.13287, 0.14884, 0.29553, -0.15047, 0.21157, -0.2726, 0.1624, 0.18192, 0.3612, -0.18391, 0.25859]}]}}}}, "xie_zi": {"slots": {"tj1": {"attachment": [{"name": "xj1"}]}, "tst": {"attachment": [{"name": "xst"}]}, "tt3": {"attachment": [{"name": "t3"}]}, "tt2": {"attachment": [{"name": "t2"}]}, "tw1": {"attachment": [{"name": "w1"}]}, "tyingzi": {"attachment": [{"name": "<PERSON><PERSON><PERSON>"}]}, "tj2": {"attachment": [{"name": "xj2"}]}, "tt1": {"attachment": [{"name": "t1"}]}, "ttt1": {"attachment": [{"name": "tt1"}]}, "ttt3": {"attachment": [{"name": "tt3"}]}, "ttt2": {"attachment": [{"name": "tt2"}]}}, "bones": {"tj1": {"rotate": [{"angle": 1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 22.8, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.8333, "angle": 1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": 22.8, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": 1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.5667, "curve": 0.25, "c3": 0.75}, {"time": 15.4667, "angle": 22.8, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 17.5333, "angle": 1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 18.4, "curve": 0.25, "c3": 0.75}, {"time": 21.3, "angle": 22.8, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 23.3667, "angle": 1.74}]}, "tj2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -42, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -42, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": -42, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": -42, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "tj6": {"rotate": [{"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -22.8, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "curve": "stepped"}, {"time": 7.8667, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": -22.8, "curve": 0.25, "c3": 0.75}, {"time": 11.4, "curve": "stepped"}, {"time": 13.7333, "curve": 0.25, "c3": 0.75}, {"time": 15.4667, "angle": -22.8, "curve": 0.25, "c3": 0.75}, {"time": 17.2333, "curve": "stepped"}, {"time": 19.5667, "curve": 0.25, "c3": 0.75}, {"time": 21.3, "angle": -22.8, "curve": 0.25, "c3": 0.75}, {"time": 23.0667}]}, "tj4": {"rotate": [{"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 24, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": "stepped"}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": 24, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": "stepped"}, {"time": 12.8333, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": 24, "curve": 0.25, "c3": 0.75}, {"time": 16.3333, "curve": "stepped"}, {"time": 18.7, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": 24, "curve": 0.25, "c3": 0.75}, {"time": 22.2}]}, "tst": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": -1.2, "y": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "x": -1.2, "y": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "x": -1.2, "y": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "x": -1.2, "y": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "tt7": {"rotate": [{"angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 2.4667}, {"time": 5.3667, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5.8333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 8.3}, {"time": 11.2, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 11.6667, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 14.1333}, {"time": 17.0333, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 17.5333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 19.9667}, {"time": 22.9, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 23.3667, "angle": 10.1}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "tt4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}], "scale": [{"x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "curve": 0.25, "c3": 0.75}, {"time": 23.3667, "x": 0.92, "y": 0.88}]}, "tt1": {"rotate": [{"angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 2.4667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5.8333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 8.3, "curve": 0.25, "c3": 0.75}, {"time": 11.2, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 11.6667, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 14.1333, "curve": 0.25, "c3": 0.75}, {"time": 17.0333, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 17.5333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 19.9667, "curve": 0.25, "c3": 0.75}, {"time": 22.9, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 23.3667, "angle": 10.1}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "tt8": {"rotate": [{"angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 2.4667}, {"time": 5.3667, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5.8333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 8.3}, {"time": 11.2, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 11.6667, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 14.1333}, {"time": 17.0333, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 17.5333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 19.9667}, {"time": 22.9, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 23.3667, "angle": 10.1}]}, "tt9": {"rotate": [{"angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 2.4667}, {"time": 5.3667, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5.8333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 8.3}, {"time": 11.2, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 11.6667, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 14.1333}, {"time": 17.0333, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 17.5333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 19.9667}, {"time": 22.9, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 23.3667, "angle": 10.1}]}, "tt5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "tt6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "tt2": {"rotate": [{"angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 2.4667}, {"time": 5.3667, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5.8333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 8.3}, {"time": 11.2, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 11.6667, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 14.1333}, {"time": 17.0333, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 17.5333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 19.9667}, {"time": 22.9, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 23.3667, "angle": 10.1}]}, "tt3": {"rotate": [{"angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 2.4667}, {"time": 5.3667, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5.8333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 8.3}, {"time": 11.2, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 11.6667, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 14.1333}, {"time": 17.0333, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 17.5333, "angle": 10.1, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 19.9667}, {"time": 22.9, "angle": 11.11, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 23.3667, "angle": 10.1}]}, "ttt7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}], "scale": [{"x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "curve": 0.25, "c3": 0.75}, {"time": 23.3667, "x": 0.92, "y": 0.88}]}, "ttt4": {"rotate": [{"angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 5.8333, "angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 11.6667, "angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 14.2, "curve": 0.25, "c3": 0.75}, {"time": 17.1, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 17.5333, "angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 20.0333, "curve": 0.25, "c3": 0.75}, {"time": 22.9333, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 23.3667, "angle": -9.25}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "ttt1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}], "scale": [{"x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "x": 0.92, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "curve": 0.25, "c3": 0.75}, {"time": 23.3667, "x": 0.92, "y": 0.88}]}, "ttt2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "ttt3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": -9.99, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "ttt6": {"rotate": [{"angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 5.8333, "angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 11.6667, "angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 14.2, "curve": 0.25, "c3": 0.75}, {"time": 17.1, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 17.5333, "angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 20.0333, "curve": 0.25, "c3": 0.75}, {"time": 22.9333, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 23.3667, "angle": -9.25}]}, "ttt5": {"rotate": [{"angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 5.8333, "angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 11.6667, "angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 14.2, "curve": 0.25, "c3": 0.75}, {"time": 17.1, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 17.5333, "angle": -9.25, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 20.0333, "curve": 0.25, "c3": 0.75}, {"time": 22.9333, "angle": -9.99, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 23.3667, "angle": -9.25}]}, "ttt8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "ttt9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "tw1": {"rotate": [{"angle": -30.32, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 16.31, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": -30.32, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": 16.31, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": -30.32, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": 16.31, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "angle": -30.32, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": 16.31, "curve": 0.25, "c3": 0.75}, {"time": 23.3667, "angle": -30.32}]}, "tw2": {"rotate": [{"angle": -4.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -1.69, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.8333, "angle": -4.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.4333, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -1.69, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.6667, "angle": -4.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12.2667, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 15.1667, "angle": -1.69, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 17.5333, "angle": -4.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 18.1, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 21.0333, "angle": -1.69, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 23.3667, "angle": -4.68}]}, "tw3": {"rotate": [{"angle": -3.86, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1667, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "angle": -1.69, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.8333, "angle": -3.86, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "angle": -1.69, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.6667, "angle": -3.86, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.8333, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 15.7667, "angle": -1.69, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 17.5333, "angle": -3.86, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 18.7, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 21.6, "angle": -1.69, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 23.3667, "angle": -3.86}]}, "tw4": {"rotate": [{"angle": -2.95, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.7667, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.69, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.8333, "angle": -2.95, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.6, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": -1.69, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.6667, "angle": -2.95, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 13.4333, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 16.3333, "angle": -1.69, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 17.5333, "angle": -2.95, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 19.2667, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 22.2, "angle": -1.69, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 23.3667, "angle": -2.95}]}, "tw5": {"rotate": [{"angle": -2.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.3333, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -1.69, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.8333, "angle": -2.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 8.1667, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 11.1, "angle": -1.69, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.6667, "angle": -2.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 14, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 16.9333, "angle": -1.69, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 17.5333, "angle": -2.14, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 19.8667, "angle": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 22.7667, "angle": -1.69, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 23.3667, "angle": -2.14}]}, "tst2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -10.13, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -10.13, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": -10.13, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": -10.13, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}, "tst3": {"rotate": [{"angle": -2.45, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -10.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.8333, "angle": -2.45, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": -10.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": -2.45, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.5667, "curve": 0.25, "c3": 0.75}, {"time": 15.4667, "angle": -10.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 17.5333, "angle": -2.45, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 18.4, "curve": 0.25, "c3": 0.75}, {"time": 21.3, "angle": -10.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 23.3667, "angle": -2.45}]}, "tst4": {"rotate": [{"angle": -14.72, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": -14.72, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": -14.72, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "angle": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "angle": -14.72, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "angle": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 23.3667, "angle": -14.72}]}, "troot": {"translate": [{}, {"time": 11.6667, "x": -170.01, "y": -75.36}, {"time": 23.3667}], "scale": [{"time": 11.5667, "curve": 0.25, "c3": 0.75}, {"time": 11.7333, "x": -1, "curve": "stepped"}, {"time": 23.2333, "x": -1, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "x": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 14.6, "x": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 17.5333, "curve": 0.25, "c3": 0.75}, {"time": 20.4333, "x": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 23.3667}]}}}}}