import { UITransform, _decorator, sp } from "cc";
import { Section } from "../../../lib/object/Section";
import { GOBuff } from "./GOBuff";
import GameObject from "../../../lib/object/GameObject";
import ResMgr from "../../../lib/common/ResMgr";
const { ccclass, property } = _decorator;

@ccclass("BEBuff")
export default class BEBuff extends Section {
  public static sectionName(): string {
    return "BEBuff";
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.ready();
  }

  public onStart(): void {
    super.onStart();
  }

  protected playPrefab() {
    let role = (this.getSub() as GOBuff).getDetail().attachment;
  }

  protected async initRender(path: string) {
    ResMgr.loadPrefab(path, this.onLoadRender.bind(this));
  }

  protected onLoadRender(prefab) {}

  public removeBe() {}

  public onRemove() {}
}
