import { Prefab, Node, SpriteFrame, UITransform } from "cc";

import MsgEnum from "../../game/event/MsgEnum";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import { FightModule } from "../fight/src/FightModule";
import { PlayerModule } from "../player/PlayerModule";
import { CityEvent } from "./CityEvent";
import { CityModule } from "./CityModule";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { ConfigBuildCrystalRecord, ConfigBuildRecord } from "./CityConstant";
import AssetUtils from "../../lib/utils/AssetUtils";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";
import { <PERSON>s<PERSON>elper } from "../../../platform/src/ResHelper";
import { CityMessage } from "../../game/net/protocol/City";
import { addAttrMap, AttrArray2ToMap } from "../../lib/utils/AttrTool";
import { IConfigCopyMain } from "db://assets/GameScrpit/game/JsonDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);

const WorkerNum = [101, 201, 301, 601, 1101, 1501, 2001, 3001, 5001, 6001, 9701, 15301];
const WorkerCostCoefficient = [2.08, 2.12, 2.18, 2.3, 2.45, 2.6, 2.8, 3, 3.25, 3.3, 3.4, 3.5];
const WorkerCityCoefficient = [
  1, 2.89, 7.9, 22.9, 159, 63, 400, 990, 2300, 5000, 12500, 25000, 50000, 100000, 200000, 400000, 800000, 1600000,
  3000000, 6000000, 10000000,
];

export class CityService {
  private _tickId: number;

  public async init() {
    MsgMgr.off(MsgEnum.ON_CHAPTER_PASS, this.updatePopover, this);
    MsgMgr.off(CityEvent.ON_UICity_CHANGE, this.updatePopover, this);
    MsgMgr.off(MsgEnum.ON_ENERGYFACTORY_UPDATE, this.updatePopover, this);
    MsgMgr.off(MsgEnum.ON_CITY_HAO_ZHAO_UPDATE, this.updatePopover, this);

    MsgMgr.on(MsgEnum.ON_CHAPTER_PASS, this.updatePopover, this);
    MsgMgr.on(CityEvent.ON_UICity_CHANGE, this.updatePopover, this);
    MsgMgr.on(MsgEnum.ON_ENERGYFACTORY_UPDATE, this.updatePopover, this);
    MsgMgr.on(MsgEnum.ON_CITY_HAO_ZHAO_UPDATE, this.updatePopover, this);

    if (this._tickId) {
      TickerMgr.clearInterval(this._tickId);
    }

    this._tickId = TickerMgr.setInterval(3, this.updatePopover.bind(this), false);
  }

  /** 红点更新 */
  public updatePopover() {
    let cityIdList: string[] = Object.keys(JsonMgr.instance.jsonList.c_build);

    // 升级红点
    for (let idx in cityIdList) {
      if (!CityModule.data.cityMessageMap) {
        break;
      }

      let cityMsg: CityMessage = CityModule.data.cityMessageMap.get(Number(cityIdList[idx]));
      if (!cityMsg) {
        continue;
      }

      let cfgBuildLv = CityModule.data.getConfigBuildLv(cityMsg.cityId, cityMsg.level);

      if (cfgBuildLv.lvMax <= cityMsg.level) {
        BadgeMgr.instance.setShowById(BadgeType.UIMajorCity["btn_name" + cityMsg.cityId].btn_shengji_node.id, false);
        continue;
      }

      // 需求数量
      const existNum = PlayerModule.data.getItemNum(cfgBuildLv.cfg);

      // 下级配置
      let cfgBuildLvNext = CityModule.data.getConfigBuildLv(cityMsg.cityId, cityMsg.level + 1);
      BadgeMgr.instance.setShowById(
        BadgeType.UIMajorCity["btn_name" + cityMsg.cityId].btn_shengji_node.id,
        existNum >= cfgBuildLvNext.cost
      );
    }

    // 建造红点
    for (let idx in cityIdList) {
      const cityId = Number(cityIdList[idx]);

      const configBuild: ConfigBuildRecord = JsonMgr.instance.jsonList.c_build[cityId];
      let buildShow = false;
      if (!CityModule.data.cityMessageMap.get(cityId)) {
        const chapterId = FightModule.data.chapterId;
        if (chapterId > configBuild.unlockLv || configBuild.unlockLv == 0) {
          if (Number(configBuild.goldSpeed) <= PlayerModule.data.playerBattleAttrResponse.speed) {
            buildShow = true;
          }
        }
      }

      BadgeMgr.instance.setShowById(BadgeType.UIMajorCity["build" + cityId].id, buildShow);
    }

    // 水晶升级
    const config_buildCrystal_next = CityModule.data.getConfigBuildCrystal(CityModule.data.energyFactoryMsg.level + 1);
    if (config_buildCrystal_next.lvMax > CityModule.data.energyFactoryMsg.level) {
      const existNum = PlayerModule.data.getItemNum(config_buildCrystal_next.cfg);
      const rs = existNum >= config_buildCrystal_next.cost;
      BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_level_up.btn_upgrade.id, rs);
    }

    // 五族荣耀-据点等级
    let config = JsonMgr.instance.jsonList.c_buildLvReward;
    let list = Object.keys(config)
      .map(Number)
      .sort((a, b) => {
        return a - b;
      });
    let rsWzry = false;
    for (let i = 0; i < list.length; i++) {
      let id = list[i];
      let configBuildLvReward = config[id];
      if (configBuildLvReward.buildId <= CityModule.data.cityTotalLevel) {
        if (CityModule.data.cityAggregateMessage.nextCityLevelRewardIndex <= i) {
          rsWzry = true;
          break;
        }
      }
    }
    // 五族荣耀
    BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_wuzurongyao.btn_tab1.id, rsWzry);

    // 宝箱红点
    BadgeMgr.instance.setShowById(
      BadgeType.UIMajorCity.UICityDetail.btn_tab_box.btn_open.id,
      CityModule.data.cityAggregateMessage.boxTotalCount > 0
    );

    // 宝箱升级红点
    const canLevelUp = this.canUpLevelBox();
    BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.UICityDetail.btn_tab_level_up.btn_level_up.id, canLevelUp);

    //三界小家
    let cjBool = this.IsSanJieXiaoJiaCj();
    BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_sanjiexiaojia.btn_SJXJ_btn_chengjiu.id, cjBool);

    let zjBool = this.IsSanJieXiaoJiaZj();
    BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_sanjiexiaojia.btn_SJXJ_btn_zhanjiang.id, zjBool);

    this.SanJieXiaoJiaLvOpen();

    /**五族预览红点 */
    this.WorldPreviewCityRed();
  }

  /**五族预览红点 */
  private WorldPreviewCityRed() {
    let db = JsonMgr.instance.jsonList.c_buildShow;
    for (let i in db) {
      const info = db[i];
      let bool = this.isTrimOk(info);
      let name = "item0" + info.id;
      BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_wu_zu_yu_lan[name].open.id, bool);
    }
  }

  private isTrimOk(cfgBuildShow) {
    let list = cfgBuildShow.trimId;
    let decorationIdList = CityModule.data.cityAggregateMessage.decorationIdList;

    let bool = true;
    for (let i = 0; i < list.length; i++) {
      if (decorationIdList.includes(list[i]) == false) {
        bool = false;
        break;
      }
    }

    for (let i = 0; i < cfgBuildShow.buildShowId.length; i++) {
      if (CityModule.data.cityMessageMap.has(cfgBuildShow.buildShowId[i]) == false) {
        bool = false;
        break;
      }
    }

    if (CityModule.data.cityAggregateMessage.raceRewardIdList.includes(cfgBuildShow.id) == true) {
      bool = false;
    }

    return bool;
  }

  //三界小家 升级解锁
  private SanJieXiaoJiaLvOpen() {
    let idList = [];
    let db = JsonMgr.instance.jsonList.c_home;
    for (let i in db) {
      if (db[i].id < 200) idList.push(db[i].id);
    }

    for (let i = 0; i < idList.length; i++) {
      let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[idList[i]] || 0;
      let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
      let nextDb = this.nextLvDb(idList[i]);
      let unlock = nextDb?.unlock || 0;

      let bool = false;

      if (bool == false && lv <= 0 && bloom >= unlock) {
        bool = true;
      }

      if (bool == false && lv > 0 && bloom >= unlock && nextDb) {
        bool = true;
      }

      BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_sanjiexiaojia["home_" + idList[i]].id, bool);
    }
  }

  private nextLvDb(id) {
    let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[id] || 0;
    lv += 1;
    let newdbId = lv * 100 + (id % 100);
    let db = JsonMgr.instance.jsonList.c_home[newdbId];

    if (!db) {
      return null;
    }

    return db;
  }

  //三界小家红点 成就
  private IsSanJieXiaoJiaCj() {
    let db = JsonMgr.instance.jsonList.c_home;
    if (CityModule.data.cityAggregateMessage.smallHomeAllLevelReward.includes(2) == false) {
      let lvBool = this.isCityLv(2);
      if (lvBool == true) {
        return true;
      }
    }

    if (CityModule.data.cityAggregateMessage.smallHomeAllLevelReward.includes(1) == false) {
      let lvBool = this.isCityLv(1);
      if (lvBool == true) {
        return true;
      }
    }

    for (let i in db) {
      if (CityModule.data.cityAggregateMessage.smallHomeRewardList.includes(db[i].id) == false) {
        let id = Math.floor(db[i].id % 100) + 100;
        let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[id] || 0;
        if (lv >= Math.floor(db[i].id / 100)) {
          return true;
        }
      }
    }

    return false;
  }

  //三界小家 战将
  private IsSanJieXiaoJiaZj() {
    if (CityModule.data.cityAggregateMessage.smallHomeAllLevelReward.includes(3) == false) {
      let lvBool = this.isCityLv(3);
      if (lvBool == true) {
        return true;
      }
    }

    return false;
  }

  private isCityLv(lv: number) {
    let db = JsonMgr.instance.jsonList.c_home;
    let idList = [];
    for (let i in db) {
      if (db[i].id < 200) idList.push(db[i].id);
    }

    for (let i of idList) {
      if (!CityModule.data.cityAggregateMessage.smallHomeLevelMap[i]) return false;
      if (CityModule.data.cityAggregateMessage.smallHomeLevelMap[i] < lv) return false;
    }

    return true;
  }

  /**
   *
   * @returns 女娲形象prefab
   */
  public async loadNvWaImage(level: number = -1): Promise<Node> {
    let enertyLevel = CityModule.data.energyFactoryMsg.level;
    if (level != -1) {
      enertyLevel = level;
    }

    let lookLevel = this.getNvWaImageKey(enertyLevel);

    const prefabEffect: Prefab = (await AssetUtils.loadAsset(
      BundleEnum.BUNDLE_G_GAME_MAP,
      `prefab/building/city_100`,
      Prefab
    )) as Prefab;
    let nodeNvwa = await AssetUtils.cloneResSync(prefabEffect);
    nodeNvwa.active = true;

    return nodeNvwa;
  }

  public getNvWaImageKey(level: number) {
    let config: ConfigBuildCrystalRecord = JsonMgr.instance.jsonList.c_buildCrystal[1001];
    if (level < config.unlockLvList[0]) {
      return 1;
    }
    let lookLevel = 1;

    for (let i = 0; i < config.unlockLvList.length; i++) {
      if (level >= config.unlockLvList[i]) {
        lookLevel += 1;
      } else {
        break;
      }
    }

    return lookLevel;
  }

  public async getCitySpriteFrame(cityId: number): Promise<SpriteFrame> {
    let cityInfo = CityModule.data.cityMessageMap.get(cityId);

    let configBuild = CityModule.data.getConfigBuild(cityId);

    let spIdx = 0;
    if (cityInfo && cityInfo.level > 0) {
      for (let idx in configBuild.unlockLvList) {
        spIdx = Number(idx) + 1;
        if (configBuild.unlockLvList[idx] >= cityInfo.level) {
          break;
        }
      }
    }

    spIdx = Math.max(spIdx, 0);
    spIdx = Math.min(spIdx, 3);

    let cityBundleName = `bundle_city_${cityId}`;

    let spUrl = `C${cityId}-${spIdx}`;

    return ResHelper.preLoadResSync(cityBundleName, spUrl);
  }

  public getCityLevelList(cityId: number): IConfigCopyMain[] {
    let db = JsonMgr.instance.jsonList.c_copyMain;
    let arr = [];

    let list = Object.keys(db);

    for (let i = 0; i < list.length; i++) {
      let info = db[list[i]];
      if (info.buildId <= cityId) {
        arr.push(info);
      }
    }
    return arr;
  }

  /**
   * 找到最小升级消耗的城池
   */
  public findMinCostLevelUpCityId(): number {
    let cost = -1;
    let cityIdFind = -1;
    for (let idx in CityModule.data.cityMessageList) {
      let cityMsg = CityModule.data.cityMessageList[idx];
      let configBuildNext = CityModule.data.getConfigBuildLv(cityMsg.cityId, cityMsg.level + 1);
      if (cost == -1 || configBuildNext.cost <= cost) {
        cityIdFind = cityMsg.cityId;
        cost = configBuildNext.cost;
      }
    }

    return cityIdFind;
  }

  /**
   * 招募员工气运消耗
   * @param cityId
   * @param worker 已有员工数
   * @param workerNeed 可招募员工数 （等级上限  <= 10）
   * @returns
   */
  public cityBuildWorker(cityId: number, worker: number, workerNeed: number = 10) {
    let cost = 0;
    let cityMap = CityModule.data.cityMessageMap.get(cityId);

    if (cityMap) {
      for (let j = 1; j <= workerNeed; j++) {
        let a = worker + j;
        let b = 3.55; // 最大系数
        for (let i = 0; i < WorkerNum.length; i++) {
          if (a < WorkerNum[i]) {
            b = WorkerCostCoefficient[i];
            break;
          }
        }
        let cost0 = Math.floor(Math.pow(a, b) + 19);
        cost += Math.floor(cost0 * WorkerCityCoefficient[cityMap.cityId - 101]);
      }
    }
    return cost;
  }

  /**
   * 找到最小招募消耗的城池
   */
  public findMinCostCallCityId(): number {
    let cost = -1;
    let cityIdFind = -1;
    for (let idx in CityModule.data.cityMessageList) {
      let cityMsg = CityModule.data.cityMessageList[idx];
      let cost1 = this.cityBuildWorker(cityMsg.cityId, cityMsg.energyHire + cityMsg.itemHire, 1);
      if (cost == -1 || cost1 <= cost) {
        cityIdFind = cityMsg.cityId;
        cost = cost1;
      }
    }

    return cityIdFind;
  }

  /**获取据点形象id */
  public getCityImgIndex(level: number, cityId: number) {
    let c_builddb = JsonMgr.instance.jsonList.c_build[cityId];

    if (level == 0) {
      return 0;
    }

    let index = 0;

    for (let i = 0; i < c_builddb.unlockLvList.length; i++) {
      if (level >= c_builddb.unlockLvList[i]) {
        index += 1;
      } else {
        break;
      }
    }

    return index;
  }

  /** 五族增加属性 */
  public getWuZuAddAttr() {
    // let progress: number =
    //   (CityModule.data.cityTotalNum - lastWorkerNum) / (configWorkerReward.workerNum - lastWorkerNum);
    let totalNum = CityModule.data.cityTotalNum;

    let rsAttrMap = new Map();
    let cfgMap = JsonMgr.instance.jsonList.c_buildWorkerReward;
    let keys = Object.keys(cfgMap).map(Number);
    for (let i in keys) {
      let key = keys[i];
      let cfgBuildWorkerReward = cfgMap[key];
      if (totalNum >= cfgBuildWorkerReward.workerNum) {
        let attrMap = AttrArray2ToMap(cfgBuildWorkerReward.attrAdd);
        addAttrMap(rsAttrMap, attrMap);
      }
    }

    return rsAttrMap;
  }

  /**
   * 展示大小设置
   *
   * @param cityNode 据点
   * @param cityId id
   * @param cityLevel 等级
   */
  public setBuildSize(cityNode: Node, cityId: number, cityLevel: number) {
    let position = JsonMgr.instance.jsonList.c_build[cityId].position[cityLevel];
    let scale = JsonMgr.instance.jsonList.c_build[cityId].scale[cityLevel];

    // 设置位置
    let originalPosition = cityNode.getPosition();
    originalPosition.x = position[0];
    originalPosition.y = position[1];
    cityNode.setPosition(originalPosition);

    // 设置大小
    let originalScale = cityNode.getScale();
    originalScale.x = scale[0];
    originalScale.y = scale[1];
    cityNode.setScale(originalScale);
  }

  // /**自动培养是否勾选 */
  // public getTen(): boolean {
  //   return StorageMgr.loadStr(StorageKeyEnum.十连招募) == "1";
  // }

  // /**更改自动培养 */
  // public upTen(isAuto: boolean, callback?: Function) {
  //   StorageMgr.saveItem(StorageKeyEnum.十连招募, isAuto ? "1" : "0");
  //   callback && callback();
  // }

  // 计算升级需要的人数
  public getBuildNeed(level: number): number {
    if (level < 1) {
      log.error(" getBuildNeed < 1");
      level = 1;
    }

    let cfgMap = JsonMgr.instance.jsonList.c_buildBox;
    const key = Object.keys(cfgMap).map(Number);
    let cfg = cfgMap[key[0]];

    let buildNeed = cfg.buildNum[level - 1];

    if (cfg.buildNum.length >= level) {
      buildNeed = cfg.buildNum[level - 1];
    } else {
      buildNeed = cfg.buildNum[cfg.buildNum.length - 1];
    }
    return buildNeed;
  }

  public getPeopleNeed(level: number): number {
    if (level < 1) {
      log.error("getPeopleNeedlevel < 1");
      level = 1;
    }

    let cfgMap = JsonMgr.instance.jsonList.c_buildBox;
    const key = Object.keys(cfgMap).map(Number);
    let cfg = cfgMap[key[0]];

    let callNeed = cfg.callTime[level - 1];

    if (cfg.callTime.length >= level) {
      callNeed = cfg.callTime[level - 1];
    } else {
      callNeed = cfg.callTime[cfg.callTime.length - 1];
    }
    return callNeed;
  }

  // 是否可以升级号召宝箱
  public canUpLevelBox(level: number = 0): boolean {
    if (level == 0) {
      level = CityModule.data.cityAggregateMessage.boxLevel;
    }

    let buildNeed = this.getBuildNeed(level);
    if (buildNeed > CityModule.data.cityMessageList.length) {
      return false;
    }

    let peopleNum = this.getPeopleNeed(level);
    if (peopleNum > CityModule.data.cityTotalNum) {
      return false;
    }

    return true;
  }

  /**
   * 宝箱是否已满
   */
  public isBoxMax(): boolean {
    let cfgMap = JsonMgr.instance.jsonList.c_buildBox;
    const key = Object.keys(cfgMap).map(Number);
    let cfg = cfgMap[key[0]];

    return cfg.boxMax <= CityModule.data.cityAggregateMessage.boxTotalCount;
  }

  public getMissingNumbers(arr: number[]): number[] {
    // 获取数组最大值（处理空数组情况）
    const max = arr.length > 0 ? Math.max(...arr) : 0;

    // 生成完整序列（1到max的连续数字）
    const fullSet = new Set(Array.from({ length: max }, (_, i) => i + 1));

    // 创建原始数组的快速查询集合
    const originalSet = new Set(arr);

    // 过滤出缺失的数字
    const missing = Array.from(fullSet).filter((n) => !originalSet.has(n));

    return missing;
  }
}
