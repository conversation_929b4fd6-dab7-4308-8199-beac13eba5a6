import { _decorator, instantiate, Label, tween, v3, sp } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import Formate from "../../../lib/utils/Formate";
import { CityModule } from "../../../module/city/CityModule";
import { CityCtrl } from "../ui_gameMap/CityCtrl";
import { Node } from "cc";
import { Prefab } from "cc";
import { CityMessage } from "../../net/protocol/City";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { AssetMgr } from "../../../../platform/src/ResHelper";
import ToolExt from "../../common/ToolExt";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { IConfigBuildLv } from "../../JsonDefine";

const { ccclass, property } = _decorator;

export interface ArgsCityBuildRes {
  // 旧繁荣度
  oldBloom: number;
  // 据点信息
  cityMsg: CityMessage;
  // 奖励道具列表
  rewardList: number[];
  // 升级类型
  isCreate: boolean;
}

@ccclass("UICityBuildRes")
export class UICityBuildRes extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UICityBuildRes`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI];
  }

  private argsCityBuildRes: ArgsCityBuildRes;

  public init(param: any) {
    super.init(param);
    this.argsCityBuildRes = param.args;
  }

  protected onEvtShow(): void {
    this.getNode("bg").scale = v3(0, 0, 0);
    this.getNode("tytanchuang")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("tytanchuang")
          .getComponent(sp.Skeleton)
          .setAnimation(0, this.argsCityBuildRes.isCreate ? "zi_jianzaochenggong_1" : "zi_shengjichenggong_1", true);
      });
    this.getNode("tytanchuang")
      .getComponent(sp.Skeleton)
      .setAnimation(0, this.argsCityBuildRes.isCreate ? "zi_jianzaochenggong" : "zi_shengjichenggong", false);
    tween(this.getNode("bg"))
      .to(0.1, { scale: v3(1, 1, 1) })
      .call(() => {
        this.initPopup();
      })
      .start();
    if (this.argsCityBuildRes.isCreate) {
      AudioMgr.instance.playEffect(AudioName.Effect.建筑建造成功);
    } else {
      AudioMgr.instance.playEffect(AudioName.Effect.建筑升级成功);
    }
  }

  protected onEvtClose() {
    this.assetMgr.release();
  }

  private initPopup() {
    let configBuild = CityModule.data.getConfigBuild(this.argsCityBuildRes.cityMsg.cityId);
    let configBuildLv = CityModule.data.getConfigBuildLv(
      this.argsCityBuildRes.cityMsg.cityId,
      this.argsCityBuildRes.cityMsg.level
    );

    let configBuildLvBefore: IConfigBuildLv = null;
    if (this.argsCityBuildRes.cityMsg.level > 0) {
      configBuildLvBefore = CityModule.data.getConfigBuildLv(
        this.argsCityBuildRes.cityMsg.cityId,
        this.argsCityBuildRes.cityMsg.level - 1
      );
    }

    ToolExt.setRaceType(this.getNode("city_type_icon"), configBuild.type, this);

    this.assetMgr.loadPrefab(
      BundleEnum.BUNDLE_G_GAME_MAP,
      `prefab/building/city_${this.argsCityBuildRes.cityMsg.cityId}`,
      (pb: Prefab) => {
        let nodeCity: Node = instantiate(pb);
        nodeCity.getComponent(CityCtrl).setOnlyShow({
          hideUI: true,
          cityLevel: -1,
        });
        nodeCity.getChildByName("btn_name").active = false;
        nodeCity.setPosition(0, 0);
        this.getNode("city_img").addChild(nodeCity);
      }
    );

    // 建筑名称
    this.getNode("city_name").getComponent(Label).string = `${configBuild.name}`;

    let nodeLayout = this.getNode("layout");
    // 繁荣度提升
    let nodeBloom = nodeLayout.children[0];
    nodeBloom.getChildByName("lbl_old_value").getComponent(Label).string = `${Formate.format(
      this.argsCityBuildRes.oldBloom
    )}`;
    nodeBloom.getChildByName("lbl_new_value").getComponent(Label).string = `${Formate.format(
      CityModule.data.getCityBloom(this.argsCityBuildRes.cityMsg.cityId)
    )}`;

    // 人口上限提升
    let nodePeopleMax = nodeLayout.children[1];
    nodePeopleMax.getChildByName("lbl_old_value").getComponent(Label).string = `${Formate.format(
      configBuildLvBefore?.workerMax || 0
    )}`;
    nodePeopleMax.getChildByName("lbl_new_value").getComponent(Label).string = `${Formate.format(
      configBuildLv?.workerMax || 0
    )}`;
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
    if (this.argsCityBuildRes.rewardList?.length) {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: this.argsCityBuildRes.rewardList });
    }
  }
}
