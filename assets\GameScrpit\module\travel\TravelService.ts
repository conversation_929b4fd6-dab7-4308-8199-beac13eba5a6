import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import { TravelMsgEnum } from "./TravelConfig";
import { TravelModule } from "./TravelModule";

export class TravelService {
  public init() {
    MsgMgr.on(TravelMsgEnum.TRAVEL_TOTAL_UPDATE, this.updateBadge, this);
    MsgMgr.on(TravelMsgEnum.TRAVEL_VITALITY_UPDATE, this.updateBadge, this);

    this.updateBadge();
  }

  public updateBadge() {
    const rs = TravelModule.data.vitality >= TravelModule.data.vitalitySize;

    BadgeMgr.instance.setShowById(BadgeType.btn_tiao_zhan.btn_youli.btn_travel.id, rs);
  }
}
