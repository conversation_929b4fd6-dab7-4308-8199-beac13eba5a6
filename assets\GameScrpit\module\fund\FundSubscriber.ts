import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { AchievePayResponse, AchieveTargetValResponse } from "../../game/net/protocol/Activity";
import { CommLongListMessage } from "../../game/net/protocol/Comm";
import MsgMgr from "../../lib/event/MsgMgr";
import { FundModule } from "./FundModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class FundSubscriber {
  private fundMessageCallback(rs: AchievePayResponse) {
    log.log("基金购买成功后进行推送", rs);
    let list = [10404, 10403, 10402, 10405, 10401];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }
    /**修改用户支付状态 */
    let data = FundModule.data.getFundData(rs.achieveId);
    if (data) {
      data.paid = true;
    }
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_BUT_UP, rs);
  }
  private fundTargetUpCallback(rs: AchieveTargetValResponse) {
    log.log("当成就关注的指标发生变化时进行推送", rs);
    let list = [10404, 10403, 10402, 10405, 10401];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }
    /** achieveId*/
    let data = FundModule.data.getFundData(rs.achieveId);
    if (data) {
      data.targetVal = rs.targetVal;
    }
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_BUT_UP, rs);
  }

  private upActivityDb(rs: CommLongListMessage) {
    let callback = (id) => {
      for (let i = 0; i < rs.longList.length; i++) {
        if (rs.longList.includes(id)) {
          FundModule.data.upVO(id);
          return;
        }
      }
    };

    let list = [10404, 10403, 10402, 10405, 10401];

    for (let i = 0; i < list.length; i++) {
      callback(list[i]);
    }
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(AchievePayResponse, ActivityCmd.fundMessage, this.fundMessageCallback);

    ApiHandler.instance.subscribe(AchieveTargetValResponse, ActivityCmd.FundTargetUp, this.fundTargetUpCallback);
    ApiHandler.instance.subscribe(CommLongListMessage, ActivityCmd.ActivityUp, this.upActivityDb);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ActivityCmd.fundMessage, this.fundMessageCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.FundTargetUp, this.fundTargetUpCallback);
  }
}
