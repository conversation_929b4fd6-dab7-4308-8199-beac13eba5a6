import { _decorator, Label, Input, Sprite, sp, Node, EventTouch, Animation, v3, isValid } from "cc";
import ResMgr from "../../../lib/common/ResMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import Formate from "../../../lib/utils/Formate";
import { LuckDrawAudioName, LuckDrawMsgEnum } from "../../../module/luck_draw/LuckDrawConfig";
import { LuckDrawModule } from "../../../module/luck_draw/LuckDrawModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerRouteName, ShopRouteName } from "../../../module/player/PlayerConstant";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import MsgEnum from "../../event/MsgEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { LuckDrawResponse } from "../../net/protocol/LuckDraw";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { ShenjiEnum } from "../../../lib/common/ShenjiEnum";
import { IConfigLuckDraw } from "../../JsonDefine";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { GuideRouteEnum } from "../../../ext_guide/GuideDefine";
import { Sleep } from "../../GameDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";

const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const colorBg = ["", "XYDB_quan1", "XYDB_quan2", "XYDB_quan3", "XYDB_quan4", "XYDB_quan5"];
const blueColor = "#00af04";
const redColor = "#FF0000";

@ccclass("UILottery")
export class UILottery extends UINode {
  protected _openAct: boolean = true; // 打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_LOTTERY}?prefab/ui/UILottery`;
  }

  private _luck_db: IConfigLuckDraw;
  private _isAnimation: boolean = false;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.setTopItemNum, this);
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upItem, this);
    MsgMgr.on(LuckDrawMsgEnum.UPDATE_LUCK_DRAW_DATA, this.LuckyDrawUpdate, this);
    MsgMgr.on(LuckDrawMsgEnum.UPDATE_LUCK_DRAW_RECORD, this.load_recordList, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.setTopItemNum, this);
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upItem, this);
    MsgMgr.off(LuckDrawMsgEnum.UPDATE_LUCK_DRAW_DATA, this.LuckyDrawUpdate, this);
    MsgMgr.off(LuckDrawMsgEnum.UPDATE_LUCK_DRAW_RECORD, this.load_recordList, this);
  }

  /** 抽奖后更新幸运值，剩余道具数据 */
  private LuckyDrawUpdate() {
    this.setOneItemNeed();
    this.setTenItemNeed();
    this.set_luck_bar();
  }

  protected onEvtShow() {
    this.getData();

    LuckDrawModule.api.getDraw(this.luckDataInit.bind(this));

    /**无需请求数据的初始化 */
    this.setAwardList();
    this.setTopItemNum();

    this.upItem();

    this.setAnimationShow();

    if (LuckDrawModule.data.message.train.guideId == 0) {
      TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopEventRoute, { from: 100, to: 104 });
    }
  }

  /**获取基础数据 */
  private getData() {
    const c_luckDraw = JsonMgr.instance.jsonList.c_luckDraw;
    const list = Object.keys(c_luckDraw);
    this._luck_db = c_luckDraw[list[0]];
  }

  /**请求到数据后的初始化 */
  private luckDataInit() {
    this.set_luck_bar();
    this.load_recordList();
  }

  /**设置进度文本，和进度坐标 */
  private set_luck_bar() {
    const trainCount = LuckDrawModule.data.trainCount;
    this.getNode("lbl_luding_bar").getComponent(Label).string = `${trainCount}/${this._luck_db.maxTime}`;

    const start_pos = -95;
    const end_pos = 10;
    const inTotal = end_pos - start_pos;
    let bar = trainCount / this._luck_db.maxTime;
    let posY = start_pos + inTotal * bar;
    this.getNode("skt_luding").setPosition(v3(0, posY));
  }

  /**抽中大奖的记录人展示 */
  private async load_recordList() {
    const recordList = LuckDrawModule.data.message.recordList;

    if (this.getNode("content_record").children.length > recordList.length) {
      let num = this.getNode("content_record").children.length - recordList.length;
      for (let i = 0; i < num; i++) {
        this.getNode("content_record").children[0].destroy();
      }
    }

    for (let i = 0; i < recordList.length; i++) {
      await Sleep(0.02);
      if (isValid(this.node) == false) {
        return;
      }
      let node = this.getNode("content_record").children[i];
      if (!node) {
        node = ToolExt.clone(this.getNode("record"), this);
        this.getNode("content_record").addChild(node);
      }
      node.active = true;
      node.getChildByName("pl_name").getComponent(Label).string = recordList[i].name;
    }
  }

  /**设置自己的仙玉数量 */
  private setTopItemNum() {
    const num = PlayerModule.data.getItemNum(ItemEnum.仙玉_6);
    this.getNode("TopItemNum").getComponent(Label).string = Formate.format(num);
    ToolExt.setItemIcon(this.getNode("my_item_icon"), ItemEnum.仙玉_6, this);
  }

  private upItem() {
    this.setOneItemNeed();
    this.setTenItemNeed();
  }

  private setOneItemNeed() {
    const myItem = PlayerModule.data.getItemNum(this._luck_db.cardId);
    const color = myItem < 1 ? redColor : blueColor;
    ToolExt.setLabColor(this.getNode("lbl_oneItem_need").getComponent(Label), color);
    this.getNode("lbl_oneItem_need").getComponent(Label).string = `${myItem}/1`;
    //ToolExt.setItemIcon(this.getNode("my_item_icon"), this._luck_db.cardId, this);
  }

  private setTenItemNeed() {
    const myItem = PlayerModule.data.getItemNum(this._luck_db.cost10[0]);
    const color = myItem < this._luck_db.cost10[1] ? redColor : blueColor;
    ToolExt.setLabColor(this.getNode("lbl_ten_item_need").getComponent(Label), color);
    this.getNode("lbl_ten_item_need").getComponent(Label).string = `${myItem}/${this._luck_db.cost10[1]}`;
    //ToolExt.setItemIcon(this.getNode("my_item_icon"), this._luck_db.cost10[0], this);
  }

  private setAwardList() {
    const c_luckDraw = JsonMgr.instance.jsonList.c_luckDraw;
    const list = Object.keys(c_luckDraw);
    const arr = list.map((key) => c_luckDraw[key]);
    arr.forEach(async (luckInfo, index) => {
      const itemInfo = JsonMgr.instance.getConfigItem(luckInfo.reward1List[0]);
      const item = this.getNode("itemLayer").children[index];
      item["itemId"] = itemInfo.id;
      let click_item = item.getChildByName("click_item");
      FmUtils.setItemNode(click_item, itemInfo.id, 0);
      click_item.on(
        Input.EventType.TOUCH_START,
        () => {
          AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.点击道具图标);
        },
        this
      );

      //this.msgItemEvent(item);
      this.setItemShow(item);

      await Sleep(Math.random());
      if (isValid(this.node) == false) {
        return;
      }
      const animation = item.getComponent(Animation);
      animation.play("lottery_item_ani");
    });
  }

  private msgItemEvent(node: Node) {
    node.on(
      Input.EventType.TOUCH_END,
      (event: EventTouch) => {
        const itemId = event.target.itemId;
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
          itemId: itemId,
        });
      },
      this
    );
  }

  private setItemShow(node: Node) {
    const itemId = node["itemId"];
    const itemInfo = JsonMgr.instance.getConfigItem(itemId);
    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_G_LOTTERY}?image/${colorBg[itemInfo.color]}`,
      node.getChildByPath("ani_layer").getComponent(Sprite),
      this
    );
    ToolExt.setItemIcon(node.getChildByPath("ani_layer/icon"), itemInfo.id, this);
    node.getChildByPath("ani_layer/Label").getComponent(Label).string = "x1";
  }

  private on_click_btn_addXianyu() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(ShopRouteName.UIShopCenter, { tabIdx: 6 });
  }

  private on_click_btn_one() {
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.点击抽一次);
    const myItem = PlayerModule.data.getItemNum(this._luck_db.cardId);
    if (myItem < 1) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: this._luck_db.cardId,
        needNum: 1,
      });
      // 弹窗礼包事件
      MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, this._luck_db.cardId);
      return;
    }
    LuckDrawModule.api.oneDraw((data: LuckDrawResponse) => {
      this.openAward(data);
      for (let i = 0; i < data.trigger; i++) {
        MsgMgr.emit(MsgEnum.ON_TRIGGER_SHENJI, ShenjiEnum.宝鼎神迹_103);
      }
    });
  }

  private on_click_btn_ten() {
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.点击抽十次);
    const myItem = PlayerModule.data.getItemNum(this._luck_db.cost10[0]);
    if (myItem < this._luck_db.cost10[1]) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: this._luck_db.cost10[0],
        needNum: this._luck_db.cost10[1],
      });
      // 弹窗礼包事件
      MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, this._luck_db.cost10[0]);
      return;
    }
    LuckDrawModule.api.tenDraw((data: LuckDrawResponse) => {
      this.openAward(data);
      for (let i = 0; i < data.trigger; i++) {
        MsgMgr.emit(MsgEnum.ON_TRIGGER_SHENJI, ShenjiEnum.宝鼎神迹_103);
      }
    });
  }

  private openAward(data: LuckDrawResponse) {
    if (this._isAnimation) {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.resAddList });
      return;
    }
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.幸运夺宝动画);
    this.getNode("choukajiemian").active = true;
    let skt = this.getNode("choukajiemian").getComponent(sp.Skeleton);
    skt.setAnimation(0, "chou", false);
    skt.setEventListener((trackEntry: sp.spine.TrackEntry, event) => {
      if (event["data"].name === "tanchuang") {
        skt.setEventListener(null);
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.resAddList });
      }
    });
    skt.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
      if (trackEntry.animation.name === "chou") {
        this.getNode("choukajiemian").active = false;
        skt.setCompleteListener(null);
      }
    });
  }

  private on_click_btn_animation() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._isAnimation = !this._isAnimation;
    this.setAnimationShow();
  }

  private setAnimationShow() {
    this.getNode("icon_duigou").active = this._isAnimation;
  }

  private on_click_btn_probability() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog("UILotteryProb");
  }
  private on_click_btn_luckyShop() {
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.点击兑换商店);
    GoodsModule.api.buyInfo((data) => {
      UIMgr.instance.showDialog("UILotteryShop");
    });
  }
  private on_click_btn_luckyBar() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog("UILotteryHint", { keepMask: true });
  }
  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  // private _tickMgrIdList: number[] = [];
  // // 缓存节点和组件
  // private _topItemNumLabel: Label;
  // private _oneItemNeedLabel: Label;
  // private _tenItemNeedLabel: Label;
  // private _luckyBarLab: Label;
  // private _bgXingyuncaoSprite: Sprite;
  // private _choukajiemianNode: Node;
  // private _choukajiemianSkeleton: sp.Skeleton;
  // private _recordContentNode: Node;
  // private _sktQiPaoNode: Node;
  // private _sktIconNode: Node;
  // private _sktLabNode: Node;
  // protected onRegEvent(): void {
  //   MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.setTopItemNum, this);
  //   MsgMgr.on(LuckDrawMsgEnum.UPDATE_LUCK_DRAW_TRAIN_COUNT, this.LuckyDrawUpdate, this);
  // }
  // protected onDelEvent(): void {
  //   MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.setTopItemNum, this);
  //   MsgMgr.off(LuckDrawMsgEnum.UPDATE_LUCK_DRAW_TRAIN_COUNT, this.LuckyDrawUpdate, this);
  // }
  // protected onEvtShow() {
  //   GoodsModule.api.buyInfo(() => {
  //     LuckDrawModule.api.getDraw(this.initMain.bind(this));
  //     this.initIcon();
  //     this.setTopItemNum();
  //     this.setAwardList();
  //     this.setOneItemNeed();
  //     this.setTenItemNeed();
  //     this.setLuckyBarLab();
  //     this.setAnimationShow();
  //   });
  //   // 缓存节点和组件
  //   this._topItemNumLabel = this.getNode("TopItemNum").getComponent(Label);
  //   this._oneItemNeedLabel = find("resLay/num", this.getNode("btn_one")).getComponent(Label);
  //   this._tenItemNeedLabel = find("resLay/num", this.getNode("btn_ten")).getComponent(Label);
  //   this._luckyBarLab = this.getNode("luckyBarLab").getComponent(Label);
  //   this._bgXingyuncaoSprite = this.getNode("bg_xingyuncao").getComponent(Sprite);
  //   this._choukajiemianNode = this.getNode("choukajiemian");
  //   this._choukajiemianSkeleton = this._choukajiemianNode.getComponent(sp.Skeleton);
  //   this._recordContentNode = this.getNode("content_record");
  //   this._sktQiPaoNode = this.getNode("skt_qi_pao");
  //   this._sktIconNode = this.getNode("skt_icon");
  //   this._sktLabNode = this.getNode("skt_lab");
  // }
  // private getData() {
  //   const c_luckDraw = JsonMgr.instance.jsonList.c_luckDraw;
  //   const list = Object.keys(c_luckDraw);
  //   const data = c_luckDraw[list[0]];
  //   return data;
  // }
  // private initIcon() {
  //   const data = this.getData();
  //   const iconTen = find("resLay/icon", this.getNode("btn_ten"));
  //   ToolExt.setItemIcon(iconTen, data.cost10[0]);
  //   const iconOne = find("resLay/icon", this.getNode("btn_one"));
  //   ToolExt.setItemIcon(iconOne, data.cardId);
  //   const iconXianyu = find("icon", this.getNode("itemLayer"));
  //   ToolExt.setItemIcon(iconXianyu, 6);
  // }
  // private setAnimationShow() {
  //   this.getNode("icon_duigou").active = this._isAnimation;
  // }
  // private initMain(data: LuckTrainResponse) {
  //   //log.log("幸运夺宝数据====", data);
  //   const recordList = data.recordList;
  //   recordList.forEach((record, index) => {
  //     const id = TickerMgr.setTimeout(dtTime * index, () => {
  //       const node = ToolExt.clone(this.getNode("record"), this);
  //       this._recordContentNode.addChild(node);
  //       node.active = true;
  //       node.getChildByName("pl_name").getComponent(Label).string = record.name;
  //     });
  //     this._tickMgrIdList.push(id);
  //   });
  // }
  // private setTopItemNum() {
  //   const num = PlayerModule.data.getItemNum(ItemEnum.仙玉_6);
  //   this._topItemNumLabel.string = Formate.format(num);
  // }
  // private setAwardList() {
  //   const c_luckDraw = JsonMgr.instance.jsonList.c_luckDraw;
  //   const list = Object.keys(c_luckDraw);
  //   const arr = list.map((key) => c_luckDraw[key]);
  //   arr.forEach((luckInfo, index) => {
  //     const itemInfo = JsonMgr.instance.getConfigItem(luckInfo.reward1List[0]);
  //     const nodeQiPao = this._sktQiPaoNode.children[index];
  //     nodeQiPao["itemId"] = itemInfo.id;
  //     nodeQiPao.on(Input.EventType.TOUCH_END, this.click_item, this);
  //     ResMgr.loadSpriteFrame(
  //       `${BundleEnum.BUNDLE_G_LOTTERY}?patterns/UILottery`,
  //       colorBg[itemInfo.color],
  //       nodeQiPao.getComponent(Sprite),
  //       this
  //     );
  //     const nodeIcon = this._sktIconNode.children[index];
  //     ToolExt.setItemIcon(nodeIcon.getChildByName("icon"), itemInfo.id, this);
  //     const nodeLab = this._sktLabNode.children[index];
  //     nodeLab.getChildByName("Label").getComponent(Label).string = "X1";
  //   });
  //   this._sktQiPaoNode.getComponent(sp.Skeleton).setAnimation(0, "animation", true);
  //   this._sktIconNode.getComponent(sp.Skeleton).setAnimation(0, "animation", true);
  //   this._sktLabNode.getComponent(sp.Skeleton).setAnimation(0, "animation", true);
  // }
  // private click_item(event) {
  //   const itemId = event.target.itemId;
  //   UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
  //     itemId: itemId,
  //   });
  // }
  // /** 抽奖后更新幸运值，剩余道具数据 */
  // private LuckyDrawUpdate() {
  //   this.setOneItemNeed();
  //   this.setTenItemNeed();
  //   this.setLuckyBarLab();
  // }
  // private setOneItemNeed() {
  //   const data = this.getData();
  //   const myItem = PlayerModule.data.getItemNum(data.cardId);
  //   const color = myItem < 1 ? redColor : blueColor;
  //   ToolExt.setLabColor(this._oneItemNeedLabel, color);
  //   this._oneItemNeedLabel.string = `${myItem}/1`;
  // }
  // private setTenItemNeed() {
  //   const data = this.getData();
  //   const myItem = PlayerModule.data.getItemNum(data.cost10[0]);
  //   const color = myItem < data.cost10[1] ? redColor : blueColor;
  //   ToolExt.setLabColor(this._tenItemNeedLabel, color);
  //   this._tenItemNeedLabel.string = `${myItem}/${data.cost10[1]}`;
  // }
  // private setLuckyBarLab() {
  //   const data = this.getData();
  //   const trainCount = LuckDrawModule.data.trainCount;
  //   this._luckyBarLab.string = `幸运槽 : ${trainCount}/${data.maxTime}`;
  //   this._bgXingyuncaoSprite.fillRange = trainCount / data.maxTime;
  // }
  // private on_click_btn_one() {
  //   const data = this.getData();
  //   const myItem = PlayerModule.data.getItemNum(data.cardId);
  //   if (myItem < 1) {
  //     UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
  //       itemId: data.cardId,
  //       needNum: 1,
  //     });
  //     // 弹窗礼包事件
  //     MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, data.cardId);
  //     return;
  //   }
  //   LuckDrawModule.api.oneDraw((data: LuckDrawResponse) => {
  //     this.openAward(data);
  //     for (let i = 0; i < data.trigger; i++) {
  //       MsgMgr.emit(MsgEnum.ON_TRIGGER_SHENJI, ShenjiEnum.宝鼎神迹_103);
  //     }
  //   });
  // }
  // private on_click_btn_ten() {
  //   const data = this.getData();
  //   const myItem = PlayerModule.data.getItemNum(data.cost10[0]);
  //   if (myItem < data.cost10[1]) {
  //     UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
  //       itemId: data.cost10[0],
  //       needNum: data.cost10[1],
  //     });
  //     // 弹窗礼包事件
  //     MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, data.cost10[0]);
  //     return;
  //   }
  //   LuckDrawModule.api.tenDraw((data: LuckDrawResponse) => {
  //     this.openAward(data);
  //     for (let i = 0; i < data.trigger; i++) {
  //       MsgMgr.emit(MsgEnum.ON_TRIGGER_SHENJI, ShenjiEnum.宝鼎神迹_103);
  //     }
  //   });
  // }
  // private on_click_btn_animation() {
  //   this._isAnimation = !this._isAnimation;
  //   this.setAnimationShow();
  // }
  // private openAward(data: LuckDrawResponse) {
  //   if (this._isAnimation) {
  //     MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.resAddList });
  //     return;
  //   }
  //   this._choukajiemianNode.active = true;
  //   this._choukajiemianSkeleton.setAnimation(0, "chou", false);
  //   this._choukajiemianSkeleton.setEventListener((trackEntry: sp.spine.TrackEntry, event) => {
  //     if (event["data"].name === "tanchuang") {
  //       this._choukajiemianSkeleton.setEventListener(null);
  //       MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.resAddList });
  //     }
  //   });
  //   this._choukajiemianSkeleton.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
  //     if (trackEntry.animation.name === "chou") {
  //       this._choukajiemianNode.active = false;
  //       this._choukajiemianSkeleton.setCompleteListener(null);
  //     }
  //   });
  // }

  // protected onEvtClose(): void {
  //   this._tickMgrIdList.forEach((id) => TickerMgr.clearTimeout(id));
  // }
}
