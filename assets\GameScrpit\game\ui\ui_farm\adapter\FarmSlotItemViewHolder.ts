import { _decorator, Label, math, Node } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { FarmDispatchMessage } from "../../../net/protocol/Farm";
import { FarmModule } from "../../../../module/farm/FarmModule";
import { FarmSlotUITool } from "../../../../module/farm/FarmSlotUITool";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { FarmRouteName } from "../../../../module/farm/FarmRoute";
import FmUtils from "../../../../lib/utils/FmUtils";
import { LangMgr } from "../../../mgr/LangMgr";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { PlayerBaseMessage } from "../../../net/protocol/Player";
import { PublicRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import { ConfirmMsg } from "../../UICostConfirm";

const { ccclass, property } = _decorator;
@ccclass("FarmSlotItemViewHolder")
export class FarmSlotItemViewHolder extends ViewHolder {
  private nodeMy: Node;
  private nodeOther: Node;
  private nodeHulu: Node;

  private _dispatchMsg: FarmDispatchMessage;
  private identifyId: number;

  public init() {}

  start() {
    super.start();
  }

  public updateData(args: any) {
    this.nodeMy = this.getNode("node_my");
    this.nodeOther = this.getNode("node_other");
    this.nodeHulu = this.getNode("node_hulu");

    let data: FarmDispatchMessage = args.dispatch;
    this._dispatchMsg = data;
    this.identifyId = args.key;

    // 设置葫芦等级
    FarmSlotUITool.updateHulu(this.nodeHulu.getChildByName("hulu"), data.gourdId, false, true);

    // 设置场地信息
    this.getNode("lbl_my_farm").active = data.farmerMessage?.userId == PlayerModule.data.playerId;
    const lbl_other_farm = this.getNode("lbl_other_farm");
    lbl_other_farm.active = !this.getNode("lbl_my_farm").active;
    if (lbl_other_farm.active) {
      lbl_other_farm.getComponent(Label).string = LangMgr.txMsgCode(479, [data.farmerMessage?.nickname]);
    }

    // 是否我方胜利
    let isWin = data.win;

    // 我方信息
    let ownBeeCnt = 0;
    let otherBeeCnt = 0;
    let ownMsg: PlayerBaseMessage;
    let otherMsg: PlayerBaseMessage;
    if (data.farmerMessage?.userId == PlayerModule.data.playerId) {
      ownBeeCnt = data.ownBeeCnt || 0;
      otherBeeCnt = data.otherBeeCnt || 0;
      ownMsg = data.farmerMessage;
      otherMsg = data.otherMessage;
    } else {
      ownBeeCnt = data.ownBeeCnt || 0;
      otherBeeCnt = data.otherBeeCnt || 0;
      ownMsg = data.otherMessage;
      otherMsg = data.farmerMessage;
    }

    // 头像初始化
    this.nodeMy.getChildByName("img_houtou_0").active = false;
    this.nodeMy.getChildByName("img_houtou_1").active = false;
    this.nodeMy.getChildByName("node_count").active = false;
    this.nodeOther.getChildByName("img_houtou_0").active = false;
    this.nodeOther.getChildByName("img_houtou_1").active = false;
    this.nodeOther.getChildByName("node_count").active = false;

    // 我方头像
    if (ownBeeCnt > 0) {
      this.nodeMy.getChildByName("img_houtou_1").active = true;
      // 我方派遣数量
      const node_count = this.nodeMy.getChildByName("node_count");
      node_count.active = true;
      node_count.getChildByName("lbl_count").getComponent(Label).string = ownBeeCnt.toString();
    } else {
      this.nodeMy.getChildByName("img_houtou_0").active = true;
    }

    // 敌方形象
    if (otherBeeCnt > 0) {
      this.nodeOther.getChildByName("img_houtou_1").active = true;
      // 敌方派遣数量
      const node_count = this.nodeOther.getChildByName("node_count");
      node_count.active = true;
      node_count.getChildByName("lbl_count").getComponent(Label).string = ownBeeCnt.toString();
    } else {
      this.nodeOther.getChildByName("img_houtou_0").active = true;
    }

    // 倒计时
    const lbl_cd_my = this.getNode("lbl_cd_my");
    lbl_cd_my.active = isWin;
    const lbl_cd_other = this.getNode("lbl_cd_other");
    lbl_cd_other.active = !isWin;

    if (ownBeeCnt) {
      if (lbl_cd_my.active) {
        FmUtils.setCd(lbl_cd_my, data.endTime);
        this.getNode("lbl_collecting").getComponent(Label).color = math.color("#00af04");
      } else {
        FmUtils.setCd(lbl_cd_other, data.endTime);
        this.getNode("lbl_collecting").getComponent(Label).color = math.color("#E10000");
      }
    } else {
      this.getNode("lbl_collecting").active = false;
    }

    // 敌方名称
    const lblName = this.nodeOther.getChildByName("lbl_name").getComponent(Label);
    if (otherBeeCnt > 0) {
      lblName.string = otherMsg.nickname;
    } else {
      lblName.string = LangMgr.txMsgCode(459); // "无人采集";
    }

    // 采集标志
    this.getNode("bg_working_my").active = false;
    this.getNode("bg_working_other").active = false;
    this.getNode("spine_get_my").active = false;
    this.getNode("spine_get_other").active = false;
    if (isWin) {
      this.getNode("spine_get_my").active = true;
      if (otherBeeCnt) {
        this.getNode("bg_working_my").active = true;
      }
    } else {
      this.getNode("spine_get_other").active = true;
      if (ownBeeCnt) {
        this.getNode("bg_working_other").active = true;
      }
    }
  }

  public on_click_btn_callback() {
    let msg: ConfirmMsg = {
      msg: LangMgr.txMsgCode(999999, [], "是否召回该猴子？"),
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp.ok) {
        FarmModule.api.recall(this.identifyId);
      }
    });
  }

  public on_click_btn_go() {
    FarmModule.api.getOtherFarm(this._dispatchMsg.farmerMessage.userId, (resp) => {
      UIMgr.instance.showDialog(FarmRouteName.UIFarmMainOther, { farm: resp });
    });
  }
}
