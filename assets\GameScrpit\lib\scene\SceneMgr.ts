import { _decorator, Component, Node } from "cc";
import { StartUp } from "../StartUp";
import Scene from "./scene";
const { ccclass, property } = _decorator;

@ccclass("SceneMgr")
export class SceneMgr extends Component {
  private static _instance: SceneMgr = null;
  public static get instance() {
    if (SceneMgr._instance == null) {
      let node = new Node();
      node.name = "SceneMgr";
      StartUp.instance.uiRoot.addChild(node);
      SceneMgr._instance = node.addComponent(SceneMgr);
      SceneMgr._instance.init();
    }
    return SceneMgr._instance;
  }

  private _current: Scene = null;

  private init() {}

  public replaceScene<T extends Scene>(type: { new (): T }, args?: any) {
    let scene = new type();

    if (this._current != null) {
      this._current.onExit();
    }

    this._current = scene;
    this._current.onEnter(args);
  }

  public getCurrentScene<T extends Scene>() {
    return this._current as T;
  }

  update(dt) {
    if (this._current == null) {
      return;
    }
    this._current.update(dt);
  }
}
