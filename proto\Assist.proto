syntax = "proto3";
package sim;
import "Player.proto";

// 
message AssistMessage {
  // 协助ID
  int64 id = 1;
  // 被协助人员的人员信息
  sim.PlayerBaseMessage playerBaseMessage = 2;
  // 协助类型 1-路障 2-怪物 3-npc
  int32 assistType = 3;
  // 参与协助人员列表
  repeated sim.PlayerBaseMessage assistMembers = 4;
  // 协助进度
  double progress = 5;
  // 协助初始值或最大值
  double maxProgress = 6;
  // 过期时间
  int64 deadline = 7;
  // 描述:assistType = NPC,{"enemyId"："对手ID"，"enemyName":"对手名称"}
  map<string,string> descMap = 8;
}

// 
message AssistResponse {
  // 0-协助成功 1-被协助模块数据不存在(被协助的成功) 2-已经协助过，数据不一致 3-被协助的模块功能暂时关闭，则前端需要重新拉取协助列表
  int32 code = 1;
  // 当code=2时，有值前端需要更新
  AssistMessage assistMessage = 2;
  // 进度
  double progress = 3;
  // 参与协助人员列表
  repeated sim.PlayerBaseMessage assistMembers = 4;
  // 帮助详情 assistType=MONSTER,NPC时，{"win":1或0,"replay":"战斗录像"}
  map<string,string> detailMap = 5;
}

