import { _decorator, instantiate, Label, Node } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { BargainRecordMessage } from "../../../net/protocol/Club";
import { PlayerSimpleMessage } from "../../../net/protocol/Player";
import { TimeUtils } from "db://assets/GameScrpit/lib/utils/TimeUtils";

const { ccclass, property } = _decorator;
@ccclass("ClubUnBargainViewHolder")
export class ClubUnBargainViewHolder extends ViewHolder {
  @property(Label)
  private lblPlayerName: Label;
  @property(Label)
  private lblLastLoginTime: Label;
  public init() {}
  public updateData(position: number, data: PlayerSimpleMessage) {
    this.lblPlayerName.string = `${position + 1}.${data.nickname}`;
    let dateStr = TimeUtils.getTimeAgo(data.lastLoginTime);
    this.lblLastLoginTime.string = `${dateStr}`;
  }
}
export class ClubUnBargainAdapter extends ListAdapter {
  private item: Node;
  private data: any[];
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }

  getViewType(position: number): number {
    return 0;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(ClubUnBargainViewHolder).init();
    return item;
  }
  onBindData(view: Node, position: number): void {
    view.getComponent(ClubUnBargainViewHolder).updateData(position, this.data[position]);
  }
  getCount(): number {
    return this.data.length;
  }
}
