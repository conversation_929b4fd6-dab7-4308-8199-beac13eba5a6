import { UIClubBargain } from "../../game/ui/club/UIClubBargain";
import { UIClubBossAward } from "../../game/ui/club/UIClubBossAward";
import { UIClubBossMain } from "../../game/ui/club/UIClubBossMain";
import { UIClubCreate } from "../../game/ui/club/UIClubCreate";
import { UIClubEditAvatar } from "../../game/ui/club/UIClubEditAvatar";
import { UIClubFight } from "../../game/ui/club/UIClubFight";
import { UIClubFightFinish } from "../../game/ui/club/UIClubFightFinish";
import { UIClubFightWin } from "../../game/ui/club/UIClubFightWin";
import { UIClubList } from "../../game/ui/club/UIClubList";
import { UIClubMain } from "../../game/ui/club/UIClubMain";
import { UIClubNameFix } from "../../game/ui/club/UIClubNameFix";
import { UIClubRank } from "../../game/ui/club/UIClubRank";
import { UIClubSetting } from "../../game/ui/club/UIClubSetting";
import { UIClubShop } from "../../game/ui/club/UIClubShop";
import { UIClubTask } from "../../game/ui/club/UIClubTask";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum ClubRouteItem {
  UIClubMain = "UIClubMain",
  UIClubList = "UIClubList",
  UIClubCreate = "UIClubCreate",
  UIClubSetting = "UIClubSetting",
  UIClubTask = "UIClubTask",
  UIClubShop = "UIClubShop",
  UIClubRank = "UIClubRank",
  UIClubBossMain = "UIClubBossMain",
  UIClubBossAward = "UIClubBossAward",
  UIClubEditAvatar = "UIClubEditAvatar",
  UIClubNameFix = "UIClubNameFix",
  UIClubFight = "UIClubFight",
  UIClubFightWin = "UIClubFightWin",
  UIClubFightFinish = "UIClubFightFinish",
  UIClubBargain = "UIClubBargain",
}

export class ClubRoute {
  rotueTables: Recording[] = [
    { node: UIClubMain, uiName: ClubRouteItem.UIClubMain, keep: true, relevanceUIList: [], music: 503 },
    { node: UIClubList, uiName: ClubRouteItem.UIClubList, keep: false, relevanceUIList: [] },
    { node: UIClubCreate, uiName: ClubRouteItem.UIClubCreate, keep: false, relevanceUIList: [] },
    { node: UIClubSetting, uiName: ClubRouteItem.UIClubSetting, keep: false, relevanceUIList: [] },
    { node: UIClubTask, uiName: ClubRouteItem.UIClubTask, keep: false, relevanceUIList: [] },
    { node: UIClubShop, uiName: ClubRouteItem.UIClubShop, keep: false, relevanceUIList: [] },
    { node: UIClubRank, uiName: ClubRouteItem.UIClubRank, keep: false, relevanceUIList: [] },
    { node: UIClubBossMain, uiName: ClubRouteItem.UIClubBossMain, keep: false, relevanceUIList: [] },
    { node: UIClubBossAward, uiName: ClubRouteItem.UIClubBossAward, keep: false, relevanceUIList: [] },
    { node: UIClubEditAvatar, uiName: ClubRouteItem.UIClubEditAvatar, keep: false, relevanceUIList: [] },
    { node: UIClubNameFix, uiName: ClubRouteItem.UIClubNameFix, keep: false, relevanceUIList: [] },
    {
      node: UIClubFight,
      uiName: ClubRouteItem.UIClubFight,
      keep: false,
      relevanceUIList: [],
      music: 1536,
    },
    { node: UIClubFightWin, uiName: ClubRouteItem.UIClubFightWin, keep: false, relevanceUIList: [] },
    { node: UIClubFightFinish, uiName: ClubRouteItem.UIClubFightFinish, keep: false, relevanceUIList: [] },
    { node: UIClubBargain, uiName: ClubRouteItem.UIClubBargain, keep: false, relevanceUIList: [] },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
