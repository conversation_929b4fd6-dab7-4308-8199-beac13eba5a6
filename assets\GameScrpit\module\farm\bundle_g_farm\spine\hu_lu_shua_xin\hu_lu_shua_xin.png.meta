{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "0aaadc7f-3de5-4797-b808-3c920b63facc", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "0aaadc7f-3de5-4797-b808-3c920b63facc@6c48a", "displayName": "hu_lu_shua_xin", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "0aaadc7f-3de5-4797-b808-3c920b63facc", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "0aaadc7f-3de5-4797-b808-3c920b63facc@f9941", "displayName": "hu_lu_shua_xin", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -29, "offsetY": 7, "trimX": 2, "trimY": 1, "width": 194, "height": 112, "rawWidth": 256, "rawHeight": 128, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-97, -56, 0, 97, -56, 0, -97, 56, 0, 97, 56, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [2, 127, 196, 127, 2, 15, 196, 15], "nuv": [0.0078125, 0.1171875, 0.765625, 0.1171875, 0.0078125, 0.9921875, 0.765625, 0.9921875], "minPos": [-97, -56, 0], "maxPos": [97, 56, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "0aaadc7f-3de5-4797-b808-3c920b63facc@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "0aaadc7f-3de5-4797-b808-3c920b63facc@6c48a"}}