import { JsonMgr } from "../../game/mgr/JsonMgr";
import { Node } from "cc";
import { BadgeMgr, BadgeType, IBadgeCreate } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { PlayerModule } from "../player/PlayerModule";
import { FriendModule } from "./FriendModule";
import { GameDirector } from "../../game/GameDirector";
import { HeroAttrEnum, SystemOpenEnum } from "../../game/GameDefine";
import { IConfigFriendPicture } from "../../game/JsonDefine";
import { addAttrMap } from "../../lib/utils/AttrTool";
import { times } from "../../lib/utils/NumbersUtils";
import MsgEnum from "../../game/event/MsgEnum";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
class FriendRed {
  name: "friend";
  skill: {
    name: "skill";
    child;
  };
}
export class FriendService {
  //
  public init() {
    MsgMgr.on(MsgEnum.ON_FRIEND_UPDATE, this.updatePopover, this);
    MsgMgr.on(MsgEnum.ON_FRIEND_CITY_SKILL_UPDATE, this.updatePopover, this);
    MsgMgr.on(MsgEnum.ON_FRIEND_HERO_SKILL_UPDATE, this.updatePopover, this);

    // 每3秒检查是否要更新精力
    TickerMgr.setInterval(
      3,
      () => {
        if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.FRIEND_仙友系统)) {
          return;
        }

        if (!FriendModule.data.vitalityMessage) {
          FriendModule.api.getVitality();
          return;
        }

        const cfg = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level);
        const chatMax = cfg.chatMax;
        const chatTime = cfg.chatTime;

        if (FriendModule.data.vitality < chatMax) {
          const tsNow = new Date().valueOf() / 1000;
          const nextUpdateTime = FriendModule.data.vitalityMessage.lastUpdateTime || tsNow;

          if (nextUpdateTime + chatTime < tsNow) {
            FriendModule.api.getVitality();
          }
        }
      },
      false
    );

    // 图鉴红点初始化
    // 获取配置文件
    let redNameList: IBadgeCreate[] = [];
    let configTujianMap = JsonMgr.instance.jsonList.c_friendPicture;
    Object.keys(configTujianMap).forEach((key) => {
      let config = configTujianMap[key];
      let id = config.id;
      redNameList.push({ name: "friendPicture" + id, children: [] });
    });

    BadgeMgr.instance.addBadgeItemExt(BadgeType.UITerritory.btn_friend.tab_tujian, redNameList);
  }
  public createBadgeList() {
    let redExtList: IBadgeCreate[] = [];

    let friends = FriendModule.data.getFriendIds(true);
    for (let index = 0; index < friends.length; index++) {
      let friendMessage = FriendModule.data.getFriendMessage(friends[index]);
      if (friendMessage) {
        let redExt = { name: `friend${friends[index]}`, children: [] };
        let redExtSkill = { name: "friendSkill", children: [] };
        redExt.children.push(redExtSkill);
        //
        let redExtSkillCity = { name: "friendCity", children: [] };
        for (let i = 0; i < friendMessage.citySkillList.length; i++) {
          redExtSkillCity.children.push({ name: "friendCitySkill" + i });
        }
        redExtSkill.children.push(redExtSkillCity);
        //
        let redExtSkillHero = { name: "friendHero", children: [] };
        redExtSkill.children.push(redExtSkillHero);
        //
        let redExtSkillHeroFix = { name: "friendHeroFix", children: [] };
        let redExtSkillHeroPercent = {
          name: "friendHeroPercent",
          children: [],
        };
        redExtSkillHero.children.push(redExtSkillHeroFix);
        redExtSkillHero.children.push(redExtSkillHeroPercent);
        //
        redExtList.push(redExt);
      }
    }
    BadgeMgr.instance.addBadgeItemExt(BadgeType.UITerritory.btn_friend.tab_friend, redExtList);
    log.log("仙友红点树", BadgeType.UITerritory.btn_friend.tab_friend);
  }

  public registerFriendListBadge(node: Node, friendId: number) {
    BadgeMgr.instance.setBadgeId(node, BadgeType.UITerritory.btn_friend?.tab_friend[`friend${friendId}`]?.id);
  }
  public registerFriendSkillBadge(node: Node, friendId: number) {
    BadgeMgr.instance.setBadgeId(
      node,
      BadgeType.UITerritory.btn_friend?.tab_friend[`friend${friendId}`][`friendSkill`]?.id
    );
  }
  public registerFriendSkillCityBadge(node: Node, friendId: number) {
    BadgeMgr.instance.setBadgeId(
      node,
      BadgeType.UITerritory.btn_friend.tab_friend[`friend${friendId}`][`friendSkill`][`friendCity`]?.id
    );
  }
  /**
   * 注册据点技能红点
   * @param node
   * @param friendId
   * @param index
   */
  public registerFriendSkillCitySkillBadge(node: Node, friendId: number, index: number) {
    BadgeMgr.instance.setBadgeId(
      node,
      BadgeType.UITerritory.btn_friend.tab_friend[`friend${friendId}`][`friendSkill`][`friendCity`][
        `friendCitySkill${index}`
      ]?.id
    );
  }
  public registerFriendSkillHeroBadge(node: Node, friendId: number) {
    BadgeMgr.instance.setBadgeId(
      node,
      BadgeType.UITerritory.btn_friend.tab_friend[`friend${friendId}`][`friendSkill`][`friendHero`]?.id
    );
  }
  public registerFriendSkillHeroFixBadge(node: Node, friendId: number) {
    BadgeMgr.instance.setBadgeId(
      node,
      BadgeType.UITerritory.btn_friend.tab_friend[`friend${friendId}`][`friendSkill`][`friendHero`][`friendHeroFix`]?.id
    );
  }
  public registerFriendSkillHeroPercentBadge(node: Node, friendId: number) {
    BadgeMgr.instance.setBadgeId(
      node,
      BadgeType.UITerritory.btn_friend.tab_friend[`friend${friendId}`][`friendSkill`][`friendHero`][`friendHeroPercent`]
        ?.id
    );
  }
  private showFriendCitySkillBadge(friendId: number, index: number, isShow: boolean) {
    // log.log("showFriendCitySkillBadge", BadgeType.UITerritory.btn_friend.tab_friend);
    BadgeMgr.instance.setShowById(
      BadgeType.UITerritory.btn_friend.tab_friend[`friend${friendId}`][`friendSkill`][`friendCity`][
        `friendCitySkill${index}`
      ]?.id,
      isShow
    );
  }
  private showFriendHeroFixBadge(friendId: number, isShow: boolean) {
    BadgeMgr.instance.setShowById(
      BadgeType.UITerritory.btn_friend.tab_friend[`friend${friendId}`][`friendSkill`][`friendHero`][`friendHeroFix`]
        ?.id,
      isShow
    );
  }
  private showFriendHeroPercentBadge(friendId: number, isShow: boolean) {
    BadgeMgr.instance.setShowById(
      BadgeType.UITerritory.btn_friend.tab_friend[`friend${friendId}`][`friendSkill`][`friendHero`][`friendHeroPercent`]
        ?.id,
      isShow
    );
  }

  private tujianBadge(data: IConfigFriendPicture): boolean {
    let collectNum = 0;
    for (let index = 0; index < data.friendId.length; index++) {
      let friendMessage = FriendModule.data.getFriendMessage(data.friendId[index]);
      if (friendMessage) {
        collectNum++;
      }
    }
    if (collectNum == data.friendId.length && !FriendModule.data.pictureMessage[data.id]) {
      log.log("有图鉴奖励未领取");
      return true;
    }
    return false;
  }
  private updatePopover() {
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.FRIEND_仙友系统)) {
      return;
    }

    const chatMax = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level).chatMax;
    const rs = FriendModule.data.vitality >= chatMax;
    // log.log("仙友红点 updatePopover", tujianBadge);

    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_friend.heart_talk.id, rs);
    // BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_friend.tab_tujian.id, tujianBadge);

    // 图鉴红点
    let configTujianMap = JsonMgr.instance.jsonList.c_friendPicture;
    Object.keys(configTujianMap).forEach((key) => {
      let config = configTujianMap[key];
      let id = config.id;
      let tujianBadge = this.tujianBadge(config);
      // log.log("图鉴红点", tujianBadge);
      // 是否显示 todo
      BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_friend.tab_tujian["friendPicture" + id].id, tujianBadge);
    });
    // 城市据点技能红点显示
    FriendModule.data.getFriendIds(true).forEach((friendId) => {
      let friendMessage = FriendModule.data.getFriendMessage(friendId);
      for (let i = 0; i < friendMessage?.citySkillList?.length; i++) {
        this.showFriendCitySkillBadge(friendId, i, times(friendMessage.citySkillList[i].skillAdd, 100) <= 1);
      }
      let friendHeroSkill1 = FriendModule.config.getFriendHeroSkill(friendMessage?.heroSkillList[0] + 1);
      let friendHeroSkill2 = FriendModule.config.getFriendHeroSkill(friendMessage.heroSkillList[1] + 1);
      let cost1 = friendHeroSkill1?.cost1 ?? 0;
      let cost2 = friendHeroSkill2?.cost2 ?? 0;
      this.showFriendHeroFixBadge(friendId, cost1 <= friendMessage.friendShip);
      this.showFriendHeroPercentBadge(friendId, cost2 <= friendMessage.friendShip);
    });
  }

  /**只要有一个目标未完成，就显示入口图标 */
  public canShowFriendMubiao(): boolean {
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.FRIEND_仙友系统)) {
      return false;
    }
    let friendMubiaos = FriendModule.config.obtainFriendTarget();
    for (let i = 0; i < friendMubiaos.length; i++) {
      let firendMessage = FriendModule.data.getFriendMessage(friendMubiaos[i].id);
      if (!firendMessage) {
        return true;
      }
    }
    return false;
  }
  /**
   * 获取单个挚友属性加成
   * @param friendId
   * @returns
   */
  public getHeroAttrAddByFriendId(friendId: number): Map<number, number> {
    let friend = FriendModule.data.getFriendMessage(friendId);
    let attr = new Map<number, number>();
    if (friend) {
      let fix = FriendModule.config.getFriendHeroSkill(friend.heroSkillList[0]);
      let percent = FriendModule.config.getFriendHeroSkill(friend.heroSkillList[1]);
      for (let i1 = 0; i1 < fix.value1.length; i1++) {
        attr[fix.value1[i1][0]] = fix.value1[i1][1];
      }
      attr[HeroAttrEnum.战将生命百分比_11] = percent.value2;
      attr[HeroAttrEnum.战将攻击百分比_12] = percent.value2;
      attr[HeroAttrEnum.战将防御百分比_13] = percent.value2;
    }
    return attr;
  }

  public getHeroAttrAdd(heroId: number): Map<number, number> {
    let fateList = FriendModule.data.getHeroFriends(heroId);
    let fixAttr = new Map<number, number>();
    for (let i = 0; i < fateList.length; i++) {
      let attrAdd = this.getHeroAttrAddByFriendId(fateList[i]);
      addAttrMap(fixAttr, attrAdd);
    }
    return fixAttr;
  }
  // 获取所有挚友属性加成
}
