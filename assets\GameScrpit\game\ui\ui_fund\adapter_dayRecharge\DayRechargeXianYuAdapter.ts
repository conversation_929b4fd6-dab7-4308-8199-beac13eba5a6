import { _decorator, Component, instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { DatRechargeXianYuViewHolder } from "./DatRechargeXianYuViewHolder";

const { ccclass, property } = _decorator;

@ccclass("DayRechargeXianYuAdapter")
export class DayRechargeXianYuAdapter extends ListAdapter {
  private data: any[] = [];

  private item: Node;
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }

  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(DatRechargeXianYuViewHolder).init();
    return item;
  }

  onBindData(view: Node, position: number): void {
    view.getComponent(DatRechargeXianYuViewHolder).updateData(this.data[position], position);
  }
  getCount(): number {
    return this.data.length;
  }
}
