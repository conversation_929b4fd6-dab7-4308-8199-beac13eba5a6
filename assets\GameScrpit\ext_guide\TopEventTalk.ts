import { _decorator, instantiate, Label, Node, RichText, sp, tween } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { IConfigGuideV2 } from "../game/JsonDefine";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { PlayerModule } from "../module/player/PlayerModule";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { LangMgr } from "../game/mgr/LangMgr";

const { ccclass, property } = _decorator;

const enum TalkEnum {
  INIT = 0,
  LOADING_SPINE = 1, // 加载中
  PLAY_TALK = 2, // 播放中
  COMPLETE = 3, // 播放完成
}

@ccclass("TopEventTalk")
export class TopEventTalk extends BaseCtrl {
  @property(Label)
  private lbl_name: Label;

  @property(RichText)
  private richtext_msg: RichText;

  @property(Label)
  private lbl_skip: Label;

  @property(sp.Skeleton)
  private skt: sp.Skeleton;

  private _configGuideV2: IConfigGuideV2[];

  // 当前状态
  private curState: TalkEnum = TalkEnum.INIT;

  private curTalkIdx: number = 0;

  private cd = 0;

  private actIs: boolean = false;

  init(args: any) {
    this._configGuideV2 = args.cfgList;
  }

  start() {
    super.start();
    let configTalk = this._configGuideV2[this.curTalkIdx];

    this.lbl_skip.node.active = false;
    this.lbl_skip.string = LangMgr.txMsgCode(243, [], "跳过");
    this.richtext_msg.string = "";
    this.richtext_msg.node.active = false;
    this.lbl_name.string = configTalk.name;
    this.lbl_name.node.active = false;

    // 是否关闭
    if (!this._configGuideV2?.length) {
      this.closeBack();
      return;
    }

    TipsMgr.setEnableTouch(false, 5, false);
    this.skt.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
      if (trackEntry.animation.name == "pop_up_appear") {
        TipsMgr.setEnableTouch(true);
        this.skt.setCompleteListener(null);
        this.actIs = true;
        this.curTalkIdx = 0;
      }
    });

    tween(this.lbl_skip.node).delay(0.01).set({ active: true }).start();
    tween(this.richtext_msg.node).delay(0.01).set({ active: true }).start();
    tween(this.lbl_name.node).delay(0.01).set({ active: true }).start();

    this.skt.setAnimation(0, "pop_up_appear", false);
  }

  update(deltaTime: number) {
    if (this.actIs === false) {
      return;
    }
    this.cd += deltaTime;
    if (this.cd < 0.1) {
      return;
    }
    this.cd = 0;

    if (this.curTalkIdx == -1 || this.curTalkIdx >= this._configGuideV2.length) {
      return;
    }

    let configTalk = this._configGuideV2[this.curTalkIdx];

    if (this.curState == TalkEnum.INIT) {
      TipsMgr.setEnableTouch(false, 0.2);

      this.curState = TalkEnum.PLAY_TALK;
      this.lbl_name.string = configTalk.name;
    } else if (this.curState == TalkEnum.PLAY_TALK) {
      let strWordNow = this.richtext_msg.string;
      if (strWordNow.length < configTalk.args.length) {
        // todo 打字效果

        strWordNow = configTalk.args;
      } else {
        strWordNow = configTalk.args;
        this.curState = TalkEnum.COMPLETE;
      }
      this.richtext_msg.string = strWordNow;
    } else if (this.curState == TalkEnum.COMPLETE) {
      this.lbl_skip.node.active = true;
    }
  }

  /**
   * 加载资源
   * @param nodeParent 父节点
   * @param spineId spine配置ID
   */
  async showPrefab(nodeParent: Node, spineId: number) {
    this.curState = TalkEnum.LOADING_SPINE;

    let nodeName = "role" + spineId;

    let nodeExist = nodeParent.getChildByName(nodeName);
    if (!nodeExist) {
      let configSpine = JsonMgr.instance.getConfigSpineShow(spineId);
      if (!configSpine) {
        let dbLeaderSkin = JsonMgr.instance.jsonList.c_leaderSkin[PlayerModule.data.skin.skinId];
        configSpine = JsonMgr.instance.getConfigSpineShow(dbLeaderSkin.spineId);
      }

      if (!configSpine) {
        return;
      }

      let rs = configSpine.prefabPath.split("?");

      let pb = await this.assetMgr.loadPrefabSync(rs[0], rs[1]);

      let nodeRole = instantiate(pb);
      nodeRole.walk((child) => {
        child.layer = this.node.layer;
      });

      nodeParent.addChild(nodeRole);
      nodeParent.active = true;

      // todo 抽象接口调用
      nodeRole.getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);

      nodeRole.name = nodeName;
    }
  }

  on_click_btn_next() {
    if (this.curState == TalkEnum.COMPLETE) {
      this.curTalkIdx++;
      this.lbl_skip.node.active = false;
      this.richtext_msg.string = "";
      this.curState = TalkEnum.INIT;
    } else if (this.curState == TalkEnum.PLAY_TALK) {
      let configTalk = this._configGuideV2[this.curTalkIdx];
      this.richtext_msg.string = configTalk.args;
      this.curState = TalkEnum.COMPLETE;
    }

    if (this.curTalkIdx >= this._configGuideV2.length) {
      TipsMgr.setEnableTouch(false, 5);
      this.skt.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == "pop_up_disappear") {
          TipsMgr.setEnableTouch(true);
          this.skt.setCompleteListener(null);
          this.closeBack();
        }
      });
      this.skt.setAnimation(0, "pop_up_disappear", false);
    }
  }
}
